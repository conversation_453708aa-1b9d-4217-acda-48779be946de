<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">试卷管理</el-breadcrumb-item>
      <el-breadcrumb-item>试卷详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">基本信息</span>
        </div>
        <el-row type="flex" justify="center">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form :inline="true" label-width="150px" class="m-text-form f-mt30">
              <el-col :span="12">
                <el-form-item label="试卷名称：">试卷名称试卷名称试卷名称</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="试卷分类：">试卷分类</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="组卷方式：">智能组卷</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="试卷总分：">100 分</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="考试时长：">120 分钟</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="出题范围：">出题范围</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="已选题库：">已选题库已选题库已选题库</el-form-item>
              </el-col>
            </el-form>
          </el-col>
        </el-row>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">配置试题</span>
        </div>
        <el-row type="flex" justify="center">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form :inline="true" label-width="auto" class="m-text-form f-mt30">
              <el-col :span="12">
                <el-form-item label="大题数量：">2 大题</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="试题总数：">8 题</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="涵盖题型：">单选题</el-form-item>
              </el-col>
            </el-form>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="f-mb40">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form label-width="90px" class="m-form">
              <div class="m-question-set f-clear f-mt20">
                <el-form-item class="is-text">
                  <div slot="label" class="f-f16 f-cb">第一大题</div>
                  <div class="f-f16">
                    请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。
                  </div>
                  <el-row :gutter="20" class="f-mt20">
                    <el-col :span="6">题型： <span class="f-fb">单选题</span></el-col>
                    <el-col :span="6">大题总分：<span class="f-fb">40分</span></el-col>
                    <el-col :span="6">大题数量：<span class="f-fb">40道题</span></el-col>
                  </el-row>
                </el-form-item>
              </div>
              <div class="m-question-set f-clear f-mt20">
                <el-form-item class="is-text">
                  <div slot="label" class="f-f16 f-cb">第二大题</div>
                  <div class="f-f16">
                    请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。
                  </div>
                  <el-row :gutter="20" class="f-mt20">
                    <el-col :span="6">题型： <span class="f-fb">单选题</span></el-col>
                    <el-col :span="6">大题总分：<span class="f-fb">40分</span></el-col>
                    <el-col :span="6">大题数量：<span class="f-fb">40道题</span></el-col>
                  </el-row>
                </el-form-item>
              </div>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
