import QuestionnaireLearningPreconditionOutlineRequire from '@api/service/common/scheme/model/schemeDto/common/questionnaire-learning/precondition/outline-requires/QuestionnaireLearningPreconditionOutlineRequire'

/**
 * @description 问卷学习方式前置条件
 */
class QuestionnaireLearningPrecondition {
  /**
   * 前置条件id
   */
  id: string
  /**
   * 操作类型
   */
  operation: number
  /**
   * 前置条件名称
   */
  name: string
  /**
   * 关联课程学习方式id
   */
  referLearningId: string
  /**
   * 必修学时
   * @description
   * 自主选课模式下是必学学时
   * 选课规则下是必修学时
   *
   */
  compulsoryRequirePeriod: number
  /**
   * 选课规则-选修学时
   */
  electiveRequirePeriod: number
  /**
   * 自主选课-总要求学时
   */
  requirePeriod: number
  /**
   * 自主选课-课程大纲要求
   */
  outlineRequires: QuestionnaireLearningPreconditionOutlineRequire[]
  /**
   * 期别Id
   * @description
   */
  issueId: string
}

export default QuestionnaireLearningPrecondition
