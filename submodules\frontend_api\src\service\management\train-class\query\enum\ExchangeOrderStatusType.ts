import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 换货单状态
 * 1：发起换班
 * 2：退班处理中
 * 3：退班失败
 * 4：申请发货
 * 5：发货处理中
 * 6：换班成功
 */
export enum ExchangeOrderStatusEnum {
  Apply_Exchange = 1,
  Off_Shift_Processing = 2,
  Off_Shift_Fail = 3,
  Apply_Delivery = 4,
  Delivery_Processing = 5,
  Complete_Exchange = 6
}

class ExchangeOrderStatusType extends AbstractEnum<ExchangeOrderStatusEnum> {
  static enum = ExchangeOrderStatusEnum
  constructor(status?: ExchangeOrderStatusEnum) {
    super()
    this.current = status
    this.map.set(ExchangeOrderStatusEnum.Apply_Exchange, '发起换班')
    this.map.set(ExchangeOrderStatusEnum.Off_Shift_Processing, '退班处理中')
    this.map.set(ExchangeOrderStatusEnum.Off_Shift_Fail, '退班失败')
    this.map.set(ExchangeOrderStatusEnum.Apply_Delivery, '申请发货')
    this.map.set(ExchangeOrderStatusEnum.Delivery_Processing, '发货处理中')
    this.map.set(ExchangeOrderStatusEnum.Complete_Exchange, '换班成功')
  }
}

export default new ExchangeOrderStatusType()
