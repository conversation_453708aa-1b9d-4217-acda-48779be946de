<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2023-07-12 16:34:07
 * @LastEditors: <PERSON>
 * @LastEditTime: 2023-07-17 10:58:50
-->
<route-meta>
{
"isMenu": true,
"onlyShowOnTab": true,
"title": "个人帐号设置",
"sort": 2,
"hideMenu": true,
"icon": "icon_guanli"
}
</route-meta>
<script lang="ts">
  // 引入运营域下的代码
  import PersonInfo from '@hbfe/jxjy-admin-operationPersonInfo/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { ZXMGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    personalInfo: [ZXMGLY],
    editName: [ZXMGLY],
    changePassword: [ZXMGLY],
    getEditPhoneCapture: [ZXMGLY],
    getChangePhoneCapture: [ZXMGLY],
    submitChange: [ZXMGLY],
    submitEdit: [ZXMGLY]
  })
  export default class extends PersonInfo {}
</script>
