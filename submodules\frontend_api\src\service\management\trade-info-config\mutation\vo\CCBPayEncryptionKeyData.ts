import { EncryptionKeyDataResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

export class CCBPayEncryptionKeyDataResponse implements EncryptionKeyDataResponse {
  /**
   * 商户柜台代码
   */
  POS_ID = ''
  /**
   * 分行代码
   */
  BRANCH_ID = ''
  /**
   * 建行网银支付接口的公钥
   */
  PUBLIC_KEY = ''
  /**
   * 建行的操作员账号不能为空
   */
  OPERATOR = ''
  /**
   * 建行操作员的登陆密码
   */
  PASSWORD = ''
  /**
   * 是否使用防钓鱼,如果1表示使用防钓鱼接口,其他则不使用
   */
  PHISHING = 0
  /**
   * 小程序/公众号的 APPID 当前调起支付的小程序/公众号 APPID
   */
  SUB_APP_ID = ''
  /**
   * 文件证书路径
   */
  CERT_FILE_PATH = ''
  /**
   * 文件证书密码
   */
  CERT_PASS_WORD = ''
  /**
     * 秘钥类型
  Alipay
  WechatPay
  OfflinePay
  CIBPay
  @see EncryptionKeyType
     */
  encryptionKeyType: string
}
