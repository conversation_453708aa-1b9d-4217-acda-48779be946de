import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 注册来源
 */
enum IdentityTypeEnum {
  /**
   * 用户名、省平台id
   */
  user_name = 1
}
export { IdentityTypeEnum }

class IdentityType extends AbstractEnum<IdentityTypeEnum> {
  static enum = IdentityTypeEnum

  constructor(status?: IdentityTypeEnum) {
    super()
    this.current = status
    this.map.set(IdentityTypeEnum.user_name, '用户名')
  }
}

export default new IdentityType()
