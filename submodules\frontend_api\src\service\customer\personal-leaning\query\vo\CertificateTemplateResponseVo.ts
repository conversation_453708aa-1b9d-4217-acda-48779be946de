import { CertificateTemplateResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { TrainingSchemeFormEnum } from '@api/service/common/enums/personal-leaning/TrainingSchemeForm'
import { UsageRangeEnum } from '@api/service/common/enums/personal-leaning/UsageRange'

/*
 * 证明模板
 */
class CertificateTemplateResponseVo extends CertificateTemplateResponse {
  /**
   * 模板id
   */
  id = ''
  /**
   * 服务商id
   */
  servicerId = ''
  /**
   * 模板名称
   */
  name = ''
  /**
   * 模板说明
   */
  describe = ''
  /**
   * 所属行业id
   */
  belongsIndustryId = ''
  /**
   * 所属行业名称
   */
  belongsIndustryName = ''
  /**
   * 使用范围
   * 【默认 全网校】
   */
  usableRange: string = UsageRangeEnum.ALL_SERVICER
  /**
   * 适用培训方案形式
   * 【默认 培训班】
   */
  suitableSchemeType: string = TrainingSchemeFormEnum.TRAINING_CLASS
  /**
   * html模板地址
   */
  url = ''
  /**
   * 预览html模板地址
   */
  previewUrl = ''
  /**
   * 打印快照数据源
   */
  printSnapShotDataSource = ''
  /**
   * 是否应用电子章
   */
  provideElectronicSeal: boolean = undefined
  /**
   * 电子章数据源
   */
  electronicDataSource = ''
  /**
   * 是否提供防伪二维码
   */
  provideAntiBogusQRCode: boolean = undefined
  /**
   * 防伪二维码id
   */
  AntiBogusQRCodeId = ''
  /**
   * 创建人id
   */
  createUserId = ''
  /**
   * 创建时间
   */
  createdTime = ''
  /**
   * 更新时间
   */
  updatedTime = ''

  from(item: CertificateTemplateResponse) {
    this.id = item.id
    this.servicerId = item.servicerId
    this.name = item.name
    this.describe = item.describe
    this.belongsIndustryId = item.belongsIndustryId
    this.usableRange = item.usableRange
    this.suitableSchemeType = item.suitableSchemeType
    this.url = item.url
    this.previewUrl = item.previewUrl
    this.printSnapShotDataSource = item.printSnapShotDataSource
    this.provideElectronicSeal = item.provideElectronicSeal
    this.provideAntiBogusQRCode = item.provideAntiBogusQRCode
    this.electronicDataSource = item.electronicDataSource
    this.AntiBogusQRCodeId = item.AntiBogusQRCodeId
    this.createUserId = item.createUserId
    this.size = item.size
    this.createdTime = item.createdTime
    this.updatedTime = item.updatedTime
  }
}

export default CertificateTemplateResponseVo
