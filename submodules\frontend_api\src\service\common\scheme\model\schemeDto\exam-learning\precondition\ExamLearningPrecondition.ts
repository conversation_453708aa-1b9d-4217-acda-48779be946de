import ExamLearningPreconditionOutlineRequire from '@api/service/common/scheme/model/schemeDto/exam-learning/precondition/outline-requires/ExamLearningPreconditionOutlineRequire'

/**
 * @description 考试学习方式-前置条件
 */
class ExamLearningPrecondition {
  /**
   *
   */
  id: string
  /**
   *
   */
  name: string
  /**
   * 操作类型
   */
  operation: number
  /**
   * 学习方式id
   */
  referLearningId: string
  /**
   * 必修课要求学时
   */
  compulsoryRequirePeriod: number
  /**
   * 要求学时
   */
  requirePeriod: number
  /**
   * 大纲学习要求
   */
  outlineRequires: ExamLearningPreconditionOutlineRequire[]
}

export default ExamLearningPrecondition
