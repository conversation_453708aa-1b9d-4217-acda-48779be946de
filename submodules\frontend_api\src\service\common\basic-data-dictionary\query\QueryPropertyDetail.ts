import BasicDataGateway, {
  SchoolTrainingPropertyQueryRequest,
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { listIndustryPropertyByOnlineSchoolV2 } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage/graphql-importer'
import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
import Context from '@api/service/common/context/Context'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'

export class BatchQueryPropertyRequest {
  /**
   * 行业id
   */
  industryId = ''
  /**
   * 属性Id
   */
  propertyIds: Array<string> = new Array<string>()

  constructor(industryId?: string, propertyIds?: Array<string>) {
    this.industryId = industryId
    this.propertyIds = propertyIds
  }
}

export default new (class QueryPropertyDetail {
  /**
   * 获取指定业务属性id下属性详情
   * @param industryId 行业id
   * @param propertyIds 岗位属性id数组
   */
  async getPropertyDetailByIds(
    industryId: string,
    propertyIds: Array<string>
  ): Promise<Array<TrainingPropertyResponse>> {
    const request = new SchoolTrainingPropertyQueryRequest()

    request.industryId = industryId
    request.propertyId = propertyIds
    request.schoolId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId

    const res = await BasicDataGateway.listIndustryPropertyByOnlineSchoolV2(request)

    return (res?.data?.length && res.data) || new Array<TrainingPropertyResponse>()
  }

  /**
   * 批量获取指定业务属性id下属性详情
   * @param param 请求入参
   * @param.industryId 行业id
   * @param.propertyIds 岗位属性id数组
   */
  async batchGetPropertyDetailByIds(param: Array<BatchQueryPropertyRequest>): Promise<Array<TrainingPropertyResponse>> {
    const requestList = new Array<SchoolTrainingPropertyQueryRequest>()

    param.map(item => {
      const req = new SchoolTrainingPropertyQueryRequest()
      req.schoolId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
      req.propertyId = item.propertyIds
      req.industryId = item.industryId
      requestList.push(req)
    })
    const reWriteGQL = new RewriteGraph<TrainingPropertyResponse[], SchoolTrainingPropertyQueryRequest>(
      BasicDataGateway._commonQuery,
      listIndustryPropertyByOnlineSchoolV2
    )
    await reWriteGQL.request(requestList)
    const result = new Array<TrainingPropertyResponse>()

    for (const value of reWriteGQL.itemMap.values()) {
      if (value && value.length) {
        value.forEach(el => {
          result.push(TrainingCategoryVo.from(el))
        })
      }
    }

    return result
  }
})()
