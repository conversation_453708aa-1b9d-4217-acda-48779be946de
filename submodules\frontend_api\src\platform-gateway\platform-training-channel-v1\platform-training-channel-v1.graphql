schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""导出批量更新方案展示全部结果
		@param mainTaskId 主任务id
		@return 导出结果url
	"""
	exportAllUpdateSchemeShowResult(mainTaskId:String!):ExportUpdateSchemeShowResultResponse
	"""导出批量更新方案展失败结果
		@param mainTaskId 主任务id
		@return 导出结果url
	"""
	exportFailUpdateSchemeShowResult(mainTaskId:String!):ExportUpdateSchemeShowResultResponse
	"""获取专题方案门户展示模版地址
		@return 专题方案门户展示模版地址
	"""
	getTainingChannelSchemePortalShowTempletePath:String
	"""分页获取专题方案门户展示模版导入结果
		@param page 分页参数
		@param request 查询参数
		@return 专题方案门户展示导入结果
	"""
	pageUpdateSchemePortalResultTaskInfo(page:Page,request:ImportSchemePortalShowTaskQueryRequest):UpdateSchemePortalResultTaskInfoResponsePage @page(for:"UpdateSchemePortalResultTaskInfoResponse")
	"""校验专题入口名称是否唯一
		@param request
		@return
		200002 专题入口名称已存在
	"""
	validTrainingChannelEntryNameUnique(request:ValidTrainingChannelEntryNameUniqueRequest):GenernalResponse
}
type Mutation {
	"""批量更新方案展示"""
	batchUpdateSchemeShow(request:BatchUpdateSchemeRequest):Void
	"""校验学员工作单位与专题所属单位是否一致"""
	compareTrainingChannelUnitWithStudentUnit(userId:String,trainingChannelId:String):Boolean!
	"""停用专题信息
		@param id 专题id
		@return
	"""
	disableTrainingChannelInfo(id:String):GenernalResponse
	"""启用专题信息
		@param id 专题id
		@return
	"""
	enableTrainingChannelInfo(id:String):GenernalResponse
	"""获取网校专题基础配置信息
		todo 疑问H5端需要提供服务商id进行查询，
		管理端不需要服务商id，根据上下文进行查询
	"""
	getTrainingChannelSetting:GetTrainingChannelSettingResponse @optionalLogin
	"""从专题里移除方案
		@param request
		@return
	"""
	removeSchemeOfTopic(request:RemoveSchemeOfTopicRequest):GenernalResponse
	"""保存专题基础信息
		@param request
		@return
		200001 域名已存在
	"""
	saveTrainingChannelInfo(request:SaveTrainingChannelInfoRequest):SaveTrainingChannelInfoResponse
	"""保存专题线下集体报名配置信息
		@param request
		@return
	"""
	saveTrainingChannelOfflineCollectiveSignUpSetting(request:SaveTrainingChannelOfflineCollectiveSignUpSettingsRequest):GenernalResponse
	"""保存专题线上集体报名配置信息"""
	saveTrainingChannelOnlineCollectiveSignUpSetting(request:SaveTrainingChannelOnlineCollectiveSignUpSettingsRequest):GenernalResponse
	"""保存专题门户信息
		@param request
		@return
	"""
	saveTrainingChannelPortalInfo(request:SaveTrainingChannelPortalInfoRequest):TrainingChannelPortalInfoResponse
	"""保存专题学习方案信息
		@param request
		@return
	"""
	saveTrainingChannelScheme(request:SaveTrainingChannelSchemeRequest):GenernalResponse
	"""保存专题精品课程信息"""
	saveTrainingChannelSelectedCourse(request:SaveTrainingChannelSelectedCourseRequest):SaveTrainingChannelSelectedCourseResponse
	"""设置专题基础配置"""
	setTrainingChannelSetting(request:SetTrainingChannelSettingRequest):GenernalResponse
	"""修改销售配置
		@param request  UpdateSaleSettingRequest 修改销售配置请求
		@return GenernalResponse 修改销售配置结果
	"""
	updateSaleSetting(request:UpdateSaleSettingRequest):GenernalResponse
	"""更新专题基础信息
		@param request
		@return
	"""
	updateTrainingChannelInfo(request:UpdateTrainingChannelInfoRequest):GenernalResponse
	"""更新专题线下集体报名配置信息
		@param request
		@return
	"""
	updateTrainingChannelOfflineCollectiveSignUpSetting(request:UpdateTrainingChannelOfflineCollectiveSignUpSettingsRequest):GenernalResponse
	"""更新专题线上集体报名配置信息
		@param request
		@return
	"""
	updateTrainingChannelOnlineCollectiveSignUpSetting(request:UpdateTrainingChannelOnlineCollectiveSignUpSettingsRequest):GenernalResponse
	"""更新专题门户信息
		@return
	"""
	updateTrainingChannelPortalInfo(request:UpdateTrainingChannelPortalInfoRequest):TrainingChannelPortalInfoResponse
	"""更新专题排序
		@param request
		@return
	"""
	updateTrainingChannelSort(request:UpdateTrainingChannelSortRequest):GenernalResponse
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""@Description 附件请求
	<AUTHOR>
	@Date 2024/3/20 9:56
"""
input Attachment @type(value:"com.fjhb.platform.jxjy.v1.api.trainingchannel.event.trainingchannelonlinecollective.Attachment") {
	"""附件名称"""
	name:String
	"""附件地址"""
	url:String
}
"""从专题中移除方案请求"""
input RemoveSchemeOfTopicRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.RemoveSchemeOfTopicRequest") {
	"""专题ID"""
	ptcId:String!
	"""学习方案ID"""
	schemeId:String!
}
"""@author: linxiquan
	@Date: 2024/1/11 19:00
	@Description: 保存专题基础信息请求
"""
input SaveTrainingChannelInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.SaveTrainingChannelInfoRequest") {
	"""专题入口名称"""
	entryName:String!
	"""专题名称"""
	name:String!
	"""域名类型
		DEFAULT = 1; 系统默认域名
		CUSTOM = 2；自定义域名
	"""
	domainNameType:Int!
	"""专题域名"""
	domainName:String!
	"""专题类型(已废弃，改用新专题类型字段complexTypes,适配类型多选场景)"""
	type:[Int]!
	"""复合专题类型
		1-地区  2-行业  3-单位
	"""
	complexTypes:[Int]!
	"""所属单位"""
	unitName:String
	"""专题适用地区 类型为地区时，存储地区code集合"""
	suiteAreaList:[String]
	"""专题适用行业  类型为行业时，存储行业id集合"""
	suiteIndustryList:[String]
	"""H5端专题模板编号"""
	h5TemplateNo:String!
	"""PC端专题模板编号"""
	pcTemplateNo:String!
	"""是否显示在网校"""
	showOnNetSchool:Boolean!
	"""是否不允许访问 true-不允许 false -允许"""
	notAllowAccess:Boolean!
}
"""@Description 保存专题线下集体报名配置信息
	<AUTHOR>
	@Date 2024/3/26 15:42
"""
input SaveTrainingChannelOfflineCollectiveSignUpSettingsRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.SaveTrainingChannelOfflineCollectiveSignUpSettingsRequest") {
	"""专题ID"""
	trainingChannelId:String!
	"""专题线下集体报名入口开关"""
	openEntrySwitch:Boolean!
	"""专题线下集体报名入口图片附件"""
	entryPictureAttachments:[Attachment]
	"""线下集体报名名称"""
	name:String
	"""专题线下集体报名模板地址"""
	templateAttachment:Attachment
	"""访问链接"""
	accessUrl:String
	"""底部文本说明内容"""
	bottomDescription:String
	"""报名步骤信息"""
	signUpSteps:[SignUpStep]
}
"""@Description 保存专题线上集体报名配置信息
	<AUTHOR>
	@Date 2024/3/20 9:51
"""
input SaveTrainingChannelOnlineCollectiveSignUpSettingsRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.SaveTrainingChannelOnlineCollectiveSignUpSettingsRequest") {
	"""专题ID"""
	trainingChannelId:String!
	"""专题线上集体报名入口开关"""
	openEntrySwitch:Boolean!
	"""专题线上集体报名入口图片开关"""
	openEntryPictureSwitch:Boolean!
	"""专题线上集体报名入口图片附件"""
	entryPictureAttachments:[Attachment]
	"""专题线上集体报名模版地址"""
	templateAttachment:Attachment
	"""展示报名班级链接地址"""
	showSignUpClassUrl:String
}
"""@author: linxiquan
	@Date: 2024/1/11 20:20
	@Description: 保存专题门户信息请求
"""
input SaveTrainingChannelPortalInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.SaveTrainingChannelPortalInfoRequest") {
	"""专题id"""
	id:String!
	"""专题门户logo类型
		CHARACTER = 1; 文字
		PICTURE = 2；图片
		com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.LogoTypes
	"""
	logoType:Int!
	"""专题门户logo名称  专题门户logo类型为文字时，有值"""
	logoName:String
	"""专题门户logo图片地址  专题门户logo类型为图片时，有值"""
	logoPictureUrl:String
	"""客服电话类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
		com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.CustomerServicePhoneTypes
	"""
	customerServicePhoneType:Int
	"""客服电话"""
	customerServicePhone:String
	"""客服电话图片"""
	customerServicePhonePictureUrl:String
	"""培训流程类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
		@see com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.TrainingProcessTypes
	"""
	trainingProcessType:Int
	"""培训流程图片"""
	trainingProcessAttachments:[Attachment]
	"""企业微信客服类型
		@see  com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.CustomerServicePhoneTypes
	"""
	enterpriseWechatCustomerType:Int
	"""企业微信客服类型"""
	enterpriseWechatCustomerAttachments:[Attachment]
	"""咨询时间"""
	seekTime:String
	"""专题门户底部落款类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
		com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.PortalBottomShowTypes
	"""
	bottomShowType:Int!
	"""专题门户底部落款内容Id 专题门户底部落款类型为自定义时 有值"""
	bottomShowContentId:String
	"""专题门户底部落款内容"""
	bottomShowContent:String
	"""专题门户轮播图"""
	trainingChannelPortalBannerList:[TrainingChannelPortalBanner]
}
"""@author: linxiquan
	@Date: 2024/1/11 19:47
	@Description: 设置专题基础配置
"""
input SetTrainingChannelSettingRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.SetTrainingChannelSettingRequest") {
	"""专题基础配置id （首次保存为空）"""
	id:String
	"""是否开启专题"""
	isOpenTrainingChannel:Boolean!
	"""是否开启网校展示入口"""
	isShowEntry:Boolean!
	"""网校展示专题方案"""
	isShowTrainingChannelScheme:Boolean!
	"""专题入口名称"""
	name:String
	"""专题引导语"""
	guideExpression:String
	"""专题配置拓展属性"""
	trainingChannelSettingExtReq:[TrainingChannelSettingExtReq]
}
"""@Description 线下集体报名的步骤信息
	<AUTHOR>
	@Date 2024/3/26 16:07
"""
input SignUpStep @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.SignUpStep") {
	"""步骤序号"""
	index:Int!
	"""步骤标题"""
	title:String
	"""步骤内容"""
	content:String
	"""步骤内容ID【更新时需上传】"""
	contentId:String
}
"""专题配置拓展信息"""
input TrainingChannelSettingExtReq @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.TrainingChannelSettingExtReq") {
	"""专题配置id"""
	trainingChannelSettingId:String
	"""拓展属性id"""
	objectId:String
	"""拓展属性类型"""
	type:Int
	"""拓展属性key"""
	key:String
	"""拓展属性value"""
	value:String
}
"""@author: linxiquan
	@Date: 2024/1/11 19:26
	@Description: 更新专题基础信息请求
"""
input UpdateTrainingChannelInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.UpdateTrainingChannelInfoRequest") {
	"""专题基础信息id"""
	id:String!
	"""专题入口名称"""
	entryName:String!
	"""专题名称"""
	name:String!
	"""域名类型
		DEFAULT = 1; 系统默认域名
		CUSTOM = 2；自定义域名
	"""
	domainNameType:Int!
	"""专题域名"""
	domainName:String!
	"""专题类型(已废弃，改用新专题类型字段complexTypes,适配类型多选场景)"""
	type:[Int]!
	"""复合专题类型
		1-地区  2-行业  3-单位
	"""
	complexTypes:[Int]!
	"""所属单位"""
	unitName:String
	"""专题适用地区 类型为地区时，存储地区code集合"""
	suiteAreaList:[String]
	"""专题适用行业  类型为行业时，存储行业id集合"""
	suiteIndustryList:[String]
	"""H5端专题模板编号"""
	h5TemplateNo:String!
	"""PC端专题模板编号"""
	pcTemplateNo:String!
	"""是否显示在网校"""
	showOnNetSchool:Boolean!
	"""是否不允许访问 true-不允许 false -允许"""
	notAllowAccess:Boolean!
}
"""@Description 更新专题线下集体报名配置信息
	<AUTHOR>
	@Date 2024/3/26 15:42
"""
input UpdateTrainingChannelOfflineCollectiveSignUpSettingsRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.UpdateTrainingChannelOfflineCollectiveSignUpSettingsRequest") {
	"""专题线上集体报名配置id"""
	id:String
	"""专题线下集体报名入口开关"""
	openEntrySwitch:Boolean!
	"""专题线下集体报名入口图片附件"""
	entryPictureAttachments:[Attachment]
	"""线下集体报名名称"""
	name:String
	"""专题线下集体报名模板地址"""
	templateAttachment:Attachment
	"""访问链接"""
	accessUrl:String
	"""底部文本说明内容"""
	bottomDescription:String
	"""报名步骤信息"""
	signUpSteps:[SignUpStep]
}
"""@Description TODO
	<AUTHOR>
	@Date 2024/3/22 16:05
"""
input UpdateTrainingChannelOnlineCollectiveSignUpSettingsRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.UpdateTrainingChannelOnlineCollectiveSignUpSettingsRequest") {
	"""线上集体报名配置ID"""
	id:String!
	"""专题线上集体报名入口开关"""
	openEntrySwitch:Boolean!
	"""专题线上集体报名入口图片开关"""
	openEntryPictureSwitch:Boolean!
	"""专题线上集体报名入口图片附件"""
	entryPictureAttachments:[Attachment]
	"""专题线上集体报名模版地址"""
	templateAttachment:Attachment
	"""展示报名班级链接地址"""
	showSignUpClassUrl:String
}
"""@author: linxiquan
	@Date: 2024/1/11 20:20
	@Description: 更新专题门户信息请求
"""
input UpdateTrainingChannelPortalInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.UpdateTrainingChannelPortalInfoRequest") {
	"""专题id"""
	id:String!
	"""专题门户id"""
	trainingChannelPortalId:String!
	"""专题门户logo类型
		CHARACTER = 1; 文字
		PICTURE = 2；图片
		com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.LogoTypes
	"""
	logoType:Int!
	"""专题门户logo名称  专题门户logo类型为文字时，有值"""
	logoName:String
	"""专题门户logo图片地址  专题门户logo类型为图片时，有值"""
	logoPictureUrl:String
	"""客服电话类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
		com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.CustomerServicePhoneTypes
	"""
	customerServicePhoneType:Int
	"""客服电话"""
	customerServicePhone:String
	"""客服电话图片"""
	customerServicePhonePictureUrl:String
	"""培训流程类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
		@see com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.TrainingProcessTypes
	"""
	trainingProcessType:Int
	"""培训流程图片"""
	trainingProcessAttachments:[Attachment]
	"""企业微信客服类型
		@see  com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.CustomerServicePhoneTypes
	"""
	enterpriseWechatCustomerType:Int
	"""企业微信客服类型"""
	enterpriseWechatCustomerAttachments:[Attachment]
	"""咨询时间"""
	seekTime:String
	"""专题门户底部落款类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
		com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.PortalBottomShowTypes
	"""
	bottomShowType:Int!
	"""专题门户底部落款内容Id 专题门户底部落款类型为自定义时 有值"""
	bottomShowContentId:String
	"""专题门户底部落款内容"""
	bottomShowContent:String
	"""专题门户轮播图"""
	trainingChannelPortalBannerList:[TrainingChannelPortalBanner]
}
"""@author: linxiquan
	@Date: 2024/1/16 8:42
	@Description: 更新专题排序请求
"""
input UpdateTrainingChannelSortRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.UpdateTrainingChannelSortRequest") {
	"""专题id"""
	id:String
	"""排序值"""
	sort:Int
}
"""@author: linxiquan
	@Date: 2024/1/23 18:46
	@Description: 校验专题入口名称是否唯一请求
"""
input ValidTrainingChannelEntryNameUniqueRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.ValidTrainingChannelEntryNameUniqueRequest") {
	"""专题入口名称"""
	entryName:String
	"""专题id"""
	trainingChannelId:String
}
"""@Description 批量更新方案展示请求
	<AUTHOR>
	@Date 2025/7/2 16:00
"""
input BatchUpdateSchemeRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.scheme.BatchUpdateSchemeRequest") {
	"""文件路径"""
	filePath:String
	"""文件名"""
	fileName:String
}
input DeleteScheme @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.scheme.DeleteScheme") {
	"""学习方案id"""
	schemeId:String
}
"""@Description 导入更新方案门户展示任务查询请求
	<AUTHOR>
	@Date 2025/7/8 11:50
"""
input ImportSchemePortalShowTaskQueryRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.scheme.ImportSchemePortalShowTaskQueryRequest") {
	"""任务类型"""
	caetgory:String
	"""任务名称"""
	taskName:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""执行时间（起始）"""
	executeStartTime:DateTime
	"""执行时间（终止）"""
	executeEndTime:DateTime
}
"""@author: linxiquan
	@Date: 2024/1/11 20:25
	@Description: 保存专题学习方案信息
"""
input SaveTrainingChannelSchemeRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.scheme.SaveTrainingChannelSchemeRequest") {
	"""专题id"""
	id:String!
	"""新增方案"""
	addScheme:[SchemeList]
	"""更新方案"""
	updateScheme:[SchemeList]
	"""删除方案"""
	deleteScheme:[DeleteScheme]
}
input SchemeList @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.scheme.SchemeList") {
	"""学习方案id"""
	schemeId:String
	"""排序"""
	sort:Int
}
"""@Description 修改销售配置请求
	<AUTHOR>
	@Date 2025/7/2 16:43
"""
input UpdateSaleSettingRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.scheme.UpdateSaleSettingRequest") {
	"""专题id【必填】"""
	id:String
	"""学习方案id【必填】"""
	schemeId:String
	"""是否展示专题门户【必填】"""
	showPortal:Boolean!
	"""展示对象类型【必填】 1-用户 2-集体报名管理员"""
	showObjectTypes:[Int]
}
"""@Description 删除时的精品课程
	<AUTHOR>
	@Date 2024/3/21 9:02
"""
input DeleteSelectedCourse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.selectedcourse.DeleteSelectedCourse") {
	"""课程id"""
	courseId:String
}
"""@Description 保存专题精品课程信息请求
	<AUTHOR>
	@Date 2024/3/21 8:31
"""
input SaveTrainingChannelSelectedCourseRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.selectedcourse.SaveTrainingChannelSelectedCourseRequest") {
	"""专题id"""
	id:String
	"""专题精品课程"""
	trainingChannelSelectedCourses:[TrainingChannelSelectedCourse]
}
"""@Description 选择的课程信息
	<AUTHOR>
	@Date 2024/3/21 8:42
"""
input SelectedCourse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.selectedcourse.SelectedCourse") {
	"""课程id"""
	courseId:String
	"""排序"""
	sort:Int!
}
"""@Description 各个分类的精品课程
	<AUTHOR>
	@Date 2024/3/21 8:57
"""
input TrainingChannelSelectedCourse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.selectedcourse.TrainingChannelSelectedCourse") {
	"""精品课程分类id"""
	selectedCourseCategoryId:String
	"""添加的精品课程"""
	addSelectedCourse:[SelectedCourse]
	"""修改的精品课程"""
	updateSelectedCourse:[SelectedCourse]
	"""删除的精品课程"""
	deleteSelectedCourse:[DeleteSelectedCourse]
}
"""@author: linxiquan
	@Date: 2024/1/11 20:12
	@Description: 专题门户轮播图
"""
input TrainingChannelPortalBanner @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.portal.TrainingChannelPortalBanner") {
	"""专题门户轮播图id，在新增时不填"""
	id:String
	"""专题门户轮播图类型
		WEB = 1; web端
		H5 = 2；H5端
		com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.BannerTypes
	"""
	type:Int!
	"""专题门户轮播图地址"""
	pictureUrl:String
	"""链接地址"""
	linkUrl:String
	"""排序值"""
	sort:Int!
}
type EachStateCount @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.CommonImportTaskQueryResponse$EachStateCount") {
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	state:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int
	"""数量"""
	count:Long!
}
"""<AUTHOR> [2023/7/11 20:54]"""
type GenernalResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.GenernalResponse") {
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""@Description 导出更新培训方案展示信息结果
	<AUTHOR>
	@Date 2025/7/4 9:51
"""
type ExportUpdateSchemeShowResultResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.ExportUpdateSchemeShowResultResponse") {
	fileUrl:String
}
"""@author: linxiquan
	@Date: 2024/1/11 19:53
	@Description: 获取网校专题基础配置信息响应
"""
type GetTrainingChannelSettingResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.GetTrainingChannelSettingResponse") {
	"""专题基础配置id"""
	id:String
	"""是否开启专题"""
	isOpenTrainingChannel:Boolean
	"""网校展示专题方案"""
	isShowTrainingChannelScheme:Boolean
	"""是否开启网校展示入口"""
	isShowEntry:Boolean
	"""专题入口名称"""
	name:String
	"""专题引导语"""
	guideExpression:String
	"""专题配置拓展信息"""
	trainingChannelSettingExtList:[TrainingChannelSettingExt]
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""保存专题基础信息响应"""
type SaveTrainingChannelInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.SaveTrainingChannelInfoResponse") {
	"""专题id"""
	id:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""@Description 保存专题精品课程信息响应
	<AUTHOR>
	@Date 2024/3/21 10:03
"""
type SaveTrainingChannelSelectedCourseResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.SaveTrainingChannelSelectedCourseResponse") {
	"""课程id 集合"""
	ids:[String]
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""@Description 更新方案门户显示结果信息
	<AUTHOR>
	@Date 2025/7/8 11:43
"""
type UpdateSchemePortalResultTaskInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.UpdateSchemePortalResultTaskInfoResponse") {
	"""任务编号"""
	id:String
	"""【必填】平台编号"""
	platformId:String
	"""【必填】平台版本编号"""
	platformVersionId:String
	"""【必填】项目编号"""
	projectId:String
	"""【必填】子项目编号"""
	subProjectId:String
	"""任务名称"""
	name:String
	"""任务分类"""
	category:String
	"""所属批次单编号"""
	batchNo:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""处理信息"""
	message:String
	"""创建时间"""
	createdTime:DateTime
	"""就绪时间"""
	alreadyTime:DateTime
	"""执行时间"""
	executingTime:DateTime
	"""完成时间"""
	completedTime:DateTime
	"""各状态及执行结果对应数量集合
		总数：全部数量之和
		成功数：result = 1数量之和
		失败数：result = 2数量之和
	"""
	eachStateCounts:[EachStateCount]
}
"""门户信息响应"""
type TrainingChannelPortalInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.portal.TrainingChannelPortalInfoResponse") {
	"""专题门户id"""
	trainingChannelPortalId:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""@Date: 2024/1/12 15:16
	@Description: 专题配置拓展信息仓储模型
"""
type TrainingChannelSettingExt @type(value:"com.fjhb.platform.jxjy.v1.kernel.trainingchannel.repository.model.TrainingChannelSettingExt") {
	"""专题配置拓展信息id"""
	id:String
	"""专题配置id"""
	trainingChannelSettingId:String
	"""拓展属性id"""
	objectId:String
	"""拓展属性类型"""
	type:Int
	"""拓展属性key"""
	key:String
	"""拓展属性value"""
	value:String
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updateTime:DateTime
	"""创建人(userId)"""
	createdUserId:String
}

scalar List
type UpdateSchemePortalResultTaskInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UpdateSchemePortalResultTaskInfoResponse]}
