import { OnlineInvoiceOperationResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { OperationLogTypeEnum } from '@api/service/management/trade/single/invoice/enum/OperationLogTypeEnum'
import QueryPortal from '@api/service/management/online-school-config/portal/query/QueryPortal'

export default class OperationLogItem extends OnlineInvoiceOperationResponse {
  /**
   * 综合描述
   */
  operationStr: string = undefined

  /**
   * 模型转化
   * @param dto
   */
  static from(dto: OnlineInvoiceOperationResponse) {
    const vo = new OperationLogItem()

    vo.operatorUserId = dto?.operatorUserId
    vo.operationType = dto?.operationType
    vo.operateTime = dto?.operateTime
    vo.operationMessage = dto?.operationMessage
    vo.operatorUserName = dto?.operatorUserName

    switch (dto?.operationType) {
      case OperationLogTypeEnum.ISSUE:
        vo.operationStr = `【${QueryPortal?.webPortalInfo?.title}】在 【${dto?.operateTime}】 开票了【培训发票】。`
        break
      case OperationLogTypeEnum.RED_BLOOD:
        vo.operationStr = `【${QueryPortal?.webPortalInfo?.title}】在 【${dto?.operateTime}】 开票了【冲红发票】。`
        break
      case OperationLogTypeEnum.ISSUE_FAIL:
        vo.operationStr = `【${dto?.operateTime}】发票开具失败，失败原因是【${dto?.operationMessage}】`
        break
      case OperationLogTypeEnum.UPDATE:
        vo.operationStr = `【${dto?.operatorUserName}】在【${dto?.operateTime}】修改了【培训发票】`
        break
      case OperationLogTypeEnum.RED_BLOOD_FAIL:
        vo.operationStr = `【${dto?.operateTime}】冲红发票开具失败，失败原因是【${dto?.operationMessage}】`
        break
    }

    return vo
  }
}
