import Query, { PageResult } from '@hbfe/jxjy-admin-common/src/models/Query'

interface GetPageFn {
  (queryParams?: any): Promise<PageResult>
}

export default class PageQuery<T = any> extends Query {
  // 先声明
  async getPage() {
    return {} as PageResult
  }

  pageData: Array<T> = new Array<T>()

  constructor(params: any, getPage: GetPageFn) {
    super(params, getPage)
    this.getPage = getPage
  }
}
