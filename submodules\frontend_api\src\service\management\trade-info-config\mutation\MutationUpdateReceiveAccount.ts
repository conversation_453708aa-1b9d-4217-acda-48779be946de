import { ResponseStatus } from '@hbfe/common'
import msTradeConfigurationV1 from '@api/ms-gateway/ms-trade-configuration-v1'
import { AccountTypeEunm } from '@api/service/common/enums/trade-configuration/AccountType'
import UpdateAliPayReceiveAccountVo from './vo/UpdateAliPayReceiveAccountVo'
import UpdateOfflineReceiveAccountVo from './vo/UpdateOfflineReceiveAccountVo'
import UpdateReceiveAccountVo from './vo/UpdateReceiveAccountVo'
import UpdateWXPayReceiveAccountVo from './vo/UpdateWXPayReceiveAccountVo'
import msTradeQueryFrontGatewayTradeQueryBackstage, {
  ReceiveAccountConfigResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { PayAccountTypeEnum } from '@api/service/management/trade-info-config/enums/PayAccountTypeEnum'
import UpdateXYPayReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/UpdateXYPayReceiveAccountVo'
import UpdateJSPayReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/UpdateJSPayReceiveAccountVo'
import UpdateWFTPayReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/UpdateWFTPayReceiveAccountVo'
import UpdateXDLPayReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/UpdateXDLPayReceiveAccountVo'

class MutationUpdateReceiveAccount {
  updateReceiveAccount = new UpdateReceiveAccountVo()
  receiveAccountId = ''

  constructor(receiveAccountId: string) {
    this.receiveAccountId = receiveAccountId
  }

  /**
   * 更新收款账户
   * @returns
   */
  async doUpdate(): Promise<ResponseStatus> {
    const isExist = await this.verifyUpdate()
    const msRes = await msTradeConfigurationV1.updateReceiveAccount(this.updateReceiveAccount.to())
    if (!msRes.status.isSuccess()) {
      msRes.status.errors[0].message = msRes.status.errors[0].message.split(':')[1].trim()
    }
    return msRes.status
    // return new ResponseStatus(500, '无法修改')
  }

  /**
   * 查询收款账户
   * @returns
   */
  async queryDetail(): Promise<ResponseStatus> {
    const msRes = await msTradeQueryFrontGatewayTradeQueryBackstage.getReceiveAccountInServicer(this.receiveAccountId)
    if (msRes.status.isSuccess()) {
      this.updateVoByPaymentChannelType(msRes.data)
    }
    return msRes.status
  }

  /**
   * 校验是否能修改收款账号， false: 无订单、存在0元单 / true: 存在>0元单
   * @returns boolean
   */
  async verifyUpdate(): Promise<boolean> {
    const res = await msTradeConfigurationV1.isAllowUpdate(this.receiveAccountId)
    return res.data
  }

  /**
   * 根据线上、线下，支付账号类型进行创建类的初始化
   * @param accountType
   * @param paymentChannelId
   */
  updateVoByPaymentChannelType(res: ReceiveAccountConfigResponse) {
    const PaymentChannelType = (type: string) => {
      if (type.indexOf(PayAccountTypeEnum.ALIPAY) > -1) {
        return new UpdateAliPayReceiveAccountVo()
      }
      if (type.indexOf(PayAccountTypeEnum.WXPAY) > -1) {
        return new UpdateWXPayReceiveAccountVo()
      }
      if (type.indexOf(PayAccountTypeEnum.CIB_PAY) > -1) {
        return new UpdateXYPayReceiveAccountVo()
      }
      if (type.indexOf(PayAccountTypeEnum.CCB_PAY) > -1) {
        return new UpdateJSPayReceiveAccountVo()
      }
      if (type.indexOf(PayAccountTypeEnum.SWIFT_PASS_PAY) > -1) {
        return new UpdateWFTPayReceiveAccountVo()
      }
      if (type.indexOf(PayAccountTypeEnum.NEW_LAND_PAY) > -1) {
        return new UpdateXDLPayReceiveAccountVo()
      }
    }
    if (res.accountType === AccountTypeEunm.OFFLINE) {
      this.updateReceiveAccount = new UpdateOfflineReceiveAccountVo()
    } else {
      this.updateReceiveAccount = PaymentChannelType(res.paymentChannelId)
    }
    this.updateReceiveAccount.from(res)
  }
}
export default MutationUpdateReceiveAccount
