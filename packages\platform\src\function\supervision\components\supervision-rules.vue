<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 09:15:13
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2024-10-23 15:12:23
 * @Description: 培训监管-监管规则tab
-->
<template>
  <div>
    <el-card shadow="never" class="m-card">
      <div class="m-tit is-border-bottom">
        <span class="tit-txt">网校级监管规则</span>
      </div>
      <el-alert type="warning" v-if="!supervisionEnable" show-icon :closable="false" class="m-alert f-mt20 f-mb20">
        当前监管功能为“关闭“状态，设置的监管规则不会生效，请前往<span
          class="f-cb"
          style="cursor: pointer"
          @click="goBasicTab"
          >【基础配置】</span
        >开启监管功能总开关。
      </el-alert>
      <el-table stripe :data="platformAntiList.items" max-height="500px" class="m-table">
        <el-table-column label="进入学习" min-width="150">
          <template #default="scope">
            <span v-if="scope.row.studyEnterVerifyEnable">{{ scope.row.studyEnterVerifyText }}</span>
            <span v-else>关闭</span>
          </template>
        </el-table-column>
        <el-table-column label="学习过程" min-width="250">
          <template #default="scope">
            <template v-if="scope.row.studyProcessEnable">
              {{ scope.row.studyProcessVerifyText }}
              <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-co f-ml5"></i>
                <div slot="content">
                  <p>{{ scope.row.studyProcessFrequency }}</p>
                  <p>{{ scope.row.studyReVerify }}</p>
                  <p>{{ scope.row.studyFailProcess }}</p>
                </div>
              </el-tooltip>
            </template>
            <template v-else>关闭</template>
          </template>
        </el-table-column>
        <el-table-column label="状态" min-width="100">
          <template #default="scope">
            <div v-if="scope.row.enable">
              <el-badge is-dot type="success" class="badge-status">启用</el-badge>
            </div>
            <div v-else>
              <el-badge is-dot type="info" class="badge-status">停用</el-badge>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" align="center" fixed="right">
          <template #default="scope">
            <el-button type="text" @click="editSchoolSupervision(scope.row)">编辑</el-button>
            <el-button
              type="text"
              v-if="!scope.row.enable"
              :disabled="!scope.row.antiConfigId"
              @click="changePlatformSupervision(scope.row)"
              >启用
            </el-button>
            <el-button
              type="text"
              v-else
              :disabled="!scope.row.antiConfigId"
              @click="changePlatformSupervision(scope.row)"
              >停用
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 方案级监管 -->
      <div class="m-tit is-border-bottom">
        <span class="tit-txt">方案级监管规则</span>
      </div>

      <div class="f-p15">
        <el-button type="primary" @click="addSupervisionRules">新建方案级监管</el-button>
      </div>
      <el-table stripe :data="antiCheatConfigList.items" max-height="500px" class="m-table">
        <el-table-column label="NO." type="index" width="80" align="center" fixed="left"></el-table-column>
        <el-table-column label="监管规则名称" min-width="150">
          <template #default="scope">
            {{ scope.row.antiConfigName }}（<span
              class="f-cb f-csp f-fb"
              @click="openSchemeList(scope.row.schemeIds)"
              >{{ scope.row.schemeIds.length }}</span
            >）
          </template>
        </el-table-column>
        <el-table-column label="进入学习" min-width="150">
          <template #default="scope">
            <div v-if="scope.row.studyEnterVerifyEnable">
              <template>{{ scope.row.studyEnterVerifyText }}</template>
            </div>
            <div v-else>关闭</div>
          </template>
        </el-table-column>
        <el-table-column label="学习过程" min-width="250">
          <template #default="scope">
            <template v-if="scope.row.studyProcessEnable">
              {{ scope.row.studyProcessVerifyText }}
              <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-co f-ml5"></i>
                <div slot="content">
                  <p>{{ scope.row.studyProcessFrequency }}</p>
                  <p>{{ scope.row.studyReVerify }}</p>
                  <p>{{ scope.row.studyFailProcess }}</p>
                </div>
              </el-tooltip>
            </template>
            <template v-else> 关闭</template>
          </template>
        </el-table-column>
        <el-table-column label="状态" min-width="100">
          <template #default="scope">
            <div v-if="scope.row.enable">
              <el-badge is-dot type="success" class="badge-status">启用</el-badge>
            </div>
            <div v-else>
              <el-badge is-dot type="info" class="badge-status">停用</el-badge>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="scope">
            <el-button type="text" @click="editBaseConfig(scope.row)">编辑</el-button>
            <el-button type="text" @click="deleteSchoolSupervision(scope.row)">删除</el-button>
            <el-button type="text" v-if="!scope.row.enable" @click="changePlatformSupervision(scope.row)"
              >启用
            </el-button>
            <el-button type="text" v-else @click="changePlatformSupervision(scope.row)">停用</el-button>
          </template>
        </el-table-column>
      </el-table>
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </el-card>
    <training-list :open-training-list.sync="openTrainingList" ref="trainingListRef"></training-list>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Ref, Prop } from 'vue-property-decorator'
  import TrainingList from '@hbfe/jxjy-admin-platform/src/function/supervision/components/training-list.vue'
  import AntiCheatConfigList from '@hbfe-biz/biz-anticheat/dist/config/AntiCheatConfigList'
  import { UiPage } from '@hbfe/common'
  import AntiCheatConfigItemModel from '@hbfe-biz/biz-anticheat/dist/config/models/AntiCheatConfigItemModel'
  import { RangeTypeEnum } from '@hbfe-biz/biz-anticheat/dist/config/enums/VerifyConfigModeEnum'
  import { ElTable } from 'element-ui/types/table'

  @Component({
    components: {
      TrainingList
    }
  })
  export default class extends Vue {
    @Ref('trainingListRef') trainingListRef: TrainingList

    @Prop({
      type: Boolean,
      default: false
    })
    supervisionEnable: boolean

    page: UiPage
    platformPage: UiPage
    antiCheatConfigList = new AntiCheatConfigList()
    platformAntiList = new AntiCheatConfigList()
    // 是否打开培训方案列表抽屉
    openTrainingList = false

    constructor() {
      super()
      this.page = new UiPage(this.queryList, this.queryList)
      this.platformPage = new UiPage(this.queryPlatformList, this.queryPlatformList)
    }

    async activated() {
      setTimeout(() => {
        this.queryList()
        this.queryPlatformList()
      }, 1500)
    }

    async queryList() {
      this.antiCheatConfigList.filterParam.platform = RangeTypeEnum.SCHEME
      // 包里的Page类型和本页面的Page类型不一样，所以使用any
      await this.antiCheatConfigList.queryList(this.page as any)
      ;(this.$refs['antiRules'] as ElTable)?.doLayout()
    }

    async queryPlatformList() {
      this.platformAntiList.filterParam.platform = RangeTypeEnum.PLATFORM
      // 包里的Page类型和本页面的Page类型不一样，所以使用any
      await this.platformAntiList.queryList(this.platformPage as any)
      if (this.platformAntiList.items.length === 0) {
        const defaultAntiCheatConfigItemModel = new AntiCheatConfigItemModel()
        defaultAntiCheatConfigItemModel.range = RangeTypeEnum.PLATFORM
        defaultAntiCheatConfigItemModel.enable = false
        defaultAntiCheatConfigItemModel.studyEnterVerifyEnable = false
        defaultAntiCheatConfigItemModel.studyProcessEnable = false
        this.platformAntiList.items = [defaultAntiCheatConfigItemModel]
      }
    }

    openSchemeList(schemeIds: Array<string>) {
      this.trainingListRef.schemeIds = schemeIds
      this.trainingListRef.querySchemeList()
      this.openTrainingList = true
    }

    // 修改基础配置
    editBaseConfig(item: AntiCheatConfigItemModel) {
      this.$router.push({
        path: `/basic-data/platform/function/supervision/supervisionRulesDetail/${item.antiConfigId}`
      })
    }

    // 添加监管规则
    addSupervisionRules() {
      this.$router.push('/basic-data/platform/function/supervision/supervisionRulesDetail/:id')
    }

    /**
     * 启停用网校级别监管提示
     */
    textMap = {
      true: ['停用后监管规则不生效，仍保留已产生的监管日志，是否确认停用？', '确定停用'],
      false: ['启用后，当监管功能开启时规则生效，是否确认启用？', '确定启用']
    }

    /**
     * 启停用监管
     */
    changePlatformSupervision(row: AntiCheatConfigItemModel) {
      const [message, confirmBtnText] = this.textMap[String(!!row.enable)]
      this.$confirm(message, '提示', {
        confirmButtonText: confirmBtnText,
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // 启停用
          const status = await row.changeStatus()
          if (status) {
            this.$message({
              type: 'success',
              message: '操作成功!'
            })
          }
        })
        .catch(() => {
          // 取消
        })
    }

    goBasicTab() {
      this.$emit('goBasicTab')
    }

    /**
     * 编辑网校级别监管
     */
    editSchoolSupervision(row: AntiCheatConfigItemModel) {
      const id = row.antiConfigId ?? ':id'
      this.$router.push({
        path: `/basic-data/platform/function/supervision/platform-supervision-edit/${id}`
      })
    }

    /**
     * 删除方案级别监管
     */

    deleteSchoolSupervision(row: AntiCheatConfigItemModel) {
      this.$confirm('删除后监管规则失效，仍保留已产生的监管日志，是否确认删除？', '提示', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await row.deleteConfig()
          if (res) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
          }
          setTimeout(async () => {
            await this.queryList()
          }, 2000)
        })
        .catch(() => {
          //取消删除
        })
    }
  }
</script>
