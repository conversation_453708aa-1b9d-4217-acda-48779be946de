schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取学习异常统计数据
		@return
	"""
	getLearningResultErrorStatisticsInServicer:LearningResultErrorStatisticsResponse
	"""获取学习异常统计数据
		@return
	"""
	getLearningResultErrorStatisticsInTrainingChannel:LearningResultErrorStatisticsResponse
	"""查询学习类型
		学习类型 0正常学习 1一键合格 2补学
		@return
	"""
	getLearningType(studentNo:String):[Int]
	"""获取模拟学习数据
		@return
	"""
	getStudentTrainingResultSimulateResponseInServicer(request:StudentTrainingResultSimulateRequest):[StudentTrainingResultSimulateResponse]
	"""分页获取学习异常数据
		@return
	"""
	pageLearningResultErrorInServicer(page:Page,request:LearningResultErrorRequest):LearningResultErrorResponsePage @page(for:"LearningResultErrorResponse")
	"""分页获取学习异常数据 专题管理员
		@return
	"""
	pageLearningResultErrorInTrainingChannel(page:Page,request:LearningResultErrorRequest):LearningResultErrorResponsePage @page(for:"LearningResultErrorResponse")
	"""重新生成补学数据
		@return
	"""
	reGenerateStudentTrainingResultSimulateInServicer(studentNos:[String]):String
	"""重推学员学习合格数据
		@return
	"""
	rePushStudentTrainingResultInServicer(studentNos:[String]):String
	"""重推学员学习合格数据-统一网关
		@return
		@deprecated 后续删除
	"""
	rePushStudentTrainingResultToGatewayInServicer(studentNos:[String]):String
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input LearningResultErrorRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.LearningResultErrorRequest") {
	"""用户身份证"""
	userIdCard:String
	"""用户姓名"""
	userName:String
	"""用户手机号"""
	userPhone:String
	"""方案名称"""
	trainingName:String
	"""学习数据异常信息类型  1.学习规则"""
	errorType:Int
}
input StudentTrainingResultSimulateRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.StudentTrainingResultSimulateRequest") {
	"""学号 集合"""
	studentNos:[String]
	"""学员信息"""
	user:UserRequest
	"""学员学习班级信息"""
	studentSchemeLearning:SimulateStudentSchemeLearningRequest
}
"""学员班级学习信息模型"""
input SimulateStudentSchemeLearningRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.nested.SimulateStudentSchemeLearningRequest") {
	"""班级id  集合"""
	schemeIds:[String]
	"""班级名称"""
	trainName:String
}
"""学员信息主题模型"""
input UserRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.nested.UserRequest") {
	"""用户ID"""
	userId:String
	"""证件号"""
	idCard:String
	"""用户名称"""
	userName:String
	"""手机号"""
	phone:String
}
type SchemeSkuPropertyResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.scheme.SchemeSkuPropertyResponse") {
	year:SchemeSkuPropertyValueResponse
	province:SchemeSkuPropertyValueResponse
	city:SchemeSkuPropertyValueResponse
	county:SchemeSkuPropertyValueResponse
	industry:SchemeSkuPropertyValueResponse
	subjectType:SchemeSkuPropertyValueResponse
	trainingCategory:SchemeSkuPropertyValueResponse
	trainingProfessional:SchemeSkuPropertyValueResponse
	technicalGrade:SchemeSkuPropertyValueResponse
	positionCategory:SchemeSkuPropertyValueResponse
	trainingObject:SchemeSkuPropertyValueResponse
	jobLevel:SchemeSkuPropertyValueResponse
	jobCategory:SchemeSkuPropertyValueResponse
	subject:SchemeSkuPropertyValueResponse
	grade:SchemeSkuPropertyValueResponse
	learningPhase:SchemeSkuPropertyValueResponse
	discipline:SchemeSkuPropertyValueResponse
	certificatesType:SchemeSkuPropertyValueResponse
	practitionerCategory:SchemeSkuPropertyValueResponse
	trainingInstitution:SchemeSkuPropertyValueResponse
	mainAdditionalItem:SchemeSkuPropertyValueResponse
	trainingWay:SchemeSkuPropertyValueResponse
}
type SchemeSkuPropertyValueResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.scheme.SchemeSkuPropertyValueResponse") {
	skuPropertyValueId:String
}
type LearningResultErrorResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.LearningResultErrorResponse") {
	"""用户身份证"""
	userIdCard:String
	"""用户姓名"""
	userName:String
	"""学号"""
	studentNo:String
	"""用户手机号"""
	userPhone:String
	"""方案名称"""
	trainingName:String
	"""
		sku属性
	"""
	skuProperty:SchemeSkuPropertyResponse
	"""操作时间"""
	operateTime:String
	"""错误信息"""
	errorMsg:String
	"""生成时使用的规则ID"""
	ruleId:String
}
type LearningResultErrorStatisticsResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.LearningResultErrorStatisticsResponse") {
	"""智能学习错误数量  //暂不使用"""
	autoLearningErrorCount:Int
	"""补学数据错误数量"""
	supplementStudyErrorCount:Int
}
type StudentTrainingResultSimulateResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.StudentTrainingResultSimulateResponse") {
	"""学号"""
	studentNo:String
	"""归属信息"""
	owner:OwnerModel
	"""学员信息"""
	user:UserModel
	"""学员学习班级信息"""
	studentSchemeLearning:SimulateStudentSchemeLearningModel
	"""学员培训内容"""
	trainingContentList:[TrainingContentModel]
	"""拓展信息
		@see  TrainingResultSimulateConstant
	"""
	expandInfo:Map
	"""// 创建时间"""
	recordCreatedTime:DateTime
	"""@see com.fjhb.platform.jxjy.v1.kernel.consts.SimulateType
		补学数据类型：1、模拟补学全部数据 2、仅模拟考试成绩 默认1
	"""
	type:Int!
}
"""@Description   归属信息
	<AUTHOR>
	@Date 2023/6/29 9:49
"""
type OwnerModel @type(value:"com.fjhb.platform.jxjy.v1.kernel.model.nested.OwnerModel") {
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""单位id"""
	unitId:String
	"""服务商ID"""
	servicerId:String
}
"""学员班级学习信息模型"""
type SimulateStudentSchemeLearningModel @type(value:"com.fjhb.platform.jxjy.v1.kernel.model.nested.SimulateStudentSchemeLearningModel") {
	"""班级id"""
	schemeId:String
	"""班级名称"""
	trainName:String
	"""培训年度"""
	trainYear:String
	"""培训形式"""
	trainType:Int!
	"""科目类型"""
	subjectType:Int!
	"""公需科目名称"""
	publicSubjectName:String
	"""培训开始时间    2023-03-01 08:02:20"""
	trainBeginTime:String
	"""培训结束时间    2023-03-01 08:02:20"""
	trainEndTime:String
	"""合格学时"""
	trainPeriod:String
	"""考试开始时间    2023-03-01 08:02:20"""
	examBeginTime:String
	"""考试结束时间    2023-03-01 08:02:20"""
	examEndTime:String
	"""考试成绩"""
	score:String
	"""合格时间      2023-03-12 13:13:13"""
	qualifiedTime:String
	"""培训属性
		@see SkuPropertyType
	"""
	skuProperty:Map
}
"""培训内容"""
type TrainingContentModel @type(value:"com.fjhb.platform.jxjy.v1.kernel.model.nested.TrainingContentModel") {
	"""学员课程ID"""
	studentCourseId:String
	"""课程id"""
	courseId:String
	"""课程科目类型"""
	courseSubjectType:String
	"""课程名称"""
	name:String
	"""课程学时"""
	period:Double!
	"""课程开始学习时间"""
	startTime:String
	"""课程结束结束时间"""
	endTime:String
	"""测验分数"""
	quizScore:Double
	"""测试开始时间"""
	quizStartTime:String
	"""测试结束时间"""
	quizEndTime:String
}
"""学员信息主题模型"""
type UserModel @type(value:"com.fjhb.platform.jxjy.v1.kernel.model.nested.UserModel") {
	"""用户ID"""
	userId:String
	"""证件号"""
	idCard:String
	"""用户名称"""
	userName:String
	"""手机号"""
	phone:String
}

scalar List
type LearningResultErrorResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [LearningResultErrorResponse]}
