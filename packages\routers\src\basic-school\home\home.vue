<route-meta>
{
"isMenu": true,
"openWhenInit": true,
"closeAble": false,
"title": "首页",
"sort":1,
"icon": "icon-shouye1"
}
</route-meta>

<script lang="ts">
  import Home from '@hbfe/jxjy-admin-basicSchoolHome/src/home.vue'
  import { WXGLY, NZFXS, NZFXSJCB, DQGLY, ZTGLY } from '@/models/RoleTypes'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'

  @RoleTypeDecorator({
    home: [WXGLY],
    Home: [DQGLY, NZFXS, NZFXSJCB],
    queryUnitInfoList: [NZFXS, NZFXSJCB],
    specialHome: [ZTGLY]
  })
  export default class extends Home {}
</script>
