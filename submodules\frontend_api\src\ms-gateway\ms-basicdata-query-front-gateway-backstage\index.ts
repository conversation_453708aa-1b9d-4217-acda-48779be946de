import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-basicdata-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-basicdata-query-front-gateway-backstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum BusinessDictionarySortFieldEnum {
  createdTime = 'createdTime',
  sort = 'sort'
}
export enum JobCategorySortFieldEnum {
  code = 'code',
  sort = 'sort',
  updateTime = 'updateTime',
  available = 'available',
  name = 'name'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * @Description 获取单个业务数据字典信息的查询
<AUTHOR>
@Date 2022/9/27 9:33
@Version 1.0
 */
export class BusinessDataDictionaryCodeRequest {
  /**
   * 字典编码
   */
  code?: number
  /**
   * 业务数据字典类型（必填项）
DEGREE(&quot;学位&quot;, &quot;DEGREE&quot;),
EDUCATION_BACKGROUND(&quot;学历&quot;, &quot;EDUCATION_BACKGROUND&quot;),
ENTERPRISE_ECONOMIC_TYPE(&quot;企业经济类型&quot;, &quot;ENTERPRISE_ECONOMIC_TYPE&quot;),
ENTERPRISE_TYPE(&quot;企业类型&quot;, &quot;ENTERPRISE_TYPE&quot;),
EXECUTIVES_UNIT_TYPE(&quot;主管单位类型&quot;, &quot;EXECUTIVES_UNIT_TYPE&quot;),
GENDER(&quot;性别&quot;, &quot;GENDER&quot;),
HOUSEHOLD_REGISTRATION_TYPE(&quot;户口性质&quot;, &quot;HOUSEHOLD_REGISTRATION_TYPE&quot;),
ID_CARD_TYPE(&quot;证件类型&quot;, &quot;ID_CARD_TYPE&quot;),
INDUSTRY_EXECUTIVES_TYPE(&quot;行业主管类型&quot;, &quot;INDUSTRY_EXECUTIVES_TYPE&quot;),
INDUSTRY_TYPE(&quot;行业类型&quot;, &quot;INDUSTRY_TYPE&quot;),
NATIONALITY(&quot;民族&quot;, &quot;NATIONALITY&quot;),
PERSON_TYPE(&quot;人员类型&quot;, &quot;PERSON_TYPE&quot;),
PERSON_TYPE_GROUP(&quot;人员类型分组&quot;, &quot;PERSON_TYPE_GROUP&quot;),
POLITICS_STATUS(&quot;政治面貌&quot;, &quot;POLITICS_STATUS&quot;),
USER_REGISTER_TYPE(&quot;注册方式&quot;, &quot;USER_REGISTER_TYPE&quot;),
USER_SOURCE_TYPE(&quot;注册来源&quot;, &quot;USER_SOURCE_TYPE&quot;);
@see BusinessDataDictionaryTypeEnum
   */
  businessDataDictionaryType?: string
}

/**
 * 业务数据字典查询
 */
export class BusinessDataDictionaryRequest {
  /**
   * 字典编码
   */
  code?: number
  /**
   * 字典编码扩展
   */
  codeExt?: string
  /**
   * 业务数据字典id集合
   */
  idList?: Array<string>
  /**
   * 父级业务数据字典id
   */
  parentId?: string
  /**
   * 业务数据字典类型（必填项）
   */
  businessDataDictionaryType?: string
  /**
   * 业务数据字典业务配置id
   */
  businessId?: string
  /**
   * 字典名称
   */
  name?: string
  /**
   * 字典名称 匹配方式
1:模糊查询   2:后缀查询   3:前缀查询 0:精确匹配  tips:未传参默认为精确匹配
(1:*name*   2:*name     3:name*)
   */
  nameMatchType?: number
  /**
   * 是否可用（0：禁用 1：启用）
   */
  available?: number
  /**
   * 排序规则
   */
  sortList?: Array<BusinessDictionarySortKParam>
}

/**
 * 证书类型查询
 */
export class CertificateCategoryRequest {
  /**
   * 证书类型名称
   */
  certificateCategoryName?: string
}

/**
 * 证书等级查询
 */
export class CertificateLevelRequest {
  /**
   * 关联实体id（证书类型id/证书类别id/证书专业id）
   */
  relationId?: string
  /**
   * 证书等级名称
   */
  certificateLevelName?: string
}

/**
 * @Description 证书专业查询
<AUTHOR>
@Date 2022/9/23 14:44
@Version 1.0
 */
export class CertificateMajorRequest {
  /**
   * 证书专业名称
   */
  certificateMajorName?: string
}

/**
 * @Description 证书类别查询
<AUTHOR>
@Date 2022/9/23 14:44
@Version 1.0
 */
export class CertificateTypeRequest {
  /**
   * 证书类别名称
   */
  certificateTypeName?: string
}

/**
 * 工种查询
 */
export class JobCategoryRequest {
  /**
   * 工种基本查询条件
   */
  jobCategoryBase?: JobCategoryBaseRequest
  /**
   * 工种排序
   */
  sortList?: Array<JobCategorySortRequest>
}

/**
 * @Description 地区code集合查询条件
<AUTHOR>
@Date 2022/9/27 10:57
@Version 1.0
 */
export class RegionCodeListRequest {
  /**
   * 地区字典业务配置id
@see BusinessDataDictionaryConfigEnum
   */
  businessId?: string
  /**
   * 地区编码集合
   */
  codeList?: Array<string>
}

/**
 * @Description 地区code查询
<AUTHOR>
@Date 2022/9/27 10:44
@Version 1.0
 */
export class RegionCodeRequest {
  /**
   * 地区字典业务配置id
@see BusinessDataDictionaryConfigEnum
   */
  businessId?: string
  /**
   * 地区编码
   */
  code?: string
}

/**
 * 地区查询
 */
export class RegionRequest {
  /**
   * 地区字典业务配置id
@see BusinessDataDictionaryConfigEnum
   */
  businessId?: string
  /**
   * 地区编码
   */
  code?: string
  /**
   * 级别|1省级 2市级 3区县级
   */
  level?: number
}

/**
 * 工种基本查询条件
 */
export class JobCategoryBaseRequest {
  /**
   * 工种id集合
   */
  idList?: Array<string>
  /**
   * 工种类别id
   */
  typeId?: string
  /**
   * 工种编号
   */
  code?: string
  /**
   * 工种名称
   */
  name?: string
  /**
   * 工种名称 匹配方式
   */
  nameMatchType?: number
  /**
   * 父级工种id集合
   */
  parentIdList?: Array<string>
  /**
   * 是否可用（0：禁用 1：启用）
   */
  available?: number
  /**
   * 工种来源(1：国家职业大典 2：平台自定义)
   */
  source?: number
  /**
   * 工种字典业务配置id
@see  BusinessDataDictionaryConfigEnum
   */
  businessId?: string
}

/**
 * 功能描述：工种排序
@Author： wtl
@Date： 2022年8月12日 15:44:42
 */
export class JobCategorySortRequest {
  /**
   * 工种排序字段
   */
  sortField?: JobCategorySortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

export class BusinessDictionarySortKParam {
  /**
   * 排序字段
   */
  sortField?: BusinessDictionarySortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 功能描述：业务数据字典信息
@Author： wtl
@Date： 2022年8月12日 17:25:23
 */
export class BusinessDataDictionaryResponse {
  /**
   * 字典id
   */
  id: string
  /**
   * 字典类型
   */
  type: string
  /**
   * 父级字典id
   */
  parentId: string
  /**
   * 字典编码
   */
  code: number
  /**
   * 字典编码扩展
   */
  codeExt: string
  /**
   * 字典名称
   */
  name: string
  /**
   * 是否可用（0：禁用 1：启用）
   */
  available: number
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 功能描述：证书类型字典信息
@Author： wtl
@Date： 2022年8月12日 11:54:38
 */
export class CertificateCategoryResponse {
  /**
   * 证书类型id
   */
  certificateCategoryId: string
  /**
   * 证书类型名称
   */
  certificateCategoryName: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 功能描述：证书等级字典信息
@Author： wtl
@Date： 2022年8月12日 11:54:38
 */
export class CertificateLevelResponse {
  /**
   * 证书等级id
   */
  certificateLevelId: string
  /**
   * 证书等级名称
   */
  certificateLevelName: string
  /**
   * 排序
   */
  sort: number
}

/**
 * @Description 证书专业字典信息
<AUTHOR>
@Date 2022/9/23 14:30
@Version 1.0
 */
export class CertificateMajorResponse {
  /**
   * 证书专业id
   */
  certificateMajorId: string
  /**
   * 证书专业名称
   */
  certificateMajorName: string
  /**
   * 排序
   */
  sort: number
}

/**
 * @Description 证书类别字典信息
<AUTHOR>
@Date 2022/9/23 14:30
@Version 1.0
 */
export class CertificateTypeResponse {
  /**
   * 证书类别id
   */
  certificateTypeId: string
  /**
   * 证书类别名称
   */
  certificateTypeName: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 功能描述：工种字典信息
@Author： wtl
@Date： 2022年8月12日 11:54:38
 */
export class JobCategoryResponse {
  /**
   * 工种id
   */
  id: string
  /**
   * 工种类别id
   */
  typeId: string
  /**
   * 工种编号
   */
  code: string
  /**
   * 工种名称
   */
  name: string
  /**
   * 父级工种id
   */
  parentId: string
  /**
   * 工种简介
   */
  about: string
  /**
   * 工种来源(1：国家职业大典 2：平台自定义)
   */
  source: number
  /**
   * 是否可用（0：禁用 1：启用）
   */
  available: number
  /**
   * 工种排序
   */
  sort: number
  /**
   * 工种创建时间
   */
  createTime: string
  /**
   * 工种最后修改时间
   */
  updateTime: string
}

/**
 * 功能描述：地区字典信息
@Author： wtl
@Date： 2022年8月12日 11:54:38
 */
export class RegionResponse {
  /**
   * 地区编码
   */
  code: string
  /**
   * 父级地区编码
   */
  parentCode: string
  /**
   * 地区编码路径
   */
  codePath: string
  /**
   * 地区名称
   */
  name: string
  /**
   * 级别|1省级 2市级 3区县级
   */
  level: number
  /**
   * 地区排序
   */
  sort: number
}

export class BusinessDataDictionaryResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BusinessDataDictionaryResponse>
}

export class CertificateCategoryResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CertificateCategoryResponse>
}

export class CertificateLevelResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CertificateLevelResponse>
}

export class CertificateMajorResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CertificateMajorResponse>
}

export class CertificateTypeResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CertificateTypeResponse>
}

export class JobCategoryResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<JobCategoryResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 功能描述：项目级-根据code和业务数据字典类型查询单个业务数据字典信息接口-明细接口
   * 描述：根据code和业务数据字典类型查询当前子项目下的业务数据字典信息
   * @param request :业务数据字典查询条件
   * @return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse
   * @date : 2022年8月12日 15:56:50
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBusinessDataDictionaryInSubProject(
    request: BusinessDataDictionaryCodeRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBusinessDataDictionaryInSubProject,
    operation?: string
  ): Promise<Response<BusinessDataDictionaryResponse>> {
    return commonRequestApi<BusinessDataDictionaryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据id查询单个证书类型字典信息接口-明细接口
   * 描述：根据id查询当前子项目下的证书类型信息
   * @param certificateCategoryId :证书类型id
   * @return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateCategoryResponse
   * @date : 2022年8月12日 14:08:53
   * @param query 查询 graphql 语法文档
   * @param certificateCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCertificateCategoryInSubProject(
    certificateCategoryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCertificateCategoryInSubProject,
    operation?: string
  ): Promise<Response<CertificateCategoryResponse>> {
    return commonRequestApi<CertificateCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { certificateCategoryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据id查询单个证书等级字典信息-明细接口
   * 描述：查询当前子项目下的证书等级信息
   * @param certificateLevelId :证书等级id
   * @return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateLevelResponse
   * @date : 2022年8月12日 14:08:53
   * @param query 查询 graphql 语法文档
   * @param certificateLevelId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCertificateLevelInSubProject(
    certificateLevelId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCertificateLevelInSubProject,
    operation?: string
  ): Promise<Response<CertificateLevelResponse>> {
    return commonRequestApi<CertificateLevelResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { certificateLevelId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据id查询单个证书专业字典信息-明细接口
   * 描述：查询当前子项目下的证书专业信息
   * @param certificateMajorId :证书专业id
   * @return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateMajorResponse
   * @date : 2022年9月27日 14:08:53
   * @param query 查询 graphql 语法文档
   * @param certificateMajorId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCertificateMajorInSubProject(
    certificateMajorId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCertificateMajorInSubProject,
    operation?: string
  ): Promise<Response<CertificateMajorResponse>> {
    return commonRequestApi<CertificateMajorResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { certificateMajorId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据id查询单个证书类别字典信息-明细接口
   * 描述：查询当前子项目下的证书类别信息
   * <AUTHOR>
   * @Date 2022/9/27 14:38
   * @param certificateTypeId :证书类别id
   * @return com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateTypeResponse
   * @param query 查询 graphql 语法文档
   * @param certificateTypeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCertificateTypeInSubProject(
    certificateTypeId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCertificateTypeInSubProject,
    operation?: string
  ): Promise<Response<CertificateTypeResponse>> {
    return commonRequestApi<CertificateTypeResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { certificateTypeId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据工种id查询单个工种字典信息-明细接口
   * 描述：根据工种id查询当前子项目下的工种信息
   * @param id :工种id
   * @return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.JobCategoryResponse
   * @date : 2022年8月12日 15:56:50
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getJobCategoryInSubProject(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getJobCategoryInSubProject,
    operation?: string
  ): Promise<Response<JobCategoryResponse>> {
    return commonRequestApi<JobCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据地区编码查询单个地区字典信息-明细接口
   * 描述：根据地区编码查询当前子项目下的地区信息
   * @param request : 地区查询条件
   * @return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.RegionResponse
   * @date : 2022年10月19日18:32:12
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getRegionInSubProject(
    request: RegionCodeRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRegionInSubProject,
    operation?: string
  ): Promise<Response<RegionResponse>> {
    return commonRequestApi<RegionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-所有地区-列表接口
   * 描述：查询当前子项目下的指定地区字典业务配置id的所有地区信息，默认按排序字段升序排
   * @param businessId : 地区字典业务配置id
   * @return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.RegionResponse>
   * @param query 查询 graphql 语法文档
   * @param businessId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listAllRegionInSubProject(
    businessId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listAllRegionInSubProject,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { businessId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据id集合查询字典信息-列表接口
   * 描述：查询指定id的字典列表，默认按排序字段升序排
   * @param idList :字典id集合
   * @return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse>
   * @date : 2024年1月24日 09:35:49
   * @param query 查询 graphql 语法文档
   * @param idList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessDataDictionaryByIdInSubProject(
    idList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessDataDictionaryByIdInSubProject,
    operation?: string
  ): Promise<Response<Array<BusinessDataDictionaryResponse>>> {
    return commonRequestApi<Array<BusinessDataDictionaryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { idList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询指定业务数据字典类型的字典信息-列表接口
   * 描述：查询指定业务数据字典类型的字典列表，默认按排序字段升序排
   * @param request :业务数据字典查询条件
   * @return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse>
   * @date : 2022年8月12日 17:28:02
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessDataDictionaryInSubProject(
    request: BusinessDataDictionaryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessDataDictionaryInSubProject,
    operation?: string
  ): Promise<Response<Array<BusinessDataDictionaryResponse>>> {
    return commonRequestApi<Array<BusinessDataDictionaryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询下一级地区信息-列表接口
   * 描述：根据父级地区编码查询当前子项目下的地区信息，默认按排序字段升序排
   * @param request : 地区查询条件
   * @return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.RegionResponse>
   * @date : 2022年10月19日18:32:12
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listChildRegionInSubProject(
    request: RegionCodeRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listChildRegionInSubProject,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据地区编码集合查询地区信息-列表接口
   * 描述：根据地区编码集合查询当前子项目下的地区信息集合，默认按排序字段升序排
   * @param request : 地区集合查询条件
   * @return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.RegionResponse>
   * @date : 2022年10月19日18:32:12
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listRegionByCodeInSubProject(
    request: RegionCodeListRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listRegionByCodeInSubProject,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据地区编码查询指定级别的地区信息-列表接口
   * 描述：根据地区编码查询指定级别的地区信息，默认按排序字段升序排
   * @param request : 地区查询条件
   * @return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.RegionResponse>
   * @date : 2022年9月22日 20:00:00
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listRegionInSubProject(
    request: RegionRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listRegionInSubProject,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询指定业务数据字典类型的字典信息-分页列表接口
   * 描述：查询指定业务数据字典类型的字典分页列表，默认按排序字段升序排
   * @param page    :分页对象
   * @param request :业务数据字典查询条件
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse>
   * @date : 2022年8月12日 17:28:02
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageBusinessDataDictionaryInSubProject(
    params: { page?: Page; request?: BusinessDataDictionaryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageBusinessDataDictionaryInSubProject,
    operation?: string
  ): Promise<Response<BusinessDataDictionaryResponsePage>> {
    return commonRequestApi<BusinessDataDictionaryResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询证书类型字典信息-分页列表接口
   * 描述：查询当前子项目下的证书类型信息，默认按排序字段升序排
   * @param page    :分页对象
   * @param request :证书类型查询条件
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateCategoryResponse>
   * @date : 2022年8月12日 14:08:53
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCertificateCategoryInSubProject(
    params: { page?: Page; request?: CertificateCategoryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCertificateCategoryInSubProject,
    operation?: string
  ): Promise<Response<CertificateCategoryResponsePage>> {
    return commonRequestApi<CertificateCategoryResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询证书等级字典信息-分页列表接口
   * 描述：查询当前子项目下的证书等级信息，默认按排序字段升序排
   * @param page    :分页对象
   * @param request :证书等级查询条件
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateLevelResponse>
   * @date : 2022年8月12日 14:08:53
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCertificateLevelInSubProject(
    params: { page?: Page; request?: CertificateLevelRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCertificateLevelInSubProject,
    operation?: string
  ): Promise<Response<CertificateLevelResponsePage>> {
    return commonRequestApi<CertificateLevelResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询证书专业字典信息-分页列表接口
   * 描述：查询当前子项目下的证书专业信息，默认按排序字段升序排
   * @param page    :分页对象
   * @param request :证书专业查询条件
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateMajorResponse>
   * @date : 2022年9月27日 14:08:53
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCertificateMajorInSubProject(
    params: { page?: Page; request?: CertificateMajorRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCertificateMajorInSubProject,
    operation?: string
  ): Promise<Response<CertificateMajorResponsePage>> {
    return commonRequestApi<CertificateMajorResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询证书类别字典信息-分页列表接口
   * 描述：查询当前子项目下的证书类别信息，默认按排序字段升序排
   * <AUTHOR>
   * @Date 2022/9/27 14:30
   * @param page    :分页对象
   * @param request :证书类别查询条件
   * @return com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateTypeResponse>
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCertificateTypeInSubProject(
    params: { page?: Page; request?: CertificateTypeRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCertificateTypeInSubProject,
    operation?: string
  ): Promise<Response<CertificateTypeResponsePage>> {
    return commonRequestApi<CertificateTypeResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询工种字典信息-分页列表接口
   * 描述：查询当前子项目下的工种信息，默认按排序字段升序排
   * @param page    :分页对象
   * @param request :工种查询条件
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.JobCategoryResponse>
   * @date : 2022年8月12日 14:08:53
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageJobCategoryInSubProject(
    params: { page?: Page; request?: JobCategoryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageJobCategoryInSubProject,
    operation?: string
  ): Promise<Response<JobCategoryResponsePage>> {
    return commonRequestApi<JobCategoryResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
