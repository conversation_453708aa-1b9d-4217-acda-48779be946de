import { BatchOrderTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'

/**
 * @description 【集体报名订单】订单详情-批次信息
 */

class BatchOrderDetailBatchInfoVo {
  /**
   * 报名批次号
   */
  batchOrderNo = ''

  /**
   * 批次状态 1:待下单、2:下单中、3:待付款、4:支付中、5:开通中、6:交易成功、7:交易关闭中、8:交易关闭
   */
  orderStatus: BatchOrderTradeStatusEnum = null

  /**
   * 缴费人次
   */
  payPersonTime: number = null

  /**
   * 缴费金额
   */
  payAmount: number = null

  /**
   * 销售渠道
   */
  saleChannel: SaleChannelEnum = null
}

export default BatchOrderDetailBatchInfoVo
