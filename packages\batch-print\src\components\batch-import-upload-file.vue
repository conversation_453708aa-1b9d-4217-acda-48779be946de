<route-meta>
{
"title": "批量导入上传组件"
}
</route-meta>
<!--sku属性-->
<template>
  <div class="f-tc">
    <el-upload
      drag
      :action="resourceUrl"
      ref="upload"
      :auto-upload="false"
      :headers="uploadHeader"
      :on-error="error"
      :on-exceed="handleExceed"
      :on-remove="handleRemove"
      :on-change="change"
      :on-success="handleSuccess"
      :file-list="fileList"
      :multiple="true"
      :limit="1"
      :accept="fileType === 1 ? excelAccepts : certificateAccepts"
    >
      <slot>
        <!-- <el-button type="primary" size="small" plain class="ml20 mt20" icon="el-icon-upload2">选择文件 </el-button> -->
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </slot>
    </el-upload>
    <div class="f-mt10 f-mlr50">
      <el-alert type="info" :closable="false" class="m-alert">
        或者您需要 <a @click.stop="downloadTemplate" class="f-link f-cb f-underline">下载模板</a>
      </el-alert>
    </div>
  </div>
</template>

<script lang="ts">
  import { Component, Emit, Prop, Ref, Vue } from 'vue-property-decorator'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import { bind, debounce } from 'lodash-decorators'
  import QueryShowOffline from '@api/service/common/config/QueryShowOffline'

  @Component
  export default class HBUploadFileComponent extends Vue {
    /**
     * 1、excel
     * 2、微信证书
     */
    @Prop({
      type: Number,
      required: true
    })
    fileType: number

    @Ref('upload') uploader: any
    // 资源服务地址
    resourceUrl = ''
    /**
     * excel的文件类型
     */
    excelAccepts = '.xls,.xlsx'
    /**
     * 微信证书的默认文件后缀
     */
    certificateAccepts = '.p12,.pfx'
    uploadHeader = {}
    $authentication: any
    loading = false
    fileList: Array<any> = new Array<any>()

    /**
     * 是否展示面网授
     */
    get isOnlineClassSupport() {
      const show = QueryShowOffline.getShowOfflineApolloConfig()
      return !show
    }

    constructor() {
      super()
      this.uploadHeader = this.$authentication.getRequestHeader()
    }

    async addInitFile(list: Array<HBFileUploadResponse>) {
      for (const item of list) {
        this.fileList.push({
          name: item.fileName
        })
      }
    }

    //下载导入模板
    @bind
    @debounce(1000)
    async downloadTemplate() {
      const link = document.createElement('a')
      let resolverName = ''
      if (this.isOnlineClassSupport) {
        resolverName =
          '/mfs/ms-file/public/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/template/导入学员名单打印证明模板.xls'
      } else {
        resolverName =
          '/mfs/ms-file/public/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/template/批量导入人员名单打印.xls'
      }
      const resolver = this.$router.resolve({
        name: resolverName
      })
      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      const urlArr = link.href.split('.'),
        typeName = urlArr.pop()
      link.setAttribute('download', '批量导入人员名单打印')
      document.body.appendChild(link)
      link.click()
      link.remove()
    }

    async created() {
      // 获取配置
      this.resourceUrl = ConfigCenterModule.getIngress(ingress.apiendpoint) + '/web/ms-file-v1/web/uploadPublic'
    }

    handleExceed() {
      this.$message.error('每次只能上传一个文件，如需重新上传，请取消已上传的文件!')
      return false
    }

    handleRemove(file: any, fileList: any) {
      this.fileList = new Array<any>()
      this.bindCallBackParam(fileList, 0)
    }

    async handleSuccess(file1: any, fileList: any) {
      console.log(fileList, 'handleSuccess')
      const file = fileList
      if (file) {
        this.bindCallBackParam(file, 1)
      } else {
        this.$message.error('文件上传失败')
      }
      return false
    }

    @Emit('input')
    bindCallBackParam(val: any, type: number) {
      const hbFileUploadResponse = new HBFileUploadResponse()
      // Object.assign(hbFileUploadResponse, val)
      if (type === 1) {
        console.log(val)
        hbFileUploadResponse.fileName = val.name
        hbFileUploadResponse.url = val.response.data
      } else {
        hbFileUploadResponse.fileName = ''
        hbFileUploadResponse.url = ''
      }
      this.$emit('batchUpload', hbFileUploadResponse)
      return hbFileUploadResponse
    }

    error(error: any) {
      this.$message.error(error)
      return false
    }

    change(file: any, fileList: Array<any>) {
      const fileExtension = file.name.substring(file.name.lastIndexOf('.') + 1)
      let accepts = ''
      if (this.fileType === 1) {
        accepts = this.excelAccepts
      } else if (this.fileType === 2) {
        accepts = this.certificateAccepts
      } else {
        this.$message.error('请指定上传的文件类型')
        return
      }
      const isExcel = accepts.split(',').includes(`.${fileExtension}`)
      if (!isExcel) {
        this.$message({
          type: 'warning',
          message: '请选择 【' + accepts + '】 格式的文件!'
        })
        fileList.splice(
          fileList.findIndex((f) => f.uid === file.uid),
          1
        )
        return
      }
      // todo 需要调优成父组件通知上传才真正上传内部服务器，减少没用的文件上传
      this.uploader.submit()
      return false
    }
  }
</script>
