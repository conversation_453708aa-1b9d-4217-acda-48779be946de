<template>
  <div style="padding: 10px 20px">
    <el-button icon="el-icon-knife-fork" type="text" @click="$router.push('/centre')">返回首页</el-button>
    <el-button primary="danger" type="text" @click="showTargetJsonDialog = true">切换比对项</el-button>
    <div style="padding: 20px 10px;">
      <div id="container"></div>
    </div>

    <el-dialog :visible.sync="showTargetJsonDialog" width="40%" :close-on-click-modal="false">
      <el-upload
        class="upload-demo"
        :drag="true"
        accept="json"
        :on-change="changeFile"
        action=""
        :auto-upload="false"
      >
        <div style>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过500kb</div>
        </div>
      </el-upload>
      <div style="display: flex; align-items: center; justify-content: center; margin-top: 30px;">
        <el-button type="primary" size="middle" @click="begin">开始比对</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style>
  #container {
    height: 850px;
  }
</style>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import * as monaco from 'monaco-editor/esm/vs/editor/editor.api'
  import axios from 'axios'
  import SecureRouteInfo from '@hbfe/security-toolkit/src/.cache/ui-assigns.json'

  const secureList = JSON.parse(JSON.stringify(SecureRouteInfo)).sort((a, b) => {
    return a.meta.sort - b.meta.sort
  })

  @Component
  export default class extends Vue {
    showTargetJsonDialog = false
    json = secureList
    sourceText = ''

    test = []
    async mounted() {
      const result = await axios.post('/prettier-text', {
        text: JSON.stringify(this.json),
        responseType: 'text'
      })
      this.sourceText = result.data
      this.diffEditor = monaco.editor.createDiffEditor(document.getElementById('container'))
    }

    doChange() {
      // The diff editor offers a navigator to jump between changes. Once the diff is computed the <em>next()</em> and <em>previous()</em> method allow navigation. By default setting the selection in the editor manually resets the navigation state.
      const originalModel = monaco.editor.createModel(
        this.sourceText,
        'text/plain'
      )
      var modifiedModel = monaco.editor.createModel(
        this.waitCompareText,
        'text/plain'
      )

      this.diffEditor.setModel({
        original: originalModel,
        modified: modifiedModel
      })

      const navi = monaco.editor.createDiffNavigator(this.diffEditor, {
        followsCaret: true, // resets the navigator state when the user selects something in the editor
        ignoreCharChanges: true // jump from line to line
      })

      navi.next()
    }

    diffEditor: any = {}

    waitCompareText = ''

    changeFile(files: any) {
      const reader = new FileReader()
      reader.readAsText(files.raw)
      reader.onload = () => {
        this.waitCompareText = reader.result as string
      }
    }

    begin() {
      this.doChange()
      this.showTargetJsonDialog = false
    }
  }
</script>
