<route-params content="/:id"></route-params>
<route-meta>
{
"isMenu":true,
"hideMenu": true,
"onlyShowOnTab":true,
"title": "个人订单退款详情"
}
</route-meta>

<script lang="ts">
  import RefundPersonalDetail from '@hbfe/jxjy-admin-trade/src/diff/xmlg/refund/personal/detail.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    refundDetail: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    approve: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    retryRecycleRefund: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    confirmRefund: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    continueRefund: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY]
  })
  export default class extends RefundPersonalDetail {}
</script>
