import MsCertificate, { QueryExportTaskRequest } from '@api/ms-gateway/ms-certificate-v1'
import PlatformCertificate, {
  ImportListVerificationRequest,
  PrintTheListRemoveAllStudentRequest
} from '@api/platform-gateway/platform-certificate-v1'
import ImportStudentItem from '@api/service/management/personal-leaning/import-print/model/ImportStudentItem'
import QueryStudentParams from '@api/service/diff/management/jxgx/personal-leaning/QueryStudentParams'
import { Page } from '@hbfe/common'
import PrintCertificationsVo from '@api/service/management/personal-leaning/mutation/vo/PrintCertificationsVo'
export default class ImportPrintStudent {
  /**
   * 导入任务id
   */
  mainTaskId = ''
  /**
   * 导入学员名单
   */
  async importStudentList(filePath: string, name?: string) {
    const params = new ImportListVerificationRequest()
    params.filePath = filePath
    params.fileName = name
    const res = await PlatformCertificate.batchImportStudentList(params)
    this.mainTaskId = res.data ?? ''
    return res.status
  }

  /**
   * 查询最新进度任务
   */
  async queryLatestProgress(userId: string) {
    const request = new QueryExportTaskRequest()
    request.category = 'BATCH_IMPORT_STUDENTS_PRINT_LIST'
    request.taskState = 2
    request.userId = userId
    const page = new Page(1, 1)
    const res = await MsCertificate.queryExportTaskResponsePage({ request, page })
    this.mainTaskId = res.data?.currentPageData?.[0]?.id ?? ''
  }
  /**
   * 查询处理进度
   * @return {Promise<{total: number, success: number}>}
   */
  async queryProcessingProgress() {
    const res = await PlatformCertificate.checkImportPrintListTheUploadProgress(this.mainTaskId)
    return res.data
  }

  /**
   * 查询总数统计
   */
  async queryTotalStatistics() {
    const res = await PlatformCertificate.findUploadSuccessFailureQuantity()
    return {
      success: res.data?.success ?? 0,
      fail: res.data?.fail ?? 0
    }
  }
  /**
   * 查询成功列表
   */
  async queryList(page: Page, params: QueryStudentParams) {
    const request = QueryStudentParams.to(params)
    request.pageNo = page.pageNo
    request.pageSize = page.pageSize
    const res = await PlatformCertificate.returnImportedData(request)
    if (res.status.isSuccess()) {
      page.totalSize = res.data.totalSize
      page.totalPageSize = res.data.totalPageSize
      return res.data.currentPageData.map(ImportStudentItem.from)
    }
    page.totalSize = 0
    page.totalPageSize = 0
    return [] as ImportStudentItem[]
  }

  /**
   * 移除所有数据
   * @param type 导入状态 1 = 成功列表 2 = 失败列表
   * @return {Promise<ResponseStatus>}
   */
  async removeAll(param: QueryStudentParams) {
    const params = Object.assign(new PrintTheListRemoveAllStudentRequest(), QueryStudentParams.to(param))
    const res = await PlatformCertificate.printTheListRemoveAllStudent(params)
    return res.status
  }

  /**
   * 导出失败数据
   * @return {Promise<string>} 文件地址
   */
  async exportFailData(params: QueryStudentParams) {
    const request = QueryStudentParams.to(params)
    const res = await PlatformCertificate.exportFailedData(request)
    return res.data
  }

  /**
   * 批量打印
   */
  async batchPrintStudentCertificates(studentParams: QueryStudentParams, params: PrintCertificationsVo) {
    const res = await PlatformCertificate.learnerImportBatchPrintCertificates({
      batchPrintingRequest: QueryStudentParams.toBatchPrinting(studentParams),
      request: params.toStudent()
    })
    return res.status
  }
}
