<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <div class="m-no-date is-row f-ptb50 f-mtb50">
        <img class="img" src="@design/admin/assets/images/no-data-login.png" alt="" />
        <div class="login-txt">
          <p class="f-cb txt-1">恭喜你！</p>
          <p class="txt-2">成功登录{{ webPortalInfo.title }}~</p>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts">
  import { Vue, Component } from 'vue-property-decorator'
  import PortalVo from '@api/service/common/online-school-config/vo/PortalVo'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'

  @Component({})
  export default class extends Vue {
    webPortalInfo: PortalVo = OnlineSchoolConfigModule.queryPortal.webPortalInfo
  }
</script>
