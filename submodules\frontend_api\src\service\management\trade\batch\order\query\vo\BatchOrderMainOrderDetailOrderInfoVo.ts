import { BatchOrderMainOrderStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderMainOrderStatus'
import { OrderTypeEnum } from '@api/service/common/enums/order/OrderTypes'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'

/**
 * @description 【集体报名订单】主单详情-订单信息
 */
class BatchOrderMainOrderDetailOrderInfoVo {
  /**
   * 主单id
   */
  mainOrderNo = ''

  /**
   * 批次单id
   */
  batchOrderNo = ''

  /**
   * 主单状态
   */
  mainOrderStatus: BatchOrderMainOrderStatusEnum = null

  /**
   * 订单类型
   */
  mainOrderType: OrderTypeEnum = null

  /**
   * 订单创建人Id
   */
  creatorId = ''

  /**
   * 订单创建人名称
   */
  creatorName = ''

  /**
   * 订单创建人账号
   */
  creatorAccount = ''

  /**
   * 销售渠道
   */
  saleChannel: SaleChannelEnum = null
}

export default BatchOrderMainOrderDetailOrderInfoVo
