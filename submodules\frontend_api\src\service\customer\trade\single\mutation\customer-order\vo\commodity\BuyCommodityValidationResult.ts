import { BuyCommodityValidationResult as BuyCommodityValidationResultParent } from '@api/service/customer/commodity/models/BuyCommodityValidationResult'
export default class BuyCommodityValidationResult extends BuyCommodityValidationResultParent {
  /**
   * 新：
   * 200：验证通过
   * 500：验证不通过，带 message
   * 30001：商品不存在
   * 30002：商品已下架
   * 30003：不支持当前渠道购买该商品
   * 30004：当前渠道已关闭
   * 40001：已经存在预定记录(未支付)
   * 40002：已经存在预约记录（已购买）
   * 40003：培训时间已结束
   * 40004: 订单已付款（给Ui，后端未返回）
   * 40005: 订单号已生成，但是没有订单信息
   */
  declare errCode: string
}
