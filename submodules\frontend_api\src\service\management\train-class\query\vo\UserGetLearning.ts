export class LearningResultVo {
  learningResultId = ''
  learningResultName = ''
}
/**
 * 学员在培训班中获取到的数据类
 */
export class UserGetLearning {
  /**
   * 必修课要求学时 选课规则使用
   */
  compulsoryRequirePeriod = 0
  /**
   *选修课要求学时 选课规则使用
   */
  electiveRequirePeriod = 0
  /**
   * 已选课总学时 选课规则使用
   */
  selectedCoursePeriod = 0
  /**
   * 还需选课 选课规则使用
   */
  needSelectedCoursePeriod = 0
  /**
   * 共需完成多少学时
   */
  requirePeriod = 0
  /**
   * 已完成多少学时
   */
  currentPeriod = 0
  /**
   * 整体课程完成学习时间
   */
  courseQualifiedTime: string
  /**
   * 是否已考试
   */
  committedExam = false
  /**
   * 考试是否合格
   */
  examQualified = false
  /**
   * 课程学习是否合格
   */
  courseQualified = false
  /**
   * 考试合格时间
   */
  examQualifiedTime = ''
  /**
   * 考试次数
   */
  examCount = 0
  /**
   * 最高成绩
   */
  maxExamScore = 0
  /**
   * 剩余考试次数 -1无限次
   */
  surplusExamCount = 0
  /**
   * 获得的学时
   */
  credit = 0
  /**
   * 获得的培训模板对象
   */
  learningResult = new LearningResultVo()
  /**
   * 培训结果
   <p>
   -1:未知，培训尚未完成
   1:培训合格
   0:培训不合格
   @see StudentTrainingResults
   */
  trainingResult: number
  /**
   * 取得培训结果时间
   */
  trainingResultTime: string
  /**
   * 心得完成个数
   */
  learningExperienceCount = 0
  /**
   * 心得要求个数
   */
  learningExperienceRequireCount = 0
  /**
 * 方案问卷考核要求提交个数
 */
  questionnaireRequirementCount = 0
  /**
   * 方案问卷需要考核已提交个数
   */
  questionnaireSubmittedCount = 0
  /**
   * 方案问卷不需要考核已提交个数
   */
  questionnaireNoAssessSubmittedCount = 0
  /**
   * 方案问卷已提交个数
   */
  questionnairTotalCount = 0
}
