import CustomerOrderAction from '@api/service/customer/trade/single/mutation/customer-order/CustomerOrderAction'
import CancelOrder from '@api/service/customer/trade/single/mutation/cancel-order/CancelOrder'
import Commodity from '@api/service/customer/trade/single/mutation/customer-order/vo/commodity/Commodity'
import CreateOrder from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/CreateOrder'
import MutationPay from '@api/service/customer/trade/single/mutation/MutationPay'
import MutationOrder from '@api/service/customer/trade/single/mutation/MutationOrder'
class MutationTradeFactory {
  /**
   * @description: 获取创建订单对象
   * @param {*}
   * @return {*}
   */

  getCreateOrderAction() {
    return new CreateOrder()
  }
  /**
   * @description: 获取创建订单对象
   * @param {*}
   * @return {*}
   */

  getMutationOrder() {
    return new MutationOrder()
  }
  /**
   * @description: 获取支付对象
   * @param {*}
   * @return {*}
   */

  getPay() {
    return new MutationPay()
  }
  /**
   * @description: 获取订单相关部业务操作类
   * @param {*}
   * @return {*}
   */

  static get cancelOrderAction() {
    return (orderNo: string) => {
      return new CancelOrder(orderNo)
    }
  }
}

export default new MutationTradeFactory()
