<route-meta>
{
"isMenu": true,
"title": "新建专题",
"sort": 1,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>
<script lang="ts">
  import SpecialTopicsAddIndex from '@hbfe/jxjy-admin-specialTopics/src/add/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    //TODO
    addTopics: [WXGLY],
    addBasicInfo: [WXGLY],
    addPortalInfo: [WXGLY],
    addTraining: [WXGLY],
    addFinish: [WXGLY]
  })
  export default class extends SpecialTopicsAddIndex {}
</script>
