/*
 * @Description: 发票配送查询条件
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-06 08:33:09
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-15 16:22:00
 */
import { DeliveryStatusEnum, DeliveryWayEnum } from '../../enum/DeliveryInvoiceEnum'

export default class DeliveryInvoiceParamVo {
  /**
   * 配送状态
   */
  deliveryStatus?: DeliveryStatusEnum = DeliveryStatusEnum.READY
  /**
   * 收件人姓名
   */
  name?: string = undefined
  /**
   * 证件号
   */
  idCard?: string = undefined
  /**
   * 批次单号
   */
  invoiceNo?: string = undefined
  /**
   * 状态更新开始时间
   */
  startDate?: string = undefined
  /**
   * 状态更新结束时间
   */
  endDate?: string = undefined
  /**
   * 配送方式
   */
  deliveryWay?: DeliveryWayEnum = undefined
  /**
   * 运单号
   */
  theAwb?: string = undefined
  /**
   * 领取人
   */
  recipient?: string = undefined
  /**
   * 冻结状态
   */
  frozenState?: boolean = undefined

  /**
   * 创建者Id
   */
  createUserId: string = undefined

  static to() {
    //转换
  }
}
