import CheckStatusEnumClass, { CheckStatusEnum } from '@api/service/management/train-class/query/enum/CheckStatusEnum'
import AnswerType from '@api/service/management/train-class/mutation/Enum/AnswerTypeEnum'
import {
  ExperienceType,
  StudentLearningExperienceResponse,
  StudentLearningExperienceStatus
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import MsKnowledge from '@api/ms-gateway/ms-knowledge-v1'
import { ResponseStatus } from '@hbfe/common'

/**
 * 学员学习心得模型
 */
class ExperienceRecordItem {
  /**
   * 心得id
   */
  id = ''
  /**
   * 学员心得Id
   */
  studentLearningExperienceId = ''
  /**
   * 主题
   */
  theme = ''
  /**
   * 是否必选
   */
  isRequired: boolean = null
  /**
   * 审核时间
   */
  checkTime = ''
  /**

  /**
   * 作答形式
   */
  answerType: AnswerType = new AnswerType()
  /**
   * 审核状态
   */
  checkStatus: CheckStatusEnumClass = new CheckStatusEnumClass()
  /**
   * 审核结果
   */
  checkResults = 0
  /**
   * 分数
   */
  score = 0

  /**
   * 提交时间
   */
  submitTime = ''

  /**
   * 心得类型 1-班级  2-课程
   */
  learningExperienceTopicType = 0

  /**
   * 删除
   */
  async delete() {
    const res = await MsKnowledge.removeStudentLearningExperience({
      studentExperienceId: this.studentLearningExperienceId,
      learningExperienceTopicType: this.learningExperienceTopicType
    })
    if (res.status.isSuccess()) {
      if (res.data.code === '200') {
        return new ResponseStatus(200, res.data.msg)
      } else {
        return new ResponseStatus(500, res.data.msg)
      }
    } else {
      return res.status
    }
  }
  /**
   * 填充
   */
  static from(dto: StudentLearningExperienceResponse) {
    const vo = new ExperienceRecordItem()
    vo.studentLearningExperienceId = dto.studentLearningExperienceId
    vo.learningExperienceTopicType = dto.experienceType === ExperienceType.SCHEME ? 1 : 2
    vo.score = dto.score
    vo.submitTime = dto.statusChangeTime.find(
      item => item.status === StudentLearningExperienceStatus.SUBMITTED
    )?.changeTime
    vo.checkStatus = new CheckStatusEnumClass((dto.status as unknown) as CheckStatusEnum)
    if (vo.checkStatus.equal(CheckStatusEnum.pass)) {
      vo.checkTime = dto.statusChangeTime.find(item => item.status === StudentLearningExperienceStatus.PASS)?.changeTime
    } else if (vo.checkStatus.equal(CheckStatusEnum.returned)) {
      vo.checkTime = dto.statusChangeTime.find(
        item => item.status === StudentLearningExperienceStatus.RETURNED
      )?.changeTime
    } else {
      vo.checkTime = ''
    }
    return vo
  }
}

export default ExperienceRecordItem
