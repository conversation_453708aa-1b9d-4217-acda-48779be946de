schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""为期别批量创建课程
		@param request:
		<AUTHOR> By Cb
		@since 2024/11/26 20:04
	"""
	batchImportCourse(request:ImportCourseBatchRequest):ImportCourseResponse
	"""校验期别编号在网校是否重复"""
	judgeIssueNumExist(request:JudgeIssueNumExistRequest):Boolean!
	"""更新参训资格住宿信息"""
	updateAccommodationType(request:ChangeAccommodationInfoRequest):Void
}
"""修改住宿信息
	<AUTHOR>
"""
input ChangeAccommodationInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.ChangeAccommodationInfoRequest") {
	"""参训资格ID"""
	trainingQualificationId:String
	"""住宿类型
		0-无需住宿
		1-单人住宿
		2-合住
	"""
	accommodationType:Int!
}
"""校验期别编号是否存在
	<AUTHOR>
	@date 2022/12/5 16:56
"""
input JudgeIssueNumExistRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.JudgeIssueNumExistRequest") {
	"""年度"""
	year:String!
	"""网校id"""
	servicerId:String!
	"""期别编号"""
	issueNum:String!
}
"""导入课程
	<AUTHOR>
	@date 2025/4/27 16:37
"""
input ImportCourseBatchRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.issuecourse.ImportCourseBatchRequest") {
	"""excel文件路径"""
	filePath:String
}
"""导入课程返回
	异常code
	500-接口异常
	3003-表头校验失败
	3004-excel表最大长度校验失败
"""
type ImportCourseResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.issuecourse.ImportCourseResponse") {
	"""成功的导入课程数据"""
	successCourseInfos:[CourseInfo]
	"""失败的导入课程数据"""
	failureCourseInfos:[CourseInfo]
	"""如果有失败数据，查询失败数据文件路径"""
	excelFilePath:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""课程信息"""
type CourseInfo @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.issuecourse.dto.CourseInfo") {
	"""校验code"""
	code:String
	"""校验信息"""
	message:String
	"""课程名称"""
	courseName:String
	"""授课教师"""
	teacherName:String
	"""教师职称"""
	teacherTitle:String
	"""教师所在单位"""
	teacherUnit:String
	"""课程学时"""
	period:String
	"""授课日期"""
	teachingDate:String
	"""授课开始时间"""
	teachingStartTime:String
	"""授课结束时间"""
	teachingEndTime:String
}

scalar List
