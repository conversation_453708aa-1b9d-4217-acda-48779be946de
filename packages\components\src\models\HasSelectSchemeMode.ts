import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
import SkuVo from '@api/service/management/train-class/query/vo/SkuVo'

/**
 * 回传给父组件的数据模型
 */
export class HasSelectSchemeMode {
  /**
   * 选中的学习方案id
   */
  schemeId = ''
  /**
   * 选中的学习方案名称
   */
  scheme = ''
  /**
   * 学习方案id
   */
  id = ''

  /**
   * 培训形式
   */
  trainingMode = new SkuVo<TrainingModeEnum>()

  /**
   * 培训商品id
   */
  commodityId = ''
}
