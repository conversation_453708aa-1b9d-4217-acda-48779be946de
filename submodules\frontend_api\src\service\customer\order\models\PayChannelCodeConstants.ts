/**
 * 支付渠道代码常量类
 */
export class PayChannelCodeConstants {
  static readonly QUICKMONEY_CODE = 'QM' // 快钱B2C网银
  static readonly TONGLIAN_CODE = 'TL' // 通联
  static readonly CCB_CODE = 'CCB' // 中国建设银行
  static readonly BOC_CODE = 'BOC' // 中国银行
  static readonly CMB_CODE = 'CMB' // 招商银行
  static readonly SRCB_CODE = 'SRCB' // 上海农村商业银行
  static readonly ICBC_CODE = 'ICBC' // 中国工商银行
  static readonly BOB_CODE = 'BOB' // 北京银行
  static readonly ABC_CODE = 'ABC' // 中国农业银行
  static readonly CBHB_CODE = 'CBHB' // 渤海银行
  static readonly BJRCB_CODE = 'BJRCB' // 北京农商银行
  static readonly NJCB_CODE = 'NJCB' // 南京银行
  static readonly SPDB_CODE = 'SPDB' // 浦収银行
  static readonly CEB_CODE = 'CEB' // 中国光大银行
  static readonly BCOM_CODE = 'BCOM' // 中国交通银行
  static readonly BEA_CODE = 'BEA' // 东亚银行
  static readonly CMBC_CODE = 'CMBC' // 中国民生银行
  static readonly NBCB_CODE = 'NBCB' // 宁波银行
  static readonly SDB_CODE = 'SDB' // 深圳収展银行
  static readonly HZB_CODE = 'HZB' // 杭州银行
  static readonly GDB_CODE = 'GDB' // 广东収展银行
  static readonly PAB_CODE = 'PAB' // 平安银行
  static readonly CITIC_CODE = 'CITIC' // 中信银行
  static readonly HSB_CODE = 'HSB' // 徽商银行
  static readonly HXB_CODE = 'HXB' // 华夏银行
  static readonly CZB_CODE = 'CZB' // 浙商银行
  static readonly CIB_CODE = 'CIB' // 兴业银行
  static readonly SHB_CODE = 'SHB' // 上海银行
  static readonly GZCB_CODE = 'GZCB' // 广州银行
  static readonly PSBC_CODE = 'PSBC' // 中国邮政储蓄银行
  static readonly UPOP_CODE = 'UPOP' // 银联在线支付
  static readonly DLB_CODE = 'DLB' // 大连银行
  static readonly JSB_CODE = 'JSB' // 江苏银行
  static readonly UNION_CODE = 'UNION' // 银联在线交易渠道
  static readonly ZFB_CODE = 'ALIPAY' // 支付宝
  static readonly ZFB_OPENAPI_PC_CODE = 'ALIPAY_OPENAPI_PC' // 支付宝openapi用
  static readonly WEIXIN_CODE = 'WXPAY' // 微信支付
  static readonly WEIXIN_PUBLIC_NO_CODE = 'WX_PUBLIC_NO_PAY' // 微信公众号支付
  static readonly OTHER_CODE = 'OTHER' // 其它
  static readonly MOBILE_ZFB_CODE = 'MOBILE_ALIPAY' // 移动支付宝
  static readonly HAOBAI_CODE = 'HAOBAI_PAY' // 号百支付
  static readonly CCB_B2B_CODE = 'CCB_B2B' // 建行B2B支付
  static readonly EPAY_CODE = 'EPAY' // E缴通
  static readonly HB_ELECTRONIC_WALLET_CODE = 'HB_ELECTRONIC_WALLET' // 华博电子钱包
  static readonly WEIXIN_H5_CODE = 'WX_H5_PAY' // 微信H5支付
  static readonly ZFB_WAP_V2_CODE = 'ALIPAY_WAP_2.0' // 支付宝OPENAPI手机网站支付接口2.0
  static readonly ICBC_QRCODE = 'ICBC_QRCODE' // 工行二维码支付
  static readonly CCB_QRCODE = 'CCB_QRCODE' // 建行二维码支付
  static readonly YLTY_QRCODE = 'YLTY_QRCODE' // 移领统一扫码支付
  static readonly ZXJH_CODE = 'ZXJH' // 知校聚合支付

  static getTradeChannelName(payChannelCode: string): string {
    switch (payChannelCode) {
      case PayChannelCodeConstants.QUICKMONEY_CODE:
        return '快钱B2C网银'
      case PayChannelCodeConstants.TONGLIAN_CODE:
        return '通联'
      case PayChannelCodeConstants.CCB_CODE:
        return '中国建设银行B2C网银'
      case PayChannelCodeConstants.BOC_CODE:
        return '中国银行B2C网银'
      case PayChannelCodeConstants.CMB_CODE:
        return '招商银行B2C网银'
      case PayChannelCodeConstants.SRCB_CODE:
        return '上海农村商业银行B2C网银'
      case PayChannelCodeConstants.ICBC_CODE:
        return '中国工商银行B2C网银'
      case PayChannelCodeConstants.BOB_CODE:
        return '北京银行B2C网银'
      case PayChannelCodeConstants.NJCB_CODE:
        return '南京银行'
      case PayChannelCodeConstants.SPDB_CODE:
        return '浦収银行'
      case PayChannelCodeConstants.CEB_CODE:
        return '中国光大银行'
      case PayChannelCodeConstants.BCOM_CODE:
        return '中国交通银行'
      case PayChannelCodeConstants.BEA_CODE:
        return '东亚银行'
      case PayChannelCodeConstants.CMBC_CODE:
        return '中国民生银行'
      case PayChannelCodeConstants.NBCB_CODE:
        return '宁波银行'
      case PayChannelCodeConstants.SDB_CODE:
        return '深圳収展银行'
      case PayChannelCodeConstants.HZB_CODE:
        return '杭州银行'
      case PayChannelCodeConstants.GDB_CODE:
        return '广东収展银行'
      case PayChannelCodeConstants.PAB_CODE:
        return '平安银行'
      case PayChannelCodeConstants.CITIC_CODE:
        return '中信银行'
      case PayChannelCodeConstants.HSB_CODE:
        return '徽商银行'
      case PayChannelCodeConstants.HXB_CODE:
        return '华夏银行'
      case PayChannelCodeConstants.CZB_CODE:
        return '浙商银行'
      case PayChannelCodeConstants.CIB_CODE:
        return '兴业银行'
      case PayChannelCodeConstants.SHB_CODE:
        return '上海银行'
      case PayChannelCodeConstants.GZCB_CODE:
        return '广州银行'
      case PayChannelCodeConstants.PSBC_CODE:
        return '中国邮政储蓄银行'
      case PayChannelCodeConstants.UPOP_CODE:
        return '银联在线'
      case PayChannelCodeConstants.DLB_CODE:
        return '大连银行'
      case PayChannelCodeConstants.JSB_CODE:
        return '江苏银行'
      case PayChannelCodeConstants.UNION_CODE:
        return '银联在线交易'
      case PayChannelCodeConstants.ZFB_CODE:
        return '支付宝即时到账'
      case PayChannelCodeConstants.ZFB_OPENAPI_PC_CODE:
        return '支付宝openapi电脑网站'
      case PayChannelCodeConstants.WEIXIN_CODE:
        return '微信Native支付'
      case PayChannelCodeConstants.WEIXIN_PUBLIC_NO_CODE:
        return '微信公众号'
      case PayChannelCodeConstants.OTHER_CODE:
        return '其它'
      case PayChannelCodeConstants.MOBILE_ZFB_CODE:
        return '移动支付宝'
      case PayChannelCodeConstants.HAOBAI_CODE:
        return '号百'
      case PayChannelCodeConstants.CCB_B2B_CODE:
        return '中国建设银行B2B网银'
      case PayChannelCodeConstants.EPAY_CODE:
        return 'E缴通'
      case PayChannelCodeConstants.HB_ELECTRONIC_WALLET_CODE:
        return '华博电子钱包'
      case PayChannelCodeConstants.WEIXIN_H5_CODE:
        return '微信H5'
      case PayChannelCodeConstants.ZFB_WAP_V2_CODE:
        return '支付宝OPENAPI手机网站支付接口2.0'
      case PayChannelCodeConstants.ICBC_QRCODE:
        return '中国工商银行二维码'
      case PayChannelCodeConstants.CCB_QRCODE:
        return '中国建设银行二维码'
      case PayChannelCodeConstants.YLTY_QRCODE:
        return '移领统一扫码'
      case PayChannelCodeConstants.ZXJH_CODE:
        return '知校聚合支付'
      default:
        return '还未支持的交易渠道号' + payChannelCode
    }
  }
}
