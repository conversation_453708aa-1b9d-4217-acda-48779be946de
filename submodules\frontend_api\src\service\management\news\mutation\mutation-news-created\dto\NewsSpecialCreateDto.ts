import CreateDraftNewsVo from '@api/service/management/news/mutation/mutation-news-created/vo/CreateDraftNewsVo'
import MsBusinessNews, { SpecialSubjectNewsCreateRequest } from '@api/ms-gateway/ms-news-v1'
import { Response } from '@hbfe/common'

export default class NewsSpecialCreateDto extends SpecialSubjectNewsCreateRequest {
  static from(createDraftNewsVo: CreateDraftNewsVo) {
    const request = new NewsSpecialCreateDto()
    request.title = createDraftNewsVo.title
    request.summary = createDraftNewsVo.abstract
    request.coverPath = createDraftNewsVo.bgImage
    request.content = createDraftNewsVo.content
    request.source = createDraftNewsVo.source
    request.publishTime = createDraftNewsVo.time
    request.popUps = createDraftNewsVo.isPopup
    request.top = createDraftNewsVo.top
    request.newCategoryId = createDraftNewsVo.categoryType[createDraftNewsVo.categoryType.length - 1]
    request.popupBeginTime = createDraftNewsVo.popupBeginTime
    request.popupEndTime = createDraftNewsVo.popupEndTime
    request.areaCodePath =
      createDraftNewsVo?.areaCodeList && createDraftNewsVo?.areaCodeList.length
        ? '/' + createDraftNewsVo.areaCodeList.join('/')
        : undefined
    request.specialSubjectInfoList = createDraftNewsVo.specialList
    return request
  }

  async toDraft(verifyPopUps = false): Promise<Response<string[]>> {
    this.verifyPopUps = verifyPopUps
    const response = await MsBusinessNews.createSpecialSubjectNews(this)
    if (response?.status?.code === 30006) {
      return Promise.reject((response.status.errors?.length && response.status.errors[0].message) || '')
    }
    return response
  }
}
