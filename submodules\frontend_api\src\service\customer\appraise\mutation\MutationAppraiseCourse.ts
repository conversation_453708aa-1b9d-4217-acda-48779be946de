import MsStudentcourseAppraisalV1, { AppraisalCourseRequest } from '@api/ms-gateway/ms-studentcourse-appraisal-v1'
import MsStudentcourselearningV1 from '@api/ms-gateway/ms-studentcourselearning-v1'
import { ResponseStatus } from '@hbfe/common'

class MutationAppraiseCourse {
  private readonly studentCourseLearningToken: string
  content = ''
  courseScore = 0
  teacherScore = 0

  constructor(studentCourseLearningToken: string) {
    this.studentCourseLearningToken = studentCourseLearningToken
  }

  /**
   * 评价课程，提供学员学习课程的 token， 在进入播放页面的时候一般有提供。
   */
  async doAppraise() {
    const { data } = await MsStudentcourselearningV1.applyCourseAppraisal(this.studentCourseLearningToken)
    if (!data.token) {
      return new ResponseStatus(parseInt(data.applyResult.code), data.applyResult.message)
    }
    const requestContent = new AppraisalCourseRequest()
    requestContent.comment = this.content
    requestContent.courseContentValue = this.courseScore
    requestContent.courseTeacherValue = this.teacherScore
    requestContent.courseAppraisalToken = data.token
    const { status } = await MsStudentcourseAppraisalV1.appraisalCourse(requestContent)
    return status
  }
}

export default MutationAppraiseCourse
