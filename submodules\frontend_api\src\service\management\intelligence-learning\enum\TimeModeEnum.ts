import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum TimeModeEnum {
  /**
   * 按课程物理时长
   */
  physical = 0,
  /**
   * 按课程学习学时
   */
  learning = 1
}
export default class TimeModeType extends AbstractEnum<TimeModeEnum> {
  static enum = TimeModeEnum
  constructor(status?: TimeModeEnum) {
    super()
    this.current = status
    this.map.set(TimeModeEnum.learning, '按课程学习学时')
    this.map.set(TimeModeEnum.physical, '按课程物理时长')
  }
}
