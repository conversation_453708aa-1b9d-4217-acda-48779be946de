<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">基础信息-保存提示</el-button>
        <el-dialog title="系统提醒" :visible.sync="dialog1" width="400px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-f16 f-fb">网校信息修改成功！</p>
              <div class="f-f13 f-mt5">
                修改后的信息会同步更新至网校，请与网校确认是否完成对应的关联配置。修改后网校已生成的数据将不受影响。
              </div>
            </div>
          </div>
          <div slot="footer">
            <el-button type="primary">知道了</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">模板配置-保存提示</el-button>
        <el-dialog title="系统提醒" :visible.sync="dialog2" width="400px" class="m-dialog">
          <div>
            当前网校<span class="f-cb">【福建省专业技术人员继续教育培训平台】</span
            >变更了网校模板，变更模板后对应布局及已配置内容将会清空，是否确认变更模板？<br />
            如若确认更换，请与网校做好沟通，并告知网校门户前端暂停开放。
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog3 = true" type="primary" class="f-mr20">重置密码</el-button>
        <el-drawer title="重置密码" :visible.sync="dialog3" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="">
                    你正在重置<span class="f-fb f-cb">【linlin001】</span>管理员帐号的密码
                  </el-form-item>
                  <el-form-item label="密码：">
                    <el-input clearable show-password placeholder="请输入6-18位由字母、数字或符号组合的密码" />
                    <!--密码安全判断-->
                    <div class="psw-tips">
                      <el-progress :percentage="33.33" color="#e93737" :show-text="false"></el-progress>
                      <!--弱：txt-l，中：txt-m，强：txt-h-->
                      <span class="txt txt-l">弱</span>
                    </div>
                    <div class="psw-tips">
                      <el-progress :percentage="66.66" color="#ee9e2d" :show-text="false"></el-progress>
                      <!--弱：txt-l，中：txt-m，强：txt-h-->
                      <span class="txt txt-m">中</span>
                    </div>
                    <div class="psw-tips">
                      <el-progress :percentage="100" color="#49b042" :show-text="false"></el-progress>
                      <!--弱：txt-l，中：txt-m，强：txt-h-->
                      <span class="txt txt-h">强</span>
                    </div>
                  </el-form-item>
                  <el-form-item label="确认密码：" required>
                    <el-input v-model="form.name" clearable show-password placeholder="请再次输入密码" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确认</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <el-button @click="dialog4 = true" type="primary" class="f-mr20">启用提示</el-button>
        <el-dialog title="系统提醒" :visible.sync="dialog4" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt"
              >您当前正在启用<i class="f-cb">【linlin001】</i>管理员帐号，启用后该帐号可正常登录，是否确认启用？</span
            >
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确认</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">停用提示</el-button>
        <el-dialog title="系统提醒" :visible.sync="dialog5" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt"
              >您当前正在停用<i class="f-cb">【linlin001】</i
              >管理员帐号，停用后该帐号不可登录管理后台，是否确认停用？</span
            >
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确认</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog6 = true" type="primary" class="f-mr20">修改帐号</el-button>
        <el-drawer title="修改帐号" :visible.sync="dialog6" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb10">
              您正在进行管理员帐号修改操作，修改后旧帐号失效，只可使用新帐号登录。
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="管理员姓名：" required>
                    <el-input clearable placeholder="林依依" />
                  </el-form-item>
                  <el-form-item label="管理员帐号：" required>
                    <el-input clearable placeholder="linlin001" />
                  </el-form-item>
                  <el-form-item label="手机号：">
                    <el-input clearable placeholder="13003831002" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确认</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <br />
        <!--增值服务提示-->
        <el-button @click="dialog7 = true" type="primary" class="f-mr20 f-mt20">增值服务提示</el-button>
        <el-dialog title="增值服务" :visible.sync="dialog7" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">确认开启该增值服务吗？</span>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确认</el-button>
          </div>
        </el-dialog>

        <!--增值服务说明-->
        <el-button @click="dialog8 = true" type="primary" class="f-mr20 f-mt20">增值服务说明</el-button>
        <el-drawer
          title="增值服务说明"
          :visible.sync="dialog8"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-table stripe border :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column label="增值服务内容" min-width="118" align="center">
                <template>学习规则</template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">学习规则</div>
                  <div v-else-if="scope.$index === 1">智能学习</div>
                  <div v-else-if="scope.$index === 2">分销服务（专业版）</div>
                  <div v-else>分销服务（基础版）</div>
                </template>
              </el-table-column>
              <el-table-column label="规则说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    指提供网校学习规则配置功能，当学员的学习数据不符合学习规则，系统将按照学习规则生成另外的同步/模拟数据供网校使用。
                  </div>
                  <div v-else-if="scope.$index === 1">
                    指在提供完成课程学习、完成测验、完成考试的规则下，按照指定的开始学习时间和合格时间，模拟真实用户登录平台进行培训的过程。同时为学员提供一键选课功能。
                  </div>
                  <div v-else-if="scope.$index === 2">
                    指网校分销能力，开启网校分销服务后，新增供应商和分销商角色，供应商角色相关功能赋予超管，分销商角色不会默认授予，支持创建分销单位和分销商管理员，支持分销商管理员登录网校管理域开展分销业务。网校新增一级功能菜单营销中心-分销管理，新增分销商销售统计、分销商品销售统计报表。
                  </div>
                  <div v-else>
                    指网校分销能力，开启网校分销服务后，新增供应商和分销商角色，供应商角色相关功能赋予超管，分销商角色不会默认授予，支持创建分销单位和分销商管理员，网校新增一级功能菜单营销中心-分销管理，新增分销商销售统计、分销商品销售统计报表。开启基础版服务后，分销商不支持管理分销业务，仅支持查看交易数据和报表。
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-drawer>
        <!--增值服务提示2-->
        <el-button @click="dialog9 = true" type="primary" class="f-mr20 f-mt20">增值服务提示2</el-button>
        <el-dialog title="增值服务" :visible.sync="dialog9" width="460px" class="m-dialog">
          <div>请选择开通的分销服务类型，保存成功后不支持修改，请谨慎操作：</div>
          <div class="f-mt15">
            <el-radio v-model="radio1" label="1" class="f-mr30">基础版</el-radio
            ><el-radio v-model="radio2" label="2">专业版</el-radio>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确认</el-button>
          </div>
        </el-dialog>
        <!--增值服务提示3-->
        <el-button @click="dialog10 = true" type="primary" class="f-mr20 f-mt20">增值服务提示3</el-button>
        <el-dialog title="增值服务" :visible.sync="dialog10" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">确认关闭该增值服务吗？</span>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确认</el-button>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible1: false,
        dialogVisible2: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
