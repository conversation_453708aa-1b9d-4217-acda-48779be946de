schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""导出批次单明细"""
	exportBatchOrderDetailInServicer(request:BatchOrderRequest,sort:[BatchOrderSortRequest]):Boolean!
	"""专题管理员 - 导出批次单明细"""
	exportBatchOrderDetailInTrainingChannel(request:BatchOrderRequest,sort:[BatchOrderSortRequest]):Boolean!
	"""导出批次单
		@param request
		@param sort
		@return
	"""
	exportBatchOrderInServicer(request:BatchOrderRequest,sort:[BatchOrderSortRequest]):Boolean!
	"""专题管理员 - 导出批次单
		@param request
		@param sort
		@return
	"""
	exportBatchOrderInTrainingChannel(request:BatchOrderRequest,sort:[BatchOrderSortRequest]):Boolean!
	"""导出批次对账
		@param request
		@param sort
		@return
	"""
	exportBatchReconciliationInServicer(request:BatchOrderRequest,sort:[BatchOrderSortRequest]):Boolean!
	"""专题管理员 - 导出批次对账
		@param request
		@param sort
		@return
	"""
	exportBatchReconciliationInTrainingChannel(request:BatchOrderRequest,sort:[BatchOrderSortRequest]):Boolean!
	"""导出批次退货单明细"""
	exportBatchReturnOrderDetailExcelInServicer(request:BatchReturnOrderRequest,sort:[BatchReturnOrderSortRequest]):Boolean!
	"""专题管理员 - 导出批次退货单明细"""
	exportBatchReturnOrderDetailExcelInTrainingChannel(request:BatchReturnOrderRequest,sort:[BatchReturnOrderSortRequest]):Boolean!
	"""导出批次退货单"""
	exportBatchReturnOrderExcelInServicer(request:BatchReturnOrderRequest,sort:[BatchReturnOrderSortRequest]):Boolean!
	"""专题管理员 - 导出批次退货单"""
	exportBatchReturnOrderExcelInTrainingChannel(request:BatchReturnOrderRequest,sort:[BatchReturnOrderSortRequest]):Boolean!
	"""导出批次退货报名对账"""
	exportBatchReturnReconciliationExcelInServicer(request:BatchReturnOrderRequest,sort:[BatchReturnOrderSortRequest]):Boolean!
	"""专题管理员 - 导出批次退货报名对账"""
	exportBatchReturnReconciliationExcelInTrainingChannel(request:BatchReturnOrderRequest,sort:[BatchReturnOrderSortRequest]):Boolean!
	"""导出中心财务数据表
		@param request
		@param sort
		@return
	"""
	exportCentralFinancialDataInServicer(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出学员选课统计
		@param request
		@return
	"""
	exportChooseCourseStatistic(request:ChooseCourseStatisticsRequest):Boolean!
	"""导出商品开通统计列表"""
	exportCommodityOpenReportFormsInServicer(request:TradeReportRequest):Boolean!
	"""商品导出"""
	exportCommoditySkuInServicer(queryRequest:CommoditySkuRequest,sortRequest:[CommoditySkuSortRequest]):Boolean!
	"""导出方案配置专题信息"""
	exportCommoditySkuWithTrainingChannelInServicer(queryRequest:CommoditySkuRequest,sortRequest:[CommoditySkuSortRequest]):Boolean!
	"""导出培训单位销售统计报表   课件供应商维度
		@return
	"""
	exportCourseSalesStatistics(request:CourseSalesStatisticsRequest):Boolean!
	"""发票配送导出
		@return
	"""
	exportInvoiceDeliveryInServicer(request:OfflineInvoiceExportRequest):Boolean!
	"""专题管理员 - 发票配送导出
		@return
	"""
	exportInvoiceDeliveryInTrainingChannel(request:OfflineInvoiceExportRequest):Boolean!
	"""商品期别明细列表导出"""
	exportIssueCommoditySkuInServicer(queryRequest:CommoditySkuRequest,sortRequest:[CommoditySkuSortRequest]):Boolean!
	"""线下发票导出-继续教育
		@return
	"""
	exportOfflineInvoiceInServicerForJxjy(request:OfflineInvoiceExportRequest):Boolean!
	"""专题管理员 - 线下发票导出-继续教育
		@return
	"""
	exportOfflineInvoiceInTrainingChannelForJxjy(request:OfflineInvoiceExportRequest):Boolean!
	"""线上发票导出-继续教育
		@return
	"""
	exportOnlineInvoiceInServicerForJxjy(request:OnlineInvoiceRequest):Boolean!
	"""专题管理员 - 线上发票导出-继续教育
		@return
	"""
	exportOnlineInvoiceInTrainingChannelForJxjy(request:OnlineInvoiceRequest):Boolean!
	"""导出个人订单
		@param request
		@param sort
		@return
	"""
	exportOrderExcelInServicer(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""专题管理员 - 导出个人订单
		@param request
		@param sort
		@return
	"""
	exportOrderExcelInTrainingChannel(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出个人对账
		@param request
		@param sort
		@return
	"""
	exportReconciliationExcelInServicer(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""专题管理员 - 导出个人对账
		@param request
		@param sort
		@return
	"""
	exportReconciliationExcelInTrainingChannel(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出地区开通统计
		@param request
		@return
	"""
	exportRegionOpenReportFormsInServier(request:TradeReportRequest):Boolean!
	"""导出个人退货单"""
	exportReturnOrderExcelInServicer(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""专题管理员 - 导出个人退货单"""
	exportReturnOrderExcelInTrainingChannel(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""导出个人报名退货对账"""
	exportReturnReconciliationExcelInServicer(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""专题管理员 - 导出个人报名退货对账"""
	exportReturnReconciliationExcelInTrainingChannel(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""功能描述：学员导出
		@return : void
		@Author： wtl
		@Date： 2022/1/18 15:14
	"""
	exportStudentExcelInServicer(request:StudentQueryRequest):Boolean!
	"""功能描述：项目级-学员导出
		@return : void
		@Author： wtl
		@Date： 2022年11月17日 15:25:00
	"""
	exportStudentExcelInSubProject(request:StudentQueryRequest):Boolean!
	"""导出专题列表 - 网校超管
		@param request
		@return
	"""
	exportTrainingChannel(request:TrainingChannelRequest):Boolean!
	"""导出专题的方案"""
	exportTrainingChannelCommoditySkuInServicer(queryRequest:CommoditySkuRequest,sortRequest:[CommoditySkuSortRequest]):Boolean!
	"""导出专题明细-专题管理员"""
	exportTrainingChannelCommoditySkuInTrainingChannelAdmin(queryRequest:CommoditySkuRequest,sortRequest:[CommoditySkuSortRequest]):Boolean!
	"""导出专题列表 -专题管理员
		@param request
		@return
	"""
	exportTrainingChannelInTrainingChannelAdmin(request:TrainingChannelRequest):Boolean!
	"""功能描述：分页查询导出任务组
		@param jobGroupRequest : 任务查询条件
		@return : void
		@Author： sjm
		@Date： 2022/1/18 15:14
	"""
	listExportTaskGroupInfoInServicer(jobGroupRequest:JobGroupRequest):[JobGroupResponse]
	"""功能描述：分页查询导出任务组  （专题管理员）
		@param jobGroupRequest : 任务查询条件
		@return : void
		@Author： sjm
		@Date： 2022/1/18 15:14
	"""
	listExportTaskGroupInfoInTrainingChannel(jobGroupRequest:JobGroupRequest):[JobGroupResponse]
	mapTradeRecordParam(request:TradeReportRequest):TradeRecordExcelKParam1
	mapTrainingChannelParam(request:TradeReportRequest,param:TradeRecordExcelKParam):TradeRecordExcelKParam1
}
input TrainingChannelSortKParam @type(value:"com.fjhb.ms.basicdata.query.kernel.service.param.account.nested.TrainingChannelSortKParam") {
	sortField:TrainingChannelEnum
	sortType:SortTypeEnum1
}
input DateScopeRequest1 @type(value:"com.fjhb.ms.basicdata.repository.DateScopeRequest") {
	beginTime:DateTime
	endTime:DateTime
}
input ObsFileMetaData @type(value:"com.fjhb.ms.data.export.commons.utils.async.ObsFileMetaData") {
	bizType:String
	owner:String
	sign:String
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
input CommoditySkuRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.CommoditySkuRequest") {
	commoditySkuIdList:[String]
	saleTitle:String
	issueInfo:IssueInfo
	skuProperty:SkuPropertyRequest1
	externalTrainingPlatform:[String]
	trainingInstitution:[String]
}
input RegionSkuPropertyRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertySearchRequest") {
	regionSearchType:Int
	region:[RegionSkuPropertyRequest1]
}
input SkuPropertyRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.SkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest1
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	trainingObject:[String]
	positionCategory:[String]
	jobLevel:[String]
	jobCategory:[String]
	grade:[String]
	subject:[String]
	learningPhase:[String]
	discipline:[String]
	trainingChannelIds:[String]
	certificatesType:[String]
	practitionerCategory:[String]
	qualificationCategory:[String]
	trainingForm:[String]
}
input IssueInfo @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.IssueInfo") {
	issueId:String
	issueName:String
	issueNum:String
	trainStartTime:DateTime
	trainEndTime:DateTime
	sourceType:String
	sourceId:String
}
input OrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.OrderInfoRequest") {
	orderNoList:[String]
	batchOrderNoList:[String]
	buyerIdList:[String]
	receiveAccountIdList:[String]
	flowNoList:[String]
	channelTypesList:[Int]
	terminalCodeList:[String]
	saleChannel:Int
	saleChannels:[Int]
	saleChannelName:String
	saleChannelIds:[String]
	policyTrainingSchemeIdList:[String]
	declarationUnitCodeList:[String]
}
input SubOrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.SubOrderInfoRequest") {
	subOrderNoList:[String]
	orderInfo:OrderInfoRequest
	discountType:Int
	useDiscount:Boolean
}
"""@Description 范围查询条件
	<AUTHOR>
	@Date 8:51 2022/5/23
"""
input BigDecimalScopeExcelRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.common.request.BigDecimalScopeExcelRequest") {
	"""result >= begin"""
	begin:BigDecimal
	"""result <= end"""
	end:BigDecimal
}
"""范围查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/5/7 15:34
"""
input DateScopeExcelRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.common.request.DateScopeExcelRequest") {
	"""result >= begin"""
	begin:DateTime
	"""result <= end"""
	end:DateTime
}
"""商品查询条件
	<AUTHOR>
	@date 2022/01/25
"""
input CommoditySkuExcelKParam @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.trade.common.request.CommoditySkuExcelKParam") {
	"""商品id"""
	commoditySkuIdList:[String]
	"""商品Sku名称"""
	saleTitle:String
	"""商品sku属性查询"""
	skuProperty:SkuPropertyExcelKParam
}
"""地区查询参数
	<AUTHOR>
	@date 2022/02/25
"""
input RegionSkuPropertyExcelKParam @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.trade.common.request.skuProperty.RegionSkuPropertyExcelKParam") {
	"""地区: 省"""
	province:String
	"""地区: 市"""
	city:String
	"""地区: 区县"""
	county:String
}
"""地区查询请求参数
	<AUTHOR>
	@date 2022/02/25
"""
input RegionSkuPropertySearchExcelKParam @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.trade.common.request.skuProperty.RegionSkuPropertySearchExcelKParam") {
	"""地区匹配方式
		<p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
		<p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
		@see RegionSearchType
	"""
	regionSearchType:Int
	"""地区"""
	region:[RegionSkuPropertyExcelKParam]
}
"""商品sku属性查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input SkuPropertyExcelKParam @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.trade.common.request.skuProperty.SkuPropertyExcelKParam") {
	"""年度"""
	year:[String]
	"""地区"""
	regionSkuPropertySearch:RegionSkuPropertySearchExcelKParam
	"""行业"""
	industry:[String]
	"""科目类型"""
	subjectType:[String]
	"""培训类别"""
	trainingCategory:[String]
	"""培训专业"""
	trainingProfessional:[String]
	"""学段"""
	learningPhase:[String]
	"""学科"""
	discipline:[String]
	"""黑龙江药师-证书类型"""
	certificatesType:[String]
	"""黑龙江药师-执业类别"""
	practitionerCategory:[String]
	"""工勤行业-工种"""
	jobCategory:[String]
	"""卫生行业-培训对象"""
	trainingObject:[String]
}
"""子订单状态变更模型的订单查询参数
	<AUTHOR>
	@date 2022/05/09
"""
input OrderExcelKParam @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.trade.tradereport.OrderExcelKParam") {
	"""买家所在地区路径"""
	buyerAreaPath:[RegionExcelKParam]
}
"""地区查询参数
	<AUTHOR>
	@date 2022/05/09
"""
input RegionExcelKParam @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.trade.tradereport.RegionExcelKParam") {
	"""省code"""
	province:String
	"""市code"""
	city:String
	"""县code"""
	county:String
	"""路径 省-市-县"""
	path:String
}
"""发票关联订单查询参数
	<AUTHOR>
	@date 2022/3/18
"""
input InvoiceAssociationInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.InvoiceAssociationInfoRequest") {
	"""关联订单类型
		0:订单号
		1:批次单号
		@see AssociationTypes
	"""
	associationType:Int
	"""订单号 | 批次单号"""
	associationIdList:[String]
	"""买家信息"""
	buyerIdList:[String]
	"""企业id"""
	unitBuyerUnitIdList:[String]
	"""收款账号"""
	receiveAccountIdList:[String]
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
}
"""异步任务组名返回对象"""
input JobGroupRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.asynctask.JobGroupRequest") {
	"""任务组key"""
	group:String
	"""任务组名（模糊查询）"""
	groupName:String
}
"""功能描述：学员查询条件
	@Author： wtl
	@Date： 2022年1月26日 10:10:33
"""
input StudentQueryRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.basicdata.StudentQueryRequest") {
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:StudentUserRequest
	"""用户认证信息"""
	authentication:AuthenticationRequest
	"""排序"""
	sortList:[StudentSortRequest]
}
input TrainingChannelRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.basicdata.TrainingChannelRequest") {
	"""专题ID集合"""
	ids:[String]
	"""专题名称"""
	name:String
	"""专题入口名称"""
	entryName:String
	"""专题类型：
		1-地区  2-行业  3-班级
	"""
	types:[Int]
	"""行业id"""
	industryId:String
	"""地区路径"""
	regionPath:String
	"""启用状态（0：停用 1：启用）"""
	enable:Boolean
	"""是否显示在网校"""
	showOnNetSchool:Boolean
	"""排序"""
	sort:Int
	"""编辑时间范围"""
	createdDateScope:DateScopeRequest1
	"""排序"""
	sortList:[TrainingChannelSortKParam]
	"""专题管理员用户id"""
	userIdList:[String]
	"""单位名称"""
	unitName:String
}
input AccountRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.basicdata.nested.AccountRequest") {
	"""账户状态 1：正常，2：冻结，3：注销
		@see AccountStatus
	"""
	statusList:[Int]
	"""创建时间范围"""
	createTimeScope:DateScopeRequest
	"""创建人用户id"""
	createdUserId:String
}
"""功能描述：账户认证查询条件
	@Author： wtl
	@Date： 2022年1月26日 09:30:12
"""
input AuthenticationRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.basicdata.nested.AuthenticationRequest") {
	"""帐号"""
	identity:String
}
"""功能描述：学员集体缴费信息
	@Author： wtl
	@Date： 2022年4月21日 08:58:49
"""
input CollectiveRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.basicdata.nested.CollectiveRequest") {
	"""集体缴费管理员用户id集合"""
	collectiveUserIdList:[String]
}
"""功能描述 : 学员排序参数
	@date : 2022/4/1 17:15
"""
input StudentSortRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.basicdata.nested.StudentSortRequest") {
	"""学员排序字段"""
	studentSortField:StudentSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
input StudentUserRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.basicdata.nested.StudentUserRequest") {
	"""工作单位名称（模糊）"""
	companyName:String
	"""用户所属地区路径集合（模糊，右like）"""
	regionPathList:[String]
	"""集体缴费信息"""
	collective:CollectiveRequest
	"""单位所属地区路径集合（模糊，右like）"""
	companyRegionPathList:[String]
	"""单位所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	companyRegionPathListMatchType:Int
	"""是否工勤人员  (0:非工勤人员  1:工勤人员)"""
	isWorker:String
	"""是否退休   (0:非退休人员 1:退休人员)"""
	isRetire:String
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
}
"""@Description
	<AUTHOR>
	@Date 16:18 2022/5/20
"""
input OwnerKParam @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.common.request.OwnerKParam") {
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""企业账号id"""
	rootAccountId:String
	"""所属服务商类型
		@see com.fjhb.domain.basicdata.api.servicer.consts.ServicerTypes
	"""
	servicerType:Int
	"""所属服务商ID"""
	servicerId:String
	"""平台租户id"""
	tenantId:String
	"""平台租户id集合"""
	tenantIdList:[String]
}
"""地区查询参数
	<AUTHOR>
	@date 2022/02/25
"""
input RegionSkuPropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.common.request.skuProperty.RegionSkuPropertyRequest") {
	"""地区: 省"""
	province:String
	"""地区: 市"""
	city:String
	"""地区: 区县"""
	county:String
}
"""地区查询请求参数
	<AUTHOR>
	@date 2022/02/25
"""
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.common.request.skuProperty.RegionSkuPropertySearchRequest") {
	"""地区匹配方式
		<p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
		<p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
		@see RegionSearchType
	"""
	regionSearchType:Int
	"""地区"""
	region:[RegionSkuPropertyRequest]
}
"""商品sku属性查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input SkuPropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.common.request.skuProperty.SkuPropertyRequest") {
	"""年度"""
	year:[String]
	"""地区"""
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	"""行业"""
	industry:[String]
	"""科目类型"""
	subjectType:[String]
	"""培训类别"""
	trainingCategory:[String]
	"""培训专业"""
	trainingProfessional:[String]
	"""学段"""
	learningPhase:[String]
	"""学科"""
	discipline:[String]
	"""黑龙江药师-证书类型"""
	certificatesType:[String]
	"""黑龙江药师-执业类别"""
	practitionerCategory:[String]
	"""工勤行业-工种"""
	jobCategory:[String]
	"""卫生行业-培训对象"""
	trainingObject:[String]
	"""培训形式"""
	trainingForm:[String]
}
"""线下发票查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceExportRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.OfflineInvoice.OfflineInvoiceExportRequest") {
	"""发票ID集合"""
	invoiceIdList:[String]
	"""发票基本信息"""
	basicData:OfflineInvoiceBasicDataRequest
	"""发票关联订单查询参数"""
	associationInfo:InvoiceAssociationInfoRequest
	"""发票配送信息"""
	invoiceDeliveryInfo:OfflineInvoiceDeliveryInfoRequest
	"""所属单位id集合"""
	unitIds:[String]
	"""收款账号id"""
	receiveAccountId:[String]
	"""期别名称"""
	issueId:[String]
	jobName:String
	metaData:ObsFileMetaData
}
"""配送地址信息
	<AUTHOR>
	@date 2022/05/07
"""
input DeliveryAddressRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.OfflineInvoice.nested.DeliveryAddressRequest") {
	"""收件人"""
	consignee:String
}
"""配送状态变更时间查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input DeliveryStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.OfflineInvoice.nested.DeliveryStatusChangeTimeRequest") {
	"""未就绪"""
	unReady:DateScopeExcelRequest
	"""已就绪"""
	ready:DateScopeExcelRequest
	"""已配送"""
	shipped:DateScopeExcelRequest
	"""已自取"""
	taken:DateScopeExcelRequest
}
"""快递信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input ExpressRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.OfflineInvoice.nested.ExpressRequest") {
	"""快递单号"""
	expressNo:String
}
"""发票开票状态变更时间记录查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input InvoiceBillStatusChangTimeRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.OfflineInvoice.nested.InvoiceBillStatusChangTimeRequest") {
	"""发票申请开票时间"""
	unBillDateScope:DateScopeExcelRequest
	"""发票开票时间"""
	successDateScope:DateScopeExcelRequest
}
"""线下发票基本信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceBasicDataRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.OfflineInvoice.nested.OfflineInvoiceBasicDataRequest") {
	"""发票类型
		1:电子发票 2:纸质发票
		@see InvoiceTypes
	"""
	invoiceTypeList:[Int]
	"""发票种类
		1:普通发票 2:增值税普通发票 3:增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategory:[Int]
	"""发票状态
		1:正常
		2:作废
		@see InvoiceStatus
	"""
	invoiceStatus:Int
	"""发票状态变更时间记录"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeRequest
	"""发票开票状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	billStatusList:[Int]
	"""发票开票状态变更时间记录"""
	billStatusChangTime:InvoiceBillStatusChangTimeRequest
	"""发票是否冻结"""
	freeze:Boolean
	"""发票号集合"""
	invoiceNoList:[String]
	"""商品id集合"""
	commoditySkuIdList:[String]
}
"""线下发票配送信息
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceDeliveryInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.OfflineInvoice.nested.OfflineInvoiceDeliveryInfoRequest") {
	"""配送状态
		0:未就绪 1：已就绪 2：已自取 3：已配送
		@see OfflineDeliveryStatus
	"""
	deliveryStatusList:[Int]
	"""配送状态变更时间记录
		0:未就绪 1：已就绪 2：已自取 3：已配送
		key值 {@link OfflineDeliveryStatus}
	"""
	deliveryStatusChangeTime:DeliveryStatusChangeTimeRequest
	"""配送方式
		0:无 1：自取 2：快递
		@see OfflineShippingMethods
	"""
	shippingMethodList:[Int]
	"""快递信息"""
	express:ExpressRequest
	"""自取信息"""
	takeResult:TakeResultRequest
	"""配送地址信息"""
	deliveryAddress:DeliveryAddressRequest
}
"""取件信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input TakeResultRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.OfflineInvoice.nested.TakeResultRequest") {
	"""领取人"""
	takePerson:String
}
"""发票查询参数
	<AUTHOR>
	@date 2022/03/23
"""
input OnlineInvoiceRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.OnlineInvoice.OnlineInvoiceRequest") {
	"""发票id集合"""
	invoiceIdList:[String]
	"""发票基础信息查询参数"""
	basicData:OnlineInvoiceBasicDataRequest
	"""发票关联订单查询参数"""
	associationInfoList:[InvoiceAssociationInfoRequest]
	"""蓝票票据查询参数"""
	blueInvoiceItem:OnlineInvoiceItemRequest
	"""红票票据查询参数"""
	redInvoiceItem:OnlineInvoiceItemRequest
	"""销售渠道
		0-自营 1-分销 不传则查全部
	"""
	saleChannel:Int
	"""所属单位id集合"""
	unitIds:[String]
	"""收款账号id"""
	receiveAccountId:[String]
	"""期别名称"""
	issueId:[String]
	jobName:String
	metaData:ObsFileMetaData
}
"""发票开具状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input BillStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.OnlineInvoice.nested.BillStatusChangeTimeRequest") {
	"""未开具"""
	unBill:DateScopeExcelRequest
	"""开票中"""
	billing:DateScopeExcelRequest
	"""开票成功"""
	success:DateScopeExcelRequest
	"""开票失败"""
	failure:DateScopeExcelRequest
}
"""发票状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input InvoiceStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.OnlineInvoice.nested.InvoiceStatusChangeTimeRequest") {
	"""正常"""
	normal:DateScopeExcelRequest
	"""作废"""
	invalid:DateScopeExcelRequest
}
"""发票基础信息查询参数
	<AUTHOR>
	@date 2022/03/23
"""
input OnlineInvoiceBasicDataRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.OnlineInvoice.nested.OnlineInvoiceBasicDataRequest") {
	"""发票类型
		1:电子发票 2:纸质发票
		@see InvoiceTypes
	"""
	invoiceType:Int
	"""发票种类
		1:普通发票 2:增值税普通发票 3:增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategoryList:[Int]
	"""发票状态变更时间
		@see InvoiceStatus
	"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeRequest
	"""发票状态
		1：正常 2：作废
		@see InvoiceStatus
	"""
	invoiceStatusList:[Int]
	"""蓝票票据开具状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	blueInvoiceItemBillStatusList:[Int]
	"""红票票据开具状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	redInvoiceItemBillStatusList:[Int]
	"""发票是否已冲红"""
	flushed:Boolean
	"""发票是否已生成红票票据"""
	redInvoiceItemExist:Boolean
	"""商品id集合"""
	commoditySkuIdList:[String]
	"""发票是否冻结"""
	freeze:Boolean
}
"""发票票据
	<AUTHOR>
	@date 2022/03/18
"""
input OnlineInvoiceItemRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.OnlineInvoice.nested.OnlineInvoiceItemRequest") {
	"""票据开具状态变更时间"""
	billStatusChangeTime:BillStatusChangeTimeRequest
	"""发票号码"""
	billNoList:[String]
}
"""订单查询参数
	<AUTHOR>
	@date 2022/01/26
"""
input BatchOrderRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchorder.BatchOrderRequest") {
	"""批次单号集合"""
	batchOrderNoList:[String]
	"""批次单基本信息查询参数"""
	basicData:BatchOrderBasicDataRequest
	"""批次单支付信息查询参数"""
	payInfo:OrderPayInfoRequest
	"""批次单创建人查询参数"""
	creatorIdList:[String]
	"""是否已经申请发票"""
	isInvoiceApplied:Boolean
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""批次单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input BatchOrderSortRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchorder.BatchOrderSortRequest") {
	"""需要排序的字段"""
	field:BatchOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""批次单基本信息查询参数
	<AUTHOR>
	@date 2022/04/17
"""
input BatchOrderBasicDataRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchorder.nested.BatchOrderBasicDataRequest") {
	"""批次单状态
		0: 未确认，批次单初始状态
		1: 正常
		2: 交易完成
		3: 交易关闭
		4: 提交处理中 提交处理完成后，变更为NORMAl
		5: 取消处理中
		@see BatchOrderStatus
	"""
	batchOrderStatusList:[Int]
	"""批次单状态变更时间"""
	batchOrderStatusChangeTime:BatchOrderStatusChangeTimeRequest
	"""批次单支付状态
		<p>
		0：未支付
		1：支付中
		2：已支付
		@see BatchOrderPaymentStatus
	"""
	batchOrderPaymentStatusList:[Int]
	"""批次单发货状态
		0: 未发货
		1: 发货中
		2: 已发货
		@see BatchOrderDeliveryStatus
	"""
	batchOrderDeliveryStatusList:[Int]
	"""批次单价格范围
		<p> 查询非0元批次单 begin填0.01
	"""
	batchOrderAmountScope:BigDecimalScopeExcelRequest
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
}
"""批次单状态变更时间查询参数
	<AUTHOR>
	@date 2022/04/17
"""
input BatchOrderStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchorder.nested.BatchOrderStatusChangeTimeRequest") {
	"""未确认"""
	unConfirmed:DateScopeExcelRequest
	"""正常"""
	normal:DateScopeExcelRequest
	"""交易成功"""
	completed:DateScopeExcelRequest
	"""已关闭"""
	closed:DateScopeExcelRequest
	"""提交中"""
	committing:DateScopeExcelRequest
	"""取消处理中"""
	canceling:DateScopeExcelRequest
}
"""批次退货单查询参数
	<AUTHOR>
	@date 2022/04/19
"""
input BatchReturnOrderRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchreturnorder.BatchReturnOrderRequest") {
	"""批次退货单号集合"""
	batchReturnOrderList:[String]
	"""基本信息"""
	basicData:BatchReturnOrderBasicDataRequest
	"""审批信息"""
	approvalInfo:BatchReturnOrderApprovalInfoRequest
	"""批次退货单关联批次单"""
	batchOrderInfo:BatchOrderInfoRequest
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""批次退货单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input BatchReturnOrderSortRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchreturnorder.BatchReturnOrderSortRequest") {
	"""需要排序的字段"""
	field:BatchReturnOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""批次退货单关联批次单查询参数
	<AUTHOR>
	@date 2022/04/19
"""
input BatchOrderInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchreturnorder.nested.BatchOrderInfoRequest") {
	"""批次单号集合"""
	batchOrderNoList:[String]
	"""批次单创建人id集合"""
	creatorIdList:[String]
	"""收款账号ID集合"""
	receiveAccountIdList:[String]
	"""交易流水号集合"""
	flowNoList:[String]
	"""付款类型
		1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
	"""
	paymentOrderTypeList:[Int]
}
"""批次退货单关闭信息
	<AUTHOR>
	@date 2022/4/19
"""
input BatchReturnCloseReasonRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchreturnorder.nested.BatchReturnCloseReasonRequest") {
	"""批次退货单关闭类型（1：卖家取消 2：卖家拒绝退货 3：买家取消 4：确认失败取消）
		@see BatchReturnCloseTypes
	"""
	closeTypeList:[Int]
}
"""批次退货单审批信息查询参数
	<AUTHOR>
	@date 2022/03/18
"""
input BatchReturnOrderApprovalInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchreturnorder.nested.BatchReturnOrderApprovalInfoRequest") {
	"""审批时间"""
	approveTime:DateScopeExcelRequest
}
"""批次退货单基本信息查询参数
	<AUTHOR>
	@date 2022/4/19
"""
input BatchReturnOrderBasicDataRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchreturnorder.nested.BatchReturnOrderBasicDataRequest") {
	"""批次退货单状态
		0: 已创建
		1: 已确认
		2: 取消申请中
		3: 退货处理中
		4: 退货失败
		5: 正在申请退款
		6: 已申请退款
		7: 退款处理中
		8: 退款失败
		9: 退货完成
		10: 退款完成
		11: 退货退款完成
		12: 已关闭
		@see BatchReturnOrderStatus
	"""
	batchReturnOrderStatus:[Int]
	"""批次退货单关闭信息"""
	batchReturnCloseReason:BatchReturnCloseReasonRequest
	"""批次退货单状态变更时间"""
	batchReturnStatusChangeTime:BatchReturnOrderStatusChangeTimeRequest
	"""退款金额范围
		<br> 查询非0元  begin填0.01
	"""
	refundAmountScope:BigDecimalScopeExcelRequest
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
}
"""批次退货单状态变更时间查询参数
	<AUTHOR>
	@date 2022/4/19
"""
input BatchReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchreturnorder.nested.BatchReturnOrderStatusChangeTimeRequest") {
	"""申请退货时间"""
	applied:DateScopeExcelRequest
	"""批次退货完成时间
		<p> 这个参数包含了退货退款完成（批次退货单类型为退货退款）、仅退货完成（批次退货单类型为仅退货）、仅退款完成（批次退货单类型为仅退款）时间，三个时间之间用or匹配
	"""
	returnCompleted:DateScopeExcelRequest
}
"""商品查询条件
	<AUTHOR>
	@date 2022/01/25
"""
input CommoditySkuRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.commodity.CommoditySkuRequest") {
	"""商品id"""
	commoditySkuIdList:[String]
	"""商品名称（精确匹配）"""
	saleTitleList:[String]
	"""商品名称（模糊查询）"""
	saleTitleMatchLike:String
	"""要从查询结果中剔除的商品ID集合"""
	notShowCommoditySkuIdList:[String]
	"""商品售价"""
	price:Double
	"""商品上下架信息"""
	onShelveRequest:OnShelveRequest
	"""培训方案信息"""
	schemeRequest:SchemeRequest
	"""商品sku属性查询"""
	skuPropertyRequest:SkuPropertyRequest
	"""是否展示资源不可用的商品"""
	isDisabledResourceShow:Boolean
	"""是否展示所有资源
		（该字段会屏蔽可见渠道、商品资源是否可用、商品上下架状态三个条件）
	"""
	isShowAll:Boolean
	"""专题id"""
	trainingChannelIds:[String]
	"""是否存在专题"""
	existTrainingChannel:Boolean
	"""管理系统平台"""
	externalTrainingPlatform:[String]
}
"""商品排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input CommoditySkuSortRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.commodity.CommoditySkuSortRequest") {
	"""用来排序的字段"""
	sortField:CommoditySkuSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""商品上下架相关查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input OnShelveRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.commodity.nested.onshelve.OnShelveRequest") {
	"""商品上下架状态
		<br> 0:已下架 1：已上架
	"""
	onShelveStatus:Int
}
"""培训方案相关查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input SchemeRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.commodity.nested.scheme.SchemeRequest") {
	"""培训方案ID"""
	schemeIdList:[String]
	"""培训方案类型
		<br> chooseCourseLearning:选课规则 autonomousCourseLearning:自主学习
	"""
	schemeType:String
	"""培训方案名称(模糊查询)"""
	schemeName:String
	"""培训开始时间"""
	trainingBeginDate:DateScopeRequest
	"""培训结束时间"""
	trainingEndDate:DateScopeRequest
}
"""<AUTHOR>
input ChooseCourseStatisticsRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.course.ChooseCourseStatisticsRequest") {
	"""培训班级上的年度"""
	year:String
	"""课件供应商ID"""
	supplierId:[String]
	"""技术等级"""
	technicalGrade:[String]
	"""课程Id"""
	courseId:[String]
	"""选课时间开始"""
	chooseCourseDateStart:DateTime
	"""选课时间结束"""
	chooseCourseDateEnd:DateTime
}
"""<AUTHOR>
input CourseSalesStatisticsRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.course.CourseSalesStatisticsRequest") {
	"""课件供应商ID"""
	supplierId:[String]
	"""选课时间开始"""
	chooseCourseDateStart:DateTime
	"""选课时间结束"""
	chooseCourseDateEnd:DateTime
}
"""订单查询参数
	<AUTHOR>
	@date 2022/01/26
"""
input OrderRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.order.request.OrderRequest") {
	"""订单号集合"""
	orderNoList:[String]
	"""子订单号集合"""
	subOrderNoList:[String]
	"""子订单退货状态
		0: 未退货
		1: 退货申请中
		2: 退货中
		3: 退货成功
		4: 退款中
		5: 退款成功
		@see SubOrderReturnStatus
	"""
	subOrderReturnStatus:[Int]
	"""订单基本信息查询参数"""
	orderBasicData:OrderBasicDataRequest
	"""订单支付信息查询"""
	payInfo:OrderPayInfoRequest
	"""买家查询参数"""
	buyerIdList:[String]
	"""发货商品信息"""
	deliveryCommodity:CommoditySkuRequest1
	"""销售渠道
		0-自营 1-分销 不传则查全部
	"""
	saleChannel:Int
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""管理系统平台"""
	externalTrainingPlatform:[String]
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""订单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input OrderSortRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.order.request.OrderSortRequest") {
	"""需要排序的字段"""
	field:OrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""订单基本信息查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input OrderBasicDataRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.order.request.nested.OrderBasicDataRequest") {
	"""订单类型
		1:常规订单 2:批次关联订单
		@see com.fjhb.domain.trade.api.order.consts.OrderTypes
	"""
	orderType:Int
	"""批次单号"""
	batchOrderNoList:[String]
	"""订单状态
		<br> 1:正常 2：交易完成 3：交易关闭
		@see OrderStatus
	"""
	orderStatusList:[Int]
	"""订单支付状态
		<br> 0:未支付 1：支付中 2：已支付
		@see com.fjhb.domain.trade.api.order.consts.OrderPaymentStatus
	"""
	orderPaymentStatusList:[Int]
	"""订单发货状态
		<br> 0:未发货 1：发货中 2：已发货
		@see com.fjhb.domain.trade.api.order.consts.OrderDeliveryStatus
	"""
	orderDeliveryStatusList:[Int]
	"""订单状态变更时间"""
	orderStatusChangeTime:OrderStatusChangeTimeRequest
	"""购买渠道
		<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelTypesList:[Int]
	"""终端
		<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
		@see PurchaseChannelTerminalCodes
	"""
	terminalCodeList:[String]
	"""订单价格范围
		<br> 查询非0元订单 begin填0.01
	"""
	orderAmountScope:BigDecimalScopeExcelRequest
}
"""订单支付信息相关查询参数
	<AUTHOR>
	@date 2022/01/27
"""
input OrderPayInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.order.request.nested.OrderPayInfoRequest") {
	"""收款账号ID"""
	receiveAccountIdList:[String]
	"""交易流水号"""
	flowNoList:[String]
	"""付款类型
		1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
	"""
	paymentOrderTypeList:[Int]
}
"""订单状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input OrderStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.order.request.nested.OrderStatusChangeTimeRequest") {
	"""订单处于正常状态时间范围(创建时间范围)"""
	normalDateScope:DateScopeExcelRequest
	"""订单创建时间范围"""
	completedDatesScope:DateScopeExcelRequest
}
"""退货单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.returnorder.ReturnOrderRequest") {
	"""退货单号"""
	returnOrderNoList:[String]
	"""基本信息"""
	basicData:ReturnOrderBasicDataRequest
	"""审批信息"""
	approvalInfo:ReturnOrderApprovalInfoRequest
	"""退货商品id集合"""
	returnCommoditySkuIdList:[String]
	"""退款商品id集合"""
	refundCommoditySkuIdList:[String]
	"""退货单关联子订单查询参数"""
	subOrderInfo:SubOrderInfoRequest
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""退货商品"""
	returnCommodity:CommoditySkuRequest1
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""订单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input ReturnSortRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.returnorder.ReturnSortRequest") {
	"""需要排序的字段"""
	field:ReturnOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""退货单关闭信息
	<AUTHOR>
	@date 2022年4月11日 11:33:35
"""
input ReturnCloseReasonRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.returnorder.nested.ReturnCloseReasonRequest") {
	"""退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
		@see ReturnOrderCloseTypes
	"""
	closeTypeList:[Int]
}
"""退货单审批信息查询参数
	<AUTHOR>
	@date 2022/03/18
"""
input ReturnOrderApprovalInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.returnorder.nested.ReturnOrderApprovalInfoRequest") {
	"""审批时间"""
	approveTime:DateScopeExcelRequest
}
"""退货单基本信息查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderBasicDataRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.returnorder.nested.ReturnOrderBasicDataRequest") {
	"""退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)"""
	returnOrderStatus:[Int]
	"""退货单类型
		1-仅退货
		2-仅退款
		3-退货并退款
		4-部分退货
		5-部分退款
		6-部分退货并部分退款
		7-部分退货并全额退款
		8-全部退货并部分退款
	"""
	returnOrderTypes:[Int]
	"""退货单申请来源类型
		SUB_ORDER
		BATCH_RETURN_ORDER
		@see ReturnOrderApplySourceTypes
	"""
	applySourceType:String
	"""来源ID集合"""
	applySourceIdList:[String]
	"""退货单关闭信息"""
	returnCloseReason:ReturnCloseReasonRequest
	"""退货单状态变更时间"""
	returnStatusChangeTime:ReturnOrderStatusChangeTimeRequest
	"""退款金额范围
		<br> 查询非0元  begin填0.01
	"""
	refundAmountScope:BigDecimalScopeExcelRequest
}
"""退货单状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.returnorder.nested.ReturnOrderStatusChangeTimeRequest") {
	"""申请退货时间"""
	applied:DateScopeExcelRequest
	"""退货单完成时间
		<br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
	"""
	returnCompleted:DateScopeExcelRequest
}
"""交易变更记录查询参数
	<AUTHOR>
	@date 2022/05/09
"""
input TradeRecordExcelKParam @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.tradereport.TradeRecordExcelKParam") {
	"""交易时间"""
	tradeTime:DateScopeExcelRequest
	"""订单查询参数"""
	order:OrderExcelKParam
	"""商品查询参数"""
	commoditySku:CommoditySkuExcelKParam
	"""归属信息"""
	owner:OwnerKParam
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""排除的销售渠道
		0-自营 1-分销 2-专题 3-华医网
	"""
	excludedSaleChannels:[Int]
	"""专题id"""
	saleChannelIds:[String]
	"""分销商id"""
	distributorId:String
	"""门户标识"""
	portalIdentifier:String
	"""查看非推广门户数据 | true 为勾选效果"""
	notDistributionPortal:Boolean
}
"""商品开通统计报表查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input TradeReportRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.tradereport.TradeReportRequest") {
	"""交易时间范围"""
	tradeTime:DateScopeExcelRequest
	"""买家所在地区路径"""
	buyerAreaPath:[String]
	"""商品查询条件"""
	commoditySku:CommoditySkuRequest12
	"""是否包含集体缴费信息"""
	containsCollective:Boolean!
	"""买家所在地区是否需要包含全部下级地区数据"""
	isAllCotained:Boolean
	"""销售渠道
		0-自营 1-分销 不传则查全部
	"""
	saleChannel:Int
	"""排除的销售渠道
		0-自营 1-分销 2-专题 3-华医网
	"""
	excludedSaleChannels:[Int]
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
	"""分销商id"""
	distributorId:String
	"""门户id"""
	portalId:String
	"""查看非推广门户数据 | true 为勾选效果"""
	notDistributionPortal:Boolean
	jobName:String
	metaData:ObsFileMetaData
}
"""商品查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input CommoditySkuRequest12 @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.tradereport.nest.CommoditySkuRequest") {
	"""商品Sku名称"""
	saleTitle:String
	"""商品sku属性查询"""
	skuProperty:SkuPropertyRequest
	"""学习方案查询参数"""
	scheme:SchemeRequest1
	"""排除的商品List"""
	excludeCommodityIdlist:[String]
}
"""方案查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input SchemeRequest1 @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.tradereport.nest.SchemeRequest") {
	"""方案类型
		@see SchemeType
	"""
	schemeType:String
	"""方案学时"""
	schemePeriodScope:DoubleScopeRequest
}
enum SortTypeEnum1 @type(value:"com.fjhb.ms.basicdata.enums.SortTypeEnum") {
	ASC
	DESC
}
enum TrainingChannelEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.TrainingChannelEnum") {
	publishedTime
}
"""范围查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/5/7 15:34
"""
type DateScopeExcelRequest1 @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.common.request.DateScopeExcelRequest") {
	"""result >= begin"""
	begin:DateTime
	"""result <= end"""
	end:DateTime
}
"""商品查询条件
	<AUTHOR>
	@date 2022/01/25
"""
type CommoditySkuExcelKParam1 @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.trade.common.request.CommoditySkuExcelKParam") {
	"""商品id"""
	commoditySkuIdList:[String]
	"""商品Sku名称"""
	saleTitle:String
	"""商品sku属性查询"""
	skuProperty:SkuPropertyExcelKParam1
}
"""地区查询参数
	<AUTHOR>
	@date 2022/02/25
"""
type RegionSkuPropertyExcelKParam1 @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.trade.common.request.skuProperty.RegionSkuPropertyExcelKParam") {
	"""地区: 省"""
	province:String
	"""地区: 市"""
	city:String
	"""地区: 区县"""
	county:String
}
"""地区查询请求参数
	<AUTHOR>
	@date 2022/02/25
"""
type RegionSkuPropertySearchExcelKParam1 @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.trade.common.request.skuProperty.RegionSkuPropertySearchExcelKParam") {
	"""地区匹配方式
		<p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
		<p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
		@see RegionSearchType
	"""
	regionSearchType:Int
	"""地区"""
	region:[RegionSkuPropertyExcelKParam1]
}
"""商品sku属性查询参数
	<AUTHOR>
	@date 2022/01/25
"""
type SkuPropertyExcelKParam1 @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.trade.common.request.skuProperty.SkuPropertyExcelKParam") {
	"""年度"""
	year:[String]
	"""地区"""
	regionSkuPropertySearch:RegionSkuPropertySearchExcelKParam1
	"""行业"""
	industry:[String]
	"""科目类型"""
	subjectType:[String]
	"""培训类别"""
	trainingCategory:[String]
	"""培训专业"""
	trainingProfessional:[String]
	"""学段"""
	learningPhase:[String]
	"""学科"""
	discipline:[String]
	"""黑龙江药师-证书类型"""
	certificatesType:[String]
	"""黑龙江药师-执业类别"""
	practitionerCategory:[String]
	"""工勤行业-工种"""
	jobCategory:[String]
	"""卫生行业-培训对象"""
	trainingObject:[String]
}
"""子订单状态变更模型的订单查询参数
	<AUTHOR>
	@date 2022/05/09
"""
type OrderExcelKParam1 @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.trade.tradereport.OrderExcelKParam") {
	"""买家所在地区路径"""
	buyerAreaPath:[RegionExcelKParam1]
}
"""地区查询参数
	<AUTHOR>
	@date 2022/05/09
"""
type RegionExcelKParam1 @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.trade.tradereport.RegionExcelKParam") {
	"""省code"""
	province:String
	"""市code"""
	city:String
	"""县code"""
	county:String
	"""路径 省-市-县"""
	path:String
}
"""功能描述：排序类型
	@Author： wtl
	@Date： 2021/12/27 10:30
"""
enum SortTypeEnum @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.basicdata.nested.SortTypeEnum") {
	"""升序"""
	ASC
	"""降序"""
	DESC
}
"""功能描述：学员排序字段
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
enum StudentSortFieldEnum @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.basicdata.nested.StudentSortFieldEnum") {
	"""创建时间"""
	createdTime
}
"""@Description
	<AUTHOR>
	@Date 16:18 2022/5/20
"""
type OwnerKParam1 @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.common.request.OwnerKParam") {
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""企业账号id"""
	rootAccountId:String
	"""所属服务商类型
		@see com.fjhb.domain.basicdata.api.servicer.consts.ServicerTypes
	"""
	servicerType:Int
	"""所属服务商ID"""
	servicerId:String
	"""平台租户id"""
	tenantId:String
	"""平台租户id集合"""
	tenantIdList:[String]
}
"""排序参数
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum SortPolicy @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.common.request.SortPolicy") {
	"""正序"""
	ASC
	"""倒序"""
	DESC
}
"""批次单可用于排序的字段
	<AUTHOR>
	@date 2022/04/17
"""
enum BatchOrderSortField @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchorder.nested.BatchOrderSortField") {
	"""批次单未确认时间（批次单创建）"""
	BATCH_ORDER_UN_CONFIRMED_TIME
	"""批次单提交时间"""
	BATCH_ORDER_COMMIT_TIME
}
"""批次退货单可用于排序的字段
	<AUTHOR>
	@date 2022/04/19
"""
enum BatchReturnOrderSortField @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchreturnorder.nested.BatchReturnOrderSortField") {
	"""批次退货单创建时间"""
	CREATED_TIME
}
"""商品可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum CommoditySkuSortField @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.commodity.nested.CommoditySkuSortField") {
	"""上架时间"""
	ON_SHELVE_TIME
	"""最新编辑时间"""
	LAST_EDIT_TIME
	"""期别培训开始时间(安徽建设期别，仅解决前端编译不通过问题，未实现)"""
	ISSUE_TRAINING_BEGIN_TIME
	"""商品创建时间(仅解决前端编译不通过问题，未实现)"""
	COMMODITY_CREATED_TIME
	"""商品销售数"""
	SALE_TOTAL_NUMBER
	"""商品sku属性-年度"""
	SKU_PROPERTY_YEAR
	"""专题排序"""
	TRAINING_CHANNEL
}
"""订单可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum OrderSortField @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.order.request.nested.OrderSortField") {
	"""订单创建时间"""
	ORDER_NORMAL_TIME
	"""订单交易完成时间"""
	ORDER_COMPLETED_TIME
}
"""退货单可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum ReturnOrderSortField @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.returnorder.nested.ReturnOrderSortField") {
	"""退货单申请时间"""
	APPLIED_TIME
}
"""交易变更记录查询参数
	<AUTHOR>
	@date 2022/05/09
"""
type TradeRecordExcelKParam1 @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.tradereport.TradeRecordExcelKParam") {
	"""交易时间"""
	tradeTime:DateScopeExcelRequest1
	"""订单查询参数"""
	order:OrderExcelKParam1
	"""商品查询参数"""
	commoditySku:CommoditySkuExcelKParam1
	"""归属信息"""
	owner:OwnerKParam1
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""排除的销售渠道
		0-自营 1-分销 2-专题 3-华医网
	"""
	excludedSaleChannels:[Int]
	"""专题id"""
	saleChannelIds:[String]
	"""分销商id"""
	distributorId:String
	"""门户标识"""
	portalIdentifier:String
	"""查看非推广门户数据 | true 为勾选效果"""
	notDistributionPortal:Boolean
}
"""异步任务组名返回对象"""
type JobGroupResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.export.JobGroupResponse") {
	"""异步任务组key"""
	group:String
	"""异步任务组名"""
	groupName:String
	"""排序大小"""
	order:Int!
	"""所在域"""
	domain:[String]
}

scalar List
