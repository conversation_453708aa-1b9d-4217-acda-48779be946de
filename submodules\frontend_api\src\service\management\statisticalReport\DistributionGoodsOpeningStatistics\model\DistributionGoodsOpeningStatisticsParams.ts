import DateScope from '@api/service/common/models/DateScope'
import PriceScope from '@api/service/common/models/PriceScope'
import {
  DoubleScopeRequest,
  QueryWayType,
  StatisticTradeRecordRequest
} from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import {
  QueryWayType as QueryWayTypeExport,
  StatisticTradeRecordRequest as StatisticTradeRecordRequestExport
} from '@api/platform-gateway/fxnl-data-export-gateway-backstage'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

export default class DistributionGoodsOpeningStatisticsParams {
  /**
   * 分销商品名称
   */
  distributedTradeName = ''
  /**
   * 分销商名称
   */
  distributorName = ''
  /**
   * 分销商Id
   */
  distributorId = ''
  /**
   * 推广门户名称
   */
  portalPromoteTheName = ''
  /**
   * 推广门户Id
   */
  portalPromoteId = ''
  /**
   * 报名时间
   */
  registrationPeriod: DateScope = new DateScope()
  /**
   * 分销价格
   */
  distributionPrice: PriceScope = new PriceScope()

  /**
   * 查看非门户推广数据
   */
  isPortalData: boolean = undefined
  /**
   * 培训形式
   */
  trainingWay: TrainingModeEnum = undefined
  static toStatisticTradeRecordRequest(dto: DistributionGoodsOpeningStatisticsParams) {
    const vo = new StatisticTradeRecordRequest()
    if (dto.distributorId) {
      vo.distributorIdList = [dto.distributorId]
    }
    // 查看非推广门户时，传false
    if (dto.isPortalData) {
      vo.isPortalData = !dto.isPortalData
    }
    if (dto.portalPromoteId && !dto.isPortalData) {
      vo.portalId = dto.portalPromoteId
    }
    vo.commodityPriceScope = new DoubleScopeRequest()
    vo.commodityPriceScope.begin = dto.distributionPrice.min
    vo.commodityPriceScope.end = dto.distributionPrice.max
    vo.queryDateScope = dto.registrationPeriod
    vo.commodityName = dto.distributedTradeName
    vo.queryWayType = QueryWayType.ALL
    vo.propertyList = []
    if (dto.trainingWay) {
      vo.propertyList.push({
        propertyKey: 'trainingWay',
        propertyValue: dto.trainingWay
      })
    }
    return vo
  }

  static toStatisticTradeRecordRequestExport(dto: DistributionGoodsOpeningStatisticsParams) {
    const vo = new StatisticTradeRecordRequestExport()
    vo.commodityName = dto.distributedTradeName
    if (dto.distributorId) {
      vo.distributorIdList = [dto.distributorId]
    }
    // 查看非推广门户时，传false
    if (dto.isPortalData) {
      vo.isPortalData = !dto.isPortalData
    }
    if (dto.portalPromoteId && !dto.isPortalData) {
      vo.portalId = dto.portalPromoteId
    }
    vo.commodityPriceScope = new DoubleScopeRequest()
    vo.commodityPriceScope.begin = dto.distributionPrice.min
    vo.commodityPriceScope.end = dto.distributionPrice.max
    vo.queryDateScope = dto.registrationPeriod
    vo.queryWayType = QueryWayTypeExport.ALL
    vo.queryWayType = QueryWayTypeExport.ALL
    return vo
  }
}
