import getContractStatus from './queries/getContractStatus.graphql'
import applyForService from './mutates/applyForService.graphql'
import cancelAllCVendorAuthPromotionTraining from './mutates/cancelAllCVendorAuthPromotionTraining.graphql'
import createChannelVendor from './mutates/createChannelVendor.graphql'
import createCoursewareSupplier from './mutates/createCoursewareSupplier.graphql'
import createParticipatingUnit from './mutates/createParticipatingUnit.graphql'
import createTrainingInstitution from './mutates/createTrainingInstitution.graphql'
import createTrainingInstitutionPortal from './mutates/createTrainingInstitutionPortal.graphql'
import disableTrainingInstitution from './mutates/disableTrainingInstitution.graphql'
import enableTrainingInstitution from './mutates/enableTrainingInstitution.graphql'
import removeTrainingInstitutionPortal from './mutates/removeTrainingInstitutionPortal.graphql'
import resumeWithChannelVendorSigned from './mutates/resumeWithChannelVendorSigned.graphql'
import resumeWithCoursewareSupplierSigned from './mutates/resumeWithCoursewareSupplierSigned.graphql'
import resumeWithTrainingInstitutionSigned from './mutates/resumeWithTrainingInstitutionSigned.graphql'
import signUpChannelVendor from './mutates/signUpChannelVendor.graphql'
import signUpCoursewareSupplier from './mutates/signUpCoursewareSupplier.graphql'
import signUpTrainingInstitution from './mutates/signUpTrainingInstitution.graphql'
import suspendWithChannelVendorSigned from './mutates/suspendWithChannelVendorSigned.graphql'
import suspendWithCoursewareSupplierSigned from './mutates/suspendWithCoursewareSupplierSigned.graphql'
import suspendWithTrainingInstitutionSigned from './mutates/suspendWithTrainingInstitutionSigned.graphql'
import updateChannelVendor from './mutates/updateChannelVendor.graphql'
import updateCoursewareSupplier from './mutates/updateCoursewareSupplier.graphql'
import updateParticipatingUnit from './mutates/updateParticipatingUnit.graphql'
import updateTrainingInstitution from './mutates/updateTrainingInstitution.graphql'
import updateTrainingInstitutionPortal from './mutates/updateTrainingInstitutionPortal.graphql'
import verifyAuthCVendorPromotionTrainingEffective from './mutates/verifyAuthCVendorPromotionTrainingEffective.graphql'

export {
  getContractStatus,
  applyForService,
  cancelAllCVendorAuthPromotionTraining,
  createChannelVendor,
  createCoursewareSupplier,
  createParticipatingUnit,
  createTrainingInstitution,
  createTrainingInstitutionPortal,
  disableTrainingInstitution,
  enableTrainingInstitution,
  removeTrainingInstitutionPortal,
  resumeWithChannelVendorSigned,
  resumeWithCoursewareSupplierSigned,
  resumeWithTrainingInstitutionSigned,
  signUpChannelVendor,
  signUpCoursewareSupplier,
  signUpTrainingInstitution,
  suspendWithChannelVendorSigned,
  suspendWithCoursewareSupplierSigned,
  suspendWithTrainingInstitutionSigned,
  updateChannelVendor,
  updateCoursewareSupplier,
  updateParticipatingUnit,
  updateTrainingInstitution,
  updateTrainingInstitutionPortal,
  verifyAuthCVendorPromotionTrainingEffective
}
