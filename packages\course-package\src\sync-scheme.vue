<route-params content="/:id"></route-params>
<route-meta>
{
"title": "同步培训方案页面"
}
</route-meta>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/training/course-package')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/course-package' }">课程包管理</el-breadcrumb-item>
      <el-breadcrumb-item>同步培训方案</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom mb0">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="6">
              <el-form-item label="培训方案：">
                <biz-learning-scheme-select :multiple="true" v-model="commoditySkuIdList"></biz-learning-scheme-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="6">
              <el-form-item label="数据状态">
                <el-select
                  v-model="courserPackageSyncSchemeRequest.dataStatus"
                  clearable
                  placeholder="方案和课程包课程是否一致"
                >
                  <el-option label="一致的" value="1"></el-option>
                  <el-option label="不一致" value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="6">
              <el-form-item label="同步状态">
                <el-select v-model="courserPackageSyncSchemeRequest.syncStatus" clearable placeholder="请选择同步状态">
                  <el-option label="未同步" value="0"></el-option>
                  <el-option label="同步成功" value="1"></el-option>
                  <el-option label="同步中" value="2"></el-option>
                  <el-option label="同步失败" value="3"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary" @click="clickQuery">查询</el-button>
                <el-button @click="reset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <div class="m-tit is-small">
          <span class="tit-txt f-flex-sub">
            同步培训方案（共{{ summaryList.schemeSyncCount }}个方案使用，已同步{{
              summaryList.syncedCount
            }}
            个，未同步{{ summaryList.noSyncedCount }}个，同步中{{ summaryList.syncingCount }}个，同步失败{{
              summaryList.syncFailedCount
            }}个）
          </span>
          <el-button type="primary" :loading="allLoadin" @click="syncAllCoursePackageUsed">同步所有方案</el-button>
          <el-button type="primary" @click="synchronization">批量同步</el-button>
        </div>
        <!--表格-->
        <el-table
          stripe
          :data="courserPackageSyncSchemeVo"
          ref="courserPackageSyncSchemeVo"
          max-height="500px"
          class="m-table"
          @selection-change="selectChange"
          :loading="loading"
          v-table-scroll="slideQuery"
        >
          <el-table-column type="selection" :selectable="selectable" width="55" align="center" fixed="left">
          </el-table-column>

          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="培训方案名称" prop="schemeName" min-width="300" fixed="left">
            <!-- <template></template> -->
          </el-table-column>
          <el-table-column label="数据状态" prop="dataStatus" min-width="140" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.dataStatus == '不一致'">
                <el-tag type="danger">不一致</el-tag>
              </div>
              <div v-if="scope.row.dataStatus == '一致'">
                <el-tag type="success">一致</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="同步状态" prop="syncStatus" min-width="140">
            <template slot-scope="scope">
              <div v-if="scope.row.syncStatus == '未同步'">
                <el-badge is-dot type="info" class="badge-status">未同步</el-badge>
              </div>
              <div v-else-if="scope.row.syncStatus == '同步中'">
                <el-badge is-dot type="primary" class="badge-status">同步中</el-badge>
              </div>
              <div v-else-if="scope.row.syncStatus == '同步失败'">
                <el-badge is-dot type="danger" class="badge-status">同步失败</el-badge>
                <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                  <i class="el-icon-warning m-tooltip-icon f-cr f-ml5"></i>
                  <div slot="content">失败原因：{{ scope.row.errorMessage }}</div>
                </el-tooltip>
              </div>
              <div v-else>
                <el-badge is-dot type="success" class="badge-status">已同步</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="最新同步时间" prop="syncTime" align="center" min-width="180">
            <template slot-scope="scope">
              {{ scope.row.syncTime || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="synchronizationLog(scope.row.id)">同步日志</el-button>
              <el-button type="text" size="mini" @click="viewSchene(scope.row)">查看方案</el-button>
              <el-button type="text" size="mini" :disabled="!selectable(scope.row)" @click="singleSync(scope.row)"
                >同步方案</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <div class="m-btn-bar f-mt10 f-tc is-sticky-1">
        <el-button @click="beforeStep">返回上一步</el-button>
      </div>
      <el-drawer title="日志详情" :visible.sync="syncLogDetailDialog" size="800px" custom-class="m-drawer">
        <div class="drawer-bd f-mt20 f-mlr40">
          <el-timeline>
            <el-timeline-item v-for="(item, index) in logDetailList" :key="index">
              <p class="f-mb10 f-fb f-f15">
                {{ item.logTime }}<span class="f-ml30">{{ item.creator.name }}</span>
              </p>
              <div class="f-c6">
                <div class="f-mt5" v-if="item.status == 1">发起同步，同步结果【成功】</div>
                <div class="f-mt5" v-if="item.status !== 1">
                  发起同步，同步结果【失败】，失败原因：{{ item.message }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-drawer>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import CoursePackageSyncSchemeInfo from '@api/service/management/resource/course-package/query/vo/CoursePackageSyncSchemeInfo'
  import CoursePackageSyncLog from '@api/service/management/resource/course-package/query/vo/CoursePackageSyncLog'
  import { CourserPackageSyncSchemeStatisticResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
  import CourserPackageSyncSchemeVo from '@api/service/management/resource/course-package/mutation/vo/CourserPackageSyncSchemeVo'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import QuerySyncSchemeParam from '@api/service/management/resource/course-package/mutation/vo/QuerySyncSchemeParam'

  @Component
  export default class extends Vue {
    page: UiPage
    loading = false
    totalSize = 0
    id: ''
    summaryList = new CourserPackageSyncSchemeStatisticResponse() //tip信息
    asyncCoursepackageList: Array<CoursePackageSyncSchemeInfo> = new Array<CoursePackageSyncSchemeInfo>()
    selectList = new Array<CourserPackageSyncSchemeVo>()
    syncLogDetailDialog = false //打开日志标识
    logDetailList = new Array<CoursePackageSyncLog>()
    courserPackageSyncSchemeRequest: QuerySyncSchemeParam = new QuerySyncSchemeParam()
    courserPackageSyncSchemeVo: Array<CourserPackageSyncSchemeVo> = new Array<CourserPackageSyncSchemeVo>()
    allLoadin = false //同步所有loading
    // 商品id数组
    commoditySkuIdList = new Array<HasSelectSchemeMode>()
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    async reset() {
      // 111
      this.commoditySkuIdList = new Array<HasSelectSchemeMode>()
      this.courserPackageSyncSchemeRequest = new QuerySyncSchemeParam()
      this.page.pageNo = 1
      await this.doQueryPage()
      await this.getSyncSummary()
    }
    async clickQuery() {
      this.page.pageNo = 1
      this.courserPackageSyncSchemeRequest.schemeIds = this.commoditySkuIdList?.map((item) => item.id)
      await this.doQueryPage()
      await this.getSyncSummary()
    }
    async doQueryPage() {
      this.loading = true
      const id = this.$route.params.id
      const res = await ResourceModule.coursePackageFactory
        .syncCoursePackage(id)
        .querySyncTrainingSchemePage(this.page, this.courserPackageSyncSchemeRequest)
      this.courserPackageSyncSchemeVo = res.data
      ;(this.$refs['courserPackageSyncSchemeVo'] as any)?.doLayout()
      this.loading = false
    }

    async slideQuery() {
      if (this.page.pageNo >= this.page.totalPageSize) {
        return
      }

      this.page.pageNo += 1
      this.loading = true
      const id = this.$route.params.id
      const res = await ResourceModule.coursePackageFactory
        .syncCoursePackage(id)
        .querySyncTrainingSchemePage(this.page, this.courserPackageSyncSchemeRequest)
      this.courserPackageSyncSchemeVo.push(...res.data)
      ;(this.$refs['courserPackageSyncSchemeVo'] as any)?.doLayout()
      this.loading = false
    }

    async getSyncSummary() {
      const id = this.$route.params.id
      const res = await ResourceModule.coursePackageFactory
        .syncCoursePackage(id)
        .queryCourserPackageSyncSchemeStatistic(this.courserPackageSyncSchemeRequest)
      this.summaryList = res.data
    }
    async activated() {
      await this.doQueryPage()
      await this.getSyncSummary()
    }
    /**
     * 判断el-table哪些是禁止选中
     */
    selectable(item: CourserPackageSyncSchemeVo) {
      if (item.syncStatus === '同步成功' || item.syncStatus == '同步中') {
        return false
      } else {
        return true
      }
    }
    /*
     * 选择
     */
    selectChange(selection: any) {
      // const index = this.selectList.indexOf(val[0].schemeId)
      // if (index > -1) {
      //   this.selectList.splice(index, 1)
      // } else {
      //   this.selectList.push(val[0].schemeId)
      // }

      this.selectList = selection
      console.log(this.selectList, 'selectList')
    }
    /**
     * 同步方案
     * */
    async singleSync(item: CourserPackageSyncSchemeVo) {
      const id = this.$route.params.id
      const res = await ResourceModule.coursePackageFactory.syncCoursePackage(id).single(item.coursePackageUseId)
      if (res.code == 200) {
        this.$message.success('同步成功')
        this.page.pageNo = 1
        await this.doQueryPage()
      } else {
        this.$message.error('同步失败')
      }
    }
    /**
     * 批量同步
     * */

    async synchronization() {
      const id = this.$route.params.id
      const idList = [] as string[]
      this.selectList.map((item) => {
        idList.push(item?.coursePackageUseId)
      })
      const res = await ResourceModule.coursePackageFactory.syncCoursePackage(id).batch(idList)
      if (res.isSuccess()) {
        this.$message.success('批量同步成功')
        this.page.pageNo = 1
        await this.doQueryPage()
      } else {
        this.$message.error(res?.errors[0]?.message || '批量同步失败')
      }
    }
    /**
     * 同步所有
     */
    async syncAllCoursePackageUsed() {
      this.allLoadin = true
      const res = await ResourceModule.coursePackageFactory
        .syncAllUsedCoursePackage(this.$route.params.id)
        .syncAllCoursePackageUsed()
      this.allLoadin = false
      if (res.status.isSuccess()) {
        this.$message.success('批量同步成功')
        this.page.pageNo = 1
        await this.doQueryPage()
      } else {
        this.$message.error(res.status?.errors[0]?.message || '批量同步失败')
      }
    }
    /*
     *查看方案
     */

    viewSchene(item: CourserPackageSyncSchemeVo) {
      let url = ''
      url = '/training/scheme/manage'
      this.$router.push({
        path: url,
        query: { schemeName: item.schemeName }
      })
    }
    async synchronizationLog(id: string) {
      try {
        this.logDetailList = await ResourceModule.coursePackageFactory.queryCoursePackage.queryCoursePackageSyncLog(id)
      } catch (e) {
        console.log(e, '同步日志获取失败')
      } finally {
        this.syncLogDetailDialog = true
      }
    }
    beforeStep() {
      this.$router.go(-1)
    }
  }
</script>
