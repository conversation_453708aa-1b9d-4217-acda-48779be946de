schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""培训机构查询渠道商列表
		已废弃 请使用{@link #findChannelVendorPageForTrainingInstitutionNew }
	"""
	findChannelVendorPageForTrainingInstitution(page:Page,name:String):ServicerDtoWithTrainingInstitutionPage @page(for:"ServicerDtoWithTrainingInstitution")
	"""培训机构查询渠道商列表
		@param page
		@param request
		@return
	"""
	findChannelVendorPageForTrainingInstitutionNew(page:Page,request:QueryChannelVendorOfTInstitutionArgs):ServicerDtoWithTrainingInstitutionPage @page(for:"ServicerDtoWithTrainingInstitution")
	"""子项目管理员查询课件供应商列表"""
	findCoursewareSupplierPage(page:Page,trainingInstitutionIdList:[String],name:String,regionPath:String):ServicerDtoWithTrainingInstitutionPage @page(for:"ServicerDtoWithTrainingInstitution")
	"""培训机构查询课件供应商列表
		已废弃,请使用{@link #findCoursewareSupplierPageForTrainingInstitutionNew}
	"""
	findCoursewareSupplierPageForTrainingInstitution(page:Page,name:String,regionPath:String):ServicerDtoWithTrainingInstitutionPage @page(for:"ServicerDtoWithTrainingInstitution")
	"""培训机构查询课件供应商列表"""
	findCoursewareSupplierPageForTrainingInstitutionNew(page:Page,request:QueryCoursewareSupplierOfTInstitutionArgs):ServicerDtoWithTrainingInstitutionPage @page(for:"ServicerDtoWithTrainingInstitution")
	"""培训机构查询参训单位列表"""
	findParticipatingUnitForTrainingInstitution(page:Page,name:String,code:String,regionPath:String,status:ServicerStatusEnums):ServicerDtoWithTrainingInstitutionPage @page(for:"ServicerDtoWithTrainingInstitution")
	"""通过服务商类型查询关联的服务商信息列表
		@param inputIdList 服务商Id列表
		@param inPutType   传入服务商Id列表对应的服务商类型
		@param outPutType  需查询关联的服务商类型
		@return
	"""
	findRelationServiceListByType(inputIdList:[String],inPutType:ServicerTypeEnums,outPutType:ServicerTypeEnums,contractStatus:ServicerContractStatusEnums):[ServicerDto] @NotAuthenticationRequired
	"""通过服务商类型所有服务商信息列表"""
	findServicerListByType(servicerType:ServicerTypeEnums):[ServicerDto] @NotAuthenticationRequired
	"""子项目管理员查询培训机构列表"""
	findTrainingInstitutionPage(page:Page,name:String,code:String,regionPath:String,status:ServicerStatusEnums):TrainingInstitutionInfoDtoPage @page(for:"TrainingInstitutionInfoDto")
	"""渠道商查询培训机构列表"""
	findTrainingInstitutionPageForChannelVendor(page:Page,name:String,code:String,regionPath:String,status:ServicerStatusEnums):TrainingInstitutionInfoDtoPage @page(for:"TrainingInstitutionInfoDto")
	"""课件供应商查询培训机构列表"""
	findTrainingInstitutionPageForCoursewareSupplier(page:Page,name:String,code:String,regionPath:String,status:ServicerStatusEnums):TrainingInstitutionInfoDtoPage @page(for:"TrainingInstitutionInfoDto")
	"""参训单位查询培训机构列表
		v1.11.1
	"""
	findTrainingInstitutionPageForParticipatingUnit(page:Page,name:String,code:String,regionPath:String,status:ServicerStatusEnums):TrainingInstitutionInfoDtoPage @page(for:"TrainingInstitutionInfoDto")
	"""获取当前用户可用的服务商
		@return
	"""
	getCurrentUserServicer:[ServicerDto]
	"""服务商Id列表获取服务商信息"""
	list(idList:[String]):[ServicerDto] @NotAuthenticationRequired
}
type Mutation {
	"""新增专题配置
		@param type        专题类型
		@param commodityId 商品Id
		@param servicerId  服务商Id
		@param sort        排序
		@return
	"""
	createSpecialTopicConfig(type:SpecialTopicTypeEnums,commodityId:String,servicerId:String,sort:Int!):String
	"""培训机构恢复与服务商合作
		@return
	"""
	resumeServicerContract(token:String,contractId:String,contractType:ServicerContractTypeEnums):Void @NotAuthenticationRequired
	"""通过签约类型进行服务商签约
		@return 合约Id
	"""
	signUpServicerContract(token:String,servicerIdA:String,servicerIdB:String,contractContent:String,contractType:ServicerContractTypeEnums):String @NotAuthenticationRequired
	"""培训机构中止与服务商合作
		@return
	"""
	suspendServicerContract(token:String,contractId:String,contractType:ServicerContractTypeEnums):Void @NotAuthenticationRequired
}
input QueryChannelVendorOfTInstitutionArgs @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.servicer.trainingInstitution.QueryChannelVendorOfTInstitutionArgs") {
	"""手机号"""
	phone:String
	"""渠道地区"""
	regionPath:String
	"""名称"""
	name:String
	"""合作签约开始时间"""
	beginTime:DateTime
	"""合作签约结束时间"""
	endTime:DateTime
	"""合作状态"""
	contractStatus:ServicerContractStatusEnums
}
input QueryCoursewareSupplierOfTInstitutionArgs @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.servicer.trainingInstitution.QueryCoursewareSupplierOfTInstitutionArgs") {
	"""手机号"""
	phone:String
	"""渠道地区"""
	regionPath:String
	"""名称"""
	name:String
	"""合作签约开始时间"""
	beginTime:DateTime
	"""合作签约结束时间"""
	endTime:DateTime
	"""合作状态"""
	contractStatus:ServicerContractStatusEnums
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
enum ServicerContractStatusEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerContractStatusEnums") {
	"""全部"""
	ALL
	"""合作"""
	NORMAL
	"""中止"""
	SUSPEND
}
enum ServicerContractTypeEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerContractTypeEnums") {
	"""培训机构与课件供应商合约"""
	TINSTITUTION_CSUPPLIER_CONTRACT
	"""培训机构与渠道商合约"""
	TINSTITUTION_CVENDOR_CONTRACT
}
enum ServicerStatusEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerStatusEnums") {
	"""全部"""
	ALL
	"""正常"""
	NORMAL
	"""失效"""
	SUSPEND
}
enum ServicerTypeEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerTypeEnums") {
	"""全部"""
	ALL
	"""培训机构"""
	TRAINING_INSTITUTION
	"""课件供应商"""
	COURSEWARE_SUPPLIER
	"""渠道商"""
	CHANNEL_VENDOR
	"""参训单位"""
	PARTICIPATING_UNIT
}
"""专题类型"""
enum SpecialTopicTypeEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.specialtopicconfig.enums.SpecialTopicTypeEnums") {
	"""社区矫正专题"""
	SHE_QU_JIAO_ZHENG
}
"""有合约的服务商信息"""
type ServicerContractDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.common.ServicerContractDto") {
	"""服务商 Id"""
	id:String
	"""服务商类型"""
	servicerType:ServicerTypeEnums
	"""名称"""
	name:String
	"""合作状态"""
	status:ServicerContractStatusEnums
}
"""服务商信息"""
type ServicerDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.common.ServicerDto") {
	"""项目ID"""
	projectId:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""子项目ID"""
	subProjectId:String
	"""服务商 Id"""
	id:String
	"""企业账户id"""
	accountId:String
	"""企业单位id"""
	unitId:String
	"""名称"""
	name:String
	"""服务商类型 培训机构：1，课件供应商：2，渠道商：3 ，参训单位：4
		{@link ServicerTypes}
	"""
	servicerType:Int!
	"""地区名称列表，省市区县..."""
	regionNames:[String]
	"""所在地区路径"""
	regionPath:String
	"""联系人"""
	contactPerson:String
	"""联系电话"""
	phone:String
	"""创建时间"""
	createTime:DateTime
	"""服务商简介"""
	abouts:String
	"""服务商状态 正常：1，失效：2"""
	status:Int!
}
"""服务商信息"""
type ServicerDtoWithTrainingInstitution @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.common.ServicerDtoWithTrainingInstitution") {
	"""有合约的服务商信息"""
	servicerContracts:[ServicerContractDto]
	"""项目ID"""
	projectId:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""子项目ID"""
	subProjectId:String
	"""服务商 Id"""
	id:String
	"""企业账户id"""
	accountId:String
	"""企业单位id"""
	unitId:String
	"""名称"""
	name:String
	"""服务商类型 培训机构：1，课件供应商：2，渠道商：3 ，参训单位：4
		{@link ServicerTypes}
	"""
	servicerType:Int!
	"""地区名称列表，省市区县..."""
	regionNames:[String]
	"""所在地区路径"""
	regionPath:String
	"""联系人"""
	contactPerson:String
	"""联系电话"""
	phone:String
	"""创建时间"""
	createTime:DateTime
	"""服务商简介"""
	abouts:String
	"""服务商状态 正常：1，失效：2"""
	status:Int!
}
"""服务商信息"""
type TrainingInstitutionInfoDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.common.TrainingInstitutionInfoDto") {
	"""机构代码"""
	code:String
	"""logo"""
	logo:String
	"""有合约的服务商信息"""
	servicerContracts:[ServicerContractDto]
	"""项目ID"""
	projectId:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""子项目ID"""
	subProjectId:String
	"""服务商 Id"""
	id:String
	"""企业账户id"""
	accountId:String
	"""企业单位id"""
	unitId:String
	"""名称"""
	name:String
	"""服务商类型 培训机构：1，课件供应商：2，渠道商：3 ，参训单位：4
		{@link ServicerTypes}
	"""
	servicerType:Int!
	"""地区名称列表，省市区县..."""
	regionNames:[String]
	"""所在地区路径"""
	regionPath:String
	"""联系人"""
	contactPerson:String
	"""联系电话"""
	phone:String
	"""创建时间"""
	createTime:DateTime
	"""服务商简介"""
	abouts:String
	"""服务商状态 正常：1，失效：2"""
	status:Int!
}

scalar List
type ServicerDtoWithTrainingInstitutionPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ServicerDtoWithTrainingInstitution]}
type TrainingInstitutionInfoDtoPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TrainingInstitutionInfoDto]}
