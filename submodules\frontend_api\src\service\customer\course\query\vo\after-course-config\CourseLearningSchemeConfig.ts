import AfterCourseConfig from '@api/service/customer/course/query/vo/after-course-config/AfterCourseConfig'

class CourseLearningSchemeConfig {
  trainingBeginDate: string
  trainingEndDate: string
  afterCourseConfig: AfterCourseConfig = new AfterCourseConfig()
  chooseCourseLearning: {
    id: string
  }

  autonomousCourseLearning: {
    id: string
  }

  interestCourseLearning: {
    id: string
  }

  from(config: any) {
    this.trainingBeginDate = config.trainingBeginDate
    this.trainingEndDate = config.trainingEndDate
    const courseLearning =
      config.chooseCourseLearning || config.autonomousCourseLearning || config.interestCourseLearning
    this.afterCourseConfig = courseLearning.config.courseQuizConfig
    this.chooseCourseLearning = {
      id: courseLearning.id
    }
    this.autonomousCourseLearning = {
      id: courseLearning.id
    }
    this.autonomousCourseLearning = {
      id: courseLearning.id
    }
  }
}

export default CourseLearningSchemeConfig
