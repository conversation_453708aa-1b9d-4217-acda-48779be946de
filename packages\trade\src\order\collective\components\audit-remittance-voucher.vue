<template>
  <div class="pure">
    <!--审核汇款凭证-->
    <el-drawer
      :title="isAudit ? '审核汇款凭证' : '查看汇款凭证'"
      :visible.sync="show"
      size="600px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd f-tc" v-for="(item, index) in remittanceVoucherInfoList" :key="index">
        <el-image
          class="web-banner f-mt20"
          :src="mfsImage(item.thumbnailImageUrl)"
          :preview-src-list="[mfsImage(item.originImageUrl)]"
        />
        <p class="f-mt5 f-c9">点击图片可查看原图</p>
      </div>
      <div class="m-btn-bar drawer-ft" v-if="isAudit">
        <el-button @click="cancel">取消</el-button>
        <template v-if="$hasPermission('confirmReceipt')" desc="确认收款" actions="confirmReceipt">
          <el-button type="primary" @click="confirmReceipt">确认款项</el-button>
        </template>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, PropSync, Vue, Watch } from 'vue-property-decorator'
  import QueryBatchOrderList from '@api/service/management/trade/batch/order/query/QueryBatchOrderList'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import BatchOrderRemittanceVoucherInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderRemittanceVoucherInfoVo'
  import { bind, debounce } from 'lodash-decorators'
  import DataResolve from '@api/service/common/utils/DataResolve'

  @Component
  export default class extends Vue {
    /**
     * 是否展示
     */
    @PropSync('visible', {
      type: Boolean
    })
    show!: boolean

    /**
     * 批次单付款单号
     */
    @Prop({
      type: String,
      required: true
    })
    paymentOrderNo: string

    /**
     * 接口查询
     */
    queryRemote: QueryBatchOrderList =
      TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchOrderList
    /**
     * 是否审核
     */
    isAudit = true
    /**
     * 批次单id
     */
    batchOrderNo = ''

    /**
     * 汇款凭证信息列表
     */
    remittanceVoucherInfoList: BatchOrderRemittanceVoucherInfoVo[] = []

    get mfsImage() {
      return (url: string) => {
        return DataResolve.getMFSImage(url)
      }
    }

    /**
     * 取消
     */
    cancel() {
      this.show = false
    }

    /**
     * 确认
     */
    @bind
    @debounce(200)
    async confirmReceipt() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        const response =
          await TradeModule.batchTradeBatchFactor.orderFactor.mutationOrderFactor.mutationBatchOrderPay.confirmCollectivePay(
            this.paymentOrderNo
          )
        if (response.isSuccess()) {
          this.$message.success('批次缴费成功，系统将自动以短信的形式通知管理员')
          this.show = false
          this.$emit('reloadData')
        } else {
          this.$message.error(response.getMessage() || '操作失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }
  }
</script>
