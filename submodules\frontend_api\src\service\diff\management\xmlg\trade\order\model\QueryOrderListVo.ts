import { DoubleScopeRequest } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import {
  CommoditySkuRequest1,
  DateScopeRequest,
  OrderBasicDataRequest,
  OrderPayInfoRequest,
  OrderRequest,
  OrderStatusChangeTimeRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import SaleChannel, { SaleChannelEnum } from '@api/service/diff/management/xmlg/trade/enums/SaleChannelType'
import { OrderTransaction } from '@api/service/management/trade/single/order/query/enum/OrderTransactionStatus'
import UserModule from '@api/service/management/user/UserModule'
import QueryStudentList from '@api/service/management/user/query/student/QueryStudentList'
import QueryOrderListSchoolVo from '@api/service/management/trade/single/order/query/vo/QueryOrderListVo'

/**
 * @description 查询订单列表参数
 */
class QueryOrderListVo extends QueryOrderListSchoolVo {
  /**
   * 销售渠道
   */
  saleChannel: SaleChannelEnum = null

  /**
   * 转换成微服务查询对象
   * @param {boolean} isBusinessConsult - 是否是业务咨询相关
   */
  async to(isBusinessConsult: boolean): Promise<OrderRequest> {
    const to = new OrderRequest()
    to.orderNoList = this.orderNo ? [this.orderNo] : undefined

    to.issueId = this.periodId ? [this.periodId] : []

    if (this.saleChannel || this.saleChannel === SaleChannelEnum.self) {
      to.saleChannels = [this.saleChannel]
    } else {
      to.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic,
        SaleChannelEnum.huayi
      ]
    }
    to.saleChannelName = to.saleChannels.includes(SaleChannelEnum.topic) ? this.specialSubjectName : ''
    if (isBusinessConsult) {
      to.buyerIdList = this.buyerId ? [this.buyerId] : undefined
    } else {
      to.buyerIdList = await this.getBuyerIdList()
    }
    to.orderBasicData = new OrderBasicDataRequest()
    /** 订单状态 */
    if (this.orderStatus === OrderTransaction.Wait_Pay) {
      // 等待付款：订单状态正常&支付状态未支付
      to.orderBasicData.orderStatusList = [1]
      to.orderBasicData.orderPaymentStatusList = [0]
    } else if (this.orderStatus === OrderTransaction.Paying) {
      // 支付中：订单状态正常&支付状态支付中
      to.orderBasicData.orderStatusList = [1]
      to.orderBasicData.orderPaymentStatusList = [1]
    } else if (this.orderStatus === OrderTransaction.Opening) {
      // 开通中：订单状态正常&支付状态已支付
      to.orderBasicData.orderStatusList = [1]
      to.orderBasicData.orderPaymentStatusList = [2]
    } else if (this.orderStatus === OrderTransaction.Complete_Transaction) {
      // 交易成功：订单状态交易成功
      to.orderBasicData.orderStatusList = [2]
      to.orderBasicData.orderPaymentStatusList = undefined
    } else if (this.orderStatus === OrderTransaction.Close_Transaction) {
      // 交易关闭：订单状态交易关闭
      to.orderBasicData.orderStatusList = [3]
      to.orderBasicData.orderPaymentStatusList = undefined
    } else {
      // 不处理
      to.orderBasicData.orderStatusList = undefined
      to.orderBasicData.orderPaymentStatusList = undefined
    }
    to.deliveryCommodity = new CommoditySkuRequest1()
    to.deliveryCommodity.commoditySkuIdList = QueryOrderListVo.setListValue(this.commoditySkuIdList)
    to.payInfo = new OrderPayInfoRequest()
    to.payInfo.receiveAccountIdList = QueryOrderListVo.setListValue(this.receiveAccountIdList)
    to.payInfo.flowNoList = this.flowNo ? [this.flowNo] : undefined

    /** 缴费渠道 */
    if (this.terminalCode === 1) {
      // web端
      to.orderBasicData.terminalCodeList = ['Web']
      to.orderBasicData.channelTypesList = [1]
    } else if (this.terminalCode === 2) {
      // h5端
      to.orderBasicData.terminalCodeList = ['H5', 'WechatOfficial']
      to.orderBasicData.channelTypesList = [1]
    } else if (this.terminalCode === 3) {
      // 导入开通
      to.orderBasicData.terminalCodeList = undefined
      to.orderBasicData.channelTypesList = [3]
    } else {
      to.orderBasicData.terminalCodeList = undefined
      if (this.haveCollection) {
        to.orderBasicData.channelTypesList = [1, 2, 3, 5]
      } else {
        to.orderBasicData.channelTypesList = [1, 3, 5]
      }
    }

    to.orderBasicData.orderStatusChangeTime = new OrderStatusChangeTimeRequest()
    // 订单创建时间
    if (QueryOrderListVo.isWeightyArr(this.orderCreateTime)) {
      to.orderBasicData.orderStatusChangeTime.normalDateScope = new DateScopeRequest()
      to.orderBasicData.orderStatusChangeTime.normalDateScope.begin = this.orderCreateTime[0] || ''
      to.orderBasicData.orderStatusChangeTime.normalDateScope.end = this.orderCreateTime[1] || ''
    } else {
      to.orderBasicData.orderStatusChangeTime.normalDateScope = undefined
    }
    // 交易成功时间
    if (QueryOrderListVo.isWeightyArr(this.paymentCompleteTime)) {
      to.orderBasicData.orderStatusChangeTime.completedDatesScope = new DateScopeRequest()
      to.orderBasicData.orderStatusChangeTime.completedDatesScope.begin = this.paymentCompleteTime[0] || ''
      to.orderBasicData.orderStatusChangeTime.completedDatesScope.end = this.paymentCompleteTime[1] || ''
    } else {
      to.orderBasicData.orderStatusChangeTime.completedDatesScope = undefined
    }
    // 是否剔除0元单
    if (this.isRemoveZeroOrder) {
      to.orderBasicData.orderAmountScope = new DoubleScopeRequest()
      to.orderBasicData.orderAmountScope.begin = 0.01
    } else {
      to.orderBasicData.orderAmountScope = undefined
    }

    if (this.distributorId) {
      to.distributorId = this.distributorId
    }
    if (this.promotionPortalId && !this.isDistributionExcludePortal) {
      to.portalId = this.promotionPortalId
    }

    to.isDistributionExcludePortal = this.isDistributionExcludePortal
    return to
  }

  private static setListValue<T>(arr: T[]): T[] | undefined {
    return Array.isArray(arr) && arr.length ? arr : undefined
  }

  static isWeightyArr<T>(arr: T[]): boolean {
    return Array.isArray(arr) && arr.length ? true : false
  }

  /**
   * 获取买家id集合
   */
  async getBuyerIdList(): Promise<string[] | undefined> {
    if (!this.idCard && !this.userName) return undefined
    const queryRemote: QueryStudentList = UserModule.queryUserFactory.queryStudentList
    queryRemote.queryStudentIdParams.idCard = this.idCard || undefined
    queryRemote.queryStudentIdParams.userName = this.userName || undefined
    const result = await queryRemote.queryStudentIdList()
    if (result.status?.isSuccess()) {
      return QueryOrderListVo.setListValue(result.data)
    }
    return undefined
  }
}

export default QueryOrderListVo
