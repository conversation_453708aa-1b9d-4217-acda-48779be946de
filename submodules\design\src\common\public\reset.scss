@charset "utf-8";

/* 设置页面基础样式 */
html,
body {
  width: 100%;
  height: 100%;
}

/* 清除内外边距 */
body,
p,
ul[class],
ol[class] {
  margin: 0;
  padding: 0;
}

/* 设置文字属性 */
body {
  font-family: arial, Microsoft YaHei, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background-color: #fff;
}

/* 斜体扶正 */
i {
  font-style: normal;
}

/* 重置列表风格 */
ul[class],
ol[class] {
  list-style: none;
}

/* 重置超链接格式 */
a {
  text-decoration: none;
  color: #333;
}

/* 重置超链接与按钮指针样式 */
a,
button {
  cursor: pointer;
}

/* 去除表单元素默认外观样式 */
input,
textarea,
select {
  outline-style: none;
  -webkit-appearance: none;
  border-style: none;
  font-size: 100%;
}

/* 清除ff的button点击虚线框 */
button::-moz-focus-inner {
  border: 0;
}

/* 清除焦点虚线框 */
input:focus,
button:focus {
  outline: none;
}

/* 输入框提示文字颜色 */
input::-webkit-input-placeholder {
  color: #999;
}

/* 禁止文本域缩放 */
textarea {
  resize: none;
  font-family: arial, Microsoft YaHei, sans-serif;
}

/* 重置表格元素 */
table {
  border-collapse: collapse;
  border-spacing: 0;
  font-size: inherit;
}
