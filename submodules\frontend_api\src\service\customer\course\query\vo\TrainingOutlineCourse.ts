import { CourseInSchemeResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import Mockjs from 'mockjs'
import CourseTypeEnum from '@api/service/customer/course/query/enum/CourseTypeEnum'
import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'

/**
 * 学习大纲里面的课程
 */
class TrainingOutlineCourse {
  id = ''
  outlineId = ''
  // 名称
  name = ''
  // 学时
  period: number = null
  // 综合评分
  score: number = null
  // 是否必学
  isCompulsory = false
  // 教师信息
  teachers: Array<SimpleUserInfo>
  // 课程包名称
  sourceCoursePackageName = ''
  sourceCoursePackageId = ''
  isAllowAudition = true
  // * 报名人数
  chooseCoursePeopleCount: number = null
  // * 课程类别
  courseTypeName = ''
  // * 课程Logo
  logoUrl = ''
  // 课件供应商Id
  providerId = ''
  // * 课件供应商名称
  providerName = ''
  // 课程被勾选
  isChecked = false
  // 课程大纲下课程分类信息（仅ui使用）
  courseCategoryInfo: string[] = []

  getTeacherNames() {
    return this.teachers ? this.teachers.map((teacher: SimpleUserInfo) => teacher.name).join('、') : ''
  }

  static from(response: CourseInSchemeResponse) {
    const course = new TrainingOutlineCourse()
    course.id = response.course.courseId
    course.period = response.courseOfCourseTrainingOutline.period
    course.score = response.totalAppraise
    course.outlineId = response.courseOfCourseTrainingOutline.outlineId
    course.sourceCoursePackageId = response.range.courseSourceId
    course.chooseCoursePeopleCount = response.chooseCoursePeopleCount || 0
    return course
  }
}

export default TrainingOutlineCourse
