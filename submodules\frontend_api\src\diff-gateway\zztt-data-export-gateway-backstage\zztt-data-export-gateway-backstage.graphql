"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""导出组合商品开通统计列表"""
	exportCombinationCommodityOpenReportFormsInServicer(request:ZZTTTradeReportRequest):Boolean!
	"""商品导出"""
	exportCommoditySkuInServicer(queryRequest:CommoditySkuRequest1,sortRequest:[CommoditySkuSortRequest]):Boolean!
	"""线下发票导出-继续教育
		@return
	"""
	exportOfflineInvoiceInServicerForJxjy(request:OfflineInvoiceExportRequest):Boolean!
	"""线上发票导出-继续教育
		@return
	"""
	exportOnlineInvoiceInServicerForJxjy(request:OnlineInvoiceRequest):Boolean!
	"""导出个人订单
		@param request
		@param sort
		@return
	"""
	exportOrderExcelInDistributor(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出个人订单
		@param request
		@param sort
		@return
	"""
	exportOrderExcelInServicer(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出个人对账
		@param request
		@param sort
		@return
	"""
	exportReconciliationExcelInDistributor(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出个人对账
		@param request
		@param sort
		@return
	"""
	exportReconciliationExcelInServicer(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出个人退货单"""
	exportReturnOrderExcelInDistributor(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""导出个人退货单"""
	exportReturnOrderExcelInServicer(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""导出个人报名退货对账"""
	exportReturnReconciliationExcelInDistributor(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""导出个人报名退货对账"""
	exportReturnReconciliationExcelInServicer(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""功能描述：学员导出
		@return : void
		@Author： wtl
		@Date： 2022/1/18 15:14
	"""
	exportStudentExcelInServicer(request:StudentQueryRequest):Boolean!
}
input ObsFileMetaData @type(value:"com.fjhb.ms.data.export.commons.utils.async.ObsFileMetaData") {
	bizType:String
	owner:String
	sign:String
}
input BigDecimalScopeRequest @type(value:"com.fjhb.ms.query.commons.BigDecimalScopeRequest") {
	begin:BigDecimal
	end:BigDecimal
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
input CommodityAuthInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.CommodityAuthInfoRequest") {
	distributorId:String
	distributionLevel:Int
	superiorDistributorId:String
	supplierId:String
	salesmanId:String
}
input CommoditySkuRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.CommoditySkuRequest") {
	commoditySkuIdList:[String]
	saleTitle:String
	issueInfo:IssueInfo
	skuProperty:SkuPropertyRequest
	externalTrainingPlatform:[String]
	trainingInstitution:[String]
}
input RegionSkuPropertyRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertySearchRequest") {
	regionSearchType:Int
	region:[RegionSkuPropertyRequest]
}
input SkuPropertyRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.SkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	trainingObject:[String]
	positionCategory:[String]
	jobLevel:[String]
	jobCategory:[String]
	grade:[String]
	subject:[String]
	learningPhase:[String]
	discipline:[String]
	trainingChannelIds:[String]
	certificatesType:[String]
	practitionerCategory:[String]
	qualificationCategory:[String]
	trainingForm:[String]
}
input IssueInfo @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.IssueInfo") {
	issueId:String
	issueName:String
	issueNum:String
	trainStartTime:DateTime
	trainEndTime:DateTime
	sourceType:String
	sourceId:String
}
input OrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderBasicDataRequest") {
	orderType:Int
	batchOrderNoList:[String]
	orderStatusList:[Int]
	orderPaymentStatusList:[Int]
	orderDeliveryStatusList:[Int]
	orderStatusChangeTime:OrderStatusChangeTimeRequest
	channelTypesList:[Int]
	excludeChannelTypesList:[Int]
	terminalCodeList:[String]
	orderAmountScope:BigDecimalScopeRequest
}
input OrderFixQueryRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderFixQueryRequest") {
	excludeChannelTypesList:[Int]
	excludeSaleChannels:[Int]
}
input OrderPayInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderPayInfoRequest") {
	receiveAccountIdList:[String]
	flowNoList:[String]
	paymentOrderTypeList:[Int]
}
input OrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderStatusChangeTimeRequest") {
	normalDateScope:DateScopeRequest
	completedDatesScope:DateScopeRequest
}
input SubOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.SubOrderBasicDataRequest") {
	discountType:Int
	discountSourceId:String
	useDiscount:Boolean
	commodityAuthInfo:CommodityAuthInfoRequest
}
input OrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.OrderInfoRequest") {
	orderNoList:[String]
	batchOrderNoList:[String]
	buyerIdList:[String]
	receiveAccountIdList:[String]
	flowNoList:[String]
	channelTypesList:[Int]
	terminalCodeList:[String]
	saleChannel:Int
	saleChannels:[Int]
	saleChannelName:String
	saleChannelIds:[String]
	policyTrainingSchemeIdList:[String]
	declarationUnitCodeList:[String]
}
input ReturnCloseReasonRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnCloseReasonRequest") {
	closeTypeList:[Int]
}
input ReturnOrderApprovalInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderApprovalInfoRequest") {
	approveTime:DateScopeRequest
}
input ReturnOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderBasicDataRequest") {
	returnOrderStatus:[Int]
	returnOrderTypes:[Int]
	applySourceType:String
	applySourceIdList:[String]
	returnCloseReason:ReturnCloseReasonRequest
	returnStatusChangeTime:ReturnOrderStatusChangeTimeRequest
	refundAmountScope:BigDecimalScopeRequest
}
input ReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderStatusChangeTimeRequest") {
	applied:DateScopeRequest
	returnCompleted:DateScopeRequest
}
input SubOrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.SubOrderInfoRequest") {
	subOrderNoList:[String]
	orderInfo:OrderInfoRequest
	discountType:Int
	useDiscount:Boolean
}
"""商品查询条件
	<AUTHOR>
	@date 2022/01/25
"""
input CommoditySkuRequest1 @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.CommoditySkuRequest") {
	"""商品id"""
	commoditySkuIdList:[String]
	"""商品名称（精确匹配）"""
	saleTitleList:[String]
	"""商品名称（模糊查询）"""
	saleTitleMatchLike:String
	"""要从查询结果中剔除的商品ID集合"""
	notShowCommoditySkuIdList:[String]
	"""商品售价"""
	price:Double
	"""商品上下架信息"""
	onShelveRequest:OnShelveRequest
	"""培训方案信息"""
	schemeRequest:SchemeRequest
	"""商品sku属性查询"""
	skuPropertyRequest:SkuPropertyRequest
	"""是否展示资源不可用的商品"""
	isDisabledResourceShow:Boolean
	"""是否展示所有资源
		（该字段会屏蔽可见渠道、商品资源是否可用、商品上下架状态三个条件）
	"""
	isShowAll:Boolean
	"""专题id"""
	trainingChannelIds:[String]
	"""是否存在专题"""
	existTrainingChannel:Boolean
}
"""商品排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input CommoditySkuSortRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.CommoditySkuSortRequest") {
	"""用来排序的字段"""
	sortField:CommoditySkuSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""发票关联订单查询参数
	<AUTHOR>
	@date 2022/3/18
"""
input InvoiceAssociationInfoRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.InvoiceAssociationInfoRequest") {
	"""关联订单类型
		0:订单号
		1:批次单号
		@see AssociationTypes
	"""
	associationType:Int
	"""订单号 | 批次单号"""
	associationIdList:[String]
	"""买家信息"""
	buyerIdList:[String]
	"""企业id"""
	unitBuyerUnitIdList:[String]
	"""收款账号"""
	receiveAccountIdList:[String]
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
}
"""线下发票查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceExportRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.OfflineInvoiceExportRequest") {
	"""发票ID集合"""
	invoiceIdList:[String]
	"""发票基本信息"""
	basicData:OfflineInvoiceBasicDataRequest
	"""发票关联订单查询参数"""
	associationInfo:InvoiceAssociationInfoRequest
	"""发票配送信息"""
	invoiceDeliveryInfo:OfflineInvoiceDeliveryInfoRequest
	jobName:String
	metaData:ObsFileMetaData
}
"""发票查询参数
	<AUTHOR>
	@date 2022/03/23
"""
input OnlineInvoiceRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.OnlineInvoiceRequest") {
	"""发票id集合"""
	invoiceIdList:[String]
	"""发票基础信息查询参数"""
	basicData:OnlineInvoiceBasicDataRequest
	"""发票关联订单查询参数"""
	associationInfoList:[InvoiceAssociationInfoRequest]
	"""蓝票票据查询参数"""
	blueInvoiceItem:OnlineInvoiceItemRequest
	"""红票票据查询参数"""
	redInvoiceItem:OnlineInvoiceItemRequest
	"""销售渠道
		0-自营 1-分销 不传则查全部
	"""
	saleChannel:Int
	jobName:String
	metaData:ObsFileMetaData
}
"""订单查询参数
	<AUTHOR>
	@date 2022/01/26
"""
input OrderRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.OrderRequest") {
	"""订单号集合"""
	orderNoList:[String]
	"""子订单号集合"""
	subOrderNoList:[String]
	"""子订单退货状态
		0: 未退货
		1: 退货申请中
		2: 退货中
		3: 退货成功
		4: 退款中
		5: 退款成功
		@see SubOrderReturnStatus
	"""
	subOrderReturnStatus:[Int]
	"""订单基本信息查询参数"""
	orderBasicData:OrderBasicDataRequest
	"""子订单基本信息查询参数"""
	subOrderBasicData:SubOrderBasicDataRequest
	"""订单支付信息查询"""
	payInfo:OrderPayInfoRequest
	"""买家查询参数"""
	buyerIdList:[String]
	"""发货商品信息"""
	deliveryCommodity:CommoditySkuRequest
	"""现有商品信息"""
	currentCommodity:CommoditySkuRequest
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannel:Int
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""需要排除的销售渠道
		0-自营 1-分销 2专题
	"""
	excludeSaleChannels:[Int]
	"""专题id"""
	saleChannelIds:[String]
	"""专题名称"""
	saleChannelName:String
	"""华医网卡类型id"""
	cardTypeId:String
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""订单属性组合查询"""
	orderFixQuery:OrderFixQueryRequest
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
	"""管理系统平台"""
	externalTrainingPlatform:[String]
	"""单位id集合"""
	unitIds:[String]
	"""期别id"""
	issueId:[String]
	"""培训计划ID，例如补贴性培训平台和补贴管理系统对接"""
	policyTrainingSchemeIdList:[String]
	"""申报单位统一信用代码，精确匹配"""
	declarationUnitCodeList:[String]
	"""结算状态
		@see com.fjhb.ms.trade.query.common.constants.SettlementStatusConstants
	"""
	settlementStatus:Int
	"""结算时间"""
	settlementDate:DateScopeRequest
}
"""订单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input OrderSortRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.OrderSortRequest") {
	"""需要排序的字段"""
	field:OrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""退货单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.ReturnOrderRequest") {
	"""单位id集合"""
	unitIdList:[String]
	"""退货单号"""
	returnOrderNoList:[String]
	"""基本信息"""
	basicData:ReturnOrderBasicDataRequest
	"""审批信息"""
	approvalInfo:ReturnOrderApprovalInfoRequest
	"""退货商品id集合"""
	returnCommoditySkuIdList:[String]
	"""退货商品查询条件"""
	returnCommodity:CommoditySkuRequest
	"""退款商品id集合"""
	refundCommoditySkuIdList:[String]
	"""退款商品查询条件"""
	refundCommodity:CommoditySkuRequest
	"""退货单关联子订单查询参数"""
	subOrderInfo:SubOrderInfoRequest
	"""商品分销授权信息"""
	commodityAuthInfo:CommodityAuthInfoRequest
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""订单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input ReturnSortRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.ReturnSortRequest") {
	"""需要排序的字段"""
	field:ReturnOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""功能描述：学员查询条件
	@Author： wtl
	@Date： 2022年1月26日 10:10:33
"""
input StudentQueryRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.StudentQueryRequest") {
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:StudentUserRequest
	"""用户认证信息"""
	authentication:AuthenticationRequest
	"""排序"""
	sortList:[StudentSortRequest]
}
"""商品查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input ZZTTCommoditySkuRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.ZZTTCommoditySkuRequest") {
	"""商品Sku名称"""
	saleTitle:String
	"""商品sku属性查询"""
	skuProperty:SkuPropertyRequest
	"""学习方案查询参数"""
	scheme:ZZTTSchemeRequest
}
"""方案查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input ZZTTSchemeRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.ZZTTSchemeRequest") {
	"""培训方案ID"""
	schemeIdList:[String]
	"""方案类型
		@see SchemeType
	"""
	schemeType:String
	"""方案学时"""
	schemePeriodScope:DoubleScopeRequest
}
"""商品开通统计报表查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input ZZTTTradeReportRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.ZZTTTradeReportRequest") {
	"""交易时间范围"""
	tradeTime:DateScopeRequest
	"""买家所在地区路径"""
	buyerAreaPath:[String]
	"""商品查询条件"""
	commoditySku:ZZTTCommoditySkuRequest
	"""销售渠道
		0-自营 1-分销 不传则查全部
	"""
	saleChannel:Int
	"""销售渠道
		0-自营 1-分销 2专题 3-华医网 不传则查全部
	"""
	saleChannels:[Int]
	"""排除的销售渠道
		0-自营 1-分销 2-专题 3-华医网
	"""
	excludedSaleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
	"""分销商id"""
	distributorId:String
	"""门户id"""
	portalId:String
	"""查看非推广门户数据 | true 为勾选效果"""
	notDistributionPortal:Boolean
	"""收款账户"""
	receiveAccountIdList:[String]
	"""期别id"""
	issueId:[String]
	"""机构ID集合"""
	institutionIdList:[String]
}
input AccountRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.AccountRequest") {
	"""账户状态 1：正常，2：冻结，3：注销
		@see AccountStatus
	"""
	statusList:[Int]
	"""创建时间范围"""
	createTimeScope:DateScopeRequest
	"""创建人用户id"""
	createdUserId:String
	"""来源类型
		0-内置 1-项目主网站 2-安卓 3-IOS 4-后台导入 5-迁移数据 6-分销平台项目主网站 7-专题 8-华医网
	"""
	sourceTypes:[Int]
}
"""功能描述：账户认证查询条件
	@Author： wtl
	@Date： 2022年1月26日 09:30:12
"""
input AuthenticationRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.AuthenticationRequest") {
	"""帐号"""
	identity:String
}
"""发票开具状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input BillStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.BillStatusChangeTimeRequest") {
	"""未开具"""
	unBill:DateScopeExcelRequest
	"""开票中"""
	billing:DateScopeExcelRequest
	"""开票成功"""
	success:DateScopeExcelRequest
	"""开票失败"""
	failure:DateScopeExcelRequest
}
"""功能描述：学员集体缴费信息
	@Author： wtl
	@Date： 2022年4月21日 08:58:49
"""
input CollectiveRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.CollectiveRequest") {
	"""集体缴费管理员用户id集合"""
	collectiveUserIdList:[String]
}
"""范围查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/5/7 15:34
"""
input DateScopeExcelRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.DateScopeExcelRequest") {
	"""result >= begin"""
	begin:DateTime
	"""result <= end"""
	end:DateTime
}
"""配送地址信息
	<AUTHOR>
	@date 2022/05/07
"""
input DeliveryAddressRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.DeliveryAddressRequest") {
	"""收件人"""
	consignee:String
}
"""配送状态变更时间查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input DeliveryStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.DeliveryStatusChangeTimeRequest") {
	"""未就绪"""
	unReady:DateScopeExcelRequest
	"""已就绪"""
	ready:DateScopeExcelRequest
	"""已配送"""
	shipped:DateScopeExcelRequest
	"""已自取"""
	taken:DateScopeExcelRequest
}
"""快递信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input ExpressRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.ExpressRequest") {
	"""快递单号"""
	expressNo:String
}
"""发票开票状态变更时间记录查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input InvoiceBillStatusChangTimeRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.InvoiceBillStatusChangTimeRequest") {
	"""发票申请开票时间"""
	unBillDateScope:DateScopeExcelRequest
	"""发票开票时间"""
	successDateScope:DateScopeExcelRequest
}
"""发票状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input InvoiceStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.InvoiceStatusChangeTimeRequest") {
	"""正常"""
	normal:DateScopeExcelRequest
	"""作废"""
	invalid:DateScopeExcelRequest
}
"""线下发票基本信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceBasicDataRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.OfflineInvoiceBasicDataRequest") {
	"""发票类型
		1:电子发票 2:纸质发票
		@see InvoiceTypes
	"""
	invoiceTypeList:[Int]
	"""发票种类
		1:普通发票 2:增值税普通发票 3:增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategory:[Int]
	"""发票状态
		1:正常
		2:作废
		@see InvoiceStatus
	"""
	invoiceStatus:Int
	"""发票状态变更时间记录"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeRequest
	"""发票开票状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	billStatusList:[Int]
	"""发票开票状态变更时间记录"""
	billStatusChangTime:InvoiceBillStatusChangTimeRequest
	"""发票是否冻结"""
	freeze:Boolean
	"""发票号集合"""
	invoiceNoList:[String]
	"""商品id集合"""
	commoditySkuIdList:[String]
}
"""线下发票配送信息
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceDeliveryInfoRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.OfflineInvoiceDeliveryInfoRequest") {
	"""配送状态
		0:未就绪 1：已就绪 2：已自取 3：已配送
		@see OfflineDeliveryStatus
	"""
	deliveryStatusList:[Int]
	"""配送状态变更时间记录
		0:未就绪 1：已就绪 2：已自取 3：已配送
		key值 {@link OfflineDeliveryStatus}
	"""
	deliveryStatusChangeTime:DeliveryStatusChangeTimeRequest
	"""配送方式
		0:无 1：自取 2：快递
		@see OfflineShippingMethods
	"""
	shippingMethodList:[Int]
	"""快递信息"""
	express:ExpressRequest
	"""自取信息"""
	takeResult:TakeResultRequest
	"""配送地址信息"""
	deliveryAddress:DeliveryAddressRequest
}
"""发票基础信息查询参数
	<AUTHOR>
	@date 2022/03/23
"""
input OnlineInvoiceBasicDataRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.OnlineInvoiceBasicDataRequest") {
	"""发票类型
		1:电子发票 2:纸质发票
		@see InvoiceTypes
	"""
	invoiceType:Int
	"""发票种类
		1:普通发票 2:增值税普通发票 3:增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategoryList:[Int]
	"""发票状态变更时间
		@see InvoiceStatus
	"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeRequest
	"""发票状态
		1：正常 2：作废
		@see InvoiceStatus
	"""
	invoiceStatusList:[Int]
	"""蓝票票据开具状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	blueInvoiceItemBillStatusList:[Int]
	"""红票票据开具状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	redInvoiceItemBillStatusList:[Int]
	"""发票是否已冲红"""
	flushed:Boolean
	"""发票是否已生成红票票据"""
	redInvoiceItemExist:Boolean
	"""商品id集合"""
	commoditySkuIdList:[String]
	"""发票是否冻结"""
	freeze:Boolean
}
"""发票票据
	<AUTHOR>
	@date 2022/03/18
"""
input OnlineInvoiceItemRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.OnlineInvoiceItemRequest") {
	"""票据开具状态变更时间"""
	billStatusChangeTime:BillStatusChangeTimeRequest
	"""发票号码"""
	billNoList:[String]
}
"""功能描述 : 学员排序参数
	@date : 2022/4/1 17:15
"""
input StudentSortRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.StudentSortRequest") {
	"""学员排序字段"""
	studentSortField:StudentSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
input StudentUserRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.StudentUserRequest") {
	"""工作单位名称（模糊）"""
	companyName:String
	"""用户所属地区路径集合（模糊，右like）"""
	regionPathList:[String]
	"""集体缴费信息"""
	collective:CollectiveRequest
	"""单位所属地区路径集合（模糊，右like）"""
	companyRegionPathList:[String]
	"""单位所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	companyRegionPathListMatchType:Int
	"""是否工勤人员  (0:非工勤人员  1:工勤人员)"""
	isWorker:String
	"""是否退休   (0:非退休人员 1:退休人员)"""
	isRetire:String
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
}
"""取件信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input TakeResultRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.TakeResultRequest") {
	"""领取人"""
	takePerson:String
}
"""商品上下架相关查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input OnShelveRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.onshelve.OnShelveRequest") {
	"""商品上下架状态
		<br> 0:已下架 1：已上架
	"""
	onShelveStatus:Int
}
"""培训方案相关查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input SchemeRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.scheme.SchemeRequest") {
	"""培训方案ID"""
	schemeIdList:[String]
	"""培训方案类型
		<br> chooseCourseLearning:选课规则 autonomousCourseLearning:自主学习
	"""
	schemeType:String
	"""培训方案名称(模糊查询)"""
	schemeName:String
	"""培训开始时间"""
	trainingBeginDate:DateScopeRequest
	"""培训结束时间"""
	trainingEndDate:DateScopeRequest
}
"""商品可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum CommoditySkuSortField @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.CommoditySkuSortField") {
	"""上架时间"""
	ON_SHELVE_TIME
	"""最新编辑时间"""
	LAST_EDIT_TIME
	"""商品销售数"""
	SALE_TOTAL_NUMBER
	"""商品sku属性-年度"""
	SKU_PROPERTY_YEAR
	"""专题排序"""
	TRAINING_CHANNEL
}
"""订单可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum OrderSortField @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.OrderSortField") {
	"""订单创建时间"""
	ORDER_NORMAL_TIME
	"""订单交易完成时间"""
	ORDER_COMPLETED_TIME
}
"""退货单可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum ReturnOrderSortField @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.ReturnOrderSortField") {
	"""退货单申请时间"""
	APPLIED_TIME
}
"""排序参数
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum SortPolicy @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.SortPolicy") {
	"""正序"""
	ASC
	"""倒序"""
	DESC
}
"""功能描述：排序类型
	@Author： wtl
	@Date： 2021/12/27 10:30
"""
enum SortTypeEnum @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.SortTypeEnum") {
	"""升序"""
	ASC
	"""降序"""
	DESC
}
"""功能描述：学员排序字段
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
enum StudentSortFieldEnum @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.nests.StudentSortFieldEnum") {
	"""创建时间"""
	createdTime
}

scalar List
