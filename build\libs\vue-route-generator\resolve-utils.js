/**
 * 该工具类是用于自动解析api的Module中@Action方法中有调用的graphql方法解析生成对应的graphql调用结果,会自动解析@Action方法的内部调用情况不用在方法上标记@Secure了
 */
const fs = require('fs')
const parser = require('@babel/parser')
const traverse = require('@babel/traverse').default
const tsConfigPaths = require('tsconfig-paths')
const path = require('path')
const graphqlUtils = require('graphql')
const tsConfigLoader = tsConfigPaths.loadConfig()
const matchPath = tsConfigPaths.createMatchPath(tsConfigLoader.absoluteBaseUrl, tsConfigLoader.paths, null, true)
const parseContext = {
  //Module文件解析结果环境,key为Module文件的绝对路径,value为解析结果对象{
  //     filePath: moduleFilePath,
  //     //action方法对应的graphql的调用方法
  //     actionSecures: {actionMethodName:[graphql的schema.operation.method]}
  //   }
  moduleFileParseResults: {},
  //graphqlImporter的ts文件解析,key为graphql-importer ts文件的绝对路径,value为解析结果对象{filePath:'graphql-importer ts的绝对路径',importDeclarations:{import的变量名:import from后面的path}}
  graphqlImporterTsParseResults: {},
  //graphql文件解析,这是对自定义的graphql文件进行解析，key为graphql文件的绝对路径,value为解析结果对象{filePath:'graphql文件的绝对路径',methods:[调用的graphql方法名]}
  graphqlFileParseResults: {},
  //graphql schema生成的gateway的ts文件解析结果,key为graphql的gateway ts文件的绝对路径,value为解析结果对象{filePath:'graphql gateway ts文件的绝对路径',methods:{方法名:'query'|'mutation'}}
  graphqlGatewayFileParseResults: {},
  tsASTMap: {}
}
const waitingSecureMap = {}
const parserOptions = {
  sourceType: 'module',
  plugins: [
    'typescript',
    [
      'decorators',
      {
        decoratorsBeforeExport: true
      }
    ],
    'classProperties'
  ]
}
const defaultBasePath = path.resolve(process.cwd(), `${process.env.API}`)

/**
 * 根据basePath当前路径解析一个import from 的path路径为绝对路径
 * 其会使用tsconfig.json中的映射支持@api/,@/等import的path解析
 */
function resolveImportPath(importPath, basePath) {
  if (fs.existsSync(importPath)) {
    return importPath
  }
  const oldImportPath = importPath
  const extname = path.extname(importPath)
  if (extname === '') {
    importPath = matchPath(importPath + '.ts')
    if (!importPath) {
      importPath = path.resolve(basePath || defaultBasePath, oldImportPath + '.ts')
    }
    if (!fs.existsSync(importPath)) {//如果不存在的可能是目录下的index.ts
      importPath = matchPath(oldImportPath + '/index.ts')
      if (!importPath) {
        importPath = path.resolve(basePath || defaultBasePath, oldImportPath + '/index.ts')
      }
    }
  } else {
    importPath = matchPath(importPath)
    if (!importPath) {
      importPath = path.resolve(basePath || defaultBasePath, oldImportPath)
    }
  }
  if (importPath) {
    importPath = importPath.replace(/\\/g, '/')
  }
  if (!fs.existsSync(importPath)) importPath = undefined
  return importPath
}

/**
 * 判断import from的path是否是一个Module的ts文件
 */
function isModuleFile(importPath, basePath) {
  importPath = resolveImportPath(importPath, basePath)
  let isModule = false
  if (importPath) {
    const result = parseContext.moduleFileParseResults[importPath]
    if (result) {
      isModule = true
    } else {
      if (importPath.endsWith('.ts')) {
        const ast = getTsAST(importPath)
        traverse(ast, {
          ClassDeclaration(classDeclaration) {
            isModule = isModule || (classDeclaration.node.decorators && !!classDeclaration.node.decorators.find(
              decorator => decorator.expression.callee.name === 'Module'))
          }
        })
      }
    }
  }
  return isModule
}

/**
 * 判断import from的path是否是一个Graphql的Gateway的ts文件
 */
function isGateway(importPath, basePath) {
  if (importPath.startsWith('@api/gateway/')) return true
  const abstractImportPath = resolveImportPath(importPath, basePath)
  return abstractImportPath && abstractImportPath.indexOf('/src/gateway/') !== -1
}

const defaultParseModuleOptions = {
  filterClassDeclaration: function(classDeclaration) {
    return classDeclaration.node.decorators && !!classDeclaration.node.decorators.find(decorator => decorator.expression.callee.name === 'Module')
  }
}

function getTsAST(abstractFilePath) {
  let ast = parseContext[abstractFilePath]
  if (!ast) {
    const code = fs.readFileSync(abstractFilePath, { encoding: 'utf-8' }).toString()
    ast = parser.parse(code, parserOptions)
    parseContext.tsASTMap[abstractFilePath] = ast
  }
  return ast
}

/**
 * 解析import from后path对应的Module的ts文件，返回解析结果对象{filePath: 'ts文件的绝对路径', actionSecures: {'action方法名':{'graphql的schemaName':['(query|mutation).调用的graphql方法']}}
 */
function parseModuleFile(importModulePath, basePath, options) {
  const abstractImportModulePath = resolveImportPath(importModulePath, basePath)
  let result = parseContext.moduleFileParseResults[abstractImportModulePath]
  if (!result) {
    //Module文件解析结果
    result = {
      filePath: abstractImportModulePath,
      className: '',
      superClass: null,
      //action方法对应的graphql的调用方法
      actionMethods: {},
      $checkOk: function() {
        let isOk = true
        Object.values(this.actionMethods).forEach(actionMethod => {
          isOk = isOk && actionMethod.$isOk
        })
        this.$isOk = isOk
      }
    }
    const importDeclarationMap = {}
    Object.setPrototypeOf(result, { $isOk: false, $importDeclarationMap: importDeclarationMap })
    const ast = getTsAST(abstractImportModulePath)
    parseContext.moduleFileParseResults[abstractImportModulePath] = result
    if (!options) options = defaultParseModuleOptions
    traverse(ast, {
      ImportDeclaration(importDeclaration) {
        let value = importDeclaration.node.source.value
        for (const specifier of importDeclaration.node.specifiers) {
          const name = specifier.local.name
          importDeclarationMap[name] = {
            path: value,
            isModule: false,
            isGraphqlGateway: false,
            schemaName: '',
            isTs: path.extname(value) === ''
          }
          if (specifier.type === 'ImportDefaultSpecifier') {
            if (isGateway(value, path.dirname(abstractImportModulePath))) {
              importDeclarationMap[name].isGraphqlGateway = true
              importDeclarationMap[name].schemaName = path.basename(value)
            } else if (isModuleFile(value, path.dirname(abstractImportModulePath))) {
              importDeclarationMap[name].isModule = true
            }
          }
        }
      },
      ClassDeclaration(classDeclaration) {
        //必须是@Module的class定义
        if (options.filterClassDeclaration(classDeclaration)) {
          result.className = classDeclaration.node.id.name
          const superClass = classDeclaration.node.superClass
          if (superClass) {
            result.superClass = { name: superClass.name, path: importDeclarationMap[superClass.name].path }
          }
          classDeclaration.traverse({
            ClassMethod(classMethod) {
              //只管@Action的方法
              if (classMethod.node.decorators && !!classMethod.node.decorators.find(decorator => decorator.expression.name === 'Action')) {
                const actionMethod = parseActionMethod(result.filePath, classMethod, result)
                result.actionMethods[classMethod.node.key.name] = actionMethod
              }
            }
          })
        }
      }
    })
    result.$checkOk()
  }
  return result
}

//解析@Action方法中所有调用到Graphql的方法生成{'graphql的schemaName':[调用的graphql方法]}
function parseActionMethod(importModulePath, actionMethod, moduleFileParseResult) {
  const result = {
    moduleFilePath: importModulePath,
    methodName: actionMethod.node.key.name,
    roles: [],
    graphqlMethods: []
  }
  actionMethod.node.decorators.forEach(decorator => {
    if (decorator.expression.callee && decorator.expression.callee.name === 'Role') {
      decorator.expression.arguments && decorator.expression.arguments[0].elements.forEach(role => {
        if (result.roles.indexOf(role.property.name) === -1) {
          result.roles.push(role.property.name)
        }
      })
    }
  })
  Object.setPrototypeOf(result, {
    $isOk: true,
    $waiting: {},
    $actionPath: importModulePath + '@' + actionMethod.node.key.name
  })
  actionMethod.traverse({
    CallExpression(callExpression) {
      //@Action方法中所有的方法的调用
      if (callExpression.node.callee && callExpression.node.callee.object) {
        const callee = callExpression.node.callee
        const callObject = callee.object
        const callMethodName = callee.property.name
        const callObjectName = callObject.name
        const importDeclaration = moduleFileParseResult.$importDeclarationMap[callObjectName]
        if (callObject.type === 'Super' && moduleFileParseResult.superClass) {
          parseInvokeModuleMethodSecures(
            moduleFileParseResult,
            result,
            moduleFileParseResult.superClass.path,
            path.dirname(importModulePath),
            callMethodName,
            {
              filterClassDeclaration: function(classDeclaration) {
                return classDeclaration.node.id.name === moduleFileParseResult.superClass.name
              }
            }
          )
        } else if (importDeclaration && importDeclaration.isGraphqlGateway) {//调用graphql gateway方法的
          const graphqlGatewayParseResult = parseGraphqlGatewayTs(moduleFileParseResult.$importDeclarationMap[callObjectName].path,path.dirname(importModulePath))
          if (callMethodName === '_commonQuery') {//如果是调用_commonQuery的方法说明是自定义graphql调用那么要解析引入的graphql文件获取到底调用了哪些方法
            const expression = callExpression.node.arguments[0]
            switch (expression.type) {
              case 'MemberExpression': {
                //声明在import中引入的graphql-importer的变量名
                const graphqlImporterVariableName = expression.object.name
                //声明在graphql-importer的ts的import的变量名
                const importVariableNameInGraphqlImporter = expression.property.name
                let importGraphqlTsPath = moduleFileParseResult.$importDeclarationMap[graphqlImporterVariableName].path
                //解析graphql-importer ts文件
                const graphqlImportParseResult = parseImportGraphqlTs(
                  importGraphqlTsPath,
                  path.dirname(importModulePath)
                )
                //从解析到的graphql-importer ts文件的结果中获取该调用变量名对应的graphql文件
                const graphqlFilePath = graphqlImportParseResult.importDeclarations[importVariableNameInGraphqlImporter]
                //解析该graphql文件得到其调用的对应graphql方法
                const graphqlParseResult = parseGraphqlFile(
                  graphqlFilePath,
                  path.dirname(graphqlImportParseResult.filePath)
                )
                //将所有调用的graphql方法到对应的graphql gateway中找到
                graphqlParseResult.methods.forEach(method => {
                  result.graphqlMethods.push(importDeclaration.schemaName + '.' + graphqlGatewayParseResult.methods[method] + '.' + method)
                })
                break
              }
              case 'Identifier': {//如果是声明的变量必须是import的graphql文件
                const variableImportDeclaration = moduleFileParseResult.$importDeclarationMap[expression.name]
                if (variableImportDeclaration) {
                  let graphqlFilePath = variableImportDeclaration.path
                  if (variableImportDeclaration.isTs) {
                    //解析graphql-importer ts文件
                    const graphqlImportParseResult = parseImportGraphqlTs(
                      variableImportDeclaration.path,
                      path.dirname(importModulePath)
                    )
                    graphqlFilePath = graphqlImportParseResult.importDeclarations[expression.name]
                  }
                  const abstractGraphqlPath = resolveImportPath(graphqlFilePath, path.dirname(importModulePath))
                  if (!abstractGraphqlPath) {
                    const msg = '在' + result.moduleFilePath + '的第' + callee.loc.start.line + '行的' +
                      result.methodName + '方法中调用' + callObjectName + '._commonQuery()时使用变量' +
                      expression.name + '在该ts中import的path:' + graphqlFilePath + '无法被找到'
                    throw new Error(msg)
                  }
                  //解析该graphql文件得到其调用的对应graphql方法
                  const graphqlParseResult = parseGraphqlFile(abstractGraphqlPath)
                  //将所有调用的graphql方法到对应的graphql gateway中找到
                  graphqlParseResult.methods.forEach(method => {
                    result.graphqlMethods.push(importDeclaration.schemaName + '.' + graphqlGatewayParseResult.methods[method] + '.' + method)
                  })
                } else {
                  const msg = '在' + result.moduleFilePath + '的第' + callee.loc.start.line + '行的' + result.methodName + '方法中调用' + callObjectName + '._commonQuery()时使用变量' + expression.name + '必须在该ts中import'
                  throw new Error(msg)
                }
                break
              }
            }
          } else {
            const graphqlMethod = importDeclaration.schemaName + '.' + graphqlGatewayParseResult.methods[callMethodName] + '.' + callMethodName
            if (result.graphqlMethods.indexOf(graphqlMethod) === -1) {
              result.graphqlMethods.push(graphqlMethod)
            }
          }
        } else if ((importDeclaration && importDeclaration.isModule)) {//如果不是graphql gateway的调用那要看下是否是调用了其它的Module方法或者调用super方法
          //解析该模块,获取当前调用的action方法的secures加入到该action方法中
          parseInvokeModuleMethodSecures(
            moduleFileParseResult,
            result,
            importDeclaration.path,
            path.dirname(importModulePath),
            callMethodName
          )
        }
      }
    }
  })
  if (result.$isOk) {
    notifyFinishWaitingSecures(result.$actionPath, result.graphqlMethods)
  }
  return result
}

function parseInvokeModuleMethodSecures(
  moduleFileParseResult,
  currentActionMethodResult,
  callModulePath,
  basePath,
  callMethodName,
  options
) {
  //解析该模块,获取当前调用的action方法的secures加入到该action方法中
  const callModuleResult = parseModuleFile(callModulePath, basePath, options)
  const callActionMethodResult = callModuleResult.actionMethods[callMethodName]
  //如果调用的action方法的
  if (callActionMethodResult && callActionMethodResult.$isOk) {
    //如果该模块能够完全解析完被调用的action方法
    mergeActionSecures(callActionMethodResult.graphqlMethods, currentActionMethodResult.graphqlMethods)
  } else if (!callModuleResult.$isOk) {//如果不能完成解析完被调用的action方法说明有回环模块调用
    Object.getPrototypeOf(currentActionMethodResult).$isOk = false
    //将其放入等待队列中以便后续对应模块的action方法被解析完成后触发当前actionSecures解析
    const waitingActionPath = callModuleResult.filePath + '@' + callMethodName
    let waitingSecures = waitingSecureMap[waitingActionPath]
    if (!waitingSecures) {
      waitingSecures = []
      waitingSecureMap[waitingActionPath] = waitingSecures
    }
    waitingSecures.push({ result: moduleFileParseResult, actionMethodResult: currentActionMethodResult })
    currentActionMethodResult.$waiting[waitingActionPath] = waitingActionPath
  }
}

//通知完成等待该action方法的所有ActionSecures进行合并
function notifyFinishWaitingSecures(actionPath, actionSecures) {
  const waitingSecures = waitingSecureMap[actionPath]
  if (waitingSecures) {
    const temp = [].concat(waitingSecures)
    temp.forEach((waitingSecure, index) => {
      mergeActionSecures(actionSecures, waitingSecure.actionMethodResult.graphqlMethods)
      delete waitingSecure.actionMethodResult.$waiting[actionPath]
      //如果该等待合并的Secure都没有其它等待处理的action方法了，可以从等待队列中移除
      if (Object.keys(waitingSecure.actionMethodResult.$waiting).length === 0) {
        Object.getPrototypeOf(waitingSecure.actionMethodResult).$isOk = true
        waitingSecures.splice(index, 1)
        waitingSecure.result.$checkOk()
        notifyFinishWaitingSecures(
          waitingSecure.actionMethodResult.$actionPath,
          waitingSecure.actionMethodResult.graphqlMethods
        )
      }
    })
  }
}

function mergeActionSecures(srcGraphqlMethods, targetGraphqlMethods) {
  srcGraphqlMethods.forEach(value => {
    if (targetGraphqlMethods.indexOf(value) === -1) {
      targetGraphqlMethods.push(value)
    }
  })
}

//解析引入的graphql-importer的ts文件
function parseImportGraphqlTs(importGraphqlTsPath, basePath) {
  importGraphqlTsPath = resolveImportPath(importGraphqlTsPath, basePath)
  let result = parseContext.graphqlImporterTsParseResults[importGraphqlTsPath]
  if (!result) {
    const code = fs.readFileSync(importGraphqlTsPath, { encoding: 'utf-8' }).toString()
    const ast = parser.parse(code, parserOptions)
    result = { filePath: importGraphqlTsPath, importDeclarations: {} }
    traverse(ast, {
      ImportDeclaration(path) {
        let value = path.node.source.value
        for (const specifier of path.node.specifiers) {
          result.importDeclarations[specifier.local.name] = value
        }
      }
    })
    parseContext.graphqlImporterTsParseResults[importGraphqlTsPath] = result
  }
  return result
}

//解析.graphql文件,返回结果对象{filePath:'.graphql文件的绝对路径',methods:[调用的graphql方法名]}
function parseGraphqlFile(importGraphqlPath, basePath) {
  const abstractGraphqlPath = resolveImportPath(importGraphqlPath, basePath)
  let result = parseContext.graphqlFileParseResults[abstractGraphqlPath]
  if (!result) {
    result = { filePath: abstractGraphqlPath, methods: [] }
    const language = fs.readFileSync(abstractGraphqlPath, { encoding: 'utf-8' }).toString()
    const document = graphqlUtils.parse(language)
    for (const definition of document.definitions) {
      if (definition.variableDefinitions.length === 0) {//如果没有使用变量的说明自己就已经是graphql调用的方法了
        result.methods.push(definition.name.value)
      } else {//否则的话真正调用的graphql方法在他的selectionSet中
        for (const selection of definition.selectionSet.selections) {
          result.methods.push(selection.name.value)
        }
      }
    }
    parseContext.graphqlFileParseResults[abstractGraphqlPath] = result
  }
  return result
}

//graphql gateway ts文件,返回的结果对象{filePath:'graphql gateway ts文件绝对路径',methods:{'graphql方法名':'query|mutation'}}
function parseGraphqlGatewayTs(importGatewayPath, basePath) {
  const abstractImportGatewayPath = resolveImportPath(importGatewayPath, basePath)
  let result = parseContext.graphqlGatewayFileParseResults[abstractImportGatewayPath]
  if (!result) {
    result = { filePath: abstractImportGatewayPath, methods: {} }
    const code = fs.readFileSync(abstractImportGatewayPath, { encoding: 'utf-8' }).toString()
    const ast = parser.parse(code, parserOptions)
    traverse(ast, {
      ClassDeclaration(classDeclaration) {
        if (classDeclaration.node.id.name === 'DataGateway') {
          classDeclaration.traverse({
            ClassMethod(classMethod) {
              const methodName = classMethod.node.key.name
              if (methodName !== '_commonQuery') {
                //方法倒数第二个参数名决定了该方法是mutation还是query
                const params = classMethod.node.params
                let operationParam = params[params.length - 2]
                if (operationParam.left.name === 'mutate') {
                  result.methods[methodName] = 'mutation'
                } else {
                  result.methods[methodName] = 'query'
                }
              }
            }
          })
        }
      }
    })
    parseContext.graphqlGatewayFileParseResults[abstractImportGatewayPath] = result
  }
  return result
}

module.exports = {
  resolveImportPath,
  isModuleFile,
  parseModuleFile,
  parseImportGraphqlTs,
  parseGraphqlFile,
  parseGraphqlGatewayTs,
  isGateway
}