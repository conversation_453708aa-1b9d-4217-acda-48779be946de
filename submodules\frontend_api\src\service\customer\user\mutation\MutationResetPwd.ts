import { ResponseStatus } from '@hbfe/common'
import ResetPasswordVo from './vo/password/ResetPasswordVo'
import basicdataDomain, { CurrentAccountChangePasswordRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'

/**
 * 修改密码
 */
class MutationResetPwd {
  resetPasswordParams = new ResetPasswordVo()

  async doResetPwd(): Promise<ResponseStatus> {
    const { status } = await basicdataDomain.changePasswordByCurrent(this.resetPasswordParams)
    return status
  }
  /**
   * 修改当前登录账号密码(有返回值信息)
   */
  async doResetPwdNew() {
    const { status } = await basicdataDomain.changePasswordByCurrentWithResponse(this.resetPasswordParams)
    return status
  }
}
export default MutationResetPwd
