schema {
	query:Query
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""检查课程池被引用的情况"""
	checkCoursePoolReference(poolId:String):[CoursePoolReferenceDTO]
	"""课件名称是否存在"""
	courseWareNameExists(name:String,id:String):Boolean!
	"""获取课程信息  --课程服务
		@param courseId 课程编号
		@return 课程信息
	"""
	getCourse(courseId:String):CourseDTO @NotAuthenticationRequired
	"""查询指定考纲下课程数量
		@param chapterIdList 考纲编号列表
		@return 章节对应的课程数量
	"""
	getCourseCountBySyllabus(chapterIdList:[String]):[SyllabusCourseCountDTO]
	"""获取课程被学习的人次 - 来源中间表(已实现)
		@param courseId
		@return
	"""
	getCourseLearningCount(courseId:String!):Double! @NotAuthenticationRequired
	"""查询课程池dto
		@param poolId
		@return
	"""
	getCoursePoolDTO(poolId:String):CoursePoolDTO
	"""获取课件
		@param id
		@return
	"""
	getCourseWare(id:String):CoursewareDTO
	"""课件分页
		@param page
		@param query
		@param authorizedQuery
		@return
	"""
	getCourseWarePage(page:Page,query:CourseWareQueryDTO,authorizedQuery:ResAuthorizedQuery):CoursewareDTOPage @page(for:"CoursewareDTO")
	"""获取课件提供商
		@return
	"""
	getCourseWareSupplier:[SupplierInfoDTO]
	"""查询讲师详情"""
	getTeacherById(id:String):TeacherInfoDTO @NotAuthenticationRequired
	"""获取最后一次学习的课程 - 来源中间表(已实现)
		@param paramDTO
		@return UserLastLearningCourseDTO || null:未选课的时候
	"""
	getUserLastCourseLearning(paramDTO:UserLastLearningCourseParamDTO):UserLastLearningCourseDTO
	"""批量获取指定课程信息  -课程服务
		@param courseIdList 课程编号列表
		@return 课程信息列表
	"""
	listCourse(courseIdList:[String]):[CourseDTO] @NotAuthenticationRequired
	"""批量获取指定课程的课程分类
		@param courseIdList
		@return
	"""
	listCourseCategory(courseIdList:[String]):[CourseCategoryList]
	"""根据课程包id查询课程包内课程集合"""
	listCourseInPool(poolId:String):[CourseInPoolDetailDTO]
	"""获取课程目录信息  -课程服务
		@param courseId 课程编号
		@return 课程目录及包含课件列表
	"""
	listCourseOutline(courseId:String):CourseOutlineDTO @NotAuthenticationRequired
	"""批量获取课程包
		@param ids
		@return
	"""
	listCoursePoolInfoByIds(ids:[String]):[CoursePoolInfoDTO]
	"""根据课件id集合批量获取课件信息
		@param ids
		@return
	"""
	listCourseWareListByIds(ids:[String]):[CourseWareDTO]
	"""查询正常状态的（解析成功的，未被删除的）精品课程"""
	listExcellentCourse:[ExcellentCourseListDTO]
	"""根据教师id集合 批量获取讲师信息"""
	listTeachersByIds(teacherIds:[String]!):[TeacherInfoDTO] @NotAuthenticationRequired
	"""获取用户方案内的已选课程-课程服务
		@param paramDTO
		@return
	"""
	listUserCourse(paramDTO:UserCourseParamDTO):[UserCourseDTO]
	"""获取用户指定课程的学习进度 -课程服务
		@param paramDTO
		@return
	"""
	listUserCourseLearningSchedule(paramDTO:UserCourseLearningScheduleParamDTO):[UserCourseLearningScheduleDTO]
	"""根据方案id和课程学习方式id获取所有可选课程包
		@param paramDTO
		@return
	"""
	listUserCoursePool(paramDTO:UserCourseParamDTO):[CoursePoolBaseInfoDTO]
	"""获取指定课程下的所有课件进度
		@param paramDTO
		@return
	"""
	listUserCoursewareLearningSchedule(paramDTO:UserCourseLearningScheduleParamDTO):[UserCoursewareLearningScheduleDTO]
	"""获取用户方案内未选择的课程 -课程服务
		@param paramDTO
		@return
	"""
	listUserUnSelectCourse(page:Page,paramDTO:UserCourseParamDTO):UserCourseDTOPage @page(for:"UserCourseDTO")
	"""分页查询课程
		@param page
		@param queryParam
		@return
	"""
	pageCourse(page:Page,queryParam:CoursePageParamDTO):CourseDTOPage @page(for:"CourseDTO")
	"""分页查询课程池
		@param page
		@param queryPram
		@return
	"""
	pageCoursePool(page:Page,queryPram:CoursePoolPageParamDTO):CoursePoolInfoDTOPage @page(for:"CoursePoolInfoDTO")
	"""查询讲师分页"""
	pageTeachers(page:Page,query:TeacherQueryDTO):TeacherInfoDTOPage @page(for:"TeacherInfoDTO") @NotAuthenticationRequired
	"""获取用户课程学习记录
		@param page
		@param param
		@return
	"""
	pageUserCourseLearning(page:Page,param:UserCourseRecordParam):UserCourseLearningRecordResponsePage @page(for:"UserCourseLearningRecordResponse")
	"""课程学习统计
		@param param
		@return
	"""
	statisticCourseLearning(page:Page,param:CourseLearningStatisticParam):CourseLearningStatisticDTOPage @page(for:"CourseLearningStatisticDTO") @NotAuthenticationRequired
	"""统计用户学习方案内的课程学习情况  - 来源中间表(已实现)
		@param paramDTO
		@return
	"""
	statisticUserLearningCourseLearningInfo(paramDTO:UserSchemeCourseLearningStatisticParamDTO):UserSchemeCourseLearningStatisticDTO
}
"""课程学习统计参数
	@author: eleven
	@date: 2020/4/13
"""
input CourseLearningStatisticParam @type(value:"com.fjhb.btpx.integrative.service.course.dto.param.CourseLearningStatisticParam") {
	"""学习方案ID"""
	schemeId:String
	"""学习方式id"""
	learningId:String
	"""课程id集合"""
	courseIdList:[String]
}
"""课程分页查询参数对象"""
input CoursePageParamDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.param.CoursePageParamDTO") {
	"""课程名称"""
	name:String
	"""课程考纲"""
	tagIds:[String]
	"""课程状态
		-1不查
		0表示解析中，1表示解析成功，2表示解析失败
	"""
	status:Int!
	"""创建时间查询的起始时间"""
	startCreateTime:DateTime
	"""创建时间查询的截止时间"""
	endCreateTime:DateTime
	"""是否启用， -1表示不查询，0表示不启用，1表示启用"""
	isEnabled:Int!
	"""分类编号"""
	categoryId:String
	"""分类编号集合(当categoryId有值时，categoryIdList设置无效)"""
	categoryIdList:[String]
}
input CoursePoolPageParamDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.param.CoursePoolPageParamDTO") {
	"""课程池名称"""
	poolName:String
	"""筛选课程池编号列表"""
	poolIdList:[String]
}
"""用户课程学习进度查询
	@author: eleven
	@date: 2020/3/5
"""
input UserCourseLearningScheduleParamDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.param.UserCourseLearningScheduleParamDTO") {
	"""课程id集合"""
	courseIdList:[String]
	"""用户id -运营域参数，学员端忽略该参数"""
	userId:String
	"""选课类型
		@see com.fjhb.platform.core.courselearning.v1.api.constants.UserCourseSource
	"""
	source:String
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""用户已选课程查询参数
	@author: eleven
	@date: 2020/3/5
"""
input UserCourseParamDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.param.UserCourseParamDTO") {
	"""所属课程包id"""
	coursePoolId:String
	"""选课类型
		@see com.fjhb.platform.core.courselearning.v1.api.constants.UserCourseSource
	"""
	source:String
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""用户最后学习所在的课程查询条件
	@author: eleven
	@date: 2020/3/5
"""
input UserLastLearningCourseParamDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.param.UserLastLearningCourseParamDTO") {
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""用户课程
	@author: eleven
	@date: 2020/3/5
"""
input UserSchemeCourseLearningStatisticParamDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.param.UserSchemeCourseLearningStatisticParamDTO") {
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""用户课程学习记录查询参数
	@author: eleven
	@date: 2020/5/18
"""
input UserCourseRecordParam @type(value:"com.fjhb.btpx.platform.dao.elasticsearch.dto.UserCourseRecordParam") {
	userId:String
	schemeId:String
	issueId:String
	learningId:String
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input Sort @type(value:"com.fjhb.platform.core.course.v1.api.dto.Sort") {
	field:String
	dir:String
}
input CourseWareQueryDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.courseware.CourseWareQueryDTO") {
	name:String
	type:Int!
	categoryId:String
	supplierId:String
	needHasQuestion:Int!
	isUsable:Int!
	status:Int!
	startCreateTime:String
	endCreateTime:String
	statusList:[Int]
	sort:[Sort]
	unitId:String
	courseId:String
}
input TeacherQueryDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.teacher.TeacherQueryDTO") {
	name:String
	createUserId:String
}
input ResAuthorizedQuery @type(value:"com.fjhb.platformstandard.common.utils.dataauthorized.ResAuthorizedQuery") {
	authorizedState:Int!
	hasAuthorize:Boolean
	forbidAuthorize:Boolean!
	rangeType:String
	belongsType:String
	authorizeToUnitId:String
	authorizedFromUnitId:String
	objectId:String
	useType:String
	targetUnitId:String
}
"""精品课程
	<AUTHOR> create 2020/3/5 9:48
"""
type ExcellentCourseListDTO @type(value:"com.fjhb.btpx.integrative.gateway.graphql.dto.ExcellentCourseListDTO") {
	"""课程ID"""
	id:String
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""权重,表示学时,学分等"""
	period:Int!
	"""课程简介"""
	abouts:String
	"""是否启用"""
	enabled:Boolean!
	"""是否被逻辑删除"""
	delete:Boolean!
	"""课程的课件状态，0表示解析中，1表示解析成功，2表示解析失败"""
	status:Int!
	"""完成时间"""
	completedTime:DateTime
	"""课程详情"""
	contents:String
	"""创建时间"""
	createTime:DateTime
	"""创建者编号"""
	createUsrId:String
	"""计划授课讲数"""
	plannedLecturesNum:Int!
	"""多少人学过"""
	studyCount:Int!
	"""教师集合"""
	teachers:[TeacherInfoDTO]
}
"""<AUTHOR>
	@date 2020/8/18
	@description
"""
type CourseCategoryDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.CourseCategoryDTO") {
	"""分类id"""
	id:String
	"""分类名称"""
	name:String
}
"""<AUTHOR>
	@date 2020/8/18
	@description
"""
type CourseCategoryList @type(value:"com.fjhb.btpx.integrative.service.course.dto.CourseCategoryList") {
	"""课程id"""
	courseId:String
	"""所属分类"""
	categoryList:[CourseCategoryDTO]
}
"""课程信息 -不包括课程目录信息
	@author: eleven
	@date: 2020/3/4
"""
type CourseDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.CourseDTO") {
	"""课程ID"""
	id:String
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""权重,表示学时,学分等
		当查询未选课列表时，为课程在课程包的学时
		当查询已选课时，用户选课时，课程包配置的学时
	"""
	period:Double!
	"""课程简介"""
	abouts:String
	"""课程的课件状态，0表示解析中，1表示解析成功，2表示解析失败"""
	status:Int!
	"""课程时长"""
	timeLength:Long!
	"""是否支持试听"""
	supportAudition:Boolean!
	"""计划课件数量"""
	courseWareCount:Int!
	"""已更新的课件数量"""
	courseWareUpdateCount:Int!
	"""课件教师id集合"""
	teacherIdList:[String]
	"""所属课程分类集合（正常只有一个）"""
	courseCategoryDtoList:[CourseCategoryDto]
	"""课程所属考纲id集合"""
	tagIdList:[String]
	"""创建时间"""
	createTime:DateTime
	"""是否启用"""
	enabled:Boolean!
}
"""课程包被方案引用的对象
	<AUTHOR> create 2020/3/31 16:59
"""
type CoursePoolReferenceDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.CoursePoolReferenceDTO") {
	"""方案id"""
	schemeId:String
	"""方案名称"""
	schemeName:String
}
type CoursewareCategoryInfoDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.CoursewareCategoryInfoDTO") {
	"""分类编号"""
	id:String
	"""分类名称"""
	name:String
	"""排序"""
	sort:Int!
	"""是否启用"""
	enabled:Boolean!
	"""备注"""
	remarks:String
	"""父分类id"""
	parentId:String
	"""父分类"""
	parent:CoursewareCategoryInfoDTO
	"""创建者ID"""
	createUsrId:String
	"""创建人"""
	creator:Operator
	"""创建时间"""
	createTime:DateTime
}
type CoursewareDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.CoursewareDTO") {
	"""课件ID"""
	id:String
	"""课件名称"""
	name:String
	"""课件类型，1表示文档，2表示视频，3表示多媒体"""
	type:Int!
	"""课件原始类型, 1表示文档，2表示单视频，3表示串流大师，4表示汉博尔，5表示会计靠前，6表示Power+，7表示网视宝，8表示新华网，9表示地税网络学院，10表示中经网"""
	originalType:Int!
	"""媒体时长，单位秒"""
	timeLength:Int!
	"""教师名称"""
	teacherName:[String]
	"""教师简介"""
	teacherAbouts:String
	"""课件分类ID"""
	cwyId:String
	"""课件分类"""
	category:CoursewareCategoryInfoDTO
	"""供应商ID"""
	supplierId:String
	"""供应商"""
	supplier:SupplierInfoDTO
	"""课件状态 0解析中，1解析成功，2解析失败"""
	status:Int!
	"""是否可用"""
	usable:Boolean!
	"""创建者ID"""
	createUsrId:String
	"""创建人"""
	creator:Operator
	"""创建时间"""
	createTime:String
	"""自定义拓展信息"""
	expandData:String
	"""创建类型，0表示自建,1表示内置,2表示共享，3表示迁移，4表示购买"""
	createType:Int!
	"""创建类型相应时间"""
	createTypeTime:String
}
type SupplierInfoDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.SupplierInfoDTO") {
	"""主键 课件提供商ID"""
	id:String
	"""提供商名称"""
	name:String
	"""创建人id"""
	creator:String
	"""创建时间"""
	createTime:DateTime
}
"""考纲对应的课程数量
	<AUTHOR>
	@date 2020/4/1
	@since 1.0.0
"""
type SyllabusCourseCountDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.SyllabusCourseCountDTO") {
	"""考纲中的章节编号"""
	chapterId:String
	"""章节下课程数量"""
	courseCount:Long!
}
"""教师信息对象"""
type TeacherInfoDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.TeacherInfoDTO") {
	id:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	organizationId:String
	name:String
	photo:String
	abouts:String
	contents:String
	gender:Int!
	professionalTitle:String
	createUsrId:String
	creator:OperatorDTO
	createTime:DateTime
}
"""课程池内课程信息对象"""
type CourseInPoolDetailDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.CourseInPoolDetailDTO") {
	"""课程编号"""
	courseId:String
	"""课程名称"""
	courseName:String
	"""课程序号"""
	sequence:Int!
	"""课程标量值|在课程池规则中，课程在课程池中权重值"""
	quantitative:Double!
	"""课程学时|课程在课程池中的学时"""
	period:Double!
	"""课程过期时间|课程在课程池中到期时间，null表示该课程在课程池中无期限限制"""
	courseExpireTime:DateTime
	"""创建人编号"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""课程学时|课程中的学时"""
	coursePeriod:Double!
	"""所属课程池编号"""
	ccpId:String
	"""计划课件数量"""
	courseWareCount:Int!
	"""已更新的课件数量"""
	courseWareUpdateCount:Int!
	"""标签id集合"""
	tagIdList:[String]
}
"""
	课程被学习次数统计
	@author: eleven
	@date: 2020/4/13
"""
type CourseLearningStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.CourseLearningStatisticDTO") {
	"""课程id"""
	courseId:String
	"""被选课次数"""
	selectedCount:Long!
	"""待学习课程数"""
	waitStudyCount:Long!
	"""学习中课程数"""
	studyCount:Long!
	"""学习完成课程数"""
	studyFinishCount:Long!
}
"""课程目录信息
	@author: eleven
	@date: 2020/3/6
"""
type CourseOutlineDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.CourseOutlineDTO") {
	"""所属课程编号"""
	courseId:String
	"""目录信息"""
	outlineDTOList:[OutlineDTO]
	"""课件目录信息"""
	courseWareOutlineDTOList:[CourseWareOutlineDTO]
}
"""课程包基础信息
	Author:FangKunSen
	Time:2020-06-06,11:17
"""
type CoursePoolBaseInfoDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.CoursePoolBaseInfoDTO") {
	"""课程包id"""
	coursePoolId:String
	"""课程包名"""
	showName:String
}
type CoursePoolInfoDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.CoursePoolInfoDTO") {
	"""课程池编号"""
	id:String
	"""课程池名称"""
	poolName:String
	"""课程展示名称"""
	showName:String
	"""课程池状态|0/1/2，正常/无效/过期"""
	poolState:Int!
	"""排序序号"""
	sequence:Int!
	"""创建人编号"""
	createUserId:String
	"""创建时间"""
	createTime:String
	"""更新时间"""
	updateTime:DateTime
	"""过期时间,null表示不设置过期"""
	expireTime:DateTime
	"""课程池描述"""
	poolDescription:String
	"""课程池内课程数量"""
	courseCount:Int!
	"""课程池内课程权重值总和"""
	totalQuantitative:Double!
	"""课程池内课程学时或学分总和"""
	totalPeriod:Double!
}
"""课件目录 - 从课程目录信息取数，绑定父节点，前端自拼凑成树的结构
	@author: eleven
	@date: 2020/3/6
"""
type CourseWareOutlineDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.CourseWareOutlineDTO") {
	"""课件所在的目录id"""
	outlineId:String
	"""课件名称"""
	name:String
	"""课程id"""
	courseId:String
	"""课件id"""
	courseWareId:String
	"""课件类型，1表示文档，2表示视频，3表示多媒体"""
	type:Int!
	"""媒体时长，单位秒"""
	timeLength:Int!
}
"""课程目录-不含课件信息
	@author: eleven
	@date: 2020/3/5
"""
type OutlineDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.OutlineDTO") {
	"""课程目录ID"""
	id:String
	"""目录名称"""
	name:String
	"""父类ID"""
	parentId:String
	"""课程id"""
	courseId:String
}
"""用户已选课程
	@author: eleven
	@date: 2020/3/5
"""
type UserCourseDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.UserCourseDTO") {
	"""所属课程包编号"""
	poolId:String
	"""所属课程类型
		1:必修课；2：选修；
	"""
	courseType:Int!
	"""课程ID"""
	id:String
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""权重,表示学时,学分等
		当查询未选课列表时，为课程在课程包的学时
		当查询已选课时，用户选课时，课程包配置的学时
	"""
	period:Double!
	"""课程简介"""
	abouts:String
	"""课程的课件状态，0表示解析中，1表示解析成功，2表示解析失败"""
	status:Int!
	"""课程时长"""
	timeLength:Long!
	"""是否支持试听"""
	supportAudition:Boolean!
	"""计划课件数量"""
	courseWareCount:Int!
	"""已更新的课件数量"""
	courseWareUpdateCount:Int!
	"""课件教师id集合"""
	teacherIdList:[String]
	"""所属课程分类集合（正常只有一个）"""
	courseCategoryDtoList:[CourseCategoryDto]
	"""课程所属考纲id集合"""
	tagIdList:[String]
	"""创建时间"""
	createTime:DateTime
	"""是否启用"""
	enabled:Boolean!
}
"""课程学习记录，来自于清洗中间表
	<AUTHOR>
	@date 2020/8/25
	@description
"""
type UserCourseLearningRecordResponse @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.UserCourseLearningRecordResponse") {
	"""用户"""
	userId:String
	"""方案id"""
	schemeId:String
	"""方式id"""
	learningId:String
	"""期别编号"""
	stageId:String
	"""期数编号"""
	issueId:String
	"""年度"""
	year:Int!
	"""选课规则id"""
	ruleId:String
	"""包id"""
	poolId:String
	"""课程id"""
	courseId:String
	"""选课类型
		@see UserChooseCourseType
	"""
	chooseCourseType:Int!
	"""课程学时"""
	period:Double!
	"""课程时长"""
	timeLength:Double!
	"""课程学习进度"""
	schedule:Double!
	"""课程学习时长"""
	learningTimeLength:Double!
	"""课程学习状态
		@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
	"""
	studyState:Int!
	"""开始学习时间"""
	startStudyTime:String
	"""最后学习时间"""
	lastStudyTime:String
	"""学习完成时间"""
	studyCompleteTime:String
}
"""用户指定学习方案内指定课程的学习进度
	@author: eleven
	@date: 2020/3/5
"""
type UserCourseLearningScheduleDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.UserCourseLearningScheduleDTO") {
	"""学习方案id"""
	schemeId:String
	"""课程id"""
	courseId:String
	"""学习进度"""
	schedule:Double!
	"""学习状态
		0/1/2，未学习/学习中/学习完成
		@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
	"""
	studyState:Int!
	"""最新学习时间"""
	lastStudyTime:DateTime
}
"""用户课件学习记录编号
	<AUTHOR>
	@date 2020/3/14
	@since 1.0.0
"""
type UserCoursewareLearningScheduleDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.UserCoursewareLearningScheduleDTO") {
	"""学习方案id"""
	schemeId:String
	"""课程id"""
	courseId:String
	"""课件编号"""
	courseWareId:String
	"""课件学习进度"""
	schedule:Double!
	"""课件学习状态
		0/1/2，未学习/学习中/学习完成
		@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
	"""
	studyState:Int!
	"""最新学习时间"""
	lastStudyTime:DateTime
}
"""用户最后一次学习的课程
	@author: eleven
	@date: 2020/3/5
"""
type UserLastLearningCourseDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.UserLastLearningCourseDTO") {
	"""学习方案id"""
	schemeId:String
	"""学习方式id"""
	learningId:String
	"""包id"""
	poolId:String
	"""课程id"""
	courseId:String
	"""课程学习进度"""
	schedule:Double!
	"""最后一次学习时间"""
	lastStudyTime:String
}
"""用户方案内正在的学习的课程数统计
	@author: eleven
	@date: 2020/3/5
"""
type UserSchemeCourseLearningStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.UserSchemeCourseLearningStatisticDTO") {
	"""待学习课程数"""
	waitStudyCount:Double!
	"""学习中课程数"""
	studyCount:Double!
	"""学习完成课程数"""
	studyFinishCount:Double!
}
type MarkerDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.MarkerDTO") {
	key:String
	value:String
}
type OperatorDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.OperatorDTO") {
	userId:String
	name:String
	uniqueData:String
}
type SupplierDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.SupplierDTO") {
	id:String
	name:String
	creator:String
	createTime:DateTime
}
type UnitDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.UnitDTO") {
	id:String
	name:String
}
type CoursePoolDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.coursepool.CoursePoolDTO") {
	id:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	unit:UnitDTO
	organizationId:String
	poolName:String
	sequence:Int!
	markers:[MarkerDTO]
	createUsrId:String
	expireTime:DateTime
	poolDescription:String
	showName:String
	creator:OperatorDTO
	createTime:DateTime
	courseCount:Int!
	totalPeriod:Double!
}
type CourseWareDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.courseware.CourseWareDTO") {
	id:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	organizationId:String
	name:String
	type:Int!
	originalType:Int!
	timeLength:Int!
	teacherId:String
	teacher:TeacherDTO
	abouts:String
	cwyId:String
	category:CourseWareCategoryDTO
	coursewareResourcePath:String
	resourceMD5:String
	supplierId:String
	supplier:SupplierDTO
	usable:Boolean!
	status:Int!
	createUsrId:String
	creator:OperatorDTO
	createTime:DateTime
	expandData:String
	createType:Int!
	createTypeTime:DateTime
}
type CourseWareCategoryDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.coursewarecategory.CourseWareCategoryDTO") {
	id:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	organizationId:String
	name:String
	sort:Int!
	enabled:Boolean!
	remarks:String
	parentId:String
	createUsrId:String
	creator:OperatorDTO
	createTime:DateTime
}
type TeacherDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.teacher.TeacherDTO") {
	id:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	organizationId:String
	name:String
	photo:String
	abouts:String
	contents:String
	gender:Int!
	professionalTitle:String
	createUsrId:String
	creator:OperatorDTO
	createTime:DateTime
}
type Operator @type(value:"com.fjhb.platform.core.course.v1.kernel.assist.operator.Operator") {
	userId:String
	name:String
	nickName:String
	uniqueData:String
}
type CourseCategoryDto @type(value:"com.fjhb6.course.v2.north.api.dto.CourseCategoryDto") {
	id:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	organizationId:String
	name:String
	parentId:String
	sort:Int!
	remarks:String
	objectId:String
}

scalar List
type CoursewareDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CoursewareDTO]}
type UserCourseDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserCourseDTO]}
type CourseDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseDTO]}
type CoursePoolInfoDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CoursePoolInfoDTO]}
type TeacherInfoDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TeacherInfoDTO]}
type UserCourseLearningRecordResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserCourseLearningRecordResponse]}
type CourseLearningStatisticDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseLearningStatisticDTO]}
