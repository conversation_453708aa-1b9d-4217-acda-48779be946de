import MsSchemeLearningQueryForestageGateway from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import CertificateTemplateResponseVo from './vo/CertificateTemplateResponseVo'
import { Response } from '@hbfe/common'

/**
 * 培训证明模板
 */
class QueryCertificateTemplate {
  /**
   * @description: 根据证书模板id获取证书详情
   * @param {string} certificateTemplateId
   */
  async queryCertificateTemplate(certificateTemplateId: string): Promise<Response<CertificateTemplateResponseVo>> {
    const res = await MsSchemeLearningQueryForestageGateway.getCertificateTemplate(certificateTemplateId)
    const response = new Response<CertificateTemplateResponseVo>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    const responseData = new CertificateTemplateResponseVo()
    responseData.from(res?.data)
    response.status = res.status
    response.data = responseData
    return response
  }
}

export default QueryCertificateTemplate
