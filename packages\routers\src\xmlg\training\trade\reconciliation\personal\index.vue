<route-meta>
{
"isMenu": true,
"title": "个人报名对账",
"sort": 1,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>

<script lang="ts">
  import ReconciliationPersonalIndex from '@hbfe/jxjy-admin-trade/src/diff/xmlg/reconciliation/personal/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    orderReconciliation: [WXGLY, FXS, GYS],
    orderReconciliationFx: [NZFXS, NZFXSJCB],
    orderReconciliationZt: [ZTGLY],
    refundReconciliation: [WXGLY, FXS, GYS],
    refundReconciliationFx: [NZFXS, NZFXSJCB],
    refundReconciliationZt: [ZTGLY],
    editInvoicePopup: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB],
    export: [WXGLY, FXS, GYS],
    exportFx: [NZFXS, NZFXSJCB],
    exportZt: [ZTGLY],
    query: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    doSearch: [WXGLY, FXS, GYS],
    doSearchfx: [NZFXS, NZFXSJCB],
    doSearchzt: [ZTGLY]
  })
  export default class extends ReconciliationPersonalIndex {}
</script>
