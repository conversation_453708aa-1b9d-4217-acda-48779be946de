import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 换班状态枚举
 * 1：全部
 * 2：换班中
 * 3：换班成功
 */
export enum ExchangeTrainClassStatusEnum {
  ALL = 1,
  Exchanging = 2,
  Complete_Exchange = 3
}

class ExchangeTrainClassStatusType extends AbstractEnum<ExchangeTrainClassStatusEnum> {
  static enum = ExchangeTrainClassStatusEnum
  constructor(status?: ExchangeTrainClassStatusEnum) {
    super()
    this.current = status
    this.map.set(ExchangeTrainClassStatusEnum.ALL, '全部')
    this.map.set(ExchangeTrainClassStatusEnum.Exchanging, '换班中')
    this.map.set(ExchangeTrainClassStatusEnum.Complete_Exchange, '换班成功')
  }
}

export default new ExchangeTrainClassStatusType()
