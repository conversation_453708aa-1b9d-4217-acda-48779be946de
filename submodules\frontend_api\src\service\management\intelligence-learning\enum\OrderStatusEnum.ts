import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum OrderStatusEnum {
  /**
   * 未开通
   */
  un_open = 0,
  /**
   * 开通中
   */
  open_ing,
  /**
   * 已开通
   */
  open_success,
  /**
   * 开通失败
   */
  open_fail
}
export default class OrderStatusType extends AbstractEnum<OrderStatusEnum> {
  static enum = OrderStatusEnum
  constructor(status?: OrderStatusEnum) {
    super()
    this.current = status
    this.map.set(OrderStatusEnum.un_open, '未开通')
    this.map.set(OrderStatusEnum.open_ing, '开通中')
    this.map.set(OrderStatusEnum.open_success, '已开通')
    this.map.set(OrderStatusEnum.open_fail, '开通失败')
  }

  /**
   * 映射标签类型
   */
  get labelType() {
    switch (this.current) {
      case OrderStatusEnum.open_success:
        return 'success'
      case OrderStatusEnum.open_fail:
        return 'danger'
      default:
        return ''
    }
  }
}
