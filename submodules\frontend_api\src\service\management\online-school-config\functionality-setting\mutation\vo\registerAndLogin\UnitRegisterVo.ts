/*
 * @Author: dong<PERSON><PERSON>
 * @Date: 2024-02-26 14:59:31
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-02-26 16:17:20
 * @Description:
 */
import {
  DockingInfoRequest,
  DockingTycAndQccRequest,
  DockingTycAndQccResponse
} from '@api/ms-gateway/ms-servicer-series-v1'
import { UnitSearchServiceTypeEnum } from '@api/service/management/online-school-config/functionality-setting/enum/UnitSearchServiceType'

export default class UnitRegisterVo {
  /**
   * 工作单位对接 天眼查/企查查
   */
  unitConnectQuery: boolean = undefined
  /**
   * 服务商类型
   */
  serviceType: Array<UnitSearchServiceTypeEnum> = new Array<UnitSearchServiceTypeEnum>()

  /**
   * 天眼查账号授权token
   */
  tyAccountToken: string = undefined

  /**
   * 企查查查账号授权token
   */
  qccAccountToken: string = undefined

  /**
   * 企查查密钥
   */
  qccKey: string = undefined

  static from(dto: DockingTycAndQccResponse) {
    const vo = new UnitRegisterVo()
    vo.unitConnectQuery = dto?.enabled
    dto?.dockingInfoList?.length &&
      dto.dockingInfoList.map(item => {
        switch (item.serviceType) {
          case UnitSearchServiceTypeEnum.qccSearch:
            vo.serviceType.push(UnitSearchServiceTypeEnum.qccSearch)
            vo.qccAccountToken = item.dockingAccount
            vo.qccKey = item.secret
            break
          case UnitSearchServiceTypeEnum.tySearch:
            vo.serviceType.push(UnitSearchServiceTypeEnum.tySearch)
            vo.tyAccountToken = item.dockingAccount
            break
        }
      })
    return vo
  }

  toRequest() {
    const request = new DockingTycAndQccRequest()
    request.enabled = this.unitConnectQuery
    request.dockingInfoList = new Array<DockingInfoRequest>()
    const qccSearch = new DockingInfoRequest()
    const tySearch = new DockingInfoRequest()
    this.serviceType.map(item => {
      switch (item) {
        case UnitSearchServiceTypeEnum.qccSearch:
          qccSearch.serviceType = item
          qccSearch.dockingAccount = this.qccAccountToken
          qccSearch.secret = this.qccKey
          request.dockingInfoList.push(qccSearch)
          break
        case UnitSearchServiceTypeEnum.tySearch:
          tySearch.serviceType = item
          tySearch.dockingAccount = this.tyAccountToken
          // 天眼查没有密钥 默认赋值空串
          tySearch.secret = ''
          request.dockingInfoList.push(tySearch)
          break
      }
    })
    return request
  }
}
