import { Page } from '@hbfe/common'
import DistributionCourseParams from '@api/service/management/resource/course/query/vo/DistributionCourseParams'
import DistributionCourseList from '@api/service/management/resource/course/query/vo/DistributionCourseList'
import DistributionCourseDetail from '@api/service/management/resource/course/query/vo/DistributionCourseDetail'
import CourseLearningBackstage, {
  CourseResponse,
  CoursewareDetailResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import * as GraphqlImporterCourseLearningBackstage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage/graphql-importer'
import MsCourseLearningQueryFrontGatewayCourseLearningForestage, {
  TeacherResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

class QueryCourseDistribution {
  courseList: CourseResponse[] = []

  /**
   * 查询待分配课程列表
   * @param page
   * @param params
   */
  async queryCourseList(page: Page, params: DistributionCourseParams): Promise<DistributionCourseList[]> {
    const teacherIds: string[] = []
    const request = await DistributionCourseParams.to(params)
    if ((!request.courseIdList || !request.courseIdList.length) && params.courseName) {
      return [] as DistributionCourseList[]
    }
    const response = await CourseLearningBackstage.pageCourseInServicer({
      page,
      request
    })
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('获取待分配查询课件失败', response)
      return Promise.reject(response)
    }
    page.totalSize = response.data.totalSize
    page.totalPageSize = response.data.totalPageSize
    if (response.data.currentPageData && response.data.currentPageData.length) {
      response.data.currentPageData.map(res => {
        teacherIds.push(...res.teacherIds)
      })
      let teacherMap: TeacherResponse[] = []
      if (teacherIds && teacherIds.length) {
        teacherMap = await this.batchQueryTeacherInfo(teacherIds)
      }
      this.courseList = response.data.currentPageData
      return response.data.currentPageData.map(res => DistributionCourseList.from(res, teacherMap))
    } else {
      return [] as DistributionCourseList[]
    }
  }

  /**
   * 查询待分配课程详情
   * @param courseId
   */
  async queryCourseById(courseId: string): Promise<DistributionCourseDetail> {
    let coursewareIds: string[] = []
    const temp = this.courseList.find(res => res.id === courseId)
    let course
    let teacherMap
    let coursewearMap
    if (temp) {
      const courseDetail = await this.queryCourseDetail(temp.id)
      coursewareIds = courseDetail.data.courseChapters.map(res => res.coursewareId)
      if (temp.teacherIds && temp.teacherIds.length) {
        teacherMap = await this.batchQueryTeacherInfo(temp.teacherIds)
      }
      if (coursewareIds && coursewareIds.length) {
        coursewearMap = await this.batchQueryCourseware(coursewareIds)
      }
      course = DistributionCourseDetail.from(temp, teacherMap)
      course = DistributionCourseDetail.addCourseContent(course, courseDetail.data, coursewearMap)
    } else {
      return new DistributionCourseDetail()
    }
    return course
  }

  // * 批量查询教师信息
  async batchQueryTeacherInfo(ids: string[]) {
    ids = Array.from(new Set(ids))
    const response = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.listTeacherInServicer(ids)
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('查询教师列表报错')
      return Promise.reject(response)
    }
    return response.data
    // const rew = new RewriteGraph<TeacherResponse, string>(CourseDefault._commonQuery, GraphqlImporter.getTeacher)
    // await rew.request(ids)
    // return rew.itemMap
  }

  // * 查询课程详情
  async queryCourseDetail(id: string) {
    const response = await CourseLearningBackstage.getCourseInServicer(id)
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('获取课程详情失败', response)
      return Promise.reject(response)
    }
    return response
  }

  // * 批量查询课件详情
  async batchQueryCourseware(ids: string[]) {
    ids = Array.from(new Set(ids))
    const rew = new RewriteGraph<CoursewareDetailResponse, string>(
      CourseLearningBackstage._commonQuery,
      GraphqlImporterCourseLearningBackstage.getCoursewareInServicer
    )
    await rew.request(ids)
    return rew.itemMap
  }
}

export default QueryCourseDistribution
