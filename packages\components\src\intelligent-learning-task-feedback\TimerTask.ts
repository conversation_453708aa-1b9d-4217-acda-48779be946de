const { EventEmitter } = require('events')

export enum Events {
  start = 'start',
  timeupdate = 'timeupdate',
  stop = 'stop'
}

/**
 * 时间定时器
 */
export default class TimerTask extends EventEmitter {
  static Events = Events
  id: any
  /**
   * 时间间隔,单位毫秒
   */
  duration = 1000

  constructor(duration = 1000) {
    super()
    this.duration = duration
  }

  /**
   * @param countDown 当前已倒计时时间
   */
  start(countDown?: number) {
    if (!this.duration) {
      return
    }
    this.stop()
    const currentDuration = countDown ? this.duration - countDown : this.duration
    this.id = setTimeout(() => {
      this.emit(TimerTask.Events.timeupdate)
    }, currentDuration)
    this.emit(TimerTask.Events.start)
  }

  stop() {
    if (!this.duration) {
      return
    }
    clearTimeout(this.id)
    this.id = null
    this.emit(TimerTask.Events.stop)
  }
}
