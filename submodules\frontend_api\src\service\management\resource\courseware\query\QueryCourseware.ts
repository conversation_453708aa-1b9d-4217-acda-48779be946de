import { Page, UiPage } from '@hbfe/common'
import CoursewareDetail from '@api/service/management/resource/courseware/query/vo/CoursewareDetail'
import QueryCoursewareListParamVo from '@api/service/management/resource/courseware/query/vo/QueryCoursewareListParamVo'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage, {
  CoursewareCategoryRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import CoursewareListDetail from '@api/service/management/resource/courseware/query/vo/CoursewareListDetail'
import CoursewareTreeListDetail from '@api/service/management/resource/courseware/query/vo/CoursewareTreeListDetail'
import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'
import MsBasicdataQueryFrontGatewayBasicDataQueryBackstage, {
  AdminQueryRequest,
  AdminUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import pageSimpleAdminInfoInServicer from './graphql/pageSimpleAdminInfoInServicer.graphql'
import { uniq } from 'lodash'
import UserModule from '@api/service/management/user/UserModule'

class QueryCourseware {
  coursewareTreeList: Array<CoursewareTreeListDetail> = new Array<CoursewareTreeListDetail>()

  /**
   * 根据用户 id 查询用户列表
   * @param idList
   * @private
   */
  private async queryUserInfoByIdList(idList: Array<string>): Promise<SimpleUserInfo> {
    const list = uniq(idList)
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = list.length
    const request = new AdminQueryRequest()
    request.user = new AdminUserRequest()
    request.user.userIdList = list
    const pageList = await MsBasicdataQueryFrontGatewayBasicDataQueryBackstage.pageAdminInfoInServicer({
      page,
      request
    })
    console.log(pageList)
    return
  }

  /**
   * 查询课件分页
   * @param page
   * @param queryParam
   */
  // todo(wengpf) 等待实现
  async pageCourseware(page: Page, queryParam: QueryCoursewareListParamVo): Promise<Array<CoursewareListDetail>> {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursewareInServicer({
      page,
      request: queryParam.to()
    })
    page.totalPageSize = result.data?.totalPageSize
    page.totalSize = result.data?.totalSize
    let temp = result.data.currentPageData.map(CoursewareListDetail.from)
    let userIds = result.data.currentPageData.map(item => {
      return item.createUserId
    })
    userIds = [...new Set(userIds.map(item => item).filter(Boolean))]
    if (userIds.length) {
      // 购买人id集合不为空才获取用户信息
      UserModule.queryUserFactory.queryManager.batchQueryUserInfo(userIds).then(userMap => {
        temp = temp.map(res => CoursewareListDetail.addUserInfo(res, userMap))
      })
    }
    return temp
  }

  async queryCoursewareByIdDeep(idDeep: Array<string>) {
    await this.queryCoursewareListTree()
    // await this.queryCoursewareList(this.coursewareTreeList[0])
    // await this.queryCoursewareList(this.coursewareTreeList[0].children[0])
  }

  /**
   * 给定分类，查询子类，自动填充到子类 children 中
   * @param coursewareCategory
   */
  async queryCoursewareListTree(coursewareCategory?: CoursewareTreeListDetail) {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    const request = new CoursewareCategoryRequest()
    request.parentId = coursewareCategory?.id
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursewareInServicer({
      page,
      request
    })
    page.pageSize = result.data.totalSize
    const lastResult = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursewareCategoryInServicer({
      page,
      request
    })

    if (coursewareCategory) {
      coursewareCategory.children = lastResult.data.currentPageData.map(CoursewareTreeListDetail.from)
    } else {
      this.coursewareTreeList = lastResult.data.currentPageData.map(CoursewareTreeListDetail.from)
    }
  }

  /**
   * 根据 id 查询子列表
   * 直接返回列表数据
   * @param coursewareId
   */
  async queryCoursewareChildren(coursewareId?: string) {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    const request = new CoursewareCategoryRequest()
    request.parentId = coursewareId
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursewareCategoryInServicer({
      page,
      request
    })
    page.pageSize = result.data.totalSize
    const lastResult = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursewareCategoryInServicer({
      page,
      request
    })
    return lastResult.data.currentPageData.map(CoursewareTreeListDetail.from)
  }

  /**
   * 获取课件详情
   * @param id
   */
  async queryCoursewareById(id: string): Promise<CoursewareDetail> {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.getCoursewareInServicer(id)
    console.log(result, 'result')

    return CoursewareDetail.from(result.data)
  }
}

export default QueryCourseware
