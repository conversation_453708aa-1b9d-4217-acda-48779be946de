/*
 * @Description: 资讯变异业务类
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-24 19:06:28
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-16 10:23:06
 */
import QueryHotNewsList from '@api/service/customer/news/query/query-hot-news-list/QueryHotNewsList'
import QueryWebTypeList from '@api/service/customer/news/query/query-type-news-list/QueryWebTypeNewsList'
import QueryH5TypeList from '@api/service/customer/news/query/query-type-news-list/QueryH5TypeNewsList'
import QueryPopupNews from '@api/service/customer/news/query/query-popup-news/QueryPopupNews'
import QueryNewsDetail from '@api/service/customer/news/query/query-news-detail/QueryDetailNews'
class QueryNewsFactory {
  /**
   * 获取热门资讯 - 完成
   * @returns QueryHotNewsList
   */
  get getQueryHostNewsList() {
    return new QueryHotNewsList()
  }
  /**
   * web获取类别资讯列表 -- 完成
   * @returns QueryWebTypeList
   */
  get getQueryWebTypeNewsList() {
    return new QueryWebTypeList()
  }
  /**
   * H5获取类别资讯列表
   * @returns QueryH5TypeList
   */
  get getQueryH5TypeNewsList() {
    return new QueryH5TypeList()
  }
  /**
   * 获取弹窗资讯 - 完成
   * @returns QueryH5TypeList
   */
  get getQueryPopupNews() {
    return new QueryPopupNews()
  }
  /**
   * 获取资讯详情 - 完成
   * @returns QueryH5TypeList
   */
  get getQueryDetailNews() {
    return (id: string) => {
      return new QueryNewsDetail(id)
    }
  }
}
export default QueryNewsFactory
