// import { SpecifySchemeRuleDto } from '@api/ms-gateway/ms-learning-constraint-v1'
import { SpecifySchemeRuleDto } from '@api/ms-gateway/ms-learning-constraint-v1'
import { TimeModeEnum } from '@api/service/management/online-learning-rule/enum/TimeModeEnum'
import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'
export default class SchemeSpecialRuleItem {
  /**
   * 规则id
   */
  id = ''
  /**
   * 适用方案数组
   */
  schemeList: RuleSchemeItem[] = []
  /**
   * 适用方案名称数组
   */
  get schemeNameList() {
    return this.schemeList.map((item) => item.schemeName)
  }
  /**
   * 每天学习物理时长（分钟）
   */
  everyDayLearningTime = 0
  /**
   * 每天学习学时（时）
   */
  everyDayLearningHours = 0
  /**
   * 时长方式
   * 1：按课程物理时长 2：按课程学习学时
   */
  timeMode: TimeModeEnum = undefined

  static to(item: SchemeSpecialRuleItem, type?: string) {
    // const temp = new SpecifySchemeRuleDto()
    const temp: SpecifySchemeRuleDto = new SpecifySchemeRuleDto()
    if (type != 'add') {
      temp.specifySchemeRuleId = item.id
    }
    temp.schemeIdList = item.schemeList.map((scheme) => {
      return scheme.schemeId
    })
    if (item.timeMode === TimeModeEnum.physical) {
      temp.ruleType = 1
      temp.maxStudyTimeLength = item.everyDayLearningTime * 60
    } else if (item.timeMode === TimeModeEnum.learning) {
      temp.ruleType = 2
      temp.maxStudyTimeLength = item.everyDayLearningHours
    }
    temp.timeLengthLimitValue = 1
    temp.timeLengthLimitWay = 0
    return temp
  }
}
