/*
 * @Description: 列表转换数据
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 11:53:50
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-01-29 15:29:52
 */

import { OrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import CheckAccountList from '@api/service/management/trade/single/checkAccount/query/vo/CheckAccountListResponse'
import QueryPlatform from '@api/service/diff/common/qztg/dictionary/QueryPlatform'
import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/qztg/trade/enums/SaleChannelType'

export default class CheckAccountListResponse extends CheckAccountList {
  /**
   * 第三方平台
   */
  thirdPartyPlatform = ''
  static from(orderResponse: OrderResponse) {
    const refundCheckAccountListResponse = Object.assign(
      new CheckAccountListResponse(),
      CheckAccountList.from(orderResponse)
    )
    if (orderResponse.saleChannel == SaleChannelEnum.huayi) {
      refundCheckAccountListResponse.thirdPartyPlatform = SaleChannelType.map.get(orderResponse.saleChannel)
    } else if (orderResponse.subOrderItems?.[0]?.deliveryCommoditySku.tppTypeId) {
      refundCheckAccountListResponse.thirdPartyPlatform = QueryPlatform.map.get(
        orderResponse.subOrderItems?.[0]?.deliveryCommoditySku.tppTypeId
      )?.name
    }
    return refundCheckAccountListResponse
  }
}
