import { LodgingTypeEnum } from '@api/service/common/implement/enums/LodgingTypeEnum'

export default class AccommodationInformation {
  /**
   * 是否住宿
   */
  isAccommodation: boolean = null

  /**
   * 住宿方式（例如：单人间、双人间等）
   */
  accommodationType: LodgingTypeEnum = null

  /**
   * 住宿信息须知（例如：入住须知、退房须知等）
   */
  accommodationInstructions: string = undefined
  /**
   * 是否开启住宿采集信息配置
   */
  isOpenAccommodationGather: boolean = null
}
