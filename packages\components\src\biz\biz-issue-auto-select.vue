<template>
  <el-autocomplete
    v-model="selectValue"
    :fetch-suggestions="querySearchAsync"
    value-key="issueName"
    value="issueName"
    placeholder="请输入期别名称"
    @select="handleSelect"
    @clear="clear"
    clearable
  ></el-autocomplete>
</template>
<script lang="ts">
  import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
  import QueryTrainClassIssue, {
    QueryTrainClassIssueListParam
  } from '@api/service/management/train-class/offlinePart/QueryTrainClassIssue'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'

  @Component({})
  export default class extends Vue {
    @Prop({ type: String, default: '' }) value: string
    @Prop({ type: String, default: '' }) commoditySkuId: string
    @Watch('value', { immediate: true, deep: true })
    valueChange() {
      this.selectValue = this.value
    }
    /**
     * 期别列表
     */
    issueList = new Array<IssueConfigDetail>()

    /**
     * 查询数据
     */
    queryTrainClassIssue = new QueryTrainClassIssue()

    selectValue = ''

    async querySearchAsync(queryStr: string, callback: any) {
      await this.queryIssueList(queryStr)
      callback(this.issueList)
    }

    /**
     * 查询数据
     */
    async queryIssueList(issueName: string) {
      try {
        const params = new QueryTrainClassIssueListParam()
        params.issueName = issueName
        params.commoditySkuId = this.commoditySkuId === '' ? '' : this.commoditySkuId
        this.issueList = await this.queryTrainClassIssue.queryTrainClassIssueList(params)
      } catch (e) {
        console.log(e)
        this.issueList = new Array<IssueConfigDetail>()
      }
    }

    /**
     * 选中
     */
    handleSelect(item: IssueConfigDetail) {
      this.$emit('getIssueId', item.id)
      this.$emit('input', this.selectValue)
    }

    clear() {
      this.$emit('getIssueId', '')
      this.$emit('input', '')
    }
  }
</script>
