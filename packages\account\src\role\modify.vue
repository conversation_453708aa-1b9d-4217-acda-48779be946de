<route-meta>{"title": "修改角色信息"}</route-meta>
<route-params content="/:id"></route-params>

<template>
  <el-main>
    <hb-bread-crumb></hb-bread-crumb>
    <div class="f-p15">
      <el-row :gutter="15" class="is-height">
        <el-col :md="11" :xl="10">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header">
              <span class="tit-txt">角色信息</span>
            </div>
            <div class="f-p30">
              <el-skeleton animated :rows="3" v-if="!updateRole.id" />
              <el-form
                ref="createForm"
                :model="updateRole"
                label-width="auto"
                class="m-form"
                v-if="updateRole.id"
                :rules="rules"
                @submit.native.prevent
              >
                <el-form-item label="角色名称：" prop="name">
                  <el-input v-model="updateRole.name" clearable placeholder="请输入角色名称" class="form-m" />
                </el-form-item>
                <el-form-item label="角色说明：">
                  <el-input type="textarea" :rows="6" v-model="updateRole.description" placeholder="请输入角色说明" />
                </el-form-item>
              </el-form>
            </div>
            <div class="m-tit is-border-bottom">
              <span class="tit-txt">关联的管理员帐号</span>
            </div>
            <div class="f-p20">
              <used-admin-account :role="updateRole"></used-admin-account>
            </div>
          </el-card>
        </el-col>
        <el-col :md="13" :xl="14">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header">
              <span class="tit-txt">角色权限</span>
            </div>
            <div class="f-p20 m-authority" v-loading="queryPermissionLoading">
              <div class="selected-all">
                <el-checkbox label="超管（全选）" v-model="isAllChecked"></el-checkbox>
              </div>
              <el-tree
                :data="permissionList"
                size="mini"
                show-checkbox
                node-key="id"
                ref="tree"
                highlight-current
                :props="defaultProps"
              >
              </el-tree>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button type="primary" @click="save" :loading="isLoading">保存</el-button>
        <el-button @click="goBack">取消</el-button>
      </div>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import { RolePermissionDto, SecurityObjectGroupDto } from '@api/gateway/PlatformBasicData'
  import { SecurityGroupTree } from '@api/service/management/authority/security/query/vo/SecurityGroupTree'
  import SecurityFactory from '@api/service/management/authority/security/SecurityFactory'
  import UpdateRole from '@api/service/management/authority/role/UpdateRole'
  import UsedAdminAccount from '@hbfe/jxjy-admin-account/src/role/__components__/used-admin-account.vue'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

  @Component({
    components: { UsedAdminAccount }
  })
  export default class extends Vue {
    @Ref('tree') tree: any
    @Ref('createForm') createForm: any
    roleId = ''

    isLoading = false
    createRole = new RolePermissionDto()
    detailSecurityObjectGroupDtoList = new Array<SecurityObjectGroupDto>()
    getCreateOrUpdateRole = AuthorityModule.roleMutationFactory.getCreateOrUpdateRole()
    updateRole: UpdateRole = new UpdateRole()
    /**
     * el-tree取值规则
     */
    defaultProps = {
      children: 'children',
      label: 'name'
    }
    rules = {
      name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }]
    }
    isAllChecked = false
    getQueryAllPermission = new SecurityFactory()
    getQuerySecurityGroupByRoleId = AuthorityModule.roleFactory.getQuerySecurityGroupByRoleId()
    permissionList = new Array<SecurityGroupTree>()
    rolePermission = new Array<SecurityGroupTree>()
    nodeIds = [] as any
    queryPermissionLoading = false

    /**
     * 节点映射表，用于快速查找节点
     * 空间换时间
     */
    nodeMap: Record<string, SecurityGroupTree> = {}

    async created() {
      this.roleId = this.$route.params.id
      this.updateRole = new UpdateRole(this.roleId)
      await this.updateRole.query()
      // 获取当前角色全部安全对象
      await this.queryPermission()
      // 填充节点映射表
      this.buildNodeMap(this.permissionList)
      // 获取当前角色安全对象
      const res = await this.getQueryAllPermission.querySecurity.getSpecificRoleSecurityGroup(
        this.roleId,
        this.updateRole.category
      )
      this.rolePermission = res
      this.recursionMethodForSecurityGroupIds(this.rolePermission)
    }

    /**
     * 监听isAllChecked值变化，触发全选/取消全选
     */
    @Watch('isAllChecked', { deep: true })
    handleWatch() {
      const tree: any = this.$refs.tree
      const bol = this.isAllChecked
      this.permissionList.forEach(function (data: any) {
        // setCheckedKeys 会更快，但是这个通用取消全选
        tree.setChecked(data.id, bol, true)
      })
      const checkedKeys = tree.getCheckedKeys(false)
    }

    /**
     *
     循环所有子节点，温馨提示，tree的方法只有setChecked可以用，其他（setCheckedNodes、setCheckedKeys、setCurrentKey、setCurrentNode）都有bug  ?note:可后续优化
     el-tree组件文档：https://element.eleme.cn/#/zh-CN/component/tree
     设置节点选中
     */
    recursionMethodForSecurityGroupIds(SecurityGroupTree: SecurityGroupTree[]) {
      SecurityGroupTree.forEach((group: SecurityGroupTree) => {
        // 在完整权限树中查找对应节点
        const fullNode = this.findNodeInPermissionList(group.id)

        // 判断是否是叶子节点（同时验证完整树结构）
        if (fullNode?.children?.length === 0) {
          this.tree.setChecked(group.id, true)
        } else {
          this.recursionMethodForSecurityGroupIds(group.children)
        }
      })
    }

    /**
     * 递归构建哈希表
     */
    buildNodeMap(nodes: SecurityGroupTree[]) {
      nodes.forEach((node) => {
        this.nodeMap[node.id] = node
        if (node.children) {
          this.buildNodeMap(node.children)
        }
      })
    }

    /**
     * 在完整权限树中查找节点
     */
    findNodeInPermissionList(id: string): SecurityGroupTree | undefined {
      return this.nodeMap[id]
    }

    async queryPermission() {
      this.queryPermissionLoading = true
      try {
        if (this.updateRole.category === CategoryEnums.ztgly) {
          const specialAdminRoleId = ConfigCenterModule.getFrontendApplication(frontendApplication.specialAdminRoleId)
          this.permissionList = await this.getQueryAllPermission.querySecurity.getSecurityGroupByRoleId(
            [specialAdminRoleId],
            this.updateRole.category
          )
        } else {
          this.permissionList = await this.getQueryAllPermission.querySecurity.getCurrentUserSecurityGroupByCategory(
            this.updateRole.category
          )
        }
      } catch (e) {
        this.permissionList = []
        console.log(e)
      } finally {
        this.queryPermissionLoading = false
      }
    }

    async save() {
      this.isLoading = true
      try {
        await this.createForm.validate()
        const createRole = new RolePermissionDto()
        const tree: any = this.$refs.tree
        const nodeIds = new Array<string>()
        const permission = tree.getCheckedNodes(false, true)
        if (!tree.isAllSelected) {
          const result = permission.find((item: any) => {
            return item.name === '查询(必选)'
          })
          if (!result?.id) {
            this.isLoading = false
            this.$message.error('请勾选查询(必选)')
            return
          }
        }

        permission.forEach(function (node: SecurityGroupTree) {
          if (node.securityAuthorizationId) {
            nodeIds.push(node.securityAuthorizationId)
          } else {
            nodeIds.push(node.id)
          }
        })
        this.updateRole.functionalAuthorityIds = nodeIds
        const res = await this.updateRole.updateRoleByAdminType()
        console.log(res)

        if (res.code == 200) {
          this.$message.success('修改成功')
          this.isLoading = false
          await this.$router.replace({
            path: '/basic-data/account/role'
          })
        } else {
          this.$message.error(res.errors[0].message)
          this.isLoading = false
        }
      } catch (e) {
        console.log(e)
        this.$message.error('修改失败')
        this.isLoading = false

        // 需要输入表单才能正常保存
      }
    }

    // 返回上一级页面
    goBack() {
      this.$router.back()
    }
  }
</script>
