import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-zzkd-school'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

export class StudentSchemeLearningRequest {
  studentNoList?: Array<string>
  student?: UserRequest
  learningRegister?: LearningRegisterRequest
  scheme?: SchemeRequest
  studentLearning?: StudentLearningRequest
  dataAnalysis?: DataAnalysisRequest
  connectManageSystem?: ConnectManageSystemRequest
  extendedInfo?: ExtendedInfoRequest
  openPrintTemplate?: boolean
  saleChannels?: Array<number>
  trainingChannelName?: string
  trainingChannelId?: string
  notDistributionPortal?: boolean
  trainingType?: string
  issueId?: string
}

export class ConnectManageSystemRequest {
  syncStatus?: number
}

export class DataAnalysisRequest {
  trainingResultPeriod?: DoubleScopeRequest
  requirePeriod?: DoubleScopeRequest
  acquiredPeriod?: DoubleScopeRequest
}

export class ExtendedInfoRequest {
  whetherToPrint?: boolean
  applyCompanyCode?: string
  policyTrainingSchemeId?: string
  policyTrainingSchemeName?: string
}

export class LearningRegisterRequest {
  registerType?: number
  sourceType?: string
  sourceId?: string
  status?: Array<number>
  registerTime?: DateScopeRequest
  saleChannels?: Array<number>
  orderNoList?: Array<string>
  subOrderNoList?: Array<string>
  batchOrderNoList?: Array<string>
  distributorId?: string
  portalId?: string
}

export class RegionRequest {
  province?: string
  city?: string
  county?: string
}

export class StudentLearningRequest {
  trainingResultList?: Array<number>
  trainingResultTime?: DateScopeRequest
  notLearningTypeList?: Array<number>
  courseScheduleStatus?: number
  examAssessResultList?: Array<number>
}

export class RegionSkuPropertyRequest {
  province?: string
  city?: string
  county?: string
}

export class RegionSkuPropertySearchRequest {
  region?: Array<RegionSkuPropertyRequest>
  regionSearchType?: number
}

export class SchemeRequest {
  schemeId?: string
  schemeIdList?: Array<string>
  schemeType?: string
  schemeName?: string
  skuProperty?: SchemeSkuPropertyRequest
}

export class SchemeSkuPropertyRequest {
  year?: Array<string>
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  industry?: Array<string>
  subjectType?: Array<string>
  trainingCategory?: Array<string>
  trainingProfessional?: Array<string>
  technicalGrade?: Array<string>
  positionCategory?: Array<string>
  trainingObject?: Array<string>
  jobLevel?: Array<string>
  jobCategory?: Array<string>
  subject?: Array<string>
  grade?: Array<string>
  learningPhase?: Array<string>
  discipline?: Array<string>
  qualificationCategory?: Array<string>
  trainingWay?: Array<string>
  trainingInstitution?: Array<string>
  mainAdditionalItem?: Array<string>
}

export class UserPropertyRequest {
  regionList?: Array<RegionRequest>
  companyName?: string
  payOrderRegionList?: Array<RegionRequest>
}

export class UserRequest {
  userIdList?: Array<string>
  accountIdList?: Array<string>
  userProperty?: UserPropertyRequest
}

export class ConnectManageSystemResponse {
  syncStatus: number
  syncMessage: string
}

export class BatchOwnerResponse {
  unitId: string
  userId: string
}

export class ExtendedInfoResponse {
  whetherToPrint: boolean
  printTime: string
  pdfUrl: string
  certificateId: string
  certificateNo: string
}

export class LearningRegisterResponse {
  registerType: number
  sourceType: string
  sourceId: string
  status: number
  statusChangeTime: string
  registerTime: string
  saleChannel: number
  orderNo: string
  subOrderNo: string
  batchOrderNo: string
  frozenAndInvalidSourceType: string
  frozenAndInvalidSourceId: string
}

export class OwnerResponse {
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  servicerType: number
  servicerId: string
  batchOwner: BatchOwnerResponse
}

export class CourseLearningResponse {
  courseScheduleStatus: number
  courseQualifiedTime: string
  selectedCourseCount: number
  selectedCoursePeriod: number
  learningId: string
  learningType: number
  enabled: boolean
  learningResourceType: number
  learningResourceId: string
  userAssessResult: Array<string>
  learningResult: Array<LearningResultResponse>
}

export class DataAnalysisResponse {
  trainingResultPeriod: number
  requirePeriod: number
  acquiredPeriod: number
}

export class ExamLearningResponse {
  committedExam: boolean
  examAssessResult: number
  examQualifiedTime: string
  examCount: number
  maxExamScore: number
  learningId: string
  learningType: number
  enabled: boolean
  learningResourceType: number
  learningResourceId: string
  userAssessResult: Array<string>
  learningResult: Array<LearningResultResponse>
}

export class LearningExperienceLearningResponse {
  committedLearningExperience: boolean
  learningExperienceAssessResult: number
  learningExperienceQualifiedTime: string
  maxLearningExperienceScore: number
  learningExperiencePassCount: number
  learningId: string
  learningType: number
  enabled: boolean
  learningResourceType: number
  learningResourceId: string
  userAssessResult: Array<string>
  learningResult: Array<LearningResultResponse>
}

export class StudentLearningResponse {
  trainingResult: number
  trainingResultTime: string
  courseLearning: CourseLearningResponse
  examLearning: ExamLearningResponse
  learningExperienceLearning: LearningExperienceLearningResponse
  userAssessResult: Array<string>
  learningResult: Array<LearningResultResponse>
}

export class CertificateLearningConfigResultResponse implements LearningResultConfigResponse {
  certificateTemplateId: string
  openPrintTemplate: boolean
  resultType: number
}

export class GradeLearningConfigResultResponse implements LearningResultConfigResponse {
  gradeType: string
  grade: number
  resultType: number
}

export interface LearningResultConfigResponse {
  resultType: number
}

export class LearningResultResponse {
  learningResultId: string
  gainedTime: string
  learningResultConfig: LearningResultConfigResponse
}

export class SchemeResponse {
  schemeId: string
  schemeType: string
  skuProperty: SchemeSkuPropertyResponse
  schemeName: string
  learningResult: Array<LearningResultConfigResponse>
}

export class SchemeSkuPropertyResponse {
  year: SchemeSkuPropertyValueResponse
  province: SchemeSkuPropertyValueResponse
  city: SchemeSkuPropertyValueResponse
  county: SchemeSkuPropertyValueResponse
  industry: SchemeSkuPropertyValueResponse
  subjectType: SchemeSkuPropertyValueResponse
  trainingCategory: SchemeSkuPropertyValueResponse
  trainingProfessional: SchemeSkuPropertyValueResponse
  technicalGrade: SchemeSkuPropertyValueResponse
  positionCategory: SchemeSkuPropertyValueResponse
  trainingObject: SchemeSkuPropertyValueResponse
  jobLevel: SchemeSkuPropertyValueResponse
  jobCategory: SchemeSkuPropertyValueResponse
  subject: SchemeSkuPropertyValueResponse
  grade: SchemeSkuPropertyValueResponse
  learningPhase: SchemeSkuPropertyValueResponse
  discipline: SchemeSkuPropertyValueResponse
  certificatesType: SchemeSkuPropertyValueResponse
  practitionerCategory: SchemeSkuPropertyValueResponse
  trainingInstitution: SchemeSkuPropertyValueResponse
  mainAdditionalItem: SchemeSkuPropertyValueResponse
  trainingWay: SchemeSkuPropertyValueResponse
}

export class SchemeSkuPropertyValueResponse {
  skuPropertyValueId: string
}

export class RegionResponse {
  province: string
  city: string
  county: string
}

export class UserPropertyResponse {
  region: RegionResponse
  payOrderRegion: RegionResponse
}

export class UserResponse {
  userId: string
  accountId: string
  userProperty: UserPropertyResponse
}

export class ZZKDStudentSchemeLearningResponse {
  /**
   * 证书编号
   */
  certificateNumber: string
  qualificationId: string
  studentNo: string
  owner: OwnerResponse
  student: UserResponse
  learningRegister: LearningRegisterResponse
  scheme: SchemeResponse
  studentLearning: StudentLearningResponse
  dataAnalysis: DataAnalysisResponse
  connectManageSystem: ConnectManageSystemResponse
  extendedInfo: ExtendedInfoResponse
  schemeQuestionnaireRequirementCount: number
  schemeQuestionnaireNoAssessSubmittedCount: number
  schemeQuestionnaireSubmittedCount: number
  issueName: string
}

export class ZZKDStudentSchemeLearningResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ZZKDStudentSchemeLearningResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分销管理员导出学习明细导出（超级管理员导出学习明细新口）
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportStudentSchemeLearningExcelInDistributor(
    request: StudentSchemeLearningRequest,
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: true
      })
    )
  }

  /**   * 超级管理员导出学习明细（超级管理员导出学习明细新口）
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportStudentSchemeLearningExcelInServicer(
    request: StudentSchemeLearningRequest,
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: true
      })
    )
  }

  /**   * 地区管理员导出学习明细（超级管理员导出学习明细新口）
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportStudentSchemeLearningExcelInServicerManageRegion(
    request: StudentSchemeLearningRequest,
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInServicerManageRegion,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: true
      })
    )
  }

  /**   * 专题管理员导出学习明细导出（超级管理员导出学习明细新口）
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportStudentSchemeLearningExcelInTrainingChannelV2(
    request: StudentSchemeLearningRequest,
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInTrainingChannelV2,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: true
      })
    )
  }

  /**   * 集体查询学习明细接口
   * @param page
   * @param request
   * @param dataFetchingEnvironment
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentSchemeLearningInCollectiveV2(
    params: { page?: Page; request?: StudentSchemeLearningRequest },
    query: DocumentNode = GraphqlImporter.pageStudentSchemeLearningInCollectiveV2,
    operation?: string
  ): Promise<Response<ZZKDStudentSchemeLearningResponsePage>> {
    return commonRequestApi<ZZKDStudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商管理员查询学习明细接口(超级管理员查询学习明细新口)
   * @param page
   * @param request
   * @param dataFetchingEnvironment
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentSchemeLearningInDistributor(
    params: { page?: Page; request?: StudentSchemeLearningRequest },
    query: DocumentNode = GraphqlImporter.pageStudentSchemeLearningInDistributor,
    operation?: string
  ): Promise<Response<ZZKDStudentSchemeLearningResponsePage>> {
    return commonRequestApi<ZZKDStudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 地区管理员管理员查询学习明细接口(超级管理员查询学习明细新口)
   * @param page
   * @param request
   * @param dataFetchingEnvironment
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentSchemeLearningInServicerManageRegion(
    params: { page?: Page; request?: StudentSchemeLearningRequest },
    query: DocumentNode = GraphqlImporter.pageStudentSchemeLearningInServicerManageRegion,
    operation?: string
  ): Promise<Response<ZZKDStudentSchemeLearningResponsePage>> {
    return commonRequestApi<ZZKDStudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 超级管理员查询学习明细接口(超级管理员查询学习明细新口)
   * @param page
   * @param request
   * @param dataFetchingEnvironment
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentSchemeLearningInServicerV2(
    params: { page?: Page; request?: StudentSchemeLearningRequest },
    query: DocumentNode = GraphqlImporter.pageStudentSchemeLearningInServicerV2,
    operation?: string
  ): Promise<Response<ZZKDStudentSchemeLearningResponsePage>> {
    return commonRequestApi<ZZKDStudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员管理员查询学习明细接口(超级管理员查询学习明细新口)
   * @param page
   * @param request
   * @param dataFetchingEnvironment
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentSchemeLearningInTrainingChannelV2(
    params: { page?: Page; request?: StudentSchemeLearningRequest },
    query: DocumentNode = GraphqlImporter.pageStudentSchemeLearningInTrainingChannelV2,
    operation?: string
  ): Promise<Response<ZZKDStudentSchemeLearningResponsePage>> {
    return commonRequestApi<ZZKDStudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
