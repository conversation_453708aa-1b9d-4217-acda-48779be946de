import CreateExamCategory from './mutation/CreateExamPaperCategory'
import ExamPaperCategoryAction from './mutation/ExamPaperCategoryAction'
class MutationExamCategoryFactory {
  /**
   * @description: 获取创建试卷分类业务类
   * @param {*}
   * @return {*}
   */
  get createExamCategory() {
    return new CreateExamCategory()
  }

  /**
   * @description: 获取试卷分类基础操作类-删除修改
   * @param {*}
   * @return {*}
   */
  get examCategoryAction() {
    return (id: string, name: string, parentId: string, sort?: number) => {
      return new ExamPaperCategoryAction(id, name, parentId, sort)
    }
  }
}
export default new MutationExamCategoryFactory()
