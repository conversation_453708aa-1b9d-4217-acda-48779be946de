import Vue from 'vue'
import orderGateway, {
  CreateOrderRequest,
  CreateOrderResultResponse,
  OrderOnlinePaymentRequest
} from '@api/ms-gateway/ms-order-v1'
import tradeQueryGateway, { OrderDTO } from '@api/gateway/PlatformTrade'
import { MyOrderPageQuery } from './models/MyOrderPageQuery'
import { ResponseStatus } from '@api/Response'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import StateKey from '@api/service/common/models/common/enums/StateKey'

/**
 * 订单数据状态
 */
interface UserOrderState {
  /**
   * 订单列表
   * key由页面提供，页面获取分页列表时根据key再获取对应分页信息
   */
  orderListMap: { [key: string]: { list: Array<OrderDTO>; totalSize: number } }
  /**
   * 我的订单详情
   */
  orderDetailMap: { [orderNo: string]: OrderDTO }
  /**
   * 订单创建结果
   */
  createOrderResult: { [commoditySkuId: string]: CreateOrderResultResponse }
}

/**
 * 用户订单模块
 * 需要的事件
 * 提交订单完成，订单列表需要重新加载
 * 去支付订单，订单列表需要重新加载
 * 修改发票信息，订单列表需要重新加载
 * 取消订单，订单列表需要重新加载
 */
@Module({ namespaced: true, dynamic: true, name: 'CustomerUserOrderModule', store })
class UserOrderModule extends VuexModule implements UserOrderState {
  //region state
  orderListMap: { [key: string]: { list: Array<OrderDTO>; totalSize: number } } = {}
  orderDetailMap: { [orderNo: string]: OrderDTO } = {}
  createOrderResult: { [commoditySkuId: string]: CreateOrderResultResponse } = {}
  //endregion

  //region action

  /**
   * 创建订单
   * @param createOrderRequest
   */
  @Action
  async doCreateOrder(params: { createOrderRequest: CreateOrderRequest }) {
    // params.createOrderRequest.needInvoice = false
    // ; unitId: string
    // 校验渠道商id是否有效
    // try {
    //   if (params.createOrderRequest.channelVendorId) {
    //     const status = await ServicerModule.findRelationServiceListByType({
    //       inputIdList: [params.createOrderRequest.channelVendorId],
    //       inPutType: ServicerTypeEnums.CHANNEL_VENDOR,
    //       outPutType: ServicerTypeEnums.TRAINING_INSTITUTION
    //     })
    //     const index = ServicerModule.servicerList?.findIndex(el => el.id === params.unitId)
    //     const isExist = index !== -1
    //     if (
    //       !status.isSuccess() ||
    //       !ServicerModule.servicerList ||
    //       ServicerModule.servicerList?.length === 0 ||
    //       !isExist
    //     ) {
    //       console.error('渠道商id无效，已匹配机构列表：', ServicerModule.servicerList)
    //       params.createOrderRequest.channelVendorId = undefined
    //     }
    //   }
    // } catch (error) {
    //   console.error(error)
    //   params.createOrderRequest.channelVendorId = undefined
    // }
    const { status, data } = await orderGateway.createOrder({
      ...params.createOrderRequest,
      invoiceInfo: params.createOrderRequest.invoiceInfo
        ? Object.keys(params.createOrderRequest.invoiceInfo).length == 0
          ? undefined
          : params.createOrderRequest.invoiceInfo
        : undefined
    })
    if (!status.isSuccess()) {
      return { status, data }
    }
    return { status, data }
  }

  /**
   * 申请在线支付
   * 前端如果从缓存中取到创建订单结果，可以直接调用在线支付
   */
  @Action
  async onlinePayOrder(payInfo: OrderOnlinePaymentRequest) {
    return await orderGateway.onlinePayOrder(payInfo)
  }

  /**
   * 取消订单
   * @param payload
   */
  @Action
  async doCancelOrder(payload: { orderNo: string; reasonId?: string; reason?: string }) {
    const response = await orderGateway.applyCancelOrder(payload)
    if (response.status.isSuccess()) {
      // 修改订单状态为：关闭订单
      this.SET_ORDER_STATUS({ orderNo: payload.orderNo, status: 7 })
    }
    return response
  }

  /**
   * 根据订单查询条件，获取查询订单列表,append:表示是否往状态层的订单列表中增加数据，而不是将列表数据变成获取后的分页数据
   * key由页面提供，页面获取分页列表时根据key再获取对应分页信息
   * @param payload
   */
  @Action
  async getMyOrderPage(payload: { query: MyOrderPageQuery; key?: string; append: boolean }): Promise<ResponseStatus> {
    const queryParam = payload.query.toMyOrderQueryDTO()
    const { status, data } = await tradeQueryGateway.pageMyOrder({
      page: {
        pageNo: payload.query.pageNo,
        pageSize: payload.query.pageSize
      },
      param: queryParam
    })
    if (!status.isSuccess()) {
      return status
    }
    if (data) {
      this.SET_ORDER_LIST({
        key: payload.key || StateKey.DEFAULT,
        list: data.currentPageData,
        totalSize: data.totalSize,
        append: payload.append
      })
      for (const orderInfo of data.currentPageData) {
        this.SET_ORDER_DETAIL(orderInfo)
      }
    }
    return status
  }

  // 查询订单详情
  @Action
  async getMyOrderDetail(orderNo: string): Promise<ResponseStatus> {
    const response = await tradeQueryGateway.getMyOrder(orderNo)
    if (!response.status) {
      return response.status
    }
    if (response.data) {
      this.SET_ORDER_DETAIL(response.data)
    } else {
      return new ResponseStatus(30001, '订单不存在')
    }
    return response.status
  }

  //endregion

  //region mutation
  @Mutation
  private SET_ORDER_LIST(payload: { key: string; list: Array<OrderDTO>; totalSize: number; append: boolean }) {
    const oldList = this.orderListMap[payload.key]
    if (oldList?.list) {
      if (!payload.append) {
        oldList.list = payload.list
      } else {
        oldList.list.push(...payload.list)
      }
      oldList.totalSize = payload.totalSize
    } else {
      Vue.set(this.orderListMap, payload.key, { list: payload.list, totalSize: payload.totalSize })
    }
  }

  @Mutation
  private SET_ORDER_DETAIL(order: OrderDTO) {
    const oldOrder = this.orderDetailMap[order.orderNo]
    if (oldOrder) {
      Object.assign(oldOrder, order)
    } else {
      Vue.set(this.orderDetailMap, order.orderNo, order)
    }
  }

  @Mutation
  private SET_ORDER_STATUS(payload: { orderNo: string; status: number }) {
    const order = this.orderDetailMap[payload.orderNo]
    if (order) {
      order.status = payload.status
    }
    this.orderListMap?.values?.list.filter(o => o.orderNo === payload.orderNo).forEach(o => (o.status = payload.status))
  }

  //endregion
}

export default getModule(UserOrderModule)
