import { ResponseStatus } from '@hbfe/common'
import MsOfflineinvoiceV1 from '@api/ms-gateway/ms-offlineinvoice-v1'
import UpdateTakePlaceVo from '@api/service/management/online-school-config/distribution-channels-config/mutation/vo/UpdateTakePlaceVo'

/**
 * @description 更新自取点信息
 */
class MutationUpdateTakePlace {
  vo = new UpdateTakePlaceVo()

  constructor(update: UpdateTakePlaceVo) {
    this.vo = update
  }

  async doUpdate(): Promise<ResponseStatus> {
    const { status } = await MsOfflineinvoiceV1.updateChannel(this.vo.toUpdateDto())
    return status
  }
}

export default MutationUpdateTakePlace
