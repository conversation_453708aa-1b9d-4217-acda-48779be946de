<template>
  <div>
    <el-card shadow="never" class="m-card">
      <div class="m-card is-header-sticky">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="loginSetting" class="m-collapse-item">
            <template slot="title">通用登录规则</template>
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt">登录</span>
            </div>
            <el-row type="flex" justify="center" class="width-limit f-p20">
              <el-col :md="14" :lg="14" :xl="14">
                <el-form
                  ref="loginForm"
                  :rules="loginRules"
                  :model="mutationRegisterAndLogin.loginSetting.generalLogin"
                  label-width="180px"
                  class="m-form"
                >
                  <el-form-item>
                    <div slot="label">
                      <span class="f-vm">强制初始密码修改</span>
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                        <div slot="content">
                          <p>以下是会出现初始密码的场景：</p>
                          <p>1.通过导入学员、导入学员并开班、导入学员开班并学习创建的学员账号密码</p>
                          <p>2.通过客服重置过学员的账号密码</p>
                          <p>3.通过学员前端页面线上自助集体报名方式创建的学员账号密码</p>
                        </div>
                      </el-tooltip>
                      <span>：</span>
                    </div>
                    <el-switch
                      v-model="mutationRegisterAndLogin.loginSetting.generalLogin.forcePwdChange"
                      active-text="开启"
                      inactive-text="关闭"
                      class="m-switch"
                    />
                    <el-alert type="warning" :closable="false" class="m-alert f-mt10">
                      本网校使用初始密码的学员在下次登录时必须修改密码
                    </el-alert>
                  </el-form-item>
                  <el-form-item label="更换密码周期：">
                    <el-switch
                      v-model="mutationRegisterAndLogin.loginSetting.generalLogin.changePwdCycleRule.enable"
                      active-text="开启"
                      inactive-text="关闭"
                      class="m-switch"
                    />
                    <template v-if="mutationRegisterAndLogin.loginSetting.generalLogin.changePwdCycleRule.enable">
                      <div class="f-mt10">
                        <el-form-item
                          prop="changePwdCycle"
                          :rules="
                            mutationRegisterAndLogin.loginSetting.generalLogin.changePwdCycleRule.enable
                              ? loginRules.changePwdCycle
                              : [{ required: false }]
                          "
                        >
                          <el-input
                            class="input-num f-mr10"
                            v-model="
                              mutationRegisterAndLogin.loginSetting.generalLogin.changePwdCycleRule.changePwdCycle
                            "
                            placeholder="请输入"
                          ></el-input
                          >天
                        </el-form-item>
                      </div>
                      <el-alert type="warning" :closable="false" class="m-alert f-mt20">
                        本网校管理端、学员端（含H5）要求用户更新其密码的固定时间间隔
                      </el-alert>
                    </template>
                  </el-form-item>
                  <el-form-item label="限制非法登录次数：">
                    <el-switch
                      v-model="mutationRegisterAndLogin.loginSetting.generalLogin.limitLoginNumRule.enable"
                      active-text="开启"
                      inactive-text="关闭"
                      class="m-switch"
                    />
                    <template v-if="mutationRegisterAndLogin.loginSetting.generalLogin.limitLoginNumRule.enable">
                      <div class="f-mt10">
                        <el-form-item
                          prop="limitLoginNum"
                          :rules="
                            mutationRegisterAndLogin.loginSetting.generalLogin.limitLoginNumRule.enable
                              ? loginRules.limitLoginNum
                              : [{ required: false }]
                          "
                        >
                          <el-input
                            class="input-num f-mr10"
                            placeholder="请输入"
                            v-model="mutationRegisterAndLogin.loginSetting.generalLogin.limitLoginNumRule.limitLoginNum"
                          ></el-input
                          >次
                        </el-form-item>
                      </div>
                      <el-alert type="warning" :closable="false" class="m-alert f-mt20">
                        本网校管理端、学员端（含H5）用户在一定时间（1小时）内允许的尝试登录失败次数上限
                      </el-alert>
                      <div class="f-mt10">
                        <el-form-item
                          prop="lockTime"
                          :rules="
                            mutationRegisterAndLogin.loginSetting.generalLogin.limitLoginNumRule.enable
                              ? loginRules.lockTime
                              : [{ required: false }]
                          "
                        >
                          <el-input
                            class="input-num f-mr10"
                            placeholder="请输入"
                            v-model="mutationRegisterAndLogin.loginSetting.generalLogin.limitLoginNumRule.lockTime"
                          ></el-input
                          >分钟
                        </el-form-item>
                      </div>
                      <el-alert type="warning" :closable="false" class="m-alert f-mt20">
                        设置达到失败次数上限后，账户被锁一定时间
                      </el-alert>
                    </template>
                  </el-form-item>
                  <el-form-item label="登录时短信验证：">
                    <el-switch
                      v-model="mutationRegisterAndLogin.loginSetting.generalLogin.loginWithSMSRule.enable"
                      active-text="开启"
                      inactive-text="关闭"
                      class="m-switch"
                    />
                    <el-alert type="warning" :closable="false" class="m-alert f-mt10">
                      该功能仅在管理员登录时生效
                    </el-alert>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item name="webLoginSetting" class="m-collapse-item">
            <template slot="title">Web 登录设置</template>
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt">微信扫码登录设置</span>
            </div>
            <el-row type="flex" justify="center" class="width-limit f-p20">
              <el-col :md="14" :lg="13" :xl="10">
                <el-form
                  ref="scanForm"
                  :model="mutationRegisterAndLogin.loginSetting.scanSeetingVo"
                  :rules="scanRules"
                  label-width="180px"
                  class="m-form"
                >
                  <el-form-item>
                    <div slot="label">
                      <span class="f-vm">微信扫码登录</span>
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                        <div slot="content">
                          <p>开启微信扫码登录功能需要配置以下信息：</p>
                          <p>1.申请微信开放平台账户信息：（帐号、密码、绑定人）</p>
                          <p>2.前往开放平台申请“网站应用”并配置相关域名和回调地址</p>
                          <p>3.填写APP ID 和 秘钥信息</p>
                          <p class="f-mt10">
                            操作提醒：如果修改APPID和秘钥信息会将原绑定微信的学员全部解绑，请联系平台运营管理员进行操作。
                          </p>
                        </div>
                      </el-tooltip>
                      <span>：</span>
                    </div>
                    <el-switch
                      v-model="mutationRegisterAndLogin.loginSetting.scanSeetingVo.enable"
                      active-text="开启"
                      inactive-text="关闭"
                      class="m-switch"
                    />
                  </el-form-item>
                  <el-form-item
                    label="App ID："
                    prop="appId"
                    v-if="mutationRegisterAndLogin.loginSetting.scanSeetingVo.enable"
                  >
                    <el-input
                      v-model="mutationRegisterAndLogin.loginSetting.scanSeetingVo.appId"
                      :disabled="isSaveScan"
                      clearable
                      placeholder="请输入 App ID 信息"
                    />
                  </el-form-item>
                  <el-form-item
                    label="AppSecret："
                    prop="secret"
                    v-if="mutationRegisterAndLogin.loginSetting.scanSeetingVo.enable"
                  >
                    <el-input
                      v-model="mutationRegisterAndLogin.loginSetting.scanSeetingVo.secret"
                      :disabled="isSaveScan"
                      clearable
                      placeholder="请输入 AppSecret 信息"
                    />
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item name="H5LoginSetting" class="m-collapse-item">
            <template slot="title">H5 登录设置</template>
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt">微信授权登录设置</span>
            </div>
            <el-row type="flex" justify="center" class="width-limit f-pt35 f-p20">
              <el-col :md="14" :lg="13" :xl="10">
                <el-form
                  ref="authForm"
                  :rules="authRules"
                  :model="mutationRegisterAndLogin.loginSetting.authSeetingVo"
                  label-width="180px"
                  class="m-form"
                >
                  <el-form-item>
                    <div slot="label">
                      <span class="f-vm">微信授权登录</span>
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                        <div slot="content">
                          <p>开启微信绑定登录功能需要配置以下信息：</p>
                          <p>1.申请微信公众号账户信息：（帐号、密码、绑定人）</p>
                          <p>2.前往公众号设置下的功能设置配置相关城名</p>
                          <p>3.填写APP ID 和 秘钥信息</p>
                          <p class="f-mt10">
                            操作提醒：如果修改APPID和秘钥信息会将原绑定微信的学员全部解绑，请联系平台运营管理员进行操作。
                          </p>
                        </div>
                      </el-tooltip>
                      <span>：</span>
                    </div>
                    <el-switch
                      v-model="mutationRegisterAndLogin.loginSetting.authSeetingVo.enable"
                      active-text="开启"
                      inactive-text="关闭"
                      class="m-switch"
                    />
                  </el-form-item>
                  <el-form-item
                    label="App ID："
                    prop="appId"
                    v-if="mutationRegisterAndLogin.loginSetting.authSeetingVo.enable"
                  >
                    <el-input
                      v-model="mutationRegisterAndLogin.loginSetting.authSeetingVo.appId"
                      :disabled="isSaveAuth"
                      clearable
                      placeholder="请输入 App ID 信息"
                    />
                  </el-form-item>
                  <el-form-item
                    label="AppSecret："
                    prop="secret"
                    v-if="mutationRegisterAndLogin.loginSetting.authSeetingVo.enable"
                  >
                    <el-input
                      v-model="mutationRegisterAndLogin.loginSetting.authSeetingVo.secret"
                      :disabled="isSaveAuth"
                      clearable
                      placeholder="请输入 AppSecret 信息"
                    />
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt">账号登录设置</span>
            </div>
            <el-row type="flex" justify="center" class="width-limit f-pt35 f-p20">
              <el-col :md="14" :lg="13" :xl="10">
                <el-form :model="mutationRegisterAndLogin.loginSetting" label-width="160px" class="m-form">
                  <el-form-item>
                    <div slot="label">
                      <span class="f-vm">账号登录（H5）</span>
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                        <div slot="content">
                          开放设置H5的登录方式，如开启，支持使用账号密码登录平台。
                          需注意：在微信内部访问，在支付环节需学员账号绑定当前的微信，否则无法进入支付。
                        </div>
                      </el-tooltip>
                      <span>：</span>
                    </div>
                    <el-switch
                      v-model="mutationRegisterAndLogin.loginSetting.accountSettingVo.enable"
                      active-text="开启"
                      inactive-text="关闭"
                      class="m-switch"
                    />
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    <div class="m-btn-bar f-tc is-sticky-1">
      <el-button @click="doCancel" :loading="loading">取消</el-button>
      <el-button type="primary" @click="validForm" :loading="loading">保存</el-button>
    </div>
    <el-dialog title="提示" :visible.sync="sureSaveDialog" width="450px" class="m-dialog">
      <div>修改APPID和AppSecret会将原绑定微信的学员全部解绑，保存后无法修改。如需调整请联系运营人员。确定保存？</div>
      <div slot="footer">
        <el-button @click="sureSaveDialog = false">取 消</el-button>
        <el-button type="primary" @click="doSave">确 定</el-button>
      </div>
    </el-dialog>
    <give-up-dialog :give-up-model="uiConfig.giveUpModel" @callBack="resetData"></give-up-dialog>
  </div>
</template>

<script lang="ts">
  import MutationRegisterAndLogin from '@api/service/management/online-school-config/functionality-setting/mutation/MutationRegisterAndLogin'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import { bind, debounce } from 'lodash-decorators'
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'

  @Component({
    components: { GiveUpDialog }
  })
  export default class extends Vue {
    @Ref('scanForm') scanForm: any
    @Ref('authForm') authForm: any
    @Ref('loginForm') loginForm: any

    /**
     * 展开模块
     */
    activeNames = ['loginSetting', 'webLoginSetting', 'H5LoginSetting']
    //保存二次提醒弹窗
    sureSaveDialog = false
    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }

    mutationRegisterAndLogin: MutationRegisterAndLogin =
      OnlineSchoolConfigModule.mutationFunctionalitySettingFactory.registerAndLogin

    form = {
      delivery1: '',
      resource: '',
      name: ''
    }

    scanRules = {
      appId: [
        {
          required: true,
          message: '请输入AppID信息',
          trigger: ['blur']
        }
      ],
      secret: [
        {
          required: true,
          message: '请输入AppSecret信息',
          trigger: ['blur']
        }
      ]
    }

    authRules = {
      appId: [
        {
          required: true,
          message: '请输入AppID信息',
          trigger: ['blur']
        }
      ],
      secret: [
        {
          required: true,
          message: '请输入AppSecret信息',
          trigger: ['blur']
        }
      ]
    }

    /**
     * 通用登录规则表单校验
     */
    loginRules = {
      changePwdCycle: { required: true, validator: this.validateChangePwdCycleRule, trigger: 'blur' },
      limitLoginNum: { required: true, validator: this.validateLimitLoginNum, trigger: 'blur' },
      lockTime: { required: true, validator: this.validateLockTime, trigger: 'blur' }
    }

    //扫码配置是否已经保存过
    isSaveScan = false

    //授权配置是否已经保存过
    isSaveAuth = false

    /**
     * 校验是否为正整数
     */
    numberTest = new RegExp(/^\d+$/)

    /**
     * 加载状态
     */
    loading = false

    @Watch('mutationRegisterAndLogin.loginSetting.scanSeetingVo.enable')
    onScanSeetingVoEnableChange(newVal: boolean) {
      this.scanRules.appId = [{ required: newVal, message: '请输入App ID', trigger: ['blur'] }]
      this.scanRules.secret = [{ required: newVal, message: '请输入AppSecret', trigger: ['blur'] }]
      // 如果开关打开，手动触发表单验证
      if (newVal) {
        this.scanForm.validateField(['appId', 'secret'])
      }
    }
    @Watch('mutationRegisterAndLogin.loginSetting.authSeetingVo.enable')
    onAuthSeetingVoEnableChange(newVal: boolean) {
      this.authRules.appId = [{ required: newVal, message: '请输入App ID', trigger: ['blur'] }]
      this.authRules.secret = [{ required: newVal, message: '请输入AppSecret', trigger: ['blur'] }]

      // 如果开关打开，手动触发表单验证
      if (newVal) {
        this.authForm.validateField(['appId', 'secret'])
      }
    }

    /**
     * 校验更换密码周期
     */
    validateChangePwdCycleRule(rule: any, value: any, callback: any) {
      if (!this.mutationRegisterAndLogin.loginSetting.generalLogin.changePwdCycleRule.changePwdCycle) {
        callback(new Error('请输入更换密码周期'))
      } else if (
        !this.numberTest.test(
          this.mutationRegisterAndLogin.loginSetting.generalLogin.changePwdCycleRule.changePwdCycle.toString()
        ) ||
        Number(this.mutationRegisterAndLogin.loginSetting.generalLogin.changePwdCycleRule.changePwdCycle) <= 0
      ) {
        callback(new Error('请输入正整数'))
      } else {
        callback()
      }
    }

    /**
     * 校验限制非登录次数
     */
    validateLimitLoginNum(rule: any, value: any, callback: any) {
      if (!this.mutationRegisterAndLogin.loginSetting.generalLogin.limitLoginNumRule.limitLoginNum) {
        callback(new Error('请输入限制非法登录次数'))
      } else if (
        !this.numberTest.test(
          this.mutationRegisterAndLogin.loginSetting.generalLogin.limitLoginNumRule.limitLoginNum.toString()
        ) ||
        Number(this.mutationRegisterAndLogin.loginSetting.generalLogin.limitLoginNumRule.limitLoginNum) <= 0
      ) {
        callback(new Error('请输入正整数'))
      } else {
        callback()
      }
    }

    /**
     * 校验限制非法登录时间
     */
    validateLockTime(rule: any, value: any, callback: any) {
      if (!this.mutationRegisterAndLogin.loginSetting.generalLogin.limitLoginNumRule.lockTime) {
        callback(new Error('请输入限制非法登录时间'))
      } else if (
        !this.numberTest.test(
          this.mutationRegisterAndLogin.loginSetting.generalLogin.limitLoginNumRule.lockTime.toString()
        ) ||
        Number(this.mutationRegisterAndLogin.loginSetting.generalLogin.limitLoginNumRule.lockTime) <= 0
      ) {
        callback(new Error('请输入正整数'))
      } else {
        callback()
      }
    }
    //验证表单
    validForm() {
      if (
        OnlineSchoolConfigModule.queryPortal.h5Access &&
        !this.mutationRegisterAndLogin.loginSetting.authSeetingVo.enable &&
        !this.mutationRegisterAndLogin.loginSetting.accountSettingVo.enable
      ) {
        this.$message.error('H5的微信授权登录设置、账号登录设置必须至少有一个是开启，请调整配置。')
        return
      }
      this.loginForm.validate((loginValid: boolean) => {
        this.scanForm.validate((scanValid: boolean) => {
          this.authForm.validate((authValid: boolean) => {
            if (scanValid && authValid && loginValid) {
              // this.sureSaveDialog = true
              this.doSave()
            }
          })
        })
      })
    }

    @bind
    @debounce(200)
    async doSave() {
      this.loading = true
      try {
        if (!this.mutationRegisterAndLogin.loginSetting.scanSeetingVo.enable) {
          this.mutationRegisterAndLogin.loginSetting.scanSeetingVo.appId = undefined
          this.mutationRegisterAndLogin.loginSetting.scanSeetingVo.secret = undefined
        }
        const res = await this.mutationRegisterAndLogin.saveLoginSetting()
        const res1 = await this.mutationRegisterAndLogin.saveCommonLoginSetting()
        const ssoRes = await this.mutationRegisterAndLogin.saveSinglePointSecurityConfig()
        if (res.isSuccess() && res1.isSuccess() && ssoRes.isSuccess()) {
          this.$message.success('保存登录设置成功')
          // this.sureSaveDialog = false
          await this.getLoginConfig()
        } else {
          this.$message.error('保存登录设置失败')
        }
      } catch (error) {
        throw new Error('保存登录设置时发生错误: ' + error)
      } finally {
        this.loading = false
      }

      //保存
    }

    async resetData() {
      //重置数据
      await this.getLoginConfig()
    }

    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
    }
    //获取登录配置
    async getLoginConfig() {
      this.loading = true
      const res = await this.mutationRegisterAndLogin.queryLoginSetting()
      const pwdRes = await this.mutationRegisterAndLogin.getLoginForceModifyPassword()
      const ssoRes = await this.mutationRegisterAndLogin.getSinglePointSecurityConfig()
      if (res.isSuccess() && pwdRes.isSuccess() && ssoRes.isSuccess()) {
        if (
          this.mutationRegisterAndLogin.loginSetting.scanSeetingVo.appId ||
          this.mutationRegisterAndLogin.loginSetting.scanSeetingVo.secret
        ) {
          this.isSaveScan = true
        } else {
          this.isSaveScan = false
        }
        if (
          this.mutationRegisterAndLogin.loginSetting.authSeetingVo.appId ||
          this.mutationRegisterAndLogin.loginSetting.authSeetingVo.secret
        ) {
          this.isSaveAuth = true
        } else {
          this.isSaveAuth = false
        }
      } else {
        this.$message.error('获取登录设置失败')
      }
      this.loading = false
    }

    async created() {
      await this.getLoginConfig()
    }
  }
</script>
