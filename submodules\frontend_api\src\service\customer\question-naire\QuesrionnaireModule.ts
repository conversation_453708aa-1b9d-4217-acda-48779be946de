import Questionnaire from '@api/service/customer/question-naire/Questionnaire'
import QuestionnaireParams from '@api/service/customer/question-naire/models/QuestionnaireParams'
import MsExamQueryFrontGatewayQuestionnaireQueryForeStage, {
  PageQuestionnaireIssueRequest,
  PageQuestionnaireSchemeRequest
} from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryForeStage'
import { Page } from '@hbfe/common'
import ServerTime from '@api/service/common/service-time/ServiceTime'
import { QuestionnaireStatisEnum } from '@api/service/customer/question-naire/enums/QuestionnaireStatisEnum'

/**
 *  问卷模块
 */
export default class QuestionnaireModule {
  /**
   * 筛选项
   */
  params = new QuestionnaireParams()

  /**
   * 列表
   */
  questionnaireList = new Array<Questionnaire>()
  /**
   * 查询问卷列表-期别
   */
  async queryQuestionnaireList(page: Page) {
    const rquest = new PageQuestionnaireIssueRequest()
    rquest.questionnaireId = this.params.questionnaireId
    rquest.qualificationId = this.params.periodQualificationId
    const res = await MsExamQueryFrontGatewayQuestionnaireQueryForeStage.pageQuestionnaireIssueInMyself({
      page,
      rquest
    })
    this.questionnaireList = []
    if (res.status.isSuccess()) {
      await ServerTime.getServiceTime()
      this.questionnaireList = (res.data?.currentPageData || []).map((item) => {
        const vo = Questionnaire.from(item)
        if (item.surveyInformationResponse.questionnaireStartTime > ServerTime.currentServiceTime) {
          vo.questionnaireStatis = QuestionnaireStatisEnum.before
        } else if (
          item.surveyInformationResponse.questionnaireStartTime <= ServerTime.currentServiceTime &&
          item.surveyInformationResponse.questionnaireEndTime >= ServerTime.currentServiceTime
        ) {
          vo.questionnaireStatis = QuestionnaireStatisEnum.in
        } else {
          vo.questionnaireStatis = QuestionnaireStatisEnum.after
        }
        if (item.surveyInformationResponse.status == 2) {
          vo.questionnaireStatis = QuestionnaireStatisEnum.close
        }
        return vo
      })
    }
    page.totalPageSize = res.data?.totalPageSize || 0
    page.totalSize = res.data?.totalSize || 0
    return this.questionnaireList
  }
  /**
   * 查询问卷列表-方案
   */
  async querySchemeQuestionnaireList(page: Page) {
    const rquest = new PageQuestionnaireSchemeRequest()
    rquest.questionnaireId = this.params.questionnaireId
    rquest.qualificationId = this.params.qualificationId
    const res = await MsExamQueryFrontGatewayQuestionnaireQueryForeStage.pageQuestionnaireSchemeInMyself({
      page,
      rquest
    })
    this.questionnaireList = []
    if (res.status.isSuccess()) {
      await ServerTime.getServiceTime()
      this.questionnaireList = res.data?.currentPageData.map((item) => {
        const vo = Questionnaire.from(item)
        if (item.surveyInformationResponse.questionnaireStartTime > ServerTime.currentServiceTime) {
          vo.questionnaireStatis = QuestionnaireStatisEnum.before
        } else if (
          item.surveyInformationResponse.questionnaireStartTime <= ServerTime.currentServiceTime &&
          item.surveyInformationResponse.questionnaireEndTime >= ServerTime.currentServiceTime
        ) {
          vo.questionnaireStatis = QuestionnaireStatisEnum.in
        } else {
          vo.questionnaireStatis = QuestionnaireStatisEnum.after
        }
        if (item.surveyInformationResponse.status == 2) {
          vo.questionnaireStatis = QuestionnaireStatisEnum.close
        }
        return vo
      })
    }
    page.totalPageSize = res.data?.totalPageSize || 0
    page.totalSize = res.data?.totalSize || 0
    return this.questionnaireList
  }
}
