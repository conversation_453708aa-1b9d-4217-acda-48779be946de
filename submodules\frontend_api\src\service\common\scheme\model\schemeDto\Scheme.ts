import AttendanceConfigConfigure from '@api/service/common/scheme/model/schemeDto/attendance-config-configure/AttendanceConfigConfigure'
import TrainingConfigConfigure from '@api/service/common/scheme/model/schemeDto/training-config-configure/TrainingConfigConfigure'
import QuestionnaireLearning from '@api/service/common/scheme/model/schemeDto/common/questionnaire-learning/QuestionnaireLearning'
import IssueConfigure from '@api/service/common/scheme/model/schemeDto/issue-configures/IssueConfigure'
import CommoditySale from '@api/service/common/scheme/model/schemeDto/commodity-sale/CommoditySale'
import AssessSetting from '@api/service/common/scheme/model/schemeDto/common/course-training-outlines/assess-setting/AssessSetting'
import ChooseCourseLearning from '@api/service/common/scheme/model/schemeDto/choose-course-learning/ChooseCourseLearning'
import ExamLearning from '@api/service/common/scheme/model/schemeDto/exam-learning/ExamLearning'
import PracticeLearning from '@api/service/common/scheme/model/schemeDto/practice-learning/PracticeLearning'
import InterestCourseLearning from '@api/service/common/scheme/model/schemeDto/interest-course-learning/InterestCourseLearning'
import LearningExperienceLearning from '@api/service/common/scheme/model/schemeDto/learning-experience-learning/LearningExperienceLearning'
import AutonomousCourseLearning from '@api/service/common/scheme/model/schemeDto/autonomous-course-learning/AutonomousCourseLearning'
import { SchemeLearningTypeEnum } from '@api/service/common/scheme/enum/SchemeLearningType'
import SchemeExtendProperty from '@api/service/common/scheme/model/schemeDto/extend-properties/SchemeExtendProperty'
import SchemeTeachResourceConfigConfigure from '@api/service/common/scheme/model/schemeDto/teach-resource-config-configure/SchemeTeachResourceConfigConfigure'
import TemplateNameManager from '@api/service/management/train-class/mutation/dto/TemplateNameManager'
import { TrainingTypeEnum } from '@api/service/common/scheme/enum/TrainingType'

/**
 * @description 方案dto
 */
class Scheme {
  /**
   * 商品销售信息
   */
  commoditySale: CommoditySale
  /**
   * 方案id
   */
  id: string
  /**
   * 方案名称
   */
  name: string
  /**
   * 是否提供重学
   */
  provideRelearn: boolean
  /**
   * 封面
   */
  picture: string
  /**
   * 方案介绍内容
   */
  comment: string
  /**
   * 报名开始时间
   */
  registerBeginDate: string
  /**
   * 报名结束时间
   */
  registerEndDate: string
  /**
   * 培训开始时间
   */
  trainingBeginDate: string
  /**
   * 培训结束时间
   */
  trainingEndDate: string
  /**
   * 培训须知
   */
  notice: string
  /**
   * 培训期别-通用培训须知
   */
  issueNotice: string
  /**
   * 培训方案简介id
   */
  introId: string
  /**
   * 培训方案简介
   */
  introContent: string
  /**
   * 培训方案类型
   */
  type: SchemeLearningTypeEnum
  /**
   * 选课规则学习方式
   */
  chooseCourseLearning: ChooseCourseLearning
  /**
   * 自主选课学习方式
   */
  autonomousCourseLearning: AutonomousCourseLearning
  /**
   * 考试学习方式
   */
  examLearning: ExamLearning
  /**
   * 练习学习方式
   */
  practiceLearning: PracticeLearning
  /**
   * 兴趣课学习方式
   */
  interestCourseLearning: InterestCourseLearning
  /**
   * 学习心得学习方式
   */
  learningExperienceLearning: LearningExperienceLearning
  /**
   * 考核配置
   */
  assessSetting: AssessSetting[] | AssessSetting
  /**
   * 年度
   */
  year: number
  /**
   * 地区
   */
  region: string
  /**
   * 扩展属性
   */
  extendProperties: SchemeExtendProperty[]
  /**
   * 期别配置
   */
  issueConfigures: IssueConfigure[]
  /**
   * 问卷学习方式
   */
  questionnaireLearning: QuestionnaireLearning[]
  /**
   * 培训配置
   * @description 整个对象不用了，整体考勤配置不用了，具体在每个期别单独配置
   */
  trainingConfigConfigure: TrainingConfigConfigure
  /**
   * 考勤配置
   */
  attendanceConfigConfigure: AttendanceConfigConfigure
  /**
   * 教学资源配置
   */
  teachResourceConfigConfigure: SchemeTeachResourceConfigConfigure
  /**
   * 培训形式，0-线上 1-综合 2-线下
   */
  trainingType: TrainingTypeEnum

  /**
   * 【通用】获取方案考核项配置
   */
  static parseTrainClassAssess(jsonConfig: Scheme): {
    schemeAssessItem: AssessSetting
    issueAssessItem: AssessSetting
  } {
    // 获取考核项配置
    const assessSetting = jsonConfig.assessSetting
    let schemeAssessItem: AssessSetting, issueAssessItem: AssessSetting
    if (Array.isArray(assessSetting)) {
      schemeAssessItem = assessSetting.find((el) => el.name === TemplateNameManager.schemeAssessSettingName)
      issueAssessItem = assessSetting.find((el) => el.name === TemplateNameManager.notRootAssessAssessSettingName)
    } else {
      schemeAssessItem = assessSetting
    }
    return {
      schemeAssessItem,
      issueAssessItem
    }
  }
}

export default Scheme
