/*
 * @Description: 退款订单对账参数
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-06 09:59:49
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-01-17 18:33:46
 */
import { ReturnOrderRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import RefundCheckParam from '@api/service/management/trade/single/checkAccount/query/vo/RefundCheckAccountParam'

export default class RefundCheckAccountParam extends RefundCheckParam {
  /**
   * 商品方案id
   */
  commoditySkuId = ''
  static refurnTo(refundCheckAccountParam: RefundCheckAccountParam) {
    const returnOrderRequest = Object.assign(
      new ReturnOrderRequest(),
      RefundCheckParam.refurnTo(refundCheckAccountParam)
    )
    returnOrderRequest.subOrderInfo.orderInfo.channelTypesList = [1, 5]
    if (refundCheckAccountParam.saleSource || refundCheckAccountParam.saleSource === SaleChannelEnum.self) {
      returnOrderRequest.subOrderInfo.orderInfo.saleChannels = [refundCheckAccountParam.saleSource]
    } else {
      returnOrderRequest.subOrderInfo.orderInfo.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic
      ]
    }
    returnOrderRequest.subOrderInfo.orderInfo.saleChannelName =
      returnOrderRequest.subOrderInfo.orderInfo.saleChannels.includes(SaleChannelEnum.topic)
        ? refundCheckAccountParam.specialSubjectName
        : ''
    returnOrderRequest.returnCommoditySkuIdList = refundCheckAccountParam.commoditySkuId
      ? [refundCheckAccountParam.commoditySkuId]
      : undefined
    if (refundCheckAccountParam.distributorId) {
      returnOrderRequest.distributorId = refundCheckAccountParam.distributorId
    }
    if (refundCheckAccountParam.promotionPortalId && !refundCheckAccountParam.isDistributionExcludePortal) {
      returnOrderRequest.portalId = refundCheckAccountParam.promotionPortalId
    }
    returnOrderRequest.isDistributionExcludePortal = refundCheckAccountParam.isDistributionExcludePortal
    if (refundCheckAccountParam.refundType) {
      returnOrderRequest.basicData.returnOrderTypes = [refundCheckAccountParam.refundType]
    }
    return returnOrderRequest
  }
}
