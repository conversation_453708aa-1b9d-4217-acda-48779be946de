import ServicerSeriesV1Gateway from '@api/ms-gateway/ms-servicer-series-v1'
import { ResponseStatus } from '@hbfe/common'
class QueryStudentRegisterToken {
  /**
   * 查询注册配置的加密token 在提交注册之前获取、修改学员信息
   * @return registerSetting
   */
  studentFieldVerifyToken = ''

  async queryStudentFieldVerifyToken(): Promise<ResponseStatus> {
    const res = await ServicerSeriesV1Gateway.getStudentRegisterFormConstraint()
    if (res.status.isSuccess()) {
      this.studentFieldVerifyToken = res.data?.token
    }
    return res.status
  }
}
export default new QueryStudentRegisterToken()
