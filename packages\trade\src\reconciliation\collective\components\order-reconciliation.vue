<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <!--条件查询-->
      <template
        v-if="$hasPermission('orderReconciliation,orderReconciliationfx,orderReconciliationzt')"
        desc="orderReconciliation:退款订单对账,orderReconciliationfx:分销退款订单对账,orderReconciliationzt:专题退款订单对账"
        actions="orderReconciliation:doSearch,exportDataty,@BizDistributorSelect,@BizPortalSelect#orderReconciliationfx:doSearchfx,exportDatafx,@BizPortalDistributorSelect#orderReconciliationzt:doSearchzt,exportDatazt,@BizDistributorSelect,@BizPortalSelect"
      >
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="收款账号" v-if="!isZtlogin">
                <el-input
                  id="input"
                  v-model="accountName"
                  clearable
                  placeholder="请选择收款账号"
                  @focus="editInvoicePopup()"
                />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="批次号">
                <el-input v-model="checkAccountParam.orderId" clearable placeholder="请输入批次号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="交易流水号">
                <el-input v-model="checkAccountParam.batchId" clearable placeholder="请输入交易流水号" />
              </el-form-item>
            </el-col>

            <el-form-item label="销售渠道" v-if="!isFxlogin && !isZtlogin">
              <el-select v-model="checkAccountParam.saleSource" clearable filterable placeholder="请选择销售渠道">
                <el-option
                  v-for="item in saleChannelList"
                  :key="item.code"
                  :value="item.code"
                  :label="item.desc"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-col :sm="12" :md="8" :xl="6" v-if="topPicNameFilterShow && !isFxlogin">
              <el-form-item label="专题名称">
                <el-input v-model="checkAccountParam.specialSubjectName" clearable placeholder="请输入专题进行查询" />
              </el-form-item>
            </el-col>

            <!--  v-if="isFXshow" -->
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="分销商">
                <biz-distributor-select
                  v-model="checkAccountParam.distributorId"
                  :name="distributorName"
                ></biz-distributor-select> </el-form-item
            ></el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
                <biz-portal-select
                  v-model="checkAccountParam.promotionPortalId"
                  :name="promotionPortalName"
                ></biz-portal-select> </el-form-item
            ></el-col>
            <el-form-item v-if="isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
              <biz-portal-distributor-select
                v-model="checkAccountParam.promotionPortalId"
                :name="promotionPortalName"
              ></biz-portal-distributor-select>
            </el-form-item>
            <!--  v-if="isFXshow" -->

            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="交易成功时间">
                <double-date-picker
                  :begin-create-time.sync="checkAccountParam.startDate"
                  :end-create-time.sync="checkAccountParam.endDate"
                ></double-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="缴费方式">
                <payment-mode v-model="checkAccountParam.paymentMethod"></payment-mode>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary" @click="search">查询</el-button>
                <el-button @click="exportData">导出列表数据</el-button>
                <el-button @click="reset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert">
            <div class="f-c6">
              当前共有 <span class="f-fb f-co">{{ orderCount }}</span> 笔订单，交易总额
              <span class="f-fb f-co">¥ {{ amount }}</span
              >。
            </div>
          </el-alert>
        </div>
        <!--表格-->
        <el-table
          stripe
          :data="checkAccountListResponse"
          max-height="500px"
          v-loading="query.loading"
          @sort-change="sortChange"
          class="m-table f-mt10"
          ref="batchRef"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="集体报名批次号" min-width="220" fixed="left">
            <template slot-scope="scope">
              <hb-copy :content="scope.row.orderId"></hb-copy>
              {{ scope.row.orderId }}
              <p>
                <el-tag type="success" size="mini" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="交易流水号" prop="batchId" min-width="300"> </el-table-column>
          <el-table-column label="缴费方式" min-width="120" align="center">
            <template slot-scope="scope">{{ PaymentMethod.map.get(scope.row.paymentMethod) }}</template>
          </el-table-column>
          <el-table-column label="交易成功时间" prop="startDate" min-width="180" sortable> </el-table-column>
          <el-table-column label="购买人信息" min-width="240">
            <template slot-scope="scope">
              <p>姓名：{{ scope.row.name || '-' }}</p>
              <!-- <p>证件号：{{ scope.row.idCard || '-' }}</p> -->
              <p>手机号：{{ scope.row.phone || '-' }}</p>
            </template>
          </el-table-column>
          <el-table-column label="实付金额(元)" width="140" align="right">
            <template slot-scope="scope">
              <div>{{ scope.row.money }}</div>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务管理-集体报名对账</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
          </div>
        </el-dialog>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
        <template v-if="$hasPermission('editInvoicePopup')" desc="选择收款账号" actions="@accountNumber">
          <account-number
            :visible.sync="editInvoiceDialog"
            ref="accountNumberRef"
            :get-data="getData"
            @getAccountNumber="getAccountNumber"
          ></account-number>
        </template>
      </template>
    </el-card>
  </div>
</template>

<script lang="ts">
  import ReceiveAccountVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountVo'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { Query, UiPage } from '@hbfe/common'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'

  import { BatchOrderSortRequest, SortPolicy } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import MutationCheckAccountInTrainingChannel from '@api/service/management/trade/batch/checkAccount/mutation/MutationCheckAccountInTrainingChannel'
  import QueryCheckAccount from '@api/service/management/trade/batch/checkAccount/query/QueryCheckAccount'
  import QueryCheckAccountInTrainingChannel from '@api/service/management/trade/batch/checkAccount/query/QueryCheckAccountInTrainingChannel'
  import CheckAccountListResponse from '@api/service/management/trade/batch/checkAccount/query/vo/CheckAccountListResponse'
  import CheckAccountParam from '@api/service/management/trade/batch/checkAccount/query/vo/CheckAccountParam'
  import PaymentMethod from '@api/service/management/trade/batch/order/enum/PaymentMethod'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import PaymentMode from '@hbfe/jxjy-admin-trade/src/order/collective/components/payment-mode.vue'
  import accountNumber from '@hbfe/jxjy-admin-trade/src/refund/components/account-number.vue'

  @Component({
    components: {
      DoubleDatePicker,
      accountNumber,
      BizPortalSelect,
      BizDistributorSelect,
      BizPortalDistributorSelect,
      PaymentMode
    }
  })
  export default class extends Vue {
    @Ref('accountNumberRef') accountNumberRef: any
    SaleChannelEnum = SaleChannelEnum

    select = ''
    input = ''
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    page: UiPage
    query: Query = new Query()
    form = {
      data1: ''
    }
    accountName = ''
    getData = new ReceiveAccountVo()
    editInvoiceDialog = false
    // 缴费方式枚举
    PaymentMethod = PaymentMethod
    queryCheckAccount: QueryCheckAccount = TradeModule.batchTradeBatchFactor.checkAccountFactor.queryCheckAccount
    checkAccountListResponse: Array<CheckAccountListResponse> = new Array<CheckAccountListResponse>()
    //入参
    checkAccountParam: CheckAccountParam = new CheckAccountParam()

    sortRequest: Array<BatchOrderSortRequest> = new Array<BatchOrderSortRequest>()
    //导出成功弹窗
    exportSuccessVisible = false
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable
    promotionPortalName = ''
    distributorName = ''
    queryZtReconciliation = new QueryCheckAccountInTrainingChannel()
    mutationZtCheckAccount = new MutationCheckAccountInTrainingChannel()

    // 获取销售渠道列表
    saleChannelList = [
      {
        code: SaleChannelEnum.self,
        desc: '网校'
      },
      {
        code: SaleChannelEnum.distribution,
        desc: '分销'
      },
      {
        code: SaleChannelEnum.topic,
        desc: '专题'
      }
    ]

    // 专题名称筛选显示
    get topPicNameFilterShow() {
      return (
        this.checkAccountParam.saleSource === SaleChannelEnum.topic ||
        (!this.checkAccountParam.saleSource && this.checkAccountParam.saleSource !== SaleChannelEnum.self)
      )
    }
    get orderCount() {
      if (!this.isZtlogin) {
        return this.queryCheckAccount.orderCount
      } else {
        return this.queryZtReconciliation.orderCount
      }
    }
    get amount() {
      if (!this.isZtlogin) {
        return this.queryCheckAccount.amount
      } else {
        return this.queryZtReconciliation.amount
      }
    }
    constructor() {
      super()
      if (this.isFxlogin && this.isHadFxAbility) {
        this.page = new UiPage(this.doSearchfx, this.doSearchfx)
      } else if (this.isZtlogin) {
        this.page = new UiPage(this.doSearchzt, this.doSearchzt)
      } else {
        this.page = new UiPage(this.doSearch, this.doSearch)
      }
    }

    async doSearch() {
      this.query.loading = true
      try {
        this.checkAccountListResponse = await this.queryCheckAccount.queryOfRegistrationOrder(
          this.page,
          this.checkAccountParam
        )
        //处理切换页数后行数错位问题
        ;(this.$refs['batchRef'] as any)?.doLayout()
      } catch (e) {
        console.log(e, '集体报名对账列表获取失败')
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['batchRef'] as any)?.doLayout()
        this.query.loading = false
      }
    }

    async doSearchfx() {
      this.query.loading = true
      try {
        this.checkAccountListResponse = await this.queryCheckAccount.queryOfFxRegistrationOrder(
          this.page,
          this.checkAccountParam
        )
        //处理切换页数后行数错位问题
        ;(this.$refs['batchRef'] as any)?.doLayout()
      } catch (e) {
        console.log(e, '集体报名对账列表获取失败')
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['batchRef'] as any)?.doLayout()
        this.query.loading = false
      }
    }
    async doSearchzt() {
      this.query.loading = true
      try {
        this.checkAccountListResponse = await this.queryZtReconciliation.queryOfRegistrationOrder(
          this.page,
          this.checkAccountParam
        )
        //处理切换页数后行数错位问题
        ;(this.$refs['batchRef'] as any)?.doLayout()
      } catch (e) {
        console.log(e, '集体报名对账列表获取失败')
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['batchRef'] as any)?.doLayout()
        this.query.loading = false
      }
    }
    async exportData() {
      try {
        let res
        if (this.isFxlogin && this.isHadFxAbility) {
          res = await this.exportDatafx()
        } else if (this.isZtlogin) {
          res = await this.exportDatazt()
        } else {
          res = await this.exportDataty()
        }

        if (res) {
          this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }
    async exportDataty() {
      return await TradeModule.batchTradeBatchFactor.checkAccountFactor.mutationCheckAccount.listExport(
        this.checkAccountParam
      )
    }

    async exportDatafx() {
      return await TradeModule.batchTradeBatchFactor.checkAccountFactor.mutationCheckAccount.listFxExport(
        this.checkAccountParam
      )
    }
    async exportDatazt() {
      return await this.mutationZtCheckAccount.listExport(this.checkAccountParam)
    }
    // 请求
    async search() {
      this.page.pageNo = 1
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doSearchfx()
      } else if (this.isZtlogin) {
        await this.doSearchzt()
      } else {
        await this.doSearch()
      }
    }
    async reset() {
      // this.questionRequestVo = new QuestionRequestVo()
      this.page.pageNo = 1
      this.accountName = ''
      this.promotionPortalName = ''
      this.distributorName = ''
      this.checkAccountParam = new CheckAccountParam()
      await this.search()
    }
    sortChange(column: any) {
      console.log(column.order, 1)
      this.sortRequest = []
      const item = new BatchOrderSortRequest()
      if (column.order === 'ascending') {
        //正序
        item.policy = 'ASC' as SortPolicy
        this.sortRequest.push(item)
      } else if (column.order === 'descending') {
        item.policy = 'DESC' as SortPolicy
        this.sortRequest.push(item)
      } else {
        this.sortRequest = []
      }
    }
    async editInvoicePopup() {
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.accountNumberRef.doQueryPagefx()
      } else {
        await this.accountNumberRef.doQueryPage()
      }
      const inputEl = document.getElementById('input')
      inputEl.blur()
      this.editInvoiceDialog = true
    }

    getAccountNumber(idList: ReceiveAccountVo[]) {
      this.getData = idList[0]
      this.checkAccountParam.paymentAccountID = this.getData?.id ? this.getData.id : ''
      this.accountName = this.getData?.accountName
    }
    @Watch('accountName', {
      deep: true,
      immediate: true
    })
    accountNameChange() {
      if (this.accountName === '') {
        this.getAccountNumber([])
      }
    }
    //前往下载
    goDownloadPage() {
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportBatchReconciliation' }
      })
      this.exportSuccessVisible = false
    }
    async created() {
      await this.search()
    }
  }
</script>
