<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <div slot="header" class="">
      <span class="tit-txt">学习有效期配置</span>
    </div>
    <div class="m-sub-tit is-border-bottom">
      <span class="tit-txt">学习时间配置</span>
    </div>
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :md="20" :lg="16" :xl="13">
        <el-form ref="stageTrainingForm" :model="commodityInfo" :label-width="labelWidth" class="m-form f-mt20">
          <el-form-item label="学习起止时间：" required>
            <el-radio-group v-model="isShortTermTraining" @change="handleIsShortTermTraining">
              <el-radio v-for="(item, index) in uiOptionGroup.trainingTime" :key="index" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <!--选择 指定培训时间 后出现-->
          <el-form-item label="选择时间：" v-if="isShortTermTraining">
            <!--选中后出现输入框-->
            <el-date-picker
              v-model="commodityInfo.trainClassBaseInfo.trainingBeginDate"
              :value-format="'yyyy-MM-dd HH:mm:ss'"
              type="datetime"
              placeholder="请选择学习开始时间"
              class="f-ml10"
            >
            </el-date-picker>
            &nbsp;&nbsp;--
            <el-date-picker
              v-model="commodityInfo.trainClassBaseInfo.trainingEndDate"
              :value-format="'yyyy-MM-dd HH:mm:ss'"
              type="datetime"
              placeholder="请选择学习结束时间"
              class="f-ml10"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <div class="m-sub-tit is-border-bottom">
      <span class="tit-txt">销售配置</span>
    </div>
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :md="20" :lg="16" :xl="13">
        <el-form ref="stageSaleForm" :model="commodityInfo" :label-width="labelWidth" class="m-form f-mt20">
          <el-form-item label="展示在门户：" required>
            <el-radio-group v-model="isCustomerVisible" @change="handleIsCustomerVisible">
              <el-radio :label="true">
                展示
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-info m-tooltip-icon f-c9"></i>
                  <div slot="content">
                    配置培训方案是否展示在网校报名培训列表。展示门户分为学员门户报名列表和集体报名管理员查看培训班，设置为集体报名管理员可见，只生效于有开启线上集体报名的网校。<br />
                    不展示门户的情况下，如该方案有纳入专题，则会显示在专题门户上。默认为专题学员门户报名列表和集体报名管理员查看专题内培训班。集体报名管理员仅生效有开启线上集体报名的网校。
                  </div>
                </el-tooltip>
              </el-radio>
              <el-radio :label="false">不展示门户</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="展示用户：" v-if="isCustomerVisible">
            <el-checkbox-group v-model="commodityInfo.visibleChannelList">
              <el-checkbox name="type" :label="1">学员门户可见</el-checkbox>
              <el-checkbox name="type" :label="2">集体报名管理员可见</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <!--          <el-form-item>-->
          <!--            <span slot="label">开放学员报名<slot name="sales-configuration-slot"></slot>：</span>-->
          <!--            <el-checkbox v-model="commodityInfo.closeCustomerPurchase">-->
          <!--              不开放-->
          <!--              <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">-->
          <!--                <i class="el-icon-info m-tooltip-icon f-c9"></i>-->
          <!--                <div slot="content">不开放学员报名，仅支持导入开通的方式报班。</div>-->
          <!--              </el-tooltip>-->
          <!--            </el-checkbox>-->
          <!--          </el-form-item>-->
          <el-form-item required>
            <span slot="label">方案上架状态<slot name="sales-configuration-slot"></slot></span>
            <el-radio-group v-model="commodityInfo.onShelves" @change="handleIsOpenSchemeNow">
              <el-radio v-for="(item, index) in uiOptionGroup.openSchemeTime" :key="index" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="方案计划上架时间：" v-if="!commodityInfo.onShelves">
            <!--选中后出现输入框-->
            <el-date-picker
              v-model="commodityInfo.onShelvesPlanTime"
              :value-format="'yyyy-MM-dd HH:mm:ss'"
              :disabled="commodityInfo.onShelves"
              :picker-options="isDisabled"
              type="datetime"
              placeholder="请输入方案计划上架时间"
              class="f-ml10"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="方案计划下架时间：">
            <el-date-picker
              v-model="commodityInfo.offShelvesPlanTime"
              :value-format="'yyyy-MM-dd HH:mm:ss'"
              :picker-options="isDisabled"
              type="datetime"
              placeholder="请输入方案计划下架时间"
              class="f-ml10"
            >
            </el-date-picker>
          </el-form-item>
          <!--          <el-form-item label="展示在门户：" required>-->
          <!--            <el-radio-group v-model="isCustomerVisible" @change="handleIsCustomerVisible">-->
          <!--              <el-radio :label="true">-->
          <!--                展示-->
          <!--                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">-->
          <!--                  <i class="el-icon-info m-tooltip-icon f-c9"></i>-->
          <!--                  <div slot="content">-->
          <!--                    配置培训方案是否展示在网校报名培训列表。展示门户分为学员门户报名列表和集体报名管理员查看培训班，设置为集体报名管理员可见，只生效于有开启线上集体报名的网校。<br />-->
          <!--                    不展示门户的情况下，如该方案有纳入专题，则会显示在专题门户上。默认为专题学员门户报名列表和集体报名管理员查看专题内培训班。集体报名管理员仅生效有开启线上集体报名的网校。-->
          <!--                  </div>-->
          <!--                </el-tooltip>-->
          <!--              </el-radio>-->
          <!--              <el-radio :label="false">不展示门户</el-radio>-->
          <!--            </el-radio-group>-->
          <!--            <div class="bg-gray f-mt10 f-plr15 f-pt5 f-pb5" v-if="isCustomerVisible">-->
          <!--              <div class="f-flex">-->
          <!--                <span><i class="f-ci f-mr5">*</i>展示用户：</span>-->
          <!--                <el-checkbox-group v-model="commodityInfo.visibleChannelList" style="display: inline-block">-->
          <!--                  <el-checkbox name="type" :disabled="routerMode === 3" :label="1">学员门户可见</el-checkbox>-->
          <!--                  <el-checkbox name="type" disabled :label="2">集体报名管理员可见</el-checkbox>-->
          <!--                </el-checkbox-group>-->
          <!--              </div>-->
          <!--            </div>-->
          <!--          </el-form-item>-->
          <template v-if="trainingType">
            <el-form-item label="开放学员报名：">
              <el-checkbox v-model="commodityInfo.closeCustomerPurchase">
                不开放
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-info m-tooltip-icon f-c9"></i>
                  <div slot="content">不开放学员报名，仅支持导入开通的方式报班。</div>
                </el-tooltip>
              </el-checkbox>
            </el-form-item>
            <el-form-item required>
              <span slot="label">配置开启报名时间<slot name="sales-configuration-slot"></slot>：</span>
              <el-radio-group v-model="isOpenSignUpNow" @change="handleIsOpenSignUpNow">
                <el-radio :label="true">立即开启</el-radio>
                <el-radio :label="false">
                  指定开启时间
                  <!--选中后出现输入框-->
                  <el-date-picker
                    v-model="commodityInfo.trainClassBaseInfo.registerBeginDate"
                    :value-format="'yyyy-MM-dd HH:mm:ss'"
                    :disabled="isOpenSignUpNow"
                    :picker-options="isDisabled"
                    type="datetime"
                    placeholder="请输入指定开启时间"
                    class="f-ml10"
                  >
                  </el-date-picker>
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item required>
              <span slot="label">关闭报名时间<slot name="sales-configuration-slot"></slot>：</span>

              <el-radio-group v-model="isCloseSignUp" @change="handleIsCloseSignUp">
                <el-radio :label="false">无关闭时间</el-radio>
                <el-radio :label="true">
                  指定关闭时间
                  <!--选中后出现输入框-->
                  <el-date-picker
                    v-model="commodityInfo.trainClassBaseInfo.registerEndDate"
                    :value-format="'yyyy-MM-dd HH:mm:ss'"
                    :disabled="!isCloseSignUp"
                    :picker-options="isDisabled"
                    type="datetime"
                    placeholder="请输入指定关闭时间"
                    class="f-ml10"
                  >
                  </el-date-picker>
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
          <el-form-item
            label="期别报名信息："
            required
            v-if="
              commodityInfo.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId !== TrainingModeEnum.online
            "
          >
            本方案下已添加{{ commodityInfo.learningTypeModel.issue.issueConfigList.length }}个期别<el-button
              type="text"
              class="f-ml10"
              @click="drawerConfig.signUpIssueDrawer = true"
              >[查看详情]</el-button
            >
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <div class="m-sub-tit is-border-bottom">
      <span class="tit-txt">报名费用</span>
    </div>
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :md="20" :lg="16" :xl="13">
        <el-form ref="stagePriceForm" :model="commodityInfo" :label-width="labelWidth" class="m-form f-mt20">
          <el-form-item label="培训费：">
            <el-input-number :precision="2" :min="0" v-model="commodityInfo.price"></el-input-number>元 / 人
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <sign-up-issue
      :issueList="commodityInfo.learningTypeModel.issue.issueConfigList"
      :sign-up-issue-drawer.sync="drawerConfig.signUpIssueDrawer"
    ></sign-up-issue>
  </el-card>
</template>

<script lang="ts">
  import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator'
  import moment from 'moment'
  import MutationCreateTrainClassCommodity from '@api/service/management/train-class/mutation/MutationCreateTrainClassCommodity'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import SignUpIssue from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/sign-up-issue.vue'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import RootModule from '@/store/RootModule'

  @Component({
    components: { SignUpIssue }
  })
  export default class extends Vue {
    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number

    /**
     * 商品信息 - 双向绑定
     */
    @PropSync('commodity', { type: Object })
    commodityInfo!: MutationCreateTrainClassCommodity
    /**
     * 抽屉显隐
     */
    drawerConfig = {
      signUpIssueDrawer: false
    }
    // 是否指定培训时间，默认是
    isShortTermTraining = true
    // 是否展示在门户，默认是
    isCustomerVisible = true
    // 报名是否立即开启，默认是
    isOpenSignUpNow = true
    // 是否指定关闭时间，默认否
    isCloseSignUp = false
    // UI选项组
    uiOptionGroup = {
      // 学习起止时间
      trainingTime: [
        { label: '指定培训时间', value: true },
        { label: '长期有效', value: false }
      ],
      // 开启方案时间-仅控制方案是否立即上架，不与方案报名时间的配置关联
      openSchemeTime: [
        { label: '立即上架', value: true },
        { label: '暂不上架', value: false }
      ],
      // 报名时间的配置-只控制方案是否能够报名与方案是否上架无关联
      openSignUpTime: [
        { label: '配置开启报名时间', value: true },
        { label: '暂不开启', value: false }
      ]
    }

    /**
     * 宽度
     */
    labelWidth = '150px'

    // 设置时间选择范围
    isDisabled = {
      disabledDate(time: Date) {
        if (!time) {
          return false
        }
        // 大于某个日期不能选择
        const myDate = new Date(moment().format('YYYY-MM-DD 00:00:00'))
        return time.getTime() < myDate.getTime()
      }
    }
    TrainingModeEnum = TrainingModeEnum
    // 学习时间配置
    @Ref('stageForm') stageForm: any

    get openCustomerPurchase() {
      return !this.commodityInfo.closeCustomerPurchase
    }

    /**
     * 培训方案培训形式
     */
    get trainingType() {
      return (
        this.commodityInfo.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId === TrainingModeEnum.online
      )
    }

    async created() {
      // 编辑/复制
      if (this.routerMode !== 1) {
        this.initConfiguration()
      }
      // 创建
      if (this.routerMode === 1) {
        this.commodityInfo.visibleChannelList = [1, 2, 3]
        this.commodityInfo.closeCustomerPurchase = false
      }
    }

    /**
     * 加载配置项
     */
    initConfiguration() {
      // 有配置培训起止时间的，说明是短期培训
      const trainingBeginDate = this.commodityInfo.trainClassBaseInfo.trainingBeginDate
      const trainingEndDate = this.commodityInfo.trainClassBaseInfo.trainingEndDate
      if (trainingBeginDate && trainingEndDate) {
        if (
          CreateSchemeUtils.checkTimeValidity(trainingBeginDate) &&
          CreateSchemeUtils.checkTimeValidity(trainingEndDate)
        ) {
          this.isShortTermTraining = true
        } else {
          this.isShortTermTraining = false
          this.commodityInfo.trainClassBaseInfo.trainingBeginDate = CreateSchemeUtils.defaultBeginDate
          this.commodityInfo.trainClassBaseInfo.trainingEndDate = CreateSchemeUtils.defaultEndDate
        }
      }
      // 配置开启报名时间或关闭报名时间，表明配置报名时间范围
      const registerBeginDate = this.commodityInfo.trainClassBaseInfo.registerBeginDate
      const registerEndDate = this.commodityInfo.trainClassBaseInfo.registerEndDate
      if (registerBeginDate || registerEndDate) {
        // 配置开启报名时间，表明不是立即开启报名
        if (registerBeginDate) {
          this.isOpenSignUpNow = false
        }
        // 配置关闭报名时间，表明指定关闭时间
        if (registerEndDate) {
          this.isCloseSignUp = true
        }
      }
      if (registerBeginDate && this.routerMode === 2) {
        if (new Date().getTime() > Date.parse(registerBeginDate)) {
          this.commodityInfo.trainClassBaseInfo.registerBeginDate = ''
          this.isOpenSignUpNow = true
        }
      }
      // 回显方案计划上下架时间
      // const onShelvesPlanTime = this.commodityInfo.onShelvesPlanTime
      // const offShelvesPlanTime = this.commodityInfo.offShelvesPlanTime
      // if (onShelvesPlanTime && offShelvesPlanTime) {
      //   this.commodityInfo.onShelves = true
      // }
      // 读取展示在门户；学员、集体报名管理员可见配置
      const visibleChannelList = this.commodityInfo.visibleChannelList.slice()
      this.isCustomerVisible = visibleChannelList.includes(1) || visibleChannelList.includes(2)
    }

    /**
     * 修改方案起止时间
     */
    openScheme(val: boolean) {
      if (!val) {
        this.commodityInfo.onShelves = true
        this.commodityInfo.onShelvesPlanTime = ''
        this.commodityInfo.offShelvesPlanTime = ''
      }
    }

    /**
     * 修改学习起止时间
     */
    // openSignUp(val: boolean) {
    //   if (!val) {
    //     this.isOpenSignUpNow = true
    //     this.commodityInfo.trainClassBaseInfo.registerBeginDate = ''
    //     this.isCloseSignUp = false
    //     this.commodityInfo.trainClassBaseInfo.registerEndDate = ''
    //   }
    // }

    /**
     * 修改学习起止时间
     */
    handleIsShortTermTraining(val: boolean) {
      if (val) {
        this.commodityInfo.trainClassBaseInfo.trainingBeginDate = ''
        this.commodityInfo.trainClassBaseInfo.trainingEndDate = ''
      } else {
        this.commodityInfo.trainClassBaseInfo.trainingBeginDate = CreateSchemeUtils.defaultBeginDate
        this.commodityInfo.trainClassBaseInfo.trainingEndDate = CreateSchemeUtils.defaultEndDate
      }
    }

    /**
     * 是否开启报名响应事件
     */
    handleIsCustomerVisible(val: number) {
      if (val) {
        this.commodityInfo.visibleChannelList = [1, 2, 3]
        this.commodityInfo.closeCustomerPurchase = false
      } else {
        this.commodityInfo.visibleChannelList = []
        this.commodityInfo.closeCustomerPurchase = true
      }
    }

    /**
     * 切换是否立即方案响应
     */
    handleIsOpenSchemeNow(val: number) {
      if (val) {
        this.commodityInfo.onShelvesPlanTime = ''
        // this.commodityInfo.offShelvesPlanTime = ''
      }
    }

    /**
     * 切换是否立即报名响应
     */
    handleIsOpenSignUpNow(val: number) {
      if (val) {
        this.commodityInfo.onShelves = true
        this.commodityInfo.trainClassBaseInfo.registerBeginDate = ''
      }
    }

    /**
     * 切换是否无关闭时间响应
     */
    handleIsCloseSignUp(val: number) {
      if (!val) {
        this.commodityInfo.trainClassBaseInfo.registerEndDate = ''
      }
    }

    /**
     * 表单校验 - 学习有效期配置
     */
    validateForm() {
      let isValid = false
      const myDate = Date.now()
      // 配置开启报名时间或关闭报名时间，表明配置报名时间范围
      const compareOpenTime = new Date(this.commodityInfo.trainClassBaseInfo.registerBeginDate).getTime()
      const compareCloseTime = new Date(this.commodityInfo.trainClassBaseInfo.registerEndDate).getTime()

      if (this.isOpenSignUpNow) {
        this.isOpenSignUpNow = false
        const date = this.$moment(new Date(new Date().getTime() + 60 * 1000)).format('YYYY-MM-DD HH:mm:ss')
        this.commodityInfo.trainClassBaseInfo.registerBeginDate = date
      }

      if (this.isShortTermTraining) {
        if (
          !this.commodityInfo.trainClassBaseInfo.trainingBeginDate ||
          !this.commodityInfo.trainClassBaseInfo.trainingEndDate
        ) {
          this.$message.error('请选择学习起止时间')
          return isValid
        }
      }

      // 配置开启方案时间或关闭方案时间，表明配置方案开启时间范围-改为非必填无需校验当前时间
      const openPlanTime = new Date(this.commodityInfo.onShelvesPlanTime).getTime()
      const closePlanTime = new Date(this.commodityInfo.offShelvesPlanTime).getTime()
      if (!this.commodityInfo.onShelves && closePlanTime && !openPlanTime) {
        this.$message.error('请选择方案计划上架时间')
        return isValid
      }
      console.log(this.commodityInfo.onShelves, 'xxxx')
      if (
        !this.commodityInfo.onShelves &&
        openPlanTime &&
        openPlanTime < myDate &&
        !(
          this.routerMode === 3 &&
          this.commodityInfo.onShelvesPlanTime === this.commodityInfo.timeConfig?.onShelvesPlanTime
        )
      ) {
        this.$message.error('方案计划上架时间必须大于当前时间')
        return isValid
      }
      if (
        closePlanTime &&
        closePlanTime < myDate &&
        !(
          this.routerMode === 3 &&
          this.commodityInfo.offShelvesPlanTime === this.commodityInfo.timeConfig?.offShelvesPlanTime
        )
      ) {
        this.$message.error('方案计划下架时间必须大于当前时间')
        return isValid
      }

      if (!this.isOpenSignUpNow && !this.commodityInfo.trainClassBaseInfo.registerBeginDate && this.trainingType) {
        this.$message.error('请选择报名开启时间')
        return isValid
      }
      if (this.isCloseSignUp && !this.commodityInfo.trainClassBaseInfo.registerEndDate && this.trainingType) {
        this.$message.error('请选择报名关闭时间')
        return isValid
      }
      if (
        !this.isOpenSignUpNow &&
        compareOpenTime < myDate &&
        this.trainingType &&
        !(
          this.routerMode === 3 &&
          this.commodityInfo.trainClassBaseInfo.registerBeginDate === this.commodityInfo.timeConfig?.registerBeginDate
        )
      ) {
        this.$message.error('指定开启时间必须大于当前时间')
        return isValid
      }
      if (
        this.isCloseSignUp &&
        compareCloseTime < myDate &&
        !(
          this.routerMode === 3 &&
          this.commodityInfo.trainClassBaseInfo.registerEndDate === this.commodityInfo.timeConfig?.registerEndDate
        )
      ) {
        this.$message.error('指定关闭时间必须大于当前时间')
        return isValid
      }

      const trainingBeginDate = this.commodityInfo.trainClassBaseInfo?.trainingBeginDate || ''
      const trainingEndDate = this.commodityInfo.trainClassBaseInfo?.trainingEndDate || ''
      const onShelvesPlanTime = this.commodityInfo.onShelvesPlanTime || ''
      const offShelvesPlanTime = this.commodityInfo.offShelvesPlanTime || ''
      const registerBeginDate = this.commodityInfo.trainClassBaseInfo?.registerBeginDate || ''
      const registerEndDate = this.commodityInfo.trainClassBaseInfo?.registerEndDate || ''
      let validResult, resultOne, resultTwo
      if (trainingBeginDate && trainingEndDate) {
        validResult = CreateSchemeUtils.validTwoDateOrder(trainingBeginDate, trainingEndDate)
        if (!validResult) {
          this.$message.error('学习开始时间大于学习结束时间，请调整！')
          return isValid
        }
      }
      if (registerBeginDate && registerEndDate) {
        validResult = CreateSchemeUtils.validTwoDateOrder(registerBeginDate, registerEndDate)
        if (!validResult) {
          this.$message.error('班级报名关闭时间必须大于班级报名开启时间')
          return isValid
        }
      }
      if (onShelvesPlanTime && offShelvesPlanTime) {
        validResult = CreateSchemeUtils.validTwoDateOrder(onShelvesPlanTime, offShelvesPlanTime)
        if (!validResult) {
          this.$message.error('方案计划下架时间必须大于方案计划上架时间')
          return isValid
        }
      }
      if (trainingBeginDate && trainingEndDate && registerEndDate) {
        resultTwo = CreateSchemeUtils.validTwoDateOrder(registerEndDate, trainingEndDate)
        validResult = resultTwo
        if (!validResult) {
          this.$message.error('报名关闭时间，不能晚于培训结束时间。')
          return isValid
        }
      }
      if (trainingBeginDate && trainingEndDate && registerBeginDate) {
        resultTwo = CreateSchemeUtils.validTwoDateOrder(registerBeginDate, trainingEndDate)
        validResult = resultTwo
        if (!validResult) {
          this.$message.error('班级报名开启时间必须小于学习结束时间')
          return isValid
        }
      }
      if (
        this.commodityInfo.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId === TrainingModeEnum.online
      ) {
        if (this.commodityInfo.onShelvesPlanTime) {
          if (
            !registerBeginDate ||
            !CreateSchemeUtils.validTwoDateOrder(this.commodityInfo.onShelvesPlanTime, registerBeginDate)
          ) {
            this.$message.error('方案报名时段不在方案上下架期间，请调整')
            return isValid
          }
        }
        if (this.commodityInfo.offShelvesPlanTime) {
          if (
            !registerEndDate ||
            !CreateSchemeUtils.validTwoDateOrder(registerEndDate, this.commodityInfo.offShelvesPlanTime)
          ) {
            this.$message.error('方案报名时段不在方案上下架期间，请调整')
            return isValid
          }
        }
      }
      isValid = true
      return isValid
    }
  }
</script>
