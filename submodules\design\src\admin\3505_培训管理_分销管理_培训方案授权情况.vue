<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--培训方案授权情况-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">培训方案授权情况</el-button>
        <el-drawer
          title="培训方案授权情况"
          :visible.sync="dialog2"
          size="90%"
          :append-to-body="true"
          custom-class="m-drawer m-table-auto"
        >
          <div class="drawer-bd">
            <el-row :gutter="16" class="m-query">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训方案名称">
                    <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训方案类型">
                    <el-select v-model="select" clearable placeholder="请选择培训方案类型">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="年度">
                    <el-select v-model="select" clearable placeholder="请选择年度">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <!--<el-col :sm="12" :md="8" :xl="6">-->
                <!--  <el-form-item label="地区">-->
                <!--    <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" />-->
                <!--  </el-form-item>-->
                <!--</el-col>-->
                <!--<el-col :sm="12" :md="8" :xl="6">-->
                <!--  <el-form-item label="行业">-->
                <!--    <el-select v-model="select" clearable placeholder="请选择行业">-->
                <!--      <el-option value="选项1"></el-option>-->
                <!--      <el-option value="选项2"></el-option>-->
                <!--    </el-select>-->
                <!--  </el-form-item>-->
                <!--</el-col>-->
                <!--选择行业后展示原型上对应的内容-->
                <!--<el-col :sm="12" :md="8" :xl="6">-->
                <!--  <el-form-item label="科目类型">-->
                <!--    <el-select v-model="select" clearable placeholder="请选择行业">-->
                <!--      <el-option value="选项1"></el-option>-->
                <!--      <el-option value="选项2"></el-option>-->
                <!--    </el-select>-->
                <!--  </el-form-item>-->
                <!--</el-col>-->
                <!--<el-col :sm="12" :md="8" :xl="6">-->
                <!--  <el-form-item label="培训专业">-->
                <!--    <el-select v-model="select" clearable placeholder="请选择培训专业">-->
                <!--      <el-option value="选项1"></el-option>-->
                <!--      <el-option value="选项2"></el-option>-->
                <!--    </el-select>-->
                <!--  </el-form-item>-->
                <!--</el-col>-->
                <!--<el-col :sm="12" :md="8" :xl="6">-->
                <!--  <el-form-item label="培训类别">-->
                <!--    <el-select v-model="select" clearable placeholder="请选择培训类别">-->
                <!--      <el-option value="选项1"></el-option>-->
                <!--      <el-option value="选项2"></el-option>-->
                <!--    </el-select>-->
                <!--  </el-form-item>-->
                <!--</el-col>-->
                <!--<el-col :sm="12" :md="8" :xl="6">-->
                <!--  <el-form-item label="培训对象">-->
                <!--    <el-select v-model="select" clearable placeholder="请选择培训对象">-->
                <!--      <el-option value="选项1"></el-option>-->
                <!--      <el-option value="选项2"></el-option>-->
                <!--    </el-select>-->
                <!--  </el-form-item>-->
                <!--</el-col>-->
                <!--<el-col :sm="12" :md="8" :xl="6">-->
                <!--  <el-form-item label="岗位类别">-->
                <!--    <el-select v-model="select" clearable placeholder="请选择岗位类别">-->
                <!--      <el-option value="选项1"></el-option>-->
                <!--      <el-option value="选项2"></el-option>-->
                <!--    </el-select>-->
                <!--  </el-form-item>-->
                <!--</el-col>-->
                <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                    <el-button type="text">展开<i class="el-icon-arrow-down el-icon--right"></i></el-button>
                    <!--<el-button type="text">收起<i class="el-icon-arrow-up el-icon&#45;&#45;right"></i></el-button>-->
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <el-tabs v-model="activeName" type="card" class="m-tab-card">
              <el-tab-pane label="未授权" name="first">
                <el-table stripe :data="tableData" class="m-table f-mt20">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="培训方案名称" width="320" fixed="left">
                    <template>
                      <el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag>培训方案名称培训方案名称
                    </template>
                  </el-table-column>
                  <el-table-column label="方案学时" min-width="120" align="right">
                    <template>5 </template>
                  </el-table-column>
                  <el-table-column label="方案价格" min-width="120" align="right">
                    <template><i class="f-mr5">¥</i>99</template>
                  </el-table-column>
                  <el-table-column label="培训属性" min-width="240">
                    <template>
                      <p class="f-mb5">人社行业</p>
                      <div class="f-c9 f-f12">
                        <p>培训年度：读取方案的培训年度</p>
                        <p>培训地区：读取培训方案的地区属性值</p>
                        <p>科目类别：读取方案的科目属性值</p>
                        <p>培训类别：方案的专业属性值</p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="学习起止时间" min-width="220">
                    <template>
                      <p><el-tag type="info" size="mini" class="f-mr5">开始</el-tag>2023-08-14 09:56:20</p>
                      <p><el-tag type="info" size="mini" class="f-mr5">结束</el-tag>2023-08-14 09:56:20</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="报名起止时间" min-width="220">
                    <template>
                      <p><el-tag type="info" size="mini" class="f-mr5">开始</el-tag>2023-08-14 09:56:20</p>
                      <p><el-tag type="info" size="mini" class="f-mr5">结束</el-tag>2023-08-14 09:56:20</p>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane label="已授权" name="second">
                <el-table stripe :data="tableData" class="m-table f-mt20">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="培训方案名称" width="320" fixed="left">
                    <template>
                      <el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag>培训方案名称培训方案名称
                    </template>
                  </el-table-column>
                  <el-table-column label="指导价格" min-width="130">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <p><el-tag type="success" size="mini">最低</el-tag><i class="f-mlr5">¥</i>99</p>
                        <p><el-tag type="danger" size="mini">最高</el-tag><i class="f-mlr5">¥</i>199</p>
                      </div>
                      <div v-else><i class="f-mr5">¥</i>99</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="供应商" min-width="150">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-tooltip effect="dark" placement="right" popper-class="m-tooltip">
                          <div class="f-vm">张三<i class="el-icon-document f-ml5 f-c9"></i></div>
                          <div slot="content">
                            <p>张三</p>
                            <p>350555555555555555</p>
                          </div>
                        </el-tooltip>
                      </div>
                      <div v-else>
                        <el-tooltip effect="dark" placement="right" popper-class="m-tooltip">
                          <div class="f-vm">爱迪科森<i class="el-icon-document f-ml5 f-c9"></i></div>
                          <div slot="content">
                            <p>北京爱迪科森教育科技股份有限公司</p>
                            <p>913101175821220588</p>
                          </div>
                        </el-tooltip>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="供应商合作状态" min-width="150" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="primary" class="badge-status">未开始</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 2">
                        <el-badge is-dot type="danger" class="badge-status">中止合作</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="success" class="badge-status">进行中</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="分销授权状态" min-width="150" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="info" class="badge-status">授权关闭</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="success" class="badge-status">授权开启</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="销售有效期" min-width="220">
                    <template>
                      <p><el-tag type="info" size="mini" class="f-mr5">开始</el-tag>2023-08-14 09:56:20</p>
                      <p><el-tag type="info" size="mini" class="f-mr5">结束</el-tag>2023-08-14 09:56:20</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="培训属性" min-width="240">
                    <template>
                      <p class="f-mb5">人社行业</p>
                      <div class="f-c9 f-f12">
                        <p>培训年度：读取方案的培训年度</p>
                        <p>培训地区：读取培训方案的地区属性值</p>
                        <p>科目类别：读取方案的科目属性值</p>
                        <p>培训类别：方案的专业属性值</p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="方案价格" min-width="120" align="right">
                    <template><i class="f-mr5">¥</i>99</template>
                  </el-table-column>
                  <el-table-column label="方案学时" min-width="120" align="right">
                    <template>5 </template>
                  </el-table-column>
                  <el-table-column label="学习起止时间" min-width="220">
                    <template>
                      <p><el-tag type="info" size="mini" class="f-mr5">开始</el-tag>2023-08-14 09:56:20</p>
                      <p><el-tag type="info" size="mini" class="f-mr5">结束</el-tag>2023-08-14 09:56:20</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="报名起止时间" sortable min-width="220">
                    <template>
                      <p><el-tag type="info" size="mini" class="f-mr5">开始</el-tag>2023-08-14 09:56:20</p>
                      <p><el-tag type="info" size="mini" class="f-mr5">结束</el-tag>2023-08-14 09:56:20</p>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        num: '100.00',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: true,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
