import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import MutationBizAccount from './MutationBizAccount'
import MutationFactory from './MutationFactory'
@Module({
  name: 'AccountModule',
  dynamic: true,
  namespaced: true,
  store
})
class AccountModule extends VuexModule {
  mutationFactory = new MutationFactory()
}
export default getModule(AccountModule)
