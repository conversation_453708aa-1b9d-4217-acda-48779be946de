import { getModule, Module, VuexModule, Mutation, Action } from 'vuex-module-decorators'
import store from '@/store'
@Module({
  name: 'SpecialTopicsManageUnitApplyPicSizeTypeModel',
  dynamic: true,
  namespaced: true,
  store
})
class UnitApplyPicSizeTpyeModel extends VuexModule {
  originOfflinePicEnale = false
  originOnlinePicEnable = false
  offlinePicEnable = false
  onlinePicEnable = false
  //判断是否是单入口
  get isSingle() {
    return (this.offlinePicEnable && !this.onlinePicEnable) || (!this.offlinePicEnable && this.onlinePicEnable)
  }
  //判断线下报名图片尺寸是否发生改变
  get isOfflinePicChange() {
    return this.offlinePicEnable !== this.originOfflinePicEnale
  }
  //判断线上报名图片尺寸是否发生改变
  get isOnlinePicChange() {
    return this.onlinePicEnable !== this.originOnlinePicEnable
  }
  //设置线下报名图片启用初始值
  @Mutation
  setOriginOfflinePicEnable(value: boolean) {
    this.originOfflinePicEnale = value
    this.offlinePicEnable = value
  }
  //设置线上报名图片启用初始值
  @Mutation
  setOriginOnlinePicEnable(value: boolean) {
    this.originOnlinePicEnable = value
    this.onlinePicEnable = value
  }
  //设置线下报名图片启用
  @Mutation
  setOfflinePicEnable(value: boolean) {
    this.offlinePicEnable = value
  }
  //设置线上报名图片启用
  @Mutation
  setOnlinePicEnable(value: boolean) {
    this.onlinePicEnable = value
  }
}
export default getModule(UnitApplyPicSizeTpyeModel)
