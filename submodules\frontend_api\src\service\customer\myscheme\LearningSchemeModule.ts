import Response, { ResponseStatus } from '../../../Response'
import PlatformUserLearningSchemeGateway, {
  Page,
  UserExamLearningDTO,
  UserIssueReservationParamDTO,
  UserPracticeLearningDTO
} from '@api/gateway/PlatformUserLearningScheme'
import moment from 'moment'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { Role, RoleType, Secure } from '@api/Secure'
import CommonModule from '@api/service/common/common/CommonModule'
import { UserIssueParam } from '@api/service/common/models/myscheme/UserIssueParam'
import { InterestCourseSetting } from '@api/service/common/models/myscheme/InterestCourseSetting'
import NormalIssueClassLS, { NormalIssueClassLSResponse } from '@api/gateway/NormalIssueClassLS-default'
import UserCourseModule from '@api/service/customer/user-course/UserCourseModule'
import CommonCommodityModule from '@api/service/common/commodity/CommonCommodityModule'
import UserModule from '@api/service/customer/user/query-user/UserModule'
import PracticeLearning, { QuestionLibPractice } from '@api/service/common/models/myscheme/PracticeLearning'
import UserExamLearning from '@api/service/common/models/myscheme/UserExamLearning'
import Effectiveness, { IssueEffectivenessCode } from '@api/service/common/models/myscheme/Effectiveness'
import CourseLearning from '@api/service/common/models/myscheme/CourseLearning'
import IssueAssess from '@api/service/common/models/myscheme/IssueAssess'
import { Constants } from '@api/service/common/models/common/Constants'
import UserIssue from '@api/service/common/models/myscheme/UserIssue'

/**
 * 本地状态数据
 */
interface MySchemeState {
  /**
   * 我报名的所有期数
   */
  myLearningSchemeList: Array<UserIssue>
  /**
   * 是否加载报名的期数
   */
  isLoadLearningScheme: boolean
}

export class UserIssuePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserIssue>
}

/**
 * 我的学习方案模块
 */
@Module({ namespaced: true, dynamic: true, name: 'CustomerLearningSchemeModule', store })
class LearningSchemeModule extends VuexModule implements MySchemeState {
  //region implements
  /**
   * 我报名的所有期数
   */
  public myLearningSchemeList: Array<UserIssue> = new Array<UserIssue>()
  /**
   * 是否加载报名的期数
   */
  public isLoadLearningScheme = false
  /**
   * 方案详情列表
   */
  schemeDetailInfoList = new Array<NormalIssueClassLSResponse>()
  //endregion

  //region private Methods

  /**
   * 创建当前state下练习学习方式对象
   * @param userPracticeLearningDTO
   */
  private static createPracticeLearning(userPracticeLearningDTO: UserPracticeLearningDTO): PracticeLearning {
    const practiceLearning: PracticeLearning = new PracticeLearning()
    if (userPracticeLearningDTO) {
      practiceLearning.learningId = userPracticeLearningDTO.learningId
      if (userPracticeLearningDTO.questionPractice) {
        const questionLibPractice: QuestionLibPractice = new QuestionLibPractice()
        questionLibPractice.practiceId = userPracticeLearningDTO.questionPractice.practiceId
        practiceLearning.questionPractice = questionLibPractice
      }
    }
    return practiceLearning
  }

  /**
   * 创建当前state下考试学习方式对象
   * @param userExamLearningDTO
   */
  private static createExamLearning(userExamLearningDTO: UserExamLearningDTO): UserExamLearning {
    const examLearning: UserExamLearning = new UserExamLearning()
    if (userExamLearningDTO) {
      examLearning.learningId = userExamLearningDTO.learningId
      examLearning.examPaperId = userExamLearningDTO.examPaperId
      examLearning.examRoundId = userExamLearningDTO.examRoundId
      examLearning.examCount = userExamLearningDTO.examCount
      examLearning.beginTime = userExamLearningDTO.beginTime
      examLearning.endTime = userExamLearningDTO.endTime
      examLearning.name = userExamLearningDTO.name
      examLearning.examTimeLength = userExamLearningDTO.examTimeLength
      if (userExamLearningDTO.userExamAssess) {
        examLearning.highestExamScore = userExamLearningDTO.userExamAssess.userHigestScore
        examLearning.passScore = userExamLearningDTO.userExamAssess.score
        examLearning.assessStatus = userExamLearningDTO.userExamAssess.assessStatus
        if (userExamLearningDTO.userExamAssess.passedTime) {
          examLearning.passedTime = moment(
            userExamLearningDTO.userExamAssess.passedTime,
            Constants.DATE_PATTERN
          ).toDate()
        }
      }
    }
    return examLearning
  }

  /**
   * 获取学习期别的有效情况
   * @param params 期别编号
   */
  @Action
  @Role([RoleType.user])
  private static async validUserIssue(params: { issueId: string }): Promise<Effectiveness> {
    const response = await PlatformUserLearningSchemeGateway.validUserIssue({
      issueId: params.issueId,
      userId: undefined
    })
    if (response.status.isSuccess()) {
      const effectiveness: Effectiveness = new Effectiveness()
      effectiveness.isEffective = response.data.code === IssueEffectivenessCode.EFFECTIVE
      effectiveness.code = response.data.code as IssueEffectivenessCode
      effectiveness.message = response.data.message
      return Promise.resolve(effectiveness)
    }
    return Promise.resolve(new Effectiveness())
  }

  //endregion

  //region actions
  /**
   * 初始化我的报名班级列表
   */
  @Action
  public async init(param: UserIssueParam): Promise<ResponseStatus> {
    if (!UserModule.userInfo.isLogin()) {
      return Promise.reject(new ResponseStatus(201, ''))
    }
    if (!this.isLoadLearningScheme) {
      const params = new UserIssueReservationParamDTO()
      if (param.issueId) {
        params.issueIds = [param.issueId]
      }
      const response = await PlatformUserLearningSchemeGateway.listUserIssueReservation(params)
      if (response.status.isSuccess()) {
        // await CommonModule.init()
        const userIssueList: Array<UserIssue> = new Array<UserIssue>()
        const issueIds = new Array<string>()
        for (const issue of response.data) {
          const userIssue = new UserIssue()
          userIssue.schemeId = issue.schemeId
          userIssue.schemeName = issue.scheme
          userIssue.picture = issue.picture ? '/mfs' + issue.picture : ''
          userIssue.year = issue.year
          userIssue.trainingTypeId = issue.trainingTypeId
          userIssue.workTypeId = issue.workTypeId
          // userIssue.jobCategoryId = issue.jobCategoryId
          // userIssue.unitCategoryId = issue.unitCategoryId
          userIssue.issueId = issue.issueId
          issueIds.push(issue.issueId)
          userIssue.issueName = issue.issueTitle
          userIssue.practiceLearning = LearningSchemeModule.createPracticeLearning(issue.practiceLearning)
          CommonModule.appendSkuNameByValue(userIssue)
          userIssue.examLearning = LearningSchemeModule.createExamLearning(issue.examLearning)
          if (issue.courseLearning) {
            const courseLearning: CourseLearning = new CourseLearning()
            courseLearning.learningId = issue.courseLearning.learningId
            courseLearning.ruleConfigId = issue.courseLearning.ruleConfigId
            if (issue.courseLearning.userCourseLearningAssess) {
              courseLearning.schedule = issue.courseLearning.userCourseLearningAssess.userSchedule
              courseLearning.requireSchedule = issue.courseLearning.userCourseLearningAssess.schedule
              courseLearning.hasStudyPeriod = issue.courseLearning.userCourseLearningAssess.hasStudyPeriod
            }
            courseLearning.maxPeriod = issue.courseLearning.minTotalPeriod
            userIssue.courseLearning = courseLearning
          }
          const issueAssess: IssueAssess = new IssueAssess()
          issueAssess.standard = issue.assessStatus
          userIssue.userAssess = issueAssess
          if (issue.achieveSetting) {
            userIssue.achieve.templateId = issue.achieveSetting.templateId
            userIssue.achieve.grade = issue.achieveSetting.grade
          }
          if (issue.createTime) {
            userIssue.appointment = moment(issue.createTime, Constants.DATE_PATTERN).toDate()
          }
          if (issue.startTime) {
            userIssue.startTime = moment(issue.startTime, Constants.DATE_PATTERN).toDate()
          }
          if (issue.endTime) {
            userIssue.endTime = moment(issue.endTime, Constants.DATE_PATTERN).toDate()
          }
          if (issue.interestCourseSetting) {
            userIssue.interestCourseSetting = new InterestCourseSetting()
            userIssue.interestCourseSetting.poolList = issue.interestCourseSetting.poolList
          }
          // const effectiveness = await LearningSchemeModule.validUserIssue({ issueId: issue.issueId })
          // if (effectiveness) {
          //   userIssue.effectiveness = effectiveness
          // }
          userIssue.isOpenPrintCertificate = issue.isOpenPrintCertificate
          userIssueList.push(userIssue)
        }
        // 获取期数商品信息
        const commodityResponse = await CommonCommodityModule.listIssueCommodityInfo(issueIds)
        if (!commodityResponse.isSuccess()) {
          return Promise.reject(commodityResponse)
        }
        this.SET_MY_LEARNING_SCHEME_LIST({ learningSchemeList: userIssueList })
        if (!param.issueId) {
          this.SET_IS_LOAD_MY_SCHEME(true)
        }
        return Promise.resolve(response.status)
      } else {
        return Promise.reject(response.status)
      }
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  @Action
  async pageMyScheme(params: { page: Page; param: UserIssueParam }) {
    const response = await PlatformUserLearningSchemeGateway.pageUserIssueReservationActual({
      page: params.page,
      paramDTO: params.param
    })
    const userIssueList: Array<UserIssue> = new Array<UserIssue>()
    if (response.status.isSuccess()) {
      const issueIds = new Array<string>()
      await CommonModule.init()
      for (const issue of response.data.currentPageData) {
        const userIssue = new UserIssue()
        userIssue.schemeId = issue?.schemeId
        userIssue.schemeName = issue.scheme
        userIssue.picture = issue.picture ? '/mfs' + issue.picture : ''
        userIssue.year = issue.year
        userIssue.trainingTypeId = issue.trainingTypeId
        userIssue.workTypeId = issue.workTypeId
        userIssue.issueId = issue.issueId
        userIssue.isOpenPrintCertificate = issue.isOpenPrintCertificate
        issueIds.push(issue.issueId)
        userIssue.issueName = issue.issueTitle
        userIssue.isOpenPrintCertificate = issue.isOpenPrintCertificate
        userIssue.practiceLearning = LearningSchemeModule.createPracticeLearning(issue.practiceLearning)
        CommonModule.appendSkuNameByValue(userIssue)
        userIssue.examLearning = LearningSchemeModule.createExamLearning(issue.examLearning)
        if (issue.courseLearning) {
          const courseLearning: CourseLearning = new CourseLearning()
          courseLearning.learningId = issue.courseLearning.learningId
          courseLearning.ruleConfigId = issue.courseLearning.ruleConfigId
          if (issue.courseLearning.userCourseLearningAssess) {
            courseLearning.schedule = issue.courseLearning.userCourseLearningAssess.userSchedule
            courseLearning.requireSchedule = issue.courseLearning.userCourseLearningAssess.schedule
            courseLearning.hasStudyPeriod = issue.courseLearning.userCourseLearningAssess.hasStudyPeriod
          }
          courseLearning.maxPeriod = issue.courseLearning.minTotalPeriod
          userIssue.courseLearning = courseLearning
        }
        const issueAssess: IssueAssess = new IssueAssess()
        issueAssess.standard = issue.assessStatus
        userIssue.userAssess = issueAssess
        if (issue.achieveSetting) {
          userIssue.achieve.templateId = issue.achieveSetting.templateId
          userIssue.achieve.grade = issue.achieveSetting.grade
        }
        if (issue.createTime) {
          userIssue.appointment = moment(issue.createTime, Constants.DATE_PATTERN).toDate()
        }
        if (issue.startTime) {
          userIssue.startTime = moment(issue.startTime, Constants.DATE_PATTERN).toDate()
        }
        if (issue.endTime) {
          userIssue.endTime = moment(issue.endTime, Constants.DATE_PATTERN).toDate()
        }
        if (issue.interestCourseSetting) {
          userIssue.interestCourseSetting = new InterestCourseSetting()
          userIssue.interestCourseSetting.poolList = issue.interestCourseSetting.poolList
        }
        userIssueList.push(userIssue)
      }
      // 获取期数商品信息
      const commodityResponse = await CommonCommodityModule.listIssueCommodityInfo(issueIds)
      if (!commodityResponse.isSuccess()) {
        return Promise.reject(commodityResponse)
      }
    }
    const res = new Response<UserIssuePage>()
    res.status = new ResponseStatus(response.status.code, response.status.message)
    res.data = new UserIssuePage()
    res.data.currentPageData = userIssueList
    res.data.totalSize = response.data.totalSize
    res.data.pageNo = response.data.pageNo
    res.data.pageSize = response.data.pageSize
    return res
  }

  /**
   * 重新加载我的报名班级
   */
  @Action
  public doRefreshLearningScheme(param: UserIssueParam): Promise<ResponseStatus> {
    this.SET_IS_LOAD_LEARNING_SCHEME({ isLoad: false })
    return this.init(param)
  }

  /**
   * 重新验证指定的期数班级有效性
   * @param params 参数：issueId - 期数编号
   */
  @Action
  @Role([RoleType.user])
  public async doVerifyLearningSchemeIssue(params: { issueId: string }) {
    const effectiveness = await LearningSchemeModule.validUserIssue(params)
    if (effectiveness) {
      this.SET_MY_LEARNING_SCHEME_EFFECTIVE({
        issueId: params.issueId,
        effectiveness: effectiveness
      })
    }
    return effectiveness
  }

  /**
   * 加载方案详情
   * @param schemeId
   */
  @Action
  async loadSchemeDetailInfo(schemeId: string) {
    if (this.schemeDetailInfoList.find(s => s.schemeId === schemeId)) {
      return new ResponseStatus(200, '')
    }
    const res = await NormalIssueClassLS.findById(schemeId)
    if (res.status.isSuccess()) {
      this.PUSH_SCHEME_DETAIL(res.data)
    }
    return res.status
  }

  //endregion

  //region getters
  /**
   * 指示获取我的所有期数
   */
  get getAllLearningScheme(): Array<UserIssue> {
    return this.myLearningSchemeList
  }

  /**
   * 判断方案是否已购买
   */
  get isSchemeUserBuy() {
    return (schemeId: string): boolean | false => {
      const scheme = this.myLearningSchemeList.find(s => s.schemeId === schemeId) || undefined
      if (scheme) {
        return true
      }
      return false
    }
  }

  /**
   * 获取某个期数信息
   */
  get getLearningSchemeByIssueId() {
    return (issueId: string): UserIssue | undefined => {
      return this.myLearningSchemeList.find(x => x.issueId === issueId)
    }
  }

  /**
   * 是否提供重学
   */
  get enableRelearn() {
    return (issueId: string): boolean => {
      return this.myLearningSchemeList.find(p => p.issueId === issueId)?.enableRelearn || false
    }
  }

  /**
   * 获取已购买的某个班级信息
   */
  get getScheme() {
    return (schemeId: string) => {
      return this.myLearningSchemeList.find(s => s.schemeId === schemeId) || new UserIssue()
    }
  }

  /**
   * 获取我正在学习中的班级
   */
  get listInLearningScheme() {
    return (
      this.myLearningSchemeList.filter(
        s => s.courseLearning.schedule <= 100 && !s.hasIssueFinish() && s.userAssess.standard != 1
      ) || new Array<UserIssue>()
    )
  }

  /**
   * 获取我已合格或培训时间已结束的班级（or关系获取列表）
   */
  get listFinishOrPassedScheme() {
    return (
      this.myLearningSchemeList.filter(s => s.userAssess.standard === 1 || s.hasIssueFinish()) || new Array<UserIssue>()
    )
  }

  /**
   * 获取方案详情
   */
  get getSchemeDetailInfo() {
    return (schemeId: string) => {
      return this.schemeDetailInfoList.find(s => s.schemeId === schemeId) || undefined
    }
  }

  /**
   * 清楚指定期数的所有学习记录
   * @param param
   */
  @Action
  async clearAllLearningRecord(param: { schemeId: string; issueId: string }): Promise<ResponseStatus> {
    const response = await NormalIssueClassLS.clearAllLearningRecordForIssue(param)
    this.ALERT_EXAM_RELOAD(param)
    //刷新缓存
    await this.doRefreshLearningScheme({} as UserIssueParam)
    const learningScheme = this.getLearningSchemeByIssueId(param.issueId)
    if (learningScheme && learningScheme.courseLearning) {
      await UserCourseModule.doRefreshInitByLearningId({
        schemeId: param.schemeId,
        learningId: learningScheme.courseLearning.learningId
      })
    }
    return Promise.resolve(response.status)
  }

  @Mutation
  ALERT_EXAM_RELOAD(payload: any) {
    console.log('提醒需要重载' + JSON.stringify(payload))
  }

  //endregion

  //region mutations
  /**
   * 设置我报名的期数列表
   * @param params 参数：learningSchemeList - 我的期数列表
   */
  @Mutation
  private SET_MY_LEARNING_SCHEME_LIST(params: { learningSchemeList: Array<UserIssue> }) {
    this.myLearningSchemeList = params.learningSchemeList
  }

  /**
   * 设置是否加载我的报名期数
   * @param params 参数：isLoad - 是否加载
   */
  @Mutation
  private SET_IS_LOAD_LEARNING_SCHEME(params: { isLoad: boolean }) {
    this.isLoadLearningScheme = params.isLoad
  }

  /**
   * 设置指定期数的有效性
   * @param params 参数
   */
  @Mutation
  private SET_MY_LEARNING_SCHEME_EFFECTIVE(params: { issueId: string; effectiveness: Effectiveness }) {
    const issue = this.myLearningSchemeList.find(x => x.issueId === params.issueId)
    if (issue) {
      issue.effectiveness = params.effectiveness
    }
  }

  @Mutation
  SET_IS_LOAD_MY_SCHEME(bol: boolean) {
    this.isLoadLearningScheme = bol
  }

  @Mutation
  private PUSH_SCHEME_DETAIL(schemeDetailInfo: NormalIssueClassLSResponse) {
    const find = this.schemeDetailInfoList.find(s => s.schemeId === schemeDetailInfo.schemeId) || undefined
    if (!find) {
      this.schemeDetailInfoList.push(schemeDetailInfo)
    }
  }

  //endregion
}

export default getModule(LearningSchemeModule)
