import platformCertificateGateway, { CheckPrintConditionResponse } from '@api/platform-gateway/platform-certificate-v1'
import CourseCertificatePrintRequestVo from './vo/CourseCertificatePrintRequestVo'
import {Response, ResponseStatus} from '@hbfe/common'
import PrintCertResponse from "@api/service/customer/personal-leaning/mutation/vo/PrintCertResponse";
import PlatformCertificateV1 from "@api/platform-gateway/platform-certificate-v1";

export default class MutationBatchPrintTrainingByCourse {
  /**
   * @description: 打印课程学习证书(科目类型为专业课)
   * @date: 2024/01/11 11:08:36
   * @param params
   */
  async coursePrintCertificate(
    params: CourseCertificatePrintRequestVo
  ): Promise<Response<PrintCertResponse>> {
    const result = new Response<PrintCertResponse>()
    result.status = new ResponseStatus(200)
    result.data = new PrintCertResponse()
    const {status, data} = await PlatformCertificateV1.studentPrintCertificate(params.to())
    result.status = status
    if (data) {
      result.data = PrintCertResponse.from(data)
    }
    return result
  }
}
