<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <div class="m-tit bg-gray is-border-bottom f-align-center">
      <span class="tit-txt">主方案设置</span>
      <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
        <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
        <div slot="content">
          设置成功后，学员报名所选方案时自动与当前方案合并报名。合并报名的任意方案下架或关闭学员报名或不在报名时段内将导致学员无法合并报名。<br />
          当前方案是否单独开放学员报名仍然以本方案设置为准。<br />
          合并报名规则仅在主网站和专题学员门户生效，集体报名、导入开通、分销仍然分别报名。
        </div>
      </el-tooltip>
    </div>
    <div class="f-p20">
      <el-button type="primary" icon="el-icon-plus" class="f-mb15" @click="newProposal">新增方案</el-button>
      <!--通用空数据-->
      <div class="m-no-date" v-if="associatedCommodityList.length == 0">
        <img class="img" src="@design/admin/assets/images/no-data-normal.png" alt="" />
        <div class="date-bd">
          <p class="f-f15 f-c9">暂无数据~</p>
        </div>
      </div>
      <el-table
        stripe
        :data="associatedCommodityList"
        max-height="500px"
        class="m-table"
        v-if="associatedCommodityList.length"
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="方案名称" min-width="240" fixed="left">
          <template slot-scope="scope">
            <p>
              <span style="margin-right: 5px">{{ scope.row.commodityBasicData.saleTitle }}</span>
              <el-tag type="danger" effect="dark" size="mini" v-if="scope.row.onShelve.shelveStatus === 0"
                >已下架</el-tag
              >
              <el-tag
                type="danger"
                effect="dark"
                size="mini"
                v-if="scope.row.commodityPurchaseChannelConfig.customerPurchase.couldBuy === false"
                >不开放学员报名</el-tag
              >
            </p>
          </template>
        </el-table-column>
        <el-table-column label="培训属性" min-width="220">
          <template slot-scope="scope">
            <p v-if="getSkuPropertyName(scope.row, 'industry')">
              行业：{{ getSkuPropertyName(scope.row, 'industry') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'region')">地区：{{ getSkuPropertyName(scope.row, 'region') }}</p>
            <p v-if="getSkuPropertyName(scope.row, 'jobLevel')">
              技术等级：{{ getSkuPropertyName(scope.row, 'jobLevel') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'subjectType')">
              科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
            </p>
            <p v-if="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
              培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'trainingMajor')">
              培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'trainingObject')">
              培训对象：{{ getSkuPropertyName(scope.row, 'trainingObject') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'positionCategory')">
              岗位类别：{{ getSkuPropertyName(scope.row, 'positionCategory') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'year')">培训年度：{{ getSkuPropertyName(scope.row, 'year') }}</p>
            <p v-if="getSkuPropertyName(scope.row, 'learningPhase')">
              学段：{{ getSkuPropertyName(scope.row, 'learningPhase') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'discipline')">
              学科：{{ getSkuPropertyName(scope.row, 'discipline') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'practitionerCategory')">
              执业类别：{{ getSkuPropertyName(scope.row, 'certificatesType') }}-{{
                getSkuPropertyName(scope.row, 'practitionerCategory')
              }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="报名学时" min-width="100" align="center">
          <template slot-scope="scope">{{ scope.row.period }}</template>
        </el-table-column>
        <el-table-column label="价格" min-width="100" align="center">
          <template slot-scope="scope">{{ scope.row.commodityBasicData.price }}</template>
        </el-table-column>
        <el-table-column label="学习起止时间" min-width="200">
          <template slot-scope="scope">
            <p v-if="!scope.row.isLongTerm">起始：{{ scope.row.trainingBeginDate }}</p>
            <p v-if="!scope.row.isLongTerm">结束：{{ scope.row.trainingEndDate }}</p>
            <p v-if="scope.row.isLongTerm">长期有效</p>
          </template>
        </el-table-column>
        <el-table-column label="报名起止时间" min-width="200">
          <template slot-scope="scope">
            <p v-if="scope.row.registerBeginDate || scope.row.registerEndDate">
              起始：{{ scope.row.registerBeginDate ? scope.row.registerBeginDate : '--' }}
            </p>
            <p v-if="scope.row.registerBeginDate || scope.row.registerEndDate">
              结束：{{ scope.row.registerEndDate ? scope.row.registerEndDate : '--' }}
            </p>
            <p v-if="!scope.row.registerBeginDate && !scope.row.registerEndDate">暂不开启</p>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" align="center" fixed="right">
          <template slot-scope="scope">
            <el-popconfirm
              confirm-button-text="确认"
              cancel-button-text="取消"
              icon="el-icon-info"
              icon-color="red"
              title="移除主方案不影响已创建的订单。学员报名已移除的主方案时，当前方案将无法被合并报名。请确认是否移除？"
              @confirm="removeScheme(scope.$index)"
            >
              <el-button type="text" size="mini" slot="reference">移除</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <learning-scheme-select ref="learningSchemeSelectRef" @selectScheme="selectScheme" />
  </el-card>
</template>

<script lang="ts">
  import { Component, PropSync, Ref, Vue, Watch } from 'vue-property-decorator'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import UITrainClassCommodityDetail from '@hbfe/jxjy-admin-scheme/src/models/UITrainClassCommodityDetail'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
  import MutationCreateTrainClassCommodity from '@api/service/diff/management/qztg/train-class/mutation/MutationCreateTrainClassCommodity'
  import LearningSchemeSelect from '@hbfe/jxjy-admin-scheme/src/diff/qztg/__components__/learning-scheme-select.vue'

  @Component({
    components: {
      LearningSchemeSelect
    }
  })
  export default class extends Vue {
    @Ref('learningSchemeSelectRef') learningSchemeSelect: LearningSchemeSelect

    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: Object }) schemeDetail: TrainClassDetailClassVo

    /**
     * 合并的商品列表
     */
    @PropSync('associatedCommodityArr', { type: Array, default: new Array<TrainClassCommodityVo>() })
    associatedCommodityArrSync: Array<TrainClassCommodityVo>

    // 长期有效 - 开始时间
    defaultBeginDate = CreateSchemeUtils.defaultBeginDate
    // 长期有效 - 结束时间
    defaultEndDate = CreateSchemeUtils.defaultEndDate
    /**
     * 合并的商品列表
     */
    associatedCommodityList: Array<UITrainClassCommodityDetail> = []

    // @Watch('associatedCommodityArrSync', {
    //   immediate: true,
    //   deep: true
    // })
    // async changeMergeCommodityArrSync() {
    //   this.updateCommodityList(this.associatedCommodityArrSync, 'associatedCommodityArrSync')
    // }

    created() {
      this.updateCommodityList(this.associatedCommodityArrSync, 'associatedCommodityList')
    }
    /**
     * 新增方案
     */
    newProposal() {
      const commoditySkuIdList = this.associatedCommodityList.map((item) => item.commoditySkuId)
      console.log(this.schemeDetail, 'this.schemeDetail')
      if (this.schemeDetail.commoditySkuId) {
        commoditySkuIdList.push(this.schemeDetail.commoditySkuId)
      }
      this.learningSchemeSelect.showDialog(commoditySkuIdList)
    }

    /**
     * 选择培训方案
     * @param {Array<UITrainClassCommodityDetail>} arr - 一个包含列车班级商品详情的数组
     */
    selectScheme(arr: Array<UITrainClassCommodityDetail>) {
      console.log(arr, 'arr')
      if (arr && arr.length > 0) {
        this.associatedCommodityList = [...arr, ...this.associatedCommodityList]
      }
      console.log(this.associatedCommodityList, 'this.associatedCommodityList')
    }

    /**
     * 移除方案
     */
    removeScheme(index: number) {
      this.associatedCommodityList.splice(index, 1)
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: UITrainClassCommodityDetail, type: string): string {
      if (row.skuValueNameProperty[type]?.skuPropertyName) {
        const value = row.skuValueNameProperty[type].skuPropertyName
        const valuesArr = value.split('/'),
          lastIndex = valuesArr.length - 1
        return type === 'trainingMajor' && !row.isSocietyIndustry ? valuesArr[lastIndex] : value
      }
      return ''
    }

    updateCommodityList(sourceList: TrainClassCommodityVo[], targetProperty: string) {
      const trainSchemeList = sourceList || []
      let trainSchemeListArr: Array<UITrainClassCommodityDetail> = []

      trainSchemeList?.forEach((el: UITrainClassCommodityDetail) => {
        const item = new UITrainClassCommodityDetail()
        Object.assign(item, el)
        item.isLongTerm = false
        item.isSocietyIndustry = false

        if (item.trainingEndDate === this.defaultEndDate && item.trainingBeginDate === this.defaultBeginDate) {
          item.isLongTerm = true
        }
        if (item.skuValueNameProperty?.industry?.skuPropertyName === '人社行业') {
          item.isSocietyIndustry = true
        }

        trainSchemeListArr.push(item)
      })

      // 根据传入的targetProperty动态设置属性值
      this[targetProperty] = trainSchemeListArr
    }
  }
</script>
