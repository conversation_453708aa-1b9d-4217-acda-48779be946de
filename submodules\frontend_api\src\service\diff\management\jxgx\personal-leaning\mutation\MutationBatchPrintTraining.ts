import MsCertificateGateway, { PrintCertificateTemplateRequest } from '@api/ms-gateway/ms-certificate-v1'
import {
  default as certificateDiff,
  CertificateBatchPrintRequest,
  SinglePrintCertificateReponse
} from '@api/diff-gateway/platform-jxjypxtypt-jxgx-certificate'
import {
  default as Cdertificate,
  ChooseTemplateCertificatePrintRequest,
  default as StCertificate,
  StudentBatchPrintCertificatesRequest,
  StudentSchemeLearningRequest,
  UserPropertyRequest,
  UserRequest,
  CheckPrintConditionResponse
} from '@api/platform-gateway/platform-certificate-v1'
import LearningArcjovesRequest from '@api/service/management/personal-leaning/query/vo/LearningArcjovesRequest'
import { Response } from '@hbfe/common'
import UserModule from '@api/service/management/user/UserModule'
import PrintCertificateRequestVo from '@api/service/management/personal-leaning/mutation/vo/PrintCertificateRequestVo'
import PrintCertificationsVo from '@api/service/management/personal-leaning/mutation/vo/PrintCertificationsVo'
import QueryStudentParams from '@api/service/diff/management/jxgx/personal-leaning/QueryStudentParams'

class MutationBatchPrintTraining {
  // 打印证明请求参数
  printCertificateParams = new PrintCertificateRequestVo()

  /**
   * @description: 打印/预览 单个证明
   * @param {Array} param
   */
  async doPrintTraining(): Promise<Response<SinglePrintCertificateReponse>> {
    const res = await certificateDiff.singlePrintCertificate(this.printCertificateParams)
    const response = new Response<SinglePrintCertificateReponse>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.status = res.status
    response.data = res.data
    return response
    // return res
  }

  /**
   * @description: 打印单个学员/下载
   * @param {ChooseTemplateCertificatePrintRequest} params
   */
  async doPrintTrainingStudent(
    params: ChooseTemplateCertificatePrintRequest
  ): Promise<Response<CheckPrintConditionResponse>> {
    const res = await StCertificate.chooseTemplatePrintCertificate(params)
    return res
  }

  /**
   * @description: 批量打印证明(方案打印)
   * @param {PrintCertificationsVo} params
   */
  async doBatchPrintCertificates(
    params: PrintCertificationsVo,
    studentParams: LearningArcjovesRequest
  ): Promise<Response<string>> {
    const studentParam: StudentSchemeLearningRequest = LearningArcjovesRequest.to(studentParams)
    if (studentParams.idCard || studentParams.name) {
      // 查学员ID
      const module = UserModule.queryUserFactory.queryStudentList
      module.queryStudentIdParams.idCard = studentParams.idCard ? studentParams.idCard : undefined
      module.queryStudentIdParams.userName = studentParams.name ? studentParams.name : undefined
      const re = await module.queryStudentIdList()
      if (re.data.length === 0) {
        return [] as any
      }
      studentParam.student = new UserRequest()
      studentParam.student.userProperty = new UserPropertyRequest()
      studentParam.student.userIdList = re.data
      studentParam.student.userProperty.companyName = studentParams.unit || undefined
    }
    // const res = await Cdertificate.batchPrintCertificates({
    const request = new CertificateBatchPrintRequest()
    request.jxjyjxCertificatePrintRequest = params.to()
    request.studentSchemeLearningRequest = studentParam
    request.importStudentPrint = 2
    const res = await certificateDiff.batchPrintCertificate(request)
    // const response = new Response<string>()
    // if (!res.status?.isSuccess()) {
    //   response.status = res.status
    //   return response
    // }
    // response.status = res.status
    // response.data = res.data
    // return response
    return res
  }

  /**
   * @description: 批量打印证明(学员打印)
   * @param {StudentBatchPrintCertificatesRequest} params
   */
  async doBatchPrintStudentCertificates(
    params: PrintCertificationsVo,
    studentParams: LearningArcjovesRequest
  ): Promise<Response<string>> {
    const studentParam: StudentSchemeLearningRequest = LearningArcjovesRequest.to(studentParams)
    if (studentParams.idCard || studentParams.name) {
      // 查学员ID
      const module = UserModule.queryUserFactory.queryStudentList
      module.queryStudentIdParams.idCard = studentParams.idCard ? studentParams.idCard : undefined
      module.queryStudentIdParams.userName = studentParams.name ? studentParams.name : undefined
      const re = await module.queryStudentIdList()
      if (re.data.length === 0) {
        return [] as any
      }
      studentParam.student = new UserRequest()
      studentParam.student.userProperty = new UserPropertyRequest()
      studentParam.student.userIdList = re.data
      studentParam.student.userProperty.companyName = studentParams.unit || undefined
    }
    const request = new CertificateBatchPrintRequest()
    request.jxjyjxCertificatePrintRequest = params.toStudent()
    request.studentSchemeLearningRequest = studentParam
    request.importStudentPrint = 2
    const res = await certificateDiff.batchPrintCertificate(request)
    return res
  }

  /**
   * 批量打印(导入学员打印)
   */
  async batchPrintStudentCertificates(studentParams: QueryStudentParams, params: PrintCertificationsVo) {
    // const res = await PlatformCertificate.learnerImportBatchPrintCertificates({
    //   batchPrintingRequest: QueryStudentParams.to(studentParams),
    //   request: params.toStudent()
    // })
    const request = new CertificateBatchPrintRequest()
    request.jxjyjxCertificatePrintRequest = params.toStudent()
    request.importStudentBatchPrintingRequest = studentParams
    request.importStudentPrint = 1
    const res = await certificateDiff.batchPrintCertificate(request)
    return res.status
  }

  /**
   * @description: 下载证明模板
   * @param {PrintCertificateTemplateRequest} params
   */
  async doDownloadCertificateTemplate(params: PrintCertificateTemplateRequest): Promise<string> {
    const res = await MsCertificateGateway.printCertificateTemplate(params)
    if (!res?.status?.isSuccess()) {
      console.error('下载证明模板请求失败！')
      return null
    }
    return res?.data
  }
}

export default MutationBatchPrintTraining
