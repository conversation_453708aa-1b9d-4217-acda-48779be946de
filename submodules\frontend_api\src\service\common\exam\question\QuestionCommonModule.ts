import Question from '@api/service/common/models/exam/question/Question'
import { Action, Mutation, VuexModule } from 'vuex-module-decorators'
import QuestionCreate from '@api/service/common/models/exam/question/create/QuestionCreate'
import QuestionUpdate from '@api/service/common/models/exam/question/update/QuestionUpdate'
import ExamGateway from '@api/gateway/btpx@GeneralExam-default'

export interface IQuestionCommonState<TQuestion extends Question> {
  /**
   * 试题列表
   */
  questionList: Array<TQuestion>
  /**
   * 机构试题列表
   */
  teachQuestionList: Array<TQuestion>

  /**
   * 总题数
   */
  totalSize: number
  /**
   * 机构试题总题数
   */
  teachQuestionTotalSize: number
}

class QuestionCommonModule<
  TQuestion extends Question,
  TQuestionCreate extends QuestionCreate,
  TQuestionUpdate extends QuestionUpdate
> extends VuexModule implements IQuestionCommonState<TQuestion> {
  questionList = new Array<TQuestion>()
  teachQuestionList = new Array<TQuestion>()
  totalSize = 0
  teachQuestionTotalSize = 0

  /**
   * 删除试题
   * @param questionId
   */
  @Action
  async deleteQuestion(questionId: string) {
    const response = await ExamGateway.deleteQuestion(questionId)
    return response.status
  }

  /**
   * 启用试题
   * @param questionId
   */
  @Action
  async enableQuestion(questionId: string) {
    const response = await ExamGateway.enableQuestion(questionId)
    return response.status
  }

  /**
   * 停用试题
   * @param questionId
   */
  @Action
  async disableQuestion(questionId: string) {
    const response = await ExamGateway.disableQuestion(questionId)
    return response.status
  }

  @Mutation
  setTotalSize(totalSize: number) {
    this.totalSize = totalSize
  }

  @Mutation
  setTeachQuestionTotalSize(totalSize: number) {
    this.teachQuestionTotalSize = totalSize
  }

  @Mutation
  setQuestionList(questionList: Array<TQuestion>) {
    this.questionList = questionList
  }

  @Mutation
  setTeachQuestionQuestionList(questionList: Array<TQuestion>) {
    this.teachQuestionList = questionList
  }

  @Mutation
  addOrReplaceQuestion(question: TQuestion) {
    if (this.questionList.some(x => x.id === question.id)) {
      this.questionList = this.questionList.map(x => {
        if (x.id === question.id) {
          const result = new Question() as TQuestion
          Object.assign(result, question)
          return result
        } else {
          return x
        }
      })
    } else {
      this.questionList.push(question)
    }
  }
}

export default QuestionCommonModule
