"""独立部署的微服务,K8S服务名:ms-shortmessage-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""校验短信授权配置是否启用
		@return {@link VerifyAuthConfigEnableResponse}
		<AUTHOR> By Cb
		@date 2022/6/14 14:06
	"""
	verifyAuthConfigEnable:VerifyAuthConfigEnableResponse
}
"""校验短信授权配置是否启用响应
	<AUTHOR> By Cb
	@since 2022/6/14 14:03
"""
type VerifyAuthConfigEnableResponse @type(value:"com.fjhb.ms.shortmessage.v1.kernel.gateway.graphql.response.VerifyAuthConfigEnableResponse") {
	"""是否启用"""
	enable:Boolean!
}

scalar List
