<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 17:00:41
 * @LastEditors: chenweinian
 * @LastEditTime: 2023-07-24 19:46:12
 * @Description:
-->
<template>
  <el-drawer :title="title" :visible.sync="openList" direction="rtl" size="750px" custom-class="m-drawer">
    <div class="drawer-bd">
      <!--表格-->
      <el-table stripe :data="allSchemeList" class="m-table" v-loading="loading">
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="培训方案名称" min-width="300">
          <template slot-scope="scope">{{ scope.row.schemeName }}</template>
        </el-table-column>
        <el-table-column label="方案属性" width="240" align="center" fixed="right">
          <template slot-scope="scope">
            <sku-display :sku-item="scope.row"></sku-display>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </div>
    <div class="m-btn-bar drawer-ft">
      <el-button type="primary" @click="openList = false">确定</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, PropSync, Vue } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import AntiSchemeItem from '@api/service/management/train-class/query/vo/AntiSchemeItem'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import AntiSchemeParams from '@api/service/management/train-class/query/vo/AntiSchemeParams'
  import SkuDisplay from '@hbfe/jxjy-admin-platform/src/function/components/skuDisplay.vue'
  import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'

  @Component({
    components: {
      SkuDisplay
    }
  })
  export default class extends Vue {
    // 是否打开培训方案列表抽屉
    @PropSync('openTrainingList', {
      type: Boolean,
      default: false
    })
    openList: boolean

    // 是否打开培训方案列表抽屉
    @Prop({
      type: String,
      default: '当前监管规则下的培训方案'
    })
    title: string

    // 是哪个页面打开的弹窗
    @Prop({
      type: String,
      default: 'LearningRulesList'
    })
    pageTitle: string

    // 方案id列表
    schemeIds: Array<string>

    // 查询参数
    queryParams = new AntiSchemeParams()
    // 分页
    page: UiPage

    // 加载
    loading = false

    constructor() {
      super()
      this.page = new UiPage(this.querySchemeList, this.querySchemeList)
    }

    // 方案列表
    allSchemeList = new Array<RuleSchemeItem>()
    // 查询方案列表方法
    queryTrainClassCommodityList = new QueryTrainClassCommodityList()

    async querySchemeList() {
      this.queryParams.schemeIds = this.schemeIds
      this.loading = true
      if (this.pageTitle == 'LearningRulesList') {
        this.allSchemeList = await this.queryTrainClassCommodityList.pageRuleSchemeList(this.page, this.queryParams)
      }
      this.loading = false
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: AntiSchemeItem, type: string): string {
      if (row.sku[type]?.skuPropertyName) {
        return row.sku[type].skuPropertyName
      }
      return ''
    }
  }
</script>
