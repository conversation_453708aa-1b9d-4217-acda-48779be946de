/*
 * @Description: 发票查询(线下)
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:32:55
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-06-07 16:25:50
 */
import { Page } from '@hbfe/common'
import QueryPageInvoiceParam from '@api/service/management/trade/single/invoice/query/dto/QueryPageInvoiceParam'
import {
  OfflineInvoiceSortField,
  OfflineInvoiceSortRequest,
  SortPolicy1
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import TradeQueryBackstage from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
import UserModule from '@api/service/management/user/UserModule'
import StudentUserInfoVo from '@api/service/management/user/query/student/vo/StudentUserInfoVo'
import QueryOffLinePageInvoiceParam from './vo/QueryOffLinePageInvoiceParam'

import ExportGateWay from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import QueryOffLineInvoiceBase from '@api/service/management/trade/single/invoice/query/vo/QueryOffLineInvoiceBase'
export default class QueryOffLineInvoice extends QueryOffLineInvoiceBase {
  /**
   * 分页电子查询发票
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<OffLinePageInvoiceVo>
   */
  async offLinePageInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest> = [
      { field: OfflineInvoiceSortField.INVOICE_CREAT_TIME, policy: SortPolicy1.DESC }
    ]
  ): Promise<Array<OffLinePageInvoiceVo>> {
    queryOffLinePageInvoiceParam.invoiceType = 1
    queryOffLinePageInvoiceParam.invoiceCategoryList = [2]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone ||
      queryOffLinePageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryOffLinePageInvoiceParam.userName
        ? queryOffLinePageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryOffLinePageInvoiceParam.idCard
        ? queryOffLinePageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryOffLinePageInvoiceParam.phone
        ? queryOffLinePageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryOffLinePageInvoiceParam.loginAccount
        ? queryOffLinePageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        if (!idList.data.length) {
          page.totalSize = 0
          page.totalPageSize = 0
          return []
        }
        request.associationInfo.buyerIdList = idList.data
      }
    }
    const params = {
      page,
      request,
      sort
    }

    const result = await TradeQueryBackstage.pageOfflineInvoiceInServicer(params)
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize
    const data = new Array<OffLinePageInvoiceVo>()
    const userIds = new Array<string>()
    result.data.currentPageData.forEach(item => {
      data.push(OffLinePageInvoiceVo.from(item))
      userIds.push(item.associationInfo.buyer.userId)
    })
    const userIdMap = await this.getUserInfo(userIds)
    data.forEach(item => {
      if (userIdMap.has(item.userId)) {
        const result = userIdMap.get(item.userId)
        item.setUserInfo(result.userName, result.idCard, result.phone, result.email, result.loginAccount)
      }
    })
    return data
  }
  /**
   * 电子发票导出
   * @param QueryOffLinePageInvoiceParam 查询参数
   */
  async offLinePageInvoiceInExport(queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam): Promise<boolean> {
    queryOffLinePageInvoiceParam.invoiceType = 1
    queryOffLinePageInvoiceParam.invoiceCategoryList = [2]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone ||
      queryOffLinePageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryOffLinePageInvoiceParam.userName
        ? queryOffLinePageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryOffLinePageInvoiceParam.idCard
        ? queryOffLinePageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryOffLinePageInvoiceParam.phone
        ? queryOffLinePageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryOffLinePageInvoiceParam.loginAccount
        ? queryOffLinePageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        request.associationInfo.buyerIdList = idList.data
      }
    }
    const result = await ExportGateWay.exportOfflineInvoiceInServicerForJxjy(request)
    return result.data
  }
  /**
   * 分页专票查询发票（纸质票）
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<OffLinePageInvoiceVo>
   */
  async offLinePageVatspecialplaInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest> = [
      { field: OfflineInvoiceSortField.INVOICE_CREAT_TIME, policy: SortPolicy1.DESC }
    ]
  ): Promise<Array<OffLinePageInvoiceVo>> {
    queryOffLinePageInvoiceParam.invoiceType = 2
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (request.invoiceDeliveryInfo?.deliveryStatusList?.length > 0) {
      request.basicData.billStatusList = []
    }
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone ||
      queryOffLinePageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryOffLinePageInvoiceParam.userName
        ? queryOffLinePageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryOffLinePageInvoiceParam.idCard
        ? queryOffLinePageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryOffLinePageInvoiceParam.phone
        ? queryOffLinePageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryOffLinePageInvoiceParam.loginAccount
        ? queryOffLinePageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        //
        request.associationInfo.buyerIdList = idList.data
        if (idList.data.length === 0) {
          page.totalSize = 0
          page.totalPageSize = 0
          return []
        }
      }
    }
    const params = {
      page,
      request,
      sort
    }

    const result = await TradeQueryBackstage.pageOfflineInvoiceInServicer(params)
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize
    const data = new Array<OffLinePageInvoiceVo>()
    const userIds = new Array<string>()
    result.data.currentPageData.forEach(item => {
      data.push(OffLinePageInvoiceVo.from(item))
      userIds.push(item.associationInfo.buyer.userId)
    })
    const userIdMap = await this.getUserInfo(userIds)
    data.forEach(item => {
      if (userIdMap.has(item.userId)) {
        const result = userIdMap.get(item.userId)
        item.setUserInfo(result.userName, result.idCard, result.phone, result.email, result.loginAccount)
      }
    })
    return data
  }
  /**
   * 分页专票查询发票（电子）
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<OffLinePageInvoiceVo>
   */
  async offLinePageElectVatspecialplaInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest> = [
      { field: OfflineInvoiceSortField.INVOICE_CREAT_TIME, policy: SortPolicy1.DESC }
    ]
  ): Promise<Array<OffLinePageInvoiceVo>> {
    queryOffLinePageInvoiceParam.invoiceType = 1
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone ||
      queryOffLinePageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryOffLinePageInvoiceParam.userName
        ? queryOffLinePageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryOffLinePageInvoiceParam.idCard
        ? queryOffLinePageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryOffLinePageInvoiceParam.phone
        ? queryOffLinePageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryOffLinePageInvoiceParam.loginAccount
        ? queryOffLinePageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        //
        request.associationInfo.buyerIdList = idList.data
        if (idList.data.length === 0) {
          page.totalSize = 0
          page.totalPageSize = 0
          return []
        }
      }
    }
    const params = {
      page,
      request,
      sort
    }

    const result = await TradeQueryBackstage.pageOfflineInvoiceInServicer(params)
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize
    const data = new Array<OffLinePageInvoiceVo>()
    const userIds = new Array<string>()
    result.data.currentPageData.forEach(item => {
      data.push(OffLinePageInvoiceVo.from(item))
      userIds.push(item.associationInfo.buyer.userId)
    })
    const userIdMap = await this.getUserInfo(userIds)
    data.forEach(item => {
      if (userIdMap.has(item.userId)) {
        const result = userIdMap.get(item.userId)
        item.setUserInfo(result.userName, result.idCard, result.phone, result.email, result.loginAccount)
      }
    })
    return data
  }
  /**
   * 个人线下发票导出 - 专票（纸质票）
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  async offLinePageVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean> {
    queryOffLinePageInvoiceParam.invoiceType = 2
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone ||
      queryOffLinePageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryOffLinePageInvoiceParam.userName
        ? queryOffLinePageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryOffLinePageInvoiceParam.idCard
        ? queryOffLinePageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryOffLinePageInvoiceParam.phone
        ? queryOffLinePageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryOffLinePageInvoiceParam.loginAccount
        ? queryOffLinePageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        request.associationInfo.buyerIdList = idList.data
      }
    }
    const result = await ExportGateWay.exportOfflineInvoiceInServicerForJxjy(request)
    return result.data
  }
  /**
   * 个人线下发票导出 - 专票（电子票）
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  async offLinePageElectVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean> {
    queryOffLinePageInvoiceParam.invoiceType = 1
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone ||
      queryOffLinePageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryOffLinePageInvoiceParam.userName
        ? queryOffLinePageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryOffLinePageInvoiceParam.idCard
        ? queryOffLinePageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryOffLinePageInvoiceParam.phone
        ? queryOffLinePageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryOffLinePageInvoiceParam.loginAccount
        ? queryOffLinePageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        request.associationInfo.buyerIdList = idList.data
      }
    }
    const result = await ExportGateWay.exportOfflineInvoiceInServicerForJxjy(request)
    return result.data
  }
  /**
   * 查询发票详情 - 线下电子票
   * @param invoiceId 发票ID
   */
  async offLineGetInvoiceInServicer(invoiceId: Array<string> | string, page?: Page) {
    if (typeof invoiceId === 'string') {
      const { data } = await TradeQueryBackstage.getOfflineInvoiceInServicer(invoiceId)
      return OffLinePageInvoiceVo.from(data)
    } else {
      const promiseArr = []
      const data: OffLinePageInvoiceVo[] = []
      for (let i = 0; i < invoiceId.length; i++) {
        const element = invoiceId[i]
        promiseArr.push(TradeQueryBackstage.getOfflineInvoiceInServicer(element))
      }
      await Promise.all(promiseArr).then(res => {
        for (let i = 0; i < res.length; i++) {
          const element = res[i]
          data.push(OffLinePageInvoiceVo.from(element.data))
        }
      })
      return data
    }
  }
  //   /**
  //    * 查询发票详情 - 专票
  //    * @param invoiceId 发票ID
  //    */
  //   async offLineGetTicketInvoiceInServicer(invoiceId: string | Array<string>, page?: Page) {
  //     if (typeof invoiceId == 'string') {
  //       const result = await TradeQueryBackstage.getOfflineInvoiceInServicer(invoiceId)
  //       const data = OffLinePageInvoiceVo.from(result.data)
  //       return data
  //     } else {
  //       // 根据发票ID集合请求
  //       const queryPageInvoiceParam = new QueryOffLinePageInvoiceParam()
  //       queryPageInvoiceParam.invoiceIdList = invoiceId
  //       return this.offLinePageVatspecialplaInvoiceInServicer(page, queryPageInvoiceParam)
  //     }
  //   }
}
