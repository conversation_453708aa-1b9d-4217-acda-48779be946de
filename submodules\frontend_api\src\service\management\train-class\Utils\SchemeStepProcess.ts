import MsLearningscheme from '@api/ms-gateway/ms-learningscheme-v1'
import { ResponseStatus } from '@hbfe/common'
import { SchemeProcessStatusEnum } from '@api/service/management/train-class/query/enum/SchemeProcessStatusEnum'

export default class SchemeStepProcess {
  /**
   * 轮询状态
   */
  polling = false

  /**
   * 轮询间隔
   */
  interval = 1500

  /**
   * 轮询计时器
   */
  private timeOut: any = undefined

  /**
   * 检验方案是否可修改
   */
  async checkSchemeCanModify(id: string) {
    const res = await MsLearningscheme.learningSchemeProcessTransactionStepQuery(id)

    // 接口异常
    if (!res?.status || !res.status.isSuccess() || !res?.data) {
      return new ResponseStatus(SchemeProcessStatusEnum.error, '系统异常')
    }

    // 任务被挂起，需要人为接入
    if (res.data.hangUp) {
      return new ResponseStatus(SchemeProcessStatusEnum.hangUp, '方案异常，无法修改')
    }

    // 方案重算中，无法修改
    if (res.data.recalculating) {
      return new ResponseStatus(SchemeProcessStatusEnum.recalculating, '方案正在重算中，无法修改')
    }

    // 未完成
    if (res.data.lastTransactionStep != 5) {
      return new ResponseStatus(SchemeProcessStatusEnum.processing, '方案正在处理中，无法修改')
    } else {
      // 已完成与异常允许再次修改
      return new ResponseStatus(SchemeProcessStatusEnum.finish, '')
    }
  }

  /**
   * 获取方案修改状态（轮询）
   * @param id 方案id
   */
  async getSchemeTaskStatus(id: string) {
    if (!id) {
      console.error('方案id不能为空')
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
    if (this.polling && this.timeOut) {
      console.error('已有计时器在运行')
      return Promise.reject(new ResponseStatus(501, '已有计时器在运行'))
    }
    this.polling = true
    const res = await new Promise<ResponseStatus>((resolve, reject) => {
      this.pollingGetSchemeProcessSuccess(id, resolve, reject)
    })

    return res
  }

  /**
   * 轮询方案是否完成更新
   * @param id 方案id
   * @param resolve
   * @param reject
   */
  private async pollingGetSchemeProcessSuccess(
    id: string,
    resolve: (data: ResponseStatus) => void,
    reject: (reason?: ResponseStatus) => void
  ) {
    const res = await MsLearningscheme.learningSchemeProcessTransactionStepQuery(id)

    // 接口异常
    if (!res?.status || !res.status.isSuccess() || !res?.data) {
      this.stopPolling()
      return reject(new ResponseStatus(SchemeProcessStatusEnum.error, '系统异常'))
    }

    // 任务被挂起，需要人为接入
    if (res.data.hangUp) {
      this.stopPolling()
      return reject(new ResponseStatus(SchemeProcessStatusEnum.hangUp, '方案异常，无法修改'))
    }

    // 处理进程异常
    if (res.data.errors?.length) {
      this.stopPolling()
      const errorMessage = res.data.errors.find((err) => !!err)?.message || '方案异常，无法修改'
      return reject(new ResponseStatus(SchemeProcessStatusEnum.processing, errorMessage))
    }

    // 已完成
    if (res.data.lastTransactionStep === 5) {
      this.stopPolling()
      return resolve(new ResponseStatus(SchemeProcessStatusEnum.finish, ''))
    }

    // 继续轮询
    if (this.polling) {
      clearTimeout(this.timeOut)
      this.timeOut = setTimeout(async () => {
        await this.pollingGetSchemeProcessSuccess(id, resolve, reject)
      }, this.interval)
    }
  }

  /**
   * 主动结束轮询
   */
  stopPolling() {
    this.polling = false
    clearTimeout(this.timeOut)
    this.timeOut = undefined
  }
}
