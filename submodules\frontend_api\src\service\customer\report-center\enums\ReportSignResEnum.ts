import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum ReportSignResEnum {
  /**
   * 成功
   */
  success = 200,

  /**
   * 重复报道
   */
  repeat = 1001,

  /**
   * 不在培训点范围
   */
  outOfTrainingLocation = 1002,

  /**
   * 不在报道时间
   */
  notInTime = 1003,

  /**
   * 未设置报道范围
   */
  notSetRange = 1004
}

export const BackendToReportSignResEnum: Record<string, ReportSignResEnum> = {
  '200': ReportSignResEnum.success,
  E001: ReportSignResEnum.repeat,
  E002: ReportSignResEnum.outOfTrainingLocation,
  E003: ReportSignResEnum.notInTime,
  E004: ReportSignResEnum.notSetRange
}

class ReportSignRes extends AbstractEnum<ReportSignResEnum> {
  static enum = ReportSignResEnum
  constructor(status?: ReportSignResEnum) {
    super()
    this.current = status
    this.map.set(ReportSignResEnum.success, '报道成功。')
    this.map.set(ReportSignResEnum.repeat, '您已报到成功，无需重复报到。')
    this.map.set(ReportSignResEnum.notInTime, '不在报到时段内，无法报到。')
    this.map.set(ReportSignResEnum.outOfTrainingLocation, '不在报到范围内，无法报到。')
    this.map.set(ReportSignResEnum.notSetRange, '未设置报道地点范围，请联系管理员。')
  }
}

export default new ReportSignRes()
