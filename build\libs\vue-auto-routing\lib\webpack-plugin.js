'use strict'
const assert = require('assert')
const fs = require('fs')
const path = require('path')
const vueRouteGenerator = require('../../vue-route-generator/lib/index')
const pluginName = 'VueAutoRoutingPlugin'

class VueAutoRoutingPlugin {
  constructor(options) {
    this.options = options
    this.cacheDir = path.resolve(__dirname, '../../../../.cache')
    this.cacheDirTarget = path.resolve(this.cacheDir, options.cacheDir || 'boilerplate-routes')
    if (!fs.existsSync(this.cacheDir)) {
      fs.mkdirSync(this.cacheDir)
    }
    if (!fs.existsSync(this.cacheDirTarget)) {
      fs.mkdirSync(this.cacheDirTarget)
    }
    assert(options.pages, '`pages` is required')
  }

  apply(compiler) {
    const generate = () => {
      const code = vueRouteGenerator.generateRoutes(this.options)
      const to = path.resolve(this.cacheDirTarget, 'index.js')
      if (fs.existsSync(to) && fs.readFileSync(to, 'utf8').trim() === code.trim()) {
        return
      }
      fs.writeFileSync(to, code)
    }
    compiler.hooks.thisCompilation.tap(pluginName, compilation => {
      try {
        generate()
      } catch (error) {
        compilation.errors.push(error)
      }
    })
  }
}

module.exports = VueAutoRoutingPlugin
