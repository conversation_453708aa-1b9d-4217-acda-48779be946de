import domainGateWay from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { RoleDetailVo } from '@api/service/management/authority/role/query/vo/RoleDetailVo'
import bind from 'lodash-decorators/bind'

export default class QueryCurrentUserRoleList {
  roleList: Array<RoleDetailVo> = new Array<RoleDetailVo>()

  /**
   * 获取当前登录用户角色列表
   */
  @bind
  async getCurrentUserRoleList() {
    const roleRes = await domainGateWay.findRoleByAccountId()
    if (roleRes.status.isSuccess()) {
      this.roleList = roleRes.data.map(role => RoleDetailVo.from(role))
    } else {
      this.roleList = []
    }
    return roleRes.status
  }
}
