import getCombinationCommodityReportSummaryInServicer from './queries/getCombinationCommodityReportSummaryInServicer.graphql'
import getReturnOrderInDistributor from './queries/getReturnOrderInDistributor.graphql'
import getReturnOrderInServicer from './queries/getReturnOrderInServicer.graphql'
import pageCombinationOpenReportFormsInServicer from './queries/pageCombinationOpenReportFormsInServicer.graphql'
import pageReturnOrderInDistributor from './queries/pageReturnOrderInDistributor.graphql'
import pageReturnOrderInServicer from './queries/pageReturnOrderInServicer.graphql'
import pageReturnOrderInTrainingChannel from './queries/pageReturnOrderInTrainingChannel.graphql'

export {
  getCombinationCommodityReportSummaryInServicer,
  getReturnOrderInDistributor,
  getReturnOrderInServicer,
  pageCombinationOpenReportFormsInServicer,
  pageReturnOrderInDistributor,
  pageReturnOrderInServicer,
  pageReturnOrderInTrainingChannel
}
