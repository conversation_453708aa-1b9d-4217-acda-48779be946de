<template>
  <div>
    <hb-search-wrapper v-if="type === '1'" class="m-query is-border-bottom" @reset="resetSearch">
      <el-form-item label="分销商品">
        <!--        <biz-distributor-autocomplete-->
        <!--          v-model="params.trainingProductName"-->
        <!--          :isShowFullName="true"-->
        <!--          :includeOneself="true"-->
        <!--        />-->
      </el-form-item>
      <el-form-item label="商品售价">
        <el-input-number v-model="params.outOfPocketAmount.min" :controls="false" class="input-num f-flex-sub" />
        -
        <el-input-number v-model="params.outOfPocketAmount.max" :controls="false" class="input-num f-flex-sub" />
      </el-form-item>
      <el-form-item label="查询时间">
        <el-date-picker
          v-model="params.queryTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="起始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item label="推广门户简称">
        <el-input clearable placeholder="请输入分销商推广门户简称" v-model="params.portalPromoteTheName" />
      </el-form-item>
      <template slot="actions">
        <el-button type="primary" @click="searchMy">查询</el-button>
        <el-button @click="exportMy">导出列表数据</el-button>
      </template>
    </hb-search-wrapper>
    <hb-search-wrapper v-else class="m-query is-border-bottom" @reset="resetSearch">
      <el-form-item label="推广门户简称">
        <el-input clearable placeholder="请输入分销商推广门户简称" v-model="params.portalPromoteTheName" />
      </el-form-item>
      <el-form-item label="分销商">
        <!--        <biz-distributor-autocomplete v-model="params.distributorName" :isShowFullName="true" :includeOneself="true" />-->
      </el-form-item>
      <el-form-item label="培训商品">
        <!-- <biz-goods-autocomplete-multiple
              v-model="queryParams.schemeId"
              :schoolId="queryParams.schoolId"
            ></biz-goods-autocomplete-multiple> -->
      </el-form-item>
      <el-form-item label="实付金额">
        <el-input-number v-model="params.outOfPocketAmount.min" :controls="false" class="input-num" />
        -
        <el-input-number v-model="params.outOfPocketAmount.max" :controls="false" class="input-num" />
      </el-form-item>
      <el-form-item label="查询时间">
        <el-date-picker
          v-model="params.queryTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="起始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <template slot="actions">
        <el-button type="primary" @click="searchLow">查询</el-button>
        <el-button @click="exportLow">导出列表数据</el-button>
      </template>
    </hb-search-wrapper>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'
  import DistributorSalesStatisticsParams from '@api/service/management/statisticalReport/DistributorSalesStatistics/model/DistributorSalesStatisticsParams'
  @Component
  export default class extends Vue {
    @Prop({
      type: String,
      required: true
    })
    type: string // 查询类型
    @Prop({
      type: Object,
      required: true
    })
    params: DistributorSalesStatisticsParams // 参数模型

    /**
     * 查询我的分销列表数据
     */
    searchMy() {
      this.$emit('searchMy')
    }

    /**
     * 导出我的分销列表数据
     */
    exportMy() {
      this.$emit('exportMy')
    }

    /**
     * 查询下级分销商列表数据
     */
    searchLow() {
      this.$emit('searchLow')
    }

    /**
     * 导出下级分销商列表数据
     */
    exportLow() {
      this.$emit('exportLow')
    }

    /**
     * 重置
     */
    resetSearch() {
      this.$emit('resetSearch')
    }
  }
</script>
