import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjy-import-open-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjy-import-open-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 批量并行查询
 */
export class BatchParallelQueryDto {
  /**
   * 不同ParallelQueryDto间查询条件相互独立
   */
  parallelQueryDtoList?: Array<ParallelQueryDto>
}

export class ImportOpenQueryParamRequest {
  /**
   * 主任务id
   */
  mainTaskId?: string
  /**
   * 不同的BatchParallelQueryDto间查询条件不相互独立，需要同时满足条件才可以查到结果
   */
  batchParallelQueryDtoList?: Array<BatchParallelQueryDto>
  /**
   * 订单状态
0-未开通 1-开通中 2-已开通，默认null
@see OrderStateConstant
   */
  orderState?: number
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 商品id
   */
  commodityId?: string
  /**
   * 子任务状态
0 - 已创建
1 - 已就绪
2 - 执行中
3 - 执行完成
   */
  subTaskState?: number
  /**
   * 子任务处理结果
0 - 未处理
1 - 处理成功
2 - 处理失败
3 - 就绪失败
   */
  subTaskProcessResult?: number
  /**
   * 导入开始时间
   */
  importStartTime?: string
  /**
   * 导入结束时间
   */
  importEndTime?: string
  /**
   * 用户快照
   */
  userSnapshot?: UserSnapshot
}

/**
 * 单个属性元数据
<AUTHOR>
@since 2022/4/20
 */
export class MetaProperty {
  /**
   * 属性键
   */
  key?: string
  /**
   * 属性值
   */
  value?: string
}

/**
 * 单个属性元数据
<AUTHOR>
@since 2022/4/20
 */
export class MetaPropertyExist {
  /**
   * 属性键
订单状态：orderState
   */
  key?: string
  /**
   * 是否存在
   */
  value: boolean
}

export class ParallelQueryDto {
  commodityId?: string
  /**
   * 子任务状态
0 - 已创建
1 - 已就绪
2 - 执行中
3 - 执行完成
   */
  subTaskState?: number
  /**
   * 子任务处理结果
0 - 未处理
1 - 处理成功
2 - 处理失败
3 - 就绪失败
   */
  subTaskProcessResult?: number
  /**
   * 查询属性是否存在集合
   */
  existProperties?: Array<MetaPropertyExist>
  /**
   * 订单状态
0-未开通 1-开通中 2-已开通，3-无法创建订单，默认null
@see OrderStateConstant
   */
  orderState?: number
}

/**
 * 优惠导入开通导入参数
 */
export class DiscountImportOpenRequest {
  /**
   * 文件路径
   */
  filePath?: string
  /**
   * 文件名称
   */
  fileName?: string
  /**
   * 终端类型
<p>
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  terminalCode?: string
  /**
   * 注册方式
@see AccountRegisterTypes
   */
  registerType: number
  /**
   * 来源类型
@see AccountSourceTypes
   */
  sourceType: number
  /**
   * 默认密码
   */
  password?: string
  /**
   * 密码模式
1-默认密码——password
2-使用身份证后六位
   */
  passwordModel: number
  /**
   * 密码生效范围，1-默认仅新用户，2-全部用户（含已注册）
   */
  passwordEffectiveRange: number
  /**
   * 是否更新基础信息
   */
  updateBasicInfo: boolean
  /**
   * 产品优惠申请ID
   */
  distributionDiscountApplyId?: string
}

/**
 * 分销导入开通导入参数
 */
export class DistributionImportOpenRequest {
  /**
   * 文件路径
   */
  filePath?: string
  /**
   * 文件名称
   */
  fileName?: string
  /**
   * 终端类型
<p>
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  terminalCode?: string
  /**
   * 注册方式
@see AccountRegisterTypes
   */
  registerType: number
  /**
   * 来源类型
@see AccountSourceTypes
   */
  sourceType: number
  /**
   * 默认密码
   */
  password?: string
  /**
   * 密码模式
1-默认密码——password
2-使用身份证后六位
   */
  passwordModel: number
  /**
   * 密码生效范围，1-默认仅新用户，2-全部用户（含已注册）
   */
  passwordEffectiveRange: number
  /**
   * 是否更新基础信息
   */
  updateBasicInfo: boolean
}

/**
 * 集体缴费查询对象
<AUTHOR>
 */
export class ImportOpenQueryRequest {
  /**
   * 任务类型
分销导入-DISTRIBUTION_IMPORT
优惠导入-DISCOUNT_IMPORT
   */
  category?: string
  /**
   * 查询属性集合
   */
  metaPropertyList?: Array<MetaProperty>
  /**
   * 模糊查询的属性集合
   */
  dimMetaPropertyKeyList?: Array<string>
  /**
   * 模糊查询的拓展属性集合
   */
  dimExpandPropertyKeyList?: Array<string>
  /**
   * 额外附加查询参数
   */
  queryParam?: ImportOpenQueryParamRequest
}

export class TaskExecuteParamRequest {
  /**
   * 任务名称
   */
  taskName?: string
  /**
   * 任务类型
分销导入-DISTRIBUTION_IMPORT
优惠导入-DISCOUNT_IMPORT
   */
  category?: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState?: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult?: number
  /**
   * 执行时间（起始）
   */
  executeStartTime?: string
  /**
   * 执行时间（终止）
   */
  executeEndTime?: string
}

/**
 * @Description
<AUTHOR>
@Date 2024/10/28 8:48
 */
export class UserSnapshot {
  /**
   * 名字快照
   */
  nameSnapshot?: string
  /**
   * 身份证快照
   */
  idCardSnapshot?: string
  /**
   * 身份证类型快照
   */
  idCardTypeSnapshot?: number
  /**
   * 手机号快照
   */
  phoneSnapshot?: string
  /**
   * 工作单位地区code
   */
  unitAreaCodeSnapshot?: string
  /**
   * 单位统一信用编码
   */
  companyCodeSnapshot?: string
  /**
   * 单位名称快照
   */
  companyNameSnapshot?: string
  /**
   * 单位所在省份快照
   */
  unitProvinceSnapshot?: string
  /**
   * 单位所在城市快照
   */
  unitCitySnapshot?: string
  /**
   * 单位所在区县快照
   */
  unitAreaSnapshot?: string
}

/**
 * 各状态及执行结果对应数量
<AUTHOR>
 */
export class EachStateCount {
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  state: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 数量
   */
  count: number
}

/**
 * 单个属性元数据
<AUTHOR>
@since 2022/4/20
 */
export class MetaProperty1 {
  /**
   * 属性键
   */
  key: string
  /**
   * 属性值
   */
  value: string
}

/**
 * 数据行对象
<AUTHOR>
@since 2022/4/24
 */
export class MetaRow {
  /**
   * 每一行的数据
   */
  row: Array<MetaProperty1>
  /**
   * 子任务状态
0-已创建 1-已就绪 2-执行中 3-已完成
   */
  subTaskState: number
  /**
   * 产品优惠申请ID
   */
  distributionDiscountApplyId: string
  /**
   * 方案名称
   */
  schemeName: string
  /**
   * 优惠价格
   */
  discountPrice: number
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 订单状态
其余情况-null
未开通-0
开通中-1
已开通-2
   */
  orderState: number
  /**
   * 是否更新密码
   */
  updatePassword: boolean
  /**
   * 是否更新基础信息
   */
  updateBasicInfo: boolean
  /**
   * 学员状态
null-暂无记录
0-默认
1-新学员
2-已注册的学员
   */
  studentState: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 完成时间
   */
  completeTime: string
  /**
   * 错误日志
   */
  errorMessage: string
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
   */
  result: number
}

/**
 * 导入开班上传模板结果
异常code
500-接口异常
3003-表头校验失败
3004-excel表最大长度校验失败
<AUTHOR>
@since 2022/5/12
 */
export class ImportOpenResponse {
  /**
   * 批次订单号
   */
  batchOrderNo: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * 任务执行情况
<AUTHOR>
@since 2022/5/5
 */
export class TaskExecuteByPageResponse {
  /**
   * 任务编号
   */
  id: string
  /**
   * 【必填】平台编号
   */
  platformId: string
  /**
   * 【必填】平台版本编号
   */
  platformVersionId: string
  /**
   * 【必填】项目编号
   */
  projectId: string
  /**
   * 【必填】子项目编号
   */
  subProjectId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务分类
   */
  category: string
  /**
   * 所属批次单编号
   */
  batchNo: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
  /**
   * 处理信息
   */
  message: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 就绪时间
   */
  alreadyTime: string
  /**
   * 执行时间
   */
  executingTime: string
  /**
   * 完成时间
   */
  completedTime: string
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount>
}

export class MetaRowPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<MetaRow>
}

export class TaskExecuteByPageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TaskExecuteByPageResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出导入数据
   * @param mainTaskId 主任务ID
   * @return excel文件路径
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportExcel(
    params: { mainTaskId: string; onlyFail: boolean },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportExcel,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出所有导入数据
   * @param mainTaskId:
   * @return {@link String}
   * <AUTHOR> By Cb
   * @date 2022/5/24 17:35
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportExcelAllData(
    mainTaskId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportExcelAllData,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询导入数据列表，根据服务商进行筛选
   * mainTaskId 主任务id
   * commodityId 商品id
   * subTaskState 子任务状态
   * importStartTime 开始时间
   * importEndTime 结束时间
   * @param request 集体查询请求
   * @param page    分页信息
   * @return 分页响应数据
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findImportDataWithServicerByPage(
    params: { request?: ImportOpenQueryRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findImportDataWithServicerByPage,
    operation?: string
  ): Promise<Response<MetaRowPage>> {
    return commonRequestApi<MetaRowPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询指定批次下的任务执行情况，根据服务商进行筛选
   * @param page 分页信息
   * @return 执行情况
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTaskExecuteWithServicerResponseByPage(
    params: { param?: TaskExecuteParamRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findTaskExecuteWithServicerResponseByPage,
    operation?: string
  ): Promise<Response<TaskExecuteByPageResponsePage>> {
    return commonRequestApi<TaskExecuteByPageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询模板地址
   * 入参：任务类型
   * 分销导入-DISTRIBUTION_IMPORT
   * 优惠导入-DISCOUNT_IMPORT
   * @param query 查询 graphql 语法文档
   * @param category 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryImportOpenTemplatePath(
    category: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryImportOpenTemplatePath,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { category },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 优惠导入开通
   * @param importRequest 导入信息
   * @return 集体报名编号
   * @param mutate 查询 graphql 语法文档
   * @param importRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async discountImportOpen(
    importRequest: DiscountImportOpenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.discountImportOpen,
    operation?: string
  ): Promise<Response<ImportOpenResponse>> {
    return commonRequestApi<ImportOpenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { importRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销导入开通
   * @param importRequest 导入信息
   * @return 集体报名编号
   * @param mutate 查询 graphql 语法文档
   * @param importRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async distributionImportOpen(
    importRequest: DistributionImportOpenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.distributionImportOpen,
    operation?: string
  ): Promise<Response<ImportOpenResponse>> {
    return commonRequestApi<ImportOpenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { importRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
