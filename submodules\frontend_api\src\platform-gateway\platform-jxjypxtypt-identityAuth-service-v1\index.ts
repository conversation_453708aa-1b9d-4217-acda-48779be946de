import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjypxtypt-identityAuth-service-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-identityAuth-service-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * <AUTHOR>
 */
export class ApplyDistributorReAuthenticateRequest {
  /**
   * 访问令牌（网校域的令牌）
   */
  accessToken: string
}

/**
 * 身份认证统一响应
<AUTHOR>
 */
export class IdentityAuthenticationTokenResponse {
  /**
   * 身份凭证Token
   */
  identityAuthenticationToken: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请分销商身份再认证
   * @param request {accessToken:访问令牌（网校域的令牌）}
   * @return {code,message,identityAuthenticationToken}
   * code 说明如下:
   * 30001:请求头未携带分销商服务商信息
   * 30002:当前分销商不存在
   * 30003:当前服务商不是分销商类型
   * 30004:当前分销商服务商信息中缺失所属服务商ID
   * 30010:访问令牌凭证信息异常，未包含业务域信息
   * 30011:当前分销商不属于目标网校
   * 30012:再认证失败,没有成功签发身份凭证
   * 30013:再认证异常,没有成功签发身份凭证
   * 30021:无法获取客户端标识
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyDistributorReAuthenticate(
    request: ApplyDistributorReAuthenticateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyDistributorReAuthenticate,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
