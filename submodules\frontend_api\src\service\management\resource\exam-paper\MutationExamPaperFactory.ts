import CopyExamPaper from './mutation/CopyExamPaper'
import CreateExamPaper from './mutation/CreateExamPaper'
import UpdateExamPaper from './mutation/UpdateExamPaper'
import ExamPaperAction from './mutation/ExamPaperAction'
import ExamPublishPattern from '@api/service/management/resource/exam-paper/mutation/vo/common/ExamPublishPattern'
import AutomaticExamPaperVo from '@api/service/management/resource/exam-paper/mutation/vo/common/AutomaticExamPaperVo'
// 导出试卷Vo
export { AutomaticExamPaperVo }
class MutationExamPaperFactory {
  /**
   * @description: 创建试卷
   * @param {*}
   * @return {*}
   */
  getCreateExamPaper<T extends ExamPublishPattern = AutomaticExamPaperVo>(examPublishPattern: { new (): T }) {
    return new CreateExamPaper<T>(examPublishPattern)
  }

  /**
   * @description: 更新试卷
   * @param {string} 试卷id
   * @return {*}
   */
  getUpdateExamPaper(id: string) {
    return new UpdateExamPaper(id)
  }

  /**
   * @description: 试卷列表基础操作
   * @param {id} 试卷id
   * @return {*}
   */
  getExamPaperAction(id: string) {
    return new ExamPaperAction(id)
  }

  /**
   * @description: 复制试卷
   * @return {*}
   */
  getCopyExamPaper(id: string) {
    return new CopyExamPaper(id)
  }
}
export default new MutationExamPaperFactory()
