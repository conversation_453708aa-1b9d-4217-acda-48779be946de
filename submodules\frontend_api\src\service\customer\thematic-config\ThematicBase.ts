import Training from '@api/platform-gateway/platform-training-channel-v1'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import { RuleTypeEnum } from '@api/service/customer/thematic-config/enum/RuleType'
import IndustryRule from '@api/service/customer/thematic-config/model/IndustryRule'
class ThematicManagementItemBase {
  /**
   * 配置专题ID
   */
  id = ''
  /**
   * 是否开启专题
   */
  isOpen = false
  /**
   * 是否网校展示入口
   */
  isShow = false
  /**
   * 名称
   */
  name = ''
  /**
   * 引导语
   */
  guide = ''
  /**
   * 网校展示专题方案
   */
  showSpecialScheme = false
  /**
   * 行业规则
   */
  industryRule: Array<IndustryRule> = []
  /**
   * 重复请求时间戳
   */
  repeatRequestTime = 0
  /**
   * 获取基础配置
   */
  async getBaseConfig() {
    const persistentDataTime = Number(ConfigCenterModule.getFrontendApplication(frontendApplication.persistentDataTime))
    if (new Date().getTime() - this.repeatRequestTime > persistentDataTime * 60 * 1000) {
      const res = await Training.getTrainingChannelSetting()
      this.id = res.data.id
      this.isOpen = res.data.isOpenTrainingChannel
      this.isShow = res.data.isShowEntry
      this.name = res.data.name
      this.guide = res.data.guideExpression
      this.showSpecialScheme = res.data.isShowTrainingChannelScheme
      this.industryRule = []
      await Promise.all([QueryIndustry.getIndustryForestage(), QueryIndustry.queryIndustry()])
      QueryIndustry.industryDICTList.forEach(item => {
        const vo = new IndustryRule(item.id)
        const enableIndustry = QueryIndustry.industryList.find(ite => ite.id === item.id)
        const configIndustry = res.data.trainingChannelSettingExtList?.find(ite => ite.objectId === item.id)
        if (enableIndustry) {
          vo.isEnable = true
          vo.industryName = enableIndustry.name
        }
        if (configIndustry) {
          vo.isChecked = true
          vo.type = configIndustry.value as RuleTypeEnum
        }
        if (item.id === IndustryIdEnum.RS) {
          this.industryRule.unshift(vo)
        } else {
          this.industryRule.push(vo)
        }
      })
      this.repeatRequestTime = new Date().getTime()
    }
  }
}

export default new ThematicManagementItemBase()
