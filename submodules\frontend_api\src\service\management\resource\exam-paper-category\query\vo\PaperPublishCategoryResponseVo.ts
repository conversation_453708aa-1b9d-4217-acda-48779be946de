import { PaperPublishConfigureCategoryResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'

class PaperPublishCategoryResponseVo extends PaperPublishConfigureCategoryResponse {
  /**
   * 试卷分类ID
   */
  id = ''
  /**
   * 分类名称
   */
  name = ''
  /**
   * 父级分类id
   */
  parentCategoryId = ''
  /**
   * 父级分类名称
   */
  parentCategoryName = ''

  /**
   * 创建人id
   */
  createUserId = ''
  /**
   * 创建时间
   */
  createTime = ''

  // 转为Vo模型
  static from(data: PaperPublishConfigureCategoryResponse) {
    const params = new PaperPublishCategoryResponseVo()
    params.id = data.id
    params.name = data.name
    params.parentCategoryId = data.parentCategory?.id
    params.createUserId = data.createUserId
    params.createTime = data.createTime
    params.parentCategoryName = data.parentCategory?.name
    return params
  }
}

export default PaperPublishCategoryResponseVo
