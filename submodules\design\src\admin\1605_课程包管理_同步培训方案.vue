<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">课程包修改</el-breadcrumb-item>
      <el-breadcrumb-item>同步培训方案</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom mb0">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="6">
              <el-form-item label="方案名称">
                <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="6">
              <el-form-item label="数据状态">
                <el-select v-model="select" clearable placeholder="方案和课程包课程是否一致">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="6">
              <el-form-item label="同步状态">
                <el-select v-model="select" clearable placeholder="请选择同步状态">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <div class="m-tit is-small">
          <span class="tit-txt f-flex-sub">
            同步培训方案（共100个方案使用，已同步 10个，未同步70个，同步中10个，同步失败10个）
          </span>
          <el-button type="primary">批量同步</el-button>
        </div>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table">
          <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="培训方案名称" min-width="300" fixed="left">
            <template>培训方案名称培训方案名称培训方案名称</template>
          </el-table-column>
          <el-table-column label="数据状态" min-width="140" align="center">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-tag type="danger">不一致</el-tag>
              </div>
              <div v-else>
                <el-tag type="success">一致</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="同步状态" min-width="140">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-badge is-dot type="info" class="badge-status">未同步</el-badge>
              </div>
              <div v-else-if="scope.$index === 1">
                <el-badge is-dot type="primary" class="badge-status">同步中</el-badge>
              </div>
              <div v-else-if="scope.$index === 2">
                <el-badge is-dot type="danger" class="badge-status">同步失败</el-badge>
                <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                  <i class="el-icon-warning m-tooltip-icon f-cr f-ml5"></i>
                  <div slot="content">失败原因：这是失败原因失败原因这是失败原因</div>
                </el-tooltip>
              </div>
              <div v-else>
                <el-badge is-dot type="success" class="badge-status">已同步</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="最新同步时间" min-width="180">
            <template>2020-11-11 12:20:20</template>
          </el-table-column>
          <el-table-column label="操作" width="240" align="center" fixed="right">
            <template>
              <el-button type="text" size="mini">同步日志</el-button>
              <el-button type="text" size="mini">查看方案</el-button>
              <el-button type="text" size="mini">同步方案</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <div class="m-btn-bar f-mt10 f-tc is-sticky-1">
        <el-button>放弃编辑</el-button>
        <el-button>返回上一步</el-button>
        <el-button type="primary">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
