import QueryCoursePackage from '@api/service/management/resource/course-package/query/QueryCoursePackage'
import UpdateCoursePackageVo from '@api/service/management/resource/course-package/mutation/vo/UpdateCoursePackageVo'
import MutationUpdateCoursePackage from '@api/service/management/resource/course-package/mutation/MutationUpdateCoursePackage'
import MutationCreateCoursePackage from '@api/service/management/resource/course-package/mutation/MutationCreateCoursePackage'
import MutationBizCoursePackage from '@api/service/management/resource/course-package/mutation/MutationBizCoursePackage'
import ImportCoursePackage from '@api/service/management/resource/course-package/mutation/ImportCoursePackage'
import MutationSyncCoursePackage from './mutation/MutationSyncCoursePackage'
import UpdateCoursePackageByPageVo from '@api/service/management/resource/course-package/mutation/vo/UpdateCoursePackageByPageVo'
import MutationSyncAllUsedCoursePackage from '@api/service/management/resource/course-package/mutation/MutationSyncAllUsedCoursePackage'
import { CheckCoursePackageRequest } from '@api/ms-gateway/ms-course-resource-v1'
import MsCourseResourceV1 from '@api/ms-gateway/ms-course-resource-v1'

/**
 * 课程包模型工程
 */
class CoursePackageFactory {
  queryCoursePackage: QueryCoursePackage = new QueryCoursePackage()

  get createCoursePackage() {
    return new MutationCreateCoursePackage()
  }

  get importCoursePackage() {
    return new ImportCoursePackage()
  }

  get bizCoursePackage() {
    return (id: string) => new MutationBizCoursePackage(id)
  }

  get syncCoursePackage() {
    return (coursePackageId: string) => new MutationSyncCoursePackage(coursePackageId)
  }

  get syncAllUsedCoursePackage() {
    return (coursePackageId: string) => new MutationSyncAllUsedCoursePackage(coursePackageId)
  }

  /**
   * 查课程包名称是否重复
   * @param id
   */
  async getCheckPackage(courseName: string, id?: string) {
    const request = new CheckCoursePackageRequest()
    request.name = courseName
    request.id = id
    request.type = 1
    const { data } = await MsCourseResourceV1.checkCoursePackageForName(request)
    return data
  }

  /**
   * 获取更新课程包的模型
   * @param id
   */
  async getUpdateCoursePackage(id: string): Promise<MutationUpdateCoursePackage> {
    // 1-获取基本的课程包详情
    const coursePackageDetailVo = await this.queryCoursePackage.queryCoursePackageById(id)
    const mutationUpdateCoursePackage = new MutationUpdateCoursePackage()
    // 2-课程包信息填充
    const updateCoursePackageVo = UpdateCoursePackageByPageVo.from(coursePackageDetailVo)
    // 3-初始化更新课程包模型
    await updateCoursePackageVo.initData(coursePackageDetailVo.id)
    mutationUpdateCoursePackage.updateCoursePackageVo = updateCoursePackageVo
    console.log('mutationUpdateCoursePackage', mutationUpdateCoursePackage)
    return mutationUpdateCoursePackage
  }
}

export default CoursePackageFactory
