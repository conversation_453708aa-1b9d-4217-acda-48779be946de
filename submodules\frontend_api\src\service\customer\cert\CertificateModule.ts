import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import Response, { ResponseStatus } from '@api/Response'
import { Role, RoleType } from '@api/Secure'
import platformCertGateway, { CertificateRenderResponse, CertificateResponsePage } from '@api/gateway/PlatformCert'
import Vue from 'vue'
import CertificateRenderData from '@api/service/common/models/cert/CertificateRenderData'
import CertificateRenderInfo from '@api/service/common/models/cert/CertificateRenderInfo'
import CertificateRenderModel from '@api/service/common/models/cert/CertificateRenderModel'
import PageRequest from '@api/service/common/models/common/PageRequest'
import PageData from '@api/service/common/models/common/PageData'
import CertificateInfo from '@api/service/customer/cert/models/CertificateInfo'
import LearningSchemeModule from '@api/service/customer/myscheme/LearningSchemeModule'

interface CertificateState {
  /**
   * 当前登陆用户购买的期数对应的培训证明预览数据
   */
  previewDataCache: { [key: string]: CertificateRenderInfo }
  /**
   * 当前登陆用户的培训证明分页数据
   */
  certificatePageData: PageData<CertificateInfo>
}

@Module({ namespaced: true, store, dynamic: true, name: 'CustomerCertificateModule' })
class CertificateModule extends VuexModule implements CertificateState {
  previewDataCache = {}
  certificatePageData = new PageData<CertificateInfo>()

  static convertCertificateRenderResponseToCertificateRenderInfo(
    response: CertificateRenderResponse,
    info: CertificateRenderInfo
  ) {
    info.templateUrl = response.templateUrl
    info.model = new CertificateRenderModel()
    info.model.real = response.model.real
    response.model.list.forEach(dataResponse => {
      const data = new CertificateRenderData()
      Object.assign(data, dataResponse)
      info.model.add(data)
    })
  }

  /**
   * 获取当前登陆用户的培训证明分页数据
   */
  @Action
  @Role([RoleType.user])
  async getStudentCertificatePage(page: PageRequest): Promise<ResponseStatus> {
    const response = await platformCertGateway.getStudentCertificatePage(page)
    if (response.status.isSuccess()) {
      this.setCertificatePage(response.data)
    }
    return response.status
  }

  @Mutation
  private setCertificatePage(page: CertificateResponsePage) {
    this.certificatePageData.total = page.totalSize
    this.certificatePageData.pageCount = page.totalPageSize
    this.certificatePageData.data = page.currentPageData.map(response => {
      const info = new CertificateInfo()
      Object.assign(info, response)
      const scheme = LearningSchemeModule.getScheme(info.schemeId)
      //增加方案名称、SKU信息、获得学时
      info.schemeName = scheme.schemeName
      info.year = scheme.year
      info.trainingTypeName = scheme.trainingTypeName
      info.workTypeName = scheme.workTypeName
      info.isOpenPrintCertificate = scheme.isOpenPrintCertificate
      info.grade = scheme.achieve?.grade
      return info
    })
  }

  /**
   * 获取当前登陆用户购买的指定期数的证书预览数据
   * @param issueId
   */
  @Action
  @Role([RoleType.user])
  async getCertificatePreviewData(issueId: string): Promise<Response<CertificateRenderInfo>> {
    const resultResponse = new Response<CertificateRenderInfo>()
    const model = this.previewDataCache[issueId]
    if (model) {
      resultResponse.data = model
    } else {
      const response = await platformCertGateway.getStudentCertificateData(issueId)
      const data = response.data
      resultResponse.status = response.status
      if (data) {
        const info = new CertificateRenderInfo()
        CertificateModule.convertCertificateRenderResponseToCertificateRenderInfo(data, info)
        resultResponse.data = info
      }
      this.setCache({ issueId, model })
    }
    return resultResponse
  }

  @Mutation
  private setCache(payload: { issueId: string; model: CertificateRenderInfo }) {
    Vue.set(this.previewDataCache, payload.issueId, payload.model)
  }

  get getPreviewData() {
    return (issueId: string): CertificateRenderInfo => {
      return this.previewDataCache[issueId]
    }
  }
}

export default getModule(CertificateModule)
