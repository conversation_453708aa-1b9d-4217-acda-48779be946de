import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjy-examquestion-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjy-examquestion-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum FillAnswerType {
  disarray = 'disarray',
  sequence = 'sequence',
  sequenceRelate = 'sequenceRelate'
}

// 类

/**
 * 正确率评定方式
<AUTHOR>
 */
export class CorrectRateEvaluatePatternRequest implements EvaluatePatternRequest {
  /**
   * 评定方式类型
1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
@see EvaluatePatternTypes
   */
  type: number
}

/**
 * 评定方式
<AUTHOR>
 */
export class EvaluatePatternRequest {
  /**
   * 评定方式类型
1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
@see EvaluatePatternTypes
   */
  type: number
}

/**
 * 无评定方式
<AUTHOR>
 */
export class NoneEvaluatePatternRequest implements EvaluatePatternRequest {
  /**
   * 评定方式类型
1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
@see EvaluatePatternTypes
   */
  type: number
}

/**
 * 计分评定方式
<AUTHOR>
 */
export class OnlyScoringEvaluatePatternRequest implements EvaluatePatternRequest {
  scoringDescribes?: Array<ScoringDescribe>
  /**
   * 评定方式类型
1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
@see EvaluatePatternTypes
   */
  type: number
}

/**
 * <AUTHOR>
 */
export class QuestionScoreRequest {
  /**
   * 试题id
   */
  questionId?: string
  /**
   * 分数
   */
  score: number
}

/**
 * 试题分数设置信息
<AUTHOR>
 */
export class QuestionScoreSettingRequest {
  /**
   * 大题序号
   */
  sequence?: number
  /**
   * 试题类型
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 每题平均分
   */
  eachQuestionScore: number
  /**
   * 具体试题分数
   */
  questionScores?: Array<QuestionScoreRequest>
}

/**
 * 分值评定方式
<AUTHOR>
 */
export class ScoreEvaluatePatternRequest implements EvaluatePatternRequest {
  /**
   * 总分
   */
  totalScore: number
  /**
   * 合格分数
   */
  qualifiedScore: number
  /**
   * 试题分数
   */
  questionScores?: Array<QuestionScoreSettingRequest>
  /**
   * 多选提漏选得分模式
0:不得分|1：的全部分数|2：得一半分数|3：每个选项按平均得分
@see MultipleQuestionMissScorePatterns
   */
  multipleMissScorePattern: number
  /**
   * 评定方式类型
1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
@see EvaluatePatternTypes
   */
  type: number
}

/**
 * 单选题计分描述
 */
export class RadioScoringDescribe implements ScoringDescribe {
  /**
   * 分数来源类型
@see  ScoreSourceTypes
   */
  scoreSourceTypes?: number
  type?: number
}

/**
 * 计分描述基类
<AUTHOR>
 */
export class ScoringDescribe {
  type?: number
}

/**
 * 选择题答案选项实体
<AUTHOR>
 */
export class ChooseAnswerOptionRequest {
  /**
   * 答案ID
   */
  id: string
  /**
   * 答案内容
   */
  content: string
  /**
   * 选项建议分数
   */
  suggestionScore?: number
  /**
   * 是否允许填空
   */
  enableFillContent?: boolean
  /**
   * 填空是否必填
   */
  mustFillContent?: boolean
}

/**
 * <AUTHOR> create 2021/6/29 14:03
 */
export class CreateQuestionRequest {
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * <AUTHOR> create 2021/6/29 14:03
 */
export class UpdateQuestionRequest {
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * <AUTHOR> create 2021/6/28 14:13
 */
export class CreateAskQuestionRequest implements CreateQuestionRequest {
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * <AUTHOR> create 2021/6/28 14:13
 */
export class UpdateAskQuestionRequest implements UpdateQuestionRequest {
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * <AUTHOR> create 2021/6/28 14:43
 */
export class ChildQuestionCreateInfoRequest {
  /**
   * 子题序号
   */
  no: number
  /**
   * 试题内容
   */
  question?: CreateQuestionRequest
}

/**
 * <AUTHOR> create 2021/6/28 14:43
 */
export class ChildQuestionUpdateInfoRequest {
  /**
   * 子题序号
   */
  no: number
  /**
   * 试题内容
   */
  question?: UpdateQuestionRequest
  /**
   * 新增试题
   */
  newQuestion?: CreateQuestionRequest
}

/**
 * <AUTHOR> create 2021/6/28 14:43
 */
export class CreateFatherQuestionRequest implements CreateQuestionRequest {
  /**
   * 子题集合
   */
  childQuestions: Array<ChildQuestionCreateInfoRequest>
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * <AUTHOR> create 2021/6/28 14:43
 */
export class UpdateFatherQuestionRequest implements UpdateQuestionRequest {
  /**
   * 子题集合
   */
  childQuestions: Array<ChildQuestionUpdateInfoRequest>
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * 填空题创建命令
<AUTHOR> create 2021/6/28 14:09
 */
export class CreateFillQuestionRequest implements CreateQuestionRequest {
  /**
   * 填空数
   */
  fillCount: number
  /**
   * 正确答案
   */
  correctAnswer?: FillAnswerRequest
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 散乱无序填空题答案实体
<AUTHOR>
 */
export class DisarrayFillAnswerRequest implements FillAnswerRequest {
  /**
   * 正确答案集合
   */
  correctAnswers: Array<string>
  /**
   * 答案类型
   */
  type: FillAnswerType
}

/**
 * 填空题答案基类
<AUTHOR>
 */
export class FillAnswerRequest {
  /**
   * 答案类型
   */
  type: FillAnswerType
}

/**
 * <AUTHOR> create 2021/6/29 15:41
 */
export class FillCorrectAnswers {
  /**
   * 空格位置
   */
  blankNo: number
  /**
   * 答案备选项
   */
  answers?: Array<string>
}

/**
 * 按序填空题答案
<AUTHOR>
 */
export class SequenceFillAnswerRequest implements FillAnswerRequest {
  correctAnswers: Array<FillCorrectAnswers>
  /**
   * 答案类型
   */
  type: FillAnswerType
}

/**
 * 按序关联填空题答案实体
<AUTHOR>
 */
export class SequenceRateFillAnswerRequest implements FillAnswerRequest {
  /**
   * 正确答案集合
   */
  correctAnswers?: Array<SequenceFillAnswerRequest>
  /**
   * 答案类型
   */
  type: FillAnswerType
}

/**
 * 填空题创建命令
<AUTHOR> create 2021/6/28 14:09
 */
export class UpdateFillQuestionRequest implements UpdateQuestionRequest {
  /**
   * 填空数
   */
  fillCount: number
  /**
   * 正确答案
   */
  correctAnswer?: FillAnswerRequest
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * 多选题创建命令
<AUTHOR> create 2021/6/28 14:07
 */
export class CreateMultipleQuestionRequest implements CreateQuestionRequest {
  /**
   * 可选答案列表【必填】
   */
  answerOptions: Array<ChooseAnswerOptionRequest>
  /**
   * 正确答案ID集合【必填】
   */
  correctAnswerIds: Array<string>
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 多选题创建命令
<AUTHOR> create 2021/6/28 14:07
 */
export class UpdateMultipleQuestionRequest implements UpdateQuestionRequest {
  /**
   * 可选答案列表【必填】
   */
  answerOptions: Array<ChooseAnswerOptionRequest>
  /**
   * 正确答案ID集合【必填】
   */
  correctAnswerIds: Array<string>
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * 判断题创建命令
<AUTHOR> create 2021/6/28 14:05
 */
export class CreateOpinionQuestionRequest implements CreateQuestionRequest {
  /**
   * 正确答案【必填】
   */
  correctAnswer: boolean
  /**
   * 正确文本【必填】
   */
  correctAnswerText?: string
  /**
   * 不正确文本【必填】
   */
  incorrectAnswerText?: string
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 判断题创建命令
<AUTHOR> create 2021/6/28 14:05
 */
export class UpdateOpinionQuestionRequest implements UpdateQuestionRequest {
  /**
   * 正确答案【必填】
   */
  correctAnswer: boolean
  /**
   * 正确文本【必填】
   */
  correctAnswerText: string
  /**
   * 不正确文本【必填】
   */
  incorrectAnswerText: string
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * 单选题创建命令
<AUTHOR> create 2021/6/28 9:39
 */
export class CreateRadioQuestionRequest implements CreateQuestionRequest {
  /**
   * 可选答案列表【必填】
   */
  answerOptions: Array<ChooseAnswerOptionRequest>
  /**
   * 正确答案ID【必填】
   */
  correctAnswerId?: string
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 单选题创建命令
<AUTHOR> create 2021/6/28 9:39
 */
export class UpdateRadioQuestionRequest implements UpdateQuestionRequest {
  /**
   * 可选答案列表
   */
  answerOptions: Array<ChooseAnswerOptionRequest>
  /**
   * 正确答案ID
   */
  correctAnswerId?: string
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * @Author: chenzeyu
@CreateTime: 2024-07-29  16:12
@Description: 量表题创建请求
 */
export class CreateScaleQuestionRequest implements CreateQuestionRequest {
  /**
   * 量表类型
@see ScaleTypes
   */
  scaleType: number
  /**
   * 程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  startDegree?: string
  /**
   * 程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  endDegree?: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * @Author: chenzeyu
@CreateTime: 2024-07-30  15:40
@Description: 量表题修改请求
 */
export class UpdateScaleQuestionRequest implements UpdateQuestionRequest {
  /**
   * 量表类型
@see ScaleTypes
   */
  scaleType: number
  /**
   * 程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  startDegree?: string
  /**
   * 程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  endDegree?: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * <AUTHOR> create 2021/6/3 17:35
 */
export class CopyQuestionnaireQuestion {
  /**
   * 试题ID
   */
  questionId?: string
  /**
   * 分数，-1表示不为分数评定方式为
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题类型
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
   */
  answerRequired?: boolean
  /**
   * 教师评价题标签code，非教师评价题不用传
   */
  teacherEvaluateCode?: string
  /**
   * 复制的试题内容
   */
  copyQuestionContent?: CreateQuestionRequest
}

/**
 * 出卷模式基类
<AUTHOR> create 2021/8/20 15:05
 */
export class CopyQuestionnaireRequest {
  /**
   * 出卷配置名称
   */
  name?: string
  /**
   * 适用范围 用于筛选自定义的分类
@see com.fjhb.domain.exam.api.consts.UsageScopes
   */
  usageScope: number
  /**
   * 调查问卷类型
@see com.fjhb.domain.exam.api.consts.QuestionnairePaperPublishConfigureType
   */
  questionnaireType?: number
  /**
   * 出卷模式，固定卷
   */
  publishPattern?: QuestionnairePaperRequest
  /**
   * 分类id
   */
  paperPublishConfigureCategoryId?: string
  /**
   * 是否是草稿    1-是 2-不是
   */
  isDraft: number
}

/**
 * <AUTHOR> create 2021/6/3 17:37
 */
export class QuestionGroupDTO {
  /**
   * 序号
   */
  sequence: number
  /**
   * 试题类型
   */
  questionType: number
  /**
   * 大题名称
   */
  groupName?: string
  /**
   * 每题平均分数，-1表示不为分数评定方式
   */
  eachQuestionScore: number
}

/**
 * 调查问卷固定卷出卷模式
<AUTHOR> create 2021/8/20 16:08
 */
export class QuestionnairePaperRequest {
  /**
   * 试卷id
   */
  id?: string
  /**
   * 出卷模式类型
@see PublishPatterns
   */
  type: number
  /**
   * 试卷名称
   */
  name?: string
  /**
   * 描述
   */
  description?: string
  /**
   * 作答时长
   */
  timeLength: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 大题集合
   */
  groups?: Array<QuestionGroupDTO>
  /**
   * 试题集合
   */
  questions?: Array<CopyQuestionnaireQuestion>
  /**
   * 评定方式
   */
  evaluatePattern?: EvaluatePatternRequest
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async copyQuestionnaire(
    request: CopyQuestionnaireRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.copyQuestionnaire,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 非教师评价题创建
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createQuestionnaireQuestion(
    request: CreateQuestionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createQuestionnaireQuestion,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 教师评价多选题创建
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createTeacherEvaluationMultipleChoiceQuestionnaireQuestion(
    request: CreateQuestionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createTeacherEvaluationMultipleChoiceQuestionnaireQuestion,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 教师评价单选题创建
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createTeacherEvaluationSingleChoiceQuestionnaireQuestion(
    request: CreateQuestionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createTeacherEvaluationSingleChoiceQuestionnaireQuestion,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeQuestionnaireQuestion(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeQuestionnaireQuestion,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 非教师评价题更新
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateQuestionnaireQuestion(
    request: UpdateQuestionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateQuestionnaireQuestion,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 教师评价多选题更新
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTeacherEvaluationMultipleChoiceQuestionnaireQuestion(
    request: UpdateQuestionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTeacherEvaluationMultipleChoiceQuestionnaireQuestion,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 教师评价单选题更新
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTeacherEvaluationSingleChoiceQuestionnaireQuestion(
    request: UpdateQuestionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTeacherEvaluationSingleChoiceQuestionnaireQuestion,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
