import { AdministratorStatusEnum } from '@api/service/training-institution/online-school/enum/AdministratorStatusEnum'
import MsBasicdataDomain, {
  ResetAdminPasswordRequest,
  ResetPasswordRequest,
  UpdateAdministratorAccountRequest,
  UpdateOnlineAdministratorAccountRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { ContractProviderAdminInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

export default class AdministratorModel {
  /**
   * 用户id
   */
  administratorId: string = undefined

  /**
   * 账户id
   */
  accountId: string = undefined

  /**
   * 管理员姓名
   */
  administratorName: string = undefined

  /**
   * 管理员账号
   */
  account: string = undefined

  /**
   * 手机号
   */
  phone: string = undefined

  /**
   * 角色名称
   */
  roleName: string = undefined

  /**
   * 状态
   */
  status: AdministratorStatusEnum = undefined

  static from(dto: ContractProviderAdminInfoResponse) {
    // TODO 模型转化
    const vo = new AdministratorModel()
    vo.administratorId = dto?.userInfo?.userId
    vo.accountId = dto?.accountInfo?.accountId
    vo.administratorName = dto?.userInfo?.userName
    vo.account = dto.authenticationList?.find(item => item.identityType === 1)?.identity || ''
    vo.phone = dto.userInfo?.phone
    vo.roleName = dto.roleList[0]?.roleName
    vo.status = dto.accountInfo?.status
    return vo
  }

  /**
   * 启停用
   */
  async changeAdministratorStatus() {
    if (this.status === AdministratorStatusEnum.ENABLE) {
      // 停用
      const res = await MsBasicdataDomain.freezeAccount(this.accountId)

      return res
    }

    if (this.status === AdministratorStatusEnum.DISABLE) {
      // 启用
      const res = await MsBasicdataDomain.resumeAccount(this.accountId)

      return res
    }
  }

  /**
   * 重置密码
   * @param password 新密码
   */
  async resetPassword(newPassword: string) {
    const request = new ResetAdminPasswordRequest()
    request.password = newPassword
    request.accountId = this.accountId

    const res = await MsBasicdataDomain.ResetAdminPasswordWithResponse(request)

    return res
  }

  /**
   * 更新账户
   * @param.administratorName 管理员姓名
   * @param.account 管理员账号
   * @param.phone 手机号（不填或填空串表示不更新手机号）
   * @param.schoolId 网校id
   */
  async updateAdministrator(param: { administratorName: string; account: string; phone?: string; schoolId: string }) {
    const request = new UpdateOnlineAdministratorAccountRequest()
    request.serviceId = param.schoolId
    request.accountId = this.accountId
    request.name = param.administratorName
    request.accountName = param.account
    if (param.phone) {
      request.updateAll = true
      request.phone = param.phone
    }

    const res = await MsBasicdataDomain.updateOnlineAdministratorAccount(request)
    // TODO 返回code要确定
    return res
  }
}
