import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/jxjy-data-export-gateway-forestage'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'jxjy-data-export-gateway-forestage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum BatchOrderSortField {
  BATCH_ORDER_UN_CONFIRMED_TIME = 'BATCH_ORDER_UN_CONFIRMED_TIME',
  BATCH_ORDER_COMMIT_TIME = 'BATCH_ORDER_COMMIT_TIME'
}

// 类

/**
 * @Description 范围查询条件
<AUTHOR>
@Date 8:51 2022/5/23
 */
export class BigDecimalScopeExcelRequest {
  /**
   * result >&#x3D; begin
   */
  begin?: number
  /**
   * result <&#x3D; end
   */
  end?: number
}

/**
 * 范围查询条件
<AUTHOR>
@version 1.0
@date 2022/5/7 15:34
 */
export class DateScopeExcelRequest {
  /**
   * result >&#x3D; begin
   */
  begin?: string
  /**
   * result <&#x3D; end
   */
  end?: string
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class BatchOrderRequest {
  /**
   * 批次单号集合
   */
  batchOrderNoList?: Array<string>
  /**
   * 批次单基本信息查询参数
   */
  basicData?: BatchOrderBasicDataRequest
  /**
   * 批次单支付信息查询参数
   */
  payInfo?: OrderPayInfoRequest
  /**
   * 批次单创建人查询参数
   */
  creatorIdList?: Array<string>
  /**
   * 是否已经申请发票
   */
  isInvoiceApplied?: boolean
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 批次单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class BatchOrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: BatchOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 批次单基本信息查询参数
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderBasicDataRequest {
  /**
   * 批次单状态
0: 未确认，批次单初始状态
1: 正常
2: 交易完成
3: 交易关闭
4: 提交处理中 提交处理完成后，变更为NORMAl
5: 取消处理中
@see BatchOrderStatus
   */
  batchOrderStatusList?: Array<number>
  /**
   * 批次单状态变更时间
   */
  batchOrderStatusChangeTime?: BatchOrderStatusChangeTimeRequest
  /**
   * 批次单支付状态
<p>
0：未支付
1：支付中
2：已支付
@see BatchOrderPaymentStatus
   */
  batchOrderPaymentStatusList?: Array<number>
  /**
   * 批次单发货状态
0: 未发货
1: 发货中
2: 已发货
@see BatchOrderDeliveryStatus
   */
  batchOrderDeliveryStatusList?: Array<number>
  /**
   * 批次单价格范围
<p> 查询非0元批次单 begin填0.01
   */
  batchOrderAmountScope?: BigDecimalScopeExcelRequest
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 批次单状态变更时间查询参数
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderStatusChangeTimeRequest {
  /**
   * 未确认
   */
  unConfirmed?: DateScopeExcelRequest
  /**
   * 正常
   */
  normal?: DateScopeExcelRequest
  /**
   * 交易成功
   */
  completed?: DateScopeExcelRequest
  /**
   * 已关闭
   */
  closed?: DateScopeExcelRequest
  /**
   * 提交中
   */
  committing?: DateScopeExcelRequest
  /**
   * 取消处理中
   */
  canceling?: DateScopeExcelRequest
}

/**
 * 订单支付信息相关查询参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderPayInfoRequest {
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 付款类型
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
   */
  paymentOrderTypeList?: Array<number>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 集体缴费管理员导出批次单明细
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchOrderDetailInMyself(
    params: { request?: BatchOrderRequest; sort?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchOrderDetailInMyself,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
