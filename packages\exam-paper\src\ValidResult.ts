/**
 *
 *  表单校验结果
 * @author: eleven
 * @date: 2020/3/20
 */
import { Code } from '@hbfe/jxjy-admin-examPaper/src/Code'

export class ValidResult {
  code: number
  message: string

  constructor() {
    this.code = Code.SUCCESS
  }

  /**
   * 是否校验通过
   */
  isSuccess(): boolean {
    return this.code === Code.SUCCESS
  }

  /**
   * 设定code
   * @param code
   */
  withCode(code: number) {
    this.code = code
    return this
  }

  /**
   *  添加一个默认的业务校验失败的code
   */
  withBusinessErrorCode() {
    this.code = Code.WARN
    return this
  }

  /**
   * 空值error
   */
  withNullError(message: string) {
    this.message = message
    this.code = Code.NULL
    return this
  }

  /**
   * 输入不合法
   */
  withInvalidError(message: string) {
    this.code = Code.INVALID
    this.message = message
    return this
  }

  /**
   * 设定错误提示
   * @param message
   */
  withMessage(message: string) {
    this.message = message
    return this
  }

  withBusinessError(message: string) {
    this.message = message
    this.code = Code.WARN
  }
}
