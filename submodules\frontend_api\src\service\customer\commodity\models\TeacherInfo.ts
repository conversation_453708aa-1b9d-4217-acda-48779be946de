/**
 * 商品中显示的教师信息
 */
import { TeacherSimpleDTO } from '@api/gateway/PlatformTrade'

class TeacherInfo {
  /**
   * 教师id
   */
  id: string
  /**
   * 教师姓名
   */
  name: string
  /**
   * 讲师头像照片地址
   */
  photo: string
  /**
   * 教师简介
   */
  abouts: string

  static from(dto: TeacherSimpleDTO): TeacherInfo {
    const info = new TeacherInfo()
    info.id = dto.id
    info.name = dto.name
    info.photo = dto.photo
    info.abouts = dto.abouts
    return info
  }
}

export default TeacherInfo
