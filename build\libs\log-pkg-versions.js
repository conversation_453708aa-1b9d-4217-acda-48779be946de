const fastglob = require('fast-glob')
const fs = require('fs')
const path = require('path')

// 获取所有子包的版本信息
const getVersions = () => {
  const dir = path.resolve(process.cwd(), './packages')
  const files = fastglob.sync('**/package.json', { cwd: dir })
  return files.reduce((accumulator, currentFilePath) => {
    const pkgPath = path.resolve(dir, currentFilePath)
    const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf8'))
    return {
      ...accumulator,
      [pkg.name]: pkg.version
    }
  }, {})
}
console.log(JSON.stringify(getVersions(), null, 2))
