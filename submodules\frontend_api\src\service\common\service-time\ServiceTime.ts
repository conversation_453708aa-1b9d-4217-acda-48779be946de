import PlatformInformation from '@api/platform-gateway/platform-information-gateway'
import moment from 'moment'
class ServiceTime {
  /**
   * 当前服务器时间（YYYY-MM-DD HH:mm:ss）
   */
  currentServiceTime = ''

  /**
   * 获取服务器时间
   */
  async getServiceTime() {
    const res = await PlatformInformation.getCurrentTime()

    this.currentServiceTime = res?.data ?? ''

    return this.currentServiceTime
  }
}

export default new ServiceTime()
