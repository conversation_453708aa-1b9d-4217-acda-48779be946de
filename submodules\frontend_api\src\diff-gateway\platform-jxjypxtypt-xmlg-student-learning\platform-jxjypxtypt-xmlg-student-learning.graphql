"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""申请学习"""
	applyStudentLearningToken(request:XMLGApplyStudentLearningTokenRequest):XMLGApplyStudentLearningTokenResponse
	"""验证是否允许学习"""
	validAllowToLearning(request:XMLGValidAllowToLearningRequest):Boolean
}
input XMLGApplyStudentLearningTokenRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.request.XMLGApplyStudentLearningTokenRequest") {
	"""参训资格ID"""
	qualificationId:String!
	"""学习方式ID"""
	learningId:String!
	"""学习课程所属类别
		1：专业课，2：公需课
	"""
	learnType:Int!
}
input XMLGValidAllowToLearningRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.request.XMLGValidAllowToLearningRequest") {
	qualificationId:String!
	"""学习课程所属类别
		1：专业课，2：公需课
	"""
	learnType:Int!
}
type XMLGApplyStudentLearningTokenResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.response.XMLGApplyStudentLearningTokenResponse") {
	token:String
}

scalar List
