<template>
  <div>
    <template v-if="$hasPermission('queryList')" desc="查询" actions="initPage,getTrainingMajorList">
      <div class="f-p15" v-loading="uiConfig.pageLoading">
        <el-card shadow="never" class="m-card f-mb15" v-if="this.notLinkageList.length">
          <div slot="header" class="">
            <span class="tit-txt">{{ headerTitle }}</span>
          </div>
          <div class="m-attribute">
            <el-row :gutter="20">
              <el-col :sm="6" :xl="4" v-for="(item, index) in notLinkageList" :key="index">
                <div class="item">
                  <div class="item-bd">
                    <div class="name">{{ item.name }}</div>
                  </div>
                  <div class="item-ft">
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip" v-show="item.showName">
                      <i class="el-icon-document f-f18 f-cb f-mr10"></i>
                      <div slot="content">对外展示：{{ item.showName }}</div>
                    </el-tooltip>
                    <template v-if="$hasPermission('modify')" desc="修改" actions="@ModifyFieldShowName">
                      <el-button type="text" @click.stop="modifyFieldInfo(item)">
                        <i class="el-icon-edit-outline f-f18"></i>
                      </el-button>
                    </template>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card shadow="never" class="m-card f-mb15" v-if="this.linkageList.length">
          <div slot="header" class="">
            <span class="tit-txt">{{ linkageTitle }}</span>
          </div>
          <div class="m-attribute">
            <el-collapse v-model="activeName" accordion @change="handleCollapseChange">
              <el-collapse-item v-for="(item, index) in linkageList" :key="index" :name="item.propertyId">
                <template slot="title">
                  <div class="m-sub-tit f-align-center">
                    <span class="tit-txt f-mr15">{{ item.name }}</span>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip" v-show="item.showName">
                      <i class="el-icon-document f-f18 f-cb f-mr10"></i>
                      <div slot="content">对外展示：{{ item.showName }}</div>
                    </el-tooltip>
                    <template v-if="$hasPermission('modify')" desc="修改" actions="@ModifyFieldShowName">
                      <el-button type="text" @click.stop="modifyFieldInfo(item)">
                        <i class="el-icon-edit-outline f-f18"></i>
                      </el-button>
                    </template>
                  </div>
                </template>
                <template v-if="activeName === item.propertyId">
                  <div class="f-plr20 f-pt20" v-loading="uiConfig.majorLoading">
                    <el-row :gutter="20" v-if="linkageChildList.length">
                      <el-col :sm="6" :xl="4" v-for="(item, index) in linkageChildList" :key="index">
                        <div class="item">
                          <div class="item-bd">
                            <div class="name">{{ item.name }}</div>
                          </div>
                          <div class="item-ft">
                            <el-tooltip effect="dark" placement="top" popper-class="m-tooltip" v-show="item.showName">
                              <i class="el-icon-document f-f18 f-cb f-mr10"></i>
                              <div slot="content">对外展示：{{ item.showName }}</div>
                            </el-tooltip>
                            <template v-if="$hasPermission('modify')" desc="修改" actions="@ModifyFieldShowName">
                              <el-button type="text" @click.stop="modifyFieldInfo(item)">
                                <i class="el-icon-edit-outline f-f18"></i>
                              </el-button>
                            </template>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20" v-else>
                      {{ industryType == 5 ? '暂无学科' : '暂无专业信息' }}
                    </el-row>
                  </div></template
                >
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-card>
        <template v-if="$hasPermission('modify')" desc="修改" actions="@ModifyFieldShowName">
          <modify-field-show-name
            :visible.sync="uiConfig.dialog.editAttributeValueVisible"
            :modify-info.sync="this.modifyInfo"
            @reloadData="handleReloadData"
          ></modify-field-show-name>
        </template>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import MajorParam from '@api/service/common/basic-data-dictionary/query/vo/majorParam'
  import SubjectTypeVo from '@api/service/common/basic-data-dictionary/query/vo/SubjectTypeVo'
  import QueryBasicdataDictionaryFactory from '@api/service/common/basic-data-dictionary/QueryBasicdataDictionaryFactory'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import ModifyFieldShowName from '@hbfe/jxjy-admin-scheme/src/field/modify-field-show-name.vue'
  import FieldEditModel from '@hbfe/jxjy-admin-scheme/src/field/models/FieldEditModel'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import QueryJobLevel from '@api/service/common/basic-data-dictionary/query/QueryJobLevel'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
  import QueryTrainingObject from '@api/service/common/basic-data-dictionary/query/QueryTrainingObject'
  import QueryPositionCategory from '@api/service/common/basic-data-dictionary/query/QueryPositionCategory'
  import QueryGrade from '@api/service/common/basic-data-dictionary/query/QueryGrade'
  import QuerySubject from '@api/service/common/basic-data-dictionary/query/QuerySubject'
  import QueryPersonIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustry'
  import QueryCertType from '@api/service/common/basic-data-dictionary/query/QueryCertType'
  import QueryPractitionerCategory from '@api/service/common/basic-data-dictionary/query/QueryPractitionerCategory'

  @Component({
    components: { ModifyFieldShowName }
  })
  export default class extends Vue {
    /**
     * 行业信息（必传）
     */
    @Prop({
      required: true,
      type: IndustryVo,
      default: () => {
        return new IndustryVo()
      }
    })
    industryInfo: IndustryVo

    /**
     * 行业类型（必传）
     */
    @Prop({
      required: true,
      type: Number
    })
    industryType: number

    @Watch('industryInfo', {
      deep: true,
      immediate: true
    })
    async industryInfoChange(val: IndustryVo) {
      if (val.propertyId) {
        this.uiConfig.pageLoading = true
        await this.initPage()
        this.uiConfig.pageLoading = false
      }
    }

    /**
     * 接口查询
     */
    queryM: QueryBasicdataDictionaryFactory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory

    /**
     * sku属性是否展示
     */
    skuVisible = {
      // 科目类型
      subjectType: true,
      // 培训类别
      trainingCategory: true,
      //   技术等级
      technicalGrade: true,
      // 培训对象
      jobCategory: true,
      // 学段
      studyPeriod: true,
      //执业类别
      occupationalCategory: true
    }

    /**
     * sku查询参数
     */
    skuQueryParams: MajorParam = {
      // 行业分类属性Id
      industryPropertyId: '',
      // 人社行业 - 父级培训类别
      parentPropertyId: ''
    }

    /**
     * ui控制组
     */
    uiConfig = {
      // 页面加载
      pageLoading: false,
      // 对话框是否展示
      dialog: {
        editAttributeValueVisible: false
      },
      // 专业加载
      majorLoading: false
    }

    /**
     * 活页名称
     */
    activeName = ''

    /**
     * 无联动列表
     */
    notLinkageList: SubjectTypeVo[] | TrainingPropertyResponse[] = []

    /**
     * 联动列表父级
     */
    linkageList: TrainingCategoryVo[] | TrainingPropertyResponse[] = []

    /**
     * 联动列表子级
     */
    linkageChildList: TrainingCategoryVo[] | TrainingPropertyResponse[] = []

    /**
     * 行业分类属性id
     */
    industryPropertyId = ''

    /**
     * 编辑对象
     */
    modifyInfo: FieldEditModel = new FieldEditModel()

    /**
     * 类别、专业标题
     */
    get headerTitle() {
      let title = ''
      switch (this.industryType) {
        case 1:
        case 2:
        case 6:
          title = '科目类型'
          break
        case 3:
          title = '培训类别'
          break
        case 4:
          title = '技术等级'
          break
        default:
          break
      }
      return title
    }

    /**
     * 类别、专业标题
     */
    get linkageTitle() {
      let title = ''
      switch (this.industryType) {
        case 1:
          title = '培训专业'
          break
        case 2:
          title = '培训类别-培训专业'
          break
        case 3:
          title = '培训对象-岗位类别'
          break
        case 5:
          title = '学段-学科'
          break
        case 6:
          title = '证书类型-执业类别'
          break
        default:
          break
      }
      return title
    }

    /**
     * 页面初始化
     */
    async initPage() {
      await this.getIndustryAuthorizedProperties()
      if (this.skuVisible.subjectType) this.getSubjectTypeList()
      if (this.skuVisible.trainingCategory) this.getTrainingCategoryList()
      if (this.skuVisible.technicalGrade) this.getTechnologyLevelList()
      if (this.skuVisible.jobCategory) this.getTrainingObjectList()
      if (this.skuVisible.studyPeriod) this.getStudyPeriodOptions()
      if (this.skuVisible.occupationalCategory) this.getCertificatesType()
    }

    /**
     * 获取行业授权属性
     */
    async getIndustryAuthorizedProperties() {
      this.industryPropertyId = this.industryInfo.propertyId
      this.skuQueryParams.industryPropertyId = this.industryInfo.propertyId
      const configList: IndustryPropertyCategoryVo[] =
        await this.queryM.queryIndustryPropertyCategory.getIndustryPropertyCategoryList(
          this.skuQueryParams.industryPropertyId
        )
      const configSubjectType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.SUBJECT_TYPE)
      const configTrainingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.TRAINING_CATEGORY
      )
      const technicalGrade = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.JOB_LEVEL)
      const jobCategory = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.TRAINING_OBJECT)
      const studyPeriod = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.LEARNING_PHASE)
      const occupational = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.PRACTITIONER_CATEGORY)
      this.skuVisible.subjectType = configSubjectType > -1
      this.skuVisible.trainingCategory = configTrainingCategory > -1
      this.skuVisible.technicalGrade = technicalGrade > -1
      this.skuVisible.jobCategory = jobCategory > -1
      this.skuVisible.studyPeriod = studyPeriod > -1
      this.skuVisible.occupationalCategory = occupational > -1
    }

    /**
     * 获取科目类型列表
     */
    async getSubjectTypeList() {
      if (this.skuVisible.subjectType) {
        const res = await this.queryM.querySubjectType.querySubjectTypeList(
          this.skuQueryParams.industryPropertyId,
          this.industryInfo.id
        )
        this.notLinkageList = res.isSuccess() ? this.queryM.querySubjectType.subjectTypeList : ([] as SubjectTypeVo[])
        console.log('notLinkageList', this.notLinkageList)
      }
    }

    /**
     * 获取培训类别
     */
    async getTrainingCategoryList() {
      const res = await this.queryM.queryTrainingCategory.queryTrainingCategory(
        this.skuQueryParams.industryPropertyId,
        this.industryInfo.id
      )
      if (this.industryType === 3) {
        this.notLinkageList = res.isSuccess()
          ? this.queryM.queryTrainingCategory.trainingCategoryList
          : ([] as TrainingCategoryVo[])
      } else {
        this.linkageList = res.isSuccess()
          ? this.queryM.queryTrainingCategory.trainingCategoryList
          : ([] as TrainingCategoryVo[])
      }

      console.log('linkageList', this.linkageList)
    }

    /**
     * 获取培训专业列表
     */
    async getTrainingMajorList() {
      this.linkageChildList = [] as TrainingCategoryVo[] | TrainingPropertyResponse[]
      const res = await this.queryM.queryTrainingMajor.queryTrainingMajor(this.skuQueryParams)
      this.linkageChildList = res.isSuccess()
        ? this.queryM.queryTrainingMajor.trainingMajorList
        : ([] as TrainingCategoryVo[])
    }

    /**
     * 获取技术等级 - 工勤行业
     */
    async getTechnologyLevelList() {
      this.notLinkageList = await QueryJobLevel.QueryJobLevelByIndustry(
        this.industryInfo.id,
        this.industryInfo.propertyId
      )
    }

    /**
     * 获取培训对象 - 职业卫生行业
     */
    async getTrainingObjectList() {
      this.linkageList = await QueryTrainingObject.queryTrainingObjectByIndustry(
        this.industryInfo.id,
        this.industryInfo.propertyId
      )
    }

    /**
     * 获取岗位类别 - 职业卫生行业
     */
    async getPositionCategoryList() {
      this.linkageChildList = await QueryPositionCategory.queryPositionCategoryByTrainingObjectId(
        this.skuQueryParams.industryPropertyId,
        this.skuQueryParams.parentPropertyId
      )
    }
    /**
     * 获取学段
     */
    async getStudyPeriodOptions() {
      //   await QueryGrade.queryGradeByIndustry()
      await QueryGrade.queryGradeByIndustryV2(this.industryInfo.id, this.industryInfo.propertyId)
      this.linkageList = QueryGrade.gradeList
    }
    /**
     * 获取证书类型
     */
    async getCertificatesType() {
      this.linkageList = [] as TrainingCategoryVo[] | TrainingPropertyResponse[]
      const res = await QueryCertType.querycertTypeByIndustryV2(this.industryInfo.id, this.industryInfo.propertyId)
      this.linkageList = res
      // await Promise.all(
      //   this.linkageList.map(async item => {
      //     this.linkageChildList = await QueryPersonIndustry.getPractitionerCategory(item.propertyId)
      //     console.log(this.linkageChildList, ' this.linkageChildList')
      //   })
      // )
    }
    /**
     * 获取执业类别
     */
    async getPractitionerCategory() {
      this.linkageChildList = await QueryPractitionerCategory.queryPractitionerCategoryByGradeIdV2(
        this.industryInfo.propertyId,
        this.skuQueryParams.parentPropertyId
      )
    }
    /**
     * 获取学科
     */
    async getSubjectOptions(val: string) {
      //   const res = await QuerySubject.querySubjectByIndustry(val)
      const res = await QuerySubject.querySubjectByGradeIdV2(this.industryInfo.propertyId, val)
      this.linkageChildList = res
    }

    /**
     * 响应折叠面板选中事件
     */
    async handleCollapseChange(activeName: string) {
      if (activeName) {
        this.skuQueryParams.parentPropertyId = this.activeName
        this.uiConfig.majorLoading = true
        if (this.industryType === 3) {
          await this.getPositionCategoryList()
        } else if (this.industryType === 5) {
          await this.getSubjectOptions(activeName)
        } else if (this.industryType === 6) {
          await this.getPractitionerCategory()
        } else await this.getTrainingMajorList()
        this.uiConfig.majorLoading = false
      }
    }

    /**
     * 编辑信息
     */
    modifyFieldInfo(item: SubjectTypeVo | TrainingCategoryVo | TrainingCategoryVo) {
      this.modifyInfo.industryId = this.industryInfo.id
      this.modifyInfo.name = item.name
      this.modifyInfo.showName = item.showName
      this.modifyInfo.trainingPropertyId = item.propertyId
      this.uiConfig.dialog.editAttributeValueVisible = true
    }

    /**
     * 重新加载
     */
    async handleReloadData() {
      if (this.skuVisible.subjectType) await this.getSubjectTypeList()
      if (this.skuVisible.trainingCategory) await this.getTrainingCategoryList()
      if (this.skuVisible.technicalGrade) await this.getTechnologyLevelList()
      if (this.skuVisible.jobCategory) await this.getTrainingObjectList()
      if (this.skuVisible.studyPeriod) await this.getStudyPeriodOptions()
      console.log(this.skuVisible.occupationalCategory, 'this.skuVisible.occupationalCategory')
      if (this.skuVisible.occupationalCategory) {
        console.log(22)
        await this.getCertificatesType()
      }
      if (this.activeName) {
        this.skuQueryParams.parentPropertyId = this.activeName
        this.uiConfig.majorLoading = true
        if (this.industryType === 3) {
          await this.getPositionCategoryList()
        } else if (this.industryType === 5) {
          await this.getSubjectOptions(this.skuQueryParams.parentPropertyId)
        } else if (this.industryType === 6) {
          await this.getPractitionerCategory()
        } else await this.getTrainingMajorList()
        this.uiConfig.majorLoading = false
      }
    }
  }
</script>
