// 单位信息即培训机构信息
import { TeachUnitManagerInfo } from '@api/service/common/models/user/TeachUnitManagerInfo'

export class TrainingInstitution {
  id: string
  name: string
  /**
   * 组织机构证
   */
  organizationCode: string
  /**
   * 单位省份id
   */
  provinceId: string
  /**
   * 省份名称
   */
  provinceName = ''
  /**
   * 单位地区 市
   */
  cityId: string
  /**
   * 市名
   */
  cityName = ''
  /**
   * 单位地区 区县
   */
  countyId: string
  /**
   * 区县名
   */
  countyName = ''
  /**
   * 单位状态 | 1：正常;2:停用  默认正常
   */
  status: string

  /**
   * 门户名称
   */
  portalName = ''

  /**
   * 域名
   */
  domain = ''

  /**
   * 管理员
   */
  manager = new TeachUnitManagerInfo()

  /**
   * logo地址
   */
  logoUrl = ''

  /**
   * 标识
   */
  registType = ''

  /**
   * 机构简称
   */
  abbreviation = ''

  /**
   * 创建时间
   */
  createTime: string

  /**
   * 手机号
   */
  phoneNumber: string
  /**
   * 宣传口号
   */
  slogan: string
  /**
   * web端轮播图
   */
  webBannerUrls: Array<string>
  /**
   * 手机端轮播图
   */
  mobileBannerUrls: Array<string>
  /**
   * web侧机构简介
   */
  webDescription: string
  /**
   * 移动端机构简介
   */
  mobileDescription: string
  /**
   * 机构小程序二维码
   */
  qrCode: string
  /**
   * 机构网址路径
   */
  urlPath: string

  getStatusDesc(): string {
    switch (this.status) {
      case '1':
        return '正常'
      case '2':
        return '停用'
      default:
        return '未知'
    }
  }
}
