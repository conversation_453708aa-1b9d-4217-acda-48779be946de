<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/training/course-package')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/course-package' }">课程包管理</el-breadcrumb-item>
      <el-breadcrumb-item>课程包导入</el-breadcrumb-item>
    </el-breadcrumb>
    <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
      <p>温馨提示：</p>
      <p>1. 在导入课程包前请下载[导入课程包模板]，并严格根据表格内容填写保存后再导入系统；</p>
      <p>2. 导入表格一次最多支持1000条记录，若超过1000条则不能正常导入；</p>
      <p>3. 导入后可以通过“导入任务查看” 查看并确认导入结果；</p>
      <p>4. 仅支持新建课程包，已存在的课程包无法通过导入修改；</p>
      <p>5. 导入的课程按序号正序排列，序号越小越靠前；</p>
    </el-alert>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-row type="flex" justify="center">
          <el-col :sm="14" :lg="10">
            <el-steps direction="vertical" :active="2" class="m-vertical-steps">
              <el-step title="下载导入课程包模板，填写要求信息">
                <div slot="description" @click="downloadModule">
                  <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-download">
                    导入课程包模板
                  </el-button>
                </div>
              </el-step>
              <el-step title="上传填写好的课程包导入表格">
                <div slot="description">
                  <hb-upload-file v-model="hbFileUploadResponse" :file-type="1"></hb-upload-file>
                </div>
              </el-step>
            </el-steps>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc">
        <el-button @click="goBack('/training/course-package')">返回上一级</el-button>
        <el-button type="primary" @click="commit" :loading="uploading">上传</el-button>
      </div>
    </div>
    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导入成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导入任务查看-课程包导入任务</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import ImportCoursePackage from '@api/service/management/resource/course-package/mutation/ImportCoursePackage'
  import hbUploadFile from '@hbfe/jxjy-admin-components/src/hb-upload-file.vue'
  import Downloader, { HeaderObj } from '@api/service/common/utils/Downloader'
  @Component({
    components: {
      hbUploadFile
    }
  })
  export default class extends Vue {
    props: { multiple: true }
    value1: ''
    dialogImageUrl: ''
    dialogVisible: false
    dialog = false
    address = ''
    importCoursePackage: ImportCoursePackage = new ImportCoursePackage()
    // 报名模板地址
    signUpTemplate = ''
    /**
     * 文件上传之后的回调参数
     */
    hbFileUploadResponse = new HBFileUploadResponse()
    //导出成功弹窗
    exportSuccessVisible = false
    /**
     * 上传按钮loading
     */
    uploading = false
    handleRemove(file: any, fileList: any) {
      console.log(file, fileList)
    }
    constructor() {
      super()
      this.importCoursePackage = ResourceModule.coursePackageFactory.importCoursePackage
    }

    /**
     * 确定上传
     */
    async commit() {
      this.uploading = true
      console.log(this.hbFileUploadResponse, 'hbFileUploadResponse')
      this.importCoursePackage.filePath = '/mfs' + this.hbFileUploadResponse.url
      if (!this.importCoursePackage.filePath) {
        this.$message.warning('请选择上传的文件')
        return
      }
      const res = await this.importCoursePackage.doImport()
      if (res.isSuccess()) {
        this.$message.success('上传成功')
        this.exportSuccessVisible = true
      } else {
        if (res.message) {
          this.$message.error(res.message as string)
        } else {
          this.$message.error('上传失败')
        }
      }
      this.uploading = false
    }
    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/importtask',
        query: { type: '课程包导入任务' }
      })
    }
    // 下载报名列表
    async downloadModule() {
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const download = new Downloader(this.signUpTemplate, '课程包导入模板', header)
      download.download()
    }
    async created() {
      this.signUpTemplate =
        '/mfs/resource/file/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/课程包导入模板.xlsx'
    }

    // 返回上一级
    goBack(url: string) {
      this.$router.push(url)
    }
  }
</script>

<style scoped></style>
