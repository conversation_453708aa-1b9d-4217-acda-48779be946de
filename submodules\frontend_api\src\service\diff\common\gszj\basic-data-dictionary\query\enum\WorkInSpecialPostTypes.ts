import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 是否是专技岗位工作
 */
enum WorkInSpecialPostEnum {
  FALSE = '0',
  TRUE = '1'
}

export { WorkInSpecialPostEnum }
class WorkInSpecialPostTypes extends AbstractEnum<WorkInSpecialPostEnum> {
  static enum = WorkInSpecialPostEnum

  constructor(status?: WorkInSpecialPostEnum) {
    super()
    this.current = status
    this.map.set(WorkInSpecialPostEnum.FALSE, '否')
    this.map.set(WorkInSpecialPostEnum.TRUE, '是')
  }
}

export default new WorkInSpecialPostTypes()
