import BasicDataGateway, {
  SchoolTrainingPropertyQueryRequest,
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { listIndustryPropertyByOnlineSchoolV2 } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage/graphql-importer'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
import Context from '@api/service/common/context/Context'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import Vue from 'vue'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'

/**
 * @description 培训类别请求
 */
export class TrainingCategoryRequest {
  /**
   * 行业id
   */
  industryId = ''
  /**
   * 科目类型id集合
   */
  trainingCategoryIdList: string[] = []

  constructor(industryId?: string, trainingCategoryIdList?: string[]) {
    this.industryId = industryId
    this.trainingCategoryIdList = trainingCategoryIdList
  }
}
class QueryTrainingCategory {
  /**
   * 培训类别列表
   */
  trainingCategoryList: Array<TrainingCategoryVo>

  /**
   * 培训类别列表缓存
   */
  trainingCategoryCache: { [key: string]: TrainingCategoryVo } = {}

  /**
   * 查询培训类别列表
   * @param industryPropertyId 行业属性id 行业属性分类id
   * @param industryId 行业id
   * @return
   */
  async queryTrainingCategory(industryPropertyId: string, industryId: string) {
    const res = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
      industryPropertyId: industryPropertyId,
      categoryCode: IndustryPropertyCodeEnum.PERSON_TRAINING_CATEGORY,
      industryId
    })
    if (res.status.isSuccess()) {
      // todo 需要字段统一
      const currentIndustry = await BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustry.getIndustryByIdList(
        [industryPropertyId]
      )
      this.setTrainingCategoryCache(currentIndustry[0].id, res.data)
      this.trainingCategoryList = res.data
    }
    return res.status
  }

  /**
   * 查询培训类别列表通过idList
   */
  async queryTrainingCategoryByIdList(param: { industryId: string; categoryIds: Array<string> }) {
    const schoolTrainingPropertyQueryRequest = new SchoolTrainingPropertyQueryRequest()
    schoolTrainingPropertyQueryRequest.propertyId = param.categoryIds
    schoolTrainingPropertyQueryRequest.industryId = param.industryId
    schoolTrainingPropertyQueryRequest.schoolId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
    const res = await BasicDataGateway.listIndustryPropertyByOnlineSchoolV2(schoolTrainingPropertyQueryRequest)
    if (res.status.isSuccess()) {
      const list = res.data?.map(TrainingCategoryVo.from)
      this.setTrainingCategoryCache(param.industryId, list)
      return list
    }
    return new Array<TrainingCategoryVo>()
  }

  /**
   * 设置培训类别的缓存
   */
  private setTrainingCategoryCache(industryId: string, trainingCategoryList: Array<TrainingCategoryVo>) {
    trainingCategoryList?.forEach(trainingCategory => {
      if (!this.trainingCategoryCache[`${industryId}_${trainingCategory.propertyId}`]) {
        Vue.set(this.trainingCategoryCache, `${industryId}_${trainingCategory.propertyId}`, trainingCategory)
      }
    })
  }

  /**
   * 获取培训类别列表
   */
  async getTrainingCategoryByIdList(param: { industryId: string; trainingCategoryIdList: Array<string> }) {
    const trainingCategoryList = new Array<TrainingCategoryVo>()
    const idList = new Array<string>()
    param.trainingCategoryIdList?.forEach(id => {
      const trainingCategory = this.trainingCategoryCache[`${param.industryId}_${id}`]
      if (trainingCategory) {
        trainingCategoryList.push(trainingCategory)
      } else {
        // 不在缓存中时会发起请求获取
        idList.push(id)
      }
    })
    if (idList.length) {
      const list = await this.queryTrainingCategoryByIdList({
        industryId: param.industryId,
        categoryIds: idList
      })
      trainingCategoryList.push(...list)
    }
    return trainingCategoryList
  }

  /**
   * 批量查询培训类别
   * @param request 请求入参
   */
  async batchGetTrainingCategoryByIdList(request: TrainingCategoryRequest[]): Promise<TrainingCategoryVo[]> {
    const result = [] as TrainingCategoryVo[]
    const params = request.map(item => {
      const opt = new SchoolTrainingPropertyQueryRequest()
      opt.propertyId = item.trainingCategoryIdList
      opt.industryId = item.industryId
      opt.schoolId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
      return opt
    })
    const reWriteGQL = new RewriteGraph<TrainingPropertyResponse[], SchoolTrainingPropertyQueryRequest>(
      BasicDataGateway._commonQuery,
      listIndustryPropertyByOnlineSchoolV2
    )
    await reWriteGQL.request(params)
    for (const [key, value] of reWriteGQL.itemMap.entries()) {
      if (value && value.length) {
        value.forEach(el => {
          result.push(TrainingCategoryVo.from(el))
        })
      }
    }
    return result
  }
}

export default new QueryTrainingCategory()
