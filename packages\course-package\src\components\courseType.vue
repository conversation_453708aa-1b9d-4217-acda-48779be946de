<template>
  <el-card shadow="never" class="m-card f-mb15">
    <div slot="header">
      <span class="tit-txt">可选课程</span>
    </div>
    <el-row :gutter="16" class="m-query is-border-bottom">
      <el-form :inline="true" label-width="auto" style="display: flex">
        <el-col :span="18">
          <el-form-item label="课程分类：" class="fb ml20">
            <el-input placeholder="请输入末级课程分类名称" v-model="name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-button type="primary" @click="getAllWorkTypeCategoryTreeList">查询</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <el-table
      style="max-height: 500px; overflow: hidden; overflow-y: auto"
      highlight-current-row
      :data="queryCourseCategory.courseCategoryTreeList"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :load="load"
      lazy
      tooltip-effect="dark"
      size="small"
      @row-click="handleNodeClick"
    >
      <el-table-column prop="name" label="分类名称" header-align="left" align="left" :show-overflow-tooltip="true">
      </el-table-column>
    </el-table>
  </el-card>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import CourseCategoryDetail from '@api/service/management/resource/course-category/query/vo/CourseCategoryDetail'
  import QueryCourseCategory from '@api/service/management/resource/course-category/query/QueryCourseCategory'
  import CourseCategoryListDetail from '@api/service/management/resource/course-category/query/vo/CourseCategoryListDetail'

  @Component
  export default class extends Vue {
    @Prop({
      type: String,
      default: ''
    })
    unitId: string // 机构id
    defaultProps = {
      label: 'name'
    }
    name = ''
    workTypeCategoryTreeList: CourseCategoryDetail = new CourseCategoryDetail()
    queryCourseCategory = new QueryCourseCategory()

    async mounted() {
      this.getAllWorkTypeCategoryTreeList()
    }

    // 工种分类树
    async getAllWorkTypeCategoryTreeList() {
      this.queryCourseCategory.courseCategoryTreeList = await this.queryCourseCategory.queryList('-1', this.name)
    }

    async handleNodeClick(data: CourseCategoryListDetail) {
      console.log(data)
      this.$emit('input', data.id)
    }

    //懒加载
    async load(tree: CourseCategoryListDetail, treeNode: any, resolve: (arr: Array<CourseCategoryListDetail>) => void) {
      const parentId = tree.id
      const res = await this.queryCourseCategory.queryChildrenById(parentId)
      const categoryList = new CourseCategoryListDetail()
      resolve(res)
      // resolve(this.queryCourseCategory.courseCategoryTreeList)
    }
  }
</script>
