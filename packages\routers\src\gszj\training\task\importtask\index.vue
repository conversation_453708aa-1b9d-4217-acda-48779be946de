<!--
 * @Author: z张仁榕
 * @Date: 2025-01-21 17:09:50
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-02-24 14:00:41
 * @Description: 
-->
<route-meta>
{
"isMenu": true,
"title": "导入任务管理",
"sort": 1,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>

<script lang="ts">
  import ImportTask from '@hbfe/jxjy-admin-task/src/diff/gszj/importtask/index.vue'

  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import {
    // 施教机构管理员
    WXGLY,
    ZTGLY
  } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY, ZTGLY],
    download: [WXGLY, ZTGLY],
    viewLog: [WXGLY, ZTGLY]
  })
  export default class extends ImportTask {}
</script>
