import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { ResponseStatus } from '@api/Response'
import AntiCheatConfigModule, { LoginConfigParameter } from '@api/service/customer/anti-cheat/AntiCheatConfigModule'
import AntiVerifyResult from '@api/service/customer/anti-cheat/models/AntiVerifyResult'
import InterceptResult from '@api/service/customer/anti-cheat/models/InterceptResult'
import $http from '@packages/request'
import AntiUtils from '@api/service/customer/anti-cheat/models/AntiUtils'
import HttpResponse from '@api/service/customer/anti-cheat/models/HttpResponse'
import LoginScenesAntiConfig from '@api/service/common/models/anticheat/LoginScenesAntiConfig'
import { Role, RoleType } from '@api/Secure'

/**
 * 本地状态数据
 */
interface LoginAntiCheatState {
  /**
   * 当前平台学习场景防作弊配置
   */
  loginScenesConfig: LoginScenesAntiConfig
  /**
   * 请求拦截结果
   */
  interceptResult: InterceptResult
  /**
   * 短码状态
   */
  codeStateResult: AntiVerifyResult
  /**
   * 防作弊验证结果
   */
  antiVerifyResult: AntiVerifyResult
  /**
   * 是否加载学习场景防作弊
   */
  isLoginScenesConfigLoad: boolean
}

export class InterceptionParameter {
  /**
   * 防作弊配置编号
   * @see LoginScenesAntiConfig#id
   */
  configId = ''
  /**
   * 防作弊模式编号
   * @see LoginScenesAntiConfig#shapeModel#id
   */
  modeId = ''
  userId = ''
  platformId = ''
  platformVersionId = ''
  projectId = ''
  subProjectId = ''
  organizationId = ''
  unitId = ''
  /**
   * 维度
   */
  dimensions: number
  /**
   * 记录点
   */
  recordPoint = ''
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerLoginAntiCheatModule', store })
class LoginAntiCheatModule extends VuexModule implements LoginAntiCheatState {
  /**
   * 当前平台学习场景防作弊配置
   */
  public loginScenesConfig: LoginScenesAntiConfig = new LoginScenesAntiConfig()
  /**
   * 请求拦截结果
   */
  public interceptResult: InterceptResult = new InterceptResult()
  /**
   * 短码状态
   */
  public codeStateResult: AntiVerifyResult = new AntiVerifyResult()
  /**
   * 防作弊验证结果
   */
  public antiVerifyResult: AntiVerifyResult = new AntiVerifyResult()
  /**
   * 是否加载学习场景防作弊
   */
  public isLoginScenesConfigLoad = false

  //region action
  /**
   * 加载防作弊配置
   */
  @Action
  @Role([RoleType.user])
  public async init(param: LoginConfigParameter): Promise<ResponseStatus> {
    if (!this.isLoginScenesConfigLoad) {
      const response = await AntiCheatConfigModule.initLoginConfig(param)
      if (response.isSuccess()) {
        const config = AntiCheatConfigModule.getLoginAntiConfig(param)
        if (config) {
          this.SET_LOGIN_CONFIG(config)
        }
      }
      return Promise.resolve(response)
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 重新加载学习场景防作弊配置
   */
  @Action
  @Role([RoleType.user])
  public async doReloadConfig(param: LoginConfigParameter): Promise<ResponseStatus> {
    this.SET_IS_LOAD_LOGIN_CONFIG(false)
    await AntiCheatConfigModule.doReloadLoginConfig(param)
    return this.init(param)
  }

  /**
   * 请求判断是否进行防作弊
   * @param params 参数：params - 请求参数
   * @typedef InterceptionParameter
   */
  @Action
  @Role([RoleType.user])
  public async doInterception(params: InterceptionParameter): Promise<ResponseStatus> {
    const requestParam = {
      configId: params.configId,
      modeId: params.modeId,
      userId: params.userId,
      platformId: params.platformId,
      platformVersionId: params.platformVersionId,
      projectId: params.projectId,
      subProjectId: params.subProjectId,
      organizationId: params.organizationId,
      unitId: params.unitId,
      dimensions: params.dimensions,
      recordPoint: params.recordPoint
    }
    const response = await $http.post(
      AntiUtils.getInterceptionServiceUrl(),
      AntiUtils.generateRequestParameter(requestParam)
    )
    const result = Object.assign(new HttpResponse(), response.data)
    if (result.successfully()) {
      const interceptResult = Object.assign(new InterceptResult(), result.data)
      this.SET_INTERCEPT_RESULT(interceptResult)
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, result.getMessage()))
    }
  }

  /**
   * 请求检测短码状态
   * @param params 参数：code - 短码
   */
  @Action
  @Role([RoleType.user])
  public async doCheckCodeState(params: { code: string }): Promise<ResponseStatus> {
    const response = await $http.get(AntiUtils.getCheckCodeStateServiceUrl() + '?code=' + params.code)
    const result = Object.assign(new HttpResponse(), response.data)
    if (result.successfully()) {
      const codeResult = Object.assign(new AntiVerifyResult(), result.data)
      this.SET_CODE_STATE_RESULT(codeResult)
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, result.getMessage()))
    }
  }

  /**
   * 请求发送照片进行防作弊验证
   * @param params 参数：code - 短码；photo - 照片的base64字符串
   */
  @Action
  @Role([RoleType.user])
  public async doPush(params: { code: string; photo: string }): Promise<ResponseStatus> {
    const response = await $http.post(AntiUtils.getPushServiceUrl(), AntiUtils.generateRequestParameter(params))
    const result = Object.assign(new HttpResponse(), response.data)
    if (result.successfully()) {
      const pushResult = Object.assign(new AntiVerifyResult(), result.data)
      this.SET_ANTI_VERIFY_RESULT(pushResult)
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, result.getMessage()))
    }
  }

  //endregion

  //region mutation
  /**
   * 设置学习场景防作弊配置
   * @param params
   * @constructor
   */
  @Mutation
  private SET_LOGIN_CONFIG(params: LoginScenesAntiConfig) {
    this.loginScenesConfig = params
    this.isLoginScenesConfigLoad = true
  }

  /**
   * 设置加载学习场景防作弊配置
   * @param isLoad 是否加载
   * @constructor
   */
  @Mutation
  private SET_IS_LOAD_LOGIN_CONFIG(isLoad: boolean) {
    this.isLoginScenesConfigLoad = isLoad
  }

  /**
   * 设置防作弊拦截验证结果
   * @param params
   * @constructor
   */
  @Mutation
  private SET_INTERCEPT_RESULT(params: InterceptResult) {
    this.interceptResult = params
  }

  /**
   * 设置短码验证结果
   * @param params
   * @constructor
   */
  @Mutation
  private SET_CODE_STATE_RESULT(params: AntiVerifyResult) {
    this.codeStateResult = params
  }

  /**
   * 设置防作弊验证结果
   * @param params
   * @constructor
   */
  @Mutation
  private SET_ANTI_VERIFY_RESULT(params: AntiVerifyResult) {
    this.antiVerifyResult = params
  }

  //endregion

  //region getter
  /**
   * 获取拦截结果
   * @see doInterception
   */
  get interception(): InterceptResult {
    return this.interceptResult
  }

  /**
   * 获取短码状态
   * @see doCheckCodeState
   */
  get codeState(): AntiVerifyResult {
    return this.codeStateResult
  }

  /**
   * 获取上传照片验证防作弊结果
   * @see doPush
   */
  get pushResult(): AntiVerifyResult {
    return this.antiVerifyResult
  }

  //endregion
}

export default getModule(LoginAntiCheatModule)
