<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2024-09-11 19:07:04
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2024-12-30 15:04:26
-->
<route-meta>
{
"isMenu": true,
"title": "集体报名对账",
"sort": 2,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>

<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { FXS, GYS, WXGLY, ZTGLY } from '@/models/RoleTypes'
  import ReconciliationPersonalIndex from '@hbfe/jxjy-admin-trade/src/reconciliation/collective/index.vue'

  @RoleTypeDecorator({
    query: [WXGLY, FXS, GYS, ZTGLY],
    orderReconciliation: [WXGLY, FXS, GYS],
    orderReconciliationzt: [ZTGLY],
    refundReconciliation: [WXGLY, FXS, GYS],
    refundReconciliationzt: [ZTGLY],
    editInvoicePopup: [WXGLY, FXS, GYS]
  })
  export default class extends ReconciliationPersonalIndex {}
</script>
