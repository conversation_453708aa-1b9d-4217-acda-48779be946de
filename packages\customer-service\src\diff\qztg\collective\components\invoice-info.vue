<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import SpecialInvoice from '@hbfe/jxjy-admin-customerService/src/diff/qztg/collective/components/special-invoice.vue'
  import ElectronicSpecialInvoiceOffline from '@hbfe/jxjy-admin-customerService/src/diff/qztg/collective/components/electonic-special-invoice-offline.vue'
  import InvoiceInfo from '@hbfe/jxjy-admin-customerService/src/collective/components/invoice-info.vue'

  @Component({
    components: {
      SpecialInvoice,
      ElectronicSpecialInvoiceOffline
    }
  })
  export default class extends InvoiceInfo {}
</script>
