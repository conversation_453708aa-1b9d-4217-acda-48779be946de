/*
 * @Description: 列表数据
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-06 08:33:09
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-15 16:22:06
 */
import { DeliveryStatusEnum, DeliveryWayEnum } from '../../enum/DeliveryInvoiceEnum'

export default class DeliveryInvoiceParamVo {
  /**
   * 批次单号
   */
  orderID?: string
  /**
   * 发票号
   */
  invoiceNo?: string
  /**
   * 运单号
   */
  theAwb?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 配送状态
   */
  deliveryStatus?: DeliveryStatusEnum
  /**
   * 收件人姓名
   */
  name?: string
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 配送方式
   */
  deliveryWay?: DeliveryWayEnum
  /**
   * 领取人
   */
  recipient?: string
  /**
   * 冻结状态
   */
  frozenState?: boolean
  /**
   * 收获地址
   */
  shippingAddress?: string
  /**
   * 发货时间
   */
  shippingDate?: string
  /**
   * 自取地址
   */
  selffetchedAddress?: string
  /**
   * 自取时间
   */
  selffetchedDate?: string
  /**
   * 快递公司
   */
  courierCompany: string
  static from() {
    //转换
  }
}
