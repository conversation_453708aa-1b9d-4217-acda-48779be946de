"""独立部署的微服务,K8S服务名:ms-teachingplan-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""培训点名称重复校验
		@param name
		@param excludeId
		@return
	"""
	checkTrainingPointName(name:String,excludeId:String):TrainingPointResponse
	"""传入的课程id是否有签到记录"""
	hasSignRecord(planItemId:String):PlanItemResponse
	"""查询听课程模版列表
		@return 听课程模版列表 {@link CourseCardTemplateResponse}
	"""
	listCourseCardTemplate:[CourseCardTemplateResponse]
	"""打印听课证模板预览
		@param templateId 模板ID
		@return 打印结果的文件路径
	"""
	printCourseCardTemplate(templateId:String):String
	"""学员打印听课证
		@param request 学员打印听课证请求 {@link StudentPrintCourseCardRequest}
		@return 听课证文件路径
	"""
	studentPrintCourseCard(request:StudentPrintCourseCardRequest):String
	tes:String
}
type Mutation {
	"""教学计划项签到
		@param token 学员学习token
		@return 学生签到结果
	"""
	applySign(token:String,request:SignInRequest):SignResultResponse
	"""更改培训点状态
		@param request
	"""
	changeTrainingPointStatus(request:TrainingPointStatusRequest):Void
	"""课程校验"""
	checkPlanItem(planItemIds:[String],schemeId:String,issueId:String):[PlanItemResponse]
	"""创建教学计划项台账接口"""
	createPlanItemRepair(request:CreatePlanItemRequest):String
	"""创建培训点
		@param request
		@return
	"""
	createTrainingPoint(request:TrainingPointCreateRequest):TrainingPointResponse
	"""自定义时间签到
		@return
	"""
	customSignIn(request:CustomSignInRequest):SignResultResponse
	"""删除培训点
		@param request
	"""
	deleteTrainingPoints(request:TrainingPointDeleteRequest):TrainingPointResponse
	recalculatePeriodByPlanItemIdAndStudentNo(request:RecalculatePeriodByPlanItemIdAndStudentNoRequest):Void @optionalLogin
	"""清空一个教学计划项之下的所有签到记录及其通过签到获得的学时
		@param planItemId 教学计划项id
	"""
	removeSignRecordAndStudyResultByPlanItemId(planItemId:String!):Void
	"""学员签到
		@param token 学员学习token
		@return 学生签到结果
	"""
	studentsSign(token:String,request:SignInRequest):SignResultResponse
	"""更新培训点
		@param request
	"""
	updateTrainingPoint(request:TrainingPointUpdateRequest):TrainingPointResponse
}
input Property @type(value:"com.fjhb.domain.seedwork.property.Property") {
	name:String!
	value:String
}
"""[教学地点]
	<AUTHOR> [xwj]
	@createTime : [2022/8/26 11:01]
"""
input PlanAddress @type(value:"com.fjhb.ms.teachingplan.v1.kernel.aggregate.plan.entities.PlanAddress") {
	"""教学地点Id"""
	id:String
	"""地点名称"""
	addressName:String
	"""教学地点性质"""
	nature:Int!
	"""维度"""
	lat:Double!
	"""经度"""
	lng:Double!
	"""有效半径"""
	diameter:Double
	"""地点数据（冗余，供地图工具使用）"""
	addressData:String
	"""是否启用"""
	enabled:Boolean
	"""创建人Id"""
	createdUserId:String
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""培训点ID"""
	trainingPointId:String
}
"""[教学计划资源]
	<AUTHOR> [xwj]
	@createTime : [2022/8/26 11:40]
"""
input PlanResource @type(value:"com.fjhb.ms.teachingplan.v1.kernel.aggregate.plan.entities.PlanResource") {
	"""资源id"""
	id:String
	"""资源名称"""
	name:String
	"""教学计划id"""
	planId:String
	"""教学计划项组id"""
	planItemGroupId:String
	"""教学计划项id"""
	planItemId:String
	"""教学资源类型
		@see com.fjhb.domain.teachingplan.api.plan.consts.PlanResourceTypes
	"""
	resourceType:Int!
	"""教学资源内容"""
	resourceContent:String
}
"""[教学人员]
	<AUTHOR> [xwj]
	@createTime : [2022/8/19 10:08]
"""
input PlanTeacher @type(value:"com.fjhb.ms.teachingplan.v1.kernel.aggregate.plan.entities.PlanTeacher") {
	"""教学人员id"""
	id:String
	"""教学人员类型
		1-讲师 2-助教
		@see com.fjhb.domain.teachingplan.api.plan.consts.PlanTeacherTypes
	"""
	teacherType:Int!
	"""教学人员性质
		@see com.fjhb.domain.teachingplan.api.plan.consts.PlanTeacherNature
	"""
	nature:Int!
	"""教学人员名称"""
	teacherName:String
	"""教学人员关联id"""
	teacherReferenceId:String
	"""教学人员来源类型
		@see PlanTeacherSourceTypes
	"""
	sourceType:Int
	"""单位名称"""
	unitName:String
	"""扩展属性集合"""
	properties:[Property]
}
"""创建教学计划请求
	<AUTHOR>
	@date 2024/8/24 13:06
"""
input CreatePlanItemRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.CreatePlanItemRequest") {
	"""六个字段通用"""
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerId:String
	"""创建人Id"""
	createdUserId:String
	"""教学计划项id"""
	id:String
	"""教学计划Id"""
	planId:String
	"""教学计划组Id"""
	planItemGroupId:String
	"""教学计划项名称"""
	name:String
	"""相关教学人员"""
	teachers:[PlanTeacher]
	"""简介"""
	abouts:String
	"""教学模式
		@see com.fjhb.domain.teachingplan.api.plan.consts.PlanModes
	"""
	planMode:Int!
	"""时长（秒）"""
	timeLength:Int
	"""教学资源"""
	resources:[PlanResource]
	"""学时（课时）"""
	period:Double
	"""教学计划项类型
		@see com.fjhb.domain.teachingplan.api.plan.consts.PlanItemTypes
	"""
	planItemType:Int!
	"""教学开始时间"""
	startTime:DateTime
	"""教学结束时间"""
	endTime:DateTime
	"""教学地址"""
	address:PlanAddress
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:DateTime
}
"""<AUTHOR> [2023/4/14 14:29]"""
input CustomSignInRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.CustomSignInRequest") {
	"""学习token"""
	token:String
	"""签到时间"""
	signTime:DateTime
	"""签到Key"""
	pointKey:String
	"""维度"""
	lat:Double!
	"""经度"""
	lng:Double!
}
"""重新触发学时计算接口
	<AUTHOR> [2023/7/14 11:58]
"""
input RecalculatePeriodByPlanItemIdAndStudentNoRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.RecalculatePeriodByPlanItemIdAndStudentNoRequest") {
	"""学号"""
	studentNos:[String]
	"""教学计划项id"""
	planItemId:String
	"""教学计划id"""
	planId:String
}
"""[签到请求]
	<AUTHOR> [xwj]
	@createTime : [2022/10/20 10:33]
"""
input SignInRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.SignInRequest") {
	"""签到Key"""
	pointKey:String
	"""维度"""
	lat:Double!
	"""经度"""
	lng:Double!
}
"""学员打印听课证请求"""
input StudentPrintCourseCardRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.StudentPrintCourseCardRequest") {
	"""用户ID"""
	studentId:String
	"""听课证模板ID"""
	templateId:String
	"""学习方案ID"""
	learnSchemeId:String
	"""期别ID"""
	issueId:String
}
"""<AUTHOR>
	@since 安徽建设
"""
input TrainingPointCreateRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.TrainingPointCreateRequest") {
	"""培训点名称"""
	name:String
	"""经度"""
	longitude:Double!
	"""纬度"""
	latitude:Double!
	"""选中的培训地址"""
	specificAddress:String
	"""所在地区"""
	areaPath:String
	"""培训教室"""
	classRoom:String
	"""单位id"""
	ownerId:String
}
"""培训点删除
	<AUTHOR>
	@since 安徽建设
"""
input TrainingPointDeleteRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.TrainingPointDeleteRequest") {
	"""id
		聚合id
	"""
	id:String
}
"""培训点状态调整
	<AUTHOR>
	@since 安徽建设
"""
input TrainingPointStatusRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.TrainingPointStatusRequest") {
	"""id
		聚合id
	"""
	id:String
	"""状态
		0=停用
		1=启用
		@see TrainingPointStatus
	"""
	status:Int!
}
"""培训点更新
	<AUTHOR>
	@since 安徽建设
"""
input TrainingPointUpdateRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.TrainingPointUpdateRequest") {
	"""id
		聚合id
	"""
	id:String
	"""培训点名称"""
	name:String
	"""所在地区"""
	areaPath:String
	"""培训教室"""
	classRoom:String
}
"""[签到结果]
	<AUTHOR> [xwj]
	@createTime : [2022/9/1 14:37]
"""
type SignResultResponse @type(value:"com.fjhb.ms.teachingplan.v1.api.response.SignResultResponse") {
	"""学员签到id
		学号+教学计划id
	"""
	studentSignId:String
	"""签到结果
		200 签到成功
		100 迟到
		300 早退
		500 无效打卡时间
		501 无效地点
		502 已签到
	"""
	code:Int
	"""返回信息"""
	message:String
}
"""<AUTHOR>
	@since
"""
type TrainingPointResponse @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.TrainingPointResponse") {
	"""200 正常
		E500 名称重复
		E501 已被引用不可删除
	"""
	code:String
	"""培训点id"""
	id:String
}
"""查询听课证模板列表响应体"""
type CourseCardTemplateResponse @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.response.CourseCardTemplateResponse") {
	"""听课证模板ID"""
	templateId:String
	"""听课证模板名称"""
	templateName:String
	"""听课证模板描述"""
	templateDescription:String
	"""听课证模板使用范围"""
	templateScope:String
	"""听课证模板创建时间"""
	createTime:DateTime
}
"""教学计划项返回值
	<AUTHOR>
	@date 2024/12/11 10:18
"""
type PlanItemResponse @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.response.PlanItemResponse") {
	"""200 正常
		C600 课程是否已上课
		C601 是否有打卡记录
		C602 课程授课时间重叠
		@see PlanItemCodes
	"""
	code:String
	"""教学计划项id"""
	planItemId:String
}

scalar List
