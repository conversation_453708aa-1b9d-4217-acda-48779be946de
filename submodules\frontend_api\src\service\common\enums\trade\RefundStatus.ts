/**
 * 退款状态
 */
import AbstractEnum from '../AbstractEnum'

enum RefundStatusEnum {
  applied = 1,
  refund_success = 2,
  refund_fail = 3
}

export { RefundStatusEnum }

class RefundStatus extends AbstractEnum<RefundStatusEnum> {
  constructor() {
    super()
    this.map[RefundStatusEnum.applied] = '已申请'
    this.map[RefundStatusEnum.refund_success] = '退款成功'
    this.map[RefundStatusEnum.refund_fail] = '退款失败'
  }
}

export default RefundStatus
