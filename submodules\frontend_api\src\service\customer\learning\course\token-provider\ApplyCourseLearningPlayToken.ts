import AbstractApplyToken from '@api/service/common/token/AbstractApplyToken'
import MsStudentCourseLearningV1 from '@api/ms-gateway/ms-studentcourselearning-v1'

/**
 * 申请课程播放 token
 */
class ApplyCourseLearningPlayToken extends AbstractApplyToken {
  /**
   * 课程学习 token
   * @private
   */
  private readonly courseLearningToken: string

  constructor(courseLearningToken: string) {
    super()
    this.courseLearningToken = courseLearningToken
  }

  async apply(): Promise<void> {
    const courseLearningPlayToken = await MsStudentCourseLearningV1.applyCourseLearningPlay(this.courseLearningToken)
    this.token = courseLearningPlayToken.data.token
  }
}

export default ApplyCourseLearningPlayToken
