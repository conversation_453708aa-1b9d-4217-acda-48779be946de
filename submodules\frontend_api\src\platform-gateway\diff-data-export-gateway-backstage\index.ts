import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/diff-data-export-gateway-backstage'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'diff-data-export-gateway-backstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class ObsFileMetaData {
  bizType?: string
  owner?: string
  sign?: string
}

export class AnswerAskQuestionRequest {
  /**
   * 调查问卷id
   */
  questionnaireId?: string
  /**
   * 试题id
   */
  questionId?: string
  jobName?: string
  metaData?: ObsFileMetaData
}

export class AnswerPaperAnswerQuestionRequest {
  /**
   * 调查问卷id
   */
  questionnaireId?: string
  jobName?: string
  metaData?: ObsFileMetaData
}

export class AnswerQuestionPdfRequest {
  /**
   * 调查问卷id
   */
  questionnaireId?: string
  jobName?: string
  metaData?: ObsFileMetaData
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出调查问卷内问答试题作答内容(问答文本
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportAnswerAskQuestionExcelInServicer(
    request: AnswerAskQuestionRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportAnswerAskQuestionExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出调查问卷内选项试题作答内容
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportAnswerPaperAnswerQuestionExcelInServicer(
    request: AnswerPaperAnswerQuestionRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportAnswerPaperAnswerQuestionExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 打印调查问卷试题
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportAnswerQuestionPdfInServicer(
    request: AnswerQuestionPdfRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportAnswerQuestionPdfInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
