schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	pageExportTaskInfoInMyself(page:Page,jobRequest:JobRequest):UserJobLogResponsePage @page(for:"UserJobLogResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
"""功能描述：任务查询参数
	@Author： wtl
	@Date： 2022/1/18 15:13
"""
input JobRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.JobRequest") {
	"""任务组名（必填）"""
	group:String
	"""任务组名匹配方式（EQ：完全匹配 LIKE：模糊匹配[*group*] LLIKE：左模糊匹配[group*] RLIKE：右模糊匹配[*group]，不传值默认为完全匹配）"""
	groupOperator:String
	"""任务名（模糊查询）"""
	jobName:String
	"""任务状态(executing:运行中 executed:运行完成 fail:运行失败)
		@see UserJobState
	"""
	jobState:String
	"""任务执行时间 yyyy-MM-dd HH:mm:ss"""
	executeTimeScope:DateScopeRequest
	"""异步任务处理结果（true:成功 false:失败）"""
	jobResult:Boolean
	"""分割粒度
		null-无 1-单位
	"""
	granularity:Int
}
"""功能描述：异步任务日志返回对象
	@Author： wtl
	@Date： 2022/4/11 17:18
"""
type UserJobLogResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.export.UserJobLogResponse") {
	"""任务id"""
	jobId:String
	"""任务组名"""
	group:String
	"""任务名"""
	jobName:String
	"""任务开始时间"""
	beginTime:DateTime
	"""任务结束时间"""
	endTime:DateTime
	"""任务状态(executing:运行中 executed:运行完成 fail:运行失败)
		@see UserJobState
	"""
	jobState:String
	"""异步任务处理结果（true:成功 false:失败）"""
	jobResult:Boolean
	"""任务执行成功或失败的信息"""
	message:String
	"""导出文件路径"""
	exportFilePath:String
	"""是否受保护"""
	isProtected:Boolean!
	"""资源id"""
	fileResourceId:String
	"""操作人id"""
	operatorUserId:String
	"""操作人帐户id"""
	operatorAccountId:String
}

scalar List
type UserJobLogResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserJobLogResponse]}
