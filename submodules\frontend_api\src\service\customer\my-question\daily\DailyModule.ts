import store from '@/store'
import { Module as Mod } from 'vuex'
import { Module, VuexModule, getModule, Action, Mutation } from 'vuex-module-decorators'
import PlatformExamGateway, {
  DailyPracticeDTO,
  DailyPracticeParamDTO,
  PracticeType,
  UserPracticeStatisticParamDTO
} from '@api/gateway/PlatformExam'
import Response, { ResponseStatus } from '../../../../Response'
import moment from 'moment'
// import PreExamLSGateway from '@api/gateway/NormalIssueClassLS-default'
import { Role, RoleType } from '../../../../Secure'
import DailyPracticeAnswer from '@api/service/customer/my-question/daily/models/DailyPracticeAnswer'
import DailyPracticePaper from '@api/service/customer/my-question/daily/models/DailyPracticePaper'

class StateCache {
  constructor(schemeId: string, learningId: string, majorId: string) {
    this.schemeId = schemeId
    this.learningId = learningId
    this.majorId = majorId
  }

  // 方案id
  schemeId: string
  // 方式id
  learningId: string
  // 专业id
  majorId: string
  // 每日一练卷
  questionPractice: DailyPracticePaper
  // 答卷列表最近七天
  latestDailyPracticeAnswer: Array<DailyPracticeAnswer> = new Array<DailyPracticeAnswer>()
  // 参与人数
  participantsNumber: number
  // 每日一练天数
  practiceDayCount: number
  // 参与
  dailyPracticeStatistic: Array<DailyPracticeDTO> = new Array<DailyPracticeDTO>()
  // 是否需要重载，在学员触发去作答练习是置为true，在下次取数时先清空数据然后加载新数据
  needReload = false
  // 最新一次加载时间，从apollo配置的超时时间与当前时间判断，在下次取数时清空当前取数数据然后加载新数据
  latestLoadTime: Date = new Date()
}

export interface IState {
  /**
   * 各学习方式每日一练数据
   */
  learningDailyPracticeListMap: Array<StateCache>
  // 作答id
  currentAnswerId: string
}

@Module({
  namespaced: true,
  store,
  name: 'CustomerDailyModule',
  dynamic: true
})
class DailyModule extends VuexModule implements IState {
  learningDailyPracticeListMap = new Array<StateCache>()
  currentAnswerId = ''

  constructor(module: Mod<ThisType<any>, any>) {
    super(module)
    store.subscribe(mutation => {
      if (mutation.type?.indexOf('ALERT_EXAM_RELOAD') !== -1) {
        this.processSubmitPaperMessage(mutation.payload)
      }
    })
  }

  @Role([RoleType.user])
  @Action
  async init(payload: any) {
    if (
      !this.learningDailyPracticeListMap.find(p => p.learningId === payload.learningId) ||
      this.learningDailyPracticeListMap.find(p => p.learningId === payload.learningId)?.needReload
    ) {
      if (!payload.learningId) {
        return new ResponseStatus(500, '方式id不能为空')
      }
      if (!this.learningDailyPracticeListMap.find(p => p.learningId === payload.learningId)) {
        const stateCache = new StateCache(payload.schemeId, payload.learningId, payload.majorId)
        this.setStateCacheToLearningDailyPracticeListMap(stateCache)
      }
      const stateCache = new StateCache(payload.schemeId, payload.learningId, payload.majorId)
      // 获取每日一练卷子
      let paperResponse: Response<any>
      // let paperResponse: Response<any> = await PreExamGateway.getDailyPracticePaper(payload.learningId)
      // if (!paperResponse.status.isSuccess()) {
      //   return paperResponse.status
      // }
      // stateCache.questionPractice = paperResponse.data
      //
      // // 获取最新答卷列表
      // paperResponse = await PreExamGateway.getPreExamDailyPracticeList(payload.learningId)
      // if (!paperResponse.status.isSuccess()) {
      //   return paperResponse.status
      // }
      // stateCache.latestDailyPracticeAnswer = paperResponse.data.latestDailyPracticeAnswer

      const countUserParam: DailyPracticeParamDTO = new DailyPracticeParamDTO()
      countUserParam.schemeId = payload.schemeId
      countUserParam.learningId = payload.learningId
      countUserParam.recentDay = 0
      countUserParam.date = moment().format('YYYY-MM-DD')
      // 获取参与人数
      paperResponse = await PlatformExamGateway.countDailyPracticePartInUser(countUserParam)
      if (!paperResponse.status.isSuccess()) {
        return paperResponse.status
      }
      stateCache.participantsNumber = paperResponse.data

      const dailyPracticeParam: DailyPracticeParamDTO = new DailyPracticeParamDTO()
      dailyPracticeParam.schemeId = payload.schemeId
      dailyPracticeParam.learningId = payload.learningId
      dailyPracticeParam.recentDay = 7
      // 获取最近7天当前用户的每日一练
      paperResponse = await PlatformExamGateway.listDailyPractice(dailyPracticeParam)
      if (!paperResponse.status.isSuccess()) {
        return paperResponse.status
      }
      stateCache.dailyPracticeStatistic = paperResponse.data

      const frequencyParam: UserPracticeStatisticParamDTO = new UserPracticeStatisticParamDTO()
      frequencyParam.schemeId = payload.schemeId
      frequencyParam.learningId = payload.learningId
      frequencyParam.statisticPracticeTypeList = [PracticeType.DAILY]
      const frequencyResponse = await PlatformExamGateway.statisticUserPracticeFrequency(frequencyParam)
      if (!frequencyResponse.status.isSuccess()) {
        return frequencyResponse.status
      }
      stateCache.practiceDayCount = frequencyResponse.data.practiceDayCount
      this.setStateCacheToLearningDailyPracticeListMap(stateCache)
    }
    // else if ((this.learningDailyPracticeListMap.find(p => p.learningId === payload.learningId)?.latestLoadTime.getDate() || 0) < new Date().getDate() + 100000) {
    //   this.setNeedReload({
    //     schemeId: payload.schemeId,
    //     learningId: payload.learningId,
    //     majorId: payload.majorId
    //   })
    //   this.init({
    //     schemeId: payload.schemeId,
    //     learningId: payload.learningId,
    //     majorId: payload.majorId
    //   })
    // }
  }

  @Role([RoleType.user])
  @Action
  async goDailyPractice(payload: { schemeId: string; issueId: string }) {
    console.log(payload)
    // const examOutlineParam: ApplyDailyPracticeLearningTokenRequest = new ApplyDailyPracticeLearningTokenRequest()
    // // examOutlineParam.userId = payload.userId
    // examOutlineParam.schemeId = payload.schemeId
    // examOutlineParam.issueId = payload.issueId
    // const tokenResponse = await PreExamLSGateway.applyDailyPracticeLearningToken(examOutlineParam)
    //
    // const goPracticeResponse = await PreExamGateway.goPracticeByToken(tokenResponse.data)
    // this.setAnswerId(goPracticeResponse.data)
    //
    // return goPracticeResponse.status
  }

  @Action
  async processSubmitPaperMessage(payload: { schemeId: string; issueId: string; answersId: string }) {
    this.learningDailyPracticeListMap.forEach((stateCache: StateCache) => {
      if (
        stateCache?.schemeId === payload.schemeId &&
        (!payload.answersId || this.currentAnswerId === payload.answersId)
      ) {
        this.setNeedReload({
          schemeId: stateCache.schemeId,
          learningId: stateCache.learningId,
          majorId: stateCache.majorId
        })
      }
    })
  }

  @Mutation
  setStateCacheToLearningDailyPracticeListMap(payload: StateCache) {
    this.learningDailyPracticeListMap = this.learningDailyPracticeListMap.filter(
      p => p.learningId !== payload.learningId
    )
    this.learningDailyPracticeListMap.push(payload)
  }

  @Mutation
  setNeedReload(payload: any) {
    const stateCache = this.learningDailyPracticeListMap.find(p => p.learningId === payload.learningId)
    if (stateCache) {
      stateCache.needReload = true
    }
  }

  @Mutation
  setAnswerId(answerId: string) {
    this.currentAnswerId = answerId
  }

  /**
   * 获取每日一练试卷
   */
  get getQuestionPractice() {
    return (schemeId: string, learningId: string) => {
      return this.learningDailyPracticeListMap.find(p => p.learningId === learningId)?.questionPractice
    }
  }

  /**
   * 获取答卷列表最近七天
   */
  get getLatestDailyPracticeAnswer() {
    return (schemeId: string, learningId: string) => {
      return this.learningDailyPracticeListMap.find(p => p.learningId === learningId)?.latestDailyPracticeAnswer
    }
  }

  /**
   * 获取参与人数
   */
  get getParticipantsNumber() {
    return (schemeId: string, learningId: string) => {
      return this.learningDailyPracticeListMap.find(p => p.learningId === learningId)?.participantsNumber
    }
  }

  /**
   * 获取每日一练天数
   */
  get getPracticeDayCount() {
    return (schemeId: string, learningId: string) => {
      return this.learningDailyPracticeListMap.find(p => p.learningId === learningId)?.practiceDayCount
    }
  }

  /**
   * 获取7天内作答统计
   */
  get getDailyPracticeStatistic() {
    return (schemeId: string, learningId: string) => {
      return this.learningDailyPracticeListMap.find(p => p.learningId === learningId)?.dailyPracticeStatistic
    }
  }

  /**
   * 答卷状态
   * @return answerStatus 1:未开始做题 2：正在作答 3：已交卷
   */
  get getAnswerStatus() {
    return (schemeId: string, learningId: string, day: string) => {
      return this.getLatestDailyPracticeAnswer(schemeId, learningId)?.find(
        (p: DailyPracticeAnswer) => moment(p.createTime).format('YYYY-MM-DD') === day
      )
        ? this.getLatestDailyPracticeAnswer(schemeId, learningId)?.find(
            (p: DailyPracticeAnswer) => moment(p.createTime).format('YYYY-MM-DD') === day
          )?.complete
          ? 3
          : 2
        : 1
    }
  }
}

export default getModule(DailyModule)
