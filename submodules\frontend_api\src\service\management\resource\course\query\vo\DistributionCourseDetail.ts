import {
  CourseDetailResponse,
  CourseResponse,
  CoursewareDetailResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { TeacherItemResponse } from '@api/gateway/Course-default'
import { TeacherResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

class CheatContent {
  /**
   * 章节名称
   */
  cheatName = ''
  // * 课件名称
  coursewareName = ''
  /**
   * 章节ID
   */
  cheatId = ''
  /**
   * 时长
   */
  timeLength: number = null
  // * 排序
  sort: number = null
}

class CourseContent {
  // * 目录名称
  outLineName = ''
  // * 目录ID
  outLineId = ''
  // * 章节列表
  cheatList: CheatContent[] = []
  // * 排序
  sort: number = null
}
class DistributionCourseDetail {
  /**
   * 课程封面URL
   */
  courseCoverUrl = ''
  /**
   * 课程名称
   */
  courseName = ''
  /**
   * 课程ID
   */
  courseId = ''
  /**
   * 课程简介
   */
  courseDesc = ''
  /**
   * 教师名称
   */
  teacherNames = ''
  /**
   * 课件供应商名称
   */
  providerName = ''
  /**
   * 课件供应商ID
   */
  providerId = ''
  /**
   * 章节内容
   */
  courseContent: CourseContent[] = []

  static from(dto: CourseResponse, teacherMap: TeacherResponse[]) {
    const vo = new DistributionCourseDetail()
    vo.courseCoverUrl = dto.iconPath
    vo.courseName = dto.name
    vo.courseId = dto.id
    vo.courseDesc = dto.aboutsContent
    const name = dto.teacherIds.map(res => teacherMap.find(item => res === item.id)?.name) || ['']
    vo.teacherNames = name.join('、')
    // * 未分配哪来的课件xc供应商
    vo.providerId = dto.supplierId || ''
    vo.providerName = '-'
    return vo
  }

  static addCourseContent(
    vo: DistributionCourseDetail,
    detail: CourseDetailResponse,
    coursewareMap: Map<string, CoursewareDetailResponse>
  ) {
    detail.courseOutlines.map(res => {
      const cheatList = detail.courseChapters.filter(cheat => cheat.courseOutline.id === res.id)
      const temp: CheatContent[] = []
      cheatList.map(cheat => {
        temp.push({
          cheatName: cheat.name,
          cheatId: cheat.id,
          timeLength: cheat.timeLength,
          coursewareName: coursewareMap.get(cheat.coursewareId)?.name || '',
          sort: cheat.sort
        })
      })
      vo.courseContent.push({
        outLineName: res.name,
        outLineId: res.id,
        sort: res.sort,
        cheatList: temp
      })
    })
    return vo
  }
}

export default DistributionCourseDetail
