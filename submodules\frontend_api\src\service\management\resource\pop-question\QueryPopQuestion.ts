import { Page } from '@hbfe/common'
import PopQuestionDTO from '@api/service/management/resource/pop-question/dto/PopQuestionDTO'

class QueryPopQuestion {
  /**
   * 查询弹窗题分页
   * @param page 分页信息
   * @param coursewareId 课件 id
   */
  async pagePopQuestion(page: Page, coursewareId: string): Promise<Array<PopQuestionDTO>> {
    return new Array<PopQuestionDTO>()
  }
}

export default QueryPopQuestion
