import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformRollPicture'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformRollPicture'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum RollPictureTypeEnum {
  DEFAULT = 'DEFAULT',
  PORTAL = 'PORTAL',
  MINI_PROGRAM = 'MINI_PROGRAM'
}

// 类

/**
 * @author: puxf
@date: 2020/2/3
@description:
 */
export class RollPictureRequest {
  /**
   * 主键id 修改时有值 新增时没值
   */
  id?: string
  /**
   * 轮播图类型
@see com.fjhb.btpx.platform.service.rollpicture.RollPictureTypeEnum
   */
  type?: string
  /**
   * 轮播图附件地址
   */
  attachmentUrl?: string
  /**
   * 链接地址
   */
  url?: string
  /**
   * 轮播图描述
   */
  description?: string
  /**
   * 轮播图状态（0-停用 1-启用）
   */
  status?: number
  /**
   * 轮播图排序
   */
  sort?: number
}

/**
 * @author: puxf
@date: 2021/2/2
@description: 轮播图
 */
export class RollPictureDetailResponse {
  /**
   * id
   */
  id: string
  /**
   * 轮播图类型
@see com.fjhb.btpx.platform.service.rollpicture.RollPictureTypeEnum
   */
  type: string
  /**
   * 轮播图附件地址
   */
  attachmentUrl: string
  /**
   * 链接地址
   */
  url: string
  /**
   * 轮播图描述
   */
  description: string
  /**
   * 创建人
   */
  creator: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime: string
  /**
   * 轮播图状态 0-停用 1-启用
   */
  status: number
  /**
   * 轮播图排序
   */
  sort: number
  /**
   * 是否内置
   */
  internal: boolean
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取轮播图详情
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findById(
    id: string,
    query: DocumentNode = GraphqlImporter.findById,
    operation?: string
  ): Promise<Response<RollPictureDetailResponse>> {
    return commonRequestApi<RollPictureDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取所有轮播图
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listAll(
    query: DocumentNode = GraphqlImporter.listAll,
    operation?: string
  ): Promise<Response<Array<RollPictureDetailResponse>>> {
    return commonRequestApi<Array<RollPictureDetailResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 通过类别获取轮播图列表
   * @return
   * @param query 查询 graphql 语法文档
   * @param rollPictureType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listByType(
    rollPictureType: RollPictureTypeEnum,
    query: DocumentNode = GraphqlImporter.listByType,
    operation?: string
  ): Promise<Response<Array<RollPictureDetailResponse>>> {
    return commonRequestApi<Array<RollPictureDetailResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { rollPictureType },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 新增轮播图
   * @param dto
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param dto 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async create(
    dto: RollPictureRequest,
    mutate: DocumentNode = GraphqlImporter.create,
    operation?: string
  ): Promise<Response<RollPictureDetailResponse>> {
    return commonRequestApi<RollPictureDetailResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { dto },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除轮播图(设置isDeleted为1)
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteById(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deleteById,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除轮播图(设置isDeleted为1)
   * @param idList
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param idList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteByIdList(
    idList: Array<string>,
    mutate: DocumentNode = GraphqlImporter.deleteByIdList,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { idList },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新轮播图，Id为空时新增
   * @param requestList
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param requestList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async modify(
    requestList: Array<RollPictureRequest>,
    mutate: DocumentNode = GraphqlImporter.modify,
    operation?: string
  ): Promise<Response<Array<RollPictureDetailResponse>>> {
    return commonRequestApi<Array<RollPictureDetailResponse>>(
      SERVER_URL,
      {
        query: mutate,
        variables: { requestList },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 轮播图下移指定偏移量
   * @param id
   * @param offset
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async moveDown(
    params: { id?: string; offset: number },
    mutate: DocumentNode = GraphqlImporter.moveDown,
    operation?: string
  ): Promise<Response<RollPictureDetailResponse>> {
    return commonRequestApi<RollPictureDetailResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 轮播图上移指定偏移量
   * @param id
   * @param offset
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async moveUp(
    params: { id?: string; offset: number },
    mutate: DocumentNode = GraphqlImporter.moveUp,
    operation?: string
  ): Promise<Response<RollPictureDetailResponse>> {
    return commonRequestApi<RollPictureDetailResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新轮播图
   * @param dto
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param dto 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async update(
    dto: RollPictureRequest,
    mutate: DocumentNode = GraphqlImporter.update,
    operation?: string
  ): Promise<Response<RollPictureDetailResponse>> {
    return commonRequestApi<RollPictureDetailResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { dto },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
