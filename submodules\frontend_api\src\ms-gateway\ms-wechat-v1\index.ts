import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-wechat-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-wechat-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 微信人脸核身认证请求
<AUTHOR>
 */
export class ApplyFacialRecognitionVerifyRequest {
  /**
   * 姓名
   */
  name?: string
  /**
   * 身份证号码
   */
  idCardNumber?: string
}

/**
 * 微信公众号JS SDK 权限签名申请信息
 */
export class ApplyJsSDKSignatureRequest {
  /**
   * 公众号AppId，不填表示当前项目默认只有一个微信公众号，自动从配置中获取
   */
  appId?: string
  /**
   * 【必填】当前网页的URL，不包含#及其后面部分
   */
  url?: string
}

/**
 * 获取二维码
@author: zhengp
@since 2021/9/2 10:16
 */
export class GetAppletCodeRequest {
  /**
   * 微信小程序 appId 不填表示当前项目默认只有一个小程序，自动从配置中获取
   */
  appId?: string
  /**
   * page参数 选填 如果不填写这个字段，默认跳主页面
   */
  page?: string
  /**
   * scene参数 必填
   */
  scene?: string
  /**
   * width参数 选填
   */
  width?: string
  /**
   * 自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调，默认 false 选填
   */
  autoColor?: boolean
  /**
   * auto_color 为 false 时生效，使用 rgb 设置颜色 例如 {&quot;r&quot;:&quot;xxx&quot;,&quot;g&quot;:&quot;xxx&quot;,&quot;b&quot;:&quot;xxx&quot;} 十进制表示 选填
   */
  lineColor?: string
  /**
   * 是否需要透明底色，为 true 时，
生成透明底色的小程序 选填
   */
  isHyaline?: boolean
}

/**
 * 获取解密手机号
@author: zhengp 2021/12/2 16:34
 */
export class GetDecodeTelephoneRequest {
  /**
   * 用户openId [必填]
   */
  openId: string
  /**
   * 加密数据 [必填]
   */
  encryptedData: string
  /**
   * 加密算法的初始向量 [必填]
   */
  iv: string
}

/**
 * 获取微信用户信息
@author: zhengp
@since 2021/9/3 14:16
 */
export class GetUserInfoRequest {
  /**
   * 微信appId，不填表示当前项目默认只有一个微信开发平台程序，自动从配置中获取
   */
  appId?: string
  /**
   * 授权临时票据code
   */
  code?: string
  /**
   * 国家地区语言版本，默认为中文简体
   */
  lang?: string
}

/**
 * 校验人脸核身认证结果请求
<AUTHOR>
 */
export class ValidFacialRecognitionVerifyResultRequest {
  /**
   * jsapi返回的加密key（凭据）
   */
  verify_result?: string
}

/**
 * 获取微信opneiId和unionId等信息的入参
@author: zhengp
@since 2021/9/9 10:00
 */
export class WXAppletInfoQueryParam {
  appId?: string
  code?: string
  encryptedData?: string
  iv?: string
}

/**
 * <AUTHOR>
 */
export class ResponseEntityValidResult {
  /**
   * 凭证类型
@see TokenTypes
   */
  tokenType: number
  /**
   * 凭证值
@apiNote access_token or user_id_key
   */
  tokenValue: string
  /**
   * 响应码
0：成功
40001：获取 access_token 时 AppSecret 错误，或者 access_token 无效
40002：请确保 grant_type 字段值为 client_credential
40013：不合法的 AppID
84001：非法identity_id
84002：用户信息过期
84003：用户信息不存在
   */
  code: string
  /**
   * 响应信息
   */
  msg: string
}

/**
 * 微信人脸核身认证响应
<AUTHOR>
 */
export class ApplyFacialRecognitionVerifyResponse {
  /**
   * 表示用户姓名、身份证的凭证
   */
  user_id_key: string
  /**
   * 响应码
0：成功
40001：获取 access_token 时 AppSecret 错误，或者 access_token 无效
40002：请确保 grant_type 字段值为 client_credential
40013：不合法的 AppID
84001：非法identity_id
84002：用户信息过期
84003：用户信息不存在
   */
  code: string
  /**
   * 响应信息
   */
  msg: string
}

/**
 * @author: zhengp
@since 2021/9/10 9:37
 */
export class GetAppletCodeResponse {
  /**
   * 二维码存储mfs地址
   */
  mfsAddress: string
}

/**
 * 获取微信用户信息
@author: zhengp
@since 2021/9/3 14:16
 */
export class GetUserInfoResponse {
  /**
   * 用户统一标识。针对一个微信开放平台帐号下的应用，同一用户的unionid是唯一的。
   */
  unionId: string
  /**
   * 普通用户的标识，对当前开发者帐号唯一
   */
  openId: string
  /**
   * 普通用户昵称
   */
  nickName: string
  /**
   * 普通用户性别，1为男性，2为女性
   */
  sex: number
  /**
   * 国家，如中国为CN
   */
  country: string
  /**
   * 普通用户个人资料填写的省份
   */
  province: string
  /**
   * 普通用户个人资料填写的城市
   */
  city: string
  /**
   * 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像）
   */
  headImageUrl: string
}

/**
 * 微信公众号JS SKD 权限签名信息
 */
export class JsSDKSignatureResponse {
  /**
   * 生成签名的随机串
   */
  nonceStr: string
  /**
   * 生成签名的时间戳
   */
  timestamp: number
  /**
   * 签名
   */
  signature: string
}

/**
 * 手机号信息
@author: zhengp 2021/12/3 16:21
 */
export class TelephoneData {
  /**
   * 200 成功 500 失败
   */
  code: number
  msg: string
  data: Result
}

export class Result {
  /**
   * 用户绑定的手机号（国外手机号会有区号）
   */
  phoneNumber: string
  /**
   * 没有区号的手机号
   */
  purePhoneNumber: string
  /**
   * 区号
   */
  countryCode: string
}

/**
 * 校验人脸核身认证结果响应
<AUTHOR>
 */
export class ValidFacialRecognitionVerifyResultResponse {
  /**
   * 姓名和身份证生成的jwt token
   */
  token: string
  /**
   * 响应码
0：成功
40001：获取 access_token 时 AppSecret 错误，或者 access_token 无效
40002：请确保 grant_type 字段值为 client_credential
40013：不合法的 AppID
84001：非法identity_id
84002：用户信息过期
84003：用户信息不存在
   */
  code: string
  /**
   * 响应信息
   */
  msg: string
}

/**
 * @author: zhengp
@since 2021/9/9 10:02
获取微信必要信息
 */
export class WXAppletIdInfoResponse {
  openId: string
  unionId: string
  accessToken: string
  nickname: string
  refreshToken: string
  sex: string
  headimgurl: string
  purePhoneNumber: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请人脸核身认证
   * 入参：{姓名、身份证}
   * 出参：{code,msg,user_id_key(表示用户姓名、身份证的凭证)}
   * 请求url：https://api.weixin.qq.com/cityservice/face/identify/getuseridkey?access_token={access_token}
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyFacialRecognitionVerify(
    request: ApplyFacialRecognitionVerifyRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.applyFacialRecognitionVerify,
    operation?: string
  ): Promise<Response<ApplyFacialRecognitionVerifyResponse>> {
    return commonRequestApi<ApplyFacialRecognitionVerifyResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取接口调用凭据
   * 请求url：https://api.weixin.qq.com/cgi-bin/token?appid={appId}&secret={secret}&grant_type=client_credential
   * @return {@link ApplyAccessTokenResult}
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAccessToken(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAccessToken,
    operation?: string
  ): Promise<Response<ResponseEntityValidResult>> {
    return commonRequestApi<ResponseEntityValidResult>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAppletCode(
    request: GetAppletCodeRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAppletCode,
    operation?: string
  ): Promise<Response<GetAppletCodeResponse>> {
    return commonRequestApi<GetAppletCodeResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取解密后的手机号
   * @param request 加密的数
   * @return 解密后的json串
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDecodeTelephone(
    request: GetDecodeTelephoneRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDecodeTelephone,
    operation?: string
  ): Promise<Response<TelephoneData>> {
    return commonRequestApi<TelephoneData>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUserInfo(
    request: GetUserInfoRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getUserInfo,
    operation?: string
  ): Promise<Response<GetUserInfoResponse>> {
    return commonRequestApi<GetUserInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getWXAppletUserInfo(
    request: WXAppletInfoQueryParam,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getWXAppletUserInfo,
    operation?: string
  ): Promise<Response<WXAppletIdInfoResponse>> {
    return commonRequestApi<WXAppletIdInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 校验人脸核身认证结果
   * 入参：{verify_result本次认证结果凭据，第三方可以选择根据这个凭据获取相关信息}
   * 出参：{code,msg,token(姓名和身份证生成的jwt)}
   * 请求url：https://api.weixin.qq.com/cityservice/face/identify/getinfo?access_token={access_token}
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validFacialRecognitionVerifyResult(
    request: ValidFacialRecognitionVerifyResultRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.validFacialRecognitionVerifyResult,
    operation?: string
  ): Promise<Response<ValidFacialRecognitionVerifyResultResponse>> {
    return commonRequestApi<ValidFacialRecognitionVerifyResultResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请微信公众号JS SDK 权限签名
   * <p>微信文档地址：https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#62</p>
   * @param applyInfo 申请信息
   * @return 签名信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyJsSDKSignature(
    applyInfo: ApplyJsSDKSignatureRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyJsSDKSignature,
    operation?: string
  ): Promise<Response<JsSDKSignatureResponse>> {
    return commonRequestApi<JsSDKSignatureResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
