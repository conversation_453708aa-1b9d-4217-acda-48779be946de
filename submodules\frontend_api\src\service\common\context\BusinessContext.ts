import ServiceProvider from './ServiceProvider'
import UserIdentity from './UserIdentity'
import DataRouterIdentity from './DataRouterIdentity'

/**
 * 业务上下文
 */
class BusinessContext {
  sequenceNo: string
  /*
   所属平台编号
   */
  platformId: string
  /*
   所属平台版本编号
   */
  platformVersionId: string
  /*
   所属项目编号
   */
  projectId: string
  /*
   所属子项目编号
   */
  subProjectId: string
  /*

   */
  serviceProvider: ServiceProvider = new ServiceProvider()
  user: UserIdentity = new UserIdentity()
  dataRouter: DataRouterIdentity = new DataRouterIdentity()

  async getUser() {
    this.user = new UserIdentity()
  }

  async getDataRouter() {
    this.dataRouter = new DataRouterIdentity()
  }

  async getServiceProvider() {
    this.serviceProvider = new ServiceProvider()
  }
}

export default BusinessContext
