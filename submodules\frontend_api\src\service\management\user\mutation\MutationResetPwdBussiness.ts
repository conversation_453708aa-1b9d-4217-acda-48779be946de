import basicdata, {
  ImmediateResetPasswordRequest,
  ResetPasswordRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'

/**
 * 重置密码
 */
class MutationResetPwdBussiness {
  async doResetStudentPwd(accountId: string, password: string) {
    return await this.doResetPwd(accountId, password)
  }

  async doResetAdminPwd(accountId: string, password: string) {
    return await this.doResetPwd(accountId, password)
  }

  async doResetRegionAdminPwd(accountId: string, password: string) {
    return await this.doResetPwd(accountId, password)
  }

  async doResetCollectivePwd(accountId: string, password: string) {
    return await this.doResetPwd(accountId, password)
  }

  private async doResetPwd(accountId: string, password: string) {
    const params = new ResetPasswordRequest()
    params.accountId = accountId
    params.password = password
    const response = await basicdata.ResetPassword(params)
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('重置密码失败', response)
      return Promise.reject(response)
    }
    return response
  }
}

export default MutationResetPwdBussiness
