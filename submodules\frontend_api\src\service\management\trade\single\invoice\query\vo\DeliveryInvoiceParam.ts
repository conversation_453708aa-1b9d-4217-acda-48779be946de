/*
 * @Description: 发票配送查询条件
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-06 08:33:09
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-15 15:54:26
 */
import { DeliveryStatusEnum, DeliveryWayEnum } from '../../enum/DeliveryInvoiceEnum'

export default class DeliveryInvoiceParamVo {
  /**
   * 配送状态
   */
  deliveryStatus?: DeliveryStatusEnum = 1
  /**
   * 收件人姓名
   */
  name?: string
  /**
   * 登录账号
   */
  loginAccount?: string
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 订单号
   */
  invoiceNo?: string
  /**
   * 状态更新开始时间
   */
  startDate?: string
  /**
   * 状态更新结束时间
   */
  endDate?: string
  /**
   * 配送方式
   */
  deliveryWay?: DeliveryWayEnum
  /**
   * 运单号
   */
  theAwb?: string
  /**
   * 领取人
   */
  recipient?: string
  /**
   * 冻结状态
   */
  frozenState?: boolean
  static to() {
    //转换
  }
}
