import {
  CourseLearningStatisticsResponse,
  ExamLearningStatisticsResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
/**
 * 学员培训方案学习统计信息
 <AUTHOR>
 @version 1.0
 @date 2022/1/17 14:08
 */
export class LearningStatisticsResponse {
  /**
   * 净报名人次
   */
  netRegisterCount: number
  /**
   * 考核通过人次
   */
  qualifiedCount: number
  /**
   * 课程学习统计
   */
  courseLearningStatistic: CourseLearningStatisticsResponse = new CourseLearningStatisticsResponse()
  /**
   * 考试统计
   */
  examLearningStatistic: ExamLearningStatisticsResponse = new ExamLearningStatisticsResponse()
  /**
   * 期别合格数
   */
  issueQualifiedCount: number
  /**
   * 期别未合格数
   */
  issueUnQualifiedCount: number
  /**
   * 问卷未提交的学院方案参训资格数
   */
  questionnaireUnSubmitCount: number
  /**
   * 问卷已提交的学院方案参训资格数
   */
  questionnaireSubmitCount: number
}
export class SchemeLearningStatisticsResponseVo {
  /**
   * 培训班id
   */
  schemeId: string
  /**
   * 统计信息
   */
  learningStatistic: LearningStatisticsResponse = new LearningStatisticsResponse()
  //  培训班详情信息
  trainClassDetail = new TrainClassCommodityVo()

  async addTrainClass() {
    // try {
    //   queryClassDetail.commodityId = this.commoditySkuId
    //   const res = await queryClassDetail.queryTrainClassDetail()
    //
    //   if (res.isSuccess()) {
    //     this.trainClassDetail = queryClassDetail.trainClassDetail
    //   }
    // } catch (e) {
    //   console.log(
    //     '报错了，所处位置/service/management/statisticalReport/query/vo/CommodityOpenReportFormResponseVo.ts所处方法，addTrainClass',
    //     e
    //   )
    // }
  }
}
