"""独立部署的微服务,K8S服务名:ms-knowledge-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询未完成的学习心得主题
		@param request :
		@return com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.response.QueryUnCompleteLearningExperienceTopicResponse
		<AUTHOR> By Cb
		@date 2024/1/4 10:43
	"""
	queryUnCompleteLearningExperienceTopic(request:QueryUnCompleteLearningExperienceTopicRequest):QueryUnCompleteLearningExperienceTopicResponse
}
type Mutation {
	"""学员申请提交学习心得
		@param request
		@return {@link ApplyLearningExperienceResponse}
	"""
	applyLearningExperience(request:ApplyLearningExperienceRequest):ApplyLearningExperienceResponse
	"""学员心得审核
		@param request
		@return {@link AuditStudentLearningExperienceResponse}
	"""
	auditStudentLearningExperience(request:AuditStudentLearningExperienceRequest):AuditStudentLearningExperienceResponse
	"""学员心得撤回
		@return {@link CancelStudentLearningExperienceResponse}
	"""
	cancelStudentLearningExperience(request:CancelStudentLearningExperienceRequest):CancelStudentLearningExperienceResponse
	"""校验接口"""
	check(request:CheckLearningExperienceRequest):SubmitLearningExperienceResponse
	"""新增课程心得主题信息"""
	createCourseLearningExperienceTopic(request:CreateCourseLearningExperienceTopicRequest):CreateCourseLearningExperienceTopicResponse
	"""学习心得删除
		@return {@link RemoveStudentLearningExperienceResponse}
	"""
	removeStudentLearningExperience(request:RemoveStudentLearningExperienceRequest):RemoveStudentLearningExperienceResponse
	"""学员保存学习心得
		@param request
		@return {@link SaveLearningExperienceResponse}
	"""
	saveLearningExperience(request:SaveLearningExperienceRequest):SaveLearningExperienceResponse
	"""保存心得主题文本信息
		@param content 学员心得活动内容
		@return {@link SaveExperienceTopicContentResponse}
	"""
	saveLearningExperienceTopicContent(content:String!):SaveExperienceTopicContentResponse
	"""学员提交学习心得
		@param request
		@return {@link SubmitLearningExperienceResponse}
	"""
	submitLearningExperience(request:SubmitLearningExperienceRequest):SubmitLearningExperienceResponse
	"""修改课程心得主题信息"""
	updateCourseLearningExperienceTopic(request:UpdateCourseLearningExperienceTopicRequest):UpdateCourseLearningExperienceTopicResponse
	"""更新心得主题文本信息
		@param request
		@return {@link UpdateExperienceTopicContentResponse}
	"""
	updateLearningExperienceTopicContent(request:UpdateLearningExperienceTopicContentRequest):UpdateExperienceTopicContentResponse
	"""校验课程是否被删除"""
	verifyExists(request:VerifyCourseRequest):SubmitLearningExperienceResponse
}
"""学员申请提交学习心得请求
	<AUTHOR>
	@date 2023/12/4
"""
input ApplyLearningExperienceRequest @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.request.ApplyLearningExperienceRequest") {
	"""学习Token"""
	studentLearningToken:String
	"""资源类型：1-班级 2-课程"""
	resourceType:Int!
	"""学习资源id(班级id or 课程id)"""
	resourceId:String
	"""课程大纲id(课程心得创建时需要)"""
	outlineId:String
	"""学习心得主题id"""
	learningExperienceTopicId:String
}
"""学员心得审核请求
	<AUTHOR>
	@date 2023/12/4
"""
input AuditStudentLearningExperienceRequest @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.request.AuditStudentLearningExperienceRequest") {
	"""学员心得id"""
	studentExperienceId:String!
	"""审核意见"""
	remark:String
	"""得分"""
	score:Double!
	"""主题心得类型 1-班级  2-课程"""
	learningExperienceTopicType:Int!
}
"""学员心得撤回请求
	<AUTHOR>
	@date 2023/12/15 16:32
"""
input CancelStudentLearningExperienceRequest @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.request.CancelStudentLearningExperienceRequest") {
	"""学员心得id"""
	studentExperienceId:String!
	"""主题心得类型 1-班级  2-课程"""
	learningExperienceTopicType:Int!
}
"""学员学习心得校验请求
	<AUTHOR>
	@date 2023/12/12 20:57
"""
input CheckLearningExperienceRequest @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.request.CheckLearningExperienceRequest") {
	"""学员心得token"""
	learningExperienceToken:String
	"""主题心得类型 1-班级  2-课程"""
	learningExperienceTopicType:Int!
}
"""新增课程心得主体信息请求
	<AUTHOR>
	@date 2023/12/4 10:11
"""
input CreateCourseLearningExperienceTopicRequest @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.request.CreateCourseLearningExperienceTopicRequest") {
	"""学习心得id"""
	learningExperienceId:String
	"""学习心得主题"""
	experienceTopicName:String
	"""学习心得内容id"""
	descriptionContentId:String
	"""参加活动时间类型
		@see ParticipateTimeType
	"""
	participateTimeType:Int!
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
	"""参与形式
		@see ParticipateType
	"""
	participateType:Int!
	"""提交要求(如果是附件提交，就是文件大小，如果是在线编辑，就是字数)"""
	submitLimitNum:Double!
	"""审核方式
		@see AuditType
	"""
	auditType:Int!
	"""提交次数"""
	submitLimitCount:Int!
	"""课程id列表"""
	courseIds:[String]
	"""通过分数"""
	passScore:Double!
	"""总分"""
	totalScore:Double!
	"""是否必选"""
	isRequired:Boolean!
}
"""查询为完成的学习心得主题请求
	<AUTHOR> By Cb
	@since 2024/01/04 10:40
"""
input QueryUnCompleteLearningExperienceTopicRequest @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.request.QueryUnCompleteLearningExperienceTopicRequest") {
	"""学习方案ID"""
	learningSchemeId:String!
	"""学号"""
	studentNo:String!
}
"""删除学员心得请求
	<AUTHOR>
	@date 2023/12/15 16:34
"""
input RemoveStudentLearningExperienceRequest @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.request.RemoveStudentLearningExperienceRequest") {
	"""学员心得id"""
	studentExperienceId:String!
	"""主题心得类型 1-班级  2-课程"""
	learningExperienceTopicType:Int!
}
"""学员保存学习心得请求请求
	<AUTHOR>
	@date 2023/12/4
"""
input SaveLearningExperienceRequest @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.request.SaveLearningExperienceRequest") {
	"""学员心得token"""
	learningExperienceToken:String!
	"""学习心得信息"""
	content:String
	"""编辑形式  1.提交附件 2.在线编辑"""
	participateType:Int!
	"""主题心得类型 1-班级  2-课程"""
	learningExperienceTopicType:Int!
}
"""学员提交学习心得请求
	<AUTHOR>
	@date 2023/12/4 11:37
"""
input SubmitLearningExperienceRequest @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.request.SubmitLearningExperienceRequest") {
	"""学员心得token"""
	learningExperienceToken:String
	"""学习心得信息"""
	content:String
	"""编辑形式  1.提交附件 2.在线编辑"""
	participateType:Int!
	"""主题心得类型 1-班级  2-课程"""
	learningExperienceTopicType:Int!
}
"""修改课程心得主题信息请求
	<AUTHOR>
	@date 2023/12/4 11:22
"""
input UpdateCourseLearningExperienceTopicRequest @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.request.UpdateCourseLearningExperienceTopicRequest") {
	"""课程心得主题信息"""
	learningExperienceTopicId:String
	"""学习心得id"""
	learningExperienceId:String
	"""学习心得主题"""
	experienceTopicName:String
	"""学习心得内容id"""
	descriptionContentId:String
	"""参加活动时间类型
		@see ParticipateTimeType
	"""
	participateTimeType:Int!
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
	"""参与形式
		@see ParticipateType
	"""
	participateType:Int!
	"""提交要求(如果是附件提交，就是文件大小，如果是在线编辑，就是字数)"""
	submitLimitNum:Double!
	"""审核方式
		@see AuditType
	"""
	auditType:Int!
	"""提交次数"""
	submitLimitCount:Int!
	"""课程id列表"""
	courseIds:[String]
	"""通过分数"""
	passScore:Double!
	"""总分"""
	totalScore:Double!
	"""是否必选"""
	isRequired:Boolean!
}
"""更新主题文本信息请求
	<AUTHOR>
	@date 2023/12/4
"""
input UpdateLearningExperienceTopicContentRequest @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.request.UpdateLearningExperienceTopicContentRequest") {
	"""学员心得活动内容id"""
	contentId:String!
	"""学员心得活动内容"""
	content:String
}
"""校验课程是否存在请求
	<AUTHOR>
	@date 2023/12/29 16:24
"""
input VerifyCourseRequest @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.request.VerifyCourseRequest") {
	"""学号"""
	studentNo:String
	"""资源类型：1-班级 2-课程"""
	resourceType:Int!
	"""学习资源id(班级id or 课程id)"""
	resourceId:String
	"""课程大纲id(课程心得创建时需要)"""
	outlineId:String
	"""学习心得主题id"""
	learningExperienceTopicId:String
}
"""申请提交学习心得返回
	1001 - 未完成课程学习
	1002 - 未完成必选学习心得
	<AUTHOR>
	@date 2023/12/4
"""
type ApplyLearningExperienceResponse @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.response.ApplyLearningExperienceResponse") {
	"""学习心得Token"""
	learningExperienceToken:String
	"""错误码"""
	code:String
	"""错误信息"""
	msg:String
}
"""学员心得审核返回值
	<AUTHOR>
	@date 2023/12/4 11:41
"""
type AuditStudentLearningExperienceResponse @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.response.AuditStudentLearningExperienceResponse") {
	"""错误码"""
	code:String
	"""错误信息"""
	msg:String
}
"""学员学习心得撤回返回值
	<AUTHOR>
	@date 2023/12/4 11:42
"""
type CancelStudentLearningExperienceResponse @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.response.CancelStudentLearningExperienceResponse") {
	"""错误码"""
	code:String
	"""错误信息"""
	msg:String
}
"""新增课程心得主题信息返回值
	<AUTHOR>
	@date 2023/12/4 10:53
"""
type CreateCourseLearningExperienceTopicResponse @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.response.CreateCourseLearningExperienceTopicResponse") {
	"""课程心得主题信息id"""
	learningExperienceTopicId:String
	"""错误码"""
	code:String
	"""错误信息"""
	msg:String
}
"""查询未完成的学习心得主题响应
	<AUTHOR> By Cb
	@since 2024/01/03 9:56
"""
type QueryUnCompleteLearningExperienceTopicResponse @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.response.QueryUnCompleteLearningExperienceTopicResponse") {
	"""code
		1001 - 无未参加的学习心得主题
		1002 - 存在未参加学习心得主题，但是只有课程心得主题
		1003 - 存在未参加学习心得主题，且不只有课程心得主题
	"""
	code:String
	"""message"""
	message:String
}
"""学员学习心得删除
	<AUTHOR>
	@date 2023/12/4 11:42
"""
type RemoveStudentLearningExperienceResponse @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.response.RemoveStudentLearningExperienceResponse") {
	"""错误码"""
	code:String
	"""错误信息"""
	msg:String
}
"""保存心得主题文本信息返回
	<AUTHOR>
	@date 2023/12/4
"""
type SaveExperienceTopicContentResponse @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.response.SaveExperienceTopicContentResponse") {
	"""错误码"""
	code:String
	"""错误信息"""
	msg:String
	"""文本信息id"""
	contentId:String
}
"""保存学习心得返回
	<AUTHOR>
	@date 2023/12/4
"""
type SaveLearningExperienceResponse @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.response.SaveLearningExperienceResponse") {
	"""错误码"""
	code:String
	"""错误信息"""
	msg:String
}
"""学员提交学习心得返回值
	<AUTHOR>
	@date 2023/12/4 11:40
"""
type SubmitLearningExperienceResponse @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.response.SubmitLearningExperienceResponse") {
	"""错误码"""
	code:String
	"""错误信息"""
	msg:String
}
"""修改课程心得主题信息返回值
	<AUTHOR>
	@date 2023/12/4 11:23
"""
type UpdateCourseLearningExperienceTopicResponse @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.response.UpdateCourseLearningExperienceTopicResponse") {
	"""心得主题信息id"""
	learningExperienceTopicId:String
	"""错误码"""
	code:String
	"""错误信息"""
	msg:String
}
"""更新心得主题文本信息返回值
	<AUTHOR>
	@date 2023/12/4 11:46
"""
type UpdateExperienceTopicContentResponse @type(value:"com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.response.UpdateExperienceTopicContentResponse") {
	"""文本信息id"""
	contentId:String
	"""错误码"""
	code:String
	"""错误信息"""
	msg:String
}

scalar List
