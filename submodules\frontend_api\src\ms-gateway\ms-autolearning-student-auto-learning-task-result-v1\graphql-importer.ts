import findLastFailSubTaskByMainTaskIdList from './queries/findLastFailSubTaskByMainTaskIdList.graphql'
import queryByPage from './queries/queryByPage.graphql'
import queryNewestTaskResultByQualificationIds from './queries/queryNewestTaskResultByQualificationIds.graphql'
import queryStudentAutoLearningTaskResult from './queries/queryStudentAutoLearningTaskResult.graphql'
import queryStudentAutoLearningTaskResultByPage from './queries/queryStudentAutoLearningTaskResultByPage.graphql'
import queryStudentNormalAutoLearningTaskResult from './queries/queryStudentNormalAutoLearningTaskResult.graphql'

export {
  findLastFailSubTaskByMainTaskIdList,
  queryByPage,
  queryNewestTaskResultByQualificationIds,
  queryStudentAutoLearningTaskResult,
  queryStudentAutoLearningTaskResultByPage,
  queryStudentNormalAutoLearningTaskResult
}
