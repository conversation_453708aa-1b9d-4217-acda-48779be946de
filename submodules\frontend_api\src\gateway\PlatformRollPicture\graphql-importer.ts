import findById from './queries/findById.graphql'
import listAll from './queries/listAll.graphql'
import listByType from './queries/listByType.graphql'
import create from './mutates/create.graphql'
import deleteById from './mutates/deleteById.graphql'
import deleteByIdList from './mutates/deleteByIdList.graphql'
import modify from './mutates/modify.graphql'
import moveDown from './mutates/moveDown.graphql'
import moveUp from './mutates/moveUp.graphql'
import update from './mutates/update.graphql'

export { findById, listAll, listByType, create, deleteById, deleteByIdList, modify, moveDown, moveUp, update }
