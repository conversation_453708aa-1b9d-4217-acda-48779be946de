<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--新增主方案-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20 f-mb20">新增主方案</el-button>
        <el-drawer
          title="选择培训方案"
          :visible.sync="dialog1"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="f-pl10 f-pr10">
              <el-row :gutter="16" class="m-query is-border-bottom">
                <el-form :inline="true" label-width="auto">
                  <el-col :span="8">
                    <el-form-item label="年度">
                      <el-select v-model="select" clearable placeholder="请选择培训年度">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训方案名称">
                      <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="行业">
                      <el-select v-model="select" clearable placeholder="请选择行业">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="科目类型">
                      <el-select v-model="select" clearable placeholder="请选择科目类型">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训专业">
                      <el-select v-model="select" clearable placeholder="请选择培训专业">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="方案学时">
                      <el-input v-model="input" class="input-num" style="width: 105px;" />
                      -
                      <el-input v-model="input" class="input-num" style="width: 105px;" />
                      <span class="f-ml5">学时</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="f-fr">
                    <el-form-item class="f-tr">
                      <el-button type="primary">查询</el-button>
                      <el-button>重置</el-button>
                      <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon--right"></i></el-button>-->
                      <el-button type="text">收起<i class="el-icon-arrow-up el-icon&#45;&#45;right"></i></el-button>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
            <el-alert type="warning" :closable="false" class="m-alert f-mb10">
              <p class="f-c3">
                已选择 <span class="f-co">x</span> 个方案，报名所选方案时将自动选择当前方案合并下单，请确认无误后提交。
              </p>
            </el-alert>
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table" max-height="400px">
              <el-table-column width="100" fixed="left">
                <template #header>
                  <el-checkbox v-model="checked">选择全部</el-checkbox>
                </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text" size="mini">选择</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text" size="mini">取消选择</el-button>
                  </div>
                </template>
              </el-table-column>
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="培训方案名称" min-width="240" fixed="left">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <div>课程名称课程名称课程名称课程名称</div>
                    <div>
                      <el-tag type="danger" effect="dark" size="mini">已下架</el-tag>
                    </div>
                  </div>
                  <div v-else>
                    <div>课程名称课程名称课程名称课程名称</div>
                    <div>
                      <el-tag type="danger" effect="dark" size="mini">不开放学员报名</el-tag>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="报名学时" min-width="100">
                <template>50</template>
              </el-table-column>
              <el-table-column label="价格" min-width="90">
                <template>50.00</template>
              </el-table-column>
              <el-table-column label="培训属性" min-width="220">
                <template>
                  <p>某某行业</p>
                  <p>属性1：属性值</p>
                  <p>属性2：属性值</p>
                  <p>属性3：属性值</p>
                </template>
              </el-table-column>
              <el-table-column label="学习起止时间" min-width="120">
                <template>长期有效</template>
              </el-table-column>
              <el-table-column label="报名起止时间" min-width="200">
                <template>
                  <p>起始：2021-10-15 00:21:21</p>
                  <p>结束：2021-10-15 00:21:21</p>
                </template>
              </el-table-column>
            </el-table>
            <div class="f-mt10">
              <!--分页-->
              <el-pagination
                background
                class="f-tc"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确 定</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames: ['1'],
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: true,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleChange(val) {
        console.log(val)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
