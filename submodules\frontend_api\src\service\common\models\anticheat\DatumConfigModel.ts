/**
 * 基准照采集配置信息
 */
class DatumConfigModel {
  /**
   * 基准照片采集配置编号
   */
  id: string
  /**
   * 采集照片数量，默认：1
   */
  collectCount: number
  /**
   * 更新次数
   */
  updateCount: number
  /**
   * 采集协议文本
   */
  protocolText: string
  /**
   * 创建人
   */
  createUserId: string
  /**
   * 最后更新人
   */
  updateUserId: string
  /**
   * 最后更新时间
   */
  updateTime: Date
  /**
   * 创建时间
   */
  createTime: Date
  /**
   * 是否启用
   */
  enable: boolean
}

export default DatumConfigModel
