import CourseWareCreate from '@api/service/common/models/course/course-ware/CourseWareCreate'
import CourseChapterCreate from '@api/service/common/models/course/create/CourseChapterCreate'

class CourseCreate {
  /**
   * 课程名称
   */
  name = ''
  /**
   * 封面图片路径
   */
  iconPath = ''
  /**
   * 课程简介
   */
  abouts = ''

  /**
   * 是否启用
   */
  enabled: boolean

  /**
   * 分类id
   */
  categoryIds?: Array<string>
  // /**
  //  * 课程分类
  //  */
  // category: CourseCategory = new CourseCategory()
  /**
   * 课程章节
   */
  courseOutline: Array<CourseChapterCreate> = new Array<CourseChapterCreate>()

  /**
   * 课件
   */
  courseWares: Array<CourseWareCreate>

  /**
   * 教师id集合
   */
  teacherIds: Array<string>
}

export default CourseCreate
