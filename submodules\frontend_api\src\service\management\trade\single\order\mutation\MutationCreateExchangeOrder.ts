import msOrder, { SellerApplyOrderExchangeRequest, SellerApplyOrderReturnRequest } from '@api/ms-gateway/ms-order-v1'
import { Response } from '@hbfe/common'
import { CalReturnOrderUtil } from '@api/service/management/trade/single/order/utils/CalReturnOrderUtil'
export class MutationCreateExchangeOrder {
  orderNo = ''
  subOrderNo = ''
  /**
   * 商品id
   */
  commoditySkuId = ''
  /**
   * 定价策略id
   */
  exchangePricingPolicyId = ''
  /**
   * 退货原因id
   */
  reasonId?: string
  /**
   * 退货原因描述
   */
  description?: string
  /**
   * 原期别id
   */
  originIssueId?: string
  /**
   * 目标期别id
   */
  targetIssueID?: string

  /*
   *  发起换货（换班）
   *  "60007", "期别不开放报名"
   *  "60004", "报名未开始"
   *  "60005", "报名已结束"
   *  "10005", "期别已开始培训"
   *  "10006", "期别已结束培训"
   *  "60001"  本期培训已报满
   *  "90001", "不在学习起止时间内"
   *  "10001", "用户证书没找到"
   *  "10002" "班级证书主增项不匹配"
   *  "99996" "报名未开始"
   *  "99997" "报名已结束"
   *  "99998" "培训类别不同"
   *  "99999" "资源供应商不同"
   *  "30000" "该班级已下架"
   *  "30001" "购买渠道已关闭"
   *  "30002" "该班级未开放学员报名"
   */
  async sellerApplyExchange() {
    const request = new SellerApplyOrderExchangeRequest()
    request.orderNo = this.orderNo
    request.subOrderNo = this.subOrderNo
    request.reasonId = this.reasonId
    request.description = this.description
    request.exchangeSkuId = this.commoditySkuId
    request.exchangePricingPolicyId = this.exchangePricingPolicyId || undefined
    request.needManualApprove = false
    request.originIssueId = this.originIssueId
    request.targetIssueId = this.targetIssueID
    const res = await msOrder.sellerApplyExchange(request)
    return this.filterRes(res).status
  }

  filterRes(res: Response<any>) {
    return CalReturnOrderUtil.filterRes(res)
  }
}
