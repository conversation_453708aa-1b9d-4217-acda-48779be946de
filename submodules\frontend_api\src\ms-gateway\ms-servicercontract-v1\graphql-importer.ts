import createAndOpenContractProvider from './mutates/createAndOpenContractProvider.graphql'
import createAndOpenOSContract from './mutates/createAndOpenOSContract.graphql'
import createOSContractDraft from './mutates/createOSContractDraft.graphql'
import createOnlineSchoolContract from './mutates/createOnlineSchoolContract.graphql'
import deliveredOSContract from './mutates/deliveredOSContract.graphql'
import disbaleOSContract from './mutates/disbaleOSContract.graphql'
import enableOSContract from './mutates/enableOSContract.graphql'
import findUiThemeColorList from './mutates/findUiThemeColorList.graphql'
import isOnlineSchoolContractExpired from './mutates/isOnlineSchoolContractExpired.graphql'
import newCreateAndOpenOSContract from './mutates/newCreateAndOpenOSContract.graphql'
import openOSContract from './mutates/openOSContract.graphql'
import removeOSContract from './mutates/removeOSContract.graphql'
import renewOSContract from './mutates/renewOSContract.graphql'
import updateOSBasicInfo from './mutates/updateOSBasicInfo.graphql'
import updateOSContract from './mutates/updateOSContract.graphql'
import updateOSTemplate from './mutates/updateOSTemplate.graphql'
import updateOnlineSchool from './mutates/updateOnlineSchool.graphql'
import validOnlineSchoolNameHasResponse from './mutates/validOnlineSchoolNameHasResponse.graphql'
import validUpdateOnlineSchoolNameHasResponse from './mutates/validUpdateOnlineSchoolNameHasResponse.graphql'

export {
  createAndOpenContractProvider,
  createAndOpenOSContract,
  createOSContractDraft,
  createOnlineSchoolContract,
  deliveredOSContract,
  disbaleOSContract,
  enableOSContract,
  findUiThemeColorList,
  isOnlineSchoolContractExpired,
  newCreateAndOpenOSContract,
  openOSContract,
  removeOSContract,
  renewOSContract,
  updateOSBasicInfo,
  updateOSContract,
  updateOSTemplate,
  updateOnlineSchool,
  validOnlineSchoolNameHasResponse,
  validUpdateOnlineSchoolNameHasResponse
}
