import BasicDataQueryForestage from '@api/platform-gateway/platform-training-channel-fore-gateway'
import { Page } from '@hbfe/common'
import { SubjectType } from '@api/service/customer/thematic-config/enum/SubjectType'
/**
 * 专题列表
 */

export default new (class ThematicMap {
  /**
   * map数据
   */
  map: Map<
    string,
    { id: string; region: string[]; type: SubjectType[]; industry: string; unitName: string }
  > = new Map()
  /**
   * 获取详情
   * @param trainingChanelIds 专题id
   * @return {Promise<void>}
   */
  async getThematicMap(trainingChanelIds?: string[]) {
    const noCacheIdList = trainingChanelIds.filter(item => !this.map.has(item))
    if (noCacheIdList.length) {
      const page = new Page(1, noCacheIdList.length)
      const res = await BasicDataQueryForestage.getPageTrainingChannelInfo({
        page,
        trainingChanelIds: noCacheIdList,
        isShowAll: true
      })
      res.data?.currentPageData?.forEach(item => {
        this.map.set(item.id, {
          id: item.id,
          type: item.types,
          industry: item.industrys?.map(item => item.industryName).join(','),
          region: item.regions?.map(
            ite =>
              `${ite.provinceName}${ite.cityName ? `/${ite.cityName}` : ''}${
                ite.countyName ? `/${ite.countyName}` : ''
              }`
          ),
          unitName: item.unitName
        })
      })
    }
  }
})()
