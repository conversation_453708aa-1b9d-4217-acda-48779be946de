import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-teachingplan-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-teachingplan-studentattendance-sds-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * <AUTHOR>
@since 2025-06-06
 */
export class OneClickClockRequest {
  studentLearningToken: string
  planItemGroupIds: Array<string>
  planItemIds: Array<string>
  /**
   * 纬度
   */
  lat: number
  /**
   * 经度
   */
  lng: number
}

/**
 * 签到
<AUTHOR>
 */
export class StudentClockInRequest {
  studentLearningToken: string
  planId: string
  planItemGroupId: string
  planItemId: string
  /**
   * 纬度
   */
  lat: number
  /**
   * 经度
   */
  lng: number
}

/**
 * 签退
<AUTHOR>
 */
export class StudentClockOutRequest {
  studentLearningToken: string
  planId: string
  planItemGroupId: string
  planItemId: string
  /**
   * 纬度
   */
  lat: number
  /**
   * 经度
   */
  lng: number
}

/**
 * 打卡结果
<AUTHOR>
 */
export class ClockResponse {
  /**
   * 状态码
@see com.fjhb.domain.teachingplan.api.sign.consts.attendance.PlanAttendanceResultCodes
成功 SUCCESS &#x3D; 200;
重复考勤 REPEATED &#x3D; 300;
迟到 CLOCK_IN_LATE &#x3D; 301;
早退 CLOCK_OUT_EARLY &#x3D; 302;
不在地点范围 OUT_OF_LOCATION_RANGE &#x3D; 400;
系统异常 FAIL &#x3D; 500;
   */
  code: number
  /**
   * 消息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async clockIn(
    request: StudentClockInRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.clockIn,
    operation?: string
  ): Promise<Response<ClockResponse>> {
    return commonRequestApi<ClockResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async clockOut(
    request: StudentClockOutRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.clockOut,
    operation?: string
  ): Promise<Response<ClockResponse>> {
    return commonRequestApi<ClockResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 一键打卡: 便捷测试使用
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async oneClickClock(
    request: OneClickClockRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.oneClickClock,
    operation?: string
  ): Promise<Response<Map<string, string>>> {
    return commonRequestApi<Map<string, string>>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
