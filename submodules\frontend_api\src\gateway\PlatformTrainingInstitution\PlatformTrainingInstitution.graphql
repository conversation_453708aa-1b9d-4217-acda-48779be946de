schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""子项目管理员培训机构详情"""
	detail(id:String):TrainingInstitutionDetailDto @NotAuthenticationRequired
	"""导出培训机构信息"""
	exportTrainingInstitutionInfo(params:TrainingInstitutionQueryParams):Boolean!
	"""获取所有培训机构详情"""
	findDetailList(idList:[String]):[TrainingInstitutionDetailDto] @NotAuthenticationRequired
	"""获取所有启用的培训机构的名称首字母"""
	findServicerSpellList:[String] @NotAuthenticationRequired
	"""查询培训机构增值服务日志"""
	findTrainingInstitutionAddedServicesLog(servicerId:String):[ServicerAddedServicesLogDto]
	"""获取培训机构信息（通过域名）"""
	findTrainingInstitutionByDomain(domain:String):TrainingInstitutionDetailDto @NotAuthenticationRequired
	"""获取所有培训机构列表"""
	findTrainingInstitutionList(params:TrainingInstitutionQueryParams):[SimpleTrainingInstitutionDto] @NotAuthenticationRequired
	"""获取培训机构数"""
	getTrainingInstitutionCount:Long! @NotAuthenticationRequired
	institutionTrainingPromotionAuthExist(existParam:AuthCVendorPromotionTrainingExistRequest):Boolean!
	"""获取培训机构分页"""
	page(page:Page,params:TrainingInstitutionQueryParams):TrainingInstitutionListDtoPage @page(for:"TrainingInstitutionListDto") @NotAuthenticationRequired
}
type Mutation {
	"""培训机构下对已签约的渠道商授权推广所有班级
		@param authAllParam 授权所有班级参数
	"""
	authCVendorPromotionAllTraining(authAllParam:AuthCVendorPromotionAllTrainingRequest):Void
	"""培训机构下对已签约的渠道商授权推广班级
		@param authParam 授权参数
	"""
	authCVendorPromotionTraining(authParam:AuthCVendorPromotionTrainingRequest):Void
	"""取消培训机构下对已签约的渠道商授权推广班级
		@param cancelAuthParam 取消授权参数
	"""
	cancelAuthCVendorPromotionTraining(cancelAuthParam:CancelAuthCVendorPromotionTrainingRequest):Void
	"""申请登录的随机码
		@return 返回随机码信息
	"""
	syncTrainingInstitution(mztToken:String):SyncTrainingInstitutionResponse @NotAuthenticationRequired
	"""同步培训机构信息"""
	synchronizeTrainingInstitutions:Void
}
input AuthCVendorPromotionAllTrainingRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.AuthCVendorPromotionAllTrainingRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String
	"""渠道商编号"""
	channelVendorId:String
}
input AuthCVendorPromotionTrainingExistRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.AuthCVendorPromotionTrainingExistRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String
	"""培训班id，对应schemeId"""
	trainingId:String
}
input AuthCVendorPromotionTrainingRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.AuthCVendorPromotionTrainingRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String
	"""渠道商编号"""
	channelVendorId:String
	"""培训班编号集合"""
	trainingIdList:[String]
}
input CancelAuthCVendorPromotionTrainingRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.CancelAuthCVendorPromotionTrainingRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String
	"""渠道商编号"""
	channelVendorId:String
	"""培训班编号集合"""
	trainingIdList:[String]
}
"""培训机构查询信息"""
input TrainingInstitutionQueryParams @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.servicer.trainingInstitution.TrainingInstitutionQueryParams") {
	"""培训机构名称"""
	name:String
	"""机构代码"""
	code:String
	"""服务商状态
		@see com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerStatusEnums
	"""
	servicerStatus:ServicerStatusEnums
	"""服务商简称或名称首字母"""
	spell:String
	"""增值服务状态"""
	addedServicesStatus:AddedServicesStatusEnum
	"""人气（报名人次）排序"""
	saleTotalNumberSortPolicy:QueryParamSortEnum
	"""好评率排序"""
	averageSortPolicy:QueryParamSortEnum
	"""筛选的机构id集合"""
	trainingInstitutionIds:[String]
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""Author:FangKunSen
	Time:2020-10-27,15:55
"""
enum QueryParamSortEnum @type(value:"com.fjhb.btpx.integrative.service.utils.dto.QueryParamSortEnum") {
	"""升序"""
	ASC
	"""降序"""
	DESC
}
enum PortalTypeEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.PortalTypeEnums") {
	"""WEB端"""
	WEB
	"""移动端"""
	MOBILE
}
enum ServicerResourceTypeEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerResourceTypeEnums") {
	"""平台创建"""
	PLATFORM
	"""合作机构"""
	COOPERATIVE_AGENCY
}
enum ServicerStatusEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerStatusEnums") {
	"""全部"""
	ALL
	"""正常"""
	NORMAL
	"""失效"""
	SUSPEND
}
"""服务商增值服务状态枚举
	<AUTHOR>
	@date 2021-12-2
"""
enum AddedServicesStatusEnum @type(value:"com.fjhb.btpx.platform.enums.AddedServicesStatusEnum") {
	OPEN
	CLOSE
}
"""同步培训机构结果
	<AUTHOR> create 2021/10/21 14:12
"""
type SyncTrainingInstitutionResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.SyncTrainingInstitutionResponse") {
	"""培训机构id"""
	trainingInstitutionId:String
	"""培训机构状态,默认是正常  1：正常 2：停用"""
	trainingInstitutionStatus:Int!
	"""单位id"""
	unitId:String
	"""企业主账号id"""
	rootAccountId:String
}
type ServicerAddedServicesLogDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.common.ServicerAddedServicesLogDto") {
	"""增值服务开启状态"""
	addedServicesStatus:AddedServicesStatusEnum
	"""操作时间"""
	operationTime:DateTime
	"""操作人"""
	operationUserName:String
}
type SimpleTrainingInstitutionDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.trainingInstitution.SimpleTrainingInstitutionDto") {
	"""机构id"""
	servicerId:String
	"""机构名称"""
	servicerName:String
	"""机构简称"""
	abouts:String
	"""统一信用社会代码"""
	code:String
}
"""培训机构详情信息"""
type TrainingInstitutionDetailDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.trainingInstitution.TrainingInstitutionDetailDto") {
	"""Id"""
	id:String
	"""机构名称"""
	name:String
	"""机构代码"""
	code:String
	"""机构简介"""
	abouts:String
	"""机构logo"""
	logo:String
	"""联系人"""
	contactPerson:String
	"""联系电话"""
	phone:String
	"""创建时间"""
	createdTime:DateTime
	"""培训机构门户信息"""
	portals:[PortalDto]
	"""电子公章文件路径"""
	electronicSealPath:String
	"""好评率"""
	average:Double
	"""学员数(销售总数量，不考虑退款)"""
	saleTotalNumber:Long
	"""课程数"""
	commodityCount:Long
}
"""培训机构列表信息"""
type TrainingInstitutionListDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.trainingInstitution.TrainingInstitutionListDto") {
	"""Id"""
	id:String
	"""机构logo"""
	logo:String
	"""培训机构来源
		@see com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerResourceTypeEnums
	"""
	resourceType:ServicerResourceTypeEnums
	"""机构名称"""
	name:String
	"""服务商简介"""
	abouts:String
	"""WEB域名"""
	webDomain:String
	"""MOBILE域名"""
	mobileDomain:String
	"""地区名称列表，省市区县..."""
	regionNames:[String]
	"""所在地区路径"""
	regionPath:String
	"""机构代码"""
	code:String
	"""联系人"""
	contactPerson:String
	"""联系电话"""
	phone:String
	"""创建时间"""
	createdTime:DateTime
	"""服务商状态
		@see com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerStatusEnums
	"""
	servicerStatus:ServicerStatusEnums
	"""好评率"""
	average:Double
	"""学员数(销售总数量，不考虑退款)"""
	saleTotalNumber:Long
	"""课程数"""
	commodityCount:Long
	"""当前是否开通增值服务"""
	addedServicesStatusEnum:AddedServicesStatusEnum
	"""增值服务开通时间"""
	openAddedServiceTime:DateTime
}
"""培训机构轮播图信息"""
type BannerDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.trainingInstitution.portal.BannerDto") {
	"""轮播图编号"""
	id:String
	"""链接地址"""
	link:String
	"""图片路径"""
	path:String
	"""轮播图排序"""
	sort:Int!
	"""创建时间"""
	createTime:DateTime
}
"""培训机构门户信息"""
type PortalDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.trainingInstitution.portal.PortalDto") {
	"""门户编号"""
	id:String
	"""门户类型
		@see com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.PortalTypeEnums
	"""
	portalType:PortalTypeEnums
	"""域名"""
	domainName:String
	"""宣传口号"""
	slogan:String
	"""门户简介说明内容"""
	content:String
	"""轮播图列表"""
	banners:[BannerDto]
	"""创建时间"""
	createdTime:DateTime
}

scalar List
type TrainingInstitutionListDtoPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TrainingInstitutionListDto]}
