/**
 * 主订单状态
 * 1 - 等待付款
 * 2 - 等待卖家确认款项
 * 3 - 支付成功
 * 4 - 发货中
 * 5 - 发货已完成
 * 6 - 交易成功
 * 7 - 交易关闭
 * 8 - 支付中
 * 9 - 发货部分失败
 * 10 - 发货失败
 */
import AbstractEnum from '../AbstractEnum'

enum OrderStatusEnum {
  // 待付款
  wait_pay = 1,
  // 等待卖家确认价格
  wait_seller_confirm_price = 2,
  // 支付中
  paying = 8,
  // 支付成功
  pay_success = 3,
  // 发货中
  delivering = 4,
  // 部分发货失败
  part_deliver_fail = 9,
  // 发货失败
  deliver_fail = 10,
  // 发货已完成
  deliver_complete = 5,
  // 交易成功
  trade_success = 6,
  // 交易关闭
  trade_close = 7
}

export { OrderStatusEnum }

class OrderStatus extends AbstractEnum<OrderStatusEnum> {
  static enum = OrderStatusEnum

  constructor(status?: OrderStatusEnum) {
    super()
    this.current = status
    this.map[OrderStatusEnum.wait_pay] = '待付款'
    this.map[OrderStatusEnum.wait_seller_confirm_price] = '等待卖家确认价格'
    this.map[OrderStatusEnum.paying] = '支付中'
    this.map[OrderStatusEnum.pay_success] = '支付成功'
    this.map[OrderStatusEnum.delivering] = '发货中'
    this.map[OrderStatusEnum.part_deliver_fail] = '部分发货失败'
    this.map[OrderStatusEnum.deliver_fail] = '发货失败'
    this.map[OrderStatusEnum.deliver_complete] = '发货已完成'
    this.map[OrderStatusEnum.trade_success] = '交易成功'
    this.map[OrderStatusEnum.trade_close] = '交易关闭'
  }

  toString(): string {
    switch (this.current) {
      case 1:
        return '等待付款'
      case 2:
        return '支付中'
      case 3:
      case 4:
      case 5:
        return '开通中'
      case 8:
        return '支付中'
      case 6:
        return '交易成功'
      case 7:
        return '交易关闭'
      case 9:
        return '发货失败'
      case 10:
        return '发货失败'
      default:
        return '未知'
    }
  }
}

export default OrderStatus
