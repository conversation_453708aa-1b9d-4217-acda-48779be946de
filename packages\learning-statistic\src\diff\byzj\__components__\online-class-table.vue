<template>
  <OnlineClassTable v-bind="$attrs" v-on="$listeners">
    <template #sync-status="{ scope, isNeedDataSync, schoolConfigFlag }">
      <template v-if="isNeedDataSync(scope.row.trainClassBaseInfo.needDataSync)">
        <slot name="syncStatus" :scope="scope" :schoolConfigFlag="schoolConfigFlag">
          <div v-if="scope.row.syncStatus == 1">
            <el-badge is-dot type="success" class="badge-status">已同步</el-badge>
          </div>
          <div v-else-if="scope.row.syncStatus == 2">
            <el-badge is-dot type="danger" class="badge-status"
              >同步失败<template v-if="scope.row.syncMessage">({{ scope.row.syncMessage }})</template></el-badge
            >
          </div>
          <div v-else-if="scope.row.syncStatus == 3">
            <el-badge is-dot type="danger" class="badge-status">待同步</el-badge>
          </div>
          <div v-else-if="scope.row.syncStatus == -1">
            <el-badge is-dot type="danger" class="badge-status">不同步</el-badge>
          </div>
          <div v-else>
            <el-badge is-dot type="danger" class="badge-status">{{ schoolConfigFlag ? '未同步' : '不同步' }}</el-badge>
          </div>
        </slot>
      </template>
      <template v-else>
        <el-badge is-dot type="danger" class="badge-status">不同步</el-badge>
      </template>
    </template>
  </OnlineClassTable>
</template>

<script lang="ts">
  import OnlineClassTable from '@hbfe/jxjy-admin-learningStatistic/src/__components__/online-class-table.vue'
  import { Component, Ref, Vue } from 'vue-property-decorator'

  @Component({
    components: {
      OnlineClassTable
    }
  })
  export default class extends Vue {}
</script>
<style></style>
