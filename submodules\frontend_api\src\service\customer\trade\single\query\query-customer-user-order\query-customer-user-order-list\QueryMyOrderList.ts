import { cloneDeep } from 'lodash'
import QueryOrderListParams from '@api/service/customer/trade/single/query/query-customer-user-order/query-customer-user-order-list/vo/QueryOrderListParams'
import { ResponseStatus } from '@api/Response'
import tradeQueryGateway from '@api/gateway/PlatformTrade'
import OrderDto from './vo/OrderDto'
import { Page } from '@hbfe/common'
export { OrderDto }
/**
 * 查询学员订单列表类
 */
class QueryMyOrderList {
  // 分页
  page: Page = new Page()
  // 请求列表入参
  queryOrderListParams: QueryOrderListParams = new QueryOrderListParams()
  // 订单列表
  _orderListDto: Array<OrderDto> = []
  // 订单总条数
  private _orderListTotalSize = 0

  /**
   * @description: 获取订单列表
   * @param {*}
   * @return {*}
   */
  get orderListDto() {
    return cloneDeep(this._orderListDto)
  }

  /**
   * @description: 获取订单总条数
   * @param {*}
   * @return {*}
   */
  get orderListTotalSize() {
    return this._orderListTotalSize
  }

  /**
   * @description: 分页查询订单列表方法【增长模式】，适用于小程序或无线滚动场景
   */
  async queryOrderListIncrement(): Promise<ResponseStatus> {
    return await this.pageMyOrder((data: OrderDto[]) => {
      if (this.page.pageNo === 1) {
        this._orderListDto = data
      } else {
        this._orderListDto = this._orderListDto.concat(data)
      }
    })
  }

  /**
   * @description: 分页查询订单列表方法【分页模式】，适用于WEB分页
   */
  async queryOrderListPage(): Promise<ResponseStatus> {
    return await this.pageMyOrder((data: OrderDto[]) => {
      this._orderListDto = data
    })
  }

  /**
   * @description: 私有方法，请求我的订单列表
   * @param {function} callback 对数据的操作回调函数
   * @return {*}
   */
  private async pageMyOrder(callback: (data: OrderDto[]) => void): Promise<ResponseStatus> {
    const { status, data } = await tradeQueryGateway.pageMyOrder({
      page: this.page,
      param: this.queryOrderListParams.toMyOrderQueryDTO()
    })
    if (!status.isSuccess()) {
      return status
    }
    this._orderListTotalSize = data.totalSize
    const myOrderList = data.currentPageData.map(gqlOrderDto => {
      return OrderDto.toOrderDto(gqlOrderDto)
    })
    callback(myOrderList)
    return status
  }
}

export default QueryMyOrderList
