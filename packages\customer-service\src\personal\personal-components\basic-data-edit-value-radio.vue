<!--
 * @Author: WRP
 * @Date: 单个属性修改输入框
-->
<template>
  <span>
    <el-tooltip class="item" effect="dark" placement="top">
      <span class="el-icon-edit-outline f-c9 edit-icon" @click="isShowEditBtn"></span>
      <div slot="content">编辑</div>
    </el-tooltip>

    <div class="edit-box radiobox" v-if="isShowEditbox">
      <el-radio-group v-model="editInputValue">
        <el-radio :label="1">男</el-radio>
        <el-radio :label="0">女</el-radio>
      </el-radio-group>
      <div class="op">
        <el-tooltip class="item" effect="dark" placement="top">
          <span class="el-icon-circle-check f-cb edit-icon" @click="saveEdit"></span>
          <div slot="content">保存</div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" placement="top">
          <span class="el-icon-circle-close f-c9 edit-icon" @click="cancelEdit"></span>
          <div slot="content">取消</div>
        </el-tooltip>
      </div>
    </div>
  </span>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    // 修改的值
    editInputValue: number = null
    // 输入框显隐
    isShowEditbox = false
    // 修改后的值
    newValue: number = null

    @Prop({
      type: Number,
      default: null
    })
    value: number

    @Watch('value', {
      deep: true
    })
    valChange(val: number) {
      this.editInputValue = val
    }

    // 修改的值
    @Watch('editInputValue', {
      deep: true
    })
    inputValueChange(val: number) {
      if (val != null) {
        this.newValue = val
      }
    }

    isShowEditBtn() {
      this.isShowEditbox = true
      this.editInputValue = this.value ?? null
    }

    // 确认修改属性值
    saveEdit() {
      this.$emit('input', this.newValue)
      this.$emit('saveEdit', true)
      this.isShowEditbox = false
    }

    // 放弃修改属性值
    cancelEdit() {
      this.newValue = null
      this.editInputValue = this.value ?? null
      this.isShowEditbox = false
    }
  }
</script>
<style scoped>
  ::v-deep .radiobox {
    top: 0 !important;
  }
</style>
