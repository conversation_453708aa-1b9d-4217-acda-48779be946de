import CheckAccountParam from '@api/service/management/trade/single/checkAccount/query/vo/CheckAccountParam'
import ExportGateway, {
  OrderSortRequest,
  ReturnSortRequest
} from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import fxnlQuery from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import MutationCheckAccountBase from '@api/service/management/trade/single/checkAccount/mutation/MutationCheckAccountBase'

export default class MutationCheckAccountInTrainingChannel extends MutationCheckAccountBase {
  /**
   * 个人报名对账导出
   */
  async listExport(checkAccountParam: CheckAccountParam, sortRequest?: Array<OrderSortRequest>): Promise<boolean> {
    const request = CheckAccountParam.to(checkAccountParam)
    const { data } = await ExportGateway.exportReconciliationExcelInTrainingChannel({ request, sort: sortRequest })
    return data
  }

  /**
   * 个人退款对账导出
   */
  async listReturnExport(
    checkAccountParam: CheckAccountParam,
    sortRequest?: Array<ReturnSortRequest>
  ): Promise<boolean> {
    const request = CheckAccountParam.toReturn(checkAccountParam)
    const { data } = await ExportGateway.exportReturnReconciliationExcelInTrainingChannel({
      request,
      sort: sortRequest
    })
    return data
  }
}
