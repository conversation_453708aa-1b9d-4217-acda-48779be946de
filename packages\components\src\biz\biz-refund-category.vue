<route-meta>
{
"title": "退货/款类别"
}
</route-meta>
<template>
  <el-select v-model="selected" :placeholder="placeholder" @clear="handleClear" class="el-input" filterable clearable>
    <el-option-group v-for="group in options" :key="group.label" :label="group.label">
      <el-option v-for="item in group.arr" :label="item.label" :value="item.value" :key="item.value"></el-option>
    </el-option-group>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import OrderRefundOption from '@api/service/common/return-order/models/OrderRefundOption'
  import OrderRefundType from '@api/service/common/return-order/enums/OrderRefundType'
  import { SubOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import OrderDetailVo from '@api/service/management/trade/single/order/query/vo/UserOrderDetailVo'

  @Component
  export default class extends Vue {
    selected: string | number | undefined = ''

    options = [] as OrderRefundOption[] //返回结果

    orderDetail = new OrderDetailVo() // 订单详情

    @Prop({
      type: String,
      default: '请选择退货/款类型'
    })
    placeholder: string

    @Prop({
      type: Boolean,
      default: true
    })
    isGetAllOptions!: boolean //判断是否要获取下拉的全部信息   在发起弹框时交互特意添加 “退货/款物品” 全部取消选中时，无下拉选项

    @Prop({
      type: Boolean,
      default: true
    })
    isNeedRefundMoney: boolean //判断是否是0元单

    @Prop({
      type: Boolean,
      default: true
    })
    isShowAll: boolean // 是否显示全部选项 弹窗定制化
    @Prop({
      type: Boolean,
      default: false
    })
    isDiff: boolean // 是否是差异化

    @Prop({
      type: Number
    })
    value: number // 传入的值

    @Watch('value')
    valueChange() {
      this.selected = this.value // 监听传入的值变化
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }
    // 提取公共过滤逻辑
    filterOptionsByNeedRefund(options: any) {
      if (!Array.isArray(options)) {
        console.warn('传入的选项不是数组:', options)
        return []
      }
      options = options.filter((item: any) => item.arr.length !== 0)

      return this.isNeedRefundMoney ? options : options.filter((item: any) => item.label === '不含退款')
    }
    /**
     * 获取所有选项
     */
    async getAllEnumMemberUiOptions() {
      try {
        let allOptions
        if (!this.isDiff) {
          // 获取所有选项
          allOptions = await OrderRefundType.getAllEnumMemberUiOptions()
        } else {
          allOptions = await OrderRefundType.getAllEnumMemberUiOptionsWithDiff()
        }
        // 根据 isNeedRefundMoney 决定是否过滤选项
        // 使用公共过滤逻辑
        this.options = this.filterOptionsByNeedRefund(allOptions)
      } catch (error) {
        console.error('获取选项时出错:', error)
      }
    }
    /**
     * 获取子单可退款类型
     */
    async subOrderCanRefundType(item: SubOrderResponse, channelType?: number) {
      const allOptions = await this.orderDetail.subOrderCanRefundType(item, channelType)
      // 根据 isNeedRefundMoney 决定是否过滤选项
      this.options = this.filterOptionsByNeedRefund(allOptions)
    }
    async mounted() {
      if (this.isGetAllOptions) {
        await this.getAllEnumMemberUiOptions()
      } else {
        this.options = [] as OrderRefundOption[]
      }
    }

    handleClear() {
      this.selected = undefined // 清除时将 selected 设置为 undefined
    }
  }
</script>
