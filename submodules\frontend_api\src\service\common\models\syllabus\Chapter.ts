/**
 * 章节
 * @description 考纲中章节信息
 */
class Chapter {
  /**
   * 章节编号，对应标签编号
   */
  id: string
  /**
   * 章节名称，对应标签名称
   */
  name: string
  /**
   * 当前节点关系编号
   * @description 由于同一个标签可能存在在同一个关系中的不同节点位置,
   * 只有节点关系编号才能定位一个章节的位置
   *
   */
  relationId: string
  /**
   * 同一级序号
   */
  sequence: number
  /**
   * 当前章节code
   */
  code: string
  /**
   * 父节点编号
   */
  parentRelationId: string
}

export default Chapter
