import MsExamQuestionGateway, { ImportQuestionsRequest } from '@api/ms-gateway/ms-examquestion-v1'
import { ResponseStatus } from '@hbfe/common'
import MsDataExportBackstageGateway from '@api/platform-gateway/diff-ms-data-export-front-gateway-DataExportBackstage'
import QuestionOutputVo from './vo/QuestionOutputVo'

class MutationQuestionAsynTask {
  /*
    批量导入试题
  */
  async doImportChooseQuestions(filePath: string, fileName: string): Promise<ResponseStatus> {
    const params = new ImportQuestionsRequest()
    params.filePath = filePath
    params.fileName = fileName
    const { status } = await MsExamQuestionGateway.batchImportChooseQuestions(params)
    return status
  }

  /*
    导出试题 【试题管理】
  */
  async doOutputQuestions(request: QuestionOutputVo): Promise<boolean> {
    const { data, status } = await MsDataExportBackstageGateway.exportQuestionExcelInServicer(request.toDto())
    if (!status.isSuccess()) {
      console.error('导出试题请求失败！')
      return null
    }
    return data
  }
}

export default MutationQuestionAsynTask
