import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-offlineinvoice-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-offlineinvoice-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 创建增值税专用票电子票请求命令
<AUTHOR> By lincong
@date 2023/08/01 15:35
 */
export class CreateOfflineSpecialPaperElectronicRequestCommand {
  /**
   * 增值税专用票(电子票)发票编号【必传】
   */
  offlineInvoiceId?: string
  /**
   * 关联实体类型
@see AssociationTypeEnum
   */
  associationType: number
  /**
   * 关联实体编号
   */
  associationId?: string
  /**
   * 发票种类
   */
  invoiceCategory: number
  /**
   * 发票抬头类型
   */
  titleType: number
  /**
   * 发票抬头
   */
  title?: string
  /**
   * 纳税人识别号(统一社会信用代码)
   */
  taxPayerNo?: string
  /**
   * 购买方地址(注册地址)
   */
  address?: string
  /**
   * 购买方电话(注册电话)
   */
  phone?: string
  /**
   * 购买方银行账户(开户银行)
   */
  account?: string
  /**
   * 购买方开户名称(银行账户)
   */
  bankName?: string
  /**
   * 营业执照
   */
  businessLicensePath?: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath?: string
  /**
   * 手机号码、联系电话
   */
  contactPhone?: string
  /**
   * 联系电子邮箱
   */
  contactEmail?: string
  /**
   * 发票总金额
   */
  amount?: number
  /**
   * 创建时间
   */
  createDate?: string
  /**
   * 发票备注
   */
  remark?: string
  /**
   * 平台编号
   */
  platformId?: string
  /**
   * 平台版本编号
   */
  platformVersionId?: string
  /**
   * 项目编号
   */
  projectId?: string
  /**
   * 子项目编号
   */
  subProjectId?: string
  /**
   * 服务商编号
   */
  servicerId?: string
  /**
   * 单位编号
   */
  unitId?: string
  /**
   * 发票信息校验策略
@see InvoiceVerifyStrategy
   */
  invoiceVerifyStrategy?: number
}

export class DeliveryAddress {
  /**
   * 收件人
   */
  consignee?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 所在物理地区
   */
  region?: string
  /**
   * 地址
   */
  address?: string
}

export class TakePoint {
  /**
   * 领取地点
   */
  pickupLocation?: string
  /**
   * 领取时间
   */
  pickupTime?: string
  /**
   * 备注
   */
  remark?: string
}

export class ConfirmDeliveryRequest {
  /**
   * 线下发票编号
   */
  offlineInvoiceId?: string
  /**
   * 快递公司名称
   */
  expressCompanyName?: string
  /**
   * 快递单号
   */
  expressNo?: string
}

export class ConfirmPickupRequest {
  /**
   * 线下发票编号
   */
  offlineInvoiceId?: string
  /**
   * 领取人
   */
  takePerson?: string
  /**
   * 手机号
   */
  phone?: string
}

/**
 * <AUTHOR>
@since 2022/5/6
 */
export class CreateDeliveryChannelShippingMethodsRequest {
  /**
   * 渠道配送方式列表，1为自取，2为快递
   */
  shippingMethodList?: Array<number>
}

export class CreateOfflineInvoiceDeliveryChannelRequest {
  /**
   * 配送方式
@see com.fjhb.domain.trade.api.offlineinvoice.consts.OfflineShippingMethods
   */
  shippingMethod: number
  /**
   * 渠道名称
   */
  channelName?: string
  /**
   * 配送地点
   */
  address?: string
  /**
   * 配送时间
   */
  deliveryDate?: string
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 备注
   */
  remark?: string
}

export class DeleteOfflineInvoiceDeliveryChannelRequest {
  /**
   * 渠道编号
   */
  channelId?: string
}

/**
 * <AUTHOR>
@since 2024/4/25
 */
export class FreezeInvoiceRequest {
  /**
   * 线下发票id
   */
  offlineInvoiceId?: string
  /**
   * 冻结来源编号
   */
  freezeSourceId?: string
  /**
   * 冻结来源类型
   */
  freezeSourceType?: number
}

export class IssueInvoiceRequest {
  /**
   * 线下发票编号
   */
  offlineInvoiceId?: string
  /**
   * 发票编号,格式以英文逗号分隔
   */
  invoiceNo?: string
}

/**
 * 线下发票配送、自取导入请求
<AUTHOR>
@since 2022/5/12
 */
export class OfflineInvoiceDeliveryInfoImportRequest {
  /**
   * 文件路径
   */
  filePath?: string
  /**
   * 导入类型
1. 个人报名专票配送
2. 集体报名专票配送
   */
  importType?: number
}

/**
 * 个人线下电子发票导入请求
<AUTHOR>
@since 2022/5/9
 */
export class OfflineInvoiceImportRequest {
  /**
   * 文件路径
   */
  filePath?: string
  /**
   * 导入类型
1. 个人缴费线下电子票
2. 个人缴费线下专票
3. 批次缴费线下电子票
4. 批次缴费线下纸质票
5.个人缴费增值税专用电子票
   */
  importType?: number
}

/**
 * 查询批量导入任务的执行情况请求
<AUTHOR>
@since 2022/5/19
 */
export class QueryForImportInfoRequest {
  /**
   * 任务名称
   */
  taskName?: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState?: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult?: number
  /**
   * 起始执行时间
   */
  executeStartTime?: string
  /**
   * 结束执行时间
   */
  executeEndTime?: string
  /**
   * 导入任务类型
PERSONAL_OFFLINE_INVOICE_IMPORT(导入个人线下电子发票)
PERSONAL_OFFLINE_SPECIAL_INVOICE_IMPORT(导入个人线下纸质发票)
BATCH_OFFLINE_INVOICE_IMPORT(导入批次缴费线下电子发票)
BATCH_OFFLINE_SPECIAL_INVOICE_IMPORT(导入批次缴费线下纸质发票)
PERSONAL_OFFLINE_INVOICE_DELIVERY_INFO_IMPORT(导入个人报名线下发票配送信息)
BATCH_OFFLINE_INVOICE_DELIVERY_INFO_IMPORT(导入集体报名线下专票配送信息)
PERSONAL_OFFLINE_SPECIAL_ELECTRONIC_INVOICE_IMPORT(导入个人线下增值税专用票电子票)
BATCH_OFFLINE_SPECIAL_ELECTRONIC_INVOICE_IMPORT(导入批次缴费线下增值税专用票电子票)
@see com.fjhb.ms.offline.invoice.v1.kernel.supports.ImportCategorysTypes
   */
  category?: string
}

/**
 * 重置线下发票开票
<AUTHOR>
@since 2022/5/5
 */
export class ResetInvoiceRequest {
  /**
   * 线下发票编号
   */
  offlineInvoiceId?: string
  /**
   * 原因，可为空
   */
  reason?: string
}

/**
 * <AUTHOR>
@since 2022/5/6
 */
export class UpdateDeliveryChannelShippingMethodsRequest {
  /**
   * 渠道配送方式列表，1为自取，2为快递
   */
  shippingMethodList?: Array<number>
}

export class UpdateOfflineInvoiceDeliveryChannelRequest {
  /**
   * 渠道编号
   */
  channelId?: string
  /**
   * 配送方式
   */
  shippingMethod?: number
  /**
   * 渠道名称
   */
  channelName?: string
  /**
   * 配送地点
   */
  address?: string
  /**
   * 配送时间
   */
  deliveryDate?: string
  /**
   * 是否启用
   */
  enable?: boolean
  /**
   * 备注
   */
  remark?: string
}

export class UpdateOfflineInvoiceDeliveryChannelStatusRequest {
  /**
   * 渠道编号
   */
  channelId?: string
  /**
   * 渠道状态（是否开启）
   */
  enable?: boolean
}

export class UpdateOfflineInvoiceRequest {
  /**
   * 线下发票编号
   */
  offlineInvoiceId?: string
  /**
   * 发票总金额,null表示不更新
   */
  amount?: number
  /**
   * 发票种类,null表示不更新
   */
  invoiceCategory?: number
  /**
   * 发票抬头,null表示不更新
   */
  title?: string
  /**
   * 抬头类型,null表示不更新
   */
  titleType?: number
  /**
   * 纳税人识别号,null表示不更新
   */
  taxpayerNo?: string
  /**
   * 购买方地址,null表示不更新
   */
  address?: string
  /**
   * 购买方电话,null表示不更新
   */
  phone?: string
  /**
   * 购买方开户行名称,null表示不更新
   */
  bankName?: string
  /**
   * 购买方银行账户,null表示不更新
   */
  account?: string
  /**
   * 联系电子邮箱,null表示不更新
   */
  contactEmail?: string
  /**
   * 联系电话,null表示不更新
   */
  contactPhone?: string
  /**
   * 发票备注
   */
  remark?: string
}

/**
 * 更新增值税专票电子票请求
<AUTHOR> By lincong
@date 2023/08/02 11:27
 */
export class UpdateOfflineSpecialElectronicInvoiceRequest {
  /**
   * 增值税专用票(电子票)发票编号【必传】
   */
  offlineInvoiceId?: string
  /**
   * 发票抬头类型
   */
  titleType?: number
  /**
   * 发票抬头，null表示不跟新
   */
  title?: string
  /**
   * 纳税人识别号
   */
  taxpayerNo?: string
  /**
   * 购买方银行账户(开户银行)
   */
  account?: string
  /**
   * 购买方开户行名称(银行账户)
   */
  bankName?: string
  /**
   * 购买方电话(注册电话)
   */
  phone?: string
  /**
   * 购买方地址(注册地址)
   */
  address?: string
  /**
   * 营业执照
   */
  businessLicensePath?: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath?: string
  /**
   * 手机号码、联系电话
   */
  contactPhone?: string
  /**
   * 联系电子邮箱
   */
  contactEmail?: string
  /**
   * 发票类型
   */
  invoiceType?: number
  /**
   * 发票种类
   */
  invoiceCategory?: number
  /**
   * 发票总金额
   */
  amount?: number
  /**
   * 发票备注
   */
  remark?: string
  /**
   * 发票信息校验策略
@see InvoiceVerifyStrategy
   */
  invoiceVerifyStrategy?: number
}

export class UpdateOfflineSpecialPaperInvoiceRequest {
  /**
   * 线下发票编号
   */
  offlineInvoiceId?: string
  /**
   * 发票总金额,null表示不修改
   */
  amount?: number
  /**
   * 发票类型,null表示不修改
   */
  invoiceType?: number
  /**
   * 发票种类,null表示不修改
   */
  invoiceCategory?: number
  /**
   * 发票抬头,null表示不修改
   */
  title?: string
  /**
   * 纳税人识别号,null表示不修改
   */
  taxpayerNo?: string
  /**
   * 购买方地址,null表示不修改
   */
  address?: string
  /**
   * 购买方电话,null表示不修改
   */
  phone?: string
  /**
   * 购买方开户行名称,null表示不修改
   */
  bankName?: string
  /**
   * 购买方银行账户,null表示不修改
   */
  account?: string
  /**
   * 联系电子邮箱,null表示不修改
   */
  contactEmail?: string
  /**
   * 营业执照,null表示不修改
   */
  businessLicensePath?: string
  /**
   * 开户许可,null表示不修改
   */
  accountOpeningLicensePath?: string
  /**
   * 配送方式,null表示不修改
   */
  shippingMethod?: number
  /**
   * 配送地址信息,null表示不修改
   */
  deliveryAddress?: DeliveryAddress
  /**
   * 自取点信息,null表示不修改
   */
  takePoint?: TakePoint
  /**
   * 发票备注
   */
  remark?: string
  /**
   * 发票信息校验策略，策略中不校验的字段传null时，会将字段修改为null
@see InvoiceVerifyStrategy
   */
  invoiceVerifyStrategy?: number
}

/**
 * 各状态及执行结果对应数量
<AUTHOR>
@since 2022/5/24
 */
export class EachStateCount {
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  state: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 数量
   */
  count: number
}

/**
 * 线下发票通用返回类
<AUTHOR> By lincong
@date 2023/08/03 10:26
 */
export class OfflineInvoiceCommonResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 描述信息
   */
  message: string
  /**
   * 线下发票id
   */
  offlineInvoiceId: string
  /**
   * 返回数据
   */
  data: string
}

/**
 * 执行情况信息请求响应体
<AUTHOR>
@since 2022/5/19
 */
export class QueryImportInfoResponse {
  /**
   * 任务编号
   */
  id: string
  /**
   * 【必填】平台编号
   */
  platformId: string
  /**
   * 【必填】平台版本编号
   */
  platformVersionId: string
  /**
   * 【必填】项目编号
   */
  projectId: string
  /**
   * 【必填】子项目编号
   */
  subProjectId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务分类
   */
  category: string
  /**
   * 所属批次单编号
   */
  batchNo: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
  /**
   * 处理信息
   */
  message: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 就绪时间
   */
  alreadyTime: string
  /**
   * 执行时间
   */
  executingTime: string
  /**
   * 完成时间
   */
  completedTime: string
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount>
}

/**
 * <AUTHOR>
@since 2022/5/6
 */
export class QueryShippingMethodsResponse {
  /**
   * 信息
   */
  message: string
  /**
   * 配送渠道
   */
  shippingMethods: Array<number>
}

export class QueryImportInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<QueryImportInfoResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async commonQueryOfflineInvoiceImportResult(
    params: { request?: QueryForImportInfoRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.commonQueryOfflineInvoiceImportResult,
    operation?: string
  ): Promise<Response<QueryImportInfoResponsePage>> {
    return commonRequestApi<QueryImportInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据主任务编号获取全部导入数据
   * @param mainTaskId
   * @return 导入的excel访问地址
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAllImportData(
    mainTaskId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAllImportData,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据主任务编号获取失败数据excel表格
   * @param mainTaskId
   * @return 生成的失败数据excel表格
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getImportFailedData(
    mainTaskId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getImportFailedData,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryForImportBatchPayOfflineInvoice(
    params: { request?: QueryForImportInfoRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryForImportBatchPayOfflineInvoice,
    operation?: string
  ): Promise<Response<QueryImportInfoResponsePage>> {
    return commonRequestApi<QueryImportInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryForImportBatchPaySpecialInvoiceDelivery(
    params: { request?: QueryForImportInfoRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryForImportBatchPaySpecialInvoiceDelivery,
    operation?: string
  ): Promise<Response<QueryImportInfoResponsePage>> {
    return commonRequestApi<QueryImportInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryForImportBatchPaySpecialPaperOfflineInvoice(
    params: { request?: QueryForImportInfoRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryForImportBatchPaySpecialPaperOfflineInvoice,
    operation?: string
  ): Promise<Response<QueryImportInfoResponsePage>> {
    return commonRequestApi<QueryImportInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryForImportOfflineInvoice(
    params: { request?: QueryForImportInfoRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryForImportOfflineInvoice,
    operation?: string
  ): Promise<Response<QueryImportInfoResponsePage>> {
    return commonRequestApi<QueryImportInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryForImportOfflineInvoiceWithServiceId(
    params: { request?: QueryForImportInfoRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryForImportOfflineInvoiceWithServiceId,
    operation?: string
  ): Promise<Response<QueryImportInfoResponsePage>> {
    return commonRequestApi<QueryImportInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryForImportOfflineSpecialElectronicInvoice(
    params: { request?: QueryForImportInfoRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryForImportOfflineSpecialElectronicInvoice,
    operation?: string
  ): Promise<Response<QueryImportInfoResponsePage>> {
    return commonRequestApi<QueryImportInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryForImportSpecialInvoiceDelivery(
    params: { request?: QueryForImportInfoRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryForImportSpecialInvoiceDelivery,
    operation?: string
  ): Promise<Response<QueryImportInfoResponsePage>> {
    return commonRequestApi<QueryImportInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryForImportSpecialInvoiceDeliveryWithServiceId(
    params: { request?: QueryForImportInfoRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryForImportSpecialInvoiceDeliveryWithServiceId,
    operation?: string
  ): Promise<Response<QueryImportInfoResponsePage>> {
    return commonRequestApi<QueryImportInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryForImportSpecialPaperOfflineInvoice(
    params: { request?: QueryForImportInfoRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryForImportSpecialPaperOfflineInvoice,
    operation?: string
  ): Promise<Response<QueryImportInfoResponsePage>> {
    return commonRequestApi<QueryImportInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryForImportSpecialPaperOfflineInvoiceWithServiceId(
    params: { request?: QueryForImportInfoRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryForImportSpecialPaperOfflineInvoiceWithServiceId,
    operation?: string
  ): Promise<Response<QueryImportInfoResponsePage>> {
    return commonRequestApi<QueryImportInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询网校下的配送列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryShippingMethodsForSchool(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryShippingMethodsForSchool,
    operation?: string
  ): Promise<Response<QueryShippingMethodsResponse>> {
    return commonRequestApi<QueryShippingMethodsResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建线下发票配送渠道
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createChannel(
    request: CreateOfflineInvoiceDeliveryChannelRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createChannel,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 新建网校下的配送方式列表
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createChannelShippingMethods(
    request: CreateDeliveryChannelShippingMethodsRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createChannelShippingMethods,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建增值税专用发票电子票(自测专用口)
   * @param mutate 查询 graphql 语法文档
   * @param command 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createOfflineSpecialElectronicInvoice(
    command: CreateOfflineSpecialPaperElectronicRequestCommand,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createOfflineSpecialElectronicInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { command },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除线下发票配送渠道
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteChannel(
    request: DeleteOfflineInvoiceDeliveryChannelRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteChannel,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 请求发票配送
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deliveryInvoice(
    request: ConfirmDeliveryRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deliveryInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 冻结发票 补偿接口！
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async freezeInvoice(
    request: FreezeInvoiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.freezeInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导入个人报名线下发票
   * @param importRequest
   * @return  批次导入主任务编号
   * @param mutate 查询 graphql 语法文档
   * @param importRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async importOfflineInvoice(
    importRequest: OfflineInvoiceImportRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.importOfflineInvoice,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { importRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导入线下纸质发票配送、自取信息
   * @param importRequest
   * @return  批次导入主任务编号
   * @param mutate 查询 graphql 语法文档
   * @param importRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async importOfflineInvoiceDeliveryInfo(
    importRequest: OfflineInvoiceDeliveryInfoImportRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.importOfflineInvoiceDeliveryInfo,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { importRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导入线下纸质发票配送、自取信息(创建主任务时设置了服务商id)
   * @param importRequest
   * @return  批次导入主任务编号
   * @param mutate 查询 graphql 语法文档
   * @param importRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async importOfflineInvoiceDeliveryInfoWithServiceId(
    importRequest: OfflineInvoiceDeliveryInfoImportRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.importOfflineInvoiceDeliveryInfoWithServiceId,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { importRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导入个人报名线下发票(创建主任务时设置了服务商id)
   * @param importRequest
   * @return  批次导入主任务编号
   * @param mutate 查询 graphql 语法文档
   * @param importRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async importOfflineInvoiceWithServiceId(
    importRequest: OfflineInvoiceImportRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.importOfflineInvoiceWithServiceId,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { importRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 开票
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async issueOfflineInvoice(
    request: IssueInvoiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.issueOfflineInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 请求发票自提
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pickupInvoice(
    request: ConfirmPickupRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.pickupInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重置开票
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resetInvoice(
    request: ResetInvoiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.resetInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新线下发票配送渠道
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateChannel(
    request: UpdateOfflineInvoiceDeliveryChannelRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateChannel,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改网校下的配送列表
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateChannelShippingMethods(
    request: UpdateDeliveryChannelShippingMethodsRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateChannelShippingMethods,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新线下发票配送渠道状态
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateChannelStatus(
    request: UpdateOfflineInvoiceDeliveryChannelStatusRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateChannelStatus,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新线下电子发票信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateOfflineInvoice(
    request: UpdateOfflineInvoiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateOfflineInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新线下纸质专用发票信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateOfflinePaperInvoice(
    request: UpdateOfflineSpecialPaperInvoiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateOfflinePaperInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新增值税专用发票电子票信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateOfflineSpecialPaperElectronicInvoice(
    request: UpdateOfflineSpecialElectronicInvoiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateOfflineSpecialPaperElectronicInvoice,
    operation?: string
  ): Promise<Response<OfflineInvoiceCommonResponse>> {
    return commonRequestApi<OfflineInvoiceCommonResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
