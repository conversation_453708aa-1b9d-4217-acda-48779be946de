import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformServicer'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformServicer'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum ServicerContractStatusEnums {
  ALL = 'ALL',
  NORMAL = 'NORMAL',
  SUSPEND = 'SUSPEND'
}
export enum ServicerContractTypeEnums {
  TINSTITUTION_CSUPPLIER_CONTRACT = 'TINSTITUTION_CSUPPLIER_CONTRACT',
  TINSTITUTION_CVENDOR_CONTRACT = 'TINSTITUTION_CVENDOR_CONTRACT'
}
export enum ServicerStatusEnums {
  ALL = 'ALL',
  NORMAL = 'NORMAL',
  SUSPEND = 'SUSPEND'
}
export enum ServicerTypeEnums {
  ALL = 'ALL',
  TRAINING_INSTITUTION = 'TRAINING_INSTITUTION',
  COURSEWARE_SUPPLIER = 'COURSEWARE_SUPPLIER',
  CHANNEL_VENDOR = 'CHANNEL_VENDOR',
  PARTICIPATING_UNIT = 'PARTICIPATING_UNIT'
}
export enum SpecialTopicTypeEnums {
  SHE_QU_JIAO_ZHENG = 'SHE_QU_JIAO_ZHENG'
}

// 类

export class QueryChannelVendorOfTInstitutionArgs {
  /**
   * 手机号
   */
  phone?: string
  /**
   * 渠道地区
   */
  regionPath?: string
  /**
   * 名称
   */
  name?: string
  /**
   * 合作签约开始时间
   */
  beginTime?: string
  /**
   * 合作签约结束时间
   */
  endTime?: string
  /**
   * 合作状态
   */
  contractStatus?: ServicerContractStatusEnums
}

export class QueryCoursewareSupplierOfTInstitutionArgs {
  /**
   * 手机号
   */
  phone?: string
  /**
   * 渠道地区
   */
  regionPath?: string
  /**
   * 名称
   */
  name?: string
  /**
   * 合作签约开始时间
   */
  beginTime?: string
  /**
   * 合作签约结束时间
   */
  endTime?: string
  /**
   * 合作状态
   */
  contractStatus?: ServicerContractStatusEnums
}

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 有合约的服务商信息
 */
export class ServicerContractDto {
  /**
   * 服务商 Id
   */
  id: string
  /**
   * 服务商类型
   */
  servicerType: ServicerTypeEnums
  /**
   * 名称
   */
  name: string
  /**
   * 合作状态
   */
  status: ServicerContractStatusEnums
}

/**
 * 服务商信息
 */
export class ServicerDto {
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 服务商 Id
   */
  id: string
  /**
   * 企业账户id
   */
  accountId: string
  /**
   * 企业单位id
   */
  unitId: string
  /**
   * 名称
   */
  name: string
  /**
   * 服务商类型 培训机构：1，课件供应商：2，渠道商：3 ，参训单位：4
{@link ServicerTypes}
   */
  servicerType: number
  /**
   * 地区名称列表，省市区县...
   */
  regionNames: Array<string>
  /**
   * 所在地区路径
   */
  regionPath: string
  /**
   * 联系人
   */
  contactPerson: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 服务商简介
   */
  abouts: string
  /**
   * 服务商状态 正常：1，失效：2
   */
  status: number
}

/**
 * 服务商信息
 */
export class ServicerDtoWithTrainingInstitution {
  /**
   * 有合约的服务商信息
   */
  servicerContracts: Array<ServicerContractDto>
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 服务商 Id
   */
  id: string
  /**
   * 企业账户id
   */
  accountId: string
  /**
   * 企业单位id
   */
  unitId: string
  /**
   * 名称
   */
  name: string
  /**
   * 服务商类型 培训机构：1，课件供应商：2，渠道商：3 ，参训单位：4
{@link ServicerTypes}
   */
  servicerType: number
  /**
   * 地区名称列表，省市区县...
   */
  regionNames: Array<string>
  /**
   * 所在地区路径
   */
  regionPath: string
  /**
   * 联系人
   */
  contactPerson: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 服务商简介
   */
  abouts: string
  /**
   * 服务商状态 正常：1，失效：2
   */
  status: number
}

/**
 * 服务商信息
 */
export class TrainingInstitutionInfoDto {
  /**
   * 机构代码
   */
  code: string
  /**
   * logo
   */
  logo: string
  /**
   * 有合约的服务商信息
   */
  servicerContracts: Array<ServicerContractDto>
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 服务商 Id
   */
  id: string
  /**
   * 企业账户id
   */
  accountId: string
  /**
   * 企业单位id
   */
  unitId: string
  /**
   * 名称
   */
  name: string
  /**
   * 服务商类型 培训机构：1，课件供应商：2，渠道商：3 ，参训单位：4
{@link ServicerTypes}
   */
  servicerType: number
  /**
   * 地区名称列表，省市区县...
   */
  regionNames: Array<string>
  /**
   * 所在地区路径
   */
  regionPath: string
  /**
   * 联系人
   */
  contactPerson: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 服务商简介
   */
  abouts: string
  /**
   * 服务商状态 正常：1，失效：2
   */
  status: number
}

export class ServicerDtoWithTrainingInstitutionPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ServicerDtoWithTrainingInstitution>
}

export class TrainingInstitutionInfoDtoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TrainingInstitutionInfoDto>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 培训机构查询渠道商列表
   * 已废弃 请使用{@link #findChannelVendorPageForTrainingInstitutionNew }
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findChannelVendorPageForTrainingInstitution(
    params: { page?: Page; name?: string },
    query: DocumentNode = GraphqlImporter.findChannelVendorPageForTrainingInstitution,
    operation?: string
  ): Promise<Response<ServicerDtoWithTrainingInstitutionPage>> {
    return commonRequestApi<ServicerDtoWithTrainingInstitutionPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构查询渠道商列表
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findChannelVendorPageForTrainingInstitutionNew(
    params: { page?: Page; request?: QueryChannelVendorOfTInstitutionArgs },
    query: DocumentNode = GraphqlImporter.findChannelVendorPageForTrainingInstitutionNew,
    operation?: string
  ): Promise<Response<ServicerDtoWithTrainingInstitutionPage>> {
    return commonRequestApi<ServicerDtoWithTrainingInstitutionPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 子项目管理员查询课件供应商列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findCoursewareSupplierPage(
    params: { page?: Page; trainingInstitutionIdList?: Array<string>; name?: string; regionPath?: string },
    query: DocumentNode = GraphqlImporter.findCoursewareSupplierPage,
    operation?: string
  ): Promise<Response<ServicerDtoWithTrainingInstitutionPage>> {
    return commonRequestApi<ServicerDtoWithTrainingInstitutionPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构查询课件供应商列表
   * 已废弃,请使用{@link #findCoursewareSupplierPageForTrainingInstitutionNew}
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findCoursewareSupplierPageForTrainingInstitution(
    params: { page?: Page; name?: string; regionPath?: string },
    query: DocumentNode = GraphqlImporter.findCoursewareSupplierPageForTrainingInstitution,
    operation?: string
  ): Promise<Response<ServicerDtoWithTrainingInstitutionPage>> {
    return commonRequestApi<ServicerDtoWithTrainingInstitutionPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构查询课件供应商列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findCoursewareSupplierPageForTrainingInstitutionNew(
    params: { page?: Page; request?: QueryCoursewareSupplierOfTInstitutionArgs },
    query: DocumentNode = GraphqlImporter.findCoursewareSupplierPageForTrainingInstitutionNew,
    operation?: string
  ): Promise<Response<ServicerDtoWithTrainingInstitutionPage>> {
    return commonRequestApi<ServicerDtoWithTrainingInstitutionPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构查询参训单位列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findParticipatingUnitForTrainingInstitution(
    params: { page?: Page; name?: string; code?: string; regionPath?: string; status?: ServicerStatusEnums },
    query: DocumentNode = GraphqlImporter.findParticipatingUnitForTrainingInstitution,
    operation?: string
  ): Promise<Response<ServicerDtoWithTrainingInstitutionPage>> {
    return commonRequestApi<ServicerDtoWithTrainingInstitutionPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 通过服务商类型查询关联的服务商信息列表
   * @param inputIdList 服务商Id列表
   * @param inPutType   传入服务商Id列表对应的服务商类型
   * @param outPutType  需查询关联的服务商类型
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findRelationServiceListByType(
    params: {
      inputIdList?: Array<string>
      inPutType?: ServicerTypeEnums
      outPutType?: ServicerTypeEnums
      contractStatus?: ServicerContractStatusEnums
    },
    query: DocumentNode = GraphqlImporter.findRelationServiceListByType,
    operation?: string
  ): Promise<Response<Array<ServicerDto>>> {
    return commonRequestApi<Array<ServicerDto>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 通过服务商类型所有服务商信息列表
   * @param query 查询 graphql 语法文档
   * @param servicerType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findServicerListByType(
    servicerType: ServicerTypeEnums,
    query: DocumentNode = GraphqlImporter.findServicerListByType,
    operation?: string
  ): Promise<Response<Array<ServicerDto>>> {
    return commonRequestApi<Array<ServicerDto>>(
      SERVER_URL,
      {
        query: query,
        variables: { servicerType },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 子项目管理员查询培训机构列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTrainingInstitutionPage(
    params: { page?: Page; name?: string; code?: string; regionPath?: string; status?: ServicerStatusEnums },
    query: DocumentNode = GraphqlImporter.findTrainingInstitutionPage,
    operation?: string
  ): Promise<Response<TrainingInstitutionInfoDtoPage>> {
    return commonRequestApi<TrainingInstitutionInfoDtoPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 渠道商查询培训机构列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTrainingInstitutionPageForChannelVendor(
    params: { page?: Page; name?: string; code?: string; regionPath?: string; status?: ServicerStatusEnums },
    query: DocumentNode = GraphqlImporter.findTrainingInstitutionPageForChannelVendor,
    operation?: string
  ): Promise<Response<TrainingInstitutionInfoDtoPage>> {
    return commonRequestApi<TrainingInstitutionInfoDtoPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 课件供应商查询培训机构列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTrainingInstitutionPageForCoursewareSupplier(
    params: { page?: Page; name?: string; code?: string; regionPath?: string; status?: ServicerStatusEnums },
    query: DocumentNode = GraphqlImporter.findTrainingInstitutionPageForCoursewareSupplier,
    operation?: string
  ): Promise<Response<TrainingInstitutionInfoDtoPage>> {
    return commonRequestApi<TrainingInstitutionInfoDtoPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 参训单位查询培训机构列表
   * v1.11.1
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTrainingInstitutionPageForParticipatingUnit(
    params: { page?: Page; name?: string; code?: string; regionPath?: string; status?: ServicerStatusEnums },
    query: DocumentNode = GraphqlImporter.findTrainingInstitutionPageForParticipatingUnit,
    operation?: string
  ): Promise<Response<TrainingInstitutionInfoDtoPage>> {
    return commonRequestApi<TrainingInstitutionInfoDtoPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取当前用户可用的服务商
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCurrentUserServicer(
    query: DocumentNode = GraphqlImporter.getCurrentUserServicer,
    operation?: string
  ): Promise<Response<Array<ServicerDto>>> {
    return commonRequestApi<Array<ServicerDto>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 服务商Id列表获取服务商信息
   * @param query 查询 graphql 语法文档
   * @param idList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async list(
    idList: Array<string>,
    query: DocumentNode = GraphqlImporter.list,
    operation?: string
  ): Promise<Response<Array<ServicerDto>>> {
    return commonRequestApi<Array<ServicerDto>>(
      SERVER_URL,
      {
        query: query,
        variables: { idList },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 新增专题配置
   * @param type        专题类型
   * @param commodityId 商品Id
   * @param servicerId  服务商Id
   * @param sort        排序
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createSpecialTopicConfig(
    params: { type?: SpecialTopicTypeEnums; commodityId?: string; servicerId?: string; sort: number },
    mutate: DocumentNode = GraphqlImporter.createSpecialTopicConfig,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构恢复与服务商合作
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resumeServicerContract(
    params: { token?: string; contractId?: string; contractType?: ServicerContractTypeEnums },
    mutate: DocumentNode = GraphqlImporter.resumeServicerContract,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 通过签约类型进行服务商签约
   * @return 合约Id
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async signUpServicerContract(
    params: {
      token?: string
      servicerIdA?: string
      servicerIdB?: string
      contractContent?: string
      contractType?: ServicerContractTypeEnums
    },
    mutate: DocumentNode = GraphqlImporter.signUpServicerContract,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构中止与服务商合作
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async suspendServicerContract(
    params: { token?: string; contractId?: string; contractType?: ServicerContractTypeEnums },
    mutate: DocumentNode = GraphqlImporter.suspendServicerContract,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
