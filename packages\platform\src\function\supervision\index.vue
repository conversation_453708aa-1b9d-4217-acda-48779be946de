<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 08:51:51
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2024-10-23 14:53:36
 * @Description: 培训监管管理
-->
<template>
  <div class="f-p15">
    <el-tabs
      v-model="activeName"
      type="card"
      class="m-tab-card"
      v-if="$hasPermission('trainingSupervision')"
      desc="培训监管管理"
      actions="activated,@BasicData"
    >
      <el-tab-pane label="基础配置" name="basicData">
        <template v-if="$hasPermission('basicDataDraw')" desc="设置基础配置" actions="@BasicDataDraw"></template>
        <template
          v-if="$hasPermission('supervisionBasicTableData')"
          desc="基础配置列表"
          actions="@SupervisionBasicTableData"
        >
        </template>
        <basic-data :base-config-module="baseConfig" @update-info="updateSuccess"></basic-data>
      </el-tab-pane>
      <el-tab-pane label="监管规则" name="supervisionRules">
        <el-card shadow="never" class="m-card">
          <div class="f-mb20 f-flex f-align-center f-justify-between">
            <template
              v-if="$hasPermission('addSupervisionRules')"
              desc="添加监管规则"
              actions="@hbfe/jxjy-admin-platform/src/function/supervision/supervisionRulesDetail.vue"
            >
            </template>
          </div>
          <!-- 监管规则 -->
          <template
            v-if="$hasPermission('supervisionRules')"
            desc="监管规则列表"
            actions="@SupervisionRules,@hbfe/jxjy-admin-platform/src/function/supervision/platform-supervision-edit.vue"
          >
            <supervision-rules
              :supervision-enable="baseConfig.baseConfig.antiEnable"
              @goBasicTab="goBasicTab"
            ></supervision-rules>
          </template>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import SupervisionRules from '@hbfe/jxjy-admin-platform/src/function/supervision/components/supervision-rules.vue'
  import BaseConfig from '@hbfe-biz/biz-anticheat/dist/config/BaseConfig'
  import QueryManager from '@api/service/management/user/query/manager/QueryManager'
  import BasicData from '@hbfe/jxjy-admin-platform/src/function/supervision/components/basic-data.vue'
  import SupervisionBasicTableData from '@hbfe/jxjy-admin-platform/src/function/supervision/components/supervision-basic-table-data.vue'
  import BasicDataDraw from '@hbfe/jxjy-admin-platform/src/function/supervision/components/basic-data-draw.vue'

  @Component({
    components: {
      SupervisionRules,
      BasicData,
      BasicDataDraw,
      SupervisionBasicTableData
    }
  })
  export default class extends Vue {
    activeName = 'basicData'
    setBasicData = false
    // 基础配置信息
    baseConfig = new BaseConfig()
    QueryManager = new QueryManager()
    // 更新人名称
    updateUserName = ''

    // 添加监管规则
    addSupervisionRules() {
      this.$router.push('/basic-data/platform/function/supervision/supervisionRulesDetail')
    }

    updateSuccess() {
      setTimeout(async () => {
        await this.baseConfig.queryDetail()
      }, 1000)
    }

    goBasicTab() {
      this.activeName = 'basicData'
    }

    async activated() {
      await this.baseConfig.queryDetail()
      if (!this.baseConfig.baseConfig.datumConfig.datumConfigId) {
        this.baseConfig.baseConfig.datumConfig.updateCount = undefined
        this.baseConfig.baseConfig.datumConfig.comparePhoto = undefined
        this.baseConfig.baseConfig.datumConfig.liveDetection = true
      }
      if (this.baseConfig.updateUserId) {
        const res = await this.QueryManager.queryAdminInfo(this.baseConfig.updateUserId)
        this.updateUserName = res.userName
      }
    }
  }
</script>
