import RegionTreeVo from './vo/RegionTreeVo'

import Basicdata, { RegionRequest, RegionResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import { listChildRegionInSubProject } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage/graphql-importer'
import AssembleTree from '../../utils/AssembleTree'
import { RewriteGraph } from '../../utils/RewriteGraph'
import { SkuPropertyResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
class QueryPhysicalRegion {
  /**
   * 地区树
   */
  regionTree: Array<RegionTreeVo> = new Array<RegionTreeVo>()
  businessId = 'PLATFORM_BUSINESS_REGION'

  /**
   * 地区信息缓存
   */
  private regionCache = new Map<string, Array<RegionResponse>>()
  /**
   * 地区信息单个缓存
   */
  private regionSingleCache = new Map<string, RegionResponse>()
  /**
   * 查询网校下的物理地区（可能是省、市、区县、省直单位） --- 做查询使用的数据
   * @return {ResponseStatus}
   */
  async queryRegion() {
    if (!this.regionTree) {
      const result: any[] = []
      const req = new RegionRequest()
      req.businessId = this.businessId
      req.code = '350000'
      req.level = 1
      const response = await Basicdata.listRegionInSubProject(req)
      result.push(response.data[0])
      const level2 = await Basicdata.listChildRegionInSubProject({
        businessId: this.businessId,
        code: response.data[0].code
      })
      const code = level2.data.map((item) => {
        result.push(item)
        return item.code
      })
      const rew = new RewriteGraph(Basicdata._commonQuery, listChildRegionInSubProject)
      const param = code.map((item) => {
        return {
          businessId: this.businessId,
          code: item
        }
      })
      const rewResult = await rew.request(param)
      for (const key in rewResult.data) {
        if (Object.prototype.hasOwnProperty.call(rewResult.data, key)) {
          const element = rewResult.data[key]
          result.push(...element)
        }
      }
      result.forEach((item) => {
        item.children = []
      })
      const tree = new AssembleTree<RegionTreeVo>(result, 'code', 'parentCode')
      this.regionTree = tree.assembleTree()
      this.removeEmptyChildren(this.regionTree[0])
      // for (let i = 0; i < this.regionTree.length; i++) {
      //   const element = this.regionTree[i]
      //   if (element.children?.length > 0) {
      //     for (let j = 0; j < element.children.length; j++) {
      //       const elementL2 = element.children[j]
      //       if (elementL2.children[0].name == '省直' || elementL2.children[0].name == '市直') {
      //         // elementL2.children = undefined
      //       }
      //       if (elementL2.children?.length > 0) {
      //         for (let z = 0; z < elementL2.children.length; z++) {
      //           const elementL3 = elementL2.children[z]
      //           elementL3.children = undefined
      //         }
      //       }
      //     }
      //   }
      // }
    }
    return this.regionTree
  }

  private removeEmptyChildren(node: any) {
    if (node.children && node.children.length > 0) {
      // 递归遍历子节点
      for (let i = 0; i < node.children.length; i++) {
        this.removeEmptyChildren(node.children[i])
      }
    } else {
      // 将空子数组置为 undefined
      node.children = undefined
    }
  }

  /**
   * 根据code查下级地区
   */
  async queryLowerLevelRegion(code: string): Promise<Array<RegionResponse>> {
    if (!this.regionCache.get(code)) {
      const { data } = await Basicdata.listChildRegionInSubProject({
        businessId: this.businessId,
        code: code
      })
      this.regionCache.set(code, data)
    }
    return this.regionCache.get(code)
  }
  /**
   * 根据code查地区信息 - 批量
   */
  async querRegionDetil(codes: string[], region?: SkuPropertyResponse[]): Promise<Array<RegionTreeVo>> {
    const noCache: string[] = []
    codes.forEach((code) => {
      if (!this.regionSingleCache.get(code)) {
        noCache.push(code)
      }
    })
    if (noCache.length > 0) {
      const response = await Basicdata.listRegionByCodeInSubProject({
        businessId: this.businessId,
        codeList: noCache
      })
      response.data.forEach((item) => {
        this.regionSingleCache.set(item.code, item)
      })
    }
    const codesArr = codes.map((item) => this.regionSingleCache.get(item))

    return codesArr.map((item) => {
      const temp = RegionTreeVo.fromBusiness(item)
      // 解决集体报名sku地区筛选
      if (region && region.length) {
        const regionList = region.map((sitem) => sitem.skuPropertyValueId)

        temp.enable = item && !regionList.includes(item.code)
      }
      return temp
    })
  }
}

export default new QueryPhysicalRegion()
