<template>
  <el-container>
    <el-aside width="240px">
      <!--侧边栏按钮 展开时显示-->
      <a href="#" class="aside-btn el-icon-s-fold"></a>
      <!--侧边栏按钮 收起时显示-->
      <!--<a href="#" class="aside-btn el-icon-s-unfold"></a>-->
      <div class="logo">
        <span class="logo-txt">通用平台运营管理后台</span>
      </div>
      <div class="user-info">
        <img class="photo" src="./assets/images/default-photo.jpg" alt=" " />
        <p class="name">张某某</p>
        <div class="op-btn f-mt10">
          <el-button type="primary" size="mini" round plain>
            <i class="el-icon-s-tools"></i>
            帐号设置
          </el-button>
        </div>
      </div>
      <el-menu default-active="1" class="aside-nav" unique-opened="true" @open="handleOpen" @close="handleClose">
        <el-menu-item index="0">
          <template slot="title">
            <i class="iconfont icon-weiwangxiao"></i>
            <span>开通网校</span>
          </template>
        </el-menu-item>
        <!--网校管理-->
        <el-menu-item index="1">
          <template slot="title">
            <i class="hb-iconfont icon-setup"></i>
            <span>网校管理</span>
          </template>
        </el-menu-item>
      </el-menu>
      <div class="m-company-info">
        <p>福建华博教育科技股份有限公司</p>
        <a class="a-txt" href="http://beian.miit.gov.cn/" target="_blank">闽ICP备2021002737号</a>
      </div>
    </el-aside>
    <el-container>
      <el-header height="100px" class="f-flex">
        <ul class="header-nav f-flex-sub">
          <li class="nav-item current">
            <i class="iconfont icon-weiwangxiao"></i>
            <span class="txt">网校管理</span>
          </li>
          <li class="current-bg" style="min-width: 124px;"></li>
        </ul>
        <ul class="header-nav">
          <li class="nav-item nav-item-1"><i class="iconfont icon-tuichu"></i>退出</li>
        </ul>
      </el-header>
      <div class="tags">
        <span class="prev"><i class="el-icon el-icon-arrow-left"></i></span>
        <div class="tags-bd">
          <div class="tags-items" style="width: 500%;">
            <el-tag class="current" closable>网校管理</el-tag>
          </div>
        </div>
        <span class="next"><i class="el-icon el-icon-arrow-right"></i></span>
        <el-dropdown :hide-on-click="false">
          <span class="more"><i class="el-icon el-icon-more"></i></span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>黄金糕</el-dropdown-item>
            <el-dropdown-item>狮子头</el-dropdown-item>
            <el-dropdown-item>螺蛳粉</el-dropdown-item>
            <el-dropdown-item disabled>双皮奶</el-dropdown-item>
            <el-dropdown-item divided>蚵仔煎</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <!--面包屑-->
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-button type="text" size="mini" class="return-btn">
          <i class="iconfont icon-lsh-return"></i>
        </el-button>
        <el-breadcrumb-item :to="{ path: '/' }">网校管理</el-breadcrumb-item>
        <el-breadcrumb-item>修改网校</el-breadcrumb-item>
      </el-breadcrumb>
      <el-main>
        <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb10">
          配置提示：<br />
          1.网校可修改内容，修改后信息会同步影响到当前已应用的网校，若网校已生成相应数据的信息则不影响，自修改后同步更新。<br />
          2.修改网校请慎重。
        </el-alert>
        <!--顶部tab标签-->
        <el-tabs v-model="activeName" class="m-tab-top is-sticky">
          <el-tab-pane label="基础信息" name="first">
            <div class="f-p15">
              <el-card shadow="never" class="m-card">
                <div slot="header" class="">
                  <span class="tit-txt">基础信息</span>
                </div>
                <div class="f-p10">
                  <el-row type="flex" justify="center" class="width-limit">
                    <el-col :md="20" :lg="16" :xl="13">
                      <!--右侧输入框及选择器默认长度为100%，中长.form-l，短.form-s-->
                      <el-form ref="form" :model="form" label-width="auto" class="m-form">
                        <el-form-item label="网校平台名称：" required>
                          <el-input
                            v-model="form.name"
                            clearable
                            placeholder="请输入网校平台名称，网校名称会同步显示给学员"
                          />
                        </el-form-item>
                        <el-form-item label="服务地区：" required>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">全国范围</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">选择地区</el-radio>
                          <span class="f-co">注：选择的网校开展培训的地区范围</span>
                          <div class="f-mt10">
                            <el-cascader :options="options2" :props="props" collapse-tags clearable></el-cascader>
                          </div>
                        </el-form-item>
                        <el-form-item label="培训行业：" required>
                          <el-checkbox v-model="checked1" label="人社行业" border class="f-mr10"></el-checkbox>
                          <el-checkbox v-model="checked1" label="建设行业" border class="f-mr10"></el-checkbox>
                          <el-checkbox v-model="checked1" label="职业卫生行业" border class="f-mr10"></el-checkbox>
                          <el-checkbox v-model="checked1" label="工勤行业" border class="f-mr10"></el-checkbox>
                          <el-checkbox v-model="checked1" label="教师行业" border class="f-mr10"></el-checkbox>
                        </el-form-item>
                        <el-form-item label="业务属性：" required>
                          <el-form
                            ref="form"
                            :model="form"
                            label-width="auto"
                            labelPosition="top"
                            class="m-form pb0 bg-gray f-pt5 f-pl15 f-pr15"
                          >
                            <div class="f-co">
                              <i class="el-icon-warning f-f16 f-mr5 f-vm"></i
                              >请设置培训行业对应的业务属性，需要配置公共的业务属性值和行业属性值
                            </div>
                            <el-form-item label="公共业务属性：">
                              <!--竖式表格-->
                              <div class="info-table">
                                <div class="info-row col-merge">
                                  <div class="info-th f-tl">年度</div>
                                  <div class="info-td p0">
                                    <div class="m-radio-border-list">
                                      <el-radio v-model="radio1" label="1" border>2023年</el-radio>
                                      <el-radio v-model="radio1" label="2" border>2022年</el-radio>
                                      <el-radio v-model="radio1" label="3" border>2021年</el-radio>
                                      <el-radio v-model="radio1" label="4" border>2020年</el-radio>
                                      <el-radio v-model="radio1" label="5" border>2019年</el-radio>
                                      <el-radio v-model="radio1" label="6" border>2018年</el-radio>
                                      <el-radio v-model="radio1" label="7" border>2017年</el-radio>
                                      <el-radio v-model="radio1" label="9" border>2016年</el-radio>
                                      <el-radio v-model="radio1" label="10" border>2015年</el-radio>
                                      <el-radio v-model="radio1" label="11" border>2014年</el-radio>
                                      <el-radio v-model="radio1" label="12" border>2013年</el-radio>
                                      <el-radio v-model="radio1" label="13" border>2012年</el-radio>
                                      <el-radio v-model="radio1" label="14" border>2011年</el-radio>
                                      <el-radio v-model="radio1" label="15" border>2010年</el-radio>
                                    </div>
                                  </div>
                                </div>
                                <div class="info-row col-merge">
                                  <div class="info-th f-tl">地区</div>
                                  <div class="info-td p0">
                                    <div class="m-city-btn-list">
                                      <el-button type="primary">北京市<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">天津市<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">河北省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">山西省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary"
                                        >内蒙古自治区<i class="el-icon-arrow-right"></i
                                      ></el-button>
                                      <el-button type="primary">辽宁省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">吉林省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">黑龙江省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">上海市<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">江苏省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">浙江省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">安徽省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-tooltip placement="top" effect="light">
                                        <div slot="content">
                                          <!--级联面板需要默认全部展开，需前端人员开发-->
                                          <el-cascader-panel
                                            :options="options1"
                                            class="m-cascader-noborder"
                                          ></el-cascader-panel>
                                        </div>
                                        <el-button type="primary">福建省<i class="el-icon-arrow-right"></i></el-button>
                                      </el-tooltip>
                                      <el-button type="primary">江西省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">山东省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">河南省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">湖北省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">湖南省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">广东省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary"
                                        >广西壮族自治区<i class="el-icon-arrow-right"></i
                                      ></el-button>
                                      <el-button type="primary">海南省<i class="el-icon-arrow-right"></i></el-button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </el-form-item>
                            <el-form-item label="行业培训属性：" required>
                              <!--竖式表格-->
                              <div class="info-table f-mb15">
                                <div class="info-row col-merge">
                                  <div class="info-th f-tl">行业属性</div>
                                  <div class="info-td">
                                    <el-button type="primary" plain>已选择人社行业下的培训属性</el-button>
                                    <el-button type="primary" plain>已选择建设行业下的人员属性</el-button>
                                  </div>
                                </div>
                              </div>
                            </el-form-item>
                          </el-form>
                        </el-form-item>
                        <el-form-item label="人员行业属性：" required>
                          <el-form
                            ref="form"
                            :model="form"
                            label-width="auto"
                            labelPosition="top"
                            class="m-form f-p15 bg-gray"
                          >
                            <!--竖式表格-->
                            <div class="info-table">
                              <div class="info-row col-merge">
                                <div class="info-th f-tl">行业属性</div>
                                <div class="info-td">
                                  <el-button type="primary" plain>已选择人社行业下的培训属性</el-button>
                                  <el-button type="primary" plain>已选择建设行业下的人员属性</el-button>
                                </div>
                              </div>
                            </div>
                          </el-form>
                        </el-form-item>
                        <el-form-item label="业主单位全称：" required>
                          <el-input clearable placeholder="请输入业主单位全称" class="form-l" />
                          <el-checkbox v-model="checked" class="f-ml10">配置简称</el-checkbox>
                        </el-form-item>
                        <el-form-item label="单位简称：">
                          <el-input clearable placeholder="请在此输入单位简称" class="form-l" />
                        </el-form-item>
                        <el-form-item label="业主负责人：">
                          <el-input
                            v-model="form.name"
                            clearable
                            placeholder="请输入业主单位负责人姓名"
                            class="form-l"
                          />
                        </el-form-item>
                        <el-form-item label="手机号：">
                          <el-input
                            v-model="form.name"
                            clearable
                            placeholder="请输入业主单位负责人手机号"
                            class="form-l"
                          />
                        </el-form-item>
                        <el-form-item label="网校性质：" required>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">正式实施</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">DEMO</el-radio>
                        </el-form-item>
                        <el-form-item label="合同签订情况：">
                          <el-radio v-model="radio1" label="1" border class="f-mr10">已签约</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">未签约</el-radio>
                        </el-form-item>
                        <el-form-item label="合同签定时间：">
                          <el-date-picker v-model="value1" type="date" class="form-l" placeholder="请选择合同签定日期">
                          </el-date-picker>
                        </el-form-item>
                        <el-form-item label="归属市场经办：" required>
                          <el-input clearable placeholder="请输入该网校市场经办负责人姓名" class="form-l" />
                        </el-form-item>
                        <el-form-item label="网校背景说明：">
                          <el-input
                            type="textarea"
                            placeholder="请输入网校背景说明，文本框就可"
                            :rows="6"
                            v-model="textarea"
                            maxlength="30"
                            show-word-limit
                          >
                          </el-input>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-card>
            </div>
          </el-tab-pane>
          <el-tab-pane label="网校配置" name="second">
            <div class="f-p15">详见 0203_网校管理_修改网校_网校配置.vue</div>
          </el-tab-pane>
          <el-tab-pane label="模板配置" name="third">
            <div class="f-p15">详见 0203_网校管理_修改网校_模板配置.vue</div>
          </el-tab-pane>
          <el-tab-pane label="管理员信息" name="fourth">
            <div class="f-p15">详见 0203_网校管理_修改网校_管理员信息.vue</div>
          </el-tab-pane>
        </el-tabs>
        <div class="m-btn-bar f-tc is-sticky-1">
          <el-button>取消</el-button>
          <el-button type="primary">保存</el-button>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        activeNames: ['1'],
        props: { multiple: true },
        radio: 3,
        radio1: '1',
        input: '',
        select: '',
        checked: true,
        checked2: false,
        checked3: false,
        cascader: [
          {
            value: 'fujian',
            label: '福建省',
            children: [
              {
                value: 'fuzhou',
                label: '福州',
                children: [
                  {
                    value: 'gulou',
                    label: '鼓楼区'
                  },
                  {
                    value: 'taijiang',
                    label: '台江区'
                  },
                  {
                    value: 'cangshan',
                    label: '仓山区'
                  },
                  {
                    value: 'mawei',
                    label: '马尾区'
                  },
                  {
                    value: 'jinan',
                    label: '晋安区'
                  },
                  {
                    value: 'changle',
                    label: '长乐区'
                  }
                ]
              },
              {
                value: 'xiamen',
                label: '厦门',
                children: [
                  {
                    value: 'siming',
                    label: '思明区'
                  },
                  {
                    value: 'huli',
                    label: '湖里区'
                  },
                  {
                    value: 'jimei',
                    label: '集美区'
                  },
                  {
                    value: 'haicang',
                    label: '海沧区'
                  },
                  {
                    value: 'tongan',
                    label: '同安区'
                  },
                  {
                    value: 'xiangan',
                    label: '翔安区'
                  }
                ]
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        visible: false,
        fits: ['cover'],
        options1: [
          {
            value: 'fuzhou',
            label: '福州市',
            children: [
              {
                value: 'gulou',
                label: '鼓楼区'
              },
              {
                value: 'taijiang',
                label: '台江区'
              },
              {
                value: 'cangshan',
                label: '仓山区'
              },
              {
                value: 'mawei',
                label: '马尾区'
              },
              {
                value: 'jinan',
                label: '晋安区'
              },
              {
                value: 'changle',
                label: '长乐区'
              }
            ]
          },
          {
            value: 'xiamen',
            label: '厦门市',
            children: [
              {
                value: 'siming',
                label: '思明区'
              },
              {
                value: 'huli',
                label: '湖里区'
              },
              {
                value: 'data',
                label: '集美区'
              }
            ]
          },
          {
            value: 'zhangzhou',
            label: '漳州市'
          },
          {
            value: 'quanzhou',
            label: '泉州市'
          }
        ],
        options2: [
          {
            value: 'fuzhou',
            label: '福建省',
            children: [
              {
                value: 'gulou',
                label: '福州市'
              },
              {
                value: 'taijiang',
                label: '厦门市'
              },
              {
                value: 'cangshan',
                label: '泉州市'
              },
              {
                value: 'mawei',
                label: '漳州市'
              },
              {
                value: 'jinan',
                label: '三明市'
              },
              {
                value: 'changle',
                label: '莆田市'
              },
              {
                value: 'changle',
                label: '南平市'
              },
              {
                value: 'changle',
                label: '龙岩市'
              },
              {
                value: 'changle',
                label: '宁德市'
              }
            ]
          },
          {
            value: 'xiamen',
            label: '江苏省',
            children: [
              {
                value: 'suzhou',
                label: '苏州'
              },
              {
                value: 'wuxi',
                label: '无锡'
              },
              {
                value: 'chanzhou',
                label: '常州'
              },
              {
                value: 'zhenjiang',
                label: '镇江'
              },
              {
                value: 'nanjing',
                label: '南京'
              },
              {
                value: 'nantong',
                label: '南通'
              },
              {
                value: 'yangzhou',
                label: '扬州'
              }
            ]
          },
          {
            value: 'gansu',
            label: '甘肃省'
          },
          {
            value: 'guangdong',
            label: '广东省'
          }
        ]
      }
    },
    methods: {
      onPreview() {
        this.$refs.preview.clickHandler()
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
