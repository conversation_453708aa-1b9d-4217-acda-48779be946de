/**
 * 课后测验
 */
class QuizConfig {
  // region properties

  /**
   * 课后测验id
   */
  id = ''
  /**
   * 课后测验配置id
   */
  configId = ''
  /**
   * 课后测验配置name
   */
  configName = ''
  /**
   *要求进度，-1不限制，类型为number
   */
  minCourseSchedule = 0
  /**
   *要求课后测验合格情况，-1不限制，int，类型为number
   */
  courseQuizPaperStandard = 0
  /**
   *名称，类型为string
   */
  name = ''
  /**
   *出卷模式,0 智能卷，类型为number
   */
  publishPattern = 0
  /**
   *试题总数，类型为number
   */
  questionCount = 0
  /**
   *总分，类型为number
   */
  totalScore = 0
  /**
   *及格分，类型为number
   */
  passScore = 0
  /**
   *是否开放题析，类型为boolean
   */
  openDissects = false
  /**
   *是否限制测验次数，类型为boolean
   */
  limitCourseQuizNum = false
  /**
   *允许测验次数，类型为number
   */
  allowCourseQuizNum = 0
  /**
   *多选题漏选得分模式，0 不得分，类型为number
   */
  multipleMissScorePattern = 0
  /**
   *测试试题数量配置0 固定题数  1 按课程学时数计算
   */
  questionCountConfigureType = 0

  /**
   *每学时测试试题数量配置
   */
  questionCountPerPeriod = 0
  /**
   * 每道测验题答题时长
   */
  timeLengthPerQuestion = 0

  // endregion
  // region methods
  // endregion

  static from(remoteConfig: any): QuizConfig {
    const config = new QuizConfig()
    config.id = remoteConfig.id
    config.minCourseSchedule = remoteConfig.precondition.minCourseSchedule
    config.courseQuizPaperStandard = remoteConfig.precondition.courseQuizPaperStandard
    config.name = remoteConfig.quizConfig.name
    config.publishPattern = remoteConfig.quizConfig.publishPattern
    config.questionCount = remoteConfig.quizConfig.userCoursePaperPublishConfigure.questionCount
    config.totalScore = remoteConfig.quizConfig.userCoursePaperPublishConfigure.totalScore
    config.passScore = remoteConfig.quizConfig.passScore
    config.openDissects = remoteConfig.quizConfig.openDissects
    config.limitCourseQuizNum = remoteConfig.quizConfig.limitCourseQuizNum
    config.allowCourseQuizNum = remoteConfig.quizConfig.allowCourseQuizNum
    config.multipleMissScorePattern = remoteConfig.quizConfig.multipleMissScorePattern
    config.questionCountConfigureType =
      remoteConfig.quizConfig.userCoursePaperPublishConfigure.questionCountConfigureType
    config.questionCountPerPeriod = remoteConfig.quizConfig.userCoursePaperPublishConfigure.questionCountPerPeriod
    config.timeLengthPerQuestion = remoteConfig.quizConfig.userCoursePaperPublishConfigure.timeLengthPerQuestion
    return config
  }
}

export default QuizConfig
