/**
 * @description 期别培训配置
 */
class IssueTrainingConfigConfigure {
  /**
   * 配置项id
   */
  id: string
  /**
   * 开放考勤
   */
  openAttendance: boolean
  /**
   * 考勤要求类型
   */
  attendanceRequireType: number
  /**
   * 最低考勤率
   */
  minAttendanceRate: number
  /**
   * 是否开启报到
   */
  openReport: boolean
  /**
   * 是否开启结业测试
   */
  openCompletionTest: boolean
  /**
   * 是否开启住宿信息采集
   */
  openStayInfoCollect: boolean
  /**
   * 住宿须知
   */
  stayNotice: string
}

export default IssueTrainingConfigConfigure
