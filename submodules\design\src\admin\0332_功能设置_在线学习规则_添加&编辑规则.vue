<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">学习规则</el-breadcrumb-item>
      <el-breadcrumb-item>添加学习规则</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header">
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">基础设置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit f-p20">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form">
              <el-form-item label="适用行业：" required>
                <el-checkbox-group v-model="form.type">
                  <el-checkbox label="建设行业" name="type"></el-checkbox>
                  <el-checkbox label="人社行业" name="type"></el-checkbox>
                  <el-checkbox label="职业卫生行业" name="type"></el-checkbox>
                  <el-checkbox label="教师行业" name="type"></el-checkbox>
                  <el-checkbox label="工勤行业" name="type"></el-checkbox>
                </el-checkbox-group>
                <div class="m-left-divider">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">人社行业</i></el-divider>
                  <el-form ref="form" :model="form" label-width="150px" class="m-form is-mb f-ptb10 f-mb20">
                    <el-form-item label="年度：" required>
                      <el-select v-model="select" clearable multiple placeholder="请选择年度" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="科目类型：" required>
                      <el-select v-model="select" clearable placeholder="请选择科目类型" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="培训专业：" required>
                      <el-select v-model="select" clearable placeholder="请选择培训专业" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">建设行业</i></el-divider>
                  <el-form ref="form" :model="form" label-width="150px" class="m-form is-mb f-ptb10 f-mb20">
                    <el-form-item label="年度：" required>
                      <el-select v-model="select" clearable multiple placeholder="请选择" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="科目类型：" required>
                      <el-select v-model="select" clearable placeholder="请选择" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="培训类别：" required>
                      <el-select v-model="select" clearable multiple placeholder="请选择" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">教师行业</i></el-divider>
                  <el-form ref="form" :model="form" label-width="150px" class="m-form is-mb f-ptb10 f-mb20">
                    <el-form-item label="年度：" required>
                      <el-select v-model="select" clearable multiple placeholder="请选择" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="学段：">
                      <el-select v-model="select" clearable multiple placeholder="请选择" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="学科：">
                      <el-select v-model="select" clearable multiple placeholder="请选择" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">工勤行业</i></el-divider>
                  <el-form ref="form" :model="form" label-width="150px" class="m-form is-mb f-ptb10 f-mb20">
                    <el-form-item label="年度：" required>
                      <el-select v-model="select" clearable multiple placeholder="请选择" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="技术等级：" required>
                      <el-select v-model="select" clearable multiple placeholder="请选择" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">职业卫生行业 </i></el-divider>
                  <el-form ref="form" :model="form" label-width="150px" class="m-form is-mb f-ptb10 f-mb20">
                    <el-form-item label="年度：" required>
                      <el-select v-model="select" clearable multiple placeholder="请选择" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="培训类别：" required>
                      <el-select v-model="select" clearable multiple placeholder="请选择" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="培训对象：" required>
                      <el-select v-model="select" clearable multiple placeholder="请选择" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="岗位类别：" required>
                      <el-select v-model="select" clearable multiple placeholder="请选择" class="form-l">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-form>
                </div>
              </el-form-item>
              <el-form-item label="每天学习时长：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="按课程学习学时"></el-radio>
                  <el-radio label="按课程物理时长"></el-radio>
                </el-radio-group>
                <div class="f-mt15">
                  每天课程学习最多<el-input placeholder="" class="input-num f-mr10 f-ml10" />学时
                  <span class="f-c9 f-ml20">
                    <i class="el-icon-warning f-f16 f-mr5 f-vm"></i>仅可输入小数点后一位，一天学习时长不能超过32学时
                  </span>
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">特殊规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit f-p20">
          <el-col :md="20" :lg="16" :xl="13">
            <div class="m-sub-tit">
              <span class="tit-txt">设置不包含的方案</span>
            </div>
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-ml15 f-mt10 f-mb25">
              <div class="lh20">
                <p>可以设置不需要在线学习规则的培训方案，选择的培训方案不受规则学习时长影响。</p>
                <p>
                  例：设置人社行业全部年度的公需课每天只能学习60分钟。设置方案A为不需要规则，那么学员在学习方案A时，将不受60分钟学习时长影响。
                </p>
              </div>
            </el-alert>
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-ml15">
              <el-form-item label="选择培训方案：" required>
                <el-button type="primary" class="f-mb10">选择培训方案</el-button>
                <!--表格-->
                <el-table :data="tableData" max-height="500px" class="m-table" border>
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="培训方案名称" min-width="220">
                    <template>任务名称任务名称任务名称</template>
                  </el-table-column>
                  <el-table-column label="方案属性" min-width="240">
                    <template>
                      <div>行业：建设行业</div>
                      <div>地区：福建省/福州市/鼓楼区</div>
                      <div>科目类型：科目类型1</div>
                      <div>培训年度：2023年</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="100" align="center" fixed="right">
                    <template>
                      <el-button type="text" size="mini">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button>取消</el-button>
        <el-button type="primary">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        checkList: ['开通年度与方案年度不一致'],
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
