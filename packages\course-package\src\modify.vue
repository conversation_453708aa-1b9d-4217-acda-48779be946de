<route-params content="/:id"></route-params>
<route-meta>
{
"title": "修改课程包"
}
</route-meta>
<template>
  <el-main>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/training/course-package')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/course-package' }">课程包管理</el-breadcrumb-item>
      <el-breadcrumb-item>修改课程包</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit f-p20">
          <el-col :md="14" :lg="13" :xl="10">
            <el-form
              ref="updateForm"
              :model="mutationUpdateCoursePackage.updateCoursePackageVo"
              :rules="checkRules"
              label-width="140px"
              class="m-form"
            >
              <div :class="{ isRed: isInputRed }">
                <el-form-item label="课程包名称：" prop="name">
                  <el-input
                    @focus="isInputRed = false"
                    v-model="mutationUpdateCoursePackage.updateCoursePackageVo.name"
                    placeholder="请输入课程包名称"
                    clearable
                    :style="{ width: '100%' }"
                  >
                  </el-input>
                </el-form-item>
              </div>
              <el-form-item label="展示名称：">
                <el-input
                  v-model="mutationUpdateCoursePackage.updateCoursePackageVo.showName"
                  placeholder="请输入展示名称"
                  clearable
                  :style="{ width: '100%' }"
                >
                </el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-row :gutter="15" class="is-height f-mb15">
        <el-col :md="8">
          <workType v-model="workValue"></workType>
        </el-col>
        <el-col :md="16">
          <course-choose
            ref="chooseCourseComponent"
            :course-param="courseParam"
            :change-data-num="changeDataNum"
            :create-course-package="mutationUpdateCoursePackage.updateCoursePackageVo"
            :is-modify="modifyStatus"
            @addPeriod="addPeriod"
            @reductionPeriod="reductionPeriod"
          ></course-choose>
        </el-col>
      </el-row>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="f-clear">
          <span class="tit-txt f-fl"
            >已选待确认课程（共 {{ chooseCourseLength }} 门 ， {{ chooseCoursePeriod }} 学时）</span
          >
          <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
            <i class="el-icon-info m-tooltip-icon f-c9"></i>
            <div slot="content">
              课程学习学时必须大于0，支持小数点后一位，如需调整课程的展示顺序，可长按具体课程拖拽至想要的位置。
            </div>
          </el-tooltip>
        </div>
        <modify-choose-course
          ref="ModifyChooseCourseRef"
          v-loading="loading"
          :course-param="courseParam"
          :update-course-package.sync="mutationUpdateCoursePackage.updateCoursePackageVo"
          @packageChange="getPackageChange"
          @updatePeriod="updatePeriod"
          @reductionPeriod="reductionPeriod"
        ></modify-choose-course>
      </el-card>

      <div class="m-btn-bar f-mt10 f-tc is-sticky-1">
        <el-button @click="giveUpEdit">放弃编辑</el-button>
        <el-button type="primary" :disabled="uiConfig.clickSave" @click="updateCoursePool">保存</el-button>
        <el-button type="primary" :disabled="uiConfig.clickSave" @click="syncUpdateCoursePool"
          >保存并同步培训方案</el-button
        >
      </div>
      <!--    删除提示-->
      <el-dialog
        title="提示"
        :visible.sync="uiConfig.giveUpEditFlag"
        :lock-scroll="true"
        :append-to-body="true"
        width="30%"
      >
        <span>确定要放弃编辑吗?</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="uiConfig.giveUpEditFlag = false">取 消</el-button>
          <el-button type="primary" @click="giveUpEditConfirm"> 确 定 </el-button>
        </span>
      </el-dialog>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import Page from '@hbfe/jxjy-admin-common/src/models/Page'
  import workType from '@hbfe/jxjy-admin-coursePackage/src/components/courseType.vue'
  import CourseChoose from '@hbfe/jxjy-admin-coursePackage/src/components/course-choose.vue'
  import ModifyChooseCourse from '@hbfe/jxjy-admin-coursePackage/src/components/modify-choose-course.vue'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import MutationUpdateCoursePackage from '@api/service/management/resource/course-package/mutation/MutationUpdateCoursePackage'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import UpdateCoursePackageByPageVo from '@api/service/management/resource/course-package/mutation/vo/UpdateCoursePackageByPageVo'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'

  @Component({
    components: {
      CourseChoose,
      workType,
      ModifyChooseCourse
    }
  })
  export default class extends Vue {
    @Ref('updateForm') updateForm: any
    @Ref('chooseCourseComponent') chooseCourseComponent: any
    @Ref('ModifyChooseCourseRef') ModifyChooseCourseRef: any
    index = 1
    changeDataNum = 1
    modifyStatus = true
    mutationUpdateCoursePackage: MutationUpdateCoursePackage = new MutationUpdateCoursePackage()
    page: Page = new Page()
    // coursePoolOperate = new UpdateCoursePackageVo()
    unitId = ''
    workValue = '' // 选中的工种分类
    courseWareSupId = '' //课件供应商ID
    id = ''
    coursePoolId = ''
    uiConfig = {
      loadDetail: true,
      clickSave: false,
      giveUpEditFlag: false
    }
    chooseCourseLength = 0
    chooseCoursePeriod = 0
    componentsParam = {
      giveUpModel: new GiveUpCommonModel()
    }
    isInputRed = false
    // 表单校验规则
    checkRules = {
      name: [
        { required: true, message: '课程包名称不可为空！', trigger: ['change', 'blur'] },
        {
          validator: async (rule: any, value: any, callback: any) => {
            await this.checkName(rule, value, callback)
          },
          trigger: 'blur'
        }
      ]
    }

    // 数据加载标志位
    loading = false

    async checkName(rule: any, value: any, callback: any) {
      const res: any = await ResourceModule.coursePackageFactory.getCheckPackage(value, this.id)
      if (res?.code !== '200') {
        return callback('课程包名称重复，请修改')
      }
      return callback()
    }
    // 传递给组件的参数
    get courseParam() {
      return this.workValue
    }

    // 重置清空
    async restForm() {
      const el: any = this.$refs.updateForm
      if (el) {
        el.resetFields()
      }
    }
    async initUpdate() {
      await this.restForm()
    }
    /**
     * 更新总学时
     */
    updatePeriod(period: number) {
      this.chooseCoursePeriod = CalculatorObj.add(this.chooseCoursePeriod, period)
    }
    /**
     * 更新总学时以及课程数量（添加课程时使用）
     */
    addPeriod(period: number) {
      this.chooseCoursePeriod = CalculatorObj.add(this.chooseCoursePeriod, period)
      this.chooseCourseLength++
    }
    /**
     * 更新总学时以及课程数量（删除课程时使用）
     */
    reductionPeriod(period: number) {
      this.chooseCoursePeriod = CalculatorObj.subtract(this.chooseCoursePeriod, period)
      this.chooseCourseLength--
    }
    // @Watch('mutationUpdateCoursePackage.updateCoursePackageVo.uiBindCourseList', {
    //   immediate: true,
    //   deep: true
    // })
    // addlistChange(val: any) {
    //   if (val) {
    //     this.chooseCoursePeriod = 0
    //     val.forEach((item: any) => {
    //       console.log(item.period, 'item.period')
    //         this.chooseCoursePeriod = CalculatorObj.add(this.chooseCoursePeriod, item.period)
    //     })
    //     this.chooseCourseLength = val.length
    //   }
    // }
    constructor() {
      super()
      this.mutationUpdateCoursePackage = new MutationUpdateCoursePackage()
      this.mutationUpdateCoursePackage.updateCoursePackageVo = new UpdateCoursePackageByPageVo()
    }

    async activated() {
      try {
        this.loading = true
        this.id = this.$route.params.id
        this.mutationUpdateCoursePackage = await ResourceModule.coursePackageFactory.getUpdateCoursePackage(this.id)
        this.chooseCourseLength = this.mutationUpdateCoursePackage.updateCoursePackageVo.courseCount
        this.chooseCoursePeriod = this.mutationUpdateCoursePackage.updateCoursePackageVo.totalPeriod
        // this.mutationUpdateCoursePackage.updateCoursePackageVo.addedList.sort((a, b) => {
        //   return a.sort - b.sort
        // })
        this.$nextTick(() => {
          this.ModifyChooseCourseRef && this.ModifyChooseCourseRef.dragElement()
        })
      } catch (e) {
        console.log('err', e)
      } finally {
        this.loading = false
      }
    }
    async createCoursePoolCheck() {
      await this.updateForm.validate((valid: boolean) => {
        if (valid) {
          return true
        }
        return false
      })
      // if (!this.mutationUpdateCoursePackage.updateCoursePackageVo.name) {
      //   // this.$message.warning('课程包名称不能为空哦~')
      //   this.$confirm('课程包名称不能为空哦~', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   }).then(() => {
      //     this.isInputRed = true
      //   })
      //   return false
      // }
      // if (!this.mutationUpdateCoursePackage.updateCoursePackageVo.showName) {
      //   this.$message.warning('展示名称不能为空哦~')
      //   return false
      // }
      return true
    }
    async updateCoursePool() {
      this.uiConfig.clickSave = true
      const check = await this.createCoursePoolCheck()
      if (!check) {
        this.uiConfig.clickSave = false
        return
      }
      const res = await this.mutationUpdateCoursePackage.doUpdate()
      if (!res.isSuccess()) {
        // this.$message.error(res.message.toString())
        this.$confirm(res.getMessage() || res.message.toString(), '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.isInputRed = true
        })
      } else {
        this.$message.success('更新成功')
        setTimeout(() => {
          this.$router.push('/training/course-package')
        }, 200)
      }
      this.uiConfig.clickSave = false
    }
    getPackageChange() {
      this.changeDataNum++
    }
    giveUpEdit() {
      this.uiConfig.giveUpEditFlag = true
    }
    giveUpEditConfirm() {
      this.uiConfig.giveUpEditFlag = false
      this.$router.push('/training/course-package')
    }
    async syncUpdateCoursePool() {
      this.uiConfig.clickSave = true
      const check = await this.createCoursePoolCheck()
      if (!check) {
        this.uiConfig.clickSave = false
        return
      }
      const res = await this.mutationUpdateCoursePackage.doUpdate()
      if (!res.isSuccess()) {
        this.$confirm(res.getMessage() || res.message.toString(), '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.isInputRed = true
        })
      } else {
        this.$message.success('更新成功')
        setTimeout(() => {
          this.$router.push('/training/course-package/sync-scheme/' + this.$route.params.id)
        }, 200)
      }
      this.uiConfig.clickSave = false
    }
  }
</script>

<style scoped>
  .isRed ::v-deep .el-input__inner {
    border-color: #f56c6c;
  }
</style>
