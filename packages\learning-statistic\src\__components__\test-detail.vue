<template>
  <div>
    <!-- 查看详情抽屉 -->
    <el-drawer title="测验记录" :visible.sync="isShowDrawer" size="700px" custom-class="m-drawer">
      <template v-if="tableData.length">
        <div class="drawer-bd" v-for="(item, index) in tableData" :key="index">
          <p class="f-fb f-f15 f-mtb10">课程名称：{{ item[0].courseName }}</p>
          <el-table stripe :data="item" class="m-table f-mb30" v-loading="loading">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="测验提交时间" min-width="300">
              <template slot-scope="scope">{{ scope.row.answerPaperTimeInfo.handingTime }}</template>
            </el-table-column>
            <el-table-column label="测验成绩" min-width="180">
              <template slot-scope="scope">{{ scope.row.answerPaperMarkInfo.score }}</template>
            </el-table-column>
          </el-table>
        </div>
      </template>
      <template v-else
        ><div class="el-table__empty-block" style="width: 690px;">
          <span class="el-table__empty-text">暂无数据</span>
        </div></template
      >
      <hb-pagination :page="page" v-bind="page"> </hb-pagination>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import StaticticalReportManagerModule from '@api/service/management/statisticalReport/StaticticalReportManagerModule'
  import { UiPage } from '@hbfe/common'
  import { ExaminationAnswerPaperResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
  import { PracticeAnswerPaperResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
  import QueryPraticeRecordList from '@api/service/management/statisticalReport/query/QueryPraticeRecordList'
  import { CourseQuizAnswerPaper } from '@api/service/management/statisticalReport/query/QueryPraticeRecordList'
  @Component
  export default class extends Vue {
    /**
     * 是否展示
     */
    @Prop({
      required: true,
      type: Boolean,
      default: false
    })
    value: boolean
    @Prop({
      required: true,
      type: String
    })
    qualificationId: string
    @Watch('value', {
      immediate: true,
      deep: true
    })
    valueChange(val: boolean) {
      this.isShowDrawer = cloneDeep(val)
    }
    @Emit('isTextDialog')
    @Watch('isShowDrawer', {
      immediate: true,
      deep: true
    })
    showRoleDialogChange(val: number) {
      return val
    }
    isShowDrawer = false
    loading = false
    page: UiPage
    tableData: Array<PracticeAnswerPaperResponse> = new Array<PracticeAnswerPaperResponse>()
    courseQuizAnswerPaper = new CourseQuizAnswerPaper()
    constructor() {
      super()
      this.page = new UiPage(this.getDetailList, this.getDetailList)
    }
    async created() {
      this.getDetailList()
    }
    async getDetailList() {
      this.loading = true
      const detailStatic = new QueryPraticeRecordList()

      const list = await detailStatic.pagePracticeRecordInSubProject(this.page, this.qualificationId)
      this.tableData = Object.values(
        list.reduce((res, item) => {
          res[item?.courseName] ? res[item.courseName].push(item) : (res[item.courseName] = [item])
          return res
        }, {})
      )
      console.group('开始')
      console.log(this.tableData)
      console.groupEnd()
      this.loading = false
    }
  }
</script>

<style scoped>
  .drawer-bd {
    flex: none;
  }
</style>
