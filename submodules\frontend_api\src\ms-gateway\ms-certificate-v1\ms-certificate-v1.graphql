"""独立部署的微服务,K8S服务名:ms-certificate-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""根据网校id(服务商id)判断该网校证明模版是否需要上传证件照
		@return true为需要 false为不需要
	"""
	determineCertificateTemplateIsNeedPhoto:Boolean @optionalLogin
	"""获取模板信息"""
	findCertificateTemplate(templateId:String):CertificateTemplate1 @optionalLogin
	"""查询指定批次下的任务执行情况
		@param request
		@param page    分页信息
		@return 执行情况
	"""
	findImportPrintTaskExecuteResponsePage(request:ImportPrintTaskQueryRequest,page:Page):ImportTaskQueryResponsePage @page(for:"ImportTaskQueryResponse")
	"""获取证书快照信息"""
	findSnapShotByCertificateId(request:GetSnapShotRequest):String @optionalLogin
	"""查询指定批次下的任务执行情况
		@param request
		@param page    分页信息
		@return 执行情况
	"""
	findTaskExecuteResponsePage(request:AllocateCourseTaskQueryRequest,page:Page):ImportTaskQueryResponsePage @page(for:"ImportTaskQueryResponse")
	"""根据用户id查询用户证件照
		@param userId
		@return 证件照url
	"""
	findUserIdentificationPhotoByUserId(userId:String!):String
	"""查询指定批次下的导出任务执行情况
		@param request
		@param page  分页信息
		@return 执行情况
	"""
	queryExportTaskResponsePage(request:QueryExportTaskRequest,page:Page):ImportTaskQueryResponsePage @page(for:"ImportTaskQueryResponse")
	"""根据用户id校验用户是否完成证件照上传
		@return true为完成 false为未完成
	"""
	verifyPhotoIsUpload(userId:String):Boolean
}
type Mutation {
	"""重发证书事件"""
	anewConsumeCertificateEvent(eventId:String):Void
	"""批量打印证书
		@param request 批量打印证书请求
		@return zip压缩包路径
	"""
	batchPrintCertificates(request:BatchPrintCertificatesRequest):String
	"""调用接口执行无编号证书记录表数据加密操作"""
	certificateNumberRecordUpdateCrypto(request:CertificateCryptoRequest):Void
	"""调用接口执行证书快照表数据加密操作"""
	certificateSnapshotUpdateCrypto(request:CertificateCryptoRequest):Void
	"""调用接口执行证书表数据加密操作"""
	certificateUpdateCrypto(request:CertificateCryptoRequest):Void
	"""配置电子章（覆盖）
		@param request 配置电子章请求
		@return 电子章id
	"""
	configureElectronicSeal(request:ConfigureElectronicSealRequest):String
	"""更新证书二维码
		@param request 请求
		@return {@link String}
	"""
	createCertificateDimensionalCode(request:AntiBogusQRCode):String
	"""更新证书电子印章
		@param request 电子印章
		@return {@link String}
	"""
	createCertificateElectronicSeal(request:ElectronicSeal):String
	"""更新证书模板
		@param request 证书模板
		@return {@link String}
	"""
	createCertificateTemplate(request:CertificateTemplate):String
	"""获取证书快照
		@param studentNo 学号
		@return 获取证书快照json
	"""
	getCertificateSnapShot(studentNo:String):String @optionalLogin
	"""打印证书
		@param request 打印证书请求
		@return 证书path
	"""
	printCertificate(request:PrintCertificateRequest):String
	"""打印证明模板
		@param request 打印证明模板请求
		@return 文件路径
	"""
	printCertificateTemplate(request:PrintCertificateTemplateRequest):String
	rebuildCertificateNo(list:[String]):Void
	"""扫描证书二维码生成证明
		@param request 请求
		@return 证明路径
	"""
	scanQrCodeGenerateCertificate(request:ScanQrCodeGenerateCertificateRequest):String @optionalLogin
	"""用户证件照上传（用于证书打印）
		@param request
		@return
	"""
	uploadPhoto(request:UploadPhotoRequest):Void
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""分页查询入参
	<AUTHOR>
	@date 2022/12/19 16:32
"""
input AllocateCourseTaskQueryRequest @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.AllocateCourseTaskQueryRequest") {
	"""任务名"""
	name:String
	"""执行状态
		@see com.fjhb.batchtask.core.enums.TaskState  0 - 已创建 1 - 已就绪 2 - 执行中 3 - 已完成
	"""
	taskState:Int
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
}
"""防伪二维码
	<AUTHOR>
"""
input AntiBogusQRCode @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.AntiBogusQRCode") {
	"""防伪二维码id"""
	id:String
	"""平台id"""
	platformId:String
	"""平台版本id"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位id"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""防伪二维码url"""
	url:String
	"""预览路径"""
	previewUrl:String
	"""是否提供短链"""
	transShortUrl:Boolean!
	"""创建时间"""
	createdTime:DateTime
	"""创建人id"""
	createUserId:String
	"""更新时间"""
	updateTime:DateTime
}
"""批量打印证书请求
	<AUTHOR>
"""
input BatchPrintCertificatesRequest @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.BatchPrintCertificatesRequest") {
	"""学号集合"""
	studentNos:[String]
	"""文件类型 1-PDF   2-IMAGE
		@see FileTypes
	"""
	fileType:Int!
	"""方案id"""
	schemeId:String
	"""打印方式
		@see PrintTypes
	"""
	printType:Int!
}
input CertificateCryptoRequest @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.CertificateCryptoRequest") {
	pageNo:Int!
	pageSize:Int!
	servicerId:String
	projectId:String
}
"""证书模板
	<AUTHOR>
	@date 2023/08/16
"""
input CertificateTemplate @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.CertificateTemplate") {
	"""模板id"""
	id:String
	"""平台id"""
	platformId:String
	"""平台版本id"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位id"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""模板名称"""
	name:String
	"""模板说明"""
	describe:String
	"""所属行业id"""
	belongsIndustryId:String
	"""使用范围"""
	usableRange:String
	"""适用培训方案形式"""
	suitableSchemeType:String
	"""html模板地址"""
	url:String
	"""预览html模板地址"""
	previewUrl:String
	"""打印快照数据源"""
	printSnapShotDataSource:String
	"""是否应用电子章"""
	provideElectronicSeal:Boolean!
	"""电子章数据源"""
	electronicDataSource:String
	"""是否提供防伪二维码"""
	provideAntiBogusQRCode:Boolean!
	"""防伪二维码id"""
	AntiBogusQRCodeId:String
	"""创建人id"""
	createUserId:String
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""是否可用"""
	available:Boolean!
}
"""配置电子章请求
	<AUTHOR>
"""
input ConfigureElectronicSealRequest @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.ConfigureElectronicSealRequest") {
	"""生成形式
		1-图片
	"""
	generateType:Int!
	"""电子章url"""
	url:String!
	"""电子章落款"""
	sign:String
}
"""电子印章
	<AUTHOR>
	@date 2023/08/16
"""
input ElectronicSeal @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.ElectronicSeal") {
	"""电子章id"""
	id:String
	"""平台id"""
	platformId:String
	"""平台版本id"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位id"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""生成形式"""
	generateType:Int!
	"""电子章url"""
	url:String
	"""电子章落款"""
	sign:String
	"""创建时间"""
	createdTime:DateTime
	"""创建人id"""
	createUserId:String
	"""更新时间"""
	updateTime:DateTime
}
"""通过证书id查询快照请求
	<AUTHOR>
	@date 2023/9/8 11:32
"""
input GetSnapShotRequest @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.GetSnapShotRequest") {
	"""证书id"""
	certificateId:String
}
"""导入学员批量任务查询请求
	<AUTHOR>
	@date 2024/06/18
"""
input ImportPrintTaskQueryRequest @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.ImportPrintTaskQueryRequest") {
	"""任务分类"""
	category:String
	"""任务名"""
	name:String
	"""执行状态
		@see com.fjhb.batchtask.core.enums.TaskState  0 - 已创建 1 - 已就绪 2 - 执行中 3 - 已完成
	"""
	taskState:Int
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
}
"""打印证书请求 - 下载
	<AUTHOR>
"""
input PrintCertificateRequest @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.PrintCertificateRequest") {
	"""学号"""
	studentNo:String
	"""文件类型 1-PDF   2-IMAGE
		@see FileTypes
	"""
	fileType:Int!
	"""打印方式
		@see com.fjhb.domain.learningscheme.api.certificate.consts.PrintTypes
	"""
	printType:Int!
}
"""打印
	<AUTHOR>
"""
input PrintCertificateTemplateRequest @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.PrintCertificateTemplateRequest") {
	"""证明模板id"""
	certificateTemplateId:String
	"""打印文件类型
		@see FileTypes
		1-PDF    2-图片
	"""
	printFileType:Int!
}
"""导出任务查询入参
	<AUTHOR>
	@date 2023/09/04
"""
input QueryExportTaskRequest @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.QueryExportTaskRequest") {
	"""任务分类"""
	category:String
	"""执行状态
		@see com.fjhb.batchtask.core.enums.TaskState  0 - 已创建 1 - 已就绪 2 - 执行中 3 - 已完成
	"""
	taskState:Int
	"""用户id"""
	userId:String
}
"""扫描证书二维码生成证明请求体"""
input ScanQrCodeGenerateCertificateRequest @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.ScanQrCodeGenerateCertificateRequest") {
	"""证书快照id"""
	snapshotId:String!
	"""{@link FileTypes} 文件类型 1:PDF 2:IMAGE"""
	fileType:Int!
}
"""证件照上传请求"""
input UploadPhotoRequest @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.request.UploadPhotoRequest") {
	"""用户id"""
	userId:String!
	"""证件照mfs文件路径"""
	photoUrl:String!
}
"""证书模板
	<AUTHOR>
"""
type CertificateTemplate1 @type(value:"com.fjhb.ms.certificate.v1.kernel.domain.certificatetemplate.model.CertificateTemplate") {
	"""模板id"""
	id:String
	"""平台id"""
	platformId:String
	"""平台版本id"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位id"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""模板名称"""
	name:String
	"""模板说明"""
	describe:String
	"""所属行业id"""
	belongsIndustryId:String
	"""使用范围"""
	usableRange:String
	"""适用培训方案形式"""
	suitableSchemeType:String
	"""html模板地址"""
	url:String
	"""预览html模板路径"""
	previewUrl:String
	"""打印快照数据源"""
	printSnapShotDataSource:String
	"""是否应用电子章"""
	provideElectronicSeal:Boolean!
	"""电子章数据源"""
	electronicDataSource:String
	"""是否提供防伪二维码"""
	provideAntiBogusQRCode:Boolean!
	"""防伪二维码id"""
	AntiBogusQRCodeId:String
	"""创建人id"""
	createUserId:String
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""是否合并"""
	isMerge:String
	"""模板尺寸"""
	size:String
	"""是否可用"""
	available:Boolean!
	"""数据范围"""
	dataRangeList:[DataRange]
	"""数据范围"""
	formatType:String
}
"""任务执行情况
	<AUTHOR>
	@date 2022/12/19 16:41
"""
type ImportTaskQueryResponse @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.response.ImportTaskQueryResponse") {
	"""任务编号"""
	id:String
	"""【必填】平台编号"""
	platformId:String
	"""【必填】平台版本编号"""
	platformVersionId:String
	"""【必填】项目编号"""
	projectId:String
	"""【必填】子项目编号"""
	subProjectId:String
	"""任务名称"""
	name:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""处理信息 (有过执行失败这是报错信息)"""
	message:String
	"""创建时间"""
	ceratedTime:DateTime
	"""处理时间"""
	executingTime:DateTime
	"""操作人id"""
	createUserId:String
	"""结束（完成）时间"""
	completedTime:DateTime
	"""压缩包路径"""
	zipPath:String
	"""各状态及执行结果对应数量集合
		总数：全部数量之和
		成功数：result = 1数量之和
		失败数：result = 2数量之和
	"""
	eachStateCounts:[EachStateCount]
	"""处理总条数"""
	totalCount:Int!
	"""成功条数"""
	successCount:Int!
	"""失败条数"""
	failCount:Int!
	"""导入进度 1-导入成功 2-导入中 3-导入失败 4-部分成功"""
	importProgress:Int!
}
"""各状态及执行结果对应数量"""
type EachStateCount @type(value:"com.fjhb.ms.certificate.v1.kernel.gateway.graphql.response.ImportTaskQueryResponse$EachStateCount") {
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	state:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int
	"""数量"""
	count:Int!
}
"""<AUTHOR>
	@date 2024/7/15
"""
type DataRange @type(value:"com.fjhb.ms.certificate.v1.kernel.support.certificateTemplate.DataRange") {
	"""数据大类名称"""
	name:String
	"""数据key列表"""
	keyList:[String]
}

scalar List
type ImportTaskQueryResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ImportTaskQueryResponse]}
