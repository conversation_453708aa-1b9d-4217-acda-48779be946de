<template>
  <div>
    <template
      v-if="
        lineData.hasConfigCourse ||
        lineData.hasConfigExam ||
        lineData.trainClassDetail.learningTypeModel.learningExperience.isExamine ||
        (isMixedClass && issueInfo.isOpenAttendance) ||
        (isMixedClass && issueInfo.isOpenGraduationTest) ||
        lineData.periodStudy.questionnaire.require
      "
    >
      <!-- 是否配置课程 -->
      <div class="f-flex" v-if="lineData.hasConfigCourse">
        <span>1.</span>
        <div class="f-flex-sub">
          <p>已获得 {{ lineData.course.totalCompletePeriod || 0 }} 学时</p>
          <!-- 选课规则显示班级学习进度 -->
          <p v-if="lineData.basicInfo.schemeType == 1">
            ① 班级学习进度={{ formatNum(lineData.assessResult.courseLearningSchedule, '0.00') }}%
          </p>
        </div>
      </div>
      <!-- 是否配置考试 -->
      <div class="f-flex" v-if="lineData.hasConfigExam">
        <span v-if="lineData.hasConfigExam && lineData.hasConfigCourse">2.</span>
        <span v-else>1.</span>
        <div class="f-flex-sub">
          <template v-if="lineData.assessRequire.isExamAssessed">
            <p>考试成绩={{ lineData.assessResult.examScore }}分</p>
          </template>
          <template v-else>
            <p>考试成绩={{ isNumber(lineData.exam.examHighestScore) }}分</p>
          </template>
        </div>
      </div>
      <!-- 学习心得 -->
      <div class="f-flex" v-if="lineData.trainClassDetail.learningTypeModel.learningExperience.isExamine">
        <span v-show="lineData.hasConfigCourse && lineData.hasConfigExam">3.</span>
        <span
          v-show="
            (!lineData.hasConfigCourse && lineData.hasConfigExam) ||
            (lineData.hasConfigCourse && !lineData.hasConfigExam)
          "
          >2.</span
        >
        <span v-show="!lineData.hasConfigCourse && !lineData.hasConfigExam">1.</span>
        <div class="f-flex-sub">
          <p>
            已参加{{ lineData.assessResult.learningExperienceCount }}个心得，已通过{{
              lineData.assessResult.passLearningExperienceCount
            }}个
          </p>
        </div>
      </div>
      <!-- 期别-考勤率 -->
      <div class="f-flex" v-if="isMixedClass && issueInfo.isOpenAttendance">
        <span
          v-text="
            autoIndex(
              lineData.hasConfigCourse,
              lineData.hasConfigExam,
              lineData.trainClassDetail.learningTypeModel.learningExperience.isExamine,
              isMixedClass && issueInfo.isOpenAttendance
            )
          "
        ></span>
        <div class="f-flex-sub">
          <p>
            考勤率={{ lineData.periodStudy.studentAttendanceRate }}%
            {{ lineData.periodStudy.attendance.qualified ? '已' : '未' }}合格
          </p>
        </div>
      </div>
      <!-- 期别-结业测试 -->
      <div class="f-flex" v-if="isMixedClass && issueInfo.isOpenGraduationTest">
        <span
          v-text="
            autoIndex(
              lineData.hasConfigCourse,
              lineData.hasConfigExam,
              lineData.trainClassDetail.learningTypeModel.learningExperience.isExamine,
              isMixedClass && issueInfo.isOpenAttendance,
              isMixedClass && issueInfo.isOpenGraduationTest
            )
          "
        ></span>
        <div class="f-flex-sub">
          <p>
            <span>结业测试</span>
            <span v-if="lineData.periodStudy.graduationStatus == GraduationStatusEnum.qualified">已合格</span>
            <span v-if="lineData.periodStudy.graduationStatus == GraduationStatusEnum.unqualified">未合格</span>
          </p>
        </div>
      </div>
      <!-- 问卷 -->
      <div
        class="f-flex"
        v-if="lineData.periodStudy.questionnaire.require || lineData.periodStudy.schemeQuestionnaire.require"
      >
        <span
          v-text="
            autoIndex(
              lineData.hasConfigCourse,
              lineData.hasConfigExam,
              lineData.trainClassDetail.learningTypeModel.learningExperience.isExamine,
              isMixedClass && issueInfo.isOpenAttendance,
              isMixedClass && issueInfo.isOpenGraduationTest,
              lineData.periodStudy.questionnaire.require
            )
          "
        ></span>
        <div class="f-flex-sub">
          <!--          <p>调研问卷{{ lineData.periodStudy.questionnaire.qualified ? '已' : '未' }}完成</p>-->
          <p
            v-if="
              lineData.trainClassDetail.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId ==
              TrainingModeEnum.online
            "
          >
            调研问卷{{ lineData.periodStudy.schemeQuestionnaire.qualified ? '已' : '未' }}完成
          </p>
          <p
            v-if="
              lineData.trainClassDetail.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId ==
              TrainingModeEnum.offline
            "
          >
            调研问卷{{ lineData.periodStudy.questionnaire.qualified ? '已' : '未' }}完成
          </p>
          <p
            v-if="
              lineData.trainClassDetail.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId ==
              TrainingModeEnum.mixed
            "
          >
            调研问卷{{
              lineData.periodStudy.questionnaire.qualified && lineData.periodStudy.schemeQuestionnaire.qualified
                ? '已'
                : '未'
            }}完成
          </p>
        </div>
      </div>
    </template>
    <!-- 课程考试都没有配置 -->
    <template
      v-if="
        !lineData.hasConfigCourse &&
        !lineData.hasConfigExam &&
        !lineData.trainClassDetail.learningTypeModel.learningExperience.isExamine &&
        !(isMixedClass && issueInfo.isOpenAttendance) &&
        !(isMixedClass && issueInfo.isOpenGraduationTest) &&
        !lineData.periodStudy.questionnaire.require
      "
    >
      无考核要求
    </template>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import StudentTrainClassDetailVo from '@api/service/management/train-class/query/vo/StudentTrainClassDetailVo'
  import { GraduationStatusEnum } from '@api/service/common/scheme/enum/GraduationStatusEnum'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'

  @Component
  export default class extends Vue {
    /**
     * @description 行数据
     * */
    @Prop({
      type: Object,
      default: () => {
        return new StudentTrainClassDetailVo()
      }
    })
    lineData: StudentTrainClassDetailVo

    /**
     * @description 培训形式枚举
     * */
    TrainingModeEnum = TrainingModeEnum

    /**
     * @description 结业测试状态枚举
     * */
    GraduationStatusEnum = GraduationStatusEnum

    /**
     * @description 根据 true 的数量自动返回序号
     * */
    get autoIndex() {
      return (...bools: boolean[]) => {
        let serialNumber = 0
        bools.map((item) => {
          if (item) ++serialNumber
        })
        return serialNumber + '.'
      }
    }

    get isNumber() {
      return (val: any) => {
        return !isNaN(val) ? val : '-'
      }
    }

    /**
     * @description 是否面授网班
     * */
    get isMixedClass() {
      const trainingModePropertyValueId =
        this.lineData?.basicInfo?.skuValueNameProperty?.trainingMode?.skuPropertyValueId
      if (!trainingModePropertyValueId) return false
      return (
        trainingModePropertyValueId === TrainingModeEnum.mixed ||
        trainingModePropertyValueId === TrainingModeEnum.offline
      )
    }

    /**
     * @description 获取期别信息
     * */
    get issueInfo() {
      return this.lineData?.getIssueConfigById(this.lineData?.periodStudy?.periodId) ?? new IssueConfigDetail()
    }

    // 格式化工具
    formatNum(val: number, format: string) {
      const numeral = require('numeral')
      return numeral(val).format('0.00')
    }
  }
</script>
