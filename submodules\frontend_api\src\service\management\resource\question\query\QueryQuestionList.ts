import { Response, UiPage } from '@hbfe/common'
import MsExamQueryBackStageGateway, {
  BaseQuestionResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import QuestionRequestVo from './vo/QuestionRequestVo'
import QuestionListDetailVo from './vo/QuestionListDetailVo'
import MsCourseLearningBackGateway, {
  CourseRequest,
  CourseResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
class QueryQuestionList {
  // 关联课程id数组
  private relateCourseIdList = new Map<string, string>()

  /**
   * @description: 试题分页列表
   * @param {*}
   */
  async queryQuestionList(
    page: UiPage,
    questionRequestVo: QuestionRequestVo
  ): Promise<Response<Array<QuestionListDetailVo>>> {
    const result = await MsExamQueryBackStageGateway.pageQuestionInServicer({
      page: page,
      request: questionRequestVo.toDto()
    })
    const response = new Response<Array<QuestionListDetailVo>>()
    // 请求失败处理
    if (!result?.status?.isSuccess()) {
      response.status = result.status
      return response
    }
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize
    const resultList: Array<QuestionListDetailVo> = new Array<QuestionListDetailVo>()
    let arrList = new Array<string>()
    result?.data?.currentPageData?.forEach((item: BaseQuestionResponse) => {
      const questionItem = QuestionListDetailVo.from(item)
      arrList.push(...questionItem.relateCourseIds)
      resultList.push(questionItem)
    })

    // 数组去重
    arrList = [...new Set(arrList)]

    // 获取关联课程名称数组
    const relateCourseNameResponse = await this.queryRelateCourseNames(page, arrList)
    if (!relateCourseNameResponse?.status?.isSuccess()) {
      response.status = relateCourseNameResponse.status
      return response
    }
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this
    resultList?.forEach((question) => {
      question.relateCourseIds?.forEach((courseId) => {
        question.relateCourseNames.push(that.relateCourseIdList.get(courseId) || '')
      })
    })

    response.status = result.status
    response.data = resultList
    return response
  }

  /**
   * @description: 返回关联课程名称
   * @param {UiPage} page
   * @param {Array} relateCourseIdList
   * @return {*}
   */
  private async queryRelateCourseNames(
    page: UiPage,
    relateCourseIdList: Array<string>
  ): Promise<Response<Array<string>>> {
    if (relateCourseIdList.length > 0) {
      const params = new CourseRequest()
      params.courseIdList = relateCourseIdList
      // 这里默认页号只能是一
      const pageTemp = new UiPage()
      pageTemp.pageNo = 1
      pageTemp.pageSize = relateCourseIdList.length < 200 ? relateCourseIdList.length : 200
      const res = await MsCourseLearningBackGateway.pageCourseInServicer({
        page: pageTemp,
        request: params
      })
      const response = new Response<Array<string>>()
      response.status = res.status
      if (response.status.isSuccess()) {
        res.data?.currentPageData?.forEach((course: CourseResponse) => {
          this.relateCourseIdList.set(course.id, course.name)
        })
      }
      return response
    } else {
      const response = new Response<Array<string>>()
      return response
    }
  }

  /**
   * @description: 导出试题
   * @param {*}
   * @return {*}
   */
  async doExport(): Promise<Response<string>> {
    // todo 还未提供
    return
  }
}
export default QueryQuestionList
