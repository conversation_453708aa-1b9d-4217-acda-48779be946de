import MsCourseResourceV1 from '@api/ms-gateway/ms-courselearning-resource-batch-v1'
class MutationSyncAllUsedCoursePackage {
  /**
   * 课程包ID
   */
  coursePackageId: string
  constructor(coursePackageId: string) {
    this.coursePackageId = coursePackageId
  }
  /**
   * 同步所有方案
   */
  async syncAllCoursePackageUsed() {
    const res = await MsCourseResourceV1.syncAllCoursePackageUsed(this.coursePackageId)
    return res
  }
}

export default MutationSyncAllUsedCoursePackage
