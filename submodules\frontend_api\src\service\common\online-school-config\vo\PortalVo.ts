import { PortalInfoResponse1 } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import {
  TrainingInstitutionH5PortalUpdateRequest,
  TrainingInstitutionWebPortalUpdateRequest
} from '@api/ms-gateway/ms-servicer-v1'
import Context from '@api/service/common/context/Context'
import LinkVo from '@api/service/common/online-school-config/vo/LinkVo'

class PortalVo {
  /**
   * id
   */
  id = ''
  /**
   * 平台名称
   */
  title = ''
  /**
   * 门户logo
   */
  logo = ''
  /**
   * 浏览器图标
   */
  icon = ''
  /**
   * 客服电话图片
   */
  CSPhonePicture = ''
  /**
   * 客服电话
   */
  CSPhone = ''
  /**
   * 客服咨询时间
   */
  CSCallTime = ''
  /**
   * 移动二维码
   */
  mobileQRCode = ''
  /**
   * 移动二维码来源标识，1：使用系统生成二维码 2：自定义
   */
  mobileQRCodeSign = 1
  /**
   * 培训流程图片
   */
  trainingFlowPicture = ''
  /**
   * 在线客服代码内容id
   */
  CSOnlineCodeId = ''
  /**
   * 底部内容（底部落款）
   */
  footContent = ''
  /**
   * 友情链接类型 1-文本  2-图片
   */
  friendLinkType = 1
  /**
   * 友情链接列表
   */
  friendLinkList = new Array<LinkVo>()
  /**
   * 是否提供服务号
   */
  isProvideServiceAccount = false

  /**
   * web模板id
   */
  webTemplateId: string

  /**
   * 企业微信客服图片
   */
  enterPriseWxCustomer = ''
  /**
   * 强制更新（前端传什么，后端就更新什么）
   */
  compulsionUpdate = false

  /**
   * web域名
   */
  domainName = ''
  /**
   * H5域名
   */
  domainNameH5 = ''
  from(res: PortalInfoResponse1) {
    this.id = res.id
    this.title = res.title
    this.logo = res.logo
    this.icon = res.icon
    this.CSPhonePicture = res.CSPhonePicture
    this.CSPhone = res.CSPhone
    this.CSCallTime = res.CSCallTime
    this.mobileQRCode = res.mobileQRCode
    this.trainingFlowPicture = res.trainingFlowPicture
    this.CSOnlineCodeId = res.CSOnlineCodeId
    this.footContent = res.footContent
    this.friendLinkType = res.friendLinkType
    this.isProvideServiceAccount = res.isProvideServiceAccount
    this.webTemplateId = res.portalTemplateId
    this.enterPriseWxCustomer = res.CSWechat
    this.mobileQRCodeSign = res.mobileQRCodeSign ? res.mobileQRCodeSign : 1
    this.domainName = res.domainName
    this.domainNameH5 = res.domainName
    // linklist 在外部赋值
  }

  toWeb() {
    const request = new TrainingInstitutionWebPortalUpdateRequest()
    request.trainingInstitutionId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
    request.title = this.title
    request.logo = this.logo
    request.icon = this.icon
    request.csPhonePicture = this.CSPhonePicture
    request.csPhone = this.CSPhone
    request.csCallTime = this.CSCallTime
    request.mobileQRCode = this.mobileQRCode
    request.mobileQRCodeSign = this.mobileQRCodeSign
    request.trainingFlowPicture = this.trainingFlowPicture
    request.csOnlineCodeId = this.CSOnlineCodeId
    request.footContent = this.footContent
    request.friendLinkTypes = this.friendLinkType
    request.friendLinks = this.friendLinkList?.map(LinkVo.to)
    request.csWechat = this.enterPriseWxCustomer
    request.compulsionUpdate = this.compulsionUpdate
    request.friendLinks.map(item => {
      item.friendLinkType = this.friendLinkType
    })

    return request
  }

  toH5() {
    const request = new TrainingInstitutionH5PortalUpdateRequest()
    request.trainingInstitutionId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
    request.idProvideServiceAccount = this.isProvideServiceAccount
    return request
  }
}
export default PortalVo
