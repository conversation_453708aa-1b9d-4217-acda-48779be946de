import { CertificateModelEnum } from '@api/service/common/enums/personal-leaning/CertificateModelTypes'
import StudentCourseVo from '@api/service/customer/course/query/vo/StudentCourseVo'
import CourseDetailVo from './CourseDetailVo'
import UserInfoVo from './UserInfoVo'

/*
 *培训证明详情
 */
class TrainingCertificateDetailVo {
  /*
    培训班名称
  */
  schemeName = ''
  /*
    姓名
  */
  userName = ''
  /*
    证件号
  */
  idCard = ''
  /*
    培训专业
  */
  trainingProfession = ''
  /*
    科目类型
  */
  subjectType: string = undefined
  /**
   * 总课时
   */
  grade: number = undefined
  /*
    培训类别
  */
  trainingCategory: string = undefined
  /*
    培训课程信息集合
  */
  trainingCourseList: Array<CourseDetailVo> = []
  /*
    证明类型
    1人设 2建设
  */
  type: CertificateModelEnum = undefined
  /**
   *  证书技术工种：
   */
  jobCategoryName = ''
  /**
   * 证书技术等级：
   */
  professionalLevelName = ''
  /**
   * 年度：
   */
  trainingYear = ''
  /**
   * 合格时间
   */
  issueTime = ''
  /**
   * 分类学习列表
   */
  h5OutlineDataList?: {
    name: string
    h5CourseDataList: { name: string; period: number; sort: number }[]
    sort: number
  }[] = []
  /**
   * 单位
   */
  unitName = ''
  /**
   * 施训机构
   */
  trainingInstitutions = ''
  /**
   * 学习形式
   */
  learningType = ''
  /**
   * 考核结果
   * @type {string}
   */
  examinationResult = ''
  /**
   * 培训类别
   */
  trainingType = ''
  /**
   * 岗位类别
   */
  jobType = ''
  /**
   * 技术等级
   */
  declarationLevel = ''
  /**
   * 工种
   */
  profession = ''
  /**
   * 证书编号
   */
  certificateNo = ''
  /**
   * 联系方式
   */
  phone? = ''
  electronicSeal? = '' // 电子章
  photo? = '' // 证件照
  name? = '' // 姓名
  /**
   * 单位地区
   */
  unitArea = ''
  /**
   * 个人信息地区
   */
  localArea = ''
  /**
   * 数据类型(同模板二维码展示数据不同)
   * 1-新数据   2-旧数据
   */
  dataType: number = undefined
  sign? = '' // 培训单位
  /**
   * 课程列表 - 分支
   */
  courseList?: {
    id: string
    name: string
    data: { id: string; name: string; grade: number; type: number; sort?: number; parentSort?: number }[]
  }[] = []
  from(item: UserInfoVo, list: Array<StudentCourseVo>) {
    this.userName = item.userName
    this.idCard = item.idCard
    this.trainingProfession = item.trainingProfession
    this.type = item.type
    this.subjectType = item.subjectType || undefined
    this.trainingCategory = item.trainingCategory || undefined
    this.jobCategoryName = item.jobCategoryName
    this.professionalLevelName = item.professionalLevelName
    this.trainingYear = item.trainingYear

    const listTemp =
      list?.map((sub: StudentCourseVo) => {
        return {
          id: sub.id,
          name: sub.name,
          period: sub.period,
          learningResultTime: sub.learningResultTime,
          timeStamp: new Date(sub.learningResultTime).getTime() || 0
        }
      }) || []
    this.trainingCourseList = this.orderByStudyTime(listTemp) || []
  }

  // 按照学习完成时间降序排
  private orderByStudyTime(arr: Array<CourseDetailVo>) {
    for (let j = 0; j < arr.length - 1; j++) {
      // 外层循环只需要 arr.length-1
      for (let i = 0; i < arr.length - 1 - j; i++) {
        // 由于每次循环后，最大的数都会排在最后，内层两数比较次数逐次减 j
        if (arr[i]?.timeStamp > arr[i + 1]?.timeStamp) {
          this.swap(arr, i, i + 1)
        }
      }
    }
    return arr
  }

  //两数交换方法
  private swap(arr: Array<any>, i: number, j: number) {
    ;[arr[i], arr[j]] = [arr[j], arr[i]]
  }
}

export default TrainingCertificateDetailVo
