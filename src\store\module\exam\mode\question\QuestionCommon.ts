import {
  ComprehensiveRequest,
  JudgementRequest,
  MultipleChoiceRequest,
  SingleChoiceRequest
} from '@api/gateway/PreExam-default'

/**
 * 试题详情，通用的在父子组件传值模型
 * @author: eleven
 * @date: 2020/4/10
 */

export class QuestionCommon {
  /**
   * 是否易错题
   */
  errorProne: boolean
  /**
   * 题目
   */
  title: string
  /**
   * 判断题对象
   */
  judgement: JudgementRequest
  /**
   * 单选题对象
   */
  singleChoice: SingleChoiceRequest
  /**
   * 多选题对象
   */
  multipleChoice: MultipleChoiceRequest
  /**
   * 案例题对象
   */
  comprehensive: ComprehensiveRequest
  /**
   * 试题题析
   */
  description: string
}
