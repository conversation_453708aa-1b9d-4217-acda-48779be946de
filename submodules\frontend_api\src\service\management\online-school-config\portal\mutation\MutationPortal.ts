import msServicerV1, { PublishTrainingInstitutionPortalRequest } from '@api/ms-gateway/ms-servicer-v1'
import Context from '@api/service/common/context/Context'
import QueryPortal from '@api/service/management/online-school-config/portal/query/QueryPortal'
import { Response, ResponseStatus } from '@hbfe/common'

class MutationPortal {
  async doSave(type?: number): Promise<ResponseStatus> {
    let res = new Response()
    if (type === 2) {
      res = await msServicerV1.updateTrainingInstitutionH5Portal(QueryPortal.h5PortalInfo.toH5())
    } else {
      res = await msServicerV1.updateTrainingInstitutionWebPortal(QueryPortal.webPortalInfo.toWeb())
    }
    return res.status
  }

  async handleAccess(portalType: number, isAccess: boolean): Promise<ResponseStatus> {
    const request = new PublishTrainingInstitutionPortalRequest()
    request.portalType = portalType
    request.trainingInstitutionId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
    const res = isAccess
      ? await msServicerV1.publishTrainingInstitutionPortal(request)
      : await msServicerV1.unpublishTrainingInstitutionPortal(request)
    return res.status
  }
}
export default new MutationPortal()
