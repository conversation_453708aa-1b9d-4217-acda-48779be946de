import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/PlatformLogin'

// 枚举

// 类

/**
 * 小程序登录回调参数
<AUTHOR> create 2021/3/3 11:16
 */
export class AppletsRandomLoginRequest {
  /**
   * 身份证
   */
  certificate?: string
  /**
   * 用户名
   */
  userName?: string
}

/**
 * 随机码回调接口的参数
<AUTHOR> create 2021/3/1 20:57
 */
export class RandomCodeRequest {
  /**
   * 身份证号
   */
  identity?: string
  /**
   * 姓名
   */
  username?: string
  /**
   * 回调的随机码
   */
  code?: string
  /**
   * 验证时间
   */
  validationDate?: string
  /**
   * 验证结果，1验证成功
   */
  result: number
  /**
   * 失败码
详细规则查看微信文档：https://developers.weixin.qq.com/community/business/doc/000442d352c1202bd498ecb105c00d
   */
  errCode?: string
  /**
   * 失败信息
详细规则查看微信文档：https://developers.weixin.qq.com/community/business/doc/000442d352c1202bd498ecb105c00d
   */
  errMsg?: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 小程序回调接口使用，小程序人脸识别完成后，需要回调该接口
   * @param params 请求参数
   * @return 单点登录的token
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async appletsCallBackLogin(
    params: AppletsRandomLoginRequest,
    mutate: DocumentNode = GraphqlImporter.appletsCallBackLogin,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { params },
      operation: operation
    })
  }

  /**   * 登录回调使用，提供给第三方回调的，平台不需要关心该接口
   * @param params 参数
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async callBackLogin(
    params: RandomCodeRequest,
    mutate: DocumentNode = GraphqlImporter.callBackLogin,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { params },
      operation: operation
    })
  }
}

export default new DataGateway()
