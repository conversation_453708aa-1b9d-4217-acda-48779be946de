import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-teachingplan-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-teachingplan-dds-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * <AUTHOR>
@since 安徽建设
 */
export class TrainingPointCreateRequest {
  /**
   * 培训点名称
   */
  name?: string
  /**
   * 经度
   */
  longitude: number
  /**
   * 纬度
   */
  latitude: number
  /**
   * 选中的培训地址
   */
  specificAddress?: string
  /**
   * 所在地区
   */
  areaPath?: string
  /**
   * 培训教室
   */
  classRoom?: string
  /**
   * 单位id
   */
  ownerId?: string
}

/**
 * <AUTHOR>
@since
 */
export class TrainingPointResponse {
  /**
   * 200 正常
E500 名称重复
E501 已被引用不可删除
   */
  code: string
  /**
   * 培训点id
   */
  id: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建培训点
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createTrainingPoint(
    request: TrainingPointCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createTrainingPoint,
    operation?: string
  ): Promise<Response<TrainingPointResponse>> {
    return commonRequestApi<TrainingPointResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
