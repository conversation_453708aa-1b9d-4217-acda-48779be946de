import { UserInfo } from '@api/service/common/models/user/UserInfo'
import moment from 'moment'
import { Constants } from '@api/service/common/models/common/Constants'

export default class CertificateInfo {
  /**
   * 主键 证书ID
   */
  id: string
  /**
   * 证书名称
   */
  name: string
  /**
   * 证书编号
   */
  certNo: string
  /**
   * 证书持有人
   */
  owner: string
  /**
   * 证书签发时间
   */
  signTime: Date
  /**
   * 证书状态：1、未打 2、打印中、3、打印成功 4、打印失败 5、作废 6入库中 7 入库成功  8入库失败
   */
  state: string
  /**
   * 是否测试数据
   */
  test: boolean
  /**
   * 是否打印
   */
  print: boolean
  /**
   * 是否批量打印
   */
  batchPrint: boolean
  /**
   * 创建时间
   */
  createTime: Date
  /**
   * 创建人ID
   */
  creatorId: string
  /**
   * 图片路径
   */
  picPath: string
  /**
   * 考核完成的学习方案ID
   */
  schemeId: string
  /**
   * 考核完成的期数编号
   */
  issueId: string
  /**
   * 考核ID
   */
  assessId: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 证书所有人的信息
   */
  userInfo: UserInfo
  /**
   * 学习方案名称
   */
  schemeName: string
  /**
   * 培训类别
   */
  trainingTypeName: string
  /**
   * 培训工种
   */
  workTypeName: string
  /**
   * 年度
   */
  year = 0
  /**
   * 是否开放打印证明
   */
  isOpenPrintCertificate?: boolean
  /**
   * 培训获得总学时
   */
  grade = 0

  isPrint(): boolean {
    return this.print || this.batchPrint
  }

  getStatusDesc(): string {
    switch (this.state) {
      case '1':
        return '未打印'
      case '2':
        return '打印中'
      case '3':
        return '已打印'
      case '4':
        return '打印失败'
      case '5':
        return '作废'
      case '6':
        return '入库中'
      case '7':
        return '入库成功'
      case '8':
        return '入库失败'
      default:
        return '未知'
    }
  }

  formatSignTime(pattern = Constants.DATE_PATTERN) {
    return moment(this.signTime).format(pattern)
  }

  formatCreateTime(pattern = Constants.DATE_PATTERN) {
    return moment(this.createTime).format(pattern)
  }
}
