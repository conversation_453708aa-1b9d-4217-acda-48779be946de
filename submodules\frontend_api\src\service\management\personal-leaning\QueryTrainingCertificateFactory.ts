import QueryTrainingCertificate from '@api/service/management/personal-leaning/query/QueryTrainingCertificate'
import QueryTrainingDetail from '@api/service/management/personal-leaning/query/QueryTrainingDetail'
import QueryTrainingArchivesList from '@api/service/management/personal-leaning/query/QueryTrainingArchivesList'
import QueryCertificateTemplate from './query/QueryCertificateTemplate'
/* 
  查询培训证明工厂
*/
class TrainingCertificateFactory {
  // 培训证明模板实例, 电子章
  get certificateTemplate() {
    return new QueryCertificateTemplate()
  }

  // 培训证明实例
  get trainingCertificate() {
    return new QueryTrainingCertificate()
  }

  // 培训明细实例
  get trainingDetail() {
    return new QueryTrainingDetail()
  }

  // 学习档案列表实例
  get trainingArchivesList() {
    return new QueryTrainingArchivesList()
  }
}

export default new TrainingCertificateFactory()
