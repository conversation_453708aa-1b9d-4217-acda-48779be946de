import { ReceiveAccountConfigResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import PaymentChannelType from '@api/service/common/enums/trade-configuration/PaymentChannelType'

class ReceiveAccountVo {
  /**
   * 唯一标识
   */
  id = ''
  /**
   * 收款账号（商户号、支付宝账号、开户号）
   */
  accountNo = ''
  /**
   * 收款账号别名
   */
  accountName = ''
  /**
   * 支付方式 1-线上 2-线下
   */
  accountType = 1
  /**
   * 退款方式，1-线上退款，2-线下退款
   */
  returnType = 1
  /**
   * 支付账号类型id 1-支付宝 2-微信 3-培训券
   */
  paymentChannelId = ''
  /**
   * 支付账号类型 1-支付宝 2-微信 3-培训券
   */
  paymentChannel = ''
  /**
   * 收款账号状态 0-停用 1-启用
   */
  status = 0

  get statusText() {
    return this.status ? '启用' : '停用'
  }

  get accountTypeText() {
    return this.accountType === 1 ? '线上' : '线下'
  }

  get returnTypeText() {
    return this.returnType === 1 ? '线上退款' : '线下退款'
  }

  static from(res: ReceiveAccountConfigResponse) {
    const receiveAccount = new ReceiveAccountVo()
    receiveAccount.id = res.id
    receiveAccount.accountNo = res.accountNo
    receiveAccount.accountName = res.name
    receiveAccount.accountType = res.accountType
    receiveAccount.returnType = res.returnType
    receiveAccount.paymentChannelId = res.paymentChannelId
    receiveAccount.paymentChannel = PaymentChannelType[res.paymentChannelId]
    receiveAccount.status = res.status
    return receiveAccount
  }
}
export default ReceiveAccountVo
