import ExcellentCourseConfigDetail from '@api/service/management/online-school-config/excellent-course/query/vo/ExcellentCourseConfigDetail'
import { ExcellentCoursesCategoryResponse } from '@api/ms-gateway/ms-servicer-series-v1'

class ExcellentCourseCategoryConfig {
  id: string
  courses: Array<ExcellentCourseConfigDetail> = new Array<ExcellentCourseConfigDetail>()
  name: string
  sort = 0
  createTime: string
  // ui
  isRemoved = false

  static from(response: ExcellentCoursesCategoryResponse) {
    const detail = new ExcellentCourseCategoryConfig()
    detail.id = response.categoryId
    // detail.name = response.name
    detail.sort = response.sort
    detail.createTime = response.createdTime
    detail.courses = response.courses.map(ExcellentCourseConfigDetail.from)
    return detail
  }

  toJSON() {
    return {
      categoryId: this.id,
      sort: this.sort,
      courses: this.courses
    }
  }

  addCourse(courseList: Array<ExcellentCourseConfigDetail>) {
    this.courses.push(...courseList)
  }

  // 删除列表
  removeCourse(courseList: Array<ExcellentCourseConfigDetail>) {
    courseList.forEach((item: ExcellentCourseConfigDetail) => {
      const removeItemIndex = this.courses.findIndex(list => list?.id == item?.id)
      if (removeItemIndex != -1) this.courses.splice(removeItemIndex, 1)
    })
  }

  changeCoursePosition(oldIndex: number, newIndex: number) {
    if (oldIndex === newIndex) return
    const oldIndexItem = this.courses[oldIndex]
    this.courses.splice(oldIndex, 1)
    this.courses.splice(newIndex, 0, oldIndexItem)
    this.sortCourses()
  }

  sortCourses() {
    this.courses.forEach((course: ExcellentCourseConfigDetail, index: number) => {
      course.sort = index
    })
  }
}

export default ExcellentCourseCategoryConfig
