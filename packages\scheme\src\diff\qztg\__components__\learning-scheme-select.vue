<template>
  <el-drawer
    title="选择培训方案"
    :visible.sync="dialogShow"
    direction="rtl"
    size="1200px"
    custom-class="m-drawer"
    @close="handleCancel"
  >
    <div class="drawer-bd">
      <div class="f-pl10 f-pr10">
        <el-row :gutter="16" class="m-query is-border-bottom">
          <hb-search-wrapper expand @reset="resetQueryParam" class="m-query is-border-bottom">
            <el-form-item label="年度">
              <biz-year-select v-model="localSkuProperty.year" placeholder="请选择培训年度"></biz-year-select>
            </el-form-item>
            <el-form-item label="培训方案名称">
              <el-input clearable placeholder="请输入培训方案名称" v-model="localSkuProperty.schemeName" />
            </el-form-item>
            <el-form-item label="行业">
              <biz-industry-select
                v-model="localSkuProperty.industry"
                @clearIndustrySelect="handleClearIndustrySelect"
                @industryPropertyId="handleIndustryPropertyId"
                @industryInfos="handleIndustryInfos"
                ref="industrySelect"
              ></biz-industry-select>
            </el-form-item>
            <el-form-item label="科目类型" v-if="skuVisible.subjectType && localSkuProperty.industry">
              <biz-accounttype-select
                v-model="localSkuProperty.subjectType"
                :industry-property-id="industryPropertyId"
                :industryId="localSkuProperty.industry"
              >
              </biz-accounttype-select>
            </el-form-item>
            <el-form-item
              label="培训专业"
              v-if="
                skuVisible.trainingCategory &&
                localSkuProperty.industry &&
                envConfig.societyIndustryId &&
                localSkuProperty.industry === envConfig.societyIndustryId
              "
            >
              <biz-major-cascader
                v-model="localSkuProperty.societyTrainingMajor"
                :industry-property-id="industryPropertyId"
                :industryId="localSkuProperty.industry"
              />
            </el-form-item>
            <el-form-item
              label="培训类别"
              v-if="
                skuVisible.trainingCategory &&
                localSkuProperty.industry &&
                (localSkuProperty.industry === envConfig.constructionIndustryId ||
                  localSkuProperty.industry === envConfig.occupationalHealthId)
              "
            >
              <biz-training-category-select
                v-model="localSkuProperty.trainingCategory"
                :industry-property-id="industryPropertyId"
                @updateTrainingCategory="handleUpdateTrainingCategory"
                :industryId="localSkuProperty.industry"
              />
            </el-form-item>
            <el-form-item
              label="培训专业"
              v-if="
                skuVisible.trainingCategory &&
                localSkuProperty.industry &&
                envConfig.constructionIndustryId &&
                localSkuProperty.industry === envConfig.constructionIndustryId
              "
            >
              <biz-major-select
                v-model="localSkuProperty.constructionTrainingMajor"
                :industry-property-id="industryPropertyId"
                :training-category-id="trainingCategoryId"
                :industryId="localSkuProperty.industry"
              />
            </el-form-item>
            <el-form-item label="方案学时">
              <el-input v-model="localSkuProperty.minPeriod" class="input-num" style="width: 105px" />
              -
              <el-input v-model="localSkuProperty.maxPeriod" class="input-num" style="width: 105px" />
              <span class="f-ml5">学时</span>
            </el-form-item>
            <template slot="actions">
              <el-button type="primary" @click="doSearch">查询</el-button>
            </template>
          </hb-search-wrapper>
        </el-row>
      </div>
      <el-alert type="warning" :closable="false" class="m-alert f-mb10">
        <p class="f-c3">
          已选择
          <span class="f-co">{{ selectedPlan }}</span>
          个方案，报名所选方案时将自动选择当前方案合并下单，请确认无误后提交。
        </p>
      </el-alert>
      <!--表格-->
      <el-table
        stripe
        :data="tableList"
        v-loading="queryLoading"
        @selection-change="handleSelectionChange"
        ref="schemeTable"
        class="m-table"
        max-height="400px"
        :row-key="(row) => row.commoditySkuId"
      >
        <el-table-column type="selection" reserve-selection width="55" fixed="left"> </el-table-column>
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="培训方案名称" min-width="240" fixed="left">
          <template slot-scope="scope">
            <p>
              <el-tag type="primary" effect="dark" size="mini">{{ getSchemeType(scope.row) }}</el-tag>
              <el-tooltip class="item" effect="dark" :content="scope.row.commodityBasicData.saleTitle" placement="top">
                <span style="margin-right: 5px">{{ scope.row.commodityBasicData.saleTitle }}</span>
              </el-tooltip>
              <hb-copy :content="scope.row.commodityBasicData.saleTitle"></hb-copy>
            </p>
          </template>
        </el-table-column>
        <el-table-column label="报名学时" min-width="100" align="center">
          <template slot-scope="scope">{{ scope.row.period }}</template>
        </el-table-column>
        <el-table-column label="价格" min-width="90" align="center">
          <template slot-scope="scope">{{ scope.row.commodityBasicData.price }}</template>
        </el-table-column>
        <el-table-column label="培训属性" min-width="240">
          <template slot-scope="scope">
            <p v-if="getSkuPropertyName(scope.row, 'industry')">
              行业：{{ getSkuPropertyName(scope.row, 'industry') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'region')">地区：{{ getSkuPropertyName(scope.row, 'region') }}</p>
            <p v-if="getSkuPropertyName(scope.row, 'jobLevel')">
              技术等级：{{ getSkuPropertyName(scope.row, 'jobLevel') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'subjectType')">
              科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
            </p>
            <p v-if="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
              培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'trainingMajor')">
              培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'trainingObject')">
              培训对象：{{ getSkuPropertyName(scope.row, 'trainingObject') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'positionCategory')">
              岗位类别：{{ getSkuPropertyName(scope.row, 'positionCategory') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'year')">培训年度：{{ getSkuPropertyName(scope.row, 'year') }}</p>
            <p v-if="getSkuPropertyName(scope.row, 'learningPhase')">
              学段：{{ getSkuPropertyName(scope.row, 'learningPhase') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'discipline')">
              学科：{{ getSkuPropertyName(scope.row, 'discipline') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'practitionerCategory')">
              执业类别：{{ getSkuPropertyName(scope.row, 'certificatesType') }}-{{
                getSkuPropertyName(scope.row, 'practitionerCategory')
              }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="学习起止时间" min-width="220">
          <template slot-scope="scope">
            <p v-if="!scope.row.isLongTerm">起始：{{ scope.row.trainingBeginDate }}</p>
            <p v-if="!scope.row.isLongTerm">结束：{{ scope.row.trainingEndDate }}</p>
            <p v-if="scope.row.isLongTerm">长期有效</p>
          </template>
        </el-table-column>
        <el-table-column label="报名起止时间" min-width="220">
          <template slot-scope="scope">
            <p v-if="scope.row.registerBeginDate || scope.row.registerEndDate">
              起始：{{ scope.row.registerBeginDate ? scope.row.registerBeginDate : '--' }}
            </p>
            <p v-if="scope.row.registerBeginDate || scope.row.registerEndDate">
              结束：{{ scope.row.registerEndDate ? scope.row.registerEndDate : '--' }}
            </p>
            <p v-if="!scope.row.registerBeginDate && !scope.row.registerEndDate">暂不开启</p>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <div class="f-mt10">
        <hb-pagination :page="trainSchemePage" v-bind="trainSchemePage" :pageSizes="pageSizes"> </hb-pagination>
      </div>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSure">确定</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Watch, Ref, Prop } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import { cloneDeep, isBoolean } from 'lodash'
  import QueryTrainClassCommodityList from '@api/service/diff/management/qztg/train-class/query/QueryTrainClassCommodityList'
  import QztgDiffCommoditySkuRequest from '@api/service/diff/management/qztg/train-class/query/vo/QztgDiffCommoditySkuRequest'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import SchemeType, { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import UITrainClassCommodityDetail from '@hbfe/jxjy-admin-scheme/src/models/UITrainClassCommodityDetail'
  import {
    OnShelveRequest,
    RegionSkuPropertySearchRequest,
    SchemeRequest,
    SkuPropertyRequest,
    RegionSkuPropertyRequest,
    PurchaseChannelRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import BizMajorCascader from '@hbfe/jxjy-admin-components/src/biz/biz-major-cascader.vue'
  import BizAccounttypeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-accounttype-select.vue'
  import BizIndustrySelect from '@hbfe/jxjy-admin-components/src/biz/biz-industry-select.vue'
  import BizYearSelect from '@hbfe/jxjy-admin-components/src/biz/biz-year-select.vue'
  import BizTrainingCategorySelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-category-select.vue'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-scheme/src/models/SchemeSkuProperty'
  import { bind, debounce } from 'lodash-decorators'
  import { truncate } from 'lodash'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  class SchemeSkuPropertyDiff extends SchemeSkuProperty {
    schemeName = ''
    maxPeriod = ''
    minPeriod = ''
  }
  @Component({
    components: {
      BizMajorCascader,
      BizAccounttypeSelect,
      BizIndustrySelect,
      BizYearSelect,
      BizTrainingCategorySelect
    }
  })
  export default class extends Vue {
    @Ref('industrySelect') industrySelect: BizIndustrySelect
    @Ref('schemeTable') distributionListTable: any
    constructor() {
      super()
      this.initQueryParam()
      this.trainSchemePage = new UiPage(this.doQueryList, this.doQueryList)
    }

    /**
     * 页码
     * */
    trainSchemePage: UiPage = new UiPage()

    dialogShow = false
    /**
     * 查询loading
     **/
    queryLoading = false
    /**
     * 列表数据
     */
    tableList: UITrainClassCommodityDetail[] = []
    // 查询参数 - 培训方案
    trainSchemeQueryParam: QztgDiffCommoditySkuRequest
    // 培训方案业务状态层入口
    QueryTrainClassCommodityList = new QueryTrainClassCommodityList()

    schemeType = SchemeType
    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''

    /**
     * 培训类别Id
     */
    trainingCategoryId = ''

    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: true,
      // 培训类别
      trainingCategory: true,
      // 技术等级
      jobLevel: true,
      // 培训对象
      trainingObject: true,
      //   岗位类别
      positionCategory: true,
      //执业类别
      occupationalCategory: true
    }

    /**
     * 当前网校信息
     */
    envConfig = {
      // 工勤行业
      workServiceId: '',
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 职业卫生行业Id
      occupationalHealthId: '',
      //教师行业id
      teacherIndustryId: '',
      //药师行业id
      yshyId: ''
    }

    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuPropertyDiff = {
      /**
       * 年度
       */
      year: '',
      /**
       * 培训方案名称
       */
      schemeName: '',
      /**
       * 行业
       */
      industry: '',
      /**
       * 科目类型
       */
      subjectType: '',
      /**
       * 培训专业 - 人社行业
       */
      societyTrainingMajor: [] as string[],
      /**
       * 培训专业 - 建设行业
       */
      constructionTrainingMajor: '',
      /**
       * 方案学时
       */
      maxPeriod: '',
      minPeriod: ''
    } as unknown as SchemeSkuPropertyDiff
    // 被选中的方案
    mergeCommodityList: Array<TrainClassCommodityVo> = []
    // 长期有效 - 开始时间
    defaultBeginDate = CreateSchemeUtils.defaultBeginDate
    // 长期有效 - 结束时间
    defaultEndDate = CreateSchemeUtils.defaultEndDate
    // 要从查询结果中剔除的商品ID集合
    notShowCommoditySkuIdList: Array<string> = []

    pageSizes = [5, 10, 15, 20, 25, 75, 100]

    // 已选择方案数量
    get selectedPlan() {
      return this.mergeCommodityList.length
    }

    async showDialog(notShowCommoditySkuIdList: string[]) {
      console.log(notShowCommoditySkuIdList, 'notShowCommoditySkuIdList')
      this.dialogShow = true
      this.initQueryParam()
      // 要从查询结果中剔除的商品ID集合
      this.notShowCommoditySkuIdList = notShowCommoditySkuIdList
      await this.queryConfig()
    }

    /**
     * @description 基础查询
     */
    async queryConfig() {
      this.$nextTick(async () => {
        await this.industrySelect.getIndustryOptions()
        if (this.industrySelect.industryOptions && this.industrySelect.industryOptions.length > 0) {
          this.localSkuProperty.industry = this.industrySelect.industryOptions[0].id
        }
        this.doSearch()
      })
    }

    /**
     * 初始化查询参数
     */
    initQueryParam() {
      this.trainSchemeQueryParam = new QztgDiffCommoditySkuRequest()
      this.trainSchemeQueryParam.onShelveRequest = new OnShelveRequest()
      this.trainSchemeQueryParam.onShelveRequest.onShelveStatus = 1 //默认已上架
      this.trainSchemeQueryParam.schemeRequest = new SchemeRequest()
      this.trainSchemeQueryParam.schemeRequest.schemeName = ''
      // this.trainSchemeQueryParam.schemeRequest.period = new DoubleScopeRequest()
      this.trainSchemeQueryParam.skuPropertyRequest = new SkuPropertyRequest()
      this.trainSchemeQueryParam.skuPropertyRequest.year = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      this.trainSchemeQueryParam.skuPropertyRequest.regionSkuPropertySearch.region =
        new Array<RegionSkuPropertyRequest>()
      this.trainSchemeQueryParam.skuPropertyRequest.industry = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.subjectType = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingProfessional = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingObject = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.positionCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.jobLevel = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.learningPhase = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.discipline = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.certificatesType = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.practitionerCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingForm = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingForm.push(TrainingModeEnum.online)
      this.trainSchemeQueryParam.isDisabledResourceShow = true
      this.trainSchemeQueryParam.purchaseChannelList = new Array<PurchaseChannelRequest>()
      const purchaseChannel = new PurchaseChannelRequest()
      purchaseChannel.purchaseChannelType = 1
      purchaseChannel.couldBuy = true
      this.trainSchemeQueryParam.purchaseChannelList.push(purchaseChannel)
      // this.trainSchemeQueryParam.purchaseChannelList[0].couldBuy = true
    }

    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
    }

    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.SUBJECT_TYPE)
      const configTrainingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.TRAINING_CATEGORY
      )
      const jobLevel = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.JOB_LEVEL)
      const trainingObject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.TRAINING_OBJECT)
      const positionCategory = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.POSITION_CATEGORY)
      this.skuVisible.subjectType = configSubjectType > -1
      this.skuVisible.trainingCategory = configTrainingCategory > -1
      this.skuVisible.jobLevel = jobLevel > -1
      this.skuVisible.trainingObject = trainingObject > -1
      this.skuVisible.positionCategory = positionCategory > -1
    }

    /**
     * 响应组件行业Id集合传参
     */
    async handleIndustryInfos(values: any) {
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.occupationalHealthId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
      this.envConfig.yshyId = values.medicineIndustryId || ''
      // 仅当单个行业，需要默认选中
      //   if (this.industrySelect?.industryOptions?.length === 1) {
      //     this.localSkuProperty.industry = this.industrySelect.industryOptions[0].id
      //   }
    }

    /**
     * 获取培训方案类型
     */
    getSchemeType(row: UITrainClassCommodityDetail) {
      return this.schemeType.getSchemeType(row.schemeType, true)
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: UITrainClassCommodityDetail, type: string): string {
      if (row.skuValueNameProperty[type]?.skuPropertyName) {
        const value = row.skuValueNameProperty[type].skuPropertyName
        const valuesArr = value.split('/'),
          lastIndex = valuesArr.length - 1
        return type === 'trainingMajor' && !row.isSocietyIndustry ? valuesArr[lastIndex] : value
      }
      return ''
    }

    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
    }

    /**
     * 选择方案
     */
    handleSelectionChange(val: Array<UITrainClassCommodityDetail>) {
      this.mergeCommodityList = val.map((item: UITrainClassCommodityDetail) => {
        return item
      })
    }
    /**
     * 搜索
     */
    async doSearch() {
      this.trainSchemePage.pageNo = 1
      await this.doQueryList()
    }

    async doQueryList() {
      this.queryLoading = true
      try {
        this.getPageQueryParams()
        const trainSchemeList = await this.QueryTrainClassCommodityList.queryTrainClassCommodityList(
          this.trainSchemePage,
          this.trainSchemeQueryParam
        )
        this.tableList = new Array<UITrainClassCommodityDetail>()
        trainSchemeList?.map((el: UITrainClassCommodityDetail) => {
          const item = new UITrainClassCommodityDetail()
          Object.assign(item, el)
          item.isLongTerm = false
          item.isSocietyIndustry = false
          if (item.trainingEndDate === this.defaultEndDate && item.trainingBeginDate === this.defaultBeginDate) {
            item.isLongTerm = true
          }
          if (item.skuValueNameProperty?.industry?.skuPropertyName === '人社行业') {
            item.isSocietyIndustry = true
          }
          this.tableList.push(item)
        })
        console.log(this.tableList, 'trainSchemeList')
      } catch (e) {
        console.log('获取培训方案分页列表失败！', e)
      } finally {
        this.queryLoading = false
        this.$nextTick(() => {
          this.distributionListTable.doLayout()
        })
      }
    }

    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      this.initQueryParam()
      this.localSkuProperty = new SchemeSkuPropertyDiff()
      await this.doQueryList()
    }

    /**
     * 处理列表查询参数
     */
    getPageQueryParams() {
      this.getLocalSkuProperty()
      this.trainSchemeQueryParam.schemeRequest.schemeName = this.localSkuProperty.schemeName || undefined
      this.trainSchemeQueryParam.maxPeriod = this.processValue(this.localSkuProperty.maxPeriod)
      this.trainSchemeQueryParam.minPeriod = this.processValue(this.localSkuProperty.minPeriod)
      // 要从查询结果中剔除的商品ID集合
      this.trainSchemeQueryParam.notShowCommoditySkuIdList = this.notShowCommoditySkuIdList
    }
    /**
     * 获取本地sku选项
     */
    getLocalSkuProperty() {
      const skuProperties = cloneDeep(this.trainSchemeQueryParam.skuPropertyRequest)
      skuProperties.year = !this.localSkuProperty.year ? ([] as string[]) : [this.localSkuProperty.year]
      skuProperties.industry = !this.localSkuProperty.industry ? ([] as string[]) : [this.localSkuProperty.industry]
      skuProperties.subjectType = !this.localSkuProperty.subjectType
        ? ([] as string[])
        : [this.localSkuProperty.subjectType]
      skuProperties.trainingCategory = !this.localSkuProperty.trainingCategory
        ? ([] as string[])
        : [this.localSkuProperty.trainingCategory]
      skuProperties.trainingProfessional = this.getTrainingProfessional()
      this.trainSchemeQueryParam.skuPropertyRequest = cloneDeep(skuProperties)
      //   console.log('selectedSkuProperties', JSON.stringify(this.trainSchemeQueryParam.skuPropertyRequest))
    }

    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      // console.log('envConfig', this.envConfig)
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人设行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }

    // 执行赋值操作的函数
    processValue(str: string): number | null {
      let val: number | null = null
      if (str !== null && str !== undefined && str !== '') {
        const num = Number(str)
        // 判断转换后的数字是否为 NaN
        if (!Number.isNaN(num)) {
          val = num
        }
      }
      return val
    }

    // 取消
    handleCancel() {
      if (this.dialogShow) {
        this.$nextTick(() => {
          this.distributionListTable.clearSelection()
        })
        this.dialogShow = false
        this.mergeCommodityList = []
        this.initQueryParam()
        this.localSkuProperty = new SchemeSkuPropertyDiff()
      }
    }

    /**
     * 确定
     */
    @bind
    @debounce(200)
    handleSure() {
      console.log(this.mergeCommodityList, 'this.mergeCommodityList')
      this.$emit('selectScheme', this.mergeCommodityList)
      this.handleCancel()
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .m-query .el-col {
    width: 33.3333333333% !important;
  }
</style>
