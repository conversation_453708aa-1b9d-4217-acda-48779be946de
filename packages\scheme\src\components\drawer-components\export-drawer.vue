<!--
 * @Author: z张仁榕
 * @Date: 2024-11-09 14:13:26
 * @LastEditors: z张仁榕
 * @LastEditTime: 2024-11-28 09:51:37
 * @Description:
-->
<template>
  <el-drawer title="提示" :visible.sync="showDrawer" size="600px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-form ref="form" label-width="auto" class="m-form f-mt20">
        <el-form-item label="请选择导出方式：">
          <el-radio-group v-model="exportType">
            <el-radio :label="1">导出方案列表数据</el-radio>
            <el-radio :label="2">导出方案期别明细数据</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item class="m-btn-bar">
          <el-button @click="showDrawer = false">取消</el-button>
          <el-button type="primary" @click="comfirmSve">确定</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    // 导出选项
    exportType = 1
    showDrawer = false
    comfirmSve() {
      this.$emit('confirmOption', this.exportType)
    }
    open() {
      this.showDrawer = true
    }
  }
</script>
