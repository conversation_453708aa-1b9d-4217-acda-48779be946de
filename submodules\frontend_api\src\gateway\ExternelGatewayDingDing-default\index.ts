import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/ExternelGatewayDingDing-default'

// 枚举

// 类

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 根据临时授权码获取钉钉用户id
   * @param tempAuthCode
   * @return
   * @param query 查询 graphql 语法文档
   * @param tempAuthCode 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDingTalkUserIdByTempAuthCode(
    tempAuthCode: string,
    query: DocumentNode = GraphqlImporter.getDingTalkUserIdByTempAuthCode,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: query,
      variables: { tempAuthCode },
      operation: operation
    })
  }
}

export default new DataGateway()
