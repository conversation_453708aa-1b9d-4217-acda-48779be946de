/*
 * @Description: 列表转换数据
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 11:53:50
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-01-29 16:40:01
 */

import { BatchReturnOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { PaymentMethodEnum } from '@api/service/management/trade/batch/order/enum/PaymentMethod'

export default class RefundCheckAccountListResponse {
  /**
   * 批次单号
   */
  orderId?: string
  /**
   * 交易流水号
   */
  batchId?: string
  /**
   * 退款单号
   */
  refundId?: string
  /**
   * 退款成功时间
   */
  startDate?: string
  /**
   * 购买人信息 - ID
   */
  userId?: string
  /**
   * 购买人信息 - 购买人
   */
  name?: string
  /**
   * 购买人信息 - 证件号
   */
  idCard?: string
  /**
   * 购买人信息 - 手机号
   */
  phone?: string
  /**
   * 实付金额
   */
  money?: number
  /**
   * 退款人数
   */
  refundCount?: number
  /**
   * 销售渠道
   */
  saleChannel: number
  /**
   * 缴费方式
   */
  paymentMethod: PaymentMethodEnum = null

  static from(batchReturnOrderResponse: BatchReturnOrderResponse) {
    //
    const refundData = new RefundCheckAccountListResponse()
    refundData.orderId = batchReturnOrderResponse.batchOrderInfo.batchOrderNo
    refundData.batchId = batchReturnOrderResponse.batchOrderInfo.paymentInfo.flowNo
    refundData.refundId = batchReturnOrderResponse.batchReturnOrderNo
    refundData.startDate = batchReturnOrderResponse.basicData.batchReturnOrderStatusChangeTime.returnedAndRefunded
    refundData.refundCount = batchReturnOrderResponse.basicData.returnOrderCount
    refundData.userId = batchReturnOrderResponse.batchOrderInfo.creator.userId
    refundData.money = batchReturnOrderResponse.basicData.refundAmount
    refundData.saleChannel = batchReturnOrderResponse.basicData.saleChannel
    refundData.paymentMethod = batchReturnOrderResponse.batchOrderInfo.paymentInfo.paymentOrderType
    return refundData
  }
  serUserInfo(idCard: string, name: string, phone: string) {
    this.idCard = idCard
    this.name = name
    this.phone = phone
  }
}
