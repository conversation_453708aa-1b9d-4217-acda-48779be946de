import DateScope from '@api/service/common/models/DateScope'
import { ReportingTypeEnum } from '@api/service/common/implement/enums/ReportingTypeEnum'
import MsSchemeLearningQueryFront, {
  SchemeIssueConfigResponse,
  SchemeIssueResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import MsTradeQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import {
  ComplexSkuPropertyResponse,
  SkuPropertyConvertUtils
} from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
import TrainClassConfigJsonManager from '@api/service/management/train-class/Utils/TrainClassConfigJsonManager'
import issueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
import QuestionnaireImplementItem from '@api/service/management/implement/models/QuestionnaireImplementItem'
import MsExamQueryFront, {
  GetQuestionnaireInIssueRequest,
  SurveyInformationResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'
import { Page, ResponseStatus } from '@hbfe/common'
import MsSchemeQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import ExtendProperty from '@api/service/common/scheme/model/schemeDto/common/extend-properties/ExtendProperty'

export default class PeriodImplementBase {
  /*
   * 期别id
   */
  id: string = undefined

  /**
   * 方案id
   */
  schemeId: string = undefined

  /**
   * 方案信息
   */
  schemeConfig: TrainClassDetailClassVo = new TrainClassDetailClassVo()

  /*
   * 期别编号
   */
  no: string = undefined

  /*
   * 期别名称
   */
  name: string = undefined

  /*
   * 是否开启报到
   */
  isSetReportConfig: boolean = undefined

  /*
   * 报道形式
   */
  reportingType: ReportingTypeEnum = undefined

  /*
   * 培训时间
   */
  trainingTime: DateScope = new DateScope()

  /*
   * 报到时间
   */
  checkInTime: DateScope = new DateScope()

  /*
   * 是否开启考勤
   */
  isSetAttendanceConfig: boolean = undefined

  /*
   * 是否开启问卷
   */
  hasQuestionnaire: boolean = undefined

  /*
   * 可报名人数
   */
  applicantsNumber: number = undefined

  /*
   * 报名起止时间
   */
  applicationData: DateScope = new DateScope()

  /**
   * 方案期别配置信息（方案内上浮）
   */
  schemePeriodConfig: issueConfigDetail = new issueConfigDetail()

  /**
   * 方案是否开启心得（方案内上浮）
   */
  learningExperienceOpen = false

  /**
   * 问卷列表 （需调用 getQuestionnaireInfo 查询填充）
   */
  questionnaireList: Array<QuestionnaireImplementItem> = new Array<QuestionnaireImplementItem>()

  /**
   *
   * @param periodId 期别id
   */
  constructor(periodId?: string) {
    if (periodId) {
      this.id = periodId
    }
  }

  /**
   * 获取培训方案类型
   */
  static async getSchemeType(schemeId: string) {
    const res = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInServicer({
      schemeId: schemeId,
      needField: ['extendProperties']
    })

    if (res?.data?.schemeConfig) {
      const schemeInfo = JSON.parse(res.data.schemeConfig)

      const extendProperties = schemeInfo.extendProperties as Array<ExtendProperty>

      if (schemeInfo.extendProperties?.length) {
        const schemeType: any = extendProperties.find((item: ExtendProperty) => {
          return item.name == 'trainingWay'
        })

        if (schemeType) {
          return schemeType
        }
      }
    }

    return ''
  }

  /**
   * 获取方案心得开关
   * @param schemeId 方案id
   */
  static async getLearningExperienceOpen(schemeId: string) {
    const res = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInServicer({
      schemeId: schemeId
    })

    if (res?.data?.schemeConfig) {
      const learningType = TrainClassConfigJsonManager.jsonConfigConvertToLearningType(res.data.schemeConfig)

      return !!learningType.learningExperience?.isSelected
    } else {
      return false
    }
  }

  /**
   * 详情转换方法
   * @param dto
   */
  async fromSchemeIssueConfig(dto: SchemeIssueConfigResponse) {
    const { schemeConfig, issueConfig } = dto
    if (schemeConfig) {
      this.schemeConfig.learningTypeModel = TrainClassConfigJsonManager.jsonConfigConvertToLearningType(
        schemeConfig.schemeConfig
      )
      this.learningExperienceOpen = !!this.schemeConfig.learningTypeModel.learningExperience?.isSelected
      this.schemeId = schemeConfig.schemeId
      this.schemeConfig.trainClassBaseInfo.id = schemeConfig.schemeId
      this.schemeConfig.trainClassBaseInfo.name = schemeConfig.name
      const schemeDtoObj = JSON.parse(schemeConfig.schemeConfig) as any
      this.schemeConfig.commoditySkuId = schemeDtoObj?.commoditySale?.id
      // 拿到班级培训结束时间而不是期别的
      this.schemeConfig.trainClassBaseInfo.trainingEndDate = schemeDtoObj?.trainingEndDate
      /**
       * todo 原先方案json的sku并没有开公共的转换方法，但商品的Sku有，故这边取巧，取到方案json里的商品id取到商品信息转换sku
       */
      if (this.schemeConfig.commoditySkuId) {
        const res = await MsTradeQueryFrontGatewayCourseLearningBackstage.getCommoditySkuInServicer(
          this.schemeConfig.commoditySkuId
        )
        if (res?.data) {
          const skuProperty = await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
            res.data.skuProperty as ComplexSkuPropertyResponse
          )

          this.schemeConfig.trainClassBaseInfo.skuProperty = skuProperty
        }
      }
      const findSchemePeriodConfig = this.schemeConfig.learningTypeModel.issue.issueConfigList.find(
        (item: issueConfigDetail) => item.id === this.id
      )
      if (findSchemePeriodConfig) {
        this.schemePeriodConfig = findSchemePeriodConfig
        this.isSetReportConfig = findSchemePeriodConfig.isOpenCheck
        this.isSetAttendanceConfig = findSchemePeriodConfig.isOpenAttendance
      }
    }

    if (issueConfig?.length) {
      const currentIssue = issueConfig.find((it: SchemeIssueResponse) => it.issueId == this.id)
      if (currentIssue) {
        this.name = currentIssue.issueName
        this.no = currentIssue.issueNum
        this.trainingTime.begin = currentIssue.startTrainTime
        this.trainingTime.end = currentIssue.endTrainTime
        this.checkInTime.begin = currentIssue.startReportTimePeriod
        this.checkInTime.end = currentIssue.endReportTimePeriod
        this.applicationData.begin = currentIssue.startSignUpTime
        this.applicationData.end = currentIssue.endSignUpTime
      }
    }
  }
  /**
   * 根据期别id获取期别详情
   */
  async getPeriodConfigById() {
    const res = await MsSchemeLearningQueryFront.getSchemeIssueConfigInServicer(this.id)

    if (res.data) {
      await this.fromSchemeIssueConfig(res.data)
    }
  }

  /**
   * 获取当前期别是否有学员报名
   */
  async getPeriodHasStudentRegister() {
    const res = await MsSchemeLearningQueryFront.getIssueConfigureInfoInServicer(this.id)

    if (res?.data) {
      return !!res.data.signUpNum
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   * 获取问卷信息
   */
  async getQuestionnaireInfo() {
    const request = new GetQuestionnaireInIssueRequest()
    request.issueId = this.id
    const page = new Page(1, 200)
    const resultList = new Array<SurveyInformationResponse>()
    const res = await MsExamQueryFront.pageIssueQuestionnaireInServicer({
      page,
      request
    })
    if (res?.data?.currentPageData?.length) {
      resultList.push(...res.data.currentPageData)
      if (res.data.totalPageSize > 1) {
        for (let i = 1; i < res.data.totalPageSize; i++) {
          page.pageNo += 1
          const res = await MsExamQueryFront.pageIssueQuestionnaireInServicer({
            page,
            request
          })
          if (res?.data?.currentPageData?.length) {
            resultList.push(...res.data.currentPageData)
          }
        }
      }
    }

    this.questionnaireList = resultList.map((item: SurveyInformationResponse) => {
      return QuestionnaireImplementItem.fromScanning(item)
    })
  }
}
