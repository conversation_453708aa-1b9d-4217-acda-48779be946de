import ChooseCourseVo from '@api/service/customer/learning/choose-course/mutation/vo/ChooseCourseVo'
import CalculatorObj from '@api/service/customer/train-class/Utils/CalculatorObj'
import WaitChooseCourseTreeCacheVo from './WaitChooseCourseTreeCacheVo'

/**
 * @description 已选待提交课程树模型
 */
class SelectedCourseTreeVo {
  /**
   * 二级课程学习大纲编号
   */
  outlineId: string

  /**
   * 二级课程学习大纲名称
   */
  outlineName: string

  /**
   * 学时
   */
  outlinePeriod: number

  /**
   * 已选课程列表
   */
  selectedList: Array<ChooseCourseVo>
  // selectedList: Array<SelectedCourseVo>

  static from(WaitChooseCourseTreeCache: WaitChooseCourseTreeCacheVo, selectedCourseList: Array<ChooseCourseVo>) {
    const waitChooseCourseWithOutline = new SelectedCourseTreeVo()
    waitChooseCourseWithOutline.outlineId = WaitChooseCourseTreeCache.outlineId
    waitChooseCourseWithOutline.outlineName = WaitChooseCourseTreeCache.outlineName
    waitChooseCourseWithOutline.selectedList = selectedCourseList
    waitChooseCourseWithOutline.outlinePeriod = selectedCourseList?.reduce(
      (total, secondCourse) => (total = CalculatorObj.add(total, secondCourse.floatPeriod)),
      0
    )
    return waitChooseCourseWithOutline
  }
}

export default SelectedCourseTreeVo
