schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""创建考勤模板"""
	createAttendanceTemplate(request:CreateAttendanceTemplateRequest):Void
	"""删除考勤模板"""
	deleteAttendanceTemplate(attendanceTemplateId:String):Void
	"""更新考勤模板"""
	updateAttendanceTemplate(request:UpdateAttendanceTemplateRequest):Void
}
"""创建考勤模板请求
	<AUTHOR>
	@since 2024/12/10
"""
input CreateAttendanceTemplateRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.CreateAttendanceTemplateRequest") {
	"""签到点信息"""
	attendanceSignIn:AttendanceSignDto
	"""签退点信息"""
	attendanceSignOut:AttendanceSignDto
}
"""更新考勤模板请求
	<AUTHOR>
	@since 2024/12/10
"""
input UpdateAttendanceTemplateRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.UpdateAttendanceTemplateRequest") {
	"""id"""
	attendanceId:String
	"""签到点信息"""
	attendanceSignIn:AttendanceSignDto
	"""签退点信息"""
	attendanceSignOut:AttendanceSignDto
}
"""<AUTHOR>
	@since
"""
input AttendanceSignDto @type(value:"com.fjhb.platform.jxjy.v1.kernel.service.dto.AttendanceSignDto") {
	"""是否开启"""
	enable:Boolean!
	"""签到频率
		半天  1;
		每节课  2;
		@see SignFrequencyTypes
	"""
	frequency:Int!
	"""签到半径"""
	radius:Double!
	"""签到开始前
		单位:(秒)
	"""
	beforeSecond:Int!
	"""开始后
		单位:(秒)
	"""
	afterSecond:Int!
}

scalar List
