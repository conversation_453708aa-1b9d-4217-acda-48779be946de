/**
 * 最后学习的课程
 */
import CourseTeacher from '@api/service/common/models/course/CourseTeacher'

class LastLearnCourse {
  /**
   * 课程编号
   */
  id = ''
  /**
   * 课程包id
   */
  poolId = ''
  /**
   * 课程名称
   */
  name = ''
  /**
   * 课程图片
   */
  iconPath = ''
  /**
   * 课程教师信息
   */
  teacherList: Array<CourseTeacher> = new Array<CourseTeacher>()
  /**
   * 课程进度
   */
  schedule = 0
  /**
   * 所属课程类型
   * 1:必修课；2：选修；
   */
  courseType: number

  /**
   * 上次学习时间
   */
  lastStudyTime: Date

  getTeacherText() {
    if (this.teacherList) {
      let text = ''
      for (const teacher of this.teacherList) {
        if (text === '') {
          text = teacher.name
        } else {
          text = text + '、' + teacher.name
        }
      }
    }
    return ''
  }
}

export default LastLearnCourse
