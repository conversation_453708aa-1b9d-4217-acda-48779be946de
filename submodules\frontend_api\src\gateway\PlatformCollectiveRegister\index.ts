import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/PlatformCollectiveRegister'

// 枚举
export enum BatchOrderState {
  beginning = 'beginning',
  addingOrder = 'addingOrder',
  commited = 'commited',
  paying = 'paying',
  tradeClosing = 'tradeClosing',
  payFail = 'payFail',
  paySuccess = 'paySuccess',
  delivering = 'delivering',
  deliverySuccess = 'deliverySuccess',
  tradeSuccess = 'tradeSuccess',
  tradeClose = 'tradeClose'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 集体缴费报名订单查询参数
<AUTHOR> create 2020/8/11 17:42
 */
export class CollectiveregisterQueryDto {
  /**
   * 批次号
   */
  batchNo?: string
  /**
   * 交易状态
@see BatchOrderState
   */
  orderStatus?: Array<string>
  /**
   * 是否需要发票
   */
  needInvoice: number
  /**
   * 1 线上 2 线下
   */
  firstType: number
  /**
   * 提交批次单时间段- 开始
   */
  commitStartTime?: string
  /**
   * 提交批次单时间段- 结束
   */
  commitEndTime?: string
  /**
   * 创建批次单时间段- 开始
   */
  createStartTime?: string
  /**
   * 创建批次单时间段- 结束
   */
  createEndTime?: string
  /**
   * 单位id
   */
  unitId?: string
  /**
   * 是否排除 支付中并且是线下收款的订单
true 排除
false 不排除
   */
  exsitPayingOffline?: boolean
}

/**
 * 集体报名创建信息
 */
export class CollectiveRegisterCreateInfo {
  /**
   * 文件路径
   */
  filePath?: string
  /**
   * 文件名称
   */
  fileName?: string
}

/**
 * 集体报名记录查询条件
 */
export class CollectiveRegisterRecordQuery {
  /**
   * 是否允许报名
   */
  isAllowRegister?: boolean
  /**
   * 学员姓名
   */
  userName?: string
  /**
   * 身份证号码
   */
  identity?: string
  /**
   * 手机号码
   */
  phone?: string
  /**
   * 培训班
   */
  schemeId?: string
}

export class BatchOrderCancelDTO {
  batchNo: string
  reasonId: string
  reasonDescription: string
}

/**
 * 集体缴费任务的详细信息
<AUTHOR> create 2020/8/18 14:21
 */
export class CollectiveTaskInfo {
  /**
   * 任务对应的批次号
   */
  batchNo: string
  /**
   * 最新任务的创建时间
   */
  creatTime: string
  /**
   * 最新任务的id
   */
  taskId: string
  /**
   * 最新任务的导入的状态
初始状态 1:批次任务创建完成
@see CollectiveOrderImportStatus
   */
  taskImportStatus: number
  /**
   * 最新任务完成时间
没完成是null
   */
  completeTime: string
  /**
   * 批次单下 所有任务的成功条数
   */
  totalSuccessCount: number
  /**
   * 批次单下 所有任务的失败条数
   */
  totalFaildCount: number
  /**
   * 批次单下 所有任务的总条数
   */
  totalCount: number
  /**
   * 最新任务下的总条数
   */
  currentTotalCount: number
  /**
   * 最新任务当前执行到的条数
   */
  currentCount: number
  /**
   * 最新任务执行失败的条数
   */
  failCount: number
  /**
   * 最新任务执行成功的条数
只要第一步导入成功就算成功，不需要真正下订单
   */
  successCount: number
}

/**
 * 集体缴费订单返回值
<AUTHOR> create 2020/8/11 17:49
 */
export class CollectiveregisterOrderDto {
  /**
   * 批次订单号
   */
  no: string
  /**
   * 批次单总金额(该字段会在批次单提交后计算总金额)
   */
  totalMoney: number
  /**
   * 批次单报名总人数
   */
  signUpCount: number
  /**
   * 批次单状态
   */
  state: BatchOrderState
  /**
   * 支付方式1:线上,2:线下
   */
  payType: number
  /**
   * 创建方式
1:系统创建
2:用户创建
3:管理员创建
4:历史迁移
5:外部接口
   */
  createType: number
  /**
   * 创建人id
   */
  creatorId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 开始追加订单时间
   */
  startAddOrderTime: string
  /**
   * 提交时间
   */
  commitTime: string
  /**
   * 去支付时间
   */
  preparePayTime: string
  /**
   * 支付完成时间
   */
  payFinishTime: string
  /**
   * 发货完成时间
   */
  deliverySuccessTime: string
  /**
   * 交易完成时间,如果最终是交易成功就是交易成功时间,如果最终是交易关闭就是交易关闭时间
   */
  tradeFinishTime: string
  /**
   * 最后一次更新时间
   */
  lastUpdateTime: string
  /**
   * 收款账户ID
   */
  receiveAccountId: string
  /**
   * 备注
   */
  remark: string
  /**
   * 自动关闭时间
   */
  autoCloseTime: string
  /**
   * 是否为测试数据
   */
  test: boolean
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 汇款凭证地址
   */
  paths: Array<string>
}

/**
 * 集体缴费报名人次统计接口
<AUTHOR> create 2020/8/12 14:38
 */
export class CollectiveregisterSignUpCountDto {
  /**
   * 报名人次
   */
  signUpCount: number
  /**
   * 批次单号
   */
  batchNo: string
}

/**
 * 集体缴费内的订单信息
<AUTHOR> create 2020/8/12 20:47
 */
export class OrderInBatchInfo {
  /**
   * 批次号
   */
  batchNo: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 买家ID
   */
  buyerId: string
  /**
   * 订单总金额
   */
  totalAmount: number
  /**
   * 订单创建时间
   */
  createTime: string
  /**
   * 订单交易完成时间
   */
  completeTime: string
  /**
   * 主订单状态
   */
  orderStatus: number
  /**
   * 子订单列表
   */
  subOrderList: Array<SubOrderItemDTO>
}

/**
 * 订单的发票信息
 */
export class BillOrderInfoDTO {
  /**
   * 主键 主订单号
非批次订单才有值
   */
  orderNo: string
  /**
   * 买家ID
非批次订单才有值
   */
  buyerId: string
  /**
   * 发票号
   */
  billNo: string
  /**
   * 主键 发票ID
   */
  id: string
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型 1：个人 2：企业
   */
  titleType: string
  /**
   * 统一社会信用代码
   */
  taxpayerNo: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 账号
   */
  account: string
  /**
   * 发票类型：1普通发票，2增值税普通发票，3增值税专用发票
   */
  invoiceType: string
  /**
   * 是否测试数据
   */
  test: boolean
  /**
   * 发票状态 com.fjhb.platform.core.v1.bill.api.constants.InvoiceBillState
未开票： 0
开票中： 1
开票成功： 2
开票失败： 3
   */
  state: string
  /**
   * 红票状态
未开票： 0
开票中： 1
开票成功： 2
开票失败： 3
   */
  redState: string
  /**
   * 电子邮件
   */
  email: string
  /**
   * 发票代码
   */
  billCode: string
  /**
   * 发票验证码
   */
  billVeriCode: string
  /**
   * 用于接收通知的电话
   */
  noticePhone: string
  /**
   * 是否冻结
   */
  frozen: boolean
  /**
   * 开票金额
   */
  money: number
  /**
   * 发票对应的批次号
批次订单才有值
   */
  batchNo: string
  /**
   * 创建时间
   */
  createTime: string
}

export class SubOrderItemDTO {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 商品ID
   */
  commodityId: string
  /**
   * 商品名称
   */
  commodityName: string
  /**
   * 商品图片地址
   */
  photoPath: string
  /**
   * 商品规格
   */
  specification: string
  /**
   * 是否为虚拟物品
   */
  virtualGoods: boolean
  /**
   * 商品标价
   */
  labelPrice: number
  /**
   * 成交单价
   */
  dealPrice: number
  /**
   * 购买数量
   */
  purchaseQuantity: number
  /**
   * 子订单实付总价
   */
  totalAmount: number
  /**
   * 子订单状态
待付款 1
未发货 2
发货中 3
已发货 4
买家已签收 5
已换货 6
退货中 7
已退货 8
已取消 9
发货失败 10
已退款(指退货并退款) 11
退货时商品回收失败 12
退货中 13
换货单退货中 14
换货单退货中 15
换货单退货中 16
退货单退款失败 17
退货不退款成功 18
退款不退货成功 19
   */
  orderStatus: number
  /**
   * 是否需要发票
   */
  needBill: boolean
}

/**
 * 集体报名记录
 */
export class CollectiveRegisterRecord {
  /**
   * 集体报名记录ID
   */
  id: string
  /**
   * 集体报名单号
   */
  batchNo: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 用户手机
   */
  phone: string
  /**
   * 用户身份证
   */
  identity: string
  /**
   * 人员类别
   */
  userCategory: string
  /**
   * 开通的学习方案名称
   */
  schemeName: string
  /**
   * 开通的期数名称
   */
  issueName: string
  /**
   * 是否进行报名
   */
  register: boolean
  /**
   * 用户ID
   */
  userId: string
  /**
   * 销售渠道ID
   */
  marketingChannelId: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 商品id
   */
  commoditySkuId: string
  /**
   * 班级id
   */
  schemeId: string
  /**
   * 错误信息
   */
  errorMessage: string
}

export class BatchOrderDTO {
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  organizationId: string
  no: string
  status: number
  totalMoney: number
  needInvoice: boolean
  invoiceInfo: InvoiceRequestDTO
  receiveAccountId: string
  commitTime: string
  tradeFinishTime: string
  createType: number
  payType: number
  creatorId: string
  createTime: string
}

export class InvoiceRequestDTO {
  title: string
  titleType: number
  type: number
  taxpayerNo: string
  address: string
  phone: string
  bankName: string
  account: string
  email: string
  remark: string
  objectList: Array<InvoiceObjDTO>
  electron: boolean
  noTaxBill: boolean
}

export class InvoiceObjDTO {
  objectType: string
  objectId: string
}

export class CollectiveregisterOrderDtoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CollectiveregisterOrderDto>
}

export class OrderInBatchInfoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<OrderInBatchInfo>
}

export class CollectiveRegisterRecordPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CollectiveRegisterRecord>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findBatchOrderPage(
    params: { page?: Page; query?: CollectiveregisterQueryDto },
    query: DocumentNode = GraphqlImporter.findBatchOrderPage,
    operation?: string
  ): Promise<Response<CollectiveregisterOrderDtoPage>> {
    return commonRequestApi<CollectiveregisterOrderDtoPage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 查询批次单的任务状态信息
   * @return
   * @param query 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findLastRecordByBatch(
    batchNo: string,
    query: DocumentNode = GraphqlImporter.findLastRecordByBatch,
    operation?: string
  ): Promise<Response<CollectiveTaskInfo>> {
    return commonRequestApi<CollectiveTaskInfo>(SERVER_URL, {
      query: query,
      variables: { batchNo },
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findOrderListByBatchNo(
    params: { batchNo?: string; page?: Page },
    query: DocumentNode = GraphqlImporter.findOrderListByBatchNo,
    operation?: string
  ): Promise<Response<OrderInBatchInfoPage>> {
    return commonRequestApi<OrderInBatchInfoPage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 集体报名订单详情
   * @return
   * @param query 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBatchOrder(
    batchNo: string,
    query: DocumentNode = GraphqlImporter.getBatchOrder,
    operation?: string
  ): Promise<Response<CollectiveregisterOrderDto>> {
    return commonRequestApi<CollectiveregisterOrderDto>(SERVER_URL, {
      query: query,
      variables: { batchNo },
      operation: operation
    })
  }

  /**   * 获取批次订单号集合获取批次订单的发票信息
   * @param query 查询 graphql 语法文档
   * @param batchNos 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBillByOrderNo(
    batchNos: Array<string>,
    query: DocumentNode = GraphqlImporter.getBillByOrderNo,
    operation?: string
  ): Promise<Response<Array<BillOrderInfoDTO>>> {
    return commonRequestApi<Array<BillOrderInfoDTO>>(SERVER_URL, {
      query: query,
      variables: { batchNos },
      operation: operation
    })
  }

  /**   * 根据批次订单号查询批次订单状态
   * @param batchNo
   * @return
   * @param query 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCollectiveOrderStatus(
    batchNo: string,
    query: DocumentNode = GraphqlImporter.getCollectiveOrderStatus,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: query,
      variables: { batchNo },
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCollectiveRegisterRecord(
    params: { page?: Page; batchNo?: string; query?: CollectiveRegisterRecordQuery },
    query: DocumentNode = GraphqlImporter.getCollectiveRegisterRecord,
    operation?: string
  ): Promise<Response<CollectiveRegisterRecordPage>> {
    return commonRequestApi<CollectiveRegisterRecordPage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 根据批次号集合获取批次下报名人数信息
   * @param batchNos
   * @return
   * @param query 查询 graphql 语法文档
   * @param batchNos 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSignUpCountByBatchNos(
    batchNos: Array<string>,
    query: DocumentNode = GraphqlImporter.getSignUpCountByBatchNos,
    operation?: string
  ): Promise<Response<Array<CollectiveregisterSignUpCountDto>>> {
    return commonRequestApi<Array<CollectiveregisterSignUpCountDto>>(SERVER_URL, {
      query: query,
      variables: { batchNos },
      operation: operation
    })
  }

  /**   * 预提交批次订单
   * @param batchNo 批次单号
   * @return 不能报名的记录
   * @param query 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async preCommitCollectiveRegister(
    batchNo: string,
    query: DocumentNode = GraphqlImporter.preCommitCollectiveRegister,
    operation?: string
  ): Promise<Response<Array<CollectiveRegisterRecord>>> {
    return commonRequestApi<Array<CollectiveRegisterRecord>>(SERVER_URL, {
      query: query,
      variables: { batchNo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param cancelInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelBatchOrder(
    cancelInfo: BatchOrderCancelDTO,
    mutate: DocumentNode = GraphqlImporter.cancelBatchOrder,
    operation?: string
  ): Promise<Response<BatchOrderDTO>> {
    return commonRequestApi<BatchOrderDTO>(SERVER_URL, {
      query: mutate,
      variables: { cancelInfo },
      operation: operation
    })
  }

  /**   * 提交报名
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async commitCollectiveRegister(
    batchNo: string,
    mutate: DocumentNode = GraphqlImporter.commitCollectiveRegister,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { batchNo },
      operation: operation
    })
  }

  /**   * 导入集体报名
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCollectiveRegister(
    createInfo: CollectiveRegisterCreateInfo,
    mutate: DocumentNode = GraphqlImporter.createCollectiveRegister,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { createInfo },
      operation: operation
    })
  }

  /**   * 删除报名订单
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async doDelCollectiveRegister(
    batchNo: string,
    mutate: DocumentNode = GraphqlImporter.doDelCollectiveRegister,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { batchNo },
      operation: operation
    })
  }

  /**   * 移除批次订单中的部分订单
   * @param batchNo
   * @param recordIds
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeCollectiveRegister(
    params: { batchNo?: string; recordIds?: Array<string> },
    mutate: DocumentNode = GraphqlImporter.removeCollectiveRegister,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 存在批次订单的情况下，再次
   * 导入集体报名
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCollectiveRegister(
    params: { createInfo?: CollectiveRegisterCreateInfo; batchNo?: string },
    mutate: DocumentNode = GraphqlImporter.updateCollectiveRegister,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }
}

export default new DataGateway()
