import { StudentCourseQuiz } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

class StudentAfterCourseTest {
  /**
   * 课后测验id
   */
  id: string
  /**
   * 课后测验学习状态（0：未评定 1：未合格 2：合格）
   */
  status: number
  /**
   * 课后测验答卷id
   */
  answerId: string
  /**
   * 课后测验得分
   */
  score: number
  /**
   * 课后测验取得结果时间
   */
  learningResultTime: string

  isQualified() {
    return this.status === 2
  }

  static from(quiz: StudentCourseQuiz) {
    const test = new StudentAfterCourseTest()
    if (!quiz) {
      return test
    }
    test.id = quiz.courseQuizId
    test.status = quiz.courseQuizStatus
    test.answerId = quiz.courseQuizAnswerId
    test.score = quiz.score
    test.learningResultTime = quiz.learningResultTime
    return test
  }
}

export default StudentAfterCourseTest
