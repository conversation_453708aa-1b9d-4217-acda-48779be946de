import RootModule from '@/store/RootModule'
import { ImageCaptchaTrack } from '@api/ms-gateway/ms-identity-authentication-v1'
import AuthModule from '@api/service/common/auth/AuthModule'
import Authentication from '@api/service/common/authentication/Authentication'
import { AccountType, SendMessageParams } from '@api/service/common/authentication/interfaces/LoginParams'
import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
import { DistributionServiceTypeEnum } from '@api/service/common/capability-service-config/enum/DistributionServiceTypeEnum'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
import MutationRegisterAndLogin from '@api/service/management/online-school-config/functionality-setting/mutation/MutationRegisterAndLogin'
import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
import OnlineSchoolModule from '@api/service/training-institution/online-school/OnlineSchoolModule'
import { BusinessTypeEnum } from '@hbfe-biz/biz-authentication/dist/enums/BusinessTypeEnum'
import GrantType from '@hbfe-biz/biz-authentication/dist/enums/GrantType'
import { IdentityType } from '@hbfe-biz/biz-authentication/dist/enums/IdentityType'
import LoginParams from '@hbfe-biz/biz-authentication/dist/models/LoginParams'
import { CaptchaApplyRequest, SmsCodeApplyRequest } from '@hbfe-ms/ms-basicdata-domain-gateway'
import { CurrentAccountChangePasswordCauseForgetRequest } from '@hbfe-ms/ms-basicdata-domain-gateway/src'
import UnitDialog from '@hbfe/jxjy-admin-authentication/src/login/components/unit-dialog.vue'
import BizSlider from '@hbfe/jxjy-admin-components/src/biz/biz-slider.vue'
import { ElForm } from 'element-ui/types/form'
import { Component, Ref, Vue, Watch } from 'vue-property-decorator'

export class LoginResult {
  code: number
  message: string

  constructor(code: number, message: string) {
    this.code = code
    this.message = message
  }

  isSuccess(): boolean {
    return this.code === 200
  }
}

export enum RoleType {
  ADMIN = 1,
  SERVICEPROVIDER = 2,
  REGIONADMINISTRATOR = 3,
  DISTRIBUTOR = 4,
  SPECIALSUBJECT = 5
}

export enum SendSmsType {
  STUDENT = 'student',
  ADMIN = 'admin',
  PROVIDER = 'provider',
  REGION = 'region'
}

const countDownTime = 60

@Component
class LoginCore extends Vue {
  @Ref('loginForm') loginForm: ElForm
  @Ref('loginFormSec') loginFormSec: ElForm
  @Ref('checkPassworRef') checkPassworRef: any

  checkPassword = {
    password: '',
    againPassword: ''
  }
  checkPasswordRules = {
    password: [
      { required: true, message: '请输入8~18位由数字、字母或符号组成的密码', trigger: 'blur' },
      {
        message: '请输入8~18位由数字、字母或符号组成的密码',
        max: 18,
        min: 8,
        validator: this.validatePassword,
        trigger: 'blur'
      }
    ],
    againPassword: {
      required: true,
      max: 18,
      min: 8,
      validator: this.validateRepeatPassword,
      trigger: 'blur'
    }
  }
  success = 5
  dialogVisible3 = false
  $authentication: Authentication

  mutationRegisterAndLogin: MutationRegisterAndLogin =
    OnlineSchoolConfigModule.mutationFunctionalitySettingFactory.registerAndLogin

  // 判断当前登录角色是否是分销商
  isFxlogin = QueryManagerDetail?.hasCategory(CategoryEnums.fxs)
  validateCodePic = ''
  passwordLogin = false
  // 验证码发送
  loginSending = true
  // 预计倒计时时间
  count = 60
  // 发送后倒计时计时
  countTime = 60
  dialog1 = false
  passwordDialog = false
  // 实为布尔值，此处赋值帐号值。用来解决记住帐号和密码后，图形验证码无法输入问题
  isPhoneNumberValid = false
  getMessageCoding = false
  // 发送后倒计时计时
  countDownTime = countDownTime
  applyingCaptcha = false
  rememberPassword = false
  loginIng = false
  loginResult: LoginResult = new LoginResult(200, '')
  formData = {
    roleType: RoleType.ADMIN,
    account: '',
    password: '',
    accountType: 2,
    extAttributes: '',
    // 图形验证码
    captcha: '',
    phoneCaptcha: ''
  }
  timer = 0
  phoneNumber = ''
  continueLoading = false
  smsToken = ''

  intervalTimer: any = null

  isCaptchaValid = false
  rules = {}
  // 分销单位组件
  isShow = false

  sureTime = 5
  @Ref('changePhoneRef')
  changePhoneRef: any
  @Ref('unitDialogRef')
  unitDialogRef: UnitDialog
  // 应用方类型
  applicationMemberType: number = null
  // 应用方id
  applicationMemberId = ''
  chainToken = ''

  changePhoneLoading = false
  isPhoneCaptchaValid = true
  phoneCaptchaVaildFlag = false
  changePhoneFrom = {
    phone: '',
    smsCode: '',
    captcha: ''
  }
  enableSms = false
  enableRisk = false
  // 验证码发送
  sending = true
  validatePic = ''
  result: any = {}

  /**
   * 验证返回的token
   */
  sliderCaptchToken = ''

  /**
   * 验证返回的data
   */
  sliderCaptchData = new ImageCaptchaTrack()

  activeName = 'first'

  @Watch('activeName')
  paneChange() {
    if (this.activeName === 'first') {
      this.refreshValidateCodePic()
    }
  }

  /**
   * 获取是否为滑块验证
   */
  onlineSchoolModule = new OnlineSchoolModule()

  localToke = ''
  showSmsModal = false

  /**
   * 是否登录
   */
  isUseSlider = false

  stopTimerCount() {
    window.clearInterval(this.timer)
  }
  /**
   * 密码验证,必须数字和字母，特殊符号可有可无
   */
  validatePassword(rule: any, value: any, callback: any) {
    const reg = new RegExp('^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z\\W]{8,18}$')
    if (value === '') {
      callback(new Error('请输入密码'))
    } else {
      reg.test(value) ? callback() : callback(new Error('请输入8~18位由数字、字母或符号组成的密码'))
    }
  }
  /**
   * / 确定密码验证
   */
  validateRepeatPassword(rule: any, value: any, callback: any) {
    const reg = new RegExp('^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z\\W]{8,18}$')
    if (value === '') {
      callback(new Error('请再次输入密码'))
    } else if (value !== this.checkPassword.password) {
      callback(new Error('两次输入密码不一致'))
    } else {
      reg.test(value) ? callback() : callback(new Error('请输入8~18位由数字、字母或符号组成的密码'))
    }
  }

  stopIntervalTimer() {
    if (this.intervalTimer) {
      clearTimeout(this.intervalTimer)
      this.intervalTimer = null
    }
  }
  // 去登录
  async operationSuccessful(phoneData: any) {
    this.loginIng = true
    const params = new LoginParams()
    if (this.enableSms) {
      params.grantType = GrantType.identity_chain_auth_token
    }

    try {
      params.smsCode = phoneData.smsCode
      params.token = phoneData.token
      // todo
      let res
      if (this.formData.roleType == RoleType.DISTRIBUTOR) {
        res = await this.$authentication.doFxLogin(IdentityType.sms_code, params)
      } else {
        params.grantType = GrantType.identity_auth_token
        res = await this.$authentication.doLogin(IdentityType.sms_code, params)
      }
      if (res?.status?.code === 200) {
        const response = (await this.$authentication.ssoAuth()) as any
        if (response.code !== 200 || !response.data?.access_token) {
          this.$message.error(res?.data?.message)
          this.loginIng = false
          return
        }

        // 分销角色登录成功时请求单位列表
        if (this.formData.roleType === RoleType.DISTRIBUTOR) {
          await this.isFxLoginStep()
        }

        this.$emit('login-result', true)
        this.loginIng = false
        await this.loginSuccess()
      } else {
        this.$message.error(res?.data?.message)
        this.loginIng = false
      }
    } catch (e) {
      console.log(e, 'rejecte')
      if (e?.code === 4001) {
        this.$message.error('账号不存在')
      } else {
        const message = e?.message || '登录失败'
        if (message) {
          this.$message.error(message)
        }
      }
      this.loginIng = false
    }
  }

  countDown(time: number) {
    this.countTime = time

    console.log('countStart')
    this.intervalTimer = setInterval(() => {
      if (this.countTime === 0) {
        this.loginSending = true
        clearInterval(this.intervalTimer)
        this.countTime = this.count
      } else {
        this.countTime = this.countTime - 1
      }
    }, 1000)
  }

  async sendMessageWithoutCaptcha() {
    await this.loginFormSec.validate()
    this.loginSending = false
    this.applyingCaptcha = true
    const params = new SmsCodeApplyRequest()
    if (this.formData.roleType === RoleType.SERVICEPROVIDER) {
      //   params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.coursewareSupplierPhoneNumLoginToken)
    } else if (this.formData.roleType === RoleType.REGIONADMINISTRATOR) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionLoginToken)
    } else if (this.formData.roleType === RoleType.DISTRIBUTOR) {
      params.token = ConfigCenterModule.getFrontendApplication(
        frontendApplication.distributionAdministratorPhoneLoginToken
      )
    } else if (this.formData.roleType === RoleType.SPECIALSUBJECT) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.specialAdminPhoneLoginToken)
    } else {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.superPhoneNumLoginToken)
    }
    params.businessType = BusinessTypeEnum.login_account
    params.phone = this.formData.account
    try {
      const res = await this.$authentication.verify.msSendSmsCode(params)
      if (res.data.code === 409) {
        this.$message.error('一个手机号一天只能接收4条短信，请使用帐号登录。')
      } else if (res.data.code === 410) {
        this.$message.error('发送验证码失败')
      } else if (res.data.code === 701) {
        this.$message.error('验证码已发送，请勿重复操作。')
      } else if (res.data.code === 515) {
        this.$message.error('不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)')
      } else if (res.data.code === 400) {
        this.$message.error('token无效')
      } else if (res.data.code === 509) {
        this.$message.error('未绑定手机号')
      } else if (res.data.code === 514) {
        this.$message.error('token中未携带手机号')
      }
      if (res.status.code === 200) {
        this.countDown(this.count)
        return
      } else {
        this.$message.error('发送验证码失败')
        this.loginSending = true
      }
    } catch (e) {
      console.log(e)
      const message = e?.message || '获取验证码失败，'
      this.$message.error(message)
    } finally {
      this.applyingCaptcha = false
    }
  }
  async submit() {
    this.changePhoneLoading = true
    try {
      await this.changePhoneRef.validate()
      const res = await this.$authentication.verify.msValidSmsCode(
        this.changePhoneFrom.smsCode,
        this.changePhoneFrom.phone
      )
      if (res.data.code === 200) {
        let changeRes: any = null
        if (this.enableSms) {
          changeRes = await this.$authentication.account.bindPhoneForCurrentAccount(
            this.chainToken,
            this.changePhoneFrom.phone
          )
        } else {
          changeRes = await this.$authentication.account.bindPhone(this.$authentication.verify.shortMessageCaptchaToken)
        }

        if (changeRes.status.code === 200) {
          if (changeRes?.data?.code === '200') {
            this.dialogVisible3 = true
            this.sureTimeDown(this.sureTime)
            this.dialog1 = false
            this.changePhoneLoading = false
          } else {
            this.$message.error(changeRes?.data?.message)
            await this.refreshValidateCodePicPhone()
            this.changePhoneLoading = false
          }
        } else {
          this.$message.error('绑定失败')
          await this.refreshValidateCodePicPhone()
          this.changePhoneLoading = false
        }
      } else {
        this.$message.error('短信验证码错误')
        await this.refreshValidateCodePicPhone()
        this.changePhoneLoading = false
      }
    } catch (e) {
      await this.refreshValidateCodePicPhone()
      this.$message.error('绑定失败')
      this.changePhoneLoading = false
    }
  }
  /**
   * 获取图片验证码
   */
  async refreshValidateCodePicPhone() {
    this.isPhoneCaptchaValid = true
    this.phoneCaptchaVaildFlag = false
    const params = new CaptchaApplyRequest()
    if (this.formData.roleType === RoleType.SERVICEPROVIDER) {
      // params.token = ConfigCenterModule.getFrontendApplication(
      //   frontendApplication.coursewareSupplierChangePhoneNumToken
      // )
    } else if (this.formData.roleType === RoleType.REGIONADMINISTRATOR) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionChangePhoneNumToken)
    } else if (this.formData.roleType === RoleType.DISTRIBUTOR) {
      params.token = ConfigCenterModule.getFrontendApplication(
        frontendApplication.distributionAdministratorPwdLoginToken
      )
    } else if (this.formData.roleType === RoleType.SPECIALSUBJECT) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.specialAdminChangePhoneNumToken)
    } else {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.superChangePhoneNumToken)
    }
    params.businessType = BusinessTypeEnum.change_binding_phone
    const res = await this.$authentication.verify.applyCaptcha(params)
    if (res.status.code == 200) {
      this.validatePic = `data:image/jpeg;base64,${res?.data?.captcha}`
    }
  }

  // 验证码倒计时
  sureTimeDown(time: number) {
    let timer = time
    const sureTimeDown = setInterval(() => {
      if (timer === 0) {
        clearInterval(sureTimeDown)
        this.dialogVisible3 = false
        this.dialog1 = false
        this.sureTime = 5
        if (this.enableSms) {
          this.doAuthLogin()
        } else {
          window.location.replace(this.$router.resolve('/home').href)
          window.location.reload()
        }
      } else {
        timer--
        this.sureTime = timer
      }
    }, 1000)
  }
  // 获取短信验证码
  async getPhoneCaptcha() {
    this.loginSending = false
    this.countDown(this.count)

    const valStatus = await AuthModule.preValidServicerLogin(this.formData.account)
    if (!valStatus.isSuccess()) {
      const errcode = valStatus.errors[0].code
      //30407 该账号不存在
      // 30408 该帐号无合作机构
      if (errcode == 30407) {
        return this.$message.error('帐号不存在，请确认是否已申请合作')
      } else if (errcode == 30408) {
        return this.$message.error('该帐号已中止合作，请联系合作的培训机构确认')
      } else {
        return this.$message.error(valStatus.getMessage())
      }
    }
    this.applyingCaptcha = true
    const sendMessage = new SendMessageParams()
    sendMessage.phoneNumber = this.formData.account
    sendMessage.accountType = AccountType.admin
    try {
      // this.$authentication.captchaToken = ''
      const result = await this.$authentication.verify.sendMessage(sendMessage)
      this.getMessageCoding = true
      if (result.status.code != 200) {
        this.loginResult = result.status
        return
      }
      this.countDown(this.count)
    } catch (e) {
      console.log(e)
      const message = e?.message || '获取验证码失败，'
      this.$message.warning(message)
    } finally {
      this.applyingCaptcha = false
    }
  }

  async refreshValidateCodePic() {
    const params = new CaptchaApplyRequest()
    if (this.formData.roleType === RoleType.SERVICEPROVIDER) {
      // res = await this.$authentication.applyProviderCaptcha()
    } else if (this.formData.roleType === RoleType.REGIONADMINISTRATOR) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionAdminLoginToken)
      this.smsToken = ConfigCenterModule.getFrontendApplication(frontendApplication.regionAdminLoginToken)
    } else if (this.formData.roleType === RoleType.DISTRIBUTOR) {
      params.token = ConfigCenterModule.getFrontendApplication(
        frontendApplication.distributionAdministratorPwdLoginToken
      )
      this.smsToken = ConfigCenterModule.getFrontendApplication(
        frontendApplication.distributionAdministratorPwdLoginToken
      )
    } else if (this.formData.roleType === RoleType.SPECIALSUBJECT) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.specialAdminPwdLoginToken)
      this.smsToken = ConfigCenterModule.getFrontendApplication(frontendApplication.specialAdminPwdLoginToken)
    } else {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.superLoginToken)
      this.smsToken = ConfigCenterModule.getFrontendApplication(frontendApplication.superLoginToken)
    }
    params.businessType = BusinessTypeEnum.login_account
    const res = await this.$authentication.verify.applyCaptcha(params)
    this.validateCodePic = `data:image/jpeg;base64,${res?.data?.captcha}`
  }

  async doPasswordLogin() {
    const loginParams = new LoginParams()
    loginParams.identity = this.formData.account
    loginParams.password = this.formData.password
    loginParams.longTerm = this.rememberPassword
    loginParams.captchaValue = this.formData.captcha
    loginParams.sliderCaptchaValue = this.sliderCaptchData
    loginParams.accountType = AccountType.admin
    if (this.enableSms) {
      loginParams.grantType = GrantType.identity_chain_auth_token
    }
    if (this.isUseSlider) {
      loginParams.token = this.sliderCaptchToken
    } else {
      loginParams.token = this.$authentication.verify.captchaToken
    }
    if (this.formData.roleType == RoleType.DISTRIBUTOR) {
      const response = await this.$authentication.doFxLogin(IdentityType.account_pwd_captcha, loginParams)
      this.$authentication.account.doRememberLoginInfo(loginParams)
      return response
    } else {
      const response = await this.$authentication.doLogin(IdentityType.account_pwd_captcha, loginParams)
      this.$authentication.account.doRememberLoginInfo(loginParams)
      return response
    }
  }

  async doAuthLogin() {
    const loginParams = new LoginParams()
    loginParams.token = this.chainToken
    let response: any = null
    if (this.formData.roleType == RoleType.DISTRIBUTOR) {
      response = await this.$authentication.doFxLogin(IdentityType.account_pwd_captcha, loginParams)
      this.$authentication.account.doRememberLoginInfo(loginParams)
    } else {
      response = await this.$authentication.doLogin(IdentityType.account_pwd_captcha, loginParams)
      this.$authentication.account.doRememberLoginInfo(loginParams)
      return response
    }
    if (response.status.isSuccess() && response.data.code === '200') {
      this.doLoginByAuthentication()
    } else {
      this.$message.error(response?.data?.message)
    }
  }

  async shortMessageLogin() {
    const shortMessageLoginParams = new LoginParams()
    shortMessageLoginParams.phoneNumber = this.formData.account
    shortMessageLoginParams.smsCode = this.formData.phoneCaptcha
    shortMessageLoginParams.longTerm = this.rememberPassword
    shortMessageLoginParams.token = this.$authentication.verify.shortMessageCaptchaToken
    return await this.$authentication.doLogin(IdentityType.sms_code, shortMessageLoginParams)
  }

  async captchaValid(rule: any, value: any, callback: (error?: Error) => void) {
    if (rule.regexp.test(value)) {
      try {
        const res = await this.$authentication.verify.msValidateCaptcha(value)
        if (!res.status.isSuccess() || res.data.code !== 200) {
          callback(new Error('验证失败'))
        }
        callback()
      } catch (e) {
        // await this.refreshValidateCodePic()
        callback(new Error('验证失败'))
      }
    } else {
      callback(new Error('验证码为4位数'))
    }
  }

  public async created() {
    this.formData.account = this.$authentication.account.rememberLoginInfo.account
    this.formData.password = this.$authentication.account.rememberLoginInfo.password
    this.rememberPassword = this.$authentication.account.rememberLoginInfo.longTerm
    this.rules = {
      roleType: [
        {
          required: true,
          message: '请选择登录帐号类型',
          trigger: ['blur', 'change']
        }
      ],
      account: [
        {
          required: true,
          validator: async (rules: any, value: any, callback: (message?: any) => {}) => {
            const message = this.activeName === 'first' ? '账号' : '手机号'
            if (!value) {
              this.isPhoneNumberValid = false
              return callback(new Error(`请输入${message}`))
            } else {
              if (this.activeName === 'second') {
                // 宽松匹配
                if (!/^(?:(?:\+|00)86)?1\d{10}$/.test(value)) {
                  this.isPhoneNumberValid = false
                  return callback(new Error('手机号格式不正确'))
                }
              }
            }
            this.isPhoneNumberValid = true
            callback()
          },
          trigger: ['blur', 'change']
        }
      ],
      password: [
        {
          required: true,
          message: '请输入密码',
          trigger: 'blur'
        }
      ],
      captcha: [
        {
          regexp: /[a-zA-z0-9]{4}/,
          validator: this.captchaValid,
          required: true,
          trigger: 'none'
        }
      ]
    }
    const res = await this.onlineSchoolModule.getOnlineSchoolConfig()
    console.log('xxxxxx')
    if (res.status.isSuccess()) {
      this.isUseSlider = res.data.validateMethodConfig.enabledSlideCircus
    }
    // 获取登录票
    try {
      await this.refreshValidateCodePic()
      const smsRes = await this.mutationRegisterAndLogin.getSmsCodeAuthConfig(this.smsToken)
      this.enableSms = smsRes.enabled
      const riskRes = await this.mutationRegisterAndLogin.getFailedAuthConfig(this.smsToken)

      this.enableRisk = riskRes.enabled
    } catch (e) {
      console.log(e)
    }

    // this.unitDialogRef.isShow = true
  }

  // 加载钉钉二维码
  ddQRCodeLoading = true

  async mounted() {
    // this.doRefreshDdQRCode()
  }

  async loginSuccess() {
    // 123
    console.log('dengls')
  }
  dialogVisible() {
    this.dialogVisible3 = true
    this.successTime()
  }
  successTime() {
    let timer = 5
    const timers = setInterval(() => {
      if (timer === 0) {
        clearInterval(timers)
        this.success = 5
        this.dialogVisible3 = false
        this.dialog1 = false
        this.doLogin()
      } else {
        timer--
        this.success = timer
      }
    }, 1000)
  }
  loginFirst() {
    this.dialog1 = true
  }

  async roleTypeChange() {
    await this.refreshValidateCodePic()
  }

  /**
   * 滑块校验结果
   */
  async validCaptcha(options: { id: string; data: any }) {
    this.sliderCaptchData = options.data
    this.sliderCaptchToken = options.id
    if (this.$refs.sliderRef) {
      // await (this.$refs.sliderRef as BizSlider).destroyWindow()
      this.loginIng = false
    }
    await this.doLogin()
  }

  /**
   * 登入前是否有滑块验证
   */
  async beforeLogin() {
    if (this.isUseSlider) {
      if ((this.$refs?.sliderRef as BizSlider)?.visible) return
    }
    await this.loginForm.validate()
    this.$authentication.removeToken()
    if (this.isUseSlider) {
      if (this.formData.roleType === RoleType.SERVICEPROVIDER) {
        // res = await this.$authentication.applyProviderCaptcha()
      } else if (this.formData.roleType === RoleType.REGIONADMINISTRATOR) {
        this.localToke = ConfigCenterModule.getFrontendApplication(frontendApplication.regionAdminLoginToken)
      } else if (this.formData.roleType === RoleType.DISTRIBUTOR) {
        this.localToke = ConfigCenterModule.getFrontendApplication(
          frontendApplication.distributionAdministratorPwdLoginToken
        )
      } else if (this.formData.roleType === RoleType.SPECIALSUBJECT) {
        this.localToke = ConfigCenterModule.getFrontendApplication(frontendApplication.specialAdminPwdLoginToken)
      } else {
        this.localToke = ConfigCenterModule.getFrontendApplication(frontendApplication.superLoginToken)
      }
      if (this.$refs.sliderRef) {
        await (this.$refs.sliderRef as BizSlider).init()
      }
    } else {
      this.sliderCaptchData = new ImageCaptchaTrack()
      this.sliderCaptchToken = ''
      await this.doLogin()
    }
  }
  async doLogin() {
    try {
      this.loginIng = true
      // 判断使用的登录方式
      const result = this.passwordLogin ? await this.doPasswordLogin() : await this.shortMessageLogin()
      this.result = result
      console.log('result', result)
      // 登录成功处理
      if (result.status.isSuccess() && result.data.code === '200') {
        await this.handleLoginSuccess(result)
      } else {
        this.loginIng = false
        this.handleLoginError(result)
      }
      this.loginIng = false
    } catch (e) {
      this.loginIng = false
      this.handleError(e)
    }
  }

  // 登录成功的处理逻辑
  async handleLoginSuccess(result: any) {
    const { forcedChangePassword, passwordChangeCycle } =
      result.data.securityMetadata?.nonSecurityInfo?.passwordChangeAuthMetadata || {}
    this.chainToken = result.data?.identityAuthenticationToken
    if (forcedChangePassword) {
      await this.showPasswordChangeAlert(passwordChangeCycle)
    } else {
      if (this.enableSms) {
        this.handleSmsLogin(result)
      } else {
        await this.doLoginByAuthentication()
      }
    }
  }

  // 显示修改密码的提示框
  async showPasswordChangeAlert(passwordChangeCycle: number) {
    if (this.$refs.sliderRef) {
      await (this.$refs.sliderRef as BizSlider).destroyWindow()
      this.loginIng = false
    }
    this.$alert(`您的密码已超过${passwordChangeCycle}天未更新,请立即修改密码以保障安全！`, '修改密码', {
      confirmButtonText: '修改密码',
      type: 'error'
    })
      .then(() => {
        this.passwordDialog = true
        this.checkPassword.againPassword = ''
        this.checkPassword.password = ''
      })
      .catch(async () => {
        this.loginIng = false
        await this.refreshValidateCodePic()
      })
  }
  // 处理链式登录
  async handleSmsLogin(result: any) {
    this.phoneNumber = result.data?.tokenMetadata?.phone
    this.chainToken = result.data?.identityAuthenticationToken
    if (this.phoneNumber) {
      this.showSmsModal = true
    } else {
      if (this.$refs.sliderRef) {
        await (this.$refs.sliderRef as BizSlider).destroyWindow()
        this.loginIng = false
      }
      this.$alert(`您当前账号尚未绑定手机号码，请进行绑定后再登录`, {
        confirmButtonText: '立即绑定',
        type: 'warning'
      })
        .then(async () => {
          await this.refreshValidateCodePicPhone()
          this.dialog1 = true
        })
        .catch(() => {
          this.loginIng = false
        })
    }
  }

  // 登录失败的处理逻辑
  handleLoginError(result: any) {
    this.loginTips(result)
    this.$authentication.removeToken()
    // 处理特定的错误码
    if (result.data?.code === '40006') {
      this.loginResult = new LoginResult(result.data.code, '账号密码不匹配，请检查输入是否有误。请确认是否已注册。')
    } else {
      this.loginResult = new LoginResult(result.data.code, result.data.message)
    }

    // 滑动验证码错误的处理
    if (this.isUseSlider) {
      if (['70002', '70003', '70004'].includes(result.data.code)) {
        this.$message.error('验证失败')
        if (this.$refs.sliderRef) {
          ;(this.$refs.sliderRef as BizSlider).reloadCaptcha()
          this.loginIng = false
        }
        return
      }
    }

    this.refreshValidateCodePicIfNeeded(result)
  }

  // 刷新验证码图片
  async refreshValidateCodePicIfNeeded(result: any) {
    if (result.code === 4201) {
      result.message = '验证码不正确，请重新输入'
    }

    if (result.data?.message && result.data?.code !== '40006') {
      this.$message.error(result.data.message)
    } else if (result.data?.code === '40006') {
      this.$message.error('账号密码不匹配，请检查输入是否有误。请确认是否已注册。')
    }

    if (this.isUseSlider) {
      if (this.$refs.sliderRef) {
        await (this.$refs.sliderRef as BizSlider).destroyWindow()
        this.loginIng = false
      }
    } else {
      await this.refreshValidateCodePic()
    }
  }

  // 错误处理函数
  handleError(e: any) {
    this.$authentication.removeToken()
    this.loginIng = false

    if (e.data?.code === '40006') {
      this.loginResult = new LoginResult(e.data.code, '账号密码不匹配，请检查输入是否有误。请确认是否已注册。')
    } else {
      this.loginResult = new LoginResult(e.code, e.message)
    }

    e.code === 4201 && (e.message = '验证码不正确，请重新输入')

    if (e.message && e.data?.code !== '40006') {
      this.$message.error(e.message)
    } else if (e.data?.code === '40006') {
      this.$message.error('账号密码不匹配，请检查输入是否有误。请确认是否已注册。')
    }

    this.refreshValidateCodePic()
  }

  async doLoginByAuthentication() {
    try {
      const response = (await this.$authentication.ssoAuth()) as any
      if (response.code !== 200 || !response.data?.access_token) {
        await this.refreshValidateCodePic()
        this.$authentication.removeToken()
        return
      }
      // 分销角色登录成功时请求单位列表
      if (this.formData.roleType === RoleType.DISTRIBUTOR) {
        await this.isFxLoginStep()
        return
      }
      this.$emit('login-result', true)
      const messageContent = {
        message: 'login-success',
        type: 'admin'
      }
      if (this.$util?.channel) {
        this.$util.channel.postMessage(messageContent)
      }
      await this.loginSuccess()
    } catch (e) {
      if (this.isUseSlider) {
        if (this.$refs.sliderRef) {
          await (this.$refs.sliderRef as BizSlider).reloadCaptcha()
          this.loginIng = false
        }
      }
      await this.refreshValidateCodePic()
      this.$authentication.removeToken()
      this.loginResult = new LoginResult(e.code, e.message)
    } finally {
      if (this.isUseSlider) {
        if (this.$refs.sliderRef) {
          await (this.$refs.sliderRef as BizSlider).destroyWindow()
          this.loginIng = false
        }
      }
      this.loginIng = false
    }
  }

  async changePasswordSubmit() {
    try {
      await this.checkPassworRef.validate()
      const params = new CurrentAccountChangePasswordCauseForgetRequest()
      params.token = this.chainToken
      params.password = this.checkPassword.password
      const res = await this.$authentication.account.forgetPassword(params)
      if (res.data.code === 200) {
        this.chainToken = res.data.token
        this.passwordDialog = false
        this.$alert(`密码修改成功，请重新登录！`, '修改密码', {
          confirmButtonText: '确定',
          type: 'success'
        })
          .then(async () => {
            this.formData.account = ''
            this.formData.password = ''
            this.loginIng = false
            await this.refreshValidateCodePic()
          })
          .catch(async () => {
            this.formData.account = ''
            this.formData.password = ''
            this.loginIng = false
            await this.refreshValidateCodePic()
          })
      }
    } catch (e) {
      console.log(e)
    }
  }

  loginTips(result: any) {
    const nonSecurityInfo = result?.data?.securityMetadata?.nonSecurityInfo
    // 如果 nonSecurityInfo 不存在，直接返回，避免后续报错
    if (!nonSecurityInfo) {
      return
    }
    const { failedAuthMetadata } = nonSecurityInfo
    const { accountLock, failedAuthAttemptUpperLimit, failedAuthAttemptLimit, accountUnLockTime } = failedAuthMetadata
    if (accountLock) {
      this.handleAccountLocked(accountUnLockTime)
    } else {
      this.handleFailedAuthAttempt(failedAuthAttemptUpperLimit, failedAuthAttemptLimit)
    }
  }

  // 处理账号被锁定的情况
  async handleAccountLocked(accountUnLockTime: number) {
    const minutes = this.timestampToDateFormat(accountUnLockTime)
    await this.$confirm('提示', {
      message: `由于您的账号或密码多次输入错误，账号被暂时锁定，建议您在${minutes}之后再尝试登录或找回密码。`,
      confirmButtonText: '找回密码',
      cancelButtonText: '我知道了',
      type: 'warning'
    })
      .then(() => {
        this.$router.push('/forget')
      })
      .catch(() => {
        console.log('用户取消了找回密码')
      })
  }

  // 处理密码错误尝试的情况
  async handleFailedAuthAttempt(failedAuthAttemptUpperLimit: number, failedAuthAttemptLimit: number) {
    const remainingAttempts = failedAuthAttemptUpperLimit - failedAuthAttemptLimit
    await this.$alert(
      `请输入正确的账号或密码，连续输错${failedAuthAttemptUpperLimit}次后，账号将被锁定，您还可重试${remainingAttempts}次！`,
      '提示',
      {
        confirmButtonText: '我知道了'
      }
    )
      .then(() => {
        console.log('提示已关闭')
      })
      .catch(() => {
        this.loginIng = false
      })
  }

  timestampToDateFormat(timestamp: number) {
    const date = new Date(timestamp)

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始，需要加1，并确保两位数
    const day = String(date.getDate()).padStart(2, '0') // 确保两位数
    const hours = String(date.getHours()).padStart(2, '0') // 确保两位数
    const minutes = String(date.getMinutes()).padStart(2, '0') // 确保两位数

    return `${year}-${month}-${day} ${hours}:${minutes}`
  }

  async smsCodeLogin() {
    this.loginIng = true
    const params = new LoginParams()
    params.phoneNumber = this.formData.account
    params.smsCode = this.formData.phoneCaptcha

    try {
      await this.loginFormSec.validate()

      const codeVaildRes = await this.$authentication.verify.msValidSmsCode(params.smsCode, params.phoneNumber)
      if (codeVaildRes?.status?.code !== 200) {
        this.$message.error('验证码未知错误')
        this.loginIng = false
        return
      } else if (codeVaildRes?.data?.code !== 200) {
        if (codeVaildRes?.data?.code === 500) {
          this.$message.error('请输入正确验证码')
        } else if (codeVaildRes?.data?.code === 408) {
          this.$message.error('验证码超时')
        } else {
          this.$message.error('验证码未知错误')
        }
        this.loginIng = false
        return
      }

      params.token = this.$authentication.verify.shortMessageCaptchaToken
      // todo
      let res
      if (this.formData.roleType == RoleType.DISTRIBUTOR) {
        res = await this.$authentication.doFxLogin(IdentityType.sms_code, params)
      } else {
        res = await this.$authentication.doLogin(IdentityType.sms_code, params)
      }
      if (res?.status?.code === 200) {
        const response = (await this.$authentication.ssoAuth()) as any
        if (response.code !== 200 || !response.data?.access_token) {
          this.$message.error(res?.data?.message)
          this.loginIng = false
          return
        }

        // 分销角色登录成功时请求单位列表
        if (this.formData.roleType === RoleType.DISTRIBUTOR) {
          await this.isFxLoginStep()
        }

        this.$emit('login-result', true)
        this.loginIng = false
        await this.loginSuccess()
      } else {
        this.$message.error(res?.data?.message)
        this.loginIng = false
      }
    } catch (e) {
      console.log(e, 'rejecte')
      if (e?.code === 4001) {
        this.$message.error('账号不存在')
      } else {
        const message = e?.message || '登录失败'
        if (message) {
          this.$message.error(message)
        }
      }
      this.loginIng = false
    }
  }

  async isFxLoginStep() {
    // 只有一个单位时直接登录跳转,否则走弹窗选择单位
    if ((await this.queryUnitList()) == 'stopLogin') {
      await this.$authentication.ssoLogout()
    } else if (this.unitDialogRef.unitModel?.length > 1) {
      // （多个单位）请求列表（登录成功时）
      RootModule.SET_MENU_LIST([])
      await QueryManagerDetail.queryManagerDetail()
      if (CapabilityServiceConfig.distributionType === DistributionServiceTypeEnum.basic) {
        location.hash = '/welcome'
      } else {
        location.hash = '/fx/distribution/promotion-gateway/check'
      }
    } else {
      // 单个单位直接赋值并登录
      await this.unitDialogRef.getUnitToken(this.unitDialogRef.unitModel[0])
      if (CapabilityServiceConfig.distributionType === DistributionServiceTypeEnum.basic) {
        location.hash = '/welcome'
      } else {
        location.hash = '/fx/distribution/promotion-gateway/check'
      }
      location.reload()
    }
  }

  // fx请求单位列表
  async queryUnitList() {
    const res = await QueryManagerDetail.changeAuthorizationUnitInfoList()
    if (res?.data?.code == '200') {
      this.unitDialogRef.unitModel = QueryManagerDetail.distributionUnitInformationList
    } else if (res?.data?.code == '517') {
      this.$message.error('非本网校分销商账号')
      return 'stopLogin'
    } else {
      this.$message.error('账号或密码有误')
      return 'stopLogin'
    }
  }

  beforeDestroy() {
    this.loginIng = false
  }

  /**
   * 更新加载钉钉二维码
   */
  async doRefreshDdQRCode() {
    // this.ddQRCodeLoading = true
    // try {
    //   let url = `${location.protocol}//${location.host}/#/binding-dd`
    //   if (process.env.NODE_ENV === 'production') {
    //     url = `${location.protocol}//${location.host}/admin/#/binding-dd`
    //   }
    //   console.log('applyDingDingLoginQRCodeGoto.url,', url)
    //   await this.$authentication.applyDingDingLoginQRCodeGoto(url)
    // } catch (err) {
    //   console.error(err)
    // }
    // const param = this.$authentication.getDingDingLoginGotoParam()
    // if (!param) {
    //   this.ddQRCodeLoading = false
    //   return
    // }
    // ;(window as any).DDLogin({
    //   id: 'dd_login_container',
    //   goto: param, //请参考注释里的方式
    //   style: 'border:none;background-color:#FFFFFF;margin-top:-30px;',
    //   width: '230',
    //   height: '290'
    // })
    // this.ddQRCodeLoading = false
  }

  async ddLoginWithToken(token: string) {
    try {
      this.loginIng = true
      const result = await this.$authentication.ddLoginWithToken(token)
      if (result.status.code === 200) {
        try {
          await this.$authentication.auth()
          this.$emit('login-result', true)
        } catch (e) {
          this.loginResult = new LoginResult(e.code, e.message)
        } finally {
          this.loginIng = false
        }
      } else {
        // todo
        // this.loginResult = result
      }
      await this.loginSuccess()
    } catch (e) {
      this.loginResult = new LoginResult(e.code, e.message)
    }
  }
}

export default LoginCore
