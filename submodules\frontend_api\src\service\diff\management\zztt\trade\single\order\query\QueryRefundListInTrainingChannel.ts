import QueryRefundListBase from '@api/service/management/trade/single/order/query/vo/QueryRefundListBase'
import { Page, Response } from '@hbfe/common'
import {
  ReturnOrderBasicDataRequest,
  ReturnOrderRequestVo
} from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
import ReturnOrderResponseVo from '@api/service/diff/management/zztt/trade/order/model/ReturnOrderResponseVo'
import UserModule from '@api/service/management/user/UserModule'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import MsTradeQueryFrontGatewayCourseLearningBacktage, {
  IssueInfo1,
  ReturnOrderRequest,
  ReturnOrderSortField,
  ReturnSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import QueryStudentList from '@api/service/management/user/query/student/QueryStudentList'
import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
import DataExportBackstage from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import TradeQueryFrontGatewayTradeQueryBackstage from '@api/diff-gateway/zztt-trade-query-front-gateway-TradeQueryBackstage'
import QueryRefundListInTrainingChannelMain from '@api/service/management/trade/single/order/query/QueryRefundListInTrainingChannel'
import { ChangeOrderType } from '@api/service/common/trade/ChangeOrderType'

/**
 * 查询个人退款单 - 专题管理员
 */
export default class QueryRefundListInTrainingChannel extends QueryRefundListInTrainingChannelMain {
  /**
   * 获取退款单列表 差异化
   */
  async queryRefundOrderListDiff(page: Page, request: ReturnOrderRequestVo): Promise<Array<ReturnOrderResponseVo>> {
    let userIdList: string[] = []
    if (request.name || request.idCard || request.loginAccount) {
      const queryUser = UserModule.queryUserFactory.queryStudentList
      queryUser.queryStudentIdParams.idCard = request.idCard
      queryUser.queryStudentIdParams.userName = request.name
      queryUser.queryStudentIdParams.loginAccount = request.loginAccount
      const res = await queryUser.queryStudentIdList()
      userIdList = res.data
      if (!userIdList.length) {
        page.totalSize = 0
        page.totalPageSize = 0
        this.returnOrderStatisic.totalReturnOrderCount = 0
        this.returnOrderStatisic.totalRefundAmount = 0
        return []
      }
    }
    request.subOrderInfo.orderInfo.buyerIdList = userIdList

    if (request.saleSource || request.saleSource === SaleChannelEnum.self) {
      request.subOrderInfo.orderInfo.saleChannels = [request.saleSource]
    } else {
      request.subOrderInfo.orderInfo.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic
      ]
    }

    if (request.refundType) {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = [request.refundType]
    } else {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = []
    }
    request.returnCommodity.issueInfo = new IssueInfo1()
    if (request.periodId) {
      request.returnCommodity.issueInfo.issueId = request.periodId
    }
    request.fillDtoReturnStatusWithVo()

    const sort = new ReturnSortRequest()
    sort.policy = SortPolicy.DESC
    sort.field = ReturnOrderSortField.APPLIED_TIME
    const res = await TradeQueryFrontGatewayTradeQueryBackstage.pageReturnOrderInTrainingChannel({
      page: page,
      request: request as ReturnOrderRequest,
      sort: [sort]
    })
    page.totalSize = res.data?.totalSize
    page.totalPageSize = res.data?.totalPageSize
    await this.queryStatisticReturnOrder(request)
    if (res.status.isSuccess()) {
      const dataArr = [] as ReturnOrderResponseVo[]
      for (const item of res.data.currentPageData) {
        const tmpItem = new ReturnOrderResponseVo()
        Object.assign(tmpItem, item)
        const ext = item.ext as any
        tmpItem.courseType = ext?.courseType || ''
        // 处理换班、换期标签
        const { subOrderInfo } = item
        if (subOrderInfo) {
          const isExistExchangeScheme = subOrderInfo.exchanged
          const isExistExchangeIssue = subOrderInfo.isExchangeIssue
          if (isExistExchangeScheme) {
            tmpItem.changeOrderStatus.push(ChangeOrderType.CLASS_TYPE)
          }
          if (isExistExchangeIssue) {
            tmpItem.changeOrderStatus.push(ChangeOrderType.PERIOD_TYPE)
          }
        }
        tmpItem.getPeriodMessage(item)
        tmpItem.changeStatus()
        dataArr.push(tmpItem)
      }
      const userIds = [...new Set(dataArr.map((item) => item.subOrderInfo.orderInfo.buyer.userId))]
      if (userIds && userIds.length) {
        const queryM = new QueryStudentList()
        const studentDetailMap = await queryM.batchQueryStudentDetailMapByUserId(userIds)
        dataArr.forEach((item) => {
          if (!item.subOrderInfo.orderInfo.buyer.userId) return
          item.buyer = studentDetailMap.get(item.subOrderInfo.orderInfo.buyer.userId) || new UserDetailVo()
        })
      }
      this.totalSize = res.data.totalSize
      return dataArr
    }
    return []
  }
}
