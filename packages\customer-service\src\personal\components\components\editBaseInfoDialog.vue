<template>
  <el-drawer title="编辑基础信息" :visible.sync="editBaseInfoDialog" size="560px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-form ref="form" :model="userInfo" :rules="rules" label-width="auto" class="m-form f-mt20">
        <el-form-item label="姓名：">
          <el-input v-model="userInfo.userName" clearable placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="登录账号：" v-if="queryShowLoginAccount.isShowLoginAccount">
          <el-input v-model="userInfo.loginAccount" clearable disabled />
        </el-form-item>
        <el-form-item label="证件类型：" prop="idCardType">
          <el-select v-model="userInfo.idCardType" clearable placeholder="请选择证件类型">
            <el-option
              :label="item.name"
              :value="item.idCardTypeValue"
              name="type"
              v-for="item in idCardTypeList"
              :key="item.idCardTypeValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="证件号：" prop="idCard">
          <el-input v-model="userInfo.idCard" clearable placeholder="请输入证件号" />
        </el-form-item>
        <el-form-item label="性别：">
          <el-select v-model="userInfo.gender" placeholder="请选择性别">
            <el-option label="男" :value="1"></el-option>
            <el-option label="女" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="手机号：" prop="phone">
          <el-input v-model="userInfo.phone" clearable placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="单位地区：">
          <basic-data-edit-value-cascader
            v-model="companyRegionCodeList"
            :regionOptions="regionOptions"
          ></basic-data-edit-value-cascader>
        </el-form-item>
        <el-form-item label="工作单位：">
          <el-input
            v-model="userInfo.companyName"
            clearable
            placeholder="请输入工作单位（全称）"
            v-if="!unitConfig.enableSearch"
          />
          <enterprise-checks
            v-model="userInfo.companyName"
            @unitObject="unitCall"
            :qcc-option="unitConfig.hasQcc"
            :tyc-option="unitConfig.hasTy"
            :unit-name="userInfo.companyName"
            v-else
          ></enterprise-checks>
        </el-form-item>
        <el-form-item class="m-btn-bar">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="saveEditVal" :loading="loading">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Watch, Prop, Ref } from 'vue-property-decorator'
  import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
  import BasicDataEditValueCascader from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/basic-new-data-edit-value-cascader.vue'

  import UserModule from '@api/service/management/user/UserModule'
  import { IdentityCardType } from '@api/service/management/online-school-config/functionality-setting/enum/IdentityCardTypeEnum'
  import cloneDeep from 'lodash/cloneDeep'
  import { ElForm } from 'element-ui/types/form'
  import IdCardTypeVo from '@api/service/common/basic-data-dictionary/query/vo/IdRegisterCardTypeVo'
  import MutationRegisterAndLogin from '@api/service/management/online-school-config/functionality-setting/mutation/MutationRegisterAndLogin'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import UserIndustryJsInfo from '@api/service/management/user/mutation/student/vo/UserIndustryJsInfo'
  import CertificateInfoVo from '@api/service/management/user/mutation/student/vo/CertificateInfoVo'
  import StudentCertificateInfoVo from '@api/service/management/user/query/student/vo/StudentCertificateInfoVo'
  import { CertificateAttachment } from '@api/ms-gateway/ms-account-v1'
  import { AttachmentInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage/index'
  import UnitConfig from '@api/service/management/online-school-config/portal/models/UnitConfig'
  import EnterpriseChecks from '@hbfe/jxjy-admin-customerService/src/personal/components/components/enterprise-checks.vue'
  import EnterpriseItem from '@api/service/common/enterprise-Information/EnterpriseItem'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'

  @Component({
    components: {
      BasicDataEditValueCascader,
      EnterpriseChecks
    }
  })
  export default class extends Vue {
    @Ref('form')
    userInfoForm: ElForm
    @Prop({
      type: Object,
      default: null
    })
    studentParams: UserDetailVo
    @Prop({
      type: Object,
      default: null
    })
    unitConfig: UnitConfig
    // 弹窗是否显示
    editBaseInfoDialog = false
    //证件类型枚举
    identityCardTypeEnum = IdentityCardType
    // 单位地区列表
    companyRegionCodeList: Array<string> | string = null
    //用户信息
    userInfo = new UserDetailVo()
    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount
    // 学员修改实例
    studentUpdateObj = UserModule.mutationUserFactory.mutationUpdateStudentInfo
    /**
     * 地区选项
     */
    regionOptions: Array<RegionTreeVo> = new Array<RegionTreeVo>()
    /**
     * 全国树
     */
    nationWideTree: Array<RegionTreeVo> = new Array<RegionTreeVo>()

    /**
     * 获取服务地区id
     */
    serviceId: string[]
    /**
     * 加载中
     */
    loading = false
    /**
     * 查询注册配置实例化
     */
    mutationRegisterAndLogin: MutationRegisterAndLogin =
      OnlineSchoolConfigModule.mutationFunctionalitySettingFactory.registerAndLogin
    /**
     * 证件类型数组
     */
    idCardTypeList = new Array<IdCardTypeVo>()

    //证件号验证规则
    idCardRules(rule: any, value: any, callback: any) {
      if (!value) {
        return callback(new Error('请输入证件号'))
      }
      if (this.userInfo.idCardType == 9) {
        const reg2 = /^(810|820|830)\d*/
        // 港澳台先前判断条件
        reg2.test(value) ? callback() : callback(new Error('港澳台居民居住证格式有误'))
      }
      if (this.userInfo.idCardType !== 1 && this.userInfo.idCardType !== 9) {
        if (value.length < 6) {
          return callback(new Error('输入正确的证件号'))
        }
      } else {
        const reg =
          /(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0[1-9]|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)/
        if (!reg.test(value)) {
          return callback(new Error('输入正确的证件号'))
        }
      }

      callback()
    }
    //   证件类型验证规则
    identityCardTypeRules(rule: any, value: any, callback: any) {
      if (!value) {
        callback(new Error('请选择证件类型'))
      } else {
        callback()
      }
    }
    // 手机号的表单验证
    validatePhone = (rule: any, value: any, callback: any) => {
      const reg = new RegExp(/^[1]([3-9])[0-9]{9}$/)
      reg.test(value) ? callback() : callback(new Error('手机号输入错误'))
    }
    //验证规则
    rules = {
      idCardType: {
        required: true,
        message: '请选择证件类型',
        validator: this.identityCardTypeRules,
        trigger: ['blur', 'change']
      },
      idCard: {
        required: true,
        validator: this.idCardRules,
        trigger: ['blur', 'change']
      },
      phone: {
        required: true,
        validator: this.validatePhone,
        trigger: ['blur', 'change']
      }
    }

    @Watch('studentParams', {
      deep: true
    })
    async studentParamsChange(newVal: UserDetailVo) {
      this.userInfo = cloneDeep(newVal)
      this.userInfo.gender = this.userInfo.gender === -1 ? null : this.userInfo.gender
    }
    // 单位地区回显
    @Watch('userInfo.companyRegion.regionPath', {
      deep: true
    })
    areaChange(newVal: string) {
      if (newVal) {
        this.$nextTick(() => {
          this.companyRegionCodeList = newVal.split('/').filter((item) => item)
        })
      }
    }
    @Watch('userInfo', {
      deep: true
    })
    userInfoChange(newVal: UserDetailVo) {
      this.studentUpdateObj.updateStudentParams.userId = newVal?.userId || undefined
      this.studentUpdateObj.updateStudentParams.idCard = newVal?.idCard || undefined
      this.studentUpdateObj.updateStudentParams.gender = newVal?.gender || 0
      this.studentUpdateObj.updateStudentParams.name = newVal?.userName || undefined
      this.studentUpdateObj.updateStudentParams.phone = newVal?.phone || undefined
      this.studentUpdateObj.updateStudentParams.companyName = newVal?.companyName || undefined
      this.studentUpdateObj.updateStudentParams.companyCode = newVal?.companyCode || undefined
      this.studentUpdateObj.updateStudentParams.companyRegionCode = newVal?.companyRegion?.regionId || undefined
      this.studentUpdateObj.updateStudentParams.idCardType = newVal?.idCardType || undefined
      this.studentUpdateObj.updateStudentParams.accountId = newVal?.accountId || undefined
      if (newVal?.idCard?.length === 18 && newVal?.idCardType === 1) {
        if (newVal.gender === -1 || newVal.gender === null) {
          // 提取第17位作为性别标识位
          const genderDigit = newVal?.idCard?.charAt(16)
          // 判断性别
          const genderCode = parseInt(genderDigit, 10)
          if (genderCode % 2 === 0) {
            this.userInfo.gender = 0
            this.studentUpdateObj.updateStudentParams.gender = 0
          } else {
            this.userInfo.gender = 1
            this.studentUpdateObj.updateStudentParams.gender = 1
          }
        }
      }
      // 人社行业信息
      this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.industryId =
        newVal?.rsStudentIndustryInfo?.industryId || undefined
      this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.firstProfessionalCategory =
        newVal?.rsStudentIndustryInfo?.firstProfessionalCategory || undefined
      this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.firstProfessionalCategoryName =
        newVal?.rsStudentIndustryInfo?.firstProfessionalCategoryName || undefined
      this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.secondProfessionalCategory =
        newVal?.rsStudentIndustryInfo?.secondProfessionalCategory || undefined
      this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.secondProfessionalCategoryName =
        newVal?.rsStudentIndustryInfo?.secondProfessionalCategoryName || undefined
      this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.professionalQualification =
        newVal?.rsStudentIndustryInfo?.professionalQualification || undefined
      // 建设行业信息
      const jsUserIndustryInfo = new UserIndustryJsInfo()
      jsUserIndustryInfo.industryId = newVal?.jsStudentIndustryInfo?.industryId || undefined
      jsUserIndustryInfo.firstProfessionalCategory =
        newVal?.jsStudentIndustryInfo?.firstProfessionalCategory || undefined
      jsUserIndustryInfo.secondProfessionalCategory =
        newVal?.jsStudentIndustryInfo?.secondProfessionalCategory || undefined
      jsUserIndustryInfo.professionalQualification =
        newVal?.jsStudentIndustryInfo?.professionalQualification || undefined
      // 证书信息
      const jsCertificateInfos = new Array<CertificateInfoVo>()
      newVal?.jsStudentIndustryInfo?.userCertificateList?.map((item: StudentCertificateInfoVo) => {
        const certificateInfo = new CertificateInfoVo()
        certificateInfo.certificateId = item?.certificateId || undefined
        certificateInfo.certificateNo = item?.certificateNo || undefined
        certificateInfo.certificateCategory = item?.certificateCategory || undefined
        certificateInfo.registerProfessional = item?.registerProfessional || undefined
        certificateInfo.releaseStartTime = item?.releaseStartTime || undefined
        certificateInfo.certificateEndTime = item?.certificateEndTime || undefined
        certificateInfo.certificateCategoryName = item?.certificateCategoryName || undefined
        certificateInfo.registerProfessionalName = item?.registerProfessionalName || undefined
        // 证书附件
        const certificateAttachments = Array<CertificateAttachment>()
        item?.attachmentInfoList?.map((item: AttachmentInfoResponse) => {
          const certificateAttachment = new CertificateAttachment()
          certificateAttachment.certificateUrl = item?.url || undefined
          certificateAttachment.name = item?.name || undefined
          certificateAttachments.push(certificateAttachment)
        })
        certificateInfo.certificateAttachments = certificateAttachments
        jsCertificateInfos.push(certificateInfo)
      })
      jsUserIndustryInfo.certificateInfos = jsCertificateInfos
      this.studentUpdateObj.updateStudentParams.jsUserIndustryInfo = jsUserIndustryInfo
      // 卫生行业信息
      this.studentUpdateObj.updateStudentParams.wsUserIndustryInfo.industryId =
        newVal?.wsStudentIndustryInfo?.industryId || undefined
      this.studentUpdateObj.updateStudentParams.wsUserIndustryInfo.personnelCategory =
        newVal?.wsStudentIndustryInfo?.personnelCategory || undefined
      this.studentUpdateObj.updateStudentParams.wsUserIndustryInfo.positionCategory =
        newVal?.wsStudentIndustryInfo?.positionCategory || undefined
      // 工勤行业信息
      this.studentUpdateObj.updateStudentParams.gqUserIndustryInfo.industryId =
        newVal?.gqStudentIndustryInfo?.industryId || undefined
      this.studentUpdateObj.updateStudentParams.gqUserIndustryInfo.professionalLevel =
        newVal?.gqStudentIndustryInfo?.professionalLevel || undefined
      this.studentUpdateObj.updateStudentParams.gqUserIndustryInfo.jobCategoryId =
        newVal?.gqStudentIndustryInfo?.jobCategoryId || undefined
      // 教师行业信息
      if (newVal.lsStudentIndustryInfo.industryId) {
        this.studentUpdateObj.updateStudentParams.lsUserIndustryInfo.industryId =
          newVal?.lsStudentIndustryInfo?.industryId || undefined
        this.studentUpdateObj.updateStudentParams.lsUserIndustryInfo.grade =
          newVal?.lsStudentIndustryInfo?.sectionAndSubjects[0]?.section || undefined
        this.studentUpdateObj.updateStudentParams.lsUserIndustryInfo.subject =
          newVal?.lsStudentIndustryInfo?.sectionAndSubjects[0]?.subjects || undefined
      }
      // 药师行业信息
      if (newVal.ysStudentIndustryInfo.industryId) {
        this.studentUpdateObj.updateStudentParams.ysUserIndustryInfo.industryId =
          newVal?.ysStudentIndustryInfo?.industryId || undefined
        this.studentUpdateObj.updateStudentParams.ysUserIndustryInfo.certificatesType =
          newVal?.ysStudentIndustryInfo?.certificatesType || undefined
        this.studentUpdateObj.updateStudentParams.ysUserIndustryInfo.practitionerCategory =
          newVal?.ysStudentIndustryInfo?.practitionerCategory || undefined
      }
    }

    async created() {
      this.nationWideTree = await QueryBusinessRegion.getCountrywideRegion()
      this.serviceId = await QueryBusinessRegion.getServiceRegionIds()
      this.regionOptions = await QueryBusinessRegion.filterRegionTree(this.nationWideTree, this.serviceId)

      const res = await this.mutationRegisterAndLogin.queryRegisterSettingFormIdCardTypeList()
      this.idCardTypeList = res.data.filter((item) => {
        return item.select
      })
    }

    // 执行修改操作
    async saveEditVal() {
      this.userInfoForm.validate(async (value: boolean) => {
        if (value) {
          // 单位地区code转换
          if (this.companyRegionCodeList && this.companyRegionCodeList.length) {
            // 获取最后一位地区值传入
            const companyRegionCode = this.companyRegionCodeList[this.companyRegionCodeList.length - 1]
            this.studentUpdateObj.updateStudentParams.companyRegionCode = companyRegionCode
          }
          this.loading = true
          const res = await this.studentUpdateObj.doUpdateStudentInfo()
          if (!res?.status.isSuccess()) {
            this.loading = false
            return this.$message.error('修改请求失败！')
          } else {
            if (res?.data?.code == 200) {
              this.$message.success('修改请求成功！')
              this.$emit('updateUserInfo')
              this.closeDialog()
            } else {
              this.$message.error(res.data.message)
            }
            if (res?.data?.code == 100001) {
              this.$message.warning('手机号码重复！')
            }
            if (res?.data?.code == 100002) {
              this.$message.warning('身份证号码重复！')
            }
            if (res.data.code == 402) {
              this.$message.error(res.data.message)
            }
            this.loading = false
          }
        }
      })
    }
    /**
     * 打开弹窗
     */
    openDialog() {
      this.editBaseInfoDialog = true
      this.$nextTick(() => {
        this.userInfoForm.clearValidate(['phone'])
      })
    }

    /**
     * 关闭弹窗
     */
    closeDialog() {
      this.editBaseInfoDialog = false
    }

    unitCall(val: EnterpriseItem) {
      this.userInfo.companyName = val.name
      this.userInfo.companyCode = val.code
    }
  }
</script>
