<route-meta>
{
"title": "方案类型下拉搜索选择器（单选）"
}
</route-meta>
<template>
  <el-cascader
    v-model="categoryIdList"
    :clearable="clearable"
    filterable
    @clear="categoryIdList = undefined"
    :props="props"
    :options="options"
    :placeholder="placeholder"
  />
</template>

<script lang="ts">
  import { Prop, Emit, Watch, Component, Vue } from 'vue-property-decorator'
  import { CascaderOptions } from '@hbfe/jxjy-admin-components/src/models/CascaderOptions'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
  import Context from '@api/service/common/context/Context'
  import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: true
    })
    checkStrictly: boolean

    @Prop({
      type: String,
      default: '请选择培训方案类型'
    })
    placeholder: string

    @Prop({
      type: Boolean,
      default: true
    })
    clearable: boolean

    @Prop({
      type: Array,
      default: () => new Array<string>()
    })
    value: Array<string>

    categoryIdList: Array<string> = new Array<string>()
    options: Array<CascaderOptions> = new Array<CascaderOptions>()
    props = {
      checkStrictly: true
    }

    /**
     * 是否对接第三方平台
     */
    isDockThirdParty = false

    @Watch('value')
    valueChange() {
      this.categoryIdList = this.value
    }

    @Emit('input')
    @Watch('categoryIdList')
    selectedChange() {
      return this.categoryIdList
    }

    created() {
      // 是否对接第三方平台
      const dockThirdPartyServicerIds = CommonConfigCenter.getFrontendApplication(
        frontendApplication.dockThirdPartyServicerIds
      )
      if (dockThirdPartyServicerIds) {
        this.isDockThirdParty = dockThirdPartyServicerIds.includes(Context.servicerInfo.id)
      }

      this.setProps()
      this.options = [
        {
          value: '-1',
          label: '培训班',
          children: [
            {
              value: 'chooseCourseLearning',
              label: '选课规则'
            },
            {
              value: SchemeTypeEnum[SchemeTypeEnum.trainingCooperation],
              label: '自主选课'
            }
          ]
        }
      ]

      if (this.isDockThirdParty) {
        this.options.push({ value: 'trainingCooperation', label: '合作办学' })
      }
    }
    setProps() {
      this.props.checkStrictly = this.checkStrictly
    }
  }
</script>
