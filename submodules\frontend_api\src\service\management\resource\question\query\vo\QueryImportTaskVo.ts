import { EachStateCount, TaskExecuteByPageResponse } from '@api/ms-gateway/ms-importopen-v1'
import { QuestionImportTaskResultEnum } from '@api/service/common/enums/async-task/QuestionImportTaskResult'
import { QuestionImportTaskStatusEnum } from '@api/service/common/enums/async-task/QuestionImportTaskStatus'

class QueryImportTaskVo extends TaskExecuteByPageResponse {
  /**
   * 任务编号
   */
  taskId = ''

  /**
   * 任务名称
   */
  name = ''
  /**
   * 任务分类
   */
  category = ''
  /**
   * 所属批次单编号
   */
  batchNo = ''
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
   */
  taskState: QuestionImportTaskStatusEnum = null
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
   */
  processResult: QuestionImportTaskResultEnum = null
  /**
   * 处理信息
   */
  message = ''
  /**
   * 创建时间
   */
  createdTime = ''
  /**
   * 就绪时间
   */
  alreadyTime = ''
  /**
   * 执行时间
   */
  executingTime = ''
  /**
   * 完成时间
   */
  completedTime = ''
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount> = []

  from(item: TaskExecuteByPageResponse) {
    this.taskId = item.id
    this.name = item.name
    this.category = item.category
    this.batchNo = item.batchNo
    this.taskState = item.taskState
    this.processResult = item.processResult
    this.message = item.message
    this.createdTime = item.createdTime
    this.alreadyTime = item.alreadyTime
    this.executingTime = item.executingTime
    this.completedTime = item.completedTime
    this.eachStateCounts = item.eachStateCounts
  }
}

export default QueryImportTaskVo
