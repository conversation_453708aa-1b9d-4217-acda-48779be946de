const path = require('path')
const fs = require('fs')
const cwd = process.cwd()
const filePath = path.resolve(cwd, '.cache')
const flags = process.env.FLAG.split(',')
const targetValue = process.env.TARGET

/**
 * 递归遍历对象并替换指定值，并在特定条件下添加或修改属性
 * @param {Object|Array} obj - 需要处理的对象或数组
 * @param {String} targetValue - 需要替换的目标值
 * @param {String} newValue - 用来替换的值
 * @param {String} flag - 替换标识
 * @returns {Object|Array} - 修改后的对象或数组
 */
function replaceValues(obj, targetValue, newValue, flag) {
  if (typeof obj === 'object' && obj !== null) {
    // 记录是否pathSegments被替换
    let pathSegmentsReplaced = false

    for (let key in obj) {
      // 如果值是对象（不包括数组），递归调用replaceValues
      if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
        obj[key] = replaceValues(obj[key], targetValue, newValue, flag)

        // 特殊处理 permissionMap 下的节点
        if (key === 'permissionMap') {
          for (let permKey in obj[key]) {
            if (obj[key][permKey].ownerGroup) {
              obj[key][permKey].ownerGroup = obj[key][permKey].ownerGroup.map(item =>
                item.includes(targetValue) ? item.replace(targetValue, newValue) : item
              )

              // 如果 permissionMap 下的节点没有 meta 属性，则添加 ext 属性
              if (!obj[key][permKey].meta) {
                if (!obj[key][permKey].ext || !obj[key][permKey].ext.diffSchool)  {
                  obj[key][permKey].ext = { diffSchool: [] }
                }
                if (!obj[key][permKey].ext.diffSchool.includes(flag)) {
                  obj[key][permKey].ext.diffSchool.push(flag)
                }
              }
            }
          }
        }
      } else if (Array.isArray(obj[key])) {
        obj[key] = obj[key].map(item => {
          if (typeof item === 'object') {
            return replaceValues(item, targetValue, newValue, flag)
          }
          // 如果是 graphql 并且包含 flag，不进行替换
          if (key === 'graphql' && item.includes(flag)) {
            return item
          }
          if (key !== 'diffSchool' && typeof item === 'string' && item.includes(targetValue)) {
            if (key === 'pathSegments') {
              pathSegmentsReplaced = true
              return item.replace(targetValue, newValue)
            }
            return item.replace(targetValue, newValue)
          }
          return item === targetValue ? newValue : item
        })
      } else if (typeof obj[key] === 'string' && obj[key].includes(targetValue)) {
        obj[key] = obj[key].replace(targetValue, newValue)
      } else if (obj[key] === targetValue) {
        obj[key] = newValue
      }
    }

    // 如果 pathSegments 被替换且同级没有 ownerGroup 且同级有一个 meta 属性
    if (pathSegmentsReplaced && !obj.ownerGroup && obj.meta) {
      if (!obj.meta.diffSchool) {
        obj.meta.diffSchool = []
      }
      if (!obj.meta.diffSchool.includes(flag)) {
        obj.meta.diffSchool.push(flag)
      }
    }
  } else if (Array.isArray(obj)) {
    obj = obj.map(item => {
      if (typeof item === 'object') {
        return replaceValues(item, targetValue, newValue, flag)
      }
      if (typeof item === 'string' && item.includes(targetValue)) {
        return item.replace(targetValue, newValue)
      }
      return item === targetValue ? newValue : item
    })
  }
  return obj
}

// 保存修改后的对象到JSON文件
function saveJSONFile(filePath, data) {
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8')
}

// 读取JSON文件并解析为对象
function readJSONFile(filePath) {
  const data = fs.readFileSync(filePath, 'utf8')
  return JSON.parse(data)
}

flags.forEach(flag => {
  const inputFilePath = path.join(filePath, `${flag}-ui-assigns.json`)
  const jsonObj = readJSONFile(inputFilePath)
  const modifiedJsonObj = replaceValues(jsonObj, flag, targetValue, flag)
  const outputFilePath = path.join(filePath, `${flag}-ui-assigns.json`)
  saveJSONFile(outputFilePath, modifiedJsonObj)
  console.log(`修改后的文件已保存到: ${outputFilePath}`)
})
