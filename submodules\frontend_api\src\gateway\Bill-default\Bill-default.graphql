schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""批次订单发票查询
		@param batchOrderBillQuery 查询条件
		@param page                分页信息
		@return 批次订单发票信息
	"""
	findBatchOrderBillPageList(batchOrderBillQuery:BatchOrderBillQueryRequest,page:Page):BatchOrderBillResponsePage @page(for:"BatchOrderBillResponse")
	"""获取发票的下载地址
		@param billId      发票编号
		@param invoiceType 发票类型：1-蓝票，2-红票
		@return 发票文件对应相对地址，包含mfs路径
	"""
	findInvoiceDownLoadUrl(billId:String,invoiceType:Int!):String
	"""非批次订单发票信息查询，不包含批次订单发票
		@param nonBatchOrderBillQuery 查询条件
		@param page                   分页信息
		@return 非批次订单发票
	"""
	findNonBatchOrderBillPageList(nonBatchOrderBillQuery:NonBatchOrderBillQueryRequest,page:Page):OrderBillItemResponsePage @page(for:"OrderBillItemResponse")
	"""查询所有订单发票
		@param orderBillQuery 查询条件
		@param page           分页信息
		@return 订单发票信息
	"""
	findOrderBillPageList(orderBillQuery:OrderBillQueryRequest,page:Page):OrderBillItemResponsePage @page(for:"OrderBillItemResponse")
	"""依据收款账号编号获取纳税人信息
		@param accountId 收款账号编号
		@return 纳税人信息
	"""
	findTaxPayerByAccount(accountId:String):TaxPayerResponse
}
type Mutation {
	"""收款账号绑定纳税人
		@param accountId  账号ID
		@param taxPayerId 纳税人ID
	"""
	bindTaxPayer(accountId:String,taxPayerId:String):Void
	"""解散发票，将已生成的发票标记为不可用且解除与子订单关系
		@param billId 发票编号
	"""
	disbandInvoice(billId:String):Void
	"""批量开具蓝票
		@param billIdList 发票编号列表
		@return 开具发票结果
	"""
	drawBatchBlue(billIdList:[String]):[DrawInvoiceResultResponse]
	"""开具蓝票
		@param billId 发票编号
		@return 开具结果
	"""
	drawBlue(billId:String):DrawInvoiceResultResponse
	"""开具红票
		@param billId 发票编号
		@return 开具结果
	"""
	drawRed(billId:String):DrawInvoiceResultResponse
	"""冻结发票
		@param billId 发票编号
	"""
	freezeInvoice(billId:String):Void
	"""依据给予的子订单号生成发票
		@param invoiceType    发票类型：1普通发票，2增值税普通发票，3增值税专用发票
		@param subOrderNoList 指定需要生成发票的子订单编号
		@return 发票编号
	"""
	generateInvoice(invoiceType:Int!,subOrderNoList:[String]):String
	"""依据订单的合并规则生成发票
		@param orderNo 订单号
		@return 发票编号
	"""
	generateInvoiceByCombineRule(orderNo:String):String
	"""收款账号解绑定纳税人
		@param accountId  账号ID
		@param taxPayerId 纳税人ID
	"""
	unBindTaxPayer(accountId:String,taxPayerId:String):Void
	"""解冻发票
		@param billId 发票编号
	"""
	unfreezeInvoice(billId:String):Void
	"""更新子项目自动开票间隔天数
		@param subProjectId 子项目编号
		@param day          天数
	"""
	updateAutoBillDay(day:Int!):Void
	"""更新发票信息
		@param billId           发票编号
		@param invoiceUpdateDTO 发票信息
	"""
	updateInvoiceInfo(billId:String,invoiceUpdateDTO:InvoiceUpdateRequest):Void
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""批次发票订单查询条件
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
input BatchOrderBillQueryRequest @type(value:"com.fjhb.platform.core.v1.bill.kernel.gateway.graphql.request.BatchOrderBillQueryRequest") {
	"""批次号"""
	batchNum:String
	"""发票号"""
	billNo:String
	"""收货方式 1：快递 2：自取"""
	deliveryWayType:String
	"""缴费方式"""
	payWay:String
	"""运费收款方式"""
	freightPaymentWay:String
	"""买家ID"""
	buyerIds:[String]
	"""业务参数"""
	bizParam:String
	"""发票抬头"""
	billTitle:String
	"""发票抬头类别"""
	billTitleType:String
	"""支付成功检索的开始时间"""
	payStartTime:DateTime
	"""支付成功检索的结束时间"""
	payEndTime:DateTime
	"""项目ID"""
	projectId:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""仓储点id"""
	storageId:String
	"""承运商id"""
	carrierId:String
	"""发票类型：1普通发票，2增值税普通发票，3增值税专用发票"""
	invoiceType:String
	"""索取发票开始时间"""
	getBillStartTime:DateTime
	"""索取发票结束时间"""
	getBillEndTime:DateTime
	"""是否开票"""
	makeInvoice:Boolean
	"""开票开始时间"""
	makeInvoiceStartTime:DateTime
	"""开票结束时间"""
	makeInvoiceEndTime:DateTime
	"""业务对象集合列表"""
	objectList:[InvoiceObjectRequest]
	"""是否冻结"""
	frozen:Boolean
	"""发票代码"""
	billCode:String
	"""发票验证码"""
	billVeriCode:String
	"""是否电子发票"""
	electron:Boolean
	"""创建人ID"""
	creatorId:String
	"""发票所有人"""
	billOwners:[String]
	"""是否是测试数据"""
	test:Boolean
	"""发票状态查询
		发票状态| 1：未确认 2：已确认3：等待打印 4：打印中 5：打印成功 6：入库中 7：入库成功 8：入库失败9：打正票中 10 打正票失败
	"""
	states:[String]
	"""是否非税务票"""
	noTaxBill:Boolean
	"""批次单创建类型, 1、系统创建 2、买家创建 3、管理员创建 4、历史迁移 5、外部接口"""
	createType:String
	"""发票所属订单的收款账号"""
	receiptAccount:String
}
"""发票特征标记信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
input InvoiceObjectRequest @type(value:"com.fjhb.platform.core.v1.bill.kernel.gateway.graphql.request.InvoiceObjectRequest") {
	objectType:String
	objectId:String
}
"""发票更新信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
input InvoiceUpdateRequest @type(value:"com.fjhb.platform.core.v1.bill.kernel.gateway.graphql.request.InvoiceUpdateRequest") {
	"""发票抬头类型 1：个人 2：企业
		该值不传表示不修改
	"""
	invoiceTitleType:Int
	"""发票抬头
		该值不传表示不修改，传入""空字符串表示修改为空
		<pre>
		当发票抬头为1（个人）时，填写用户姓名
		当发票抬头为2（企业）时，填写企业全称
		</pre>
	"""
	invoiceTitle:String
	"""纳税人识别号
		该值不传表示不修改，传入""空字符串表示修改为空
		<pre>
		当发票抬头为1（个人）时，可以不填
		当发票抬头为2（企业）时，必须填写企业纳税人识别号
		</pre>
	"""
	taxpayerNo:String
	"""购货方地址
		该值不传表示不修改，传入""空字符串表示修改为空
	"""
	address:String
	"""购货方电话
		该值不传表示不修改，传入""空字符串表示修改为空
	"""
	phone:String
	"""购货方银行开户行
		该值不传表示不修改，传入""空字符串表示修改为空
	"""
	bankName:String
	"""购货方银行账号
		该值不传表示不修改，传入""空字符串表示修改为空
	"""
	bankAccount:String
	"""发票票面备注
		该值不传表示不修改，传入""空字符串表示修改为空
	"""
	billRemark:String
}
"""非批次发票查询条件
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
input NonBatchOrderBillQueryRequest @type(value:"com.fjhb.platform.core.v1.bill.kernel.gateway.graphql.request.NonBatchOrderBillQueryRequest") {
	"""主订单号"""
	orderNos:[String]
	"""发票号"""
	billNo:String
	"""收货方式 1：快递 2：自取"""
	deliveryWayType:String
	"""缴费方式"""
	payWay:String
	"""运费收款方式"""
	freightPaymentWay:String
	"""买家ID"""
	buyerIds:[String]
	"""业务参数"""
	bizParam:String
	"""发票抬头"""
	billTitle:String
	"""发票抬头类别"""
	billTitleType:String
	"""支付成功检索的开始时间"""
	payStartTime:DateTime
	"""支付成功检索的结束时间"""
	payEndTime:DateTime
	"""项目ID"""
	projectId:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""仓储点id"""
	storageId:String
	"""承运商id"""
	carrierId:String
	"""发票类型：1普通发票，2增值税普通发票，3增值税专用发票"""
	invoiceType:String
	"""索取发票开始时间"""
	getBillStartTime:DateTime
	"""索取发票结束时间"""
	getBillEndTime:DateTime
	"""是否开票"""
	makeInvoice:Boolean
	"""开票开始时间"""
	makeInvoiceStartTime:DateTime
	"""开票结束时间"""
	makeInvoiceEndTime:DateTime
	"""业务对象集合列表"""
	objectList:[InvoiceObjectRequest]
	"""是否冻结"""
	frozen:Boolean
	"""发票代码"""
	billCode:String
	"""发票验证码"""
	billVeriCode:String
	"""是否电子发票"""
	electron:Boolean
	"""是否测试数据"""
	test:Boolean
	"""发票状态查询
		发票状态| 1：未确认 2：已确认3：等待打印 4：打印中 5：打印成功 6：入库中 7：入库成功 8：入库失败9：打正票中 10 打正票失败
	"""
	states:[String]
	"""是否非税务发票"""
	noTaxBill:Boolean
	"""发票所属订单的收款账号"""
	receiptAccount:String
	"""冲红状态，-1/0/1/2/3，全部/未冲红/冲红中/冲红成功/冲红失败"""
	redInvoiceStatus:Int!
}
"""发票查询条件
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
input OrderBillQueryRequest @type(value:"com.fjhb.platform.core.v1.bill.kernel.gateway.graphql.request.OrderBillQueryRequest") {
	"""主订单号"""
	orderNos:[String]
	"""发票号"""
	billNo:String
	"""收货方式 1：快递 2：自取"""
	deliveryWayType:String
	"""缴费方式"""
	payWay:String
	"""运费收款方式"""
	freightPaymentWay:String
	"""买家ID"""
	buyerIds:[String]
	"""业务参数"""
	bizParam:String
	"""发票抬头"""
	billTitle:String
	"""发票抬头类别"""
	billTitleType:String
	"""支付成功检索的开始时间"""
	payStartTime:DateTime
	"""支付成功检索的结束时间"""
	payEndTime:DateTime
	"""项目ID"""
	projectId:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""仓储点id"""
	storageId:String
	"""承运商id"""
	carrierId:String
	"""发票类型：1普通发票，2增值税普通发票，3增值税专用发票"""
	invoiceType:String
	"""索取发票开始时间"""
	getBillStartTime:DateTime
	"""索取发票结束时间"""
	getBillEndTime:DateTime
	"""是否开票"""
	makeInvoice:Boolean
	"""开票开始时间"""
	makeInvoiceStartTime:DateTime
	"""开票结束时间"""
	makeInvoiceEndTime:DateTime
	"""业务对象集合列表"""
	objectList:[InvoiceObjectRequest]
	"""是否冻结"""
	frozen:Boolean
	"""发票代码"""
	billCode:String
	"""发票验证码"""
	billVeriCode:String
	"""是否电子发票"""
	electron:Boolean
	"""创建人ID"""
	creatorId:String
	"""发票所有人"""
	billOwners:[String]
	"""是否是测试数据"""
	test:Boolean
	"""批次号 仅针对批次单查询有效"""
	batchNum:String
	"""主订单创建类型 1、系统创建 2、买家创建 3、管理员创建 4、历史迁移 5、外部接口
		如果是查学员侧的发票，则入参传入2，管理员的则传入3
		值参照 CreateTypeConstant.MANAGER_CREATE等
	"""
	createType:String
	"""批次单创建类型,如果有批次号的情况下才存在 1、系统创建 2、买家创建 3、管理员创建 4、历史迁移 5、外部接口
		此字段可以查批次发票
		值参照 CreateTypeConstant.MANAGER_CREATE等
	"""
	batchOrderCreateType:String
	"""发票状态查询
		发票状态| 1：未确认 2：已确认3：等待打印 4：打印中 5：打印成功 6：入库中 7：入库成功 8：入库失败9：打正票中 10 打正票失败
	"""
	states:[String]
}
"""发票特征标记信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
type InvoiceObjectRequest1 @type(value:"com.fjhb.platform.core.v1.bill.kernel.gateway.graphql.request.InvoiceObjectRequest") {
	objectType:String
	objectId:String
}
"""批次发票信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
type BatchOrderBillResponse @type(value:"com.fjhb.platform.core.v1.bill.kernel.gateway.graphql.response.BatchOrderBillResponse") {
	"""批次号"""
	batchNum:String
	"""买家ID"""
	buyerId:String
	"""主键 发票ID"""
	id:String
	"""发票号"""
	billNo:String
	"""仓点编号"""
	storageNo:String
	"""发票状态 1：未确认 2：已确认3：等待打印 4：打印中 5：打印成功 6：入库中 7：入库成功 8：入库失败"""
	state:String
	"""objectId"""
	objectId:String
	"""是否完善 true：完善 false：未完善"""
	perfect:Boolean
	"""是否合并开票 true：合并 false：未合并"""
	combine:Boolean
	"""是否打印 true：打印 false：未打印"""
	printBill:Boolean
	"""是否配送 true：配送 false：未配送"""
	delivery:Boolean
	"""收款账户"""
	receiptAccount:String
	"""发票抬头"""
	title:String
	"""发票抬头类型 1：个人 2：企业"""
	titleType:String
	"""发票类别"""
	type:String
	"""开票金额"""
	money:BigDecimal
	"""开票内容"""
	content:String
	"""导入时间"""
	importTime:DateTime
	"""备注"""
	remark:String
	"""创建时间"""
	createTime:DateTime
	"""创建人ID"""
	creatorId:String
	"""纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账号"""
	account:String
	"""是否电子发票"""
	electron:Boolean!
	"""发票类型：1普通发票，2增值税普通发票，3增值税专用发票"""
	invoiceType:String
	"""电子邮件"""
	email:String
	"""发票代码"""
	billCode:String
	"""发票验证码"""
	billVeriCode:String
	"""是否冻结"""
	frozen:Boolean!
	"""承运商id"""
	carrierId:String
	"""商品配送方式"""
	deliverType:String
	"""发票所有人,非批次单则是购买者ID，批次单则为平台层传入的所有者"""
	billOwner:String
	"""是否测试数据"""
	test:Boolean!
	"""税额"""
	tax:BigDecimal
	"""发票备注"""
	billRemark:String
	"""是否非税务票"""
	noTaxBill:Boolean!
	"""数据权限Token"""
	token:String
}
"""开票结果信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
type DrawInvoiceResultResponse @type(value:"com.fjhb.platform.core.v1.bill.kernel.gateway.graphql.response.DrawInvoiceResultResponse") {
	"""发票开具是否成功"""
	success:Boolean
	"""发票ID"""
	billId:String
	"""错误信息"""
	errorMessage:String
}
"""发票信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
type OrderBillItemResponse @type(value:"com.fjhb.platform.core.v1.bill.kernel.gateway.graphql.response.OrderBillItemResponse") {
	"""主键 主订单号"""
	orderNo:String
	"""买家ID"""
	buyerId:String
	"""主键 发票ID"""
	id:String
	"""发票号"""
	billNo:String
	"""仓点编号"""
	storageNo:String
	"""发票状态 1：未确认 2：已确认3：等待打印 4：打印中 5：打印成功 6：入库中 7：入库成功 8：入库失败"""
	state:String
	"""objectId"""
	objectId:String
	"""是否完善 true：完善 false：未完善"""
	perfect:Boolean
	"""是否合并开票 true：合并 false：未合并"""
	combine:Boolean
	"""是否打印 true：打印 false：未打印"""
	printBill:Boolean
	"""是否配送 true：配送 false：未配送"""
	delivery:Boolean
	"""收款账户"""
	receiptAccount:String
	"""发票抬头"""
	title:String
	"""发票抬头类型 1：个人 2：企业"""
	titleType:String
	"""发票类别"""
	type:String
	"""开票金额"""
	money:BigDecimal
	"""开票内容"""
	content:String
	"""导入时间"""
	importTime:DateTime
	"""备注"""
	remark:String
	"""创建时间"""
	createTime:DateTime
	"""创建人ID"""
	creatorId:String
	"""纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账号"""
	account:String
	"""是否电子发票"""
	electron:Boolean!
	"""发票类型：1普通发票，2增值税普通发票，3增值税专用发票"""
	invoiceType:String
	"""电子邮件"""
	email:String
	"""发票代码"""
	billCode:String
	"""发票验证码"""
	billVeriCode:String
	"""是否冻结"""
	frozen:Boolean!
	"""承运商id"""
	carrierId:String
	"""商品配送方式"""
	deliverType:String
	"""业务对象集合列表"""
	objectList:[InvoiceObjectRequest1]
	"""是否测试数据"""
	test:Boolean!
	"""税额"""
	tax:BigDecimal
	"""发票备注"""
	billRemark:String
	"""冲红状态：0/1/2/3，未冲红/冲红中/冲红成功/冲红失败"""
	redInvoiceStatus:Int!
	"""冲红发票金额"""
	redMoney:BigDecimal
	"""冲红发票税额"""
	redTaxRate:BigDecimal
	"""冲红发票代码"""
	redBillCode:String
	"""冲红发票号码"""
	redBillNo:String
}
"""纳税人信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
type TaxPayerResponse @type(value:"com.fjhb.platform.core.v1.bill.kernel.gateway.graphql.response.TaxPayerResponse") {
	"""主键ID"""
	id:String
	"""名称"""
	name:String
	"""纳税人识别号"""
	taxCode:String
	"""发票地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bank:String
	"""账号"""
	account:String
}

scalar List
type BatchOrderBillResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BatchOrderBillResponse]}
type OrderBillItemResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [OrderBillItemResponse]}
