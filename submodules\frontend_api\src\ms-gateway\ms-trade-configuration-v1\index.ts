import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-trade-configuration-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-trade-configuration-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 收款账号扩展属性
<AUTHOR>
@since 2021/2/6
 */
export class ReceiveAccountExtProperty {
  /**
   * 属性值
   */
  name?: string
  /**
   * 属性值
   */
  value?: string
}

/**
 * 创建商品票面信息
<AUTHOR>
@since 2022/3/28
 */
export class CommodityInvoiceTicketConfigCreateRequest {
  /**
   * 商品税务编码
   */
  commodityCode: string
  /**
   * 税务编码关联信息
   */
  taxCodeRelation?: TaxCodeRelationCreateRequest
  /**
   * 服务名称
   */
  serviceTitle?: string
  /**
   * 单位
   */
  unitTitle?: string
  /**
   * 规格型号
   */
  specificationMode?: string
  /**
   * 是否打印数量
   */
  printQuantity: boolean
  /**
   * 是否打印单价
   */
  printPrice: boolean
  /**
   * 税率
   */
  rate?: number
  /**
   * 税务优惠(仅全电票使用):0-无优惠,1-简易征收
   */
  taxFavoured?: number
}

/**
 * 更新商品票面信息
<AUTHOR>
@since 2022/3/28
 */
export class CommodityInvoiceTicketConfigUpdateRequest {
  /**
   * 商品税务编码
   */
  commodityCode?: string
  /**
   * 税务编码关联信息
   */
  taxCodeRelation?: TaxCodeRelationUpdateRequest
  /**
   * 服务名称
   */
  serviceTitle?: string
  /**
   * 单位
   */
  unitTitle?: string
  /**
   * 规格型号
   */
  specificationMode?: string
  /**
   * 是否打印数量
   */
  printQuantity?: boolean
  /**
   * 是否打印单价
   */
  printPrice?: boolean
  /**
   * 税率
   */
  rate?: number
  /**
   * 税务优惠(仅全电票使用):0-无优惠,1-简易征收
   */
  taxFavoured?: number
  /**
   * 是否标记删除
   */
  markDelete: boolean
}

/**
 * 创建收款账号
<AUTHOR>
@since 2021/2/6
 */
export class CreateReceiveAccountRequest {
  /**
   * 收款账号（微信为商户号，支付宝为支付宝账号，建设银行为开户号）
   */
  accountNo?: string
  /**
   * 收款账户类型
<pre>
1-线上支付
2-线下支付
</pre>
   */
  accountType?: number
  /**
   * 支付渠道ID，由支付服务提供的通用支付渠道编号
   */
  paymentChannelId?: string
  /**
   * 收款帐号名称
别名
   */
  name?: string
  /**
   * 所属商户名称
没有可以不写
   */
  merchantName?: string
  /**
   * 所属商户电话
没有可以不写
   */
  merchantPhone?: string
  /**
   * 各渠道类型的收款账号扩展属性
<p>
1、支付宝收款账号 ----原型给的支付宝密钥不需要了
{
// 应用id-支付宝应用id
&quot;appId&quot;: &quot;&quot;,
// 合作者身份id
&quot;partner&quot;: &quot;&quot;,
// 支付宝应用私钥
&quot;privateKey&quot;: &quot;&quot;,
// 支付宝公钥
&quot;publicKey&quot;: &quot;&quot;
}
2、微信支付
{
// 应用id-公众账户ID
&quot;appId&quot;: &quot;&quot;,
// API秘钥，
&quot;merchantKey&quot;: &quot;&quot;,
// 证书文件名称
&quot;privateKeyFileName&quot;: &quot;&quot;,
// 证书文件地址
&quot;privateKeyPath&quot;: &quot;&quot;,
// 微信证书密钥 默认写商户号
&quot;privateKeyPWD&quot;: &quot;&quot;
}
3、线下收款账户
{
// 企业名称（开户户名）
&quot;merchantName&quot;:&quot;&quot;,
// 开户银行
&quot;depositBank&quot;:&quot;&quot;,
// 柜台号
&quot;counterNumber&quot;:&quot;&quot;
}
4、兴业支付
{
//终端编号(兴业银行提供)
&quot;terminalId&quot;:&quot;&quot;,
//应用ID，为KY字母开头的字符串，区分大小写
&quot;appId&quot;:&quot;&quot;,
//请求报文签名私钥
&quot;requestPrivateKey&quot;:&quot;&quot;,
//响应报文验签公钥
&quot;responsePublicKey&quot;:&quot;&quot;,
//请求字段加密密钥
&quot;requestParamEncryptKey&quot;:&quot;&quot;,
// 当前收款账号有使用小程序&amp;公众号支付时必填，公众账号或小程序ID
&quot;subAppid&quot;:&quot;&quot;
}
   */
  properties?: Array<ReceiveAccountExtProperty>
  /**
   * 退款方式
1-线上 2-线下
   */
  refundWay: number
  /**
   * 纳税人识别号
   */
  taxPayerId?: string
  /**
   * 付款扫码引导语
   */
  qrScanPrompt?: string
}

/**
 * 电子发票纳税人创建请求
<AUTHOR>
@since 2022/3/28
 */
export class ElectronicInvoiceTaxpayerCreateRequest {
  /**
   * 纳税人名称
   */
  name?: string
  /**
   * 纳税人识别号
   */
  taxpayerNo?: string
  /**
   * 地址
   */
  address?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 开户行
   */
  bankName?: string
  /**
   * 开户账号
   */
  bankAccount?: string
  /**
   * 最大开票金额
   */
  invoiceMaxMoney?: number
  /**
   * 收款人
   */
  payee?: string
  /**
   * 开票人
   */
  issuer?: string
  /**
   * 复核人
   */
  reviewer?: string
  /**
   * 开票平台授权信息
   */
  invoiceAuthList?: Array<InvoiceServiceAuthCreateRequest>
  /**
   * 商品票面配置
   */
  commodityTicketList?: Array<CommodityInvoiceTicketConfigCreateRequest>
}

/**
 * 电子发票纳税人更新请求
<AUTHOR>
@since 2022/3/28
 */
export class ElectronicInvoiceTaxpayerUpdateRequest {
  /**
   * 纳税人编号
   */
  taxpayerId: string
  /**
   * 纳税人名称，null表示不更新
   */
  name?: string
  /**
   * 纳税人识别号，null表示不更新
   */
  taxpayerNo?: string
  /**
   * 地址，null表示不更新
   */
  address?: string
  /**
   * 电话，null表示不更新
   */
  phone?: string
  /**
   * 开户行，null表示不更新
   */
  bankName?: string
  /**
   * 开户账号，null表示不更新
   */
  bankAccount?: string
  /**
   * 最大开票金额，null表示不更新
   */
  invoiceMaxMoney?: number
  /**
   * 收款人，null表示不更新
   */
  payee?: string
  /**
   * 开票人，null表示不更新
   */
  issuer?: string
  /**
   * 复核人，null表示不更新
   */
  reviewer?: string
  /**
   * 开票平台授权信息，null表示不更新
   */
  invoiceAuthList?: Array<InvoiceServiceAuthUpdateRequest>
  /**
   * 商品票面配置，null表示不更新
   */
  commodityTicketList?: Array<CommodityInvoiceTicketConfigUpdateRequest>
}

export class InvoiceCategoryConfig1 {
  /**
   * 允许开具的发票种类
1 - 普通发票
2 - 增值税普通发票
3 - 增值税专用发票
   */
  invoiceCategory: number
  /**
   * 允许发票抬头
1 - 个人
2 - 企业
当openInvoiceType&#x3D;0时，该值为null
   */
  invoiceTitleTypes?: Array<number>
  /**
   * 开票方式
1 - 线上开票
2 - 线下开票
当openInvoiceType&#x3D;0时，该值为null
   */
  invoiceMethod: number
  /**
   * 发票类型
1 - 电子票
2 - 纸质票
   */
  invoiceType?: number
  /**
   * 发票备注类型
0-未配置
1-学员填写（学员、集体报名管理员）
2-统一设置(运营端、管理员)
   */
  invoiceRemarksType?: number
  /**
   * 发票备注内容
   */
  invoiceRemarksContent?: string
}

/**
 * 发票配置
 */
export class InvoiceConfigRequest {
  /**
   * 开放发票类型
   */
  openInvoiceType: number
  /**
   * 是否允许索取发票
   */
  allowAskFor?: boolean
  /**
   * 索取发票截止日期（格式MM/dd）
   */
  askForInvoiceDeadline?: string
  /**
   * 索取发票年度类型
   */
  askForInvoiceYearType?: number
  /**
   * 允许开具发票种类
   */
  allowInvoiceCategoryList?: Array<InvoiceCategoryConfig1>
}

/**
 * 创建开票平台授权信息
<AUTHOR>
@since 2022/3/28
 */
export class InvoiceServiceAuthCreateRequest {
  /**
   * 开票提供商编号
5 - 诺诺，
6 - 福建百旺
7 - 诺诺V2
8 - 诺税通V2全电票
9 - 诺税通V2
   */
  invoiceProviderId: string
  /**
   * 应用访问标识/平台公钥
   */
  accessKey?: string
  /**
   * 授权码/企业私钥
   */
  secretAssessKey?: string
  /**
   * 部门ID
   */
  deptId?: string
  /**
   * 分机号
   */
  extensionNumber?: string
  /**
   * 企业代码
   */
  enterpriseCode?: string
  /**
   * 办税人身份证号
   */
  taxpayerIdNumber?: string
  /**
   * 备注
   */
  remark?: string
}

/**
 * 更新开票平台授权信息
<AUTHOR>
@since 2022/3/28
 */
export class InvoiceServiceAuthUpdateRequest {
  /**
   * 开票提供商编号
5 - 诺诺，
6 - 福建百旺
7 - 诺诺V2
   */
  invoiceProviderId?: string
  /**
   * 应用访问标识/平台公钥
   */
  accessKey?: string
  /**
   * 授权码/企业私钥
   */
  secretAssessKey?: string
  /**
   * 部门ID
   */
  deptId?: string
  /**
   * 分机号
   */
  extensionNumber?: string
  /**
   * 企业代码
   */
  enterpriseCode?: string
  /**
   * 办税人身份证号
   */
  taxpayerIdNumber?: string
  /**
   * 备注
   */
  remark?: string
  /**
   * 是否标记删除
   */
  markDelete: boolean
}

/**
 * 请求操作用户登录信息
<AUTHOR>
@since 2023/8/10
 */
export class OperateEITaxpayerUserLogonConfigRequest {
  /**
   * 纳税人id
   */
  taxpayerId: string
  /**
   * 纳税人识别号
   */
  taxpayerNo: string
  /**
   * 登录账号
电子税局登录账号
   */
  loginAccount: string
  /**
   * 登录密码
电子税局登录密码
   */
  loginPassword: string
}

/**
 * 购买渠道创建信息
 */
export class PurchaseChannelConfigCreateRequest {
  /**
   * 渠道类型
   */
  channelType: number
  /**
   * 渠道名称
   */
  channelName?: string
  /**
   * 终端列表
   */
  terminalList?: Array<TerminalCreateRequest>
  /**
   * 发票配置
   */
  invoiceConfig?: InvoiceConfigRequest
}

/**
 * 购买渠道创建信息
 */
export class PurchaseChannelConfigForAdminCreateRequest {
  /**
   * 服务商id
   */
  servicerId?: string
  /**
   * 渠道类型
   */
  channelType: number
  /**
   * 渠道名称
   */
  channelName?: string
  /**
   * 终端列表
   */
  terminalList?: Array<TerminalCreateRequest>
  /**
   * 发票配置
   */
  invoiceConfig?: InvoiceConfigRequest
}

/**
 * 购买渠道配置更新请求
 */
export class PurchaseChannelConfigUpdateRequest {
  /**
   * 购买渠道id
   */
  purchaseChannelId: string
  /**
   * 终端列表 null和空代表不更新 几何元素代表需要更新的值
   */
  terminalList?: Array<TerminalUpdateRequest>
  /**
   * 发票配置
   */
  invoiceConfig?: InvoiceConfigRequest
}

/**
 * 创建税务编码关联信息
 */
export class TaxCodeRelationCreateRequest {
  /**
   * 商品类目ID
   */
  commodityCategoryId?: string
}

/**
 * 更新税务编码关联信息
 */
export class TaxCodeRelationUpdateRequest {
  /**
   * 商品类目ID
   */
  commodityCategoryId?: string
}

export class TerminalCreateRequest {
  /**
   * 终端代码
   */
  terminalCode: string
  /**
   * 是否关闭
   */
  isClosed: boolean
  /**
   * 收款账号编号列表
   */
  receiveAccountIdList?: Array<string>
}

/**
 * 终端配置
 */
export class TerminalUpdateRequest {
  /**
   * 终端代码
   */
  terminalCode: string
  /**
   * 是否关闭
   */
  isClosed: boolean
  /**
   * 收款账号编号列表  null代表不更新 集合元素代表所有的值 ;空数组代表移除所有;
   */
  receiveAccountIdList?: Array<string>
}

/**
 * 创建收款账号
<AUTHOR>
@since 2021/2/6
 */
export class UpdateReceiveAccountRequest {
  /**
   * 收款账号编号
   */
  receiveAccountId?: string
  /**
   * 收款账号（微信为商户号，支付宝为支付宝账号，建设银行为开户号）
   */
  accountNo?: string
  /**
   * 收款帐号名称
   */
  name?: string
  /**
   * 所属商户名称，null表示不更新
   */
  merchantName?: string
  /**
   * 所属商户电话，null表示不更新
   */
  merchantPhone?: string
  /**
   * 退款方式 -1不更新
1-线上 2-线下
   */
  refundWay: number
  /**
   * 纳税人识别号
null表示不更新
   */
  taxPayerId?: string
  /**
   * 各渠道类型的收款账号扩展属性
<p>
1、支付宝收款账号 ----原型给的支付宝密钥不需要了
{
// 应用id-支付宝应用id
&quot;appId&quot;: &quot;&quot;,
// 合作者身份id
&quot;partner&quot;: &quot;&quot;,
// 支付宝应用私钥
&quot;privateKey&quot;: &quot;&quot;,
// 支付宝公钥
&quot;publicKey&quot;: &quot;&quot;
}
2、微信支付
{
// 应用id-公众账户ID
&quot;appId&quot;: &quot;&quot;,
// 微信证书秘钥，默认写商户号
&quot;merchantKey&quot;: &quot;&quot;,
// 证书文件名称
&quot;privateKeyFileName&quot;: &quot;&quot;,
// 证书文件地址
&quot;privateKeyPath&quot;: &quot;&quot;,
// api秘钥
&quot;privateKeyPWD&quot;: &quot;&quot;
}
3、线下收款账户
{
// 企业名称（开户户名）
&quot;merchantName&quot;:&quot;&quot;,
// 开户银行
&quot;depositBank&quot;:&quot;&quot;,
// 柜台号
&quot;counterNumber&quot;:&quot;&quot;
}
</p>
   */
  properties?: Array<ReceiveAccountExtProperty>
  /**
   * 付款扫码引导语
   */
  qrScanPrompt?: string
}

/**
 * @Description
<AUTHOR>
@Date 2023/8/4 10:36
 */
export class TaxCodeRelationConfig {
  /**
   * 关联类型
   */
  relationType: number
  /**
   * 商品类目ID
   */
  commodityCategoryId: string
  /**
   * 服务商编号
   */
  servicerId: string
}

export class InvoiceCategoryConfig {
  /**
   * 允许开具的发票种类
1 - 普通发票
2 - 增值税普通发票
3 - 增值税专用发票
   */
  invoiceCategory: number
  /**
   * 允许发票抬头
1 - 个人
2 - 企业
当openInvoiceType&#x3D;0时，该值为null
   */
  invoiceTitleTypes: Array<number>
  /**
   * 开票方式
1 - 线上开票
2 - 线下开票
当openInvoiceType&#x3D;0时，该值为null
   */
  invoiceMethod: number
  /**
   * 发票类型
1 - 电子票
2 - 纸质票
   */
  invoiceType: number
  /**
   * 发票备注类型
0-未配置
1-学员填写（学员、集体报名管理员）
2-统一设置(运营端、管理员)
   */
  invoiceRemarksType: number
  /**
   * 发票备注内容
   */
  invoiceRemarksContent: string
}

/**
 * 商品票面信息
<AUTHOR>
@since 2022/3/28
 */
export class CommodityInvoiceTicketConfigResponse {
  /**
   * 商品税务编码
   */
  commodityCode: string
  /**
   * 税务编码关联信息
   */
  taxCodeRelation: TaxCodeRelationConfig
  /**
   * 服务名称
   */
  serviceTitle: string
  /**
   * 单位
   */
  unitTitle: string
  /**
   * 规格型号
   */
  specificationMode: string
  /**
   * 是否打印数量
   */
  printQuantity: boolean
  /**
   * 是否打印单价
   */
  printPrice: boolean
  /**
   * 税率
   */
  rate: number
  /**
   * 税务优惠(仅全电票使用):0-无优惠,1-简易征收
   */
  taxFavoured: number
}

/**
 * 纳税人配置信息
<AUTHOR>
@since 2022/3/28
 */
export class ElectronicInvoiceTaxpayerResponse {
  /**
   * 纳税人名称
   */
  name: string
  /**
   * 纳税人识别号
   */
  taxpayerNo: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 开户账号
   */
  bankAccount: string
  /**
   * 最大开票金额
   */
  invoiceMaxMoney: number
  /**
   * 收款人
   */
  payee: string
  /**
   * 开票人
   */
  issuer: string
  /**
   * 复核人
   */
  reviewer: string
  /**
   * 开票平台授权信息
   */
  invoiceAuthList: Array<InvoiceServiceAuthResponse>
  /**
   * 商品票面配置
   */
  commodityTicketList: Array<CommodityInvoiceTicketConfigResponse>
  /**
   * 登录账号
电子税局登录账号
   */
  loginAccount: string
  /**
   * 登录密码
电子税局登录密码
   */
  loginPassword: string
}

/**
 * 发票配置
<AUTHOR>
@since 2022/3/24
 */
export class InvoiceConfigResponse {
  /**
   * 开放发票类型
0 - 不开放
1 - 自主选择
2 - 强制提供
   */
  openInvoiceType: number
  /**
   * 是否允许索取发票
当openInvoiceType&#x3D;0时，该值为null
   */
  allowAskFor: boolean
  /**
   * 索取发票年度类型
1 - 当年度
2 - 下一个年度
当openInvoiceType&#x3D;0时，该值为null
   */
  askForInvoiceYearType: number
  /**
   * 索取发票截止日期，格式（MM/dd）,如：5月3日，则05/03
当openInvoiceType&#x3D;0时，该值为null
   */
  askForInvoiceDeadline: string
  /**
   * 允许开具的发票种类
1 - 普通发票
2 - 增值税普通发票
3 - 增值税专用发票
当openInvoiceType&#x3D;0时，该值为null
   */
  allowInvoiceCategoryList: Array<InvoiceCategoryConfig>
}

/**
 * 开票平台授权信息更新
<AUTHOR>
@since 2022/3/28
 */
export class InvoiceServiceAuthResponse {
  /**
   * 开票提供商编号
5 - 诺诺，
7 - 诺诺V2
   */
  invoiceProviderId: string
  /**
   * 应用访问标识,null 表示不更新
   */
  accessKey: string
  /**
   * 授权码
   */
  secretAssessKey: string
  /**
   * 企业代码
   */
  enterpriseCode: string
  /**
   * 办税人身份证号
   */
  taxpayerIdNumber: string
  /**
   * 部门ID
   */
  deptId: string
  /**
   * 分机号
   */
  extensionNumber: string
  /**
   * 备注
   */
  remark: string
}

export class PaymentChannelIdResponse {
  /**
   * ID值
   */
  paymentChannelId: string
  /**
   * 描述
   */
  describe: string
}

/**
 * <AUTHOR>
@since 2022/3/31
 */
export class PrepareElectronicInvoiceResponse {
  /**
   * 开票平台类型
提供商如下：
5 - 诺诺
   */
  providerList: Array<string>
  /**
   * 商品税务编码列表
   */
  commodityCodeList: Array<CommodityCode>
}

export class CommodityCode {
  /**
   * 商品税务编码
   */
  code: string
  /**
   * 税务名称
   */
  name: string
}

/**
 * 购买渠道信息
<AUTHOR>
@since 2022/3/25
 */
export class PreparePurchaseChannelResponse {
  /**
   * 网校支持的终端代码列表
Web - Web端
H5 - H5端
   */
  terminalCodeList: Array<string>
  /**
   * 网校下购买渠道
   */
  purchaseChannelList: Array<PurchaseChannel>
}

export class PurchaseChannel {
  /**
   * 购买渠道id,如果该值不存在或者null,代表未创建当前购买渠道
   */
  purchaseChannelId: string
  /**
   * 渠道类型 用户自主购买:1,集体缴费:2,管理员导入:3,集体报名个人缴费渠道:4
   */
  channelType: number
  /**
   * 渠道名称
   */
  channelName: string
  /**
   * 状态 (1-启用，0-禁用）
   */
  status: number
  /**
   * 终端列表
   */
  terminalList: Array<TerminalResponse>
  /**
   * 发票配置
   */
  invoiceConfig: InvoiceConfigResponse
  /**
   * 创建人
   */
  createdUserId: string
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 终端配置
 */
export class TerminalResponse {
  /**
   * 终端代码
   */
  terminalCode: string
  /**
   * 是否关闭
   */
  isClosed: boolean
  /**
   * 收款账号编号列表
   */
  receiveAccountIdList: Array<string>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 从apollo配置中获取支持的支付渠道
   * @return {@link List<PaymentChannelIdResponse>}
   * <AUTHOR> By Cb
   * @date 2022/10/12 16:55
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPaymentChannelIdList(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPaymentChannelIdList,
    operation?: string
  ): Promise<Response<Array<PaymentChannelIdResponse>>> {
    return commonRequestApi<Array<PaymentChannelIdResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param receiveAccountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async isAllowUpdate(
    receiveAccountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.isAllowUpdate,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { receiveAccountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建电子发票纳税人
   * @param request 纳税人信息
   * @returns 返回纳税人编号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createElectronicInvoiceTaxpayer(
    request: ElectronicInvoiceTaxpayerCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createElectronicInvoiceTaxpayer,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建购买渠道
   * @param createRequest 购买渠道创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createPurchaseChannel(
    createRequest: PurchaseChannelConfigCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createPurchaseChannel,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建购买渠道(管理员)
   * @param createRequest 购买渠道创建信息
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param createRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createPurchaseChannelForAdmin(
    createRequest: PurchaseChannelConfigForAdminCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createPurchaseChannelForAdmin,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建导入开通购买渠道
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createPurchaseChannelForImportRequest(
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createPurchaseChannelForImportRequest,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 创建收款账号
   * @param info 收款账号信息
   * @param mutate 查询 graphql 语法文档
   * @param info 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createReceiveAccount(
    info: CreateReceiveAccountRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createReceiveAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { info },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除收款账号
   * @param receiveAccountId 收款账号编号
   * @param mutate 查询 graphql 语法文档
   * @param receiveAccountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteReceiveAccount(
    receiveAccountId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteReceiveAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { receiveAccountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 禁用收款账号
   * @param receiveAccountId 收款账号编号
   * @param mutate 查询 graphql 语法文档
   * @param receiveAccountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableReceiveAccount(
    receiveAccountId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disableReceiveAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { receiveAccountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用收款账号
   * @param receiveAccountId 收款账号编号
   * @param mutate 查询 graphql 语法文档
   * @param receiveAccountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableReceiveAccount(
    receiveAccountId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableReceiveAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { receiveAccountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 依据纳税人编号获取纳税人配置信息详细
   * @param taxpayerId 纳税人编号
   * @return 纳税人配置信息
   * @param mutate 查询 graphql 语法文档
   * @param taxpayerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findElectronicInvoiceTaxpayer(
    taxpayerId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.findElectronicInvoiceTaxpayer,
    operation?: string
  ): Promise<Response<ElectronicInvoiceTaxpayerResponse>> {
    return commonRequestApi<ElectronicInvoiceTaxpayerResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { taxpayerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取当前网校下所有配置的纳税人
   * @return 纳税人编号
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findElectronicInvoiceTaxpayerList(
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.findElectronicInvoiceTaxpayerList,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建用户登录配置
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async operateUserLoginConfig(
    request: OperateEITaxpayerUserLogonConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.operateUserLoginConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 准备配置电子发票
   * 提供商如下：
   * 5 - 诺诺
   * 6 - 福建百旺
   * 7 - 诺诺V2
   * @return 电子发票配置提供商
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async prepareElectronicInvoiceConfig(
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.prepareElectronicInvoiceConfig,
    operation?: string
  ): Promise<Response<PrepareElectronicInvoiceResponse>> {
    return commonRequestApi<PrepareElectronicInvoiceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据网校id获取购买渠道类型
   * @return preparePurchaseChannelResponse 购买渠道信息
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async preparePurchaseChannel(
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.preparePurchaseChannel,
    operation?: string
  ): Promise<Response<PreparePurchaseChannelResponse>> {
    return commonRequestApi<PreparePurchaseChannelResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据服务商id获取购买渠道类型(管理员)
   * @param servicerId 服务商id
   * @return preparePurchaseChannelResponse 购买渠道信息
   * @param mutate 查询 graphql 语法文档
   * @param servicerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async preparePurchaseChannelForAdmin(
    servicerId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.preparePurchaseChannelForAdmin,
    operation?: string
  ): Promise<Response<PreparePurchaseChannelResponse>> {
    return commonRequestApi<PreparePurchaseChannelResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { servicerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新电子发票纳税人
   * @param request 电子发票纳税人更新请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateElectronicInvoiceTaxpayer(
    request: ElectronicInvoiceTaxpayerUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateElectronicInvoiceTaxpayer,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 购买渠道更新
   * @param updateRequest 购买渠道配置请求
   * @param mutate 查询 graphql 语法文档
   * @param updateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updatePurchaseChannel(
    updateRequest: PurchaseChannelConfigUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updatePurchaseChannel,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新收款账号
   * @param info 更新的收款账号信息
   * @param mutate 查询 graphql 语法文档
   * @param info 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateReceiveAccount(
    info: UpdateReceiveAccountRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateReceiveAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { info },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
