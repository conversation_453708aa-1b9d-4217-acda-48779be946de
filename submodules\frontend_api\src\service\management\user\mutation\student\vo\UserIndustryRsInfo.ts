import { CreateUserIndustryRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'

/*
  人社信息
*/
class UserIndustryRsInfo extends CreateUserIndustryRequest {
  /**
   * 所属行业
   */
  industryId: string = undefined
  /**
   * 一级专业类别id
   */
  firstProfessionalCategory?: string = undefined
  /**
   * 二级专业类别id
   */
  secondProfessionalCategory?: string = undefined
  /**
   * 一级专业类别名称
   */
  firstProfessionalCategoryName: string = undefined
  /**
   * 二级专业类别名称
   */
  secondProfessionalCategoryName?: string = undefined
  /**
   * 职称等级
   */
  professionalQualification: string = undefined
  /*
   * 培训类别专业
   */
  majorAndCategory: string[] = []
  /**
   * 人员类别（职业卫生行业）
   */
  personnelCategory: string = undefined
  /**
   * 岗位类别（职业卫生行业）
   */
  positionCategory: string = undefined
  /**
   * 技术等级（工勤行业）
   */
  professionalLevel: string = undefined
  /**
   * 工种（工勤行业）
   */
  jobCategoryId: string = undefined
  /**
   * 学段（教师行业）code
   */
  grade: number = undefined
  /**
   * 学科（教师行业）code
   */
  subject: number = undefined
  /**
   * 学段（教师行业）
   */
  learningPhase: string = undefined
  /**
   * 学科（教师行业）
   */
  disciplin: string = undefined
  /**
   * 证书类型（药师行业）
   */
  certificatesType: string = undefined
  /**
   * 执业类别（药师行业）
   */
  practitionerCategory: string = undefined
}

export default UserIndustryRsInfo
