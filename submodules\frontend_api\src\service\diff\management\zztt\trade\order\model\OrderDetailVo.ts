import OrderDetailSchoolVo from '@api/service/management/trade/single/order/query/vo/OrderDetailVo'
import { OrderResponse, SchemeResourceResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import QueryPlatform from '@api/service/diff/common/zztt/dictionary/QueryPlatform'
import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/zztt/trade/enums/SaleChannelType'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'

/**
 * @description
 */
class OrderDetailVo extends OrderDetailSchoolVo {
  /**
   * 对接第三方平台
   */
  thirdPartyPlatform = ''

  /**
   * 方案类型发货商品
   */
  schemeType: SchemeTypeEnum = null
  static from(orderResponse: OrderResponse) {
    const detail = Object.assign(new OrderDetailVo(), OrderDetailSchoolVo.from(orderResponse))
    if (orderResponse.saleChannel == SaleChannelEnum.huayi) {
      detail.thirdPartyPlatform = SaleChannelType.map.get(orderResponse.saleChannel)
    } else if (orderResponse.subOrderItems?.[0]?.deliveryCommoditySku?.tppTypeId) {
      detail.thirdPartyPlatform = QueryPlatform.map.get(
        orderResponse.subOrderItems[0].deliveryCommoditySku.tppTypeId
      )?.name
    }
    detail.schemeType =
      SchemeTypeEnum[
        (orderResponse.subOrderItems?.[0]?.deliveryCommoditySku?.resource as SchemeResourceResponse)?.schemeType
      ]
    return detail
  }
}

export default OrderDetailVo
