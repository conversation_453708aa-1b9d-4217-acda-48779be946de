import QueryContainCoursePackageClass from '@api/service/management/train-class/query/QueryContainCoursePackageClass'
import QueryTrainClassDetailClass from '@api/service/management/train-class/query/QueryTrainClassDetailClass'
import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
import QueryExchangeTrainClass from '@api/service/management/train-class/query/QueryExchangeTrainClass'
import QueryMyTrainClassDetail from '@api/service/management/train-class/query/QueryMyTrainClassDetail'
import QueryStudentTrainClass from '@api/service/management/train-class/query/QueryStudentTrainClass'

/**
 * 培训班查询工厂类
 */
class QueryTrainClassFactory {
  // region properties
  /**
   * 获取查询学员培训班对象
   */
  get queryStudentTrainClass() {
    return new QueryStudentTrainClass()
  }

  // endregion
  // region methods
  /**
   *获取查询引用某个课程包的培训班列表对象
   */
  getQueryContainCoursePackageClass() {
    return new QueryContainCoursePackageClass()
  }
  /**
   *获取运营域获取培训班商品详情
   */
  getQueryTrainClassDetailClass() {
    return new QueryTrainClassDetailClass()
  }
  /**
   *获取运营域获取学员培训班详情
   */
  getQueryUserTrainClassDetailClass() {
    return new QueryMyTrainClassDetail()
  }
  /**
   *获取运营域获取培训班商品列表
   */
  getQueryTrainClassCommodityList() {
    return new QueryTrainClassCommodityList()
  }

  /**
   * 获取运营域换班培训班商品查询对象
   */
  getQueryExchangeTrainClass() {
    return new QueryExchangeTrainClass()
  }
  // endregion
}
export default QueryTrainClassFactory
