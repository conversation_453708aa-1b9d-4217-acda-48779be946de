import { Component, Ref, Vue } from 'vue-property-decorator'
import rules from '@hbfe/jxjy-admin-account/src/role/rule'
import { ElForm } from 'element-ui/types/form'

@Component
class Operation extends Vue {
  rules = rules
  @Ref('form')
  form: ElForm

  async save() {
    if (!this.form) return
    const validateResult = await this.form.validate()
    if (!validateResult) {
      this.$message.warning('请完善表单！')
    }
  }
}

export default Operation
