import AbstractEnum from '@api/service/common/enums/AbstractEnum'
import { TrainingTypeEnum } from '@api/service/common/scheme/enum/TrainingType'

/**
 * @description 培训形式枚举
 * online 网授班
 * offline 面授班
 * mixed 面网授班
 */
export enum TrainingModeEnum {
  online = 'trainingWay0001',
  offline = 'trainingWay0003',
  mixed = 'trainingWay0002'
}

/**
 * @description 培训形式
 */
class TrainingMode extends AbstractEnum<TrainingModeEnum> {
  static enum = TrainingModeEnum

  constructor(status?: TrainingModeEnum) {
    super()
    this.current = status
    this.map.set(TrainingModeEnum.online, '网授')
    this.map.set(TrainingModeEnum.offline, '面授')
    this.map.set(TrainingModeEnum.mixed, '面网授')
  }

  /**
   * 根据培训形式获取培训类型
   * @param trainingMode 培训形式
   */
  getTrainingTypeByTrainingMode(trainingMode: TrainingModeEnum): TrainingTypeEnum {
    let result: TrainingTypeEnum = null
    if (trainingMode === TrainingModeEnum.online) {
      result = TrainingTypeEnum.online
    }
    if (trainingMode === TrainingModeEnum.offline) {
      result = TrainingTypeEnum.offline
    }
    if (trainingMode === TrainingModeEnum.mixed) {
      result = TrainingTypeEnum.mixed
    }
    return result
  }
}

export default new TrainingMode()
