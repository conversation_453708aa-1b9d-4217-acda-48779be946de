import findById from './queries/findById.graphql'
import getContent from './queries/getContent.graphql'
import getContentByIds from './queries/getContentByIds.graphql'
import getLearningSchemeDetail from './queries/getLearningSchemeDetail.graphql'
import isCanDownOrDeleteScheme from './queries/isCanDownOrDeleteScheme.graphql'
import listAllSkuPropertyAndOption from './queries/listAllSkuPropertyAndOption.graphql'
import listInterestCourse from './queries/listInterestCourse.graphql'
import listIssue from './queries/listIssue.graphql'
import listYearSkuOption from './queries/listYearSkuOption.graphql'
import pageIssue from './queries/pageIssue.graphql'
import pageLearningScheme from './queries/pageLearningScheme.graphql'
import createBTPXLS from './mutates/createBTPXLS.graphql'
import createLS from './mutates/createLS.graphql'
import offShelfBTPXLS from './mutates/offShelfBTPXLS.graphql'
import updateBTPXLS from './mutates/updateBTPXLS.graphql'
import updateIssue from './mutates/updateIssue.graphql'
import updateLS from './mutates/updateLS.graphql'

export {
  findById,
  getContent,
  getContentByIds,
  getLearningSchemeDetail,
  isCanDownOrDeleteScheme,
  listAllSkuPropertyAndOption,
  listInterestCourse,
  listIssue,
  listYearSkuOption,
  pageIssue,
  pageLearningScheme,
  createBTPXLS,
  createLS,
  offShelfBTPXLS,
  updateBTPXLS,
  updateIssue,
  updateLS
}
