import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-fjzjbx-data-repair-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-fjzjbx-data-repair-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * @Author: chenzeyu
@CreateTime: 2024-10-12  14:58
@Description: TODO
 */
export class FjzjBxDataRepairRequest {
  pageSize: number
}

/**
 * @Author: chenzeyu
@CreateTime: 2024-10-11  13:48
@Description: TODO
 */
export class FjzjBxDataRepairTestPageRequest {
  pageNo: number
  pageSize: number
}

/**
 * @Author: chenzeyu
@CreateTime: 2024-10-11  13:49
@Description: TODO
 */
export class FjzjBxDataRepairTestUserRequest {
  userIdList?: Array<string>
}

/**
 * @Author: chenzeyu
@CreateTime: 2024-10-17  11:42
@Description: TODO
 */
export class FjzjHandleStudentNoListRequest {
  studentNoList?: Array<string>
}

/**
 * @Author: chenzeyu
@CreateTime: 2024-10-16  11:01
@Description: TODO
 */
export class FjzjRetryArrangeFailedTaskRequest {
  mainTaskId?: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async fjzjBxDataRepair(
    request: FjzjBxDataRepairRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.fjzjBxDataRepair,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async fjzjBxDataRepairTestPage(
    request: FjzjBxDataRepairTestPageRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.fjzjBxDataRepairTestPage,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async fjzjBxDataRepairTestUser(
    request: FjzjBxDataRepairTestUserRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.fjzjBxDataRepairTestUser,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async fjzjHandleStudentNoList(
    request: FjzjHandleStudentNoListRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.fjzjHandleStudentNoList,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async fjzjRetryArrangeFailedTask(
    request: FjzjRetryArrangeFailedTaskRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.fjzjRetryArrangeFailedTask,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
