/**
 * 学员学习课程场景
 */
import ApplyStudentLearningToken from '@api/service/common/token/ApplyStudentLearningToken'
import StudentLearningCourseTypeEnum from '@api/service/customer/learning/scene/gates/enum/StudentLearningCourseTypeEnum'
import StudentLearningSceneProof from '@api/service/customer/learning/scene/proofs/StudentLearningSceneProof'
import ApplyCourseLearningTokenFactory from '@api/service/common/token/ApplyCourseLearningTokenFactory'

class StudentAppraiseCourseSceneGate {
  private readonly proof: StudentLearningSceneProof
  schemeType: StudentLearningCourseTypeEnum
  applyCourseLearningTokenFactory: ApplyCourseLearningTokenFactory

  constructor(proof: StudentLearningSceneProof, schemeType: StudentLearningCourseTypeEnum) {
    this.proof = proof
    this.schemeType = schemeType
    this.applyCourseLearningTokenFactory = new ApplyCourseLearningTokenFactory(schemeType, proof)
  }

  /**
   * 申请进入场景门票
   * 进入场景预演：
   * 1. 申请学员 token
   * 2.【学员 token】申请课程学习 token
   */
  async applyStudentLearningCourseToken(): Promise<string> {
    const applyStudentLearningToken = new ApplyStudentLearningToken(this.proof.qualificationId, this.proof.learningId)
    await applyStudentLearningToken.apply()
    // 申请失败不进行下一步
    const token = await this.applyCourseLearningTokenFactory.getStudentCourseLearningToken(
      applyStudentLearningToken.token
    )
    return token as string
  }
}

export default StudentAppraiseCourseSceneGate
