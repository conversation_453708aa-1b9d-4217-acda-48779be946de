<template>
  <div>
    <el-select
      v-model="skuModel.year"
      popper-class="data-screen"
      class="ty-item ty-item-1"
      placeholder="请选择培训年度"
      clearable
    >
      <el-option
        v-for="item in yearList"
        :key="item.value"
        :label="item.optionName"
        :value="item.optionCode"
      ></el-option>
    </el-select>
    <hb-select-unit v-model="skuModel.unitId"></hb-select-unit>
  </div>
</template>

<script lang="ts">
  import { Component, Model, Vue } from 'vue-property-decorator'
  import CommonModule from '@api/service/common/common/CommonModule'
  import { SkuValue } from '@api/service/common/models/common/SkuValue'

  @Component
  export default class extends Vue {
    @Model('change', { type: SkuValue }) readonly skuModel!: SkuValue
    async init() {
      await CommonModule.init()
    }

    get yearList() {
      return CommonModule.getYearList
    }

    // 加载sku属性
    async created() {
      this.init()
    }
  }
</script>
