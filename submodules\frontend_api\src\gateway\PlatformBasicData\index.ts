import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformBasicData'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformBasicData'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum SpecialTopicTypeEnums {
  SHE_QU_JIAO_ZHENG = 'SHE_QU_JIAO_ZHENG'
}
export enum UserJobState {
  deleted = 'deleted',
  deletedAndUnscheduled = 'deletedAndUnscheduled',
  unstart = 'unstart',
  running = 'running',
  paused = 'paused',
  interrupted = 'interrupted',
  resumed = 'resumed',
  deletedFromScheduler = 'deletedFromScheduler',
  executed = 'executed',
  fail = 'fail',
  addedToScheduler = 'addedToScheduler',
  toExecuted = 'toExecuted',
  deleteAllRelevance = 'deleteAllRelevance',
  deleteSchedulerRelevance = 'deleteSchedulerRelevance',
  rescheduled = 'rescheduled',
  updated = 'updated',
  updatedAndRescheduled = 'updatedAndRescheduled'
}
export enum AsyncExecuteResultEnum {
  SUCCESS = 'SUCCESS',
  FAIL = 'FAIL',
  PART_SUCCESS = 'PART_SUCCESS',
  ERROR = 'ERROR'
}

// 类

/**
 * 任务查询参数
@author: eleven
@date: 2020/4/14
 */
export class UserJobParamDTO {
  /**
   * 任务组名
   */
  group?: string
  /**
   * 任务当前的运行状态
   */
  userJobState?: UserJobState
  /**
   * 开始处理时间
   */
  startDate?: string
  /**
   * 处理完成时间
   */
  endDate?: string
}

/**
 * 二维码图片请求
 */
export class PromoteQRCodeRequestDTO {
  /**
   * page参数
   */
  page: string
  /**
   * scene参数
   */
  scene: string
  /**
   * width参数
   */
  width: string
}

export class RoleCreateDto {
  id?: string
  name?: string
  description?: string
}

export class RolePermissionDto {
  roleMessage?: RoleCreateDto
  nodeSelectedIdArray?: Array<string>
  itemSelectedIdArray?: Array<string>
  rootSelectedIdArray?: Array<string>
}

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 异步任务日志
@author: eleven
@date: 2020/4/14
 */
export class JobLogDTO {
  /**
   * 异步任务的主键ID
   */
  id: string
  /**
   * 执行状态
   */
  state: UserJobState
  /**
   * 异步任务的名称
   */
  name: string
  /**
   * 异步任务的组名
   */
  group: string
  /**
   * 操作人编号
   */
  operatorId: string
  /**
   * 操作人
比较稳定掉接口，直接返回
   */
  operator: string
  /**
   * 处理总数量
   */
  rowSum: number
  /**
   * 成功数
   */
  rowSuccess: number
  /**
   * 失败数
   */
  rowFail: number
  /**
   * 源文件地址
   */
  originalUrl: string
  /**
   * 处理结果文件
导入：导入成功的数据文件
导出：导出的数据文件
   */
  resultUrl: string
  /**
   * 处理失败的文件地址(导入任务的字段)
   */
  failUrl: string
  /**
   * 开始处理时间
   */
  startDate: string
  /**
   * 处理完成时间
   */
  endDate: string
  /**
   * 导入结果
   */
  result: AsyncExecuteResultEnum
  /**
   * 异常原因
   */
  message: string
}

/**
 * Author:FangKunSen
Time:2021-02-02,19:06
 */
export class LazyCommodityDTO {
  /**
   * 商品id
   */
  commodityId: string
  /**
   * 商品名
   */
  commoditySaleTitle: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方案名
   */
  schemeName: string
  /**
   * 期别id
   */
  stageId: string
  /**
   * 期别名
   */
  stageName: string
  /**
   * 期数id
   */
  issueId: string
  /**
   * 期数名
   */
  issueName: string
  /**
   * 方案封面图路径
   */
  schemePicturePath: string
  /**
   * 商品销售卖点
   */
  commoditySellingPoint: string
  /**
   * 商品描述介绍
   */
  commodityDescription: string
  /**
   * 商品描述介绍小程序
   */
  commodityDescriptionUniApp: string
  /**
   * 商品是否开启web渠道(即是否开放报名)
   */
  allowWebChannel: boolean
  /**
   * 商品是否开启导入开通渠道
   */
  allowImportChannel: boolean
  /**
   * 商品是否开启集体缴费渠道
   */
  allowBatchChannel: boolean
  /**
   * 机构ID
   */
  trainingInstitutionId: string
  /**
   * 机构名称
   */
  trainingInstitutionName: string
  /**
   * 机构简介
   */
  trainingInstitutionAbouts: string
  /**
   * 课件供应商id
   */
  coursewareSupplierId: string
  /**
   * 课件供应商名称
   */
  coursewareSupplierName: string
  /**
   * 培训类别id(路径)
   */
  trainingCategoryId: string
  /**
   * 培训类别名称
   */
  trainingCategoryName: string
  /**
   * 培训类别 名称路径
   */
  trainingCategoryNamePath: string
  /**
   * 工种id
   */
  workTypeId: string
  /**
   * 工种name
   */
  workTypeName: string
  /**
   * 适用人群名
   */
  suitableCrowNames: Array<string>
  /**
   * 学时
   */
  period: number
  /**
   * 价格
   */
  price: number
  /**
   * 商品状态：&quot;UPED&quot;上架|&quot;DOWNED&quot;下架
   */
  commodityState: string
  /**
   * 商品是否有效
   */
  commodityAvailable: boolean
  /**
   * 培训时间 起
   */
  trainingTimeStart: string
  /**
   * 培训时间 止
   */
  trainingTimeEnd: string
  /**
   * 开通人数
   */
  openNumber: number
  /**
   * 创建者id
   */
  creatorId: string
  /**
   * 创建者名
   */
  creatorName: string
  /**
   * 上架时间
   */
  onShelveTime: string
  /**
   * 下架时间
   */
  offShelveTime: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 最后修改时间
   */
  lastUpdateTime: string
  /**
   * 价格变更记录
   */
  priceChangeHistoryDTOS: Array<PriceChangeHistoryDTO>
  /**
   * 渠道商
   */
  channelVendorList: Array<ServicerDTO>
  /**
   * 商品总评价
   */
  evaluation: number
}

/**
 * Author:FangKunSen
Time:2021-02-02,19:15
 */
export class PriceChangeHistoryDTO {
  /**
   * 变更时间
   */
  changeTime: string
  /**
   * 操作者
   */
  operatorId: string
  /**
   * 操作者名字
   */
  operatorName: string
  /**
   * 原价格
   */
  oldPrice: number
  /**
   * 新价格
   */
  newPrice: number
}

/**
 * @Description 服务商
<AUTHOR>
@Date 15:30 2021/10/21
 */
export class ServicerDTO {
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 服务商名称
   */
  servicerName: string
}

export class RoleDTO {
  /**
   * 角色ID
   */
  id: string
  /**
   * 角色级别值
   */
  levelValue: number
  /**
   * 角色名称
   */
  name: string
  /**
   * 角色描述
   */
  description: string
  /**
   * 创建方式 | 1:自建; 2:导入
   */
  createType: number
  /**
   * 数据类型 | 1:普通; 2:内置
   */
  dataType: number
  /**
   * 创建人ID
   */
  creatorId: string
  /**
   * 创建时间
   */
  createDate: string
  /**
   * 是否可用
   */
  available: boolean
}

export class RoleEditDto {
  /**
   * 安全对象组集合
   */
  securityObjectGroupList: Array<SecurityObjectGroupDto>
  /**
   * 第三级安全对象id集合
旧平台的东西，不建议写死层级数，请使用RoleEditDto#securityObjectGroupList
   */
  nodeSelectedIdArray: Array<string>
  /**
   * 第二级安全对象id集合
旧平台的东西，不建议写死层级数，请使用RoleEditDto#securityObjectGroupList
   */
  itemSelectedIdArray: Array<string>
  /**
   * 第一级安全对象id集合
旧平台的东西，不建议写死层级数，请使用RoleEditDto#securityObjectGroupList
   */
  rootSelectedIdArray: Array<string>
}

export class SecurityObjectGroupDto {
  id: string
  name: string
  /**
   * URL内容
   */
  urlContent: string
  url: string
  isSelected: boolean
  children: Array<SecurityObjectGroupDto>
  parentId: string
  sort: number
}

/**
 * 基础地区，只包含子级地区信息和地区路径
<AUTHOR> create 2019/10/11 9:21
 */
export class BaseRegionDTO {
  /**
   * 地区 id
   */
  id: string
  /**
   * 地区 名称
   */
  name: string
  /**
   * 上级区域ID|顶级空，为空默认为-1
   */
  parentId: string
  /**
   * 区域路径
   */
  regionPath: string
  /**
   * 排序
   */
  sort: number
  /**
   * 版本号
   */
  version: string
  /**
   * 是否可用
   */
  available: boolean
}

export class BaseRegionTreeDTO {
  children: Array<BaseRegionTreeDTO>
  /**
   * 地区 id
   */
  id: string
  /**
   * 地区 名称
   */
  name: string
  /**
   * 上级区域ID|顶级空，为空默认为-1
   */
  parentId: string
  /**
   * 区域路径
   */
  regionPath: string
  /**
   * 排序
   */
  sort: number
  /**
   * 版本号
   */
  version: string
  /**
   * 是否可用
   */
  available: boolean
}

export class RegionInfoDTO {
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 省份id
   */
  provinceId: string
  /**
   * 省份名称
   */
  provinceName: string
  /**
   * 城市id
   */
  cityId: string
  /**
   * 城市名称
   */
  cityName: string
  /**
   * 区、县id
   */
  countyId: string
  /**
   * 区、县名称
   */
  countyName: string
}

export class DataRouterIdentity {
  dataPlatformVersionId: string
  dataProjectId: string
}

export class HttpIdentity {
  ip: string
  domain: string
  requestUrl: string
}

export class MicroContext {
  sequenceNo: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  servicerProvider: ServicerProvider
  userIdentity: UserIdentity
  dataRouterIdentity: DataRouterIdentity
  httpIdentity: HttpIdentity
}

export class ServicerProvider {
  unitId: string
  servicerType: number
  servicerId: string
}

export class UserIdentity {
  accountId: string
  rootAccountId: string
  accountType: number
  userId: string
}

export class LazyCommodityDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<LazyCommodityDTO>
}

export class RoleDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<RoleDTO>
}

export class JobLogDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<JobLogDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取施教机构单位id（通过域名）
   * 该接口只返回id和name
   * @param domain
   * @return
   * @param query 查询 graphql 语法文档
   * @param domain 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTeachUnitByDomain(
    domain: string,
    query: DocumentNode = GraphqlImporter.findTeachUnitByDomain,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: query,
        variables: { domain },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 通过施教机构分页获取总数
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTeachUnitCount(
    query: DocumentNode = GraphqlImporter.findTeachUnitCount,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param ids 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTeachUnitListByIds(
    ids: Array<string>,
    query: DocumentNode = GraphqlImporter.findTeachUnitListByIds,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: query,
        variables: { ids },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取启用的施教机构分页
   * @param page
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTeachUnitPage(
    params: { page?: Page; queryParam?: string },
    query: DocumentNode = GraphqlImporter.findTeachUnitPage,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取当前管理员所属施教机构分页
   * @param page
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTeachUnitPageByAdmin(
    params: { page?: Page; param?: string },
    query: DocumentNode = GraphqlImporter.findTeachUnitPageByAdmin,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取所有组安全对象
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAllPermission(
    query: DocumentNode = GraphqlImporter.getAllPermission,
    operation?: string
  ): Promise<Response<Array<SecurityObjectGroupDto>>> {
    return commonRequestApi<Array<SecurityObjectGroupDto>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取系统的所有角色信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAllRoles(
    query: DocumentNode = GraphqlImporter.getAllRoles,
    operation?: string
  ): Promise<Response<Array<RoleDTO>>> {
    return commonRequestApi<Array<RoleDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 当前请求系统时间
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCurrentDate(
    query: DocumentNode = GraphqlImporter.getCurrentDate,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 当前请求上下文信息
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCurrentMicroContext(
    query: DocumentNode = GraphqlImporter.getCurrentMicroContext,
    operation?: string
  ): Promise<Response<MicroContext>> {
    return commonRequestApi<MicroContext>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取当前角色安全对象
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCurrentUserSecurityGroup(
    query: DocumentNode = GraphqlImporter.getCurrentUserSecurityGroup,
    operation?: string
  ): Promise<Response<Array<SecurityObjectGroupDto>>> {
    return commonRequestApi<Array<SecurityObjectGroupDto>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取系统中所有可用的角色
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnabledRoles(
    query: DocumentNode = GraphqlImporter.getEnabledRoles,
    operation?: string
  ): Promise<Response<Array<RoleDTO>>> {
    return commonRequestApi<Array<RoleDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据roleId获取安全对象组
   * @return
   * @param query 查询 graphql 语法文档
   * @param roleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPermissionByRoleId(
    roleId: string,
    query: DocumentNode = GraphqlImporter.getPermissionByRoleId,
    operation?: string
  ): Promise<Response<Array<SecurityObjectGroupDto>>> {
    return commonRequestApi<Array<SecurityObjectGroupDto>>(
      SERVER_URL,
      {
        query: query,
        variables: { roleId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取安全对象--编辑角色
   * @param roleId
   * @return
   * @param query 查询 graphql 语法文档
   * @param roleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPermissionForEditRole(
    roleId: string,
    query: DocumentNode = GraphqlImporter.getPermissionForEditRole,
    operation?: string
  ): Promise<Response<RoleEditDto>> {
    return commonRequestApi<RoleEditDto>(
      SERVER_URL,
      {
        query: query,
        variables: { roleId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取二维码图片
   * @param params
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPromoteQRCode(
    params: PromoteQRCodeRequestDTO,
    query: DocumentNode = GraphqlImporter.getPromoteQRCode,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { params },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据地区id获取地区信息
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRegionById(
    id: string,
    query: DocumentNode = GraphqlImporter.getRegionById,
    operation?: string
  ): Promise<Response<BaseRegionDTO>> {
    return commonRequestApi<BaseRegionDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据地区路径查询地区信息
   * @param query 查询 graphql 语法文档
   * @param path 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRegionInfoByPath(
    path: string,
    query: DocumentNode = GraphqlImporter.getRegionInfoByPath,
    operation?: string
  ): Promise<Response<RegionInfoDTO>> {
    return commonRequestApi<RegionInfoDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { path },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据地区id获取该地区树
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRegionTree(
    id: string,
    query: DocumentNode = GraphqlImporter.getRegionTree,
    operation?: string
  ): Promise<Response<BaseRegionTreeDTO>> {
    return commonRequestApi<BaseRegionTreeDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据地区id集合批量获取地区信息
   * @param query 查询 graphql 语法文档
   * @param ids 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRegionsByIds(
    ids: Array<string>,
    query: DocumentNode = GraphqlImporter.getRegionsByIds,
    operation?: string
  ): Promise<Response<Array<BaseRegionDTO>>> {
    return commonRequestApi<Array<BaseRegionDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { ids },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据id获取角色
   * @return
   * @param query 查询 graphql 语法文档
   * @param roleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRoleById(
    roleId: string,
    query: DocumentNode = GraphqlImporter.getRoleById,
    operation?: string
  ): Promise<Response<RoleDTO>> {
    return commonRequestApi<RoleDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { roleId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取短码地址
   * @param url
   * @return
   * @param query 查询 graphql 语法文档
   * @param url 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getShortUrl(
    url: string,
    query: DocumentNode = GraphqlImporter.getShortUrl,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { url },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取专题分页
   * @param type       专题类型
   * @param servicerId 服务商Id(可选参数，不传则使用上下文服务商Id)
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSpecialTopicPage(
    params: { page?: Page; type?: SpecialTopicTypeEnums; servicerId?: string },
    query: DocumentNode = GraphqlImporter.getSpecialTopicPage,
    operation?: string
  ): Promise<Response<LazyCommodityDTOPage>> {
    return commonRequestApi<LazyCommodityDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据父级地区id获取子级地区
   * @param parentId
   * @return
   * @param query 查询 graphql 语法文档
   * @param parentId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSubRegionListByParentId(
    parentId: string,
    query: DocumentNode = GraphqlImporter.getSubRegionListByParentId,
    operation?: string
  ): Promise<Response<Array<BaseRegionDTO>>> {
    return commonRequestApi<Array<BaseRegionDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { parentId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取施教机构
   * @param unitId
   * @return
   * @param query 查询 graphql 语法文档
   * @param unitId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTeachUnit(
    unitId: string,
    query: DocumentNode = GraphqlImporter.getTeachUnit,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: query,
        variables: { unitId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 判断账号是否存在
   * @param check  0：忽略该字段|1：false
   * @param field
   * @return 存在则返回false
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async isRoleExist(
    params: { check: number; field?: string },
    query: DocumentNode = GraphqlImporter.isRoleExist,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取角色分页
   * @param page
   * @return
   * @param query 查询 graphql 语法文档
   * @param page 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageRolesByQuery(
    page: Page,
    query: DocumentNode = GraphqlImporter.pageRolesByQuery,
    operation?: string
  ): Promise<Response<RoleDTOPage>> {
    return commonRequestApi<RoleDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: { page },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 异步任务处理结果查询
   * @param page
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUserJob(
    params: { page?: Page; param?: UserJobParamDTO },
    query: DocumentNode = GraphqlImporter.pageUserJob,
    operation?: string
  ): Promise<Response<JobLogDTOPage>> {
    return commonRequestApi<JobLogDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 添加用户角色信息
   * @param accountId 账户id
   * @param roleIds   角色id集合
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addUserOwnRoles(
    params: { accountId: string; roleIds: Array<string> },
    mutate: DocumentNode = GraphqlImporter.addUserOwnRoles,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除角色
   * @param
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param roleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteRole(
    roleId: string,
    mutate: DocumentNode = GraphqlImporter.deleteRole,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { roleId },
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPermission(
    params: {
      nodeSelectedIdArray?: Array<string>
      itemSelectedIdArray?: Array<string>
      rootSelectedIdArray?: Array<string>
    },
    mutate: DocumentNode = GraphqlImporter.getPermission,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 移除用户角色信息
   * @param accountId
   * @param accountRoleIds 账户角色关系id集合
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeUserOwnRoles(
    params: { accountId: string; accountRoleIds: Array<string> },
    mutate: DocumentNode = GraphqlImporter.removeUserOwnRoles,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 保存角色
   * @param
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param roleDto 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveRole(
    roleDto: RolePermissionDto,
    mutate: DocumentNode = GraphqlImporter.saveRole,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { roleDto },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
