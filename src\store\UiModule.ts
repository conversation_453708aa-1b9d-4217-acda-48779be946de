import { getModule, Module, VuexModule, Mutation } from 'vuex-module-decorators'
import store from '@/store'

@Module({
  name: 'UiModule',
  dynamic: true,
  namespaced: true,
  store
})
/**
 * ui交互用的状态层
 */
class UiModule extends VuexModule {
  // todo
  startGuide = false
  startSteer = false
  @Mutation
  changeGuideState(key: boolean) {
    this.startGuide = key
  }
  @Mutation
  changeStart(key: boolean) {
    this.startSteer = key
  }
}

export default getModule(UiModule)
