import QueryStudentLearningList from '@api/service/management/statisticalReport/query/QueryStudentLearningList'
import {
  StudentSchemeLearningRequestVo,
  SyncResultEnmu
} from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
import exportMsgateway from '@api/diff-gateway/qztg-data-export-gateway-backstage'
class QueryStudentLearningListDiff extends QueryStudentLearningList {
  /**
   * 导出
   */
  async exportExcelDetail(param: StudentSchemeLearningRequestVo) {
    return await exportMsgateway.exportStudentLearningDetailInServicer(param)
  }
}
export default QueryStudentLearningListDiff
