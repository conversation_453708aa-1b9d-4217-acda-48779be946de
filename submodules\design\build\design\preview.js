import Vue from 'vue'
import Element from 'element-ui'
import '@/common/theme.scss'
import '@/<%=app%><%=sub!=="undefined"?("/"+sub):""%>/styles/webstyle.scss'
import '@/common/webstyle.scss'
import designComponents from '../design-components?app=<%=app%>&sub=<%=sub%>'
import changeTheme from './theme'

const queryString = require('query-string')

Vue.use(Element);

(async() => {
  const queryParams = queryString.parse(location.search)
  await changeTheme(queryParams.themeColor)
  /* eslint-disable no-new */
  new Vue({
    render: h => h(designComponents[queryParams.module])
  }).$mount('#app')
})()
