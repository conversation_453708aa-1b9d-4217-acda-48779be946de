"""独立部署的微服务,K8S服务名:ms-media-resource-learning-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取当前用户正在学习的学习信息"""
	getUserCurrentLearningInfo:UserCurrentLearningResponse
}
type Mutation {
	"""课件媒体学习token申请
		状态码：
		<pre>
		200 - 成功
		E10000 - 参数异常
		E90001 - 内部异常
		</pre>
		@param request 参数
		@return token信息
	"""
	applyCoursewareLearningToken(request:CoursewareMediaLearningTokenApplyRequest):CoursewareMediaLearningTokenResponse
	"""课件媒体学习token申请
		状态码：
		<pre>
		200 - 成功
		E10000 - 参数异常
		E90001 - 内部异常
		</pre>
		@param request 参数
		@return token信息
	"""
	applyCoursewareLearningTokenByToken(request:CoursewareMediaLearningTokenApplyByCourseRecordTokenRequest!):CoursewareMediaLearningTokenResponse
	"""申请多媒体计时
		状态码：
		<pre>
		200 - 成功
		E10000 - 参数异常
		E90001 - 内部异常
		L10000 - 由于学习的课程资源变更，学习被锁定，无法学习
		L10001 - 由于学习的课程进度变更，学习被锁定，无法学习
		L10002 - 由于学习的课程进度重置，学习被锁定，无法学习
		L20000 - 您被系统判定为疑似机器学习，学习被锁定，无法学习
		T10005 - 服务拒绝用户学习
		</pre>
		@param coursewareLearningToken 课件学习凭证
		@return 学习结果
	"""
	applyMediaLearningTiming(coursewareLearningToken:String):LearningResultResponse
	"""申请多媒体计时
		状态码：
		<pre>
		200 - 成功
		E10000 - 参数异常
		E90001 - 内部异常
		L10000 - 由于学习的课程资源变更，学习被锁定，无法学习
		L10001 - 由于学习的课程进度变更，学习被锁定，无法学习
		L10002 - 由于学习的课程进度重置，学习被锁定，无法学习
		L20000 - 您被系统判定为疑似机器学习，学习被锁定，无法学习
		T10005 - 服务拒绝用户学习
		</pre>
		@param coursewareLearningToken 课件学习凭证
		@return 学习结果
	"""
	applyMediaLearningTimingWithAnti(coursewareLearningToken:String):LearningResultResponse
	"""提交多媒体计时
		状态码：
		<pre>
		200 - 成功
		E10000 - 参数异常
		T10000 - 计时凭证不存在
		T10001 - 用户已学习其他课程
		T10002 - 用户已学习当前课程，其他课件
		T10003 - 用户已在其他地方学习当前课程当前课件
		T10004 - 用户学习已离线
		T10005 - 服务拒绝用户学习
		</pre>
		@param token                  计时凭证
		@param currentMediaTimeLength 当前媒体学习时长
		@return 学习结果
	"""
	commitMediaLearningTiming(token:String,currentMediaTimeLength:Long!):LearningResultResponse
	"""提交多媒体计时
		状态码：
		<pre>
		200 - 成功
		E10000 - 参数异常
		T10000 - 计时凭证不存在
		T10001 - 用户已学习其他课程
		T10002 - 用户已学习当前课程，其他课件
		T10003 - 用户已在其他地方学习当前课程当前课件
		T10004 - 用户学习已离线
		T10005 - 服务拒绝用户学习
		</pre>
		@param token                  计时凭证
		@param currentMediaTimeLength 当前媒体学习时长
		@return 学习结果
	"""
	commitMediaLearningTimingWithAnti(token:String,currentMediaTimeLength:Long!):LearningResultResponse
	"""补发课程相关完成学习事件"""
	courseRelatedCompleteReissue(requestList:[CourseRelatedCompleteRequest]):Void @optionalLogin
	"""结束多媒体计时
		状态码：
		<pre>
		200 - 成功
		E10000 - 参数异常
		T10000 - 计时凭证不存在
		T10001 - 用户已学习其他课程
		T10004 - 用户学习已离线
		E90001 - 在线学习用户主动离线异常
		</pre>
		@param token 计时凭证
		@return 学习结果
	"""
	endMediaLearningTiming(token:String):LearningResultResponse
	"""维持在线心跳
		状态码：
		<pre>
		200 - 成功
		E10000 - 参数异常
		T10000 - 计时凭证不存在
		T10001 - 用户已学习其他课程
		T10002 - 用户已学习当前课程，其他课件
		T10003 - 用户已在其他地方学习当前课程当前课件
		T10004 - 用户学习已离线
		T10005 - 服务拒绝用户学习
		</pre>
		@param token 计时凭证
	"""
	keepAliveHeartbeat(token:String):LearningErrorCodeResponse
	"""准备课程媒体资源计时
		@param courseLearningPlayToken 课程学习播放凭证
		@return 课程学习信息
	"""
	prepareCourseLearningTiming(courseLearningPlayToken:String):LearningCourseRecordResponse
	"""准备课程媒体资源计时
		@param courseLearningPlayToken 课程学习播放凭证
		@return 课程学习信息
	"""
	prepareCourseLearningTimingBeToken(courseLearningPlayToken:String!):LearningCourseRecordBeTokenResponse
}
"""<AUTHOR>
	@since 2023/11/27
"""
input CourseRelatedCompleteRequest @type(value:"com.fjhb.ms.mediaresourcelearning.v1.kernel.gateway.graphql.request.CourseRelatedCompleteRequest") {
	"""文件路径"""
	filePath:String
	"""课程学习记录id"""
	courseLearningRecordId:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerId:String
	userId:String
}
"""课件媒体学习token申请"""
input CoursewareMediaLearningTokenApplyByCourseRecordTokenRequest @type(value:"com.fjhb.ms.mediaresourcelearning.v1.kernel.gateway.graphql.request.CoursewareMediaLearningTokenApplyByCourseRecordTokenRequest") {
	"""课件编号"""
	coursewareId:String
	"""多媒体编号"""
	multiMediaId:String
	"""课程学习记录token"""
	courseRecordToken:String
}
"""课件媒体学习token申请"""
input CoursewareMediaLearningTokenApplyRequest @type(value:"com.fjhb.ms.mediaresourcelearning.v1.kernel.gateway.graphql.request.CoursewareMediaLearningTokenApplyRequest") {
	"""课程学习播放凭证"""
	courseLearningPlayToken:String
	"""课件编号"""
	coursewareId:String
	"""多媒体编号"""
	multiMediaId:String
}
"""课件媒体学习token申请
	<AUTHOR>
	@since 2022/1/20
"""
type CoursewareMediaLearningTokenResponse @type(value:"com.fjhb.ms.mediaresourcelearning.v1.kernel.gateway.graphql.response.CoursewareMediaLearningTokenResponse") {
	"""申请结果"""
	applyResult:TokenResponse
	"""课件媒体学习token申请"""
	token:String
}
"""课程学习信息
	<AUTHOR>
	@since 2021/12/3
"""
type LearningCourseRecordBeTokenResponse @type(value:"com.fjhb.ms.mediaresourcelearning.v1.kernel.gateway.graphql.response.LearningCourseRecordBeTokenResponse") {
	token:String
	"""课程编号"""
	courseId:String
	"""课件学习进度"""
	schedule:Double!
	"""学习状态，0/1/2，未学习/学习中/学习完成"""
	studyStatus:Int!
	"""当前课程下所有课件学习信息"""
	learningCoursewareRecords:[LearningCoursewareRecordResponse]
	"""学习状态码"""
	learningCode:LearningErrorCodeResponse
	"""code==L90001 有这个值
		已学习(时长/课时)
	"""
	timeLength:Double
	"""code==L90001 有这个值
		学习规则类型1=时长(秒) 2=课时
	"""
	ruleType:Int
}
"""课程学习信息
	<AUTHOR>
	@since 2021/12/3
"""
type LearningCourseRecordResponse @type(value:"com.fjhb.ms.mediaresourcelearning.v1.kernel.gateway.graphql.response.LearningCourseRecordResponse") {
	"""课程编号"""
	courseId:String
	"""课件学习进度"""
	schedule:Double!
	"""学习状态，0/1/2，未学习/学习中/学习完成"""
	studyStatus:Int!
	"""当前课程下所有课件学习信息"""
	learningCoursewareRecords:[LearningCoursewareRecordResponse]
	"""学习状态码"""
	learningCode:LearningErrorCodeResponse
	"""code==L90001 有这个值
		已学习(时长/课时)
	"""
	timeLength:Double
	"""code==L90001 有这个值
		学习规则类型1=时长(秒) 2=课时
	"""
	ruleType:Int
}
"""课件学习信息
	<AUTHOR>
	@since 2021/12/3
"""
type LearningCoursewareRecordResponse @type(value:"com.fjhb.ms.mediaresourcelearning.v1.kernel.gateway.graphql.response.LearningCoursewareRecordResponse") {
	"""课件编号"""
	coursewareId:String
	"""课件学习进度"""
	schedule:Double!
	"""学习状态，0/1/2，未学习/学习中/学习完成"""
	studyStatus:Int!
	"""当前课件下所有媒体学习信息"""
	learningMediaRecords:[LearningMediaRecordResponse]
}
"""学习代码
	<AUTHOR>
	@since 2021/12/6
"""
type LearningErrorCodeResponse @type(value:"com.fjhb.ms.mediaresourcelearning.v1.kernel.gateway.graphql.response.LearningErrorCodeResponse") {
	"""代码"""
	code:String
	"""信息"""
	message:String
}
"""媒体学习信息
	<AUTHOR>
	@since 2021/12/3
"""
type LearningMediaRecordResponse @type(value:"com.fjhb.ms.mediaresourcelearning.v1.kernel.gateway.graphql.response.LearningMediaRecordResponse") {
	"""课件编号"""
	coursewareId:String
	"""多媒体编号"""
	multiMediaId:String
	"""多媒体学习进度"""
	schedule:Double!
	"""当前学习时长"""
	currentLearningTimeLength:Long!
	"""最后播放刻度"""
	lastPlayScale:Long!
	"""学习状态，0/1/2，未学习/学习中/学习完成"""
	studyStatus:Int!
	"""最后学习时间"""
	lastLearningTime:DateTime
	"""是否最后一次学习媒体"""
	isLastLearning:Boolean!
}
"""学习结果
	<AUTHOR>
	@since 2021/12/7
"""
type LearningResultResponse @type(value:"com.fjhb.ms.mediaresourcelearning.v1.kernel.gateway.graphql.response.LearningResultResponse") {
	"""学习状态码"""
	learningCode:LearningErrorCodeResponse
	"""计时凭证信息"""
	timingToken:TimingTokenResponse
	"""code==L90001 有这个值
		已学习(时长/课时)
	"""
	timeLength:Double
	"""code==L90001 有这个值
		学习规则类型1=时长(秒) 2=课时
	"""
	ruleType:Int
}
"""计时凭证信息
	<AUTHOR>
	@since 2021/12/7
"""
type TimingTokenResponse @type(value:"com.fjhb.ms.mediaresourcelearning.v1.kernel.gateway.graphql.response.TimingTokenResponse") {
	"""计时凭证"""
	token:String
	"""课程编号"""
	courseId:String
	"""课件编号"""
	coursewareId:String
	"""多媒体编号"""
	multiMediaId:String
	"""课程进度"""
	courseSchedule:Double!
	"""课件进度"""
	coursewareSchedule:Double!
	"""多媒体进度"""
	multiMediaSchedule:Double!
	"""课程学习状态，0/1/2，未学习/学习中/学习完成"""
	courseStudyStatus:Int!
	"""课件学习状态，0/1/2，未学习/学习中/学习完成"""
	coursewareStudyStatus:Int!
	"""多媒体学习状态，0/1/2，未学习/学习中/学习完成"""
	multiMediaStudyStatus:Int!
	"""当前媒体已学习刻度，单位：秒"""
	mediaLearningTimeLength:Long!
	"""当前媒体最后播放刻度，单位：秒"""
	lastmediaPlayScale:Long!
	"""计时间隔时间，单位：秒"""
	intervalTime:Int!
}
"""凭证响应基类
	<AUTHOR>
	@since 2022/1/20
"""
type TokenResponse @type(value:"com.fjhb.ms.mediaresourcelearning.v1.kernel.gateway.graphql.response.TokenResponse") {
	"""代码：
		200-成功
	"""
	code:String
	"""信息"""
	message:String
}
"""用户在线学习实体
	<AUTHOR>
	@since 2021/12/3
"""
type UserCurrentLearningResponse @type(value:"com.fjhb.ms.mediaresourcelearning.v1.kernel.gateway.graphql.response.UserCurrentLearningResponse") {
	"""用户编号"""
	userId:String
	"""课程学习记录编号"""
	courseLearningRecordId:String
	"""课件学习记录编号"""
	coursewareLearningRecordId:String
	"""多媒体学习记录编号"""
	multiMediaLearningRecordId:String
	"""参训资格编号"""
	qualificationId:String
	"""学号"""
	studentNo:String
	"""计时凭证"""
	token:String
	"""上线时间"""
	startOnlineTime:DateTime
	"""最后学习时间"""
	lastLearningTime:DateTime
}

scalar List
