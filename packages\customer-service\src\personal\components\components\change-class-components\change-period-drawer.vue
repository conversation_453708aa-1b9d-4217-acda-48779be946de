<template>
  <div>
    <el-drawer title="更换期别" :visible.sync="show" size="1000px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert">
          <span class="f-c9">当前待更换的期别为：</span>
          <span class="f-fb">{{ currentPeriodName }}</span>
          <span class="f-c9">，请先在以下列表中选择更换的目标期别：</span>
        </el-alert>
        <el-row :gutter="16" class="m-query f-mt20">
          <el-form :inline="true" label-width="auto">
            <el-col :span="8">
              <el-form-item label="期别名称：">
                <el-input placeholder="请输入期别名称" v-model="queryReplaceableIssueListParam.issueName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报名时间：">
                <double-date-picker
                  :begin-create-time.sync="queryReplaceableIssueListParam.registerStartTimeScope"
                  :end-create-time.sync="queryReplaceableIssueListParam.registerEndTime"
                  :isLimitEndTime="false"
                  beginTimePlaceholder="请选择报名成功时间"
                  endTimePlaceholder="请选择报名成功时间"
                  :isLimitEndTimeSecond="isLimitEndTimeSecond"
                ></double-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary" @click="searchTableList">查询</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table" ref="tableDataRef" v-loading="loading">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="可更换的期别" min-width="300">
            <template #default="scope">
              <el-tag type="primary" effect="dark" size="mini">{{ scope.row.issueNo }} </el-tag>
              {{ scope.row.issueName }}
            </template>
          </el-table-column>
          <el-table-column label="报名时间" min-width="140" align="right">
            <template #default="scope"
              >{{ scope.row.registerStartTime || '-' }}~{{ scope.row.registerEndTime || '-' }}</template
            >
          </el-table-column>
          <el-table-column label="剩余可报名人数" min-width="100" align="center">
            <template #default="scope">{{ scope.row.canRegisterNum }}</template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template #default="scope">
              <div>
                <el-button type="text" @click="confirmChange(scope.row)">确认更换</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination v-bind="page" :page="page"></hb-pagination>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Ref, Watch, Prop } from 'vue-property-decorator'
  import { bind, debounce } from 'lodash-decorators'
  import ReplaceableIssueDetail from '@api/service/management/scheme/models/ReplaceableIssueDetail'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/news-date-picker.vue'
  import WaitExchangeTrainClassDetailVo from '@api/service/management/train-class/query/vo/WaitExchangeTrainClassDetailVo'
  import QueryExchangeTrainClass from '@api/service/management/train-class/query/QueryExchangeTrainClass'
  import { UiPage } from '@hbfe/common'
  import ChangeIssueItem from '@api/service/management/train-class/offlinePart/model/ChangeIssueItem'
  import QueryCanChangeIssueParam from '@api/service/management/train-class/offlinePart/model/QueryCanChangeIssueParam'
  import MutationCreateExchangeIssueOrder from '@api/service/management/trade/single/order/mutation/MutationCreateExchangeIssueOrder'
  import { ChangeIssueRequest } from '@api/ms-gateway/ms-learningscheme-enrollment-v1'
  import { ResponseStatus } from '@hbfe/common'
  import { ElTable } from 'element-ui/types/table'
  export class CertificationUrl {
    name: string
    url: string
  }

  @Component({
    components: { DoubleDatePicker }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.getTableList, this.getTableList)
    }
    @Ref('tableDataRef') tableDataRef: ElTable
    @Prop({
      type: String,
      default: ''
    })
    userId: string
    // 查询更换期别的方法
    queryExchangeScheme = new QueryExchangeTrainClass()
    // 查询方法的参数
    queryReplaceableIssueListParam = new QueryCanChangeIssueParam()
    page: UiPage
    show = false
    tableData = new Array<ChangeIssueItem>()
    /**
     * 当前期别名称
     */
    currentPeriodName = ''
    /**
     * 模型初始化
     */
    mutationCreateExchangeIssueOrder = new MutationCreateExchangeIssueOrder()
    /**
     * 初始期别
     */
    initialPeriod = new WaitExchangeTrainClassDetailVo()
    /**
     * 剔除的期别id
     */
    excludedIssueIdList = new Array<string>()
    /**
     * 表格loading
     */
    loading = false
    /**
     * 默认结束时分秒
     */
    isLimitEndTimeSecond = '23:59:59'
    /**
     * 打开抽屉
     */
    async openDrawer(params: WaitExchangeTrainClassDetailVo, excludedIssueIdList: Array<string>) {
      this.show = !this.show
      this.queryReplaceableIssueListParam = new QueryCanChangeIssueParam()
      this.queryReplaceableIssueListParam.commoditySkuId = params.commoditySkuId
      this.excludedIssueIdList = excludedIssueIdList
      this.queryReplaceableIssueListParam.excludedIssueIdList = this.excludedIssueIdList
      this.initialPeriod = params
      this.page.pageNo = 1
      this.currentPeriodName = params.periodName
      await this.getTableList()
    }
    async getTableList() {
      this.loading = true
      try {
        this.tableData = await this.queryExchangeScheme.queryCanChangeIssueList(
          this.page,
          this.queryReplaceableIssueListParam
        )
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
        this.tableDataRef.doLayout()
      }
      console.log('🚀🐱‍🚀🐱‍👓 ~ getTableList ~ this.tableData:', this.tableData)
    }
    @bind()
    @debounce(200)
    searchTableList() {
      this.page.pageNo = 1
      this.getTableList()
    }
    @bind()
    @debounce(200)
    async confirmChange(item: ReplaceableIssueDetail) {
      this.$confirm(`当前选择的目标期别：${item.issueName}，是否确认继续更换？`, '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        showCancelButton: true,
        type: 'warning'
      })
        .then(async () => {
          const loading = this.$loading({
            lock: true,
            text: '加载中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.8)'
          })
          const params = new ChangeIssueRequest()
          params.issueId = item.issueId
          params.sourceId = this.initialPeriod?.sourceId
          params.learningSchemeId = this.initialPeriod?.schemeId
          params.originalIssueId = this.initialPeriod?.periodId
          params.userId = this.userId
          params.sourceType = this.initialPeriod?.sourceType
          try {
            const res = await this.mutationCreateExchangeIssueOrder.changeIssue(params)
            if (res.isSuccess()) {
              this.$message.success('操作成功')
              this.show = false
            }
          } catch (e) {
            this.$message.error(((e as ResponseStatus).message as string) || '操作失败')
            console.log(e)
          } finally {
            loading.close()
          }
        })
        .catch(() => {
          //
        })
    }
  }
</script>
