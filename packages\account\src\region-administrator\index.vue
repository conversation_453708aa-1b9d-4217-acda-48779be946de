<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2023-07-10 14:14:27
 * @LastEditors: <PERSON>
 * @LastEditTime: 2023-07-25 15:46:34
-->
<route-meta>
{
"isMenu": true,
"title": "地区管理员管理",
"sort": 5,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="doQuery,created">
    <div class="f-p15 region-admin-table">
      <div class="f-mb15">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="showAddDrawer"
          v-if="$hasPermission('create')"
          desc="创建"
          actions="@ModifyDrawer"
          >新增管理员</el-button
        >
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <hb-search-wrapper @reset="reset" class="m-query is-border-bottom">
          <el-form-item label="管理员名称">
            <el-input v-model="regionAdministratorList.name" clearable placeholder="请输入管理员名称" />
          </el-form-item>
          <el-form-item label="管理员帐号">
            <el-input v-model="regionAdministratorList.account" clearable placeholder="请输入管理员帐号" />
          </el-form-item>
          <el-form-item label="帐号状态">
            <el-select v-model="regionAdministratorList.status" clearable filterable placeholder="请选择帐号状态">
              <el-option label="启用" :value="AccountStatus.enable"></el-option>
              <el-option label="停用" :value="AccountStatus.disable"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="管辖地区">
            <el-cascader
              ref="elCascaderRef"
              :props="props"
              v-model="regionAdministratorList.region"
              :options="regionOptions"
              :style="{ width: '100%' }"
              collapse-tags
              clearable
              v-bind="$attrs"
              @change="onInput"
            ></el-cascader>
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
          </template>
        </hb-search-wrapper>
        <!--表格-->
        <el-table stripe v-loading="loading" :data="tableData" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="姓名" min-width="120" fixed="left" prop="name"> </el-table-column>
          <el-table-column label="帐号" min-width="150" prop="account"> </el-table-column>
          <el-table-column label="手机号" min-width="150" prop="phone"> </el-table-column>
          <el-table-column label="管辖地区" min-width="240" prop="regionName">
            <template slot-scope="scope">
              {{ getRegionName(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column label="状态" min-width="100" prop="status">
            <template slot-scope="scope">
              <div v-if="scope.row.status == 2">
                <el-badge is-dot type="info" class="badge-status">停用</el-badge>
              </div>
              <div v-if="scope.row.status == 1">
                <el-badge is-dot type="success" class="badge-status">正常</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                @click="showDetiailDrawer(scope.row)"
                v-if="$hasPermission('detail')"
                desc="详情"
                actions="@DetailDrawer"
                >详情</el-button
              >
              <el-button
                type="text"
                size="mini"
                @click="showModifyDrawer(scope.row)"
                v-if="$hasPermission('modify')"
                desc="修改"
                actions="@ModifyDrawer"
                >修改</el-button
              >
              <template v-if="$hasPermission('resetPassword')" desc="重置密码" actions="doResetPassword">
                <hb-popconfirm title="确认重置该账号的密码为dqgly123吗？" @confirm="resetPassword(scope.row.accountId)">
                  <el-button slot="reference" type="text" size="mini">重置密码</el-button>
                </hb-popconfirm>
              </template>
              <template v-if="$hasPermission('enable')" desc="启停用" actions="doChangeAccountStatus">
                <hb-popconfirm
                  placement="top"
                  v-if="scope.row.status == 1"
                  title="是否停用该管理员账号"
                  @confirm="doDeactivate(scope.row.accountId)"
                >
                  <el-button slot="reference" type="text" size="mini">停用</el-button>
                </hb-popconfirm>
                <hb-popconfirm
                  placement="top"
                  v-if="scope.row.status == 2"
                  title="是否启用该管理员账号"
                  @confirm="doEnable(scope.row.accountId)"
                >
                  <el-button slot="reference" type="text" size="mini">启用</el-button>
                </hb-popconfirm>
              </template>
            </template>
          </el-table-column>
        </el-table>

        <!--分页-->
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
      </el-card>
      <!-- 新建/修改地区管理员信息 -->
      <modify-drawer @is-success="isSuccess" :region-options="regionOptions" ref="modifyDrawerRef"></modify-drawer>

      <!--查看地区管理员详情-->
      <detail-drawer ref="detailDrawerRef"></detail-drawer>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import ModifyDrawer from '@hbfe/jxjy-admin-account/src/region-administrator/__components__/modify-drawer.vue'
  import DetailDrawer from '@hbfe/jxjy-admin-account/src/region-administrator/__components__/detail-drawer.vue'
  import AdministratorStatus, {
    AdministratorStatusEnum
  } from '@api/service/management/authority/region-administrator/AdministratorStatusEnum'
  import RegionAdministratorList from '@api/service/management/authority/region-administrator/RegionAdministratorList'
  import RegionAdministrator from '@api/service/management/authority/region-administrator/RegionAdministratorItem'
  import { UiPage } from '@hbfe/common'
  import { cloneDeep } from 'lodash'
  import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import RegionAdministratorItem from '@api/service/management/authority/region-administrator/RegionAdministratorItem'
  import UserModule from '@api/service/management/user/UserModule'
  import { ElForm } from 'element-ui/types/form'
  import RegionVo from '@api/service/common/basic-data-dictionary/query/vo/RegionVo'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'

  @Component({
    components: {
      ModifyDrawer,
      DetailDrawer
    }
  })
  export default class extends Vue {
    @Ref('modifyDrawerRef')
    modifyDrawerRef: ModifyDrawer
    @Ref('detailDrawerRef')
    detailDrawerRef: DetailDrawer
    /**
     * 启用/禁用枚举
     */
    AccountStatus = AdministratorStatusEnum
    /**
     * 启用/禁用枚举Map
     */
    administratorStatus = new AdministratorStatus()
    /**
     * 查询方法
     */
    regionAdministratorList = new RegionAdministratorList()
    /**
     * 新增模型
     */
    regionAdministrator = new RegionAdministrator()
    /**
     * 地区选择器配置
     */
    regionOptions: Array<RegionTreeVo> = new Array<RegionTreeVo>()
    /**
     * 全国树
     */
    nationWideTree: Array<RegionTreeVo> = new Array<RegionTreeVo>()
    /**
     * 获取服务地区id
     */
    serviceId: string[]
    /**
     * 分页
     */
    page = new UiPage()
    /**
     * 地区选择器配置
     */
    props = {
      lazy: false,
      value: 'id',
      label: 'name',
      disabled: 'disabled',
      checkStrictly: true
    }
    loading = false
    constructor() {
      super()
      this.page = new UiPage(this.doQuery, this.doQuery)
    }
    /**
     * 获取地区列表
     */
    get getRegionName() {
      return (item: RegionAdministratorItem) => {
        return item?.regionName || '-'
      }
    }
    onInput(values: any) {
      const id = '/' + values?.join('/')
      this.$emit('toParent', id)
    }
    /**
     * 列表数据
     */
    tableData = new Array<RegionAdministratorItem>()
    /**
     * 打开新增抽屉
     */
    showAddDrawer() {
      this.modifyDrawerRef.showAddDrawer()
    }
    /**
     * 打开修改抽屉
     */
    showModifyDrawer(item: RegionAdministratorItem) {
      this.modifyDrawerRef.showModifyDrawer(item)
    }
    showDetiailDrawer(item: RegionAdministratorItem) {
      this.detailDrawerRef.openGrawer(item)
    }
    /**
     * 新增修改成功
     */
    isSuccess(isAdd: boolean) {
      console.log(isAdd)
      this.loading = true
      setTimeout(async () => {
        await this.doQuery()
      }, 800)
    }
    /**
     * 重置密码
     */
    async resetPassword(id: string) {
      //重置密码
      const res = await UserModule.mutationUserFactory.resetPwdBussiness.doResetRegionAdminPwd(id, 'dqgly123')
      if (res?.status?.isSuccess()) {
        this.$message.success('重置成功')
      } else {
        this.$message.warning('重置失败')
      }
    }
    /**
     * 列表查询
     */
    async doQuery() {
      try {
        this.loading = true
        this.tableData = await this.regionAdministratorList.queryList(this.page)
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }
    reset() {
      this.regionAdministratorList = new RegionAdministratorList()
      this.page.currentChange(1)
    }
    //停用
    async doDeactivate(id: string) {
      const res = await UserModule.mutationUserFactory.mutationManagerBusiness(id).doDeactivate()
      if (res?.status?.isSuccess()) {
        this.loading = true
        setTimeout(async () => {
          await this.doQuery()
          this.$message.success('停用成功')
        }, 800)
      } else {
        this.$message.warning('停用失败')
      }
    }
    //启用
    async doEnable(id: string) {
      const res = await UserModule.mutationUserFactory.mutationManagerBusiness(id).doEnable()
      if (res?.status?.isSuccess()) {
        this.loading = true
        setTimeout(async () => {
          await this.doQuery()
          this.$message.success('启用成功')
        }, 800)
      } else {
        this.$message.warning('启用失败')
      }
    }
    // -------------------------------地区选择器选项--------------------------------
    /**
     * 地区去掉最末级children为空
     */
    treeData(regionData: RegionTreeVo[]) {
      regionData.map((region) => {
        if (region.children.length) {
          this.treeData(region.children)
        } else {
          delete region.children
        }
      })
      return regionData
    }
    async activated() {
      this.doQuery()
    }
    async created() {
      //   const res = await QueryPhysicalRegion.queryRegion()
      //   this.regionOptions = this.treeData(cloneDeep(QueryPhysicalRegion.regionTree))
      this.nationWideTree = await QueryBusinessRegion.getCountrywideRegion()
      this.serviceId = await QueryBusinessRegion.getServiceRegionIds()
      this.regionOptions = await QueryBusinessRegion.filterRegionTree(this.nationWideTree, this.serviceId)
    }
  }
</script>
<style scoped>
  .region-admin-table ::v-deep .el-table .cell {
    white-space: pre-wrap;
  }
</style>
