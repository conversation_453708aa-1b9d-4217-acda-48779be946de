<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 08:51:51
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-08-22 16:52:34
 * @Description: 培训监管管理
-->
<template>
  <div class="f-p15">
    <el-tabs type="card" class="m-tab-card">
      <el-card shadow="never" class="m-card">
        <div class="f-mb20 f-flex f-align-center f-justify-between">
          <template
            v-if="$hasPermission('addLearningRules')"
            desc="添加学习规则"
            actions="@hbfe/jxjy-admin-platform/src/function/learningRules/addLearningRules.vue"
          >
            <el-button type="primary" @click="addLearningRules">添加学习规则</el-button>
          </template>
        </div>
        <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb15 f-mb15">
          开启学习规则后，学员合格时会判断培训班是否有是学习规则，若有配置规则并且学习记录不符合要求时，会根据配置内容重新计算学习记录。多个规则都符合时，优先执行方案级规则，其次是地区级，最后是平台级。
        </el-alert>
        <!-- 学习规则 -->
        <template
          v-if="$hasPermission('learningRuleList')"
          desc="学习规则列表"
          actions="@LearningRulesList,@hbfe/jxjy-admin-platform/src/function/learningRules/detail.vue,@hbfe/jxjy-admin-platform/src/function/learningRules/modify.vue"
        >
          <learning-rules-list></learning-rules-list>
        </template>
      </el-card>
    </el-tabs>
  </div>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import LearningRulesList from '@hbfe/jxjy-admin-platform/src/function/learningRules/components/learningRulesList.vue'
  @Component({
    components: {
      LearningRulesList
    }
  })
  export default class extends Vue {
    // 添加学习规则
    addLearningRules() {
      this.$router.push('/basic-data/platform/function/learningRules/addLearningRules')
    }
  }
</script>
