<!--
 * @Author: z张仁榕
 * @Date: 2025-07-07 16:07:16
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-07-10 10:31:17
 * @Description: 
-->
<template>
  <el-drawer title="修改销售配置" :visible.sync="showConfigDialog" size="700px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-form ref="form" label-width="180px" class="m-form f-mt10">
        <el-form-item label="展示在门户：" required>
          <el-radio-group v-model="showType">
            <el-radio :label="true">
              展示
              <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-c9"></i>
                <div slot="content">
                  配置培训方案是否展示在报名培训列表。展示门户分为学员门户报名列表和集体报名管理员查看培训班，设置为集体报名管理员可见，只生效于有开启线上集体报名的专题。
                </div>
              </el-tooltip>
            </el-radio>
            <el-radio :label="false">不展示</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="展示用户：" v-if="showType">
          <el-checkbox-group v-model="showCustomers">
            <el-checkbox :label="ShowUserRangeEnum.student">学员门户可见</el-checkbox>
            <el-checkbox :label="ShowUserRangeEnum.collective">集体报名管理员可见</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </div>
    <div class="m-btn-bar drawer-ft">
      <el-button @click="showConfigDialog = false">取 消</el-button>
      <el-button type="primary" @click="comfirm">确 定</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { ShowUserRangeEnum } from '@api/service/management/train-class/query/enum/ShowUserRangeEnum'
  import MinUploadFile from '@hbfe/jxjy-admin-platform/src/function/components/min-upload-file.vue'
  import { Component, Vue, Watch } from 'vue-property-decorator'

  @Component({
    components: { MinUploadFile }
  })
  export default class extends Vue {
    // 是否展示弹窗
    showConfigDialog = false

    // 枚举
    ShowUserRangeEnum = ShowUserRangeEnum
    // 是否展示
    showType = true
    // 可见用户
    showCustomers = [ShowUserRangeEnum.student, ShowUserRangeEnum.collective]

    @Watch('showType')
    showTypeChange(val: boolean) {
      if (val) {
        this.showCustomers = [ShowUserRangeEnum.student, ShowUserRangeEnum.collective]
      } else {
        this.showCustomers = []
      }
    }

    comfirm() {
      if (this.showCustomers.length == 0) {
        this.showType = false
      }
      this.$emit('comfirm', this.showType, this.showCustomers)
      this.showConfigDialog = false
    }
  }
</script>
