"""独立部署的微服务,K8S服务名:ms-examextraction-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""复制问卷
		@param request
		@return {@link String}
	"""
	copyQuestionnaire(request:CopyQuestionnaireRequest):String
	"""创建发布试卷配置
		@param request 创建出卷配置请求
		@return
	"""
	createPaperPublishConfigure(request:PaperPublishConfigureCreateRequest):String
	"""创建出卷配置分类
		@param request 创建出卷配置分类请求
	"""
	createPaperPublishConfigureCategory(request:CreatePaperPublishConfigureCategoryRequest):Void
	"""停用试卷配置
		@param id 出卷配置id
	"""
	disablePaperPublishConfigure(id:String!):Void
	"""启用试卷配置
		@param id 出卷配置id
	"""
	enablePaperPublishConfigure(id:String!):Void
	"""预览
		@param paperPublishConfigureId 试卷id【必填】
		@return 预览试卷响应对象
	"""
	previewAnswerPaper(paperPublishConfigureId:String!):PreviewPaperPublishConfigureResponse @optionalLogin
	"""删除试卷配置
		@param id 出卷配置id
		@return
	"""
	removePaperPublishConfigure(id:String!):Void
	"""删除出卷配置分类
		@param id 出卷配置分类id
	"""
	removePaperPublishConfigureCategory(id:String):Void
	"""修改试卷配置
		@param request 修改出卷配置请求
		@return
	"""
	updatePaperPublishConfigure(request:PaperPublishConfigureUpdateRequest):String
	"""修改出卷配置分类
		@param request 修改出卷配置分类请求
	"""
	updatePaperPublishConfigureCategory(request:UpdatePaperPublishConfigureCategoryRequest):Void
}
"""题库对应数量试题设置对象
	<AUTHOR>
"""
input LibraryMapQuestionNumSetting @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.paper.publish.config.extract.scopes.LibraryMapQuestionNumSetting") {
	"""题库id"""
	libraryId:String
	"""问题数量"""
	questionNum:Int
}
"""<AUTHOR> create 2021/6/3 17:35"""
input PaperQuestionDTO @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.adapter.dto.paper.PaperQuestionDTO") {
	"""试题ID"""
	questionId:String
	"""分数，-1表示不为分数评定方式为"""
	score:Double!
	"""所属大题序号，-1表示试卷没有使用大题"""
	groupSequence:Int!
	"""试题类型
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""是否必答"""
	answerRequired:Boolean
}
"""<AUTHOR> create 2021/6/3 17:37"""
input QuestionGroupDTO @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.adapter.dto.paper.QuestionGroupDTO") {
	"""序号"""
	sequence:Int!
	"""试题类型"""
	questionType:Int!
	"""大题名称"""
	groupName:String
	"""每题平均分数，-1表示不为分数评定方式"""
	eachQuestionScore:Double!
}
"""创建分类请求
	<AUTHOR>
"""
input CreatePaperPublishConfigureCategoryRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.CreatePaperPublishConfigureCategoryRequest") {
	"""分类名称【必填】"""
	name:String
	"""父级分类id(根目录填-1)【必填】"""
	parentId:String
	"""排序"""
	sort:Int
}
"""出卷配置创建事件
	<AUTHOR> create 2021/8/20 14:48
"""
input PaperPublishConfigureCreateRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.PaperPublishConfigureCreateRequest") {
	"""出卷配置名称"""
	name:String
	"""适用范围 用于筛选自定义的分类
		@see com.fjhb.domain.exam.api.consts.UsageScopes
	"""
	usageScope:Int!
	"""调查问卷类型
		@see com.fjhb.domain.exam.api.consts.QuestionnairePaperPublishConfigureType
	"""
	questionnaireType:Int
	"""出卷模式"""
	publishPattern:PublishPatternRequest
	"""分类id"""
	paperPublishConfigureCategoryId:String
	"""是否是草稿    1-是 2-不是"""
	isDraft:Int!
}
"""<AUTHOR> create 2021/8/20 14:48"""
input PaperPublishConfigureUpdateRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.PaperPublishConfigureUpdateRequest") {
	"""出卷配置id"""
	id:String!
	"""出卷配置名称"""
	name:String
	"""适用范围 用于筛选自定义的分类"""
	usageScope:Int
	"""调查问卷类型
		@see com.fjhb.domain.exam.api.consts.QuestionnairePaperPublishConfigureType
	"""
	questionnaireType:Int
	"""出卷模式"""
	publishPattern:PublishPatternRequest
	"""分类id"""
	paperPublishConfigureCategoryId:String
	"""是否启用 1-启用    2-停用"""
	status:Int!
	"""是否是草稿    1-是 2-不是"""
	isDraft:Int!
}
"""出卷模式基类
	<AUTHOR> create 2021/8/20 15:05
"""
input PublishPatternRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.PublishPatternRequest") {
	"""出卷模式类型
		@see PublishPatterns
	"""
	type:Int!
}
"""更新分类请求
	<AUTHOR>
"""
input UpdatePaperPublishConfigureCategoryRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.UpdatePaperPublishConfigureCategoryRequest") {
	"""分类id【必填】"""
	id:String
	"""分类名称【必填】"""
	name:String
	"""父级分类id【必填】"""
	parentId:String
	"""排序【必填】"""
	sort:Int
}
"""正确率评定方式
	<AUTHOR>
"""
input CorrectRateEvaluatePatternRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.evaluatepattern.CorrectRateEvaluatePatternRequest",implementsInputs:["EvaluatePatternRequest"]) {
	"""评定方式类型
		1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
		@see EvaluatePatternTypes
	"""
	type:Int!
}
"""评定方式
	<AUTHOR>
"""
input EvaluatePatternRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.evaluatepattern.EvaluatePatternRequest") {
	"""评定方式类型
		1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
		@see EvaluatePatternTypes
	"""
	type:Int!
}
"""无评定方式
	<AUTHOR>
"""
input NoneEvaluatePatternRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.evaluatepattern.NoneEvaluatePatternRequest",implementsInputs:["EvaluatePatternRequest"]) {
	"""评定方式类型
		1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
		@see EvaluatePatternTypes
	"""
	type:Int!
}
"""计分评定方式
	<AUTHOR>
"""
input OnlyScoringEvaluatePatternRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.evaluatepattern.OnlyScoringEvaluatePatternRequest",implementsInputs:["EvaluatePatternRequest"]) {
	scoringDescribes:[ScoringDescribe]
	"""评定方式类型
		1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
		@see EvaluatePatternTypes
	"""
	type:Int!
}
"""<AUTHOR>
input QuestionScoreRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.evaluatepattern.QuestionScoreRequest") {
	"""试题id"""
	questionId:String
	"""分数"""
	score:Double!
}
"""试题分数设置信息
	<AUTHOR>
"""
input QuestionScoreSettingRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.evaluatepattern.QuestionScoreSettingRequest") {
	"""大题序号"""
	sequence:Int
	"""试题类型
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""每题平均分"""
	eachQuestionScore:Double!
	"""具体试题分数"""
	questionScores:[QuestionScoreRequest]
}
"""分值评定方式
	<AUTHOR>
"""
input ScoreEvaluatePatternRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.evaluatepattern.ScoreEvaluatePatternRequest",implementsInputs:["EvaluatePatternRequest"]) {
	"""总分"""
	totalScore:Double!
	"""合格分数"""
	qualifiedScore:Double!
	"""试题分数"""
	questionScores:[QuestionScoreSettingRequest]
	"""多选提漏选得分模式
		0:不得分|1：的全部分数|2：得一半分数|3：每个选项按平均得分
		@see MultipleQuestionMissScorePatterns
	"""
	multipleMissScorePattern:Int!
	"""评定方式类型
		1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
		@see EvaluatePatternTypes
	"""
	type:Int!
}
"""单选题计分描述"""
input RadioScoringDescribe @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.evaluatepattern.onlyscoringevaluatepattern.RadioScoringDescribe",implementsInputs:["ScoringDescribe"]) {
	"""分数来源类型
		@see  ScoreSourceTypes
	"""
	scoreSourceTypes:Int
	type:Int
}
"""计分描述基类
	<AUTHOR>
"""
input ScoringDescribe @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.evaluatepattern.onlyscoringevaluatepattern.ScoringDescribe") {
	type:Int
}
"""智能出卷配置
	<AUTHOR> create 2021/8/20 15:06
"""
input AutomaticPublishPatternRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.publihs.patterns.AutomaticPublishPatternRequest",implementsInputs:["PublishPatternRequest"]) {
	"""建议作答时长【单位：秒】"""
	suggestionTimeLength:Int!
	"""抽题规则"""
	questionExtractRule:QuestionExtractRuleRequest
	"""评定方式"""
	evaluatePattern:EvaluatePatternRequest
	"""出卷模式类型
		@see PublishPatterns
	"""
	type:Int!
}
"""固定卷出卷模式
	<AUTHOR> create 2021/8/20 16:08
"""
input FixedPaperRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.publihs.patterns.FixedPaperRequest",implementsInputs:["PublishPatternRequest"]) {
	"""试卷id"""
	id:String
	"""试卷名称"""
	name:String
	"""描述"""
	description:String
	"""作答时长"""
	timeLength:Int!
	"""试卷总分"""
	totalScore:Double!
	"""大题集合"""
	groups:[QuestionGroupDTO]
	"""试题集合"""
	questions:[PaperQuestionDTO]
	"""评定方式"""
	evaluatePattern:EvaluatePatternRequest
	"""出卷模式类型
		@see PublishPatterns
	"""
	type:Int!
}
"""AB卷出卷模式
	<AUTHOR> create 2021/8/20 16:09
"""
input MultipleFixedPaperPublishPatternRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.publihs.patterns.MultipleFixedPaperPublishPatternRequest",implementsInputs:["PublishPatternRequest"]) {
	"""固定卷集合"""
	fixedPapers:[FixedPaperRequest]
	"""出卷模式类型
		@see PublishPatterns
	"""
	type:Int!
}
"""选择题答案选项实体
	<AUTHOR>
"""
input ChooseAnswerOptionRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.ChooseAnswerOptionRequest") {
	"""答案ID"""
	id:String!
	"""答案内容"""
	content:String!
	"""选项建议分数"""
	suggestionScore:Double
	"""是否允许填空"""
	enableFillContent:Boolean
	"""填空是否必填"""
	mustFillContent:Boolean
}
"""<AUTHOR> create 2021/6/29 14:03"""
input CreateQuestionRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.CreateQuestionRequest") {
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""<AUTHOR> create 2021/6/28 14:13"""
input CreateAskQuestionRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.ask.CreateAskQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""<AUTHOR> create 2021/6/28 14:43"""
input ChildQuestionCreateInfoRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.father.ChildQuestionCreateInfoRequest") {
	"""子题序号"""
	no:Int!
	"""试题内容"""
	question:CreateQuestionRequest
}
"""<AUTHOR> create 2021/6/28 14:43"""
input CreateFatherQuestionRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.father.CreateFatherQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""子题集合"""
	childQuestions:[ChildQuestionCreateInfoRequest]!
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""填空题创建命令
	<AUTHOR> create 2021/6/28 14:09
"""
input CreateFillQuestionRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.fill.CreateFillQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""填空数"""
	fillCount:Int!
	"""正确答案"""
	correctAnswer:FillAnswerRequest
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""散乱无序填空题答案实体
	<AUTHOR>
"""
input DisarrayFillAnswerRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.fill.DisarrayFillAnswerRequest",implementsInputs:["FillAnswerRequest"]) {
	"""正确答案集合"""
	correctAnswers:[[String]]!
	"""答案类型"""
	type:FillAnswerType!
}
"""填空题答案基类
	<AUTHOR>
"""
input FillAnswerRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.fill.FillAnswerRequest") {
	"""答案类型"""
	type:FillAnswerType!
}
"""<AUTHOR> create 2021/6/29 15:41"""
input FillCorrectAnswers @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.fill.FillCorrectAnswers") {
	"""空格位置"""
	blankNo:Int!
	"""答案备选项"""
	answers:[String]
}
"""按序填空题答案
	<AUTHOR>
"""
input SequenceFillAnswerRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.fill.SequenceFillAnswerRequest",implementsInputs:["FillAnswerRequest"]) {
	correctAnswers:[FillCorrectAnswers]!
	"""答案类型"""
	type:FillAnswerType!
}
"""按序关联填空题答案实体
	<AUTHOR>
"""
input SequenceRateFillAnswerRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.fill.SequenceRateFillAnswerRequest",implementsInputs:["FillAnswerRequest"]) {
	"""正确答案集合"""
	correctAnswers:[SequenceFillAnswerRequest]
	"""答案类型"""
	type:FillAnswerType!
}
"""多选题创建命令
	<AUTHOR> create 2021/6/28 14:07
"""
input CreateMultipleQuestionRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.multiple.CreateMultipleQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""可选答案列表【必填】"""
	answerOptions:[ChooseAnswerOptionRequest]!
	"""正确答案ID集合【必填】"""
	correctAnswerIds:[String]!
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""判断题创建命令
	<AUTHOR> create 2021/6/28 14:05
"""
input CreateOpinionQuestionRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.opinion.CreateOpinionQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""正确答案【必填】"""
	correctAnswer:Boolean!
	"""正确文本【必填】"""
	correctAnswerText:String
	"""不正确文本【必填】"""
	incorrectAnswerText:String
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""单选题创建命令
	<AUTHOR> create 2021/6/28 9:39
"""
input CreateRadioQuestionRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.radio.CreateRadioQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""可选答案列表【必填】"""
	answerOptions:[ChooseAnswerOptionRequest]!
	"""正确答案ID【必填】"""
	correctAnswerId:String
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""@Author: chenzeyu
	@CreateTime: 2024-07-29  16:12
	@Description: 量表题创建请求
"""
input CreateScaleQuestionRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.scale.CreateScaleQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""量表类型
		@see ScaleTypes
	"""
	scaleType:Int!
	"""程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	startDegree:String
	"""程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	endDegree:String
	"""级数"""
	series:Int!
	"""初始值"""
	initialValue:Int!
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""抽题规则
	<AUTHOR>
"""
input QuestionExtractRuleRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.questionextractrule.QuestionExtractRuleRequest") {
	"""试题总数"""
	questionCount:Int!
	"""出题范围"""
	questionScopes:[QuestionScopeSettingRequest]
	"""出题描述"""
	questionExtracts:[QuestionExtractSettingRequest]
}
"""出题描述配置请求
	<AUTHOR>
"""
input QuestionExtractSettingRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.questionextractrule.questionextract.QuestionExtractSettingRequest") {
	"""试题类型
		1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
	"""
	questionType:Int!
	"""大题序号"""
	sequence:Int!
	"""大题名称"""
	groupName:String
	"""试题数"""
	questionCount:Int!
	"""出题范围"""
	questionScopes:[QuestionScopeSettingRequest]
}
"""题库抽取指定数量试题设置信息
	<AUTHOR>
"""
input LibraryFixedQuestionScopeSettingRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.questionextractrule.questionscope.LibraryFixedQuestionScopeSettingRequest",implementsInputs:["QuestionScopeSettingRequest"]) {
	"""题库对应数量出题设置信息"""
	libraryMapQuestionNumSettings:[LibraryMapQuestionNumSetting]
	"""类型
		1-题库出题范围  2-用户课程出题范围  3-题库指定数量出题范围  4-标签出题范围  5-单位出题范围
		@see QuestionScopeSettingTypes
	"""
	type:Int!
}
"""题库出题配置请求
	<AUTHOR>
"""
input LibraryQuestionScopeSettingRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.questionextractrule.questionscope.LibraryQuestionScopeSettingRequest",implementsInputs:["QuestionScopeSettingRequest"]) {
	"""题库id集合"""
	libraryIds:[String]
	"""类型
		1-题库出题范围  2-用户课程出题范围  3-题库指定数量出题范围  4-标签出题范围  5-单位出题范围
		@see QuestionScopeSettingTypes
	"""
	type:Int!
}
"""出题范围请求
	<AUTHOR>
"""
input QuestionScopeSettingRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.questionextractrule.questionscope.QuestionScopeSettingRequest") {
	"""类型
		1-题库出题范围  2-用户课程出题范围  3-题库指定数量出题范围  4-标签出题范围  5-单位出题范围
		@see QuestionScopeSettingTypes
	"""
	type:Int!
}
"""标签code出题范围请求
	<AUTHOR>
"""
input TagQuestionScopeSettingRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.questionextractrule.questionscope.TagQuestionScopeSettingRequest",implementsInputs:["QuestionScopeSettingRequest"]) {
	"""标签code"""
	tagsCode:[String]
	"""类型
		1-题库出题范围  2-用户课程出题范围  3-题库指定数量出题范围  4-标签出题范围  5-单位出题范围
		@see QuestionScopeSettingTypes
	"""
	type:Int!
}
"""单位id出题范围请求
	<AUTHOR>
"""
input UnitQuestionScopeSettingRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.questionextractrule.questionscope.UnitQuestionScopeSettingRequest",implementsInputs:["QuestionScopeSettingRequest"]) {
	"""单位id集合"""
	unitIds:[String]
	"""类型
		1-题库出题范围  2-用户课程出题范围  3-题库指定数量出题范围  4-标签出题范围  5-单位出题范围
		@see QuestionScopeSettingTypes
	"""
	type:Int!
}
"""用户课程出题配置请求
	<AUTHOR>
"""
input UserCourseScopeSettingRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.questionextractrule.questionscope.UserCourseScopeSettingRequest",implementsInputs:["QuestionScopeSettingRequest"]) {
	"""课程来源
		0-指定唯一课程  1-用户课程题库
		@see UserCourseSources
	"""
	userCourseSource:Int!
	"""要求的组卷信息key.
		当{@link #userCourseSource} = 用户课程题库时需要指定:USER_COURSES_SCHEME_ID
		@see ExtractionMessageKeys
	"""
	requireKeys:[String]
	"""类型
		1-题库出题范围  2-用户课程出题范围  3-题库指定数量出题范围  4-标签出题范围  5-单位出题范围
		@see QuestionScopeSettingTypes
	"""
	type:Int!
}
"""<AUTHOR> create 2021/6/3 17:35"""
input CopyQuestionnaireQuestion @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.questionnaire.CopyQuestionnaireQuestion") {
	"""试题ID"""
	questionId:String
	"""分数，-1表示不为分数评定方式为"""
	score:Double!
	"""所属大题序号，-1表示试卷没有使用大题"""
	groupSequence:Int!
	"""试题类型
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""是否必答"""
	answerRequired:Boolean
	"""教师评价题标签code，非教师评价题不用传"""
	teacherEvaluateCode:String
	"""复制的试题内容"""
	copyQuestionContent:CreateQuestionRequest
}
"""出卷模式基类
	<AUTHOR>
	@date 2025/04/30
"""
input CopyQuestionnaireRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.questionnaire.CopyQuestionnaireRequest") {
	"""出卷配置名称"""
	name:String
	"""适用范围 用于筛选自定义的分类
		@see com.fjhb.domain.exam.api.consts.UsageScopes
	"""
	usageScope:Int!
	"""调查问卷类型
		@see com.fjhb.domain.exam.api.consts.QuestionnairePaperPublishConfigureType
	"""
	questionnaireType:Int
	"""出卷模式，固定卷"""
	publishPattern:QuestionnairePaperRequest
	"""分类id"""
	paperPublishConfigureCategoryId:String
	"""是否是草稿    1-是 2-不是"""
	isDraft:Int!
}
"""调查问卷固定卷出卷模式
	<AUTHOR> create 2021/8/20 16:08
"""
input QuestionnairePaperRequest @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.questionnaire.QuestionnairePaperRequest") {
	"""试卷id"""
	id:String
	"""出卷模式类型
		@see PublishPatterns
	"""
	type:Int!
	"""试卷名称"""
	name:String
	"""描述"""
	description:String
	"""作答时长"""
	timeLength:Int!
	"""试卷总分"""
	totalScore:Double!
	"""大题集合"""
	groups:[QuestionGroupDTO]
	"""试题集合"""
	questions:[CopyQuestionnaireQuestion]
	"""评定方式"""
	evaluatePattern:EvaluatePatternRequest
}
type QuestionGroup @type(value:"com.fjhb.domain.exam.api.paper.events.entities.QuestionGroup") {
	sequence:Int!
	questionType:Int!
	groupName:String
	eachQuestionScore:Double!
}
"""预览试卷响应类
	<AUTHOR>
"""
type PreviewPaperPublishConfigureResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.PreviewPaperPublishConfigureResponse") {
	"""试卷名称"""
	name:String
	"""试卷描述"""
	description:String
	"""作答时长"""
	timeLength:Int!
	"""试卷总分"""
	totalScore:Double!
	"""总题数"""
	totalQuestionCount:Int!
	"""大题集合"""
	groups:[QuestionGroup]
	"""试卷类型
		@see PaperTypes
		1-智能卷 2-固定卷
	"""
	paperType:Int!
	"""试题集合"""
	questions:[BaseQuestionResponse]
}
"""填空题答案基类
	<AUTHOR>
"""
interface BaseFillAnswerResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.BaseFillAnswerResponse") {
	"""填空题答案类型
		@see FillAnswerTypes
		1-散乱无序答案 2-有序答案  3-有序关联答案
	"""
	type:Int!
}
"""子题信息响应对象
	<AUTHOR>
"""
type ChildItemResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.ChildItemResponse") {
	"""编号"""
	no:Int!
	"""试题id"""
	questionId:String
}
"""选择题选项
	<AUTHOR>
"""
type ChooseAnswerOptionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.ChooseAnswerOptionResponse") {
	"""选项id"""
	id:String
	"""选项文本"""
	content:String
	"""是否允许填空"""
	enableFillContent:Boolean
	"""填空是否必填"""
	mustFillContent:Boolean
}
"""散乱无序的填空题答案响应对象
	<AUTHOR>
"""
type DisarrayFillAnswerResponse implements BaseFillAnswerResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.DisarrayFillAnswerResponse") {
	"""正确答案集合"""
	disarrayCorrectAnswers:[[String]]
	"""填空题答案类型
		@see FillAnswerTypes
		1-散乱无序答案 2-有序答案  3-有序关联答案
	"""
	type:Int!
}
"""有序填空题答案响应对象
	<AUTHOR>
"""
type FillCorrectAnswersResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.FillCorrectAnswersResponse") {
	"""空格位置"""
	blankNo:Int!
	"""答案备选项"""
	answers:[String]
}
"""按序填空题答案响应对象
	<AUTHOR>
"""
type SequenceFillAnswerResponse implements BaseFillAnswerResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.SequenceFillAnswerResponse") {
	"""正确答案集合"""
	sequenceCorrectAnswers:[FillCorrectAnswersResponse]
	"""填空题答案类型
		@see FillAnswerTypes
		1-散乱无序答案 2-有序答案  3-有序关联答案
	"""
	type:Int!
}
"""按序关联填空题答案实体
	<AUTHOR>
"""
type SequenceRateFillAnswerResponse implements BaseFillAnswerResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.SequenceRateFillAnswerResponse") {
	"""正确答案集合"""
	sequenceRateCorrectAnswers:[SequenceFillAnswerResponse]
	"""填空题答案类型
		@see FillAnswerTypes
		1-散乱无序答案 2-有序答案  3-有序关联答案
	"""
	type:Int!
}
"""问答题响应对象
	<AUTHOR>
"""
type AskQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.AskQuestionResponse") {
	"""已作答答案"""
	askAnswer:String
	"""试题id"""
	id:String
	"""大题序号"""
	groupSequence:Int!
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""得分"""
	score:Double!
	"""试题题目"""
	topic:String
	"""是否为子题"""
	isChildQuestion:Boolean!
	"""父题id"""
	parentQuestionId:String
	"""题析"""
	dissects:String
	"""关联课程id集合"""
	relateCourseId:[String]
	"""试题难度"""
	questionDifficulty:Int!
	"""是否必答"""
	answerRequired:Boolean
	"""是否已作答"""
	answered:Boolean!
	"""标签Code集合"""
	labelCodeList:[String]
}
"""试题响应基类
	<AUTHOR>
"""
interface BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.BaseQuestionResponse") {
	"""试题id"""
	id:String
	"""大题序号"""
	groupSequence:Int!
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""得分"""
	score:Double!
	"""试题题目"""
	topic:String
	"""是否为子题"""
	isChildQuestion:Boolean!
	"""父题id"""
	parentQuestionId:String
	"""题析"""
	dissects:String
	"""关联课程id集合"""
	relateCourseId:[String]
	"""试题难度"""
	questionDifficulty:Int!
	"""是否必答"""
	answerRequired:Boolean
	"""是否已作答"""
	answered:Boolean!
	"""标签Code集合"""
	labelCodeList:[String]
}
"""父子题响应对象
	<AUTHOR>
"""
type FatherQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.FatherQuestionResponse") {
	"""子题集合"""
	childQuestions:[ChildItemResponse]
	"""试题id"""
	id:String
	"""大题序号"""
	groupSequence:Int!
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""得分"""
	score:Double!
	"""试题题目"""
	topic:String
	"""是否为子题"""
	isChildQuestion:Boolean!
	"""父题id"""
	parentQuestionId:String
	"""题析"""
	dissects:String
	"""关联课程id集合"""
	relateCourseId:[String]
	"""试题难度"""
	questionDifficulty:Int!
	"""是否必答"""
	answerRequired:Boolean
	"""是否已作答"""
	answered:Boolean!
	"""标签Code集合"""
	labelCodeList:[String]
}
"""填空题响应对象
	<AUTHOR>
"""
type FillQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.FillQuestionResponse") {
	"""填空数"""
	fillCount:Int!
	"""正确答案"""
	fillQuestionCorrectAnswer:BaseFillAnswerResponse
	"""试题id"""
	id:String
	"""大题序号"""
	groupSequence:Int!
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""得分"""
	score:Double!
	"""试题题目"""
	topic:String
	"""是否为子题"""
	isChildQuestion:Boolean!
	"""父题id"""
	parentQuestionId:String
	"""题析"""
	dissects:String
	"""关联课程id集合"""
	relateCourseId:[String]
	"""试题难度"""
	questionDifficulty:Int!
	"""是否必答"""
	answerRequired:Boolean
	"""是否已作答"""
	answered:Boolean!
	"""标签Code集合"""
	labelCodeList:[String]
}
"""多选题响应对象
	<AUTHOR>
"""
type MultipleQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.MultipleQuestionResponse") {
	"""选项"""
	answerOptions:[ChooseAnswerOptionResponse]
	"""正确答案id集合"""
	multipleQuestionCorrectAnswerIds:[String]
	"""已答答案"""
	multipleAnswer:[String]
	"""填空内容 key是答案id，value是填空的内容"""
	fillContentMap:Map
	"""试题id"""
	id:String
	"""大题序号"""
	groupSequence:Int!
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""得分"""
	score:Double!
	"""试题题目"""
	topic:String
	"""是否为子题"""
	isChildQuestion:Boolean!
	"""父题id"""
	parentQuestionId:String
	"""题析"""
	dissects:String
	"""关联课程id集合"""
	relateCourseId:[String]
	"""试题难度"""
	questionDifficulty:Int!
	"""是否必答"""
	answerRequired:Boolean
	"""是否已作答"""
	answered:Boolean!
	"""标签Code集合"""
	labelCodeList:[String]
}
"""判断题响应对象
	<AUTHOR>
"""
type OpinionQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.OpinionQuestionResponse") {
	"""正确答案文本"""
	correctAnswerText:String
	"""错误答案文本"""
	incorrectAnswerText:String
	"""正确答案"""
	opinionQuestionCorrectAnswer:Boolean
	"""试题id"""
	id:String
	"""大题序号"""
	groupSequence:Int!
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""得分"""
	score:Double!
	"""试题题目"""
	topic:String
	"""是否为子题"""
	isChildQuestion:Boolean!
	"""父题id"""
	parentQuestionId:String
	"""题析"""
	dissects:String
	"""关联课程id集合"""
	relateCourseId:[String]
	"""试题难度"""
	questionDifficulty:Int!
	"""是否必答"""
	answerRequired:Boolean
	"""是否已作答"""
	answered:Boolean!
	"""标签Code集合"""
	labelCodeList:[String]
}
"""单选题试题响应
	<AUTHOR>
"""
type RadioQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.RadioQuestionResponse") {
	"""选项"""
	answerOptions:[ChooseAnswerOptionResponse]
	"""正确答案id"""
	radioQuestionCorrectAnswerId:String
	"""已作答答案"""
	radioAnswer:String
	"""填空内容"""
	fillContent:String
	"""试题id"""
	id:String
	"""大题序号"""
	groupSequence:Int!
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""得分"""
	score:Double!
	"""试题题目"""
	topic:String
	"""是否为子题"""
	isChildQuestion:Boolean!
	"""父题id"""
	parentQuestionId:String
	"""题析"""
	dissects:String
	"""关联课程id集合"""
	relateCourseId:[String]
	"""试题难度"""
	questionDifficulty:Int!
	"""是否必答"""
	answerRequired:Boolean
	"""是否已作答"""
	answered:Boolean!
	"""标签Code集合"""
	labelCodeList:[String]
}
"""量表题响应对象
	<AUTHOR>
"""
type ScaleQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.ScaleQuestionResponse") {
	"""量表类型
		@see com.fjhb.domain.exam.api.question.consts.ScaleTypes
	"""
	scaleType:Int!
	"""程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	startDegree:String
	"""程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	endDegree:String
	"""级数"""
	series:Int!
	"""初始值"""
	initialValue:Int!
	"""已作答答案"""
	scaleAnswer:Int
	"""试题id"""
	id:String
	"""大题序号"""
	groupSequence:Int!
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""得分"""
	score:Double!
	"""试题题目"""
	topic:String
	"""是否为子题"""
	isChildQuestion:Boolean!
	"""父题id"""
	parentQuestionId:String
	"""题析"""
	dissects:String
	"""关联课程id集合"""
	relateCourseId:[String]
	"""试题难度"""
	questionDifficulty:Int!
	"""是否必答"""
	answerRequired:Boolean
	"""是否已作答"""
	answered:Boolean!
	"""标签Code集合"""
	labelCodeList:[String]
}
"""<AUTHOR> create 2021/6/29 14:23"""
enum FillAnswerType @type(value:"com.fjhb.ms.exam.extraction.v1.kernel.gateway.graphql.request.question.FillAnswerType") {
	disarray
	sequence
	sequenceRelate
}

scalar List
