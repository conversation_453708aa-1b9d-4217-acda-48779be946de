schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""分销商-导出培训商品开通统计报表"""
	exportCommodityDistributorOpenReportInDistributor(request:StatisticTradeRecordRequest):Boolean!
	"""导出培训商品开通统计明细报表"""
	exportCommodityOpenStatisticsDetailExcelInSupplier(request:StatisticTradeRecordRequest):Boolean!
	"""供应商-导出培训商品开通统计报表"""
	exportCommodityOpenStatisticsExcelInSupplier(request:StatisticTradeRecordRequest):Boolean!
	"""导出培训商品开通统计汇总报表"""
	exportCommodityOpenStatisticsSummaryExcelInSupplier(request:StatisticTradeRecordRequest):Boolean!
	"""分销商品管理数据导出"""
	exportDistributionCommodityManageExcelInSupplier(request:DistributorCommodityAndRelationRequest):Boolean!
	"""导出分销商销售统计报表
		@return
	"""
	exportDistributorSalesStatisticsExcelInSupplier(request:StatisticTradeRecordRequest):Boolean!
	"""导出分销商的推广门户-分销商"""
	exportPricingPolicyinPortalInDistributor(request:PricingPolicyinPortalInDistributorRequest):Boolean!
	"""导出分销商的推广门户-供应商"""
	exportPricingPolicyinPortalInSupplier(request:PricingPolicyinPortalInSupplierRequest):Boolean!
	"""分销商-定价方案导出
		@param request
		@return
	"""
	exportProductPricingSchemeInSDistributor(request:DistributorCommodityAndRelationRequest):Boolean!
	"""供应商-定价方案导出
		@param request
		@return
	"""
	exportProductPricingSchemeInSupplier(request:DistributorCommodityAndRelationRequest):Boolean!
	"""分销商-学习明细报表导出
		@param request
		@return
	"""
	exportStudentSchemeLearningExcelInSDistributor(request:StudentSchemeLearningRequestInDistributor):Boolean!
	"""分销商-学习明细报表导出
		@param request
		@return
	"""
	exportStudentSchemeLearningExcelInSupplier(request:StudentSchemeLearningRequestInSupplier):Boolean!
	"""功能描述：分页查询导出任务组(分销商)
		@param jobGroupRequest : 任务查询条件
		@return : void
		@Author： sjm
		@Date： 2022/1/18 15:14
	"""
	listExportTaskGroupInfoInDistributor(jobGroupRequest:JobGroupRequest):[JobGroupResponse]
	"""功能描述：分页查询导出任务组(供应商)
		@param jobGroupRequest : 任务查询条件
		@return : void
		@Author： sjm
		@Date： 2022/1/18 15:14
	"""
	listExportTaskGroupInfoInSupplier(jobGroupRequest:JobGroupRequest):[JobGroupResponse]
	pageExportTaskInfoInMyself(page:Page,jobRequest:JobRequest):UserJobLogResponsePage @page(for:"UserJobLogResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input ObsFileMetaData @type(value:"com.fjhb.ms.data.export.commons.utils.async.ObsFileMetaData") {
	bizType:String
	owner:String
	sign:String
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
"""异步任务组名返回对象"""
input JobGroupRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.JobGroupRequest") {
	"""任务组key"""
	group:String
	"""任务组名（模糊查询）"""
	groupName:String
}
"""功能描述：任务查询参数
	@Author： wtl
	@Date： 2022/1/18 15:13
"""
input JobRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.JobRequest") {
	"""任务组名（必填）"""
	group:String
	"""任务组名匹配方式（EQ：完全匹配 LIKE：模糊匹配[*group*] LLIKE：左模糊匹配[group*] RLIKE：右模糊匹配[*group]，不传值默认为完全匹配）"""
	groupOperator:String
	"""任务名（模糊查询）"""
	jobName:String
	"""任务状态(executing:运行中 executed:运行完成 fail:运行失败)
		@see UserJobState
	"""
	jobState:String
	"""任务执行时间 yyyy-MM-dd HH:mm:ss"""
	executeTimeScope:DateScopeRequest
	"""异步任务处理结果（true:成功 false:失败）"""
	jobResult:Boolean
	"""分割粒度
		null-无 1-单位
	"""
	granularity:Int
}
input PricingPolicyinPortalInDistributorRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.PricingPolicyinPortalInDistributorRequest") {
	"""门户id"""
	portalId:String
	"""门户标题"""
	title:String
	"""门户简称"""
	shortName:String
	"""域名"""
	domainName:String
	"""是否已发布 1-开启 2-关闭"""
	isPublished:Boolean
	jobName:String
	metaData:ObsFileMetaData
}
input PricingPolicyinPortalInSupplierRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.PricingPolicyinPortalInSupplierRequest") {
	"""门户id"""
	portalId:String
	"""分销商id"""
	belongServiceId:String
	"""门户展示名称"""
	title:String
	"""门户简称"""
	shortName:String
	jobName:String
	metaData:ObsFileMetaData
}
"""商品sku属性查询条件"""
input PropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.common.PropertyRequest") {
	"""商品skuKey"""
	propertyKey:String
	"""商品skuValue"""
	propertyValue:String
}
"""供应商授权出的商品"""
input DistributorCommodityAndRelationRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.DistributorCommodityAndRelationRequest") {
	"""分销商商品名称"""
	saleTitle:String
	"""分销商id集合"""
	distributorIdList:[String]
	"""分销商等级"""
	distributorLevel:Int
	"""销售状态 1-有效 2-无效"""
	saleStatus:Int
	"""分销地区路径"""
	contractDistributionRegionPathList:[String]
	"""商品sku属性"""
	propertyList:[PropertyRequest]
	"""培训方案类型"""
	schemeTypeList:[String]
	"""授权价格类型 1-固定 2-区间"""
	priceType:Int
	"""分销价格范围查询-最大价格"""
	maxPrice:BigDecimal
	"""分销价格范围查询-最小价格"""
	minPrice:BigDecimal
	"""分销价格-最大价格"""
	policyMaxPrice:BigDecimal
	"""分销价格-最小价格"""
	policyMinPrice:BigDecimal
	"""定价方案状态"""
	statusList:[Int]
	"""分销是否有效
		0-有效 1-无效
		商品的分销开始时间、结束时间作为判断
	"""
	distributionStatus:Int
	"""分销商商品id集合"""
	distributorCommodityIdList:[String]
	"""商品id集合"""
	commodityIdList:[String]
	"""网校原始商品id集合"""
	rootCommodityIdList:[String]
	"""需要排除的网校原始商品id集合"""
	excludedRootCommodityIdList:[String]
	"""网校id"""
	onlineSchoolId:String
	"""培训方案名称"""
	schemeName:String
	"""网校销售状态
		0-开启 1-关闭
		商品的网校销售开始时间、结束时间作为判断
	"""
	onlineSchoolStatus:Int
	"""授权商品来源类型"""
	commoditySourceTypeList:[Int]
	"""定价方案id"""
	productPricingSchemeIdList:[String]
	"""需要排除的定价方案id"""
	excludedPricingSchemeIdList:[String]
	"""是否存在定价方案"""
	existPricingScheme:Boolean
	"""是否已启用定价方案"""
	enablePricingScheme:Boolean
	"""是否已启用优惠申请"""
	enableDiscountScheme:Boolean
	"""推广门户标识id"""
	portalIdentify:String
	"""推广门户展示名称"""
	showName:String
	"""推广门户简称"""
	shortName:String
	"""门户域名"""
	domainName:String
	"""门户状态
		0-停用 1-启用
	"""
	portalStatus:Int
	"""门户展示 (0-不展示, 1-展示）"""
	showPortal:Int
	"""门户推广 (0-不推广, 1-推广）"""
	portalPromotion:Int
	"""商品上架状态
		0-下架 1-上架
	"""
	shelveStatus:Int
	"""销售渠道类型
		0-自营渠道 1-分销渠道 2-专题渠道 3-华医网 4-推广门户渠道
	"""
	saleChannelType:Int
	"""销售渠道id"""
	saleChannelId:String
	"""优惠申请审批状态
		0-待处理 1-通过 2-未通过
	"""
	auditStatusList:[Int]
	"""优惠状态
		1-开启 2-关闭
		与优惠申请内容状态有关、与优惠周期约束、优惠开始时间、优惠结束时间有关
	"""
	discountStatusList:[Int]
}
"""学员培训方案学习查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 11:40
"""
input StudentSchemeLearningRequestInDistributor @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.StudentSchemeLearningRequestInDistributor") {
	"""学员信息"""
	student:UserRequest
	"""报名信息"""
	learningRegister:LearningRegisterRequest
	"""培训班信息"""
	scheme:SchemeRequest
	"""学习信息"""
	studentLearning:StudentLearningRequest
	"""数据分析信息"""
	dataAnalysis:DataAnalysisRequest
	"""对接管理系统"""
	connectManageSystem:ConnectManageSystemRequest
	"""扩展信息"""
	extendedInfo:ExtendedInfoRequest
	"""方案是否提供培训证明"""
	openPrintTemplate:Boolean
	"""是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1"""
	saleChannels:[Int]
	"""专题名称"""
	trainingChannelName:String
	"""专题Id  用于不同专题域名 查询对应专题的培训班"""
	trainingChannelId:String
	"""门户id"""
	portalId:String
}
"""学员培训方案学习查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 11:40
"""
input StudentSchemeLearningRequestInSupplier @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.StudentSchemeLearningRequestInSupplier") {
	"""学员信息"""
	student:UserRequest
	"""报名信息"""
	learningRegister:LearningRegisterRequest
	"""培训班信息"""
	scheme:SchemeRequest
	"""学习信息"""
	studentLearning:StudentLearningRequest
	"""数据分析信息"""
	dataAnalysis:DataAnalysisRequest
	"""对接管理系统"""
	connectManageSystem:ConnectManageSystemRequest
	"""扩展信息"""
	extendedInfo:ExtendedInfoRequest
	"""方案是否提供培训证明"""
	openPrintTemplate:Boolean
	"""是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1"""
	saleChannels:[Int]
	"""专题名称"""
	trainingChannelName:String
	"""专题Id  用于不同专题域名 查询对应专题的培训班"""
	trainingChannelId:String
	"""门户id"""
	portalId:String
	"""分销商ID"""
	distributorId:String
}
input ExtendedInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.ExtendedInfoRequest") {
	"""是否打印
		true 打印 false 未打印
	"""
	whetherToPrint:Boolean
}
"""学员学习信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
input StudentLearningRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.learning.StudentLearningRequest") {
	"""培训结果
		<p>
		-1:未知，培训尚未完成
		1:培训合格
		0:培训不合格
	"""
	trainingResultList:[Int]
	"""培训结果时间"""
	trainingResultTime:DateScopeRequest
	"""无需学习的学习方式类型
		<p>
		1: 选课学习方式
		2: 考试学习方式
		3: 练习学习方式
		4：自主学习课程学习方式
	"""
	notLearningTypeList:[Int]
	"""课程学习状态（0：未学习 1：学习中 2：学习完成）"""
	courseScheduleStatus:Int
	"""考试结果（-1：未考核 0：不合格 1：合格）"""
	examAssessResultList:[Int]
}
"""地区sku属性查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/2/25 10:55
"""
input RegionSkuPropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.scheme.RegionSkuPropertyRequest") {
	"""地区: 省"""
	province:String
	"""地区: 市"""
	city:String
	"""地区: 区县"""
	county:String
}
"""地区匹配查询
	<AUTHOR>
	@version 1.0
	@date 2022/2/25 14:19
"""
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.scheme.RegionSkuPropertySearchRequest") {
	"""地区"""
	region:[RegionSkuPropertyRequest]
	"""地区匹配条件
		<p>
		ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回，如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
		PART部分匹配：查询结果返回的地区与查询条件有给值的地区就会返回 如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
	"""
	regionSearchType:Int
}
"""培训方案信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
input SchemeRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.scheme.SchemeRequest") {
	"""培训方案id"""
	schemeId:String
	"""培训方案id"""
	schemeIdList:[String]
	"""培训方案类型
		<p>
		chooseCourseLearning: 选课规则
		autonomousCourseLearning: 自主选课
	"""
	schemeType:String
	"""方案名称"""
	schemeName:String
	"""培训属性"""
	skuProperty:SchemeSkuPropertyRequest
}
"""培训属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 10:22
"""
input SchemeSkuPropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.scheme.SchemeSkuPropertyRequest") {
	"""年度"""
	year:[String]
	"""地区"""
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	"""行业"""
	industry:[String]
	"""科目类型"""
	subjectType:[String]
	"""培训类别"""
	trainingCategory:[String]
	"""培训专业"""
	trainingProfessional:[String]
	"""技术等级"""
	technicalGrade:[String]
	"""岗位类别"""
	positionCategory:[String]
	"""培训对象"""
	trainingObject:[String]
	"""技术等级"""
	jobLevel:[String]
	"""工种"""
	jobCategory:[String]
	"""科目"""
	subject:[String]
	"""年级"""
	grade:[String]
	"""学段"""
	learningPhase:[String]
	"""学科"""
	discipline:[String]
}
"""@version: 1.0
	@description: 对接管理系统
	@author: sugs
	@create: 2022-11-15 11:27
"""
input ConnectManageSystemRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.user.ConnectManageSystemRequest") {
	"""同步状态
		0 未同步
		1 已同步
		2 同步失败
	"""
	syncStatus:Int
}
"""数据分析信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/20 15:14
"""
input DataAnalysisRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.user.DataAnalysisRequest") {
	"""成果配置可获得学时"""
	trainingResultPeriod:DoubleScopeRequest
	"""考核要求学时"""
	requirePeriod:DoubleScopeRequest
	"""已获得总学时"""
	acquiredPeriod:DoubleScopeRequest
}
"""学员学习报名信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:08
"""
input LearningRegisterRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.user.LearningRegisterRequest") {
	"""报名方式
		<p>
		1:学员自主报名
		2:集体报名
		3:管理员导入
	"""
	registerType:Int
	"""报名来源类型(ORDER：订单 SUB_ORDER：子订单 EXCHANGE_ORDER：换货单)"""
	sourceType:String
	"""报名来源ID"""
	sourceId:String
	"""学员状态(1:正常 2：冻结 3：失效)"""
	status:[Int]
	"""报名时间"""
	registerTime:DateScopeRequest
	"""来源订单号"""
	orderNoList:[String]
	"""来源子订单号"""
	subOrderNoList:[String]
	"""来源批次单号"""
	batchOrderNoList:[String]
}
"""地区模型
	<AUTHOR>
	@version 1.0
	@date 2022/2/27 20:01
"""
input RegionRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.user.RegionRequest") {
	"""地区：省"""
	province:String
	"""地区：市"""
	city:String
	"""地区：区"""
	county:String
}
"""用户属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:01
"""
input UserPropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.user.UserPropertyRequest") {
	"""所属地区路径"""
	regionList:[RegionRequest]
	"""工作单位名称"""
	companyName:String
	"""下单地区"""
	payOrderRegionList:[RegionRequest]
}
"""用户信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:00
"""
input UserRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.user.UserRequest") {
	"""用户id"""
	userIdList:[String]
	"""账户id"""
	accountIdList:[String]
	"""用户属性"""
	userProperty:UserPropertyRequest
}
"""交易统计请求参数
	<AUTHOR>
"""
input StatisticTradeRecordRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.statistic.StatisticTradeRecordRequest") {
	"""分销商id集合"""
	distributorIdList:[String]
	"""供应商id集合"""
	supplierIdList:[String]
	"""商品名称 模糊匹配"""
	commodityName:String
	"""网校id集合 List"""
	onlineSchoolList:[String]
	"""商品ID集合"""
	commodityIdList:[String]
	"""排除商品ID集合"""
	excludeCommodityIdList:[String]
	"""商品售价范围"""
	commodityPriceScope:DoubleScopeRequest
	"""查询时间范围"""
	queryDateScope:DateScopeRequest
	"""查询方式
		仅查询自己
		只查询下级分销商
		包含自己和下级分销商
		@see QueryWayType
	"""
	queryWayType:QueryWayType
	"""门户id"""
	portalId:String
	"""是否是推广门户的数据 | true.是 false.否"""
	isPortalData:Boolean
	jobName:String
	metaData:ObsFileMetaData
}
"""异步任务组名返回对象"""
type JobGroupResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.export.JobGroupResponse") {
	"""异步任务组key"""
	group:String
	"""异步任务组名"""
	groupName:String
	"""排序大小"""
	order:Int!
	"""所在域"""
	domain:[String]
}
"""功能描述：异步任务日志返回对象
	@Author： wtl
	@Date： 2022/4/11 17:18
"""
type UserJobLogResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.export.UserJobLogResponse") {
	"""任务id"""
	jobId:String
	"""任务组名"""
	group:String
	"""任务名"""
	jobName:String
	"""任务开始时间"""
	beginTime:DateTime
	"""任务结束时间"""
	endTime:DateTime
	"""任务状态(executing:运行中 executed:运行完成 fail:运行失败)
		@see UserJobState
	"""
	jobState:String
	"""异步任务处理结果（true:成功 false:失败）"""
	jobResult:Boolean
	"""任务执行成功或失败的信息"""
	message:String
	"""导出文件路径"""
	exportFilePath:String
	"""是否受保护"""
	isProtected:Boolean!
	"""资源id"""
	fileResourceId:String
	"""操作人id"""
	operatorUserId:String
	"""操作人帐户id"""
	operatorAccountId:String
}
"""@author: yxw
	@date: 2024/1/18
	@time: 15:01
	@description：
"""
enum QueryWayType @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.constants.QueryWayType") {
	"""仅查询自己"""
	ONLY_ME
	"""只查询下级分销商"""
	ONLY_MY_DISTRIBUTOR
	"""包含自己和下级分销商"""
	ALL
}

scalar List
type UserJobLogResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserJobLogResponse]}
