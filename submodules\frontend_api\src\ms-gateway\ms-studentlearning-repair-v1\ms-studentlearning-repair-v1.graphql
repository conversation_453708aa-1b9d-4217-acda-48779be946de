"""独立部署的微服务,K8S服务名:ms-studentlearning-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""重试learningResultCommitCreatedEventRetry
		@return java.lang.String
		<AUTHOR> By Cb
		@date 2023/9/11 16:51
	"""
	learningResultCommitCreatedEventRetry(payload:String):String
	"""重试learningResultCommitRemovedEventEventRetry
		@return java.lang.String
		<AUTHOR> By Cb
		@date 2023/9/11 16:51
	"""
	learningResultCommitRemovedEventEventRetry(payload:String):String
	"""重试learningResultCommitUpdatedEventEvent
		@return java.lang.String
		<AUTHOR> By Cb
		@date 2023/9/11 16:51
	"""
	learningResultCommitUpdatedEventEventRetry(payload:String):String
	"""学习成果 - 用户考核指标合格事件处理
		@return java.lang.String
		<AUTHOR> By Cb
		@date 2023/9/11 16:51
	"""
	learningResultRelateUserAssessIndicatorQualifiedEventHandle(payload:String):String
	"""学习成果重试获取
		@param request:
		@return {@link String}
		<AUTHOR> By Cb
		@since 2024/6/5 17:35
	"""
	learningResultRetryGain(request:LearningResultRetryGainRequest):String
	"""获取指定成果ID"""
	reGainLearningResult(learningResultId:String):Void
	"""根据学生学号列表和学习成果ID，重新获取对应的学生学习成果记录。
		@param studentNos       需要重新获取学习成果的学生学号列表
		@param learningResultId 要重新获取的学习成果唯一标识ID
	"""
	reGainStudentsLearningResult(studentNos:[String],learningResultId:String):Void
	"""重试发送事件
		@param aggregateIdentifier 聚合根的唯一标识符
		@param globalIndex         事件的全局索引，用于定位特定事件
		@param className           预期事件对象的类全限定名
	"""
	retrySendEvent(aggregateIdentifier:String,globalIndex:Int!,className:String):Void
	"""撤销指定学习成果及其关联的用户学习记录
		@param learningResultId 要撤销的学习成果的唯一标识符
	"""
	revokeLearningResult(learningResultId:String):Void
	"""根据学生学号列表和学习成果ID，撤销对应的学生学习成果记录。
		@param studentNos       需要撤销学习成果的学生学号列表
		@param learningResultId 要撤销的学习成果唯一标识ID
	"""
	revokeStudentsLearningResult(studentNos:[String],learningResultId:String):Void
	"""参训人员证书成果撤回
		@param schemeId
	"""
	traineesAchievementRevocation(schemeId:String,time:DateTime):Void @optionalLogin
}
"""学习成果重新获得请求
	<AUTHOR> By Cb
	@since 2024/6/5 17:25
"""
input LearningResultRetryGainRequest @type(value:"com.fjhb.ms.student.learning.v1.kernel.gateway.graphql.request.LearningResultRetryGainRequest") {
	"""学习方案ID"""
	learningSchemeId:String
}

scalar List
