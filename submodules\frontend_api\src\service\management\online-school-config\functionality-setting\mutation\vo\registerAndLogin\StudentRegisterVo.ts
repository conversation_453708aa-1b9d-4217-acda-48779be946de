import { FieldConstraintResponse } from '@api/ms-gateway/ms-servicer-series-v1'
import FieldConstraintVo from './FieldConstraintVo'
import IdRegisterCardTypeVo from '@api/service/common/basic-data-dictionary/query/vo/IdRegisterCardTypeVo'

class StudentRegisterVo {
  /**
   * 姓名
   */
  name: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 性别
   */
  gender: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 证件号
   */
  idCard: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 密码
   */
  password: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 手机号
   */
  phone: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 单位所在地区
   */
  area: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 工作单位
   */
  companyName: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 行业
   */
  industries: FieldConstraintVo = new FieldConstraintVo()

  /**
   * 证件类型
   */
  idCardType: Array<IdRegisterCardTypeVo> = []

  from(res: Array<FieldConstraintResponse>) {
    const resMap = new Map()
    res?.forEach(item => {
      resMap.set(item.field, item)
    })
    this.name = FieldConstraintVo.from(resMap.get('name'))
    this.gender = FieldConstraintVo.from(resMap.get('gender'))
    this.idCard = FieldConstraintVo.from(resMap.get('idCard'))
    this.password = FieldConstraintVo.from(resMap.get('password'))
    this.phone = FieldConstraintVo.from(resMap.get('phone'))
    this.area = FieldConstraintVo.from(resMap.get('area'))
    this.companyName = FieldConstraintVo.from(resMap.get('companyName'))
    this.industries = FieldConstraintVo.from(resMap.get('industries'))
  }
}
export default StudentRegisterVo
