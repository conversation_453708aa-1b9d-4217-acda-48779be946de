import StudentCourseVo from '@api/service/customer/course/query/vo/StudentCourseVo'

/*
 * 课程信息详情
 */
class CourseDetailVo extends StudentCourseVo {
  /*
    课程id
  */
  id = ''
  /*
    课程名称
  */
  name = ''
  /*
    课程学时
  */
  period: number = null
  /*
    课程学习取得结果时间
  */
  learningResultTime = ''
  /*
    完成学习时间戳
  */
  timeStamp = 0
  // 学习完成时间
  learningResultTimeStr?: string = ''
  // 课程类型
  courseType?: string = ''
  //选课时间
  selectCourseTime?: string = ''
  //排序
  sort?: string = ''
}

export default CourseDetailVo
