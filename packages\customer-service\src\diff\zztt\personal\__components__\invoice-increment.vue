<script lang="ts">
  import { Component } from 'vue-property-decorator'
  import invoiceIncrement from '@hbfe/jxjy-admin-customerService/src/personal/components/invoice-increment.vue'
  import QueryOffLineInvoice from '@api/service/diff/management/zztt/trade/invoice/QueryOffLineInvoice'

  @Component
  export default class extends invoiceIncrement {
    //接口请求
    queryOffLineInvoice = new QueryOffLineInvoice()
    /**
     * 查询发票分页
     */
    async doQueryPage() {
      if (!this.userId) {
        return
      }
      this.query.loading = true
      try {
        this.pageData = await this.queryOffLineInvoice.offLinePageVatspecialplaInvoiceInServicer(
          this.page,
          this.pageQueryParam
        )
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        this.query.loading = false
        this.getBusinessRegionList()
      }
    }
  }
</script>
