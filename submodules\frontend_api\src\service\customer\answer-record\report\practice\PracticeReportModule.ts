import store from '@/store'
import { Module, VuexModule, getModule, Action, Mutation } from 'vuex-module-decorators'
import PlatformExamGateway, {
  AnswerQuestionCountParamDTO,
  PracticeCorrectRateTrendDTO,
  PracticeCountTrendDTO,
  PracticeTrendStatisticParamDTO,
  PracticeType,
  UserAnswerQuestionCountDTO,
  UserFavoriteQuestionStatisticDTO,
  UserFavoriteQuestionStatisticParamDTO,
  UserPracticeStatisticParamDTO
} from '@api/gateway/PlatformExam'
import Response, { ResponseStatus } from '../../../../../Response'
import * as graphqlImporter from './graphql-importer'
import { Role, RoleType, Secure } from '../../../../../Secure'
import { DateRange } from '@api/service/customer/answer-record/report/enums/enums'
import SyllabusModule from '@api/service/customer/syllabus/SyllabusModule'
import Chapter from '@api/service/common/models/syllabus/Chapter'
import AnswerRecordUtils from '@api/service/customer/answer-record/report/utils/AnswerRecordUtils'

class StateCache {
  constructor(schemeId: string, learningId: string, majorId: string) {
    this.schemeId = schemeId
    this.learningId = learningId
    this.majorId = majorId
  }

  // 方案id
  schemeId: string
  // 方式id
  learningId: string
  majorId: string
  // 做题总量
  totalAnswerCount = 0
  allCategoryCount: UserAnswerQuestionCountDTO = new UserAnswerQuestionCountDTO()
  // 正确率
  correctRate = 0
  // 累计练习（天）
  keepPracticeDay = 0
  // 收藏试题数
  favoriteQuestionCount = 0
  // 基础做题总量
  totalAnswerCountBase = 0
  // 基础正确率
  correctRateBase = 0
  // 基础累计练习（天）
  keepPracticeDayBase = 0
  // 基础收藏试题数
  favoriteQuestionCountBase = 0
  // 要比较的做题总量
  totalAnswerCountToCompare = 0
  // 要比较的正确率
  correctRateToCompare = 0
  // 要比较的累计练习（天）
  keepPracticeDayToCompare = 0
  // 要比较的收藏试题数
  favoriteQuestionCountToCompare = 0
  // 正确率趋势
  correctRateTrend = new Array<PracticeCorrectRateTrendDTO>()
  // 练习题量趋势
  practiceTrend: Array<PracticeCountTrendDTO> = new Array<PracticeCountTrendDTO>()
  // 要比较的正确率趋势
  correctRateTrendToCompare: Array<PracticeCorrectRateTrendDTO> = new Array<PracticeCorrectRateTrendDTO>()
  // 要比较的练习题量趋势
  practiceTrendToCompare: Array<PracticeCountTrendDTO> = new Array<PracticeCountTrendDTO>()
  // 是否需要重载，在学员触发去作答练习是置为true，在下次取数时先清空数据然后加载新数据
  needReload = true
  // 最新一次加载时间，从apollo配置的超时时间与当前时间判断，在下次取数时清空当前取数数据然后加载新数据
  latestLoadTime: Date = new Date()
}

export interface IState {
  learningStateCacheMap: Array<StateCache>
}

@Module({
  namespaced: true,
  name: 'PracticeReportModule',
  store,
  dynamic: true
})
class PracticeReportModule extends VuexModule implements IState {
  learningStateCacheMap = new Array<StateCache>()

  @Role([RoleType.user])
  @Action
  async init(payload: {
    schemeId: string
    issueId: string
    learningId: string
    majorId: string
    range: DateRange
    customStartDate: Date
    customEndDate: Date
  }) {
    if (
      !this.learningStateCacheMap.find(p => p.learningId === payload.learningId) ||
      this.learningStateCacheMap.find(p => p.learningId === payload.learningId)?.needReload
    ) {
      if (
        !payload.schemeId ||
        !payload.issueId ||
        !payload.learningId ||
        !payload.majorId ||
        payload.range === undefined
      ) {
        console.log('初始化练习报告参数异常' + JSON.stringify(payload))
        return new ResponseStatus(500, '参数为空，请检查')
      }
      if (!this.learningStateCacheMap.find(p => p.learningId === payload.learningId)) {
        const stateCache = new StateCache(payload.schemeId, payload.learningId, payload.majorId)
        this.setStateCacheToLearningStateCacheMap(stateCache)
      }
      // 初始化考纲信息
      const status = await SyllabusModule.init()
      if (!status.isSuccess()) {
        return status
      }
      const leafSyllabus: Array<Chapter> = SyllabusModule.getLeafSyllabusByMajorId(payload.majorId)

      const stateCache = new StateCache(payload.schemeId, payload.learningId, payload.majorId)

      const currentDate = new Date()

      // 根据条件查询值
      // 统计 答题数和正确率
      const param: UserPracticeStatisticParamDTO = new UserPracticeStatisticParamDTO()
      param.schemeId = payload.schemeId
      param.learningId = payload.learningId
      param.statisticPracticeTypeList = [
        PracticeType.REAL,
        PracticeType.SIMULATION,
        PracticeType.PRACTICE,
        PracticeType.DAILY,
        PracticeType.ERROR_PRONE,
        PracticeType.RANDOM
      ]
      param.completeTimeStart = AnswerRecordUtils.getStartTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      param.completeTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      // 各题型
      const paramDTO: AnswerQuestionCountParamDTO = new AnswerQuestionCountParamDTO()
      paramDTO.schemeId = payload.schemeId
      paramDTO.learningId = payload.learningId
      paramDTO.statisticPracticeTypeList = [
        PracticeType.REAL,
        PracticeType.SIMULATION,
        PracticeType.PRACTICE,
        PracticeType.DAILY,
        PracticeType.ERROR_PRONE,
        PracticeType.RANDOM
      ]
      // 统计累计作答天数
      const frequencyParam: UserPracticeStatisticParamDTO = new UserPracticeStatisticParamDTO()
      frequencyParam.schemeId = payload.schemeId
      frequencyParam.learningId = payload.learningId
      frequencyParam.statisticPracticeTypeList = [
        PracticeType.REAL,
        PracticeType.SIMULATION,
        PracticeType.PRACTICE,
        PracticeType.DAILY,
        PracticeType.ERROR_PRONE,
        PracticeType.RANDOM
      ]
      frequencyParam.completeTimeStart = AnswerRecordUtils.getStartTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      frequencyParam.completeTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      // 统计收藏题数
      const favoriteParam: UserFavoriteQuestionStatisticParamDTO = new UserFavoriteQuestionStatisticParamDTO()
      favoriteParam.schemeId = payload.schemeId
      favoriteParam.issueId = payload.issueId
      favoriteParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      favoriteParam.createTimeStart = AnswerRecordUtils.getStartTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      favoriteParam.validTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )

      // 查询基准值
      // 统计 答题数和正确率
      const baseParam: UserPracticeStatisticParamDTO = new UserPracticeStatisticParamDTO()
      baseParam.schemeId = payload.schemeId
      baseParam.learningId = payload.learningId
      baseParam.statisticPracticeTypeList = [
        PracticeType.REAL,
        PracticeType.SIMULATION,
        PracticeType.PRACTICE,
        PracticeType.DAILY,
        PracticeType.ERROR_PRONE,
        PracticeType.RANDOM
      ]
      baseParam.completeTimeStart = undefined
      baseParam.completeTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      // 统计累计作答天数
      const baseFrequencyParam: UserPracticeStatisticParamDTO = new UserPracticeStatisticParamDTO()
      baseFrequencyParam.schemeId = payload.schemeId
      baseFrequencyParam.learningId = payload.learningId
      baseFrequencyParam.statisticPracticeTypeList = [
        PracticeType.REAL,
        PracticeType.SIMULATION,
        PracticeType.PRACTICE,
        PracticeType.DAILY,
        PracticeType.ERROR_PRONE,
        PracticeType.RANDOM
      ]
      baseFrequencyParam.completeTimeStart = undefined
      baseFrequencyParam.completeTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      // 统计收藏题数
      const baseFavoriteParam: UserFavoriteQuestionStatisticParamDTO = new UserFavoriteQuestionStatisticParamDTO()
      baseFavoriteParam.schemeId = payload.schemeId
      baseFavoriteParam.issueId = payload.issueId
      baseFavoriteParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      baseFavoriteParam.createTimeStart = undefined
      baseFavoriteParam.validTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )

      // 查询比较值
      // 统计 答题数和正确率
      const toCompareParam: UserPracticeStatisticParamDTO = new UserPracticeStatisticParamDTO()
      toCompareParam.schemeId = payload.schemeId
      toCompareParam.learningId = payload.learningId
      toCompareParam.statisticPracticeTypeList = [
        PracticeType.REAL,
        PracticeType.SIMULATION,
        PracticeType.PRACTICE,
        PracticeType.DAILY,
        PracticeType.ERROR_PRONE,
        PracticeType.RANDOM
      ]
      toCompareParam.completeTimeStart = undefined
      toCompareParam.completeTimeEnd = AnswerRecordUtils.getToCompareQueryEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      // 统计累计作答天数
      const toCompareFrequencyParam: UserPracticeStatisticParamDTO = new UserPracticeStatisticParamDTO()
      toCompareFrequencyParam.schemeId = payload.schemeId
      toCompareFrequencyParam.learningId = payload.learningId
      toCompareFrequencyParam.statisticPracticeTypeList = [
        PracticeType.REAL,
        PracticeType.SIMULATION,
        PracticeType.PRACTICE,
        PracticeType.DAILY,
        PracticeType.ERROR_PRONE,
        PracticeType.RANDOM
      ]
      toCompareFrequencyParam.completeTimeStart = undefined
      toCompareFrequencyParam.completeTimeEnd = AnswerRecordUtils.getToCompareQueryEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      // 统计收藏题数
      const toCompareFavoriteParam: UserFavoriteQuestionStatisticParamDTO = new UserFavoriteQuestionStatisticParamDTO()
      toCompareFavoriteParam.schemeId = payload.schemeId
      toCompareFavoriteParam.issueId = payload.issueId
      toCompareFavoriteParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      toCompareFavoriteParam.createTimeStart = undefined
      toCompareFavoriteParam.validTimeEnd = AnswerRecordUtils.getToCompareQueryEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )

      // 趋势统计
      // 正确率趋势
      const correctRateTrendParam: PracticeTrendStatisticParamDTO = new PracticeTrendStatisticParamDTO()
      correctRateTrendParam.schemeId = payload.schemeId
      correctRateTrendParam.learningId = payload.learningId
      correctRateTrendParam.completeTimeStart = AnswerRecordUtils.getStartTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      correctRateTrendParam.completeTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      correctRateTrendParam.statisticSize = AnswerRecordUtils.getTrendQueryTimes(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      correctRateTrendParam.statisticPracticeTypeList = [
        PracticeType.REAL,
        PracticeType.SIMULATION,
        PracticeType.PRACTICE,
        PracticeType.DAILY,
        PracticeType.ERROR_PRONE,
        PracticeType.RANDOM
      ]
      // 答题量趋势
      const practiceTrendParam: PracticeTrendStatisticParamDTO = new PracticeTrendStatisticParamDTO()
      practiceTrendParam.schemeId = payload.schemeId
      practiceTrendParam.learningId = payload.learningId
      practiceTrendParam.completeTimeStart = AnswerRecordUtils.getStartTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      practiceTrendParam.completeTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      practiceTrendParam.statisticSize = AnswerRecordUtils.getTrendQueryTimes(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      practiceTrendParam.statisticPracticeTypeList = [
        PracticeType.REAL,
        PracticeType.SIMULATION,
        PracticeType.PRACTICE,
        PracticeType.DAILY,
        PracticeType.ERROR_PRONE,
        PracticeType.RANDOM
      ]

      const response: Response<any> = await PlatformExamGateway._commonQuery(graphqlImporter.getPracticeReport, {
        statisticParam: param,
        allCategoryCountParam: paramDTO,
        frequencyParam: frequencyParam,
        favoriteParam: favoriteParam,
        baseStatisticParam: baseParam,
        baseFrequencyParam: baseFrequencyParam,
        baseFavoriteParam: baseFavoriteParam,
        toCompareStatisticParam: toCompareParam,
        toCompareFrequencyParam: toCompareFrequencyParam,
        toCompareFavoriteParam: toCompareFavoriteParam,
        correctRateTrendParam: correctRateTrendParam,
        practiceTrendParam: practiceTrendParam
      })
      if (!response.status.isSuccess()) {
        return response.status
      }
      // 统计 答题数和正确率
      stateCache.totalAnswerCount = response.data.statistic?.totalAnswerCount
      stateCache.correctRate = response.data.statistic?.correctRate
      // 各试题类型题数
      stateCache.allCategoryCount = response.data.allCategoryCount
      // 统计累计作答天数
      stateCache.keepPracticeDay = response.data.frequency?.practiceDayCount
      // 统计收藏题数
      stateCache.favoriteQuestionCount =
        response.data.favorite
          ?.map((p: UserFavoriteQuestionStatisticDTO) => p.questionCount)
          .reduce((a: number, b: number) => a + b, 0) || 0
      // 查询基准值
      // 统计 答题数和正确率
      stateCache.totalAnswerCountBase = response.data.baseStatistic?.totalAnswerCount
      stateCache.correctRateBase = response.data.baseStatistic?.correctRate
      // 统计累计作答天数
      stateCache.keepPracticeDayBase = response.data.baseFrequency?.practiceDayCount
      // 统计收藏题数
      stateCache.favoriteQuestionCountBase =
        response.data.baseFavorite
          ?.map((p: UserFavoriteQuestionStatisticDTO) => p.questionCount)
          .reduce((a: number, b: number) => a + b, 0) || 0
      // 查询比较值
      // 统计 答题数和正确率
      stateCache.totalAnswerCountToCompare = response.data.toCompareStatistic?.totalAnswerCount
      stateCache.correctRateToCompare = response.data.toCompareStatistic?.correctRate
      // 统计累计作答天数
      stateCache.keepPracticeDayToCompare = response.data.toCompareFrequency?.practiceDayCount
      // 统计收藏题数
      stateCache.favoriteQuestionCountToCompare =
        response.data.toCompareFavorite
          ?.map((p: UserFavoriteQuestionStatisticDTO) => p.questionCount)
          .reduce((a: number, b: number) => a + b, 0) || 0
      // 趋势统计
      // 正确率趋势
      stateCache.correctRateTrend = response.data.correctRateTrend
      // 答题量趋势
      stateCache.practiceTrend = response.data.practiceTrend

      // 比较趋势统计
      // 正确率趋势
      stateCache.correctRateTrendToCompare = []
      stateCache.practiceTrendToCompare = []
      if (payload.range === DateRange.YESTERDAY) {
        correctRateTrendParam.completeTimeStart = undefined
        correctRateTrendParam.completeTimeEnd = undefined
        const correctRateTrendResponse = await PlatformExamGateway.statisticUserPracticeCorrectRateTrend(
          correctRateTrendParam
        )
        if (!correctRateTrendResponse.status.isSuccess()) {
          return correctRateTrendResponse.status
        }
        stateCache.correctRateTrendToCompare = correctRateTrendResponse.data
        // 答题量趋势
        practiceTrendParam.completeTimeStart = undefined
        practiceTrendParam.completeTimeEnd = undefined
        const practiceCountTrendResponse = await PlatformExamGateway.statisticUserPracticeCountTrend(practiceTrendParam)
        if (!practiceCountTrendResponse.status.isSuccess()) {
          return practiceCountTrendResponse.status
        }
        stateCache.practiceTrendToCompare = practiceCountTrendResponse.data
      }
      this.setStateCacheToLearningStateCacheMap(stateCache)
    } else if (
      (this.learningStateCacheMap.find(p => p.learningId === payload.learningId)?.latestLoadTime?.getTime() || 0) <
      new Date().getTime() - 10000
    ) {
      this.setNeedReload({
        schemeId: payload.schemeId,
        learningId: payload.learningId,
        majorId: payload.majorId
      })
      this.init(payload)
    }
  }

  @Mutation
  setStateCacheToLearningStateCacheMap(payload: StateCache) {
    this.learningStateCacheMap = this.learningStateCacheMap.filter(p => p.learningId !== payload.learningId)
    this.learningStateCacheMap.push(payload)
  }

  @Mutation
  setNeedReload(payload: any) {
    const stateCache = this.learningStateCacheMap.find(p => p.learningId === payload.learningId)
    if (stateCache) {
      stateCache.needReload = true
    }
  }

  /**
   * 获取状态层数据
   * @param state
   */
  get getState() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)
    }
  }

  /**
   * 获取总做题数量
   * @param state
   */
  get getTotalAnswerCount() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.totalAnswerCount
    }
  }

  /**
   * 获取各试题类型做题数量
   * @param state
   */
  get getAllCategoryCount() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.allCategoryCount
    }
  }

  /**
   * 获取正确率
   * @param state
   */
  get getCorrectRate() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.correctRate
    }
  }

  /**
   * 获取累计练习天数
   * @param state
   */
  get getKeepPracticeDay() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.keepPracticeDay
    }
  }

  /**
   * 获取收藏试题数
   * @param state
   */
  get getFavoriteQuestionCount() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.favoriteQuestionCount || 0
    }
  }

  /**
   * 获取基础做题总量
   * @param state
   */
  get getTotalAnswerCountBase() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.totalAnswerCountBase || 0
    }
  }

  /**
   * 获取基础正确率
   * @param state
   */
  get getCorrectRateBase() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.correctRateBase || 0
    }
  }

  /**
   * 获取基础累计练习（天）
   * @param state
   */
  get getKeepPracticeDayBase() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.keepPracticeDayBase || 0
    }
  }

  /**
   * 获取基础收藏试题数
   * @param state
   */
  get getFavoriteQuestionCountBase() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.favoriteQuestionCountBase || 0
    }
  }

  /**
   * 要比较的做题总量
   * @param state
   */
  get getTotalAnswerCountToCompare() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.totalAnswerCountToCompare || 0
    }
  }

  /**
   * 要比较的做题总量
   * @param state
   */
  get getCorrectRateToCompare() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.correctRateToCompare || 0
    }
  }

  /**
   * 要比较的累计练习（天）
   * @param state
   */
  get getKeepPracticeDayToCompare() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.keepPracticeDayToCompare || 0
    }
  }

  /**
   * 要比较的收藏试题数
   * @param state
   */
  get getFavoriteQuestionCountToCompare() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.favoriteQuestionCountToCompare || 0
    }
  }

  /**
   * 正确率趋势
   * @param state
   */
  get getCorrectRateTrend() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.correctRateTrend
    }
  }

  /**
   * 练习题量趋势
   * @param state
   */
  get getPracticeTrend() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.practiceTrend
    }
  }

  /**
   * 要比较的正确率趋势
   * @param state
   */
  getCorrectRateTrendToCompare() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.correctRateTrendToCompare
    }
  }

  /**
   * 要比较的练习题量趋势
   * @param state
   */
  get getPracticeTrendToCompare() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.practiceTrendToCompare
    }
  }

  get correctRateChange() {
    return (schemeId: string, learningId: string) => {
      return this.getCorrectRateBase(schemeId, learningId) - this.getCorrectRateToCompare(schemeId, learningId)
    }
  }

  get totalAnswerCountChange() {
    return (schemeId: string, learningId: string) => {
      return (
        this.getTotalAnswerCountBase(schemeId, learningId) - this.getTotalAnswerCountToCompare(schemeId, learningId)
      )
    }
  }

  get keepPracticeDayChange() {
    return (schemeId: string, learningId: string) => {
      return this.getKeepPracticeDayBase(schemeId, learningId) - this.getKeepPracticeDayToCompare(schemeId, learningId)
    }
  }

  get favoriteQuestionCountChange() {
    return (schemeId: string, learningId: string) => {
      return (
        this.getFavoriteQuestionCountBase(schemeId, learningId) -
        this.getFavoriteQuestionCountToCompare(schemeId, learningId)
      )
    }
  }
}

export default getModule(PracticeReportModule)
