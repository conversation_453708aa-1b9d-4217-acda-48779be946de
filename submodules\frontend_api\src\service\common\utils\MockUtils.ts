import Mockjs from 'mockjs'

class MockUtils {
  static mockString() {
    return Mockjs.Random.csentence(10, 10)
  }
  static mockYear() {
    return Mockjs.Random.integer(1990, 2030).toString()
  }
  static mockBoolean() {
    return Mockjs.Random.boolean()
  }
  static mockEnum() {
    return Mockjs.Random.integer(1, 4)
  }
  static mockTime() {
    return Mockjs.Random.datetime()
  }
  static mockRegionPath() {
    return Mockjs.Random.integer(100000, 400000).toString()
  }
  static mockRegion() {
    return Mockjs.Random.city()
  }
  static mockInt() {
    return Mockjs.Random.integer(1, 500)
  }
  static mockStringArray() {
    const list = new Array<string>()
    new Array(10).fill('').forEach(item => {
      list.push(Mockjs.Random.string(10, 10))
    })
    return list
  }
  static mockUrl() {
    return Mockjs.Random.url()
  }
  static mockName() {
    return Mockjs.Random.cname()
  }
  static mockTotalPageSize() {
    return Mockjs.Random.integer(5, 20)
  }
  static mockTotalSize() {
    return Mockjs.Random.integer(50, 200)
  }
  static mockSex() {
    return Mockjs.Random.integer(0, 1) == 0 ? '男' : '女'
  }
  static mockPhone() {
    return Mockjs.Random.integer(18800000000, 19000000000).toString()
  }
  static mockAge() {
    return Mockjs.Random.integer(1, 100)
  }
}
export default MockUtils
