<route-params content="/:id"></route-params>
<route-meta>
{
  "title": "用户详情",
  "requiresAuth": true,
  "permission": "user.view",
  "isMenu": false
}
</route-meta>

<template>
  <div>
    <!-- 直接引用我们创建的用户信息组件 -->
    <UserInfoPage />
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import UserInfoPage from '@packages/user/src/views/user-info/index.vue'

  @Component({
    components: {
      UserInfoPage
    }
  })
  export default class UserInfoRoute extends Vue {
    // 这个文件会自动生成路由: /user/:id/info
  }
</script>
