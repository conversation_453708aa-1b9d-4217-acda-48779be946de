import {
  CourseLearningStatisticsResponse,
  ExamLearningStatisticsResponse,
  RegionLearningStatisticsResponse,
  RegionResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
/**
 * 学员培训方案学习统计信息
 <AUTHOR>
 @version 1.0
 @date 2022/1/17 14:08
 */
export class LearningStatisticsResponse {
  /**
   * 净报名人次
   */
  netRegisterCount: number
  /**
   * 考核通过人次
   */
  qualifiedCount: number
  /**
   * 课程学习统计
   */
  courseLearningStatistic: CourseLearningStatisticsResponse = new CourseLearningStatisticsResponse()
  /**
   * 考试统计
   */
  examLearningStatistic: ExamLearningStatisticsResponse = new ExamLearningStatisticsResponse()
  /**
   * 期别合格数
   */
  issueQualifiedCount = 0
  /**
   * 期别未合格数
   */
  issueUnQualifiedCount = 0
  /**
   * 问卷未提交的学院方案参训资格数
   */
  questionnaireUnSubmitCount = 0
  /**
   * 问卷已提交的学院方案参训资格数
   */
  questionnaireSubmitCount = 0
}
export class RegionLearningStatisticsResponseVo {
  /**
   * 学员所属地区路径
   */
  region: RegionResponse = new RegionResponse()
  /**
   * 统计信息
   */
  learningStatistic: LearningStatisticsResponse = new LearningStatisticsResponse()
  /**
   * 地区id
   */
  id: string
}
