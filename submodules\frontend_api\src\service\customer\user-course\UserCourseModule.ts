import PlatformCourseGateway, { CourseCategoryList } from '@api/gateway/PlatformCourse'
import CourseLearningGateway, {
  BatchChooseCourseRequest,
  UserBatchChooseCourseRequest
} from '@api/gateway/CourseLearning-default'
import PlatformUserCourseGateway, {
  Page,
  UserCourseDTO,
  UserCourseLearningScheduleDTO,
  UserCoursewareLearningScheduleDTO
} from '@api/gateway/PlatformUserCourse'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import LastLearnCourse from './model/LastLearnCourse'
import { UserCoursewareItem } from './model/UserCoursewareItem'
import UserCourse from './model/UserCourse'
import { ResponseStatus } from '../../../Response'
import moment from 'moment'
import UserCourseware from './model/UserCourseware'
import { Role, RoleType, Secure } from '@api/Secure'
import PoolCourse from '@api/service/customer/user-course/model/PoolCourse'
import PoolMapCourse from '@api/service/customer/user-course/model/PoolMapCourse'
import PoolMapUserCourse from '@api/service/customer/user-course/model/PoolMapUserCourse'
import { CoursePool } from '@api/service/customer/user-course/model/CoursePool'
import PlatformLearningScheme, { CourseInPoolResponse } from '@api/gateway/PlatformLearningScheme'
import CourseCategory from '@api/service/common/models/course/course-category/CourseCategory'
import CoursePoolCustomerModule from '@api/service/customer/course-pool/CoursePoolCustomerModule'
import PreExamLsGateway from '@api/gateway/NormalIssueClassLS-default'
import LearningSchemeModule from '@api/service/customer/myscheme/LearningSchemeModule'
import { CourseInPoolInfo } from '@api/service/customer/user-course/model/CourseInPoolInfo'
import CourseTeacher from '@api/service/common/models/course/CourseTeacher'
import { Constants } from '@api/service/common/models/common/Constants'
import CourseChapter from '@api/service/common/models/course/CourseChapter'

interface UserInterestCourseItem {
  /**
   * 用户已选兴趣课程
   */
  interestCourseList: Array<PoolMapUserCourse>
  /**
   * 用户已选兴趣课程包
   */
  interestCoursePoolList: Array<CoursePool>
  /**
   * 未选的兴趣课程
   */
  unSelectInterestCourseList: Array<PoolMapCourse>
  /**
   * 用户未选兴趣课程包
   */
  unSelectInterestCoursePoolList: Array<CoursePool>
  /**
   * 是否加载兴趣课程信息
   */
  isLoadInterestCourse: boolean
  /**
   * 是否加载未选的兴趣课程信息
   */
  isLoadUnSelectedInterestCourse: boolean
}

interface UserCourseItem {
  /**
   * 最后学习的课程
   */
  lastLearnCourse: LastLearnCourse
  /**
   * 用户已选课程
   */
  myCourseList: Array<UserCourse>
  /**
   * 用户已选课程对应课件信息
   */
  myCoursewareList: Array<UserCoursewareItem>
  /**
   * 未选择的课程
   */
  unSelectedCourseList: Array<PoolMapCourse>
  /**
   * 是否加载我的课程列表
   */
  isLoadMyCourseList: boolean
  /**
   * 是否加载未选的课程
   */
  isLoadUnSelectedCourseList: boolean
  /**
   * 是否加载课程包信息
   */
  isLoadCoursePool: boolean
}

interface MapX<K extends string, V> {
  key: K
  value: V
}

/**
 * 用户课程模块
 */
interface UserCourseModuleState {
  /**
   * 学习方式下的用户课程
   */
  userCourseList: Array<MapX<string, UserCourseItem>>
  /**
   * 学习方案下的兴趣课程
   */
  userInterestCourseList: Array<MapX<string, UserInterestCourseItem>>
}

class CourseSchedule {
  courseId: string
  lastStudyTime: Date
  schedule: number
  studyState: number
}

class CoursewareSchedule {
  coursewareId: string
  courseId: string
  lastStudyTime: Date
  schedule: number
  studyState: number
}

const internalFunction = {
  /**
   * 获取当前学习方式下用户课程信息
   * @param state 状态
   * @param learningId 学习方式编号
   */
  getUserCourseItem: (state: UserCourseModuleState, learningId: string): UserCourseItem | undefined => {
    const map = state.userCourseList.find(x => x.key === learningId)
    if (map) {
      return map.value
    }
    return undefined
  },
  /**
   * 获取当前方案下用户兴趣课程信息
   * @param state 状态
   * @param schemeId 学习方式编号
   */
  getUserInterestCourseItem: (state: UserCourseModuleState, schemeId: string): UserInterestCourseItem | undefined => {
    const map = state.userInterestCourseList.find(x => x.key === schemeId)
    if (map) {
      return map.value
    }
    return undefined
  },
  /**
   * 是否加载课件列表
   * @param state 状态
   * @param learningId 学习方式编号
   * @param courseId 课程编号
   */
  getUserCoursewareItem: (
    state: UserCourseModuleState,
    learningId: string,
    courseId: string
  ): UserCoursewareItem | undefined => {
    const map = internalFunction.getUserCourseItem(state, learningId)
    if (map) {
      return map.myCoursewareList.find(v => v.courseId === courseId)
    }
    return undefined
  },
  /**
   * 初始化Map结构
   * @param state 状态
   * @param learningId 学习方式编号
   * @param callback 回调函数
   */
  initWhenNotExists: (
    state: UserCourseModuleState,
    learningId: string,
    callback: (userCourse: UserCourseItem) => void
  ) => {
    const map = state.userCourseList.find(x => x.key === learningId)
    if (!map) {
      state.userCourseList.push({
        key: learningId,
        value: {
          lastLearnCourse: new LastLearnCourse(),
          myCourseList: new Array<UserCourse>(),
          myCoursewareList: new Array<UserCoursewareItem>(),
          unSelectedCourseList: new Array<PoolMapCourse>(),
          isLoadMyCourseList: false,
          isLoadUnSelectedCourseList: false,
          isLoadCoursePool: false
        }
      } as MapX<string, UserCourseItem>)
    }
    const item = internalFunction.getUserCourseItem(state, learningId)
    if (item) {
      callback(item)
    }
  },

  /**
   * 初始化兴趣课程Map结构
   * @param state 状态
   * @param schemeId 学习方式编号
   * @param callback 回调函数
   */
  initInterestCourseWhenNotExists: (
    state: UserCourseModuleState,
    schemeId: string,
    callback: (userCourse: UserInterestCourseItem) => void
  ) => {
    const map = state.userInterestCourseList.find(x => x.key === schemeId)
    if (!map) {
      state.userInterestCourseList.push({
        key: schemeId,
        value: {
          interestCourseList: new Array<PoolMapUserCourse>(),
          unSelectInterestCourseList: new Array<PoolMapCourse>(),
          isLoadInterestCourse: false,
          isLoadUnSelectedInterestCourse: false
        }
      } as MapX<string, UserInterestCourseItem>)
    }
    const item = internalFunction.getUserInterestCourseItem(state, schemeId)
    if (item) {
      callback(item)
    }
  },
  /**
   * 获取指定课程包未选择课程数据
   * @param userCourse 用户课程数据
   * @param poolId 课程包编号
   */
  getPoolMapCourse: (userCourse: UserCourseItem, poolId: string): PoolMapCourse | undefined => {
    return userCourse.unSelectedCourseList.find(e => e.poolId === poolId)
  },
  /**
   * 指示远程获取教师信息
   * @param teacherIdList 教师编号
   */
  loadTeacherList: async (teacherIdList: Array<string>): Promise<Array<CourseTeacher>> => {
    // 获取课程教师编号并且去重
    const currentTeacherIdList = teacherIdList.filter((value, index, array) => array.indexOf(value, 0) === index)
    const courseTeacherList: Array<CourseTeacher> = new Array<CourseTeacher>()
    if (currentTeacherIdList?.length) {
      const response = await PlatformCourseGateway.listTeachersByIds(currentTeacherIdList)
      if (response.status.isSuccess()) {
        const teacherList = response.data
        for (const teacherDto of teacherList) {
          const courseTeacher: CourseTeacher = new CourseTeacher()
          courseTeacher.id = teacherDto.id
          courseTeacher.abouts = teacherDto.abouts
          courseTeacher.gender = teacherDto.gender
          courseTeacher.name = teacherDto.name
          courseTeacher.photo = teacherDto.photo ? '/mfs' + teacherDto.photo : ''
          courseTeacher.professionalTitle = teacherDto.professionalTitle
          courseTeacherList.push(courseTeacher)
        }
      } else {
        return Promise.reject(response.status)
      }
    }
    return courseTeacherList
  },
  /**
   * 批量执行调用函数
   * @param run 执行函数
   * @return 响应状态
   */
  execute: async (run: Function): Promise<ResponseStatus> => {
    try {
      await run()
      return new ResponseStatus(200, '成功')
    } catch (e) {
      if (e instanceof ResponseStatus) {
        return e
      } else {
        return new ResponseStatus(500, '状态层异常' + e)
      }
    }
  }
}

export class UnSelectedCourseParameter {
  /**
   * 学习方案编号
   */
  schemeId: string
  /**
   * 课程学习方式编号
   */
  learningId: string
  /**
   * 课程包编号
   */
  poolId: string
  /**
   * 每页条数
   */
  pageSize?: number
  /**
   * 页码
   */
  pageNo?: number
  /**
   * 是否增量，即是否保存原来数据，如果为false，则只存储当前页数据
   */
  append: boolean
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerUserCourseModule', store })
class UserCourseModule extends VuexModule implements UserCourseModuleState {
  //region implements

  /**
   * 学习方式下的用户课程
   */
  public userCourseList: Array<MapX<string, UserCourseItem>> = new Array<MapX<string, UserCourseItem>>()

  /**
   * 学习方案下的兴趣课程
   */
  userInterestCourseList: Array<MapX<string, UserInterestCourseItem>> = new Array<
    MapX<string, UserInterestCourseItem>
  >()
  //endregion

  //region Actions

  /**
   * 加载我的选课列表
   * @param params 参数：schemeId - 学习方案编号；learningId - 学习方式编号
   */
  @Action
  @Role([RoleType.user])
  public async init(params: { schemeId: string; learningId: string }): Promise<ResponseStatus> {
    return internalFunction.execute(async () => {
      if (!params.schemeId || !params.learningId) {
        return Promise.resolve(new ResponseStatus(500, ''))
      }
      let stateData = internalFunction.getUserCourseItem(this, params.learningId)
      // region 加载用户课程数据不包含进度信息
      if (!stateData || !stateData.isLoadMyCourseList) {
        const userCourseResponse = await PlatformUserCourseGateway.listUserCourse({
          schemeId: params.schemeId,
          learningId: params.learningId
        })
        const myCourseList: Array<UserCourse> = new Array<UserCourse>()
        if (userCourseResponse.status.isSuccess()) {
          const userCourseDTOList: Array<UserCourseDTO> = userCourseResponse.data
          const teacherList = await internalFunction.loadTeacherList(userCourseDTOList.flatMap(x => x.teacherIdList))
          await CoursePoolCustomerModule.listCoursePoolInfoByIds(userCourseDTOList.map(p => p.poolId))
          const poolList = CoursePoolCustomerModule.courseInfoPoolsCache
          for (const userCourseDTO of userCourseDTOList) {
            const userCourse: UserCourse = new UserCourse()
            userCourse.isSelected = true
            userCourse.id = userCourseDTO.id
            userCourse.name = userCourseDTO.name
            userCourse.iconPath = userCourseDTO.iconPath ? '/mfs' + userCourseDTO.iconPath : ''
            userCourse.totalLecture = userCourseDTO.courseWareCount
            userCourse.alreadyUpdateLecture = userCourseDTO.courseWareUpdateCount
            userCourse.timeLength = userCourseDTO.timeLength
            userCourse.abouts = userCourseDTO.abouts
            userCourse.poolId = userCourseDTO.poolId
            userCourse.period = userCourseDTO.period
            userCourse.courseType = userCourseDTO.courseType
            userCourse.poolName = poolList.find(p => p.id === userCourseDTO.poolId)?.showName || '-'
            if (userCourseDTO?.teacherIdList?.length) {
              userCourse.teacherList = teacherList.filter(x => userCourseDTO.teacherIdList.includes(x.id))
            }
            const categoryList = new Array<CourseCategory>()
            userCourseDTO.courseCategoryDtoList.forEach(c => {
              const category = new CourseCategory()
              category.id = c.id
              category.name = c.name
              category.parentId = c.parentId
              categoryList.push(category)
            })
            userCourse.categoryList = categoryList
            myCourseList.push(userCourse)
          }
          this.SET_MY_COURSE_LIST({ learningId: params.learningId, myCourseList: myCourseList })
          stateData = internalFunction.getUserCourseItem(this, params.learningId)
        } else {
          return Promise.reject(userCourseResponse.status)
        }
      }
      // endregion
      // region 加载用户课程进度信息
      if (stateData) {
        // 进度信息每次都刷新
        const courseIdList = stateData.myCourseList.map(x => x.id)
        const courseScheduleResponse = await PlatformUserCourseGateway.listUserCourseLearningSchedule({
          courseIdList: courseIdList,
          schemeId: params.schemeId,
          learningId: params.learningId
        })
        if (courseScheduleResponse.status.isSuccess()) {
          const courseScheduleList: Array<UserCourseLearningScheduleDTO> = courseScheduleResponse.data
          const currentCourseScheduleList: Array<CourseSchedule> = courseScheduleList.map(x => {
            const courseSchedule: CourseSchedule = new CourseSchedule()
            courseSchedule.courseId = x.courseId
            courseSchedule.lastStudyTime = moment(x.lastStudyTime, Constants.DATE_PATTERN).toDate()
            courseSchedule.schedule = x.schedule
            courseSchedule.studyState = x.studyState
            return courseSchedule
          })
          this.SET_COURSE_SCHEDULE({
            learningId: params.learningId,
            schemeId: params.schemeId,
            courseScheduleList: currentCourseScheduleList,
            source: 'assessCourse'
          })
        } else {
          return Promise.reject(courseScheduleResponse.status)
        }
      }
      // endregion
      await this.doLoadInterestCourse({ schemeId: params.schemeId })
    })
  }

  /**
   * 加载指定培训班课程学习方式下课程包列表
   * @param params
   */
  @Action
  @Role([RoleType.user])
  public async initCoursePool(params: { schemeId: string; learningId: string }): Promise<ResponseStatus> {
    if (!params.schemeId || !params.learningId) {
      return Promise.resolve(new ResponseStatus(500, ''))
    }
    const stateData = internalFunction.getUserCourseItem(this, params.learningId)
    if (!stateData || !stateData.isLoadCoursePool) {
      const { status, data } = await PlatformUserCourseGateway.listUserCoursePool({
        schemeId: params.schemeId,
        learningId: params.learningId
      })
      if (status.isSuccess()) {
        if (data) {
          const mapList: Array<PoolMapCourse> = data.map(e => {
            const map: PoolMapCourse = new PoolMapCourse()
            map.poolId = e.coursePoolId
            map.poolName = e.showName
            return map
          })
          this.SET_UNSELECTED_MAP_LIST({ learningId: params.learningId, mapList: mapList })
        }
        return Promise.resolve(status)
      } else {
        return Promise.reject(status)
      }
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 重新加载未选课程包列表
   * @param params
   */
  @Action
  @Role([RoleType.user])
  public async doReloadCoursePool(params: { schemeId: string; learningId: string }): Promise<ResponseStatus> {
    this.SET_IS_LOAD_UNSELECTED_MAP_LIST({ learningId: params.learningId, isLoad: false })
    return this.initCoursePool(params)
  }

  /**
   * 加载未选择的课程
   * @param params
   */
  @Action
  @Role([RoleType.user])
  public async doLoadUnSelectedCourse(params: UnSelectedCourseParameter): Promise<ResponseStatus> {
    return internalFunction.execute(async () => {
      const stateData = internalFunction.getUserCourseItem(this, params.learningId)
      if (!stateData || !stateData.isLoadUnSelectedCourseList) {
        await this.initCoursePool({ schemeId: params.schemeId, learningId: params.learningId })
        const page: Page = new Page()
        page.pageNo = params.pageNo
        page.pageSize = params.pageSize
        const unSelectedCourseResponse = await PlatformUserCourseGateway.listUserUnSelectCourse({
          page: page,
          paramDTO: {
            coursePoolId: params.poolId,
            schemeId: params.schemeId,
            learningId: params.learningId
          }
        })
        if (unSelectedCourseResponse.status.isSuccess()) {
          const courseListDto = unSelectedCourseResponse.data.currentPageData
          const courseList: Array<PoolCourse> = new Array<PoolCourse>()
          const teacherList = await internalFunction.loadTeacherList(courseListDto.flatMap(x => x.teacherIdList))
          for (const courseDto of courseListDto) {
            const course: PoolCourse = new PoolCourse()
            course.id = courseDto.id
            course.name = courseDto.name
            course.iconPath = courseDto.iconPath ? '/mfs' + courseDto.iconPath : ''
            course.totalLecture = courseDto.courseWareCount
            course.alreadyUpdateLecture = courseDto.courseWareUpdateCount
            course.timeLength = courseDto.timeLength
            course.abouts = courseDto.abouts
            course.courseType = courseDto.courseType
            course.poolId = courseDto.poolId
            course.period = courseDto.period
            if (courseDto?.teacherIdList?.length) {
              course.teacherList = teacherList.filter(x => courseDto.teacherIdList.includes(x.id))
            }
            const categoryList = new Array<CourseCategory>()
            courseDto.courseCategoryDtoList.forEach(c => {
              const category = new CourseCategory()
              category.id = c.id
              category.name = c.name
              category.parentId = c.parentId
              categoryList.push(category)
            })
            course.categoryList = categoryList
            courseList.push(course)
          }
          const map: PoolMapCourse = new PoolMapCourse()
          map.courseList = courseList
          map.poolId = params.poolId
          map.totalPageSize = unSelectedCourseResponse.data.totalPageSize
          map.totalSize = unSelectedCourseResponse.data.totalSize
          if (courseList.length > 0) {
            this.SET_UNSELECTED_COURSE_LIST({ learningId: params.learningId, map: map, append: params.append })
          }
        } else {
          return Promise.reject(unSelectedCourseResponse.status)
        }
      }
    })
  }

  /**
   * 加载未选择的课程【所有】
   * @param params
   */
  @Action
  @Role([RoleType.user])
  public async doLoadUnSelectedCourseList(params: UnSelectedCourseParameter): Promise<ResponseStatus> {
    return internalFunction.execute(async () => {
      const stateData = internalFunction.getUserCourseItem(this, params.learningId)
      if (!stateData || !stateData.isLoadUnSelectedCourseList) {
        await this.initCoursePool({ schemeId: params.schemeId, learningId: params.learningId })
        const page = new Page()
        page.pageNo = 1
        page.pageSize = 50
        const totalSize = 0
        let response
        const list = new Array<UserCourseDTO>()
        do {
          response = await PlatformUserCourseGateway.listUserUnSelectCourse({
            page: page,
            paramDTO: {
              coursePoolId: params.poolId,
              schemeId: params.schemeId,
              learningId: params.learningId
            }
          })
          if (!response.status.isSuccess()) {
            break
          }
          list.push(...response.data.currentPageData)
        } while (page.pageNo++ * page.pageSize <= totalSize)
        if (response.status.isSuccess()) {
          const courseListDto = list
          const courseList: Array<PoolCourse> = new Array<PoolCourse>()
          const teacherList = await internalFunction.loadTeacherList(courseListDto.flatMap(x => x.teacherIdList))
          for (const courseDto of courseListDto) {
            const course: PoolCourse = new PoolCourse()
            course.id = courseDto.id
            course.name = courseDto.name
            course.iconPath = courseDto.iconPath ? '/mfs' + courseDto.iconPath : ''
            course.totalLecture = courseDto.courseWareCount
            course.alreadyUpdateLecture = courseDto.courseWareUpdateCount
            course.timeLength = courseDto.timeLength
            course.abouts = courseDto.abouts
            course.courseType = courseDto.courseType
            course.poolId = courseDto.poolId
            course.period = courseDto.period
            if (courseDto?.teacherIdList?.length) {
              course.teacherList = teacherList.filter(x => courseDto.teacherIdList.includes(x.id))
            }
            const categoryList = new Array<CourseCategory>()
            courseDto.courseCategoryDtoList.forEach(c => {
              const category = new CourseCategory()
              category.id = c.id
              category.name = c.name
              category.parentId = c.parentId
              categoryList.push(category)
            })
            course.categoryList = categoryList
            course.customStatus = courseDto.supportAudition ? 1 : 0
            courseList.push(course)
          }
          const map: PoolMapCourse = new PoolMapCourse()
          map.courseList = courseList
          map.poolId = params.poolId
          map.totalPageSize = response.data.totalPageSize
          map.totalSize = response.data.totalSize
          if (courseList.length > 0) {
            this.SET_UNSELECTED_COURSE_LIST({ learningId: params.learningId, map: map, append: params.append })
          }
        } else {
          return Promise.reject(response.status)
        }
      }
    })
  }

  /**
   * 加载未选择的兴趣课程
   * @param params
   */
  @Action
  @Role([RoleType.user])
  public async doLoadUnSelectedInterestCourse(params: { schemeId: string }): Promise<ResponseStatus> {
    return internalFunction.execute(async () => {
      const stateData = internalFunction.getUserInterestCourseItem(this, params.schemeId)
      if (!stateData || !stateData.isLoadUnSelectedInterestCourse) {
        const interestCourseResponse = await PlatformLearningScheme.listInterestCourse(params.schemeId)
        if (interestCourseResponse.status.isSuccess()) {
          const hasSelectInterestCourseIdList =
            stateData?.interestCourseList.flatMap(p => p.courseList).map(p => p.id) || new Array<string>()

          const interestCourseList: Array<CourseInPoolResponse> = interestCourseResponse.data
          // 查询课程包下的课程信息，取出学分，这步是必须的，反倒是下面的批量查询课程如果不要教师信息大可不必
          const courseInPoolList = new Array<CourseInPoolInfo>()
          for (const courseInPoolResponse of interestCourseList) {
            const res = await PlatformCourseGateway.listCourseInPool(courseInPoolResponse.poolId)
            if (res.status.isSuccess()) {
              courseInPoolList.push({
                poolId: courseInPoolResponse.poolId,
                courses: res.data
              })
            }
          }
          const courseDetailResponse = await PlatformCourseGateway.listCourse(interestCourseList.map(p => p.courseId))
          if (courseDetailResponse.status.isSuccess()) {
            const courseDetailList = courseDetailResponse.data
            const teacherList = await internalFunction.loadTeacherList(courseDetailList.flatMap(x => x.teacherIdList))
            const courseCategoryListResponse = await PlatformCourseGateway.listCourseCategory(
              courseDetailList.map(p => p.id)
            )
            const courseCategoryList: Array<CourseCategoryList> = courseCategoryListResponse.data

            const poolList = new Array<CoursePool>()
            for (const courseInPoolResponse of interestCourseList.filter(
              p => hasSelectInterestCourseIdList.indexOf(p.courseId) < 0
            )) {
              if (poolList.findIndex(p => p.poolId === courseInPoolResponse.poolId) < 0) {
                const pool = new CoursePool()
                pool.poolId = courseInPoolResponse.poolId
                pool.poolName = courseInPoolResponse.showName
                poolList.push(pool)
              }
            }
            this.SET_UN_SELECT_INTEREST_COURSE_POOL_LIST({ schemeId: params.schemeId, poolList: poolList })

            const map: Array<PoolMapCourse> = new Array<PoolMapCourse>()
            for (const coursePool of poolList) {
              const courseList: Array<PoolCourse> = new Array<PoolCourse>()
              for (const courseInPool of interestCourseList.filter(
                p => p.poolId === coursePool.poolId && hasSelectInterestCourseIdList.indexOf(p.courseId) < 0
              )) {
                const courseDetail = courseDetailList.find(p => p.id === courseInPool.courseId)
                const course: PoolCourse = new PoolCourse()
                course.id = courseInPool.courseId
                course.poolId = courseInPool.poolId
                if (courseDetail) {
                  course.name = courseDetail.name
                  course.iconPath = courseDetail.iconPath ? '/mfs' + courseDetail.iconPath : ''
                  course.timeLength = courseDetail.timeLength
                  course.abouts = courseDetail.abouts
                  course.period = courseInPoolList
                    .find(pool => pool.poolId === courseInPool.poolId)
                    ?.courses?.find(c => c.courseId === courseInPool.courseId)?.period
                  // course.period = courseDetail.period
                  if (courseDetail?.teacherIdList?.length) {
                    course.teacherList = teacherList.filter(x => courseDetail.teacherIdList.includes(x.id))
                  }
                  course.customStatus = courseDetail.supportAudition ? 1 : 0
                }
                const courseCategory = courseCategoryList.find(x => x.courseId === course.id)
                if (courseCategory && courseCategory.categoryList.length) {
                  for (const item of courseCategory.categoryList) {
                    const courseCategory = new CourseCategory()
                    courseCategory.id = item.id
                    courseCategory.name = item.name
                    course.categoryList.push(courseCategory)
                  }
                }
                courseList.push(course)
              }
              const item: PoolMapCourse = new PoolMapCourse()
              item.courseList = courseList
              item.poolId = coursePool.poolId
              map.push(item)
            }
            this.SET_UN_SELECT_INTEREST_COURSE_LIST({ schemeId: params.schemeId, interestCourseList: map })
          }
        } else {
          return Promise.reject(interestCourseResponse.status)
        }
      }
    })
  }

  /**
   * 加载已选择的兴趣课程
   * @param params
   */
  @Action
  @Role([RoleType.user])
  public async doLoadInterestCourse(params: { schemeId: string }): Promise<ResponseStatus> {
    return internalFunction.execute(async () => {
      let stateData = internalFunction.getUserInterestCourseItem(this, params.schemeId)
      if (!stateData || !stateData.isLoadInterestCourse) {
        const interestCourseResponse = await PlatformUserCourseGateway.listUserCourse({
          schemeId: params.schemeId,
          source: 'interestCourse'
        })
        if (interestCourseResponse.status.isSuccess() && interestCourseResponse.data.length) {
          const courseListDto = interestCourseResponse.data
          const teacherList = await internalFunction.loadTeacherList(courseListDto.flatMap(x => x.teacherIdList))
          const courseCategoryListResponse = await PlatformCourseGateway.listCourseCategory(
            courseListDto.map(p => p.id)
          )
          const courseCategoryList: Array<CourseCategoryList> = courseCategoryListResponse.data

          const poolList: Array<CoursePool> = new Array<CoursePool>()
          const poolIdList = courseListDto.map(p => p.poolId)
          const coursePoolResponse = await CoursePoolCustomerModule.listCoursePoolInfoByIds(poolIdList)
          if (coursePoolResponse.isSuccess()) {
            const poolDetailList = CoursePoolCustomerModule.courseInfoPoolsCache
            // 遍历包含的所有课程包
            for (const userCourseDTO of courseListDto) {
              if (poolList.findIndex(p => p.poolId === userCourseDTO.poolId) < 0) {
                const pool = new CoursePool()
                pool.poolId = userCourseDTO.poolId
                const poolDetail = poolDetailList.find(p => p.id === pool.poolId)
                pool.poolName = poolDetail?.showName || poolDetail?.poolName || ''
                poolList.push(pool)
              }
            }
            this.SET_INTEREST_COURSE_POOL_LIST({ schemeId: params.schemeId, poolList: poolList })
          } else {
            console.log('兴趣课程包加载失败')
          }
          const interestCourseList: Array<PoolMapUserCourse> = new Array<PoolMapUserCourse>()
          for (const pool of poolList) {
            const item = new PoolMapUserCourse()
            const courseList: Array<UserCourse> = new Array<UserCourse>()
            for (const courseDto of courseListDto.filter(p => p.poolId === pool.poolId)) {
              const course: UserCourse = new UserCourse()
              course.id = courseDto.id
              course.name = courseDto.name
              course.iconPath = courseDto.iconPath ? '/mfs' + courseDto.iconPath : ''
              course.timeLength = courseDto.timeLength
              course.abouts = courseDto.abouts
              course.poolId = courseDto.poolId
              course.period = courseDto.period
              if (courseDto?.teacherIdList?.length) {
                course.teacherList = teacherList.filter(x => courseDto.teacherIdList.includes(x.id))
              }
              course.categoryList = new Array<CourseCategory>()
              const courseCategory = courseCategoryList.find(p => p.courseId === course.id)
              if (courseCategory && courseCategory.categoryList.length) {
                for (const item of courseCategory.categoryList) {
                  const courseCategory = new CourseCategory()
                  courseCategory.id = item.id
                  courseCategory.name = item.name
                  course.categoryList.push(courseCategory)
                }
              }
              courseList.push(course)
            }
            item.courseList = courseList
            item.poolId = pool.poolId
            item.poolName = pool.poolName
            interestCourseList.push(item)
          }
          this.SET_INTEREST_COURSE_LIST({ schemeId: params.schemeId, interestCourseList: interestCourseList })
          stateData = internalFunction.getUserInterestCourseItem(this, params.schemeId)
        } else {
          return Promise.reject(interestCourseResponse.status)
        }
      }
      // 加载进度信息
      if (stateData) {
        // 进度信息每次都刷新
        const courseIdList = stateData.interestCourseList.flatMap(p => p.courseList).map(x => x.id)
        const courseScheduleResponse = await PlatformUserCourseGateway.listUserCourseLearningSchedule({
          courseIdList: courseIdList,
          schemeId: params.schemeId,
          source: 'interestCourse'
        })
        if (courseScheduleResponse.status.isSuccess()) {
          const courseScheduleList: Array<UserCourseLearningScheduleDTO> = courseScheduleResponse.data
          const currentCourseScheduleList: Array<CourseSchedule> = courseScheduleList.map(x => {
            const courseSchedule: CourseSchedule = new CourseSchedule()
            courseSchedule.courseId = x.courseId
            courseSchedule.lastStudyTime = moment(x.lastStudyTime, Constants.DATE_PATTERN).toDate()
            courseSchedule.schedule = x.schedule
            courseSchedule.studyState = x.studyState
            return courseSchedule
          })
          this.SET_COURSE_SCHEDULE({
            learningId: params.schemeId,
            schemeId: params.schemeId,
            courseScheduleList: currentCourseScheduleList,
            source: 'interestCourse'
          })
        } else {
          return Promise.reject(courseScheduleResponse.status)
        }
      }
    })
  }

  /**
   * 初始化最近学习的课程
   * @param params
   */
  @Action
  @Role([RoleType.user])
  public async initLastCourseLearning(params: { schemeId: string; learningId: string }): Promise<ResponseStatus> {
    return internalFunction.execute(async () => {
      const stateData = internalFunction.getUserCourseItem(this, params.learningId)
      if (!stateData) {
        return
      }
      // region 加载最后学习课程
      const userLastCourseResponse = await PlatformUserCourseGateway.getUserLastCourseLearning({
        schemeId: params.schemeId,
        learningId: params.learningId
      })
      if (userLastCourseResponse.status.isSuccess()) {
        const lastLearningCourseDto = userLastCourseResponse.data
        if (lastLearningCourseDto) {
          const mycourse = stateData.myCourseList.find(x => x.id === lastLearningCourseDto.courseId)
          if (mycourse) {
            const lastLearnCourse: LastLearnCourse = new LastLearnCourse()
            lastLearnCourse.id = lastLearningCourseDto.courseId
            // 从我的课程中获取
            lastLearnCourse.name = mycourse.name
            lastLearnCourse.iconPath = mycourse.iconPath
            lastLearnCourse.poolId = mycourse.poolId
            lastLearnCourse.schedule = lastLearningCourseDto.schedule
            lastLearnCourse.teacherList = mycourse.teacherList
            lastLearnCourse.courseType = mycourse.courseType
            lastLearnCourse.lastStudyTime = mycourse.lastStudyTime
            this.SET_LAST_LEARN_COURSE({
              learningId: params.learningId,
              lastLearnCourse: lastLearnCourse
            })
          }
        }
      } else {
        return Promise.reject(userLastCourseResponse.status)
      }
    })
  }

  /**
   * 加载指定课程下的目录及课件信息
   * @param params 参数：schemeId - 学习方案编号； learningId - 学习方式编号； courseId - 课程编号
   */
  @Action
  @Role([RoleType.user])
  public async loadUserCoursewareList(params: {
    schemeId: string
    learningId: string
    courseId: string
  }): Promise<ResponseStatus> {
    const userCoursewareItem = internalFunction.getUserCoursewareItem(this, params.learningId, params.courseId)
    if (!userCoursewareItem) {
      const courseChapters: Array<CourseChapter> = new Array<CourseChapter>()
      const usercoursewareList: Array<UserCourseware> = new Array<UserCourseware>()
      const response = await PlatformCourseGateway.listCourseOutline(params.courseId)
      if (response.status.isSuccess()) {
        const courseOutlineDTO = response.data
        if (courseOutlineDTO.outlineDTOList?.length) {
          for (const outline of courseOutlineDTO.outlineDTOList) {
            const chapter: CourseChapter = new CourseChapter()
            chapter.id = outline.id
            chapter.name = outline.name
            chapter.parentId = outline.parentId
            courseChapters.push(chapter)
          }
        }
        if (courseOutlineDTO?.courseWareOutlineDTOList?.length) {
          for (const courseware of courseOutlineDTO.courseWareOutlineDTOList) {
            const userCourseware: UserCourseware = new UserCourseware()
            userCourseware.id = courseware.courseWareId
            userCourseware.name = courseware.name
            userCourseware.courseChapterId = courseware.outlineId
            userCourseware.timeLength = courseware.timeLength
            userCourseware.type = courseware.type
            usercoursewareList.push(userCourseware)
          }
        }
        const coursewareItem = {
          /**
           * 课程编号
           */
          courseId: params.courseId,
          /**
           * 学习的课件信息列表
           */
          userCoursewareList: usercoursewareList,
          /**
           * 课件目录
           */
          courseChapterList: courseChapters
        } as UserCoursewareItem
        this.SET_MY_COURSE_WARE_INFO({
          learningId: params.learningId,
          coursewareItem: coursewareItem
        })
      } else {
        return Promise.resolve(response.status)
      }
    }

    const coursewareScheduleResponse = await PlatformUserCourseGateway.listUserCoursewareLearningSchedule({
      courseIdList: [params.courseId],
      schemeId: params.schemeId,
      learningId: params.learningId
    })
    if (coursewareScheduleResponse.status.isSuccess()) {
      const coursewareScheduleList: Array<UserCoursewareLearningScheduleDTO> = coursewareScheduleResponse.data
      const currentCoursewareScheduleList: Array<CoursewareSchedule> = coursewareScheduleList.map(x => {
        const coursewareSchedule: CoursewareSchedule = new CoursewareSchedule()
        coursewareSchedule.courseId = x.courseId
        coursewareSchedule.coursewareId = x.courseWareId
        coursewareSchedule.lastStudyTime = moment(x.lastStudyTime, Constants.DATE_PATTERN).toDate()
        coursewareSchedule.schedule = x.schedule
        coursewareSchedule.studyState = x.studyState
        return coursewareSchedule
      })
      this.SET_COURSE_WARE_SCHEDULE({
        learningId: params.learningId,
        courseId: params.courseId,
        coursewareScheduleList: currentCoursewareScheduleList
      })
      return Promise.resolve(coursewareScheduleResponse.status)
    } else {
      return Promise.reject(coursewareScheduleResponse.status)
    }
  }

  /**
   * 指定选择课程
   * @param params 参数：userId - 用户编号；schemeId - 学习方案编号；learningId - 学习方式编号；poolId - 课程包编号；courseIdList - 课程编号集合
   */
  @Action
  @Role([RoleType.user])
  public doChooseCourse(params: {
    userId: string
    schemeId: string
    learningId: string
    poolId: string
    courseIdList: Array<string>
    ruleId: string | undefined
  }): Promise<ResponseStatus> {
    const batchChooseDto = new UserBatchChooseCourseRequest()
    batchChooseDto.userId = params.userId
    batchChooseDto.courseIds = params.courseIdList
    batchChooseDto.test = false
    batchChooseDto.courseLearning = {
      schemeId: params.schemeId,
      learningId: params.learningId,
      poolId: params.poolId ? params.poolId : undefined,
      ruleId: params.ruleId ? params.ruleId : undefined
    }
    batchChooseDto.sourceType = 'chooseCourseLearning'
    // 对接课程选课
    return internalFunction.execute(async () => {
      const response = await CourseLearningGateway.userBatchChooseCourse(batchChooseDto)
      if (response.status.isSuccess()) {
        this.SET_MY_COURSE_LIST_LOAD({ learningId: params.learningId, isLoadMyCourseList: false })
        this.SET_IS_LOAD_UNSELECTED_MAP_LIST({ learningId: params.learningId, isLoad: false })
        return await this.init({ schemeId: params.schemeId, learningId: params.learningId })
      } else {
        return Promise.reject(response.status)
      }
    })
  }

  /**
   * 指定选择课程
   * @param params
   * params.isInterestCourseChoose 是否兴趣课选课
   */
  @Action
  @Role([RoleType.user])
  public async doCrossBatchChooseCourse(params: {
    schemeId: string
    issueId: string
    learningId: string
    userId: string
    isInterestCourseChoose: boolean
    chooseInfoList: Array<BatchChooseCourseRequest>
  }): Promise<ResponseStatus> {
    let token: string
    const tokenParam = {
      userId: params.userId,
      schemeId: params.schemeId,
      issueId: params.issueId
    }
    if (params.isInterestCourseChoose) {
      const res = await PreExamLsGateway.applyChooseInterestCourseToken(tokenParam)
      if (res.status.isSuccess()) {
        token = res.data
      }
    } else {
      const res = await PreExamLsGateway.applyChooseCourseToken(tokenParam)
      if (res.status.isSuccess()) {
        token = res.data
      }
    }
    // 对接课程选课
    return internalFunction.execute(async () => {
      const response = await CourseLearningGateway.batchChooseCourse({
        token: token,
        chooseInfoList: params.chooseInfoList
      })
      if (response.status.isSuccess()) {
        this.SET_MY_COURSE_LIST_LOAD({
          learningId: params.learningId,
          isLoadMyCourseList: false
        })
        this.SET_IS_LOAD_UNSELECTED_MAP_LIST({
          learningId: params.learningId,
          isLoad: false
        })
        return await this.init({
          schemeId: params.schemeId,
          learningId: params.learningId
        })
      } else {
        return Promise.reject(response.status)
      }
    })
  }

  /**
   * 刷新某个学习方式用户课程数据
   * @param params
   */
  @Action
  public async doRefreshInitByLearningId(params: { schemeId: string; learningId: string }): Promise<ResponseStatus> {
    this.SET_MY_COURSE_LIST_LOAD({ learningId: params.learningId, isLoadMyCourseList: false })
    this.SET_IS_LOAD_UNSELECTED_MAP_LIST({ learningId: params.learningId, isLoad: false })
    return await this.init(params)
  }

  //endregion
  //region Mutations

  /**
   * 设置我的课程列表
   * @param params 课程列表
   */
  @Mutation
  private SET_MY_COURSE_LIST(params: { learningId: string; myCourseList: Array<UserCourse> }) {
    internalFunction.initWhenNotExists(this, params.learningId, userCourse => {
      userCourse.myCourseList = params.myCourseList
      userCourse.isLoadMyCourseList = true
    })
  }

  /**
   * 设置已选择的兴趣课程列表
   * @param params 课程列表
   */
  @Mutation
  private SET_INTEREST_COURSE_LIST(params: { schemeId: string; interestCourseList: Array<PoolMapUserCourse> }) {
    internalFunction.initInterestCourseWhenNotExists(this, params.schemeId, userCourse => {
      userCourse.interestCourseList = params.interestCourseList
      userCourse.isLoadInterestCourse = true
    })
  }

  /**
   * 设置已选择的兴趣课程包
   * @param params 课程列表
   */
  @Mutation
  private SET_INTEREST_COURSE_POOL_LIST(params: { schemeId: string; poolList: Array<CoursePool> }) {
    internalFunction.initInterestCourseWhenNotExists(this, params.schemeId, userCourse => {
      userCourse.interestCoursePoolList = params.poolList
      console.log('已选兴趣课程包：', params.poolList)
    })
  }

  /**
   * 设置未选择的兴趣课程列表
   * @param params 课程列表
   */
  @Mutation
  private SET_UN_SELECT_INTEREST_COURSE_LIST(params: { schemeId: string; interestCourseList: Array<PoolMapCourse> }) {
    internalFunction.initInterestCourseWhenNotExists(this, params.schemeId, userCourse => {
      userCourse.unSelectInterestCourseList = params.interestCourseList
      userCourse.isLoadUnSelectedInterestCourse = true
    })
  }

  /**
   * 设置未选择的兴趣课程包
   * @param params 课程列表
   */
  @Mutation
  private SET_UN_SELECT_INTEREST_COURSE_POOL_LIST(params: { schemeId: string; poolList: Array<CoursePool> }) {
    internalFunction.initInterestCourseWhenNotExists(this, params.schemeId, userCourse => {
      userCourse.unSelectInterestCoursePoolList = params.poolList
      console.log('未选兴趣课程包：', params.poolList)
    })
  }

  /**
   * 设置最后学习课程
   * @param params 最后学习的课程
   */
  @Mutation
  private SET_LAST_LEARN_COURSE(params: { learningId: string; lastLearnCourse: LastLearnCourse }) {
    internalFunction.initWhenNotExists(this, params.learningId, userCourse => {
      userCourse.lastLearnCourse = params.lastLearnCourse
    })
  }

  /**
   * 设置我的课程列表是否加载
   * @param params 参数
   */
  @Mutation
  private SET_MY_COURSE_LIST_LOAD(params: { learningId: string; isLoadMyCourseList: boolean }) {
    internalFunction.initWhenNotExists(this, params.learningId, userCourse => {
      userCourse.isLoadMyCourseList = params.isLoadMyCourseList
    })
  }

  /**
   * 设置课件进度信息
   * @param params 参数：learningId - 学习方式编号；courseId - 课程编号；coursewareScheduleList - 课件进度列表
   */
  @Mutation
  private SET_COURSE_WARE_SCHEDULE(params: {
    learningId: string
    courseId: string
    coursewareScheduleList: Array<CoursewareSchedule>
  }) {
    const userCoursewareItem = internalFunction.getUserCoursewareItem(this, params.learningId, params.courseId)
    if (userCoursewareItem?.userCoursewareList?.length) {
      for (const courseware of userCoursewareItem.userCoursewareList) {
        const coursewareSchedule = params.coursewareScheduleList.find(
          x => x.coursewareId === courseware.id && x.courseId === params.courseId
        )
        if (coursewareSchedule) {
          courseware.lastStudyTime = coursewareSchedule.lastStudyTime
          courseware.studyState = coursewareSchedule.studyState
          courseware.schedule = coursewareSchedule.schedule
        }
      }
    }
  }

  /**
   * 设置课程及课件进度
   * @param params 参数：learningId - 学习方式编号；courseScheduleList - 课程进度集合；coursewareScheduleList - 课件进度集合
   */
  @Mutation
  private SET_COURSE_SCHEDULE(params: {
    learningId: string
    schemeId: string
    courseScheduleList: Array<CourseSchedule>
    source: string
  }) {
    if (params.source === 'assessCourse') {
      internalFunction.initWhenNotExists(this, params.learningId, userCourse => {
        const courseList = userCourse.myCourseList
        for (const course of courseList) {
          const courseSchedule = params.courseScheduleList.find(x => x.courseId === course.id)
          if (courseSchedule) {
            course.lastStudyTime = courseSchedule.lastStudyTime
            course.schedule = courseSchedule.schedule
            course.studyState = courseSchedule.studyState
          }
        }
      })
    } else if (params.source === 'interestCourse') {
      internalFunction.initInterestCourseWhenNotExists(this, params.schemeId, userCourse => {
        const courseList = userCourse.interestCourseList
        for (const course of courseList.flatMap(p => p.courseList)) {
          const courseSchedule = params.courseScheduleList.find(x => x.courseId === course.id)
          if (courseSchedule) {
            course.lastStudyTime = courseSchedule.lastStudyTime
            course.schedule = courseSchedule.schedule
            course.studyState = courseSchedule.studyState
          }
        }
      })
    }
  }

  /**
   * 设置未选择的课程
   * @param params 参数：learningId - 学习方式编号；courseList - 未选择课程列表
   */
  @Mutation
  private SET_UNSELECTED_COURSE_LIST(params: { learningId: string; map: PoolMapCourse; append: boolean }) {
    internalFunction.initWhenNotExists(this, params.learningId, userCourse => {
      const item = internalFunction.getPoolMapCourse(userCourse, params.map.poolId)
      if (item) {
        item.totalPageSize = params.map.totalPageSize
        item.totalSize = params.map.totalSize
        if (params.append) {
          item.courseList.push(...params.map.courseList)
        } else {
          item.courseList = params.map.courseList
        }
      } else {
        userCourse.unSelectedCourseList.push(params.map)
      }
    })
  }

  @Mutation
  private SET_UNSELECTED_MAP_LIST(params: { learningId: string; mapList: Array<PoolMapCourse> }) {
    internalFunction.initWhenNotExists(this, params.learningId, userCourse => {
      userCourse.unSelectedCourseList = params.mapList
      userCourse.isLoadCoursePool = true
    })
  }

  @Mutation
  private SET_IS_LOAD_UNSELECTED_MAP_LIST(params: { learningId: string; isLoad: boolean }) {
    internalFunction.initWhenNotExists(this, params.learningId, userCourse => {
      userCourse.isLoadUnSelectedCourseList = params.isLoad
    })
  }

  /**
   * 设置课件信息
   * @param params 参数：learningId - 学习方式；coursewareItem - 课件信息
   */
  @Mutation
  private SET_MY_COURSE_WARE_INFO(params: { learningId: string; coursewareItem: UserCoursewareItem }) {
    internalFunction.initWhenNotExists(this, params.learningId, userCourse => {
      const currentCoursewareItem = userCourse.myCoursewareList.find(x => x.courseId === params.coursewareItem.courseId)
      if (currentCoursewareItem) {
        currentCoursewareItem.userCoursewareList = params.coursewareItem.userCoursewareList
        currentCoursewareItem.courseChapterList = params.coursewareItem.courseChapterList
      } else {
        if (!userCourse.myCoursewareList) {
          userCourse.myCoursewareList = new Array<UserCoursewareItem>()
        }
        userCourse.myCoursewareList.push(params.coursewareItem)
      }
    })
  }

  //endregion

  //region Getters

  /**
   * 指示获取学习方式下我的课程信息
   * @return Function 参数：learningId - 学习方式编号
   */
  get getSelectedCourseList() {
    return (learningId: string): Array<UserCourse> => {
      return internalFunction.getUserCourseItem(this, learningId)?.myCourseList || []
    }
  }

  /**
   * 指示获取学习方式下未学习、学习中的课程
   * @return Function 参数：learningId - 学习方式编号
   */
  get getStudyCourseList() {
    return (learningId: string): Array<UserCourse> => {
      return internalFunction.getUserCourseItem(this, learningId)?.myCourseList?.filter(p => p.studyState !== 2) || []
    }
  }

  /**
   * 指示获取学习方式下已学完的课程
   * @return Function 参数：learningId - 学习方式编号
   */
  get getStudyFinishCourseList() {
    return (learningId: string): Array<UserCourse> => {
      return internalFunction.getUserCourseItem(this, learningId)?.myCourseList?.filter(p => p.studyState === 2) || []
    }
  }

  /**
   * 获取我已选的课程总学时
   */
  get getSelectedTotalPeriod() {
    return (learningId: string): number => {
      const result = internalFunction.getUserCourseItem(this, learningId)?.myCourseList.map(x => x.period)
      if (result?.length) {
        return result.reduce((a, b) => a + b)
      }
      return 0
    }
  }

  /**
   * 获取是否存在已选课程
   */
  get hasSelectedCourseList() {
    return (learningId: string): boolean => {
      const courseItem = internalFunction.getUserCourseItem(this, learningId)
      return !!courseItem?.myCourseList?.length
    }
  }

  /**
   * 获取已选课程总数
   */
  get getSelectedCourseCount() {
    return (learningId: string): number => {
      const courseItem = internalFunction.getUserCourseItem(this, learningId)
      if (courseItem?.myCourseList) {
        return courseItem.myCourseList.length
      }
      return 0
    }
  }

  /**
   * 获取是否存在未选择的课程
   */
  get hasUnSelectedCourseList() {
    return (learningId: string): boolean => {
      const courseItem = internalFunction.getUserCourseItem(this, learningId)
      return !!courseItem?.unSelectedCourseList?.length
    }
  }

  /**
   * 获取最后一次学习课程信息
   * @see initLastCourseLearning
   * @return function 参数： learningId - 学习方式编号
   */
  get getLastLearnCourse() {
    return (learningId: string): LastLearnCourse | undefined => {
      const userCourse = internalFunction.getUserCourseItem(this, learningId)
      if (userCourse) {
        return userCourse.lastLearnCourse
      }
      return new LastLearnCourse()
    }
  }

  /**
   * 获取用户某个课程的信息
   * @see init
   * @return function 参数：learningId - 学习方式编号； courseId - 课程编号
   */
  get getUserOneCourse() {
    return (learningId: string, courseId: string, schemeId: string, isInterest?: boolean): UserCourse | undefined => {
      if (isInterest !== undefined && isInterest === true) {
        const userInterestCourse = internalFunction.getUserInterestCourseItem(this, schemeId)
        if (userInterestCourse) {
          for (const poolMapUserCourse of userInterestCourse.interestCourseList) {
            for (const userCourse of poolMapUserCourse.courseList) {
              if (userCourse.id === courseId) {
                return userCourse
              }
            }
          }
        }
      } else {
        const userCourse = internalFunction.getUserCourseItem(this, learningId)
        return userCourse?.myCourseList.find(x => x.id === courseId) || undefined
      }
      return undefined
    }
  }

  /**
   * 指示获取某课程的最后学习课件
   * @see loadUserCoursewareList
   */
  get getLastStudyCoursewareByCourseId() {
    return (learningId: string, courseId: string): UserCourseware | undefined => {
      const userCoursewareItem = internalFunction.getUserCoursewareItem(this, learningId, courseId)
      if (userCoursewareItem?.userCoursewareList?.length) {
        const coursewareList = userCoursewareItem.userCoursewareList.filter(x => x.studyState !== 0)
        if (coursewareList?.length) {
          return coursewareList.sort(
            (a: UserCourseware, b: UserCourseware) => b.lastStudyTime.getDate() - a.lastStudyTime.getDate()
          )[0]
        }
        return coursewareList[0]
      }
      return undefined
    }
  }

  /**
   * 获取某个课程下的目录及课件信息
   * @see loadUserCoursewareList
   */
  get getUserCoursewareItemInfo() {
    return (learningId: string, courseId: string): UserCoursewareItem | undefined => {
      return internalFunction.getUserCoursewareItem(this, learningId, courseId)
    }
  }

  /**
   * 获取某个课程某个章节下的用户课件信息
   * @see loadUserCoursewareList
   */
  get getUserCourseWare() {
    return (learningId: string, courseId: string, chapterId: string): Array<UserCourseware> => {
      const list = internalFunction.getUserCoursewareItem(this, learningId, courseId)
      return list?.userCoursewareList.filter(ware => ware.courseChapterId === chapterId)
    }
  }

  /**
   * 获取某个学习方式下的未选择的课程包列表
   * 需要调用获取课程包数据后才能获取
   * @see initCoursePool
   * @see LearningSchemeModule.loadSchemeDetailInfo
   */
  get getCoursePoolList() {
    return (learningId: string, schemeId: string): Array<{ poolId: string; poolName: string }> | undefined => {
      const schemeAllPoolWithSoft = LearningSchemeModule.getSchemeDetailInfo(schemeId)
      const unSelectList = internalFunction.getUserCourseItem(this, learningId)?.unSelectedCourseList
      const resultList = new Array<{ poolId: string; poolName: string }>()
      if (!schemeAllPoolWithSoft) {
        console.info('未调用LearningSchemeModule.loadSchemeDetailInfo初始化，将返回无序选修包列表')
        return unSelectList?.map(x => {
          return { poolId: x.poolId, poolName: x.poolName }
        })
      }
      schemeAllPoolWithSoft?.courseLearning.optionalPackages.forEach(pool => {
        const find = unSelectList?.find(un => un.poolId === pool.packageId)
        if (find) {
          resultList.push({ poolName: find.poolName, poolId: find.poolId })
        }
      })
      return resultList
    }
  }

  /**
   * 获取指定学习方式指定课程包下未选择的课程列表
   * @see initCoursePool
   * @see doLoadUnSelectedCourse
   * @Return PoolMapCourse.courseList为课程集合，PoolMapCourse.totalSize为分页数据
   */
  get getUnSelectedCourseList() {
    return (learningId: string, poolId: string): PoolMapCourse | undefined => {
      return internalFunction.getUserCourseItem(this, learningId)?.unSelectedCourseList.find(x => x.poolId === poolId)
    }
  }

  /**
   * 获取某个学习方式下的已选择的兴趣课程包列表【所有】
   * 需要调用获取课程包数据后才能获取
   * @see initCoursePool
   */
  get getInterestCoursePoolList() {
    return (schemeId: string): Array<CoursePool> => {
      const interestCoursePoolList = new Array<CoursePool>()
      const all = new CoursePool()
      all.poolId = '-1'
      all.poolName = '全部'
      interestCoursePoolList.push(all)
      const list = internalFunction.getUserInterestCourseItem(this, schemeId)?.interestCoursePoolList
      if (list) {
        interestCoursePoolList.push(...list)
      }
      return interestCoursePoolList
    }
  }

  /**
   * 获取某个学习方式下的已选择的兴趣课程包列表【学习中】
   * 需要调用获取课程包数据后才能获取
   * @see initCoursePool
   */
  get getInterestCoursePoolListStudying() {
    return (schemeId: string): Array<CoursePool> => {
      const interestCoursePoolList = new Array<CoursePool>()
      const all = new CoursePool()
      all.poolId = '-1'
      all.poolName = '全部'
      interestCoursePoolList.push(all)
      internalFunction.getUserInterestCourseItem(this, schemeId)?.interestCourseList.forEach(c => {
        if (c.courseList.find(course => course.studyState !== 2)) {
          const pool = new CoursePool()
          pool.poolId = c.poolId
          pool.poolName = c.poolName
          interestCoursePoolList.push(pool)
        }
      })
      return interestCoursePoolList
    }
  }

  /**
   * 获取某个学习方式下的已选择的兴趣课程包列表【已学完】
   * 需要调用获取课程包数据后才能获取
   * @see initCoursePool
   */
  get getInterestCoursePoolListStudyFinish() {
    return (schemeId: string): Array<CoursePool> => {
      const interestCoursePoolList = new Array<CoursePool>()
      const all = new CoursePool()
      all.poolId = '-1'
      all.poolName = '全部'
      interestCoursePoolList.push(all)
      internalFunction.getUserInterestCourseItem(this, schemeId)?.interestCourseList.forEach(c => {
        if (c.courseList.find(course => course.studyState === 2)) {
          const pool = new CoursePool()
          pool.poolId = c.poolId
          pool.poolName = c.poolName
          interestCoursePoolList.push(pool)
        }
      })
      return interestCoursePoolList
    }
  }

  /**
   * 获取指定学习方式指定课程包下未选择的兴趣课程包列表
   * @see initCoursePool
   * @see doLoadUnSelectedCourse
   */
  get getUnSelectedInterestCoursePoolList() {
    return (schemeId: string): Array<CoursePool> => {
      const interestCoursePoolList = new Array<CoursePool>()
      // const all = new CoursePool()
      // all.poolId = '-1'
      // all.poolName = '全部'
      // interestCoursePoolList.push(all)
      const list = internalFunction.getUserInterestCourseItem(this, schemeId)?.unSelectInterestCoursePoolList
      if (list) {
        interestCoursePoolList.push(...list)
      }
      return interestCoursePoolList
    }
  }

  /**
   * 获取某个学习方式下的已选择的兴趣课程列表【全部】
   * 需要调用获取课程包数据后才能获取
   * @see initCoursePool
   */
  get getInterestCourseList() {
    return (schemeId: string, poolId: string): Array<UserCourse> => {
      const temp = internalFunction.getUserInterestCourseItem(this, schemeId)
      if (!temp) {
        return []
      }
      const interestCourseList: Array<PoolMapUserCourse> = temp.interestCourseList
      if (!interestCourseList) {
        return []
      }
      if (poolId === '-1') {
        return interestCourseList
          .flatMap(p => p.courseList)
          .sort(function(a, b) {
            return b.lastStudyTime.getTime() - a.lastStudyTime.getTime()
          })
      } else {
        return (
          interestCourseList
            .find(p => p.poolId === poolId)
            ?.courseList.sort(function(a, b) {
              return b.lastStudyTime.getTime() - a.lastStudyTime.getTime()
            }) || []
        )
      }
    }
  }

  /**
   * 获取某个学习方式下的已选择的兴趣课程列表【学习中】
   * 需要调用获取课程包数据后才能获取
   * @see initCoursePool
   */
  get getInterestCourseListStudying() {
    return (schemeId: string, poolId: string): Array<UserCourse> => {
      const temp = internalFunction.getUserInterestCourseItem(this, schemeId)
      if (!temp) {
        return []
      }
      const interestCourseList: Array<PoolMapUserCourse> = temp.interestCourseList
      if (!interestCourseList) {
        return []
      }
      if (poolId === '-1') {
        return interestCourseList
          .flatMap(p => p.courseList)
          .filter(c => c.studyState !== 2)
          .sort(function(a, b) {
            return b.lastStudyTime.getTime() - a.lastStudyTime.getTime()
          })
      } else {
        return (
          interestCourseList
            .find(p => p.poolId === poolId)
            ?.courseList.filter(c => c.studyState !== 2)
            .sort(function(a, b) {
              return b.lastStudyTime.getTime() - a.lastStudyTime.getTime()
            }) || []
        )
      }
    }
  }

  /**
   * 获取某个学习方式下的已选择的兴趣课程列表【已学完】
   * 需要调用获取课程包数据后才能获取
   * @see initCoursePool
   */
  get getInterestCourseListStudyFinish() {
    return (schemeId: string, poolId: string): Array<UserCourse> => {
      const temp = internalFunction.getUserInterestCourseItem(this, schemeId)
      if (!temp) {
        return []
      }
      const interestCourseList: Array<PoolMapUserCourse> = temp.interestCourseList
      if (!interestCourseList) {
        return []
      }
      if (poolId === '-1') {
        return interestCourseList
          .flatMap(p => p.courseList)
          .filter(c => c.studyState === 2)
          .sort(function(a, b) {
            return b.lastStudyTime.getTime() - a.lastStudyTime.getTime()
          })
      } else {
        return (
          interestCourseList
            .find(p => p.poolId === poolId)
            ?.courseList.filter(c => c.studyState === 2)
            .sort(function(a, b) {
              return b.lastStudyTime.getTime() - a.lastStudyTime.getTime()
            }) || []
        )
      }
    }
  }

  /**
   * 获取指定学习方式指定课程包下未选择的兴趣课程列表
   * @see initCoursePool
   * @see doLoadUnSelectedCourse
   */
  get getUnSelectedInterestCourseList() {
    return (schemeId: string, poolId: string): Array<PoolCourse> => {
      if (poolId === '-1') {
        return (
          internalFunction
            .getUserInterestCourseItem(this, schemeId)
            ?.unSelectInterestCourseList.flatMap(p => p.courseList) || []
        )
      } else {
        return (
          internalFunction
            .getUserInterestCourseItem(this, schemeId)
            ?.unSelectInterestCourseList.find(p => p.poolId === poolId || poolId === '-1')?.courseList || []
        )
      }
    }
  }

  //endregion
}

export default getModule(UserCourseModule)
