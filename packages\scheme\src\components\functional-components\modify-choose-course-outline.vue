<template>
  <div class="pure" v-loading="uiConfig.loading.pageLoading">
    <div class="f-ptb20 fixed-btn">
      <el-button type="primary" icon="el-icon-plus" @click="addOutlineTreeNode">添加分类</el-button>
      <el-button type="primary" icon="el-icon-plus" @click="addCourse">添加课程</el-button>
    </div>
    <div class="pure" v-if="uiConfig.showTree">
      <el-card
        shadow="never"
        class="m-card is-header f-mb15"
        v-for="(item, index) in outlineInfo.childOutlines"
        :key="index"
      >
        <div class="m-tit is-small bg-gray is-border-bottom f-align-center">
          <!--点击修改后隐藏-->
          <span class="tit-txt" v-show="!selectedOutlineInfoList[index].isNameEditable">
            {{ item.name }}
          </span>
          <a
            class="f-link f-cb f-ml10 m-tooltip-icon"
            v-show="!selectedOutlineInfoList[index].isNameEditable"
            @click="editTopLevelOutlineName(item, index)"
          >
            <i class="el-icon-edit-outline f-f18"></i>
          </a>
          <!-- /点击修改后隐藏-->
          <!--点击修改后出现-->
          <el-input
            v-show="selectedOutlineInfoList[index].isNameEditable"
            v-model="currentOutlineValue"
            size="mini"
            clearable
            placeholder="请输入名称"
            class="ipt"
          />
          <a
            class="f-link f-cb f-ml10 m-tooltip-icon"
            v-show="selectedOutlineInfoList[index].isNameEditable"
            @click="confirmTopLevelOutlineName(item, index)"
          >
            <i class="el-icon-circle-check f-f18"></i>
          </a>
          <a
            class="f-link f-cb f-ml10 m-tooltip-icon"
            v-show="selectedOutlineInfoList[index].isNameEditable"
            @click="cancelEditTopLevelOutlineName(index)"
          >
            <i class="el-icon-circle-close f-f18"></i>
          </a>
          <!--/点击修改后出现-->
          <el-tag type="danger" effect="dark" size="mini" class="f-ml10" v-show="item.category">
            {{ item.category === 1 ? '必修' : '选修' }}
          </el-tag>
          <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
            <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
            <div slot="content">
              {{
                item.category
                  ? item.category == 1
                    ? '本分类的课程为必修课程，报名成功后直接推送给学员，无需选课。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置。'
                    : '此分类下的课程为选修课程，支持配置选课要求学时。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置。'
                  : '本分类的课程为兴趣课程，报名成功后直接推送给学员，无需选课。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置'
              }}
            </div>
          </el-tooltip>
          <span class="f-fb f-ml20 f-flex-sub f-tr">
            一共 <i class="f-cr">{{ item.courseTotal }}</i> 门， <i class="f-cr">{{ item.coursePeriodTotal }} </i>学时
          </span>
        </div>
        <el-row class="is-height" v-loading="selectedOutlineInfoList[index].contentLoading">
          <el-col :sm="8" :xl="6" class="is-border-right">
            <div class="f-p20">
              <el-tree
                :data="[item]"
                node-key="id"
                :expand-on-click-node="false"
                class="m-course-tree"
                @node-drop="handleDrop"
                draggable
                :allow-drag="allowDrag"
                :allow-drop="allowDrop"
                default-expand-all
                :props="{ label: 'name', children: 'childOutlines' }"
                @node-click="
                  (data) => {
                    return handleNodeClick(data, true, index)
                  }
                "
              >
                <div class="custom-tree-node" slot-scope="{ node, data }">
                  <div class="tit" v-show="!data.isEditing">{{ node.label }}</div>
                  <div class="op" v-show="!data.isEditing">
                    <el-button type="text" size="mini" @click.stop="() => editTreeNodeName(data)">
                      <i class="el-icon-edit-outline"></i>
                    </el-button>
                    <el-button type="text" size="mini" v-show="node.level > 1" @click.stop="() => removeTreeNode(node)">
                      <i class="el-icon-delete"></i>
                    </el-button>
                  </div>
                  <!--编辑中-->
                  <div class="tit" v-show="data.isEditing">
                    <el-input v-model="currentOutlineValue" size="mini" clearable placeholder="请输入" />
                  </div>
                  <div class="op" v-show="data.isEditing">
                    <el-button type="text" size="mini" @click.stop="() => confirmEditTreeNodeName(data)">
                      <i class="el-icon-circle-check"></i>
                    </el-button>
                    <el-button type="text" size="mini" @click.stop="() => cancelEditTreeNodeName(data)">
                      <i class="el-icon-circle-close"></i>
                    </el-button>
                  </div>
                </div>
              </el-tree>
            </div>
          </el-col>
          <el-col :sm="16" :xl="18">
            <div class="f-p20">
              <div class="f-flex f-align-center">
                <div class="m-tit is-mini">
                  <span class="tit-txt">{{ selectedOutlineInfoList[index].currentNode.name }}</span>
                </div>
                <div class="f-fb f-flex-sub">
                  （一共 <i class="f-cr">{{ selectedOutlineInfoList[index].currentNode.courseTotal }}</i> 门，
                  <i class="f-cr">{{ selectedOutlineInfoList[index].currentNode.coursePeriodTotal }}</i
                  >学时）
                </div>
                <div class="f-tr">
                  <el-button
                    type="text"
                    size="small"
                    v-show="isLeafNode(index) && selectedOutlineInfoList[index].currentNode.coursePackageId"
                    @click="editCoursePackage(selectedOutlineInfoList[index].currentNode)"
                  >
                    编辑课程包
                  </el-button>
                  <el-button
                    type="primary"
                    size="small"
                    plain
                    v-show="isLeafNode(index)"
                    @click="removeAllCourse(selectedOutlineInfoList[index].currentNode)"
                  >
                    一键移除课程
                  </el-button>
                  <el-button
                    type="primary"
                    size="small"
                    icon="el-icon-plus"
                    v-show="isLeafNode(index)"
                    :disabled="Boolean(selectedOutlineInfoList[index].currentNode.coursePackageId)"
                    @click="addCourse(selectedOutlineInfoList[index].currentNode)"
                  >
                    添加课程
                  </el-button>
                </div>
              </div>
              <el-table
                :ref="`courseTableRef_${index}`"
                stripe
                :data="selectedOutlineInfoList[index].courseList"
                v-loading="selectedOutlineInfoList[index].courseQuery.loading"
                max-height="400px"
                class="m-table f-mt15"
              >
                <el-table-column label="No." width="60" align="center">
                  <template slot-scope="scope">
                    <span
                      :data-index="scope.$index + 1"
                      v-observe-visibility="(isVisible, entry) => visibleCourseList(isVisible, entry, index)"
                      >{{ scope.$index + 1 }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column label="课程名称" min-width="240">
                  <template slot-scope="scope">{{ scope.row.name }}</template>
                </el-table-column>
                <el-table-column label="分类信息" min-width="200">
                  <template slot-scope="scope">{{ getCourseCategory(scope.row) }}</template>
                </el-table-column>
                <el-table-column label="所属课程包名称" min-width="200">
                  <template slot-scope="scope">{{ scope.row.coursePackageName }}</template>
                </el-table-column>
                <el-table-column label="课程学时数" width="120" align="center">
                  <template slot-scope="scope">{{ scope.row.period }}</template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
    <!--新建分类-->
    <el-drawer
      title="新建分类"
      :visible.sync="uiConfig.dialog.addOutlineVisible"
      size="600px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
      @close="cancelAddOutlineTreeNode"
    >
      <div class="drawer-bd">
        <el-form ref="form" label-width="auto" class="m-form f-mt20">
          <el-form-item label="上级分类：">
            <el-cascader
              clearable
              lazy
              :options="outlineCategoryOptions"
              :props="defaultProps"
              @change="onChangeCascader"
              v-model="createOutlineInfo.parentNodePath"
              placeholder="请选择上级分类"
            />
          </el-form-item>
          <el-form-item label="分类名称：">
            <el-input
              v-model="createOutlineInfo.name"
              clearable
              maxlength="15"
              show-word-limit
              placeholder="请输入分类名称，不能超过15字"
            />
          </el-form-item>
          <el-form-item class="m-btn-bar">
            <el-button @click="cancelAddOutlineTreeNode">取消</el-button>
            <el-button type="primary" @click="saveOutlineTreeNode">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
    <choose-course-package
      ref="chooseCoursePackageRef"
      :options="outlineInfo.childOutlines"
      v-model="uiConfig.dialog.chooseCoursePackageVisible"
      :path="selectedOutlinePath"
      @updateTreeNode="updateTreeNode"
    ></choose-course-package>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import { cloneDeep } from 'lodash'
  import {
    CreateSchemeUtils,
    SchemeCourseDetailInCoursePackage
  } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import Mockjs from 'mockjs'
  import { TreeNode } from 'element-ui/types/tree'
  import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'
  import ChooseCoursePackage from '@hbfe/jxjy-admin-scheme/src/components/functional-components/choose-course-package.vue'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'
  import UtilClass from '@hbfe/jxjy-admin-common/src/util'
  import { CreateOutlineUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateOutlineUtils'
  import JobCategoryQuery from '@api/service/management/basic-data/JobCategory/JobCategoryQuery'
  import { JobCategoryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
  import { Query, UiPage } from '@hbfe/common'
  import QueryCoursePackage from '@api/service/management/resource/course-package/query/QueryCoursePackage'
  import { bind, debounce } from 'lodash-decorators'
  import SchemeDetailUtils from '@hbfe/jxjy-admin-scheme/src/utils/SchemeDetailUtils'
  import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'
  import CourseStatisticVo from '@api/service/management/resource/course/query/vo/CourseStatisticVo'
  import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
  import { CourseLoadModeEnum } from '@api/service/management/train-class/mutation/Enum/CourseLoadMode'

  /**
   * 编辑课程信息
   */
  class EditOutlineInfo {
    // 是否可编辑
    isNameEditable: boolean
    // 是否加载右侧内容
    contentLoading: boolean
    // 当前分类课程信息
    currentNode: Classification
    // 课程分页
    coursePage: UiPage
    // 课程查询
    courseQuery: Query = new Query()
    // 课程列表
    courseList: CourseInCoursePackage[]

    constructor() {
      this.contentLoading = false
      this.currentNode = new Classification()
      this.coursePage = new UiPage()
      this.courseQuery = new Query()
      this.courseList = [] as CourseInCoursePackage[]
    }
  }

  /**
   * 大纲节点信息
   */
  class OutlineInfo {
    // 节点路径
    parentNodePath: Array<string>
    // 展示名称
    name: string
  }

  @Component({
    components: {
      ChooseCoursePackage
    }
  })
  export default class extends Vue {
    /**
     * 初始化模型：1-课程学习，2-兴趣课
     */
    @Prop({
      required: true,
      type: Number
    })
    initMode: number

    /**
     * 是否允许编辑 - 结合不同initMode展示不同提示信息
     */
    @Prop({
      required: true,
      type: Boolean
    })
    enableEdit: boolean

    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: TrainClassBaseModel }) schemeDetail!: TrainClassBaseModel

    /**
     * 大纲树 - 双向绑定
     */
    @PropSync('outlineTree', {
      type: Classification
    })
    outlineInfo!: Classification
    @Ref('chooseCoursePackageRef') chooseCoursePackageRef: any

    // 查询课程总控
    queryCourseM: QueryCourse = new QueryCourse()
    // 课程统计信息列表
    courseStatisticList: CourseStatisticVo[] = []

    // 查询课程包总控
    queryCoursePackageM: QueryCoursePackage = new QueryCoursePackage()
    // 创建大纲树节点信息
    createOutlineInfo: OutlineInfo = new OutlineInfo()
    // 选中的课程大纲信息列表
    selectedOutlineInfoList: Array<EditOutlineInfo> = new Array<EditOutlineInfo>()
    // 当前（正在编辑的）大纲节点名称
    currentOutlineValue = ''
    // 课程大纲树内选中的节点路径
    selectedOutlinePath: Array<string> = new Array<string>()

    // 课程分类选项 - 新增分类
    outlineCategoryOptions: Array<Classification> = new Array<Classification>()

    /**
     * 工种信息
     */
    JobQuery = new JobCategoryQuery()
    jobInfoOptions = new Array<JobCategoryResponse>()
    jobInfo = new JobCategoryResponse()
    // 级联选择器配置项
    defaultProps = {
      label: 'name',
      children: 'childOutlines',
      value: 'id',
      checkStrictly: true // 取消关联，可选任意级
    }
    /**
     * 是否为选修分类
     */
    isElectiveClassify = true
    /**
     *  ui相关的变量控制
     */
    uiConfig = {
      // 对话框是否展示
      dialog: {
        // 新建课程分类抽屉
        addOutlineVisible: false,
        // 选择课程包抽屉
        chooseCoursePackageVisible: false
      },
      loading: {
        // 页面加载
        pageLoading: false
      },
      // 展示大纲树
      showTree: false
    }
    /**
     * 查询全量工种信息
     */
    async getJobCategoryQuery() {
      this.jobInfoOptions = await this.JobQuery.getAllJobCategory()
    }
    onChangeJobCascader(val: object) {
      if (val) {
        this.createOutlineInfo.name = this.jobInfoOptions[this.jobInfoOptions.findIndex((e) => e.id == val[0])].name
      }
    }
    /**
     * 上级分类选择方法
     *
     */
    onChangeCascader(val: any) {
      if (val) {
        // console.log(val)
        // console.log(this.outlineCategoryOptions)
        const oneElective = this.outlineCategoryOptions[this.outlineCategoryOptions.findIndex((e) => e.id == val[0])]
        if (oneElective && val.length > 1 && oneElective.childOutlines) {
          console.log(oneElective.childOutlines.findIndex((e) => e.id == val[1]))
          this.isElectiveClassify =
            oneElective.category == 2 && oneElective.childOutlines.findIndex((e) => e.id == val[1]) !== -1
              ? false
              : true
        } else {
          this.isElectiveClassify = true
        }
      }
    }
    /**
     * 选中的课程节点是否是叶子节点
     */
    get isLeafNode() {
      return (index: number) => {
        const length = this.selectedOutlineInfoList[index].currentNode.childOutlines?.length || 0
        return length === 0 ? true : false
      }
    }

    /**
     * 培训方案id
     */
    get schemeId() {
      return this.schemeDetail?.id || ''
    }

    /**
     * 获取大纲节点课程统计数据
     * @param ele 课程大纲节点
     */
    getCourseStatistic(ele: Classification) {
      const result = new CourseStatisticVo()
      const leaves = this.outlineTreeFindAllLeaves([ele])?.filter((item) => item.coursePackageId)
      const outlineIdList = leaves?.map((item) => item.id)
      if (outlineIdList.length) {
        const filterList = this.courseStatisticList.filter((item) => outlineIdList.includes(item.outlineId))
        result.courseTotal = filterList.reduce((prev, cur) => {
          return CalculatorObj.add(cur.courseTotal, prev)
        }, 0)
        result.coursePeriodTotal = filterList.reduce((prev, cur) => {
          return CalculatorObj.add(cur.coursePeriodTotal, prev)
        }, 0)
      }
      return result
    }

    /**
     * 页面初始化
     */
    async created() {
      await this.refresh()
    }
    async refresh() {
      // 获取原来的大纲节点课程统计信息
      this.courseStatisticList = await this.queryCourseM.queryCourseStatisticByScheme(this.schemeId)
      console.log('modify-scheme', this.courseStatisticList)
      this.uiConfig.loading.pageLoading = true
      this.uiConfig.showTree = false
      this.isElectiveClassify = true
      // 主要是进行课程大纲树绑定ui变量
      await this.initConfiguration()
      // await this.getJobCategoryQuery()
      /** 主要是进行判断是否加载课程大纲节点下课程 */
      this.outlineInfo.childOutlines.forEach((item, index) => {
        this.selectedOutlineInfoList[index].currentNode = item
        this.selectedOutlineInfoList[index].contentLoading = true
      })
      // 如果顶级节点就是末级节点，加载课程
      await Promise.all(
        this.selectedOutlineInfoList?.map(async (el: EditOutlineInfo, index: number) => {
          if (this.isLeafNode(index)) {
            el.courseList = await this.queryCourseList(el)
          }
          el.contentLoading = false
        })
      )
      console.log('###init', this.outlineInfo.childOutlines, this.selectedOutlineInfoList)
      this.uiConfig.showTree = true
      this.uiConfig.loading.pageLoading = false
    }

    /**
     * 查询课程列表
     */
    async queryCourseList(target: EditOutlineInfo): Promise<CourseInCoursePackage[]> {
      const result = [] as CourseInCoursePackage[]
      /** 加载课程包下课程 */
      if (target.currentNode.courseLoadMode === CourseLoadModeEnum.BY_COURSE_PACKAGE_ID) {
        const coursePackageId = target.currentNode.coursePackageId
        // 如果没有课程包id，直接返回空数组
        if (!coursePackageId) return result
        const respList = await this.queryCoursePackageM.pageQueryCourseListInCoursePackage(
          target.coursePage,
          coursePackageId
        )
        respList.forEach((item) => {
          item.courseCategoryInfo = this.outlineTreeFindPath((node: Classification) => {
            return node.coursePackageId === coursePackageId
          }, 'name')
          result.push(item)
        })
      }
      /** 加载课程大纲节点下课程 */
      if (target.currentNode.courseLoadMode === CourseLoadModeEnum.BY_OUTLINE_ID) {
        const leaves = this.outlineTreeFindAllLeaves([target.currentNode])?.filter((item) => item.coursePackageId)
        const outlineIdList = leaves?.map((item) => item.id)
        if (outlineIdList.length) {
          const respList = await this.queryCourseM.queryCourseListInSchemeByOutline(
            target.coursePage,
            outlineIdList,
            this.schemeId
          )
          respList?.forEach((item) => {
            const coursePackageName = item.sourceCoursePackageName
            const opt = new CourseInCoursePackage()
            Object.assign(opt, item)
            opt.coursePackageName = coursePackageName
            opt.courseCategoryInfo = this.outlineTreeFindPath((node: Classification) => {
              return node.id === item.outlineId
            }, 'name')
            result.push(opt)
          })
        }
      }
      return result
    }

    /**
     * 滚动查询课程列表
     */
    async visibleCourseList(isVisible: boolean, entry: any, index: number) {
      const target = this.selectedOutlineInfoList[index]
      const scopeIndex = entry.target.dataset.index
      if (isVisible) {
        if (parseInt(scopeIndex) >= target.coursePage.totalSize) {
          // 最大值时不请求
          return
        }
        if (parseInt(scopeIndex) == target.courseList.length) {
          target.courseQuery.loading = true
          target.coursePage.pageNo++
          console.log(`list_${index}.coursePage.pageNo`, target.coursePage.pageNo)
          const list = await this.queryCourseList(target)
          target.courseList = target.courseList.concat(list)
          target.courseQuery.loading = false
        }
      }
    }

    /**
     * 查找所有叶子节点 - 课程大纲树通用
     */
    outlineTreeFindAllLeaves(tree: Array<Classification>) {
      return CreateSchemeUtils.treeFindAllLeaves<Classification>(tree, 'childOutlines')
    }

    /**
     * 初始化配置
     */
    async initConfiguration() {
      if (!this.outlineInfo.childOutlines.length) {
        if (this.initMode === 1) {
          // 初始化课程学习大纲树
          this.initCourseOutlineTree()
        } else {
          // 初始化兴趣课大纲树
          this.initInterestCourseTree()
        }
      } else {
        // 递归设置大纲分类名称
        CreateOutlineUtils.setOutlineNameRecursion(this.outlineInfo.childOutlines)
        // 获取包含课程包id的叶子节点集合
        const leaves = this.outlineTreeFindAllLeaves(this.outlineInfo.childOutlines)?.filter(
          (item) => item.coursePackageId
        )
        if (leaves.length) {
          leaves.forEach((item) => {
            const courseStatistic = this.courseStatisticList.find((ele) => ele.outlineId === item.id)
            if (!courseStatistic) return
            // 标识节点加载课程的模式
            item.courseLoadMode = CourseLoadModeEnum.BY_OUTLINE_ID
            item.courseTotal = courseStatistic.courseTotal
            item.coursePeriodTotal = courseStatistic.coursePeriodTotal
            // 自下而上递归更新整棵树的课程包统计信息
            this.refreshOutlineTree(item.parentId)
          })
        }
      }
      // 设置默认选中课程大纲节点列表
      this.initSelectedOutlineInfoList()
    }

    /**
     * 获取课程大纲分类选项 - 二级之后的子节点都不展示
     */
    getOutlineCategoryOptions() {
      const tree = cloneDeep(this.outlineInfo.childOutlines)
      tree?.map((el: Classification) => {
        if (el.childOutlines && el.childOutlines.length) {
          el.childOutlines.map((subEl: Classification) => {
            subEl.childOutlines = undefined
          })
        }
      })
      return tree
    }

    /**
     * 初始化课程分类
     */
    initCourseOutlineTree() {
      // 必修课
      const compulsoryChild = new Classification()
      compulsoryChild.id = Classification.classificationIdPre + '_' + Mockjs.Random.guid()
      compulsoryChild.name = '必修课'
      compulsoryChild.sort = 0
      compulsoryChild.parentId = undefined
      compulsoryChild.courseTotal = 0
      compulsoryChild.coursePeriodTotal = 0
      compulsoryChild.category = 1 // 1-必修，2-选修
      compulsoryChild.childOutlines = undefined
      // 选修课
      const electiveChild = new Classification()
      electiveChild.id = Classification.classificationIdPre + '_' + Mockjs.Random.guid()
      electiveChild.name = '选修课'
      electiveChild.sort = 1
      electiveChild.parentId = undefined
      electiveChild.courseTotal = 0
      electiveChild.coursePeriodTotal = 0
      electiveChild.category = 2 // 1-必修，2-选修
      electiveChild.childOutlines = undefined
      this.outlineInfo.childOutlines.push(compulsoryChild, electiveChild)
    }

    /**
     * 初始化兴趣课分类
     */
    initInterestCourseTree() {
      const interestChild = new Classification()
      interestChild.id = Classification.classificationIdPre + '_' + Mockjs.Random.guid()
      interestChild.name = '兴趣课程'
      interestChild.sort = 1
      interestChild.parentId = undefined
      interestChild.courseTotal = 0
      interestChild.coursePeriodTotal = 0
      // interestChild.category = 0 // 兴趣课不纳入考核、不配置
      interestChild.childOutlines = undefined
      this.outlineInfo.childOutlines.push(interestChild)
    }

    /**
     * 初始化选中课程大纲信息列表
     */
    initSelectedOutlineInfoList() {
      this.selectedOutlineInfoList = new Array<EditOutlineInfo>()
      this.outlineInfo.childOutlines.forEach((el) => {
        // console.log('topLevelOutlines', el)
        const option = new EditOutlineInfo()
        option.isNameEditable = false
        option.contentLoading = false
        option.currentNode = new Classification()
        option.currentNode = el
        this.selectedOutlineInfoList.push(option)
      })
    }

    /**
     * 查询符合条件的节点 - 课程大纲树通用
     */
    outlineTreeFind(tree: Array<Classification>, func: any) {
      return CreateSchemeUtils.treeFind<Classification>(tree, func, 'childOutlines')
    }

    /**
     * 查找节点路径 - 课程大纲树通用
     */
    outlineTreeFindPath(func: any, key: string) {
      return CreateSchemeUtils.treeFindPath<Classification>(this.outlineInfo.childOutlines, func, key, 'childOutlines')
    }

    /**
     * 是否可操作 - 未勾选课程学习不可操作
     */
    canOperate() {
      let result = true
      if (!this.enableEdit) {
        result = false
        const errMessage =
          this.initMode === 1 ? '请先勾选学习内容模块，再配置对应内容。' : '请先勾选“兴趣课程”，再配置课程包！'
        this.$message.error(errMessage)
      }
      return result
    }

    /**
     * 是否可编辑节点展示名称 - 课程大纲树
     */
    canEditNodeName() {
      let result = true
      const editingNameNode = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
        return node.isEditing === true
      })
      const editingNameItem = this.selectedOutlineInfoList.find((el) => el.isNameEditable === true)
      if (editingNameItem || editingNameNode) {
        this.$message.error('请先保存后再操作！')
        result = false
      }
      return result
    }

    /**
     * 添加课程
     */
    async addCourse(item?: Classification) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      this.selectedOutlinePath = new Array<string>()
      if (item) {
        this.selectedOutlinePath = this.outlineTreeFindPath((node: Classification) => {
          return node.id === item.id
        }, 'id')
      }
      // 还原选择课程包组件
      await this.chooseCoursePackageRef.reset()
      this.uiConfig.dialog.chooseCoursePackageVisible = true
    }

    /**
     * 编辑课程包
     */
    editCoursePackage(item: Classification) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      const targetUrl = `/training/course-package/modify/${item.coursePackageId}`
      UtilClass.openUrl(targetUrl)
    }

    /**
     * 一键移除课程
     */
    removeAllCourse(item: Classification) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      if (!item.coursePackageId) {
        this.$alert('当前分类下无课程， 移除失败', '提示', {
          confirmButtonText: '确认',
          type: 'warning',
          callback: (action) => {
            console.log(action)
          }
        })
      } else {
        this.$confirm('一键移除后需重新添加课程，确认要移除？', '提示', {
          confirmButtonText: '确认',
          type: 'warning',
          callback: async (action) => {
            console.log(action)
            // 'confirm'-确认，'cancel'-取消
            if (action === 'confirm') {
              // 重置节点信息
              item.coursePackageId = ''
              item.courseLoadMode = CourseLoadModeEnum.BY_COURSE_PACKAGE_ID
              item.courseList = new Array<SchemeCourseDetailInCoursePackage>()
              item.courseTotal = 0
              item.coursePeriodTotal = 0
              // 更新大纲树
              this.refreshOutlineTree(item.parentId)
              this.$emit('updateLearningRequire')
              // ui页面重新展示
              const index = item.category === 2 ? 1 : 0
              await this.handleNodeClick(item, false, index)
              this.$message.success('操作成功')
            }
          }
        })
      }
    }

    /**
     * 更新节点信息 - 课程相关
     * @param {string} nodeId - 末级节点Id
     * @param {string} coursePackageId - 课程包id
     * @param {number} type - 类型 区分是添加课程包还是做回显
     */
    async updateTreeNode(nodeId: string, coursePackageId: string, type?: string) {
      const targetNode = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
        return node.id === nodeId
      })
      targetNode.coursePackageId = coursePackageId
      if (SchemeDetailUtils.isLeaf(targetNode)) {
        // 是叶子节点，查询课程包信息并存储
        const coursePackageInfo = await this.queryCoursePackageM.queryCoursePackageById(coursePackageId)
        targetNode.courseTotal = coursePackageInfo.courseCount || 0
        targetNode.coursePeriodTotal = coursePackageInfo.totalPeriod || 0
      }
      // 递归更新
      this.refreshOutlineTree(targetNode.parentId)
      this.$emit('updateLearningRequire')
      if (!type) {
        const index = targetNode.category === 2 ? 1 : 0
        await this.handleNodeClick(targetNode, false, index)
      }
    }

    /**
     * 更新属性信息 - 末级课程列表变化触发
     * @param {string} parentId - 父节点id
     */
    refreshOutlineTree(parentId: string) {
      const parentOutlineNode = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
        return node.id === parentId
      })
      if (!parentOutlineNode) return
      // 更新只更新课程数、课程总学时数
      parentOutlineNode.courseTotal =
        parentOutlineNode.childOutlines.reduce((prev, cur) => {
          return CalculatorObj.add(cur.courseTotal || 0, prev)
        }, 0) || 0
      parentOutlineNode.coursePeriodTotal =
        parentOutlineNode.childOutlines.reduce((prev, cur) => {
          return CalculatorObj.add(cur.coursePeriodTotal || 0, prev)
        }, 0) || 0
      this.refreshOutlineTree(parentOutlineNode.parentId)
    }

    /**
     * 获取课程分类信息
     */
    getCourseCategory(row: CourseInCoursePackage) {
      return row.courseCategoryInfo?.join('>') || ''
    }

    /**
     * 添加分类
     */
    addOutlineTreeNode() {
      const canOperate = this.canOperate()
      if (!canOperate) return
      this.createOutlineInfo = new OutlineInfo()
      this.outlineCategoryOptions = this.getOutlineCategoryOptions()
      this.jobInfo = new JobCategoryResponse()
      this.uiConfig.dialog.addOutlineVisible = true
    }

    /**
     * 取消添加分类 - 创建分类弹窗
     */
    cancelAddOutlineTreeNode() {
      this.uiConfig.dialog.addOutlineVisible = false
      this.isElectiveClassify = true
    }

    /**
     * 保存课程大纲节点 - 创建分类弹窗
     */
    saveOutlineTreeNode() {
      const nodeName = cloneDeep(this.createOutlineInfo.name)
      const parentNodePath = cloneDeep(this.createOutlineInfo.parentNodePath)
      // 节点名称不允许为空
      if (!nodeName) {
        this.$message.error('请输入分类名称')
        return
      }
      if (!parentNodePath || !parentNodePath.length) {
        this.$message.error('请选择上级分类')
        return
      }
      // 节点层级不超过三级
      if (parentNodePath.length >= 3) {
        this.$message.error('最多设置3级分类！')
        return
      }
      const parentNodeId = parentNodePath[parentNodePath.length - 1]
      const parentNode = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
        return node.id === parentNodeId
      })
      if (parentNode.coursePackageId) {
        this.$message.error('该上级分类已添加课程，需要移除课程后才能添加分类')
        return
      }
      // 同级节点不允许同名
      let validResult = true
      if (parentNode.childOutlines?.length >= 0) {
        const childOutlines = parentNode.childOutlines
        childOutlines.map((el) => {
          if (el.name === nodeName) {
            validResult = false
          }
        })
      }
      if (!validResult) {
        this.$message.error('同级分类不允许重名，请调整分类名称')
        return
      }
      const option = new Classification()
      option.parentId = parentNode.id
      option.id = Classification.classificationIdPre + '_' + Mockjs.Random.guid()
      option.category = parentNode.category
      option.name = nodeName
      option.courseTotal = 0
      option.coursePeriodTotal = 0
      option.courseLoadMode = CourseLoadModeEnum.BY_COURSE_PACKAGE_ID
      option.childOutlines = undefined
      if (!parentNode.childOutlines) {
        parentNode.childOutlines = new Array<Classification>()
      }
      parentNode.childOutlines.push(option)
      parentNode.childOutlines.map((el, index) => {
        el.sort = index
      })
      this.uiConfig.dialog.addOutlineVisible = false
      this.isElectiveClassify = true
      if (parentNodePath.length == 1 && parentNode.category == 2) {
        this.$emit('towElectiveRequire', option)
      }
    }

    /**
     * 编辑 - 一级课程大纲展示名称
     */
    editTopLevelOutlineName(node: Classification, index: number) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      const canEditNodeName = this.canEditNodeName()
      if (!canEditNodeName) return
      this.currentOutlineValue = cloneDeep(node.name)
      this.selectedOutlineInfoList[index].isNameEditable = true
    }

    /**
     * 确认编辑 - 一级课程大纲展示名称
     */
    confirmTopLevelOutlineName(node: Classification, index: number) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      if (!this.currentOutlineValue) {
        this.$message.error('分类名称不能为空！')
        return
      }
      const isRepeat = CreateOutlineUtils.validNodeNameIsRepeatInTheSameLevel(
        this.outlineInfo,
        node,
        this.currentOutlineValue
      )
      if (isRepeat) {
        this.$message.error('同级分类不允许重名，请调整分类名称')
        return
      } else {
        node.name = cloneDeep(this.currentOutlineValue)
        this.selectedOutlineInfoList[index].isNameEditable = false
      }
    }

    /**
     * 取消编辑 - 一级课程大纲展示名称
     */
    cancelEditTopLevelOutlineName(index: number) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      this.selectedOutlineInfoList[index].isNameEditable = false
    }

    // 分割线：以下都是elTree内操作

    /**
     * 课程大纲节点点击响应事件
     * @param cancelLoad 强制刷新
     */
    @debounce(200)
    @bind
    async handleNodeClick(data: Classification, cancelLoad = true, index: number) {
      // 判断前后节点是否重复，重复则不变化
      const sourceId = this.selectedOutlineInfoList[index].currentNode.id
      const targetId = data.id
      if (cancelLoad && sourceId === targetId) return
      const target = this.selectedOutlineInfoList[index]
      // 开始加载
      target.contentLoading = true
      target.currentNode = new Classification()
      target.currentNode = data
      target.coursePage = new UiPage()
      if (this.isLeafNode(index)) {
        // 叶子节点：重新请求列表
        target.courseList = await this.queryCourseList(target)
      } else {
        // 非叶子节点：清空课程列表
        target.courseList = [] as CourseInCoursePackage[]
      }
      // 切换节点后，表格右侧滚动条自动滚到顶部
      this.$nextTick(() => {
        const ele = this.$refs[`courseTableRef_${index}`][0] as any
        const bodyWrapper = ele.bodyWrapper as any
        if (bodyWrapper) bodyWrapper.scrollTop = 0
      })
      target.contentLoading = false
    }

    /**
     * 响应节点拖拽事件
     */
    handleDrop(
      draggingNode: TreeNode<any, Classification>,
      dropNode: TreeNode<any, Classification>,
      dropType: 'prev' | 'inner' | 'next',
      evt: any
    ) {
      console.log(JSON.stringify(dropType, evt))
      const temp = draggingNode.data.sort
      draggingNode.data.sort = dropNode.data.sort
      dropNode.data.sort = temp
    }

    /**
     * 节点结束拖拽触发事件
     */
    allowDrop(draggingNode: TreeNode<any, any>, dropNode: TreeNode<any, any>, type: 'prev' | 'inner' | 'next') {
      if (draggingNode.data.level === dropNode.data.level) {
        if (draggingNode.data.parentId === dropNode.data.parentId) {
          return type === 'prev' || type === 'next'
        } else {
          return false
        }
      } else {
        return false
      }
    }

    /**
     * 节点开始拖拽触发事件
     */
    allowDrag(draggingNode: TreeNode<any, any>) {
      const canOperate = this.canOperate()
      if (!canOperate) return false
      const canEditNodeName = this.canEditNodeName()
      if (!canEditNodeName) return false
      return draggingNode.level !== 1
    }

    /**
     * 编辑节点展示名称
     */
    editTreeNodeName(data: Classification) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      const canEditNodeName = this.canEditNodeName()
      if (!canEditNodeName) return
      data.isEditing = true
      this.currentOutlineValue = cloneDeep(data.name)
    }

    /**
     * 确认编辑节点展示名称
     */
    confirmEditTreeNodeName(data: Classification) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      if (!this.currentOutlineValue) {
        this.$message.error('分类名称不能为空！')
        return
      }
      const isRepeat = CreateOutlineUtils.validNodeNameIsRepeatInTheSameLevel(
        this.outlineInfo,
        data,
        this.currentOutlineValue
      )
      if (isRepeat) {
        this.$message.error('同级分类不允许重名，请调整分类名称')
        return
      } else {
        data.name = cloneDeep(this.currentOutlineValue)
        data.isEditing = false
      }
    }

    /**
     * 取消编辑节点展示名称
     */
    cancelEditTreeNodeName(data: Classification) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      data.isEditing = false
    }

    /**
     * 移除节点
     */
    removeTreeNode(treeNode: TreeNode<any, Classification>) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      const canEditNodeName = this.canEditNodeName()
      if (!canEditNodeName) return
      const targetTree = [treeNode.data]
      const hasCourseNode = this.outlineTreeFind(targetTree, (node: Classification) => {
        return node.coursePackageId
      })
      // const parentNode = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
      //   return node.id === targetTree[0].parentId
      // })
      const parentNode =
        this.outlineInfo.childOutlines[this.outlineInfo.childOutlines.findIndex((e) => e.id == targetTree[0].parentId)]
      if (hasCourseNode) {
        this.$alert('本分类下已经添加课程，无法删除。请先移除课程后再删除分类。', '提示', {
          confirmButtonText: '确认',
          type: 'warning',
          callback: (action) => {
            console.log(action)
          }
        })
        return
      }
      this.$confirm('删除后，需要重新添加分类。确定要删除分类？', '提示', {
        confirmButtonText: '确认',
        type: 'warning',
        callback: (action) => {
          // 'confirm'-确认，'cancel'-取消
          if (action === 'confirm') {
            // 找到父级
            const parentOutline = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
              return node.id === treeNode.data.parentId
            })
            // 删除对应子节点
            const deleteSortIndex = treeNode.data.sort
            parentOutline.childOutlines.splice(deleteSortIndex, 1)
            if (parentOutline.childOutlines.length) {
              // 调整同级排序序号
              parentOutline.childOutlines?.map((el, index) => {
                el.sort = index
              })
            } else {
              parentOutline.childOutlines = undefined
            }
            //选修课删除二级分类
            if (parentNode && parentNode.category == 2) {
              //
              this.$emit('deleteTowElectiveRequire', targetTree[0])
            }
          }
        }
      })
    }
    // 分割线：以上都是elTree内操作

    /**
     * 【选课规则】判断（课程分类）是否可保存
     * @return {boolean} 是否可保存
     */
    validateEnableSave(): boolean {
      let result = false
      const nodeList = this.outlineInfo.childOutlines
      const leafNodeList =
        this.outlineTreeFindAllLeaves(nodeList).filter((el: Classification) => {
          return !el.coursePackageId
        }) || ([] as Classification[])
      // 没有叶子节点说明节点都挂载了课程
      if (!CreateSchemeUtils.isWeightyArray(leafNodeList)) {
        result = true
      }
      const length = leafNodeList.length
      // 仅仅必修或仅选修允许保存
      if (length == 1) {
        const compulsoryNode: Classification = nodeList?.find((item) => item.category == 1)
        const electiveNode: Classification = nodeList?.find((item) => item.category == 2)
        if (leafNodeList[0].id === compulsoryNode.id || leafNodeList[0].id === electiveNode.id) {
          result = true
        }
      }
      return result
    }

    /**
     * 表单校验
     */
    validateForm() {
      let finalValid = true
      try {
        const enableSave = this.validateEnableSave()
        if (!enableSave) {
          this.$message.error('末级分类下课程不能为空，请调整')
          throw new Error('中断函数执行')
        }
      } catch (e) {
        finalValid = false
      }
      return finalValid
    }

    /**
     * 重置课程学习大纲
     */
    resetClassification() {
      this.uiConfig.loading.pageLoading = true
      this.uiConfig.showTree = false
      this.$nextTick(() => {
        if (this.initMode === 1) {
          // 课程学习
          this.initCourseOutlineTree()
        } else {
          // 兴趣课
          this.initInterestCourseTree()
        }
        this.initSelectedOutlineInfoList()
      })
      this.uiConfig.showTree = true
      this.uiConfig.loading.pageLoading = false
    }
  }
</script>

<style scoped lang="scss">
  .pure {
    margin: 0;
    padding: 0;
  }
</style>
<style>
  .job-select-height {
    height: 500px !important;
  }
  .job-select-height .el-cascader-panel {
    height: 100% !important;
  }
  .job-select-height .el-cascader-panel .el-cascader-menu__wrap {
    height: 104% !important;
  }
</style>
