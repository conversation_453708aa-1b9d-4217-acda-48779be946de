<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: Lin yt
 * @Date: 2022-02-15 16:01:36
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-06-24 19:46:56
-->
<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: Lin yt
 * @Date: 2022-02-15 16:01:36
 * @LastEditors: Lin yt
 * @LastEditTime: 2022-06-08 17:09:57
-->
<route-meta>{"title": "角色详情"}</route-meta>
<route-params content="/:id"></route-params>

<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="goBack">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/basic-data/account/role' }">运营帐号管理</el-breadcrumb-item>
      <el-breadcrumb-item>查看功能权限</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-row :gutter="15" class="is-height">
        <el-col :md="11" :xl="10">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header">
              <span class="tit-txt">角色信息</span>
            </div>
            <div class="f-p30">
              <el-skeleton animated :rows="3" v-if="!UpdateRole.id" />
              <el-descriptions :column="1" :colon="true" v-if="UpdateRole.id" :label-style="{ fontWeight: 'bold' }">
                <el-descriptions-item label="角色名称" label-class-name="my-label" content-class-name="my-content">
                  {{ UpdateRole.name }}
                </el-descriptions-item>
                <el-descriptions-item label="角色说明">
                  {{ UpdateRole.description }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="m-tit is-border-bottom">
              <span class="tit-txt">关联的管理员帐号</span>
            </div>
            <div class="f-p20">
              <used-admin-account :role="UpdateRole"></used-admin-account>
            </div>
          </el-card>
        </el-col>
        <el-col :md="13" :xl="14">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header">
              <span class="tit-txt">角色权限</span>
            </div>
            <el-tree
              :data="setDialbedPermissionList"
              show-checkbox
              :default-expand-all="false"
              node-key="id"
              ref="tree"
              :disabled="true"
              highlight-current
              :props="defaultProps"
              ><!-- :default-checked-keys="selectIds"-->
            </el-tree>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'

  import { cloneDeep } from 'lodash'
  import RoleInfoResponseVo from '@api/service/management/authority/role/query/vo/RoleInfoResponseVo'
  import { SecurityObjectGroupDto } from '@api/service/management/authority/role/dto/SecurityObjectGroupDto'
  import SecurityFactory from '@api/service/management/authority/security/SecurityFactory'
  import { SecurityGroupTree } from '@api/service/management/authority/security/query/vo/SecurityGroupTree'
  import UpdateRole from '@api/service/management/authority/role/UpdateRole'
  import UsedAdminAccount from '@hbfe/jxjy-admin-account/src/role/__components__/used-admin-account.vue'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  @Component({
    components: { UsedAdminAccount }
  })
  export default class extends Vue {
    @Ref('tree')
    tree: any
    roleDetail: RoleInfoResponseVo = new RoleInfoResponseVo()
    roleId = ''
    detailSecurityObjectGroupDtoList = new Array<SecurityObjectGroupDto>()
    /**
     * el-tree取值规则
     */
    defaultProps = {
      children: 'children',
      label: 'name'
    }

    getQueryAllPermission = new SecurityFactory()
    permissionList = new Array<SecurityGroupTree>()
    rolePermission = new Array<SecurityGroupTree>()
    UpdateRole: UpdateRole = new UpdateRole()
    async created() {
      this.roleId = this.$route.params.id
      this.UpdateRole = new UpdateRole(this.roleId)
      await this.UpdateRole.query()
      // 获取全部安全对象
      if ( this.UpdateRole.category === CategoryEnums.ztgly) {
        const specialAdminRoleId = ConfigCenterModule.getFrontendApplication(frontendApplication.specialAdminRoleId)
        this.permissionList = await this.getQueryAllPermission.querySecurity.getSecurityGroupByRoleId(
          [specialAdminRoleId],
          this.UpdateRole.category
        )
      } else {
        this.permissionList = await this.getQueryAllPermission.querySecurity.getCurrentUserSecurityGroupByCategory(
          this.UpdateRole.category
        )
      }
      // 获取当前角色安全对象
      const res = await this.getQueryAllPermission.querySecurity.getSpecificRoleSecurityGroup(
        this.roleId,
        this.UpdateRole.category
      )
      this.rolePermission = res
      this.recursionMethodForSecurityGroupIds(this.rolePermission)
    }

    /**
     *
    循环所有子节点，温馨提示，tree的方法只有setChecked可以用，其他（setCheckedNodes、setCheckedKeys、setCurrentKey、setCurrentNode）都有bug
    el-tree组件文档：https://element.eleme.cn/#/zh-CN/component/tree
    设置节点选中
     */
    recursionMethodForSecurityGroupIds(SecurityGroupTree: Array<SecurityGroupTree>) {
      SecurityGroupTree.forEach((group: SecurityGroupTree) => {
        if (group.children.length === 0) {
          return this.tree.setChecked(group.id, true, true)
        } else {
          this.recursionMethodForSecurityGroupIds(group.children)
        }
      })
    }
    get setDialbedPermissionList() {
      let list = new Array<SecurityGroupTree>()
      if (this.permissionList) {
        list = this.permissionList
      }
      return this.setDialbed(cloneDeep(list))
    }
    handleChange() {
      //todo
    }

    /**设置灰显
     * disabled=true
     */
    setDialbed(list: any) {
      list.forEach((group: any) => {
        group.disabled = true
        if (group.children.length !== 0) this.setDialbed(group.children)
      })
      return list
    }
    // 返回上一级页面
    goBack() {
      this.$router.push('/basic-data/account/role')
    }
  }
</script>
