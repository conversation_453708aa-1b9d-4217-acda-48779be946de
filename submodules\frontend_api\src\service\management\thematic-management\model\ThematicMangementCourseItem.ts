import { TrainingChannelSelectCourseResponse } from '@api/platform-gateway/platform-training-channel-back-gateway'

export default class ThematicMangementCourseItem {
  /**
   * 专题课程id
   */
  id = ''
  /**
   * 课程id
   */
  courseId = ''
  /**
   * 课程名称
   * @type {string}
   */
  name = ''
  /**
   * 排序
   * @type {number}
   */
  sort = 0
  /**
   * 创建时间
   * @type {string}
   */
  createTime = ''
  /**
   * 更新时间
   * @type {string}
   */
  updateTime = ''
  /**
   * 分类名称
   * @type {string}
   */
  categoriesName = ''
  /**
   * 学时
   */
  period = 0
  /**
   * 是否当期专题已选中
   */
  selected = false
  static from(dto: TrainingChannelSelectCourseResponse) {
    const vo = new ThematicMangementCourseItem()
    vo.id = dto.id
    vo.courseId = dto.courseId
    vo.name = dto.courseName
    vo.sort = dto.sort
    vo.categoriesName = dto.categoryName
    vo.createTime = dto.createdTime
    vo.updateTime = dto.updatedTime
    return vo
  }
}
