<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/basic-data/platform/function' }">培训监管管理</el-breadcrumb-item>
      <!-- TODO 调整面包屑动态名称 -->
      <el-breadcrumb-item>编辑网校级监管规则</el-breadcrumb-item>
    </el-breadcrumb>
    <!-- 学习监管规则 -->
    <learning-supervision
      ref="learningSupervision"
      :is-open-face-verify="baseConfig.baseConfig.datumConfig.enable"
      :antiCheatConfigProp.sync="antiCheatConfig"
    />
    <div class="m-btn-bar f-tc is-sticky-1">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" :loading="isLoading" @click="saveAntiConfig">保存</el-button>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import LearningSupervision from '@hbfe/jxjy-admin-platform/src/function/supervision/components/learning-supervision.vue'
  import AntiCheatConfig from '@hbfe-biz/biz-anticheat/dist/config/AntiCheatConfig'
  import { RangeTypeEnum } from '@hbfe-biz/biz-anticheat/dist/config/enums/VerifyConfigModeEnum'
  import Page from '@hbfe/jxjy-admin-common/src/models/Page'
  import { EnterVerifyMode } from '@hbfe-biz/biz-anticheat/src/config/enums/VerifyConfigModeEnum'
  import BaseConfig from '@hbfe-biz/biz-anticheat/dist/config/BaseConfig'

  @Component({
    components: { LearningSupervision }
  })
  export default class extends Vue {
    antiCheatConfig = new AntiCheatConfig()

    @Ref('learningSupervision')
    learningSupervision: LearningSupervision
    /**
     * 加载中
     */
    isLoading = false

    /**
     * 查询监管配置page
     */

    newPage = new Page()

    /**
     * 基础信息配置
     */
    baseConfig = new BaseConfig()

    // 保存、修改基础配置(校验模块)
    async saveAntiConfig() {
      this.antiCheatConfig.antiCheatConfig.range = RangeTypeEnum.PLATFORM
      this.learningSupervision.learningSupervisionForm
        .validate()
        .then(() => {
          this.savePort()
        })
        .catch((e) => {
          console.error(e)
        })
    }

    // 保存接口
    async savePort() {
      this.isLoading = true
      if (this.antiCheatConfig.antiCheatConfig.studyConfig.enterLearnVerify) {
        this.antiCheatConfig.antiCheatConfig.studyConfig.enterVerifyMode = EnterVerifyMode.EVERYONE
      }
      // 判断是否存在配置id，存在走修改
      if (this.$route.params.id && this.$route.params.id !== ':id') {
        try {
          this.antiCheatConfig.antiCheatConfig.studyConfig.enable = true
          await this.antiCheatConfig.updateConfig()
          this.$message.success('保存成功')
          await this.$router.push('/basic-data/platform/function')
        } catch (error) {
          this.$message.error('系统异常')
          this.isLoading = false
        }
      } else {
        // 路由不存在监管id，走新建配置流程
        try {
          await this.antiCheatConfig.createConfig()
          this.$message.success('保存成功')
          await this.$router.push('/basic-data/platform/function')
        } catch (error) {
          this.$message.error('系统异常')
          this.isLoading = false
        }
      }
    }

    /**
     * 取消-返回列表
     */
    cancel() {
      this.$router.push('/basic-data/platform/function')
    }

    async created() {
      //查询基础监管配置
      await this.baseConfig.queryDetail()
      this.antiCheatConfig = new AntiCheatConfig(this.$route.params.id as string)
      if (this.$route.params.id && this.$route.params.id !== ':id') {
        await this.antiCheatConfig.queryDetail()
      }
    }
  }
</script>
