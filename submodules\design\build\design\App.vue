<template>
  <split-pane :min-percent="15"
              :default-percent="15"
              split="vertical"
              style="overflow: hidden">
    <template slot="paneL">
      <el-aside width="100%"
                style="overflow: auto; padding: 0 10px;">
        <el-tree class="my-tree"
                 :highlight-current="true"
                 :data="dirTree"
                 @node-click="click" />
      </el-aside>
    </template>
    <template slot="paneR">
      <el-container style="margin-left: 7px; background: #afafaf"
                    class="">
        <el-header>
          <el-menu mode="horizontal"
                   background-color="#191a23"
                   text-color="#ffffff">
            <el-menu-item @click="refresh">
              <i class="el-icon-refresh"></i>
              刷新
            </el-menu-item>
            <el-menu-item @click="outPreview">
              <i class="el-icon-link"></i>
              打开预览
            </el-menu-item>
            <el-menu-item>
              <i class="el-icon-s-flag"></i>
              当前访问页面：
              <el-tag size="small">{{ current.fileDir }}</el-tag>
            </el-menu-item>
            <el-menu-item @click="showController = true">
              <i class="el-icon-s-opportunity"></i>
              主题预览色
            </el-menu-item>
          </el-menu>
        </el-header>
        <el-main style="height: 100%; padding: 0; margin: 20px;">
          <iframe :src="src"
                  ref="$iframe"
                  width="100%"
                  height="99%"
                  frameborder="0"></iframe>
        </el-main>
        <el-drawer :visible.sync="showController"
                   :modal="false"
                   width="30%">
          <el-form ref="form"
                   label-width="100px">
            <el-form-item label="选择主题色：">
              <div class="palette-selector">
                <div v-for="(palette, index) in palettes"
                     :key="index"
                     class="theme-color"
                     :style="{ backgroundColor: palette }"
                     @click="toggle(palette)">
                  <i class="el-icon-check"
                     v-if="palette === currentColor"></i>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="色盘选择：">
              <el-color-picker v-model="themeColor"></el-color-picker>
            </el-form-item>
          </el-form>
        </el-drawer>
      </el-container>
    </template>
  </split-pane>
</template>

<script>
import SplitPane from '@hbfe-vue-components/split-pane'
import { palettes } from '../themeController/config/default/admin.config.js'
import treeList from '../../tree-list'
export default {
  components: {
    SplitPane
  },
  data () {
    return {
      dirTree: [],
      src: '',
      current: {},
      showController: false,
      palettes,
      currentColor: palettes[0],
      themeColor: ''
    }
  },
  methods: {
    click (item) {
      if (!item.isDirectory) {
        this.current = item
        this.setSrc()
      }
    },
    setSrc () {
      let pathName = window.location.pathname
      if (process.env.NODE_ENV === 'development') {
        pathName=''
      }
      this.src = `${pathName}${this.current.appPath}.html?module=${this.current.moduleName}&themeColor=${encodeURIComponent(this.themeColor)}`
    },
    outPreview () {
      window.open(this.src, '_blank')
    },
    refresh () {
      this.$refs.$iframe.contentWindow.location.reload()
    },
    async toggle (color) {
      this.themeColor = color
      this.currentColor = color
    },
    changeSrc () {
      this.setSrc()
      this.refresh()
    }
  },
  watch: {
    themeColor () {
      this.changeSrc()
    }
  },
  async created () {
    this.dirTree = treeList
  }
}
</script>

<style lang="scss">
html,
body {
  height: 100%;
  padding: 0;
  margin: 0;
}

.my-tree {
  .el-tree-node__content {
    &:hover {
      background-color: #1a9896 !important;
    }
  }

  .el-tree-node {
    &.is-current {
      .el-tree-node__content {
        background-color: #1a9896 !important;
      }
    }

    &:focus {
      .el-tree-node__content {
        background-color: #1a9896 !important;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
$dark: #191a23;

.el-aside,
.el-header {
  height: 100%;
  background: $dark;
}

.el-header {
  line-height: 60px;
}

.el-container {
  height: 100%;
}

.my-tree {
  background: $dark !important;
  color: #fff !important;
  margin-top: 10px;

  .el-tree-node__content:hover {
    background: #1b1b1b;
  }
}
</style>

<style lang="scss">
.palette-selector {
  display: flex;

  .theme-color {
    width: 15px;
    height: 15px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 5px;
    align-items: center;
    display: flex;
    justify-content: center;

    i {
      font-weight: bold;
      color: white;
      font-size: 12px;
    }
  }
}
</style>
