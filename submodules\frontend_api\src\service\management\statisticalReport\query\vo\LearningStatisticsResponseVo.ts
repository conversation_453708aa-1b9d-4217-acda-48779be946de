import {
  CourseLearningStatisticsResponse,
  ExamLearningStatisticsResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

export class LearningStatisticsResponse {
  /**
   * 期别合格数
   */
  issueQualifiedCount: number
  /**
   * 期别未合格数
   */
  issueUnQualifiedCount: number
  /**
   * 问卷未提交的学院方案参训资格数
   */
  questionnaireUnSubmitCount: number
  /**
   * 问卷已提交的学院方案参训资格数
   */
  questionnaireSubmitCount: number
  /**
   * 净报名人次
   */
  netRegisterCount: number
  /**
   * 考核通过人次
   */
  qualifiedCount: number
  /**
   * 地区人数
   */
  regionPeopleCount: number
  /**
   * 课程学习统计
   */
  courseLearningStatistic: CourseLearningStatisticsResponse = new CourseLearningStatisticsResponse()
  /**
   * 考试统计
   */
  examLearningStatistic: ExamLearningStatisticsResponse = new ExamLearningStatisticsResponse()
}
