import exportAllStudentResult from './queries/exportAllStudentResult.graphql'
import exportErrorStudentResult from './queries/exportErrorStudentResult.graphql'
import pageStudentImportTask from './queries/pageStudentImportTask.graphql'
import batchImportStudent from './mutates/batchImportStudent.graphql'
import bindPhone from './mutates/bindPhone.graphql'
import bindPhoneByAdmin from './mutates/bindPhoneByAdmin.graphql'
import changePasswordByCurrent from './mutates/changePasswordByCurrent.graphql'
import collectiveRegistrationAdminIdentify from './mutates/collectiveRegistrationAdminIdentify.graphql'
import createAdmin from './mutates/createAdmin.graphql'
import disableNoticeForWebChatApplet from './mutates/disableNoticeForWebChatApplet.graphql'
import enableNoticeForWebChatApplet from './mutates/enableNoticeForWebChatApplet.graphql'
import freezeAccount from './mutates/freezeAccount.graphql'
import immediateResetPassword from './mutates/immediateResetPassword.graphql'
import loadBasicValidationData from './mutates/loadBasicValidationData.graphql'
import loadBasicValidationDataWithBindPhone from './mutates/loadBasicValidationDataWithBindPhone.graphql'
import loadRetrievePasswordBasicValidationData from './mutates/loadRetrievePasswordBasicValidationData.graphql'
import loginAndBindOpenPlatform from './mutates/loginAndBindOpenPlatform.graphql'
import platformManagementAdminIdentify from './mutates/platformManagementAdminIdentify.graphql'
import registerAreaAdmin from './mutates/registerAreaAdmin.graphql'
import registerCollective from './mutates/registerCollective.graphql'
import registerStudent from './mutates/registerStudent.graphql'
import registerSubAdmin from './mutates/registerSubAdmin.graphql'
import resetAccountPwdAuthIdentity from './mutates/resetAccountPwdAuthIdentity.graphql'
import resetPasswordWithToken from './mutates/resetPasswordWithToken.graphql'
import resumeAccount from './mutates/resumeAccount.graphql'
import sendSmsCodeByRegister from './mutates/sendSmsCodeByRegister.graphql'
import sendSmsCodeByUpdatePhone from './mutates/sendSmsCodeByUpdatePhone.graphql'
import sendSmsCodeByUpdatePhoneWithSmsValidToken from './mutates/sendSmsCodeByUpdatePhoneWithSmsValidToken.graphql'
import sendSmsCodeByUpdatePwd from './mutates/sendSmsCodeByUpdatePwd.graphql'
import studentIdentify from './mutates/studentIdentify.graphql'
import unbind from './mutates/unbind.graphql'
import unbindWechatOpenPlatform from './mutates/unbindWechatOpenPlatform.graphql'
import updateAdmin from './mutates/updateAdmin.graphql'
import updateAreaAdmin from './mutates/updateAreaAdmin.graphql'
import updateStudent from './mutates/updateStudent.graphql'
import updateStudentSystem from './mutates/updateStudentSystem.graphql'
import updateUser from './mutates/updateUser.graphql'
import updateUserByCurrent from './mutates/updateUserByCurrent.graphql'
import validCaptcha from './mutates/validCaptcha.graphql'
import validSmsCode from './mutates/validSmsCode.graphql'

export {
  exportAllStudentResult,
  exportErrorStudentResult,
  pageStudentImportTask,
  batchImportStudent,
  bindPhone,
  bindPhoneByAdmin,
  changePasswordByCurrent,
  collectiveRegistrationAdminIdentify,
  createAdmin,
  disableNoticeForWebChatApplet,
  enableNoticeForWebChatApplet,
  freezeAccount,
  immediateResetPassword,
  loadBasicValidationData,
  loadBasicValidationDataWithBindPhone,
  loadRetrievePasswordBasicValidationData,
  loginAndBindOpenPlatform,
  platformManagementAdminIdentify,
  registerAreaAdmin,
  registerCollective,
  registerStudent,
  registerSubAdmin,
  resetAccountPwdAuthIdentity,
  resetPasswordWithToken,
  resumeAccount,
  sendSmsCodeByRegister,
  sendSmsCodeByUpdatePhone,
  sendSmsCodeByUpdatePhoneWithSmsValidToken,
  sendSmsCodeByUpdatePwd,
  studentIdentify,
  unbind,
  unbindWechatOpenPlatform,
  updateAdmin,
  updateAreaAdmin,
  updateStudent,
  updateStudentSystem,
  updateUser,
  updateUserByCurrent,
  validCaptcha,
  validSmsCode
}
