schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	convertDistributionContractResponse(dto:ProductConsignmentContractDto):DistributionContractResponse
	"""分销商 -导出批次单明细"""
	exportBatchOrderDetailInDistributor(request:BatchOrderRequest,sort:[BatchOrderSortRequest]):Boolean!
	"""分销商 - 导出批次单"""
	exportBatchOrderInDistributor(request:BatchOrderRequest,sort:[BatchOrderSortRequest]):Boolean!
	"""分销商 -导出批次对账"""
	exportBatchReconciliationInDistributor(request:BatchOrderRequest,sort:[BatchOrderSortRequest]):Boolean!
	"""导出批次退货单明细"""
	exportBatchReturnOrderDetailExcelInDistributor(request:BatchReturnOrderRequest,sort:[BatchReturnOrderSortRequest]):Boolean!
	"""导出批次退货单"""
	exportBatchReturnOrderExcelInDistributor(request:BatchReturnOrderRequest,sort:[BatchReturnOrderSortRequest]):Boolean!
	"""导出批次退货报名对账"""
	exportBatchReturnReconciliationExcelInDistributor(request:BatchReturnOrderRequest,sort:[BatchReturnOrderSortRequest]):Boolean!
	"""分销商  - 导出个人订单"""
	exportOrderExcelInDistributor(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""分销商  - 导出个人对账"""
	exportReconciliationExcelInDistributor(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""分销商 -导出个人退货单"""
	exportReturnOrderExcelInDistributor(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""分销商 -导出个人报名退货对账"""
	exportReturnReconciliationExcelInDistributor(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""根据推广门户域名获取 当前 推广门户id
		portalType  1-web端 2-移动端
	"""
	getCurrentPortalIdInDustributor(portalType:Int):String @optionalLogin
	"""根据优惠申请id获取优惠地区树
		@param applyId
		@return
	"""
	getDiscountRegionInSubject(applyId:String):[RegionTreeResponse]
	"""根据合同ID获取合同信息
		@param contractId
		@return
	"""
	getDistributionContractByContractId(contractId:String):DistributionContractDetailResponse
	"""功能描述 : 查询当前登录分销商信息接口"""
	getDistributionServicerInfoInMyself:DistributionServicerResponse
	"""功能描述 : 项目级-分销服务商详情查询接口"""
	getDistributionServicerInfoInSubProject(servicerId:String):DistributionServicerResponse @optionalLogin
	"""根据分销商商品id获取分销商商品详情"""
	getDistributorCommodityInSubProject(distributorCommodityId:String):DistributorCommodityResponse
	"""供应商 - 查询已创建的推广门户"""
	getDistributorPortalInfosInSupplier(page:Page,portalRequestInSupplier:PortalRequestInSupplier):DistributorPortalInfoResponsePage @page(for:"DistributorPortalInfoResponse")
	"""根据地区树id查询最末级详细地区
		@param regionTreeId 地区树id
	"""
	getLastLevelRegionDetailByRegionTreeId(regionTreeId:String):[RegionDataResponse]
	"""查询资讯详情
		@param newId
		@return
	"""
	getNewsDetail(newId:String!):NewsDetailResponse
	"""获取推广门户线下集体报名配置
		@return
	"""
	getOfflineCollectiveByPotalIdInDistributor:PortalOfflineCollectiveSignUpSettingResponse
	"""获取推广门户是否开启
		@param portalId
		@return
	"""
	getOfflineRegistrationConfigByPortalId(portalId:String):OfflineRegistrationConfigResponse @optionalLogin
	"""查看定价方案的推广门户
		@param requset
		@return
	"""
	getPolicyPortalList(requset:PolicyPortalRequset):[PolicyPortalResponse]
	"""获取当前分销商下的所有门户
		@return
	"""
	getPortalInDistributor:[PortalInDistributorResponse]
	"""获取推广门户下的展示的定价方案数量
		@param portalIds 推广门户id
		@return
	"""
	getPortalPricingCount(portalIds:[String]):[PricingPolicyCommodityCountResponse]
	"""查看海报
		@param identifier
		@return
	"""
	getPosterConfigurationInDistributor(identifier:String!):PosterConfigurationResponse
	"""根据优惠申请id获取优惠申请信息
		@param applyId 申请id
		@return
	"""
	getProductDiscountApplyInfoInSubject(applyId:String):CommodityDiscountApplyResponse
	"""分销商-推广门户详情
		@param request
		@return
	"""
	getPromotionPortalInfoInDistributor(request:PortalInfoDetailRequest):PortalInfoDetailResponse
	"""根据优惠申请id 查询地区(供应商)
		@param applyId
		@param parentRegionId
		@param regionLevel
		@return
	"""
	getRegionByApplyIdInSupplier(applyId:String,parentRegionId:String,regionLevel:Int):[RegionTreeResponse]
	"""根据合同id查分销地区(分销商)
		@param contractId 合同id
	"""
	getRegionByContractIdInDistributor(contractId:String):[RegionTreeResponse]
	"""根据合同id查分销地区(供应商)
		@param contractId     合同id
		@param parentRegionId 父级地区id
		@param regionLevel    地区查询等级 0-查全部地区  1-只查省级  2-只查市级  3-只查区县级
	"""
	getRegionByContractIdInSupplier(contractId:String,parentRegionId:String,regionLevel:Int):[RegionTreeResponse]
	"""根据地区树id查详细地区
		@param regionTreeId 地区树id
	"""
	getRegionDetailByRegionTreeId(regionTreeId:String):[RegionTreeResponse]
	"""获取业务员详情
		@param salesmanId
	"""
	getSalesman(salesmanId:String):SalesmanResponse
	"""查询是否当前分销商下（已发布的）的推广子门户只有一个
		唯一时 返回门户id  不唯一时返回空
		portalType 1-web 2-移动端
		@return
	"""
	ifOnlyPorttalInDustributor(portalType:Int):String @optionalLogin
	"""判断网校商品是否已授权给分销商
		@param commodityIdList 网校商品id列表
		@param distributorId 分销商id
		@return 已被授权的网校商品id列表
	"""
	judgeCommodityAuthorized(commodityIdList:[String],distributorId:String):[String]
	"""判断定价方案是否已添加到渠道
		@param pricingSchemeIdList 定价方案id列表
		@param channelId 渠道id
		@return 已被添加到门户的定价方案id列表
	"""
	listAddedPortalPricingPolicy(pricingSchemeIdList:[String],channelId:String):[String]
	"""分销商-获取我的分销商商品分销地区属性集合"""
	listMyDistributorCommodityRegionInDistributor:[CommoditySkuRegionPropertyNodeResponse] @optionalLogin
	"""分销商-获取我的分销商商品sku属性集合"""
	listMyDistributorCommoditySkuPropertyInDistributor(request:DistributorCommodityAndRelationRequest):DistributorCommodityPropertyCollectionResponse
	"""分销商-获取当前分销商下的定价策略的商品sku属性集合"""
	listPricingSchemeSkuPropertyInDistributor(request:DistributorCommodityAndRelationRequest):DistributorCommodityPropertyCollectionResponse
	"""供应商-获取当前供应商下的定价方案的商品sku属性集合"""
	listPricingSchemeSkuPropertyInSupplier(request:DistributorCommodityAndRelationRequest):DistributorCommodityPropertyCollectionResponse
	"""分销商-获取当前分销商的优惠申请sku属性"""
	listProductDiscountApplySkuPropertyInDistributor(request:DistributorCommodityAndRelationRequest):DistributorCommoditySkuPropertyCollectionResponse @optionalLogin
	"""分销商分页获取商品分销商开通统计信息
		商品+分销商+是否有优惠+缴费渠道+支付方式维度的开通统计
	"""
	pageCommoditySkuDistributorOpenReportInDistributor(page:Page,request:StatisticTradeRecordRequest):CommodityDistributorOpenReportResponsePage @page(for:"CommodityDistributorOpenReportResponse")
	"""供应商分页获取商品分销商开通统计信息
		商品+分销商+是否有优惠+缴费渠道+支付方式维度的开通统计
	"""
	pageCommoditySkuDistributorOpenReportInSupplier(page:Page,request:StatisticTradeRecordRequest):CommodityDistributorOpenReportInSupplierResponsePage @page(for:"CommodityDistributorOpenReportInSupplierResponse")
	"""供应商分页获取商品开通统计信息
		商品+缴费渠道+支付方式维度的开通统计接口
	"""
	pageCommoditySkuOpenReportInSupplier(page:Page,request:StatisticTradeRecordRequest):CommodityOpenReportResponsePage @page(for:"CommodityOpenReportResponse")
	"""分销商-分页查询分销商品"""
	pageDistributorCommodityBasicInSubject(page:Page,request:DistributorCommodityAndRelationRequest):DistributorCommodityAndRelationResponsePage @page(for:"DistributorCommodityAndRelationResponse")
	"""分页查询当前供应商下已授权商品"""
	pageDistributorCommodityInSupplier(page:Page,request:DistributorCommodityAndRelationRequest,sortRequests:[DistributorCommoditySortRequest]):DistributorCommodityAndRelationResponsePage @page(for:"DistributorCommodityAndRelationResponse")
	"""分页获取当前供应商下的分销商列表"""
	pageDistributorInSupplier(page:Page,request:DistributionAuthorizationDistributorRequest):DistributionAuthorizationDistributorResponsePage @page(for:"DistributionAuthorizationDistributorResponse")
	"""分销商分页查询可更换的分销期别列表"""
	pageDistributorIssueCommodityInDistributor(page:Page,request:DistributorIssueCommodityRequest,sortRequests:[SaleCommoditySortRequest]):DistributorIssueCommodityResponsePage @page(for:"DistributorIssueCommodityResponse")
	"""分页查询可更换的分销期别列表"""
	pageDistributorIssueCommodityInServicer(page:Page,request:DistributorIssueCommodityRequest,sortRequests:[SaleCommoditySortRequest]):DistributorIssueCommodityResponsePage @page(for:"DistributorIssueCommodityResponse")
	"""供应商获取分销商销售统计"""
	pageDistributorSellStatisticInSupplier(page:Page,request:StatisticTradeRecordRequest):DistributorSellStatisticIncludedPurchaseResponsePage @page(for:"DistributorSellStatisticIncludedPurchaseResponse")
	"""分页获取当前供应商下的分销商列表"""
	pageDistributorWithOnlineSchoolInSupplier(page:Page,request:DistributionRelationshipRequest):DistributionRelationshipResponsePage @page(for:"DistributionRelationshipResponse")
	"""分页获取当前供应商下的业务员对接记录列表"""
	pageDockingDistributorSalesmanInSupplier(page:Page,request:DockingSalesmanRelationshipRequest):DockingSalesmanRelationshipResponsePage @page(for:"DockingSalesmanRelationshipResponse")
	"""分销商-分页查询我的分销商商品"""
	pageMyDistributorCommodityInDistributor(page:Page,request:DistributorCommodityAndRelationRequest,sortRequests:[DistributorCommoditySortRequest]):DistributorCommodityAndRelationResponsePage @page(for:"DistributorCommodityAndRelationResponse")
	"""分页获取当前供应商下的存在分销关系的合作网校列表
		供应商-分销商管理-分销设置
		@param page
	"""
	pageOnlineSchoolWithDistributionRelationInSupplier(page:Page,request:OnlineSchoolDistributionRelationshipRequest,sort:[DistributionRelationshipSortRequest]):OnlineSchoolDistributionRelationshipResponsePage @page(for:"OnlineSchoolDistributionRelationshipResponse")
	"""分销商-分页查询优惠申请信息
		Params:
		page – 分页条件 request – 优惠申请筛选条件
	"""
	pageProductDiscountApplyInfoInDistributor(page:Page,request:ProductDiscountApplyRequest,sortRequests:[ProductDiscountApplySortRequest]):CommodityDiscountApplyResponsePage @page(for:"CommodityDiscountApplyResponse")
	"""供应商-分页查询优惠申请信息
		@param page    分页条件
		@param request 优惠申请筛选条件
		@return
	"""
	pageProductDiscountApplyInfoInSupplier(page:Page,request:ProductDiscountApplyRequest,sortRequests:[ProductDiscountApplySortRequest]):CommodityDiscountApplyResponsePage @page(for:"CommodityDiscountApplyResponse")
	"""分销商-定价方案-定价方案-列表"""
	pageProductPricingSchemeInDistributor(page:Page,request:DistributorCommodityAndRelationRequest,sortRequests:[DistributorCommoditySortRequest]):DistributorCommodityAndRelationResponsePage @page(for:"DistributorCommodityAndRelationResponse")
	"""供应商-定价方案-定价方案-列表"""
	pageProductPricingSchemeInSupplier(page:Page,request:DistributorCommodityAndRelationRequest,sortRequests:[DistributorCommoditySortRequest]):DistributorCommodityAndRelationResponsePage @page(for:"DistributorCommodityAndRelationResponse")
	"""分销商-推广门户列表
		@param page
		@param request
		@return
	"""
	pagePromotionPortalInfoInDistributor(page:Page,request:PortalInfoRequest):PortalInfoResponsePage @page(for:"PortalInfoResponse")
	"""分页获取当前供应商下的业务员列表"""
	pageSalesmanInSupplier(page:Page,request:SalesmanRequest):SalesmanResponsePage @page(for:"SalesmanResponse")
	"""获取资讯列表
		@param queryRequest
		@param page
		@return
	"""
	pageSimpleNews(queryRequest:NewsSimpleQueryRequest,page:Page):NewsSimpleResponsePage @page(for:"NewsSimpleResponse")
	"""分页获取当前服务商下的学员培训方案学习列表
		@param page
		@param request
		@return
	"""
	pageStudentSchemeLearningInDistributor(page:Page,request:StudentSchemeLearningRequestInDistributor,sort:[StudentSchemeLearningSortRequest]):StudentSchemeLearningResponsePage @page(for:"StudentSchemeLearningResponse")
	"""分页获取当前服务商下的学员培训方案学习列表
		@param page
		@param request
		@return
	"""
	pageStudentSchemeLearningInSupplier(page:Page,request:StudentSchemeLearningRequestInSupplier,sort:[StudentSchemeLearningSortRequest]):StudentSchemeLearningResponsePage @page(for:"StudentSchemeLearningResponse")
	"""分销商-统计分销商商品数量"""
	statisticAuthorizedDistributorCommodityInDistributor(request:DistributorCommodityAndRelationRequest):[DistributorCommodityStatisticResponse]
	"""分销商获取交易统计信息"""
	statisticTradeRecordInDistributor(request:StatisticTradeRecordRequest):TradeStatisticResponse
	"""供应商获取交易统计信息"""
	statisticTradeRecordInSupplier(request:StatisticTradeRecordRequest):TradeStatisticResponse
	"""分销商获取交易合计统计信息"""
	statisticTradeSummaryInDistributor(request:StatisticTradeRecordRequest):[TradeSummaryStatisticResponse]
	transformDistributorPortalInfoResponse(servicerPortalDto:ServicerPortalDto,unitName:String):DistributorPortalInfoResponse
	transformPortalInfoResponse(servicerPortalDto:ServicerPortalDto):PortalInfoResponse
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input ObsFileMetaData @type(value:"com.fjhb.ms.data.export.commons.utils.async.ObsFileMetaData") {
	bizType:String
	owner:String
	sign:String
}
input ProductConsignmentContractDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.ProductConsignmentContractDto") {
	productConsignmentContractId:String
	contractType:Int
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	servicerId:String
	unitId:String
	supplierId:String
	distributorId:String
	superiorDistributorId:String
	distributionLevel:Int
	contractStartTime:DateTime
	contractEndTime:DateTime
	status:Int
	effectiveStatusChangeTime:DateTime
	stopStatusChangeTime:DateTime
	contractDurationType:Int
	signTime:DateTime
	distributorServicerIdList:[String]
	regionList:[String]
	contractDistributionRegionTreeId:String
}
input PortalBannerDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.ServicePortal.PortalBannerDto") {
	id:String
	name:String
	path:String
	link:String
	sort:Int
	enable:Boolean
	createdTime:DateTime
}
input PortalFriendLinkDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.ServicePortal.PortalFriendLinkDto") {
	id:String
	title:String
	picture:String
	friendLinkType:Int
	link:String
	sort:Int
	createdTime:DateTime
}
input PortalMenuDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.ServicePortal.PortalMenuDto") {
	id:String
	name:String
	displayName:String
	parentId:String
	type:Int
	sourceType:Int
	link:String
	code:String
	referenceId:String
	enable:Boolean
	sort:Int
	createdTime:DateTime
	updatedTime:DateTime
}
input PortalPlateDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.ServicePortal.PortalPlateDto") {
	id:String
	name:String
	displayName:String
	parentId:String
	type:Int
	sourceType:Int
	link:String
	code:String
	referenceId:String
	allowChildren:Int
	sort:Int
	createdTime:DateTime
	updatedTime:DateTime
}
input ServicerPortalDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.ServicePortal.ServicerPortalDto") {
	id:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	servicerId:String
	belongServiceId:String
	category:Int
	identifier:String
	portalType:Int
	title:String
	shortName:String
	logo:String
	icon:String
	themeColor:String
	mobileQRcode:String
	CSPhonePicture:String
	CSPhone:String
	CSCallTime:String
	CSOnlineCodeId:String
	CSWechat:String
	trainingFlowPicture:String
	footContentId:String
	slogan:String
	domainName:String
	domainShortCode:String
	contentId:String
	isProvideServiceAccount:Int
	isPublished:Int
	publishedTime:DateTime
	unpublishedTime:DateTime
	cnzz:String
	dirName:String
	domainNameType:Int
	plates:[PortalPlateDto]
	friendLinks:[PortalFriendLinkDto]
	banner:[PortalBannerDto]
	menu:[PortalMenuDto]
	createdTime:DateTime
	updatedTime:DateTime
	isDeleted:Int
	deletedTime:DateTime
	recordCreatedTime:DateTime
	recordUpdatedTime:DateTime
}
input BigDecimalScopeRequest @type(value:"com.fjhb.ms.query.commons.BigDecimalScopeRequest") {
	begin:BigDecimal
	end:BigDecimal
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
input BatchOrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.BatchOrderRequest") {
	batchOrderNoList:[String]
	basicData:BatchOrderBasicDataRequest
	payInfo:OrderPayInfoRequest
	creatorIdList:[String]
	isInvoiceApplied:Boolean
	distributorId:String
	portalId:String
	isDistributionExcludePortal:Boolean
}
input BatchOrderSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.BatchOrderSortRequest") {
	field:BatchOrderSortField
	policy:SortPolicy
}
input BatchOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.nested.BatchOrderBasicDataRequest") {
	batchOrderStatusList:[Int]
	batchOrderStatusChangeTime:BatchOrderStatusChangeTimeRequest
	batchOrderPaymentStatusList:[Int]
	batchOrderDeliveryStatusList:[Int]
	batchOrderAmountScope:BigDecimalScopeRequest
	saleChannels:[Int]
	saleChannelName:String
	saleChannelIds:[String]
}
input BatchOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.nested.BatchOrderStatusChangeTimeRequest") {
	unConfirmed:DateScopeRequest
	normal:DateScopeRequest
	completed:DateScopeRequest
	closed:DateScopeRequest
	committing:DateScopeRequest
	canceling:DateScopeRequest
}
input BatchReturnOrderApprovalInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.nested.BatchReturnOrderApprovalInfoRequest") {
	approveTime:DateScopeRequest
}
input BatchReturnOrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.BatchReturnOrderRequest") {
	batchReturnOrderList:[String]
	basicData:BatchReturnOrderBasicDataRequest
	approvalInfo:BatchReturnOrderApprovalInfoRequest
	batchOrderInfo:BatchOrderInfoRequest
	distributorId:String
	portalId:String
	isDistributionExcludePortal:Boolean
}
input BatchReturnOrderSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.BatchReturnOrderSortRequest") {
	field:BatchReturnOrderSortField
	policy:SortPolicy
}
input BatchOrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.nested.BatchOrderInfoRequest") {
	batchOrderNoList:[String]
	creatorIdList:[String]
	receiveAccountIdList:[String]
	flowNoList:[String]
	paymentOrderTypeList:[Int]
}
input BatchReturnCloseReasonRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.nested.BatchReturnCloseReasonRequest") {
	closeTypeList:[Int]
}
input BatchReturnOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.nested.BatchReturnOrderBasicDataRequest") {
	batchReturnOrderStatus:[Int]
	batchReturnCloseReason:BatchReturnCloseReasonRequest
	batchReturnStatusChangeTime:BatchReturnOrderStatusChangeTimeRequest
	refundAmountScope:BigDecimalScopeRequest
	saleChannels:[Int]
	saleChannelName:String
	saleChannelIds:[String]
}
input BatchReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.nested.BatchReturnOrderStatusChangeTimeRequest") {
	applied:DateScopeRequest
	returnCompleted:DateScopeRequest
}
input CommodityAuthInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.CommodityAuthInfoRequest") {
	distributorId:String
	distributionLevel:Int
	superiorDistributorId:String
	supplierId:String
	salesmanId:String
}
input CommoditySkuRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.CommoditySkuRequest") {
	commoditySkuIdList:[String]
	saleTitle:String
	issueInfo:IssueInfo
	skuProperty:SkuPropertyRequest1
	externalTrainingPlatform:[String]
	trainingInstitution:[String]
}
input RegionSkuPropertyRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertySearchRequest") {
	regionSearchType:Int
	region:[RegionSkuPropertyRequest1]
}
input SkuPropertyRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.SkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest1
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	trainingObject:[String]
	positionCategory:[String]
	jobLevel:[String]
	jobCategory:[String]
	grade:[String]
	subject:[String]
	learningPhase:[String]
	discipline:[String]
	trainingChannelIds:[String]
	certificatesType:[String]
	practitionerCategory:[String]
	qualificationCategory:[String]
	trainingForm:[String]
}
input IssueInfo @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.IssueInfo") {
	issueId:String
	issueName:String
	issueNum:String
	trainStartTime:DateTime
	trainEndTime:DateTime
	sourceType:String
	sourceId:String
}
input OrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.OrderRequest") {
	orderNoList:[String]
	subOrderNoList:[String]
	subOrderReturnStatus:[Int]
	orderBasicData:OrderBasicDataRequest
	subOrderBasicData:SubOrderBasicDataRequest
	payInfo:OrderPayInfoRequest
	buyerIdList:[String]
	deliveryCommodity:CommoditySkuRequest
	currentCommodity:CommoditySkuRequest
	saleChannel:Int
	saleChannels:[Int]
	excludeSaleChannels:[Int]
	saleChannelIds:[String]
	saleChannelName:String
	cardTypeId:String
	distributorId:String
	portalId:String
	orderFixQuery:OrderFixQueryRequest
	isDistributionExcludePortal:Boolean
	externalTrainingPlatform:[String]
	unitIds:[String]
	issueId:[String]
	policyTrainingSchemeIdList:[String]
	declarationUnitCodeList:[String]
	settlementStatus:Int
	settlementDate:DateScopeRequest
}
input OrderSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.OrderSortRequest") {
	field:OrderSortField
	policy:SortPolicy
}
input OrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderBasicDataRequest") {
	orderType:Int
	batchOrderNoList:[String]
	orderStatusList:[Int]
	orderPaymentStatusList:[Int]
	orderDeliveryStatusList:[Int]
	orderStatusChangeTime:OrderStatusChangeTimeRequest
	channelTypesList:[Int]
	excludeChannelTypesList:[Int]
	terminalCodeList:[String]
	orderAmountScope:BigDecimalScopeRequest
}
input OrderFixQueryRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderFixQueryRequest") {
	excludeChannelTypesList:[Int]
	excludeSaleChannels:[Int]
}
input OrderPayInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderPayInfoRequest") {
	receiveAccountIdList:[String]
	flowNoList:[String]
	paymentOrderTypeList:[Int]
}
input OrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderStatusChangeTimeRequest") {
	normalDateScope:DateScopeRequest
	completedDatesScope:DateScopeRequest
}
input SubOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.SubOrderBasicDataRequest") {
	discountType:Int
	discountSourceId:String
	useDiscount:Boolean
	commodityAuthInfo:CommodityAuthInfoRequest
}
input ReturnOrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.ReturnOrderRequest") {
	unitIdList:[String]
	returnOrderNoList:[String]
	basicData:ReturnOrderBasicDataRequest
	approvalInfo:ReturnOrderApprovalInfoRequest
	returnCommoditySkuIdList:[String]
	returnCommodity:CommoditySkuRequest
	refundCommoditySkuIdList:[String]
	refundCommodity:CommoditySkuRequest
	subOrderInfo:SubOrderInfoRequest
	commodityAuthInfo:CommodityAuthInfoRequest
	distributorId:String
	portalId:String
	isDistributionExcludePortal:Boolean
}
input ReturnSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.ReturnSortRequest") {
	field:ReturnOrderSortField
	policy:SortPolicy
}
input OrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.OrderInfoRequest") {
	orderNoList:[String]
	batchOrderNoList:[String]
	buyerIdList:[String]
	receiveAccountIdList:[String]
	flowNoList:[String]
	channelTypesList:[Int]
	terminalCodeList:[String]
	saleChannel:Int
	saleChannels:[Int]
	saleChannelName:String
	saleChannelIds:[String]
	policyTrainingSchemeIdList:[String]
	declarationUnitCodeList:[String]
}
input ReturnCloseReasonRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnCloseReasonRequest") {
	closeTypeList:[Int]
}
input ReturnOrderApprovalInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderApprovalInfoRequest") {
	approveTime:DateScopeRequest
}
input ReturnOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderBasicDataRequest") {
	returnOrderStatus:[Int]
	returnOrderTypes:[Int]
	applySourceType:String
	applySourceIdList:[String]
	returnCloseReason:ReturnCloseReasonRequest
	returnStatusChangeTime:ReturnOrderStatusChangeTimeRequest
	refundAmountScope:BigDecimalScopeRequest
}
input ReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderStatusChangeTimeRequest") {
	applied:DateScopeRequest
	returnCompleted:DateScopeRequest
}
input SubOrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.SubOrderInfoRequest") {
	subOrderNoList:[String]
	orderInfo:OrderInfoRequest
	discountType:Int
	useDiscount:Boolean
}
"""<AUTHOR> linq
	@date : 2025-04-15 14:57
	@description : 查询可更换的分销期别列表request入参
"""
input DistributorIssueCommodityRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.distributorcommodity.DistributorIssueCommodityRequest") {
	"""原始商品id集合"""
	rootCommodityIdList:[String]
	"""排除的期别商品id集合"""
	excludedIssueCommodityIdList:[String]
	"""期别名称（模糊查询）"""
	issueName:String
	"""期别名称（精确查询）"""
	issueNameMatch:String
	"""期别报名时间"""
	issueRegisterTime:DateScopeRequest
	"""期别培训时间"""
	issueTrainingTime:DateScopeRequest
}
"""简略资讯查询条件"""
input NewsSimpleQueryRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.common.NewsSimpleQueryRequest") {
	"""资讯标题"""
	title:String
	"""资讯分类编号"""
	necId:String
	"""发布地区编码"""
	areaCodePath:String
	"""弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]"""
	popUpsStatus:Int!
	"""资讯状态 [-1-全部，0-草稿，1-发布 默认全部]"""
	status:Int!
	"""门户id"""
	portalId:String
	"""门户标识"""
	identifier:String
}
"""商品sku属性查询条件"""
input PropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.common.PropertyRequest") {
	"""商品skuKey"""
	propertyKey:String
	"""商品skuValue"""
	propertyValue:String
}
"""地区查询请求参数
	<AUTHOR>
	@date 2022/02/25
"""
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.common.RegionSkuPropertySearchRequest") {
	"""地区匹配方式
		<p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
		<p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
		@see RegionSearchType
	"""
	regionSearchType:Int
	"""地区"""
	region:[RegionSkuPropertyRequest]
}
"""商品sku属性查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input SkuPropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.common.SkuPropertyRequest") {
	"""年度"""
	year:[String]
	"""地区"""
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	"""行业"""
	industry:[String]
	"""科目类型"""
	subjectType:[String]
	"""培训类别"""
	trainingCategory:[String]
	"""培训专业"""
	trainingProfessional:[String]
	"""技术等级"""
	technicalGrade:[String]
	"""卫生行业-培训对象"""
	trainingObject:[String]
	"""卫生行业-岗位类别"""
	positionCategory:[String]
	"""工勤行业-技术等级"""
	jobLevel:[String]
	"""工勤行业-工种"""
	jobCategory:[String]
	"""年级"""
	grade:[String]
	"""科目"""
	subject:[String]
	"""学段"""
	learningPhase:[String]
	"""学科"""
	discipline:[String]
	"""专题id"""
	trainingChannelIds:[String]
	"""黑龙江药师-证书类型"""
	certificatesType:[String]
	"""黑龙江药师-执业类别"""
	practitionerCategory:[String]
	"""培训形式"""
	trainingForm:[String]
}
input DistributionAuthorizationDistributorRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributionrelationship.DistributionAuthorizationDistributorRequest") {
	"""分销商名称"""
	distributorName:String
	"""分销商id"""
	distributorId:String
	"""分销状态"""
	distributionStatus:Int
	"""是否授权"""
	isAuthorized:Boolean
	"""商品ID"""
	commodityId:String
	"""网校ID"""
	onlineSchoolId:String
	"""合同id"""
	contractId:String
	"""分销等级"""
	distributorLevel:Int
	"""合作状态
		0 未开始
		1 进行中
		2 即将到期
		3 已结束
	"""
	cooperateStatus:[Int]
	"""是否去重
		默认去重
		false 不去重
		true 去重
	"""
	isDistinct:Boolean
	"""父级区域id"""
	parentRegionId:String
}
input DistributionRelationshipRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributionrelationship.DistributionRelationshipRequest") {
	"""分销商名称"""
	distributorName:String
}
"""网校分销Request"""
input OnlineSchoolDistributionRelationshipRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributionrelationship.OnlineSchoolDistributionRelationshipRequest") {
	"""分销商id"""
	distributorId:String
	"""网校名称"""
	onlineSchoolName:String
	"""域名"""
	domain:String
	"""分销地区"""
	regionList:[String]
	"""上级分销商名称"""
	superDistributorName:String
	"""分销关系状态，0代表有效，1代表失效"""
	status:Int
	"""分销级别，1代表一级，2代表二级"""
	distributionLevel:Int
	"""合同id"""
	contractIds:[String]
	"""合作状态
		0 未开始
		1 进行中
		2 即将到期
		3 已结束
	"""
	cooperateStatus:[Int]
	"""状态和合作状态之间的关系（不传默认AND）
		AND 并且
		OR  或者
	"""
	queryType:QueryType
}
"""供应商授权出的商品"""
input DistributorCommodityAndRelationRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.DistributorCommodityAndRelationRequest") {
	"""分销商商品名称"""
	saleTitle:String
	"""分销商id集合"""
	distributorIdList:[String]
	"""分销商等级"""
	distributorLevel:Int
	"""销售状态 1-有效 2-无效"""
	saleStatus:Int
	"""分销地区路径"""
	contractDistributionRegionPathList:[String]
	"""商品sku属性"""
	propertyList:[PropertyRequest]
	"""培训方案类型"""
	schemeTypeList:[String]
	"""授权价格类型 1-固定 2-区间"""
	priceType:Int
	"""分销价格范围查询-最大价格"""
	maxPrice:BigDecimal
	"""分销价格范围查询-最小价格"""
	minPrice:BigDecimal
	"""分销价格-最大价格"""
	policyMaxPrice:BigDecimal
	"""分销价格-最小价格"""
	policyMinPrice:BigDecimal
	"""定价方案状态"""
	statusList:[Int]
	"""分销是否有效
		0-有效 1-无效
		商品的分销开始时间、结束时间作为判断
	"""
	distributionStatus:Int
	"""分销商商品id集合"""
	distributorCommodityIdList:[String]
	"""商品id集合"""
	commodityIdList:[String]
	"""网校原始商品id集合"""
	rootCommodityIdList:[String]
	"""需要排除的网校原始商品id集合"""
	excludedRootCommodityIdList:[String]
	"""网校id"""
	onlineSchoolId:String
	"""培训方案名称"""
	schemeName:String
	"""网校销售状态
		0-开启 1-关闭
		商品的网校销售开始时间、结束时间作为判断
	"""
	onlineSchoolStatus:Int
	"""授权商品来源类型"""
	commoditySourceTypeList:[Int]
	"""定价方案id"""
	productPricingSchemeIdList:[String]
	"""需要排除的定价方案id"""
	excludedPricingSchemeIdList:[String]
	"""是否存在定价方案"""
	existPricingScheme:Boolean
	"""是否已启用定价方案"""
	enablePricingScheme:Boolean
	"""是否已启用优惠申请"""
	enableDiscountScheme:Boolean
	"""推广门户标识id"""
	portalIdentify:String
	"""推广门户展示名称"""
	showName:String
	"""推广门户简称"""
	shortName:String
	"""门户域名"""
	domainName:String
	"""门户状态
		0-停用 1-启用
	"""
	portalStatus:Int
	"""门户展示 (0-不展示, 1-展示）"""
	showPortal:Int
	"""门户推广 (0-不推广, 1-推广）"""
	portalPromotion:Int
	"""商品上架状态
		0-下架 1-上架
	"""
	shelveStatus:Int
	"""销售渠道类型
		0-自营渠道 1-分销渠道 2-专题渠道 3-华医网 4-推广门户渠道
	"""
	saleChannelType:Int
	"""销售渠道id"""
	saleChannelId:String
	"""优惠申请审批状态
		0-待处理 1-通过 2-未通过
	"""
	auditStatusList:[Int]
	"""优惠状态
		1-开启 2-关闭
		与优惠申请内容状态有关、与优惠周期约束、优惠开始时间、优惠结束时间有关
	"""
	discountStatusList:[Int]
}
input PolicyPortalRequset @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.PolicyPortalRequset") {
	"""定价方案id"""
	productPricingSchemeId:String
}
input PortalInfoDetailRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.PortalInfoDetailRequest") {
	"""门户id"""
	id:String
}
input PortalInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.PortalInfoRequest") {
	"""门户id"""
	portalId:String
	"""门户标题"""
	title:String
	"""门户简称"""
	shortName:String
	"""域名"""
	domainName:String
	"""门户状态  是否已发布 1-开启 2-关闭"""
	isPublished:Boolean
}
input PortalRequestInSupplier @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.PortalRequestInSupplier") {
	"""门户id"""
	portalId:String
	"""分销商id"""
	belongServiceId:String
	"""门户展示名称"""
	title:String
	"""门户简称"""
	shortName:String
}
"""学员培训方案学习查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 11:40
"""
input StudentSchemeLearningRequestInDistributor @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.StudentSchemeLearningRequestInDistributor") {
	"""学员信息"""
	student:UserRequest
	"""报名信息"""
	learningRegister:LearningRegisterRequest
	"""培训班信息"""
	scheme:SchemeRequest
	"""学习信息"""
	studentLearning:StudentLearningRequest
	"""数据分析信息"""
	dataAnalysis:DataAnalysisRequest
	"""对接管理系统"""
	connectManageSystem:ConnectManageSystemRequest
	"""扩展信息"""
	extendedInfo:ExtendedInfoRequest
	"""方案是否提供培训证明"""
	openPrintTemplate:Boolean
	"""是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1"""
	saleChannels:[Int]
	"""专题名称"""
	trainingChannelName:String
	"""专题Id  用于不同专题域名 查询对应专题的培训班"""
	trainingChannelId:String
	"""门户id"""
	portalId:String
}
"""学员培训方案学习查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 11:40
"""
input StudentSchemeLearningRequestInSupplier @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.StudentSchemeLearningRequestInSupplier") {
	"""学员信息"""
	student:UserRequest
	"""报名信息"""
	learningRegister:LearningRegisterRequest
	"""培训班信息"""
	scheme:SchemeRequest
	"""学习信息"""
	studentLearning:StudentLearningRequest
	"""数据分析信息"""
	dataAnalysis:DataAnalysisRequest
	"""对接管理系统"""
	connectManageSystem:ConnectManageSystemRequest
	"""扩展信息"""
	extendedInfo:ExtendedInfoRequest
	"""方案是否提供培训证明"""
	openPrintTemplate:Boolean
	"""是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1"""
	saleChannels:[Int]
	"""专题名称"""
	trainingChannelName:String
	"""专题Id  用于不同专题域名 查询对应专题的培训班"""
	trainingChannelId:String
	"""门户id"""
	portalId:String
	"""分销商ID"""
	distributorId:String
}
input ExtendedInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.ExtendedInfoRequest") {
	"""是否打印
		true 打印 false 未打印
	"""
	whetherToPrint:Boolean
}
"""学员方案学习查询排序条件"""
input StudentSchemeLearningSortRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.StudentSchemeLearningSortRequest") {
	"""需要排序的字段"""
	field:StudentSchemeLearningSortField
	"""正序或倒序"""
	policy:SortPolicy1
}
"""学员学习信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
input StudentLearningRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.learning.StudentLearningRequest") {
	"""培训结果
		<p>
		-1:未知，培训尚未完成
		1:培训合格
		0:培训不合格
	"""
	trainingResultList:[Int]
	"""培训结果时间"""
	trainingResultTime:DateScopeRequest
	"""无需学习的学习方式类型
		<p>
		1: 选课学习方式
		2: 考试学习方式
		3: 练习学习方式
		4：自主学习课程学习方式
	"""
	notLearningTypeList:[Int]
	"""课程学习状态（0：未学习 1：学习中 2：学习完成）"""
	courseScheduleStatus:Int
	"""考试结果（-1：未考核 0：不合格 1：合格）"""
	examAssessResultList:[Int]
}
"""地区sku属性查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/2/25 10:55
"""
input RegionSkuPropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.scheme.RegionSkuPropertyRequest") {
	"""地区: 省"""
	province:String
	"""地区: 市"""
	city:String
	"""地区: 区县"""
	county:String
}
"""地区匹配查询
	<AUTHOR>
	@version 1.0
	@date 2022/2/25 14:19
"""
input RegionSkuPropertySearchRequest12 @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.scheme.RegionSkuPropertySearchRequest") {
	"""地区"""
	region:[RegionSkuPropertyRequest]
	"""地区匹配条件
		<p>
		ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回，如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
		PART部分匹配：查询结果返回的地区与查询条件有给值的地区就会返回 如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
	"""
	regionSearchType:Int
}
"""培训方案信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
input SchemeRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.scheme.SchemeRequest") {
	"""培训方案id"""
	schemeId:String
	"""培训方案id"""
	schemeIdList:[String]
	"""培训方案类型
		<p>
		chooseCourseLearning: 选课规则
		autonomousCourseLearning: 自主选课
	"""
	schemeType:String
	"""方案名称"""
	schemeName:String
	"""培训属性"""
	skuProperty:SchemeSkuPropertyRequest
}
"""培训属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 10:22
"""
input SchemeSkuPropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.scheme.SchemeSkuPropertyRequest") {
	"""年度"""
	year:[String]
	"""地区"""
	regionSkuPropertySearch:RegionSkuPropertySearchRequest12
	"""行业"""
	industry:[String]
	"""科目类型"""
	subjectType:[String]
	"""培训类别"""
	trainingCategory:[String]
	"""培训专业"""
	trainingProfessional:[String]
	"""技术等级"""
	technicalGrade:[String]
	"""岗位类别"""
	positionCategory:[String]
	"""培训对象"""
	trainingObject:[String]
	"""技术等级"""
	jobLevel:[String]
	"""工种"""
	jobCategory:[String]
	"""科目"""
	subject:[String]
	"""年级"""
	grade:[String]
	"""学段"""
	learningPhase:[String]
	"""学科"""
	discipline:[String]
}
"""@version: 1.0
	@description: 对接管理系统
	@author: sugs
	@create: 2022-11-15 11:27
"""
input ConnectManageSystemRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.user.ConnectManageSystemRequest") {
	"""同步状态
		0 未同步
		1 已同步
		2 同步失败
	"""
	syncStatus:Int
}
"""数据分析信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/20 15:14
"""
input DataAnalysisRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.user.DataAnalysisRequest") {
	"""成果配置可获得学时"""
	trainingResultPeriod:DoubleScopeRequest
	"""考核要求学时"""
	requirePeriod:DoubleScopeRequest
	"""已获得总学时"""
	acquiredPeriod:DoubleScopeRequest
}
"""学员学习报名信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:08
"""
input LearningRegisterRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.user.LearningRegisterRequest") {
	"""报名方式
		<p>
		1:学员自主报名
		2:集体报名
		3:管理员导入
	"""
	registerType:Int
	"""报名来源类型(ORDER：订单 SUB_ORDER：子订单 EXCHANGE_ORDER：换货单)"""
	sourceType:String
	"""报名来源ID"""
	sourceId:String
	"""学员状态(1:正常 2：冻结 3：失效)"""
	status:[Int]
	"""报名时间"""
	registerTime:DateScopeRequest
	"""来源订单号"""
	orderNoList:[String]
	"""来源子订单号"""
	subOrderNoList:[String]
	"""来源批次单号"""
	batchOrderNoList:[String]
}
"""地区模型
	<AUTHOR>
	@version 1.0
	@date 2022/2/27 20:01
"""
input RegionRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.user.RegionRequest") {
	"""地区：省"""
	province:String
	"""地区：市"""
	city:String
	"""地区：区"""
	county:String
}
"""用户属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:01
"""
input UserPropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.user.UserPropertyRequest") {
	"""所属地区路径"""
	regionList:[RegionRequest]
	"""工作单位名称"""
	companyName:String
	"""下单地区"""
	payOrderRegionList:[RegionRequest]
}
"""用户信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:00
"""
input UserRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.report.user.UserRequest") {
	"""用户id"""
	userIdList:[String]
	"""账户id"""
	accountIdList:[String]
	"""用户属性"""
	userProperty:UserPropertyRequest
}
input ProductDiscountApplyRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.productdiscountapply.ProductDiscountApplyRequest") {
	"""网校id"""
	onlineSchoolId:String
	"""分销商商品名称"""
	saleTitle:String
	"""培训方案名称"""
	schemeName:String
	"""培训方案类型"""
	schemeTypeList:[String]
	"""商品sku属性(后端自用)"""
	propertyList:[PropertyRequest]
	"""商品sku属性查询"""
	skuPropertyRequest:SkuPropertyRequest
	"""分销商id集合"""
	distributorIdList:[String]
	"""分销商等级"""
	distributorLevel:Int
	"""优惠申请审批状态
		0-待处理 1-通过 2-未通过
	"""
	auditStatusList:[Int]
	"""优惠状态
		1-开启 2-关闭
		与优惠申请内容状态有关、与优惠周期约束、优惠开始时间、优惠结束时间有关
	"""
	discountStatusList:[Int]
	"""分销商品状态
		0-开启 1-关闭
	"""
	distributionStatus:Int
	"""商品上架状态
		0-下架 1-上架
	"""
	shelveStatus:Int
	"""销售渠道id"""
	saleChannelId:String
}
"""业务员对接的分销商关系"""
input DockingSalesmanRelationshipRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.salesman.DockingSalesmanRelationshipRequest") {
	"""业务员ID"""
	salesmanId:String
	"""网校名称"""
	onlineSchoolName:String
	"""网校域名"""
	onlineSchoolDomainName:String
	"""分销商名称"""
	distributorName:String
	"""来源合同状态 0-有效 1-终止"""
	status:Int
	"""来源合同的分销地区路径(/省/市/区)list"""
	regionList:[String]
	"""对接开始时间排序方式
		0-倒序 1-正序
	"""
	dockingStartTimeSortType:Int
}
input SalesmanRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.salesman.SalesmanRequest") {
	"""业务员ID"""
	salesmanId:String
	"""姓名"""
	name:String
	"""手机号"""
	phoneNumber:String
	"""状态：1-启用，2-禁用"""
	status:String
}
"""交易统计请求参数
	<AUTHOR>
"""
input StatisticTradeRecordRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.statistic.StatisticTradeRecordRequest") {
	"""分销商id集合"""
	distributorIdList:[String]
	"""供应商id集合"""
	supplierIdList:[String]
	"""商品名称 模糊匹配"""
	commodityName:String
	"""网校id集合 List"""
	onlineSchoolList:[String]
	"""商品sku属性"""
	propertyList:[PropertyRequest]
	"""商品ID集合"""
	commodityIdList:[String]
	"""排除商品ID集合"""
	excludeCommodityIdList:[String]
	"""商品售价范围"""
	commodityPriceScope:DoubleScopeRequest
	"""查询时间范围"""
	queryDateScope:DateScopeRequest
	"""查询方式
		仅查询自己
		只查询下级分销商
		包含自己和下级分销商
		@see QueryWayType
	"""
	queryWayType:QueryWayType
	"""门户id"""
	portalId:String
	"""是否是推广门户的数据 | true.是 false.否"""
	isPortalData:Boolean
	jobName:String
	metaData:ObsFileMetaData
}
"""@version: 1.0
	@description: 分销关系排序参数
	@author: sugs
	@create: 2023-12-28 19:29
"""
input DistributionRelationshipSortRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.sort.DistributionRelationshipSortRequest") {
	"""排序字段"""
	field:DistributorRelationSortEnum
	"""正序或倒序"""
	policy:Direction
}
input DistributorCommoditySortRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.sort.DistributorCommoditySortRequest") {
	"""排序字段"""
	field:DistributorCommoditySortEnum
	"""正序或倒序"""
	policy:Direction
}
input ProductDiscountApplySortRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.sort.ProductDiscountApplySortRequest") {
	"""排序字段"""
	field:ProductDiscountApplySortEnum
	"""正序或倒序"""
	policy:Direction
}
input SaleCommoditySortRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.sort.SaleCommoditySortRequest") {
	"""排序字段"""
	field:SaleCommoditySortEnum
	"""正序或倒序"""
	policy:Direction
}
enum Direction @type(value:"com.fjhb.commons.dao.page.SortPolicy$Direction") {
	ASC
	DESC
}
enum DistributorCommoditySortEnum @type(value:"com.fjhb.ms.datapedestal.kernel.enums.DistributorCommoditySortEnum") {
	RECORD_UPDATED_TIME
	RECORD_CREATED_TIME
	DISTRIBUTION_END_TIME
	SALE_CHANNEL_SORT
}
enum SaleCommoditySortEnum @type(value:"com.fjhb.ms.datapedestal.kernel.enums.SaleCommoditySortEnum") {
	SALE_CHANNEL_SORT
	ISSUE_NUM_SORT
}
type ChannelInfoDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.ChannelInfoDto") {
	purchaseChannel:Int
	payTypeStatisticResponseList:[PaymentInfoDto]
}
type PaymentInfoDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.PaymentInfoDto") {
	payType:Int
	statisticInfo:ReportNumStatisticDto
}
enum QueryType @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.QueryType") {
	AND
	OR
}
type ReportNumStatisticDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.ReportNumStatisticDto") {
	tradeSuccessCount:Long
	returnCount:Long
	exchangeInCount:Long
	exchangeOutCount:Long
	netTradeSuccessCount:Long
}
type ServicerAttachResponse1 @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.nested.ServicerAttachResponse") {
	fileName:String
	filePath:String
}
enum BatchOrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.nested.BatchOrderSortField") {
	BATCH_ORDER_UN_CONFIRMED_TIME
	BATCH_ORDER_COMMIT_TIME
}
enum BatchReturnOrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.nested.BatchReturnOrderSortField") {
	CREATED_TIME
}
enum SortPolicy @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.SortPolicy") {
	ASC
	DESC
}
enum OrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderSortField") {
	ORDER_NORMAL_TIME
	ORDER_COMPLETED_TIME
}
enum ReturnOrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderSortField") {
	APPLIED_TIME
}
"""<AUTHOR> linq
	@date : 2025-04-15 15:03
	@description : 查询可更换的分销期别列表response返回值
"""
type DistributorIssueCommodityResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.distributorcommodity.DistributorIssueCommodityResponse") {
	"""期别商品信息"""
	distributorIssueCommodityInfoResponse:NestDistributorIssueCommodityInfoResponse
	"""期别资源信息"""
	issueResourceInfo:NestIssueResourceResponse
	"""剩余报名人数"""
	remainingRegisterNumber:Int!
}
"""<AUTHOR> linq
	@date : 2025-04-15 15:27
	@description : 购买渠道配置
"""
type NestCommodityPurchaseChannelConfigResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.distributorcommodity.nests.NestCommodityPurchaseChannelConfigResponse") {
	"""用户自主购买"""
	customerPurchase:NestPurchaseChannelConfigResponse
	"""集体缴费"""
	collectivePurchase:NestPurchaseChannelConfigResponse
	"""管理员导入"""
	administratorImport:NestPurchaseChannelConfigResponse
	"""集体报名个人缴费渠道"""
	collectiveSignUpPersonalPay:NestPurchaseChannelConfigResponse
}
"""<AUTHOR> linq
	@date : 2025-04-15 15:26
	@description : 分销期别商品信息
"""
type NestDistributorIssueCommodityInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.distributorcommodity.nests.NestDistributorIssueCommodityInfoResponse") {
	"""商品所有渠道的配置信息"""
	commodityPurchaseChannelConfig:NestCommodityPurchaseChannelConfigResponse
}
"""<AUTHOR> linq
	@date : 2025-04-15 15:11
	@description : 冗余期别资源信息
"""
type NestIssueResourceResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.distributorcommodity.nests.NestIssueResourceResponse") {
	"""期别id"""
	issueId:String
	"""期别编号"""
	issueNum:String
	"""期别名称"""
	issueName:String
	"""期别报名开始时间"""
	issueSignUpBeginDate:DateTime
	"""期别报名结束时间"""
	issueSignUpEndDate:DateTime
	"""期别培训开始时间"""
	issueTrainingBeginDate:DateTime
	"""期别培训结束时间"""
	issueTrainingEndDate:DateTime
}
"""<AUTHOR> linq
	@date : 2025-04-15 15:26
	@description : 购买渠道配置
"""
type NestPurchaseChannelConfigResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.distributorcommodity.nests.NestPurchaseChannelConfigResponse") {
	"""是否开启可见"""
	couldSee:Boolean
	"""是否开启可购买"""
	couldBuy:Boolean
}
"""<AUTHOR> linq
	@date : 2024-08-22 11:01
	@description：推广门户线下集体报名配置
"""
type OfflineRegistrationConfigResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.portal.OfflineRegistrationConfigResponse") {
	"""推广门户线下集体报名入口开关"""
	openEntrySwitch:Boolean!
}
enum DistributorRelationSortEnum @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.enums.DistributorRelationSortEnum") {
	"""合同签订时间"""
	RELATION_CREATE_TIME
	"""分销关系开始时间"""
	RELATION_START_TIME
	"""分销关系结束时间"""
	RELATION_END_TIME
}
enum ProductDiscountApplySortEnum @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.enums.ProductDiscountApplySortEnum") {
	"""申请时间"""
	APPLY_TIME
	"""申请通过时间"""
	PASS_TIME
	"""申请拒绝时间"""
	REFUSE_TIME
}
"""@author: yxw
	@date: 2024/1/18
	@time: 15:01
	@description：
"""
enum QueryWayType @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.constants.QueryWayType") {
	"""仅查询自己"""
	ONLY_ME
	"""只查询下级分销商"""
	ONLY_MY_DISTRIBUTOR
	"""包含自己和下级分销商"""
	ALL
}
"""排序参数
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum SortPolicy1 @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.constants.SortPolicy") {
	"""正序"""
	ASC
	"""倒序"""
	DESC
}
"""学员方案学习可用于排序的字段
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum StudentSchemeLearningSortField @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.constants.StudentSchemeLearningSortField") {
	"""报名时间"""
	REGISTER_TIME
	"""方案年度"""
	SCHEME_YEAR
}
"""附件"""
type AttachesResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.AttachesResponse") {
	"""id"""
	id:String
	"""附件名称"""
	fileName:String
	"""附件路径"""
	filePath:String
}
"""<AUTHOR> linq
	@date : 2024-09-13 15:33
	@description：授权商品商品属性
"""
type CommodityPropertyInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.CommodityPropertyInfoResponse") {
	"""分销商商品上下架状态
		0-已下架 1-已上架
	"""
	shelveStatus:Int
}
"""<AUTHOR> linq
	@date : 2024-09-13 15:32
	@description：授权商品方案属性
"""
type CommoditySchemeResourceResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.CommoditySchemeResourceResponse") {
	"""培训方案类型"""
	schemeType:String
}
"""商品sku属性
	<AUTHOR>
"""
type CommoditySkuPropertyResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.CommoditySkuPropertyResponse") {
	"""商品sku属性id"""
	commoditySkuPropertyId:String
	"""商品sku属性名"""
	commoditySkuPropertyName:String
	"""商品sku属性值集合"""
	commoditySkuPropertyValueList:[CommoditySkuPropertyValueResponse]
}
"""商品sku属性值
	<AUTHOR>
"""
type CommoditySkuPropertyValueResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.CommoditySkuPropertyValueResponse") {
	"""商品sku属性值id"""
	commoditySkuPropertyValueId:String
	"""商品sku属性值展示名"""
	commoditySkuPropertyValueName:String
	"""父级code"""
	parentCode:String
}
"""商品sku地区属性节点
	<AUTHOR>
"""
type CommoditySkuRegionPropertyNodeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.CommoditySkuRegionPropertyNodeResponse") {
	"""商品sku地区属性id"""
	commoditySkuRegionId:String
	"""地区编码"""
	code:String
	"""地区名"""
	name:String
	"""地区等级"""
	level:Int
	"""地区路径"""
	codePath:String
	"""父级地区编码"""
	parentCode:String
	"""下级商品sku地区属性"""
	subRegionList:[CommoditySkuRegionPropertyNodeResponse]
}
"""商品sku培训专业属性节点
	<AUTHOR>
"""
type CommoditySkuTrainingProfessionPropertyNodeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.CommoditySkuTrainingProfessionPropertyNodeResponse") {
	"""培训专业ID"""
	id:String
	"""培训专业名称"""
	name:String
	"""培训专业编码"""
	code:String
	"""父级专业ID"""
	parentCode:String
	"""排序序号"""
	sort:Int
	"""是否启用"""
	isAvailable:Int
	"""下级培训专业sku地区属性"""
	subTrainingProfessionList:[CommoditySkuTrainingProfessionPropertyNodeResponse]
}
"""上下文信息"""
type OwnerInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.OwnerInfoResponse") {
	"""平台id"""
	platformId:String
	"""平台版本id"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位id"""
	unitId:String
	"""服务商id"""
	servicer:String
}
type PortalInDistributorResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.PortalInDistributorResponse") {
	"""门户id"""
	id:String
	"""门户简称"""
	shortName:String
}
type PropertyResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.PropertyResponse") {
	"""商品skuKey"""
	propertyKey:String
	"""商品skuKeyName"""
	propertyKeyName:String
	"""商品skuValue"""
	propertyValue:String
	"""商品skuValue名称"""
	propertyValueName:String
}
type RegionResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.RegionResponse") {
	"""地区ID"""
	regionId:String
	"""地区路径"""
	regionPath:String
	"""省ID"""
	provideId:String
	"""市ID"""
	cityId:String
	"""区县ID"""
	countyId:String
	"""名称"""
	name:String
	"""下级地区"""
	children:[RegionResponse]
}
"""地区统计"""
type RegionStatisticsResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.RegionStatisticsResponse") {
	"""地区信息"""
	response:RegionResponse
	"""统计数量"""
	total:Int
}
type RegionTreeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.RegionTreeResponse") {
	"""地区ID"""
	regionId:String
	"""名称"""
	name:String
	"""地区全路径"""
	regionPath:String
	"""子地区"""
	children:[RegionTreeResponse]
	"""子地区数量"""
	childrenCount:Int!
}
"""商品-培训方案资源类型"""
type SchemeResourceInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.SchemeResourceInfoResponse") {
	"""培训方案id"""
	schemeId:String
	"""培训方案类型"""
	schemeType:String
	"""培训方案名称"""
	schemeName:String
	"""学时"""
	period:String
	"""报名开始时间"""
	registerBeginDate:DateTime
	"""报名结束时间"""
	registerEndDate:DateTime
	"""上架状态 1-上架 2-下架"""
	sellStatus:Int
	"""培训开始时间"""
	trainingBeginDate:DateTime
	"""培训结束时间"""
	trainingEndDate:DateTime
}
type DistributionAuthorizationDistributorResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributionrelationship.DistributionAuthorizationDistributorResponse") {
	"""分销商ID"""
	distributorId:String
	"""分销商类型 1-个人 2-企业"""
	distributorType:Int
	"""分销商名字"""
	distributorName:String
	"""统一社会信用代码"""
	unifiedSocialCreditCode:String
	"""身份证号"""
	idCard:String
	"""合作状态
		0 未开始
		1 进行中
		2 即将到期
		3 已结束
	"""
	cooperateStatus:Int
	"""合同id"""
	contractId:String
	"""合同类型
		1 供销
		2 分销
	"""
	contractType:Int
	"""合同开始时间"""
	relationStartTime:DateTime
	"""合同结束时间"""
	relationEndTime:DateTime
	"""期限类型 1-周期 2-长期"""
	durationType:Int
	"""分销状态 0-停用 1-正常"""
	distributionStatus:Int
	"""分销地市"""
	regionStatistics:[RegionTreeResponse]
	"""分销级别 1-一级 2-二级"""
	distributionLevel:Int
	"""是否已授权 1- 授权 2-未授权"""
	isAuthorized:Int
	"""上级分销商ID"""
	superDistributorId:String
	"""上级分销商是否已授权"""
	superDistributorIsAuthorized:Int
	"""上级分销商合同"""
	superDistributorContractId:String
	"""上级分销商详情"""
	superDistributorInfo:DistributorServicerReponse
	"""上级分销地市"""
	superRegionStatistics:[RegionStatisticsResponse]
}
type DistributionContractDetailResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributionrelationship.DistributionContractDetailResponse") {
	"""分销商名称"""
	distributorName:String
	"""上级分销商名称"""
	superiorDistributorName:String
	"""分销地市"""
	regionStatistics:[RegionTreeResponse]
	"""合同ID"""
	productConsignmentContractId:String
	"""供应商ID"""
	supplierId:String
	"""分销商ID"""
	distributorId:String
	"""上级分销商ID"""
	superiorDistributorId:String
	"""分销级别：1-一级，2-二级"""
	distributionLevel:Int
	"""合同开始时间"""
	contractStartTime:DateTime
	"""合同结束时间"""
	contractEndTime:DateTime
	"""合同状态：0-有效，1-终止"""
	status:Int
	"""期限类型：1-周期，2-长期"""
	contractDurationType:Int
	"""签订时间"""
	signTime:DateTime
	"""分销网校ID"""
	distributorServicerIdList:[String]
	"""分销地区路径"""
	regionList:[String]
}
type DistributionContractResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributionrelationship.DistributionContractResponse") {
	"""合同ID"""
	productConsignmentContractId:String
	"""供应商ID"""
	supplierId:String
	"""分销商ID"""
	distributorId:String
	"""上级分销商ID"""
	superiorDistributorId:String
	"""分销级别：1-一级，2-二级"""
	distributionLevel:Int
	"""合同开始时间"""
	contractStartTime:DateTime
	"""合同结束时间"""
	contractEndTime:DateTime
	"""合同状态：0-有效，1-终止"""
	status:Int
	"""期限类型：1-周期，2-长期"""
	contractDurationType:Int
	"""签订时间"""
	signTime:DateTime
	"""分销网校ID"""
	distributorServicerIdList:[String]
	"""分销地区路径"""
	regionList:[String]
}
type DistributionRelationshipResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributionrelationship.DistributionRelationshipResponse") {
	"""分销商ID"""
	distributorId:String
	"""分销商名称"""
	distributorName:String
	"""分销商类型 1-个人 2-企业"""
	distributorType:Int
	"""分销商创建时间"""
	createTime:DateTime
	"""超管账户"""
	adminAccount:String
	"""管理员名称"""
	adminName:String
	"""管理员手机号"""
	adminPhone:String
	"""证件号"""
	idCard:String
	"""统一社会信用代码"""
	code:String
}
"""分销商服务商
	<AUTHOR>
"""
type DistributorServicerReponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributionrelationship.DistributorServicerReponse") {
	"""服务商id"""
	id:String
	"""所属平台id"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目id"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商名称"""
	servicerName:String
	"""服务商名称简称"""
	servicerShortName:String
	"""隶属企业单位路径"""
	attachToEnterpriseUnitIdPath:String
	"""隶属企业帐户ID"""
	accountId:String
	"""统一社会信用代码"""
	code:String
	"""服务商类型 1培训机构、2课件供应商、3渠道商、4参训单位、5合同供应商、6供应商、7分销商"""
	servicerType:Int
	"""证件类型"""
	idCardType:Int
	"""证件号"""
	idCard:String
	"""合作伙伴类型（供应商、分销商类型）1-个人 2-企业"""
	partnerType:Int
	"""服务商创建时间"""
	createdTime:DateTime
	"""附件列表"""
	attachList:[ServicerAttachResponse1]
}
type OnlineSchoolDistributionRelationshipResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributionrelationship.OnlineSchoolDistributionRelationshipResponse") {
	"""分销关系来源ID（合同ID）"""
	sourceId:String
	"""分销商名称"""
	distributerName:String
	"""分销商级别"""
	distributerLevel:Int
	"""合作伙伴类型（供应商、分销商类型）1-个人 2-企业"""
	partnerType:Int
	"""分销类型 1-周期 2-长期"""
	durationType:Int
	"""分销开始时间"""
	distributionStartTime:DateTime
	"""分销结束时间"""
	distributionEndTime:DateTime
	"""分销关系来源类型，1代表供销合同，2代表分销合同"""
	sourceType:Int
	"""分销地市"""
	regionStatistics:[RegionTreeResponse]
	"""上级分销商信息"""
	superDistributor:DistributorResponse
	"""业务员ID"""
	salemanId:String
	"""业务员名称"""
	salemanName:String
	"""业务员手机号"""
	phoneNumber:String
	"""分销关系状态，0代表有效，1代表无效"""
	status:Int
}
"""@version: 1.0
	@description: 分销商信息
	@author: sugs
	@create: 2023-12-26 20:54
"""
type DistributorResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.DistributorResponse") {
	"""分销商ID"""
	distributorId:String
	"""分销商名称"""
	distributorName:String
	"""分销商类型 1-个人 2-企业"""
	distributorType:Int
	"""分销商简称"""
	distributorShortName:String
	"""分销商全称"""
	distributorFullName:String
	"""统一社会信用代码"""
	unifiedSocialCreditCode:String
	"""身份证号"""
	idCard:String
}
type PolicyPortalResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.PolicyPortalResponse") {
	"""id"""
	id:String
	"""门户标题"""
	title:String
	"""门户简称"""
	shortName:String
	"""域名"""
	domainName:String
	"""网校状态
		1-正常  2-失效
	"""
	onlineSchoolStatus:Int
	"""门户状态 是否开启"""
	isPublished:Boolean
	"""渠道id"""
	channel:String
	"""门户推广 (0-不推广, 1-推广）"""
	portalPromotion:Int
}
type PortalInfoDetailResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.PortalInfoDetailResponse") {
	"""id"""
	id:String
	"""门户标识"""
	identifier:String
	"""门户类别"""
	category:Int
	"""门户类型"""
	portalType:Int
	"""门户标题"""
	title:String
	"""门户简称"""
	shortName:String
	"""域名"""
	domainName:String
	"""域名短码"""
	domainShortCode:String
	"""域名h5"""
	domainNameh5:String
	"""h5门户id"""
	portalIdh5:String
	"""h5域名短码"""
	domainShortCodeh5:String
	"""网校状态
		1-正常  2-失效
	"""
	onlineSchoolStatus:Int
	"""客服电话号码"""
	CSPhone:String
	"""咨询时间"""
	CSCallTime:String
	"""底部内容"""
	footContent:String
	"""门户logo"""
	logo:String
	"""浏览器图标"""
	icon:String
	"""客服电话图片"""
	CSPhonePicture:String
	"""友情链接"""
	friendLinks:[FriendLinkInfo]
	"""web端轮播图"""
	bannersInWeb:[BannerInfo]
	"""移动端轮播图"""
	bannersInh5:[BannerInfo]
	"""是否开启线下报名入口"""
	isOfflineEnabled:Boolean
	"""创建时间"""
	createTime:DateTime
	"""线下集体报名id"""
	offlineId:String
	"""渠道id"""
	channelId:String
	"""门户简介说明内容"""
	content:String
	"""域名类型（系统默认域名 1 自有域名 2)"""
	domainNameType:Int
}
type PortalInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.PortalInfoResponse") {
	"""id"""
	id:String
	"""门户标识"""
	identifier:String
	"""门户类别"""
	category:Int
	"""门户类型"""
	portalType:Int
	"""门户标题"""
	title:String
	"""门户简称"""
	shortName:String
	"""域名"""
	domainName:String
	"""域名短码"""
	domainShortCode:String
	"""h5域名"""
	domainNameh5:String
	"""h5域名短码"""
	domainShortCodeh5:String
	"""网校状态
		1-正常  2-失效
	"""
	onlineSchoolStatus:Int
	"""门户状态   是否已发布"""
	isPublished:Boolean
	"""创建时间"""
	createTime:DateTime
	"""当前门户的销售渠道id"""
	channelId:String
	"""所属服务商id (例如网校id)"""
	belongServiceId:String
	"""域名类型（系统默认域名 1 自有域名 2)"""
	domainNameType:Int
}
type QRCode1 @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.nested.QRCode") {
	"""二维码图片"""
	mobileQrcode:String
	"""二维码操作提示"""
	qrcodeTip:String
}
"""附件"""
type ServicerAttachResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.nested.ServicerAttachResponse") {
	"""文件名"""
	fileName:String
	"""文件路径"""
	filePath:String
}
type BannerInfo @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.BannerInfo") {
	"""轮播图id"""
	id:String
	"""轮播图名称"""
	name:String
	"""轮播图路径"""
	path:String
	"""轮播图链接"""
	link:String
	"""创建时间"""
	createdTime:DateTime
	"""轮播图排序"""
	sort:Int
}
"""分销商推广门户"""
type DistributorPortalInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.DistributorPortalInfoResponse") {
	"""门户id"""
	id:String
	"""所属平台id"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目id"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""业务平台服务商id  (例如分销商id)"""
	servicerId:String
	"""所属服务商id (例如网校id)"""
	belongServiceId:String
	"""0-主门户 1-子门户"""
	category:Int
	"""门户标识"""
	identifier:String
	"""1-web端 2-移动端"""
	portalType:Int
	"""门户标题"""
	title:String
	"""门户简称"""
	shortName:String
	"""门户logo"""
	logo:String
	"""浏览器图标"""
	icon:String
	"""主题颜色"""
	themeColor:String
	"""移动二维码"""
	mobileQRcode:String
	"""移动二维码来源标识 1-系统生成 2-自定义"""
	mobileQRCodeSign:Int
	"""客服电话图片"""
	CSPhonePicture:String
	"""客服电话"""
	CSPhone:String
	"""客服咨询时间"""
	CSCallTime:String
	"""在线客服代码内容id"""
	CSOnlineCodeId:String
	"""培训流程图片"""
	trainingFlowPicture:String
	"""底部内容"""
	footContent:String
	"""宣传口号"""
	slogan:String
	"""域名"""
	domainName:String
	"""域名短码"""
	domainShortCode:String
	"""H5域名(请求Web端门户信息才会使用这个字段 如果请求的是H5端的门户信息，该字段为null)"""
	domainNameH5:String
	"""h5门户id"""
	portalIdh5:String
	"""域名短码h5"""
	domainShortCodeh5:String
	"""门户简介说明内容"""
	content:String
	"""是否提供服务号"""
	isProvideServiceAccount:Boolean
	"""门户状态  是否已发布"""
	isPublished:Boolean
	"""网校状态
		1-正常  2-失效
	"""
	onlineSchoolStatus:Int
	"""网校模式 1-正式实施 2-DEMO"""
	onlineSchoolModes:Int!
	"""是否到期"""
	isExpired:Boolean
	"""前端模板id"""
	portalTemplateId:String
	"""企业客服微信"""
	CSWechat:String
	"""海报二维码"""
	billQRCodes:[QRCode]
	"""门户发布时间"""
	publishedTime:DateTime
	"""门户取消发布时间"""
	unPublishedTime:DateTime
	"""信息"""
	cnzz:String
	"""目录名"""
	dirName:String
	"""域名类型（系统默认域名 1 自有域名 2）"""
	domainNameType:Int
	"""门户板块集合"""
	plates:[PortalPlateResponse]
	"""友情链接集合"""
	friendLinks:[PortalFriendLinkResponse]
	"""轮播图"""
	banner:[PortalBannerResponse]
	"""移动端轮播图"""
	bannersInh5:[PortalBannerResponse]
	"""门户菜单"""
	menu:[PortalMenuResponse]
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""是否删除"""
	isDeleted:Int
	"""删除时间"""
	deletedTime:DateTime
	"""记录创建时间"""
	recordCreatedTime:DateTime
	"""记录更新时间"""
	recordUpdatedTime:DateTime
	"""销售渠道id"""
	channelId:String
	"""当前分销商单位名称"""
	distributorUnitName:String
}
"""友情链接信息
	<AUTHOR>
"""
type FriendLinkInfo @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.FriendLinkInfo") {
	"""id"""
	id:String
	"""所属门户id"""
	portalId:String
	"""标题"""
	title:String
	"""图片"""
	picture:String
	"""友情链接类型"""
	friendLinkType:Int
	"""链接"""
	link:String
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
}
"""详细资讯信息"""
type NewsDetailResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.NewsDetailResponse") {
	"""资讯编号"""
	newId:String
	"""平台id"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商id"""
	serviceId:String
	"""分类id"""
	necId:String
	"""标题"""
	title:String
	"""摘要"""
	summary:String
	"""内容"""
	content:String
	"""封面图片路径"""
	coverPath:String
	"""来源"""
	source:String
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""是否置顶"""
	isTop:Boolean!
	"""发布地区编码"""
	areaCodePath:String
	"""资讯状态 0 草稿 1正常"""
	status:Int!
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""浏览数量"""
	reviewCount:Int!
	"""弹窗起始时间"""
	popupBeginTime:DateTime
	"""弹窗截止时间"""
	popupEndTime:DateTime
	"""专题ID"""
	specialSubjectId:String
	"""门户简称"""
	shortName:String
	"""门户标识"""
	identifier:String
}
"""简略资讯信息"""
type NewsSimpleResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.NewsSimpleResponse") {
	"""门户id"""
	portalId:String
	"""门户展示名称"""
	showName:String
	"""门户简称"""
	shortName:String
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""是否置顶"""
	isTop:Boolean!
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""摘要"""
	summary:String
	"""分类名称"""
	name:String
	"""资讯状态 0 草稿 1正常"""
	status:Int!
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""发布地区编码"""
	areaCodePath:String
	"""专题Id"""
	specialSubjectId:String
}
"""门户轮播图"""
type PortalBannerResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.PortalBannerResponse") {
	"""轮播图id"""
	id:String
	"""轮播图名称"""
	name:String
	"""图片路径"""
	path:String
	"""链接地址"""
	link:String
	"""排序"""
	sort:Int
	"""是否启用"""
	enable:Boolean
	"""创建时间"""
	createdTime:DateTime
}
"""门户友情链接"""
type PortalFriendLinkResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.PortalFriendLinkResponse") {
	id:String
	title:String
	picture:String
	friendLinkType:Int
	link:String
	sort:Int
	createdTime:DateTime
}
"""门户菜单"""
type PortalMenuResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.PortalMenuResponse") {
	"""栏目id"""
	id:String
	"""门户栏目名称"""
	name:String
	"""门户栏目展示名称"""
	displayName:String
	"""父栏目id"""
	parentId:String
	"""门户栏目类型 1-菜单 2-资讯"""
	type:Int
	"""来源类型 1-内置 2-用户创建"""
	sourceType:Int
	"""链接"""
	link:String
	"""业务code"""
	code:String
	"""引用id"""
	referenceId:String
	"""是否可用"""
	enable:Boolean
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
}
type PortalPlateResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.PortalPlateResponse") {
	"""板块id"""
	id:String
	"""门户板块名称"""
	name:String
	"""门户板块展示名称"""
	displayName:String
	"""父板块id"""
	parentId:String
	"""门户板块类型 1-默认 2-资讯"""
	type:Int
	"""来源类型 1-内置 2-用户创建"""
	sourceType:Int
	"""链接"""
	link:String
	"""业务code"""
	code:String
	"""引用id"""
	referenceId:String
	"""是否允许存在子级"""
	allowChildren:Int
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
}
type PosterConfigurationResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.PosterConfigurationResponse") {
	id:String
	portalId:String
	identifier:String
	title:String
	promotionalCopy:String
	templateId:String
	csPhone:String
	qrcodeList:[QRCode1]
	createdTime:DateTime
	updatedTime:DateTime
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	serviceId:String
}
"""<AUTHOR> linq
	@date : 2024-09-18 14:04
	@description：二维码
"""
type QRCode @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.QRCode") {
	"""二维码图片"""
	mobileQrcode:String
	"""二维码操作提示"""
	qrcodeTip:String
}
"""<AUTHOR>
	对接管理系统
	@date 2022/11/15 14:40
"""
type ConnectManageSystemResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.ConnectManageSystemResponse") {
	"""同步状态
		0 未同步
		1 已同步
		2 同步失败
	"""
	syncStatus:Int
	"""同步信息"""
	syncMessage:String
}
type ExtendedInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.ExtendedInfoResponse") {
	"""是否打印
		true 打印 false 未打印
	"""
	whetherToPrint:Boolean
	"""打印时间"""
	printTime:DateTime
	"""pdf证书地址"""
	pdfUrl:String
}
"""学员学习报名信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:08
"""
type LearningRegisterResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.LearningRegisterResponse") {
	"""报名方式
		<p>
		1:学员自主报名
		2:集体报名
		3:管理员导入
	"""
	registerType:Int
	"""报名来源类型
		Order: 订单
	"""
	sourceType:String
	"""报名来源ID"""
	sourceId:String
	"""学员状态
		<p>
		1:正常
		2:冻结
		3:失效
	"""
	status:Int
	"""学员状态最后变更时间"""
	statusChangeTime:DateTime
	"""报名时间"""
	registerTime:DateTime
	"""来源订单号"""
	orderNo:String
	"""来源子订单号"""
	subOrderNo:String
	"""来源批次单号"""
	batchOrderNo:String
	"""失效来源类型"""
	frozenAndInvalidSourceType:String
	"""失效来源id"""
	frozenAndInvalidSourceId:String
}
"""学员方案学习主题模型
	<AUTHOR>
	@version 1.0
	@date 2021/12/14 11:41
"""
type StudentSchemeLearningResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.StudentSchemeLearningResponse") {
	"""参训资格ID"""
	qualificationId:String
	"""学号id"""
	studentNo:String
	"""数据归属信息"""
	owner:OwnerResponse
	"""学员信息"""
	student:UserResponse
	"""学员学习报名信息"""
	learningRegister:LearningRegisterResponse
	"""培训方案信息"""
	scheme:SchemeResponse
	"""学员学习信息"""
	studentLearning:StudentLearningResponse
	"""数据分析信息"""
	dataAnalysis:DataAnalysisResponse
	"""对接管理系统"""
	connectManageSystem:ConnectManageSystemResponse
	"""扩展信息"""
	extendedInfo:ExtendedInfoResponse
}
"""课程学习方式学习信息
	<AUTHOR>
	@version 1.0
	true:@date 2022/1/15 14:08
"""
type CourseLearningResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.learning.CourseLearningResponse") {
	"""整体课程学习状态
		<p>
		0:未学习
		1:学习中
		2:学习完成
	"""
	courseScheduleStatus:Int
	"""整体课程完成学习时间"""
	courseQualifiedTime:DateTime
	"""已选课程数"""
	selectedCourseCount:Int
	"""已选课总学时"""
	selectedCoursePeriod:Double
	"""学习方式id"""
	learningId:String
	"""学习方式类型
		<p>
		1: 选课学习方式
		2: 考试学习方式
		3: 自主学习课程学习方式
	"""
	learningType:Int
	"""是否启用"""
	enabled:Boolean
	"""学习资源类型
		<p>
		1: 课程
		2: 考试
	"""
	learningResourceType:Int
	"""学习资源ID
		<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
	"""
	learningResourceId:String
	"""课程学习方式下只提供课程学习方式考核指标结果json结构
		如果是在考试学习方式则只提供考核指标结果
		扩展方案配置json课程或考试学习方式考核指标部分的json结构
		ownerType枚举值
		1：学习方案
		2：期别
		3：期数
		4：学习方式
		5：课程大纲
		json结构如：
		[{
		assessId: 考核指标id,
		ownerType: 拥有者类型,
		ownerId: 拥有者id如方案id学习方式id等,
		name: 考核要求模板如Scheme_Assess_001,
		必修考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		},
		选修考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		}
		},{
		...(数组结构，允许有过个考核要求配置)
		}]
	"""
	userAssessResult:[String]
	"""学员培训成果"""
	learningResult:[LearningResultResponse]
}
"""数据分析信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/20 15:14
"""
type DataAnalysisResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.learning.DataAnalysisResponse") {
	"""成果配置可获得学时"""
	trainingResultPeriod:Double
	"""考核要求学时"""
	requirePeriod:Double
	"""已获得总学时"""
	acquiredPeriod:Double
}
"""考试学习方式学习信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 14:09
"""
type ExamLearningResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.learning.ExamLearningResponse") {
	"""是否已考试"""
	committedExam:Boolean
	"""考试考核计算结果
		-1：未考核
		1：合格
		0：不合格
	"""
	examAssessResult:Int
	"""考试合格时间"""
	examQualifiedTime:DateTime
	"""考试次数"""
	examCount:Int
	"""最高成绩"""
	maxExamScore:Double
	"""学习方式id"""
	learningId:String
	"""学习方式类型
		<p>
		1: 选课学习方式
		2: 考试学习方式
		3: 自主学习课程学习方式
	"""
	learningType:Int
	"""是否启用"""
	enabled:Boolean
	"""学习资源类型
		<p>
		1: 课程
		2: 考试
	"""
	learningResourceType:Int
	"""学习资源ID
		<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
	"""
	learningResourceId:String
	"""课程学习方式下只提供课程学习方式考核指标结果json结构
		如果是在考试学习方式则只提供考核指标结果
		扩展方案配置json课程或考试学习方式考核指标部分的json结构
		ownerType枚举值
		1：学习方案
		2：期别
		3：期数
		4：学习方式
		5：课程大纲
		json结构如：
		[{
		assessId: 考核指标id,
		ownerType: 拥有者类型,
		ownerId: 拥有者id如方案id学习方式id等,
		name: 考核要求模板如Scheme_Assess_001,
		必修考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		},
		选修考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		}
		},{
		...(数组结构，允许有过个考核要求配置)
		}]
	"""
	userAssessResult:[String]
	"""学员培训成果"""
	learningResult:[LearningResultResponse]
}
"""考试学习方式学习信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 14:09
"""
type LearningExperienceLearningResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.learning.LearningExperienceLearningResponse") {
	"""是否已提交学习心得"""
	committedLearningExperience:Boolean
	"""学习心得考核计算结果
		-1：未考核
		1：合格
		0：不合格
	"""
	learningExperienceAssessResult:Int
	"""学习心得合格时间"""
	learningExperienceQualifiedTime:DateTime
	"""最高成绩"""
	maxLearningExperienceScore:Double
	"""心得通过数量  没有考核时使用这个字段"""
	learningExperiencePassCount:Long
	"""学习方式id"""
	learningId:String
	"""学习方式类型
		<p>
		1: 选课学习方式
		2: 考试学习方式
		3: 自主学习课程学习方式
	"""
	learningType:Int
	"""是否启用"""
	enabled:Boolean
	"""学习资源类型
		<p>
		1: 课程
		2: 考试
	"""
	learningResourceType:Int
	"""学习资源ID
		<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
	"""
	learningResourceId:String
	"""课程学习方式下只提供课程学习方式考核指标结果json结构
		如果是在考试学习方式则只提供考核指标结果
		扩展方案配置json课程或考试学习方式考核指标部分的json结构
		ownerType枚举值
		1：学习方案
		2：期别
		3：期数
		4：学习方式
		5：课程大纲
		json结构如：
		[{
		assessId: 考核指标id,
		ownerType: 拥有者类型,
		ownerId: 拥有者id如方案id学习方式id等,
		name: 考核要求模板如Scheme_Assess_001,
		必修考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		},
		选修考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		}
		},{
		...(数组结构，允许有过个考核要求配置)
		}]
	"""
	userAssessResult:[String]
	"""学员培训成果"""
	learningResult:[LearningResultResponse]
}
"""学员学习信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
type StudentLearningResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.learning.StudentLearningResponse") {
	"""培训结果
		<p>
		-1:未知，培训尚未完成
		1:培训合格
		0:培训不合格
	"""
	trainingResult:Int
	"""取得培训结果时间"""
	trainingResultTime:DateTime
	"""课程学习方式学习信息"""
	courseLearning:CourseLearningResponse
	"""考试学习方式学习信息"""
	examLearning:ExamLearningResponse
	"""学习心得学习方式学习信息"""
	learningExperienceLearning:LearningExperienceLearningResponse
	"""方案考核指标结果，json结构（只提供方案部分配置的考核要求）
		扩展方案配置json考核指标部分的json结构
		ownerType枚举值
		1：学习方案
		2：期别
		3：期数
		4：学习方式
		5：课程大纲
		json结构如：
		[{
		assessId: 考核指标id,
		ownerType: 拥有者类型,
		ownerId: 拥有者id如方案id学习方式id等,
		name: 考核要求模板如Scheme_Assess_001,
		方案考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		}
		},{
		...(数组结构，允许有过个考核要求配置)
		}]
	"""
	userAssessResult:[String]
	"""学员培训成果"""
	learningResult:[LearningResultResponse]
}
"""证书型型培训成果配置
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 14:30
"""
type CertificateLearningConfigResultResponse implements LearningResultConfigResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.learning.result.CertificateLearningConfigResultResponse") {
	"""证书模板ID"""
	certificateTemplateId:String
	"""是否开放打印"""
	openPrintTemplate:Boolean!
	"""成果类型
		<p>
		1：分数型学习成果
		2：证书型学习成果
	"""
	resultType:Int
}
"""分数型培训成果配置
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 14:30
"""
type GradeLearningConfigResultResponse implements LearningResultConfigResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.learning.result.GradeLearningConfigResultResponse") {
	"""分数类型
		<p>说明：查询时可以根据相同的分数类型进行累加分数</p>
		<p>
		CREDIT: 学分
	"""
	gradeType:String
	"""分数"""
	grade:Double
	"""成果类型
		<p>
		1：分数型学习成果
		2：证书型学习成果
	"""
	resultType:Int
}
"""培训成果配置
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 14:30
"""
interface LearningResultConfigResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.learning.result.LearningResultConfigResponse") {
	"""成果类型
		<p>
		1：分数型学习成果
		2：证书型学习成果
	"""
	resultType:Int
}
"""学员培训成果
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 14:28
"""
type LearningResultResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.learning.result.LearningResultResponse") {
	"""用户成果id"""
	learningResultId:String
	"""取得时间"""
	gainedTime:DateTime
	"""培训成果配置"""
	learningResultConfig:LearningResultConfigResponse
}
"""培训方案信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
type SchemeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.scheme.SchemeResponse") {
	"""培训方案id"""
	schemeId:String
	"""培训方案类型
		<p>
		chooseCourseLearning: 选课规则
		autonomousCourseLearning: 自主选课
	"""
	schemeType:String
	"""培训属性"""
	skuProperty:SchemeSkuPropertyResponse
	"""培训属性"""
	schemeName:String
	"""学习成果"""
	learningResult:[LearningResultConfigResponse]
}
"""培训属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 10:22
"""
type SchemeSkuPropertyResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.scheme.SchemeSkuPropertyResponse") {
	"""年度"""
	year:SchemeSkuPropertyValueResponse
	"""地区: 省"""
	province:SchemeSkuPropertyValueResponse
	"""地区: 市"""
	city:SchemeSkuPropertyValueResponse
	"""地区: 区县"""
	county:SchemeSkuPropertyValueResponse
	"""行业"""
	industry:SchemeSkuPropertyValueResponse
	"""科目类型"""
	subjectType:SchemeSkuPropertyValueResponse
	"""培训类别"""
	trainingCategory:SchemeSkuPropertyValueResponse
	"""培训专业"""
	trainingProfessional:SchemeSkuPropertyValueResponse
	"""技术等级"""
	technicalGrade:SchemeSkuPropertyValueResponse
	"""岗位类别"""
	positionCategory:SchemeSkuPropertyValueResponse
	"""培训对象"""
	trainingObject:SchemeSkuPropertyValueResponse
	"""技术等级"""
	jobLevel:SchemeSkuPropertyValueResponse
	"""工种"""
	jobCategory:SchemeSkuPropertyValueResponse
	"""科目"""
	subject:SchemeSkuPropertyValueResponse
	"""年级"""
	grade:SchemeSkuPropertyValueResponse
	"""学段"""
	learningPhase:SchemeSkuPropertyValueResponse
	"""学科"""
	discipline:SchemeSkuPropertyValueResponse
}
"""培训属性值
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 10:22
"""
type SchemeSkuPropertyValueResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.report.scheme.SchemeSkuPropertyValueResponse") {
	"""sku属性值id"""
	skuPropertyValueId:String
}
type DistributorCommodityAndRelationResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.DistributorCommodityAndRelationResponse") {
	"""指导价格"""
	suggestedPrice:BigDecimal
	"""上级分销商商品价格"""
	superiorDistributorPrice:BigDecimal
	"""产品分销授权是否展示
		0-不展示 1-展示
	"""
	relationIsShow:Boolean
	"""来源代销合同信息"""
	productConsignmentContract:ProductConsignmentContractResponse
	"""定价方案信息"""
	productPricingSchemeResponse:ProductPricingSchemeResponse
	"""当前商品网校信息"""
	onlineSchoolInfo:OnlineSchoolInfoResponse
	"""分销商商品id"""
	distributorCommodityId:String
	"""分销商商品销售名称"""
	saleTitle:String
	"""商品-培训方案资源类型"""
	schemeResourceInfo:SchemeResourceInfoResponse
	"""分销商信息"""
	distributor:DistributorResponse
	"""分销商商品sku属性"""
	propertyList:[PropertyResponse]
	"""授权价格类型 1-固定 2-区间"""
	priceType:Int
	"""分销商商品价格"""
	price:BigDecimal
	"""最大价格"""
	maxPrice:BigDecimal
	"""最小价格"""
	minPrice:BigDecimal
	"""网校商品来源系统上下文信息"""
	sourceOwnerInfo:OwnerInfoResponse
	"""原始商品id"""
	commodityId:String
	"""分销商商品图片路径"""
	commodityPicturePath:String
	"""分销状态
		0-开启 1-关闭
		商品的分销开始时间、结束时间作为判断
	"""
	distributionStatus:Int
	"""分销商商品销售时段类型
		1-周期 2-长期
	"""
	saleTimeType:Int
	"""分销商商品销售开始时间"""
	saleStartTime:DateTime
	"""分销商商品销售结束时间"""
	saleEndTime:DateTime
	"""销售状态 1-有效 2-无效"""
	saleStatus:Int
	"""分销商品来源类型
		1-产品分销授权
	"""
	commoditySourceType:Int
	"""分销商品来源id
		1-产品分销授权id
	"""
	commoditySourceId:String
	"""分销商品创建时间"""
	commodityCreatedTime:DateTime
	"""分销商品更新时间"""
	commodityUpdatedTime:DateTime
	"""商品是否门户可见"""
	isShow:Boolean
	"""定价方案启用个数"""
	productPricingSchemeEnableNum:Long
	"""培训方案期别数(面网授班级才有值)"""
	issueNum:Int
	"""分销商品上下架状态"""
	shelveStatus:Int
}
"""<AUTHOR> linq
	@date : 2024-09-13 15:15
	@description：分销商商品商品属性集合
"""
type DistributorCommodityPropertyCollectionResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.DistributorCommodityPropertyCollectionResponse") {
	"""授权商品商品属性"""
	commodityPropertyInfoList:[CommodityPropertyInfoResponse]
	"""授权商品方案属性"""
	commoditySchemeResourceList:[CommoditySchemeResourceResponse]
	"""商品销售地区属性树集合（商品授权地区）"""
	commodityAuthorizedRegionTreeList:[CommoditySkuRegionPropertyNodeResponse]
	"""商品sku属性集合"""
	commoditySkuPropertyList:[CommoditySkuPropertyResponse]
	"""商品sku地区属性树集合"""
	commoditySkuPropertyRegionTreeList:[CommoditySkuRegionPropertyNodeResponse]
	"""商品sku培训专业属性树集合"""
	commoditySkuPropertyTrainingProfessionalTreeList:[CommoditySkuTrainingProfessionPropertyNodeResponse]
	"""商品销售地区属性树集合（定价方案地区）"""
	commodityRegionTreeList:[CommoditySkuRegionPropertyNodeResponse]
}
type DistributorCommodityResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.DistributorCommodityResponse") {
	"""指导价格"""
	suggestedPrice:BigDecimal
	"""上级分销商商品价格"""
	superiorDistributorPrice:BigDecimal
	"""分销商商品id"""
	distributorCommodityId:String
	"""分销商商品销售名称"""
	saleTitle:String
	"""商品-培训方案资源类型"""
	schemeResourceInfo:SchemeResourceInfoResponse
	"""分销商信息"""
	distributor:DistributorResponse
	"""分销商商品sku属性"""
	propertyList:[PropertyResponse]
	"""授权价格类型 1-固定 2-区间"""
	priceType:Int
	"""分销商商品价格"""
	price:BigDecimal
	"""最大价格"""
	maxPrice:BigDecimal
	"""最小价格"""
	minPrice:BigDecimal
	"""网校商品来源系统上下文信息"""
	sourceOwnerInfo:OwnerInfoResponse
	"""原始商品id"""
	commodityId:String
	"""分销商商品图片路径"""
	commodityPicturePath:String
	"""分销状态
		0-开启 1-关闭
		商品的分销开始时间、结束时间作为判断
	"""
	distributionStatus:Int
	"""分销商商品销售时段类型
		1-周期 2-长期
	"""
	saleTimeType:Int
	"""分销商商品销售开始时间"""
	saleStartTime:DateTime
	"""分销商商品销售结束时间"""
	saleEndTime:DateTime
	"""销售状态 1-有效 2-无效"""
	saleStatus:Int
	"""分销商品来源类型
		1-产品分销授权
	"""
	commoditySourceType:Int
	"""分销商品来源id
		1-产品分销授权id
	"""
	commoditySourceId:String
	"""分销商品创建时间"""
	commodityCreatedTime:DateTime
	"""分销商品更新时间"""
	commodityUpdatedTime:DateTime
	"""商品是否门户可见"""
	isShow:Boolean
	"""定价方案启用个数"""
	productPricingSchemeEnableNum:Long
	"""培训方案期别数(面网授班级才有值)"""
	issueNum:Int
	"""分销商品上下架状态"""
	shelveStatus:Int
}
"""<AUTHOR> linq
	@date : 2024-05-06 14:04
	@description：分销商品sku属性
"""
type DistributorCommoditySkuPropertyCollectionResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.DistributorCommoditySkuPropertyCollectionResponse") {
	"""商品sku属性集合"""
	commoditySkuPropertyList:[CommoditySkuPropertyResponse]
	"""商品sku地区属性树集合"""
	commoditySkuPropertyRegionTreeList:[CommoditySkuRegionPropertyNodeResponse]
	"""商品sku培训专业属性树集合"""
	commoditySkuPropertyTrainingProfessionalTreeList:[CommoditySkuTrainingProfessionPropertyNodeResponse]
	"""商品销售地区属性树集合（定价方案地区）"""
	commodityRegionTreeList:[CommoditySkuRegionPropertyNodeResponse]
}
"""分销商商品统计结果"""
type DistributorCommodityStatisticResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.DistributorCommodityStatisticResponse") {
	"""统计类型
		1-我的分销 2-我的下级分销
	"""
	statisticType:Int
	"""统计数量"""
	distributorCommodityNumber:Long!
}
"""推广门户线下集体报名配置返回值"""
type PortalOfflineCollectiveSignUpSettingResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.PortalOfflineCollectiveSignUpSettingResponse") {
	"""推广门户线下集体报名配置信息"""
	id:String
	"""推广门户线下集体报名入口开关"""
	openEntrySwitch:Boolean!
	"""推广门户线下集体报名入口图片附件"""
	entryPictureAttachments:[Attachment]
	"""线下集体报名名称"""
	name:String
	"""推广门户线下集体报名模板地址"""
	templateAttachment:Attachment
	"""访问链接"""
	accessUrl:String
	"""底部文本说明内容id"""
	bottomDescriptionId:String
	"""底部文本说明内容"""
	bottomDescription:String
	"""报名步骤信息"""
	signUpSteps:[SignUpStepDto]
	"""创建时间"""
	createdTime:DateTime
}
"""<AUTHOR> linq
	@date : 2024-09-12 19:36
	@description：推广门户定价方案数量response
"""
type PricingPolicyCommodityCountResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.PricingPolicyCommodityCountResponse") {
	"""推广门户id"""
	portalId:String
	"""定价方案商品数量"""
	commodityCount:Int!
}
"""@Description 附件请求
	<AUTHOR>
	@Date 2024/3/20 9:56
"""
type Attachment @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.Attachment") {
	"""附件名称"""
	name:String
	"""附件地址"""
	url:String
}
"""<AUTHOR> linq
	@date : 2024-09-23 11:28
	@description：网校相关信息
"""
type OnlineSchoolInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.OnlineSchoolInfoResponse") {
	"""网校名称"""
	schoolName:String
	"""业主单位全称"""
	unitName:String
	"""业主单位简称"""
	unitShotName:String
}
"""<AUTHOR> linq
	@date : 2024-09-18 17:21
	@description：定价方案推广门户信息
"""
type PricingPolicyPortalInfo @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.PricingPolicyPortalInfo") {
	"""推广门户标识id"""
	portalIdentify:String
	"""推广门户销售渠道id"""
	saleChannelId:String
	"""推广门户渠道排序"""
	portalSort:Int
	"""门户展示 (0-不展示, 1-展示）"""
	showPortal:Int
	"""门户推广 (0-不推广, 1-推广）"""
	portalPromotion:Int
	"""推广门户定价方案商品id"""
	portalPricingPolicyCommodityId:String
}
type ProductConsignmentContractResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.ProductConsignmentContractResponse") {
	"""代销合同id"""
	productConsignmentContractId:String
	"""代销合同类型
		1-供销合同 2-分销合同
	"""
	contractType:Int
	"""分销商ID"""
	distributorId:String
	"""供应商id"""
	supplierId:String
	"""分销商简称"""
	distributorShortName:String
	"""分销商全称"""
	distributorFullName:String
	"""分销级别 1-一级 2-二级"""
	distributionLevel:Int
	"""期限类型 1-周期 2-长期"""
	durationType:Int
	"""合作状态
		0 未开始
		1 进行中
		2 即将到期
		3 已结束
	"""
	cooperateStatus:Int
	"""合同开始时间"""
	contractStartTime:DateTime
	"""合同结束时间"""
	contractEndTime:DateTime
	"""分销地市"""
	regionStatistics:[RegionStatisticsResponse]
	"""上级分销商ID"""
	superDistributorId:String
	"""上级分销商合同"""
	superDistributorContractId:String
	"""上级分销商名称"""
	superDistributorName:String
	"""上级分销商简称"""
	superDistributorShortName:String
	"""统一社会信用代码"""
	superUnifiedSocialCreditCode:String
	"""身份证号"""
	superIdCard:String
	"""上级分销地市"""
	superRegionStatistics:[RegionStatisticsResponse]
	"""地区树结构"""
	regionTreeList:[RegionTreeResponse]
	"""地区树ID"""
	contractDistributionRegionTreeId:String
}
type ProductPricingSchemeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.ProductPricingSchemeResponse") {
	"""定价方案ID"""
	productPricingSchemeId:String
	"""产品分销授权ID"""
	productDistributionAuthId:String
	"""授权商品id"""
	productId:String
	"""价格"""
	price:BigDecimal
	"""启用时间"""
	enableTime:DateTime
	"""禁用时间"""
	disEnableTime:DateTime
	"""授权价格类型 1-固定 2-区间"""
	priceType:Int
	"""最大价格"""
	maxPrice:BigDecimal
	"""最小价格"""
	minPrice:BigDecimal
	"""产品销售范围"""
	saleScopeList:[SaleScopeResponse]
	"""定价方案状态 (0-禁用, 1-启用)"""
	status:Int
	"""加入的推广门户信息"""
	pricingPolicyPortalInfoList:[PricingPolicyPortalInfo]
}
type RegionItemResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.RegionItemResponse") {
	"""销售范围ID+地区ID 或 合同ID+地区ID（主键）"""
	id:String
	"""地区来源类型 (1-销售地区, 2-分销地区)"""
	sourceType:Int
	"""地区来源ID（销售范围ID/合同ID）"""
	sourceId:String
	"""地区ID"""
	regionId:String
	"""地区路径"""
	regionPath:String
	"""省ID"""
	provinceId:String
	"""市ID"""
	cityId:String
	"""县ID"""
	countyId:String
	"""层级"""
	level:Int
	"""记录创建时间"""
	recordCreatedTime:DateTime
}
"""市粒度统计"""
type RegionSaleScopeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.RegionSaleScopeResponse") {
	"""地区来源类型 (1-销售地区, 2-分销地区)"""
	sourceType:Int
	"""地区来源ID（销售范围ID/合同ID）"""
	sourceId:String
	"""地区ID"""
	regionId:String
	"""地区名称"""
	regionName:String
	"""统计数量(区数量)"""
	total:Int
}
"""@see SaleScopeDto"""
type SaleScopeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.SaleScopeResponse") {
	"""销售范围ID"""
	saleScopeId:String
	"""地区销售范围类型 (0-同分销商品, 1-自定义)"""
	regionSaleScopeType:Int
	"""地区销售范围关联ID（销售范围ID/合同ID）"""
	regionSaleScopeRelationId:String
	"""地区销售范围关联地区树id"""
	regionSaleRegionTreeId:String
	"""单位销售范围类型 (1-不限, 2-自定义)"""
	unitSaleScopeType:Int
	"""是否启用"""
	enable:Int
	"""备注"""
	remark:String
	"""记录创建时间"""
	recordCreatedTime:DateTime
	"""产品地区销售范围"""
	regionSaleScopeList:[RegionSaleScopeResponse]
	"""地区树结构"""
	regionTreeList:[RegionTreeResponse]
	"""销售地区明细"""
	regionItemList:[RegionItemResponse]
	"""产品单位销售范围"""
	unitSaleScopeList:[UnitSaleScopeResponse]
}
type SignUpStepDto @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.SignUpStepDto") {
	"""步骤内容"""
	content:String
	"""序号"""
	index:Int!
	"""步骤标题"""
	title:String
	"""步骤内容 Id"""
	contentId:String
}
type UnitSaleScopeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.UnitSaleScopeResponse") {
	"""销售范围ID+统一社会信用代码（主键）"""
	id:String
	"""销售范围ID"""
	saleScopeId:String
	"""单位名称"""
	unitName:String
	"""统一社会信用代码"""
	creditCode:String
	"""记录创建时间"""
	recordCreatedTime:DateTime
}
type CommodityDiscountApplyResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.productdiscountapply.CommodityDiscountApplyResponse") {
	"""优惠申请id"""
	applyId:String
	"""上下文信息"""
	ownerInfo:OwnerInfoResponse
	"""申请人id"""
	applyUserId:String
	"""申请方id"""
	applicantId:String
	"""申请原因"""
	applyReason:String
	"""审批人id"""
	auditUserId:String
	"""审批人名字"""
	auditUserName:String
	"""审批方id"""
	auditPartyId:String
	"""审批信息"""
	auditMessage:String
	"""审批状态
		1-待处理 2-通过 3-不通过 4-取消
	"""
	auditStatus:Int!
	"""申请附件"""
	applyAttaches:[AttachesResponse]
	"""优惠申请时间"""
	applyTime:DateTime
	"""优惠通过时间"""
	passTime:DateTime
	"""优惠拒绝时间"""
	refuseTime:DateTime
	"""优惠内容"""
	productDiscountApplyContent:CommodityDiscountApplyContentResponse
}
type CommodityDiscountApplyContentResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.productdiscountapply.nested.CommodityDiscountApplyContentResponse") {
	"""产品分销授权关系id"""
	productDistributionAuthId:String
	"""允许报名人数数量约束 | 1-不限 2-区间"""
	quantityConstraint:Int
	"""允许报名人数最小值"""
	discountMinQuantity:Int
	"""允许报名人数最大值"""
	discountMaxQuantity:Int
	"""剩余可报名人数"""
	remainQuantity:Int
	"""已报名人数"""
	signedNumber:Int
	"""优惠周期约束 | 1-周期 2-长期"""
	discountDateConstraint:Int
	"""优惠开始时间"""
	discountStartTime:DateTime
	"""优惠结束时间"""
	discountEndTime:DateTime
	"""优惠价格"""
	discountPrice:BigDecimal
	"""优惠状态
		1-开启 2-关闭
		与优惠申请内容状态有关、与优惠周期约束、优惠开始时间、优惠结束时间有关
	"""
	discountStatus:Int
	"""优惠地区树id"""
	discountRegionTreeId:String
	"""优惠地市"""
	regionStatistics:[RegionTreeResponse]
	"""优惠商品信息"""
	discountCommodity:DiscountCommodityResponse
}
type DiscountCommodityResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.productdiscountapply.nested.DiscountCommodityResponse") {
	"""分销商商品id"""
	distributorCommodityId:String
	"""商品id"""
	commodityId:String
	"""分销商商品销售名称"""
	saleTitle:String
	"""分销商商品价格"""
	price:BigDecimal
	"""授权价格类型 1-固定 2-区间"""
	priceType:Int
	"""最大价格"""
	maxPrice:BigDecimal
	"""最小价格"""
	minPrice:BigDecimal
	"""分销商商品图片路径"""
	commodityPicturePath:String
	"""商品-培训方案资源类型"""
	schemeResourceInfo:SchemeResourceInfoResponse
	"""分销商商品sku属性"""
	propertyList:[PropertyResponse]
	"""分销商品来源类型
		1-产品分销授权
	"""
	commoditySourceType:Int
	"""分销商品来源id
		1-产品分销授权id
	"""
	commoditySourceId:String
	"""当前分销商ID"""
	distributorId:String
	"""当前分销商名称"""
	distributorName:String
	"""当前分销级别 1-一级 2-二级"""
	distributionLevel:Int
	"""当前分销商类型"""
	distributionType:Int
	"""当前分销商单位名称"""
	distributorUnitName:String
	"""商品授权状态
		0-停用 1-正常
	"""
	distributionStatus:Int
	"""上级分销商ID"""
	superDistributorId:String
	"""上级分销商全称"""
	superDistributorFullName:String
	"""上级分销商简称"""
	superDistributorShortName:String
	"""期别数"""
	issueNum:Long
}
"""<AUTHOR> linq
	@date : 2024-09-23 19:07
	@description：地区数据响应体
"""
type RegionDataResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.regiontree.RegionDataResponse") {
	"""地区树id"""
	regionTreeId:String
	"""地区code"""
	regionCode:String
	"""父级地区code"""
	parentRegionCode:String
	"""地区路径"""
	regionPath:String
	"""省id"""
	provinceId:String
	"""市id"""
	cityId:String
	"""县id"""
	countyId:String
	"""层级 （1-省级 2-市级 3-区县级）"""
	level:Int
}
"""业务员对接的分销商关系"""
type DockingSalesmanRelationshipResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.salesman.DockingSalesmanRelationshipResponse") {
	"""业务员ID"""
	salesmanId:String
	"""对接开始时间"""
	dockingStartTime:DateTime
	"""对接结束时间"""
	dockingEndTime:DateTime
	"""来源合同的分销商id"""
	distributorId:String
	"""分销商名称"""
	distributorName:String
	"""分销商身份证"""
	distributorIdCard:String
	"""分销商电话"""
	distributorPhone:String
	"""分销商统一社会信用代码（企业类型）"""
	distributorCreditCode:String
	"""来源合同分销商合作伙伴类型（供应商、分销商类型）1-个人 2-企业"""
	distributorPartnerType:Int
	"""分销合同状态 0-有效 1-终止"""
	status:Int
	"""分销地区"""
	distributorRegionList:[RegionResponse]
	"""合同id"""
	contractId:String
}
type SalesmanResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.salesman.SalesmanResponse") {
	"""业务员ID"""
	salesmanId:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""服务商ID"""
	servicerId:String
	"""单位ID"""
	unitId:String
	"""姓名"""
	name:String
	"""手机号"""
	phoneNumber:String
	"""状态：1-启用，2-禁用"""
	status:String
	"""备注"""
	remark:String
	"""所属供应商ID"""
	supplierId:String
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
}
"""功能描述 : 分销服务商信息"""
type DistributionServicerResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.servicer.DistributionServicerResponse") {
	"""服务商 Id"""
	servicerId:String
	"""统一社会信用社代码"""
	code:String
	"""服务商名称"""
	distributorName:String
	"""附件"""
	attach:[ServicerAttachResponse]
	"""分销商域名"""
	domainInfos:[DomainInfo]
	"""管理账户id"""
	adminAccountId:String
	"""管理员账户名"""
	adminAccountName:String
	"""管理员名称"""
	adminName:String
	"""管理员手机号"""
	adminPhone:String
	"""身份证idcard"""
	idCard:String
	"""服务商类型 1-个人 2- 企业"""
	partnerType:Int
}
"""分销商域名范围类"""
type DomainInfo @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.servicer.nested.DomainInfo") {
	"""域名"""
	domainName:String
	"""客户端类型 1-web 2-h5"""
	clientType:Int
	"""域名类型 |1-华博域名 2-业主自由域名"""
	domainNameType:Int
}
"""商品分销商开通统计返回
	<AUTHOR>
"""
type CommodityDistributorOpenReportInSupplierResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.CommodityDistributorOpenReportInSupplierResponse") {
	"""商品ID"""
	commodityId:String
	"""商品名称"""
	commodityName:String
	"""商品所属网校ID"""
	servicerId:String
	"""商品所属网校名"""
	servicerName:String
	"""分销商商品sku属性"""
	propertyList:[PropertyResponse]
	"""商品价格"""
	price:Double
	"""供应商ID"""
	supplierId:String
	"""分销商ID"""
	distributorId:String
	"""分销商名称"""
	distributorName:String
	"""分销等级"""
	distributionLevel:Int
	"""分销定价方式冗余统计信息"""
	distributionPricingInfo:[DistributionPricingResponse]
}
"""商品分销商开通统计返回
	<AUTHOR>
"""
type CommodityDistributorOpenReportResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.CommodityDistributorOpenReportResponse") {
	"""商品ID"""
	commodityId:String
	"""商品名称"""
	commodityName:String
	"""商品所属网校ID"""
	servicerId:String
	"""商品所属网校名"""
	servicerName:String
	"""分销商商品sku属性"""
	propertyList:[PropertyResponse]
	"""商品价格"""
	price:Double
	"""供应商ID"""
	supplierId:String
	"""商品各授权分销商的销售统计"""
	distributorOpenReportList:[DistributorOpenReportResponse]
}
"""商品开通统计返回
	<AUTHOR>
"""
type CommodityOpenReportResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.CommodityOpenReportResponse") {
	"""商品ID"""
	commodityId:String
	"""商品名称"""
	commodityName:String
	"""商品所属网校ID"""
	servicerId:String
	"""授权供应商id"""
	supplierId:String
	"""授权供应商名称"""
	supplierName:String
	"""商品所属网校名"""
	servicerName:String
	"""分销商商品sku属性"""
	propertyList:[PropertyResponse]
	"""商品价格"""
	price:Double
	"""商品合计统计"""
	statisticSummary:ReportNumStatisticResponse
	"""商品购买渠道数据"""
	purchaseNList:[PurchaseStatisticResponse]
}
"""分销商销售统计
	包含各渠道的统计
	<AUTHOR>
"""
type DistributorSellStatisticIncludedPurchaseResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.DistributorSellStatisticIncludedPurchaseResponse") {
	"""分销商ID"""
	distributorId:String
	"""分销商名称"""
	distributorName:String
	"""分销商简称"""
	distributorShortName:String
	"""分销商销售合计信息"""
	distributorSellSummary:DistributionSellStatisticResponse
	"""分销商销售渠道统计信息"""
	distributionSellPurchaseStatisticList:[DistributionSellPurchaseStatisticResponse]
}
"""交易统计信息
	<AUTHOR>
"""
type TradeStatisticResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.TradeStatisticResponse") {
	"""交易完成总数量"""
	totalTradeCompleteNum:Long
	"""退班总数量"""
	totalReturnCompleteNum:Long
	"""换出总数量"""
	totalExchangeOutNum:Long
	"""换入总数量"""
	totalExchangeInNum:Long
	"""总金额"""
	totalAmount:BigDecimal
	"""净交易成功数量"""
	netTradeSuccessCount:Long
	"""净成交金额"""
	netTradeSuccessAmount:BigDecimal
}
"""交易合计统计信息
	<AUTHOR>
"""
type TradeSummaryStatisticResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.TradeSummaryStatisticResponse") {
	"""合计类型 | 1、合计 2、个人缴费-线上支付 3、集体缴费-线上支付 4、集体缴费线下支付 5、导入开通-线下支付"""
	summaryType:Int
	"""合计统计信息"""
	summaryInfo:TradeSummaryInfoResponse
}
"""分销定价冗余模型"""
type DistributionPricingResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.nested.DistributionPricingResponse") {
	"""交易净成交金额"""
	tradeNetAmount:BigDecimal
	"""分销定价方式 | 0、授权定价 1、优惠申请"""
	pricingType:Int
	"""缴费渠道统计信息"""
	useSpecialChannelInfos:[ChannelInfoDto]
	"""开通统计合计"""
	discountSummary:ReportNumStatisticDto
}
"""分销销售支付方式统计信息
	<AUTHOR>
"""
type DistributionSellPayTypeStatisticResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.nested.DistributionSellPayTypeStatisticResponse") {
	"""支付方式
		@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderTypes
	"""
	payType:Int
	"""统计信息"""
	statisticInfo:DistributionSellStatisticResponse
}
"""分销销售缴费渠道统计信息
	<AUTHOR>
"""
type DistributionSellPurchaseStatisticResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.nested.DistributionSellPurchaseStatisticResponse") {
	"""购买渠道
		1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
	"""
	purchaseChannel:Int
	"""各支付方式统计信息"""
	payTypeStatisticResponseList:[DistributionSellPayTypeStatisticResponse]
}
"""分销销售统计信息
	<AUTHOR>
"""
type DistributionSellStatisticResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.nested.DistributionSellStatisticResponse") {
	"""交易成功数量"""
	tradeSuccessCount:Long
	"""退货数量"""
	returnCount:Long
	"""净交易成功数量"""
	netTradeSuccessCount:Long
	"""净成交金额"""
	netTradeSuccessAmount:BigDecimal
}
"""分销商开通统计结果
	<AUTHOR>
"""
type DistributorOpenReportResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.nested.DistributorOpenReportResponse") {
	"""分销商ID"""
	distributorId:String
	"""分销商名称"""
	distributorName:String
	"""分销商等级 | 1-一级，2-二级"""
	distributionLevel:Int
	"""分销定价方式冗余统计信息"""
	distributionPricingInfo:[DistributionPricingResponse]
}
"""支付方式统计信息
	<AUTHOR>
"""
type PayTypeStatisticResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.nested.PayTypeStatisticResponse") {
	"""支付方式
		@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderTypes
	"""
	payType:Int
	"""统计信息"""
	statisticInfo:ReportNumStatisticResponse
}
"""缴费渠道统计信息
	<AUTHOR>
"""
type PurchaseStatisticResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.nested.PurchaseStatisticResponse") {
	"""购买渠道
		1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
	"""
	purchaseChannel:Int
	"""各支付方式统计信息"""
	payTypeStatisticResponseList:[PayTypeStatisticResponse]
}
"""交易统计情况
	<AUTHOR>
"""
type ReportNumStatisticResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.nested.ReportNumStatisticResponse") {
	"""交易成功数量"""
	tradeSuccessCount:Long
	"""退货数量"""
	returnCount:Long
	"""换入数量"""
	exchangeInCount:Long
	"""换出数量"""
	exchangeOutCount:Long
	"""净交易成功数量"""
	netTradeSuccessCount:Long
}
"""合计"""
type TradeSummaryInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.statistic.nested.TradeSummaryInfoResponse") {
	"""交易完成总数量"""
	totalTradeCompleteNum:Long
	"""退班总数量"""
	totalReturnCompleteNum:Long
	"""换出总数量"""
	totalExchangeOutNum:Long
	"""换入总数量"""
	totalExchangeInNum:Long
	"""净交易成功数量"""
	totalTradeCompleteNetNum:Long
}
"""所属集体缴费信息"""
type BatchOwnerResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.user.BatchOwnerResponse") {
	"""所属集体缴费单位id"""
	unitId:String
	"""所属集体缴费用户id"""
	userId:String
}
"""数据归属信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/8 17:29
"""
type OwnerResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.user.OwnerResponse") {
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位id"""
	unitId:String
	"""所属服务商类型
		<p>
		1：培训机构
		2：课件供应商
		3：渠道商
		4：参训单位
	"""
	servicerType:Int
	"""所属服务商id"""
	servicerId:String
	"""所属集体缴费信息"""
	batchOwner:BatchOwnerResponse
}
type RegionResponse1 @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.user.RegionResponse") {
	"""地区：省"""
	province:String
	"""地区：市"""
	city:String
	"""地区：区"""
	county:String
}
"""用户属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:01
"""
type UserPropertyResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.user.UserPropertyResponse") {
	"""所属地区"""
	region:RegionResponse1
	"""下单地区"""
	payOrderRegion:RegionResponse1
}
"""用户信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:00
"""
type UserResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.user.UserResponse") {
	"""用户id"""
	userId:String
	"""账户id"""
	accountId:String
	"""用户属性"""
	userProperty:UserPropertyResponse
}

scalar List
type DistributorPortalInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [DistributorPortalInfoResponse]}
type CommodityDistributorOpenReportResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CommodityDistributorOpenReportResponse]}
type CommodityDistributorOpenReportInSupplierResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CommodityDistributorOpenReportInSupplierResponse]}
type CommodityOpenReportResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CommodityOpenReportResponse]}
type DistributorCommodityAndRelationResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [DistributorCommodityAndRelationResponse]}
type DistributionAuthorizationDistributorResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [DistributionAuthorizationDistributorResponse]}
type DistributorIssueCommodityResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [DistributorIssueCommodityResponse]}
type DistributorSellStatisticIncludedPurchaseResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [DistributorSellStatisticIncludedPurchaseResponse]}
type DistributionRelationshipResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [DistributionRelationshipResponse]}
type DockingSalesmanRelationshipResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [DockingSalesmanRelationshipResponse]}
type OnlineSchoolDistributionRelationshipResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [OnlineSchoolDistributionRelationshipResponse]}
type CommodityDiscountApplyResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CommodityDiscountApplyResponse]}
type PortalInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [PortalInfoResponse]}
type SalesmanResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SalesmanResponse]}
type NewsSimpleResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [NewsSimpleResponse]}
type StudentSchemeLearningResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentSchemeLearningResponse]}
