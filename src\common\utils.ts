const NAV_LIST_NAME = 'navList'
const FX_NAV_LIST_NAME = 'fxNavList'
export const getRootRoute = (str = '') => {
  return str
    .replace(/^\//, '')
    .split('/')[0]
    .toLowerCase()
}

/**
 * 将系统中的 nav tab list 缓存到 localstorage 中。
 * @param list
 */
export const setLocalStorageNavList = (list: Array<any>) => {
  return localStorage.setItem(NAV_LIST_NAME, JSON.stringify(list))
}

export const setLocalStorageFxNavList = (list: Array<any>) => {
  return localStorage.setItem(FX_NAV_LIST_NAME, JSON.stringify(list))
}

/**
 * 获取 localstorage 中的 nav tab list
 * @returns {Array}
 */
export const getLocalStorageNavList = () => {
  const list = localStorage.getItem(NAV_LIST_NAME)
  return list !== 'undefined' && list ? JSON.parse(list) : []
}

export const getLocalStorageFxNavList = () => {
  const list = localStorage.getItem(FX_NAV_LIST_NAME)
  return list !== 'undefined' && list ? JSON.parse(list) : []
}

/**
 * 加载钉钉支持
 */
export const loadDingDing = () => {
  const head = document.querySelector('head')
  const body = document.querySelector('body')
  const script = document.createElement('script')
  const span = document.createElement('span')
  script.src = 'https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js'
  script.type = 'text/javascript'
  body?.appendChild(span)
  head?.appendChild(script)
}
