import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-studentcourselearning-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-studentcourselearning-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 课程评价凭证响应
<AUTHOR>
@since 2022/1/20
 */
export class CourseAppraisalTokenResponse {
  /**
   * 申请结果
   */
  applyResult: TokenResponse
  /**
   * 课程评价凭证
   */
  token: string
}

/**
 * 课程学习播放凭证响应
<AUTHOR>
@since 2022/1/20
 */
export class CourseLearningPlayTokenResponse {
  /**
   * 申请结果
   */
  applyResult: TokenResponse
  /**
   * 课程学习播放凭证
   */
  token: string
}

/**
 * 课后测验凭证响应
<AUTHOR>
@since 2022/1/20
 */
export class CourseQuizTokenResponse {
  /**
   * 申请结果
60001 查询不到课后测验配置
60002 课后测验已达标，不可进入
21001 试题不足，自动合格
60003 不大于最小学习进度
   */
  applyResult: TokenResponse
  /**
   * 课后测验凭证
   */
  token: string
  /**
   * 最小课程学习进度
   */
  minCourseSchedule: number
}

/**
 * 凭证响应基类
<AUTHOR>
@since 2022/1/20
 */
export class TokenResponse {
  /**
   * 代码：
200-成功
50001 - 已评价过，无法评价
50002-评价配置未启用
50003-学习进度未达标
   */
  code: string
  /**
   * 信息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请课程评价
   * @param studentCourseLearningToken 课程学习凭证
   * @return 课程评价凭证
   * @param mutate 查询 graphql 语法文档
   * @param studentCourseLearningToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCourseAppraisal(
    studentCourseLearningToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCourseAppraisal,
    operation?: string
  ): Promise<Response<CourseAppraisalTokenResponse>> {
    return commonRequestApi<CourseAppraisalTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { studentCourseLearningToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请课程学习播放
   * @param studentCourseLearningToken 课程学习凭证
   * @return 课程学习播放凭证
   * @param mutate 查询 graphql 语法文档
   * @param studentCourseLearningToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCourseLearningPlay(
    studentCourseLearningToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCourseLearningPlay,
    operation?: string
  ): Promise<Response<CourseLearningPlayTokenResponse>> {
    return commonRequestApi<CourseLearningPlayTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { studentCourseLearningToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请课后测验
   * 60001-查询不到课后测验配置信息，检查是否有配置课后测验
   * @param studentCourseLearningToken 课程学习凭证
   * @return 课后测验凭证
   * @param mutate 查询 graphql 语法文档
   * @param studentCourseLearningToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCourseQuizLearning(
    studentCourseLearningToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCourseQuizLearning,
    operation?: string
  ): Promise<Response<CourseQuizTokenResponse>> {
    return commonRequestApi<CourseQuizTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { studentCourseLearningToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 提交课程一键学习
   * @param courseImmediatelyLearningToken 学员课程一键学习token
   * @return 结果
   * @param mutate 查询 graphql 语法文档
   * @param courseImmediatelyLearningToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async commitImmediatelyCourseLearning(
    courseImmediatelyLearningToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.commitImmediatelyCourseLearning,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseImmediatelyLearningToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 提交课件一键学习
   * @param coursewareImmediatelyLearningToken 学员课件一键学习token
   * @return 结果
   * @param mutate 查询 graphql 语法文档
   * @param coursewareImmediatelyLearningToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async commitImmediatelyCoursewareLearning(
    coursewareImmediatelyLearningToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.commitImmediatelyCoursewareLearning,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { coursewareImmediatelyLearningToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
