<template>
  <el-drawer
    title="请选择打印方式"
    :visible.sync="visiable"
    size="600px"
    custom-class="m-drawer"
    :append-to-body="true"
    destroy-on-close
    @close="cancel"
  >
    <div class="drawer-bd">
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form ref="form" :model="printCertificationsVo" label-width="auto" class="m-form f-mt50">
            <el-form-item>
              <div slot="label">
                <span class="f-vm">文件类型</span>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                  <div slot="content">
                    <p>支持配置批量打印的类型：</p>
                    <p>1.连贯打印：本次批量打印的所有合格记录，是一份PDF，命名方式同导出文件名。</p>
                    <p>2.单个文件：每个学员对应的一条合格记录，是一份PDF，且以学员的姓名、证件号命名。</p>
                  </div>
                </el-tooltip>
                <span>：</span>
              </div>
              <el-radio-group v-model="printCertificationsVo.printType">
                <el-radio :label="1">连贯打印</el-radio>
                <el-radio :label="2">单个文件</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="printCertificationsVo.printType === 1">
              <div slot="label">
                <span class="f-vm">是否合并打印</span>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                  <div slot="content">
                    <p>
                      连贯打印模式下，如打印的证明尺寸为1/4
                      A4纸，如需四张证明合并在一张A4上进行打印，是否合并打印请选择“是”。
                    </p>
                    <p class="f-mt5">
                      <i class="f-co">注意：</i>如本次需要打印的证明尺寸超过1/2
                      A4，合并打印不生效，打印出来的文件仍然是一张证明一张A4纸。
                    </p>
                  </div>
                </el-tooltip>
                <span>：</span>
              </div>
              <el-radio-group v-model="printCertificationsVo.isMerge">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="cancel">取消</el-button>
              <el-button type="primary" @click="printModel" :loading="determineBtnLoading">确认</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { debounce, bind } from 'lodash-decorators'
  import PrintCertificationsVo from '@api/service/management/personal-leaning/mutation/vo/PrintCertificationsVo'
  import MutationBatchPrintTraining from '@api/service/management/personal-leaning/mutation/MutationBatchPrintTraining'
  import LearningArcjovesRequest from '@api/service/management/personal-leaning/query/vo/LearningArcjovesRequest'
  import { Response } from '@hbfe/common'

  @Component
  export default class extends Vue {
    // 是否显示抽屉控制
    visiable = false

    // 按钮加载状态
    determineBtnLoading = false
    // 批量打印证明实例
    batchPrintTrainingModule = new MutationBatchPrintTraining()
    // 数据
    printCertificationsVo = new PrintCertificationsVo()
    // 是否学员打印
    isStudentPrint = false
    // 查询参数
    studentParams = new LearningArcjovesRequest()
    // 展开抽屉
    showDrower(data: PrintCertificationsVo, studentParams: LearningArcjovesRequest, isStudent = false) {
      this.visiable = true
      this.isStudentPrint = isStudent
      this.studentParams = studentParams
      this.printCertificationsVo = new PrintCertificationsVo()
      Object.assign(this.printCertificationsVo, data, { isMerge: '1', printType: 1 })
    }
    // 打印模板
    @bind
    @debounce(200)
    async printModel() {
      try {
        this.determineBtnLoading = true
        let res = new Response()
        if (this.isStudentPrint) {
          res = await this.batchPrintTrainingModule.doBatchPrintStudentCertificates(
            this.printCertificationsVo,
            this.studentParams
          )
        } else {
          res = await this.batchPrintTrainingModule.doBatchPrintCertificates(
            this.printCertificationsVo,
            this.studentParams
          )
        }
        if (res.status.isSuccess()) {
          this.$emit('downloadDialog', true)

          this.cancel()
        } else {
          this.$message.error('批量打印失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.determineBtnLoading = false
      }
    }
    // 取消
    cancel() {
      this.$emit('getLoadingResult', false)
      this.isStudentPrint = false
      this.visiable = false
    }
  }
</script>

<style></style>
