import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 学习操作类型枚举
 * start_learning 开始学习
 * stop_learning 结束学习
 * learning_supervision 学习监管
 */
export enum LearningOperateTypeEnum {
  start_learning = 0,
  stop_learning = 1,
  learning_supervision = 2
}

/**
 * @description 学习操作类型
 */
class LearningOperateType extends AbstractEnum<LearningOperateTypeEnum> {
  static enum = LearningOperateTypeEnum

  constructor(status?: LearningOperateTypeEnum) {
    super()
    this.current = status
    this.map.set(LearningOperateTypeEnum.start_learning, '开始学习')
    this.map.set(LearningOperateTypeEnum.stop_learning, '结束学习')
    this.map.set(LearningOperateTypeEnum.learning_supervision, '学习监管')
  }
}

export default new LearningOperateType()
