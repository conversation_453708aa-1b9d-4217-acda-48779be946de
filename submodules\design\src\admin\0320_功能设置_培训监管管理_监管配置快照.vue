<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">监管配置快照</el-button>
        <el-drawer
          title="监管配置快照"
          :visible.sync="dialog1"
          :direction="direction"
          size="700px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-tit"><span class="tit-txt">基础配置</span></div>
            <el-form label-width="175px" class="m-text-form is-column f-mt10">
              <el-form-item label="监管功能：">开启</el-form-item>
              <el-form-item label="监管频率：">开启</el-form-item>
            </el-form>
            <div class="m-tit"><span class="tit-txt">网校级监管规则</span></div>
            <div class="m-left-divider f-mb30">
              <el-divider content-position="left"><i class="f-f15 f-fb f-cb">登录</i></el-divider>
              <el-form label-width="175px" class="m-text-form is-column">
                <el-form-item label="监管状态：">开启</el-form-item>
                <el-form-item label="监管频率：">显示配置信息快照例如“每次登录”</el-form-item>
                <el-form-item label="监管方式：">显示配置信息快照例如“人脸识别”</el-form-item>
              </el-form>
            </div>
            <div class="m-left-divider f-mb30">
              <el-divider content-position="left"><i class="f-f15 f-fb f-cb">进入学习</i></el-divider>
              <el-form label-width="175px" class="m-text-form is-column">
                <el-form-item label="监管状态：">关闭</el-form-item>
              </el-form>
            </div>
            <div class="m-left-divider f-mb30">
              <el-divider content-position="left"><i class="f-f15 f-fb f-cb">学习过程</i></el-divider>
              <el-form label-width="175px" class="m-text-form is-column">
                <el-form-item label="监管状态：">关闭</el-form-item>
                <el-form-item label="监管方式：">显示配置信息快照例如“人脸识别”</el-form-item>
                <el-form-item label="监管频率："
                  >显示配置信息快照例如“间隔X分钟监管1次，监管通过方可继续学习”</el-form-item
                >
                <el-form-item label="重试次数：">显示配置信息快照例如“重试X次仍未通过记为监管不通过”</el-form-item>
                <el-form-item label="监管不通过处理结果：">显示配置信息快照</el-form-item>
              </el-form>
            </div>
            <div class="m-left-divider f-mb30">
              <el-divider content-position="left"><i class="f-f15 f-fb f-cb">进入考试</i></el-divider>
              <el-form label-width="175px" class="m-text-form is-column">
                <el-form-item label="监管状态：">关闭</el-form-item>
                <el-form-item label="监管方式：">人脸识别</el-form-item>
              </el-form>
            </div>
            <div class="m-left-divider f-mb20">
              <el-divider content-position="left"><i class="f-f15 f-fb f-cb">考试过程</i></el-divider>
              <el-form label-width="175px" class="m-text-form is-column">
                <el-form-item label="监管状态：">关闭</el-form-item>
              </el-form>
            </div>
            <div class="m-tit"><span class="tit-txt">方案级监管规则</span></div>
            <el-tabs v-model="activeName" type="card" class="m-tab-card">
              <el-tab-pane label="这是监管规则名称（2）" name="first">
                <div class="m-left-divider f-mb30">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">监管方案</i></el-divider>
                  <div class="f-pl30 f-pb15 f-pr30">方案1，方案2，方案3</div>
                </div>
                <div class="m-left-divider f-mb30">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">进入学习</i></el-divider>
                  <el-form label-width="175px" class="m-text-form is-column">
                    <el-form-item label="监管状态：">关闭</el-form-item>
                    <el-form-item label="监管方式：">显示配置信息快照</el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider f-mb30">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">学习过程</i></el-divider>
                  <el-form label-width="175px" class="m-text-form is-column">
                    <el-form-item label="监管状态：">开启</el-form-item>
                    <el-form-item label="监管方式：">显示配置信息快照</el-form-item>
                    <el-form-item label="监管频率：">显示配置信息快照</el-form-item>
                    <el-form-item label="重试次数：">显示配置信息快照例如“重试X次仍未通过记为监管不通过”</el-form-item>
                    <el-form-item label="监管不通过处理结果：">显示配置信息快照</el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider f-mb30">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">进入考试</i></el-divider>
                  <el-form label-width="175px" class="m-text-form is-column">
                    <el-form-item label="监管状态：">开启</el-form-item>
                    <el-form-item label="监管方式：">显示配置信息快照</el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider f-mb10">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">考试过程</i></el-divider>
                  <el-form label-width="175px" class="m-text-form is-column">
                    <el-form-item label="监管状态：">开启</el-form-item>
                    <el-form-item label="监管方式：">显示配置信息快照</el-form-item>
                    <el-form-item label="监管频率：">显示配置信息快照</el-form-item>
                    <el-form-item label="重试次数：">显示配置信息快照例如“重试X次仍未通过记为监管不通过”</el-form-item>
                    <el-form-item label="监管不通过处理结果：">显示配置信息快照</el-form-item>
                  </el-form>
                </div>
              </el-tab-pane>
              <el-tab-pane label="方案级监管（10）" name="second">
                <div class="m-left-divider f-mb30">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">监管方案</i></el-divider>
                  <div class="f-pl30 f-pb15 f-pr30">方案4，方案2，方案3</div>
                </div>
                <div class="m-left-divider f-mb30">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">进入学习</i></el-divider>
                  <el-form label-width="175px" class="m-text-form is-column">
                    <el-form-item label="监管状态：">关闭</el-form-item>
                    <el-form-item label="监管方式：">显示配置信息快照</el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider f-mb30">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">学习过程</i></el-divider>
                  <el-form label-width="175px" class="m-text-form is-column">
                    <el-form-item label="监管状态：">开启</el-form-item>
                    <el-form-item label="监管方式：">显示配置信息快照</el-form-item>
                    <el-form-item label="监管频率：">显示配置信息快照</el-form-item>
                    <el-form-item label="重试次数：">显示配置信息快照例如“重试X次仍未通过记为监管不通过”</el-form-item>
                    <el-form-item label="监管不通过处理结果：">显示配置信息快照</el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider f-mb30">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">进入考试</i></el-divider>
                  <el-form label-width="175px" class="m-text-form is-column">
                    <el-form-item label="监管状态：">开启</el-form-item>
                    <el-form-item label="监管方式：">显示配置信息快照</el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider f-mb10">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">考试过程</i></el-divider>
                  <el-form label-width="175px" class="m-text-form is-column">
                    <el-form-item label="监管状态：">开启</el-form-item>
                    <el-form-item label="监管方式：">显示配置信息快照</el-form-item>
                    <el-form-item label="监管频率：">显示配置信息快照</el-form-item>
                    <el-form-item label="重试次数：">显示配置信息快照例如“重试X次仍未通过记为监管不通过”</el-form-item>
                    <el-form-item label="监管不通过处理结果：">显示配置信息快照</el-form-item>
                  </el-form>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>返回</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: true,
        dialog2: false,
        dialog3: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
