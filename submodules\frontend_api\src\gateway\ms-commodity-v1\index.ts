import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/gql/ms-commodity-v1'

// 是否微服务
const isMicroService = true

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-commodity-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: 'ms-commodity-v1'
}

// 枚举

// 类

/**
 * <AUTHOR>
 */
export class CommoditySkuOffShelveRequest {
  /**
   * 商品SkuID
   */
  id: string
}

/**
 * <AUTHOR>
 */
export class CommoditySkuOnShelveRequest {
  /**
   * 商品SkuID
   */
  id: string
}

/**
 * <AUTHOR>
 */
export class ValidateCommodityRequest {
  /**
   * 商品Sku
   */
  commoditySkuId: string
  /**
   * 购买渠道类型
1:用户自主购买
2:集体缴费
3:管理员导入
   */
  channelType: number
  /**
   * 终端唯一编码
Web:Web端
IOS:IOS端
Android:安卓端
WechatMini:微信小程序
WechatOfficial:微信公众号
   */
  terminalCode: string
}

export class CommodityInfo {
  /**
   * 资源类型，该类型由发布为商品的线上资源定义
   */
  resourceType: string
  /**
   * 资源ID
   */
  resourceId: string
}

/**
 * <AUTHOR>
 */
export class ValidateCommodityResponse {
  /**
   * 验证结果
200：验证通过
30001：商品不存在
30002：商品已下架
30003：不支持当前渠道购买该商品
30004：当前渠道已关闭
   */
  code: string
  /**
   * 验证失败原因
   */
  message: string
  /**
   * 资源信息
   */
  data: CommodityInfo
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 校验商品是否可以购买
   * @param commodityInfo 请求验证信息
   * @return 验证结果信息
   * @param query 查询 graphql 语法文档
   * @param commodityInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validateCommodity(
    commodityInfo: ValidateCommodityRequest,
    query: DocumentNode = GraphqlImporter.validateCommodity,
    operation?: string
  ): Promise<Response<ValidateCommodityResponse>> {
    return commonRequestApi<ValidateCommodityResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { commodityInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 下架商品
   * @param request 商品下架信息
   * @return true/false,下架成功/下架失败
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async offShelve(
    request: CommoditySkuOffShelveRequest,
    mutate: DocumentNode = GraphqlImporter.offShelve,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 上架商品
   * @param request 商品上架信息
   * @return true/false,上架成功/上架失败
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async onShelve(
    request: CommoditySkuOnShelveRequest,
    mutate: DocumentNode = GraphqlImporter.onShelve,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
