<template>
  <el-dialog title="提示" :visible.sync="giveUpModel.giveUpCtrl" :lock-scroll="true" :append-to-body="true" width="30%">
    <span>确定要放弃编辑吗？</span>
    <span slot="footer" class="dialog-footer">
      <el-button @click="giveUpModel.changeGiveUp(false)">取 消</el-button>
      <el-button type="primary" @click="giveUpGo()">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'

  @Component
  export default class GiveUpDialog extends Vue {
    @Prop({
      type: GiveUpCommonModel,
      required: false,
      default: () => {
        return new GiveUpCommonModel()
      }
    })
    giveUpModel: GiveUpCommonModel

    // 放弃编辑路由
    giveUpGo() {
      this.giveUpModel.changeGiveUp(false)
      this.$router.push(this.giveUpModel.routerUrl)
      this.$emit('callBack')
    }
  }
</script>
