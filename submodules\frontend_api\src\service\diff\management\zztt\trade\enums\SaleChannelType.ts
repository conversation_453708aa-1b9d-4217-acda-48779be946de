/*
 * @Author: z张仁榕
 * @Date: 2025-01-08 10:41:14
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-01-15 09:30:07
 * @Description:
 */
import abstractEnum from '@api/service/common/enums/AbstractEnum'
import EnumOption from '@api/service/common/enums/EnumOption'

export enum SaleChannelEnum {
  /**
   * 网校
   */
  self,
  /**
   * 分销
   */
  distribution,
  /**
   * 专题
   */
  topic,
  /**
   * 华医网
   */
  huayi
}
class SaleChannelType extends abstractEnum<SaleChannelEnum> {
  static enum = SaleChannelEnum
  constructor(status?: SaleChannelEnum) {
    super()
    this.current = status
    this.map.set(SaleChannelEnum.self, '网校')
    this.map.set(SaleChannelEnum.distribution, '分销')
    this.map.set(SaleChannelEnum.topic, '专题')
    this.map.set(SaleChannelEnum.huayi, '华医网')
  }

  getSpecialSubjectSaleChannel(SaleChannel: boolean) {
    if (typeof SaleChannel === 'boolean') {
      return SaleChannel
        ? [SaleChannelEnum.topic]
        : [SaleChannelEnum.self, SaleChannelEnum.distribution, SaleChannelEnum.huayi]
    } else {
      return undefined
    }
  }

  /**
   * 筛选列表
   */
  list() {
    return super.list().map((item) => {
      if (item.code === SaleChannelEnum.self) {
        return Object.assign(new EnumOption<SaleChannelEnum>(), { desc: '网校', code: SaleChannelEnum.self })
      } else {
        return item
      }
    })
  }
}
export default new SaleChannelType()
