import { Constants } from '@api/service/common/models/common/Constants'
import moment from 'moment'

class WorkTypeCategory {
  id: string
  name: string
  parentId: string
  sort: number
  createTime: Date
  lastUpdateTime: Date

  parse(response: any) {
    this.id = response.id
    this.name = response.name
    this.parentId = response.parentId
    this.sort = response.sort
    this.createTime = moment(response.createTime, Constants.DATE_PATTERN).toDate()
    this.lastUpdateTime = moment(response.lastUpdateTime, Constants.DATE_PATTERN).toDate()
    return this
  }
}

export default WorkTypeCategory
