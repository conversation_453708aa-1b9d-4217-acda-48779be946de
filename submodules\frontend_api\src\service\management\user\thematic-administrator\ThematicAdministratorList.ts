import ThematicAdministratorItem from '@api/service/management/user/thematic-administrator/ThematicAdministratorItem'
import MsBasicDataQueryBackstageGateway, {
  AdminQueryRequest,
  AdminUserRequest,
  AccountRequest,
  AuthenticationRequest,
  RoleRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

import { Page } from '@hbfe/common'
import { AdministratorStatusEnum } from '@api/service/management/user/thematic-administrator/AdministratorStatusEnum'
import PlatformTrainingChannel, {
  ContractProviderAdminQueryRequest,
  TrainingChannelCountRequest
} from '@api/platform-gateway/platform-training-channel-back-gateway'

export default class ThematicAdministratorList {
  /**
   * 管理员账号
   */
  account = ''
  /**
   * 管理员名称
   */
  name = ''
  /**
   * 启停用状态 | 1.启用 | 2.禁用
   */
  status: AdministratorStatusEnum = null
  /**
   * 专题名称
   */
  thematicName = ''
  /**
   * 查询
   */
  async queryList(page: Page): Promise<ThematicAdministratorItem[]> {
    const request = new ContractProviderAdminQueryRequest()
    request.user = new AdminUserRequest()
    request.user.userName = this.name ?? undefined
    request.user.userNameMatchType = 1
    request.account = new AccountRequest()
    request.role = new RoleRequest()
    request.authentication = new AuthenticationRequest()
    request.authentication.identity = this.account ?? undefined
    request.account.statusList = this.status ? [this.status] : undefined
    request.role.roleCategoryList = [19]
    request.trainingChannelName = this.thematicName
    const res = await PlatformTrainingChannel.pageOnlineSchoolAdminInfoInServicer({ page, request })
    if (res.status.isSuccess()) {
      const resList = res.data.currentPageData || []
      const queryChannelCountReq = new TrainingChannelCountRequest()
      queryChannelCountReq.userIds = resList.map((item) => {
        return item.userInfo?.userId
      })
      const queryCountRes = await PlatformTrainingChannel.listTrainingChannelCount(queryChannelCountReq)

      page.totalSize = res.data.totalSize
      page.totalPageSize = res.data.totalPageSize
      return resList.map((item) => ThematicAdministratorItem.from(item, queryCountRes?.data || []))
    } else {
      return []
    }
  }
}
