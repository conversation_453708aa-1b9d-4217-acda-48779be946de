<route-meta>
{
"isMenu": true,
"title": "配送渠道配置",
"sort": 4,
"icon": "icon-peisong"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="查询配送渠道" actions="create">
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div class="f-flex f-align-center">
          <span class="f-mr5 f-fb">网校提供的配送方式</span>
          <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
            <i class="el-icon-question m-tooltip-icon f-c9"></i>
            <div slot="content">请勾选本网校支持的配送方式，对应的邮寄 / 自取方式才会对学员展示。</div>
          </el-tooltip>
          <span class="f-mr5">：</span>
          <template v-if="$hasPermission('chooseWay')" desc="配送方式" actions="doModify">
            <el-checkbox-group v-model="mutationModifyDeliverWay.deliverWays">
              <el-checkbox
                v-for="option in wayList"
                :key="option.code"
                :label="option.code"
                name="type"
                :disabled="saving"
                @change="doModify"
              >
                {{ option.desc }}
              </el-checkbox>
            </el-checkbox-group>
          </template>
          <i class="el-icon-loading f-ml10" v-if="saving"></i>
        </div>
      </el-card>
      <el-tabs v-model="tabName" type="card" class="m-tab-card">
        <el-tab-pane label="快递" name="1">
          <template v-if="$hasPermission('Delivery')" desc="快递点" actions="@Delivery">
            <delivery></delivery>
          </template>
        </el-tab-pane>
        <el-tab-pane label="自取点" name="2">
          <template v-if="$hasPermission('PickUp')" desc="自取点列表" actions="@PickUp">
            <pick-up></pick-up>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import Delivery from '@hbfe/jxjy-admin-distributionChannel/src/components/delivery.vue'
  import PickUp from '@hbfe/jxjy-admin-distributionChannel/src/components/pick-up.vue'
  import QueryDeliverWayList from '@api/service/common/trade-config/query/QueryDeliverWayList'
  import DeliverWayType from '@api/service/common/trade-config/query/enums/DeliverWayType'
  import MutationModifyDeliverWay from '@api/service/management/online-school-config/distribution-channels-config/mutation/MutationModifyDeliverWay'
  import { bind, debounce } from 'lodash-decorators'

  @Component({
    components: { Delivery, PickUp }
  })
  export default class extends Vue {
    tabName = '1'

    wayList = new DeliverWayType().list()
    mutationModifyDeliverWay: MutationModifyDeliverWay = new MutationModifyDeliverWay()

    async created() {
      await this.queryDeliverWayList.queryList()
      this.mutationModifyDeliverWay.setDeliverWays(this.queryDeliverWayList.typeList)
    }

    saving = false

    @bind
    @debounce(200)
    async doModify() {
      this.saving = true
      await this.mutationModifyDeliverWay.doModify()
      this.saving = false
    }

    queryDeliverWayList: QueryDeliverWayList = new QueryDeliverWayList()
  }
</script>

<style scoped></style>
