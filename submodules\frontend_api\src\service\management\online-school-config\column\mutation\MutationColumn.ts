import { ResponseStatus } from '@hbfe/common'

class MutationColumn {
  id: string
  name = ''

  constructor(id: string) {
    this.id = id
  }

  async doEnable(): Promise<ResponseStatus> {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(new ResponseStatus(200, '启用成功'))
      }, 1500)
    })
  }

  async doDisable(): Promise<ResponseStatus> {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(new ResponseStatus(200, '启用成功'))
      }, 1500)
    })
  }

  async doModifyName() {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(new ResponseStatus(200, '修改成功'))
      }, 1500)
    })
  }
}

export default MutationColumn
