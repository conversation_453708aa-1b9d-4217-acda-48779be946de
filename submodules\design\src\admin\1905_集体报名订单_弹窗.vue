<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--查看下单结果-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20 f-mb20">查看下单结果</el-button>
        <el-drawer
          title="查看下单结果"
          :visible.sync="dialog3"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-text-form is-column f-ml10 f-mt10">
              <el-form-item label="集体报名批次号：">batch210820181132748989080007</el-form-item>
              <el-form-item label="缴费人次：">2</el-form-item>
              <el-form-item label="实付金额（元）：">200</el-form-item>
              <el-form-item label="支付方式：">集体报名-线下支付</el-form-item>
              <el-form-item label="下单结果："><el-tag type="success" size="small">处理完成</el-tag></el-form-item>
              <el-form-item label="下单时间：">2021.02.02 14:00:00</el-form-item>
              <el-form-item label="付款成功时间：">2021.02.02 14:00:00</el-form-item>
            </el-form>
            <div class="m-tit f-mt10">
              <span class="tit-txt">处理结果说明</span>
            </div>
            <div class="f-ml20">
              当前缴费人次
              <span class="f-co">2</span>
              次，处理成功
              <span class="f-co">2</span>
              ，处理失败
              <span class="f-co">0</span>
            </div>
          </div>
        </el-drawer>
        <!--强制关闭批次-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20">强制关闭批次</el-button>
        <el-drawer title="提示" :visible.sync="dialog4" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item>
                    <p>请输入简直关闭批次原因：</p>
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar f-tc">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--关联售后信息-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">查看关联售后信息</el-button>
        <el-drawer
          title="关联售后信息"
          :visible.sync="dialog1"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
              <el-tab-pane label="换班" name="first">
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column label="操作时间" min-width="180" fixed="left">
                    <template>2021-10-15 00:21:21</template>
                  </el-table-column>
                  <el-table-column label="初始班级" min-width="300">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <!--超出两行样式-->
                        <el-popover placement="top-start" width="400" trigger="hover">
                          <div slot="reference">
                            <div class="f-to-two">
                              【培训班】2019-2020公需课物品名称支持两行显示2019-2020公需课物品名称支持两行显示
                            </div>
                            <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                            <p><el-tag type="danger" size="mini">初始物品</el-tag></p>
                          </div>
                          <p>【培训班】2019-2020公需课物品名称支持两行显示2019-2020公需课物品名称支持两行显示</p>
                        </el-popover>
                      </div>
                      <div v-else>
                        <div class="f-to-two">【培训班】2019-2020公需课物品名称支持两行显示</div>
                        <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                        <p><el-tag type="danger" size="mini">初始物品</el-tag></p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="新班级" min-width="300">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <div class="f-to-two">【培训班】2019-2020公需课物品名称支持两行显示</div>
                        <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                        <p><el-tag type="danger" size="mini">最新物品</el-tag></p>
                      </div>
                      <div v-else>
                        <div class="f-to-two">
                          <div class="f-to-two">【培训班】2019-2020公需课物品名称支持两行显示</div>
                          <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                          <p><el-tag type="danger" size="mini">最新物品</el-tag></p>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="单价(元)" min-width="120" align="right">
                    <template>0.01</template>
                  </el-table-column>
                  <el-table-column label="售后状态" min-width="120" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-tag type="success">换班成功</el-tag>
                        <p class="f-mt5"><a href="#" class="f-link f-underline f-cb f-f12">查看详情</a></p>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-tag type="success">换班成功</el-tag>
                        <p class="f-mt5"><a href="#" class="f-link f-underline f-cb f-f12">查看详情</a></p>
                      </div>
                      <div v-else>
                        <el-tag type="warning">换班中</el-tag>
                        <p class="f-mt5"><a href="#" class="f-link f-underline f-cb f-f12">查看详情</a></p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作人" min-width="110">
                    <template>林林一</template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane label="换期" name="second">
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column label="操作时间" min-width="180" fixed="left">
                    <template>2021-10-15 00:21:21</template>
                  </el-table-column>
                  <el-table-column label="初始期别" min-width="300">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <!--超出两行样式-->
                        <el-popover placement="top-start" width="400" trigger="hover">
                          <div slot="reference">
                            <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                            <div class="f-to-two">
                              <el-tag type="info" size="mini">所属方案</el-tag
                              >2019-2020公需课物品名称支持两行显示2019-2020公需课物品名称支持两行显示
                            </div>
                            <p><el-tag type="danger" size="mini">初始物品</el-tag></p>
                          </div>
                          <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                          <p>
                            <el-tag type="info" size="mini">所属方案</el-tag
                            >2019-2020公需课物品名称支持两行显示2019-2020公需课物品名称支持两行显示
                          </p>
                        </el-popover>
                      </div>
                      <div v-else>
                        <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                        <div class="f-to-two">
                          <el-tag type="info" size="mini">所属方案</el-tag>2019-2020公需课物品名称支持两行显示
                        </div>
                        <p><el-tag type="danger" size="mini">初始物品</el-tag></p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="新期别" min-width="300">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                        <div class="f-to-two">
                          <el-tag type="info" size="mini">所属方案</el-tag>2019-2020公需课物品名称支持两行显示
                        </div>
                        <p><el-tag type="danger" size="mini">最新物品</el-tag></p>
                      </div>
                      <div v-else>
                        <div class="f-to-two">
                          <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                          <div class="f-to-two">
                            <el-tag type="info" size="mini">所属方案</el-tag>2019-2020公需课物品名称支持两行显示
                          </div>
                          <p><el-tag type="danger" size="mini">最新物品</el-tag></p>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作类型" min-width="120" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        换班换期
                      </div>
                      <div v-else>
                        换班换期
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="售后状态" min-width="120" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-tag type="success">换期成功</el-tag>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-tag type="success">换期成功</el-tag>
                      </div>
                      <div v-else>
                        <el-tag type="warning">换期中</el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作人" min-width="110">
                    <template>林林一</template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-drawer>
        <!--普通退款-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">普通退款</el-button>
        <el-drawer title="退款" :visible.sync="dialog2" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              退款后，原有的学习记录将清空，请确认是否继续。
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="退款金额：">
                    <span class="f-cr f-fb f-f16">¥ 10</span>，<span class="f-fb f-ml20">退款人次：</span
                    ><span class="f-cr f-fb f-f16">1</span>
                    <p class="f-cr">（换班订单退款退的是初始订单子订单，商品是最新换入订单子订单）</p>
                  </el-form-item>
                  <el-form-item label="退款理由：" required>
                    <el-select v-model="form.region" clearable placeholder="请选择退款理由">
                      <el-option label="帮助中心" value="shanghai"></el-option>
                      <el-option label="消息" value="beijing"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="退款说明：" required>
                    <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入退款说明" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确认退款</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--强制退款-->
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">强制退款</el-button>
        <el-drawer title="退款" :visible.sync="dialog5" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              退款后，原有的学习记录将清空，请确认是否继续。
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="退款金额：">
                    <span class="f-cr f-fb f-f16">¥ 10</span>，<span class="f-fb f-ml20">退款人次：</span
                    ><span class="f-cr f-fb f-f16">1</span>
                    <p class="f-cr">（换班订单退款退的是初始订单子订单，商品是最新换入订单子订单）</p>
                  </el-form-item>
                  <el-form-item label="退款提示：" class="is-text">
                    已有学员考核通过，是否强制退款？
                  </el-form-item>
                  <el-form-item label="退款理由：" required>
                    <el-select v-model="form.region" clearable placeholder="请选择退款理由">
                      <el-option label="帮助中心" value="shanghai"></el-option>
                      <el-option label="消息" value="beijing"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="退款说明：" required>
                    <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入退款说明" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确认退款</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--审核汇款凭证-->
        <el-button @click="dialog6 = true" type="primary" class="f-mr20">审核汇款凭证</el-button>
        <el-drawer
          title="审核汇款凭证"
          :visible.sync="dialog6"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd f-tc">
            <el-image
              class="web-banner f-mt20"
              src="/assets/images/demo-invoice.png"
              :preview-src-list="['/assets/images/demo-invoice.png']"
            />
            <p class="f-mt5 f-c9">点击图片可查看原图</p>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确认款项</el-button>
          </div>
        </el-drawer>
        <!--查看导入任务-->
        <el-button @click="dialog7 = true" type="primary" class="f-mr20">查看导入任务</el-button>
        <el-drawer
          title="查看导入任务"
          :visible.sync="dialog7"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="任务名称" min-width="240" fixed="left">
                <template>读取导入的文件名.xlsx.2021-12-09 13:56:04</template>
              </el-table-column>
              <el-table-column label="任务处理时间" min-width="170">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
              <el-table-column label="任务结束时间" min-width="170">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
              <el-table-column label="任务执行状态" min-width="120">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-tag type="info">未执行</el-tag>
                  </div>
                  <div v-else>
                    <el-tag type="success">已执行</el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="任务处理结果" min-width="120">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="danger" class="badge-status">失败</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">成功</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="处理总条数/成功条数/失败条数" width="240" align="center">
                <template>10 / 5 / 5</template>
              </el-table-column>
              <el-table-column label="操作" width="140" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">查看日志</el-button>
                  <el-button type="text" size="mini">下载失败数据</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
        </el-drawer>
        <!--查看换班详情-->
        <el-button @click="dialog8 = true" type="primary" class="f-mr20">查看详情（换班）</el-button>
        <el-drawer title="查看详情" :visible.sync="dialog8" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd f-mt20 f-mlr40">
            <el-timeline>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">发起换班</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">退班处理中</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">退班失败</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">申请发货</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">发货处理中</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">换班成功</span></p>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-drawer>
        <!--查看换期详情-->
        <el-button @click="dialog9 = true" type="primary" class="f-mr20">查看详情（换期）</el-button>
        <el-drawer title="查看详情" :visible.sync="dialog9" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd f-mt20 f-mlr40">
            <el-timeline>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">发起换期</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">换期处理中</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">换期失败</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">发起换期</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">换期成功</span></p>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
