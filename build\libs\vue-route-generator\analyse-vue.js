const babelParse = require('@babel/parser')
const { JSDOM } = require('jsdom')
const fs = require('fs')
const path = require('path')
const vueTemplateCompiler = require('vue-template-compiler')
const analyseTypescript = require('./analyse-ts')
const resolveUtils = require('./resolve-utils')

function analyseHtml(content, permissionMap, filePath, routerPath = [], prefix = '', scriptParseResult) {
  // 因为jsdom 不解析 template 的子节点， 所以取巧
  const jsHtml = new JSDOM(content.replace(/template/g, 'has-permission-template'))
  const hasPermissionNodes = jsHtml.window.document.querySelectorAll('*[v-if*="$hasPermission"]')
  Array.from(hasPermissionNodes).forEach(dom => {
    const attr = dom.attributes
    const getId = attr['v-if']
    const resultName = getId.value.replace(/\$hasPermission\(['|"](.*?)['|"]\)/, '$1')
    permissionMap[resultName] = {
      name: attr.desc.value,
      ownerGroup: [`${prefix}${routerPath.concat(resultName).join('.')}`],
      graphql: []
    }
    //attr.actions && analyseTypescript(attr.actions.value.split(/,\s*/g), permissionMap[resultName])
    if (attr.actions) {
      const actions = attr.actions.value.split(/,\s*/g)
      actions.forEach(action => {
        const split = action.split('/')
        const moduleName = split[0]
        const actionMethodName = split[1]
        let importPath = scriptParseResult.importDeclarations[moduleName]
        if (!importPath) {//如果在引入的模块中有声明该变量的直接用没有的用引入路径的最后一段匹配
          importPath = Object.values(scriptParseResult.importDeclarations).find(importPath=>path.parse(importPath).name === moduleName)
        }
        if (importPath) {
          const result = resolveUtils.parseModuleFile(importPath,path.basename(filePath))
          const actionMethod=result.actionMethods[actionMethodName]
          if(actionMethod){
            actionMethod.graphqlMethods.forEach(graphqlMethod=>permissionMap[resultName].graphql.push(graphqlMethod))
          }else{
            const errMsg=filePath+'配置的'+action+'在'+result.filePath+'中不存在@Action的'+actionMethodName+'方法'
            throw new Error(errMsg)
          }
        }
      })
    }
  })
}

function analyseScript(content, permissionMap, filePath, routerPath, prefix) {
  const parseResult = { importDeclarations: {} }
  const result = babelParse.parse(content, {
    sourceType: 'module',
    plugins: [
      'typescript',
      [
        'decorators',
        {
          decoratorsBeforeExport: true
        }
      ],
      'classProperties'
    ]
  })
  result.program.body.forEach(body => {
    if (body.type === 'ImportDeclaration') {
      body.specifiers.forEach(specifier => parseResult.importDeclarations[specifier.local.name] = body.source.value)
      if (body.source && /\.vue/.test(body.source.value)) {
        const value = body.source.value.replace(/^@\//, '')
        const parsePath = path.parse(value)
        let readPath = filePath
        if (/@\//.test(body.source.value)) {
          readPath = path.resolve(process.cwd(), 'src')
        }
        const resultContent = fs.readFileSync(path.resolve(readPath, value)).toString()
        const compiled = vueTemplateCompiler.parseComponent(resultContent)
        let scriptParseResult = {}
        if (compiled.script) {
          scriptParseResult = analyseScript(compiled.script.content, permissionMap, path.join(readPath, parsePath.dir), routerPath, prefix)
        }
        // compiled.template &&
        // analyseHtml(compiled.template.content, permissionMap, path.join(readPath, parsePath.dir), routerPath, prefix, scriptParseResult)
      }
    }
  })
  return parseResult
}

function analyse(compiled, permissionMap, filePath, routerPath, prefix) {
  let scriptParseResult = {}
  if (compiled.script) {
    scriptParseResult = analyseScript(compiled.script.content, permissionMap, filePath, routerPath, prefix)
  }
  //compiled.template && analyseHtml(compiled.template.content, permissionMap, filePath, routerPath, prefix, scriptParseResult)
}

module.exports = analyse
