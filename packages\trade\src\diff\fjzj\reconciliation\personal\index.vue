<route-meta>
{
"isMenu": true,
"title": "个人报名对账",
"sort": 1,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>
<template>
  <el-main
    v-if="$hasPermission('query')"
    query
    actions="@OrderReconciliation,@RefundReconciliation"
    desc="个人报名对账"
  >
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky" @tab-click="handleClick">
      <template
        v-if="$hasPermission('orderReconciliation,orderReconciliationFx,orderReconciliationZt')"
        desc="orderReconciliation:报名订单对账,orderReconciliationFx:报名订单对账（分销）,orderReconciliationZt:报名订单对账（专题）"
        query
        actions="orderReconciliation:@OrderReconciliation#orderReconciliationFx:@OrderReconciliation#orderReconciliationZt:@OrderReconciliation"
      >
        <el-tab-pane label="报名订单对账" name="order-reconciliation">
          <order-reconciliation ref="orderRef"></order-reconciliation>
        </el-tab-pane>
      </template>
      <template
        v-if="$hasPermission('refundReconciliation,refundReconciliationFx,refundReconciliationZt')"
        desc="refundReconciliation:退款订单对账,refundReconciliationFx:分销退款订单对账,refundReconciliationZt:专题退款订单对账"
        query
        actions="refundReconciliation:@RefundReconciliation#refundReconciliationFx:@RefundReconciliation#refundReconciliationZt:@RefundReconciliation"
      >
        <el-tab-pane label="退款订单对账" name="refund-reconciliation">
          <refund-reconciliation ref="refundRef"></refund-reconciliation>
        </el-tab-pane>
      </template>
    </el-tabs>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import OrderReconciliation from '@hbfe/jxjy-admin-trade/src/diff/fjzj/reconciliation/personal/components/order-reconciliation.vue'
  import RefundReconciliation from '@hbfe/jxjy-admin-trade/src/diff/fjzj/reconciliation/personal/components/refund-reconciliation.vue'

  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'

  @Component({
    components: { OrderReconciliation, RefundReconciliation }
  })
  export default class extends Vue {
    @Ref('orderRef') orderRef: any
    @Ref('refundRef') refundRef: any
    activeName = 'order-reconciliation'
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable

    async handleClick(val: any) {
      if (val.name === 'order-reconciliation') {
        if (this.isFxlogin && this.isHadFxAbility) {
          await this.orderRef.doSearchfx()
        } else if (this.isZtlogin) {
          await this.orderRef.doSearchzt()
        } else {
          await this.orderRef.doSearch()
        }
      } else {
        if (this.isFxlogin && this.isHadFxAbility) {
          await this.refundRef.doSearchFx()
        } else if (this.isZtlogin) {
          await this.refundRef.doSearchZt()
        } else {
          await this.refundRef.doSearch()
        }
      }
    }
  }
</script>
