<template>
  <div class="course-drawer-content clear-chrome-input-class">
    <div class="el-drawer__body">
      <!--  -->
      <el-drawer
        title="选择题库"
        :visible.sync="show"
        :append-to-body="true"
        :show-close="false"
        :wrapperClosable="false"
        :close-on-press-escape="false"
        size="1000px"
        custom-class="m-drawer"
      >
        <div class="drawer-bd">
          <el-row :gutter="16" class="m-query f-mt10">
            <el-form :inline="true" label-width="auto">
              <el-col :span="10">
                <el-form-item label="题库名称">
                  <el-input v-model="libraryResponseVo.libraryName" clearable placeholder="请输入题库名称" />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item>
                  <el-button type="primary" @click="doQueryPage" :loading="loading">查询</el-button>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
          <!--表格-->
          <el-table
            stripe
            highlight-current-row
            size="small"
            style="width: 100%"
            :data="pageData"
            v-loading="loading"
            max-height="500px"
            class="m-table f-mt10"
          >
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column prop="name" label="题库名称" min-width="300"> </el-table-column>
            <el-table-column prop="enabledQuestionCount" label="已启用的试题数量" width="180" align="center">
            </el-table-column>
            <el-table-column prop="createUserName" label="创建人" min-width="120"> </el-table-column>
            <el-table-column prop="createTime" label="创建时间" min-width="180"> </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <el-checkbox-group v-model="idList" @change="changeShow">
                  <el-checkbox :label="scope.row.id">选择</el-checkbox>
                </el-checkbox-group>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="page" v-bind="page"></hb-pagination>
        </div>
        <div class="m-btn-bar drawer-ft">
          <el-button @click="close()">取消</el-button>
          <el-button type="primary" @click="save()">确定</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import { SelectQuestionLibrary } from '@/store/modules-ui/exam/ExamPaperUIModule'
  import { UiPage, Query } from '@hbfe/common'
  import LibraryResponseVo from '@api/service/management/resource/question-library/query/vo/LibraryResponseVo'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import LibraryRequestVo from '@api/service/management/resource/question-library/query/vo/LibraryRequestVo'

  @Component
  export default class extends Vue {
    /**
     * 外部传过来打开关闭
     */
    @Prop({
      required: true
    })
    visible: boolean

    idList: Array<string> = [] as string[]
    @Prop({
      type: Object,
      required: false,
      default: () => new LibraryResponseVo()
    })
    changeExamList: LibraryResponseVo

    @Prop({
      type: Number,
      default: 0
    })
    changeDataNum: number

    @Watch('changeDataNum', {
      immediate: true,
      deep: true
    })
    changeDataNumChange(val: any) {
      if (val > 1) {
        this.pageData.forEach((item: LibraryResponseVo, index: number) => {
          if (item.id == this.changeExamList.id) {
            this.pageData[index].isSelected = false
            this.questionList.splice(index, 1)
          }
        })
      }
    }

    get itemIsSelected() {
      return (id: string) => {
        return this.idList.indexOf(id) > -1 ? true : false
      }
    }
    // @Watch('chooseAllStatus',{
    //   immediate: true,
    //   deep: true
    // })
    // chooseAllStatusChange(val:any){

    // }
    @Watch('idList', {
      immediate: true,
      deep: true
    })
    idListChange(val: any) {
      // if (this.idList.length !== this.pageData.length) {
      //   this.chooseAllStatus = false
      // }
      this.pageData.forEach((p) => {
        if (this.idList.indexOf(p.id) === -1) {
          this.chooseAllStatus = false
        }
        // else{
        //   this.idList.forEach(itm=>{
        //     if(itm===p.id)
        //   })
        // }
      })
    }
    isAllChose() {
      const flag = false
      let length = 0
      this.pageData.forEach((item) => {
        const index = this.idList.indexOf(item.id)
        if (index > -1) {
          length++
        }
      })
      if (this.pageData.length === length) {
        this.chooseAllStatus = true
      } else {
        this.chooseAllStatus = false
      }
    }
    // changeSelect(id: string) {
    //   // console.log('id:', id)
    //   // debugger
    //   const exist = this.idList.find((item: any) => {
    //     return item.id == id
    //   })
    //   const index = this.idList.indexOf(id)
    //   console.log(index, exist)
    //   if (exist) {
    //     this.idList.remove(id)
    //     // this.idList.splice(index, 1)
    //   } else {
    //     this.idList.push(id)
    //   }
    //   console.log('idList222:', this.idList)
    //   this.isAllChose()
    // }
    changeAllSelect(id: string) {
      const index = this.idList.indexOf(id)
      if (this.chooseAllStatus) {
        if (index === -1) {
          this.idList.push(id)
        }
      } else {
        if (index > -1) {
          this.idList.splice(index, 1)
        }
      }
    }

    @Prop({
      type: Array,
      default: () => new Array<LibraryResponseVo>()
    })
    isModifyData: Array<LibraryResponseVo>

    get show() {
      return this.visible
    }

    set show(val: boolean) {
      this.$emit('update:visible', val)
    }

    get selectNumber() {
      return this.idList?.length || 0
    }

    page: UiPage
    query: Query = new Query()
    pageData: Array<LibraryResponseVo> = new Array<LibraryResponseVo>()
    questionList = new Array<LibraryResponseVo>()
    showClear = false
    loading = false
    chooseArrForComponent = new Array<SelectQuestionLibrary>()
    chooseAllStatus = false
    questionLibrary = ResourceModule.queryQuestionLibraryFactory.queryQuestionLibraryMultiton

    libraryResponseVo: LibraryRequestVo = new LibraryRequestVo()

    tempUnitId = '' //暂存机构ID

    activeName = 'second' //默认选中tab
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }

    async mounted() {
      await this.doQueryPage()
    }

    /**
     * 初始化
     */
    async doQueryPage() {
      this.loading = true
      this.pageData = new Array<LibraryResponseVo>()
      this.libraryResponseVo.parentLibraryId = ''
      const res = await this.questionLibrary.queryQuestionBankLibrary(this.page, this.libraryResponseVo)
      res.data.forEach((item) => {
        const option = new LibraryResponseVo()
        Object.assign(option, item)
        option.isSelected = false
        this.pageData.push(option)
      })

      this.pageData?.forEach((item: LibraryResponseVo, index: number) => {
        this.isModifyData.forEach((itm: LibraryResponseVo, idx: number) => {
          if (itm.id == item.id) {
            this.questionList.push(item)
            this.pageData[index].isSelected = true
          }
        })
      })
      this.isAllChose()
      this.loading = false
    }

    select(item: LibraryResponseVo, reallyChoose: boolean) {
      if (reallyChoose) {
        if (!item.isSelected) {
          item.isSelected = reallyChoose
          this.questionList.push(item)
        }
      }
      if (!reallyChoose) {
        item.isSelected = reallyChoose

        const index = this.questionList.findIndex(function (value) {
          return value.libraryId === item.libraryId
        })
        if (index !== -1) {
          this.questionList.splice(index, 1)
        }
      }
    }

    // save() {
    //   this.$emit('childByValue', this.questionList)
    //   this.show = false
    // }

    save() {
      this.$emit('updateIdList', this.idList)
      this.show = false
    }

    close() {
      this.show = false
    }
    changeShow(val: any) {
      console.log('idList:', val)
    }
    /** 选择全部 */
    chooseAll() {
      const chooseAllStatus = this.chooseAllStatus
      this.chooseAllStatus = !chooseAllStatus
      this.pageData.forEach((p: any, i: number) => {
        this.changeAllSelect(p.id)
      })
    }
  }
</script>
<style scoped>
  .course-select {
    margin-bottom: 20px;
  }

  .course-drawer-content {
    display: inline;
  }

  .el-drawer__body {
    display: flex;
    flex-direction: column;
    overflow: auto;
    flex: 1;
    /* position: relative; */
  }

  .my-drawer-bd {
    padding: 0 20px 20px;
    flex: 1;
    overflow: auto;
    /* min-height: 688px; */
  }

  .my-drawer-ft {
    text-align: center;
    padding: 15px 0;
    background-color: #f2f2f2;
    /* position: absolute;
    bottom: 50px;
    width: 100%; */
  }

  .num {
    padding: 15px;
  }
</style>

<style>
  /* 清除浏览器弹出层选中样式 */
  /* .my-drawer:focus {
  outline: 0;
} */
  .paper-question-my-drawer {
    height: 100%;
    top: 0;
    bottom: 0;
    width: 640px;
  }

  :focus {
    outline: 0;
  }
</style>
