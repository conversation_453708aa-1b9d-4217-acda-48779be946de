import AttendanceQRCodeDto from '@api/service/management/implement/models/AttendanceQRCodeDto'
import { Page } from '@hbfe/common'
import { AttendanceTypeEnum } from '@api/service/management/implement/enums/AttendanceTypeEnum'
import MsSchemeLearningQuery, {
  SchemeIssuePlanItemResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

/**
 * 考勤二维码列表
 */
export default class AttendanceQRCodeManage {
  /**
   * 期别id
   */
  private periodId: string = undefined

  /**
   * 考勤类型
   */
  attendanceType: AttendanceTypeEnum = undefined

  /**
   * 考勤二维码列表
   */
  list: Array<AttendanceQRCodeDto> = new Array<AttendanceQRCodeDto>()

  /**
   * 期别id
   * @param periodId
   */
  constructor(periodId: string) {
    this.periodId = periodId
  }

  /**
   * 查询问卷二维码列表
   * @param page 分页
   */
  async queryList(page: Page) {
    const res = await MsSchemeLearningQuery.pageSchemeIssuePlanItemInServicer({
      page: page,
      issueId: this.periodId,
      signType: this.attendanceType
    })

    if (res?.data?.currentPageData?.length) {
      this.list = res.data.currentPageData.map((item: SchemeIssuePlanItemResponse) => {
        return AttendanceQRCodeDto.from(item)
      })
    } else {
      this.list = new Array<AttendanceQRCodeDto>()
    }

    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
  }
}
