import IntelligentLearningTaskTfeedback from './index.vue'
import Vue from 'vue'
import TimerTask from './TimerTask'
import { EventEmitter } from 'events'

class FeedbackManager extends EventEmitter {
  private instance: Vue | null = null
  readonly duration = 1000 * 60 * 15
  private readonly timerTask = new TimerTask(this.duration)

  isMounted(): boolean {
    return !!this.instance
  }

  getTimerTask(): TimerTask {
    return this.timerTask
  }

  show(options: { isAdditional?: boolean; count: number }) {
    if (this.isMounted()) {
      console.warn('弹窗已存在，请勿重复创建')
      return
    }
    const Constructor = Vue.extend(IntelligentLearningTaskTfeedback)
    const { isAdditional = false, count = 0 } = options

    this.instance = new Constructor({
      data: {
        isAdditional: !!isAdditional,
        count,
        visible: true
      }
    })
    this.setupInstance()
  }

  private setupInstance() {
    this.instance.$mount()
    document.body.appendChild(this.instance.$el)

    this.instance.$on('goHandle', () => {
      this.destroy()

      this.emit('goHandle')
    })

    this.instance.$on('remindMe', () => this.destroy())
  }

  private destroy() {
    if (this.instance) {
      this.instance.$destroy()
      document.body.removeChild(this.instance.$el)
      this.instance = null
    }
  }
}

// 创建单例并导出
const feedbackInstance = new FeedbackManager()
export default feedbackInstance
