import { LibraryResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import { LibrarySourceTypesEnum } from '../../enum/LibrarySourceTypes'
import MutationQuestionLibraryFactory from '../../MutationQuestionLibraryFactory'

class LibraryResponseVo extends LibraryResponse {
  /**
   * 题库ID
   */
  id = ''
  /**
   * 题库名称
   */
  name = ''

  /**
   * 是否可用
   */
  enabled = true

  /**
   * 题库来源类型
    @see LibrarySourceTypes
   */
  sourceType = LibrarySourceTypesEnum.CREATE

  /**
   * 题库描述
   */
  description = ''

  /**
   * 父题库id
   */
  parentId = '-1'

  /* 
    父题库名称
  */
  parentlibraryName = ''

  /**
   * 是否选中
   * 【默认未选】
   */
  isSelected = false

  /**
   * 创建人Id
   */
  createUserId = ''
  /**
   * 创建时间
   */
  createTime = ''
  /**
   * 已启用试题数
   */
  enabledQuestionCount = 0
  /**
   * 所有的试题数
   */
  questionCount = 0
  /**
   * 创建人姓名
   */
  createUserName = ''

  /**
   * @description: 获取题库操作模型
   * @param {*}
   * @return {*}
   */
  get questionLibraryAction() {
    return MutationQuestionLibraryFactory.questionLibraryAction(this.id, this.name, this.parentId, this.description)
  }

  // 模型转换vo
  static from(item: LibraryResponse) {
    const library = new LibraryResponseVo()
    library.id = item.libraryId
    library.name = item.libraryName
    library.enabled = item.enabled
    library.parentId = item.parentLibraryInfo?.libraryId
    library.parentlibraryName = item.parentLibraryInfo?.libraryName
    library.createUserId = item.createUserId
    library.createTime = item.createTime
    // library.questionCount = item
    return library
  }
}

export default LibraryResponseVo
