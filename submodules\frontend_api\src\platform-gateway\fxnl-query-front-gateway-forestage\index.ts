import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/fxnl-query-front-gateway-forestage'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'fxnl-query-front-gateway-forestage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum Direction {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum SaleCommoditySortEnum {
  SALE_CHANNEL_SORT = 'SALE_CHANNEL_SORT',
  ISSUE_NUM_SORT = 'ISSUE_NUM_SORT',
  ISSUE_SIGN_UP_TIME_SORT = 'ISSUE_SIGN_UP_TIME_SORT'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class PortalBannerDto {
  id?: string
  name?: string
  path?: string
  link?: string
  sort?: number
  enable?: boolean
  createdTime?: string
}

export class PortalFriendLinkDto {
  id?: string
  title?: string
  picture?: string
  friendLinkType?: number
  link?: string
  sort?: number
  createdTime?: string
}

export class PortalMenuDto {
  id?: string
  name?: string
  displayName?: string
  parentId?: string
  type?: number
  sourceType?: number
  link?: string
  code?: string
  referenceId?: string
  enable?: boolean
  sort?: number
  createdTime?: string
  updatedTime?: string
}

export class PortalPlateDto {
  id?: string
  name?: string
  displayName?: string
  parentId?: string
  type?: number
  sourceType?: number
  link?: string
  code?: string
  referenceId?: string
  allowChildren?: number
  sort?: number
  createdTime?: string
  updatedTime?: string
}

export class ServicerPortalDto {
  id?: string
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  servicerId?: string
  belongServiceId?: string
  category?: number
  identifier?: string
  portalType?: number
  title?: string
  shortName?: string
  logo?: string
  icon?: string
  themeColor?: string
  mobileQRcode?: string
  CSPhonePicture?: string
  CSPhone?: string
  CSCallTime?: string
  CSOnlineCodeId?: string
  CSWechat?: string
  trainingFlowPicture?: string
  footContentId?: string
  slogan?: string
  domainName?: string
  domainShortCode?: string
  contentId?: string
  isProvideServiceAccount?: number
  isPublished?: number
  publishedTime?: string
  unpublishedTime?: string
  cnzz?: string
  dirName?: string
  domainNameType?: number
  plates?: Array<PortalPlateDto>
  friendLinks?: Array<PortalFriendLinkDto>
  banner?: Array<PortalBannerDto>
  menu?: Array<PortalMenuDto>
  createdTime?: string
  updatedTime?: string
  isDeleted?: number
  deletedTime?: string
  recordCreatedTime?: string
  recordUpdatedTime?: string
}

export class CommodityPropertyDto {
  id?: string
  commodityId?: string
  propertyKey?: string
  propertyValue?: string
}

/**
 * <AUTHOR> linq
@date : 2025-04-15 18:39
@description :
 */
export class PortalDistributorIssueCommodityInDistributorRequest {
  /**
   * 原始商品id
   */
  rootCommodityIdList?: Array<string>
}

/**
 * 商品sku属性查询条件
 */
export class PropertyRequest {
  /**
   * 商品skuKey
   */
  propertyKey?: string
  /**
   * 商品skuValue
   */
  propertyValue?: string
}

/**
 * <AUTHOR> linq
@date : 2024-05-06 14:13
@description：分销商品sku属性请求入参
 */
export class DistributorCommoditySkuPropertyCollectionRequest {
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 网校id
   */
  onlineSchoolId?: string
  /**
   * 商品名称
   */
  distributorCommodityName?: string
  /**
   * 商品属性key-value集合
   */
  propertyList?: Array<PropertyRequest>
  /**
   * 商品销售地区（左右模糊查询）
   */
  areaPath?: string
}

export class PortalDistributorCommodityRequest {
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 商品名称查询
   */
  distributorCommodityName?: string
  /**
   * 商品属性key-value集合
   */
  propertyList?: Array<PropertyRequest>
  /**
   * 定价方案-商品销售地区（右模糊查询）
   */
  areaPath?: string
  /**
   * 定价方案-商品销售地区（右模糊查询）
   */
  areaPathT?: string
  /**
   * 商品id集合
(该id会无视门户展示的限制条件查询回所有商品)
   */
  commodityIds?: Array<string>
}

export class SaleCommoditySortRequest {
  /**
   * 排序字段
   */
  field?: SaleCommoditySortEnum
  /**
   * 正序或倒序
   */
  policy?: Direction
}

/**
 * <AUTHOR> linq
@date : 2025-04-15 17:50
@description : 指定分销商下的门户分销期别商品列表response返回值
 */
export class PortalDistributorIssueCommodityInDistributorResponse {
  /**
   * 期别信息
   */
  issueResourceInfo: NestDistributorIssueResourceInfoResponse
  /**
   * 已报名人数
   */
  registeredNumber: number
  /**
   * 最大报名人数
   */
  maxRegisterNumber: number
  /**
   * 剩余报名人数
   */
  remainingRegisterNumber: number
}

/**
 * <AUTHOR> linq
@date : 2025-04-15 18:29
@description : 指定分销商下的门户分销期别商品列表response返回值 - 冗余期别资源信息
 */
export class NestDistributorIssueResourceInfoResponse {
  /**
   * 期别id
   */
  issueId: string
  /**
   * 期别编号
   */
  issueNum: string
  /**
   * 期别名称
   */
  issueName: string
  /**
   * 期别报名开始时间
   */
  issueSignUpBeginDate: string
  /**
   * 期别报名结束时间
   */
  issueSignUpEndDate: string
  /**
   * 期别报到开始时间
   */
  issueStartReportDate: string
  /**
   * 期别报到结束时间
   */
  issueEndReportDate: string
  /**
   * 期别培训开始时间
   */
  issueTrainingBeginDate: string
  /**
   * 期别培训结束时间
   */
  issueTrainingEndDate: string
}

/**
 * 商品sku属性
<AUTHOR>
 */
export class CommoditySkuPropertyResponse {
  /**
   * 商品sku属性id
   */
  commoditySkuPropertyId: string
  /**
   * 商品sku属性名
   */
  commoditySkuPropertyName: string
  /**
   * 商品sku属性值集合
   */
  commoditySkuPropertyValueList: Array<CommoditySkuPropertyValueResponse>
}

/**
 * 商品sku属性值
<AUTHOR>
 */
export class CommoditySkuPropertyValueResponse {
  /**
   * 商品sku属性值id
   */
  commoditySkuPropertyValueId: string
  /**
   * 商品sku属性值展示名
   */
  commoditySkuPropertyValueName: string
  /**
   * 父级code
   */
  parentCode: string
}

/**
 * 商品sku地区属性节点
<AUTHOR>
 */
export class CommoditySkuRegionPropertyNodeResponse {
  /**
   * 商品sku地区属性id
   */
  commoditySkuRegionId: string
  /**
   * 地区编码
   */
  code: string
  /**
   * 地区名
   */
  name: string
  /**
   * 地区等级
   */
  level: number
  /**
   * 地区路径
   */
  codePath: string
  /**
   * 父级地区编码
   */
  parentCode: string
  /**
   * 下级商品sku地区属性
   */
  subRegionList: Array<CommoditySkuRegionPropertyNodeResponse>
}

/**
 * 商品sku培训专业属性节点
<AUTHOR>
 */
export class CommoditySkuTrainingProfessionPropertyNodeResponse {
  /**
   * 培训专业ID
   */
  id: string
  /**
   * 培训专业名称
   */
  name: string
  /**
   * 培训专业编码
   */
  code: string
  /**
   * 父级专业ID
   */
  parentCode: string
  /**
   * 排序序号
   */
  sort: number
  /**
   * 是否启用
   */
  isAvailable: number
  /**
   * 下级培训专业sku地区属性
   */
  subTrainingProfessionList: Array<CommoditySkuTrainingProfessionPropertyNodeResponse>
}

/**
 * 上下文信息
 */
export class OwnerInfoResponse {
  /**
   * 平台id
   */
  platformId: string
  /**
   * 平台版本id
   */
  platformVersionId: string
  /**
   * 项目id
   */
  projectId: string
  /**
   * 子项目id
   */
  subProjectId: string
  /**
   * 单位id
   */
  unitId: string
  /**
   * 服务商id
   */
  servicer: string
}

export class PropertyResponse {
  /**
   * 商品skuKey
   */
  propertyKey: string
  /**
   * 商品skuKeyName
   */
  propertyKeyName: string
  /**
   * 商品skuValue
   */
  propertyValue: string
  /**
   * 商品skuValue名称
   */
  propertyValueName: string
}

export class RegionResponse {
  /**
   * 地区ID
   */
  regionId: string
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 省ID
   */
  provideId: string
  /**
   * 市ID
   */
  cityId: string
  /**
   * 区县ID
   */
  countyId: string
  /**
   * 名称
   */
  name: string
  /**
   * 下级地区
   */
  children: Array<RegionResponse>
}

export class RegionTreeResponse {
  /**
   * 地区ID
   */
  regionId: string
  /**
   * 名称
   */
  name: string
  /**
   * 地区全路径
   */
  regionPath: string
  /**
   * 子地区
   */
  children: Array<RegionTreeResponse>
  /**
   * 子地区数量
   */
  childrenCount: number
}

/**
 * 商品-培训方案资源类型
 */
export class SchemeResourceInfoResponse {
  /**
   * 培训方案id
   */
  schemeId: string
  /**
   * 培训方案类型
   */
  schemeType: string
  /**
   * 培训方案名称
   */
  schemeName: string
  /**
   * 学时
   */
  period: string
  /**
   * 报名开始时间
   */
  registerBeginDate: string
  /**
   * 报名结束时间
   */
  registerEndDate: string
  /**
   * 上架状态 1-上架 2-下架
   */
  sellStatus: number
  /**
   * 培训开始时间
   */
  trainingBeginDate: string
  /**
   * 培训结束时间
   */
  trainingEndDate: string
}

/**
 * @version: 1.0
@description: 分销商信息
@author: sugs
@create: 2023-12-26 20:54
 */
export class DistributorResponse {
  /**
   * 分销商ID
   */
  distributorId: string
  /**
   * 分销商名称
   */
  distributorName: string
  /**
   * 分销商类型 1-个人 2-企业
   */
  distributorType: number
  /**
   * 分销商简称
   */
  distributorShortName: string
  /**
   * 分销商全称
   */
  distributorFullName: string
  /**
   * 统一社会信用代码
   */
  unifiedSocialCreditCode: string
  /**
   * 身份证号
   */
  idCard: string
}

/**
 * 分销商推广门户
 */
export class DistributorPortalInfoResponse {
  /**
   * 门户id
   */
  id: string
  /**
   * 所属平台id
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目id
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 业务平台服务商id  (例如分销商id)
   */
  servicerId: string
  /**
   * 所属服务商id (例如网校id)
   */
  belongServiceId: string
  /**
   * 0-主门户 1-子门户
   */
  category: number
  /**
   * 门户标识
   */
  identifier: string
  /**
   * 1-web端 2-移动端
   */
  portalType: number
  /**
   * 门户标题
   */
  title: string
  /**
   * 门户简称
   */
  shortName: string
  /**
   * 门户logo
   */
  logo: string
  /**
   * 浏览器图标
   */
  icon: string
  /**
   * 主题颜色
   */
  themeColor: string
  /**
   * 移动二维码
   */
  mobileQRcode: string
  /**
   * 移动二维码来源标识 1-系统生成 2-自定义
   */
  mobileQRCodeSign: number
  /**
   * 客服电话图片
   */
  CSPhonePicture: string
  /**
   * 客服电话
   */
  CSPhone: string
  /**
   * 客服咨询时间
   */
  CSCallTime: string
  /**
   * 在线客服代码内容id
   */
  CSOnlineCodeId: string
  /**
   * 培训流程图片
   */
  trainingFlowPicture: string
  /**
   * 底部内容
   */
  footContent: string
  /**
   * 宣传口号
   */
  slogan: string
  /**
   * 域名
   */
  domainName: string
  /**
   * 域名短码
   */
  domainShortCode: string
  /**
   * H5域名(请求Web端门户信息才会使用这个字段 如果请求的是H5端的门户信息，该字段为null)
   */
  domainNameH5: string
  /**
   * h5门户id
   */
  portalIdh5: string
  /**
   * 域名短码h5
   */
  domainShortCodeh5: string
  /**
   * 门户简介说明内容
   */
  content: string
  /**
   * 是否提供服务号
   */
  isProvideServiceAccount: boolean
  /**
   * 门户状态  是否已发布
   */
  isPublished: boolean
  /**
   * 网校状态
1-正常  2-失效
   */
  onlineSchoolStatus: number
  /**
   * 网校模式 1-正式实施 2-DEMO
   */
  onlineSchoolModes: number
  /**
   * 是否到期
   */
  isExpired: boolean
  /**
   * 前端模板id
   */
  portalTemplateId: string
  /**
   * 企业客服微信
   */
  CSWechat: string
  /**
   * 海报二维码
   */
  billQRCodes: Array<QRCode>
  /**
   * 门户发布时间
   */
  publishedTime: string
  /**
   * 门户取消发布时间
   */
  unPublishedTime: string
  /**
   * 信息
   */
  cnzz: string
  /**
   * 目录名
   */
  dirName: string
  /**
   * 域名类型（系统默认域名 1 自有域名 2）
   */
  domainNameType: number
  /**
   * 门户板块集合
   */
  plates: Array<PortalPlateResponse>
  /**
   * 友情链接集合
   */
  friendLinks: Array<PortalFriendLinkResponse>
  /**
   * 轮播图
   */
  banner: Array<PortalBannerResponse>
  /**
   * 移动端轮播图
   */
  bannersInh5: Array<PortalBannerResponse>
  /**
   * 门户菜单
   */
  menu: Array<PortalMenuResponse>
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 是否删除
   */
  isDeleted: number
  /**
   * 删除时间
   */
  deletedTime: string
  /**
   * 记录创建时间
   */
  recordCreatedTime: string
  /**
   * 记录更新时间
   */
  recordUpdatedTime: string
  /**
   * 销售渠道id
   */
  channelId: string
  /**
   * 当前分销商单位名称
   */
  distributorUnitName: string
}

/**
 * 门户轮播图
 */
export class PortalBannerResponse {
  /**
   * 轮播图id
   */
  id: string
  /**
   * 轮播图名称
   */
  name: string
  /**
   * 图片路径
   */
  path: string
  /**
   * 链接地址
   */
  link: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 门户友情链接
 */
export class PortalFriendLinkResponse {
  id: string
  title: string
  picture: string
  friendLinkType: number
  link: string
  sort: number
  createdTime: string
}

/**
 * 门户菜单
 */
export class PortalMenuResponse {
  /**
   * 栏目id
   */
  id: string
  /**
   * 门户栏目名称
   */
  name: string
  /**
   * 门户栏目展示名称
   */
  displayName: string
  /**
   * 父栏目id
   */
  parentId: string
  /**
   * 门户栏目类型 1-菜单 2-资讯
   */
  type: number
  /**
   * 来源类型 1-内置 2-用户创建
   */
  sourceType: number
  /**
   * 链接
   */
  link: string
  /**
   * 业务code
   */
  code: string
  /**
   * 引用id
   */
  referenceId: string
  /**
   * 是否可用
   */
  enable: boolean
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
}

export class PortalPlateResponse {
  /**
   * 板块id
   */
  id: string
  /**
   * 门户板块名称
   */
  name: string
  /**
   * 门户板块展示名称
   */
  displayName: string
  /**
   * 父板块id
   */
  parentId: string
  /**
   * 门户板块类型 1-默认 2-资讯
   */
  type: number
  /**
   * 来源类型 1-内置 2-用户创建
   */
  sourceType: number
  /**
   * 链接
   */
  link: string
  /**
   * 业务code
   */
  code: string
  /**
   * 引用id
   */
  referenceId: string
  /**
   * 是否允许存在子级
   */
  allowChildren: number
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
}

/**
 * <AUTHOR> linq
@date : 2024-09-18 14:04
@description：二维码
 */
export class QRCode {
  /**
   * 二维码图片
   */
  mobileQrcode: string
  /**
   * 二维码操作提示
   */
  qrcodeTip: string
}

/**
 * 校验url响应参数
 */
export class CheckResponse {
  /**
   * 错误码
   */
  code: string
  /**
   * true: 有效
false: 无效
   */
  valid: boolean
}

/**
 * <AUTHOR> linq
@date : 2024-05-06 14:04
@description：分销商品sku属性
 */
export class DistributorCommoditySkuPropertyCollectionResponse {
  /**
   * 商品sku属性集合
   */
  commoditySkuPropertyList: Array<CommoditySkuPropertyResponse>
  /**
   * 商品sku地区属性树集合
   */
  commoditySkuPropertyRegionTreeList: Array<CommoditySkuRegionPropertyNodeResponse>
  /**
   * 商品sku培训专业属性树集合
   */
  commoditySkuPropertyTrainingProfessionalTreeList: Array<CommoditySkuTrainingProfessionPropertyNodeResponse>
  /**
   * 商品销售地区属性树集合（定价方案地区）
   */
  commodityRegionTreeList: Array<CommoditySkuRegionPropertyNodeResponse>
}

export class PortalDistributorCommodityResponse {
  /**
   * 优惠价格  详情口需要返回
   */
  discountPrice: number
  /**
   * 优惠周期约束 | 1-周期 2-长期
   */
  discountDateConstraint: number
  /**
   * 优惠开始时间
   */
  discountStartTime: string
  /**
   * 优惠结束时间
   */
  discountEndTime: string
  /**
   * 优惠地区
   */
  discountRegionList: Array<RegionResponse>
  /**
   * 网校id
   */
  schoolId: string
  /**
   * 网校名称
   */
  schoolName: string
  /**
   * 网校名称简称
   */
  schoolShortName: string
  /**
   * 业主单位全称
   */
  unitName: string
  /**
   * 业主单位简称
   */
  unitShotName: string
  /**
   * 网校域名
   */
  schoolDomainName: string
  /**
   * 分销地区list
   */
  regionList: Array<RegionResponse>
  /**
   * 产品分销授权ID  优惠商品下单时取的授权id
   */
  productDistributionAuthId: string
  /**
   * 定价方案信息
   */
  productPricingSchemeResponse: ProductPricingSchemeResponse
  /**
   * 用户分销商品拥有信息
   */
  userDistributionCommodityOwnResponse: UserDistributionCommodityOwnResponse
  /**
   * 期别信息
   */
  issueResourceInfoList: Array<PortalDistributorIssueResourceInfoResponse>
  /**
   * 分销商商品id
   */
  distributorCommodityId: string
  /**
   * 分销商商品销售名称
   */
  saleTitle: string
  /**
   * 商品-培训方案资源类型
   */
  schemeResourceInfo: SchemeResourceInfoResponse
  /**
   * 分销商信息
   */
  distributor: DistributorResponse
  /**
   * 分销商商品sku属性
   */
  propertyList: Array<PropertyResponse>
  /**
   * 授权价格类型 1-固定 2-区间
   */
  priceType: number
  /**
   * 分销商商品价格
   */
  price: number
  /**
   * 最大价格
   */
  maxPrice: number
  /**
   * 最小价格
   */
  minPrice: number
  /**
   * 网校商品来源系统上下文信息
   */
  sourceOwnerInfo: OwnerInfoResponse
  /**
   * 原始商品id
   */
  commodityId: string
  /**
   * 分销商商品图片路径
   */
  commodityPicturePath: string
  /**
   * 分销状态
0-开启 1-关闭
商品的分销开始时间、结束时间作为判断
   */
  distributionStatus: number
  /**
   * 分销商商品销售时段类型
1-周期 2-长期
   */
  saleTimeType: number
  /**
   * 分销商商品销售开始时间
   */
  saleStartTime: string
  /**
   * 分销商商品销售结束时间
   */
  saleEndTime: string
  /**
   * 销售状态 1-有效 2-无效
   */
  saleStatus: number
  /**
   * 分销商品来源类型
1-产品分销授权
   */
  commoditySourceType: number
  /**
   * 分销商品来源id
1-产品分销授权id
   */
  commoditySourceId: string
  /**
   * 分销商品创建时间
   */
  commodityCreatedTime: string
  /**
   * 分销商品更新时间
   */
  commodityUpdatedTime: string
  /**
   * 商品是否门户可见
   */
  isShow: boolean
  /**
   * 定价方案启用个数
   */
  productPricingSchemeEnableNum: number
  /**
   * 培训方案期别数(面网授班级才有值)
   */
  issueNum: number
  /**
   * 分销商品上下架状态
   */
  shelveStatus: number
}

/**
 * <AUTHOR> linq
@date : 2025-04-15 18:29
@description : 根据分销商商品id获取分销商商品详情response返回值 - 冗余期别资源信息
 */
export class PortalDistributorIssueResourceInfoResponse {
  /**
   * 期别id
   */
  issueId: string
  /**
   * 期别编号
   */
  issueNum: string
  /**
   * 期别名称
   */
  issueName: string
  /**
   * 期别报名开始时间
   */
  issueSignUpBeginDate: string
  /**
   * 期别报名结束时间
   */
  issueSignUpEndDate: string
  /**
   * 期别报到开始时间
   */
  issueStartReportDate: string
  /**
   * 期别报到结束时间
   */
  issueEndReportDate: string
  /**
   * 期别培训开始时间
   */
  issueTrainingBeginDate: string
  /**
   * 期别培训结束时间
   */
  issueTrainingEndDate: string
}

/**
 * <AUTHOR> linq
@date : 2024-09-18 17:21
@description：定价方案推广门户信息
 */
export class PricingPolicyPortalInfo {
  /**
   * 推广门户标识id
   */
  portalIdentify: string
  /**
   * 推广门户销售渠道id
   */
  saleChannelId: string
  /**
   * 推广门户渠道排序
   */
  portalSort: number
  /**
   * 门户展示 (0-不展示, 1-展示）
   */
  showPortal: number
  /**
   * 门户推广 (0-不推广, 1-推广）
   */
  portalPromotion: number
  /**
   * 推广门户定价方案商品id
   */
  portalPricingPolicyCommodityId: string
}

export class ProductPricingSchemeResponse {
  /**
   * 定价方案ID
   */
  productPricingSchemeId: string
  /**
   * 产品分销授权ID
   */
  productDistributionAuthId: string
  /**
   * 授权商品id
   */
  productId: string
  /**
   * 价格
   */
  price: number
  /**
   * 启用时间
   */
  enableTime: string
  /**
   * 禁用时间
   */
  disEnableTime: string
  /**
   * 授权价格类型 1-固定 2-区间
   */
  priceType: number
  /**
   * 最大价格
   */
  maxPrice: number
  /**
   * 最小价格
   */
  minPrice: number
  /**
   * 产品销售范围
   */
  saleScopeList: Array<SaleScopeResponse>
  /**
   * 定价方案状态 (0-禁用, 1-启用)
   */
  status: number
  /**
   * 加入的推广门户信息
   */
  pricingPolicyPortalInfoList: Array<PricingPolicyPortalInfo>
}

export class RegionItemResponse {
  /**
   * 销售范围ID+地区ID 或 合同ID+地区ID（主键）
   */
  id: string
  /**
   * 地区来源类型 (1-销售地区, 2-分销地区)
   */
  sourceType: number
  /**
   * 地区来源ID（销售范围ID/合同ID）
   */
  sourceId: string
  /**
   * 地区ID
   */
  regionId: string
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 省ID
   */
  provinceId: string
  /**
   * 市ID
   */
  cityId: string
  /**
   * 县ID
   */
  countyId: string
  /**
   * 层级
   */
  level: number
  /**
   * 记录创建时间
   */
  recordCreatedTime: string
}

/**
 * 市粒度统计
 */
export class RegionSaleScopeResponse {
  /**
   * 地区来源类型 (1-销售地区, 2-分销地区)
   */
  sourceType: number
  /**
   * 地区来源ID（销售范围ID/合同ID）
   */
  sourceId: string
  /**
   * 地区ID
   */
  regionId: string
  /**
   * 地区名称
   */
  regionName: string
  /**
   * 统计数量(区数量)
   */
  total: number
}

/**
 * @see SaleScopeDto
 */
export class SaleScopeResponse {
  /**
   * 销售范围ID
   */
  saleScopeId: string
  /**
   * 地区销售范围类型 (0-同分销商品, 1-自定义)
   */
  regionSaleScopeType: number
  /**
   * 地区销售范围关联ID（销售范围ID/合同ID）
   */
  regionSaleScopeRelationId: string
  /**
   * 地区销售范围关联地区树id
   */
  regionSaleRegionTreeId: string
  /**
   * 单位销售范围类型 (1-不限, 2-自定义)
   */
  unitSaleScopeType: number
  /**
   * 是否启用
   */
  enable: number
  /**
   * 备注
   */
  remark: string
  /**
   * 记录创建时间
   */
  recordCreatedTime: string
  /**
   * 产品地区销售范围
   */
  regionSaleScopeList: Array<RegionSaleScopeResponse>
  /**
   * 地区树结构
   */
  regionTreeList: Array<RegionTreeResponse>
  /**
   * 销售地区明细
   */
  regionItemList: Array<RegionItemResponse>
  /**
   * 产品单位销售范围
   */
  unitSaleScopeList: Array<UnitSaleScopeResponse>
}

export class UnitSaleScopeResponse {
  /**
   * 销售范围ID+统一社会信用代码（主键）
   */
  id: string
  /**
   * 销售范围ID
   */
  saleScopeId: string
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 统一社会信用代码
   */
  creditCode: string
  /**
   * 记录创建时间
   */
  recordCreatedTime: string
}

/**
 * 用户分销商品拥有信息
 */
export class UserDistributionCommodityOwnResponse {
  /**
   * 是否拥有分销商下分销商品
0.不是  1。是
   */
  isOwn: number
  /**
   * 商品类型
1-子订单 2-换货单
   */
  commoditySourceType: number
  /**
   * 商品来源ID
   */
  commoditySourcesId: string
  /**
   * 商品子订单发货状态.
0: 等待发货,
100: 发货中,
200: 发货成功,
401: 发货失败
   */
  subOrderDeliveryStatus: number
}

export class PortalDistributorCommodityResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PortalDistributorCommodityResponse>
}

export class PortalDistributorIssueCommodityInDistributorResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PortalDistributorIssueCommodityInDistributorResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param servicerPortalDto 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async Transform(
    servicerPortalDto: ServicerPortalDto,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.Transform,
    operation?: string
  ): Promise<Response<DistributorPortalInfoResponse>> {
    return commonRequestApi<DistributorPortalInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { servicerPortalDto },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验商品是否有效
   * @return
   * @param query 查询 graphql 语法文档
   * @param distributorCommodityId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async checkDistributorCommodityRelationValid(
    distributorCommodityId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.checkDistributorCommodityRelationValid,
    operation?: string
  ): Promise<Response<CheckResponse>> {
    return commonRequestApi<CheckResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { distributorCommodityId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据分销商商品id获取分销商商品详情
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getDistributorCommodityInDistributor(
    params: { discountApplyId?: string; distributorCommodityId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDistributorCommodityInDistributor,
    operation?: string
  ): Promise<Response<PortalDistributorCommodityResponse>> {
    return commonRequestApi<PortalDistributorCommodityResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取指定分销商下的分销商品属性列表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getDistributorCommoditySkuPropertyCollectionInDistributor(
    request: DistributorCommoditySkuPropertyCollectionRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDistributorCommoditySkuPropertyCollectionInDistributor,
    operation?: string
  ): Promise<Response<DistributorCommoditySkuPropertyCollectionResponse>> {
    return commonRequestApi<DistributorCommoditySkuPropertyCollectionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 用户域-获取当前分销商指定门户的推广门户信息
   * @param portalId 门户id
   * @return 门户信息
   * @param query 查询 graphql 语法文档
   * @param portalId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTrainingInstitutionPortalInfoByPortalId(
    portalId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTrainingInstitutionPortalInfoByPortalId,
    operation?: string
  ): Promise<Response<DistributorPortalInfoResponse>> {
    return commonRequestApi<DistributorPortalInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 映射属性值名称
   * @param commodityPropertyDtos 商品sku属性
   * @param servicerId 商品网校iD
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async mapPropertyList(
    params: { commodityPropertyDtos?: Array<CommodityPropertyDto>; servicerId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.mapPropertyList,
    operation?: string
  ): Promise<Response<Array<PropertyResponse>>> {
    return commonRequestApi<Array<PropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取指定分销商下的分销商品列表（门户）
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageDistributorCommodityInDistributor(
    params: {
      page?: Page
      request?: PortalDistributorCommodityRequest
      sortRequests?: Array<SaleCommoditySortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageDistributorCommodityInDistributor,
    operation?: string
  ): Promise<Response<PortalDistributorCommodityResponsePage>> {
    return commonRequestApi<PortalDistributorCommodityResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取指定分销商下的分销期别商品列表（门户）
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pagePortalDistributorIssueCommodityInDistributor(
    params: {
      page?: Page
      request?: PortalDistributorIssueCommodityInDistributorRequest
      sortRequests?: Array<SaleCommoditySortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pagePortalDistributorIssueCommodityInDistributor,
    operation?: string
  ): Promise<Response<PortalDistributorIssueCommodityInDistributorResponsePage>> {
    return commonRequestApi<PortalDistributorIssueCommodityInDistributorResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
