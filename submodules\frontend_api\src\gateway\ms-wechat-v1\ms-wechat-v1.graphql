"""独立部署的微服务,K8S服务名:ms-wechat-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getAppletCode(request:GetAppletCodeRequest):GetAppletCodeResponse
	"""获取解密后的手机号
		@param request 加密的数
		@return 解密后的json串
	"""
	getDecodeTelephone(request:GetDecodeTelephoneRequest):TelephoneData
	getUserInfo(request:GetUserInfoRequest):GetUserInfoResponse
	getWXAppletUserInfo(request:WXAppletInfoQueryParam):WXAppletIdInfoResponse
}
type Mutation {
	"""申请微信公众号JS SDK 权限签名
		<p>微信文档地址：https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#62</p>
		@param applyInfo 申请信息
		@return 签名信息
	"""
	applyJsSDKSignature(applyInfo:ApplyJsSDKSignatureRequest):JsSDKSignatureResponse
}
"""微信公众号JS SDK 权限签名申请信息"""
input ApplyJsSDKSignatureRequest @type(value:"com.fjhb.ms.wechat.v1.kernel.gateway.graphql.request.ApplyJsSDKSignatureRequest") {
	"""公众号AppId，不填表示当前项目默认只有一个微信公众号，自动从配置中获取"""
	appId:String
	"""【必填】当前网页的URL，不包含#及其后面部分"""
	url:String
}
"""获取二维码
	@author: zhengp
	@since 2021/9/2 10:16
"""
input GetAppletCodeRequest @type(value:"com.fjhb.ms.wechat.v1.kernel.gateway.graphql.request.GetAppletCodeRequest") {
	"""微信小程序 appId 不填表示当前项目默认只有一个小程序，自动从配置中获取"""
	appId:String
	"""page参数 选填 如果不填写这个字段，默认跳主页面"""
	page:String
	"""scene参数 必填"""
	scene:String
	"""width参数 选填"""
	width:String
	"""自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调，默认 false 选填"""
	autoColor:Boolean
	"""auto_color 为 false 时生效，使用 rgb 设置颜色 例如 {"r":"xxx","g":"xxx","b":"xxx"} 十进制表示 选填"""
	lineColor:String
	"""是否需要透明底色，为 true 时，
		生成透明底色的小程序 选填
	"""
	isHyaline:Boolean
}
"""获取解密手机号
	@author: zhengp 2021/12/2 16:34
"""
input GetDecodeTelephoneRequest @type(value:"com.fjhb.ms.wechat.v1.kernel.gateway.graphql.request.GetDecodeTelephoneRequest") {
	"""用户openId [必填]"""
	openId:String!
	"""加密数据 [必填]"""
	encryptedData:String!
	"""加密算法的初始向量 [必填]"""
	iv:String!
}
"""获取微信用户信息
	@author: zhengp
	@since 2021/9/3 14:16
"""
input GetUserInfoRequest @type(value:"com.fjhb.ms.wechat.v1.kernel.gateway.graphql.request.GetUserInfoRequest") {
	"""微信appId，不填表示当前项目默认只有一个微信开发平台程序，自动从配置中获取"""
	appId:String
	"""授权临时票据code"""
	code:String
	"""国家地区语言版本，默认为中文简体"""
	lang:String
}
"""获取微信opneiId和unionId等信息的入参
	@author: zhengp
	@since 2021/9/9 10:00
"""
input WXAppletInfoQueryParam @type(value:"com.fjhb.ms.wechat.v1.kernel.gateway.graphql.request.WXAppletInfoQueryParam") {
	appId:String
	code:String
	encryptedData:String
	iv:String
}
"""@author: zhengp
	@since 2021/9/10 9:37
"""
type GetAppletCodeResponse @type(value:"com.fjhb.ms.wechat.v1.kernel.gateway.graphql.response.GetAppletCodeResponse") {
	"""二维码存储mfs地址"""
	mfsAddress:String
}
"""获取微信用户信息
	@author: zhengp
	@since 2021/9/3 14:16
"""
type GetUserInfoResponse @type(value:"com.fjhb.ms.wechat.v1.kernel.gateway.graphql.response.GetUserInfoResponse") {
	"""用户统一标识。针对一个微信开放平台帐号下的应用，同一用户的unionid是唯一的。"""
	unionId:String
	"""普通用户的标识，对当前开发者帐号唯一"""
	openId:String
	"""普通用户昵称"""
	nickName:String
	"""普通用户性别，1为男性，2为女性"""
	sex:Int!
	"""国家，如中国为CN"""
	country:String
	"""普通用户个人资料填写的省份"""
	province:String
	"""普通用户个人资料填写的城市"""
	city:String
	"""用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像）"""
	headImageUrl:String
}
"""微信公众号JS SKD 权限签名信息"""
type JsSDKSignatureResponse @type(value:"com.fjhb.ms.wechat.v1.kernel.gateway.graphql.response.JsSDKSignatureResponse") {
	"""生成签名的随机串"""
	nonceStr:String
	"""生成签名的时间戳"""
	timestamp:Long!
	"""签名"""
	signature:String
}
"""手机号信息
	@author: zhengp 2021/12/3 16:21
"""
type TelephoneData @type(value:"com.fjhb.ms.wechat.v1.kernel.gateway.graphql.response.TelephoneData") {
	"""200 成功 500 失败"""
	code:Int!
	msg:String
	data:Result
}
type Result @type(value:"com.fjhb.ms.wechat.v1.kernel.gateway.graphql.response.TelephoneData$Result") {
	"""用户绑定的手机号（国外手机号会有区号）"""
	phoneNumber:String
	"""没有区号的手机号"""
	purePhoneNumber:String
	"""区号"""
	countryCode:String
}
"""@author: zhengp
	@since 2021/9/9 10:02
	获取微信必要信息
"""
type WXAppletIdInfoResponse @type(value:"com.fjhb.ms.wechat.v1.kernel.gateway.graphql.response.WXAppletIdInfoResponse") {
	openId:String
	unionId:String
	accessToken:String
	nickname:String
	refreshToken:String
	sex:String
	headimgurl:String
	purePhoneNumber:String
}

scalar List
