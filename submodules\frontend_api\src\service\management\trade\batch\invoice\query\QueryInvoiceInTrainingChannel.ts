import QueryInvoiceBase from '@api/service/management/trade/batch/invoice/query/vo/QueryInvoiceBase'
import { Page, UiPage } from '@hbfe/common'
import QueryPageInvoiceParam from '@api/service/management/trade/batch/invoice/query/dto/QueryPageInvoiceParam'
import {
  InvoiceAutoBillPolicyResponse,
  InvoiceAssociationInfoRequest,
  OnlineInvoiceRequest,
  OnlineInvoiceSortRequest,
  OnlineInvoiceResponse,
  OnlineInvoiceSortField,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import TradeQueryBackstage from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import InvoiceListResponse from '@api/service/management/trade/batch/invoice/query/vo/InvoiceListResponse'
import UserModule from '@api/service/management/user/UserModule'
import ExportGateWay from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { getOnlineInvoiceInServicer } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage/graphql-importer'
import MsBill from '@api/ms-gateway/ms-bill-v1'
import { InvoiceStatusEnum } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'

export default class QueryInvoiceInTrainingChannel extends QueryInvoiceBase {
  /**
   * 开票总金额
   */
  totalAmount = 0
  /**
   * 发票总税额
   */
  totalTax = 0
  /**
   * 分页查询发票 【增值税电子普通发票 自动开票】
   * @param page 页数
   * @param queryPageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<InvoiceListResponse>
   */
  async onLinePageInvoiceInServicer(
    page: Page,
    queryPageInvoiceParam?: QueryPageInvoiceParam,
    sort: Array<OnlineInvoiceSortRequest> = [
      { field: OnlineInvoiceSortField.INVOICE_CREAT_TIME, policy: SortPolicy.DESC }
    ]
  ): Promise<Array<InvoiceListResponse>> {
    const request = QueryPageInvoiceParam.to(queryPageInvoiceParam)
    if (queryPageInvoiceParam.userId) {
      if (request.associationInfoList[0]) {
        request.associationInfoList[0].buyerIdList = [queryPageInvoiceParam.userId]
      } else {
        request.associationInfoList[0] = new InvoiceAssociationInfoRequest()
      }
      request.associationInfoList[0].buyerIdList = [queryPageInvoiceParam.userId]
    } else if (queryPageInvoiceParam.userName || queryPageInvoiceParam.idCard || queryPageInvoiceParam.phone) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const page = new UiPage()
      page.pageNo = 1
      page.pageSize = 200
      const idList = await queryStudentIdList.queryPageCollectiveList(page, {
        name: queryPageInvoiceParam.userName ? queryPageInvoiceParam.userName : undefined,
        idCard: queryPageInvoiceParam.idCard ? queryPageInvoiceParam.idCard : undefined,
        phone: queryPageInvoiceParam.phone ? queryPageInvoiceParam.phone : undefined
      })
      if (idList.length === 0) {
        page.totalSize = 0
        page.totalPageSize = 0
        return []
      }
      if (request.associationInfoList[0]) {
        request.associationInfoList[0].buyerIdList = idList.map(item => item.userInfo.userId)
      } else {
        request.associationInfoList[0] = new InvoiceAssociationInfoRequest()
        request.associationInfoList[0].buyerIdList = idList.map(item => item.userInfo.userId)
      }
    }
    const params = {
      page,
      request,
      sort
    }
    const result = await TradeQueryBackstage.pageOnlineInvoiceInTrainingChannel(params)
    await this.onLineStatisticInvoiceInServicer(page, request, sort)
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize
    const data = new Array<InvoiceListResponse>()
    const userIds = new Array<string>()
    const blueInvoiceIdList: string[] = [],
      redInvoiceIdList: string[] = []
    result.data.currentPageData.forEach(item => {
      data.push(InvoiceListResponse.from(item))
      userIds.push(item.associationInfoList[0]?.buyer?.userId)
      if (item.basicData?.blueInvoiceItemBillStatus === InvoiceStatusEnum.OPENING)
        blueInvoiceIdList.push(item.invoiceId)
      if (item.basicData?.redInvoiceItemBillStatus === InvoiceStatusEnum.OPENING) redInvoiceIdList.push(item.invoiceId)
    })
    let blueMsgs: Map<string, string>, redMsgs: Map<string, string>
    if (blueInvoiceIdList.length) {
      blueMsgs = await this.queryBlueInvoiceException(blueInvoiceIdList)
    }
    if (redInvoiceIdList.length) {
      redMsgs = await this.queryRedInvoiceException(redInvoiceIdList)
    }
    if (blueMsgs?.size || redMsgs?.size) {
      data.forEach(item => {
        item.blueInvoiceExceptionMsg = blueMsgs?.get(item.invoiceId)
        item.redInvoiceExceptionMsg = redMsgs?.get(item.invoiceId)
      })
    }
    if (userIds.length === 0) {
      return data
    }
    const userIdMap = await this.getUserInfo(userIds)
    data.forEach(item => {
      if (userIdMap.has(item.userId)) {
        const result = userIdMap.get(item.userId)
        item.setUserInfo(result.userName, result.idCard, result.phone, result.email)
      }
    })
    return data
  }
  /**
   * 线上发票导出
   * @param queryPageInvoiceParam 查询参数
   */
  async onLinePageInvoiceInExport(queryPageInvoiceParam?: QueryPageInvoiceParam): Promise<boolean> {
    const request = QueryPageInvoiceParam.to(queryPageInvoiceParam)
    if (queryPageInvoiceParam.userName || queryPageInvoiceParam.idCard || queryPageInvoiceParam.phone) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryPageInvoiceParam.userName
        ? queryPageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryPageInvoiceParam.idCard
        ? queryPageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryPageInvoiceParam.phone
        ? queryPageInvoiceParam.phone
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        if (request.associationInfoList[0]) {
          request.associationInfoList[0].buyerIdList = idList.data
        } else {
          request.associationInfoList[0] = new InvoiceAssociationInfoRequest()
          request.associationInfoList[0].buyerIdList = idList.data
        }
      }
    }
    const result = await ExportGateWay.exportOnlineInvoiceInTrainingChannelForJxjy(request)
    return result.data
  }
  /**
   * statisticInvoiceInServicer
   */
  async onLineStatisticInvoiceInServicer(
    page: Page,
    request?: OnlineInvoiceRequest,
    sort?: Array<OnlineInvoiceSortRequest>
  ) {
    const result = await TradeQueryBackstage.statisticOnlineInvoiceInTrainingChannel(request)
    this.totalTax = result.data.totalTax ? result.data.totalTax : 0
    this.totalAmount = result.data.totalAmount ? result.data.totalAmount : 0
  }
}
