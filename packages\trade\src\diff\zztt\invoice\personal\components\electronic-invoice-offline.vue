<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <!--条件查询-->
      <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
      <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
      <hb-search-wrapper :model="pageQueryParam" @reset="resetCondition">
        <el-form-item label="培训方案" v-if="!isZtlogin">
          <learning-scheme-select-diff v-model="hasSelectSchemeMode"></learning-scheme-select-diff>
        </el-form-item>
        <el-form-item label="培训方案" v-if="isZtlogin">
          <zt-learning-scheme-select v-model="hasSelectSchemeMode"></zt-learning-scheme-select>
        </el-form-item>
        <el-form-item label="期别名称" v-if="showPeriodName">
          <biz-period-select :scheme-id="hasSelectSchemeMode[0].id" v-model="pageQueryParam.periodId" />
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="pageQueryParam.orderNoList" clearable placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="登录账号" v-if="queryShowLoginAccount.isShowLoginAccount">
          <el-input v-model="pageQueryParam.loginAccount" clearable placeholder="请输入省平台id" />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="pageQueryParam.userName" clearable placeholder="请输入购买人姓名" />
        </el-form-item>
        <el-form-item label="证件号">
          <el-input v-model="pageQueryParam.idCard" clearable placeholder="请输入证件号" />
        </el-form-item>
        <template>
          <el-form-item label="手机号">
            <el-input v-model="pageQueryParam.phone" clearable placeholder="请输入购买人手机号" />
          </el-form-item>
          <el-form-item label="发票状态">
            <el-select v-model="pageQueryParam.invoiceStatusList" clearable placeholder="请选择发票状态">
              <el-option v-for="item in blueInvoiceStatus" :label="item.name" :value="item.value" :key="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="冻结状态">
            <el-select v-model="pageQueryParam.invoiceFreezeStatus" clearable filterable placeholder="请选择冻结状态">
              <el-option label="正常" :value="false"></el-option>
              <el-option label="冻结" :value="true"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发票号">
            <el-input v-model="pageQueryParam.invoiceNoList" clearable placeholder="请输入发票号" />
          </el-form-item>
          <el-form-item label="申请开票时间">
            <double-date-picker
              :begin-create-time.sync="pageQueryParam.billStatusChangeTime.billing.begin"
              :end-create-time.sync="pageQueryParam.billStatusChangeTime.billing.end"
              begin-time-placeholder="请选择申请开票时间开始时间"
              end-time-placeholder="请选择申请开票时间结束时间"
            ></double-date-picker>
          </el-form-item>
          <el-form-item label="开票时间">
            <double-date-picker
              :begin-create-time.sync="pageQueryParam.billStatusChangeTime.success.begin"
              :end-create-time.sync="pageQueryParam.billStatusChangeTime.success.end"
              begin-time-placeholder="请选择开票开始时间"
              end-time-placeholder="请选择开票开始时间"
            ></double-date-picker>
          </el-form-item>

          <el-form-item label="销售渠道" v-if="!isZtlogin">
            <el-select v-model="pageQueryParam.saleSource" clearable filterable placeholder="请选择销售渠道">
              <el-option
                v-for="item in saleChannelList"
                :key="item.code"
                :value="item.code"
                :label="item.desc"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="专题名称" v-if="topPicNameFilterShow">
            <el-input v-model="pageQueryParam.specialSubjectName" clearable placeholder="请输入专题进行查询" />
          </el-form-item>
        </template>
        <template slot="actions">
          <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
          <el-button @click="exportSpecialInvoice">导出列表数据</el-button>
          <el-button v-if="!isZtlogin" @click="importElectronicInvoice">导入电子发票</el-button>
        </template>
      </hb-search-wrapper>
      <!--表格-->
      <el-table
        stripe
        :data="pageData"
        max-height="500px"
        class="m-table"
        v-loading="query.loading"
        ref="offlineInvoiceTable"
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="订单号" min-width="220" fixed="left" prop="associationId">
          <template #default="scope">
            {{ scope.row.associationId || '-' }}
            <el-tag type="success" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag>

            <el-tag type="warning" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.distribution"
              >分销推广
            </el-tag>

            <el-tag type="danger" size="small" v-if="scope.row.thirdPartyPlatform">{{
              scope.row.thirdPartyPlatform
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="退款状态" min-width="130">
          <template #default="scope">
            <el-badge
              is-dot
              :type="refundStatusMapType[scope.row.orderReturnStatus]"
              class="badge-status"
              v-if="refundStatusMapName[scope.row.orderReturnStatus]"
            >
              {{ refundStatusMapName[scope.row.orderReturnStatus] }}</el-badge
            >
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="付款金额(元)" width="140" align="right">
          <template #default="scope">
            {{ scope.row.payAmount || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="开票金额(元)" width="140" align="right">
          <template #default="scope">
            {{ scope.row.totalAmount || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="购买人信息" min-width="240">
          <template #default="scope">
            <p>姓名：{{ scope.row.name || '-' }}</p>
            <p>证件号：{{ scope.row.idCard || '-' }}</p>
            <p>手机号：{{ scope.row.phone || '-' }}</p>
          </template>
        </el-table-column>
        <el-table-column label="发票抬头" min-width="300">
          <template #default="scope">【{{ invoiceTitleMapType[scope.row.titleType] }}】{{ scope.row.title }}</template>
        </el-table-column>
        <el-table-column label="统一社会信用代码" min-width="180">
          <template #default="scope">
            {{ scope.row.taxpayerNo || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="申请开票时间" min-width="180">
          <template #default="scope">
            {{ scope.row.applyForDate || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="发票状态" min-width="130">
          <template #default="scope">
            <el-badge
              is-dot
              :type="invoiceStatusMapType[scope.row.invoiceStatus]"
              class="badge-status"
              v-if="invoiceStatusMapName[scope.row.invoiceStatus]"
            >
              {{ invoiceStatusMapName[scope.row.invoiceStatus] }}
            </el-badge>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="开票时间" min-width="180">
          <template #default="scope">
            {{ scope.row.invoiceDate || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="发票号" min-width="120">
          <template #default="scope">
            {{ scope.row.invoiceNo || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="手机号 / 邮箱" min-width="220">
          <template #default="scope">
            {{ scope.row.contactPhone || '-' }} /
            <p>{{ scope.row.contactEmail || '-' }}</p>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="text"
              size="mini"
              v-if="
                (scope.row.invoiceStatus == 0 || scope.row.invoiceStatus == 3) &&
                !scope.row.invoiceFreezeStatus &&
                !isZtlogin
              "
              @click="editInvoice(scope.row.invoiceId)"
              >修改发票信息</el-button
            >
            <el-button type="text" size="mini" @click="invoiceLog(scope.row.invoiceId)">记录</el-button>
            <el-button
              type="text"
              size="mini"
              v-if="
                (scope.row.invoiceStatus === 1 || scope.row.invoiceStatus === 2) &&
                !scope.row.invoiceFreezeStatus &&
                !isZtlogin
              "
              @click="cancelInvoice(scope.row)"
              >作废</el-button
            >
            <el-button
              type="text"
              size="mini"
              v-if="
                (scope.row.invoiceStatus === 0 || scope.row.invoiceStatus === 3) &&
                !scope.row.invoiceFreezeStatus &&
                !isZtlogin
              "
              @click="dealInvoice(scope.row.invoiceId)"
              >处理发票</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
      <el-dialog title="提示" :visible.sync="importSuccessVisible" width="450px" class="m-dialog">
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb">导入成功，是否前往下载数据？</p>
            <p class="f-f13 f-mt5">下载入口：导入任务查看-个人报名增值税电子普通发票（线下开票）</p>
          </div>
        </div>
        <div slot="footer">
          <el-button @click="importSuccessVisible = false">暂 不</el-button>
          <el-button type="primary" @click="goImportDownloadPage">前往下载</el-button>
        </div>
      </el-dialog>
      <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb">导出成功，是否前往下载数据？</p>
            <p class="f-f13 f-mt5">下载入口：导出任务管理-个人报名增值税电子普通发票（线下开票）</p>
          </div>
        </div>
        <div slot="footer">
          <el-button @click="exportSuccessVisible = false">暂 不</el-button>
          <el-button type="primary" @click="goExportDownloadPage">前往下载</el-button>
        </div>
      </el-dialog>
      <deal-invoice-dialog
        :deal-dialog.sync="dealInvoiceVisible"
        :invoice-id="invoiceId"
        @dealSuccess="doSearch"
      ></deal-invoice-dialog>
      <cancel-invoice-dialog
        :cancel-dialog.sync="cancelInvoiceVisible"
        :cancel-item="curInvoiceItem"
        @cancelSuccess="doSearch"
      ></cancel-invoice-dialog>
      <edit-offline-invoice-dialog
        :dialog-ctrl.sync="editInvoiceVisible"
        :invoice-id="invoiceId"
        invoice-type="2"
        @callBack="doSearch"
      ></edit-offline-invoice-dialog>
      <import-invoice-dialog
        :import-dialog.sync="importInvoiceVisible"
        :invoice-type="1"
        @importSuccess="importSuccessVisible = true"
      ></import-invoice-dialog>
      <invoice-log-dialog ref="logDialogRef" :is-auto="false"></invoice-log-dialog>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import {
    InvoiceStatusEnum,
    OrderReturnStatusEnum,
    TitleTypeEnum
  } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import { Query, UiPage } from '@hbfe/common'
  import {
    BillStatusChangeTimeRequest,
    DateScopeRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import EditOfflineInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/edit-offline-invoice-dialog.vue'

  import DealInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/deal-invoice-dialog.vue'
  import CancelInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/cancel-invoice-dialog.vue'
  import ImportInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/import-invoice-dialog.vue'
  import QueryOffLinePageInvoiceParam from '@api/service/diff/management/zztt/trade/invoice/model/QueryOffLinePageInvoiceParam'
  import OffLinePageInvoiceVo from '@api/service/diff/management/zztt/trade/invoice/model/OffLinePageInvoiceResponseVo'
  import InvoiceLogDialog from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/invoice-log-dialog.vue'
  import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/zztt/trade/enums/SaleChannelType'
  import QueryOffLineInvoice from '@api/service/diff/management/zztt/trade/invoice/QueryOffLineInvoice'
  import LearningSchemeSelectDiff from '@hbfe/jxjy-admin-trade/src/diff/zztt/order/personal/components/learning-scheme-select-diff.vue'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryOffLineInvoiceInTrainingChannel from '@api/service/diff/management/zztt/trade/invoice/QueryZzttOffLineInvoiceInTrainingChannel'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import ZtLearningSchemeSelect from '@hbfe/jxjy-admin-trade/src/order/personal/components/zt-learning-scheme-select.vue'

  @Component({
    components: {
      InvoiceLogDialog,
      DoubleDatePicker,
      EditOfflineInvoiceDialog,
      DealInvoiceDialog,
      CancelInvoiceDialog,
      ImportInvoiceDialog,
      LearningSchemeSelectDiff,
      ZtLearningSchemeSelect
    }
  })
  export default class extends Vue {
    @Ref('logDialogRef') logDialogRef: InvoiceLogDialog
    pageQueryParam: QueryOffLinePageInvoiceParam = new QueryOffLinePageInvoiceParam()
    exportQueryParam: QueryOffLinePageInvoiceParam = new QueryOffLinePageInvoiceParam()
    hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
    pageData: Array<OffLinePageInvoiceVo> = new Array<OffLinePageInvoiceVo>()
    page: UiPage
    query: Query = new Query()
    SaleChannelEnum = SaleChannelEnum
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    queryZtInvoice = new QueryOffLineInvoiceInTrainingChannel()
    //当前选中的发票id
    invoiceId = ''
    //导出成功弹窗
    exportSuccessVisible = false
    //修改发票弹窗
    editInvoiceVisible = false
    //操作日志弹窗
    logDialog = false
    //处理发票弹窗
    dealInvoiceVisible = false
    //作废发票弹窗
    cancelInvoiceVisible = false
    //导入电子发票弹窗
    importInvoiceVisible = false
    //导入成功弹窗
    importSuccessVisible = false
    //当前选中作发票
    curInvoiceItem: OffLinePageInvoiceVo = new OffLinePageInvoiceVo()
    //发票状态
    blueInvoiceStatus = [
      {
        name: '待开票',
        value: InvoiceStatusEnum.NOTPTOOPEN
      },
      {
        name: '开票中',
        value: InvoiceStatusEnum.OPENING
      },
      {
        name: '开票成功',
        value: InvoiceStatusEnum.OPEMSUCCESS
      },
      {
        name: '开票失败',
        value: InvoiceStatusEnum.OPENERROR
      }
    ]

    //开票状态
    invoiceStatusMapName = {
      [InvoiceStatusEnum.NOTPTOOPEN]: '待开票',
      [InvoiceStatusEnum.OPENING]: '开票中',
      [InvoiceStatusEnum.OPENERROR]: '开票失败',
      [InvoiceStatusEnum.OPEMSUCCESS]: '开票成功'
    }

    invoiceStatusMapType = {
      [InvoiceStatusEnum.NOTPTOOPEN]: 'info',
      [InvoiceStatusEnum.OPENING]: 'primary',
      [InvoiceStatusEnum.OPENERROR]: 'danger',
      [InvoiceStatusEnum.OPEMSUCCESS]: 'success'
    }

    //退款状态
    refundStatusMapName = {
      [OrderReturnStatusEnum.RETURNSING]: '退款中',
      [OrderReturnStatusEnum.RETURNSUCCESS]: '退款成功'
    }

    refundStatusMapType = {
      [OrderReturnStatusEnum.RETURNSING]: 'primary',
      [OrderReturnStatusEnum.RETURNSUCCESS]: 'success'
    }

    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }
    /**
     * 实例化
     */
    queryOffLineInvoice = new QueryOffLineInvoice()

    // 获取销售渠道列表
    saleChannelList = SaleChannelType.list()
    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount

    TrainingModeEnum = TrainingModeEnum

    get showPeriodName() {
      return [this.TrainingModeEnum.mixed, this.TrainingModeEnum.offline].includes(
        this.hasSelectSchemeMode[0]?.trainingMode?.skuPropertyValueId
      )
    }
    // 培训方案入参
    @Watch('hasSelectSchemeMode', {
      deep: true
    })
    changeScheme(val: Array<HasSelectSchemeMode>) {
      this.pageQueryParam.periodId = ''
    }

    // 专题名称筛选显示
    get topPicNameFilterShow() {
      return (
        this.pageQueryParam.saleSource === SaleChannelEnum.topic ||
        (!this.pageQueryParam.saleSource && this.pageQueryParam.saleSource !== SaleChannelEnum.self)
      )
    }

    constructor() {
      super()
      this.pageQueryParam.billStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.billStatusChangeTime.billing = new DateScopeRequest()
      this.pageQueryParam.billStatusChangeTime.success = new DateScopeRequest()
      if (this.isZtlogin) {
        this.page = new UiPage(this.doSearchZt, this.doSearchZt)
      } else {
        this.page = new UiPage(this.doSearch, this.doSearch)
      }
    }

    async doSearch() {
      this.query.loading = true
      if (this.hasSelectSchemeMode.length) {
        this.pageQueryParam.commoditySkuIdList = this.hasSelectSchemeMode[0].schemeId
      } else {
        this.pageQueryParam.commoditySkuIdList = null
      }
      try {
        this.pageData = await this.queryOffLineInvoice.offLinePageInvoiceInServicer(this.page, this.pageQueryParam)
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['offlineInvoiceTable'] as any)?.doLayout()
        this.query.loading = false
      }
    }
    async doSearchZt() {
      this.query.loading = true
      if (this.hasSelectSchemeMode.length) {
        this.pageQueryParam.commoditySkuIdList = this.hasSelectSchemeMode[0].schemeId
      } else {
        this.pageQueryParam.commoditySkuIdList = null
      }
      try {
        this.pageData = await this.offLinePageInvoiceInZtServicer()
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['offlineInvoiceTable'] as any)?.doLayout()
        this.query.loading = false
      }
    }
    async offLinePageInvoiceInZtServicer() {
      return await this.queryZtInvoice.offLineZzttPageInvoiceInServicer(this.page, this.pageQueryParam)
    }
    async resetCondition() {
      this.page.pageNo = 1
      //this.pageQueryParam.commoditySkuIdList = undefined
      this.pageQueryParam = new QueryOffLinePageInvoiceParam()
      this.pageQueryParam.billStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.billStatusChangeTime.billing = new DateScopeRequest()
      this.pageQueryParam.billStatusChangeTime.success = new DateScopeRequest()
      this.hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
      /*this.pageQueryParam.orderNoList = undefined
      this.pageQueryParam.userName = undefined
      this.pageQueryParam.idCard = undefined
      this.pageQueryParam.phone = undefined
      this.pageQueryParam.invoiceStatusList = undefined
      this.pageQueryParam.invoiceFreezeStatus = undefined
      this.pageQueryParam.invoiceNoList = undefined
      this.pageQueryParam.billStatusChangeTime.billing.begin = undefined
      this.pageQueryParam.billStatusChangeTime.billing.end = undefined
      this.pageQueryParam.billStatusChangeTime.success.begin = undefined
      this.pageQueryParam.billStatusChangeTime.success.end = undefined*/
      if (this.isZtlogin) {
        await this.doSearchZt()
      } else {
        await this.doSearch()
      }
    }

    //修改发票信息
    editInvoice(id: string) {
      this.invoiceId = id
      this.editInvoiceVisible = true
    }

    //处理发票
    dealInvoice(id: string) {
      this.invoiceId = id
      this.dealInvoiceVisible = true
    }

    //作废发票
    cancelInvoice(item: OffLinePageInvoiceVo) {
      this.curInvoiceItem = item
      this.cancelInvoiceVisible = true
    }

    //发票操作记录
    invoiceLog(id: string) {
      this.logDialogRef.init(id)
    }

    // 下载导入数据
    goImportDownloadPage() {
      this.importSuccessVisible = false
      this.$router.push({
        path: '/training/task/importtask',
        query: { type: '个人报名增值税电子普通发票（线下开票）' }
      })
    }

    // 导出列表数据
    async exportSpecialInvoice() {
      this.exportQueryParam = Object.assign(new QueryOffLinePageInvoiceParam(), this.pageQueryParam)

      try {
        let res
        if (this.isZtlogin) {
          res = await this.offLinePageInvoiceInZtExport()
        } else {
          res = await this.queryOffLineInvoice.offLinePageInvoiceInExport(this.exportQueryParam)
        }
        if (res) {
          this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }

    /**
     * 导出列表数据
     */
    async offLinePageInvoiceInZtExport() {
      return await this.queryZtInvoice.offLinePageInvoiceInExport(this.exportQueryParam)
    }
    // 下载导出数据
    goExportDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportNormalOfflineInvoice' }
      })
    }

    importElectronicInvoice() {
      //导入电子发票
      this.importInvoiceVisible = true
    }

    async created() {
      if (this.isZtlogin) {
        await this.doSearchZt()
      } else {
        await this.doSearch()
      }
    }
  }
</script>
