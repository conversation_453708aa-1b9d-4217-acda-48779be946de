import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * 转换状态
 */
export enum CoursewareTypeEnum {
  // undefined = '0',
  // 文档
  document = '1',
  // 单视频
  video = '2',
  // 课件包
  // package = 3,
  // 多媒体
  videoPackage = '3'
}

class CoursewareType extends AbstractEnum<CoursewareTypeEnum> {
  static enum = CoursewareTypeEnum

  constructor(status?: CoursewareTypeEnum) {
    super()
    this.current = status
    // this.map.set(CoursewareTypeEnum.undefined, '-')
    this.map.set(CoursewareTypeEnum.document, '文档')
    this.map.set(CoursewareTypeEnum.video, '单视频')
    this.map.set(CoursewareTypeEnum.videoPackage, '视频包')
  }
}

export default CoursewareType
