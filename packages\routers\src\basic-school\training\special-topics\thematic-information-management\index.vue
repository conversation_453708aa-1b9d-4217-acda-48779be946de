<route-meta>
{
"isMenu": true,
"title": "专题资讯管理",
"sort": 3
}
</route-meta>

<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, ZTGLY } from '@/models/RoleTypes'
  import info from '@hbfe/jxjy-admin-specialTopics/src/thematic-information-management/index.vue'
  @RoleTypeDecorator({
    query: [WXGLY],
    create: [WXGLY],
    category: [WXGLY],
    copy: [WXGLY],
    publish: [WXGLY],
    modify: [WXGLY],
    remove: [WXGLY],
    draft: [WXGLY],
    check: [ZTGLY],
    queryZt: [ZTGLY]
  })
  export default class extends info {}
</script>

<style scoped></style>
