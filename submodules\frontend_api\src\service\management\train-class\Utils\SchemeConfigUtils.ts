import MsSchemeQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

/**
 * @description
 */
class SchemeConfigUtils {
  /**
   * 获取培训班属性
   */
  static async queryConfig(schemeId: string): Promise<any> {
    if (!schemeId) return null
    //获取培训班配置模板jsonString
    const res = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInServicer({
      schemeId: schemeId,
      needField: [
        'registerBeginDate',
        'registerEndDate',
        'trainingBeginDate',
        'trainingEndDate',
        'type',
        'assessSetting.learningResults'
      ]
    })
    let jsonObj
    try {
      jsonObj = JSON.parse(res.data.schemeConfig)
    } catch (e) {
      return ''
    }
    return jsonObj
  }
  /**
   * 获取培训班属性
   */
  async queryConfigInDistributor(schemeId: string): Promise<any> {
    if (!schemeId) return null
    //获取培训班配置模板jsonString
    const res = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInDistributor({
      schemeId: schemeId,
      needField: [
        'registerBeginDate',
        'registerEndDate',
        'trainingBeginDate',
        'trainingEndDate',
        'type',
        'assessSetting.learningResults'
      ]
    })
    let jsonObj
    try {
      jsonObj = JSON.parse(res.data.schemeConfig)
    } catch (e) {
      return ''
    }
    return jsonObj
  }
}

export default SchemeConfigUtils
