import { Page } from '@hbfe/common'
import MsSchemeQueryFrontGatewayCourseLearningBackstage, {
  SchemeConfigResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

/**
 * @description
 */
class ConfigJsonUtil {
  /**
   * 字段列表
   */
  static defaultNeedFields = [
    'name',
    'registerBeginDate',
    'registerEndDate',
    'trainingBeginDate',
    'trainingEndDate',
    'type',
    'assessSetting.learningResults',
    'extendProperties'
  ]

  /**
   * 批量查询方案json配置信息
   * @description 根据培训方案id
   * @param schemeIdList 培训方案id集合
   * @param needFields 需要的字段
   * @param role 角色类型
   */
  async batchQuerySchemeJsonConfigMapBySchemeId(
    schemeIdList: string[],
    needFields?: string[]
  ): Promise<Map<string, any>> {
    const result = new Map<string, any>()
    // 没有方案id集合或者方案id集合为空直接返回
    if (!schemeIdList || !schemeIdList.length) return result
    // 获取请求列表
    const reqList = Array(Math.ceil(schemeIdList.length / 200)).fill('')
    // 获取返回值列表
    const respList = await Promise.all(
      reqList.map(async (item, index) => {
        const params: Record<string, any> = {
          page: new Page(index + 1, 200),
          schemeIds: schemeIdList.slice(index * 200, (index + 1) * 200)
        }
        if (needFields) params.needField = needFields
        return MsSchemeQueryFrontGatewayCourseLearningBackstage.pageSchemeConfigInServicer(params)
      })
    )
    respList.forEach(tempResp => {
      if (tempResp.status?.isSuccess() && tempResp.data?.currentPageData && tempResp.data?.currentPageData?.length) {
        const metadata = tempResp.data.currentPageData
        metadata.forEach(item => {
          // json字符串需要转换
          let jsonObj: { [key: string]: any } = {}
          const json = item.schemeConfig
          if (json) {
            jsonObj = JSON.parse(json)
          }
          jsonObj.resourceServicerId = item.owner?.servicerId || ''
          jsonObj.resourceUnitId = item.owner?.unitId || ''
          result.set(item.schemeId, jsonObj)
        })
      }
    })
    return result
  }

  /**
   * 批量查询方案json配置信息
   * @description 根据培训方案id
   * @param schemeIdList 培训方案id集合
   * @param needFields 需要的字段
   * @param role 角色类型
   */
  async batchQuerySchemeJsonConfigMapBySchemeIdInDistributor(
    schemeIdList: string[],
    needFields?: string[]
  ): Promise<Map<string, any>> {
    const result = new Map<string, any>()
    // 没有方案id集合或者方案id集合为空直接返回
    if (!schemeIdList || !schemeIdList.length) return result
    // 获取请求列表
    const reqList = Array(Math.ceil(schemeIdList.length / 200)).fill('')
    // 获取返回值列表
    const respList = await Promise.all(
      reqList.map(async (item, index) => {
        const params: Record<string, any> = {
          page: new Page(index + 1, 200),
          schemeIds: schemeIdList.slice(index * 200, (index + 1) * 200)
        }
        if (needFields) params.needField = needFields
        return MsSchemeQueryFrontGatewayCourseLearningBackstage.pageSchemeConfigInDistributor(params)
      })
    )
    respList.forEach(tempResp => {
      if (tempResp.status?.isSuccess() && tempResp.data?.currentPageData && tempResp.data?.currentPageData?.length) {
        const metadata = tempResp.data.currentPageData
        metadata.forEach(item => {
          // json字符串需要转换
          let jsonObj: { [key: string]: any } = {}
          const json = item.schemeConfig
          if (json) {
            jsonObj = JSON.parse(json)
          }
          jsonObj.resourceServicerId = item.owner?.servicerId || ''
          jsonObj.resourceUnitId = item.owner?.unitId || ''
          result.set(item.schemeId, jsonObj)
        })
      }
    })
    return result
  }

  /**
   * 批量查询方案json配置信息
   * @description 根据培训方案id
   * @param schemeIdList 培训方案id集合
   * @param needFields 需要的字段
   */
  async batchQuerySchemeJsonConfigRespMap(
    schemeIdList: string[],
    needFields?: string[]
  ): Promise<Map<string, SchemeConfigResponse>> {
    const result = new Map<string, SchemeConfigResponse>()
    // 没有方案id集合或者方案id集合为空直接返回
    if (!schemeIdList || !schemeIdList.length) return result
    // 获取请求列表
    const reqList = Array(Math.ceil(schemeIdList.length / 200)).fill('')
    // 获取返回值列表
    const respList = await Promise.all(
      reqList.map(async (item, index) => {
        const params: Record<string, any> = {
          page: new Page(index + 1, 200),
          schemeIds: schemeIdList.slice(index * 200, (index + 1) * 200)
        }
        if (needFields && needFields.length) params.needField = needFields
        return MsSchemeQueryFrontGatewayCourseLearningBackstage.pageSchemeConfigInServicer(params)
      })
    )
    respList.forEach(tempResp => {
      if (tempResp.status?.isSuccess() && tempResp.data?.currentPageData && tempResp.data?.currentPageData?.length) {
        const metadata = tempResp.data.currentPageData
        metadata.forEach(item => {
          result.set(item.schemeId, item)
        })
      }
    })
    return result
  }
}

export default ConfigJsonUtil
