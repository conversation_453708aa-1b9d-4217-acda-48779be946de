import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-identity-authentication-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-identity-authentication-secure-config-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class ApplicationDomain {
  /**
   * 应用类型【必填】
   */
  applicationType?: number
  /**
   * 应用方类型【必填】
   */
  applicationMemberType?: number
}

/**
 * <AUTHOR>
 */
export class CredentialRequest {
  /**
   * 初始token
   */
  token: string
  /**
   * 授权类型
@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
identity_auth_token:身份凭证Token(默认授权类型)
identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
   */
  grantType?: string
}

/**
 * 失败认证配置
 */
export class FailedAuthConfig {
  /**
   * 失败认证配置id
   */
  id?: string
  /**
   * 适配的应用域【必填】
   */
  applicationDomains?: Array<ApplicationDomain>
  /**
   * 适配的凭据类型【必填】
   */
  credentialTypes?: Array<number>
  /**
   * 认证失败次数检测周期（分钟）【必填】
   */
  failedAuthAttemptDuration?: number
  /**
   * 认证失败次数上限（次）【必填】
   */
  failedAuthAttemptUpperLimit?: number
  /**
   * 达到认证失败次数上限后账户锁定的时间（分钟）【必填】
   */
  accountLockDuration?: number
  /**
   * 安全级别（默认为3）【必填】
   */
  securityLevel?: number
  /**
   * 是否启用(默认false)【必填】
   */
  enabled?: boolean
}

/**
 * 更换密码配置
 */
export class PasswordChangeConfig {
  /**
   * 更换密码配置id
   */
  id?: string
  /**
   * 适配的应用域【必填】
   */
  applicationDomains?: Array<ApplicationDomain>
  /**
   * 适配的凭据类型【必填】
   */
  credentialTypes?: Array<number>
  /**
   * 更换密码周期（天）【必填】
   */
  passwordChangeCycle?: number
  /**
   * 安全级别（默认为3）【必填】
   */
  securityLevel?: number
  /**
   * 是否启用(默认false)【必填】
   */
  enabled?: boolean
}

export class SaveSSOSecureConfigRequest {
  /**
   * 短信认证配置【必填】
   */
  smsCodeAuthConfig?: SmsCodeAuthConfig
  /**
   * 更换密码配置【必填】
   */
  passwordChangeConfig?: PasswordChangeConfig
  /**
   * 失败认证配置【必填】
   */
  failedAuthConfig?: FailedAuthConfig
}

/**
 * 短信认证配置
 */
export class SmsCodeAuthConfig {
  /**
   * 短信认证配置id
   */
  id?: string
  /**
   * 适配的应用域【必填】
   */
  applicationDomains?: Array<ApplicationDomain>
  /**
   * 适配的凭据类型【必填】
   */
  credentialTypes?: Array<number>
  /**
   * 是否启用(默认false)【必填】
   */
  enabled?: boolean
}

export class ApplicationDomainData {
  /**
   * 应用类型【必填】
   */
  applicationType: number
  /**
   * 应用方类型【必填】
   */
  applicationMemberType: number
}

/**
 * 失败认证配置
 */
export class FailedAuthConfigData {
  /**
   * 失败认证配置id
   */
  id: string
  /**
   * 适配的应用域【必填】
   */
  applicationDomainData: Array<ApplicationDomainData>
  /**
   * 适配的凭据类型【必填】
   */
  credentialTypes: Array<number>
  /**
   * 认证失败次数检测周期（分钟）【必填】
   */
  failedAuthAttemptDuration: number
  /**
   * 认证失败次数上限（次）【必填】
   */
  failedAuthAttemptUpperLimit: number
  /**
   * 达到认证失败次数上限后账户锁定的时间（分钟）【必填】
   */
  accountLockDuration: number
  /**
   * 安全级别（默认为3）【必填】
   */
  securityLevel: number
  /**
   * 是否启用(默认false)【必填】
   */
  enabled: boolean
}

export class FailedAuthConfigResponse {
  /**
   * 认证失败次数检测周期（分钟）
   */
  failedAuthAttemptDuration: number
  /**
   * 认证失败次数上限（次）
   */
  failedAuthAttemptUpperLimit: number
  /**
   * 达到认证失败次数上限后账户锁定的时间（分钟）
   */
  accountLockDuration: number
  /**
   * 安全级别
   */
  securityLevel: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 状态码
@see IdentityAuthenticationStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

export class GetSSOSecureConfigResponse {
  /**
   * 短信认证配置【必填】
   */
  smsCodeAuthConfigData: Array<SmsCodeAuthConfigData>
  /**
   * 更换密码配置【必填】
   */
  passwordChangeConfigData: Array<PasswordChangeConfigData>
  /**
   * 失败认证配置【必填】
   */
  failedAuthConfigData: Array<FailedAuthConfigData>
}

/**
 * 更换密码配置
 */
export class PasswordChangeConfigData {
  /**
   * 更换密码配置id
   */
  id: string
  /**
   * 适配的应用域【必填】
   */
  applicationDomainData: Array<ApplicationDomainData>
  /**
   * 适配的凭据类型【必填】
   */
  credentialTypes: Array<number>
  /**
   * 更换密码周期（天）【必填】
   */
  passwordChangeCycle: number
  /**
   * 安全级别（默认为3）【必填】
   */
  securityLevel: number
  /**
   * 是否启用(默认false)【必填】
   */
  enabled: boolean
}

export class SaveSSOSecureConfigResponse {
  /**
   * 状态码
@see IdentityAuthenticationStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * 短信认证配置
 */
export class SmsCodeAuthConfigData {
  /**
   * 短信认证配置id
   */
  id: string
  /**
   * 适配的应用域【必填】
   */
  applicationDomainData: Array<ApplicationDomainData>
  /**
   * 适配的凭据类型【必填】
   */
  credentialTypes: Array<number>
  /**
   * 是否启用(默认false)【必填】
   */
  enabled: boolean
}

/**
 * 短信验证码认证配置响应
<AUTHOR>
 */
export class SmsCodeAuthConfigResponse {
  /**
   * 配置是否启用
   */
  enabled: boolean
  /**
   * 状态码
@see IdentityAuthenticationStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取失败认证配置
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getFailedAuthConfig(
    request: CredentialRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getFailedAuthConfig,
    operation?: string
  ): Promise<Response<FailedAuthConfigResponse>> {
    return commonRequestApi<FailedAuthConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取单点安全配置(获取当前登录主体的单点安全配置信息)
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSSOSecureConfig(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSSOSecureConfig,
    operation?: string
  ): Promise<Response<GetSSOSecureConfigResponse>> {
    return commonRequestApi<GetSSOSecureConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取短信认证配置
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getSmsCodeAuthConfig(
    request: CredentialRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSmsCodeAuthConfig,
    operation?: string
  ): Promise<Response<SmsCodeAuthConfigResponse>> {
    return commonRequestApi<SmsCodeAuthConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 保存单点安全配置
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveSSOSecureConfig(
    request: SaveSSOSecureConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveSSOSecureConfig,
    operation?: string
  ): Promise<Response<SaveSSOSecureConfigResponse>> {
    return commonRequestApi<SaveSSOSecureConfigResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
