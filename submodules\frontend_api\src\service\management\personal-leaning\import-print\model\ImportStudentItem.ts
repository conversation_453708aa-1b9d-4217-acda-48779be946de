import PlatformCertificate, { ImportedData } from '@api/platform-gateway/platform-certificate-v1'
import { ImportStatusEnum } from '@api/service/management/personal-leaning/import-print/enums/ImportStatus'
export default class ImportStudentItem {
  /**
   * 导入学员打印表id
   */
  id = ''
  /**
   * 学员姓名
   */
  name = ''
  /**
   * 证件号
   */
  idCard = ''
  /**
   * 所属方案
   */
  schemeId = ''
  /**
   * 所属方案名称
   */
  schemeName = ''
  /**
   * 失败原因
   */
  failReason = ''
  /**
   * 导入状态
   */
  importStatus: ImportStatusEnum = null

  static from(dto: ImportedData) {
    const item = new ImportStudentItem()
    item.id = dto.id
    item.name = dto.name
    item.idCard = dto.idCard
    item.schemeName = dto.schemeName
    item.importStatus = dto.importStatus
    item.failReason = dto.reasonForFailure
    return item
  }

  /**
   * 移除
   */
  async remove() {
    const res = await PlatformCertificate.printTheListRemoveStudent({ id: this.id })
    return res.status
  }
}
