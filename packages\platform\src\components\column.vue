<template>
  <el-card
    v-if="$hasPermission('column')"
    desc="栏目设置"
    actions="created,securityPort"
    shadow="never"
    class="m-card f-mb15"
  >
    <div class="m-web-column">
      <div class="m-tit">
        <span class="tit-txt">一级栏目</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="6" v-for="column in columnList" :key="column.id">
          <div class="item">
            <div class="item-bd">
              <div class="f-flex f-flex-sub f-align-start">
                <template v-if="!column.onEditMode">
                  <span class="name">{{ column.name }}</span>
                  <el-tag type="danger" v-if="column.isInformation" size="mini">资讯类别</el-tag>
                </template>
                <template v-else>
                  <el-form :model="column" :rules="formRule" ref="itemForm" :id="column.id">
                    <el-form-item prop="tempoName">
                      <el-input
                        style="top:10px"
                        size="medium"
                        :maxlength="10"
                        v-model="column.tempoName"
                        placeholder="请输入名称"
                        clearable
                        @keydown.native.enter="doSave(column)"
                        v-if="column.onEditMode"
                      />
                    </el-form-item>
                  </el-form>
                </template>
              </div>
            </div>
            <div class="item-ft">
              <template v-if="column.hasChildren && !column.onEditMode">
                <template v-if="column.onShowChildren">
                  <el-button type="text" @click="closeSecondaryColumn(column)">收起二级栏目</el-button>
                </template>
                <template v-else>
                  <el-button type="text" @click="secondaryColumn(column)">展开二级栏目</el-button>
                </template>
              </template>
              <template
                v-if="$hasPermission('editColumn')"
                desc="栏目设置（编辑）"
                actions="column.enableEdit,column.cancelEdit,doSave"
              >
                <template v-if="!column.onEditMode">
                  <el-button type="text" @click="column.enableEdit()">修改名称</el-button>
                </template>
                <template v-else>
                  <el-button type="text" v-if="!column.onSaving" @click="column.cancelEdit()">取消</el-button>
                  <el-button type="text" :loading="column.onSaving" @click="doSave(column)">保存</el-button>
                </template>
              </template>
            </div>
          </div>
        </el-col>
      </el-row>
      <div class="m-tit" v-if="showSecondaryColumn">
        <span class="tit-txt">二级栏目</span>
        <!-- <a href="#" class="f-link f-cb f-ml10">[收起]</a> -->
      </div>
      <el-row :gutter="15" v-if="showSecondaryColumn">
        <el-col :span="6" v-for="column in activeList.children" :key="column.id">
          <div class="item">
            <div class="item-hd">
              <el-tag type="info" size="mini" class="f-mr5">父级</el-tag>
              {{ activeList.name }}
            </div>
            <div class="item-bd">
              <div class="f-flex f-flex-sub f-align-start">
                <template v-if="!column.onEditMode">
                  <span class="name">{{ column.name }}</span>
                </template>
                <template v-else>
                  <el-form :model="column" :rules="formRule" ref="itemForm" :id="column.id">
                    <el-form-item prop="tempoName">
                      <el-input
                        size="mini"
                        :maxlength="10"
                        v-model="column.tempoName"
                        placeholder="请输入名称"
                        clearable
                        @keyup.native.enter="doSave(column)"
                        v-if="column.onEditMode"
                      />
                    </el-form-item>
                  </el-form>
                </template>
              </div>
            </div>

            <div class="item-ft">
              <template
                v-if="$hasPermission('editColumn')"
                desc="栏目设置（编辑）"
                actions="column.doEnable,column.doDisable,column.enableEdit,doSave"
              >
                <template v-if="!column.onEditMode">
                  <template v-if="!column.isBuildIn">
                    <template v-if="!column.enable">
                      <el-button type="text" @click="column.doEnable()" :loading="column.onSaving">启用</el-button>
                    </template>
                    <template v-else>
                      <el-button type="text" @click="column.doDisable()" :loading="column.onSaving">禁用</el-button>
                    </template>
                  </template>
                  <el-button type="text" @click="column.enableEdit()">修改名称</el-button>
                </template>
                <template v-else>
                  <el-button type="text" v-if="!column.onSaving" @click="cancel(column)">取消</el-button>
                  <el-button type="text" :loading="column.onSaving" @click="doSave(column)">保存</el-button>
                </template>
              </template>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <!-- 新增 -->
          <template v-if="$hasPermission('editColumn')" desc="栏目设置（编辑）">
            <div class="item add" @click="active.addChildren()">
              新增二级栏目
            </div>
          </template>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script lang="ts">
  import QueryColumn from '@api/service/management/online-school-config/column/query/QueryColumn'
  import Column from '@api/service/management/online-school-config/column/query/vo/Column'
  import { ElForm } from 'element-ui/types/form'
  import { Component, Vue } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    formRule = {
      tempoName: [
        {
          required: true,
          message: '请输入名称',
          trigger: 'blur'
        }
      ]
    }
    queryColumn: QueryColumn = new QueryColumn()
    columnList: Array<Column> = new Array<Column>()
    activeList: Column = new Column() // 当前一级栏目的二级栏目数据
    showSecondaryColumn = false // 二级栏目显隐

    async securityPort() {
      const query = new Column()
      await query.enableEdit()
      await query.cancelEdit()
      await query.doEnable()
      await query.doDisable()
    }

    async created() {
      this.columnList = await this.queryColumn.queryList()
    }
    // 二级栏目
    secondaryColumn(item: Column) {
      this.columnList.map(column => {
        if (column.id === item.id) {
          column.onShowChildren = true
          this.activeList = column
        } else {
          column.onShowChildren = false
        }
      })
      this.showSecondaryColumn = this.activeList.children.length > 0 ? true : false
    }
    // 关闭二级栏目
    closeSecondaryColumn(item: Column) {
      this.showSecondaryColumn = false
      item.onShowChildren = false
    }

    async doSave(column: Column) {
      const $itemForm = this.$refs.itemForm as Array<ElForm>
      const theForm = $itemForm.find(form => {
        return form.$attrs.id === column.id
      })
      theForm.validate(async validateResult => {
        if (validateResult) {
          try {
            if (column.status === 1) {
              await column.doModifyName()
              this.$message.success('修改成功')
            } else if (column.status === 0) {
              await column.doCreate()
              this.$message.success('创建成功')
            }
          } catch (e) {
            this.$message.error(e.message)
          }
        }
      })
    }

    cancel(column: Column) {
      if (column.isInCache()) {
        this.active.removeChildren(column.id)
      } else {
        column.cancelEdit()
      }
    }

    get active() {
      return this.columnList.find(column => {
        return column.onShowChildren
      })
    }
  }
</script>
