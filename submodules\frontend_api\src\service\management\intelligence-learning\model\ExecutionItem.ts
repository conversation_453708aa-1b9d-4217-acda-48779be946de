import TrackingItem from '@api/service/management/intelligence-learning/model/TrackingItem'
import ExecutionLogItem from '@api/service/management/intelligence-learning/model/ExecutionLogItem'
import ArrangeLogItem from '@api/service/management/intelligence-learning/model/ArrangeLogItem'
import TaskStatusType, { TaskStatusEnum } from '@api/service/management/intelligence-learning/enum/TaskStatusEnum'
import { ResponseStatus } from '@hbfe/common'
import { RestartTaskTypeEnum } from '@api/service/management/intelligence-learning/enum/RestartTaskTypeEnum'
import { StudentAutoLearningTaskResult } from '@api/ms-gateway/ms-autolearning-student-auto-learning-task-result-v1'
import MsAutoLearningLog from '@api/ms-gateway/ms-autolearning-log-v1'
import MsAutoLearning, { RestartAutoLearningTaskRequest } from '@api/ms-gateway/ms-autolearning-v1'

/**
 * 执行情况Item
 */
export default class ExecutionItem extends TrackingItem {
  /**
   * 任务执行状态
   */
  taskStatus: TaskStatusType = null
  /**
   * 更新时间
   */
  updateTime = ''
  /**
   * 期望开始学习时间
   */
  expectStartTime = ''
  /**
   * 自动学习主任务ID
   */
  mainTaskId = ''
  /**
   * 当前自动学习任务ID
   */
  currentTaskId = ''
  /**
   * 学员自动学习任务结果ID
   */
  studentAutoLearningTaskResultId = ''
  /**
   * 学号
   */
  studentNo = ''

  /**
   * 查询编排日志列表
   */
  async queryArrangeLogList(studentAutoLearningTaskResultId = this.studentAutoLearningTaskResultId) {
    const { status, data } = await MsAutoLearningLog.queryAutoLearningArrangeLog(studentAutoLearningTaskResultId)
    if (status.isSuccess() && data) {
      const list = data.map((item) => ArrangeLogItem.from(item))
      // 是否多条重启任务
      if (data.filter((item) => item.type === 1)?.length > 1) {
        list.forEach((item) => {
          if (item.arrangeNum) item.taskType = `第${item.arrangeNum}次${item.taskType}`
        })
      }
      return list
    } else {
      return [] as ArrangeLogItem[]
    }
  }

  /**
   * 查看执行日志列表
   */
  async queryExecutionLogList(studentAutoLearningTaskResultId = this.studentAutoLearningTaskResultId) {
    const { status, data } = await MsAutoLearningLog.queryAutoLearningExecuteLog(studentAutoLearningTaskResultId)
    if (status.isSuccess() && data) {
      return data.map((item) => ExecutionLogItem.from(item))
    } else {
      return [] as ExecutionLogItem[]
    }
  }

  /**
   * 查询上传智能学习期望开始时间
   */
  async queryUploadExpectStartTime() {
    if (!this.currentTaskId && !this.mainTaskId) return new ResponseStatus(500, '任务ID为空')
    const { status, data } = await MsAutoLearning.queryLastTimeExpectationStartStudyDate({
      autoLearningMainTaskId: this.currentTaskId || this.mainTaskId
    })
    if (status.isSuccess() && data) {
      this.expectStartTime = data.expectationStartStudyDate
    }
    return status
  }

  /**
   * 重启任务
   * @param params type: 期望开始学习时间类型; startTime: 自定义时间
   * @return {Promise<ResponseStatus>}
   */
  async restartTask(params: { type: RestartTaskTypeEnum; startTime?: string }) {
    if (!this.studentAutoLearningTaskResultId) return new ResponseStatus(500, '任务ID为空')
    const request = new RestartAutoLearningTaskRequest()
    request.studentAutoLearningTaskResultId = this.studentAutoLearningTaskResultId
    request.studyStartTimeConfigTypes = params.type
    if (params.type === RestartTaskTypeEnum.custom) {
      request.studyStartTime = params.startTime
    }
    const { status, data } = await MsAutoLearning.restartAutoLearningTask(request)
    if (status.isSuccess()) {
      return new ResponseStatus(Number(data.code), data.msg)
    } else {
      return status
    }
  }

  /**
   * 后端返回数据转换前端模型
   */
  static from(dto: StudentAutoLearningTaskResult) {
    const temp = new ExecutionItem()
    temp.name = dto.name
    if (dto.result == 7) {
      temp.taskStatus = new TaskStatusType(TaskStatusEnum.stop)
    } else if (dto.result == 8) {
      temp.taskStatus = new TaskStatusType(TaskStatusEnum.un_start)
    } else {
      temp.taskStatus = new TaskStatusType(dto.result)
    }
    temp.idCard = dto.idCard
    temp.phone = dto.phone
    temp.trainingSchemeName = dto.learningSchemeName
    temp.currentTaskId = dto.currentMainTaskId
    temp.studentNo = dto.studentNo
    temp.startTime = dto.createdTime
    temp.mainTaskId = dto.mainTaskId
    temp.updateTime = dto.updatedTime
    temp.studentAutoLearningTaskResultId = dto.studentAutoLearningTaskResultId
    return temp
  }
}
