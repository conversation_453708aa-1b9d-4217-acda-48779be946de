<route-meta>
{
"isMenu": true,
"title": "公需课课程维护",
"sort": 5
}
</route-meta>
<script lang="ts">
  import SchemeCourseMaintenance from '@hbfe/jxjy-admin-scheme/src/diff/byzj/course-maintenance.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY],
    import: [WXGLY],
    remove: [WXGLY]
  })
  export default class extends SchemeCourseMaintenance {}
</script>
