<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2024-09-25 16:26:58
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2024-12-30 15:04:33
-->
<route-meta>
{
"isMenu": true,
"title": "个人报名对账",
"sort": 1,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>

<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { FXS, GYS, NZFXS, NZFXSJCB, WXGLY, ZTGLY } from '@/models/RoleTypes'
  import ReconciliationPersonalIndex from '@hbfe/jxjy-admin-trade/src/reconciliation/personal/index.vue'

  @RoleTypeDecorator({
    orderReconciliation: [WXGLY, FXS, GYS],
    orderReconciliationFx: [NZFXS, NZFXSJCB],
    orderReconciliationZt: [ZTGLY],
    refundReconciliation: [WXGLY, FXS, GYS],
    refundReconciliationFx: [NZFXS, NZFXSJCB],
    refundReconciliationZt: [ZTGLY],
    editInvoicePopup: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB],
    export: [WXGLY, FXS, GYS],
    exportFx: [NZFXS, NZFXSJCB],
    exportZt: [ZTGLY],
    query: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    doSearch: [WXGLY, FXS, GYS],
    doSearchfx: [NZFXS, NZFXSJCB],
    doSearchzt: [ZTGLY]
  })
  export default class extends ReconciliationPersonalIndex {}
</script>
