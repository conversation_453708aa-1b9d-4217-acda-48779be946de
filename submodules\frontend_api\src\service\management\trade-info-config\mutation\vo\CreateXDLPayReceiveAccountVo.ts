import CreateReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/CreateReceiveAccountVo'
import MutationReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/MutationReceiveAccountVo'
import { CreateReceiveAccountRequest, ReceiveAccountExtProperty } from '@api/ms-gateway/ms-trade-configuration-v1'

export default class CreateXDLPayReceiveAccountVo extends CreateReceiveAccountVo {
  /**
   * 支付账号类型id
   * 支付宝:ALIPAY
   * 微信：WXPAY
   * 支付宝H5:ALIPAYH5
   * 微信H5：WXPAYH5
   */
  paymentChannelId = ''
  /**
   * 支付商户号
   */
  payMerchantId = ''
  /**
   * 代理商号
   */
  proxyId = ''
  /**
   * 密钥
   */
  xdlPrivateKey = ''
  constructor(type: string) {
    super()
    this.paymentChannelId = type
  }

  from(mutationReceiveAccountVo: MutationReceiveAccountVo) {
    this.accountType = mutationReceiveAccountVo.accountType
    this.accountNo = mutationReceiveAccountVo.accountNo
    this.accountName = mutationReceiveAccountVo.accountName
    this.qrScanPrompt = mutationReceiveAccountVo.qrScanPrompt
    this.taxPayerId = mutationReceiveAccountVo.taxPayerId
    this.refundWay = mutationReceiveAccountVo.refundWay
    this.paymentChannelId = mutationReceiveAccountVo.paymentChannelId
    this.payMerchantId = mutationReceiveAccountVo.payMerchantId
    this.proxyId = mutationReceiveAccountVo.proxyId
    this.xdlPrivateKey = mutationReceiveAccountVo.xdlPrivateKey
  }

  to(): CreateReceiveAccountRequest {
    const createReceiveAccountRequest = new CreateReceiveAccountRequest()
    createReceiveAccountRequest.accountType = 1
    createReceiveAccountRequest.paymentChannelId = this.paymentChannelId
    createReceiveAccountRequest.accountNo = this.payMerchantId
    createReceiveAccountRequest.name = this.accountName
    createReceiveAccountRequest.qrScanPrompt = this.qrScanPrompt
    createReceiveAccountRequest.refundWay = this.refundWay
    createReceiveAccountRequest.taxPayerId = this.taxPayerId
    createReceiveAccountRequest.properties = new Array<ReceiveAccountExtProperty>()
    // createReceiveAccountRequest.properties.push({ name: 'storeNo', value: this.payMerchantId })
    createReceiveAccountRequest.properties.push({ name: 'proxyNo', value: this.proxyId })
    createReceiveAccountRequest.properties.push({ name: 'privateKey', value: this.xdlPrivateKey })
    return createReceiveAccountRequest
  }
}
