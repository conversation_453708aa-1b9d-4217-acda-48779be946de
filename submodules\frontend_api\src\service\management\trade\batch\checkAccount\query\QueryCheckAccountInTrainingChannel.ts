import { Page, ResponseStatus } from '@hbfe/common'
import CheckAccountParam from './vo/CheckAccountParam'
import CheckAccountListResponse from './vo/CheckAccountListResponse'
import RefundCheckAccountParam from './vo/RefundCheckAccountParam'
import RefundCheckAccountListResponse from './vo/RefundCheckAccountListResponse'
import TradeQuery, {
  BatchOrderRequest,
  BatchOrderSortField,
  BatchOrderSortRequest,
  BatchReturnOrderRequest,
  BatchReturnOrderSortField,
  BatchReturnOrderSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import QueryCheckAccountBase from '@api/service/management/trade/batch/checkAccount/query/vo/QueryCheckAccountBase'

export default class QueryCheckAccountInTrainingChannel extends QueryCheckAccountBase {
  /**
   * 报名订单数
   */
  orderCount = 0
  /**
   * 交易总金额
   */
  amount = 0
  /**
   * 退款订单数
   */
  refundTradeCount = 0
  /**
   * 退款总金额
   */
  refundAmountCount = 0

  /**
   * 报名订单分页查询
   * @param page 页数
   * @param queryCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<CheckAccountListResponse>
   */
  async queryOfRegistrationOrder(
    page: Page,
    queryCheckAccountParam?: CheckAccountParam
  ): Promise<Array<CheckAccountListResponse>> {
    const atchOrderSortRequest = new BatchOrderSortRequest()
    atchOrderSortRequest.field = BatchOrderSortField.BATCH_ORDER_UN_CONFIRMED_TIME
    atchOrderSortRequest.policy = SortPolicy.DESC
    const request = CheckAccountParam.to(queryCheckAccountParam)
    const { data } = await TradeQuery.pageBatchOrderInTrainingChannel({
      page,
      request,
      sortRequest: [atchOrderSortRequest]
    })
    this.statisticBatchOrderInServicer(request)
    page.totalSize = data.totalSize
    page.totalPageSize = data.totalPageSize
    const list = new Array<CheckAccountListResponse>()
    const userList = new Array<string>()
    data.currentPageData.map((item) => {
      const temp = CheckAccountListResponse.from(item)
      list.push(temp)
      userList.push(temp.userId)
    })
    const userInfoMap = await this.getUserInfo(userList)
    list.map((item) => {
      const userInfo = userInfoMap.get(item.userId)
      if (userInfo) {
        item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone)
      }
    })
    return list
  }
  async statisticBatchOrderInServicer(batchOrderRequest: BatchOrderRequest) {
    const { data } = await TradeQuery.statisticBatchOrderInTrainingChannel(batchOrderRequest)
    this.amount = data?.totalBatchOrderAmount || 0
    this.orderCount = data?.totalBatchOrderCount || 0
  }

  /**
   * 退款订单分页查询
   * @param page 页数
   * @param queryRefundCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<RefundCheckAccountListResponse>
   */
  async queryOfRefundOrder(
    page: Page,
    queryRefundCheckAccountParam?: RefundCheckAccountParam
  ): Promise<Array<RefundCheckAccountListResponse>> {
    //
    const sort = new BatchReturnOrderSortRequest()
    sort.field = BatchReturnOrderSortField.CREATED_TIME
    sort.policy = SortPolicy.DESC
    const batchReturnOrderRequest = RefundCheckAccountParam.refurnTo(queryRefundCheckAccountParam)
    const request = {
      page,
      request: batchReturnOrderRequest,
      sortRequest: [sort]
    }
    const response = await TradeQuery.pageBatchReturnOrderInTrainingChannel(request)
    // 获取批次退货单总数量、退款总金额  不使用同步执行，避免影响获取列表
    this.queryBatchRefoundListStatistic(batchReturnOrderRequest)
    page.totalSize = response.data.totalSize
    page.totalPageSize = response.data.totalPageSize
    const list = new Array<RefundCheckAccountListResponse>()
    const userIdList = []
    for (let i = 0; i < response.data.currentPageData.length; i++) {
      const element = response.data.currentPageData[i]
      userIdList.push(element.batchOrderInfo.creator.userId)
      list.push(RefundCheckAccountListResponse.from(element))
    }
    const userInfoMap = await this.getUserInfo(userIdList)
    list.map((item) => {
      const userInfo = userInfoMap.get(item.userId)
      if (userInfo) {
        item.serUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone)
      }
    })
    return list
  }
  /**
   * 【集体报名退款订单】查询列表统计数据 statisticBatchReturnOrderInServicer
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<ResponseStatus>}
   */
  async queryBatchRefoundListStatistic(queryParams: BatchReturnOrderRequest): Promise<ResponseStatus> {
    const { data, status } = await TradeQuery.statisticBatchReturnOrderInTrainingChannel(queryParams)
    this.refundTradeCount = data?.totalBatchReturnOrderCount || 0
    this.refundAmountCount = data?.totalBatchReturnOrderRefundAmount || 0
    return status
  }
}
