import exportAllUpdateSchemeShowResult from './queries/exportAllUpdateSchemeShowResult.graphql'
import exportFailUpdateSchemeShowResult from './queries/exportFailUpdateSchemeShowResult.graphql'
import getTainingChannelSchemePortalShowTempletePath from './queries/getTainingChannelSchemePortalShowTempletePath.graphql'
import pageUpdateSchemePortalResultTaskInfo from './queries/pageUpdateSchemePortalResultTaskInfo.graphql'
import validTrainingChannelEntryNameUnique from './queries/validTrainingChannelEntryNameUnique.graphql'
import batchUpdateSchemeShow from './mutates/batchUpdateSchemeShow.graphql'
import compareTrainingChannelUnitWithStudentUnit from './mutates/compareTrainingChannelUnitWithStudentUnit.graphql'
import disableTrainingChannelInfo from './mutates/disableTrainingChannelInfo.graphql'
import enableTrainingChannelInfo from './mutates/enableTrainingChannelInfo.graphql'
import getTrainingChannelSetting from './mutates/getTrainingChannelSetting.graphql'
import removeSchemeOfTopic from './mutates/removeSchemeOfTopic.graphql'
import saveTrainingChannelInfo from './mutates/saveTrainingChannelInfo.graphql'
import saveTrainingChannelOfflineCollectiveSignUpSetting from './mutates/saveTrainingChannelOfflineCollectiveSignUpSetting.graphql'
import saveTrainingChannelOnlineCollectiveSignUpSetting from './mutates/saveTrainingChannelOnlineCollectiveSignUpSetting.graphql'
import saveTrainingChannelPortalInfo from './mutates/saveTrainingChannelPortalInfo.graphql'
import saveTrainingChannelScheme from './mutates/saveTrainingChannelScheme.graphql'
import saveTrainingChannelSelectedCourse from './mutates/saveTrainingChannelSelectedCourse.graphql'
import setTrainingChannelSetting from './mutates/setTrainingChannelSetting.graphql'
import updateSaleSetting from './mutates/updateSaleSetting.graphql'
import updateTrainingChannelInfo from './mutates/updateTrainingChannelInfo.graphql'
import updateTrainingChannelOfflineCollectiveSignUpSetting from './mutates/updateTrainingChannelOfflineCollectiveSignUpSetting.graphql'
import updateTrainingChannelOnlineCollectiveSignUpSetting from './mutates/updateTrainingChannelOnlineCollectiveSignUpSetting.graphql'
import updateTrainingChannelPortalInfo from './mutates/updateTrainingChannelPortalInfo.graphql'
import updateTrainingChannelSort from './mutates/updateTrainingChannelSort.graphql'

export {
  exportAllUpdateSchemeShowResult,
  exportFailUpdateSchemeShowResult,
  getTainingChannelSchemePortalShowTempletePath,
  pageUpdateSchemePortalResultTaskInfo,
  validTrainingChannelEntryNameUnique,
  batchUpdateSchemeShow,
  compareTrainingChannelUnitWithStudentUnit,
  disableTrainingChannelInfo,
  enableTrainingChannelInfo,
  getTrainingChannelSetting,
  removeSchemeOfTopic,
  saveTrainingChannelInfo,
  saveTrainingChannelOfflineCollectiveSignUpSetting,
  saveTrainingChannelOnlineCollectiveSignUpSetting,
  saveTrainingChannelPortalInfo,
  saveTrainingChannelScheme,
  saveTrainingChannelSelectedCourse,
  setTrainingChannelSetting,
  updateSaleSetting,
  updateTrainingChannelInfo,
  updateTrainingChannelOfflineCollectiveSignUpSetting,
  updateTrainingChannelOnlineCollectiveSignUpSetting,
  updateTrainingChannelPortalInfo,
  updateTrainingChannelSort
}
