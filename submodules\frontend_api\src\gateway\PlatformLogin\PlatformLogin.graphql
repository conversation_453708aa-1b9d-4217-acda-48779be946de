schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""小程序回调接口使用，小程序人脸识别完成后，需要回调该接口
		@param params 请求参数
		@return 单点登录的token
	"""
	appletsCallBackLogin(params:AppletsRandomLoginRequest):String @NotAuthenticationRequired
	"""登录回调使用，提供给第三方回调的，平台不需要关心该接口
		@param params 参数
	"""
	callBackLogin(params:RandomCodeRequest):Void @NotAuthenticationRequired
}
"""小程序登录回调参数
	<AUTHOR> create 2021/3/3 11:16
"""
input AppletsRandomLoginRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.AppletsRandomLoginRequest") {
	"""身份证"""
	certificate:String
	"""用户名"""
	userName:String
}
"""随机码回调接口的参数
	<AUTHOR> create 2021/3/1 20:57
"""
input RandomCodeRequest @type(value:"com.fjhb.btpx.platform.gateway.web.randomcode.request.RandomCodeRequest") {
	"""身份证号"""
	identity:String
	"""姓名"""
	username:String
	"""回调的随机码"""
	code:String
	"""验证时间"""
	validationDate:String
	"""验证结果，1验证成功"""
	result:Int!
	"""失败码
		详细规则查看微信文档：https://developers.weixin.qq.com/community/business/doc/000442d352c1202bd498ecb105c00d
	"""
	errCode:String
	"""失败信息
		详细规则查看微信文档：https://developers.weixin.qq.com/community/business/doc/000442d352c1202bd498ecb105c00d
	"""
	errMsg:String
}

scalar List
