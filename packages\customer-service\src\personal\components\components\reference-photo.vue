<template>
  <div>
    <el-drawer title="修改基准照片次数" :visible.sync="dialogVisible" size="450px" custom-class="m-drawer">
      <div class="drawer-bd">
        <div>
          <span class="f-fb f-c6">当前剩余修改基准照片次数： </span
          ><span class="f-f24 f-fb f-ci f-mr5">{{ userDatum.userDatum.currentDatum.residueUpdateCount }}</span
          >次
        </div>
        <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20" :rules="rules">
          <el-form-item label="增加修改基准照片次数：" prop="number">
            <el-input
              v-model="form.number"
              class="u-w180"
              placeholder="请输入"
              oninput="value=value.replace(/^0|[^0-9]/g,'')"
              @input="addPhotoTime"
            />
            <span class="f-ml5">次</span>
          </el-form-item>
        </el-form>
        <div class="m-btn-bar f-tc f-mt30">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="save(form.number)"
            :loading="loading"
            :disabled="!disabled || !userDatum.userDatum.currentDatum.residueUpdateCount"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import UserDatum from '@hbfe-biz/biz-anticheat/dist/datum/UserDatum'
  import CurrentDatum from '@hbfe-biz/biz-anticheat/dist/datum/models/UserDatumModel'
  import ResidueUpdateCount from '@hbfe-biz/biz-anticheat/dist/datum/base-models/SingleDatumModel'

  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import { log } from 'lodash-decorators/utils'
  @Component({})
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    dialogCtrl: boolean

    // 学员id
    @Prop({
      type: String,
      default: null
    })
    userId: string

    dialogVisible = false

    userDatum = new UserDatum()
    currentDatum = new CurrentDatum()
    residueUpdateCount = new ResidueUpdateCount()

    // 加载loading效果
    loading = false

    //保存按钮的禁用
    disabled = false

    // 当前用户基准照的id
    userDatumId = ''

    @Watch('dialogCtrl')
    changeDialogCtrl() {
      this.dialogVisible = this.dialogCtrl
    }

    @Watch('dialogVisible')
    changeDialogVisible() {
      this.$emit('update:dialogCtrl', this.dialogVisible)
    }

    @Watch('userId')
    async initUserDatum() {
      // 用户基准照
      if (this.userId) {
        this.userDatum = new UserDatum(this.userId)
        const res = await this.userDatum.queryDetail()
        // console.log('******res******', res.currentDatum.userDatumId)
        this.userDatumId = res.currentDatum.userDatumId
      }
    }

    form = {
      number: ''
    }

    rules = {
      number: [
        {
          required: true,
          trigger: 'blur',
          message: '请添加增加修改基准照片次数'
        }
      ]
    }

    async save(value: number) {
      //   console.log('点击了保存按钮 ')
      this.loading = true
      //   try {
      this.userDatum.userDatum.currentDatum.userDatumId = this.userDatumId
      const oldCount = this.userDatum.userDatum.currentDatum.residueUpdateCount
      const newValue = Number(value) + oldCount
      await this.userDatum
        .changeAllowCount(newValue)
        .then((res) => {
          console.log(res)
          this.$message.success('保存成功')
          setTimeout(async () => {
            await this.userDatum.queryDetail()
            // console.log(res, '查询一下')
          }, 1500)
        })
        .catch((err) => {
          this.$message(err.status.errors[0])
          console.log(err)
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false
          }, 2000)
          this.close()
        })
      // console.log('------res--------', res)

      // if (res.code == 200) {
      //   this.$message.success('保存成功')
      //   setTimeout(async () => {
      //     await this.userDatum.queryDetail()
      //     // console.log(res, '查询一下')
      //   }, 1500)
      // } else {
      //   return this.$message.error('保存失败')
      // }
      //   } catch (error) {
      //     this.$message.error('jiazai')
      //   } finally {
      //     setTimeout(() => {
      //       this.loading = false
      //     }, 2000)
      //     this.close()
      //   }
    }

    addPhotoTime() {
      this.disabled = true
    }

    // 关闭抽屉
    async close() {
      this.dialogVisible = false
    }
  }
</script>

<style></style>
