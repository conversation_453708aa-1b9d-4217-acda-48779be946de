import CheckAccountParam from '@api/service/management/trade/batch/checkAccount/query/vo/CheckAccountParam'
import ExportGateway, {
  BatchOrderSortRequest,
  BatchReturnOrderSortRequest
} from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import RefundCheckAccountParam from '@api/service/management/trade/batch/checkAccount/query/vo/RefundCheckAccountParam'

export default class MutationCheckAccountInTrainingChannel {
  /**
   * 集体报名对账导出
   */
  async listExport(checkAccountParam: CheckAccountParam, sortRequest?: Array<BatchOrderSortRequest>): Promise<boolean> {
    const request = CheckAccountParam.to(checkAccountParam)
    const { data } = await ExportGateway.exportBatchReconciliationInTrainingChannel({ request, sort: sortRequest })
    return data
  }

  /**
   * 集体退款对账导出
   */
  async listReturnExport(
    checkAccountParam: RefundCheckAccountParam,
    sortRequest?: Array<BatchReturnOrderSortRequest>
  ): Promise<boolean> {
    const request = RefundCheckAccountParam.refurnTo(checkAccountParam)
    const { data } = await ExportGateway.exportBatchReturnReconciliationExcelInTrainingChannel({
      request,
      sort: sortRequest
    })
    return data
  }
}
