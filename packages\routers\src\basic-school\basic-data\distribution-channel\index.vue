<route-meta>
{
"isMenu": true,
"title": "配送渠道配置",
"sort": 4,
"icon": "icon-peisong"
}
</route-meta>
<script lang="ts">
  import DistributionChannel from '@hbfe/jxjy-admin-distributionChannel/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY],
    create: [WXGLY],
    collectionEdit: [WXGLY],
    disable: [WXGLY],
    add: [WXGLY],
    disuse: [WXGLY],
    enable: [WXGLY],
    remove: [WXGLY],
    attemptModify: [WXGLY],
    Delivery: [WXGLY],
    PickUp: [WXGLY],
    chooseWay: [WXGLY]
  })
  export default class extends DistributionChannel {}
</script>
