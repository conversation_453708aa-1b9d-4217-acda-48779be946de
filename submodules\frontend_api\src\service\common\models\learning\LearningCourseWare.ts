import LearningMedia from './LearningMedia'

/**
 * 学习中课件信息
 */
class LearningCourseWare {
  /**
   * 课件编号
   */
  courseWareId = ''
  /**
   * 课件名称
   */
  courseWareName = ''
  /**
   * 所属章节编号
   */
  courseChapterId = ''
  /**
   * 课件类型，1表示文档，2表示视频，3表示多媒体
   */
  type: number
  /**
   * 课件总时长
   */
  timeTotalLength = 0
  /**
   * 当前课件学习的进度
   */
  currentLearningSchedule = 0
  /**
   * 最后学习时间
   */
  lastLearningTime: Date
  /**
   * 学习的媒体信息
   */
  learningMedia: LearningMedia = new LearningMedia()
  /**
   * 自定义状态 0不可以试听，1可以试听
   */
  customeStatus: number

  /**
   * 是否多媒体或视频类课件
   */
  isVideo() {
    return this.type === 2 || this.type === 3
  }

  /**
   * 是否文档类课件
   */
  isDocument() {
    return this.type === 1
  }

  /**
   * 是否允许试听
   */
  isAllowTryListen() {
    return this.customeStatus === 1
  }
}

export default LearningCourseWare
