import { StudentIndustryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

export class SectionAndSubjects {
  sectionCode?: number
  section: string
  subjectsCode?: number
  subjects: string
}
export default class LsStudentIndustryInfoVo extends StudentIndustryResponse {
  /**
   * 教师行业 学段、学科信息
   */
  sectionAndSubjectsName: Array<SectionAndSubjects> = []
}
