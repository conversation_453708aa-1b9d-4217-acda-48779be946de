import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'
import { CourseResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

/**
 * 课程列表详情
 */
class CourseListDetail {
  id: string
  // 名称
  name: string
  // 封面
  coverImage: string
  // 课程教师
  teachers: Array<SimpleUserInfo> = new Array<SimpleUserInfo>()
  // 学时、课程物理学时
  period: number
  // 是否允许试听
  canListen: boolean
  // * 课程类别
  courseTypeName: string[] = []

  static from(response: CourseResponse): CourseListDetail {
    const detail = new CourseListDetail()
    detail.id = response.id
    detail.name = response.name
    detail.period = response.period
    detail.coverImage = response.iconPath
    detail.canListen = response.auditionStatus === 1
    detail.courseTypeName = response.categorys?.map(item => item.name)
    return detail
  }
}

export default CourseListDetail
