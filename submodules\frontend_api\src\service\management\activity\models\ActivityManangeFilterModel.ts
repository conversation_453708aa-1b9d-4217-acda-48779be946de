import {
  LearningExperienceEnum,
  AnswerMethodEnum,
  ApproveMethodEnum,
  ApproveResultEnum
} from '@api/service/management/activity/enum/ActivityEnum'
// 筛选项
import {
  StudentLearningExperienceRequest,
  LearningExperienceTopicRequest,
  DateScopeRequest1,
  ExperienceType,
  AuditType,
  StudentLearningExperienceStatus,
  StudentSchemeLearningRequest,
  ParticipateType
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import DataExportBackstage, {
  StudentLearningExperienceRequest as StudentLearningExperienceExportRequest
} from '@api/ms-gateway/ms-data-export-front-gateway-DataExportBackstage'
export default class ActivityManangeFilterModel {
  /**
   * 培训方案名称
   */
  schemeName = ''
  /**
   * 培训方案id
   */
  schemeIds: string[] = []
  /**
   * 主题
   */
  theme = ''
  /**
   * 主题id
   */
  themeId = ''
  /**
   * 学习心得类型
   */
  learningExperienceType: LearningExperienceEnum = null
  /**
   * 参加起始时间
   */
  joinStartTime = ''
  /**
   * 参加结束时间
   */
  joinEndTime = ''
  /**
   * 姓名
   */
  name = ''
  /**
   * 证件号
   */
  idCard = ''
  /**
   * 作答方式
   */
  answerMethod: AnswerMethodEnum = null
  /**
   * 审核方式
   */
  approveMethod: ApproveMethodEnum = null
  /**
   * 审核结果
   */
  approveResult: ApproveResultEnum = null
  /**
   * 审核状态
   */
  auditStatus: Array<StudentLearningExperienceStatus>
  /**
   * 转化为查询参数
   */
  toQueryRequest() {
    const dto = new StudentLearningExperienceRequest()
    dto.learningExperienceTopic = new LearningExperienceTopicRequest()
    dto.learningExperienceTopic.dateScopeRequest = new DateScopeRequest1()

    // TODO 方案id 主题id
    if (this.schemeIds?.length) {
      dto.studentLearning = new StudentSchemeLearningRequest()
      dto.studentLearning.schemeIds = this.schemeIds
    }
    if (this.themeId) {
      dto.learningExperienceTopic.topicIds = [this.themeId]
    }
    if (this.learningExperienceType === LearningExperienceEnum.CLASS) {
      dto.experienceType = ExperienceType.SCHEME
    } else if (this.learningExperienceType === LearningExperienceEnum.COURSE) {
      dto.experienceType = ExperienceType.COURSE
    }
    dto.learningExperienceTopic.dateScopeRequest.begin = this.joinStartTime
    dto.learningExperienceTopic.dateScopeRequest.end = this.joinEndTime
    // dto.user.userName = this.name
    // dto.user.idCard = this.idCard
    if (this.answerMethod === AnswerMethodEnum.EDIT) {
      dto.learningExperienceTopic.participateType = ParticipateType.EDIT_ONLINE
    } else if (this.answerMethod === AnswerMethodEnum.UPLOAD) {
      dto.learningExperienceTopic.participateType = ParticipateType.SUBMIT_FILE
    }

    if (this.approveMethod === ApproveMethodEnum.AUTO) {
      dto.learningExperienceTopic.auditType = AuditType.AUTO_AUDIT
    } else if (this.approveMethod === ApproveMethodEnum.ARTIFICIAL) {
      dto.learningExperienceTopic.auditType = AuditType.MANUAL_AUDIT
    }

    dto.status = this.auditStatus
    if (this.approveResult === ApproveResultEnum.SUCCESS) {
      dto.status = [StudentLearningExperienceStatus.PASS]
    } else if (this.approveResult === ApproveResultEnum.FAIL) {
      dto.status = [StudentLearningExperienceStatus.RETURNED]
    }
    return dto
  }
  /**
   * 转化为查询参数 导出
   */
  toExportQueryRequest() {
    const dto = new StudentLearningExperienceExportRequest()
    dto.learningExperienceTopic = new LearningExperienceTopicRequest()
    dto.learningExperienceTopic.dateScopeRequest = new DateScopeRequest1()
    if (this.schemeIds.length) {
      dto.studentLearning = new StudentSchemeLearningRequest()
      dto.studentLearning.schemeIds = this.schemeIds
    }
    if (this.themeId) {
      dto.learningExperienceTopic.topicIds = [this.themeId]
    }
    if (this.learningExperienceType === LearningExperienceEnum.CLASS) {
      dto.experienceType = ExperienceType.SCHEME
    } else if (this.learningExperienceType === LearningExperienceEnum.COURSE) {
      dto.experienceType = ExperienceType.COURSE
    }
    dto.learningExperienceTopic.dateScopeRequest.begin = this.joinStartTime
    dto.learningExperienceTopic.dateScopeRequest.end = this.joinEndTime
    // dto.user.userName = this.name
    // dto.user.idCard = this.idCard
    if (this.answerMethod === AnswerMethodEnum.EDIT) {
      dto.learningExperienceTopic.participateType = ParticipateType.EDIT_ONLINE
    } else if (this.answerMethod === AnswerMethodEnum.UPLOAD) {
      dto.learningExperienceTopic.participateType = ParticipateType.SUBMIT_FILE
    }

    if (this.approveMethod === ApproveMethodEnum.AUTO) {
      dto.learningExperienceTopic.auditType = AuditType.AUTO_AUDIT
    } else if (this.approveMethod === ApproveMethodEnum.ARTIFICIAL) {
      dto.learningExperienceTopic.auditType = AuditType.MANUAL_AUDIT
    }
    dto.status = this.auditStatus
    if (this.approveResult === ApproveResultEnum.SUCCESS) {
      dto.status = [StudentLearningExperienceStatus.PASS]
    } else if (this.approveResult === ApproveResultEnum.FAIL) {
      dto.status = [StudentLearningExperienceStatus.RETURNED]
    }
    return dto
  }
}
