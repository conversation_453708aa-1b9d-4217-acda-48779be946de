import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
import DateRange from '@api/service/common/scheme/model/DateRange'
import MsTradeQueryFrontGatewayTradeQueryBackstage, {
  DateScopeRequest,
  IssueCommoditySkuBackStageRequest,
  IssueCommoditySkuBackStageResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { Page } from '@hbfe/common'
import { cloneDeep } from 'lodash'

/**
 * @description 查询培训方案期别列表参数
 */
export class QueryTrainClassIssueListParam {
  /**
   * 商品Id【必传】
   */
  commoditySkuId = ''
  /**
   * 期别名称
   */
  issueName = ''
  /**
   * 报名时间范围
   */
  registerDateRange = new DateRange()
}

/**
 * @description 查询培训方案期别
 */
class QueryTrainClassIssue {
  /**
   * 【超管】查询培训方案下期别列表
   * @param queryParam 查询条件
   */
  async queryTrainClassIssueList(queryParam: QueryTrainClassIssueListParam): Promise<IssueConfigDetail[]> {
    let result: IssueConfigDetail[] = []
    const respDataList: IssueCommoditySkuBackStageResponse[] = []
    const request = new IssueCommoditySkuBackStageRequest()
    const page = new Page(1, 200)
    const {
      issueName,
      commoditySkuId,
      registerDateRange: { startDate, endDate }
    } = queryParam
    if (commoditySkuId) {
      request.commoditySkuId = commoditySkuId
    }
    if (startDate) {
      request.issueSignUpBeginDate = new DateScopeRequest()
      request.issueSignUpBeginDate.begin = startDate
    }
    if (endDate) {
      request.issueSignUpEndDate = new DateScopeRequest()
      request.issueSignUpEndDate.end = endDate
    }
    if (issueName) {
      request.issueNameMatchLike = issueName
    }
    const { status, data } = await MsTradeQueryFrontGatewayTradeQueryBackstage.pageIssueCommoditySkuInServicer({
      request,
      page
    })
    if (status && status.isSuccess() && data) {
      if (data.currentPageData && data.currentPageData.length) {
        respDataList.push(...data.currentPageData)
      }
      if (data.totalPageSize > 200) {
        const requestList = Array(Math.ceil(data.totalSize / 200)).fill('')
        const tmpRespList = await Promise.allSettled(
          requestList.map(async (item, index) => {
            const tmpPage = new Page(index + 2, 200)
            const tmpRequest = cloneDeep(request)
            return MsTradeQueryFrontGatewayTradeQueryBackstage.pageIssueCommoditySkuInServicer({
              page: tmpPage,
              request: tmpRequest
            })
          })
        )
        tmpRespList.forEach((tmpResp) => {
          if (tmpResp.status === 'fulfilled') {
            const { status, data } = tmpResp.value
            if (status && status.isSuccess() && data) {
              if (data.currentPageData && data.currentPageData.length) {
                respDataList.push(...data.currentPageData)
              }
            }
          }
        })
      }
    }
    if (respDataList.length) {
      result = respDataList.map((item) => {
        const issueConfigDetail = new IssueConfigDetail()
        const { issueResourceInfo } = item
        issueConfigDetail.remainingRegisterNumber = item.remainingRegisterNumber
        if (issueResourceInfo) {
          issueConfigDetail.id = issueResourceInfo.issueId
          issueConfigDetail.issueName = issueResourceInfo.issueName
          issueConfigDetail.issueNo = issueResourceInfo.issueNum
          issueConfigDetail.registerBeginTime = issueResourceInfo.issueSignUpBeginDate
          issueConfigDetail.registerEndTime = issueResourceInfo.issueSignUpEndDate
        }
        issueConfigDetail.isEnableStudentEnroll = !!issueConfigDetail.registerBeginTime
        return issueConfigDetail
      })
      // 按期别编号从小到大排序
      result = result.sort((a, b) => Number(a.issueNo) - Number(b.issueNo))
    }
    // if (issueName) {
    //   result = result.filter((item) => item.issueName.includes(issueName))
    // }
    return result
  }
}

export default QueryTrainClassIssue
