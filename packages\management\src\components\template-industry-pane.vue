<template>
  <div>
    <el-card shadow="never" class="m-card f-mb15">
      <div class="f-p10">
        <el-row :gutter="20" type="flex" justify="center">
          <el-col :lg="20" :xl="18">
            <el-form label-width="auto" class="m-form f-clear f-mt40">
              <el-form-item v-if="schoolDetail.schoolConfig.provideWebService" label="PC端模板：">
                <el-checkbox-group
                  v-for="(item, index) in TemplateConfigObj[pclistkey]"
                  :key="item.id"
                  v-model="pclist"
                  @change="handleChange"
                >
                  <ul class="m-demo-pic">
                    <!-- nth-child(3n)失效 -->
                    <!-- 添加动态样式原因：非人社的第四个模板需要去除左外边距，不然会挤到右边导致样式问题，若后续需要添加新的模板，则需要修改此处代码 -->
                    <li
                      :style="(index + 1) % 3 == 0 ? 'margin-right:0' : ''"
                      style="margin-bottom: 40px"
                      :class="{ 'template-industry-con': (index + 1) % 4 === 0 && industryType !== 'rs' }"
                    >
                      <div class="demo-pic">
                        <div class="mask" style="z-index: 999">
                          <i class="icon el-icon-zoom-in" @click="maskPc(index)"></i>
                        </div>
                        <div class="pic">
                          <el-image
                            ref="imageRef"
                            :src="getPath(item.reviewPath)"
                            :preview-src-list="[getPath(item.reviewPath)]"
                          ></el-image>
                        </div>
                        <el-checkbox :label="item.id" style="z-index: 1000">{{
                          pclist.indexOf(item.id) != -1 ? '当前已选' : '请选择'
                        }}</el-checkbox>
                      </div>
                      <div class="demo-pic-info">
                        <p>
                          <span class="t">bananer轮播图片尺寸：</span>{{ item.bannerSize[0] }}*{{ item.bannerSize[1] }}
                        </p>
                        <p>
                          <span class="t">客服电话图片尺寸：</span>{{ item.customerPhoneSize[0] }}*{{
                            item.customerPhoneSize[1]
                          }}
                        </p>
                        <p>
                          <span class="t">集体报名图片尺寸：</span
                          ><i v-if="item.groupRegistrationSize.length">
                            {{ item.groupRegistrationSize[0] }}*{{ item.groupRegistrationSize[1] }}</i
                          ><i v-else>-</i>
                        </p>
                        <p>
                          <span class="t">移动端悬浮图片尺寸：</span>{{ item.H5QRCodeSize[0] }}*{{
                            item.H5QRCodeSize[1]
                          }}
                        </p>
                        <p><span class="t">友情链接图片尺寸：</span>{{ item.linksSize[0] }}*{{ item.linksSize[1] }}</p>
                        <p><span class="t">logo尺寸：</span>{{ item.logoSize[0] }}*{{ item.logoSize[1] }}</p>
                        <p v-if="item.id === 'rs-pc-003' || item.id === 'js-pc-003'">
                          <span class="t">培训流程：</span>{{ item.trainingProcessSize[0] }}*{{
                            item.trainingProcessSize[1]
                          }}
                        </p>
                      </div>
                    </li>
                  </ul>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item v-if="schoolDetail.schoolConfig.provideH5Service" label="移动端（H5）模板：">
                <el-checkbox-group
                  v-for="(item, index) in TemplateConfigObj[h5listkey]"
                  :key="item.id"
                  v-model="h5list"
                  @change="handleChange"
                >
                  <ul class="m-demo-pic">
                    <li :style="(index + 1) % 3 == 0 ? 'margin-right:0' : ''">
                      <div class="demo-pic">
                        <div class="mask" style="z-index: 999">
                          <i class="icon el-icon-zoom-in" @click="maskH5(index)"></i>
                        </div>
                        <div class="pic">
                          <!-- <img :src="getPath(item.reviewPath)" /> -->
                          <el-image
                            ref="imageRefH5"
                            :src="getPath(item.reviewPath)"
                            :preview-src-list="[getPath(item.reviewPath)]"
                          ></el-image>
                        </div>
                        <el-checkbox :label="item.id" style="z-index: 1000">{{
                          h5list.indexOf(item.id) != -1 ? '当前已选' : '请选择'
                        }}</el-checkbox>
                      </div>
                      <div class="demo-pic-info phone-text">
                        <p>
                          <span class="t">bananer轮播图片尺寸：</span>{{ item.bannerSize[0] }}*{{ item.bannerSize[1] }}
                        </p>
                      </div>
                    </li>
                  </ul>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import TemplateModule from '@api/service/common/template-school/TemplateModule'
  import OnlineSchoolModel from '@api/service/training-institution/online-school/models/OnlineSchoolModel'
  @Component
  export default class extends Vue {
    @Prop({
      required: false,
      default: () => {
        return new Array<string>()
      }
    })
    pcCheckedList: Array<string>
    @Prop({
      required: false,
      default: () => {
        return new Array<string>()
      }
    })
    h5CheckedList: Array<string>
    @Prop({
      required: false,
      default: () => {
        return 'rs'
      }
    })
    industryType: string
    @Prop({
      required: true,
      default: () => {
        return new OnlineSchoolModel()
      }
    })
    schoolDetail: OnlineSchoolModel
    @Watch('industryType', { immediate: true, deep: true })
    getValue(val: any) {
      switch (val) {
        case 'rs':
          this.title = '人社'
          this.pclistkey = '_templateJSPCList'
          this.h5listkey = '_templateJSH5List'
          break
        case 'js':
          this.title = '建设'
          this.pclistkey = '_templateJSPCList'
          this.h5listkey = '_templateJSH5List'
          break
        case 'ws':
          this.title = '职业卫生'
          this.pclistkey = '_templateJSPCList'
          this.h5listkey = '_templateJSH5List'
          break
        case 'gq':
          this.title = '工勤'
          this.pclistkey = '_templateJSPCList'
          this.h5listkey = '_templateJSH5List'
          break
        case 'ls':
          this.title = '教师'
          this.pclistkey = '_templateJSPCList'
          this.h5listkey = '_templateJSH5List'
          break
        case 'ys':
          this.title = '药师'
          this.pclistkey = '_templateJSPCList'
          this.h5listkey = '_templateJSH5List'
          break
        default:
          break
      }
    }
    @Watch('pcCheckedList', { immediate: true, deep: true })
    @Watch('h5CheckedList', { immediate: true, deep: true })
    pcChangeValue(val: any) {
      this.changeValue()
    }
    TemplateConfigObj = TemplateModule
    title = ''
    pclistkey = ''
    h5listkey = ''
    pclist = new Array<string>()
    h5list = new Array<string>()

    created() {
      console.log(this.schoolDetail, '里面的sch')
    }
    changeValue() {
      this.pclist = this.pcCheckedList
      this.h5list = this.h5CheckedList
    }
    getPath(url: string) {
      if (url.indexOf('s-') != -1 || url == 'demo-h5-homepage-1') {
        return require('@design/trainingInstitution/assets/images/' + url + '.jpg')
      } else {
        return require('@design/trainingInstitution/assets/images/' + url + '.png')
      }
    }
    handleChange(val: Array<string>) {
      if (this.pclist.length > 1) {
        this.pclist.shift()
        this.pclist = val
      }
      if (this.h5list.length > 1) {
        this.h5list.shift()
        this.h5list = val
      }
      this.$emit('getList', this.pclist, this.h5list)
    }
    getColor(item: any, sitem: any) {
      item.color = sitem
    }

    maskPc(index: number) {
      this.$refs['imageRef'][index].$el.children[0].click()
    }

    maskH5(index: number) {
      this.$refs['imageRefH5'][index].$el.children[0].click()
    }
  }
</script>

<style lang="scss" scoped>
  .el-collapse-item__header {
    font-size: 16px;
    font-weight: bold;
    padding-left: 20px;
    padding-right: 10px;
  }
  //模板配置-模版图片列表
  .m-demo-pic {
    li {
      &:nth-child(3n) {
        margin-right: 0;
      }
    }
    .demo-pic-info {
      height: 124px;
      color: #666;
      font-size: 14px;
      // p {
      //   line-height: 18px;
      //   margin-bottom: 8px;

      //   .t {
      //     display: inline-block;
      //     font-weight: bold;
      //   }

      //   &:last-child {
      //     margin-bottom: 0;
      //   }
      // }
      // }
    }
    .phone-text {
      height: 20px;
    }
    .template-config--pic-color {
      display: flex;
      align-items: center;
      .t {
        font-size: 14px;
        color: #666;
        font-weight: bold;
        width: 70px;
      }
      .color-template-con {
        display: flex;
        width: 200px;
        justify-content: space-evenly;
        .color-tem {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          cursor: pointer;
          // margin: 0 20px;
        }
      }
    }
    .template-industry-con {
      margin-left: 80px;
    }
  }
</style>
