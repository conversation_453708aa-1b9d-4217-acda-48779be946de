import {
  RefundCommodityResponse,
  RefundInfoResponse,
  ReturnApprovalInfoResponse,
  ReturnCommodityResponse,
  ReturnOrderBasicDataResponse,
  ReturnOrderResponse,
  SubOrderInfoResponse1,
  UserResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import TradeModule from '@api/service/management/trade/TradeModule'
import { RefundOrderStatusEnum } from '@api/service/common/trade/RefundOrderStatusEnum'
import { ReturnOrderRecordVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRecordVo'

export default class ReturnOrderResponseVo {
  /**
   * 退货单号
   */
  returnOrderNo: string
  /**
   * 退货单基本信息
   */
  basicData: ReturnOrderBasicDataResponse = new ReturnOrderBasicDataResponse()
  /**
   * 退货单是否需要审批
   */
  needApprove: boolean
  /**
   * 退货单审批信息
   */
  approvalInfo: ReturnApprovalInfoResponse = new ReturnApprovalInfoResponse()
  /**
   * 退款确认人
   */
  confirmUser: UserResponse
  /**
   * 退货单关联退款单信息
   */
  refundInfo: RefundInfoResponse
  /**
   * 退货商品信息
   */
  returnCommodity: ReturnCommodityResponse
  /**
   * 退款商品信息
   */
  refundCommodity: RefundCommodityResponse
  /**
   * 退货子订单信息
   */
  subOrderInfo: SubOrderInfoResponse1
  //UI列表展示的状态
  UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumApproving

  //退款操作记录
  records: ReturnOrderRecordVo[] = []
  /*
   * 获取退款业务对象
   *
   * */
  getMutationReturn() {
    const mutationReturn = TradeModule.singleTradeBatchFactor.mutationFactory.getMutationReturnOrder()
    mutationReturn.returnOrderNo = this.returnOrderNo
    return mutationReturn
  }
  async fillRecords() {
    if (this.UIReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumApproving) {
      await this.addProveRecord()
    } else if (
      this.UIReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumHandling ||
      this.UIReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumRefuse ||
      this.UIReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumFail
    ) {
      await this.addProveRecord()
      await this.addHandleRecord(
        this.UIReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumFail
          ? RefundOrderStatusEnum.RefundOrderStatusEnumHandling
          : this.UIReturnOrderStatue
      )
    } else if (this.UIReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumCancel) {
      await this.addProveRecord()
      await this.addCloseRecord()
    } else {
      await this.addProveRecord()
      await this.addSuccessRecord()
    }
  }
  private async addProveRecord() {
    //   因为用户模块还没有。所以这里暂时用用户id代替名字
    try {
      const record = new ReturnOrderRecordVo()
      record.name = this.basicData.applyInfo.applyUser.userId
      record.time = this.basicData.returnOrderStatusChangeTime.applied
      record.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumApproving
      record.tipMsg = this.basicData.applyInfo.reasonContent
      record.money = this.basicData.refundAmount
      this.records.push(record)
    } catch (e) {
      console.log('添加审批人失败', e)
    }
  }
  private async addHandleRecord(UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumRefuse) {
    //   因为用户模块还没有。所以这里暂时用用户id代替名字
    const record = new ReturnOrderRecordVo()
    record.name = this.approvalInfo.approveUser.userId
    record.time = this.approvalInfo.approveTime
    record.UIReturnOrderStatue = UIReturnOrderStatue
    record.tipMsg = this.approvalInfo.approveComment
    this.records.push(record)
  }
  private async addCloseRecord() {
    //   因为用户模块还没有。所以这里暂时用用户id代替名字
    const record = new ReturnOrderRecordVo()
    record.name = this.basicData.returnCloseReason.cancelUser.userId
    record.time = this.basicData.returnOrderStatusChangeTime.closed
    record.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumCancel
    record.tipMsg = this.basicData.returnCloseReason.cancelReason
    this.records.push(record)
  }
  private async addSuccessRecord() {
    //  因为用户模块还没有。所以这里暂时用用户id代替名字
    const record = new ReturnOrderRecordVo()
    if (this.confirmUser && this.confirmUser.userId) {
      record.name = this.confirmUser.userId
    }

    const map = {
      '1': this.basicData.returnOrderStatusChangeTime.returnCompleted,
      '2': this.basicData.returnOrderStatusChangeTime.refunded,
      '3': this.basicData.returnOrderStatusChangeTime.returnedAndRefunded
    }

    record.time = map[this.basicData.returnOrderType]
    record.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumSuccess
    this.records.push(record)
  }
  //  将底层返回的退款状态转化为前端展示的状态
  changeStatus() {
    // const tmpItem = this
    if (this.basicData && this.basicData.returnOrderStatus !== undefined) {
      const approveStates = [0, 1],
        handlingStates = [2, 3, 4, 5, 6],
        successStatues = [8, 9, 10]
      if (approveStates.indexOf(this.basicData.returnOrderStatus) != -1) {
        this.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumApproving
      } else if (handlingStates.indexOf(this.basicData.returnOrderStatus) != -1) {
        this.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumHandling
      } else if (successStatues.indexOf(this.basicData.returnOrderStatus) != -1) {
        this.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumSuccess
      } else if (this.basicData.returnOrderStatus == 11 && this.basicData.returnCloseReason.closeType == 3) {
        this.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumRefuse
      } else if (
        this.basicData.returnOrderStatus == 11 &&
        [1, 2].indexOf(this.basicData.returnCloseReason.closeType) != -1
      ) {
        this.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumCancel
      } else if (this.basicData.returnOrderStatus == 7) {
        this.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumFail
      }
    }
  }
}
