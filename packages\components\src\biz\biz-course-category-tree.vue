<template>
  <div v-loading="onLoading">
    <span style="margin-right:10px"><span style="color:red;vertical-align:middle;">*</span>选中课程分类</span>
    <el-select v-model="selectItem" placeholder="请选择" ref="elSelect">
      <el-option :value="selectTree" style="height:auto !important;padding:3px 0px !important;" disabled>
        <el-tree
          highlight-current
          @current-change="currentChange"
          lazy
          :data="options"
          :props="props"
          :load="load"
          ref="tree"
        ></el-tree>
      </el-option>
    </el-select>

    <div slot="footer" class="tree-confirm">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="certain">确 定</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .tree-confirm {
    margin: 20px auto 0 0;
    text-align: center;
  }
</style>

<script lang="ts">
  import { Watch, Prop, Emit, Component, Vue, Ref, Inject } from 'vue-property-decorator'
  import CourseCategoryListDetail from '@api/service/management/resource/course-category/query/vo/CourseCategoryListDetail'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { ElTree } from 'element-ui/types/tree'
  import { ElSelect } from 'element-ui/types/select'

  @Component
  export default class extends Vue {
    @Inject({
      from: 'validateChooseCategory',
      default() {
        return () => {
          return true
        }
      }
    })
    validateChooseCategory: (category: CourseCategoryListDetail) => {}

    // 选中项
    selectItem = ''
    selectTree = ''

    @Prop({
      type: [Array, String]
    })
    value: Array<string>

    @Prop({
      type: Boolean,
      default: false
    })
    isArray: boolean

    @Prop({
      type: String,
      default: ''
    })
    leafId: string

    @Prop({
      type: Boolean,
      default: true
    })
    checkStrictly: boolean

    @Prop({
      type: Boolean,
      default: false
    })
    filterable: boolean

    categoryIdList: Array<string> = new Array<string>()
    props = {
      label: 'name',
      value: 'id',
      children: 'children'
    }

    options: any = []
    onLoading = false

    selected: CourseCategoryListDetail = new CourseCategoryListDetail()

    selectedNode: any

    @Ref('tree')
    tree: ElTree<any, any>

    @Ref('elSelect')
    elSelect: ElSelect

    cancel() {
      this.$emit('cancel')
    }

    async load(node: { data: CourseCategoryListDetail }, resolve: any) {
      if (node?.data?.id) {
        if (node?.data?.children?.length) {
          resolve(null)
        } else {
          const result = await this.query(node?.data?.id)
          resolve(result)
        }
      }
    }

    @Watch('value')
    valueChange() {
      this.categoryIdList = this.value
    }

    selectedChange() {
      if (this.categoryIdList && this.categoryIdList.length) {
        return this.categoryIdList
      }
    }

    async created() {
      this.onLoading = true
      this.options = await this.query('-1')
      this.onLoading = false
    }

    @Watch('categoryIdList')
    @Emit('input')
    changeValue() {
      return this.categoryIdList
    }

    async query(id?: string) {
      return await ResourceModule.courseCategoryFactory.query.queryChildrenById(id)
    }

    currentChange(course: CourseCategoryListDetail, node: any) {
      this.selected = course
      this.selectedNode = node
      // 单选回显，手动失焦
      this.selectItem = course.name
      this.elSelect.blur()
    }

    certain() {
      if (!this.selectItem) return this.cancel()
      if (this.validateChooseCategory(this.selected)) {
        if (this.selectedNode?.childNodes?.length) {
          return this.$message.warning('只能选择叶子节点')
        }
        this.$emit('confirm', this.selected)
        this.cancel()
      }
    }
  }
</script>
