import { ResponseStatus } from '@hbfe/common'
import IntelligenceLearningModel from '@api/service/management/intelligence-learning/model/IntelligenceLearningModel'
import MsAutolearning from '@api/ms-gateway/ms-autolearning-v1'
import MsAutolearningSmartSerivice from '@api/ms-gateway/ms-autolearning-online-school-smart-learning-service-v1'
import MSAutoLearningTask, {
  StudentAutoLearningTaskResult,
  StudentAutoLearningTaskResultQueryPageRequest
} from '@api/ms-gateway/ms-autolearning-student-auto-learning-task-result-v1'
import Context from '@api/service/common/context/Context'
export default class IntelligenceLearningModule {
  /**
   * 智能学习配置
   */
  IntelligenceLearningModel = new IntelligenceLearningModel()

  /**
   * 查询智能学习规则 网校配置了智能学习增值服务的情况下才能查到
   */
  async doQuery(servicerId?: string) {
    const res = await MsAutolearningSmartSerivice.queryOnlineSchoolSmartLearningServiceConfig(servicerId)
    if (res.status.isSuccess()) {
      return this.IntelligenceLearningModel.from(res.data)
    } else {
      return new IntelligenceLearningModel()
    }
  }

  /**
   * 保存智能学习规则
   */
  async doSave() {
    const res = await MsAutolearning.updateOnlineSchoolSmartLearningServiceConfig(this.IntelligenceLearningModel.to())
    return res.status
  }

  /**
   * 校验学习方案是否存在自动学习 单个
   * @param schemeId
   */
  async doCheck(schemeId: string) {
    let res
    try {
      res = await MsAutolearning.validLearningSchemeExistAutoLearning({ learningSchemeIdList: [schemeId] })
    } catch (error) {
      console.log(error, 'error 校验学习方案是否存在自动学习')
    }
    const resultMap = res?.data?.resultMap
    return resultMap[schemeId]
  }

  /**
   * 校验学习方案是否存在自动学习 批量
   * @param schemeIdList
   */
  async doBatchCheck(schemeIdList: string[]) {
    let res
    try {
      res = await MsAutolearning.validLearningSchemeExistAutoLearning({ learningSchemeIdList: schemeIdList })
    } catch (error) {
      console.log(error, 'error 校验学习方案是否存在自动学习')
    }

    return res?.data?.resultMap || new Map<string, string>()
  }

  /**
   * 网校内 无缓存
   * 查询网校智能学习服务配置(查询是否开通)
   * 返回结果：未开启0 正常1 失效2
   */
  async doQueryServiceConfig() {
    const servicerId = Context.servicerInfo.id
    const res = await MsAutolearningSmartSerivice.queryOnlineSchoolSmartLearningServiceConfigByServicerId(servicerId)
    return res?.data || 0
  }

  /**
   * 网校内 有缓存 用于开过就一直显示的
   * 查询网校智能学习服务配置(查询是否开通)
   * 返回结果：未开启0 正常1 失效2
   */
  async doQueryServiceConfigWithCache() {
    const res = await MsAutolearningSmartSerivice.queryOnlineSchoolSmartLearningServiceConfigExist()
    return res?.data || 0
  }

  /**
   * 运营域 用服务商Id
   * 查询网校智能学习服务配置(查询是否开通)
   * 返回结果：未开启0 正常1 失效2
   */
  async doQueryServiceConfigByServicerId(servicerId: string) {
    const res = await MsAutolearningSmartSerivice.queryOnlineSchoolSmartLearningServiceConfigByServicerId(servicerId)
    return res?.data || 0
  }
  /**
   * 查询学员智能学习任务信息（业务咨询学习内容列表）
   * @param qualificationIdList
   */
  async doQuerylearningStatus(qualificationIdList: string[]): Promise<StudentAutoLearningTaskResult[]> {
    const res = await MSAutoLearningTask.queryNewestTaskResultByQualificationIds(qualificationIdList)
    let result = res?.data || []
    if (result.length) {
      result = result.map((item) => {
        if (item.result == 8) {
          // 新增状态编排中，这边也为待处理
          item.result = 0
        }
        return item
      })
    }
    return result || []
  }
}
