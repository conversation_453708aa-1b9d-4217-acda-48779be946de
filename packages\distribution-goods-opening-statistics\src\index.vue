//分销商分销商品开通统计
<template>
  <div
    class="f-p15"
    v-if="$hasPermission('queryGoodsOpeningStatistics')"
    desc="查询分销商品开通统计"
    query
    actions="search,@BizDistributorSelect,@BizPortalSelect"
  >
    <el-card shadow="never" class="m-card f-mb15">
      <hb-search-wrapper class="m-query is-border-bottom" @reset="resetSearch">
        <el-form-item label="分销商品">
          <el-input
            v-model="distributionGoodsOpeningStatistics.param.distributedTradeName"
            clearable
            placeholder="请输入分销商品名称"
          />
        </el-form-item>
        <el-form-item label="培训形式">
          <biz-training-mode-select
            v-model="distributionGoodsOpeningStatistics.param.trainingWay"
          ></biz-training-mode-select>
        </el-form-item>
        <el-form-item label="推广门户简称">
          <biz-portal-select
            :disabled="distributionGoodsOpeningStatistics.param.isPortalData"
            v-model="distributionGoodsOpeningStatistics.param.portalPromoteId"
            :name="distributionGoodsOpeningStatistics.param.portalPromoteTheName"
          ></biz-portal-select>
        </el-form-item>
        <el-form-item label="报名时间">
          <el-date-picker
            v-model="dates"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="报名成功时间"
            end-placeholder="报名成功时间"
          />
        </el-form-item>
        <el-form-item label="分销价格">
          <div class="f-flex">
            <el-input
              v-model="distributionGoodsOpeningStatistics.param.distributionPrice.min"
              placeholder="请输入价格"
              class="input-num f-flex-sub"
            />
            <i class="f-mlr10">~</i>
            <el-input
              v-model="distributionGoodsOpeningStatistics.param.distributionPrice.max"
              placeholder="请输入价格"
              class="input-num f-flex-sub"
            />
          </div>
        </el-form-item>
        <el-form-item>
          <el-checkbox
            label="查看非门户推广数据"
            name="type"
            v-model="distributionGoodsOpeningStatistics.param.isPortalData"
          ></el-checkbox>
        </el-form-item>
        <template slot="actions">
          <el-button type="primary" @click="search">查询</el-button>
          <el-button
            @click="exportData"
            v-if="$hasPermission('exportDataGoodsOpeningStatistics')"
            desc="导出分销商品开通统计"
            query
            actions="exportData"
            >导出列表数据</el-button
          >
        </template>
      </hb-search-wrapper>
      <!--操作栏-->
      <div class="f-mt20">
        <el-alert type="warning" :closable="false" class="m-alert f-clear">
          <div class="f-c6 f-fl">
            搜索结果合计：当前分销推广净开通
            <span class="f-fb f-co">{{ distributionGoodsOpeningStatistics.staticData.netTurnOn }}</span> 人次，成交总额
            <span class="f-fb f-co">¥ {{ distributionGoodsOpeningStatistics.staticData.transactionTotal }}</span>
          </div>
          <div class="f-fr f-csp f-flex f-align-center" @click="statisticalCaliberDeclarationDrawer.openDrawer()">
            <i class="el-icon-info f-f16 f-mr5"></i>统计说明
          </div>
        </el-alert>
      </div>
      <!--表格-->
      <el-table
        stripe
        :data="distributionGoodsOpeningStatistics.list"
        border
        class="m-table f-mt10"
        ref="distributionGoodsOpeningStatisticsTable"
        :span-method="objectSpanMethod"
        :summary-method="getSummaries"
        show-summary
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left">
          <template v-slot="{ row }">{{ row.no }}</template>
        </el-table-column>
        <el-table-column label="分销商品" min-width="160" fixed="left">
          <template v-slot="{ row }">{{ row.distributorInformation.goodsName }}</template>
        </el-table-column>
        <el-table-column label="培训属性" min-width="160">
          <template v-slot="{ row }">
            <p class="f-mb5">{{ getindustry(row) }}</p>
            <div class="f-c9 f-f12">
              <p v-for="(item, index) in getProcessedTrainingAttributes(row)" :key="index">
                {{ item }}
              </p>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="定价方式" min-width="160" align="center">
          <template v-slot="{ row }">
            <div>
              <el-tag :type="tagType(row)" size="mini" class="f-ml5 f-mr5">{{ row.pricingMethod.toString() }}</el-tag>
              ￥{{ row.price }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="合计" header-align="center">
          <el-table-column label="开通" min-width="90" align="right" prop="total.open">
            <template v-slot="{ row }">
              {{ row.total.open }}
            </template>
          </el-table-column>
          <el-table-column label="退班" min-width="90" align="right" prop="total.return">
            <template v-slot="{ row }">
              {{ row.total.return }}
            </template>
          </el-table-column>
          <el-table-column label="换入(换班)" min-width="110" align="right" prop="total.swapOutOfClass">
            <template v-slot="{ row }">
              {{ row.total.swapIntoAClass }}
            </template>
          </el-table-column>
          <el-table-column label="换出(换班)" min-width="110" align="right" prop="total.swapIntoAClass">
            <template v-slot="{ row }">
              {{ row.total.swapOutOfClass }}
            </template>
          </el-table-column>
          <el-table-column label="净开通" min-width="90" align="right" prop="total.count">
            <template v-slot="{ row }">
              {{ row.total.count }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="个人缴费" header-align="center">
          <el-table-column label="线上支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right" prop="individualOnline.open">
              <template v-slot="{ row }">{{ row.individualOnline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right" prop="individualOnline.return">
              <template v-slot="{ row }">{{ row.individualOnline.return }}</template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="110" align="right" prop="individualOnline.swapOutOfClass">
              <template v-slot="{ row }">{{ row.individualOnline.swapIntoAClass }}</template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="110" align="right" prop="individualOnline.swapIntoAClass">
              <template v-slot="{ row }">{{ row.individualOnline.swapOutOfClass }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right" prop="individualOnline.count">
              <template v-slot="{ row }">{{ row.individualOnline.count }}</template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
        <el-table-column label="集体报名" header-align="center">
          <el-table-column label="线上支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right" prop="collectivelyOnline.open">
              <template v-slot="{ row }">{{ row.collectivelyOnline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right" prop="collectivelyOnline.return">
              <template v-slot="{ row }">{{ row.collectivelyOnline.return }}</template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="110" align="right" prop="collectivelyOnline.swapOutOfClass">
              <template v-slot="{ row }">{{ row.collectivelyOnline.swapIntoAClass }}</template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="110" align="right" prop="collectivelyOnline.swapIntoAClass">
              <template v-slot="{ row }">{{ row.collectivelyOnline.swapOutOfClass }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right" prop="collectivelyOnline.count">
              <template v-slot="{ row }">{{ row.collectivelyOnline.count }}</template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="线下支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right" prop="collectivelyOffline.open">
              <template v-slot="{ row }">{{ row.collectivelyOffline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right" prop="collectivelyOffline.return">
              <template v-slot="{ row }">{{ row.collectivelyOffline.return }}</template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="110" align="right" prop="collectivelyOffline.swapOutOfClass">
              <template v-slot="{ row }">{{ row.collectivelyOffline.swapIntoAClass }}</template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="110" align="right" prop="collectivelyOffline.swapIntoAClass">
              <template v-slot="{ row }">{{ row.collectivelyOffline.swapOutOfClass }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right" prop="collectivelyOffline.count">
              <template v-slot="{ row }">{{ row.collectivelyOffline.count }}</template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
        <el-table-column label="导入开通" header-align="center">
          <el-table-column label="线下支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right" prop="importOffline.open">
              <template v-slot="{ row }">{{ row.importOffline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right" prop="importOffline.return">
              <template v-slot="{ row }">{{ row.importOffline.return }}</template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="110" align="right" prop="importOffline.swapOutOfClass">
              <template v-slot="{ row }">{{ row.importOffline.swapIntoAClass }}</template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="110" align="right" prop="importOffline.swapIntoAClass">
              <template v-slot="{ row }">{{ row.importOffline.swapOutOfClass }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right" prop="importOffline.count">
              <template v-slot="{ row }">{{ row.importOffline.count }}</template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page" />
    </el-card>

    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务查看，分销商品开通统计！</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
    <statistical-caliber-declaration-drawer
      ref="statisticalCaliberDeclarationDrawer"
      :search-data="distributionGoodsOpeningStatisticsSearchData"
      :field-data="distributionGoodsOpeningStatisticsFieldData"
    />
  </div>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import StatisticalCaliberDeclarationDrawer from '@hbfe/jxjy-admin-components/src/statistical-caliber-declaration-drawer.vue'
  import {
    distributionGoodsOpeningStatisticsFieldData,
    distributionGoodsOpeningStatisticsSearchData
  } from '@hbfe/jxjy-admin-components/src/models/statisticalExplanatoryData'
  import { UiPage } from '@hbfe/common'
  import DistributionGoodsOpeningStatistics from '@api/service/management/statisticalReport/DistributionGoodsOpeningStatistics/DistributionGoodsOpeningStatistics'
  import DistributionGoodsOpeningStatisticsParams from '@api/service/management/statisticalReport/DistributionGoodsOpeningStatistics/model/DistributionGoodsOpeningStatisticsParams'
  import { ElTable } from 'element-ui/types/table'
  import DistributionGoodsOpeningStatisticsItem from '@api/service/management/statisticalReport/DistributionGoodsOpeningStatistics/model/DistributionGoodsOpeningStatisticsItem'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-autocomplete.vue'
  import { DistributionPricingMethodsEnum } from '@api/service/management/statisticalReport/enums/DistributionPricingMethodsEnumsClass'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'

  @Component({ components: { StatisticalCaliberDeclarationDrawer, BizDistributorSelect, BizPortalSelect } })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.search, this.search)
    }
    // 统计口径说明抽屉ref
    @Ref('statisticalCaliberDeclarationDrawer') statisticalCaliberDeclarationDrawer: StatisticalCaliberDeclarationDrawer
    // 统计口径说明抽屉ref
    @Ref('distributionGoodsOpeningStatisticsTable') distributionGoodsOpeningStatisticsTable: ElTable
    // 分销商品开通统计——搜索字段说明
    get distributionGoodsOpeningStatisticsSearchData() {
      return distributionGoodsOpeningStatisticsSearchData
    }
    // 分销商品开通统计——列表字段说明
    get distributionGoodsOpeningStatisticsFieldData() {
      return distributionGoodsOpeningStatisticsFieldData
    }
    page: UiPage // 分页
    // 分销商品开通统计 模型
    distributionGoodsOpeningStatistics = new DistributionGoodsOpeningStatistics()

    spanList: { rowspan: number; colspan: number }[] = []

    get tagType() {
      return (item: DistributionGoodsOpeningStatisticsItem) => {
        return item.pricingMethod.equal(DistributionPricingMethodsEnum.licensing_pricing) ? 'warning' : 'success'
      }
    }

    async created() {
      await this.init()
    }

    getProcessedTrainingAttributes(row: DistributionGoodsOpeningStatisticsItem) {
      const arr = row.distributorInformation.trainingAttributes
      const filteredArr = arr.filter((item) => !item.includes('行业'))
      return filteredArr
    }

    getindustry(row: DistributionGoodsOpeningStatisticsItem) {
      const arr = row.distributorInformation.trainingAttributes
      const industry = arr.find((item) => item.includes('行业'))
      return industry
    }
    /**
     * 初始化
     */
    async init() {
      await this.search()
    }

    dates: Array<string> = []
    /**
     * 搜索列表
     */
    async search() {
      if (this.dates?.length) {
        this.distributionGoodsOpeningStatistics.param.registrationPeriod.begin = this.dates[0]
        this.distributionGoodsOpeningStatistics.param.registrationPeriod.end = this.dates[1]
      } else {
        this.distributionGoodsOpeningStatistics.param.registrationPeriod.begin = ''
        this.distributionGoodsOpeningStatistics.param.registrationPeriod.end = ''
      }
      await this.distributionGoodsOpeningStatistics.queryListDistributor(this.page)

      await this.distributionGoodsOpeningStatistics.queryTotalStaticDistributor()
      await this.distributionGoodsOpeningStatistics.querySummaryStaticDistributor()
      this.$nextTick(() => {
        this.calculationSpan()
        this.distributionGoodsOpeningStatisticsTable.doLayout()
      })
    }

    /**
     * 导出数据
     */

    exportSuccessVisible = false
    /**
     * 导出数据
     */
    async exportData() {
      try {
        if (this.dates?.length) {
          this.distributionGoodsOpeningStatistics.param.registrationPeriod.begin = this.dates[0]
          this.distributionGoodsOpeningStatistics.param.registrationPeriod.end = this.dates[1]
        } else {
          this.distributionGoodsOpeningStatistics.param.registrationPeriod.begin = ''
          this.distributionGoodsOpeningStatistics.param.registrationPeriod.end = ''
        }

        const res = await await this.distributionGoodsOpeningStatistics.exportListDistributor()
        if (res.status.code == 200 && res.data) {
          //   this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        //console.log(e)
      } finally {
        //todo
      }
    }

    goDownloadPage() {
      this.exportSuccessVisible = false
      let type = 'exportCommodityOpenSummary'
      const roleCategoryList = AuthorityModule.securityFactory.querySecurity.roleInfoList.map((res) => res.category)
      type = roleCategoryList.includes(18) ? 'exportCommodityDistributorOpenReport' : 'exportCommodityOpenSummary'
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type }
      })
    }

    /**
     * 重置
     */
    async resetSearch() {
      this.distributionGoodsOpeningStatistics.param = new DistributionGoodsOpeningStatisticsParams()
      this.distributionGoodsOpeningStatistics.param.isPortalData = null
      this.dates = []
      await this.init()
    }

    /**
     * 计算合并行方法
     */
    calculationSpan() {
      this.spanList = []
      this.distributionGoodsOpeningStatistics.list.map((res) => {
        if (res.colspan > 0) {
          this.spanList.push({
            rowspan: res.colspan,
            colspan: 1
          })
        } else {
          this.spanList.push({ rowspan: 0, colspan: 0 })
        }
      })
    }

    /**
     * 合并坊方法
     * @param item
     */
    objectSpanMethod(item: { row: any; column: any; rowIndex: number; columnIndex: number }) {
      if (this.spanList.length) {
        if (item.columnIndex <= 2) {
          return this.spanList[item.rowIndex]
        }
      }
    }
    // 自定义合计方法，仅对合计列进行合计
    getSummaries({ columns, data }: { columns: any[]; data: any[] }) {
      const sums: any[] = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计' // 第一列显示合计
        } else if (index > 3) {
          // 只对从第四列（index 4）开始的列进行合计
          const property = column.property
          const summary = this.distributionGoodsOpeningStatistics.summaryStaticData
          if (property) {
            const before = property.split('.')[0]
            const after = property.split('.').slice(1).join('.')
            switch (before) {
              case 'total':
                sums[index] = summary.find((item) => item.summaryType == 1)?.summaryInfo[after] || 0
                break
              case 'individualOnline':
                sums[index] = summary.find((item) => item.summaryType == 2)?.summaryInfo[after] || 0
                break
              case 'collectivelyOnline':
                sums[index] = summary.find((item) => item.summaryType == 3)?.summaryInfo[after] || 0
                break
              case 'collectivelyOffline':
                sums[index] = summary.find((item) => item.summaryType == 4)?.summaryInfo[after] || 0
                break
              case 'importOffline':
                sums[index] = summary.find((item) => item.summaryType == 5)?.summaryInfo[after] || 0
                break
              default:
                break
            }
          } else {
            sums[index] = ''
          }
        } else {
          sums[index] = '' // 前四列不做任何合计
        }
      })
      return sums
    }
  }
</script>
