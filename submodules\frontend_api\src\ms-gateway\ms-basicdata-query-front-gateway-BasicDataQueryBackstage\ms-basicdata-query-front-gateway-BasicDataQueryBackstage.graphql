"""独立部署的微服务,K8S服务名:ms-basicdata-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""根据服务商(网校id)、行业id、行业属性类别（业务、人员类别）查询行业属性id
		@param industryPropertyInfoQueryRequest 查询条件
		@return 行业属性id
	"""
	findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType(industryPropertyInfoQueryRequest:IndustryPropertyInfoQueryRequest):[IndustryPropertyInfoResponse] @optionalLogin
	"""功能描述：获取我的管理员信息接口
		描述：查询当前登录管理员的信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.AdminInfoResponse
		@date : 2022/4/2 14:19
	"""
	getAdminInfoInMyself:AdminInfoResponse
	"""功能描述：获取网校管理员信息接口
		描述：查询当前网校下指定管理员的信息，如不存在返回null
		@param adminUserId             :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.AdminInfoResponse
		@date : 2022/4/2 14:19
	"""
	getAdminInfoInServicer(adminUserId:String):AdminInfoResponse
	"""功能描述：获取子项目下管理员信息接口
		描述：查询当前子项目下指定管理员的信息，如不存在返回null
		@param adminUserId             :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.AdminInfoResponse
		@date : 2022/4/2 14:19
	"""
	getAdminInfoInSubProject(adminUserId:String):AdminInfoResponse
	"""功能描述：获取子项目下管理员的用户信息接口
		描述：查询当前子项目下指定管理员的用户信息，如不存在返回null
		@param adminUserId             :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.AdminInfoResponse
		@date : 2022/4/2 14:19
	"""
	getAdminUserInfoInSubProject(adminUserId:String):AdminUserInfoResponse
	"""获取指定门户下的获取轮播图集合
		@param portalId 门户id
		@return 轮播图集合响应
	"""
	getBannerListByPortalId(portalId:String):BannerInfoListResponse @optionalLogin
	"""获取轮播图集合
		@param portalType 门户类型
		@return 轮播图集合响应
	"""
	getBannerListByPortalType(portalType:Int!):BannerInfoListResponse @optionalLogin
	"""获取轮播图集合
		-分销平台使用 由于分销平台取上下文的方法与其他项目不一致
		@param portalType 门户类型
		@return 轮播图集合响应
	"""
	getBannerListByPortalTypeForFxpt(portalType:Int!):BannerInfoListResponse @optionalLogin
	"""根据id查询业务地区
		@param id 业务地区编码
		@return 业务地区
	"""
	getBusinessRegionById(id:String!):BusinessRegionResponse @optionalLogin
	"""根据地区编码和业务id查找一条地区
		@param request 请求参数对象
		@return 业务地区
	"""
	getBusinessRegionTree(request:BusinessRegionTreeQueryRequest!):BusinessTreeRegionResponse @optionalLogin
	"""获取网校合约客户端信息"""
	getClientInfoByServicerId(servicerId:String):[ClientInfoResponse] @optionalLogin
	"""功能描述：根据集体缴费管理员id获取网校集体缴费管理员
		描述：查询当前网校下的集体缴费管理员信息
		@param collectiveId            :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.collective.CollectiveInfoResponse
		@date : 2022/5/27 08:56
	"""
	getCollectiveInfoInServicer(collectiveId:String):CollectiveInfoResponse @optionalLogin
	"""功能描述 : 服务商-获取当前登录合同服务商管理员详情接口
		@date : 2022/7/28 20:50
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse
	"""
	getContractProviderInfoAdminInfoInMyself:ContractProviderAdminInfoResponse
	"""功能描述 : 服务商-合同服务商管理员详情查询接口
		@date : 2022/7/28 20:50
		@param accountId :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse
	"""
	getContractProviderInfoAdminInfoInServicer(accountId:String):ContractProviderAdminInfoResponse
	"""功能描述 : 项目级-合同服务商管理员详情查询接口
		@date : 2022/7/28 20:50
		@param accountId :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse
	"""
	getContractProviderInfoAdminInfoInSubProject(accountId:String):ContractProviderAdminInfoResponse @optionalLogin
	"""功能描述 : 合同供应商-查询当前登录服务商合同信息接口
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.ContractProviderResponse
		@date : 2022/7/16 14:54
	"""
	getContractProviderInfoInMyself:ContractProviderResponse
	"""功能描述 : 项目级-合同供应商详情查询接口
		@param servicerId              :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.ContractProviderResponse
		@date : 2022/7/16 14:54
	"""
	getContractProviderInfoInSubProject(servicerId:String):ContractProviderResponse @optionalLogin
	"""功能描述 : 服务商-获取当前登录课件供应商管理员详情接口
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ServicerAdminInfoResponse
		@date : 2022/7/28 20:50
	"""
	getCoursewareSupplierAdminInfoInMyself:ServicerAdminInfoResponse
	"""功能描述 : 服务商-课件供应商管理员详情查询接口
		@param accountId               :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ServicerAdminInfoResponse
		@date : 2022/7/28 20:50
	"""
	getCoursewareSupplierAdminInfoInServicer(accountId:String):ServicerAdminInfoResponse
	"""功能描述 : 项目级-课件供应商管理员详情查询接口
		@param accountId               :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ServicerAdminInfoResponse
		@date : 2022/7/28 20:50
	"""
	getCoursewareSupplierAdminInfoInSubProject(accountId:String):ServicerAdminInfoResponse @optionalLogin
	"""功能描述 : 查询当前登录课件供应商信息接口
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.CoursewareSupplierResponse
		@date : 2022年11月1日 11:36:22
	"""
	getCoursewareSupplierInfoInMyself:CoursewareSupplierResponse
	"""功能描述 : 项目级-课件供应商详情查询接口
		@param servicerId              :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.CoursewareSupplierResponse
		@date : 2022年11月1日 11:36:13
	"""
	getCoursewareSupplierInfoInSubProject(servicerId:String):CoursewareSupplierResponse @optionalLogin
	"""上下文获取当前服务商信息
		@return 服务商信息
	"""
	getCurrentServicerInfo:ServicerInfoResponse
	"""功能描述 : 查询当前登录分销商信息接口"""
	getDistributionServicerInfoInMyself:DistributionServicerResponse
	"""功能描述 : 项目级-分销服务商详情查询接口"""
	getDistributionServicerInfoInSubProject(servicerId:String):DistributionServicerResponse @optionalLogin
	"""功能描述：企业-当前登录企业管理员信息
		描述：查询当前登录管理员的信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
	"""
	getEnterpriseUnitAdminInfoInMyself:EnterpriseUnitAdminInfoResponse
	"""功能描述：项目级-企业单位管理员详情查询接口
		描述：查询当前企业单位下指定管理员的信息，如不存在返回null
		@param accountId               : 帐户ID
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
		@date : 2022年6月9日 16:50:10
	"""
	getEnterpriseUnitAdminInfoInSubProject(accountId:String):EnterpriseUnitAdminInfoResponse @optionalLogin
	"""政府单位下-按条件统计企业单位总数量
		@param request 企业单位统计查询条件
		@return 统计数量结果
	"""
	getEnterpriseUnitCountInGovernmentUnit(request:EnterpriseUnitStatisticRequest):Long!
	"""子项目下-按条件统计企业单位总数量
		@param request 企业单位统计查询条件
		@return 统计数量结果
	"""
	getEnterpriseUnitCountInSubProject(request:EnterpriseUnitStatisticRequest):Long! @optionalLogin
	"""功能描述：企业单位-当前登录企业单位详情查询接口
		描述：查询当前企业单位信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse
		@date : 2022年6月9日 10:49:47
	"""
	getEnterpriseUnitInfoInMyself:EnterpriseUnitInfoResponse
	"""网校级 - 根据单位ID查询当前网校下企业单位信息"""
	getEnterpriseUnitInfoInServicer(unitId:String):EnterpriseUnitInfoResponse @optionalLogin
	"""功能描述：项目级-企业单位详情查询接口
		描述：查询当前子项目下指定企业单位的信息
		@param unitId                  :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse
		@date : 2022年6月9日 10:49:47
	"""
	getEnterpriseUnitInfoInSubProject(unitId:String):EnterpriseUnitInfoResponse @optionalLogin
	"""获取指定门户下的友情链接集合
		@param portalId 门户id
		@return 友情链接集合响应
	"""
	getFriendLinkListByPortalId(portalId:String):FriendLinkListResponse @optionalLogin
	"""获取友情链接集合
		@param portalType 门户类型
		@return 友情链接集合响应
	"""
	getFriendLinkListByPortalType(portalType:Int!):FriendLinkListResponse @optionalLogin
	"""功能描述：政府单位-当前登录政府单位管理员信息
		描述：查询当前登录管理员的信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
	"""
	getGovernmentUnitAdminInfoInMyself:GovernmentUnitAdminInfoResponse
	"""功能描述：政府单位管理员详情查询接口
		描述：查询当前政府单位下指定管理员的信息，如不存在返回null
		@param accountId               : 帐户ID
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.GovernmentUnitAdminInfoResponse
		@date : 2022年6月9日 16:50:10
	"""
	getGovernmentUnitAdminInfoInSubProject(accountId:String):GovernmentUnitAdminInfoResponse @optionalLogin
	"""功能描述：获取政府单位信息接口
		描述：查询当前子项目下指定政府单位的信息
		@param unitId                  :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.GovernmentUnitInfoResponse
		@date : 2022年6月9日 10:49:47
	"""
	getGovernmentUnitInfoInSubProject(unitId:String):GovernmentUnitInfoResponse @optionalLogin
	"""查询指定行业信息
		@param industryId 行业编号
		@return 行业信息
	"""
	getIndustryInfo(industryId:String!):IndustryResponse @optionalLogin
	"""查询指定行业信息
		@param industryId 行业编号
		@return 行业信息
	"""
	getIndustryInfoV2(industryId:String!):IndustryResponse @optionalLogin
	"""查询行业属性 - 平台级
		@param industryPropertyId 行业培训属性ID
	"""
	getIndustryPropertyByIdInSubProject(industryPropertyId:String):IndustryPropertyResponse @optionalLogin
	"""查询行業类型"""
	getIndustryType(businessId:String,code:String):GetIndustryTypeResponse @optionalLogin
	"""查询职称等级信息
		@param levelId 职称等级编号
		@return 职称等级信息
	"""
	getLeaderPositionLevel(levelId:String!):LeaderPositionLevelResponse @optionalLogin
	"""获取栏目配置信息集合
		@param portalType 栏目类型
		@return 栏目配置集合响应
	"""
	getMenuSettingByPortalType(portalType:Int!):MenuSettingInfoListResponse @optionalLogin
	"""根据分类id获取顶级分类信息
		@param rootCategoryCode 顶级分类代码
		@param code 分类代码
		@return
	"""
	getNewsCategoryId(rootCategoryCode:String!,code:String!):NewsCategoryResponse @optionalLogin
	"""查询详细资讯
		@param newId 资讯id
		@return 资讯信息
	"""
	getNewsDetail(newId:String!):NewsDetailResponse
	"""功能描述 : 服务商-获取当前登录合同服务商管理员详情接口
		@date : 2022/7/28 20:50
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse
	"""
	getOnlineSchoolAdminInfoInMyself:ContractProviderAdminInfoResponse
	"""功能描述 : 服务商-合同服务商管理员详情查询接口
		@date : 2022/7/28 20:50
		@param accountId :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse
	"""
	getOnlineSchoolAdminInfoInServicer(accountId:String):ContractProviderAdminInfoResponse
	"""功能描述 : 项目级-合同服务商管理员详情查询接口"""
	getOnlineSchoolAdminInfoInSubProject(accountId:String):ContractProviderAdminInfoResponse @optionalLogin
	"""功能描述 : 查询网校信息-网校开通统计接口"""
	getOnlineSchoolInfoCount(request:OnlineSchoolInfoRequest):OnlineSchoolCountResponse
	"""功能描述 : 网校信息查询接口"""
	getOnlineSchoolInfoResponseInSubProject(onlineSchoolId:String):OnlineSchoolInfoResponse
	"""根据id获取物理地区信息
		@param id 物理地区编号
		@return 物理地区信息
	"""
	getPhysicalRegionById(id:String!):PhysicalRegionResponse @optionalLogin
	"""功能描述：项目级-根据门户ID查询门户信息-详情接口
		@param portalId                : 门户id
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.PortalResponse
		@Author： wtl
		@Date： 2022/10/19 10:32
	"""
	getPortalInfoInSubProject(portalId:String):PortalResponse @optionalLogin
	"""根据项目id查询顶级资讯分类
		@return 资讯分类信息
	"""
	getRootNewsCategory:[NewsCategoryResponse]
	"""根据分类id获取顶级分类信息
		@param necId 分类id
		@return
	"""
	getRootNewsCategoryById(necId:String!):NewsCategoryResponse
	"""@param type 服务类型 0或1
		@return 服务地区列表
		查询服务地区
		@Date 2023/6/14 17:57
	"""
	getServiceOrIndustryRegion(type:Int!):[RegionResponse] @optionalLogin
	"""@description: 查询服务地区（这里只是对原方法进行二次筛选）
		@author: linq
		@date: 2023/8/22 15:54
	"""
	getServiceOrIndustryRegionByQuery(request:ServiceOrIndustryRegionQueryRequest):[RegionResponse] @optionalLogin
	"""@param type 服务类型 0或1
		@return 服务地区列表(分销商)
		查询服务地区
		@Date 2023/6/14 17:57
	"""
	getServiceOrIndustryRegionInDistribution(type:Int!):[RegionResponse]
	"""功能描述 :获取网校学员详细信息接口
		描述：查询当前网校下指定管理员的信息，如不存在返回null
		@param studentUserId           :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse
		@date : 2022/3/31 16:54
	"""
	getStudentInfoInServicer(studentUserId:String):StudentInfoResponse
	"""功能描述 :子项目-获取学员详细信息接口-详细接口
		描述：查询子项目下指定的学员信息，如不存在返回null
		@param studentUserId           :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse
		@date : 2022年11月9日 14:48:20
	"""
	getStudentInfoInSubProject(studentUserId:String):StudentInfoResponse
	"""功能描述 :获取子项目学员的用户详细信息接口
		描述：查询当前子项目下指定学员的用户信息，如不存在返回null
		@param studentUserId           :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse
		@date : 2022/3/31 16:54
	"""
	getStudentInfoInSubject(studentUserId:String):StudentUserInfoResponse
	"""功能描述：获取网校在线学员数量接口
		描述：查询当前网校下时间段内累计在线学员数量接口,不传时间段则查询历史累计在线学员数量（一人一天计算一次）
		@return : java.lang.Long
		@Author： wtl
		@Date： 2022年1月25日 16:29:08
	"""
	getStudentOnlineCountInServicer(dateScopeRequest:DateScopeRequest):Long!
	"""功能描述：获取子项目下学员注册数量统计接口
		@param studentRegisteredStatisticsRequest:统计查询条件
		@return :java.util.List
		@Author： wtl
		@Date： 2022年5月11日 09:57:01
	"""
	getStudentRegisteredCountInSubProject(studentRegisteredStatisticsRequest:StudentRegisteredStatisticsRequest):Long!
	"""功能描述 : 查询当前登录供应商信息接口"""
	getSupplierServicerInfoInMyself:SupplierServicerResponse
	"""功能描述 : 项目级-供应服务商详情查询接口"""
	getSupplierServicerInfoInSubProject(servicerId:String):SupplierServicerResponse @optionalLogin
	"""查询指定培训类别信息
		@param categoryId 培训类别编号
		@return 培训类别信息
	"""
	getTrainingCategoryInfo(categoryId:String!):TrainingCategoryResponse @optionalLogin
	"""上下文服务商id、门户类型获取门户信息
		@param portalType 门户类型(包含富文本信息) 1-WEB 2-移动端
		@param servicerId 服务商ID
		@return 门户信息
	"""
	getTrainingInstitutionPortalInfo(portalType:Int!,servicerId:String):PortalInfoResponse1 @optionalLogin
	"""获取门户主题颜色
		@return 主题颜色标识
	"""
	getTrainingInstitutionPortalThemeColor:String @optionalLogin
	"""查询单位类型"""
	getUnitType(businessId:String,code:String):GetUnitTypeResponse @optionalLogin
	"""根据年度编号查询业务年度
		@param yearId 年度编号
		@return 年度信息
	"""
	getYearById(yearId:String!):BusinessYearResponse @optionalLogin
	"""查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表
		这个口不会去查业务那边的配置
		@param categoryCode 行业属性分类代码
		@return 培训属性列表
	"""
	listALLIndustryPropertyRootByCategory(industryId:String!,categoryCode:String!):[TrainingPropertyResponse] @optionalLogin
	"""查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表V2
		这个口不会去查业务那边的配置
	"""
	listALLIndustryPropertyRootByCategoryV2(request:TrainingPropertyCommonRequest):[TrainingPropertyResponse] @optionalLogin
	"""查询指定行业属性编号下的所有培训属性列表"""
	listAllIndustryProperty(industryPropertyId:String!,industryId:String!):[TrainingPropertyResponse] @optionalLogin
	"""获取地区专题用户信息
		@param userId 用户id（必填）
	"""
	listAreaTrainingChannelStudentInfoInSubProject(userId:String):[AreaTrainingChannelInfoResponse]
	"""根据下级字典获取上级字典信息"""
	listBusinessDictionaryAcrossTypeBySalveId(salveId:String!):[BusinessDictionaryAcrossTypeResponse] @optionalLogin
	"""获取指定业务地区的下一级业务地区列表
		@param id 业务地区编号
		@return 业务地区
	"""
	listBusinessRegionChildById(id:String!):[BusinessRegionResponse] @optionalLogin
	"""根据id列表查询业务地区
		@param idList 业务地区编号，最大支持200个
		@return 业务地区
	"""
	listBusinessRegionListById(idList:[String]!):[BusinessRegionResponse] @optionalLogin
	"""批量查询指定路径下行政区划名称
		@param pathList 行政区划路径，格式：/350000/350100/350101
		@return 路径下行政区划名称
	"""
	listBusinessRegionNameMap(pathList:[String]!):[BusinessTreeRegionNameMap] @optionalLogin
	"""查找指定业务代码的地区树结构的指定父节点下的子节点
		@param request 请求参数对象
		@return 业务地区
	"""
	listBusinessRegionTreeChild(request:BusinessRegionTreeQueryChildrenRequest!):[BusinessTreeRegionResponse] @optionalLogin
	"""查找指定业务代码的地区树结构的根节点
		@param request 请求参数对象
		@return 业务地区
	"""
	listBusinessRegionTreeRoot(request:BusinessRegionTreeQueryRootRequest!):[BusinessTreeRegionResponse] @optionalLogin
	listChildNewsCategory(status:Int!,necId:String!):[NewsCategoryResponse] @optionalLogin
	listChildNewsCategoryInServicer(status:Int!,necId:String!):[NewsCategoryResponse] @optionalLogin
	listChildNewsCategoryNeedLogin(status:Int!,necId:String!):[NewsCategoryResponse]
	"""查询学历字典"""
	listEducationProperty:[TrainingPropertyResponse] @optionalLogin
	"""查询指定行业信息
		@param industryIds 行业编号，最大支持200个
		@return 行业信息
	"""
	listIndustryInfo(industryIds:[String]!):[IndustryResponse] @optionalLogin
	"""查询指定行业信息  v2
		@param industryIds 行业编号，最大支持200个
		@return 行业信息
	"""
	listIndustryInfoV2(industryIds:[String]!):[IndustryResponse] @optionalLogin
	"""查询指定查询条件下的培训属性信息列表
		@param request 查询条件
		@return 培训属性信息
		<AUTHOR>
	"""
	listIndustryPropertyByOnlineSchool(request:SchoolTrainingPropertyQueryRequest!):[TrainingPropertyResponse] @optionalLogin
	"""查询指定查询条件下的培训属性信息列表
		@param request 查询条件
		@return 培训属性信息
		<AUTHOR>
	"""
	listIndustryPropertyByOnlineSchoolV2(request:SchoolTrainingPropertyQueryRequest!):[TrainingPropertyResponse] @optionalLogin
	"""查询行业属性编号下行业属性分类列表
		@param industryPropertyId 行业属性编号
		@return 行业属性分类列表
	"""
	listIndustryPropertyCategory(industryPropertyId:String!):[IndustryPropertyCategoryResponse] @optionalLogin
	"""查询行业属性编号下行业属性分类列表
		@param industryPropertyId 行业属性编号
		@return 行业属性分类列表
	"""
	listIndustryPropertyCategoryV2(industryPropertyId:String!):[IndustryPropertyCategoryResponse] @optionalLogin
	"""查询指定行业属性编号下指定行业属性分类下指定培训属性编号下子节点培训属性列表
		@param request 查询条件
		@return 培训属性列表
	"""
	listIndustryPropertyChildByCategory(request:TrainingPropertyQueryRequest!):[TrainingPropertyResponse] @optionalLogin
	"""查询指定行业属性编号下指定行业属性分类下指定培训属性编号下子节点培训属性列表
		@param request 查询条件
		@return 培训属性列表
	"""
	listIndustryPropertyChildByCategoryV2(request:TrainingPropertyQueryRequest!):[TrainingPropertyResponse] @optionalLogin
	"""查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表
		@param industryPropertyId 行业属性编号
		@param categoryCode       行业属性分类代码
		@return 培训属性列表
	"""
	listIndustryPropertyRootByCategory(industryPropertyId:String!,categoryCode:String!):[TrainingPropertyResponse] @optionalLogin
	"""查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表
		@param industryPropertyId 行业属性编号
		@param categoryCode       行业属性分类代码
		@return 培训属性列表
	"""
	listIndustryPropertyRootByCategoryV2(industryPropertyId:String!,industryId:String!,categoryCode:String!):[TrainingPropertyResponse] @optionalLogin
	"""查询行业类型子节点列表"""
	listIndustryTypeChild(businessId:String,parentId:String):[IndustryTypeResponse] @optionalLogin
	"""查询行业类型父节点列表
		@param businessId
	"""
	listIndustryTypeRoot(businessId:String):[IndustryTypeResponse] @optionalLogin
	"""查询职称等级信息
		@param levelIds 职称等级编号，最大支持200个
		@return 职称等级信息
	"""
	listLeaderPositionLevel(levelIds:[String]!):[LeaderPositionLevelResponse] @optionalLogin
	"""查询根节点的职称等级列表
		@return 职称等级列表
	"""
	listLeaderPositionLevelRoot:[LeaderPositionLevelResponse] @optionalLogin
	listNewsCategoryTree(status:Int!,parentId:String):[NewsCategoryTreeResponse] @optionalLogin
	"""获取指定物理地区的下一级业务地区列表
		@param id 物理地区编号
		@return 物理地区
	"""
	listPhysicalRegionChildById(id:String!):[PhysicalRegionResponse] @optionalLogin
	"""根据id列表查询物理地区
		@param idList 物理地区编号，最大支持200个
		@return 物理地区
	"""
	listPhysicalRegionListById(idList:[String]!):[PhysicalRegionResponse] @optionalLogin
	listRootNewsCategory(status:Int!):[NewsCategoryResponse] @optionalLogin
	listRootNewsCategoryNeedLogin(status:Int!):[NewsCategoryResponse]
	"""根据主字典ID获取所有从字典 （用于上下级关系的字典类型）
		@param masterId
		@param salveDictionaryType
		@return
	"""
	listSalveDictionaryByMasterId(masterId:String!,salveDictionaryType:String!):[TrainingPropertyResponse] @optionalLogin
	"""查询当前网校合约下的培训属性"""
	listServicerContractPropertyByCategory(request:ServicerContractPropertyByCategoryRequest):[TrainingPropertyResponse] @optionalLogin
	"""查询指定行业下的培训类别子节点列表
		@param industryId 行业编号
		@param categoryId 培训类别编号
		@return 培训类别列表
	"""
	listTrainingCategoryChild(industryId:String!,categoryId:String!):[TrainingCategoryResponse] @optionalLogin
	"""查询指定培训类别信息
		@param categoryIds 培训类别编号，最大支持200个
		@return 培训类别信息
	"""
	listTrainingCategoryInfo(categoryIds:[String]!):[TrainingCategoryResponse] @optionalLogin
	"""查询指定行业下的根节点培训类别
		@param industryId 行业编号
		@return 培训类别列表
	"""
	listTrainingCategoryRoot(industryId:String!):[TrainingCategoryResponse] @optionalLogin
	"""查询单位类型子节点列表"""
	listUnitTypeChild(businessId:String,parentId:String):[UnitTypeResponse] @optionalLogin
	"""查询单位类型父节点列表"""
	listUnitTypeRoot(businessId:String):[UnitTypeResponse] @optionalLogin
	"""根据年度编号列表查询业务年度
		@param yearIdList 业务年度编号,最大支持200个
		@return 业务年度列表
	"""
	listYearListById(yearIdList:[String]!):[BusinessYearResponse] @optionalLogin
	"""分页查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表V2"""
	pageALLIndustryPropertyRootByCategoryV2(page:Page,request:TrainingPropertyCommonRequest):TrainingPropertyResponsePage @page(for:"TrainingPropertyResponse") @optionalLogin
	"""功能描述：分页获取用户信息列表接口
		描述：查询当前网校下的管理员信息，默认按创建时间降序排
	"""
	pageAccountInfoInServicer(page:Page,request:AccountUserInfoQueryRequest):UserInfoResponsePage @page(for:"UserInfoResponse")
	"""功能描述：分页获取用户信息列表接口
		描述：查询当前子项目下的管理员信息，默认按创建时间降序排，如不存在返回null
		@param page
		@param request
	"""
	pageAccountInfoInSubProject(page:Page,request:AccountUserInfoQueryRequest):UserInfoResponsePage @page(for:"UserInfoResponse")
	"""功能描述：分页获取分销商管理员列表接口
		描述：查询当前网校下的管理员信息(查询自己以及自己创建的管理员)，默认按创建时间降序排，如不存在返回null
	"""
	pageAdminInInDistributor(page:Page,request:AdminQueryRequest):AdminInfoResponsePage @page(for:"AdminInfoResponse")
	"""分页获取课程供应商管理员列表接口<br/>
		课程供应商包含个人账号，且不是账号归属某个单位，而是人员和单位产生关系
	"""
	pageAdminInfoInCourseSupplier(page:Page,request:AdminQueryRequest):AdminInfoResponsePage @page(for:"AdminInfoResponse") @optionalLogin
	"""功能描述：分页获取网校管理员列表接口
		描述：查询当前网校下的管理员信息(查询自己以及自己创建的管理员)，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.AdminInfoResponse>
		@date : 2022/4/2 14:19
	"""
	pageAdminInfoInServicer(page:Page,request:AdminQueryRequest):AdminInfoResponsePage @page(for:"AdminInfoResponse")
	"""功能描述：分页获取子项目下管理员列表接口
		描述：查询当前子项目下的管理员信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.AdminInfoResponse>
		@date : 2022年6月9日 16:50:21
	"""
	pageAdminInfoInSubProject(page:Page,request:AdminQueryRequest):AdminInfoResponsePage @page(for:"AdminInfoResponse")
	"""功能描述：分页获取网校集体缴费管理员列表接口
		描述：查询当前网校下的集体缴费管理员分页信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.collective.CollectiveInfoResponse>
		@date : 2022/4/2 14:20
	"""
	pageCollectiveInfoInServicer(page:Page,request:CollectiveQueryRequest):CollectiveInfoResponsePage @page(for:"CollectiveInfoResponse") @optionalLogin
	"""查询资讯列表(用于顶级分类的业务平台)
		@param queryRequest 简略资讯查询条件  todo
		@return 资讯分页信息
	"""
	pageCompleteNews(queryRequest:NewsCompleteQueryRequest,page:Page):NewsCompleteResponsePage @page(for:"NewsCompleteResponse")
	"""功能描述 : 服务商-查询合同服务商管理员-分页列表接口
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse>
		@date : 2022/7/26 11:51
	"""
	pageContractProviderInfoAdminInfoInServicer(page:Page,request:ContractProviderAdminQueryRequest):ContractProviderAdminInfoResponsePage @page(for:"ContractProviderAdminInfoResponse")
	"""功能描述 : 项目级-查询合同服务商管理员-分页列表接口
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse>
		@date : 2022/7/26 11:51
	"""
	pageContractProviderInfoAdminInfoInSubProject(page:Page,request:ContractProviderAdminQueryRequest):ContractProviderAdminInfoResponsePage @page(for:"ContractProviderAdminInfoResponse") @optionalLogin
	"""功能描述 : 项目级-查询合同服务商-分页列表接口
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.ContractProviderResponse>
		@date : 2022/7/26 9:16
	"""
	pageContractProviderInfoInSubProject(page:Page,request:ContractProviderRequest):ContractProviderResponsePage @page(for:"ContractProviderResponse") @optionalLogin
	"""功能描述 : 服务商-查询课件供应商管理员-分页列表接口
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ServicerAdminInfoResponse>
		@date : 2022/7/26 11:51
	"""
	pageCoursewareSupplierAdminInfoInServicer(page:Page,request:ServicerAdminQueryRequest):ServicerAdminInfoResponsePage @page(for:"ServicerAdminInfoResponse")
	"""功能描述 : 项目级-查询课件供应商管理员-分页列表接口
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ServicerAdminInfoResponse>
		@date : 2022/7/26 11:51
	"""
	pageCoursewareSupplierAdminInfoInSubProject(page:Page,request:ServicerAdminQueryRequest):ServicerAdminInfoResponsePage @page(for:"ServicerAdminInfoResponse") @optionalLogin
	"""课程供应商GRPC分页查询"""
	pageCoursewareSupplierGRPCInSubject(page:Page):CoursewareSupplierGRPCResponsePage @page(for:"CoursewareSupplierGRPCResponse")
	"""功能描述 : 项目级-分页查询课件供应商-分页接口
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.CoursewareSupplierResponse>
		@date : 2022年10月31日 11:44:28
	"""
	pageCoursewareSupplierInfoInSubProject(page:Page,request:CoursewareSupplierRequest):CoursewareSupplierResponsePage @page(for:"CoursewareSupplierResponse") @optionalLogin
	"""分页获取当前网校下的企业单位信息
		<br/>
		<strong>注意：历史原因pageEnterpriseUnitInfoInServicer不能实现当前网校下，要用这个</strong>
	"""
	pageCurrentServicerEnterpriseUnitInfoInServicer(page:Page,request:EnterpriseUnitRequest):EnterpriseUnitInfoResponsePage @page(for:"EnterpriseUnitInfoResponse")
	"""分页获取当前网校下的企业单位信息(分销商)"""
	pageCurrentServicerEnterpriseUnitInfoInServicerInDistribution(page:Page,request:EnterpriseUnitRequest):EnterpriseUnitInfoResponsePage @page(for:"EnterpriseUnitInfoResponse")
	"""功能描述 : 项目级-分页查询分销服务商"""
	pageDistributionServicerInfoInSubProject(page:Page,request:DistributionServerRequest):DistributionServicerResponsePage @page(for:"DistributionServicerResponse") @optionalLogin
	"""功能描述： 政府单位-查询本级及下属企业单位管理员-分页列表接口
		描述：查询政府单位管辖下的企业单位管理员信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:10
	"""
	pageEnterpriseUnitAdminInfoInGovernmentUnit(page:Page,request:EnterpriseUnitAdminQueryRequest):EnterpriseUnitAdminInfoResponsePage @page(for:"EnterpriseUnitAdminInfoResponse") @optionalLogin
	"""功能描述：企业单位-查询当前登录企业单位下属管理员-分页列表接口
		描述：查询当前企业单位下的管理员信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:10
	"""
	pageEnterpriseUnitAdminInfoInMyself(page:Page,request:AdminQueryRequest):EnterpriseUnitAdminInfoResponsePage @page(for:"EnterpriseUnitAdminInfoResponse") @optionalLogin
	"""功能描述：项目级-查询企业单位管理员-分页列表接口
		描述：查询当前子项目下的企业单位管理员信息，默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:21
	"""
	pageEnterpriseUnitAdminInfoInSubProject(page:Page,request:EnterpriseUnitAdminQueryRequest):EnterpriseUnitAdminInfoResponsePage @page(for:"EnterpriseUnitAdminInfoResponse") @optionalLogin
	"""功能描述：政府单位-查询本级及下属企业单位-分页列表接口
		描述：查询当前政府单位下管辖的企业单位信息，默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse>
		@date : 2022年6月9日 10:47:54
	"""
	pageEnterpriseUnitInfoInGovernmentUnit(page:Page,request:EnterpriseUnitRequest):EnterpriseUnitInfoResponsePage @page(for:"EnterpriseUnitInfoResponse")
	"""功能描述：服务商-查询企业单位-分页列表接口
		描述：默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse>
		@date : 2022年7月29日15:59:49
	"""
	pageEnterpriseUnitInfoInServicer(page:Page,request:EnterpriseUnitRequest):EnterpriseUnitInfoResponsePage @page(for:"EnterpriseUnitInfoResponse")
	"""功能描述：项目级-查询企业单位-分页列表接口
		描述：查询当前子项目下的企业单位信息，默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse>
		@date : 2022年6月9日 10:47:54
	"""
	pageEnterpriseUnitInfoInSubProject(page:Page,request:EnterpriseUnitRequest):EnterpriseUnitInfoResponsePage @page(for:"EnterpriseUnitInfoResponse") @optionalLogin
	"""功能描述：政府单位-查询本级及下属政府单位管理员-分页列表接口
		描述：查询当前政府单位下管辖的政府单位管理员信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.GovernmentUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:21
	"""
	pageGovernmentUnitAdminInfoInGovernmentUnit(page:Page,request:GovernmentUnitAdminQueryRequest):GovernmentUnitAdminInfoResponsePage @page(for:"GovernmentUnitAdminInfoResponse") @optionalLogin
	"""功能描述：政府单位-查询当前登录政府单位下属管理员-分页列表接口
		描述：查询当前政府单位下的政府单位管理员信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.GovernmentUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:21
	"""
	pageGovernmentUnitAdminInfoInMyself(page:Page,request:AdminQueryRequest):GovernmentUnitAdminInfoResponsePage @page(for:"GovernmentUnitAdminInfoResponse") @optionalLogin
	"""功能描述：项目级-查询政府单位管理员-分页列表接口
		描述：查询当前子项目下的政府单位管理员信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.GovernmentUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:21
	"""
	pageGovernmentUnitAdminInfoInSubProject(page:Page,request:GovernmentUnitAdminQueryRequest):GovernmentUnitAdminInfoResponsePage @page(for:"GovernmentUnitAdminInfoResponse") @optionalLogin
	"""功能描述：政府单位-查询本级及下属政府单位-分页列表接口
		描述：查询当前政府单位以及下级政府单位信息，默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.GovernmentUnitInfoResponse>
		@date : 2022年6月9日 10:47:54
	"""
	pageGovernmentUnitInfoInGovernmentUnit(page:Page,request:GovernmentUnitRequest):GovernmentUnitInfoResponsePage @page(for:"GovernmentUnitInfoResponse") @optionalLogin
	"""功能描述：项目级-查询政府单位-分页列表接口
		描述：查询当前子项目下的政府单位信息，默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.GovernmentUnitInfoResponse>
		@date : 2022年6月9日 10:47:54
	"""
	pageGovernmentUnitInfoInSubProject(page:Page,request:GovernmentUnitRequest):GovernmentUnitInfoResponsePage @page(for:"GovernmentUnitInfoResponse") @optionalLogin
	"""分页查询指定行业属性编号下指定行业属性分类下的培训属性列表"""
	pageIndustryPropertyByCategoryInSubProject(page:Page,request:PageIndustryPropertyByCategoryRequest):TrainingPropertyResponsePage @page(for:"TrainingPropertyResponse") @optionalLogin
	"""分页查询行业属性 - 平台级"""
	pageIndustryPropertyInSubProject(page:Page,request:IndustryPropertyRequest):IndustryPropertyResponsePage @page(for:"IndustryPropertyResponse") @optionalLogin
	"""功能描述 : 服务商-查询网校服务商管理员-分页列表接口  (分销能力专属)"""
	pageOnlineSchoolAdminInfoInFxpt(page:Page,request:ContractProviderAdminQueryRequest):ContractProviderAdminInfoResponsePage @page(for:"ContractProviderAdminInfoResponse")
	"""功能描述 : 服务商-查询网校服务商管理员-分页列表接口"""
	pageOnlineSchoolAdminInfoInServicer(page:Page,request:ContractProviderAdminQueryRequest):ContractProviderAdminInfoResponsePage @page(for:"ContractProviderAdminInfoResponse")
	"""功能描述 : 项目级-查询网校服务商管理员-分页列表接口"""
	pageOnlineSchoolAdminInfoInSubProject(page:Page,request:ContractProviderAdminQueryRequest):ContractProviderAdminInfoResponsePage @page(for:"ContractProviderAdminInfoResponse") @optionalLogin
	"""功能描述 : 查询网校信息-分页列表接口"""
	pageOnlineSchoolInfoResponseInSubProject(page:Page,request:OnlineSchoolInfoRequest):OnlineSchoolInfoResponsePage @page(for:"OnlineSchoolInfoResponse")
	"""功能描述：项目级-分页查询门户信息-分页接口
		@param page                    : 分页对象
		@param request                 : 查询对象
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.PortalResponse>
		@Author： wtl
		@Date： 2022/10/19 10:40
	"""
	pagePortalInfoInSubProject(page:Page,request:PortalRequest):PortalResponsePage @page(for:"PortalResponse") @optionalLogin
	"""获取当前网校下的管理员"""
	pageServicerUnitAdminInfoInServicer(page:Page,request:EnterpriseUnitAdminQueryRequest):EnterpriseUnitAdminInfoResponsePage @page(for:"EnterpriseUnitAdminInfoResponse")
	"""功能描述：项目级-查询服务商企业单位管理员-分页列表接口
		描述：查询当前子项目下的企业单位管理员信息，默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:21
	"""
	pageServicerUnitAdminInfoInSubProject(page:Page,request:EnterpriseUnitAdminQueryRequest):EnterpriseUnitAdminInfoResponsePage @page(for:"EnterpriseUnitAdminInfoResponse") @optionalLogin
	"""查询简略资讯列表
		@param queryRequest 简略资讯查询条件
		@return 资讯分页信息
	"""
	pageSimpleNews(queryRequest:NewsSimpleQueryRequest,page:Page):NewsSimpleResponsePage @page(for:"NewsSimpleResponse") @optionalLogin
	"""功能描述：分页获取网校学员列表接口
		描述：查询当前网校下的学员分页信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 : 查询参数
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse>
		@date : 2022/4/1 17:17
	"""
	pageStudentInfoInServicer(page:Page,request:StudentQueryRequest):StudentInfoResponsePage @page(for:"StudentInfoResponse")
	"""功能描述：子项目-查询学员信息列表-分页接口
		描述：查询子项目下的学员分页信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 : 查询参数
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse>
		@date : 2022年11月7日 09:37:49
	"""
	pageStudentInfoInSubProject(page:Page,request:StudentQueryRequest):StudentInfoResponsePage @page(for:"StudentInfoResponse")
	"""还没有实现，待确认实现方式
		功能描述：分页获取网校学员列表接口（供应商）
		描述：查询当前网校下的学员分页信息，默认按创建时间降序排，如不存在返回null
	"""
	pageStudentInfoInSupplier(page:Page,request:StudentQueryRequest):StudentInfoResponsePage @page(for:"StudentInfoResponse")
	pageSubjectType(industryPropertyIds:[String],page:Page):SubjectTypePageResponsePage @page(for:"SubjectTypePageResponse")
	"""功能描述 : 项目级-分页查询供应服务商"""
	pageSupplierServicerInfoInSubProject(page:Page,request:SupplierServerRequest):SupplierServicerResponsePage @page(for:"SupplierServicerResponse") @optionalLogin
	"""根据条件查询培训类别信息
		@param queryRequest
		@param page 分页参数
		@return 培训类别信息
		@throws InvalidProtocolBufferException
	"""
	pageTrainingCategory(queryRequest:TrainingCategoryQueryRequest,page:Page):TrainingCategoryPageResponsePage @page(for:"TrainingCategoryPageResponse")
	"""查询专题简略资讯列表
		没传专题id也是默认查专题资讯，其他的查询口没传专题id查非专题资讯的
	"""
	pageTrainingChannelSimpleNews(queryRequest:TrainingChannelNewsSimpleQueryRequest,page:Page):NewsSimpleResponsePage @page(for:"NewsSimpleResponse") @optionalLogin
	"""分页查询行业属性下未被添加的培训属性"""
	pageUnAddIndustryProperty(page:Page,request:TrainingPropertyCommonRequest):TrainingPropertyResponsePage @page(for:"TrainingPropertyResponse")
	"""分页获取当前网校下用户账号信息列表接口"""
	pageUserAccountInfoInServicer(page:Page,request:AccountUserInfoQueryRequest):UserAccountInfoResponsePage @page(for:"UserAccountInfoResponse")
	"""政府单位下-统计各时间段内企业单位总数量
		@param request
		@return
	"""
	statisticEnterpriseUnitGroupByTimeInGovernmentUnit(request:EnterpriseUnitDateHistogramRequest):DateHistogramResponse
	"""政府单位下-统计各行业类型下企业单位总数量"""
	statisticEnterpriseUnitIndustryInGovernmentUnit(request:EnterpriseUnitStatisticRequest):[EnterpriseUnitIndustryStatisticResponse]
	"""政府单位下-统计各地区下企业单位总数量"""
	statisticEnterpriseUnitRegionInGovernmentUnit(request:EnterpriseUnitStatisticRequest):[EnterpriseUnitRegionStatisticResponse]
	"""政府单位下-统计各单位类型下企业单位总数量"""
	statisticEnterpriseUnitTypeInGovernmentUnit(request:EnterpriseUnitStatisticRequest):[EnterpriseUnitTypeStatisticResponse]
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""功能描述：账户信息查询条件
	@Author： wtl
	@Date： 2022年5月11日 15:30:56
"""
input AccountRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.AccountRequest") {
	"""账户状态 1：正常，2：冻结
		@see AccountStatus
	"""
	statusList:[Int]
	"""创建时间范围"""
	createTimeScope:DateScopeRequest
	"""创建人用户id"""
	createdUserId:String
	"""账户类型 1：企业帐户，2：企业个人帐户，3：个人帐户"""
	accountTypeList:[Int]
	"""单位id （原始单位id，不会随着id 人员与单位的关系变化而变化）"""
	unitIdList:[String]
	"""单位id匹配方式 默认-1、and匹配 2、or匹配
		@see MatchTypeConstant
	"""
	unitIdMatchType:Int
	"""来源类型
		0-内置 1-项目主网站 2-安卓 3-IOS 4-后台导入 5-迁移数据
	"""
	sourceTypes:[Int]
}
"""功能描述：账户用户信息查询条件
	@Author： linq
	@Date： 2024年05月09日 15:05
"""
input AccountUserInfoQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.AccountUserInfoQueryRequest") {
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:StudentUserRequest
	"""用户认证信息"""
	authentication:AuthenticationRequest
	"""集体缴费信息"""
	collective:CollectiveRequest
	"""人员信息"""
	person:PersonRequest
	"""排序"""
	sortList:[StudentSortRequest]
}
"""功能描述：登录认证查询条件
	@Author： wtl
	@Date： 2022年1月26日 09:30:12
"""
input AuthenticationRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.AuthenticationRequest") {
	"""帐号"""
	identity:String
	"""用户名"""
	userName:String
}
"""功能描述 : 直方图统计查询条件"""
input DateHistogramRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.DateHistogramRequest") {
	"""开始时间（必填）"""
	startTime:DateTime
	"""结束时间（必填）"""
	endTime:DateTime
	"""时间单位枚举（必填）"""
	timeUnit:TimeUnitEnum
}
"""功能描述：人员查询条件"""
input PersonRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.PersonRequest") {
	"""业务类型，
		@see PersonUnitRelationshipBusinessTypes
	"""
	businessType:Int
	"""关系类型（标识人与哪个实体产生的关系）unit或department
		@see PersonRelationTypeConstant
	"""
	relationType:String
	"""关系值（业务类型为组织关系时，目前该值为单位id或部门id；业务类型为主要经办或经办，该值为单位id）"""
	relationValue:String
	"""关系状态，1正常，0冻结
		@see PersonUnitRelationshipStatus
	"""
	status:Int
}
"""功能描述：角色查询条件
	@Author： wtl
	@Date： 2022年5月11日 11:46:41
"""
input RoleRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.RoleRequest") {
	"""角色id集合"""
	roleIdList:[String]
	"""角色类型
		（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
		@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
	"""
	roleTypeList:[String]
	"""角色类别
		（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管 10:人社超管
		11:人社职建处 12:人社就业局 13:超级管理员 14:合同供应商 15:专家 16:电子劳动合同业务角色 320:地区管理员 410:人社行业管理员
		411:人社行业省级管理员 412:人社行业市级管理员 413:人社行业区县级管理员 420:人社地区管理员 430:人社业务管理员 440:省级人社主管 450:市级人社主管
		460:区县级人社主管 510:培训机构管理员 520:技工院校管理员 530:职业院校管理员 540:政策参与者 550:线上培训机构管理员 560:课件供应商
		5001:学徒制_企业管理员 5101:学徒制_培训机构管理员 5201:学徒制_技工院校管理员 5301:学徒制_职业院校管理员 4011:学徒制_人社_省级管理员
		4021:学徒制_人社_市级管理员 4031:学徒制_人社_区/县级管理员 6001:揭榜挂帅_企业管理员 6101:揭榜挂帅_培训机构管理员
		6201:揭榜挂帅_技工院校管理员 6301:揭榜挂帅_职业院校管理员 6401:揭榜挂帅_人社_省级管理员 6402:揭榜挂帅_人社_市级管理员 6403:揭榜挂帅_人社_区/县级管理员）
		@see RoleCategories
	"""
	roleCategoryList:[Int]
	"""排除的角色类别集合
		（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管 10:人社超管
		11:人社职建处 12:人社就业局 13:超级管理员 14:合同供应商 15:专家 16:电子劳动合同业务角色 320:地区管理员 410:人社行业管理员
		411:人社行业省级管理员 412:人社行业市级管理员 413:人社行业区县级管理员 420:人社地区管理员 430:人社业务管理员 440:省级人社主管 450:市级人社主管
		460:区县级人社主管 510:培训机构管理员 520:技工院校管理员 530:职业院校管理员 540:政策参与者 550:线上培训机构管理员 560:课件供应商
		5001:学徒制_企业管理员 5101:学徒制_培训机构管理员 5201:学徒制_技工院校管理员 5301:学徒制_职业院校管理员 4011:学徒制_人社_省级管理员
		4021:学徒制_人社_市级管理员 4031:学徒制_人社_区/县级管理员 6001:揭榜挂帅_企业管理员 6101:揭榜挂帅_培训机构管理员
		6201:揭榜挂帅_技工院校管理员 6301:揭榜挂帅_职业院校管理员 6401:揭榜挂帅_人社_省级管理员 6402:揭榜挂帅_人社_市级管理员 6403:揭榜挂帅_人社_区/县级管理员）
		@see RoleCategories
	"""
	excludeRoleCategoryList:[Int]
	"""角色应用方Id集合"""
	applicationMemberIdList:[String]
	"""授予性质 | 1.系统授予 2.用户授予"""
	natureList:[Int]
	"""排除的角色Code | JGGLY-机构管理员"""
	excludeRoleCodeList:[String]
}
"""功能描述：用户基本查询条件
	@Author： wtl
	@Date： 2022年1月26日 09:30:12
"""
input UserRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.UserRequest") {
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	phoneMatchType:Int
	"""工作单位名称"""
	companyName:String
	"""工作单位统一社会信用代码"""
	companyCode:String
}
"""功能描述：管理员查询条件
	@Author： wtl
	@Date： 2022年1月25日 15:24:10
"""
input AdminQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.admin.AdminQueryRequest") {
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:AdminUserRequest
	"""登录认证信息"""
	authentication:AuthenticationRequest
	"""角色信息查询"""
	role:RoleRequest
	"""排序"""
	sortList:[AdminSortRequest]
}
"""功能描述：管理员排序
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
input AdminSortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.admin.AdminSortRequest") {
	"""管理员排序字段"""
	sortField:EnterprisePersonSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
"""功能描述：管理员查询条件
	@Author： wtl
	@Date： 2022年1月25日 15:24:10
"""
input AdminUserRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.admin.AdminUserRequest") {
	"""默认为右模糊(0：完全匹配 1：模糊查询，*manageRegionPath* 2：左模糊查询，*manageRegionPath 3:右模糊查询，manageRegionPath*)
		管辖地区路径匹配方式
	"""
	manageRegionPathType:Int
	"""管理地区路径集合"""
	manageRegionPathList:[String]
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	phoneMatchType:Int
	"""工作单位名称"""
	companyName:String
	"""工作单位统一社会信用代码"""
	companyCode:String
}
"""功能描述：集体缴费管理员排序
	@Author： wtl
	@Date： 2022年1月26日 16:19:01
"""
input CollectiveSortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.admin.CollectiveSortRequest") {
	"""集体缴费管理员排序字段"""
	sortField:EnterprisePersonSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
"""功能描述 : 合同服务商管理员查询条件"""
input ContractProviderAdminQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.admin.ContractProviderAdminQueryRequest") {
	"""合同服务商管理员归属信息"""
	owner:ContractProviderInfoAdminOwnerRequest
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:AdminUserRequest
	"""登录认证信息"""
	authentication:AuthenticationRequest
	"""角色信息查询"""
	role:RoleRequest
	"""排序"""
	sortList:[AdminSortRequest]
}
"""功能描述 : 企业单位管理员查询条件
	@date : 2022/6/17 17:32
"""
input EnterpriseUnitAdminQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.admin.EnterpriseUnitAdminQueryRequest") {
	"""企业单位管理员归属信息"""
	owner:EnterpriseUnitAdminOwnerRequest
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:AdminUserRequest
	"""登录认证信息"""
	authentication:AuthenticationRequest
	"""角色信息查询"""
	role:RoleRequest
	"""排序"""
	sortList:[AdminSortRequest]
}
"""功能描述 : 政府单位管理员查询条件
	@date : 2022/6/17 17:32
"""
input GovernmentUnitAdminQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.admin.GovernmentUnitAdminQueryRequest") {
	"""政府单位管理员归属信息"""
	owner:GovernmentUnitAdminOwnerRequest
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:AdminUserRequest
	"""登录认证信息"""
	authentication:AuthenticationRequest
	"""角色信息查询"""
	role:RoleRequest
	"""排序"""
	sortList:[AdminSortRequest]
}
"""功能描述 : 服务商管理员查询条件"""
input ServicerAdminQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.admin.ServicerAdminQueryRequest") {
	"""服务商管理员归属信息"""
	businessOwnerInfo:ServicerAdminBusinessOwnerRequest
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:AdminUserRequest
	"""登录认证信息"""
	authentication:AuthenticationRequest
	"""角色信息查询"""
	role:RoleRequest
	"""排序"""
	sortList:[AdminSortRequest]
}
"""功能描述 : 合同服务商管理员归属查询条件
	@date : 2022年7月29日16:02:39
"""
input ContractProviderInfoAdminOwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.admin.nested.ContractProviderInfoAdminOwnerRequest") {
	"""合同服务商id路径集合
		说明：核心层需要将服务商ID需要转换为角色应用方ID查询
	"""
	servicerIdList:[String]
}
"""功能描述 : 企业单位管理员归属查询条件
	@date : 2022/6/17 17:42
"""
input EnterpriseUnitAdminOwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.admin.nested.EnterpriseUnitAdminOwnerRequest") {
	"""企业单位id路径集合
		String："/福建电信id/福州电信分公司id"
	"""
	enterpriseUnitIdPathList:[String]
	"""企业单位id路径匹配方式，默认为右模糊查询(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	enterpriseUnitIdPathMatchType:Int
}
"""功能描述 : 政府单位管理员归属查询条件
	@date : 2022/6/17 17:42
"""
input GovernmentUnitAdminOwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.admin.nested.GovernmentUnitAdminOwnerRequest") {
	"""政府单位id路径集合
		String："/福建省人社id/福州市人社id"
	"""
	governmentUnitIdPathList:[String]
	"""政府单位id路径匹配方式，默认为右模糊查询(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	governmentUnitIdPathMatchType:Int
}
"""功能描述 : 服务商管理员业务归属查询条件
	@date : 2022年10月31日 15:52:19
"""
input ServicerAdminBusinessOwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.admin.nested.ServicerAdminBusinessOwnerRequest") {
	"""服务商id集合"""
	servicerIdList:[String]
}
"""功能描述：集体缴费管理员查询条件
	@Author： wtl
	@Date： 2022年1月26日 10:10:33
"""
input CollectiveQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.collective.CollectiveQueryRequest") {
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:UserRequest
	"""登录认证信息"""
	authentication:AuthenticationRequest
	"""集体缴费管理员排序集合"""
	sortList:[CollectiveSortRequest]
}
"""业务区域树根结点查询"""
input BusinessRegionTreeQueryChildrenRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.BusinessRegionTreeQueryChildrenRequest") {
	"""业务id，代表使用哪一棵业务地区树"""
	businessId:String
	"""父节点Id -1时为根结点"""
	parentId:String
}
"""业务区域树根结点查询"""
input BusinessRegionTreeQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.BusinessRegionTreeQueryRequest") {
	"""业务id，代表使用哪一棵业务地区树"""
	businessId:String
	"""业务区域编码"""
	code:String
}
"""业务区域树根结点查询"""
input BusinessRegionTreeQueryRootRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.BusinessRegionTreeQueryRootRequest") {
	"""业务id，代表使用哪一棵业务地区树"""
	businessId:String
}
"""查询行业属性信息"""
input IndustryPropertyInfoQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.IndustryPropertyInfoQueryRequest") {
	"""服务商(网校id)"""
	servicerId:String
	"""行业id"""
	industryIdList:[String]
	"""行业属性类别 0-业务行业属性，1-人员行业属性"""
	propertyType:Int
}
input IndustryPropertyRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.IndustryPropertyRequest") {
	"""行业属性名称"""
	industryPropertyName:String
	"""行业id"""
	industryIdList:[String]
	"""行业属性类别 0-业务行业属性，1-人员行业属性"""
	propertyType:Int
}
input PageIndustryPropertyByCategoryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.PageIndustryPropertyByCategoryRequest") {
	"""行业属性编号"""
	industryPropertyId:String
	"""行业ID"""
	industryId:String
	"""行业属性分类代码
		TRAINING_CATEGORY
		PERSON_TRAINING_CATEGORY
		TRAINING_PROFESSIONAL
		PERSON_TRAINING_PROFESSIONAL
	"""
	categoryCode:String
	"""字典名称，模糊查询"""
	name:String
	"""是否可用，0停用1可用，不传返回全部"""
	available:Int
}
"""网校培训属性查询条件
	<AUTHOR>
	@since 2022/1/17
"""
input SchoolTrainingPropertyQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.SchoolTrainingPropertyQueryRequest") {
	"""网校编号"""
	schoolId:String
	"""行业编号"""
	industryId:String
	"""培训属性编号列表，最大支持200个"""
	propertyId:[String]
}
"""<AUTHOR> linq
	@date : 2023-08-22 11:33
	@description：查询服务地区 请求
"""
input ServiceOrIndustryRegionQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.ServiceOrIndustryRegionQueryRequest") {
	"""服务类型 0或1
		0 - 平台业务地区(PLATFORM_BUSINESS_REGION)
		1 - 培训方案地区(TRAINING_SCHEME_REGION)
	"""
	type:Int
	"""需要返回的地区级别
		0 - 全部 (自身及所有下级地区)
		1 - 仅省级
		2 - 仅市级
		3 - 仅区县级
	"""
	whichLevel:Int
	"""地区父节点code"""
	parentRegionCode:String
}
input ServicerContractPropertyByCategoryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.ServicerContractPropertyByCategoryRequest") {
	"""字典分类"""
	categoryCode:String
	"""可用状态，不传的情况下默认可用，0停用 1可用"""
	availableList:[Int]
}
input TrainingCategoryQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.TrainingCategoryQueryRequest") {
	"""层级"""
	level:Int!
	"""父类编号"""
	parentId:String
	"""行业属性编号列表"""
	industryPropertyIds:[String]
}
"""培训属性通用查询参数"""
input TrainingPropertyCommonRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.TrainingPropertyCommonRequest") {
	"""行业属性编号"""
	industryPropertyId:String
	"""行业ID"""
	industryId:String
	"""行业属性分类代码
		TRAINING_CATEGORY
		PERSON_TRAINING_CATEGORY
		TRAINING_PROFESSIONAL
		PERSON_TRAINING_PROFESSIONAL
	"""
	categoryCode:String
	"""字典名称，模糊查询"""
	name:String
	"""是否可用，0停用1可用，不传默认可用。传-1返回全部"""
	available:Int
}
"""培训属性查询条件
	<AUTHOR>
	@since 2022/1/17
"""
input TrainingPropertyQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.TrainingPropertyQueryRequest") {
	"""行业属性编号"""
	industryPropertyId:String
	"""行业属性分类"""
	categoryCode:String
	"""培训属性编号"""
	propertyId:String
	"""培训属性编号集合
		目前只在以下接口使用，优先使用 propertyId 字段，即 propertyId 有值时，propertyIdList 入参无效
		com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.DictionaryCommonQueryResolver#listIndustryPropertyChildByCategoryV2
	"""
	propertyIdList:[String]
}
"""简略资讯查询条件"""
input NewsCompleteQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.NewsCompleteQueryRequest") {
	"""资讯标题"""
	title:String
	"""资讯分类编号"""
	necId:String
	"""顶级分类代码"""
	rootCategoryCode:String
	"""弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]"""
	popUpsStatus:Int!
	"""资讯状态 [-1-全部，0-草稿，1-发布 默认全部]"""
	status:Int!
}
"""简略资讯查询条件"""
input NewsSimpleQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.NewsSimpleQueryRequest") {
	"""资讯标题"""
	title:String
	"""资讯分类编号"""
	necId:String
	"""发布地区编码"""
	areaCodePath:String
	"""弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]"""
	popUpsStatus:Int!
	"""资讯状态 [-1-全部，0-草稿，1-发布 默认全部]"""
	status:Int!
}
"""简略资讯查询条件"""
input TrainingChannelNewsSimpleQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.TrainingChannelNewsSimpleQueryRequest") {
	"""资讯标题"""
	title:String
	"""资讯分类编号"""
	necId:String
	"""发布地区编码"""
	areaCodePath:String
	"""弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]"""
	popUpsStatus:Int!
	"""资讯状态 [-1-全部，0-草稿，1-发布 默认全部]"""
	status:Int!
	"""专题ID"""
	specialSubjectIds:[String]
}
"""<AUTHOR> linq
	@date : 2023-07-14 10:50
	@description：网校信息查询参数
"""
input OnlineSchoolInfoRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.onlineschool.OnlineSchoolInfoRequest") {
	"""查询参数"""
	onlineSchoolInfoQuery:OnlineSchoolInfoQueryRequest
	"""排序参数"""
	onlineSchoolInfoSort:OnlineSchoolInfoSortRequest
}
"""<AUTHOR> linq
	@date : 2023-07-18 10:50
	@description：网校信息查询参数
"""
input OnlineSchoolInfoQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.onlineschool.nested.OnlineSchoolInfoQueryRequest") {
	"""网校ID集合"""
	onlineSchoolIdList:[String]
	"""网校平台名称"""
	name:String
	"""服务商名称"""
	servicerName:String
	"""服务地区"""
	serviceAreas:[String]
	"""培训行业id"""
	industryIds:[String]
	"""补贴培训：培训类别"""
	trainingCategoryIds:[String]
	"""业主单位"""
	unitName:String
	"""统一社会信用代码"""
	code:String
	"""网校模式
		1-正式实施 2-DEMO
		@see OnlineSchoolModes
	"""
	onlineSchoolModes:Int
	"""线下签署合约日期 开始时间"""
	offlineContractSignedDateBegin:DateTime
	"""线下签署合约日期 结束时间"""
	offlineContractSignedDateEnd:DateTime
	"""服务商创建时间 开始时间"""
	servicerCreatedTimeBegin:DateTime
	"""服务商创建时间 结束时间"""
	servicerCreatedTimeEnd:DateTime
	"""合约状态
		0-草案 1-交付中 2-履约中 3-终止
		@see OnlineSchoolContractStatusConstant
	"""
	status:Int
	"""是否可用"""
	isEnable:Boolean
	"""是否到期"""
	isExpired:Boolean
	"""是否续期"""
	isRenewed:Boolean
	"""到期时间 开始时间"""
	expireDateBegin:DateTime
	"""到期时间 结束时间"""
	expireDateEnd:DateTime
	"""客户端类型
		1-web访问 2-h5访问
		@see ClientTypesConstant
	"""
	clientType:[Int]
	"""培训周期模式
		1-长期 2-短期
		@see TrainingPeriodModesConstant
	"""
	trainingPeriodModes:Int
	"""网校开通时间 - 开始时间"""
	openTimeBegin:DateTime
	"""网校开通时间 - 结束时间"""
	openTimeEnd:DateTime
}
"""<AUTHOR> linq
	@date : 2023-07-18 10:51
	@description：网校信息排序参数
"""
input OnlineSchoolInfoSortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.onlineschool.nested.OnlineSchoolInfoSortRequest") {
	"""网校开通时间
		1-升序 2-降序
	"""
	openTimeSort:Int
}
"""功能描述 : 门户持核心层查询条件
	@date : 2022年10月18日 19:53:03
"""
input PortalRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.portal.PortalRequest") {
	"""归属信息"""
	owner:PortalOwnerRequest
	"""门户信息"""
	portalInfo:PortalInfoRequest
}
"""功能描述：门户信息查询条件"""
input PortalInfoRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.portal.nested.PortalInfoRequest") {
	"""门户id集合"""
	portalId:[String]
	"""门户类型（1：web端 2：移动端）
		@see com.fjhb.domain.basicdata.api.servicer.consts.PortalTypes
	"""
	portalType:Int
	"""门户状态（0：未发布 1：已发布）"""
	status:Int
}
"""功能描述：门户归属信息查询参数
	@Author： wtl
	@Date： 2022年10月18日 19:53:52
"""
input PortalOwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.portal.nested.PortalOwnerRequest") {
	"""所属培训机构id集合"""
	trainingInstitutionIdList:[String]
}
"""功能描述：合同供应商查询条件"""
input ContractProviderRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.servicer.ContractProviderRequest") {
	"""服务商基本信息"""
	contractProviderBase:ContractProviderBaseRequest
	"""排序集合"""
	sortList:[ServicerSortKParam]
}
"""功能描述：课件供应商查询条件"""
input CoursewareSupplierRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.servicer.CoursewareSupplierRequest") {
	"""服务商基本信息"""
	servicerBase:ServicerBaseRequest
	"""排序集合"""
	sortList:[ServicerSortKParam]
}
"""功能描述：分销商查询条件"""
input DistributionServerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.servicer.DistributionServerRequest") {
	"""服务商基本信息"""
	servicerBase:ServicerBaseRequest
	"""排序集合"""
	sortList:[ServicerSortKParam]
}
"""功能描述：供应商查询条件"""
input SupplierServerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.servicer.SupplierServerRequest") {
	"""服务商基本信息"""
	servicerBase:ServicerBaseRequest
	"""排序集合"""
	sortList:[ServicerSortKParam]
}
"""功能描述：服务商基本查询条件"""
input ContractProviderBaseRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.servicer.nested.ContractProviderBaseRequest") {
	"""服务商id"""
	servicerId:String
	"""服务商名称"""
	servicerName:String
	"""服务商名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	servicerNameMatchType:Int
	"""服务商所属地区路径集合"""
	regionPathList:[String]
	"""服务商所属地区路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	regionPathListMatchType:Int
	"""创建时间范围"""
	servicerCreatedTimeScope:DateScopeRequest
}
"""功能描述：服务商基本查询条件"""
input ServicerBaseRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.servicer.nested.ServicerBaseRequest") {
	"""服务商id"""
	servicerIdList:[String]
	"""服务商名称"""
	servicerName:String
	"""服务商简称"""
	servicerShortName:String
	"""服务商名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	servicerNameMatchType:Int
	"""服务商名称与简称匹配关系
		@see ParamRelationConstant
	"""
	servicerNameRelationType:Int
	"""服务商所属地区路径集合"""
	regionPathList:[String]
	"""服务商所属地区路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	regionPathListMatchType:Int
	"""创建时间范围"""
	servicerCreatedTimeScope:DateScopeRequest
}
"""功能描述：学员集体缴费信息
	@Author： wtl
	@Date： 2022年4月21日 08:58:49
"""
input CollectiveRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.student.CollectiveRequest") {
	"""集体缴费管理员用户id集合"""
	collectiveUserIdList:[String]
}
"""功能描述：学员查询条件
	@Author： wtl
	@Date： 2022年1月26日 10:10:33
"""
input StudentQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.student.StudentQueryRequest") {
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:StudentUserRequest
	"""用户认证信息"""
	authentication:AuthenticationRequest
	"""集体缴费信息"""
	collective:CollectiveRequest
	"""排序"""
	sortList:[StudentSortRequest]
}
"""功能描述 : 学员注册数量统计查询条件"""
input StudentRegisteredStatisticsRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.student.StudentRegisteredStatisticsRequest") {
	"""所属服务商ID集合"""
	servicerIdList:[String]
	"""行业id"""
	industryId:String
	"""类别id"""
	categoryId:String
	"""专业id"""
	professionalId:String
}
"""功能描述 : 学员排序参数
	@date : 2022/4/1 17:15
"""
input StudentSortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.student.StudentSortRequest") {
	"""学员排序字段"""
	sortField:PersonAccountSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
"""功能描述：学员查询条件
	@Author： wtl
	@Date： 2022年1月26日 10:10:33
"""
input StudentUserRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.student.StudentUserRequest") {
	"""用户所属地区路径集合（模糊，右like）"""
	regionPathList:[String]
	"""用户所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	regionPathListMatchType:Int
	"""单位所属地区路径集合（模糊，右like）"""
	companyRegionPathList:[String]
	"""单位所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	companyRegionPathListMatchType:Int
	"""是否工勤人员  (0:非工勤人员  1:工勤人员)"""
	isWorker:String
	"""是否退休   (0:非退休人员 1:退休人员)"""
	isRetire:String
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	phoneMatchType:Int
	"""工作单位名称"""
	companyName:String
	"""工作单位统一社会信用代码"""
	companyCode:String
}
"""时间纬度下单位数量统计查询条件
	@date 2022-07-27
"""
input EnterpriseUnitDateHistogramRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.unit.EnterpriseUnitDateHistogramRequest") {
	"""时间纬度查询条件"""
	dateHistogram:DateHistogramRequest
	"""企业单位基本信息"""
	unitBase:EnterpriseUnitBaseRequest
}
"""功能描述：企业单位查询条件
	@Author： wtl
	@Date： 2022年6月9日 11:56:47
"""
input EnterpriseUnitRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.unit.EnterpriseUnitRequest") {
	"""企业单位基本信息"""
	unitBase:EnterpriseUnitBaseRequest
	"""排序集合"""
	sortList:[EnterpriseUnitSortRequest]
}
"""企业单位统计查询条件
	<AUTHOR>
	@date 2022-07-23
"""
input EnterpriseUnitStatisticRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.unit.EnterpriseUnitStatisticRequest") {
	"""企业单位基本信息"""
	unitBase:EnterpriseUnitBaseRequest
}
"""功能描述：政府单位查询条件
	@Author： wtl
	@Date： 2022年6月9日 10:24:09
"""
input GovernmentUnitRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.unit.GovernmentUnitRequest") {
	"""基本归属信息"""
	owner:OwnerRequest
	"""单位业务归属信息"""
	businessOwner:GovernmentUnitOwnerRequest
	"""单位基本信息"""
	unitBase:GovernmentUnitBaseRequest
	"""排序集合"""
	sortList:[GovernmentUnitSortRequest]
}
"""功能描述：企业单位基本查询条件
	@Author： wtl
	@Date： 2022年6月9日 10:24:09
"""
input EnterpriseUnitBaseRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.unit.nested.EnterpriseUnitBaseRequest") {
	"""单位id集合"""
	unitIdList:[String]
	"""单位名称"""
	unitName:String
	"""单位名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	unitNameMatchType:Int
	"""统一社会信用代码"""
	code:String
	"""单位业务类型
		说明：
		1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
		11地区管理单位,12行业主管单位,13技工院校,14职业院校.15线上培训机构,10000实名制报表补贴单位,10001评价机构
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
		@see UnitBusinessQueryTypes
	"""
	businessTypeList:[Int]
	"""单位类型id路径集合"""
	unitTypeIdPathList:[String]
	"""单位类型id路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*unitTypeIdPathList* 2：左模糊查询，*unitTypeIdPathList 3:右模糊查询，unitTypeIdPathList*)
		@see MatchTypeConstant
	"""
	unitTypeIdPathListMatchType:Int
	"""单位地区路径集合"""
	regionPathList:[String]
	"""单位地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	regionPathListMatchType:Int
	"""单位创建时间范围"""
	createdDateScope:DateScopeRequest
	"""企业法人"""
	legalPerson:LegalPersonRequest
	"""单位状态
		说明：1正常,2冻结
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitStatus
	"""
	statusList:[Int]
}
"""企业单位排序
	<AUTHOR>
	@date 2022-06-18
"""
input EnterpriseUnitSortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.unit.nested.EnterpriseUnitSortRequest") {
	"""排序字段"""
	sortField:EnterpriseUnitSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
"""功能描述：政府单位基本查询条件
	@Author： wtl
	@Date： 2022年6月9日 10:24:09
"""
input GovernmentUnitBaseRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.unit.nested.GovernmentUnitBaseRequest") {
	"""单位id集合"""
	unitIdList:[String]
	"""单位名称"""
	unitName:String
	"""单位名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	unitNameMatchType:Int
	"""管辖地区路径"""
	manageRegionPathList:[String]
	"""管辖地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	manageRegionPathListMatchType:Int
}
"""功能描述：政府单位归属查询条件
	@Author： wtl
	@Date： 2022年6月9日 12:08:14
"""
input GovernmentUnitOwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.unit.nested.GovernmentUnitOwnerRequest") {
	"""单位id路径集合"""
	unitIdPathList:[String]
	"""单位id路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	unitIdPathMatchType:Int
}
"""政府单位排序
	<AUTHOR>
	@date 2022-06-18
"""
input GovernmentUnitSortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.unit.nested.GovernmentUnitSortRequest") {
	"""排序字段"""
	sortField:GovernmentUnitSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
"""功能描述：企业法人查询条件
	@Author： wtl
	@Date： 2022年6月9日 12:08:14
"""
input LegalPersonRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.unit.nested.LegalPersonRequest") {
	"""法人姓名"""
	name:String
	"""法人姓名匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	nameMatchType:Int
	"""证件号"""
	idCard:String
	"""证件号匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	idCardMatchType:Int
}
"""基本归属信息"""
input OwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.unit.nested.OwnerRequest") {
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
}
"""功能描述：服务商排序"""
input ServicerSortKParam @type(value:"com.fjhb.ms.basicdata.query.kernel.service.param.servicer.nested.ServicerSortKParam") {
	"""排序字段"""
	sortField:ServicerSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
input DateScopeRequest @type(value:"com.fjhb.ms.basicdata.repository.DateScopeRequest") {
	beginTime:DateTime
	endTime:DateTime
}
enum SortTypeEnum @type(value:"com.fjhb.ms.basicdata.enums.SortTypeEnum") {
	ASC
	DESC
}
enum TimeUnitEnum @type(value:"com.fjhb.ms.basicdata.enums.TimeUnitEnum") {
	YEARS
	MONTHS
	DAYS
	HOURS
}
type DateHistogramItemModel @type(value:"com.fjhb.ms.basicdata.model.DateHistogramItemModel") {
	date:DateTime
	count:Long!
}
type RegionModel @type(value:"com.fjhb.ms.basicdata.model.RegionModel") {
	regionId:String
	regionPath:String
	provinceId:String
	provinceName:String
	cityId:String
	cityName:String
	countyId:String
	countyName:String
}
type SectionAndSubjects @type(value:"com.fjhb.ms.basicdata.model.account.nested.SectionAndSubjects") {
	section:Int
	subjects:Int
}
type MessageTemplate @type(value:"com.fjhb.ms.basicdata.model.servicer.nested.MessageTemplate") {
	templateName:String
	content:String
	category:String
}
"""功能描述：账户信息
	@Author： wtl
	@Date： 2022年5月11日 15:30:56
"""
type AccountResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.AccountResponse") {
	"""账户id"""
	accountId:String
	"""帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
		@see AccountTypes
	"""
	accountType:Int
	"""单位信息"""
	unitInfo:UnitInfoResponse
	"""所属顶级企业帐户Id"""
	rootAccountId:String
	"""帐户状态 1：正常，2：冻结，3：注销
		@see AccountStatus
	"""
	status:Int
	"""注册方式
		0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
		@see AccountRegisterTypes
	"""
	registerType:Int
	"""来源类型
		0、内置，1、项目主网站，2、安卓，3、IOS，4、后台导入，5、迁移数据，6、分销平台项目主网站，7、专题，8、华医网，9、江西管理平台
		@see AccountSourceTypes
	"""
	sourceType:Int
	"""创建时间"""
	createdTime:DateTime
}
"""功能描述：帐户认证信息
	@Author： wtl
	@Date： 2022年5月11日 14:23:18
"""
type AuthenticationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.AuthenticationResponse") {
	"""帐号"""
	identity:String
	"""认证标识类型
		1用户名,2手机,3身份证,4邮箱,5第三方OpenId
	"""
	identityType:Int
	"""认证方式状态 1启用，2禁用
		@see AuthenticationStatusEnum
	"""
	status:Int
}
"""功能描述：时间直方图统计结果
	@Author： wtl
	@Date： 2021/12/30 9:58
"""
type DateHistogramResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.DateHistogramResponse") {
	"""时间单位"""
	timeUnit:TimeUnitEnum
	"""统计结果元素"""
	histogram:[DateHistogramItemModel]
	"""总计"""
	totalCount:Long!
}
"""功能描述：角色信息
	@Author： wtl
	@Date： 2022/1/24 20:17
"""
type RoleResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.RoleResponse") {
	"""角色id"""
	roleId:String
	"""角色名称"""
	roleName:String
	"""角色类型
		（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
		@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
	"""
	roleType:String
	"""角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
		@see RoleCategories
	"""
	roleCategory:Int
	"""应用方类型(4:子项目 5：单位 6：服务商)
		@see SystemMemberTypes
	"""
	applicationMemberType:Int
	"""是否冻结，1代表该账户的角色被冻结，其他情况均为未冻结
		@see com.fjhb.ms.basicdata.constants.AccountRoleFrozeStatusConstants
	"""
	frozeStatus:Int
	"""应用方ID"""
	applicationMemberId:String
	"""角色说明"""
	description:String
}
"""功能描述：账号信息"""
type UserAccountInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.UserAccountInfoResponse") {
	"""账户信息"""
	accountInfo:AccountResponse
	"""用户信息"""
	userInfo:UserInfoResponse
	"""人员信息"""
	personInfo:PersonInfoBasicResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
	"""角色信息集合"""
	roleList:[RoleResponse]
}
"""功能描述：用户基础信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type UserInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.UserInfoResponse") {
	"""用户id"""
	userId:String
	"""用户名称"""
	userName:String
	"""性别
		-1未知，0女，1男
		@see Genders
	"""
	gender:Int
	"""证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）"""
	idCardType:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""邮箱"""
	email:String
	"""工作单位名称"""
	companyName:String
	"""工作单位统一社会信用代码"""
	companyCode:String
}
"""功能描述：管理员信息
	@Author： wtl
	@Date： 2022/1/24 20:17
"""
type AdminInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.AdminInfoResponse") {
	"""账户信息"""
	accountInfo:AccountResponse
	"""管理员用户信息"""
	userInfo:AdminUserInfoResponse
	"""人员信息"""
	personInfo:PersonInfoBasicResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
	"""角色信息集合"""
	roleList:[RoleResponse]
}
"""功能描述 : 合同服务商管理员信息"""
type ContractProviderAdminInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse") {
	"""账户信息"""
	accountInfo:AccountResponse
	"""管理员用户信息"""
	userInfo:AdminUserInfoResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
	"""角色信息集合"""
	roleList:[RoleResponse]
}
"""功能描述 : 企业单位管理员信息
	@date : 2022/6/18 12:24
"""
type EnterpriseUnitAdminInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse") {
	"""账户信息"""
	accountInfo:AccountResponse
	"""管理员用户信息"""
	userInfo:AdminUserInfoResponse
	"""人员信息"""
	personInfo:EnterpriseUnitPersonInfoResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
	"""角色信息集合"""
	roleList:[RoleResponse]
}
"""功能描述 : 政府单位管理员信息
	@date : 2022/6/18 12:24
"""
type GovernmentUnitAdminInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.GovernmentUnitAdminInfoResponse") {
	"""账户信息"""
	accountInfo:AccountResponse
	"""管理员用户信息"""
	userInfo:AdminUserInfoResponse
	"""人员信息"""
	personInfo:PersonInfoBasicResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
	"""角色信息集合"""
	roleList:[RoleResponse]
}
"""功能描述 : 服务商管理员信息"""
type ServicerAdminInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ServicerAdminInfoResponse") {
	"""服务商业务归属信息"""
	businessOwnerInfo:ServicerAdminBusinessOwnerInfoResponse
	"""账户信息"""
	accountInfo:AccountResponse
	"""管理员用户信息"""
	userInfo:AdminUserInfoResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
	"""角色信息集合"""
	roleList:[RoleResponse]
}
"""功能描述：管理员用户信息
	@Author： wtl
	@Date： 2022年1月25日 15:48:48
"""
type AdminUserInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.nested.AdminUserInfoResponse") {
	"""管辖地区集合"""
	manageRegionList:[RegionModel]
	"""办公室（所在处/科室）"""
	office:String
	"""用户id"""
	userId:String
	"""用户名称"""
	userName:String
	"""性别
		-1未知，0女，1男
		@see Genders
	"""
	gender:Int
	"""证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）"""
	idCardType:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""邮箱"""
	email:String
	"""工作单位名称"""
	companyName:String
	"""工作单位统一社会信用代码"""
	companyCode:String
}
"""人员信息模型"""
type EnterpriseUnitPersonInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.nested.EnterpriseUnitPersonInfoResponse") {
	"""是否法人帐号"""
	isCorporateAccount:Boolean
	"""人员实名认证信息"""
	personIdentityVerificationInfo:PersonIdentityVerificationResponse
}
"""人员实名认证信息模型"""
type PersonIdentityVerificationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.nested.PersonIdentityVerificationResponse") {
	"""是否已认证"""
	identityVerification:Boolean
	"""认证渠道(1:闽政通 2：腾讯)"""
	identityVerificationChannel:Int
	"""认证时间"""
	identityVerificationTime:DateTime
}
"""人员信息模型"""
type PersonInfoBasicResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.nested.PersonInfoBasicResponse") {
	"""人员实名认证信息"""
	personIdentityVerificationInfo:PersonIdentityVerificationResponse
}
"""业务归属信息模型"""
type ServicerAdminBusinessOwnerInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.nested.ServicerAdminBusinessOwnerInfoResponse") {
	"""服务商集合"""
	servicerList:[ServicerResponse]
}
"""服务商信息模型"""
type ServicerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.nested.ServicerResponse") {
	"""服务商类型（1：培训机构 2：课件供应商 3：渠道商 4：参训单位 5：合同供应商）
		@see ServicerTypes
	"""
	servicerType:Int
	"""服务商id"""
	servicerId:String
}
"""单位信息模型"""
type UnitInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.nested.UnitInfoResponse") {
	"""单位ID"""
	unitId:String
}
"""功能描述：集体缴费管理员信息
	@Author： wtl
	@Date： 2022年1月26日 10:38:15
"""
type CollectiveInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.collective.CollectiveInfoResponse") {
	"""账户信息"""
	accountInfo:AccountResponse
	"""用户信息"""
	userInfo:UserInfoResponse
	"""用户认证信息"""
	authenticationList:[AuthenticationResponse]
}
"""Description:业务数据字典"""
type BusinessDictionaryAcrossTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.BusinessDictionaryAcrossTypeResponse") {
	"""字典关系id"""
	id:String
	"""主字典ID"""
	masterId:String
	"""从字典ID"""
	slaveId:String
	"""从字典字典类型|对应枚举BusinessDataDictionaryTypeEnum
		@see BusinessDataDictionaryTypeEnum
	"""
	slaveType:String
	"""初始化数据识别标志"""
	initable:Int
}
"""业务地区信息"""
type BusinessRegionResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.BusinessRegionResponse") {
	"""地区编码"""
	id:String
	"""上级地区编码"""
	parentId:String
	"""地区路径"""
	regionPath:String
	"""地区名称"""
	name:String
	"""排序"""
	sort:Int!
	"""是否启用"""
	enable:Boolean!
}
"""行政区划地区名称
	<AUTHOR>
	@since 2022/7/6
"""
type BusinessTreeRegionNameMap @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.BusinessTreeRegionNameMap") {
	"""地区路径
		格式：/350000/350100/350101
	"""
	path:String
	"""地区名称拼接
		格式：福建省/福州市/鼓楼区
	"""
	name:String
}
type BusinessTreeRegionResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.BusinessTreeRegionResponse") {
	"""id"""
	id:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""行政区划"""
	code:String
	"""结点名称"""
	name:String
	"""父节点Id -1时为根结点"""
	parentId:String
	"""业务Id"""
	businessId:String
	"""业务树结构路径"""
	regionPath:String
}
"""业务年度信息"""
type BusinessYearResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.BusinessYearResponse") {
	"""业务年度编号"""
	id:String
	"""年度"""
	year:String
	"""序号"""
	sort:Int!
	"""是否启用"""
	enable:Boolean!
}
type GetIndustryTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.GetIndustryTypeResponse") {
	"""行业类型id"""
	id:String
	"""代码"""
	code:String
	"""行业类型名称"""
	industryType:String
	"""上级编号"""
	parentId:String
	"""上级代码"""
	parentCode:String
}
"""单位类型"""
type GetUnitTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.GetUnitTypeResponse") {
	"""单位类型id"""
	id:String
	"""代码"""
	code:String
	"""单位类型名称"""
	unitType:String
	"""上级编号"""
	parentId:String
	"""上级代码"""
	parentCode:String
}
"""行业属性分类信息
	<AUTHOR>
	@since 2022/1/17
"""
type IndustryPropertyCategoryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.IndustryPropertyCategoryResponse") {
	"""分类code"""
	code:String
	"""分类名称"""
	name:String
	"""序号"""
	sort:Int!
}
"""网校行业属性id集合"""
type IndustryPropertyInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.IndustryPropertyInfoResponse") {
	"""行业培训属性ID"""
	industryPropertyId:String
	"""服务商ID"""
	serviceId:String
	"""行业属性名称"""
	industryPropertyName:String
	"""排序"""
	sort:Int
	"""来源id，模板数据的值为-1，网校的值为对应的模板行业培训属性id"""
	sourceId:String
	"""行业属性类型"""
	propertyType:Int
}
type IndustryPropertyResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.IndustryPropertyResponse") {
	"""行业培训属性ID"""
	industryPropertyId:String
	"""行业属性名称"""
	industryPropertyName:String
	"""行业字典ID"""
	industryId:String
	"""行业字典名称"""
	industryName:String
	"""更新时间"""
	updateTime:DateTime
	"""创建时间"""
	createTime:DateTime
}
"""培训类别信息
	<AUTHOR>
	@since 2022/2/10
"""
type IndustryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.IndustryResponse") {
	"""行业编号"""
	id:String
	"""行业名称"""
	name:String
	"""序号"""
	sort:Int!
}
type IndustryTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.IndustryTypeResponse") {
	"""行业类型id"""
	id:String
	"""代码"""
	code:String
	"""行业类型名称"""
	industryType:String
	"""排序"""
	sort:Int!
}
"""职称等级
	<AUTHOR>
	@since 2022/2/10
"""
type LeaderPositionLevelResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.LeaderPositionLevelResponse") {
	"""职称等级编号"""
	id:String
	"""职称等级名称"""
	name:String
	"""序号"""
	sort:Int!
}
"""物理地区信息"""
type PhysicalRegionResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.PhysicalRegionResponse") {
	"""地区编码"""
	id:String
	"""上级地区编码"""
	parentId:String
	"""地区路径"""
	regionPath:String
	"""地区名称"""
	name:String
	"""序号"""
	sort:Int!
	"""是否启用"""
	enable:Boolean!
}
"""功能描述：地区字典信息
	@Author： yxw
	@Date： 2023年6月15日
"""
type RegionResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.RegionResponse") {
	"""地区编码"""
	code:String
	"""父级地区编码"""
	parentCode:String
	"""地区编码路径"""
	codePath:String
	"""地区名称"""
	name:String
	"""级别|1省级 2市级 3区县级"""
	level:Int
	"""地区排序"""
	sort:Int
}
"""培训类别信息
	<AUTHOR>
	@since 2022/2/10
"""
type SubjectTypePageResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.SubjectTypePageResponse") {
	"""科目类型编号"""
	id:String
	"""科目类型名称"""
	name:String
	"""展示名称"""
	showName:String
}
"""培训类别信息
	<AUTHOR>
	@since 2022/2/10
"""
type TrainingCategoryPageResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.TrainingCategoryPageResponse") {
	"""培训类别编号"""
	categoryId:String
	"""培训类别父级编号"""
	parentId:String
	"""培训类别名称"""
	name:String
	"""展示名称"""
	showName:String
}
"""培训类别信息
	<AUTHOR>
	@since 2022/2/10
"""
type TrainingCategoryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.TrainingCategoryResponse") {
	"""培训类别编号"""
	categoryId:String
	"""培训类别父级编号"""
	parentId:String
	"""培训类别名称"""
	name:String
	"""序号"""
	sort:Int!
}
"""培训属性信息
	<AUTHOR>
	@since 2022/1/17
"""
type TrainingPropertyResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.TrainingPropertyResponse") {
	"""业务侧培训属性关系主键ID"""
	propertyRelationId:String
	"""清洗侧属性字典ID"""
	propertyId:String
	"""属性名称"""
	name:String
	"""序号"""
	sort:Int!
	"""code值"""
	code:Int
	"""code值，扩展"""
	codeExt:String
	"""展示名称"""
	showName:String
	"""如果是科目类型下的属性，则该值为null"""
	parentId:String
	"""是否可用，0停用1可用"""
	available:Int
}
"""单位类型"""
type UnitTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.UnitTypeResponse") {
	"""单位类型id"""
	id:String
	"""代码"""
	code:String
	"""单位类型名称"""
	unitType:String
	"""排序"""
	sort:Int!
}
"""资讯分类信息"""
type NewsCategoryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.NewsCategoryResponse") {
	"""资讯分类编号"""
	newsCategoryId:String
	"""分类名称"""
	categoryName:String
	"""分类代码"""
	code:String
}
"""<AUTHOR> linq
	@date : 2025-03-31 09:54
	@description : 资讯分类信息(树形结构)
"""
type NewsCategoryTreeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.NewsCategoryTreeResponse") {
	"""资讯分类编号"""
	newsCategoryId:String
	"""分类名称"""
	categoryName:String
	"""分类代码"""
	code:String
	"""子资讯分类"""
	children:[NewsCategoryTreeResponse]
}
"""简略资讯信息"""
type NewsCompleteResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.NewsCompleteResponse") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""所属业务平台"""
	businessPlatform:String
	"""分类名称"""
	name:String
	"""资讯状态 0 草稿 1正常"""
	status:Int!
	"""发布时间"""
	publishTime:DateTime
	"""分类id"""
	necId:String
}
"""详细资讯信息"""
type NewsDetailResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.NewsDetailResponse") {
	"""资讯编号"""
	newId:String
	"""平台id"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商id"""
	serviceId:String
	"""分类id"""
	necId:String
	"""标题"""
	title:String
	"""摘要"""
	summary:String
	"""内容"""
	content:String
	"""封面图片路径"""
	coverPath:String
	"""来源"""
	source:String
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""是否置顶"""
	isTop:Boolean!
	"""发布地区编码"""
	areaCodePath:String
	"""资讯状态 0 草稿 1正常"""
	status:Int!
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""浏览数量"""
	reviewCount:Int!
	"""弹窗起始时间"""
	popupBeginTime:DateTime
	"""弹窗截止时间"""
	popupEndTime:DateTime
	"""专题ID"""
	specialSubjectId:String
}
"""简略资讯信息"""
type NewsSimpleResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.NewsSimpleResponse") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""是否置顶"""
	isTop:Boolean!
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""摘要"""
	summary:String
	"""分类名称"""
	name:String
	"""资讯状态 0 草稿 1正常"""
	status:Int!
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""发布地区编码"""
	areaCodePath:String
	"""专题Id"""
	specialSubjectId:String
}
"""<AUTHOR> linq
	@date : 2023-07-19 08:36
	@description：网校开通统计返回值
"""
type OnlineSchoolCountResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.onlineschool.OnlineSchoolCountResponse") {
	"""已开通"""
	openCount:String
	"""使用中"""
	usingCount:String
	"""停用"""
	unableCount:String
	"""已到期"""
	expireCount:String
}
"""<AUTHOR> linq
	@date : 2023-07-13 18:01
	@description：网校信息返回值
"""
type OnlineSchoolInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.onlineschool.OnlineSchoolInfoResponse") {
	"""网校基础信息"""
	basicInfo:OnlineSchoolBasicInfo
	"""网校配置信息"""
	configInfo:OnlineSchoolConfigInfo
}
"""<AUTHOR> linq
	@date : 2023-07-18 10:42
	@description：客户端信息
"""
type Client @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.onlineschool.nested.Client") {
	"""客户端类型
		1-web访问 2-h5访问
		@see ClientTypesConstant
	"""
	clientType:Int!
	"""域名类型
		1-华博域名 2-业主自由域名
		@see DomainNameTypesConstant
	"""
	domainNameType:Int!
	"""域名"""
	domainName:String
	"""前端模板id"""
	portalTemplateId:String
	"""cnzz信息"""
	cnzz:String
	"""目录名"""
	dirName:String
}
"""<AUTHOR> linq
	@date : 2023-07-14 09:48
	@description：行业培训属性
"""
type Industry @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.onlineschool.nested.Industry") {
	"""id"""
	id:String
	"""行业名称"""
	name:String
	"""属性"""
	properties:String
	"""属于网校的培训属性"""
	onlineSchoolProperty:String
}
"""<AUTHOR> linq
	@date : 2023-07-14 08:42
	@description：网校基础信息
"""
type OnlineSchoolBasicInfo @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.onlineschool.nested.OnlineSchoolBasicInfo") {
	"""网校id"""
	onlineSchoolId:String
	"""网校平台名称"""
	name:String
	"""服务商名称"""
	servicerName:String
	"""服务商LOGO"""
	logo:String
	"""合作伙伴类型(供应商、分销商类型) 1-个人 2-企业"""
	partnerType:Int
	"""服务地区"""
	serviceAreas:[String]
	"""培训属性"""
	trainingProperties:TrainingProperties
	"""人员行业属性"""
	personIndustries:[Industry]
	"""业主单位全称"""
	unitName:String
	"""统一社会信用代码"""
	code:String
	"""隶属单位ID"""
	unitId:String
	"""业主单位简称"""
	unitShotName:String
	"""业主负责人"""
	transactor:String
	"""手机号"""
	phone:String
	"""网校模式
		@see OnlineSchoolModesConstant
	"""
	onlineSchoolModes:Int!
	"""是否已线下签署合约"""
	isOfflineContractSigned:Boolean
	"""线下签署合约日期"""
	offlineContractSignedDate:DateTime
	"""归属市场经办"""
	marketTransactor:String
	"""网校背景描述"""
	description:String
	"""合约状态
		@see OnlineSchoolContractStatusConstant
	"""
	status:Int
	"""是否可用"""
	isEnable:Boolean
	"""是否到期"""
	isExpired:Boolean
	"""是否续期"""
	isRenewed:Boolean
	"""网校开通时间"""
	OSOpenTime:DateTime
	"""网校合约id"""
	contractId:String
}
"""<AUTHOR> linq
	@date : 2023-07-14 10:03
	@description：网校配置信息
"""
type OnlineSchoolConfigInfo @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.onlineschool.nested.OnlineSchoolConfigInfo") {
	"""客户端"""
	clients:[Client]
	"""是否提供短信服务"""
	offerSmsService:Boolean
	"""短信配置信息"""
	smsConfig:SmsConfig
	"""培训周期模式
		1-长期 2-短期
		@see TrainingPeriodModesConstant
	"""
	trainingPeriodModes:Int
	"""到期时间"""
	expireDate:DateTime
	"""强制完善信息：
		true：当学员信息不全时，强制触发完善信息页面
		false：当学员信息不全时，强制跳过完善信息页面
	"""
	enabledForceCompleteInfo:Boolean
}
"""<AUTHOR> linq
	@date : 2023-07-18 10:47
	@description：短信配置信息
"""
type SmsConfig @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.onlineschool.nested.SmsConfig") {
	"""短信账号"""
	smsAccount:String
	"""短信密码"""
	smsPassword:String
	"""提供商id"""
	providerId:String
	"""短信网关授权配置名称"""
	ispName:String
	"""短信网关appId(非必填)"""
	ispAppId:String
	"""短信授权仅账号扩展信息，存放第三方额外信息，例如证书(非必填)"""
	extension:String
	"""短信模板列表"""
	messageTemplateList:[MessageTemplate]
}
"""<AUTHOR> linq
	@date : 2023-07-14 09:46
	@description：培训属性
"""
type TrainingProperties @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.onlineschool.nested.TrainingProperties") {
	"""年份"""
	years:[String]
	"""地区"""
	areas:[String]
	"""培训行业属性"""
	industries:[Industry]
	"""补贴培训：培训类别"""
	trainingCategoryIds:[String]
}
"""门户信息返回模型"""
type PortalResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.PortalResponse") {
	"""归属信息"""
	ownerInfo:PortalOwnerInfoResponse
	"""门户信息"""
	portalInfo:PortalInfoResponse
}
"""门户轮播图"""
type PortalBannerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.nested.PortalBannerResponse") {
	"""轮播图id"""
	bannerId:String
	"""轮播图名称"""
	name:String
	"""图片路径"""
	path:String
	"""链接地址"""
	link:String
	"""轮播图排序"""
	sort:Int
	"""是否启用"""
	isEnable:Boolean
	"""创建时间"""
	createdTime:DateTime
}
"""门户友情链接"""
type PortalFriendLinkResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.nested.PortalFriendLinkResponse") {
	"""友情链接id"""
	friendLinkId:String
	"""友情链接标题"""
	title:String
	"""友情链接图片"""
	picture:String
	"""友情链接类型（1：文本 2：图片）
		@see FriendLinkTypes
	"""
	friendLinkType:Int
	"""链接"""
	link:String
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:DateTime
}
"""门户信息"""
type PortalInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.nested.PortalInfoResponse") {
	"""门户id"""
	portalId:String
	"""门户类型（1：web端 2：移动端）
		@see com.fjhb.domain.basicdata.api.servicer.consts.PortalTypes
	"""
	portalType:Int
	"""门户状态（0：未发布 1：已发布）"""
	status:Int
	"""门户标题"""
	title:String
	"""门户logo"""
	logo:String
	"""浏览器图标"""
	icon:String
	"""移动二维码"""
	mobileQRCode:String
	"""客服电话图片"""
	csPhonePicture:String
	"""客服电话"""
	csPhone:String
	"""客服咨询时间"""
	csCallTime:String
	"""在线客服代码内容id"""
	csOnlineCodeId:String
	"""在线客服代码内容"""
	csOnlineCodeContent:String
	"""培训流程图片"""
	trainingFlowPicture:String
	"""底部内容(底部落款)"""
	footContentId:String
	"""底部内容(底部落款)"""
	footContent:String
	"""友情链接集合"""
	friendLinkList:[PortalFriendLinkResponse]
	"""主题颜色"""
	themeColor:String
	"""宣传口号"""
	slogan:String
	"""门户简介说明内容Id"""
	contentId:String
	"""门户简介说明内容"""
	content:String
	"""是否提供服务号"""
	isProvideServiceAccount:Boolean
	"""域名"""
	domainName:String
	"""轮播图列表"""
	portalBannerList:[PortalBannerResponse]
	"""栏目列表"""
	portalMenuList:[PortalMenuResponse]
	"""创建时间"""
	createdTime:DateTime
}
"""栏目信息"""
type PortalMenuResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.nested.PortalMenuResponse") {
	"""栏目id"""
	menuId:String
	"""栏目名称"""
	name:String
	"""父栏目id"""
	parentId:String
	"""栏目类型（1：菜单 2：资讯）
		@see MenuTypes
	"""
	menuType:Int
	"""来源类型（1：内置 2：用户创建）
		@see MenuSourceTypes
	"""
	sourceType:Int
	"""链接"""
	link:String
	"""业务code"""
	code:String
	"""引用id(咨询类型的栏目，引用的是资讯分类id)"""
	referenceId:String
	"""是否允许添加子级"""
	allowChildren:Boolean
	"""是否启用"""
	enable:Boolean
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:DateTime
}
"""功能描述：门户归属信息
	@Author： wtl
	@Date： 2022/10/19 9:18
"""
type PortalOwnerInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.nested.PortalOwnerInfoResponse") {
	"""所属培训机构id"""
	trainingInstitutionId:String
}
"""企业单位业务归属信息"""
type ContractProviderOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.ContractProviderOwnerResponse") {
	"""服务商隶属企业单位路径"""
	attachToEnterpriseUnitIdPath:String
	"""隶属企业帐户ID
		value:String，"隶属企业帐户ID"
	"""
	accountId:String
}
"""功能描述 : 合同服务商信息
	@date : 2022/7/16 14:52
"""
type ContractProviderResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.ContractProviderResponse") {
	"""业务归属信息"""
	businessOwnerInfo:ContractProviderOwnerResponse
	"""服务商基本信息"""
	contractProvider:ContractProviderInfoResponse
	"""服务商合约信息"""
	servicerContract:[ServicerContractResponse]
}
"""课件供应商查询GRPC"""
type CoursewareSupplierGRPCResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.CoursewareSupplierGRPCResponse") {
	"""id"""
	id:String
	"""名称"""
	name:String
	"""创建者ID"""
	createdUserId:String
	"""创建时间"""
	createTime:DateTime
	initable:Int
}
"""功能描述 : 课件供应商信息
	@date : 2022年10月31日 12:07:38
"""
type CoursewareSupplierResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.CoursewareSupplierResponse") {
	"""业务归属信息"""
	businessOwnerInfo:ServicerBusinessOwnerResponse
	"""服务商基本信息"""
	servicerBase:ServicerBaseInfoResponse
}
"""功能描述 : 分销服务商信息"""
type DistributionServicerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.DistributionServicerResponse") {
	"""服务商基本信息"""
	servicerBase:ServicerBaseInfoResponse
	"""业务归属信息"""
	businessOwnerInfo:ServicerBusinessOwnerResponse
}
"""服务商信息响应对象
	<AUTHOR>
"""
type ServicerInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.ServicerInfoResponse") {
	"""服务商信息"""
	servicerInfo:ServicerInfo
}
"""功能描述 : 供应商服务商信息"""
type SupplierServicerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.SupplierServicerResponse") {
	"""服务商基本信息"""
	servicerBase:ServicerBaseInfoResponse
	"""业务归属信息"""
	businessOwnerInfo:ServicerBusinessOwnerResponse
}
"""服务商基础信息"""
type ContractProviderInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.nested.ContractProviderInfoResponse") {
	"""服务商 Id"""
	servicerId:String
	"""服务商名称"""
	servicerName:String
	"""服务商简称"""
	servicerShortName:String
	"""组织机构代码"""
	code:String
	"""服务商来源
		1平台创建、2合作机构
	"""
	resourceType:Int
	"""所在地区"""
	region:RegionModel
	"""联系人"""
	contactPerson:String
	"""联系电话"""
	phone:String
	"""启用状态 1：启用，2：停用"""
	enable:Int
	"""创建时间"""
	createdTime:DateTime
}
"""附件"""
type ServicerAttachResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.nested.ServicerAttachResponse") {
	"""文件名"""
	fileName:String
	"""文件路径"""
	filePath:String
}
"""服务商基础信息"""
type ServicerBaseInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.nested.ServicerBaseInfoResponse") {
	"""服务商 Id"""
	servicerId:String
	"""服务商名称"""
	servicerName:String
	"""服务商简称"""
	servicerShortName:String
	"""统一社会信用社代码"""
	code:String
	"""服务商类型
		说明：1培训机构、2课件供应商、3渠道商、4参训单位、5合同供应商、6供应商、7分销商
	"""
	servicerType:Int
	"""服务商来源
		1平台创建、2合作机构
	"""
	resourceType:Int
	"""所在地区"""
	region:RegionModel
	"""联系人"""
	contactPerson:String
	"""联系电话"""
	phone:String
	"""附件"""
	attach:[ServicerAttachResponse]
	"""证件类型"""
	idCardType:Int
	"""证件号"""
	idCard:String
	"""合作伙伴类型(供应商、分销商类型) 1-个人 2-企业"""
	partnerType:Int
	"""服务商简介"""
	abouts:String
	"""服务商介绍内容"""
	content:String
	"""启用状态 1：启用，2：停用"""
	enable:Int
	"""创建时间"""
	createdTime:DateTime
}
"""服务商业务归属信息"""
type ServicerBusinessOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.nested.ServicerBusinessOwnerResponse") {
	"""服务商隶属企业单位路径"""
	attachToEnterpriseUnitIdPath:String
	"""隶属企业帐户ID
		value:String，"隶属企业帐户ID"
	"""
	accountId:String
}
"""服务商合约信息"""
type ServicerContractResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.nested.ServicerContractResponse") {
	"""合约Id"""
	contractId:String
	"""业主单位全称"""
	unitName:String
	"""业主单位简称"""
	unitShortName:String
	"""统一社会信用代码"""
	code:String
	"""所属地区编号(业务用户创建企业单位)"""
	region:RegionModel
	"""是否已线下签署合约"""
	isOfflineContractSigned:Boolean
	"""线下签署合约日期"""
	offlineContractSignedDate:DateTime
	"""到期时间"""
	expireDate:DateTime
	"""合约状态
		@see ServicerContractStatus
	"""
	status:Int
	"""拓展信息 key:服务商类型
		@see ContractProviderContractBaseModel （网校合约）
	"""
	extendedMap:Map
	"""创建人id"""
	createdUserId:String
	"""创建时间"""
	createdTime:DateTime
}
"""服务商信息
	<AUTHOR>
"""
type ServicerInfo @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.support.ServicerInfo") {
	"""服务商ID"""
	id:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""帐户ID"""
	accountId:String
	"""所在地区"""
	region:String
	"""服务商类型"""
	servicerType:Int!
	"""服务商名称"""
	name:String
	"""服务商来源|1/2，平台创建/合作机构"""
	resourceType:Int!
	"""状态"""
	status:Int!
	"""服务商logo"""
	logo:String
	"""联系人"""
	contactPerson:String
	"""联系电话"""
	phone:String
	"""服务商简介"""
	abouts:String
	"""服务商详情ID"""
	contentId:String
	"""电子公章文件路径"""
	electronicSealPath:String
	"""创建时间"""
	createdTime:DateTime
}
"""功能描述：学员信息
	@Author： wtl
	@Date： 2022年1月26日 10:38:15
"""
type StudentInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse") {
	"""账户信息"""
	accountInfo:AccountResponse
	"""学员用户信息"""
	userInfo:StudentUserInfoResponse
	"""学员人员信息"""
	personInfo:StudentPersonInfoResponse
	"""第三方绑定信息"""
	openPlatformBind:OpenPlatformBindResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
}
"""功能描述：附件信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type AttachmentInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.nested.AttachmentInfoResponse") {
	"""附件名称"""
	name:String
	"""附件地址"""
	url:String
}
"""功能描述：学员绑定信息
	@Author： wtl
	@Date： 2022年5月12日 14:42:51
"""
type OpenPlatformBindResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.nested.OpenPlatformBindResponse") {
	"""是否绑定微信"""
	bindWX:Boolean!
	"""微信昵称"""
	nickNameByWX:String
}
"""功能描述：学员证书信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type StudentCertificateResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.nested.StudentCertificateResponse") {
	"""证书id"""
	certificateId:String
	"""证书编号"""
	certificateNo:String
	"""证书类别"""
	certificateCategory:String
	"""注册专业"""
	registerProfessional:String
	"""主/增项 | 1-主项 2-增项
		@see com.fjhb.domain.basicdata.api.user.consts.CertificateMainAddOnTypes
	"""
	mainAddOn:String
	"""发证日期"""
	releaseStartTime:DateTime
	"""证书有效期"""
	certificateEndTime:DateTime
	"""证书附件信息"""
	attachmentList:[AttachmentInfoResponse]
}
"""功能描述：学员行业信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type StudentIndustryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.nested.StudentIndustryResponse") {
	"""用户行业id"""
	userIndustryId:String
	"""行业id"""
	industryId:String
	"""一级专业类别id"""
	firstProfessionalCategory:String
	"""二级专业类别id"""
	secondProfessionalCategory:String
	"""职称等级"""
	professionalQualification:String
	"""人员类别（职业卫生行业）"""
	personnelCategory:String
	"""岗位类别（职业卫生行业）"""
	positionCategory:String
	"""技术等级（工勤行业）"""
	professionalLevel:String
	"""工种（工勤行业）"""
	jobCategoryId:String
	"""学员证书信息集合"""
	userCertificateList:[StudentCertificateResponse]
	"""教师行业 学段、学科信息"""
	sectionAndSubjects:[SectionAndSubjects]
	"""证书类型（药师行业）"""
	certificatesType:String
	"""执证类别（药师行业）"""
	practitionerCategory:String
}
type StudentPersonInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.nested.StudentPersonInfoResponse") {
	"""学历"""
	education:String
}
"""功能描述：学员用户信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type StudentUserInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.nested.StudentUserInfoResponse") {
	"""用户昵称"""
	nickName:String
	"""用户所属地区"""
	region:RegionModel
	"""头像地址"""
	photo:String
	"""联系地址"""
	address:String
	"""学员行业信息集合"""
	userIndustryList:[StudentIndustryResponse]
	"""证书技术工种Id"""
	jobCategoryId:String
	"""证书技术等级"""
	professionalLevel:Int
	"""所属工考办地区编码"""
	managementUnitRegion:RegionModel
	"""证书技术工种名称"""
	jobCategoryName:String
	"""用户工作单位所在地区"""
	companyRegion:RegionModel
	"""是否工勤人员 （0非工勤人员，1工勤人员）"""
	isWorker:String
	"""是否退休 (0非退休人员，1退休人员)"""
	isRetire:String
	"""学历"""
	education:String
	"""用户id"""
	userId:String
	"""用户名称"""
	userName:String
	"""性别
		-1未知，0女，1男
		@see Genders
	"""
	gender:Int
	"""证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）"""
	idCardType:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""邮箱"""
	email:String
	"""工作单位名称"""
	companyName:String
	"""工作单位统一社会信用代码"""
	companyCode:String
}
"""地区专题信息响应体"""
type AreaTrainingChannelInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.trainingchannel.AreaTrainingChannelInfoResponse") {
	"""专题用户id"""
	trainingChannelUserId:String
	"""用户id"""
	userId:String
	"""地区id"""
	regionId:String
	"""工作单位性质 字典"""
	unitNature:String
	"""在编情况 字典"""
	staffingStatus:String
	"""是否在专技岗位工作 字典"""
	isZJPosition:String
	"""职称系列 字典"""
	titleSeries:String
	"""职称专业"""
	titleProfessional:String
	"""现有职称等级  字典"""
	titleGrade:String
	"""现有职称资格名称"""
	titleQualificationName:String
	"""现有职称有效范围  字典"""
	titleEffectiveRange:String
	"""最高学历"""
	highestEducationLevel:String
}
"""客户端信息返回"""
type ClientInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.ClientInfoResponse") {
	"""客户端类型 | 1、Web 2、H5
		@see ClientTypesConstant
	"""
	clientType:Int
	"""域名类型
		@see DomainNameTypesConstant
	"""
	domainNameType:Int
	"""域名"""
	domainName:String
	"""前端模板id"""
	portalTemplateId:String
	"""cnzz信息"""
	cnzz:String
	"""目录名"""
	dirName:String
}
"""门户信息
	<AUTHOR>
"""
type PortalInfoResponse1 @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.PortalInfoResponse") {
	"""id"""
	id:String
	"""培训机构id"""
	servicerId:String
	"""服务商类型 | 1培训机构、2课件供应商、3渠道商、4参训单位、5合同供应商、6供应商、7分销商、8企业"""
	servicerType:Int
	"""培训机构logo"""
	institutionLogo:String
	"""门户类型"""
	portalType:Int
	"""门户标题"""
	title:String
	"""门户logo"""
	logo:String
	"""门户图标"""
	icon:String
	"""友情链接类型 1-文本  2-图片"""
	friendLinkType:Int
	"""主题颜色"""
	themeColor:String
	"""移动二维码"""
	mobileQRCode:String
	"""移动二维码来源标识
		1-系统生成 2-自定义
	"""
	mobileQRCodeSign:Int
	"""客服电话图片"""
	CSPhonePicture:String
	"""客服电话"""
	CSPhone:String
	"""客服咨询时间"""
	CSCallTime:String
	"""在线客服代码内容id"""
	CSOnlineCodeId:String
	"""培训流程图片"""
	trainingFlowPicture:String
	"""底部内容（底部落款）"""
	footContent:String
	"""宣传口号"""
	slogan:String
	"""域名"""
	domainName:String
	"""H5域名(请求Web端门户信息才会使用这个字段 如果请求的是H5端的门户信息，该字段为null)"""
	domainNameH5:String
	"""域名类型
		系统默认域名 1
		自有域名 2
		@see com.fjhb.domain.basicdata.api.servicer.consts.DomainNameTypeConsts
	"""
	domainNameType:Int
	"""门户简介说明内容"""
	content:String
	"""是否提供服务号"""
	isProvideServiceAccount:Boolean
	"""是否已发布"""
	isPublished:Boolean
	"""网校状态
		1-正常  2-失效
	"""
	onlineSchoolStatus:Int!
	"""创建时间"""
	createTime:DateTime
	"""更新时间"""
	updateTime:DateTime
	"""发布时间"""
	publishedTime:DateTime
	"""取消发布时间"""
	unpublishedTime:DateTime
	"""cnzz信息"""
	cnzz:String
	"""目录名"""
	dirName:String
	"""网校模式
		@see OnlineSchoolModesConstant
	"""
	onlineSchoolModes:Int
	"""是否到期"""
	isExpired:Boolean
	"""前端模板id"""
	portalTemplateId:String
	"""企业客服微信"""
	CSWechat:String
}
"""轮播图信息集合响应
	<AUTHOR>
"""
type BannerInfoListResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.banner.BannerInfoListResponse") {
	"""轮播图信息集合"""
	bannerInfos:[BannerInfo]
}
"""轮播图信息
	<AUTHOR>
"""
type BannerInfo @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.banner.support.BannerInfo") {
	"""id"""
	id:String
	"""所属门户id"""
	portalId:String
	"""名称"""
	name:String
	"""路径"""
	path:String
	"""链接"""
	link:String
	"""排序"""
	sort:Int
	"""是否启用"""
	isEnable:Boolean
	"""创建时间"""
	createdTime:DateTime
}
"""友情链接集合响应
	<AUTHOR>
"""
type FriendLinkListResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.friendlink.FriendLinkListResponse") {
	"""友情链接集合"""
	friendLinkInfos:[FriendLinkInfo]
}
"""友情链接信息
	<AUTHOR>
"""
type FriendLinkInfo @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.friendlink.support.FriendLinkInfo") {
	"""id"""
	id:String
	"""所属门户id"""
	portalId:String
	"""标题"""
	title:String
	"""图片"""
	picture:String
	"""友情链接类型"""
	friendLinkType:Int
	"""链接"""
	link:String
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
}
"""栏目配置集合响应
	<AUTHOR>
"""
type MenuSettingInfoListResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.menu.MenuSettingInfoListResponse") {
	"""栏目配置信息集合"""
	menuSettingInfos:[MenuSettingInfo]
}
"""栏目配置信息
	<AUTHOR>
"""
type MenuSettingInfo @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.menu.support.MenuSettingInfo") {
	"""栏目id"""
	id:String
	"""栏目类型"""
	menuType:Int
	"""来源类型
		@see com.fjhb.domain.basicdata.api.servicer.consts.MenuSourceTypes
		1-内置  2-用户创建
	"""
	sourceType:Int!
	"""栏目名称（对应业务那边的displayName）"""
	name:String
	"""原始名称（对应业务那边的name）"""
	originName:String
	"""父栏目id"""
	parentId:String
	"""链接"""
	link:String
	"""业务code（用于同步资讯、前端link相同的情况下做二次识别）"""
	code:String
	"""排序"""
	sort:Int
	"""是否允许添加子级"""
	allowChildren:Boolean
	"""是否启用"""
	enable:Boolean!
}
"""行业信息下-企业单位统计
	<AUTHOR>
	@date 2022-07-25
"""
type EnterpriseUnitIndustryStatisticResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitIndustryStatisticResponse") {
	"""行业类型id，可能是门类id、大类id、中类id、小类id"""
	industryId:String
	"""企业单位统计结果"""
	statisticInfo:EnterpriseUnitStatisticResponse
}
"""功能描述：企业单位信息
	@Author： wtl
	@Date： 2022年6月9日 14:20:55
"""
type EnterpriseUnitInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse") {
	"""企业单位业务归属信息"""
	businessOwnerInfo:EnterpriseUnitBusinessOwnerResponse
	"""单位基本信息"""
	unitBase:EnterpriseUnitBaseResponse
	"""经营信息"""
	businessInfo:BusinessInfoResponse
	"""单位认证信息"""
	unitIdentityVerificationInfo:UnitIdentityVerificationResponse
	"""管理员数量"""
	administratorCount:Long!
	"""可培训工种字典ID列表"""
	trainingJobCategoryList:[String]
}
"""行业信息下-企业单位统计
	<AUTHOR>
	@date 2022-07-25
"""
type EnterpriseUnitRegionStatisticResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitRegionStatisticResponse") {
	"""注册地区"""
	region:RegionModel
	"""企业单位统计结果"""
	statisticInfo:EnterpriseUnitStatisticResponse
}
"""单位类型下-企业单位统计
	<AUTHOR>
	@date 2022-07-25
"""
type EnterpriseUnitTypeStatisticResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitTypeStatisticResponse") {
	"""单位类型id,可能是一级id、二级id、三级id"""
	unitTypeId:String
	"""企业单位统计结果"""
	statisticInfo:EnterpriseUnitStatisticResponse
}
"""功能描述：政府单位信息
	@Author： wtl
	@Date： 2022年6月9日 10:41:26
"""
type GovernmentUnitInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.GovernmentUnitInfoResponse") {
	"""单位归属信息"""
	ownerInfo:GovernmentUnitOwnerResponse
	"""单位业务归属信息"""
	businessOwnerInfo:GovernmentUnitBusinessOwnerResponse
	"""政府单位基本信息"""
	unitBase:GovernmentUnitBaseResponse
}
"""功能描述：企业经营信息
	@Author： wtl
	@Date： 2022年6月9日 14:31:03
"""
type BusinessInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.nested.BusinessInfoResponse") {
	"""营业期限起始日期"""
	operatingBeginDate:DateTime
	"""营业期限截止日期"""
	operatingEndDate:DateTime
	"""行业信息"""
	industry:IndustryResponse1
	"""经营范围,例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。"""
	businessScope:String
	"""营业执照图片"""
	businessLicense:String
}
"""功能描述：企业单位信息
	@Author： wtl
	@Date： 2022年6月9日 14:23:04
"""
type EnterpriseUnitBaseResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.nested.EnterpriseUnitBaseResponse") {
	"""单位ID"""
	unitId:String
	"""单位名称"""
	unitName:String
	"""单位英文名称"""
	enName:String
	"""统一社会信用代码"""
	code:String
	"""logo"""
	logo:String
	"""法人信息"""
	legalPersonInfo:LegalPersonResponse
	"""单位类型（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）"""
	unitType:UnitTypeResponse1
	"""成立日期"""
	foundedDate:DateTime
	"""联系电话"""
	phone:String
	"""邮政编码"""
	postCode:String
	"""传真"""
	faxNumber:String
	"""注册地区"""
	region:RegionModel
	"""联系地址"""
	address:String
	"""注册地址"""
	registerAddress:String
	"""登记机关"""
	registeredOrgan:String
	"""注册资金"""
	registeredCapital:String
	"""创建时间"""
	createdTime:DateTime
	"""单位状态
		说明：1正常,2冻结
	"""
	status:Int
}
"""企业单位业务归属信息"""
type EnterpriseUnitBusinessOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.nested.EnterpriseUnitBusinessOwnerResponse") {
	"""企业归属信息路径
		单位路径（若单位为福州市企业，则该值为:"/福建省企业id/福州市企业id"）
	"""
	unitIdPath:String
}
"""企业单位统计结果
	<AUTHOR>
	@date 2022-07-25
"""
type EnterpriseUnitStatisticResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.nested.EnterpriseUnitStatisticResponse") {
	"""企业单位数量统计"""
	enterpriseUnitCount:Long!
}
"""功能描述：政府单位信息
	@Author： wtl
	@Date： 2022年6月9日 10:41:26
"""
type GovernmentUnitBaseResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.nested.GovernmentUnitBaseResponse") {
	"""单位id"""
	unitId:String
	"""单位名称"""
	unitName:String
	"""电话"""
	phone:String
	"""传真"""
	faxNumber:String
	"""所属地区"""
	region:RegionModel
	"""地址"""
	address:String
	"""管辖地区"""
	manageRegion:RegionModel
	"""创建时间"""
	createdTime:DateTime
}
"""功能描述：政府单位业务归属信息
	@Author： wtl
	@Date： 2022年6月24日 19:11:30
"""
type GovernmentUnitBusinessOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.nested.GovernmentUnitBusinessOwnerResponse") {
	"""单位路径（若单位为福州市人社，则该值为:"/福建省人社id/福州市人社id"）"""
	unitIdPath:String
}
"""功能描述：政府单位信息
	@Author： wtl
	@Date： 2022年6月9日 10:41:26
"""
type GovernmentUnitOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.nested.GovernmentUnitOwnerResponse") {
	"""上级单位id（若为顶级单位，该值为空）"""
	parentUnitId:String
}
"""行业信息
	<AUTHOR>
	@date 2022-06-18
"""
type IndustryResponse1 @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.nested.IndustryResponse") {
	"""行业信息ID路径"""
	industryIdPath:String
	"""门类"""
	firstLevelIndustryId:String
	"""大类"""
	secondLevelIndustryId:String
	"""中类"""
	thirdLevelIndustryId:String
	"""小类"""
	fourthLevelIndustryId:String
}
"""功能描述：企业法人信息
	@Author： wtl
	@Date： 2022年6月9日 14:31:03
"""
type LegalPersonResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.nested.LegalPersonResponse") {
	"""法定代表人"""
	legalPerson:String
	"""证件类型"""
	idCardType:String
	"""证件号"""
	idCard:String
}
"""单位认证信息模型"""
type UnitIdentityVerificationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.nested.UnitIdentityVerificationResponse") {
	"""是否已认证"""
	identityVerification:Boolean
	"""认证渠道（单位认证渠道：UnitIdentityVerificationChannels 人员认证渠道：PersonIdentityVerificationChannels）
		@see PersonIdentityVerificationChannels
		@see UnitIdentityVerificationChannels
	"""
	identityVerificationChannel:Int
	"""认证时间"""
	identityVerificationTime:DateTime
}
"""单位类型:（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）
	<AUTHOR>
	@date : 2022/6/18 14:15
"""
type UnitTypeResponse1 @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.nested.UnitTypeResponse") {
	"""单位类型ID路径"""
	unitTypeIdPath:String
	"""一级"""
	firstLevelUnitTypeId:String
	"""二级"""
	secondLevelUnitTypeId:String
	"""三级"""
	thirdLevelUnitTypeId:String
}
"""功能描述：管理员排序字段
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
enum EnterprisePersonSortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.EnterprisePersonSortFieldEnum") {
	"""创建时间"""
	createdTime
	"""账户类型"""
	accountType
	"""用户名称首字母"""
	userNameFirstLetter
	"""授予性质"""
	nature
}
"""功能描述：企业单位排序字段
	@Author： wtl
	@Date： 2022年5月12日 09:23:31
"""
enum EnterpriseUnitSortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.EnterpriseUnitSortFieldEnum") {
	"""创建时间"""
	createdTime
	"""单位名称"""
	unitName
	"""单位拼音名称"""
	pinyinName
	"""单位地区ID"""
	regionId
}
"""功能描述：政府单位排序字段
	@Author： wtl
	@Date： 2022年5月12日 09:23:31
"""
enum GovernmentUnitSortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.GovernmentUnitSortFieldEnum") {
	"""所属地区"""
	unitRegionPath
	"""单位名称"""
	unitName
	"""创建时间"""
	createdTime
}
"""功能描述：学员排序字段
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
enum PersonAccountSortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.PersonAccountSortFieldEnum") {
	"""创建时间"""
	createdTime
	"""授予性质"""
	nature
}
"""功能描述：服务商排序字段"""
enum ServicerSortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.ServicerSortFieldEnum") {
	"""创建时间"""
	createdTime
}

scalar List
type TrainingPropertyResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TrainingPropertyResponse]}
type UserInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserInfoResponse]}
type AdminInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [AdminInfoResponse]}
type CollectiveInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CollectiveInfoResponse]}
type NewsCompleteResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [NewsCompleteResponse]}
type ContractProviderAdminInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ContractProviderAdminInfoResponse]}
type ContractProviderResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ContractProviderResponse]}
type ServicerAdminInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ServicerAdminInfoResponse]}
type CoursewareSupplierGRPCResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CoursewareSupplierGRPCResponse]}
type CoursewareSupplierResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CoursewareSupplierResponse]}
type EnterpriseUnitInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [EnterpriseUnitInfoResponse]}
type DistributionServicerResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [DistributionServicerResponse]}
type EnterpriseUnitAdminInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [EnterpriseUnitAdminInfoResponse]}
type GovernmentUnitAdminInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [GovernmentUnitAdminInfoResponse]}
type GovernmentUnitInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [GovernmentUnitInfoResponse]}
type IndustryPropertyResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [IndustryPropertyResponse]}
type OnlineSchoolInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [OnlineSchoolInfoResponse]}
type PortalResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [PortalResponse]}
type NewsSimpleResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [NewsSimpleResponse]}
type StudentInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentInfoResponse]}
type SubjectTypePageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SubjectTypePageResponse]}
type SupplierServicerResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SupplierServicerResponse]}
type TrainingCategoryPageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TrainingCategoryPageResponse]}
type UserAccountInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserAccountInfoResponse]}
