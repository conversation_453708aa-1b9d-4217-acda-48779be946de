schema {
	query:Query
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取某个专业关系节点下一级章节子节点
		@param majorRelationId 专业关系子节点
		@return 章节列表
	"""
	findAllChildChapterByMajor(majorRelationId:String):[ChapterDTO] @NotAuthenticationRequired
	"""获取指定多个专业关系下章节节点及子节点
		@param majorRelationIdList 专业关系节点列表
		@return 专业关系节点对应章节列表
	"""
	findAllChildChapterByMajorList(majorRelationIdList:[String]):[MajorMapChapterListDTO] @NotAuthenticationRequired
	"""获取所有行业及专业
		@return 行业专业关系树
	"""
	findAllIndustryRelationList:[IndustryDTO] @NotAuthenticationRequired
}
type ChapterDTO @type(value:"com.fjhb.platform.core.v1.knowledge.api.dto.ChapterDTO") {
	relationId:String
	parentRelationId:String
	sort:Long
	id:String
	type:String
	name:String
	code:String
	enabled:Boolean
}
type IndustryDTO @type(value:"com.fjhb.platform.core.v1.knowledge.api.dto.IndustryDTO") {
	relationId:String
	majorModelList:[MajorDTO]
	id:String
	type:String
	name:String
	code:String
	enabled:Boolean
}
type MajorDTO @type(value:"com.fjhb.platform.core.v1.knowledge.api.dto.MajorDTO") {
	relationId:String
	sort:Long
	id:String
	type:String
	name:String
	code:String
	enabled:Boolean
}
type MajorMapChapterListDTO @type(value:"com.fjhb.platform.core.v1.knowledge.api.dto.MajorMapChapterListDTO") {
	majorRelationId:String
	chapterList:[ChapterDTO]
}

scalar List
