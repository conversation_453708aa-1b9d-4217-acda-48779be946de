<template>
  <el-container>
    <el-aside width="240px">
      <!--侧边栏按钮 展开时显示-->
      <a href="#" class="aside-btn el-icon-s-fold"></a>
      <!--侧边栏按钮 收起时显示-->
      <!--<a href="#" class="aside-btn el-icon-s-unfold"></a>-->
      <div class="logo">
        <span class="logo-txt">培训管理平台</span>
      </div>
      <div class="user-info">
        <img class="photo" src="./assets/images/default-photo.jpg" alt=" " />
        <p class="name">张某某</p>
        <div class="op-btn f-mt10">
          <el-button type="primary" size="mini" round plain>
            <i class="el-icon-s-tools"></i>
            帐号设置
          </el-button>
        </div>
      </div>
      <el-menu default-active="8" class="aside-nav" unique-opened="true" @open="handleOpen" @close="handleClose">
        <el-menu-item index="0">
          <template slot="title">
            <i class="iconfont icon-shouye1"></i>
            <span>首页</span>
          </template>
        </el-menu-item>
        <!--系统基础配置-->
        <el-submenu index="1">
          <template slot="title">
            <i class="iconfont icon-weiwangxiao"></i>
            <span>网校配置</span>
          </template>
          <el-menu-item index="1-1">基础信息配置</el-menu-item>
          <el-menu-item index="1-2">功能配置</el-menu-item>
          <el-menu-item index="1-2">修改日志</el-menu-item>
        </el-submenu>
        <el-menu-item index="12">
          <template slot="title">
            <i class="iconfont icon-zixun"></i>
            <span>资讯管理</span>
          </template>
        </el-menu-item>
        <el-submenu index="998">
          <template slot="title">
            <i class="iconfont icon-xuanzeleixing"></i>
            <span>交易信息配置</span>
          </template>
          <el-menu-item index="1-1">收款账户管理</el-menu-item>
          <el-menu-item index="1-2">报名方式配置</el-menu-item>
        </el-submenu>
        <el-menu-item index="14">
          <template slot="title">
            <i class="iconfont icon-peisong"></i>
            <span>配送渠道配置</span>
          </template>
        </el-menu-item>
        <el-submenu index="999">
          <template slot="title">
            <i class="iconfont icon-yunyingrenyuan"></i>
            <span>运营帐号管理</span>
          </template>
          <el-menu-item index="1-1">平台管理员账号管理</el-menu-item>
          <el-menu-item index="1-2">角色管理</el-menu-item>
          <el-menu-item index="1-3">地区管理员</el-menu-item>
        </el-submenu>
        <!--教学资源管理-->
        <el-menu-item index="4">
          <template slot="title">
            <i class="iconfont icon-kejian"></i>
            <span>课件管理</span>
          </template>
        </el-menu-item>
        <el-menu-item index="5">
          <template slot="title">
            <i class="iconfont icon-xuexi"></i>
            <span>课程管理</span>
          </template>
        </el-menu-item>
        <el-menu-item index="6">
          <template slot="title">
            <i class="iconfont icon-tiku"></i>
            <span>题库管理</span>
          </template>
        </el-menu-item>
        <el-menu-item index="7">
          <template slot="title">
            <i class="iconfont icon-shenqing"></i>
            <span>试题管理</span>
          </template>
        </el-menu-item>
        <el-menu-item index="8">
          <template slot="title">
            <i class="iconfont icon-shijuan"></i>
            <span>试卷管理</span>
          </template>
        </el-menu-item>
        <!--培训管理-->
        <el-menu-item index="9">
          <template slot="title">
            <i class="iconfont icon-dabao"></i>
            <span>课程包管理</span>
          </template>
        </el-menu-item>
        <el-submenu index="10">
          <template slot="title">
            <i class="iconfont icon-fangan"></i>
            <span>培训方案管理</span>
          </template>
          <el-menu-item index="1-1">新建培训方案</el-menu-item>
          <el-menu-item index="1-2">培训方案管理</el-menu-item>
          <el-menu-item index="1-3">培训属性值管理</el-menu-item>
        </el-submenu>
        <el-submenu index="100">
          <template slot="title">
            <i class="iconfont icon-jiaoyi"></i>
            <span>交易管理</span>
          </template>
          <el-submenu index="1-4">
            <template slot="title">订单管理</template>
            <el-menu-item index="1-4-1">个人报名订单</el-menu-item>
            <el-menu-item index="1-4-2">集体报名订单</el-menu-item>
          </el-submenu>
          <el-submenu index="1-5">
            <template slot="title">退款管理</template>
            <el-menu-item index="1-5-1">个人报名退款订单</el-menu-item>
            <el-menu-item index="1-5-2">集体报名退款订单</el-menu-item>
          </el-submenu>
          <el-submenu index="1-6">
            <template slot="title">发票管理</template>
            <el-menu-item index="1-5-1">个人报名发票管理</el-menu-item>
            <el-menu-item index="1-5-2">集体报名发票管理</el-menu-item>
          </el-submenu>
          <el-submenu index="1-7">
            <template slot="title">对账管理</template>
            <el-menu-item index="1-5-1">个人报名对账</el-menu-item>
            <el-menu-item index="1-5-2">集体报名对账</el-menu-item>
          </el-submenu>
          <el-submenu index="1-8">
            <template slot="title">导入开通</template>
            <el-menu-item index="1-5-1">导入学员</el-menu-item>
            <el-menu-item index="1-5-2">导入学员并开班</el-menu-item>
            <el-menu-item index="1-5-2">导入开通结果跟踪</el-menu-item>
          </el-submenu>
          <el-submenu index="1-9">
            <template slot="title">用户管理</template>
            <el-menu-item index="1-5-1">学员管理</el-menu-item>
            <el-menu-item index="1-5-2">集体报名帐号管理</el-menu-item>
          </el-submenu>
          <el-submenu index="1-10">
            <template slot="title">客服管理</template>
            <el-menu-item index="1-5-1">业务咨询</el-menu-item>
            <el-menu-item index="1-5-2">集体报名咨询</el-menu-item>
          </el-submenu>
          <el-menu-item index="1-11">
            <template slot="title">批量打印证明</template>
          </el-menu-item>
          <el-submenu index="1-12">
            <template slot="title">导入导出任务管理</template>
            <el-menu-item index="1-5-1">导入任务管理</el-menu-item>
            <el-menu-item index="1-5-2">导出任务管理</el-menu-item>
          </el-submenu>
        </el-submenu>
        <!--统计报表-->
        <el-menu-item index="13">
          <template slot="title">
            <i class="iconfont icon-ribaotongji"></i>
            <span>方案开通统计</span>
          </template>
        </el-menu-item>
        <el-menu-item index="14">
          <template slot="title">
            <i class="iconfont icon-cptj"></i>
            <span>地区开通统计</span>
          </template>
        </el-menu-item>
        <el-menu-item index="15">
          <template slot="title">
            <i class="iconfont icon-tongjiyuce"></i>
            <span>方案学习统计</span>
          </template>
        </el-menu-item>
        <el-menu-item index="16">
          <template slot="title">
            <i class="iconfont icon-tongjibaobiao"></i>
            <span>地区学习统计</span>
          </template>
        </el-menu-item>
        <el-menu-item index="17">
          <template slot="title">
            <i class="iconfont icon-mingxi"></i>
            <span>学员学习明细</span>
          </template>
        </el-menu-item>
        <!--日志管理-->
        <el-submenu index="18">
          <template slot="title">
            <i class="iconfont icon-zixun"></i>
            <span>日志管理</span>
          </template>
          <el-menu-item index="1-1">登录日志</el-menu-item>
        </el-submenu>
      </el-menu>
      <div class="m-company-info">
        <p>福建华博教育科技股份有限公司</p>
        <a class="a-txt" href="http://beian.miit.gov.cn/" target="_blank">闽ICP备2021002737号</a>
      </div>
    </el-aside>
    <el-container>
      <el-header height="100px" class="f-flex">
        <ul class="header-nav f-flex-sub">
          <li class="nav-item current">
            <i class="iconfont icon-shouye"></i>
            <span class="txt">首页</span>
          </li>
          <li class="nav-item">
            <i class="iconfont icon-peizhi"></i>
            <span class="txt">系统基础配置</span>
          </li>
          <li class="nav-item">
            <i class="iconfont icon-kecheng"></i>
            <span class="txt">教学资源管理</span>
          </li>
          <li class="nav-item">
            <i class="iconfont icon-peixun"></i>
            <span class="txt">培训管理</span>
          </li>
          <li class="nav-item">
            <i class="iconfont icon-shuju"></i>
            <span class="txt">统计报表</span>
          </li>
          <li class="current-bg"></li>
        </ul>
        <ul class="header-nav">
          <li class="nav-item nav-item-1"><i class="iconfont icon-tuichu"></i>退出</li>
        </ul>
      </el-header>
      <div class="tags">
        <span class="prev"><i class="el-icon el-icon-arrow-left"></i></span>
        <div class="tags-bd">
          <div class="tags-items" style="width: 500%;">
            <el-tag class="current" closable>首页</el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
          </div>
        </div>
        <span class="next"><i class="el-icon el-icon-arrow-right"></i></span>
        <el-dropdown :hide-on-click="false">
          <span class="more"><i class="el-icon el-icon-more"></i></span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>黄金糕</el-dropdown-item>
            <el-dropdown-item>狮子头</el-dropdown-item>
            <el-dropdown-item>螺蛳粉</el-dropdown-item>
            <el-dropdown-item disabled>双皮奶</el-dropdown-item>
            <el-dropdown-item divided>蚵仔煎</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <el-main>
        <div class="f-p15">
          <el-card shadow="never" class="m-card f-mb15">
            <!--条件查询-->
            <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
            <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
            <el-row :gutter="16" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-form-item label="登录地址">
                  <el-input v-model="input" clearable placeholder="请输入登录地址" class="u-w300 f-mr20" />
                  <el-button type="primary">查询</el-button>
                  <el-button>重置</el-button>
                </el-form-item>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="用户名称" min-width="300">
                <template>张三</template>
              </el-table-column>
              <el-table-column label="登录IP地址" min-width="300">
                <template>***************</template>
              </el-table-column>
              <el-table-column label="登录成功时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
