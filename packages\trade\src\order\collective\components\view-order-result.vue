<template>
  <div class="pure">
    <!--查看下单结果-->
    <el-drawer
      title="查看下单结果"
      :visible.sync="show"
      size="600px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd">
        <el-form ref="form" label-width="auto" class="m-text-form is-column f-ml10 f-mt10">
          <el-form-item label="集体报名批次号：">{{ data.batchOrderNo }}</el-form-item>
          <el-form-item label="缴费人次：">{{ data.payPersonTime }}</el-form-item>
          <el-form-item label="实付金额（元）：">{{ data.amount }}</el-form-item>
          <el-form-item label="支付方式：">{{ payMode(data.payMode) }}</el-form-item>
          <el-form-item label="下单结果：">
            <el-tag type="success" size="small">
              {{ placeOrderResult(data.placeOrderResult) }}
            </el-tag>
          </el-form-item>
          <el-form-item label="下单时间：">{{ data.placeOrderTime }}</el-form-item>
          <el-form-item label="付款成功时间：">{{ data.paySuccessTime }}</el-form-item>
        </el-form>
        <div class="m-tit f-mt10" v-if="isPlaceOrderComplete">
          <span class="tit-txt">处理结果说明</span>
        </div>
        <div class="f-ml20" v-if="isPlaceOrderComplete">
          当前缴费人次
          <span class="f-co">{{ data.currentBatchPayPersonTime }}</span>
          次，处理成功
          <span class="f-co">{{ data.successPersonTime }}</span>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, PropSync, Vue } from 'vue-property-decorator'
  import BatchOrderListPlaceOrderResultVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderListPlaceOrderResultVo'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import BatchOrderPayMode, {
    BatchOrderPayModeEnum
  } from '@api/service/management/trade/batch/order/enum/BatchOrderPayMode'
  import PlaceBatchOrderResult, {
    PlaceBatchOrderResultEnum
  } from '@api/service/management/trade/batch/order/enum/PlaceBatchOrderResult'

  @Component
  export default class extends Vue {
    /**
     * 是否展示
     */
    @PropSync('visible', {
      type: Boolean
    })
    show!: boolean

    /**
     * 下单结果
     */
    data: BatchOrderListPlaceOrderResultVo = new BatchOrderListPlaceOrderResultVo()

    /**
     * 支付方式列表
     */
    payModeList: EnumOption<BatchOrderPayModeEnum>[] = BatchOrderPayMode.list()

    /**
     * 下单
     */
    PlaceOrderResultList: EnumOption<PlaceBatchOrderResultEnum>[] = PlaceBatchOrderResult.list()

    /**
     * 是否处理完成
     */
    get isPlaceOrderComplete() {
      return this.data.placeOrderResult === PlaceBatchOrderResultEnum.Complete_Process ? true : false
    }

    /**
     * 支付方式
     */
    get payMode() {
      return (mode: BatchOrderPayModeEnum) => {
        const desc = this.payModeList.find((item) => item.code === mode)?.desc || null
        if (desc) {
          if (mode == BatchOrderPayModeEnum.Online_Pay) {
            return `集体报名-${this.data.payTerminal}-${desc}`
          } else {
            return `集体报名-${desc}-线下转账汇款`
          }
        } else {
          return ''
        }
      }
    }

    /**
     * 下单结果
     */
    get placeOrderResult() {
      return (type: PlaceBatchOrderResultEnum) => {
        return this.PlaceOrderResultList.find((item) => item.code === type)?.desc || null
      }
    }
  }
</script>
