<template>
  <el-card shadow="never" class="m-card f-mb15">
    <!--条件查询-->
    <el-row :gutter="16" class="m-query">
      <el-form :inline="true" label-width="auto">
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item label="集体报名批次号">
            <el-input v-model="pageQueryParam.orderNoList" clearable placeholder="请输入集体报名批次号" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item label="开票状态">
            <el-select v-model="pageQueryParam.invoiceStatusList" clearable placeholder="请选择发票状态">
              <el-option v-for="item in invoiceStatus" :label="item.name" :value="item.value" :key="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item>
            <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <!--表格-->
    <el-table
      stripe
      :data="pageData"
      v-loading="query.loading"
      ref="specialInvoiceTable"
      max-height="500px"
      class="m-table"
    >
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="集体报名批次号" prop="associationId" min-width="220" fixed="left"></el-table-column>
      <el-table-column label="退款状态" min-width="130">
        <template slot-scope="scope">
          <el-badge
            is-dot
            :type="refundStatusMapType[scope.row.orderReturnStatus]"
            class="badge-status"
            v-if="refundStatusMapName[scope.row.orderReturnStatus]"
          >
            {{ refundStatusMapName[scope.row.orderReturnStatus] }}
          </el-badge>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="付款金额(元)" width="140" align="right">
        <template slot-scope="scope">
          {{ scope.row.payAmount || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="开票金额(元)" width="140" align="right">
        <template slot-scope="scope">
          {{ scope.row.totalAmount || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="配送方式" width="120" align="center">
        <template slot-scope="scope">
          <el-popover placement="right" width="600" trigger="hover" popper-class="m-popover">
            <el-tag
              type="success"
              class="f-mr5"
              effect="dark"
              v-if="scope.row.deliveryInfo.deliveryStatus === 3 && scope.row.deliveryInfo.shippingMethod === 2"
              >已配送
            </el-tag>
            <el-tag
              type="success"
              class="f-mr5"
              effect="dark"
              v-if="scope.row.deliveryInfo.deliveryStatus === 2 && scope.row.deliveryInfo.shippingMethod === 1"
              >已自取
            </el-tag>
            <el-tag
              type="primary"
              class="f-mr5"
              effect="dark"
              v-if="scope.row.deliveryInfo.deliveryStatus === 1 && scope.row.deliveryInfo.shippingMethod === 2"
              >已就绪
            </el-tag>
            <el-tag type="info" class="f-mr5" effect="dark" v-if="scope.row.deliveryInfo.deliveryStatus === 0"
              >{{ scope.row.deliveryInfo.shippingMethod === 2 ? '未就绪' : '未自取' }}
            </el-tag>
            <el-form
              v-if="scope.row.deliveryInfo.shippingMethod === 2 && scope.row.deliveryInfo.deliveryStatus === 3"
              :inline="true"
              label-width="85px"
              class="m-text-form is-border-bottom f-mt15"
            >
              <el-col :span="24">
                <el-form-item label="快递公司：">{{ scope.row.deliveryInfo.express.expressCompanyName }}</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="运单号：">
                  {{ scope.row.deliveryInfo.express.expressNo }}
                  <a
                    @click="jumpExpress(scope.row.deliveryInfo.express.expressNo)"
                    class="f-cb f-link f-underline f-ml10"
                    >复制运单号并查询</a
                  >
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="发货时间："
                  >{{ scope.row.deliveryInfo.deliveryStatusChangeTime.shipped }}
                </el-form-item>
              </el-col>
            </el-form>
            <el-form
              :inline="true"
              label-width="85px"
              class="m-text-form is-border-bottom f-mt15"
              v-if="scope.row.deliveryInfo.shippingMethod === 1 && scope.row.deliveryInfo.deliveryStatus === 2"
            >
              <el-col :span="24">
                <el-form-item label="领取人：">{{ scope.row.deliveryInfo.takeResult.takePerson }}</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="手机号：">{{ scope.row.deliveryInfo.takeResult.phone }}</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="发货时间："
                  >{{ scope.row.deliveryInfo.deliveryStatusChangeTime.taken }}
                </el-form-item>
              </el-col>
            </el-form>
            <el-form :inline="true" label-width="85px" class="m-text-form f-mt15">
              <el-col :span="24">
                <el-form-item label="收货地址：" v-if="scope.row.deliveryInfo.shippingMethod === 2"
                  >{{ scope.row.deliveryInfo.deliveryAddress && scope.row.deliveryInfo.deliveryAddress.address }}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="收货人："
                  >{{ scope.row.deliveryInfo.deliveryAddress && scope.row.deliveryInfo.deliveryAddress.consignee }}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="手机号码："
                  >{{ scope.row.deliveryInfo.deliveryAddress && scope.row.deliveryInfo.deliveryAddress.phone }}
                </el-form-item>
              </el-col>
              <el-col :span="24" v-if="scope.row.deliveryInfo.shippingMethod === 1">
                <el-form-item label="自取地址："
                  >{{ scope.row.deliveryInfo.deliveryAddress && scope.row.deliveryInfo.takePoint.pickupLocation }}
                </el-form-item>
              </el-col>
            </el-form>
            <span slot="reference" class="f-link f-cb">{{
              scope.row.deliveryInfo.shippingMethod === 2 ? '邮寄' : '自取'
            }}</span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="购买人信息" min-width="240">
        <template slot-scope="scope">
          <p>姓名：{{ scope.row.name || '-' }}</p>
          <p>证件号：{{ scope.row.idCard || '-' }}</p>
          <p>手机号：{{ scope.row.phone || '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="发票抬头" min-width="300">
        <template slot-scope="scope">【单位】{{ scope.row.title }}</template>
      </el-table-column>
      <el-table-column label="统一社会信用代码" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.taxpayerNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="增票信息" min-width="360">
        el-table-column label="增票信息" min-width="360">
        <template slot-scope="scope">
          <p class="f-flex">
            <span>银行账户：</span>
            <span class="f-flex-sub">{{ scope.row.account || '-' }}</span>
          </p>
          <p class="f-flex">
            <span>开户银行：</span>
            <span class="f-flex-sub">{{ scope.row.bankName || '-' }}</span>
          </p>
        </template>
      </el-table-column>
      <el-table-column label="申请开票时间" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.applyForDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="发票状态" min-width="130">
        <template slot-scope="scope">
          <el-badge
            is-dot
            :type="invoiceStatusMapType[scope.row.invoiceStatus]"
            class="badge-status"
            v-if="invoiceStatusMapName[scope.row.invoiceStatus]"
          >
            {{ invoiceStatusMapName[scope.row.invoiceStatus] }}
          </el-badge>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="开票时间" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.invoiceDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="发票号" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.invoiceNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            v-if="(scope.row.invoiceStatus === 0 || scope.row.invoiceStatus === 3) && !scope.row.invoiceFreezeStatus"
            @click="editInvoice(scope.row.invoiceId)"
            >修改发票信息
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <edit-special-invoice-dialog
      :dialog-ctrl.sync="editInvoiceVisible"
      :invoice-id="invoiceId"
      @callBack="doSearch"
    ></edit-special-invoice-dialog>
  </el-card>
</template>
<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import {
    InvoiceStatusEnum,
    OrderReturnStatusEnum,
    TitleTypeEnum
  } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import {
    BillStatusChangeTimeRequest,
    DateScopeRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import EditSpecialInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/edit-special-invoice-dialog.vue'
  import QueryOffLinePageInvoiceParam from '@api/service/management/trade/batch/invoice/query/vo/QueryOffLinePageInvoiceParam'
  import {
    DeliveryStatusEnum,
    DeliveryWayEnum
  } from '@api/service/management/trade/single/invoice/enum/DeliveryInvoiceEnum'
  import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { handleClipboard } from '@hbfe/jxjy-admin-common/src/clipboard'
  import DealInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/deal-invoice-dialog.vue'
  import CancelSpecialInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/cancel-special-invoice-dialog.vue'
  import ImportInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/import-invoice-dialog.vue'
  import InvoiceLogDialog from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/invoice-log-dialog.vue'
  import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
  import { bind, debounce } from 'lodash-decorators'
  import QueryOffLineInvoice from '@api/service/management/trade/batch/invoice/query/QueryOffLineInvoice'

  @Component({
    components: {
      DoubleDatePicker,
      EditSpecialInvoiceDialog,
      DealInvoiceDialog,
      CancelSpecialInvoiceDialog,
      ImportInvoiceDialog,
      InvoiceLogDialog
    }
  })
  export default class extends Vue {
    @Ref('specialInvoiceTable') specialInvoiceTable: any

    @Prop({
      type: String,
      default: ''
    })
    userId: string

    @Watch('userId')
    async userIdChange(id: string) {
      await this.doSearch()
    }

    pageQueryParam: QueryOffLinePageInvoiceParam = new QueryOffLinePageInvoiceParam()
    exportQueryParam: QueryOffLinePageInvoiceParam = new QueryOffLinePageInvoiceParam()
    select = ''
    input = ''
    pageData: Array<OffLinePageInvoiceVo> = new Array<OffLinePageInvoiceVo>()
    page: UiPage
    query: Query = new Query()
    hasSelectSchemeMode = new Array<HasSelectSchemeMode>()

    //处理发票弹窗
    dealInvoiceVisible = false
    //作废发票弹窗
    cancelInvoiceVisible = false
    //修改发票弹窗
    editInvoiceVisible = false
    //导入电子发票弹窗
    importInvoiceVisible = false
    //导入成功弹窗
    importSuccessVisible = false
    //导出成功弹窗
    exportSuccessVisible = false
    //操作日志弹窗
    logDialog = false

    invoiceId = ''
    //当前选中作发票
    curInvoiceItem: OffLinePageInvoiceVo = new OffLinePageInvoiceVo()

    regionNameList: Array<string> = new Array<string>()

    //配送方式
    dispatchMethod = [
      {
        name: '请选择配送方式',
        value: null
      },
      {
        name: '邮寄',
        value: DeliveryWayEnum.COURIER
      },
      {
        name: '自取',
        value: DeliveryWayEnum.SELFFETCHED
      }
    ]

    //发票状态
    invoiceStatus = [
      {
        name: '请选择发票状态',
        value: null
      },
      {
        name: '待开票',
        value: InvoiceStatusEnum.NOTPTOOPEN
      },
      {
        name: '开票中',
        value: InvoiceStatusEnum.OPENING
      },
      {
        name: '开票成功',
        value: InvoiceStatusEnum.OPEMSUCCESS
      },
      {
        name: '开票失败',
        value: InvoiceStatusEnum.OPENERROR
      }
    ]

    invoiceStatusMapName = {
      [InvoiceStatusEnum.NOTPTOOPEN]: '待开票',
      [InvoiceStatusEnum.OPENING]: '开票中',
      [InvoiceStatusEnum.OPENERROR]: '开票失败',
      [InvoiceStatusEnum.OPEMSUCCESS]: '开票成功'
    }

    invoiceStatusMapType = {
      [InvoiceStatusEnum.NOTPTOOPEN]: 'info',
      [InvoiceStatusEnum.OPENING]: 'primary',
      [InvoiceStatusEnum.OPENERROR]: 'danger',
      [InvoiceStatusEnum.OPEMSUCCESS]: 'success'
    }

    refundStatusMapName = {
      [OrderReturnStatusEnum.RETURNSING]: '退款中',
      [OrderReturnStatusEnum.RETURNSUCCESS]: '退款成功'
    }

    refundStatusMapType = {
      [OrderReturnStatusEnum.RETURNSING]: 'primary',
      [OrderReturnStatusEnum.RETURNSUCCESS]: 'success'
    }

    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }

    // 配送状态
    deliveryStatusMapType = {
      [DeliveryStatusEnum.NOTREADY]: 'info',
      [DeliveryStatusEnum.READY]: 'info',
      [DeliveryStatusEnum.SELFFETCHED]: 'success',
      [DeliveryStatusEnum.DELIVERED]: 'primary'
    }

    constructor() {
      super()
      this.pageQueryParam.billStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.billStatusChangeTime.billing = new DateScopeRequest()
      this.pageQueryParam.billStatusChangeTime.success = new DateScopeRequest()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    //修改发票信息
    editInvoice(id: string) {
      this.invoiceId = id
      this.editInvoiceVisible = true
    }

    async doSearch() {
      if (!this.userId) {
        this.pageData = []
        return
      }
      this.query.loading = true
      if (this.hasSelectSchemeMode.length) {
        this.pageQueryParam.commoditySkuIdList = this.hasSelectSchemeMode[0].schemeId
      } else {
        this.pageQueryParam.commoditySkuIdList = null
      }
      const queryOffLineInvoice = new QueryOffLineInvoice()
      this.pageQueryParam.userId = this.userId
      try {
        const result = await queryOffLineInvoice.offLinePageVatspecialplaInvoiceInServicer(
          this.page,
          this.pageQueryParam
        )
        this.pageData = result
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['specialInvoiceTable'] as any)?.doLayout()
        this.query.loading = false
        this.exportQueryParam = Object.assign(new QueryOffLinePageInvoiceParam(), this.pageQueryParam)
        this.getBusinessRegionList()
      }
    }

    async pollingSearch() {
      if (!this.userId) {
        this.pageData = []
        return
      }
      this.query.loading = true
      if (this.hasSelectSchemeMode.length) {
        this.pageQueryParam.commoditySkuIdList = this.hasSelectSchemeMode[0].schemeId
      } else {
        this.pageQueryParam.commoditySkuIdList = null
      }
      const queryOffLineInvoice = new QueryOffLineInvoice()
      this.pageQueryParam.userId = this.userId
      try {
        const origin = this.pageData.slice()

        const result = await queryOffLineInvoice.offLinePageVatspecialplaInvoiceInServicer(
          this.page,
          this.pageQueryParam
        )
        this.pageData = [...origin, ...result]
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['specialInvoiceTable'] as any)?.doLayout()
        this.query.loading = false
        this.exportQueryParam = Object.assign(new QueryOffLinePageInvoiceParam(), this.pageQueryParam)
        this.getBusinessRegionList()
      }
    }

    async resetCondition() {
      this.page.pageNo = 1
      this.pageQueryParam = new QueryOffLinePageInvoiceParam()
      this.pageQueryParam.billStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.billStatusChangeTime.billing = new DateScopeRequest()
      this.pageQueryParam.billStatusChangeTime.success = new DateScopeRequest()
      //this.pageQueryParam.commoditySkuIdList = undefined
      this.hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
      /*this.pageQueryParam.orderNoList = undefined
      this.pageQueryParam.userName = undefined
      this.pageQueryParam.idCard = undefined
      this.pageQueryParam.phone = undefined
      this.pageQueryParam.invoiceStatusList = undefined
      this.pageQueryParam.invoiceFreezeStatus = undefined
      this.pageQueryParam.invoiceNoList = undefined
      this.pageQueryParam.shippingMethodList = undefined
      this.pageQueryParam.billStatusChangeTime.billing.begin = undefined
      this.pageQueryParam.billStatusChangeTime.billing.end = undefined
      this.pageQueryParam.billStatusChangeTime.success.begin = undefined
      this.pageQueryParam.billStatusChangeTime.success.end = undefined*/
      await this.doSearch()
    }

    //处理发票
    dealInvoice(id: string) {
      this.invoiceId = id
      this.dealInvoiceVisible = true
    }

    //作废发票
    cancelInvoice(item: OffLinePageInvoiceVo) {
      this.curInvoiceItem = item
      this.cancelInvoiceVisible = true
    }

    // 下载导入数据
    goImportDownloadPage() {
      this.importSuccessVisible = false
      this.$router.push('/training/task/import')
    }

    // 导出列表数据
    async exportSpecialInvoice() {
      try {
        const queryOffLineInvoice = new QueryOffLineInvoice()
        const res = await queryOffLineInvoice.offLinePageVatspecialplaInvoiceInExport(this.exportQueryParam)
        if (res) {
          this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }

    // 下载导出数据
    goExportDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/export',
        query: { type: 'exportBatchVatSpecialOfflineInvoice' }
      })
    }

    importElectronicInvoice() {
      //导入电子发票
      this.importInvoiceVisible = true
    }

    //发票操作记录
    invoiceLog() {
      this.logDialog = true
    }

    // 注释===========================
    //获取所在地区中文
    async getBusinessRegion(regionCode: string, index: number) {
      const region = ''
      if (regionCode) {
        let regionArr: Array<string> = []
        if (regionCode.substring(0, 1) === '/') {
          regionArr = regionCode.substring(1, regionCode.length).split('/')
        } else {
          regionArr = regionCode.split('/')
        }
        // await QueryPhysicalRegion.queryPhysicalRegionByIdList(regionArr)
        // region = QueryPhysicalRegion.getNameByRegionPath(regionCode)
        this.$set(this.regionNameList, index, region)
        this.regionNameList[index] = region
      } else {
        this.$set(this.regionNameList, index, '')
        this.regionNameList[index] = ''
      }
    }

    getBusinessRegionList() {
      this.regionNameList = []
      this.pageData.forEach((item, index) => {
        this.getBusinessRegion(item.deliveryRegion, index)
      })
    }

    copyAndGoSearch(content: string, event: MouseEvent) {
      handleClipboard(content, event)
      window.open('https://www.kuaidi100.com/', '_blank')
    }

    async created() {
      // 给表格内滚动条滚动增加监听事件
      await this.$nextTick(async () => {
        const element = this.specialInvoiceTable.bodyWrapper
        element.addEventListener('scroll', this.infiniteScroll)
      })
      await this.doSearch()
    }

    /**
     * 【换班记录】无限加载
     */
    @bind
    @debounce(200)
    async infiniteScroll() {
      const element = this.specialInvoiceTable.bodyWrapper
      const scrollDistance = element.scrollHeight - element.scrollTop - element.clientHeight
      console.log('scrollInfo', scrollDistance, element.scrollHeight, element.scrollTop, element.clientHeight)
      if (scrollDistance <= 0) {
        if (this.pageData.length >= this.page.totalSize) {
          // this.$message.warning('没有更多数据')
        } else {
          this.page.pageNo++
          await this.pollingSearch()
        }
      }
    }

    jumpExpress(expressNo: string) {
      if (expressNo) {
        const textArea = document.createElement('textarea')
        textArea.value = expressNo
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        window.open('https://www.kuaidi100.com/', '_blank')
      }
    }
  }
</script>
