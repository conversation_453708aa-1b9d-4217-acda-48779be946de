import Property from '@api/service/common/scheme/model/Property'
import RegionProperty from '@api/service/common/scheme/model/RegionProperty'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * @description 方案sku培训属性
 */
class SkuProperty {
  /**
   * 年度
   */
  year = new Property()
  /**
   * 地区
   */
  region = new RegionProperty()
  /**
   * 行业
   */
  industry = new Property()
  /**
   * 科目类型
   */
  subjectType = new Property()
  /**
   * 技术等级
   */
  technicalGrade = new Property()
  /**
   * 培训类别
   */
  trainingCategory = new Property()
  /**
   * 培训专业
   */
  trainingMajor = new Property()
  /**
   * 培训对象
   */
  trainingObject = new Property()
  /**
   * 岗位类别
   */
  positionCategory = new Property()
  /**
   * 技术等级
   */
  jobLevel = new Property()
  /**
   * 学段
   */
  learningPhase = new Property()
  /**
   * 学科
   */
  discipline = new Property()
  /**
   * 证书类型
   */
  certificatesType = new Property()
  /**
   * 执业类别
   */
  practitionerCategory = new Property()
  /**
   * 主/增项
   */
  mainOrAdditionalItem = new Property()
  /**
   * 培训形式
   */
  trainingMode = new Property<TrainingModeEnum>()
}

export default SkuProperty
