<template>
  <div>
    <el-form ref="form" :model="mutationRegisterAndLogin.registerSetting" label-width="auto" class="m-form">
      <!--注册设置-->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <el-row type="flex" justify="center" class="f-plr20 f-pt40">
          <el-col :lg="20" :xl="17">
            <el-form-item label="开放学员注册入口：" required>
              <el-switch
                v-model="mutationRegisterAndLogin.registerSetting.enable"
                active-text="开启"
                inactive-text="关闭"
                class="m-switch"
              />
            </el-form-item>
            <el-col :lg="20" :xl="17">
              <el-form-item label="工作单位对接天眼查/企查查：" required>
                <el-switch
                  v-model="mutationRegisterAndLogin.registerSetting.unitRegister.unitConnectQuery"
                  active-text="开启"
                  inactive-text="关闭"
                  class="m-switch"
                />
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-question m-tooltip-icon f-co f-ml10"></i>
                  <div slot="content">
                    如网校采集学员的工作单位字段需对接企查查/天眼查。需在工作单位配置信息中，选择需要对接的服务商类型与账号信息。<br />
                    该功能生效与学员端账号信息管理、运营端的导入 和业务咨询功能。
                  </div>
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-col>
        </el-row>
        <div class="m-tit is-small is-border-bottom" v-if="mutationRegisterAndLogin.registerSetting.enable">
          <span class="tit-txt">注册基础信息</span>
        </div>
        <el-row type="flex" justify="center" class="f-p20" v-if="mutationRegisterAndLogin.registerSetting.enable">
          <el-col :lg="20" :xl="17">
            <el-form-item>
              <div slot="label" class="f-mt20">注册字段：</div>
              <el-row class="m-function-set" type="flex">
                <!-- <el-col :span="8" class="item">
                  <div class="name">
                    姓名
                  </div>
                  <el-switch
                    v-model="mutationRegisterAndLogin.registerSetting.studentRegister.name.isRequire"
                    active-text="必填"
                    inactive-text="非必填"
                    disabled
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">证件号</div>
                  <el-switch
                    v-model="mutationRegisterAndLogin.registerSetting.studentRegister.idCard.isRequire"
                    active-text="必填"
                    inactive-text="非必填"
                    disabled
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">密码</div>
                  <el-switch
                    v-model="mutationRegisterAndLogin.registerSetting.studentRegister.password.isRequire"
                    active-text="必填"
                    inactive-text="非必填"
                    disabled
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">手机号</div>
                  <el-switch
                    v-model="mutationRegisterAndLogin.registerSetting.studentRegister.phone.isRequire"
                    active-text="必填"
                    inactive-text="非必填"
                    disabled
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">单位所在地区</div>
                  <el-switch
                    v-model="mutationRegisterAndLogin.registerSetting.studentRegister.area.isRequire"
                    active-text="必填"
                    inactive-text="非必填"
                    disabled
                  />
                </el-col> -->
                <el-col :span="8" class="item">
                  <div class="name">姓名</div>
                  <span class="f-cb">必填</span>
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">证件类型</div>
                  <span class="f-cb">必填</span>
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">证件号</div>
                  <span class="f-cb">必填</span>
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">密码</div>
                  <span class="f-cb">必填</span>
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">手机号</div>
                  <span class="f-cb">必填</span>
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">单位所在地区</div>
                  <span class="f-cb">必填</span>
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">工作单位</div>
                  <el-switch
                    v-model="mutationRegisterAndLogin.registerSetting.studentRegister.companyName.isRequire"
                    active-text="必填"
                    inactive-text="非必填"
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">性别</div>
                  <el-switch
                    v-model="mutationRegisterAndLogin.registerSetting.studentRegister.gender.isRequire"
                    active-text="必填"
                    inactive-text="非必填"
                  />
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="证件类型信息：">
              <el-checkbox
                :label="item.select"
                name="type"
                v-for="item in mutationRegisterAndLogin.registerSetting.studentRegister.idCardType"
                :key="item.idCardTypeValue"
                v-model="item.select"
                >{{ item.name }}</el-checkbox
              >
              <!-- </el-checkbox-group> -->
            </el-form-item>
          </el-col>
        </el-row>

        <div
          class="m-tit is-small is-border-bottom"
          v-if="mutationRegisterAndLogin.registerSetting.unitRegister.unitConnectQuery"
        >
          <span class="tit-txt">工作单位配置信息</span>
        </div>
        <el-row
          type="flex"
          justify="center"
          class="f-p20"
          v-if="mutationRegisterAndLogin.registerSetting.unitRegister.unitConnectQuery"
        >
          <el-col :lg="20" :xl="17">
            <el-form-item label="服务商类型：" required>
              <el-checkbox-group v-model="mutationRegisterAndLogin.registerSetting.unitRegister.serviceType">
                <el-checkbox :label="unitSearchServiceTypeEnum.tySearch">天眼查</el-checkbox>
                <el-checkbox :label="unitSearchServiceTypeEnum.qccSearch"
                  >企查查
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co"></i>
                    <div slot="content">
                      <p>
                        请选择工作单位开放对接外部接口的服务商类型，如同时选择了天眼查和企查查，系统默认先查询天眼查再查询企查查。
                      </p>
                    </div>
                  </el-tooltip>
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item
              label="天眼查账号信息："
              required
              v-if="
                mutationRegisterAndLogin.registerSetting.unitRegister.serviceType.includes(
                  unitSearchServiceTypeEnum.tySearch
                )
              "
            >
              <el-input
                v-model="mutationRegisterAndLogin.registerSetting.unitRegister.tyAccountToken"
                clearable
                placeholder="请输入授权token"
                class="form-l"
              />
            </el-form-item>
            <el-form-item
              label="企查查账号信息："
              required
              v-if="
                mutationRegisterAndLogin.registerSetting.unitRegister.serviceType.includes(
                  unitSearchServiceTypeEnum.qccSearch
                )
              "
            >
              <el-input
                v-model="mutationRegisterAndLogin.registerSetting.unitRegister.qccAccountToken"
                clearable
                placeholder="请输入授权token"
                class="form-l"
              />
            </el-form-item>
            <el-form-item
              label="企查查密钥信息："
              required
              v-if="
                mutationRegisterAndLogin.registerSetting.unitRegister.serviceType.includes(
                  unitSearchServiceTypeEnum.qccSearch
                )
              "
            >
              <el-input
                v-model="mutationRegisterAndLogin.registerSetting.unitRegister.qccKey"
                clearable
                placeholder="请输入密钥"
                class="form-l"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <div class="m-tit is-small is-border-bottom" v-if="mutationRegisterAndLogin.registerSetting.enable">
          <span class="tit-txt">行业信息</span>
          <el-button type="primary" size="small" class="f-ml20" @click="detailDialog = true">查看详情</el-button>
        </div>
        <el-row type="flex" justify="center" class="f-p20" v-if="mutationRegisterAndLogin.registerSetting.enable">
          <el-col :lg="20" :xl="17">
            <el-form-item label="行业：">
              <el-checkbox-group v-model="industryNameList">
                <el-checkbox v-for="item in industryList" :key="item.id" :label="item.name" name="type" disabled>{{
                  item.name
                }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <!--人社行业-->
            <el-form-item v-if="hasSociety">
              <div slot="label" class="f-mt20">人社行业信息：</div>
              <el-row class="m-function-set" type="flex">
                <!-- <el-col :span="8" class="item">
                  <div class="name">专业类别</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.personIndustryRegister.professionalQualification
                        .isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                    disabled
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">职称等级</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.personIndustryRegister.firstProfessionalCategory
                        .isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                    disabled
                  />
                </el-col> -->
                <el-col :span="8" class="item">
                  <div class="name">专业类别</div>
                  <span class="f-cb">必填</span>
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">职称等级</div>
                  <span class="f-cb">必填</span>
                </el-col>
              </el-row>
            </el-form-item>
            <!--建设行业-->
            <el-form-item v-if="hasBuild">
              <div slot="label" class="f-mt20">建设行业信息：</div>
              <el-row class="m-function-set" type="flex">
                <!-- <el-col :span="8" class="item">
                  <div class="name">证书类别</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.constructionIndustryRegister.certificateCategory
                        .isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                    disabled
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">注册专业</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.constructionIndustryRegister.registerProfessional
                        .isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                    disabled
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">证书编号</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.constructionIndustryRegister.certificateNo.isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                    disabled
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">证书发证日期</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.constructionIndustryRegister.releaseStartTime.isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                    disabled
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">证书有效期</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.constructionIndustryRegister.certificateEndTime.isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                    disabled
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">证书附件</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.constructionIndustryRegister.certificateAttachments
                        .isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                  />
                </el-col> -->
                <el-col :span="8" class="item">
                  <div class="name">证书类别</div>
                  <span class="f-cb">必填</span>
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">注册专业</div>
                  <span class="f-cb">必填</span>
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">证书编号</div>
                  <span class="f-cb">必填</span>
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">证书发证日期</div>
                  <span class="f-cb">必填</span>
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">证书有效期</div>
                  <span class="f-cb">必填</span>
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">证书附件</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.constructionIndustryRegister.certificateAttachments
                        .isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                  />
                </el-col>
              </el-row>
            </el-form-item>

            <!--职业卫生行业-->
            <el-form-item v-if="hasHygiene">
              <div slot="label" class="f-mt20">职业卫生行业信息：</div>
              <el-row class="m-function-set" type="flex">
                <el-col :span="8" class="item">
                  <div class="name">人员类别</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.occupationalHealthIndustryRegister.personnelCategory
                        .isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">岗位类别</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.occupationalHealthIndustryRegister.positionCategory
                        .isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                  />
                </el-col>
              </el-row>
            </el-form-item>
            <!--工勤行业-->
            <el-form-item v-if="hasWorkService">
              <div slot="label" class="f-mt20">工勤行业信息：</div>
              <el-row class="m-function-set" type="flex">
                <el-col :span="8" class="item">
                  <div class="name">技术等级</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.workServiceIndustryRegister.professionalLevel.isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">工种</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.workServiceIndustryRegister.jobCategoryId.isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                  />
                </el-col>
              </el-row>
            </el-form-item>

            <!-- 教师行业 -->
            <el-form-item v-if="hasTeacher">
              <div slot="label" class="f-mt20">教师行业信息：</div>
              <el-row class="m-function-set" type="flex">
                <el-col :span="8" class="item">
                  <div class="name">学段</div>
                  <el-switch
                    v-model="mutationRegisterAndLogin.registerSetting.teacherIndustryRegister.section.isRequire"
                    active-text="必填"
                    inactive-text="非必填"
                    @change="changeSection"
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">学科</div>
                  <el-switch
                    v-model="mutationRegisterAndLogin.registerSetting.teacherIndustryRegister.subjects.isRequire"
                    active-text="必填"
                    inactive-text="非必填"
                    @change="changeSubject"
                  />
                </el-col>
              </el-row>
            </el-form-item>

            <!-- 药师行业 -->
            <el-form-item v-if="hasMedicine">
              <div slot="label" class="f-mt20">药师行业信息：</div>
              <el-row class="m-function-set" type="flex">
                <el-col :span="8" class="item">
                  <div class="name">证书类型</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.pharmacistIndustryRegister.certificatesType.isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                    @change="changeCertificateType"
                  />
                </el-col>
                <el-col :span="8" class="item">
                  <div class="name">执业类别</div>
                  <el-switch
                    v-model="
                      mutationRegisterAndLogin.registerSetting.pharmacistIndustryRegister.practitionerCategory.isRequire
                    "
                    active-text="必填"
                    inactive-text="非必填"
                    @change="changeProfessionCategory"
                  />
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div class="m-btn-bar f-tc is-sticky-1">
      <el-button @click="doCancel">取消</el-button>
      <el-button type="primary" @click="doSave">保存</el-button>
    </div>
    <give-up-dialog :give-up-model="uiConfig.giveUpModel" @callBack="resetData"></give-up-dialog>
    <industry-detail :detail-dialog.sync="detailDialog"></industry-detail>
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import IndustryDetail from '@hbfe/jxjy-admin-platform/src/function/components/industry-detail.vue'
  import MutationRegisterAndLogin, {
    RegisterSettingEnum
  } from '@api/service/management/online-school-config/functionality-setting/mutation/MutationRegisterAndLogin'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
  import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
  import { IdentityCardType } from '@api/service/management/online-school-config/functionality-setting/enum/IdentityCardTypeEnum'
  import { UnitSearchServiceTypeEnum } from '@api/service/management/online-school-config/functionality-setting/enum/UnitSearchServiceType'

  @Component({
    components: { GiveUpDialog, IndustryDetail }
  })
  export default class extends Vue {
    unitSearchServiceTypeEnum = UnitSearchServiceTypeEnum

    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }

    activeName2 = 'first'

    //证件类型枚举
    identityCardTypeEnum = IdentityCardType

    //行业列表
    industryList: Array<IndustryVo> = new Array<IndustryVo>()

    industryNameList: Array<string> = new Array<string>()

    //详情弹窗
    detailDialog = false

    //是否有人社行业
    hasSociety = false
    //是否有建筑行业
    hasBuild = false
    //是否有工勤行业
    hasWorkService = false
    //是否有职业卫生行业
    hasHygiene = false
    // 是否有教师行业
    hasTeacher = false
    // 是否有药师行业
    hasMedicine = false

    mutationRegisterAndLogin: MutationRegisterAndLogin =
      OnlineSchoolConfigModule.mutationFunctionalitySettingFactory.registerAndLogin
    form = {
      delivery1: '',
      resource: '',
      name: ''
    }

    get profession() {
      const professionList: any = []
      if (this.industryList.length) {
        this.industryList.forEach((item) => {
          // professionList.push(item)
          if (item.id === IndustryIdEnum.WS) {
            professionList.push(RegisterSettingEnum.HYGIENE)
          }
          if (item.id === IndustryIdEnum.GQ) {
            professionList.push(RegisterSettingEnum.DILIGENCE)
          }
          if (item.id === IndustryIdEnum.RS) {
            professionList.push(RegisterSettingEnum.PERSON)
          }
          if (item.id === IndustryIdEnum.JS) {
            professionList.push(RegisterSettingEnum.CONSTRUCT)
          }
          if (item.id === IndustryIdEnum.LS) {
            professionList.push(RegisterSettingEnum.TEACHER)
          }
          if (item.id === IndustryIdEnum.YS) {
            professionList.push(RegisterSettingEnum.PHARMACIST)
          }
        })
      }
      return professionList
    }
    // 根据学段是否必填判断学科是否必填
    changeSection() {
      if (this.mutationRegisterAndLogin.registerSetting.teacherIndustryRegister.section.isRequire == false) {
        this.mutationRegisterAndLogin.registerSetting.teacherIndustryRegister.subjects.isRequire = false
      }
    }
    // 根据学科是否必填判断学段是否必填
    changeSubject() {
      if (this.mutationRegisterAndLogin.registerSetting.teacherIndustryRegister.subjects.isRequire == true) {
        this.mutationRegisterAndLogin.registerSetting.teacherIndustryRegister.section.isRequire = true
      }
    }

    changeCertificateType() {
      if (
        this.mutationRegisterAndLogin.registerSetting.pharmacistIndustryRegister.certificatesType.isRequire == false
      ) {
        this.mutationRegisterAndLogin.registerSetting.pharmacistIndustryRegister.practitionerCategory.isRequire = false
      }
    }

    // 执业类别配置为必填，证书类型为必填
    changeProfessionCategory() {
      if (
        this.mutationRegisterAndLogin.registerSetting.pharmacistIndustryRegister.practitionerCategory.isRequire == true
      ) {
        this.mutationRegisterAndLogin.registerSetting.pharmacistIndustryRegister.certificatesType.isRequire = true
      }
    }

    //保存
    async doSave() {
      //         {
      //   'PERSON' = 1,
      //   'CONSTRUCT' = 2,
      //   'BOTH' = 3
      // }
      const isSelect = this.mutationRegisterAndLogin.registerSetting.studentRegister.idCardType.some(
        (item) => item.select
      )
      if (!isSelect) {
        return this.$alert('请至少选择一种证件类型。', '提示', {
          confirmButtonText: '我知道了',
          type: 'warning'
        })
      }

      //   工作单位配置信息校验
      if (
        this.mutationRegisterAndLogin.registerSetting.unitRegister.unitConnectQuery &&
        !this.mutationRegisterAndLogin.registerSetting.unitRegister.serviceType.includes(
          this.unitSearchServiceTypeEnum.tySearch
        ) &&
        !this.mutationRegisterAndLogin.registerSetting.unitRegister.serviceType.includes(
          this.unitSearchServiceTypeEnum.qccSearch
        )
      ) {
        return this.$alert('单位对接天眼查/企查查，需要勾选一个服务商', '提示', {
          confirmButtonText: '我知道了',
          type: 'warning'
        })
      }

      // 天眼查账号
      if (
        this.mutationRegisterAndLogin.registerSetting.unitRegister.unitConnectQuery &&
        !this.mutationRegisterAndLogin.registerSetting.unitRegister.tyAccountToken &&
        this.mutationRegisterAndLogin.registerSetting.unitRegister.serviceType.includes(
          this.unitSearchServiceTypeEnum.tySearch
        )
      ) {
        return this.$alert('请输入天眼查账号信息', '提示', {
          confirmButtonText: '我知道了',
          type: 'warning'
        })
      }

      //   企查查账号
      if (
        this.mutationRegisterAndLogin.registerSetting.unitRegister.unitConnectQuery &&
        !this.mutationRegisterAndLogin.registerSetting.unitRegister.qccAccountToken &&
        this.mutationRegisterAndLogin.registerSetting.unitRegister.serviceType.includes(
          this.unitSearchServiceTypeEnum.qccSearch
        )
      ) {
        return this.$alert('请输入企查查账号信息', '提示', {
          confirmButtonText: '我知道了',
          type: 'warning'
        })
      }

      //企查查秘钥信息
      if (
        this.mutationRegisterAndLogin.registerSetting.unitRegister.unitConnectQuery &&
        !this.mutationRegisterAndLogin.registerSetting.unitRegister.qccKey &&
        this.mutationRegisterAndLogin.registerSetting.unitRegister.serviceType.includes(
          this.unitSearchServiceTypeEnum.qccSearch
        )
      ) {
        return this.$alert('请输入企查查密钥', '提示', {
          confirmButtonText: '我知道了',
          type: 'warning'
        })
      }

      const res = await this.mutationRegisterAndLogin.saveRegisterSetting(this.profession)

      if (res.isSuccess()) {
        this.$message.success('保存注册设置成功')
        await this.getRegistConfig()
      } else {
        this.$message.error('保存注册设置失败')
      }
    }

    async resetData() {
      //重置数据
      await this.getRegistConfig()
    }

    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
    }

    //因为只有必填，强制改必填
    changeToRequire() {
      const studentRegister = this.mutationRegisterAndLogin.registerSetting.studentRegister
      const personIndustryRegister = this.mutationRegisterAndLogin.registerSetting.personIndustryRegister
      const constructionIndustryRegister = this.mutationRegisterAndLogin.registerSetting.constructionIndustryRegister
      this.mutationRegisterAndLogin.registerSetting.studentRegister.name.isRequire = true
      studentRegister.idCard.isRequire = true
      studentRegister.password.isRequire = true
      studentRegister.phone.isRequire = true
      studentRegister.area.isRequire = true
      personIndustryRegister.professionalQualification.isRequire = true
      personIndustryRegister.firstProfessionalCategory.isRequire = true
      constructionIndustryRegister.certificateCategory.isRequire = true
      constructionIndustryRegister.registerProfessional.isRequire = true
      constructionIndustryRegister.certificateNo.isRequire = true
      constructionIndustryRegister.releaseStartTime.isRequire = true
      constructionIndustryRegister.certificateEndTime.isRequire = true
    }

    //获取注册配置
    async getRegistConfig() {
      const industryRes = await QueryIndustry.queryIndustry()
      if (industryRes.isSuccess()) {
        this.hasSociety = false
        this.hasBuild = false
        this.hasWorkService = false
        this.hasHygiene = false
        this.industryList = QueryIndustry.industryList
        console.log(this.industryList, '---industryList')

        this.industryNameList = []
        this.industryList.forEach((item) => {
          if (item?.name?.indexOf('人社行业') !== -1) {
            this.hasSociety = true
          }
          if (item?.name?.indexOf('建设行业') !== -1) {
            this.hasBuild = true
          }
          if (item?.name?.indexOf('工勤行业') !== -1) {
            this.hasWorkService = true
          }
          if (item?.name?.indexOf('职业卫生行业') !== -1) {
            this.hasHygiene = true
          }
          if (item?.name?.indexOf('教师行业') !== -1) {
            this.hasTeacher = true
          }
          if (item?.name?.indexOf('药师行业') !== -1) {
            this.hasMedicine = true
          }
          this.industryNameList.push(item.name)
        })
      }
      const res = await this.mutationRegisterAndLogin.queryRegisterSetting()
      if (res.isSuccess()) {
        this.changeToRequire()
        console.log('获取注册设置成功')
        //this.$message.success('获取注册设置成功')
      } else {
        this.$message.error('获取注册设置失败')
      }
    }

    async created() {
      await this.getRegistConfig()
    }
  }
</script>
