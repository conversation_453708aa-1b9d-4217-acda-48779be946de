import { BaseQuestionResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import MutationQuestionFactory from '@api/service/management/resource/question/MutationQuestionFactory'
import BaseQuestionResponseVo from './BaseQuestionResponseVo'
/*
 * 试题模型 【查询试题列表返回项】
 */
class QuestionListDetailVo extends BaseQuestionResponseVo {
  /**
   * 试题ID
   */
  questionId = ''
  /**
   * 题库ID
   */
  libraryId = ''
  /**
   * 题库名称
   */
  libraryName = ''

  /**
   * 试题题目
   */
  topic = ''
  /**
   * 试题解析
   */
  dissects = ''

  /**
   * 创建时间
   */
  createTime = ''

  /**
   * 试题类型
    @see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType = 0
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string> = []

  /**
   * 关联课程名称
   */
  relateCourseNames: Array<string> = []

  /**
   * 试题是否可用
   */
  isEnabled: boolean = undefined

  // 模型转换
  static from(item: BaseQuestionResponse) {
    const questionListDetail = new QuestionListDetailVo()
    questionListDetail.questionId = item.questionId
    questionListDetail.libraryId = item.libraryInfo?.libraryId
    questionListDetail.libraryName = item.libraryInfo?.libraryName
    questionListDetail.isEnabled = item.isEnabled
    questionListDetail.topic = item.topic
    questionListDetail.dissects = item.dissects
    questionListDetail.createTime = item.createTime
    questionListDetail.questionType = item.questionType
    questionListDetail.relateCourseIds = item.relateCourseIds
    return questionListDetail
  }

  // 获取试题操作方法
  get questionAction() {
    return MutationQuestionFactory.getQuestionAction(this.questionId)
  }
}

export default QuestionListDetailVo
