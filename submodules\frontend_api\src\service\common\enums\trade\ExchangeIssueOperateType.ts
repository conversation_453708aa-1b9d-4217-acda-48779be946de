import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 更换期别操作类型枚举
 * cross_scheme 换班换期
 * in_scheme 班内换期
 */
export enum ExchangeIssueOperateTypeEnum {
  cross_scheme = 0,
  in_scheme = 1
}

/**
 * @description 更换期别操作类型
 */
class ExchangeIssueOperateType extends AbstractEnum<ExchangeIssueOperateTypeEnum> {
  static enum = ExchangeIssueOperateTypeEnum

  constructor(status?: ExchangeIssueOperateTypeEnum) {
    super()
    this.current = status
    this.map.set(ExchangeIssueOperateTypeEnum.cross_scheme, '换班换期')
    this.map.set(ExchangeIssueOperateTypeEnum.in_scheme, '班内换期')
  }
}

export default new ExchangeIssueOperateType()
