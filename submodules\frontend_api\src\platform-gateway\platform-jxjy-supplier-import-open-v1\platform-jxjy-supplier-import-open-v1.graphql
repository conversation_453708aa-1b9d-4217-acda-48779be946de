schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""导出 导入模板失败数据
		@param batchNo 批次号
		@return excel文件路径
	"""
	exportAlreadyFailExcel(batchNo:String!):String
	"""查询导入excel就绪失败的数据列表
		@param request 集体查询请求
		@param page    分页信息
		@return 分页响应数据
	"""
	findImportFailDataByPage(request:SupplierImportQueryRequest,page:Page!):SupplierImportOpenMetaRowPage @page(for:"SupplierImportOpenMetaRow")
	"""查询导入excel就绪成功的数据列表
		@param request 集体查询请求
		@param page    分页信息
		@return 分页响应数据
	"""
	findImportSuccessDataByPage(request:SupplierImportQueryRequest,page:Page!):ImportOpenPageWithSuccessAmount
	"""查询开通学习后的数据列表
		@param request 集体查询请求
		@param page    分页信息
		@return 分页响应数据
	"""
	findOpenLearningPermissionSubTuskByPage(request:SupplierImportQueryRequest,page:Page!):SupplierImportOpenMetaRowPage @page(for:"SupplierImportOpenMetaRow")
	"""按批次分组查询任务执行情况，根据服务商进行筛选
		@param page 分页信息
		@return 执行情况
	"""
	findTaskExecuteByBatchAndServicer(param:TaskBatchImportParamRequest,page:Page):BatchTaskExecuteByPageResponsePage @page(for:"BatchTaskExecuteByPageResponse")
	"""查询指定批次下的任务执行情况，根据服务商进行筛选
		@param page 分页信息
		@return 执行情况
	"""
	findTaskExecuteWithServicerResponseByPage(param:TaskExecuteParamRequest,page:Page):TaskExecuteByPageResponsePage @page(for:"TaskExecuteByPageResponse")
	"""查询模板地址
		入参：任务类型
		供应商分销导入基础版-SUPPLIER_DISTRIBUTION_BASIC_IMPORT
	"""
	queryImportOpenTemplatePath(category:String):String
	"""报名数据excel解析进度
		@param batchNo 集体报名编号
		@return 报名数据解析响应
	"""
	supplierImportDataAnalysis(batchNo:String!):SignupDataAnalysisResponse
	"""提交后报名数据执行进度
		@param batchNo 集体报名编号
		@return 报名数据执行响应
	"""
	supplierImportDataProcess(batchNo:String!):SignupDataProcessResponse
}
type Mutation {
	"""批量标记删除报名数据"""
	batchMarkDeleteSignupData(request:BatchMarkDeleteSignupRequest):ImportOpenBaseResponse
	"""清空失败数据
		@param batchNo 集体报名编号
	"""
	clearFailureData(batchNo:String!):ImportOpenBaseResponse
	"""开通学习权限
		@param batchNo 集体报名编号
	"""
	openLearningPermission(batchNo:String!):ImportOpenBaseResponse
	"""供应商为分销商导入开通（基础版）
		@param importRequest 导入信息
	"""
	supplierImportOpenBasic(importRequest:SupplierImportOpenRequest):ImportOpenResponse
	"""供应商为分销商导入开通（专业版）
		@param importRequest 导入信息
	"""
	supplierImportOpenMajor(importRequest:SupplierImportOpenRequest):ImportOpenResponse
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""批量并行查询"""
input BatchParallelQueryDto @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.entities.BatchParallelQueryDto") {
	"""不同ParallelQueryDto间查询条件相互独立"""
	parallelQueryDtoList:[ParallelQueryDto]
}
input ImportOpenQueryParamRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.entities.ImportOpenQueryParamRequest") {
	"""主任务id"""
	mainTaskId:String
	"""不同的BatchParallelQueryDto间查询条件不相互独立，需要同时满足条件才可以查到结果"""
	batchParallelQueryDtoList:[BatchParallelQueryDto]
	"""订单状态
		0-未开通 1-开通中 2-已开通，默认null
		@see OrderStateConstant
	"""
	orderState:Int
	"""方案名称"""
	schemeName:String
	"""商品id"""
	commodityId:String
	"""子任务状态
		0 - 已创建
		1 - 已就绪
		2 - 执行中
		3 - 执行完成
	"""
	subTaskState:Int
	"""子任务处理结果
		0 - 未处理
		1 - 处理成功
		2 - 处理失败
		3 - 就绪失败
	"""
	subTaskProcessResult:Int
	"""导入开始时间"""
	importStartTime:DateTime
	"""导入结束时间"""
	importEndTime:DateTime
	"""用户快照"""
	userSnapshot:UserSnapshot
}
"""单个属性元数据
	<AUTHOR>
	@since 2022/4/20
"""
input MetaProperty @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.entities.MetaProperty") {
	"""属性键"""
	key:String
	"""属性值"""
	value:String
}
"""单个属性元数据
	<AUTHOR>
	@since 2022/4/20
"""
input MetaPropertyExist @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.entities.MetaPropertyExist") {
	"""属性键
		订单状态：orderState
	"""
	key:String
	"""是否存在"""
	value:Boolean!
}
input ParallelQueryDto @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.entities.ParallelQueryDto") {
	commodityId:String
	"""子任务状态
		0 - 已创建
		1 - 已就绪
		2 - 执行中
		3 - 执行完成
	"""
	subTaskState:Int
	"""子任务处理结果
		0 - 未处理
		1 - 处理成功
		2 - 处理失败
		3 - 就绪失败
	"""
	subTaskProcessResult:Int
	"""查询属性是否存在集合"""
	existProperties:[MetaPropertyExist]
	"""订单状态
		0-未开通 1-开通中 2-已开通，3-无法创建订单，默认null
		@see OrderStateConstant
	"""
	orderState:Int
}
"""批量标记删除数据入参
	<AUTHOR>
"""
input BatchMarkDeleteSignupRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.BatchMarkDeleteSignupRequest") {
	"""批次号"""
	batchNo:String!
	"""子任务集合"""
	subTaskIdList:[String]!
}
"""供应商导入开通导入参数
	<AUTHOR>
"""
input SupplierImportOpenRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.SupplierImportOpenRequest") {
	"""批次号
		允许为空，若为空则默认创建新的批次
	"""
	batchNo:String
	"""文件路径"""
	filePath:String
	"""文件名称"""
	fileName:String
	"""终端类型
		<p>
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	terminalCode:String
	"""注册方式
		@see AccountRegisterTypes
	"""
	registerType:Int!
	"""来源类型
		@see AccountSourceTypes
	"""
	sourceType:Int!
	"""默认密码"""
	password:String
	"""密码模式
		1-默认密码——password
		2-使用身份证后六位
	"""
	passwordModel:Int!
	"""密码生效范围，1-默认仅新用户，2-全部用户（含已注册）"""
	passwordEffectiveRange:Int!
	"""是否更新基础信息"""
	updateBasicInfo:Boolean!
}
"""集体缴费查询对象
	<AUTHOR>
"""
input SupplierImportQueryRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.SupplierImportQueryRequest") {
	"""批次编号（上传失败、上传成功列表接口调用时必填，供应商分销导入跟踪里不用填）"""
	batchNo:String
	"""查询属性集合"""
	metaPropertyList:[MetaProperty]
	"""模糊查询的属性集合"""
	dimMetaPropertyKeyList:[String]
	"""模糊查询的拓展属性集合"""
	dimExpandPropertyKeyList:[String]
	"""额外附加查询参数"""
	queryParam:ImportOpenQueryParamRequest
	"""主任务类型列表"""
	taskCategoryList:[String]
}
input TaskBatchImportParamRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.TaskBatchImportParamRequest") {
	"""主任务类型列表"""
	taskCategoryList:[String]
	"""任务名称"""
	name:String
	"""任务执行状态
		0-执行中 1-成功 2-失败 3-部分失败
		@see com.fjhb.batchtask.core.enums.TaskExecuteState
	"""
	taskExecuteState:Int
	"""导入时间（起始）
		导入时间：:主任务最小执行时间
	"""
	importStartTime:DateTime
	"""导入时间（终止）"""
	importEndTime:DateTime
}
input TaskExecuteParamRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.TaskExecuteParamRequest") {
	"""任务名称"""
	taskName:String
	"""任务类型
		分销导入-DISTRIBUTION_IMPORT
		优惠导入-DISCOUNT_IMPORT
	"""
	category:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""执行时间（起始）"""
	executeStartTime:DateTime
	"""执行时间（终止）"""
	executeEndTime:DateTime
}
"""@Description
	<AUTHOR>
	@Date 2024/10/28 8:48
"""
input UserSnapshot @type(value:"com.fjhb.platform.jxjy.v1.kernel.service.task.importopen.data.dto.UserSnapshot") {
	"""名字快照"""
	nameSnapshot:String
	"""身份证快照"""
	idCardSnapshot:String
	"""身份证类型快照"""
	idCardTypeSnapshot:Int
	"""手机号快照"""
	phoneSnapshot:String
	"""工作单位地区code"""
	unitAreaCodeSnapshot:String
	"""单位统一信用编码"""
	companyCodeSnapshot:String
	"""单位名称快照"""
	companyNameSnapshot:String
	"""单位所在省份快照"""
	unitProvinceSnapshot:String
	"""单位所在城市快照"""
	unitCitySnapshot:String
	"""单位所在区县快照"""
	unitAreaSnapshot:String
}
type SortPolicy @type(value:"com.fjhb.commons.dao.page.SortPolicy") {
	orders:[Order]
}
enum Direction @type(value:"com.fjhb.commons.dao.page.SortPolicy$Direction") {
	ASC
	DESC
}
type Order @type(value:"com.fjhb.commons.dao.page.SortPolicy$Order") {
	direction:Direction
	sortKey:String
}
"""各状态及执行结果对应数量
	<AUTHOR>
"""
type EachStateCount @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.entities.EachStateCount") {
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	state:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int
	"""数量"""
	count:Int!
}
"""单个属性元数据
	<AUTHOR>
	@since 2022/4/20
"""
type MetaProperty1 @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.entities.MetaProperty") {
	"""属性键"""
	key:String
	"""属性值"""
	value:String
}
"""数据行对象
	<AUTHOR>
	@since 2022/4/24
"""
type SupplierImportOpenMetaRow @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.entities.SupplierImportOpenMetaRow") {
	"""每一行的数据"""
	row:[MetaProperty1]
	"""子任务状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	subTaskState:Int!
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int!
	"""订单号"""
	orderNo:String
	"""订单状态
		其余情况-null
		未开通-0
		开通中-1
		已开通-2
	"""
	orderState:Int
	"""是否更新密码"""
	updatePassword:Boolean
	"""是否更新基础信息"""
	updateBasicInfo:Boolean
	"""学员状态
		null-暂无记录
		0-默认
		1-新学员
		2-已注册的学员
	"""
	studentState:Int
	"""创建时间"""
	createTime:DateTime
	"""完成时间"""
	completeTime:DateTime
	"""错误日志"""
	errorMessage:String
	"""价格"""
	price:BigDecimal
	"""用户信息快照"""
	userSnapshot:UserSnapshot1
	"""分销商名称"""
	distributorName:String
	"""所属批次单编号"""
	batchNo:String
}
type BatchTaskExecuteByPageResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.BatchTaskExecuteByPageResponse") {
	"""任务名称"""
	name:String
	"""导入时间：批次分组下的主任务最小的执行时间"""
	importTime:DateTime
	"""结束时间：批次分组下主任务最大完成时间
	"""
	endTime:DateTime
	"""成功數量
	"""
	successCount:Long!
	"""失敗數量
	"""
	failureCount:Long!
	"""总數量
	"""
	totalCount:Long!
}
"""<AUTHOR>
	@date 2024/12/20 9:32
"""
type ImportOpenBaseResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.ImportOpenBaseResponse") {
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""<AUTHOR>
	@date 2024/12/13 17:15
"""
type ImportOpenPageWithSuccessAmount @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.ImportOpenPageWithSuccessAmount") {
	successAmount:BigDecimal
	totalPageSize:Long!
	currentPageData:[SupplierImportOpenMetaRow]
	currentPageStartIndex:Int!
	pageSize:Int!
	totalSize:Long!
	pageNo:Int!
	sortPolicy:SortPolicy
}
"""导入开班上传模板结果
	异常code
	500-接口异常
	3003-表头校验失败
	3004-excel表最大长度校验失败
	<AUTHOR>
	@since 2022/5/12
"""
type ImportOpenResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.ImportOpenResponse") {
	"""批次订单号"""
	batchOrderNo:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""报名数据解析响应
	<AUTHOR>
"""
type SignupDataAnalysisResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.SignupDataAnalysisResponse") {
	"""解析状态
		0=未解析，1-解析中，2=解析完成
	"""
	processStatus:Int!
	"""总数"""
	totalCount:Int!
	"""已执行数"""
	executedCount:Int!
}
"""报名数据执行进度响应
	<AUTHOR>
"""
type SignupDataProcessResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.SignupDataProcessResponse") {
	"""指定批次下是否全部所有主任务执行成功"""
	completed:Boolean!
	"""总数"""
	totalCount:Int!
	"""已执行数"""
	executedCount:Int!
}
"""任务执行情况
	<AUTHOR>
	@since 2022/5/5
"""
type TaskExecuteByPageResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.TaskExecuteByPageResponse") {
	"""任务编号"""
	id:String
	"""【必填】平台编号"""
	platformId:String
	"""【必填】平台版本编号"""
	platformVersionId:String
	"""【必填】项目编号"""
	projectId:String
	"""【必填】子项目编号"""
	subProjectId:String
	"""任务名称"""
	name:String
	"""任务分类"""
	category:String
	"""所属批次单编号"""
	batchNo:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""处理信息"""
	message:String
	"""创建时间"""
	createdTime:DateTime
	"""就绪时间"""
	alreadyTime:DateTime
	"""执行时间"""
	executingTime:DateTime
	"""完成时间"""
	completedTime:DateTime
	"""各状态及执行结果对应数量集合
		总数：全部数量之和
		成功数：result = 1数量之和
		失败数：result = 2数量之和
	"""
	eachStateCounts:[EachStateCount]
}
"""@Description
	<AUTHOR>
	@Date 2024/10/28 8:48
"""
type UserSnapshot1 @type(value:"com.fjhb.platform.jxjy.v1.kernel.service.task.importopen.data.dto.UserSnapshot") {
	"""名字快照"""
	nameSnapshot:String
	"""身份证快照"""
	idCardSnapshot:String
	"""身份证类型快照"""
	idCardTypeSnapshot:Int
	"""手机号快照"""
	phoneSnapshot:String
	"""工作单位地区code"""
	unitAreaCodeSnapshot:String
	"""单位统一信用编码"""
	companyCodeSnapshot:String
	"""单位名称快照"""
	companyNameSnapshot:String
	"""单位所在省份快照"""
	unitProvinceSnapshot:String
	"""单位所在城市快照"""
	unitCitySnapshot:String
	"""单位所在区县快照"""
	unitAreaSnapshot:String
}

scalar List
type SupplierImportOpenMetaRowPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SupplierImportOpenMetaRow]}
type BatchTaskExecuteByPageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BatchTaskExecuteByPageResponse]}
type TaskExecuteByPageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TaskExecuteByPageResponse]}
