import KeyValue from '@api/service/common/models/KeyValue'
import {
  CommoditySkuPropertyResponse,
  SkuPropertyResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'

/**
 * @description 方案sku属性
 */
class SchemeSkuProperty {
  /**
   * 行业
   */
  industry: KeyValue = new KeyValue()
  /**
   * 年度
   */
  year: KeyValue = new KeyValue()
  /**
   * 科目类型
   */
  subjectType: KeyValue = new KeyValue()
  /**
   * 培训类别
   */
  trainingCategory: KeyValue = new KeyValue()
  /**
   * 培训专业
   */
  trainingProfessional: KeyValue = new KeyValue()
  /**
   * 地区码集合
   */
  regionCode: string[] = []
  /**
   * 地区名称集合
   */
  regionName: string[] = []

  static from(response: CommoditySkuPropertyResponse) {
    const detail = new SchemeSkuProperty()
    // 行业
    detail.industry = SchemeSkuProperty.getKeyValueBySkuResp(response.industry)
    // 年度
    detail.year = SchemeSkuProperty.getKeyValueBySkuResp(response.year)
    // 科目类型
    detail.subjectType = SchemeSkuProperty.getKeyValueBySkuResp(response.subjectType)
    // 培训类别
    detail.trainingCategory = SchemeSkuProperty.getKeyValueBySkuResp(response.trainingCategory)
    // 培训专业
    detail.trainingProfessional = SchemeSkuProperty.getKeyValueBySkuResp(response.trainingProfessional)
    // 获取地区信息
    const province = SchemeSkuProperty.getKeyValueBySkuResp(response.province)
    const city = SchemeSkuProperty.getKeyValueBySkuResp(response.city)
    const county = SchemeSkuProperty.getKeyValueBySkuResp(response.county)
    if (province.key) {
      detail.regionCode.push(province.key)
      detail.regionName.push(province.value)
    }
    if (city.key) {
      detail.regionCode.push(city.key)
      detail.regionName.push(city.value)
    }
    if (county.key) {
      detail.regionCode.push(county.key)
      detail.regionName.push(county.value)
    }
    return detail
  }

  /**
   * 获取sku属性和名称
   * @param skuResp
   */
  static getKeyValueBySkuResp(skuResp: SkuPropertyResponse) {
    const sku = new KeyValue()
    sku.key = skuResp?.skuPropertyValueId || ''
    sku.value = skuResp?.skuPropertyValueName || ''
    return sku
  }
}

export default SchemeSkuProperty
