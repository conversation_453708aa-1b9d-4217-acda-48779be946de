<route-meta>
{
"isMenu": true,
"title": "活动管理",
"sort": 4
}
</route-meta>
<template>
  <div>
    <el-main>
      <el-tabs v-model="activeName" class="m-tab-top is-sticky" @tab-click="handleReLoad">
        <!-- 待审核 -->
        <template v-if="$hasPermission('ActivityManageTab')" desc="活动管理列表" actions="@ActivityManageTab">
          <el-tab-pane label="待审核" name="first">
            <activity-manage-tab ref="waitRef" @toDetail="toDetail" :auditStatus="1" />
          </el-tab-pane>
        </template>
        <!-- 已审核 -->
        <template v-if="$hasPermission('ActivityManageTab')" desc="活动管理列表" actions="@ActivityManageTab">
          <el-tab-pane label="已审核" name="second">
            <activity-manage-tab ref="passRef" @toDetail="toDetail" :auditStatus="2" />
          </el-tab-pane>
        </template>
      </el-tabs>
      <template v-if="$hasPermission('DetailDialog')" desc="查询详情" actions="@DetailDialog">
        <detail-dialog
          ref="detailDialogRef"
          :learningExperienceDetail="learningExperienceDetail"
          :learningExperienceId="learningExperienceId"
          @handleReLoad="handleReLoad"
        ></detail-dialog>
      </template>
      <!-- 详情的抽屉 -->
    </el-main>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import FieldUnderIndustry from '@hbfe/jxjy-admin-scheme/src/field/field-under-industry.vue'
  import DetailDialog from '@hbfe/jxjy-admin-scheme/src/components/detail-dialog.vue'
  import ActivityManageTab from '@hbfe/jxjy-admin-scheme/src/components/activity-manage-tab.vue'
  import ActivityManangeItemModel from '@api/service/management/activity/models/ActivityManangeItemModel'
  import ActivityManangeDetailModel from '@api/service/management/activity/models/ActivityManangeDetailModel'
  import ActivityManangeModel from '@api/service/management/activity/ActivityManangeModel'

  @Component({
    components: { FieldUnderIndustry, DetailDialog, ActivityManageTab }
  })
  export default class extends Vue {
    @Ref('detailDialogRef') detailDialogRef: DetailDialog
    @Ref('waitRef') waitRef: ActivityManageTab
    @Ref('passRef') passRef: ActivityManageTab
    @Watch('detailDialogRef.logVisible')
    valueChange(val: any) {
      console.log(val, 'logvisible')
    }
    activeName = 'first'
    learningExperienceId = ''
    learningExperienceDetail = new ActivityManangeDetailModel()
    ActivityManangeModelObj = new ActivityManangeModel()
    async toDetail(row: ActivityManangeItemModel) {
      console.log(row, '点击进入详情')
      this.learningExperienceDetail = row.detail
      this.learningExperienceId = row.id
      this.learningExperienceDetail.passScore = await this.ActivityManangeModelObj.requestClassConfig(row.schemeId)

      this.detailDialogRef.init()
    }
    handleReLoad() {
      setTimeout(() => {
        if (this.activeName == 'first') {
          this.waitRef.handleLoadData()
        } else {
          this.passRef.handleLoadData()
        }
      }, 100)
    }
  }
</script>
