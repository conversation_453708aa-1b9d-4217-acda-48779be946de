import Context from '@api/service/common/context/Context'
import PlatformDistributionService, {
  GetDistributionServiceRequest
} from '@api/platform-gateway/platform-jxjypxtypt-distribution-service-v1'
import { DistributionServiceTypeEnum } from '@api/service/common/capability-service-config/enum/DistributionServiceTypeEnum'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

class CapabilityServiceConfig {
  /**
   * 是否开启分销能力服务
   */
  fxCapabilityEnable = false

  /**
   * 分销商服务类型
   */
  distributionType: DistributionServiceTypeEnum = DistributionServiceTypeEnum.basic

  /**
   * 重复请求时间戳
   */
  repeatRequestTime = 0

  /**
   * 是否需要重新请求
   * @description 比较距离最后一次请求的时间戳是否大于x分钟，如果大于x分钟则需要重新请求，x取阿波罗配置，若该配置不存在则默认5分钟
   */
  get isNeedReRequest() {
    const persistentDataTime =
      Number(ConfigCenterModule.getFrontendApplication(frontendApplication.persistentDataTime)) || 5
    return Date.now() - this.repeatRequestTime > persistentDataTime * 60 * 1000
  }

  /**
   * 根据上下文服务商id判断网校是否开启分销能力服务
   */
  async checkFxCapability() {
    if (this.isNeedReRequest) {
      const serviceId = Context?.businessEnvironment?.serviceToken?.tokenMeta?.servicerId

      const request = new GetDistributionServiceRequest()
      request.servicerId = serviceId
      const res = await PlatformDistributionService.getDistributionService(request)

      // 请求成功，则更新时间戳，后续比较时间戳判断是否需要重新请求
      this.repeatRequestTime = Date.now()
      if (res?.data) {
        this.fxCapabilityEnable = !!res.data.distributionService
        this.distributionType = res.data.distributionServiceType
      } else {
        this.fxCapabilityEnable = false
      }
    }
  }
}

export default new CapabilityServiceConfig()
