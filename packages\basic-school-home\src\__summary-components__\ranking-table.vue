<template>
  <div class="w h3 d-box line line-1" style="display: flex; flex-direction: column">
    <div class="tit">{{ title }}</div>
    <dv-scroll-board :config="config" style="height: 430px" v-if="config.data.length" />
    <div v-if="!config.data.length" class="null-data">
      暂无排行数据
    </div>
  </div>
</template>

<style lang="scss">
  .dv-scroll-board {
    .rows {
      &.row-item:nth-child(odd) {
        background-color: red;
      }
    }
  }

  .null-data {
    font-size: 15px;
    color: #9e9e9e;
    text-align: center;
  }
</style>

<script lang="ts">
  import { scrollBoard } from '@jiaminghi/data-view'
  import Vue from 'vue'
  import { Component, Prop, Vue as VueDecorator } from 'vue-property-decorator'

  Vue.use(scrollBoard)
  @Component
  export default class extends VueDecorator {
    @Prop({
      required: true,
      default() {
        return new Array<any>()
      }
    })
    data: Array<any>

    @Prop({
      required: true,
      default() {
        return ''
      }
    })
    title: string

    @Prop({
      default() {
        return ''
      }
    })
    unit: string

    @Prop({
      required: true,
      default() {
        return ''
      }
    })
    filed: string

    get config() {
      return {
        oddRowBGC: '#193c5d',
        evenRowBGC: '#0a2e4f',
        data: this.data.map(item => [item.userName, `${item[this.filed]}${this.unit}`]),
        index: true,
        rowNum: 10,
        columnWidth: [60, 170, 90],
        align: ['center']
      }
    }
  }
</script>
