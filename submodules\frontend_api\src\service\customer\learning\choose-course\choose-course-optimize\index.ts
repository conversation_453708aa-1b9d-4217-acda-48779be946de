import SelectModule, { SelectCourse } from './SelectModule'
import TreeModule from './TreeModule'
import ChooseCourseGateway from '@api/ms-gateway/ms-choose-course-v1'
import MsCourseLearning from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'

/**
 * 完整的选课类 --- 选修选课
 */
export default class ChooseCourseOptimize {
  /**
   * 方案ID
   */
  private schemeId: string
  /**
   * 自身工种
   */
  private workJobName: string
  /**
   * 学号
   */
  private qualificationId: string
  /**
   * 选课时的token
   */
  token: string
  /**
   * 方案已选学时 --- 不包含未提交
   */
  selectedPeriod = 0

  /**
   * 方案还需选学时 --- 不包含未提交
   */
  needPeriod = 0
  /**
   * 是否存在二级分类要求
   */
  isSecondElectiveMaxPeriod = false
  /**
   * @param schemeId 方案ID
   * @param qualificationId 学号
   * @param token 获取选课时的token
   */
  constructor(schemeId: string, qualificationId: string, token: string) {
    this.schemeId = schemeId
    this.token = token
    this.qualificationId = qualificationId
  }
  /**
   * 初始化配置
   * tips:
   *     levelTree 栏目树
   *     selectCourse 选中树
   */
  async init() {
    const treeModule = new TreeModule()
    const levelTree = await treeModule.init(this.schemeId)
    this.isSecondElectiveMaxPeriod = treeModule.isSecondElectiveMaxPeriod
    const coursePeriodMap = await this.querySelectedPeriod()
    // const keys = [...coursePeriodMap.keys()]
    const ids = [...treeModule.treeIemMap.keys()]
    ids.forEach(item => {
      const temp = treeModule.treeIemMap.get(item)
      if (temp.level === 2 || temp.level === 1) {
        if (temp.childrenTreeItem.length === 0) {
          temp.selectCourseSumbit = coursePeriodMap.get(item) ? coursePeriodMap.get(item) : 0
          treeModule.treeIemMap.set(item, temp)
        } else {
          let count = 0
          temp.childrenTreeItem.forEach(temp => {
            if (!temp.outlineId.includes('all')) {
              count = CalculatorObj.add(
                count,
                coursePeriodMap.get(temp.outlineId) ? coursePeriodMap.get(temp.outlineId) : 0
              )
            }
            // if (temp.outlineId.includes('otherCourse')) {
            //   temp.gatherTreeItem.forEach(res => {
            //     count = CalculatorObj.add(
            //       count,
            //       coursePeriodMap.get(res.outlineId) ? coursePeriodMap.get(res.outlineId) : 0
            //     )
            //   })
            // } else {
            //
            // }
          })
          temp.selectCourseSumbit = count

          treeModule.treeIemMap.set(item, temp)
        }
      }
      //   if (temp.level === 1) {
      //     temp.selectCourseSumbit = coursePeriodMap.get(item) ? coursePeriodMap.get(item) : 0
      //     treeModule.treeIemMap.set(item, temp)
      //   }
    })
    // keys.forEach(item => {
    //   const temp = treeModule.treeIemMap.get(item)
    //   console.group(
    //     '%c%s',
    //     'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#32FFFF, #fff)',
    //     'temp调试输出'
    //   )
    //   console.log(temp)
    //   console.count('temp输出次数')
    //   console.groupEnd()
    //   if (temp) {
    //     if (temp.childrenTreeItem.length === 0) {
    //       temp.selectCourseSumbit = coursePeriodMap.get(item)
    //       treeModule.treeIemMap.set(item, temp)
    //     } else {
    //       let count = 0
    //       temp.childrenTreeItem.forEach(temp => {
    //         if (temp.outlineId === 'otherCourse') {
    //           temp.gatherTreeItem.forEach(res => {
    //             count += coursePeriodMap.get(res.outlineId)
    //           })
    //         } else {
    //           count += coursePeriodMap.get(item)
    //         }
    //       })
    //       temp.selectCourseSumbit = count

    //       treeModule.treeIemMap.set(item, temp)
    //     }
    //   }
    // })
    const selectCourse = new SelectModule(treeModule.treeIemMap, this.token, treeModule.isSecondElectiveMaxPeriod)
    selectCourse.needPeriod = this.needPeriod
    return { levelTree, selectCourse, isSecondElectiveMaxPeriod: this.isSecondElectiveMaxPeriod }
  }
  /**
   * 根据选课token获取学时
   */
  private async querySelectedPeriod() {
    const response = await ChooseCourseGateway.prepareChooseCourse(this.token)
    const propertiesObj = JSON.parse(response.data.properties)
    this.selectedPeriod = propertiesObj.electiveMaxPeriod.current
    this.needPeriod = CalculatorObj.subtract(
      propertiesObj.electiveMaxPeriod.config,
      propertiesObj.electiveMaxPeriod.current
    )
    const result = await MsCourseLearning.statisticsCourseLearningSceneChooseCourseByOutlineOfInMyself(
      this.qualificationId
    )
    const coursePeriodMap: Map<string, number> = new Map()
    result.data.forEach(item => coursePeriodMap.set(item.outlineId, item.chooseCourseTotalPeriod))
    return coursePeriodMap
  }

  /**
   * 查询启用的内容提供方列表
   */
  /*private async queryEnableContentList(): Promise<Array<ContentProviderResponse>> {
    const contentProviderRequest = new ContentProviderRequest()

    const res = await Contentproviderv1.listContentProvider(contentProviderRequest)

    const result = (res?.data?.length && res.data.filter(item => item.status === 1)) || []

    return result.sort((a, b) => {
      return a.sort - b.sort
    })
  }*/
}
