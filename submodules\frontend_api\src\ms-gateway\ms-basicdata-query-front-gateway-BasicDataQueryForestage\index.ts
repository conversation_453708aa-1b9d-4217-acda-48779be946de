import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-basicdata-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-basicdata-query-front-gateway-BasicDataQueryForestage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 业务区域树根结点查询
 */
export class BusinessRegionTreeQueryChildrenRequest {
  /**
   * 业务id，代表使用哪一棵业务地区树
   */
  businessId?: string
  /**
   * 父节点Id -1时为根结点
   */
  parentId?: string
}

/**
 * 业务区域树根结点查询
 */
export class BusinessRegionTreeQueryRequest {
  /**
   * 业务id，代表使用哪一棵业务地区树
   */
  businessId?: string
  /**
   * 业务区域编码
   */
  code?: string
}

/**
 * 业务区域树根结点查询
 */
export class BusinessRegionTreeQueryRootRequest {
  /**
   * 业务id，代表使用哪一棵业务地区树
   */
  businessId?: string
}

/**
 * 查询行业属性信息
 */
export class IndustryPropertyInfoQueryRequest {
  /**
   * 服务商(网校id)
   */
  servicerId?: string
  /**
   * 行业id
   */
  industryIdList?: Array<string>
  /**
   * 行业属性类别 0-业务行业属性，1-人员行业属性
   */
  propertyType?: number
}

export class IndustryPropertyRequest {
  /**
   * 行业属性名称
   */
  industryPropertyName?: string
  /**
   * 行业id
   */
  industryIdList?: Array<string>
  /**
   * 行业属性类别 0-业务行业属性，1-人员行业属性
   */
  propertyType?: number
}

export class PageIndustryPropertyByCategoryRequest {
  /**
   * 行业属性编号
   */
  industryPropertyId?: string
  /**
   * 行业ID
   */
  industryId?: string
  /**
   * 行业属性分类代码
TRAINING_CATEGORY
PERSON_TRAINING_CATEGORY
TRAINING_PROFESSIONAL
PERSON_TRAINING_PROFESSIONAL
   */
  categoryCode?: string
  /**
   * 字典名称，模糊查询
   */
  name?: string
  /**
   * 是否可用，0停用1可用，不传返回全部
   */
  available?: number
}

/**
 * 网校培训属性查询条件
<AUTHOR>
@since 2022/1/17
 */
export class SchoolTrainingPropertyQueryRequest {
  /**
   * 网校编号
   */
  schoolId?: string
  /**
   * 行业编号
   */
  industryId?: string
  /**
   * 培训属性编号列表，最大支持200个
   */
  propertyId?: Array<string>
}

/**
 * <AUTHOR> linq
@date : 2023-08-22 11:33
@description：查询服务地区 请求
 */
export class ServiceOrIndustryRegionQueryRequest {
  /**
   * 服务类型 0或1
0 - 平台业务地区(PLATFORM_BUSINESS_REGION)
1 - 培训方案地区(TRAINING_SCHEME_REGION)
   */
  type?: number
  /**
   * 需要返回的地区级别
0 - 全部 (自身及所有下级地区)
1 - 仅省级
2 - 仅市级
3 - 仅区县级
   */
  whichLevel?: number
  /**
   * 地区父节点code
   */
  parentRegionCode?: string
}

export class ServicerContractPropertyByCategoryRequest {
  /**
   * 字典分类
   */
  categoryCode?: string
  /**
   * 可用状态，不传的情况下默认可用，0停用 1可用
   */
  availableList?: Array<number>
}

/**
 * 培训属性通用查询参数
 */
export class TrainingPropertyCommonRequest {
  /**
   * 行业属性编号
   */
  industryPropertyId?: string
  /**
   * 行业ID
   */
  industryId?: string
  /**
   * 行业属性分类代码
TRAINING_CATEGORY
PERSON_TRAINING_CATEGORY
TRAINING_PROFESSIONAL
PERSON_TRAINING_PROFESSIONAL
   */
  categoryCode?: string
  /**
   * 字典名称，模糊查询
   */
  name?: string
  /**
   * 是否可用，0停用1可用，不传默认可用。传-1返回全部
   */
  available?: number
}

/**
 * 培训属性查询条件
<AUTHOR>
@since 2022/1/17
 */
export class TrainingPropertyQueryRequest {
  /**
   * 行业属性编号
   */
  industryPropertyId?: string
  /**
   * 行业属性分类
   */
  categoryCode?: string
  /**
   * 培训属性编号
   */
  propertyId?: string
  /**
   * 培训属性编号集合
目前只在以下接口使用，优先使用 propertyId 字段，即 propertyId 有值时，propertyIdList 入参无效
com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.DictionaryCommonQueryResolver#listIndustryPropertyChildByCategoryV2
   */
  propertyIdList?: Array<string>
}

/**
 * 分销商指定推广门户资讯查询条件
 */
export class DistributorNewsQueryCommonRequest {
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 资讯分类编号
   */
  necId?: string
  /**
   * 资讯发布的地区编码
   */
  areaCodePath?: string
  /**
   * 是否需要过滤发布时间在当前时间之后的数据 true-是 false-否
   */
  needAfterPublishTime?: boolean
}

/**
 * 首页资讯查询条件
 */
export class NewsFrontQueryByCodeRequest {
  /**
   * 资讯标题 可为空
   */
  title?: string
  /**
   * 资讯分类代码  必填
   */
  codes?: Array<string>
  /**
   * 顶级分类代码  必填
   */
  rootCategoryCode?: string
  /**
   * 是否需要过滤发布时间在当前时间之后的数据 true-是 false-否
   */
  needAfterPublishTime?: boolean
}

/**
 * 首页资讯查询条件
 */
export class NewsFrontQueryRequest {
  /**
   * 资讯标题 可为空
   */
  title?: string
  /**
   * 资讯分类编代码  可为空
   */
  code?: string
  /**
   * 顶级分类代码  必填
   */
  rootCategoryCode?: string
  /**
   * 是否需要过滤发布时间在当前时间之后的数据 true-是 false-否
   */
  needAfterPublishTime?: boolean
}

/**
 * 资讯查询条件
 */
export class NewsQueryByAreaCodePathRequest {
  /**
   * 资讯分类编号
   */
  necId: string
  /**
   * 资讯发布的地区编码
   */
  areaCodePath: string
  /**
   * 是否需要过滤发布时间在当前时间之后的数据 true-是 false-否
   */
  needAfterPublishTime?: boolean
}

/**
 * 资讯查询条件
 */
export class NewsQueryCommonRequest {
  /**
   * 资讯分类编号
   */
  necId?: string
  /**
   * 资讯发布的地区编码
   */
  areaCodePath?: string
  /**
   * 是否需要过滤发布时间在当前时间之后的数据 true-是 false-否
   */
  needAfterPublishTime?: boolean
}

/**
 * 资讯查询条件，支持针对不同字段
 */
export class NewsQueryForOrderRequest {
  /**
   * 资讯分类编号
   */
  necId: string
  /**
   * 排序字段名称，格式如下
发布时间：0
浏览数量：1
<p>
若排序字段为-1，默认按照从置顶到非置顶，发布时间从新到旧顺序排列
   */
  orderFiled?: number
  /**
   * 排序方式
0 升序
1 降序
   */
  orderType?: number
  /**
   * 是否需要过滤发布时间在当前时间之后的数据 true-是 false-否
   */
  needAfterPublishTime?: boolean
}

/**
 * 资讯查询条件
 */
export class NewsWithPreviousAndNextCommonInDistributorRequest {
  /**
   * 资讯编号
   */
  newId?: string
  /**
   * 资讯发布的地区编码
   */
  areaCodePath?: string
  /**
   * 是否需要过滤发布时间在当前时间之后的数据 true-是 false-否
   */
  needAfterPublishTime?: boolean
  /**
   * 门户id
   */
  portalId?: string
}

/**
 * 资讯查询条件
 */
export class NewsWithPreviousAndNextCommonRequest {
  /**
   * 资讯编号
   */
  newId?: string
  /**
   * 资讯发布的地区编码
   */
  areaCodePath?: string
  /**
   * 是否需要过滤发布时间在当前时间之后的数据 true-是 false-否
   */
  needAfterPublishTime?: boolean
}

/**
 * 首页资讯查询条件
 */
export class TrainingChannelNewsFrontQueryByCodeRequest {
  /**
   * 资讯标题 可为空
   */
  title?: string
  /**
   * 资讯分类代码  必填
   */
  codes?: Array<string>
  /**
   * 顶级分类代码  必填
   */
  rootCategoryCode?: string
  /**
   * 是否需要过滤发布时间在当前时间之后的数据 true-是 false-否
   */
  needAfterPublishTime?: boolean
  /**
   * 专题ID集合
   */
  specialSubjectIds?: Array<string>
}

/**
 * 专题资讯查询条件
 */
export class TrainingChannelNewsQueryCommonRequest {
  /**
   * 资讯分类编号
   */
  necId?: string
  /**
   * 资讯分类编号集合
如果单个咨询分类编号与集合都有值，优先使用单个id
   */
  necIdList?: Array<string>
  /**
   * 资讯发布的地区编码
   */
  areaCodePath?: string
  /**
   * 是否需要过滤发布时间在当前时间之后的数据 true-是 false-否
   */
  needAfterPublishTime?: boolean
  /**
   * 专题ID
   */
  specialSubjectId?: string
}

/**
 * 专题资讯查询条件
 */
export class TrainingChannelNewsWithPreviousAndNextCommonRequest {
  /**
   * 资讯编号
   */
  newId?: string
  /**
   * 资讯发布的地区编码
   */
  areaCodePath?: string
  /**
   * 是否需要过滤发布时间在当前时间之后的数据 true-是 false-否
   */
  needAfterPublishTime?: boolean
  /**
   * 专题ID
   */
  specialSubjectId?: string
}

/**
 * 浏览数最多的专题资讯列表请求参数
 */
export class TrainingChannelReviewTopNewsCommonRequest {
  /**
   * top数量,在1~50之间
   */
  topNum?: number
  /**
   * 是否需要过滤发布时间在当前时间之后
   */
  needAfterPublishTime?: boolean
  /**
   * 专题ID
   */
  specialSubjectId?: string
}

/**
 * 功能描述 : 门户持核心层查询条件
@date : 2022年10月18日 19:53:03
 */
export class PortalRequest {
  /**
   * 归属信息
   */
  owner?: PortalOwnerRequest
  /**
   * 门户信息
   */
  portalInfo?: PortalInfoRequest
}

/**
 * 功能描述：门户信息查询条件
 */
export class PortalInfoRequest {
  /**
   * 门户id集合
   */
  portalId?: Array<string>
  /**
   * 门户类型（1：web端 2：移动端）
@see com.fjhb.domain.basicdata.api.servicer.consts.PortalTypes
   */
  portalType?: number
  /**
   * 门户状态（0：未发布 1：已发布）
   */
  status?: number
}

/**
 * 功能描述：门户归属信息查询参数
@Author： wtl
@Date： 2022年10月18日 19:53:52
 */
export class PortalOwnerRequest {
  /**
   * 所属培训机构id集合
   */
  trainingInstitutionIdList?: Array<string>
}

export class RegionModel {
  regionId: string
  regionPath: string
  provinceId: string
  provinceName: string
  cityId: string
  cityName: string
  countyId: string
  countyName: string
}

export class SectionAndSubjects {
  section: number
  subjects: number
}

/**
 * 功能描述：账户信息
@Author： wtl
@Date： 2022年5月11日 15:30:56
 */
export class AccountResponse {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
@see AccountTypes
   */
  accountType: number
  /**
   * 单位信息
   */
  unitInfo: UnitInfoResponse
  /**
   * 所属顶级企业帐户Id
   */
  rootAccountId: string
  /**
   * 帐户状态 1：正常，2：冻结，3：注销
@see AccountStatus
   */
  status: number
  /**
   * 注册方式
0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
@see AccountRegisterTypes
   */
  registerType: number
  /**
   * 来源类型
0、内置，1、项目主网站，2、安卓，3、IOS，4、后台导入，5、迁移数据，6、分销平台项目主网站，7、专题，8、华医网，9、江西管理平台
@see AccountSourceTypes
   */
  sourceType: number
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 功能描述：帐户认证信息
@Author： wtl
@Date： 2022年5月11日 14:23:18
 */
export class AuthenticationResponse {
  /**
   * 帐号
   */
  identity: string
  /**
   * 认证标识类型
1用户名,2手机,3身份证,4邮箱,5第三方OpenId
   */
  identityType: number
  /**
   * 认证方式状态 1启用，2禁用
@see AuthenticationStatusEnum
   */
  status: number
}

/**
 * 功能描述：用户基础信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class UserInfoResponse {
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 性别
-1未知，0女，1男
@see Genders
   */
  gender: number
  /**
   * 证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string
  /**
   * 工作单位名称
   */
  companyName: string
  /**
   * 工作单位统一社会信用代码
   */
  companyCode: string
}

/**
 * 单位信息模型
 */
export class UnitInfoResponse {
  /**
   * 单位ID
   */
  unitId: string
}

/**
 * 功能描述：集体缴费管理员信息
@Author： wtl
@Date： 2022年1月26日 10:38:15
 */
export class CollectiveInfoResponse {
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 用户信息
   */
  userInfo: UserInfoResponse
  /**
   * 用户认证信息
   */
  authenticationList: Array<AuthenticationResponse>
}

/**
 * Description:业务数据字典
 */
export class BusinessDictionaryAcrossTypeResponse {
  /**
   * 字典关系id
   */
  id: string
  /**
   * 主字典ID
   */
  masterId: string
  /**
   * 从字典ID
   */
  slaveId: string
  /**
   * 从字典字典类型|对应枚举BusinessDataDictionaryTypeEnum
@see BusinessDataDictionaryTypeEnum
   */
  slaveType: string
  /**
   * 初始化数据识别标志
   */
  initable: number
}

/**
 * 业务地区信息
 */
export class BusinessRegionResponse {
  /**
   * 地区编码
   */
  id: string
  /**
   * 上级地区编码
   */
  parentId: string
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 地区名称
   */
  name: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enable: boolean
}

/**
 * 行政区划地区名称
<AUTHOR>
@since 2022/7/6
 */
export class BusinessTreeRegionNameMap {
  /**
   * 地区路径
格式：/350000/350100/350101
   */
  path: string
  /**
   * 地区名称拼接
格式：福建省/福州市/鼓楼区
   */
  name: string
}

export class BusinessTreeRegionResponse {
  /**
   * id
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 行政区划
   */
  code: string
  /**
   * 结点名称
   */
  name: string
  /**
   * 父节点Id -1时为根结点
   */
  parentId: string
  /**
   * 业务Id
   */
  businessId: string
  /**
   * 业务树结构路径
   */
  regionPath: string
}

/**
 * 业务年度信息
 */
export class BusinessYearResponse {
  /**
   * 业务年度编号
   */
  id: string
  /**
   * 年度
   */
  year: string
  /**
   * 序号
   */
  sort: number
  /**
   * 是否启用
   */
  enable: boolean
}

export class GetIndustryTypeResponse {
  /**
   * 行业类型id
   */
  id: string
  /**
   * 代码
   */
  code: string
  /**
   * 行业类型名称
   */
  industryType: string
  /**
   * 上级编号
   */
  parentId: string
  /**
   * 上级代码
   */
  parentCode: string
}

/**
 * 单位类型
 */
export class GetUnitTypeResponse {
  /**
   * 单位类型id
   */
  id: string
  /**
   * 代码
   */
  code: string
  /**
   * 单位类型名称
   */
  unitType: string
  /**
   * 上级编号
   */
  parentId: string
  /**
   * 上级代码
   */
  parentCode: string
}

/**
 * 行业属性分类信息
<AUTHOR>
@since 2022/1/17
 */
export class IndustryPropertyCategoryResponse {
  /**
   * 分类code
   */
  code: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
}

/**
 * 网校行业属性id集合
 */
export class IndustryPropertyInfoResponse {
  /**
   * 行业培训属性ID
   */
  industryPropertyId: string
  /**
   * 服务商ID
   */
  serviceId: string
  /**
   * 行业属性名称
   */
  industryPropertyName: string
  /**
   * 排序
   */
  sort: number
  /**
   * 来源id，模板数据的值为-1，网校的值为对应的模板行业培训属性id
   */
  sourceId: string
  /**
   * 行业属性类型
   */
  propertyType: number
}

export class IndustryPropertyResponse {
  /**
   * 行业培训属性ID
   */
  industryPropertyId: string
  /**
   * 行业属性名称
   */
  industryPropertyName: string
  /**
   * 行业字典ID
   */
  industryId: string
  /**
   * 行业字典名称
   */
  industryName: string
  /**
   * 更新时间
   */
  updateTime: string
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 培训类别信息
<AUTHOR>
@since 2022/2/10
 */
export class IndustryResponse {
  /**
   * 行业编号
   */
  id: string
  /**
   * 行业名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
}

export class IndustryTypeResponse {
  /**
   * 行业类型id
   */
  id: string
  /**
   * 代码
   */
  code: string
  /**
   * 行业类型名称
   */
  industryType: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 职称等级
<AUTHOR>
@since 2022/2/10
 */
export class LeaderPositionLevelResponse {
  /**
   * 职称等级编号
   */
  id: string
  /**
   * 职称等级名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
}

/**
 * 物理地区信息
 */
export class PhysicalRegionResponse {
  /**
   * 地区编码
   */
  id: string
  /**
   * 上级地区编码
   */
  parentId: string
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 地区名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
  /**
   * 是否启用
   */
  enable: boolean
}

/**
 * 功能描述：地区字典信息
@Author： yxw
@Date： 2023年6月15日
 */
export class RegionResponse {
  /**
   * 地区编码
   */
  code: string
  /**
   * 父级地区编码
   */
  parentCode: string
  /**
   * 地区编码路径
   */
  codePath: string
  /**
   * 地区名称
   */
  name: string
  /**
   * 级别|1省级 2市级 3区县级
   */
  level: number
  /**
   * 地区排序
   */
  sort: number
}

/**
 * 培训类别信息
<AUTHOR>
@since 2022/2/10
 */
export class TrainingCategoryResponse {
  /**
   * 培训类别编号
   */
  categoryId: string
  /**
   * 培训类别父级编号
   */
  parentId: string
  /**
   * 培训类别名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
}

/**
 * 培训属性信息
<AUTHOR>
@since 2022/1/17
 */
export class TrainingPropertyResponse {
  /**
   * 业务侧培训属性关系主键ID
   */
  propertyRelationId: string
  /**
   * 清洗侧属性字典ID
   */
  propertyId: string
  /**
   * 属性名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
  /**
   * code值
   */
  code: number
  /**
   * code值，扩展
   */
  codeExt: string
  /**
   * 展示名称
   */
  showName: string
  /**
   * 如果是科目类型下的属性，则该值为null
   */
  parentId: string
  /**
   * 是否可用，0停用1可用
   */
  available: number
}

/**
 * 单位类型
 */
export class UnitTypeResponse {
  /**
   * 单位类型id
   */
  id: string
  /**
   * 代码
   */
  code: string
  /**
   * 单位类型名称
   */
  unitType: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 网校前端查询的资讯信息
 */
export class CompleteNewsByPublishResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 摘要
   */
  summary: string
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 图片路径
   */
  coverPath: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 专题ID
   */
  specialSubjectId: string
}

/**
 * 资讯分类信息
 */
export class NewsCategoryResponse {
  /**
   * 资讯分类编号
   */
  newsCategoryId: string
  /**
   * 分类名称
   */
  categoryName: string
  /**
   * 分类代码
   */
  code: string
}

/**
 * <AUTHOR> linq
@date : 2025-03-31 09:54
@description : 资讯分类信息(树形结构)
 */
export class NewsCategoryTreeResponse {
  /**
   * 资讯分类编号
   */
  newsCategoryId: string
  /**
   * 分类名称
   */
  categoryName: string
  /**
   * 分类代码
   */
  code: string
  /**
   * 子资讯分类
   */
  children: Array<NewsCategoryTreeResponse>
}

/**
 * 详细资讯信息
 */
export class NewsDetailResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 平台id
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目id
   */
  projectId: string
  /**
   * 子项目id
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商id
   */
  serviceId: string
  /**
   * 分类id
   */
  necId: string
  /**
   * 标题
   */
  title: string
  /**
   * 摘要
   */
  summary: string
  /**
   * 内容
   */
  content: string
  /**
   * 封面图片路径
   */
  coverPath: string
  /**
   * 来源
   */
  source: string
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 发布地区编码
   */
  areaCodePath: string
  /**
   * 资讯状态 0 草稿 1正常
   */
  status: number
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 浏览数量
   */
  reviewCount: number
  /**
   * 弹窗起始时间
   */
  popupBeginTime: string
  /**
   * 弹窗截止时间
   */
  popupEndTime: string
  /**
   * 专题ID
   */
  specialSubjectId: string
}

/**
 * 详细资讯信息及上下资讯id
 */
export class NewsDetailWithPreviousAndNext {
  /**
   * 详细资讯信息
   */
  newsDetail: NewsDetailResponse
  /**
   * 上一条资讯id
   */
  previousId: string
  /**
   * 下一条资讯id
   */
  nextId: string
}

/**
 * 详细资讯信息及上下资讯id
 */
export class NewsDetailWithPreviousAndNextCommonResponse {
  /**
   * 详细资讯信息
   */
  newsDetail: NewsDetailResponse
  /**
   * 上一条资讯id
   */
  previousId: string
  /**
   * 下一条资讯id
   */
  nextId: string
}

/**
 * 资讯信息
 */
export class NewsInfoResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 资讯分类id
   */
  necId: string
  /**
   * 专题ID
   */
  specialSubjectId: string
}

/**
 * 网校前端查询的资讯信息
 */
export class SimpleNewsByPublishCommonResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 摘要
   */
  summary: string
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 浏览数量
   */
  reviewCount: number
  /**
   * 专题ID
   */
  specialSubjectId: string
  /**
   * 分类编号
   */
  necId: string
}

/**
 * 网校前端查询的资讯信息
 */
export class SimpleNewsByPublishResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 摘要
   */
  summary: string
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
}

/**
 * 网校前端查询的资讯信息
 */
export class SimpleNewsByPublishResponseWithReviewCount {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 摘要
   */
  summary: string
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 浏览数量
   */
  reviewCount: number
}

/**
 * 门户信息返回模型
 */
export class PortalResponse {
  /**
   * 归属信息
   */
  ownerInfo: PortalOwnerInfoResponse
  /**
   * 门户信息
   */
  portalInfo: PortalInfoResponse
}

/**
 * 门户轮播图
 */
export class PortalBannerResponse {
  /**
   * 轮播图id
   */
  bannerId: string
  /**
   * 轮播图名称
   */
  name: string
  /**
   * 图片路径
   */
  path: string
  /**
   * 链接地址
   */
  link: string
  /**
   * 轮播图排序
   */
  sort: number
  /**
   * 是否启用
   */
  isEnable: boolean
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 门户友情链接
 */
export class PortalFriendLinkResponse {
  /**
   * 友情链接id
   */
  friendLinkId: string
  /**
   * 友情链接标题
   */
  title: string
  /**
   * 友情链接图片
   */
  picture: string
  /**
   * 友情链接类型（1：文本 2：图片）
@see FriendLinkTypes
   */
  friendLinkType: number
  /**
   * 链接
   */
  link: string
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 门户信息
 */
export class PortalInfoResponse {
  /**
   * 门户id
   */
  portalId: string
  /**
   * 门户类型（1：web端 2：移动端）
@see com.fjhb.domain.basicdata.api.servicer.consts.PortalTypes
   */
  portalType: number
  /**
   * 门户状态（0：未发布 1：已发布）
   */
  status: number
  /**
   * 门户标题
   */
  title: string
  /**
   * 门户logo
   */
  logo: string
  /**
   * 浏览器图标
   */
  icon: string
  /**
   * 移动二维码
   */
  mobileQRCode: string
  /**
   * 客服电话图片
   */
  csPhonePicture: string
  /**
   * 客服电话
   */
  csPhone: string
  /**
   * 客服咨询时间
   */
  csCallTime: string
  /**
   * 在线客服代码内容id
   */
  csOnlineCodeId: string
  /**
   * 在线客服代码内容
   */
  csOnlineCodeContent: string
  /**
   * 培训流程图片
   */
  trainingFlowPicture: string
  /**
   * 底部内容(底部落款)
   */
  footContentId: string
  /**
   * 底部内容(底部落款)
   */
  footContent: string
  /**
   * 友情链接集合
   */
  friendLinkList: Array<PortalFriendLinkResponse>
  /**
   * 主题颜色
   */
  themeColor: string
  /**
   * 宣传口号
   */
  slogan: string
  /**
   * 门户简介说明内容Id
   */
  contentId: string
  /**
   * 门户简介说明内容
   */
  content: string
  /**
   * 是否提供服务号
   */
  isProvideServiceAccount: boolean
  /**
   * 域名
   */
  domainName: string
  /**
   * 轮播图列表
   */
  portalBannerList: Array<PortalBannerResponse>
  /**
   * 栏目列表
   */
  portalMenuList: Array<PortalMenuResponse>
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 栏目信息
 */
export class PortalMenuResponse {
  /**
   * 栏目id
   */
  menuId: string
  /**
   * 栏目名称
   */
  name: string
  /**
   * 父栏目id
   */
  parentId: string
  /**
   * 栏目类型（1：菜单 2：资讯）
@see MenuTypes
   */
  menuType: number
  /**
   * 来源类型（1：内置 2：用户创建）
@see MenuSourceTypes
   */
  sourceType: number
  /**
   * 链接
   */
  link: string
  /**
   * 业务code
   */
  code: string
  /**
   * 引用id(咨询类型的栏目，引用的是资讯分类id)
   */
  referenceId: string
  /**
   * 是否允许添加子级
   */
  allowChildren: boolean
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 功能描述：门户归属信息
@Author： wtl
@Date： 2022/10/19 9:18
 */
export class PortalOwnerInfoResponse {
  /**
   * 所属培训机构id
   */
  trainingInstitutionId: string
}

/**
 * 功能描述：学员信息
@Author： wtl
@Date： 2022年1月26日 10:38:15
 */
export class StudentInfoResponse {
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 学员用户信息
   */
  userInfo: StudentUserInfoResponse
  /**
   * 学员人员信息
   */
  personInfo: StudentPersonInfoResponse
  /**
   * 第三方绑定信息
   */
  openPlatformBind: OpenPlatformBindResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
}

/**
 * 功能描述：附件信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class AttachmentInfoResponse {
  /**
   * 附件名称
   */
  name: string
  /**
   * 附件地址
   */
  url: string
}

/**
 * 功能描述：学员绑定信息
@Author： wtl
@Date： 2022年5月12日 14:42:51
 */
export class OpenPlatformBindResponse {
  /**
   * 是否绑定微信
   */
  bindWX: boolean
  /**
   * 微信昵称
   */
  nickNameByWX: string
}

/**
 * 功能描述：学员证书信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class StudentCertificateResponse {
  /**
   * 证书id
   */
  certificateId: string
  /**
   * 证书编号
   */
  certificateNo: string
  /**
   * 证书类别
   */
  certificateCategory: string
  /**
   * 注册专业
   */
  registerProfessional: string
  /**
   * 主/增项 | 1-主项 2-增项
@see com.fjhb.domain.basicdata.api.user.consts.CertificateMainAddOnTypes
   */
  mainAddOn: string
  /**
   * 发证日期
   */
  releaseStartTime: string
  /**
   * 证书有效期
   */
  certificateEndTime: string
  /**
   * 证书附件信息
   */
  attachmentList: Array<AttachmentInfoResponse>
}

/**
 * 功能描述：学员行业信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class StudentIndustryResponse {
  /**
   * 用户行业id
   */
  userIndustryId: string
  /**
   * 行业id
   */
  industryId: string
  /**
   * 一级专业类别id
   */
  firstProfessionalCategory: string
  /**
   * 二级专业类别id
   */
  secondProfessionalCategory: string
  /**
   * 职称等级
   */
  professionalQualification: string
  /**
   * 人员类别（职业卫生行业）
   */
  personnelCategory: string
  /**
   * 岗位类别（职业卫生行业）
   */
  positionCategory: string
  /**
   * 技术等级（工勤行业）
   */
  professionalLevel: string
  /**
   * 工种（工勤行业）
   */
  jobCategoryId: string
  /**
   * 学员证书信息集合
   */
  userCertificateList: Array<StudentCertificateResponse>
  /**
   * 教师行业 学段、学科信息
   */
  sectionAndSubjects: Array<SectionAndSubjects>
  /**
   * 证书类型（药师行业）
   */
  certificatesType: string
  /**
   * 执证类别（药师行业）
   */
  practitionerCategory: string
}

export class StudentPersonInfoResponse {
  /**
   * 学历
   */
  education: string
}

/**
 * 功能描述：学员用户信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class StudentUserInfoResponse {
  /**
   * 用户昵称
   */
  nickName: string
  /**
   * 用户所属地区
   */
  region: RegionModel
  /**
   * 头像地址
   */
  photo: string
  /**
   * 联系地址
   */
  address: string
  /**
   * 学员行业信息集合
   */
  userIndustryList: Array<StudentIndustryResponse>
  /**
   * 证书技术工种Id
   */
  jobCategoryId: string
  /**
   * 证书技术等级
   */
  professionalLevel: number
  /**
   * 所属工考办地区编码
   */
  managementUnitRegion: RegionModel
  /**
   * 证书技术工种名称
   */
  jobCategoryName: string
  /**
   * 用户工作单位所在地区
   */
  companyRegion: RegionModel
  /**
   * 是否工勤人员 （0非工勤人员，1工勤人员）
   */
  isWorker: string
  /**
   * 是否退休 (0非退休人员，1退休人员)
   */
  isRetire: string
  /**
   * 学历
   */
  education: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 性别
-1未知，0女，1男
@see Genders
   */
  gender: number
  /**
   * 证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string
  /**
   * 工作单位名称
   */
  companyName: string
  /**
   * 工作单位统一社会信用代码
   */
  companyCode: string
}

/**
 * 地区专题信息响应体
 */
export class AreaTrainingChannelInfoResponse {
  /**
   * 专题用户id
   */
  trainingChannelUserId: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 地区id
   */
  regionId: string
  /**
   * 工作单位性质 字典
   */
  unitNature: string
  /**
   * 在编情况 字典
   */
  staffingStatus: string
  /**
   * 是否在专技岗位工作 字典
   */
  isZJPosition: string
  /**
   * 职称系列 字典
   */
  titleSeries: string
  /**
   * 职称专业
   */
  titleProfessional: string
  /**
   * 现有职称等级  字典
   */
  titleGrade: string
  /**
   * 现有职称资格名称
   */
  titleQualificationName: string
  /**
   * 现有职称有效范围  字典
   */
  titleEffectiveRange: string
  /**
   * 最高学历
   */
  highestEducationLevel: string
}

/**
 * 客户端信息返回
 */
export class ClientInfoResponse {
  /**
   * 客户端类型 | 1、Web 2、H5
@see ClientTypesConstant
   */
  clientType: number
  /**
   * 域名类型
@see DomainNameTypesConstant
   */
  domainNameType: number
  /**
   * 域名
   */
  domainName: string
  /**
   * 前端模板id
   */
  portalTemplateId: string
  /**
   * cnzz信息
   */
  cnzz: string
  /**
   * 目录名
   */
  dirName: string
}

/**
 * 门户信息
<AUTHOR>
 */
export class PortalInfoResponse1 {
  /**
   * id
   */
  id: string
  /**
   * 培训机构id
   */
  servicerId: string
  /**
   * 服务商类型 | 1培训机构、2课件供应商、3渠道商、4参训单位、5合同供应商、6供应商、7分销商、8企业
   */
  servicerType: number
  /**
   * 培训机构logo
   */
  institutionLogo: string
  /**
   * 门户类型
   */
  portalType: number
  /**
   * 门户标题
   */
  title: string
  /**
   * 门户logo
   */
  logo: string
  /**
   * 门户图标
   */
  icon: string
  /**
   * 友情链接类型 1-文本  2-图片
   */
  friendLinkType: number
  /**
   * 主题颜色
   */
  themeColor: string
  /**
   * 移动二维码
   */
  mobileQRCode: string
  /**
   * 移动二维码来源标识
1-系统生成 2-自定义
   */
  mobileQRCodeSign: number
  /**
   * 客服电话图片
   */
  CSPhonePicture: string
  /**
   * 客服电话
   */
  CSPhone: string
  /**
   * 客服咨询时间
   */
  CSCallTime: string
  /**
   * 在线客服代码内容id
   */
  CSOnlineCodeId: string
  /**
   * 培训流程图片
   */
  trainingFlowPicture: string
  /**
   * 底部内容（底部落款）
   */
  footContent: string
  /**
   * 宣传口号
   */
  slogan: string
  /**
   * 域名
   */
  domainName: string
  /**
   * H5域名(请求Web端门户信息才会使用这个字段 如果请求的是H5端的门户信息，该字段为null)
   */
  domainNameH5: string
  /**
   * 域名类型
系统默认域名 1
自有域名 2
@see com.fjhb.domain.basicdata.api.servicer.consts.DomainNameTypeConsts
   */
  domainNameType: number
  /**
   * 门户简介说明内容
   */
  content: string
  /**
   * 是否提供服务号
   */
  isProvideServiceAccount: boolean
  /**
   * 是否已发布
   */
  isPublished: boolean
  /**
   * 网校状态
1-正常  2-失效
   */
  onlineSchoolStatus: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime: string
  /**
   * 发布时间
   */
  publishedTime: string
  /**
   * 取消发布时间
   */
  unpublishedTime: string
  /**
   * cnzz信息
   */
  cnzz: string
  /**
   * 目录名
   */
  dirName: string
  /**
   * 网校模式
@see OnlineSchoolModesConstant
   */
  onlineSchoolModes: number
  /**
   * 是否到期
   */
  isExpired: boolean
  /**
   * 前端模板id
   */
  portalTemplateId: string
  /**
   * 企业客服微信
   */
  CSWechat: string
}

/**
 * 轮播图信息集合响应
<AUTHOR>
 */
export class BannerInfoListResponse {
  /**
   * 轮播图信息集合
   */
  bannerInfos: Array<BannerInfo>
}

/**
 * 轮播图信息
<AUTHOR>
 */
export class BannerInfo {
  /**
   * id
   */
  id: string
  /**
   * 所属门户id
   */
  portalId: string
  /**
   * 名称
   */
  name: string
  /**
   * 路径
   */
  path: string
  /**
   * 链接
   */
  link: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  isEnable: boolean
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 友情链接集合响应
<AUTHOR>
 */
export class FriendLinkListResponse {
  /**
   * 友情链接集合
   */
  friendLinkInfos: Array<FriendLinkInfo>
}

/**
 * 友情链接信息
<AUTHOR>
 */
export class FriendLinkInfo {
  /**
   * id
   */
  id: string
  /**
   * 所属门户id
   */
  portalId: string
  /**
   * 标题
   */
  title: string
  /**
   * 图片
   */
  picture: string
  /**
   * 友情链接类型
   */
  friendLinkType: number
  /**
   * 链接
   */
  link: string
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
}

/**
 * 栏目信息集合响应
<AUTHOR>
 */
export class MenuInfoListResponse {
  /**
   * 栏目信息集合
   */
  menuInfos: Array<MenuInfo>
}

/**
 * 栏目信息(前台)
<AUTHOR>
 */
export class MenuInfo {
  /**
   * 栏目id
   */
  id: string
  /**
   * 栏目类型 1-菜单  2-资讯
   */
  menuType: number
  /**
   * 来源类型 1-内置  2-用户创建
   */
  sourceType: number
  /**
   * 栏目名称
   */
  name: string
  /**
   * 父栏目id
   */
  parentId: string
  /**
   * 链接
   */
  link: string
  /**
   * 业务code（用于同步资讯、前端link相同的情况下做二次识别）
   */
  code: string
  /**
   * 排序
   */
  sort: number
  /**
   * 引用id
   */
  referenceId: string
}

export class TrainingPropertyResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TrainingPropertyResponse>
}

export class SimpleNewsByPublishCommonResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SimpleNewsByPublishCommonResponse>
}

export class CompleteNewsByPublishResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CompleteNewsByPublishResponse>
}

export class IndustryPropertyResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<IndustryPropertyResponse>
}

export class PortalResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PortalResponse>
}

export class SimpleNewsByPublishResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SimpleNewsByPublishResponse>
}

export class SimpleNewsByPublishResponseWithReviewCountPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SimpleNewsByPublishResponseWithReviewCount>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据服务商(网校id)、行业id、行业属性类别（业务、人员类别）查询行业属性id
   * @param industryPropertyInfoQueryRequest 查询条件
   * @return 行业属性id
   * @param query 查询 graphql 语法文档
   * @param industryPropertyInfoQueryRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType(
    industryPropertyInfoQueryRequest: IndustryPropertyInfoQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType,
    operation?: string
  ): Promise<Response<Array<IndustryPropertyInfoResponse>>> {
    return commonRequestApi<Array<IndustryPropertyInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { industryPropertyInfoQueryRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取地区专题用户信息
   * @param regionCode 地区code（必填）
   * @param query 查询 graphql 语法文档
   * @param regionCode 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAreaTrainingChannelStudentInfoInMyself(
    regionCode: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAreaTrainingChannelStudentInfoInMyself,
    operation?: string
  ): Promise<Response<AreaTrainingChannelInfoResponse>> {
    return commonRequestApi<AreaTrainingChannelInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { regionCode },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取指定门户下的获取轮播图集合
   * @param portalId 门户id
   * @return 轮播图集合响应
   * @param query 查询 graphql 语法文档
   * @param portalId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBannerListByPortalId(
    portalId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBannerListByPortalId,
    operation?: string
  ): Promise<Response<BannerInfoListResponse>> {
    return commonRequestApi<BannerInfoListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取轮播图集合
   * @param portalType 门户类型
   * @return 轮播图集合响应
   * @param query 查询 graphql 语法文档
   * @param portalType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBannerListByPortalType(
    portalType: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBannerListByPortalType,
    operation?: string
  ): Promise<Response<BannerInfoListResponse>> {
    return commonRequestApi<BannerInfoListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalType },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取轮播图集合
   * -分销平台使用 由于分销平台取上下文的方法与其他项目不一致
   * @param portalType 门户类型
   * @return 轮播图集合响应
   * @param query 查询 graphql 语法文档
   * @param portalType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBannerListByPortalTypeForFxpt(
    portalType: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBannerListByPortalTypeForFxpt,
    operation?: string
  ): Promise<Response<BannerInfoListResponse>> {
    return commonRequestApi<BannerInfoListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalType },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据id查询业务地区
   * @param id 业务地区编码
   * @return 业务地区
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBusinessRegionById(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBusinessRegionById,
    operation?: string
  ): Promise<Response<BusinessRegionResponse>> {
    return commonRequestApi<BusinessRegionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据地区编码和业务id查找一条地区
   * @param request 请求参数对象
   * @return 业务地区
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBusinessRegionTree(
    request: BusinessRegionTreeQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBusinessRegionTree,
    operation?: string
  ): Promise<Response<BusinessTreeRegionResponse>> {
    return commonRequestApi<BusinessTreeRegionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取网校合约客户端信息
   * @param query 查询 graphql 语法文档
   * @param servicerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getClientInfoByServicerId(
    servicerId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getClientInfoByServicerId,
    operation?: string
  ): Promise<Response<Array<ClientInfoResponse>>> {
    return commonRequestApi<Array<ClientInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { servicerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：获取当前登录的集体缴费管理员信息
   * 描述：查询当前登录的集体缴费管理员信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.collective.CollectiveInfoResponse
   * @date : 2022/4/2 14:21
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCollectiveInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCollectiveInfoInMyself,
    operation?: string
  ): Promise<Response<CollectiveInfoResponse>> {
    return commonRequestApi<CollectiveInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询资讯展示详细
   * @param commonRequest 通用查询条件，与查询列表相同的查询条件
   * @return NewsDetailWithPreviousAndNext 详细资讯信息及上下资讯id
   * @param query 查询 graphql 语法文档
   * @param commonRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCommonNewsDetailWithPreviousAndNext(
    commonRequest: NewsWithPreviousAndNextCommonRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCommonNewsDetailWithPreviousAndNext,
    operation?: string
  ): Promise<Response<NewsDetailWithPreviousAndNextCommonResponse>> {
    return commonRequestApi<NewsDetailWithPreviousAndNextCommonResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { commonRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯展示详细 -- 分销专属
   * @param commonRequest 通用查询条件，与查询列表相同的查询条件
   * @return NewsDetailWithPreviousAndNext 详细资讯信息及上下资讯id
   * @param query 查询 graphql 语法文档
   * @param commonRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCommonNewsDetailWithPreviousAndNextInDistributor(
    commonRequest: NewsWithPreviousAndNextCommonInDistributorRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCommonNewsDetailWithPreviousAndNextInDistributor,
    operation?: string
  ): Promise<Response<NewsDetailWithPreviousAndNextCommonResponse>> {
    return commonRequestApi<NewsDetailWithPreviousAndNextCommonResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { commonRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取指定门户下的友情链接集合
   * @param portalId 门户id
   * @return 友情链接集合响应
   * @param query 查询 graphql 语法文档
   * @param portalId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getFriendLinkListByPortalId(
    portalId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getFriendLinkListByPortalId,
    operation?: string
  ): Promise<Response<FriendLinkListResponse>> {
    return commonRequestApi<FriendLinkListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取友情链接集合
   * @param portalType 门户类型
   * @return 友情链接集合响应
   * @param query 查询 graphql 语法文档
   * @param portalType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getFriendLinkListByPortalType(
    portalType: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getFriendLinkListByPortalType,
    operation?: string
  ): Promise<Response<FriendLinkListResponse>> {
    return commonRequestApi<FriendLinkListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalType },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业信息
   * @param industryId 行业编号
   * @return 行业信息
   * @param query 查询 graphql 语法文档
   * @param industryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getIndustryInfo(
    industryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getIndustryInfo,
    operation?: string
  ): Promise<Response<IndustryResponse>> {
    return commonRequestApi<IndustryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { industryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业信息
   * @param industryId 行业编号
   * @return 行业信息
   * @param query 查询 graphql 语法文档
   * @param industryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getIndustryInfoV2(
    industryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getIndustryInfoV2,
    operation?: string
  ): Promise<Response<IndustryResponse>> {
    return commonRequestApi<IndustryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { industryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询行业属性 - 平台级
   * @param industryPropertyId 行业培训属性ID
   * @param query 查询 graphql 语法文档
   * @param industryPropertyId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getIndustryPropertyByIdInSubProject(
    industryPropertyId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getIndustryPropertyByIdInSubProject,
    operation?: string
  ): Promise<Response<IndustryPropertyResponse>> {
    return commonRequestApi<IndustryPropertyResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { industryPropertyId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询行業类型
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getIndustryType(
    params: { businessId?: string; code?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getIndustryType,
    operation?: string
  ): Promise<Response<GetIndustryTypeResponse>> {
    return commonRequestApi<GetIndustryTypeResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询职称等级信息
   * @param levelId 职称等级编号
   * @return 职称等级信息
   * @param query 查询 graphql 语法文档
   * @param levelId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getLeaderPositionLevel(
    levelId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getLeaderPositionLevel,
    operation?: string
  ): Promise<Response<LeaderPositionLevelResponse>> {
    return commonRequestApi<LeaderPositionLevelResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { levelId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取栏目信息集合
   * @param portalType 栏目类型
   * @return 栏目信息集合响应
   * @param query 查询 graphql 语法文档
   * @param portalType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getMenusByPortalType(
    portalType: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getMenusByPortalType,
    operation?: string
  ): Promise<Response<MenuInfoListResponse>> {
    return commonRequestApi<MenuInfoListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalType },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据分类id获取顶级分类信息
   * @param rootCategoryCode 顶级分类代码
   * @param code 分类代码
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getNewsCategoryId(
    params: { rootCategoryCode: string; code: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getNewsCategoryId,
    operation?: string
  ): Promise<Response<NewsCategoryResponse>> {
    return commonRequestApi<NewsCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯展示详细
   * @param newId 资讯id
   * @return NewsDetailWithPreviousAndNext 详细资讯信息及上下资讯id
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getNewsDetailWithPreviousAndNext(
    params: { newId: string; needAfterPublishTime?: boolean },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getNewsDetailWithPreviousAndNext,
    operation?: string
  ): Promise<Response<NewsDetailWithPreviousAndNext>> {
    return commonRequestApi<NewsDetailWithPreviousAndNext>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据id获取物理地区信息
   * @param id 物理地区编号
   * @return 物理地区信息
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getPhysicalRegionById(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPhysicalRegionById,
    operation?: string
  ): Promise<Response<PhysicalRegionResponse>> {
    return commonRequestApi<PhysicalRegionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据门户ID查询门户信息-详情接口
   * @param portalId                : 门户id
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.PortalResponse
   * @Author： wtl
   * @Date： 2022/10/19 10:32
   * @param query 查询 graphql 语法文档
   * @param portalId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getPortalInfoInSubProject(
    portalId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPortalInfoInSubProject,
    operation?: string
  ): Promise<Response<PortalResponse>> {
    return commonRequestApi<PortalResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * @param type 服务类型 0或1
   * @return 服务地区列表
   * 查询服务地区
   * @Date 2023/6/14 17:57
   * @param query 查询 graphql 语法文档
   * @param type 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getServiceOrIndustryRegion(
    type: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getServiceOrIndustryRegion,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { type },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * @description: 查询服务地区（这里只是对原方法进行二次筛选）
   * @author: linq
   * @date: 2023/8/22 15:54
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getServiceOrIndustryRegionByQuery(
    request: ServiceOrIndustryRegionQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getServiceOrIndustryRegionByQuery,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述 :获取我的学员信息接口
   * 描述：查询当前登录学员的详细信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse
   * @date : 2022/3/31 16:54
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudentInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentInfoInMyself,
    operation?: string
  ): Promise<Response<StudentInfoResponse>> {
    return commonRequestApi<StudentInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询指定培训类别信息
   * @param categoryId 培训类别编号
   * @return 培训类别信息
   * @param query 查询 graphql 语法文档
   * @param categoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTrainingCategoryInfo(
    categoryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTrainingCategoryInfo,
    operation?: string
  ): Promise<Response<TrainingCategoryResponse>> {
    return commonRequestApi<TrainingCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { categoryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询专题资讯展示详细
   * @param query 查询 graphql 语法文档
   * @param commonRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTrainingChannelCommonNewsDetailWithPreviousAndNext(
    commonRequest: TrainingChannelNewsWithPreviousAndNextCommonRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTrainingChannelCommonNewsDetailWithPreviousAndNext,
    operation?: string
  ): Promise<Response<NewsDetailWithPreviousAndNextCommonResponse>> {
    return commonRequestApi<NewsDetailWithPreviousAndNextCommonResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { commonRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 上下文服务商id、门户类型获取门户信息
   * @param portalType 门户类型(包含富文本信息) 1-WEB 2-移动端
   * @param servicerId 服务商ID
   * @return 门户信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTrainingInstitutionPortalInfo(
    params: { portalType: number; servicerId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTrainingInstitutionPortalInfo,
    operation?: string
  ): Promise<Response<PortalInfoResponse1>> {
    return commonRequestApi<PortalInfoResponse1>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询单位类型
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getUnitType(
    params: { businessId?: string; code?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getUnitType,
    operation?: string
  ): Promise<Response<GetUnitTypeResponse>> {
    return commonRequestApi<GetUnitTypeResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据年度编号查询业务年度
   * @param yearId 年度编号
   * @return 年度信息
   * @param query 查询 graphql 语法文档
   * @param yearId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getYearById(
    yearId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getYearById,
    operation?: string
  ): Promise<Response<BusinessYearResponse>> {
    return commonRequestApi<BusinessYearResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { yearId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表
   * 这个口不会去查业务那边的配置
   * @param categoryCode 行业属性分类代码
   * @return 培训属性列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listALLIndustryPropertyRootByCategory(
    params: { industryId: string; categoryCode: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listALLIndustryPropertyRootByCategory,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表V2
   * 这个口不会去查业务那边的配置
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listALLIndustryPropertyRootByCategoryV2(
    request: TrainingPropertyCommonRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listALLIndustryPropertyRootByCategoryV2,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下的所有培训属性列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listAllIndustryProperty(
    params: { industryPropertyId: string; industryId: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listAllIndustryProperty,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据下级字典获取上级字典信息
   * @param query 查询 graphql 语法文档
   * @param salveId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessDictionaryAcrossTypeBySalveId(
    salveId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessDictionaryAcrossTypeBySalveId,
    operation?: string
  ): Promise<Response<Array<BusinessDictionaryAcrossTypeResponse>>> {
    return commonRequestApi<Array<BusinessDictionaryAcrossTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { salveId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取指定业务地区的下一级业务地区列表
   * @param id 业务地区编号
   * @return 业务地区
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessRegionChildById(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessRegionChildById,
    operation?: string
  ): Promise<Response<Array<BusinessRegionResponse>>> {
    return commonRequestApi<Array<BusinessRegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据id列表查询业务地区
   * @param idList 业务地区编号，最大支持200个
   * @return 业务地区
   * @param query 查询 graphql 语法文档
   * @param idList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessRegionListById(
    idList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessRegionListById,
    operation?: string
  ): Promise<Response<Array<BusinessRegionResponse>>> {
    return commonRequestApi<Array<BusinessRegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { idList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 批量查询指定路径下行政区划名称
   * @param pathList 行政区划路径，格式：/350000/350100/350101
   * @return 路径下行政区划名称
   * @param query 查询 graphql 语法文档
   * @param pathList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessRegionNameMap(
    pathList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessRegionNameMap,
    operation?: string
  ): Promise<Response<Array<BusinessTreeRegionNameMap>>> {
    return commonRequestApi<Array<BusinessTreeRegionNameMap>>(
      SERVER_URL,
      {
        query: query,
        variables: { pathList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查找指定业务代码的地区树结构的指定父节点下的子节点
   * @param request 请求参数对象
   * @return 业务地区
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessRegionTreeChild(
    request: BusinessRegionTreeQueryChildrenRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessRegionTreeChild,
    operation?: string
  ): Promise<Response<Array<BusinessTreeRegionResponse>>> {
    return commonRequestApi<Array<BusinessTreeRegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查找指定业务代码的地区树结构的根节点
   * @param request 请求参数对象
   * @return 业务地区
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessRegionTreeRoot(
    request: BusinessRegionTreeQueryRootRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessRegionTreeRoot,
    operation?: string
  ): Promise<Response<Array<BusinessTreeRegionResponse>>> {
    return commonRequestApi<Array<BusinessTreeRegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listChildNewsCategory(
    params: { status: number; necId: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listChildNewsCategory,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listChildNewsCategoryInServicer(
    params: { status: number; necId: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listChildNewsCategoryInServicer,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询学历字典
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listEducationProperty(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listEducationProperty,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业信息
   * @param industryIds 行业编号，最大支持200个
   * @return 行业信息
   * @param query 查询 graphql 语法文档
   * @param industryIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryInfo(
    industryIds: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryInfo,
    operation?: string
  ): Promise<Response<Array<IndustryResponse>>> {
    return commonRequestApi<Array<IndustryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { industryIds },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业信息  v2
   * @param industryIds 行业编号，最大支持200个
   * @return 行业信息
   * @param query 查询 graphql 语法文档
   * @param industryIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryInfoV2(
    industryIds: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryInfoV2,
    operation?: string
  ): Promise<Response<Array<IndustryResponse>>> {
    return commonRequestApi<Array<IndustryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { industryIds },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定查询条件下的培训属性信息列表
   * @param request 查询条件
   * @return 培训属性信息
   * <AUTHOR>
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyByOnlineSchool(
    request: SchoolTrainingPropertyQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyByOnlineSchool,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定查询条件下的培训属性信息列表
   * @param request 查询条件
   * @return 培训属性信息
   * <AUTHOR>
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyByOnlineSchoolV2(
    request: SchoolTrainingPropertyQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyByOnlineSchoolV2,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询行业属性编号下行业属性分类列表
   * @param industryPropertyId 行业属性编号
   * @return 行业属性分类列表
   * @param query 查询 graphql 语法文档
   * @param industryPropertyId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyCategory(
    industryPropertyId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyCategory,
    operation?: string
  ): Promise<Response<Array<IndustryPropertyCategoryResponse>>> {
    return commonRequestApi<Array<IndustryPropertyCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { industryPropertyId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询行业属性编号下行业属性分类列表
   * @param industryPropertyId 行业属性编号
   * @return 行业属性分类列表
   * @param query 查询 graphql 语法文档
   * @param industryPropertyId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyCategoryV2(
    industryPropertyId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyCategoryV2,
    operation?: string
  ): Promise<Response<Array<IndustryPropertyCategoryResponse>>> {
    return commonRequestApi<Array<IndustryPropertyCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { industryPropertyId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下指定行业属性分类下指定培训属性编号下子节点培训属性列表
   * @param request 查询条件
   * @return 培训属性列表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyChildByCategory(
    request: TrainingPropertyQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyChildByCategory,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下指定行业属性分类下指定培训属性编号下子节点培训属性列表
   * @param request 查询条件
   * @return 培训属性列表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyChildByCategoryV2(
    request: TrainingPropertyQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyChildByCategoryV2,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表
   * @param industryPropertyId 行业属性编号
   * @param categoryCode       行业属性分类代码
   * @return 培训属性列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyRootByCategory(
    params: { industryPropertyId: string; categoryCode: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyRootByCategory,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表
   * @param industryPropertyId 行业属性编号
   * @param categoryCode       行业属性分类代码
   * @return 培训属性列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyRootByCategoryV2(
    params: { industryPropertyId: string; industryId: string; categoryCode: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyRootByCategoryV2,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询行业类型子节点列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryTypeChild(
    params: { businessId?: string; parentId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryTypeChild,
    operation?: string
  ): Promise<Response<Array<IndustryTypeResponse>>> {
    return commonRequestApi<Array<IndustryTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询行业类型父节点列表
   * @param businessId
   * @param query 查询 graphql 语法文档
   * @param businessId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryTypeRoot(
    businessId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryTypeRoot,
    operation?: string
  ): Promise<Response<Array<IndustryTypeResponse>>> {
    return commonRequestApi<Array<IndustryTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { businessId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询职称等级信息
   * @param levelIds 职称等级编号，最大支持200个
   * @return 职称等级信息
   * @param query 查询 graphql 语法文档
   * @param levelIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listLeaderPositionLevel(
    levelIds: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listLeaderPositionLevel,
    operation?: string
  ): Promise<Response<Array<LeaderPositionLevelResponse>>> {
    return commonRequestApi<Array<LeaderPositionLevelResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { levelIds },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询根节点的职称等级列表
   * @return 职称等级列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listLeaderPositionLevelRoot(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listLeaderPositionLevelRoot,
    operation?: string
  ): Promise<Response<Array<LeaderPositionLevelResponse>>> {
    return commonRequestApi<Array<LeaderPositionLevelResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listNewsCategoryTree(
    params: { status: number; parentId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listNewsCategoryTree,
    operation?: string
  ): Promise<Response<Array<NewsCategoryTreeResponse>>> {
    return commonRequestApi<Array<NewsCategoryTreeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取指定物理地区的下一级业务地区列表
   * @param id 物理地区编号
   * @return 物理地区
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listPhysicalRegionChildById(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listPhysicalRegionChildById,
    operation?: string
  ): Promise<Response<Array<PhysicalRegionResponse>>> {
    return commonRequestApi<Array<PhysicalRegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据id列表查询物理地区
   * @param idList 物理地区编号，最大支持200个
   * @return 物理地区
   * @param query 查询 graphql 语法文档
   * @param idList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listPhysicalRegionListById(
    idList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listPhysicalRegionListById,
    operation?: string
  ): Promise<Response<Array<PhysicalRegionResponse>>> {
    return commonRequestApi<Array<PhysicalRegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { idList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询弹窗公告列表
   * @param topNum top数量,在1~50之间
   * @return 资讯信息列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listPopUpsNews(
    params: { topNum: number; needAfterPublishTime?: boolean },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listPopUpsNews,
    operation?: string
  ): Promise<Response<Array<NewsInfoResponse>>> {
    return commonRequestApi<Array<NewsInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定门户的弹窗公告列表
   * @param topNum top数量,在1~50之间
   * @param needAfterPublishTime 是否需要过滤发布时间在当前时间之后的数据
   * @param portalId 门户id
   * @return 资讯信息列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listPopUpsNewsByPortalId(
    params: { topNum: number; needAfterPublishTime?: boolean; portalId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listPopUpsNewsByPortalId,
    operation?: string
  ): Promise<Response<Array<NewsInfoResponse>>> {
    return commonRequestApi<Array<NewsInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询浏览数最多的资讯列表
   * @param topNum top数量,在1~50之间
   * @return 资讯信息列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listReviewTopNews(
    params: { topNum: number; needAfterPublishTime?: boolean },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listReviewTopNews,
    operation?: string
  ): Promise<Response<Array<NewsInfoResponse>>> {
    return commonRequestApi<Array<NewsInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param status 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listRootNewsCategory(
    status: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listRootNewsCategory,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { status },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据主字典ID获取所有从字典 （用于上下级关系的字典类型）
   * @param masterId
   * @param salveDictionaryType
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listSalveDictionaryByMasterId(
    params: { masterId: string; salveDictionaryType: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listSalveDictionaryByMasterId,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询当前网校合约下的培训属性
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listServicerContractPropertyByCategory(
    request: ServicerContractPropertyByCategoryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listServicerContractPropertyByCategory,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 批量查询用户信息，不含敏感字段。目前只有头像和姓名
   * @param query 查询 graphql 语法文档
   * @param userIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listStudentInfoByUserIdInServicer(
    userIdList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listStudentInfoByUserIdInServicer,
    operation?: string
  ): Promise<Response<Array<StudentInfoResponse>>> {
    return commonRequestApi<Array<StudentInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { userIdList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询最多资讯的资讯分类列表
   * @param necId  排除的资讯分类id
   * @param topNum top数量,在1~50之间
   * @return NewsCategoryResponse 资讯分类信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listTopNewsCategory(
    params: { necId: string; topNum: number },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTopNewsCategory,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业下的培训类别子节点列表
   * @param industryId 行业编号
   * @param categoryId 培训类别编号
   * @return 培训类别列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listTrainingCategoryChild(
    params: { industryId: string; categoryId: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTrainingCategoryChild,
    operation?: string
  ): Promise<Response<Array<TrainingCategoryResponse>>> {
    return commonRequestApi<Array<TrainingCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定培训类别信息
   * @param categoryIds 培训类别编号，最大支持200个
   * @return 培训类别信息
   * @param query 查询 graphql 语法文档
   * @param categoryIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listTrainingCategoryInfo(
    categoryIds: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTrainingCategoryInfo,
    operation?: string
  ): Promise<Response<Array<TrainingCategoryResponse>>> {
    return commonRequestApi<Array<TrainingCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { categoryIds },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业下的根节点培训类别
   * @param industryId 行业编号
   * @return 培训类别列表
   * @param query 查询 graphql 语法文档
   * @param industryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listTrainingCategoryRoot(
    industryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTrainingCategoryRoot,
    operation?: string
  ): Promise<Response<Array<TrainingCategoryResponse>>> {
    return commonRequestApi<Array<TrainingCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { industryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询专题弹窗公告列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listTrainingChannelPopUpsNews(
    params: { topNum: number; needAfterPublishTime?: boolean; specialSubjectId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTrainingChannelPopUpsNews,
    operation?: string
  ): Promise<Response<Array<NewsInfoResponse>>> {
    return commonRequestApi<Array<NewsInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询浏览数最多的专题资讯列表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listTrainingChannelReviewTopNews(
    request: TrainingChannelReviewTopNewsCommonRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTrainingChannelReviewTopNews,
    operation?: string
  ): Promise<Response<Array<NewsInfoResponse>>> {
    return commonRequestApi<Array<NewsInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询单位类型子节点列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listUnitTypeChild(
    params: { businessId?: string; parentId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listUnitTypeChild,
    operation?: string
  ): Promise<Response<Array<UnitTypeResponse>>> {
    return commonRequestApi<Array<UnitTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询单位类型父节点列表
   * @param query 查询 graphql 语法文档
   * @param businessId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listUnitTypeRoot(
    businessId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listUnitTypeRoot,
    operation?: string
  ): Promise<Response<Array<UnitTypeResponse>>> {
    return commonRequestApi<Array<UnitTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { businessId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据年度编号列表查询业务年度
   * @param yearIdList 业务年度编号,最大支持200个
   * @return 业务年度列表
   * @param query 查询 graphql 语法文档
   * @param yearIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listYearListById(
    yearIdList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listYearListById,
    operation?: string
  ): Promise<Response<Array<BusinessYearResponse>>> {
    return commonRequestApi<Array<BusinessYearResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { yearIdList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表V2
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageALLIndustryPropertyRootByCategoryV2(
    params: { page?: Page; request?: TrainingPropertyCommonRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageALLIndustryPropertyRootByCategoryV2,
    operation?: string
  ): Promise<Response<TrainingPropertyResponsePage>> {
    return commonRequestApi<TrainingPropertyResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯分页列表-通用
   * @param request 信息中心资讯查询条件(资讯分类id与资讯发布的地区编码)
   * @return 资讯分页列表信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCommonSimpleNewsByPublish(
    params: { request?: NewsQueryCommonRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommonSimpleNewsByPublish,
    operation?: string
  ): Promise<Response<SimpleNewsByPublishCommonResponsePage>> {
    return commonRequestApi<SimpleNewsByPublishCommonResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取指定门户下的查询资讯分页列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCommonSimpleNewsByPublishByPortalId(
    params: { request?: DistributorNewsQueryCommonRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommonSimpleNewsByPublishByPortalId,
    operation?: string
  ): Promise<Response<SimpleNewsByPublishCommonResponsePage>> {
    return commonRequestApi<SimpleNewsByPublishCommonResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯分页列表-通用（服务商）
   * @param request 信息中心资讯查询条件(资讯分类id与资讯发布的地区编码)
   * @return 资讯分页列表信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCommonSimpleNewsByPublishInServicer(
    params: { request?: NewsQueryCommonRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommonSimpleNewsByPublishInServicer,
    operation?: string
  ): Promise<Response<SimpleNewsByPublishCommonResponsePage>> {
    return commonRequestApi<SimpleNewsByPublishCommonResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 首页根据条件查询资讯列表
   * @param newsFrontQueryRequest 首页资讯查询条件
   * @param page                  分页信息
   * @return
   * @throws InvalidProtocolBufferException
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCompleteNewsByCodeList(
    params: { newsFrontQueryRequest?: NewsFrontQueryByCodeRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCompleteNewsByCodeList,
    operation?: string
  ): Promise<Response<CompleteNewsByPublishResponsePage>> {
    return commonRequestApi<CompleteNewsByPublishResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 首页根据顶级分类代码查询资讯列表
   * @param newsFrontQueryRequest 首页资讯查询条件
   * @param page                  分页信息
   * @return
   * @throws InvalidProtocolBufferException
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCompleteNewsByRootCategoryCode(
    params: { newsFrontQueryRequest?: NewsFrontQueryRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCompleteNewsByRootCategoryCode,
    operation?: string
  ): Promise<Response<CompleteNewsByPublishResponsePage>> {
    return commonRequestApi<CompleteNewsByPublishResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询指定行业属性编号下指定行业属性分类下的培训属性列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageIndustryPropertyByCategoryInSubProject(
    params: { page?: Page; request?: PageIndustryPropertyByCategoryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageIndustryPropertyByCategoryInSubProject,
    operation?: string
  ): Promise<Response<TrainingPropertyResponsePage>> {
    return commonRequestApi<TrainingPropertyResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询行业属性 - 平台级
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageIndustryPropertyInSubProject(
    params: { page?: Page; request?: IndustryPropertyRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageIndustryPropertyInSubProject,
    operation?: string
  ): Promise<Response<IndustryPropertyResponsePage>> {
    return commonRequestApi<IndustryPropertyResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-分页查询门户信息-分页接口
   * @param page                    : 分页对象
   * @param request                 : 查询对象
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.PortalResponse>
   * @Author： wtl
   * @Date： 2022/10/19 10:40
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pagePortalInfoInSubProject(
    params: { page?: Page; request?: PortalRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pagePortalInfoInSubProject,
    operation?: string
  ): Promise<Response<PortalResponsePage>> {
    return commonRequestApi<PortalResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯分页列表
   * @param necId 资讯分类id
   * @return 资讯分页列表信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSimpleNewsByPublish(
    params: { necId: string; needAfterPublishTime?: boolean; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSimpleNewsByPublish,
    operation?: string
  ): Promise<Response<SimpleNewsByPublishResponsePage>> {
    return commonRequestApi<SimpleNewsByPublishResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯分页列表
   * @param request 信息中心资讯查询条件(资讯分类id与资讯发布的地区编码)
   * @return 资讯分页列表信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSimpleNewsByPublishAndAreaCodePath(
    params: { request?: NewsQueryByAreaCodePathRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSimpleNewsByPublishAndAreaCodePath,
    operation?: string
  ): Promise<Response<SimpleNewsByPublishResponsePage>> {
    return commonRequestApi<SimpleNewsByPublishResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯分页列表（服务商）
   * @param request 信息中心资讯查询条件(资讯分类id与资讯发布的地区编码)
   * @return 资讯分页列表信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSimpleNewsByPublishAndAreaCodePathInServicer(
    params: { request?: NewsQueryByAreaCodePathRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSimpleNewsByPublishAndAreaCodePathInServicer,
    operation?: string
  ): Promise<Response<SimpleNewsByPublishResponsePage>> {
    return commonRequestApi<SimpleNewsByPublishResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯分页列表，可以根据排序字段进行排序
   * @param request 信息中心资讯查询条件
   * @return 资讯分页列表信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSimpleNewsByPublishForOrder(
    params: { request?: NewsQueryForOrderRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSimpleNewsByPublishForOrder,
    operation?: string
  ): Promise<Response<SimpleNewsByPublishResponseWithReviewCountPage>> {
    return commonRequestApi<SimpleNewsByPublishResponseWithReviewCountPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询专题资讯分页列表-通用
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageTrainingChannelCommonSimpleNewsByPublish(
    params: { request?: TrainingChannelNewsQueryCommonRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageTrainingChannelCommonSimpleNewsByPublish,
    operation?: string
  ): Promise<Response<SimpleNewsByPublishCommonResponsePage>> {
    return commonRequestApi<SimpleNewsByPublishCommonResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 首页根据条件查询专题资讯列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageTrainingChannelCompleteNewsByCodeList(
    params: { newsFrontQueryRequest?: TrainingChannelNewsFrontQueryByCodeRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageTrainingChannelCompleteNewsByCodeList,
    operation?: string
  ): Promise<Response<CompleteNewsByPublishResponsePage>> {
    return commonRequestApi<CompleteNewsByPublishResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询当前网校下的学员数（有Redis缓存）
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async statisticStudentCountInServicer(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticStudentCountInServicer,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
