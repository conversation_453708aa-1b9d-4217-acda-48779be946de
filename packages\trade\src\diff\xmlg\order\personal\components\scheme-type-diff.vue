<route-meta>
{
"title": "方案类型下拉搜索选择器（单选）"
}
</route-meta>
<template>
  <el-cascader
    v-model="categoryIdList"
    :clearable="clearable"
    filterable
    @clear="categoryIdList = undefined"
    :props="props"
    :options="options"
    :placeholder="placeholder"
  />
</template>

<script lang="ts">
  import { Prop, Emit, Watch, Component, Vue } from 'vue-property-decorator'
  import { CascaderOptions } from '@hbfe/jxjy-admin-components/src/models/CascaderOptions'
  import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: true
    })
    checkStrictly: boolean

    @Prop({
      type: String,
      default: '请选择培训方案类型'
    })
    placeholder: string

    @Prop({
      type: Boolean,
      default: true
    })
    clearable: boolean

    @Prop({
      type: Array,
      default: () => new Array<string>()
    })
    value: Array<string>

    categoryIdList: Array<string> = new Array<string>()
    options: Array<CascaderOptions> = new Array<CascaderOptions>()
    props = {
      checkStrictly: true
    }

    @Watch('value')
    valueChange() {
      this.categoryIdList = this.value
    }

    @Emit('input')
    @Watch('categoryIdList')
    selectedChange() {
      return this.categoryIdList
    }

    created() {
      this.setProps()
      this.options = [
        {
          value: '-1',
          label: '培训班',
          children: [
            {
              value: 'chooseCourseLearning',
              label: '选课规则'
            },
            {
              value: 'autonomousCourseLearning',
              label: '自主选课'
            }
          ]
        },
        {
          value: SchemeTypeEnum[SchemeTypeEnum.trainingCooperation],
          label: '合作办学'
        }
      ]
    }
    setProps() {
      this.props.checkStrictly = this.checkStrictly
    }
  }
</script>
