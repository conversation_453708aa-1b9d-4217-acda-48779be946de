// 报名前校验结果对象
export class BuyCommodityValidationResult {
  // 是否允许报名，即页面点击我要报名时校验结果
  allowBuy: boolean
  /**
   * 错误码
   * 原：50001：已经买过该商品，50002：已经下单还未支付，50003：已经有正在学习的期别
   * 新：
   * 200：验证通过
   * 500：验证不通过，带 message
   * 30001：商品不存在
   * 30002：商品已下架
   * 30003：不支持当前渠道购买该商品
   * 30004：当前渠道已关闭
   * 40001：已经存在预定记录
   * 40002：已经存在预约记录
   * 40003：培训时间已结束
   * 40004: 订单已经生成支付了
   */
  errCode: string
  // 不允许报名的原因
  errMsg: string
  // 扩展订单号，当errCode=40001时，此值为待支付订单号
  orderNo?: string
}
