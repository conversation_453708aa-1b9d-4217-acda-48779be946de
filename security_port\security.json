{"security": ["ms-payment-v1.query.queryPayFlowStatus:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-payment-v1.mutation.getBatchPreParePayResult:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryQrScanPromptByPayFlowNo:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.deliveredOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":true}", "ms-assess-v1.mutation.batchRePushSchemeIndicator:{\"serviceName\":\"ms-assess-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.createJxjyCommonMenu:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.temporaryOrderUpdateOrderInfoInSubject:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.enableOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForService:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.tryComparePhoto:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.updateUserDatumCodePhoto:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.refreshConfigAndUpdate:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.executeAntiInspection:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":false}", "platform-account-v1.mutation.compensatingStudentAccount:{\"serviceName\":\"platform-account-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.mutation.openDistributionService:{\"authorizationRequired\":false}", "ms-order-repair-data-v1.mutation.orderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-repair-data-v1.mutation.batchOrderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.distributionAuthBatchProduct:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.createPricePlan:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.pricePlanSetSaleChannel:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissReturnOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissExchangeOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.clearStudentLearningRule:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-teachingplan-studentattendance-sds-v1.mutation.oneClickClock:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":false}"]}