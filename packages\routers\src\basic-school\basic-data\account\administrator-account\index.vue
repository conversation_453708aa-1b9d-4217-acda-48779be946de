<route-meta>
{
"isMenu": true,
"title": "平台管理员账号管理",
"sort": 1,
"icon": "icon_guanli"
}
</route-meta>
//
<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  import AdministratorAccount from '@hbfe/jxjy-admin-account/src/administrator-account/index.vue'
  @RoleTypeDecorator({
    query: [WXGLY],
    create: [WXGLY],
    resetPassword: [WXGLY],
    detail: [WXGLY],
    modify: [WXGLY],
    enable: [WXGLY],
    deactivate: [WXGLY]
  })
  export default class extends AdministratorAccount {}
</script>
