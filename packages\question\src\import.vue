<template>
  <el-main>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/question')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/question' }">试题管理</el-breadcrumb-item>
      <el-breadcrumb-item>批量创建试题</el-breadcrumb-item>
    </el-breadcrumb>
    <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
      <p>温馨提示：</p>
      <p>1. 导入试题，需选择下载对应题型的模板；</p>
      <p>2. 填写的信息，请按下载的模板填写要求，严格填写。</p>
      <p>
        3.
        批量创建失败的原因。请查阅“导入任务管理”并下载失败数据！您可以下载未导入成功的信息表，修改正确后再试。您仅需再次上传出错的记录即可。
      </p>
    </el-alert>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-row type="flex" justify="center">
          <el-col :sm="14" :lg="10">
            <el-steps direction="vertical" :active="2" class="m-vertical-steps">
              <el-step title="下载批量创建试题模板，填写要求信息">
                <div slot="description" @click="downloadModule">
                  <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-download">
                    单选题、多选题、判断题模板
                  </el-button>
                </div>
              </el-step>
              <el-step title="上传填好的试题表格">
                <div slot="description">
                  <hb-upload-file v-model="hbFileUploadResponse" :file-type="1">
                    <el-button type="primary" size="small" plain class="ml20 mt20" icon="el-icon-upload2"
                      >选择文件
                    </el-button>
                  </hb-upload-file>
                </div>
              </el-step>
            </el-steps>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc">
        <el-button type="primary" :loading="uploading" @click="commit">上传</el-button>
        <el-button @click="goBack">返回上一级</el-button>
      </div>
      <!-- 成功弹窗 -->
    </div>
    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导入成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导入任务查看-试题导入任务</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { isEmpty } from 'lodash'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import HbUploadFile from '@hbfe/jxjy-admin-components/src/hb-upload-file.vue'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import MutationQuestionAsynTask from '@api/service/management/resource/question/mutation/MutationQuestionAsynTask'
  import Downloader, { HeaderObj } from '@api/service/common/utils/Downloader'

  @Component({
    components: { HbUploadFile }
  })
  export default class extends Vue {
    // 报名模板地址
    signUpTemplate = ''
    /**
     * 文件上传之后的回调参数
     */
    hbFileUploadResponse = new HBFileUploadResponse()
    mutationQuestionAsynTask: MutationQuestionAsynTask = ResourceModule.mutationQuestionFactory.mutationQuestionAsynTask
    filePath = ''
    uploading = false
    exportSuccessVisible = false

    goBack() {
      this.$router.push('/resource/question')
    }

    /**
     * 确定上传
     */
    async commit() {
      this.uploading = true
      console.log(this.hbFileUploadResponse, 'hbFileUploadResponse')
      this.filePath = '/mfs' + this.hbFileUploadResponse.url
      const fileName = this.hbFileUploadResponse.fileName
      if (!fileName) {
        this.$message.warning('请选择上传的文件')
        return
      }
      const res = await this.mutationQuestionAsynTask.doImportChooseQuestions(this.filePath, fileName)
      if (res.isSuccess()) {
        this.$message.success('上传成功')
        this.exportSuccessVisible = true
      } else {
        this.$message.error('上传失败')
      }
      this.uploading = false
    }

    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/importtask',
        query: { type: '试题导入任务' }
      })
    }

    // 下载报名列表
    async downloadModule() {
      console.log(this.signUpTemplate)
      // debugger
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const download = new Downloader(this.signUpTemplate, '试题导入模板', header)
      download.download()
      // const link = document.createElement('a')
      // const resolver = this.$router.resolve({
      //   name: this.signUpTemplate
      // })
      // link.id = 'taskLink'
      // link.style.display = 'none'
      // link.href = resolver.location.name
      // const urlArr = link.href.split('.'),
      //   typeName = urlArr.pop()
      // link.setAttribute('download', '试题导入模板')
      // document.body.appendChild(link)
      // link.click()
      // link.remove()
    }

    /**
     * 组件初始化加载
     */
    async created() {
      this.signUpTemplate =
        '/mfs/resource/file/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/template/question/试题模板.xlsx'
    }
  }
</script>
