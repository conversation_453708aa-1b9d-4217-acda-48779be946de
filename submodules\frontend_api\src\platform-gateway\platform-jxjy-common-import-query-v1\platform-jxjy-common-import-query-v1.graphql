schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询导入/导出任务执行情况
		@param request 任务查询参数
		@param page    分页数据
		@return 任务执行情况分页
	"""
	queryForTask(request:QueryForTaskInfoRequest,page:Page):QueryTaskInfoResponsePage @page(for:"QueryTaskInfoResponse")
	"""查询导入模板
		@param request 任务类型
		@return 模板地址
	"""
	queryImportTemplate(request:QueryImportTemplateRequest):String
}
type Mutation {
	"""导出全部数据
		@param request 导出异常数据请求
		@return 导出异常数据响应
	"""
	exportAll(request:ExportTaskDataRequest):ExportTaskDataResponse
	"""导出异常数据
		@param request 导出异常数据请求
		@return 导出异常数据响应
	"""
	exportFail(request:ExportTaskDataRequest):ExportTaskDataResponse
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""导出任务数据地址请求"""
input ExportTaskDataRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.ExportTaskDataRequest") {
	"""任务编号"""
	taskId:String!
}
"""<AUTHOR>
	@date 2022/12/29 14:02
"""
input QueryImportTemplateRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.QueryImportTemplateRequest") {
	taskCategory:String
}
"""查询批量任务的执行情况请求
	<AUTHOR>
	@since 2022/5/19
"""
input QueryForTaskInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.service.request.QueryForTaskInfoRequest") {
	"""代理账户TOKEN"""
	token:String
	"""任务名称"""
	taskName:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""任务创建起始时间"""
	createStartTime:DateTime
	"""任务创建截止时间"""
	createEndTime:DateTime
	"""起始执行时间"""
	executeStartTime:DateTime
	"""结束执行时间"""
	executeEndTime:DateTime
	"""任务类型"""
	taskCategory:String
}
"""各状态及执行结果对应数量
	<AUTHOR>
"""
type EachStateCount @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.entities.EachStateCount") {
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	state:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int
	"""数量"""
	count:Int!
}
"""导出任务数据地址响应
	<AUTHOR>
	@date 2022/11/7 14:12
"""
type ExportTaskDataResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.ExportTaskDataResponse") {
	"""文件名称"""
	fileName:String
	"""文件路径"""
	filePath:String
}
"""执行情况信息请求响应体
	<AUTHOR>
	@since 2022/5/19
"""
type QueryTaskInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.service.response.QueryTaskInfoResponse") {
	"""任务编号"""
	id:String
	"""【必填】平台编号"""
	platformId:String
	"""【必填】平台版本编号"""
	platformVersionId:String
	"""【必填】项目编号"""
	projectId:String
	"""【必填】子项目编号"""
	subProjectId:String
	"""任务名称"""
	name:String
	"""任务分类"""
	category:String
	"""所属批次单编号"""
	batchNo:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""处理信息"""
	message:String
	"""创建时间"""
	createdTime:DateTime
	"""就绪时间"""
	alreadyTime:DateTime
	"""执行时间"""
	executingTime:DateTime
	"""完成时间"""
	completedTime:DateTime
	"""创建人id（操作人编号）"""
	createUserId:String
	"""各状态及执行结果对应数量集合
		总数：全部数量之和
		成功数：result = 1数量之和
		失败数：result = 2数量之和
	"""
	eachStateCounts:[EachStateCount]
	"""处理总条数"""
	totalCount:Int!
	"""成功条数"""
	successCount:Int!
	"""失败条数"""
	failCount:Int!
	"""导入进度 1-导入成功 2-导入中 3-导入失败 4-部分成功"""
	importProgress:Int!
}

scalar List
type QueryTaskInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [QueryTaskInfoResponse]}
