<template>
  <div style="display: flex" v-if="env.isProxyInnerNetworkEnv">
    <div class="development-tool">
      <i
        class="el-icon-s-operation tool-operate"
        :class="{ true: 'red' }[submitForm.openMock]"
        @click="drawer = true"
      ></i>
      <el-drawer title="开发工具" :visible.sync="drawer" size="290px">
        <el-alert type="error" :closable="false">
          <span class="info">远程权限拦截开启状态：</span>
          <span class="status"> 【{{ isPermissionOpen }}】</span>
        </el-alert>
        <div v-if="isDev" class="mt10">
          <div shadow="never" class="links mt10">
            <div @click="openMockClient()">Mock</div>
            <div @click="openYapi()">Yapi</div>
          </div>
          <el-form ref="form" :model="submitForm" label-width="130px" size="mini" style="padding: 20px">
            <el-form-item label="使用远程菜单：">
              <el-switch v-model="submitForm.openRemoteMenu" :active-value="true" :inactive-value="false"></el-switch>
            </el-form-item>
            <el-form-item label="权限控制：">
              <el-switch
                v-model="submitForm.permissionControl"
                :active-value="true"
                :inactive-value="false"
              ></el-switch>
            </el-form-item>
            <el-form-item label="开启全局 mock：">
              <el-switch v-model="submitForm.openMock" :active-value="true" :inactive-value="false"></el-switch>
            </el-form-item>
            <el-form-item label="模拟服务商：">
              <el-switch
                v-model="submitForm.enableProviderMock"
                :active-value="true"
                :inactive-value="false"
              ></el-switch>
              <el-select
                v-if="submitForm.enableProviderMock"
                v-model="submitForm.provider"
                placeholder="请选择"
                style="margin-top: 15px"
              >
                <el-option :key="0" label="超管" :value="0"></el-option>
                <el-option :key="1" label="机构" :value="1"></el-option>
                <el-option :key="4" label="参训单位" :value="4"></el-option>
                <el-option :key="2" label="渠道" :value="3"></el-option>
                <el-option :key="3" label="供应商" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <el-alert type="error" :closable="false" class="mt10 branch-info">
          <span class="info">当前运行分支：</span>
          <span class="status"> {{ branch }}</span>
        </el-alert>
      </el-drawer>
    </div>
  </div>
</template>

<script lang="ts">
  import { merge } from 'lodash'
  import { Component, Vue, Watch } from 'vue-property-decorator'
  import DevToolsModule from '@/store/devtools/DevToolsModule'
  import DevelopmentSettings from '@/store/devtools/models/DevelopmentSettings'
  import ConfigCenterModule from '@api/service/common/config-center/ConfigCenter'
  import Env from '@api/service/common/utils/Env'

  @Component
  export default class extends Vue {
    providers: Array<string> = new Array<string>()
    // 是否打开抽屉
    drawer = false
    // 是否是研发环境
    isDev = process.env.NODE_ENV === 'development'
    // 当前运行分支是多少
    branch = process.env.GIT_INFO_BRANCH
    // 环境单例
    env = Env

    /**
     * 判断当前的远程的安全对象控制的按钮是否是关闭的状态。
     */
    get isPermissionOpen() {
      const disableAuthorization = ConfigCenterModule.getApplicationByName('application.disableAuthorization')
      return disableAuthorization == 'true' ? '关闭' : '开启'
    }

    submitForm: DevelopmentSettings = merge<DevelopmentSettings, DevelopmentSettings>(
      new DevelopmentSettings(),
      DevToolsModule.developmentSettings
    )
    storeKey = 'developmentConfig'

    @Watch('submitForm', {
      deep: true
    })
    submitFormChange() {
      DevToolsModule.setDevelopmentSetting(
        merge<DevelopmentSettings, DevelopmentSettings>(new DevelopmentSettings(), this.submitForm)
      )
    }

    openMockClient() {
      window.open(`
      ${process.env.VUE_APP_DEVELOPMENT_SERVER}/clients/mock-client/#/?appKey=${process.env.VUE_APP_COMMON_MS_APP_KEY}
      `)
    }

    openYapi() {
      window.open(`
      ${process.env.VUE_APP_YAPI_SERVER}project/${process.env.VUE_APP_YAPI_COMMON_MICRO_SERVICE_PROJECT_ID}/interface/api
      `)
    }
  }
</script>

<style lang="scss">
  .development-tool {
    color: white;
    display: flex;
    align-items: center;
    align-content: center;

    .branch-info {
      position: absolute;
      bottom: 0;
      margin-bottom: 10px;
    }

    .tool-operate {
      cursor: pointer;
      padding: 10px;

      &:hover {
        background: #999999;
      }

      &.red {
        background: #e72323;
      }
    }

    .links {
      font-size: 12px;
      color: black;
      padding: 0 20px;
      display: flex;
      margin-top: 15px;

      div {
        margin-left: 10px;
        cursor: pointer;
        border: 1px solid #999;
        padding: 3px 10px;
        border-radius: 4px;

        &::first-letter {
          font-size: 15px;
          color: #ce223f;
          font-weight: bold;
        }
      }
    }
  }
</style>
