import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 换班状态
 */
export enum ExchangeStatusEnum {
  // 1：换入
  Import = 1,
  // 2：换出
  Export
}

/**
 * @description 换班状态
 */
class ExchangeStatusList extends AbstractEnum<ExchangeStatusEnum> {
  static enum = ExchangeStatusEnum
  constructor(status?: ExchangeStatusEnum) {
    super()
    this.current = status
    this.map.set(ExchangeStatusEnum.Import, '换入')
    this.map.set(ExchangeStatusEnum.Export, '换出')
  }
}

export default new ExchangeStatusList()
