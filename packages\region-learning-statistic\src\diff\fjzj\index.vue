<route-meta>
{
"isMenu": true,
"title": "地区学习统计",
"sort": 4,
"icon": "icon-tongjibaobiao"
}
</route-meta>
<template>
  <div>
    <RegionLearningStatisticFjzj ref="regionLearningStatistic">
      <template #sale-channel="{ localSkuProperty }">
        <el-form-item label="销售渠道">
          <biz-sale-channel-select v-model="localSkuProperty.tradeChannels"></biz-sale-channel-select>
        </el-form-item>
      </template>
    </RegionLearningStatisticFjzj>
  </div>
</template>
<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import RegionLearningStatistic from '@hbfe/jxjy-admin-regionLearningStatistic/src/index.vue'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-common/src/models/sku'
  import { StudentSchemeLearningRequestVo } from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
  import bizSaleChannelSelect from '@hbfe/jxjy-admin-components/src/biz-sale-channel-select.vue'

  class NewSchemeSkuProperty extends SchemeSkuProperty {
    tradeChannels: number = null
  }
  @Component
  class RegionLearningStatisticFjzj extends RegionLearningStatistic {
    /**
     * 本地sku属性
     */
    localSkuProperty = new NewSchemeSkuProperty()

    /**
     * 获取本地sku属性
     */
    getPageQueryParams() {
      this.getLocalSkuProperty()
      this.learningReportFormsRequest.scheme.schemeName = this.schemeName || undefined
      this.configureTrainSchemeQueryParam()
      this.learningReportFormsRequest.learningRegister.saleChannels = this.localSkuProperty.tradeChannels
        ? [this.localSkuProperty.tradeChannels]
        : undefined
    }
  }
  @Component({
    components: {
      RegionLearningStatisticFjzj,
      bizSaleChannelSelect
    }
  })
  export default class extends Vue {
    @Ref('regionLearningStatistic') regionLearningStatistic: RegionLearningStatisticFjzj
    /**
     * 本地sku属性
     */
    localSkuProperty = new NewSchemeSkuProperty()

    mounted() {
      this.regionLearningStatistic.localSkuProperty = this.localSkuProperty
    }
  }
</script>
