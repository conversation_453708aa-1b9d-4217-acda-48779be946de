<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--重新开票信息-->
        <el-button @click="dialog15 = true" type="primary" class="f-mr20 f-mb20">重新开票</el-button>
        <el-drawer
          title="重新开票"
          :visible.sync="dialog15"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-tit">
              <span class="tit-txt">原发票信息</span>
            </div>
            <el-form ref="form" :model="form" label-width="150px" class="m-text-form is-column f-mt10">
              <el-form-item label="发票类型：" class="is-text">增值税电子普通发票（自动发票）</el-form-item>
              <el-form-item label="发票抬头：" class="is-text">【单位】福建华博教育科技股份有限公司</el-form-item>
              <el-form-item label="统一社会信用代码：" class="is-text">31350100761760186L</el-form-item>
            </el-form>
            <el-divider class="m-divider"></el-divider>
            <div class="m-tit">
              <span class="tit-txt">修改的发票信息</span>
            </div>
            <el-form ref="form" :model="form" label-width="150px" class="m-form f-mt10">
              <el-form-item label="发票类型：" class="is-text">增值税电子普通发票</el-form-item>
              <el-form-item label="发票抬头：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="个人"></el-radio>
                  <el-radio label="单位"></el-radio>
                </el-radio-group>
                <div class="f-mt10">
                  <!--选择单位时改为 请输入单位名称-->
                  <el-input v-model="form.name" clearable placeholder="请输入个人名字" class="form-l" />
                </div>
              </el-form-item>
              <el-form-item label="统一社会信用代码：" required>
                <el-input v-model="form.name" clearable placeholder="请输入18位统一社会信用代码" class="form-l" />
              </el-form-item>
              <el-form-item label=" " class="is-text">
                <span class="f-co">注：开企业抬头发票须填写统一社会信用代码，以免影响报销。</span>
              </el-form-item>
              <div class="bg-gray f-plr20 f-mb20 f-pb10">
                <div class="f-pt5 f-pb10">
                  <el-divider content-position="left">
                    <span class="f-cr">* 以下内容请根据需要填写，请全部填写或者全部不填写。</span>
                  </el-divider>
                </div>
                <el-form-item label="开户银行：">
                  <el-input v-model="form.name" clearable placeholder="请输入开户银行" class="form-l" />
                </el-form-item>
                <el-form-item label="开户帐号：">
                  <el-input v-model="form.name" clearable placeholder="请输入开户帐号" class="form-l" />
                </el-form-item>
                <el-form-item label="注册电话：">
                  <el-input v-model="form.name" clearable placeholder="请输入单位注册电话" class="form-m" />
                </el-form-item>
                <el-form-item label="注册地址：">
                  <el-input v-model="form.name" clearable placeholder="请输入单位注册地址" />
                </el-form-item>
              </div>
              <el-form-item label="手机号：" required>
                <el-input v-model="form.name" clearable placeholder="请输入11位手机号码" class="form-l" />
              </el-form-item>
              <el-form-item label="电子邮箱：" required>
                <el-input v-model="form.name" clearable placeholder="请输入电子邮箱" class="form-l" />
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">保存并重开发票</el-button>
          </div>
        </el-drawer>
        <!--设置自动开票时间-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20 f-mb20">设置自动开票时间</el-button>
        <el-drawer title="提示" :visible.sync="dialog1" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              设置电子发票，订单索要发票多少天后自动开票！
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt40">
                  <el-form-item label="设置天数：">
                    <el-input v-model="form.desc" class="input-num" />
                    <span class="f-ml10">天</span>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--操作记录-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20 f-mb20">操作记录</el-button>
        <el-drawer title="操作记录" :visible.sync="dialog2" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <div class="f-mt20 f-mlr40">
              <el-timeline>
                <el-timeline-item>
                  【读取操作者名称】在 【读取操作时间2019-11-07 10:48:55】 开票了【培训发票】。
                </el-timeline-item>
                <el-timeline-item>
                  【读取操作者名称】在 【读取操作时间2019-11-07 10:48:55】 开票了【培训发票】。
                </el-timeline-item>
              </el-timeline>
            </div>
            <el-alert type="info" :closable="false" class="m-alert">
              <div class="f-ptb30 f-tc">该发票暂时还没有操作记录！</div>
            </el-alert>
          </div>
        </el-drawer>
        <!--修改发票信息-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20 f-mb20">修改发票信息</el-button>
        <el-drawer
          title="修改发票信息"
          :visible.sync="dialog3"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-tit">
              <span class="tit-txt">原发票信息</span>
            </div>
            <el-form ref="form" :model="form" label-width="150px" class="m-text-form is-column f-mt10">
              <el-form-item label="发票类型：" class="is-text">增值税电子普通发票</el-form-item>
              <el-form-item label="发票抬头：" class="is-text">【个人】林林一</el-form-item>
            </el-form>
            <el-divider class="m-divider"></el-divider>
            <div class="m-tit">
              <span class="tit-txt">修改的发票信息</span>
            </div>
            <el-form ref="form" :model="form" label-width="150px" class="m-form f-mt10">
              <el-form-item label="发票类型：" class="is-text">增值税电子普通发票</el-form-item>
              <el-form-item label="发票抬头：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="个人"></el-radio>
                  <el-radio label="单位"></el-radio>
                </el-radio-group>
                <div class="f-mt10">
                  <!--选择单位时改为 请输入单位名称-->
                  <el-input v-model="form.name" clearable placeholder="请输入个人名字" class="form-l" />
                </div>
              </el-form-item>
              <el-form-item label="统一社会信用代码：" required>
                <el-input v-model="form.name" clearable placeholder="请输入18位统一社会信用代码" class="form-l" />
              </el-form-item>
              <el-form-item label=" " class="is-text">
                <span class="f-co">注：开企业抬头发票须填写统一社会信用代码，以免影响报销。</span>
              </el-form-item>
              <div class="bg-gray f-plr20 f-mb20 f-pb10">
                <div class="f-pt5 f-pb10">
                  <el-divider content-position="left">
                    <span class="f-cr">* 以下内容请根据需要填写，请全部填写或者全部不填写。</span>
                  </el-divider>
                </div>
                <el-form-item label="开户银行：">
                  <el-input v-model="form.name" clearable placeholder="请输入开户银行" class="form-l" />
                </el-form-item>
                <el-form-item label="开户帐号：">
                  <el-input v-model="form.name" clearable placeholder="请输入开户帐号" class="form-l" />
                </el-form-item>
                <el-form-item label="注册电话：">
                  <el-input v-model="form.name" clearable placeholder="请输入单位注册电话" class="form-m" />
                </el-form-item>
                <el-form-item label="注册地址：">
                  <el-input v-model="form.name" clearable placeholder="请输入单位注册地址" />
                </el-form-item>
              </div>
              <el-form-item label="手机号：" required>
                <el-input v-model="form.name" clearable placeholder="请输入11位手机号码" class="form-l" />
              </el-form-item>
              <el-form-item label="电子邮箱：" required>
                <el-input v-model="form.name" clearable placeholder="请输入电子邮箱" class="form-l" />
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">保存发票信息</el-button>
          </div>
        </el-drawer>
        <!--修改发票信息（专票）-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20 f-mb20">修改发票信息（专票）</el-button>
        <el-drawer
          title="修改发票信息"
          :visible.sync="dialog4"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-tit">
              <span class="tit-txt">原发票信息</span>
            </div>
            <el-form ref="form" :model="form" label-width="170px" class="m-text-form f-mt10">
              <el-col :span="12">
                <el-form-item label="发票类型：">增值税专用发票</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发票抬头：">【单位】福建华博教育科技股份有限公司</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="统一社会信用代码：">35212544125658956L</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开户银行：">福建建设银行</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开户帐号：">215421512542152</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册电话：">8312008</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册地址：">工业路611号</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册地址：">工业路611号</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="统一社会信用代码证：">
                  <el-image
                    src="/assets/images/web-default-banner.jpg"
                    :preview-src-list="['/assets/images/web-default-banner.jpg']"
                    class="course-pic is-small"
                  >
                  </el-image>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开户许可证：">
                  <el-image
                    src="/assets/images/web-default-banner.jpg"
                    :preview-src-list="['/assets/images/web-default-banner.jpg']"
                    class="course-pic is-small"
                  >
                  </el-image>
                </el-form-item>
              </el-col>
            </el-form>
            <div class="m-sub-tit">
              <span class="tit-txt">配送信息</span>
            </div>
            <el-form ref="form" :model="form" label-width="150px" class="m-text-form f-mt10">
              <el-col :span="12">
                <el-form-item label="配送方式：">自取</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="收货人：">林林一</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号：">13003831002</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="证件号：">350121199112025689</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="自取地址：">工业路611号</el-form-item>
              </el-col>
            </el-form>
            <el-divider class="m-divider"></el-divider>
            <div class="m-tit">
              <span class="tit-txt">修改的发票信息</span>
            </div>
            <el-form ref="form" :model="form" label-width="170px" class="m-form f-mt10">
              <el-form-item label="发票类型：" class="is-text">增值税专用发票</el-form-item>
              <el-form-item label="发票抬头：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="个人"></el-radio>
                  <el-radio label="单位"></el-radio>
                </el-radio-group>
                <div class="f-mt10">
                  <!--选择单位时改为 请输入单位名称-->
                  <el-input v-model="form.name" clearable placeholder="请输入个人名字" class="form-l" />
                </div>
              </el-form-item>
              <el-form-item label="统一社会信用代码：" required>
                <el-input v-model="form.name" clearable placeholder="请输入18位统一社会信用代码" class="form-l" />
              </el-form-item>
              <el-form-item label=" " class="is-text">
                <span class="f-co">注：开企业抬头发票须填写统一社会信用代码，以免影响报销。</span>
              </el-form-item>
              <el-form-item label="开户银行：" required>
                <el-input v-model="form.name" clearable placeholder="请输入开户银行" class="form-l" />
              </el-form-item>
              <el-form-item label="开户帐号：" required>
                <el-input v-model="form.name" clearable placeholder="请输入开户帐号" class="form-l" />
              </el-form-item>
              <el-form-item label="注册电话：" required>
                <el-input v-model="form.name" clearable placeholder="请输入单位注册电话" class="form-m" />
              </el-form-item>
              <el-form-item label="注册地址：" required>
                <el-input v-model="form.name" clearable placeholder="请输入单位注册地址" />
              </el-form-item>
              <el-form-item label="统一社会信用代码证：">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :auto-upload="false"
                  class="m-pic-upload proportion-pic is-small"
                >
                  <div slot="default" class="upload-placeholder">
                    <i class="el-icon-plus"></i>
                    <p class="txt">上传图片</p>
                  </div>
                  <div slot="file" slot-scope="{ file }" class="img-file">
                    <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                    <div class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </div>
                  </div>
                </el-upload>
                <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                  <img :src="dialogImageUrl" alt="" />
                </el-dialog>
              </el-form-item>
              <el-form-item label="开户许可证：">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :auto-upload="false"
                  class="m-pic-upload proportion-pic is-small"
                >
                  <div slot="default" class="upload-placeholder">
                    <i class="el-icon-plus"></i>
                    <p class="txt">上传图片</p>
                  </div>
                  <div slot="file" slot-scope="{ file }" class="img-file">
                    <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                    <div class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </div>
                  </div>
                </el-upload>
                <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                  <img :src="dialogImageUrl" alt="" />
                </el-dialog>
              </el-form-item>
            </el-form>
            <div class="m-sub-tit">
              <span class="tit-txt">配送信息</span>
            </div>
            <el-form ref="form" :model="form" label-width="150px" class="m-form f-mt10">
              <el-form-item label="配送方式：">
                <el-radio-group v-model="form.resource">
                  <el-radio label="快递"></el-radio>
                  <el-radio label="自取"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="收货人：" required>
                <el-input v-model="form.name" clearable placeholder="请输入收货人" class="form-s" />
              </el-form-item>
              <el-form-item label="手机号：" required>
                <el-input v-model="form.name" clearable placeholder="请输入手机号" class="form-m" />
              </el-form-item>
              <el-form-item label="所在地区：" required>
                <el-cascader clearable :options="cascader" placeholder="请选择所在地区" class="form-m" />
              </el-form-item>
              <el-form-item label="详细地址：" required>
                <el-input v-model="form.name" clearable placeholder="请输入详细地址" />
              </el-form-item>
              <el-form-item label="邮编：">
                <el-input v-model="form.name" clearable placeholder="请输入邮编" class="form-s" />
              </el-form-item>
              <el-form-item label="姓名：" required>
                <el-input v-model="form.name" clearable placeholder="请输入姓名" class="form-s" />
              </el-form-item>
              <el-form-item label="手机号：" required>
                <el-input v-model="form.name" clearable placeholder="请输入手机号" class="form-m" />
              </el-form-item>
              <el-form-item label="身份证号：" required>
                <el-input v-model="form.name" clearable placeholder="请输入身份证号" class="form-m" />
              </el-form-item>
              <el-form-item label=" " class="is-text">
                <!--选中添加 is-checked-->
                <div class="take-address is-checked">
                  <div class="label"><i class="hb-iconfont icon-finish"></i></div>
                  <el-form ref="form" :model="form" label-width="auto" class="m-form">
                    <el-form-item label="领取时间：" class="is-text">
                      读取下单时候选取的自取点的信息（领取时间）
                    </el-form-item>
                    <el-form-item label="领取地点：" class="is-text">
                      读取下单时候选取的自取点的信息（领取地点）
                    </el-form-item>
                    <el-form-item label="备注：" class="is-text">
                      读取下单时候选取的自取点的信息（备注）
                    </el-form-item>
                  </el-form>
                </div>
                <div class="take-address">
                  <div class="label"><i class="hb-iconfont icon-finish"></i></div>
                  <el-form ref="form" :model="form" label-width="auto" class="m-form">
                    <el-form-item label="领取时间：" class="is-text">
                      读取下单时候选取的自取点的信息（领取时间）
                    </el-form-item>
                    <el-form-item label="领取地点：" class="is-text">
                      读取下单时候选取的自取点的信息（领取地点）
                    </el-form-item>
                    <el-form-item label="备注：" class="is-text">
                      读取下单时候选取的自取点的信息（备注）
                    </el-form-item>
                  </el-form>
                </div>
                <div class="take-address">
                  <div class="label"><i class="hb-iconfont icon-finish"></i></div>
                  <el-form ref="form" :model="form" label-width="auto" class="m-form">
                    <el-form-item label="领取时间：" class="is-text">
                      读取下单时候选取的自取点的信息（领取时间）
                    </el-form-item>
                    <el-form-item label="领取地点：" class="is-text">
                      读取下单时候选取的自取点的信息（领取地点）
                    </el-form-item>
                    <el-form-item label="备注：" class="is-text">
                      读取下单时候选取的自取点的信息（备注）
                    </el-form-item>
                  </el-form>
                </div>
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">保存发票信息</el-button>
          </div>
        </el-drawer>
        <!--处理发票-->
        <el-button @click="dialog5 = true" type="primary" class="f-mr20 f-mb20">处理发票</el-button>
        <el-drawer title="处理发票" :visible.sync="dialog5" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="发票票号：" required>
                    <el-input v-model="form.desc" placeholder="请输入发票号码" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--处理发票（专票）-->
        <el-button @click="dialog6 = true" type="primary" class="f-mr20 f-mb20">处理发票（专票）</el-button>
        <el-drawer title="处理发票" :visible.sync="dialog6" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="查询验证码：" class="is-text">25632</el-form-item>
                  <el-form-item label="发票代码：" class="is-text">25632</el-form-item>
                  <el-form-item label="发票号码：" class="is-text">25632</el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--处理发票（开票）-->
        <el-button @click="dialog7 = true" type="primary" class="f-mr20 f-mb20">处理发票（开票）</el-button>
        <el-drawer title="处理发票" :visible.sync="dialog7" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="查询验证码：" required>
                    <el-input v-model="form.desc" placeholder="请输入查询验证码" />
                  </el-form-item>
                  <el-form-item label="发票代码：">
                    <el-input v-model="form.desc" placeholder="请输入发票代码" />
                  </el-form-item>
                  <el-form-item label="发票号码：" required>
                    <el-input v-model="form.desc" placeholder="请输入发票号码" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--作废发票-->
        <el-button @click="dialog8 = true" type="primary" class="f-mr20 f-mb20">作废发票</el-button>
        <el-drawer title="作废发票" :visible.sync="dialog8" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="发票号：" class="is-text">587456815</el-form-item>
                  <el-form-item label="发票抬头：" class="is-text">【个人】圣尼古拉斯的雪天</el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">作废发票</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--作废发票（专票）-->
        <el-button @click="dialog9 = true" type="primary" class="f-mr20 f-mb20">作废发票（专票）</el-button>
        <el-drawer title="作废发票" :visible.sync="dialog9" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              作废后需要重新打印发票，请确认是否作废？
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="作废说明：">
                    <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">作废发票</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--确认自取）-->
        <el-button @click="dialog10 = true" type="primary" class="f-mr20 f-mb20">确认自取</el-button>
        <el-drawer
          title="确认自取"
          :visible.sync="dialog10"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="领取人：">
                    <el-input v-model="form.desc" placeholder="请输入领取人姓名" />
                  </el-form-item>
                  <el-form-item label="手机号：">
                    <el-input v-model="form.desc" placeholder="请输入手机号" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--确认配送-->
        <el-button @click="dialog11 = true" type="primary" class="f-mr20 f-mb20">确认配送</el-button>
        <el-drawer
          title="确认配送"
          :visible.sync="dialog11"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="快递公司名称：">
                    <el-input v-model="form.desc" placeholder="请输入快递公司名称" />
                  </el-form-item>
                  <el-form-item label="运单号：">
                    <el-input v-model="form.desc" placeholder="请输入运单号" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--导入开票结果-->
        <el-button @click="dialog12 = true" type="primary" class="f-mr20 f-mb20">导入开票结果</el-button>
        <el-drawer
          title="导入开票结果"
          :visible.sync="dialog12"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt50">
                  <el-form-item label="导入文件：">
                    <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                      <el-button type="primary" plain>点击上传</el-button>
                      <div slot="tip" class="el-upload__tip">
                        <i class="el-icon-warning"></i>
                        <span class="txt">导入的文件格式必须为xls,xlsx文件</span>
                      </div>
                    </el-upload>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--导入包裹运单-->
        <el-button @click="dialog13 = true" type="primary" class="f-mr20 f-mb20">导入包裹运单</el-button>
        <el-drawer
          title="导入包裹运单"
          :visible.sync="dialog13"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt50">
                  <el-form-item label="导入文件：">
                    <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                      <el-button type="primary" plain>点击上传</el-button>
                      <div slot="tip" class="el-upload__tip">
                        <i class="el-icon-warning"></i>
                        <span class="txt">导入的文件格式必须为xls,xlsx文件</span>
                      </div>
                    </el-upload>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--操作记录-->
        <el-button @click="dialog14 = true" type="primary" class="f-mr20 f-mb20">配送纪录</el-button>
        <el-drawer
          title="配送纪录"
          :visible.sync="dialog14"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="f-mt20 f-mlr40">
              <el-timeline>
                <el-timeline-item>
                  <p>发票号【12387596】 自取信息为：</p>
                  <el-card shadow="never" class="bg-gray f-mt10">
                    <p>领取人: 伍赠和</p>
                    <p class="f-mt5">手机号: 13859552508</p>
                    <p class="f-mt5">取货时间: 2020-03-03 11:41:06</p>
                  </el-card>
                </el-timeline-item>
                <el-timeline-item>
                  <p>发票号【12387596】配送信息为：</p>
                  <el-card shadow="never" class="bg-gray f-mt10">
                    <p>快递公司: 中国邮政</p>
                    <p class="f-mt5">运单号: 1155194294875</p>
                    <p class="f-mt5">发货时间: 2020-02-27 14:22:32</p>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
            <el-alert type="info" :closable="false" class="m-alert">
              <div class="f-ptb30 f-tc">该发票暂时还没有操作记录！</div>
            </el-alert>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        dialog11: false,
        dialog12: false,
        dialog13: false,
        dialog14: false,
        dialog15: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
