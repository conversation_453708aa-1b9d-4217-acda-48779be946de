import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 【集体缴费管理员】集体报名订单状态枚举
 */
export enum BatchOrderStatusEnum {
  // 待报名
  Wait_Sign_Up = 1,
  // 报名中
  Signing_Up,
  // 待付款
  Wait_Pay,
  // 支付中
  Paying,
  // 汇款凭证待审核
  Remittance_Voucher_Wait_Audit,
  // 开通中
  Opening,
  // 交易成功
  Pay_Success,
  // 交易关闭中
  Closing_Pay,
  // 交易关闭
  Close_Pay
}

/**
 * @description 【集体缴费管理员】集体报名订单状态
 */
class BatchOrderStatusList extends AbstractEnum<BatchOrderStatusEnum> {
  static enum = BatchOrderStatusEnum
  constructor(status?: BatchOrderStatusEnum) {
    super()
    this.current = status
    this.map.set(BatchOrderStatusEnum.Wait_Sign_Up, '待报名')
    this.map.set(BatchOrderStatusEnum.Signing_Up, '报名中')
    this.map.set(BatchOrderStatusEnum.Wait_Pay, '待付款')
    this.map.set(BatchOrderStatusEnum.Paying, '支付中')
    this.map.set(BatchOrderStatusEnum.Remittance_Voucher_Wait_Audit, '汇款凭证待审核')
    this.map.set(BatchOrderStatusEnum.Opening, '开通中')
    this.map.set(BatchOrderStatusEnum.Pay_Success, '交易成功')
    this.map.set(BatchOrderStatusEnum.Closing_Pay, '交易关闭中')
    this.map.set(BatchOrderStatusEnum.Close_Pay, '交易关闭')
  }
}

export default new BatchOrderStatusList()
