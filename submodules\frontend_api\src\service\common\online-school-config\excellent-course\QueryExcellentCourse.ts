import ExcellentCourseCategoryConfig from '@api/service/management/online-school-config/excellent-course/query/vo/ExcellentCourseCategoryConfig'
import ExcellentCourseConfigDetail from '@api/service/management/online-school-config/excellent-course/query/vo/ExcellentCourseConfigDetail'
import MsServicerSeriesV1 from '@api/ms-gateway/ms-servicer-series-v1'
import { Page } from '@hbfe/common'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class QueryExcellentCourse {
  hasCategory = false
  categories: Array<ExcellentCourseCategoryConfig> = new Array<ExcellentCourseCategoryConfig>()
  courses: Array<ExcellentCourseConfigDetail> = new Array<ExcellentCourseConfigDetail>()

  async queryConfig() {
    const { data } = await MsServicerSeriesV1.getExcellentCourses()
    this.hasCategory = data.usedCategory
    if (this.hasCategory) {
      this.categories = data.categories.map(ExcellentCourseCategoryConfig.from)
      // 获取选中课程分类的 id 集合
      const categoryIdList = data.categories.map(item => item.categoryId)
      const {
        data: result
      } = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.listCourseCategoryInServicer({
        categoryIdList: categoryIdList
      })
      this.categories
        .sort((categoryA: ExcellentCourseCategoryConfig, categoryB: ExcellentCourseCategoryConfig) => {
          return categoryA.sort - categoryB.sort
        })
        .forEach(cat => {
          const id = cat.id
          const courseCat = result.find(courseCategory => {
            return courseCategory.id === id
          })
          if (courseCat) {
            cat.name = courseCat.name
          }
        })
    } else {
      this.courses = data.courses.map(ExcellentCourseConfigDetail.from)
    }

    let courseIdList: Array<string> = []
    if (this.hasCategory) {
      this.categories.forEach(item => {
        item.courses.forEach((course: ExcellentCourseConfigDetail) => {
          courseIdList.push(course.id)
        })
      })
    } else {
      courseIdList = this.courses.map(course => {
        return course.id
      })
    }
    const page = new Page()
    page.pageSize = courseIdList.length
    if (courseIdList.length) {
      const courseList = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInServicer({
        page: page,
        request: {
          courseIdList: courseIdList
        }
      })
      if (this.hasCategory) {
        this.categories.forEach(item => {
          item.courses.forEach((course: ExcellentCourseConfigDetail) => {
            const findCourse = courseList.data.currentPageData.find(courseResource => courseResource.id === course.id)
            course.name = findCourse.name
            course.categoryName = findCourse.categorys.map(courseCategory => courseCategory.name).join('、')
          })
        })
      } else {
        this.courses.forEach((course: ExcellentCourseConfigDetail) => {
          const findCourse = courseList.data.currentPageData.find(courseResource => courseResource.id === course.id)
          course.name = findCourse.name
          course.categoryName = findCourse.categorys.map(courseCategory => courseCategory.name).join('、')
        })
      }
    }
  }
}

export default QueryExcellentCourse
