import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-exam-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-exam-query-front-gateway-ExamQueryForeStage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum AnswerPaperSort {
  CREATE_TIME = 'CREATE_TIME',
  HANDED_TIME = 'HANDED_TIME'
}
export enum FillAnswerType {
  disarray = 'disarray',
  sequence = 'sequence',
  sequenceRelate = 'sequenceRelate'
}
export enum QuestionTypeEnum {
  RADIO = 'RADIO',
  MULTIPLE = 'MULTIPLE',
  FILL = 'FILL',
  OPINION = 'OPINION',
  ASK = 'ASK',
  FATHER = 'FATHER',
  SCALE = 'SCALE'
}
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 功能描述：时间范围查询条件
@Author： wtl
@Date： 2022/1/25 15:30
 */
export class DateScopeRequest {
  /**
   * 开始时间
查询大于等于开始时间的结果
   */
  beginTime?: string
  /**
   * 结束时间
查询小于等于结束时间的结果
   */
  endTime?: string
}

/**
 * 试题查询条件
 */
export class QuestionRequest {
  /**
   * 参训资格id
   */
  qualificationId?: string
  /**
   * 答卷ID
   */
  answerPaperId?: string
  /**
   * 试题ID集合
   */
  questionIdList?: Array<string>
  /**
   * 题库ID集合
   */
  libraryIdList?: Array<string>
  /**
   * 关联课程ID集合
   */
  relateCourseIds?: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes?: Array<string>
  /**
   * 试题题目
   */
  topic?: string
  /**
   * 试题类型（1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7：量表题）
@see QuestionTypeEnum
   */
  questionType?: number
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest
  /**
   * 是否启用
   */
  isEnabled?: boolean
  /**
   * 课程供应商id
   */
  courseSupplierId?: string
  /**
   * 查询范围（不传默认普通试题，不包含问卷试题）
0-普通试题
1-全部（包含普通试题和问卷试题）
2-问卷试题
   */
  queryScope?: number
}

/**
 * @Description （跟底层返回的一致）答卷信息
<AUTHOR>
@Date 15:19 2022/2/24
 */
export class AnswerPaperResponse {
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位id
   */
  unitId: string
  /**
   * 所属服务商id
   */
  servicerId: string
  /**
   * id
   */
  id: string
  /**
   * 答卷状态 发布中 1  取消发布 2  已发布 3
   */
  status: number
  /**
   * 试卷作答状态 未开始作答 -1  作答中0 交卷中 1 已交卷 2
   */
  answerStatus: number
  /**
   * 试卷阅卷状态  未交卷-1 阅卷中 0 阅卷完成 1
   */
  markStatus: number
  /**
   * 用户答卷评定结果常量 未评定 -1  无评定结果 0 合格 1 不合格 2
   */
  evaluateResult: number
  /**
   * 场景类型
   */
  sceneType: number
  /**
   * 场景id
   */
  sceneId: string
  qualificationId: string
  userId: string
  /**
   * 答题数
   */
  answerCount: number
  /**
   * 开始作答时间
   */
  beginAnswerTime: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 发布时间
   */
  publishedTime: string
  /**
   * 交卷时间
   */
  handingTime: string
  /**
   * 交卷完成时间
   */
  handedTime: string
  /**
   * 阅卷开始时间
   */
  markingTime: string
  /**
   * 阅卷完成时间
   */
  markedTime: string
  /**
   * 发布时间
   */
  pusblishedTime: string
  /**
   * 考试时长
   */
  timeLength: number
  /**
   * 阅卷人ID
   */
  markUserId: string
  /**
   * 得分，-1表示不是为分数评定方式
   */
  score: number
  /**
   * 正确题数，-1表示试题不进行评定
   */
  correctCount: number
  /**
   * 错误题数，-1表示试题不进行评定
   */
  incorrectCount: number
  studentNo: string
  name: string
  description: string
  /**
   * 总分
   */
  totalScore: number
  cancelReason: string
  createUserId: string
  systemHanded: boolean
  groups: Array<QuestionGroup>
  /**
   * 试题
   */
  questions: Array<Question>
  /**
   * 答卷时长
   */
  answerTimeLength: number
}

/**
 * 简答题主题模型
 */
export class AskQuestionResponse implements BaseQuestionResponse {
  /**
   * 试题标签，教师评价题专用
   */
  code: Array<string>
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 试题主题模型
 */
export interface BaseQuestionResponse {
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 考试答卷主题模型
 */
export class CourseQuizAnswerPaperResponse {
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 答卷ID
   */
  answerPaperId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 答卷基础信息
   */
  answerPaperBasicInfo: CourseQuizAnswerPaperBasicInfo
  /**
   * 答卷状态信息
   */
  answerPaperStateInfo: CourseQuizAnswerPaperStateInfo
  /**
   * 答卷时间信息
   */
  answerPaperTimeInfo: CourseQuizAnswerPaperTimeInfo
  /**
   * 答卷答题信息
   */
  answerPaperAnswerInfo: CourseQuizAnswerPaperAnswerInfo
  /**
   * 答卷评定信息
   */
  answerPaperMarkInfo: CourseQuizAnswerPaperMarkInfo
}

/**
 * 考试答卷主题模型
 */
export class ExaminationAnswerPaperResponse {
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 答卷ID
   */
  answerPaperId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 答卷基础信息
   */
  answerPaperBasicInfo: ExaminationAnswerPaperBasicInfo
  /**
   * 答卷状态信息
   */
  answerPaperStateInfo: ExaminationAnswerPaperStateInfo
  /**
   * 答卷时间信息
   */
  answerPaperTimeInfo: ExaminationAnswerPaperTimeInfo
  /**
   * 答卷答题信息
   */
  answerPaperAnswerInfo: ExaminationAnswerPaperAnswerInfo
  /**
   * 答卷评定信息
   */
  answerPaperMarkInfo: ExaminationAnswerPaperMarkInfo
}

/**
 * 父子题主题模型
 */
export class FatherQuestionResponse implements BaseQuestionResponse {
  /**
   * 子题集合
   */
  childQuestions: Array<ChildItem>
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 填空题主题模型
 */
export class FillQuestionResponse implements BaseQuestionResponse {
  /**
   * 填空数
   */
  fillCount: number
  /**
   * 正确答案
   */
  fillCorrectAnswer: FillAnswer
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 题库主题模型
 */
export class LibraryResponse {
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 题库ID
   */
  libraryId: string
  /**
   * 题库名称
   */
  libraryName: string
  /**
   * 父题库信息
   */
  parentLibraryInfo: LibraryResponse
  /**
   * 题库描述
   */
  description: string
  /**
   * 是否可用
   */
  enabled: boolean
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 题库来源类型（1：创建 2：导入）
@see LibrarySourceTypes
   */
  sourceType: number
}

/**
 * 多选题主题模型
 */
export class MultipleQuestionResponse implements BaseQuestionResponse {
  /**
   * 可选答案列表
   */
  multipleAnswerOptions: Array<ChooseAnswerOption>
  /**
   * 正确答案ID集合
   */
  correctAnswerIds: Array<string>
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 判断题主题模型
 */
export class OpinionQuestionResponse implements BaseQuestionResponse {
  /**
   * 正确答案
   */
  opinionCorrectAnswer: boolean
  /**
   * 正确文本
   */
  correctAnswerText: string
  /**
   * 不正确文本
   */
  incorrectAnswerText: string
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 课后测验答卷主题模型
 */
export class PracticeAnswerPaperResponse {
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 答卷ID
   */
  answerPaperId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 答卷基础信息
   */
  answerPaperBasicInfo: PracticeAnswerPaperBasicInfo
  /**
   * 答卷状态信息
   */
  answerPaperStateInfo: PracticeAnswerPaperStateInfo
  /**
   * 答卷时间信息
   */
  answerPaperTimeInfo: PracticeAnswerPaperTimeInfo
  /**
   * 答卷答题信息
   */
  answerPaperAnswerInfo: PracticeAnswerPaperAnswerInfo
  /**
   * 答卷评定信息
   */
  answerPaperMarkInfo: PracticeAnswerPaperMarkInfo
}

/**
 * 单选题主题模型
 */
export class RadioQuestionResponse implements BaseQuestionResponse {
  /**
   * 可选答案列表
   */
  radioAnswerOptions: Array<ChooseAnswerOption>
  /**
   * 正确答案ID
   */
  correctAnswerId: string
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 量表题主题模型
 */
export class ScaleQuestionResponse implements BaseQuestionResponse {
  /**
   * 量表类型
   */
  scaleType: number
  /**
   * 程度-始
   */
  startDegree: string
  /**
   * 程度-止
   */
  endDegree: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 数据归属模型
 */
export class DataBelongModel {
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位id
   */
  unitId: string
  /**
   * 所属服务商id
   */
  servicerId: string
}

/**
 * 课后测验答题信息
 */
export class CourseQuizAnswerPaperAnswerInfo {
  /**
   * 作答时长
   */
  answerTimeLength: number
  /**
   * 答题总数
   */
  answerCount: number
  /**
   * 是否系统强制交卷
   */
  systemHanded: boolean
  /**
   * 考试时长，单位：秒，-1表示没有时长限制
   */
  examTimeLength: number
}

/**
 * 课后测验基础信息
 */
export class CourseQuizAnswerPaperBasicInfo {
  /**
   * 课后测验ID
   */
  sceneId: string
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷描述
   */
  description: string
  /**
   * 试题总数
   */
  questionCount: number
  /**
   * 试卷总分 -1表示不以分数方式进行评定
   */
  totalScore: number
  /**
   * 及格分
   */
  qualifiedScore: number
}

/**
 * 课后测验评定信息
 */
export class CourseQuizAnswerPaperMarkInfo {
  /**
   * 阅卷人ID
   */
  markUserId: string
  /**
   * 得分，-1表示不是为分数评定方式
   */
  score: number
  /**
   * 正确题数，-1表示试题不进行评定
   */
  correctCount: number
  /**
   * 错误题数，-1表示试题不进行评定
   */
  incorrectCount: number
}

/**
 * 课后测验状态信息
 */
export class CourseQuizAnswerPaperStateInfo {
  /**
   * 答卷状态
   */
  answerPaperStatus: number
  /**
   * 答卷作答状态
   */
  answerPaperAnswerStatus: number
  /**
   * 答卷阅卷状态
   */
  answerPaperMarkStatus: number
  /**
   * 答卷评定结果
   */
  answerPaperEvaluateResults: number
}

/**
 * 课后测验时间信息
 */
export class CourseQuizAnswerPaperTimeInfo {
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 发布时间
   */
  publishedTime: string
  /**
   * 开始作答时间
   */
  answeringTime: string
  /**
   * 交卷时间
   */
  handingTime: string
  /**
   * 交卷完成时间
   */
  handedTime: string
  /**
   * 阅卷开始时间
   */
  markingTime: string
  /**
   * 阅卷完成时间
   */
  markedTime: string
}

/**
 * 答卷答题信息
 */
export class ExaminationAnswerPaperAnswerInfo {
  /**
   * 作答时长
   */
  answerTimeLength: number
  /**
   * 答题总数
   */
  answerCount: number
}

/**
 * 答卷基础信息
 */
export class ExaminationAnswerPaperBasicInfo {
  /**
   * 考试ID
   */
  sceneId: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷描述
   */
  description: string
  /**
   * 试题总数
   */
  questionCount: number
  /**
   * 试卷总分 -1表示不以分数方式进行评定
   */
  totalScore: number
  /**
   * 及格分
   */
  qualifiedScore: number
  /**
   * 考试场次名称
   */
  examSessionName: string
}

/**
 * 答卷评定信息
 */
export class ExaminationAnswerPaperMarkInfo {
  /**
   * 阅卷人ID
   */
  markUserId: string
  /**
   * 得分，-1表示不是为分数评定方式
   */
  score: number
  /**
   * 正确题数，-1表示试题不进行评定
   */
  correctCount: number
  /**
   * 错误题数，-1表示试题不进行评定
   */
  incorrectCount: number
}

/**
 * 答卷状态信息
 */
export class ExaminationAnswerPaperStateInfo {
  /**
   * 答卷状态
   */
  answerPaperStatus: number
  /**
   * 答卷作答状态
   */
  answerPaperAnswerStatus: number
  /**
   * 答卷阅卷状态
   */
  answerPaperMarkStatus: number
  /**
   * 答卷评定结果
   */
  answerPaperEvaluateResults: number
}

/**
 * 答卷时间信息
 */
export class ExaminationAnswerPaperTimeInfo {
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 发布时间
   */
  publishedTime: string
  /**
   * 开始作答时间
   */
  answeringTime: string
  /**
   * 交卷时间
   */
  handingTime: string
  /**
   * 交卷完成时间
   */
  handedTime: string
  /**
   * 阅卷开始时间
   */
  markingTime: string
  /**
   * 阅卷完成时间
   */
  markedTime: string
}

/**
 * 练习答题信息
 */
export class PracticeAnswerPaperAnswerInfo {
  /**
   * 作答时长
   */
  answerTimeLength: number
  /**
   * 答题总数
   */
  answerCount: number
}

/**
 * 练习基础信息
 */
export class PracticeAnswerPaperBasicInfo {
  /**
   * 练习ID
   */
  sceneId: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷描述
   */
  description: string
  /**
   * 试题总数
   */
  questionCount: number
}

/**
 * 练习评定信息
 */
export class PracticeAnswerPaperMarkInfo {
  /**
   * 阅卷人ID
   */
  markUserId: string
  /**
   * 得分，-1表示不是为分数评定方式
   */
  score: number
  /**
   * 正确题数，-1表示试题不进行评定
   */
  correctCount: number
  /**
   * 错误题数，-1表示试题不进行评定
   */
  incorrectCount: number
}

/**
 * 练习状态信息
 */
export class PracticeAnswerPaperStateInfo {
  /**
   * 答卷状态
   */
  answerPaperStatus: number
  /**
   * 答卷作答状态
   */
  answerPaperAnswerStatus: number
  /**
   * 答卷阅卷状态
   */
  answerPaperMarkStatus: number
  /**
   * 答卷评定结果
   */
  answerPaperEvaluateResults: number
}

/**
 * 练习时间信息
 */
export class PracticeAnswerPaperTimeInfo {
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 发布时间
   */
  publishedTime: string
  /**
   * 开始作答时间
   */
  answeringTime: string
  /**
   * 交卷时间
   */
  handingTime: string
  /**
   * 交卷完成时间
   */
  handedTime: string
}

/**
 * 试卷大题信息
<AUTHOR>
 */
export class QuestionGroup {
  sequence: number
  questionType: number
  groupName: string
  eachQuestionScore: number
}

/**
 * @Description
<AUTHOR>
@Date 17:13 2022/3/9
 */
export class Answer {
  key: number
  /**
   * 答案
   */
  answer: string
}

/**
 * @Description 问答题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class AskQuestion implements Question {
  /**
   * 答案
   */
  askAnswer: string
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * 父子题子题项
 */
export class ChildItem {
  /**
   * 子题序号
   */
  no: number
  /**
   * 子题ID
   */
  questionId: string
}

/**
 * 选择题答案选项
 */
export class ChooseAnswerOption {
  /**
   * 答案ID
   */
  id: string
  /**
   * 答案内容
   */
  content: string
  /**
   * 是否填空题
   */
  enableFillContent: boolean
  /**
   * 是否必填
   */
  mustFillContent: boolean
}

/**
 * 散乱无序填空题答案实体
<pre>
每空有多种匹配答案，空格答案不存在顺序关系： 比如：
试题题目：请写出中国四大银行__________、__________、__________、__________。
每空备选答案：
1/中国建设银行 建设银行 建行
2/中国银行 中行
3/中国工商银行 工商银行 工行
4/中国农业银行 农业银行 农行
学员答题答案：农行、工行、中行、建行；评卷为正确并得分；
</pre>
<AUTHOR>
 */
export class DisarrayFillAnswer implements FillAnswer {
  /**
   * 正确答案集合
   */
  disarrayCorrectAnswers: Array<string>
  /**
   * 填空题答案类型
   */
  type: FillAnswerType
}

/**
 * @Description 父子题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class FatherQuestion implements Question {
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * 填空题答案基类
 */
export interface FillAnswer {
  /**
   * 填空题答案类型
   */
  type: FillAnswerType
}

/**
 * 功能描述：填空
@Author： wtl
@Date： 2022/2/17 17:40
 */
export class FillCorrectAnswers {
  /**
   * 空格位置
   */
  blankNo: number
  /**
   * 答案备选项
   */
  answers: Array<string>
}

/**
 * @Description 填空题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class FillQuestion implements Question {
  /**
   * 答案
   */
  fillAnswer: Array<Answer>
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * @Description 多选题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class MultipleQuestion implements Question {
  /**
   * 答案
   */
  multipleAnswer: Array<string>
  /**
   * 填空的内容
   */
  fillContents: Array<MultipleQuestionFillContent>
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

export class MultipleQuestionFillContent {
  /**
   * 选项id
   */
  id: string
  /**
   * 填空内容
   */
  fillContent: string
}

/**
 * @Description 判断题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class OpinionQuestion implements Question {
  /**
   * 答案
   */
  opinionAnswer: boolean
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * @Description 试题
<AUTHOR>
@Date 16:07 2022/2/28
 */
export interface Question {
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * @Description 单选题和底层一样
<AUTHOR>
@Date 15:40 2022/2/28
 */
export class RadioQuestion implements Question {
  /**
   * 答案
   */
  radioAnswer: string
  /**
   * 需要填空的内容
   */
  fillContent: string
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * 试题关联课程
 */
export class RelateCourse {
  /**
   * 课程id
   */
  courseId: string
  /**
   * 课程供应商id
   */
  courseSupplierId: string
}

/**
 * @Description 量表题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class ScaleQuestion implements Question {
  /**
   * 答案
   */
  answer: number
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * 按序填空题答案实体
<pre>
每空有多种匹配答案且空格答案存在顺序关系： 比如：
试题题目：中国的政治中心是__________；中国的经济中心是__________。
试题答案： 1/北京北京市、2/上海上海市
学员答题答案：北京市、上海；评卷为正确并得分；
</pre>
<AUTHOR>
 */
export class SequenceFillAnswer implements FillAnswer {
  /**
   * 每个填空数答案
   */
  sequenceCorrectAnswers: Array<FillCorrectAnswers>
  /**
   * 填空题答案类型
   */
  type: FillAnswerType
}

/**
 * 按序关联填空题答案实体
<pre>
每空答案精确匹配： 适用于前后空格的答案是有关联的。比如：
试题题目：请写出中国四大名著之一是__________；作者是__________。
试题答案： 1/红楼梦曹雪芹、2/西游记吴承恩
学员答题答案：红楼梦、曹雪芹；评卷为正确并得分；
红楼梦、吴承恩；评卷则给第一个空得分；
</pre>
<AUTHOR>
 */
export class SequenceRelateFillAnswer implements FillAnswer {
  /**
   * 正确答案集合
   */
  sequenceRelateCorrectAnswers: Array<SequenceFillAnswer>
  /**
   * 填空题答案类型
   */
  type: FillAnswerType
}

export class CourseQuizAnswerPaperResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseQuizAnswerPaperResponse>
}

export class ExaminationAnswerPaperResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ExaminationAnswerPaperResponse>
}

export class PracticeAnswerPaperResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PracticeAnswerPaperResponse>
}

export class BaseQuestionResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BaseQuestionResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取答卷详情
   * @param id 答卷id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAnswerPaperRecordInMyself(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAnswerPaperRecordInMyself,
    operation?: string
  ): Promise<Response<AnswerPaperResponse>> {
    return commonRequestApi<AnswerPaperResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取我的课后测验分页
   * @param courseId          课程ID
   * @param qualificationId   学员ID
   * @param answerPaperStatus 作答状态
   * @param answerPaperSort   排序类型
   * @param sort              answerPaperSort排序方式 正序 逆序
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageMyCourseQuizRecordInMyself(
    params: {
      page?: Page
      courseId?: string
      qualificationId?: string
      answerPaperStatus?: number
      answerPaperSort?: AnswerPaperSort
      sort?: SortTypeEnum
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageMyCourseQuizRecordInMyself,
    operation?: string
  ): Promise<Response<CourseQuizAnswerPaperResponsePage>> {
    return commonRequestApi<CourseQuizAnswerPaperResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取我的考试记录分页
   * @param qualificationId   学员ID
   * @param answerPaperStatus 作答状态
   * @param answerPaperSort   排序类型
   * @param sort              answerPaperSort排序方式 正序 逆序
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageMyExaminationRecordInMyself(
    params: {
      page?: Page
      qualificationId?: string
      answerPaperStatus?: number
      answerPaperSort?: AnswerPaperSort
      sort?: SortTypeEnum
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageMyExaminationRecordInMyself,
    operation?: string
  ): Promise<Response<ExaminationAnswerPaperResponsePage>> {
    return commonRequestApi<ExaminationAnswerPaperResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取我的班级练习分页
   * @param qualificationId   学员ID
   * @param answerPaperStatus 作答状态
   * @param answerPaperSort   排序类型
   * @param sort              answerPaperSort排序方式 正序 逆序
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageMyPracticeRecordInMyself(
    params: {
      page?: Page
      qualificationId?: string
      answerPaperStatus?: number
      answerPaperSort?: AnswerPaperSort
      sort?: SortTypeEnum
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageMyPracticeRecordInMyself,
    operation?: string
  ): Promise<Response<PracticeAnswerPaperResponsePage>> {
    return commonRequestApi<PracticeAnswerPaperResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取我的试题分页(用于考试页面查询试题信息)
   * @param page    分页对象
   * @param request 查询参数对象
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageQuestionInMySelf(
    params: { page?: Page; request?: QuestionRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageQuestionInMySelf,
    operation?: string
  ): Promise<Response<BaseQuestionResponsePage>> {
    return commonRequestApi<BaseQuestionResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
