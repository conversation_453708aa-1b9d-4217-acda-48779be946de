import EnumOption from './EnumOption'

class AbstractEnum<T> {
  set current(value: T) {
    this._current = value
  }

  get current(): T {
    return this._current ?? undefined
  }

  map: Map<T, string> = new Map<T, string>()
  private _current: T = undefined

  list(): Array<EnumOption<T>> {
    return Array.from(this.map.keys()).map((key: T) => {
      const enumOption = new EnumOption<T>()
      enumOption.code = key
      enumOption.desc = this.map.get(key)
      return enumOption
    })
  }

  toString(): number | string {
    return this.map.get(this._current)
  }

  equal(status: T) {
    return this._current === status
  }

  listByTypes(types: Array<T>, externalAll?: boolean): Array<EnumOption<T>> {
    let typesList: Array<EnumOption<T>> = new Array<EnumOption<T>>()
    if (types.length) {
      typesList = types.map(type => {
        if (this.map.get(type)) {
          const option = new EnumOption<T>()
          option.code = type
          option.desc = this.map.get(type)
          return option
        }
      })
    } else {
      typesList = this.list()
    }

    if (externalAll) {
      typesList.unshift(new EnumOption(null, '全部'))
    }
    return typesList
  }
}

export default AbstractEnum
