<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/questionnaire' }">问卷管理</el-breadcrumb-item>
      <el-breadcrumb-item>{{ type === 'add' ? '新建' : '修改' }}调研问卷</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header">
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">问卷信息</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit" v-loading="query.loading">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="modifyForm" :model="params" :rules="rules" label-width="auto" class="m-form f-mt20">
              <el-form-item label="问卷名称：" prop="name">
                <el-input v-model="params.name" clearable placeholder="请输入问卷名称" :maxlength="50" />
              </el-form-item>
              <el-form-item label="问卷类型：" prop="type">
                <el-radio-group v-model="params.type" @change="questionnaireTypeChange" :disabled="isDisableDraft">
                  <el-radio :label="1">普通问卷</el-radio>
                  <el-radio :label="2">量表问卷</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="问卷说明：" prop="questionnaireInfo">
                <div class="rich-text">
                  <!-- <hb-tinymce-editor v-model="params.questionnaireInfo"></hb-tinymce-editor>
                  -->
                  <hb-tinymce-editor
                    :propId="`1${editorKey}`"
                    v-model="params.questionnaireInfo"
                    :key="`1${editorKey}`"
                  ></hb-tinymce-editor>
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">配置试题<i class="f-ci f-fn f-f14">（鼠标长按试题可拖拽排序）</i></span>
        </div>
        <ConfigureQuestions
          ref="ConfigureQuestionsRef"
          :isDisabled="isDisableQuestion"
          :isDisableDraft="isDisableDraft"
          v-loading="query.loading"
          :questionType="params.type"
          :questionListInfo.sync="params.questionList"
          @changeData="changeData"
        ></ConfigureQuestions>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button :loading="query.loading" @click="cancel">取消</el-button>
        <el-button :loading="query.loading" @click="preview">预览问卷</el-button>
        <el-button :loading="query.loading" v-if="!isDisableDraft" type="primary" @click="saveDraft"
          >保存为草稿</el-button
        >
        <el-button :loading="query.loading" type="primary" @click="commit">发布问卷</el-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import { ElForm } from 'element-ui/types/form'
  import { Query } from '@hbfe/common'
  import ConfigureQuestions from '@hbfe/jxjy-admin-questionnaire/src/component/configureQuestions.vue'
  import QuestionnaireDetail from '@api/service/management/resource/question-naire/QuestionnaireDetail'
  import { QuestionnaireStatusEnum } from '@api/service/management/resource/question-naire/enums/QuestionnaireStatus'
  import HbTinyMceEditor from '@hbfe/jxjy-admin-components/src/tinymce-editor/index.vue'
  @Component({
    components: {
      ConfigureQuestions,
      HbTinyMceEditor
    }
  })
  export default class extends Vue {
    @Ref('modifyForm') modifyForm: ElForm
    @Ref('ConfigureQuestionsRef') ConfigureQuestionsRef: ConfigureQuestions
    params = new QuestionnaireDetail() //问卷详情 请求
    query: Query = new Query()
    type = '' //新建还是修改？？
    questionnaireId = '' //问卷id
    isDisable = false //是否禁用
    id = '' //问卷id
    editorKey = 'tiny_mce_editor_' + new Date().getTime() //key 解决数据不同步问题
    rules = {
      name: {
        required: true,
        message: '请配置问卷名称，不可为空。',
        trigger: 'blur'
      },
      type: {
        required: true,
        message: '请配置问卷类型，不可为空。',
        trigger: 'change'
      },
      questionnaireInfo: {
        required: true,
        validator: this.validateQuestionnaireInfo,
        trigger: 'blur'
      }
    }
    /**
     * 问卷说明验证
     */
    validateQuestionnaireInfo(rule: any, value: any, callback: any) {
      if (!this.params.questionnaireInfo) {
        callback(new Error('请配置问卷说明，不可为空。'))
      } else {
        const re = /[\u4E00-\u9FA5]/g
        if (value.match(re) && value.match(re).length >= 1000) {
          callback(new Error('问卷说明文字不可超过1000个汉字。'))
        } else {
          callback()
        }
      }
    }
    /**
     * 取消
     */
    cancel() {
      this.$router.go(-1)
    }
    /**
     * 发布问卷
     */
    async commit() {
      if (this.params.questionList.length === 0) {
        this.$message.error('请至少配置一题试题才可发布问卷。')
        return
      }
      await this.modifyForm.validate()
      try {
        this.query.loading = true
        let res
        if (this.isCopy) {
          res = await this.params.newCopy(2)
        } else {
          if (this.type === 'add') {
            res = await this.params.publish()
          } else {
            res = await this.params.save(2)
          }
        }

        if (res.data) {
          this.$router.push('/resource/questionnaire')
          this.$message.success('问卷发布成功。')
        } else {
          if (res.status.errors[0]?.code === 10003) {
            this.$message.error('问卷模板被引用，不可修改。')
          } else {
            this.$message.error(res.status.getMessage())
          }
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.query.loading = false
      }
    }
    /**
     * 预览
     */
    async preview() {
      if (this.params.questionList.length === 0) {
        this.$message.error('请至少配置一题试题才可预览。')
        return
      }
      // QuestionnairePreviewModel.setQuestionnairePreview(this.params.questionList)
      localStorage.setItem('questionList', JSON.stringify(this.params.questionList))
      window.open(`/admin#/resource/questionnaire/preview?name=${this.params.name}`, '_blank')
    }
    /**
     * 保存为草稿
     */
    async saveDraft() {
      this.modifyForm.clearValidate()
      if (!this.params.name) {
        this.$message.error('请填写问卷名称')
        return
      }
      try {
        this.query.loading = true
        let res
        if (this.isCopy) {
          res = await this.params.newCopy(1)
        } else {
          if (this.type === 'add') {
            res = await this.params.createDraft()
          } else {
            res = await this.params.save(1)
          }
        }
        if (res.data) {
          this.$message.success('保存草稿成功')
          this.$router.push('/resource/questionnaire')
        } else {
          this.query.loading = false
          if (res.status.errors[0]?.code === 10003) {
            this.$message.error('问卷模板被引用，不可修改。')
          } else {
            this.$message.error(res.status.getMessage())
          }
        }
      } catch (e) {
        console.log(e)
      } finally {
        // this.query.loading = false
      }
    }
    /**
     *
     */
    get isDisableDraft() {
      return this.type === 'edit' && this.params.status === QuestionnaireStatusEnum.publish
    }
    /**
     * 已发布且被引用 统统不允许修改
     */
    get isDisableQuestion() {
      return this.type === 'edit' && this.params.schemeUseStatus
    }
    /**
     * 判断是否是复制进来
     */
    get isCopy() {
      return this.id && this.type === 'add'
    }

    /**
     * 手动处理请求
     */
    async changeData() {
      await this.queryDetail()
    }
    /**
     * 详情
     */
    async queryDetail() {
      try {
        this.query.loading = true
        this.params.id = this.id
        await this.params.queryDetail()
        this.editorKey = 'tiny_mce_editor_' + new Date().getTime() + parseInt(`${Math.random() * 10}`) //key 解决数据不同步问题
      } catch (e) {
        console.log(e)
      } finally {
        this.query.loading = false
      }
    }
    /**
     * 切问卷类型
     */
    questionnaireTypeChange() {
      this.ConfigureQuestionsRef.questionList = []
    }
    /**
     * 初始化
     */
    async init() {
      this.id = this.$route?.query?.id as string
      this.type = this.$route?.params?.type
      if (this.$route?.query?.id) {
        // 查询请求
        await this.queryDetail()
      }
    }
    async created() {
      await this.init()
    }
  }
</script>
