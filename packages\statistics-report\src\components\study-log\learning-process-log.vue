<!-- 学习日志 -->
<template>
  <div>
    <template v-if="tableData.length">
      <div v-for="(item, i) in tableData" :key="i">
        <el-badge is-dot type="info" class="badge-status f-mt15 f-mb15"
          ><span class="f-fb">课程名称：{{ item.courseName || '-' }}</span></el-badge
        >
        <!-- 合并表单 -->
        <ProcessTable :tableData="item.courseWareUserLearningLog"></ProcessTable>
      </div>
      <hb-pagination :page="page" v-bind="page"></hb-pagination
    ></template>
    <template v-else>
      <!--通用空数据-->
      <div class="m-no-date">
        <img class="img" src="@design/admin/assets/images/no-data-normal.png" alt="" />
        <div class="date-bd">
          <p class="f-f15 f-c9">暂时还没有内容~</p>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import UserLearningList from '@api/service/management/anticheat/UserLearningList'
  import { SupervisionTypeEnum } from '@hbfe-biz/biz-anticheat/dist/log/enums/SupervisionEnum'
  import UserLearningLogItem from '@api/service/management/anticheat/models/UserLearningLogItem'
  import { CurrentResultEnum } from '@hbfe-biz/biz-anticheat/dist/log/enums/SupervisionEnum'
  import ProcessTable from '@hbfe/jxjy-admin-statisticsReport/src/components/study-log/components/process-table.vue'

  @Component({
    components: {
      ProcessTable
    }
  })
  export default class extends Vue {
    // 数据加载
    tableLoading = false

    // page
    page: UiPage
    constructor() {
      super()
      this.page = new UiPage(this.doQuery, this.doQuery)
    }

    // 枚举
    supervisionTypeEnum: SupervisionTypeEnum

    // 表单数据
    tableData: Array<UserLearningLogItem> = new Array<UserLearningLogItem>()
    // tableData: any = [
    //   {
    //     courseName: 'kec1',
    //     courseId: '1111111111',
    //     courseWareId: '',
    //     courseWareName: '',
    //     userLogInfo: [],
    //     courseWareUserLearningLog: [
    //       {
    //         courseName: 'kec1',
    //         courseId: '1111111111',
    //         courseWareId: '111111111222222',
    //         courseWareName: '媒体1',
    //         userLogInfo: [{ createdTime: '2020', photoCreatedTime: '2022', photoList: '///' }]
    //       },
    //       {
    //         courseName: 'kec1',
    //         courseId: '1111111111',
    //         courseWareId: '111111111222222',
    //         courseWareName: '媒体1',
    //         userLogInfo: [{ createdTime: '2021', photoCreatedTime: '2022', photoList: '//3/' }]
    //       },
    //       {
    //         courseName: 'kec1',
    //         courseId: '1111111111',
    //         courseWareId: '111111111222222',
    //         courseWareName: '媒体1',
    //         userLogInfo: [{ createdTime: '2021', photoCreatedTime: '2023', photoList: '///' }]
    //       },
    //       {
    //         courseName: 'kec1',
    //         courseId: '1111111111',
    //         courseWareId: '11111123',
    //         courseWareName: '媒体2',
    //         userLogInfo: [{ createdTime: '2020', photoCreatedTime: '2022', photoList: '///' }]
    //       },
    //       {
    //         courseName: 'kec1',
    //         courseId: '1111111111',
    //         courseWareId: '11111123',
    //         courseWareName: '媒体2',
    //         userLogInfo: [{ createdTime: '2023', photoCreatedTime: '2021', photoList: '///' }]
    //       }
    //     ]
    //   },
    //   {
    //     courseName: 'kec2',
    //     courseId: '1111111111',
    //     courseWareId: '',
    //     courseWareName: '',
    //     userLogInfo: [],
    //     courseWareUserLearningLog: [
    //       {
    //         courseName: 'kec2',
    //         courseId: '1111111111',
    //         courseWareId: '1111111112222223',
    //         courseWareName: '媒体3',
    //         userLogInfo: [{ createdTime: '2020', photoCreatedTime: '2022', photoList: '///' }]
    //       },
    //       {
    //         courseName: 'kec2',
    //         courseId: '1111111111',
    //         courseWareId: '1111111112222223',
    //         courseWareName: '媒体3',
    //         userLogInfo: [{ createdTime: '2021', photoCreatedTime: '2022', photoList: '//3/' }]
    //       },
    //       {
    //         courseName: 'kec2',
    //         courseId: '1111111111',
    //         courseWareId: '1111111112222223',
    //         courseWareName: '媒体3',
    //         userLogInfo: [{ createdTime: '2021', photoCreatedTime: '2023', photoList: '///' }]
    //       },
    //       {
    //         courseName: 'kec2',
    //         courseId: '1111111111',
    //         courseWareId: '111111231',
    //         courseWareName: '媒体4',
    //         userLogInfo: [{ createdTime: '2020', photoCreatedTime: '2022', photoList: '///' }]
    //       },
    //       {
    //         courseName: 'kec2',
    //         courseId: '1111111111',
    //         courseWareId: '111111231',
    //         courseWareName: '媒体4',
    //         userLogInfo: [{ createdTime: '2023', photoCreatedTime: '2021', photoList: '///' }]
    //       }
    //     ]
    //   }
    // ]

    // 查询对象
    queryObj = new UserLearningList()

    // 查询
    async doQuery() {
      this.tableLoading = true
      try {
        this.queryObj.userId = this.$route.query.userId as string
        if (!this.queryObj.userId) throw new Error('用户id为空')
        this.queryObj.qualificationId = this.$route.query.qualificationId as string
        if (!this.queryObj.qualificationId) throw new Error('参训人员id为空')
        if (!this.$route.query.schemeType) throw new Error('培训类型为空')
        this.queryObj.studentNo = this.$route.query.studentNo as string
        if (!this.queryObj.studentNo) throw new Error('学号为空')
        this.queryObj.schemeType = Number(this.$route.query.schemeType)
        this.tableData = await this.queryObj.queryCourseLogList(this.page)
        // this.tableData =
        console.log(this.tableData, '===tab2')
      } catch (error) {
        console.log(error)
      } finally {
        this.tableLoading = false
      }
    }

    // 匹配结果
    get result() {
      return (res: number) => {
        return res == CurrentResultEnum.PASS
      }
    }

    // 初始化请求
    initSearch() {
      this.page.pageNo = 1
      this.doQuery()
    }
  }
</script>
