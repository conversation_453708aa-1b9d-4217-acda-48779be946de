schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""批量导入学员名单
		@param request
	"""
	batchImportStudentList(request:ImportListVerificationRequest):String
	"""批量打印证书sync（没用）"""
	batchPrintCertificateSync(request:BatchPrintCertificatesSyncRequest):String
	"""批量打印证书（按方案打印）
		@param request
		@param studentSchemeLearningRequest
		@param sort
		@param dataFetchingEnvironment
		@return zip压缩包路径
	"""
	batchPrintCertificates(request:BatchPrintCertificatesRequest,studentSchemeLearningRequest:StudentSchemeLearningRequest,sort:[StudentSchemeLearningSortRequest]):String
	"""查询导入打印名单上传进度
		@param mainTaskId 主任务ID
		@return {@link String}
	"""
	checkImportPrintListTheUploadProgress(mainTaskId:String):BatchTaskInformation
	"""打印单个证书 - 选择模板
		@param request
		@return {@link String}
	"""
	chooseTemplatePrintCertificate(request:ChooseTemplateCertificatePrintRequest):CheckPrintConditionResponse
	"""集体报名管理员导出批量证明打印失败数据"""
	collectivelyAdministratorExportCertificateFailedData(request:ExportCertificateFailedDataRequest):String
	"""历史证明打印，无需登录，泉州提高需求，使用时需确认内部逻辑
		@param request 请求体
		@return 文件路径
	"""
	dataMigrationCertificatePrintWithOutLogin(request:DataMigrationCertificatePrintWithOutLoginRequest):String @optionalLogin
	"""旧平台迁移数据打印
		@param request
		@return {@link String}
	"""
	dataMigrationPrintCertificate(request:DataMigrationCertificatePrintRequest):String
	"""批量删除试题"""
	deleteQuestion(ids:[String]):Void
	"""导出批量证明打印失败数据"""
	exportCertificateFailedData(request:ExportCertificateFailedDataRequest):String
	"""导出失败数据
		@param request
		@return {@link String}
	"""
	exportFailedData(request:ExportFailedDataRequest):String
	"""查看导入任务数据
		@return {@link String}
	"""
	findImportPrintData(mainTaskId:String):String
	"""查看导入任务失败数据
		@return {@link String}
	"""
	findImportPrintFailData(mainTaskId:String):String
	"""上传成功失败数量
		@return {@link String}
	"""
	findUploadSuccessFailureQuantity:SuccessFailureQuantityRequest
	"""获取证书快照
		@param request
		@return
	"""
	getCertificateSnapshot(request:GetCertificateSnapshotRequest):String @optionalLogin
	"""集体报名打印单个证书  文件名:名字+证件号
		<p>已弃用，统一换成 printCertificate 接口</p>
		@param request
		@return {@link String}
	"""
	groupRegistrationPrintCertificate(request:CertificatePrintRequest):CheckPrintConditionResponse
	"""导入名单打印
		@param request
		@return {@link String}
	"""
	learnerImportBatchPrintCertificates(batchPrintingRequest:BatchPrintingRequest,request:ImportListBatchPrintCertificatesRequest):String
	"""单个打印证书
		@param request 请求体
		@return 证书路径
	"""
	printCertificate(request:CertificatePrintRequest):CheckPrintConditionResponse
	"""打印名单列表移除所有学员
		@param request
	"""
	printTheListRemoveAllStudent(request:PrintTheListRemoveAllStudentRequest):Void
	"""打印名单列表移除单个学员
		@param request
	"""
	printTheListRemoveStudent(request:PrintTheListRemoveStudentRequest):Void
	"""返回导入名单信息
		@param request
		@return {@link ImportedData}
	"""
	returnImportedData(request:ImportListDataRequest):ImportedDataPage @page(for:"ImportedData")
	"""筛选批量打印证书（集体管理员报名）
		@param request 请求
		@return zip压缩包路径
	"""
	screeningBatchPrintCertificates(request:ScreeningBatchPrintCertificatesRequest):[String]
	"""批量打印证书 学号（按学员打印）
		@param request
		@param studentSchemeLearningRequest
		@param sort
		@param dataFetchingEnvironment
		@return zip压缩包路径
	"""
	studentBatchPrintCertificates(request:StudentBatchPrintCertificatesRequest,studentSchemeLearningRequest:StudentSchemeLearningRequest,sort:[StudentSchemeLearningSortRequest]):String
	"""学员端打印证书(需要判断是否强制调查问卷)"""
	studentPrintCertificate(request:StudentPrintCertificateRequest):CheckPrintConditionResponse
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
input StudentSchemeLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningRequest") {
	studentNoList:[String]
	student:UserRequest
	learningRegister:LearningRegisterRequest
	scheme:SchemeRequest
	studentLearning:StudentLearningRequest
	dataAnalysis:DataAnalysisRequest
	connectManageSystem:ConnectManageSystemRequest
	extendedInfo:ExtendedInfoRequest
	openPrintTemplate:Boolean
	saleChannels:[Int]
	trainingChannelName:String
	trainingChannelId:String
	notDistributionPortal:Boolean
	trainingType:String
	issueId:String
}
input StudentSchemeLearningSortRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningSortRequest") {
	field:StudentSchemeLearningSortField
	policy:SortPolicy
}
input ConnectManageSystemRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ConnectManageSystemRequest") {
	syncStatus:Int
}
input DataAnalysisRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.DataAnalysisRequest") {
	trainingResultPeriod:DoubleScopeRequest
	requirePeriod:DoubleScopeRequest
	acquiredPeriod:DoubleScopeRequest
}
input ExtendedInfoRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ExtendedInfoRequest") {
	whetherToPrint:Boolean
	applyCompanyCode:String
	policyTrainingSchemeId:String
	policyTrainingSchemeName:String
}
input LearningRegisterRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.LearningRegisterRequest") {
	registerType:Int
	sourceType:String
	sourceId:String
	status:[Int]
	registerTime:DateScopeRequest
	saleChannels:[Int]
	orderNoList:[String]
	subOrderNoList:[String]
	batchOrderNoList:[String]
	distributorId:String
	portalId:String
}
input RegionRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.RegionRequest") {
	province:String
	city:String
	county:String
}
input StudentLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.learning.StudentLearningRequest") {
	trainingResultList:[Int]
	trainingResultTime:DateScopeRequest
	notLearningTypeList:[Int]
	courseScheduleStatus:Int
	examAssessResultList:[Int]
}
input RegionSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertySearchRequest") {
	region:[RegionSkuPropertyRequest]
	regionSearchType:Int
}
input SchemeRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeRequest") {
	schemeId:String
	schemeIdList:[String]
	schemeType:String
	schemeName:String
	skuProperty:SchemeSkuPropertyRequest
}
input SchemeSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeSkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	positionCategory:[String]
	trainingObject:[String]
	jobLevel:[String]
	jobCategory:[String]
	subject:[String]
	grade:[String]
	learningPhase:[String]
	discipline:[String]
	qualificationCategory:[String]
	trainingWay:[String]
	trainingInstitution:[String]
	mainAdditionalItem:[String]
}
input UserPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserPropertyRequest") {
	regionList:[RegionRequest]
	companyName:String
	payOrderRegionList:[RegionRequest]
}
input UserRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserRequest") {
	userIdList:[String]
	accountIdList:[String]
	userProperty:UserPropertyRequest
}
"""批量打印证书请求
	<AUTHOR>
	@date 2022/12/5 16:56
"""
input BatchPrintCertificatesRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.BatchPrintCertificatesRequest") {
	"""文件类型 1-PDF   2-IMAGE
		@see FileTypes
	"""
	fileType:Int!
	"""文件打印方式 1-连贯打印 2-单个打印"""
	printType:Int!
	"""是否合并打印（有连贯打印才有合并打印选项）
		1-是，0-否
	"""
	merge:String
	"""打印模板来源  (打印模板是读取方案配置，还是读取学员端选择模板)
		读方案 = 1  读学员选择 = 2
	"""
	printSource:Int!
	"""打印端口
		方案端 = 1  学员端 = 2
	"""
	printPort:Int!
	"""服务商类型
		1 网校
		5 分销商
		6 专题管理员
	"""
	servicerType:Int!
}
"""批量打印证书请求
	<AUTHOR>
	@date 2022/12/15 21:01
"""
input BatchPrintCertificatesSyncRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.BatchPrintCertificatesSyncRequest") {
	"""学号集合"""
	studentNos:[String]
	"""文件类型 1-PDF   2-IMAGE
		@see FileTypes
	"""
	fileType:Int!
}
"""证书打印请求
	<AUTHOR>
"""
input CertificatePrintRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.CertificatePrintRequest") {
	"""参训资格id"""
	qualificationId:String
	"""学号"""
	studentNo:String
	"""文件类型 1-PDF   2-IMAGE
		@see FileTypes
	"""
	fileType:Int!
	"""扩展数据
		key 对应courseId
	"""
	data:[Extend]
}
"""证书打印请求 - 选择模板
	<AUTHOR>
	@date 2023/11/07
"""
input ChooseTemplateCertificatePrintRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.ChooseTemplateCertificatePrintRequest") {
	"""参训资格id"""
	qualificationId:String
	"""学号"""
	studentNo:String
	"""证书模板id"""
	templateId:String
	"""文件类型 1-PDF   2-IMAGE
		@see FileTypes
	"""
	fileType:Int!
}
"""旧平台迁移数据打印请求
	<AUTHOR>
	@date 2024/1/31
"""
input DataMigrationCertificatePrintRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.DataMigrationCertificatePrintRequest") {
	"""历史学习数据id"""
	learningDataId:String
	"""文件类型 1-PDF  2-IMAGE
		@see FileTypes
	"""
	fileType:Int!
	"""扩展数据"""
	data:[Extend]
}
input DataMigrationCertificatePrintWithOutLoginRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.DataMigrationCertificatePrintWithOutLoginRequest") {
	userId:String
	"""历史学习数据id"""
	learningDataId:String
	"""文件类型 1-PDF  2-IMAGE
		@see FileTypes
	"""
	fileType:Int!
	"""扩展数据"""
	data:[Extend]
}
"""扩展数据
	<AUTHOR>
	@date 2024/1/31
"""
input Extend @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.Extend") {
	"""键"""
	key:String
	"""值"""
	value:String
}
"""获取证书快照请求
	<AUTHOR>
"""
input GetCertificateSnapshotRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.GetCertificateSnapshotRequest") {
	"""证书id"""
	certificateId:String
	"""证书快照id"""
	snapshotId:String
}
"""导入名单批量打印
	<AUTHOR>
	@date 2024/4/11
"""
input ImportListBatchPrintCertificatesRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.ImportListBatchPrintCertificatesRequest") {
	"""证书模板id"""
	templateId:String
	"""文件类型 1-PDF   2-IMAGE
		@see FileTypes
	"""
	fileType:Int!
	"""文件打印方式 1-连贯打印 2-单个打印"""
	printType:Int!
	"""是否合并打印（有连贯打印才有合并打印选项）
		1-是，0-否
	"""
	merge:String
	"""打印模板来源  (打印模板是读取方案配置，还是读取学员端选择模板)
		读方案 = 1  读学员选择 = 2
	"""
	printSource:Int!
	"""打印端口 - 导入名单打印为学员端
		方案端 = 1  学员端 = 2
	"""
	printPort:Int!
}
"""方案信息
	<AUTHOR>
	@date 2023/9/11
"""
input SchemeMessage @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.SchemeMessage") {
	"""学习方案id"""
	schemeId:String
	"""学习方案名字"""
	schemeName:String
}
"""根据学号批量打印"""
input StudentBatchPrintCertificatesRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.StudentBatchPrintCertificatesRequest") {
	"""证书模板id"""
	templateId:String
	"""文件类型 1-PDF   2-IMAGE
		@see FileTypes
	"""
	fileType:Int!
	"""文件打印方式 1-连贯打印 2-单个打印"""
	printType:Int!
	"""是否合并打印（有连贯打印才有合并打印选项）
		1-是，0-否
	"""
	merge:String
	"""打印模板来源  (打印模板是读取方案配置，还是读取学员端选择模板)
		读方案 = 1  读学员选择 = 2
	"""
	printSource:Int!
	"""打印端口
		方案端 = 1  学员端 = 2
	"""
	printPort:Int!
	"""服务商类型
		1 网校
		5 分销商
		6 专题管理员
	"""
	servicerType:Int!
}
"""学员端打印请求
	<AUTHOR>
	@date 2025/4/15 15:40
"""
input StudentPrintCertificateRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.StudentPrintCertificateRequest") {
	"""参训资格id"""
	qualificationId:String
	"""学号"""
	studentNo:String
	"""文件类型 1-PDF   2-IMAGE
		@see FileTypes
	"""
	fileType:Int!
	"""方案id"""
	schemeId:String
	"""扩展数据
		key 对应courseId
	"""
	data:[Extend]
}
"""<AUTHOR>
	@date 2024/5/23
"""
input BatchPrintingRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.batch.BatchPrintingRequest") {
	"""姓名"""
	name:String
	"""证件号"""
	idCard:String
	"""学习方案名称"""
	schemeName:String
	"""导入状态
		1 = 成功
		2 = 失败
	"""
	importStatus:Int!
	"""服务商类型
		1 网校
		5 分销商
	"""
	servicerType:Int!
}
"""证书打印导出失败数据请求
	<AUTHOR>
	@date 2024/7/9 11:12
"""
input ExportCertificateFailedDataRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.batch.ExportCertificateFailedDataRequest") {
	"""主任务id"""
	mainTaskId:String!
}
"""导出失败数据请求
	<AUTHOR>
	@date 2024/5/17
"""
input ExportFailedDataRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.batch.ExportFailedDataRequest") {
	"""姓名"""
	name:String
	"""证件号"""
	idCard:String
	"""学习方案名称"""
	schemeName:String
	"""导入状态
		1 = 成功
		2 = 失败
	"""
	importStatus:Int!
}
"""导入列表数据请求
	<AUTHOR>
	@date 2024/5/17
"""
input ImportListDataRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.batch.ImportListDataRequest") {
	"""起始页码"""
	pageNo:Int!
	"""每页记录数
		默认10
	"""
	pageSize:Int!
	"""姓名"""
	name:String
	"""证件号"""
	idCard:String
	"""学习方案名称"""
	schemeName:String
	"""导入状态
		1 = 成功
		2 = 失败
	"""
	importStatus:Int!
}
"""导入名单校验请求
	<AUTHOR>
	@date 2024/05/13
"""
input ImportListVerificationRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.batch.ImportListVerificationRequest") {
	"""文件路径"""
	filePath:String!
	"""文件名字"""
	fileName:String
}
"""打印列表删除所有学员请求
	<AUTHOR>
	@date 2024/5/13
"""
input PrintTheListRemoveAllStudentRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.batch.PrintTheListRemoveAllStudentRequest") {
	"""姓名"""
	name:String
	"""证件号"""
	idCard:String
	"""学习方案名称"""
	schemeName:String
	"""导入状态
		1 = 成功列表
		2 = 失败列表
	"""
	importStatus:Int!
}
"""打印列表删除学生请求
	<AUTHOR>
	@date 2024/5/13
"""
input PrintTheListRemoveStudentRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.batch.PrintTheListRemoveStudentRequest") {
	"""导入学员打印表id"""
	id:String!
}
"""筛选批量打印证书请求
	<AUTHOR>
	@date 2023/8/29
"""
input ScreeningBatchPrintCertificatesRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.ScreeningBatchPrintCertificatesRequest") {
	"""学习方案id"""
	schemeMessages:[SchemeMessage]
	"""是否筛选已导出证明"""
	screening:Boolean!
	"""文件类型 1-PDF   2-IMAGE
		@see FileTypes
	"""
	fileType:Int!
	"""专题id"""
	specialId:String
}
enum SortPolicy @type(value:"com.fjhb.ms.scheme.learning.query.kernel.constants.SortPolicy") {
	ASC
	DESC
}
enum StudentSchemeLearningSortField @type(value:"com.fjhb.ms.scheme.learning.query.kernel.constants.StudentSchemeLearningSortField") {
	REGISTER_TIME
	SCHEME_YEAR
}
"""批量任务信息
	<AUTHOR>
	@date 2024/5/17
"""
type BatchTaskInformation @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.batch.BatchTaskInformation") {
	"""主任务状态
		已创建 = 0
		已就绪 = 1
		执行中 = 2
		已完成 = 3
	"""
	taskStatus:Int!
	"""主任务状态
		未处理 = 0
		成功 = 1
		失败 = 2
		就绪失败 = 3
	"""
	result:Int!
	"""子任务总数"""
	SubtasksTotal:Long!
	"""子任务完成数"""
	SubtasksFinishNumber:Long!
}
"""导入学员数据  ImportedSuccessData
	<AUTHOR>
	@date 2024/5/15
"""
type ImportedData @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.batch.ImportedData") {
	"""导入学员打印表id"""
	id:String
	"""姓名"""
	name:String
	"""证件号"""
	idCard:String
	"""学习方案名称"""
	schemeName:String
	"""学号"""
	studentNo:String
	"""导入状态"""
	importStatus:Int!
	"""失败原因"""
	reasonForFailure:String
}
"""上传成功失败数量
	<AUTHOR>
	@date 2024/5/21
"""
type SuccessFailureQuantityRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.batch.SuccessFailureQuantityRequest") {
	"""成功条数"""
	success:Int!
	"""失败条数"""
	fail:Int!
}
"""打印前置校验返回值
	<AUTHOR>
	@date 2025/3/5 8:52
"""
type CheckPrintConditionResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.CheckPrintConditionResponse") {
	"""code
		200 成功
		50001 成果数据未同步成功
	"""
	code:String
	"""message"""
	message:String
	"""路径(单个打印)"""
	path:String
	"""主任务id"""
	taskId:String
	"""用户信息"""
	userInfo:[UserInfo]
	"""是否需要强制完成调查问卷"""
	needForceQuestionnaire:Boolean!
	"""需要强制完成的调查问卷信息"""
	unaccomplishedQuestionnaire:[UnaccomplishedQuestionnaire]
}
type UserInfo @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.CheckPrintConditionResponse$UserInfo") {
	"""姓名"""
	userName:String
	"""身份证"""
	idCard:String
	"""学号"""
	studentNo:String
}
"""未完成问卷信息
	<AUTHOR>
	@date 2025/4/15 10:15
"""
type UnaccomplishedQuestionnaire @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.exam.UnaccomplishedQuestionnaire") {
	"""未完成问卷id"""
	unaccomplishedQuestionnaireId:String
	"""学习方式id"""
	learningId:String
	"""允许开始时间"""
	allowStartTime:DateTime
	"""允许结束时间"""
	allowEndTime:DateTime
}

scalar List
type ImportedDataPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ImportedData]}
