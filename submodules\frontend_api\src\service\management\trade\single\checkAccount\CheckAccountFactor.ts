/*
 * @Description: 对账工厂
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-25 14:27:32
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-16 09:48:52
 */
import QueryCheckAccount from '@api/service/management/trade/single/checkAccount/query/QueryCheckAccount'
import MutationCheckAccount from './mutation/MutationCheckAccount'
class InvoiceFactor {
  /**
   * 个人报名对账查询
   */
  get queryCheckAccount() {
    return new QueryCheckAccount()
  }
  /**
   * 个人报名对账业务
   */
  get mutationCheckAccount() {
    return new MutationCheckAccount()
  }
}
export default InvoiceFactor
