<template>
  <el-card shadow="never" class="m-card f-mb15 is-header-sticky">
    <el-collapse v-model="activeNames" accordion>
      <el-collapse-item name="4" class="m-collapse-item">
        <template slot="title">
          <span class="el-collapse-item__header">
            管理员帐号
          </span>
        </template>
        <div class="f-plr20 f-pt40">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <!--右侧输入框及选择器默认长度为100%，中长.form-l，短.form-s-->
              <el-form
                ref="adminAccountForm"
                :model="adminAccountData"
                label-width="auto"
                class="m-form"
                :rules="rules"
              >
                <el-form-item label="管理员姓名：" prop="name">
                  <el-input v-model="adminAccountData.name" clearable placeholder="请输入管理员名称" class="form-l" />
                </el-form-item>
                <el-form-item label="管理员帐号：" prop="authenticationIdentity">
                  <el-input
                    v-model="adminAccountData.authenticationIdentity"
                    clearable
                    placeholder="管理员帐号支持6-18位由数字、字母、符号组合"
                    class="form-l"
                  />
                </el-form-item>
                <el-form-item label="手机号：" prop="phone">
                  <el-input
                    v-model="adminAccountData.phone"
                    clearable
                    placeholder="请输入11位有效手机号，可作为登录帐号"
                    class="form-l"
                  />
                </el-form-item>
                <el-form-item label="密码：" class="is-required" prop="password">
                  <div class="form-l">
                    <el-input
                      v-model="adminAccountData.password"
                      clearable
                      show-password
                      placeholder="请输入8~18位由数字、字母或符号组成的密码"
                      auto-complete="new-password"
                      @input="checkoutPasswordStrength"
                    />
                    <!--密码安全判断-->
                    <div v-if="passwordStrengthLow" class="psw-tips">
                      <el-progress :percentage="33.33" color="#e93737" :show-text="false"></el-progress>
                      <!--弱：txt-l，中：txt-m，强：txt-h-->
                      <span class="txt txt-l">弱</span>
                    </div>
                    <div v-if="passwordStrengthIntermediate" class="psw-tips">
                      <el-progress :percentage="66.66" color="#ee9e2d" :show-text="false"></el-progress>
                      <!--弱：txt-l，中：txt-m，强：txt-h-->
                      <span class="txt txt-m">中</span>
                    </div>
                    <div v-if="passwordStrengthHigh" class="psw-tips">
                      <el-progress :percentage="100" color="#49b042" :show-text="false"></el-progress>
                      <!--弱：txt-l，中：txt-m，强：txt-h-->
                      <span class="txt txt-h">强</span>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="确认密码：" class="is-required" prop="rePassword">
                  <el-input
                    v-model="adminAccountData.rePassword"
                    clearable
                    show-password
                    placeholder="请再次确认密码"
                    class="form-l"
                    auto-complete="new-password"
                  />
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-collapse-item>
    </el-collapse>
  </el-card>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'
  import { Administrator } from '@api/ms-gateway/ms-servicercontract-v1'
  @Component
  export default class extends Vue {
    @Ref('adminAccountForm') adminAccountForm: any
    activeNames: Array<string> = ['4']
    adminAccountData: Administrator = new Administrator()
    props = { multiple: true }
    rePassword = ''
    rules = {
      name: [{ required: true, message: '请输入管理员姓名', trigger: 'blur' }],
      authenticationIdentity: [{ required: true, message: '请输入管理员账号', trigger: 'blur' }],
      phone: [{ required: true, validator: this.validatePhone, trigger: 'blur' }],
      password: [{ required: true, validator: this.validatePassword, trigger: 'blur' }],
      rePassword: [{ required: true, validator: this.validateAgainPassword, trigger: 'blur' }]
    }
    passwordStrengthLow = false
    passwordStrengthIntermediate = false
    passwordStrengthHigh = false
    reg = new RegExp('^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z\\W]{8,18}$') // 验证密码的正则是否符合的正则
    created() {
      this.$emit('childrenThis', this, 4)
    }
    //验证手机号格式
    validatePhone(rule: any, value: any, callback: any) {
      const reg = new RegExp(/^[1]([3-9])[0-9]{9}$/)
      if (value === '') {
        callback(new Error('请输入手机号'))
      } else {
        reg.test(value) ? callback() : callback(new Error('请输入正确的手机号'))
      }
    }
    //密码强弱验证
    checkoutPasswordStrength() {
      const reg1 = /^.{1,6}$|^\d{7,}$|^[a-zA-Z]{7,}$|^(?=[\x21-\x7e]+)[^A-Za-z0-9]{7,}$/ //密码低强度正则--纯数字或字母或字符或长度1-6
      const reg2 = /^(?!\d+$)[a-zA-Z0-9]{7,}$|^(?![0-9]+$)[^a-zA-Z]{7,}$|^(?![a-zA-Z]+$)[^0-9]{7,}$/ //密码中强度正则--有两种且长度在7-10
      const reg3 = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[^0-9a-zA-Z]).{11,}$/ //密码高强度正则--三种都有且长度大于10
      const reg4 = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[^0-9a-zA-Z]).{7,10}$/ //密码中强度正则--介于7-10位的三种字符都有的密码
      if (!this.adminAccountData.password) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = false
      } else if (reg1.test(this.adminAccountData.password)) {
        this.passwordStrengthLow = true
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = false
      } else if (reg2.test(this.adminAccountData.password)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = true
        this.passwordStrengthHigh = false
      } else if (reg3.test(this.adminAccountData.password)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = true
      } else if (reg4.test(this.adminAccountData.password)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = true
        this.passwordStrengthHigh = false
      }
    }
    //密码验证
    validatePassword(rule: any, value: any, callback: any) {
      if (!this.reg.test(this.adminAccountData.password)) {
        callback(new Error('请输入8~18位由数字、字母或符号组成的密码'))
      } else {
        callback()
      }
    }
    //再次输入密码验证
    validateAgainPassword(rule: any, value: any, callback: any) {
      if (!value) {
        callback(new Error('请再次输入密码'))
      } else if (value != this.adminAccountData.password) {
        callback(new Error('密码不一致请重新输入'))
      } else {
        callback()
      }
    }
    // 校验
    handleCheck() {
      return new Promise(resolve => {
        let result = {
          status: true,
          msg: '',
          data: {}
        }
        this.adminAccountForm.validate((valid: any) => {
          if (valid) {
            result.data = this.adminAccountData
          } else {
            result = {
              status: false,
              msg: '管理员账号未填写完整，请检查！',
              data: {}
            }
            return false
          }
        })
        resolve(result)
      })
    }
  }
</script>

<style lang="scss" scoped>
  .el-collapse-item__header {
    font-size: 16px;
    font-weight: bold;
    padding-left: 20px;
    padding-right: 10px;
  }
</style>
