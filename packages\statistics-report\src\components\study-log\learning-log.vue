<!-- 进入学习日志 -->
<template>
  <div>
    <template v-if="tableData.length">
      <el-table
        stripe
        :data="tableData"
        max-height="500"
        highlight-current-row
        class="m-table f-mt15"
        v-loading="tableLoading"
        :span-method="objectSpanMethod"
      >
        <!-- <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column> -->
        <el-table-column label="No." min-width="60">
          <template slot-scope="{ row }">{{ row.index }}</template>
        </el-table-column>
        <el-table-column label="课程名称" min-width="240">
          <template slot-scope="{ row }">{{ row.courseName || '-' }}</template>
        </el-table-column>
        <el-table-column label="监管通过时间" min-width="200">
          <template slot-scope="{ row }">{{ row.userLogInfo[0] ? row.userLogInfo[0].verifiedTime : '-' }}</template>
        </el-table-column>
        <!-- <el-table-column label="拍摄照片" min-width="150">
          <template slot-scope="{ row }">
            <img
              v-if="row.userLogInfo[0] && row.userLogInfo[0].photoList"
              :src="
                row.userLogInfo[0] && row.userLogInfo[0].photoList ? addAccessToken(row.userLogInfo[0].photoList) : ''
              "
              alt=""
              class="u-benchmark-photos-small"
            />
            <img v-else src="@design/admin/assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
          </template>
        </el-table-column> -->
        <el-table-column label="监管方式" min-width="150">
          <template slot-scope="{ row }">{{ VerifyType.map.get(row.userLogInfo[0].antiType) }}</template>
        </el-table-column>
        <el-table-column label="监管结果" min-width="140" align="center">
          <template slot-scope="{ row }">
            <div v-if="result(row.userLogInfo[0])">
              <el-badge is-dot type="success" class="badge-status">通过</el-badge>
            </div>
            <div v-if="notPass(row.userLogInfo[0])">
              <el-badge is-dot type="danger" class="badge-status">不通过</el-badge>
            </div>
            <template
              v-if="
                row.userLogInfo[0].antiType === VerifyTypeEnum.face ||
                row.userLogInfo[0].antiType === VerifyTypeEnum.tencentFace
              "
            >
              <img
                v-if="row.userLogInfo[0] && row.userLogInfo[0].photoList"
                :src="
                  row.userLogInfo[0] && row.userLogInfo[0].photoList ? addAccessToken(row.userLogInfo[0].photoList) : ''
                "
                alt=""
                class="u-benchmark-photos-small"
              />
              <img v-else src="@design/admin/assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
            </template>
          </template>
        </el-table-column>
      </el-table>
      <hb-pagination :page="page" v-bind="page"></hb-pagination
    ></template>
    <template v-else>
      <!--通用空数据-->
      <div class="m-no-date">
        <img class="img" src="@design/admin/assets/images/no-data-normal.png" alt="" />
        <div class="date-bd">
          <p class="f-f15 f-c9">暂时还没有内容~</p>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import UserLearningList from '@api/service/management/anticheat/UserLearningList'
  import { SupervisionTypeEnum } from '@hbfe-biz/biz-anticheat/dist/log/enums/SupervisionEnum'
  import UserLearningLogItem from '@api/service/management/anticheat/models/UserLearningLogItem'
  import { CurrentResultEnum } from '@hbfe-biz/biz-anticheat/dist/log/enums/SupervisionEnum'
  import FileModule from '@api/service/common/file/FileModule'
  import UserLogInfo from '@api/service/management/anticheat/models/UserLogInfo'
  import VerifyType, { VerifyTypeEnum } from '@hbfe-biz/biz-anticheat/dist/config/enums/VerifyType'
  class MergeCourse {
    courseWare: string
    count: number
  }
  @Component
  export default class extends Vue {
    // page
    page: UiPage
    constructor() {
      super()
      this.page = new UiPage(this.doQuery, this.doQuery)
    }

    tableLoading = false

    // 表单数据
    tableData: Array<UserLearningLogItem> = new Array<UserLearningLogItem>()
    /**
     * 监管方式枚举
     */
    VerifyTypeEnum = VerifyTypeEnum
    VerifyType = VerifyType
    // tableData: any = [
    //   {
    //     index: 0,
    //     courseName: '补贴培训bug2',
    //     courseId: '2ca4cd0788d6accb01890a67d7af0252',
    //     courseWareId: '',
    //     courseWareName: '',
    //     userLogInfo: [
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: 1,
    //         verifiedTime: null,
    //         createdTime: null,
    //         currentResult: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       },
    //       {
    //         status: null,
    //         verifiedTime: '2023-07-26 15:13:55',
    //         createdTime: null,
    //         currentResult: 1,
    //         photoList:
    //           '/ms-file/protected/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/anti/20230726/b5bf7f66cb7342a1a789a488995718e5.jpg',
    //         photoCreatedTime: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       }
    //     ],
    //     courseWareUserLearningLog: []
    //   },
    //   {
    //     index: 0,
    //     courseName: 'fan2',
    //     courseId: '2ca4cd0788d6accb01890a67d7af0252',
    //     courseWareId: '',
    //     courseWareName: '',
    //     userLogInfo: [],
    //     courseWareUserLearningLog: []
    //   },
    //   {
    //     index: 0,
    //     courseName: 'fan8',
    //     courseId: '2ca4cd0788d6accb01890a67d7af0253',
    //     courseWareId: '',
    //     courseWareName: '',
    //     userLogInfo: [],
    //     courseWareUserLearningLog: []
    //   },
    //   {
    //     index: 0,
    //     courseName: '阿斯顿撒打死',
    //     courseId: '2ca4cd0788d6accb01890a67d7af0253',
    //     courseWareId: '',
    //     courseWareName: '',
    //     userLogInfo: [
    //       {
    //         status: null,
    //         verifiedTime: '2023-07-26 15:32:26',
    //         createdTime: null,
    //         currentResult: 1,
    //         photoList:
    //           '/ms-file/protected/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/anti/20230726/487ac23f602a4245bfcebea068c4d78a.jpg',
    //         photoCreatedTime: null,
    //         currentLength: 0,
    //         currentSchedule: 0
    //       }
    //     ],
    //     courseWareUserLearningLog: []
    //   },
    //   {
    //     index: 0,
    //     courseName: 'fan6',
    //     courseId: '2ca4cd0788d6accb01890a67d7af0253',
    //     courseWareId: '',
    //     courseWareName: '',
    //     userLogInfo: [],
    //     courseWareUserLearningLog: []
    //   },
    //   {
    //     index: 0,
    //     courseName: 'fan9',
    //     courseId: '2ca4cd0788d6accb01890a67d7af0254',
    //     courseWareId: '',
    //     courseWareName: '',
    //     userLogInfo: [],
    //     courseWareUserLearningLog: []
    //   }
    // ]

    // 枚举
    supervisionTypeEnum: SupervisionTypeEnum

    // 合并二维数组
    mergeList = new Array<Array<number>>()

    // 查询对象
    queryObj = new UserLearningList()

    // 查询
    async doQuery() {
      this.tableLoading = true
      try {
        console.log(this.$route.query)
        this.queryObj.userId = this.$route.query.userId as string
        if (!this.queryObj.userId) throw new Error('用户id为空')
        this.queryObj.qualificationId = this.$route.query.qualificationId as string
        if (!this.queryObj.qualificationId) throw new Error('参训人员id为空')
        this.queryObj.studentNo = this.$route.query.studentNo as string
        if (!this.queryObj.studentNo) throw new Error('学号为空')
        if (!this.$route.query.schemeType) throw new Error('培训类型为空')
        this.queryObj.schemeType = Number(this.$route.query.schemeType)
        this.tableData = await this.queryObj.queryBeforeCourseLogList(this.page)
      } catch (error) {
        console.log(error)
      } finally {
        this.tableLoading = false
      }
    }

    // 匹配结果
    get result() {
      return (res: UserLogInfo) => {
        return res && res.currentResult == CurrentResultEnum.PASS
      }
    }
    get notPass() {
      return (res: UserLogInfo) => {
        return res && res.currentResult == CurrentResultEnum.NOT_PASS
      }
    }

    // 初始化请求
    async initSearch() {
      this.page.pageNo = 1
      await FileModule.applyResourceAccessToken()
      this.doQuery()
    }

    // 合并规则
    getMergeRules() {
      const mergeList = new Array<Array<number>>()
      mergeList.length = this.tableData.length
      mergeList.fill([0, 0])
      const mergeCourseList = new Array<MergeCourse>()
      const courseWareIdList = new Array<string>()
      this.tableData.forEach((item: UserLearningLogItem) => {
        courseWareIdList.push(item.courseId)
      })
      this.tableData.forEach((item: any) => {
        const mergeObj = new MergeCourse()
        const mergeNum = courseWareIdList.reduce((prev: any, cur: string) => {
          if (cur in prev) {
            prev[cur]++
          } else {
            prev[cur] = 1
          }
          return prev
        }, {})
        if (!JSON.stringify(mergeCourseList).includes(item.courseId)) {
          mergeObj.courseWare = item.courseId
          mergeObj.count = mergeNum[item.courseId]
        } else {
          mergeObj.courseWare = ''
          mergeObj.count = 0
        }
        mergeCourseList.push(mergeObj)
      })
      console.log(mergeCourseList)
      for (let i = 0; i < mergeCourseList.length; i++) {
        mergeList[i] = [mergeCourseList[i].count, mergeCourseList[i].count >= 1 ? 1 : 0]
      }
      this.mergeList = mergeList
    }

    objectSpanMethod({ rowIndex, columnIndex }: { rowIndex: number; columnIndex: number }) {
      const obj = {
        rowspan: 0,
        colspan: 0
      }
      if ((columnIndex === 0 || columnIndex === 1) && this.mergeList[rowIndex]) {
        obj.rowspan = this.mergeList[rowIndex][0]
        obj.colspan = this.mergeList[rowIndex][1]
        console.log(obj, this.mergeList)
        return obj
      }
    }

    @Watch('tableData', { immediate: true, deep: true })
    tableDataChange(newVal: Array<UserLearningLogItem>) {
      if (newVal && newVal.length) {
        this.getMergeRules()
      }
    }

    get addAccessToken() {
      return (url: string) => {
        return `${window.location.origin}/mfs${url}?token=${FileModule.resourceAccessToken}`
      }
    }
  }
</script>
