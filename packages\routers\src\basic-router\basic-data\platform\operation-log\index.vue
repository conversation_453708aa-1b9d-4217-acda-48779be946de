<route-meta>
{
"isMenu": true,
"title": "操作日志",
"sort": 3,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="操作时间">
                <double-date-picker
                  :begin-create-time.sync="queryParams.orderCreateTime"
                  :end-create-time.sync="queryParams.orderCreateTime"
                  begin-time-placeholder="开始时间"
                  end-time-placeholder="结束时间"
                ></double-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="模块名称">
                <el-select v-model="queryParams.select" clearable placeholder="请选择模块名称">
                  <el-option
                    v-for="item in moduleNameList"
                    :key="item.id"
                    :value="item.id"
                    :label="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary" @click="search">查询</el-button>
                <el-button @click="reset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="模块名称" min-width="300">
            <template slot-scope="scope">{{ scope.row.moduleName }}</template>
          </el-table-column>
          <el-table-column label="管理员" min-width="300">
            <template slot-scope="scope">{{ scope.row.administrator }}</template>
          </el-table-column>
          <el-table-column label="操作时间" min-width="180">
            <template slot-scope="scope">{{ scope.row.operatingTime }}</template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template>
              <el-button type="text" size="mini" @click="detail(1)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
      </el-card>
    </div>
    <el-drawer title="日志详情" :visible.sync="isShow" size="1000px" custom-class="m-drawer">
      <div class="drawer-bd f-mt20 f-mlr40">
        <el-timeline>
          <el-timeline-item>{{ logDetail.detail1 }}</el-timeline-item>
          <el-timeline-item>{{ logDetail.detail2 }}</el-timeline-item>
          <el-timeline-item>{{ logDetail.detail3 }}</el-timeline-item>
          <el-timeline-item>{{ logDetail.detail4 }}</el-timeline-item>
          <el-timeline-item>{{ logDetail.detail5 }}</el-timeline-item>
        </el-timeline>
      </div>
    </el-drawer>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src//double-date-picker/index.vue'

  @Component({
    components: { DoubleDatePicker }
  })
  export default class extends Vue {
    queryParams = {}
    moduleNameList = [{ id: 1, value: '233' }]
    isShow = false
    select = ''
    date = ''
    tableData = [
      { moduleName: '福建华博教育科技股份有限公司', administrator: 'admin', operatingTime: '2020-11-11 12:20:20' },
      { moduleName: '福建华博教育科技股份有限公司', administrator: 'admin', operatingTime: '2020-11-11 12:20:20' }
    ]
    logDetail = {
      detail1: '修改网校名称【福建专技培训平台】改为【福州专技培训平台】',
      detail2: '修改门户轮播图修',
      detail3: '修改内容名称【原属性值】改为【新属性值】',
      detail4: '修改内容名称【原属性值】改为【新属性值】',
      detail5: '修改门户轮播图'
    }
    page: UiPage
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    async doQueryPage() {
      //
    }
    // 查看详情
    async detail(id: string) {
      this.isShow = true
    }
    // 查询
    async search() {
      await this.doQueryPage()
    }
    // 重置
    async reset() {
      this.queryParams = {}
      await this.doQueryPage()
    }
  }
</script>
