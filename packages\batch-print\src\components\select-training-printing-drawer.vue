<template>
  <div>
    <el-drawer
      title="选择培训证明进行打印"
      :visible.sync="visible"
      size="700px"
      custom-class="m-drawer"
      :append-to-body="true"
      destroy-on-close
      @close="cancel"
    >
      <div class="drawer-bd">
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form ref="form" :model="printCertificationsVo" label-width="auto" class="m-form f-mt50">
              <el-form-item>
                <div slot="label">
                  <span class="f-vm">模版类型</span>
                  <span>：</span>
                </div>
                <el-radio-group v-model="printCertificationsVo.printType">
                  <el-radio :label="1">班级配置模版</el-radio>
                  <el-radio :label="2">其他模版</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="printCertificationsVo.printType === 2">
                <div slot="label">
                  <span class="f-vm">培训证明模版</span>
                  <span>：</span>
                </div>
                <el-button size="small" type="primary" class="mr-10" @click="chooseTemplate" v-if="!templateInfo.id"
                  >选择模版
                </el-button>
                <!-- 选择后出现 -->
                <p v-if="templateInfo.id">
                  {{ templateInfo.name }}
                  <el-button type="text" class="f-ml20" @click="chooseTemplate">替换模板</el-button>
                </p>
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="cancel">取消</el-button>
                <el-button type="primary" @click="sure" :loading="sureBtnLoading">确定</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>

    <!-- 选择培训证明模版组件 -->
    <student-batch-print-proof ref="studentPrintRef" @getTemplateInfo="getTemplateInfo"></student-batch-print-proof>

    <el-dialog title="提示" :visible.sync="previewDialog" width="300px" class="m-dialog" @close="cancelPreview">
      <div>是否要前往证书预览页？</div>
      <div slot="footer">
        <el-button @click="cancelPreview">取 消</el-button>
        <el-button type="primary" @click="openPreview">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Prop } from 'vue-property-decorator'
  import { debounce, bind } from 'lodash-decorators'
  import PrintCertificationsVo from '@api/service/management/personal-leaning/mutation/vo/PrintCertificationsVo'
  import MutationBatchPrintTraining from '@api/service/management/personal-leaning/mutation/MutationBatchPrintTraining'
  import StudentBatchPrintProof from '@hbfe/jxjy-admin-batchPrint/src/student-batch-print-proof.vue'
  import { FileTypesEnum } from '@api/service/common/enums/personal-leaning/FileTypes'
  import { Response } from '@hbfe/common'
  import {
    CheckPrintConditionResponse,
    ChooseTemplateCertificatePrintRequest
  } from '@api/platform-gateway/platform-certificate-v1'
  import CertificateTemplateResponseVo from '@api/service/management/personal-leaning/query/vo/CertificateTemplateResponseVo'

  @Component({
    components: {
      StudentBatchPrintProof
    }
  })
  export default class extends Vue {
    @Ref('studentPrintRef')
    studentPrintRef: StudentBatchPrintProof

    /**
     * 接收到的学员参训资格id
     */
    @Prop({
      type: String,
      default: ''
    })
    studentQualificationId: string

    /**
     * 接收到的学员id数组
     */
    @Prop({
      type: Array,
      default: () => {
        return new Array<string>()
      }
    })
    studentNoList: string[]

    /**
     * 是否显示抽屉控制
     */
    visible = false

    //模版信息
    templateInfo = new CertificateTemplateResponseVo()

    // 按钮加载状态
    sureBtnLoading = false

    // 批量打印证明实例
    batchPrintTrainingModule = new MutationBatchPrintTraining()

    // 数据
    printCertificationsVo = new PrintCertificationsVo()

    open() {
      this.visible = true
    }

    // 确定按钮
    @bind
    @debounce(200)
    async sure() {
      //如果没有选择模版类型校验
      if (!this.printCertificationsVo.printType) {
        return this.$message.error('请选择模版类型')
      }
      //   学员的班级是否有配置模版，没有要校验
      if (this.printCertificationsVo.printType === 1) {
        // 接口参数
        this.batchPrintTrainingModule.printCertificateParams.studentNo = this.studentNoList[0]
        this.batchPrintTrainingModule.printCertificateParams.fileType = FileTypesEnum.PDF
        this.batchPrintTrainingModule.printCertificateParams.qualificationId = this.studentQualificationId
        // 走接口打印
        try {
          this.sureBtnLoading = true
          const res = await this.batchPrintTrainingModule.doPrintTraining()
          if (!res.status.isSuccess()) {
            return this.errorResTips(res)
          }
          if (res.data?.code === '50001') {
            return this.$message.error('培训成果未推送，无法打印培训证明')
          }
          if (res.data?.path) {
            this.$message.success('打印成功')
            this.preview(res.data.path)
          }
        } catch (e) {
          console.log(e)
        } finally {
          this.sureBtnLoading = false
        }
      }

      //   模版类型为其他模版时校验
      if (this.printCertificationsVo.printType === 2) {
        if (!this.templateInfo.id) {
          return this.$message.error('请选择培训证明模版')
        }
        // 走接口打印
        try {
          this.sureBtnLoading = true

          // 接口参数
          const params = new ChooseTemplateCertificatePrintRequest()
          params.templateId = this.templateInfo.id
          params.studentNo = this.studentNoList[0]
          params.fileType = FileTypesEnum.PDF
          params.qualificationId = this.studentQualificationId
          const res = await this.batchPrintTrainingModule.doPrintTrainingStudent(params)

          if (res.data?.code === '50001') {
            return this.$message.error('培训成果未推送，无法打印培训证明')
          }
          if (res.data?.path && res.status) {
            this.$message.success('打印成功')
            this.preview(res.data.path)
          } else {
            // return this.$message.error('存在学员的班级未配置证明模板，请检查')
            this.errorResTips(res)
          }
        } catch (e) {
          console.log(e)
        } finally {
          this.sureBtnLoading = false
        }
      }
    }

    // 取消按钮
    cancel() {
      // 将模版类型置空
      this.printCertificationsVo.printType = 0
      //   选择的模版清空
      this.templateInfo = new CertificateTemplateResponseVo()

      this.visible = false
      this.sureBtnLoading = false
    }

    getTemplateInfo(value: CertificateTemplateResponseVo) {
      this.templateInfo = value
    }

    // 选择模版按钮
    chooseTemplate() {
      // 打开选择证明模版的弹框
      this.studentPrintRef.showTemplateListDrawer(this.templateInfo.name)
    }

    templateUrl = ''

    previewDialog = false

    // 跳转新窗口打开证明预览
    preview(url: string) {
      this.templateUrl = url
      this.visible = false
      this.previewDialog = true
    }

    cancelPreview() {
      this.previewDialog = false
      this.templateUrl = ''
    }

    openPreview() {
      this.previewDialog = false
      window.open(this.templateUrl, '_blank')
    }

    errorResTips(res: Response<CheckPrintConditionResponse>) {
      const errorMsg = res.status?.errors[0]?.message
      if (errorMsg) {
        const msg = errorMsg.replace(/['{}?(),"\\/\b\f\n\r\t.:;]/g, '')
        this.$message.error(msg.replace(/[a-z0-9A-Z^\s]+/g, ''))
      } else {
        this.$message.error('打印失败！')
      }
    }
  }
</script>

<style></style>
