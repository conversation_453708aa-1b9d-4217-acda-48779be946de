import asyncCreateLearningScheme from './mutates/asyncCreateLearningScheme.graphql'
import asyncUpdateLearningScheme from './mutates/asyncUpdateLearningScheme.graphql'
import oneKeyPass from './mutates/oneKeyPass.graphql'
import studentLearningResourcePushIsCompleted from './mutates/studentLearningResourcePushIsCompleted.graphql'

export { asyncCreateLearningScheme, asyncUpdateLearningScheme, oneKeyPass, studentLearningResourcePushIsCompleted }
