import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-ahzjzh-scys-student'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class StudentLoginRequest {
  /**
   * 学员信息key【必填】
   */
  studentInfoKey?: string
}

export class StudentLoginResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 响应信息
   */
  message: string
  /**
   * 单点登录token
   */
  loginToken: string
  /**
   * 用户id【8.0继续教育通用平台】
   */
  userId: string
  accountId: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 安徽住建综合平台单点登录演示
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async studentLogin(
    request: StudentLoginRequest,
    mutate: DocumentNode = GraphqlImporter.studentLogin,
    operation?: string
  ): Promise<Response<StudentLoginResponse>> {
    return commonRequestApi<StudentLoginResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
