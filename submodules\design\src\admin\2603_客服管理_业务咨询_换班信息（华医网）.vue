<template>
  <el-main>
    <el-card shadow="never" class="m-card is-bg is-overflow-hidden">
      <div class="f-plr20 f-pt20">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="姓名">
                <el-input v-model="input" clearable placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="证件号">
                <el-input v-model="input" clearable placeholder="请输入证件号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="手机号">
                <el-input v-model="input" clearable placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="订单号">
                <el-input v-model="input" clearable placeholder="请输入订单号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="4" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--人员信息-->
        <el-collapse v-model="activeNames5" @change="handleChange" class="m-collapse no-border">
          <el-collapse-item name="1">
            <div slot="title" class="m-tit">
              <span class="tit-txt">人员信息</span>
            </div>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="240" highlight-current-row class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="姓名" min-width="160" fixed="left">
                <template>张依依</template>
              </el-table-column>
              <el-table-column label="证件号" min-width="200">
                <template>354875965412365896</template>
              </el-table-column>
              <el-table-column label="手机号" min-width="200">
                <template>13003831002</template>
              </el-table-column>
              <el-table-column label="单位地区" min-width="200">
                <template>福建省-福州市-鼓楼区</template>
              </el-table-column>
              <el-table-column label="注册时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-border-top is-sticky">
      <el-tab-pane label="学员信息" name="first">
        <div class="f-p15">详见 2601_客服管理_业务咨询_学员信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="学习内容" name="second">
        <div class="f-p15">详见 2602_客服管理_业务咨询_学习内容.vue</div>
      </el-tab-pane>
      <el-tab-pane label="换班信息" name="third">
        <div class="f-p15">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="f-flex f-align-center">
              <span class="tit-txt">待更换班级</span>
              <span class="f-cr f-ml5"
                >（换班的前提条件为：培训班同等价格）此功能操作仅支持【培训班】类型的培训方案。</span
              >
            </div>
            <div class="f-p20">
              <el-row :gutter="16" class="m-query">
                <el-form :inline="true" label-width="auto">
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item label="年度">
                      <el-select v-model="select" clearable filterable placeholder="请选择">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item label="班级名称">
                      <el-input v-model="input" clearable placeholder="请输入培训班名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                    <el-form-item class="f-tr">
                      <el-button type="primary">查询</el-button>
                      <el-button>重置</el-button>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="培训班信息" min-width="300">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <p><el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag>培训班名称支持两行</p>
                      <p class="f-f13 f-c9">
                        <el-tag type="danger" size="mini">华医网</el-tag>人社行业 / 2020年 / 公需科目
                      </p>
                    </div>
                    <div v-else>
                      <p><el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag>培训班名称支持两行</p>
                      <p class="f-f13 f-c9">人社行业 / 2020年 / 公需科目</p>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="单价(元)" min-width="140" align="right">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">3.15</div>
                    <div v-else-if="scope.$index === 1">52.36</div>
                    <div v-else>158.15</div>
                  </template>
                </el-table-column>
                <el-table-column label="考核情况" min-width="140" align="center">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-tag type="warning" size="small">待考核</el-tag>
                    </div>
                    <div v-else>
                      <el-tag type="success" size="small">已合格</el-tag>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="状态" min-width="100">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-badge is-dot type="primary" class="badge-status">冻结</el-badge>
                    </div>
                    <div v-else-if="scope.$index === 1">
                      <el-badge is-dot type="info" class="badge-status">失效</el-badge>
                    </div>
                    <div v-else>
                      <el-badge is-dot type="success" class="badge-status">有效</el-badge>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="来源类型" min-width="120" align="center">
                  <template>个人报名</template>
                </el-table-column>
                <el-table-column label="操作" min-width="100" align="center" fixed="right">
                  <template>
                    <el-button type="text" size="mini">换班</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!--分页-->
              <el-pagination
                background
                class="f-mt15 f-tr"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
          </el-card>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="f-flex f-align-center">
              <span class="tit-txt">换班记录</span>
            </div>
            <div class="f-p20">
              <el-row :gutter="16" class="m-query">
                <el-form :inline="true" label-width="auto">
                  <el-col :sm="12" :md="8" :lg="6">
                    <el-form-item label="年度">
                      <el-select v-model="select" clearable filterable placeholder="请选择">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :lg="6">
                    <el-form-item label="班级名称">
                      <el-input v-model="input" clearable placeholder="请输入培训班名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :lg="6">
                    <el-form-item label="换班状态">
                      <el-select v-model="select" clearable filterable placeholder="请选择">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :lg="4" class="f-fr">
                    <el-form-item class="f-tr">
                      <el-button type="primary">查询</el-button>
                      <el-button>重置</el-button>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="初始班级（换班前）" min-width="300">
                  <template>
                    <p>【培训班-选课规则】培训班名称支持两行</p>
                    <p class="f-f13 f-c9 f-ml5">人社行业 / 2020年 / 公需科目</p>
                  </template>
                </el-table-column>
                <el-table-column label="单价(元)" min-width="140" align="right">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">3.15</div>
                    <div v-else-if="scope.$index === 1">52.36</div>
                    <div v-else>158.15</div>
                  </template>
                </el-table-column>
                <el-table-column label="新班级（换班后）" min-width="300">
                  <template>
                    <p>【培训班-选课规则】培训班名称支持两行</p>
                    <p class="f-f13 f-c9 f-ml5">人社行业 / 2020年 / 公需科目</p>
                  </template>
                </el-table-column>
                <el-table-column label="换班状态" min-width="150" align="center">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-tag type="success">换班成功</el-tag>
                      <p class="f-mt5"><a href="#" class="f-link f-underline f-cb f-f12">查看详情</a></p>
                    </div>
                    <div v-else-if="scope.$index === 1">
                      <el-tag type="success">换班成功</el-tag>
                      <p class="f-mt5"><a href="#" class="f-link f-underline f-cb f-f12">查看详情</a></p>
                    </div>
                    <div v-else>
                      <el-tag type="warning">换班中</el-tag>
                      <p class="f-mt5"><a href="#" class="f-link f-underline f-cb f-f12">查看详情</a></p>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="换班时间" min-width="180">
                  <template>2020-11-11 12:20:20</template>
                </el-table-column>
                <el-table-column label="操作帐号" min-width="120">
                  <template>inlin001</template>
                </el-table-column>
                <el-table-column label="操作" min-width="100" align="center" fixed="right">
                  <template>
                    <el-button type="text" size="mini">继续换班</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!--分页-->
              <el-pagination
                background
                class="f-mt15 f-tr"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane label="订单信息" name="fourth">
        <div class="f-p15">详见 2604_客服管理_业务咨询_订单信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="发票信息" name="five">
        <div class="f-p15">详见 2605_客服管理_业务咨询_发票信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="退款信息" name="six">
        <div class="f-p15">详见 2606_客服管理_业务咨询_退款信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="学习档案" name="seven">
        <div class="f-p15">详见 2607_客服管理_业务咨询_培训档案.vue</div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames5: ['1'],
        activeName: 'third',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
