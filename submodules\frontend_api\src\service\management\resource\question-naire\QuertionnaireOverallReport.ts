import QuetionReport from '@api/service/common/question-naire/QuetionReport'
import { Response } from '@hbfe/common'
import DiffQuestionnaireQueryBackStage, {
  QuestionnaireAnswerAllTeacherResponse,
  QuestionnaireAnswerStaitsticsRequest
} from '@api/platform-gateway/diff-exam-query-front-gateway-QuestionnaireQueryBackStage'
import PlatformExamQueryBackStage, {
  BaseQuestionResponse,
  MultipleQuestionResponse,
  RadioQuestionResponse,
  ScaleQuestionResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import Question from '@api/service/common/question-naire/Question'
import DiffBackstage, {
  AnswerPaperAnswerQuestionRequest,
  AnswerQuestionPdfRequest
} from '@api/platform-gateway/diff-data-export-gateway-backstage'
import PlatSchemeLearningQueryForestage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
/**
 * 问卷整体报告
 */
export default class QuestionnaireOverallReport {
  /**
   * 调查问卷id
   */
  id = ''
  /**
   * 问卷模板id
   */
  quertionnaireTemplateId = ''
  /**
   * 方案id
   */
  schemeId = ''
  /**
   * 试题报告列表
   */
  quetionsReportList = new Array<QuetionReport>()
  /**
   * 问卷名称
   */
  queationnaireName = ''
  /**
   * 参与且提交问卷总人数
   */
  totalNum = 0

  /**
   * 获取整体报告
   */
  async doQuery(issueId?: string) {
    const request = new QuestionnaireAnswerStaitsticsRequest()
    request.questionnaireId = this.id
    const res = await DiffQuestionnaireQueryBackStage.getQuestionnaireAnswerStaitsticsInServicer(request)
    if (res.status.isSuccess() && res.data) {
      if (res.data.surveyInformationResponse) {
        this.queationnaireName = res.data.surveyInformationResponse.questionnaireName
        this.id = res.data.surveyInformationResponse.questionnaireId
        this.quertionnaireTemplateId = res.data.surveyInformationResponse.templateId
        this.schemeId = res.data.surveyInformationResponse.schemeId
      }
      this.totalNum = res.data.totalAnswerNum
      let teacherRes = new Response<QuestionnaireAnswerAllTeacherResponse>()
      if (issueId) {
        // teacherRes = await PlatSchemeLearningQueryForestage.getTeacherListInServicer(issueId)
        teacherRes = await DiffQuestionnaireQueryBackStage.getCourseTeachersWithPeriodAndOnlineInServicer({
          issueId
        })
      } else {
        // 如果期别和方案冲突 就优先调期别的口
        teacherRes = await DiffQuestionnaireQueryBackStage.getCourseTeachersWithPeriodAndOnlineInServicer({
          schemeId: this.schemeId
        })
      }
      this.quetionsReportList = res.data.answerStaitsticsList?.map((item) => {
        const temp = QuetionReport.from(item, teacherRes.data?.offlineTeachers, teacherRes.data?.onlineTeachers)
        return temp
      })

      const questionIdList = this.quetionsReportList.map((item) => item.id)
      if (!questionIdList.length) {
        this.quetionsReportList = []
        return
      }
      const currentPageData = new Array<BaseQuestionResponse>()
      // 获取试题 防止分页口超过200条
      const fetchQuestionList = async (ids: string[]) => {
        const questionRes = await PlatformExamQueryBackStage.pageQuestionInServicer({
          page: {
            pageNo: 1,
            pageSize: ids.length
          },
          request: {
            queryScope: 2,
            questionIdList: ids
          }
        })
        if (questionRes.status.isSuccess() && questionRes.data.currentPageData?.length) {
          currentPageData.push(...questionRes.data.currentPageData)
        }
      }
      for (let i = 0; i < questionIdList.length; i += 200) {
        const chunk = questionIdList.slice(i, i + 200)
        await fetchQuestionList(chunk)
      }
      if (currentPageData?.length) {
        const newArray = currentPageData.map((item) => {
          const temp = Question.fromQuestionReport(
            item as RadioQuestionResponse | MultipleQuestionResponse | ScaleQuestionResponse
          )
          return temp
        })
        const map = new Map(newArray.map((item) => [item.id, item]))
        const mergedArray = this.quetionsReportList.map((item1) => {
          // 教师题会get不到
          const temp = map.get(item1.id) || item1

          if (map.get(item1.id)) {
            temp.submitNum = item1.submitNum
            temp.submitProportion = Number((item1.submitNum / this.totalNum).toFixed(4))
            temp.scaleStatisc = temp.scaleStatisc.map((ite) => {
              ite.selectNum = 0
              ite.selectProportion = 0
              item1.scaleStatisc.map((it) => {
                if (it.levelNum == ite.levelNum) {
                  ite.selectNum = it.selectNum
                  ite.selectProportion = it.selectProportion
                }
              })
              return ite
            })
            // temp.optionsStatisc = item1.optionsStatisc
          }
          console.log(item1.isTeacherEvaluate, 'item')

          if (!item1.isTeacherEvaluate) {
            temp.optionsStatisc = temp.optionsStatisc.map((ite) => {
              ite.selectNum = 0
              ite.selectProportion = 0
              item1.optionsStatisc.map((it) => {
                if (it.id == ite.id) {
                  ite.selectNum = it.selectNum
                  ite.selectProportion = it.selectProportion
                }
              })

              return ite
            })
          } else {
            temp.optionsStatisc = item1.optionsStatisc
          }
          return temp
        })
        this.quetionsReportList = mergedArray?.filter((item) => item != undefined)
        console.log(this.quetionsReportList, 'this.quetionsReportList')
      }
    }
    return res.status
  }

  /**
   * 下载试题选项内容
   */
  async exportQuestionnaireReportOptions() {
    const request = new AnswerPaperAnswerQuestionRequest()
    request.questionnaireId = this.id
    request.jobName = '试题选项问答内容'
    const res = await DiffBackstage.exportAnswerPaperAnswerQuestionExcelInServicer(request)
    return res.data || false
  }
  /**
   * 下载问卷报告pdf
   */
  async exportQuestionnaireReport() {
    const request = new AnswerQuestionPdfRequest()
    request.questionnaireId = this.id
    request.jobName = '问卷整体报告'
    const res = await DiffBackstage.exportAnswerQuestionPdfInServicer(request)
    return res.data || false
  }
}
