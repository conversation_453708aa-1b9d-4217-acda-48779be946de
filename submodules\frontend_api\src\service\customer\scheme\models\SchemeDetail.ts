import MsCommodity, { ValidateCommodityRequest } from '@api/ms-gateway/ms-commodity-v1'
import MsLearningScheme from '@api/ms-gateway/ms-learningscheme-v1'
import MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage, {
  LearningRegisterRequest,
  StudentSchemeLearningRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import MsTradeQueryFrontGatewayTradeQueryForestage, {
  CommoditySkuForestageResponse,
  OrderRequest,
  SchemeResourceResponse,
  UserPossessionInfoResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import DateScope from '@api/service/common/models/DateScope'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import { PurchaseChannelTypeEnum } from '@api/service/customer/scheme/enums/PurchaseChannelType'
import { TerminalCodeEnum } from '@api/service/customer/scheme/enums/TerminalCode'
import { TrainingSchemeTypeEnum } from '@api/service/customer/scheme/enums/TrainingSchemeType'
import ClassOutline, { ChildOutlineResp } from '@api/service/customer/scheme/models/ClassOutline'
import CourseLearningRequire from '@api/service/customer/scheme/models/CourseLearningRequire'
import CourseQuizRequire from '@api/service/customer/scheme/models/CourseQuizRequire'
import ExamRequire from '@api/service/customer/scheme/models/ExamRequire'
import SchemeSkuProperty from '@api/service/customer/scheme/models/SchemeSkuProperty'
import ClassOutlineUtil from '@api/service/customer/scheme/utils/ClassOutlineUtil'
import SchemeUtil from '@api/service/customer/scheme/utils/SchemeUtil'
import { Page, Response, ResponseStatus } from '@hbfe/common'
/**
 * @description 培训方案详情
 */
class SchemeDetail {
  /**
   * 培训方案id
   */
  schemeId = ''
  /**
   * 培训班商品id
   */
  commoditySkuId = ''
  /**
   * 是否已报名（仅限登录后）
   * true —— 已报名、按钮显示“立即学习”
   * false —— 未报名、按钮显示“立即报名”
   */
  isSignedUp: boolean = null
  /**
   * 方案封面图片
   */
  schemeCoverImgUrl = ''
  /**
   * 方案Sku属性
   */
  schemeSkuProperty: SchemeSkuProperty = new SchemeSkuProperty()
  /**
   * 方案名称
   */
  schemeName = ''
  /**
   * 方案学时
   */
  schemePeriod: number = null
  /**
   * 方案价格
   */
  schemePrice: number = null
  /**
   * 注册时间段
   */
  signingUpTime: DateScope = new DateScope()
  /**
   * 培训时间段
   */
  trainingTime: DateScope = new DateScope()
  /**
   * 培训方案类型
   */
  schemeType: TrainingSchemeTypeEnum = null
  /**
   * 课程学习配置
   */
  courseLearningRequire: CourseLearningRequire = new CourseLearningRequire()
  /**
   * 课后测验配置
   */
  courseQuizRequire: CourseQuizRequire = new CourseQuizRequire()
  /**
   * 考试配置
   */
  examRequire: ExamRequire = new ExamRequire()
  /**
   * 课程大纲
   * @description 选课模式为自主选课时，只有一个一级节点且无子节点表示无分类
   */
  classOutline: ClassOutline[] = []
  /**
   * 是否是无分类，仅选课模式为自主选课时有效
   * true-无分类
   * false-有分类
   */
  outlineHasChildren: boolean = null
  /**
   * 获取参训资格id时要用
   */
  possessionInfo: UserPossessionInfoResponse = new UserPossessionInfoResponse()

  /**
   * 完整的课程大纲树
   * @private
   */
  private _courseTrainingOutlines: ChildOutlineResp[] = []

  /**
   * 方案工具类
   * @private
   */
  private _schemeUtil = new SchemeUtil()

  /**
   * 大纲工具类
   * @private
   */
  private _classOutlineUtil = new ClassOutlineUtil()

  static from(response: CommoditySkuForestageResponse) {
    const detail = new SchemeDetail()
    detail.schemeId = (response.resource as SchemeResourceResponse)?.schemeId
    detail.commoditySkuId = response.commoditySkuId
    detail.isSignedUp = response.possessionInfo?.possessing
    detail.schemeName = response.commodityBasicData?.saleTitle
    detail.schemeCoverImgUrl = response.commodityBasicData?.commodityPicturePath
    detail.schemeSkuProperty = SchemeSkuProperty.from(response.skuProperty)
    detail.schemePeriod = (response.resource as SchemeResourceResponse)?.period
    detail.schemePrice = response.commodityBasicData?.price
    detail.possessionInfo = response.possessionInfo || new UserPossessionInfoResponse()
    return detail
  }

  /**
   * 查询培训方案详情
   * @param commoditySkuId 培训班商品id
   */
  async querySchemeDetail(commoditySkuId: string): Promise<void> {
    const commodityResp = await MsTradeQueryFrontGatewayTradeQueryForestage.getCommoditySkuCustomerPurchaseInServicer({
      commoditySkuId: commoditySkuId
    })
    console.log('###commodityResp', commodityResp)
    const response = commodityResp.data
    this.schemeId = (response.resource as SchemeResourceResponse)?.schemeId
    this.commoditySkuId = response.commoditySkuId
    this.isSignedUp = response.possessionInfo?.possessing
    this.schemeName = response.commodityBasicData?.saleTitle
    this.schemeCoverImgUrl = response.commodityBasicData?.commodityPicturePath
    this.schemeSkuProperty = SchemeSkuProperty.from(response.skuProperty)
    this.schemePeriod = (response.resource as SchemeResourceResponse)?.period
    this.schemePrice = response.commodityBasicData?.price
    this.possessionInfo = response.possessionInfo || new UserPossessionInfoResponse()
    /** 获取方案配置信息 */
    if (this.schemeId) {
      const jsonConfigMap = await this._schemeUtil.batchQuerySchemeConfig([this.schemeId])
      const jsonConfig = jsonConfigMap.get(this.schemeId)
      console.log(`schemeId：${this.schemeId} - jsonConfig`, jsonConfig)
      this.fillSchemeWithJsonConfig(jsonConfig)
    }
    console.log(`schemeId：${this.schemeId} - schemeDetail`, this)
  }

  /**
   * 获取参训资格id
   * H5-获取学号
   * @description “去学习”前置查询接口
   */
  async doQueryQualificationId(): Promise<{ qualificationId: string; studentNo: string }> {
    let qualificationId = ''
    let studentNo = ''
    // 这一步是为了保证方案信息是最新的
    const schemeDetail = new SchemeDetail()
    await schemeDetail.querySchemeDetail(this.commoditySkuId)
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.sourceType = schemeDetail.possessionInfo.sourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
    request.learningRegister.sourceId = schemeDetail.possessionInfo.sourceId
    const response = await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage.pageSchemeLearningInMyself({
      page: new Page(1, 1),
      request
    })
    if (response.status?.isSuccess() && response.data?.currentPageData && response.data?.currentPageData?.length) {
      qualificationId = response.data.currentPageData[0].qualificationId
      studentNo = response.data.currentPageData[0].studentNo
    }
    return { qualificationId, studentNo }
  }

  /**
   * 校验是否能够发起报名
   * @param terminalCode 终端统一编码，默认Web端
   * @param channelType 购买渠道类型，默认用户自主购买
   * @description
   * code状态码说明：
   * 50001 —— 不可重复报名同一个班级
   * 30001 —— 商品不存在
   * 30002 —— 商品已下架
   * 30003 —— 不支持当前渠道购买该商品
   * 30004 —— 当前渠道已关闭
   * 50002 —— 培训未开始
   * 50003 —— 培训已结束
   * 50004 —— 报名未开始
   * 50005 —— 报名已结束
   * 50006 —— 当前用户此班级正在开班中（判断当前用户此班级是否开班完成）
   * 50007 —— 当前用户此班级正在退班中（判断当前用户正在退班）
   * 52001 —— 存在未支付的个人缴费订单，此时提供orderNo
   * 52009 —— 存在未支付的集体报名订单，此时提供orderNo
   * 500 —— 其他未定义异常
   */
  async doValidateEnableApplySignUp(
    terminalCode: TerminalCodeEnum = TerminalCodeEnum.WEB,
    channelType: PurchaseChannelTypeEnum = PurchaseChannelTypeEnum.SELF_PURCHASE
  ): Promise<Response<{ orderNo: string }>> {
    const result = new Response<{ orderNo: string }>()
    // 返回值code默认200
    result.status = new ResponseStatus(200)
    const request = new ValidateCommodityRequest()
    request.commoditySkuId = this.commoditySkuId
    request.channelType = channelType
    request.terminalCode = terminalCode
    // 1-请求接口校验商品是否可以购买
    const response = await MsCommodity.validateCommodity(request)
    if (response.status?.isSuccess()) {
      if (response.data?.code !== '200') {
        // 2-如果商品不可以购买，赋值3000x状态，
        result.status.code = parseInt(response.data?.code)
        result.status.message = response.data.message
      } else {
        // 3-商品可以购买再请求接口校验方案是否能够预约，赋值5xxxx状态
        const schemeRes = await MsLearningScheme.reservingSchemeValidate({
          schemeId: this.schemeId
        })
        if (schemeRes.status?.isSuccess()) {
          result.status.code = parseInt(schemeRes.data.code)
          result.status.message = schemeRes.data.message
        } else {
          result.status = schemeRes.status
        }
        if (result.status.code === 50001) {
          // 4-如果发现重复报名、获取订单orderNo
          const subOrderNo = schemeRes.data?.duplicateReservingInfos[0]?.sourceId
          const orderRequest = new OrderRequest()
          orderRequest.subOrderNoList = [subOrderNo]
          const orderRes = await MsTradeQueryFrontGatewayTradeQueryForestage.pageOrderInMyself({
            page: new Page(1, 1),
            request: orderRequest
          })
          if (orderRes.status?.isSuccess()) {
            const orderInfo = orderRes.data?.currentPageData[0]
            if ([0, 1].includes(orderInfo.basicData?.orderPaymentStatus)) {
              result.data = {
                orderNo: orderInfo.orderNo
              }
              // 集体缴费
              if (orderInfo.basicData.channelType === PurchaseChannelTypeEnum.COLLECTIVE_PURCHASE) {
                result.status.code = 52009
              }
              // 个人订单
              if (orderInfo.basicData.channelType === PurchaseChannelTypeEnum.SELF_PURCHASE) {
                result.status.code = 52001
              }
            }
          }
        }
      }
    }
    return result
  }

  /**
   * 用json配置填充方案信息
   * @param jsonConfig
   */
  fillSchemeWithJsonConfig(jsonConfig: any): void {
    const creditLearningResult = jsonConfig.assessSetting?.learningResults?.find(
      (learnResult: any) => learnResult.type == 1
    )
    this.schemePeriod = creditLearningResult.grade || 0
    this.signingUpTime.begin = jsonConfig.registerBeginDate
    this.signingUpTime.end = jsonConfig.registerEndDate
    this.trainingTime.begin = jsonConfig.trainingBeginDate
    this.trainingTime.end = jsonConfig.trainingEndDate
    this.schemeType = jsonConfig.type as TrainingSchemeTypeEnum
    let courseAssess // 课程学习要求
    let courseQuizEva // 课程测验考核配置
    let courseQuizConfig // 课程测验配置
    // 获取课程学习要求
    if (this.schemeType == TrainingSchemeTypeEnum.CHOOSE_COURSE_LEARNING && jsonConfig.chooseCourseLearning) {
      this.courseLearningRequire.isConfigured = true
      courseAssess = jsonConfig.chooseCourseLearning.assessSetting
      courseQuizEva = jsonConfig.chooseCourseLearning.config?.courseCompleteEvaluateConfig
      courseQuizConfig = jsonConfig.chooseCourseLearning.config?.courseQuizConfig
      if (courseAssess) {
        this.courseLearningRequire.compulsoryRequirePeriod = courseAssess.compulsoryRequirePeriod
        this.courseLearningRequire.electiveRequirePeriod = courseAssess.electiveRequirePeriod
        this.courseLearningRequire.totalRequirePeriod = CalculatorObj.add(
          this.courseLearningRequire.compulsoryRequirePeriod,
          this.courseLearningRequire.electiveRequirePeriod
        )
      }
    } else if (
      this.schemeType == TrainingSchemeTypeEnum.AUTONOMOUS_COURSE_LEARNING &&
      jsonConfig.autonomousCourseLearning
    ) {
      this.courseLearningRequire.isConfigured = true
      courseAssess = jsonConfig.autonomousCourseLearning.assessSetting
      courseQuizEva = jsonConfig.autonomousCourseLearning.config?.courseCompleteEvaluateConfig
      courseQuizConfig = jsonConfig.autonomousCourseLearning.config?.courseQuizConfig
      if (courseAssess) {
        this.courseLearningRequire.totalRequirePeriod = courseAssess.requirePeriod
      }
    }
    // 获取课程测验配置
    if (courseQuizEva && courseQuizConfig) {
      this.courseQuizRequire.isConfigured = true
      this.courseQuizRequire.incorporateCourseQuiz = courseQuizEva.courseQuizPagerStandard
      this.courseQuizRequire.eachCourseQuizPassScore = courseQuizConfig.quizConfig?.passScore
    }
    // 获取课程考试要求
    if (jsonConfig.examLearning) {
      this.examRequire.isConfigured = true
      this.examRequire.passScore = jsonConfig.examLearning.config?.qualifiedScore
    }
    this.getClassOutline(jsonConfig)
  }

  /**
   * 获取课程大纲
   */
  private getClassOutline(jsonConfig: any) {
    if (!this.schemeType) return
    this._courseTrainingOutlines =
      (jsonConfig[this.schemeType]?.config?.courseTrainingOutlines as ChildOutlineResp[]) || ([] as ChildOutlineResp[])
    if (this.schemeType === TrainingSchemeTypeEnum.AUTONOMOUS_COURSE_LEARNING) {
      // 选课模式为自主选课时，判断是否是无分类
      this.outlineHasChildren = true
      if (this._courseTrainingOutlines.length && !this._courseTrainingOutlines[0].childOutlines?.length) {
        this.outlineHasChildren = false
      }
    }
    // 1-重塑树
    const reShapeTree = this._classOutlineUtil.reShapeTree(this._courseTrainingOutlines, this.schemeId)
    // 2-获取选修课的第2层
    this.classOutline = this._classOutlineUtil.filterTreeByCondition(
      reShapeTree,
      node => node.category == 2 && node.level == 1
    )
    // 3-插入“全部”选项
    this._classOutlineUtil.insertOptionToTree(this.classOutline)
  }
}

export default SchemeDetail
