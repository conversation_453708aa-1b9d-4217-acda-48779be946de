import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 是否是专技岗位工作
 */
export enum DictionaryTypeEnum {
  GSZJ_STAFFING_STATUS = 'GSZJ_STAFFING_STATUS',
  GSZJ_TITLE_EFFECTIVE_RANGE = 'GSZJ_TITLE_EFFECTIVE_RANGE',
  GSZJ_TITLE_GRADE = 'GSZJ_TITLE_GRADE',
  GSZJ_TITLE_SERIES = 'GSZJ_TITLE_SERIES',
  GSZJ_UNIT_NATURE = 'GSZJ_UNIT_NATURE'
}

class DictionaryTypeTypes extends AbstractEnum<DictionaryTypeEnum> {
  static enum = DictionaryTypeEnum

  constructor(status?: DictionaryTypeEnum) {
    super()
    this.current = status
    this.map.set(DictionaryTypeEnum.GSZJ_STAFFING_STATUS, '在编情况')
    this.map.set(DictionaryTypeEnum.GSZJ_TITLE_EFFECTIVE_RANGE, '现有职称有效范围')
    this.map.set(DictionaryTypeEnum.GSZJ_TITLE_GRADE, '现有职称等级')
    this.map.set(DictionaryTypeEnum.GSZJ_TITLE_SERIES, '职称系列')
    this.map.set(DictionaryTypeEnum.GSZJ_UNIT_NATURE, '所在单位性质')
  }
}

export default new DictionaryTypeTypes()
