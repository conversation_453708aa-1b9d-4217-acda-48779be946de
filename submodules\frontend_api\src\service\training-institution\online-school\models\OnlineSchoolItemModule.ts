import {
  ClientTypesEnum,
  OnlineSchoolEnum,
  SchoolStatusEnum,
  ServicePeriodEnum
} from '@api/service/training-institution/online-school/enum/OnlineSchoolEnum'
import IndustryEnum, { IndustryIdEnum } from '@api/service/training-institution/online-school/enum/IndustryEnum'
import { OnlineSchoolInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

export default class OnlineSchoolItemModule {
  /**
   * 合约id
   */
  id: string = undefined

  /**
   * 网校id
   */
  schoolId: string = undefined

  /**
   * 网校名称
   */
  schoolName: string = undefined

  /**
   * 网校性质
   */
  schoolModel: OnlineSchoolEnum = undefined

  /**
   * 域名
   */
  domainName: string = undefined

  /**
   * h5域名
   */
  H5DomainName: string = undefined

  /**
   * 业主名称
   */
  ownerUnitName: string = undefined

  /**
   * 行业id
   */
  industryIds: Array<string> = new Array<string>()

  /**
   * 行业名称
   */
  industryNames: Array<string> = new Array<string>()

  /**
   * 服务地区code
   */
  serviceRegionCodes: Array<string> = new Array<string>()

  /**
   * 服务地区名称
   */
  serviceRegionNames: Array<string> = new Array<string>()

  /**
   * 是否提供web端
   */
  providerWebService: boolean = undefined

  /**
   * 是否提供H5端
   */
  providerH5Service: boolean = undefined

  /**
   * 服务期限
   */
  servicePeriodModel: ServicePeriodEnum = undefined

  /**
   * 到期时间
   */
  serviceOverTime: string = undefined

  /**
   * 网校状态
   */
  schoolStatus: SchoolStatusEnum = undefined

  /**
   * 开通时间
   */
  openTime: string = undefined

  /**
   * 模型转化
   * @param dto 后端模型
   * @param regionMap 地区code名称映射
   */
  static from(dto: OnlineSchoolInfoResponse, regionMap: Map<string, string>): OnlineSchoolItemModule {
    const vo = new OnlineSchoolItemModule()
    vo.id = dto?.basicInfo?.contractId
    vo.schoolId = dto?.basicInfo?.onlineSchoolId
    vo.schoolName = dto?.basicInfo?.name
    vo.schoolModel = dto?.basicInfo?.onlineSchoolModes
    dto?.configInfo?.clients?.length &&
      dto.configInfo.clients.map(item => {
        if (item.clientType === ClientTypesEnum.WEB) {
          vo.providerWebService = true
          vo.domainName = item.domainName
        }
        if (item.clientType === ClientTypesEnum.H5) {
          vo.providerH5Service = true
          vo.H5DomainName = item.domainName
        }
      })
    vo.ownerUnitName = dto?.basicInfo?.unitName
    dto?.basicInfo?.trainingProperties?.industries?.length &&
      dto.basicInfo.trainingProperties.industries.map(item => {
        vo.industryIds.push(item.id)
        vo.industryNames.push(new IndustryEnum(item.id as IndustryIdEnum).toString() as string)
      })
    vo.serviceRegionCodes = dto?.basicInfo?.serviceAreas
    vo.serviceRegionCodes?.map(code => {
      const name = regionMap?.get(code)
      if (name) {
        vo.serviceRegionNames.push(name)
      }
    })
    vo.servicePeriodModel = dto?.configInfo?.trainingPeriodModes
    vo.serviceOverTime = dto?.configInfo?.expireDate
    if (dto?.basicInfo?.status === 1) {
      vo.schoolStatus = SchoolStatusEnum.OPENING
    } else if (dto?.basicInfo?.status === 2 && dto?.basicInfo?.isExpired) {
      vo.schoolStatus = SchoolStatusEnum.OVER
    } else if (dto?.basicInfo?.status === 2 && dto?.basicInfo?.isEnable && !dto?.basicInfo?.isExpired) {
      vo.schoolStatus = SchoolStatusEnum.OPERATE
    } else if (dto?.basicInfo?.status === 2 && !dto?.basicInfo?.isEnable && !dto?.basicInfo?.isExpired) {
      vo.schoolStatus = SchoolStatusEnum.DISABLE
    }
    vo.openTime = dto?.basicInfo?.OSOpenTime
    return vo
  }
}
