<route-meta>
{
"isMenu": true,
"title": "学员学习明细",
"sort": 5,
"icon": "icon-mingxi"
}
</route-meta>
<template>
  <div>
    <LearningStatisticDiff ref="learningStatistic" v-bind="$attrs" v-on="$listeners">
      <template #disSynchronized>
        <el-option :value="syncResultEnmu.DisableSynchronized" label="不同步"></el-option>
      </template>
      <template #export-docking-data>
        <el-button
          v-if="$hasPermission('exportStatementsData')"
          query
          desc="导出报盘数据"
          actions="doExportStatementsData"
          @click="doExportStatementsData"
          >导出报盘数据</el-button
        >
      </template>
    </LearningStatisticDiff>
    <el-dialog title="提示" :visible.sync="exportDataSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务查看-培训成果报盘数据</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportDataSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadDataPage">前往下载</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import LearningStatistic from '@hbfe/jxjy-admin-learningStatistic/src/index.vue'
  import UserModule from '@api/service/management/user/UserModule'
  import ExportStatementsData from '@api/service/diff/management/byzj/learning-statistic/ExportStatementsData'
  import { SyncResultEnmu } from '@api/service/diff/management/byzj/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
  import OnlineClassTable from '@hbfe/jxjy-admin-learningStatistic/src/diff/byzj/__components__/online-class-table.vue'
  import QueryStudentLearningList from '@api/service/diff/management/byzj/statisticalReport/query/QueryStudentLearningList'
  import QueryStudentLearningManagerRegionListDiff from '@api/service/diff/management/byzj/statisticalReport/query/QueryStudentLearningManagerRegionList'
  import StaticticalReportManagerModule from '@api/service/management/statisticalReport/StaticticalReportManagerModule'
  @Component({ components: { OnlineClassTable } })
  class LearningStatisticDiff extends LearningStatistic {
    queryStudentLearningList = new QueryStudentLearningList()
    queryStudentLearningManagerRegionList = new QueryStudentLearningManagerRegionListDiff()
    /**
     * 地区管理员导出
     */
    async exportExcelRegion() {
      return await this.queryStudentLearningManagerRegionList.exportExcel(this.filter)
    }
    /**
     * 分销管理员导出
     */
    async exportStudentSchemeLearningExcelInSDistributor() {
      return await this.queryStudentLearningList.exportStudentSchemeLearningExcelInSDistributor(this.filter)
    }
    /**
     * 专题管理员导出
     */
    async exportExcelTrainingChannel() {
      return await this.queryStudentLearningList.exportExcelTrainingChannel(this.filter)
    }
    /**
     * 超级管理员导出
     */
    async exportExcel() {
      return await this.queryStudentLearningList.exportExcel(this.filter)
    }
    /**
     * 地区管理员查询
     */
    async listRegionLearningReportFormsInServicerRegion() {
      return await this.queryStudentLearningManagerRegionList.listRegionLearningReportFormsInServicer(
        this.page,
        this.filter
      )
    }
    /**
     * 查询列表——分销商
     */
    async pageStudentSchemeLearningInDistributor() {
      return await this.queryStudentLearningList.pageStudentSchemeLearningInDistributor(this.page, this.filter)
    }

    /**
     * 学员学习统计列表--专题管理员
     */
    async listRegionLearningReportFormsInTrainingChannel() {
      return await this.queryStudentLearningList.listRegionLearningReportFormsInTrainingChannel(this.page, this.filter)
    }

    /**
     * 学员学习统计列表
     */
    async listRegionLearningReportFormsInServicer() {
      return await this.queryStudentLearningList.listRegionLearningReportFormsInServicer(this.page, this.filter)
    }
  }
  @Component({ components: { LearningStatisticDiff } })
  export default class extends Vue {
    @Ref('learningStatistic') learningStatistic: LearningStatistic

    statementsData: ExportStatementsData = new ExportStatementsData()

    // 对接数据导出弹窗
    exportDataSuccessVisible = false
    /**
     * 重写同步枚举类
     */
    syncResultEnmu = SyncResultEnmu
    /**
     * 导出对接数据
     */
    async doExportStatementsData() {
      this.$confirm('导出后成果同步状态将标记为“已同步”，是否确认导出报盘数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(async () => {
          this.learningStatistic.getLocalSkuProperty()
          const res = await this.statementsData.exportStatementsDiff(this.learningStatistic.filter)
          if (res.status.code == 200 && res.data) {
            this.exportDataSuccessVisible = true
          } else {
            this.$message.warning('导出失败')
          }
        })
        .catch(() => {
          // do nothing
        })
    }

    /**
     * 导出对接数据任务下载
     */
    goDownloadDataPage() {
      this.exportDataSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportStudentLearningQuotation' }
      })
    }
  }
</script>
