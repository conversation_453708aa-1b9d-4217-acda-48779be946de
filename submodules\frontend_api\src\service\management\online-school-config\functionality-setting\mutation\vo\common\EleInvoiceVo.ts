import { InvoiceProviderEnum } from '@api/service/common/enums/online-school-config/InvoiceProviderTypes'
/*
  增值税发票配置 基类
*/
class EleInvoiceVo {
  /**
   * 纳税人名称
   */
  name?: string = ''
  /**
   * 纳税人识别号
   */
  taxpayerNo?: string = ''
  /**
   * 地址
   */
  address?: string = ''
  /**
   * 电话
   */
  phone?: string = ''
  /**
   * 开户行
   */
  bankName?: string = ''
  /**
   * 开户账号
   */
  bankAccount?: string = ''
  /**
   * 最大开票金额
   */
  invoiceMaxMoney?: number = 0
  /**
   * 收款人
   */
  payee?: string = ''
  /**
   * 开票人
   */
  issuer?: string = ''
  /**
   * 复核人
   */
  reviewer?: string = ''

  /*开票平台授权信息开始 【start】*/
  /*
    开票提供商编号, 5 当前只支持诺诺配置
  */
  invoiceProviderId: string = InvoiceProviderEnum.NUONUOV2
  /**
   * 授权码/企业私钥
   */
  secretAssessKey?: string = ''
  /**
   * 应用访问标识/平台公钥
   */
  accessKey?: string = ''
  /**
   * 部门ID
   */
  deptId?: string = ''

  /*开票平台授权信息结束 【end】*/

  /* 商品票面配置 【start】 */
  /**
   * 商品税务编码
   */
  commodityCode = ''
  /**
   * 服务名称
   */
  serviceTitle?: string = ''
  /**
   * 单位
   */
  unitTitle?: string = ''

  /**
   * 规格型号
   */
  specificationMode?: string = ''
  /**
   * 规格型号
   */
  taxFavoured?: number = null
  /**
   * 是否打印数量
   */
  printQuantity?: boolean = null
  /**
   * 是否打印单价
   */
  printPrice?: boolean = null
  /**
   * 税率
   */
  rate?: number = null
  /* 商品票面配置 【end】 */
  /**
   * 备注
   */
  remark?: string = ''
  /**
   * 企业代码
   */
  enterpriseCode? = ''
  /**
   * 版税人身份证号
   */
  taxIdCard? = ''

  /**
   * 登录账号
   电子税局登录账号
   */
  taxLoginAccount: string
  /**
   * 登录密码
   电子税局登录密码
   */
  taxLoginPassword: string

  /**
   * 分机号（诺税通使用）
   */
  extensionNumber = ''
  toDto() {
    // 子类实现该方法
    return
  }
}
export default EleInvoiceVo
