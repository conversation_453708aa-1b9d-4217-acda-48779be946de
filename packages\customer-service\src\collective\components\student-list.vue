<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <el-row :gutter="16" class="m-query">
        <el-form :inline="true" label-width="auto">
          <el-col :sm="12" :md="8" :lg="6">
            <el-form-item label="姓名">
              <el-input v-model="studentListParams.userName" clearable placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="8" :lg="6">
            <el-form-item label="证件号">
              <el-input v-model="studentListParams.idCard" clearable placeholder="请输入证件号" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="8" :lg="6">
            <el-form-item label="手机号">
              <el-input v-model="studentListParams.phone" clearable placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="8" :lg="6" class="f-fr">
            <el-form-item class="f-tr">
              <el-button type="primary" @click="clickSearch">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <!--表格-->
      <el-table
        stripe
        :data="studentTableData"
        v-loading="uiStatus.query.loadStudentPage"
        max-height="500px"
        class="m-table"
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left">
          <template slot-scope="scope"
            ><span
              v-observe-visibility="
                scope.$index === studentTableData.length - 1
                  ? (isVisible, entry) => {
                      lastOrderVisible(isVisible, scope.$index)
                    }
                  : null
              "
              >{{ scope.$index + 1 }}</span
            ></template
          >
        </el-table-column>
        <el-table-column label="学员姓名" min-width="140" fixed="left">
          <template slot-scope="scope">{{ scope.row.userName }}</template>
        </el-table-column>
        <el-table-column label="证件号" min-width="200">
          <template slot-scope="scope">{{ scope.row.idCard }}</template>
        </el-table-column>
        <el-table-column label="手机号" min-width="160">
          <template slot-scope="scope">{{ scope.row.phone }}</template>
        </el-table-column>
        <el-table-column label="单位地区" min-width="240">
          <template slot-scope="scope">{{ regionStr(scope.row.region) }}</template>
        </el-table-column>
        <el-table-column label="工作单位" min-width="300">
          <template slot-scope="scope">{{ scope.row.companyName }}</template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import UserModule from '@api/service/management/user/UserModule'
  import { UiPage } from '@hbfe/common'
  import StudentUserInfoVo from '@api/service/management/user/query/student/vo/StudentUserInfoVo'
  import StudentQueryByCollectionParamVo from '@api/service/management/user/query/student/vo/StudentQueryByCollectionParamVo'
  import { RegionModel } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  @Component
  export default class extends Vue {
    // 学员列表实例
    studentListObj = UserModule.queryUserFactory.queryStudentList
    input = ''
    studentTableData = new Array<StudentUserInfoVo>()
    page: UiPage
    studentListParams = new StudentQueryByCollectionParamVo()
    uiStatus = {
      query: {
        loadStudentPage: false
      }
    }

    constructor() {
      super()
      this.page = new UiPage(this.doQuery, this.doQuery)
    }
    @Prop({
      type: String,
      default: ''
    })
    userId: string

    @Watch('userId')
    async userIdChange(id: string) {
      if (id) {
        await this.clickSearch()
      }
    }

    async created() {
      await this.doQuery()
    }

    async clickSearch() {
      this.page.pageNo = 1
      await this.doQuery()
    }

    async doQuery() {
      if (!this.userId) {
        this.studentTableData = []
        return
      }
      try {
        this.uiStatus.query.loadStudentPage = true
        this.studentListParams.collectionUserId = this.userId
        this.studentTableData = await this.studentListObj.queryStudentListForCollectivity(
          this.page,
          this.studentListParams
        )
        console.info('🚀 ~ file:student-list method:doQuery line:129 -----', this.studentTableData)
      } catch (e) {
        console.log(e)
        this.$message.error('查询学员名单列表请求失败！')
      } finally {
        this.uiStatus.query.loadStudentPage = false
      }
    }

    async pollingQuery() {
      if (!this.userId) {
        this.studentTableData = []
        return
      }
      try {
        this.uiStatus.query.loadStudentPage = true
        this.page.pageNo++
        this.studentListParams.collectionUserId = this.userId
        const res = await this.studentListObj.queryStudentListForCollectivity(this.page, this.studentListParams)
        console.info('🚀 ~ file:student-list method:doQuery line:149 -----', this.studentTableData)
        this.studentTableData.push(...res)
      } catch (e) {
        console.log(e)
        this.$message.error('查询学员名单列表请求失败！')
      } finally {
        this.uiStatus.query.loadStudentPage = false
      }
    }

    async resetQuery() {
      this.studentListParams = new StudentQueryByCollectionParamVo()
      this.studentListParams.collectionUserId = this.userId
      await this.doQuery()
    }

    lastOrderVisible(isVisible: boolean, index: number) {
      if (isVisible && index === this.studentTableData.length - 1) {
        if (this.page.pageNo >= this.page.totalPageSize) {
          return
        }
        this.pollingQuery()
      }
    }

    get regionStr() {
      return (item: RegionModel) => {
        let regionPath = ''
        if (item && item.regionPath) {
          if (item.provinceId) regionPath = item.provinceName
          if (item.cityId) regionPath = regionPath + '-' + item.cityName
          if (item.countyId) regionPath = regionPath + '-' + item.countyName
        } else {
          regionPath = '-'
        }
        return regionPath
      }
    }
  }
</script>
