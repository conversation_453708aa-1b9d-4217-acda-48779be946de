import store from '@/store'
import MutationBasicdataDictionaryFactory from '@api/service/common/basic-data-dictionary/MutationBasicdataDictionaryFactory'
import QueryBasicdataDictionaryFactory from '@api/service/common/basic-data-dictionary/QueryBasicdataDictionaryFactory'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'

@Module({
  name: 'BasicDataDictionaryModule',
  dynamic: true,
  namespaced: true,
  store
})
class BasicDataDictionaryModule extends VuexModule {
  /**
   * 基础数据字典查询工厂
   * 业务地区
   * 行业
   * 行业属性
   * 职称等级
   * 物理地区
   * 科目类型
   * 培训类别
   * 培训专业
   *
   * 网校状态
   */
  queryBasicDataDictionaryFactory: QueryBasicdataDictionaryFactory = new QueryBasicdataDictionaryFactory()
  /**
   * 基础数据字典业务工厂
   * 培训属性：更新培训属性的展示名称
   */
  mutationBasicdataDictionaryFactory: MutationBasicdataDictionaryFactory = new MutationBasicdataDictionaryFactory()
}

export default getModule(BasicDataDictionaryModule)
