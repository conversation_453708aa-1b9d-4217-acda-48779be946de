import ConfigCenterGateway from '@api/gateway/ConfigCenter-default'
import queryGraphql from './query.graphql'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { UnAuthorize } from '@api/Secure'

interface ConfigType {
  key: string
  value: string
}

interface Config {
  ingress: Array<ConfigType>
  application: Array<ConfigType>
}

export interface IState {
  ingress: Map<string, string>
  application: Map<string, string>
}

@Module({
  dynamic: true,
  namespaced: true,
  store,
  name: 'CommonConfigCenterModule'
})
class ConfigCenterModule extends VuexModule implements IState {
  application = new Map<string, string>()
  ingress = new Map<string, string>()
  namespaces = new Map<string, Map<string, string | object | Array<object>>>()

  @UnAuthorize
  @Action
  async getNamespacesConfig() {
    const { data } = await ConfigCenterGateway._commonQuery<Config>(queryGraphql)
    this.SET_NAMESPACES_CONFIG(data)
  }

  @Mutation
  SET_NAMESPACES_CONFIG(config: Config) {
    config.ingress.forEach((ingress: ConfigType) => {
      this.ingress.set(ingress.key, ingress.value)
    })
    config.application.forEach((application: ConfigType) => {
      this.application.set(application.key, application.value)
    })
    const filterItems = Object.keys(config).filter(item => {
      return !['application', 'ingress'].includes(item)
    })
    filterItems.forEach(namespace => {
      const item = config[namespace] as Array<{ key: string; value: string }>
      const newMap = new Map<string, string | object>()
      item.forEach(valueItem => {
        let value = valueItem.value
        try {
          value = JSON.parse(value)
        } catch (e) {
          // nothing
        }
        newMap.set(valueItem.key, value)
      })
      this.namespaces.set(namespace, newMap)
    })
  }

  get getApplicationByName() {
    return (name: string) => {
      return this.application.get(name) || ''
    }
  }

  get getIngressByName() {
    return (name: string) => {
      return this.ingress.get(name) || ''
    }
  }

  get getValueByNamespace() {
    return (namespace: string, key: string): string | object | Array<object> => {
      const namespaceItem = this.namespaces.get(namespace)
      if (!namespace) {
        return ''
      }
      return namespaceItem.get(key)
    }
  }
}

export default getModule(ConfigCenterModule)
