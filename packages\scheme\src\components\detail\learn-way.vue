<template>
  <el-tabs v-model="activeName" type="card" class="m-tab-card is-sticky">
    <el-tab-pane name="first" v-if="schemeDetail.learningTypeModel.courseLearning.isSelected">
      <div slot="label">
        <i class="f-pl5">线上课程</i>
      </div>
      <course-learn
        :courseLearning.sync="schemeDetail.learningTypeModel.courseLearning"
        :isTowElectiveListShow="isTowElectiveListShow"
        :trainSchemeDetail.sync="schemeDetail"
      />
    </el-tab-pane>
    <el-tab-pane name="second" v-if="schemeDetail.learningTypeModel.exam.isSelected">
      <div slot="label">
        <i class="f-pl5">班级考试</i>
      </div>
      <exam :examLearning.sync="schemeDetail.learningTypeModel.exam" />
    </el-tab-pane>
    <el-tab-pane name="third" v-if="schemeDetail.learningTypeModel.practiceLearning.isSelected">
      <div slot="label">
        <i class="f-pl5">班级练习</i>
      </div>
      <practice :practiceLearning.sync="schemeDetail.learningTypeModel.practiceLearning" />
    </el-tab-pane>
    <el-tab-pane name="fourth" v-if="schemeDetail.learningTypeModel.interestCourse.isSelected">
      <div slot="label">
        <i class="f-pl5">兴趣课程</i>
      </div>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="f-plr20 f-pt20">
          <show-choose-course-outline
            :outline-info="schemeDetail.learningTypeModel.interestCourse.classification"
            :type="2"
            :trainSchemeDetail.sync="schemeDetail"
          />
        </div>
      </el-card>
    </el-tab-pane>
    <el-tab-pane name="fifth" v-if="schemeDetail.learningTypeModel.learningExperience.isSelected">
      <div slot="label">
        <i class="f-pl5">学习心得</i>
      </div>
      <study-notes :learningFeelInfo="schemeDetail.learningTypeModel.learningExperience" />
    </el-tab-pane>
    <el-tab-pane name="sixth" v-if="schemeDetail.learningTypeModel.issue.isSelected">
      <div slot="label">
        <i class="f-pl5">培训期别</i>
      </div>
      <training-period
        :issue="schemeDetail.learningTypeModel.issue"
        :trainClassBaseInfo="schemeDetail.trainClassBaseInfo"
        type="detail"
      ></training-period>
    </el-tab-pane>
    <el-tab-pane name="seventh" v-if="schemeDetail.learningTypeModel.questionnaire.isSelected">
      <div slot="label">
        <i class="f-pl5">调研问卷</i>
      </div>
      <questionnaire
        :questionnaire="schemeDetail.learningTypeModel.questionnaire"
        :issue="schemeDetail.learningTypeModel.issue"
        :trainClassBaseInfo="schemeDetail.trainClassBaseInfo"
        type="detail"
      ></questionnaire>
    </el-tab-pane>
  </el-tabs>
</template>

<script lang="ts">
  import { Component, PropSync, Vue, Watch } from 'vue-property-decorator'
  import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
  import CourseLearn from '@hbfe/jxjy-admin-scheme/src/components/detail/course-learn.vue'
  import ShowChooseCourseOutline from '@hbfe/jxjy-admin-scheme/src/components/functional-components/show-choose-course-outline.vue'
  import Practice from '@hbfe/jxjy-admin-scheme/src/components/detail/practice.vue'
  import Exam from '@hbfe/jxjy-admin-scheme/src/components/detail/exam.vue'
  import StudyNotes from '@hbfe/jxjy-admin-scheme/src/components/detail/study-notes.vue'
  import TrainingPeriod from '@hbfe/jxjy-admin-scheme/src/components/training-period.vue'
  import Questionnaire from '@hbfe/jxjy-admin-scheme/src/components/questionnaire.vue'
  @Component({
    components: { Questionnaire, TrainingPeriod, Exam, Practice, ShowChooseCourseOutline, CourseLearn, StudyNotes }
  })
  export default class extends Vue {
    /**
     * 活动活页
     */
    activeName = 'first'
    /**
     * 培训方案修改选修二级分类是否展示
     */
    isTowElectiveListShow = false
    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: Object }) schemeDetail: TrainClassDetailClassVo
    @Watch('schemeDetail', {
      immediate: true,
      deep: true
    })
    schemeDetailWatch() {
      this.activeName = this.calculateInitialActiveName()
    }
    @Watch('schemeDetail.learningTypeModel.courseLearning.chooseCourseRule.secondElectiveMaxPeriod', {
      immediate: true,
      deep: true
    })
    courseLearningChange(val: any) {
      this.isTowElectiveListShow = false
      if (val) {
        this.isTowElectiveListShow = true
      }
    }

    // 计算初始激活的标签页名称
    calculateInitialActiveName(): string {
      const tabNames = ['first', 'second', 'third', 'fourth', 'fifth', 'sixth', 'seventh']
      for (const tabName of tabNames) {
        if (this.isTabVisible(tabName)) {
          return tabName
        }
      }
      return 'first' // 如果没有可见的标签页，返回空字符串
    }

    // 检查标签页是否可见
    isTabVisible(tabName: string): boolean {
      switch (tabName) {
        case 'first':
          return this.schemeDetail.learningTypeModel.courseLearning.isSelected
        case 'second':
          return this.schemeDetail.learningTypeModel.exam.isSelected
        case 'third':
          return this.schemeDetail.learningTypeModel.practiceLearning.isSelected
        case 'fourth':
          return this.schemeDetail.learningTypeModel.interestCourse.isSelected
        case 'fifth':
          return this.schemeDetail.learningTypeModel.learningExperience.isSelected
        case 'sixth':
          return this.schemeDetail.learningTypeModel.issue.isSelected
        case 'seventh':
          return this.schemeDetail.learningTypeModel.questionnaire.isSelected
        default:
          return false
      }
    }
  }
</script>
