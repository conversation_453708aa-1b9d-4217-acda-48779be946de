<route-meta>
{ "isMenu": true, "title": "课程管理", "sort": 2, "icon": "icon-xuexi" }
</route-meta>
<script lang="ts">
  import Course from '@hbfe/jxjy-admin-course/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    query: [WXGLY],
    unassignedCourse: [WXGLY],
    create: [WXGLY],
    classDetail: [WXGLY],
    modify: [WXGLY],
    enable: [WXGLY],
    category: [WXGLY],
    preview: [WXGLY],
    remove: [WXGLY]
  })
  export default class extends Course {}
</script>
