<route-params content="/:schemeId"></route-params>
<template>
  <operation-fjzj
    ref="operationRef"
    @defaultSchemeTypeChange="defaultSchemeTypeChange"
    :defaultSchemeTypeDiff="defaultSchemeTypeDiff"
  ></operation-fjzj>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import BasicInfo from '@hbfe/jxjy-admin-scheme/src/diff/fjzj/__components__/basic-info.vue'
  import TrainingRequire from '@hbfe/jxjy-admin-scheme/src/diff/fjzj/__components__/training-require.vue'
  import MutationCreateTrainClassCommodity from '@api/service/diff/management/fjzj/train-class/MutationCreateTrainClassCommodity'
  import Operation from '@hbfe/jxjy-admin-scheme/src/components/operation.vue'
  import MutationTrainClassFactory from '@api/service/diff/management/fjzj/train-class/MutationTrainClassFactory'
  import QueryPlatform from '@api/service/diff/common/zztt/dictionary/QueryPlatform'

  @Component({
    components: { BasicInfo, TrainingRequire }
  })
  class OperationFjzj extends Operation {
    /**
     * 培训班商品信息
     */
    trainClassCommodityInfo: MutationCreateTrainClassCommodity = new MutationCreateTrainClassCommodity()
    /**
     * 创建/修改/复制
     */
    MutationTrainClassFactory = new MutationTrainClassFactory()

    @Watch('defaultSchemeType', { immediate: true })
    defaultSchemeTypeChange() {
      this.$emit('defaultSchemeTypeChange', this.defaultSchemeType)
    }

    thirdPlatformIdChange(val: string) {
      this.trainClassCommodityInfo.trainClassBaseInfo.thirdPartyId = val
    }
  }

  @Component({
    components: { OperationFjzj }
  })
  export default class extends Vue {
    /**
     * 基础信息
     */
    @Ref('operationRef') operationRef: OperationFjzj

    /**
     *  培训班类型赋值
     */
    defaultSchemeTypeDiff = ''

    /**
     *  培训班类型赋值
     */
    async defaultSchemeTypeChange(val: string) {
      this.defaultSchemeTypeDiff = val
      if (this.defaultSchemeTypeDiff === 'train_cooperate') {
        await QueryPlatform.queryList()
        this.operationRef.thirdPlatformIdChange(QueryPlatform.list[0].id)
      }
    }
  }
</script>
