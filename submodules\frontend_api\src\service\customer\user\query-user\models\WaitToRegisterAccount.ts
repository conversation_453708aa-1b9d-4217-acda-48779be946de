export class WaitToRegisterAccount {
  /**
   * 手机号码
   */
  phoneNumber: string
  /**
   * 注册方式，11：平台注册，21：第三方互联之QQ，31：第三方互联之新浪微博，41：第三方互联之微信，42：第三方互联之 微信公众号，51：第三方互联之 外部来源
   */
  registerType: number

  /**
   * 注册来源，1：项目主网站，2：安卓，3：IOS 苹果
   */
  sourceType: number

  /**
   * 密码
   */
  password: string

  /**
   * 昵称必填，目前暂时和手机号一致
   */
  nickName: string
  /**
   * 注册的机构id
   */
  registerUnitId = '-1'
}
