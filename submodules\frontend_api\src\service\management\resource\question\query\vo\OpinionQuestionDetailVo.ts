import { OpinionQuestionResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import QuestionDetail from '../common/QuestionDetail'

/*
 *判断题详情
 */
class OpinionQuestionDetailVo extends QuestionDetail {
  /**
   * 正确答案
   */
  correctAnswer = true

  /**
   * 正确文本
   */
  correctAnswerText = ''

  /**
   * 不正确文本
   */
  incorrectAnswerText = ''

  // 模型转换Vo
  from(item: OpinionQuestionResponse, list: Array<string>) {
    this.questionId = item.questionId
    this.libraryId = item.libraryInfo.libraryId
    this.libraryName = item.libraryInfo.libraryName
    this.topic = item.topic
    this.dissects = item.dissects
    this.createTime = item.createTime
    this.questionType = item.questionType
    this.relateCourseIds = item.relateCourseIds
    this.correctAnswer = item.opinionCorrectAnswer
    this.correctAnswerText = item.correctAnswerText
    this.incorrectAnswerText = item.incorrectAnswerText
    this.isEnable = item.isEnabled
    this.relateCourseNames = list
  }
}
export default OpinionQuestionDetailVo
