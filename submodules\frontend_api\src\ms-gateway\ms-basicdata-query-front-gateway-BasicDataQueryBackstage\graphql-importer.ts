import findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType from './queries/findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType.graphql'
import getAdminInfoInMyself from './queries/getAdminInfoInMyself.graphql'
import getAdminInfoInServicer from './queries/getAdminInfoInServicer.graphql'
import getAdminInfoInSubProject from './queries/getAdminInfoInSubProject.graphql'
import getAdminUserInfoInSubProject from './queries/getAdminUserInfoInSubProject.graphql'
import getBannerListByPortalId from './queries/getBannerListByPortalId.graphql'
import getBannerListByPortalType from './queries/getBannerListByPortalType.graphql'
import getBannerListByPortalTypeForFxpt from './queries/getBannerListByPortalTypeForFxpt.graphql'
import getBusinessRegionById from './queries/getBusinessRegionById.graphql'
import getBusinessRegionTree from './queries/getBusinessRegionTree.graphql'
import getClientInfoByServicerId from './queries/getClientInfoByServicerId.graphql'
import getCollectiveInfoInServicer from './queries/getCollectiveInfoInServicer.graphql'
import getContractProviderInfoAdminInfoInMyself from './queries/getContractProviderInfoAdminInfoInMyself.graphql'
import getContractProviderInfoAdminInfoInServicer from './queries/getContractProviderInfoAdminInfoInServicer.graphql'
import getContractProviderInfoAdminInfoInSubProject from './queries/getContractProviderInfoAdminInfoInSubProject.graphql'
import getContractProviderInfoInMyself from './queries/getContractProviderInfoInMyself.graphql'
import getContractProviderInfoInSubProject from './queries/getContractProviderInfoInSubProject.graphql'
import getCoursewareSupplierAdminInfoInMyself from './queries/getCoursewareSupplierAdminInfoInMyself.graphql'
import getCoursewareSupplierAdminInfoInServicer from './queries/getCoursewareSupplierAdminInfoInServicer.graphql'
import getCoursewareSupplierAdminInfoInSubProject from './queries/getCoursewareSupplierAdminInfoInSubProject.graphql'
import getCoursewareSupplierInfoInMyself from './queries/getCoursewareSupplierInfoInMyself.graphql'
import getCoursewareSupplierInfoInSubProject from './queries/getCoursewareSupplierInfoInSubProject.graphql'
import getCurrentServicerInfo from './queries/getCurrentServicerInfo.graphql'
import getDistributionServicerInfoInMyself from './queries/getDistributionServicerInfoInMyself.graphql'
import getDistributionServicerInfoInSubProject from './queries/getDistributionServicerInfoInSubProject.graphql'
import getEnterpriseUnitAdminInfoInMyself from './queries/getEnterpriseUnitAdminInfoInMyself.graphql'
import getEnterpriseUnitAdminInfoInSubProject from './queries/getEnterpriseUnitAdminInfoInSubProject.graphql'
import getEnterpriseUnitCountInGovernmentUnit from './queries/getEnterpriseUnitCountInGovernmentUnit.graphql'
import getEnterpriseUnitCountInSubProject from './queries/getEnterpriseUnitCountInSubProject.graphql'
import getEnterpriseUnitInfoInMyself from './queries/getEnterpriseUnitInfoInMyself.graphql'
import getEnterpriseUnitInfoInServicer from './queries/getEnterpriseUnitInfoInServicer.graphql'
import getEnterpriseUnitInfoInSubProject from './queries/getEnterpriseUnitInfoInSubProject.graphql'
import getFriendLinkListByPortalId from './queries/getFriendLinkListByPortalId.graphql'
import getFriendLinkListByPortalType from './queries/getFriendLinkListByPortalType.graphql'
import getGovernmentUnitAdminInfoInMyself from './queries/getGovernmentUnitAdminInfoInMyself.graphql'
import getGovernmentUnitAdminInfoInSubProject from './queries/getGovernmentUnitAdminInfoInSubProject.graphql'
import getGovernmentUnitInfoInSubProject from './queries/getGovernmentUnitInfoInSubProject.graphql'
import getIndustryInfo from './queries/getIndustryInfo.graphql'
import getIndustryInfoV2 from './queries/getIndustryInfoV2.graphql'
import getIndustryPropertyByIdInSubProject from './queries/getIndustryPropertyByIdInSubProject.graphql'
import getIndustryType from './queries/getIndustryType.graphql'
import getLeaderPositionLevel from './queries/getLeaderPositionLevel.graphql'
import getMenuSettingByPortalType from './queries/getMenuSettingByPortalType.graphql'
import getNewsCategoryId from './queries/getNewsCategoryId.graphql'
import getNewsDetail from './queries/getNewsDetail.graphql'
import getOnlineSchoolAdminInfoInMyself from './queries/getOnlineSchoolAdminInfoInMyself.graphql'
import getOnlineSchoolAdminInfoInServicer from './queries/getOnlineSchoolAdminInfoInServicer.graphql'
import getOnlineSchoolAdminInfoInSubProject from './queries/getOnlineSchoolAdminInfoInSubProject.graphql'
import getOnlineSchoolInfoCount from './queries/getOnlineSchoolInfoCount.graphql'
import getOnlineSchoolInfoResponseInSubProject from './queries/getOnlineSchoolInfoResponseInSubProject.graphql'
import getPhysicalRegionById from './queries/getPhysicalRegionById.graphql'
import getPortalInfoInSubProject from './queries/getPortalInfoInSubProject.graphql'
import getRootNewsCategory from './queries/getRootNewsCategory.graphql'
import getRootNewsCategoryById from './queries/getRootNewsCategoryById.graphql'
import getServiceOrIndustryRegion from './queries/getServiceOrIndustryRegion.graphql'
import getServiceOrIndustryRegionByQuery from './queries/getServiceOrIndustryRegionByQuery.graphql'
import getServiceOrIndustryRegionInDistribution from './queries/getServiceOrIndustryRegionInDistribution.graphql'
import getStudentInfoInServicer from './queries/getStudentInfoInServicer.graphql'
import getStudentInfoInSubProject from './queries/getStudentInfoInSubProject.graphql'
import getStudentInfoInSubject from './queries/getStudentInfoInSubject.graphql'
import getStudentOnlineCountInServicer from './queries/getStudentOnlineCountInServicer.graphql'
import getStudentRegisteredCountInSubProject from './queries/getStudentRegisteredCountInSubProject.graphql'
import getSupplierServicerInfoInMyself from './queries/getSupplierServicerInfoInMyself.graphql'
import getSupplierServicerInfoInSubProject from './queries/getSupplierServicerInfoInSubProject.graphql'
import getTrainingCategoryInfo from './queries/getTrainingCategoryInfo.graphql'
import getTrainingInstitutionPortalInfo from './queries/getTrainingInstitutionPortalInfo.graphql'
import getTrainingInstitutionPortalThemeColor from './queries/getTrainingInstitutionPortalThemeColor.graphql'
import getUnitType from './queries/getUnitType.graphql'
import getYearById from './queries/getYearById.graphql'
import listALLIndustryPropertyRootByCategory from './queries/listALLIndustryPropertyRootByCategory.graphql'
import listALLIndustryPropertyRootByCategoryV2 from './queries/listALLIndustryPropertyRootByCategoryV2.graphql'
import listAllIndustryProperty from './queries/listAllIndustryProperty.graphql'
import listAreaTrainingChannelStudentInfoInSubProject from './queries/listAreaTrainingChannelStudentInfoInSubProject.graphql'
import listBusinessDictionaryAcrossTypeBySalveId from './queries/listBusinessDictionaryAcrossTypeBySalveId.graphql'
import listBusinessRegionChildById from './queries/listBusinessRegionChildById.graphql'
import listBusinessRegionListById from './queries/listBusinessRegionListById.graphql'
import listBusinessRegionNameMap from './queries/listBusinessRegionNameMap.graphql'
import listBusinessRegionTreeChild from './queries/listBusinessRegionTreeChild.graphql'
import listBusinessRegionTreeRoot from './queries/listBusinessRegionTreeRoot.graphql'
import listChildNewsCategory from './queries/listChildNewsCategory.graphql'
import listChildNewsCategoryInServicer from './queries/listChildNewsCategoryInServicer.graphql'
import listChildNewsCategoryNeedLogin from './queries/listChildNewsCategoryNeedLogin.graphql'
import listEducationProperty from './queries/listEducationProperty.graphql'
import listIndustryInfo from './queries/listIndustryInfo.graphql'
import listIndustryInfoV2 from './queries/listIndustryInfoV2.graphql'
import listIndustryPropertyByOnlineSchool from './queries/listIndustryPropertyByOnlineSchool.graphql'
import listIndustryPropertyByOnlineSchoolV2 from './queries/listIndustryPropertyByOnlineSchoolV2.graphql'
import listIndustryPropertyCategory from './queries/listIndustryPropertyCategory.graphql'
import listIndustryPropertyCategoryV2 from './queries/listIndustryPropertyCategoryV2.graphql'
import listIndustryPropertyChildByCategory from './queries/listIndustryPropertyChildByCategory.graphql'
import listIndustryPropertyChildByCategoryV2 from './queries/listIndustryPropertyChildByCategoryV2.graphql'
import listIndustryPropertyRootByCategory from './queries/listIndustryPropertyRootByCategory.graphql'
import listIndustryPropertyRootByCategoryV2 from './queries/listIndustryPropertyRootByCategoryV2.graphql'
import listIndustryTypeChild from './queries/listIndustryTypeChild.graphql'
import listIndustryTypeRoot from './queries/listIndustryTypeRoot.graphql'
import listLeaderPositionLevel from './queries/listLeaderPositionLevel.graphql'
import listLeaderPositionLevelRoot from './queries/listLeaderPositionLevelRoot.graphql'
import listNewsCategoryTree from './queries/listNewsCategoryTree.graphql'
import listPhysicalRegionChildById from './queries/listPhysicalRegionChildById.graphql'
import listPhysicalRegionListById from './queries/listPhysicalRegionListById.graphql'
import listRootNewsCategory from './queries/listRootNewsCategory.graphql'
import listRootNewsCategoryNeedLogin from './queries/listRootNewsCategoryNeedLogin.graphql'
import listSalveDictionaryByMasterId from './queries/listSalveDictionaryByMasterId.graphql'
import listServicerContractPropertyByCategory from './queries/listServicerContractPropertyByCategory.graphql'
import listTrainingCategoryChild from './queries/listTrainingCategoryChild.graphql'
import listTrainingCategoryInfo from './queries/listTrainingCategoryInfo.graphql'
import listTrainingCategoryRoot from './queries/listTrainingCategoryRoot.graphql'
import listUnitTypeChild from './queries/listUnitTypeChild.graphql'
import listUnitTypeRoot from './queries/listUnitTypeRoot.graphql'
import listYearListById from './queries/listYearListById.graphql'
import pageALLIndustryPropertyRootByCategoryV2 from './queries/pageALLIndustryPropertyRootByCategoryV2.graphql'
import pageAccountInfoInServicer from './queries/pageAccountInfoInServicer.graphql'
import pageAccountInfoInSubProject from './queries/pageAccountInfoInSubProject.graphql'
import pageAdminInInDistributor from './queries/pageAdminInInDistributor.graphql'
import pageAdminInfoInCourseSupplier from './queries/pageAdminInfoInCourseSupplier.graphql'
import pageAdminInfoInServicer from './queries/pageAdminInfoInServicer.graphql'
import pageAdminInfoInSubProject from './queries/pageAdminInfoInSubProject.graphql'
import pageCollectiveInfoInServicer from './queries/pageCollectiveInfoInServicer.graphql'
import pageCompleteNews from './queries/pageCompleteNews.graphql'
import pageContractProviderInfoAdminInfoInServicer from './queries/pageContractProviderInfoAdminInfoInServicer.graphql'
import pageContractProviderInfoAdminInfoInSubProject from './queries/pageContractProviderInfoAdminInfoInSubProject.graphql'
import pageContractProviderInfoInSubProject from './queries/pageContractProviderInfoInSubProject.graphql'
import pageCoursewareSupplierAdminInfoInServicer from './queries/pageCoursewareSupplierAdminInfoInServicer.graphql'
import pageCoursewareSupplierAdminInfoInSubProject from './queries/pageCoursewareSupplierAdminInfoInSubProject.graphql'
import pageCoursewareSupplierGRPCInSubject from './queries/pageCoursewareSupplierGRPCInSubject.graphql'
import pageCoursewareSupplierInfoInSubProject from './queries/pageCoursewareSupplierInfoInSubProject.graphql'
import pageCurrentServicerEnterpriseUnitInfoInServicer from './queries/pageCurrentServicerEnterpriseUnitInfoInServicer.graphql'
import pageCurrentServicerEnterpriseUnitInfoInServicerInDistribution from './queries/pageCurrentServicerEnterpriseUnitInfoInServicerInDistribution.graphql'
import pageDistributionServicerInfoInSubProject from './queries/pageDistributionServicerInfoInSubProject.graphql'
import pageEnterpriseUnitAdminInfoInGovernmentUnit from './queries/pageEnterpriseUnitAdminInfoInGovernmentUnit.graphql'
import pageEnterpriseUnitAdminInfoInMyself from './queries/pageEnterpriseUnitAdminInfoInMyself.graphql'
import pageEnterpriseUnitAdminInfoInSubProject from './queries/pageEnterpriseUnitAdminInfoInSubProject.graphql'
import pageEnterpriseUnitInfoInGovernmentUnit from './queries/pageEnterpriseUnitInfoInGovernmentUnit.graphql'
import pageEnterpriseUnitInfoInServicer from './queries/pageEnterpriseUnitInfoInServicer.graphql'
import pageEnterpriseUnitInfoInSubProject from './queries/pageEnterpriseUnitInfoInSubProject.graphql'
import pageGovernmentUnitAdminInfoInGovernmentUnit from './queries/pageGovernmentUnitAdminInfoInGovernmentUnit.graphql'
import pageGovernmentUnitAdminInfoInMyself from './queries/pageGovernmentUnitAdminInfoInMyself.graphql'
import pageGovernmentUnitAdminInfoInSubProject from './queries/pageGovernmentUnitAdminInfoInSubProject.graphql'
import pageGovernmentUnitInfoInGovernmentUnit from './queries/pageGovernmentUnitInfoInGovernmentUnit.graphql'
import pageGovernmentUnitInfoInSubProject from './queries/pageGovernmentUnitInfoInSubProject.graphql'
import pageIndustryPropertyByCategoryInSubProject from './queries/pageIndustryPropertyByCategoryInSubProject.graphql'
import pageIndustryPropertyInSubProject from './queries/pageIndustryPropertyInSubProject.graphql'
import pageOnlineSchoolAdminInfoInFxpt from './queries/pageOnlineSchoolAdminInfoInFxpt.graphql'
import pageOnlineSchoolAdminInfoInServicer from './queries/pageOnlineSchoolAdminInfoInServicer.graphql'
import pageOnlineSchoolAdminInfoInSubProject from './queries/pageOnlineSchoolAdminInfoInSubProject.graphql'
import pageOnlineSchoolInfoResponseInSubProject from './queries/pageOnlineSchoolInfoResponseInSubProject.graphql'
import pagePortalInfoInSubProject from './queries/pagePortalInfoInSubProject.graphql'
import pageServicerUnitAdminInfoInServicer from './queries/pageServicerUnitAdminInfoInServicer.graphql'
import pageServicerUnitAdminInfoInSubProject from './queries/pageServicerUnitAdminInfoInSubProject.graphql'
import pageSimpleNews from './queries/pageSimpleNews.graphql'
import pageStudentInfoInServicer from './queries/pageStudentInfoInServicer.graphql'
import pageStudentInfoInSubProject from './queries/pageStudentInfoInSubProject.graphql'
import pageStudentInfoInSupplier from './queries/pageStudentInfoInSupplier.graphql'
import pageSubjectType from './queries/pageSubjectType.graphql'
import pageSupplierServicerInfoInSubProject from './queries/pageSupplierServicerInfoInSubProject.graphql'
import pageTrainingCategory from './queries/pageTrainingCategory.graphql'
import pageTrainingChannelSimpleNews from './queries/pageTrainingChannelSimpleNews.graphql'
import pageUnAddIndustryProperty from './queries/pageUnAddIndustryProperty.graphql'
import pageUserAccountInfoInServicer from './queries/pageUserAccountInfoInServicer.graphql'
import statisticEnterpriseUnitGroupByTimeInGovernmentUnit from './queries/statisticEnterpriseUnitGroupByTimeInGovernmentUnit.graphql'
import statisticEnterpriseUnitIndustryInGovernmentUnit from './queries/statisticEnterpriseUnitIndustryInGovernmentUnit.graphql'
import statisticEnterpriseUnitRegionInGovernmentUnit from './queries/statisticEnterpriseUnitRegionInGovernmentUnit.graphql'
import statisticEnterpriseUnitTypeInGovernmentUnit from './queries/statisticEnterpriseUnitTypeInGovernmentUnit.graphql'

export {
  findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType,
  getAdminInfoInMyself,
  getAdminInfoInServicer,
  getAdminInfoInSubProject,
  getAdminUserInfoInSubProject,
  getBannerListByPortalId,
  getBannerListByPortalType,
  getBannerListByPortalTypeForFxpt,
  getBusinessRegionById,
  getBusinessRegionTree,
  getClientInfoByServicerId,
  getCollectiveInfoInServicer,
  getContractProviderInfoAdminInfoInMyself,
  getContractProviderInfoAdminInfoInServicer,
  getContractProviderInfoAdminInfoInSubProject,
  getContractProviderInfoInMyself,
  getContractProviderInfoInSubProject,
  getCoursewareSupplierAdminInfoInMyself,
  getCoursewareSupplierAdminInfoInServicer,
  getCoursewareSupplierAdminInfoInSubProject,
  getCoursewareSupplierInfoInMyself,
  getCoursewareSupplierInfoInSubProject,
  getCurrentServicerInfo,
  getDistributionServicerInfoInMyself,
  getDistributionServicerInfoInSubProject,
  getEnterpriseUnitAdminInfoInMyself,
  getEnterpriseUnitAdminInfoInSubProject,
  getEnterpriseUnitCountInGovernmentUnit,
  getEnterpriseUnitCountInSubProject,
  getEnterpriseUnitInfoInMyself,
  getEnterpriseUnitInfoInServicer,
  getEnterpriseUnitInfoInSubProject,
  getFriendLinkListByPortalId,
  getFriendLinkListByPortalType,
  getGovernmentUnitAdminInfoInMyself,
  getGovernmentUnitAdminInfoInSubProject,
  getGovernmentUnitInfoInSubProject,
  getIndustryInfo,
  getIndustryInfoV2,
  getIndustryPropertyByIdInSubProject,
  getIndustryType,
  getLeaderPositionLevel,
  getMenuSettingByPortalType,
  getNewsCategoryId,
  getNewsDetail,
  getOnlineSchoolAdminInfoInMyself,
  getOnlineSchoolAdminInfoInServicer,
  getOnlineSchoolAdminInfoInSubProject,
  getOnlineSchoolInfoCount,
  getOnlineSchoolInfoResponseInSubProject,
  getPhysicalRegionById,
  getPortalInfoInSubProject,
  getRootNewsCategory,
  getRootNewsCategoryById,
  getServiceOrIndustryRegion,
  getServiceOrIndustryRegionByQuery,
  getServiceOrIndustryRegionInDistribution,
  getStudentInfoInServicer,
  getStudentInfoInSubProject,
  getStudentInfoInSubject,
  getStudentOnlineCountInServicer,
  getStudentRegisteredCountInSubProject,
  getSupplierServicerInfoInMyself,
  getSupplierServicerInfoInSubProject,
  getTrainingCategoryInfo,
  getTrainingInstitutionPortalInfo,
  getTrainingInstitutionPortalThemeColor,
  getUnitType,
  getYearById,
  listALLIndustryPropertyRootByCategory,
  listALLIndustryPropertyRootByCategoryV2,
  listAllIndustryProperty,
  listAreaTrainingChannelStudentInfoInSubProject,
  listBusinessDictionaryAcrossTypeBySalveId,
  listBusinessRegionChildById,
  listBusinessRegionListById,
  listBusinessRegionNameMap,
  listBusinessRegionTreeChild,
  listBusinessRegionTreeRoot,
  listChildNewsCategory,
  listChildNewsCategoryInServicer,
  listChildNewsCategoryNeedLogin,
  listEducationProperty,
  listIndustryInfo,
  listIndustryInfoV2,
  listIndustryPropertyByOnlineSchool,
  listIndustryPropertyByOnlineSchoolV2,
  listIndustryPropertyCategory,
  listIndustryPropertyCategoryV2,
  listIndustryPropertyChildByCategory,
  listIndustryPropertyChildByCategoryV2,
  listIndustryPropertyRootByCategory,
  listIndustryPropertyRootByCategoryV2,
  listIndustryTypeChild,
  listIndustryTypeRoot,
  listLeaderPositionLevel,
  listLeaderPositionLevelRoot,
  listNewsCategoryTree,
  listPhysicalRegionChildById,
  listPhysicalRegionListById,
  listRootNewsCategory,
  listRootNewsCategoryNeedLogin,
  listSalveDictionaryByMasterId,
  listServicerContractPropertyByCategory,
  listTrainingCategoryChild,
  listTrainingCategoryInfo,
  listTrainingCategoryRoot,
  listUnitTypeChild,
  listUnitTypeRoot,
  listYearListById,
  pageALLIndustryPropertyRootByCategoryV2,
  pageAccountInfoInServicer,
  pageAccountInfoInSubProject,
  pageAdminInInDistributor,
  pageAdminInfoInCourseSupplier,
  pageAdminInfoInServicer,
  pageAdminInfoInSubProject,
  pageCollectiveInfoInServicer,
  pageCompleteNews,
  pageContractProviderInfoAdminInfoInServicer,
  pageContractProviderInfoAdminInfoInSubProject,
  pageContractProviderInfoInSubProject,
  pageCoursewareSupplierAdminInfoInServicer,
  pageCoursewareSupplierAdminInfoInSubProject,
  pageCoursewareSupplierGRPCInSubject,
  pageCoursewareSupplierInfoInSubProject,
  pageCurrentServicerEnterpriseUnitInfoInServicer,
  pageCurrentServicerEnterpriseUnitInfoInServicerInDistribution,
  pageDistributionServicerInfoInSubProject,
  pageEnterpriseUnitAdminInfoInGovernmentUnit,
  pageEnterpriseUnitAdminInfoInMyself,
  pageEnterpriseUnitAdminInfoInSubProject,
  pageEnterpriseUnitInfoInGovernmentUnit,
  pageEnterpriseUnitInfoInServicer,
  pageEnterpriseUnitInfoInSubProject,
  pageGovernmentUnitAdminInfoInGovernmentUnit,
  pageGovernmentUnitAdminInfoInMyself,
  pageGovernmentUnitAdminInfoInSubProject,
  pageGovernmentUnitInfoInGovernmentUnit,
  pageGovernmentUnitInfoInSubProject,
  pageIndustryPropertyByCategoryInSubProject,
  pageIndustryPropertyInSubProject,
  pageOnlineSchoolAdminInfoInFxpt,
  pageOnlineSchoolAdminInfoInServicer,
  pageOnlineSchoolAdminInfoInSubProject,
  pageOnlineSchoolInfoResponseInSubProject,
  pagePortalInfoInSubProject,
  pageServicerUnitAdminInfoInServicer,
  pageServicerUnitAdminInfoInSubProject,
  pageSimpleNews,
  pageStudentInfoInServicer,
  pageStudentInfoInSubProject,
  pageStudentInfoInSupplier,
  pageSubjectType,
  pageSupplierServicerInfoInSubProject,
  pageTrainingCategory,
  pageTrainingChannelSimpleNews,
  pageUnAddIndustryProperty,
  pageUserAccountInfoInServicer,
  statisticEnterpriseUnitGroupByTimeInGovernmentUnit,
  statisticEnterpriseUnitIndustryInGovernmentUnit,
  statisticEnterpriseUnitRegionInGovernmentUnit,
  statisticEnterpriseUnitTypeInGovernmentUnit
}
