<route-meta>
{
"title": "日期时间选择器"
}
</route-meta>
<template>
  <div>
    <hb-range-wrapper>
      <el-date-picker
        v-model="startTime"
        :type="dateType"
        :placeholder="beginTimePlaceholder"
        :value-format="valueFormat"
        :picker-options="pickerOptionsStart"
      >
      </el-date-picker>
      <el-date-picker
        v-model="endTime"
        :type="dateType"
        :placeholder="endTimePlaceholder"
        :value-format="valueFormat"
        :picker-options="pickerOptionsEnd"
        :default-time="endDefaultTime"
      >
      </el-date-picker>
    </hb-range-wrapper>
  </div>
</template>
<script lang="ts">
  // 时间选择
  import { Vue, Component, Emit, Prop } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    // 格式化时间
    @Prop({
      type: String,
      default: 'yyyy-MM-dd HH:mm:ss'
    })
    valueFormat: string
    // 日期
    @Prop({
      type: String,
      default: 'datetime'
    })
    dateType: string

    // 开始时间
    @Prop({ type: String, default: '' }) beginCreateTime: string
    // 结束时间
    @Prop({ type: String, default: '' }) endCreateTime: string

    @Prop({ type: String, default: '起始时间' }) beginTimePlaceholder: string

    @Prop({ type: String, default: '结束时间' }) endTimePlaceholder: string

    // 结束默认选中日期后的默认具体时刻
    @Prop({
      type: String,
      default: '00:00:00'
    })
    endDefaultTime: string
    // 参数
    pickerOptionsStart = {
      // 日期不能选择
      disabledDate: (time: any) => {
        // 当前时间
        const myDate = Date.now()
        let compareTime: number = myDate
        // 已经选择结束时间
        if (this.endTime) {
          compareTime = new Date(this.endTime).getTime()
          return time.getTime() >= myDate || time.getTime() > compareTime
        } else {
          return time.getTime() > myDate
        }
      }
    }
    beforeDestroy() {
      console.log('beforeDestroyDouble')
    }
    destroyed() {
      console.log('destroyedDouble')
    }
    pickerOptionsEnd = {
      // 日期不能选择
      disabledDate: (time: any) => {
        // 当前日期
        const myDate = Date.now()
        let compareTime: number = myDate
        // 已经选择开始时间
        if (this.startTime) {
          compareTime = new Date(this.startTime).getTime()
          return time.getTime() > myDate + 86400000 || time.getTime() < compareTime
        } else {
          return time.getTime() > myDate + 86400000
        }
      }
    }

    // 接收开始时间
    get startTime(): string {
      return this.beginCreateTime
    }
    set startTime(val: string) {
      this.starTimeDrager(val)
    }
    // 接收结束时间
    get endTime(): string {
      return this.endCreateTime
    }
    set endTime(val: string) {
      this.endTimeDrager(val)
    }

    // 开始时间
    @Emit('update:beginCreateTime')
    starTimeDrager(val: string) {
      if (!val) {
        return null
      }
      return val
    }
    // 结束时间
    @Emit('update:endCreateTime')
    endTimeDrager(val: string) {
      if (!val) {
        return null
      }
      return val
    }
  }
</script>
