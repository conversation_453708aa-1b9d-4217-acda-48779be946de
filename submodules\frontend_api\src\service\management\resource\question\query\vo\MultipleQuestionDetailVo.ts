import {
  ChooseAnswerOption,
  MultipleQuestionResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import QuestionDetail from '../common/QuestionDetail'

/*
 * 多选题详情
 */
class MultipleQuestionDetailVo extends QuestionDetail {
  /**
   * 可选答案列表
   */
  answerOptions: Array<ChooseAnswerOption> = []
  /**
   * 正确答案ID集合
   */
  correctAnswerIds: Array<string> = []

  /**
   * 正确答案内容集合
   */
  correctAnswerContent: Array<ChooseAnswerOption> = []

  // 模型转换Vo
  from(item: MultipleQuestionResponse, list: Array<string>) {
    this.questionId = item.questionId
    this.libraryId = item.libraryInfo.libraryId
    this.libraryName = item.libraryInfo.libraryName
    this.topic = item.topic
    this.dissects = item.dissects
    this.createTime = item.createTime
    this.questionType = item.questionType
    this.relateCourseIds = item.relateCourseIds
    this.isEnable = item.isEnabled
    this.answerOptions = item.multipleAnswerOptions?.map((el) => {
      if (this.correctAnswerIds.includes(el.id)) {
        this.correctAnswerContent.push({
          id: el.id,
          content: el.content,
          enableFillContent: el.enableFillContent,
          mustFillContent: el.mustFillContent
        })
      }
      return {
        id: el.id,
        content: el.content,
        enableFillContent: el.enableFillContent,
        mustFillContent: el.mustFillContent
      }
    })
    this.correctAnswerIds = item.correctAnswerIds
    this.relateCourseNames = list
  }
}
export default MultipleQuestionDetailVo
