<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--编辑基础信息-->
        <el-button @click="dialog12 = true" type="primary" class="f-mr20 f-mb20">编辑基础信息</el-button>
        <el-drawer
          title="编辑基础信息"
          :visible.sync="dialog12"
          :direction="direction"
          size="560px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="姓名：">
                <el-input v-model="form.name" clearable placeholder="请输入姓名" />
              </el-form-item>
              <el-form-item label="证件类型：">
                <el-select v-model="form.region" clearable placeholder="请选择证件类型">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="证件号：">
                <el-input v-model="form.name" clearable placeholder="请输入证件号" />
              </el-form-item>
              <el-form-item label="性别：">
                <el-select v-model="form.region" clearable placeholder="请选择性别">
                  <el-option value="男"></el-option>
                  <el-option value="女"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="手机号：">
                <el-input v-model="form.name" clearable placeholder="请输入手机号" />
              </el-form-item>
              <el-form-item label="单位地区：">
                <el-cascader clearable filterable :options="cascader" placeholder="请选择单位地区" />
              </el-form-item>
              <el-form-item label="工作单位：">
                <el-cascader clearable filterable :options="cascader" placeholder="请选择工作单位" />
              </el-form-item>
              <el-form-item label="工作单位：">
                <el-cascader clearable filterable :options="cascader" placeholder="请选择工作单位（全称）" />
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button>取消</el-button>
                <el-button type="primary">保存</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <!--确认配送-->
        <el-button @click="dialog14 = true" type="primary" class="f-mr20 f-mb20">新增单位</el-button>
        <el-drawer
          title="新增单位"
          :visible.sync="dialog14"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              为确保单位数据真实性，请输入单位全称，及统一社会信用代码。
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="单位名称：" required>
                    <el-input v-model="form.desc" placeholder="请输入单位全称" />
                  </el-form-item>
                  <el-form-item label="统一社会信用代码：" required>
                    <el-input v-model="form.desc" placeholder="请输入18位有效的统一社会信用代码" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--选择单位-->
        <el-button @click="dialog13 = true" type="primary" class="f-mr20">选择单位</el-button>
        <el-drawer
          title="选择单位"
          :visible.sync="dialog13"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--条件查询-->
            <el-row :gutter="0" class="m-query">
              <el-form :inline="true" label-width="auto">
                <el-col :span="12">
                  <el-form-item label="培训方案">
                    <el-input v-model="input" clearable placeholder="请输入单位名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <div class="clear f-mb30">
              <div class="f-fl f-ci">请至少输入4个关键字</div>
              <div class="f-fr"><a href="#" class="f-cb">找不到单位，手动输入！</a></div>
            </div>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column label="单位名称" min-width="300">
                <template>这里是单位名称这里是单位名称这里是单位名称</template>
              </el-table-column>
              <el-table-column label="社会统一信用代码" min-width="140" align="center">
                <template>54353453454353453</template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template>
                  <el-button type="text">选择</el-button>
                  <el-button type="text">取消选择</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
            <div class="m-no-date">
              <img class="img" src="./assets/images/no-data-normal.png" alt="" />
              <div class="date-bd">
                <p class="f-f15 f-c9">暂时还没有内容~</p>
              </div>
            </div>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--一修改基准照片次数-->
        <el-button @click="dialog11 = true" type="primary" class="f-mr20 f-mb20">修改基准照片次数</el-button>
        <el-drawer
          title="修改基准照片次数"
          :visible.sync="dialog11"
          :direction="direction"
          size="450px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div>
              <span class="f-fb f-c6">当前剩余修改基准照片次数：</span><span class="f-f24 f-fb f-ci f-mr5">2</span>次
            </div>
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="增加修改基准照片次数：">
                <el-input v-model="form.name" class="u-w180" placeholder="请输入" />
                <span class="f-ml5">次</span>
              </el-form-item>
            </el-form>
            <div class="m-btn-bar f-tc f-mt30">
              <el-button>取消</el-button>
              <el-button type="primary">保存</el-button>
            </div>
          </div>
        </el-drawer>
        <!--新增证书-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20 f-mb20">新增证书</el-button>
        <el-drawer title="新增证书" :visible.sync="dialog2" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="证书类别：" required>
                <el-cascader clearable :options="cascader" placeholder="请选择证书类别" />
              </el-form-item>
              <el-form-item label="选择专业：" required>
                <el-select v-model="form.region" clearable placeholder="请选择选择专业">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="证书编号：" required>
                <el-input v-model="form.name" clearable placeholder="请输入开户银行" class="form-l" />
              </el-form-item>
              <el-form-item label="证书发证日期：" required>
                <el-input v-model="form.name" clearable placeholder="请输入证书发证日期" class="form-l" />
              </el-form-item>
              <el-form-item label="证书有效期：" required>
                <el-input v-model="form.name" clearable placeholder="请输入证书有效期" class="form-m" />
              </el-form-item>
              <el-form-item label="证书附件：">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :auto-upload="false"
                  class="m-pic-upload proportion-pic is-small"
                >
                  <div slot="default" class="upload-placeholder">
                    <i class="el-icon-plus"></i>
                    <p class="txt">上传图片</p>
                  </div>
                  <div slot="file" slot-scope="{ file }" class="img-file">
                    <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                    <div class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </div>
                  </div>
                  <div slot="tip" class="el-upload__tip">
                    <i class="el-icon-warning"></i>
                    <span class="txt">请上传*.jpg，*.jpeg，*.png格式的图片</span>
                  </div>
                </el-upload>
                <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                  <img :src="dialogImageUrl" alt="" />
                </el-dialog>
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button>取消</el-button>
                <el-button type="primary">保存</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <!--一键合格-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20 f-mb20">一键合格</el-button>
        <el-drawer title="一键合格" :visible.sync="dialog1" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="error" :closable="false" class="m-alert f-mb10"
              ><i class="f-cr el-icon-warning f-mr5 f-f16"></i>一键合格的班级，无学习监拍日志，请谨慎操作！</el-alert
            >
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="一键合格类型：" required>
                <el-radio v-model="radio" label="1">
                  按系统当前操作成功时间
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                    <i class="el-icon-info m-tooltip-icon f-c9"></i>
                    <div slot="content">
                      按系统当前操作成功时间：快速完成培训，所有学习过程的时间都按照系统当前的操作时间
                    </div>
                  </el-tooltip>
                </el-radio>
              </el-form-item>
              <!--<el-form-item label="合格时间配置：" required>-->
              <!--  <el-radio-group v-model="form.resource">-->
              <!--    <el-radio>-->
              <!--      指定合格时间-->
              <!--      &lt;!&ndash;选中后出现输入框&ndash;&gt;-->
              <!--      <el-date-picker-->
              <!--        v-model="form.date1"-->
              <!--        type="datetime"-->
              <!--        placeholder="请输入指定合格时间"-->
              <!--        class="f-ml10"-->
              <!--      >-->
              <!--      </el-date-picker>-->
              <!--    </el-radio>-->
              <!--    <el-radio label="按系统当前操作成功时间"></el-radio>-->
              <!--  </el-radio-group>-->
              <!--</el-form-item>-->
              <el-form-item label="考试合格分：" required>
                <el-input v-model="form.name" class="input-num" placeholder="请输入" />
                <span class="f-ml5">分</span>
                <span class="f-cb">（及格 / 满分：60 / 100分）</span>
              </el-form-item>
              <el-form-item label="课程测验合格分：" required>
                <el-input v-model="form.name" class="input-num" placeholder="请输入" />
                <span class="f-ml5">分</span>
                <span class="f-cb">（及格 / 满分：60 / 100分）</span>
              </el-form-item>
              <el-form-item label=" ">
                <span class="f-co">注：请设置合格成绩介于合格分和满分之间。</span>
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button>取消</el-button>
                <el-button type="primary">确认</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <!--一键学习-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20 f-mb20">一键学习</el-button>
        <el-drawer title="一键学习" :visible.sync="dialog3" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              课程存在测验考核要求，是否确认一键学习？一键学习会同步合格课后测验。
            </el-alert>
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt40">
              <el-form-item label="设定测验合格分：" required>
                <el-input v-model="form.name" class="input-num" placeholder="请输入" />
                <span class="f-ml5">分</span>
                <span class="f-cb">（及格 / 满分：60 / 100分）</span>
              </el-form-item>
              <el-form-item label=" ">
                <span class="f-co">注：请设置合格成绩介于合格分和满分之间。</span>
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button>取消</el-button>
                <el-button type="primary">确认</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <!--测验详情-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20 f-mb20">测验详情</el-button>
        <el-drawer title="测验详情" :visible.sync="dialog4" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="测验提交时间" min-width="300">
                <template>2021.02.0 14:00:00</template>
              </el-table-column>
              <el-table-column label="测验成绩" min-width="180">
                <template>60分</template>
              </el-table-column>
              <el-table-column label="是否合格" min-width="120">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="danger" class="badge-status">未合格</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">合格</el-badge>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-drawer>
        <!--更换班级-->
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">更换班级</el-button>
        <el-drawer
          title="更换班级"
          :visible.sync="dialog5"
          :direction="direction"
          size="1000px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              <span class="f-c9">当前更换的班级为：</span>
              <span class="f-fb">2020年人社行业公需科目培训班</span>
              <span class="f-c9"
                >，当前订单来源于专题【读取专题名称】，可更换班级在本专题范围内，请在以下列表中选择更换的目标班级！</span
              >
            </el-alert>
            <!--分销推广订单提示语-->
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              <span class="f-c9">当前更换的班级为：</span>
              <span class="f-fb">2020年人社行业公需科目培训班</span>
              <span class="f-c9"
                >，当前订单属于分销推广订单，可更换班级为分销商推广的班级且班级价格一致，请在以下列表中选择更换的目标班级！</span
              >
            </el-alert>
            <el-row :gutter="16" class="m-query f-mt20">
              <el-form :inline="true" label-width="auto">
                <el-col :span="8">
                  <el-form-item label="班级名称">
                    <el-input v-model="input" clearable placeholder="请输入班级名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="年度">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="地区">
                    <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="行业">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="培训类别">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="培训专业">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                    <el-button type="text">展开<i class="el-icon-arrow-down el-icon--right"></i></el-button>
                    <!--<el-button type="text">收起<i class="el-icon-arrow-up el-icon&#45;&#45;right"></i></el-button>-->
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="可更换的目标培训班" min-width="300">
                <template>
                  <p><el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag>培训班名称支持两行</p>
                  <p class="f-c9 f-f13">人社行业 / 2020年 / 公需科目</p>
                </template>
              </el-table-column>
              <el-table-column label="单价(元)" min-width="140" align="right">
                <template>13.00</template>
              </el-table-column>
              <el-table-column label="学时" min-width="100" align="center">
                <template>13</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">确认换班</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
        </el-drawer>
        <!--补要发票-->
        <el-button @click="dialog6 = true" type="primary" class="f-mr20 f-mb20">补要发票</el-button>
        <el-drawer title="补要发票" :visible.sync="dialog6" :direction="direction" size="900px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="170px" class="m-form f-mt10">
              <el-form-item label="发票类型：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="增值税电子普通发票"></el-radio>
                  <el-radio label="增值税专用发票"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="发票抬头：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="个人"></el-radio>
                  <el-radio label="单位"></el-radio>
                </el-radio-group>
                <div class="f-mt10">
                  <!--选择单位时改为 请输入单位名称-->
                  <el-input v-model="form.name" clearable placeholder="请输入个人名字" class="form-l" />
                </div>
              </el-form-item>
              <el-form-item label="统一社会信用代码：" required>
                <el-input v-model="form.name" clearable placeholder="请输入18位统一社会信用代码" class="form-l" />
              </el-form-item>
              <el-form-item label=" " class="is-text">
                <span class="f-co">注：开企业抬头发票须填写统一社会信用代码，以免影响报销。</span>
              </el-form-item>
              <div class="bg-gray f-plr20 f-mb20 f-pb10">
                <div class="f-pt5 f-pb10">
                  <el-divider content-position="left">
                    <span class="f-cr">* 以下内容请根据需要填写，请全部填写或者全部不填写。</span>
                  </el-divider>
                </div>
                <el-form-item label="开户银行：">
                  <el-input v-model="form.name" clearable placeholder="请输入开户银行" class="form-l" />
                </el-form-item>
                <el-form-item label="开户帐号：">
                  <el-input v-model="form.name" clearable placeholder="请输入开户帐号" class="form-l" />
                </el-form-item>
                <el-form-item label="注册电话：">
                  <el-input v-model="form.name" clearable placeholder="请输入单位注册电话" class="form-m" />
                </el-form-item>
                <el-form-item label="注册地址：">
                  <el-input v-model="form.name" clearable placeholder="请输入单位注册地址" />
                </el-form-item>
              </div>
              <el-form-item label="手机号：" required>
                <el-input v-model="form.name" clearable placeholder="请输入11位手机号码" class="form-l" />
              </el-form-item>
              <el-form-item label="电子邮箱：" required>
                <el-input v-model="form.name" clearable placeholder="请输入电子邮箱" class="form-l" />
              </el-form-item>
            </el-form>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">保存发票信息</el-button>
          </div>
        </el-drawer>
        <!--补要发票（专票）-->
        <el-button @click="dialog7 = true" type="primary" class="f-mr20 f-mb20">补要发票（专票）</el-button>
        <el-drawer title="补要发票" :visible.sync="dialog7" :direction="direction" size="900px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="170px" class="m-form f-mt10">
              <el-form-item label="发票类型：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="增值税电子普通发票"></el-radio>
                  <el-radio label="增值税专用发票"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="发票抬头：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="个人"></el-radio>
                  <el-radio label="单位"></el-radio>
                </el-radio-group>
                <div class="f-mt10">
                  <!--选择单位时改为 请输入单位名称-->
                  <el-input v-model="form.name" clearable placeholder="请输入个人名字" class="form-l" />
                </div>
              </el-form-item>
              <el-form-item label="统一社会信用代码：" required>
                <el-input v-model="form.name" clearable placeholder="请输入18位统一社会信用代码" class="form-l" />
              </el-form-item>
              <el-form-item label=" " class="is-text">
                <span class="f-co">注：开企业抬头发票须填写统一社会信用代码，以免影响报销。</span>
              </el-form-item>
              <el-form-item label="开户银行：" required>
                <el-input v-model="form.name" clearable placeholder="请输入开户银行" class="form-l" />
              </el-form-item>
              <el-form-item label="开户帐号：" required>
                <el-input v-model="form.name" clearable placeholder="请输入开户帐号" class="form-l" />
              </el-form-item>
              <el-form-item label="注册电话：" required>
                <el-input v-model="form.name" clearable placeholder="请输入单位注册电话" class="form-m" />
              </el-form-item>
              <el-form-item label="注册地址：" required>
                <el-input v-model="form.name" clearable placeholder="请输入单位注册地址" />
              </el-form-item>
              <el-form-item label="统一社会信用代码证：">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :auto-upload="false"
                  class="m-pic-upload proportion-pic is-small"
                >
                  <div slot="default" class="upload-placeholder">
                    <i class="el-icon-plus"></i>
                    <p class="txt">上传图片</p>
                  </div>
                  <div slot="file" slot-scope="{ file }" class="img-file">
                    <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                    <div class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </div>
                  </div>
                </el-upload>
                <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                  <img :src="dialogImageUrl" alt="" />
                </el-dialog>
              </el-form-item>
              <el-form-item label="开户许可证：">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  :auto-upload="false"
                  class="m-pic-upload proportion-pic is-small"
                >
                  <div slot="default" class="upload-placeholder">
                    <i class="el-icon-plus"></i>
                    <p class="txt">上传图片</p>
                  </div>
                  <div slot="file" slot-scope="{ file }" class="img-file">
                    <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                    <div class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </div>
                  </div>
                </el-upload>
                <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                  <img :src="dialogImageUrl" alt="" />
                </el-dialog>
              </el-form-item>
            </el-form>
            <div class="m-sub-tit">
              <span class="tit-txt">配送信息</span>
            </div>
            <el-form ref="form" :model="form" label-width="150px" class="m-form f-mt10">
              <el-form-item label="配送方式：">
                <el-radio-group v-model="form.resource">
                  <el-radio label="快递"></el-radio>
                  <el-radio label="自取"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="收货人：" required>
                <el-input v-model="form.name" clearable placeholder="请输入收货人" class="form-s" />
              </el-form-item>
              <el-form-item label="手机号：" required>
                <el-input v-model="form.name" clearable placeholder="请输入手机号" class="form-m" />
              </el-form-item>
              <el-form-item label="所在地区：" required>
                <el-cascader clearable :options="cascader" placeholder="请选择所在地区" class="form-m" />
              </el-form-item>
              <el-form-item label="详细地址：" required>
                <el-input v-model="form.name" clearable placeholder="请输入详细地址" />
              </el-form-item>
              <el-form-item label="邮编：">
                <el-input v-model="form.name" clearable placeholder="请输入邮编" class="form-s" />
              </el-form-item>
              <el-form-item label="姓名：" required>
                <el-input v-model="form.name" clearable placeholder="请输入姓名" class="form-s" />
              </el-form-item>
              <el-form-item label="手机号：" required>
                <el-input v-model="form.name" clearable placeholder="请输入手机号" class="form-m" />
              </el-form-item>
              <el-form-item label="身份证号：" required>
                <el-input v-model="form.name" clearable placeholder="请输入身份证号" class="form-m" />
              </el-form-item>
              <el-form-item label="自取地点：" class="is-text">
                福建省福州市鼓楼区工业路611号
              </el-form-item>
            </el-form>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">保存发票信息</el-button>
          </div>
        </el-drawer>
        <!--查看换班详情-->
        <el-button @click="dialog8 = true" type="primary" class="f-mr20">查看换班详情</el-button>
        <el-drawer title="查看详情" :visible.sync="dialog8" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd f-mt20 f-mlr40">
            <el-timeline>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">发起换班</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">退班处理中</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">退班失败</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">申请发货</span></p>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-drawer>
        <p>发票信息 相关弹窗详见 2209_发票管理_弹窗.vue</p>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <!--删除基准照-->
        <el-button @click="dialog9 = true" type="primary" class="f-mr20 f-mb20">删除基准照提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog9" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">删除学员基准照后，学员需重新采集基准照，确认要删除？</span>
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确 定</el-button>
          </div>
        </el-dialog>
        <!--重置密码-->
        <el-button @click="dialog10 = true" type="primary" class="f-mr20 f-mb20">重置密码提示</el-button>
        <el-dialog title="重置密码" :visible.sync="dialog10" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt"
              >你正在重置<span class="f-ci">【蓝水平】</span>学员的密码为：<span class="f-ci">abc123</span
              >，重置后密码会同步生效，是否确认重置?</span
            >
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确 定</el-button>
          </div>
        </el-dialog>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <!--系统正在推送课程-->
        <el-button @click="dialog10 = true" type="primary" class="f-mr20 f-mb20">系统正在推送课程</el-button>
        <el-dialog title="提示" :visible.sync="dialog10" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">系统正在推送课程，请稍后重试。</span>
          </div>
          <div slot="footer">
            <el-button type="primary">确 定</el-button>
          </div>
        </el-dialog>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <!--提示-->
        <el-button @click="dialog15 = true" type="primary" class="f-mr20 f-mb20">面网授暂不支持一键合格</el-button>
        <el-dialog title="提示" :visible.sync="dialog15" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">面网授暂不支持一键合格！</span>
          </div>
          <div slot="footer">
            <el-button type="primary">我知道了</el-button>
          </div>
        </el-dialog>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">学习情况</span>
        </div>
        <!--查看考勤详情-->
        <el-button @click="dialog001 = true" type="primary" class="f-mr20 f-mb20">查看考勤详情</el-button>
        <el-drawer
          title="查看考勤详情"
          :visible.sync="dialog001"
          :direction="direction"
          size="1000px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" :closable="false" class="m-alert">
              <p><b>考勤规则</b></p>
              <p>1、开启签到：每半天签到一次，第一节课开始授课前X分钟和开始授课后Y分钟之间，需签到1次。</p>
              <p>2、开启答退：每半天签退一次，第一节课开始授课前X分钟和开始授课后Y分钟之间，需签退1次。</p>
            </el-alert>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="课程名称" min-width="240">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    课程名称课程名称课程名称课程名称课程名称课程名称
                  </div>
                  <div v-else>
                    课程名称课程名称课程名称课程名称课程名称课程名称
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="授课时间" min-width="180" align="center">
                <template>
                  <p><el-tag type="info" size="mini">开始</el-tag>xxxx-xx-xx</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>xxxx-xx-xx</p>
                </template>
              </el-table-column>
              <el-table-column label="要求签到/签退情况" min-width="150" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    要求签到/不要求签退
                  </div>
                  <div v-else>
                    要求签到/不要求签退
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="签到时间" min-width="120" align="center">
                <template>
                  xxxx-xx-xx
                </template>
              </el-table-column>
              <el-table-column label="签退时间" min-width="120" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    xxxx-xx-xx
                  </div>
                  <div v-else>
                    <span class="f-ci">未签退</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">返回</el-button>
          </div>
        </el-drawer>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">售后信息</span>
        </div>
        <!--更换班级-->
        <el-button @click="dialog0001 = true" type="primary" class="f-mr20">更换班级</el-button>
        <el-drawer
          title="更换班级"
          :visible.sync="dialog0001"
          :direction="direction"
          size="1600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
              <span class="f-c9">当前更换的班级为：</span>
              <span class="f-fb">2024年建设专业课培训</span>
              <span class="f-c9">，请先在以下列表中选择更换的目标班级后，再选择目标期别：</span>
            </el-alert>
            <el-row :gutter="15" class="is-height">
              <el-col :span="12">
                <el-card shadow="never" class="m-card is-header-sticky f-mb15">
                  <div slot="header" class="is-sticky">
                    <span class="tit-txt">选择班级</span>
                  </div>
                  <div class="f-plr20 f-pb20">
                    <!--第一步：选班级-->
                    <el-row :gutter="16" class="m-query f-mt20">
                      <el-form :inline="true" label-width="auto">
                        <el-col :span="8">
                          <el-form-item label="班级">
                            <el-input placeholder="请输入班级名称"></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="年度">
                            <el-select v-model="select" clearable filterable placeholder="请选择">
                              <el-option value="选项1"></el-option>
                              <el-option value="选项2"></el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="地区">
                            <el-select v-model="select" clearable filterable placeholder="请选择地区">
                              <el-option value="选项1"></el-option>
                              <el-option value="选项2"></el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="行业">
                            <el-select v-model="select" clearable filterable placeholder="请选择行业">
                              <el-option value="选项1"></el-option>
                              <el-option value="选项2"></el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8" class="f-fr">
                          <el-form-item class="f-tr">
                            <el-button type="primary">查询</el-button>
                            <el-button>重置</el-button>
                          </el-form-item>
                        </el-col>
                      </el-form>
                    </el-row>
                    <!--表格-->
                    <el-table stripe :data="tableData" highlight-current-row max-height="500px" class="m-table">
                      <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                      <el-table-column label="可更换的目标培训班" min-width="300">
                        <template>
                          <p><el-tag type="primary" effect="dark" size="mini">方案类型</el-tag>培训班名称支持两行</p>
                          <p class="f-c9 f-f13">年度 / 科目类型 / 培训类别 / 培训专业 / 主项增项</p>
                        </template>
                      </el-table-column>
                      <el-table-column label="单价(元)" min-width="140" align="right">
                        <template>13.00</template>
                      </el-table-column>
                      <el-table-column label="学时" min-width="100" align="center">
                        <template>13</template>
                      </el-table-column>
                      <el-table-column label="操作" width="100" align="center" fixed="right">
                        <template slot-scope="scope">
                          <div v-if="scope.$index === 0">
                            <el-button type="text">已选择</el-button>
                          </div>
                          <div v-else>
                            <el-button type="text">选择</el-button>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <!--分页-->
                    <el-pagination
                      background
                      class="f-mt15 f-tr"
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="currentPage4"
                      :page-sizes="[100, 200, 300, 400]"
                      :page-size="100"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="400"
                    >
                    </el-pagination>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card shadow="never" class="m-card is-header-sticky f-mb15">
                  <div slot="header" class="">
                    <span class="tit-txt">选择期别</span>
                  </div>
                  <div class="f-plr20 f-pt20">
                    <!--第二步：选期别-->
                    <el-row :gutter="16" class="m-query">
                      <el-form :inline="true" label-width="auto">
                        <el-col :span="24">
                          <el-col :span="16">
                            <el-form-item label="期别名称">
                              <el-input placeholder="请输入期别名称"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-col>
                        <el-col :span="24">
                          <el-col :span="16">
                            <el-form-item label="报到时间">
                              <el-date-picker
                                v-model="value1"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                              ></el-date-picker>
                            </el-form-item>
                          </el-col>
                          <el-col :span="8" class="f-fr">
                            <el-form-item class="f-tr">
                              <el-button type="primary">查询</el-button>
                            </el-form-item>
                          </el-col>
                        </el-col>
                      </el-form>
                    </el-row>
                    <!--表格-->
                    <el-table stripe :data="tableData" max-height="500px" class="m-table">
                      <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                      <el-table-column label="可更换的期别" min-width="240">
                        <template> <el-tag type="primary" effect="dark" size="mini">编号</el-tag>期别名称 </template>
                      </el-table-column>
                      <el-table-column label="报到时间" min-width="140" align="right">
                        <template>xxxx-xx-xx</template>
                      </el-table-column>
                      <el-table-column label="剩余可报名人数" min-width="160" align="center">
                        <template>13</template>
                      </el-table-column>
                      <el-table-column label="操作" width="100" align="center" fixed="right">
                        <template slot-scope="scope">
                          <div v-if="scope.$index === 0">
                            <el-button type="text">确认更换</el-button>
                          </div>
                          <div v-else>
                            <el-button type="text">确认更换</el-button>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <!--分页-->
                    <el-pagination
                      background
                      class="f-mt15 f-tr"
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="currentPage4"
                      :page-sizes="[100, 200, 300, 400]"
                      :page-size="100"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="400"
                    >
                    </el-pagination>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--更换期别-->
        <el-button @click="dialog0002 = true" type="primary" class="f-mr20">更换期别</el-button>
        <el-drawer
          title="更换期别"
          :visible.sync="dialog0002"
          :direction="direction"
          size="1000px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              <span class="f-c9">当前待更换的期别为：</span>
              <span class="f-fb">2024年建设专业课培训（第一期）</span>
              <span class="f-c9">，请先在以下列表中选择更换的目标期别：</span>
            </el-alert>
            <el-row :gutter="16" class="m-query f-mt20">
              <el-form :inline="true" label-width="auto">
                <el-col :span="8">
                  <el-form-item label="期别名称">
                    <el-input placeholder="请输入期别名称"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="报到时间">
                    <el-date-picker
                      v-model="value1"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="可更换的期别" min-width="300">
                <template> <el-tag type="primary" effect="dark" size="mini">编号</el-tag>期别名称 </template>
              </el-table-column>
              <el-table-column label="报到时间" min-width="140" align="right">
                <template>xxxx-xx-xx</template>
              </el-table-column>
              <el-table-column label="剩余可报名人数" min-width="100" align="center">
                <template>13</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text">确认换期</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text">确认换期</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
        </el-drawer>
        <!--查看换班详情-->
        <el-button @click="dialog0003 = true" type="primary" class="f-mr20">查看详情（换班）</el-button>
        <el-drawer
          title="查看详情"
          :visible.sync="dialog0003"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd f-mt20 f-mlr40">
            <el-timeline>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">发起换班</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">退班处理中</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">退班失败</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">申请发货</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">发货处理中</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">换班成功</span></p>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-drawer>
        <!--查看换期详情-->
        <el-button @click="dialog0004 = true" type="primary" class="f-mr20">查看详情（换期）</el-button>
        <el-drawer
          title="查看详情"
          :visible.sync="dialog0004"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd f-mt20 f-mlr40">
            <el-timeline>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">发起换期</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">换期处理中</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">换期失败</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">发起换期</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">换期成功</span></p>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-drawer>
        <!--换班提示-->
        <el-button @click="dialog0005 = true" type="primary" class="f-mr20 f-mb20">换班提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog0005" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">当前选择的目标班级：xxxxxxx，目标期别：xxxxxxxxxx，是否确认继续更换？</span>
          </div>
          <div slot="footer">
            <el-button type="">取消</el-button>
            <el-button type="primary">确 定</el-button>
          </div>
        </el-dialog>
        <!--换期提示-->
        <el-button @click="dialog0006 = true" type="primary" class="f-mr20 f-mb20">换期提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog0006" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">当前选择的目标期别：xxxxxxxxxx，是否确认继续更换？</span>
          </div>
          <div slot="footer">
            <el-button type="">取消</el-button>
            <el-button type="primary">确 定</el-button>
          </div>
        </el-dialog>
        <!--换期失败提示-->
        <el-button @click="dialog0006 = true" type="primary" class="f-mr20 f-mb20">换期失败提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog0006" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">换期失败，当前学员已换至其他期别，请刷新页面后重新选择。</span>
          </div>
          <div slot="footer">
            <el-button type="">取消</el-button>
            <el-button type="primary">确 定</el-button>
          </div>
        </el-dialog>
        <!--更换班级-->
        <el-button @click="dialog0007 = true" type="primary" class="f-mr20">更换班级（面网授）</el-button>
        <el-drawer
          title="更换班级"
          :visible.sync="dialog0007"
          :direction="direction"
          size="1600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
              <span class="f-c9">当前更换的班级为：</span>
              <span class="f-fb">2024年建设专业课培训</span>
              <span class="f-c9">，请先在以下列表中选择更换的目标班级后，再选择目标期别：</span>
            </el-alert>
            <el-row :gutter="15" class="is-height">
              <el-col :span="12">
                <el-card shadow="never" class="m-card is-header-sticky f-mb15">
                  <div slot="header" class="is-sticky">
                    <span class="tit-txt">选择班级</span>
                  </div>
                  <div class="f-plr20 f-pb20">
                    <!--第一步：选班级-->
                    <el-row :gutter="16" class="m-query f-mt20">
                      <el-form :inline="true" label-width="auto">
                        <el-col :span="8">
                          <el-form-item label="班级">
                            <el-input placeholder="请输入班级名称"></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="年度">
                            <el-select v-model="select" clearable filterable placeholder="请选择">
                              <el-option value="选项1"></el-option>
                              <el-option value="选项2"></el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="地区">
                            <el-select v-model="select" clearable filterable placeholder="请选择地区">
                              <el-option value="选项1"></el-option>
                              <el-option value="选项2"></el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="行业">
                            <el-select v-model="select" clearable filterable placeholder="请选择行业">
                              <el-option value="选项1"></el-option>
                              <el-option value="选项2"></el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8" class="f-fr">
                          <el-form-item class="f-tr">
                            <el-button type="primary">查询</el-button>
                            <el-button>重置</el-button>
                          </el-form-item>
                        </el-col>
                      </el-form>
                    </el-row>
                    <!--表格-->
                    <el-table stripe :data="tableData" highlight-current-row max-height="500px" class="m-table">
                      <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                      <el-table-column label="可更换的目标培训班" min-width="300">
                        <template>
                          <p>
                            <el-tag type="primary" effect="dark" size="mini">方案类型-网授选课方式</el-tag
                            >培训班名称支持两行
                          </p>
                          <p class="f-c9 f-f13">年度 / 科目类型 / 培训类别 / 培训专业 / 主项增项</p>
                        </template>
                      </el-table-column>
                      <el-table-column label="单价(元)" min-width="140" align="right">
                        <template>13.00</template>
                      </el-table-column>
                      <el-table-column label="学时" min-width="100" align="center">
                        <template>13</template>
                      </el-table-column>
                      <el-table-column label="操作" width="100" align="center" fixed="right">
                        <template slot-scope="scope">
                          <div v-if="scope.$index === 0">
                            <el-button type="text">已选择</el-button>
                          </div>
                          <div v-else>
                            <el-button type="text">选择</el-button>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <!--分页-->
                    <el-pagination
                      background
                      class="f-mt15 f-tr"
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="currentPage4"
                      :page-sizes="[100, 200, 300, 400]"
                      :page-size="100"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="400"
                    >
                    </el-pagination>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card shadow="never" class="m-card is-header-sticky f-mb15">
                  <div slot="header" class="">
                    <span class="tit-txt">选择期别</span>
                  </div>
                  <div class="f-plr20 f-pt20">
                    <!--第二步：选期别-->
                    <el-row :gutter="16" class="m-query">
                      <el-form :inline="true" label-width="auto">
                        <el-col :span="24">
                          <el-col :span="16">
                            <el-form-item label="期别名称">
                              <el-input placeholder="请输入期别名称"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-col>
                        <el-col :span="24">
                          <el-col :span="16">
                            <el-form-item label="报到时间">
                              <el-date-picker
                                v-model="value1"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                              ></el-date-picker>
                            </el-form-item>
                          </el-col>
                          <el-col :span="8" class="f-fr">
                            <el-form-item class="f-tr">
                              <el-button type="primary">查询</el-button>
                            </el-form-item>
                          </el-col>
                        </el-col>
                      </el-form>
                    </el-row>
                    <!--表格-->
                    <el-table stripe :data="tableData" max-height="500px" class="m-table">
                      <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                      <el-table-column label="可更换的期别" min-width="240">
                        <template> <el-tag type="primary" effect="dark" size="mini">编号</el-tag>期别名称 </template>
                      </el-table-column>
                      <el-table-column label="报到时间" min-width="140" align="right">
                        <template>xxxx-xx-xx</template>
                      </el-table-column>
                      <el-table-column label="剩余可报名人数" min-width="160" align="center">
                        <template>13</template>
                      </el-table-column>
                      <el-table-column label="操作" width="100" align="center" fixed="right">
                        <template slot-scope="scope">
                          <div v-if="scope.$index === 0">
                            <el-button type="text">确认更换</el-button>
                          </div>
                          <div v-else>
                            <el-button type="text">确认更换</el-button>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <!--分页-->
                    <el-pagination
                      background
                      class="f-mt15 f-tr"
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="currentPage4"
                      :page-sizes="[100, 200, 300, 400]"
                      :page-size="100"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="400"
                    >
                    </el-pagination>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 1,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        dialog11: false,
        dialog12: false,
        dialog13: false,
        dialog14: false,
        dialog15: false,
        dialog01: false,
        dialog02: false,
        dialog03: false,
        dialog04: false,
        dialog05: false,
        dialog001: false,
        dialog002: false,
        dialog003: false,
        dialog004: false,
        dialog005: false,
        dialog0001: false,
        dialog0002: false,
        dialog0003: false,
        dialog0004: false,
        dialog0005: false,
        dialog0006: false,
        dialog0007: false,
        dialog0008: false,
        dialog0009: false,
        dialog0010: false,
        dialog0011: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
