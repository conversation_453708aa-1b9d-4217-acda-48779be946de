schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	Transform(servicerPortalDto:ServicerPortalDto):DistributorPortalInfoResponse
	"""校验商品是否有效
		@return
	"""
	checkDistributorCommodityRelationValid(distributorCommodityId:String):CheckResponse @optionalLogin
	"""根据分销商商品id获取分销商商品详情"""
	getDistributorCommodityInDistributor(discountApplyId:String,distributorCommodityId:String):PortalDistributorCommodityResponse @optionalLogin
	"""获取指定分销商下的分销商品属性列表"""
	getDistributorCommoditySkuPropertyCollectionInDistributor(request:DistributorCommoditySkuPropertyCollectionRequest):DistributorCommoditySkuPropertyCollectionResponse @optionalLogin
	"""用户域-获取当前分销商指定门户的推广门户信息
		@param portalId 门户id
		@return 门户信息
	"""
	getTrainingInstitutionPortalInfoByPortalId(portalId:String):DistributorPortalInfoResponse @optionalLogin
	"""映射属性值名称
		@param commodityPropertyDtos 商品sku属性
		@param servicerId 商品网校iD
		@return
	"""
	mapPropertyList(commodityPropertyDtos:[CommodityPropertyDto],servicerId:String):[PropertyResponse]
	"""分页获取指定分销商下的分销商品列表（门户）"""
	pageDistributorCommodityInDistributor(page:Page,request:PortalDistributorCommodityRequest,sortRequests:[SaleCommoditySortRequest]):PortalDistributorCommodityResponsePage @page(for:"PortalDistributorCommodityResponse") @optionalLogin
	"""分页获取指定分销商下的分销期别商品列表（门户）"""
	pagePortalDistributorIssueCommodityInDistributor(page:Page,request:PortalDistributorIssueCommodityInDistributorRequest,sortRequests:[SaleCommoditySortRequest]):PortalDistributorIssueCommodityInDistributorResponsePage @page(for:"PortalDistributorIssueCommodityInDistributorResponse") @optionalLogin
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input PortalBannerDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.ServicePortal.PortalBannerDto") {
	id:String
	name:String
	path:String
	link:String
	sort:Int
	enable:Boolean
	createdTime:DateTime
}
input PortalFriendLinkDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.ServicePortal.PortalFriendLinkDto") {
	id:String
	title:String
	picture:String
	friendLinkType:Int
	link:String
	sort:Int
	createdTime:DateTime
}
input PortalMenuDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.ServicePortal.PortalMenuDto") {
	id:String
	name:String
	displayName:String
	parentId:String
	type:Int
	sourceType:Int
	link:String
	code:String
	referenceId:String
	enable:Boolean
	sort:Int
	createdTime:DateTime
	updatedTime:DateTime
}
input PortalPlateDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.ServicePortal.PortalPlateDto") {
	id:String
	name:String
	displayName:String
	parentId:String
	type:Int
	sourceType:Int
	link:String
	code:String
	referenceId:String
	allowChildren:Int
	sort:Int
	createdTime:DateTime
	updatedTime:DateTime
}
input ServicerPortalDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.ServicePortal.ServicerPortalDto") {
	id:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	servicerId:String
	belongServiceId:String
	category:Int
	identifier:String
	portalType:Int
	title:String
	shortName:String
	logo:String
	icon:String
	themeColor:String
	mobileQRcode:String
	CSPhonePicture:String
	CSPhone:String
	CSCallTime:String
	CSOnlineCodeId:String
	CSWechat:String
	trainingFlowPicture:String
	footContentId:String
	slogan:String
	domainName:String
	domainShortCode:String
	contentId:String
	isProvideServiceAccount:Int
	isPublished:Int
	publishedTime:DateTime
	unpublishedTime:DateTime
	cnzz:String
	dirName:String
	domainNameType:Int
	plates:[PortalPlateDto]
	friendLinks:[PortalFriendLinkDto]
	banner:[PortalBannerDto]
	menu:[PortalMenuDto]
	createdTime:DateTime
	updatedTime:DateTime
	isDeleted:Int
	deletedTime:DateTime
	recordCreatedTime:DateTime
	recordUpdatedTime:DateTime
}
input CommodityPropertyDto @type(value:"com.fjhb.ms.datapedestal.kernel.service.dto.nested.CommodityPropertyDto") {
	id:String
	commodityId:String
	propertyKey:String
	propertyValue:String
}
"""<AUTHOR> linq
	@date : 2025-04-15 18:39
	@description :
"""
input PortalDistributorIssueCommodityInDistributorRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.portaldistributorcommodity.PortalDistributorIssueCommodityInDistributorRequest") {
	"""原始商品id"""
	rootCommodityIdList:[String]
}
"""商品sku属性查询条件"""
input PropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.common.PropertyRequest") {
	"""商品skuKey"""
	propertyKey:String
	"""商品skuValue"""
	propertyValue:String
}
"""<AUTHOR> linq
	@date : 2024-05-06 14:13
	@description：分销商品sku属性请求入参
"""
input DistributorCommoditySkuPropertyCollectionRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.DistributorCommoditySkuPropertyCollectionRequest") {
	"""门户id"""
	portalId:String
	"""网校id"""
	onlineSchoolId:String
	"""商品名称"""
	distributorCommodityName:String
	"""商品属性key-value集合"""
	propertyList:[PropertyRequest]
	"""商品销售地区（左右模糊查询）"""
	areaPath:String
}
input PortalDistributorCommodityRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.PortalDistributorCommodityRequest") {
	"""门户id"""
	portalId:String
	"""商品名称查询"""
	distributorCommodityName:String
	"""商品属性key-value集合"""
	propertyList:[PropertyRequest]
	"""定价方案-商品销售地区（右模糊查询）"""
	areaPath:String
	"""定价方案-商品销售地区（右模糊查询）"""
	areaPathT:String
	"""商品id集合
		(该id会无视门户展示的限制条件查询回所有商品)
	"""
	commodityIds:[String]
}
input SaleCommoditySortRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.sort.SaleCommoditySortRequest") {
	"""排序字段"""
	field:SaleCommoditySortEnum
	"""正序或倒序"""
	policy:Direction
}
enum Direction @type(value:"com.fjhb.commons.dao.page.SortPolicy$Direction") {
	ASC
	DESC
}
enum SaleCommoditySortEnum @type(value:"com.fjhb.ms.datapedestal.kernel.enums.SaleCommoditySortEnum") {
	SALE_CHANNEL_SORT
	ISSUE_NUM_SORT
	ISSUE_SIGN_UP_TIME_SORT
}
"""<AUTHOR> linq
	@date : 2025-04-15 17:50
	@description : 指定分销商下的门户分销期别商品列表response返回值
"""
type PortalDistributorIssueCommodityInDistributorResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.portaldistributorcommodity.PortalDistributorIssueCommodityInDistributorResponse") {
	"""期别信息"""
	issueResourceInfo:NestDistributorIssueResourceInfoResponse
	"""已报名人数"""
	registeredNumber:Int!
	"""最大报名人数"""
	maxRegisterNumber:Int!
	"""剩余报名人数"""
	remainingRegisterNumber:Int!
}
"""<AUTHOR> linq
	@date : 2025-04-15 18:29
	@description : 指定分销商下的门户分销期别商品列表response返回值 - 冗余期别资源信息
"""
type NestDistributorIssueResourceInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.portaldistributorcommodity.nests.NestDistributorIssueResourceInfoResponse") {
	"""期别id"""
	issueId:String
	"""期别编号"""
	issueNum:String
	"""期别名称"""
	issueName:String
	"""期别报名开始时间"""
	issueSignUpBeginDate:DateTime
	"""期别报名结束时间"""
	issueSignUpEndDate:DateTime
	"""期别报到开始时间"""
	issueStartReportDate:DateTime
	"""期别报到结束时间"""
	issueEndReportDate:DateTime
	"""期别培训开始时间"""
	issueTrainingBeginDate:DateTime
	"""期别培训结束时间"""
	issueTrainingEndDate:DateTime
}
"""商品sku属性
	<AUTHOR>
"""
type CommoditySkuPropertyResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.CommoditySkuPropertyResponse") {
	"""商品sku属性id"""
	commoditySkuPropertyId:String
	"""商品sku属性名"""
	commoditySkuPropertyName:String
	"""商品sku属性值集合"""
	commoditySkuPropertyValueList:[CommoditySkuPropertyValueResponse]
}
"""商品sku属性值
	<AUTHOR>
"""
type CommoditySkuPropertyValueResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.CommoditySkuPropertyValueResponse") {
	"""商品sku属性值id"""
	commoditySkuPropertyValueId:String
	"""商品sku属性值展示名"""
	commoditySkuPropertyValueName:String
	"""父级code"""
	parentCode:String
}
"""商品sku地区属性节点
	<AUTHOR>
"""
type CommoditySkuRegionPropertyNodeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.CommoditySkuRegionPropertyNodeResponse") {
	"""商品sku地区属性id"""
	commoditySkuRegionId:String
	"""地区编码"""
	code:String
	"""地区名"""
	name:String
	"""地区等级"""
	level:Int
	"""地区路径"""
	codePath:String
	"""父级地区编码"""
	parentCode:String
	"""下级商品sku地区属性"""
	subRegionList:[CommoditySkuRegionPropertyNodeResponse]
}
"""商品sku培训专业属性节点
	<AUTHOR>
"""
type CommoditySkuTrainingProfessionPropertyNodeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.CommoditySkuTrainingProfessionPropertyNodeResponse") {
	"""培训专业ID"""
	id:String
	"""培训专业名称"""
	name:String
	"""培训专业编码"""
	code:String
	"""父级专业ID"""
	parentCode:String
	"""排序序号"""
	sort:Int
	"""是否启用"""
	isAvailable:Int
	"""下级培训专业sku地区属性"""
	subTrainingProfessionList:[CommoditySkuTrainingProfessionPropertyNodeResponse]
}
"""上下文信息"""
type OwnerInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.OwnerInfoResponse") {
	"""平台id"""
	platformId:String
	"""平台版本id"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位id"""
	unitId:String
	"""服务商id"""
	servicer:String
}
type PropertyResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.PropertyResponse") {
	"""商品skuKey"""
	propertyKey:String
	"""商品skuKeyName"""
	propertyKeyName:String
	"""商品skuValue"""
	propertyValue:String
	"""商品skuValue名称"""
	propertyValueName:String
}
type RegionResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.RegionResponse") {
	"""地区ID"""
	regionId:String
	"""地区路径"""
	regionPath:String
	"""省ID"""
	provideId:String
	"""市ID"""
	cityId:String
	"""区县ID"""
	countyId:String
	"""名称"""
	name:String
	"""下级地区"""
	children:[RegionResponse]
}
type RegionTreeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.RegionTreeResponse") {
	"""地区ID"""
	regionId:String
	"""名称"""
	name:String
	"""地区全路径"""
	regionPath:String
	"""子地区"""
	children:[RegionTreeResponse]
	"""子地区数量"""
	childrenCount:Int!
}
"""商品-培训方案资源类型"""
type SchemeResourceInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.common.SchemeResourceInfoResponse") {
	"""培训方案id"""
	schemeId:String
	"""培训方案类型"""
	schemeType:String
	"""培训方案名称"""
	schemeName:String
	"""学时"""
	period:String
	"""报名开始时间"""
	registerBeginDate:DateTime
	"""报名结束时间"""
	registerEndDate:DateTime
	"""上架状态 1-上架 2-下架"""
	sellStatus:Int
	"""培训开始时间"""
	trainingBeginDate:DateTime
	"""培训结束时间"""
	trainingEndDate:DateTime
}
"""@version: 1.0
	@description: 分销商信息
	@author: sugs
	@create: 2023-12-26 20:54
"""
type DistributorResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.DistributorResponse") {
	"""分销商ID"""
	distributorId:String
	"""分销商名称"""
	distributorName:String
	"""分销商类型 1-个人 2-企业"""
	distributorType:Int
	"""分销商简称"""
	distributorShortName:String
	"""分销商全称"""
	distributorFullName:String
	"""统一社会信用代码"""
	unifiedSocialCreditCode:String
	"""身份证号"""
	idCard:String
}
"""分销商推广门户"""
type DistributorPortalInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.DistributorPortalInfoResponse") {
	"""门户id"""
	id:String
	"""所属平台id"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目id"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""业务平台服务商id  (例如分销商id)"""
	servicerId:String
	"""所属服务商id (例如网校id)"""
	belongServiceId:String
	"""0-主门户 1-子门户"""
	category:Int
	"""门户标识"""
	identifier:String
	"""1-web端 2-移动端"""
	portalType:Int
	"""门户标题"""
	title:String
	"""门户简称"""
	shortName:String
	"""门户logo"""
	logo:String
	"""浏览器图标"""
	icon:String
	"""主题颜色"""
	themeColor:String
	"""移动二维码"""
	mobileQRcode:String
	"""移动二维码来源标识 1-系统生成 2-自定义"""
	mobileQRCodeSign:Int
	"""客服电话图片"""
	CSPhonePicture:String
	"""客服电话"""
	CSPhone:String
	"""客服咨询时间"""
	CSCallTime:String
	"""在线客服代码内容id"""
	CSOnlineCodeId:String
	"""培训流程图片"""
	trainingFlowPicture:String
	"""底部内容"""
	footContent:String
	"""宣传口号"""
	slogan:String
	"""域名"""
	domainName:String
	"""域名短码"""
	domainShortCode:String
	"""H5域名(请求Web端门户信息才会使用这个字段 如果请求的是H5端的门户信息，该字段为null)"""
	domainNameH5:String
	"""h5门户id"""
	portalIdh5:String
	"""域名短码h5"""
	domainShortCodeh5:String
	"""门户简介说明内容"""
	content:String
	"""是否提供服务号"""
	isProvideServiceAccount:Boolean
	"""门户状态  是否已发布"""
	isPublished:Boolean
	"""网校状态
		1-正常  2-失效
	"""
	onlineSchoolStatus:Int
	"""网校模式 1-正式实施 2-DEMO"""
	onlineSchoolModes:Int!
	"""是否到期"""
	isExpired:Boolean
	"""前端模板id"""
	portalTemplateId:String
	"""企业客服微信"""
	CSWechat:String
	"""海报二维码"""
	billQRCodes:[QRCode]
	"""门户发布时间"""
	publishedTime:DateTime
	"""门户取消发布时间"""
	unPublishedTime:DateTime
	"""信息"""
	cnzz:String
	"""目录名"""
	dirName:String
	"""域名类型（系统默认域名 1 自有域名 2）"""
	domainNameType:Int
	"""门户板块集合"""
	plates:[PortalPlateResponse]
	"""友情链接集合"""
	friendLinks:[PortalFriendLinkResponse]
	"""轮播图"""
	banner:[PortalBannerResponse]
	"""移动端轮播图"""
	bannersInh5:[PortalBannerResponse]
	"""门户菜单"""
	menu:[PortalMenuResponse]
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""是否删除"""
	isDeleted:Int
	"""删除时间"""
	deletedTime:DateTime
	"""记录创建时间"""
	recordCreatedTime:DateTime
	"""记录更新时间"""
	recordUpdatedTime:DateTime
	"""销售渠道id"""
	channelId:String
	"""当前分销商单位名称"""
	distributorUnitName:String
}
"""门户轮播图"""
type PortalBannerResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.PortalBannerResponse") {
	"""轮播图id"""
	id:String
	"""轮播图名称"""
	name:String
	"""图片路径"""
	path:String
	"""链接地址"""
	link:String
	"""排序"""
	sort:Int
	"""是否启用"""
	enable:Boolean
	"""创建时间"""
	createdTime:DateTime
}
"""门户友情链接"""
type PortalFriendLinkResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.PortalFriendLinkResponse") {
	id:String
	title:String
	picture:String
	friendLinkType:Int
	link:String
	sort:Int
	createdTime:DateTime
}
"""门户菜单"""
type PortalMenuResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.PortalMenuResponse") {
	"""栏目id"""
	id:String
	"""门户栏目名称"""
	name:String
	"""门户栏目展示名称"""
	displayName:String
	"""父栏目id"""
	parentId:String
	"""门户栏目类型 1-菜单 2-资讯"""
	type:Int
	"""来源类型 1-内置 2-用户创建"""
	sourceType:Int
	"""链接"""
	link:String
	"""业务code"""
	code:String
	"""引用id"""
	referenceId:String
	"""是否可用"""
	enable:Boolean
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
}
type PortalPlateResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.PortalPlateResponse") {
	"""板块id"""
	id:String
	"""门户板块名称"""
	name:String
	"""门户板块展示名称"""
	displayName:String
	"""父板块id"""
	parentId:String
	"""门户板块类型 1-默认 2-资讯"""
	type:Int
	"""来源类型 1-内置 2-用户创建"""
	sourceType:Int
	"""链接"""
	link:String
	"""业务code"""
	code:String
	"""引用id"""
	referenceId:String
	"""是否允许存在子级"""
	allowChildren:Int
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
}
"""<AUTHOR> linq
	@date : 2024-09-18 14:04
	@description：二维码
"""
type QRCode @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributor.portal.QRCode") {
	"""二维码图片"""
	mobileQrcode:String
	"""二维码操作提示"""
	qrcodeTip:String
}
"""校验url响应参数"""
type CheckResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.CheckResponse") {
	"""错误码"""
	code:String
	"""true: 有效
		false: 无效
	"""
	valid:Boolean!
}
"""<AUTHOR> linq
	@date : 2024-05-06 14:04
	@description：分销商品sku属性
"""
type DistributorCommoditySkuPropertyCollectionResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.DistributorCommoditySkuPropertyCollectionResponse") {
	"""商品sku属性集合"""
	commoditySkuPropertyList:[CommoditySkuPropertyResponse]
	"""商品sku地区属性树集合"""
	commoditySkuPropertyRegionTreeList:[CommoditySkuRegionPropertyNodeResponse]
	"""商品sku培训专业属性树集合"""
	commoditySkuPropertyTrainingProfessionalTreeList:[CommoditySkuTrainingProfessionPropertyNodeResponse]
	"""商品销售地区属性树集合（定价方案地区）"""
	commodityRegionTreeList:[CommoditySkuRegionPropertyNodeResponse]
}
type PortalDistributorCommodityResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.PortalDistributorCommodityResponse") {
	"""优惠价格  详情口需要返回"""
	discountPrice:BigDecimal
	"""优惠周期约束 | 1-周期 2-长期"""
	discountDateConstraint:Int
	"""优惠开始时间"""
	discountStartTime:DateTime
	"""优惠结束时间"""
	discountEndTime:DateTime
	"""优惠地区"""
	discountRegionList:[RegionResponse]
	"""网校id"""
	schoolId:String
	"""网校名称"""
	schoolName:String
	"""网校名称简称"""
	schoolShortName:String
	"""业主单位全称"""
	unitName:String
	"""业主单位简称"""
	unitShotName:String
	"""网校域名"""
	schoolDomainName:String
	"""分销地区list"""
	regionList:[RegionResponse]
	"""产品分销授权ID  优惠商品下单时取的授权id"""
	productDistributionAuthId:String
	"""定价方案信息"""
	productPricingSchemeResponse:ProductPricingSchemeResponse
	"""用户分销商品拥有信息"""
	userDistributionCommodityOwnResponse:UserDistributionCommodityOwnResponse
	"""期别信息"""
	issueResourceInfoList:[PortalDistributorIssueResourceInfoResponse]
	"""分销商商品id"""
	distributorCommodityId:String
	"""分销商商品销售名称"""
	saleTitle:String
	"""商品-培训方案资源类型"""
	schemeResourceInfo:SchemeResourceInfoResponse
	"""分销商信息"""
	distributor:DistributorResponse
	"""分销商商品sku属性"""
	propertyList:[PropertyResponse]
	"""授权价格类型 1-固定 2-区间"""
	priceType:Int
	"""分销商商品价格"""
	price:BigDecimal
	"""最大价格"""
	maxPrice:BigDecimal
	"""最小价格"""
	minPrice:BigDecimal
	"""网校商品来源系统上下文信息"""
	sourceOwnerInfo:OwnerInfoResponse
	"""原始商品id"""
	commodityId:String
	"""分销商商品图片路径"""
	commodityPicturePath:String
	"""分销状态
		0-开启 1-关闭
		商品的分销开始时间、结束时间作为判断
	"""
	distributionStatus:Int
	"""分销商商品销售时段类型
		1-周期 2-长期
	"""
	saleTimeType:Int
	"""分销商商品销售开始时间"""
	saleStartTime:DateTime
	"""分销商商品销售结束时间"""
	saleEndTime:DateTime
	"""销售状态 1-有效 2-无效"""
	saleStatus:Int
	"""分销商品来源类型
		1-产品分销授权
	"""
	commoditySourceType:Int
	"""分销商品来源id
		1-产品分销授权id
	"""
	commoditySourceId:String
	"""分销商品创建时间"""
	commodityCreatedTime:DateTime
	"""分销商品更新时间"""
	commodityUpdatedTime:DateTime
	"""商品是否门户可见"""
	isShow:Boolean
	"""定价方案启用个数"""
	productPricingSchemeEnableNum:Long
	"""培训方案期别数(面网授班级才有值)"""
	issueNum:Int
	"""分销商品上下架状态"""
	shelveStatus:Int
}
"""<AUTHOR> linq
	@date : 2025-04-15 18:29
	@description : 根据分销商商品id获取分销商商品详情response返回值 - 冗余期别资源信息
"""
type PortalDistributorIssueResourceInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.PortalDistributorIssueResourceInfoResponse") {
	"""期别id"""
	issueId:String
	"""期别编号"""
	issueNum:String
	"""期别名称"""
	issueName:String
	"""期别报名开始时间"""
	issueSignUpBeginDate:DateTime
	"""期别报名结束时间"""
	issueSignUpEndDate:DateTime
	"""期别报到开始时间"""
	issueStartReportDate:DateTime
	"""期别报到结束时间"""
	issueEndReportDate:DateTime
	"""期别培训开始时间"""
	issueTrainingBeginDate:DateTime
	"""期别培训结束时间"""
	issueTrainingEndDate:DateTime
}
"""<AUTHOR> linq
	@date : 2024-09-18 17:21
	@description：定价方案推广门户信息
"""
type PricingPolicyPortalInfo @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.PricingPolicyPortalInfo") {
	"""推广门户标识id"""
	portalIdentify:String
	"""推广门户销售渠道id"""
	saleChannelId:String
	"""推广门户渠道排序"""
	portalSort:Int
	"""门户展示 (0-不展示, 1-展示）"""
	showPortal:Int
	"""门户推广 (0-不推广, 1-推广）"""
	portalPromotion:Int
	"""推广门户定价方案商品id"""
	portalPricingPolicyCommodityId:String
}
type ProductPricingSchemeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.ProductPricingSchemeResponse") {
	"""定价方案ID"""
	productPricingSchemeId:String
	"""产品分销授权ID"""
	productDistributionAuthId:String
	"""授权商品id"""
	productId:String
	"""价格"""
	price:BigDecimal
	"""启用时间"""
	enableTime:DateTime
	"""禁用时间"""
	disEnableTime:DateTime
	"""授权价格类型 1-固定 2-区间"""
	priceType:Int
	"""最大价格"""
	maxPrice:BigDecimal
	"""最小价格"""
	minPrice:BigDecimal
	"""产品销售范围"""
	saleScopeList:[SaleScopeResponse]
	"""定价方案状态 (0-禁用, 1-启用)"""
	status:Int
	"""加入的推广门户信息"""
	pricingPolicyPortalInfoList:[PricingPolicyPortalInfo]
}
type RegionItemResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.RegionItemResponse") {
	"""销售范围ID+地区ID 或 合同ID+地区ID（主键）"""
	id:String
	"""地区来源类型 (1-销售地区, 2-分销地区)"""
	sourceType:Int
	"""地区来源ID（销售范围ID/合同ID）"""
	sourceId:String
	"""地区ID"""
	regionId:String
	"""地区路径"""
	regionPath:String
	"""省ID"""
	provinceId:String
	"""市ID"""
	cityId:String
	"""县ID"""
	countyId:String
	"""层级"""
	level:Int
	"""记录创建时间"""
	recordCreatedTime:DateTime
}
"""市粒度统计"""
type RegionSaleScopeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.RegionSaleScopeResponse") {
	"""地区来源类型 (1-销售地区, 2-分销地区)"""
	sourceType:Int
	"""地区来源ID（销售范围ID/合同ID）"""
	sourceId:String
	"""地区ID"""
	regionId:String
	"""地区名称"""
	regionName:String
	"""统计数量(区数量)"""
	total:Int
}
"""@see SaleScopeDto"""
type SaleScopeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.SaleScopeResponse") {
	"""销售范围ID"""
	saleScopeId:String
	"""地区销售范围类型 (0-同分销商品, 1-自定义)"""
	regionSaleScopeType:Int
	"""地区销售范围关联ID（销售范围ID/合同ID）"""
	regionSaleScopeRelationId:String
	"""地区销售范围关联地区树id"""
	regionSaleRegionTreeId:String
	"""单位销售范围类型 (1-不限, 2-自定义)"""
	unitSaleScopeType:Int
	"""是否启用"""
	enable:Int
	"""备注"""
	remark:String
	"""记录创建时间"""
	recordCreatedTime:DateTime
	"""产品地区销售范围"""
	regionSaleScopeList:[RegionSaleScopeResponse]
	"""地区树结构"""
	regionTreeList:[RegionTreeResponse]
	"""销售地区明细"""
	regionItemList:[RegionItemResponse]
	"""产品单位销售范围"""
	unitSaleScopeList:[UnitSaleScopeResponse]
}
type UnitSaleScopeResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.UnitSaleScopeResponse") {
	"""销售范围ID+统一社会信用代码（主键）"""
	id:String
	"""销售范围ID"""
	saleScopeId:String
	"""单位名称"""
	unitName:String
	"""统一社会信用代码"""
	creditCode:String
	"""记录创建时间"""
	recordCreatedTime:DateTime
}
"""用户分销商品拥有信息"""
type UserDistributionCommodityOwnResponse @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.response.distributorcommodity.nested.UserDistributionCommodityOwnResponse") {
	"""是否拥有分销商下分销商品
		0.不是  1。是
	"""
	isOwn:Int
	"""商品类型
		1-子订单 2-换货单
	"""
	commoditySourceType:Int
	"""商品来源ID"""
	commoditySourcesId:String
	"""商品子订单发货状态.
		0: 等待发货,
		100: 发货中,
		200: 发货成功,
		401: 发货失败
	"""
	subOrderDeliveryStatus:Int
}

scalar List
type PortalDistributorCommodityResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [PortalDistributorCommodityResponse]}
type PortalDistributorIssueCommodityInDistributorResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [PortalDistributorIssueCommodityInDistributorResponse]}
