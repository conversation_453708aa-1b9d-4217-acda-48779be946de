<template>
  <el-card shadow="never" class="m-card">
    <!--条件查询-->
    <hb-search-wrapper expand @reset="resetQueryParam" class="m-query is-border-bottom">
      <el-form-item label="年度">
        <biz-year-select v-model="localSkuProperty.year" placeholder="请选择培训年度"></biz-year-select>
      </el-form-item>
      <el-form-item label="地区">
        <biz-national-region
          v-model="localSkuProperty.region"
          :check-strictly="true"
          placeholder="请选择地区"
        ></biz-national-region>
      </el-form-item>
      <el-form-item label="行业">
        <biz-industry-select
          v-model="localSkuProperty.industry"
          @clearIndustrySelect="handleClearIndustrySelect"
          @industryPropertyId="handleIndustryPropertyId"
          @industryInfos="handleIndustryInfos"
          ref="industrySelect"
        ></biz-industry-select>
      </el-form-item>
      <!-- TODO -->
      <el-form-item label="培训形式">
        <el-select v-model="localSkuProperty.trainingForm" clearable placeholder="请选择培训形式">
          <el-option v-for="item in TrainingMode.list()" :key="item.code" :label="item.desc" :value="item.code">{{
            item.desc
          }}</el-option>
        </el-select>
      </el-form-item>
      <template v-if="localSkuProperty.industry && localSkuProperty.industry === envConfig.teacherIndustryId">
        <el-form-item label="学段">
          <biz-study-period
            v-model="localSkuProperty.studyPeriodId"
            :industry-id="localSkuProperty.industry"
            :industry-property-id="industryPropertyId"
            @updateStudyPeriod="updateStudyPeriod"
            @clearSubject="clearSubject"
          ></biz-study-period>
        </el-form-item>
        <el-form-item label="学科">
          <biz-subject
            v-model="localSkuProperty.subjectId"
            :industry-property-id="industryPropertyId"
            :studyPeriodId="localSkuProperty.studyPeriodId"
            @updateSubject="updateSubject"
          ></biz-subject>
        </el-form-item>
      </template>
      <el-form-item
        label="技术等级"
        v-if="skuVisible.jobLevel && localSkuProperty.industry && localSkuProperty.industry === envConfig.workServiceId"
      >
        <biz-technical-grade-select
          v-model="localSkuProperty.jobLevel"
          :industry-id="localSkuProperty.industry"
          :industry-property-id="industryPropertyId"
        ></biz-technical-grade-select>
      </el-form-item>
      <el-form-item label="科目类型" v-if="skuVisible.subjectType && localSkuProperty.industry">
        <biz-accounttype-select
          v-model="localSkuProperty.subjectType"
          :industry-property-id="industryPropertyId"
          :industryId="localSkuProperty.industry"
        >
        </biz-accounttype-select>
      </el-form-item>
      <el-form-item
        label="培训专业"
        v-if="
          skuVisible.trainingCategory &&
          localSkuProperty.industry &&
          envConfig.societyIndustryId &&
          localSkuProperty.industry === envConfig.societyIndustryId
        "
      >
        <biz-major-cascader
          v-model="localSkuProperty.societyTrainingMajor"
          :industry-property-id="industryPropertyId"
          :industryId="localSkuProperty.industry"
        />
      </el-form-item>
      <el-form-item
        label="培训类别"
        v-if="
          skuVisible.trainingCategory &&
          localSkuProperty.industry &&
          (localSkuProperty.industry === envConfig.constructionIndustryId ||
            localSkuProperty.industry === envConfig.occupationalHealthId)
        "
      >
        <biz-training-category-select
          v-model="localSkuProperty.trainingCategory"
          :industry-property-id="industryPropertyId"
          @updateTrainingCategory="handleUpdateTrainingCategory"
          :industryId="localSkuProperty.industry"
        />
      </el-form-item>
      <el-form-item
        label="培训专业"
        v-if="
          skuVisible.trainingCategory &&
          localSkuProperty.industry &&
          envConfig.constructionIndustryId &&
          localSkuProperty.industry === envConfig.constructionIndustryId
        "
      >
        <biz-major-select
          v-model="localSkuProperty.constructionTrainingMajor"
          :industry-property-id="industryPropertyId"
          :training-category-id="trainingCategoryId"
          :industryId="localSkuProperty.industry"
        />
      </el-form-item>
      <el-form-item
        label="培训对象"
        v-if="
          skuVisible.trainingCategory &&
          localSkuProperty.industry &&
          localSkuProperty.industry === envConfig.occupationalHealthId
        "
      >
        <biz-training-object-select
          v-model="localSkuProperty.trainingObject"
          placeholder="请选择培训对象"
          :industry-property-id="industryPropertyId"
          :industry-id="localSkuProperty.industry"
          @updateTrainingCategory="updateTrainingCategory"
        />
      </el-form-item>
      <el-form-item
        label="岗位类别"
        v-if="
          skuVisible.trainingCategory &&
          localSkuProperty.industry &&
          localSkuProperty.industry === envConfig.occupationalHealthId
        "
      >
        <biz-obj-category-select
          v-model="localSkuProperty.positionCategory"
          placeholder="请选择岗位类别"
          :industry-property-id="industryPropertyId"
          :industryId="localSkuProperty.industry"
          :training-object-id="localSkuProperty.trainingObject"
        />
      </el-form-item>
      <el-form-item
        label="执业类别"
        v-if="
          skuVisible.occupationalCategory && localSkuProperty.industry && localSkuProperty.industry === envConfig.yshyId
        "
      >
        <biz-practicing-category-cascader
          v-model="localSkuProperty.pharmacistIndustry"
          :industryId="localSkuProperty.industry"
        ></biz-practicing-category-cascader>
      </el-form-item>
      <el-form-item label="培训方案类型">
        <biz-scheme-type v-model="schemeTypeInfo"></biz-scheme-type>
      </el-form-item>
      <el-form-item label="培训方案名称">
        <el-input clearable placeholder="请输入培训方案名称" v-model="schemeName" />
      </el-form-item>
      <el-form-item label="销售状态">
        <el-select clearable @clear="onShelveStatus = undefined" placeholder="请选择销售状态" v-model="onShelveStatus">
          <el-option
            v-for="item in onShelveStatusOptions"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <template slot="actions">
        <el-button type="primary" @click="searchBase">查询</el-button>
      </template>
    </hb-search-wrapper>
    <el-alert type="warning" show-icon :closable="false" class="m-alert is-border f-mb15" v-if="placeholder">
      <div class="f-ptb5">{{ placeholder }}</div>
    </el-alert>
    <!--表格-->
    <el-table
      stripe
      :data="trainSchemeList"
      class="m-table"
      v-loading="trainSchemeQuery.loading"
      @sort-change="handleSortChange"
      ref="elTableRef"
    >
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="培训方案名称" min-width="300" fixed="left">
        <template slot-scope="scope">
          <el-popover
            placement="top-start"
            width="270"
            trigger="hover"
            popper-class="m-table-pop is-hover"
            :content="scope.row.commodityBasicData.saleTitle"
          >
            <div class="f-to-three f-mb5" slot="reference">
              <!--需要前端判断，当高度大于60px时，外层添加 is-hover-->
              <span class="txt"> {{ scope.row.commodityBasicData.saleTitle }}</span>
            </div>
          </el-popover>
          <biz-show-scheme-type
            :schemeType="scope.row.schemeType"
            :trainingMode="scope.row.skuValueNameProperty.trainingMode.skuPropertyValueId"
          ></biz-show-scheme-type>
        </template>
      </el-table-column>
      <el-table-column label="报名学时" min-width="100" align="center">
        <template slot-scope="scope">{{ scope.row.period }}</template>
      </el-table-column>
      <el-table-column label="价格" min-width="100" align="right">
        <template slot-scope="scope">{{ scope.row.commodityBasicData.price }}</template>
      </el-table-column>
      <el-table-column label="培训属性" min-width="240">
        <template slot-scope="scope">
          <p class="f-mb5" v-if="getSkuPropertyName(scope.row, 'industry')">
            {{ getSkuPropertyName(scope.row, 'industry') }}
          </p>
          <div class="f-c9 f-f12">
            <p v-if="getSkuPropertyName(scope.row, 'region')">地区：{{ getSkuPropertyName(scope.row, 'region') }}</p>
            <p v-if="getSkuPropertyName(scope.row, 'jobLevel')">
              技术等级：{{ getSkuPropertyName(scope.row, 'jobLevel') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'subjectType')">
              科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
            </p>
            <p v-if="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
              培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'trainingMajor')">
              培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'trainingObject')">
              培训对象：{{ getSkuPropertyName(scope.row, 'trainingObject') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'positionCategory')">
              岗位类别：{{ getSkuPropertyName(scope.row, 'positionCategory') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'year')">培训年度：{{ getSkuPropertyName(scope.row, 'year') }}</p>
            <p v-if="getSkuPropertyName(scope.row, 'learningPhase')">
              学段：{{ getSkuPropertyName(scope.row, 'learningPhase') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'discipline')">
              学科：{{ getSkuPropertyName(scope.row, 'discipline') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'practitionerCategory')">
              执业类别：{{ getSkuPropertyName(scope.row, 'certificatesType') }}-{{
                getSkuPropertyName(scope.row, 'practitionerCategory')
              }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'trainingMode')">
              培训形式：{{ getSkuPropertyName(scope.row, 'trainingMode') }}
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="distributorId" label="授权状态" min-width="100">
        <template v-slot="{ row }">
          <div v-if="row.isAuthorize">
            <el-tag type="success">已授权</el-tag>
          </div>
          <div v-else>
            <el-tag type="info">未授权</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="销售状态" min-width="140">
        <template slot-scope="scope">
          <div v-if="scope.row.onShelve.shelveStatus === 0">
            <el-badge is-dot type="info" class="badge-status">已下架</el-badge>
          </div>
          <div v-if="scope.row.onShelve.shelveStatus === 1">
            <el-badge is-dot type="success" class="badge-status">已上架</el-badge>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="报名起止时间" min-width="220">
        <template slot-scope="scope">
          <p v-if="scope.row.registerBeginDate || scope.row.registerEndDate">
            起始：{{ scope.row.registerBeginDate ? scope.row.registerBeginDate : '--' }}
          </p>
          <p v-if="scope.row.registerBeginDate || scope.row.registerEndDate">
            结束：{{ scope.row.registerEndDate ? scope.row.registerEndDate : '--' }}
          </p>
          <p v-if="!scope.row.registerBeginDate && !scope.row.registerEndDate">暂不开启</p>
        </template>
      </el-table-column>
      <el-table-column label="学习起止时间" min-width="220">
        <template slot-scope="scope">
          <p v-if="!scope.row.isLongTerm">起始：{{ scope.row.trainingBeginDate }}</p>
          <p v-if="!scope.row.isLongTerm">结束：{{ scope.row.trainingEndDate }}</p>
          <p v-if="scope.row.isLongTerm">长期有效</p>
        </template>
      </el-table-column>
      <el-table-column label="最新修改时间" min-width="180" sortable="custom" prop="commodityLastEditTime">
      </el-table-column>
      <el-table-column label="是否仅导入开通" min-width="140">
        <template slot-scope="scope">
          {{ scope.row.commodityPurchaseChannelConfig.customerPurchase.couldBuy ? '否' : '是' }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="schoolConfigFlag"
        key="needDataSync"
        label="成果是否同步"
        min-width="180"
        prop="needDataSync"
        align="center"
      >
        <template slot-scope="scope">
          {{ isNeedDataSync(scope.row.needDataSync) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template v-slot="{ row }">
          <div v-if="row.isAuthorize">
            <el-tooltip class="item" effect="dark" content="培训方案已授权该分销商" placement="top">
              <el-button type="text" disabled>选择</el-button>
            </el-tooltip>
          </div>
          <div v-else>
            <el-button type="text" @click="check(row)" v-if="showSelectBtn(row)">选择</el-button>
            <el-button type="text" @click="unCheck(row)" v-else>取消选择</el-button>
          </div>
        </template>
        <!-- <template slot-scope="scope">
          <el-button type="text" size="mini" slot="reference" @click="check(scope.row)" v-if="showSelectBtn(scope.row)"
            >选择</el-button
          >
          <el-button type="text" size="mini" slot="reference" @click="unCheck(scope.row)" v-else>取消选择</el-button>
        </template> -->
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="trainSchemePage" v-bind="trainSchemePage"> </hb-pagination>
  </el-card>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch, Prop } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import {
    CommoditySkuRequest,
    CommoditySkuSortField,
    CommoditySkuSortRequest,
    OnShelveRequest,
    RegionSkuPropertySearchRequest,
    SchemeRequest,
    SkuPropertyRequest,
    SortPolicy
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import { cloneDeep, isBoolean } from 'lodash'
  import BizYearSelect from '@hbfe/jxjy-admin-components/src/biz/biz-year-select.vue'
  import BizNationalRegion from '@hbfe/jxjy-admin-components/src/biz/biz-national-region.vue'
  import BizIndustrySelect from '@hbfe/jxjy-admin-components/src/biz/biz-industry-select.vue'
  import BizAccounttypeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-accounttype-select.vue'
  import BizTrainingCategorySelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-category-select.vue'
  import BizMajorSelect from '@hbfe/jxjy-admin-components/src/biz/biz-major-select.vue'
  import { RegionSkuPropertyRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import BizMajorCascader from '@hbfe/jxjy-admin-components/src/biz/biz-major-cascader.vue'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import { TableSort } from '@hbfe/jxjy-admin-scheme/src/models/TableSort'
  import { FakeOperateTypeEnum } from '@hbfe/jxjy-admin-scheme/src/models/FakeOperateTypeEnum'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-common/src/models/sku'
  import UITrainClassCommodityDetail from '@hbfe/jxjy-admin-scheme/src/models/UITrainClassCommodityDetail'
  import MutationTrainClassCommodityClass from '@api/service/management/train-class/mutation/MutationTrainClassCommodityClass'
  import QueryOrder from '@api/service/management/trade/single/order/query/QueryOrder'
  import BizTechnicalGradeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-technical-grade-select.vue'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import getServicerIsDocking from '@api/service/management/online-school-config/portal/query/QueryPortal'
  import moment from 'moment'
  import SchemeType, { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import SchemeDto from '@hbfe/fx-manage/src/views/distribution/commodity/model/SchemeDto'
  import TrainingMode from '@api/service/common/scheme/enum/TrainingMode'
  @Component({
    components: {
      BizTechnicalGradeSelect,
      BizMajorCascader,
      BizMajorSelect,
      BizTrainingCategorySelect,
      BizAccounttypeSelect,
      BizIndustrySelect,
      BizNationalRegion,
      BizYearSelect
    }
  })
  export default class extends Vue {
    @Ref('elTableRef') elTableRef: any
    @Ref('industrySelect') industrySelect: BizIndustrySelect

    @Prop({
      type: Array,
      default: () => new Array<string>()
    })
    value: string[]

    @Prop({
      type: Array,
      default: () => new Array<SchemeDto>()
    })
    schemeList: SchemeDto[]

    /**
     * 提示语
     */
    @Prop({
      type: String,
      default: ''
    })
    placeholder: string

    @Prop({
      type: String,
      default: ''
    })
    distributorId: string

    /**
     * 是否多选
     */
    @Prop({
      type: Boolean,
      default: false
    })
    ismultiple: boolean

    FakeOperateTypeEnum = FakeOperateTypeEnum
    // 查询参数 - 培训方案
    trainSchemeQueryParam: CommoditySkuRequest
    // 分页参数 - 培训方案
    trainSchemePage: UiPage
    // 分页加载参数 - 培训方案
    trainSchemeQuery: Query = new Query()
    // 培训方案列表
    trainSchemeList: Array<UITrainClassCommodityDetail> = new Array<UITrainClassCommodityDetail>()
    // 培训方案业务状态层入口
    QueryTrainClassCommodityList = new QueryTrainClassCommodityList()
    // 排序策略
    sortPolicy: Array<CommoditySkuSortRequest> = new Array<CommoditySkuSortRequest>()

    /**
     * 选中的列表数据
     */
    selectedList: string[] = []

    /**
     * 选中的数据
     */
    selectedSchemeList: SchemeDto[] = []

    // 设置时间选择范围
    isDisabled = {
      disabledDate(time: Date) {
        if (!time) {
          return false
        }
        // 大于某个日期不能选择
        const myDate = new Date(moment().format('YYYY-MM-DD 00:00:00'))
        return time.getTime() < myDate.getTime()
      }
    }

    QueryOrder = new QueryOrder()

    schemeType = SchemeType

    // 培训方案名称
    schemeName = ''
    /**
     * 是否在当前页面，否则停止轮询
     */
    isActivated = true
    /**
     * 定时器创建，修改查询定时器
     */
    timer: any
    /**
     * loading遮罩层
     */
    loading: any

    /**
     * 导出列表数据按钮loading加载
     */
    exportLoading = false

    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuProperty = {
      /**
       * 年度
       */
      year: '' as any,
      /**
       * 地区
       */
      region: [] as string[],
      /**
       * 行业
       */
      industry: '',
      /**
       * 科目类型
       */
      subjectType: '',
      /**
       * 培训类别
       */
      trainingCategory: '',
      /**
       * 培训专业 - 建设行业
       */
      constructionTrainingMajor: '',
      /**
       * 培训专业 - 人社行业
       */
      societyTrainingMajor: [] as string[],
      /**
       * 技术等级 - 工勤行业
       **/
      jobLevel: '',
      /**
       * 培训对象
       */
      trainingObject: '',
      /**
       * 岗位类别
       */
      positionCategory: '',
      /**
       * 学段id
       */
      studyPeriodId: '',
      /**
       * 学科id
       */
      subjectId: ''
    } as SchemeSkuProperty

    /**
     * 培训方案类型
     */
    schemeTypeInfo: Array<string> = new Array<string>()

    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''

    /**
     * 培训类别Id
     */
    trainingCategoryId = ''

    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: true,
      // 培训类别
      trainingCategory: true,
      // 技术等级
      jobLevel: true,
      // 培训对象
      trainingObject: true,
      //   岗位类别
      positionCategory: true
    }

    /**
     * 当前网校信息
     */
    envConfig = {
      // 工勤行业
      workServiceId: '',
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 职业卫生行业Id
      occupationalHealthId: '',
      //教师行业id
      teacherIndustryId: '',
      // 药师行业
      yshyId: ''
    }

    // 长期有效 - 开始时间
    defaultBeginDate = CreateSchemeUtils.defaultBeginDate
    // 长期有效 - 结束时间
    defaultEndDate = CreateSchemeUtils.defaultEndDate

    MutationTrainClassCommodityClass = new MutationTrainClassCommodityClass()
    /**
     * 上下架状态-（销售状态）
     */
    onShelveStatus: number = null
    // 网校对接与否
    schoolConfigFlag = true
    onShelveStatusOptions = [
      { id: 1, label: '已上架' },
      { id: 0, label: '已下架' }
    ]
    /**
     * 培训形式枚举
     */
    TrainingMode = TrainingMode

    /**
     * 选择按钮显隐判断
     */
    get showSelectBtn() {
      return (item: UITrainClassCommodityDetail) => {
        const data = this.selectedList.find((ite) => ite === item.schemeId)
        if (data) {
          return false
        }
        return true
      }
    }

    @Watch('value', {
      immediate: true
    })
    valueChange() {
      this.selectedList = this.value
    }
    @Watch('schemeList', { immediate: true, deep: true })
    schemeChange() {
      this.selectedSchemeList = this.schemeList
    }

    @Watch('localSkuProperty', {
      immediate: true,
      deep: true
    })
    localSkuPropertyChange(val: any) {
      console.log('localSkuProperty', cloneDeep(val))
    }

    constructor() {
      super()
      this.initQueryParam()
      this.trainSchemePage = new UiPage(this.pageScheme, this.pageScheme)
    }

    /**
     * 初始化查询参数
     */
    initQueryParam() {
      this.trainSchemeQueryParam = new CommoditySkuRequest()
      this.trainSchemeQueryParam.onShelveRequest = new OnShelveRequest()
      this.trainSchemeQueryParam.schemeRequest = new SchemeRequest()
      this.trainSchemeQueryParam.schemeRequest.schemeName = ''
      this.trainSchemeQueryParam.skuPropertyRequest = new SkuPropertyRequest()
      this.trainSchemeQueryParam.skuPropertyRequest.year = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      this.trainSchemeQueryParam.skuPropertyRequest.regionSkuPropertySearch.region =
        new Array<RegionSkuPropertyRequest>()
      this.trainSchemeQueryParam.skuPropertyRequest.industry = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.subjectType = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingProfessional = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingObject = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.positionCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.jobLevel = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.learningPhase = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.discipline = new Array<string>()
      this.trainSchemeQueryParam.isDisabledResourceShow = true
      this.sortPolicy = new Array<CommoditySkuSortRequest>()
      this.schemeTypeInfo = new Array<string>()
    }

    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.studyPeriodId = ''
      this.localSkuProperty.trainingForm = ''
      this.initQueryParam()
      this.localSkuProperty = new SchemeSkuProperty()
      this.onShelveStatus = null
      this.schemeName = ''
      this.sortPolicy = new Array<CommoditySkuSortRequest>()

      // 移除表格排序
      this.elTableRef.clearSort()
      await this.searchBase()
    }

    /**
     * 加载第一页
     */
    async searchBase() {
      this.trainSchemePage.pageNo = 1
      await this.pageScheme()
    }

    /**
     * 分页查询
     */
    async pageScheme() {
      this.trainSchemeQuery.loading = true
      // 清除滚动条位置并定位回顶部
      this.clearScrollPositionBackTop()
      try {
        this.getPageQueryParams()
        const trainSchemeList = await this.getTrainSchemeList()
        this.trainSchemeList = new Array<UITrainClassCommodityDetail>()
        trainSchemeList?.map((el: UITrainClassCommodityDetail) => {
          const item = new UITrainClassCommodityDetail()
          Object.assign(item, el)
          item.isLongTerm = false
          item.isSocietyIndustry = false
          if (item.trainingEndDate === this.defaultEndDate && item.trainingBeginDate === this.defaultBeginDate) {
            item.isLongTerm = true
          }
          if (item.skuValueNameProperty?.industry?.skuPropertyName === '人社行业') {
            item.isSocietyIndustry = true
          }
          this.trainSchemeList.push(item)
        })
        console.log('trainSchemeList', this.trainSchemeList)
      } catch (e) {
        console.log('获取培训方案分页列表失败！', e)
      } finally {
        this.trainSchemeQuery.loading = false
      }
      this.$nextTick(() => {
        this.elTableRef && this.elTableRef.doLayout()
      })
    }

    /**
     * 处理列表查询参数
     */
    getPageQueryParams() {
      this.getLocalSkuProperty()
      this.trainSchemeQueryParam.onShelveRequest.onShelveStatus =
        this.onShelveStatus || this.onShelveStatus === 0 ? this.onShelveStatus : undefined
      this.trainSchemeQueryParam.schemeRequest.schemeName = this.schemeName || undefined

      this.configureTrainSchemeQueryParam()
    }

    /**
     * 获取培训班列表
     */
    async getTrainSchemeList() {
      console.group(
        '%c%s',
        'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#800080, #C71585)',
        '11111调试输出'
      )
      console.log(this.distributorId)
      console.count('11111输出次数')
      console.groupEnd()
      if (this.sortPolicy.length) {
        return (
          (await this.QueryTrainClassCommodityList.queryTrainClassCommodityList(
            this.trainSchemePage,
            this.trainSchemeQueryParam,
            this.sortPolicy,
            this.distributorId
          )) || ([] as TrainClassCommodityVo[])
        )
      } else {
        return (
          (await this.QueryTrainClassCommodityList.queryTrainClassCommodityList(
            this.trainSchemePage,
            this.trainSchemeQueryParam,
            [],
            this.distributorId
          )) || ([] as TrainClassCommodityVo[])
        )
      }
    }

    /**
     * 配置查询参数
     */
    configureTrainSchemeQueryParam() {
      // 处理培训方案类型
      if (!this.schemeTypeInfo.length) {
        this.trainSchemeQueryParam.schemeRequest.schemeType = undefined
      }
      const schemeType = this.schemeTypeInfo[this.schemeTypeInfo.length - 1]
      if (schemeType in SchemeTypeEnum) {
        this.trainSchemeQueryParam.schemeRequest.schemeType = schemeType
      } else {
        this.trainSchemeQueryParam.schemeRequest.schemeType = undefined
      }
    }

    /**
     * 获取本地sku选项
     */
    getLocalSkuProperty() {
      const skuProperties = cloneDeep(this.trainSchemeQueryParam.skuPropertyRequest)
      // 下方year 为 string类型，故用any
      skuProperties.year = !this.localSkuProperty.year.length ? ([] as string[]) : [this.localSkuProperty.year as any]
      skuProperties.trainingForm = !this.localSkuProperty.trainingForm
        ? ([] as string[])
        : [this.localSkuProperty.trainingForm as any]
      skuProperties.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
      const localRegion = cloneDeep(this.localSkuProperty.region)
      if (Array.isArray(localRegion) && localRegion.length) {
        const option = new RegionSkuPropertyRequest()
        option.province = localRegion.length >= 1 ? localRegion[0] : undefined
        option.city = localRegion.length >= 2 ? localRegion[1] : undefined
        option.county = localRegion.length >= 3 ? localRegion[2] : undefined
        skuProperties.regionSkuPropertySearch.region.push(option)
        skuProperties.regionSkuPropertySearch.regionSearchType = 1
      } else {
        skuProperties.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      }
      skuProperties.industry = !this.localSkuProperty.industry ? ([] as string[]) : [this.localSkuProperty.industry]
      skuProperties.jobLevel = !this.localSkuProperty.jobLevel ? ([] as string[]) : [this.localSkuProperty.jobLevel]
      skuProperties.subjectType = !this.localSkuProperty.subjectType
        ? ([] as string[])
        : [this.localSkuProperty.subjectType]
      skuProperties.trainingCategory = !this.localSkuProperty.trainingCategory
        ? ([] as string[])
        : [this.localSkuProperty.trainingCategory]
      skuProperties.trainingProfessional = this.getTrainingProfessional()
      skuProperties.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      skuProperties.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      skuProperties.learningPhase = !this.localSkuProperty.studyPeriodId
        ? ([] as string[])
        : [this.localSkuProperty.studyPeriodId]
      skuProperties.discipline = !this.localSkuProperty.subjectId ? ([] as string[]) : [this.localSkuProperty.subjectId]

      // 药师行业
      skuProperties.certificatesType = !this.localSkuProperty.pharmacistIndustry
        ? ([] as string[])
        : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
        ? [this.localSkuProperty.pharmacistIndustry[0]]
        : []
      skuProperties.practitionerCategory = !this.localSkuProperty.pharmacistIndustry
        ? ([] as string[])
        : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
        ? [this.localSkuProperty.pharmacistIndustry[1]]
        : []
      this.trainSchemeQueryParam.skuPropertyRequest = cloneDeep(skuProperties)
      //   console.log('selectedSkuProperties', JSON.stringify(this.trainSchemeQueryParam.skuPropertyRequest))
    }

    created() {
      this.clearScrollPosition()
    }

    /**
     * @description 页面初始化
     * */
    async mounted() {
      this.queryConfig()
      await this.searchBase()
    }

    /**
     * @description 页面被激活
     */
    async activated() {
      this.isActivated = true
      if (this.$route.query.schemeName) {
        this.schemeName = this.$route.query.schemeName as string
        await this.searchBase()
      }
      this.$nextTick(() => {
        this.elTableRef && this.elTableRef.doLayout()
      })
    }

    /**
     * @description 基础查询
     */
    async queryConfig() {
      // 获取是否对接公共服务平台
      this.schoolConfigFlag = await getServicerIsDocking.getServicerIsDocking()
      if (this.$route.query.schemeName) {
        this.schemeName = this.$route.query.schemeName as string
      }
      // 查询sku
      await QueryIndustry.queryIndustry()
      if (QueryIndustry.industryList.length == 1) {
        this.localSkuProperty.industry = QueryIndustry.industryList[0].id
      }
    }

    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      // console.log('envConfig', this.envConfig)
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人设行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }

    /**
     * 获取培训方案类型
     */
    getSchemeType(row: UITrainClassCommodityDetail) {
      // 培训形式：网授、面授、面网授
      // 方案类型：培训班、合作办学
      // 网授选课方式：选课规则、自主选课
      // 当方案类型是面授，展示 “培训形式-方案类型”，否则展示 “培训形式-方案类型-网授选课方式”

      return this.schemeType.getSchemeType(row.schemeType, true)
      //   if (row.schemeType === 1) {
      //     return '培训班-选课规则'
      //   } else if (row.schemeType === 2) {
      //     return '培训班-自主选课'
      //   } else {
      //     return ''
      //   }
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: UITrainClassCommodityDetail, type: string): string {
      if (row.skuValueNameProperty[type]?.skuPropertyName) {
        const value = row.skuValueNameProperty[type].skuPropertyName
        const valuesArr = value.split('/'),
          lastIndex = valuesArr.length - 1
        return type === 'trainingMajor' && !row.isSocietyIndustry ? valuesArr[lastIndex] : value
      }
      return ''
    }

    /**
     * 处理排序 - 最后修改时间
     */
    async handleSortChange(column: any) {
      console.log('start sort', column.prop, column.order)
      if (column?.prop === 'commodityLastEditTime') {
        if (!column.order) {
          // 按发布时间降序排
          console.log('默认按发布时间降序排')
          this.sortPolicy = new Array<CommoditySkuSortRequest>()
        }
        if (column.order === TableSort.DESC) {
          // 按最后修改时间降序排
          console.log('按最后修改时间降序排')
          this.sortPolicy = new Array<CommoditySkuSortRequest>()
          const option = new CommoditySkuSortRequest()
          option.sortField = CommoditySkuSortField.LAST_EDIT_TIME
          option.policy = SortPolicy.DESC
          this.sortPolicy.push(option)
        }
        if (column.order === TableSort.ASC) {
          // 按最后修改时间升序排
          console.log('按最后修改时间升序排')
          this.sortPolicy = new Array<CommoditySkuSortRequest>()
          const option = new CommoditySkuSortRequest()
          option.sortField = CommoditySkuSortField.LAST_EDIT_TIME
          option.policy = SortPolicy.ASC
          this.sortPolicy.push(option)
        }
      }
      await this.searchBase()
    }

    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      this.localSkuProperty.jobLevel = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.studyPeriodId = ''
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.pharmacistIndustry = [] as string[]
    }
    updateTrainingCategory(val: string) {
      if (val) {
        this.localSkuProperty.positionCategory = ''
      }
    }
    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.SUBJECT_TYPE)
      const configTrainingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.TRAINING_CATEGORY
      )
      const jobLevel = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.JOB_LEVEL)
      const trainingObject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.TRAINING_OBJECT)
      const positionCategory = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.POSITION_CATEGORY)
      this.skuVisible.subjectType = configSubjectType > -1
      this.skuVisible.trainingCategory = configTrainingCategory > -1
      this.skuVisible.jobLevel = jobLevel > -1
      this.skuVisible.trainingObject = trainingObject > -1
      this.skuVisible.positionCategory = positionCategory > -1
    }

    /**
     * 响应组件行业Id集合传参
     */
    async handleIndustryInfos(values: any) {
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.occupationalHealthId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
      this.envConfig.yshyId = values.medicineIndustryId || ''
    }
    //选择学段
    updateStudyPeriod(val: string) {
      this.localSkuProperty.studyPeriodId = val
      this.localSkuProperty.subjectId = ''
      this.trainSchemeQueryParam.skuPropertyRequest.discipline = []
    }
    //选择学科
    updateSubject(val: string) {
      this.localSkuProperty.subjectId = val
    }

    /**
     * 是否处理中
     */
    getSchemeIsProcessing(item: UITrainClassCommodityDetail) {
      return ((item?.lastTransactionStep && item?.lastTransactionStep !== 5) || item?.recalculating) && !item?.hasError
    }

    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
    }

    beforeDestroy() {
      this.isActivated = false
      clearTimeout(this.timer)
      this.loading?.close()
    }
    deactivated() {
      this.isActivated = false
      clearTimeout(this.timer)
      this.loading?.close()
    }
    clearSubject() {
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.studyPeriodId = ''
      this.trainSchemeQueryParam.skuPropertyRequest.discipline = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.learningPhase = new Array<string>()
    }

    isNeedDataSync(needDataSync: boolean) {
      if (!needDataSync && isBoolean(needDataSync)) {
        return '不同步'
      } else {
        // 旧数据默认显示同步
        return '同步'
      }
    }

    /**
     * @description 清除滚动条位置
     * @mark localStorage
     * */
    clearScrollPosition() {
      localStorage.removeItem('scrollPosition')
    }

    /**
     * @description 清除滚动条并滚动回顶部
     * */
    clearScrollPositionBackTop() {
      this.clearScrollPosition()
      const tableBodyWrapper = this.elTableRef.$el.querySelector('.el-table__body-wrapper')
      this.delayChangeScrollTop(tableBodyWrapper, 1)
    }

    /**
     * @description 定位滚动条延迟滚动
     * @reason 用于防止滚动条定位后列表错位
     * */
    delayChangeScrollTop(tableBodyWrapper: Element, position: number) {
      // 判断边界
      tableBodyWrapper.scrollTop = position - 1 >= 0 ? position - 1 : position + 1
      setTimeout(() => {
        tableBodyWrapper.scrollTop = position
      }, 100)
    }

    form(item: UITrainClassCommodityDetail) {
      const scheme = new SchemeDto()
      scheme.commoditySkuId = item.commoditySkuId
      scheme.schemeId = item.schemeId
      scheme.isLongTerm = item.isLongTerm
      scheme.schemeName = item.commodityBasicData?.saleTitle
      scheme.price = item.commodityBasicData.price
      scheme.registerBeginDate = item.registerBeginDate
      scheme.registerEndDate = item.registerEndDate
      scheme.trainingBeginDate = item.trainingBeginDate
      scheme.trainingEndDate = item.trainingEndDate
      scheme.resourceServicerId = item.resourceServicerId
      scheme.resourceUnitId = item.resourceUnitId
      const skuNames: string[] = []
      if (item.skuValueNameProperty?.region?.skuPropertyName) {
        skuNames.push(item.skuValueNameProperty.region.skuPropertyName)
      }
      if (item.skuValueNameProperty?.year?.skuPropertyName) {
        skuNames.push(item.skuValueNameProperty.year.skuPropertyName)
      }
      if (item.skuValueNameProperty?.industry?.skuPropertyName) {
        skuNames.push(item.skuValueNameProperty.industry.skuPropertyName)
      }
      if (item.skuValueNameProperty?.subjectType?.skuPropertyName) {
        skuNames.push(item.skuValueNameProperty.subjectType.skuPropertyName)
      }
      if (item.skuValueNameProperty?.trainingCategory?.skuPropertyName) {
        skuNames.push(item.skuValueNameProperty.trainingCategory.skuPropertyName)
      }
      if (item.skuValueNameProperty?.trainingMajor?.skuPropertyName) {
        skuNames.push(item.skuValueNameProperty.trainingMajor.skuPropertyName)
      }
      if (item.skuValueNameProperty?.trainingObject?.skuPropertyName) {
        skuNames.push(item.skuValueNameProperty.trainingObject.skuPropertyName)
      }
      if (item.skuValueNameProperty?.positionCategory?.skuPropertyName) {
        skuNames.push(item.skuValueNameProperty.positionCategory.skuPropertyName)
      }
      if (item.skuValueNameProperty?.technicalGrade?.skuPropertyName) {
        skuNames.push(item.skuValueNameProperty.technicalGrade.skuPropertyName)
      }
      if (item.skuValueNameProperty?.learningPhase?.skuPropertyName) {
        skuNames.push(item.skuValueNameProperty.learningPhase.skuPropertyName)
      }
      if (item.skuValueNameProperty?.discipline?.skuPropertyName) {
        skuNames.push(item.skuValueNameProperty.discipline.skuPropertyName)
      }

      scheme.skuNames = skuNames
      return scheme
    }

    /**
     * 选择
     */
    check(item: UITrainClassCommodityDetail) {
      if (this.ismultiple) {
        this.selectedList.push(item.schemeId)
        this.selectedSchemeList.push(this.form(item))
        this.$emit('fxChange', this.selectedSchemeList)
      } else {
        this.selectedList = [item.schemeId]
        this.selectedSchemeList = [this.form(item)]
        this.$emit('fxChange', this.selectedSchemeList)
      }
    }

    /**
     * 取消选择
     */
    unCheck(item: UITrainClassCommodityDetail) {
      if (this.ismultiple) {
        this.selectedList = this.selectedList.filter((ite) => ite !== item.schemeId)
        this.selectedSchemeList = this.selectedSchemeList.filter((ite) => ite.schemeId !== item.schemeId)
        this.$emit('fxChange', this.selectedSchemeList)
      } else {
        this.selectedSchemeList = []
        this.$emit('fxChange', this.selectedSchemeList)
      }
    }
  }
</script>

<style scoped lang="scss">
  .m-table {
    width: 100%;
    color: #444;
    height: auto;
  }
  //   .fullscreen {
  //     height: 70vh;
  //   }
  //   .normal {
  //     max-height: 500px;
  //   }
</style>
