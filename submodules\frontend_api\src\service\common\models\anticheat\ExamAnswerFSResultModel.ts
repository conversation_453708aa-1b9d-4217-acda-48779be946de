/**
 * 考试作答记录人脸识别结果
 */
class ExamAnswerFSResultModel {
  /**
   * 用户ID
   */
  userId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 考试场次ID
   */
  examRoundId: string
  /**
   * 考试作答记录ID
   */
  examAnswerId: string
  /**
   * 是否通过
   */
  passed: boolean
  /**
   * 人脸拍摄总点数
   */
  totalPointCount: number
  /**
   * 通过的人脸拍摄总点数
   */
  passedPointCount: number
}

export default ExamAnswerFSResultModel
