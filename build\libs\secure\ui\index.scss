html, body, #app {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;

  .el-container {
    height: 100%;

    .el-tree {
      background: black;
      color: #d1d1d1;
    }
  }
}

.brand {
  color: white;
  padding: 10px;
  font-size: 24px;
}

.secure-tree {
  overflow: auto;
}

.pf-header {
  color: white;
  background: black;
  display: flex;
  align-items: center;
  font-size: 24px;

  .pf-menu {
    flex: 1;
    font-size: 15px;
  }

  .header-tool {
    cursor: pointer;
    padding: 5px 10px;

    &:hover {
      background: #d1d1d1;
    }
  }
}

.pf-aside {
  display: flex;
  flex-direction: column;

  .secure-tree {
    &::-webkit-scrollbar {
      width: 0 !important;
      display: none
    }
  }
}

.el-tree {
  .is-current {
    .el-tree-node__content {
      background: #a30c0c !important;
      color: white;
    }
  }

  .el-tree-node__content {
    &:hover {
      background: #680e0e;
    }
  }
}

.search-wrapper {
  display: flex;
  justify-content: center;

  .search-mask {
    background: black;
    width: 100%;
    height: 100%;
    opacity: 0.8;
    position: absolute;
    z-index: -1
  }

  .search-box {
    width: 100%;
    background: white;
    border-radius: 20px;
    height: 500px;

    .search-items {
      overflow: auto;
      height: 450px;
      margin-top: 15px;

      &::-webkit-scrollbar {
        width: 0 !important;
        display: none
      }

      .search-item {
        padding: 15px 12px;
        display: flex;
        align-items: center;

        .index {
          font-weight: bold;
          margin-right: 20px;
        }

        .search-desc {
          color: #d1d1d1;
          font-size: 12px;
          margin-top: 10px;
        }

        &:hover {
          background: #409EFF;
          cursor: pointer;
        }
      }
    }
  }
}

.upload-demo {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .el-upload-dragger {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-upload--text, .el-upload-dragger {
    width: 100%;
    height: 100%;
  }
}
