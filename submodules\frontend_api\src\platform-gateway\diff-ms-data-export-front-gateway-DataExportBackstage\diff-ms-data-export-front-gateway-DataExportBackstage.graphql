schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""批量生成学员学习心得pdf
		@param request
		@return
	"""
	BatchStudentLearningExperienceExportPdf(request:StudentLearningExperienceRequest):Boolean!
	"""导出当前服务商下方案学习统计详情（面网授）
		@return
	"""
	exportBlendedSchemeLearningReportFormsDetailExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出当前服务商下方案学习统计（面网授）
		@return
	"""
	exportBlendedSchemeLearningReportFormsExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出当前分销商下学员方案学习 - 面网授
		@param request
		@return
	"""
	exportBlendedStudentSchemeLearningExcelInDistributor(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""导出当前服务商下学员方案学习（面网授）
		@param request
		@return
	"""
	exportBlendedStudentSchemeLearningExcelInServicer(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""导出当前服务商地区管理员下学员方案学习 - 面网授
		@return
	"""
	exportBlendedStudentSchemeLearningExcelInServicerManageRegion(request:StudentSchemeLearningRequest):Boolean!
	"""导出当前专题管理员下学员方案学习  - 面网授
		@param request
		@return
	"""
	exportBlendedStudentSchemeLearningExcelInTrainingChannel(request:StudentSchemeLearningRequest):Boolean!
	"""导出当前服务商下方案学习统计详情（面授）
		@return
	"""
	exportFaceToFaceSchemeLearningReportFormsDetailExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出当前服务商下方案学习统计（面授）
		@return
	"""
	exportFaceToFaceSchemeLearningReportFormsExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出当前分销商下学员方案学习 - 面授
		@param request
		@return
	"""
	exportFaceToFaceStudentSchemeLearningExcelInDistributor(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""导出当前服务商下学员方案学习（面授）
		@param request
		@return
	"""
	exportFaceToFaceStudentSchemeLearningExcelInServicer(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""导出当前服务商地区管理员下学员方案学习 -面授
		@return
	"""
	exportFaceToFaceStudentSchemeLearningExcelInServicerManageRegion(request:StudentSchemeLearningRequest):Boolean!
	"""导出当前专题管理员下学员方案学习  - 面授
		@param request
		@return
	"""
	exportFaceToFaceStudentSchemeLearningExcelInTrainingChannel(request:StudentSchemeLearningRequest):Boolean!
	"""导出当前网校下面网授班合格学员学习明细
		@return
	"""
	exportQualificationStudentSchemeLearningDetailInServicer(request:AHJSBDYStudentSchemeLearningInOnlineRequest,sort:[StudentSchemeLearningSortRequest]):Boolean!
	"""导出试题
		@param request 查询参数对象
		@return true 成功 false 失败
	"""
	exportQuestionExcelInServicer(request:QuestionRequest):Boolean!
	"""导出当前服务商下地区学习统计详情
		@return
	"""
	exportRegionLearningReportFormsDetailExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出当前服务商地区管理员下地区学习统计详情
		@return
	"""
	exportRegionLearningReportFormsDetailExcelInServicerManageRegion(request:LearningReportFormsRequest):Boolean!
	"""导出当前服务商下地区学习统计
		@return
	"""
	exportRegionLearningReportFormsExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出当前服务商地区管理员下地区学习统计
		@return
	"""
	exportRegionLearningReportFormsExcelInServicerManageRegion(request:LearningReportFormsRequest):Boolean!
	"""导出查询当前网校下方案期别下的学员报名情况列表
		@return
	"""
	exportSchemeLearningEnrollmentInServicer(issueId:String,request:UserInfoRequest):Boolean!
	"""导出当前服务商下方案学习统计详情（网授）
		@return
	"""
	exportSchemeLearningReportFormsDetailExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出当前服务商下方案学习统计
		@return
	"""
	exportSchemeLearningReportFormsExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出学员学习心得
		@param request
		@return
	"""
	exportStudentLearningExperienceInServicer(request:StudentLearningExperienceRequest):Boolean!
	"""导出当前分销商下学员方案学习 - 网授
		@param request
		@return
	"""
	exportStudentSchemeLearningExcelInDistributor(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""导出当前服务商下学员方案学习（网授）
		@param request
		@return
	"""
	exportStudentSchemeLearningExcelInServicer(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""导出当前服务商地区管理员下学员方案学习 - 网授
		@return
	"""
	exportStudentSchemeLearningExcelInServicerManageRegion(request:StudentSchemeLearningRequest):Boolean!
	"""导出当前专题管理员下学员方案学习  - 网授
		@param request
		@return
	"""
	exportStudentSchemeLearningExcelInTrainingChannel(request:StudentSchemeLearningRequest):Boolean!
}
input OwnerInfoModel @type(value:"com.fjhb.ms.basicdata.model.OwnerInfoModel") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerId:String
}
input ObsFileMetaData @type(value:"com.fjhb.ms.data.export.commons.utils.async.ObsFileMetaData") {
	bizType:String
	owner:String
	sign:String
}
input DateScopeRequest1 @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
input StudentSchemeLearningSortRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningSortRequest") {
	field:StudentSchemeLearningSortField
	policy:SortPolicy
}
input ConnectManageSystemRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ConnectManageSystemRequest") {
	syncStatus:Int
}
input DataAnalysisRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.DataAnalysisRequest") {
	trainingResultPeriod:DoubleScopeRequest
	requirePeriod:DoubleScopeRequest
	acquiredPeriod:DoubleScopeRequest
}
input ExtendedInfoRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ExtendedInfoRequest") {
	whetherToPrint:Boolean
	applyCompanyCode:String
	policyTrainingSchemeId:String
	policyTrainingSchemeName:String
}
input LearningRegisterRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.LearningRegisterRequest") {
	registerType:Int
	sourceType:String
	sourceId:String
	status:[Int]
	registerTime:DateScopeRequest1
	saleChannels:[Int]
	orderNoList:[String]
	subOrderNoList:[String]
	batchOrderNoList:[String]
	distributorId:String
	portalId:String
}
input RegionRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.RegionRequest") {
	province:String
	city:String
	county:String
}
input StudentLearningRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.learning.StudentLearningRequest") {
	trainingResultList:[Int]
	trainingResultTime:DateScopeRequest1
	notLearningTypeList:[Int]
	courseScheduleStatus:Int
	examAssessResultList:[Int]
}
input RegionSkuPropertyRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertySearchRequest") {
	region:[RegionSkuPropertyRequest1]
	regionSearchType:Int
}
input SchemeRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeRequest") {
	schemeId:String
	schemeIdList:[String]
	schemeType:String
	schemeName:String
	skuProperty:SchemeSkuPropertyRequest1
}
input SchemeSkuPropertyRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeSkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest1
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	positionCategory:[String]
	trainingObject:[String]
	jobLevel:[String]
	jobCategory:[String]
	subject:[String]
	grade:[String]
	learningPhase:[String]
	discipline:[String]
	qualificationCategory:[String]
	trainingWay:[String]
	trainingInstitution:[String]
	mainAdditionalItem:[String]
}
input UserInfoRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserInfoRequest") {
	name:String
	idCard:String
	phoneNumber:String
}
input UserPropertyRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserPropertyRequest") {
	regionList:[RegionRequest1]
	companyName:String
	payOrderRegionList:[RegionRequest1]
}
input UserRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserRequest") {
	userIdList:[String]
	accountIdList:[String]
	userProperty:UserPropertyRequest1
}
input AHJSBDYStudentSchemeLearningInOnlineRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.AHJSBDYStudentSchemeLearningInOnlineRequest") {
	"""资源供应商"""
	resourceSupplier:String
	"""报名点机构"""
	registerOrg:String
	studentNoList:[String]
	student:UserRequest1
	learningRegister:LearningRegisterRequest1
	scheme:SchemeRequest1
	studentLearning:StudentLearningRequest1
	dataAnalysis:DataAnalysisRequest1
	connectManageSystem:ConnectManageSystemRequest1
	extendedInfo:ExtendedInfoRequest1
	openPrintTemplate:Boolean
	saleChannels:[Int]
	trainingChannelName:String
	trainingChannelId:String
	notDistributionPortal:Boolean
	trainingType:String
	issueId:String
}
"""试题查询条件"""
input QuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.QuestionRequest") {
	"""试题ID集合"""
	questionIdList:[String]
	"""题库ID集合"""
	libraryIdList:[String]
	"""关联课程ID集合"""
	relateCourseIds:[String]
	"""试题题目"""
	topic:String
	"""试题类型（1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题）"""
	questionType:Int
	"""创建时间范围"""
	createTimeScope:DateScopeRequest
	"""是否启用"""
	isEnabled:Boolean
	jobName:String
	metaData:ObsFileMetaData
}
"""功能描述：时间范围查询条件
	@Author： wtl
	@Date： 2022/1/25 15:30
"""
input DateScopeRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.nested.DateScopeRequest") {
	"""开始时间
		查询大于等于开始时间的结果
	"""
	beginTime:DateTime
	"""结束时间
		查询小于等于结束时间的结果
	"""
	endTime:DateTime
}
"""地区学习统计查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 14:17
"""
input LearningReportFormsRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.LearningReportFormsRequest") {
	"""是否统计暂无地区的学员班级"""
	isStatisticsNullArea:Boolean
	"""学员信息"""
	student:UserRequest
	"""报名信息"""
	learningRegister:LearningRegisterRequest
	"""培训班信息"""
	scheme:SchemeRequest
	"""学习信息"""
	studentLearning:StudentLearningRequest
	"""数据分析信息"""
	dataAnalysis:DataAnalysisRequest
	"""是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1"""
	saleChannels:[Int]
	"""专题名称"""
	trainingChannelName:String
	"""非分销门户"""
	notDistributionPortal:Boolean
	"""培训类型（全部、网授、面网授）"""
	trainingType:String
}
input StudentLearningExperienceRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.StudentLearningExperienceRequest") {
	"""学员学习心得ID"""
	studentLearningExperienceIds:[String]
	"""数据归属"""
	owner:OwnerInfoModel
	"""学习心得主题"""
	learningExperienceTopic:LearningExperienceTopicRequest
	"""学员方案学习"""
	studentLearning:StudentExperienceSchemeLearningRequest
	"""用户信息"""
	user:UserRequest
	"""学习心得类型（班级心得，课程心得）"""
	experienceType:ExperienceType
	"""状态
		@see StudentLearningExperienceStatus
	"""
	status:[StudentLearningExperienceStatus]
	"""不传值默认全查  是否要心得被删除的数据  true只要被删除的 false 只要未被删除的  null全查"""
	isDelete:Boolean
}
"""学员培训方案学习查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 11:40
"""
input StudentSchemeLearningRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.StudentSchemeLearningRequest") {
	"""学员信息"""
	student:UserRequest
	"""报名信息"""
	learningRegister:LearningRegisterRequest
	"""培训班信息"""
	scheme:SchemeRequest
	"""学习信息"""
	studentLearning:StudentLearningRequest
	"""数据分析信息"""
	dataAnalysis:DataAnalysisRequest
	"""对接管理系统"""
	connectManageSystem:ConnectManageSystemRequest
	"""扩展信息"""
	extendedInfo:ExtendedInfoRequest
	"""是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1"""
	saleChannels:[Int]
	"""专题名称"""
	trainingChannelName:String
	"""非分销门户"""
	notDistributionPortal:Boolean
	"""培训类型"""
	trainingType:String
	"""期别id"""
	issueId:String
}
"""@version: 1.0
	@description: 对接管理系统
	@author: sugs
	@create: 2022-11-15 11:27
"""
input ConnectManageSystemRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.ConnectManageSystemRequest") {
	"""同步状态
		@see SyncStatus
		0 未同步
		1 已同步
		2 同步失败
		-1不同步
	"""
	syncStatus:Int
}
"""数据分析信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/20 15:14
"""
input DataAnalysisRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.DataAnalysisRequest") {
	"""成果配置可获得学时"""
	trainingResultPeriod:DoubleScopeRequest
	"""考核要求学时"""
	requirePeriod:DoubleScopeRequest
	"""已获得总学时"""
	acquiredPeriod:DoubleScopeRequest
}
input ExtendedInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.ExtendedInfoRequest") {
	"""是否打印
		true 打印 false 未打印
	"""
	whetherToPrint:Boolean
}
"""学员学习报名信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:08
"""
input LearningRegisterRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.LearningRegisterRequest") {
	"""报名方式
		<p>
		1:学员自主报名
		2:集体报名
		3:管理员导入
		@see StudentRegisterTypes
	"""
	registerType:Int
	"""报名来源类型(ORDER：订单 SUB_ORDER：子订单 EXCHANGE_ORDER：换货单)
		@see StudentSourceTypes
	"""
	sourceType:String
	"""报名来源ID"""
	sourceId:String
	"""学员状态(1:正常 2：冻结 3：失效)
		@see StudentStatus
	"""
	status:[Int]
	"""报名时间"""
	registerTime:DateScopeRequest1
	"""订单销售渠道"""
	saleChannels:[Int]
	"""来源订单号"""
	orderNoList:[String]
	"""来源子订单号"""
	subOrderNoList:[String]
	"""来源批次单号"""
	batchOrderNoList:[String]
	"""分销商id"""
	distributorId:String
	"""分销门户id"""
	portalId:String
}
"""地区模型
	<AUTHOR>
	@version 1.0
	@date 2022/2/27 20:01
"""
input RegionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.RegionRequest") {
	"""地区：省"""
	province:String
	"""地区：市"""
	city:String
	"""地区：区"""
	county:String
}
"""学员学习信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
input StudentLearningRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.learning.StudentLearningRequest") {
	"""培训结果
		<p>
		-1:未知，培训尚未完成
		1:培训合格
		0:培训不合格
		@see StudentTrainingResults
	"""
	trainingResultList:[Int]
	"""培训结果时间"""
	trainingResultTime:DateScopeRequest1
	"""无需学习的学习方式类型
		<p>
		1: 选课学习方式
		2: 考试学习方式
		3: 练习学习方式
		4：自主学习课程学习方式
		@see LearningTypes
	"""
	notLearningTypeList:[Int]
	"""课程学习状态（0：未学习 1：学习中 2：学习完成）"""
	courseScheduleStatus:Int
	"""考试结果（-1：未考核 0：不合格 1：合格）
		@see AssessCalculateResults
	"""
	examAssessResultList:[Int]
}
"""地区sku属性查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/2/25 10:55
"""
input RegionSkuPropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.scheme.RegionSkuPropertyRequest") {
	"""地区: 省"""
	province:String
	"""地区: 市"""
	city:String
	"""地区: 区县"""
	county:String
}
"""地区匹配查询
	<AUTHOR>
	@version 1.0
	@date 2022/2/25 14:19
"""
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.scheme.RegionSkuPropertySearchRequest") {
	"""地区"""
	region:[RegionSkuPropertyRequest]
	"""地区匹配条件
		<p>
		ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回，如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
		PART部分匹配：查询结果返回的地区与查询条件有给值的地区就会返回 如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
		@see com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.common.request.skuProperty.RegionSkuPropertySearchRequest.RegionSearchType
	"""
	regionSearchType:Int
}
"""培训方案信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
input SchemeRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.scheme.SchemeRequest") {
	"""培训方案id"""
	schemeId:String
	"""培训方案id"""
	schemeIdList:[String]
	"""培训方案类型
		<p>
		chooseCourseLearning: 选课规则
		autonomousCourseLearning: 自主选课
		@see SchemeType
	"""
	schemeType:String
	"""方案名称"""
	schemeName:String
	"""培训属性"""
	skuProperty:SchemeSkuPropertyRequest
}
"""培训属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 10:22
"""
input SchemeSkuPropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.scheme.SchemeSkuPropertyRequest") {
	"""年度"""
	year:[String]
	"""地区"""
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	"""行业"""
	industry:[String]
	"""科目类型"""
	subjectType:[String]
	"""培训类别"""
	trainingCategory:[String]
	"""培训专业"""
	trainingProfessional:[String]
	"""技术等级"""
	technicalGrade:[String]
	"""岗位类别"""
	positionCategory:[String]
	"""培训对象"""
	trainingObject:[String]
	"""技术等级"""
	jobLevel:[String]
	"""工种"""
	jobCategory:[String]
	"""科目"""
	subject:[String]
	"""年级"""
	grade:[String]
	"""学段"""
	learningPhase:[String]
	"""学科"""
	discipline:[String]
	"""培训类型 （网授、面授）"""
	trainingWay:[String]
}
"""学习心得主题"""
input LearningExperienceTopicRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.studentlearningExperience.LearningExperienceTopicRequest") {
	"""心得主题id"""
	topicIds:[String]
	"""参与活动时间"""
	dateScopeRequest:DateScopeRequest1
	"""审核方式"""
	auditType:AuditType
	"""心得参与形式"""
	participateType:ParticipateType
	"""是否要心得被删除的数据 默认要"""
	isDelete:Boolean
}
input StudentExperienceSchemeLearningRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.studentlearningExperience.StudentExperienceSchemeLearningRequest") {
	"""参训资格ID"""
	qualificationIds:[String]
	"""学号"""
	studentNos:[String]
	"""方案id"""
	schemeIds:[String]
	"""学习方式id"""
	learningIds:[String]
}
"""用户属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:01
"""
input UserPropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.user.UserPropertyRequest") {
	"""所属地区路径"""
	regionList:[RegionRequest]
	"""工作单位名称"""
	companyName:String
}
"""用户信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:00
"""
input UserRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.schemeLearningExport.nested.user.UserRequest") {
	"""用户id"""
	userIdList:[String]
	"""账户id"""
	accountIdList:[String]
	"""用户属性"""
	userProperty:UserPropertyRequest
}
enum AuditType @type(value:"com.fjhb.ms.course.learning.query.constants.AuditType") {
	AUTO_AUDIT
	MANUAL_AUDIT
}
enum ExperienceType @type(value:"com.fjhb.ms.course.learning.query.constants.ExperienceType") {
	SCHEME
	COURSE
}
enum ParticipateType @type(value:"com.fjhb.ms.course.learning.query.constants.ParticipateType") {
	SUBMIT_FILE
	EDIT_ONLINE
}
enum StudentLearningExperienceStatus @type(value:"com.fjhb.ms.course.learning.query.constants.StudentLearningExperienceStatus") {
	SUBMITING
	SUBMITTED
	PASS
	RETURNED
}
enum SortPolicy @type(value:"com.fjhb.ms.scheme.learning.query.kernel.constants.SortPolicy") {
	ASC
	DESC
}
enum StudentSchemeLearningSortField @type(value:"com.fjhb.ms.scheme.learning.query.kernel.constants.StudentSchemeLearningSortField") {
	REGISTER_TIME
	SCHEME_YEAR
}

scalar List
