<template>
  <el-card shadow="never" class="m-card f-mb15">
    <!--条件查询-->
    <el-row :gutter="16" class="m-query">
      <el-form :inline="true" label-width="auto">
        <!-- <el-col :sm="12" :md="8" :xl="6">
          <el-form-item label="集体报名批次号">
            <el-input v-model="pageQueryParam.orderNoList" clearable placeholder="请输入集体报名批次号" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item label="发票状态">
            <el-select v-model="pageQueryParam.invoiceStatusList" clearable placeholder="请选择发票状态">
              <el-option v-for="item in invoiceStatus" :label="item.name" :value="item.value" :key="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->

        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item label="订单号">
            <el-input v-model="pageQueryParam.orderNoList" clearable placeholder="请输入订单号" />
          </el-form-item>
        </el-col>

        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item>
            <el-button type="primary" @click="search">查询</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <!--表格-->
    <el-table stripe :data="pageData" max-height="500px" class="m-table">
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="订单号" min-width="220" fixed="left">
        <template slot-scope="scope">
          {{ scope.row.associationId }}
          <p><el-tag type="success" size="mini" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag></p>
        </template>
      </el-table-column>
      <el-table-column label="退款状态" min-width="130">
        <template slot-scope="scope">
          <el-badge
            is-dot
            :type="refundStatusMapType[scope.row.orderReturnStatus]"
            class="badge-status"
            v-if="refundStatusMapName[scope.row.orderReturnStatus]"
          >
            {{ refundStatusMapName[scope.row.orderReturnStatus] }}</el-badge
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="付款金额(元)" width="140" align="right">
        <template slot-scope="scope">
          {{ scope.row.payAmount || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="开票金额(元)" width="140" align="right">
        <template slot-scope="scope">
          {{ scope.row.totalAmount || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="配送方式" width="120" align="center">
        <template slot-scope="scope">
          <el-popover
            placement="right"
            width="600"
            trigger="hover"
            popper-class="m-popover"
            v-if="scope.row.shippingMethod == 2"
          >
            <el-tag
              type="info"
              class="f-mr5"
              effect="dark"
              v-if="scope.row.deliveryStatus == 0 || scope.row.deliveryStatus == 1"
              >未就绪</el-tag
            >
            <el-tag type="success" class="f-mr5" effect="dark" v-else-if="scope.row.deliveryStatus == 3">已配送</el-tag>
            <el-form
              :inline="true"
              label-width="85px"
              class="m-text-form is-border-bottom f-mt15"
              v-if="scope.row.deliveryStatus == 3"
            >
              <el-col :span="24">
                <el-form-item label="快递公司：">{{ scope.row.expressCompanyName || '-' }}</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="运单号：">
                  {{ scope.row.expressNo || '-' }}
                  <a class="f-cb f-link f-underline f-ml10" @click="copyAndGoSearch(scope.row.expressNo, $event)"
                    >复制运单号并查询</a
                  >
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="发货时间：">{{ scope.row.shipped || '-' }}</el-form-item>
              </el-col>
            </el-form>
            <el-form :inline="true" label-width="85px" class="m-text-form f-mt15">
              <el-col :span="24">
                <el-form-item label="收货地址："
                  >{{ getRegionName(scope.row.deliveryRegion) }}&nbsp; {{ scope.row.deliveryAddress || '-' }}&nbsp;
                  <span></span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="收货人：">{{ scope.row.consignee || '-' }}</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="手机号码：">{{ scope.row.deliveryphone || '-' }}</el-form-item>
              </el-col>
            </el-form>
            <span slot="reference" class="f-link f-cb">邮寄</span>
          </el-popover>
          <el-popover
            placement="right"
            width="600"
            trigger="hover"
            popper-class="m-popover"
            v-else-if="scope.row.shippingMethod == 1"
          >
            <el-tag
              type="info"
              class="f-mr5"
              effect="dark"
              v-if="scope.row.deliveryStatus == 0 || scope.row.deliveryStatus == 1"
              >未就绪</el-tag
            >
            <el-tag type="success" class="f-mr5" effect="dark" v-else-if="scope.row.deliveryStatus == 2">已配送</el-tag>
            <el-form
              :inline="true"
              label-width="85px"
              class="m-text-form is-border-bottom f-mt15"
              v-if="scope.row.deliveryStatus == 2"
            >
              <el-col :span="24">
                <el-form-item label="领取人：">{{ scope.row.takePerson || '-' }}</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="手机号：">{{ scope.row.takePhone || '-' }}</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="取货时间：">{{ scope.row.taken || '-' }}</el-form-item>
              </el-col>
            </el-form>
            <el-form :inline="true" label-width="85px" class="m-text-form f-mt15">
              <el-col :span="24">
                <el-form-item label="收货人：">{{ scope.row.name || '-' }}</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="手机号码：">{{ scope.row.phone || '-' }}</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="证件号：">{{ scope.row.idCard || '-' }}</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="自取地址：">{{ scope.row.takePointPickupLocation || '-' }}</el-form-item>
              </el-col>
            </el-form>
            <span slot="reference" class="f-link f-cb">自取</span>
          </el-popover>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="购买人信息" min-width="240">
        <template slot-scope="scope">
          <p>姓名：{{ scope.row.name || '-' }}</p>
          <p>证件号：{{ scope.row.idCard || '-' }}</p>
          <p>手机号：{{ scope.row.phone || '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="发票抬头" min-width="300">
        <template slot-scope="scope">【单位】{{ scope.row.title }}</template>
      </el-table-column>
      <el-table-column label="统一社会信用代码" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.taxpayerNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="增票信息" min-width="360">
        <template slot-scope="scope">
          <p class="f-flex">
            <span>银行账户：</span>
            <span class="f-flex-sub">{{ scope.row.account || '-' }}</span>
          </p>
          <p class="f-flex">
            <span>开户银行：</span>
            <span class="f-flex-sub">{{ scope.row.bankName || '-' }}</span>
          </p>
        </template>
      </el-table-column>
      <el-table-column label="申请开票时间" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.applyForDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="发票状态" min-width="130">
        <template slot-scope="scope">
          <el-badge
            is-dot
            :type="invoiceStatusMapType[scope.row.invoiceStatus]"
            class="badge-status"
            v-if="invoiceStatusMapName[scope.row.invoiceStatus]"
          >
            {{ invoiceStatusMapName[scope.row.invoiceStatus] }}
          </el-badge>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="开票时间" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.invoiceDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="发票号" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.invoiceNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            v-if="(scope.row.invoiceStatus === 0 || scope.row.invoiceStatus === 3) && !scope.row.invoiceFreezeStatus"
            @click="editInvoice(scope.row.invoiceId)"
            >修改发票信息</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="page" v-bind="page"></hb-pagination>
    <edit-special-invoice-dialog
      :dialog-ctrl.sync="editInvoiceVisible"
      :invoice-id="invoiceId"
      @callBack="doQueryPage"
    ></edit-special-invoice-dialog>
  </el-card>
</template>
<script lang="ts">
  import invoiceFetching from '@hbfe/jxjy-admin-customerService/src/personal/components/invoice-fetching.vue'
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { UiPage, Query } from '@hbfe/common'
  import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
  import QueryOffLinePageInvoiceParam from '@api/service/management/trade/single/invoice/query/vo/QueryOffLinePageInvoiceParam'
  import {
    BillStatusChangeTimeRequest,
    DateScopeRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import {
    InvoiceStatusEnum,
    OrderReturnStatusEnum,
    TitleTypeEnum
  } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import {
    DeliveryStatusEnum,
    DeliveryWayEnum
  } from '@api/service/management/trade/single/invoice/enum/DeliveryInvoiceEnum'
  import EditSpecialInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/edit-special-invoice-dialog.vue'
  import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
  import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  @Component({
    components: {
      EditSpecialInvoiceDialog,
      invoiceFetching
    }
  })
  export default class extends Vue {
    // 页面分页控件
    page: UiPage
    // 分页查询
    query: Query = new Query()

    //接口查询参数
    pageQueryParam: QueryOffLinePageInvoiceParam = new QueryOffLinePageInvoiceParam()

    //订单发票列表
    pageData: Array<OffLinePageInvoiceVo> = new Array<OffLinePageInvoiceVo>()

    /**
     * 打开-弹窗标识
     */
    fetchingDialog = false
    // 用户id
    userId = ''
    /**
     * 地区数组
     */
    regionMapArray = new Array<{ code: string; name: string }>()

    invoiceId = ''
    //处理发票弹窗
    editInvoiceVisible = false
    // 订单来源枚举
    SaleChannelEnum = SaleChannelEnum

    //发票状态
    invoiceStatus = [
      {
        name: '请选择发票状态',
        value: null
      },
      {
        name: '待开票',
        value: InvoiceStatusEnum.NOTPTOOPEN
      },
      {
        name: '开票中',
        value: InvoiceStatusEnum.OPENING
      },
      {
        name: '开票成功',
        value: InvoiceStatusEnum.OPEMSUCCESS
      },
      {
        name: '开票失败',
        value: InvoiceStatusEnum.OPENERROR
      }
    ]
    refundStatusMapName = {
      [OrderReturnStatusEnum.RETURNSING]: '退款中',
      [OrderReturnStatusEnum.RETURNSUCCESS]: '退款成功'
    }
    refundStatusMapType = {
      [OrderReturnStatusEnum.DIDNOTRETURN]: 'info',
      [OrderReturnStatusEnum.RETURNSING]: 'primary',
      [OrderReturnStatusEnum.RETURNSUCCESS]: 'success'
    }
    invoiceStatusMapName = {
      [InvoiceStatusEnum.NOTPTOOPEN]: '待开票',
      [InvoiceStatusEnum.OPENING]: '开票中',
      [InvoiceStatusEnum.OPENERROR]: '开票失败',
      [InvoiceStatusEnum.OPEMSUCCESS]: '开票成功'
    }

    invoiceStatusMapType = {
      [InvoiceStatusEnum.NOTPTOOPEN]: 'info',
      [InvoiceStatusEnum.OPENING]: 'primary',
      [InvoiceStatusEnum.OPENERROR]: 'danger',
      [InvoiceStatusEnum.OPEMSUCCESS]: 'success'
    }
    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }

    // 配送状态
    deliveryStatusMapType = {
      [DeliveryStatusEnum.NOTREADY]: 'info',
      [DeliveryStatusEnum.READY]: 'info',
      [DeliveryStatusEnum.SELFFETCHED]: 'success',
      [DeliveryStatusEnum.DELIVERED]: 'primary'
    }

    constructor() {
      super()

      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }

    /**
     * 动态获取映射
     */
    get regionCodeNameMap() {
      const map = {}
      this.regionMapArray.forEach(({ code, name }) => {
        map[code] = name
      })
      return map
    }

    /**
     * UI 地区回显
     * @param code
     */
    getRegionName(code: string) {
      return this.regionCodeNameMap[code] || this.regionCodeNameMap[`/${code}`]
    }

    @Prop({
      type: UserDetailVo,
      default: new UserDetailVo()
    })
    userData!: UserDetailVo
    @Watch('userData', {
      deep: true,
      immediate: true
    })
    async userDataChange({ userName, idCard, phone, userId, loginAccount }: UserDetailVo) {
      this.pageQueryParam.userName = userName
      this.pageQueryParam.idCard = idCard
      this.pageQueryParam.phone = phone
      this.pageQueryParam.loginAccount = loginAccount
      this.userId = userId
      if (userName || idCard || phone) {
        await this.search()
      } else {
        this.pageData = []
      }
    }
    /**
     * 查询发票分页
     */
    async doQueryPage() {
      console.group(
        '%c%s',
        'padding:3px 60px;color:#fff;background-image: linear-gradient(to right, #ffa17f, #00223e)',
        'schemeRefundList调试'
      )
      console.log(this.userId, 'this.userId')
      console.groupEnd()
      if (!this.userId) {
        return
      }
      this.query.loading = true
      const queryOffLineInvoice = TradeModule.singleTradeBatchFactor.invoiceFactor.queryOffLineInvoice
      try {
        this.pageData = await queryOffLineInvoice.offLinePageVatspecialplaInvoiceInServicer(
          this.page,
          this.pageQueryParam
        )
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        this.query.loading = false
        this.getBusinessRegionList()
      }
    }
    async search() {
      this.page.pageNo = 1
      await this.doQueryPage()
    }
    //修改发票信息
    editInvoice(id: string) {
      this.invoiceId = id
      this.editInvoiceVisible = true
    }
    async fetchingPopup() {
      this.fetchingDialog = true
    }
    // 注释=======================
    //获取所在地区中文
    async getBusinessRegions(regionCodes: string[]) {
      const cleanedRegionCodes = regionCodes
        .map((code) => (code.startsWith('/') ? code.substring(1) : code))
        .filter((code) => code)

      if (!cleanedRegionCodes.length) {
        return
      }

      const regionIdLists = cleanedRegionCodes.map((code) => code.split('/'))
      const allRegionIds = [].concat(...regionIdLists)
      const allRegionDetails = await QueryPhysicalRegion.querRegionDetil(allRegionIds)

      this.regionMapArray = regionIdLists.map((regionIds, index) => {
        const regionNames = regionIds.map((id) => {
          const detail = allRegionDetails.find((detail) => detail.id === id)
          return detail?.name || ''
        })
        const fullRegionName = regionNames.join(' ')

        return {
          code: `/${cleanedRegionCodes[index]}`,
          name: fullRegionName
        }
      })
    }

    getBusinessRegionList() {
      const regionCodes = this.pageData.map((item) => item.deliveryRegion)
      const cleanedRegions = regionCodes.filter((region) => region != null)
      this.getBusinessRegions(Array.from(new Set(cleanedRegions)))
    }
  }
</script>
