/*
 * @Description: 资讯列表VO
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-09 19:01:15
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-03-04 16:28:57
 */
import { NewsSimpleResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import {
  TrainingChannelPageResponse,
  NewsSimpleResponse as TrainingChannelNewsSimpleResponse
} from '@api/platform-gateway/platform-training-channel-back-gateway'
import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
import { NewsStatusEnum } from '../enum/NewsStatusEnum'
export default class NewsListVo {
  // 资讯ID
  id: string
  // 发布时间
  date: string
  // 资讯标题
  title: string
  // 是否弹窗
  isPopup: boolean
  // 是否置顶
  isTop: boolean
  // 发布人 草稿时 无发布人
  issuer?: string
  // 发布人Id
  publishUserId?: string
  // 资讯类别
  categoryType: string
  // 资讯类别id (专题列表才有)
  categoryId: string
  // 父级资讯类别Id (专题列表才有)
  parentCategoryId: string
  // 资讯状态
  status: NewsStatusEnum
  // 地区路径
  areaCodePath?: string
  // 地区名称
  areaCodeName?: string
  // 专题id
  specialId = ''
  // 专题名称
  specialName = ''
  // 专题域名
  domainName = ''

  static from(newsSimpleResponse: NewsSimpleResponse, specialList?: Array<TrainingChannelPageResponse>) {
    const { newId, title, isTop, isPopUps, name, status, publishUserId, publishTime } = newsSimpleResponse
    const newsListVo = new NewsListVo()
    newsListVo.id = newId
    newsListVo.title = title
    newsListVo.date = publishTime
    newsListVo.isPopup = isPopUps
    newsListVo.isTop = isTop
    newsListVo.publishUserId = publishUserId
    newsListVo.categoryType = name
    // newsListVo.categoryId = newsSimpleResponse.c
    newsListVo.status = status
    newsListVo.areaCodePath = newsSimpleResponse.areaCodePath
    newsListVo.specialId = newsSimpleResponse.specialSubjectId
    if (specialList?.length) {
      const findSpecial = specialList.find(it => it.id === newsListVo.specialId)
      if (findSpecial) {
        newsListVo.specialName = findSpecial.name
        newsListVo.domainName = findSpecial.domainName
      }
    }
    return newsListVo
  }

  static fromTrainingChannel(
    newsSimpleResponse: TrainingChannelNewsSimpleResponse,
    specialList?: Array<TrainingChannelPageResponse>
  ) {
    const { newId, title, isTop, isPopUps, name, status, publishUserId, publishTime } = newsSimpleResponse
    const newsListVo = new NewsListVo()
    newsListVo.id = newId
    newsListVo.title = title
    newsListVo.date = publishTime
    newsListVo.isPopup = isPopUps
    newsListVo.isTop = isTop
    newsListVo.publishUserId = publishUserId
    newsListVo.categoryType = name
    newsListVo.categoryId = newsSimpleResponse.categoryId
    newsListVo.parentCategoryId = newsSimpleResponse.parentCategoryId
    newsListVo.status = status
    newsListVo.areaCodePath = newsSimpleResponse.areaCodePath
    newsListVo.specialId = newsSimpleResponse.specialSubjectId
    if (specialList?.length) {
      const findSpecial = specialList.find(it => it.id === newsListVo.specialId)
      if (findSpecial) {
        newsListVo.specialName = findSpecial.name
        newsListVo.domainName = findSpecial.domainName
      }
    }
    return newsListVo
  }

  setUserName(name: string) {
    this.issuer = name
  }
  setRegionName(region: RegionTreeVo[]) {
    this.areaCodeName = this.areaCodePath
      ?.slice(1)
      ?.split('/')
      ?.map(item => region.find(ite => ite.id === item)?.name)
      ?.join('/')
  }
}
