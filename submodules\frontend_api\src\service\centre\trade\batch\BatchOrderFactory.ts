import MutationBatchInvoiceFactory from '@api/service/centre/trade/batch/invoice/MutationBatchInvoiceFactory'
import QueryBatchInvoiceFactory from '@api/service/centre/trade/batch/invoice/QueryBatchInvoiceFactory'
import MutationBatchOrderFactory from '@api/service/centre/trade/batch/order/MutationBatchOrderFactory'
import QueryBatchOrderFactory from '@api/service/centre/trade/batch/order/QueryBatchOrderFactory'
/**
 * @description 集体缴费订单工厂
 */
class BatchOrderFactory {
  /**
   * 【查询】集体报名订单
   */
  queryBatchOrderFactory = new QueryBatchOrderFactory()
  /**
   * 【业务】集体报名订单
   */
  mutationBatchOrderFactory = new MutationBatchOrderFactory()
  /**
   *【业务】集体报名发票
   */
  mutationBatchInvoiceFactory = new MutationBatchInvoiceFactory()
  /**
   * 【查询】集体发票查询工厂
   */
  queryBatchInvoiceFactory = new QueryBatchInvoiceFactory()
}

export default BatchOrderFactory
