import { ResponseStatus, Response } from '@hbfe/common'
import MsExamQueryFrontGatewayCourseLearningForeStage, {
  AnswerPaperSort,
  Page,
  SortTypeEnum
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryForeStage'
import { ExaminationAnswerPaperResponseVo } from '@api/service/customer/exam/query/vo/ExaminationAnswerPaperResponseVo'
import { CourseQuizAnswerPaperResponseVo } from '@api/service/customer/exam/query/vo/CourseQuizAnswerPaperResponseVo'
import AfterCourseTestLastResult from '@api/service/customer/course/query/vo/AfterCourseTestLastResult'

/**
 * 获取
 */
class QueryQuizRecordList {
  // region properties

  /**
   *总数目，类型为number
   */
  totalSize = 0
  /**
   *参训资格id，类型为string
   */
  qualificationId = ''
  /**
   *课程id，类型为string
   */
  courseId = ''

  // /**
  //  *sku过滤条件，类型为SkuPropertyVo
  //  */
  // filterSkuVo = new SkuPropertyVo()
  /**
   * 课后测验列表，类型为CourseQuizAnswerPaperResponseVo[]
   */
  quizRecordList: CourseQuizAnswerPaperResponseVo[] = []
  // endregion
  // region methods

  /**
   * 获取考试记录
   */
  async queryQuizRecordList(page: Page, qualificationId: string): Promise<Array<CourseQuizAnswerPaperResponseVo>> {
    // const filter = new CommoditySkuRequest()
    // filter.skuPropertyRequest = this.filterSkuVo.convertToDto()
    const res = await MsExamQueryFrontGatewayCourseLearningForeStage.pageMyCourseQuizRecordInMyself({
      courseId: this.courseId,
      page: {
        pageSize: page.pageSize,
        pageNo: page.pageNo
      },
      qualificationId: qualificationId,
      answerPaperStatus: 2,
      answerPaperSort: AnswerPaperSort.CREATE_TIME,
      sort: SortTypeEnum.DESC
    })
    if (res.status.isSuccess()) {
      this.quizRecordList = res.data.currentPageData as CourseQuizAnswerPaperResponseVo[]
      this.totalSize = res.data.totalSize
    }
    //pageCommoditySkuCustomerPurchaseInServicer
    return this.quizRecordList
  }

  // endregion
}
export default QueryQuizRecordList
