import CoursewareType from '@api/service/management/resource/courseware/enum/CoursewareType'
import MediaSourceType, { MediaSourceTypeEnum } from '@api/service/management/resource/courseware/enum/MediaSourceType'
import {
  CoursewareCreateRequest,
  ConfigRequest,
  ResourceDataDto,
  ExtensionResourceDataDto,
  ExtensionResourceData,
  VideoTranscodeSettings
} from '@api/ms-gateway/ms-course-resource-v1'

class CreateCoursewareDto {
  /*
   课件分类
   */
  categoryId: string = undefined

  /*
   * 总时长
   */
  duration: number = undefined

  /*
   名称
   */
  name: string = undefined

  /*
   供应商id
   */
  providerId: string = undefined

  /*
   教师名称
   */
  teacherName: string = undefined

  /*
   * 教师描述信息
   */
  teacherDescription: string = undefined

  /*
   课件描述
   */
  description: string = undefined

  /*
   课件状态
   */
  enable: boolean = undefined

  /*
   课件类型
   */
  type: CoursewareType = undefined

  /**
   * 外链信息
   */
  extensionResourceDataDto?: ExtensionResourceDataDto = new ExtensionResourceDataDto()

  /**
   * 课件媒体资源
   */
  resourceDataDto?: ResourceDataDto = new ResourceDataDto()

  /**
   *媒体源
   *1：华为云
   *2：外链地址
   */
  mediaType: MediaSourceTypeEnum = undefined

  /**
   *标清地址
   */
  standardAddress: string = undefined

  /**
   *高清地址
   */
  highAddress: string = undefined

  /**
   *超清地址
   */
  superAddress: string = undefined

  toDto() {
    console.log(this.duration, 'duration')

    const dto = new CoursewareCreateRequest()
    // const configObject = new ConfigRequest()
    // configObject.resourceDataDto = this.resourceDataDto
    // configObject.extensionResourceDataDto = this.extensionResourceDataDto
    dto.categoryId = this.categoryId
    dto.coursewareName = this.name
    dto.supplierId = this.providerId
    dto.teacherName = this.teacherName
    dto.teacherDescription = this.teacherDescription
    dto.coursewareDescription = this.description
    // dto.enable = this.enable
    dto.resourceType = this.mediaType
    const extensionResourceDataDto = new ExtensionResourceDataDto()
    extensionResourceDataDto.extensionVideoInfo = new ExtensionResourceData()
    extensionResourceDataDto.extensionVideoInfo.videoInfoDtos = []
    if (this.standardAddress) {
      extensionResourceDataDto.extensionVideoInfo.videoInfoDtos.push({ path: this.standardAddress, clarity: 9 })
    }
    if (this.highAddress) {
      extensionResourceDataDto.extensionVideoInfo.videoInfoDtos.push({ path: this.highAddress, clarity: 10 })
    }
    if (this.superAddress) {
      extensionResourceDataDto.extensionVideoInfo.videoInfoDtos.push({ path: this.superAddress, clarity: 11 })
    }
    this.resourceDataDto.videoTranscodeSettings = new VideoTranscodeSettings()
    this.resourceDataDto.videoTranscodeSettings.hwySetting = {
      extractAudio: false,
      transcode: true
    }
    dto.configJSON =
      this.mediaType === MediaSourceTypeEnum.outer
        ? JSON.stringify(extensionResourceDataDto)
        : JSON.stringify(this.resourceDataDto)
    return dto
  }
}

export default CreateCoursewareDto
