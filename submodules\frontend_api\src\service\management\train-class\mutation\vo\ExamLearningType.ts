import LearningTypeBase from '@api/service/management/train-class/mutation/vo/LearningTypeBase'
import { ExamMethodEnum } from '@api/service/common/enums/train-class/ExamMethodEnum'

/**
 * 考试
 */
class ExamLearningType extends LearningTypeBase {
  // region properties

  /**
   * 前置条件id，类型为string
   */
  preconditionId = ''
  /**
   * 前置条件名称，类型为string
   */
  preconditionName = ''
  /**
   *考试说明，类型为string
   */
  description = ''
  /**
   *试卷模板ID，类型为string
   */
  paperPublishConfigureId = ''
  /**
   *试卷模板名称，类型为string
   */
  paperPublishConfigureName = ''
  /**
   *考试场次名称，类型为string
   */
  name = ''
  /**
   *允许作答次数， int -1代表不限次，类型为number
   */
  allowCount = 0
  /**
   *允许考试开始时间 , date 同培训班的学习起止时填班级的学习开始，类型为string
   */
  allowStartTime = ''
  /**
   *允许考试结束时间， date 同培训班的学习起止时填班级的学习结束，类型为string
   */
  allowEndTime = ''
  /**
   *考试时长， int 秒数，类型为number
   */
  timeLength = 0
  /**
   *班级考试考核成绩,double，类型为number
   */
  qualifiedScore = 0
  /**
   *班级考试考核成绩
   */
  examPassScore = 0
  /**
   *合格后是否允许继续作答，类型为boolean
   */
  allowAnswerIfQualified = false
  /**
   *开放题析，类型为boolean
   */
  openDissects = false
  /**
   *试题提交答案后是否可以重答，类型为boolean
   */
  questionAgainAnswer = false
  /**
   *试题呈现方式, 1 整卷，int，类型为number
   */
  questionDisplay = 0
  /**
   *多选题漏选得分方式,0 不得分 1 得全部分数 2 得一半分数 3 每个选项按平均得分，类型为number
   */
  multipleMissScorePattern = 0
  // endregion
  /**
   *成绩是否公布
   */
  gradesWhetherHide = true
  /**
   * 考试是否纳入考核
   */
  isExamAssessed = false
  /**
   * 前置条件
   * 0 无 1 完成课程学习考核
   */
  preCondition = 0
  /**
   * 考试方式：0 随考随到，1 固定考试
   */
  examPattern: ExamMethodEnum = ExamMethodEnum.on_call_exam

  // endregion
}
export default ExamLearningType
