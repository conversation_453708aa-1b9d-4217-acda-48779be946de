<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <div class="f-plr20">
      <el-button
        type="primary"
        icon="el-icon-plus"
        class="f-mb15 f-mt20"
        @click="popExamPaper"
        :disabled="examList.length > 0"
      >
        添加模拟卷
      </el-button>
      <el-table stripe :data="examList" max-height="500px" class="m-table">
        <!-- <el-table-column width="30" align="center" fixed="left">
         <template slot-scope="scope">
            &lt;!&ndash;有新增或者修改的添加 <i class="is-tag"></i>&ndash;&gt;
            <div v-if="scope.$index === 0"><i class="is-tag"></i></div>
            <div v-else></div>
          </template>
        </el-table-column>-->
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="场次名称" min-width="240" fixed="left">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <el-table-column label="试卷名称" min-width="240">
          <template slot-scope="scope">{{ scope.row.paperPublishConfigureName }}</template>
        </el-table-column>
        <el-table-column label="组卷方式" min-width="120">
          <template>智能组卷</template>
        </el-table-column>
        <el-table-column label="考试时长 / 总分" min-width="190" align="center">
          <template slot-scope="scope"> {{ scope.row.timeLength }} / 100 </template>
        </el-table-column>
        <el-table-column label="作答次数" min-width="120" align="center">
          <template slot-scope="scope">{{ scope.row.allowCount === -1 ? '不限次' : scope.row.allowCount }}</template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="editPublishExam(scope.$index, scope.row)">编辑</el-button>

            <el-popconfirm
              title="确定移除该考试？移除后学员将无法作答此试卷。"
              icon="el-icon-info"
              icon-color="red"
              confirm-button-text="确定移除"
              cancel-button-text="取消"
              @confirm="removePublishExam(scope.$index)"
            >
              <el-button
                type="text"
                size="mini"
                slot="reference"
                :disabled="routerMode === 3 && (recalculating || isIntelligenceLearning)"
                >移除</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <el-form ref="form" label-width="100px" class="m-text-form is-column f-mt30">
        <el-form-item label="前置条件：">{{ examLearningInfo.preCondition ? '完成课程学习考核' : '无' }}</el-form-item>
        <el-form-item label="培训要求：">以班级考核配置为准</el-form-item>
      </el-form>

      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">班级考试要求</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="examLearningInfo" label-width="150px" class="m-form f-mt10">
              <el-form-item label="纳入考核：" required>
                <el-radio :label="true" v-model="examLearningInfo.isExamAssessed">是</el-radio>
                <el-radio :label="false" v-model="examLearningInfo.isExamAssessed">否</el-radio>
              </el-form-item>
              <el-form-item label="成绩要求：" required>
                <p>
                  {{ examLearningInfo.isExamAssessed ? '班级考试考核成绩' : '班级考试及格分' }} ≥
                  <el-input v-model.number="examLearningInfo.examPassScore" size="small" class="input-num f-mlr5" />
                  分（总分100分，考试次数：<i class="f-cr">{{ examAllowCountConfigure }}</i> 次）
                </p>
              </el-form-item>
              <el-form-item label="前置条件：" required>
                <el-radio :label="1" v-model="examLearningInfo.preCondition">完成课程学习考核</el-radio>
                <el-radio :label="0" v-model="examLearningInfo.preCondition">无</el-radio>
              </el-form-item>
              <el-form-item label="考核要求：">
                <p>
                  1.
                  {{ examLearningInfo.isExamAssessed ? '考试纳入考核' : '班级考试不纳入考核' }}，班级考试成绩 ≥
                  {{ examLearningInfo.examPassScore }}
                  分，{{ examLearningInfo.isExamAssessed ? '视为通过' : '考试合格' }}
                </p>
                <p v-show="examLearningInfo.preCondition">2. 参加考试前需完成课程学习考核</p>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
    <!--选择试卷模板-->
    <el-drawer
      title="选择试卷模板"
      :visible.sync="uiConfig.dialog.examPaperDialogVisible"
      size="1000px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd">
        <el-row :gutter="16" class="m-query f-mt10">
          <el-form :inline="true">
            <el-col :span="10">
              <el-form-item label="试卷名称">
                <el-input v-model="pageQueryParam.name" clearable placeholder="请输入试卷名称关键字" />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="试卷分类">
                <paper-classify-tree v-model="paperCategoryId" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-button type="primary" @click="searchExamPaper">查询</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table
          stripe
          :data="publishedExamPaperList"
          v-loading="query.loading"
          max-height="500px"
          class="m-table f-mt10"
        >
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="试卷名称" min-width="300">
            <template slot-scope="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column label="组卷方式" min-width="120" align="center">
            <template>智能组卷</template>
          </el-table-column>
          <el-table-column label="试卷分类" min-width="120" align="center">
            <template slot-scope="scope">{{ scope.row.categoryName }}</template>
          </el-table-column>
          <el-table-column label="查看" width="80" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="publishedExamPaperPreview(scope.row)">预览</el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <el-checkbox
                v-model="scope.row.isSelected"
                label="选择"
                @change="
                  (value) => {
                    return handlePublishedExamPaperSelected(value, scope.row)
                  }
                "
              ></el-checkbox>
            </template>
          </el-table-column>
        </el-table>
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button @click="cancelSelectPublishedExamPaper">取消</el-button>
        <el-button type="primary" @click="confirmSelectPublishedExamPaper">确定</el-button>
      </div>
    </el-drawer>
    <!--发布/修改试卷-->
    <el-drawer
      :title="editTempExamInfoTitle"
      :visible.sync="uiConfig.dialog.examPublishDialogVisible"
      size="800px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd">
        <el-form
          ref="publishExamForm"
          label-width="auto"
          class="m-form f-mt10 f-mlr20"
          :rules="publishExamRules"
          :model="tempExamInfo"
        >
          <el-form-item label="考试方式：">线上网页考试</el-form-item>
          <el-form-item label="考试场次名称：">
            <el-input
              v-model="tempExamInfo.name"
              placeholder="请输入本场次考试名次（开放学员可见）"
              :disabled="isEditExam && routerMode === 3 && (recalculating || isIntelligenceLearning)"
            />
          </el-form-item>
          <el-form-item label="考试模板：">
            {{ tempExamInfo.paperPublishConfigureName }}
            <el-button type="text" class="f-ml10" @click="examPaperPreview(tempExamInfo)"> 预览 </el-button>
            <el-button
              type="text"
              class="f-ml10"
              @click="popExamPaper"
              :disabled="isEditExam && routerMode === 3 && (recalculating || isIntelligenceLearning)"
              >替换</el-button
            >
          </el-form-item>
          <el-form-item label="考试方式：">
            <el-radio-group v-model="tempExamInfo.examPattern" @change="changeExamPattern">
              <el-radio :label="ExamMethodEnum.on_call_exam">随到随考</el-radio>
              <el-radio :label="ExamMethodEnum.fixed_exam">
                固定考试
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-info m-tooltip-icon f-c9"></i>
                  <div slot="content">考试需在指定考试时间开始后进入考试，在指定考试时间结束时停止作答。</div>
                </el-tooltip>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <template v-if="tempExamInfo.examPattern === ExamMethodEnum.on_call_exam">
            <el-form-item label="考试起止时间：">
              <el-radio-group v-model="configExamTime">
                <el-radio :label="false">同培训班的学习起止时间</el-radio>
                <el-radio :label="true">指定考试时间</el-radio>
              </el-radio-group>
            </el-form-item>
            <!--选择 指定考试时间 后出现-->
            <el-form-item label="选择时间：" v-if="configExamTime">
              <el-date-picker
                placeholder="请选择开始时间"
                type="datetime"
                class="ml-10"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                v-model="tempExamInfo.allowStartTime"
              ></el-date-picker>
              ---
              <el-date-picker
                placeholder="请选择结束时间"
                type="datetime"
                class="ml-10"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                v-model="tempExamInfo.allowEndTime"
              ></el-date-picker>
            </el-form-item>
          </template>
          <div v-if="tempExamInfo.examPattern === ExamMethodEnum.fixed_exam">
            <el-form-item label="考试时间：" prop="allowEndTime" key="allowEndTime">
              <el-date-picker
                placeholder="请选择开始时间"
                type="datetime"
                class="ml-10"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                v-model="tempExamInfo.allowStartTime"
              ></el-date-picker>
              ---
              <el-date-picker
                placeholder="请选择结束时间"
                type="datetime"
                class="ml-10"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                v-model="tempExamInfo.allowEndTime"
              ></el-date-picker>
            </el-form-item>
          </div>

          <el-form-item label="考试时长：">
            <el-input
              v-model="tempExamInfo.timeLength"
              size="small"
              class="input-num f-mr5"
              :disabled="tempExamInfo.examPattern === ExamMethodEnum.fixed_exam"
            />分钟
          </el-form-item>
          <el-form-item label="作答次数：">
            <el-radio-group
              v-model="limitAllowCount"
              :disabled="
                (isEditExam && routerMode === 3 && (recalculating || isIntelligenceLearning)) ||
                tempExamInfo.examPattern === ExamMethodEnum.fixed_exam
              "
            >
              <el-radio :label="false">不限次</el-radio>
              <el-radio :label="true">
                限定作答
                <el-input
                  v-model="tempExamInfo.allowCount"
                  :disabled="!limitAllowCount || tempExamInfo.examPattern === ExamMethodEnum.fixed_exam"
                  size="small"
                  class="input-num f-mr5"
                />
                次
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="合格后是否允许继续作答：">
            <el-radio-group v-model="tempExamInfo.allowAnswerIfQualified" :disabled="disableExam">
              <el-radio :label="false">不能继续作答</el-radio>
              <el-radio :label="true">
                允许继续作答
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-info m-tooltip-icon f-c9"></i>
                  <div slot="content">
                    考试合格后允许继续作答，合格时间取首次合格时间。作答次数还有剩余且在培训结束时间前允许继续作答，并取当前最高分作为考试成绩。
                  </div>
                </el-tooltip>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="多选题漏选得分：">
            <el-radio-group
              v-model="tempExamInfo.multipleMissScorePattern"
              :disabled="isEditExam && routerMode === 3 && (recalculating || isIntelligenceLearning)"
            >
              <el-radio v-for="item in scoreTypeList" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="成绩公布：">
            <el-radio-group v-model="tempExamInfo.gradesWhetherHide" @change="gradesWhetherHideChange">
              <el-radio :label="true"> 交卷后立即公布（仅针对不含非主观题试卷）</el-radio>
              <el-radio :label="false" :disabled="tempExamInfo.openDissects">
                交卷后不公布
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-info m-tooltip-icon f-c9"></i>
                  <div slot="content">
                    选择“交卷后不公布”，则学员提交试卷后不显示成绩和本次考试结果，且不支持查看题析
                  </div>
                </el-tooltip>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="开放题析：">
            <el-checkbox
              v-model="tempExamInfo.openDissects"
              :disabled="
                (isEditExam && routerMode === 3 && (recalculating || isIntelligenceLearning)) ||
                !tempExamInfo.gradesWhetherHide
              "
              @change="openDissectsChange"
              >查看考试结果，开放题析</el-checkbox
            >
          </el-form-item>
          <el-form-item class="m-btn-bar is-sticky">
            <el-button @click="cancelPublishExamPaper">放弃编辑</el-button>
            <el-button type="primary" @click="publishExamPaper">发布考试</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, Prop, PropSync, Watch } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import BizExamPaperCategory from '@hbfe/jxjy-admin-components/src/biz/biz-exam-paper-category.vue'
  import { cloneDeep, parseInt } from 'lodash'
  import ExamLearningType from '@api/service/management/train-class/mutation/vo/ExamLearningType'
  import PaperPublishConfigureRequestVo from '@api/service/management/resource/exam-paper/query/vo/PaperPublishConfigureRequestVo'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import PaperPublishConfigureResponseVo from '@api/service/management/resource/exam-paper/query/vo/PaperPublishConfigureResponseVo'
  import UtilsClass from '@hbfe/jxjy-admin-common/src/util'
  import PaperClassifyTree from '@hbfe/jxjy-admin-components/src/paper-classify-tree.vue'
  import CourseLearningLearningType from '@api/service/management/train-class/mutation/vo/CourseLearningLearningType'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import DataResolve from '@api/service/common/utils/DataResolve'
  import { ExamMethodEnum } from '@api/service/common/enums/train-class/ExamMethodEnum'

  /**
   * 试卷信息
   */
  class ExamPaperInfo {
    id: string
    name: string
  }

  @Component({
    components: { PaperClassifyTree, BizExamPaperCategory }
  })
  export default class extends Vue {
    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number

    /**
     * 课程学习配置 - 仅用作判断，不能修改
     */
    @PropSync('courseLearning', { type: CourseLearningLearningType }) courseLearningInfo!: CourseLearningLearningType

    /**
     * 考试配置 - 双向绑定
     */
    @PropSync('examLearn', { type: ExamLearningType }) examLearningInfo!: ExamLearningType

    /**
     * 是否重算中
     */
    @Prop({ type: Boolean, default: false })
    recalculating: boolean
    /**
     * 是否智能学习中
     */
    @Prop({ type: Boolean, default: false })
    isIntelligenceLearning: boolean
    /**
     * 考试方式枚举
     */
    ExamMethodEnum = ExamMethodEnum
    // 考试列表
    examList: Array<ExamLearningType> = new Array<ExamLearningType>()
    // 当前编辑的考试信息
    tempExamInfo: ExamLearningType = new ExamLearningType()

    // 是否限制作答次数
    limitAllowCount = false
    // 是否指定考试时间
    configExamTime = false
    // 时间发布信息展示标题
    editTempExamInfoTitle = '发布试卷'

    // 分页参数 - 试卷
    page: UiPage
    // 分页相关 - 试卷
    query: Query = new Query()
    // 查询参数 - 试卷
    pageQueryParam: PaperPublishConfigureRequestVo = new PaperPublishConfigureRequestVo()
    // 已发布试卷列表
    publishedExamPaperList: Array<PaperPublishConfigureResponseVo> = new Array<PaperPublishConfigureResponseVo>()
    // 已选择的试卷 - 仅在保存时使用
    selectedExamPaper: PaperPublishConfigureResponseVo = new PaperPublishConfigureResponseVo()
    // 暂存选中的试卷信息 - 选择试卷
    tempSelectedExamPaperInfo: ExamPaperInfo = new ExamPaperInfo()

    publishExamRules = {
      allowEndTime: [{ require, validator: this.validateDateTime, trigger: 'change' }]
    }
    validateDateTime(rule: any, value: string, callback: Function) {
      const startTime = this.tempExamInfo.allowStartTime
      const endTime = this.tempExamInfo.allowEndTime

      if (!startTime || !endTime) {
        return callback(new Error('请选择开始和结束时间'))
      }

      const startDate = new Date(startTime).setHours(0, 0, 0, 0)
      const endDate = new Date(endTime).setHours(0, 0, 0, 0)

      if (startDate !== endDate) {
        return callback(new Error('考试起止时间不允许跨天'))
      } else {
        this.getTimeLength()
        callback()
      }
    }

    // 试卷分类Id
    paperCategoryId = ''
    /**
     * ui相关的变量控制
     */
    uiConfig = {
      // 对话框是否展示
      dialog: {
        // 选择试卷
        examPaperDialogVisible: false,
        // 发布/修改试卷
        examPublishDialogVisible: false
      }
    }

    // 多选题漏选得分可选项
    scoreTypeList = [
      { value: 0, label: '不得分' },
      { value: 1, label: '全得' },
      { value: 2, label: '得一半' },
      { value: 3, label: '平均得分' }
    ]

    /**
     * 是否勾选课程学习
     */
    get enableCourseLearning() {
      return this.courseLearningInfo.isSelected
    }

    /**
     * 考试次数
     */
    get examAllowCountConfigure() {
      return this.examLearningInfo.allowCount === -1 ? '不限' : this.examLearningInfo.allowCount
    }
    /**
     * 是否是编辑考试
     */
    get isEditExam() {
      return this.editTempExamInfoTitle == '修改试卷' ? true : false
    }
    /**
     * 考试时长
     */
    getTimeLength() {
      const timer =
        this.parseToTimestamp(this.tempExamInfo.allowEndTime) - this.parseToTimestamp(this.tempExamInfo.allowStartTime)

      const timestamp = parseInt(timer.toString(), 10)
      const seconds = Math.floor(timestamp / 1000)
      const minutes = Math.floor(seconds / 60) || 0
      this.tempExamInfo.timeLength = parseInt(String(timer % 60000))
        ? parseInt(String(timer / 60000)) + 1
        : parseInt(String(timer / 60000)) || 0
    }
    /**
     * 年月日时分秒转时间戳
     */
    parseToTimestamp(dateStr: string) {
      if (!dateStr) return
      const [datePart, timePart] = dateStr.split(' ')
      const [year, month, day] = datePart.split('-')
      const [hours, minutes, seconds] = timePart.split(':')

      const date = new Date(
        Number(year),
        Number(Number(month) - 1),
        Number(day),
        Number(hours),
        Number(minutes),
        Number(seconds)
      )
      return date.getTime()
    }

    @Watch('paperCategoryId', {
      immediate: true,
      deep: true
    })
    paperCategoryIdChange(val: string) {
      if (!this.pageQueryParam) {
        this.pageQueryParam = new PaperPublishConfigureRequestVo()
        this.pageQueryParam.status = 1
      }
      if (!this.pageQueryParam.categoryIdList) {
        this.pageQueryParam.categoryIdList = [] as string[]
      }
      if (!val) {
        this.pageQueryParam.categoryIdList = undefined
      } else {
        this.pageQueryParam.categoryIdList = [val]
      }
    }

    constructor() {
      super()
      this.page = new UiPage(this.pageExamPaper, this.pageExamPaper)
    }

    /**
     * 页面初始化
     */
    created() {
      if (this.routerMode !== 1) this.initExamList()
    }

    /**
     * 是否公布改变
     */
    gradesWhetherHideChange() {
      if (this.tempExamInfo.gradesWhetherHide === false) {
        this.tempExamInfo.openDissects = false
      }
    }
    /**
     * 若勾选开放题解析，则不能配置交卷后不公布成绩
     */
    openDissectsChange() {
      if (this.tempExamInfo.openDissects) {
        this.tempExamInfo.gradesWhetherHide = true
      }
    }

    /**
     * 初始化试卷列表
     */
    initExamList() {
      this.examList = new Array<ExamLearningType>()
      if (this.examLearningInfo.paperPublishConfigureId) {
        const option = new ExamLearningType()
        Object.assign(option, this.examLearningInfo)
        this.examList.push(option)
      }
    }

    get disableExam() {
      return (
        (this.isEditExam && this.routerMode === 3 && (this.recalculating || this.isIntelligenceLearning)) ||
        this.tempExamInfo.examPattern === ExamMethodEnum.fixed_exam
      )
    }

    /**
     * 显示选择模拟卷的抽屉
     */
    async popExamPaper() {
      const validResult = this.validatePermission()
      if (!validResult) return
      this.uiConfig.dialog.examPaperDialogVisible = true
      this.uiConfig.dialog.examPublishDialogVisible = false
      this.page.pageNo = 1
      this.pageQueryParam = new PaperPublishConfigureRequestVo()
      this.pageQueryParam.status = 1
      this.selectedExamPaper = new PaperPublishConfigureResponseVo()
      await this.searchExamPaper()
    }

    /**
     * 试卷分页查询
     */
    async searchExamPaper() {
      this.page.pageNo = 1
      await this.pageExamPaper()
    }

    /**
     * 试卷分页查询
     */
    async pageExamPaper() {
      this.query.loading = true
      try {
        const response = await ResourceModule.queryExamPaperFactory.queryExamPaperList.queryExamPaperList(
          this.page,
          this.pageQueryParam
        )
        this.publishedExamPaperList = response.status.isSuccess()
          ? response.data
          : ([] as PaperPublishConfigureResponseVo[])
        console.log('publishedExamPaperList', this.publishedExamPaperList)
      } catch (e) {
        console.log('获取试卷列表失败：', e)
      } finally {
        this.query.loading = false
      }
    }

    /**
     * 取消（添加模拟卷）
     */
    cancelSelectPublishedExamPaper() {
      this.uiConfig.dialog.examPaperDialogVisible = false
    }

    /**
     * 确定（添加模拟卷）
     */
    confirmSelectPublishedExamPaper() {
      // 校验是否能够选中 - 按课程id抽题要先选择课程
      if (this.selectedExamPaper.type === 2) {
        const hasConfigureCourse = CreateSchemeUtils.treeFind<Classification>(
          this.courseLearningInfo.classification.childOutlines,
          (node: Classification) => {
            return node.coursePackageId && node.coursePackageId !== ''
          },
          'childOutlines'
        )
        const hasConfigCourseLearning = this.courseLearningInfo.isSelected && hasConfigureCourse ? true : false
        if (!hasConfigCourseLearning) {
          this.$alert('该试卷抽题方式为“按课程id出题”，请先勾选课程学习', '', {
            confirmButtonText: '确定',
            type: 'error',
            callback: (action) => {
              console.log(action)
            }
          })
          return
        }
      }
      if (!this.tempSelectedExamPaperInfo.name && !this.tempSelectedExamPaperInfo.id) {
        this.$message.error('请选择试卷模板，不能为空！')
        return
      }
      this.tempExamInfo.isSelected = true
      this.tempExamInfo.paperPublishConfigureId = this.tempSelectedExamPaperInfo.id
      this.tempExamInfo.paperPublishConfigureName = this.tempSelectedExamPaperInfo.name
      this.limitAllowCount = this.tempExamInfo.allowCount > 0
      this.configExamTime = !!(this.tempExamInfo.allowStartTime && this.tempExamInfo.allowEndTime)
      this.uiConfig.dialog.examPaperDialogVisible = false
      this.uiConfig.dialog.examPublishDialogVisible = true
    }

    /**
     * 试卷预览
     */
    async examPaperPreview(row: ExamLearningType) {
      // TODO
      // 请求获取试卷时间入参
      const res = await ResourceModule.queryExamPaperFactory
        .queryExamPaperDetail(row.paperPublishConfigureId)
        .queryAutomaticExamPaperDetail()
      let time = '60'
      if (res.status.isSuccess()) {
        time = (res.data.suggestionTimeLength * 60).toString()
      } else {
        this.$message.error('获取试卷详情失败！')
      }
      window.open(
        [
          `/exam/mockPreview`,
          `?id=${row.paperPublishConfigureId}&timeLength=${time}&examName=${row.paperPublishConfigureName}&multipleMissScorePattern=${row.multipleMissScorePattern}`
        ].join('')
      )
    }

    /**
     * 放弃编辑（发布考试）
     */
    cancelPublishExamPaper() {
      this.tempExamInfo = new ExamLearningType()
      this.uiConfig.dialog.examPublishDialogVisible = false
    }

    /**
     * 发布考试
     */
    publishExamPaper() {
      if (this.configExamTime && (!this.tempExamInfo.allowStartTime || !this.tempExamInfo.allowEndTime)) {
        this.$message.error('请配置指定考试时间')
        return
      }
      if (
        this.configExamTime &&
        !DataResolve.validTwoDateOrder(this.tempExamInfo.allowStartTime, this.tempExamInfo.allowEndTime)
      ) {
        this.$message.error('起始时间不能超过结束时间')
        return
      }
      if (!this.tempExamInfo.name) {
        this.$message.error('请配置场次名称，不可为空！')
        return
      }
      if (!this.tempExamInfo.timeLength) {
        this.$message.error('请配置考试时长，不可为空或0！')
        return
      }
      let validResult = DataResolve.validNumber(this.tempExamInfo.timeLength)
      if (!validResult) {
        this.$message.error('请配置数字格式的考试时长。')
        return
      }
      if (this.limitAllowCount) {
        if (!this.tempExamInfo.allowCount) {
          this.$message.error('请配置限定作答次数，不能为空或0！')
          return
        }
        validResult = DataResolve.validNumber(this.tempExamInfo.allowCount)
        if (!validResult) {
          this.$message.error('请输入数字格式的作答次数。')
          return
        }
      } else {
        this.tempExamInfo.allowCount = -1
      }

      ;(this.$refs.publishExamForm as any).validate((valid: boolean) => {
        if (valid) {
          if (!this.configExamTime && this.tempExamInfo.examPattern === ExamMethodEnum.on_call_exam) {
            this.tempExamInfo.allowStartTime = undefined
            this.tempExamInfo.allowEndTime = undefined
          }
          this.tempExamInfo.timeLength = Number(this.tempExamInfo.timeLength)
          this.tempExamInfo.allowCount = Number(this.tempExamInfo.allowCount)
          // 发布考试（本地）
          this.examList = new Array<ExamLearningType>()

          console.group(
            '%c%s',
            'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#800080, #C71585)',
            'this.tempExamInfo调试输出'
          )
          console.log(this.tempExamInfo)
          console.count('this.tempExamInfo输出次数')
          console.groupEnd()
          this.examList.push(this.tempExamInfo)
          // 发布考试（远端）
          this.examLearningInfo = cloneDeep(this.tempExamInfo)
          this.uiConfig.dialog.examPublishDialogVisible = false
        }
      })
    }

    /**
     * 编辑考试内容
     */
    editPublishExam(index: number, item: ExamLearningType) {
      const validResult = this.validatePermission()
      if (!validResult) return
      this.tempExamInfo = cloneDeep(item)
      if (item.gradesWhetherHide === undefined) {
        this.tempExamInfo.gradesWhetherHide = true
      }
      this.tempExamInfo.allowCount = this.tempExamInfo.allowCount === -1 ? 0 : this.tempExamInfo.allowCount
      this.limitAllowCount = this.tempExamInfo.allowCount > 0
      this.configExamTime = !!(this.tempExamInfo.allowStartTime && this.tempExamInfo.allowEndTime)
      this.editTempExamInfoTitle = '修改试卷'
      this.uiConfig.dialog.examPublishDialogVisible = true
    }

    /**
     * 移除考试
     */
    removePublishExam(index: number) {
      const validResult = this.validatePermission()
      if (!validResult) return
      this.editTempExamInfoTitle = '发布试卷'
      this.examList.splice(index, 1)
      // 重置正在编辑的考试对象
      this.tempExamInfo = new ExamLearningType()
      this.examLearningInfo = new ExamLearningType()
    }

    /**
     * 校验是否允许操作
     */
    validatePermission() {
      let result = true
      if (!this.examLearningInfo.isSelected) {
        result = false
        this.$message.error('请先勾选“班级考试”的考试内容，再配置试卷！')
      }
      return result
    }

    /**
     * 试卷预览 - 选择试卷
     */
    publishedExamPaperPreview(row: PaperPublishConfigureResponseVo) {
      //   const targetUrl = `/resource/exam-paper/detail/${row.id}`
      //   UtilsClass.openUrl(targetUrl)
      if (row.type === 2) {
        this.$message.warning('当前试卷按照学员选课ID出题，暂不支持预览')
        return
      }
      const time = (row.suggestionTimeLength * 60).toString()
      window.open([`/exam/mockPreview`, `?id=${row.id}&timeLength=${time}&examName=${row.name}`].join(''))
    }

    /**
     * 响应试卷列表是否选中
     */
    handlePublishedExamPaperSelected(value: boolean, row: PaperPublishConfigureResponseVo) {
      this.publishedExamPaperList?.forEach((el) => {
        el.isSelected = false
      })
      if (value) {
        row.isSelected = true
        this.selectedExamPaper = row
        this.tempSelectedExamPaperInfo = new ExamPaperInfo()
        this.tempSelectedExamPaperInfo.id = row.id
        this.tempSelectedExamPaperInfo.name = row.name
      } else {
        this.tempSelectedExamPaperInfo = new ExamPaperInfo()
      }
    }

    /**
     * 重置考试配置
     */
    resetExam() {
      // 重置发布试卷标题
      this.editTempExamInfoTitle = '发布试卷'
      // 重置考试配置列表
      this.examList = [] as ExamLearningType[]
      // 重置正在编辑的考试对象
      this.tempExamInfo = new ExamLearningType()
    }
    /**
     * 考试方式更改
     */
    changeExamPattern(val: number) {
      this.tempExamInfo.allowStartTime = ''
      this.tempExamInfo.allowEndTime = ''
      if (val === ExamMethodEnum.fixed_exam) {
        this.limitAllowCount = true
        this.tempExamInfo.allowCount = 1
        this.tempExamInfo.allowAnswerIfQualified = false
      } else {
        this.limitAllowCount = false
        this.tempExamInfo.allowCount = 0
      }
    }
    /**
     * 考试开始时间更改
     */
    changeStartTime(val: any) {
      if (
        this.parseToTimestamp(this.tempExamInfo.allowStartTime) >=
          this.parseToTimestamp(this.tempExamInfo.allowEndTime) &&
        this.parseToTimestamp(this.tempExamInfo.allowStartTime) &&
        this.parseToTimestamp(this.tempExamInfo.allowEndTime)
      ) {
        this.tempExamInfo.allowStartTime = ''
        this.tempExamInfo.allowEndTime = ''
        this.tempExamInfo.timeLength = 0
        this.$message.error('考试结束时间必须大于考试开始时间，请重新选择考试起止时间！')
      }
    }
  }
</script>
