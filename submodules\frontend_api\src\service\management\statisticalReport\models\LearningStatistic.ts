import CourseLearningStatistic from '@api/service/management/statisticalReport/models/CourseLearningStatistic'
import ExamLearningStatistic from '@api/service/management/statisticalReport/models/ExamLearningStatistic'

/**
 * @description 学习统计信息
 */
export class LearningStatistic {
  /**
   * 净报名人次
   */
  netRegisterCount = 0
  /**
   * 考核通过人次
   */
  qualifiedCount = 0
  /**
   * 地区人数
   */
  regionPeopleCount = 0
  /**
   * 课程学习统计
   */
  courseLearningStatistic = new CourseLearningStatistic()
  /**
   * 考试统计
   */
  examLearningStatistic = new ExamLearningStatistic()
  /**
   * 期别合格数
   */
  issueQualifiedCount = 0
  /**
   * 期别未合格数
   */
  issueUnQualifiedCount = 0
  /**
   * 问卷未提交的学院方案参训资格数
   */
  questionnaireUnSubmitCount = 0
  /**
   * 问卷已提交的学院方案参训资格数
   */
  questionnaireSubmitCount = 0
}

export class LearningTableInfo { }
