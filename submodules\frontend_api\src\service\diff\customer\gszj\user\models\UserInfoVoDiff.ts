import UserInfoVo from '@api/service/customer/user/query/vo/UserInfoVo'
import {
  AreaTrainingChannelInfoResponse,
  StudentUserInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import QueryDictionaryType from '@api/service/diff/common/gszj/basic-data-dictionary/query/QueryDictionaryType'
import { DictionaryTypeEnum } from '@api/service/diff/common/gszj/basic-data-dictionary/query/enum/DictionaryType'

export default class UserInfoVoDiff extends StudentUserInfoResponse {
  /**
   * 工作单位性质
   */
  unitNature = ''
  /**
   * 工作单位性质名字
   */
  unitNatureName = ''

  /**
   * 在编情况
   */
  staffingStatus = ''
  /**
   * 在编情况名字
   */
  staffingStatusName = ''

  /**
   * 是否在专技岗位工作
   */
  isZjPosition = ''
  /**
   * 是否在专技岗位工作名字
   */
  isZjPositionName = ''

  /**
   * 职称系列
   */
  titleSeries = ''
  /**
   * 职称系列名字
   */
  titleSeriesName = ''

  /**
   * 职称专业（文本）
   */
  titleProfessional = ''

  /**
   * 现有职称等级
   */
  titleGrade = ''
  /**
   * 现有职称等级名字
   */
  titleGradeName = ''
  /**
   * 现有职称资格名称（文本）
   */
  titleQualificationName = ''
  /**
   * 现有职称有效范围
   */
  titleEffectiveRange = ''

  /**
   * 现有职称有效范围名字
   */
  titleEffectiveRangeName = ''
  /**
   * 最高学历(文本)
   */
  highestEducationLevel = ''

  from(data: AreaTrainingChannelInfoResponse) {
    this.unitNature = data.unitNature
    this.unitNatureName = this.getDictionaryTypeName(DictionaryTypeEnum.GSZJ_UNIT_NATURE, this.unitNature)

    this.staffingStatus = data.staffingStatus
    this.staffingStatusName = this.getDictionaryTypeName(DictionaryTypeEnum.GSZJ_STAFFING_STATUS, this.staffingStatus)

    this.isZjPosition = data.isZJPosition
    this.isZjPositionName = data.isZJPosition === '1' ? '是' : '否'

    this.titleSeries = data.titleSeries
    this.titleSeriesName = this.getDictionaryTypeName(DictionaryTypeEnum.GSZJ_TITLE_SERIES, this.titleSeries)

    this.titleProfessional = data.titleProfessional

    this.titleGrade = data.titleGrade
    this.titleGradeName = this.getDictionaryTypeName(DictionaryTypeEnum.GSZJ_TITLE_GRADE, this.titleGrade)

    this.titleQualificationName = data.titleQualificationName

    this.titleEffectiveRange = data.titleEffectiveRange
    this.titleEffectiveRangeName = this.getDictionaryTypeName(
      DictionaryTypeEnum.GSZJ_TITLE_EFFECTIVE_RANGE,
      this.titleEffectiveRange
    )

    this.highestEducationLevel = data.highestEducationLevel
  }
  getDictionaryTypeName(type: DictionaryTypeEnum, value: string) {
    const data = QueryDictionaryType.dictionaryTypeMap.get(type)
    return data.find((item) => {
      return item.id === value
    })?.name
  }
  /**
   * 用于判断当前单位性质是否属于特定的编制状态(“事业单位”或“中央在甘单位”或“外省企业在甘单位”)
   */
  get isStaffingStatus() {
    const validValues = [
      'jxjyGszjUnitNatureDict0000000002',
      'jxjyGszjUnitNatureDict0000000005',
      'jxjyGszjUnitNatureDict0000000006'
    ]
    const unitNature = this.unitNature
    return validValues.includes(unitNature)
  }
  /**
   * 职称系列是否选择无
   */
  get professionalSeriesIsNull() {
    return this.titleSeries == 'jxjyGszjTitleSeries0000000000032'
  }
  /**
   * 是否存在为空的值
   */
  get isEmpty() {
    return !(
      this.unitNature &&
      (this.isStaffingStatus ? this.staffingStatus : true) &&
      this.isZjPosition &&
      this.titleSeries &&
      // 当职称系列不是“无”时，才需要校验以下字段
      (!this.professionalSeriesIsNull ? this.titleProfessional : true) &&
      (!this.professionalSeriesIsNull ? this.titleGrade : true) &&
      (!this.professionalSeriesIsNull ? this.titleQualificationName : true) &&
      (!this.professionalSeriesIsNull ? this.titleEffectiveRange : true) &&
      this.highestEducationLevel
    )
  }
}
