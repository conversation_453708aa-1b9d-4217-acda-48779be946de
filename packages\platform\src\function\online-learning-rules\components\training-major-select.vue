<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-07-08 14:50:10
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2025-05-29 15:38:24
 * @Description: 科目类型选择器
-->
<template>
  <el-cascader
    style="width: 60%"
    ref="elCascaderRef"
    v-if="show"
    :props="props"
    :value="selectIds"
    @input="setSelectValue"
    :options="options"
    :placeholder="placeholder"
    clearable
    v-bind="$attrs"
    filterable
    :show-all-levels="false"
    @remove-tag="handleRemoveTag"
  ></el-cascader>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Ref, Emit } from 'vue-property-decorator'
  import CommonSkuMixins from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSkuMixins'
  import QueryTrainingCategory from '@api/service/common/basic-data-dictionary/query/QueryTrainingCategory'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import SocietyTrainingMajorVo from '@api/service/common/basic-data-dictionary/query/vo/SocietyTrainingMajorVo'
  import AssembleTree from '@api/service/common/utils/AssembleTree'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import CommonSchemeListModel from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSchemeListModel'
  import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'
  @Component
  export default class extends Vue {
    @Ref('elCascaderRef') elCascaderRef: any
    trainingMajorList: Array<TrainingCategoryVo> = new Array<TrainingCategoryVo>()
    show = true

    options = new Array<SocietyTrainingMajorVo>()
    societyMajorOptions = new Array<SocietyTrainingMajorVo>()
    props = {}

    /**
     * 行业id
     */
    @Prop({
      type: String,
      default: ''
    })
    industryId: string

    //  行业属性id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string

    //行业属性分类id
    @Prop({
      type: String,
      default: ''
    })
    categoryCode: IndustryPropertyCodeEnum
    /**
     * 提示语
     */
    @Prop({
      type: String,
      default: '请选择'
    })
    placeholder: string

    /**
     * 当前存储行业信息
     */
    commonSchemeListModel = CommonSchemeListModel

    /**
     * 当前点击移除的值
     */
    removeValue: string[] = []

    /**
     * 选项值
     */
    @Prop({
      type: Array,
      default: () => new Array<string>()
    })
    value: string[]

    /**
     * 选择器类型
     */
    @Prop({
      type: String,
      default: ''
    })
    selectType: string

    /**
     * 接口返回的所有选项
     */
    allTree = new Array<SocietyTrainingMajorVo>()

    /**
     * 移除中
     */
    isHandlingRemove = false

    /**
     * 防止重复重写
     */
    deleteTagInitialized = false

    /**
     * 是否展示拦截弹窗
     */
    confirmDialogShow = false

    /**
     * 不包含行业下的行业信息
     */
    get selectSchemeIndustry() {
      return this.commonSchemeListModel.noIncludeSchemeList.filter((item) => {
        return item.sku.industry.skuPropertyValueId == this.industryId
      })
    }

    /**
     * 不包含行业下的相同行业属性
     */
    get selectSchemeValue() {
      return this.selectSchemeIndustry.some((ite) => {
        return this.removeValue.includes(ite.sku[this.selectType].skuPropertyValueId.toString())
      })
    }

    /**
     * 特殊规则下的行业信息
     */
    get specialIndustry() {
      const arr: RuleSchemeItem[] = []
      this.commonSchemeListModel.schemeSpecialRuleList.forEach((item) => {
        item.schemeList.forEach((ite) => {
          if (ite.sku.industry.skuPropertyValueId == this.industryId) {
            arr.push(ite)
          }
        })
      })
      return arr
    }

    /**
     * 特殊规则下的相同行业属性
     */
    get specialSchemeValue() {
      return this.specialIndustry.some((ite) => {
        return this.removeValue.includes(ite.sku[this.selectType].skuPropertyValueId.toString())
      })
    }

    /**
     * 关联父级map
     */
    idsMap = new Map<string, string>()
    /**
     * 已选id(选择器用-存在级联)
     */
    selectIds: string[][] = []
    /**
     * 根据末级id获取级联ids(选择器用)
     */
    getSelectIds(value: string[]) {
      this.selectIds = value.map((ite) => {
        if (this.idsMap.get(ite) && this.idsMap.get(ite) != ite && this.idsMap.get(ite) != '-1') {
          return ['-1', this.idsMap.get(ite), ite]
        } else if (ite != '-1') {
          return ['-1', ite]
        } else {
          return ['-1']
        }
      })
    }
    /**
     * 设置选项值
     */
    setSelectValue(val: string[][]) {
      let ids = val.map((item) => {
        if (item.length > 2) {
          this.idsMap.set(item[item.length - 1], item[item.length - 2])
        }
        return item[item.length - 1]
      })
      if (ids.length === 1 && ids[0] === '-1') {
        ids = []
      }

      // 获取当前选择器已勾选节点
      const cascader = this.$refs.elCascaderRef as any
      const nodes = cascader.getCheckedNodes(false, false)

      let isAllNode = false
      // 判断是否点击了所有节点，是的话将值强制改为-1
      if (nodes.length) {
        if (nodes[0].value == '-1') {
          isAllNode = true
        }
      }

      // 当前点击移除的信息
      this.removeValue = this.value.filter((item) => {
        return !ids.includes(item)
      })
      if (isAllNode) {
        // 选择全部节点的时候，直接赋值（未涉及移除）
        this.getSelectIds(ids)
        this.$emit('input', ids)
        this.$emit('changeValue', ['-1'])
        this.$nextTick(() => {
          this.updataTagName()
        })
      } else if (ids.length > this.value.length || (!this.selectSchemeValue && !this.specialSchemeValue)) {
        // 非全部节点时
        this.getSelectIds(ids)
        this.$emit('input', ids)
        this.$emit('changeValue', ids)
        this.$nextTick(() => {
          this.updataTagName()
        })
      } else {
        this.showConfirm(ids, false)
      }
    }

    /**
     * 弹出拦截提示
     */
    showConfirm(ids: string[], isRemoveAll?: boolean, removeData?: string[]) {
      if (this.confirmDialogShow) {
        return
      }
      this.confirmDialogShow = true
      const removeDialogContent =
        '当前属性对应的培训方案存在被特殊规则引用的方案，取消属性，将移除特殊规则里已添加的对应属性培训方案。是否确定取消？'
      this.$confirm(removeDialogContent, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then((e) => {
          if (removeData) {
            // 移除全部时
            this.removeValue = removeData
          }
          if (isRemoveAll) {
            this.$emit('removeSku', 'removeAll', this.industryId, this.selectType, this.removeValue)
          }
          this.$emit('removeSku', this.industryId, this.selectType, this.removeValue)
          this.getSelectIds(ids)
          this.$emit('input', ids)
          this.$emit('changeValue', ids)
          this.$nextTick(() => {
            this.updataTagName()
          })
        })
        .catch((e) => {
          this.getSelectIds(this.value)
          this.$nextTick(() => {
            this.updataTagName()
          })
          console.log(e)
        })
        .finally(() => {
          this.confirmDialogShow = false
        })
    }

    /**
     * 重置选项名称
     */
    updataTagName() {
      const cascader = this.$refs.elCascaderRef as any
      const nodes = cascader.getCheckedNodes(false, false)
      // 回显标签名称
      let presentTags = new Array<any>()
      // 去重key
      const addedKeys = new Set<string>()
      // 循环当前节点，重组标签名称
      for (const node of nodes) {
        const path = node.path
        let label = ''
        let keys = ''
        let curNode = {}
        if (path.length == 1 && path[0].value == '-1') {
          // 全部节点
          label = '全部'
          keys = '-1'
          curNode = node.pathNodes[0]
        } else if (path.length == 2) {
          // 一级节点获取当前名称
          label = node.pathNodes[1].label
          keys = node.pathNodes[1].value
          curNode = node.pathNodes[1]
        } else if (path.length >= 3) {
          // 二级节点使用一级节点名称
          label = node.pathNodes[path.length - 2].label
          keys = node.pathNodes[path.length - 2].value
          curNode = node.pathNodes[path.length - 2]
        }
        // 去重
        if (addedKeys.has(keys)) {
          continue
        }
        addedKeys.add(keys)
        presentTags.push({
          text: label,
          closable: true,
          hitState: false,
          node: curNode,
          key: keys
        })
      }
      // 判断是否点击了所有节点，是的话将值强制改为-1
      if (nodes.length) {
        if (nodes[0].value == '-1') {
          presentTags = [
            {
              text: '全部',
              closable: true,
              hitState: false,
              key: '-1',
              node: nodes[0]
            }
          ]
        }
      }
      this.$nextTick(() => {
        // 对选择器回显标签名重新赋值
        cascader.presentTags = presentTags
        // 若当前选择器已为空，则触发清空选择器方法
        if (!this.value.length) {
          cascader.handleClear()
        }
      })
    }

    /**
     * 获取科目类型
     */
    async created() {
      this.setProps()
      await this.getTrainingMajorOptions()
      await this.echo()
      // 判断是否为全选
      if (this.value.length) {
        if (this.value[0] == '-1') {
          this.allTree.forEach((item) => {
            if (item.children) {
              item.children.forEach((ite) => {
                this.value.push(ite.propertyId)
              })
            } else {
              this.value.push(item.propertyId)
            }
          })
        }
      }
      // 异步
      if (this.value.length) {
        this.societyMajorOptions[0].children.forEach((item: SocietyTrainingMajorVo) => {
          if (item.children?.some((ite) => this.value.includes(ite.propertyId))) {
            item.children.forEach((it) => {
              this.idsMap.set(it.propertyId, item.propertyId)
            })
          }
        })
      }
      this.getSelectIds(this.value)
      this.setSelectValue(this.selectIds)
      this.updataTagName()
    }

    mounted() {
      this.$nextTick(() => {
        const cascader = this.$refs.elCascaderRef as any
        if (!this.deleteTagInitialized) {
          // 重写 deleteTag 方法，传入当前 tag 对应的路径
          cascader.deleteTag = (tag: any) => {
            const currentPath = tag.node?.getValueByOption() // 获取当前 tag 对应的路径数组
            if (!currentPath) return
            // 触发 @remove-tag 事件并传参
            this.handleRemoveTag(currentPath)
            // 更新 checkedValue 并触发 setSelectValue
            const newCheckedValue = cascader.checkedValue.filter(
              (path: string[]) => !this.isEqualPath(path, currentPath)
            )
            cascader.checkedValue = newCheckedValue
            this.$nextTick(() => {
              this.setSelectValue(newCheckedValue)
            })
          }
          this.deleteTagInitialized = true
        }
      })
    }
    /**
     * 处理 tag 删除事件
     */
    handleRemoveTag(tag: any) {
      if (this.isHandlingRemove) {
        return
      }
      this.isHandlingRemove = true
      try {
        const cascader = this.$refs.elCascaderRef as any
        const checkedValue = [...cascader.checkedValue] // 当前所有选中的路径数组
        if (tag.length == 1 && tag[0] == '-1') {
          // 移除全部
          const filtered: string[][] = []
          // 过滤出当前移除的节点
          const removeData = checkedValue.map((item) => item[item.length - 1])
          // 判断当前移除全部时，是否存在与不包含方案中对应的skuId
          const selectSchemeId = this.selectSchemeIndustry.some((ite) => {
            return removeData.includes(ite.sku[this.selectType].skuPropertyValueId.toString())
          })
          // 判断是否存在特殊规则对应的skuId
          const specialSchemeId = this.specialIndustry.some((ite) => {
            return removeData.includes(ite.sku[this.selectType].skuPropertyValueId.toString())
          })
          // 移除全部时判断是否需要弹窗拦截
          if (!selectSchemeId && !specialSchemeId) {
            cascader.checkedValue = filtered
            this.setSelectValue(filtered)
          } else {
            this.showConfirm([], true, removeData)
          }
        } else if (tag.length == 2 && tag[0] == '-1' && tag[1]) {
          // 移除一级节点（全部不算）下的所有数据
          const filtered = checkedValue.filter((path) => {
            return !(path.length >= 2 && path[1] === tag[1])
          })
          cascader.checkedValue = filtered
          this.setSelectValue(filtered)
        } else {
          // 移除二级节点
          const index = checkedValue.findIndex((path) => this.isEqualPath(path, tag))
          if (index > -1) {
            checkedValue.splice(index, 1) // 删除对应项
            cascader.checkedValue = checkedValue // 更新 el-cascader 内部状态
            this.setSelectValue(checkedValue) // 触发外部 value 更新
          }
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.isHandlingRemove = false
      }
    }

    // 只比较数组前两项是否相同
    isEqualPath(a: string[], b: string[]): boolean {
      // 如果长度不足2项，按原样比较
      if (a.length < 2 || b.length < 2) {
        return a.length === b.length && a.every((item, i) => item === b[i])
      } else {
        // 只比较前两项
        return a[0] === b[0] && a[1] === b[1]
      }
    }

    /**
     * 获取培训类别
     */
    async getTrainingMajorOptions() {
      const res = await QueryTrainingCategory.queryTrainingCategoryV2(this.industryPropertyId, this.industryId)
      this.trainingMajorList = res.isSuccess()
        ? QueryTrainingCategory.trainingCategoryListV2
        : ([] as TrainingCategoryVo[])
      this.societyMajorOptions = [] as SocietyTrainingMajorVo[]
      const list = this.trainingMajorList.map((item) => {
        const option = new SocietyTrainingMajorVo()
        Object.assign(option, item)
        return option
      })
      const tree = new AssembleTree(list, 'propertyId', 'parentId')
      this.allTree = tree.assembleTree()
      const newTree = new SocietyTrainingMajorVo()
      newTree.children = this.allTree
      newTree.name = '全部'
      newTree.propertyId = '-1'
      this.societyMajorOptions = [newTree]
    }

    setProps() {
      this.props = {
        lazy: false,
        value: 'propertyId',
        label: 'optionName',
        multiple: true,
        checkStrictly: false
      }
    }

    async echo() {
      if (this.value?.length !== 2) {
        return (this.options = this.societyMajorOptions)
      }
      this.options = this.societyMajorOptions

      this.show = true
    }
  }
</script>
