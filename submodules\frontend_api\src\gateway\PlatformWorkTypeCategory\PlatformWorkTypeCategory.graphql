schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取所有工种分类"""
	getAllCategoryList:[WorkTypeCategoryResponse] @NotAuthenticationRequired
	"""获取所有第一级分类"""
	getAllTopCategoryList:[WorkTypeCategoryResponse] @NotAuthenticationRequired
	"""获取指定工种分类以及父类
		@param level 递归到第几级,<=0则一直取到没有父类为止
	"""
	getCategoryAndParents(id:String,level:Int!):[WorkTypeCategoryResponse]
	"""根据工种id集合获取每个工种id对应所属的工种分类信息
		@param containsParent是否取出对应分类的父分类
	"""
	getCategoryListByWorkTypeId(workTypeId:String,containsParent:Boolean!):[WorkTypeCategoryResponse] @NotAuthenticationRequired
	"""根据工种id集合获取每个工种id对应所属的工种分类信息
		@param containsParent是否取出对应分类的父分类
	"""
	getCategoryListByWorkTypeIds(workTypeIds:[String],containsParent:Boolean!):[WorkTypeRelationCategoryResponse]
	"""获取指定工种分类的所有子分类
		@param level 递归到第几级,<=0则一直取到没有子类为止
	"""
	getChildren(id:String,level:Int!):[WorkTypeCategoryResponse] @NotAuthenticationRequired
	"""通过Id获取工种分类信息"""
	getWorkTypeCategoryList(idList:[String]):[WorkTypeCategoryResponse] @NotAuthenticationRequired
}
type Mutation {
	"""创建工种分类"""
	createWorkTypeCategory(createDTO:WorkTypeCategoryCreateDTO!):WorkTypeCategoryResponse
	"""工种分类下移指定偏移量"""
	moveDownCategory(id:String,offset:Int!):WorkTypeCategoryResponse
	"""工种分类上移指定偏移量"""
	moveUpCategory(id:String,offset:Int!):WorkTypeCategoryResponse
	removeWorkTypeCategory(id:String!):WorkTypeCategoryResponse
	"""更新工种分类信息"""
	updateWorkTypeCategory(updateDTO:WorkTypeCategoryUpdateDTO!):WorkTypeCategoryResponse
	"""更新工种分类与工种的关系"""
	updateWorkTypeCategoryRelations(categoryId:String,workTypeIds:[String]):Void
}
input WorkTypeCategoryCreateDTO @type(value:"com.fjhb.btpx.platform.service.worktypecategory.dto.WorkTypeCategoryCreateDTO") {
	name:String!
	parentId:String
	sort:Int!
}
input WorkTypeCategoryUpdateDTO @type(value:"com.fjhb.btpx.platform.service.worktypecategory.dto.WorkTypeCategoryUpdateDTO") {
	id:String!
	name:String!
	parentId:String
	sort:Int!
}
"""工种分类
	<AUTHOR>
"""
type WorkTypeCategoryResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.WorkTypeCategoryResponse") {
	id:String
	name:String
	parentId:String
	sort:Int
	creatorId:String
	createTime:DateTime
	lastUpdateTime:DateTime
}
type WorkTypeRelationCategoryResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.WorkTypeRelationCategoryResponse") {
	workTypeId:String
	categoryList:[WorkTypeCategoryResponse]
}

scalar List
