import MsTradeQueryFrontGatewayCourseLearningBacktage, {
  Page,
  ReturnOrderRequest,
  ReturnOrderStatisticResponse,
  ReturnReasonInfoResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import ReturnOrderResponseVo from '@api/service/management/trade/single/order/query/vo/ReturnOrderResponseVo'
import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
import { RefundOrderStatusEnum } from '@api/service/common/trade/RefundOrderStatusEnum'

class QueryRefundList {
  private returnReasonCache = new Array<ReturnReasonInfoResponse>()
  /**
   * 获取退款原因
   *
   */
  async queryRefundReasonList(): Promise<Array<ReturnReasonInfoResponse>> {
    if (!this.returnReasonCache.length) {
      const res = await MsTradeQueryFrontGatewayCourseLearningBacktage.listReturnReasonInfoInSubProject()
      if (res.status.isSuccess()) {
        this.returnReasonCache = res.data
      }
    }
    return this.returnReasonCache
  }
}
export default new QueryRefundList()
