/*
 * @Description: 退款订单对账参数
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-06 09:59:49
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-01-17 18:33:46
 */
import {
  BigDecimalScopeRequest,
  CommoditySkuRequest1,
  DateScopeRequest,
  IssueInfo1,
  OrderInfoRequest,
  ReturnOrderBasicDataRequest,
  ReturnOrderRequest,
  ReturnOrderStatusChangeTimeRequest,
  SubOrderInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import SaleChannelType, { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import CheckAccountParam from './CheckAccountParam'
import { OrderRefundTypeEnum } from '@api/service/common/return-order/enums/OrderRefundType'

export default class RefundCheckAccountParam extends CheckAccountParam {
  /**
   * 退款单号
   */
  refundId?: string

  static refurnTo(refundCheckAccountParam: RefundCheckAccountParam) {
    //
    const {
      paymentAccountID,
      orderId,
      batchId,
      startDate,
      refundId,
      endDate,
      specialSubjectName,
      saleSource,
      trainingProgramId,
      periodId
    } = refundCheckAccountParam
    const returnOrderRequest = new ReturnOrderRequest()
    returnOrderRequest.returnOrderNoList = refundId ? [refundId] : undefined
    returnOrderRequest.subOrderInfo = new SubOrderInfoRequest()
    returnOrderRequest.subOrderInfo.orderInfo = new OrderInfoRequest()
    returnOrderRequest.subOrderInfo.orderInfo.channelTypesList = [1]
    returnOrderRequest.subOrderInfo.orderInfo.flowNoList = batchId ? [batchId] : undefined
    returnOrderRequest.subOrderInfo.orderInfo.orderNoList = orderId ? [orderId] : undefined
    returnOrderRequest.subOrderInfo.orderInfo.receiveAccountIdList = paymentAccountID ? [paymentAccountID] : undefined
    if (saleSource || saleSource === SaleChannelEnum.self) {
      returnOrderRequest.subOrderInfo.orderInfo.saleChannels = [saleSource]
    } else {
      returnOrderRequest.subOrderInfo.orderInfo.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic
      ]
    }
    // returnOrderRequest.subOrderInfo.orderInfo.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(
    //   isFromSpecialSubject
    // )
    returnOrderRequest.subOrderInfo.orderInfo.saleChannelName =
      returnOrderRequest.subOrderInfo.orderInfo.saleChannels.includes(SaleChannelEnum.topic) ? specialSubjectName : ''
    returnOrderRequest.basicData = new ReturnOrderBasicDataRequest()
    returnOrderRequest.basicData.returnOrderStatus = [8, 9, 10]
    returnOrderRequest.basicData.refundAmountScope = new BigDecimalScopeRequest()
    returnOrderRequest.basicData.refundAmountScope.begin = 0.01
    returnOrderRequest.basicData.returnStatusChangeTime = new ReturnOrderStatusChangeTimeRequest()
    returnOrderRequest.basicData.returnStatusChangeTime.returnCompleted = new DateScopeRequest()
    returnOrderRequest.basicData.returnStatusChangeTime.returnCompleted.begin = startDate ? startDate : undefined
    returnOrderRequest.basicData.returnStatusChangeTime.returnCompleted.end = endDate ? endDate : undefined

    if (refundCheckAccountParam.distributorId) {
      returnOrderRequest.distributorId = refundCheckAccountParam.distributorId
    }
    if (refundCheckAccountParam.promotionPortalId && !refundCheckAccountParam.isDistributionExcludePortal) {
      returnOrderRequest.portalId = refundCheckAccountParam.promotionPortalId
    }
    returnOrderRequest.isDistributionExcludePortal = refundCheckAccountParam.isDistributionExcludePortal

    if (refundCheckAccountParam.refundType) {
      returnOrderRequest.basicData.returnOrderTypes = [refundCheckAccountParam.refundType]
    }

    returnOrderRequest.returnCommodity = new CommoditySkuRequest1()
    returnOrderRequest.returnCommodity.issueInfo = new IssueInfo1()
    returnOrderRequest.returnCommodity.issueInfo.issueId = periodId
    returnOrderRequest.returnCommoditySkuIdList = trainingProgramId ? [trainingProgramId] : []
    return returnOrderRequest
  }
}
