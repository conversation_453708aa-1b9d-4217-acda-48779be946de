import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum SubjectTypeEnum {
  //公需科目
  public_demand = 0,
  //专业科目
  speciality = 1,
  //专业科目+公需科目
  public_demand_and_speciality
}

class SubjectType extends AbstractEnum<SubjectTypeEnum> {
  static enum = SubjectTypeEnum

  constructor(status?: SubjectTypeEnum) {
    super()
    this.current = status
    this.map.set(SubjectTypeEnum.public_demand, '公需科目')
    this.map.set(SubjectTypeEnum.speciality, '专业科目')
    this.map.set(SubjectTypeEnum.public_demand_and_speciality, '专业科目+公需科目')
  }
}

export default SubjectType
