import QuestionType from '@api/service/common/enums/question/QuestionType'
import CreateQuestionVo from './CreateQuestionVo'
import ChooseAnswerOptionVo from '../ChooseAnswerOptionVo'
import { CreateRadioQuestionRequest } from '@api/ms-gateway/ms-examquestion-v1'
/**
 **单选题
 */
class CreateRadioQuestionVo extends CreateQuestionVo {
  questionType = QuestionType.enum.radio
  /**
   * 可选答案列表【必填】
   */
  answerOptions: Array<ChooseAnswerOptionVo> = new Array<ChooseAnswerOptionVo>()
  /**
   * 正确答案ID【必填】
   */
  correctAnswerId = ''

  // 模型转换为Dto
  toDto() {
    const createQuestionDto = new CreateRadioQuestionRequest()
    createQuestionDto.topic = this.topic
    createQuestionDto.questionType = this.questionType
    createQuestionDto.libraryId = this.libraryId
    createQuestionDto.enabled = this.enabled
    createQuestionDto.dissects = this.dissects
    createQuestionDto.relateCourseIds = [this.relateCourseId]
    createQuestionDto.correctAnswerId = this.correctAnswerId
    createQuestionDto.questionDifficulty = this.questionDifficulty
    createQuestionDto.answerOptions = this.answerOptions?.map(item => {
      return {
        id: item.id,
        content: item.content
      }
    })
    return createQuestionDto
  }
}

export default CreateRadioQuestionVo
