import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-autolearning-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-autolearning_log-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 自动学习编排日志
<AUTHOR>
@date 2025/3/18 10:28
 */
export class AutoLearningArrangeLog {
  /**
   * 日志ID
   */
  logId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 学员自动学习任务结果ID
   */
  studentAutoLearningTaskResultId: string
  /**
   * 主任务ID
   */
  mainTaskId: string
  /**
   * 学习方案ID
   */
  learningSchemeId: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 信息
   */
  message: string
  /**
   * 类型
@see com.fjhb.ms.autolearning.v1.kernel.consts.AutoLearningLogTypes
   */
  type: number
  /**
   * 编排次数(当type&#x3D;1的时候才有值)
   */
  arrangeNum: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 完成时间
   */
  completeTime: string
}

/**
 * 自动学习执行日志
<AUTHOR>
@date 2025/3/18 10:31
 */
export class AutoLearningExecuteLog {
  /**
   * 日志ID
   */
  logId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 学员自动学习任务结果ID
   */
  studentAutoLearningTaskResultId: string
  /**
   * 主任务ID
   */
  mainTaskId: string
  /**
   * 学习方案ID
   */
  learningSchemeId: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 状态
@see com.fjhb.ms.autolearning.v1.kernel.consts.AutoLearningLogExecuteStatus
   */
  status: number
  /**
   * 信息
   */
  message: string
  /**
   * 类型
@see com.fjhb.ms.autolearning.v1.kernel.consts.AutoLearningLogTypes
   */
  type: number
  /**
   * 创建时间
   */
  createTime: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询自动学习编排日志
   * @param query 查询 graphql 语法文档
   * @param studentAutoLearningTaskResultId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryAutoLearningArrangeLog(
    studentAutoLearningTaskResultId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryAutoLearningArrangeLog,
    operation?: string
  ): Promise<Response<Array<AutoLearningArrangeLog>>> {
    return commonRequestApi<Array<AutoLearningArrangeLog>>(
      SERVER_URL,
      {
        query: query,
        variables: { studentAutoLearningTaskResultId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询自动学习执行日志
   * @param query 查询 graphql 语法文档
   * @param studentAutoLearningTaskResultId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryAutoLearningExecuteLog(
    studentAutoLearningTaskResultId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryAutoLearningExecuteLog,
    operation?: string
  ): Promise<Response<Array<AutoLearningExecuteLog>>> {
    return commonRequestApi<Array<AutoLearningExecuteLog>>(
      SERVER_URL,
      {
        query: query,
        variables: { studentAutoLearningTaskResultId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查看详情-课程学习
   * @param query 查询 graphql 语法文档
   * @param logId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryCourseLearningDetail(
    logId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryCourseLearningDetail,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: query,
        variables: { logId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
