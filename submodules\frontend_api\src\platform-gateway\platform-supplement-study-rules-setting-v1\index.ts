import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-supplement-study-rules-setting-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-supplement-study-rules-setting-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * @Description 课程学习规则设置
<AUTHOR>
@Date 2024/5/11 15:17
 */
export class CourseStudyRuleSettingRequest {
  /**
   * 每日学习时间区间
   */
  allowedDailyStudyTimes?: Array<StringTimeRequest>
  /**
   * 每日不学习时间区间
   */
  disallowedDailyStudyTimes?: Array<StringTimeRequest>
  /**
   * 开通班级后开始学习的天数-开始
   */
  startDay?: number
  /**
   * 开通班级后开始学习的天数-结束
   */
  endDay?: number
  /**
   * 学习限制类型 (1-课程学习时长 2-课程物理时长)
   */
  studyLimitTypes?: number
  /**
   * 每日最多学习时间
   */
  dailyStudyTime?: number
  /**
   * 每日每次学习时间
   */
  dailyOneTimeStudyHours?: number
}

/**
 * @Description TODO
<AUTHOR>
@Date 2024/5/13 11:35
 */
export class IntegerRequest {
  /**
   * 开始时间
   */
  startTime?: number
  /**
   * 结束时间
   */
  endTime?: number
}

/**
 * @Description 添加学习规则请求
<AUTHOR>
@Date 2024/5/11 16:25
 */
export class SaveSupplementStudyRuleSettingRequest {
  /**
   * 适用行业id
   */
  suitIndustryId?: string
  /**
   * 适用范围
@see com.fjhb.platform.jxjy.v1.kernel.appservice.supplementStudyRuleSetting.enums.SuitTypes
1-平台 2-地区级别 3-培训方案级别
   */
  suitType?: number
  /**
   * 适用属性列表
   */
  sultProperties?: Array<SuitPropertyRequest>
  /**
   * 培训时间区间
   */
  suitTrainingTimes?: Array<SuitTrainingTimeRequest>
  /**
   * 是否需要错开开始学习日期
   */
  staggerStartTrainingTime?: boolean
  /**
   * 课程学习规则配置
   */
  courseStudyRuleSetting?: CourseStudyRuleSettingRequest
  /**
   * 特殊规则配置
   */
  specialtyRuleSetting?: SpecialRuleSettingRequest
}

/**
 * @Description 特殊规则配置
<AUTHOR>
@Date 2024/5/11 15:38
 */
export class SpecialRuleSettingRequest {
  /**
   * 提前开通时间天数区间
   */
  advanceOpenTimeDay?: IntegerRequest
}

/**
 * @Description TODO
<AUTHOR>
@Date 2024/5/15 9:44
 */
export class StringTimeRequest {
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 结束时间
   */
  endTime?: string
}

/**
 * @Description TODO
<AUTHOR>
@Date 2024/5/14 8:46
 */
export class SuitPropertyRequest {
  /**
   * 适用范围类型
@see com.fjhb.platform.jxjy.v1.kernel.appservice.supplementStudyRuleSetting.enums.SuitTypes
1-平台 2-地区级别 3-培训方案级别
   */
  type?: number
  /**
   * 适用范围值
   */
  value?: string
}

/**
 * @Description TODO
<AUTHOR>
@Date 2024/5/13 11:30
 */
export class SuitTrainingTimeRequest {
  /**
   * 年度
   */
  year?: number
  /**
   * 时间请求
   */
  timeRequest?: StringTimeRequest
}

/**
 * @Description 添加学习规则请求
<AUTHOR>
@Date 2024/5/11 16:25
 */
export class UpdateSupplementStudyRuleSettingRequest {
  /**
   * 规则id
   */
  ruleId?: string
  /**
   * 适用行业id
   */
  suitIndustryId?: string
  /**
   * 适用范围
@see com.fjhb.platform.jxjy.v1.kernel.appservice.supplementStudyRuleSetting.enums.SuitTypes
1-平台 2-地区级别 3-培训方案级别
   */
  suitType?: number
  /**
   * 适用属性列表
   */
  sultProperties?: Array<SuitPropertyRequest>
  /**
   * 培训时间区间
   */
  suitTrainingTimes?: Array<SuitTrainingTimeRequest>
  /**
   * 是否需要错开开始学习日期
   */
  staggerStartTrainingTime?: boolean
  /**
   * 课程学习规则配置
   */
  courseStudyRuleSetting?: CourseStudyRuleSettingRequest
  /**
   * 特殊规则配置
   */
  specialtyRuleSetting?: SpecialRuleSettingRequest
}

/**
 * <AUTHOR> [2023/7/11 20:54]
 */
export class GenernalResponse {
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * @Description 课程学习规则设置
<AUTHOR>
@Date 2024/5/11 15:17
 */
export class CourseStudyRuleSettingResponse {
  /**
   * 每日学习时间区间
   */
  allowedDailyStudyTimes: Array<RangeLocalDateBaseResponse>
  /**
   * 每日不学习时间区间
   */
  disallowedDailyStudyTimes: Array<RangeLocalDateBaseResponse>
  /**
   * 开通班级后开始学习的天数区间
   */
  dailyOneTimeStudyHoursRange: RangeIntegerBaseResponse
  /**
   * 学习限制类型 (1-课程学习时长 2-课程物理时长)
   */
  studyLimitTypes: number
  /**
   * 休息时长区间（小时）
   */
  restTimeRange: RangeIntegerBaseResponse
  /**
   * 每日最多学习时间
   */
  dailyStudyTime: number
  /**
   * 每日每次学习时间
   */
  dailyOneTimeStudyHours: number
}

/**
 * @Description 学习规则业务响应
<AUTHOR>
@Date 2024/5/20 10:20
 */
export class OnlineSchoolSupplementStudyServiceResponse {
  /**
   * 补学服务id
   */
  id: string
  /**
   * 平台id
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商id
   */
  serviceId: string
  /**
   * 服务状态
   */
  status: boolean
  /**
   * 创建人id
   */
  createdUserId: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
}

/**
 * @Description 添加学习规则请求
<AUTHOR>
@Date 2024/5/11 16:25
 */
export class PageSupplementStudyRuleSettingResponse {
  /**
   * 规则id
   */
  ruleId: string
  /**
   * 适用行业id
   */
  suitIndustryId: string
  /**
   * 适用范围
@see com.fjhb.platform.jxjy.v1.kernel.appservice.supplementStudyRuleSetting.enums.SuitTypes
1-平台 2-地区级别 3-培训方案级别
   */
  suitType: number
  /**
   * 适用属性列表
   */
  suitProperties: Array<SuitPropertyResponse>
  /**
   * 指定年度
   */
  years: Array<number>
  /**
   * 更新时间
   */
  updateTime: string
  /**
   * 创建人
   */
  createUserId: string
  /**
   * 是否启用
   */
  enabled: boolean
}

/**
 * @Description TODO
<AUTHOR>
@Date 2024/5/14 17:09
 */
export class RangeIntegerBaseResponse {
  /**
   * 开始时间
   */
  startTime: number
  /**
   * 结束时间
   */
  endTime: number
}

/**
 * @Description TODO
<AUTHOR>
@Date 2024/5/14 17:09
 */
export class RangeLocalDateBaseResponse {
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 结束时间
   */
  endTime: string
}

/**
 * @Description 保存学习规则响应
<AUTHOR>
@Date 2024/5/11 16:29
 */
export class SaveSupplementStudyRuleSettingResponse {
  /**
   * 学习规则id
   */
  ruleId: string
  /**
   * 重复的code
   */
  codeList: Array<string>
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * @Description 特殊规则配置
<AUTHOR>
@Date 2024/5/11 15:38
 */
export class SpecialRuleSettingResponse {
  /**
   * 提前开通时间天数区间
   */
  advanceOpenTimeDay: RangeIntegerBaseResponse
}

/**
 * @Description TODO
<AUTHOR>
@Date 2024/5/14 8:46
 */
export class SuitPropertyResponse {
  /**
   * 适用范围类型
@see com.fjhb.platform.jxjy.v1.kernel.appservice.supplementStudyRuleSetting.enums.SuitTypes
1-平台 2-地区级别 3-培训方案级别
   */
  type: number
  /**
   * 适用范围值
   */
  value: string
}

/**
 * @Description TODO
<AUTHOR>
@Date 2024/5/13 11:30
 */
export class SuitTrainingTimeResponse {
  /**
   * 年度
   */
  year: number
  /**
   * 时间范围
   */
  trainingTime: RangeLocalDateBaseResponse
}

/**
 * @Description 添加学习规则请求
<AUTHOR>
@Date 2024/5/11 16:25
 */
export class SupplementStudyRuleSettingResponse {
  /**
   * 规则id
   */
  ruleId: string
  /**
   * 适用行业id
   */
  suitIndustryId: string
  /**
   * 适用范围
@see com.fjhb.platform.jxjy.v1.kernel.appservice.supplementStudyRuleSetting.enums.SuitTypes
1-平台 2-地区级别 3-培训方案级别
   */
  suitType: number
  /**
   * 适用属性列表
   */
  suitProperties: Array<SuitPropertyResponse>
  /**
   * 培训时间区间
   */
  suitTrainingTimes: Array<SuitTrainingTimeResponse>
  /**
   * 是否需要错开开始学习日期
   */
  staggerStartTrainingTime: boolean
  /**
   * 课程学习规则配置
   */
  courseStudyRuleSetting: CourseStudyRuleSettingResponse
  /**
   * 特殊规则配置
   */
  specialtyRuleSetting: SpecialRuleSettingResponse
}

/**
 * @Description 编辑学习规则响应
<AUTHOR>
@Date 2024/5/11 16:29
 */
export class UpdateSupplementStudyRuleSettingResponse {
  /**
   * 重复的code
   */
  codeList: Array<string>
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

export class PageSupplementStudyRuleSettingResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PageSupplementStudyRuleSettingResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习规则已配置平台级别的行业
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getHasPlatformSupplementStudyRuleSettingIndustrysInServicer(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getHasPlatformSupplementStudyRuleSettingIndustrysInServicer,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 通过网校id查找网校学习规则
   * @param query 查询 graphql 语法文档
   * @param serviceId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSupplementStudyRuleSettingByServiceId(
    serviceId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSupplementStudyRuleSettingByServiceId,
    operation?: string
  ): Promise<Response<OnlineSchoolSupplementStudyServiceResponse>> {
    return commonRequestApi<OnlineSchoolSupplementStudyServiceResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { serviceId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取学习规则配置详情
   * @return
   * @param query 查询 graphql 语法文档
   * @param ruleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSupplementStudyRuleSettingInServicer(
    ruleId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSupplementStudyRuleSettingInServicer,
    operation?: string
  ): Promise<Response<SupplementStudyRuleSettingResponse>> {
    return commonRequestApi<SupplementStudyRuleSettingResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { ruleId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取学习规则配置已使用的地区最末级code
   * @return
   * @param query 查询 graphql 语法文档
   * @param industryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUseSupplementStudyRuleSettingRegionInServicer(
    industryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getUseSupplementStudyRuleSettingRegionInServicer,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: query,
        variables: { industryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取学习规则配置
   * @return
   * @param query 查询 graphql 语法文档
   * @param page 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSupplementStudyRuleSettingInServicer(
    page: Page,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSupplementStudyRuleSettingInServicer,
    operation?: string
  ): Promise<Response<PageSupplementStudyRuleSettingResponsePage>> {
    return commonRequestApi<PageSupplementStudyRuleSettingResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: { page },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 停用学习规则
   * @param ruleId 规则id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param ruleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disAbleSupplementStudyRuleSetting(
    ruleId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disAbleSupplementStudyRuleSetting,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { ruleId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用学习规则
   * @param ruleId 规则id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param ruleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableSupplementStudyRuleSetting(
    ruleId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableSupplementStudyRuleSetting,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { ruleId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 添加学习规则
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveSupplementStudyRuleSetting(
    request: SaveSupplementStudyRuleSettingRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveSupplementStudyRuleSetting,
    operation?: string
  ): Promise<Response<SaveSupplementStudyRuleSettingResponse>> {
    return commonRequestApi<SaveSupplementStudyRuleSettingResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 编辑学习规则
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateSupplementStudyRuleSetting(
    request: UpdateSupplementStudyRuleSettingRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateSupplementStudyRuleSetting,
    operation?: string
  ): Promise<Response<UpdateSupplementStudyRuleSettingResponse>> {
    return commonRequestApi<UpdateSupplementStudyRuleSettingResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
