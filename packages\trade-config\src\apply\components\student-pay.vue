<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div slot="header">
        <span class="tit-txt">销售渠道配置</span>
      </div>
      <!--销售渠道 1-->
      <div class="f-plr20 f-pb20">
        <div class="m-sub-tit">
          <span class="tit-txt f-flex-sub">销售渠道：Web 端</span>
          <el-button type="primary" size="small" @click="isShowDialog(1)">选择收款账号</el-button>
        </div>
        <el-table stripe :data="tableDataWeb" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="支付方式" min-width="100" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.accountType === 2">
                <el-tag type="warning" size="small">线下</el-tag>
              </div>
              <div v-if="scope.row.accountType === 1">
                <el-tag type="primary" size="small">线上</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="支付账号类型" min-width="120">
            <template slot-scope="scope">{{ paymentChannelTypeTitle(scope.row.paymentChannelId) }}</template>
          </el-table-column>
          <el-table-column label="收款账号别名" min-width="120">
            <template slot-scope="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column label="开户账户信息" min-width="250">
            <template slot-scope="scope">商户号：{{ scope.row.merchantName }}</template>
          </el-table-column>
          <el-table-column label="分行号" min-width="180">
            <template slot-scope="scope">{{ scope.row.depositBank ? scope.row.depositBank : '-' }}</template>
          </el-table-column>
          <el-table-column label="柜台号" min-width="180">
            <template slot-scope="scope">{{ scope.row.counterNo ? scope.row.counterNo : '-' }}</template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="removeItem(1, scope.row)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!--销售渠道 2-->
      <div class="f-plr20 f-pb20">
        <div class="m-sub-tit">
          <span class="tit-txt f-flex-sub">销售渠道：H5</span>
          <el-button type="primary" size="small" @click="isShowDialog(2)">选择收款账号</el-button>
        </div>
        <el-table stripe :data="tableDataH5" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="支付方式" min-width="100" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.accountType === 2">
                <el-tag type="warning" size="small">线下</el-tag>
              </div>
              <div v-if="scope.row.accountType === 1">
                <el-tag type="primary" size="small">线上</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="支付账号类型" min-width="120">
            <template slot-scope="scope">{{ paymentChannelTypeTitle(scope.row.paymentChannelId) }}</template>
          </el-table-column>
          <el-table-column label="收款账号别名" min-width="120">
            <template slot-scope="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column label="开户账户信息" min-width="250">
            <template slot-scope="scope">商户号：{{ scope.row.merchantName }}</template>
          </el-table-column>
          <el-table-column label="分行号" min-width="180">
            <template slot-scope="scope">{{ scope.row.depositBank ? scope.row.depositBank : '-' }}</template>
          </el-table-column>
          <el-table-column label="柜台号" min-width="180">
            <template slot-scope="scope">{{ scope.row.counterNo ? scope.row.counterNo : '-' }}</template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="removeItem(2, scope.row)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div slot="header" class="f-flex">
        <span class="tit-txt f-flex-sub">渠道发票配置</span>
        <el-button type="primary" size="small" icon="el-icon-edit" @click="isEdit = true">编辑</el-button>
      </div>
      <div class="f-p30">
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="formRef" :rules="rulesForm" :model="form" label-width="auto" class="m-form">
              <el-form-item label="是否提供发票：" required prop="allowProvide">
                <el-radio-group v-model="form.allowProvide" @change="allowProvideChange">
                  <el-radio :disabled="!isEdit" :label="true">是</el-radio>
                  <el-radio :disabled="!isEdit" :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="是否开放索要发票：" v-if="isShowAskForInvoice" required prop="allowOpen">
                <el-radio-group v-model="form.allowOpen">
                  <el-radio :disabled="!isEdit" :label="1">开放学员自主选择是否索要发票</el-radio>
                  <el-radio :disabled="!isEdit" :label="2">强制向学员提供发票</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="是否开放补开发票：" v-if="isShowAskForInvoice" required prop="allowAskFor">
                <el-radio-group v-model="form.allowAskFor">
                  <el-radio :disabled="!isEdit" :label="true">开放</el-radio>
                  <el-radio :disabled="!isEdit" :label="false">不开放</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                v-if="form.allowAskFor == 1 && isShowAskForInvoice"
                label="补开发票截止日期类型："
                :required="form.allowAskFor == 1"
                prop="askForInvoiceYearType"
              >
                <el-radio-group v-model="form.askForInvoiceYearType">
                  <el-radio :disabled="!isEdit" :label="1">当年度</el-radio>
                  <el-radio :disabled="!isEdit" :label="2">下一个年度</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                v-if="form.allowAskFor == 1 && isShowAskForInvoice"
                label="补开发票截止日期："
                :required="form.allowAskFor == 1"
                prop="askForInvoiceDeadline"
              >
                <!-- 隐藏年度，产品要求不要年，因为当前年不是系统当前年而是学员补要发票的当年，不固定是哪一年 -->
                <el-date-picker
                  v-model="form.askForInvoiceDeadline"
                  type="date"
                  placeholder="请选择补开发票截止日期"
                  class="form-m"
                  :disabled="!isEdit"
                  value-format="yyyy-MM-dd"
                  format="MM 月 dd 日"
                  @focus="focusclick"
                  v-if="isShowAskForInvoice"
                />
              </el-form-item>

              <el-form-item label="发票类型：" v-if="isShowAskForInvoice">
                <el-checkbox-group v-model="form.invoiceTypeGroupCheck">
                  <el-checkbox :disabled="!isEdit" :label="2" name="type" class="f-show">
                    增值税电子普通发票
                  </el-checkbox>
                  <!--选择 增值税电子普通发票 后出现-->
                  <div class="f-mb5" v-if="showOrdinaryVat">
                    <el-form ref="form" label-width="120px" class="m-form bg-gray f-pr50 f-pt5">
                      <el-form-item label="开票方式：" required>
                        <el-radio-group v-model="form.vatOrdinaryInvoice.invoiceMethod">
                          <el-radio :disabled="!isEdit" :label="1" @change="changeInvoiceMethod">自动开票</el-radio>
                          <el-radio :disabled="!isEdit" :label="2" @change="changeInvoiceMethod">线下开票</el-radio>
                        </el-radio-group>
                      </el-form-item>
                      <el-form-item label="发票抬头：" required>
                        <el-checkbox-group v-model="form.vatOrdinaryInvoice.invoiceTitleTypes">
                          <el-checkbox :disabled="!isEdit" :label="1" name="type">个人</el-checkbox>
                          <el-checkbox :disabled="!isEdit" :label="2" name="type">单位</el-checkbox>
                        </el-checkbox-group>
                      </el-form-item>
                      <el-form-item label="发票备注：" required>
                        <el-radio-group v-model="form.vatOrdinaryInvoice.remarkMode">
                          <el-radio :disabled="!isEdit" :label="1">学员填写</el-radio>
                          <el-radio :disabled="!isEdit" :label="2">统一设置</el-radio>
                        </el-radio-group>
                        <el-input
                          v-if="form.vatOrdinaryInvoice.remarkMode == 2"
                          v-model="form.vatOrdinaryInvoice.remark"
                          maxlength="100"
                          type="textarea"
                          :rows="3"
                          placeholder="请填写统一设置"
                          class="f-mt5 f-mb15"
                          :disabled="!isEdit"
                        ></el-input>
                      </el-form-item>
                    </el-form>
                  </div>
                  <el-checkbox :disabled="!isEdit" :label="3" name="type" class="f-show"
                    >增值税专用发票<span class="f-co">（默认开票方式为线下开票，抬头为单位）</span></el-checkbox
                  >
                  <!--选择增值税专用发票后出现-->
                  <div class="f-mb5" v-if="showSpecialVat">
                    <el-form ref="form" label-width="120px" class="m-form bg-gray f-pr50 f-pt5">
                      <el-form-item label="开票方式：" required>
                        <el-radio-group v-model="form.vatSpecialInvoice.invoiceMethodWithSpecial">
                          <el-radio :disabled="!isEdit" :label="1">线下开电子票</el-radio>
                          <el-radio :disabled="!isEdit" :label="2">纸质发票</el-radio>
                        </el-radio-group>
                      </el-form-item>
                      <el-form-item label="发票抬头：" required>
                        <el-checkbox-group v-model="form.vatSpecialInvoice.invoiceTitleTypes">
                          <el-checkbox :disabled="!isEdit" :label="2" name="type">单位</el-checkbox>
                        </el-checkbox-group>
                      </el-form-item>
                      <el-form-item label="发票备注：" required>
                        <el-radio-group v-model="form.vatSpecialInvoice.remarkMode">
                          <el-radio :disabled="!isEdit" :label="1">学员填写</el-radio>
                          <el-radio :disabled="!isEdit" :label="2">统一设置</el-radio>
                        </el-radio-group>
                        <el-input
                          v-if="form.vatSpecialInvoice.remarkMode == 2"
                          v-model="form.vatSpecialInvoice.remark"
                          maxlength="100"
                          type="textarea"
                          :rows="3"
                          placeholder="请填写统一设置"
                          class="f-mt5 f-mb15"
                          :disabled="!isEdit"
                        ></el-input>
                      </el-form-item>
                    </el-form>
                  </div>
                </el-checkbox-group>
              </el-form-item>

              <el-form-item class="m-btn-bar">
                <el-button :disabled="!isEdit" @click="giveUpTheEditor">放弃编辑</el-button>
                <el-button type="primary" :disabled="!isEdit" @click="save">保存</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
      <el-drawer
        title="选择收款账号"
        :visible.sync="isShow"
        :wrapperClosable="false"
        :close-on-press-escape="false"
        size="1200px"
        custom-class="m-drawer"
        :before-close="isDialog"
      >
        <div class="drawer-bd">
          <!--表格-->
          <el-table stripe :data="tableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="支付方式" min-width="100" align="center">
              <template slot-scope="scope">
                <div v-if="scope.row.accountType === 2">
                  <el-tag type="warning" size="small">线下</el-tag>
                </div>
                <div v-if="scope.row.accountType === 1">
                  <el-tag type="primary" size="small">线上</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="支付账号类型" min-width="120">
              <template slot-scope="scope">{{ paymentChannelTypeTitle(scope.row.paymentChannelId) }}</template>
            </el-table-column>
            <el-table-column label="收款账号别名" min-width="150">
              <template slot-scope="scope">{{ scope.row.name }}</template>
            </el-table-column>
            <el-table-column label="开户账户信息" min-width="250">
              <template slot-scope="scope">商户号：{{ scope.row.merchantName }}</template>
            </el-table-column>
            <el-table-column label="分行号" min-width="180">
              <template slot-scope="scope">{{ scope.row.depositBank ? scope.row.depositBank : '-' }}</template>
            </el-table-column>
            <el-table-column label="柜台号" min-width="180">
              <template slot-scope="scope">{{ scope.row.counterNo ? scope.row.counterNo : '-' }}</template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <el-checkbox
                  v-model="scope.row.isSelected"
                  :disabled="scope.row.disableSelect"
                  @change="chooseItem(scope.row)"
                  >选择</el-checkbox
                >
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="page" v-bind="page"> </hb-pagination>
          <div class="m-btn-bar f-tc f-mt20">
            <el-button @click="isDialog">取消</el-button>
            <el-button type="primary" @click="confirm">确定</el-button>
          </div>
        </div>
      </el-drawer>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import MutationUpdatePurchaseChannel from '@api/service/management/trade-info-config/mutation/MutationUpdatePurchaseChannel'
  import TradeInfoConfigModule from '@api/service/management/trade-info-config/TradeInfoConfigModule'
  import ReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/ReceiveAccountVo'
  import InvoiceConfigVo from '@api/service/management/trade-info-config/mutation/vo/InvoiceConfigVo'
  import UpdatePurchaseChannelVo from '@api/service/management/trade-info-config/mutation/vo/UpdatePurchaseChannelVo'
  import PurchaseChannelTypeVo from '@api/service/management/trade-info-config/mutation/vo/PurchaseChannelTypeVo'
  import TerminalVo from '@api/service/management/trade-info-config/mutation/vo/TerminalVo'
  import { UiPage } from '@hbfe/common'
  import { TerminalTypeEnum } from '@api/service/common/enums/trade-configuration/TerminalType'
  import { InvoiceCategoryEunm } from '@api/service/common/enums/trade-configuration/InvoiceCtegory'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'

  @Component({
    components: {}
  })
  export default class extends Vue {
    @Ref('formRef') formRef: any
    @Prop({ type: Object, default: {} }) studentPayDetail: PurchaseChannelTypeVo
    @Watch('studentPayDetail', { deep: true })
    async studentPayDetailWatch(newVal: PurchaseChannelTypeVo) {
      if (newVal) {
        this.studentPayDetail = newVal
        this.id = this.studentPayDetail.id
        // 根据当前选中的报名方式进行赋值
        this.updatePurchaseChannelVo = TradeInfoConfigModule.mutationTradeInfoConfigFactory.getUpdatePurchaseChannel(
          this.id,
          1
        )
        await this.requestDetail()
      }
    }
    id = ''
    //是否提供发票
    isShowAskForInvoice = false

    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable

    get showOrdinaryVat() {
      return this.form.invoiceTypeGroupCheck.includes(InvoiceCategoryEunm.VAT_GENERAL_INVOICE)
    }

    get showSpecialVat() {
      return this.form.invoiceTypeGroupCheck.includes(InvoiceCategoryEunm.VAT_SPECIAL_INVOICE)
    }

    rulesForm = {
      allowProvide: [
        {
          required: true,
          message: '请选择是否提供发票',
          trigger: ['change', 'blur']
        }
      ],
      allowOpen: [
        {
          validator: this.allowOpenRule,
          trigger: ['change', 'blur']
        }
      ],
      allowAskFor: [
        {
          required: true,
          message: '请选择是否开放补开发票',
          trigger: ['change', 'blur']
        }
      ],
      askForInvoiceYearType: [
        {
          validator: this.askForInvoiceYearTypeRule,
          trigger: ['change', 'blur']
        }
      ],
      askForInvoiceDeadline: [
        {
          required: true,
          message: '请选择补开发票截止日期',
          trigger: ['change', 'blur']
        }
      ]
    }
    allowOpenRule(rule: any, value: any, callback: any) {
      if (value == 0) {
        callback(new Error('请选择是否开放索要发票'))
      } else {
        callback()
      }
    }
    askForInvoiceYearTypeRule(rule: any, value: any, callback: any) {
      if (value == 0) {
        callback(new Error('请选择补开发票截止日期类型'))
      } else {
        callback()
      }
    }
    // 是否编辑
    isEdit = false
    mutationUpdatePurchaseChannel: MutationUpdatePurchaseChannel

    form = new InvoiceConfigVo()
    updateParam = new UpdatePurchaseChannelVo()

    // web端渠道信息
    tableDataWeb = new Array<ReceiveAccountVo>()
    // H5端渠道信息
    tableDataH5 = new Array<ReceiveAccountVo>()

    terminalCode = ''
    terminalCodeWeb = ''
    terminalCodeH5 = ''

    preparePurchaseChannelVo = TradeInfoConfigModule.mutationTradeInfoConfigFactory.getPreparePurchaseChannel()
    updatePurchaseChannelVo = new MutationUpdatePurchaseChannel()

    // 是否显示弹窗
    isShowDialog(type: number) {
      if (type === 1) {
        this.terminalCode = this.terminalCodeWeb
      } else {
        this.terminalCode = this.terminalCodeH5
      }
      this.isDialog()
      if (this.isFxlogin && this.isHadFxAbility) {
        this.doQueryPagefx()
      } else {
        this.doQueryPage()
      }
    }

    // 移除
    removeItem(type: number, item: ReceiveAccountVo) {
      if (type === 1) {
        this.terminalCode = this.terminalCodeWeb
      } else {
        this.terminalCode = this.terminalCodeH5
      }
      // todo
      this.$confirm('是否移除该收款账号？', '提示', {
        closeOnClickModal: false,
        closeOnPressEscape: false,
        type: 'warning',
        confirmButtonText: '确定',
        center: true
      })
        .then(async () => {
          this.updatePurchaseChannelVo.removeReceiveAccount(this.terminalCode, item)
          const res = await this.updatePurchaseChannelVo.doReceiveAccountSave()
          if (res.code === 200) {
            this.$message.success('移除成功')
            await this.requestDetail()
          } else {
            this.$message.warning('请求失败')
          }
        })
        .catch(() => {
          //
        })
    }

    // 放弃编辑
    async giveUpTheEditor() {
      await this.requestDetail()
      this.isEdit = false
    }

    // 保存
    async save() {
      // 不提供发票
      if (!this.form.allowProvide) {
        //   this.formRef.clearValidate()
        //   this.form = new InvoiceConfigVo()
        this.form.allowProvide = false
        this.updatePurchaseChannelVo.updatePurchaseChannelParam.invoiceConfig = this.form
        const res = await this.updatePurchaseChannelVo.doInvoiceConfigSave()
        if (res.isSuccess()) {
          this.$message.success('保存成功')
          this.isEdit = false
        } else {
          this.$message.warning('保存失败')
        }
        return
      }
      // 提供发票
      this.formRef.validate(async (valid: boolean, key: any) => {
        let canSave = true
        this.form.invoiceTypeGroupCheck.map((item) => {
          if (item === InvoiceCategoryEunm.VAT_GENERAL_INVOICE) {
            if (!this.form.vatOrdinaryInvoice.invoiceMethod || this.form.vatOrdinaryInvoice.invoiceMethod == -1) {
              canSave = false
              return this.$message.warning('请选择增值税电子普通发票开票方式')
            }
            if (!this.form.vatOrdinaryInvoice.invoiceTitleTypes?.length) {
              canSave = false
              return this.$message.warning('请选择增值税电子普通发票发票抬头')
            }
            if (!this.form.vatOrdinaryInvoice.remarkMode) {
              canSave = false
              return this.$message.warning('请选择增值税电子普通发票发票备注')
            }
          }
          if (item === InvoiceCategoryEunm.VAT_SPECIAL_INVOICE) {
            if (
              !this.form.vatSpecialInvoice.invoiceMethodWithSpecial ||
              this.form.vatSpecialInvoice.invoiceMethodWithSpecial == -1
            ) {
              canSave = false
              return this.$message.warning('请选择增值税专用发票开票方式')
            }
            if (!this.form.vatSpecialInvoice.invoiceTitleTypes?.length) {
              canSave = false
              return this.$message.warning('请选择增值税专用发票发票抬头')
            }
            if (!this.form.vatSpecialInvoice.remarkMode) {
              canSave = false
              return this.$message.warning('请选择增值税专用发票发票备注')
            }
          }
        })

        if (valid && canSave) {
          this.updatePurchaseChannelVo.updatePurchaseChannelParam.invoiceConfig = this.form
          const res = await this.updatePurchaseChannelVo.doInvoiceConfigSave()
          if (res.isSuccess()) {
            this.$message.success('保存成功')
            this.isEdit = false
          } else {
            this.$message.warning('保存失败')
          }
        }
      })
    }

    // 请求当前销售渠道配置
    async requestDetail() {
      // 详情获取切换至业务微服务接口
      await this.preparePurchaseChannelVo.preparePurchaseChannel()
      const res = await this.updatePurchaseChannelVo.getupdatePurchaseChannelParam()
      this.channelListMate(res.terminalList)

      this.form = Object.assign({}, res.invoiceConfig) // 浅克隆
      if (this.form.allowProvide === false) {
        this.isShowAskForInvoice = false
      } else {
        this.isShowAskForInvoice = true
      }
    }

    // 区分哪个端
    channelListMate(list: Array<TerminalVo>) {
      if (list && list.length > 0) {
        for (let i = 0; i < list.length; i++) {
          if (list[i].terminalCode == 'Web') {
            this.terminalCodeWeb = 'Web'
            this.tableDataWeb = this.updatePurchaseChannelVo.receiveAccountListByTerminalCode(TerminalTypeEnum.WEB)
          }
          if (list[i].terminalCode == 'H5') {
            this.terminalCodeH5 = 'H5'
            this.tableDataH5 = this.updatePurchaseChannelVo.receiveAccountListByTerminalCode(TerminalTypeEnum.H5)
          }
        }
      }
    }

    // 是否提供发票
    allowProvideChange() {
      if (this.form.allowProvide === false) {
        this.isShowAskForInvoice = false
      } else {
        this.isShowAskForInvoice = true
      }
    }

    // 切换自动开票
    async changeInvoiceMethod(num: any) {
      if (num == 1) {
        const res = await this.updatePurchaseChannelVo.hasAutomInvoice()
        if (res === false) {
          this.form.vatOrdinaryInvoice.invoiceMethod = -1
          this.$confirm('当前未完成【增值税电子普通发票（自动开票）】配置无法使用。是否前往配置？', {
            showCancelButton: false,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            showClose: false,
            confirmButtonText: '立即前往',
            center: true
          })
            .then(() => {
              window.open('#/basic-data/platform/function?type=electronic-invoice-auto-v2', '_blank')
            })
            .catch(() => {
              //
            })
        }
      }
    }

    // 抽屉逻辑
    isShow = false
    async isDialog() {
      this.isShow = !this.isShow
      if (!this.isShow) {
        this.updatePurchaseChannelVo.clearReceiveAccount(this.terminalCode)
        // await this.requestDetail()
      }
    }
    page: UiPage

    constructor() {
      super()
      if (this.isFxlogin && this.isHadFxAbility) {
        this.page = new UiPage(this.doQueryPagefx, this.doQueryPagefx)
      } else {
        this.page = new UiPage(this.doQueryPage, this.doQueryPage)
      }
    }
    async doQueryPage() {
      const res = await this.updatePurchaseChannelVo.queryPageReceiveAccountForOnline(this.page, this.terminalCode)
      this.tableData = res
    }
    async doQueryPagefx() {
      const res = await this.updatePurchaseChannelVo.queryPageFxReceiveAccountForOnline(this.page, this.terminalCode)
      this.tableData = res
    }
    tableData = new Array<ReceiveAccountVo>()
    // 确定
    async confirm() {
      await this.updatePurchaseChannelVo.doReceiveAccountSave()
      this.isDialog()
      await this.requestDetail()
    }
    // 选择账号
    chooseItem(item: any) {
      this.updatePurchaseChannelVo.chooseReceiveAccount(this.terminalCode, item)
    }

    focusclick() {
      setTimeout(() => {
        document
          .getElementsByClassName('el-picker-panel__icon-btn el-date-picker__prev-btn el-icon-d-arrow-left')[0]
          .remove()
        document
          .getElementsByClassName('el-picker-panel__icon-btn el-date-picker__next-btn el-icon-d-arrow-right')[0]
          .remove()
        document.getElementsByClassName('el-date-picker__header-label')[0].remove()
      }, 100)
    }
    // 支付账号类型
    paymentChannelTypeTitle(paymentChannelId: string) {
      if (paymentChannelId.indexOf('WXPAY') != -1) {
        return '微信支付'
      }
      if (paymentChannelId.indexOf('ALIPAY') != -1) {
        return '支付宝支付'
      }
      if (paymentChannelId.indexOf('CIB_PAY') != -1) {
        return '兴业银行聚合支付'
      }
      if (paymentChannelId.indexOf('CCB_PAY') != -1) {
        return '建设银行聚合支付'
      }
      if (paymentChannelId.indexOf('SWIFT_PASS_PAY') != -1) {
        return '兴业银行聚合支付（威富通）'
      }
      if (paymentChannelId.indexOf('NEW_LAND_PAY') != -1) {
        return '新大陆聚合支付'
      }
      return '银行支付'
    }
  }
</script>

<style scoped>
  .border-margin {
    margin-left: 90px;
  }
  .m-l {
    margin-left: -80px;
    margin-bottom: 8px;
  }
</style>
