<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <div class="f-p15">
      <el-alert type="warning" :closable="false" class="m-alert f-clear">
        <div class="f-flex f-align-center">
          <div class="f-flex-sub">
            <!-- <el-button type="warning" size="small">学习数据异常管理（1）</el-button> -->
          </div>
          <div @click="changeStavisible" class="f-fr f-csp f-flex f-align-center">
            <i class="el-icon-info f-f16 f-mr5"></i>统计口径说明
          </div>
        </div>
      </el-alert>
      <div class="f-mt10">
        <el-button
          v-if="$hasPermission('allSync') && schoolConfigFlag"
          desc="批量同步"
          actions="allSync"
          @click="allSync"
          :disabled="!studentNoList.length"
          type="primary"
          >批量推送
        </el-button>
      </div>
      <div v-if="$hasPermission('mixedClassTable')" desc="混合班列表" actions="@MixedClassTable">
        <el-table
          stripe
          :data="tableData"
          border
          max-height="500px"
          v-loading="loading"
          class="m-table is-statistical f-mt10"
          ref="schemeTable"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            :selectable="selectable"
            width="50"
            align="center"
            fixed="left"
          ></el-table-column>
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="用户信息" min-width="240" align="center">
            <template slot-scope="scope">
              <p>姓名：{{ scope.row.studentDetail ? scope.row.studentDetail.userName : '-' }}</p>
              <p v-if="queryShowLoginAccount.isShowLoginAccount">
                登录账号：{{ scope.row.studentDetail ? scope.row.studentDetail.loginAccount : '-' }}
              </p>
              <p>证件号：{{ scope.row.studentDetail ? scope.row.studentDetail.idCard : '-' }}</p>
              <p>手机号：{{ scope.row.studentDetail ? scope.row.studentDetail.phone : '-' }}</p>
              <!-- <span v-if="isAxServicerId()">
            <p
              v-if="
                scope.row.studentDetail.lsStudentIndustryInfo.sectionAndSubjectsName.length &&
                  scope.row.studentDetail.lsStudentIndustryInfo.sectionAndSubjectsName[0].section
              "
            >
              学段：{{ scope.row.studentDetail.lsStudentIndustryInfo.sectionAndSubjectsName[0].section }}
            </p>
            <p
              v-if="
                scope.row.studentDetail.lsStudentIndustryInfo.sectionAndSubjectsName.length &&
                  scope.row.studentDetail.lsStudentIndustryInfo.sectionAndSubjectsName[0].subjects
              "
            >
              学科：{{ scope.row.studentDetail.lsStudentIndustryInfo.sectionAndSubjectsName[0].subjects }}
            </p>
          </span> -->
            </template>
          </el-table-column>
          <el-table-column label="工作单位信息" min-width="280" align="center">
            <template slot-scope="scope">
              <p>
                工作单位：{{
                  scope.row.studentDetail && scope.row.studentDetail.companyName
                    ? scope.row.studentDetail.companyName
                    : '-'
                }}
              </p>
              <p v-if="scope.row.studentDetail">
                单位所在地区：
                {{ transfromRegion(scope.row.studentDetail.companyRegion) }}
              </p>
              <p>下单地区：{{ scope.row.managementUnitRegionName }}</p>
            </template>
          </el-table-column>
          <el-table-column label="方案名称" min-width="200">
            <template slot-scope="scope">
              <p>
                {{ scope.row.trainClassBaseInfo.name || '-' }}
              </p>

              <p><el-tag type="info" size="mini">培训期别</el-tag>{{ scope.row.periodName }}</p>
            </template>
          </el-table-column>
          <el-table-column label="方案属性" min-width="200" align="center">
            <template slot-scope="scope">
              <p>行业：{{ scope.row.trainClassBaseInfo.skuProperty.industry.skuPropertyName || '-' }}</p>
              <p>地区：{{ scope.row.trainClassBaseInfo.skuProperty.region.skuPropertyName || '-' }}</p>
              <!-- <p>技术等级：{{ scope.row.trainClassBaseInfo.skuProperty.technicalGrade.skuPropertyName || '-' }}</p> -->
              <p v-if="scope.row.trainClassBaseInfo.skuProperty.year.skuPropertyName">
                培训年度：{{ scope.row.trainClassBaseInfo.skuProperty.year.skuPropertyName || '-' }}
              </p>
              <p v-if="scope.row.trainClassBaseInfo.skuProperty.subjectType.skuPropertyName">
                科目类型：{{ scope.row.trainClassBaseInfo.skuProperty.subjectType.skuPropertyName || '-' }}
              </p>
              <p v-if="scope.row.trainClassBaseInfo.skuProperty.trainingCategory.skuPropertyName">
                培训类别：{{ scope.row.trainClassBaseInfo.skuProperty.trainingCategory.skuPropertyName || '-' }}
              </p>
              <!-- {{ scope.row.trainClassBaseInfo.skuProperty }} -->
              <p v-if="scope.row.trainClassBaseInfo.skuProperty.trainingMajor.skuPropertyName">
                培训专业：{{ scope.row.trainClassBaseInfo.skuProperty.trainingMajor.skuPropertyName }}
              </p>
              <!-- 学科、学段 -->
              <p v-if="scope.row.trainClassBaseInfo.skuProperty.learningPhase.skuPropertyName">
                学段：{{ scope.row.trainClassBaseInfo.skuProperty.learningPhase.skuPropertyName }}
              </p>
              <p
                v-if="
                  scope.row.trainClassBaseInfo.skuProperty.learningPhase.skuPropertyName &&
                  scope.row.trainClassBaseInfo.skuProperty.discipline.skuPropertyName
                "
              >
                学科：{{ scope.row.trainClassBaseInfo.skuProperty.discipline.skuPropertyName }}
              </p>
              <p
                v-if="
                  scope.row.trainClassBaseInfo.skuProperty.certificatesType.skuPropertyName &&
                  scope.row.trainClassBaseInfo.skuProperty.practitionerCategory.skuPropertyName
                "
              >
                执业类别：{{ scope.row.trainClassBaseInfo.skuProperty.certificatesType.skuPropertyName }}/{{
                  scope.row.trainClassBaseInfo.skuProperty.practitionerCategory.skuPropertyName
                }}
              </p>
              <p v-if="scope.row.trainClassBaseInfo.skuProperty.positionCategory.skuPropertyName">
                岗位类别：{{ scope.row.trainClassBaseInfo.skuProperty.positionCategory.skuPropertyName }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="报名成功时间" min-width="160" align="center">
            <template slot-scope="scope">{{ scope.row.registerTime }}</template>
          </el-table-column>
          <el-table-column label="报名学时" min-width="80" align="center">
            <template slot-scope="scope">{{ scope.row.trainClassBaseInfo.period }}</template>
          </el-table-column>
          <el-table-column label="培训期别" header-align="center">
            <el-table-column label="结业测试" min-width="160" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.graduationAccess">
                  {{ scope.row.periodStudy.graduationStatus == GraduationStatusEnum.qualified ? '已合格' : '未合格' }}
                </span>
                <span v-else> - </span>
              </template>
            </el-table-column>
            <el-table-column label="考勤签到（总次数/实际次数）" min-width="160" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.periodStudy.attendance.totalNum">
                  {{ scope.row.periodStudy.attendance.totalNum || 0 }} /
                  <span :style="styleColor(scope.row)">
                    {{ scope.row.periodStudy.attendance.completedNum || 0 }}
                  </span>
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="调研问卷（问卷要求/已提交）" min-width="160" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.periodStudy.questionnaire.totalNum">
                  {{ scope.row.periodStudy.questionnaire.totalNum || '-' }}
                </span>
                <span v-else>-</span>
                /
                <el-tooltip effect="dark" placement="top">
                  <span>
                    {{ scope.row.periodStudy.questionnaireCompletedNum || 0 }}
                  </span>
                  <div slot="content">
                    <p>纳入考核问卷已提交：{{ scope.row.periodStudy.questionnaire.completedNum || 0 }}</p>
                    <p>
                      不纳入考核问卷已提交：{{
                        Number(scope.row.periodStudy.questionnaireCompletedNum) -
                        Number(scope.row.periodStudy.questionnaire.completedNum)
                      }}
                    </p>
                  </div>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="培训结果" min-width="80" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.userGetLearning.trainingResult == 1">
                <el-badge is-dot type="success" class="badge-status">已合格</el-badge>
              </div>
              <div v-else>
                <el-badge is-dot type="danger" class="badge-status">未合格</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="培训通过时间" min-width="155" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.haveTrainSimulate && !scope.row.onlyHaveExamSimulate">
                <el-tooltip
                  popper-class="studnet-simulate-tooltip-style"
                  class="item"
                  effect="light"
                  :content="'同步第三方数据：' + scope.row.trainSimulatePassTime"
                  placement="top"
                >
                  <i class="f-dot red f-mr5"></i>
                </el-tooltip>
                {{ scope.row.userGetLearning.trainingResultTime || '-' }}
              </div>
              <div v-else>
                {{ scope.row.userGetLearning.trainingResult == 1 ? scope.row.userGetLearning.trainingResultTime : '-' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="140" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button
                v-if="$hasPermission('attendanceDetail')"
                desc="考勤详情"
                actions="@attendanceDetail"
                type="text"
                size="mini"
                @click="attendanceDetail(scope.row.periodQualificationId, scope.row.periodStudy.periodId)"
                >查看考勤详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page" :pageSizes="[5, 10, 15, 20, 25, 100]"></hb-pagination>
    </div>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Prop } from 'vue-property-decorator'
  import { StudentLearningStaticsVo } from '@api/service/management/statisticalReport/query/vo/StudentLearningStaticsVo'
  import { cloneDeep, isBoolean } from 'lodash'
  import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import { QueryStudentLearningManagerRegionList } from '@api/service/management/statisticalReport/query/QueryStudentLearningManagerRegionList'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'
  import { RegionModel } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import { GraduationStatusEnum } from '@api/service/common/scheme/enum/GraduationStatusEnum'
  import { UiPage } from '@hbfe/common'
  import { isAhServicerId } from '@hbfe/jxjy-admin-common/src/util/differentiateServicerId'
  import AhStudentTrainClassDetailVo from '@api/service/diff/management/anhui/train-class/StudentTrainClassDetailVo'
  import BaseConfig from '@hbfe-biz/biz-anticheat/dist/config/BaseConfig'
  @Component
  export default class extends Vue {
    @Ref('schemeTable') schemeTableRef: any
    @Prop({
      required: true,
      default: () => {
        return new UiPage()
      }
    })
    page: UiPage
    @Prop({
      required: true,
      default: () => {
        return new BaseConfig()
      }
    })
    baseConfig: BaseConfig

    @Prop({
      required: true,
      default: () => {
        return new Array<StudentLearningStaticsVo>()
      }
    })
    tableData: Array<StudentLearningStaticsVo>
    @Prop({
      required: true,
      default: () => {
        return false
      }
    })
    loading: boolean
    @Prop({
      required: true,
      default: () => {
        return true
      }
    })
    schoolConfigFlag: boolean
    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount
    studentNoList: Array<string> = []
    GraduationStatusEnum = GraduationStatusEnum
    QueryStudentLearningManagerRegionListObj: QueryStudentLearningManagerRegionList =
      new QueryStudentLearningManagerRegionList()
    mounted() {
      this.$emit('getChildRef', this.$refs['schemeTable'] as any, 'mixed')
    }
    handleSelectionChange(val: Array<StudentLearningStaticsVo>) {
      const noAllowPushSchemeIdsArr = (
        CommonConfigCenter.getFrontendApplication(frontendApplication.noAllowPushSchemeIds) || ''
      ).split(',')
      let flag = true
      val.map((item: StudentLearningStaticsVo) => {
        if (noAllowPushSchemeIdsArr.includes(item.trainClassBaseInfo.id)) {
          flag = false
          return
        }
      })
      if (!flag) {
        this.schemeTableRef.clearSelection()
        this.$message.error('存在不在推送范围内的方案！')
        return
      }
      this.studentNoList = val.map((item: StudentLearningStaticsVo) => {
        return item.studentNo
      })
      this.$emit('getStudentNoList', this.studentNoList)
    }

    selectable(row: any, index: number) {
      if (row.studentNo && row.syncStatus != 1 && row.userGetLearning.trainingResult == 1) {
        return true
      } else {
        return false
      }
    }

    // 前往学习日志
    toStudyLog(row: StudentLearningStaticsVo) {
      // scope.row.studentDetail.userId, scope.row.trainClassBaseInfo.id
      const { userId } = row.studentDetail
      const schemeId = row.trainClassBaseInfo.id
      const { qualificationId, schemeType } = row.trainClassBaseInfo
      const { studentNo } = row
      this.$router.push({
        path: '/statistic/statistics-report/study-log',
        query: { userId, schemeId, qualificationId, schemeType: String(schemeType), studentNo }
      })
    }
    learningLog(row: StudentLearningStaticsVo) {
      const { userId } = row.studentDetail
      const { qualificationId } = row.trainClassBaseInfo
      const studentNo = row.studentNo
      const parentPage = 'studyDetail'
      this.$router.push({
        path: '/statistic/statistics-report/learning-log',
        query: { userId, qualificationId, studentNo, parentPage }
      })
    }
    //重新推送
    async handleRePush(row: StudentLearningStaticsVo) {
      const noAllowPushSchemeIdsArr = (
        CommonConfigCenter.getFrontendApplication(frontendApplication.noAllowPushSchemeIds) || ''
      ).split(',')
      if (noAllowPushSchemeIdsArr.includes(row.trainClassBaseInfo.id)) {
        this.$message.error('存在不在推送范围内的方案！')
        return
      }
      // this.queryTrainClassTableData.studentNo = row.studentNo
      let res
      if (isAhServicerId()) {
        const obj = new AhStudentTrainClassDetailVo()
        res = await obj.rePush([row.studentNo])
      } else {
        res = await this.QueryStudentLearningManagerRegionListObj.batchRePushStudentTraining([row.studentNo])
      }
      if (res.status.code == 200 && res.data == '重推成功') {
        this.$message.success('重推成功！')
      } else {
        this.$message.error('重推失败！')
      }
      // await this.doQueryPage()
    }

    // 判断是否成果同步
    isNeedDataSync(needDataSync: boolean) {
      return !(isBoolean(needDataSync) && !needDataSync)
    }
    /**
     * 查看考试
     */
    examDetail(qualificationId: string, row: StudentLearningStaticsVo) {
      this.$emit('examDetail', qualificationId, row)
    }
    /**
     * 查看测验
     */
    testDetail(qualificationId: string) {
      this.$emit('testDetail', qualificationId)
    }
    /**
     * 查看考勤详情
     */
    attendanceDetail(qualificationId: string, period: string) {
      this.$emit('attendanceDetail', qualificationId, period)
    }
    get showNeedPeriod() {
      return (row: any) => {
        if (
          !row.userAssessResult?.electiveRequirePeriod?.current &&
          row.userAssessResult?.electiveRequirePeriod?.current != 0
        ) {
          return false
        }
        if (
          !row.userAssessResult?.compulsoryRequirePeriod?.current &&
          row.userAssessResult?.compulsoryRequirePeriod?.current != 0
        ) {
          return false
        }
        return true
      }
    }

    get showGetPeriod() {
      return (row: any) => {
        if (
          !row.userAssessResult?.electiveRequirePeriod?.current &&
          row.userAssessResult?.electiveRequirePeriod?.current != 0
        ) {
          return false
        }
        if (
          !row.userAssessResult?.compulsoryRequirePeriod?.current &&
          row.userAssessResult?.compulsoryRequirePeriod?.current != 0
        ) {
          return false
        }
        return true
      }
    }

    // 避免精度缺失
    get numToFixed() {
      return (num: number) => {
        if (!num) return 0
        const numStr = num.toString()
        if (numStr.indexOf('.') != -1 && numStr.length - numStr.indexOf('.') > 3) {
          num = Number(num.toFixed(2))
        }
        return num
      }
    }

    styleColor(row: any) {
      return row.periodStudy.attendance.totalNum > row.periodStudy.attendance.completedNum
        ? 'color:#ff0000'
        : 'color:#70B603'
    }
    /**
     * 转换单位地区
     */
    transfromRegion(region: RegionModel) {
      const temp = new Array<string>()
      if (region?.provinceName) {
        temp.push(region.provinceName)
      }
      if (region?.cityName) {
        temp.push(region.cityName)
      }
      if (region?.countyName) {
        temp.push(region.countyName)
      }
      return temp.join('-') || '-'
    }
    async allSync() {
      this.$confirm('提示', {
        message: '确定要批量同步吗？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          if (isAhServicerId()) {
            await new AhStudentTrainClassDetailVo().rePush(this.studentNoList)
          } else [await this.QueryStudentLearningManagerRegionListObj.batchRePushStudentTraining(this.studentNoList)]
          this.$emit('doQueryPage')
        })
        .catch((e) => {
          console.log(e)
        })
    }
    changeStavisible() {
      this.$emit('changeStavisible')
    }
  }
</script>
<style></style>
