<route-meta>
  { "isMenu": false, "title": "课件分类管理"}
</route-meta>
<template>
  <el-main :key="key">
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/courseware')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/courseware' }">课件管理</el-breadcrumb-item>
      <el-breadcrumb-item>课件分类管理</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <div class="f-mb15">
        <el-button type="primary" icon="el-icon-plus" @click="openCreate()">新建分类</el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-refresh"
          class="f-ml10"
          @click="loadData"
          :loading="query.loading"
        >
          刷新表格
        </el-button>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <!--表格-->
        <el-table
          :data="tableData"
          lazy
          :load="load"
          row-key="id"
          v-loading="query.loading"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          max-height="500px"
          class="m-table"
        >
          <el-table-column prop="name" min-width="400">
            <template slot="header">
              <span class="f-ml5">分类名称</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" align="center" fixed="right">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" @click="openDetail(row.id)">详情</el-button>
              <el-button type="text" size="mini" @click="openModify(row.id)">编辑</el-button>
              <hb-popconfirm placement="top" title="确定删除该课件分类吗？" @confirm="deleteCategory(row.id)">
                <el-button slot="reference" type="text" size="mini" @click="confirmDelete(row.id)">删除</el-button>
              </hb-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <operation-drawer
        :title="title"
        :load-detail="loadDetail"
        :refreshData.sync="refreshData"
        ref="operation-drawer"
      ></operation-drawer>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import BizCoursewareCategory from '@hbfe/jxjy-admin-components/src/biz/biz-courseware-category.vue'
  import { Query, ResponseStatus } from '@hbfe/common'
  import OperationDrawer from '@hbfe/jxjy-admin-courseware/src/components/operation.vue'
  import { Mode, CategoryComponent } from '@hbfe/jxjy-admin-components/src/operation.vue'
  import AbstractStandardFactory from '@api/service/management/resource/AbstractStandardFactory'
  import AbstractQueryCategory from '@api/service/management/resource/AbstractQueryCategory'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import CourseCategoryListDetail from '@api/service/management/resource/course-category/query/vo/CourseCategoryListDetail'
  import CoursewareCategoryDetail from '@api/service/management/resource/courseware-category/query/vo/CoursewareCategoryDetail'

  @Component({
    components: {
      BizCoursewareCategory,
      OperationDrawer
    }
  })
  export default class extends Vue {
    key = 0
    title = ''
    query: Query = new Query()
    //删除弹窗显隐
    currentId = ''
    deleteDialog = false
    tableData = new Array<CoursewareCategoryDetail>()

    @Ref('operation-drawer')
    operationDrawer: OperationDrawer
    categoryFactory = ResourceModule.coursewareCategoryFactory

    refreshData = false

    async loadDetail(id: string) {
      return this.categoryFactory.query.queryById(id)
    }

    async load(tree: any, treeNode: any, resolve: any) {
      const result = await this.categoryFactory.query.queryChildrenById(tree.id)
      resolve(result)
    }

    async loadData() {
      this.query.loading = true
      this.tableData = new Array<CoursewareCategoryDetail>()
      this.tableData = await this.categoryFactory.query.queryList()
      this.query.loading = false
    }

    async created() {
      await this.loadData()
    }

    @Watch('refreshData')
    async refreshList() {
      if (this.refreshData) {
        await this.loadData()
        this.refreshData = false
      }
    }

    openDetail(id: string) {
      this.title = '分类详情'
      this.currentId = id
      this.operationDrawer.open(Mode.detail, id)
    }

    openModify(id: string) {
      this.title = '编辑分类'
      this.currentId = id
      this.operationDrawer.open(Mode.modify, id)
    }

    openCreate() {
      this.title = '新建分类'
      this.currentId = ''
      this.operationDrawer.open(Mode.create)
    }

    confirmDelete(id: string) {
      this.currentId = id
      this.deleteDialog = true
    }

    async deleteCategory(id: string) {
      const result = await this.categoryFactory.mutationBiz(id).doRemove()
      if (result.isSuccess()) {
        this.$message.success('操作成功')
        setTimeout(async () => {
          await this.loadData()
          // 刷新组件
          this.key++
        }, 1000)
      } else {
        this.$message.error(result.message.toString())
      }
    }
  }
</script>
