import BaseReportModel from '@api/service/common/webfunny/models/BaseReportModel'

/**
 * 创建订单异常埋点对象
 */
export class CreateOrderModel extends BaseReportModel {
  orderNo?: string // orderNo | 类型：文本 | 长度：200 | 描述：订单号
  createTime?: number // createTime | 类型：文本 | 长度：100 | 描述：创建时间
  commodityId?: string // commodityId | 类型：文本 | 长度：100 | 描述：商品id
}

/**
 * 订单重定向异常埋点对象
 */
export class OrderRedirectError extends BaseReportModel {
  orderNo?: string // orderNo | 类型：文本 | 长度：200 | 描述：订单号
  commodityId?: string // commodityId | 类型：文本 | 长度：100 | 描述：商品id
  commodityName?: string // commodityName | 类型：文本 | 长度：100 | 描述：商品名称
  paymentChannelId?: string // paymentChannelId | 类型：文本 | 长度：100 | 描述：支付渠道 微信支付 || 支付宝支付 || 其他银行支付
  purchaseChannelTerminal?: string // purchaseChannelTerminal | 类型：文本 | 长度：100 | 描述：支付终端 WEB || H5
  purchaseChannelType?: number // purchaseChannelType | 类型：文本 | 长度：100 | 描述：报名方式 1:学员报名 2:集体报名管理员
}
