"""独立部署的微服务,K8S服务名:ms-learningscheme-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""更换期别
		60007 -期别不开放报名
		60004 -报名未开始
		60005 -报名已结束
		10005 -期别已开始培训
		10006 -期别已结束培训
		60001  本期培训已报满
		@param request:
		<AUTHOR> By Cb
		@since 2024/11/26 20:04
	"""
	changeIssue(request:ChangeIssueRequest):BaseResponse
	"""校验期别报名数据
		"80002", "期别已存在报名数据"
	"""
	validateIssueSignUpData(issueId:String!):BaseResponse
	"""校验期别报名人数
		"80001", "已报名人数>准备开设报名人数"
	"""
	validateIssueSignUpNum(request:ValidateIssueSignUpNumRequest):BaseResponse
}
"""期别参训资格更换期别请求
	<AUTHOR> By Cb
	@since 2024/11/12 14:39
"""
input ChangeIssueRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.ChangeIssueRequest") {
	"""学习方案ID"""
	learningSchemeId:String!
	"""原始期别ID"""
	originalIssueId:String
	"""目标期别ID"""
	issueId:String!
	"""用户ID【必填】"""
	userId:String!
	"""报名来源类型【必填】
		如：子订单：SUB_ORDER、换货单：EXCHANGE_ORDER
		@see StudentSourceTypes
	"""
	sourceType:String!
	"""报名来源ID【必填】"""
	sourceId:String!
}
"""验证报名人数请求"""
input ValidateIssueSignUpNumRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.ValidateIssueSignUpNumRequest") {
	"""学习方案ID"""
	schemeId:String!
	"""期别ID"""
	issueId:String!
	"""允许报名人数"""
	allowSignUpNum:Int!
}
"""通用响应基类，用于异常转化为code"""
type BaseResponse @type(value:"com.fjhb.ms.learningscheme.v1.kernel.util.exceptiontransform.BaseResponse") {
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}

scalar List
