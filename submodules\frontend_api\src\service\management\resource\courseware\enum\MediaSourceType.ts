import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * 转换状态
 */
export enum MediaSourceTypeEnum {
  // 华为云
  huawei = 1,
  // 外链地址
  outer = 2
}

class MediaSourceType extends AbstractEnum<MediaSourceTypeEnum> {
  static enum = MediaSourceTypeEnum

  constructor(status?: MediaSourceTypeEnum) {
    super()
    this.current = status
    this.map.set(MediaSourceTypeEnum.huawei, '华为云')
    this.map.set(MediaSourceTypeEnum.outer, '外链地址')
  }
}

export default MediaSourceType
