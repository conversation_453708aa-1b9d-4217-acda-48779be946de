import IPaymentProperties, {
  TrainingVouchersProperty
} from '@api/service/customer/trade/mutation/customer-order/interfaces/btpx/IPaymentProperties'
import { TerminalEnum } from '@api/service/common/enums/trade/Terminal'
export default class PayOrderParams implements IPaymentProperties {
  constructor(purchaseChannelTerminal: TerminalEnum, orderNo: string) {
    this.orderNo = orderNo
    this.purchaseChannelTerminal = purchaseChannelTerminal
  }
  /**
   * 订单号
   */
  orderNo = ''
  /**
      * 支付渠道编号
   培训券，对接众智汇云培训券:TRAINING_VOUCHER
   支付宝:ALIPAY
   微信：WXPAY
      */
  readonly paymentChannelId = 'TRAINING_VOUCHER'
  /**
      * 购买渠道类型
   1-用户自主购买
   2-集体缴费
   3-管理员导入
      */
  readonly purchaseChannelType = 1
  /**
      * 支付终端
   Web端：Web
   IOS端：IOS
   安卓端：Android
   微信小程序：WechatMini
   微信公众号：WechatOfficial
      */
  private purchaseChannelTerminal: TerminalEnum
  /**
   * 支付描述
   */
  description?: string

  /**
      * 支付的附加属性，由支付渠道决定
   当支付渠道为培训券（TRAINING4_VOUCHER）时，
   {
   &quot;couponCode&quot;:&quot;培训券编码&quot;,
   &quot;educationCode&quot;:&quot;机构编码（企业统一社会信用代码）&quot;,
   &quot;workType&quot;: &quot;工种名称&quot;,
   &quot;learningSchemeId&quot;: &quot;培训班id&quot;
   }
      */
  readonly paymentProperties?: Array<TrainingVouchersProperty> = []

  /**
   * @description: 请调用该方法设置支付扩展属性
   * @param {*}
   * @return {*}
   */

  addPaymentProperties(key: 'couponCode' | 'educationCode' | 'workType' | 'learningSchemeId', value: string) {
    this.paymentProperties.push({ key, value })
    return this
  }

  /**
   * @description: 转化网关层的入参
   * @param {*}
   * @return {*}
   */

  to() {
    return {
      orderNo: this.orderNo,
      paymentChannelId: this.paymentChannelId,
      purchaseChannelType: this.purchaseChannelType,
      purchaseChannelTerminal: this.purchaseChannelTerminal,
      description: this.description,
      paymentProperties: this.paymentProperties
    }
  }
}
