<route-meta>
  {
  "isMenu": true,
  "title": "地区开通统计",
  "sort": 2,
  "icon": "icon-cptj"
  }
  </route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="doSearch,@BizDistributorSelect,@BizPortalSelect">
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <hb-search-wrapper @reset="resetQueryParam" class="m-query is-border-bottom">
          <el-form-item label="单位所在地">
            <biz-region-cascader
              v-model="locationUnit"
              :check-strictly="true"
              placeholder="请选择工作单位所在地区"
            ></biz-region-cascader>
          </el-form-item>
          <el-form-item label="年度">
            <biz-year-select
              placeholder="请选择培训年度"
              v-model="localSkuProperty.year"
              :multiple="true"
            ></biz-year-select>
          </el-form-item>
          <el-form-item label="地区">
            <biz-national-region
              ref="regionValueRef"
              v-model="localSkuProperty.region"
              :check-strictly="true"
              placeholder="请选择地区"
            ></biz-national-region>
          </el-form-item>
          <el-form-item label="培训形式">
            <biz-training-mode-select v-model="localSkuProperty.trainingForm"></biz-training-mode-select>
          </el-form-item>
          <!-- <el-form-item label="培训方案类型">
            <biz-scheme-type v-model="schemeTypeInfo"></biz-scheme-type>
          </el-form-item> -->
          <el-form-item label="培训方案名称">
            <el-input clearable placeholder="请输入培训方案名称" v-model="schemeName" />
          </el-form-item>
          <el-form-item label="行业">
            <biz-industry-select
              v-model="localSkuProperty.industry"
              @clearIndustrySelect="handleClearIndustrySelect"
              @industryPropertyId="handleIndustryPropertyId"
              @industryInfos="handleIndustryInfos"
            ></biz-industry-select>
          </el-form-item>
          <el-form-item
            label="科目类型"
            v-if="
              skuVisible.subjectType &&
              localSkuProperty.industry &&
              envConfig.societyIndustryId &&
              localSkuProperty.industry === envConfig.societyIndustryId
            "
          >
            <biz-accounttype-select
              v-model="localSkuProperty.subjectType"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            >
            </biz-accounttype-select>
          </el-form-item>
          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.societyIndustryId &&
              localSkuProperty.industry === envConfig.societyIndustryId
            "
          >
            <biz-major-cascader
              v-model="localSkuProperty.societyTrainingMajor"
              placeholder="请选择培训专业"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.constructionIndustryId &&
              localSkuProperty.industry === envConfig.constructionIndustryId
            "
          >
            <biz-training-category-select
              placeholder="请选择培训类别"
              v-model="localSkuProperty.trainingCategory"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              @updateTrainingCategory="handleUpdateTrainingCategory"
            />
          </el-form-item>

          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.constructionIndustryId &&
              localSkuProperty.industry === envConfig.constructionIndustryId
            "
          >
            <biz-major-select
              v-model="localSkuProperty.constructionTrainingMajor"
              placeholder="请选择培训专业"
              :industry-property-id="industryPropertyId"
              :training-category-id="trainingCategoryId"
            />
          </el-form-item>
          <!-- 工勤行业 -->
          <el-form-item
            label="技术等级"
            v-if="
              skuVisible.technicalGrade &&
              localSkuProperty.industry &&
              envConfig.workServiceId &&
              localSkuProperty.industry === envConfig.workServiceId
            "
          >
            <biz-technical-grade-select
              v-model="localSkuProperty.technicalGrade"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            ></biz-technical-grade-select>
          </el-form-item>
          <!-- 卫生行业 -->
          <el-form-item
            label="培训类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.professionHealthIndustryId &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-training-category-select
              placeholder="请选择培训类别"
              v-model="localSkuProperty.trainingCategory"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训对象"
            v-if="
              skuVisible.trainingObject &&
              localSkuProperty.industry &&
              envConfig.professionHealthIndustryId &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-training-object-select
              placeholder="请选择培训对象"
              v-model="localSkuProperty.trainingObject"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              @updateTrainingCategory="handleWSUpdateTrainingObject"
            />
          </el-form-item>
          <el-form-item
            label="岗位类别"
            v-if="
              skuVisible.positionCategory &&
              localSkuProperty.industry &&
              envConfig.professionHealthIndustryId &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-obj-category-select
              placeholder="请选择岗位类别"
              v-model="localSkuProperty.positionCategory"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              :training-object-id="trainingObjectId"
            />
          </el-form-item>
          <!-- 教师行业 -->
          <el-form-item
            label="学段"
            v-if="
              skuVisible.learningPhase &&
              localSkuProperty.industry &&
              envConfig.teacherIndustryId &&
              localSkuProperty.industry === envConfig.teacherIndustryId
            "
          >
            <biz-study-period
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              placeholder="请选择学段"
              v-model="localSkuProperty.learningPhase"
              @updateStudyPeriod="updateStudyPeriod"
            />
          </el-form-item>
          <el-form-item
            label="学科"
            v-if="
              skuVisible.discipline &&
              localSkuProperty.industry &&
              envConfig.teacherIndustryId &&
              localSkuProperty.industry === envConfig.teacherIndustryId
            "
          >
            <biz-subject
              :study-period-id="localSkuProperty.learningPhase"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              placeholder="请选择学科"
              v-model="localSkuProperty.discipline"
            />
          </el-form-item>
          <!-- <el-form-item
            label="执业类别"
            v-if="
              skuVisible.practitionerCategory &&
                localSkuProperty.industry &&
                envConfig.medicineIndustryId &&
                localSkuProperty.industry === envConfig.medicineIndustryId
            "
          >
            <biz-practicing-category-cascader
              v-model="localSkuProperty.pharmacistIndustry"
              :industryId="localSkuProperty.industry"
            ></biz-practicing-category-cascader>
          </el-form-item> -->
          <slot name="promotion-select" :localSkuProperty="localSkuProperty">
            <el-form-item label="分销推广">
              <biz-promotion-select v-model="tradeReportRequest.saleChannel"></biz-promotion-select>
            </el-form-item>
          </slot>
          <el-form-item label="分销商" v-if="isOpenFxServer">
            <biz-distributor-select
              v-model="tradeReportRequest.distributorId"
              :name="distributorName"
            ></biz-distributor-select>
          </el-form-item>
          <el-form-item label="推广门户简称" v-if="isOpenFxServer">
            <biz-portal-select
              :disabled="tradeReportRequest.notDistributionPortal"
              v-model="portalId"
              :name="portalName"
            ></biz-portal-select>
          </el-form-item>
          <el-form-item label="报名时间">
            <double-date-picker
              :begin-create-time.sync="tradeReportRequest.tradeTime.begin"
              :end-create-time.sync="tradeReportRequest.tradeTime.end"
              beginTimePlaceholder="请选择报名成功时间"
              endTimePlaceholder="请选择报名成功时间"
              endDefaultTime="23:59:59"
            ></double-date-picker>
          </el-form-item>
          <el-form-item label="专题">
            <biz-special-select v-model="saleChannels" @input="changeSaleChannels"></biz-special-select>
          </el-form-item>
          <el-form-item label="专题名称">
            <biz-special-name v-model="tradeReportRequest.saleChannelName"></biz-special-name>
          </el-form-item>
          <slot name="sale-channel" :localSkuProperty="localSkuProperty"></slot>
          <slot name="remove-plan" :localSkuProperty="localSkuProperty"></slot>
          <el-form-item v-if="isOpenFxServer">
            <el-checkbox
              label="查看非门户推广数据"
              name="type"
              v-model="tradeReportRequest.notDistributionPortal"
            ></el-checkbox>
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="doSearch()">查询</el-button>
            <el-button v-if="$hasPermission('export')" desc="导出" actions="exportListData" @click="exportListData"
              >导出列表数据</el-button
            >
          </template>
        </hb-search-wrapper>

        <!--操作栏-->
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert f-clear">
            <div class="f-c6 f-fl">
              搜索结果合计：当前净开通
              <span class="f-fb f-co">{{
                getQueryRegionReportList.statisticsM.tradeCountSummaryInfo.netTradeSuccessCount
              }}</span>
              人次，成交总额
              <span class="f-fb f-co">¥ {{ getQueryRegionReportList.statisticsM.totalNetAmount }}</span>
            </div>
            <div class="f-fr f-csp f-flex f-align-center" @click="dialog6 = true">
              <i class="el-icon-info f-f16 f-mr5"></i>统计口径说明
            </div>
          </el-alert>
        </div>
        <!--表格-->
        <!-- v-if="showTable"
          :default-expand-all="openFlag" -->
        <el-table
          stripe
          :data="tableData2"
          row-key="regionId"
          lazy
          :load="load"
          v-loading="trainSchemeQuery.loading"
          :tree-props="{ children: 'children', hasChildren: 'leaf' }"
          border
          :summary-method="getSummaries"
          :show-summary="showSum"
          max-height="500px"
          class="m-table is-statistical f-mt10"
          ref="schemeTable"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="工作单位所在地区" prop="name" min-width="220" fixed="left"> </el-table-column>
          <el-table-column label="合计" header-align="center">
            <el-table-column label="开通" prop="summaryInfo.tradeSuccessCount" min-width="90" align="right">
              <template slot-scope="scope">
                {{ scope.row.summaryInfo.tradeSuccessCount }}
              </template>
            </el-table-column>
            <el-table-column label="退班" prop="summaryInfo.returnCount" min-width="90" align="right">
              <template slot-scope="scope">
                {{ scope.row.summaryInfo.returnCount }}
              </template>
            </el-table-column>
            <el-table-column label="换入(换班)" prop="summaryInfo.exchangeInCount" min-width="90" align="right">
              <template slot-scope="scope">
                {{ scope.row.summaryInfo.exchangeInCount }}
              </template>
            </el-table-column>
            <el-table-column label="换出(换班)" prop="summaryInfo.exchangeOutCount" min-width="90" align="right">
              <template slot-scope="scope">
                {{ scope.row.summaryInfo.exchangeOutCount }}
              </template>
            </el-table-column>
            <el-table-column label="净开通" prop="summaryInfo.netTradeSuccessCount" min-width="90" align="center">
              <template slot-scope="scope"> {{ scope.row.summaryInfo.netTradeSuccessCount }}</template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="个人缴费" align="center">
            <el-table-column label="线上支付" align="center">
              <el-table-column label="开通" prop="online.tradeSuccessCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[0].purchaseChannel === 1 &&
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].paymentType === 1
                  "
                >
                  {{
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo
                      .tradeSuccessCount
                  }}
                </template>
              </el-table-column>
              <el-table-column label="退班" prop="online.returnCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[0].purchaseChannel === 1 &&
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo
                      .returnCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换入(换班)" prop="online.exchangeInCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[0].purchaseChannel === 1 &&
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo
                      .exchangeInCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换出(换班)" prop="online.exchangeOutCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[0].purchaseChannel === 1 &&
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo
                      .exchangeOutCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="净开通" min-width="90" prop="online.netTradeSuccessCount" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[0].purchaseChannel === 1 &&
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo
                      .netTradeSuccessCount
                  }}</template
                >
              </el-table-column>
            </el-table-column>
          </el-table-column>
          <el-table-column label="集体报名" header-align="center">
            <el-table-column label="线上支付" header-align="center">
              <el-table-column label="开通" prop="team.online.tradeSuccessCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo
                      .tradeSuccessCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="退班" prop="team.online.returnCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo
                      .returnCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换入(换班)" prop="team.online.exchangeInCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo
                      .exchangeInCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换出(换班)" prop="team.online.exchangeOutCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo
                      .exchangeOutCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="净开通" prop="team.online.netTradeSuccessCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo
                      .netTradeSuccessCount
                  }}</template
                >
              </el-table-column>
            </el-table-column>
            <el-table-column label="线下支付" header-align="center">
              <el-table-column label="开通" prop="team.offline.tradeSuccessCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo
                      .tradeSuccessCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="退班" prop="team.offline.returnCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo
                      .returnCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换入(换班)" prop="team.offline.exchangeInCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo
                      .exchangeInCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换出(换班)" prop="team.offline.exchangeOutCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo
                      .exchangeOutCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="净开通" prop="team.offline.netTradeSuccessCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo
                      .netTradeSuccessCount
                  }}</template
                >
              </el-table-column>
            </el-table-column>
          </el-table-column>
          <el-table-column label="导入开通" header-align="center">
            <el-table-column label="线下支付" header-align="center">
              <el-table-column label="开通" prop="export.tradeSuccessCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList[2].purchaseChannel === 3 &&
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo
                      .tradeSuccessCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="退班" prop="export.returnCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList[2].purchaseChannel === 3 &&
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo
                      .returnCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换入(换班)" prop="export.exchangeInCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList[2].purchaseChannel === 3 &&
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo
                      .exchangeInCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换出(换班)" prop="export.exchangeOutCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList[2].purchaseChannel === 3 &&
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo
                      .exchangeOutCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="净开通" prop="export.netTradeSuccessCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList[2].purchaseChannel === 3 &&
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo
                      .netTradeSuccessCount
                  }}</template
                >
              </el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table>
        <!--分页-->
        <!-- <hb-pagination class="f-mt15 f-tr" :page="trainSchemePage" v-bind="trainSchemePage"> </hb-pagination> -->
        <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务管理-地区开通统计</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
          </div>
        </el-dialog>
        <el-drawer title="统计口径说明" :visible.sync="dialog6" size="900px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
            </el-alert>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">搜索条件说明</span>
              （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
            </p>
            <el-table stripe :data="searchConditions" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  {{ scope.row.field }}
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  {{ scope.row.description }}
                </template>
              </el-table-column>
            </el-table>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">列表字段及详细说明</span>
              （列表下的数据显示受搜索条件的约束，统计单位：人次）
            </p>
            <el-table stripe :data="listFields" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  {{ scope.row.field }}
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  {{ scope.row.description }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import Page from '@hbfe/jxjy-admin-common/src/models/Page'
  import StaticticalReportManagerModule from '@api/service/management/statisticalReport/StaticticalReportManagerModule'
  import { Query, UiPage } from '@hbfe/common'
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'

  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { TradeReportRequest as TradeReportReq } from '@api/ms-gateway/ms-data-export-front-gateway-DataExportBackstage'
  import {
    CommoditySkuRequest,
    CommoditySkuRequest12,
    CommoditySkuSortRequest,
    DateScopeRequest,
    DoubleScopeRequest,
    RegionSkuPropertyRequest,
    RegionSkuPropertySearchRequest,
    SchemeRequest1,
    SkuPropertyRequest,
    TradeReportRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import { QueryRegionReportList } from '@api/service/management/statisticalReport/query/QueryRegionReportList'
  import { RegionOpenReportFormResponseVo } from '@api/service/management/statisticalReport/query/vo/RegionOpenReportFormResponseVo'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import { cloneDeep } from 'lodash'
  // import { ReportSummaryResponse } from '@api/service/management/statisticalReport/query/vo/RegionReportSummaryResponse'
  import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
  // import QueryRegisterRegion from '@api/service/common/basic-data-dictionary/query/QueryRegisterRegion'
  import NewRegionTreeVo from '@hbfe/jxjy-admin-common/src/models/NewRegionTreeVo'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-common/src/models/sku'
  import { RegionResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import SaleChannelType from '@api/service/common/enums/trade/SaleChannelType'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  class NewTradeReportRequest extends TradeReportRequest {
    //是否来源于专题
    subjectType = ''
    //专题名称
    subjectName = ''
  }

  @Component({
    components: {
      DoubleDatePicker,
      BizPortalSelect,
      BizDistributorSelect
    }
  })
  export default class extends Vue {
    @Ref('regionValueRef') regionValueRef: any
    @Ref('schemeTable') schemeTableRef: any
    dialog6 = false
    searchConditions = [
      { field: '方案属性', description: '选择不同的行业，可以查询不同的培训属性值。' },
      { field: '方案类型', description: '创建培训方案时定义的类型属性，如培训班等' },
      { field: '选择方案', description: '选择全部已发布的培训方案' },
      {
        field: '工作单位所在地区',
        description:
          '默认显示省份下各地市的数据，如xx省、省直单位、xx市等；选xx市则显示的是xx市各区县数据，如xx市、市直、xx区等'
      },
      { field: '报名成功时间', description: '查看在某个开通时间内，地区开通方案数据' },
      { field: '分销推广', description: '默认显示所有订单的开通数据，支持选择查看分销订单或非分销订单的开通数据' }
    ]

    listFields = [
      { field: '工作单位所在地区', description: '学员工作单位所在地区信息，以学员报名时信息为准' },
      { field: '开通', description: '统计搜索时间段内实际开通成功的班级人次数据，开通成功：指实际缴费成功的情况' },
      { field: '退班', description: '统计搜索时间段内，通过管理后台执行退班操作时生成的退班订单数' },
      { field: '换出（换班）', description: '统计搜索时间段内，通过管理后台执行换班操作时生成换出班级的订单数' },
      { field: '换入（换班）', description: '统计搜索时间段内，通过管理后台执行换班操作时生成换入班级的订单数' },
      {
        field: '净开通',
        description: '净开通=开通+换入-换出-退班；如9.1查8.1-8.31开通数100，退班数2，换出5，换入5，净开通=100+5-2-5=98'
      }
    ]
    maps = new Map()
    pid: string
    select = ''
    input = ''
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    tableData2 = [] as any[]
    tableData10 = [] as any[]
    // 分页参数 - 培训方案
    trainSchemePage: UiPage
    // 分页加载参数 - 培训方案
    trainSchemeQuery: Query = new Query()
    // 培训方案业务状态层入口
    schemeBusinessEntry: QueryTrainClassCommodityList = new QueryTrainClassCommodityList()
    // 培训方案名称
    schemeName = ''
    /**
     * 培训方案类型
     */
    schemeTypeInfo: Array<string> = new Array<string>()
    // 培训方案列表
    trainSchemeList: Array<TrainClassCommodityVo> = new Array<TrainClassCommodityVo>()
    // 排序策略
    sortPolicy: Array<CommoditySkuSortRequest> = new Array<CommoditySkuSortRequest>()
    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''

    /**
     * 培训类别Id
     */
    trainingCategoryId = ''
    /**
     * 培训对象Id
     */
    trainingObjectId = ''
    /**
     * 推广门户id
     */
    portalId = ''
    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: false,
      // 培训类别
      trainingCategory: false,
      // 岗位类别
      positionCategory: false,
      // 培训对象
      trainingObject: false,
      // 技术等级
      technicalGrade: false,
      // 学段
      learningPhase: false,
      // 科目
      discipline: false,
      // 证书类型
      certificatesType: false,
      // 执业类别
      practitionerCategory: false
    }
    /**
     * 当前网校信息
     */
    envConfig = {
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 工勤行业Id
      workServiceId: '',
      // 职业卫生行业Id
      professionHealthIndustryId: '',
      // 教师行业id
      teacherIndustryId: '',
      // 药师行业id
      medicineIndustryId: ''
    }
    //门户简介
    portalName = ''
    //分销商名称
    distributorName = ''
    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuProperty = new SchemeSkuProperty()
    tableData1 = [
      {
        id: 1,
        field01: '福建省',
        hasChildren: true
      },
      {
        id: 2,
        field01: '江西省',
        hasChildren: true
      },
      {
        id: 3,
        field01: '河南省',
        hasChildren: true
      },
      {
        id: 4,
        field01: '广东省',
        hasChildren: true
      },
      {
        id: 5,
        field01: '湖南省',
        hasChildren: true
      }
    ]
    page = new Page()
    form = {
      data1: ''
    }
    getQueryRegionReportList: QueryRegionReportList =
      StaticticalReportManagerModule.queryStaticticalReportFactory.getQueryRegionReportList()
    //分页查询入参
    tradeReportRequest: TradeReportRequest = new TradeReportRequest()
    //区县分页查询入参
    tradeReportRequestChildren: NewTradeReportRequest = new NewTradeReportRequest()
    //导出查询入参
    exportQueryParam: TradeReportReq = new TradeReportReq()
    //分页查询结果
    RegionOpenReportFormResponseVo: Array<RegionOpenReportFormResponseVo> = new Array<RegionOpenReportFormResponseVo>()
    //区县查询结果
    RegionOpenReportFormResponseVoChildren: Array<RegionOpenReportFormResponseVo> =
      new Array<RegionOpenReportFormResponseVo>()
    //导出成功弹窗
    exportSuccessVisible = false
    // 查询参数 - 培训方案
    trainSchemeQueryParam: CommoditySkuRequest
    provinces: Array<NewRegionTreeVo> = new Array<NewRegionTreeVo>() // 初始省级地区
    expanArea: Array<NewRegionTreeVo> = new Array<NewRegionTreeVo>() //懒加载拓展地区

    locationUnit: string[] = [] //单位所在地
    staticount = 0
    showSum = true

    ServiceRegion: RegionTreeVo[] = []
    areaOptions = [] as any[]
    /**
     * 是否专题
     */
    saleChannels: boolean = null
    /**
     * 判断当前网校是否开启分销服务
     */
    get isOpenFxServer() {
      return CapabilityServiceConfig.fxCapabilityEnable
    }
    // 重置表格 控制默认表格展开
    // showTable = true
    // openFlag = false
    constructor() {
      super()
      this.trainSchemePage = new UiPage(this.pageScheme, this.pageScheme)
    }
    async created() {
      this.initQueryParam()
      this.areaOptions = await QueryBusinessRegion.getCountrywideRegion()
      await this.getServiceArea()
      await this.initialArea()
      await Promise.all([this.getSummaryInServicer(), this.pageScheme()])
      //console.log(this.RegionOpenReportFormResponseVo)
      //console.log(this.provinces)
      // debugger
      this.dataMerge()
      // console.log(this.tableData2, '表格数据')
    }
    async getSummaryInServicer() {
      // if (this.tradeReportRequest.buyerAreaPath.length >= 2) {
      //   delete this.tradeReportRequest.buyerAreaPath
      // }
      this.getPageQueryParams()
      await this.getQueryRegionReportList.getCommodityReportSummaryInServicer(this.tradeReportRequest)
    }
    //
    changeSaleChannels() {
      this.tradeReportRequest.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(this.saleChannels)
    }
    /**
     * 分页查询
     */
    async pageScheme() {
      this.trainSchemeQuery.loading = true
      try {
        this.getPageQueryParams()
        // const list = await this.getQueryRegionReportList.listRegionOpenReportFormsInServier(this.tradeReportRequest)
        let list = await this.getQueryRegionReportList.listRegionOpenReportFormsInServier(this.tradeReportRequest)
        let sum = 0
        list = list.map((item) => {
          if (item.regionId == '419000') {
            if (sum == 1) {
              item.regionId = '429000'
            } else if (sum == 2) {
              item.regionId = '469000'
            }
            sum++
          }
          return item
        })
        // const childrenList = await this.getQueryRegionReportList.listRegionOpenReportFormsInServier(
        //   this.tradeReportRequestChildren
        // )
        // this.RegionOpenReportFormResponseVoChildren = childrenList.map(listItem => {
        //   listItem.purchaseChannelStatisticInfoList.map(subListItem => {
        //     subListItem.paymentTypeStatisticInfoList.sort(function(a: any, b: any) {
        //       return b.paymentType - a.paymentType
        //     })
        //   })
        //   listItem.purchaseChannelStatisticInfoList = listItem.purchaseChannelStatisticInfoList
        //     .filter(function(item, index, e) {
        //       return e.findIndex(el => el.purchaseChannel == item.purchaseChannel) === index
        //     })
        //     .sort(function(a: any, b: any) {
        //       return a.purchaseChannel - b.purchaseChannel
        //     })
        //   return listItem
        // })
        this.RegionOpenReportFormResponseVo = list.map((listItem) => {
          listItem.purchaseChannelStatisticInfoList.map((subListItem) => {
            subListItem.paymentTypeStatisticInfoList.sort(function (a: any, b: any) {
              return b.paymentType - a.paymentType
            })
          })
          listItem.purchaseChannelStatisticInfoList = listItem.purchaseChannelStatisticInfoList
            .filter(function (item, index, e) {
              return e.findIndex((el) => el.purchaseChannel == item.purchaseChannel) === index
            })
            .sort(function (a: any, b: any) {
              return a.purchaseChannel - b.purchaseChannel
            })
          return listItem
        })
        // console.log(
        //   this.RegionOpenReportFormResponseVo,
        //   this.RegionOpenReportFormResponseVoChildren,
        //   'lllxqlxqlxqlllxqlxqlxqlllxqlxqlxqlllxqlxqlxq'
        // )
      } catch (e) {
        console.log('获取培训方案分页列表失败！', e)
      } finally {
        if (this.maps.size) {
          // 获取节点
          const node = this.maps.get(this.pid)
          // 获取需要刷新节点
          const { tree, treeNode, resolve } = node
          // 重新加载子节点数据
          this.load(tree, treeNode, resolve)
        }
        //处理切换页数后行数错位问题
        ;(this.$refs['schemeTable'] as any)?.doLayout()
        // this.tableData2 = [...this.provinces, ...this.RegionOpenReportFormResponseVo]
        this.getExportQueryParam()
      }
    }

    getExportQueryParam() {
      if (this.portalId) {
        this.tradeReportRequest.portalId = this.portalId
      }
      if (this.tradeReportRequest.notDistributionPortal) {
        this.tradeReportRequest.portalId = ''
      }
      this.exportQueryParam = Object.assign(new TradeReportReq(), this.tradeReportRequest)
      if (this.locationUnit.length <= 1) {
        this.exportQueryParam.isAllCotained = true
        const list = new Array<string>()
        this.exportQueryParam?.buyerAreaPath?.map((item) => {
          const province = '/' + item.split('/')[1]
          if (!list.includes(province)) {
            list.push(province)
          }
        })
        this.exportQueryParam.buyerAreaPath = list
      } else {
        this.exportQueryParam.isAllCotained = false
      }
    }

    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportRegionOpenStatistical' }
      })
    }
    /**
     * 初始化查询参数
     */
    initQueryParam() {
      this.portalName = ''
      this.distributorName = ''
      this.tradeReportRequest = new TradeReportRequest()
      this.tradeReportRequestChildren = new NewTradeReportRequest()
      this.tradeReportRequest.tradeTime = new DateScopeRequest()
      this.tradeReportRequestChildren.tradeTime = new DateScopeRequest()
      this.tradeReportRequest.tradeTime.begin = ''
      this.tradeReportRequest.tradeTime.end = ''
      this.tradeReportRequest.commoditySku = new CommoditySkuRequest12()
      this.tradeReportRequestChildren.commoditySku = new CommoditySkuRequest12()

      this.tradeReportRequest.commoditySku.saleTitle = ''
      this.tradeReportRequest.commoditySku.scheme = new SchemeRequest1()
      this.tradeReportRequestChildren.commoditySku.scheme = new SchemeRequest1()

      this.tradeReportRequest.commoditySku.scheme.schemeType = ''
      this.tradeReportRequest.commoditySku.scheme.schemePeriodScope = new DoubleScopeRequest()
      this.tradeReportRequest.commoditySku.skuProperty = new SkuPropertyRequest()
      this.tradeReportRequest.commoditySku.skuProperty.year = new Array<string>()
      this.tradeReportRequest.commoditySku.skuProperty.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      this.tradeReportRequest.commoditySku.skuProperty.regionSkuPropertySearch.region =
        new Array<RegionSkuPropertyRequest>()
      this.tradeReportRequest.commoditySku.skuProperty.industry = new Array<string>()
      this.tradeReportRequest.commoditySku.skuProperty.subjectType = new Array<string>()
      this.tradeReportRequest.commoditySku.skuProperty.trainingCategory = new Array<string>()
      this.tradeReportRequest.commoditySku.skuProperty.trainingProfessional = new Array<string>()
      // 初始化添加 学科、学段置空
      // todo
      this.tradeReportRequestChildren.commoditySku.skuProperty = new SkuPropertyRequest()
      this.tradeReportRequest.buyerAreaPath = new Array<string>()
      this.sortPolicy = new Array<CommoditySkuSortRequest>()
      this.schemeTypeInfo = new Array<string>()
      // this.queryTrainClassReportList.statisticsM.tradeCountSummaryInfo = new SubOrderStatisticDto()
    }
    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.technicalGrade = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      this.localSkuProperty.discipline = ''
      this.localSkuProperty.learningPhase = ''
      this.localSkuProperty.pharmacistIndustry = [] as string[]
    }
    /**
     * 学段变化清空学科
     */
    updateStudyPeriod() {
      this.localSkuProperty.discipline = ''
    }
    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => el.code == 'SUBJECT_TYPE')
      const configTrainingCategory = configList.findIndex((el) => el.code == 'TRAINING_CATEGORY')
      const configPositionCategory = configList.findIndex((el) => el.code == 'POSITION_CATEGORY')
      const configTrainingObject = configList.findIndex((el) => el.code == 'TRAINNING_OBJECT')
      const configTechnicalLevel = configList.findIndex((el) => el.code == 'JOB_LEVEL')
      // 修改
      const configTechnicalStudying = configList.findIndex((el) => el.code == 'LEARNING_PHASE')
      const configTechnicalSubject = configList.findIndex((el) => el.code == 'DISCIPLINE')
      const configCertificatesType = configList.findIndex((el) => el.code == 'CERTIFICATES_TYPE')
      const configPractitionerCategory = configList.findIndex((el) => (el.code = 'PRACTITIONER_CATEGORY'))
      this.skuVisible.subjectType = configSubjectType > -1 ? true : false
      this.skuVisible.trainingCategory = configTrainingCategory > -1 ? true : false
      this.skuVisible.positionCategory = configPositionCategory > -1 ? true : false
      this.skuVisible.trainingObject = configTrainingObject > -1 ? true : false
      this.skuVisible.technicalGrade = configTechnicalLevel > -1 ? true : false
      // 获取教师配置
      this.skuVisible.learningPhase = configTechnicalStudying > -1 ? true : false
      this.skuVisible.discipline = configTechnicalSubject > -1 ? true : false
      this.skuVisible.certificatesType = configCertificatesType > -1 ? true : false
      this.skuVisible.practitionerCategory = configPractitionerCategory > -1 ? true : false
    }
    // 卫生行业培训类别联动
    handleWSUpdateTrainingObject(value: string) {
      this.localSkuProperty.positionCategory = ''
      this.trainingObjectId = value
    }
    /**
     * 响应组件行业Id集合传参
     */
    handleIndustryInfos(values: any) {
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.professionHealthIndustryId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
      this.envConfig.medicineIndustryId = values.medicineIndustryId || ''
    }
    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
    }
    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      this.saleChannels = null
      this.tradeReportRequest.saleChannels = new Array<number>()
      await this.initQueryParam()
      this.portalId = null
      this.$set(this.tradeReportRequest, 'notDistributionPortal', false)
      this.localSkuProperty = new SchemeSkuProperty()
      // this.onShelveStatus = null
      this.schemeName = ''
      this.locationUnit = []
      this.regionValueRef.selctValue = []
      this.sortPolicy = new Array<CommoditySkuSortRequest>()
      this.tableData2 = []
      // this.tradeReportRequest.buyerAreaPath = ['/320000']
      await this.initialArea()
      await Promise.all([this.getSummaryInServicer(), this.pageScheme()])
      this.dataMerge()
    }
    /**
     * 数据合并
     *
     */
    async dataMerge() {
      this.tableData2 = []
      this.tableData10 = []
      try {
        this.RegionOpenReportFormResponseVo.map((item) => {
          this.provinces.map((itm) => {
            if (item.regionId === itm.id) {
              //id相同时数据合并
              const obj = Object.assign(item, itm)
              this.tableData2.push(obj)
            }
          })
        })
        // this.RegionOpenReportFormResponseVoChildren.map(item => {
        //   this.provinces.map(itm => {
        //     // debugger
        //     if (item.regionId === itm.id && itm.level == 1) {
        //       //id相同时数据合并
        //       const obj = Object.assign(item, itm)
        //       this.tableData10.push(obj)
        //     }
        //   })
        // })
        const idList: Array<string> = []
        const indexList: Array<number> = []
        this.tableData2.map((item, index) => {
          if (
            (index < this.tableData2.length - 1 &&
              item.regionId.substr(0, 2) != this.tableData2[index + 1].regionId.substr(0, 2)) ||
            index == this.tableData2.length - 1
          ) {
            idList.push(item.regionId.substr(0, 2) + '0000')
            if (index != this.tableData2.length - 1) {
              indexList.push(index)
            }
          }
        })
        const area: any = await QueryBusinessRegion.queryRegionsNameByIds(idList)
        const provinceList: any = []
        for (const [key, value] of area) {
          provinceList.push({ name: value, regionId: key })
        }
        // 调整三级结构 添加省级
        this.tableData2 = provinceList.map((item: any, index: number) => {
          item.summaryInfo = {
            exchangeInCount: 0,
            exchangeOutCount: 0,
            netTradeSuccessCount: 0,
            returnCount: 0,
            tradeSuccessCount: 0
          }
          item.purchaseChannelStatisticInfoList = [
            {
              purchaseChannel: 1,
              paymentTypeStatisticInfoList: [
                {
                  paymentType: 1,
                  statisticInfo: {
                    exchangeInCount: 0,
                    exchangeOutCount: 0,
                    netTradeSuccessCount: 0,
                    returnCount: 0,
                    tradeSuccessCount: 0
                  }
                }
              ]
            },
            {
              purchaseChannel: 2,
              paymentTypeStatisticInfoList: [
                {
                  paymentType: 2,
                  statisticInfo: {
                    exchangeInCount: 0,
                    exchangeOutCount: 0,
                    netTradeSuccessCount: 0,
                    returnCount: 0,
                    tradeSuccessCount: 0
                  }
                },
                {
                  paymentType: 1,
                  statisticInfo: {
                    exchangeInCount: 0,
                    exchangeOutCount: 0,
                    netTradeSuccessCount: 0,
                    returnCount: 0,
                    tradeSuccessCount: 0
                  }
                }
              ]
            },
            {
              purchaseChannel: 3,
              paymentTypeStatisticInfoList: [
                {
                  paymentType: 2,
                  statisticInfo: {
                    exchangeInCount: 0,
                    exchangeOutCount: 0,
                    netTradeSuccessCount: 0,
                    returnCount: 0,
                    tradeSuccessCount: 0
                  }
                }
              ]
            }
          ]
          if (index === 0) {
            if (indexList.length > 0) {
              item.children = this.tableData2.filter((sitem, sindex) => {
                return sindex <= indexList[0]
              })
            } else {
              item.children = this.tableData2
            }
          } else if (index === provinceList.length - 1) {
            item.children = this.tableData2.filter((sitem, sindex) => {
              return sindex > indexList[indexList.length - 1]
            })
          } else {
            item.children = this.tableData2.filter((sitem, sindex) => {
              return indexList[index - 1] < sindex && sindex <= indexList[index]
            })
          }
          item.children.map((mitem: any) => {
            // 合计数据
            item.summaryInfo.exchangeInCount += mitem.summaryInfo.exchangeInCount || 0
            item.summaryInfo.exchangeOutCount += mitem.summaryInfo.exchangeOutCount || 0
            item.summaryInfo.netTradeSuccessCount += mitem.summaryInfo.netTradeSuccessCount || 0
            item.summaryInfo.returnCount += mitem.summaryInfo.returnCount || 0
            item.summaryInfo.tradeSuccessCount += mitem.summaryInfo.tradeSuccessCount || 0
            // 个人缴费
            item.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.exchangeInCount +=
              mitem.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.exchangeInCount ||
              0
            item.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.exchangeOutCount +=
              mitem.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo
                .exchangeOutCount || 0
            item.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.netTradeSuccessCount +=
              mitem.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo
                .netTradeSuccessCount || 0
            item.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.returnCount +=
              mitem.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.returnCount || 0
            item.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.tradeSuccessCount +=
              mitem.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo
                .tradeSuccessCount || 0
            // 导入开通
            item.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.exchangeInCount +=
              mitem.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.exchangeInCount ||
              0
            item.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.exchangeOutCount +=
              mitem.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo
                .exchangeOutCount || 0
            item.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.netTradeSuccessCount +=
              mitem.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo
                .netTradeSuccessCount || 0
            item.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.returnCount +=
              mitem.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.returnCount || 0
            item.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.tradeSuccessCount +=
              mitem.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo
                .tradeSuccessCount || 0
            // 集体缴费
            item.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo.exchangeInCount +=
              mitem.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo.exchangeInCount ||
              0
            item.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo.exchangeOutCount +=
              mitem.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo
                .exchangeOutCount || 0
            item.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo.netTradeSuccessCount +=
              mitem.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo
                .netTradeSuccessCount || 0
            item.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo.returnCount +=
              mitem.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo.returnCount || 0
            item.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo.tradeSuccessCount +=
              mitem.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo
                .tradeSuccessCount || 0

            item.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo.exchangeInCount +=
              mitem.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo.exchangeInCount ||
              0
            item.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo.exchangeOutCount +=
              mitem.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo
                .exchangeOutCount || 0
            item.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo.netTradeSuccessCount +=
              mitem.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo
                .netTradeSuccessCount || 0
            item.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo.returnCount +=
              mitem.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo.returnCount || 0
            item.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo.tradeSuccessCount +=
              mitem.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo
                .tradeSuccessCount || 0
          })

          return item
        })
        // this.showTable = false
        // this.openFlag = this.tableData2.length == 1
        // this.$nextTick(() => {
        //   this.showTable = true
        // })
        this.trainSchemeQuery.loading = false
        console.log(this.tableData2, 'tab2')
      } catch (err) {
        console.log(err)
      }
    }
    /**
     * 加载第一页
     */
    async searchBase() {
      this.trainSchemePage.pageNo = 1
      await this.pageScheme()
    }
    /**
     * 处理列表查询参数
     */
    getPageQueryParams() {
      this.getLocalSkuProperty()
      this.tradeReportRequest.commoditySku.saleTitle = this.schemeName || undefined
      this.configureTrainSchemeQueryParam()
      this.tradeReportRequestChildren.commoditySku = this.tradeReportRequest.commoditySku
      this.tradeReportRequestChildren.tradeTime = this.tradeReportRequest.tradeTime
      if (this.portalId) {
        this.tradeReportRequest.portalId = this.portalId
      }
      if (this.tradeReportRequest.notDistributionPortal) {
        this.tradeReportRequest.portalId = ''
      }
    }
    /**
     * 配置查询参数
     */
    configureTrainSchemeQueryParam() {
      // 处理培训方案类型
      if (!this.schemeTypeInfo.length) {
        this.tradeReportRequest.commoditySku.scheme.schemeType = undefined
        this.tradeReportRequestChildren.commoditySku.scheme.schemeType = undefined
      }
      const schemeType = this.schemeTypeInfo[this.schemeTypeInfo.length - 1]
      // if (schemeType === 'chooseCourseLearning' || schemeType === 'autonomousCourseLearning') {
      this.tradeReportRequest.commoditySku.scheme.schemeType = schemeType
      this.tradeReportRequestChildren.commoditySku.scheme.schemeType = schemeType
      // } else {
      //   this.tradeReportRequest.commoditySku.scheme.schemeType = undefined
      //   this.tradeReportRequestChildren.commoditySku.scheme.schemeType = undefined
      // }
    }
    /**
     * 获取网校地区
     */
    async getServiceArea() {
      this.ServiceRegion = await QueryBusinessRegion.getServiceOrIndustry(1)
    }
    /**
     * 获取本地sku选项
     */
    getLocalSkuProperty() {
      // const skuProperties = cloneDeep(this.trainSchemeQueryParam.skuPropertyRequest)
      const skuProperties = cloneDeep(this.tradeReportRequest.commoditySku.skuProperty)
      skuProperties.year = !this.localSkuProperty.year?.length ? ([] as string[]) : this.localSkuProperty.year
      skuProperties.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
      const localRegion = cloneDeep(this.localSkuProperty.region)
      if (Array.isArray(localRegion) && localRegion.length) {
        const option = new RegionSkuPropertyRequest()
        option.province = localRegion.length >= 1 ? localRegion[0] : undefined
        option.city = localRegion.length >= 2 ? localRegion[1] : undefined
        option.county = localRegion.length >= 3 ? localRegion[2] : undefined
        // option.province = '320000'
        // option.city = localRegion ? localRegion[0] : undefined
        // option.county = undefined
        skuProperties.regionSkuPropertySearch.region.push(option)
        skuProperties.regionSkuPropertySearch.regionSearchType = 1
      } else {
        skuProperties.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
        // this.ServiceRegion.map(item => {
        //   const option = new RegionSkuPropertyRequest()
        //   if (item.parentId == '0' && item.id.substr(2, 4) == '0000') {
        //     option.province = item.id
        //   } else if (item.parentId.substr(2, 4) == '0000') {
        //     option.city = item.id
        //   } else if (item.id.substr(2, 4) != '0000' && item.id.substr(4, 2) != '00') {
        //     option.county = item.id
        //   } else {
        //     option.city = item.id
        //   }
        //   skuProperties.regionSkuPropertySearch.region.push(option)
        // })
        // skuProperties.regionSkuPropertySearch.regionSearchType = 2
      }
      skuProperties.industry = !this.localSkuProperty.industry ? ([] as string[]) : [this.localSkuProperty.industry]
      skuProperties.subjectType = !this.localSkuProperty.subjectType
        ? ([] as string[])
        : [this.localSkuProperty.subjectType]
      skuProperties.trainingCategory = !this.localSkuProperty.trainingCategory
        ? ([] as string[])
        : [this.localSkuProperty.trainingCategory]
      skuProperties.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      skuProperties.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      skuProperties.technicalGrade = !this.localSkuProperty.technicalGrade
        ? ([] as string[])
        : [this.localSkuProperty.technicalGrade]
      skuProperties.trainingProfessional = this.getTrainingProfessional()

      // 学科、学段转换
      skuProperties.learningPhase = !this.localSkuProperty.learningPhase
        ? ([] as string[])
        : [this.localSkuProperty.learningPhase]
      skuProperties.discipline = !this.localSkuProperty.discipline
        ? ([] as string[])
        : [this.localSkuProperty.discipline]
      skuProperties.trainingForm = !this.localSkuProperty.trainingForm ? [] : [this.localSkuProperty.trainingForm]
      // skuProperties.certificatesType = !this.localSkuProperty.pharmacistIndustry
      //   ? ([] as string[])
      //   : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
      //   ? [this.localSkuProperty.pharmacistIndustry[0]]
      //   : []
      // skuProperties.practitionerCategory = !this.localSkuProperty.pharmacistIndustry
      //   ? ([] as string[])
      //   : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
      //   ? [this.localSkuProperty.pharmacistIndustry[1]]
      //   : []

      // this.trainSchemeQueryParam.skuPropertyRequest = cloneDeep(skuProperties)
      this.tradeReportRequest.commoditySku.skuProperty = cloneDeep(skuProperties)
      this.tradeReportRequestChildren.commoditySku.skuProperty = cloneDeep(skuProperties)
      // //console.log('selectedSkuProperties', JSON.stringify(this.tradeReportRequest.commoditySku.skuProperty))
    }
    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      ////console.log('envConfig', this.envConfig)
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人设行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }
    /**
     * 获取培训班列表
     */
    async getTrainSchemeList() {
      if (this.sortPolicy.length) {
        return await this.schemeBusinessEntry.queryTrainClassCommodityList(
          this.trainSchemePage,
          this.trainSchemeQueryParam,
          this.sortPolicy
        )
      } else {
        return await this.schemeBusinessEntry.queryTrainClassCommodityList(
          this.trainSchemePage,
          this.trainSchemeQueryParam
        )
      }
    }
    async initialArea() {
      this.tradeReportRequest.buyerAreaPath = []
      const childrenAreaList: Array<NewRegionTreeVo> = []
      const childBuyerAreaPath: Array<string> = []
      // const queryRegisterRegion = (await QueryPhysicalRegion.queryRegion()) as any[]
      const queryRegisterRegion = (await QueryBusinessRegion.getServiceOrIndustry(1)) as any[]
      if (queryRegisterRegion.length > 0) {
        const arr = queryRegisterRegion.map((item) => {
          item.id = item.id.substr(0, 2) + '0000'
          return item.id
        })
        const area = arr.filter(function (item, index) {
          return arr.indexOf(item) === index
        })
        const areaList = QueryBusinessRegion.filterRegionTree(this.areaOptions, area)
        // const topRegionTree = QueryRegisterRegion.topRegionTree
        areaList.map((aitem) => {
          aitem.children.map(
            (item: {
              id: string
              name: string
              parentId: string
              parentCode: string
              regionPath: string
              children: import('@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo').default[]
            }) => {
              if (item.children?.length > 0) {
                item.children.map((subItem) => {
                  const subRegion = new NewRegionTreeVo()
                  subRegion.name = subItem.name
                  subRegion.id = subItem.id
                  subRegion.parentId = subItem.parentId
                  subRegion.leaf = false
                  subRegion.level = 1
                  childrenAreaList.push(subRegion)
                  childBuyerAreaPath.push(subItem.regionPath)
                })
              }
              this.tradeReportRequest.buyerAreaPath.push(item.regionPath)
              const region = new NewRegionTreeVo()
              region.name = item.name
              region.id = item.id
              region.parentId = item.parentId
              region.leaf = true
              region.level = 0
              childrenAreaList.push(region)
              // region.children = item.children
              return region
            }
          )
        })
        // 区县级的传参
        this.tradeReportRequestChildren.buyerAreaPath = childBuyerAreaPath
        this.provinces = []
        this.provinces = this.provinces.concat(childrenAreaList)
      }
    }

    async doSearch() {
      await Promise.all([this.finalSearch(), this.getSummaryInServicer()])
    }
    /**
     * 处理单位所在地区后再进行查询
     */

    async finalSearch() {
      if (this.locationUnit.length > 1) {
        this.tradeReportRequest.buyerAreaPath = []
        this.tradeReportRequest.buyerAreaPath[0] =
          this.locationUnit.length == 2
            ? '/' + this.locationUnit[0] + '/' + this.locationUnit[1]
            : '/' + this.locationUnit[0] + '/' + this.locationUnit[1] + '/' + this.locationUnit[2]
        await this.pageScheme()
        this.dataMerge()
      } else {
        this.tableData2 = []
        // 省级查询
        if (this.locationUnit.length === 1) {
          this.tradeReportRequest.buyerAreaPath = []
          const areaList = await QueryPhysicalRegion.queryLowerLevelRegion(this.locationUnit[0])
          areaList.map((item) => {
            let region1 = ''
            region1 = '/' + this.locationUnit[0] + '/' + item.code
            this.tradeReportRequest.buyerAreaPath.push(region1)
          })
        } else {
          await this.initialArea() //获取初始地区
        }
        await this.pageScheme()
        this.dataMerge()
      }
    }
    async exportListData() {
      try {
        this.getExportQueryParam()
        const res = await this.getQueryRegionReportList.exportExcel(this.exportQueryParam)
        if (res.status.code == 200 && res.data) {
          //   this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        //console.log(e)
      } finally {
        //todo
      }
    }

    @Watch('getQueryRegionReportList.statisticsM', {
      deep: true,
      immediate: true
    })
    statisticsMChange(val: any) {
      if (val) {
        // //console.log(val, 'val值的变化:')
        //
      }
    }
    //懒加载
    async load(tree: NewRegionTreeVo, treeNode: any, resolve: any) {
      // console.log('treeNode', treeNode)
      this.subLoad(tree, treeNode, resolve)
    }
    async subLoad(tree: any, treeNode: any, resolve: (arr: Array<NewRegionTreeVo>) => void) {
      // 兼容省直辖县级行政区划 419000
      if (['469000', '429000'].includes(tree.id)) {
        tree.id = '419000'
      }
      const subRegionList = await QueryPhysicalRegion.queryLowerLevelRegion(tree.id)
      const subRegionRqList = new Array<string>()
      if (['469000', '429000', '419000'].includes(tree.regionId)) {
        subRegionList.map((item: RegionResponse) => {
          console.log(item, 'item')
          if (item.code.substr(0, 3) == tree.regionId.substr(0, 3)) {
            subRegionRqList.push(item.codePath)
          }
        })
      } else {
        subRegionList.map((item: RegionResponse) => {
          subRegionRqList.push(item.codePath)
        })
      }
      if (this.portalId) {
        this.tradeReportRequest.portalId = this.portalId
      }
      if (this.tradeReportRequest.notDistributionPortal) {
        this.tradeReportRequest.portalId = ''
      }
      const subRq = cloneDeep(this.tradeReportRequest) as TradeReportRequest
      subRq.buyerAreaPath = subRegionRqList
      const res = await this.getQueryRegionReportList.listRegionOpenReportFormsInServier(subRq)
      const resolveList = new Array<NewRegionTreeVo>()
      res.map((item) => {
        const regionTreeVo = new NewRegionTreeVo()
        const findSubRegion = subRegionList.find((it) => it.code === item.regionId)
        if (findSubRegion) {
          regionTreeVo.name = findSubRegion?.name
          regionTreeVo.id = findSubRegion?.code
          regionTreeVo.parentId = findSubRegion?.parentCode
          regionTreeVo.leaf = false
          regionTreeVo.level = findSubRegion?.level
          resolveList.push(Object.assign(regionTreeVo, item))
        }
      })

      this.pid = tree.pid
      this.maps.set(tree.pid, { tree, treeNode, resolve })

      // console.log(this.RegionOpenReportFormResponseVoChildren)
      // console.log(this.provinces)
      // console.log(tree, treeNode)
      // debugger

      // const arr = [] as NewRegionTreeVo[]
      // this.tableData10.map(item => {
      //   // console.log(item, tree)
      //   // console.log(item.parentId == tree.id)
      //   if (item.parentId == tree.id) {
      //     //id相同时数据合并
      //     // const obj = Object.assign(item)
      //     arr.push(item)
      //   }
      // })
      // console.log(arr, 'arr')

      // console.log(arr)
      // resolve(arr)
      resolve(resolveList)
    }
    /**
     *  合计数据
     *  垃圾合计数据  不看也罢！！！ - -
     */
    getSummaries(param: any) {
      // debugger
      // console.log(this.getQueryRegionReportList, 'statisticsM')
      const { columns, data } = param
      //console.log(columns, 'columnscolumnscolumnscolumns')

      const sums = [] as any[]
      columns.forEach((column: any, index: number) => {
        // 可以根据对象属性名判断是否合计
        // if (column.property === "amount3") {
        // 根据索引修改‘合计’文案用了getCommodityReportSummaryInServicer方法，返回值=statisticsM
        if (index === 0) {
          sums[index] = '合计'
          return
          // 可以根据索引判断是否合计
        }
        switch (column.property) {
          case 'summaryInfo.tradeSuccessCount':
            sums[index] = this.getQueryRegionReportList.statisticsM.tradeCountSummaryInfo.tradeSuccessCount
            break
          case 'summaryInfo.returnCount':
            sums[index] = this.getQueryRegionReportList.statisticsM.tradeCountSummaryInfo.returnCount
            break
          case 'summaryInfo.exchangeInCount':
            sums[index] = this.getQueryRegionReportList.statisticsM.tradeCountSummaryInfo.exchangeInCount
            break
          case 'summaryInfo.exchangeOutCount':
            sums[index] = this.getQueryRegionReportList.statisticsM.tradeCountSummaryInfo.exchangeOutCount
            break
          case 'summaryInfo.netTradeSuccessCount':
            sums[index] = this.getQueryRegionReportList.statisticsM.tradeCountSummaryInfo.netTradeSuccessCount
            break
          case 'online.tradeSuccessCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.tradeSuccessCount
            }

            break

          case 'online.returnCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.returnCount
            }

            break

          case 'online.exchangeInCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.exchangeInCount
            }

            break

          case 'online.exchangeOutCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.exchangeOutCount
            }

            break

          case 'online.netTradeSuccessCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.netTradeSuccessCount
            }

            break
          case 'team.online.tradeSuccessCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1]?.statisticInfo.tradeSuccessCount
            }

            break

          case 'team.online.returnCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1]?.statisticInfo.returnCount
            }

            break

          case 'team.online.exchangeInCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1]?.statisticInfo.exchangeInCount
            }

            break

          case 'team.online.exchangeOutCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1]?.statisticInfo.exchangeOutCount
            }

            break

          case 'team.online.netTradeSuccessCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1]?.statisticInfo.netTradeSuccessCount
            }

            break
          case 'team.offline.tradeSuccessCount':
            if (
              this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList &&
              this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0
            ) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0]?.statisticInfo.tradeSuccessCount
            }

            break

          case 'team.offline.returnCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0]?.statisticInfo.returnCount
            }

            break

          case 'team.offline.exchangeInCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0]?.statisticInfo.exchangeInCount
            }

            break

          case 'team.offline.exchangeOutCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0]?.statisticInfo.exchangeOutCount
            }

            break

          case 'team.offline.netTradeSuccessCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0]?.statisticInfo.netTradeSuccessCount
            }

            break
          case 'export.tradeSuccessCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.tradeSuccessCount
            }

            break

          case 'export.returnCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.returnCount
            }

            break

          case 'export.exchangeInCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.exchangeInCount
            }

            break

          case 'export.exchangeOutCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.exchangeOutCount
            }

            break

          case 'export.netTradeSuccessCount':
            if (this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.getQueryRegionReportList.statisticsM.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.netTradeSuccessCount
            }

            break
        }
      })
      // //console.log(sums, 'sumssumssumssumssumssums')
      return sums
    }
  }
</script>
