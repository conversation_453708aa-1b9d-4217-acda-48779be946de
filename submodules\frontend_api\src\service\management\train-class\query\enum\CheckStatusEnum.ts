import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum CheckStatusEnum {
  submiting = 'SUBMITING',
  submitted = 'SUBMITTED',
  pass = 'PASS',
  returned = 'RETURNED'
}

class CheckStatusEnumClass extends AbstractEnum<CheckStatusEnum> {
  constructor(status?: CheckStatusEnum) {
    super()
    this.current = status
    this.map.set(CheckStatusEnum.submiting, '待提交')
    this.map.set(CheckStatusEnum.submitted, '已提交')
    this.map.set(CheckStatusEnum.pass, '通过')
    this.map.set(CheckStatusEnum.returned, '退回')
  }

  /**
   * 是否审核
   */
  get isChecked() {
    return [CheckStatusEnum.pass, CheckStatusEnum.returned].includes(this.current) ? '已审核' : '未审核'
  }

  /**
   * 审核结果
   */
  get checkResult() {
    if (this.equal(CheckStatusEnum.returned)) {
      return '不通过'
    } else if (this.equal(CheckStatusEnum.pass)) {
      return '通过'
    } else {
      return ''
    }
  }
}

export default CheckStatusEnumClass
