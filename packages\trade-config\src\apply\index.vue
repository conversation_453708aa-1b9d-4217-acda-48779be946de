<route-meta>
{
"isMenu": true,
"title": "报名方式配置",
"sort": 2,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="requestDetail,@StudentPay,@UnitPay,@ImportOpen">
    <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
      <p>配置相关说明：</p>
      <p>
        1. 当前系统提供个人缴费、集体报名及教务导入三个报名方式，请针对实际渠道的运用情况配置对应的收款账户及发票信息；
      </p>
      <p>2. 个人缴费渠道、单位缴费渠道可允许添加多个收款账号，导入开通渠道默认收款账号帐号无法修改。</p>
    </el-alert>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky" @tab-click="handleClick">
      <el-tab-pane label="学员缴费" name="student-pay" v-if="isStudentPay">
        <student-pay
          ref="studentPayRef"
          :purchaseChannelList="purchaseChannelList"
          :studentPayDetail="studentPayDetail"
        ></student-pay>
      </el-tab-pane>
      <el-tab-pane label="单位缴费" name="unit-pay" v-if="isUnitPay">
        <unit-pay ref="unitPayRef" :purchaseChannelList="purchaseChannelList" :unitPayDetail="unitPayDetail"></unit-pay>
      </el-tab-pane>
      <el-tab-pane label="导入开通" name="import-open" v-if="isImportOpen">
        <import-open
          ref="importOpenRef"
          :purchaseChannelList="purchaseChannelList"
          :importOpenDetail="importOpenDetail"
        ></import-open>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import StudentPay from '@hbfe/jxjy-admin-tradeConfig/src/apply/components/student-pay.vue'
  import UnitPay from '@hbfe/jxjy-admin-tradeConfig/src/apply/components/unit-pay.vue'
  import ImportOpen from '@hbfe/jxjy-admin-tradeConfig/src/apply/components/import-open.vue'
  import TradeInfoConfigModule from '@api/service/management/trade-info-config/TradeInfoConfigModule'
  import PurchaseChannelTypeVo from '@api/service/management/trade-info-config/mutation/vo/PurchaseChannelTypeVo'
  @Component({
    components: { StudentPay, UnitPay, ImportOpen }
  })
  export default class extends Vue {
    @Ref('studentPayRef') studentPayRef: StudentPay
    @Ref('unitPayRef') unitPayRef: UnitPay
    @Ref('importOpenRef') importOpenRef: ImportOpen
    activeName = 'student-pay'
    getPreparePurchaseChannel = TradeInfoConfigModule.mutationTradeInfoConfigFactory.getPreparePurchaseChannel()
    purchaseChannelList = new Array<PurchaseChannelTypeVo>()
    isStudentPay = false
    studentPayDetail = new PurchaseChannelTypeVo()
    isUnitPay = false
    unitPayDetail = new PurchaseChannelTypeVo()
    isImportOpen = false
    importOpenDetail = new PurchaseChannelTypeVo()
    async activated() {
      await this.requestDetail()
    }
    async created() {
      await this.requestDetail()
    }

    async requestDetail() {
      await this.getPreparePurchaseChannel.preparePurchaseChannel()
      this.purchaseChannelList = this.getPreparePurchaseChannel.purchaseChannelList
      console.log(this.purchaseChannelList, '111')
      for (let i = 0; i < this.purchaseChannelList.length; i++) {
        if (this.purchaseChannelList[i].type === 1) {
          this.isStudentPay = true
          this.studentPayDetail = this.purchaseChannelList[i]
        }
        if (this.purchaseChannelList[i].type === 2) {
          this.isUnitPay = true
          this.unitPayDetail = this.purchaseChannelList[i]
        }
        if (this.purchaseChannelList[i].type === 3) {
          this.isImportOpen = true
          this.importOpenDetail = this.purchaseChannelList[i]
        }
      }
    }

    async handleClick() {
      console.log(this.activeName)
      if (this.activeName == 'student-pay') {
        await this.studentPayRef.requestDetail()
      }
      if (this.activeName == 'unit-pay') {
        await this.unitPayRef.requestDetail()
      }
      if (this.activeName == 'import-open') {
        await this.importOpenRef.requestDetail()
      }
    }
  }
</script>
