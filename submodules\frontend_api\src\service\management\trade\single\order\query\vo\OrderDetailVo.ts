import {
  CommodityAuthInfoResponse,
  InvoiceApplyInfoResponse,
  OrderResponse,
  SchemeResourceResponse,
  SubOrderResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import { ReturnOrderStatusEnum } from '@api/service/management/trade/single/order/enum/returnOrderStatusEnum'
import { OrderTerminal } from '@api/service/management/trade/single/order/query/enum/OrderTerminalType'
import { OrderTransaction } from '@api/service/management/trade/single/order/query/enum/OrderTransactionStatus'
import BuyerInfoVo from '@api/service/management/trade/single/order/query/vo/BuyerInfoVo'
import OrderInvoiceApplyInfoResponseVo from '@api/service/management/trade/single/order/query/vo/OrderInvoiceApplyInfoResponseVo'
import { ForceReasonEnum } from '@api/service/management/trade/single/order/enum/ForceReasonEnum'
import MsLearningQueryFrontGatewayCourseLearningForestage, {
  LearningRegisterRequest,
  StudentSchemeLearningRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import TrainClassModule from '@api/service/management/train-class/TrainClassManagerModule'
import CommodityRefundStatus, {
  CommodityRefundStatusEnum
} from '@api/service/common/return-order/enums/CommodityRefundStatus'
import { ChangeOrderType } from '@api/service/common/trade/ChangeOrderType'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
/**
 * @description
 */
class OrderDetailVo {
  /**
   * 订单号
   */
  orderNo = ''

  /**
   * 商品id
   */
  commodityId: string[] = []
  /**
   * 换货状态
   */
  changeOrderStatus: ChangeOrderType[] = []
  /**
   * 培训期别名称
   */
  trainingPeriodName = ''
  /**
   * 培训形式
   */
  trainingForm = ''
  /**
   * 培训形式ID
   */
  trainingFormId: TrainingModeEnum = undefined

  /**
   * 单位id
   */
  unitId = ''

  /**
   * 商品名称
   */
  commodityName: string[] = []

  /**
   * 是否换班
   */
  isExchange = false

  /**
   * 订单类型、1:个人报名 2:集体报名 3：导入开通
   */
  channelType = 0

  /**
   * 收款账号类型 0:线上收款账号 1:线下收款账号
   */
  receiveAccountType: number

  /**
   * 终端（销售渠道）、Web:Web端 H5:H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
   */
  terminalCode: OrderTerminal

  /**
   * 支付渠道名称、支付宝：支付宝
   */
  payChannelName = ''

  /**
   * 购买人信息
   */
  buyerInfo: BuyerInfoVo = new BuyerInfoVo()

  /**
   * 购买学时
   */
  period = 0

  /**
   * 支付金额
   */
  payAmount = 0

  /**
   * 订单创建时间
   */
  createTime = ''

  /**
   * 交易完成时间
   */
  transactionCompletionTime = ''

  /**
   * 交易状态 1：等待付款 2：支付中 3：开通中 4：交易成功 5：交易关闭
   */
  orderStatus: OrderTransaction

  /**
   * 允许补要发票
   */
  enableReApplyInvoice = false

  /**
   * 交易流水号
   */
  flowNo = ''
  /**
   * 发票申请信息
   */
  invoiceApplyInfo: InvoiceApplyInfoResponse
  // * 退款状态
  returnOrderStatus: ReturnOrderStatusEnum = null

  /**
   * 批次单信息
   */
  batchOrderNo: string = undefined

  /**
   * 销售渠道
   */
  saleChannel: SaleChannelEnum = null

  /**
   * 子订单号
   */
  subOrderNo: string

  /**
   * 商品分销授权信息（仅分销订单有值）
   */
  commodityAuthInfo = new CommodityAuthInfoResponse()

  /**
   * 方案id
   */
  schemeId: string

  /**
   * 参训资格id
   */
  qualificationId: string

  /**
   * 方案开始时间
   */
  trainBeginTime = ''
  /**
   * 子订单信息
   */
  subOrderItems: SubOrderResponse[] = []

  //是否属于强制退款的类型
  isForce = false
  foreseReasonArr: ForceReasonEnum[] = []

  static from(orderResponse: OrderResponse): OrderDetailVo {
    const detail = new OrderDetailVo()
    // detail.isSpecialOrder = orderResponse.basicData?.isSpecialOrder
    detail.orderNo = orderResponse.orderNo
    detail.subOrderNo = orderResponse.subOrderItems[0].subOrderNo
    detail.batchOrderNo = orderResponse.basicData?.batchOrderNo
    detail.saleChannel = orderResponse.saleChannel
    detail.commodityId =
      orderResponse.subOrderItems?.map(item => {
        return item.deliveryCommoditySku?.commoditySkuId
      }) || ([] as string[])
    detail.commodityName =
      orderResponse.subOrderItems?.map(item => {
        return item.deliveryCommoditySku?.saleTitle
      }) || ([] as string[])
    detail.invoiceApplyInfo = orderResponse.invoiceApplyInfo
    detail.commodityAuthInfo = orderResponse.subOrderItems[0]?.commodityAuthInfo
    detail.isExchange = OrderDetailVo.validateIsExchange(orderResponse.subOrderItems)
    detail.subOrderItems = orderResponse.subOrderItems
    detail.channelType = orderResponse.basicData?.channelType
    detail.receiveAccountType = orderResponse.payInfo?.receiveAccount?.receiveAccountType
    detail.terminalCode = OrderDetailVo.getOrderTerminalEnum(orderResponse.basicData?.terminalCode)
    detail.payChannelName = orderResponse.payInfo?.receiveAccount?.payChannelName
    detail.buyerInfo.userId = orderResponse.buyer?.userId || ''
    detail.period = OrderDetailVo.getPeriod(orderResponse.subOrderItems)
    detail.payAmount = orderResponse.basicData?.amount
    detail.createTime = orderResponse.basicData?.orderStatusChangeTime?.normal
    detail.transactionCompletionTime = orderResponse.basicData?.orderStatusChangeTime?.completed
    detail.orderStatus = OrderDetailVo.getOrderStatus(orderResponse)
    detail.enableReApplyInvoice = OrderDetailVo.validateEnableReApplyInvoice(orderResponse)
    detail.flowNo = orderResponse.payInfo?.flowNo || ''

    detail.unitId = orderResponse?.basicData?.unitId
    detail.trainingPeriodName = orderResponse?.subOrderItems[0]?.deliveryCommoditySku?.issueInfo?.issueName
    detail.trainingForm =
      orderResponse?.subOrderItems[0]?.deliveryCommoditySku?.skuProperty?.trainingWay?.skuPropertyValueName
    const trainingWayId =
      orderResponse?.subOrderItems[0]?.deliveryCommoditySku?.skuProperty?.trainingWay?.skuPropertyValueId
    detail.trainingFormId = trainingWayId as TrainingModeEnum
    // 处理换班、换期标签
    const { subOrderItems } = orderResponse
    if (subOrderItems && subOrderItems.length) {
      const subOrderItem = subOrderItems[0]
      const isExistExchangeScheme = subOrderItem.exchangeStatus !== 0
      const isExistExchangeIssue = subOrderItem.isExchangeIssue
      if (isExistExchangeScheme) {
        detail.changeOrderStatus.push(ChangeOrderType.CLASS_TYPE)
      }
      if (isExistExchangeIssue) {
        detail.changeOrderStatus.push(ChangeOrderType.PERIOD_TYPE)
      }
    }
    return detail
  }
  /**
   * 子订单退货状态（目前只有单一商品，直接上浮即可）
   */
  get subOrderRefundStatus() {
    return (subOrderItem: SubOrderResponse) => {
      return CommodityRefundStatus.transferSubOrderStatusToCurrent(
        subOrderItem.returnStatus,
        subOrderItem.refundSchedule,
        subOrderItem.returnSchedule
      )
    }
  }
  /**
   * 校验是否换班
   * @param {SubOrderResponse[]} subOrderItems - 子订单列表
   */
  private static validateIsExchange(subOrderItems: SubOrderResponse[]): boolean {
    let result = false
    subOrderItems?.forEach((item: SubOrderResponse) => {
      if (item.exchangeStatus !== 0) {
        result = true
      }
    })
    return result
  }

  /**
   * 获取订单交易状态
   * @param {OrderResponse} orderResponse - 订单详情
   */
  static getOrderStatus(orderResponse: OrderResponse): OrderTransaction {
    let result = null
    if (orderResponse.basicData?.orderStatus === 1 && orderResponse.basicData?.orderPaymentStatus === 0) {
      // 等待付款：订单状态正常&支付状态未支付
      result = OrderTransaction.Wait_Pay
    }
    if (orderResponse.basicData?.orderStatus === 1 && orderResponse.basicData?.orderPaymentStatus === 1) {
      // 支付中：订单状态正常&支付状态支付中
      result = OrderTransaction.Paying
    }
    if (orderResponse.basicData?.orderStatus === 1 && orderResponse.basicData?.orderPaymentStatus === 2) {
      // 开通中：订单状态正常&支付状态已支付
      result = OrderTransaction.Opening
    }
    if (orderResponse.basicData?.orderStatus === 2) {
      // 交易成功：订单状态交易成功
      result = OrderTransaction.Complete_Transaction
    }
    if (orderResponse.basicData?.orderStatus === 3) {
      // 交易关闭：订单状态交易关闭
      result = OrderTransaction.Close_Transaction
    }

    return result
  }

  /**
   * 获取学时
   * @param {SubOrderResponse[]} subOrderItems - 子订单列表
   */
  static getPeriod(subOrderItems: SubOrderResponse[]): number {
    const total =
      subOrderItems?.reduce((prev, cur) => {
        return CalculatorObj.add((cur?.deliveryCommoditySku?.resource as SchemeResourceResponse)?.period || 0, prev)
      }, 0) || 0
    return total
  }

  /**
   * 获取终端类型枚举值
   */
  public static getOrderTerminalEnum(str: string): OrderTerminal | null {
    switch (str) {
      case 'Web':
        return OrderTerminal.Web
      case 'IOS':
        return OrderTerminal.IOS
      case 'Android':
        return OrderTerminal.Android
      case 'WechatMini':
        return OrderTerminal.WechatMini
      case 'WechatOfficial':
        return OrderTerminal.WechatOfficial
      case 'ExternalSystemManage':
        return OrderTerminal.ExternalSystemManage
      case 'H5':
        return OrderTerminal.H5
    }
    return null
  }

  /**
   * 【内部校验】是否允许补要发票
   */
  static validateEnableReApplyInvoice(orderResponse: OrderResponse): boolean {
    /** 满足条件：个人报名 && 未开票 && 允许退款 */
    // 是否是个人报名
    const isSingleOrder = orderResponse.basicData?.channelType === 1 ? true : false

    // 是否未开票
    const withoutInvoice = orderResponse?.isInvoiceApplied === false ? true : false

    // 是否允许退款
    let enableRefund = true
    orderResponse.subOrderItems?.forEach((el: SubOrderResponse) => {
      if (el.returnStatus === 4) {
        enableRefund = false
      }
    })
    return isSingleOrder && withoutInvoice && enableRefund
  }

  /*
   *    获取参训资格id，仅在已报名状态才能获取
   * */
  async getQualificationId() {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    const firstOrder = this.subOrderItems[0]
    request.learningRegister.sourceType = firstOrder.currentCommoditySourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
    request.learningRegister.sourceId = firstOrder.currentCommoditySourceId

    const res = await MsLearningQueryFrontGatewayCourseLearningForestage.pageStudentSchemeLearningInServicer({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data?.currentPageData && res.data.currentPageData.length) {
      return res.data.currentPageData[0].qualificationId
    }
    return ''
  }

  //获取是否为强制退款
  async getForceStatue() {
    if (this.invoiceApplyInfo && (this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceStatus == 2) {
      this.isForce = true
      this.foreseReasonArr.push(ForceReasonEnum.ForceReasonEnumInvoiced)
    }
    const quaId = await this.getQualificationId()
    if (quaId) {
      const queryUserTrainClassDetail = TrainClassModule.queryTrainClassFactory.getQueryUserTrainClassDetailClass()
      queryUserTrainClassDetail.qualificationId = quaId
      const trainclassRes = await queryUserTrainClassDetail.queryTrainClassDetail()
      if (trainclassRes.isSuccess()) {
        if (
          queryUserTrainClassDetail.trainClassDetail.userGetLearning.courseQualified ||
          queryUserTrainClassDetail.trainClassDetail.userGetLearning.trainingResult == 1
        ) {
          this.isForce = true
          if (queryUserTrainClassDetail.trainClassDetail.userGetLearning.trainingResult == 1) {
            this.foreseReasonArr.push(ForceReasonEnum.ForceReasonEnumAssesed)
          }
          if (queryUserTrainClassDetail.trainClassDetail.userGetLearning.courseQualified) {
            this.foreseReasonArr.push(ForceReasonEnum.ForceReasonEnumCourseQuafield)
          }
        }
      }
    }
  }
}

export default OrderDetailVo
