<template>
  <div>
    <el-upload
      action="#"
      list-type="picture-card"
      class="m-pic-upload proportion-pic is-small"
      :class="{ showUpload: isShowUpload }"
      :on-change="uploadImgChange"
      :auto-upload="false"
      :file-list="imgList"
      :limit="limit"
      :accept="imgType"
      :multiple="isMultiple"
    >
      <div slot="default" class="upload-placeholder">
        <i class="el-icon-plus"></i>
        <p class="txt">上传图片</p>
      </div>
      <div slot="file" slot-scope="{ file }" class="img-file">
        <el-image
          class="el-upload-list__item-thumbnail"
          :src="file.url"
          alt=""
          fit="fill"
          v-loading="loading[file.uid]"
        />
        <div class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <i class="el-icon-zoom-in"></i>
          </span>
          <span class="el-upload-list__item-delete" @click="handleRemove(file)">
            <i class="el-icon-delete"></i>
          </span>
        </div>
      </div>
      <div slot="tip" class="el-upload__tip">
        <i class="el-icon-warning"></i>
        <span class="txt">{{ imgTypeTip }}</span>
      </div>
    </el-upload>
    <!-- <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
      <img :src="dialogImageUrl" alt="" />
    </el-dialog> -->
    <!-- 大图预览 -->
    <el-image style="width: 100px; height: 100px" :previewSrcList="previewList" v-show="false" ref="elImage">
    </el-image>
  </div>
</template>
<script lang="ts">
  import { Component, Ref, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import axios from 'axios'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import { ElImage } from 'element-ui/types/image'
  import { cloneDeep } from 'lodash'
  export class CertificationUrl {
    name: string
    url: string
  }

  @Component
  export default class extends Vue {
    // 大图预览ref
    @Ref('elImage')
    elImage: ElImage

    // 资源地址
    resourceUrl = ''
    // 加载状态
    loading = {}

    // 最大上传数量
    @Prop({
      type: Number,
      default: 10
    })
    limit: number

    // 图片后缀
    @Prop({
      type: String,
      default: '.jpg, .png, .jpeg'
    })
    imgType: string

    // 图片后缀的文字提示
    @Prop({
      type: String,
      default: '请上传*.jpg，*.jpeg，*.png格式的图片'
    })
    imgTypeTip: string

    // 默认支持多选
    @Prop({
      type: Boolean,
      default: true
    })
    isMultiple: boolean

    // 传给后端的图片路径数组
    imgList = new Array<CertificationUrl>()
    // 图片预览数组
    previewList = new Array<string>()

    // 用于接收回显的图片数组
    @Prop({
      type: Array,
      default: () => {
        return new Array<CertificationUrl>()
      }
    })
    value: Array<CertificationUrl>

    // 预览图片路径
    dialogImageUrl = ''

    // 是否展示上传按钮
    get isShowUpload(): boolean {
      if (this.imgList?.length >= this.limit) {
        return true
      }
      return false
    }

    @Watch('value', {
      immediate: true,
      deep: true
    })
    valueChange(val: Array<CertificationUrl>) {
      this.imgList = val
    }

    @Watch('imgList', {
      deep: true
    })
    imgListChange(val: Array<CertificationUrl>) {
      this.$emit('input', val)
    }

    created() {
      this.resourceUrl = ConfigCenterModule.getIngress('ingress.resource')
    }

    // 上传图片
    uploadImgChange(file: any, fileList: any) {
      this.$set(this.loading, file?.uid, true)
      const imgReg = /\.jpg|\.png|\.jpeg$/
      if (imgReg.test(file?.name)) {
        // 校验通过之后，转为base图片
        const reader = new FileReader()
        reader.readAsDataURL(file.raw || file)
        reader.onloadend = () => {
          // 图片的 base64 格式, 可以直接当成 img 的 src 属性值
          const dataURL = reader.result as string
          this.getResourceUrl(dataURL, fileList, file?.name, file?.uid)
        }
      } else {
        this.$message.warning('请上传指定后缀的图片！')
        return
      }
    }

    // 保存符合格式的图片
    getResourceUrl(imgCode: string, fileList: any, fileName: string, uid: number) {
      const data = {
        base64Data: imgCode.split(',')[1].toString(),
        fileName
      }
      // 转为图片
      // const baseUrl = `${this.resourceUrl}/auth/uploadBase64ToProtectedFile`
      const baseUrl = `${ConfigCenterModule.getIngress(ingress.apiendpoint)}/web/ms-file-v1/web/uploadPublicBase64`
      try {
        axios.post(baseUrl, data).then(data => {
          const imgUrl = data.data.data
          console.log('上传链接：', imgUrl)

          fileList[fileList.length - 1].name = imgUrl
          fileList[fileList.length - 1].url = '/mfs' + imgUrl
          this.imgList = fileList
          // console.log('后端用', this.imgList)
          this.previewList.push('/mfs' + imgUrl)
          // console.log('本地预览', this.previewList)
          this.$emit('input', this.imgList)
          this.$set(this.loading, uid, false)
        })
      } catch (error) {
        this.$message('图片转换失败')
      }
    }

    // 预览图片
    handlePictureCardPreview(file: any) {
      this.setPhotoTop(this.previewList, file)
      this.$nextTick(() => {
        // 触发点击方法
        ;(this.elImage as any).clickHandler()
      })
    }

    // 置顶预览图片数组中的一张图片
    setPhotoTop(arr: Array<string>, file: any) {
      const curUrl = arr.find(el => el === file.url)
      const curIndex = arr.findIndex(el => el === file.url)
      if (!curUrl && curIndex === -1) {
        this.$message({
          message: '上传中...',
          type: 'warning'
        })
        return
      }
      arr.splice(curIndex, 1)
      arr.unshift(curUrl)
    }

    /*
      删除图片
      删除的时候，预览图片数组与传给后端数组图片数组，需要分开删除
    */
    handleRemove(file: any) {
      const idx: number = this.imgList.findIndex(el => el.url === file.url)
      this.imgList.splice(idx, 1)

      const preIdx: number = this.previewList.findIndex(el => el === file.url)
      this.previewList.splice(preIdx, 1)
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep.showUpload > .el-upload {
    display: none;
  }
</style>
