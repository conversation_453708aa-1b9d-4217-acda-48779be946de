import basicdata, { UpdateAdministratorAccountRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import UserModule from '@api/service/management/user/UserModule'

/**
 * 修改姓名
 */
class MutationResetNameAdmin {
  async doResetAdminInfo(name: string) {
    const request = new UpdateAdministratorAccountRequest()
    request.accountId = UserModule.queryUserFactory.queryManagerDetail.adminInfo.accountInfo.accountId
    request.name = name
    request.updateAll = false
    const res = await basicdata.updateAdministratorAccount(request)
    return res.status
  }
}

export default MutationResetNameAdmin
