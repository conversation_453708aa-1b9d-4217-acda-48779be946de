<template>
  <el-card shadow="never" class="m-card f-mb15">
    <el-alert type="warning" :closable="false" class="m-alert">
      系统当前已配置 <span class="f-fb">{{ page.totalSize || 0 }}</span> 个培训证明模板
    </el-alert>
    <!--表格-->
    <el-table stripe :data="certTemplateList" max-height="500px" class="m-table f-mt10" v-loading="query.loading">
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="模板名称" min-width="200" prop="name" fixed="left">
        <template slot-scope="scope">{{ scope.row.name || '-' }}</template>
      </el-table-column>
      <el-table-column label="模板说明" min-width="200" prop="remark">
        <template slot-scope="scope">{{ scope.row.describe || '-' }}</template>
      </el-table-column>
      <el-table-column label="所属行业" min-width="100" prop="industry">
        <template slot-scope="scope">{{ scope.row.belongsIndustryName || '-' }}</template>
      </el-table-column>
      <el-table-column label="使用范围" min-width="120" prop="useRange">
        <template slot-scope="scope">{{
          rangeMapName[scope.row.usableRange] || scope.row.usableRange || '-'
        }}</template>
      </el-table-column>
      <el-table-column label="适用培训方案形式" min-width="150" prop="type">
        <template slot-scope="scope">{{ classMapName[scope.row.suitableSchemeType] || '-' }}</template>
      </el-table-column>
      <el-table-column label="是否应用电子章配置" min-width="150" align="center" prop="seal">
        <template slot-scope="scope">{{ scope.row.provideElectronicSeal ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column label="创建时间" min-width="170" prop="createTime">
        <template slot-scope="scope">{{ scope.row.createdTime || '-' }}</template>
      </el-table-column>
      <el-table-column label="操作" width="150" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="preview(scope.row)" :loading="previewing">预览</el-button>
          <el-button type="text" size="mini" @click="download(scope.row)" :loading="downloading">下载</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="page" v-bind="page"></hb-pagination>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import TrainingCertificateModule from '@api/service/management/personal-leaning/TrainingCertificateModule'
  import CertificateTemplateResponseVo from '@api/service/management/personal-leaning/query/vo/CertificateTemplateResponseVo'
  import { UsageRangeEnum } from '@api/service/common/enums/personal-leaning/UsageRange'
  import { TrainingSchemeFormEnum } from '@api/service/common/enums/personal-leaning/TrainingSchemeForm'
  import MutationBatchPrintTraining from '@api/service/management/personal-leaning/mutation/MutationBatchPrintTraining'
  import { PrintCertificateTemplateRequest } from '@api/ms-gateway/ms-certificate-v1'
  @Component
  export default class extends Vue {
    page: UiPage
    query: Query = new Query()

    certTemplateList = new Array<CertificateTemplateResponseVo>()

    mutationBatchPrintTraining: MutationBatchPrintTraining =
      TrainingCertificateModule.mutationBatchPrintTrainingFactory.batchPrintTraining

    //防抖
    downloading = false
    previewing = false

    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    async created() {
      await this.doSearch()
    }

    rangeMapName = {
      [UsageRangeEnum.ALL_SERVICER]: '全网校'
    }

    classMapName = {
      [TrainingSchemeFormEnum.TRAINING_CLASS]: '培训班学习'
    }

    async doSearch() {
      this.query.loading = true
      try {
        const res = await TrainingCertificateModule.queryTrainingCertificateFactory.certificateTemplate.queryCertificateTemplateList(
          this.page
        )
        if (res.status.isSuccess()) {
          this.certTemplateList = res.data
        }
        //搜索请求
      } catch (e) {
        //报错处理
        console.log(e)
      } finally {
        this.query.loading = false
      }
    }

    //预览
    async preview(item: CertificateTemplateResponseVo) {
      this.previewing = true
      window.open('/mfs/Cooper/templates/' + item.previewUrl, '_blank')
      this.previewing = false
      /*if (resolver) {
        window.open(resolver.location.name, '_blank')
      }*/
    }

    //下载
    async download(item: CertificateTemplateResponseVo) {
      const params = new PrintCertificateTemplateRequest()
      params.certificateTemplateId = item.id
      params.printFileType = 1
      let url
      try {
        url = await this.mutationBatchPrintTraining.doDownloadCertificateTemplate(params)
      } catch (e) {
        console.log(e)
        this.$message.error('请求cooper失败')
      }
      console.log(url)
      this.downloading = true
      //下载文件
      const link = document.createElement('a')
      const resolver = this.$router.resolve({
        name: url
      })

      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      const urlArr = link.href.split('.'),
        typeName = urlArr.pop()
      link.setAttribute('download', item.name)
      document.body.appendChild(link)
      link.click()
      link.remove()
      this.downloading = false
    }
  }
</script>
