import Account from '@api/ms-gateway/ms-account-v1'

class RegisterBasic {
  /**
   * 加载学员账号注册所需基础验证数据
   */
  async loadStudentRegisterBasicValidationData() {
    const response = await Account.loadBasicValidationData()
    return {
      captcha: response.data.captcha,
      token: response.data.token,
      status: response.status
    }
  }

  /**
   * 加载集体账号注册所需基础验证数据
   */
  async loadCollectiveBasicValidationData() {
    const response = await Account.loadBasicValidationData()
    return {
      captcha: response.data.captcha,
      token: response.data.token,
      status: response.status
    }
  }
}

export default RegisterBasic
