/**
 * @description 课程大纲树模型
 */
class CourseTrainingOutlineVo {
  /**
   * 唯一标识
   */
  id: string

  /**
   * 节点名称
   */
  name: string

  /**
   * 父节点唯一标识（顶级节点值为-1）
   */
  parentId: string

  /**
   * 同级排序序号
   */
  sort: number

  /**
   * 大纲类型（1-必修 2-选修）
   */
  category: number

  /**
   * 是否为叶子节点
   */
  isLeaf: boolean
  /**
   * 子节点内容
   */
  children: Array<CourseTrainingOutlineVo>

  constructor(id: string, name = '全部') {
    this.id = id
    this.name = name
  }
}

export default CourseTrainingOutlineVo
