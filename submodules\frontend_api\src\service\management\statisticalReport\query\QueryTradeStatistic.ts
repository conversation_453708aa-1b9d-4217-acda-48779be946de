import msTrade, {
  TradeStatisticDateHistogramBucketResponse,
  TradeStatisticDateHistogramRequest,
  TradeStatisticDateHistogramResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
export class QueryTradeStatistic {
  async getTradeStatisticDateHistogramInServicer(request: TradeStatisticDateHistogramRequest) {
    const res = await msTrade.getTradeStatisticDateHistogramInServicer(request)
    let tmpArr: TradeStatisticDateHistogramBucketResponse[] = []
    if (res.status.isSuccess()) {
      tmpArr = res.data.histogramData
    }
    return tmpArr
  }
}
