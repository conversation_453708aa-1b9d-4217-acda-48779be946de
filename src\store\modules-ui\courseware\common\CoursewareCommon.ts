import CoursewareTransformStatus from '@api/service/management/resource/courseware/enum/CoursewareTransformStatus'

class CoursewareCommon {
  static statusValues = [
    {
      name: '全部',
      value: null
    },
    {
      name: '正常',
      value: 1
    },
    {
      name: '停用',
      value: 0
    }
  ]

  static changeStatusValues = [
    {
      name: '全部',
      value: null
    },
    {
      name: '转换中',
      value: CoursewareTransformStatus.enum.TRANSCODING
    },
    {
      name: '转换成功',
      value: CoursewareTransformStatus.enum.AVAILABLE
    },
    {
      name: '转换失败',
      value: CoursewareTransformStatus.enum.UNAVAILABLE
    }
  ]

  static filterTitle(type: string) {
    switch (type) {
      case 'create':
        return '新建分类'
      case 'modify':
        return '编辑分类'
      case 'detail':
        return '分类详情'
    }
  }
}

export default CoursewareCommon
