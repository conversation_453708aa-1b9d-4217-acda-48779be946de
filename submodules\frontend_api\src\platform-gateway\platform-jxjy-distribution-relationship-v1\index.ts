import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjy-distribution-relationship-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjy-distribution-relationship-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 供应商创建分销关系
<AUTHOR>
@since 2023/12/19  17:43
 */
export class CreateSupplyDistributionRelationshipRequest {
  /**
   * 【必填】分销商id
当无上级分销商时 传一级分销商ID
当有上级分销商是 传二级分销商ID（暂无二级分销商）
   */
  distributorId?: string
  /**
   * 【必填】分销服务商ID(分配的网校ID)集合
   */
  distributorServiceIds?: Array<string>
  /**
   * 【必填】分销地区集台(区级Code)
   */
  distributionRegionList?: Array<string>
  /**
   * 【必填】期限类型
1:周期  2：长期
   */
  contractDurationType: number
  /**
   * 【必填】是否立即开始
   */
  immediatelyStart: boolean
  /**
   * 【非立即开始时必填】合同开始时间
   */
  contractStartTime?: string
  /**
   * 【周期必填】合同结束时间
   */
  contractEndTime?: string
  /**
   * 【非必填项】业务员ID
   */
  salespersonId?: string
}

/**
 * 供销合同信息变更请求
<AUTHOR>
@since 2023/12/4  15:28
 */
export class SupplyDistributionContractInfoChangedRequest {
  /**
   * 【必填】产品供销合同id
   */
  productSupplyDistributionContractId?: string
  /**
   * 【必填】供销地区集台(区级Code)
   */
  supplyDistributionRegionList?: Array<string>
  /**
   * 【必填】是否立即开始
   */
  immediatelyStart: boolean
  /**
   * 【非立即开始时必填】合同开始时间
   */
  contractStartTime?: string
  /**
   * 合同结束时间
如果是长期，则不填
   */
  contractEndTime?: string
  /**
   * 【必填】 期限类型
1:周期  2：长期
   */
  contractDurationType: number
  /**
   * 【非必填项】业务员ID
   */
  salespersonId?: string
}

/**
 * 校验供应商分销关系签订是否存在（一级/二级合同）
<AUTHOR>
@since 2024/1/12  15:49
 */
export class VerifySupplyDistributionContractSignedStatusRequest {
  /**
   * 【必填】分销商id
当无上级分销商时 传一级分销商ID
当有上级分销商是 传二级分销商ID（暂无二级分销商）
   */
  distributorId?: string
  /**
   * 【必填】分销服务商ID(分配的网校ID)
   */
  distributorServiceIds?: Array<string>
}

/**
 * <AUTHOR>
@since 2024/1/26  9:14
 */
export class DistributionContractInfoChangedResultResponse {
  /**
   * 状态码
//SUCCESS(&quot;200&quot;,&quot;成功&quot;),
//ERROR(&quot;50001&quot;,&quot;异常情况&quot;),
//CURRENT_DISTRIBUTOR_EXISTS_ONE_RELATIONSHIP(&quot;50002&quot;,&quot;当前分销商在网校已经存在一条有效的一级分销关系，无法续期。&quot;),
//CURRENT_DISTRIBUTOR_EXISTS_TWO_RELATIONSHIP(&quot;50003&quot;,&quot;当前分销商在网校已经存在一条有效的二级分销关系，无法续期。&quot;),
//PARENT_DISTRIBUTION_CONTRACT_NOT_EXIST(&quot;50004&quot;,&quot;上级分销合同不存在&quot;),
//PARENT_DISTRIBUTION_CONTRACT_EXPIRED(&quot;50005&quot;,&quot;上级分销合同周期已过期&quot;),
//SECONDARY_DISTRIBUTION_CONTRACT_VALID_TIME_NOT_IN_PRIMARY_DISTRIBUTION_VALID_TIME(&quot;50006&quot;,&quot;二级分销周期时间未在一级分销周期的有效时间范围内&quot;),
//SECONDARY_DISTRIBUTION_CONTRACT_AREA_NOT_IN_PRIMARY_DISTRIBUTION_AREA(&quot;50007&quot;,&quot;二级分销地区不包含在一级分销地区范围内&quot;);
   */
  code: string
  /**
   * 授权结果信息
   */
  message: string
}

/**
 * 签署供销合同响应结果
<AUTHOR>
@since 2023/12/4  14:46
 */
export class SupplyDistributionContractSignedResultResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 签署结果信息
   */
  message: string
  /**
   * 签署供销合同的ID
   */
  productSupplyDistributionContractId: string
  /**
   * 【当合同关系已存在时候返回已存在的合同ID】
已存在的合同ID
   */
  existContractId: string
  /**
   * 二级与一级分销地区 不匹配 地区描述信息
   */
  distributionAreaNotMatch: DistributionAreaNotMatch
  /**
   * 二级与一级分销周期 不匹配 周期描述信息
   */
  distributionPeriodNotMatch: DistributionPeriodNotMatch
}

/**
 * 二级与一级分销地区 不匹配 地区描述信息
 */
export class DistributionAreaNotMatch {
  primaryDistributionArea: Array<string>
  secondaryDistributionArea: Array<string>
}

/**
 * 二级与一级分销周期 不匹配 周期描述信息
 */
export class DistributionPeriodNotMatch {
  /**
   * 【展示上级的分销周期】上级周期开始时间
   */
  primaryDistributionPeriodStartDate: string
  /**
   * 【展示上级的分销周期】上级周期结束时间
   */
  primaryDistributionPeriodEndDate: string
}

/**
 * 签署供销合同响应结果
<AUTHOR>
@since 2023/12/4  14:46
 */
export class VerifyContractSignedResultResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 签署结果信息
   */
  message: string
  /**
   * 【当合同关系已存在时候返回已存在的合同ID】
已存在的合同ID
   */
  existContractId: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 变更产品供销合同的信息
   * @param request 变更产品供销合同合作周期请求参数
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applySupplyDistributionContractInfoChanged(
    request: SupplyDistributionContractInfoChangedRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applySupplyDistributionContractInfoChanged,
    operation?: string
  ): Promise<Response<DistributionContractInfoChangedResultResponse>> {
    return commonRequestApi<DistributionContractInfoChangedResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 供应商分销关系签订
   * @param request 签订供销合同请求参数
   * @return 签订供销合同结果
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applySupplyDistributionContractSigned(
    request: CreateSupplyDistributionRelationshipRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applySupplyDistributionContractSigned,
    operation?: string
  ): Promise<Response<SupplyDistributionContractSignedResultResponse>> {
    return commonRequestApi<SupplyDistributionContractSignedResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验供应商分销关系签订是否存在（一级/二级合同）
   * @param request 签订分销合同请求参数
   * @return 校验结果
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async verifySupplyDistributionContractSignedStatus(
    request: VerifySupplyDistributionContractSignedStatusRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.verifySupplyDistributionContractSignedStatus,
    operation?: string
  ): Promise<Response<VerifyContractSignedResultResponse>> {
    return commonRequestApi<VerifyContractSignedResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
