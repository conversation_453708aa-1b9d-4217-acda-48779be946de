const simple = require('simple-git')
const git = simple()
const { writeToCache } = require('./common')

const getGitInfo = async() => {
  // 获取当前的 git 地址
  const remoteUrl = await git.listRemote(['--get-url'])
  // 获取当前的分支信息
  const currentBranch = await git.branch('--show-current')
  const splitUrl = remoteUrl.toLowerCase()
    .replace(/https?:\/\//, '').replace(/\n/, '').replace(/\.git$/, '').split('/')
  const splitUrlLength = splitUrl.length
  const slug = splitUrl[splitUrlLength - 1]
  const project = splitUrl[splitUrlLength - 2]
  return {
    slug,
    project,
    branch: currentBranch.current
  }
};
(async() => {
  const gitInfo = await getGitInfo()
  writeToCache('git-info.json', gitInfo)
})()
