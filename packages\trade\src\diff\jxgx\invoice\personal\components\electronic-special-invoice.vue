<template>
  <jxgx-electronic-special-invoice ref="electronicSpecialInvoiceRef"></jxgx-electronic-special-invoice>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import TradeExport from '@api/service/diff/management/jxgx/trade/TradeExport'
  import ElectronicSpecialInvoice from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/electronic-special-invoice.vue'

  @Component
  class JxgxElectronicSpecialInvoice extends ElectronicSpecialInvoice {
    //
    tradeExport = new TradeExport()

    /**
     * 线下专票导出
     */
    async offLinePageElectVatspecialplaInvoiceInExport() {
      return await this.tradeExport.offLinePageElectVatspecialplaInvoiceInExport(this.exportQueryParam)
    }
  }

  @Component({
    components: {
      JxgxElectronicSpecialInvoice
    }
  })
  export default class extends Vue {
    @Ref('electronicSpecialInvoiceRef') electronicSpecialInvoiceRef: JxgxElectronicSpecialInvoice

    /**
     * 线下专票查询
     */
    offLinePageElectVatspecialplaInvoiceInServicer() {
      return this.electronicSpecialInvoiceRef.offLinePageElectVatspecialplaInvoiceInServicer()
    }

    /**
     * 专题线下专票查询
     */
    offLinePageElectVatspecialplaInvoiceInZtServicer() {
      return this.electronicSpecialInvoiceRef.offLinePageElectVatspecialplaInvoiceInZtServicer()
    }
  }
</script>
