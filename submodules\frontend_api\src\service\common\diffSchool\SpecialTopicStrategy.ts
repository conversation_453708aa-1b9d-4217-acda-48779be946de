import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'

export enum TopicEnum {
  // 华医网专题
  HYWZT = 'hywzt',
  //甘兰州专题
  GLZZT = 'gszt',
  // 厦门理工专题
  HYWXMLGZT = 'hywxmlgzt',
  // 漳州天天专题
  HYWZZTTZT = 'hywzzttzt'
}

import systemContext from '@api/service/common/context/Context'
import { frontendApplicationDiff } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

/**
 * 差异化专题映射管理
 */
class SpecialTopicStrategy {
  // 专题映射关系
  topicMap = new Map<string, TopicEnum>()

  /**
   * 读取阿波罗网校服务商id
   */
  getSpecialTopicId = {
    hywzt: () => {
      const specialTopicId = ConfigCenterModule.getFrontendApplicationDiff(frontendApplicationDiff.fjzjHywZtId)?.split(
        ','
      )
      specialTopicId?.forEach((id) => {
        if (!this.topicMap.has(id)) {
          this.topicMap.set(id, TopicEnum.HYWZT)
        }
        return this.topicMap.get(id)
      })
    },
    hywxmlgzt: () => {
      const specialTopicId = ConfigCenterModule.getFrontendApplicationDiff(frontendApplicationDiff.xmlgHywZtId)?.split(
        ','
      )
      specialTopicId?.forEach((id) => {
        if (!this.topicMap.has(id)) {
          this.topicMap.set(id, TopicEnum.HYWXMLGZT)
        }
        return this.topicMap.get(id)
      })
    },
    hywzzttzt: () => {
      const specialTopicId = ConfigCenterModule.getFrontendApplicationDiff(frontendApplicationDiff.zzttHywZtId)?.split(
        ','
      )
      specialTopicId?.forEach((id) => {
        if (!this.topicMap.has(id)) {
          this.topicMap.set(id, TopicEnum.HYWZZTTZT)
        }
        return this.topicMap.get(id)
      })
    },
    gszt: () => {
      const specialTopicId = ConfigCenterModule.getFrontendApplicationDiff(frontendApplicationDiff.gszjGlzZtId)?.split(
        ','
      )
      specialTopicId?.forEach((id) => {
        if (!this.topicMap.has(id)) {
          this.topicMap.set(id, TopicEnum.GLZZT)
        }
        return this.topicMap.get(id)
      })
    }
  }

  /**
   * 构建映射关系
   */
  buildSpeciallTopicMap() {
    // TODO 构建专题映射关系
    this.getSpecialTopicId.hywzt()
    this.getSpecialTopicId.gszt()
    this.getSpecialTopicId.hywxmlgzt()
    this.getSpecialTopicId.hywzzttzt()
  }

  // 获取当前差异化专题
  getCurrentSpecialTopic() {
    const serverId = systemContext?.businessEnvironment?.specialTopicsInfo?.id
    if (this.topicMap.has(serverId)) {
      return this.topicMap.get(serverId)
    }
    return ''
  }
}

export default new SpecialTopicStrategy()
