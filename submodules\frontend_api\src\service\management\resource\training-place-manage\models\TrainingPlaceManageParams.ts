import { CourseStatusEnum } from '@api/service/common/enums/course/CourseStatus'
import { YesOrNoEnums } from '@api/service/common/enums/general/YesOrNoEnums'
import { TrainingPointRequest } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

/**
 * 培训点管理入参
 */
export default class TrainingPlaceManageParams {
  /**
   * 培训点名称
   */
  trainingPlaceName = ''
  /**
   * 培训点状态
   */
  trainingPlaceStatus: CourseStatusEnum = null
  /**
   * 是否被引用
   */
  isUsed: YesOrNoEnums = null
  /**
   * 培训点ids
   */
  ids: Array<string> = []

  static toTrainingPointRequest(dto: TrainingPlaceManageParams) {
    const vo = new TrainingPointRequest()
    vo.name = dto.trainingPlaceName
    if (dto.isUsed) {
      vo.isReferencedByIssue = dto.isUsed === YesOrNoEnums.yes
    }
    if ([CourseStatusEnum.DISABLE, CourseStatusEnum.ENABLE].includes(dto.trainingPlaceStatus)) {
      vo.enabled = dto.trainingPlaceStatus === CourseStatusEnum.ENABLE
    }
    if (dto.ids) {
      vo.ids = dto.ids
    }
    return vo
  }
}
