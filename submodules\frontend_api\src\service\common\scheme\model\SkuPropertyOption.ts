import Property from '@api/service/common/scheme/model/Property'
import RegionTreeProperty from '@api/service/common/scheme/model/RegionTreeProperty'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * @description 方案sku培训属性选项
 */
class SkuPropertyOption {
  /**
   * 年度
   */
  year: Property[] = []
  /**
   * 地区
   */
  region: RegionTreeProperty[] = []
  /**
   * 行业
   */
  industry: Property[] = []
  /**
   * 科目类型
   */
  subjectType: Property[] = []
  /**
   * 技术等级
   */
  technicalGrade: Property[] = []
  /**
   * 培训类别
   */
  trainingCategory: Property[] = []
  /**
   * 培训专业
   */
  trainingMajor: Property[] = []
  /**
   * 培训对象
   */
  trainingObject: Property[] = []
  /**
   * 岗位类别
   */
  positionCategory: Property[] = []
  /**
   * 技术等级
   */
  jobLevel: Property[] = []
  /**
   * 学段
   */
  learningPhase: Property[] = []
  /**
   * 学科
   */
  discipline: Property[] = []
  /**
   * 证书类型
   */
  certificatesType: Property[] = []
  /**
   * 执业类别
   */
  practitionerCategory: Property[] = []
  /**
   * 主/增项
   */
  mainOrAdditionalItem: Property[] = []
  /**
   * 培训形式
   */
  trainingMode: Property<TrainingModeEnum>[] = []
}

export default SkuPropertyOption
