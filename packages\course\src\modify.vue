<route-params content="/:id"></route-params>
<template>
  <el-main>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/course')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/course' }">课程管理</el-breadcrumb-item>
      <el-breadcrumb-item>修改课程</el-breadcrumb-item>
    </el-breadcrumb>
    <select-courseware :show-trigger="false" :pre-selected="arrAy" ref="selectCoursewareRef"></select-courseware>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form
                ref="modifyCourseForm"
                :model="mutationUpdateCourse.updateCourseVo"
                :rules="rules"
                label-width="120px"
                class="m-form"
              >
                <el-form-item label="课程封面：" prop="picture" ref="coursePicture">
                  <cropper-img-upload
                    :dialogStyleOpation="{
                      width: '500px',
                      height: '300px'
                    }"
                    :ratioArr="['16:9']"
                    :initWidth="400"
                    title="课程封面"
                    v-model="mutationUpdateCourse.updateCourseVo.picture"
                  ></cropper-img-upload>
                </el-form-item>
                <el-form-item label="课程名称：" prop="name">
                  <el-input v-model="mutationUpdateCourse.updateCourseVo.name" clearable placeholder="请输入课程名称" />
                </el-form-item>
                <el-form-item label="课程分类：" prop="categoryId" ref="courseCategory">
                  <biz-course-category
                    v-if="showCategory"
                    v-model="categoryId"
                    :check-strictly="false"
                    ref="bizCourseCategoryRef"
                  ></biz-course-category>
                </el-form-item>
                <!-- <el-form-item label="课件供应商：" prop="supplierId">
                  <biz-courseware-supplier :disabled="true" v-model="supplierId"></biz-courseware-supplier>
                </el-form-item> -->
                <el-form-item label="课程简介：" prop="description">
                  <hb-tinymce-editor
                    v-model="mutationUpdateCourse.updateCourseVo.description"
                    v-if="show"
                  ></hb-tinymce-editor>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">配置课程</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center">
            <el-col :lg="22" :xl="20">
              <el-form ref="form" label-width="auto" class="m-form">
                <el-form-item label="课程内容：">
                  <p class="f-fb">
                    <i class="f-dot f-mr5"></i>
                    <span class="f-f15">课程目录</span>
                  </p>
                  <div v-for="(chapter, index) in mutationUpdateCourse.updateCourseVo.chapters" :key="chapter.id">
                    <el-table
                      stripe
                      :data="chapter.coursewares"
                      max-height="500px"
                      ref="dragWebTable"
                      row-key="id"
                      class="m-table f-mt10 f-mb20"
                    >
                      <el-table-column label="排序" width="70" align="center">
                        <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
                      </el-table-column>
                      <el-table-column label="章节1" min-width="300" :render-header="renderFirstHeader(chapter, index)">
                        <template slot-scope="{ row }">
                          <div class="f-flex f-align-center">
                            <i class="el-icon-video-play f-f20 f-c9"></i>
                            <span class="f-ml5">{{ row.name }}</span>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column width="300">
                        <template>
                          <el-progress :percentage="100"></el-progress>
                        </template>
                      </el-table-column>
                      <el-table-column min-width="100" align="center">
                        <template slot-scope="{ row }">{{ row.timeLengthFormat }}</template>
                      </el-table-column>
                      <el-table-column width="100" align="center">
                        <template slot-scope="scope">
                          <el-checkbox v-model="scope.row.trialType" :true-label="1" :false-label="0">试听</el-checkbox>
                        </template>
                      </el-table-column>
                      <template>
                        <el-table-column width="140" align="center" fixed="right">
                          <template slot="header">
                            <el-button type="text" size="mini" @click="showCatalogName(index, chapter)"
                              >重命名</el-button
                            >
                            <el-button type="text" size="mini" @click="removeChapter(chapter)" v-if="!used"
                              >删除</el-button
                            >
                          </template>
                          <template slot-scope="{ row, $index }" v-if="!used">
                            <el-button type="text" size="mini" @click="chapter.removeCourseware($index)">
                              删除
                            </el-button>
                          </template>
                        </el-table-column>
                      </template>
                    </el-table>
                  </div>
                  <hb-empty
                    v-if="!mutationUpdateCourse.updateCourseVo.chapters.length && !used"
                    show-action
                    action-text="添加目录"
                    description="未添加课程目录，请点击添加目录！"
                    @action="showCatalogName()"
                  ></hb-empty>
                  <el-button
                    v-if="mutationUpdateCourse.updateCourseVo.chapters.length && !used"
                    type="primary"
                    plain
                    icon="el-icon-folder-add"
                    @click="showCatalogName()"
                  >
                    添加目录
                  </el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="doCancel">取消</el-button>
        <el-button type="primary" @click="doSave" :loading="loading">保存</el-button>
      </div>
    </div>
    <el-drawer :title="curTitle" :visible.sync="catalogNameShow" size="600px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form ref="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="目录名称：" required>
                <el-input v-model="currentCatalogName" clearable maxlength="100" placeholder="请输入目录名称" />
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="catalogNameShow = false">取消</el-button>
                <el-button type="primary" @click="addChapter">保存</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
    <give-up-dialog :give-up-model="uiConfig.giveUpModel"></give-up-dialog>
  </el-main>
</template>
<script lang="tsx">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  // import UpdateCourse from '@api/service/management/resource/course/mutation/vo/UpdateCourse'
  import CropperImgUpload from '@hbfe/jxjy-admin-components/src/cropper-img-upload.vue'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import CourseDetail from '@api/service/management/resource/course/query/vo/CourseDetail'
  import MutationUpdateCourse from '@api/service/management/resource/course/mutation/MutationUpdateCourse'
  // import { cloneDeep } from 'lodash'
  import Sortable from 'sortablejs'
  import { ElTable } from 'element-ui/types/table'
  import ChapterVo from '@api/service/management/resource/course/mutation/vo/Chapter'
  import Chapter from '@api/service/management/resource/course/mutation/vo/Chapter'
  import SelectCourseware from '@hbfe/jxjy-admin-course/src/components/select-courseware.vue'
  import CoursewareListDetail from '@api/service/management/resource/courseware/query/vo/CoursewareListDetail'
  import QueryChooseCourseStatistic, {
    ChooseCourseStatisticsParams
  } from '@api/service/management/statisticalReport/query/QueryChooseCourseStatistic'
  import { UiPage } from '@hbfe/common'
  // import Courseware from '@api/service/management/resource/course/mutation/vo/Courseware'
  @Component({
    components: {
      GiveUpDialog,
      CropperImgUpload,
      SelectCourseware
    }
  })
  export default class extends Vue {
    @Ref('modifyCourseForm') modifyCourseForm: any
    @Ref('bizCourseCategoryRef') bizCourseCategoryRef: any
    rules = {
      picture: [{ required: true, message: '请选择课程封面', trigger: ['change', 'blur'] }],
      name: [
        { required: true, message: '请输入课程名称', trigger: ['change', 'blur'] },
        {
          validator: async (rule: any, value: any, callback: any) => {
            await this.checkName(rule, value, callback)
          },
          trigger: 'blur'
        }
      ],
      categoryId: [{ required: true, message: '请选择课程分类', trigger: ['change', 'blur'] }]
      //   supplierId: [{ required: true, message: '请选择课件供应商', trigger: ['change', 'blur'] }]
    }
    updateCourseVo: CourseDetail = new CourseDetail()
    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }

    // 富文本有时候显示不出来
    show = false
    showCategory = false

    //防抖
    loading = false

    mutationUpdateCourse: MutationUpdateCourse = new MutationUpdateCourse()

    categoryId = new Array<string>()

    // supplierId = new Array<string>()

    catalogNameShow = false
    currentCatalogName = ''
    curTitle = '添加目录'
    curChapter = new Chapter()
    courseId = ''

    async checkName(rule: any, value: any, callback: any) {
      const res: any = await ResourceModule.courseFactory.getCheckCourse(value, this.courseId)
      if (res?.code === '3000') {
        return callback('课程名称重复，请修改')
      }
      return callback()
    }

    /**
     * 是否被使用
     * */
    used = true

    @Ref('dragWebTable')
    dragWebTable: any

    @Watch('mutationUpdateCourse.updateCourseVo.chapters', { immediate: false, deep: true })
    onChangeDrag(val: any) {
      if (this.dragWebTable) this.dragWebTable.map((item: any, index: number) => this.dragElement(item, index))
    }
    dragElement(table: ElTable, index: number) {
      const el = table.$el.querySelector('.el-table__body-wrapper > table > tbody') as HTMLElement
      return new Sortable(el, {
        ghostClass: 'blue-background-class',
        handle: '.hb-iconfont',
        onEnd: ({ newIndex, oldIndex }) => {
          const curRow = this.mutationUpdateCourse.updateCourseVo.chapters[index].coursewares.splice(oldIndex, 1)[0]
          this.mutationUpdateCourse.updateCourseVo.chapters[index].coursewares.splice(newIndex, 0, curRow)
          const newArray = this.mutationUpdateCourse.updateCourseVo.chapters[index].coursewares.slice(0)
          this.mutationUpdateCourse.updateCourseVo.chapters[index].coursewares = []
          this.$nextTick(function () {
            this.mutationUpdateCourse.updateCourseVo.chapters[index].coursewares = newArray
          })
        }
      })
    }
    async created() {
      this.courseId = this.$route.params.id as string
      this.mutationUpdateCourse = await ResourceModule.courseFactory.getUpdateCourse(this.courseId)
      //   this.supplierId.push(this.mutationUpdateCourse.updateCourseVo?.supplierId)
      this.categoryId = await this.getIdList(this.mutationUpdateCourse.updateCourseVo.categoryId as string)
      this.showCategory = true
      if (this.categoryId && this.showCategory) {
        this.$nextTick(() => {
          console.log(this.bizCourseCategoryRef, 'bizCourseCategoryRef')
          this.bizCourseCategoryRef?.handleFocus()
        })
      }
      this.dragWebTable.map((item: any, index: number) => this.dragElement(item, index))

      this.getUsedStatus()
    }

    /**
     * 查询课程是否被引用
     * */
    async getUsedStatus() {
      const queryChooseCourseStatistic = new QueryChooseCourseStatistic()
      const page = new UiPage()
      page.pageNo = 1
      page.pageSize = 10
      const params = new ChooseCourseStatisticsParams()
      params.eliminate = true
      params.courseId = [this.$route.params.id]

      // 依据课程统计里的选课人数与退课人数做判断
      const statistic = await queryChooseCourseStatistic.getChooseCourseStatisticList(page, params, true)
      if (statistic?.length && (statistic[0]?.sumChooseCourseCount || statistic[0]?.totalWithdrawalCount)) {
        this.used = true
      } else {
        this.used = false
      }
    }

    async getIdList(id: string) {
      const categoryList = await ResourceModule.courseCategoryFactory.query.queryReverserById(id)
      const idList = ['-1']
      categoryList.forEach((item) => {
        idList.push(item.id)
      })
      return idList
    }

    get selectCourseWareList() {
      const list = new Array<string>()
      this.mutationUpdateCourse.updateCourseVo.chapters.map((item) => {
        list.push(...item.getSelectedIdList())
      })
      return list
    }

    @Watch('updateCourseVo.picture', {
      immediate: true,
      deep: true
    })
    pictureChange(val: string) {
      const el: any = this.$refs['coursePicture']
      if (el) {
        if (val) {
          //有图片时清除校验
          el.clearValidate()
        } else {
          el.validate()
        }
      }
    }

    waitSelectCoursewareChapter: ChapterVo = new ChapterVo()

    renderFirstHeader(chapter: ChapterVo, index: number) {
      return () => {
        return (
          <div>
            <span>{chapter.name} </span>
            {(!this.used && (
              <el-button
                type="primary"
                size="mini"
                plain
                className="f-ml10"
                onClick={this.addWaitSelectCoursewareChapter(chapter, index)}
              >
                添加多媒体
              </el-button>
            )) || <span></span>}
          </div>
        )
      }
    }
    arrAy: string[] = []
    addWaitSelectCoursewareChapter(chapter: ChapterVo, index: number) {
      return () => {
        this.waitSelectCoursewareChapter = chapter
        this.arrAy = chapter.coursewares.map((item) => item.id)
        const selectCoursewareRef = this.$refs.selectCoursewareRef as SelectCourseware
        selectCoursewareRef.open()
        selectCoursewareRef.$once(
          'confirm',
          (selectedItems: Array<CoursewareListDetail>, cencelItems: Array<CoursewareListDetail>) => {
            this.waitSelectCoursewareChapter.addCourseware(selectedItems, false, cencelItems)
            selectCoursewareRef.close()
            this.$nextTick(() => {
              this.dragWebTable[index].doLayout()
            })
          }
        )
      }
    }

    showCatalogName(index?: number, item?: Chapter) {
      if (!item) {
        this.curTitle = '添加目录'
        this.currentCatalogName = ''
        this.curChapter = new Chapter()
      } else {
        this.curTitle = '重命名'
        this.currentCatalogName = item.name
        this.curChapter = item
      }
      this.catalogNameShow = true
    }

    /**
     * 添加/重命名目录
     */
    addChapter() {
      if (!this.currentCatalogName) {
        this.$message.warning('请输入目录名称')
        return
      }
      if (this.curTitle === '添加目录') {
        const chapter = new Chapter()
        chapter.name = this.currentCatalogName
        this.mutationUpdateCourse.updateCourseVo.chapters.push(chapter)
      } else {
        this.curChapter.name = this.currentCatalogName
      }
      this.catalogNameShow = false
    }

    removeChapter(chapter: Chapter) {
      if (chapter.coursewares.length) {
        this.$message.error('请先移除课件后，才可删除')
        return
      }
      this.mutationUpdateCourse.updateCourseVo.chapters = this.mutationUpdateCourse.updateCourseVo.chapters.filter(
        (n) => !Object.is(chapter, n)
      )
    }

    @Watch('categoryId')
    changeCategory(val: Array<string>) {
      if (this.categoryId?.length) {
        this.mutationUpdateCourse.updateCourseVo.categoryId = this.categoryId[this.categoryId.length - 1]
      } else {
        this.mutationUpdateCourse.updateCourseVo.categoryId = null
      }
      const el: any = this.$refs['courseCategory']
      if (el) {
        if (val?.length) {
          el.clearValidate()
        } else {
          el.validate()
        }
      }
    }
    /*    changeAuditionStatus(index: number, subIndex: number) {
      const item = Object.assign({}, this.mutationUpdateCourse.updateCourseVo.chapters[index].coursewares[subIndex])
      this.$set(this.mutationUpdateCourse.updateCourseVo.chapters[index].coursewares, subIndex, item)
      this.mutationUpdateCourse.updateCourseVo.chapters[index].coursewares[subIndex] = item
    }*/

    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
      this.uiConfig.giveUpModel.routerUrl = '/resource/course'
    }

    doSave() {
      this.modifyCourseForm.validate((valid: boolean) => {
        if (valid) {
          this.doModify()
        }
      })
    }

    async doModify() {
      this.loading = true
      if (this.categoryId?.length) {
        this.mutationUpdateCourse.updateCourseVo.categoryId = this.categoryId[this.categoryId.length - 1]
      } else {
        this.mutationUpdateCourse.updateCourseVo.categoryId = null
      }
      try {
        const res = await this.mutationUpdateCourse.doUpdate()
        if (res.code === 200) {
          this.$message.success('保存成功')
          // 加延时器，解决列表数据延迟问题
          setTimeout(() => {
            //   this.loading = false
            this.$router.push('/resource/course')
          }, 1000)
        } else {
          this.loading = false
          this.$message.error('创建失败')
        }
      } catch (e) {
        this.loading = false
      }
    }

    async activated() {
      this.show = false
      setTimeout(() => {
        this.show = true
      }, 300)
    }
  }
</script>
<style lang="scss">
  td.hb-iconfont {
    cursor: move;

    &:hover {
      background: #bfbfbf !important;
      color: white;
    }

    &::after {
      content: '::';
    }
  }
</style>
