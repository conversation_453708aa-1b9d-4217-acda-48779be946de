import getCertificateBatchPrintJobPage from './queries/getCertificateBatchPrintJobPage.graphql'
import getCertificateOperatorIdNameByUserIds from './queries/getCertificateOperatorIdNameByUserIds.graphql'
import getCertificatePage from './queries/getCertificatePage.graphql'
import getCertificatePrintLogList from './queries/getCertificatePrintLogList.graphql'
import getCertificateRenderInfo from './queries/getCertificateRenderInfo.graphql'
import getCertificateTemplateByIssueId from './queries/getCertificateTemplateByIssueId.graphql'
import getCertificateTemplateBySchemeId from './queries/getCertificateTemplateBySchemeId.graphql'
import getCertificateTemplatePreview from './queries/getCertificateTemplatePreview.graphql'
import getIssueIdNameListByIds from './queries/getIssueIdNameListByIds.graphql'
import getSchemeIdNameListByIds from './queries/getSchemeIdNameListByIds.graphql'
import getStudentCertificateData from './queries/getStudentCertificateData.graphql'
import getStudentCertificatePage from './queries/getStudentCertificatePage.graphql'
import getUserCertificateData from './queries/getUserCertificateData.graphql'
import batchPrintCertificate from './mutates/batchPrintCertificate.graphql'
import printCertificate from './mutates/printCertificate.graphql'

export {
  getCertificateBatchPrintJobPage,
  getCertificateOperatorIdNameByUserIds,
  getCertificatePage,
  getCertificatePrintLogList,
  getCertificateRenderInfo,
  getCertificateTemplateByIssueId,
  getCertificateTemplateBySchemeId,
  getCertificateTemplatePreview,
  getIssueIdNameListByIds,
  getSchemeIdNameListByIds,
  getStudentCertificateData,
  getStudentCertificatePage,
  getUserCertificateData,
  batchPrintCertificate,
  printCertificate
}
