/**
 * @description 用户测试发布配置
 */
class UserCoursePaperPublishConfigure {
  /**
   * 测试试题数
   */
  questionCount: number
  /**
   * 总成绩
   */
  totalScore: number
  /**
   * 测试试题数量配置
   * @description 0 固定题数  1 按课程学时数计算
   */
  questionCountConfigureType: number
  /**
   * 每学时测试试题数量配置
   */
  questionCountPerPeriod: number
  /**
   * 每道测验题答题时长
   */
  timeLengthPerQuestion: number
}

export default UserCoursePaperPublishConfigure
