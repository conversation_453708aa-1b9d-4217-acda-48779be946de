import { Page } from '@hbfe/common'
import TrainingPlaceInfoDto from '@api/service/customer/training-place/models/TrainingPlaceInfoDto'
import SchemeLearningQueryBackstage, {
  TrainingPointRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

export default class TrainingPlaceManage {
  /**
   * 获取培训地点
   * @param ids 培训地点集合
   */
  async getTrainingPlaceByIds(ids: string[]) {
    const map = new Map<string, TrainingPlaceInfoDto>()
    const request = new TrainingPointRequest()
    request.ids = ids
    const page = new Page(1, ids.length)
    const response = await SchemeLearningQueryBackstage.pageTrainingPointInServicer({ page, request })

    if (response.data?.currentPageData?.length) {
      response.data.currentPageData.map(res => {
        map.set(res.id, TrainingPlaceInfoDto.toTrainingPlaceInfoDto(res))
      })
    }

    return map
  }
}
