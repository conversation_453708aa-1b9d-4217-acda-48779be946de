import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-basicdata-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-basicdata-query-front-gateway-forestage-btglxt'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum CertificateSortFieldEnum {
  grantTime = 'grantTime',
  expiredTime = 'expiredTime'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 功能描述：用户基本查询条件
@Author： wtl
@Date： 2022年1月26日 09:30:12
 */
export class UserRequest {
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  userNameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  phoneMatchType?: number
}

export class CertificateQueryRequest {
  /**
   * 证书持有人
   */
  certificateOwner?: CertificateOwnerRequest
  /**
   * 证书信息
   */
  certificateInfo?: CertificateInfoRequest
  /**
   * 证书培训信息
   */
  certificateTrainingInfo?: CertificateTrainingInfoRequest
  /**
   * 证书排序
   */
  sortList?: Array<CertificateSortRequest>
}

export class CertificateInfoRequest {
  /**
   * 证书id集合
   */
  certificateIdList?: Array<string>
  /**
   * 证书编号
   */
  certificateNo?: string
  /**
   * 证书编号匹配方式，默认为like(0：完全匹配 1：模糊查询，*certificateNo* 2：左模糊查询，*certificateNo 3:右模糊查询，certificateNo*)
@see MatchTypeConstant
   */
  certificateNoMatchType?: number
  /**
   * 证书类别id集合
   */
  certificateTypeIdList?: Array<string>
  /**
   * 证书类型id集合
   */
  certificateCategoryIdList?: Array<string>
  /**
   * 证书专业id集合
   */
  certificateMajorIdList?: Array<string>
  /**
   * 证书等级id集合
   */
  certificateLevelIdList?: Array<string>
  /**
   * 证书职业id集合
   */
  certificateProfessionIdList?: Array<string>
  /**
   * 证书职业工种id集合
   */
  certificateWorkTypeIdList?: Array<string>
  /**
   * 证书职业id和证书职业工种id之间查询关系
   */
  professionAndWorkTypeRelation?: number
  /**
   * 证书类型id集合
   */
  trainingTypeIdList?: Array<string>
  /**
   * 发证单位编码
   */
  grantUnitCode?: string
  /**
   * 发证单位编码匹配方式，默认为like(0：完全匹配 1：模糊查询，*grantUnitCode* 2：左模糊查询，*grantUnitCode 3:右模糊查询，grantUnitCode*)
@see MatchTypeConstant
   */
  grantUnitCodeMatchType?: number
  /**
   * 发证单位名称
   */
  grantUnitName?: string
  /**
   * 发证单位名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*grantUnitName* 2：左模糊查询，*grantUnitName 3:右模糊查询，grantUnitName*)
@see MatchTypeConstant
   */
  grantUnitNameMatchType?: number
  /**
   * 发证地区路径
   */
  regionPathList?: Array<string>
  /**
   * 发证地区路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  regionPathListMatchType?: number
  /**
   * 职称
   */
  professional?: string
  /**
   * 证书来源类型
   */
  sourceTypeList?: Array<string>
  /**
   * 证书发证时间范围
   */
  grantDateScope?: DateScopeRequest
  /**
   * 证书有效期范围
   */
  expiredDateScope?: DateScopeRequest
  /**
   * 证书颁发时间范围
   */
  awardDateScope?: DateScopeRequest
  /**
   * 证书分类
1、内部证书 2、外部证书
@see com.fjhb.ms.basicdata.enums.CertificateClassifyEnum
   */
  certificateClassifyList?: Array<number>
  /**
   * 证书状态集合
0、未颁发 1、正常 2、注销
@see com.fjhb.ms.basicdata.enums.CertificateStatusEnum
   */
  statusList?: Array<number>
}

/**
 * 证书所有人查询条件
 */
export class CertificateOwnerRequest {
  /**
   * 学员id集合
   */
  studentIdList?: Array<string>
}

export class CertificateSortRequest {
  /**
   * 排序字段
   */
  sortField?: CertificateSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 证书相关培训信息
<AUTHOR>
@date 2022-11-4
 */
export class CertificateTrainingInfoRequest {
  /**
   * 学习方案id
   */
  learningSchemeIdList?: Array<string>
  /**
   * 参训资格id
   */
  qualificationIdList?: Array<string>
}

export class DateScopeRequest {
  beginTime?: string
  endTime?: string
}

export class RegionModel {
  regionId: string
  regionPath: string
  provinceId: string
  provinceName: string
  cityId: string
  cityName: string
  countyId: string
  countyName: string
}

/**
 * 功能描述：账户信息
@Author： wtl
@Date： 2022年5月11日 15:30:56
 */
export class AccountResponse {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
@see AccountTypes
   */
  accountType: number
  /**
   * 单位信息
   */
  unitInfo: UnitInfoResponse
  /**
   * 所属顶级企业帐户Id
   */
  rootAccountId: string
  /**
   * 帐户状态 1：正常，2：冻结，3：注销
@see AccountStatus
   */
  status: number
  /**
   * 注册方式
0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
@see AccountRegisterTypes
   */
  registerType: number
  /**
   * 来源类型
0内置，1项目主网站，2安卓，3IOS
@see AccountSourceTypes
   */
  sourceType: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 最后更新时间
   */
  lastUpdateTime: string
}

/**
 * 功能描述：帐户认证信息
@Author： wtl
@Date： 2022年5月11日 14:23:18
 */
export class AuthenticationResponse {
  /**
   * 帐号
   */
  identity: string
  /**
   * 认证标识类型
1用户名,2手机,3身份证,4邮箱,5第三方OpenId
   */
  identityType: number
  /**
   * 认证方式状态 1启用，2禁用
@see AuthenticationStatusEnum
   */
  status: number
}

/**
 * 功能描述：角色信息
@Author： wtl
@Date： 2022/1/24 20:17
 */
export class RoleResponse {
  /**
   * 角色id
   */
  roleId: string
  /**
   * 角色名称
   */
  roleName: string
  /**
   * 角色类型
（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
   */
  roleType: string
  /**
   * 角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
@see RoleCategories
   */
  roleCategory: number
}

/**
 * 功能描述 : 企业单位管理员信息
@date : 2022/6/18 12:24
 */
export class EnterpriseUnitAdminInfoResponse {
  /**
   * 企业单位管理员归属信息
   */
  enterpriseUnitAdminOwner: EnterpriseUnitAdminOwnerResponse
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 管理员用户信息
   */
  userInfo: AdminUserInfoResponse
  /**
   * 人员信息
   */
  personInfo: EnterpriseUnitPersonInfoResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
  /**
   * 角色信息集合
   */
  roleList: Array<RoleResponse>
}

/**
 * 功能描述：管理员用户信息
@Author： wtl
@Date： 2022年1月25日 15:48:48
 */
export class AdminUserInfoResponse {
  /**
   * 管辖地区集合
   */
  manageRegionList: Array<RegionModel>
  /**
   * 办公室（所在处/科室）
   */
  office: string
  /**
   * 岗位/职位
   */
  position: string
  /**
   * 备注
   */
  remark: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 性别
-1未知，0女，1男
@see Genders
   */
  gender: number
  /**
   * 证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string
}

/**
 * 功能描述 : 企业单位管理员归属查询条件
@date : 2022年9月2日 10:53:02
 */
export class EnterpriseUnitAdminOwnerResponse {
  /**
   * 企业单位id
   */
  enterpriseUnitIdList: Array<string>
}

/**
 * 人员信息模型
 */
export class EnterpriseUnitPersonInfoResponse {
  /**
   * 是否法人帐号
   */
  isCorporateAccount: boolean
  /**
   * 业务关系集合
   */
  businessRelationshipList: Array<PersonBusinessRelationshipResponse>
  /**
   * 人员实名认证信息
   */
  personIdentityVerificationInfo: PersonIdentityVerificationResponse
}

/**
 * 功能描述：业务关系模型信息
@Author： wtl
@Date： 2024年3月29日 20:51:03
 */
export class PersonBusinessRelationshipResponse {
  /**
   * 关系id
   */
  relationId: string
  /**
   * 业务类型（1：组织关系 2：主要经办  3：主要经办）
@see PersonUnitRelationshipBusinessTypes
   */
  businessType: number
  /**
   * 关系类型（标识人与哪个实体产生的关系）
@see PersonRelationTypeConstant
   */
  relationType: string
  /**
   * 关系值（业务类型为组织关系时，目前该值为单位id或部门id；业务类型为主要经办或经办，该值为单位id）
   */
  relationValue: string
  /**
   * 关系状态（0：冻结 1：正常）
@see PersonUnitRelationshipStatus
   */
  status: number
}

/**
 * 人员实名认证信息模型
 */
export class PersonIdentityVerificationResponse {
  /**
   * 是否已认证
   */
  identityVerification: boolean
  /**
   * 认证渠道(1:闽政通 2：腾讯)
   */
  identityVerificationChannel: number
  /**
   * 认证时间
   */
  identityVerificationTime: string
}

/**
 * 单位信息模型
 */
export class UnitInfoResponse {
  /**
   * 单位ID
   */
  unitId: string
}

/**
 * 网关证书信息返回值
<AUTHOR>
@date 2022-08-18
 */
export class CertificateResponse {
  /**
   * 证书业务归属信息
   */
  businessOwnerInfo: CertificateBusinessOwnerResponse
  /**
   * 证书信息
   */
  certificateInfo: CertificateInfoResponse
  /**
   * 证书培训信息
   */
  certificateTrainingInfo: CertificateTrainingInfoResponse
}

export class CertificateBusinessOwnerResponse {
  /**
   * 学员用户id
   */
  studentId: string
  /**
   * 证件类型
@see com.fjhb.domain.basicdata.api.consts.IdCardTypes
   */
  idCardType: number
  /**
   * 证件号
   */
  idCard: string
  /**
   * 姓名
   */
  userName: string
}

export class CertificateInfoResponse {
  /**
   * 证书id
   */
  certificateId: string
  /**
   * 证书名称
   */
  certificateName: string
  /**
   * 证书编号
   */
  certificateNo: string
  /**
   * 证书类别id
   */
  certificateTypeId: string
  /**
   * 证书类型id
   */
  certificateCategoryId: string
  /**
   * 证书专业id
   */
  certificateMajorId: string
  /**
   * 证书等级id
   */
  certificateLevelId: string
  /**
   * 证书职业id
   */
  certificateProfessionId: string
  /**
   * 证书职业工种id
   */
  certificateWorkTypeId: string
  /**
   * 职称
   */
  professional: string
  /**
   * 培训类型Id
   */
  trainingTypeId: string
  /**
   * 证书来源类型
   */
  sourceType: string
  /**
   * 证书来源id
   */
  sourceId: string
  /**
   * 发证单位id
   */
  grantUnitId: string
  /**
   * 发证单位编码
   */
  grantUnitCode: string
  /**
   * 发证单位名称
   */
  grantUnitName: string
  /**
   * 发证日期
   */
  grantTime: string
  /**
   * 颁发日期
   */
  awardTime: string
  /**
   * 证书有效期
   */
  expiredTime: string
  /**
   * 发证地区
   */
  grantRegion: RegionModel
  /**
   * 是否纳入省级证书库
   */
  intoOfficialLibrary: boolean
  /**
   * 证书状态
0、未颁发 1、正常 2、注销
@see com.fjhb.ms.basicdata.enums.CertificateStatusEnum
   */
  status: number
  /**
   * 备注
   */
  remark: string
  /**
   * 证书分类
1、内部证书 2、外部证书 3、鉴定证书
@see CertificateClassifyEnum
   */
  certificateClassify: number
  /**
   * 证书信息创建时间
   */
  createdTime: string
  /**
   * 证书信息更新时间
   */
  updateTime: string
  /**
   * 鉴定机构id
   */
  identificationUnitId: string
  /**
   * 鉴定机构code
   */
  identificationUnitCode: string
  /**
   * 鉴定单位名称
   */
  identificationUnitName: string
  /**
   * 鉴定机构地区
   */
  identificationUnitRegion: RegionModel
}

/**
 * 证书相关培训信息
<AUTHOR>
@date 2022-11-4
 */
export class CertificateTrainingInfoResponse {
  /**
   * 学习方案id
   */
  learningSchemeId: string
  /**
   * 参训资格id
   */
  qualificationId: string
  /**
   * 培训单位
   */
  trainingUnitId: string
  /**
   * 考试日期
   */
  examTime: string
}

/**
 * 功能描述：学员信息
@Author： wtl
@Date： 2022年1月26日 10:38:15
 */
export class StudentInfoResponse {
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 学员用户信息
   */
  userInfo: StudentUserInfoResponse
  /**
   * 第三方绑定信息
   */
  openPlatformBind: OpenPlatformBindResponse
}

/**
 * 功能描述：附件信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class AttachmentInfoResponse {
  /**
   * 附件名称
   */
  name: string
  /**
   * 附件地址
   */
  url: string
}

/**
 * 功能描述：学员绑定信息
@Author： wtl
@Date： 2022年5月12日 14:42:51
 */
export class OpenPlatformBindResponse {
  /**
   * 是否绑定微信
   */
  bindWX: boolean
  /**
   * 微信昵称
   */
  nickNameByWX: string
}

/**
 * 功能描述：学员证书信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class StudentCertificateResponse {
  /**
   * 证书id
   */
  certificateId: string
  /**
   * 证书编号
   */
  certificateNo: string
  /**
   * 证书类别
   */
  certificateCategory: string
  /**
   * 注册专业
   */
  registerProfessional: string
  /**
   * 发证日期
   */
  releaseStartTime: string
  /**
   * 证书有效期
   */
  certificateEndTime: string
  /**
   * 证书附件信息
   */
  attachmentList: Array<AttachmentInfoResponse>
}

/**
 * 功能描述：学员行业信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class StudentIndustryResponse {
  /**
   * 用户行业id
   */
  userIndustryId: string
  /**
   * 行业id
   */
  industryId: string
  /**
   * 一级专业类别id
   */
  firstProfessionalCategory: string
  /**
   * 二级专业类别id
   */
  secondProfessionalCategory: string
  /**
   * 职称等级
   */
  professionalQualification: string
  /**
   * 学员证书信息集合
   */
  userCertificateList: Array<StudentCertificateResponse>
}

/**
 * 功能描述：学员用户信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class StudentUserInfoResponse {
  /**
   * 用户昵称
   */
  nickName: string
  /**
   * 单位所属地区
   */
  region: RegionModel
  /**
   * 工作单位名称
   */
  companyName: string
  /**
   * 头像地址
   */
  photo: string
  /**
   * 联系地址
   */
  address: string
  /**
   * 学员行业信息集合
   */
  userIndustryList: Array<StudentIndustryResponse>
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 性别
-1未知，0女，1男
@see Genders
   */
  gender: number
  /**
   * 证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string
}

/**
 * 功能描述：企业单位信息
@Author： wtl
@Date： 2022年6月9日 14:20:55
 */
export class EnterpriseUnitInfoResponse {
  /**
   * 企业单位业务归属信息
   */
  businessOwnerInfo: EnterpriseUnitBusinessOwnerResponse
  /**
   * 单位基本信息
   */
  unitBase: EnterpriseUnitBaseResponse
  /**
   * 经营信息
   */
  businessInfo: BusinessInfoResponse
  /**
   * 单位认证信息
   */
  unitIdentityVerificationInfo: UnitIdentityVerificationResponse
}

/**
 * 功能描述：附件信息
 */
export class AttachmentResponse {
  /**
   * 附件id
   */
  attachmentId: string
  /**
   * 附件类型
1《营业执照 / 民办法人登记证书/非民办企业法人登记证书》
2《开展短期职业培训承诺书》（加盖公司公章）》
3《培训单位基本信息表》（加盖公司公章）》
4《培训单位办学许可证》（加盖公司公章）》
5《年度年审合格证书》（加盖公司公章）》
6 开通依据
   */
  attachmentType: number
  /**
   * 附件名称
   */
  attachmentName: string
  /**
   * 附件路径
   */
  attachmentPath: string
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 功能描述：企业经营信息
@Author： wtl
@Date： 2022年6月9日 14:31:03
 */
export class BusinessInfoResponse {
  /**
   * 营业期限起始日期
   */
  operatingBeginDate: string
  /**
   * 营业期限截止日期
   */
  operatingEndDBtglxtEnterpriseUnitBackStageQueryResolverate: string
  /**
   * 行业信息
   */
  industry: IndustryResponse
  /**
   * 经营范围,例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。
   */
  businessScope: string
  /**
   * 主营业务
例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。
   */
  mainBusiness: string
}

/**
 * 功能描述：单位联系人信息
 */
export class ContactPersonInfoResponse {
  /**
   * 联系人
   */
  contact: string
  /**
   * 联系电话
   */
  contactPhone: string
}

/**
 * 功能描述：企业单位信息
@Author： wtl
@Date： 2022年6月9日 14:23:04
 */
export class EnterpriseUnitBaseResponse {
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 单位英文名称
   */
  enName: string
  /**
   * 统一社会信用代码
   */
  code: string
  /**
   * 单位简称
   */
  shortName: string
  /**
   * 单位业务类型
说明：
1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
11地区管理单位,12行业主管单位,13技工院校,14职业院校.15线上培训机构,10000实名制报表补贴单位,10001评价机构
@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
@see UnitBusinessQueryTypes
   */
  businessType: number
  /**
   * logo
   */
  logo: string
  /**
   * 法人信息
   */
  legalPersonInfo: LegalPersonResponse
  /**
   * 单位类型（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）
   */
  unitType: UnitTypeResponse
  /**
   * 单位规模（1：微型 2：小型 3：中型 4：大型）
@see com.fjhb.domain.basicdata.api.unit.consts.UnitScales
   */
  scale: number
  /**
   * 企业经济类型(国有经济、联营经济、私营企业、股份制、港澳台投资、外商投资、其他经济)
@see com.fjhb.domain.basicdata.api.unit.consts.UnitEconomicTypes
   */
  economicTypes: number
  /**
   * 产业类别
   */
  industrialCategory: string
  /**
   * 成立日期
   */
  foundedDate: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 邮政编码
   */
  postCode: string
  /**
   * 传真
   */
  faxNumber: string
  /**
   * 注册地区
   */
  region: RegionModel
  /**
   * 联系地址
   */
  address: string
  /**
   * 注册地址
   */
  registerAddress: string
  /**
   * 登记机关
   */
  registeredOrgan: string
  /**
   * 注册资金
   */
  registeredCapital: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 单位状态
说明：1正常,2冻结
   */
  status: number
  /**
   * 工商注册号
   */
  businessRegistrationNumber: string
  /**
   * 纳税人资质
   */
  taxpayerQualification: string
  /**
   * 邮箱地址
   */
  emailAddress: string
  /**
   * 联系人信息
   */
  contactPersonInfo: ContactPersonInfoResponse
  /**
   * 单位资质附件类型
   */
  attachmentList: Array<AttachmentResponse>
}

/**
 * 企业单位业务归属信息
 */
export class EnterpriseUnitBusinessOwnerResponse {
  /**
   * 企业归属信息路径
单位路径（若单位为福州市企业，则该值为:&quot;/福建省企业id/福州市企业id&quot;）
   */
  unitIdPath: string
}

/**
 * 行业信息
<AUTHOR>
@date 2022-06-18
 */
export class IndustryResponse {
  /**
   * 行业信息ID路径
   */
  industryIdPath: string
  /**
   * 门类
   */
  firstLevelIndustryId: string
  /**
   * 大类
   */
  secondLevelIndustryId: string
  /**
   * 中类
   */
  thirdLevelIndustryId: string
  /**
   * 小类
   */
  fourthLevelIndustryId: string
}

/**
 * 功能描述：企业法人信息
@Author： wtl
@Date： 2022年6月9日 14:31:03
 */
export class LegalPersonResponse {
  /**
   * 法定代表人
   */
  legalPerson: string
  /**
   * 证件类型
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
}

/**
 * 单位认证信息模型
 */
export class UnitIdentityVerificationResponse {
  /**
   * 是否已认证
   */
  identityVerification: boolean
  /**
   * 认证渠道（单位认证渠道：UnitIdentityVerificationChannels 人员认证渠道：PersonIdentityVerificationChannels）
@see PersonIdentityVerificationChannels
@see UnitIdentityVerificationChannels
   */
  identityVerificationChannel: number
  /**
   * 认证时间
   */
  identityVerificationTime: string
}

/**
 * 单位类型:（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）
<AUTHOR>
@date : 2022/6/18 14:15
 */
export class UnitTypeResponse {
  /**
   * 单位类型ID路径
   */
  unitTypeIdPath: string
  /**
   * 一级
   */
  firstLevelUnitTypeId: string
  /**
   * 二级
   */
  secondLevelUnitTypeId: string
  /**
   * 三级
   */
  thirdLevelUnitTypeId: string
}

export class CertificateResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CertificateResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * @Description  身份证姓名获取角色id
   * <AUTHOR>
   * @Date 2023/5/8 15:33
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async findRoleListByIdentity(
    request: UserRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findRoleListByIdentity,
    operation?: string
  ): Promise<Response<Array<RoleResponse>>> {
    return commonRequestApi<Array<RoleResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：企业-当前登录企业管理员信息
   * 描述：查询当前登录管理员的信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnterpriseUnitAdminInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitAdminInfoInMyself,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponse>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：人社讲师-查询企业单位信息-详情接口
   * 描述：人社讲师-查询企业单位信息-详情接口
   * @param unitId                  :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse
   * @date : 2022年6月9日 10:49:47
   * @param query 查询 graphql 语法文档
   * @param unitId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnterpriseUnitInfoInLibraryTeacher(
    unitId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitInfoInLibraryTeacher,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponse>> {
    return commonRequestApi<EnterpriseUnitInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { unitId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：学员-查询企业单位信息-详情接口
   * 描述：学员-查询企业单位信息-详情接口
   * @param unitId                  :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse
   * @date : 2022年6月9日 10:49:47
   * @param query 查询 graphql 语法文档
   * @param unitId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getEnterpriseUnitInfoInStudent(
    unitId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitInfoInStudent,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponse>> {
    return commonRequestApi<EnterpriseUnitInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { unitId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述 :学员-查询当前登录学员-详情接口
   * 描述：查询当前登录学员的详细信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse
   * @date : 2022/3/31 16:54
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudentInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentInfoInMyself,
    operation?: string
  ): Promise<Response<StudentInfoResponse>> {
    return commonRequestApi<StudentInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员-查询当前登录学员的证书信息-列表接口
   * @param page
   * @param request
   * @param dataFetchingEnvironment
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCertificateInfoInStudentMyself(
    params: { page?: Page; request?: CertificateQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCertificateInfoInStudentMyself,
    operation?: string
  ): Promise<Response<CertificateResponsePage>> {
    return commonRequestApi<CertificateResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
