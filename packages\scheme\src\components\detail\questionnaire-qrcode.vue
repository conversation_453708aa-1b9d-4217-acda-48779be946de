<template>
  <el-drawer title="问卷二维码" :visible.sync="openDialog" size="480px" custom-class="m-drawer" @open="open">
    <div class="drawer-bd">
      <div class="m-view-qrcode">
        <div class="item">
          <div class="content" :id="questionnaire.id">
            <div class="tit">{{ questionnaire.questionnaireName }}</div>
            <div class="code"><img :src="qrCode" class="u-qr-code" /></div>
          </div>
          <div class="op">
            <el-button type="primary" round size="medium" @click="downloadPoster()">保存至本地</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button type="primary" @click="openDialog = false">关闭</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, PropSync, Vue } from 'vue-property-decorator'
  import * as htmlToImage from 'html-to-image'
  import CryptUtil from '@api/service/common/crypt/CryptUtil'
  import QRCode from 'qrcode'
  import { EnterTypeEnums } from '@api/service/common/auth-status-tool/enums/EnterTypeEnums'
  import QuestionnaireConfigDetail from '@api/service/common/scheme/model/QuestionnaireConfigDetail'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import { QuestionnaireAppliedRangeTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'

  /**
   * 抄韦哥的
   */
  @Component
  export default class extends Vue {
    @PropSync('showDrawer', { type: Boolean, default: false }) openDialog: boolean
    @Prop({ type: QuestionnaireConfigDetail, default: () => new QuestionnaireConfigDetail() })
    questionnaire: QuestionnaireConfigDetail
    @Prop({ type: String, default: '' })
    schemeId: string
    @Prop({ type: String, default: TrainingModeEnum.mixed })
    trainingMode: TrainingModeEnum
    // qrCode
    qrCode = ''
    loading = false
    loading2 = false

    open() {
      this.getQrcode()
    }
    // 下载二维码
    downloadPoster() {
      this.domToPic(this.questionnaire.id, {
        width: 1140,
        height: 1300,
        name: this.questionnaire.questionnaireName || '问卷二维码'
      })
    }
    // 下载方法
    domToPic(domId: string, config = { width: 2105, height: 3035, name: `问卷二维码` }) {
      this.loading2 = true
      const node = document.getElementById(domId)
      htmlToImage
        .toPng(node)
        .then((pngUrl: string) => {
          const img = new Image()
          img.src = pngUrl
          img.onload = () => {
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')
            canvas.width = config.width
            canvas.height = config.height
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
            canvas.toBlob((blob: Blob) => {
              const url = window.URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.href = url
              link.download = `${config.name}.jpeg`
              document.body.appendChild(link)
              link.click()
              window.URL.revokeObjectURL(url)
            })
          }
        })
        .catch(function (error: any) {
          console.error('oops, something went wrong!', error)
        })
        .finally(() => {
          this.loading2 = false
        })
    }

    // 获取二维码
    async getQrcode() {
      this.loading = true
      // 问卷入参 方案id 期别id 目标路径：/pages/training/question-naire
      const origin = window.location.origin
      const accessPath =
        origin + `/h5/#/pages/transfer/qr_transfer?entryType=${EnterTypeEnums.questionnaire}&nextAddress=`
      const storageURL = await CryptUtil.encryptStr(
        `/pages/training/question/cover?schemeId=${this.schemeId}&periodId=${
          this.questionnaire.curIssueId || ''
        }&questionnaireId=${this.questionnaire.configId}&isNeedSub=true&listTrainingMode=${
          this.trainingMode
        }&uniqueKey=${this.questionnaire.uniqueKey || ''}${
          this.questionnaire.appliedRangeType != QuestionnaireAppliedRangeTypeEnum.per_issue
            ? `&learningId=${this.questionnaire.id}`
            : ''
        }`
      )
      QRCode.toDataURL(accessPath + storageURL)
        .then((res) => {
          this.qrCode = res
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
</script>
