import { Response } from '@hbfe/common'
import QualifiedCoursewareVo from '@api/service/management/train-class/mutation/vo/QualifiedCoursewareVo'
import MsLearningScheme, { ApplyAutoLearningTokenRequest } from '@api/ms-gateway/ms-learningscheme-v1'
import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
import MsChooseCourseLearningScene from '@api/ms-gateway/ms-choosecourselearningscene-v1'
import MsAutonomousCourseLearningScene from '@api/ms-gateway/ms-autonomouscourselearningscene-v1'
import MsStudentCourseLearning from '@api/ms-gateway/ms-studentcourselearning-v1'
import ApplyQualifiedResponseVo from '@api/service/management/train-class/mutation/vo/ApplyQualifiedResponseVo'

/**
 * @description
 */
class MutationQualifiedCourseware {
  /**
   * 合格参数
   */
  qualifiedParams: QualifiedCoursewareVo = new QualifiedCoursewareVo()

  /**
   * 一键合格申请结果
   */
  applyResult: Response<ApplyQualifiedResponseVo> = new Response<ApplyQualifiedResponseVo>()

  /**
   * 课件一键合格
   */
  async doQualifiedCourseware() {
    const requestSuccess = this.applyResult.status?.isSuccess()
    // 1.申请学员学习token
    const studentStudyToken = await this.applyAutoLearningTokenForManage()
    if (!requestSuccess || !studentStudyToken) return
    // 2.申请一键学习token
    const oneKeyStudyToken = await this.applyCoursewareImmediatelyLearning(studentStudyToken)
    if (!requestSuccess || !oneKeyStudyToken) return
    // 3.提交一键学习
    await this.commitImmediatelyCoursewareLearning(oneKeyStudyToken)
  }

  /**
   * 1.申请学员学习token
   */
  private async applyAutoLearningTokenForManage(): Promise<string> {
    let result = ''
    const request = new ApplyAutoLearningTokenRequest()
    request.qualificationId = this.qualifiedParams.qualificationId
    request.learningId = this.qualifiedParams.learningId
    const response = await MsLearningScheme.applyAutoLearningTokenForManage(request)
    // 网络不成功
    if (!response.status?.isSuccess()) {
      this.applyResult.status = response.status
      return
    }
    result = response.data?.token
    return result
  }

  /**
   * 2.申请课件一键学习token
   * @param {string} studentLearningToken - 学员学习token
   * @return 一键学习token
   */
  private async applyCoursewareImmediatelyLearning(studentLearningToken: string): Promise<string> {
    let response: any,
      result = ''
    const params = {
      studentLearningToken,
      studentCourseId: this.qualifiedParams.courseId,
      coursewareId: this.qualifiedParams.coursewareId
    }
    if (this.qualifiedParams.schemeType === TrainClassSchemeEnum.Choose_Course_Learning) {
      /** 选课规则 */
      response = await MsChooseCourseLearningScene.applyCoursewareImmediatelyLearning(params)
    } else {
      /** 自主选课 */
      response = await MsAutonomousCourseLearningScene.applyCoursewareImmediatelyLearning(params)
    }
    // 网络不成功
    if (!response.status?.isSuccess()) {
      this.applyResult.status = response.status
      return
    }
    // 申请token不成功
    const applyResultCode = response.data?.applyResult?.code
    const applyResultMessage = response.data?.applyResult?.message
    if (applyResultCode !== '200') {
      const applyResult = new ApplyQualifiedResponseVo()
      applyResult.applyResultCode = applyResultCode
      applyResult.applyResultMessage = applyResultMessage
      this.applyResult.data = applyResult
      return
    }
    result = response.data?.token
    return result
  }

  /**
   * 3.提交课件一键学习token
   * @param {string} oneKeyStudyToken - 学员课程一键学习token
   */
  private async commitImmediatelyCoursewareLearning(oneKeyStudyToken: string) {
    const response = await MsStudentCourseLearning.commitImmediatelyCoursewareLearning(oneKeyStudyToken)
    // 网络不成功
    if (!response.status?.isSuccess()) {
      this.applyResult.status = response.status
      return
    }
    this.applyResult.data = new ApplyQualifiedResponseVo()
    this.applyResult.data.applyResult = true
  }
}

export default MutationQualifiedCourseware
