import { ResponseStatus } from '@hbfe/common'

/*
 * @Description: 个人对账业务
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:32:55
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-19 10:32:17
 */
import ExportGateway, {
  OrderSortRequest,
  ReturnSortRequest
} from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import CheckAccountParam from '../query/vo/CheckAccountParam'
import fxnlQuery, {
  OrderSortRequest as OrderSortRequestv2,
  ReturnSortRequest as ReturnSortRequestv2
} from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import MutationCheckAccountBase from '@api/service/management/trade/single/checkAccount/mutation/MutationCheckAccountBase'

export default class MutationCheckAccount extends MutationCheckAccountBase {
  /**
   * 个人报名对账导出
   */
  async listExport(checkAccountParam: CheckAccountParam, sortRequest?: Array<OrderSortRequest>): Promise<boolean> {
    const request = CheckAccountParam.to(checkAccountParam)
    const { data } = await ExportGateway.exportReconciliationExcelInServicer({ request, sort: sortRequest })
    return data
  }
  /**
   * 个人报名对账导出（分销）
   */
  async listFxExport(checkAccountParam: CheckAccountParam, sortRequest?: Array<OrderSortRequestv2>): Promise<boolean> {
    const request = CheckAccountParam.to(checkAccountParam)
    const { data } = await fxnlQuery.exportReconciliationExcelInDistributor({ request, sort: sortRequest })
    return data
  }

  /**
   * 个人退款对账导出
   */
  async listReturnExport(
    checkAccountParam: CheckAccountParam,
    sortRequest?: Array<ReturnSortRequest>
  ): Promise<boolean> {
    const request = CheckAccountParam.toReturn(checkAccountParam)
    const { data } = await ExportGateway.exportReturnReconciliationExcelInServicer({ request, sort: sortRequest })
    return data
  }
  /**
   * 个人退款对账导出（分销）
   */
  async listFxReturnExport(
    checkAccountParam: CheckAccountParam,
    sortRequest?: Array<ReturnSortRequestv2>
  ): Promise<boolean> {
    const request = CheckAccountParam.toReturn(checkAccountParam)
    const { data } = await fxnlQuery.exportReturnReconciliationExcelInDistributor({ request, sort: sortRequest })
    return data
  }
}
