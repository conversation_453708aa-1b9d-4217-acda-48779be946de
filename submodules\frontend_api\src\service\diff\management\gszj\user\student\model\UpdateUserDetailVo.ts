import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
import TopicInformationVo from '@api/service/diff/management/gszj/user/student/model/TopicInformationVo'
import { UpdateStudentSystemDifferentiationRequest } from '@api/platform-gateway/platform-training-channel-user-v1'
import UpdateStudentRequestVo from '@api/service/management/user/mutation/student/vo/UpdateStudentRequestVo'

/*
 * 用户详情
 */
class UserDetailVoDiff extends UpdateStudentRequestVo {
  /**
   * 专题信息
   */
  topicInformationVo = new TopicInformationVo()

  to() {
    // const result = Object.assign(new UpdateStudentSystemDifferentiationRequest(), this.toDto())
    const result = new UpdateStudentSystemDifferentiationRequest()
    result.unitNature = this.topicInformationVo.natureWorkUnit
    result.staffingStatus = this.topicInformationVo.currentSituation
    result.isZjPosition = this.topicInformationVo.isOnTechnicalPost
    result.titleSeries = this.topicInformationVo.titleSeries
    result.titleProfessional = this.topicInformationVo.professionalTitle
    result.titleGrade = this.topicInformationVo.currentTitleLevel
    result.titleQualificationName = this.topicInformationVo.currentTitleQualification
    result.titleEffectiveRange = this.topicInformationVo.currentTitleValidRange
    result.highestEducationLevel = this.topicInformationVo.highestEducation
    result.userId = this.userId
    result.areaCode = '620300'
    return result
  }
}

export default UserDetailVoDiff
