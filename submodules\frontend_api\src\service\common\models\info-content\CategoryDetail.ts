/**
 *
 *
 * @author: eleven
 * @date: 2020/4/22
 */
export class CategoryDetail {
  /**
   * id
   */
  id: string
  /**
   * 父分类id
   */
  parentId: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 父分类名称
   */
  parentName: string
  /**
   * 分类说明
   */
  description: string
  /**
   * 状态 0-停用 1-启用
   */
  status: number
  /**
   * 是否内置
   */
  internal: boolean
  /**
   * 资讯类别类型
   @see com.fjhb.jskqcp.platform.service.news.NoticeCategoryEnum
   */
  categoryType: string
}
