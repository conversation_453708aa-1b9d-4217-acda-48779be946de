/*
 * @Description: 更新线下发票
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-22 09:30:44
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-22 09:50:16
 */

import { UpdateOfflineInvoiceRequest } from '@api/ms-gateway/ms-offlineinvoice-v1'
import OffLinePageInvoiceResponseVo from '../../query/vo/OffLinePageInvoiceResponseVo'
export default class OffLineInvoiceUpdateVo extends OffLinePageInvoiceResponseVo {
  //
  static to(offLinePageInvoiceResponseVo: OffLinePageInvoiceResponseVo) {
    const updateOfflineInvoiceRequest = new UpdateOfflineInvoiceRequest()
    updateOfflineInvoiceRequest.offlineInvoiceId = offLinePageInvoiceResponseVo.invoiceId
    updateOfflineInvoiceRequest.invoiceCategory = offLinePageInvoiceResponseVo.invoiceCategory
    updateOfflineInvoiceRequest.title = offLinePageInvoiceResponseVo.title
    updateOfflineInvoiceRequest.titleType = offLinePageInvoiceResponseVo.titleType
    updateOfflineInvoiceRequest.taxpayerNo = offLinePageInvoiceResponseVo.taxpayerNo
    updateOfflineInvoiceRequest.address = offLinePageInvoiceResponseVo.address
    updateOfflineInvoiceRequest.phone = offLinePageInvoiceResponseVo.rePhone
    updateOfflineInvoiceRequest.bankName = offLinePageInvoiceResponseVo.bankName
    updateOfflineInvoiceRequest.contactEmail = offLinePageInvoiceResponseVo.contactEmail
    updateOfflineInvoiceRequest.contactPhone = offLinePageInvoiceResponseVo.contactPhone
    updateOfflineInvoiceRequest.account = offLinePageInvoiceResponseVo.account
    updateOfflineInvoiceRequest.remark = offLinePageInvoiceResponseVo.remark
    return updateOfflineInvoiceRequest
  }
}
