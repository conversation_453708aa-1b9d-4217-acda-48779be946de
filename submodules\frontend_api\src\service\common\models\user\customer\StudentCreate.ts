import { RegisterType, SourceType, UniqueType } from '@api/service/common/models/user/enums'
import LoginAccountCreate from '@api/service/common/models/user/LoginAccountCreate'

class StudentCreate {
  /**
   * 姓名
   */
  name: string
  /**
   * 昵称
   */
  nickName: string
  /**
   * 唯一性类型
   */
  uniqueType: UniqueType = UniqueType.UNIQUE_TYPE_NONE
  /**
   * 唯一性值
   */
  uniqueData: string
  /**
   * 头像地址
   */
  displayPhotoUrl: string
  /**
   * 性别，男：1，女：2，中性：3，未知：4
   */
  sex: number
  /**
   * 出生年月
   */
  bornDay: Date
  /**
   * 手机号码
   */
  phoneNumber: string
  /**
   * 注册方式
   */
  registerType: RegisterType = RegisterType.PLATFORM
  /**
   * 注册来源
   */
  sourceType: SourceType = SourceType.HOME

  /**
   * 登录账号
   */
  loginAccounts: Array<LoginAccountCreate>

  /**
   * 注册来源单位id
   */
  registerUnitId?: string

  /**
   * 工作单位ID
   */
  workUnitIds: Array<string>
}

export default StudentCreate
