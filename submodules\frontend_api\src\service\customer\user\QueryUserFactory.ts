/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-05-20 08:48:32
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-25 09:33:54
 */
import QueryUserInfo from '@api/service/customer/user/query/QueryUserInfo'
import QueryLoginStatus from '@api/service/customer/user/query/QueryLoginStatus'
import QueryPhone from './query/RegisterBasic'
import RegisterBasic from './query/RegisterBasic'

/**
 * 查询工厂类
 */
class QueryUserFactory {
  /**
   * 获取登录状态
   * @returns
   */
  getLoginStatus() {
    return QueryLoginStatus
  }

  /**
   * 获取用户信息
   * @returns QueryUserInfo
   */
  getQueryUserInfo() {
    return QueryUserInfo
  }
  /**
   * 获取注册基础数据(校验码)
   */
  getRegisterBasic() {
    return new RegisterBasic()
  }
}

export default QueryUserFactory
