import CreateReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/CreateReceiveAccountVo'
import MutationReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/MutationReceiveAccountVo'
import { CreateReceiveAccountRequest, ReceiveAccountExtProperty } from '@api/ms-gateway/ms-trade-configuration-v1'

export default class CreateJSPayReceiveAccountVo extends CreateReceiveAccountVo {
  /**
   * 支付账号类型id
   * 支付宝:ALIPAY
   * 微信：WXPAY
   * 支付宝H5:ALIPAYH5
   * 微信H5：WXPAYH5
   */
  paymentChannelId = ''
  /**
   * 商户柜台代码
   */
  posId = ''
  /**
   * 分行代码
   */
  branchId = ''
  /**
   * 建行网银支付接口的公钥
   */
  jsPublicKey = ''
  /**
   * 建行的操作员账号不能为空
   */
  operator = ''
  /**
   * 建行操作员的登陆密码
   */
  password = ''
  /**
   * 是否使用防钓鱼,如果1表示使用防钓鱼接口,其他则不使用
   */
  phishing = 0
  /**
   * 小程序/公众号的 APPID 当前调起支付的小程序/公众号 APPID
   */
  jsSubAppid = ''
  /**
   * 文件证书路径
   */
  certFilePath = ''
  /**
   * 文件证书密码
   */
  certPassword = ''

  constructor(type: string) {
    super()
    this.paymentChannelId = type
  }

  from(mutationReceiveAccountVo: MutationReceiveAccountVo) {
    this.accountType = mutationReceiveAccountVo.accountType
    this.accountNo = mutationReceiveAccountVo.accountNo
    this.accountName = mutationReceiveAccountVo.accountName
    this.qrScanPrompt = mutationReceiveAccountVo.qrScanPrompt
    this.taxPayerId = mutationReceiveAccountVo.taxPayerId
    this.refundWay = mutationReceiveAccountVo.refundWay
    this.paymentChannelId = mutationReceiveAccountVo.paymentChannelId

    this.posId = mutationReceiveAccountVo.posId
    this.branchId = mutationReceiveAccountVo.branchId
    this.jsPublicKey = mutationReceiveAccountVo.jsPublicKey
    this.operator = mutationReceiveAccountVo.operator
    this.password = mutationReceiveAccountVo.password
    this.phishing = mutationReceiveAccountVo.phishing
    this.jsSubAppid = mutationReceiveAccountVo.jsSubAppid
    this.certFilePath = mutationReceiveAccountVo.certFilePath
    this.certPassword = mutationReceiveAccountVo.certPassword
  }

  to(): CreateReceiveAccountRequest {
    const createReceiveAccountRequest = new CreateReceiveAccountRequest()
    createReceiveAccountRequest.accountType = 1
    createReceiveAccountRequest.paymentChannelId = this.paymentChannelId
    createReceiveAccountRequest.accountNo = this.accountNo
    createReceiveAccountRequest.name = this.accountName
    createReceiveAccountRequest.qrScanPrompt = this.qrScanPrompt
    createReceiveAccountRequest.refundWay = this.refundWay
    createReceiveAccountRequest.taxPayerId = this.taxPayerId
    createReceiveAccountRequest.properties = new Array<ReceiveAccountExtProperty>()
    createReceiveAccountRequest.properties.push({ name: 'posId', value: this.posId })
    createReceiveAccountRequest.properties.push({ name: 'branchId', value: this.branchId })
    createReceiveAccountRequest.properties.push({ name: 'publicKey', value: this.jsPublicKey })
    createReceiveAccountRequest.properties.push({ name: 'operator', value: this.operator })
    createReceiveAccountRequest.properties.push({ name: 'password', value: this.password })
    createReceiveAccountRequest.properties.push({ name: 'phishing', value: this.phishing.toString() })
    createReceiveAccountRequest.properties.push({ name: 'subAppid', value: this.jsSubAppid })
    createReceiveAccountRequest.properties.push({ name: 'certFilePath', value: this.certFilePath })
    createReceiveAccountRequest.properties.push({ name: 'certPassword', value: this.certPassword })
    return createReceiveAccountRequest
  }
}
