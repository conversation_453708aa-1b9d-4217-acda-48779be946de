<template>
  <el-drawer title="当前方案所属的专题" :visible.sync="dialogShow" size="600px" custom-class="m-drawer">
    <div class="drawer-bd">
      <!--表格-->
      <el-table stripe :data="tableData" class="m-table" max-height="600px" v-loading="loading">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="专题名称" min-width="240" prop="trainingChannelName"> </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template v-slot="{ row }">
            <el-popconfirm
              confirm-button-text="确定移除"
              title="移除后，当前方案不在该专题中显示，确定移除？"
              icon="el-icon-info"
              icon-color="gray"
              @confirm="remove(row.trainingChannelId)"
            >
              <el-button type="text" :loading="removeLoading[row.trainingChannelId]" size="mini" slot="reference"
                >移除</el-button
              >
            </el-popconfirm>
            <el-button type="text" size="mini" @click="edit(row.trainingChannelId)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="f-mt10">
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
      </div>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button type="primary" @click="close">确 定</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import {
    CommodityTrainingChannelResponse,
    SchemeResourceResponse
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import QueryTrainClassDetailClass from '@api/service/management/train-class/query/QueryTrainClassDetailClass'
  import ThematicManagementList from '@api/service/management/thematic-management/ThematicManagementList'
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'

  @Component
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.getPageData, this.getPageData)
    }

    /**
     * 分页类
     * @type {UiPage}
     */
    page: UiPage
    /**
     * 表格数据
     * @type {any[]}
     */
    tableData: Array<CommodityTrainingChannelResponse> = []
    /**
     * 总数据
     * @type {any[]}
     */
    channelData: Array<CommodityTrainingChannelResponse> = []
    /**
     * 抽屉显隐
     * @type {boolean}
     */
    dialogShow = false
    /**
     * 商品id
     */
    commodityId = ''
    /**
     * 方案id
     */
    schemeId = ''
    /**
     * 加载中
     */
    loading = false
    // 移除loading
    removeLoading: { [key: string]: boolean } = {}
    /**
     * 查询总数据
     */
    async getCommodityDetail() {
      this.loading = true
      await new QueryTrainClassDetailClass()
        .getCommodityDetail(this.commodityId)
        .then(res => {
          this.schemeId = (res?.resource as SchemeResourceResponse)?.schemeId || ''
          this.channelData = res?.trainingChannels || []
        })
        .catch(e => {
          this.channelData = []
          this.tableData = []
          console.log(e)
        })
        .finally(() => {
          this.loading = false
        })
    }

    /**
     * 分页数据
     */
    async getPageData() {
      this.tableData = this.channelData.slice(
        (this.page.pageNo - 1) * this.page.pageSize,
        this.page.pageNo * this.page.pageSize
      )
      this.page.totalSize = this.channelData.length
      this.page.totalPageSize = Math.ceil(this.page.totalSize / this.page.pageSize)
    }
    /**
     * 打开抽屉
     */
    async open(commodityId: string) {
      this.commodityId = commodityId
      this.channelData = []
      this.tableData = []
      this.page.pageSize = 10
      this.dialogShow = true
      this.getCommodityDetail().then(() => {
        this.page.currentChange(1)
      })
    }

    /**
     * 移除专题
     */
    remove(topicId: string) {
      if (!topicId) return this.$message.error('专题id不存在')
      this.$set(this.removeLoading, topicId, true)
      new ThematicManagementList()
        .removeSchemeOfTopic(topicId, this.schemeId)
        .then(res => {
          //清洗可能存在延迟
          if (res.isSuccess()) {
            setTimeout(() => {
              // 刷新列表数据
              this.$emit('pageScheme')
              this.$message.success('移除成功')
              this.getCommodityDetail().then(() => {
                this.page.currentChange(1)
              })
            }, 200)
          } else {
            this.$message.error('移除失败')
          }
        })
        .catch(e => {
          console.log(e)
          this.$message.error('移除失败')
        })
        .finally(() => {
          this.removeLoading[topicId] = false
        })
    }

    /**
     * 编辑专题
     */
    async edit(trainingChannelId: string) {
      this.dialogShow = false
      this.$router.push(`/training/special-topics/manage/edit/${trainingChannelId}`)
    }

    /**
     * 关闭抽屉
     */
    close() {
      this.dialogShow = false
    }
  }
</script>
