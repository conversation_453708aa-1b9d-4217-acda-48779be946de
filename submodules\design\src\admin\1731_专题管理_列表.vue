<template>
  <el-main>
    <div class="f-p15 m-table-auto">
      <div class="f-mb15 m-btn-right">
        <el-button type="primary">批量更新方案展示</el-button><el-button type="primary">基础配置</el-button>
      </div>
      <el-tabs v-model="activeName2" type="card" class="m-tab-card">
        <el-tab-pane label="按专题" name="first">
          <el-card shadow="never" class="m-card f-mb15">
            <!--条件查询-->
            <el-row :gutter="16" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="专题名称">
                    <el-input v-model="input" clearable placeholder="请输入专题名称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="专题入口名称">
                    <el-input v-model="input" clearable placeholder="请输入专题入口名称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="专题类型">
                    <el-select v-model="select" clearable placeholder="请选择专题类型">
                      <el-option value="1"></el-option>
                      <el-option value="2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="行业">
                    <el-input v-model="input" clearable placeholder="请输入专题行业关键字" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="地区">
                    <el-cascader clearable filterable :options="cascader" placeholder="请选择专题地区"></el-cascader>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="所属单位">
                    <el-input v-model="input" clearable placeholder="请输入单位名称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="专题状态">
                    <el-select v-model="select" clearable placeholder="请选择专题状态">
                      <el-option value="启用"></el-option>
                      <el-option value="停用"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="显示在网校">
                    <el-select v-model="select" clearable placeholder="请选择是否显示在网校">
                      <el-option value="显示"></el-option>
                      <el-option value="不显示"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="编辑时间">
                    <el-date-picker
                      v-model="value1"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button type="primary">导出</el-button>
                    <el-button>重置</el-button>
                    <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                    <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table is-statistical">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="排序" min-width="65" align="center" fixed="left">
                <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
              </el-table-column>
              <el-table-column label="专题名称" min-width="220" fixed="left">
                <template>福州市专业人员培训平台</template>
              </el-table-column>
              <el-table-column label="专题入口名称" min-width="140">
                <template>
                  <el-tooltip placement="top" effect="light">
                    <div slot="content">
                      读取专题入口
                    </div>
                    <el-button type="text" class="f-to-three"><i class="f-c4">读取专题入口</i></el-button>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="专题类型" min-width="160">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-tooltip placement="top" effect="light">
                      <div slot="content">
                        <i class="f-c4"><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag>福建省</i>
                      </div>
                      <el-button type="text" class="f-to-three"
                        ><i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag>福建省</i
                        ></el-button
                      >
                    </el-tooltip>
                    <el-tooltip placement="top" effect="light">
                      <div slot="content">
                        <i class="f-c4"><el-tag type="success" size="mini" class="f-mr5">单位</el-tag>福建华博科技</i>
                      </div>
                      <el-button type="text" class="f-to-three"
                        ><i class="f-c4"
                          ><el-tag type="success" size="mini" class="f-mt5">单位</el-tag>福建华博科技</i
                        ></el-button
                      >
                    </el-tooltip>
                  </div>
                  <div v-else><el-tag type="warning" size="mini" class="f-mt5">行业</el-tag>工艺美术</div>
                </template>
              </el-table-column>
              <el-table-column label="显示在网校" min-width="110" align="center">
                <template>显示</template>
              </el-table-column>
              <el-table-column label="已配置方案数" min-width="120" align="center">
                <template><el-button type="text" size="mini">100</el-button></template>
              </el-table-column>
              <el-table-column label="状态" min-width="90">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="success" class="badge-status">启用</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="最新编辑时间" min-width="160">
                <template>2021-10-15 00:21:21</template>
              </el-table-column>
              <el-table-column label="操作" width="400" align="center" fixed="right">
                <template
                  ><el-button type="text">停用</el-button>
                  <el-button type="text">编辑</el-button>
                  <el-button type="text">日志</el-button>
                  <el-button type="text">web端链接</el-button>
                  <el-button type="text">H5二维码</el-button>
                  <el-button type="text">导出专题方案</el-button></template
                >
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="按方案" name="second">
          <el-card shadow="never" class="m-card f-mb15">
            <!--条件查询-->
            <el-row :gutter="16" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="年度">
                    <el-select v-model="select" clearable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="地区">
                    <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="行业">
                    <el-select v-model="select" clearable placeholder="请选择行业">
                      <el-option value="人社"></el-option>
                      <el-option value="建设"></el-option>
                      <el-option value="职业卫生"></el-option>
                      <el-option value="工勤"></el-option>
                      <el-option value="教师"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训形式">
                    <el-select v-model="select" clearable filterable placeholder="请选择培训形式">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训方案类型">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训方案名称">
                    <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="报名状态">
                    <el-select v-model="select" clearable filterable placeholder="请选择报名状态">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="是否归属专题">
                    <el-select v-model="select" clearable filterable placeholder="请选择是否归属专题">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>导出列表数据</el-button>
                    <el-button>重置</el-button>
                    <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                    <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="培训方案名称" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-tooltip placement="top" effect="light">
                      <div slot="content">
                        <i class="f-c4"
                          ><el-tag type="primary" effect="dark" size="mini" class="f-mr5">网授-培训班-选课规则</el-tag
                          >培训方案名称培训方案名称培训方案名称培训方案名称</i
                        >
                      </div>
                      <el-button type="text" class="f-to-three"
                        ><i class="f-c4"
                          ><el-tag type="primary" effect="dark" size="mini">网授-培训班-选课规则</el-tag
                          >培训方案名称培训方案名称培训方案名称培训方案名称</i
                        ></el-button
                      >
                    </el-tooltip>
                  </div>
                  <div v-else>
                    <el-tooltip placement="top" effect="light">
                      <div slot="content">
                        <i class="f-c4"
                          ><el-tag type="primary" effect="dark" size="mini" class="f-mr5">面网授-培训班-选课规则</el-tag
                          >培训方案名称培训方案名称培训方案名称培训方案名称</i
                        >
                      </div>
                      <el-button type="text" class="f-to-three"
                        ><i class="f-c4"
                          ><el-tag type="primary" effect="dark" size="mini">面网授-培训班-选课规则</el-tag
                          >培训方案名称培训方案名称培训方案名称培训方案名称</i
                        ></el-button
                      >
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="培训属性" min-width="240">
                <template>
                  <p>行业：行业行业</p>
                  <p>地区：为空，不展示</p>
                  <p>科目类型：科目类型</p>
                  <p>培训类别：培训类别</p>
                  <p>培训专业：培训专业培训专业</p>
                  <p>培训年度：2019</p>
                </template>
              </el-table-column>
              <el-table-column label="报名状态" min-width="140">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="info" class="badge-status">报名关闭</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">报名开启</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="归属专题数" min-width="100" align="center">
                <template>
                  <el-button type="text" size="mini">3</el-button>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-button type="text">编辑方案</el-button>
                  <el-button type="text">查看期别(3)</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
