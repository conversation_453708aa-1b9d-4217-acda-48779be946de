import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum ImportStatusEnum {
  /**
   * 成功
   */
  success = 1,
  /**
   * 失败
   */
  fail
}

class ImportStatus extends AbstractEnum<ImportStatusEnum> {
  static enum = ImportStatusEnum

  constructor(status?: ImportStatusEnum) {
    super()
    this.current = status
    this.map.set(ImportStatusEnum.success, '成功')
    this.map.set(ImportStatusEnum.fail, '失败')
  }
}

export default ImportStatus
