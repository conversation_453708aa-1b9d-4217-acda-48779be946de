<script lang="ts">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import BasicData from '@hbfe/jxjy-admin-customerService/src/diff/jxgx/personal/__components__/basic-data.vue'
  import StudyContent from '@hbfe/jxjy-admin-customerService/src/diff/jxgx/personal/__components__/study-content.vue'
  import StudyRecords from '@hbfe/jxjy-admin-customerService/src/diff/jxgx/personal/__components__/study-records.vue'
  import Personal from '@hbfe/jxjy-admin-customerService/src/personal/index.vue'

  @Component({
    components: { BasicData, StudyContent, StudyRecords }
  })
  export default class extends Personal {}
</script>
