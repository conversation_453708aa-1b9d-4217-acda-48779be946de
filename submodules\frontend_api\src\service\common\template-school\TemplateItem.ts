import { ClientTypeEnum } from '@api/service/common/template-school/enums/ClientTypeEnum'
import { IndustryTypeEnum } from '@api/service/common/template-school/enums/IndustryTypeEnum'
import { ColorEnum } from '@api/service/common/template-school/enums/ColorEnum'
export default class TemplateItem {
  /**
   * 模板id
   */
  id: string = undefined
  /**
   * 行业类型
   */
  industryType: IndustryTypeEnum = undefined
  /**
   * 客户端类型
   */
  clientType: ClientTypeEnum = undefined
  /**
   * 轮播图尺寸-[宽,高]
   */
  bannerSize?: Array<number> = new Array<number>()
  /**
   * 浏览器图标尺寸-[宽,高]
   */
  iconSize?: Array<number> = new Array<number>()
  /**
   * 友情链接尺寸-[宽,高]
   */
  linksSize?: Array<number> = new Array<number>()
  /**
   * 客服电话尺寸-[宽,高]
   */
  customerPhoneSize?: Array<number> = new Array<number>()
  /**
   * 集体报名尺寸-[宽,高]
   */
  groupRegistrationSize?: Array<number> = new Array<number>()
  /**
   * 门户logo尺寸-[宽,高]
   */
  logoSize?: Array<number> = new Array<number>()
  /**
   * 移动端二维码尺寸-[宽,高]
   */
  H5QRCodeSize?: Array<number> = new Array<number>()
  /**
   * 培训流程图片尺寸-[宽,高]
   */
  trainingProcessSize?: Array<number> = new Array<number>()
  /**
   * 可选色系
   */
  colorSchemes = new Array<ColorEnum>()
  /**
   * 预览路径
   */
  reviewPath?: string = undefined
  /**
   * 当前色系
   */
  currentColor?: string = undefined
  /**
   * 单入口线上集体报名尺寸
   */
  singleOnlineCollectSize?: Array<number> = new Array<number>()
  /**
   * 双入口线上集体报名尺寸
   */
  doubleOnlineCollectSize?: Array<number> = new Array<number>()
  /**
   * 单入口线下集体报名尺寸
   */
  singleOfflineCollectSize?: Array<number> = new Array<number>()
  /**
   * 双入口线下集体报名尺寸
   */
  doubleOfflineCollectSize?: Array<number> = new Array<number>()
  /**
   *   企业微信客服图片尺寸
   */
  wechatCustomerServiceSize?: Array<number> = new Array<number>()
  /**
   * 双入口线上示例图片
   */
  doubleOnlineExampleImgSrc?: string = undefined
  /**
   * 单入口线上示例图片
   */
  singleOnlineExampleImgSrc?: string = undefined
  /**
   * 双入口线下示例图片
   */
  doubleOfflineExampleImgSrc?: string = undefined
  /**
   * 单入口线下示例图片
   */
  singleOfflineExampleImgSrc?: string = undefined
}
