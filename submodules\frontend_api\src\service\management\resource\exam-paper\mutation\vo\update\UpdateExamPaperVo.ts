import {
  AutomaticPublishPatternRequest,
  LibraryQuestionScopeSettingRequest,
  PaperPublishConfigureCreateRequest,
  PaperPublishConfigureUpdateRequest
} from '@api/ms-gateway/ms-examextraction-v1'
import ExamPublishPattern from '../common/ExamPublishPattern'
import AutomaticExamPaperVo from '../common/AutomaticExamPaperVo'
import { PaperPublishConfigureResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import ExamLibrary from '../common/ExamLibrary'
class UpdateExamPaperVo<
  T extends ExamPublishPattern = AutomaticExamPaperVo
> extends PaperPublishConfigureUpdateRequest {
  /**
   * 出卷配置id
   */
  id = '-1'
  /**
   * 出卷配置名称
   */
  name?: string = ''
  /**
   * 适用范围 用于筛选自定义的分类
   */
  usageScope?: number = 2
  /**
   * 出卷模式
   */
  declare publishPattern: T
  /**
   * 分类id
   */
  paperPublishConfigureCategoryId?: string = '-1'
  /*
    分类名称
  */
  paperPublishConfigureCategoryName = ''
  /**
   * 是否启用 1-启用    2-停用
   */
  status = 1

  /**
   * 是否是草稿    1-是 2-不是
   */
  isDraft = 2

  /**
   * @description: 初始化参数
   * @param {object} publishPatternConstructor: T的构造函数（把类传进来
   */
  constructor(publishPatternConstructor: { new (): T }) {
    super()
    this.publishPattern = new publishPatternConstructor()
  }

  // 给Vo模型赋值
  from(paper: PaperPublishConfigureResponse, list: Array<ExamLibrary>) {
    this.id = paper.id
    this.name = paper.name
    this.paperPublishConfigureCategoryId = paper.paperPublishConfigureCategory?.id
    this.paperPublishConfigureCategoryName = paper.paperPublishConfigureCategory?.name
    this.status = paper.status
    this.usageScope = paper.usageScope
    this.isDraft = paper.isDraft
    this.publishPattern.from(paper.publishPattern, list)
  }

  // 转换Dto
  toDto() {
    const paramsDto = new PaperPublishConfigureUpdateRequest()
    paramsDto.id = this.id
    paramsDto.status = this.status
    paramsDto.name = this.name
    paramsDto.usageScope = this.usageScope
    paramsDto.paperPublishConfigureCategoryId = this.paperPublishConfigureCategoryId
    paramsDto.isDraft = this.isDraft
    paramsDto.publishPattern = new AutomaticPublishPatternRequest()
    this.publishPattern.toDto(paramsDto.publishPattern)
    const questionExtractRule = (paramsDto.publishPattern as AutomaticPublishPatternRequest).questionExtractRule
    questionExtractRule.questionExtracts.forEach(item => {
      item.questionScopes.forEach(subItem => {
        if (subItem.type == 1) {
          ;(subItem as LibraryQuestionScopeSettingRequest).libraryIds =
            (questionExtractRule.questionScopes[0] as LibraryQuestionScopeSettingRequest).libraryIds || []
        }
      })
    })
    return paramsDto
  }

  // 转换Dto
  toCopyDto() {
    const paramsDto = new PaperPublishConfigureCreateRequest()
    paramsDto.name = this.name + '_复制'
    paramsDto.usageScope = this.usageScope
    paramsDto.paperPublishConfigureCategoryId = this.paperPublishConfigureCategoryId
    paramsDto.isDraft = this.isDraft
    paramsDto.publishPattern = new AutomaticPublishPatternRequest()
    this.publishPattern.toDto(paramsDto.publishPattern)
    return paramsDto
  }
}
export default UpdateExamPaperVo
