import QueryStudentLearningList from '@api/service/management/statisticalReport/query/QueryStudentLearningList'
import {
  StudentSchemeLearningRequestVo,
  SyncResultEnmu
} from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
import exportMsgateway from '@api/diff-gateway/zjzj-data-export-gateway-backstage'
import UserModule from '@api/service/management/user/UserModule'
class ExportDockingdata extends QueryStudentLearningList {
  /**
   * 导出对接数据
   */
  async exportDockingDiff(param: StudentSchemeLearningRequestVo) {
    try {
      param = await this.initExportParams(param)
      const res = await exportMsgateway.exportStudentSchemeLearningIntegrationDataExcelInServicer(param)
      return res
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，exportExcel',
        e
      )
    }
  }
}
export default ExportDockingdata
