import roleGateWay, { SecurityObjectGroupDto } from '@api/ms-gateway/ms-role-v1'
export class QuerySecurityGroupByRoleId {
  /*
   *  根据roleId获取安全对象组
   * */
  async getPermissionByRoleId(roleId: string) {
    try {
      console.log('roleId参数=', roleId)
      const res = await roleGateWay.getPermissionByRoleId(roleId)
      let tmpArr: SecurityObjectGroupDto[] = []

      if (res.status.isSuccess()) {
        tmpArr = res.data
      }

      console.log('调用了getPermissionByRoleId方法，返回值=', tmpArr)
      return tmpArr
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/query/QuerySecurityGroupByRoleId.ts所处方法，getPermissionByRoleId',
        e
      )
    }
  }
}
