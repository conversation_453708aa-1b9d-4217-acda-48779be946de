import { Response } from '@hbfe/common'
import msTradeConfigurationV1, { PaymentChannelIdResponse } from '@api/ms-gateway/ms-trade-configuration-v1'
import CreateReceiveAccountVo from './vo/CreateReceiveAccountVo'
import CreateAliPayReceiveAccountVo from './vo/CreateAliPayReceiveAccountVo'
import CreateWXPayReceiveAccountVo from './vo/CreateWXPayReceiveAccountVo'
import { AccountTypeEunm } from '@api/service/common/enums/trade-configuration/AccountType'
import CreateOfflineReceiveAccountVo from './vo/CreateOfflineReceiveAccountVo'
import msTradeQueryFrontGatewayTradeQueryBackstage from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import TaxPayer from './vo/TaxPayer'
import CreateXYPayReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/CreateXYPayReceiveAccountVo'
import CreateJSPayReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/CreateJSPayReceiveAccountVo'
import CreateWFTPayReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/CreateWFTPayReceiveAccountVo'
import CreateXDLPayReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/CreateXDLPayReceiveAccountVo'
import { PayAccountTypeEnum } from '@api/service/management/trade-info-config/enums/PayAccountTypeEnum'

class MutationCreateReceiveAccount {
  createReceiveAccount = new CreateReceiveAccountVo()

  /**
   * 获取支付账号类型
   * @returns
   */
  async getPaymentChannelIdList(): Promise<Array<PaymentChannelIdResponse>> {
    const msRes = await msTradeConfigurationV1.getPaymentChannelIdList()
    if (msRes.status.isSuccess()) {
      return msRes.data
    }
    return new Array<PaymentChannelIdResponse>()
  }

  /**
   * 获取纳税人识别号列表
   * @returns
   */
  async getTaxPayerIdList(): Promise<Array<TaxPayer>> {
    const msRes = await msTradeQueryFrontGatewayTradeQueryBackstage.listTaxpayerInServicer()
    if (msRes.status.isSuccess()) {
      const list = msRes.data?.map((item) => {
        const taxPayer = new TaxPayer()
        taxPayer.id = item.id
        taxPayer.accountNo = item.taxpayerNo
        taxPayer.accountName = item.name
        return taxPayer
      })
      return list
    }
    return new Array<TaxPayer>()
  }

  /**
   * 创建收款账户
   * @returns
   */
  async doCreate(): Promise<Response<void>> {
    const msRes = await msTradeConfigurationV1.createReceiveAccount(this.createReceiveAccount.to())
    return msRes
  }

  /**
   * 根据线上、线下，支付账号类型进行创建类的初始化
   * @param accountType
   * @param paymentChannelId
   */
  createVoByPaymentChannelType(accountType: AccountTypeEunm, paymentChannelId?: string) {
    const PaymentChannelTtype = (type: string) => {
      if (type.indexOf(PayAccountTypeEnum.ALIPAY) > -1) {
        return new CreateAliPayReceiveAccountVo(type)
      }
      if (type.indexOf(PayAccountTypeEnum.WXPAY) > -1) {
        return new CreateWXPayReceiveAccountVo(type)
      }
      if (type.indexOf(PayAccountTypeEnum.CIB_PAY) > -1) {
        return new CreateXYPayReceiveAccountVo(type)
      }
      if (type.indexOf(PayAccountTypeEnum.CCB_PAY) > -1) {
        return new CreateJSPayReceiveAccountVo(type)
      }
      if (type.indexOf(PayAccountTypeEnum.SWIFT_PASS_PAY) > -1) {
        return new CreateWFTPayReceiveAccountVo(type)
      }
      if (type.indexOf(PayAccountTypeEnum.NEW_LAND_PAY) > -1) {
        return new CreateXDLPayReceiveAccountVo(type)
      }
    }
    if (accountType === AccountTypeEunm.OFFLINE) {
      this.createReceiveAccount = new CreateOfflineReceiveAccountVo()
      this.createReceiveAccount.accountType = accountType
    } else {
      this.createReceiveAccount = PaymentChannelTtype(paymentChannelId)
      this.createReceiveAccount.accountType = AccountTypeEunm.ONLINE
    }
  }
}
export default MutationCreateReceiveAccount
