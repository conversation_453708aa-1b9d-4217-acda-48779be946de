import { QuestionnaireUseRangeEnum } from '@api/service/management/train-class/offlinePart/enum/QuestionnaireUseRangeEnum'
import DateScope from '@api/service/common/models/DateScope'
import { QuestionnaireSubmitStatusEnum } from '@api/service/management/train-class/offlinePart/enum/QuestionnaireSubmitStatusEnum'
import { SurveyQuestionnaireResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'

export default class StudentQuestionnaireItem {
  /**
   * 问卷名称
   */
  questionnaireName: string = undefined

  /**
   * 使用范围
   */
  questionnaireRange: QuestionnaireUseRangeEnum = undefined

  /**
   * 是否纳入考核
   */
  inAssessment: boolean = undefined

  /**
   * 是否强制完成
   */
  isForced: boolean = undefined

  /**
   * 开放时间
   */
  openTime: DateScope = new DateScope()

  /**
   * 问卷提交时间
   */
  submitTime: string = undefined

  /**
   * 提交状态
   */
  submitStatus: QuestionnaireSubmitStatusEnum = undefined

  /**
   * 启用状态
   */
  enable: boolean = undefined

  /**
   * 期别Id
   */
  issueId = ''

  /**
   * 问卷id
   */
  questionnaireId = ''
  /**
   * 答卷id
   */
  answerPaperId = ''
  /**
   * 后端枚举转换
   * 0-SCHEME 1-ONLINECOURSES 2-TRAININGSTAGE
   * @private
   */
  private static backendToEnumMap: Record<number, QuestionnaireUseRangeEnum> = {
    0: QuestionnaireUseRangeEnum.scheme,
    1: QuestionnaireUseRangeEnum.online,
    2: QuestionnaireUseRangeEnum.period,
    3: QuestionnaireUseRangeEnum.all_period
  }

  /**
   * 模型转换
   */
  static from(DTO: SurveyQuestionnaireResponse) {
    // 问卷配置信息
    const questionBasicInfo = DTO?.surveyInformationResponse
    // 用户答题信息
    const basicInfoOfUser = DTO?.basicInformationofUserResponse
    const vo = new StudentQuestionnaireItem()
    // 问卷名称
    vo.questionnaireName = questionBasicInfo?.questionnaireName
    // 是否纳入考核
    vo.inAssessment = questionBasicInfo?.includedInAssessment
    // 是否强制完成
    vo.isForced = questionBasicInfo?.forceToComplete
    // 应用范围
    vo.questionnaireRange = StudentQuestionnaireItem.backendToEnumMap[questionBasicInfo?.usedRange]
    // 问卷启停用状态
    vo.enable = questionBasicInfo?.status === 1
    const voDataScope = new DateScope()
    voDataScope.begin = questionBasicInfo?.questionnaireStartTime
    voDataScope.end = questionBasicInfo?.questionnaireEndTime
    // 开放时间
    vo.openTime = voDataScope
    // 答题时间取用户答题时间
    vo.submitTime = basicInfoOfUser?.endAnswerTime
    // 提交状态
    vo.submitStatus =
      basicInfoOfUser?.answerStatus === 2
        ? QuestionnaireSubmitStatusEnum.submit
        : QuestionnaireSubmitStatusEnum.notSubmit
    // 期别id
    vo.issueId = basicInfoOfUser?.issueId
    // 问卷id
    vo.questionnaireId = questionBasicInfo.questionnaireId
    // 答卷id
    vo.answerPaperId = basicInfoOfUser?.answerPaperId
    return vo
  }
}
