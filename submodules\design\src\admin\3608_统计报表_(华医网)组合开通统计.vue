<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
        <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="分销推广">
                <el-select v-model="select" clearable filterable placeholder="请选择是否为分销推广订单">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="分销商">
                <el-select v-model="select" clearable filterable placeholder="请选择分销商">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="推广门户简称">
                <el-input v-model="input" clearable placeholder="请输入分销商推广门户简称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="报名时间">
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="起始时间"
                  end-placeholder="结束时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="专题">
                <el-select v-model="select" clearable filterable placeholder="请选择订单是否来源专题">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="专题名称">
                <el-input v-model="input" clearable placeholder="请输入专题进行查询" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="">
                <el-checkbox label="查看非门户推广数据" name="type"></el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>导出列表数据</el-button>
                <el-button>重置</el-button>
                <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--操作栏-->
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert f-clear">
            <div class="f-c6 f-fl">
              搜索结果合计：当前净开通 <span class="f-fb f-co">8</span> 人次，成交总额
              <span class="f-fb f-co">¥ 99</span>
            </div>
          </el-alert>
        </div>
        <!--表格-->
        <el-table
          stripe
          :data="tableData"
          border
          show-summary
          :span-method="arraySpanMethod"
          class="m-table is-statistical f-mt10"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="年度" min-width="80" fixed="left">
            <template>2025年</template>
          </el-table-column>
          <el-table-column label="培训方案" min-width="240" fixed="left">
            <template>
              <p>公需加专业的班级</p>
              <el-tag>与华医网合作</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="方案属性" min-width="200">
            <template>
              <p>行业：人社行业</p>
              <p>地区：福建省/省直</p>
              <p>科目类型：公需科目</p>
              <p>培训专业：卫生</p>
              <p>培训年度：2025</p>
            </template>
          </el-table-column>
          <el-table-column label="合计" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template>26252</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template>26</template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="90" align="right">
              <template>26</template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="90" align="right">
              <template>26</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template>26</template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="个人缴费" header-align="center">
            <el-table-column label="线上支付" header-align="center">
              <el-table-column label="开通" min-width="90" align="right">
                <template>26252</template>
              </el-table-column>
              <el-table-column label="退班" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换入(换班)" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换出(换班)" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="净开通" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
            </el-table-column>
          </el-table-column>
          <el-table-column label="集体报名" header-align="center">
            <el-table-column label="线上支付" header-align="center">
              <el-table-column label="开通" min-width="90" align="right">
                <template>26252</template>
              </el-table-column>
              <el-table-column label="退班" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换入(换班)" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换出(换班)" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="净开通" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="线下支付" header-align="center">
              <el-table-column label="开通" min-width="90" align="right">
                <template>26252</template>
              </el-table-column>
              <el-table-column label="退班" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换入(换班)" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换出(换班)" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="净开通" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
            </el-table-column>
          </el-table-column>
          <el-table-column label="导入开通" header-align="center">
            <el-table-column label="线下支付" header-align="center">
              <el-table-column label="开通" min-width="90" align="right">
                <template>26252</template>
              </el-table-column>
              <el-table-column label="退班" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换入(换班)" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换出(换班)" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="净开通" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-pagination
          background
          class="f-mt15 f-tr"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage4"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
        >
        </el-pagination>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      },
      arraySpanMethod({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 1) {
          if (rowIndex === 0) {
            return [2, 1]
          } else if (rowIndex === 1) {
            return [0, 0]
          }
        }
        if (columnIndex === 2) {
          if (rowIndex === 0) {
            return [2, 1]
          } else if (rowIndex === 1) {
            return [0, 0]
          }
        }
      }
    }
  }
</script>
