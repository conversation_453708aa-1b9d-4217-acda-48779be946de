import CoursewareSupplierListDetail from '@api/service/management/authority/service-provider/query/vo/CoursewareSupplierListDetail'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { Page, UiPage } from '@hbfe/common'
import BasicDataQueryBackstage, {
  AccountRequest,
  AdminUserRequest,
  AuthenticationRequest,
  CoursewareSupplierRequest,
  RoleRequest,
  ServicerAdminBusinessOwnerRequest,
  ServicerAdminQueryRequest,
  ServicerBaseRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

export class ProviderAdminQueryRequest extends ServicerAdminQueryRequest {
  constructor() {
    super()
    this.businessOwnerInfo = new ServicerAdminBusinessOwnerRequest()
    this.account = new AccountRequest()
    this.user = new AdminUserRequest()
    this.authentication = new AuthenticationRequest()
    this.role = new RoleRequest()
  }
}

/**
 * 查询服务商
 */
class QueryServiceProvider {
  coursewareSupplierList: CoursewareSupplierListDetail[] = []

  /**
   * 查询课件供应商列表-筛选项模糊查询
   */
  async queryCoursewareSupplierListIndistinct(): Promise<CoursewareSupplierListDetail[]> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    let result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursewareSupplierInSubProject(page)
    if (result.data.totalSize) {
      page.pageSize = result.data.totalSize
      result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursewareSupplierInSubProject(page)
    }
    const temp = new Array(10).fill('')
    // this.coursewareSupplierList = temp.map(CoursewareSupplierListDetail.from)
    // this.coursewareSupplierList = result.data.currentPageData.map(CoursewareSupplierListDetail.from)
    return this.coursewareSupplierList
  }

  /**
   * 查询课件供应商列表
   */
  async queryCoursewareSupplierList(
    page: Page,
    request?: ProviderAdminQueryRequest
  ): Promise<CoursewareSupplierListDetail[]> {
    const providerIds: string[] = []

    const response = await BasicDataQueryBackstage.pageCoursewareSupplierAdminInfoInSubProject({ page, request })

    if (!response.status.isSuccess() && response.status.code !== 200) {
      console.error('获取查询课件供应商管理员接口报错', response)
      return Promise.reject(response)
    }
    response.data.currentPageData.forEach(item => {
      item.businessOwnerInfo.servicerList.map(res => {
        if (res.servicerType === 560) {
          providerIds.push(res.servicerId)
        }
      })
    })

    this.coursewareSupplierList = CoursewareSupplierListDetail.from(response.data.currentPageData)

    page.totalSize = response.data.totalSize
    page.totalPageSize = response.data.totalPageSize
    return this.coursewareSupplierList
  }

  /**
   * 查询课件供应商By ID
   * @param id 课件供应商ID
   */
  async serchProvider(id: string): Promise<CoursewareSupplierListDetail> {
    if (this.coursewareSupplierList.length) {
      return this.coursewareSupplierList.find(res => res.coursewareId === id)
    }
    const page = new Page()
    const request = new ProviderAdminQueryRequest()
    request.businessOwnerInfo = new ServicerAdminBusinessOwnerRequest()
    request.businessOwnerInfo.servicerIdList = [id]
    await this.queryCoursewareSupplierList(page, request)
    return this.coursewareSupplierList.find(res => res.coursewareId === id)
  }

  /**
   * 查询课件供应商By 用户ID
   * @param id 课件供应商ID
   */
  async serchProviderByUserId(id: string): Promise<CoursewareSupplierListDetail> {
    if (this.coursewareSupplierList.length) {
      return this.coursewareSupplierList.find(res => res.userId === id)
    }
    const page = new Page()
    const request = new ProviderAdminQueryRequest()
    request.user = new AdminUserRequest()
    request.user.userIdList = [id]
    await this.queryCoursewareSupplierList(page, request)
    return this.coursewareSupplierList.find(res => res.userId === id)
  }

  /**
   * 查询课件供应商BY name 筛选项用
   * @param name
   */
  async queryProviderAll() {
    const page = new Page(1, 1)
    const request = new CoursewareSupplierRequest()
    request.servicerBase = new ServicerBaseRequest()
    request.servicerBase.servicerNameMatchType = 1
    let response = await BasicDataQueryBackstage.pageCoursewareSupplierInfoInSubProject({ page, request })
    if (response.status.code === 200 && response.status.isSuccess()) {
      page.pageSize = response.data.totalSize
    }
    response = await BasicDataQueryBackstage.pageCoursewareSupplierInfoInSubProject({ page, request })

    const providerIds = response.data.currentPageData.map(res => res.servicerBase.servicerId)
    const adminRequest = new ServicerAdminQueryRequest()
    adminRequest.businessOwnerInfo = new ServicerAdminBusinessOwnerRequest()
    adminRequest.businessOwnerInfo.servicerIdList = providerIds
    const adminResponse = await BasicDataQueryBackstage.pageCoursewareSupplierAdminInfoInSubProject({
      page,
      request: adminRequest
    })
    const temp = adminResponse.data.currentPageData.map(res => {
      return response.data.currentPageData.find(
        item =>
          item.servicerBase.servicerId ===
          res.businessOwnerInfo.servicerList.find(provider => provider.servicerType === 560)?.servicerId
      )
    })
    return temp
  }
}

export default QueryServiceProvider
