<template>
  <el-card shadow="never" class="m-card f-mb15" v-if="$hasPermission('banner')" desc="轮播图设置" actions="created">
    <template v-if="$hasPermission('editBanner')" desc="轮播图设置（编辑）" actions="addRotation">
      <el-button type="primary" icon="el-icon-plus" class="f-mb20" @click="addRotation">添加轮播图</el-button>
    </template>
    <!--表格-->
    <el-table stripe :data="WebBannerList" ref="dragWebTable" max-height="500px" class="m-table">
      <el-table-column :key="flag" type="index" label="No." width="60" align="center"></el-table-column>
      <el-table-column label="排序" min-width="80" align="center">
        <template v-if="$hasPermission('editBanner')" desc="轮播图设置（编辑）" actions="dragElement"
          ><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i
        ></template>
      </el-table-column>
      <el-table-column label="轮播图" min-width="450">
        <template slot-scope="scope">
          <el-image class="web-banner" :src="scope.row.url" :preview-src-list="[scope.row.url]" />
        </template>
      </el-table-column>
      <el-table-column label="链接地址" min-width="280">
        <template slot-scope="scope">{{ scope.row.link || '-' }}</template>
      </el-table-column>
      <el-table-column label="创建时间" min-width="180">
        <template slot-scope="scope">{{ scope.row.createdTime || '-' }}</template>
      </el-table-column>
      <el-table-column label="操作" width="160" align="center" fixed="right">
        <template
          slot-scope="scope"
          v-if="$hasPermission('editBanner')"
          desc="轮播图设置（编辑）"
          actions="editRotation,deleteItem"
        >
          <el-button type="text" size="mini" @click="editRotation(scope.row, scope.$index)">修改</el-button>
          <el-popconfirm
            title="删除后轮播图需要重新添加，是否确认删除"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-info"
            icon-color="red"
            @confirm="deleteItem(scope.$index)"
          >
            <el-button slot="reference" type="text" size="mini">删除</el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <el-drawer :title="drawerTitle" :visible.sync="addRotationDialog" size="1000px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-form ref="form" label-width="auto" class="m-form f-mt20">
          <el-form-item label="门户轮播图：">
            <!--            <rotation-image-upload-new1-->
            <!--              :dialogStyleOpation="{-->
            <!--                width: '960px',-->
            <!--                height: '280px'-->
            <!--              }"-->
            <!--              ref="uploadRef"-->
            <!--              :ratioArr="['1920:420']"-->
            <!--              :initWidth="1920"-->
            <!--              title="门户轮播图"-->
            <!--              v-model="pictureUrl"-->
            <!--              @callback="callback"-->
            <!--              :link="link"-->
            <!--              :isEdit="isEdit"-->
            <!--              @linkChange="linkChange"-->
            <!--              :has-preview="false"-->
            <!--            ></rotation-image-upload-new1>-->

            <upload-image-link
              ref="uploadRef"
              v-model="pictureUrl"
              :link="link"
              @callback="callback"
              :isEdit="isEdit"
              :imgTypeTip="imageTips"
            >
            </upload-image-link>
          </el-form-item>

          <el-form-item class="m-btn-bar">
            <el-button @click="cancelSave">取消</el-button>
            <el-button type="primary" @click="save" :loading="saving">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
    <!-- <hb-pagination :page="page" v-bind="page"></hb-pagination> -->
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, Watch, Ref, Prop } from 'vue-property-decorator'

  import rotationImageUploadNew1 from '@hbfe/jxjy-admin-platform/src/components/rotation-image-upload-new1.vue'
  import { Query, UiPage } from '@hbfe/common'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import Sortable from 'sortablejs'
  import { ElTable } from 'element-ui/types/table'
  import BannerVo from '@api/service/common/online-school-config/vo/BannerVo'
  import UploadImageLink from '@hbfe/jxjy-admin-components/src/upload-image-link.vue'
  import TemplateItem from '@api/service/common/template-school/TemplateItem'
  @Component({
    components: {
      rotationImageUploadNew1,
      UploadImageLink
    }
  })
  export default class extends Vue {
    @Ref('dragWebTable')
    dragWebTable: any
    @Ref('uploadRef') uploadRef: any
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    @Prop({
      required: true,
      default: () => {
        return new TemplateItem()
      }
    })
    TemplateModuleObj: TemplateItem
    @Watch('TemplateModuleObj', {
      deep: true
    })
    valueChange() {
      this.imageTips = `建议选择高清大图，尺寸：宽度${this.TemplateModuleObj.bannerSize[0]}PX，高度 ${this.TemplateModuleObj.bannerSize[1]}PX`
    }
    flag = 1
    //添加轮播图标识
    addRotationDialog = false

    saving = false

    imageTips = '建议选择高清大图，尺寸：宽度1920PX，高度 400PX'

    drawerTitle = '添加轮播图'
    //暂存轮播数组
    getRotationList: any[] = []
    picture = ''
    pictureList: any[] = []
    address = ''
    page: UiPage
    query: Query = new Query()
    pictureUrl = ''
    show = false
    //查询请求
    queryBanner = OnlineSchoolConfigModule.queryBanner
    //业务请求
    mutationBanner = OnlineSchoolConfigModule.mutationBanner
    WebBannerList: Array<BannerVo> = new Array<BannerVo>()
    saveParams: any[] = new Array<any>()
    editIndex = 0
    //是否修改
    isEdit = false
    link = ''
    @Watch('pictureUrl', {
      deep: true,
      immediate: true
    })
    pictureUrlChange(val: any) {
      if (val) {
        console.log(val, '门户轮播图')

        // this.getRotationList.push(val)
        // this.uiPictureList.push(new UIPicture())
      }
    }
    constructor() {
      super()
      //  this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    addRotation() {
      this.pictureUrl = ''
      this.link = ''
      if (this.isEdit) {
        this.uploadRef.reset()
      }
      this.drawerTitle = '添加轮播图'
      this.isEdit = false
      this.addRotationDialog = true
    }
    editRotation(item: BannerVo, idx: number) {
      this.editIndex = idx
      const index = item.url.indexOf('/mfs/')
      this.pictureUrl = item.url.slice(index)
      this.drawerTitle = '修改轮播图'
      this.link = item.link
      this.isEdit = true
      this.addRotationDialog = true
    }
    linkChange() {
      this.link = ''
    }
    async doQueryPage() {
      this.query.loading = true
      try {
        // const res =
        const res = await this.queryBanner.queryWebBannerList()
        if (res.isSuccess()) {
          this.WebBannerList = this.queryBanner.WebBannerList
        }
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        this.query.loading = false
      }
    }
    cancelSave() {
      this.$confirm('提示', {
        message: '确定要放弃编辑吗？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.addRotationDialog = false

          // this.uploadRef.fileList = new Array<any>()
        })
        .catch(() => {
          console.log('点个der...')
        })
    }

    callback(val: any) {
      this.link = val[0]?.address
      this.saveParams = val
    }
    async save() {
      this.saving = true
      if (this.saveParams.length === 0) {
        this.$message.error('轮播图不能为空')
        this.saving = false
        return
      }
      this.saveParams.map((item, index) => {
        const i = new BannerVo()
        i.sort = this.WebBannerList.length + index + 1
        i.url = item.url
        i.link = item.address ? item.address : ''
        i.createdTime = this.timestampToTime()
        if (!this.isEdit) {
          this.WebBannerList.push(i)
        } else {
          i.sort = this.WebBannerList[this.editIndex]?.sort
          this.WebBannerList.splice(this.editIndex, 1, i)
        }
      })

      const res = await this.mutationBanner.doSaveWebBanner()
      if (res.isSuccess()) {
        this.$message.success('保存成功')
        this.addRotationDialog = false
        // await this.doQueryPage()
      }
      this.saving = false
    }
    async deleteItem(index: number) {
      this.WebBannerList.splice(index, 1)
      const res = await this.mutationBanner.doSaveWebBanner()
      if (res.isSuccess()) {
        this.$message.success('删除成功')
        // await this.doQueryPage()
      } else {
        this.$message.error('删除失败')
      }
    }
    dragElement(table: ElTable) {
      const el = table.$el.querySelector('.el-table__body-wrapper > table > tbody') as HTMLElement
      return new Sortable(el, {
        ghostClass: 'blue-background-class',
        handle: '.hb-iconfont',
        onEnd: ({ newIndex, oldIndex }) => {
          const curRow = this.WebBannerList.splice(oldIndex, 1)[0]
          this.WebBannerList.splice(newIndex, 0, curRow)
          let newArray = this.WebBannerList.slice(0)
          this.WebBannerList = []
          this.$nextTick(async () => {
            newArray = newArray.map((res, index) => {
              res.sort = index + 1
              return res
            })
            this.WebBannerList = newArray
            this.flag += 1
            await this.mutationBanner.doSaveWebBanner()
          })
        }
      })
    }
    async created() {
      await this.doQueryPage()
      this.dragElement(this.dragWebTable)
    }

    private timestampToTime() {
      const date = new Date() //时间戳为10位需*1000，时间戳为13位的话不需乘1000
      const Y = date.getFullYear() + '-'
      const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
      const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
      const h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
      const m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
      const s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
      return Y + M + D + h + m + s
    }
  }
</script>
