<!--
 * @Author: lixinye
 * @Date: 2023-06-05 09:35:52
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2025-05-09 11:00:10
 * @Description:
-->
<route-meta>
{
"isMenu": true,
"title": "功能设置",
"sort": 2,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main
    v-if="$hasPermission('config')"
    desc="功能设置"
    actions="@ElectronicInvoiceAutoV2,@TrainingCert,@CourseSortingConfig,doQueryServiceConfig"
  >
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky" @tab-click="clickTab">
      <template v-if="$hasPermission('registLogin')" desc="注册登录" actions="@RegistLogin">
        <el-tab-pane label="注册登录" name="regist-login">
          <regist-login></regist-login>
        </el-tab-pane>
      </template>

      <template v-if="$hasPermission('unitApply')" desc="集体报名" actions="@UnitApply">
        <el-tab-pane label="集体报名" name="unit-apply" lazy>
          <unit-apply></unit-apply>
        </el-tab-pane>
      </template>
      <!-- <el-tab-pane label="增值税电子普通发票（自动开票）旧" name="electronic-invoice-auto">
        <template v-if="$hasPermission('electronicInvoice')" desc="增值税电子发票" actions="@ElectronicInvoiceAuto">
          <electronic-invoice-auto></electronic-invoice-auto>
        </template>
      </el-tab-pane> -->
      <template v-if="$hasPermission('electronicInvoice')" desc="增值税电子发票" actions="@ElectronicInvoiceAutoV2">
        <el-tab-pane label="增值税电子普通发票（自动开票）" name="electronic-invoice-auto-v2">
          <electronic-invoice-auto-v2></electronic-invoice-auto-v2>
        </el-tab-pane>
      </template>
      <template v-if="$hasPermission('trainingCert')" desc="培训证明" actions="@TrainingCert">
        <el-tab-pane label="培训证明" name="training-cert">
          <training-cert></training-cert>
        </el-tab-pane>
      </template>
      <!-- <template v-if="$hasPermission('courseSortConfig')" desc="课程排序配置" actions="@CourseSortingConfig">
        <el-tab-pane label="课程排序配置" name="Course-sorting-config">
          <course-sorting-config></course-sorting-config>
        </el-tab-pane>
      </template> -->
      <!--      <el-tab-pane label="视频播放设置" name="video-play">-->
      <!--        <video-play></video-play>-->
      <!--      </el-tab-pane>-->
      <template v-if="$hasPermission('qualityCourse')" desc="门户精品课程" actions="@QualityCourse">
        <el-tab-pane label="门户精品课程" name="quality-course">
          <quality-course></quality-course>
        </el-tab-pane>
      </template>

      <template v-if="$hasPermission('trainingSupervision')" desc="培训监管管理" actions="@TrainingSupervision">
        <el-tab-pane label="培训监管管理" name="training-supervision">
          <training-supervision></training-supervision>
        </el-tab-pane>
      </template>
      <template
        v-if="$hasPermission('learningRules') && isShowLearningRulesTab"
        desc="补学规则"
        actions="@LearningRules"
      >
        <el-tab-pane label="补学规则" name="learning-rule" :lazy="true">
          <learning-rules v-if="activeName == 'learning-rule'"></learning-rules>
        </el-tab-pane>
      </template>
      <!-- <template
        v-if="$hasPermission('intelligentLearning') && isShowIntelligentLearningTab"
        desc="智能学习"
        actions="@IntelligentLearning"
      >
        <el-tab-pane label="智能学习" name="intelligent-learning" :lazy="true">
          <intelligent-learning ref="intelligentLearningRef"></intelligent-learning>
        </el-tab-pane>
      </template> -->
      <template v-if="$hasPermission('onlineLearningRules')" desc="在线学习规则" actions="@OnlineLearningRules">
        <el-tab-pane label="在线学习规则" name="online-learning-rule" :lazy="true">
          <online-learning-rules v-if="activeName == 'online-learning-rule'"></online-learning-rules>
        </el-tab-pane>
      </template>
    </el-tabs>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import RegistLogin from '@hbfe/jxjy-admin-platform/src/function/components/regist-login.vue'
  import UnitApply from '@hbfe/jxjy-admin-platform/src/function/components/unit-apply.vue'
  import ElectronicInvoiceAuto from '@hbfe/jxjy-admin-platform/src/function/components/electronic-invoice-auto.vue'
  import ElectronicInvoiceAutoV2 from '@hbfe/jxjy-admin-platform/src/function/components/electronic-invoice-auto-v2.vue'
  import TrainingCert from '@hbfe/jxjy-admin-platform/src/function/components/training-cert.vue'
  import VideoPlay from '@hbfe/jxjy-admin-platform/src/function/components/video-play.vue'
  import QualityCourse from '@hbfe/jxjy-admin-platform/src/function/components/quality-course.vue'
  import CourseSortingConfig from '@hbfe/jxjy-admin-platform/src/function/components/course-sorting-config.vue'
  import TrainingSupervision from '@hbfe/jxjy-admin-platform/src/function/supervision/index.vue'
  import LearningRules from '@hbfe/jxjy-admin-platform/src/function/learningRules/index.vue'
  import IntelligentLearning from '@hbfe/jxjy-admin-platform/src/function/intelligent-learning/index.vue'
  import IntelligenceLearningModule from '@api/service/management/intelligence-learning/IntelligenceLearningModule'
  import LearningRuleList from '@api/service/management/learning-rule/LearningRuleList'
  import Context from '@api/service/common/context/Context'
  import OnlineLearningRules from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/index.vue'

  @Component({
    components: {
      RegistLogin,
      UnitApply,
      ElectronicInvoiceAuto,
      ElectronicInvoiceAutoV2,
      TrainingCert,
      VideoPlay,
      QualityCourse,
      CourseSortingConfig,
      OnlineLearningRules,
      TrainingSupervision,
      LearningRules,
      IntelligentLearning
    }
  })
  export default class extends Vue {
    @Ref('intelligentLearningRef') intelligentLearningRef: IntelligentLearning
    activeName = 'regist-login'
    /**
     * 是否展示智能学习tab
     */
    isShowIntelligentLearningTab = false
    /**
     * 智能学习模型初始化
     */
    intelligenceLearningModule = new IntelligenceLearningModule()
    /**
     * 是否展示学习规则tab
     */
    isShowLearningRulesTab = false
    /**
     * 学习规则模型初始化
     */
    learningRuleModule = new LearningRuleList()
    async activated() {
      if (this.$route.query.type) {
        this.activeName = this.$route.query.type as string
      }
      await this.doQueryServiceConfig()
    }
    /**
     * 查询网校是否开启学习规则、智能学习增值服务
     */
    async doQueryServiceConfig() {
      const res = await this.intelligenceLearningModule.doQueryServiceConfig()
      if (res == 1) {
        this.isShowIntelligentLearningTab = true
      } else {
        this.isShowIntelligentLearningTab = false
      }
      const target = await this.learningRuleModule.queryRuleConfigByServicerId(Context.servicerInfo.id)
      if (target.status) {
        this.isShowLearningRulesTab = true
      } else {
        this.isShowLearningRulesTab = false
      }
    }
    clickTab(e: any) {
      if (e.name == 'intelligent-learning') {
        this.$nextTick(() => {
          this.intelligentLearningRef.doQuery()
        })
      }
    }
  }
</script>
