import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 考试结果枚举
 */
export enum ProcessResultStatusEnum {
  // 0：未处理
  NOT_PROCESSED,
  // 1：成功
  SUCCEED,
  // 2：失败
  FAIL,
  // 3：就绪失败
  READY_FAILURE
}

class ExamResultStatus extends AbstractEnum<ProcessResultStatusEnum> {
  static enum = ProcessResultStatusEnum

  constructor(status?: ProcessResultStatusEnum) {
    super()
    this.current = status
    this.map.set(ProcessResultStatusEnum.NOT_PROCESSED, '导出中，请稍后')
    this.map.set(ProcessResultStatusEnum.SUCCEED, '导出成功')
    this.map.set(ProcessResultStatusEnum.FAIL, '导出失败')
    this.map.set(ProcessResultStatusEnum.READY_FAILURE, '导出失败')
  }
}

export default new ExamResultStatus()
