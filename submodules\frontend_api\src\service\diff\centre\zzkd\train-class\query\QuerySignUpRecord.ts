import { Page, ResponseStatus } from '@hbfe/common'
import QuerySignUpRecordListVo from '@api/service/centre/train-class/query/vo/QuerySignUpRecordListVo'
import SignUpRecordListDetailVo from '@api/service/centre/train-class/query/vo/SignUpRecordListDetailVo'
import MsSchemeLearningQueryForestage, {
  SchemeSkuPropertyRequest,
  SortPolicy,
  StudentSchemeLearningSortField,
  StudentSchemeLearningSortRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import DataResolve from '@api/service/common/utils/DataResolve'
import UserModule from '@api/service/centre/user/UserModule'
import TrainClassUtils from '@api/service/centre/train-class/util/TrainClassUtils'
import QuerySignUpRecord from '@api/service/centre/train-class/query/QuerySignUpRecord'
import SignUpRecordListDetailVoDiff from '@api/service/diff/centre/zzkd/train-class/query/vo/SignUpRecordListDetailVo'
import ZzkdJxjyPlatform from '@api/diff-gateway/platform-jxjypxtypt-zzkd-school'
import ZzkdGateway from '@api/diff-gateway/zzkd-data-export-gateway-forestage'
import PlatformDataExport, {
  StudentSchemeLearningRequest
} from '@api/platform-gateway/platform-data-export-forestage-v1'
import Scheme from '@api/service/common/scheme/model/schemeDto/Scheme'
/**
 * @description 查询报名记录
 */
class QuerySignUpRecordDiff extends QuerySignUpRecord {
  /**
   * 查询报名记录列表
   */
  async querySignUpRecordList(
    page: Page,
    queryParams: QuerySignUpRecordListVo
  ): Promise<SignUpRecordListDetailVoDiff[]> {
    let result = [] as SignUpRecordListDetailVoDiff[]
    if (queryParams.studentName || queryParams.studentAccount) {
      const validateExistUser = await this.validateExistUser(queryParams.studentName, queryParams.studentAccount)
      if (!validateExistUser) return result
    }
    /** 查询未完成培训 && 合格日期 => 返回空数组 */
    if (
      queryParams.hasCompleteTraining === false &&
      (queryParams.qualifiedDate.begin || queryParams.qualifiedDate.end)
    ) {
      return result
    }
    const request = await queryParams.to()
    // const sortOption = new StudentSchemeLearningSortRequest()
    // sortOption.field = StudentSchemeLearningSortField.REGISTER_TIME
    // sortOption.policy = SortPolicy.DESC
    // const sort = Array(1).fill(sortOption)
    const response = await ZzkdJxjyPlatform.pageStudentSchemeLearningInCollectiveV2({
      page,
      request
      // sort
    })
    page.totalSize = response.data?.totalSize ?? 0
    page.totalPageSize = response.data?.totalPageSize ?? 0
    const schemeIdList: string[] = []
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = await Promise.all(
        response.data.currentPageData.map(async (item) => {
          schemeIdList.push(item.scheme.schemeId)
          return await SignUpRecordListDetailVoDiff.diffFrom(item)
        })
      )

      const configJsonMap = await TrainClassUtils.pageTrainClassConfig([...new Set(schemeIdList)])
      result.forEach((detail) => {
        const configJson = configJsonMap.get(detail.scheme?.schemeId)
        console.log('🚀🐱‍🚀🐱‍👓 ~ result.forEach ~ configJson:', configJson)
        if (configJson) {
          if (
            (configJson.type === 'chooseCourseLearning' && configJson.chooseCourseLearning) ||
            (configJson.type === 'autonomousCourseLearning' && configJson.autonomousCourseLearning)
          ) {
            detail.hasConfigCourseLearning = true
          }
          const commoditySale = configJson.commoditySale
          // 培训班名称
          detail.schemeName = commoditySale?.saleTitle ?? null
          // 培训班商品id
          detail.commoditySkuId = commoditySale?.id ?? null
          // 培训班价格
          detail.price = configJson?.price ?? null
          const { schemeAssessItem } = Scheme.parseTrainClassAssess(configJson as Scheme)
          if (schemeAssessItem) {
            const creditResult = schemeAssessItem.learningResults.find((item: any) => {
              return item.type == 1
            })
            // 考核学时
            detail.signUpPeriod = creditResult.grade ?? null
            // 是否允许打印培训证明
            const certificateResults = schemeAssessItem.learningResults.find((item: any) => {
              return item.type == 2
            })
            if (certificateResults) {
              detail.enablePrintCertificate =
                certificateResults.provideCert && certificateResults.openPrintTemplate ? true : false
              detail.certificateTemplateId = certificateResults.certificateTemplateId ?? null
            }
          }

          SignUpRecordListDetailVoDiff.fillSchemeLearningInfo(detail)
        }
      })
      // 填充学员信息
      const userIds = [...new Set(result?.map((item) => item.studentInfo.userId))]
      if (DataResolve.isWeightyArr(userIds)) {
        const pageSize = userIds.length
        const userInfos = await UserModule.queryUserFactory.queryStudentList.queryStudentInfoListById(userIds, pageSize)
        result.forEach((item) => {
          const target = userInfos.find((el) => el.userId === item.studentInfo.userId)
          item.studentInfo.userName = target?.userName ?? null
          item.studentInfo.userAccount = target?.idCard ?? null
        })
      }
    }
    console.log('**signUpRecordList', result)
    return result
  }

  /**
   * 异步导出学习数据
   * @param queryParams 查询参数对象，用于指定导出数据的条件
   * @returns 返回一个布尔值，表示数据导出是否成功
   */
  async doExportStudyData(queryParams: QuerySignUpRecordListVo) {
    const request: StudentSchemeLearningRequest = await queryParams.to()
    const res = await ZzkdGateway.exportStudentSchemeLearningExcelInCollective(request)
    return res.data || false
  }
}

export default QuerySignUpRecordDiff
