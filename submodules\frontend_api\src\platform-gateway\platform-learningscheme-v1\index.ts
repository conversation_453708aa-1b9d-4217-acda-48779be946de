import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-learningscheme-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-learningscheme-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 异步创建学习方案请求
<AUTHOR>
 */
export class AsyncCreateLearningSchemeRequest {
  /**
   * 校验token
   */
  token?: string
  /**
   * 方案配置json字符串
   */
  configJson?: string
}

/**
 * 异步修改学习方案请求
<AUTHOR>
 */
export class AsyncUpdateLearningSchemeRequest {
  /**
   * 校验token
   */
  token?: string
  /**
   * 方案配置json字符串
   */
  configJson?: string
}

/**
 * <AUTHOR> create 2022/5/25 16:14
 */
export class OneKeyPassRequest {
  /**
   * 参训资格id
   */
  qualificationId?: string
  /**
   * 合格时间配置 1：按系统当前操作成功时间
   */
  passTimeType: number
  /**
   * 合格元数据
   */
  metaDataList?: Array<MetaData>
}

export class MetaData {
  /**
   * key
包含学习方式key+合格属性key以.分隔
chooseCourseLearning:选课学习
examLearning:考试
practiceLearning:练习
autonomousCourseLearning:自主学习
interestCourseLearning:兴趣课学习
@see
   */
  key?: string
  /**
   * 值
   */
  value?: string
}

/**
 * 学员学习资源是否推送完成接口
return true:推送完成 false:推送未完成
<AUTHOR>
 */
export class StudentLearningResourcePushIsCompletedRequest {
  qualificationId: string
}

/**
 * 异步创建培训方案响应
<AUTHOR>
@since 2024/12/13
 */
export class AsyncCreateLearningSchemeResponse {
  /**
   * 方案id
   */
  schemeId: string
}

/**
 * 异步更新培训方案响应
<AUTHOR>
@since 2024/12/13
 */
export class AsyncUpdateLearningSchemeResponse {
  /**
   * 方案id
   */
  schemeId: string
}

/**
 * 一键合格任务响应
<AUTHOR> By Cb
@since 2024/10/14 14:34
 */
export class OneKeyPassResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 异步创建学习方案配置接口
   * @return 学习方案id
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async asyncCreateLearningScheme(
    request: AsyncCreateLearningSchemeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.asyncCreateLearningScheme,
    operation?: string
  ): Promise<Response<AsyncCreateLearningSchemeResponse>> {
    return commonRequestApi<AsyncCreateLearningSchemeResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 异步更新学习方案配置接口
   * @return 学习方案id
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async asyncUpdateLearningScheme(
    request: AsyncUpdateLearningSchemeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.asyncUpdateLearningScheme,
    operation?: string
  ): Promise<Response<AsyncUpdateLearningSchemeResponse>> {
    return commonRequestApi<AsyncUpdateLearningSchemeResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 一键合格
   * 10001 没有剩余的考试次数,无法操作
   * 500 其他原因导致失败
   * @param request:
   * @return {@link OneKeyPassResponse}
   * <AUTHOR> By Cb
   * @since 2024/10/14 14:39
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async oneKeyPass(
    request: OneKeyPassRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.oneKeyPass,
    operation?: string
  ): Promise<Response<OneKeyPassResponse>> {
    return commonRequestApi<OneKeyPassResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员学习资源是否推送完成
   * @param request
   * @return true:推送完成 false:推送未完成
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async studentLearningResourcePushIsCompleted(
    request: StudentLearningResourcePushIsCompletedRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.studentLearningResourcePushIsCompleted,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
