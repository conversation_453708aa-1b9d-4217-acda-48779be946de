import UserCoursePaperPublishConfigure from '@api/service/customer/course/query/vo/after-course-config/UserCoursePaperPublishConfigure'

class QuizConfig {
  multipleMissScorePattern: number
  name: string
  allowCourseQuizNum: number
  openDissects: boolean
  id: string
  limitCourseQuizNum: false
  userCoursePaperPublishConfigure: UserCoursePaperPublishConfigure = new UserCoursePaperPublishConfigure()
  passScore: number
  operation: number
  publishPattern: number
}

export default QuizConfig
