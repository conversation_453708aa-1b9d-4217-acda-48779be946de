<template>
  <div>
    <el-upload
      action="#"
      list-type="picture-card"
      class="m-pic-upload long-pic"
      :class="{ disabled: imgList.length >= limits }"
      :disabled="imgList.length >= limits"
      :on-change="uploadImgChange"
      :auto-upload="false"
      :file-list="imgList"
      :limit="limits"
      :accept="imgType"
      :multiple="isMultiple"
      :before-remove="handleBeforeRemove"
    >
      <div slot="default" class="upload-placeholder">
        <i class="el-icon-plus"></i>

        <p class="txt" v-if="contentText">{{ contentText }}</p>
      </div>
      <div slot="file" slot-scope="{ file }" class="img-file">
        <el-image class="el-upload-list__item-thumbnail" :src="file.url" alt="" fit="fill"></el-image>

        <div class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <i class="el-icon-zoom-in"></i>
          </span>
          <span class="el-upload-list__item-delete" @click="handleRemove(file)">
            <i class="el-icon-delete"></i>
          </span>
        </div>
        <div class="other">
          <p>链接地址</p>
          <el-input
            v-model="file.address"
            clearable
            class="f-wf"
            placeholder="输入完整域名，例如：https://www.baidu.com/"
          />
        </div>
      </div>
      <div slot="tip" class="el-upload__tip">
        <i class="el-icon-warning"></i>
        <span class="txt">
          {{ imgTypeTip }}
        </span>
      </div>
    </el-upload>

    <!-- 大图预览 -->
    <el-image style="width: 100px; height: 100px" :preview-src-list="dialogImageUrl" v-show="false" ref="elImage">
    </el-image>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Ref } from 'vue-property-decorator'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import axios from 'axios'
  import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

  @Component
  export default class extends Vue {
    loading = {}
    // 资源地址
    resourceUrl = ''
    // 图片预览对话框
    picDialogVisible = false
    // 预览地址
    dialogImageUrl = new Array<string>()

    @Ref('elImage') elImage: any

    /**
     * 上传中间文字
     */
    @Prop({
      type: String,
      default: ''
    })
    contentText: string

    // 图片后缀
    @Prop({
      type: String,
      default: '.jpg,.png,.jpeg'
    })
    imgType: string
    // 数量限制
    @Prop({
      type: Number,
      default: 10
    })
    limit: string

    get limits() {
      return this.isEdit ? 1 : this.limit
    }

    // 图片后缀的文字提示
    @Prop({
      type: String,
      default: '请上传*.jpg，*.jpeg，*.png格式的图片'
    })
    imgTypeTip: string

    // 默认支持多选
    @Prop({
      type: Boolean,
      default: true
    })
    isMultiple: boolean

    // 传给后端的图片路径数组/ 预览数组
    imgList = new Array<{ name?: string; url: string; address?: string }>()

    // 是否是修改
    @Prop({
      type: Boolean,
      default: false
    })
    isEdit: boolean

    // 用于修改的链接
    @Prop({
      type: String,
      default: ''
    })
    link: string

    @Prop({
      type: Number,
      default: 5
    })
    maxSize = 5

    // 用于接收回显的图片数组
    @Prop([Array, Object])
    value: Array<any> | string
    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueWatch() {
      if (this.isEdit) {
        this.imgList = new Array<{ name?: string; url: string; address?: string }>()
        this.imgList.push({
          url: (this.value.length && this.value[0].url) || (this.value as string),
          address: this.link
        })
      } else {
        this.imgList = this.value ? (this.value as Array<any>) : []
      }
    }
    created() {
      this.resourceUrl = ConfigCenterModule.getIngress('ingress.resource')
    }

    // 上传图片
    uploadImgChange(file: any, fileList: any) {
      this.$set(this.loading, file.uid, true)
      // 校验通过之后，转为base图片
      const reader = new FileReader()
      if (file.size / 1024 / 1024 > this.maxSize) {
        this.$message.warning(`上传图片大小不能超过${this.maxSize}M`)

        this.maxRemove()
        this.$set(this.loading, file.uid, false)
        return
      }
      reader.readAsDataURL(file.raw || file)
      reader.onloadend = () => {
        // 图片的 base64 格式, 可以直接当成 img 的 src 属性值
        const dataURL = reader.result as string
        this.getResourceUrl(dataURL, fileList, file.name, file.uid)
      }
    }

    // 保存符合格式的图片
    getResourceUrl(imgCode: string, fileList: any, fileName: string, uid: number) {
      const data = {
        base64Data: imgCode.split(',')[1].toString(),
        fileName
      }
      // 转为图片
      //   const baseUrl = `${this.resourceUrl}/auth/uploadBase64ToProtectedFile`
      const baseUrl = `${ConfigCenterModule.getIngress(ingress.apiendpoint)}/web/ms-file-v1/web/uploadPublicBase64`

      try {
        axios.post(baseUrl, data).then(data => {
          const imgUrl = data.data.data
          fileList[fileList.length - 1].name = imgUrl
          fileList[fileList.length - 1].url = '/mfs' + imgUrl
          this.imgList = fileList
          this.$emit('input', this.imgList)

          this.$set(this.loading, uid, false)
        })
      } catch (error) {
        this.$message('图片转换失败')
      }
    }

    // 预览图片
    handlePictureCardPreview(file: any) {
      if (file?.url) {
        this.dialogImageUrl = [file.url]
        // this.picDialogVisible = true
      } else {
        this.$message.error('图片预览地址出错！')
      }
      this.$nextTick(() => {
        // 触发点击方法
        this.elImage.clickHandler()
      })
    }

    /* 删除图片*/
    handleRemove(file: any) {
      const idx: number = this.imgList.findIndex(el => el.url === file.url)
      this.imgList.splice(idx, 1)
      // if (this.isEdit) {
      //   this.imgList = new Array<{ name?: string; url: string; address?: string }>()
      // } else {
      //
      // }
    }

    maxRemove() {
      this.imgList.splice(this.imgList.length, 1)
    }

    @Watch('imgList', {
      deep: true,
      immediate: true
    })
    fileListChange(val: any) {
      if (val) {
        this.$emit('callback', val)
      }
    }
    handleBeforeRemove(file: any, fileList: any) {
      return false
    }

    reset() {
      this.imgList = []
    }
  }
</script>
<style lang="scss" scoped>
  ::v-deep div.el-dialog__body {
    text-align: center;
  }
</style>

<style rel="stylesheet/scss" lang="scss">
  .disabled .el-upload--picture-card {
    display: none;
  }
</style>
