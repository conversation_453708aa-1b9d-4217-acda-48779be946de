/*
 * @Description: 发票类
 * @Version: feature/*******.0
 * @Autor: Zhu Song
 * @Date: 2022-02-16 09:43:49
 * @LastEditors: Lin yt
 * @LastEditTime: 2022-05-06 20:07:28
 */

/**
 * DistrbutionData 类用于封装配送和取货相关的数据。
 * 包含配送配置、收货地址信息、取货地点及时间等属性。
 */
export default class DistrbutionData {
  /**
   * 配送配置标识，用于指定配送方式或逻辑。
   * 类型: number
   */
  distributionConfig: number

  /**
   * 配送地址备注信息，用于补充说明配送地址的额外信息。
   * 类型: string
   */
  deliveryAddressRemark: string

  /**
   * 收件人姓名，表示包裹接收人的名称。
   * 类型: string
   */
  consignee: string

  /**
   * 配送地址电话，收件人的联系电话。
   * 类型: string
   */
  deliveryAddressPhone: string

  /**
   * 地区信息，表示配送地址所属的区域。
   * 类型: string
   */
  region: string

  /**
   * 详细地址，具体到门牌号的收货地址。
   * 类型: string
   */
  address: string

  /**
   * 自提地点，用户选择的取货位置。
   * 类型: string
   */
  pickupLocation: string

  /**
   * 取货时间，用户预约或指定的取货时间。
   * 类型: string
   */
  pickupTime: string

  /**
   * 取货点备注（可选），用于补充说明取货点的相关信息。
   * 类型: string | undefined
   */
  takePointRemark?: string
}
