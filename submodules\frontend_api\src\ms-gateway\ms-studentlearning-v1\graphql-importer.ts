import TraineesAchievementRevocation from './mutates/TraineesAchievementRevocation.graphql'
import applyExam from './mutates/applyExam.graphql'
import applyQuestionnaire from './mutates/applyQuestionnaire.graphql'
import applyStudentLearningToken from './mutates/applyStudentLearningToken.graphql'
import applyStudentLearningTokenInIssue from './mutates/applyStudentLearningTokenInIssue.graphql'
import applyStudentLearningTokenInterruptAutoStudy from './mutates/applyStudentLearningTokenInterruptAutoStudy.graphql'
import applyStudentLearningValidate from './mutates/applyStudentLearningValidate.graphql'
import applyStudentLearningValidateInIssue from './mutates/applyStudentLearningValidateInIssue.graphql'
import passedAssessDataRepair from './mutates/passedAssessDataRepair.graphql'
import revokeLearningResult from './mutates/revokeLearningResult.graphql'
import trainingQualificationDataRepair from './mutates/trainingQualificationDataRepair.graphql'
import validStudentAllowLearning from './mutates/validStudentAllowLearning.graphql'

export {
  TraineesAchievementRevocation,
  applyExam,
  applyQuestionnaire,
  applyStudentLearningToken,
  applyStudentLearningTokenInIssue,
  applyStudentLearningTokenInterruptAutoStudy,
  applyStudentLearningValidate,
  applyStudentLearningValidateInIssue,
  passedAssessDataRepair,
  revokeLearningResult,
  trainingQualificationDataRepair,
  validStudentAllowLearning
}
