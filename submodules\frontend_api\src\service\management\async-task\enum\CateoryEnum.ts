import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum CategoryEnum {
  PERSONAL_OFFLINE_INVOICE_IMPORT = 'PERSONAL_OFFLINE_INVOICE_IMPORT',
  PERSONAL_OFFLINE_SPECIAL_INVOICE_IMPORT = 'PERSONAL_OFFLINE_SPECIAL_INVOICE_IMPORT',
  BATCH_OFFLINE_INVOICE_IMPORT = 'BATCH_OFFLINE_INVOICE_IMPORT',
  BATCH_OFFLINE_SPECIAL_INVOICE_IMPORT = 'BATCH_OFFLINE_SPECIAL_INVOICE_IMPORT',
  PERSONAL_OFFLINE_INVOICE_DELIVERY_INFO_IMPORT = 'PERSONAL_OFFLINE_INVOICE_DELIVERY_INFO_IMPORT',
  BATCH_OFFLINE_INVOICE_DELIVERY_INFO_IMPORT = 'BATCH_OFFLINE_INVOICE_DELIVERY_INFO_IMPORT',
  PERSO<PERSON>L_OFFLINE_SPECIAL_ELECTRONIC_INVOICE_IMPORT = 'PERSONAL_OFFLINE_SPECIAL_ELECTRONIC_INVOICE_IMPORT',
  BATCH_OFFLINE_SPECIAL_ELECTRONIC_INVOICE_IMPORT = 'BATCH_OFFLINE_SPECIAL_ELECTRONIC_INVOICE_IMPORT'
}

class CategoryType extends AbstractEnum<CategoryEnum> {
  static enum = CategoryEnum
  constructor(status?: CategoryEnum) {
    super()
    this.current = status
    this.map.set(CategoryEnum.PERSONAL_OFFLINE_INVOICE_IMPORT, '导入个人线下电子发票')
    this.map.set(CategoryEnum.PERSONAL_OFFLINE_SPECIAL_INVOICE_IMPORT, '导入个人线下纸质发票')
    this.map.set(CategoryEnum.BATCH_OFFLINE_INVOICE_IMPORT, '导入批次缴费线下电子发票')
    this.map.set(CategoryEnum.BATCH_OFFLINE_SPECIAL_INVOICE_IMPORT, '导入批次缴费线下纸质发票')
    this.map.set(
      CategoryEnum.PERSONAL_OFFLINE_INVOICE_DELIVERY_INFO_IMPORT,
      '个人报名增值税电子普通发票（导入个人报名线下发票配送信息）'
    )
    this.map.set(CategoryEnum.BATCH_OFFLINE_INVOICE_DELIVERY_INFO_IMPORT, '导入集体报名线下专票配送信息')
    this.map.set(CategoryEnum.PERSONAL_OFFLINE_SPECIAL_ELECTRONIC_INVOICE_IMPORT, '导入个人线下增值税专用票电子票')
    this.map.set(CategoryEnum.BATCH_OFFLINE_SPECIAL_ELECTRONIC_INVOICE_IMPORT, '导入批次缴费线下增值税专用票电子票')
  }
}

export default new CategoryType()
