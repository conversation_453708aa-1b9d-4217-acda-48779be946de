import { CoursewareCategoryResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class CoursewareCategoryListDetail {
  id: string = undefined
  name: string = undefined
  sort: number = undefined
  parentId: string = undefined
  createTime: string = undefined
  parentName: string = undefined

  children: Array<CoursewareCategoryListDetail> = undefined
  hasChildren = true

  static from(courseCategoryResponse: CoursewareCategoryResponse): CoursewareCategoryListDetail {
    const coursewareCategoryListDetail = new CoursewareCategoryListDetail()
    Object.assign(coursewareCategoryListDetail, courseCategoryResponse)
    return coursewareCategoryListDetail
  }
}

export default CoursewareCategoryListDetail
