import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformCoursewareSupplier'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformCoursewareSupplier'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum ServicerContractStatusEnums {
  ALL = 'ALL',
  NORMAL = 'NORMAL',
  SUSPEND = 'SUSPEND'
}
export enum ServicerTypeEnums {
  ALL = 'ALL',
  TRAINING_INSTITUTION = 'TRAINING_INSTITUTION',
  COURSEWARE_SUPPLIER = 'COURSEWARE_SUPPLIER',
  CHANNEL_VENDOR = 'CHANNEL_VENDOR',
  PARTICIPATING_UNIT = 'PARTICIPATING_UNIT'
}
export enum ServicerContractStatusEnum {
  NORMAL = 'NORMAL',
  SUSPEND = 'SUSPEND'
}

// 类

/**
 * 创建服务商参数
 */
export class ServicerCreateParams {
  /**
   * 合作机构id
   */
  trainingInstitutionId?: string
  /**
   * 服务商名称
   */
  name: string
  /**
   * 所在地区路径
   */
  areaPath?: string
  /**
   * 说明信息
   */
  description?: string
  /**
   * 负责人
   */
  userName?: string
  /**
   * 手机号
   */
  phoneNumber?: string
}

/**
 * 与当前培训机构角色签约的课件供应商查询条件
<AUTHOR>
@since 2021/11/1
 */
export class CoursewareSupplierForTInstitutionQueryParams {
  /**
   * 名称
   */
  name?: string
  /**
   * 课件供应商地区
   */
  regionPath?: Array<string>
  /**
   * 合作签约开始时间
   */
  beginTime?: string
  /**
   * 合作签约结束时间
   */
  endTime?: string
  /**
   * 合作状态
   */
  contractStatus?: ServicerContractStatusEnums
  /**
   * 手机号
   */
  phone?: string
}

/**
 * 课件供应商查询信息
 */
export class CoursewareSupplierQueryParams {
  /**
   * 培训机构Id
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 课件供应商名称
   */
  name?: string
  /**
   * 合作状态
   */
  status?: ServicerContractStatusEnums
}

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * graphql没有泛型的操作结果类
<AUTHOR> create 2020/3/9 15:18
 */
export class GraphqlOperateResult {
  /**
   * 返回的code
   */
  code: string
  /**
   * 返回的message
   */
  message: string
  /**
   * json字段
存放返回的参数用
   */
  expandData: Map<string, string>
}

/**
 * 账号信息
 */
export class AccountDto {
  /**
   * 帐号
   */
  loginAccount: string
  /**
   * 姓名
   */
  name: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人
   */
  creator: string
}

/**
 * 有合约的服务商信息
 */
export class ServicerContractDto {
  /**
   * 服务商 Id
   */
  id: string
  /**
   * 服务商类型
   */
  servicerType: ServicerTypeEnums
  /**
   * 名称
   */
  name: string
  /**
   * 合作状态
   */
  status: ServicerContractStatusEnums
  /**
   * 服务商合作日志
   */
  contractLogList: Array<ServicerContractLogDto>
}

/**
 * 服务商签约日志返回值
 */
export class ServicerContractLogDto {
  /**
   * 合作状态
   */
  contractLogStatus: ServicerContractStatusEnum
  /**
   * 操作时间
   */
  operationTime: string
  /**
   * 操作人
   */
  operationUserName: string
}

/**
 * 服务详情信息（渠道商、课件供应商）
 */
export class ServicerDetailDto {
  /**
   * Id
   */
  id: string
  /**
   * 课件供应商名称
   */
  name: string
  /**
   * 地区名称列表，省市区县...
   */
  regionNames: Array<string>
  /**
   * 所在地区路径
   */
  regionPath: string
  /**
   * 负责人
   */
  contactPerson: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 课件供应商优势简述
   */
  abouts: string
  /**
   * 合作状态
   */
  status: ServicerContractStatusEnums
  /**
   * 有合约的服务商信息
   */
  servicerContracts: Array<ServicerContractDto>
  /**
   * 账号信息
   */
  accounts: Array<AccountDto>
}

/**
 * 课件供应商列表信息
 */
export class CoursewareSupplierForTIDto {
  /**
   * Id
   */
  id: string
  /**
   * 课件供应商名称
   */
  name: string
  /**
   * 地区名称列表，省市区县...
   */
  regionNames: Array<string>
  /**
   * 所在地区路径
   */
  regionPath: string
  /**
   * 负责人
   */
  contactPerson: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 合作状态
   */
  status: ServicerContractStatusEnums
  /**
   * 签约时间
   */
  contractTime: string
}

/**
 * 课件供应商列表信息
 */
export class CoursewareSupplierListDto {
  /**
   * Id
   */
  id: string
  /**
   * 课件供应商名称
   */
  name: string
  /**
   * 地区名称列表，省市区县...
   */
  regionNames: Array<string>
  /**
   * 所在地区路径
   */
  regionPath: string
  /**
   * 负责人
   */
  contactPerson: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 合作状态
   */
  status: ServicerContractStatusEnums
}

export class CoursewareSupplierListDtoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursewareSupplierListDto>
}

export class CoursewareSupplierForTIDtoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursewareSupplierForTIDto>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 子项目管理员课件供应商详情
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async detail(
    id: string,
    query: DocumentNode = GraphqlImporter.detail,
    operation?: string
  ): Promise<Response<ServicerDetailDto>> {
    return commonRequestApi<ServicerDetailDto>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 子项目管理员课件供应商详情
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async page(
    params: { page?: Page; params?: CoursewareSupplierQueryParams },
    query: DocumentNode = GraphqlImporter.page,
    operation?: string
  ): Promise<Response<CoursewareSupplierListDtoPage>> {
    return commonRequestApi<CoursewareSupplierListDtoPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 与当前培训机构签约的课件供应商列表
   * @param params 查询条件
   * @param page   分页信息
   * @return 课件供应商列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageForTrainingInstitution(
    params: { page?: Page; params?: CoursewareSupplierForTInstitutionQueryParams },
    query: DocumentNode = GraphqlImporter.pageForTrainingInstitution,
    operation?: string
  ): Promise<Response<CoursewareSupplierForTIDtoPage>> {
    return commonRequestApi<CoursewareSupplierForTIDtoPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建课件供应商（通过请求直接创建）
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCoursewareSupplier(
    params: { token?: string; params?: ServicerCreateParams },
    mutate: DocumentNode = GraphqlImporter.createCoursewareSupplier,
    operation?: string
  ): Promise<Response<GraphqlOperateResult>> {
    return commonRequestApi<GraphqlOperateResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建提供企业账户课件供应商（通过请求直接创建）
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCoursewareSupplierByAccount(
    params: { token?: string; accountId?: string; params?: ServicerCreateParams },
    mutate: DocumentNode = GraphqlImporter.createCoursewareSupplierByAccount,
    operation?: string
  ): Promise<Response<GraphqlOperateResult>> {
    return commonRequestApi<GraphqlOperateResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
