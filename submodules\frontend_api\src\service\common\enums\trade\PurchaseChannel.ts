/**
 * 退款模式
 */
import AbstractEnum from '../AbstractEnum'

enum PurchaseChannelEnum {
  user_self = 1,
  collective_pay = 2,
  manager_import = 3,
  subsidy_system = 4
}

export { PurchaseChannelEnum }

class PurchaseChannel extends AbstractEnum<PurchaseChannelEnum> {
  static enum = PurchaseChannelEnum

  constructor() {
    super()
    this.map[PurchaseChannelEnum.user_self] = '用户自主购买'
    this.map[PurchaseChannelEnum.collective_pay] = '集体缴费'
    this.map[PurchaseChannelEnum.manager_import] = '管理员导入'
    this.map[PurchaseChannelEnum.subsidy_system] = '补贴系统'
  }
}

export default PurchaseChannel
