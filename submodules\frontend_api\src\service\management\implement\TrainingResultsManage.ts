import PeriodResult from '@api/service/management/implement/models/PeriodResult'
import { Page } from '@hbfe/common'
import PeriodManageParams from '@api/service/management/implement/models/PeriodManageParams'
import MsSchemeLearningQuery, {
  SchemeIssueRegistrationResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import MsSchemeQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
import ExtendProperty from '@api/service/common/scheme/model/schemeDto/common/extend-properties/ExtendProperty'

/**
 * 培训结果
 */
export default class TrainingResultsManage {
  /**
   * 培训方案id
   */
  private schemeId = ''
  /**
   * 筛选入参
   */
  params: PeriodManageParams = new PeriodManageParams()

  /**
   * 期别列表
   */
  list: Array<PeriodResult> = new Array<PeriodResult>()

  /**
   * 培训班类别
   */
  schemeType: TrainingModeEnum = undefined

  /**
   * @param schemeId 方案id
   */
  constructor(schemeId: string) {
    this.schemeId = schemeId
  }

  /**
   * 获取培训方案类型
   */
  async getSchemeType() {
    const res = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInServicer({
      schemeId: this.schemeId,
      needField: ['extendProperties']
    })

    if (res?.data?.skuProperty?.trainingWay?.skuPropertyValueId) {
      this.schemeType = res.data.skuProperty.trainingWay.skuPropertyValueId as TrainingModeEnum
    }
  }

  /**
   * 分页查询混合版简易培训成果
   * todo 面授班的要去原先方案学习统计拿
   * @param page 页码
   */
  async queryList(page: Page) {
    const res = await MsSchemeLearningQuery.pageStudentSchemeIssueRegistrationInServicer({
      page,
      schemeId: this.schemeId,
      issueName: this.params.periodName ? this.params.periodName : undefined,
      issueNum: this.params.periodNo ? this.params.periodNo : undefined
    })

    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize

    if (res?.data?.currentPageData?.length) {
      this.list = res.data.currentPageData.map((item: SchemeIssueRegistrationResponse) => {
        return PeriodResult.from(item)
      })
    } else {
      this.list = new Array<PeriodResult>()
    }
  }
}
