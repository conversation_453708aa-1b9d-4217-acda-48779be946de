import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-interestcourselearningscene-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-interestcourselearningscene-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 课程学习凭证响应
<AUTHOR>
@since 2022/1/20
 */
export class StudentCourseLearningTokenResponse {
  /**
   * 申请结果
   */
  applyResult: TokenResponse
  /**
   * 课程学习凭证
   */
  token: string
}

/**
 * 凭证响应基类
<AUTHOR>
@since 2022/1/20
 */
export class TokenResponse {
  /**
   * 代码：
200-成功
   */
  code: string
  /**
   * 信息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请课程学习
   * 200-成功
   * 60001-学习token解析异常
   * 60002-学习场景不存在
   * 60003-该大纲下课程不存在
   * 60004-选课失败
   * @param studentLearningToken 学员学习凭证
   * @param outlineId            所属课程学习大纲编号
   * @param courseId             课程编号
   * @return 课程学习凭证
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCourseLearning(
    params: { studentLearningToken: string; outlineId: string; courseId: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCourseLearning,
    operation?: string
  ): Promise<Response<StudentCourseLearningTokenResponse>> {
    return commonRequestApi<StudentCourseLearningTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
