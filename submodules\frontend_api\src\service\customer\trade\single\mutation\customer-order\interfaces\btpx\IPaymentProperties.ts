export interface TrainingVouchersProperty {
  /**
   * 字段的key
   */
  key?: string
  /**
   * 字段value
   */
  value?: string
}
// 补贴平台下特有的支付订单扩展入参
export default interface IPaymentProperties {
  /**
      * 支付的附加属性，由支付渠道决定
   当支付渠道为培训券（TRAINING4_VOUCHER）时，
   {
   &quot;couponCode&quot;:&quot;培训券编码&quot;,
   &quot;educationCode&quot;:&quot;机构编码（企业统一社会信用代码）&quot;,
   &quot;workType&quot;: &quot;工种名称&quot;,
   &quot;learningSchemeId&quot;: &quot;培训班id&quot;
   }
      */
  paymentProperties?: Array<TrainingVouchersProperty>
}
