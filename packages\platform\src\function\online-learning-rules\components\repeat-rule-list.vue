<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-08-09 11:17:10
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-08-19 16:40:35
 * @Description: 
-->
<template>
  <el-drawer
    title="查看已存在规则的培训属性"
    :visible.sync="open"
    size="900px"
    :append-to-body="true"
    custom-class="m-drawer m-table-auto"
    :close="close"
  >
    <div class="drawer-bd">
      <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb10">
        以下培训属性已存在在线学习规则，请调整规则配置
      </el-alert>
      <!--表格-->
      <el-table stripe :data="repeatRuleList" class="m-table">
        <el-table-column type="index" label="No." width="60"></el-table-column>
        <el-table-column label="所属行业" min-width="150">
          <template slot-scope="scope">{{ scope.row.industryName }}</template>
        </el-table-column>
        <el-table-column label="年度" min-width="150">
          <template slot-scope="scope">{{ scope.row.year }}</template>
        </el-table-column>
        <el-table-column label="培训属性" min-width="240">
          <template slot-scope="scope">
            <div v-html="skuStyle(scope.row.propertyName)"></div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="close">返回</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import existingProperty from '@api/service/management/online-learning-rule/model/existingProperty'
  import { Component, Vue } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    /**
     * 重复规则列表
     */
    repeatRuleList = new Array<existingProperty>()
    open = false

    /**
     * 关闭抽屉
     */
    close() {
      this.open = false
    }

    /**
     * sku样式调整
     */
    get skuStyle() {
      return (item: string) => {
        let str = ''
        item.split('、').forEach(element => {
          str += `<p>${element}</p>`
        })
        return str
      }
    }
  }
</script>
