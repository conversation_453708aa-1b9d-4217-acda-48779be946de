import MsExamQuestionGateway from '@api/ms-gateway/ms-examquestion-v1'
import MsExamQueryBackStageGateway, {
  MultipleQuestionResponse,
  OpinionQuestionResponse,
  RadioQuestionResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'

import { ResponseStatus, Response } from '@hbfe/common'
import RadioQuestionDto from '../mutation/vo/update/UpdateRadioQuestionVo'
import MultipleQuestionDto from '../mutation/vo/update/UpdateMultipleQuestionVo'
import OpinionQuestionDto from '../mutation/vo/update/UpdateOpinionQuestionVo'
import { QuestionTypeEnum } from '@api/service/common/enums/question/QuestionType'
import UpdateQuestionVo from '../mutation/vo/update/UpdateQuestionVo'

class UpdateQuestion {
  /*
    试题模型 【单选, 多选, 判断】
  */
  updateQuestionParams = new UpdateQuestionVo()

  /* 试题id, 试题类型 */
  constructor(id: string, questionType: QuestionTypeEnum) {
    const QuestionTypeConstructors = {
      [QuestionTypeEnum.radio]: () => {
        return new RadioQuestionDto()
      },
      [QuestionTypeEnum.multiple]: () => {
        return new MultipleQuestionDto()
      },
      [QuestionTypeEnum.opinion]: () => {
        return new OpinionQuestionDto()
      }
    }
    if (!QuestionTypeConstructors[questionType]) {
      console.error('选择的试题类型不存在！')
      throw new Error('选择的试题类型不存在！')
    }
    this.updateQuestionParams = QuestionTypeConstructors[questionType]()
    this.updateQuestionParams.id = id
    this.updateQuestionParams.questionType = questionType
  }

  /**
   * @description: 查询试题信息[初始化调用]
   * @param {*}
   * @return {*}
   */
  async doQueryQuestionInfo(): Promise<ResponseStatus> {
    const { data, status } = await this.doQueryByQuestionType(this.updateQuestionParams.questionType)
    if (status?.isSuccess()) {
      this.updateQuestionParams.from(data)
    }
    return status
  }

  // 根据试题类型调用查询试题接口
  doQueryByQuestionType(
    type: QuestionTypeEnum
  ): Promise<Response<RadioQuestionResponse | MultipleQuestionResponse | OpinionQuestionResponse>> {
    const QueryByQuestionTypes = {
      [QuestionTypeEnum.radio]: async () => {
        return await this.queryRadioQuestionDetail()
      },
      [QuestionTypeEnum.multiple]: async () => {
        return await this.queryMultipleQuestionDetail()
      },
      [QuestionTypeEnum.opinion]: async () => {
        return await this.queryOpinionQuestionDetail()
      }
    }
    if (!QueryByQuestionTypes[type]) {
      console.error('选择的试题类型不存在！')
      throw new Error('选择的试题类型不存在！')
    }
    return QueryByQuestionTypes[type]()
  }

  /**
   * @description: 修改试题
   * @param {*}
   */
  async doUpdateQuestion(): Promise<ResponseStatus> {
    const { status } = await MsExamQuestionGateway.updateQuestion(this.updateQuestionParams.toDto())
    return status
  }

  // 单选试题详情
  private async queryRadioQuestionDetail(): Promise<Response<RadioQuestionResponse>> {
    const res = await MsExamQueryBackStageGateway.getRadioQuestionInServicer(this.updateQuestionParams.id)
    return res
  }

  // 多选试题详情
  private async queryMultipleQuestionDetail(): Promise<Response<MultipleQuestionResponse>> {
    const res = await MsExamQueryBackStageGateway.getMultipleQuestionInServicer(this.updateQuestionParams.id)
    return res
  }

  // 判断题试题详情
  private async queryOpinionQuestionDetail(): Promise<Response<OpinionQuestionResponse>> {
    const res = await MsExamQueryBackStageGateway.getOpinionQuestionInServicer(this.updateQuestionParams.id)
    return res
  }
}
export default UpdateQuestion
