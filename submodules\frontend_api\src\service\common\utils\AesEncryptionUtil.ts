import CryptoJS from 'crypto-js'

/**
 * aes加密工具
 */
class AesEncryptionUtil {
  /**
   * 加密key值
   */
  private readonly _key = CryptoJS.enc.Utf8.parse('fa4b45a8e82dad3e')

  /**
   * 加密位移量
   */
  private readonly _iv = CryptoJS.enc.Utf8.parse('f312c5c76522a297')

  /**
   * 加密
   * @param word
   */
  encrypt(word: string | object) {
    let encrypted
    if (typeof word == 'string') {
      const srcs = CryptoJS.enc.Utf8.parse(word)
      encrypted = CryptoJS.AES.encrypt(srcs, this._key, {
        iv: this._iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      })
    } else if (typeof word == 'object') {
      //对象格式的转成json字符串
      const data = JSON.stringify(word)
      const srcs = CryptoJS.enc.Utf8.parse(data)
      encrypted = CryptoJS.AES.encrypt(srcs, this._key, {
        iv: this._iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      })
    }
    return encrypted.ciphertext.toString()
  }

  /**
   * 解密
   * @param word
   */
  decrypt(word: string) {
    const encryptedHexStr = CryptoJS.enc.Hex.parse(word)
    const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
    const decrypt = CryptoJS.AES.decrypt(srcs, this._key, {
      iv: this._iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })
    const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
    return decryptedStr.toString()
  }
}

export default new AesEncryptionUtil()
