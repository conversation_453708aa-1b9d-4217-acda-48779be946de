import MsExamQuestionGateway from '@api/ms-gateway/ms-examquestion-v1'
import { ResponseStatus } from '@hbfe/common'

import CreateRadioQuestionDto from '../mutation/vo/create/CreateRadioQuestionVo'
import CreateMultipleQuestionDto from '../mutation/vo/create/CreateMultipleQuestionVo'
import CreateOpinionQuestionDto from '../mutation/vo/create/CreateOpinionQuestionVo'
import QuestionType, { QuestionTypeEnum } from '@api/service/common/enums/question/QuestionType'
import CreateQuestionVo from './vo/create/CreateQuestionVo'

class CreateQuestion {
  //  试题父类型
  questionParams = new CreateQuestionVo()

  // 默认单选
  private questionsType = QuestionType.enum.radio

  constructor(questionType: QuestionTypeEnum) {
    this.questionsType = questionType
    this.createVoByQuestionType(questionType)
  }

  /**
   * @description: 根据试题类型返回Vo模型
   * @param {QuestionTypeEnum} type
   * @return {*}
   */
  createVoByQuestionType(type: QuestionTypeEnum) {
    const QuestionTypeConstructors = {
      [QuestionTypeEnum.radio]: () => {
        return new CreateRadioQuestionDto()
      },
      [QuestionTypeEnum.multiple]: () => {
        return new CreateMultipleQuestionDto()
      },
      [QuestionTypeEnum.opinion]: () => {
        return new CreateOpinionQuestionDto()
      }
    }
    if (!QuestionTypeConstructors[type]) {
      console.error('选择的试题类型不存在！')
      throw new Error('选择的试题类型不存在！')
    }
    this.questionParams = QuestionTypeConstructors[type]()
    this.questionParams.questionType = type
  }

  /**
   * @description: 创建试题
   * @param {*}
   * @return {*}
   */
  async doCreateQuestion(): Promise<ResponseStatus> {
    const { status } = await MsExamQuestionGateway.createQuestion(this.questionParams.toDto())
    return status
  }
}
export default CreateQuestion
