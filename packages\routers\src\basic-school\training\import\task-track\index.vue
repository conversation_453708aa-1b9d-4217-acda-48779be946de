<!--
 * @Author: z张仁榕
 * @Date: 2025-01-21 17:09:50
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-02-27 09:37:54
 * @Description:
-->
<route-meta>
{
"isMenu": true,
"title": "导入开通结果跟踪",
"sort": 3
}
</route-meta>
<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import {
    // 施教机构管理员
    WXGLY,
    // 专题管理员
    ZTGLY
  } from '@/models/RoleTypes'
  import TaskTrack from '@hbfe/jxjy-admin-import/src/task-track/index.vue'
  @RoleTypeDecorator({
    query: [WXGLY],
    queryTaskWx: [WXGLY],
    queryTaskZt: [ZTGLY],
    queryZt: [ZTGLY],
    importTask: [WXGLY, ZTGLY]
  })
  export default class extends TaskTrack {}
</script>
