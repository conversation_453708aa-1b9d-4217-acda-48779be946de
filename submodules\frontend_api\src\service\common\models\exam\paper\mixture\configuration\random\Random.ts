import { FetchSource } from '@api/service/common/models/exam/paper/mixture/configuration/fixed/Fixed'
import Configuration from '@api/service/common/models/exam/paper/mixture/configuration/Configuration'
import { RandomPaperType } from '@api/service/common/models/exam/enums'

export class FetchConfiguration {
  /**
   * 试题来源，按顺序排列优先级，为空时默认抽取{@link QuestionSourceConstants#normal}普通来源试题
   */
  sources: Array<FetchSource> = new Array<FetchSource>()
  randomTakeConfigurationItems: Array<TakeTagConfigurationItem> = new Array<TakeTagConfigurationItem>()
}

class Random extends Configuration {
  /**
   * 随机类型
   * @see RandomPaperType
   */
  randomType: RandomPaperType = 0
  /**
   * objectId抽题配置
   */
  objectIdFetchConfiguration: FetchConfiguration = new FetchConfiguration()
  /**
   * 指定抽取试题的题型
   */
  questionTypes: Array<number> = new Array<number>()
  /**
   * 是否比率
   */
  ratio = false
  /**
   * 按比率抽题时若与实际所需题量不匹配，则随机增减.
   */
  ratioRandomFit = false
  /**
   * 总题数
   */
  totalQuestionSize = 0
}

export class TakeTagConfigurationItem {
  /**
   * 对象id
   */
  objectId = ''
  /**
   * 对象类型
   **/
  type = ''
  /**
   * 比率值,最大值不大于100,最小不得小于1
   **/
  ratioValue = 0
  /**
   * 按具体数量抽题
   */
  extractCount = 0
}

export default Random
