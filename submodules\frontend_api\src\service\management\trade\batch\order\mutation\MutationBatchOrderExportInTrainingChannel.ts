import QueryBatchOrderListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderListVo'
import { Response } from '@hbfe/common'
import MsDataExportBackstage from '@api/platform-gateway/jxjy-data-export-gateway-backstage'

export default class MutationBatchOrderExportInTrainingChannel {
  exportParams: QueryBatchOrderListVo = new QueryBatchOrderListVo()

  /**
   * 导出批次单列表
   */
  async doBatchOrderExport(): Promise<Response<boolean>> {
    const result = new Response<boolean>()
    const request = await this.exportParams.to()
    const response = await MsDataExportBackstage.exportBatchOrderInTrainingChannel({
      request
    })
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }

  /**
   * 导出批次单明细
   */
  async exportBatchOrderDetailInServicer(): Promise<Response<boolean>> {
    const result = new Response<boolean>()
    const request = await this.exportParams.to()
    const response = await MsDataExportBackstage.exportBatchOrderDetailInTrainingChannel({
      request
    })
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
}
