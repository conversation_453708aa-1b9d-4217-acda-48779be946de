<template>
  <el-main>
    <div class="f-p15">
      <!--第四步-->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <el-row type="flex" justify="center">
          <el-col :sm="20" :lg="12">
            <el-steps :active="4" align-center class="m-steps f-pt40 f-pb10">
              <el-step title="设置专题基础信息"></el-step>
              <el-step title="设置专题门户信息"></el-step>
              <el-step title="设置专题培训方案"></el-step>
              <el-step title="保存专题"></el-step>
            </el-steps>
          </el-col>
        </el-row>
        <el-result icon="success" title="新建专题完成并保存成功！">
          <template slot="extra">
            <div>是否关闭当前页面? <a href="###" class="f-cb f-ml5">立即关闭</a></div>
            <div class="f-mt10">是否继续新建专题?<a href="###" class="f-cb f-ml5">继续新建专题</a></div>
            <div class="f-mt10">是否前往配置专题集体报名?<a href="###" class="f-cb f-ml5">立即前往</a></div>
            <div class="f-mt10">是否前往配置专题精品课程?<a href="###" class="f-cb f-ml5">立即前往</a></div>
          </template>
        </el-result>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
