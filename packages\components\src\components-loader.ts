import { upperFirst, camelCase } from 'lodash'
import { VueConstructor } from 'vue'
import HbSearchWrapper from '@hbfe/jxjy-admin-components/src/search-wrapper'
import HbRangeWrapper from '@hbfe/jxjy-admin-components/src/range-wrapper/index.vue'
import HbBadge from '@hbfe/jxjy-admin-components/src/badge/index.vue'
import HbPopconfirm from '@hbfe/jxjy-admin-components/src/popconfirm/index.vue'
import HbTinymceEditor from '@hbfe/jxjy-admin-components/src/tinymce-editor/index.vue'
import HbBreadCrumb from '@hbfe/jxjy-admin-components/src/bread-crumb.vue'
import Empty from '@hbfe/jxjy-admin-components/src/empty/index.vue'
import '@hbfe/jxjy-admin-components/src/biz/styles/biz.scss'

const requireComponent = require.context(
  // 其组件目录的相对路径
  './biz',
  // 是否查询其子目录
  false,
  // 匹配基础组件文件名的正则表达式
  /biz-(.*?)\.(vue|js)$/
)

export default {
  install(Vue: VueConstructor) {
    ///////////////////////////////////////////////////////////////////////////////
    /// 全局通用组件注册
    /// 通用组件注册前缀名 Hb****
    Vue.component('HbSearchWrapper', HbSearchWrapper)
    Vue.component('HbRangeWrapper', HbRangeWrapper)
    Vue.component('HbBadge', HbBadge)
    Vue.component('HbPopconfirm', HbPopconfirm)
    Vue.component('HbTinymceEditor', HbTinymceEditor)
    Vue.component('HbBreadCrumb', HbBreadCrumb)
    Vue.component('HbEmpty', Empty)
    ///////////////////////////////////////////////////////////////////////////////

    requireComponent.keys().forEach((fileName) => {
      const componentConfig = requireComponent(fileName)
      const componentName = upperFirst(
        // 获取和目录深度无关的文件名
        camelCase(
          fileName
            ?.split('/')
            ?.pop()
            ?.replace(/\.\w+$/, '')
        )
      )
      // 全局注册组件
      Vue.component(
        componentName,
        // 如果这个组件选项是通过 `export default` 导出的，
        // 那么就会优先使用 `.default`，
        // 否则回退到使用模块的根。
        componentConfig.default || componentConfig
      )
    })
  }
}
