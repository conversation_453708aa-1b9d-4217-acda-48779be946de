import QueryOrderBase from '@api/service/management/trade/single/order/query/vo/QueryOrderBase'
import { Page } from '@hbfe/common'
import QueryOrderListVo from '@api/service/management/trade/single/order/query/vo/QueryOrderListVo'
import OrderDetailVo from '@api/service/management/trade/single/order/query/vo/OrderDetailVo'
import OrderStatisticResponseVo from '@api/service/management/trade/single/order/query/vo/OrderStatisticResponseVo'
import MsTradeQuery, {
  OrderBasicDataRequest,
  OrderInfoRequest,
  OrderSortField,
  OrderSortRequest,
  OrderStatisticResponse,
  ReturnOrderRequest,
  SortPolicy,
  SubOrderInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { cloneDeep, uniq } from 'lodash'
import { ReturnOrderStatusEnum } from '@api/service/management/trade/single/order/enum/returnOrderStatusEnum'
import { OrderTransaction } from '@api/service/management/trade/single/order/query/enum/OrderTransactionStatus'
import statisticOrderInTrainingChannel from '@api/service/management/trade/single/order/query/graphql/statisticOrderInTrainingChannel.graphql'
import QueryOrderInTrainingChannelMain from '@api/service/management/trade/single/order/query/QueryOrderInTrainingChannel'
/**
 * 专题管理员查询订单
 */
export default class QueryOrderInTrainingChannel extends QueryOrderInTrainingChannelMain {
  /**
   * 查询订单商品分页
   * @param {Page} page - 分页参数
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryOrderList(
    page: Page,
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderDetailVo[]> {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName || queryParams.loginAccount)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) {
        page.totalSize = 0
        page.totalPageSize = 0
        return [] as OrderDetailVo[]
      }
    }
    const request = await queryParams.to(isBusinessConsult)
    // 过滤批次单产生的订单
    if (!request.orderBasicData) request.orderBasicData = new OrderBasicDataRequest()
    // request.orderBasicData.channelTypesList = [1, 3]
    // request.orderBasicData.orderType = 1
    const option = new OrderSortRequest()
    option.field = OrderSortField.ORDER_NORMAL_TIME
    option.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(option) as OrderSortRequest[]
    const response = await MsTradeQuery.pageOrderInTrainingChannel({
      page,
      request,
      sortRequest
    })
    page.totalSize = response.data?.totalSize
    page.totalPageSize = response.data?.totalPageSize
    const result =
      response.status.isSuccess() && this.isWeightyArr(response.data?.currentPageData)
        ? response.data?.currentPageData?.map(OrderDetailVo.from)
        : ([] as OrderDetailVo[])
    // console.log('filterOrderList', result, response)
    const res = await this.fillBuyerInfo(result)
    // * 添加退款订单信息
    if (isBusinessConsult && response?.data?.currentPageData?.length) {
      let orderIds = response.data.currentPageData.map((order) => {
        if (order.orderNo) {
          return order.orderNo
        }
      })

      if (orderIds.length) {
        orderIds = uniq(orderIds)
        const request = new ReturnOrderRequest()
        request.subOrderInfo = new SubOrderInfoRequest()
        request.subOrderInfo.orderInfo = new OrderInfoRequest()
        request.subOrderInfo.orderInfo.orderNoList = orderIds
        const newPage = new Page()
        newPage.pageNo = 1
        newPage.pageSize = page.pageSize
        const response = await MsTradeQuery.pageReturnOrderInTrainingChannel({
          page: newPage,
          request
        })

        res.map(async (item) => {
          const curOrder = response.data.currentPageData.find(
            (res) => res.subOrderInfo.orderInfo.orderNo === item.orderNo
          )
          if (curOrder?.returnOrderNo) {
            if ([0, 1, 2, 4, 5, 6].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunding
            }
            if ([8, 9, 10].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunded
            }
            if ([3, 7].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refundFailure
            }
          } else {
            item.returnOrderStatus = ReturnOrderStatusEnum.noRefund
          }
          if (!item.receiveAccountType) {
            const res = await MsTradeQuery.getBatchOrderInServicer(item.batchOrderNo)
            item.receiveAccountType = res.data.payInfo.receiveAccount.receiveAccountType == 1 ? 0 : 1
            item.payChannelName = res.data.payInfo.receiveAccount.payChannelName
          }
        })
      }
    }
    return res
  }
}
