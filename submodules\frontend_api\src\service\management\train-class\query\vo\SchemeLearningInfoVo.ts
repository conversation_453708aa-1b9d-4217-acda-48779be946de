import StudentCourseVo from '@api/service/management/resource/course/query/vo/StudentCourseVo'
import StudentCoursewareRecordDetailVo from '@api/service/management/resource/courseware/query/vo/StudentCoursewareRecordDetailVo'

/**
 * @description 学习信息模型
 */
class SchemeLearningInfoVo {
  /**
   * 父级id，仅课件一级有值
   */
  parentId = ''

  /**
   * 资源id，不区分课件、课程
   */
  id = ''

  /**
   * 资源名称，不区分课件、课程
   */
  name = ''
  /**
   * 学员课程id
   */
  studentCourseId = ''
  /**
   * 课程学时
   */
  period = 0

  /**
   * 资源学习进度，不区分课件、课程
   */
  schedule = 0

  /**
   * 资源开始学习时间，不区分课件、课程
   */
  startLearningTime = ''

  /**
   * 资源最后学习时间，不区分课件、课程
   */
  lastLearningTime = ''

  /**
   * 课程学习状态 0：未评定 1：未合格 2：合格
   */
  status: number

  /**
   * 课程学习取得结果时间
   */
  learningResultTime: string

  /**
   * 测验得分
   */
  quizScore = 0

  /**
   * 是否测验合格 0：未评定 1：未合格 2：合格
   */
  courseQuizStatus: number = null

  /**
   * 课程学习方式资源类型(1：选课学习场景 2：自主课程学习场景 3：兴趣课程学习场景)
   */
  courseLearningResourceType: string = null

  /**
   * 大纲内课程类型(1：必修 2：选修 3：兴趣课)，仅当课程学习方式为选课规则时有效
   */
  courseType: number = null

  /**
   * 课程时长
   */
  courseTimeLength = 0

  /**
   *  课件时长
   */
  coursewareTimeLength = 0

  /**
   * 资源子级信息
   */
  children: SchemeLearningInfoVo[] = []

  /**
   * 资源类型 1；课程 2：课件
   */
  resourceType: number = null
  /**
   * 课程大纲id
   */
  outlineId = ''
  /**
   * 分类信息
   */
  classifyInfo = ''

  /**
   * 是否有模拟数据
   */
  haveSimulate: boolean = undefined
  /**
   * 是否只有模拟考试数据
   */
  onlyHaveExamSimulate: boolean = undefined
  /**
   * 模拟数据课程开始时间
   */
  simulateCourseStartTime: string = undefined

  /**
   * 模拟数据课程结束事件
   */
  simulateCourseEndTime: string = undefined

  static fromCourse(response: StudentCourseVo): SchemeLearningInfoVo {
    const detail = new SchemeLearningInfoVo()
    detail.id = response.id
    detail.studentCourseId = response.studentCourseId
    detail.name = response.name
    detail.period = response.period
    detail.schedule = response.schedule
    detail.startLearningTime = response.startLearningTime
    detail.lastLearningTime = response.lastLearningTime
    detail.status = response.status
    detail.learningResultTime = response.learningResultTime
    detail.quizScore = response.quizScore
    detail.courseQuizStatus = response.courseQuizStatus
    detail.courseLearningResourceType = response.courseLearningResourceType
    detail.courseType = response.courseType
    detail.courseTimeLength = response.courseTimeLength
    detail.children = []
    detail.resourceType = 1
    detail.outlineId = response.outlineId
    return detail
  }

  static formCourseware(response: StudentCoursewareRecordDetailVo): SchemeLearningInfoVo {
    const detail = new SchemeLearningInfoVo()
    detail.id = response.id
    detail.name = response.name
    detail.schedule = response.schedule
    detail.startLearningTime = response.startLearningTime
    detail.lastLearningTime = response.lastLearningTime
    detail.resourceType = 2
    detail.children = undefined
    detail.coursewareTimeLength = response.coursewareTimeLength
    return detail
  }
}

export default SchemeLearningInfoVo
