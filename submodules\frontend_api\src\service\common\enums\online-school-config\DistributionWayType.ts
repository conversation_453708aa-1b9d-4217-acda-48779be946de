import AbstractEnum from '../AbstractEnum'

enum DistributionWayEnum {
  /**
   * 快递
   */
  EXPRESS = 1,
  /**
   * 自取点
   */
  TAKE_PLACE = 2
}

export { DistributionWayEnum }

class DistributionWayType extends AbstractEnum<DistributionWayEnum> {
  static enum = DistributionWayEnum
  constructor() {
    super()
    this.map[DistributionWayEnum.EXPRESS] = '快递'
    this.map[DistributionWayEnum.TAKE_PLACE] = '自取点'
  }
}

export default DistributionWayType
