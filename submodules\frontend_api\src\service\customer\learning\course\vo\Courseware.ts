import { CoursewarePlayResourceResponse } from '@api/ms-gateway/ms-course-play-resource-v1'
import moment from 'moment'

class Courseware {
  id: string
  // 名称
  name: string
  // 时长
  timeLength: number
  // 时长转换 HH:mm:ss
  timeLengthFormat: string
  // 进度
  schedule = 0
  // 体验类型
  trialType: number
  // 媒体类型
  type: number
  // 是否允许试听
  isAllowAudition: boolean
  // 课件媒体编号
  mediaResourceId: string
  multiMediaId: string

  belongChapterId: string

  sort: number

  setSchedule(schedule: number) {
    this.schedule = schedule
  }

  static from(response: CoursewarePlayResourceResponse, sort?: number) {
    const detail = new Courseware()
    detail.id = response.id
    detail.name = response.name
    detail.timeLength = response.timeLength
    const mom = moment.duration(detail.timeLength * 1000)
    const hour = `${mom.get('hours')}`.padStart(2, '0')
    const minutes = `${mom.get('minutes')}`.padStart(2, '0')
    const seconds = `${mom.get('seconds')}`.padStart(2, '0')
    detail.timeLengthFormat = `${hour}:${minutes}:${seconds}`
    detail.mediaResourceId = response.mediaResourceId
    detail.type = response.type
    detail.sort = sort
    return detail
  }
}

export default Courseware
