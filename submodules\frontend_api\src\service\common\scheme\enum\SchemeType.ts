import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 方案类型枚举
 * training_scheme 培训班
 * training_cooperation 合作办学
 */
export enum SchemeTypeEnum {
  training_scheme = 'training_scheme',
  cooperation = 'cooperation'
}

/**
 * @description 方案类型
 */
class SchemeType extends AbstractEnum<SchemeTypeEnum> {
  static enum = SchemeTypeEnum

  constructor(status?: SchemeTypeEnum) {
    super()
    this.current = status
    this.map.set(SchemeTypeEnum.training_scheme, '培训班')
    this.map.set(SchemeTypeEnum.cooperation, '合作办学')
  }
}

export default new SchemeType()
