<template>
  <div
    class="f-p15"
    v-if="$hasPermission('trainingResultList')"
    desc="面网授培训成果列表"
    actions="created,@OnlineTutoring,@MixTutoring"
  >
    <template v-if="type === TrainingModeEnum.online">
      <online-tutoring :training-mode="type" :scheme-id="schemeId" :training-results-manage="trainingResultsManage" />
    </template>
    <template v-else-if="type === TrainingModeEnum.mixed || type === TrainingModeEnum.offline">
      <mix-tutoring :training-mode="type" :training-results-manage="trainingResultsManage" />
    </template>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import OnlineTutoring from '@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/online-tutoring.vue'
  import MixTutoring from '@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/mix-tutoring.vue'
  import TrainingResultsManage from '@api/service/management/implement/TrainingResultsManage'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

  @Component({
    computed: {
      TrainingModeEnum() {
        return TrainingModeEnum
      }
    },
    components: { MixTutoring, OnlineTutoring }
  })
  export default class extends Vue {
    @Prop({
      type: String,
      required: true
    })
    schemeId: string
    /**
     * 页面模型
     */
    trainingResultsManage: TrainingResultsManage = null

    get type() {
      return this.trainingResultsManage.schemeType
    }

    created() {
      this.trainingResultsManage = new TrainingResultsManage(this.schemeId)
      this.trainingResultsManage.getSchemeType()
    }
  }
</script>
