<template>
  <el-card shadow="never" class="m-card f-mb15">
    <div slot="header" class="">
      <span class="tit-txt">基础信息</span>
    </div>
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :md="20" :lg="16" :xl="13">
        <el-form ref="form" label-width="150px" class="m-text-form is-column">
          <el-form-item label="培训形式：">{{ getTrainingMode() }}</el-form-item>
          <el-form-item label="方案类型：">{{ schemeType(trainClassBaseInfo) }}</el-form-item>
          <el-form-item label="方案封面：">
            <el-image
              :src="getMFSImage(trainClassBaseInfo.picture)"
              :preview-src-list="[getMFSImage(trainClassBaseInfo.picture)]"
              class="course-pic"
            />
            <!--<p class="f-cr f-mt10">注：图片比例为16:9，请选择符合该比例的图片。</p>-->
          </el-form-item>
          <el-form-item label="方案名称：">
            {{ trainClassBaseInfo.name }}
          </el-form-item>
          <el-form-item label="年度：">{{ skuPropertyName('year') }}</el-form-item>
          <el-form-item label="地区：">{{ skuPropertyName('region') }}</el-form-item>
          <el-form-item label="行业：">{{ skuPropertyName('industry') }}</el-form-item>
          <el-form-item label="学段：" v-show="skuPropertyName('learningPhase')">{{
            skuPropertyName('learningPhase')
          }}</el-form-item>
          <el-form-item label="学科：" v-show="skuPropertyName('discipline')">{{
            skuPropertyName('discipline')
          }}</el-form-item>
          <el-form-item label="科目类型：" v-show="skuPropertyName('subjectType')">{{
            skuPropertyName('subjectType')
          }}</el-form-item>
          <el-form-item label="执业类别：" v-show="skuPropertyName('certificatesType')"
            >{{ skuPropertyName('certificatesType') }}/{{ skuPropertyName('practitionerCategory') }}</el-form-item
          >
          <el-form-item label="培训类别：" v-show="!isSocietyIndustry && hasTrainingCategory">
            {{ hasTrainingCategory ? skuPropertyName('trainingCategory') : '' }}
          </el-form-item>
          <el-form-item label="培训专业：" v-show="getTrainingMajor()"> {{ getTrainingMajor() }} </el-form-item>
          <el-form-item label="技术等级：" v-show="skuPropertyName('jobLevel')">{{
            skuPropertyName('jobLevel')
          }}</el-form-item>
          <el-form-item label="培训对象：" v-show="skuPropertyName('trainingObject')">{{
            skuPropertyName('trainingObject')
          }}</el-form-item>
          <el-form-item label="岗位类别：" v-show="skuPropertyName('positionCategory')">{{
            skuPropertyName('positionCategory')
          }}</el-form-item>
          <el-form-item v-if="schoolConfigFlag" label="成果是否同步：">
            {{ needDataSync }}
          </el-form-item>
          <el-form-item
            v-if="schemeDetail.trainClassBaseInfo.needDataSync && schoolConfigFlag"
            label="成果开始时间规则："
          >
            {{ needDataSyncRule }}
          </el-form-item>
          <el-form-item label="培训须知：" v-show="trainClassBaseInfo.notice">
            {{ trainClassBaseInfo.notice }}
          </el-form-item>
          <el-form-item label="是否在进班时弹窗：" v-show="trainClassBaseInfo.notice">
            {{ trainClassBaseInfo.showNoticeDialog ? '是' : '否' }}
          </el-form-item>
          <el-form-item label="培训方案简介：" v-show="trainClassBaseInfo.introContent">
            <div class="m-intro" v-html="trainClassBaseInfo.introContent"></div>
          </el-form-item>
          <slot name="third-platform"></slot>
        </el-form>
      </el-col>
    </el-row>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, PropSync } from 'vue-property-decorator'
  import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import getServicerIsDocking from '@api/service/management/online-school-config/portal/query/QueryPortal'
  import { isBoolean } from 'lodash'
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import AchievementExhibition, {
    AchievementExhibitionEnum
  } from '@api/service/common/enums/train-class/AchievementExhibitionEnum'
  import TrainingMode from '@api/service/common/scheme/enum/TrainingMode'
  import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
  import SchemeDetail from '@api/service/common/scheme/model/SchemeDetail'

  @Component
  export default class extends Vue {
    /**
     * 状态层定义方案类型
     */
    modelSchemeType = SchemeType
    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: TrainClassDetailClassVo }) schemeDetail: TrainClassDetailClassVo
    schoolConfigFlag = false
    /**
     * 基础信息
     */
    get trainClassBaseInfo() {
      return this.schemeDetail.trainClassBaseInfo
    }

    /**
     * sku属性
     */
    get skuProperties() {
      return this.schemeDetail.trainClassBaseInfo.skuProperty
    }

    /**
     * 是否是人社行业
     */
    get isSocietyIndustry() {
      // TODO 待优化
      return this.schemeDetail.trainClassBaseInfo.skuProperty.industry.skuPropertyName === '人社行业' ? true : false
    }

    /**
     * 成果同步规则
     */
    get needDataSyncRule() {
      return AchievementExhibition.map.get(this.schemeDetail.trainClassBaseInfo.achievementExhibition)
    }

    /**
     * 是否配置培训类别
     */
    get hasTrainingCategory() {
      return this.skuProperties.trainingCategory.skuPropertyName &&
        this.skuProperties.trainingCategory.skuPropertyValueId
        ? true
        : false
    }

    /**
     * 获取sku属性值
     */
    get skuPropertyName() {
      return (type: string) => {
        return this.skuProperties[type]?.skuPropertyName ? this.skuProperties[type].skuPropertyName : ''
      }
    }
    /**
     * 获取成果是否同步字段
     */
    get needDataSync() {
      const needDataSync = this.schemeDetail.trainClassBaseInfo.needDataSync
      if (!needDataSync && isBoolean(needDataSync)) {
        return '不同步'
      } else {
        // 旧数据默认显示同步
        return '同步'
      }
    }
    /**
     * 获取培训方案类型
     */
    getSchemeType(type: number) {
      if (type === 1) return '培训班-选课规则'
      else if (type === 2) return '培训班-自主选课'
      else if (type === 3) return '培训合作'
      else return ''
    }
    getTrainingMode() {
      return TrainingMode.map.get(this.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId)
    }

    /**
     * 培训形式
     */
    get schemeType() {
      return (item: TrainClassBaseModel) => {
        return SchemeDetail.getSchemeTypeShowText({
          trainingMode: item.skuProperty.trainingMode.skuPropertyValueId,
          schemeType: item.schemeType,
          isShowTrainingMode: false
        })
      }
    }
    /**
     * 获取封面图片
     */
    getMFSImage(imageUrl: string) {
      return CreateSchemeUtils.getMFSImage(imageUrl)
    }

    /**
     * 获取建设行业培训专业
     */
    getTrainingMajorName(): string {
      const stringArr = this.skuProperties?.trainingMajor?.skuPropertyName?.split('/')
      return Array.isArray(stringArr) && stringArr.length ? stringArr[stringArr.length - 1] : ''
    }
    getTrainingMajor(): string {
      return this.isSocietyIndustry ? this.skuPropertyName('trainingMajor') : this.getTrainingMajorName()
    }
    async created() {
      this.schoolConfigFlag = await getServicerIsDocking.getServicerIsDocking()
    }
  }
</script>
