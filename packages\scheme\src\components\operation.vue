<route-params content="/:schemeId"></route-params>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right" v-if="routerMode === 3">
      <el-button type="text" size="mini" class="return-btn" @click.stop="cancel">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: backUrl }">{{ breadcrumbTitle }}</el-breadcrumb-item>
      <el-breadcrumb-item>修改培训方案</el-breadcrumb-item>
    </el-breadcrumb>
    <!--处于考核重算场景下，提示语-->
    <el-alert
      type="warning"
      :closable="false"
      class="m-alert is-border-bottom"
      v-if="routerMode === 3 && recalculating"
    >
      <span class="f-fb">修改提示：</span>
      <span class="f-c6">系统正在处理上一次修改方案的重算任务，处理过程中仅支持修改培训方案基础信息。 </span>
    </el-alert>
    <!--处于智能学习场景下，提示语-->
    <el-alert
      type="warning"
      :closable="false"
      class="m-alert is-border-bottom"
      v-if="routerMode === 3 && trainClassCommodityInfo.trainClassBaseInfo.isIntelligenceLearning"
    >
      <span class="f-fb">修改提示：</span>
      <span class="f-c6">系统在为本方案下指定已报名学员进行智能学习，处理过程中仅支持修改培训方案基础信息。</span>
    </el-alert>
    <div class="f-p15">
      <basic-info
        ref="basicInfoRef"
        v-if="show"
        :basicInfo.sync="trainClassCommodityInfo.trainClassBaseInfo"
        :learningType="trainClassCommodityInfo.learningTypeModel"
        :defaultSchemeType.sync="defaultSchemeType"
        :recalculating="recalculating"
        :isIntelligenceLearning="trainClassCommodityInfo.trainClassBaseInfo.isIntelligenceLearning"
        @schemeType="getSchemeType"
        @schemeFormChange="schemeFormChange"
        v-bind="$attrs"
      ></basic-info>
      <learn-way
        v-if="show"
        v-show="defaultSchemeType === 'train_class'"
        :defaultSchemeType.sync="defaultSchemeType"
        :basicInfo.sync="trainClassCommodityInfo.trainClassBaseInfo"
        :learningType.sync="trainClassCommodityInfo.learningTypeModel"
        :learningTypeModelCopy="trainClassCommodityInfo.learningTypeModelCopy"
        ref="learningWayRef"
        :router-mode="routerMode"
        :recalculating="recalculating"
        :isIntelligenceLearning="trainClassCommodityInfo.trainClassBaseInfo.isIntelligenceLearning"
        :commodity-info="trainClassCommodityInfo"
        v-bind="$attrs"
      ></learn-way>
      <training-require
        ref="trainingRequireRef"
        v-if="show"
        :router-mode="routerMode"
        :commodity.sync="trainClassCommodityInfo"
        :recalculating="recalculating"
        :isIntelligenceLearning="trainClassCommodityInfo.trainClassBaseInfo.isIntelligenceLearning"
        v-bind="$attrs"
      ></training-require>
      <stage ref="stageRef" v-if="show" :router-mode="routerMode" :commodity.sync="trainClassCommodityInfo"></stage>
      <div class="m-btn-bar is-sticky-1 f-tc">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="publishScheme(false)">发布</el-button>
      </div>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch, Provide } from 'vue-property-decorator'
  import BasicInfo from '@hbfe/jxjy-admin-scheme/src/components/basic-info.vue'
  import LearnWay from '@hbfe/jxjy-admin-scheme/src/components/learn-way.vue'
  import TrainingRequire from '@hbfe/jxjy-admin-scheme/src/components/training-require.vue'
  import Stage from '@hbfe/jxjy-admin-scheme/src/components/stage.vue'
  import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
  import MutationCreateTrainClassCommodity from '@api/service/management/train-class/mutation/MutationCreateTrainClassCommodity'
  import { CreateTrainClassTypeEnum } from '@api/service/management/train-class/mutation/Enum/CreateTrainClassTypeEnum'
  import { cloneDeep } from 'lodash'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import { bind, debounce } from 'lodash-decorators'
  import CreateSchemeUIModule from '@/store/modules-ui/scheme/CreateSchemeUIModule'
  import MutationTrainClassFactory from '@api/service/management/train-class/MutationTrainClassFactory'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import BasicDataQueryForestage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
  import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
  import RootModule from '@/store/RootModule'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import TrainingPlaceManage from '@api/service/management/resource/training-place-manage/TrainingPlaceManage'
  import QuestionnaireList from '@api/service/management/resource/question-naire/QuestionnaireList'
  import { Page, UiPage } from '@hbfe/common'
  import QuestionnaireAppliedRangeType, {
    QuestionnaireAppliedRangeTypeEnum
  } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'

  @Component({
    components: { BasicInfo, LearnWay, TrainingRequire, Stage }
  })
  export default class extends Vue {
    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    routerMode = 1

    /**
     * 是否展示组件
     */
    show = false

    /**
     * 基础信息
     */
    @Ref('basicInfoRef') basicInfoRef: BasicInfo

    /**
     * 学习内容
     */
    @Ref('learningWayRef') learningWayRef: LearnWay

    /**
     * 培训要求
     */
    @Ref('trainingRequireRef') trainingRequireRef: TrainingRequire

    /**
     * 学习有效期配置
     */
    @Ref('stageRef') stageRef: Stage

    /**
     * 定时器创建，修改查询定时器
     */
    timer: any
    /**
     * loading遮罩层
     */
    loading: any
    /**
     * 进入来源
     * topicScheme 专题页
     */
    from = ''
    /**
     * 经纬度
     */
    trainingPlaceManage = new TrainingPlaceManage()

    /**
     * 培训班商品信息
     */
    trainClassCommodityInfo: MutationCreateTrainClassCommodity = new MutationCreateTrainClassCommodity()
    @Provide('getLearningTypeModelCopy')
    getLearningTypeModelCopy() {
      return this.trainClassCommodityInfo.learningTypeModelCopy
    }
    @Provide('getLearningTypeModel')
    getLearningTypeModel() {
      return this.trainClassCommodityInfo.learningTypeModel
    }
    @Provide('getTrainClassBaseInfo')
    getTrainClassBaseInfo() {
      return this.trainClassCommodityInfo.trainClassBaseInfo
    }
    MutationTrainClassFactory = new MutationTrainClassFactory()
    // 培训方案状态层入口
    QueryTrainClassCommodityList = new QueryTrainClassCommodityList()

    /**
     * 培训班类型默认选中“培训班”
     */
    defaultSchemeType: 'train_class' | 'train_cooperate' = 'train_class'
    /**
     * 培训方案是否处于考核重算
     */
    recalculating = false

    // @Watch('trainClassCommodityInfo', {
    //   immediate: true,
    //   deep: true
    // })
    // trainClassCommodityInfoChange(val: any) {
    //   console.log('trainClassCommodityInfo', val)
    // }

    get breadcrumbTitle() {
      return this.from === 'topicScheme' ? '专题管理' : '培训方案管理'
    }

    get backUrl() {
      return this.from === 'topicScheme' ? '/training/special-topics/manage' : '/training/scheme/manage'
    }
    /**
     * 监听培训班类型，当选中培训合作时，清空学习方式组件
     * @param newVal
     */
    @Watch('defaultSchemeType')
    onDefaultSchemeTypeChanged(newVal: 'train_class' | 'train_cooperate') {
      if (newVal === 'train_cooperate') {
        ;['courseLearning', 'exam', 'practiceLearning', 'interestCourse', 'learningExperience'].forEach(
          (compKey, index) => {
            if (this.learningWayRef.uiLearningTypeSelect[index].value) {
              this.learningWayRef.uiLearningTypeSelect[index].value = false
              this.learningWayRef?.handleLearningTypeChange(compKey, false)
            }
          }
        )
      }
    }

    /**
     * 组件激活响应
     */
    async activated() {
      this.show = false
      CreateSchemeUIModule.setSchemeType(0)
      this.from = (this.$route.query.source as string) || ''
      // 配置路由模式，根据路由参数获取当前模式：创建/更新/复制培训班商品类
      const routerPathArr = this.$route.fullPath?.split('/') || []
      const commodityId = (this.$route.params?.schemeId as string) || ''
      if (routerPathArr.indexOf('modify') > -1) {
        this.routerMode = 3
        this.trainClassCommodityInfo = await this.MutationTrainClassFactory.getMutationCreateTrainClassCommodity(
          CreateTrainClassTypeEnum.CreateTrainClassTypeEnumUpdate,
          commodityId
        )
        const statusRes = await this.QueryTrainClassCommodityList.querySchemeStatus(
          this.trainClassCommodityInfo.trainClassBaseInfo.id
        )
        this.recalculating = statusRes?.recalculating
      } else {
        if (commodityId && commodityId !== ':schemeId') {
          this.routerMode = 2
          this.trainClassCommodityInfo = await this.MutationTrainClassFactory.getMutationCreateTrainClassCommodity(
            CreateTrainClassTypeEnum.CreateTrainClassTypeEnumCopy,
            commodityId
          )
        } else {
          this.routerMode = 1
          this.trainClassCommodityInfo = await this.MutationTrainClassFactory.getMutationCreateTrainClassCommodity(
            CreateTrainClassTypeEnum.CreateTrainClassTypeEnumCreate
          )
        }
      }
      if (
        this.trainClassCommodityInfo.learningTypeModel.issue.isSelected &&
        this.trainClassCommodityInfo.learningTypeModel.issue.issueConfigList
      ) {
        const trainingPlaceInfo = await new TrainingPlaceManage().queryTrainingPlacesByIds(
          this.trainClassCommodityInfo.learningTypeModel.issue.issueConfigList.map((item) => item.trainingPointId)
        )
        this.trainClassCommodityInfo.learningTypeModel.issue.issueConfigList.forEach((item) => {
          item.trainingPointName = trainingPlaceInfo.get(item.trainingPointId)?.trainingPlaceName
        })
      }
      if (
        this.trainClassCommodityInfo.learningTypeModel.questionnaire.isSelected &&
        this.trainClassCommodityInfo.learningTypeModel.questionnaire.questionnaireConfigList.length
      ) {
        const templateIds = this.trainClassCommodityInfo.learningTypeModel.questionnaire.questionnaireConfigList?.map(
          (item) => item.templateId
        )
        if (templateIds?.length) {
          const questionnaireQueryList = new QuestionnaireList()
          questionnaireQueryList.params.templateIds = templateIds
          await questionnaireQueryList.queryList(new Page(1, templateIds.length))
          console.log(questionnaireQueryList.list, templateIds)
          this.trainClassCommodityInfo.learningTypeModel.questionnaire.questionnaireConfigList.forEach((item) => {
            item.templateName = questionnaireQueryList.list?.find((q) => q.id === item.templateId)?.name ?? ''
          })
        }
      }
      CreateSchemeUIModule.setCreateMode(this.routerMode)
      CreateSchemeUIModule.setSchemeType(this.trainClassCommodityInfo.trainClassBaseInfo.schemeType)
      console.log(this.trainClassCommodityInfo, 'trainClassCommodityInfo')
      // 绑定的是异步数据，加载后再显示
      this.show = true
      // this.$nextTick(async () => {
      //   await this.basicInfoRef?.queryInstitutionList()
      // })
    }

    /**
     * 表单校验 - 发布培训方案前置校验
     */
    validateForm() {
      let result = this.basicInfoRef.validateForm()
      if (!result) {
        this.$message.error('请输入基础信息必填项')
        return false
      }
      result = this.learningWayRef.validateForm()
      if (!result) {
        return false
      }
      result = this.trainingRequireRef.validateForm()
      if (!result) {
        return false
      }
      result = this.stageRef.validateForm()
      return result
    }

    /**
     * 经纬度赋值
     */
    async setLocation() {
      const ids: string[] = []
      this.trainClassCommodityInfo.learningTypeModel.issue.issueConfigList.map((item) => {
        if (!item.trainingPointLng || !item.trainingPointLat || !item.trainingPointName) {
          ids.push(item.trainingPointId)
        }
      })
      if (ids.length) {
        const page = new UiPage()
        page.pageSize = ids.length
        page.pageNo = 1
        this.trainingPlaceManage.params.ids = ids
        await this.trainingPlaceManage.queryList(page)
        this.trainClassCommodityInfo.learningTypeModel.issue.issueConfigList =
          this.trainClassCommodityInfo.learningTypeModel.issue.issueConfigList.map((item) => {
            const trainingPlace = this.trainingPlaceManage.list.find((ite) => ite.id === item.trainingPointId)
            if (trainingPlace && (!item.trainingPointLng || !item.trainingPointLat || !item.trainingPointName)) {
              item.trainingPointName = trainingPlace.trainingPlaceName
              item.trainingPointLng = trainingPlace.longitude.toString()
              item.trainingPointLat = trainingPlace.latitude.toString()
            }
            return item
          })
      }
    }
    /**
     * 发布培训方案
     */
    @bind
    @debounce(200)
    async publishScheme(isUpdate?: boolean) {
      console.log(this.routerMode, isUpdate, 'this.routerMode !== 3 || isUpdate')

      if (this.routerMode !== 3 || isUpdate) {
        if (this.routerMode === 3) {
          const statusRes = await this.QueryTrainClassCommodityList.querySchemeStatus(
            this.trainClassCommodityInfo.trainClassBaseInfo.id
          )
          if (statusRes.recalculating || statusRes.lastTransactionStep !== 5) {
            return this.$confirm('方案正在重算中，无法修改', '系统提醒', {
              confirmButtonText: '确定',
              type: 'warning'
            })
          }
          // else if (statusRes.lastTransactionStep !== 5) {
          //   return this.$confirm('方案正在处理中，无法修改', '系统提醒', {
          //     confirmButtonText: '确定',
          //     type: 'warning'
          //   })
          // }
          else if (statusRes?.hangUp) {
            const errMsg = (statusRes?.errors?.find((ite) => ite.message)?.message || '方案异常') + ',无法修改'
            return this.$confirm(errMsg, '系统提醒', {
              confirmButtonText: '确定',
              type: 'error'
            })
          }
        }
        await this.setLocation()

        if (
          this.trainClassCommodityInfo.trainClassBaseInfo.skuProperty?.trainingMode?.skuPropertyValueId !==
          TrainingModeEnum.online
        ) {
          this.trainClassCommodityInfo.closeCustomerPurchase = false
        }
        // 各个表单的最终校验结果
        const result = this.validateForm()

        if (!result) return
        const MutationCreateTrainClassCommodity: MutationCreateTrainClassCommodity = cloneDeep(
          this.trainClassCommodityInfo
        )
        const trainingBeginDate = MutationCreateTrainClassCommodity.trainClassBaseInfo.trainingBeginDate
        const trainingEndDate = MutationCreateTrainClassCommodity.trainClassBaseInfo.trainingEndDate
        // 没有学习开始时间和学习结束时间，说明是长期有效
        if (!trainingEndDate && !trainingBeginDate) {
          MutationCreateTrainClassCommodity.trainClassBaseInfo.trainingBeginDate = CreateSchemeUtils.defaultBeginDate
          MutationCreateTrainClassCommodity.trainClassBaseInfo.trainingEndDate = CreateSchemeUtils.defaultEndDate
        }
        // 考试未勾选时，不支持整班重学
        if (!MutationCreateTrainClassCommodity.learningTypeModel.exam.isSelected) {
          MutationCreateTrainClassCommodity.trainClassBaseInfo.provideRelearn = false
        }
        this.loading = this.$loading({
          lock: true,
          text: '加载中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.8)'
        })
        await QueryIndustry.queryIndustry()
        if (
          !QueryIndustry.industryList.find((item) => {
            return item.id.includes(
              this.trainClassCommodityInfo.trainClassBaseInfo.skuProperty.industry?.skuPropertyValueId
            )
          }) &&
          this.routerMode == 2
        ) {
          this.loading.close()
          return this.$confirm('当前网校暂不支持该行业培训，请重新选择正确的行业信息。', '系统提醒', {
            confirmButtonText: '确定',
            type: 'warning'
          })
        }
        const response = await BasicDataQueryForestage.getServiceOrIndustryRegion(1)
        // console.log(
        //   this.trainClassCommodityInfo.trainClassBaseInfo.skuProperty.region.skuPropertyValueId,
        //   response.data,
        //   'this.trainClassCommodityInfo'
        // )
        if (
          !response.data.find((item) => {
            return item.code == this.trainClassCommodityInfo.trainClassBaseInfo.skuProperty.region.skuPropertyValueId
          }) &&
          this.routerMode == 2
        ) {
          this.loading.close()
          return this.$confirm('当前网校暂不支持该地区培训，请重新选择正确的地区信息。', '系统提醒', {
            confirmButtonText: '确定',
            type: 'warning'
          })
        }
        // 开启报名时间设置立即开启要设置报名开启时间为当前时间

        try {
          if (this.recalculating || this.trainClassCommodityInfo.trainClassBaseInfo.isIntelligenceLearning) {
            MutationCreateTrainClassCommodity.isBaseInfoModify = true
          }
          const res = await MutationCreateTrainClassCommodity.createTrainClassCommodity()
          // if (res?.data?.illegalQuestionnaires?.length) {
          //   this.trainClassCommodityInfo.learningTypeModel.questionnaire.questionnaireConfigList
          //     .filter((item) => res.data.illegalQuestionnaires?.map((q) => q.name)?.includes(item.questionnaireName))
          //     .forEach((item, index) => {
          //       setTimeout(() => {
          //         this.$message.error(
          //           `【${
          //             item.questionnaireName
          //           }】选择问卷模板中含有教师评价试题，不支持应用于${QuestionnaireAppliedRangeType.map.get(
          //             item.appliedRangeType
          //           )}，请替换模板或者重新选择问卷应用范围。`
          //         )
          //       }, index * 100)
          //     })
          // } else
          if (res.status.isSuccess()) {
            const asyncGetStatus = () =>
              new Promise((resolve, reject) => {
                const timerFn = async () => {
                  //
                  const statusRes = await this.QueryTrainClassCommodityList.querySchemeStatus(res.data.schemeId)
                  if (
                    statusRes?.lastTransactionStep === 5 &&
                    (!statusRes?.errors || statusRes?.errors?.length === 0) &&
                    !statusRes?.hangUp &&
                    !statusRes?.recalculating
                  ) {
                    clearTimeout(this.timer)
                    resolve('操作成功')
                  } else if (statusRes?.hangUp) {
                    clearTimeout(this.timer)
                    const errMsg = this.routerMode === 3 ? '方案修改中，请稍后。' : '创建失败'
                    this.$confirm(errMsg, '系统提醒', {
                      confirmButtonText: '确定',
                      type: 'warning'
                    }).then(() => {
                      this.goToSchemeList()
                    })
                    reject(errMsg)
                  } else if (statusRes?.errors?.length) {
                    let errMsg = statusRes.errors.find((err) => !!err)?.message || '操作失败'
                    if (statusRes.errors.find((err) => !!err)?.code === '51001') {
                      this.$confirm(errMsg, '提示', {
                        confirmButtonText: '确定',
                        showCancelButton: false
                      })
                    }
                    if (statusRes.errors.some((err) => err?.code === '81004')) {
                      errMsg = '结束报到时间不能是过去时间'
                    }
                    if (statusRes.errors.some((err) => err?.code === '81003')) {
                      errMsg = '开始报到时间不能是过去时间'
                    }
                    clearTimeout(this.timer)
                    reject(errMsg)
                  } else {
                    this.timer = setTimeout(timerFn, 2000)
                  }
                }
                timerFn()
              })
            await asyncGetStatus().then(() => {
              this.$message.success('操作成功')
              if (
                this.routerMode !== 3 &&
                (this.trainClassCommodityInfo.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId ===
                  TrainingModeEnum.mixed ||
                  this.trainClassCommodityInfo.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId ===
                    TrainingModeEnum.offline) &&
                this.trainClassCommodityInfo.learningTypeModel.issue.issueConfigList.some(
                  (item) =>
                    item.isOpenCheck ||
                    item.isOpenGraduationTest ||
                    item.isOpenAttendance ||
                    item.isOpenAccommodationInfoCollect
                )
              ) {
                this.$confirm('当前方案已发布，您还需前往实施管理-训前实施模块完善培训相关配置！', '提示', {
                  confirmButtonText: '立即前往',
                  cancelButtonText: '暂不前往'
                })
                  .then(() => {
                    this.$router.push(`/training/scheme/implementingManagement/${res.data.schemeId}`)
                  })
                  .catch(() => {
                    this.goToSchemeList()
                  })
              } else if (
                this.routerMode === 3 &&
                (this.trainClassCommodityInfo.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId ===
                  TrainingModeEnum.mixed ||
                  this.trainClassCommodityInfo.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId ===
                    TrainingModeEnum.offline) &&
                this.trainClassCommodityInfo.learningTypeModel.issue.issueConfigList.some((item) => {
                  const find = this.trainClassCommodityInfo.learningTypeModelCopy.issue.issueConfigList.find(
                    (ite) => ite.id == item.id
                  )
                  if (!find) {
                    return (
                      item.isOpenCheck ||
                      item.isOpenGraduationTest ||
                      item.isOpenAttendance ||
                      item.isOpenAccommodationInfoCollect
                    )
                  } else {
                    return (
                      item.isOpenCheck !== find.isOpenCheck ||
                      item.isOpenGraduationTest !== find.isOpenGraduationTest ||
                      item.isOpenAttendance !== find.isOpenAttendance ||
                      item.isOpenAccommodationInfoCollect !== find.isOpenAccommodationInfoCollect
                    )
                  }
                })
              ) {
                this.$confirm('当前方案期别要求发生变化，需前往实施管理-训前实施模块调整相关的配置！', '提示', {
                  confirmButtonText: '立即前往',
                  cancelButtonText: '暂不前往'
                })
                  .then(() => {
                    this.$router.push(`/training/scheme/implementingManagement/${res.data.schemeId}`)
                  })
                  .catch(() => {
                    this.goToSchemeList()
                  })
              } else this.goToSchemeList()
            })
          } else {
            const message = this.getValue(res.status?.errors?.[0]?.message)?.['message']
            this.$message.error(message || res.status?.errors?.[0]?.message || '操作失败')
          }
        } catch (e) {
          const errMsg = e || '操作失败'
          this.$message.warning(errMsg)
          console.log('加载失败: ', e)
        } finally {
          this.loading.close()
        }
      } else {
        if (!this.recalculating || !this.trainClassCommodityInfo.trainClassBaseInfo.isIntelligenceLearning) {
          this.publishScheme(true)
        } else {
          return this.$confirm(
            '本次修改如涉及到考核、课程内容相关。系统将锁定方案和已报名的学员进行重算处理，处理成功后管理员才能再次修改方案，学员重新进入学习。确认要保存？',
            '系统提醒',
            {
              confirmButtonText: '确定',
              type: 'warning'
            }
          ).then(() => {
            this.publishScheme(true)
          })
        }
      }
    }
    //   if (this.routerMode !== 3 || isUpdate) {

    //   }
    //

    /**
     * 获取错误信息
     * 例："{Code:50014,message:名称已存在}"
     * @param message
     */
    getValue(message: string) {
      const errObj = {}
      let prevKey = ''
      message
        ?.replace('{', '')
        ?.replace('}', '')
        ?.split(',')
        ?.forEach((item) => {
          const keyVal = item?.split(':')
          if (keyVal?.length > 1) {
            errObj[keyVal[0]] = keyVal[1]
            prevKey = keyVal[0]
          } else if (keyVal.length == 1 && prevKey) {
            errObj[prevKey] = `${errObj[prevKey]},${keyVal[0]}`
          }
        })
      return errObj
    }

    // 获取方案类型后刷新心得列表
    getSchemeType(val: number) {
      if (val) {
        this.learningWayRef?.studyNotesRef?.experienceRef?.init()
      }
    }
    schemeFormChange(val: TrainingModeEnum) {
      if (val === TrainingModeEnum.online && this.learningWayRef) {
        this.learningWayRef.activeName = 'course-learn'
        this.learningWayRef.learningTypeSelect('issue').value = false
        this.learningWayRef.handleLearningTypeChange('issue', false)
        if (this.trainClassCommodityInfo.learningTypeModel.questionnaire.isSelected) {
          this.trainClassCommodityInfo.learningTypeModel.questionnaire.questionnaireConfigList =
            this.trainClassCommodityInfo.learningTypeModel.questionnaire.questionnaireConfigList.filter((item) =>
              [QuestionnaireAppliedRangeTypeEnum.scheme, QuestionnaireAppliedRangeTypeEnum.online_course].includes(
                item.appliedRangeType
              )
            )
          this.learningWayRef.questionnaireRef?.page.currentChange(1)
        }
      } else if (val === TrainingModeEnum.mixed && this.learningWayRef) {
        this.learningWayRef.learningTypeSelect('issue').value = true
        this.learningWayRef.handleLearningTypeChange('issue', true)
      } else if (val === TrainingModeEnum.offline && this.learningWayRef) {
        this.learningWayRef.activeName = 'training-period'
        this.learningWayRef.learningTypeSelect('issue').value = true
        this.learningWayRef.handleLearningTypeChange('issue', true)
        this.learningWayRef.learningTypeSelect('courseLearning').value = false
        this.learningWayRef.handleLearningTypeChange('courseLearning', false)
        this.learningWayRef.learningTypeSelect('exam').value = false
        this.learningWayRef.handleLearningTypeChange('exam', false)
        this.learningWayRef.learningTypeSelect('practiceLearning').value = false
        this.learningWayRef.handleLearningTypeChange('practiceLearning', false)
        this.learningWayRef.learningTypeSelect('interestCourse').value = false
        this.learningWayRef.handleLearningTypeChange('interestCourse', false)
        this.learningWayRef.learningTypeSelect('learningExperience').value = false
        this.learningWayRef.handleLearningTypeChange('learningExperience', false)
        if (this.trainClassCommodityInfo.learningTypeModel.questionnaire.isSelected) {
          this.trainClassCommodityInfo.learningTypeModel.questionnaire.questionnaireConfigList =
            this.trainClassCommodityInfo.learningTypeModel.questionnaire.questionnaireConfigList.filter((item) =>
              [QuestionnaireAppliedRangeTypeEnum.per_issue, QuestionnaireAppliedRangeTypeEnum.assign_issue].includes(
                item.appliedRangeType
              )
            )
          this.learningWayRef.questionnaireRef?.page.currentChange(1)
        }
      }
    }

    /**
     * 取消新建/编辑
     */
    cancel() {
      this.$router.push(this.backUrl)
    }
    /**
     * 发布后跳转
     */
    goToSchemeList() {
      const path = this.routerMode != 3 ? this.$route.path : ''
      this.$router.push(this.backUrl)
      if (path) RootModule.doRemoveNav(path)
    }
    beforeDestroy() {
      clearTimeout(this.timer)
      this.loading?.close()
    }
    deactivated() {
      clearTimeout(this.timer)
      this.loading?.close()
    }
  }
</script>
