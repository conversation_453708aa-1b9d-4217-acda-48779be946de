import MsMediaResourceLearningV1, {
  LearningResultResponse,
  TimingTokenResponse
} from '@api/ms-gateway/ms-media-resource-learning-v1'
import AbstractApplyToken from '@api/service/common/token/AbstractApplyToken'
import { Response, ResponseStatus } from '@hbfe/common'

/**
 * 申请课程播放 token
 */
class ApplyMediaLearningTimingToken extends AbstractApplyToken {
  /**
   * 课程学习播放凭证/课程试听凭证/课程预览凭证
   */
  private readonly coursewarePlayToken?: string

  learningTokenConfig: TimingTokenResponse

  /**
   * 申请课程学习token返回值
   */
  studyMaxRes = new Response<LearningResultResponse>()

  constructor(coursewarePlayToken: string) {
    super()
    this.coursewarePlayToken = coursewarePlayToken
  }

  async apply(): Promise<ResponseStatus> {
    const { status, data } = await MsMediaResourceLearningV1.applyMediaLearningTimingWithAnti(this.coursewarePlayToken)
    if (status.isSuccess()) {
      if (data.learningCode.code !== '200') {
        if (data.learningCode.code === 'T10006') {
          return new ResponseStatus(parseInt('10006'), '系统繁忙')
        }
        this.studyMaxRes = { status, data }
        return new ResponseStatus(parseInt(data.learningCode.code), data.learningCode.message)
      }
    }
    this.learningTokenConfig = data.timingToken
    this.token = data.timingToken.token
  }
}

export default ApplyMediaLearningTimingToken
