<route-meta>
{
"isMenu": true,
"title": "导入学员开班并学习",
"sort": 1
}
</route-meta>
<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  import Scheme from '@hbfe/jxjy-admin-intelligentLearning/src/import-open/index.vue'

  @RoleTypeDecorator({
    studyImport: [WXGLY],
    WXImport: [WXGLY],
    querySchemeWx: [WXGLY]
  })
  export default class extends Scheme {}
</script>
