import convertDistributionContractResponse from './queries/convertDistributionContractResponse.graphql'
import exportBatchOrderDetailInDistributor from './queries/exportBatchOrderDetailInDistributor.graphql'
import exportBatchOrderInDistributor from './queries/exportBatchOrderInDistributor.graphql'
import exportBatchReconciliationInDistributor from './queries/exportBatchReconciliationInDistributor.graphql'
import exportBatchReturnOrderDetailExcelInDistributor from './queries/exportBatchReturnOrderDetailExcelInDistributor.graphql'
import exportBatchReturnOrderExcelInDistributor from './queries/exportBatchReturnOrderExcelInDistributor.graphql'
import exportBatchReturnReconciliationExcelInDistributor from './queries/exportBatchReturnReconciliationExcelInDistributor.graphql'
import exportOrderExcelInDistributor from './queries/exportOrderExcelInDistributor.graphql'
import exportReconciliationExcelInDistributor from './queries/exportReconciliationExcelInDistributor.graphql'
import exportReturnOrderExcelInDistributor from './queries/exportReturnOrderExcelInDistributor.graphql'
import exportReturnReconciliationExcelInDistributor from './queries/exportReturnReconciliationExcelInDistributor.graphql'
import getCurrentPortalIdInDustributor from './queries/getCurrentPortalIdInDustributor.graphql'
import getDiscountRegionInSubject from './queries/getDiscountRegionInSubject.graphql'
import getDistributionContractByContractId from './queries/getDistributionContractByContractId.graphql'
import getDistributionServicerInfoInMyself from './queries/getDistributionServicerInfoInMyself.graphql'
import getDistributionServicerInfoInSubProject from './queries/getDistributionServicerInfoInSubProject.graphql'
import getDistributorCommodityInSubProject from './queries/getDistributorCommodityInSubProject.graphql'
import getDistributorPortalInfosInSupplier from './queries/getDistributorPortalInfosInSupplier.graphql'
import getLastLevelRegionDetailByRegionTreeId from './queries/getLastLevelRegionDetailByRegionTreeId.graphql'
import getNewsDetail from './queries/getNewsDetail.graphql'
import getOfflineCollectiveByPotalIdInDistributor from './queries/getOfflineCollectiveByPotalIdInDistributor.graphql'
import getOfflineRegistrationConfigByPortalId from './queries/getOfflineRegistrationConfigByPortalId.graphql'
import getPolicyPortalList from './queries/getPolicyPortalList.graphql'
import getPortalInDistributor from './queries/getPortalInDistributor.graphql'
import getPortalPricingCount from './queries/getPortalPricingCount.graphql'
import getPosterConfigurationInDistributor from './queries/getPosterConfigurationInDistributor.graphql'
import getProductDiscountApplyInfoInSubject from './queries/getProductDiscountApplyInfoInSubject.graphql'
import getPromotionPortalInfoInDistributor from './queries/getPromotionPortalInfoInDistributor.graphql'
import getRegionByApplyIdInSupplier from './queries/getRegionByApplyIdInSupplier.graphql'
import getRegionByContractIdInDistributor from './queries/getRegionByContractIdInDistributor.graphql'
import getRegionByContractIdInSupplier from './queries/getRegionByContractIdInSupplier.graphql'
import getRegionDetailByRegionTreeId from './queries/getRegionDetailByRegionTreeId.graphql'
import getSalesman from './queries/getSalesman.graphql'
import ifOnlyPorttalInDustributor from './queries/ifOnlyPorttalInDustributor.graphql'
import judgeCommodityAuthorized from './queries/judgeCommodityAuthorized.graphql'
import listAddedPortalPricingPolicy from './queries/listAddedPortalPricingPolicy.graphql'
import listMyDistributorCommodityRegionInDistributor from './queries/listMyDistributorCommodityRegionInDistributor.graphql'
import listMyDistributorCommoditySkuPropertyInDistributor from './queries/listMyDistributorCommoditySkuPropertyInDistributor.graphql'
import listPricingSchemeSkuPropertyInDistributor from './queries/listPricingSchemeSkuPropertyInDistributor.graphql'
import listPricingSchemeSkuPropertyInSupplier from './queries/listPricingSchemeSkuPropertyInSupplier.graphql'
import listProductDiscountApplySkuPropertyInDistributor from './queries/listProductDiscountApplySkuPropertyInDistributor.graphql'
import pageCommoditySkuDistributorOpenReportInDistributor from './queries/pageCommoditySkuDistributorOpenReportInDistributor.graphql'
import pageCommoditySkuDistributorOpenReportInSupplier from './queries/pageCommoditySkuDistributorOpenReportInSupplier.graphql'
import pageCommoditySkuOpenReportInSupplier from './queries/pageCommoditySkuOpenReportInSupplier.graphql'
import pageDistributorCommodityBasicInSubject from './queries/pageDistributorCommodityBasicInSubject.graphql'
import pageDistributorCommodityInSupplier from './queries/pageDistributorCommodityInSupplier.graphql'
import pageDistributorInSupplier from './queries/pageDistributorInSupplier.graphql'
import pageDistributorIssueCommodityInDistributor from './queries/pageDistributorIssueCommodityInDistributor.graphql'
import pageDistributorIssueCommodityInServicer from './queries/pageDistributorIssueCommodityInServicer.graphql'
import pageDistributorSellStatisticInSupplier from './queries/pageDistributorSellStatisticInSupplier.graphql'
import pageDistributorWithOnlineSchoolInSupplier from './queries/pageDistributorWithOnlineSchoolInSupplier.graphql'
import pageDockingDistributorSalesmanInSupplier from './queries/pageDockingDistributorSalesmanInSupplier.graphql'
import pageMyDistributorCommodityInDistributor from './queries/pageMyDistributorCommodityInDistributor.graphql'
import pageOnlineSchoolWithDistributionRelationInSupplier from './queries/pageOnlineSchoolWithDistributionRelationInSupplier.graphql'
import pageProductDiscountApplyInfoInDistributor from './queries/pageProductDiscountApplyInfoInDistributor.graphql'
import pageProductDiscountApplyInfoInSupplier from './queries/pageProductDiscountApplyInfoInSupplier.graphql'
import pageProductPricingSchemeInDistributor from './queries/pageProductPricingSchemeInDistributor.graphql'
import pageProductPricingSchemeInSupplier from './queries/pageProductPricingSchemeInSupplier.graphql'
import pagePromotionPortalInfoInDistributor from './queries/pagePromotionPortalInfoInDistributor.graphql'
import pageSalesmanInSupplier from './queries/pageSalesmanInSupplier.graphql'
import pageSimpleNews from './queries/pageSimpleNews.graphql'
import pageStudentSchemeLearningInDistributor from './queries/pageStudentSchemeLearningInDistributor.graphql'
import pageStudentSchemeLearningInSupplier from './queries/pageStudentSchemeLearningInSupplier.graphql'
import statisticAuthorizedDistributorCommodityInDistributor from './queries/statisticAuthorizedDistributorCommodityInDistributor.graphql'
import statisticTradeRecordInDistributor from './queries/statisticTradeRecordInDistributor.graphql'
import statisticTradeRecordInSupplier from './queries/statisticTradeRecordInSupplier.graphql'
import statisticTradeSummaryInDistributor from './queries/statisticTradeSummaryInDistributor.graphql'
import transformDistributorPortalInfoResponse from './queries/transformDistributorPortalInfoResponse.graphql'
import transformPortalInfoResponse from './queries/transformPortalInfoResponse.graphql'

export {
  convertDistributionContractResponse,
  exportBatchOrderDetailInDistributor,
  exportBatchOrderInDistributor,
  exportBatchReconciliationInDistributor,
  exportBatchReturnOrderDetailExcelInDistributor,
  exportBatchReturnOrderExcelInDistributor,
  exportBatchReturnReconciliationExcelInDistributor,
  exportOrderExcelInDistributor,
  exportReconciliationExcelInDistributor,
  exportReturnOrderExcelInDistributor,
  exportReturnReconciliationExcelInDistributor,
  getCurrentPortalIdInDustributor,
  getDiscountRegionInSubject,
  getDistributionContractByContractId,
  getDistributionServicerInfoInMyself,
  getDistributionServicerInfoInSubProject,
  getDistributorCommodityInSubProject,
  getDistributorPortalInfosInSupplier,
  getLastLevelRegionDetailByRegionTreeId,
  getNewsDetail,
  getOfflineCollectiveByPotalIdInDistributor,
  getOfflineRegistrationConfigByPortalId,
  getPolicyPortalList,
  getPortalInDistributor,
  getPortalPricingCount,
  getPosterConfigurationInDistributor,
  getProductDiscountApplyInfoInSubject,
  getPromotionPortalInfoInDistributor,
  getRegionByApplyIdInSupplier,
  getRegionByContractIdInDistributor,
  getRegionByContractIdInSupplier,
  getRegionDetailByRegionTreeId,
  getSalesman,
  ifOnlyPorttalInDustributor,
  judgeCommodityAuthorized,
  listAddedPortalPricingPolicy,
  listMyDistributorCommodityRegionInDistributor,
  listMyDistributorCommoditySkuPropertyInDistributor,
  listPricingSchemeSkuPropertyInDistributor,
  listPricingSchemeSkuPropertyInSupplier,
  listProductDiscountApplySkuPropertyInDistributor,
  pageCommoditySkuDistributorOpenReportInDistributor,
  pageCommoditySkuDistributorOpenReportInSupplier,
  pageCommoditySkuOpenReportInSupplier,
  pageDistributorCommodityBasicInSubject,
  pageDistributorCommodityInSupplier,
  pageDistributorInSupplier,
  pageDistributorIssueCommodityInDistributor,
  pageDistributorIssueCommodityInServicer,
  pageDistributorSellStatisticInSupplier,
  pageDistributorWithOnlineSchoolInSupplier,
  pageDockingDistributorSalesmanInSupplier,
  pageMyDistributorCommodityInDistributor,
  pageOnlineSchoolWithDistributionRelationInSupplier,
  pageProductDiscountApplyInfoInDistributor,
  pageProductDiscountApplyInfoInSupplier,
  pageProductPricingSchemeInDistributor,
  pageProductPricingSchemeInSupplier,
  pagePromotionPortalInfoInDistributor,
  pageSalesmanInSupplier,
  pageSimpleNews,
  pageStudentSchemeLearningInDistributor,
  pageStudentSchemeLearningInSupplier,
  statisticAuthorizedDistributorCommodityInDistributor,
  statisticTradeRecordInDistributor,
  statisticTradeRecordInSupplier,
  statisticTradeSummaryInDistributor,
  transformDistributorPortalInfoResponse,
  transformPortalInfoResponse
}
