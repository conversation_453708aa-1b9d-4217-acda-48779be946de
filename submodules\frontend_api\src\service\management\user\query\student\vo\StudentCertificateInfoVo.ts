import {
  AttachmentInfoResponse,
  StudentCertificateResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

/*
  学员证书信息
*/
class StudentCertificateInfoVo extends StudentCertificateResponse {
  /**
   * 证书id
   */
  certificateId: string = undefined
  /**
   * 证书编号
   */
  certificateCode: string = undefined
  /**
   * 证书类别
   */
  certificateCategory: string = undefined
  /**
   * 注册专业
   */
  registerProfessional: string = undefined
  /**
   * 发证日期
   */
  releaseStartTime: string = undefined
  /**
   * 失效日期
   */
  certificateEndTime: string = undefined
  /**
   * 证书附件信息
   */
  attachmentInfoList: Array<AttachmentInfoResponse> = []

  /*
   *属性扩展
   */
  /* 证书类别名称 */
  certificateCategoryName: string = undefined

  /* 注册专业名称 */
  registerProfessionalName: string = undefined
}

export default StudentCertificateInfoVo
