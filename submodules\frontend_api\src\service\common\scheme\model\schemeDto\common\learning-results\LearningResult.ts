/**
 * @description 学习成果
 */
class LearningResult {
  /**
   * 成果id
   */
  id: string
  /**
   * 成果类型
   * @description 1-分数型成果、2-证书成果
   */
  type: number
  /**
   * 【分数型成果】分数型学习成果分数类型常量
   * @description 学分：CREDIT
   */
  gradeType?: string
  /**
   * 【分数型成果】分数
   */
  grade?: number
  /**
   * 【证书成果】是否提供证明
   */
  provideCert?: boolean
  /**
   * 【证书成果】开放证明打印
   */
  openPrintTemplate?: boolean
  /**
   * 【证书成果】证书模板ID
   */
  certificateTemplateId?: string
  /**
   * 操作类型
   */
  operation?: number
}

export default LearningResult
