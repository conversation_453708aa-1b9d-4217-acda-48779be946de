<template>
  <div>
    <template v-if="$hasPermission('educationalQuery')" desc="查询教务管理表格列表" actions="searchTableList,created">
      <el-card shadow="never" class="m-card">
        <!--条件查询-->
        <hb-search-wrapper @reset="resetQueryparams" class="m-query">
          <el-form-item label="姓名：">
            <el-input v-model="educationalManageParam.name" clearable placeholder="请输入姓名" />
          </el-form-item>
          <el-form-item label="证件号：">
            <el-input v-model="educationalManageParam.idCard" clearable placeholder="请输入证件号" />
          </el-form-item>
          <el-form-item label="手机号：">
            <el-input v-model="educationalManageParam.phone" clearable placeholder="请输入手机号" />
          </el-form-item>
          <template slot="actions">
            <template v-if="$hasPermission('exportDataList')" desc="教务管理导出表格数据" actions="exportDataList">
              <el-button type="primary" @click="exportDataList">导出表格数据</el-button>
            </template>
            <template v-if="$hasPermission('importResult')" desc="教务管理导入结业成果" actions="importResult">
              <el-button type="primary" @click="importResult">导入结业成果</el-button>
            </template>
            <el-button type="primary" @click="searchTableList">查询</el-button>
          </template>
        </hb-search-wrapper>

        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15" ref="tableRef" v-loading="loading">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="姓名" min-width="160">
            <template slot-scope="scope">
              <span>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="证件号" min-width="170">
            <template slot-scope="scope">
              <span>{{ scope.row.idCard }}</span>
            </template>
          </el-table-column>
          <!--          <el-table-column label="学号" min-width="140">-->
          <!--            <template slot-scope="scope">-->
          <!--              <span>{{ scope.row.businessStudentNo }}</span>-->
          <!--            </template>-->
          <!--          </el-table-column>-->
          <el-table-column label="手机号" min-width="140">
            <template slot-scope="scope">
              <span>{{ scope.row.phoneNum }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="证书类型" min-width="200">
            <template slot-scope="scope">
              <el-tag size="mini" class="f-mr5" v-if="scope.row.certificateItemType === 1">主项</el-tag>
              <el-tag type="warning" size="mini" class="f-mr5" v-if="scope.row.certificateItemType === 2">增项</el-tag>
              <span>{{ scope.row.certificateType || '--' }}</span>
            </template>
          </el-table-column> -->
          <el-table-column label="工作单位" min-width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="oneline">{{ scope.row.workUnit || '--' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="是否住宿" min-width="100" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.isLodging">
                <span v-if="scope.row.lodgingType === 1">单住</span>
                <span v-if="scope.row.lodgingType === 2">合住</span>
              </span>
              <span v-else-if="!scope.row.isLodging">--</span>
            </template>
          </el-table-column>
          <el-table-column label="是否报到" min-width="180">
            <template slot-scope="scope">
              <span v-if="isSetReportConfig === true && scope.row.reported === true">
                <div>已报到打卡</div>
                <div>{{ scope.row.reportTime }}</div>
              </span>
              <span v-else-if="isSetReportConfig === true && scope.row.reported === false">
                <span>--</span>
              </span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="签到/签退次数" min-width="200" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isOpenAttendance === undefined || scope.row.isOpenAttendance === false">
                <span>--</span>
              </div>
              <div v-if="scope.row.isOpenAttendance && !scope.row.isOpenCheckIn">
                <span>未开启签到/签退</span>
              </div>
              <div v-if="scope.row.isOpenAttendance && scope.row.isOpenCheckIn">
                <p>GPS定位签到次数：{{ scope.row.signInTimes }}次</p>
                <p>GPS定位签退次数：{{ scope.row.signOutTimes }}次</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="结业测验结果" min-width="120" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.completionTestResults !== undefined && scope.row.completionTestResults === 1"
                >合格</span
              >
              <span v-else-if="scope.row.completionTestResults !== undefined && scope.row.completionTestResults === 0"
                >不合格</span
              >
              <span v-else-if="scope.row.completionTestResults !== undefined && scope.row.completionTestResults === -1"
                >--</span
              >
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" align="center" fixed="right">
            <template
              slot-scope="scope"
              v-if="$hasPermission('openChangeLivingInfo')"
              desc="教务管理修改住宿信息"
              actions="openChangeLivingInfo"
            >
              <el-button type="text" @click="openChangeLivingInfo(scope.row)">修改住宿信息</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </el-card>
    </template>

    <el-drawer
      title="修改住宿信息"
      :visible.sync="dialog004"
      size="600px"
      custom-class="m-drawer"
      :wrapperClosable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
          学员若需住宿，食宿统一安排，费用另行自理。
        </el-alert>
        <el-form ref="form" :model="educationalInfo" label-width="150px" class="m-form">
          <el-form-item label="是否住宿：" required>
            <el-radio-group v-model="educationalInfo.isLodging">
              <el-radio :label="true">需要安排住宿</el-radio>
              <el-radio :label="false">无需安排住宿</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="住宿方式：" :required="educationalInfo.isLodging">
            <el-radio-group v-model="educationalInfo.lodgingType" :disabled="!educationalInfo.isLodging">
              <el-radio :label="1">单住</el-radio>
              <el-radio :label="2">合住</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <div class="drawer-ft m-btn-bar">
        <el-button @click="cancelDrawer">取消</el-button>
        <template v-if="$hasPermission('save')" desc="保存教务管理修改住宿信息" actions="save">
          <el-button type="primary" @click="save">保存</el-button>
        </template>
      </div>
    </el-drawer>
    <!-- 导出成功的表格 -->
    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">导出入口：导出任务管理-导出结业成果</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goExportDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
    <el-drawer
      title="导入结业成果"
      :visible.sync="dialog006"
      size="800px"
      custom-class="m-drawer"
      :wrapperClosable="false"
    >
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
          学员若需住宿，食宿统一安排，费用另行自理。
        </el-alert>
        <el-steps direction="vertical" :active="4" class="m-vertical-steps" style="padding: 0px">
          <el-step title="下载模板并按要求填写">
            <div slot="description">
              <template
                v-if="$hasPermission('downloadTemplate')"
                desc="教务管理下载导入模板"
                actions="downloadTemplate"
              >
              </template>
              <el-button
                type="primary"
                size="small"
                plain
                class="f-mt5"
                icon="el-icon-download"
                @click="downloadTemplate"
              >
                下载模板
              </el-button>
            </div>
          </el-step>
          <el-step title="上传填写好的表格">
            <div slot="description">
              <template v-if="$hasPermission('MinUploadFile')" desc="教务管理选择文件上传" actions="@MinUploadFile">
                <min-upload-file v-model="hbFileUploadResponse" :file-type="1" :is-protected="true">
                  <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                    选择文件
                  </el-button>
                </min-upload-file>
              </template>
            </div>
          </el-step>
        </el-steps>
      </div>
      <div class="drawer-ft m-btn-bar">
        <el-button @click="cancelDrawerDialog006">取消</el-button>
        <template v-if="$hasPermission('confirmImport')" desc="教务管理成果文件导入" actions="confirmImport">
          <el-button type="primary" @click="confirmImport" v-loading="importLoading">导入</el-button>
        </template>
      </div>
    </el-drawer>
    <!-- 导入成功的表格 -->
    <el-dialog title="提示" :visible.sync="importSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导入成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导入任务管理-导入结业成果</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="importSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goImportDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Prop } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import EducationalInfo from '@api/service/management/implement/models/EducationalInfo'
  import EducationalManageParam from '@api/service/management/implement/models/EducationalManageParam'
  import { bind, debounce } from 'lodash-decorators'
  import EducationalManage from '@api/service/management/implement/EducationalManage'
  import ServiceTime from '@api/service/common/service-time/ServiceTime'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import MinUploadFile from '@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/min-upload-file.vue'
  import { cloneDeep } from 'lodash'
  import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'

  @Component({
    components: {
      MinUploadFile
    }
  })
  export default class extends Vue {
    @Prop({
      type: String,
      default: ''
    })
    periodId: string
    @Prop({
      type: String,
      default: ''
    })
    schemeId: string
    // 当前方案结束时间
    @Prop({
      type: String,
      default: ''
    })
    trainingEndTime: string
    // 当前方案是否开启住宿信息采集
    @Prop({
      type: Boolean,
      default: null
    })
    isOpenAccommodationInfoCollect: boolean
    // 当前方案是否开启结业测试
    @Prop({
      type: Boolean,
      default: null
    })
    isOpenGraduationTest: boolean
    //当前方案是否开启期别报道
    @Prop({
      type: Boolean,
      default: null
    })
    isSetReportConfig: boolean
    // 当前方案的sku
    @Prop({
      type: SkuPropertyResponseVo,
      default: null
    })
    skuProperty: SkuPropertyResponseVo
    //文件上传之后的回调参数
    hbFileUploadResponse = new HBFileUploadResponse()
    educationalManageParam: EducationalManageParam = new EducationalManageParam()
    educationalManage: EducationalManage
    educationalInfo: EducationalInfo = new EducationalInfo()
    loading = false
    isOverTime = false

    constructor() {
      super()
      this.educationalManage = new EducationalManage(this.schemeId, this.periodId)
      this.page = new UiPage(this.getTableList, this.getTableList)
    }

    form = {
      isLodging: '',
      lodgingType: ''
    }

    async created() {
      this.educationalManage = new EducationalManage(this.schemeId, this.periodId)
      this.getTableList()
      const endTime = this.convertToTimestamp(this.trainingEndTime)
      const tempTime = await ServiceTime.getServiceTime()
      const nowTime = this.convertToTimestamp(tempTime)
      console.log(endTime, '培训结束时间')
      console.log(nowTime, '现在的时间')
      if (nowTime < endTime) {
        this.isOverTime = false
      } else {
        this.isOverTime = true
      }
    }

    page: UiPage
    tableData: Array<EducationalInfo> = []
    dialog004 = false
    dialog006 = false
    importLoading = false
    exportSuccessVisible = false
    importSuccessVisible = false

    @bind
    @debounce(200)
    searchTableList() {
      this.page.pageNo = 1
      this.getTableList()
    }

    @bind
    @debounce(200)
    resetQueryparams() {
      this.educationalManageParam = new EducationalManageParam()
      this.page.pageNo = 1
      this.getTableList()
    }

    async getTableList() {
      try {
        this.loading = true
        this.educationalManage.params = this.educationalManageParam
        await this.educationalManage.queryList(this.page)
        this.loading = false
        console.log(this.educationalManage.list, '-----------')
        this.tableData = this.educationalManage.list
        ;(this.$refs['tableRef'] as any)?.doLayout()
      } catch (error) {
        this.loading = false
      }
    }

    convertToTimestamp(dateString: string): number {
      const date = new Date(dateString)
      return date.getTime()
    }

    async openChangeLivingInfo(item: EducationalInfo) {
      this.educationalInfo = cloneDeep(item)
      console.log(this.isOverTime, '-=--------#d5854d')

      if (this.isOverTime) {
        this.$message.warning('培训时间已结束，无法修改住宿信息')
        return
      }
      if (this.isOpenAccommodationInfoCollect === false) {
        this.$message.warning('当前期别未开启采集住宿信息')
        return
      }
      this.$nextTick(() => {
        this.dialog004 = true
      })
    }

    cancelDrawer() {
      this.dialog004 = false
    }

    @bind
    @debounce(200)
    async exportDataList() {
      this.educationalManage.params = this.educationalManageParam
      // 导出表格数据
      const res = await this.educationalManage.exportDataList()
      console.log(res, '导出的表格数据')
      if (res.isSuccess()) {
        this.exportSuccessVisible = true
      } else {
        this.$message.error(res.errors[0])
      }
    }

    @bind
    @debounce(200)
    async save() {
      //
      if (this.educationalInfo.isLodging === false) {
        this.educationalInfo.lodgingType = 0
      }
      console.log(this.educationalInfo, 'updateLodgingInfo')

      const res = await this.educationalInfo.updateLodgingInfo()
      if (res.isSuccess()) {
        this.dialog004 = false
        setTimeout(() => {
          this.getTableList()
        }, 200)
      } else {
        this.$message.error(res.errors[0])
      }
    }

    goExportDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportIssueRegionStudyReport' }
      })
    }

    goImportDownloadPage() {
      this.importSuccessVisible = false
      this.$router.push({
        path: '/training/task/importtask',
        query: { type: '导入结业成果' }
      })
    }

    importResult() {
      if (this.isOverTime) {
        this.$message.warning('培训班已结束')
        return
      }
      if (this.isOpenGraduationTest === false) {
        this.$message.warning('当前期别未开启结业测试')
        return
      }

      this.dialog006 = true
    }

    @bind
    @debounce(200)
    async downloadTemplate() {
      const res = await this.educationalManage.getImportCompletionTemplate()
      console.log('下载模板', res)
      const link = document.createElement('a')
      const resolver = this.$router.resolve({
        name: '/mfs' + res
      })
      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      link.setAttribute('download', '导入学员结业测试成果模板')
      document.body.appendChild(link)
      link.click()
      link.remove()
    }

    cancelDrawerDialog006() {
      this.hbFileUploadResponse = null
      this.dialog006 = false
    }

    async confirmImport() {
      this.importLoading = true
      if (this.hbFileUploadResponse.url) {
        const res = await this.educationalManage.importCompletionResult(
          this.hbFileUploadResponse.url,
          this.hbFileUploadResponse.fileName
        )
        console.log(res, '上传文件res')
        if (res.isSuccess()) {
          this.dialog006 = false
          this.hbFileUploadResponse = null
          this.importSuccessVisible = true
        } else {
          this.$message.error(res.errors[0])
        }
        console.log(this.hbFileUploadResponse, '666666')
      } else {
        this.$message.warning('请选择文件')
      }
      this.importLoading = false
    }
  }
</script>

<style scoped lang="scss">
  .oneline {
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
