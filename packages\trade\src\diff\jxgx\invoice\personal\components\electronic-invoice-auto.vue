<template>
  <jxgx-electronic-invoice-auto ref="electronicInvoiceAutoRef"></jxgx-electronic-invoice-auto>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import AutoInvoice from '@hbfe/jxjy-admin-trade/src/diff/jxgx/invoice/personal/components/auto-invoice.vue'
  import RedInvoice from '@hbfe/jxjy-admin-trade/src/diff/jxgx/invoice/personal/components/red-invoice.vue'
  import ElectronicInvoiceAuto from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/electronic-invoice-auto.vue'

  @Component({
    components: { AutoInvoice, RedInvoice }
  })
  class JxgxElectronicInvoiceAuto extends ElectronicInvoiceAuto {}
  @Component({
    components: { JxgxElectronicInvoiceAuto }
  })
  export default class extends Vue {
    @Ref('electronicInvoiceAutoRef') electronicInvoiceAutoRef: JxgxElectronicInvoiceAuto
  }
</script>
