/*
 * @Description: 发票业务(线下)
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:33:37
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-25 14:57:28
 */

import Offlineinvoice from '@api/ms-gateway/ms-offlineinvoice-v1'
import { ResponseStatus } from '@hbfe/common'
import OffLinePageInvoiceResponseVo from './dto/OffLinePageInvoiceResponseVo'
import UpdateOfflineSpecialPaperInvoiceRequest from './dto/UpdateOfflineSpecialPaperInvoiceRequest'
export default class MutationOffLineInvoice {
  /**
   * 更新电子发票信息
   * @param invoiceDetail 发票详情
   */
  async updateOfflineInvoice(invoiceDetail: OffLinePageInvoiceResponseVo) {
    const detail = OffLinePageInvoiceResponseVo.to(invoiceDetail)
    const response = await Offlineinvoice.updateOfflineInvoice(detail)
    return response
  }
  /**
   * 更新专票信息
   * @param invoiceDetail 发票详情
   */
  async updateOfflinePaperInvoice(invoiceDetail: OffLinePageInvoiceResponseVo) {
    const detail = UpdateOfflineSpecialPaperInvoiceRequest.to(invoiceDetail)
    const response = await Offlineinvoice.updateOfflinePaperInvoice(detail)
    return response
  }
  /**
   * 处理发票
   * @param orderId 发票ID
   * @param dealOrderNo 发票号码
   * @returns ResponseStatus
   */
  async dealWithInvoice(orderId: string, dealOrderNo: string): Promise<ResponseStatus> {
    const { status } = await Offlineinvoice.issueOfflineInvoice({
      offlineInvoiceId: orderId,
      invoiceNo: dealOrderNo
    })
    return status
  }
  /**
   * 作废发票
   * @param offlineInvoiceId 发票ID
   * @param reason 理由
   * @returns
   */
  async invalidInvoice(offlineInvoiceId: string, reason?: string): Promise<ResponseStatus> {
    const { status } = await Offlineinvoice.resetInvoice({ offlineInvoiceId, reason })
    return status
  }
  /**
   * 导入个人报名线下发票
   * @param filePath 文件路径
   * @param importType 类型 1. 个人缴费线下电子票 2. 个人缴费线下专票 3. 批次缴费线下电子票 4. 批次缴费线下纸质票 5.个人缴费增值税专用电子票 6.批次缴费增值税专票电子票
   * @returns
   */
  async importOfflineInvoice(filePath: string, importType: number): Promise<ResponseStatus> {
    const { status } = await Offlineinvoice.importOfflineInvoiceWithServiceId({ filePath, importType })
    return status
  }
}
