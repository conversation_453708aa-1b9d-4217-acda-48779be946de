import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-trade-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-trade-query-front-gateway-TradeQueryBackstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum BatchOrderSortField {
  BATCH_ORDER_UN_CONFIRMED_TIME = 'BATCH_ORDER_UN_CONFIRMED_TIME',
  BATCH_ORDER_COMMIT_TIME = 'BATCH_ORDER_COMMIT_TIME'
}
export enum BatchReturnOrderSortField {
  CREATED_TIME = 'CREATED_TIME'
}
export enum CommoditySkuSortField {
  ON_SHELVE_TIME = 'ON_SHELVE_TIME',
  ISSUE_TRAINING_BEGIN_TIME = 'ISSUE_TRAINING_BEGIN_TIME',
  ISSUE_SIGN_UP_BEGIN_TIME = 'ISSUE_SIGN_UP_BEGIN_TIME',
  COMMODITY_CREATED_TIME = 'COMMODITY_CREATED_TIME',
  LAST_EDIT_TIME = 'LAST_EDIT_TIME',
  SALE_TOTAL_NUMBER = 'SALE_TOTAL_NUMBER',
  SKU_PROPERTY_YEAR = 'SKU_PROPERTY_YEAR',
  TRAINING_CHANNEL = 'TRAINING_CHANNEL'
}
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum ExchangeOrderSortField {
  APPLIED_TIME = 'APPLIED_TIME'
}
export enum OfflineInvoiceSortField {
  INVOICE_CREAT_TIME = 'INVOICE_CREAT_TIME'
}
export enum OnlineInvoiceSortField {
  INVOICE_CREAT_TIME = 'INVOICE_CREAT_TIME'
}
export enum OrderSortField {
  ORDER_NORMAL_TIME = 'ORDER_NORMAL_TIME',
  ORDER_COMPLETED_TIME = 'ORDER_COMPLETED_TIME'
}
export enum ReturnOrderSortField {
  APPLIED_TIME = 'APPLIED_TIME'
}
export enum CommodityOpenReportSortField {
  TRADE_SUCCESS_COUNT = 'TRADE_SUCCESS_COUNT'
}
export enum SortPolicy1 {
  ASC = 'ASC',
  DESC = 'DESC'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class BigDecimalScopeRequest {
  begin?: number
  end?: number
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class BatchOrderRequest {
  /**
   * 批次单号集合
   */
  batchOrderNoList?: Array<string>
  /**
   * 批次单基本信息查询参数
   */
  basicData?: BatchOrderBasicDataRequest
  /**
   * 批次单支付信息查询参数
   */
  payInfo?: OrderPayInfoRequest
  /**
   * 批次单创建人查询参数
   */
  creatorIdList?: Array<string>
  /**
   * 是否已经申请发票
   */
  isInvoiceApplied?: boolean
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class BatchOrderRequestInDistributor {
  /**
   * 批次单号集合
   */
  batchOrderNoList?: Array<string>
  /**
   * 批次单基本信息查询参数
   */
  basicData?: BatchOrderBasicDataRequestInDistributor
  /**
   * 批次单支付信息查询参数
   */
  payInfo?: OrderPayInfoRequest
  /**
   * 批次单创建人查询参数
   */
  creatorIdList?: Array<string>
  /**
   * 是否已经申请发票
   */
  isInvoiceApplied?: boolean
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 批次单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class BatchOrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: BatchOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 批次单基本信息查询参数
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderBasicDataRequest {
  /**
   * 批次单状态
0: 未确认，批次单初始状态
1: 正常
2: 交易完成
3: 交易关闭
4: 提交处理中 提交处理完成后，变更为NORMAl
5: 取消处理中
@see BatchOrderStatus
   */
  batchOrderStatusList?: Array<number>
  /**
   * 批次单状态变更时间
   */
  batchOrderStatusChangeTime?: BatchOrderStatusChangeTimeRequest
  /**
   * 批次单支付状态
<p>
0：未支付
1：支付中
2：已支付
@see BatchOrderPaymentStatus
   */
  batchOrderPaymentStatusList?: Array<number>
  /**
   * 批次单发货状态
0: 未发货
1: 发货中
2: 已发货
@see BatchOrderDeliveryStatus
   */
  batchOrderDeliveryStatusList?: Array<number>
  /**
   * 批次单价格范围
<p> 查询非0元批次单 begin填0.01
   */
  batchOrderAmountScope?: BigDecimalScopeRequest
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 批次单基本信息查询参数
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderBasicDataRequestInDistributor {
  /**
   * 批次单状态
0: 未确认，批次单初始状态
1: 正常
2: 交易完成
3: 交易关闭
4: 提交处理中 提交处理完成后，变更为NORMAl
5: 取消处理中
@see BatchOrderStatus
   */
  batchOrderStatusList?: Array<number>
  /**
   * 批次单状态变更时间
   */
  batchOrderStatusChangeTime?: BatchOrderStatusChangeTimeRequest
  /**
   * 批次单支付状态
<p>
0：未支付
1：支付中
2：已支付
@see BatchOrderPaymentStatus
   */
  batchOrderPaymentStatusList?: Array<number>
  /**
   * 批次单发货状态
0: 未发货
1: 发货中
2: 已发货
@see BatchOrderDeliveryStatus
   */
  batchOrderDeliveryStatusList?: Array<number>
  /**
   * 批次单价格范围
<p> 查询非0元批次单 begin填0.01
   */
  batchOrderAmountScope?: BigDecimalScopeRequest
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 门店id
   */
  portalId?: string
}

/**
 * 批次单状态变更时间查询参数
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderStatusChangeTimeRequest {
  /**
   * 未确认
   */
  unConfirmed?: DateScopeRequest
  /**
   * 正常
   */
  normal?: DateScopeRequest
  /**
   * 交易成功
   */
  completed?: DateScopeRequest
  /**
   * 已关闭
   */
  closed?: DateScopeRequest
  /**
   * 提交中
   */
  committing?: DateScopeRequest
  /**
   * 取消处理中
   */
  canceling?: DateScopeRequest
}

/**
 * 批次退货单审批信息查询参数
<AUTHOR>
@date 2022/03/18
 */
export class BatchReturnOrderApprovalInfoRequest {
  /**
   * 审批时间
   */
  approveTime?: DateScopeRequest
}

/**
 * 批次退货单查询参数
<AUTHOR>
@date 2022/04/19
 */
export class BatchReturnOrderRequest {
  /**
   * 批次退货单号集合
   */
  batchReturnOrderList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: BatchReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: BatchReturnOrderApprovalInfoRequest
  /**
   * 批次退货单关联批次单
   */
  batchOrderInfo?: BatchOrderInfoRequest
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 批次退货单查询参数
<AUTHOR>
@date 2022/04/19
 */
export class BatchReturnOrderRequestInDistributor {
  /**
   * 批次退货单号集合
   */
  batchReturnOrderList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: BatchReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: BatchReturnOrderApprovalInfoRequest
  /**
   * 批次退货单关联批次单
   */
  batchOrderInfo?: BatchOrderInfoRequest
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 批次退货单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class BatchReturnOrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: BatchReturnOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 批次退货单关联批次单查询参数
<AUTHOR>
@date 2022/04/19
 */
export class BatchOrderInfoRequest {
  /**
   * 批次单号集合
   */
  batchOrderNoList?: Array<string>
  /**
   * 批次单创建人id集合
   */
  creatorIdList?: Array<string>
  /**
   * 收款账号ID集合
   */
  receiveAccountIdList?: Array<string>
  /**
   * 交易流水号集合
   */
  flowNoList?: Array<string>
  /**
   * 付款类型
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
   */
  paymentOrderTypeList?: Array<number>
}

/**
 * 批次退货单关闭信息
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnCloseReasonRequest {
  /**
   * 批次退货单关闭类型（1：卖家取消 2：卖家拒绝退货 3：买家取消 4：确认失败取消）
@see BatchReturnCloseTypes
   */
  closeTypeList?: Array<number>
}

/**
 * 批次退货单基本信息查询参数
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnOrderBasicDataRequest {
  /**
   * 批次退货单状态
0: 已创建
1: 已确认
2: 取消申请中
3: 退货处理中
4: 退货失败
5: 正在申请退款
6: 已申请退款
7: 退款处理中
8: 退款失败
9: 退货完成
10: 退款完成
11: 退货退款完成
12: 已关闭
@see BatchReturnOrderStatus
   */
  batchReturnOrderStatus?: Array<number>
  /**
   * 批次退货单关闭信息
   */
  batchReturnCloseReason?: BatchReturnCloseReasonRequest
  /**
   * 批次退货单状态变更时间
   */
  batchReturnStatusChangeTime?: BatchReturnOrderStatusChangeTimeRequest
  /**
   * 退款金额范围
<br> 查询非0元  begin填0.01
   */
  refundAmountScope?: BigDecimalScopeRequest
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 批次退货单状态变更时间查询参数
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnOrderStatusChangeTimeRequest {
  /**
   * 申请退货时间
   */
  applied?: DateScopeRequest
  /**
   * 批次退货完成时间
<p> 这个参数包含了退货退款完成（批次退货单类型为退货退款）、仅退货完成（批次退货单类型为仅退货）、仅退款完成（批次退货单类型为仅退款）时间，三个时间之间用or匹配
   */
  returnCompleted?: DateScopeRequest
}

/**
 * 商品查询条件
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuRequest {
  /**
   * 指定需要查询的sku属性（不指定的话默认查全部）
   */
  needQuerySkuPropertyList?: Array<string>
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品名称（精确匹配）
   */
  saleTitleList?: Array<string>
  /**
   * 商品名称（模糊查询）
   */
  saleTitleMatchLike?: string
  /**
   * 要从查询结果中剔除的商品ID集合
   */
  notShowCommoditySkuIdList?: Array<string>
  /**
   * 商品售价
   */
  price?: number
  /**
   * 商品上下架信息
   */
  onShelveRequest?: OnShelveRequest
  /**
   * 培训方案信息
   */
  schemeRequest?: SchemeRequest
  /**
   * 商品sku属性查询
   */
  skuPropertyRequest?: SkuPropertyRequest
  /**
   * 商品sku属性查询(组合查询)
！！ 由于mongodb限制 不可与 {skuPropertyRequest}同时使用 ！！
   */
  fixSkuPropertyRequest?: Array<SkuPropertyRequest>
  /**
   * 是否展示资源不可用的商品
   */
  isDisabledResourceShow?: boolean
  /**
   * 是否展示所有资源
（该字段会屏蔽可见渠道、商品资源是否可用、商品上下架状态三个条件）
   */
  isShowAll?: boolean
  /**
   * 是否存在专题
   */
  existTrainingChannel?: boolean
  /**
   * 专题
   */
  trainingChannelName?: string
  /**
   * 专题id
   */
  trainingChannelIds?: Array<string>
  /**
   * 第三方平台类型ids
   */
  tppTypeIds?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 管理系统平台
   */
  externalTrainingPlatform?: Array<string>
  /**
   * 所属单位ID
   */
  unitIdList?: Array<string>
  /**
   * 收款主体id集合
   */
  payeeIdList?: Array<string>
  /**
   * 商品拓展信息请求入参
   */
  extInfoRequest?: CommoditySkuExtInfoRequest
  /**
   * 购买渠道列表
   */
  purchaseChannelList?: Array<PurchaseChannelRequest>
  /**
   * 是否展示在专题销售渠道
   */
  isShowTrainingChannel?: boolean
}

/**
 * 商品排序参数
<AUTHOR>
@date 2022/01/27
 */
export class CommoditySkuSortRequest {
  /**
   * 用来排序的字段
   */
  sortField?: CommoditySkuSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * <AUTHOR> linq
@date : 2024-12-10 14:03
@description：期别商品查询请求入参
 */
export class IssueCommoditySkuBackStageRequest {
  /**
   * 商品id
   */
  commoditySkuId?: string
  /**
   * 剔除的期别id集合
   */
  excludedIssueIdList?: Array<string>
  /**
   * 期别名称(精确查询)
   */
  issueNameExact?: string
  /**
   * 期别名称(模糊查询)
   */
  issueNameMatchLike?: string
  /**
   * 期别报名开始时间
   */
  issueSignUpBeginDate?: DateScopeRequest
  /**
   * 期别报名结束时间
   */
  issueSignUpEndDate?: DateScopeRequest
  /**
   * 期别培训开始时间
   */
  issueTrainingBeginTime?: DateScopeRequest
  /**
   * 期别培训结束时间
   */
  issueTrainingEndTime?: DateScopeRequest
}

/**
 * <AUTHOR> linq
@date : 2025-01-07 14:47
@description：商品拓展信息请求入参
 */
export class CommoditySkuExtInfoRequest {
  /**
   * 资源供应商id集合
   */
  resourceProviderIdList?: Array<string>
}

export class PurchaseChannelRequest {
  /**
   * 渠道类型 | 1、用户web自主购买 2、用户h5自主购买 3、用户小程序自主购买 4、用户公众号自主购买 5、集体缴费 6、管理员导入 7、集体报名个人缴费渠道
@see PurchaseChannelType
   */
  purchaseChannelType?: number
  /**
   * 是否可见
   */
  couldSee?: boolean
  /**
   * 是否可购买
   */
  couldBuy?: boolean
}

/**
 * 商品上下架相关查询参数
<AUTHOR>
@date 2022/01/25
 */
export class OnShelveRequest {
  /**
   * 商品上下架状态
<br> 0:已下架 1：已上架
   */
  onShelveStatus?: number
}

/**
 * 培训方案相关查询参数
<AUTHOR>
@date 2022/01/25
 */
export class SchemeRequest {
  /**
   * 培训方案ID
   */
  schemeIdList?: Array<string>
  /**
   * 排除的培训方案ID
   */
  excludedSchemeIdList?: Array<string>
  /**
   * 培训方案类型
<br> chooseCourseLearning:选课规则 autonomousCourseLearning:自主学习 trainingCooperation:培训合作
   */
  schemeType?: string
  /**
   * 培训方案名称(模糊查询)
   */
  schemeName?: string
  /**
   * 培训开始时间
   */
  trainingBeginDate?: DateScopeRequest
  /**
   * 培训结束时间
   */
  trainingEndDate?: DateScopeRequest
  /**
   * 方案学时
   */
  period?: DoubleScopeRequest
}

/**
 * <AUTHOR> linq
@date : 2023-12-25 12:08
@description：商品分销授权信息请求参数
 */
export class CommodityAuthInfoRequest {
  /**
   * 商品授权分销商ID
   */
  distributorId?: string
  /**
   * 分销级别
@see DistriButionLevel
   */
  distributionLevel?: number
  /**
   * 上级分销商ID（仅二级分销商有该值，分销合同内的上级分销商ID）
   */
  superiorDistributorId?: string
  /**
   * 商品授权供应商ID
   */
  supplierId?: string
  /**
   * 供应商对接业务员ID（仅订单创建时有该分销商+网校有业务员时才有值）
   */
  salesmanId?: string
}

/**
 * 商品查询条件
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuRequest1 {
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品Sku名称
   */
  saleTitle?: string
  /**
   * 期别信息
   */
  issueInfo?: IssueInfo
  /**
   * 商品sku属性查询
   */
  skuProperty?: SkuPropertyRequest
  /**
   * 管理系统平台
   */
  externalTrainingPlatform?: Array<string>
  /**
   * 培训机构
   */
  trainingInstitution?: Array<string>
}

/**
 * 发票关联订单查询参数
<AUTHOR>
@date 2022/3/18
 */
export class InvoiceAssociationInfoRequest {
  /**
   * 关联订单类型
0:订单号
1:批次单号
@see AssociationTypes
   */
  associationType?: number
  /**
   * 订单号 | 批次单号
   */
  associationIdList?: Array<string>
  /**
   * 买家信息
   */
  buyerIdList?: Array<string>
  /**
   * 收款账号
   */
  receiveAccountIdList?: Array<string>
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 地区查询参数
<AUTHOR>
@date 2022/02/25
 */
export class RegionSkuPropertyRequest {
  /**
   * 地区: 省
   */
  province?: string
  /**
   * 地区: 市
   */
  city?: string
  /**
   * 地区: 区县
   */
  county?: string
}

/**
 * 地区查询请求参数
<AUTHOR>
@date 2022/02/25
 */
export class RegionSkuPropertySearchRequest {
  /**
   * 地区匹配方式
<p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
<p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
@see RegionSearchType
   */
  regionSearchType?: number
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyRequest>
}

/**
 * 商品sku属性查询参数
<AUTHOR>
@date 2022/01/25
 */
export class SkuPropertyRequest {
  /**
   * 年度
   */
  year?: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 科目类型
   */
  subjectType?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string>
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>
  /**
   * 卫生行业-培训对象
   */
  trainingObject?: Array<string>
  /**
   * 卫生行业-岗位类别
   */
  positionCategory?: Array<string>
  /**
   * 工勤行业-技术等级
   */
  jobLevel?: Array<string>
  /**
   * 工勤行业-工种
   */
  jobCategory?: Array<string>
  /**
   * 年级
   */
  grade?: Array<string>
  /**
   * 科目
   */
  subject?: Array<string>
  /**
   * 学段
   */
  learningPhase?: Array<string>
  /**
   * 学科
   */
  discipline?: Array<string>
  /**
   * 专题id
   */
  trainingChannelIds?: Array<string>
  /**
   * 黑龙江药师-证书类型
   */
  certificatesType?: Array<string>
  /**
   * 黑龙江药师-执业类别
   */
  practitionerCategory?: Array<string>
  /**
   * 资质类别
   */
  qualificationCategory?: Array<string>
  /**
   * 培训形式
   */
  trainingForm?: Array<string>
}

export class IssueInfo {
  /**
   * 期别id
   */
  issueId?: string
  /**
   * 期别名称
   */
  issueName?: string
  /**
   * 培训编号
   */
  issueNum?: string
  /**
   * 培训开始时间
   */
  trainStartTime?: string
  /**
   * 培训结束时间
   */
  trainEndTime?: string
  /**
   * 当前期别来源类型。下单：SUB_ORDER 换班：EXCHANGE_ORDER 换期:CHANGE_ISSUE
   */
  sourceType?: string
  /**
   * 期别来源类型id 1:子订单号：2：换货单号：3：旧的期别参训资格D
   */
  sourceId?: string
}

/**
 * 获取网校提供的配送渠道查询
 */
export class DeliveryChannelRequest {
  /**
   * 单位ID
   */
  unit?: string
}

/**
 * 换货单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ExchangeOrderRequest {
  /**
   * 换货单号集合
   */
  exchangeOrderNoList?: Array<string>
  /**
   * 换货单关联子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 换货单关联批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 换货单关联子订单买家ID集合
   */
  buyerIdList?: Array<string>
  /**
   * 商品信息: 任一匹配原始商品和新商品
   */
  commodity?: CommoditySkuRequest1
  /**
   * 换货单状态
<p>
0: 申请换货
1: 取消中
2: 退货中
3: 退货失败
4: 申请发货
5: 发货中
6: 发货失败
7: 换货完成
8: 已关闭
@see ExchangeOrderStatus
   */
  statusList?: Array<number>
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 换货单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class ExchangeOrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: ExchangeOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 线下发票查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceRequest {
  /**
   * 发票ID集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票基本信息
   */
  basicData?: OfflineInvoiceBasicDataRequest
  /**
   * 发票关联订单查询参数
   */
  associationInfo?: InvoiceAssociationInfoRequest
  /**
   * 发票配送信息
   */
  invoiceDeliveryInfo?: OfflineInvoiceDeliveryInfoRequest
  /**
   * 所属单位id集合
   */
  unitIds?: Array<string>
  /**
   * 收款账号id
   */
  receiveAccountId?: Array<string>
  /**
   * 期别名称
   */
  issueId?: Array<string>
}

/**
 * 发票排序条件
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceSortRequest {
  /**
   * 用于排序的字段
   */
  field?: OfflineInvoiceSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy1
}

/**
 * 配送地址信息
<AUTHOR>
@date 2022/05/07
 */
export class DeliveryAddressRequest {
  /**
   * 收件人
   */
  consignee?: string
}

/**
 * 配送状态变更时间查询参数
<AUTHOR>
@date 2022/04/06
 */
export class DeliveryStatusChangeTimeRequest {
  /**
   * 未就绪
   */
  unReady?: DateScopeRequest
  /**
   * 已就绪
   */
  ready?: DateScopeRequest
  /**
   * 已配送
   */
  shipped?: DateScopeRequest
  /**
   * 已自取
   */
  taken?: DateScopeRequest
}

/**
 * 快递信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class ExpressRequest {
  /**
   * 快递单号
   */
  expressNo?: string
}

/**
 * 发票开票状态变更时间记录查询参数
<AUTHOR>
@date 2022/04/06
 */
export class InvoiceBillStatusChangTimeRequest {
  /**
   * 发票申请开票时间
   */
  unBillDateScope?: DateScopeRequest
  /**
   * 发票开票时间
   */
  successDateScope?: DateScopeRequest
}

/**
 * 线下发票基本信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceBasicDataRequest {
  /**
   * 发票类型
1:电子发票 2:纸质发票
@see InvoiceTypes
   */
  invoiceTypeList?: Array<number>
  /**
   * 发票种类
1:普通发票 2:增值税普通发票 3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategory?: Array<number>
  /**
   * 发票状态
1:正常
2:作废
@see InvoiceStatus
   */
  invoiceStatus?: number
  /**
   * 发票状态变更时间记录
   */
  invoiceStatusChangeTime?: InvoiceStatusChangeTimeRequest
  /**
   * 发票开票状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  billStatusList?: Array<number>
  /**
   * 发票开票状态变更时间记录
   */
  billStatusChangTime?: InvoiceBillStatusChangTimeRequest
  /**
   * 发票是否冻结
   */
  freeze?: boolean
  /**
   * 发票号集合
   */
  invoiceNoList?: Array<string>
  /**
   * 商品id集合
   */
  commoditySkuIdList?: Array<string>
}

/**
 * 线下发票配送信息
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceDeliveryInfoRequest {
  /**
   * 配送状态
0:未就绪 1：已就绪 2：已自取 3：已配送
@see OfflineDeliveryStatus
   */
  deliveryStatusList?: Array<number>
  /**
   * 配送状态变更时间记录 or 匹配
0:未就绪 1：已就绪 2：已自取 3：已配送
key值 {@link OfflineDeliveryStatus}
   */
  deliveryStatusChangeTime?: DeliveryStatusChangeTimeRequest
  /**
   * 配送方式
0:无 1：自取 2：快递
@see OfflineShippingMethods
   */
  shippingMethodList?: Array<number>
  /**
   * 快递信息
   */
  express?: ExpressRequest
  /**
   * 自取信息
   */
  takeResult?: TakeResultRequest
  /**
   * 配送地址信息
   */
  deliveryAddress?: DeliveryAddressRequest
}

/**
 * 取件信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class TakeResultRequest {
  /**
   * 领取人
   */
  takePerson?: string
}

/**
 * 发票查询参数
<AUTHOR>
@date 2022/03/23
 */
export class OnlineInvoiceRequest {
  /**
   * 发票id集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票基础信息查询参数
   */
  basicData?: OnlineInvoiceBasicDataRequest
  /**
   * 发票关联订单查询参数
   */
  associationInfoList?: Array<InvoiceAssociationInfoRequest>
  /**
   * 蓝票票据查询参数
   */
  blueInvoiceItem?: OnlineInvoiceItemRequest
  /**
   * 红票票据查询参数
   */
  redInvoiceItem?: OnlineInvoiceItemRequest
  /**
   * 所属单位id集合
   */
  unitIds?: Array<string>
  /**
   * 收款账号id
   */
  receiveAccountId?: Array<string>
  /**
   * 期别名称
   */
  issueId?: Array<string>
}

/**
 * 发票排序参数
<AUTHOR>
@date 2022/03/23
 */
export class OnlineInvoiceSortRequest {
  /**
   * 需要排序的字段
   */
  field?: OnlineInvoiceSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 发票开具状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class BillStatusChangeTimeRequest {
  /**
   * 未开具
   */
  unBill?: DateScopeRequest
  /**
   * 开票中
   */
  billing?: DateScopeRequest
  /**
   * 开票成功
   */
  success?: DateScopeRequest
  /**
   * 开票失败
   */
  failure?: DateScopeRequest
}

/**
 * 发票状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class InvoiceStatusChangeTimeRequest {
  /**
   * 正常
   */
  normal?: DateScopeRequest
  /**
   * 作废
   */
  invalid?: DateScopeRequest
}

/**
 * 发票基础信息查询参数
<AUTHOR>
@date 2022/03/23
 */
export class OnlineInvoiceBasicDataRequest {
  /**
   * 发票类型
1:电子发票 2:纸质发票
@see InvoiceTypes
   */
  invoiceType?: number
  /**
   * 发票种类
1:普通发票 2:增值税普通发票 3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategoryList?: Array<number>
  /**
   * 发票状态变更时间
@see InvoiceStatus
   */
  invoiceStatusChangeTime?: InvoiceStatusChangeTimeRequest
  /**
   * 发票状态
1：正常 2：作废
@see InvoiceStatus
   */
  invoiceStatusList?: Array<number>
  /**
   * 蓝票票据开具状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  blueInvoiceItemBillStatusList?: Array<number>
  /**
   * 红票票据开具状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  redInvoiceItemBillStatusList?: Array<number>
  /**
   * 发票是否已冲红
   */
  flushed?: boolean
  /**
   * 发票是否已生成红票票据
   */
  redInvoiceItemExist?: boolean
  /**
   * 商品id集合
   */
  commoditySkuIdList?: Array<string>
  /**
   * 发票是否冻结
   */
  freeze?: boolean
}

/**
 * 发票票据
<AUTHOR>
@date 2022/03/18
 */
export class OnlineInvoiceItemRequest {
  /**
   * 票据开具状态变更时间
   */
  billStatusChangeTime?: BillStatusChangeTimeRequest
  /**
   * 发票号码
   */
  billNoList?: Array<string>
}

/**
 * 用户有效商品查询参数
<AUTHOR>
@date 2022/03/24
 */
export class BuyerValidCommodityRequest {
  /**
   * 买家id
   */
  buyerId?: string
  /**
   * 商品sku查询
   */
  skuProperty?: SkuPropertyRequest
  /**
   * 商品名称（模糊查询）
   */
  saleTitle?: string
  /**
   * 期别名称（模糊查询）
   */
  issueName?: string
}

export class IssueLogRequest {
  /**
   * 子订单号
   */
  subOrderNoList?: Array<string>
  /**
   * 买家id
   */
  buyerId?: string
  /**
   * 商品sku查询
   */
  skuProperty?: SkuPropertyRequest
  /**
   * 商品名称（模糊查询）
   */
  saleTitle?: string
  /**
   * 期别名称（模糊查询）
   */
  issueName?: string
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class OrderRequest {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 子订单退货状态
0: 未退货
1: 退货申请中
2: 退货中
3: 退货成功
4: 退款中
5: 退款成功
@see SubOrderReturnStatus
   */
  subOrderReturnStatus?: Array<number>
  /**
   * 订单基本信息查询参数
   */
  orderBasicData?: OrderBasicDataRequest
  /**
   * 子订单基本信息查询参数
   */
  subOrderBasicData?: SubOrderBasicDataRequest
  /**
   * 订单支付信息查询
   */
  payInfo?: OrderPayInfoRequest
  /**
   * 买家查询参数
   */
  buyerIdList?: Array<string>
  /**
   * 发货商品信息
   */
  deliveryCommodity?: CommoditySkuRequest1
  /**
   * 现有商品信息
   */
  currentCommodity?: CommoditySkuRequest1
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannel?: number
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 需要排除的销售渠道
0-自营 1-分销 2专题
   */
  excludeSaleChannels?: Array<number>
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 华医网卡类型id
   */
  cardTypeId?: string
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 订单属性组合查询
   */
  orderFixQuery?: OrderFixQueryRequest
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
  /**
   * 管理系统平台
   */
  externalTrainingPlatform?: Array<string>
  /**
   * 单位id集合
   */
  unitIds?: Array<string>
  /**
   * 期别id
   */
  issueId?: Array<string>
  /**
   * 培训计划ID，例如补贴性培训平台和补贴管理系统对接
   */
  policyTrainingSchemeIdList?: Array<string>
  /**
   * 申报单位统一信用代码，精确匹配
   */
  declarationUnitCodeList?: Array<string>
  /**
   * 结算状态
@see com.fjhb.ms.trade.query.common.constants.SettlementStatusConstants
   */
  settlementStatus?: number
  /**
   * 结算时间
   */
  settlementDate?: DateScopeRequest
  /**
   * 商品品类
   */
  commodityCategory?: Array<string>
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class OrderRequestInDistributor {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 子订单退货状态
0: 未退货
1: 退货申请中
2: 退货中
3: 退货成功
4: 退款中
5: 退款成功
@see SubOrderReturnStatus
   */
  subOrderReturnStatus?: Array<number>
  /**
   * 订单基本信息查询参数
   */
  orderBasicData?: OrderBasicDataRequest
  /**
   * 子订单基本信息查询参数
   */
  subOrderBasicData?: SubOrderBasicDataRequest
  /**
   * 订单支付信息查询
   */
  payInfo?: OrderPayInfoRequest
  /**
   * 买家查询参数
   */
  buyerIdList?: Array<string>
  /**
   * 发货商品信息
   */
  deliveryCommodity?: CommoditySkuRequest1
  /**
   * 现有商品信息
   */
  currentCommodity?: CommoditySkuRequest1
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannel?: number
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 华医网卡类型id
   */
  cardTypeId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: OrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 订单基本信息查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderBasicDataRequest {
  /**
   * 订单类型
1:常规订单 2:批次关联订单
@see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType?: number
  /**
   * 批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 订单状态
<br> 1:正常 2：交易完成 3：交易关闭
@see OrderStatus
   */
  orderStatusList?: Array<number>
  /**
   * 订单支付状态
<br> 0:未支付 1：支付中 2：已支付
@see com.fjhb.domain.trade.api.order.consts.OrderPaymentStatus
   */
  orderPaymentStatusList?: Array<number>
  /**
   * 订单发货状态
<br> 0:未发货 1：发货中 2：已发货
@see com.fjhb.domain.trade.api.order.consts.OrderDeliveryStatus
   */
  orderDeliveryStatusList?: Array<number>
  /**
   * 订单状态变更时间
   */
  orderStatusChangeTime?: OrderStatusChangeTimeRequest
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelTypesList?: Array<number>
  /**
   * 需要排除的购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  excludeChannelTypesList?: Array<number>
  /**
   * 终端
<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCodeList?: Array<string>
  /**
   * 订单价格范围
<br> 查询非0元订单 begin填0.01
   */
  orderAmountScope?: BigDecimalScopeRequest
}

/**
 * <AUTHOR> linq
@date : 2024-09-05 18:18
@description：订单属性组合查询
 */
export class OrderFixQueryRequest {
  /**
   * 需要排除的购买渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  excludeChannelTypesList?: Array<number>
  /**
   * 需要排除的销售渠道
   */
  excludeSaleChannels?: Array<number>
}

/**
 * 订单支付信息相关查询参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderPayInfoRequest {
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 付款类型
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
   */
  paymentOrderTypeList?: Array<number>
}

/**
 * 订单状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderStatusChangeTimeRequest {
  /**
   * 订单处于正常状态时间范围(创建时间范围)
   */
  normalDateScope?: DateScopeRequest
  /**
   * 订单创建时间范围
   */
  completedDatesScope?: DateScopeRequest
}

/**
 * 子订单基本信息查询参数
<AUTHOR>
@date 2023/12/25
 */
export class SubOrderBasicDataRequest {
  /**
   * 优惠类型
@see DiscountType
   */
  discountType?: number
  /**
   * 优惠来源ID | 优惠申请单ID
   */
  discountSourceId?: string
  /**
   * 是否使用优惠
   */
  useDiscount?: boolean
  /**
   * 商品分销授权信息
   */
  commodityAuthInfo?: CommodityAuthInfoRequest
}

/**
 * 退货单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderRequest {
  /**
   * 单位id集合
   */
  unitIdList?: Array<string>
  /**
   * 退货单号
   */
  returnOrderNoList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: ReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: ReturnOrderApprovalInfoRequest
  /**
   * 退货商品id集合
   */
  returnCommoditySkuIdList?: Array<string>
  /**
   * 退货商品查询条件
   */
  returnCommodity?: CommoditySkuRequest1
  /**
   * 退款商品id集合
   */
  refundCommoditySkuIdList?: Array<string>
  /**
   * 退款商品查询条件
   */
  refundCommodity?: CommoditySkuRequest1
  /**
   * 退货单关联子订单查询参数
   */
  subOrderInfo?: SubOrderInfoRequest
  /**
   * 商品分销授权信息
   */
  commodityAuthInfo?: CommodityAuthInfoRequest
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 退货单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderRequestInDistributor {
  /**
   * 退货单号
   */
  returnOrderNoList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: ReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: ReturnOrderApprovalInfoRequest
  /**
   * 退货商品id集合
   */
  returnCommoditySkuIdList?: Array<string>
  /**
   * 退款商品id集合
   */
  refundCommoditySkuIdList?: Array<string>
  /**
   * 退货单关联子订单查询参数
   */
  subOrderInfo?: SubOrderInfoRequest
  /**
   * 商品分销授权信息
   */
  commodityAuthInfo?: CommodityAuthInfoRequest
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
  /**
   * 退货商品查询条件
   */
  returnCommodity?: CommoditySkuRequest1
  /**
   * 退款商品查询条件
   */
  refundCommodity?: CommoditySkuRequest1
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class ReturnSortRequest {
  /**
   * 需要排序的字段
   */
  field?: ReturnOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 退货单关联子订单的主订单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class OrderInfoRequest {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 关联批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 买家id集合
   */
  buyerIdList?: Array<string>
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 原始订单交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelTypesList?: Array<number>
  /**
   * 终端
<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCodeList?: Array<string>
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 培训计划ID，例如补贴性培训平台和补贴管理系统对接
   */
  policyTrainingSchemeIdList?: Array<string>
  /**
   * 申报单位统一信用代码，精确匹配
   */
  declarationUnitCodeList?: Array<string>
}

/**
 * 退货单关闭信息
<AUTHOR>
@date 2022年4月11日 11:33:35
 */
export class ReturnCloseReasonRequest {
  /**
   * 退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
@see ReturnOrderCloseTypes
   */
  closeTypeList?: Array<number>
}

/**
 * 退货单审批信息查询参数
<AUTHOR>
@date 2022/03/18
 */
export class ReturnOrderApprovalInfoRequest {
  /**
   * 审批时间
   */
  approveTime?: DateScopeRequest
}

/**
 * 退货单基本信息查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderBasicDataRequest {
  /**
   * 退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
   */
  returnOrderStatus?: Array<number>
  /**
   * 退货单类型
1-仅退货
2-仅退款
3-退货并退款
4-部分退货
5-部分退款
6-部分退货并部分退款
7-部分退货并全额退款
8-全部退货并部分退款
   */
  returnOrderTypes?: Array<number>
  /**
   * 退货单申请来源类型
SUB_ORDER
BATCH_RETURN_ORDER
@see ReturnOrderApplySourceTypes
   */
  applySourceType?: string
  /**
   * 来源ID集合
   */
  applySourceIdList?: Array<string>
  /**
   * 退货单关闭信息
   */
  returnCloseReason?: ReturnCloseReasonRequest
  /**
   * 退货单状态变更时间
   */
  returnStatusChangeTime?: ReturnOrderStatusChangeTimeRequest
  /**
   * 退款金额范围
<br> 查询非0元  begin填0.01
   */
  refundAmountScope?: BigDecimalScopeRequest
}

/**
 * 退货单状态变更时间查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderStatusChangeTimeRequest {
  /**
   * 申请退货时间
   */
  applied?: DateScopeRequest
  /**
   * 退货单完成时间
<br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
   */
  returnCompleted?: DateScopeRequest
}

/**
 * <AUTHOR>
@date 2022/03/24
 */
export class SubOrderInfoRequest {
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 订单查询参数
   */
  orderInfo?: OrderInfoRequest
  /**
   * 子订单优惠类型
@see DiscountType
   */
  discountType?: number
  /**
   * 子订单是否使用优惠
   */
  useDiscount?: boolean
}

/**
 * 发票开票策略查询条件
<AUTHOR>
 */
export class InvoiceAutoBillPolicyRequest {
  /**
   * 自动配置类型
<br> 1:个人发票 2：集体发票
   */
  configType?: number
}

/**
 * 收款账号查询条件
 */
export class ReceiveAccountConfigRequest {
  /**
   * 收款账户类型
<p>
1：线上收款帐号
2：线下收款帐号
   */
  accountType?: number
  /**
   * 收款帐号状态
<p>
0:停用
1:可用
   */
  status?: number
  /**
   * 服务商ID
   */
  servicerId?: string
  /**
   * 单位Id
   */
  unitId?: string
}

/**
 * 获取收款账号列表
 */
export class ReceiveAccountRequest {
  /**
   * 收款账户
   */
  receiveAccountIdList?: Array<string>
  /**
   * 单位Id
   */
  unitId?: string
}

/**
 * 获取纳税人去请求
 */
export class TaxpayerIRequest {
  /**
   * 单位id
   */
  unitId?: string
}

/**
 * 商品开通统计排序参数
<AUTHOR>
@date 2022/01/27
 */
export class CommodityOpenReportSortRequest {
  /**
   * 需要排序的字段
   */
  field?: CommodityOpenReportSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * <AUTHOR> linq
@date : 2024-10-10 10:25
@description：交易记录缺失数据比对request
 */
export class TradeReportMissDataRequest {
  /**
   * 本次需要收集的订单数量
   */
  collectNum?: number
  /**
   * 交易时间范围
   */
  tradeTime?: DateScopeRequest
}

/**
 * 商品开通统计报表查询参数
<AUTHOR>
@date 2022/05/11
 */
export class TradeReportRequest {
  /**
   * 交易时间范围
   */
  tradeTime?: DateScopeRequest
  /**
   * 买家所在地区路径
   */
  buyerAreaPath?: Array<string>
  /**
   * 商品地区路径
   */
  commodityAreaPath?: Array<string>
  /**
   * 商品查询条件
   */
  commoditySku?: CommoditySkuRequest12
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  /**
   * 销售渠道
0-自营 1-分销 2专题 3-华医网 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 排除的销售渠道
0-自营 1-分销 2-专题 3-华医网
   */
  excludedSaleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 查看非推广门户数据 | true 为勾选效果
   */
  notDistributionPortal?: boolean
  /**
   * 收款账户
   */
  receiveAccountIdList?: Array<string>
  /**
   * 期别id
   */
  issueId?: Array<string>
  /**
   * 机构ID集合
   */
  institutionIdList?: Array<string>
}

/**
 * 交易时间段统计信息直方图参数
<AUTHOR>
 */
export class TradeStatisticDateHistogramRequest {
  /**
   * 时间单位  1 周 、2 年、3 季度、 4 月、 5 天、6 小时、7 分钟、8 秒
@see DateHistogramTimeUnit
   */
  timeUnit?: number
  /**
   * 时间单位间隔
   */
  interval?: number
  /**
   * 统计日期
   */
  statisticTime?: DateScopeRequest
}

/**
 * 商品查询参数
<AUTHOR>
@date 2022/05/11
 */
export class CommoditySkuRequest12 {
  /**
   * 商品Sku名称
   */
  saleTitle?: string
  /**
   * 商品sku属性查询
   */
  skuProperty?: SkuPropertyRequest
  /**
   * 学习方案查询参数
   */
  scheme?: SchemeRequest1
  /**
   * 排除的商品List
   */
  excludeCommodityIdlist?: Array<string>
}

/**
 * 方案查询参数
<AUTHOR>
@date 2022/05/11
 */
export class SchemeRequest1 {
  /**
   * 方案类型
@see SchemeType
   */
  schemeType?: string
  /**
   * 方案学时
   */
  schemePeriodScope?: DoubleScopeRequest
}

export class UserEntity {
  userId: string
}

export class DiscountPolicyModel {
  discountPolicyId: string
  discountId: number
}

export class RegionModel {
  regionId: string
  province: string
  city: string
  county: string
  path: string
}

export class UserModel {
  userId: string
}

/**
 * 批次单返回模型
<AUTHOR>
@date 2022/4/16
 */
export class BatchOrderResponse {
  /**
   * 批次单号
   */
  batchOrderNo: string
  /**
   * 批次单基本信息
   */
  basicData: BatchOrderBasicDataResponse
  /**
   * 支付信息
   */
  payInfo: PayInfoResponse
  /**
   * 创建人
   */
  creator: UserResponse
  /**
   * 是否已经申请发票
   */
  isInvoiceApplied: boolean
  /**
   * 发票申请信息
   */
  invoiceApplyInfo: InvoiceApplyInfoResponse
}

/**
 * 批次单统计信息返回值
<AUTHOR>
@date 2022/01/26
 */
export class BatchOrderStatisticResponse {
  /**
   * 批次单总数量
   */
  totalBatchOrderCount: number
  /**
   * 批次单总金额
   */
  totalBatchOrderAmount: number
}

/**
 * 批次单基本信息
<AUTHOR>
@date 2022/4/17
 */
export class BatchOrderBasicDataResponse {
  /**
   * 批次单类型
<p>
0；批次缴费批次单
1；个人缴费批次单
2；无需缴费批次单
@see BatchOrderTypes
   */
  batchOrderType: number
  /**
   * 终端类型
<p>
Web: Web端
H5: H5端
IOS: IOS端
Android: 安卓端
WechatMini: 微信小程序
WechatOfficial: 微信公众号
ExternalSystemManage: 外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCode: string
  /**
   * 批次单金额
   */
  amount: number
  /**
   * 批次单的批次订单数量
   */
  orderForBatchCount: number
  /**
   * 批次单状态
0: 未确认，批次单初始状态
1: 正常
2: 交易完成
3: 交易关闭
4: 提交处理中 提交处理完成后，变更为NORMAl
5: 取消处理中
@see BatchOrderStatus
   */
  batchOrderStatus: number
  /**
   * 批次单状态变更时间记录
   */
  batchOrderStatusChangeTime: BatchOrderStatusChangeTimeResponse
  /**
   * 批次单支付状态
<p>
0：未支付
1：支付中
2：已支付
@see BatchOrderPaymentStatus
   */
  paymentStatus: number
  /**
   * 批次单支付状态变更时间
   */
  paymentStatusChangeTime: BatchOrderPaymentStatusChangeTimeResponse
  /**
   * 批次单发货状态
0: 未发货
1: 发货中
2: 已发货
@see BatchOrderDeliveryStatus
   */
  deliveryStatus: number
  /**
   * 批次单发货状态变更时间
   */
  deliveryStatusChangeTime: BatchOrderDeliveryStatusChangeTimeResponse
  /**
   * 批次单退货状态
0: 未退货
1: 退货中
2: 已部分退货
3: 退货完成
@see BatchOrderReturnStatus
   */
  batchOrderReturnStatus: number
  /**
   * 批次单退款状态
0: 未退款
1: 退款中
2: 已部分退货
3: 退货完成
@see BatchOrderRefundStatus
   */
  batchOrderRefundStatus: number
  /**
   * 批次单关闭原因
   */
  batchOrderCloseReason: BatchOrderCloseReasonResponse
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 销售渠道id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
  /**
   * 销售全路径
   */
  salePathList: Array<SalePathResponse>
}

/**
 * 批次单交易关闭原因
<AUTHOR>
@date 2022/2/14
 */
export class BatchOrderCloseReasonResponse {
  /**
   * 交易关闭类型
<p>
0：未关闭
1：买家关闭
2：系统关闭
@see BatchOrderCloseTypes
   */
  closedType: number
  /**
   * 交易关闭原因ID
   */
  reasonId: string
  /**
   * 交易关闭原因说明
   */
  reason: string
  /**
   * 取消操作人
   */
  cancelUser: UserResponse
}

/**
 * 批次单发货状态变更时间
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderDeliveryStatusChangeTimeResponse {
  /**
   * 未确认
   */
  none: string
  /**
   * 发货中
   */
  delivering: string
  /**
   * 已发货
   */
  delivered: string
}

/**
 * 批次单支付状态变更时间
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderPaymentStatusChangeTimeResponse {
  /**
   * 未确认
   */
  none: string
  /**
   * 支付中
   */
  paying: string
  /**
   * 已支付
   */
  paid: string
}

/**
 * 批次单状态变更时间
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderStatusChangeTimeResponse {
  /**
   * 未确认
   */
  unConfirmed: string
  /**
   * 正常
   */
  normal: string
  /**
   * 交易成功
   */
  completed: string
  /**
   * 已关闭
   */
  closed: string
  /**
   * 提交中
   */
  committing: string
  canceling: string
}

/**
 * 批次退货单
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnOrderResponse {
  /**
   * 批次退货单号
   */
  batchReturnOrderNo: string
  /**
   * 批次退货单基本信息
   */
  basicData: BatchReturnOrderBasicDataResponse
  /**
   * 批次退货单关联批次单信息
   */
  batchOrderInfo: BatchOrderInfoResponse
  /**
   * 批次退货单是否需要人工审批
   */
  needManualApprove: boolean
  /**
   * 批次退货单审批信息
   */
  approvalInfo: BatchReturnApprovalInfoResponse
  /**
   * 退款确认人
   */
  confirmUser: UserResponse
  /**
   * 批次退货单关联退款单信息
   */
  refundInfo: RefundInfoResponse
}

/**
 * 批次退货单统计信息返回值
<AUTHOR>
@date 2022/01/26
 */
export class BatchReturnOrderStatisticResponse {
  /**
   * 批次退货单总数量
   */
  totalBatchReturnOrderCount: number
  /**
   * 批次退款单退款总金额
   */
  totalBatchReturnOrderRefundAmount: number
}

/**
 * 批次退货单关联批次单信息
<AUTHOR>
@date 2022/04/19
 */
export class BatchOrderInfoResponse {
  /**
   * 批次单号
   */
  batchOrderNo: string
  /**
   * 批次单支付信息
   */
  paymentInfo: PaymentInfoResponse
  /**
   * 批次单创建人
   */
  creator: UserResponse
}

/**
 * 批次退货审批信息
<AUTHOR>
@date 2022/03/18
 */
export class BatchReturnApprovalInfoResponse {
  /**
   * 审批状态（0：未审批 1：已审批）
@see BatchReturnApprovalReportStatus
   */
  approveStatus: number
  /**
   * 审批结果（-1：无 0：拒绝 1：同意）
@see BatchReturnApprovalReportResults
   */
  approveResult: number
  /**
   * 审批人
   */
  approveUser: UserResponse
  /**
   * 审批意见
   */
  approveComment: string
  /**
   * 审批时间
   */
  approveTime: string
}

/**
 * 批次退货申请信息返回值
<AUTHOR>
@date 2022/03/24
 */
export class BatchReturnOrderApplyInfoResponse {
  /**
   * 申请人
   */
  applyUser: UserResponse
  /**
   * 申请原因内容id
   */
  reasonId: string
  /**
   * 申请原因内容
   */
  reasonContent: string
  /**
   * 申请描述
   */
  description: string
}

/**
 * 批次退货单基本信息
<AUTHOR>
@date 2022/04/19
 */
export class BatchReturnOrderBasicDataResponse {
  /**
   * 批次退货单类型
1: 仅退货
2: 仅退款
3: 退货退款
@see BatchReturnOrderTypes
   */
  batchReturnOrderType: number
  /**
   * 退款总额
   */
  refundAmount: number
  /**
   * 批次退货单的退货单数量
   */
  returnOrderCount: number
  /**
   * 批次退货单状态
0: 已创建
1: 已确认
2: 取消申请中
3: 退货处理中
4: 退货失败
5: 正在申请退款
6: 已申请退款
7: 退款处理中
8: 退款失败
9: 退货完成
10: 退款完成
11: 退货退款完成
12: 已关闭
@see BatchReturnOrderStatus
   */
  batchReturnOrderStatus: number
  /**
   * 批次退货单状态变更时间
   */
  batchReturnOrderStatusChangeTime: BatchReturnOrderStatusChangeTimeResponse
  /**
   * 批次退货单申请信息
   */
  applyInfo: BatchReturnOrderApplyInfoResponse
  /**
   * 批次退货单关闭信息
   */
  closeReason: BatchReturnOrderCloseReasonResponse
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 销售渠道id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
  salePathList: Array<SalePathResponse>
}

/**
 * 批次退货单关闭原因
<AUTHOR>
@date 2022/03/29
 */
export class BatchReturnOrderCloseReasonResponse {
  /**
   * 批次退货单关闭类型（（1：卖家取消 2：卖家拒绝退货 3：买家取消 4：确认失败取消））
@see BatchReturnCloseTypes
   */
  closeType: number
  /**
   * 退货单取消人
   */
  cancelUser: UserResponse
  /**
   * 取消原因
   */
  cancelReason: string
  /**
   * 确认失败信息
   */
  confirmFailureMessage: string
}

/**
 * 退货单状态变更时间
<AUTHOR>
@date 2022/03/23
 */
export class BatchReturnOrderStatusChangeTimeResponse {
  /**
   * 已创建
   */
  created: string
  /**
   * 已确认
   */
  confirmed: string
  /**
   * 申请退货取消处理中时间
   */
  cancelApplying: string
  /**
   * 退货处理中时间
   */
  returning: string
  /**
   * 退货失败时间
   */
  returnFailed: string
  /**
   * 正在申请退款时间
   */
  refundApplying: string
  /**
   * 已申请退款时间
   */
  refundApplied: string
  /**
   * 退款处理中时间
   */
  refunding: string
  /**
   * 退款失败
   */
  refundFailed: string
  /**
   * 退货完成时间
   */
  returned: string
  /**
   * 退款完成时间
   */
  refunded: string
  /**
   * 退货退款完成时间
   */
  returnedAndRefunded: string
  /**
   * 退货单完成时间
   */
  returnCompleted: string
  /**
   * 已关闭时间
   */
  closed: string
}

export class CommoditySaleChannelResponse {
  /**
   * 商品的销售渠道，0.网校 1.分销 2.专题 3.华医网
   */
  saleChannel: number
  /**
   * 销售渠道是否可用
   */
  available: boolean
  /**
   * 销售渠道下的购买渠道列表
   */
  purchaseSubjectList: Array<CommoditySaleChannelPurchaseSubjectResponse>
}

/**
 * 后台商品返回值模型
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuBackstageResponse {
  /**
   * 商品id
   */
  commoditySkuId: string
  /**
   * 培训班商品基本信息
   */
  commodityBasicData: CommodityBasicDataResponse
  /**
   * 商品属性信息
   */
  skuProperty: CommoditySkuPropertyResponse
  /**
   * 上下架信息
   */
  onShelve: OnShelveResponse
  /**
   * 商品所有渠道的配置信息
   */
  commodityPurchaseChannelConfig: CommodityPurchaseChannelConfigResponse
  /**
   * 商品关联资源信息
   */
  resource: ResourceResponse
  /**
   * 最后编辑时间
   */
  commodityLastEditTime: string
  /**
   * 商品创建时间
   */
  commodityCreatTime: string
  /**
   * 商品资源是否可用
   */
  isResourceEnabled: boolean
  /**
   * 专题
   */
  trainingChannels: Array<CommodityTrainingChannelResponse>
  /**
   * 第三方平台类型
   */
  tppTypeId: string
  /**
   * 管理系统平台
   */
  externalTrainingPlatform: string
  /**
   * 学习监管系统
   */
  learningSupervisionSystem: string
  /**
   * 课程供应商单位ID
   */
  courseSupplierId: string
  /**
   * 第三方培训方案id
   */
  thirdPartyTrainingSchemeId: string
  /**
   * 开通数
   */
  saleTotalNumber: number
  /**
   * 方案期数信息
   */
  issueInfoResponse: SchemeIssueInfoResponse
  /**
   * 所属单位id
   */
  unitId: string
  /**
   * 所属单位名称
   */
  unitName: string
  /**
   * 商品拓展信息
   */
  extInfo: CommodityExtInfoResponse
  /**
   * 商品销售渠道信息
   */
  commoditySaleChannel: Array<CommoditySaleChannelResponse>
}

/**
 * <AUTHOR> linq
@date : 2024-12-10 13:38
@description：培训班商品期别信息response
 */
export class IssueCommoditySkuBackStageResponse {
  /**
   * 商品信息
   */
  commoditySkuInfo: IssueSourceCommoditySkuInfoBackStageResponse
  /**
   * 期别资源信息
   */
  issueResourceInfo: SchemeIssueResourceBackStageResponse
  /**
   * 剩余报名人数
   */
  remainingRegisterNumber: number
}

/**
 * 地区响应
 */
export class RegionResponse {
  /**
   * 省
   */
  province: string
  /**
   * 市
   */
  city: string
  /**
   * 区县
   */
  county: string
  /**
   * 路径
   */
  path: string
}

/**
 * 商品基本信息返回值
<AUTHOR>
@date 2022/1/25
 */
export class CommodityBasicDataResponse {
  /**
   * 商品销售标题
   */
  saleTitle: string
  /**
   * 商品价格
   */
  price: number
  /**
   * 商品封面图路径
   */
  commodityPicturePath: string
}

/**
 * <AUTHOR> linq
@date : 2025-01-07 14:50
@description：商品拓展信息返回值
 */
export class CommodityExtInfoResponse {
  /**
   * 资源供应商信息
   */
  resourceProviderResponse: CommodityResourceProviderResponse
}

/**
 * 商品渠道配置
<AUTHOR>
@date 2022/01/26
 */
export class CommodityPurchaseChannelConfigResponse {
  /**
   * 用户自主购买
   */
  customerPurchase: PurchaseChannelConfigResponse
  /**
   * 集体缴费
   */
  collectivePurchase: PurchaseChannelConfigResponse
  /**
   * 管理员导入
   */
  administratorImport: PurchaseChannelConfigResponse
  /**
   * 集体报名个人缴费渠道
   */
  collectiveSignUpPersonalPay: PurchaseChannelConfigResponse
}

/**
 * <AUTHOR> linq
@date : 2025-01-07 14:54
@description：商品资源供应商信息
 */
export class CommodityResourceProviderResponse {
  /**
   * 商品资源供应商id
   */
  id: string
  /**
   * 商品资源供应商名称
   */
  name: string
}

export class CommoditySaleChannelPurchaseSubjectResponse {
  /**
   * 购买主体类型,  1.学员  2.集体报名
   */
  subjectType: number
  /**
   * 是否可见
   */
  isShow: boolean
}

/**
 * 商品sku属性返回值
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuPropertyResponse {
  /**
   * 年度
   */
  year: SkuPropertyResponse
  /**
   * 地区 - 省
   */
  province: SkuPropertyResponse
  /**
   * 地区 - 市
   */
  city: SkuPropertyResponse
  /**
   * 地区 - 县
   */
  county: SkuPropertyResponse
  /**
   * 行业
   */
  industry: SkuPropertyResponse
  /**
   * 科目类型
   */
  subjectType: SkuPropertyResponse
  /**
   * 培训类别
   */
  trainingCategory: SkuPropertyResponse
  /**
   * 培训专业
   */
  trainingProfessional: SkuPropertyResponse
  /**
   * 技术等级
   */
  technicalGrade: SkuPropertyResponse
  /**
   * 卫生行业-培训对象
   */
  trainingObject: SkuPropertyResponse
  /**
   * 卫生行业-岗位类别
   */
  positionCategory: SkuPropertyResponse
  /**
   * 工勤行业-技术等级
   */
  jobLevel: SkuPropertyResponse
  /**
   * 工勤行业-工种
   */
  jobCategory: SkuPropertyResponse
  /**
   * 年级
   */
  grade: SkuPropertyResponse
  /**
   * 科目
   */
  subject: SkuPropertyResponse
  /**
   * 学段
   */
  learningPhase: SkuPropertyResponse
  /**
   * 学科
   */
  discipline: SkuPropertyResponse
  /**
   * 黑龙江药师-证书类型
   */
  certificatesType: SkuPropertyResponse
  /**
   * 黑龙江药师-执业类别
   */
  practitionerCategory: SkuPropertyResponse
  /**
   * 资质类别
   */
  qualificationCategory: SkuPropertyResponse
  /**
   * 培训形式
   */
  trainingForm: SkuPropertyResponse
}

export class CommodityTrainingChannelResponse {
  /**
   * 专题id
   */
  trainingChannelId: string
  /**
   * 专题名称
   */
  trainingChannelName: string
  /**
   * 排序
   */
  sort: number
}

/**
 * <AUTHOR> linq
@date : 2024-12-10 13:40
@description：期别原商品信息
 */
export class IssueSourceCommoditySkuInfoBackStageResponse {
  /**
   * 商品SkuID
   */
  commoditySkuId: string
  /**
   * Sku属性
   */
  skuPropertyResponse: CommoditySkuPropertyResponse
  /**
   * 商品所有渠道的配置信息
   */
  commodityPurchaseChannelConfig: CommodityPurchaseChannelConfigResponse
}

/**
 * 商品上下架信息
<AUTHOR>
@date 2022/1/25
 */
export class OnShelveResponse {
  /**
   * 商品当前上架状态
<br> 0:已下架 1：已上架
@see CommoditySkuShelveStatus
   */
  shelveStatus: number
  /**
   * 最新上架时间
   */
  lastOnShelveTime: string
  /**
   * 最新下架时间
   */
  offShelveTime: string
  /**
   * 最新计划上架时间
   */
  onShelvePlanTime: string
  /**
   * 最新计划下架时间
   */
  offShelvePlanTime: string
  /**
   * 发布时间
   */
  publishTime: string
}

/**
 * 购买渠道配置
<AUTHOR>
@date 2022/01/26
 */
export class PurchaseChannelConfigResponse {
  /**
   * 是否开启可见
   */
  couldSee: boolean
  /**
   * 是否开启可购买
   */
  couldBuy: boolean
}

/**
 * <AUTHOR> linq
@date : 2024-11-08 19:35
@description：方案期数信息
 */
export class SchemeIssueInfoResponse {
  /**
   * 期别数量
   */
  issueCount: number
}

/**
 * <AUTHOR> linq
@date : 2024-12-10 13:51
@description：方案期别资源信息
 */
export class SchemeIssueResourceBackStageResponse {
  /**
   * 期别id
   */
  issueId: string
  /**
   * 期别编号
   */
  issueNum: string
  /**
   * 期别名称
   */
  issueName: string
  /**
   * 期别报名开始时间
   */
  issueSignUpBeginDate: string
  /**
   * 期别报名结束时间
   */
  issueSignUpEndDate: string
  /**
   * 期别培训开始时间
   */
  issueTrainingBeginDate: string
  /**
   * 期别培训结束时间
   */
  issueTrainingEndDate: string
}

/**
 * 商品sku属性
<AUTHOR>
@date 2022/01/25
 */
export class SkuPropertyResponse {
  /**
   * sku属性值id
   */
  skuPropertyValueId: string
  /**
   * sku属性值名称
   */
  skuPropertyValueName: string
}

/**
 * 商品资源
<AUTHOR>
@date 2022/03/02
 */
export interface ResourceResponse {
  /**
   * 资源类型
<br> Scheme:方案 ,Issue:期数
@see com.fjhb.domain.trade.api.commodity.consts.CommoditySaleResourceTypes
   */
  resourceType: string
}

/**
 * 培训方案信息
<AUTHOR>
@date 2022/03/02
 */
export class SchemeResourceResponse implements ResourceResponse {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 方案名
   */
  schemeName: string
  /**
   * 学时
   */
  period: number
  /**
   * 培训方案类型
<p>
chooseCourseLearning：选课学习
autonomousCourseLearning：自主学习
@see com.fjhb.domain.learningscheme.api.scheme.consts.SchemeType
   */
  schemeType: string
  /**
   * 资源类型
<br> Scheme:方案 ,Issue:期数
@see com.fjhb.domain.trade.api.commodity.consts.CommoditySaleResourceTypes
   */
  resourceType: string
}

export class Accommodation {
  /**
   * 是否安排住宿
   */
  IsAccommodation: boolean
  /**
   * 住宿方式
0-无需住宿
1-1单人住宿
2-合住
   */
  accommodationType: number
}

/**
 * <AUTHOR> linq
@date : 2023-12-20 19:09
@description：商品分销授权信息
 */
export class CommodityAuthInfoResponse {
  /**
   * 商品授权ID（产品分销授权ID）
   */
  commodityAuthId: string
  /**
   * 商品授权分销商ID
   */
  distributorId: string
  /**
   * 商品授权分销商 名称
   */
  distributorName: string
  /**
   * 分销级别
@see DistriButionLevel
   */
  distributionLevel: number
  /**
   * 上级分销商ID（仅二级分销商有该值，分销合同内的上级分销商ID）
   */
  superiorDistributorId: string
  /**
   * 上级分销商 名称
   */
  superiorDistributorName: string
  /**
   * 商品授权分销商ID路径（格式为 /供应商ID/一级分销商ID/二级分销商ID）
   */
  distributorIdPath: string
  /**
   * 商品授权供应商ID
   */
  supplierId: string
  /**
   * 商品授权供应商名称
   */
  supplierName: string
  /**
   * 供应商对接业务员ID（仅订单创建时有该分销商+网校有业务员时才有值）
   */
  salesmanId: string
  /**
   * 供应商对接业务员名称
   */
  salesmanName: string
  /**
   * 分销商电话
   */
  distributorPhone: string
  /**
   * 分销商单位统一社会信用代码
   */
  distributorUnitCreditCode: string
  /**
   * 分销商类型 1-个人 2-企业
   */
  distributorPartnerType: number
  /**
   * 供应商类型 1-个人 2-企业
   */
  supplierPartnerType: number
}

/**
 * 订单商品sku属性信息
<AUTHOR>
@date 2022/01/26
 */
export class CommoditySkuPropertyResponse1 {
  /**
   * 年度
   */
  year: SkuPropertyResponse1
  /**
   * 地区 - 省
   */
  province: SkuPropertyResponse1
  /**
   * 地区 - 市
   */
  city: SkuPropertyResponse1
  /**
   * 地区 - 区县
   */
  county: SkuPropertyResponse1
  /**
   * 行业
   */
  industry: SkuPropertyResponse1
  /**
   * 科目类型
   */
  subjectType: SkuPropertyResponse1
  /**
   * 培训类别
   */
  trainingCategory: SkuPropertyResponse1
  /**
   * 培训专业
   */
  trainingProfessional: SkuPropertyResponse1
  /**
   * 技术等级
   */
  technicalGrade: SkuPropertyResponse1
  /**
   * 卫生行业-培训对象
   */
  trainingObject: SkuPropertyResponse1
  /**
   * 卫生行业-岗位类别
   */
  positionCategory: SkuPropertyResponse1
  /**
   * 工勤行业-技术等级
   */
  jobLevel: SkuPropertyResponse1
  /**
   * 工勤行业-工种
   */
  jobCategory: SkuPropertyResponse1
  /**
   * 年级
   */
  grade: SkuPropertyResponse1
  /**
   * 科目
   */
  subject: SkuPropertyResponse1
  /**
   * 学段
   */
  learningPhase: SkuPropertyResponse1
  /**
   * 学科
   */
  discipline: SkuPropertyResponse1
  /**
   * 黑龙江药师-证书类型
   */
  certificatesType: SkuPropertyResponse1
  /**
   * 黑龙江药师-执业类别
   */
  practitionerCategory: SkuPropertyResponse1
  /**
   * 资质类别-主项增项
   */
  qualificationCategory: SkuPropertyResponse1
  /**
   * 培训形式 网授班,面授
   */
  trainingWay: SkuPropertyResponse1
}

/**
 * 订单商品信息返回值
<AUTHOR>
@date 2022/1/26
 */
export class CommoditySkuResponse {
  /**
   * 商品id
   */
  commoditySkuId: string
  /**
   * 商品Sku名称
   */
  saleTitle: string
  /**
   * 商品封面图路径
   */
  commodityPicturePath: string
  /**
   * 商品售价
   */
  price: number
  /**
   * 商品原始单价
   */
  originalPrice: number
  /**
   * 是否使用优惠价
   */
  enableSpecialPrice: boolean
  /**
   * 是否展示原价
   */
  showPrice: boolean
  /**
   * 商品sku 配置
   */
  skuProperty: CommoditySkuPropertyResponse1
  /**
   * 商品关联资源
   */
  resource: ResourceResponse
  /**
   * 第三方平台类型
   */
  tppTypeId: string
  /**
   * 管理系统平台
   */
  externalTrainingPlatform: string
  /**
   * 培训机构名称
   */
  trainingInstitution: string
  /**
   * 期别信息
   */
  issueInfo: IssueInfo1
}

/**
 * 优惠策略
 */
export class DiscountSchemeResponse {
  /**
   * 优惠价
   */
  specialPrice: number
  /**
   * 优惠策略
   */
  discountPolicyList: Array<DiscountPolicyModel>
  /**
   * 优惠类型
1-单项优惠 2-聚合优惠
   */
  discountType: number
  /**
   * 是否启用
   */
  hasEnabled: boolean
}

/**
 * 发票申请信息
<AUTHOR>
@date 2022/03/17
 */
export class InvoiceApplyInfoResponse {
  /**
   * 开票方式
1: 线上开票
2: 线下开票
@see InvoiceMethods
   */
  invoiceMethod: number
  /**
   * 发票类型
1：电子发票
2：纸质发票
@see InvoiceTypes
   */
  invoiceType: number
  /**
   * 发票种类
1：普通发票
2：增值税普通发票
3：增值税专用发票
@see InvoiceCategories
   */
  invoiceCategory: number
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型
1：个人
2：企业
@see InvoiceTitleTypes
   */
  titleType: number
  /**
   * 纳税人识别号（统一社会信用代码）
   */
  taxpayerNo: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 账户
   */
  account: string
  /**
   * 联系邮箱
   */
  email: string
  /**
   * 联系邮箱
   */
  contactEmail: string
  /**
   * 联系电话
   */
  contactPhone: string
  /**
   * 发票票面备注
   */
  remark: string
  /**
   * 营业执照
   */
  businessLicensePath: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath: string
  /**
   * 配送方式
0：无
1：自取
2：快递
@see OfflineShippingMethods
   */
  shippingMethod: number
  /**
   * 配送地址信息
   */
  deliveryAddress: DeliveryAddressResponse
  /**
   * 自取点信息
   */
  takePoint: TakePointResponse
  /**
   * 发票申请时间
   */
  appliedTime: string
  /**
   * 关联发票id集合
   */
  invoiceIdList: Array<string>
}

/**
 * 发票票面信息
<AUTHOR>
@date 2022/3/18
 */
export class InvoiceFaceInfoResponse {
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型
@see InvoiceTitleTypes
   */
  titleType: number
  /**
   * 购买方纳税人识别号
   */
  taxpayerNo: string
  /**
   * 购买方地址
   */
  address: string
  /**
   * 购买方电话号码
   */
  phone: string
  /**
   * 购买方开户行名称
   */
  bankName: string
  /**
   * 购买方银行账户
   */
  account: string
  /**
   * 购买方电子邮箱
   */
  email: string
  /**
   * 购买方营业执照
   */
  businessLicensePath: string
  /**
   * 购买方开户许可
   */
  accountOpeningLicensePath: string
  /**
   * 联系电子邮箱
   */
  contactEmail: string
  /**
   * 联系电话
   */
  contactPhone: string
  /**
   * 发票票面备注
   */
  remark: string
}

export class IssueInfo1 {
  /**
   * 期别id
   */
  issueId: string
  /**
   * 期别名称
   */
  issueName: string
  /**
   * 培训编号
   */
  issueNum: string
  /**
   * 培训开始时间
   */
  trainStartTime: string
  /**
   * 培训结束时间
   */
  trainEndTime: string
  /**
   * 当前期别来源类型。下单：SUB_ORDER 换班：EXCHANGE_ORDER 换期:CHANGE_ISSUE
   */
  sourceType: string
  /**
   * 期别来源类型id 1:子订单号：2：换货单号：3：旧的期别参训资格D
   */
  sourceId: string
}

/**
 * <AUTHOR> linq
@date : 2023-12-25 14:16
@description：归属信息
 */
export class OwnerInfoResponse {
  /**
   * 所属服务商ID
   */
  servicerId: string
  /**
   * 所属服务商名称
   */
  servicerName: string
}

/**
 * 退货单/批次退货单的原订单支付信息
<AUTHOR>
@date 2022/03/18
 */
export class PaymentInfoResponse {
  /**
   * 支付金额
   */
  payAmount: number
  /**
   * 支付流水号
   */
  flowNo: string
  /**
   * 收款账号
   */
  receiveAccountId: string
  /**
   * 付款单类型
<p>
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderTypes
   */
  paymentOrderType: number
}

/**
 * 定价策略
 */
export class PricingPolicyResponse {
  /**
   * 定价策略id
   */
  pricingPolicyId: string
  /**
   * 定价价格
   */
  price: number
  /**
   * 是否启用
   */
  hasEnabled: boolean
}

/**
 * 收款账号返回值
<AUTHOR>
@date 2022/01/26
 */
export class ReceiveAccountResponse {
  /**
   * 收款账号id
   */
  receiveAccountId: string
  /**
   * 收款账号类型（1:线上收款账号 2:线下收款账号）
@see ReceiveAccountTypes
   */
  receiveAccountType: number
  /**
   * 支付渠道id
   */
  payChannelId: string
  /**
   * 支付渠道名称
   */
  payChannelName: string
  /**
   * 收款账户的单位id
   */
  receiveAccountUnitId: string
}

/**
 * 退货单退款信息
<AUTHOR>
@date 2022/03/18
 */
export class RefundInfoResponse {
  /**
   * 退款单号
   */
  refundOrderNo: string
  /**
   * 退款单类型（1：线上退款 2：线下退款）
@see RefundOrderTypes
   */
  refundOrderType: number
  /**
   * 退款单状态（0：等待退款 1：退款中 2：已退款 3：退款失败）
@see com.fjhb.domain.trade.api.payment.consts.RefundOrderStatus
   */
  refundOrderStatus: number
  /**
   * 退款单状态变更时间
   */
  refundOrderStatusChangeTime: RefundOrderStatusChangeTimeResponse
  /**
   * 退款流水
   */
  refundFlow: string
  /**
   * 退款金额
   */
  refundAmount: number
  /**
   * 退款失败原因
   */
  refundFailReason: string
  /**
   * 已确认退款时间
   */
  refundConfirmedTime: string
}

export class SalePathResponse {
  /**
   * 主键id,  当前销售路径_销售全路径
   */
  id: string
  /**
   * 销售全路径
   */
  fullPath: string
  /**
   * 当前销售路径
   */
  currentPath: string
  /**
   * 当前销售路径末级code
   */
  currentPathLastCode: string
  /**
   * 当前销售路径末级类型
   */
  currentPathLastType: number
  /**
   * 是否为全路径的最后一级
@see CurrentSalePathType
   */
  isLast: boolean
}

export class UserInfoResponse {
  /**
   * 用户ID
   */
  userId: string
  /**
   * 用户名
   */
  name: string
}

/**
 * 用户相关返回值
<AUTHOR>
@date 2022/01/26
 */
export class UserResponse {
  /**
   * 用户ID
   */
  userId: string
  /**
   * 用户所在地区
   */
  userArea: RegionModel
  /**
   * 江苏工考 - 用户所在地区
   */
  managementUnitRegionCode: RegionModel
  /**
   * 江苏工考 - 工种
   */
  jobCategoryId: string
  /**
   * 江苏工考 - 工种等级
   */
  professionalLevel: number
  /**
   * 江苏工考 - 证书技术工种名称
   */
  jobCategoryName: string
}

/**
 * @Description 配送渠道
<AUTHOR>
@Date 16:23 2022/4/21
 */
export class OfflineInvoiceDeliveryChannelResponse {
  /**
   * 配送渠道编号，必填
   */
  channelId: string
  /**
   * 配送方式，0/1，快递/自取
   */
  shippingMethod: number
  /**
   * 渠道名称
   */
  channelName: string
  /**
   * 配送地点
   */
  address: string
  /**
   * 配送时间
   */
  deliveryDate: string
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 备注
   */
  remark: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商ID
   */
  servicerId: string
}

/**
 * 换货单主题模型
<AUTHOR>
@date 2022/03/23
 */
export class ExchangeOrderResponse {
  /**
   * 换货单号
   */
  exchangeOrderNo: string
  /**
   * 换货单基本信息
   */
  basicData: ExchangeOrderBasicDataResponse
  /**
   * 换货单审批信息
   */
  approvalInfo: ExchangeOrderApprovalInfoResponse
  /**
   * 原始商品信息
   */
  originalCommodity: OriginalCommodityResponse
  /**
   * 换货商品信息
   */
  exchangeCommodity: ExchangeCommodityResponse
  /**
   * 子订单发货商品分销授权信息（仅分销订单有值）
   */
  commodityAuthInfo: CommodityAuthInfoResponse
  /**
   * 换货单关联子订单信息
   */
  subOrderInfo: SubOrderInfoResponse
}

/**
 * 换货商品模型
<AUTHOR>
@date 2022/03/31
 */
export class ExchangeCommodityResponse {
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 商品信息
   */
  commoditySku: CommoditySkuResponse
}

/**
 * 退货申请信息返回值
<AUTHOR>
@date 2022/03/24
 */
export class ExchangeOrderApplyInfoResponse {
  /**
   * 申请人
   */
  applyUser: UserResponse
  /**
   * 申请原因内容id
   */
  reasonId: string
  /**
   * 申请原因内容
   */
  reasonContent: string
  /**
   * 申请描述
   */
  description: string
}

/**
 * 换货单审批信息
<AUTHOR>
@date 2022/03/18
 */
export class ExchangeOrderApprovalInfoResponse {
  /**
   * 审批状态
<p>
0: 未审批
1: 已审批
@see ExchangeApprovalReportStatus
   */
  approveStatus: number
  /**
   * 审批结果
<p>
-1：无
0：拒绝
1：同意
@see ExchangeApprovalReportResults
   */
  approveResult: number
  /**
   * 审批人
   */
  approveUser: UserModel
  /**
   * 审批意见
   */
  approveComment: string
  /**
   * 审批时间
   */
  approveTime: string
}

/**
 * 换货单基本信息
<AUTHOR>
@date 2022/03/23
 */
export class ExchangeOrderBasicDataResponse {
  /**
   * 换货单状态
<p>
0: 申请换货
1: 取消中
2: 退货中
3: 退货失败
4: 申请发货
5: 发货中
6: 发货失败
7: 换货完成
8: 已关闭
@see ExchangeOrderStatus
   */
  status: number
  /**
   * 换货单状态变更时间
   */
  statusChangeTime: ExchangeOrderStatusChangeTimeResponse
  /**
   * 换货单申请信息
   */
  applyInfo: ExchangeOrderApplyInfoResponse
  /**
   * 换货单退货失败信息
   */
  exchangeReturnFailReason: string
  /**
   * 换货单发货失败信息
   */
  exchangeDeliveryFailReason: string
  /**
   * 换货单关闭信息
   */
  exchangeOrderCloseReason: ExchangeOrderCloseReasonResponse
}

/**
 * 换货单交易关闭信息
<AUTHOR>
@date 2022/2/14
 */
export class ExchangeOrderCloseReasonResponse {
  /**
   * 换货单关闭类型
<p>
1：买家关闭
2：卖家关闭
@see ExchangeOrderCloseTypes
   */
  closeType: number
  /**
   * 换货单取消人
   */
  cancelUser: UserResponse
  /**
   * 换货单取消原因
   */
  cancelReason: string
}

/**
 * 换货单状态变更时间
<AUTHOR>
@date 2022/03/24
 */
export class ExchangeOrderStatusChangeTimeResponse {
  /**
   * 申请换货
   */
  applied: string
  /**
   * 取消中
   */
  cancelling: string
  /**
   * 退货中
   */
  returning: string
  /**
   * 退货失败
   */
  returnFailed: string
  /**
   * 申请发货
   */
  deliveryApplied: string
  /**
   * 发货中
   */
  delivering: string
  /**
   * 换货完成
   */
  exchanged: string
  /**
   * 已关闭
   */
  closed: string
}

/**
 * 主订单信息
<AUTHOR>
@date 2022/03/23
 */
export class OrderInfoResponse {
  /**
   * 订单类型
@see OrderTypes
   */
  orderType: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 批次单号
   */
  batchOrderNo: string
  /**
   * 买家
   */
  buyer: UserResponse
  /**
   * 创建人
   */
  creator: UserResponse
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 销售渠道id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
}

/**
 * 原商品模型
<AUTHOR>
@date 2022/03/31
 */
export class OriginalCommodityResponse {
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 商品信息
   */
  commoditySku: CommoditySkuResponse
}

/**
 * 子订单信息
<AUTHOR>
@date 2022/03/23
 */
export class SubOrderInfoResponse {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 子订单总额
   */
  amount: number
  /**
   * 订单信息
   */
  orderInfo: OrderInfoResponse
  /**
   * 子订单是否使用优惠
   */
  useDiscount: boolean
  /**
   * 子订单价格来源 0.自营 1.分销
   */
  discountType: number
  /**
   * 子订单优惠来源ID
   */
  discountSourceId: string
}

/**
 * 线下发票操作记录
<AUTHOR>
 */
export class OfflineInvoiceOperationResponse {
  /**
   * 操作类型
<br> 1:发票已开具 2:更新发票 3:发票已冻结 4:发票取消冻结 5:发票已作废
   */
  operationType: number
  /**
   * 操作人用户ID
   */
  operatorUserId: string
  /**
   * 操作人用户名
   */
  operatorUserName: string
  /**
   * 操作时间
   */
  operateTime: string
  /**
   * 操作附带信息
   */
  operationMessage: string
}

/**
 * 线下发票网关模型
<AUTHOR>
@date 2022/3/18
 */
export class OfflineInvoiceResponse {
  /**
   * 发票id
   */
  offlineInvoiceId: string
  /**
   * 发票基本信息
   */
  basicData: OfflineInvoiceBasicDataResponse
  /**
   * 发票关联订单信息
   */
  associationInfo: OfflineInvoiceAssociationInfoResponse
  /**
   * 发票配送信息
   */
  deliveryInfo: OfflineInvoiceDeliveryInfoResponse
  /**
   * 发票配送记录
   */
  deliveryRecordList: Array<OfflineDeliveryRecord>
}

/**
 * 发票开票状态变更时间记录
<AUTHOR>
@date 2022/04/06
 */
export class BillStatusChangeTimeResponse {
  /**
   * 未开票
   */
  unBill: string
  /**
   * 已开票
   */
  success: string
}

/**
 * 配送地址信息
<AUTHOR>
@date 2022/04/06
 */
export class DeliveryAddressResponse {
  /**
   * 收件人
   */
  consignee: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 所在物理地区
   */
  region: string
  /**
   * 详细地址
   */
  address: string
}

/**
 * 发票配送状态变更时间记录
<AUTHOR>
@date 2022/04/06
 */
export class DeliveryStatusChangeTimeResponse {
  /**
   * 未就绪
   */
  unReady: string
  /**
   * 已就绪
   */
  ready: string
  /**
   * 已配送
   */
  shipped: string
  /**
   * 已自取
   */
  taken: string
}

/**
 * 快递信息
<AUTHOR>
@date 2022/04/06
 */
export class ExpressResponse {
  /**
   * 快递公司名称
   */
  expressCompanyName: string
  /**
   * 快递单号
   */
  expressNo: string
}

/**
 * 发票状态变更时间记录
<AUTHOR>
@date 2022/04/06
 */
export class InvoiceStatusChangeTimeResponse {
  /**
   * 正常
   */
  normal: string
  /**
   * 作废
   */
  invalid: string
}

/**
 * 发票配送记录
<AUTHOR>
@date 2022/05/17
 */
export class OfflineDeliveryRecord {
  /**
   * 发票号
   */
  invoiceNoList: Array<string>
  /**
   * 配送方式
<p>
0:无 1:自取 2：快递
@see OfflineShippingMethods
   */
  shippingMethod: number
  /**
   * 自取结果
   */
  takeResult: TakeResultResponse
  /**
   * 快递信息
   */
  express: ExpressResponse
  /**
   * 配送时间
   */
  deliveryTime: string
}

/**
 * 发票关联订单信息
<AUTHOR>
@date 2022/3/18
 */
export class OfflineInvoiceAssociationInfoResponse {
  /**
   * 关联订单类型 | 批次单、普通订单
@see AssociationTypes
   */
  associationType: number
  /**
   * 订单号 | 批次单号
   */
  associationId: string
  /**
   * 付款金额
   */
  payAmount: number
  /**
   * 买家信息
   */
  buyer: UserModel
  /**
   * 收款账号ID
   */
  receiveAccountId: string
  /**
   * 订单退货状态
0:未退货 1：退货中 2：退货成功
   */
  orderReturnStatus: number
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 专题id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
  /**
   * 订单发货商品分销信息（仅分销订单的发票有值）
   */
  commodityAuthInfoSet: Array<CommodityAuthInfoResponse>
  /**
   * 第三方平台类型
   */
  tppTypeId: string
}

/**
 * 发票基本信息
<AUTHOR>
@date 2022/3/18
 */
export class OfflineInvoiceBasicDataResponse {
  /**
   * 发票类型
@see InvoiceTypes
   */
  invoiceType: number
  /**
   * 发票种类
@see InvoiceCategories
   */
  invoiceCategory: number
  /**
   * 发票状态
@see InvoiceStatus
   */
  invoiceStatus: number
  /**
   * 发票状态变更时间记录
   */
  invoiceStatusChangeTime: InvoiceStatusChangeTimeResponse
  /**
   * 发票开票状态
@see InvoiceBillStatus
   */
  billStatus: number
  /**
   * 发票开票状态变更时间记录
   */
  billStatusChangeTime: BillStatusChangeTimeResponse
  /**
   * 发票是否冻结
   */
  freeze: boolean
  /**
   * 冻结来源类型
@see InvoiceFrozenSourceTypes
   */
  freezeSourceType: number
  /**
   * 冻结来源编号
   */
  freezeSourceId: string
  /**
   * 发票票面信息
   */
  invoiceFaceInfo: InvoiceFaceInfoResponse
  /**
   * 发票号集合
   */
  invoiceNoList: Array<string>
  /**
   * 总金额（开票金额）
   */
  amount: number
}

/**
 * 线下发票配送信息
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceDeliveryInfoResponse {
  /**
   * 配送状态
<p>
0:未就绪 1：已就绪 2：已自取 3：已配送
@see OfflineDeliveryStatus
   */
  deliveryStatus: number
  /**
   * 配送状态变更时间记录
   */
  deliveryStatusChangeTime: DeliveryStatusChangeTimeResponse
  /**
   * 配送方式
<p>
0:无 1:自取 2：快递
@see OfflineShippingMethods
   */
  shippingMethod: number
  /**
   * 配送地址信息
   */
  deliveryAddress: DeliveryAddressResponse
  /**
   * 自取点信息
   */
  takePoint: TakePointResponse
  /**
   * 快递信息
   */
  express: ExpressResponse
  /**
   * 自取信息
   */
  takeResult: TakeResultResponse
}

/**
 * 自取点信息
<AUTHOR>
@date 2022/04/06
 */
export class TakePointResponse {
  /**
   * 领取地点
   */
  pickupLocation: string
  /**
   * 领取时间
   */
  pickupTime: string
  /**
   * 备注
   */
  remark: string
}

/**
 * 取件信息
<AUTHOR>
@date 2022/04/06
 */
export class TakeResultResponse {
  /**
   * 领取人
   */
  takePerson: string
  /**
   * 手机号
   */
  phone: string
}

/**
 * 线上发票操作记录
<AUTHOR>
 */
export class OnlineInvoiceOperationResponse {
  /**
   * 操作类型
<br> 1:发票已开具 2:更新发票 3:发票已冲红 4:发票开具失败 5:发票冲红失败
   */
  operationType: number
  /**
   * 操作人用户ID
   */
  operatorUserId: string
  /**
   * 操作人用户名
   */
  operatorUserName: string
  /**
   * 操作时间
   */
  operateTime: string
  /**
   * 操作附带信息
   */
  operationMessage: string
}

/**
 * 发票网关模型
<AUTHOR>
@date 2022/3/18
 */
export class OnlineInvoiceResponse {
  /**
   * 发票id
   */
  invoiceId: string
  /**
   * 发票基本信息
   */
  basicData: OnlineInvoiceBasicDataResponse
  /**
   * 发票关联订单信息
   */
  associationInfoList: Array<OnlineInvoiceAssociationInfo>
  /**
   * 蓝票票据
   */
  blueInvoiceItem: OnlineInvoiceItemResponse
  /**
   * 红票票据
   */
  redInvoiceItem: OnlineInvoiceItemResponse
}

/**
 * 发票统计信息返回值
<AUTHOR>
@date 2022/01/26
 */
export class OnlineInvoiceStatisticResponse {
  /**
   * 发票开票总金额
   */
  totalAmount: number
  /**
   * 发票总税额
   */
  totalTax: number
}

/**
 * 发票开具状态变更时间
<AUTHOR>
@date 2022/03/22
 */
export class BillStatusChangeTimeResponse1 {
  /**
   * 未开具
   */
  unBill: string
  /**
   * 开票中
   */
  billing: string
  /**
   * 开票成功
   */
  success: string
  /**
   * 开票失败
   */
  failure: string
}

/**
 * 发票关联订单信息
<AUTHOR>
@date 2022/3/18
 */
export class OnlineInvoiceAssociationInfo {
  /**
   * 关联订单类型
0:订单号
1:批次单号
@see AssociationTypes
   */
  associationType: number
  /**
   * 订单号 | 批次单号
   */
  associationId: string
  /**
   * 订单付款金额
   */
  payAmount: number
  /**
   * 买家信息
   */
  buyer: UserEntity
  /**
   * 收款账号
   */
  receiveAccountId: string
  /**
   * 订单退货状态
0:未退货 1：退货中 2：退货成功
   */
  orderReturnStatus: number
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 订单发货商品分销信息（仅分销订单的发票有值）
   */
  commodityAuthInfoSet: Array<CommodityAuthInfoResponse>
  /**
   * 专题id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
  /**
   * 第三方平台类型
   */
  tppTypeId: string
}

/**
 * 发票基本信息
<AUTHOR>
@date 2022/3/18
 */
export class OnlineInvoiceBasicDataResponse {
  /**
   * 是否为拆票后的发票
   */
  spilt: boolean
  /**
   * 发票类型
1:电子发票
2:纸质发票
@see InvoiceTypes
   */
  invoiceType: number
  /**
   * 发票种类
1:普通发票
2:增值税普通发票
3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategory: number
  /**
   * 发票状态
1:正常
2:作废
@see InvoiceStatus
   */
  invoiceStatus: number
  /**
   * 发票状态变更时间记录
   */
  invoiceStatusChangeTime: InvoiceStatusChangeTimeResponse
  /**
   * 发票是否冻结
   */
  freeze: boolean
  /**
   * 冻结来源类型
@see InvoiceFrozenSourceTypes
   */
  freezeSourceType: number
  /**
   * 冻结来源编号
   */
  freezeSourceId: string
  /**
   * 蓝票票据开具状态
0:未开具
1:开票中
2:开票成功
3:开票失败
@see InvoiceBillStatus
   */
  blueInvoiceItemBillStatus: number
  /**
   * 红票票据开具状态
0:未开具
1:开票中
2:开票成功
3:开票失败
@see InvoiceBillStatus
   */
  redInvoiceItemBillStatus: number
  /**
   * 发票票面信息
   */
  invoiceFaceInfo: InvoiceFaceInfoResponse
  /**
   * 纳税人编号
   */
  taxpayerId: string
  /**
   * 开票总金额
<br> 计算方式： 累加开票项目中的总价
   */
  amount: number
}

/**
 * 发票票据
<AUTHOR>
@date 2022/03/18
 */
export class OnlineInvoiceItemResponse {
  /**
   * 票据id
   */
  invoiceItemId: string
  /**
   * 开票流水号，由系统生成
   */
  flowNo: string
  /**
   * 票据开具状态
@see InvoiceBillStatus
   */
  billStatus: number
  /**
   * 票据开具状态变更时间
   */
  billStatusChangeTime: BillStatusChangeTimeResponse1
  /**
   * 价税合计（开票金额）
价税合计&#x3D;合计金额(不含税)+合计税额
   */
  totalAmount: number
  /**
   * 不含税总金额
   */
  totalExcludingTaxAmount: number
  /**
   * 总税额
   */
  totalTax: number
  /**
   * 发票代码
   */
  billCode: string
  /**
   * 发票号码
   */
  billNo: string
  /**
   * 校验码
   */
  checkCode: string
  /**
   * 票据存储文件路径
   */
  filePath: string
  /**
   * ofd票据存储文件路径
   */
  ofdFilePath: string
  /**
   * xml票据存储文件路径
   */
  xmlFilePath: string
  /**
   * 开票失败信息
   */
  failMessage: string
}

/**
 * 买家有效商品返回值
<AUTHOR>
@date 2022/03/24
 */
export class BuyerValidCommodityResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 订单信息
   */
  order: OrderResponse
  /**
   * 商品信息
   */
  commoditySku: CommoditySkuResponse
  /**
   * 商品所属订单购买渠道
<br> 1:用户自主购买 2:集体缴费 3：管理员导入 4：集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelType: number
  /**
   * 商品是否来自换货单
   */
  formExchangeOrder: boolean
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 销售渠道id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
  /**
   * 分销商id
   */
  distributorId: string
}

export class IssueLogResponse {
  /**
   * 子订单号
   */
  subOrder: string
  /**
   * 换出商品
   */
  originalCommodity: CommoditySkuResponse
  /**
   * 换入商品
   */
  exchangeCommodity: CommoditySkuResponse
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 操作人信息
   */
  creator: UserInfoResponse
  /**
   * 换期类型 ：  0：换班换期   1： 班内换期
   */
  exchangeType: number
}

/**
 * 订单返回值
<AUTHOR>
@date 2022/1/25
 */
export class OrderResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 基本信息
   */
  basicData: OrderBasicDataResponse
  /**
   * 支付信息
   */
  payInfo: PayInfoResponse
  /**
   * 子订单信息
   */
  subOrderItems: Array<SubOrderResponse>
  /**
   * 买家信息
   */
  buyer: UserResponse
  /**
   * 订单创建人
   */
  creator: UserResponse
  /**
   * 订单是否已经申请发票
   */
  isInvoiceApplied: boolean
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 销售渠道id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
  /**
   * 发票申请信息
   */
  invoiceApplyInfo: InvoiceApplyInfoResponse
  /**
   * 购买来源，门户-GATEWAY、专题-TRAINING
   */
  purchaseSourceType: string
  /**
   * 销售全路径
   */
  salePathList: Array<SalePathResponse>
  /**
   * 推广门户标识
   */
  portalIdentifier: string
  /**
   * 培训计划ID，例如补贴性培训平台和补贴管理系统对接
   */
  policyTrainingSchemeIds: string
  /**
   * 申报单位统一信用代码
   */
  declarationUnitCode: string
  /**
   * 结算状态
@see com.fjhb.ms.trade.query.common.constants.SettlementStatusConstants
   */
  settlementStatus: number
  /**
   * 结算金额
   */
  settlementAmount: number
  /**
   * 结算时间
   */
  settlementDate: string
  /**
   * 兑付状态
@see com.fjhb.ms.trade.query.common.constants.RedeemedStatusConstants
   */
  redeemedStatus: number
  /**
   * 兑付金额
   */
  redeemedAmount: number
  /**
   * 兑付时间
   */
  redeemedDate: string
  /**
   * 参训资格ID
   */
  registrationId: string
}

/**
 * 订单统计信息返回值
<AUTHOR>
@date 2022/01/26
 */
export class OrderStatisticResponse {
  /**
   * 订单总数量
   */
  totalOrderCount: number
  /**
   * 订单总金额
   */
  totalOrderAmount: number
  /**
   * 订单培训方案商品总学时数
   */
  totalPeriod: number
}

/**
 * 订单基本信息返回值
<AUTHOR>
@date 2022/01/26
 */
export class OrderBasicDataResponse {
  /**
   * 订单类型
<br> 1：常规订单 2：批次关联订单
@see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType: number
  /**
   * 关联批次单号
   */
  batchOrderNo: string
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3：管理员导入 4：集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelType: number
  /**
   * 终端
<br> Web:Web端 H5:H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTerminalCodes
   */
  terminalCode: string
  /**
   * 订单总金额
   */
  amount: number
  /**
   * 订单状态
0:未确认，批次单订单初始状态
1:正常
2:交易完成
3:交易关闭
<p>
ui订单状态：数据微服务订单状态
等待付款：订单状态正常&amp;支付状态未支付
支付中：订单状态正常&amp;支付状态支付中
开通中：订单状态正常&amp;支付状态已支付
交易成功：订单状态交易成功
交易关闭：订单状态交易关闭
@see OrderStatus
   */
  orderStatus: number
  /**
   * 订单支付状态
0:未支付
1:支付中
2:已支付
@see OrderPaymentStatus
   */
  orderPaymentStatus: number
  /**
   * 订单发货状态
0：未发货
1：发货中
2：已发货
@see OrderDeliveryStatus
   */
  orderDeliveryStatus: number
  /**
   * 订单状态变更时间
   */
  orderStatusChangeTime: OrderStatusChangeTimeResponse
  /**
   * 订单支付状态变更时间
   */
  orderPaymentStatusChangeTime: OrderPaymentStatusChangeTimeResponse
  /**
   * 自动关闭时间
   */
  autoCloseTime: string
  /**
   * 交易关闭原因
   */
  closeReason: OrderCloseReasonResponse
  /**
   * 单位id
   */
  unitId: string
}

/**
 * 订单交易关闭返回值
<AUTHOR>
@date 2022/01/26
 */
export class OrderCloseReasonResponse {
  /**
   * 交易关闭类型
<br> 1:买家取消 2:卖家取消 3：超时取消 4：批次关联取消
@see com.fjhb.domain.trade.api.order.consts.OrderClosedTypes
   */
  closedType: number
  /**
   * 交易关闭原因ID
   */
  reasonId: string
  /**
   * 交易关闭原因说明
   */
  reason: string
}

/**
 * 订单支付状态变更时间
<AUTHOR>
@date 2022/01/26
 */
export class OrderPaymentStatusChangeTimeResponse {
  /**
   * 支付中
   */
  paying: string
  /**
   * 已支付
   */
  paid: string
}

/**
 * 订单状态变更时间
<AUTHOR>
@date 2022/01/26
 */
export class OrderStatusChangeTimeResponse {
  /**
   * 订单处于正常状态的时间（订单创建时间）
   */
  normal: string
  /**
   * 订单交易完成时间
   */
  completed: string
  /**
   * 订单交易关闭时间
   */
  closed: string
}

/**
 * 订单支付信息
<AUTHOR>
@date 2022/1/26
 */
export class PayInfoResponse {
  /**
   * 付款单号
   */
  paymentOrderNo: string
  /**
   * 付款单类型
<p>
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderTypes
   */
  paymentOrderType: number
  /**
   * 付款单状态
0:待付款
1:付款中
2:已支付
3:已取消
@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderStatus
   */
  paymentOrderStatus: number
  /**
   * 支付流水号
   */
  flowNo: string
  /**
   * 支付金额
   */
  payAmount: number
  /**
   * 支付金额类型
<br> 0:人民币现金支付（线下人民币现金、微信支付、支付宝） 1:华博电子钱包虚拟币 2:消费券(培训券)
@see CurrencyType
   */
  currencyType: number
  /**
   * 收款账号
   */
  receiveAccount: ReceiveAccountResponse
  /**
   * 汇款凭证url
   */
  paymentVoucherList: Array<PaymentVoucherResponse>
  /**
   * 汇款凭证确认人
   */
  paymentVoucherConfirmUser: UserModel
}

/**
 * 付款凭证
<AUTHOR>
@date 2022/03/07
 */
export class PaymentVoucherResponse {
  /**
   * 凭证ID
   */
  id: string
  /**
   * 凭证文件路径
   */
  path: string
  /**
   * 创建人
   */
  createUserId: UserModel
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 订单商品sku属性
<AUTHOR>
@date 2022/1/25
 */
export class SkuPropertyResponse1 {
  /**
   * sku属性值id
   */
  skuPropertyValueId: string
  /**
   * sku属性值名
   */
  skuPropertyValueName: string
  /**
   * sku属性值展示名
   */
  skuPropertyValueShowName: string
}

/**
 * 子订单发货状态变更时间
<AUTHOR>
@date 2022/01/26
 */
export class SubOrderDeliveryStatusChangeTimeResponse {
  /**
   * 等待发货
   */
  waiting: string
  /**
   * 发货中
   */
  delivering: string
  /**
   * 发货成功
   */
  successDelivered: string
  /**
   * 发货失败
   */
  failDelivered: string
}

/**
 * 子订单返回值
<AUTHOR>
@date 2022/1/26
 */
export class SubOrderResponse {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 子订单发货状态
0:等待发货 100:发货中 200:发货成功 400:发货失败
@see DeliveryOrderSkuStatus
   */
  deliveryStatus: number
  /**
   * 子订单退货情况
0: 未退货,
1: 已部分退货,
2: 已全部退货
   */
  returnSchedule: number
  /**
   * 子订单退款情况
0: 未退款,
1: 已部分退款,
2: 已全部退款
   */
  refundSchedule: number
  /**
   * 子订单换货状态
<p>
0:未换货
1:换货申请中
2:换货中
3:已换货
@see com.fjhb.domain.trade.api.order.consts.SubOrderExchangeStatus
   */
  exchangeStatus: number
  /**
   * 子订单最新退货单号
   */
  returnOrderNo: string
  /**
   * 子订单退货状态
0：未退货
1：退货申请中
2：退货中
3：退货成功
4：退款中
5：退款成功
@see SubOrderReturnStatus
   */
  returnStatus: number
  /**
   * 子订单发货状态变更时间
0: 等待发货
100: 发货中
200: 发货成功
400: 发货失败
<br> key值 {@link DeliveryOrderSkuStatus}
   */
  deliveryStatusChangeTime: SubOrderDeliveryStatusChangeTimeResponse
  /**
   * 发货失败信息
   */
  deliverFailMessage: string
  /**
   * 子订单商品数量
   */
  quantity: number
  /**
   * 子订单剩余商品数量
   */
  leftQuantity: number
  /**
   * 商品单价
   */
  price: number
  /**
   * 子订单总金额
   */
  amount: number
  /**
   * 子订单剩余金额
   */
  leftAmount: number
  /**
   * 子订单优惠价（优惠的减免额度）
   */
  specialPrice: number
  /**
   * 商品分销授权信息（仅分销订单有值）
   */
  commodityAuthInfo: CommodityAuthInfoResponse
  /**
   * 优惠类型
@see DiscountType
   */
  discountType: number
  /**
   * 优惠来源ID | 优惠申请单ID
   */
  discountSourceId: string
  /**
   * 是否使用优惠
   */
  useDiscount: boolean
  /**
   * 发货商品信息
   */
  deliveryCommoditySku: CommoditySkuResponse
  /**
   * 当前商品信息
   */
  currentCommoditySku: CommoditySkuResponse
  /**
   * 当前商品来源类型
@see CommoditySkuSourceType
子订单发货: 0
换货单换货：1
   */
  currentCommoditySourceType: number
  /**
   * 当前商品来源ID
<p>
如果来源类型是子订单，那么来源ID是子订单号
如果来源类型是换货单，那么来源ID是换货单
   */
  currentCommoditySourceId: string
  /**
   * 最终成交单价
   */
  finalPrice: number
  /**
   * 优惠方案
   */
  discountScheme: DiscountSchemeResponse
  /**
   * 定价方案
   */
  pricingPolicy: PricingPolicyResponse
  /**
   * 住宿信息
   */
  accommodation: Accommodation
  /**
   * 是否换期
   */
  isExchangeIssue: boolean
}

/**
 * 退货单网关模型
 */
export class ReturnOrderResponse {
  /**
   * 退货单号
   */
  returnOrderNo: string
  /**
   * 退货单基本信息
   */
  basicData: ReturnOrderBasicDataResponse
  /**
   * 退货单是否需要审批
   */
  needApprove: boolean
  /**
   * 退货单审批信息
   */
  approvalInfo: ReturnApprovalInfoResponse
  /**
   * 退款确认人
   */
  confirmUser: UserResponse
  /**
   * 退货单关联退款单信息
   */
  refundInfo: RefundInfoResponse
  /**
   * 退货商品信息
   */
  returnCommodity: ReturnCommodityResponse
  /**
   * 退款商品信息
   */
  refundCommodity: RefundCommodityResponse
  /**
   * 退货子订单信息
   */
  subOrderInfo: SubOrderInfoResponse1
  /**
   * 来源批次退货单信息
   */
  batchReturnOrder: BatchReturnOrderResponse
  /**
   * 退货商品分销信息（仅分销订单的退货单有值）
   */
  commodityAuthInfo: CommodityAuthInfoResponse
  /**
   * 归属信息
   */
  ownerInfo: OwnerInfoResponse
  /**
   * 是否需要人工确认退款
   */
  needConfirmRefund: boolean
}

/**
 * 退货单统计信息返回值
<AUTHOR>
@date 2022/01/26
 */
export class ReturnOrderStatisticResponse {
  /**
   * 退货单总数
   */
  totalReturnOrderCount: number
  /**
   * 退货单退款总额
   */
  totalRefundAmount: number
}

/**
 * 功能描述：退货原因信息
@Author： wtl
@Date： 2022/4/1 10:45
 */
export class ReturnReasonInfoResponse {
  /**
   * 退货原因id
   */
  reasonId: string
  /**
   * 退货原因内容
   */
  reasonContent: string
}

/**
 * 退货单关联订单信息
<AUTHOR>
@date 2022/3/23
 */
export class OrderInfoResponse1 {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 订单类型（1：常规订单 2：批次关联订单）
@see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType: number
  /**
   * 关联批次单号
   */
  batchOrderNo: string
  /**
   * 购买渠道（1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道）
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelType: number
  /**
   * 终端（Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统）
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTerminalCodes
   */
  terminalCode: string
  /**
   * 订单支付信息
   */
  orderPaymentInfo: PaymentInfoResponse
  /**
   * 买家信息
   */
  buyer: UserResponse
  /**
   * 创建人
   */
  creator: UserResponse
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 专题id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
  /**
   * 培训计划ID，例如补贴性培训平台和补贴管理系统对接
   */
  policyTrainingSchemeIds: string
  /**
   * 申报单位统一信用代码
   */
  declarationUnitCode: string
}

/**
 * 退款商品信息
<AUTHOR>
@date 2022/03/28
 */
export class RefundCommodityResponse {
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 商品信息
   */
  commoditySku: CommoditySkuResponse
}

/**
 * 退款单状态变更时间
<AUTHOR>
@date 2022/03/23
 */
export class RefundOrderStatusChangeTimeResponse {
  /**
   * 等待退款
   */
  waiting: string
  /**
   * 退款中
   */
  refunding: string
  /**
   * 已退款
   */
  refunded: string
  /**
   * 退款失败
   */
  failed: string
}

/**
 * 退货审批信息
<AUTHOR>
@date 2022/03/18
 */
export class ReturnApprovalInfoResponse {
  /**
   * 审批状态（0：未审批 1：已审批）
@see ReturnApprovalReportStatus
   */
  approveStatus: number
  /**
   * 审批结果（-1：无 0：拒绝 1：同意）
@see ReturnApprovalReportResults
   */
  approveResult: number
  /**
   * 审批人
   */
  approveUser: UserResponse
  /**
   * 审批意见
   */
  approveComment: string
  /**
   * 审批时间
   */
  approveTime: string
  /**
   * 审批取消时间
   */
  cancelApproveTime: string
}

/**
 * 退货单关闭信息
<AUTHOR>
@date 2022年3月29日 15:48:33
 */
export class ReturnCloseReasonResponse {
  /**
   * 退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
@see ReturnOrderCloseTypes
   */
  closeType: number
  /**
   * 退货单取消人
   */
  cancelUser: UserModel
  /**
   * 取消原因
   */
  cancelReason: string
}

/**
 * 退货商品信息
<AUTHOR>
@date 2022/03/28
 */
export class ReturnCommodityResponse {
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 商品信息
   */
  commoditySku: CommoditySkuResponse
}

/**
 * 退货申请信息返回值
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderApplyInfoResponse {
  /**
   * 申请人
   */
  applyUser: UserResponse
  /**
   * 申请原因内容id
   */
  reasonId: string
  /**
   * 申请原因内容
   */
  reasonContent: string
  /**
   * 申请描述
   */
  description: string
}

/**
 * 退货单基本信息
<AUTHOR>
@date 2022/3/18
 */
export class ReturnOrderBasicDataResponse {
  /**
   * 退货单类型
1-仅退货
2-仅退款
3-退货并退款
4-部分退货
5-部分退款
6-部分退货并部分退款
7-部分退货并全额退款
8-全部退货并部分退款
   */
  returnOrderType: number
  /**
   * 退款总额
   */
  refundAmount: number
  /**
   * 退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
@see ReturnOrderStatus
   */
  returnOrderStatus: number
  /**
   * 退货单状态变更时间
   */
  returnOrderStatusChangeTime: ReturnOrderStatusChangeTimeResponse
  /**
   * 退货单申请信息
   */
  applyInfo: ReturnOrderApplyInfoResponse
  /**
   * 退货单退货失败信息
   */
  returnFailReason: string
  /**
   * 退货单关闭信息
   */
  returnCloseReason: ReturnCloseReasonResponse
  /**
   * 退货单申请来源类型
SUB_ORDER
BATCH_RETURN_ORDER
@see ReturnOrderApplySourceTypes
   */
  applySourceType: string
  /**
   * 退货单申请来源id
当来源类型为子订单时,该申请来源id为子订单号,为批次退货单申请来源的,该申请来源id为批次退货单号
   */
  applySourceId: string
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 专题id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
}

/**
 * 退货单状态变更时间
<AUTHOR>
@date 2022/03/23
 */
export class ReturnOrderStatusChangeTimeResponse {
  /**
   * 申请退货时间
   */
  applied: string
  /**
   * 申请退货取消处理中时间
   */
  cancelApplying: string
  /**
   * 退货处理中时间
   */
  returning: string
  /**
   * 退货失败时间
   */
  returnFailed: string
  /**
   * 正在申请退款时间
   */
  refundApplying: string
  /**
   * 已申请退款时间
   */
  refundApplied: string
  /**
   * 退款处理中时间
   */
  refunding: string
  /**
   * 退款失败
   */
  refundFailed: string
  /**
   * 退货完成时间
   */
  returned: string
  /**
   * 退款完成时间
   */
  refunded: string
  /**
   * 退货退款完成时间
   */
  returnedAndRefunded: string
  /**
   * 退货单完成时间
   */
  returnCompleted: string
  /**
   * 已关闭时间
   */
  closed: string
}

/**
 * 退货子订单信息
<AUTHOR>
@date 2022/3/18
 */
export class SubOrderInfoResponse1 {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 子订单有换货
   */
  exchanged: boolean
  /**
   * 主订单信息
   */
  orderInfo: OrderInfoResponse1
  /**
   * 子订单商品数量
   */
  quantity: number
  /**
   * 子订单优惠来源ID
   */
  discountSourceId: string
  /**
   * 子订单优惠类型
@see DiscountType
   */
  discountType: number
  /**
   * 子订单是否使用优惠
   */
  useDiscount: boolean
  /**
   * 销售渠道
   */
  saleChannel: number
  /**
   * 优惠方案
   */
  discountScheme: DiscountSchemeResponse
  /**
   * 定价方案
   */
  pricingPolicy: PricingPolicyResponse
  salePathList: Array<SalePathResponse>
  /**
   * 是否换期
   */
  isExchangeIssue: boolean
  /**
   * 子订单总金额
   */
  amount: number
  /**
   * 子订单最终成交单价
   */
  finalPrice: number
}

/**
 * 购买渠道信息
 */
export class PurchaseChannelResponse {
  /**
   * 购买渠道id
   */
  id: string
  /**
   * 平台id
   */
  platformId: string
  /**
   * 平台版本id
   */
  platformVersionId: string
  /**
   * 项目id
   */
  projectId: string
  /**
   * 子项目id
   */
  subProjectId: string
  /**
   * 单位id
   */
  unitId: string
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 购买渠道类型 1-自主购买 2-集体缴费 3-管理员导入 4-集体报名个人缴费
   */
  channelType: number
  /**
   * 购买渠道名称
   */
  name: string
  /**
   * 购买渠道状态
   */
  status: number
  /**
   * 终端列表
   */
  terminalList: Array<TerminalResponse>
  /**
   * 发票配置
   */
  invoiceConfig: InvoiceConfigResponse
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 收款账户配置
 */
export class ReceiveAccountConfigResponse {
  /**
   * 收款账号id
   */
  id: string
  /**
   * 收款账户类型
<p>
1：线上收款帐号
2：线下收款帐号
   */
  accountType: number
  /**
   * 支付渠道id
<p>
TRAINING_VOUCHER：培训券，对接众智汇云培训券
TRAINING_VOUCHER_CHECK_PAY：培训券验证支付，对接众智汇云培训券
ALIPAY：支付宝
WXPAY：微信支付
HAOBAO_PAY：号百支付
QM：快钱支付
TL：通联支付
ZERO_PAY：0元订单支付
WXPAY_V3：微信支付
   */
  paymentChannelId: string
  /**
   * 账号
   */
  accountNo: string
  /**
   * 收款账号名称
   */
  name: string
  /**
   * 所属商户名称
   */
  merchantName: string
  /**
   * 所属商户电话
   */
  merchantPhone: string
  /**
   * 收款帐号对应支付渠道的密钥数据
   */
  encryptionKeyData: EncryptionKeyDataResponse
  /**
   * 收款帐号状态
0:停用
1:可用
   */
  status: number
  /**
   * 创建人ID
   */
  createUserId: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 退款方式，1-线上退款，2-线下退款
   */
  returnType: number
  /**
   * 纳税人id
   */
  taxPayerId: string
  /**
   * 主体类别
单位：4；服务商：5
   */
  subjectType: number
  /**
   * 主体id
   */
  subjectId: string
  /**
   * 主体名称
   */
  subjectName: string
  /**
   * 付款扫码引导语
   */
  qrScanPrompt: string
}

/**
 * 纳税人信息
 */
export class TaxpayerResponse {
  id: string
  /**
   * 纳税人名称
   */
  name: string
  /**
   * 纳税人识别号
   */
  taxpayerNo: string
  /**
   * 发票地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 银行开户行
   */
  bankName: string
  /**
   * 银行账号
   */
  bankAccount: string
  /**
   * 开票人
   */
  issuer: string
  /**
   * 收款人
   */
  payee: string
  /**
   * 复核人
   */
  reviewer: string
}

/**
 * 支付宝 密钥数据
<AUTHOR> create 2021/3/9 15:24
 */
export class AlipayEncryptionKeyDataResponse implements EncryptionKeyDataResponse {
  /**
   * 支付宝的Appid
   */
  appId: string
  /**
   * 支付宝合作者身份ID(2088*)
   */
  partner: string
  /**
   * 支付宝的应用RSA私钥
   */
  privateKey: string
  /**
   * 支付宝的RSA公钥
   */
  publicKey: string
  /**
   * 秘钥类型
Alipay
WechatPay
OfflinePay
CIBPay
CCB_PAY
SWIFT_PASS_PAY
@see EncryptionKeyType
   */
  encryptionKeyType: string
}

/**
 * <AUTHOR> linq
@date : 2024-02-20 16:50
@description：建设银行支付 密钥数据
 */
export class CCBPayEncryptionKeyDataResponse implements EncryptionKeyDataResponse {
  /**
   * 商户柜台代码
   */
  POS_ID: string
  /**
   * 分行代码
   */
  BRANCH_ID: string
  /**
   * 建行网银支付接口的公钥
   */
  PUBLIC_KEY: string
  /**
   * 建行的操作员账号不能为空
   */
  OPERATOR: string
  /**
   * 建行操作员的登陆密码
   */
  PASSWORD: string
  /**
   * 是否使用防钓鱼,如果1表示使用防钓鱼接口,其他则不使用
   */
  PHISHING: string
  /**
   * 小程序/公众号的 APPID 当前调起支付的小程序/公众号 APPID
   */
  SUB_APP_ID: string
  /**
   * 文件证书路径
   */
  CERT_FILE_PATH: string
  /**
   * 文件证书密码
   */
  CERT_PASS_WORD: string
  /**
   * 秘钥类型
Alipay
WechatPay
OfflinePay
CIBPay
CCB_PAY
SWIFT_PASS_PAY
@see EncryptionKeyType
   */
  encryptionKeyType: string
}

/**
 * <AUTHOR> linq
@date : 2023-08-07 15:06
@description：兴业银行支付 密钥数据
 */
export class CIBPayEncryptionKeyDataResponse implements EncryptionKeyDataResponse {
  /**
   * 终端编号(兴业银行提供)
   */
  terminalId: string
  /**
   * 应用ID，为KY字母开头的字符串，区分大小写
   */
  appId: string
  /**
   * 请求报文签名私钥(SM2签名私钥)
   */
  requestPrivateKey: string
  /**
   * 响应报文验签公钥
   */
  responsePublicKey: string
  /**
   * 请求字段加密密钥
   */
  requestParamEncryptKey: string
  /**
   * wx公众账号或小程序ID
   */
  subAppid: string
  /**
   * 秘钥类型
Alipay
WechatPay
OfflinePay
CIBPay
CCB_PAY
SWIFT_PASS_PAY
@see EncryptionKeyType
   */
  encryptionKeyType: string
}

export interface EncryptionKeyDataResponse {
  /**
   * 秘钥类型
Alipay
WechatPay
OfflinePay
CIBPay
CCB_PAY
SWIFT_PASS_PAY
@see EncryptionKeyType
   */
  encryptionKeyType: string
}

/**
 * 发票自动开票配置
 */
export class InvoiceAutoBillPolicyResponse {
  /**
   * 配置ID
   */
  id: string
  /**
   * 平台id
   */
  platformId: string
  /**
   * 平台版本id
   */
  platformVersionId: string
  /**
   * 项目id
   */
  projectId: string
  /**
   * 子项目id
   */
  subProjectId: string
  /**
   * 单位id
   */
  unitId: string
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 是否自动开票
   */
  autoBill: boolean
  /**
   * 间隔小时|自生成发票日期算起多少小时后自动开票，默认7*24
   */
  intervalHours: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
}

/**
 * 允许开具发票种类配置
 */
export class InvoiceCategoryConfigResponse {
  /**
   * 允许开具发票种类 1-普通发票 2-增值税普通发票 3-增值税专用发票
   */
  invoiceCategory: number
  /**
   * 开票方式 1-线上开票 2-线下
   */
  invoiceMethod: number
  /**
   * 发票抬头  1-个人 2-企业
   */
  invoiceTitleList: Array<number>
}

/**
 * 发票配置
 */
export class InvoiceConfigResponse {
  /**
   * 发票配置编号
   */
  configId: string
  /**
   * 开放发票类型 0-不开放 1-自主选择 2-强制提供
   */
  openInvoiceType: number
  /**
   * 是否允许索取发票
   */
  allowAskFor: boolean
  /**
   * 允许开具发票种类配置
   */
  allowInvoiceCategoryList: Array<InvoiceCategoryConfigResponse>
  /**
   * 索取发票年度类型 1-当年度 2-下一个年度
   */
  askForInvoiceYearType: number
  /**
   * 索取发票截止日期
   */
  askForInvoiceDeadline: string
  /**
   * 开票方式 1-线上开票 2-线下
   */
  invoiceMethod: number
  /**
   * 发票抬头  1-个人 2-企业
   */
  invoiceTitleList: Array<number>
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
}

/**
 * <AUTHOR> linq
@date : 2025-03-25 10:08
@description :新大陆支付 密钥数据
 */
export class NewLandEncryptionKeyDataResponse implements EncryptionKeyDataResponse {
  /**
   * 代理商发起交易必填，签名时使用代理商密钥参与签名
   */
  proxyNo: string
  /**
   * 门店号，门店收款必填
   */
  storeNo: string
  /**
   * 密钥
   */
  privateKey: string
  /**
   * 秘钥类型
Alipay
WechatPay
OfflinePay
CIBPay
CCB_PAY
SWIFT_PASS_PAY
@see EncryptionKeyType
   */
  encryptionKeyType: string
}

/**
 * 线下收款账户信息
 */
export class OfflineEncryptionKeyDataResponse implements EncryptionKeyDataResponse {
  /**
   * 企业名称（开户户名）
   */
  merchantName: string
  /**
   * 开户银行
   */
  depositBank: string
  /**
   * 柜台号
   */
  counterNumber: string
  /**
   * 秘钥类型
Alipay
WechatPay
OfflinePay
CIBPay
CCB_PAY
SWIFT_PASS_PAY
@see EncryptionKeyType
   */
  encryptionKeyType: string
}

/**
 * <AUTHOR> linq
@date : 2024-04-24 09:19
@description : 威富通支付 密钥数据
 */
export class SwiftPassPayEncryptionKeyDataResponse implements EncryptionKeyDataResponse {
  /**
   * 商户私钥
   */
  mchPrivateKey: string
  /**
   * 平台公钥
   */
  platPublicKey: string
  /**
   * 秘钥类型
Alipay
WechatPay
OfflinePay
CIBPay
CCB_PAY
SWIFT_PASS_PAY
@see EncryptionKeyType
   */
  encryptionKeyType: string
}

/**
 * 终端信息
 */
export class TerminalResponse {
  /**
   * 终端代码
   */
  terminalCode: string
  /**
   * 是否关闭
   */
  isClosed: boolean
  /**
   * 收款账号编号列表
   */
  receiveAccountIdList: Array<string>
}

/**
 * 微信支付 密钥数据
<AUTHOR> create 2021/3/9 15:24
 */
export class WechatPayEncryptionKeyDataResponse implements EncryptionKeyDataResponse {
  /**
   * Appid
   */
  appId: string
  /**
   * 商户秘钥
   */
  merchantKey: string
  /**
   * 私钥文件名
   */
  privateKeyFileName: string
  /**
   * 私钥地址
   */
  privateKeyPath: string
  /**
   * 私钥密码
   */
  privateKeyPWD: string
  /**
   * 秘钥类型
Alipay
WechatPay
OfflinePay
CIBPay
CCB_PAY
SWIFT_PASS_PAY
@see EncryptionKeyType
   */
  encryptionKeyType: string
}

/**
 * 商品开通统计信息
<AUTHOR>
@date 2022/05/10
 */
export class CommodityOpenReportFormResponse {
  /**
   * 商品ID
   */
  commoditySkuId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方案名称
   */
  schemeName: string
  /**
   * 学习方案学时
   */
  period: string
  /**
   * 合计数据
   */
  summaryInfo: SubOrderStatisticDto
  /**
   * 各渠道统计信息
   */
  purchaseChannelStatisticInfoList: Array<PurchaseChannelStatisticDto>
}

/**
 * 地区开通统计信息
<AUTHOR>
@date 2022/05/10
 */
export class RegionOpenReportFormResponse {
  /**
   * 地区id
   */
  regionId: string
  /**
   * 合计数据
   */
  summaryInfo: SubOrderStatisticDto
  /**
   * 各渠道统计信息
   */
  purchaseChannelStatisticInfoList: Array<PurchaseChannelStatisticDto>
}

/**
 * 统计报表合计数据
<AUTHOR>
@date 2022/05/10
 */
export class ReportSummaryResponse {
  /**
   * 净成交总额
<p> 交易成功订单总金额 - 已退款订单总额
   */
  totalNetAmount: number
  /**
   * 交易次数合计数据
   */
  tradeCountSummaryInfo: SubOrderStatisticDto
  /**
   * 各渠道统计信息
   */
  purchaseChannelStatisticInfoList: Array<PurchaseChannelStatisticDto>
}

/**
 * 交易时间段统计直方图
<AUTHOR>
 */
export class TradeStatisticDateHistogramResponse {
  /**
   * 直方图数据
   */
  histogramData: Array<TradeStatisticDateHistogramBucketResponse>
}

/**
 * 支付方式统计信息
<AUTHOR>
@date 2022/05/10
 */
export class PaymentTypeStatisticDto {
  /**
   * 支付方式 	1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
@see PaymentOrderTypes
   */
  paymentType: number
  /**
   * 统计信息
   */
  statisticInfo: SubOrderStatisticDto
}

/**
 * 购买渠道统计信息
<AUTHOR>
@date 2022/05/10
 */
export class PurchaseChannelStatisticDto {
  /**
   * 购买渠道 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see PurchaseChannelTypes
   */
  purchaseChannel: number
  /**
   * 各支付方式统计信息
   */
  paymentTypeStatisticInfoList: Array<PaymentTypeStatisticDto>
}

/**
 * 子订单变更记录统计情况
<AUTHOR>
@date 2022/05/10
 */
export class SubOrderStatisticDto {
  /**
   * 交易成功数量
   */
  tradeSuccessCount: number
  /**
   * 退货数量
   */
  returnCount: number
  /**
   * 换入数量
   */
  exchangeInCount: number
  /**
   * 换出数量
   */
  exchangeOutCount: number
  /**
   * 净交易成功数量
<p> (交易成功数量 + 换入数量) - (退货数量 + 换出数量)
   */
  netTradeSuccessCount: number
}

/**
 * 交易记录时间单位统计信息
<AUTHOR>
 */
export class TradeStatisticDateHistogramBucketResponse {
  /**
   * 时间
   */
  time: string
  /**
   * 交易成功金额
   */
  tradeSuccessAmount: number
}

export class BatchOrderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BatchOrderResponse>
}

export class BatchReturnOrderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BatchReturnOrderResponse>
}

export class CommodityOpenReportFormResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CommodityOpenReportFormResponse>
}

export class CommoditySkuBackstageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CommoditySkuBackstageResponse>
}

export class ExchangeOrderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ExchangeOrderResponse>
}

export class IssueCommoditySkuBackStageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<IssueCommoditySkuBackStageResponse>
}

export class IssueLogResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<IssueLogResponse>
}

export class OfflineInvoiceResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<OfflineInvoiceResponse>
}

export class OnlineInvoiceResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<OnlineInvoiceResponse>
}

export class OrderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<OrderResponse>
}

export class ReceiveAccountConfigResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ReceiveAccountConfigResponse>
}

export class ReturnOrderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ReturnOrderResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分销商--获取批次单详情
   * @param query 查询 graphql 语法文档
   * @param batchOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBatchOrderInDistributor(
    batchOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBatchOrderInDistributor,
    operation?: string
  ): Promise<Response<BatchOrderResponse>> {
    return commonRequestApi<BatchOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { batchOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取批次单详情
   * @param query 查询 graphql 语法文档
   * @param batchOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBatchOrderInServicer(
    batchOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBatchOrderInServicer,
    operation?: string
  ): Promise<Response<BatchOrderResponse>> {
    return commonRequestApi<BatchOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { batchOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-  获取批次单退货详情
   * @param query 查询 graphql 语法文档
   * @param batchReturnOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBatchReturnOrderInDistributor(
    batchReturnOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBatchReturnOrderInDistributor,
    operation?: string
  ): Promise<Response<BatchReturnOrderResponse>> {
    return commonRequestApi<BatchReturnOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { batchReturnOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取批次单退货详情
   * @param query 查询 graphql 语法文档
   * @param batchReturnOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBatchReturnOrderInServicer(
    batchReturnOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBatchReturnOrderInServicer,
    operation?: string
  ): Promise<Response<BatchReturnOrderResponse>> {
    return commonRequestApi<BatchReturnOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { batchReturnOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取报表统计合计数据，适用于当前网校下的课程供应商
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCommodityReportSummaryInCourseSupplier(
    request: TradeReportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCommodityReportSummaryInCourseSupplier,
    operation?: string
  ): Promise<Response<ReportSummaryResponse>> {
    return commonRequestApi<ReportSummaryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取报表统计合计数据
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCommodityReportSummaryInServicer(
    request: TradeReportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCommodityReportSummaryInServicer,
    operation?: string
  ): Promise<Response<ReportSummaryResponse>> {
    return commonRequestApi<ReportSummaryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取培训班商品详情
   * @param query 查询 graphql 语法文档
   * @param commoditySkuId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCommoditySkuInServicer(
    commoditySkuId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCommoditySkuInServicer,
    operation?: string
  ): Promise<Response<CommoditySkuBackstageResponse>> {
    return commonRequestApi<CommoditySkuBackstageResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { commoditySkuId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取培训班商品详情,适用于课件供应商
   * @param query 查询 graphql 语法文档
   * @param commoditySkuId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCommoditySkuInServicerInCourseVendor(
    commoditySkuId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCommoditySkuInServicerInCourseVendor,
    operation?: string
  ): Promise<Response<CommoditySkuBackstageResponse>> {
    return commonRequestApi<CommoditySkuBackstageResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { commoditySkuId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据渠道id 获取配送渠道
   * @param channelId 渠道id
   * @return
   * @param query 查询 graphql 语法文档
   * @param channelId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDeliveryChannelInServicer(
    channelId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDeliveryChannelInServicer,
    operation?: string
  ): Promise<Response<OfflineInvoiceDeliveryChannelResponse>> {
    return commonRequestApi<OfflineInvoiceDeliveryChannelResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { channelId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取网校提供的配送渠道
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDeliveryChannelListInServicer(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDeliveryChannelListInServicer,
    operation?: string
  ): Promise<Response<Array<OfflineInvoiceDeliveryChannelResponse>>> {
    return commonRequestApi<Array<OfflineInvoiceDeliveryChannelResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取网校提供的配送渠道
   * @return
   * @param query 查询 graphql 语法文档
   * @param deliveryChannelRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDeliveryChannelListV2InServicer(
    deliveryChannelRequest: DeliveryChannelRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDeliveryChannelListV2InServicer,
    operation?: string
  ): Promise<Response<Array<OfflineInvoiceDeliveryChannelResponse>>> {
    return commonRequestApi<Array<OfflineInvoiceDeliveryChannelResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { deliveryChannelRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商获取换货单详情
   * @param query 查询 graphql 语法文档
   * @param exchangeOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getExchangeOrderInDistributor(
    exchangeOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getExchangeOrderInDistributor,
    operation?: string
  ): Promise<Response<ExchangeOrderResponse>> {
    return commonRequestApi<ExchangeOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { exchangeOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取换货单详情
   * @param query 查询 graphql 语法文档
   * @param exchangeOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getExchangeOrderInServicer(
    exchangeOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getExchangeOrderInServicer,
    operation?: string
  ): Promise<Response<ExchangeOrderResponse>> {
    return commonRequestApi<ExchangeOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { exchangeOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取发票自动开票配置
   * @return
   * @param query 查询 graphql 语法文档
   * @param queryRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getInvoiceAutoBillPolicyInServicer(
    queryRequest: InvoiceAutoBillPolicyRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getInvoiceAutoBillPolicyInServicer,
    operation?: string
  ): Promise<Response<InvoiceAutoBillPolicyResponse>> {
    return commonRequestApi<InvoiceAutoBillPolicyResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { queryRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取发票详情
   * @param query 查询 graphql 语法文档
   * @param invoiceId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOfflineInvoiceInServicer(
    invoiceId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOfflineInvoiceInServicer,
    operation?: string
  ): Promise<Response<OfflineInvoiceResponse>> {
    return commonRequestApi<OfflineInvoiceResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { invoiceId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取发票详情
   * @param query 查询 graphql 语法文档
   * @param invoiceId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOnlineInvoiceInServicer(
    invoiceId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOnlineInvoiceInServicer,
    operation?: string
  ): Promise<Response<OnlineInvoiceResponse>> {
    return commonRequestApi<OnlineInvoiceResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { invoiceId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商 - 获取订单详情
   * @param query 查询 graphql 语法文档
   * @param orderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOrderInDistributor(
    orderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOrderInDistributor,
    operation?: string
  ): Promise<Response<OrderResponse>> {
    return commonRequestApi<OrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { orderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取订单详情
   * @param query 查询 graphql 语法文档
   * @param orderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getOrderInServicer(
    orderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOrderInServicer,
    operation?: string
  ): Promise<Response<OrderResponse>> {
    return commonRequestApi<OrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { orderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据购买渠道编号获取购买渠道信息
   * @param purchaseChannelId 购买渠道编号
   * @return
   * @param query 查询 graphql 语法文档
   * @param purchaseChannelId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getPurchaseChannel(
    purchaseChannelId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPurchaseChannel,
    operation?: string
  ): Promise<Response<PurchaseChannelResponse>> {
    return commonRequestApi<PurchaseChannelResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { purchaseChannelId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下收款账户配置
   * @param receiveAccountId
   * @return
   * @param query 查询 graphql 语法文档
   * @param receiveAccountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getReceiveAccountInServicer(
    receiveAccountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getReceiveAccountInServicer,
    operation?: string
  ): Promise<Response<ReceiveAccountConfigResponse>> {
    return commonRequestApi<ReceiveAccountConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { receiveAccountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前子项目下收款账户配置
   * @param query 查询 graphql 语法文档
   * @param receiveAccountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getReceiveAccountInSubProject(
    receiveAccountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getReceiveAccountInSubProject,
    operation?: string
  ): Promise<Response<ReceiveAccountConfigResponse>> {
    return commonRequestApi<ReceiveAccountConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { receiveAccountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下收款账户配置
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getReceiveAccountV2InServicer(
    request: ReceiveAccountRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getReceiveAccountV2InServicer,
    operation?: string
  ): Promise<Response<ReceiveAccountConfigResponse>> {
    return commonRequestApi<ReceiveAccountConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分销商- 获取退货单详情
   * @param query 查询 graphql 语法文档
   * @param returnOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getReturnOrderInDistributor(
    returnOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getReturnOrderInDistributor,
    operation?: string
  ): Promise<Response<ReturnOrderResponse>> {
    return commonRequestApi<ReturnOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { returnOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取退货单详情
   * @param returnOrderNo : 退货单号
   * @return 退货单信息
   * @param query 查询 graphql 语法文档
   * @param returnOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getReturnOrderInServicer(
    returnOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getReturnOrderInServicer,
    operation?: string
  ): Promise<Response<ReturnOrderResponse>> {
    return commonRequestApi<ReturnOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { returnOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下纳税人信息
   * @param taxpayerId
   * @return
   * @param query 查询 graphql 语法文档
   * @param taxpayerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTaxpayerInServicer(
    taxpayerId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTaxpayerInServicer,
    operation?: string
  ): Promise<Response<TaxpayerResponse>> {
    return commonRequestApi<TaxpayerResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { taxpayerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下纳税人信息
   * @param taxpayerId
   * @return
   * @param query 查询 graphql 语法文档
   * @param taxpayerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTaxpayerV2InServicer(
    taxpayerId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTaxpayerV2InServicer,
    operation?: string
  ): Promise<Response<TaxpayerResponse>> {
    return commonRequestApi<TaxpayerResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { taxpayerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取时间间隔交易记录统计直方图
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTradeStatisticDateHistogramInServicer(
    request: TradeStatisticDateHistogramRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTradeStatisticDateHistogramInServicer,
    operation?: string
  ): Promise<Response<TradeStatisticDateHistogramResponse>> {
    return commonRequestApi<TradeStatisticDateHistogramResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询买家所拥有商品
   * <br> 查询用户所有不处于退货完成状态的子订单的现有商品
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBuyerAllCommodityInSerivicer(
    request: BuyerValidCommodityRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBuyerAllCommodityInSerivicer,
    operation?: string
  ): Promise<Response<Array<BuyerValidCommodityResponse>>> {
    return commonRequestApi<Array<BuyerValidCommodityResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询买家有效商品
   * <br> 查询用户所有发货完成并且不处于换货中、退货状态为未退货的子订单的现有商品
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBuyerValidCommodityInSerivicer(
    request: BuyerValidCommodityRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBuyerValidCommodityInSerivicer,
    operation?: string
  ): Promise<Response<Array<BuyerValidCommodityResponse>>> {
    return commonRequestApi<Array<BuyerValidCommodityResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取商品开通统计列表（只能获取到有销售过的商品）
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listCommodityOpenReportFormsBeSoldInServicer(
    params: { limit?: number; request?: TradeReportRequest; sortRequest?: CommodityOpenReportSortRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listCommodityOpenReportFormsBeSoldInServicer,
    operation?: string
  ): Promise<Response<Array<CommodityOpenReportFormResponse>>> {
    return commonRequestApi<Array<CommodityOpenReportFormResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取指定列表地区开通统计列表，以商品地区为聚合主键
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listCommoditySkuRegionOpenReportFormsInServicer(
    request: TradeReportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listCommoditySkuRegionOpenReportFormsInServicer,
    operation?: string
  ): Promise<Response<Array<RegionOpenReportFormResponse>>> {
    return commonRequestApi<Array<RegionOpenReportFormResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询漏清洗交易记录的换货订单号
   * @return 漏清洗的换货订单号集合
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listMissExchangeOrderByPage(
    request: TradeReportMissDataRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listMissExchangeOrderByPage,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询漏清洗交易记录的子订单号
   * @return 漏清洗的子订单号集合
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listMissOrderByPage(
    request: TradeReportMissDataRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listMissOrderByPage,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询漏清洗交易记录的退货订单号
   * @return 漏清洗的退货订单号集合
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listMissReturnOrderByPage(
    request: TradeReportMissDataRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listMissReturnOrderByPage,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取发票操作记录
   * @param query 查询 graphql 语法文档
   * @param offlineInvoiceId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listOfflineInvoiceOperationRecord(
    offlineInvoiceId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listOfflineInvoiceOperationRecord,
    operation?: string
  ): Promise<Response<Array<OfflineInvoiceOperationResponse>>> {
    return commonRequestApi<Array<OfflineInvoiceOperationResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { offlineInvoiceId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取发票操作记录
   * @param query 查询 graphql 语法文档
   * @param onlineInvoiceId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listOnlineInvoiceOperationRecord(
    onlineInvoiceId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listOnlineInvoiceOperationRecord,
    operation?: string
  ): Promise<Response<Array<OnlineInvoiceOperationResponse>>> {
    return commonRequestApi<Array<OnlineInvoiceOperationResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { onlineInvoiceId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取当前服务商下收款账户配置
   * @param receiveAccountIdList
   * @return
   * @param query 查询 graphql 语法文档
   * @param receiveAccountIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listReceiveAccountInServicer(
    receiveAccountIdList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listReceiveAccountInServicer,
    operation?: string
  ): Promise<Response<Array<ReceiveAccountConfigResponse>>> {
    return commonRequestApi<Array<ReceiveAccountConfigResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { receiveAccountIdList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前子项目下收款账户配置
   * @param query 查询 graphql 语法文档
   * @param receiveAccountIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listReceiveAccountInSubProject(
    receiveAccountIdList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listReceiveAccountInSubProject,
    operation?: string
  ): Promise<Response<Array<ReceiveAccountConfigResponse>>> {
    return commonRequestApi<Array<ReceiveAccountConfigResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { receiveAccountIdList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下收款账户配置
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listReceiveAccountV2InServicer(
    request: ReceiveAccountRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listReceiveAccountV2InServicer,
    operation?: string
  ): Promise<Response<Array<ReceiveAccountConfigResponse>>> {
    return commonRequestApi<Array<ReceiveAccountConfigResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取指定列表地区开通统计列表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listRegionOpenReportFormsInServier(
    request: TradeReportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listRegionOpenReportFormsInServier,
    operation?: string
  ): Promise<Response<Array<RegionOpenReportFormResponse>>> {
    return commonRequestApi<Array<RegionOpenReportFormResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取子项目下退货原因列表
   * @return 退货原因列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listReturnReasonInfoInSubProject(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listReturnReasonInfoInSubProject,
    operation?: string
  ): Promise<Response<Array<ReturnReasonInfoResponse>>> {
    return commonRequestApi<Array<ReturnReasonInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下纳税人信息列表
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listTaxpayerInServicer(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTaxpayerInServicer,
    operation?: string
  ): Promise<Response<Array<TaxpayerResponse>>> {
    return commonRequestApi<Array<TaxpayerResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下纳税人信息列表
   * @return
   * @param query 查询 graphql 语法文档
   * @param taxpayerIRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listTaxpayerV2InService(
    taxpayerIRequest: TaxpayerIRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTaxpayerV2InService,
    operation?: string
  ): Promise<Response<Array<TaxpayerResponse>>> {
    return commonRequestApi<Array<TaxpayerResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { taxpayerIRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前网校下的商品包含的地区列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listUniqueRegionFromCommodityInServicer(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listUniqueRegionFromCommodityInServicer,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分销商-  集体报名订单列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageBatchOrderInDistributor(
    params: { page?: Page; request?: BatchOrderRequestInDistributor; sortRequest?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageBatchOrderInDistributor,
    operation?: string
  ): Promise<Response<BatchOrderResponsePage>> {
    return commonRequestApi<BatchOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取批次单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageBatchOrderInServicer(
    params: { page?: Page; request?: BatchOrderRequest; sortRequest?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageBatchOrderInServicer,
    operation?: string
  ): Promise<Response<BatchOrderResponsePage>> {
    return commonRequestApi<BatchOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 分页获取批次单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageBatchOrderInTrainingChannel(
    params: { page?: Page; request?: BatchOrderRequest; sortRequest?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageBatchOrderInTrainingChannel,
    operation?: string
  ): Promise<Response<BatchOrderResponsePage>> {
    return commonRequestApi<BatchOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-   分页获取批次退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageBatchReturnOrderInDistributor(
    params: {
      page?: Page
      request?: BatchReturnOrderRequestInDistributor
      sortRequest?: Array<BatchReturnOrderSortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageBatchReturnOrderInDistributor,
    operation?: string
  ): Promise<Response<BatchReturnOrderResponsePage>> {
    return commonRequestApi<BatchReturnOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取批次退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageBatchReturnOrderInServicer(
    params: { page?: Page; request?: BatchReturnOrderRequest; sortRequest?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageBatchReturnOrderInServicer,
    operation?: string
  ): Promise<Response<BatchReturnOrderResponsePage>> {
    return commonRequestApi<BatchReturnOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员  - 分页获取批次退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageBatchReturnOrderInTrainingChannel(
    params: { page?: Page; request?: BatchReturnOrderRequest; sortRequest?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageBatchReturnOrderInTrainingChannel,
    operation?: string
  ): Promise<Response<BatchReturnOrderResponsePage>> {
    return commonRequestApi<BatchReturnOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取商品开通统计列表（含所有商品），适用于当前网校下的课程供应商
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCommodityOpenReportFormsInCourseSupplier(
    params: { page?: Page; request?: TradeReportRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommodityOpenReportFormsInCourseSupplier,
    operation?: string
  ): Promise<Response<CommodityOpenReportFormResponsePage>> {
    return commonRequestApi<CommodityOpenReportFormResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取商品开通统计列表（含所有商品）
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCommodityOpenReportFormsInServicer(
    params: { page?: Page; request?: TradeReportRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommodityOpenReportFormsInServicer,
    operation?: string
  ): Promise<Response<CommodityOpenReportFormResponsePage>> {
    return commonRequestApi<CommodityOpenReportFormResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 网校创建的培训班商品分页查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCommoditySkuInServicer(
    params: { page?: Page; queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommoditySkuInServicer,
    operation?: string
  ): Promise<Response<CommoditySkuBackstageResponsePage>> {
    return commonRequestApi<CommoditySkuBackstageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 网校创建的培训班商品分页查询,适用于课件供应商
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCommoditySkuInServicerInCourseVendor(
    params: { page?: Page; queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommoditySkuInServicerInCourseVendor,
    operation?: string
  ): Promise<Response<CommoditySkuBackstageResponsePage>> {
    return commonRequestApi<CommoditySkuBackstageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 商品分页
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCommoditySkuInTrainingChannel(
    params: { page?: Page; queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommoditySkuInTrainingChannel,
    operation?: string
  ): Promise<Response<CommoditySkuBackstageResponsePage>> {
    return commonRequestApi<CommoditySkuBackstageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商分页换货查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageExchangeOrderInDistributor(
    params: { page?: Page; request?: ExchangeOrderRequest; sort?: Array<ExchangeOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageExchangeOrderInDistributor,
    operation?: string
  ): Promise<Response<ExchangeOrderResponsePage>> {
    return commonRequestApi<ExchangeOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 换货单分页查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageExchangeOrderInServicer(
    params: { page?: Page; request?: ExchangeOrderRequest; sort?: Array<ExchangeOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageExchangeOrderInServicer,
    operation?: string
  ): Promise<Response<ExchangeOrderResponsePage>> {
    return commonRequestApi<ExchangeOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取培训班商品期别信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageIssueCommoditySkuInServicer(
    params: { page?: Page; request?: IssueCommoditySkuBackStageRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageIssueCommoditySkuInServicer,
    operation?: string
  ): Promise<Response<IssueCommoditySkuBackStageResponsePage>> {
    return commonRequestApi<IssueCommoditySkuBackStageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-查询期别变更记录
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageIssueLogInDistributor(
    params: { page?: Page; request?: IssueLogRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageIssueLogInDistributor,
    operation?: string
  ): Promise<Response<IssueLogResponsePage>> {
    return commonRequestApi<IssueLogResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询期别变更记录
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageIssueLogInServicer(
    params: { page?: Page; request?: IssueLogRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageIssueLogInServicer,
    operation?: string
  ): Promise<Response<IssueLogResponsePage>> {
    return commonRequestApi<IssueLogResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询线下发票
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOfflineInvoiceInServicer(
    params: { page?: Page; request?: OfflineInvoiceRequest; sort?: Array<OfflineInvoiceSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOfflineInvoiceInServicer,
    operation?: string
  ): Promise<Response<OfflineInvoiceResponsePage>> {
    return commonRequestApi<OfflineInvoiceResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 -  分页查询线下发票
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOfflineInvoiceInTrainingChannel(
    params: { page?: Page; request?: OfflineInvoiceRequest; sort?: Array<OfflineInvoiceSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOfflineInvoiceInTrainingChannel,
    operation?: string
  ): Promise<Response<OfflineInvoiceResponsePage>> {
    return commonRequestApi<OfflineInvoiceResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页查询线上发票
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOnlineInvoiceInServicer(
    params: { page?: Page; request?: OnlineInvoiceRequest; sort?: Array<OnlineInvoiceSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOnlineInvoiceInServicer,
    operation?: string
  ): Promise<Response<OnlineInvoiceResponsePage>> {
    return commonRequestApi<OnlineInvoiceResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员- 分页查询线上发票
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOnlineInvoiceInTrainingChannel(
    params: { page?: Page; request?: OnlineInvoiceRequest; sort?: Array<OnlineInvoiceSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOnlineInvoiceInTrainingChannel,
    operation?: string
  ): Promise<Response<OnlineInvoiceResponsePage>> {
    return commonRequestApi<OnlineInvoiceResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-分页查询订单信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOrderInDistributor(
    params: { page?: Page; request?: OrderRequestInDistributor; sortRequest?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOrderInDistributor,
    operation?: string
  ): Promise<Response<OrderResponsePage>> {
    return commonRequestApi<OrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取订单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageOrderInServicer(
    params: { page?: Page; request?: OrderRequest; sortRequest?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOrderInServicer,
    operation?: string
  ): Promise<Response<OrderResponsePage>> {
    return commonRequestApi<OrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 专题管理员 - 分页获取订单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOrderInTrainingChannel(
    params: { page?: Page; request?: OrderRequest; sortRequest?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOrderInTrainingChannel,
    operation?: string
  ): Promise<Response<OrderResponsePage>> {
    return commonRequestApi<OrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取当前服务商下收款账户配置
   * @param page
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageReceiveAccountInDistribution(
    params: { page?: Page; request?: ReceiveAccountConfigRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageReceiveAccountInDistribution,
    operation?: string
  ): Promise<Response<ReceiveAccountConfigResponsePage>> {
    return commonRequestApi<ReceiveAccountConfigResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取当前服务商下收款账户配置（分销商）
   * @param page
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageReceiveAccountInServicer(
    params: { page?: Page; request?: ReceiveAccountConfigRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageReceiveAccountInServicer,
    operation?: string
  ): Promise<Response<ReceiveAccountConfigResponsePage>> {
    return commonRequestApi<ReceiveAccountConfigResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageReceiveAccountInSubProject(
    params: { page?: Page; request?: ReceiveAccountConfigRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageReceiveAccountInSubProject,
    operation?: string
  ): Promise<Response<ReceiveAccountConfigResponsePage>> {
    return commonRequestApi<ReceiveAccountConfigResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分销商-  退货单分页查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageReturnOrderInDistributor(
    params: { page?: Page; request?: ReturnOrderRequestInDistributor; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageReturnOrderInDistributor,
    operation?: string
  ): Promise<Response<ReturnOrderResponsePage>> {
    return commonRequestApi<ReturnOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 退货单分页查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageReturnOrderInServicer(
    params: { page?: Page; request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageReturnOrderInServicer,
    operation?: string
  ): Promise<Response<ReturnOrderResponsePage>> {
    return commonRequestApi<ReturnOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 专题管理员 - 退货单分页查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageReturnOrderInTrainingChannel(
    params: { page?: Page; request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageReturnOrderInTrainingChannel,
    operation?: string
  ): Promise<Response<ReturnOrderResponsePage>> {
    return commonRequestApi<ReturnOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-  获取批次单总数量、总金额
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticBatchOrderInDistributor(
    request: BatchOrderRequestInDistributor,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticBatchOrderInDistributor,
    operation?: string
  ): Promise<Response<BatchOrderStatisticResponse>> {
    return commonRequestApi<BatchOrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取批次单总数量、总金额
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticBatchOrderInServicer(
    request: BatchOrderRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticBatchOrderInServicer,
    operation?: string
  ): Promise<Response<BatchOrderStatisticResponse>> {
    return commonRequestApi<BatchOrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 获取批次单总数量、总金额
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticBatchOrderInTrainingChannel(
    request: BatchOrderRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticBatchOrderInTrainingChannel,
    operation?: string
  ): Promise<Response<BatchOrderStatisticResponse>> {
    return commonRequestApi<BatchOrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-  获取批次退货单总数量、退款总金额
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticBatchReturnOrderInDistributor(
    request: BatchReturnOrderRequestInDistributor,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticBatchReturnOrderInDistributor,
    operation?: string
  ): Promise<Response<BatchReturnOrderStatisticResponse>> {
    return commonRequestApi<BatchReturnOrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取批次退货单总数量、退款总金额
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticBatchReturnOrderInServicer(
    request: BatchReturnOrderRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticBatchReturnOrderInServicer,
    operation?: string
  ): Promise<Response<BatchReturnOrderStatisticResponse>> {
    return commonRequestApi<BatchReturnOrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员  - 获取批次退货单总数量、退款总金额
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticBatchReturnOrderInTrainingChannel(
    request: BatchReturnOrderRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticBatchReturnOrderInTrainingChannel,
    operation?: string
  ): Promise<Response<BatchReturnOrderStatisticResponse>> {
    return commonRequestApi<BatchReturnOrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取发票开票总金额、发票总税额
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticOnlineInvoiceInServicer(
    request: OnlineInvoiceRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticOnlineInvoiceInServicer,
    operation?: string
  ): Promise<Response<OnlineInvoiceStatisticResponse>> {
    return commonRequestApi<OnlineInvoiceStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员- 获取发票开票总金额、发票总税额
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticOnlineInvoiceInTrainingChannel(
    request: OnlineInvoiceRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticOnlineInvoiceInTrainingChannel,
    operation?: string
  ): Promise<Response<OnlineInvoiceStatisticResponse>> {
    return commonRequestApi<OnlineInvoiceStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-订单信息统计
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticOrderInDistributor(
    request: OrderRequestInDistributor,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticOrderInDistributor,
    operation?: string
  ): Promise<Response<OrderStatisticResponse>> {
    return commonRequestApi<OrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取订单总金额、总数量
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async statisticOrderInServicer(
    request: OrderRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticOrderInServicer,
    operation?: string
  ): Promise<Response<OrderStatisticResponse>> {
    return commonRequestApi<OrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 专题管理员 - 获取订单总金额、总数量
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticOrderInTrainingChannel(
    request: OrderRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticOrderInTrainingChannel,
    operation?: string
  ): Promise<Response<OrderStatisticResponse>> {
    return commonRequestApi<OrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-  获取退货单总数量、退款总金额
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticReturnOrderInDistributor(
    request: ReturnOrderRequestInDistributor,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticReturnOrderInDistributor,
    operation?: string
  ): Promise<Response<ReturnOrderStatisticResponse>> {
    return commonRequestApi<ReturnOrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取退货单总数量、退款总金额
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async statisticReturnOrderInServicer(
    request: ReturnOrderRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticReturnOrderInServicer,
    operation?: string
  ): Promise<Response<ReturnOrderStatisticResponse>> {
    return commonRequestApi<ReturnOrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 专题管理员  - 获取退货单总数量、退款总金额
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticReturnOrderInTrainingChannel(
    request: ReturnOrderRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticReturnOrderInTrainingChannel,
    operation?: string
  ): Promise<Response<ReturnOrderStatisticResponse>> {
    return commonRequestApi<ReturnOrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async temporaryOrderUpdateOrderInfoInSubject(
    params: { page?: Page; request?: OrderRequest; sortRequest?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.temporaryOrderUpdateOrderInfoInSubject,
    operation?: string
  ): Promise<Response<OrderResponsePage>> {
    return commonRequestApi<OrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
