<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-07-08 14:50:10
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2025-02-11 11:44:16
 * @Description: 科目类型选择器
-->
<template>
  <el-select v-model="selectValue" :placeholder="placeholder" class="form-l" filterable clearable multiple>
    <el-option
      v-for="item in accountTypeOptions"
      :label="showLabel(item)"
      :value="item.propertyId"
      :key="item.propertyId"
      :disabled="isDisabled && item.name != '全部'"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Mixins, Prop, Watch, Emit } from 'vue-property-decorator'
  import SubjectTypeVo from '@api/service/common/basic-data-dictionary/query/vo/SubjectTypeVo'
  import QuerySubjectType from '@api/service/common/basic-data-dictionary/query/QuerySubjectType'
  import CommonSkuMixins from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSkuMixins'
  @Component
  export default class extends Mixins(CommonSkuMixins) {
    accountTypeOptions: Array<SubjectTypeVo> = new Array<SubjectTypeVo>()

    @Watch('industryPropertyId', {
      immediate: true,
      deep: true
    })
    async industryPropertyIdChange(val: any) {
      if (val) {
        await this.getAccountTypeOptions()
      }
    }

    /**
     * 获取展示名称
     */
    get showLabel() {
      return (item: SubjectTypeVo) => {
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    // 专业科目类型Id
    professionalSubjectTypeId = ''
    // 公需+专业科目类型Id
    mixedSubjectTypeId = ''
    /**
     * 获取科目类型
     */
    async getAccountTypeOptions() {
      const res = await QuerySubjectType.querySubjectTypeList(this.industryPropertyId, this.industryId)
      this.accountTypeOptions = res.isSuccess() ? QuerySubjectType.subjectTypeList : ([] as SubjectTypeVo[])
      // TODO 很粗糙的判断方式，待优化
      this.accountTypeOptions?.forEach((el: SubjectTypeVo) => {
        if (el.name === '专业') {
          this.professionalSubjectTypeId = el.propertyId
        }
        if (el.name === '公需+专业') {
          this.mixedSubjectTypeId = el.propertyId
        }
        this.$emit('subjectTypeInfos', {
          professionalSubjectTypeId: this.professionalSubjectTypeId,
          mixedSubjectTypeId: this.mixedSubjectTypeId
        })
      })
      const param = new SubjectTypeVo()
      param.propertyId = '-1'
      param.name = '全部'
      param.sort = 0
      param.showName = ''
      this.accountTypeOptions.unshift(param)
    }
  }
</script>
