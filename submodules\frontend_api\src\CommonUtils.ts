import * as _ from 'lodash'

/**
 * @deprecated
 */
class CommonUtils {
  static deepCopy<T>(target: T, source: any): T {
    const duplicate = _.cloneDeep(source)
    Object.assign(target, duplicate)
    return target
  }

  /**
   * 比较两个对象的属性是否相同
   * @deprecated
   * @param o1
   * @param o2
   * @param excludes 排除比较的属性
   */
  static equals(o1: any, o2: any, ...excludes: string[]): boolean {
    if (o1 instanceof Array) {
      if (o2 instanceof Array) {
        return CommonUtils.equalsArray(o1, o2)
      } else {
        return false
      }
    } else if (o1 instanceof Object) {
      if (o2 instanceof Object) {
        let same = CommonUtils.equalsObject(o1, o2, ...excludes)
        if (same) {
          same = same && CommonUtils.equalsObject(o2, o1, ...excludes)
        }
        return same
      } else {
        return false
      }
    } else {
      return _.isEqual(o1, o2)
    }
  }

  /**
   * @deprecated
   * @param o1
   * @param o2
   * @param excludes
   * @private
   */
  private static equalsObject(o1: any, o2: any, ...excludes: string[]): boolean {
    const keys = Object.keys(o1)
    let same = true
    for (const key of keys) {
      if (excludes && excludes.length > 0) {
        if (!excludes.includes(key)) {
          const value1 = o1[key]
          const value2 = o2[key]
          same = CommonUtils.equals(value1, value2)
          if (!same) return false
        }
      }
    }
    return same
  }

  /**
   * @deprecated
   * @param a1
   * @param a2
   * @private
   */
  private static equalsArray(a1: Array<any>, a2: Array<any>): boolean {
    if (a1) {
      if (a2) {
        if (a1.length === a2.length) {
          let same = true
          for (const i in a1) {
            const value1 = a1[i]
            const value2 = a2[i]
            same = CommonUtils.equals(value1, value2)
            if (!same) {
              return false
            }
          }
          return same
        } else if (a1 === a2) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    } else {
      if (a2) {
        return false
      } else {
        return true
      }
    }
  }
}

/**
 * @deprecated
 */
export class Factory<T> {
  create(type: new () => T): T {
    return new type()
  }
}

export default CommonUtils
