/**
 * 创建课程分类的对象
 */
class OperateCourseCategory {
  id: string
  // 名称
  name: string
  // 父分类 id
  parentId: string
  sort = 1

  reset() {
    this.name = ''
    this.parentId = ''
    this.sort = 1
  }

  /**
   * 类型转换静态方法
   * @param responseDetail
   */
  static from(responseDetail: any) {
    const detail = new OperateCourseCategory()
    detail.id = responseDetail.id
    detail.name = responseDetail.name
    detail.parentId = responseDetail.parentId
    detail.sort = responseDetail.sort
    return detail
  }
}

export default OperateCourseCategory
