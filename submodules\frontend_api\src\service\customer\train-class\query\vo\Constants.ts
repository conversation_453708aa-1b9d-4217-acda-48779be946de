export class Constants {
  static readonly ChooseRuleSchemeType = 'chooseCourseLearning'
  static readonly AutonomousCourseLearningSchemeType = 'autonomousCourseLearning'
  static getSchemeType(schemeTypeString: string) {
    if (schemeTypeString == Constants.ChooseRuleSchemeType) {
      return 1
    }
    if (schemeTypeString == Constants.AutonomousCourseLearningSchemeType) {
      return 2
    }
  }
}
