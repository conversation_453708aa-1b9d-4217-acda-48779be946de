"""独立部署的微服务,K8S服务名:ms-examquestion-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""批量导入试题分页查询
		@param request 批量导入试题查询请求
		@param page    分页信息
		@return 批量导入试题分页查询响应
	"""
	findBatchImportQuestionByPage(request:BatchImportQuestionQueryRequest,page:Page):FindBatchImportQuestionByPageResponsePage @page(for:"FindBatchImportQuestionByPageResponse")
}
type Mutation {
	"""导出导入试题数据
		@param request 批量导出导入的试题请求
		@return 文件路径
	"""
	batchExportAllChooseQuestionData(request:ExportQuestionDataRequest):String
	"""导出导入试题失败数据
		@param request 批量导出导入的试题请求
		@return 文件路径
	"""
	batchExportFailChooseQuestionData(request:ExportQuestionDataRequest):String
	"""批量导入试题
		@param request 导入试题请求
	"""
	batchImportChooseQuestions(request:ImportQuestionsRequest):Void
	"""创建题库
		@param request 创建题库请求
		@return 题库id
	"""
	createLibrary(request:CreateLibraryRequest):String
	"""创建试题
		@param request 创建试题请求
		@return 试题id
	"""
	createQuestion(request:CreateQuestionRequest):String
	"""@param request
		@return java.lang.String
		@title:创建调查问卷题库
		@description:父试题库id如果没有，父题库id默认为QUESTIONNAIRE_LIBRARY
		@author: chenzeyu
		@date: 2024/7/29 15:39
	"""
	createQuestionnaireLibrary(request:CreateQuestionnaireLibraryRequest):String
	"""创建试题(有标签)
		且不进行重复性校验
		@param request 创建试题请求
		@return 试题id
	"""
	createTagQuestion(request:CreateNoCheckQuestionRequest):String
	"""停用试题
		@param id 试题id
	"""
	disableQuestion(id:String!):Void
	"""启用试题
		@param id 试题id
	"""
	enableQuestion(id:String!):Void
	initQuestionData(request:InitQuestionRequest):Void @optionalLogin
	"""删除题库
		@param id 题库id
	"""
	removeLibrary(id:String!):Void
	"""删除试题
		@param id 试题id
	"""
	removeQuestion(id:String!):Void
	"""删除试题
		@param id 试题id
	"""
	removeTagQuestion(id:String!):Void
	"""@创建题库"""
	systemCreateLibrary(request:SystemCreateLibraryRequest):String @optionalLogin
	"""修改题库
		@param request 修改题库请求
		@return 题库id
	"""
	updateLibrary(request:UpdateLibraryRequest):String
	"""修改试题
		@param request 修改试题请求
		@return 试题id
	"""
	updateQuestion(request:UpdateQuestionRequest):String
	"""修改试题
		@param request 修改试题请求
		@return 试题id
	"""
	updateTagQuestion(request:UpdateNoCheckQuestionRequest):String
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""创建试题请求体
	<AUTHOR>
	@date 2025/4/30
"""
input CreateNoCheckQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.CreateNoCheckQuestionRequest") {
	"""试题内容"""
	createQuestionRequest:CreateQuestionRequest
	"""标签"""
	tagCode:[String]
}
"""<AUTHOR>
	@date 2025/4/30
"""
input UpdateNoCheckQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.UpdateNoCheckQuestionRequest") {
	"""试题内容"""
	updateQuestionRequest:UpdateQuestionRequest
	"""标签"""
	tagCode:[String]
}
"""创建题库请求
	<AUTHOR> create 2021/7/1 14:06
"""
input CreateLibraryRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.library.CreateLibraryRequest") {
	"""题库id"""
	id:String
	"""名称【必填】"""
	name:String!
	"""描述"""
	description:String
	"""父试题库id 如果没有父题库为-1【必填】"""
	parentId:String!
	"""是否可用"""
	enabled:Boolean!
}
"""@Author: chenzeyu
	@CreateTime: 2024-07-29  15:28
	@Description: 创建调查问卷题库请求
"""
input CreateQuestionnaireLibraryRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.library.CreateQuestionnaireLibraryRequest") {
	"""题库id"""
	id:String
	"""名称【必填】"""
	name:String!
	"""描述"""
	description:String
	"""父试题库id 如果没有父题库为QUESTIONNAIRE_LIBRARY"""
	parentId:String!
	"""是否可用"""
	enabled:Boolean!
}
"""创建题库请求
	<AUTHOR> 2022年12月21日20:28:05
"""
input SystemCreateLibraryRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.library.SystemCreateLibraryRequest") {
	"""题库id"""
	id:String
	"""名称【必填】"""
	name:String!
	"""描述"""
	description:String
	"""父试题库id 如果没有父题库为-1【必填】"""
	parentId:String!
	"""是否可用"""
	enabled:Boolean!
}
"""修改题库命令
	<AUTHOR> create 2021/7/1 14:06
"""
input UpdateLibraryRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.library.UpdateLibraryRequest") {
	"""题库ID【必填】"""
	id:String!
	"""名称【必填】"""
	name:String!
	"""描述"""
	description:String
	"""父试题库id 如果没有父题库为-1【必填】，问卷题库如果没有父题库为QUESTIONNAIRE_LIBRARY"""
	parentId:String!
}
"""批量导入试题查询请求
	<AUTHOR>
"""
input BatchImportQuestionQueryRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.BatchImportQuestionQueryRequest") {
	"""任务名称"""
	taskName:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""执行时间（起始）"""
	executeStartTime:DateTime
	"""执行时间（终止）"""
	executeEndTime:DateTime
}
"""选择题答案选项实体
	<AUTHOR>
"""
input ChooseAnswerOptionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.ChooseAnswerOptionRequest") {
	"""答案ID"""
	id:String!
	"""答案内容"""
	content:String!
	"""选项建议分数"""
	suggestionScore:Double
	"""是否允许填空"""
	enableFillContent:Boolean
	"""填空是否必填"""
	mustFillContent:Boolean
}
"""<AUTHOR> create 2021/6/29 14:03"""
input CreateQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.CreateQuestionRequest") {
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""批量导出导入的试题请求
	<AUTHOR>
"""
input ExportQuestionDataRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.ExportQuestionDataRequest") {
	"""主任务id"""
	mainTaskId:String!
}
"""导入试题请求
	<AUTHOR>
"""
input ImportQuestionsRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.ImportQuestionsRequest") {
	"""文件路径"""
	filePath:String
	"""文件名"""
	fileName:String
}
"""初始化试题请求"""
input InitQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.InitQuestionRequest") {
	fixdType:Int!
}
"""<AUTHOR> create 2021/6/29 14:03"""
input UpdateQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.UpdateQuestionRequest") {
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""<AUTHOR> create 2021/6/28 14:13"""
input CreateAskQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.ask.CreateAskQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""<AUTHOR> create 2021/6/28 14:13"""
input UpdateAskQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.ask.UpdateAskQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""<AUTHOR> create 2021/6/28 14:43"""
input ChildQuestionCreateInfoRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.father.ChildQuestionCreateInfoRequest") {
	"""子题序号"""
	no:Int!
	"""试题内容"""
	question:CreateQuestionRequest
}
"""<AUTHOR> create 2021/6/28 14:43"""
input ChildQuestionUpdateInfoRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.father.ChildQuestionUpdateInfoRequest") {
	"""子题序号"""
	no:Int!
	"""试题内容"""
	question:UpdateQuestionRequest
	"""新增试题"""
	newQuestion:CreateQuestionRequest
}
"""<AUTHOR> create 2021/6/28 14:43"""
input CreateFatherQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.father.CreateFatherQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""子题集合"""
	childQuestions:[ChildQuestionCreateInfoRequest]!
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""<AUTHOR> create 2021/6/28 14:43"""
input UpdateFatherQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.father.UpdateFatherQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""子题集合"""
	childQuestions:[ChildQuestionUpdateInfoRequest]!
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""填空题创建命令
	<AUTHOR> create 2021/6/28 14:09
"""
input CreateFillQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.fill.CreateFillQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""填空数"""
	fillCount:Int!
	"""正确答案"""
	correctAnswer:FillAnswerRequest
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""散乱无序填空题答案实体
	<AUTHOR>
"""
input DisarrayFillAnswerRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.fill.DisarrayFillAnswerRequest",implementsInputs:["FillAnswerRequest"]) {
	"""正确答案集合"""
	correctAnswers:[[String]]!
	"""答案类型"""
	type:FillAnswerType!
}
"""填空题答案基类
	<AUTHOR>
"""
input FillAnswerRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.fill.FillAnswerRequest") {
	"""答案类型"""
	type:FillAnswerType!
}
"""<AUTHOR> create 2021/6/29 15:41"""
input FillCorrectAnswers @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.fill.FillCorrectAnswers") {
	"""空格位置"""
	blankNo:Int!
	"""答案备选项"""
	answers:[String]
}
"""按序填空题答案
	<AUTHOR>
"""
input SequenceFillAnswerRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.fill.SequenceFillAnswerRequest",implementsInputs:["FillAnswerRequest"]) {
	correctAnswers:[FillCorrectAnswers]!
	"""答案类型"""
	type:FillAnswerType!
}
"""按序关联填空题答案实体
	<AUTHOR>
"""
input SequenceRateFillAnswerRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.fill.SequenceRateFillAnswerRequest",implementsInputs:["FillAnswerRequest"]) {
	"""正确答案集合"""
	correctAnswers:[SequenceFillAnswerRequest]
	"""答案类型"""
	type:FillAnswerType!
}
"""填空题创建命令
	<AUTHOR> create 2021/6/28 14:09
"""
input UpdateFillQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.fill.UpdateFillQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""填空数"""
	fillCount:Int!
	"""正确答案"""
	correctAnswer:FillAnswerRequest
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""多选题创建命令
	<AUTHOR> create 2021/6/28 14:07
"""
input CreateMultipleQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.multiple.CreateMultipleQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""可选答案列表【必填】"""
	answerOptions:[ChooseAnswerOptionRequest]
	"""正确答案ID集合【必填】"""
	correctAnswerIds:[String]!
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""多选题创建命令
	<AUTHOR> create 2021/6/28 14:07
"""
input UpdateMultipleQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.multiple.UpdateMultipleQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""可选答案列表【必填】"""
	answerOptions:[ChooseAnswerOptionRequest]
	"""正确答案ID集合【必填】"""
	correctAnswerIds:[String]!
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""判断题创建命令
	<AUTHOR> create 2021/6/28 14:05
"""
input CreateOpinionQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.opinion.CreateOpinionQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""正确答案【必填】"""
	correctAnswer:Boolean!
	"""正确文本【必填】"""
	correctAnswerText:String
	"""不正确文本【必填】"""
	incorrectAnswerText:String
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""判断题创建命令
	<AUTHOR> create 2021/6/28 14:05
"""
input UpdateOpinionQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.opinion.UpdateOpinionQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""正确答案【必填】"""
	correctAnswer:Boolean!
	"""正确文本【必填】"""
	correctAnswerText:String!
	"""不正确文本【必填】"""
	incorrectAnswerText:String!
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""单选题创建命令
	<AUTHOR> create 2021/6/28 9:39
"""
input CreateRadioQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.radio.CreateRadioQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""可选答案列表【必填】"""
	answerOptions:[ChooseAnswerOptionRequest]
	"""正确答案ID【必填】"""
	correctAnswerId:String
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""单选题创建命令
	<AUTHOR> create 2021/6/28 9:39
"""
input UpdateRadioQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.radio.UpdateRadioQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""可选答案列表"""
	answerOptions:[ChooseAnswerOptionRequest]
	"""正确答案ID"""
	correctAnswerId:String
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""@Author: chenzeyu
	@CreateTime: 2024-07-29  16:12
	@Description: 量表题创建请求
"""
input CreateScaleQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.scale.CreateScaleQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""量表类型
		@see com.fjhb.domain.exam.api.question.consts.ScaleTypes
	"""
	scaleType:Int!
	"""程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	startDegree:String
	"""程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	endDegree:String
	"""级数"""
	series:Int!
	"""初始值"""
	initialValue:Int!
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""@Author: chenzeyu
	@CreateTime: 2024-07-30  15:40
	@Description: 量表题修改请求
"""
input UpdateScaleQuestionRequest @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.request.question.scale.UpdateScaleQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""量表类型
		@see com.fjhb.domain.exam.api.question.consts.ScaleTypes
	"""
	scaleType:Int!
	"""程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	startDegree:String
	"""程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	endDegree:String
	"""级数"""
	series:Int!
	"""初始值"""
	initialValue:Int!
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""<AUTHOR> create 2021/6/29 14:23"""
enum FillAnswerType @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.consts.FillAnswerType") {
	disarray
	sequence
	sequenceRelate
}
"""各状态及执行结果对应数量
	<AUTHOR>
"""
type EachStateCount @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.response.EachStateCount") {
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	state:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int
	"""数量"""
	count:Int!
}
"""批量导入试题查询响应
	<AUTHOR>
"""
type FindBatchImportQuestionByPageResponse @type(value:"com.fjhb.ms.exam.question.v1.kernel.gateway.graphql.response.FindBatchImportQuestionByPageResponse") {
	"""任务编号"""
	id:String
	"""【必填】平台编号"""
	platformId:String
	"""【必填】平台版本编号"""
	platformVersionId:String
	"""【必填】项目编号"""
	projectId:String
	"""【必填】子项目编号"""
	subProjectId:String
	"""【必填】服务商id"""
	servicerId:String
	"""任务名称"""
	name:String
	"""任务分类"""
	category:String
	"""所属批次单编号"""
	batchNo:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""处理信息"""
	message:String
	"""创建时间"""
	createdTime:DateTime
	"""就绪时间"""
	alreadyTime:DateTime
	"""执行时间"""
	executingTime:DateTime
	"""完成时间"""
	completedTime:DateTime
	"""各状态及执行结果对应数量集合
		总数：全部数量之和
		成功数：result = 1数量之和
		失败数：result = 2数量之和
	"""
	eachStateCounts:[EachStateCount]
}

scalar List
type FindBatchImportQuestionByPageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [FindBatchImportQuestionByPageResponse]}
