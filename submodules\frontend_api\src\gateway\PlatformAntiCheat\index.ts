import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformAntiCheat'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformAntiCheat'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举

// 类

/**
 * <AUTHOR>
 */
export class AntiCheatLogBaseDto {
  /**
   * 跟踪维度，0/1/2/3/4，进入场景/退出场景/时间/进度/具体项目
   */
  dimensions: number
  /**
   * 单次拍照路径
   */
  currentAnswer: string
  /**
   * 当前照片的base64数据
   */
  currentAnswerBase64: string
  /**
   * 验证结果
   */
  result: boolean
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime: string
  /**
   * 记录点对应的时间点，单位：秒，如：学习对应时长，考试对应当前考试时间
   */
  timeLength: number
  /**
   * 格式化后的时长
   */
  formatTimeLength: string
}

/**
 * <AUTHOR>
 */
export class LastedExamFSInfoDto {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 答题记录卷id
   */
  answerExamPaperRecordId: string
  /**
   * 考试考试时间（用户开始考试的时间）
   */
  answerPaperStartTime: string
  /**
   * 答卷完成时间
   */
  answerPaperCompleteTime: string
  /**
   * 考试成绩
   */
  examScore: number
  /**
   * 拍摄认证是否全部通过 | 是否替考
   */
  resultAllPass: boolean
  /**
   * 是否合格
   */
  qualified: boolean
  /**
   * 进入拍摄记录
   */
  enterList: Array<AntiCheatLogBaseDto>
  /**
   * 过程拍摄记录
   */
  processList: Array<AntiCheatLogBaseDto>
}

/**
 * <AUTHOR>
@date 2020/8/24
@description
 */
export class RandomIdInfo {
  /**
   * 随机码对应上传的图片url地址
   */
  imgUrl: string
  /**
   * 随机码是否过期
   */
  expire: boolean
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 查询用户指定考试答卷的人脸识别防作弊记录，考试过程的相同拍摄点只返回最新的一次拍摄记录，进入考试和退出考试全部返回
   * @param
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findLastedExamFSInfoList(
    params: { schemeId?: string; userId?: string },
    query: DocumentNode = GraphqlImporter.findLastedExamFSInfoList,
    operation?: string
  ): Promise<Response<Array<LastedExamFSInfoDto>>> {
    return commonRequestApi<Array<LastedExamFSInfoDto>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据随机码获取随机码信息
   * @param
   * @return
   * @param query 查询 graphql 语法文档
   * @param randomId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRandomIdInfo(
    randomId: string,
    query: DocumentNode = GraphqlImporter.getRandomIdInfo,
    operation?: string
  ): Promise<Response<RandomIdInfo>> {
    return commonRequestApi<RandomIdInfo>(
      SERVER_URL,
      {
        query: query,
        variables: { randomId },
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyDatumPhotoRandom(
    mutate: DocumentNode = GraphqlImporter.applyDatumPhotoRandom,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
