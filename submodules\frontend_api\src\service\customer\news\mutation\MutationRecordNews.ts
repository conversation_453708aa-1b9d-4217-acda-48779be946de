/*
 * @Description: 记录观看次数
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-24 19:06:28
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-12 11:11:05
 */
import { Response, ResponseStatus } from '@hbfe/common'
import MsBusinessNews from '@api/ms-gateway/ms-news-v1'

class MutationRecordNews {
  /**
   * 记录资讯次数
   * @param id 资讯ID
   * @returns ResponseStatus
   */
  async doRecordNews(id: string): Promise<ResponseStatus> {
    const { status } = await MsBusinessNews.browseNews(id)
    return status
  }
}
export default MutationRecordNews
