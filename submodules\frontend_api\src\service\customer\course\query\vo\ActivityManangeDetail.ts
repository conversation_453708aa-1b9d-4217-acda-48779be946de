import {
  LearningExperienceEnum,
  AnswerMethodEnum,
  ApproveMethodEnum,
  ApproveResultEnum,
  ApproveStatusEnum
} from '@api/service/management/activity/enum/ActivityEnum'
import { LearningExperienceActStatusEnum, LearningExperienceJoinStatusEnum } from '../enum/LearningExperienceStatusEnum'
import {
  StudentLearningExperienceResponse,
  StudentLearningExperienceStatus
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

// 详情

class CourseDetail {
  /**
   * 课程id
   */
  id = ''
  /**
   * 课程名称
   */
  name = ''
}
export default class ActivityManangeDetailModel {
  /**
   * 培训方案名称
   */
  schemeName = ''
  /**
   * 培训方案id
   */
  schemeId = ''
  /**
   * 主题名称
   */
  theme = ''
  /**
   * 主题id
   */
  themeId = ''
  /**
   * 学习心得类型 1班级 2课程
   */
  learningExperienceType: LearningExperienceEnum = null
  /**
   * 参加起始时间
   */
  joinStartTime = ''
  /**
   * 参加结束时间
   */
  joinEndTime = ''
  /**
   * 姓名
   */
  name = ''
  /**
   * 证件号
   */
  idCard = ''
  /**
   * 作答方式
   */
  answerMethod: AnswerMethodEnum = null
  /**
   * 审核方式
   */
  approveMethod: ApproveMethodEnum = null
  /**
   * 审核结果
   */
  approveResult: ApproveResultEnum = null
  /**
   * 活动状态
   */
  actStatus: LearningExperienceActStatusEnum = null
  /**
   * 参加状态
   */
  joinStatus: LearningExperienceJoinStatusEnum = null
  /**
   * 内容
   */
  content = ''
  /**
   * 学习心得 文字内容 or 文件地址
   */
  LearningExperience = ''
  /**
   * 学习心得 文件名称
   */
  fileName = ''
  /**
   * 学习心得 字数限制
   */
  wordLimit = 0
  /**
   * 提交时间
   */
  submitTime = ''

  /**
   * 审核状态
   */
  status: ApproveStatusEnum = null
  /**
   * 审核时间
   */
  approveTime = ''
  /**
   * 审核意见
   */
  approveComment = ''
  /**
   * 提交次数
   */
  submitNum: number = null
  /**
   * 课程详情
   */
  courseDetail = new CourseDetail()
  /**
   * 限制大小
   */
  fileSize: number = null
  /**
   * 大纲id
   */
  outLineId = ''
  /**
   * 学员学习心得id
   */
  studentLearningExperienceId = ''
  /**
   * 审核分数
   */
  score = 0
  /**
   * 学号
   */
  studentNo = ''
  /**
   * 学习心得是否已被删除
   */
  isDel = false
  static from(response: StudentLearningExperienceResponse) {
    const vo = new ActivityManangeDetailModel()
    vo.theme = response.learningExperienceTopic?.experienceTopicName
    if (response.experienceType === 'COURSE') {
      vo.learningExperienceType = LearningExperienceEnum.COURSE
    } else if (response.experienceType === 'SCHEME') {
      vo.learningExperienceType = LearningExperienceEnum.CLASS
    }
    vo.joinStartTime = response.learningExperienceTopic?.startTime
    vo.joinEndTime = response.learningExperienceTopic?.endTime
    // 提交要求（根据参与形式不同，表征的意义不同：提交附件表示附件大小限制，在线编辑表示编辑字数限制）
    if (response.learningExperienceTopic?.participateType === 'SUBMIT_FILE') {
      vo.answerMethod = AnswerMethodEnum.UPLOAD
      vo.fileSize = response.learningExperienceTopic?.submitLimitNum
    } else if (response.learningExperienceTopic?.participateType === 'EDIT_ONLINE') {
      vo.answerMethod = AnswerMethodEnum.EDIT
      vo.wordLimit = response.learningExperienceTopic?.submitLimitNum
    }
    if (response.learningExperienceTopic?.auditType === 'AUTO_AUDIT') {
      vo.approveMethod = ApproveMethodEnum.AUTO
    } else if (response.learningExperienceTopic?.auditType === 'MANUAL_AUDIT') {
      vo.approveMethod = ApproveMethodEnum.ARTIFICIAL
    }
    if (response.status === StudentLearningExperienceStatus.PASS) {
      vo.approveResult = ApproveResultEnum.SUCCESS
    } else if (response.status === StudentLearningExperienceStatus.RETURNED) {
      vo.approveResult = ApproveResultEnum.FAIL
    }
    vo.content = response.learningExperienceTopic.descriptionContent
    // 提交内容（如果为文本时，存储关联文本id，如果是附件是一个json结构，包含名称和地址） 有调接口 外部处理
    vo.LearningExperience = response.content
    // vo.fileName = response.content
    if (response.status === StudentLearningExperienceStatus.SUBMITTED) {
      vo.status = ApproveStatusEnum.FAIL
    } else if (
      [StudentLearningExperienceStatus.PASS, StudentLearningExperienceStatus.RETURNED].includes(response.status)
    ) {
      vo.status = ApproveStatusEnum.SUCCESS
    }
    vo.approveComment = response.remark
    vo.submitTime = (
      response.statusChangeTime.find(item => {
        return item.status === StudentLearningExperienceStatus.SUBMITTED
      }) || {}
    ).changeTime
    vo.approveTime = (
      response.statusChangeTime.find(item => {
        return [StudentLearningExperienceStatus.PASS, StudentLearningExperienceStatus.RETURNED].includes(item.status)
      }) || {}
    ).changeTime
    vo.themeId = response.learningExperienceTopic?.topicId
    vo.schemeId = response.studentLearning.schemeId
    vo.studentLearningExperienceId = response.studentLearningExperienceId
    if (new Date(vo.joinStartTime.replace(/-/g, '/')) > new Date()) {
      vo.actStatus = LearningExperienceActStatusEnum.notStart
    } else if (
      new Date(vo.joinStartTime.replace(/-/g, '/')) < new Date() &&
      new Date() < new Date(vo.joinEndTime.replace(/-/g, '/'))
    ) {
      vo.actStatus = LearningExperienceActStatusEnum.inProgress
    } else if (new Date(vo.joinEndTime.replace(/-/g, '/')) < new Date()) {
      vo.actStatus = LearningExperienceActStatusEnum.Ended
    }
    if (['SUBMITTED', 'PASS', 'RETURNED'].includes(response.status)) {
      vo.joinStatus = LearningExperienceJoinStatusEnum.join
    } else {
      vo.joinStatus = LearningExperienceJoinStatusEnum.not_join
    }
    vo.score = response.score
    vo.studentNo = response.studentLearning.studentNo
    vo.isDel = response.learningExperienceTopic.isDelete
    return vo
  }
}
