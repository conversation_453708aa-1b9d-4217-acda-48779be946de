<template>
  <div class="f-p15" v-if="$hasPermission('trainingCert')" desc="培训证明" actions="@Cert,@Seal">
    <el-tabs v-model="activeName" type="card" class="m-tab-card">
      <el-tab-pane label="培训证明" name="cert">
        <template v-if="$hasPermission('cert')" desc="培训证明模板" actions="@Cert">
          <cert></cert>
        </template>
      </el-tab-pane>
      <el-tab-pane label="培训证明电子章配置" name="seal">
        <template v-if="$hasPermission('seal')" desc="电子章配置" actions="@Seal">
          <seal></seal>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import Cert from '@hbfe/jxjy-admin-platform/src/function/components/training-cert-index.vue'
  import Seal from '@hbfe/jxjy-admin-platform/src/function/components/training-cert-seal.vue'

  @Component({
    components: { Cert, Seal }
  })
  export default class extends Vue {
    activeName = 'cert'
  }
</script>
