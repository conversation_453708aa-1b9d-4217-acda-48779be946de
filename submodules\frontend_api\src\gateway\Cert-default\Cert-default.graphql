schema {
	query:Query
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getCertificateTemplatePage(page:Page):CertificateTemplateResponsePage @page(for:"CertificateTemplateResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
type CertificateTemplateResponse @type(value:"com.fjhb.platform.core.certificate.v1.kernel.gateway.graphql.response.CertificateTemplateResponse") {
	"""主键 证书模板ID"""
	id:String
	"""模板名称"""
	name:String
	"""模板路径"""
	temPath:String
	"""创建时间"""
	createTime:DateTime
	"""创建人ID"""
	creatorId:String
	"""备注"""
	remark:String
	"""使用范围"""
	useableRange:String
	"""适用形式"""
	applicableType:String
	"""默认数据"""
	defaultData:String
}

scalar List
type CertificateTemplateResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CertificateTemplateResponse]}
