import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { ResponseStatus } from '@api/Response'
import participatingUnitGateway, { ParticipatingUnitDto, ParticipatingUnitParams } from '@api/gateway/ParticipatingUnit'

export interface IParticipatingUnitState {
  /**
   * 列表
   */
  pageList: Array<ParticipatingUnitDto>
  /**
   * 总页数
   */
  totalSize: number
  /**
   * 分页总数
   */
  totalPageSize: number
  /**
   * 详情
   */
  // detailDto:

  /**
   * 是否存在参训单位
   */
  isParticipatingUnit: boolean
}
@Module({ namespaced: true, dynamic: true, name: 'ManagementParticipatingUnitModule', store })
class ParticipatingUnitModule extends VuexModule implements IParticipatingUnitState {
  pageList = new Array<ParticipatingUnitDto>()
  totalSize = 0
  totalPageSize = 0
  isParticipatingUnit = false

  /**
   * 参训单位分页
   */
  @Action
  async page(params: {
    page: { pageNo: number; pageSize: number }
    params: ParticipatingUnitParams
  }): Promise<ResponseStatus> {
    const response = await participatingUnitGateway.page(params)
    if (response.status.isSuccess()) {
      this.SET_PAGE_LIST(response.data.currentPageData)
      this.SET_TOTAL_PAGE_SIZE(response.data.totalPageSize)
      this.SET_TOTAL_SIZE(response.data.totalSize)
    }
    return response.status
  }

  /**
   * 第三方登录成功后 在培训平台中判断是否为参训单位
   */
  @Action
  async hasParticipatingUnit(token: string): Promise<ResponseStatus> {
    const response = await participatingUnitGateway.hasParticipatingUnit(token)
    this.SET_IS_PARTICIPATING_UNIT(response.data)
    return response.status
  }

  /**
   * 执行同步参训单位的动作
   */
  @Action
  async syncTrainingInstitution(token: string): Promise<ResponseStatus> {
    try {
      const { status } = await participatingUnitGateway.syncTrainingInstitution(token)
      if (!status.isSuccess()) {
        const getBizError = status.errors[0]
        return new ResponseStatus(getBizError.code, getBizError.message)
      }
      return status
    } catch (e) {
      return Promise.resolve(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   * 设置列表
   */
  @Mutation
  private SET_PAGE_LIST(pageList: Array<ParticipatingUnitDto>) {
    this.pageList = pageList
  }

  /**
   * 设置状态对象中的总数
   * @param totalSize
   * @constructor
   */
  @Mutation
  private SET_TOTAL_SIZE(totalSize: number) {
    this.totalSize = totalSize
  }

  /**
   * 设置状态对象中的分页的总页数
   * @param totalPageSize
   * @constructor
   */
  @Mutation
  private SET_TOTAL_PAGE_SIZE(totalPageSize: number) {
    this.totalPageSize = totalPageSize
  }

  @Mutation
  private SET_IS_PARTICIPATING_UNIT(isExist: boolean) {
    this.isParticipatingUnit = isExist
  }
}

export default getModule(ParticipatingUnitModule)
