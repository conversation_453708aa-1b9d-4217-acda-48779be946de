import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { InfoContent } from './models/InfoContent'
import { ResponseStatus } from '@api/Response'
import PlatformNewNotice, {
  CategoryDetailDTO,
  NewsCategoryUpdateDTO,
  NewsDTO,
  NewsNoticeCondition,
  NewsNoticeDetailDTO,
  NewsNoticeDTO,
  Page
} from '@api/gateway/PlatformNewNotice'
import { AssociateUnit } from './models/AssociateUnit'
import platformUserGateway, { UserParamDTO } from '@api/gateway/PlatformUser'
// import { TeachUnitBaseDTO } from '@api/gateway/PlatformBasicData'
import { Code } from '@api/service/common/models/Code'
import { CategoryDetail } from '@api/service/common/models/info-content/CategoryDetail'

/**
 * 试题统计全局的state
 */
export interface IInfoContentState {
  /**
   * 总页数
   */
  totalSize: number
  /**
   * 分页总数
   */
  totalPageSize: number
  /**
   * 资讯分页内容
   */
  infoContentList: Array<InfoContent>
  /**
   * 资讯详情
   */
  infoContent: InfoContent
  /**
   * 资讯分类详情
   */
  categoryList: Array<CategoryDetail>
  /**
   * 子分类
   */
  subCategoryList: Array<CategoryDetail>
  /**
   * 分类名称是否重复
   */
  categoryNameExist: boolean
}

@Module({ namespaced: true, store, dynamic: true, name: 'ManagementInfoContentModule' })
class InfoContentModule extends VuexModule implements IInfoContentState {
  /**
   * 分页总数
   */
  totalPageSize = 0
  /**
   * 总页数
   */
  totalSize = 0
  /**
   * 资讯分页内容
   */
  infoContentList = new Array<InfoContent>()
  /**
   * 资讯详情
   */
  infoContent = new InfoContent()
  /**
   * 资讯分类详情
   */
  categoryList = new Array<CategoryDetail>()
  /**
   * 子分类
   */
  subCategoryList = new Array<CategoryDetail>()
  /**
   * 顶级分类
   */
  rootCategoryList: Array<CategoryDetailDTO> = new Array<CategoryDetailDTO>()
  /**
   * 分类名称是否重复
   * true:重复；false:不重复
   */
  categoryNameExist = true

  /**
   * 初始化顶级分类
   */
  @Action
  async initCategory() {
    const res = await PlatformNewNotice.listSubCategoryByParentId('0')
    if (res.status.isSuccess()) {
      this.SET_ROOT_CATEGORY(res.data)
    }
    return res.status
  }

  /**
   * 资讯创建
   * @param request
   */
  @Action
  async create(request: NewsDTO) {
    const res = await PlatformNewNotice.create(request)
    return res.status
  }

  /**
   * 资讯更新
   * @param request
   */
  @Action
  async update(request: NewsDTO) {
    const res = await PlatformNewNotice.update(request)
    return res.status
  }

  /**
   * 资讯删除
   * @param id
   */
  @Action
  async deleteById(id: string) {
    const res = await PlatformNewNotice.deleteById(id)
    return res.status
  }

  /**
   * 将发布状态或者定时发布状态的资讯置为草稿
   * @param id
   */
  @Action
  async toDraft(id: string) {
    const res = await PlatformNewNotice.toDraft(id)
    return res.status
  }

  /**
   * 获取所有的叶子节点分类
   */
  get getAllLeafCategory() {
    const list = new Array<CategoryDetail>()
    this.categoryList.map(p => {
      const subList = this.categoryList.find(s => p.id === s.parentId)
      if (!subList) {
        list.push(p)
      }
    })
    return list
  }

  /**
   * 获取资讯分页
   * @param params
   */
  @Action
  async page(params: { page?: Page; queryParam?: NewsNoticeCondition }) {
    const response = await PlatformNewNotice.pageNewsNotice(params)
    if (response.status.isSuccess()) {
      const userIds = new Array<string>()
      response.data.currentPageData.forEach(r => {
        userIds.push(r.publishPersonId)
      })
      const param = new UserParamDTO()
      param.userIdList = userIds
      const { status, data } = await platformUserGateway.listUserInfo(param)
      if (status.isSuccess()) {
        response.data.currentPageData.forEach(r => {
          const userSimpleInfoDTO = data.find(u => u.userId === r.publishPersonId)
          if (userSimpleInfoDTO) {
            r.publishPersonId = userSimpleInfoDTO.nickName
          }
        })
      }
    }
    this.SET_PAGE_LIST(response.data.currentPageData)
    this.SET_TOTAL_PAGE_SIZE(response.data.totalPageSize)
    this.SET_TOTAL_SIZE(response.data.totalSize)

    if (this.infoContentList.length) {
      const categoryRes = await this.listAllCategory()
      if (categoryRes.isSuccess()) {
        this.APPEND_CATEGORY_NAME({ list: this.categoryList, requestType: 1 })
      }
      const unitList = new Array<string>()

      this.infoContentList.map(p =>
        p.associateUnitList.map(s => {
          unitList.push(s.associateUnitId)
        })
      )
      // const unitRes = await PlatformBasicData.findTeachUnitListByIds(unitList)
      // if (unitRes.status.isSuccess()) {
      //   this.APPEND_UNIT_NAME({ list: unitRes.data, requestType: 1 })
      // }
      //todo 弃用该方法，弃用整个状态层，请改用机构服务商接口
    }
    return new ResponseStatus(Code.SUCCESS, '')
  }

  /**
   * 获取所有的资讯分类
   */
  @Action
  async listAllCategory() {
    const res = await PlatformNewNotice.listAllCategory()
    if (!res.status.isSuccess()) {
      return res.status
    }
    this.SET_CATEGORY_LIST(res.data)
    return res.status
  }

  /**
   * 资讯详情
   * @param id
   */
  @Action
  async getNewsNotice(id: string) {
    const res = await PlatformNewNotice.getNewsNotice(id)
    if (!res.status.isSuccess()) {
      return res.status
    }
    this.SET_NEWS_NOTICE_DETAIL(res.data)

    const categoryRes = await this.listAllCategory()
    if (categoryRes.isSuccess()) {
      this.APPEND_CATEGORY_NAME({ list: this.categoryList, requestType: 2 })
    }
    const unitList = new Array<string>()

    this.infoContentList.map(p =>
      p.associateUnitList.map(s => {
        unitList.push(s.associateUnitId)
      })
    )
    // const unitRes = await PlatformBasicData.findTeachUnitListByIds(unitList)
    // if (unitRes.status.isSuccess()) {
    //   this.APPEND_UNIT_NAME({ list: unitRes.data, requestType: 2 })
    // }
    //todo 弃用该方法，弃用整个状态层，请改用机构服务商接口
    return res.status
  }

  /**
   * 获取子分类
   * @param parentId
   */
  @Action
  async listSubCategoryByParentId(parentId: string) {
    const res = await PlatformNewNotice.listSubCategoryByParentId(parentId)
    if (!res.status.isSuccess()) {
      return res.status
    }
    this.SET_SUB_CATEGORY_LIST(res.data)
    return res.status
  }

  /**
   * 判断是否可以发布
   * @param id
   */
  @Action
  async checkPublishAble(id: string) {
    const res = await PlatformNewNotice.checkPublishAble(id)
    return res
  }

  /**
   * 发布草稿状态的资讯
   * @param id
   */
  @Action
  async publish(id: string) {
    const res = await PlatformNewNotice.publish(id)
    return res.status
  }

  /**
   * 检查分类名称是否重复
   * @param name
   */
  @Action
  async checkCategoryNameExist(name: string) {
    const res = await PlatformNewNotice.checkCategoryNameExist(name)
    if (res.status.isSuccess()) {
      this.SET_CATEGORY_NAME_EXIST(res.data)
    }
    return res.status
  }

  /**
   * 创建/修改资讯类别
   * @param dto
   */
  @Action
  async updateNewsCategory(dto: NewsCategoryUpdateDTO) {
    const res = await PlatformNewNotice.updateNewsCategory(dto)
    return res.status
  }

  /**
   * 删除资讯类别
   * @param id
   */
  @Action
  async deleteNewsCategory(id: string) {
    const res = await PlatformNewNotice.deleteNewsCategory(id)
    return res.status
  }

  /**
   * 设置资讯分页数据
   * @param list
   * @constructor
   */
  @Mutation
  SET_PAGE_LIST(list: Array<NewsNoticeDTO>) {
    this.infoContentList = new Array<InfoContent>()
    list.map(p => {
      const item = new InfoContent()
      item.id = p.id
      item.title = p.title
      item.status = p.status
      item.associateUnitList = new Array<AssociateUnit>()
      item.publishTime = p.publishTime
      item.publishPersonId = p.publishPersonId
      if (p.associateUnitList && p.associateUnitList.length) {
        item.associateUnitList = p.associateUnitList.map(p => {
          const item = new AssociateUnit()
          item.associateUnitId = p.associateUnitId
          item.categoryId = p.categoryId
          return item
        })
      }
      item.regionIds = p.regionIds
      item.pop = p.pop
      this.infoContentList.push(item)
    })
  }

  @Mutation
  APPEND_CATEGORY_NAME(param: { list: Array<CategoryDetail>; requestType: number }) {
    if (param.requestType === 1) {
      this.infoContentList = this.infoContentList.map(p => {
        p.associateUnitList.forEach(i => {
          i.category = param.list.find(c => c.id === i.categoryId)?.name || '-'
        })
        return p
      })
    } else {
      this.infoContent.associateUnitList.forEach(p => {
        p.category = param.list.find(c => c.id === p.categoryId)?.name || '-'
      })
    }
  }

  // @Mutation
  // APPEND_UNIT_NAME(param: { list: Array<TeachUnitBaseDTO>; requestType: number }) {
  //   if (param.requestType === 1) {
  //     this.infoContentList = this.infoContentList.map(p => {
  //       p.associateUnitList.forEach(i => {
  //         i.associateUnit = param.list.find(t => t.unitId === i.associateUnitId)?.name || '-'
  //       })
  //       return p
  //     })
  //   } else {
  //     this.infoContent.associateUnitList.forEach(p => {
  //       p.associateUnit = param.list.find(t => t.unitId === p.associateUnitId)?.name || '-'
  //     })
  //   }
  // }

  /**
   * 设置状态对象中的总数
   * @param totalSize
   * @constructor
   */
  @Mutation
  private SET_TOTAL_SIZE(totalSize: number) {
    this.totalSize = totalSize
  }

  /**
   * 设置状态对象中的分页的总页数
   * @param totalPageSize
   * @constructor
   */
  @Mutation
  private SET_TOTAL_PAGE_SIZE(totalPageSize: number) {
    this.totalPageSize = totalPageSize
  }

  /**
   * 设置资讯详情
   * @param item
   * @constructor
   */
  @Mutation
  private SET_NEWS_NOTICE_DETAIL(item: NewsNoticeDetailDTO) {
    this.infoContent = new InfoContent()
    this.infoContent.id = item.id
    this.infoContent.title = item.title
    this.infoContent.popStartTime = item.popStart
    this.infoContent.popEndTime = item.popOver
    this.infoContent.content = item.content
    this.infoContent.origin = item.origin
    this.infoContent.imageUrl = item.photoUrl
    this.infoContent.publishTime = item.publishTime
    this.infoContent.pop = item.type === 2
    this.infoContent.status = item.status
    this.infoContent.isImportance = item.isImportance
    this.infoContent.popStartTime = item.popStart
    this.infoContent.popEndTime = item.popOver
    this.infoContent.publishPersonId = item.publishPersonId
    this.infoContent.regionIds = item.regionIds

    if (item.associateUnitList) {
      this.infoContent.associateUnitList = item.associateUnitList.map(p => {
        const item = new AssociateUnit()
        item.associateUnitId = p.associateUnitId
        item.categoryId = p.categoryId
        return item
      })
    } else {
      this.infoContent.associateUnitList = new Array<AssociateUnit>()
    }
  }

  @Mutation
  private SET_CATEGORY_LIST(list: Array<CategoryDetailDTO>) {
    this.categoryList = new Array<CategoryDetail>()
    list.map(p => {
      const item = new CategoryDetail()
      Object.assign(item, p)
      if (p.parentId) {
        const parent = list.find(s => s.id === p.parentId)
        item.parentName = parent?.name || ''
      }
      this.categoryList.push(item)
    })
  }

  @Mutation
  private SET_SUB_CATEGORY_LIST(list: Array<CategoryDetailDTO>) {
    this.subCategoryList = new Array<CategoryDetail>()
    list.map(p => {
      const item = new CategoryDetail()
      Object.assign(item, p)
      this.subCategoryList.push(item)
    })
  }

  @Mutation
  private SET_CATEGORY_NAME_EXIST(categoryNameExist: boolean) {
    this.categoryNameExist = categoryNameExist
  }

  @Mutation
  private SET_ROOT_CATEGORY(rootCategoryList: Array<CategoryDetailDTO>) {
    this.rootCategoryList = rootCategoryList
  }
}

export default getModule(InfoContentModule)
