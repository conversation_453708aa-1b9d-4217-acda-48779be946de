import exportBatchOrder from './queries/exportBatchOrder.graphql'
import exportBatchRefundOrder from './queries/exportBatchRefundOrder.graphql'
import exportOrder from './queries/exportOrder.graphql'
import exportOrderForChannelVendor from './queries/exportOrderForChannelVendor.graphql'
import exportOrderForParticipatingUnit from './queries/exportOrderForParticipatingUnit.graphql'
import exportOrderForTrainingInstitution from './queries/exportOrderForTrainingInstitution.graphql'
import exportReconciliation from './queries/exportReconciliation.graphql'
import exportReconciliationForChannelVendor from './queries/exportReconciliationForChannelVendor.graphql'
import exportReconciliationForTrainingInstitution from './queries/exportReconciliationForTrainingInstitution.graphql'
import exportRefundOrder from './queries/exportRefundOrder.graphql'
import findAllChildChapterByMajor from './queries/findAllChildChapterByMajor.graphql'
import findAllChildChapterByMajorList from './queries/findAllChildChapterByMajorList.graphql'
import findAllIndustryRelationList from './queries/findAllIndustryRelationList.graphql'
import getBatchRefundStatistics from './queries/getBatchRefundStatistics.graphql'
import getIssueByCommoditySkuId from './queries/getIssueByCommoditySkuId.graphql'
import getMyOrder from './queries/getMyOrder.graphql'
import getOrder from './queries/getOrder.graphql'
import getPreExamLSById from './queries/getPreExamLSById.graphql'
import getRefundOrder from './queries/getRefundOrder.graphql'
import lazyBatchGetOrder from './queries/lazyBatchGetOrder.graphql'
import lazyBatchGetRefundOrder from './queries/lazyBatchGetRefundOrder.graphql'
import lazyBatchOrderStatistics from './queries/lazyBatchOrderStatistics.graphql'
import lazyBatchPageOrder from './queries/lazyBatchPageOrder.graphql'
import lazyBatchPageRefundOrder from './queries/lazyBatchPageRefundOrder.graphql'
import listIssueByCommoditySkuIds from './queries/listIssueByCommoditySkuIds.graphql'
import listIssueBySchemeId from './queries/listIssueBySchemeId.graphql'
import listPreExamLSCourse from './queries/listPreExamLSCourse.graphql'
import listYear from './queries/listYear.graphql'
import pageForChannelVendor from './queries/pageForChannelVendor.graphql'
import pageForParticipatingUnit from './queries/pageForParticipatingUnit.graphql'
import pageForTrainingInstitution from './queries/pageForTrainingInstitution.graphql'
import pageMyOrder from './queries/pageMyOrder.graphql'
import pageOrder from './queries/pageOrder.graphql'
import pagePutawayPreExamLS from './queries/pagePutawayPreExamLS.graphql'
import pageRefundOrder from './queries/pageRefundOrder.graphql'
import statisticsOrder from './queries/statisticsOrder.graphql'
import statisticsOrderForChannelVendor from './queries/statisticsOrderForChannelVendor.graphql'
import statisticsOrderForParticipatingUnit from './queries/statisticsOrderForParticipatingUnit.graphql'
import statisticsOrderForTrainingInstitution from './queries/statisticsOrderForTrainingInstitution.graphql'
import statisticsRefundOrder from './queries/statisticsRefundOrder.graphql'

export {
  exportBatchOrder,
  exportBatchRefundOrder,
  exportOrder,
  exportOrderForChannelVendor,
  exportOrderForParticipatingUnit,
  exportOrderForTrainingInstitution,
  exportReconciliation,
  exportReconciliationForChannelVendor,
  exportReconciliationForTrainingInstitution,
  exportRefundOrder,
  findAllChildChapterByMajor,
  findAllChildChapterByMajorList,
  findAllIndustryRelationList,
  getBatchRefundStatistics,
  getIssueByCommoditySkuId,
  getMyOrder,
  getOrder,
  getPreExamLSById,
  getRefundOrder,
  lazyBatchGetOrder,
  lazyBatchGetRefundOrder,
  lazyBatchOrderStatistics,
  lazyBatchPageOrder,
  lazyBatchPageRefundOrder,
  listIssueByCommoditySkuIds,
  listIssueBySchemeId,
  listPreExamLSCourse,
  listYear,
  pageForChannelVendor,
  pageForParticipatingUnit,
  pageForTrainingInstitution,
  pageMyOrder,
  pageOrder,
  pagePutawayPreExamLS,
  pageRefundOrder,
  statisticsOrder,
  statisticsOrderForChannelVendor,
  statisticsOrderForParticipatingUnit,
  statisticsOrderForTrainingInstitution,
  statisticsRefundOrder
}
