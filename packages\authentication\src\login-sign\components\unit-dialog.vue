<template>
  <el-dialog
    title="请选择您要管理的单位，进入管理后仍可以切换："
    :show-close="false"
    :visible.sync="isShow"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="650px"
    class="m-dialog"
  >
    <div class="company-list scrollable-container">
      <div class="item" v-for="unit in unitModel" :key="unit.unitId" @click="selectUnit(unit)">
        <i class="icon el-icon-office-building"></i>
        <span class="txt">{{ unit.name }}</span>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Mixins } from 'vue-property-decorator'
  import Authentication from '@api/service/common/authentication/Authentication'
  import DistributionUnitInformation from '@api/service/management/user/query/manager/vo/DistributionUnitInformation'
  import UnitDialog from '@hbfe/jxjy-admin-authentication/src/login-sign/components/unit-dialog'

  @Component
  export default class extends Mixins(UnitDialog) {}
</script>
<style scoped>
  .scrollable-container {
    max-height: 400px; /* 根据需要调整最大高度 */
    overflow-y: auto;
  }
</style>
