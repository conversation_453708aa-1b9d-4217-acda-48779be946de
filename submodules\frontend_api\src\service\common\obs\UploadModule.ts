/**
 * 上传文件模型
 */
export class UpLoadFile {
  /**
   * multiple的文件，不知道是啥事格式，将就一下 直接由组件内部上传 在这边不做复制操作
   */
  readonly file: any
  /**
   * 上传文件需要的token
   */
  uploadToken: string
  /**
   * 大小 目前单位为：字节  后续根据接口做改变
   */
  size?: number
  //  ***-*** 以下为大文件需要传输的内容 ***-***
  /**
   * 大文件总切片数量
   */
  chunks?: number
  /**
   * 当前切片
   */
  chunk?: number
  /**
   * 此次切片大小
   */
  chunkSize?: number
  /**
   * MD5
   */
  md5?: string
  /**
   * 文件ID 如果是大文件，只有第一次不需要传值，后续要根据第一次值做赋值
   */
  fileResourceId?: string
}
/**
 * 上传Base64图片模型
 */
export class UpLoadBase64 {
  /**
   * 文件名
   */
  fileName: string
  /**
   * Base64
   */
  base64Data: string
  /**
   * 上传token
   */
  uploadToken: string
}
