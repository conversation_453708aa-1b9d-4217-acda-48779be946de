import IdCardTypeVo from '@api/service/common/basic-data-dictionary/query/vo/IdCardTypeVo'
import BasicData, { BusinessDataDictionaryRequest } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import { Response } from '@hbfe/common'
import IdRegisterCardTypeVo from '@api/service/common/basic-data-dictionary/query/vo/IdRegisterCardTypeVo'
import ServicerSeriesV1Gateway from '@api/ms-gateway/ms-servicer-series-v1'

class QueryIdCardType {
  /**
   * 身份证类型映射缓存
   */
  private idCardTypeMap = new Map<string, Array<IdCardTypeVo>>()

  /**
   * 查询证件类型
   */
  async queryIdCardType(): Promise<IdCardTypeVo[]> {
    if (!this.idCardTypeMap.has('ID_CARD_TYPE')) {
      const request = new BusinessDataDictionaryRequest()
      request.businessDataDictionaryType = 'ID_CARD_TYPE'
      const res = await BasicData.listBusinessDataDictionaryInSubProject(request)
      this.idCardTypeMap.set('ID_CARD_TYPE', res.data.map(IdCardTypeVo.from))
    }
    return this.idCardTypeMap.get('ID_CARD_TYPE')
  }
  /**
   * 查询证件类型可用项（学员用）
   */
  async queryRegisterSettingFormIdCardTypeList() {
    const result = new Response<IdRegisterCardTypeVo[]>()
    const idCardRes = await ServicerSeriesV1Gateway.getStudentRegisterFormIdCardTypeList()
    result.status = idCardRes.status
    if (result.status.isSuccess()) {
      const data = await this.queryIdCardType()
      result.data =
        idCardRes.data?.responseList
          ?.map(item => IdRegisterCardTypeVo.fromRegister(item, data))
          ?.filter(item => item.select) || []
    }
    return result
  }
}
export default new QueryIdCardType()
