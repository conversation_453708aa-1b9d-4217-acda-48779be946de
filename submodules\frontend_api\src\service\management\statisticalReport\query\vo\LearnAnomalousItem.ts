import { ResponseStatus } from '@hbfe/common'
import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
import StudentLearning, {
  LearningResultErrorResponse
} from '@api/platform-gateway/student-course-learning-query-back-gateway'
export default class LearnAnomalousItem {
  /**
   * 姓名
   */
  name = ''
  /**
   * 证件号
   */
  idCard = ''
  /**
   * 手机号
   */
  phone = ''
  /**
   * 规则ID
   */
  ruleId = ''
  /**
   * 学号
   */
  studentNo = ''
  /**
   * 培训方案id
   */
  schemeId = ''
  /**
   * 方案名称
   */
  schemeName = ''
  /**
   *班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuProperty = new SkuPropertyResponseVo()
  /**
   * 操作时间
   */
  operationTime = ''
  /**
   * 失败原因
   */
  failReason = ''
  static from(dto: LearningResultErrorResponse) {
    const vo = new LearnAnomalousItem()
    vo.name = dto.userName
    vo.idCard = dto.userIdCard
    vo.phone = dto.userPhone
    vo.ruleId = dto.ruleId
    vo.schemeName = dto.trainingName
    vo.operationTime = dto.operateTime
    vo.failReason = dto.errorMsg
    vo.studentNo = dto.studentNo
    return vo
  }

  /**
   * 重新计算
   */
  async reCalculate() {
    const res = await StudentLearning.reGenerateStudentTrainingResultSimulateInServicer([this.studentNo])
    if (res.status.isSuccess()) {
      return new ResponseStatus(200, res.data)
    }
    return res.status
  }
}
