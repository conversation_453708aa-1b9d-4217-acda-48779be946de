<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 09:15:13
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-08-22 17:05:12
 * @Description: 培训监管-监管规则tab
-->
<template>
  <div>
    <!--表格-->
    <el-table
      stripe
      :data="learningRuleList"
      max-height="500px"
      class="m-table"
      ref="learningRules"
      v-loading="loading"
    >
      <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
      <el-table-column label="适用范围" min-width="150">
        <template slot-scope="scope">
          <div v-if="scope.row.basicInfo.applyRange == ApplyRangeEnum.scheme">
            培训方案级别<el-button
              type="text"
              class="f-cb f-ml5 f-fb"
              @click="openSchemeList(scope.row.basicInfo.schemeIds)"
              >( {{ scope.row.basicInfo.schemeIds.length }} )</el-button
            >
          </div>
          <div v-else-if="scope.row.basicInfo.applyRange == ApplyRangeEnum.region">
            地区级别<el-button type="text" class="f-cb f-ml5 f-fb" @click="openAreaList(scope.row.basicInfo)"
              >( {{ scope.row.basicInfo.regionIds.length }} )</el-button
            >
          </div>
          <div v-else>平台级别</div>
        </template>
      </el-table-column>
      <el-table-column label="适用行业" min-width="130">
        <template slot-scope="scope"> {{ scope.row.basicInfo.industryName }} </template>
      </el-table-column>
      <el-table-column label="指定年度" min-width="150">
        <template slot-scope="scope"> {{ getYearList(scope.row.basicInfo.yearTrainRangeList) }} </template>
      </el-table-column>
      <el-table-column label="状态" min-width="100">
        <template slot-scope="scope">
          <div v-if="scope.row.enable == true">
            <el-badge is-dot type="success" class="badge-status">开启</el-badge>
          </div>
          <div v-else>
            <el-badge is-dot type="info" class="badge-status">关闭</el-badge>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" min-width="150">
        <template slot-scope="scope">
          {{ scope.row.updateTime }}
        </template>
      </el-table-column>
      <el-table-column label="操作人" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.operator }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" align="center" fixed="right">
        <template slot-scope="scope">
          <div>
            <el-button type="text" size="mini" @click="startOrStop('停用', scope.row)" v-if="scope.row.enable"
              >停用</el-button
            >
            <el-button type="text" size="mini" @click="startOrStop('启用', scope.row)" v-else>启用</el-button>
            <el-button type="text" size="mini" @click="goDetail(scope.row.id)">详情</el-button>
            <el-button type="text" size="mini" @click="editBaseConfig(scope.row.id)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="page" v-bind="page"> </hb-pagination>
    <!-- 培训列表抽屉 -->
    <training-list
      ref="trainingListRef"
      :open-training-list.sync="openTrainingList"
      :title="titleName"
      :page-title="pagTitle"
    ></training-list>
    <!-- 地区列表抽屉 -->
    <region-dialog ref="regionDialogRef"></region-dialog>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import TrainingList from '@hbfe/jxjy-admin-platform/src/function/components/training-list.vue'
  import RegionDialog from '@hbfe/jxjy-admin-platform/src/function/components/region-dialog.vue'
  import LearningRuleList from '@api/service/management/learning-rule/LearningRuleList'
  import LearningRuleItem from '@api/service/management/learning-rule/LearningRuleItem'
  import BasicInfo, { TrainingTimeRange } from '@api/service/management/learning-rule/model/BasicInfo'
  import { UiPage } from '@hbfe/common'
  import { ApplyRangeEnum } from '@api/service/management/learning-rule/enum/ApplyRangeEnum'

  @Component({
    components: {
      TrainingList,
      RegionDialog
    }
  })
  export default class extends Vue {
    @Ref('trainingListRef') trainingListRef: TrainingList
    @Ref('regionDialogRef') regionDialogRef: RegionDialog
    constructor() {
      super()
      this.page = new UiPage(this.queryList, this.queryList)
    }
    // 是否打开培训方案列表抽屉
    openTrainingList = false
    // title名称
    titleName = '查看学习规则的方案'
    // 学习规则模型
    learningRuleForm = new LearningRuleList()
    // 学习规则列表
    learningRuleList: Array<LearningRuleItem> = []
    // 加载
    loading = false
    // 分页
    page: UiPage
    // 适用范围枚举
    ApplyRangeEnum = ApplyRangeEnum
    // 页面名称
    pagTitle = 'LearningRulesList'
    async activated() {
      setTimeout(() => {
        this.page.currentChange(1)
      }, 200)
    }
    async created() {
      setTimeout(() => {
        this.page.currentChange(1)
      }, 200)
    }

    async doSearch() {
      this.page.currentChange(1)
      this.queryList()
    }
    async queryList() {
      this.loading = true
      await this.learningRuleForm.queryLearningRuleList(this.page)
      await this.learningRuleForm.queryOperationUser()
      this.learningRuleList = this.learningRuleForm.learningRuleList
      console.log(this.learningRuleList, 'learningRuleList')
      this.loading = false
      ;(this.$refs['learningRules'] as any)?.doLayout()
    }

    // 打开方案列表
    async openSchemeList(schemeIds: Array<string>) {
      this.trainingListRef.schemeIds = schemeIds
      this.trainingListRef.querySchemeList()
      this.openTrainingList = true
    }
    // 打开地区列表
    openAreaList(row?: BasicInfo) {
      this.regionDialogRef.init(row)
    }
    // 启停用
    async startOrStop(title: string, row: LearningRuleItem) {
      this.$confirm(`确认${title}规则吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(async () => {
        await row.changeStatus()
        await this.queryList()
      })
    }
    // 修改基础配置
    editBaseConfig(id: string) {
      this.$router.push(`/basic-data/platform/function/learningRules/modify/` + id)
    }

    // 去详情
    goDetail(id: string) {
      this.$router.push('/basic-data/platform/function/learningRules/detail/' + id)
    }

    // 获取年度
    getYearList(row: Array<TrainingTimeRange>) {
      return row.map((item) => item.year).join('、')
    }
  }
</script>
