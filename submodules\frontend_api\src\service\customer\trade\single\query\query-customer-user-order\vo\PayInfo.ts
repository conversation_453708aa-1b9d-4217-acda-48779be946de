import ITrainingVouchersPayInfo from '@api/service/customer/trade/single/query/query-customer-user-order/interfaces/btpx/ITrainingVouchersPayInfo'
import { OrderDTO as GqlOrderDto } from '@api/gateway/PlatformTrade'
/**
 * 支付基本信息
 */
export default class PayInfo implements ITrainingVouchersPayInfo {
  /**
   * 交易流水号
   */
  payFlowNo: string

  /**
   * 发券机构（属地人社局）（培训券的信息）
   */
  publishOrgName: string
  /**
   * 支付渠道名称 微信支付、支付宝支付 培训券支付
   */
  payChannelName: string
  /**
   * 支付渠道Id 微信支付、支付宝支付 TRAINING_VOUCHER
   */
  payChannelId: string
  /**
   * 培训券号
   */
  couponCode: string
}

export class PayInfoParse {
  // 不允许NEW
  private constructor() {
    // noop
  }
  static parsePayInfo(gqlOrderDto: GqlOrderDto): PayInfo {
    const payInfo = new PayInfo()
    payInfo.couponCode = gqlOrderDto.couponCode
    payInfo.payChannelId = gqlOrderDto.payChannelId
    payInfo.payChannelName = gqlOrderDto.payChannelName
    payInfo.payFlowNo = gqlOrderDto.payFlowNo
    payInfo.publishOrgName = gqlOrderDto.publishOrgName
    return payInfo
  }
}
