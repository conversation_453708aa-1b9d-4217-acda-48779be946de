<template>
  <el-card shadow="never" class="m-card f-mb15 is-header-sticky">
    <div slot="header" class="">
      <span class="tit-txt">基础信息</span>
    </div>
    <!-- <el-collapse v-model="activeNames" accordion>
      <el-collapse-item name="1" class="m-collapse-item"> -->
    <div class="f-plr20 f-pt40">
      <el-row type="flex" justify="center" class="width-limit">
        <el-col :md="20" :lg="16" :xl="13">
          <el-form ref="baseForm" :model="baseData" label-width="auto" class="m-form" :rules="rules">
            <el-form-item label="网校平台名称：" prop="schoolName">
              <el-input
                v-model="baseData.schoolName"
                clearable
                placeholder="请输入网校平台名称，网校名称会同步显示给学员"
              />
            </el-form-item>
            <el-form-item label="服务地区：" prop="areaType">
              <el-radio v-model="areaType" label="1" border class="f-mr10" @change="handleAllArea">全国范围</el-radio>
              <el-radio v-model="areaType" label="2" border class="f-mr10" @change="handleAllArea">选择地区</el-radio>
              <span class="f-co">注：选择的网校开展培训的地区范围</span>
              <div v-show="areaType == 2" class="f-mt10">
                <service-area
                  @getArea="getArea"
                  :options="Areaoptions"
                  :serviceRegionCodesList="baseData.serviceRegionCodes"
                ></service-area>
              </div>
            </el-form-item>
            <el-form-item label="培训行业：" class="is-required" prop="haveRSIndustry">
              <el-checkbox v-model="baseData.haveRSIndustry" border class="f-mr10" @change="getIndutry"
                >人社行业
              </el-checkbox>
              <el-checkbox v-model="baseData.haveJSIndustry" border class="f-mr10" @change="getIndutry"
                >建设行业
              </el-checkbox>
              <el-checkbox v-model="baseData.haveWSIndustry" border class="f-mr10" @change="getIndutry"
                >职业卫生行业
              </el-checkbox>
              <el-checkbox v-model="baseData.haveGQIndustry" border class="f-mr10" @change="getIndutry"
                >工勤行业
              </el-checkbox>
              <el-checkbox v-model="baseData.haveLSIndustry" border class="f-mr10" @change="getIndutry"
                >教师行业
              </el-checkbox>
              <el-checkbox
                style="margin: 10px 0 0 0"
                v-model="baseData.haveYSIndustry"
                border
                class="f-mr10"
                @change="getIndutry"
                >药师行业
              </el-checkbox>
              <!-- <el-radio v-model="radio1" label="1" border class="f-mr10">人社行业</el-radio>
                      <el-radio v-model="radio1" label="2" border class="f-mr10">建设行业</el-radio> -->
            </el-form-item>
            <el-form-item label="业务属性：" required>
              <el-form ref="form" label-width="auto" labelPosition="top" class="m-form pb0 bg-gray f-pt5 f-pl15 f-pr15">
                <div class="f-co">
                  <i class="el-icon-warning f-f16 f-mr5 f-vm"></i
                  >请设置培训行业对应的业务属性，需要配置公共的业务属性值和行业属性值
                </div>
                <el-form-item label="公共业务属性：" prop="industryYears">
                  <!--竖式表格-->
                  <div class="info-table">
                    <div class="info-row col-merge">
                      <div class="info-th f-tl">年度</div>
                      <div class="info-td p0">
                        <div class="m-radio-border-list">
                          <el-checkbox-group v-model="baseData.industryYears">
                            <el-checkbox v-for="item in yearList" :key="item.id" :label="item.id" border
                              >{{ item.year }}年
                            </el-checkbox>
                          </el-checkbox-group>
                        </div>
                      </div>
                    </div>
                    <div class="info-row col-merge">
                      <div class="info-th f-tl">
                        <span class="f-fb">地区</span>
                      </div>
                      <div class="info-td p0">
                        <div v-if="baseData.serviceRegionCodes.length" class="m-city-btn-list">
                          <el-tooltip placement="top" effect="light" v-for="item in areaList" :key="item.id">
                            <div slot="content">
                              <el-cascader-panel
                                :options="item.children"
                                class="m-cascader-noborder"
                                :props="props"
                              ></el-cascader-panel>
                            </div>
                            <el-button type="primary">{{ item.name }}<i class="el-icon-arrow-right"></i></el-button>
                          </el-tooltip>
                        </div>
                        <div v-else>请先选择服务地区</div>
                      </div>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="行业培训属性：" required prop="">
                  <!--竖式表格-->
                  <div class="info-table f-mb15">
                    <div class="info-row col-merge">
                      <div class="info-th f-tl">行业属性</div>
                      <div class="info-td">
                        <div v-for="item in industryChooseList" :key="item">
                          <el-button class="f-ml15" type="text" @click="handleDrawer(item)">
                            {{ item }}
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-form>
            </el-form-item>
            <el-form-item
              v-if="
                baseData.haveRSIndustry ||
                baseData.haveJSIndustry ||
                baseData.haveWSIndustry ||
                baseData.haveGQIndustry ||
                baseData.haveLSIndustry ||
                baseData.haveYSIndustry
              "
              label="人员行业属性："
              required
              prop="industryYears"
            >
              <el-form ref="form" label-width="auto" labelPosition="top" class="m-form pb0 bg-gray f-pt5 f-pl15 f-pr15">
                <!--竖式表格-->
                <div class="info-table f-mb15">
                  <div class="info-row col-merge">
                    <div class="info-th f-tl">行业属性</div>
                    <div class="info-td">
                      <el-button
                        v-for="item in industryPersonnelChooseList"
                        :key="item"
                        @click="handlePersonnelDrawer(item)"
                        class="f-ml15"
                        type="text"
                        style="margin-bottom: 10px"
                      >
                        {{ item }}
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-form>
            </el-form-item>
            <el-form-item label="业主单位全称：" class="is-required" prop="ownerUnitName">
              <el-input v-model="baseData.ownerUnitName" clearable placeholder="请输入业主单位全称" class="form-l" />
              <el-checkbox v-model="haveownerUnitSingleName" class="f-ml10">配置简称</el-checkbox>
            </el-form-item>
            <el-form-item v-if="haveownerUnitSingleName" label="单位简称：" prop="ownerUnitSingleName">
              <el-input
                v-model="baseData.ownerUnitSingleName"
                clearable
                placeholder="请在此输入单位简称"
                class="form-l"
              />
            </el-form-item>
            <el-form-item label="业主负责人：" prop="ownerCharge">
              <el-input
                v-model="baseData.ownerCharge"
                clearable
                placeholder="请输入业主单位负责人姓名"
                class="form-l"
              />
            </el-form-item>
            <el-form-item label="手机号：" prop="phone">
              <el-input v-model="baseData.phone" clearable placeholder="请输入业主单位负责人手机号" class="form-l" />
            </el-form-item>
            <el-form-item label="网校性质：" required prop="schoolModel">
              <el-radio-group v-model="baseData.schoolModel">
                <el-radio :label="1" border class="f-mr10">正式实施</el-radio>
                <el-radio :label="2" border class="f-mr10">DEMO</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="合同签订情况：" prop="contractSigning">
              <el-radio-group v-model="baseData.contractSigning">
                <el-radio :label="true" border class="f-mr10">已签约</el-radio>
                <el-radio :label="false" border class="f-mr10">未签约</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="baseData.contractSigning" label="合同签定时间：" prop="signingTime">
              <el-date-picker
                v-model="baseData.signingTime"
                type="datetime"
                class="form-l"
                placeholder="请选择合同签定日期"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="归属市场经办：" prop="belongMarket">
              <el-input
                v-model="baseData.belongMarket"
                clearable
                placeholder="请输入该网校市场经办负责人姓名"
                class="form-l"
              />
            </el-form-item>
            <el-form-item label="网校背景说明：" prop="description">
              <el-input
                type="textarea"
                placeholder="请输入网校背景说明，文本框就可"
                :rows="6"
                v-model="baseData.description"
                maxlength="30"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <!-- </el-collapse-item>
    </el-collapse> -->
    <div class="m-btn-bar f-tc is-sticky-1">
      <el-button @click="handleReJump">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </div>
    <industry-drawer
      :drawerVisible="drawerVisible"
      @cancel="drawerVisible = false"
      @submit="handleChooseProperty"
      :haveRSIndustry="baseData.haveRSIndustry"
      :haveJSIndustry="baseData.haveJSIndustry"
      :haveWSIndustry="baseData.haveWSIndustry"
      :haveGQIndustry="baseData.haveGQIndustry"
      :haveLSIndustry="baseData.haveLSIndustry"
      :haveYSIndustry="baseData.haveYSIndustry"
      :RSProperties="baseData.RSIndustry ? baseData.RSIndustry.properties : ''"
      :JSProperties="baseData.JSIndustry ? baseData.JSIndustry.properties : ''"
      :WSProperties="baseData.WSIndustry ? baseData.WSIndustry.properties : ''"
      :GQProperties="baseData.GQIndustry ? baseData.GQIndustry.properties : ''"
      :LSProperties="baseData.LSIndustry ? baseData.LSIndustry.properties : ''"
      :YSProperties="baseData.YSIndustry ? baseData.YSIndustry.properties : ''"
      :activeIndex="activeIndex"
      :firstIndustryList="firstIndustryList"
    ></industry-drawer>
    <industry-drawer-personnel
      :drawerVisible="personnelDrawerVisible"
      @cancel="personnelDrawerVisible = false"
      @submit="handleChoosePropertyPersonnel"
      :haveRSIndustry="baseData.haveRSIndustry"
      :haveJSIndustry="baseData.haveJSIndustry"
      :haveWSIndustry="baseData.haveWSIndustry"
      :haveGQIndustry="baseData.haveGQIndustry"
      :haveLSIndustry="baseData.haveLSIndustry"
      :haveYSIndustry="baseData.haveYSIndustry"
      :RSProperties="rsIndustryProperties"
      :JSProperties="jsIndustryProperties"
      :WSProperties="wsIndustryProperties"
      :GQProperties="gqIndustryProperties"
      :LSProperties="lsIndustryProperties"
      :YSProperties="ysIndustryProperties"
      :activeIndex="activeIndex"
      :firstIndustryList="firstIndustryList"
    ></industry-drawer-personnel>
    <el-dialog title="系统提醒" :visible.sync="dialog1" width="400px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-f16 f-fb">网校信息修改成功！</p>
          <div class="f-f13 f-mt5">
            修改后的信息会同步更新至网校，请与网校确认是否完成对应的关联配置。修改后网校已生成的数据将不受影响。
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="handleCancel">知道了</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Watch, Vue } from 'vue-property-decorator'
  import SchoolBaseModel from '@api/service/training-institution/online-school/base-models/SchoolBaseModel'
  import industryDrawer from '@hbfe/jxjy-admin-registerSchool/src/components/industry-drawer.vue'
  import IndustryDrawerPersonnel from '@hbfe/jxjy-admin-registerSchool/src/components/industry-drawer-personnel.vue'
  import OnlineSchoolModule from '@api/service/training-institution/online-school/OnlineSchoolModule'
  import OnlineSchoolModel from '@api/service/training-institution/online-school/models/OnlineSchoolModel'
  import serviceArea from '@hbfe/jxjy-admin-registerSchool/src/components/service-area.vue'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import QueryYear from '@api/service/common/basic-data-dictionary/query/QueryYear'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
  import { PersonIndustry } from '@api/ms-gateway/ms-servicercontract-v1'
  import AddServiceModel from '@api/service/training-institution/online-school/base-models/AddServiceModel'
  import { AddServiceEnum } from '@api/service/training-institution/online-school/enum/AddServiceEnum'
  import { DistributionServiceTypeEnum } from '@api/service/common/capability-service-config/enum/DistributionServiceTypeEnum'
  import { debounce, bind } from 'lodash-decorators'

  class QueryYearList {
    id: string
    enable: boolean
    sort: number
    year: string
  }

  @Component({
    components: { industryDrawer, serviceArea, IndustryDrawerPersonnel }
  })
  export default class extends Vue {
    @Ref('baseForm') baseForm: any
    @Prop({
      required: true,
      default: () => new SchoolBaseModel()
    })
    baseData: SchoolBaseModel
    // 合约id
    @Prop({
      required: true,
      default: () => ''
    })
    id: string
    @Prop({
      required: true,
      default: () => {
        return new AddServiceModel()
      }
    })
    /**
     * 增值服务数据
     */
    addData: AddServiceModel

    @Watch('addData', { deep: true })
    handleAddData(val: AddServiceModel) {
      console.log(val, 'vallllllll')

      if (val.addServiceType.length && !this.isWatch) {
        // 只要第一次触发就行 后面不用触发
        this.learningRule = val.addServiceType.find((item) => item === AddServiceEnum.learningRule)
        this.intelligentlearning = val.addServiceType.find((item) => item === AddServiceEnum.intelligentlearning)
        this.fxService = val.addServiceType.find((item) => item === AddServiceEnum.fxService)

        this.isWatch = true
      }
      this.distributionServiceType = val.distributionServiceType
    }

    onlineSchoolObj: OnlineSchoolModule = new OnlineSchoolModule()
    activeNames: Array<string> = ['1']
    props = {
      // multiple: true,
      emitPath: false,
      value: 'id',
      label: 'name',
      expandTrigger: 'hover'
      // disabled:'enable'
      //   checkStrictly: true
    }
    Areaoptions: RegionTreeVo[] = []
    rules = {
      schoolName: [{ required: true, message: '请输入网校平台名称', trigger: 'blur' }],
      areaType: [{ required: true, validator: this.validateArea, trigger: 'change' }],
      industryYears: [{ required: true, message: '请选择业务属性', trigger: 'change' }],
      ownerUnitName: [{ required: true, message: '请输入业主单位全称', trigger: 'blur' }],
      ownerUnitSingleName: [{ required: true, message: '请输入业主单位简称', trigger: 'blur' }],
      ownerCharge: [{ required: true, message: '请输入业主负责人', trigger: 'blur' }],
      phone: [{ required: true, validator: this.validatePhone, trigger: 'blur' }],
      schoolModel: [{ required: true, message: '请选择网校性质', trigger: 'change' }],
      contractSigning: [{ required: true, message: '请选择合约签订情况', trigger: 'change' }],
      signingTime: [{ required: true, message: '请选择签约时间', trigger: 'change' }],
      belongMarket: [{ required: true, message: '请输入归属市场经办', trigger: 'blur' }],
      description: [{ required: true, message: '请输入网校背景描述', trigger: 'blur' }],
      haveRSIndustry: [{ required: false, validator: this.validateIndustry, trigger: 'change' }]
    }
    drawerVisible = false
    personnelDrawerVisible = false
    haveownerUnitSingleName = false
    // needArea = false
    options: Array<string> = []
    options2: Array<string> = []
    yearList: Array<QueryYearList> = []
    areaList: RegionTreeVo[] = []
    dialog1 = false
    areaType = ''
    industryChooseList: Array<string> = []
    industryPersonnelChooseList: Array<string> = []
    activeIndex = ''
    firstIndustryList: Array<string> = []
    /**
     * 学习规则开关
     */
    learningRule: string = null
    /**
     * 智能学习开关
     */
    intelligentlearning: string = null
    /**
     * 分销服务开关
     */
    fxService: string = null
    /**
     * 分销服务类型
     */
    distributionServiceType: DistributionServiceTypeEnum = null

    isWatch = false

    /**
     * 人社行业 行业属性编号
     */
    get rsIndustryProperties() {
      if (this.baseData.RSIndustry && this.baseData?.personIndustriesProperties?.RSIndustry?.properties) {
        return this.baseData?.personIndustriesProperties?.RSIndustry?.properties
      } else {
        return ''
      }
    }

    /**
     * 建设行业 行业属性编号
     */
    get jsIndustryProperties() {
      if (this.baseData.JSIndustry && this.baseData?.personIndustriesProperties?.JSIndustry?.properties) {
        return this.baseData?.personIndustriesProperties?.JSIndustry?.properties
      } else {
        return ''
      }
    }

    /**
     * 卫生行业 行业属性编号
     */
    get wsIndustryProperties() {
      if (this.baseData.WSIndustry && this.baseData?.personIndustriesProperties?.WSIndustry?.properties) {
        return this.baseData?.personIndustriesProperties?.WSIndustry?.properties
      } else {
        return ''
      }
    }

    /**
     * 工勤行业 行业属性编号
     */
    get gqIndustryProperties() {
      if (this.baseData.GQIndustry && this.baseData?.personIndustriesProperties?.GQIndustry?.properties) {
        return this.baseData?.personIndustriesProperties?.GQIndustry?.properties
      } else {
        return ''
      }
    }

    /**
     * 教师行业 行业属性编号
     */
    get lsIndustryProperties() {
      if (this.baseData.LSIndustry && this.baseData?.personIndustriesProperties?.LSIndustry?.properties) {
        return this.baseData?.personIndustriesProperties.LSIndustry?.properties
      } else {
        return ''
      }
    }

    /**
     * 教师行业 行业属性编号
     */
    get ysIndustryProperties() {
      if (this.baseData.YSIndustry && this.baseData?.personIndustriesProperties?.YSIndustry?.properties) {
        return this.baseData?.personIndustriesProperties.YSIndustry?.properties
      } else {
        return ''
      }
    }

    @Watch('baseData')
    // string or string[]
    watchValue(val?: any) {
      // 全国地区判断 todo 先简单写
      let areaFlag = true
      if (this.baseData.serviceRegionCodes.length == 32) {
        this.baseData.serviceRegionCodes.map((item) => {
          if (item.substr(2, 4) != '0000') {
            areaFlag = false
          }
        })
      } else {
        areaFlag = false
      }
      if (areaFlag) {
        this.areaType = '1'
      } else {
        this.areaType = '2'
      }
      // 行业属性回填
      this.industryChooseList = []
      if (this.baseData.RSIndustry?.properties) {
        this.industryChooseList.push('已选择人社通用的行业培训属性')
      } else if (this.baseData.haveRSIndustry && !this.baseData.RSIndustry?.properties) {
        this.industryChooseList.push('请选择人社行业下的培训属性值')
      }
      if (this.baseData.JSIndustry?.properties) {
        this.industryChooseList.push('已选择建设通用的行业培训属性')
      } else if (this.baseData.haveJSIndustry && !this.baseData.JSIndustry?.properties) {
        this.industryChooseList.push('请选择建设行业下的培训属性值')
      }
      if (this.baseData.WSIndustry?.properties) {
        this.industryChooseList.push('已选择职业卫生通用的行业培训属性')
      } else if (this.baseData.haveWSIndustry && !this.baseData.WSIndustry?.properties) {
        this.industryChooseList.push('请选择职业卫生行业下的培训属性值')
      }
      if (this.baseData.GQIndustry?.properties) {
        this.industryChooseList.push('已选择工勤通用的行业培训属性')
      } else if (this.baseData.haveGQIndustry && !this.baseData.GQIndustry?.properties) {
        this.industryChooseList.push('请选择工勤行业下的培训属性值')
      }
      if (this.baseData.LSIndustry?.properties) {
        this.industryChooseList.push('已选择教师通用的行业培训属性')
      } else if (this.baseData.haveLSIndustry && !this.baseData.LSIndustry?.properties) {
        this.industryChooseList.push('请选择教师行业下的培训属性值')
      }
      if (this.baseData.YSIndustry?.properties) {
        this.industryChooseList.push('已选择药师通用的行业培训属性')
      } else if (this.baseData.haveYSIndustry && !this.baseData.YSIndustry?.properties) {
        this.industryChooseList.push('请选择药师行业下的培训属性值')
      }
      if (this.industryChooseList.length != 0 && !this.firstIndustryList.length) {
        this.firstIndustryList = this.industryChooseList.map((item) => {
          return item.replace('已选择', '').replace('通用的行业培训属性', '')
        })
      }
      // 人员行业属性回填
      if (this.baseData.personIndustriesProperties.RSIndustry?.properties) {
        this.industryPersonnelChooseList.push('已选择人社通用的行业人员属性')
      } else if (this.baseData.haveRSIndustry && !this.baseData.personIndustriesProperties.RSIndustry?.properties) {
        this.industryPersonnelChooseList.push('请选择人社行业下的人员属性值')
      }
      if (this.baseData.personIndustriesProperties.JSIndustry?.properties) {
        this.industryPersonnelChooseList.push('已选择建设通用的行业人员属性')
      } else if (this.baseData.haveJSIndustry && !this.baseData.personIndustriesProperties.JSIndustry?.properties) {
        this.industryPersonnelChooseList.push('请选择建设行业下的人员属性值')
      }
      if (this.baseData.personIndustriesProperties.WSIndustry?.properties) {
        this.industryPersonnelChooseList.push('已选择职业卫生通用的行业人员属性')
      } else if (this.baseData.haveWSIndustry && !this.baseData.personIndustriesProperties.WSIndustry?.properties) {
        this.industryPersonnelChooseList.push('请选择职业卫生行业下的人员属性值')
      }
      if (this.baseData.personIndustriesProperties.GQIndustry?.properties) {
        this.industryPersonnelChooseList.push('已选择工勤通用的行业人员属性')
      } else if (this.baseData.haveGQIndustry && !this.baseData.personIndustriesProperties.GQIndustry?.properties) {
        this.industryPersonnelChooseList.push('请选择工勤行业下的人员属性值')
      }
      if (this.baseData.personIndustriesProperties.LSIndustry?.properties) {
        this.industryPersonnelChooseList.push('已选择教师通用的行业人员属性')
      } else if (this.baseData.haveLSIndustry && !this.baseData.personIndustriesProperties.LSIndustry?.properties) {
        this.industryPersonnelChooseList.push('请选择教师行业下的人员属性值')
      }
      if (this.baseData.personIndustriesProperties.YSIndustry?.properties) {
        this.industryPersonnelChooseList.push('已选择药师通用的行业人员属性')
      } else if (this.baseData.haveYSIndustry && !this.baseData.personIndustriesProperties.YSIndustry?.properties) {
        this.industryPersonnelChooseList.push('请选择药师行业下的人员属性值')
      }
      // 业务地区显示
      this.areaList = QueryBusinessRegion.filterRegionTree(this.Areaoptions, this.baseData.serviceRegionCodes, true)
      // 单位简称回填
      this.haveownerUnitSingleName = this.baseData.ownerUnitSingleName ? true : false
    }

    async created() {
      // 获取年度列表
      this.yearList = (await QueryYear.getOperationYearList()).sort((a: any, b: any) => {
        return b.sort - a.sort
      })
      console.log(this.yearList, 'this.yearList')

      //获取全国地区列表
      const QueryBusinessRegionData = await QueryBusinessRegion.getCountrywideRegion()
      this.Areaoptions = QueryBusinessRegionData
      this.areaList = QueryBusinessRegion.filterRegionTree(this.Areaoptions, this.baseData.serviceRegionCodes, true)
    }

    //验证手机号格式
    validatePhone(rule: any, value: any, callback: any) {
      const reg = new RegExp(/^[1]([3-9])[0-9]{9}$/)
      if (value === '') {
        callback(new Error('请输入手机号'))
      } else {
        reg.test(value) ? callback() : callback(new Error('请输入正确的手机号'))
      }
    }

    //行业选择验证
    validateIndustry(rule: any, value: any, callback: any) {
      if (
        !this.baseData.haveRSIndustry &&
        !this.baseData.haveJSIndustry &&
        !this.baseData.haveWSIndustry &&
        !this.baseData.haveGQIndustry &&
        !this.baseData.haveLSIndustry &&
        !this.baseData.haveYSIndustry
      ) {
        callback(new Error('请至少选择一个行业'))
      } else {
        callback()
      }
    }

    // 服务地区校验
    validateArea(rule: any, value: any, callback: any) {
      // if (!value) {
      //   callback(new Error('请选择服务地区范围'))
      // } else
      if (!this.baseData.serviceRegionCodes.length) {
        callback(new Error('请选择服务地区'))
      } else {
        callback()
      }
    }

    // 行业传参
    getIndutry() {
      this.$emit(
        'getIndutry',
        this.baseData.haveRSIndustry,
        this.baseData.haveJSIndustry,
        this.baseData.haveWSIndustry,
        this.baseData.haveGQIndustry,
        this.baseData.haveLSIndustry,
        this.baseData.haveYSIndustry
      )

      this.handleChooseProperty(
        this.baseData.haveRSIndustry || this.firstIndustryList.includes('人社') ? this.baseData.RSIndustry : {},
        this.baseData.haveJSIndustry || this.firstIndustryList.includes('建设') ? this.baseData.JSIndustry : {},
        this.baseData.haveWSIndustry || this.firstIndustryList.includes('职业卫生') ? this.baseData.WSIndustry : {},
        this.baseData.haveGQIndustry || this.firstIndustryList.includes('工勤') ? this.baseData.GQIndustry : {},
        this.baseData.haveLSIndustry || this.firstIndustryList.includes('教师') ? this.baseData.LSIndustry : {},
        this.baseData.haveYSIndustry || this.firstIndustryList.includes('药师') ? this.baseData.YSIndustry : {}
      )
      this.handleChoosePropertyPersonnel(
        this.baseData.haveRSIndustry ? this.baseData.personIndustriesProperties.RSIndustry : {},
        this.baseData.haveJSIndustry ? this.baseData.personIndustriesProperties.JSIndustry : {},
        this.baseData.haveWSIndustry ? this.baseData.personIndustriesProperties.WSIndustry : {},
        this.baseData.haveGQIndustry ? this.baseData.personIndustriesProperties.GQIndustry : {},
        this.baseData.haveLSIndustry ? this.baseData.personIndustriesProperties.LSIndustry : {},
        this.baseData.haveYSIndustry ? this.baseData.personIndustriesProperties.YSIndustry : {}
      )
    }

    //保存修改
    @bind
    @debounce(200)
    async handleSubmit() {
      const baseData: any = await this.handleCheck()
      if (!baseData.status) {
        this.$alert(baseData.msg, '提示', {
          type: 'warning'
        })
        return false
      }
      this.onlineSchoolObj.onlineSchool = new OnlineSchoolModel()
      this.onlineSchoolObj.onlineSchool.id = this.id
      this.onlineSchoolObj.onlineSchool.schoolBase = baseData.data
      if (!this.onlineSchoolObj.onlineSchool.schoolBase.haveRSIndustry) {
        this.onlineSchoolObj.onlineSchool.schoolBase.RSIndustry = undefined
      }
      if (!this.onlineSchoolObj.onlineSchool.schoolBase.haveJSIndustry) {
        this.onlineSchoolObj.onlineSchool.schoolBase.JSIndustry = undefined
      }
      // 增值服务
      this.onlineSchoolObj.onlineSchool.addServiceConfig.addServiceType = []
      if (this.intelligentlearning) {
        this.onlineSchoolObj.onlineSchool.addServiceConfig.addServiceType.push(this.intelligentlearning)
      }
      if (this.learningRule) {
        this.onlineSchoolObj.onlineSchool.addServiceConfig.addServiceType.push(this.learningRule)
      }
      //   分销服务
      if (this.fxService) {
        this.onlineSchoolObj.onlineSchool.addServiceConfig.addServiceType.push(this.fxService)
        this.onlineSchoolObj.onlineSchool.addServiceConfig.distributionServiceType = this.distributionServiceType
      }
      const result = await this.onlineSchoolObj.updateSchoolBase()
      if (result.status.code == 200) {
        // this.$message.success('保存成功！')
        // this.$emit('success')
        if (result.data.code && result.data.code != '200') {
          this.$alert(result.data.message, '提示', {
            type: 'warning'
          })
          return
        }
        this.dialog1 = true
      } else {
        this.$alert((result.status.errors && result.status.errors[0]?.message) || result.data.message, '提示', {
          type: 'warning'
        })
      }
    }

    //全国地区
    handleAllArea(val: string) {
      if (val == '1') {
        this.baseData.serviceRegionCodes = this.Areaoptions.map((item) => {
          return item.id
        })
        this.areaList = QueryBusinessRegion.filterRegionTree(this.Areaoptions, this.baseData.serviceRegionCodes)
      } else if (val == '2') {
        this.baseData.serviceRegionCodes = []
        this.areaList = []
      }
    }

    // 获取服务地区选中的地区
    getArea(e: Array<string>) {
      this.baseData.serviceRegionCodes = e
      // 业务地区显示
      this.areaList = QueryBusinessRegion.filterRegionTree(this.Areaoptions, e)
    }

    // 选择行业属性
    handleChooseProperty(
      RSIndustry: any = {},
      JSIndustry: any = {},
      WSIndustry: any = {},
      GQIndustry: any = {},
      LSIndustry: any = {},
      YSIndustry: any = {}
    ) {
      this.drawerVisible = false
      this.industryChooseList = []
      if (RSIndustry?.properties) {
        this.industryChooseList.push('已选择人社通用的行业培训属性')
      } else if (this.baseData.haveRSIndustry && !RSIndustry?.properties) {
        this.industryChooseList.push('请选择人社行业下的培训属性值')
      }
      if (JSIndustry?.properties) {
        this.industryChooseList.push('已选择建设通用的行业培训属性')
      } else if (this.baseData.haveJSIndustry && !JSIndustry?.properties) {
        this.industryChooseList.push('请选择建设行业下的培训属性值')
      }
      if (WSIndustry?.properties) {
        this.industryChooseList.push('已选择职业卫生通用的行业培训属性')
      } else if (this.baseData.haveWSIndustry && !WSIndustry?.properties) {
        this.industryChooseList.push('请选择职业卫生行业下的培训属性值')
      }

      if (GQIndustry?.properties) {
        this.industryChooseList.push('已选择工勤通用的行业培训属性')
      } else if (this.baseData.haveGQIndustry && !GQIndustry?.properties) {
        this.industryChooseList.push('请选择工勤行业下的培训属性值')
      }
      if (LSIndustry?.properties) {
        this.industryChooseList.push('已选择教师通用的行业培训属性')
      } else if (this.baseData.haveLSIndustry && !LSIndustry?.properties) {
        this.industryChooseList.push('请选择教师行业下的培训属性值')
      }
      if (YSIndustry?.properties) {
        this.industryChooseList.push('已选择药师通用的行业培训属性')
      } else if (this.baseData.haveYSIndustry && !YSIndustry?.properties) {
        this.industryChooseList.push('请选择药师行业下的培训属性值')
      }
      //行业属性 todo
      this.baseData.RSIndustry = {}
      this.baseData.JSIndustry = {}
      this.baseData.WSIndustry = {}
      this.baseData.GQIndustry = {}
      this.baseData.LSIndustry = {}
      this.baseData.YSIndustry = {}
      this.baseData.RSIndustry = RSIndustry
      this.baseData.JSIndustry = JSIndustry
      this.baseData.WSIndustry = WSIndustry
      this.baseData.GQIndustry = GQIndustry
      this.baseData.LSIndustry = LSIndustry
      this.baseData.YSIndustry = YSIndustry
    }

    // 选择人员行业属性
    handleChoosePropertyPersonnel(
      RSIndustry: PersonIndustry,
      JSIndustry: PersonIndustry,
      WSIndustry: PersonIndustry,
      GQIndustry: PersonIndustry,
      LSIndustry: PersonIndustry,
      YSIndustry: PersonIndustry
    ) {
      this.personnelDrawerVisible = false
      this.industryPersonnelChooseList = []
      if (RSIndustry?.properties) {
        this.industryPersonnelChooseList.push('已选择人社通用的行业人员属性')
      } else if (this.baseData.haveRSIndustry && !RSIndustry?.properties) {
        this.industryPersonnelChooseList.push('请选择人社行业下的人员属性')
      }
      if (JSIndustry?.properties) {
        this.industryPersonnelChooseList.push('已选择建设通用的行业人员属性')
      } else if (this.baseData.haveJSIndustry && !JSIndustry?.properties) {
        this.industryPersonnelChooseList.push('请选择建设行业下的人员属性')
      }
      if (WSIndustry?.properties) {
        this.industryPersonnelChooseList.push('已选择职业卫生通用的行业人员属性')
      } else if (this.baseData.haveWSIndustry && !WSIndustry?.properties) {
        this.industryPersonnelChooseList.push('请选择职业卫生行业下的人员属性')
      }

      if (GQIndustry?.properties) {
        this.industryPersonnelChooseList.push('已选择工勤通用的行业人员属性')
      } else if (this.baseData.haveGQIndustry && !GQIndustry?.properties) {
        this.industryPersonnelChooseList.push('请选择工勤行业下的人员属性')
      }
      if (LSIndustry?.properties) {
        this.industryPersonnelChooseList.push('已选择教师通用的行业人员属性')
      } else if (this.baseData.haveLSIndustry && !LSIndustry?.properties) {
        this.industryPersonnelChooseList.push('请选择教师行业下的人员属性')
      }
      if (YSIndustry?.properties) {
        this.industryPersonnelChooseList.push('已选择药师通用的行业人员属性')
      } else if (this.baseData.haveYSIndustry && !YSIndustry?.properties) {
        this.industryPersonnelChooseList.push('请选择药师行业下的人员属性')
      }
      //行业属性 todo
      this.baseData.personIndustriesProperties.RSIndustry = {}
      this.baseData.personIndustriesProperties.JSIndustry = {}
      this.baseData.personIndustriesProperties.WSIndustry = {}
      this.baseData.personIndustriesProperties.GQIndustry = {}
      this.baseData.personIndustriesProperties.LSIndustry = {}
      this.baseData.personIndustriesProperties.YSIndustry = {}
      this.baseData.personIndustriesProperties.RSIndustry = RSIndustry
      this.baseData.personIndustriesProperties.JSIndustry = JSIndustry
      this.baseData.personIndustriesProperties.WSIndustry = WSIndustry
      this.baseData.personIndustriesProperties.GQIndustry = GQIndustry
      this.baseData.personIndustriesProperties.LSIndustry = LSIndustry
      this.baseData.personIndustriesProperties.YSIndustry = YSIndustry
    }

    //取消修改
    handleReJump() {
      this.dialog1 = false
      this.$router.push('/school-management/management')
    }

    //我知道了
    handleCancel() {
      this.dialog1 = false
      this.$router.push('/school-management/management')
    }

    //行业属性抽屉
    handleDrawer(item: string) {
      this.activeIndex = item

      this.drawerVisible = true
    }

    handlePersonnelDrawer(item: string) {
      this.activeIndex = item
      this.personnelDrawerVisible = true
    }

    //校验
    handleCheck() {
      return new Promise((resolve) => {
        let result = {
          status: true,
          msg: '',
          data: new SchoolBaseModel()
        }
        this.baseForm.validate((valid: any) => {
          if (
            !this.baseData.serviceRegionCodes.length ||
            !this.baseData.industryYears.length ||
            (!this.baseData.RSIndustry?.properties &&
              !this.baseData.JSIndustry?.properties &&
              !this.baseData.WSIndustry?.properties &&
              !this.baseData.GQIndustry?.properties &&
              !this.baseData.LSIndustry?.properties &&
              !this.baseData.YSIndustry?.properties)
          ) {
            result.status = false
            result.msg = '业务属性配置各项需至少勾选一个属性值'
            return false
          } else if (
            !this.baseData.serviceRegionCodes.length ||
            !this.baseData.industryYears.length ||
            (!this.baseData.personIndustriesProperties?.RSIndustry?.properties &&
              !this.baseData.personIndustriesProperties?.JSIndustry?.properties &&
              !this.baseData.personIndustriesProperties?.WSIndustry?.properties &&
              !this.baseData.personIndustriesProperties?.GQIndustry?.properties &&
              !this.baseData.personIndustriesProperties?.LSIndustry?.properties &&
              !this.baseData.personIndustriesProperties?.YSIndustry?.properties)
          ) {
            result.status = false
            result.msg = '人员行业属性需至少勾选一个属性值'
            return false
          } else if (
            (!this.baseData.RSIndustry?.properties && this.baseData.haveRSIndustry) ||
            (!this.baseData.JSIndustry?.properties && this.baseData.haveJSIndustry) ||
            (!this.baseData.WSIndustry?.properties && this.baseData.haveWSIndustry) ||
            (!this.baseData.GQIndustry?.properties && this.baseData.haveGQIndustry) ||
            (!this.baseData.LSIndustry?.properties && this.baseData.haveLSIndustry) ||
            (!this.baseData.YSIndustry?.properties && this.baseData.haveYSIndustry)
          ) {
            result.status = false
            result.msg = '请选择对应行业下的培训属性值'
            return false
          } else if (
            (!this.baseData.personIndustriesProperties?.RSIndustry?.properties && this.baseData.haveRSIndustry) ||
            (!this.baseData.personIndustriesProperties?.JSIndustry?.properties && this.baseData.haveJSIndustry) ||
            (!this.baseData.personIndustriesProperties?.WSIndustry?.properties && this.baseData.haveWSIndustry) ||
            (!this.baseData.personIndustriesProperties?.GQIndustry?.properties && this.baseData.haveGQIndustry) ||
            (!this.baseData.personIndustriesProperties?.LSIndustry?.properties && this.baseData.haveLSIndustry) ||
            (!this.baseData.personIndustriesProperties?.YSIndustry?.properties && this.baseData.haveYSIndustry)
          ) {
            result.status = false
            result.msg = '请选择对应行业下的人员属性值'
            return false
          } else if (valid) {
            result.data = this.baseData
          } else {
            result = {
              status: false,
              msg: '基础信息未填写完整，请检查！',
              data: new SchoolBaseModel()
            }
            return false
          }
        })
        resolve(result)
      })
    }
  }
</script>
