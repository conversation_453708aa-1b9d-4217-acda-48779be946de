import QueryOrderListVo from '@api/service/management/trade/single/order/query/vo/QueryOrderListVo'
import { Response } from '@hbfe/common'
import DataExportBackstage, {
  OrderSortRequest,
  ReturnSortRequest
} from '@api/diff-gateway/jxgx-data-export-gateway-backstage'
import CheckAccountParam from '@api/service/management/trade/single/checkAccount/query/vo/CheckAccountParam'
import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
import UserModule from '@api/service/management/user/UserModule'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import {
  InvoiceAssociationInfoRequest,
  ReturnOrderRequest,
  ReturnOrderStatisticResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import QueryPageInvoiceParam from '@api/service/management/trade/single/invoice/query/dto/QueryPageInvoiceParam'
import QueryOffLinePageInvoiceParam from '@api/service/management/trade/single/invoice/query/vo/QueryOffLinePageInvoiceParam'
import DeliveryInvoiceParamVo from '@api/service/management/trade/single/invoice/query/vo/DeliveryInvoiceParam'
import fxnlQuery from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
/**
 * @description
 */
class TradeExport {
  /**
   * 导出订单列表数据
   */
  async exportOrder(exportParams: QueryOrderListVo): Promise<Response<boolean>> {
    const result = new Response<boolean>()
    const request = await exportParams.to(false)
    const response = await DataExportBackstage.exportOrderExcelInServicer({
      request
    })
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
  /**
   * 导出订单列表数据（分销）
   */
  async exportFxOrder(exportParams: QueryOrderListVo): Promise<Response<boolean>> {
    const result = new Response<boolean>()
    const request = await exportParams.to(false)
    const response = await DataExportBackstage.exportOrderExcelInDistributor({
      request
    })
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }

  /**
   * 个人报名对账导出
   */
  async listExport(checkAccountParam: CheckAccountParam, sortRequest?: Array<OrderSortRequest>): Promise<boolean> {
    const request = CheckAccountParam.to(checkAccountParam)
    const { data } = await DataExportBackstage.exportReconciliationExcelInServicer({ request, sort: sortRequest })
    return data
  }

  /**
   * 个人报名对账导出（分销）
   */
  async listFxExport(checkAccountParam: CheckAccountParam, sortRequest?: Array<OrderSortRequest>): Promise<boolean> {
    const request = CheckAccountParam.to(checkAccountParam)
    const { data } = await DataExportBackstage.exportReconciliationExcelInDistributor({ request, sort: sortRequest })
    return data
  }

  /**
   * 个人退款对账导出
   */
  async listReturnExport(
    checkAccountParam: CheckAccountParam,
    sortRequest?: Array<ReturnSortRequest>
  ): Promise<boolean> {
    const request = CheckAccountParam.toReturn(checkAccountParam)
    const { data } = await DataExportBackstage.exportReturnReconciliationExcelInServicer({
      request,
      sort: sortRequest
    })
    return data
  }

  /**
   * 个人退款对账导出（分销）
   */
  async listFxReturnExport(
    checkAccountParam: CheckAccountParam,
    sortRequest?: Array<ReturnSortRequest>
  ): Promise<boolean> {
    const request = CheckAccountParam.toReturn(checkAccountParam)
    const { data } = await DataExportBackstage.exportReturnReconciliationExcelInDistributor({
      request,
      sort: sortRequest
    })
    return data
  }
  /**
   * 导出个人退货单
   */
  async exportReturnOrderExcelInServicer(
    request: ReturnOrderRequestVo,
    returnOrderStatisic: ReturnOrderStatisticResponse,
    sort?: Array<ReturnSortRequest>
  ) {
    let userIdList: string[] = []
    if (request.name || request.idCard || request.loginAccount) {
      const queryUser = UserModule.queryUserFactory.queryStudentList
      queryUser.queryStudentIdParams.idCard = request.idCard
      queryUser.queryStudentIdParams.userName = request.name
      queryUser.queryStudentIdParams.loginAccount = request.loginAccount
      const res = await queryUser.queryStudentIdList()
      userIdList = res.data
      if (!userIdList.length) {
        returnOrderStatisic.totalReturnOrderCount = 0
        returnOrderStatisic.totalRefundAmount = 0
        return false
      }
    }
    request.subOrderInfo.orderInfo.buyerIdList = userIdList
    if (request.saleSource || request.saleSource === SaleChannelEnum.self) {
      request.subOrderInfo.orderInfo.saleChannels = [request.saleSource]
    } else {
      request.subOrderInfo.orderInfo.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic
      ]
    }
    const response = await DataExportBackstage.exportReturnOrderExcelInServicer({
      request: request as ReturnOrderRequest,
      sort
    })
    return response
  }
  /**
   * 导出个人退货单（分销）
   */
  async exportFxReturnOrderExcelInDistributor(
    request: ReturnOrderRequestVo,
    returnOrderStatisic: ReturnOrderStatisticResponse,
    sort?: Array<ReturnSortRequest>
  ) {
    let userIdList: string[] = []
    if (request.name || request.idCard || request.loginAccount) {
      const queryUser = UserModule.queryUserFactory.queryStudentList
      queryUser.queryStudentIdParams.idCard = request.idCard
      queryUser.queryStudentIdParams.userName = request.name
      queryUser.queryStudentIdParams.loginAccount = request.loginAccount
      const res = await queryUser.queryStudentIdList()
      userIdList = res.data
      if (!userIdList.length) {
        returnOrderStatisic.totalReturnOrderCount = 0
        returnOrderStatisic.totalRefundAmount = 0
        return false
      }
    }
    request.subOrderInfo.orderInfo.buyerIdList = userIdList
    if (request.saleSource || request.saleSource === SaleChannelEnum.self) {
      request.subOrderInfo.orderInfo.saleChannels = [request.saleSource]
    } else {
      request.subOrderInfo.orderInfo.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic
      ]
    }
    const response = await DataExportBackstage.exportReturnOrderExcelInDistributor({
      request: request as ReturnOrderRequest,
      sort
    })
    return response
  }
  /**
   * 线上发票导出
   * @param queryPageInvoiceParam 查询参数
   */
  async onLinePageInvoiceInExport(queryPageInvoiceParam?: QueryPageInvoiceParam): Promise<boolean> {
    const request = QueryPageInvoiceParam.to(queryPageInvoiceParam)
    if (
      queryPageInvoiceParam.userName ||
      queryPageInvoiceParam.idCard ||
      queryPageInvoiceParam.phone ||
      queryPageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryPageInvoiceParam.userName
        ? queryPageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryPageInvoiceParam.idCard
        ? queryPageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryPageInvoiceParam.phone
        ? queryPageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryPageInvoiceParam.loginAccount
        ? queryPageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        if (request.associationInfoList[0]) {
          request.associationInfoList[0].buyerIdList = idList.data
        } else {
          request.associationInfoList[0] = new InvoiceAssociationInfoRequest()
          request.associationInfoList[0].buyerIdList = idList.data
        }
      }
    }
    const result = await DataExportBackstage.exportOnlineInvoiceInServicerForJxjy(request)
    return result.data
  }
  /**
   * 电子发票导出
   * @param QueryOffLinePageInvoiceParam 查询参数
   */
  async offLinePageInvoiceInExport(queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam): Promise<boolean> {
    queryOffLinePageInvoiceParam.invoiceType = 1
    queryOffLinePageInvoiceParam.invoiceCategoryList = [2]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone ||
      queryOffLinePageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryOffLinePageInvoiceParam.userName
        ? queryOffLinePageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryOffLinePageInvoiceParam.idCard
        ? queryOffLinePageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryOffLinePageInvoiceParam.phone
        ? queryOffLinePageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryOffLinePageInvoiceParam.loginAccount
        ? queryOffLinePageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        request.associationInfo.buyerIdList = idList.data
      }
    }
    const result = await DataExportBackstage.exportOfflineInvoiceInServicerForJxjy(request)
    return result.data
  }

  /**
   * 个人线下发票导出 - 专票（电子票）
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  async offLinePageElectVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean> {
    queryOffLinePageInvoiceParam.invoiceType = 1
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone ||
      queryOffLinePageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryOffLinePageInvoiceParam.userName
        ? queryOffLinePageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryOffLinePageInvoiceParam.idCard
        ? queryOffLinePageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryOffLinePageInvoiceParam.phone
        ? queryOffLinePageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryOffLinePageInvoiceParam.loginAccount
        ? queryOffLinePageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        request.associationInfo.buyerIdList = idList.data
      }
    }
    const result = await DataExportBackstage.exportOfflineInvoiceInServicerForJxjy(request)
    return result.data
  }

  /**
   * 个人线下发票导出 - 专票（纸质票）
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  async offLinePageVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean> {
    queryOffLinePageInvoiceParam.invoiceType = 2
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone ||
      queryOffLinePageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryOffLinePageInvoiceParam.userName
        ? queryOffLinePageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryOffLinePageInvoiceParam.idCard
        ? queryOffLinePageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryOffLinePageInvoiceParam.phone
        ? queryOffLinePageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryOffLinePageInvoiceParam.loginAccount
        ? queryOffLinePageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        request.associationInfo.buyerIdList = idList.data
      }
    }
    const result = await DataExportBackstage.exportOfflineInvoiceInServicerForJxjy(request)
    return result.data
  }

  /**
   * 导出发票配送
   */
  async exportPageDeliveryInvoice(deliveryInvoiceParamVo: DeliveryInvoiceParamVo): Promise<boolean> {
    const offlineInvoiceRequest = new QueryOffLinePageInvoiceParam()
    offlineInvoiceRequest.takePerson = deliveryInvoiceParamVo.name
    offlineInvoiceRequest.idCard = deliveryInvoiceParamVo.idCard
    offlineInvoiceRequest.loginAccount = deliveryInvoiceParamVo.loginAccount
    offlineInvoiceRequest.deliveryStatusList = deliveryInvoiceParamVo.deliveryStatus
    offlineInvoiceRequest.orderNoList = deliveryInvoiceParamVo.invoiceNo
    offlineInvoiceRequest.deliveryStartTime = deliveryInvoiceParamVo.startDate
    offlineInvoiceRequest.deliveryEndTime = deliveryInvoiceParamVo.endDate
    offlineInvoiceRequest.shippingMethodList = deliveryInvoiceParamVo.deliveryWay
    offlineInvoiceRequest.expressNo = deliveryInvoiceParamVo.theAwb
    offlineInvoiceRequest.takePerson = deliveryInvoiceParamVo.recipient
    offlineInvoiceRequest.invoiceFreezeStatus = deliveryInvoiceParamVo.frozenState
    const data = await this.offLinePageVatspecialplaInvoice(offlineInvoiceRequest)
    return data
  }
  /**
   * 个人线下发票配送 - 专票
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  async offLinePageVatspecialplaInvoice(queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam): Promise<boolean> {
    queryOffLinePageInvoiceParam.invoiceType = 2
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone ||
      queryOffLinePageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryOffLinePageInvoiceParam.userName
        ? queryOffLinePageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryOffLinePageInvoiceParam.idCard
        ? queryOffLinePageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryOffLinePageInvoiceParam.phone
        ? queryOffLinePageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryOffLinePageInvoiceParam.loginAccount
        ? queryOffLinePageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        request.associationInfo.buyerIdList = idList.data
      }
    }
    const result = await DataExportBackstage.exportInvoiceDeliveryInServicer(request)
    return result.data
  }
}

export default TradeExport
