/*
 * @Description: 发票业务
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:33:37
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-31 11:45:42
 */
import msBill, { ElectronicInvoiceResultResponse, ElectronicInvoiceRetryIssueRequest } from '@api/ms-gateway/ms-bill-v1'
import { Response } from '@hbfe/common'
import InvoiceDetail from './dto/InvoiceDetail'
import { issueElectronicInvoice } from './template/template'
import InvoiceListResponse from './vo/InvoiceListResponse'
import orderGraphl, { ApplyInvoiceResponse, ApplyOrderInvoiceRequest } from '@api/ms-gateway/ms-order-v1'
import BatchInvoiceParam from '@api/service/centre/trade/batch/invoice/mutation/vo/BatchInvoiceParam'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import * as GraphqlImporter from '@api/ms-gateway/ms-bill-v1/graphql-importer'
export default class MutationInvoice {
  //   invoiceListResponse = new InvoiceListResponse()
  /**
   * 请求冲红电子发票
   * @param invoiceId 发票编号
   */
  async flushElectronicInvoice(invoiceId: string): Promise<ElectronicInvoiceResultResponse> {
    const response = await msBill.flushElectronicInvoice(invoiceId)
    return response.data
  }
  /**
   * 请求开具电子发票
   * @param invoiceId 发票编号
   */
  async issueElectronicInvoice(invoiceIds: Array<string>): Promise<Array<ElectronicInvoiceResultResponse>> {
    const requestObj = {}
    for (let i = 0; i < invoiceIds.length; i++) {
      const element = invoiceIds[i]
      requestObj[`invoiceId` + i] = element
    }
    const result = await msBill._commonQuery<ElectronicInvoiceResultResponse>(
      issueElectronicInvoice(invoiceIds.length),
      requestObj
    )
    const errorArr = new Array<ElectronicInvoiceResultResponse>()
    for (const key in result.data) {
      if (Object.prototype.hasOwnProperty.call(result.data, key)) {
        if (key == 'success' || key == 'message') {
          // 代表只有一个参数
          if (key == 'success')
            if (!result.data[key]) {
              errorArr.push(result.data)
            }
        } else {
          // 代表多个参数
          if (!result.data[key].success) {
            errorArr.push(result.data[key])
          }
        }
      }
    }
    // const response = await msBill.issueElectronicInvoice(invoiceId)
    return errorArr
  }
  /**
   * 更新电子发票信息
   * @param invoiceDetail 发票详情
   */
  async updateElectronicInvoice(invoiceDetail: InvoiceDetail) {
    const detail = InvoiceDetail.to(invoiceDetail)
    const response = await msBill.updateElectronicInvoice(detail)
    return response
  }

  /**
   * 更新电子发票信息（批次单）
   * @param invoiceDetail 发票详情
   * @param batchOrderNo 批次单号
   */
  async updateBatchElectronicInvoice(invoiceDetail: InvoiceDetail, batchOrderNo: string) {
    const detail = InvoiceDetail.toAutoBatch(invoiceDetail)
    detail.batchOrderNo = batchOrderNo
    const response = await orderGraphl.updateBatchOrderInvoice(detail)
    return response
  }

  /**
   * 设置自动开票
   * @param auto 是否自动开票
   * @param intervalHours 时长
   * @returns
   */
  async addOrUpdateElectronicInvoiceAutoConfig(auto: boolean, intervalHours: number) {
    const response = await msBill.addOrUpdateElectronicInvoiceAutoConfig({ auto, intervalHours, configType: 2 })
    return response.status
  }

  /**
   * 申请开票
   * @param batchOrderNo 批次号
   * @returns
   */
  async applyInoice(batchOrderNo: string, batchInvoiceParam: BatchInvoiceParam) {
    const invoiceInfo = new BatchInvoiceParam()
    Object.assign(invoiceInfo, batchInvoiceParam)
    if (
      batchInvoiceParam.deliveryAddress.address &&
      batchInvoiceParam.deliveryAddress.consignee &&
      batchInvoiceParam.deliveryAddress.phone &&
      batchInvoiceParam.deliveryAddress.region
    ) {
      invoiceInfo.deliveryAddress = batchInvoiceParam.deliveryAddress
    } else {
      invoiceInfo.deliveryAddress = undefined
    }
    if (batchInvoiceParam.takePoint.pickupLocation && batchInvoiceParam.takePoint.pickupTime) {
      invoiceInfo.takePoint = batchInvoiceParam.takePoint
    } else {
      invoiceInfo.takePoint = undefined
    }
    const { status } = await orderGraphl.batchApplyInvoice({
      batchOrderNo,
      invoiceInfo
    })
    return status
  }

  /**
   * 重试开具发票
   * @param electronicInvoiceRetryIssueRequest
   */
  async retryInvoice(electronicInvoiceRetryIssueRequest: ElectronicInvoiceRetryIssueRequest) {
    const res = await msBill.retryIssueElectronicInvoice(electronicInvoiceRetryIssueRequest)
    return res
  }

  /**
   * 重试开具发票（批量）
   */
  async batchRetryInvoice(retryRequestList: Array<ElectronicInvoiceRetryIssueRequest>) {
    const req = new RewriteGraph<Response<void>, ElectronicInvoiceRetryIssueRequest>(
      msBill._commonQuery,
      GraphqlImporter.retryIssueElectronicInvoice
    )

    const res = await req.request(retryRequestList)

    return res
  }
}
