<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--编辑-->
        <el-button @click="dialog12 = true" type="primary" class="f-mr20 f-mb20">编辑专题信息-金昌市</el-button>
        <el-drawer
          title="编辑专题信息-金昌市"
          :visible.sync="dialog12"
          :direction="direction"
          size="560px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="工作单位性质：">
                <el-select v-model="form.region" clearable placeholder="请选择工作单位性质"></el-select>
              </el-form-item>
              <el-form-item label="在编情况：">
                <el-select v-model="form.region" clearable placeholder="请选择在编情况"></el-select>
              </el-form-item>
              <el-form-item label="是否在专技岗位工作：">
                <el-select v-model="form.region" clearable placeholder="请选择是否在专技岗位工作"></el-select>
              </el-form-item>
              <el-form-item label="职称系列：">
                <el-select v-model="form.region" clearable placeholder="请选择职称系列"></el-select>
              </el-form-item>
              <el-form-item label="职称专业：">
                <el-input v-model="form.name" clearable placeholder="请输入职称专业" />
              </el-form-item>
              <el-form-item label="现有职称等级：">
                <el-select v-model="form.region" clearable placeholder="请选择现有职称等级"></el-select>
              </el-form-item>
              <el-form-item label="现有职称资格名称：">
                <el-input v-model="form.name" clearable placeholder="请输入现有职称资格名称" />
              </el-form-item>
              <el-form-item label="现有职称有效范围：">
                <el-select v-model="form.region" clearable placeholder="请选择现有职称有效范围"></el-select>
              </el-form-item>
              <el-form-item label="最高学历：">
                <el-input v-model="form.name" clearable placeholder="请输入最高学历" />
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button>取消</el-button>
                <el-button type="primary">保存</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 1,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        dialog11: false,
        dialog12: false,
        dialog13: false,
        dialog14: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
