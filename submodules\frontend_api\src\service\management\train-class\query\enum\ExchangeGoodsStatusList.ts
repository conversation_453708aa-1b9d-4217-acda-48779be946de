import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 物品状态枚举
 */
export enum ExchangeGoodsStatusEnum {
  // 初始物品
  Origin = 1,
  // 中间物品
  Middle,
  // 最新物品
  Latest
}
/**
 * @description 物品状态枚举
 */
class ExchangeGoodsStatusList extends AbstractEnum<ExchangeGoodsStatusEnum> {
  static enum = ExchangeGoodsStatusEnum
  constructor(status?: ExchangeGoodsStatusEnum) {
    super()
    this.current = status
    this.map.set(ExchangeGoodsStatusEnum.Origin, '初始物品')
    this.map.set(ExchangeGoodsStatusEnum.Middle, '中间物品')
    this.map.set(ExchangeGoodsStatusEnum.Latest, '最新物品')
  }
}

export default new ExchangeGoodsStatusList()
