import MsCoursePlayResourceV1 from '@api/ms-gateway/ms-course-play-resource-v1'
import CoursewareMediaPlayResource from '@api/service/customer/learning/course/vo/CoursewareMediaPlayResource'

class UserPreviewCourseware {
  coursewareId: string
  resource: CoursewareMediaPlayResource

  constructor(coursewareId: string) {
    this.coursewareId = coursewareId
  }

  async queryCoursewarePlayResource() {
    const { data } = await MsCoursePlayResourceV1.applyCoursewareMediaPreviewAntiTheftChainResource(this.coursewareId)
    this.resource = CoursewareMediaPlayResource.from(data)
  }

  async queryCoursewarePlayResourceWithText() {
    const { data } = await MsCoursePlayResourceV1.applyCoursewareMediaPreviewResource(this.coursewareId)
    this.resource = CoursewareMediaPlayResource.from(data)
  }
}

export default UserPreviewCourseware
