const { walk, cwd, getEntries } = require('./build/utils')
const WebpackBar = require('webpackbar')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const StylelintPlugin = require('stylelint-webpack-plugin')
const StyleLintFormatter = require('stylelint-formatter-pretty')
const path = require('path')
const fs = require('fs')
const ThemeColorReplacer = require('webpack-theme-color-replacer')
const { getThemeColors } = require('./build/themeController/utils/themeUtil')
const { resolveCss } = require('./build/themeController/utils/theme-color-replacer-extend')

const treeList = []
walk(cwd('src'), treeList, cwd('src'))
fs.writeFileSync('./tree-list.ts', `export default ${JSON.stringify(treeList)}`)
module.exports = {
  publicPath: './',
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'scss',
      patterns: [path.resolve(__dirname, './src/common/theme/theme.scss')]
    }
  },
  chainWebpack: config => {
    config.entry('app').clear()
    config.entry('app').add(path.resolve(__dirname, './build/design/main.ts'))

    config.module
      .rule('scss')
      .test(/\.scss$/)

      .oneOf('normal')
      .use('sass-loader')
      .options({ sourceMap: true })
      .end()

      .use('scss')
      .before('sass-loader')
      .loader('resolve-url-loader')
      .end()
      .end()

      .oneOf('normal-modules')
      .use('sass-loader')
      .options({ sourceMap: true })
      .end()

      .use('scss')
      .before('sass-loader')
      .loader('resolve-url-loader')
      .end()
      .end()

      .oneOf('vue-modules')
      .use('sass-loader')
      .options({ sourceMap: true })
      .end()

      .use('scss')
      .before('sass-loader')
      .loader('resolve-url-loader')
      .end()
      .end()

      .oneOf('vue')
      .use('sass-loader')
      .options({ sourceMap: true })
      .end()

      .use('scss')
      .before('sass-loader')
      .loader('resolve-url-loader')
      .end()
      .end()

    config.module
      .rule('eslint')
      .use('eslint-loader')
      .options({
        formatter: require('eslint-formatter-pretty')
      })

    config.plugin('progress').use(WebpackBar)

    // stylelint
    config.plugin('stylelint').use(StylelintPlugin, [
      {
        files: ['**/*.s?(a|c)ss', '**/*.vue'],
        formatter: StyleLintFormatter
      }
    ])

    // 添加绝对路径处理loader
    config.module
      .rule('absolute-path')
      .test(/\.(vue|js|css|scss|sass|less)$/)
      .use('absolute-path-loader')
      .loader(path.resolve(__dirname, 'build/absolute-path-loader.js'))
      .end()

    getEntries('./src').forEach(app => {
      if (['common', 'config', 'utils'].includes(app)) return
      const split = app.split('/')
      const parent = split[0]
      const sub = split[1]
      config
        // Interact with entry points
        .entry(app)
        .add(cwd(`build/design/preview.js?app=${parent}&sub=${sub}`))
        .end()

      config.plugin('index').use(HtmlWebpackPlugin, [
        {
          filename: `index.html`,
          chunks: ['chunk-vendors', 'chunk-common', 'app'],
          template: './public/index.html',
          url: '',
          inject: true
        }
      ])
      config.plugin(app).use(HtmlWebpackPlugin, [
        {
          filename: `${app}.html`,
          chunks: ['chunk-vendors', 'chunk-common', app],
          template: './public/index.html',
          url: '',
          inject: true
        }
      ])
    })
  },
  configureWebpack: {
    module: {
      rules: [
        {
          test: /design-components\.js$/,
          loader: require.resolve('./build/components-loader')
        },
        {
          test: /preview\.js$/,
          loader: require.resolve('./build/design-style-loader.js')
        }
      ]
    },
    plugins: [
      new ThemeColorReplacer({
        fileName: 'css/theme-colors-[contenthash:8].css',
        matchColors: getThemeColors(),
        injectCss: true,
        resolveCss
      })
    ]
  }
}
