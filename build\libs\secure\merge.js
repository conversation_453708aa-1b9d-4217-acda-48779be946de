const path = require('path')
const fs = require('fs')
const config = require('./config.js')
const items = process.env.ITEMS || 'super'
const util = require('./utils.js')
const uiModulePrefixes = process.env.UIMODULE ? process.env.UIMODULE.split(',') : ['noMatch']
const oprationInvokeRoleListFilePath = path.join(process.cwd(), 'security_port/oprationInvokeRoleList.json')
const itemList = items.split(',')
// 收集 @hbfe 下所有包名
const hbfeModuleName = util.collectHbfeModuleName()
// 收集UI模块包内的安全对象
const hbfeUiModuleSecurityPaths = util.collectHbfeUiModuleSecurity(hbfeModuleName, uiModulePrefixes)
const groupTop = {}
const uiAssignsGroup = []
let permissionTop = []
const permissionGroup = {}

const writeFile = (filePath, content) => {
  fs.writeFileSync(path.join(config.cachePath, filePath), JSON.stringify(content), { encoding: 'UTF-8' })
}

const readFile = filePath => {
  return JSON.parse(fs.readFileSync(path.join(config.cachePath, filePath)).toString())
}
const readFileByFilename = fileName => {
  return JSON.parse(fs.readFileSync(fileName).toString())
}
itemList.forEach(item => {
  if (fs.existsSync(path.join(config.cachePath, `${item}-${config.uiAssignsFileName}`))) {
    if (item === 'WXGLY' && fs.existsSync(path.join(config.cachePath, 'merged-ui-assigns.json'))) {
      const groupTree = readFile('merged-ui-assigns.json')
      uiAssignsGroup.push(groupTree[0])
    } else {
      const groupTree = readFile(`${item}-${config.uiAssignsFileName}`)
      uiAssignsGroup.push(groupTree[0])
    }
  }
  if (fs.existsSync(path.join(config.cachePath, `${item}-${config.permissionFileName}`))) {
    const permissionTree = readFile(`${item}-${config.permissionFileName}`)
    permissionGroup[item] = permissionTree[item]
    permissionTop = permissionTop.concat(permissionTree)
  }
})

const uiInvokeMap = readFileByFilename(config.uiInvoke)
function travelNode (node) {
  if (node.meta.noMatchBiz) {
    for (const key in node.meta.noMatchBiz) {
      for (const biz of node.meta.noMatchBiz[key]) {
        if (uiInvokeMap[path.join(config.BizComponentPath, biz + '.vue')]) {
          let fileGql = []
          for (const method in uiInvokeMap[path.join(config.BizComponentPath, biz + '.vue')]) {
            fileGql = fileGql.concat(uiInvokeMap[path.join(config.BizComponentPath, biz + '.vue')][method])
          }
          if (fileGql.length > 0) {
            node.meta.permissionMap[key].graphql = Array.from(
              new Set(node.meta.permissionMap[key].graphql.concat(fileGql))
            )
          }
        }
      }
    }
    delete node.meta.noMatchBiz
  }
  if (node && node.children) {
    for (const item of node.children) {
      travelNode(item)
    }
  }
}
hbfeUiModuleSecurityPaths.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    const moduleSecurity = JSON.parse(fs.readFileSync(filePath, { encoding: 'utf-8' }))[0]
    const targetChildNode = moduleSecurity['children']
    const targetSuperQuery = moduleSecurity['meta']['permissionMap']['query']['graphql']
    const wxglySecurityIndex = uiAssignsGroup.findIndex(item => {
      return item.name === 'WXGLY'
    })
    const fxNode = uiAssignsGroup[wxglySecurityIndex].children.find(item => item.name == 'distribution')
    travelNode(fxNode)
    targetChildNode.forEach(node => {
      if (node.path == '/distribution') {
        node.path = '/fx' + node.path
      } else {
        travelNode(node)
        uiAssignsGroup[wxglySecurityIndex].children.push(node)
      }
    })
    targetSuperQuery.forEach(graphql => {
      uiAssignsGroup[wxglySecurityIndex]['meta']['permissionMap']['query']['graphql'].push(graphql)
    })
  }
})
if (fs.existsSync(oprationInvokeRoleListFilePath)) {
  let roleList = JSON.parse(fs.readFileSync(oprationInvokeRoleListFilePath).toString())
  function markMenuOpration (item) {
    if (item.name == 'WXGLY') {
      const queryPermission = item.meta.permissionMap['query']
      if (queryPermission.ext) {
        queryPermission.ext.query = true
      } else {
        queryPermission.ext = { query: true }
      }
    }
    if (item.meta.roles && item.meta.roles.find(role => roleList.includes(role))) {
      if (!item.meta.ext) {
        item.meta.ext = {
          query: true
        }
      } else {
        item.meta.ext.query=true
      }
    }
    if (item.children && item.children.length) {
      item.children.forEach(child => {
        markMenuOpration(child)
      })
    }
  }
  uiAssignsGroup.forEach(item => {
    markMenuOpration(item)
  })
}

writeFile(config.permissionFileName, uiAssignsGroup)
writeFile(`user-${config.permissionFileName}`, permissionGroup)
