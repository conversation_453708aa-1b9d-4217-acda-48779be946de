import listSignUpNumberInfo from './queries/listSignUpNumberInfo.graphql'
import applyStudentLearningToken from './mutates/applyStudentLearningToken.graphql'
import createOrder from './mutates/createOrder.graphql'
import enterIndex from './mutates/enterIndex.graphql'
import getTrainSupervisionForAHZJ from './mutates/getTrainSupervisionForAHZJ.graphql'
import getVerificationCodeForAHZJ from './mutates/getVerificationCodeForAHZJ.graphql'
import sendCodeAnswerForAHZJ from './mutates/sendCodeAnswerForAHZJ.graphql'
import validAllowToCreateOrder from './mutates/validAllowToCreateOrder.graphql'
import validAllowToLearning from './mutates/validAllowToLearning.graphql'

export {
  listSignUpNumberInfo,
  applyStudentLearningToken,
  createOrder,
  enterIndex,
  getTrainSupervisionForAHZJ,
  getVerificationCodeForAHZJ,
  sendCodeAnswerForAHZJ,
  validAllowToCreateOrder,
  validAllowToLearning
}
