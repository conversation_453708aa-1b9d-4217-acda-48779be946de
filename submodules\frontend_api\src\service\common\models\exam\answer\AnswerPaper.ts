class AnswerPaper {
  // 答卷id|答卷记录id（对应考试服务，为了避免歧义特意说明）
  id: string
  // 最后一份答卷索引
  lastIndex: number
  // 作答信息ID, 一份作答信息可能有多个作答记录（答卷）| 对应后端的AnswerExamPaper._id
  answersId: string
  // 是否自动交卷
  autoSubmit: boolean
  // 创建时间
  createTime: Date
  // 进入作答的时间
  enterTime: Date
  // 交卷时间
  submitTime: Date
  // 是否自动阅卷
  autoMark: boolean
  // 是否阅卷完成
  marked: boolean
  objects: Array<any>
}

export default AnswerPaper
