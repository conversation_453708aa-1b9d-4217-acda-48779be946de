<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">课程管理</el-breadcrumb-item>
      <el-breadcrumb-item>新建课程</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="form" label-width="120px" class="m-form">
                <el-form-item label="课程封面：" required>
                  <el-upload
                    action="#"
                    list-type="picture-card"
                    :auto-upload="false"
                    class="m-pic-upload proportion-pic"
                  >
                    <div slot="default" class="upload-placeholder">
                      <i class="el-icon-plus"></i>
                      <p class="txt">上传图片</p>
                    </div>
                    <div slot="file" slot-scope="{ file }" class="img-file">
                      <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                      <div class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                          <i class="el-icon-zoom-in"></i>
                        </span>
                        <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                          <i class="el-icon-delete"></i>
                        </span>
                      </div>
                    </div>
                    <div slot="tip" class="el-upload__tip">
                      <i class="el-icon-warning"></i>
                      <span class="txt">课程封面图片比例为16:9，建议尺寸：不小于400px * 225px</span>
                    </div>
                  </el-upload>
                  <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                    <img :src="dialogImageUrl" alt="" />
                  </el-dialog>
                </el-form-item>
                <el-form-item label="课程名称：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入课程名称" />
                </el-form-item>
                <el-form-item label="课程分类：" required>
                  <el-cascader
                    clearable
                    filterable
                    :options="cascader"
                    :show-all-levels="false"
                    class="form-m"
                    placeholder="请选择课程分类"
                  />
                  <el-button type="text" class="f-ml15">新建分类</el-button>
                </el-form-item>
                <el-form-item label="课程简介：">
                  <el-input
                    type="textarea"
                    :rows="6"
                    v-model="form.desc"
                    maxlength="300"
                    show-word-limit
                    placeholder="请输入课程简介"
                  />
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">配置课程</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center">
            <el-col :lg="22" :xl="20">
              <el-form ref="form" :model="form" label-width="auto" class="m-form">
                <el-form-item label="课程内容：">
                  <p class="f-fb"><i class="f-dot f-mr5"></i><span class="f-f15">课程目录</span></p>
                  <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10 f-mb20">
                    <el-table-column label="章节1" min-width="300">
                      <template slot="header">
                        章节1 <el-button type="primary" size="mini" plain class="f-ml10">添加多媒体</el-button>
                      </template>
                      <template slot-scope="scope">
                        <div v-if="scope.$index === 0">
                          <div class="f-flex f-align-center">
                            <i class="hb-iconfont icon-drag f-f22 f-link-gray f-mr15"></i>
                            <i class="el-icon-video-play f-f20 f-c9"></i>
                            <span class="f-ml5">这里是文件名称文件名称文件名称这里是文件名称文件名称文件名称</span>
                          </div>
                        </div>
                        <div v-else>
                          <div class="f-flex f-align-center">
                            <i class="hb-iconfont icon-drag f-f22 f-link-gray f-mr15"></i>
                            <i class="el-icon-video-play f-f20 f-c9"></i>
                            <span class="f-ml5">这里是文件名称文件名称文件名称这里是文件名称文件名称文件名称</span>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column width="300">
                      <template><el-progress :percentage="56"></el-progress></template>
                    </el-table-column>
                    <el-table-column min-width="100" align="center">
                      <template>00:02:54</template>
                    </el-table-column>
                    <el-table-column width="100" align="center">
                      <template><el-checkbox v-model="checked">试听</el-checkbox></template>
                    </el-table-column>
                    <el-table-column width="120" align="center" fixed="right">
                      <template slot="header">
                        <el-button type="text" size="mini">重命名</el-button>
                        <el-button type="text" size="mini">删除</el-button>
                      </template>
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="handleEdit(scope.$index, scope.row)">
                          重命名
                        </el-button>
                        <el-button type="text" size="mini" @click="handleDelete(scope.$index, scope.row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10 f-mb20">
                    <el-table-column label="章节1" min-width="300">
                      <template slot="header">
                        章节1 <el-button type="primary" size="mini" plain class="f-ml10">添加多媒体</el-button>
                      </template>
                      <template slot-scope="scope">
                        <div v-if="scope.$index === 0">
                          <div class="f-flex f-align-center">
                            <i class="hb-iconfont icon-drag f-f22 f-link-gray f-mr15"></i>
                            <i class="el-icon-video-play f-f20 f-c9"></i>
                            <span class="f-ml5">这里是文件名称文件名称文件名称这里是文件名称文件名称文件名称</span>
                          </div>
                        </div>
                        <div v-else>
                          <div class="f-flex f-align-center">
                            <i class="hb-iconfont icon-drag f-f22 f-link-gray f-mr15"></i>
                            <i class="el-icon-video-play f-f20 f-c9"></i>
                            <span class="f-ml5">这里是文件名称文件名称文件名称这里是文件名称文件名称文件名称</span>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column width="300">
                      <template><el-progress :percentage="56"></el-progress></template>
                    </el-table-column>
                    <el-table-column min-width="100" align="center">
                      <template>00:02:54</template>
                    </el-table-column>
                    <el-table-column width="100" align="center">
                      <template><el-checkbox v-model="checked">试听</el-checkbox></template>
                    </el-table-column>
                    <el-table-column width="120" align="center" fixed="right">
                      <template slot="header">
                        <el-button type="text" size="mini">重命名</el-button>
                        <el-button type="text" size="mini">删除</el-button>
                      </template>
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="handleEdit(scope.$index, scope.row)">
                          重命名
                        </el-button>
                        <el-button type="text" size="mini" @click="handleDelete(scope.$index, scope.row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button type="primary" plain icon="el-icon-folder-add">添加目录</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button>取消</el-button>
        <el-button type="primary">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
