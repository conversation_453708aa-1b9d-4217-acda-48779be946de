/*
 * @Description: 发票业务
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-07 10:20:55
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-13 16:21:33
 */
import orderGraphl, {
  ApplyInvoiceResponse,
  ApplyInvoiceResult,
  ApplyOrderInvoiceRequest,
  PrepareApplyInvoiceResponse
} from '@api/ms-gateway/ms-order-v1'
import { ResponseStatus, Response } from '@hbfe/common'
export enum ValidateStatus {
  /**
   * 允许开票
   */
  allow = 200,
  /**
   * 异常
   */
  error = 500,
  /**
   * 当前订单不开放申请发票
   */
  not_open = 30001,
  /**
   * 当前订单不允许申请发票
   */
  not_allow,
  /**
   * 当前订单索取发票日期已经截止
   */
  ask_for_invoice_deadline,
  /**
   * 当前订单已经申请开过发票
   */
  already_apply
}
export class CheckInvoiceResponse {
  /**
   * 校验结果code
   */
  code: ValidateStatus
  /**
   * 响应信息
   */
  message: string
  /**
   * 索取发票年度类型
1 - 当年度
2 - 下一个年度
当openInvoiceType&#x3D;0时，该值为null
   */
  askForInvoiceYearType: number
  /**
   * 索取发票截止日期，格式（MM/dd）,如：5月3日，则05/03
当openInvoiceType&#x3D;0时，该值为null
   */
  askForInvoiceDeadline: string
}
export default class MutationInvoice {
  /**
   * 补要发票前校验
   * @param orderNo 订单号
   * @return {Promise<CheckInvoiceResponse>}
   */
  async checkInvoice(orderNo: string): Promise<CheckInvoiceResponse> {
    const checkInvoiceResponse = new CheckInvoiceResponse()
    const res = await orderGraphl.applyInvoiceValidate({
      orderNo: orderNo
    })
    if (res.status.isSuccess() && res.data) {
      checkInvoiceResponse.code = res.data.isAllow ? 200 : Number(res.data.code)
      checkInvoiceResponse.message = res.data.message ?? '校验失败'
    } else {
      checkInvoiceResponse.code = 500
      checkInvoiceResponse.message = '校验失败'
      return checkInvoiceResponse
    }
    if (checkInvoiceResponse.code === ValidateStatus.ask_for_invoice_deadline) {
      const response = await orderGraphl.prepareApplyInvoice(orderNo)
      checkInvoiceResponse.askForInvoiceDeadline = response.data.invoiceConfigResult.askForInvoiceDeadline
      checkInvoiceResponse.askForInvoiceYearType = response.data.invoiceConfigResult.askForInvoiceYearType
    }
    return checkInvoiceResponse
  }
  /**
   * 补要发票
   * @param applyOrderInvoiceRequest 入参
   * @returns
   */
  async applyInvoice(applyOrderInvoiceRequest: ApplyOrderInvoiceRequest): Promise<Response<ApplyInvoiceResponse>> {
    /**  code:
       *    30001	当前订单不开放申请发票
            30002
            当前订单不允许申请发票
            30003
            当前订单索取发票日期已经截止
       */
    const response = await orderGraphl.applyInvoice(applyOrderInvoiceRequest)
    return response
  }
}
