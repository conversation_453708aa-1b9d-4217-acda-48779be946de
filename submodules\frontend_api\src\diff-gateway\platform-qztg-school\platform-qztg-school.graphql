"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""创建订单"""
	createOrder(request:QZTGCreateOrderRequest):CreateOrderResultResponse
	"""修改商品合并关系
	"""
	updateMergeCommodityRelation(request:QZTGUpdateMergeSkuRelationRequest):Void
}
input DeliveryAddress @type(value:"com.fjhb.domain.trade.api.offlineinvoice.events.entities.DeliveryAddress") {
	consignee:String!
	phone:String!
	region:String!
	address:String!
}
input TakePoint @type(value:"com.fjhb.domain.trade.api.offlineinvoice.events.entities.TakePoint") {
	pickupLocation:String!
	pickupTime:String!
	remark:String
}
"""发票信息
	<AUTHOR>
	@since 2021/3/23
"""
input InvoiceInfoRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.InvoiceInfoRequest") {
	"""发票抬头"""
	title:String
	"""发票抬头类型
		<pre>
		1-个人
		2-企业
		</pre>
	"""
	titleType:Int
	"""发票类型
		<pre>
		1-电子发票
		2-纸质发票
		</pre>
	"""
	invoiceType:Int
	"""发票种类
		<pre>
		1-普通发票
		2-增值税普通发票
		3-增值税专用发票
		</pre>
	"""
	invoiceCategory:Int
	"""购买方纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账户"""
	account:String
	"""发票票面备注"""
	remark:String
	"""开票方式
		1 - 线上开票
		2 - 线下开票
	"""
	invoiceMethod:Int
	"""联系电子邮箱"""
	email:String
	"""联系电话"""
	contactPhone:String
	"""营业执照"""
	businessLicensePath:String
	"""开户许可"""
	accountOpeningLicensePath:String
	"""配送方式
		0/1/2,无/自取/快递
		@see OfflineShippingMethods
	"""
	shippingMethod:Int
	"""配送地址信息"""
	deliveryAddress:DeliveryAddress
	"""自取点信息"""
	takePoint:TakePoint
	"""发票信息校验策略
		@see InvoiceVerifyStrategy
	"""
	invoiceVerifyStrategy:Int!
}
"""@Description: 泉州提高创建订单
	@Author: chenDB
	@Date: 2025/3/18
"""
input QZTGCreateOrderRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.QZTGCreateOrderRequest") {
	"""买家编号"""
	buyerId:String!
	"""主方案"""
	mainCommodity:Commodity!
	"""被合并的方案"""
	mergedCommodities:[Commodity]!
	"""购买渠道类型
		1-用户自主购买
		2-集体缴费
		3-管理员导入
	"""
	purchaseChannelType:Int!
	"""终端类型
		<p>
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	terminalCode:String!
	"""渠道商编号"""
	channelVendorId:String
	"""是否需要发票"""
	needInvoice:Boolean!
	"""发票信息"""
	invoiceInfo:InvoiceInfoRequest
	"""参训单位id"""
	participatingUnitId:String
	"""销售渠道Id（自营渠道为空，专题渠道时必填）"""
	saleChannelId:String
	"""销售渠道类型
		0-自营渠道
		2-专题渠道
	"""
	saleChannel:Int!
	"""购买来源类型，1-门户，2-专题
		@see com.fjhb.ms.order.v1.api.consts.PurchaseSourceType
	"""
	purchaseSourceType:Int
}
"""商品描述"""
input Commodity @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.QZTGCreateOrderRequest$Commodity") {
	"""商品sku编号"""
	skuId:String
	"""商品数量"""
	quantity:Int
	"""是否已报名"""
	enrolled:Boolean!
	"""是否是被合并方案商品（默认false，如果是true则用来跳过商品验证）"""
	mergedCommodity:Boolean!
	"""面授班时有值"""
	issueInfo:IssueInfo
}
input IssueInfo @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.QZTGCreateOrderRequest$Commodity$IssueInfo") {
	"""期别id"""
	issueId:String
	"""住宿类型
		住宿类型 0-无需住宿 1-单人住宿 2-合住
	"""
	accommodationType:Int
}
"""泉州提高修改商品合并关系请求
"""
input QZTGUpdateMergeSkuRelationRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.QZTGUpdateMergeSkuRelationRequest") {
	"""当前方案（商品sku）id
	"""
	skuId:String!
	"""删除商品id列表
	"""
	removeSkuIds:[String]
	"""新增商品id列表
	"""
	addSkuIds:[String]
}
"""创建订单结果
	<AUTHOR> create 2021/1/29 17:27
"""
type CreateOrderResultResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.response.CreateOrderResultResponse") {
	"""是否创建成功"""
	success:Boolean!
	"""订单号，仅当{@link #success}为{@code true}时有值"""
	orderNo:String
	"""子订单号"""
	subOrderNoList:[String]
	"""订单创建时间，仅当{@link #success}为{@code true}时有值"""
	createTime:DateTime
	"""下单结果信息"""
	message:String
	"""- 200成功
		- 4002 用户未登录
		- 4003 主方案或者合并方案选择错误
		- 5001 订单创建异常
	"""
	code:Int
	"""商品验证结果信息"""
	resultList:[VerifyResultResponse]
}
"""校验结果返回
	<AUTHOR> create 2021/2/3 10:53
"""
type VerifyResultResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.response.VerifyResultResponse") {
	"""校验结果"""
	message:String
	"""校验code"""
	code:String
	"""订单内的商品skuId"""
	skuId:String
	"""目前是(ms-learningscheme_reservingScheme)返回的子订单"""
	subOrderNo:String
	"""@see StudentSourceTypes"""
	sourceType:String
	sourceId:String
}

scalar List
