import systemContext from '@api/service/common/context/Context'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'

//通过服务商ID区分安溪和当前网校
export function isAxServicerId() {
  return ConfigCenterModule.getFrontendApplication(frontendApplication.anxiServicerId) == systemContext.servicerInfo.id
}

//通过服务商ID区分安徽和当前网校
export function isAhServicerId() {
  return ConfigCenterModule.getFrontendApplication(frontendApplication.ahzjServicerId) == systemContext.servicerInfo.id
}
