<route-meta>
  {
  "isMenu": true,
  "title": "学员学习明细",
  "sort": 5,
  "icon": "icon-mingxi"
  }
  </route-meta>
<template>
  <LearningStatistic v-bind="$attrs" v-on="$listeners" ref="learningStaticRef">
    <template #exportLinkData>
      <el-button
        v-if="$hasPermission('exportData')"
        query
        desc="导出对接数据"
        actions="doExportData"
        @click="doExportData"
        >导出对接数据</el-button
      >
      <el-dialog title="提示" :visible.sync="exportDataSuccessVisible" width="450px" class="m-dialog">
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb">导出成功，是否前往下载数据？</p>
            <p class="f-f13 f-mt5">下载入口：导出任务查看-对接数据</p>
          </div>
        </div>
        <div slot="footer">
          <el-button @click="exportDataSuccessVisible = false">暂 不</el-button>
          <el-button type="primary" @click="goDownloadDataPage">前往下载</el-button>
        </div>
      </el-dialog>
    </template>
  </LearningStatistic>
</template>

<script lang="ts">
  import { Component, Vue, Watch, Ref } from 'vue-property-decorator'
  import UserModule from '@api/service/management/user/UserModule'
  import Context from '@api/service/common/context/Context'
  import ExportDockingData from '@api/service/diff/management/yzzj/learning-statistic/ExportDockingData'
  import LearningStatistic from '@hbfe/jxjy-admin-learningStatistic/src/index.vue'

  @Component({
    components: {
      LearningStatistic
    }
  })
  export default class extends Vue {
    @Ref('learningStaticRef') learningStaticRef: LearningStatistic
    exportDockingData: ExportDockingData = new ExportDockingData()
    // 对接数据导出弹窗
    exportDataSuccessVisible = false
    async doExportData() {
      //
      const isDistrictAdministrator = UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
      this.learningStaticRef.getLocalSkuProperty()
      let res
      if (isDistrictAdministrator) {
        res = await this.exportDockingData.exportExcelManageRegion(this.learningStaticRef.filter)
      } else {
        res = await this.exportDockingData.exportExcel(this.learningStaticRef.filter)
      }
      if (res.status.code == 200 && res.data) {
        this.exportDataSuccessVisible = true
        // this.$message.success('导出成功')
      } else {
        this.$message.warning('导出失败')
      }
    }
    /**
     * 导出对接数据任务下载
     */
    goDownloadDataPage() {
      this.exportDataSuccessVisible = false
      const isDistrictAdministrator = UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
      if (isDistrictAdministrator) {
        this.$router.push({
          path: '/statistic/export-task',
          query: { type: 'exportStudentLearningIntegrationDataStatistical' }
        })
      } else {
        this.$router.push({
          path: '/training/task/exporttask',
          query: { type: 'exportStudentLearningIntegrationDataStatistical' }
        })
      }
    }
  }
</script>
