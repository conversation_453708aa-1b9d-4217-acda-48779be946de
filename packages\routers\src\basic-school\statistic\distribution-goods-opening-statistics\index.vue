<route-meta>
{
"isMenu": true,
"title": "分销商品开通统计",
"sort": 11,
"icon": "icon-mingxi"
}
</route-meta>

<script lang="ts">
  import DistributionGoodsOpeningStatistics from '@hbfe/jxjy-admin-distributionGoodsOpeningStatistics/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { NZFXS, NZFXSJCB } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    queryGoodsOpeningStatistics: [NZFXS, NZFXSJCB],
    exportDataGoodsOpeningStatistics: [NZFXS, NZFXSJCB]
  })
  export default class extends DistributionGoodsOpeningStatistics {}
</script>
