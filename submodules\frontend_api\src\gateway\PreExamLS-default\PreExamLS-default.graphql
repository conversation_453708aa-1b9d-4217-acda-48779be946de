schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取指定的考前培训方案
		@param schemeId 学习方案ID
	"""
	findById(schemeId:String):PreExamLSResponse
}
type Mutation {
	"""申请每日一练Token
		@param applyInfo 申请信息
	"""
	applyDailyPracticeLearningToken(applyInfo:ApplyDailyPracticeLearningTokenRequest):String
	"""申请考试Token
		@param applyInfo 申请信息
	"""
	applyExamLearningToken(applyInfo:ApplyExamLearningTokenRequest):String
	"""申请考纲结构易错题Token
		@param applyInfo 申请信息
	"""
	applyExamOutlineErrorPronePracticeLearningToken(applyInfo:ApplyExamOutlineErrorPronePracticeLearningTokenRequest):String
	"""申请考纲模式试题练习Token
		@param applyInfo 申请信息
	"""
	applyExamOutlinePracticeLearningToken(applyInfo:ApplyExamOutlinePracticeLearningTokenRequest):String
	"""申请试题方式易错题Token
		@param applyInfo 申请信息
	"""
	applyQuestionTypeErrorPronePracticeLearningToken(applyInfo:ApplyQuestionTypeErrorPronePracticeLearningTokenRequest):String
	"""申请随机抽题模式试题练习Token
		@param applyInfo 申请信息
	"""
	applyRandomPracticeLearningToken(applyInfo:ApplyRandomPracticeLearningTokenRequest):String
	"""申请课程学习Token
		@param applyInfo 申请信息
	"""
	applySingleCourseLearningToken(applyInfo:ApplySingleCourseLearningTokenRequest):String
	"""在指定的考前学习方案下新增期数
		@param createInfo 期数创建信息
	"""
	createIssue(createInfo:PreExamLSIssueCreateRequest):IssueAndCommoditySkuResponse
	"""创建一个考前学习方案
		@param createInfo 创建信息
	"""
	createLS(createInfo:PreExamLSCreateRequest):PreExamLSResponse
	"""发布考前学习方案
		@param schemeId 学习方案ID
	"""
	publishLS(schemeId:String):Void
	"""删除指定期数
		@param schemeId 学习方案ID
		@param issueId 期数ID
	"""
	removeIssue(schemeId:String,issueId:String):Void
	"""删除学习方案
		@param schemeId 学习方案ID
	"""
	removeLS(schemeId:String):Void
	"""更新考前学习方案指定的期数
		@param updateInfo 期数更新信息
	"""
	updateIssue(updateInfo:PreExamLSIssueUpdateRequest):IssueAndCommoditySkuResponse
	"""更新一个考前学习方案信息
		@param updateInfo 更新信息
	"""
	updateLS(updateInfo:PreExamLSUpdateRequest):PreExamLSResponse
}
input UpDownSettingsDto @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.api.dto.UpDownSettingsDto") {
	upping:Boolean!
	upPlainTime:DateTime
	downPlainTime:DateTime
}
"""每日一练学习Token信息"""
input ApplyDailyPracticeLearningTokenRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.ApplyDailyPracticeLearningTokenRequest") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
}
"""考试Token信息"""
input ApplyExamLearningTokenRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.ApplyExamLearningTokenRequest") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
}
"""考纲结构易错题学习Token"""
input ApplyExamOutlineErrorPronePracticeLearningTokenRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.ApplyExamOutlineErrorPronePracticeLearningTokenRequest") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
	"""章节ID"""
	chaptersId:String
}
"""申请考纲模式的练习学习Token"""
input ApplyExamOutlinePracticeLearningTokenRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.ApplyExamOutlinePracticeLearningTokenRequest") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
	"""章节ID"""
	chaptersId:String
	"""题类 null全部，real真题，practice练习题，simulation模拟题"""
	questionCategory:String
}
"""题型方式易错题学习Token"""
input ApplyQuestionTypeErrorPronePracticeLearningTokenRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.ApplyQuestionTypeErrorPronePracticeLearningTokenRequest") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
	"""专业ID"""
	professionId:String
	"""题型,-1全部,1判断题,2单选题,3多选题,6案例题"""
	questionType:Int!
}
input ApplyRandomPracticeLearningTokenRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.ApplyRandomPracticeLearningTokenRequest") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
	"""抽题模式,1:未答优先其次作答次数较少优先,2:未答,3:已答且次数较少优先"""
	extractMode:Int!
	"""抽题总数"""
	questionCount:Int!
	"""题型,-1全部,1判断题,2单选题,3多选题,6案例题"""
	questionType:Int!
}
"""申请课程学习Token信息"""
input ApplySingleCourseLearningTokenRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.ApplySingleCourseLearningTokenRequest") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
	"""课程包ID"""
	packageId:String
	"""课程ID"""
	courseId:String
}
"""课程学习方式设置信息"""
input CourseLearningSettingsRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.CourseLearningSettingsRequest") {
	"""是否启用课程学习方式"""
	enabled:Boolean!
	"""课程包ID集合"""
	packageIds:[String]
	"""是否仅系统推送"""
	systemPush:Boolean!
}
"""每日一练配置信息"""
input DailyPracticeSettingsRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.DailyPracticeSettingsRequest") {
	"""是否启用每日一练"""
	enabled:Boolean!
}
"""易错题练习配置"""
input ErrorPronePracticeSettingsRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.ErrorPronePracticeSettingsRequest") {
	"""是否启用易错题练习"""
	enabled:Boolean!
}
"""考试学习方式设置信息"""
input ExamLearningSettingsRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.ExamLearningSettingsRequest") {
	"""是否启用考试学习方式"""
	enabled:Boolean!
	"""考试名称"""
	name:String
	"""试卷ID"""
	examPaperId:String
	"""考试时长，单位分钟"""
	examTimeLength:Int!
	"""考试次数 0表示不限制次数"""
	examCount:Int!
	"""及格分数"""
	passScore:Double!
	"""是否开放试题解析"""
	openResolvedExam:Boolean!
	"""最短提交时长"""
	minSubmitTimeLength:Int!
}
input PreExamLSCreateRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.PreExamLSCreateRequest") {
	"""培训方案名称"""
	name:String
	"""考试类别ID"""
	examTypeId:String
	"""考试类别名称"""
	examTypeName:String
	"""专业ID"""
	professionId:String
	"""专业名称"""
	professionName:String
	"""年度"""
	year:Int!
	"""封面图片地址"""
	picture:String
	"""课程学习方式设置信息"""
	courseLearningSettings:CourseLearningSettingsRequest
	"""考试学习方式设置信息"""
	examLearningSettings:ExamLearningSettingsRequest
	"""试题练习学习方式配置"""
	questionPracticeLearningSettings:QuestionPracticeLearningSettingsRequest
	"""每日一练配置"""
	dailyPracticeSettings:DailyPracticeSettingsRequest
	"""易错题配置"""
	errorPronePracticeSettings:ErrorPronePracticeSettingsRequest
}
"""考前学习方案期数创建信息"""
input PreExamLSIssueCreateRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.PreExamLSIssueCreateRequest") {
	"""学习方案ID"""
	schemeId:String
	"""期数名称"""
	title:String
	"""培训开始时间"""
	startTime:DateTime
	"""培训结束时间"""
	endTime:DateTime
	"""线下考试时间"""
	offlineExamTime:DateTime
	"""是否开放报名"""
	openEnrolment:Boolean!
	"""排序"""
	sort:Int!
	"""销售价格"""
	price:BigDecimal
	"""上下架设置"""
	upDownSettings:UpDownSettingsRequest
}
"""考前学习方案期数更新信息"""
input PreExamLSIssueUpdateRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.PreExamLSIssueUpdateRequest") {
	"""要更新期数的学习方案ID"""
	schemeId:String
	"""要更新的期数ID"""
	issueId:String
	"""期数名称"""
	title:String
	"""培训开始时间"""
	startTime:DateTime
	"""培训结束时间"""
	endTime:DateTime
	"""线下考试时间"""
	offlineExamTime:DateTime
	"""排序"""
	sort:Int!
	"""是否开放报名"""
	openEnrolment:Boolean!
	"""销售价格"""
	price:BigDecimal
	"""上下架设置"""
	upDownSettings:UpDownSettingsDto
}
input PreExamLSUpdateRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.PreExamLSUpdateRequest") {
	"""学习方案ID"""
	schemeId:String
	"""培训方案名称"""
	name:String
	"""封面图片地址"""
	picture:String
	"""课程学习方式设置信息"""
	courseLearningSettings:CourseLearningSettingsRequest
	"""考试学习方式设置信息"""
	examLearningSettings:ExamLearningSettingsRequest
	"""试题练习学习方式配置"""
	questionPracticeLearningSettings:QuestionPracticeLearningSettingsRequest
	"""每日一练配置"""
	dailyPracticeSettings:DailyPracticeSettingsRequest
	"""易错题配置"""
	errorPronePracticeSettings:ErrorPronePracticeSettingsRequest
}
"""试题练习学习方式配置"""
input QuestionPracticeLearningSettingsRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.QuestionPracticeLearningSettingsRequest") {
	"""是否启用试题练习"""
	enabled:Boolean!
	"""抽题方式，1：指定考纲方式，2：按试题题型及抽题比例"""
	fetchWay:Int!
	"""是否开放真题"""
	openReal:Boolean!
	"""是否开放练习题"""
	openPractice:Boolean!
	"""是否开放模拟题"""
	openSimulation:Boolean!
	"""真题比率"""
	realRatioValue:Int!
	"""练习题比率"""
	practiceRatioValue:Int!
	"""模拟题比率"""
	simulationRatioValue:Int!
}
"""商品SKU上下架设置"""
input UpDownSettingsRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.request.UpDownSettingsRequest") {
	"""是否上架
		true: 如果商品SKU处于下架状态将进行立即上架
		false: 如果商品SKU处于上架状态将进行立即下架
	"""
	upping:Boolean!
	"""计划上架时间
		如果 upping 为 true 该值无效
	"""
	upPlainTime:DateTime
	"""计划下架时间
		如果 upping 为 false 该值无效
	"""
	downPlainTime:DateTime
}
"""课程学习方式信息"""
type CourseLearningResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.response.CourseLearningResponse") {
	"""学习方式ID"""
	learningId:String
	"""是否启用课程学习方式"""
	enabled:Boolean!
	"""课程包ID集合"""
	packageIds:[String]
	"""是否仅系统推送"""
	systemPush:Boolean!
}
"""每日一练信息"""
type DailyPracticeResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.response.DailyPracticeResponse") {
	"""学习方式ID"""
	learningId:String
	"""是否启用每日一练"""
	enabled:Boolean!
}
"""易错题练习信息"""
type ErrorPronePracticeResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.response.ErrorPronePracticeResponse") {
	"""学习方式ID"""
	learningId:String
	"""是否启用易错题练习"""
	enabled:Boolean!
}
"""考试学习方式信息"""
type ExamLearningResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.response.ExamLearningResponse") {
	"""学习方式ID"""
	learningId:String
	"""是否启用考试学习方式"""
	enabled:Boolean!
	"""考试名称"""
	name:String
	"""试卷ID"""
	examPaperId:String
	"""考试时长，单位分钟"""
	examTimeLength:Int!
	"""考试次数 0表示不限制次数"""
	examCount:Int!
	"""及格分数"""
	passScore:Double!
	"""是否开放试题解析"""
	openResolvedExam:Boolean!
	"""最短提交时长"""
	minSubmitTimeLength:Int!
}
"""期数和商品SKU信息"""
type IssueAndCommoditySkuResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.response.IssueAndCommoditySkuResponse") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
	"""商品SkuId"""
	commoditySkuId:String
	"""标题"""
	title:String
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
	"""线下考试时间"""
	offlineExamTime:DateTime
	"""是否开放报名"""
	openEnrolment:Boolean!
	"""销售价格"""
	price:BigDecimal
	"""上下架设置"""
	upDownSettings:UpDownSettingsResponse
}
"""考前学习方案信息"""
type PreExamLSResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.response.PreExamLSResponse") {
	"""学习方案ID"""
	schemeId:String
	"""培训方案名称"""
	name:String
	"""考试类别ID"""
	examTypeId:String
	"""专业ID"""
	professionId:String
	"""年度"""
	year:Int!
	"""封面图片地址"""
	picture:String
	"""课程学习方式信息"""
	courseLearning:CourseLearningResponse
	"""考试学习方式信息"""
	examLearning:ExamLearningResponse
	"""试题练习学习方式信息"""
	questionPracticeLearning:QuestionPracticeLearningResponse
	"""每日一练信息"""
	dailyPractice:DailyPracticeResponse
	"""易错题练习信息"""
	errorPronePractice:ErrorPronePracticeResponse
	"""创建人ID"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
}
"""试题练习学习方式信息"""
type QuestionPracticeLearningResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.response.QuestionPracticeLearningResponse") {
	"""学习方式ID"""
	learningId:String
	"""是否启用试题练习"""
	enabled:Boolean!
	"""抽题方式，1：指定考纲方式，2：按试题题型及抽题比例"""
	fetchWay:Int!
	"""是否开放真题"""
	openReal:Boolean!
	"""是否开放练习题"""
	openPractice:Boolean!
	"""是否开放模拟题"""
	openSimulation:Boolean!
	"""真题比率"""
	realRatioValue:Int!
	"""练习题比率"""
	practiceRatioValue:Int!
	"""模拟题比率"""
	simulationRatioValue:Int!
}
"""商品SKU上下架设置"""
type UpDownSettingsResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.preexam.kernel.gateway.graphql.response.UpDownSettingsResponse") {
	"""是否上架
		true: 如果商品SKU处于下架状态将进行立即上架
		false: 如果商品SKU处于上架状态将进行立即下架
	"""
	upping:Boolean!
	"""计划上架时间
		如果 upping 为 true 该值无效
	"""
	upPlainTime:DateTime
	"""计划下架时间
		如果 upping 为 false 该值无效
	"""
	downPlainTime:DateTime
}

scalar List
