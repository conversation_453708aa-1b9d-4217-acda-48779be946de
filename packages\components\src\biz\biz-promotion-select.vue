<route-meta>
{
"title": "分销推广类别选择器"
}
</route-meta>
<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    filterable
    clearable
  >
    <el-option v-for="item in options" :label="item.label" :value="item.value" :key="item.value"></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    selected: boolean = null

    options = [
      {
        label: '是',
        value: 1
      },
      {
        label: '否',
        value: 0
      }
    ]

    @Prop({
      type: String,
      default: '请选择是否为分销推广订单'
    })
    placeholder: string
    @Prop({
      type: Number,
      default: null
    })
    value: number

    /**
     * 监听参数变化
     * @param val
     */
    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: boolean) {
      this.selected = val
    }

    /**
     * 监听选择值
     */
    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }
  }
</script>
