import ReportConfigDto from '@api/service/management/implement/models/ReportConfigDto'
import AnnexInfoDto from '@api/service/management/implement/models/AnnexInfoDto'
import PeriodImplementBase from '@api/service/management/implement/models/PeriodImplementBase'
import AttendanceConfigDto from '@api/service/common/implement/models/AttendanceConfigDto'
import {
  IssueStudyConfigResponse,
  TeachResourceResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import Learningscheme, {
  AttendanceSettingRequest,
  AttendanceSignRequest,
  LearningResourceRequest,
  LearningResourceSettingRequest,
  SaveReportRuleSettingRequest
} from '@api/platform-gateway/dds-platform-learningscheme-v1'
import { ResponseStatus } from '@hbfe/common'
import AttendanceTemplateConfig from '@api/service/management/implement/AttendanceTemplateConfig'
import { cloneDeep } from 'lodash'

export default class PeriodConfig extends PeriodImplementBase {
  /**
   * 报道配置规则id
   */
  reportRuleId: string = undefined
  /*
   * 报到设置
   */
  reportConfig: ReportConfigDto = new ReportConfigDto()

  /**
   * 考勤配置id
   */
  attendanceSettingId: string = undefined

  /**
   * 签到配置
   */
  signIn: AttendanceConfigDto = new AttendanceConfigDto()

  /**
   * 签退配置
   */
  signOut: AttendanceConfigDto = new AttendanceConfigDto()

  /**
   * 学习资料配置id
   */
  learningSettingId: string = undefined

  /*
   * 附件列表
   */
  private annexList: Array<AnnexInfoDto> = new Array<AnnexInfoDto>()

  /*
   * 缓存附件列表 (缓存附件列表是给Ui操作使用的，因为ui有取消的动作，取消之后要将数据恢复所以要做缓存)
   */
  cacheAnnexList: Array<AnnexInfoDto> = new Array<AnnexInfoDto>()

  /**
   * 报道是否已设置过
   */
  reportSetted: boolean = undefined

  /**
   * 考勤是否已设置过
   */
  attendanceSetted: boolean = undefined

  /**
   * 学习资料是否已设置过
   */
  annexSetted: boolean = undefined

  constructor(periodId?: string) {
    super(periodId)
  }

  static from(dto: IssueStudyConfigResponse) {
    const vo = new PeriodConfig()
    const {
      schemeIssueResponse,
      trainingConfigResponse,
      reportRuleResponse,
      attendanceConfigResponse,
      teachResourceConfigResponse
    } = dto
    // 期别基础信息
    if (schemeIssueResponse) {
      vo.id = schemeIssueResponse.issueId
      vo.schemeId = schemeIssueResponse.schemeId
      vo.name = schemeIssueResponse.issueName
      vo.no = schemeIssueResponse.issueNum
      vo.schemeConfig.trainClassBaseInfo.id = schemeIssueResponse.schemeId
      vo.checkInTime.begin = schemeIssueResponse.startReportTimePeriod
      vo.checkInTime.end = schemeIssueResponse.endReportTimePeriod
      vo.trainingTime.begin = schemeIssueResponse.startTrainTime
      vo.trainingTime.end = schemeIssueResponse.endTrainTime
    }

    // 方案期别配置
    if (trainingConfigResponse) {
      vo.isSetAttendanceConfig = trainingConfigResponse.openAttendance
      vo.isSetReportConfig = trainingConfigResponse.openReport
    }

    // 签到配置
    if (attendanceConfigResponse) {
      vo.attendanceSetted = true
      vo.attendanceSettingId = attendanceConfigResponse.settingId
      const attendancesSignInConfig = attendanceConfigResponse.attendancesSignInConfig
      const attendancesSignOutConfig = attendanceConfigResponse.attendancesSignOutConfig
      if (attendancesSignInConfig) {
        vo.signIn.isOpen = attendancesSignInConfig.enable
        vo.signIn.checkInFrequency = attendancesSignInConfig.signInFrequencyType
        vo.signIn.checkInLocationRadius = attendancesSignInConfig.signInAddressRadius
        vo.signIn.preCheckInTime = attendancesSignInConfig.preSignTime
          ? attendancesSignInConfig.preSignTime / 60
          : attendancesSignInConfig.preSignTime
        vo.signIn.afterCheckInTime = attendancesSignInConfig.postSignTime
          ? attendancesSignInConfig.postSignTime / 60
          : attendancesSignInConfig.postSignTime
      }
      if (attendancesSignOutConfig) {
        vo.signOut.isOpen = attendancesSignOutConfig.enable
        vo.signOut.checkInFrequency = Number(attendancesSignOutConfig.signInFrequencyType)
        vo.signOut.checkInLocationRadius = attendancesSignOutConfig.signInAddressRadius
        vo.signOut.preCheckInTime = attendancesSignOutConfig.preSignTime
          ? attendancesSignOutConfig.preSignTime / 60
          : attendancesSignOutConfig.preSignTime
        vo.signOut.afterCheckInTime = attendancesSignOutConfig.postSignTime
          ? attendancesSignOutConfig.postSignTime / 60
          : attendancesSignOutConfig.postSignTime
      }
    } else {
      vo.attendanceSetted = false
    }

    // 报道配置
    if (reportRuleResponse) {
      // todo 接口缺少报道听课模板但一期不实现
      vo.reportRuleId = reportRuleResponse.reportRuleId
      vo.reportSetted = true
      vo.reportConfig.reportingType = reportRuleResponse.checkInType
      vo.reportConfig.morningClassTimeType = reportRuleResponse.amClassTimeType
      vo.reportConfig.afternoonClassTimeType = reportRuleResponse.pmClassTimeType
      vo.reportConfig.morningClassTime = reportRuleResponse.amClassTime
      vo.reportConfig.afternoonClassTime = reportRuleResponse.pmClassTime
      vo.reportConfig.validRange = reportRuleResponse.signRadiusRange
    } else {
      vo.reportSetted = false
    }

    // 学习资料配置
    if (teachResourceConfigResponse) {
      vo.annexSetted = true
      vo.learningSettingId = teachResourceConfigResponse.settingId
      if (teachResourceConfigResponse.teachResourceList?.length) {
        teachResourceConfigResponse.teachResourceList.map((item: TeachResourceResponse) => {
          vo.annexList.push(AnnexInfoDto.from(item))
          vo.cacheAnnexList.push(AnnexInfoDto.from(item))
        })
      }
    } else {
      vo.annexSetted = false
    }
    return vo
  }

  /**
   * 学习资料数量
   */
  get annexNum() {
    return this.annexList.length
  }

  /**
   * todo 保存实施配置（报道、考勤、学习资料）实际上修改的是方案，故需要跟方案一样先判断方案上一次的修改是否完成，完成后才可调用修改口，否则会报错
   * todo 调用完修改口后，需要轮询方案状态口直至方案修改完成
   * todo 以上两步因为涉及到计时器动作封装在实施的状态层里太重，故交给ui不放在这里，轮询状态层：src/service/management/train-class/Utils/SchemeStepProcess.ts
   */
  /*
   * 期别报道设置
   */
  async setReportPeriodInfo(ownerType?: number) {
    const request = new SaveReportRuleSettingRequest()
    request.learningSchemeId = this.schemeId
    request.ownerType = 3
    request.ownerId = this.id
    request.issueId = this.id
    request.signRadiusRange = this.reportConfig.validRange
    const res = await Learningscheme.saveReportRuleSetting(request)

    if (res?.data?.code == '200') {
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      if (res?.data?.code == 'E430') {
        return Promise.reject(new ResponseStatus(430, '培训报到时间已结束'))
      } else {
        return Promise.reject(new ResponseStatus(500, '系统异常'))
      }
    }
  }

  /**
   * 使用模板填充当前考勤配置
   * @param template 考勤配置模板
   * @param isUsed 是否已被使用
   */
  useAttendanceTemplate(template: AttendanceTemplateConfig, isUsed?: boolean) {
    if (!isUsed) {
      this.signIn = cloneDeep(template.signIn)
      this.signOut = cloneDeep(template.signOut)
    } else {
      this.signOut.checkInLocationRadius = template.signOut.checkInLocationRadius
      this.signOut.preCheckInTime = template.signOut.preCheckInTime
      this.signOut.afterCheckInTime = template.signOut.afterCheckInTime

      this.signIn.afterCheckInTime = template.signIn.afterCheckInTime
      this.signIn.afterCheckInTime = template.signIn.afterCheckInTime
      this.signIn.afterCheckInTime = template.signIn.afterCheckInTime
    }
  }

  /*
   * 期别考勤设置
   * 200 正常
   * 429 已有学员报名改方案/期别无法配置
   * 500 系统异常
   * 501 签到签退都未开启
   */
  async setAttendanceRules() {
    if (!this.signIn.isOpen && !this.signOut.isOpen) {
      // 签到签退都未开启
      return Promise.reject(new ResponseStatus(501, '保存考勤规则需至少开启签到或签退其中一个开关'))
    }
    const request = new AttendanceSettingRequest()
    // 期别子项保存配置固定为保存期别
    request.ownerTye = 3
    request.ownerId = this.id
    request.learningSchemeId = this.schemeId

    // 签到配置
    const signInRequest = new AttendanceSignRequest()
    signInRequest.enable = this.signIn.isOpen
    signInRequest.frequency = this.signIn.checkInFrequency
    signInRequest.radius = this.signIn.checkInLocationRadius
    signInRequest.beforeSecond = this.signIn.preCheckInTime
      ? this.signIn.preCheckInTime * 60
      : this.signIn.preCheckInTime
    signInRequest.afterSecond = this.signIn.afterCheckInTime
      ? this.signIn.afterCheckInTime * 60
      : this.signIn.afterCheckInTime
    request.signIn = signInRequest

    // 签退配置
    const signOutRequest = new AttendanceSignRequest()
    signOutRequest.enable = this.signOut.isOpen
    signOutRequest.frequency = this.signOut.checkInFrequency
    signOutRequest.radius = this.signOut.checkInLocationRadius
    signOutRequest.beforeSecond = this.signOut.preCheckInTime
      ? this.signOut.preCheckInTime * 60
      : this.signOut.preCheckInTime
    signOutRequest.afterSecond = this.signOut.afterCheckInTime
      ? this.signOut.afterCheckInTime * 60
      : this.signOut.afterCheckInTime
    request.signOut = signOutRequest

    const res = await Learningscheme.saveAttendanceSetting(request)

    if (res?.data?.code == '200') {
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      if (res?.data?.code == 'E429') {
        return Promise.reject(new ResponseStatus(429, '当前期别已有学员报名，无法设置考勤规则'))
      } else {
        return Promise.reject(new ResponseStatus(500, '系统异常'))
      }
    }
  }

  /**
   * 在缓存中添加学习资料
   * @param item
   */
  addCacheAnnexItem(item: AnnexInfoDto) {
    this.cacheAnnexList.push(item)
  }

  /**
   * 根据索引删除缓存中的资料
   * @param index 索引
   */
  deleteCacheAnnexByIndex(index: number) {
    this.cacheAnnexList = this.cacheAnnexList.splice(index, 1)
  }

  /*
   * 期别附件设置
   * 200 正常
   * 500 系统异常
   * 429 已有学员报班，无法设置
   */
  async setAnnex() {
    const request = new LearningResourceSettingRequest()

    // 期别子项保存配置固定为保存期别
    request.ownerTye = 3
    request.ownerId = this.id
    request.learningSchemeId = this.schemeId
    request.resourceList = new Array<LearningResourceRequest>()
    // 用缓存列表去设置
    this.cacheAnnexList.map(item => {
      const resource = new LearningResourceRequest()
      resource.filePath = item.path
      resource.name = item.name
      resource.format = item.format
      request.resourceList.push(resource)
    })

    const res = await Learningscheme.saveLearningResourceSetting(request)

    if (res?.data?.code == '200') {
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      if (res?.data?.code == 'E429') {
        return Promise.reject(new ResponseStatus(429, '当前期别已有学员报名，无法设置学习资料'))
      } else {
        return Promise.reject(new ResponseStatus(500, '系统异常'))
      }
    }
  }

  /*
   * 取消期别附件设置
   */
  async cancelAnnex() {
    this.cacheAnnexList = this.annexList.map(item => {
      return Object.assign(new AnnexInfoDto(), item)
    })
  }
}
