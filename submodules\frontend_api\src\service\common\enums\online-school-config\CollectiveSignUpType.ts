import AbstractEnum from '../AbstractEnum'
enum CollectiveSignUpTypeEnum {
  ONLINE = 1,
  OFFLINE = 2
}

export { CollectiveSignUpTypeEnum }
class CollectiveSignUpType extends AbstractEnum<CollectiveSignUpTypeEnum> {
  constructor() {
    super()
    this.map.set(CollectiveSignUpTypeEnum.ONLINE, '线上集体报名')
    this.map.set(CollectiveSignUpTypeEnum.OFFLINE, '线下集体报名')
  }
}
export default CollectiveSignUpType
