import { CoursewareSupplierResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage, {
  CourseInPackageRequest,
  CourseInPackageResponse,
  CourseInPackageResponsePage,
  CoursePackageRequest,
  CourserPackageSyncSchemeRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import * as GraphqlImporter from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage/graphql-importer'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import ResourceModule from '@api/service/management/resource/ResourceModule'
import CoursePackageCourseInfo from '@api/service/management/resource/course-package/models/CoursePackageCourseInfo'
import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'
import CoursePackageDetailVo from '@api/service/management/resource/course-package/query/vo/CoursePackageDetailVo'
import CoursePackageOperationLog from '@api/service/management/resource/course-package/query/vo/CoursePackageOperationLog'
import CoursePackageSyncLog from '@api/service/management/resource/course-package/query/vo/CoursePackageSyncLog'
import CoursePackageSyncSchemeInfo from '@api/service/management/resource/course-package/query/vo/CoursePackageSyncSchemeInfo'
import CourserPackageSyncSchemeQueryParam from '@api/service/management/resource/course-package/query/vo/CourserPackageSyncSchemeQueryParam'
import QueryCoursePackageListVo from '@api/service/management/resource/course-package/query/vo/QueryCoursePackageListVo'
import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'
import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'
import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
import { Page, UiPage, request } from '@hbfe/common'
import Mockjs from 'mockjs'

/**
 * 课程包查询对象
 */
class QueryCoursePackage {
  /**
   * 查询课程包分页
   * @param page
   * @param queryCoursePackageVo
   */
  async pageCoursePackage(
    page: UiPage,
    queryCoursePackageVo: QueryCoursePackageListVo
  ): Promise<Array<CoursePackageDetailVo>> {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursePackageInServicer({
      page: page,
      request: queryCoursePackageVo.to()
    })
    page.totalPageSize = result.data.totalPageSize
    page.totalSize = result.data.totalSize
    return result.data.currentPageData.map(CoursePackageDetailVo.from)
  }

  /**
   * 查询课程包集合
   * @param idList
   */
  async listCoursePackage(idList: Array<string>): Promise<Array<CoursePackageDetailVo>> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    const request = new CoursePackageRequest()
    request.coursePackageId = idList
    let result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursePackageInServicer({
      page: page,
      request
    })
    if (result.data.totalSize) {
      result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursePackageInServicer({
        page: page,
        request
      })
    }
    return result.data.currentPageData.map(CoursePackageDetailVo.from)
  }

  /**
   * 根据课程包id查询课程包
   */
  async queryCoursePackageById(coursePackageId: string) {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.getCoursePackageInServicer(
      coursePackageId
    )
    return CoursePackageDetailVo.from(result.data)
  }

  /**
   * 根据课程包名称查询课程包
   */
  async findCoursePackageByName(): Promise<CoursePackageDetailVo> {
    return new CoursePackageDetailVo()
  }

  /**
   * 查询课程包的同步统计信息
   * @param courserPackageSyncSchemeQueryParam
   */
  async queryCoursePackageSyncSummary(courserPackageSyncSchemeQueryParam: CourserPackageSyncSchemeQueryParam) {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.statisticCourserPackageSyncSchemeInServicer(
      courserPackageSyncSchemeQueryParam
    )
    return result.data
  }

  /**
   * 查询课程包下面的课程列表 --- 全量
   * @param coursePackageId
   */
  async queryCourseListInCoursePackageAll(coursePackageIds: string[]) {
    const promiseParam = []
    for (let i = 0; i < coursePackageIds.length; i++) {
      const element = coursePackageIds[i]
      promiseParam.push(this.queryCourseListInCoursePackageAuxiliary(element))
    }
    const promiseData = await Promise.all(promiseParam)
    const result: CourseInPackageResponse[] = []
    promiseData.forEach(item => {
      result.push(...item)
    })
    return result
  }
  /**
   * 查询课程包下面的课程列表 --- 辅助口
   * @param coursePackageId
   */
  private async queryCourseListInCoursePackageAuxiliary(coursePackageId: string) {
    const request = new CourseInPackageRequest()
    request.pageNo = 1
    request.pageSize = 1
    request.coursePackageId = coursePackageId
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInPackageV2InServicer(
      request
    )
    const rqList = new Array<CourseInPackageRequest>()
    for (let index = 0; index < result.data.totalSize / 200; index++) {
      const rq = new CourseInPackageRequest()
      rq.pageNo = 1 * (index + 1)
      rq.pageSize = 200
      rq.coursePackageId = coursePackageId
      rqList.push(rq)
    }
    const req = new RewriteGraph<CourseInPackageResponsePage, CourseInPackageRequest>(
      MsCourseLearningQueryFrontGatewayCourseLearningBackstage._commonQuery,
      GraphqlImporter.pageCourseInPackageV2InServicer
    )
    await req.request(rqList)
    const data: CourseInPackageResponse[] = []
    const keys = [...req.indexMap.keys()]
    keys.map((key, index) => {
      data.push(...req.indexMap.get(index).currentPageData)
    })
    return data
  }
  /**
   * 填充课程信息 --- 优化口
   * @param packageList
   * @private
   */
  async fillCourseListInfoPage(packageList: Array<CourseInPackageResponse>): Promise<Array<CourseInCoursePackage>> {
    const queryCourse = new QueryCourse()
    const courseList: Array<CourseListDetail> = await queryCourse.queryCourseByIdListPage(
      packageList.map((response: CourseInPackageResponse) => {
        return response.course.courseId
      })
    )
    let result = packageList.map((response: CourseInPackageResponse) => {
      const courseInPackage = new CourseInCoursePackage()
      courseInPackage.id = response.course.courseId
      courseInPackage.name = response.course.courseId
      courseInPackage.period = response.courseInPackage.period
      courseInPackage.sort = response.courseInPackage.sort
      const findOutItem = courseList.find((course: CourseListDetail) => {
        return course.id === courseInPackage.id
      })
      if (findOutItem?.courseProviderId) {
        courseInPackage.providerName = findOutItem?.courseProviderId
      }
      courseInPackage.name = findOutItem?.name
      courseInPackage.physicsPeriod = findOutItem?.period
      courseInPackage.createTime = findOutItem?.createTime
      return courseInPackage
    })
    const providerIds = result.map(res => res.providerName)
    let providerList: CoursewareSupplierResponse[] = []
    if (providerIds) {
      providerList = await queryCourse.batchQueryProvider(providerIds)
    }
    if (providerList && providerList.length) {
      result = result.map(res => this.fillProviderInfo(res, providerList))
    }
    return result
  }

  /**
   * 查询方案内课程包Map
   * @param coursePackageIdList 课程包id集合
   * @private
   */
  async queryCoursePackageListInScheme(coursePackageIdList: string[]): Promise<CoursePackageDetailVo[]> {
    let result = [] as CoursePackageDetailVo[]
    // 没有课程包id集合或者课程包id集合为空直接返回
    if (!coursePackageIdList || !coursePackageIdList.length) return result
    // 获取请求列表
    const reqList = Array(Math.ceil(coursePackageIdList.length / 200)).fill('')
    // 获取返回值列表
    const respList = await Promise.all(
      reqList.map(async (item, index) => {
        const tempReq = new CoursePackageRequest()
        tempReq.coursePackageId = coursePackageIdList.slice(index * 200, (index + 1) * 200)
        return MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursePackageInServicer({
          page: new Page(index + 1, 200),
          request: tempReq
        })
      })
    )
    // 组装课程包信息
    respList.forEach(tempResp => {
      if (tempResp.status?.isSuccess() && tempResp.data?.currentPageData && tempResp.data?.currentPageData?.length) {
        const metadata = tempResp.data.currentPageData
        result = metadata.map(CoursePackageDetailVo.from)
      }
    })
    return result
  }

  /**
   * 分页查询课程包下课程
   * @param page 分页
   * @param coursePackageId 课程包id
   */
  async pageQueryCourseListInCoursePackage(
    page: Page,
    coursePackageId: string,
    courseName?: string
  ): Promise<CourseInCoursePackage[]> {
    let result = [] as CourseInCoursePackage[]
    // 没有课程包id，直接返回
    if (!coursePackageId) {
      page.totalPageSize = 0
      page.totalSize = 0
      return result
    }
    const queryCourse = ResourceModule.courseFactory.queryCourse
    const request = new CourseInPackageRequest()
    request.pageNo = page.pageNo
    request.pageSize = page.pageSize
    request.coursePackageId = coursePackageId
    request.courseName = courseName
    const response = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInPackageV2InServicer(
      request
    )
    page.totalPageSize = response.data?.totalPageSize
    page.totalSize = response.data?.totalSize
    if (response.status?.isSuccess() && response.data?.currentPageData && response.data?.currentPageData?.length) {
      const metadata = response.data.currentPageData
      // 获取课程信息
      result = await this.fillCourseListInfo(metadata)
      /*// 获取课件供应商id集合
      const providerIdList = [...new Set(result.map(item => item.providerId).filter(Boolean))]
      const providerList = await queryCourse.batchQueryProvider(providerIdList)
      // 填充课件供应商信息
      result.forEach(item => {
        const providerInfo = providerList.find(subItem => subItem.servicerBase?.servicerId === item.providerId)
        if (!providerInfo) return
        item.providerName = providerInfo.servicerBase?.servicerName || ''
      })*/
    }
    // 查询课程包信息
    const coursePackage = await this.queryCoursePackageById(coursePackageId)
    result.forEach(item => {
      item.coursePackageName = coursePackage?.name || ''
    })
    console.log('###CourseListInCoursePackage', result)
    return result
  }

  /**
   * 根据课程包id查询课程包下课程列表
   */
  async queryCourseBaseListInCoursePackage(coursePackageId: string): Promise<CourseInCoursePackage[]> {
    const result = [] as CourseInCoursePackage[]
    const request = new CourseInPackageRequest()
    request.pageNo = 1
    request.pageSize = 1
    request.coursePackageId = coursePackageId
    const response = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInPackageV2InServicer(
      request
    )
    const rqList = new Array<CourseInPackageRequest>()
    for (let index = 0; index < response.data.totalSize / 200; index++) {
      const rq = new CourseInPackageRequest()
      rq.pageNo = 1 * (index + 1)
      rq.pageSize = 200
      rq.coursePackageId = coursePackageId
      rqList.push(rq)
    }

    // console.log(response, rqList)
    const req = new RewriteGraph<CourseInPackageResponsePage, CourseInPackageRequest>(
      MsCourseLearningQueryFrontGatewayCourseLearningBackstage._commonQuery,
      GraphqlImporter.pageCourseInPackageV2InServicer
    )
    await req.request(rqList)
    const keys = [...req.indexMap.keys()]
    const lastQueryResult = new Array<CourseInPackageResponse>()
    keys.map((key, index) => {
      lastQueryResult.push(...req.indexMap.get(index).currentPageData)
    })
    lastQueryResult.forEach((item: CourseInPackageResponse) => {
      const opt = new CourseInCoursePackage()
      opt.id = item.course?.courseId
      opt.sort = item.courseInPackage?.sort
      opt.period = item.courseInPackage?.period
      result.push(opt)
    })
    return result
  }

  /**
   * 根据课程id查询课程Map
   * @param courseIdList 课程id集合
   */
  async queryCourseMapById(courseIdList: string[]): Promise<Map<string, CourseInCoursePackage>> {
    const resultMap = new Map<string, CourseInCoursePackage>()
    if (!courseIdList.length) return resultMap
    const queryCourse = new QueryCourse()
    const courseList: Array<CourseListDetail> = await queryCourse.queryCourseByIdList([
      ...new Set(courseIdList.map(item => item).filter(Boolean))
    ])
    let providerList = [] as CoursewareSupplierResponse[]
    const providerIdList = [...new Set(courseList.map(item => item.courseProviderId).filter(Boolean))]
    if (providerIdList && providerIdList.length) {
      providerList = await queryCourse.batchQueryProvider(providerIdList)
    }
    courseIdList.forEach(item => {
      const target = courseList.find(subItem => subItem.id === item)
      if (!target) return
      const course = new CourseInCoursePackage()
      course.id = target.id
      course.name = target.name
      course.physicsPeriod = target.period
      course.createTime = target.createTime
      course.providerId = target.courseProviderId
      course.providerName = providerList.find(
        ele => ele.servicerBase?.servicerId === target.courseProviderId
      )?.servicerBase?.servicerName
      resultMap.set(item, course)
    })
    return resultMap
  }

  /**
   * 查询课程包下面的课程列表
   * @param coursePackageId
   */
  async queryCourseListInCoursePackage(coursePackageId: string) {
    const queryCourse = ResourceModule.courseFactory.queryCourse
    const request = new CourseInPackageRequest()
    request.pageNo = 1
    request.pageSize = 1
    request.coursePackageId = coursePackageId
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInPackageV2InServicer(
      request
    )
    const rqList = new Array<CourseInPackageRequest>()
    for (let index = 0; index < result.data.totalSize / 200; index++) {
      const rq = new CourseInPackageRequest()
      rq.pageNo = 1 * (index + 1)
      rq.pageSize = 200
      rq.coursePackageId = coursePackageId
      rqList.push(rq)
    }

    console.log(result, rqList)
    const req = new RewriteGraph<CourseInPackageResponsePage, CourseInPackageRequest>(
      MsCourseLearningQueryFrontGatewayCourseLearningBackstage._commonQuery,
      GraphqlImporter.pageCourseInPackageV2InServicer
    )
    await req.request(rqList)
    const keys = [...req.indexMap.keys()]
    const lastQueryResult = new Array<CourseInPackageResponse>()
    keys.map((key, index) => {
      lastQueryResult.push(...req.indexMap.get(index).currentPageData)
    })
    let temp = await this.fillCourseListInfo(lastQueryResult)
    const providerIds = temp.map(res => res.providerName)
    let providerList: CoursewareSupplierResponse[] = []
    if (providerIds) {
      providerList = await queryCourse.batchQueryProvider(providerIds)
    }
    if (providerList && providerList.length) {
      temp = temp.map(res => this.fillProviderInfo(res, providerList))
    }
    return temp
  }

  /**
   * 填充课程信息
   * @param packageList
   * @private
   */
  async fillCourseListInfo(packageList: Array<CourseInPackageResponse>): Promise<Array<CourseInCoursePackage>> {
    const queryCourse = new QueryCourse()
    const courseList: Array<CourseListDetail> = await queryCourse.queryCourseByIdList(
      packageList.map((response: CourseInPackageResponse) => {
        return response.course.courseId
      })
    )

    return packageList.map((response: CourseInPackageResponse) => {
      const courseInPackage = new CourseInCoursePackage()
      courseInPackage.id = response.course.courseId
      courseInPackage.name = response.course.courseId
      courseInPackage.period = response.courseInPackage.period
      courseInPackage.sort = response.courseInPackage.sort
      const findOutItem = courseList.find((course: CourseListDetail) => {
        return course.id === courseInPackage.id
      })
      if (findOutItem?.courseProviderId) {
        courseInPackage.providerId = findOutItem?.courseProviderId
        courseInPackage.providerName = findOutItem?.courseProviderId
      }
      courseInPackage.name = findOutItem?.name
      courseInPackage.physicsPeriod = findOutItem?.period
      courseInPackage.createTime = findOutItem?.createTime
      return courseInPackage
    })
  }

  // * 填充课件供应商信息
  private fillProviderInfo(dto: CourseInCoursePackage, providerList: CoursewareSupplierResponse[]) {
    if (dto.providerName) {
      dto.providerName =
        providerList.find(res => res.servicerBase.servicerId === dto.providerName)?.servicerBase?.servicerName || ''
    }
    return dto
  }

  /**
   * 分页查询课程包已选课程
   * @param page
   * @param coursePackageId
   */
  async queryCoursePackageCoursePage(page: UiPage, coursePackageId: string) {
    const request = new CourseInPackageRequest()
    request.coursePackageId = coursePackageId
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInPackageInServicer({
      page,
      request
    })
    page.totalPageSize = result.data?.totalPageSize
    page.totalSize = result.data?.totalSize
    return this.fillCourseListInfo(result.data.currentPageData)
  }

  /**
   * 分页查询课程包被使用的列表
   * @param page
   * @param request
   */
  async queryCoursePackageSyncList(
    page: UiPage,
    request: CourserPackageSyncSchemeRequest
  ): Promise<Array<CoursePackageSyncSchemeInfo>> {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourserPackageSyncSchemeInServicer(
      {
        page,
        request
      }
    )
    page.totalPageSize = result.data?.totalPageSize
    page.totalSize = result.data?.totalSize
    const temp = CoursePackageSyncSchemeInfo.from(result.data.currentPageData)
    const schemeIds = [...new Set(temp.map(item => item.id))]
    if (schemeIds && schemeIds.length) {
      const queryM = new QueryTrainClassCommodityList()
      const schemeNameMap = await queryM.batchQuerySchemeNameMapBySchemeId(schemeIds)
      temp.forEach(item => {
        if (!item.id) return
        item.name = schemeNameMap.get(item.id) || ''
      })
    }
    return temp
  }

  /**
   * 获取课程包修改记录
   */
  async queryCoursePackageOperationLogList() {
    return Mockjs.mock({
      'data|10': [
        {
          id: '@uuid',
          logTime: '@date("yyyy-MM-dd HH:mm:ss")',
          status: '@integer(0, 1)',
          message: Mockjs.mock({
            'data|10': ['@csentence(50)']
          }).data
        }
      ]
    }).data.map(CoursePackageOperationLog.from)
  }

  /**
   * 查询课程包的同步日志
   * @param coursePackageId
   */
  async queryCoursePackageSyncLog(coursePackageId: string): Promise<Array<CoursePackageSyncLog>> {
    console.log(coursePackageId)
    return Mockjs.mock({
      'data|10': [
        {
          id: '@uuid',
          logTime: '@date("yyyy-MM-dd HH:mm:ss")',
          status: '@integer(0, 1)',
          message: '@csentence(20)'
        }
      ]
    }).data.map(CoursePackageSyncLog.from)
  }

  /**
   * 分页查询课程包下指定课程列表
   * @param page 分页参数
   * @param coursePackageId 课程包id
   * @param courseIdList 课程id集合
   */
  async pageQueryCourseListInCoursePackageByCourseIdList(
    page: Page,
    coursePackageId: string,
    courseIdList: string[]
  ): Promise<CourseInCoursePackage[]> {
    // TODO LWF
    const result = new Array<CourseInCoursePackage>()
    const request = new CourseInPackageRequest()
    request.pageNo = page.pageNo
    request.pageSize = page.pageSize
    request.coursePackageId = coursePackageId
    request.courseIds = courseIdList
    if (!courseIdList?.length) return result
    const response = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInPackageV2InServicer(
      request
    )
    page.totalPageSize = response.data?.totalPageSize
    page.totalSize = response.data?.totalSize
    response.data.currentPageData.map(item => {
      const course = new CourseInCoursePackage()
      course.period = item.courseInPackage.period
      course.id = item.course.courseId
      course.name = item.course.courseName
      course.coursePackageId = coursePackageId
      result.push(course)
    })
    return result
  }

  /**
   * 查询课程包下指定课程列表的统计学时
   * @param coursePackageInfo 课程包下课程信息
   * @return Map结构
   * {
   *   课程包id：指定课程列表的统计学时
   * }
   */
  async queryCourseStatisticInCoursePackageByCourseIdList(
    coursePackageInfo: CoursePackageCourseInfo[]
  ): Promise<Map<string, number>> {
    // TODO LWF
    const result = new Map<string, number>()
    return result
  }

  /**
   * 查询课程包下面的课程列表 by <课程包id & 课程id集合>
   * @param params 入参
   */
  async queryCourseListByCoursePackageAndCourseId(coursePackageId: string, courseIds: string[]) {
    const rqList = new Array<CourseInPackageRequest>()
    for (let index = 0; index < courseIds.length / 200; index++) {
      const rq = new CourseInPackageRequest()
      rq.pageNo = 1
      rq.pageSize = 200
      rq.coursePackageId = coursePackageId
      rq.courseIds = courseIds.slice(index * 200, (index + 1) * 200)
      rqList.push(rq)
    }

    const req = new RewriteGraph<CourseInPackageResponsePage, CourseInPackageRequest>(
      MsCourseLearningQueryFrontGatewayCourseLearningBackstage._commonQuery,
      GraphqlImporter.pageCourseInPackageV2InServicer
    )
    await req.request(rqList)
    const keys = [...req.indexMap.keys()]
    const lastQueryResult = new Array<CourseInPackageResponse>()
    keys.map((key, index) => {
      lastQueryResult.push(...req.indexMap.get(index).currentPageData)
    })
    return lastQueryResult.map(item => {
      const course = new CourseInCoursePackage()
      course.id = item.course.courseId
      course.name = item.course.courseName
      course.coursePackageId = coursePackageId
      return course
    })
  }
  /**
   * 查询课程包下面的课程列表 by <课程包id & 课程id集合> []
   * @param params 入参
   */
  async queryCourseListByCoursePackageAndCourseIdList(params: { coursePackageId: string; courseIds: string[] }[]) {
    const courseMap = new Map<string, CourseInCoursePackage[]>()
    const courseList = new Array<CourseInCoursePackage>()
    await Promise.all(
      params.map(async item => {
        const result = await this.queryCourseListByCoursePackageAndCourseId(item.coursePackageId, item.courseIds)
        courseMap.set(item.coursePackageId, result)
        courseList.push(...result)
      })
    )
    return {
      courseList,
      courseMap
    }
  }
}

export default QueryCoursePackage
