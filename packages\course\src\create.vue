<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/course')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/course' }">课程管理</el-breadcrumb-item>
      <el-breadcrumb-item>新建课程</el-breadcrumb-item>
    </el-breadcrumb>
    <select-courseware :show-trigger="false" :pre-selected="arrAy" ref="selectCoursewareRef"></select-courseware>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form
                ref="createCourseForm"
                :model="mutationCreateCourse.createCourse"
                :rules="rules"
                label-width="7.5rem"
                class="m-form"
              >
                <el-form-item label="课程封面：" prop="picture" ref="coursePicture">
                  <cropper-img-upload
                    :dialogStyleOpation="{
                      width: '500px',
                      height: '300px'
                    }"
                    :ratioArr="['16:9']"
                    :initWidth="400"
                    title="课程封面"
                    mode="400px 225px"
                    v-model="mutationCreateCourse.createCourse.picture"
                  ></cropper-img-upload>
                </el-form-item>

                <el-form-item label="课程名称：" prop="name">
                  <el-input v-model="mutationCreateCourse.createCourse.name" clearable placeholder="请输入课程名称" />
                </el-form-item>
                <el-form-item label="课程分类：" prop="categoryId" ref="courseCategory">
                  <biz-course-category
                    :check-strictly="false"
                    v-model="mutationCreateCourse.createCourse.categoryId"
                  ></biz-course-category>
                </el-form-item>
                <!-- <el-form-item label="课件供应商：" prop="supplierId" ref="supplierIdCategory">
                  <biz-courseware-supplier v-model="supplierId"></biz-courseware-supplier>
                </el-form-item> -->
                <el-form-item label="课程简介：" prop="description">
                  <hb-tinymce-editor
                    v-model="mutationCreateCourse.createCourse.description"
                    v-if="show"
                  ></hb-tinymce-editor>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">配置课程</span>
        </div>

        <div class="f-p30">
          <el-row type="flex" justify="center">
            <el-col :lg="22" :xl="20">
              <el-form ref="form" :model="form" label-width="auto" class="m-form">
                <el-form-item label="课程内容：">
                  <p class="f-fb">
                    <i class="f-dot f-mr5"></i>
                    <span class="f-f15">课程目录</span>
                  </p>
                  <template v-for="(chapter, index) in mutationCreateCourse.createCourse.chapters">
                    <el-table
                      :key="chapter.id"
                      stripe
                      :data="chapter.coursewares"
                      max-height="500px"
                      class="m-table f-mt10 f-mb20"
                      ref="dragWebTable"
                    >
                      <el-table-column label="排序" width="70" align="center">
                        <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
                      </el-table-column>
                      <el-table-column label="章节1" min-width="300" :render-header="renderFirstHeader(chapter)">
                        <template slot-scope="scope">
                          <div class="f-flex f-align-center">
                            <i class="el-icon-video-play f-f20 f-c9"></i>
                            <el-input v-model="scope.row.cacheName" v-if="scope.row.editMode"></el-input>
                            <span class="f-ml5" v-else>
                              {{ scope.row.name }}
                            </span>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column width="300">
                        <template>
                          <el-progress :percentage="100"></el-progress>
                        </template>
                      </el-table-column>
                      <el-table-column min-width="100" align="center">
                        <template slot-scope="{ row }">
                          {{ row.timeLengthFormat }}
                        </template>
                      </el-table-column>
                      <el-table-column width="100" align="center">
                        <template slot-scope="{ row }">
                          <el-checkbox v-model="row.trialType" :true-label="1" :false-label="0">试听</el-checkbox>
                        </template>
                      </el-table-column>
                      <el-table-column width="140" align="center" fixed="right">
                        <template slot="header">
                          <el-button type="text" size="mini" @click="showCatalogName(index, chapter)">重命名</el-button>
                          <el-button type="text" size="mini" @click="removeChapter(chapter)">删除</el-button>
                        </template>
                        <template slot-scope="{ row, $index }">
                          <!--                          <template v-if="!row.editMode">
                            <el-button type="text" size="mini" @click="row.edit()">
                              重命名
                            </el-button>
                          </template>
                          <template v-else>
                            <el-button type="text" size="mini" @click="row.save()">保存</el-button>
                            <el-button type="text" size="mini" @click="row.cancel()">取消</el-button>
                          </template>-->
                          <el-button type="text" size="mini" @click="chapter.removeCourseware($index)">
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </template>
                  <hb-empty
                    v-if="!mutationCreateCourse.createCourse.chapters.length"
                    show-action
                    action-text="添加目录"
                    description="未添加课程目录，请点击添加目录！"
                    @action="showCatalogName()"
                  ></hb-empty>
                  <el-button
                    v-if="mutationCreateCourse.createCourse.chapters.length"
                    type="primary"
                    plain
                    icon="el-icon-folder-add"
                    @click="showCatalogName()"
                  >
                    添加目录
                  </el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="doCancel">取消</el-button>
        <el-button type="primary" @click="doSave" :loading="loading">保存</el-button>
      </div>
    </div>
    <el-drawer :title="curTitle" :visible.sync="catalogNameShow" size="600px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form ref="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="目录名称：" required>
                <el-input v-model="currentCatalogName" maxlength="100" clearable placeholder="请输入目录名称" />
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="catalogNameShow = false">取消</el-button>
                <el-button type="primary" @click="addChapter">保存</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
    <give-up-dialog :give-up-model="uiConfig.giveUpModel"></give-up-dialog>
  </el-main>
</template>

<script lang="tsx">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import SelectCourseware from '@hbfe/jxjy-admin-course/src/components/select-courseware.vue'
  import ChapterVo from '@api/service/management/resource/course/mutation/vo/Chapter'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import CropperImgUpload from '@hbfe/jxjy-admin-components/src/cropper-img-upload.vue'
  import Chapter from '@api/service/management/resource/course/mutation/vo/Chapter'
  import CoursewareListDetail from '@api/service/management/resource/courseware/query/vo/CoursewareListDetail'
  import MutationCreateCourse from '@api/service/management/resource/course/mutation/MutationCreateCourse'
  import Sortable from 'sortablejs'
  import { ElTable } from 'element-ui/types/table'
  @Component({
    components: {
      SelectCourseware,
      GiveUpDialog,
      CropperImgUpload
    }
  })
  export default class extends Vue {
    @Ref('createCourseForm') createCourseForm: any
    rules = {
      picture: [{ required: true, message: '课程封面不可为空！', trigger: 'blur' }],
      name: [
        { required: true, message: '课程名称不可为空！', trigger: ['change', 'blur'] },
        {
          validator: async (rule: any, value: any, callback: any) => {
            await this.checkName(rule, value, callback)
          },
          trigger: 'blur'
        }
      ],
      categoryId: [{ required: true, message: '课程分类不可为空！', trigger: ['change', 'blur'] }]
      //supplierId: [{ required: true, message: '请选择课件供应商', trigger: ['change', 'blur'] }]
    }
    checked = false
    mutationCreateCourse: MutationCreateCourse = ResourceModule.courseFactory.createCourse
    dialogImageUrl = ''
    dialogVisible = false
    detailsId = ''

    catalogNameShow = false
    currentCatalogName = ''
    curTitle = '添加目录'
    curChapter = new Chapter()

    supplierId = new Array<string>()

    async checkName(rule: any, value: any, callback: any) {
      const res: any = await ResourceModule.courseFactory.getCheckCourse(value)
      if (res?.code === '3000') {
        return callback('课程名称重复，请修改')
      }
      return callback()
    }

    //防抖
    loading = false

    // 富文本有时候显示不出来
    show = false
    form = {}
    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }

    @Watch('createCourseVo', {
      immediate: true,
      deep: true
    })
    createCourseVoChanged(val: any) {
      console.log('createCourseVo', val)
    }

    @Watch('mutationCreateCourse.createCourse.picture', {
      immediate: true,
      deep: true
    })
    pictureChange(val: string) {
      const el: any = this.$refs['coursePicture']
      if (el) {
        if (val) {
          //有图片时清除校验
          el.clearValidate()
        } else {
          el.validate()
        }
      }
    }

    get selectCourseWareList() {
      const list = new Array<string>()
      this.mutationCreateCourse.createCourse.chapters.map((item) => {
        list.push(...item.getSelectedIdList())
      })
      return list
    }

    @Watch('mutationCreateCourse.createCourse.categoryId', {
      immediate: true,
      deep: true
    })
    categoryIdChange(val: Array<string>) {
      const el: any = this.$refs['courseCategory']
      if (el) {
        if (val?.length) {
          el.clearValidate()
        } else {
          el.validate()
        }
      }
    }

    @Ref('dragWebTable')
    dragWebTable: any

    @Watch('mutationCreateCourse.createCourse.chapters', { immediate: true, deep: true })
    onChangeDrag(val: any) {
      if (this.dragWebTable) this.dragWebTable.map((item: any, index: number) => this.dragElement(item, index))
    }
    dragElement(table: ElTable, index: number) {
      const el = table.$el.querySelector('.el-table__body-wrapper > table > tbody') as HTMLElement
      return new Sortable(el, {
        ghostClass: 'blue-background-class',
        handle: '.hb-iconfont',
        onEnd: ({ newIndex, oldIndex }) => {
          const curRow = this.mutationCreateCourse.createCourse.chapters[index].coursewares.splice(oldIndex, 1)[0]
          this.mutationCreateCourse.createCourse.chapters[index].coursewares.splice(newIndex, 0, curRow)
          const newArray = this.mutationCreateCourse.createCourse.chapters[index].coursewares.slice(0)
          this.mutationCreateCourse.createCourse.chapters[index].coursewares = []
          this.$nextTick(function () {
            this.mutationCreateCourse.createCourse.chapters[index].coursewares = newArray
          })
        }
      })
    }

    showCatalogName(index?: number, item?: Chapter) {
      if (!item) {
        this.curTitle = '添加目录'
        this.currentCatalogName = ''
        this.curChapter = new Chapter()
      } else {
        this.curTitle = '重命名'
        this.currentCatalogName = item.name
        this.curChapter = item
      }
      this.catalogNameShow = true
    }

    /**
     * 添加/重命名目录
     */
    addChapter() {
      if (!this.currentCatalogName) {
        this.$message.warning('请输入目录名称')
        return
      }
      if (this.curTitle === '添加目录') {
        const chapter = new Chapter()
        chapter.name = this.currentCatalogName
        this.mutationCreateCourse.createCourse.chapters.push(chapter)
      } else {
        this.curChapter.name = this.currentCatalogName
      }
      this.catalogNameShow = false
    }

    removeChapter(chapter: Chapter) {
      if (chapter.coursewares.length) {
        this.$message.error('请先移除课件后，才可删除')
        return
      }
      this.mutationCreateCourse.createCourse.chapters = this.mutationCreateCourse.createCourse.chapters.filter(
        (n) => !Object.is(chapter, n)
      )
    }

    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
      this.uiConfig.giveUpModel.routerUrl = '/resource/course'
    }

    doSave() {
      this.createCourseForm.validate((valid: boolean) => {
        //基础信息表单校验
        if (valid) {
          //配置课程校验
          if (!this.mutationCreateCourse.createCourse?.chapters.length) {
            this.$message.warning('每一个课程至少一个课程目录，请添加课程目录')
            return
          } else {
            if (this.mutationCreateCourse.createCourse?.chapters.some((chapter) => chapter.coursewares.length === 0)) {
              this.$message.warning('每一个目录下至少一个课件，请添加课件')
              return
            }
          }
          this.doCreate()
        }
      })
    }

    async doCreate() {
      this.loading = true
      this.mutationCreateCourse?.createCourse?.chapters.length &&
        this.mutationCreateCourse.createCourse.chapters.map((item, index) => {
          item.sort = index
          item?.coursewares?.length &&
            item.coursewares.map((courseItem, index) => {
              courseItem.sort = index
            })
        })
      try {
        const res = await this.mutationCreateCourse.doCreate()
        if (res.code === 200) {
          this.$message.success('创建成功')
          // 加延时器，解决列表数据延迟问题
          setTimeout(() => {
            //   this.loading = false
            this.$router.push('/resource/course')
          }, 1000)
        } else {
          this.$message.warning('创建失败')
          this.loading = false
        }
      } catch (e) {
        console.log(e)
        this.loading = false
      }
    }

    async activated() {
      this.show = false
      setTimeout(() => {
        this.show = true
      }, 300)
    }

    handleRemove(file: any, fileList: any) {
      console.log(file, fileList)
    }

    renderFirstHeader(chapter: ChapterVo) {
      return () => {
        return (
          <div>
            <span>{chapter.name}</span>
            <el-button
              type="primary"
              size="mini"
              plain
              class="f-ml10"
              onClick={this.addWaitSelectCoursewareChapter(chapter)}
            >
              添加多媒体
            </el-button>
          </div>
        )
      }
    }

    waitSelectCoursewareChapter: ChapterVo = new ChapterVo()
    arrAy: string[] = []
    addWaitSelectCoursewareChapter(chapter: ChapterVo) {
      return () => {
        this.waitSelectCoursewareChapter = chapter
        this.arrAy = chapter.coursewares.map((item) => item.id)
        const selectCoursewareRef = this.$refs.selectCoursewareRef as SelectCourseware
        selectCoursewareRef.open()
        selectCoursewareRef.$once(
          'confirm',
          (selectedItems: Array<CoursewareListDetail>, cencelItems: Array<CoursewareListDetail>) => {
            this.waitSelectCoursewareChapter.addCourseware(selectedItems, false, cencelItems)
            selectCoursewareRef.close()
          }
        )
      }
    }

    handlePictureCardPreview(file: any) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-upload i {
    font-size: 18px;
  }
</style>
