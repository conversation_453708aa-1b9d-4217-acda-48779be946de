import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-basicdata-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum TimeUnitEnum {
  YEARS = 'YEARS',
  MONTHS = 'MONTHS',
  DAYS = 'DAYS',
  HOURS = 'HOURS'
}
export enum EnterprisePersonSortFieldEnum {
  createdTime = 'createdTime',
  accountType = 'accountType',
  userNameFirstLetter = 'userNameFirstLetter',
  nature = 'nature'
}
export enum EnterpriseUnitSortFieldEnum {
  createdTime = 'createdTime',
  unitName = 'unitName',
  pinyinName = 'pinyinName',
  regionId = 'regionId'
}
export enum GovernmentUnitSortFieldEnum {
  unitRegionPath = 'unitRegionPath',
  unitName = 'unitName',
  createdTime = 'createdTime'
}
export enum PersonAccountSortFieldEnum {
  createdTime = 'createdTime',
  nature = 'nature'
}
export enum ServicerSortFieldEnum {
  createdTime = 'createdTime'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 功能描述：账户信息查询条件
@Author： wtl
@Date： 2022年5月11日 15:30:56
 */
export class AccountRequest {
  /**
   * 账户状态 1：正常，2：冻结
@see AccountStatus
   */
  statusList?: Array<number>
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest
  /**
   * 创建人用户id
   */
  createdUserId?: string
  /**
   * 账户类型 1：企业帐户，2：企业个人帐户，3：个人帐户
   */
  accountTypeList?: Array<number>
  /**
   * 单位id （原始单位id，不会随着id 人员与单位的关系变化而变化）
   */
  unitIdList?: Array<string>
  /**
   * 单位id匹配方式 默认-1、and匹配 2、or匹配
@see MatchTypeConstant
   */
  unitIdMatchType?: number
  /**
   * 来源类型
0-内置 1-项目主网站 2-安卓 3-IOS 4-后台导入 5-迁移数据
   */
  sourceTypes?: Array<number>
}

/**
 * 功能描述：账户用户信息查询条件
@Author： linq
@Date： 2024年05月09日 15:05
 */
export class AccountUserInfoQueryRequest {
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: StudentUserRequest
  /**
   * 用户认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 集体缴费信息
   */
  collective?: CollectiveRequest
  /**
   * 人员信息
   */
  person?: PersonRequest
  /**
   * 排序
   */
  sortList?: Array<StudentSortRequest>
}

/**
 * 功能描述：登录认证查询条件
@Author： wtl
@Date： 2022年1月26日 09:30:12
 */
export class AuthenticationRequest {
  /**
   * 帐号
   */
  identity?: string
  /**
   * 用户名
   */
  userName?: string
}

/**
 * 功能描述 : 直方图统计查询条件
 */
export class DateHistogramRequest {
  /**
   * 开始时间（必填）
   */
  startTime?: string
  /**
   * 结束时间（必填）
   */
  endTime?: string
  /**
   * 时间单位枚举（必填）
   */
  timeUnit?: TimeUnitEnum
}

/**
 * 功能描述：人员查询条件
 */
export class PersonRequest {
  /**
   * 业务类型，
@see PersonUnitRelationshipBusinessTypes
   */
  businessType?: number
  /**
   * 关系类型（标识人与哪个实体产生的关系）unit或department
@see PersonRelationTypeConstant
   */
  relationType?: string
  /**
   * 关系值（业务类型为组织关系时，目前该值为单位id或部门id；业务类型为主要经办或经办，该值为单位id）
   */
  relationValue?: string
  /**
   * 关系状态，1正常，0冻结
@see PersonUnitRelationshipStatus
   */
  status?: number
}

/**
 * 功能描述：角色查询条件
@Author： wtl
@Date： 2022年5月11日 11:46:41
 */
export class RoleRequest {
  /**
   * 角色id集合
   */
  roleIdList?: Array<string>
  /**
   * 角色类型
（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
   */
  roleTypeList?: Array<string>
  /**
   * 角色类别
（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管 10:人社超管
11:人社职建处 12:人社就业局 13:超级管理员 14:合同供应商 15:专家 16:电子劳动合同业务角色 320:地区管理员 410:人社行业管理员
411:人社行业省级管理员 412:人社行业市级管理员 413:人社行业区县级管理员 420:人社地区管理员 430:人社业务管理员 440:省级人社主管 450:市级人社主管
460:区县级人社主管 510:培训机构管理员 520:技工院校管理员 530:职业院校管理员 540:政策参与者 550:线上培训机构管理员 560:课件供应商
5001:学徒制_企业管理员 5101:学徒制_培训机构管理员 5201:学徒制_技工院校管理员 5301:学徒制_职业院校管理员 4011:学徒制_人社_省级管理员
4021:学徒制_人社_市级管理员 4031:学徒制_人社_区/县级管理员 6001:揭榜挂帅_企业管理员 6101:揭榜挂帅_培训机构管理员
6201:揭榜挂帅_技工院校管理员 6301:揭榜挂帅_职业院校管理员 6401:揭榜挂帅_人社_省级管理员 6402:揭榜挂帅_人社_市级管理员 6403:揭榜挂帅_人社_区/县级管理员）
@see RoleCategories
   */
  roleCategoryList?: Array<number>
  /**
   * 排除的角色类别集合
（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管 10:人社超管
11:人社职建处 12:人社就业局 13:超级管理员 14:合同供应商 15:专家 16:电子劳动合同业务角色 320:地区管理员 410:人社行业管理员
411:人社行业省级管理员 412:人社行业市级管理员 413:人社行业区县级管理员 420:人社地区管理员 430:人社业务管理员 440:省级人社主管 450:市级人社主管
460:区县级人社主管 510:培训机构管理员 520:技工院校管理员 530:职业院校管理员 540:政策参与者 550:线上培训机构管理员 560:课件供应商
5001:学徒制_企业管理员 5101:学徒制_培训机构管理员 5201:学徒制_技工院校管理员 5301:学徒制_职业院校管理员 4011:学徒制_人社_省级管理员
4021:学徒制_人社_市级管理员 4031:学徒制_人社_区/县级管理员 6001:揭榜挂帅_企业管理员 6101:揭榜挂帅_培训机构管理员
6201:揭榜挂帅_技工院校管理员 6301:揭榜挂帅_职业院校管理员 6401:揭榜挂帅_人社_省级管理员 6402:揭榜挂帅_人社_市级管理员 6403:揭榜挂帅_人社_区/县级管理员）
@see RoleCategories
   */
  excludeRoleCategoryList?: Array<number>
  /**
   * 角色应用方Id集合
   */
  applicationMemberIdList?: Array<string>
  /**
   * 授予性质 | 1.系统授予 2.用户授予
   */
  natureList?: Array<number>
  /**
   * 排除的角色Code | JGGLY-机构管理员
   */
  excludeRoleCodeList?: Array<string>
}

/**
 * 功能描述：用户基本查询条件
@Author： wtl
@Date： 2022年1月26日 09:30:12
 */
export class UserRequest {
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  userNameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  phoneMatchType?: number
  /**
   * 工作单位名称
   */
  companyName?: string
  /**
   * 工作单位统一社会信用代码
   */
  companyCode?: string
}

/**
 * 功能描述：管理员查询条件
@Author： wtl
@Date： 2022年1月25日 15:24:10
 */
export class AdminQueryRequest {
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: AdminUserRequest
  /**
   * 登录认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 角色信息查询
   */
  role?: RoleRequest
  /**
   * 排序
   */
  sortList?: Array<AdminSortRequest>
}

/**
 * 功能描述：管理员排序
@Author： wtl
@Date： 2021/12/27 10:32
 */
export class AdminSortRequest {
  /**
   * 管理员排序字段
   */
  sortField?: EnterprisePersonSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 功能描述：管理员查询条件
@Author： wtl
@Date： 2022年1月25日 15:24:10
 */
export class AdminUserRequest {
  /**
   * 默认为右模糊(0：完全匹配 1：模糊查询，*manageRegionPath* 2：左模糊查询，*manageRegionPath 3:右模糊查询，manageRegionPath*)
管辖地区路径匹配方式
   */
  manageRegionPathType?: number
  /**
   * 管理地区路径集合
   */
  manageRegionPathList?: Array<string>
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  userNameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  phoneMatchType?: number
  /**
   * 工作单位名称
   */
  companyName?: string
  /**
   * 工作单位统一社会信用代码
   */
  companyCode?: string
}

/**
 * 功能描述：集体缴费管理员排序
@Author： wtl
@Date： 2022年1月26日 16:19:01
 */
export class CollectiveSortRequest {
  /**
   * 集体缴费管理员排序字段
   */
  sortField?: EnterprisePersonSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 功能描述 : 合同服务商管理员查询条件
 */
export class ContractProviderAdminQueryRequest {
  /**
   * 合同服务商管理员归属信息
   */
  owner?: ContractProviderInfoAdminOwnerRequest
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: AdminUserRequest
  /**
   * 登录认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 角色信息查询
   */
  role?: RoleRequest
  /**
   * 排序
   */
  sortList?: Array<AdminSortRequest>
}

/**
 * 功能描述 : 企业单位管理员查询条件
@date : 2022/6/17 17:32
 */
export class EnterpriseUnitAdminQueryRequest {
  /**
   * 企业单位管理员归属信息
   */
  owner?: EnterpriseUnitAdminOwnerRequest
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: AdminUserRequest
  /**
   * 登录认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 角色信息查询
   */
  role?: RoleRequest
  /**
   * 排序
   */
  sortList?: Array<AdminSortRequest>
}

/**
 * 功能描述 : 政府单位管理员查询条件
@date : 2022/6/17 17:32
 */
export class GovernmentUnitAdminQueryRequest {
  /**
   * 政府单位管理员归属信息
   */
  owner?: GovernmentUnitAdminOwnerRequest
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: AdminUserRequest
  /**
   * 登录认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 角色信息查询
   */
  role?: RoleRequest
  /**
   * 排序
   */
  sortList?: Array<AdminSortRequest>
}

/**
 * 功能描述 : 服务商管理员查询条件
 */
export class ServicerAdminQueryRequest {
  /**
   * 服务商管理员归属信息
   */
  businessOwnerInfo?: ServicerAdminBusinessOwnerRequest
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: AdminUserRequest
  /**
   * 登录认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 角色信息查询
   */
  role?: RoleRequest
  /**
   * 排序
   */
  sortList?: Array<AdminSortRequest>
}

/**
 * 功能描述 : 合同服务商管理员归属查询条件
@date : 2022年7月29日16:02:39
 */
export class ContractProviderInfoAdminOwnerRequest {
  /**
   * 合同服务商id路径集合
说明：核心层需要将服务商ID需要转换为角色应用方ID查询
   */
  servicerIdList?: Array<string>
}

/**
 * 功能描述 : 企业单位管理员归属查询条件
@date : 2022/6/17 17:42
 */
export class EnterpriseUnitAdminOwnerRequest {
  /**
   * 企业单位id路径集合
String：&quot;/福建电信id/福州电信分公司id&quot;
   */
  enterpriseUnitIdPathList?: Array<string>
  /**
   * 企业单位id路径匹配方式，默认为右模糊查询(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  enterpriseUnitIdPathMatchType?: number
}

/**
 * 功能描述 : 政府单位管理员归属查询条件
@date : 2022/6/17 17:42
 */
export class GovernmentUnitAdminOwnerRequest {
  /**
   * 政府单位id路径集合
String：&quot;/福建省人社id/福州市人社id&quot;
   */
  governmentUnitIdPathList?: Array<string>
  /**
   * 政府单位id路径匹配方式，默认为右模糊查询(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  governmentUnitIdPathMatchType?: number
}

/**
 * 功能描述 : 服务商管理员业务归属查询条件
@date : 2022年10月31日 15:52:19
 */
export class ServicerAdminBusinessOwnerRequest {
  /**
   * 服务商id集合
   */
  servicerIdList?: Array<string>
}

/**
 * 功能描述：集体缴费管理员查询条件
@Author： wtl
@Date： 2022年1月26日 10:10:33
 */
export class CollectiveQueryRequest {
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: UserRequest
  /**
   * 登录认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 集体缴费管理员排序集合
   */
  sortList?: Array<CollectiveSortRequest>
}

/**
 * 业务区域树根结点查询
 */
export class BusinessRegionTreeQueryChildrenRequest {
  /**
   * 业务id，代表使用哪一棵业务地区树
   */
  businessId?: string
  /**
   * 父节点Id -1时为根结点
   */
  parentId?: string
}

/**
 * 业务区域树根结点查询
 */
export class BusinessRegionTreeQueryRequest {
  /**
   * 业务id，代表使用哪一棵业务地区树
   */
  businessId?: string
  /**
   * 业务区域编码
   */
  code?: string
}

/**
 * 业务区域树根结点查询
 */
export class BusinessRegionTreeQueryRootRequest {
  /**
   * 业务id，代表使用哪一棵业务地区树
   */
  businessId?: string
}

/**
 * 查询行业属性信息
 */
export class IndustryPropertyInfoQueryRequest {
  /**
   * 服务商(网校id)
   */
  servicerId?: string
  /**
   * 行业id
   */
  industryIdList?: Array<string>
  /**
   * 行业属性类别 0-业务行业属性，1-人员行业属性
   */
  propertyType?: number
}

export class IndustryPropertyRequest {
  /**
   * 行业属性名称
   */
  industryPropertyName?: string
  /**
   * 行业id
   */
  industryIdList?: Array<string>
  /**
   * 行业属性类别 0-业务行业属性，1-人员行业属性
   */
  propertyType?: number
}

export class PageIndustryPropertyByCategoryRequest {
  /**
   * 行业属性编号
   */
  industryPropertyId?: string
  /**
   * 行业ID
   */
  industryId?: string
  /**
   * 行业属性分类代码
TRAINING_CATEGORY
PERSON_TRAINING_CATEGORY
TRAINING_PROFESSIONAL
PERSON_TRAINING_PROFESSIONAL
   */
  categoryCode?: string
  /**
   * 字典名称，模糊查询
   */
  name?: string
  /**
   * 是否可用，0停用1可用，不传返回全部
   */
  available?: number
}

/**
 * 网校培训属性查询条件
<AUTHOR>
@since 2022/1/17
 */
export class SchoolTrainingPropertyQueryRequest {
  /**
   * 网校编号
   */
  schoolId?: string
  /**
   * 行业编号
   */
  industryId?: string
  /**
   * 培训属性编号列表，最大支持200个
   */
  propertyId?: Array<string>
}

/**
 * <AUTHOR> linq
@date : 2023-08-22 11:33
@description：查询服务地区 请求
 */
export class ServiceOrIndustryRegionQueryRequest {
  /**
   * 服务类型 0或1
0 - 平台业务地区(PLATFORM_BUSINESS_REGION)
1 - 培训方案地区(TRAINING_SCHEME_REGION)
   */
  type?: number
  /**
   * 需要返回的地区级别
0 - 全部 (自身及所有下级地区)
1 - 仅省级
2 - 仅市级
3 - 仅区县级
   */
  whichLevel?: number
  /**
   * 地区父节点code
   */
  parentRegionCode?: string
}

export class ServicerContractPropertyByCategoryRequest {
  /**
   * 字典分类
   */
  categoryCode?: string
  /**
   * 可用状态，不传的情况下默认可用，0停用 1可用
   */
  availableList?: Array<number>
}

export class TrainingCategoryQueryRequest {
  /**
   * 层级
   */
  level: number
  /**
   * 父类编号
   */
  parentId?: string
  /**
   * 行业属性编号列表
   */
  industryPropertyIds?: Array<string>
}

/**
 * 培训属性通用查询参数
 */
export class TrainingPropertyCommonRequest {
  /**
   * 行业属性编号
   */
  industryPropertyId?: string
  /**
   * 行业ID
   */
  industryId?: string
  /**
   * 行业属性分类代码
TRAINING_CATEGORY
PERSON_TRAINING_CATEGORY
TRAINING_PROFESSIONAL
PERSON_TRAINING_PROFESSIONAL
   */
  categoryCode?: string
  /**
   * 字典名称，模糊查询
   */
  name?: string
  /**
   * 是否可用，0停用1可用，不传默认可用。传-1返回全部
   */
  available?: number
}

/**
 * 培训属性查询条件
<AUTHOR>
@since 2022/1/17
 */
export class TrainingPropertyQueryRequest {
  /**
   * 行业属性编号
   */
  industryPropertyId?: string
  /**
   * 行业属性分类
   */
  categoryCode?: string
  /**
   * 培训属性编号
   */
  propertyId?: string
  /**
   * 培训属性编号集合
目前只在以下接口使用，优先使用 propertyId 字段，即 propertyId 有值时，propertyIdList 入参无效
com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.DictionaryCommonQueryResolver#listIndustryPropertyChildByCategoryV2
   */
  propertyIdList?: Array<string>
}

/**
 * 简略资讯查询条件
 */
export class NewsCompleteQueryRequest {
  /**
   * 资讯标题
   */
  title?: string
  /**
   * 资讯分类编号
   */
  necId?: string
  /**
   * 顶级分类代码
   */
  rootCategoryCode?: string
  /**
   * 弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]
   */
  popUpsStatus: number
  /**
   * 资讯状态 [-1-全部，0-草稿，1-发布 默认全部]
   */
  status: number
}

/**
 * 简略资讯查询条件
 */
export class NewsSimpleQueryRequest {
  /**
   * 资讯标题
   */
  title?: string
  /**
   * 资讯分类编号
   */
  necId?: string
  /**
   * 发布地区编码
   */
  areaCodePath?: string
  /**
   * 弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]
   */
  popUpsStatus: number
  /**
   * 资讯状态 [-1-全部，0-草稿，1-发布 默认全部]
   */
  status: number
}

/**
 * 简略资讯查询条件
 */
export class TrainingChannelNewsSimpleQueryRequest {
  /**
   * 资讯标题
   */
  title?: string
  /**
   * 资讯分类编号
   */
  necId?: string
  /**
   * 发布地区编码
   */
  areaCodePath?: string
  /**
   * 弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]
   */
  popUpsStatus: number
  /**
   * 资讯状态 [-1-全部，0-草稿，1-发布 默认全部]
   */
  status: number
  /**
   * 专题ID
   */
  specialSubjectIds?: Array<string>
}

/**
 * <AUTHOR> linq
@date : 2023-07-14 10:50
@description：网校信息查询参数
 */
export class OnlineSchoolInfoRequest {
  /**
   * 查询参数
   */
  onlineSchoolInfoQuery?: OnlineSchoolInfoQueryRequest
  /**
   * 排序参数
   */
  onlineSchoolInfoSort?: OnlineSchoolInfoSortRequest
}

/**
 * <AUTHOR> linq
@date : 2023-07-18 10:50
@description：网校信息查询参数
 */
export class OnlineSchoolInfoQueryRequest {
  /**
   * 网校ID集合
   */
  onlineSchoolIdList?: Array<string>
  /**
   * 网校平台名称
   */
  name?: string
  /**
   * 服务商名称
   */
  servicerName?: string
  /**
   * 服务地区
   */
  serviceAreas?: Array<string>
  /**
   * 培训行业id
   */
  industryIds?: Array<string>
  /**
   * 补贴培训：培训类别
   */
  trainingCategoryIds?: Array<string>
  /**
   * 业主单位
   */
  unitName?: string
  /**
   * 统一社会信用代码
   */
  code?: string
  /**
   * 网校模式
1-正式实施 2-DEMO
@see OnlineSchoolModes
   */
  onlineSchoolModes?: number
  /**
   * 线下签署合约日期 开始时间
   */
  offlineContractSignedDateBegin?: string
  /**
   * 线下签署合约日期 结束时间
   */
  offlineContractSignedDateEnd?: string
  /**
   * 服务商创建时间 开始时间
   */
  servicerCreatedTimeBegin?: string
  /**
   * 服务商创建时间 结束时间
   */
  servicerCreatedTimeEnd?: string
  /**
   * 合约状态
0-草案 1-交付中 2-履约中 3-终止
@see OnlineSchoolContractStatusConstant
   */
  status?: number
  /**
   * 是否可用
   */
  isEnable?: boolean
  /**
   * 是否到期
   */
  isExpired?: boolean
  /**
   * 是否续期
   */
  isRenewed?: boolean
  /**
   * 到期时间 开始时间
   */
  expireDateBegin?: string
  /**
   * 到期时间 结束时间
   */
  expireDateEnd?: string
  /**
   * 客户端类型
1-web访问 2-h5访问
@see ClientTypesConstant
   */
  clientType?: Array<number>
  /**
   * 培训周期模式
1-长期 2-短期
@see TrainingPeriodModesConstant
   */
  trainingPeriodModes?: number
  /**
   * 网校开通时间 - 开始时间
   */
  openTimeBegin?: string
  /**
   * 网校开通时间 - 结束时间
   */
  openTimeEnd?: string
}

/**
 * <AUTHOR> linq
@date : 2023-07-18 10:51
@description：网校信息排序参数
 */
export class OnlineSchoolInfoSortRequest {
  /**
   * 网校开通时间
1-升序 2-降序
   */
  openTimeSort?: number
}

/**
 * 功能描述 : 门户持核心层查询条件
@date : 2022年10月18日 19:53:03
 */
export class PortalRequest {
  /**
   * 归属信息
   */
  owner?: PortalOwnerRequest
  /**
   * 门户信息
   */
  portalInfo?: PortalInfoRequest
}

/**
 * 功能描述：门户信息查询条件
 */
export class PortalInfoRequest {
  /**
   * 门户id集合
   */
  portalId?: Array<string>
  /**
   * 门户类型（1：web端 2：移动端）
@see com.fjhb.domain.basicdata.api.servicer.consts.PortalTypes
   */
  portalType?: number
  /**
   * 门户状态（0：未发布 1：已发布）
   */
  status?: number
}

/**
 * 功能描述：门户归属信息查询参数
@Author： wtl
@Date： 2022年10月18日 19:53:52
 */
export class PortalOwnerRequest {
  /**
   * 所属培训机构id集合
   */
  trainingInstitutionIdList?: Array<string>
}

/**
 * 功能描述：合同供应商查询条件
 */
export class ContractProviderRequest {
  /**
   * 服务商基本信息
   */
  contractProviderBase?: ContractProviderBaseRequest
  /**
   * 排序集合
   */
  sortList?: Array<ServicerSortKParam>
}

/**
 * 功能描述：课件供应商查询条件
 */
export class CoursewareSupplierRequest {
  /**
   * 服务商基本信息
   */
  servicerBase?: ServicerBaseRequest
  /**
   * 排序集合
   */
  sortList?: Array<ServicerSortKParam>
}

/**
 * 功能描述：分销商查询条件
 */
export class DistributionServerRequest {
  /**
   * 服务商基本信息
   */
  servicerBase?: ServicerBaseRequest
  /**
   * 排序集合
   */
  sortList?: Array<ServicerSortKParam>
}

/**
 * 功能描述：供应商查询条件
 */
export class SupplierServerRequest {
  /**
   * 服务商基本信息
   */
  servicerBase?: ServicerBaseRequest
  /**
   * 排序集合
   */
  sortList?: Array<ServicerSortKParam>
}

/**
 * 功能描述：服务商基本查询条件
 */
export class ContractProviderBaseRequest {
  /**
   * 服务商id
   */
  servicerId?: string
  /**
   * 服务商名称
   */
  servicerName?: string
  /**
   * 服务商名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  servicerNameMatchType?: number
  /**
   * 服务商所属地区路径集合
   */
  regionPathList?: Array<string>
  /**
   * 服务商所属地区路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  regionPathListMatchType?: number
  /**
   * 创建时间范围
   */
  servicerCreatedTimeScope?: DateScopeRequest
}

/**
 * 功能描述：服务商基本查询条件
 */
export class ServicerBaseRequest {
  /**
   * 服务商id
   */
  servicerIdList?: Array<string>
  /**
   * 服务商名称
   */
  servicerName?: string
  /**
   * 服务商简称
   */
  servicerShortName?: string
  /**
   * 服务商名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  servicerNameMatchType?: number
  /**
   * 服务商名称与简称匹配关系
@see ParamRelationConstant
   */
  servicerNameRelationType?: number
  /**
   * 服务商所属地区路径集合
   */
  regionPathList?: Array<string>
  /**
   * 服务商所属地区路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  regionPathListMatchType?: number
  /**
   * 创建时间范围
   */
  servicerCreatedTimeScope?: DateScopeRequest
}

/**
 * 功能描述：学员集体缴费信息
@Author： wtl
@Date： 2022年4月21日 08:58:49
 */
export class CollectiveRequest {
  /**
   * 集体缴费管理员用户id集合
   */
  collectiveUserIdList?: Array<string>
}

/**
 * 功能描述：学员查询条件
@Author： wtl
@Date： 2022年1月26日 10:10:33
 */
export class StudentQueryRequest {
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: StudentUserRequest
  /**
   * 用户认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 集体缴费信息
   */
  collective?: CollectiveRequest
  /**
   * 排序
   */
  sortList?: Array<StudentSortRequest>
}

/**
 * 功能描述 : 学员注册数量统计查询条件
 */
export class StudentRegisteredStatisticsRequest {
  /**
   * 所属服务商ID集合
   */
  servicerIdList?: Array<string>
  /**
   * 行业id
   */
  industryId?: string
  /**
   * 类别id
   */
  categoryId?: string
  /**
   * 专业id
   */
  professionalId?: string
}

/**
 * 功能描述 : 学员排序参数
@date : 2022/4/1 17:15
 */
export class StudentSortRequest {
  /**
   * 学员排序字段
   */
  sortField?: PersonAccountSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 功能描述：学员查询条件
@Author： wtl
@Date： 2022年1月26日 10:10:33
 */
export class StudentUserRequest {
  /**
   * 用户所属地区路径集合（模糊，右like）
   */
  regionPathList?: Array<string>
  /**
   * 用户所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  regionPathListMatchType?: number
  /**
   * 单位所属地区路径集合（模糊，右like）
   */
  companyRegionPathList?: Array<string>
  /**
   * 单位所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  companyRegionPathListMatchType?: number
  /**
   * 是否工勤人员  (0:非工勤人员  1:工勤人员)
   */
  isWorker?: string
  /**
   * 是否退休   (0:非退休人员 1:退休人员)
   */
  isRetire?: string
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  userNameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  phoneMatchType?: number
  /**
   * 工作单位名称
   */
  companyName?: string
  /**
   * 工作单位统一社会信用代码
   */
  companyCode?: string
}

/**
 * 时间纬度下单位数量统计查询条件
@date 2022-07-27
 */
export class EnterpriseUnitDateHistogramRequest {
  /**
   * 时间纬度查询条件
   */
  dateHistogram?: DateHistogramRequest
  /**
   * 企业单位基本信息
   */
  unitBase?: EnterpriseUnitBaseRequest
}

/**
 * 功能描述：企业单位查询条件
@Author： wtl
@Date： 2022年6月9日 11:56:47
 */
export class EnterpriseUnitRequest {
  /**
   * 企业单位基本信息
   */
  unitBase?: EnterpriseUnitBaseRequest
  /**
   * 排序集合
   */
  sortList?: Array<EnterpriseUnitSortRequest>
}

/**
 * 企业单位统计查询条件
<AUTHOR>
@date 2022-07-23
 */
export class EnterpriseUnitStatisticRequest {
  /**
   * 企业单位基本信息
   */
  unitBase?: EnterpriseUnitBaseRequest
}

/**
 * 功能描述：政府单位查询条件
@Author： wtl
@Date： 2022年6月9日 10:24:09
 */
export class GovernmentUnitRequest {
  /**
   * 基本归属信息
   */
  owner?: OwnerRequest
  /**
   * 单位业务归属信息
   */
  businessOwner?: GovernmentUnitOwnerRequest
  /**
   * 单位基本信息
   */
  unitBase?: GovernmentUnitBaseRequest
  /**
   * 排序集合
   */
  sortList?: Array<GovernmentUnitSortRequest>
}

/**
 * 功能描述：企业单位基本查询条件
@Author： wtl
@Date： 2022年6月9日 10:24:09
 */
export class EnterpriseUnitBaseRequest {
  /**
   * 单位id集合
   */
  unitIdList?: Array<string>
  /**
   * 单位名称
   */
  unitName?: string
  /**
   * 单位名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  unitNameMatchType?: number
  /**
   * 统一社会信用代码
   */
  code?: string
  /**
   * 单位业务类型
说明：
1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
11地区管理单位,12行业主管单位,13技工院校,14职业院校.15线上培训机构,10000实名制报表补贴单位,10001评价机构
@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
@see UnitBusinessQueryTypes
   */
  businessTypeList?: Array<number>
  /**
   * 单位类型id路径集合
   */
  unitTypeIdPathList?: Array<string>
  /**
   * 单位类型id路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*unitTypeIdPathList* 2：左模糊查询，*unitTypeIdPathList 3:右模糊查询，unitTypeIdPathList*)
@see MatchTypeConstant
   */
  unitTypeIdPathListMatchType?: number
  /**
   * 单位地区路径集合
   */
  regionPathList?: Array<string>
  /**
   * 单位地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  regionPathListMatchType?: number
  /**
   * 单位创建时间范围
   */
  createdDateScope?: DateScopeRequest
  /**
   * 企业法人
   */
  legalPerson?: LegalPersonRequest
  /**
   * 单位状态
说明：1正常,2冻结
@see com.fjhb.domain.basicdata.api.unit.consts.UnitStatus
   */
  statusList?: Array<number>
}

/**
 * 企业单位排序
<AUTHOR>
@date 2022-06-18
 */
export class EnterpriseUnitSortRequest {
  /**
   * 排序字段
   */
  sortField?: EnterpriseUnitSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 功能描述：政府单位基本查询条件
@Author： wtl
@Date： 2022年6月9日 10:24:09
 */
export class GovernmentUnitBaseRequest {
  /**
   * 单位id集合
   */
  unitIdList?: Array<string>
  /**
   * 单位名称
   */
  unitName?: string
  /**
   * 单位名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  unitNameMatchType?: number
  /**
   * 管辖地区路径
   */
  manageRegionPathList?: Array<string>
  /**
   * 管辖地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  manageRegionPathListMatchType?: number
}

/**
 * 功能描述：政府单位归属查询条件
@Author： wtl
@Date： 2022年6月9日 12:08:14
 */
export class GovernmentUnitOwnerRequest {
  /**
   * 单位id路径集合
   */
  unitIdPathList?: Array<string>
  /**
   * 单位id路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  unitIdPathMatchType?: number
}

/**
 * 政府单位排序
<AUTHOR>
@date 2022-06-18
 */
export class GovernmentUnitSortRequest {
  /**
   * 排序字段
   */
  sortField?: GovernmentUnitSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 功能描述：企业法人查询条件
@Author： wtl
@Date： 2022年6月9日 12:08:14
 */
export class LegalPersonRequest {
  /**
   * 法人姓名
   */
  name?: string
  /**
   * 法人姓名匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  nameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 证件号匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  idCardMatchType?: number
}

/**
 * 基本归属信息
 */
export class OwnerRequest {
  /**
   * 所属平台ID
   */
  platformId?: string
  /**
   * 所属平台版本ID
   */
  platformVersionId?: string
  /**
   * 所属项目ID
   */
  projectId?: string
  /**
   * 所属子项目ID
   */
  subProjectId?: string
}

/**
 * 功能描述：服务商排序
 */
export class ServicerSortKParam {
  /**
   * 排序字段
   */
  sortField?: ServicerSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

export class DateScopeRequest {
  beginTime?: string
  endTime?: string
}

export class DateHistogramItemModel {
  date: string
  count: number
}

export class RegionModel {
  regionId: string
  regionPath: string
  provinceId: string
  provinceName: string
  cityId: string
  cityName: string
  countyId: string
  countyName: string
}

export class SectionAndSubjects {
  section: number
  subjects: number
}

export class MessageTemplate {
  templateName: string
  content: string
  category: string
}

/**
 * 功能描述：账户信息
@Author： wtl
@Date： 2022年5月11日 15:30:56
 */
export class AccountResponse {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
@see AccountTypes
   */
  accountType: number
  /**
   * 单位信息
   */
  unitInfo: UnitInfoResponse
  /**
   * 所属顶级企业帐户Id
   */
  rootAccountId: string
  /**
   * 帐户状态 1：正常，2：冻结，3：注销
@see AccountStatus
   */
  status: number
  /**
   * 注册方式
0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
@see AccountRegisterTypes
   */
  registerType: number
  /**
   * 来源类型
0、内置，1、项目主网站，2、安卓，3、IOS，4、后台导入，5、迁移数据，6、分销平台项目主网站，7、专题，8、华医网，9、江西管理平台
@see AccountSourceTypes
   */
  sourceType: number
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 功能描述：帐户认证信息
@Author： wtl
@Date： 2022年5月11日 14:23:18
 */
export class AuthenticationResponse {
  /**
   * 帐号
   */
  identity: string
  /**
   * 认证标识类型
1用户名,2手机,3身份证,4邮箱,5第三方OpenId
   */
  identityType: number
  /**
   * 认证方式状态 1启用，2禁用
@see AuthenticationStatusEnum
   */
  status: number
}

/**
 * 功能描述：时间直方图统计结果
@Author： wtl
@Date： 2021/12/30 9:58
 */
export class DateHistogramResponse {
  /**
   * 时间单位
   */
  timeUnit: TimeUnitEnum
  /**
   * 统计结果元素
   */
  histogram: Array<DateHistogramItemModel>
  /**
   * 总计
   */
  totalCount: number
}

/**
 * 功能描述：角色信息
@Author： wtl
@Date： 2022/1/24 20:17
 */
export class RoleResponse {
  /**
   * 角色id
   */
  roleId: string
  /**
   * 角色名称
   */
  roleName: string
  /**
   * 角色类型
（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
   */
  roleType: string
  /**
   * 角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
@see RoleCategories
   */
  roleCategory: number
  /**
   * 应用方类型(4:子项目 5：单位 6：服务商)
@see SystemMemberTypes
   */
  applicationMemberType: number
  /**
   * 是否冻结，1代表该账户的角色被冻结，其他情况均为未冻结
@see com.fjhb.ms.basicdata.constants.AccountRoleFrozeStatusConstants
   */
  frozeStatus: number
  /**
   * 应用方ID
   */
  applicationMemberId: string
  /**
   * 角色说明
   */
  description: string
}

/**
 * 功能描述：账号信息
 */
export class UserAccountInfoResponse {
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 用户信息
   */
  userInfo: UserInfoResponse
  /**
   * 人员信息
   */
  personInfo: PersonInfoBasicResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
  /**
   * 角色信息集合
   */
  roleList: Array<RoleResponse>
}

/**
 * 功能描述：用户基础信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class UserInfoResponse {
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 性别
-1未知，0女，1男
@see Genders
   */
  gender: number
  /**
   * 证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string
  /**
   * 工作单位名称
   */
  companyName: string
  /**
   * 工作单位统一社会信用代码
   */
  companyCode: string
}

/**
 * 功能描述：管理员信息
@Author： wtl
@Date： 2022/1/24 20:17
 */
export class AdminInfoResponse {
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 管理员用户信息
   */
  userInfo: AdminUserInfoResponse
  /**
   * 人员信息
   */
  personInfo: PersonInfoBasicResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
  /**
   * 角色信息集合
   */
  roleList: Array<RoleResponse>
}

/**
 * 功能描述 : 合同服务商管理员信息
 */
export class ContractProviderAdminInfoResponse {
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 管理员用户信息
   */
  userInfo: AdminUserInfoResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
  /**
   * 角色信息集合
   */
  roleList: Array<RoleResponse>
}

/**
 * 功能描述 : 企业单位管理员信息
@date : 2022/6/18 12:24
 */
export class EnterpriseUnitAdminInfoResponse {
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 管理员用户信息
   */
  userInfo: AdminUserInfoResponse
  /**
   * 人员信息
   */
  personInfo: EnterpriseUnitPersonInfoResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
  /**
   * 角色信息集合
   */
  roleList: Array<RoleResponse>
}

/**
 * 功能描述 : 政府单位管理员信息
@date : 2022/6/18 12:24
 */
export class GovernmentUnitAdminInfoResponse {
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 管理员用户信息
   */
  userInfo: AdminUserInfoResponse
  /**
   * 人员信息
   */
  personInfo: PersonInfoBasicResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
  /**
   * 角色信息集合
   */
  roleList: Array<RoleResponse>
}

/**
 * 功能描述 : 服务商管理员信息
 */
export class ServicerAdminInfoResponse {
  /**
   * 服务商业务归属信息
   */
  businessOwnerInfo: ServicerAdminBusinessOwnerInfoResponse
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 管理员用户信息
   */
  userInfo: AdminUserInfoResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
  /**
   * 角色信息集合
   */
  roleList: Array<RoleResponse>
}

/**
 * 功能描述：管理员用户信息
@Author： wtl
@Date： 2022年1月25日 15:48:48
 */
export class AdminUserInfoResponse {
  /**
   * 管辖地区集合
   */
  manageRegionList: Array<RegionModel>
  /**
   * 办公室（所在处/科室）
   */
  office: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 性别
-1未知，0女，1男
@see Genders
   */
  gender: number
  /**
   * 证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string
  /**
   * 工作单位名称
   */
  companyName: string
  /**
   * 工作单位统一社会信用代码
   */
  companyCode: string
}

/**
 * 人员信息模型
 */
export class EnterpriseUnitPersonInfoResponse {
  /**
   * 是否法人帐号
   */
  isCorporateAccount: boolean
  /**
   * 人员实名认证信息
   */
  personIdentityVerificationInfo: PersonIdentityVerificationResponse
}

/**
 * 人员实名认证信息模型
 */
export class PersonIdentityVerificationResponse {
  /**
   * 是否已认证
   */
  identityVerification: boolean
  /**
   * 认证渠道(1:闽政通 2：腾讯)
   */
  identityVerificationChannel: number
  /**
   * 认证时间
   */
  identityVerificationTime: string
}

/**
 * 人员信息模型
 */
export class PersonInfoBasicResponse {
  /**
   * 人员实名认证信息
   */
  personIdentityVerificationInfo: PersonIdentityVerificationResponse
}

/**
 * 业务归属信息模型
 */
export class ServicerAdminBusinessOwnerInfoResponse {
  /**
   * 服务商集合
   */
  servicerList: Array<ServicerResponse>
}

/**
 * 服务商信息模型
 */
export class ServicerResponse {
  /**
   * 服务商类型（1：培训机构 2：课件供应商 3：渠道商 4：参训单位 5：合同供应商）
@see ServicerTypes
   */
  servicerType: number
  /**
   * 服务商id
   */
  servicerId: string
}

/**
 * 单位信息模型
 */
export class UnitInfoResponse {
  /**
   * 单位ID
   */
  unitId: string
}

/**
 * 功能描述：集体缴费管理员信息
@Author： wtl
@Date： 2022年1月26日 10:38:15
 */
export class CollectiveInfoResponse {
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 用户信息
   */
  userInfo: UserInfoResponse
  /**
   * 用户认证信息
   */
  authenticationList: Array<AuthenticationResponse>
}

/**
 * Description:业务数据字典
 */
export class BusinessDictionaryAcrossTypeResponse {
  /**
   * 字典关系id
   */
  id: string
  /**
   * 主字典ID
   */
  masterId: string
  /**
   * 从字典ID
   */
  slaveId: string
  /**
   * 从字典字典类型|对应枚举BusinessDataDictionaryTypeEnum
@see BusinessDataDictionaryTypeEnum
   */
  slaveType: string
  /**
   * 初始化数据识别标志
   */
  initable: number
}

/**
 * 业务地区信息
 */
export class BusinessRegionResponse {
  /**
   * 地区编码
   */
  id: string
  /**
   * 上级地区编码
   */
  parentId: string
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 地区名称
   */
  name: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enable: boolean
}

/**
 * 行政区划地区名称
<AUTHOR>
@since 2022/7/6
 */
export class BusinessTreeRegionNameMap {
  /**
   * 地区路径
格式：/350000/350100/350101
   */
  path: string
  /**
   * 地区名称拼接
格式：福建省/福州市/鼓楼区
   */
  name: string
}

export class BusinessTreeRegionResponse {
  /**
   * id
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 行政区划
   */
  code: string
  /**
   * 结点名称
   */
  name: string
  /**
   * 父节点Id -1时为根结点
   */
  parentId: string
  /**
   * 业务Id
   */
  businessId: string
  /**
   * 业务树结构路径
   */
  regionPath: string
}

/**
 * 业务年度信息
 */
export class BusinessYearResponse {
  /**
   * 业务年度编号
   */
  id: string
  /**
   * 年度
   */
  year: string
  /**
   * 序号
   */
  sort: number
  /**
   * 是否启用
   */
  enable: boolean
}

export class GetIndustryTypeResponse {
  /**
   * 行业类型id
   */
  id: string
  /**
   * 代码
   */
  code: string
  /**
   * 行业类型名称
   */
  industryType: string
  /**
   * 上级编号
   */
  parentId: string
  /**
   * 上级代码
   */
  parentCode: string
}

/**
 * 单位类型
 */
export class GetUnitTypeResponse {
  /**
   * 单位类型id
   */
  id: string
  /**
   * 代码
   */
  code: string
  /**
   * 单位类型名称
   */
  unitType: string
  /**
   * 上级编号
   */
  parentId: string
  /**
   * 上级代码
   */
  parentCode: string
}

/**
 * 行业属性分类信息
<AUTHOR>
@since 2022/1/17
 */
export class IndustryPropertyCategoryResponse {
  /**
   * 分类code
   */
  code: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
}

/**
 * 网校行业属性id集合
 */
export class IndustryPropertyInfoResponse {
  /**
   * 行业培训属性ID
   */
  industryPropertyId: string
  /**
   * 服务商ID
   */
  serviceId: string
  /**
   * 行业属性名称
   */
  industryPropertyName: string
  /**
   * 排序
   */
  sort: number
  /**
   * 来源id，模板数据的值为-1，网校的值为对应的模板行业培训属性id
   */
  sourceId: string
  /**
   * 行业属性类型
   */
  propertyType: number
}

export class IndustryPropertyResponse {
  /**
   * 行业培训属性ID
   */
  industryPropertyId: string
  /**
   * 行业属性名称
   */
  industryPropertyName: string
  /**
   * 行业字典ID
   */
  industryId: string
  /**
   * 行业字典名称
   */
  industryName: string
  /**
   * 更新时间
   */
  updateTime: string
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 培训类别信息
<AUTHOR>
@since 2022/2/10
 */
export class IndustryResponse {
  /**
   * 行业编号
   */
  id: string
  /**
   * 行业名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
}

export class IndustryTypeResponse {
  /**
   * 行业类型id
   */
  id: string
  /**
   * 代码
   */
  code: string
  /**
   * 行业类型名称
   */
  industryType: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 职称等级
<AUTHOR>
@since 2022/2/10
 */
export class LeaderPositionLevelResponse {
  /**
   * 职称等级编号
   */
  id: string
  /**
   * 职称等级名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
}

/**
 * 物理地区信息
 */
export class PhysicalRegionResponse {
  /**
   * 地区编码
   */
  id: string
  /**
   * 上级地区编码
   */
  parentId: string
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 地区名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
  /**
   * 是否启用
   */
  enable: boolean
}

/**
 * 功能描述：地区字典信息
@Author： yxw
@Date： 2023年6月15日
 */
export class RegionResponse {
  /**
   * 地区编码
   */
  code: string
  /**
   * 父级地区编码
   */
  parentCode: string
  /**
   * 地区编码路径
   */
  codePath: string
  /**
   * 地区名称
   */
  name: string
  /**
   * 级别|1省级 2市级 3区县级
   */
  level: number
  /**
   * 地区排序
   */
  sort: number
}

/**
 * 培训类别信息
<AUTHOR>
@since 2022/2/10
 */
export class SubjectTypePageResponse {
  /**
   * 科目类型编号
   */
  id: string
  /**
   * 科目类型名称
   */
  name: string
  /**
   * 展示名称
   */
  showName: string
}

/**
 * 培训类别信息
<AUTHOR>
@since 2022/2/10
 */
export class TrainingCategoryPageResponse {
  /**
   * 培训类别编号
   */
  categoryId: string
  /**
   * 培训类别父级编号
   */
  parentId: string
  /**
   * 培训类别名称
   */
  name: string
  /**
   * 展示名称
   */
  showName: string
}

/**
 * 培训类别信息
<AUTHOR>
@since 2022/2/10
 */
export class TrainingCategoryResponse {
  /**
   * 培训类别编号
   */
  categoryId: string
  /**
   * 培训类别父级编号
   */
  parentId: string
  /**
   * 培训类别名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
}

/**
 * 培训属性信息
<AUTHOR>
@since 2022/1/17
 */
export class TrainingPropertyResponse {
  /**
   * 业务侧培训属性关系主键ID
   */
  propertyRelationId: string
  /**
   * 清洗侧属性字典ID
   */
  propertyId: string
  /**
   * 属性名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
  /**
   * code值
   */
  code: number
  /**
   * code值，扩展
   */
  codeExt: string
  /**
   * 展示名称
   */
  showName: string
  /**
   * 如果是科目类型下的属性，则该值为null
   */
  parentId: string
  /**
   * 是否可用，0停用1可用
   */
  available: number
}

/**
 * 单位类型
 */
export class UnitTypeResponse {
  /**
   * 单位类型id
   */
  id: string
  /**
   * 代码
   */
  code: string
  /**
   * 单位类型名称
   */
  unitType: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 资讯分类信息
 */
export class NewsCategoryResponse {
  /**
   * 资讯分类编号
   */
  newsCategoryId: string
  /**
   * 分类名称
   */
  categoryName: string
  /**
   * 分类代码
   */
  code: string
}

/**
 * <AUTHOR> linq
@date : 2025-03-31 09:54
@description : 资讯分类信息(树形结构)
 */
export class NewsCategoryTreeResponse {
  /**
   * 资讯分类编号
   */
  newsCategoryId: string
  /**
   * 分类名称
   */
  categoryName: string
  /**
   * 分类代码
   */
  code: string
  /**
   * 子资讯分类
   */
  children: Array<NewsCategoryTreeResponse>
}

/**
 * 简略资讯信息
 */
export class NewsCompleteResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 所属业务平台
   */
  businessPlatform: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 资讯状态 0 草稿 1正常
   */
  status: number
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 分类id
   */
  necId: string
}

/**
 * 详细资讯信息
 */
export class NewsDetailResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 平台id
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目id
   */
  projectId: string
  /**
   * 子项目id
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商id
   */
  serviceId: string
  /**
   * 分类id
   */
  necId: string
  /**
   * 标题
   */
  title: string
  /**
   * 摘要
   */
  summary: string
  /**
   * 内容
   */
  content: string
  /**
   * 封面图片路径
   */
  coverPath: string
  /**
   * 来源
   */
  source: string
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 发布地区编码
   */
  areaCodePath: string
  /**
   * 资讯状态 0 草稿 1正常
   */
  status: number
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 浏览数量
   */
  reviewCount: number
  /**
   * 弹窗起始时间
   */
  popupBeginTime: string
  /**
   * 弹窗截止时间
   */
  popupEndTime: string
  /**
   * 专题ID
   */
  specialSubjectId: string
}

/**
 * 简略资讯信息
 */
export class NewsSimpleResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 摘要
   */
  summary: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 资讯状态 0 草稿 1正常
   */
  status: number
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 发布地区编码
   */
  areaCodePath: string
  /**
   * 专题Id
   */
  specialSubjectId: string
}

/**
 * <AUTHOR> linq
@date : 2023-07-19 08:36
@description：网校开通统计返回值
 */
export class OnlineSchoolCountResponse {
  /**
   * 已开通
   */
  openCount: string
  /**
   * 使用中
   */
  usingCount: string
  /**
   * 停用
   */
  unableCount: string
  /**
   * 已到期
   */
  expireCount: string
}

/**
 * <AUTHOR> linq
@date : 2023-07-13 18:01
@description：网校信息返回值
 */
export class OnlineSchoolInfoResponse {
  /**
   * 网校基础信息
   */
  basicInfo: OnlineSchoolBasicInfo
  /**
   * 网校配置信息
   */
  configInfo: OnlineSchoolConfigInfo
}

/**
 * <AUTHOR> linq
@date : 2023-07-18 10:42
@description：客户端信息
 */
export class Client {
  /**
   * 客户端类型
1-web访问 2-h5访问
@see ClientTypesConstant
   */
  clientType: number
  /**
   * 域名类型
1-华博域名 2-业主自由域名
@see DomainNameTypesConstant
   */
  domainNameType: number
  /**
   * 域名
   */
  domainName: string
  /**
   * 前端模板id
   */
  portalTemplateId: string
  /**
   * cnzz信息
   */
  cnzz: string
  /**
   * 目录名
   */
  dirName: string
}

/**
 * <AUTHOR> linq
@date : 2023-07-14 09:48
@description：行业培训属性
 */
export class Industry {
  /**
   * id
   */
  id: string
  /**
   * 行业名称
   */
  name: string
  /**
   * 属性
   */
  properties: string
  /**
   * 属于网校的培训属性
   */
  onlineSchoolProperty: string
}

/**
 * <AUTHOR> linq
@date : 2023-07-14 08:42
@description：网校基础信息
 */
export class OnlineSchoolBasicInfo {
  /**
   * 网校id
   */
  onlineSchoolId: string
  /**
   * 网校平台名称
   */
  name: string
  /**
   * 服务商名称
   */
  servicerName: string
  /**
   * 服务商LOGO
   */
  logo: string
  /**
   * 合作伙伴类型(供应商、分销商类型) 1-个人 2-企业
   */
  partnerType: number
  /**
   * 服务地区
   */
  serviceAreas: Array<string>
  /**
   * 培训属性
   */
  trainingProperties: TrainingProperties
  /**
   * 人员行业属性
   */
  personIndustries: Array<Industry>
  /**
   * 业主单位全称
   */
  unitName: string
  /**
   * 统一社会信用代码
   */
  code: string
  /**
   * 隶属单位ID
   */
  unitId: string
  /**
   * 业主单位简称
   */
  unitShotName: string
  /**
   * 业主负责人
   */
  transactor: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 网校模式
@see OnlineSchoolModesConstant
   */
  onlineSchoolModes: number
  /**
   * 是否已线下签署合约
   */
  isOfflineContractSigned: boolean
  /**
   * 线下签署合约日期
   */
  offlineContractSignedDate: string
  /**
   * 归属市场经办
   */
  marketTransactor: string
  /**
   * 网校背景描述
   */
  description: string
  /**
   * 合约状态
@see OnlineSchoolContractStatusConstant
   */
  status: number
  /**
   * 是否可用
   */
  isEnable: boolean
  /**
   * 是否到期
   */
  isExpired: boolean
  /**
   * 是否续期
   */
  isRenewed: boolean
  /**
   * 网校开通时间
   */
  OSOpenTime: string
  /**
   * 网校合约id
   */
  contractId: string
}

/**
 * <AUTHOR> linq
@date : 2023-07-14 10:03
@description：网校配置信息
 */
export class OnlineSchoolConfigInfo {
  /**
   * 客户端
   */
  clients: Array<Client>
  /**
   * 是否提供短信服务
   */
  offerSmsService: boolean
  /**
   * 短信配置信息
   */
  smsConfig: SmsConfig
  /**
   * 培训周期模式
1-长期 2-短期
@see TrainingPeriodModesConstant
   */
  trainingPeriodModes: number
  /**
   * 到期时间
   */
  expireDate: string
  /**
   * 强制完善信息：
true：当学员信息不全时，强制触发完善信息页面
false：当学员信息不全时，强制跳过完善信息页面
   */
  enabledForceCompleteInfo: boolean
}

/**
 * <AUTHOR> linq
@date : 2023-07-18 10:47
@description：短信配置信息
 */
export class SmsConfig {
  /**
   * 短信账号
   */
  smsAccount: string
  /**
   * 短信密码
   */
  smsPassword: string
  /**
   * 提供商id
   */
  providerId: string
  /**
   * 短信网关授权配置名称
   */
  ispName: string
  /**
   * 短信网关appId(非必填)
   */
  ispAppId: string
  /**
   * 短信授权仅账号扩展信息，存放第三方额外信息，例如证书(非必填)
   */
  extension: string
  /**
   * 短信模板列表
   */
  messageTemplateList: Array<MessageTemplate>
}

/**
 * <AUTHOR> linq
@date : 2023-07-14 09:46
@description：培训属性
 */
export class TrainingProperties {
  /**
   * 年份
   */
  years: Array<string>
  /**
   * 地区
   */
  areas: Array<string>
  /**
   * 培训行业属性
   */
  industries: Array<Industry>
  /**
   * 补贴培训：培训类别
   */
  trainingCategoryIds: Array<string>
}

/**
 * 门户信息返回模型
 */
export class PortalResponse {
  /**
   * 归属信息
   */
  ownerInfo: PortalOwnerInfoResponse
  /**
   * 门户信息
   */
  portalInfo: PortalInfoResponse
}

/**
 * 门户轮播图
 */
export class PortalBannerResponse {
  /**
   * 轮播图id
   */
  bannerId: string
  /**
   * 轮播图名称
   */
  name: string
  /**
   * 图片路径
   */
  path: string
  /**
   * 链接地址
   */
  link: string
  /**
   * 轮播图排序
   */
  sort: number
  /**
   * 是否启用
   */
  isEnable: boolean
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 门户友情链接
 */
export class PortalFriendLinkResponse {
  /**
   * 友情链接id
   */
  friendLinkId: string
  /**
   * 友情链接标题
   */
  title: string
  /**
   * 友情链接图片
   */
  picture: string
  /**
   * 友情链接类型（1：文本 2：图片）
@see FriendLinkTypes
   */
  friendLinkType: number
  /**
   * 链接
   */
  link: string
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 门户信息
 */
export class PortalInfoResponse {
  /**
   * 门户id
   */
  portalId: string
  /**
   * 门户类型（1：web端 2：移动端）
@see com.fjhb.domain.basicdata.api.servicer.consts.PortalTypes
   */
  portalType: number
  /**
   * 门户状态（0：未发布 1：已发布）
   */
  status: number
  /**
   * 门户标题
   */
  title: string
  /**
   * 门户logo
   */
  logo: string
  /**
   * 浏览器图标
   */
  icon: string
  /**
   * 移动二维码
   */
  mobileQRCode: string
  /**
   * 客服电话图片
   */
  csPhonePicture: string
  /**
   * 客服电话
   */
  csPhone: string
  /**
   * 客服咨询时间
   */
  csCallTime: string
  /**
   * 在线客服代码内容id
   */
  csOnlineCodeId: string
  /**
   * 在线客服代码内容
   */
  csOnlineCodeContent: string
  /**
   * 培训流程图片
   */
  trainingFlowPicture: string
  /**
   * 底部内容(底部落款)
   */
  footContentId: string
  /**
   * 底部内容(底部落款)
   */
  footContent: string
  /**
   * 友情链接集合
   */
  friendLinkList: Array<PortalFriendLinkResponse>
  /**
   * 主题颜色
   */
  themeColor: string
  /**
   * 宣传口号
   */
  slogan: string
  /**
   * 门户简介说明内容Id
   */
  contentId: string
  /**
   * 门户简介说明内容
   */
  content: string
  /**
   * 是否提供服务号
   */
  isProvideServiceAccount: boolean
  /**
   * 域名
   */
  domainName: string
  /**
   * 轮播图列表
   */
  portalBannerList: Array<PortalBannerResponse>
  /**
   * 栏目列表
   */
  portalMenuList: Array<PortalMenuResponse>
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 栏目信息
 */
export class PortalMenuResponse {
  /**
   * 栏目id
   */
  menuId: string
  /**
   * 栏目名称
   */
  name: string
  /**
   * 父栏目id
   */
  parentId: string
  /**
   * 栏目类型（1：菜单 2：资讯）
@see MenuTypes
   */
  menuType: number
  /**
   * 来源类型（1：内置 2：用户创建）
@see MenuSourceTypes
   */
  sourceType: number
  /**
   * 链接
   */
  link: string
  /**
   * 业务code
   */
  code: string
  /**
   * 引用id(咨询类型的栏目，引用的是资讯分类id)
   */
  referenceId: string
  /**
   * 是否允许添加子级
   */
  allowChildren: boolean
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 功能描述：门户归属信息
@Author： wtl
@Date： 2022/10/19 9:18
 */
export class PortalOwnerInfoResponse {
  /**
   * 所属培训机构id
   */
  trainingInstitutionId: string
}

/**
 * 企业单位业务归属信息
 */
export class ContractProviderOwnerResponse {
  /**
   * 服务商隶属企业单位路径
   */
  attachToEnterpriseUnitIdPath: string
  /**
   * 隶属企业帐户ID
value:String，&quot;隶属企业帐户ID&quot;
   */
  accountId: string
}

/**
 * 功能描述 : 合同服务商信息
@date : 2022/7/16 14:52
 */
export class ContractProviderResponse {
  /**
   * 业务归属信息
   */
  businessOwnerInfo: ContractProviderOwnerResponse
  /**
   * 服务商基本信息
   */
  contractProvider: ContractProviderInfoResponse
  /**
   * 服务商合约信息
   */
  servicerContract: Array<ServicerContractResponse>
}

/**
 * 课件供应商查询GRPC
 */
export class CoursewareSupplierGRPCResponse {
  /**
   * id
   */
  id: string
  /**
   * 名称
   */
  name: string
  /**
   * 创建者ID
   */
  createdUserId: string
  /**
   * 创建时间
   */
  createTime: string
  initable: number
}

/**
 * 功能描述 : 课件供应商信息
@date : 2022年10月31日 12:07:38
 */
export class CoursewareSupplierResponse {
  /**
   * 业务归属信息
   */
  businessOwnerInfo: ServicerBusinessOwnerResponse
  /**
   * 服务商基本信息
   */
  servicerBase: ServicerBaseInfoResponse
}

/**
 * 功能描述 : 分销服务商信息
 */
export class DistributionServicerResponse {
  /**
   * 服务商基本信息
   */
  servicerBase: ServicerBaseInfoResponse
  /**
   * 业务归属信息
   */
  businessOwnerInfo: ServicerBusinessOwnerResponse
}

/**
 * 服务商信息响应对象
<AUTHOR>
 */
export class ServicerInfoResponse {
  /**
   * 服务商信息
   */
  servicerInfo: ServicerInfo
}

/**
 * 功能描述 : 供应商服务商信息
 */
export class SupplierServicerResponse {
  /**
   * 服务商基本信息
   */
  servicerBase: ServicerBaseInfoResponse
  /**
   * 业务归属信息
   */
  businessOwnerInfo: ServicerBusinessOwnerResponse
}

/**
 * 服务商基础信息
 */
export class ContractProviderInfoResponse {
  /**
   * 服务商 Id
   */
  servicerId: string
  /**
   * 服务商名称
   */
  servicerName: string
  /**
   * 服务商简称
   */
  servicerShortName: string
  /**
   * 组织机构代码
   */
  code: string
  /**
   * 服务商来源
1平台创建、2合作机构
   */
  resourceType: number
  /**
   * 所在地区
   */
  region: RegionModel
  /**
   * 联系人
   */
  contactPerson: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 启用状态 1：启用，2：停用
   */
  enable: number
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 附件
 */
export class ServicerAttachResponse {
  /**
   * 文件名
   */
  fileName: string
  /**
   * 文件路径
   */
  filePath: string
}

/**
 * 服务商基础信息
 */
export class ServicerBaseInfoResponse {
  /**
   * 服务商 Id
   */
  servicerId: string
  /**
   * 服务商名称
   */
  servicerName: string
  /**
   * 服务商简称
   */
  servicerShortName: string
  /**
   * 统一社会信用社代码
   */
  code: string
  /**
   * 服务商类型
说明：1培训机构、2课件供应商、3渠道商、4参训单位、5合同供应商、6供应商、7分销商
   */
  servicerType: number
  /**
   * 服务商来源
1平台创建、2合作机构
   */
  resourceType: number
  /**
   * 所在地区
   */
  region: RegionModel
  /**
   * 联系人
   */
  contactPerson: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 附件
   */
  attach: Array<ServicerAttachResponse>
  /**
   * 证件类型
   */
  idCardType: number
  /**
   * 证件号
   */
  idCard: string
  /**
   * 合作伙伴类型(供应商、分销商类型) 1-个人 2-企业
   */
  partnerType: number
  /**
   * 服务商简介
   */
  abouts: string
  /**
   * 服务商介绍内容
   */
  content: string
  /**
   * 启用状态 1：启用，2：停用
   */
  enable: number
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 服务商业务归属信息
 */
export class ServicerBusinessOwnerResponse {
  /**
   * 服务商隶属企业单位路径
   */
  attachToEnterpriseUnitIdPath: string
  /**
   * 隶属企业帐户ID
value:String，&quot;隶属企业帐户ID&quot;
   */
  accountId: string
}

/**
 * 服务商合约信息
 */
export class ServicerContractResponse {
  /**
   * 合约Id
   */
  contractId: string
  /**
   * 业主单位全称
   */
  unitName: string
  /**
   * 业主单位简称
   */
  unitShortName: string
  /**
   * 统一社会信用代码
   */
  code: string
  /**
   * 所属地区编号(业务用户创建企业单位)
   */
  region: RegionModel
  /**
   * 是否已线下签署合约
   */
  isOfflineContractSigned: boolean
  /**
   * 线下签署合约日期
   */
  offlineContractSignedDate: string
  /**
   * 到期时间
   */
  expireDate: string
  /**
   * 合约状态
@see ServicerContractStatus
   */
  status: number
  /**
   * 拓展信息 key:服务商类型
@see ContractProviderContractBaseModel （网校合约）
   */
  extendedMap: Map<string, string>
  /**
   * 创建人id
   */
  createdUserId: string
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 服务商信息
<AUTHOR>
 */
export class ServicerInfo {
  /**
   * 服务商ID
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 帐户ID
   */
  accountId: string
  /**
   * 所在地区
   */
  region: string
  /**
   * 服务商类型
   */
  servicerType: number
  /**
   * 服务商名称
   */
  name: string
  /**
   * 服务商来源|1/2，平台创建/合作机构
   */
  resourceType: number
  /**
   * 状态
   */
  status: number
  /**
   * 服务商logo
   */
  logo: string
  /**
   * 联系人
   */
  contactPerson: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 服务商简介
   */
  abouts: string
  /**
   * 服务商详情ID
   */
  contentId: string
  /**
   * 电子公章文件路径
   */
  electronicSealPath: string
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 功能描述：学员信息
@Author： wtl
@Date： 2022年1月26日 10:38:15
 */
export class StudentInfoResponse {
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 学员用户信息
   */
  userInfo: StudentUserInfoResponse
  /**
   * 学员人员信息
   */
  personInfo: StudentPersonInfoResponse
  /**
   * 第三方绑定信息
   */
  openPlatformBind: OpenPlatformBindResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
}

/**
 * 功能描述：附件信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class AttachmentInfoResponse {
  /**
   * 附件名称
   */
  name: string
  /**
   * 附件地址
   */
  url: string
}

/**
 * 功能描述：学员绑定信息
@Author： wtl
@Date： 2022年5月12日 14:42:51
 */
export class OpenPlatformBindResponse {
  /**
   * 是否绑定微信
   */
  bindWX: boolean
  /**
   * 微信昵称
   */
  nickNameByWX: string
}

/**
 * 功能描述：学员证书信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class StudentCertificateResponse {
  /**
   * 证书id
   */
  certificateId: string
  /**
   * 证书编号
   */
  certificateNo: string
  /**
   * 证书类别
   */
  certificateCategory: string
  /**
   * 注册专业
   */
  registerProfessional: string
  /**
   * 主/增项 | 1-主项 2-增项
@see com.fjhb.domain.basicdata.api.user.consts.CertificateMainAddOnTypes
   */
  mainAddOn: string
  /**
   * 发证日期
   */
  releaseStartTime: string
  /**
   * 证书有效期
   */
  certificateEndTime: string
  /**
   * 证书附件信息
   */
  attachmentList: Array<AttachmentInfoResponse>
}

/**
 * 功能描述：学员行业信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class StudentIndustryResponse {
  /**
   * 用户行业id
   */
  userIndustryId: string
  /**
   * 行业id
   */
  industryId: string
  /**
   * 一级专业类别id
   */
  firstProfessionalCategory: string
  /**
   * 二级专业类别id
   */
  secondProfessionalCategory: string
  /**
   * 职称等级
   */
  professionalQualification: string
  /**
   * 人员类别（职业卫生行业）
   */
  personnelCategory: string
  /**
   * 岗位类别（职业卫生行业）
   */
  positionCategory: string
  /**
   * 技术等级（工勤行业）
   */
  professionalLevel: string
  /**
   * 工种（工勤行业）
   */
  jobCategoryId: string
  /**
   * 学员证书信息集合
   */
  userCertificateList: Array<StudentCertificateResponse>
  /**
   * 教师行业 学段、学科信息
   */
  sectionAndSubjects: Array<SectionAndSubjects>
  /**
   * 证书类型（药师行业）
   */
  certificatesType: string
  /**
   * 执证类别（药师行业）
   */
  practitionerCategory: string
}

export class StudentPersonInfoResponse {
  /**
   * 学历
   */
  education: string
}

/**
 * 功能描述：学员用户信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class StudentUserInfoResponse {
  /**
   * 用户昵称
   */
  nickName: string
  /**
   * 用户所属地区
   */
  region: RegionModel
  /**
   * 头像地址
   */
  photo: string
  /**
   * 联系地址
   */
  address: string
  /**
   * 学员行业信息集合
   */
  userIndustryList: Array<StudentIndustryResponse>
  /**
   * 证书技术工种Id
   */
  jobCategoryId: string
  /**
   * 证书技术等级
   */
  professionalLevel: number
  /**
   * 所属工考办地区编码
   */
  managementUnitRegion: RegionModel
  /**
   * 证书技术工种名称
   */
  jobCategoryName: string
  /**
   * 用户工作单位所在地区
   */
  companyRegion: RegionModel
  /**
   * 是否工勤人员 （0非工勤人员，1工勤人员）
   */
  isWorker: string
  /**
   * 是否退休 (0非退休人员，1退休人员)
   */
  isRetire: string
  /**
   * 学历
   */
  education: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 性别
-1未知，0女，1男
@see Genders
   */
  gender: number
  /**
   * 证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string
  /**
   * 工作单位名称
   */
  companyName: string
  /**
   * 工作单位统一社会信用代码
   */
  companyCode: string
}

/**
 * 地区专题信息响应体
 */
export class AreaTrainingChannelInfoResponse {
  /**
   * 专题用户id
   */
  trainingChannelUserId: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 地区id
   */
  regionId: string
  /**
   * 工作单位性质 字典
   */
  unitNature: string
  /**
   * 在编情况 字典
   */
  staffingStatus: string
  /**
   * 是否在专技岗位工作 字典
   */
  isZJPosition: string
  /**
   * 职称系列 字典
   */
  titleSeries: string
  /**
   * 职称专业
   */
  titleProfessional: string
  /**
   * 现有职称等级  字典
   */
  titleGrade: string
  /**
   * 现有职称资格名称
   */
  titleQualificationName: string
  /**
   * 现有职称有效范围  字典
   */
  titleEffectiveRange: string
  /**
   * 最高学历
   */
  highestEducationLevel: string
}

/**
 * 客户端信息返回
 */
export class ClientInfoResponse {
  /**
   * 客户端类型 | 1、Web 2、H5
@see ClientTypesConstant
   */
  clientType: number
  /**
   * 域名类型
@see DomainNameTypesConstant
   */
  domainNameType: number
  /**
   * 域名
   */
  domainName: string
  /**
   * 前端模板id
   */
  portalTemplateId: string
  /**
   * cnzz信息
   */
  cnzz: string
  /**
   * 目录名
   */
  dirName: string
}

/**
 * 门户信息
<AUTHOR>
 */
export class PortalInfoResponse1 {
  /**
   * id
   */
  id: string
  /**
   * 培训机构id
   */
  servicerId: string
  /**
   * 服务商类型 | 1培训机构、2课件供应商、3渠道商、4参训单位、5合同供应商、6供应商、7分销商、8企业
   */
  servicerType: number
  /**
   * 培训机构logo
   */
  institutionLogo: string
  /**
   * 门户类型
   */
  portalType: number
  /**
   * 门户标题
   */
  title: string
  /**
   * 门户logo
   */
  logo: string
  /**
   * 门户图标
   */
  icon: string
  /**
   * 友情链接类型 1-文本  2-图片
   */
  friendLinkType: number
  /**
   * 主题颜色
   */
  themeColor: string
  /**
   * 移动二维码
   */
  mobileQRCode: string
  /**
   * 移动二维码来源标识
1-系统生成 2-自定义
   */
  mobileQRCodeSign: number
  /**
   * 客服电话图片
   */
  CSPhonePicture: string
  /**
   * 客服电话
   */
  CSPhone: string
  /**
   * 客服咨询时间
   */
  CSCallTime: string
  /**
   * 在线客服代码内容id
   */
  CSOnlineCodeId: string
  /**
   * 培训流程图片
   */
  trainingFlowPicture: string
  /**
   * 底部内容（底部落款）
   */
  footContent: string
  /**
   * 宣传口号
   */
  slogan: string
  /**
   * 域名
   */
  domainName: string
  /**
   * H5域名(请求Web端门户信息才会使用这个字段 如果请求的是H5端的门户信息，该字段为null)
   */
  domainNameH5: string
  /**
   * 域名类型
系统默认域名 1
自有域名 2
@see com.fjhb.domain.basicdata.api.servicer.consts.DomainNameTypeConsts
   */
  domainNameType: number
  /**
   * 门户简介说明内容
   */
  content: string
  /**
   * 是否提供服务号
   */
  isProvideServiceAccount: boolean
  /**
   * 是否已发布
   */
  isPublished: boolean
  /**
   * 网校状态
1-正常  2-失效
   */
  onlineSchoolStatus: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime: string
  /**
   * 发布时间
   */
  publishedTime: string
  /**
   * 取消发布时间
   */
  unpublishedTime: string
  /**
   * cnzz信息
   */
  cnzz: string
  /**
   * 目录名
   */
  dirName: string
  /**
   * 网校模式
@see OnlineSchoolModesConstant
   */
  onlineSchoolModes: number
  /**
   * 是否到期
   */
  isExpired: boolean
  /**
   * 前端模板id
   */
  portalTemplateId: string
  /**
   * 企业客服微信
   */
  CSWechat: string
}

/**
 * 轮播图信息集合响应
<AUTHOR>
 */
export class BannerInfoListResponse {
  /**
   * 轮播图信息集合
   */
  bannerInfos: Array<BannerInfo>
}

/**
 * 轮播图信息
<AUTHOR>
 */
export class BannerInfo {
  /**
   * id
   */
  id: string
  /**
   * 所属门户id
   */
  portalId: string
  /**
   * 名称
   */
  name: string
  /**
   * 路径
   */
  path: string
  /**
   * 链接
   */
  link: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  isEnable: boolean
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 友情链接集合响应
<AUTHOR>
 */
export class FriendLinkListResponse {
  /**
   * 友情链接集合
   */
  friendLinkInfos: Array<FriendLinkInfo>
}

/**
 * 友情链接信息
<AUTHOR>
 */
export class FriendLinkInfo {
  /**
   * id
   */
  id: string
  /**
   * 所属门户id
   */
  portalId: string
  /**
   * 标题
   */
  title: string
  /**
   * 图片
   */
  picture: string
  /**
   * 友情链接类型
   */
  friendLinkType: number
  /**
   * 链接
   */
  link: string
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
}

/**
 * 栏目配置集合响应
<AUTHOR>
 */
export class MenuSettingInfoListResponse {
  /**
   * 栏目配置信息集合
   */
  menuSettingInfos: Array<MenuSettingInfo>
}

/**
 * 栏目配置信息
<AUTHOR>
 */
export class MenuSettingInfo {
  /**
   * 栏目id
   */
  id: string
  /**
   * 栏目类型
   */
  menuType: number
  /**
   * 来源类型
@see com.fjhb.domain.basicdata.api.servicer.consts.MenuSourceTypes
1-内置  2-用户创建
   */
  sourceType: number
  /**
   * 栏目名称（对应业务那边的displayName）
   */
  name: string
  /**
   * 原始名称（对应业务那边的name）
   */
  originName: string
  /**
   * 父栏目id
   */
  parentId: string
  /**
   * 链接
   */
  link: string
  /**
   * 业务code（用于同步资讯、前端link相同的情况下做二次识别）
   */
  code: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否允许添加子级
   */
  allowChildren: boolean
  /**
   * 是否启用
   */
  enable: boolean
}

/**
 * 行业信息下-企业单位统计
<AUTHOR>
@date 2022-07-25
 */
export class EnterpriseUnitIndustryStatisticResponse {
  /**
   * 行业类型id，可能是门类id、大类id、中类id、小类id
   */
  industryId: string
  /**
   * 企业单位统计结果
   */
  statisticInfo: EnterpriseUnitStatisticResponse
}

/**
 * 功能描述：企业单位信息
@Author： wtl
@Date： 2022年6月9日 14:20:55
 */
export class EnterpriseUnitInfoResponse {
  /**
   * 企业单位业务归属信息
   */
  businessOwnerInfo: EnterpriseUnitBusinessOwnerResponse
  /**
   * 单位基本信息
   */
  unitBase: EnterpriseUnitBaseResponse
  /**
   * 经营信息
   */
  businessInfo: BusinessInfoResponse
  /**
   * 单位认证信息
   */
  unitIdentityVerificationInfo: UnitIdentityVerificationResponse
  /**
   * 管理员数量
   */
  administratorCount: number
  /**
   * 可培训工种字典ID列表
   */
  trainingJobCategoryList: Array<string>
}

/**
 * 行业信息下-企业单位统计
<AUTHOR>
@date 2022-07-25
 */
export class EnterpriseUnitRegionStatisticResponse {
  /**
   * 注册地区
   */
  region: RegionModel
  /**
   * 企业单位统计结果
   */
  statisticInfo: EnterpriseUnitStatisticResponse
}

/**
 * 单位类型下-企业单位统计
<AUTHOR>
@date 2022-07-25
 */
export class EnterpriseUnitTypeStatisticResponse {
  /**
   * 单位类型id,可能是一级id、二级id、三级id
   */
  unitTypeId: string
  /**
   * 企业单位统计结果
   */
  statisticInfo: EnterpriseUnitStatisticResponse
}

/**
 * 功能描述：政府单位信息
@Author： wtl
@Date： 2022年6月9日 10:41:26
 */
export class GovernmentUnitInfoResponse {
  /**
   * 单位归属信息
   */
  ownerInfo: GovernmentUnitOwnerResponse
  /**
   * 单位业务归属信息
   */
  businessOwnerInfo: GovernmentUnitBusinessOwnerResponse
  /**
   * 政府单位基本信息
   */
  unitBase: GovernmentUnitBaseResponse
}

/**
 * 功能描述：企业经营信息
@Author： wtl
@Date： 2022年6月9日 14:31:03
 */
export class BusinessInfoResponse {
  /**
   * 营业期限起始日期
   */
  operatingBeginDate: string
  /**
   * 营业期限截止日期
   */
  operatingEndDate: string
  /**
   * 行业信息
   */
  industry: IndustryResponse1
  /**
   * 经营范围,例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。
   */
  businessScope: string
  /**
   * 营业执照图片
   */
  businessLicense: string
}

/**
 * 功能描述：企业单位信息
@Author： wtl
@Date： 2022年6月9日 14:23:04
 */
export class EnterpriseUnitBaseResponse {
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 单位英文名称
   */
  enName: string
  /**
   * 统一社会信用代码
   */
  code: string
  /**
   * logo
   */
  logo: string
  /**
   * 法人信息
   */
  legalPersonInfo: LegalPersonResponse
  /**
   * 单位类型（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）
   */
  unitType: UnitTypeResponse1
  /**
   * 成立日期
   */
  foundedDate: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 邮政编码
   */
  postCode: string
  /**
   * 传真
   */
  faxNumber: string
  /**
   * 注册地区
   */
  region: RegionModel
  /**
   * 联系地址
   */
  address: string
  /**
   * 注册地址
   */
  registerAddress: string
  /**
   * 登记机关
   */
  registeredOrgan: string
  /**
   * 注册资金
   */
  registeredCapital: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 单位状态
说明：1正常,2冻结
   */
  status: number
}

/**
 * 企业单位业务归属信息
 */
export class EnterpriseUnitBusinessOwnerResponse {
  /**
   * 企业归属信息路径
单位路径（若单位为福州市企业，则该值为:&quot;/福建省企业id/福州市企业id&quot;）
   */
  unitIdPath: string
}

/**
 * 企业单位统计结果
<AUTHOR>
@date 2022-07-25
 */
export class EnterpriseUnitStatisticResponse {
  /**
   * 企业单位数量统计
   */
  enterpriseUnitCount: number
}

/**
 * 功能描述：政府单位信息
@Author： wtl
@Date： 2022年6月9日 10:41:26
 */
export class GovernmentUnitBaseResponse {
  /**
   * 单位id
   */
  unitId: string
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 电话
   */
  phone: string
  /**
   * 传真
   */
  faxNumber: string
  /**
   * 所属地区
   */
  region: RegionModel
  /**
   * 地址
   */
  address: string
  /**
   * 管辖地区
   */
  manageRegion: RegionModel
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 功能描述：政府单位业务归属信息
@Author： wtl
@Date： 2022年6月24日 19:11:30
 */
export class GovernmentUnitBusinessOwnerResponse {
  /**
   * 单位路径（若单位为福州市人社，则该值为:&quot;/福建省人社id/福州市人社id&quot;）
   */
  unitIdPath: string
}

/**
 * 功能描述：政府单位信息
@Author： wtl
@Date： 2022年6月9日 10:41:26
 */
export class GovernmentUnitOwnerResponse {
  /**
   * 上级单位id（若为顶级单位，该值为空）
   */
  parentUnitId: string
}

/**
 * 行业信息
<AUTHOR>
@date 2022-06-18
 */
export class IndustryResponse1 {
  /**
   * 行业信息ID路径
   */
  industryIdPath: string
  /**
   * 门类
   */
  firstLevelIndustryId: string
  /**
   * 大类
   */
  secondLevelIndustryId: string
  /**
   * 中类
   */
  thirdLevelIndustryId: string
  /**
   * 小类
   */
  fourthLevelIndustryId: string
}

/**
 * 功能描述：企业法人信息
@Author： wtl
@Date： 2022年6月9日 14:31:03
 */
export class LegalPersonResponse {
  /**
   * 法定代表人
   */
  legalPerson: string
  /**
   * 证件类型
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
}

/**
 * 单位认证信息模型
 */
export class UnitIdentityVerificationResponse {
  /**
   * 是否已认证
   */
  identityVerification: boolean
  /**
   * 认证渠道（单位认证渠道：UnitIdentityVerificationChannels 人员认证渠道：PersonIdentityVerificationChannels）
@see PersonIdentityVerificationChannels
@see UnitIdentityVerificationChannels
   */
  identityVerificationChannel: number
  /**
   * 认证时间
   */
  identityVerificationTime: string
}

/**
 * 单位类型:（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）
<AUTHOR>
@date : 2022/6/18 14:15
 */
export class UnitTypeResponse1 {
  /**
   * 单位类型ID路径
   */
  unitTypeIdPath: string
  /**
   * 一级
   */
  firstLevelUnitTypeId: string
  /**
   * 二级
   */
  secondLevelUnitTypeId: string
  /**
   * 三级
   */
  thirdLevelUnitTypeId: string
}

export class TrainingPropertyResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TrainingPropertyResponse>
}

export class UserInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserInfoResponse>
}

export class AdminInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<AdminInfoResponse>
}

export class CollectiveInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CollectiveInfoResponse>
}

export class NewsCompleteResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<NewsCompleteResponse>
}

export class ContractProviderAdminInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ContractProviderAdminInfoResponse>
}

export class ContractProviderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ContractProviderResponse>
}

export class ServicerAdminInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ServicerAdminInfoResponse>
}

export class CoursewareSupplierGRPCResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursewareSupplierGRPCResponse>
}

export class CoursewareSupplierResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursewareSupplierResponse>
}

export class EnterpriseUnitInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<EnterpriseUnitInfoResponse>
}

export class DistributionServicerResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<DistributionServicerResponse>
}

export class EnterpriseUnitAdminInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<EnterpriseUnitAdminInfoResponse>
}

export class GovernmentUnitAdminInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<GovernmentUnitAdminInfoResponse>
}

export class GovernmentUnitInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<GovernmentUnitInfoResponse>
}

export class IndustryPropertyResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<IndustryPropertyResponse>
}

export class OnlineSchoolInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<OnlineSchoolInfoResponse>
}

export class PortalResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PortalResponse>
}

export class NewsSimpleResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<NewsSimpleResponse>
}

export class StudentInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<StudentInfoResponse>
}

export class SubjectTypePageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SubjectTypePageResponse>
}

export class SupplierServicerResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SupplierServicerResponse>
}

export class TrainingCategoryPageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TrainingCategoryPageResponse>
}

export class UserAccountInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserAccountInfoResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据服务商(网校id)、行业id、行业属性类别（业务、人员类别）查询行业属性id
   * @param industryPropertyInfoQueryRequest 查询条件
   * @return 行业属性id
   * @param query 查询 graphql 语法文档
   * @param industryPropertyInfoQueryRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType(
    industryPropertyInfoQueryRequest: IndustryPropertyInfoQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType,
    operation?: string
  ): Promise<Response<Array<IndustryPropertyInfoResponse>>> {
    return commonRequestApi<Array<IndustryPropertyInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { industryPropertyInfoQueryRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：获取我的管理员信息接口
   * 描述：查询当前登录管理员的信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.AdminInfoResponse
   * @date : 2022/4/2 14:19
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAdminInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAdminInfoInMyself,
    operation?: string
  ): Promise<Response<AdminInfoResponse>> {
    return commonRequestApi<AdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：获取网校管理员信息接口
   * 描述：查询当前网校下指定管理员的信息，如不存在返回null
   * @param adminUserId             :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.AdminInfoResponse
   * @date : 2022/4/2 14:19
   * @param query 查询 graphql 语法文档
   * @param adminUserId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAdminInfoInServicer(
    adminUserId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAdminInfoInServicer,
    operation?: string
  ): Promise<Response<AdminInfoResponse>> {
    return commonRequestApi<AdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { adminUserId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：获取子项目下管理员信息接口
   * 描述：查询当前子项目下指定管理员的信息，如不存在返回null
   * @param adminUserId             :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.AdminInfoResponse
   * @date : 2022/4/2 14:19
   * @param query 查询 graphql 语法文档
   * @param adminUserId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAdminInfoInSubProject(
    adminUserId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<AdminInfoResponse>> {
    return commonRequestApi<AdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { adminUserId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：获取子项目下管理员的用户信息接口
   * 描述：查询当前子项目下指定管理员的用户信息，如不存在返回null
   * @param adminUserId             :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.AdminInfoResponse
   * @date : 2022/4/2 14:19
   * @param query 查询 graphql 语法文档
   * @param adminUserId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAdminUserInfoInSubProject(
    adminUserId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAdminUserInfoInSubProject,
    operation?: string
  ): Promise<Response<AdminUserInfoResponse>> {
    return commonRequestApi<AdminUserInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { adminUserId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取指定门户下的获取轮播图集合
   * @param portalId 门户id
   * @return 轮播图集合响应
   * @param query 查询 graphql 语法文档
   * @param portalId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBannerListByPortalId(
    portalId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBannerListByPortalId,
    operation?: string
  ): Promise<Response<BannerInfoListResponse>> {
    return commonRequestApi<BannerInfoListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取轮播图集合
   * @param portalType 门户类型
   * @return 轮播图集合响应
   * @param query 查询 graphql 语法文档
   * @param portalType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBannerListByPortalType(
    portalType: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBannerListByPortalType,
    operation?: string
  ): Promise<Response<BannerInfoListResponse>> {
    return commonRequestApi<BannerInfoListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalType },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取轮播图集合
   * -分销平台使用 由于分销平台取上下文的方法与其他项目不一致
   * @param portalType 门户类型
   * @return 轮播图集合响应
   * @param query 查询 graphql 语法文档
   * @param portalType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBannerListByPortalTypeForFxpt(
    portalType: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBannerListByPortalTypeForFxpt,
    operation?: string
  ): Promise<Response<BannerInfoListResponse>> {
    return commonRequestApi<BannerInfoListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalType },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据id查询业务地区
   * @param id 业务地区编码
   * @return 业务地区
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBusinessRegionById(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBusinessRegionById,
    operation?: string
  ): Promise<Response<BusinessRegionResponse>> {
    return commonRequestApi<BusinessRegionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据地区编码和业务id查找一条地区
   * @param request 请求参数对象
   * @return 业务地区
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBusinessRegionTree(
    request: BusinessRegionTreeQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBusinessRegionTree,
    operation?: string
  ): Promise<Response<BusinessTreeRegionResponse>> {
    return commonRequestApi<BusinessTreeRegionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取网校合约客户端信息
   * @param query 查询 graphql 语法文档
   * @param servicerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getClientInfoByServicerId(
    servicerId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getClientInfoByServicerId,
    operation?: string
  ): Promise<Response<Array<ClientInfoResponse>>> {
    return commonRequestApi<Array<ClientInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { servicerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：根据集体缴费管理员id获取网校集体缴费管理员
   * 描述：查询当前网校下的集体缴费管理员信息
   * @param collectiveId            :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.collective.CollectiveInfoResponse
   * @date : 2022/5/27 08:56
   * @param query 查询 graphql 语法文档
   * @param collectiveId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCollectiveInfoInServicer(
    collectiveId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCollectiveInfoInServicer,
    operation?: string
  ): Promise<Response<CollectiveInfoResponse>> {
    return commonRequestApi<CollectiveInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { collectiveId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述 : 服务商-获取当前登录合同服务商管理员详情接口
   * @date : 2022/7/28 20:50
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getContractProviderInfoAdminInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getContractProviderInfoAdminInfoInMyself,
    operation?: string
  ): Promise<Response<ContractProviderAdminInfoResponse>> {
    return commonRequestApi<ContractProviderAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 服务商-合同服务商管理员详情查询接口
   * @date : 2022/7/28 20:50
   * @param accountId :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse
   * @param query 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getContractProviderInfoAdminInfoInServicer(
    accountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getContractProviderInfoAdminInfoInServicer,
    operation?: string
  ): Promise<Response<ContractProviderAdminInfoResponse>> {
    return commonRequestApi<ContractProviderAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-合同服务商管理员详情查询接口
   * @date : 2022/7/28 20:50
   * @param accountId :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse
   * @param query 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getContractProviderInfoAdminInfoInSubProject(
    accountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getContractProviderInfoAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<ContractProviderAdminInfoResponse>> {
    return commonRequestApi<ContractProviderAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述 : 合同供应商-查询当前登录服务商合同信息接口
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.ContractProviderResponse
   * @date : 2022/7/16 14:54
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getContractProviderInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getContractProviderInfoInMyself,
    operation?: string
  ): Promise<Response<ContractProviderResponse>> {
    return commonRequestApi<ContractProviderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-合同供应商详情查询接口
   * @param servicerId              :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.ContractProviderResponse
   * @date : 2022/7/16 14:54
   * @param query 查询 graphql 语法文档
   * @param servicerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getContractProviderInfoInSubProject(
    servicerId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getContractProviderInfoInSubProject,
    operation?: string
  ): Promise<Response<ContractProviderResponse>> {
    return commonRequestApi<ContractProviderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { servicerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述 : 服务商-获取当前登录课件供应商管理员详情接口
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ServicerAdminInfoResponse
   * @date : 2022/7/28 20:50
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCoursewareSupplierAdminInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCoursewareSupplierAdminInfoInMyself,
    operation?: string
  ): Promise<Response<ServicerAdminInfoResponse>> {
    return commonRequestApi<ServicerAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 服务商-课件供应商管理员详情查询接口
   * @param accountId               :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ServicerAdminInfoResponse
   * @date : 2022/7/28 20:50
   * @param query 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCoursewareSupplierAdminInfoInServicer(
    accountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCoursewareSupplierAdminInfoInServicer,
    operation?: string
  ): Promise<Response<ServicerAdminInfoResponse>> {
    return commonRequestApi<ServicerAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-课件供应商管理员详情查询接口
   * @param accountId               :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ServicerAdminInfoResponse
   * @date : 2022/7/28 20:50
   * @param query 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCoursewareSupplierAdminInfoInSubProject(
    accountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCoursewareSupplierAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<ServicerAdminInfoResponse>> {
    return commonRequestApi<ServicerAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述 : 查询当前登录课件供应商信息接口
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.CoursewareSupplierResponse
   * @date : 2022年11月1日 11:36:22
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCoursewareSupplierInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCoursewareSupplierInfoInMyself,
    operation?: string
  ): Promise<Response<CoursewareSupplierResponse>> {
    return commonRequestApi<CoursewareSupplierResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-课件供应商详情查询接口
   * @param servicerId              :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.CoursewareSupplierResponse
   * @date : 2022年11月1日 11:36:13
   * @param query 查询 graphql 语法文档
   * @param servicerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCoursewareSupplierInfoInSubProject(
    servicerId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCoursewareSupplierInfoInSubProject,
    operation?: string
  ): Promise<Response<CoursewareSupplierResponse>> {
    return commonRequestApi<CoursewareSupplierResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { servicerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 上下文获取当前服务商信息
   * @return 服务商信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCurrentServicerInfo(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCurrentServicerInfo,
    operation?: string
  ): Promise<Response<ServicerInfoResponse>> {
    return commonRequestApi<ServicerInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 查询当前登录分销商信息接口
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDistributionServicerInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDistributionServicerInfoInMyself,
    operation?: string
  ): Promise<Response<DistributionServicerResponse>> {
    return commonRequestApi<DistributionServicerResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-分销服务商详情查询接口
   * @param query 查询 graphql 语法文档
   * @param servicerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getDistributionServicerInfoInSubProject(
    servicerId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDistributionServicerInfoInSubProject,
    operation?: string
  ): Promise<Response<DistributionServicerResponse>> {
    return commonRequestApi<DistributionServicerResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { servicerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：企业-当前登录企业管理员信息
   * 描述：查询当前登录管理员的信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnterpriseUnitAdminInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitAdminInfoInMyself,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponse>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：项目级-企业单位管理员详情查询接口
   * 描述：查询当前企业单位下指定管理员的信息，如不存在返回null
   * @param accountId               : 帐户ID
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
   * @date : 2022年6月9日 16:50:10
   * @param query 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getEnterpriseUnitAdminInfoInSubProject(
    accountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponse>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 政府单位下-按条件统计企业单位总数量
   * @param request 企业单位统计查询条件
   * @return 统计数量结果
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnterpriseUnitCountInGovernmentUnit(
    request: EnterpriseUnitStatisticRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitCountInGovernmentUnit,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 子项目下-按条件统计企业单位总数量
   * @param request 企业单位统计查询条件
   * @return 统计数量结果
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getEnterpriseUnitCountInSubProject(
    request: EnterpriseUnitStatisticRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitCountInSubProject,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：企业单位-当前登录企业单位详情查询接口
   * 描述：查询当前企业单位信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse
   * @date : 2022年6月9日 10:49:47
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnterpriseUnitInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitInfoInMyself,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponse>> {
    return commonRequestApi<EnterpriseUnitInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 网校级 - 根据单位ID查询当前网校下企业单位信息
   * @param query 查询 graphql 语法文档
   * @param unitId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getEnterpriseUnitInfoInServicer(
    unitId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitInfoInServicer,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponse>> {
    return commonRequestApi<EnterpriseUnitInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { unitId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-企业单位详情查询接口
   * 描述：查询当前子项目下指定企业单位的信息
   * @param unitId                  :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse
   * @date : 2022年6月9日 10:49:47
   * @param query 查询 graphql 语法文档
   * @param unitId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getEnterpriseUnitInfoInSubProject(
    unitId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitInfoInSubProject,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponse>> {
    return commonRequestApi<EnterpriseUnitInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { unitId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取指定门户下的友情链接集合
   * @param portalId 门户id
   * @return 友情链接集合响应
   * @param query 查询 graphql 语法文档
   * @param portalId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getFriendLinkListByPortalId(
    portalId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getFriendLinkListByPortalId,
    operation?: string
  ): Promise<Response<FriendLinkListResponse>> {
    return commonRequestApi<FriendLinkListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取友情链接集合
   * @param portalType 门户类型
   * @return 友情链接集合响应
   * @param query 查询 graphql 语法文档
   * @param portalType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getFriendLinkListByPortalType(
    portalType: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getFriendLinkListByPortalType,
    operation?: string
  ): Promise<Response<FriendLinkListResponse>> {
    return commonRequestApi<FriendLinkListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalType },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：政府单位-当前登录政府单位管理员信息
   * 描述：查询当前登录管理员的信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getGovernmentUnitAdminInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getGovernmentUnitAdminInfoInMyself,
    operation?: string
  ): Promise<Response<GovernmentUnitAdminInfoResponse>> {
    return commonRequestApi<GovernmentUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：政府单位管理员详情查询接口
   * 描述：查询当前政府单位下指定管理员的信息，如不存在返回null
   * @param accountId               : 帐户ID
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.GovernmentUnitAdminInfoResponse
   * @date : 2022年6月9日 16:50:10
   * @param query 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getGovernmentUnitAdminInfoInSubProject(
    accountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getGovernmentUnitAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<GovernmentUnitAdminInfoResponse>> {
    return commonRequestApi<GovernmentUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：获取政府单位信息接口
   * 描述：查询当前子项目下指定政府单位的信息
   * @param unitId                  :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.GovernmentUnitInfoResponse
   * @date : 2022年6月9日 10:49:47
   * @param query 查询 graphql 语法文档
   * @param unitId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getGovernmentUnitInfoInSubProject(
    unitId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getGovernmentUnitInfoInSubProject,
    operation?: string
  ): Promise<Response<GovernmentUnitInfoResponse>> {
    return commonRequestApi<GovernmentUnitInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { unitId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业信息
   * @param industryId 行业编号
   * @return 行业信息
   * @param query 查询 graphql 语法文档
   * @param industryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getIndustryInfo(
    industryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getIndustryInfo,
    operation?: string
  ): Promise<Response<IndustryResponse>> {
    return commonRequestApi<IndustryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { industryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业信息
   * @param industryId 行业编号
   * @return 行业信息
   * @param query 查询 graphql 语法文档
   * @param industryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getIndustryInfoV2(
    industryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getIndustryInfoV2,
    operation?: string
  ): Promise<Response<IndustryResponse>> {
    return commonRequestApi<IndustryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { industryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询行业属性 - 平台级
   * @param industryPropertyId 行业培训属性ID
   * @param query 查询 graphql 语法文档
   * @param industryPropertyId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getIndustryPropertyByIdInSubProject(
    industryPropertyId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getIndustryPropertyByIdInSubProject,
    operation?: string
  ): Promise<Response<IndustryPropertyResponse>> {
    return commonRequestApi<IndustryPropertyResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { industryPropertyId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询行業类型
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getIndustryType(
    params: { businessId?: string; code?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getIndustryType,
    operation?: string
  ): Promise<Response<GetIndustryTypeResponse>> {
    return commonRequestApi<GetIndustryTypeResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询职称等级信息
   * @param levelId 职称等级编号
   * @return 职称等级信息
   * @param query 查询 graphql 语法文档
   * @param levelId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getLeaderPositionLevel(
    levelId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getLeaderPositionLevel,
    operation?: string
  ): Promise<Response<LeaderPositionLevelResponse>> {
    return commonRequestApi<LeaderPositionLevelResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { levelId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取栏目配置信息集合
   * @param portalType 栏目类型
   * @return 栏目配置集合响应
   * @param query 查询 graphql 语法文档
   * @param portalType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getMenuSettingByPortalType(
    portalType: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getMenuSettingByPortalType,
    operation?: string
  ): Promise<Response<MenuSettingInfoListResponse>> {
    return commonRequestApi<MenuSettingInfoListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalType },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据分类id获取顶级分类信息
   * @param rootCategoryCode 顶级分类代码
   * @param code 分类代码
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getNewsCategoryId(
    params: { rootCategoryCode: string; code: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getNewsCategoryId,
    operation?: string
  ): Promise<Response<NewsCategoryResponse>> {
    return commonRequestApi<NewsCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询详细资讯
   * @param newId 资讯id
   * @return 资讯信息
   * @param query 查询 graphql 语法文档
   * @param newId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getNewsDetail(
    newId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getNewsDetail,
    operation?: string
  ): Promise<Response<NewsDetailResponse>> {
    return commonRequestApi<NewsDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { newId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 服务商-获取当前登录合同服务商管理员详情接口
   * @date : 2022/7/28 20:50
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOnlineSchoolAdminInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOnlineSchoolAdminInfoInMyself,
    operation?: string
  ): Promise<Response<ContractProviderAdminInfoResponse>> {
    return commonRequestApi<ContractProviderAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 服务商-合同服务商管理员详情查询接口
   * @date : 2022/7/28 20:50
   * @param accountId :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse
   * @param query 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOnlineSchoolAdminInfoInServicer(
    accountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOnlineSchoolAdminInfoInServicer,
    operation?: string
  ): Promise<Response<ContractProviderAdminInfoResponse>> {
    return commonRequestApi<ContractProviderAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-合同服务商管理员详情查询接口
   * @param query 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getOnlineSchoolAdminInfoInSubProject(
    accountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOnlineSchoolAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<ContractProviderAdminInfoResponse>> {
    return commonRequestApi<ContractProviderAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述 : 查询网校信息-网校开通统计接口
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOnlineSchoolInfoCount(
    request: OnlineSchoolInfoRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOnlineSchoolInfoCount,
    operation?: string
  ): Promise<Response<OnlineSchoolCountResponse>> {
    return commonRequestApi<OnlineSchoolCountResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 网校信息查询接口
   * @param query 查询 graphql 语法文档
   * @param onlineSchoolId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOnlineSchoolInfoResponseInSubProject(
    onlineSchoolId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOnlineSchoolInfoResponseInSubProject,
    operation?: string
  ): Promise<Response<OnlineSchoolInfoResponse>> {
    return commonRequestApi<OnlineSchoolInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { onlineSchoolId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据id获取物理地区信息
   * @param id 物理地区编号
   * @return 物理地区信息
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getPhysicalRegionById(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPhysicalRegionById,
    operation?: string
  ): Promise<Response<PhysicalRegionResponse>> {
    return commonRequestApi<PhysicalRegionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据门户ID查询门户信息-详情接口
   * @param portalId                : 门户id
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.PortalResponse
   * @Author： wtl
   * @Date： 2022/10/19 10:32
   * @param query 查询 graphql 语法文档
   * @param portalId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getPortalInfoInSubProject(
    portalId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPortalInfoInSubProject,
    operation?: string
  ): Promise<Response<PortalResponse>> {
    return commonRequestApi<PortalResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据项目id查询顶级资讯分类
   * @return 资讯分类信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRootNewsCategory(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRootNewsCategory,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据分类id获取顶级分类信息
   * @param necId 分类id
   * @return
   * @param query 查询 graphql 语法文档
   * @param necId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRootNewsCategoryById(
    necId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRootNewsCategoryById,
    operation?: string
  ): Promise<Response<NewsCategoryResponse>> {
    return commonRequestApi<NewsCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { necId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * @param type 服务类型 0或1
   * @return 服务地区列表
   * 查询服务地区
   * @Date 2023/6/14 17:57
   * @param query 查询 graphql 语法文档
   * @param type 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getServiceOrIndustryRegion(
    type: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getServiceOrIndustryRegion,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { type },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * @description: 查询服务地区（这里只是对原方法进行二次筛选）
   * @author: linq
   * @date: 2023/8/22 15:54
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getServiceOrIndustryRegionByQuery(
    request: ServiceOrIndustryRegionQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getServiceOrIndustryRegionByQuery,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * @param type 服务类型 0或1
   * @return 服务地区列表(分销商)
   * 查询服务地区
   * @Date 2023/6/14 17:57
   * @param query 查询 graphql 语法文档
   * @param type 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getServiceOrIndustryRegionInDistribution(
    type: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getServiceOrIndustryRegionInDistribution,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { type },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 :获取网校学员详细信息接口
   * 描述：查询当前网校下指定管理员的信息，如不存在返回null
   * @param studentUserId           :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse
   * @date : 2022/3/31 16:54
   * @param query 查询 graphql 语法文档
   * @param studentUserId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudentInfoInServicer(
    studentUserId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentInfoInServicer,
    operation?: string
  ): Promise<Response<StudentInfoResponse>> {
    return commonRequestApi<StudentInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { studentUserId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 :子项目-获取学员详细信息接口-详细接口
   * 描述：查询子项目下指定的学员信息，如不存在返回null
   * @param studentUserId           :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse
   * @date : 2022年11月9日 14:48:20
   * @param query 查询 graphql 语法文档
   * @param studentUserId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudentInfoInSubProject(
    studentUserId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentInfoInSubProject,
    operation?: string
  ): Promise<Response<StudentInfoResponse>> {
    return commonRequestApi<StudentInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { studentUserId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 :获取子项目学员的用户详细信息接口
   * 描述：查询当前子项目下指定学员的用户信息，如不存在返回null
   * @param studentUserId           :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse
   * @date : 2022/3/31 16:54
   * @param query 查询 graphql 语法文档
   * @param studentUserId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudentInfoInSubject(
    studentUserId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentInfoInSubject,
    operation?: string
  ): Promise<Response<StudentUserInfoResponse>> {
    return commonRequestApi<StudentUserInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { studentUserId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：获取网校在线学员数量接口
   * 描述：查询当前网校下时间段内累计在线学员数量接口,不传时间段则查询历史累计在线学员数量（一人一天计算一次）
   * @return : java.lang.Long
   * @Author： wtl
   * @Date： 2022年1月25日 16:29:08
   * @param query 查询 graphql 语法文档
   * @param dateScopeRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudentOnlineCountInServicer(
    dateScopeRequest: DateScopeRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentOnlineCountInServicer,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { dateScopeRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：获取子项目下学员注册数量统计接口
   * @param studentRegisteredStatisticsRequest:统计查询条件
   * @return :java.util.List
   * @Author： wtl
   * @Date： 2022年5月11日 09:57:01
   * @param query 查询 graphql 语法文档
   * @param studentRegisteredStatisticsRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudentRegisteredCountInSubProject(
    studentRegisteredStatisticsRequest: StudentRegisteredStatisticsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentRegisteredCountInSubProject,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { studentRegisteredStatisticsRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 查询当前登录供应商信息接口
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSupplierServicerInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSupplierServicerInfoInMyself,
    operation?: string
  ): Promise<Response<SupplierServicerResponse>> {
    return commonRequestApi<SupplierServicerResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-供应服务商详情查询接口
   * @param query 查询 graphql 语法文档
   * @param servicerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getSupplierServicerInfoInSubProject(
    servicerId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSupplierServicerInfoInSubProject,
    operation?: string
  ): Promise<Response<SupplierServicerResponse>> {
    return commonRequestApi<SupplierServicerResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { servicerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定培训类别信息
   * @param categoryId 培训类别编号
   * @return 培训类别信息
   * @param query 查询 graphql 语法文档
   * @param categoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTrainingCategoryInfo(
    categoryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTrainingCategoryInfo,
    operation?: string
  ): Promise<Response<TrainingCategoryResponse>> {
    return commonRequestApi<TrainingCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { categoryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 上下文服务商id、门户类型获取门户信息
   * @param portalType 门户类型(包含富文本信息) 1-WEB 2-移动端
   * @param servicerId 服务商ID
   * @return 门户信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTrainingInstitutionPortalInfo(
    params: { portalType: number; servicerId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTrainingInstitutionPortalInfo,
    operation?: string
  ): Promise<Response<PortalInfoResponse1>> {
    return commonRequestApi<PortalInfoResponse1>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取门户主题颜色
   * @return 主题颜色标识
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTrainingInstitutionPortalThemeColor(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTrainingInstitutionPortalThemeColor,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询单位类型
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getUnitType(
    params: { businessId?: string; code?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getUnitType,
    operation?: string
  ): Promise<Response<GetUnitTypeResponse>> {
    return commonRequestApi<GetUnitTypeResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据年度编号查询业务年度
   * @param yearId 年度编号
   * @return 年度信息
   * @param query 查询 graphql 语法文档
   * @param yearId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getYearById(
    yearId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getYearById,
    operation?: string
  ): Promise<Response<BusinessYearResponse>> {
    return commonRequestApi<BusinessYearResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { yearId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表
   * 这个口不会去查业务那边的配置
   * @param categoryCode 行业属性分类代码
   * @return 培训属性列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listALLIndustryPropertyRootByCategory(
    params: { industryId: string; categoryCode: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listALLIndustryPropertyRootByCategory,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表V2
   * 这个口不会去查业务那边的配置
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listALLIndustryPropertyRootByCategoryV2(
    request: TrainingPropertyCommonRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listALLIndustryPropertyRootByCategoryV2,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下的所有培训属性列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listAllIndustryProperty(
    params: { industryPropertyId: string; industryId: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listAllIndustryProperty,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取地区专题用户信息
   * @param userId 用户id（必填）
   * @param query 查询 graphql 语法文档
   * @param userId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listAreaTrainingChannelStudentInfoInSubProject(
    userId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listAreaTrainingChannelStudentInfoInSubProject,
    operation?: string
  ): Promise<Response<Array<AreaTrainingChannelInfoResponse>>> {
    return commonRequestApi<Array<AreaTrainingChannelInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { userId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据下级字典获取上级字典信息
   * @param query 查询 graphql 语法文档
   * @param salveId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessDictionaryAcrossTypeBySalveId(
    salveId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessDictionaryAcrossTypeBySalveId,
    operation?: string
  ): Promise<Response<Array<BusinessDictionaryAcrossTypeResponse>>> {
    return commonRequestApi<Array<BusinessDictionaryAcrossTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { salveId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取指定业务地区的下一级业务地区列表
   * @param id 业务地区编号
   * @return 业务地区
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessRegionChildById(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessRegionChildById,
    operation?: string
  ): Promise<Response<Array<BusinessRegionResponse>>> {
    return commonRequestApi<Array<BusinessRegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据id列表查询业务地区
   * @param idList 业务地区编号，最大支持200个
   * @return 业务地区
   * @param query 查询 graphql 语法文档
   * @param idList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessRegionListById(
    idList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessRegionListById,
    operation?: string
  ): Promise<Response<Array<BusinessRegionResponse>>> {
    return commonRequestApi<Array<BusinessRegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { idList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 批量查询指定路径下行政区划名称
   * @param pathList 行政区划路径，格式：/350000/350100/350101
   * @return 路径下行政区划名称
   * @param query 查询 graphql 语法文档
   * @param pathList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessRegionNameMap(
    pathList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessRegionNameMap,
    operation?: string
  ): Promise<Response<Array<BusinessTreeRegionNameMap>>> {
    return commonRequestApi<Array<BusinessTreeRegionNameMap>>(
      SERVER_URL,
      {
        query: query,
        variables: { pathList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查找指定业务代码的地区树结构的指定父节点下的子节点
   * @param request 请求参数对象
   * @return 业务地区
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessRegionTreeChild(
    request: BusinessRegionTreeQueryChildrenRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessRegionTreeChild,
    operation?: string
  ): Promise<Response<Array<BusinessTreeRegionResponse>>> {
    return commonRequestApi<Array<BusinessTreeRegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查找指定业务代码的地区树结构的根节点
   * @param request 请求参数对象
   * @return 业务地区
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessRegionTreeRoot(
    request: BusinessRegionTreeQueryRootRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessRegionTreeRoot,
    operation?: string
  ): Promise<Response<Array<BusinessTreeRegionResponse>>> {
    return commonRequestApi<Array<BusinessTreeRegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listChildNewsCategory(
    params: { status: number; necId: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listChildNewsCategory,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listChildNewsCategoryInServicer(
    params: { status: number; necId: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listChildNewsCategoryInServicer,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listChildNewsCategoryNeedLogin(
    params: { status: number; necId: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listChildNewsCategoryNeedLogin,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询学历字典
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listEducationProperty(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listEducationProperty,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业信息
   * @param industryIds 行业编号，最大支持200个
   * @return 行业信息
   * @param query 查询 graphql 语法文档
   * @param industryIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryInfo(
    industryIds: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryInfo,
    operation?: string
  ): Promise<Response<Array<IndustryResponse>>> {
    return commonRequestApi<Array<IndustryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { industryIds },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业信息  v2
   * @param industryIds 行业编号，最大支持200个
   * @return 行业信息
   * @param query 查询 graphql 语法文档
   * @param industryIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryInfoV2(
    industryIds: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryInfoV2,
    operation?: string
  ): Promise<Response<Array<IndustryResponse>>> {
    return commonRequestApi<Array<IndustryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { industryIds },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定查询条件下的培训属性信息列表
   * @param request 查询条件
   * @return 培训属性信息
   * <AUTHOR>
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyByOnlineSchool(
    request: SchoolTrainingPropertyQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyByOnlineSchool,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定查询条件下的培训属性信息列表
   * @param request 查询条件
   * @return 培训属性信息
   * <AUTHOR>
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyByOnlineSchoolV2(
    request: SchoolTrainingPropertyQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyByOnlineSchoolV2,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询行业属性编号下行业属性分类列表
   * @param industryPropertyId 行业属性编号
   * @return 行业属性分类列表
   * @param query 查询 graphql 语法文档
   * @param industryPropertyId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyCategory(
    industryPropertyId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyCategory,
    operation?: string
  ): Promise<Response<Array<IndustryPropertyCategoryResponse>>> {
    return commonRequestApi<Array<IndustryPropertyCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { industryPropertyId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询行业属性编号下行业属性分类列表
   * @param industryPropertyId 行业属性编号
   * @return 行业属性分类列表
   * @param query 查询 graphql 语法文档
   * @param industryPropertyId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyCategoryV2(
    industryPropertyId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyCategoryV2,
    operation?: string
  ): Promise<Response<Array<IndustryPropertyCategoryResponse>>> {
    return commonRequestApi<Array<IndustryPropertyCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { industryPropertyId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下指定行业属性分类下指定培训属性编号下子节点培训属性列表
   * @param request 查询条件
   * @return 培训属性列表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyChildByCategory(
    request: TrainingPropertyQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyChildByCategory,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下指定行业属性分类下指定培训属性编号下子节点培训属性列表
   * @param request 查询条件
   * @return 培训属性列表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyChildByCategoryV2(
    request: TrainingPropertyQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyChildByCategoryV2,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表
   * @param industryPropertyId 行业属性编号
   * @param categoryCode       行业属性分类代码
   * @return 培训属性列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyRootByCategory(
    params: { industryPropertyId: string; categoryCode: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyRootByCategory,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表
   * @param industryPropertyId 行业属性编号
   * @param categoryCode       行业属性分类代码
   * @return 培训属性列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryPropertyRootByCategoryV2(
    params: { industryPropertyId: string; industryId: string; categoryCode: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryPropertyRootByCategoryV2,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询行业类型子节点列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryTypeChild(
    params: { businessId?: string; parentId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryTypeChild,
    operation?: string
  ): Promise<Response<Array<IndustryTypeResponse>>> {
    return commonRequestApi<Array<IndustryTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询行业类型父节点列表
   * @param businessId
   * @param query 查询 graphql 语法文档
   * @param businessId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIndustryTypeRoot(
    businessId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIndustryTypeRoot,
    operation?: string
  ): Promise<Response<Array<IndustryTypeResponse>>> {
    return commonRequestApi<Array<IndustryTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { businessId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询职称等级信息
   * @param levelIds 职称等级编号，最大支持200个
   * @return 职称等级信息
   * @param query 查询 graphql 语法文档
   * @param levelIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listLeaderPositionLevel(
    levelIds: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listLeaderPositionLevel,
    operation?: string
  ): Promise<Response<Array<LeaderPositionLevelResponse>>> {
    return commonRequestApi<Array<LeaderPositionLevelResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { levelIds },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询根节点的职称等级列表
   * @return 职称等级列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listLeaderPositionLevelRoot(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listLeaderPositionLevelRoot,
    operation?: string
  ): Promise<Response<Array<LeaderPositionLevelResponse>>> {
    return commonRequestApi<Array<LeaderPositionLevelResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listNewsCategoryTree(
    params: { status: number; parentId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listNewsCategoryTree,
    operation?: string
  ): Promise<Response<Array<NewsCategoryTreeResponse>>> {
    return commonRequestApi<Array<NewsCategoryTreeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取指定物理地区的下一级业务地区列表
   * @param id 物理地区编号
   * @return 物理地区
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listPhysicalRegionChildById(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listPhysicalRegionChildById,
    operation?: string
  ): Promise<Response<Array<PhysicalRegionResponse>>> {
    return commonRequestApi<Array<PhysicalRegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据id列表查询物理地区
   * @param idList 物理地区编号，最大支持200个
   * @return 物理地区
   * @param query 查询 graphql 语法文档
   * @param idList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listPhysicalRegionListById(
    idList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listPhysicalRegionListById,
    operation?: string
  ): Promise<Response<Array<PhysicalRegionResponse>>> {
    return commonRequestApi<Array<PhysicalRegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { idList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param status 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listRootNewsCategory(
    status: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listRootNewsCategory,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { status },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param status 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listRootNewsCategoryNeedLogin(
    status: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listRootNewsCategoryNeedLogin,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { status },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据主字典ID获取所有从字典 （用于上下级关系的字典类型）
   * @param masterId
   * @param salveDictionaryType
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listSalveDictionaryByMasterId(
    params: { masterId: string; salveDictionaryType: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listSalveDictionaryByMasterId,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询当前网校合约下的培训属性
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listServicerContractPropertyByCategory(
    request: ServicerContractPropertyByCategoryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listServicerContractPropertyByCategory,
    operation?: string
  ): Promise<Response<Array<TrainingPropertyResponse>>> {
    return commonRequestApi<Array<TrainingPropertyResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业下的培训类别子节点列表
   * @param industryId 行业编号
   * @param categoryId 培训类别编号
   * @return 培训类别列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listTrainingCategoryChild(
    params: { industryId: string; categoryId: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTrainingCategoryChild,
    operation?: string
  ): Promise<Response<Array<TrainingCategoryResponse>>> {
    return commonRequestApi<Array<TrainingCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定培训类别信息
   * @param categoryIds 培训类别编号，最大支持200个
   * @return 培训类别信息
   * @param query 查询 graphql 语法文档
   * @param categoryIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listTrainingCategoryInfo(
    categoryIds: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTrainingCategoryInfo,
    operation?: string
  ): Promise<Response<Array<TrainingCategoryResponse>>> {
    return commonRequestApi<Array<TrainingCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { categoryIds },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定行业下的根节点培训类别
   * @param industryId 行业编号
   * @return 培训类别列表
   * @param query 查询 graphql 语法文档
   * @param industryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listTrainingCategoryRoot(
    industryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTrainingCategoryRoot,
    operation?: string
  ): Promise<Response<Array<TrainingCategoryResponse>>> {
    return commonRequestApi<Array<TrainingCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { industryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询单位类型子节点列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listUnitTypeChild(
    params: { businessId?: string; parentId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listUnitTypeChild,
    operation?: string
  ): Promise<Response<Array<UnitTypeResponse>>> {
    return commonRequestApi<Array<UnitTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询单位类型父节点列表
   * @param query 查询 graphql 语法文档
   * @param businessId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listUnitTypeRoot(
    businessId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listUnitTypeRoot,
    operation?: string
  ): Promise<Response<Array<UnitTypeResponse>>> {
    return commonRequestApi<Array<UnitTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { businessId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据年度编号列表查询业务年度
   * @param yearIdList 业务年度编号,最大支持200个
   * @return 业务年度列表
   * @param query 查询 graphql 语法文档
   * @param yearIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listYearListById(
    yearIdList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listYearListById,
    operation?: string
  ): Promise<Response<Array<BusinessYearResponse>>> {
    return commonRequestApi<Array<BusinessYearResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { yearIdList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表V2
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageALLIndustryPropertyRootByCategoryV2(
    params: { page?: Page; request?: TrainingPropertyCommonRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageALLIndustryPropertyRootByCategoryV2,
    operation?: string
  ): Promise<Response<TrainingPropertyResponsePage>> {
    return commonRequestApi<TrainingPropertyResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：分页获取用户信息列表接口
   * 描述：查询当前网校下的管理员信息，默认按创建时间降序排
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageAccountInfoInServicer(
    params: { page?: Page; request?: AccountUserInfoQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageAccountInfoInServicer,
    operation?: string
  ): Promise<Response<UserInfoResponsePage>> {
    return commonRequestApi<UserInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页获取用户信息列表接口
   * 描述：查询当前子项目下的管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page
   * @param request
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageAccountInfoInSubProject(
    params: { page?: Page; request?: AccountUserInfoQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageAccountInfoInSubProject,
    operation?: string
  ): Promise<Response<UserInfoResponsePage>> {
    return commonRequestApi<UserInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页获取分销商管理员列表接口
   * 描述：查询当前网校下的管理员信息(查询自己以及自己创建的管理员)，默认按创建时间降序排，如不存在返回null
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageAdminInInDistributor(
    params: { page?: Page; request?: AdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageAdminInInDistributor,
    operation?: string
  ): Promise<Response<AdminInfoResponsePage>> {
    return commonRequestApi<AdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取课程供应商管理员列表接口<br/>
   * 课程供应商包含个人账号，且不是账号归属某个单位，而是人员和单位产生关系
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageAdminInfoInCourseSupplier(
    params: { page?: Page; request?: AdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageAdminInfoInCourseSupplier,
    operation?: string
  ): Promise<Response<AdminInfoResponsePage>> {
    return commonRequestApi<AdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：分页获取网校管理员列表接口
   * 描述：查询当前网校下的管理员信息(查询自己以及自己创建的管理员)，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.AdminInfoResponse>
   * @date : 2022/4/2 14:19
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageAdminInfoInServicer(
    params: { page?: Page; request?: AdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageAdminInfoInServicer,
    operation?: string
  ): Promise<Response<AdminInfoResponsePage>> {
    return commonRequestApi<AdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页获取子项目下管理员列表接口
   * 描述：查询当前子项目下的管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.AdminInfoResponse>
   * @date : 2022年6月9日 16:50:21
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageAdminInfoInSubProject(
    params: { page?: Page; request?: AdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<AdminInfoResponsePage>> {
    return commonRequestApi<AdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页获取网校集体缴费管理员列表接口
   * 描述：查询当前网校下的集体缴费管理员分页信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.collective.CollectiveInfoResponse>
   * @date : 2022/4/2 14:20
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCollectiveInfoInServicer(
    params: { page?: Page; request?: CollectiveQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCollectiveInfoInServicer,
    operation?: string
  ): Promise<Response<CollectiveInfoResponsePage>> {
    return commonRequestApi<CollectiveInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯列表(用于顶级分类的业务平台)
   * @param queryRequest 简略资讯查询条件  todo
   * @return 资讯分页信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCompleteNews(
    params: { queryRequest?: NewsCompleteQueryRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCompleteNews,
    operation?: string
  ): Promise<Response<NewsCompleteResponsePage>> {
    return commonRequestApi<NewsCompleteResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 服务商-查询合同服务商管理员-分页列表接口
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse>
   * @date : 2022/7/26 11:51
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageContractProviderInfoAdminInfoInServicer(
    params: { page?: Page; request?: ContractProviderAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageContractProviderInfoAdminInfoInServicer,
    operation?: string
  ): Promise<Response<ContractProviderAdminInfoResponsePage>> {
    return commonRequestApi<ContractProviderAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-查询合同服务商管理员-分页列表接口
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ContractProviderAdminInfoResponse>
   * @date : 2022/7/26 11:51
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageContractProviderInfoAdminInfoInSubProject(
    params: { page?: Page; request?: ContractProviderAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageContractProviderInfoAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<ContractProviderAdminInfoResponsePage>> {
    return commonRequestApi<ContractProviderAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述 : 项目级-查询合同服务商-分页列表接口
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.ContractProviderResponse>
   * @date : 2022/7/26 9:16
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageContractProviderInfoInSubProject(
    params: { page?: Page; request?: ContractProviderRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageContractProviderInfoInSubProject,
    operation?: string
  ): Promise<Response<ContractProviderResponsePage>> {
    return commonRequestApi<ContractProviderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述 : 服务商-查询课件供应商管理员-分页列表接口
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ServicerAdminInfoResponse>
   * @date : 2022/7/26 11:51
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCoursewareSupplierAdminInfoInServicer(
    params: { page?: Page; request?: ServicerAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCoursewareSupplierAdminInfoInServicer,
    operation?: string
  ): Promise<Response<ServicerAdminInfoResponsePage>> {
    return commonRequestApi<ServicerAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-查询课件供应商管理员-分页列表接口
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.ServicerAdminInfoResponse>
   * @date : 2022/7/26 11:51
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCoursewareSupplierAdminInfoInSubProject(
    params: { page?: Page; request?: ServicerAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCoursewareSupplierAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<ServicerAdminInfoResponsePage>> {
    return commonRequestApi<ServicerAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 课程供应商GRPC分页查询
   * @param query 查询 graphql 语法文档
   * @param page 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCoursewareSupplierGRPCInSubject(
    page: Page,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCoursewareSupplierGRPCInSubject,
    operation?: string
  ): Promise<Response<CoursewareSupplierGRPCResponsePage>> {
    return commonRequestApi<CoursewareSupplierGRPCResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: { page },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-分页查询课件供应商-分页接口
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.servicer.CoursewareSupplierResponse>
   * @date : 2022年10月31日 11:44:28
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCoursewareSupplierInfoInSubProject(
    params: { page?: Page; request?: CoursewareSupplierRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCoursewareSupplierInfoInSubProject,
    operation?: string
  ): Promise<Response<CoursewareSupplierResponsePage>> {
    return commonRequestApi<CoursewareSupplierResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取当前网校下的企业单位信息
   * <br/>
   * <strong>注意：历史原因pageEnterpriseUnitInfoInServicer不能实现当前网校下，要用这个</strong>
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCurrentServicerEnterpriseUnitInfoInServicer(
    params: { page?: Page; request?: EnterpriseUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCurrentServicerEnterpriseUnitInfoInServicer,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取当前网校下的企业单位信息(分销商)
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCurrentServicerEnterpriseUnitInfoInServicerInDistribution(
    params: { page?: Page; request?: EnterpriseUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCurrentServicerEnterpriseUnitInfoInServicerInDistribution,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-分页查询分销服务商
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageDistributionServicerInfoInSubProject(
    params: { page?: Page; request?: DistributionServerRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageDistributionServicerInfoInSubProject,
    operation?: string
  ): Promise<Response<DistributionServicerResponsePage>> {
    return commonRequestApi<DistributionServicerResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述： 政府单位-查询本级及下属企业单位管理员-分页列表接口
   * 描述：查询政府单位管辖下的企业单位管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:10
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageEnterpriseUnitAdminInfoInGovernmentUnit(
    params: { page?: Page; request?: EnterpriseUnitAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitAdminInfoInGovernmentUnit,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：企业单位-查询当前登录企业单位下属管理员-分页列表接口
   * 描述：查询当前企业单位下的管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:10
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageEnterpriseUnitAdminInfoInMyself(
    params: { page?: Page; request?: AdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitAdminInfoInMyself,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询企业单位管理员-分页列表接口
   * 描述：查询当前子项目下的企业单位管理员信息，默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:21
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageEnterpriseUnitAdminInfoInSubProject(
    params: { page?: Page; request?: EnterpriseUnitAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：政府单位-查询本级及下属企业单位-分页列表接口
   * 描述：查询当前政府单位下管辖的企业单位信息，默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse>
   * @date : 2022年6月9日 10:47:54
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageEnterpriseUnitInfoInGovernmentUnit(
    params: { page?: Page; request?: EnterpriseUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitInfoInGovernmentUnit,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：服务商-查询企业单位-分页列表接口
   * 描述：默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse>
   * @date : 2022年7月29日15:59:49
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageEnterpriseUnitInfoInServicer(
    params: { page?: Page; request?: EnterpriseUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitInfoInServicer,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：项目级-查询企业单位-分页列表接口
   * 描述：查询当前子项目下的企业单位信息，默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse>
   * @date : 2022年6月9日 10:47:54
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageEnterpriseUnitInfoInSubProject(
    params: { page?: Page; request?: EnterpriseUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitInfoInSubProject,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：政府单位-查询本级及下属政府单位管理员-分页列表接口
   * 描述：查询当前政府单位下管辖的政府单位管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.GovernmentUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:21
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageGovernmentUnitAdminInfoInGovernmentUnit(
    params: { page?: Page; request?: GovernmentUnitAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageGovernmentUnitAdminInfoInGovernmentUnit,
    operation?: string
  ): Promise<Response<GovernmentUnitAdminInfoResponsePage>> {
    return commonRequestApi<GovernmentUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：政府单位-查询当前登录政府单位下属管理员-分页列表接口
   * 描述：查询当前政府单位下的政府单位管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.GovernmentUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:21
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageGovernmentUnitAdminInfoInMyself(
    params: { page?: Page; request?: AdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageGovernmentUnitAdminInfoInMyself,
    operation?: string
  ): Promise<Response<GovernmentUnitAdminInfoResponsePage>> {
    return commonRequestApi<GovernmentUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询政府单位管理员-分页列表接口
   * 描述：查询当前子项目下的政府单位管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.GovernmentUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:21
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageGovernmentUnitAdminInfoInSubProject(
    params: { page?: Page; request?: GovernmentUnitAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageGovernmentUnitAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<GovernmentUnitAdminInfoResponsePage>> {
    return commonRequestApi<GovernmentUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：政府单位-查询本级及下属政府单位-分页列表接口
   * 描述：查询当前政府单位以及下级政府单位信息，默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.GovernmentUnitInfoResponse>
   * @date : 2022年6月9日 10:47:54
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageGovernmentUnitInfoInGovernmentUnit(
    params: { page?: Page; request?: GovernmentUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageGovernmentUnitInfoInGovernmentUnit,
    operation?: string
  ): Promise<Response<GovernmentUnitInfoResponsePage>> {
    return commonRequestApi<GovernmentUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询政府单位-分页列表接口
   * 描述：查询当前子项目下的政府单位信息，默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.GovernmentUnitInfoResponse>
   * @date : 2022年6月9日 10:47:54
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageGovernmentUnitInfoInSubProject(
    params: { page?: Page; request?: GovernmentUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageGovernmentUnitInfoInSubProject,
    operation?: string
  ): Promise<Response<GovernmentUnitInfoResponsePage>> {
    return commonRequestApi<GovernmentUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询指定行业属性编号下指定行业属性分类下的培训属性列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageIndustryPropertyByCategoryInSubProject(
    params: { page?: Page; request?: PageIndustryPropertyByCategoryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageIndustryPropertyByCategoryInSubProject,
    operation?: string
  ): Promise<Response<TrainingPropertyResponsePage>> {
    return commonRequestApi<TrainingPropertyResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询行业属性 - 平台级
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageIndustryPropertyInSubProject(
    params: { page?: Page; request?: IndustryPropertyRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageIndustryPropertyInSubProject,
    operation?: string
  ): Promise<Response<IndustryPropertyResponsePage>> {
    return commonRequestApi<IndustryPropertyResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述 : 服务商-查询网校服务商管理员-分页列表接口  (分销能力专属)
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOnlineSchoolAdminInfoInFxpt(
    params: { page?: Page; request?: ContractProviderAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOnlineSchoolAdminInfoInFxpt,
    operation?: string
  ): Promise<Response<ContractProviderAdminInfoResponsePage>> {
    return commonRequestApi<ContractProviderAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 服务商-查询网校服务商管理员-分页列表接口
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOnlineSchoolAdminInfoInServicer(
    params: { page?: Page; request?: ContractProviderAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOnlineSchoolAdminInfoInServicer,
    operation?: string
  ): Promise<Response<ContractProviderAdminInfoResponsePage>> {
    return commonRequestApi<ContractProviderAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-查询网校服务商管理员-分页列表接口
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageOnlineSchoolAdminInfoInSubProject(
    params: { page?: Page; request?: ContractProviderAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOnlineSchoolAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<ContractProviderAdminInfoResponsePage>> {
    return commonRequestApi<ContractProviderAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述 : 查询网校信息-分页列表接口
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOnlineSchoolInfoResponseInSubProject(
    params: { page?: Page; request?: OnlineSchoolInfoRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOnlineSchoolInfoResponseInSubProject,
    operation?: string
  ): Promise<Response<OnlineSchoolInfoResponsePage>> {
    return commonRequestApi<OnlineSchoolInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：项目级-分页查询门户信息-分页接口
   * @param page                    : 分页对象
   * @param request                 : 查询对象
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.PortalResponse>
   * @Author： wtl
   * @Date： 2022/10/19 10:40
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pagePortalInfoInSubProject(
    params: { page?: Page; request?: PortalRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pagePortalInfoInSubProject,
    operation?: string
  ): Promise<Response<PortalResponsePage>> {
    return commonRequestApi<PortalResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前网校下的管理员
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageServicerUnitAdminInfoInServicer(
    params: { page?: Page; request?: EnterpriseUnitAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageServicerUnitAdminInfoInServicer,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：项目级-查询服务商企业单位管理员-分页列表接口
   * 描述：查询当前子项目下的企业单位管理员信息，默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:21
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageServicerUnitAdminInfoInSubProject(
    params: { page?: Page; request?: EnterpriseUnitAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageServicerUnitAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询简略资讯列表
   * @param queryRequest 简略资讯查询条件
   * @return 资讯分页信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSimpleNews(
    params: { queryRequest?: NewsSimpleQueryRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSimpleNews,
    operation?: string
  ): Promise<Response<NewsSimpleResponsePage>> {
    return commonRequestApi<NewsSimpleResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：分页获取网校学员列表接口
   * 描述：查询当前网校下的学员分页信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 : 查询参数
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse>
   * @date : 2022/4/1 17:17
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentInfoInServicer(
    params: { page?: Page; request?: StudentQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentInfoInServicer,
    operation?: string
  ): Promise<Response<StudentInfoResponsePage>> {
    return commonRequestApi<StudentInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：子项目-查询学员信息列表-分页接口
   * 描述：查询子项目下的学员分页信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 : 查询参数
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse>
   * @date : 2022年11月7日 09:37:49
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentInfoInSubProject(
    params: { page?: Page; request?: StudentQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentInfoInSubProject,
    operation?: string
  ): Promise<Response<StudentInfoResponsePage>> {
    return commonRequestApi<StudentInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 还没有实现，待确认实现方式
   * 功能描述：分页获取网校学员列表接口（供应商）
   * 描述：查询当前网校下的学员分页信息，默认按创建时间降序排，如不存在返回null
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentInfoInSupplier(
    params: { page?: Page; request?: StudentQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentInfoInSupplier,
    operation?: string
  ): Promise<Response<StudentInfoResponsePage>> {
    return commonRequestApi<StudentInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSubjectType(
    params: { industryPropertyIds?: Array<string>; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSubjectType,
    operation?: string
  ): Promise<Response<SubjectTypePageResponsePage>> {
    return commonRequestApi<SubjectTypePageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-分页查询供应服务商
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSupplierServicerInfoInSubProject(
    params: { page?: Page; request?: SupplierServerRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSupplierServicerInfoInSubProject,
    operation?: string
  ): Promise<Response<SupplierServicerResponsePage>> {
    return commonRequestApi<SupplierServicerResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据条件查询培训类别信息
   * @param queryRequest
   * @param page 分页参数
   * @return 培训类别信息
   * @throws InvalidProtocolBufferException
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageTrainingCategory(
    params: { queryRequest?: TrainingCategoryQueryRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageTrainingCategory,
    operation?: string
  ): Promise<Response<TrainingCategoryPageResponsePage>> {
    return commonRequestApi<TrainingCategoryPageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询专题简略资讯列表
   * 没传专题id也是默认查专题资讯，其他的查询口没传专题id查非专题资讯的
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageTrainingChannelSimpleNews(
    params: { queryRequest?: TrainingChannelNewsSimpleQueryRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageTrainingChannelSimpleNews,
    operation?: string
  ): Promise<Response<NewsSimpleResponsePage>> {
    return commonRequestApi<NewsSimpleResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询行业属性下未被添加的培训属性
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUnAddIndustryProperty(
    params: { page?: Page; request?: TrainingPropertyCommonRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageUnAddIndustryProperty,
    operation?: string
  ): Promise<Response<TrainingPropertyResponsePage>> {
    return commonRequestApi<TrainingPropertyResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取当前网校下用户账号信息列表接口
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUserAccountInfoInServicer(
    params: { page?: Page; request?: AccountUserInfoQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageUserAccountInfoInServicer,
    operation?: string
  ): Promise<Response<UserAccountInfoResponsePage>> {
    return commonRequestApi<UserAccountInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 政府单位下-统计各时间段内企业单位总数量
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticEnterpriseUnitGroupByTimeInGovernmentUnit(
    request: EnterpriseUnitDateHistogramRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticEnterpriseUnitGroupByTimeInGovernmentUnit,
    operation?: string
  ): Promise<Response<DateHistogramResponse>> {
    return commonRequestApi<DateHistogramResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 政府单位下-统计各行业类型下企业单位总数量
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticEnterpriseUnitIndustryInGovernmentUnit(
    request: EnterpriseUnitStatisticRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticEnterpriseUnitIndustryInGovernmentUnit,
    operation?: string
  ): Promise<Response<Array<EnterpriseUnitIndustryStatisticResponse>>> {
    return commonRequestApi<Array<EnterpriseUnitIndustryStatisticResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 政府单位下-统计各地区下企业单位总数量
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticEnterpriseUnitRegionInGovernmentUnit(
    request: EnterpriseUnitStatisticRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticEnterpriseUnitRegionInGovernmentUnit,
    operation?: string
  ): Promise<Response<Array<EnterpriseUnitRegionStatisticResponse>>> {
    return commonRequestApi<Array<EnterpriseUnitRegionStatisticResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 政府单位下-统计各单位类型下企业单位总数量
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticEnterpriseUnitTypeInGovernmentUnit(
    request: EnterpriseUnitStatisticRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticEnterpriseUnitTypeInGovernmentUnit,
    operation?: string
  ): Promise<Response<Array<EnterpriseUnitTypeStatisticResponse>>> {
    return commonRequestApi<Array<EnterpriseUnitTypeStatisticResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
