import CategoryVo from '@api/service/customer/online-school-config/online-school-partal-config/query/vo/CategoryVo'
import CourseVo from './CourseVo'

/**
 * @description 精品课程
 */
class HighQualityCourseVo {
  /**
   * 唯一标识
   */
  id: string
  /**
   * 配置类型
   */
  configType: string
  /**
   * 是否使用分类
   */
  usedCategory: boolean
  /**
   * 分类列表
   */
  categories: Array<CategoryVo> = new Array<CategoryVo>()
  /**
   * 课程列表
   */
  courseList: Array<CourseVo> = new Array<CourseVo>()
}

export default HighQualityCourseVo
