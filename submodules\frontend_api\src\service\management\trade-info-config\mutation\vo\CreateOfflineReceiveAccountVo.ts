import { CreateReceiveAccountRequest, ReceiveAccountExtProperty } from '@api/ms-gateway/ms-trade-configuration-v1'
import CreateReceiveAccountVo from './CreateReceiveAccountVo'
import MutationReceiveAccountVo from './MutationReceiveAccountVo'

class CreateWXPayReceiveAccountVo extends CreateReceiveAccountVo {
  /**
   * 开户银行
   */
  depositBank = ''
  /**
   * 开户户名
   */
  merchantName = ''
  /**
   * 柜台号
   */
  counterNumber = ''

  from(mutationReceiveAccountVo: MutationReceiveAccountVo) {
    this.accountName = mutationReceiveAccountVo.accountName
    this.accountNo = mutationReceiveAccountVo.accountNo
    this.accountType = mutationReceiveAccountVo.accountType
    this.merchantName = mutationReceiveAccountVo.merchantName
    this.refundWay = mutationReceiveAccountVo.refundWay
    this.taxPayerId = mutationReceiveAccountVo.taxPayerId
    this.depositBank = mutationReceiveAccountVo.depositBank
    this.counterNumber = mutationReceiveAccountVo.counterNumber
  }

  to() {
    const createReceiveAccountRequest = new CreateReceiveAccountRequest()
    createReceiveAccountRequest.accountType = this.accountType
    createReceiveAccountRequest.paymentChannelId = 'OFFLINE_PAY'
    createReceiveAccountRequest.name = this.accountName
    createReceiveAccountRequest.accountNo = this.accountNo
    createReceiveAccountRequest.refundWay = 2
    createReceiveAccountRequest.taxPayerId = this.taxPayerId
    createReceiveAccountRequest.properties = new Array<ReceiveAccountExtProperty>()
    createReceiveAccountRequest.properties.push({ name: 'depositBank', value: this.depositBank })
    createReceiveAccountRequest.properties.push({ name: 'counterNumber', value: this.counterNumber })
    createReceiveAccountRequest.properties.push({ name: 'merchantName', value: this.merchantName })
    return createReceiveAccountRequest
  }
}
export default CreateWXPayReceiveAccountVo
