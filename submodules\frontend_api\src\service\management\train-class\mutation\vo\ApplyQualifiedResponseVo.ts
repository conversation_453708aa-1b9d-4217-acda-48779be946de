/**
 * @description 申请一键学习资源返回值
 */
class ApplyQualifiedResponseVo {
  /**
   * 申请结果Code码
   * 500-内部异常
   * 501001-学员课程不存在
   * 501002-学员课程已失效
   * 501003-课后测验分数不可为空
   */
  applyResultCode: string = null

  /**
   * 申请结果信息
   */
  applyResultMessage = ''

  /**
   * 申请结果
   */
  applyResult = false

  // constructor(applyResultCode?: string, applyResultMessage?: string, applyResult?: boolean) {
  //   this.applyResultCode = applyResultCode
  //   this.applyResultMessage = applyResultMessage
  //   this.applyResult = applyResult
  // }
}

export default ApplyQualifiedResponseVo
