<template>
  <div>
    <template>
      <!--查看期别-->
      <el-drawer
        title="查看期别"
        :visible.sync="dialog"
        size="1250px"
        custom-class="m-drawer"
        :wrapperClosable="false"
        :modal-append-to-body="false"
      >
        <div class="drawer-bd">
          <!--条件查询-->
          <el-row :gutter="0" class="m-query">
            <el-form :inline="true" label-width="auto">
              <el-col :span="10">
                <el-form-item label="期别名称">
                  <!--                  <el-input v-model="periodQuery.issueName" clearable placeholder="请输入期别名称" />-->
                  <biz-issue-auto-select v-model="periodQuery.issueName" :commoditySkuId="commoditySkuId" />
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="报名时间">
                  <double-date-picker
                    :begin-create-time.sync="periodQuery.registerDateRange.startDate"
                    :end-create-time.sync="periodQuery.registerDateRange.endDate"
                    :isLimitEndTime="false"
                    beginTimePlaceholder="请选择报名成功时间"
                    endTimePlaceholder="请选择报名成功时间"
                  >
                  </double-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item>
                  <el-button type="primary" @click="search">查询</el-button>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
          <!--表格-->
          <el-table stripe :data="issueList" class="m-table" max-height="600px" v-loading="tabelLoading">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="期别信息" min-width="240">
              <template slot-scope="scope"
                >{{ scope.row.issueName || '-' }}
                <el-tag type="primary" effect="dark" size="mini">{{ scope.row.issueNo || '-' }}</el-tag></template
              >
            </el-table-column>
            <el-table-column label="开放报名时间" min-width="240">
              <template slot-scope="scope">
                <template v-if="scope.row.isEnableStudentEnroll">
                  <p><el-tag type="info" size="mini">开始</el-tag>{{ scope.row.registerBeginTime }}</p>
                  <p v-if="scope.row.registerEndTimeType == EndTimeTypeEnum.assign">
                    <el-tag type="info" size="mini">结束</el-tag>{{ scope.row.registerEndTime }}
                  </p>
                  <p v-else><el-tag type="info" size="mini">结束</el-tag>无关闭报名时间</p>
                </template>
                <p v-else>不开放报名</p>
              </template>
            </el-table-column>
            <el-table-column label="剩余报名人数" min-width="180">
              <template slot-scope="scope">{{ scope.row.remainingRegisterNumber }}</template>
            </el-table-column>
            <el-table-column label="操作" min-width="140">
              <template slot-scope="scope">
                <el-button type="text" @click="doCopy(scope.row.issueName, $event)">复制期别名称</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="f-mt10">
            <!--分页-->
            <hb-pagination :page="page" v-bind="page"> </hb-pagination>
          </div>
        </div>
        <div class="drawer-ft m-btn-bar">
          <el-button type="primary" @click="dialog = false">取消</el-button>
        </div>
      </el-drawer>
    </template>
  </div>
</template>
<script lang="ts">
  import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import { handleClipboard } from './clipboard'

  import QueryCanChangeIssueParam from '@api/service/management/train-class/offlinePart/model/QueryCanChangeIssueParam'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import QueryTrainClassIssue, {
    QueryTrainClassIssueListParam
  } from '@api/service/management/train-class/offlinePart/QueryTrainClassIssue'
  import { EndTimeTypeEnum } from '@api/service/common/scheme/enum/EndTimeType'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import BizIssueAutoSelect from '@hbfe/jxjy-admin-components/src/biz/biz-issue-auto-select.vue'
  @Component({
    components: {
      DoubleDatePicker,
      BizIssueAutoSelect
    }
  })
  export default class extends Vue {
    @Prop({ type: String, default: '' }) commoditySkuId: string
    @Watch('commoditySkuId', { immediate: true })
    onCommoditySkuIdChange(val: string) {
      this.periodQuery.commoditySkuId = val
      this.periodQuery.issueName = ''
      this.periodQuery.registerDateRange.endDate = null
      this.periodQuery.registerDateRange.startDate = null
      this.search()
    }
    page: UiPage
    dialog = false
    tabelLoading = false
    /**
     * 期别关闭类型
     */
    EndTimeTypeEnum = EndTimeTypeEnum
    /**
     * 期别列表
     */
    issueList = new Array<IssueConfigDetail>()
    /**
     * 期别列表查询参数
     */
    periodQuery = new QueryTrainClassIssueListParam()
    constructor() {
      super()
      this.page = new UiPage(this.pagePeriodList, this.pagePeriodList)
    }
    // 是否是专题管理员
    isZtGly = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)

    /**
     * 查询方法
     */
    queryTrainClassIssue = new QueryTrainClassIssue()
    search() {
      this.pagePeriodList()
    }
    /**
     * 查询分页
     */
    async pagePeriodList() {
      this.tabelLoading = true
      try {
        if (this.isZtGly) {
          this.issueList = await this.queryPeriodZtClassList()
        } else {
          this.issueList = await this.queryPeriodWxClassList()
        }
      } catch (e) {
        console.log('获取可更换列表失败！', e)
      } finally {
        this.tabelLoading = false
      }
    }

    // 网校查询
    async queryPeriodWxClassList() {
      return await this.queryTrainClassIssue.queryTrainClassIssueList(this.periodQuery)
    }
    // 专题查询
    async queryPeriodZtClassList() {
      return await this.queryTrainClassIssue.queryTrainClassIssueList(this.periodQuery)
    }
    doCopy(content: string, event: MouseEvent) {
      return handleClipboard(content, event)
    }
  }
</script>

<style scoped></style>
