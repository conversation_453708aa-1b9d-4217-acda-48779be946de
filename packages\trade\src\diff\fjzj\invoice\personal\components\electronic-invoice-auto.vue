<template>
  <div class="f-p15">
    <template
      v-if="$hasPermission('autoBill,autoBillZt')"
      desc="自动开票列表"
      actions="autoBill:@AutoInvoice,@RedInvoice,handleClick#autoBillZt:@AutoInvoice,@RedInvoice,handleClick"
    >
      <el-tabs v-model="activeName2" type="card" class="m-tab-card" @tab-click="handleClick">
        <template
          v-if="$hasPermission('autoInvoice,autoInvoiceZt')"
          desc="autoInvoice:电子普通发票（自动开票）,autoInvoiceZt:电子普通发票（自动开票）(专题)"
          actions="autoInvoice:@AutoInvoice#autoInvoiceZt:@AutoInvoice"
        >
          <el-tab-pane label="增值税电子普通发票（自动开票）" name="auto-invoice">
            <auto-invoice ref="auto-invoice"></auto-invoice>
          </el-tab-pane>
        </template>
        <template
          v-if="$hasPermission('redInvoice,redInvoiceZt')"
          desc="redInvoice:冲红发票,redInvoiceZt:冲红发票(专题)"
          actions="redInvoice:@RedInvoice#redInvoiceZt:@RedInvoice"
        >
          <el-tab-pane label="冲红发票" name="red-invoice">
            <red-invoice ref="red-invoice"></red-invoice>
          </el-tab-pane>
        </template>
      </el-tabs>
    </template>
  </div>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import AutoInvoice from '@hbfe/jxjy-admin-trade/src/diff/fjzj/invoice/personal/components/auto-invoice.vue'
  import RedInvoice from '@hbfe/jxjy-admin-trade/src/diff/fjzj/invoice/personal/components/red-invoice.vue'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  @Component({
    components: { AutoInvoice, RedInvoice }
  })
  export default class extends Vue {
    @Ref('auto-invoice')
    autoInvoice: AutoInvoice
    @Ref('red-invoice')
    redInvoice: RedInvoice
    activeName2 = 'auto-invoice'
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    async handleClick(tab: any) {
      if (tab.name === 'auto-invoice') {
        this.autoInvoice?.page?.currentChange(1)
      } else if (tab.name === 'red-invoice') {
        this.redInvoice?.page?.currentChange(1)
      }
    }
  }
</script>
