<route-meta>
{ "isMenu": true, "title": "课件管理", "sort": 1, "icon": "icon-kejian" }
</route-meta>
<template>
  <div class="f-p15" v-if="$hasPermission('query')" desc="查询" actions="created">
    <div class="f-mb15">
      <template v-if="$hasPermission('create')" desc="新建" actions="@hbfe/jxjy-admin-courseware/src/create.vue">
        <el-button type="primary" icon="el-icon-plus" @click="goCreate()">新建课件</el-button>
      </template>
      <el-button
        type="primary"
        plain
        @click="goCategory"
        v-if="$hasPermission('category')"
        desc="课件分类管理"
        actions="@hbfe/jxjy-admin-courseware/src/category.vue"
        >课件分类管理</el-button
      >
      <template v-if="$hasPermission('export')" desc="外链课件导入" actions="@hbfe/jxjy-admin-courseware/src/off-chain-export.vue">
        <el-button type="primary" icon="el-icon-link" @click="goExport()">外链课件导入</el-button>
      </template>
      <!-- </template> -->
    </div>
    <el-card shadow="never" class="m-card f-mb15">
      <hb-search-wrapper :model="queryCoursewareListParamVo" @reset="resetCondition">
        <el-form-item label="课件名称">
          <!-- 模糊匹配 -->
          <el-autocomplete
            v-model="queryCoursewareListParamVo.name"
            :fetch-suggestions="querySearch"
            placeholder="请输入课件名称"
            :trigger-on-focus="false"
            @select="handleSelect"
            clearable
          >
            <template slot-scope="{ item }" v-if="noData">
              <div class="default">{{ item.default }}</div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="课件供应商">
          <biz-courseware-supplier
            v-model="queryCoursewareListParamVo.providers"
            placeholder="全部"
          ></biz-courseware-supplier>
        </el-form-item>
        <el-form-item label="课件类型">
          <biz-courseware-type
            v-model="queryCoursewareListParamVo.type.current"
            placeholder="全部"
          ></biz-courseware-type>
        </el-form-item>
        <el-form-item label="课件分类">
          <biz-courseware-category
            v-model="categoryIdList"
            :filterable="true"
            :showRootNode="false"
            placeholder="全部"
          ></biz-courseware-category>
        </el-form-item>
        <el-form-item label="课件状态">
          <el-select v-model="queryCoursewareListParamVo.enable" clearable placeholder="全部">
            <el-option
              v-for="item in statusValues"
              :label="item.name"
              :value="item.value"
              :key="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="转换状态">
          <el-select v-model="queryCoursewareListParamVo.transformStatus.current" clearable placeholder="全部">
            <el-option
              v-for="item in changeStatusValues"
              :label="item.desc"
              :value="item.code"
              :key="item.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="媒体源">
          <el-select v-model="queryCoursewareListParamVo.mediaType.current" clearable placeholder="全部">
            <el-option
              v-for="item in mediaSourceType"
              :label="item.desc"
              :value="item.code"
              :key="item.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <double-date-picker
            :begin-create-time.sync="queryCoursewareListParamVo.createTime[0]"
            :end-create-time.sync="queryCoursewareListParamVo.createTime[1]"
          ></double-date-picker>
        </el-form-item>

        <template slot="actions">
          <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
        </template>
      </hb-search-wrapper>
      <!--表格-->
      <el-table
        stripe
        :data="pageList"
        max-height="500px"
        class="m-table"
        v-loading="query.loading"
        ref="coursewareTable"
      >
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="课件名称" min-width="160" prop="name"></el-table-column>
        <el-table-column label="课件供应商" min-width="150" prop="providerName"></el-table-column>
        <el-table-column label="课件分类" min-width="150" prop="categoryName"></el-table-column>
        <el-table-column label="状态" min-width="80" align="center" header-align="center">
          <template slot-scope="{ row }">
            <el-tag type="success" v-if="row.enable">正常</el-tag>
            <el-tag type="info" v-else>停用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="转换状态" min-width="110" align="center" header-align="center">
          <template slot-scope="{ row }">
            <el-badge
              is-dot
              :type="statusMapType[row.status.current]"
              class="badge-status"
              v-if="row.status != '转换失败'"
            >
              {{ row.status }}
            </el-badge>
            <el-tooltip placement="top" v-else-if="row.errorSource == 2 && row.status == '转换失败'">
              <el-badge is-dot :type="statusMapType[row.status.current]" class="badge-status">
                {{ row.status }}
              </el-badge>
              <div slot="content">
                <p>如上一次修改包含替换媒体，可能是转码后获取的媒体时长小于原有媒体时长导致本次的转换失败。</p>
                <p class="f-mt5">
                  如需更正请点击修改，重新替换媒体，时长要求大于等于原有媒体时长，提交后待系统重新解析。
                </p>
              </div>
            </el-tooltip>
            <el-badge is-dot :type="statusMapType[row.status.current]" class="badge-status" v-else>
              {{ row.status }}
            </el-badge>
          </template>
        </el-table-column>
        <el-table-column label="课件类型" min-width="100">
          <template slot-scope="{ row }">{{ row.type.current == undefined ? '-' : row.type }}</template>
        </el-table-column>
        <el-table-column label="时长" min-width="100" prop="timeLengthFormat">
          <template slot-scope="{ row }">{{ row.timeLengthFormat }}</template>
        </el-table-column>
        <el-table-column label="创建人" min-width="110">
          <template slot-scope="{ row }">
            {{ row.creator.name }}
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          min-width="180"
          prop="createTime"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column label="是否被引用" min-width="110" align="center">
          <template slot-scope="{ row }">
            {{ row.isBeingUsed ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" align="center">
          <template slot-scope="{ row }">
            <el-button
              type="text"
              size="mini"
              @click="detail(row.id)"
              v-if="$hasPermission('detail')"
              desc="详情"
              actions="@hbfe/jxjy-admin-courseware/src/detail.vue"
              >详情</el-button
            >
            <el-button
              type="text"
              size="mini"
              @click="editCourseware(row.id)"
              v-if="$hasPermission('modify')"
              desc="修改"
              actions="@hbfe/jxjy-admin-courseware/src/modify.vue"
              >修改</el-button
            >
            <!--          </template>-->
            <hb-popconfirm
              placement="top"
              title="确定要删除该课件？"
              @confirm="deleteCourseware(row.id)"
              v-if="$hasPermission('remove')"
              desc="删除"
              actions="deleteCourseware"
            >
              <el-button type="text" slot="reference" size="mini">删除</el-button>
            </hb-popconfirm>

            <template v-if="$hasPermission('enable')" desc="启停用" actions="changeStatus">
              <hb-popconfirm :title="getTitle(row.enable)" placement="top" @confirm="changeStatus(row.id, row.enable)">
                <el-button
                  type="text"
                  slot="reference"
                  size="mini"
                  :disabled="row.status == '转换失败' || row.status == '转换中'"
                >
                  {{ row.enable ? '停用' : '启用' }}
                </el-button>
              </hb-popconfirm>
            </template>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="m-tooltip"
              v-if="statusMapType[row.status.current] !== 'success'"
            >
              <el-button type="text" style="color: #C0C4CC;cursor:not-allowed">预览</el-button>
              <div slot="content">课件转换状态为转换中/转换失败，暂不支持预览</div>
            </el-tooltip>
            <template
              v-if="$hasPermission('preview')"
              desc="预览"
              actions="@hbfe/jxjy-admin-components/src/previewer/components/preview-courseware.vue"
            >
              <el-button
                type="text"
                size="mini"
                @click="previewCourseware(row)"
                v-if="statusMapType[row.status.current] == 'success'"
              >
                预览
              </el-button>
            </template>

            <!-- </template> -->
          </template>
        </el-table-column>
      </el-table>
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </el-card>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="450px" class="m-dialog">
      <div class="dialog-alert">
        <!--警告-->
        <i class="icon el-icon-warning warning"></i>
        <span class="txt">课件已被课程引用，无法删除！请先到课程将对应的课件移除！</span>
      </div>
      <div slot="footer">
        <!--        <el-button @click="dialogVisible = false">取 消</el-button>-->
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Watch } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import QueryCoursewareListParamVo from '@api/service/management/resource/courseware/query/vo/QueryCoursewareListParamVo'
  import StandardRouteJumper from '@hbfe/jxjy-admin-common/src/StandardRouteJumper'
  import BizCoursewareSupplier from '@hbfe/jxjy-admin-components/src/biz/biz-courseware-supplier.vue'
  import CoursewareCommon from '@/store/modules-ui/courseware/common/CoursewareCommon'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import CoursewareListDetail from '@api/service/management/resource/courseware/query/vo/CoursewareListDetail'
  import CoursewareTransformStatus from '@api/service/management/resource/courseware/enum/CoursewareTransformStatus'
  import previewCourseware from '@hbfe/jxjy-admin-components/src/previewer/Previewer'
  import { cloneDeep } from 'lodash'
  import CoursewareDetail from '@api/service/management/resource/courseware/query/vo/CoursewareDetail'
  import MediaSourceType from '@api/service/management/resource/courseware/enum/MediaSourceType'
  import { debounce, bind } from 'lodash-decorators'
  @Component({
    components: { DoubleDatePicker, BizCoursewareSupplier }
  })
  export default class extends Vue {
    page: UiPage
    query: Query = new Query()
    queryCoursewareListParamVo: QueryCoursewareListParamVo = new QueryCoursewareListParamVo()
    pageList: Array<CoursewareListDetail> = new Array<CoursewareListDetail>()
    routeJumper: StandardRouteJumper
    statusValues = CoursewareCommon.statusValues
    changeStatusValues = new CoursewareTransformStatus().list()
    mediaSourceType = new MediaSourceType().list()
    dialogVisible = false
    coursewareStatus = CoursewareTransformStatus
    statusMapType = {
      [CoursewareTransformStatus.enum.UNAVAILABLE]: 'danger',
      [CoursewareTransformStatus.enum.AVAILABLE]: 'success',
      [CoursewareTransformStatus.enum.TRANSCODING]: 'primary'
    }
    categoryIdList: Array<string> = new Array<string>()
    results = new Array<any>() //课件名称模糊查询匹配的数据集合
    noData = false //课件名称模糊查询是否能匹配数据
    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    async created() {
      this.routeJumper = new StandardRouteJumper(this.$route.meta.group, this.$router)
      this.doSearch()
    }

    // 模糊匹配
    async querySearch(queryString: string, callBack: Function) {
      const blurPage = cloneDeep(this.page)
      blurPage.pageNo = 1
      blurPage.pageSize = 5
      this.noData = false
      let restaurants = new Array<CoursewareListDetail>()
      //预查询-用于模糊匹配
      restaurants = await ResourceModule.coursewareFactory.queryCourseware.pageCourseware(
        blurPage,
        this.queryCoursewareListParamVo
      )
      if (queryString) {
        this.results = restaurants.filter(item => {
          return item.name.indexOf(queryString) !== -1
        })
      }
      this.results.forEach(item => {
        item.value = item.name
      })

      if (!this.results.length) {
        this.noData = true
        this.results = [{ default: '无匹配数据' }]
      }
      // 调用 callback 返回模糊匹配查询到的数据
      callBack(this.results)
    }
    async handleSelect() {
      if (!this.noData) {
        this.page.pageNo = 1
        this.doSearch()
      }
    }
    @Watch('categoryIdList')
    changeCategoryId() {
      if (this.categoryIdList?.length) {
        this.queryCoursewareListParamVo.categoryIdList = [this.categoryIdList[this.categoryIdList.length - 1]]
      } else {
        this.queryCoursewareListParamVo.categoryIdList = []
      }
    }

    async doSearch() {
      this.query.loading = true
      try {
        if (this.categoryIdList?.length) {
          this.queryCoursewareListParamVo.categoryIdList = [this.categoryIdList[this.categoryIdList.length - 1]]
        } else {
          this.queryCoursewareListParamVo.categoryIdList = []
        }
        this.pageList = await ResourceModule.coursewareFactory.queryCourseware.pageCourseware(
          this.page,
          this.queryCoursewareListParamVo
        )
        console.log(this.pageList, 'this.pageList')
      } catch (e) {
        console.log(e)
        // todo
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['coursewareTable'] as any)?.doLayout()
        this.query.loading = false
      }
    }
    resetCondition() {
      this.page.pageNo = 1
      this.queryCoursewareListParamVo = new QueryCoursewareListParamVo()
      this.categoryIdList = new Array<string>()
      this.doSearch()
    }

    getTitle(enabled: boolean) {
      return enabled
        ? '确定要停用该课件？停用后已选择该课件的课程仍然可以播放，只是新的课程无法引用该课件。'
        : '确定要启用该课件？'
    }

    // 启用/停用课件
    async changeStatus(id: string, enabled: boolean) {
      let res = undefined
      try {
        if (enabled) {
          res = await ResourceModule.coursewareFactory.getBizCourseware(id).doDisable()
        } else {
          res = await ResourceModule.coursewareFactory.getBizCourseware(id).doEnable()
        }
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.pageList.filter(courseware => id === courseware.id)[0].enable = !enabled
        } else {
          this.$message.error((res.errors?.length && res.errors[0].message) || '操作失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error((e.status?.errors?.length && e.status?.errors[0].message) || e?.message || '操作失败')
      }
    }

    @bind
    @debounce(200)
    async deleteCourseware(id: string) {
      try {
        const res = await ResourceModule.coursewareFactory.getBizCourseware(id).doRemove()
        if (res.code === 200) {
          this.$message.success('删除成功')
          await this.doSearch()
        } else {
          this.$message.error((res.errors[0] && res.errors[0].message) || '删除失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error((e.status?.errors?.length && e.status?.errors[0].message) || e?.message || '删除失败')
      }
    }

    goCategory() {
      this.$router.push('/resource/courseware/category')
    }

    detail(id: string) {
      this.$router.push('/resource/courseware/detail/' + id)
    }

    editCourseware(id: string) {
      this.$router.push('/resource/courseware/modify/' + id)
    }

    previewCourseware(row: CoursewareListDetail) {
      console.log(row, 'row')
      previewCourseware.previewCourseware(
        row.id,
        row.type,
        this.pageList.map((detail: CoursewareListDetail) => ({
          name: detail.name,
          id: detail.id,
          type: detail.type.current
        }))
      )
    }

    //外链导入方法
    goExport() {
      this.$router.push('/resource/courseware/off-chain-export')
    }
    goCreate() {
      this.$router.push('/resource/courseware/create')
    }
  }
</script>
<style lang="scss" scoped>
  .default {
    color: #999;
  }
  .el-form-item__content > div {
    width: 100%;
  }
</style>
