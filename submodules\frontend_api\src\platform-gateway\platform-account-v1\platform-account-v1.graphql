schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""导出全部学员导入数据
		@param mainTaskId 任务id
		@return 学员导入数据
	"""
	exportAllStudentResult(mainTaskId:String!):ExportStudentImportResultResponse
	"""导出失败学员导入数据
		@param mainTaskId 任务id
		@return 失败学员导入数据
	"""
	exportErrorStudentResult(mainTaskId:String!):ExportStudentImportResultResponse
}
type Mutation {
	"""批量导入学员
		@param request 批量导入学员请求信息，新增密码生效范围和是否更新已注册学员信息字段
	"""
	batchImportStudent(request:BatchImportStudentRequest):Void
	compensatingStudentAccount(request:CompensatingStudentRequest):String @optionalLogin
}
"""<AUTHOR> xucenhao"""
input CompensatingStudentRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.appservice.request.CompensatingStudentRequest") {
	"""文件名称"""
	fileName:String
	type:Int!
	params:Map
}
"""批量导入学员请求信息
	@author: zhengp 2022/2/22 9:08
"""
input BatchImportStudentRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.BatchImportStudentRequest") {
	"""文件路径"""
	filePath:String
	"""文件名"""
	fileName:String
	"""密码"""
	password:String
	"""默认密码类型
		@see com.fjhb.domain.basicdata.api.user.consts.DefaultPasswordTypeConsts
	"""
	defaultPasswordType:Int
	"""密码生效范围--1:仅新用户, 2:全部用户（含已注册）
		@see com.fjhb.domain.basicdata.api.account.consts.PasswordEffectiveRange
	"""
	passwordEffectiveRange:Int
	"""已注册学员更新基础信息:是否只做创建 只做创建为 true 创建+更新为false"""
	onlyCreate:Boolean
}
"""获取导入学员信息执行结果返回信息
	@author: zhengp 2022/5/9 14:52
"""
type ExportStudentImportResultResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.appservice.certificatebatchprint.synctask.response.ExportStudentImportResultResponse") {
	"""导入学员信息执行结果文件地址"""
	fileUrl:String
}

scalar List
