<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="150px" class="m-text-form is-column">
              <el-form-item label="培训形式：">面授</el-form-item>
              <el-form-item label="方案类型：">培训班</el-form-item>
              <el-form-item label="网授选课方式：">选课规则</el-form-item>
              <el-form-item label="方案封面：">
                <el-image
                  src="/assets/images/web-default-banner.jpg"
                  :preview-src-list="['/assets/images/web-default-banner.jpg']"
                  class="course-pic"
                />
                <p class="f-cr f-mt10">注：图片比例为16:9，请选择符合该比例的图片。</p>
              </el-form-item>
              <el-form-item label="方案名称：">方案名称方案名称方案名称方案名称</el-form-item>
              <el-form-item label="年度：">2022</el-form-item>
              <el-form-item label="地区：">福建省</el-form-item>
              <el-form-item label="行业：">建设行业</el-form-item>
              <el-form-item label="科目类型：">专业科目</el-form-item>
              <el-form-item label="培训类别：">一级注册建造师</el-form-item>
              <el-form-item label="培训专业：">培训专业</el-form-item>
              <el-form-item label="培训须知：">
                温馨提示:根据闽人社办〔2024〕74号文件要求，每日最大学习时长不超过540分钟，请您合理安排好学习时间
              </el-form-item>
              <el-form-item label="培训方案简介：">
                <div class="m-intro">
                  <p>读取培训方案内配置的图文</p>
                  <img src="./assets/images/demo-special-web-001.png" />
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-tabs v-model="activeName1" type="card" class="m-tab-card is-sticky">
        <el-tab-pane name="first">
          <div slot="label">
            <i class="f-pl5">线上课程</i>
          </div>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-plr20 f-pt20">
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-small bg-gray is-border-bottom">
                  <span class="tit-txt">必修课</span>
                  <el-tag type="danger" effect="dark" size="mini" class="f-ml10">必修</el-tag>
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                    <div slot="content">
                      本分类的课程为必修课程，报名成功后直接推送给学员，无需选课。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置。
                    </div>
                  </el-tooltip>
                  <span class="f-fb f-ml20 f-flex-sub f-tr">
                    一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时
                  </span>
                </div>
                <el-row class="is-height">
                  <el-col :sm="8" :xl="6" class="is-border-right">
                    <div class="f-p20">
                      <el-tree :data="data" node-key="id" :expand-on-click-node="false" class="m-course-tree">
                        <div class="custom-tree-node">
                          <div class="tit">分类名称</div>
                        </div>
                      </el-tree>
                    </div>
                  </el-col>
                  <el-col :sm="16" :xl="18">
                    <div class="f-p20">
                      <div class="f-flex f-align-center">
                        <div class="m-tit is-mini">
                          <span class="tit-txt">分类名称</span>
                        </div>
                        <div class="f-fb f-flex-sub">（一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时）</div>
                      </div>
                      <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                        <el-table-column type="index" label="No." width="60" align="center" fixed="left">
                        </el-table-column>
                        <el-table-column label="课程名称" min-width="240" fixed="left">
                          <template>课程名称课程名称课程名称课程名称</template>
                        </el-table-column>
                        <el-table-column label="分类信息" min-width="200">
                          <template>一级分类>二级分类>三级分类</template>
                        </el-table-column>
                        <el-table-column label="所属课程包名称" min-width="200">
                          <template>所属课程包名称课程包名称</template>
                        </el-table-column>
                        <el-table-column label="课程学时数" width="120" align="center">
                          <template>10</template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-small bg-gray is-border-bottom">
                  <span class="tit-txt">选修课</span>
                  <el-tag type="danger" effect="dark" size="mini" class="f-ml10">选修</el-tag>
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                    <div slot="content">
                      此分类下的课程为选修课程，支持配置选课要求学时。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置。
                    </div>
                  </el-tooltip>
                  <span class="f-fb f-ml20 f-flex-sub f-tr">
                    一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时
                  </span>
                </div>
                <el-row class="is-height">
                  <el-col :sm="8" :xl="6" class="is-border-right">
                    <div class="f-p20">
                      <el-tree :data="data" node-key="id" :expand-on-click-node="false" class="m-course-tree">
                        <div class="custom-tree-node">
                          <div class="tit">分类名称</div>
                        </div>
                      </el-tree>
                    </div>
                  </el-col>
                  <el-col :sm="16" :xl="18">
                    <div class="f-p20">
                      <div class="f-flex f-align-center">
                        <div class="m-tit is-mini">
                          <span class="tit-txt">分类名称</span>
                        </div>
                        <div class="f-fb f-flex-sub">（一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时）</div>
                      </div>
                      <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                        <el-table-column type="index" label="No." width="60" align="center" fixed="left">
                        </el-table-column>
                        <el-table-column label="课程名称" min-width="240" fixed="left">
                          <template>课程名称课程名称课程名称课程名称</template>
                        </el-table-column>
                        <el-table-column label="分类信息" min-width="200">
                          <template>一级分类>二级分类>三级分类</template>
                        </el-table-column>
                        <el-table-column label="所属课程包名称" min-width="200">
                          <template>所属课程包名称课程包名称</template>
                        </el-table-column>
                        <el-table-column label="课程学时数" width="120" align="center">
                          <template>10</template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
              <!--自主选课-->
              <p class="f-pb15 f-fb">课程展示方式：无分类</p>
              <el-button type="primary" plain class="f-mb10">查看必学课程</el-button>
              <el-table stripe :data="tableData" max-height="500px" class="m-table f-mb15">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="课程名称" min-width="240" fixed="left">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-tag type="danger" size="mini" class="f-mr5">必学</el-tag>
                      课程名称课程名称课程名称课程名称
                    </div>
                    <div v-else>课程名称课程名称课程名称课程名称</div>
                  </template>
                </el-table-column>
                <el-table-column label="所属课程包名称" min-width="200">
                  <template>所属课程包名称课程包名称</template>
                </el-table-column>
                <el-table-column label="课程学时数" width="120" align="center">
                  <template>10</template>
                </el-table-column>
              </el-table>
              <p class="f-pb15 f-fb">课程展示方式：有分类</p>
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-small bg-gray is-border-bottom">
                  <span class="tit-txt">公共课</span>
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                    <div slot="content">
                      按照分类展示课程，可指定必学课程和学习要求学时，支持设置多个子分类。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置。
                    </div>
                  </el-tooltip>
                  <span class="f-fb f-ml20 f-flex-sub f-tr">
                    一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时
                  </span>
                </div>
                <div class="f-flex f-align-center f-plr20 f-pt20 f-fb">
                  <el-button type="primary" plain class="f-mr20">查看必学课程</el-button>
                  <div class="f-flex-sub">公共课要求完成 <i class="f-cr">10</i> 学时</div>
                </div>
                <el-divider class="m-divider no-mb"></el-divider>
                <el-row class="is-height">
                  <el-col :sm="8" :xl="6" class="is-border-right">
                    <div class="f-p20">
                      <el-tree :data="data" node-key="id" :expand-on-click-node="false" class="m-course-tree">
                        <div class="custom-tree-node">
                          <div class="tit">分类名称</div>
                        </div>
                      </el-tree>
                    </div>
                  </el-col>
                  <el-col :sm="16" :xl="18">
                    <div class="f-p20">
                      <div class="f-flex f-align-center">
                        <div class="m-tit is-mini">
                          <span class="tit-txt">分类名称</span>
                        </div>
                        <div class="f-fb f-flex-sub">
                          （一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时，必学课程
                          <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时）
                        </div>
                      </div>
                      <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                        <el-table-column type="index" label="No." width="60" align="center" fixed="left">
                        </el-table-column>
                        <el-table-column label="课程名称" min-width="240" fixed="left">
                          <template>课程名称课程名称课程名称课程名称</template>
                        </el-table-column>
                        <el-table-column label="分类信息" min-width="200">
                          <template>一级分类>二级分类>三级分类</template>
                        </el-table-column>
                        <el-table-column label="所属课程包名称" min-width="200">
                          <template>所属课程包名称课程包名称</template>
                        </el-table-column>
                        <el-table-column label="课程学时数" width="120" align="center">
                          <template>10</template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-small bg-gray is-border-bottom">
                  <span class="tit-txt">课程测验</span>
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                    <div slot="content">
                      课程测验的组卷范围为已选择课程关联的试题。请先添加课程后，再添加测验。课程测验可选配。
                    </div>
                  </el-tooltip>
                </div>
                <div class="f-p20">
                  <el-table stripe :data="tableData" max-height="500px" class="m-table">
                    <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                    <el-table-column label="总分/及格分" min-width="140" align="center" fixed="left">
                      <template>100 / 60</template>
                    </el-table-column>
                    <el-table-column label="组卷方式" min-width="120" align="center">
                      <template>智能组卷</template>
                    </el-table-column>
                    <el-table-column label="测验题数" min-width="120" align="center">
                      <template>100</template>
                    </el-table-column>
                    <el-table-column label="作答次数" min-width="120" align="center">
                      <template>不限次</template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-card>
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-small bg-gray is-border-bottom">
                  <span class="tit-txt">学习要求</span>
                </div>
                <div class="f-p20">
                  <el-row type="flex" justify="center" class="width-limit">
                    <el-col :md="20" :lg="16" :xl="13">
                      <el-form ref="form" :model="form" label-width="150px" class="m-text-form is-column f-mt10">
                        <el-form-item label="选修课选修要求：">10 学时</el-form-item>
                        <el-form-item label="课程测验纳入考核：">是</el-form-item>
                        <el-form-item label="开放课程评价：">是</el-form-item>
                        <el-form-item label="评价条件：">每门课程学习进度达 30% 可以进行课程评价</el-form-item>
                        <el-form-item label="是否强制学员评价：">是</el-form-item>
                        <el-form-item label="考核要求：">
                          <p>1. 必修 <i class="f-cr">0</i> 学时， 选修 <i class="f-cr">0</i> 学时，学习进度 100%</p>
                          <p>
                            2. 课程测验纳入考核，每门课程学习进度达
                            <i class="f-cr">30%</i>
                            可参加，测验及格分
                            <i class="f-cr">60</i>
                            分，次数不限次。
                          </p>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-card>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="second">
          <div slot="label">
            <i class="f-pl5">班级练习</i>
          </div>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-plr20 f-pt20">
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="场次名称" min-width="240" fixed="left">
                  <template>场次名称场次名称场次名称</template>
                </el-table-column>
                <el-table-column label="试卷名称" min-width="240">
                  <template>试卷名称试卷名称试卷名称</template>
                </el-table-column>
                <el-table-column label="组卷方式" min-width="120">
                  <template>智能组卷</template>
                </el-table-column>
                <el-table-column label="考试时长 / 总分 / 及格分" min-width="190" align="center">
                  <template>60 / 100 / 60</template>
                </el-table-column>
                <el-table-column label="作答次数" min-width="120" align="center">
                  <template>不限次</template>
                </el-table-column>
              </el-table>
              <el-form ref="form" :model="form" label-width="auto" class="m-text-form is-column f-mt30">
                <el-form-item label="前置条件：">完成课程学习考核</el-form-item>
                <el-form-item label="培训要求：">以班级考核配置为准</el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="third">
          <div slot="label">
            <i class="f-pl5">班级考试</i>
          </div>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-plr20 f-pt20">
              <div class="f-pt5 f-mb20"><span class="f-mr10">请选择练习来源：</span>题库</div>
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="题库名称" min-width="240" fixed="left">
                  <template>场次名称场次名称场次名称</template>
                </el-table-column>
                <el-table-column label="已启用的试题数量" min-width="190" align="center">
                  <template>60 / 100 / 60</template>
                </el-table-column>
              </el-table>
              <el-form ref="form" :model="form" label-width="auto" class="m-text-form is-column f-mt30">
                <el-form-item label="前置条件：">无</el-form-item>
                <el-form-item label="培训要求：">无</el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="fourth">
          <div slot="label">
            <i class="f-pl5">兴趣课程</i>
          </div>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-plr20 f-pt20">
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-small bg-gray is-border-bottom">
                  <span class="tit-txt">兴趣课程</span>
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                    <div slot="content">
                      本分类的课程为必修课程，报名成功后直接推送给学员，无需选课。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置。
                    </div>
                  </el-tooltip>
                  <span class="f-fb f-ml20 f-flex-sub f-tr">
                    一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时
                  </span>
                </div>
                <el-row class="is-height">
                  <el-col :sm="8" :xl="6" class="is-border-right">
                    <div class="f-p20">
                      <el-tree :data="data" node-key="id" :expand-on-click-node="false" class="m-course-tree">
                        <div class="custom-tree-node">
                          <div class="tit">分类名称</div>
                        </div>
                      </el-tree>
                    </div>
                  </el-col>
                  <el-col :sm="16" :xl="18">
                    <div class="f-p20">
                      <div class="f-flex f-align-center">
                        <div class="m-tit is-mini">
                          <span class="tit-txt">分类名称</span>
                        </div>
                        <div class="f-fb f-flex-sub">（一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时）</div>
                      </div>
                      <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                        <el-table-column type="index" label="No." width="60" align="center" fixed="left">
                        </el-table-column>
                        <el-table-column label="课程名称" min-width="240" fixed="left">
                          <template>课程名称课程名称课程名称课程名称</template>
                        </el-table-column>
                        <el-table-column label="分类信息" min-width="200">
                          <template>一级分类>二级分类>三级分类</template>
                        </el-table-column>
                        <el-table-column label="所属课程包名称" min-width="200">
                          <template>所属课程包名称课程包名称</template>
                        </el-table-column>
                        <el-table-column label="课程学时数" width="120" align="center">
                          <template>10</template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="fifth">
          <div slot="label">
            <i class="f-pl5">学习心得</i>
          </div>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-plr20">
              <!--无分类-->
              <div class="f-mtb20">学习心得对外展示名称：--</div>
              <div class="f-mtb20">一共 <i class="f-cr">X</i> 个，必选 <i class="f-cr">Y</i> 个</div>
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="f-p20">
                  <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                    <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                    <el-table-column label="主题" min-width="240" fixed="left">
                      <template
                        ><el-tag type="primary" effect="dark" size="mini">必选</el-tag
                        >课程名称课程名称课程名称课程名称</template
                      >
                    </el-table-column>
                    <el-table-column label="参加时间" min-width="240" align="center">
                      <template>2023-10-23 00:00:00至2023-11-11 23:59:59</template>
                    </el-table-column>
                    <el-table-column label="学习心得类型" min-width="100" align="center">
                      <template>班级心得</template>
                    </el-table-column>
                    <el-table-column label="作答形式" min-width="100" align="center">
                      <template>提交附件</template>
                    </el-table-column>
                    <el-table-column label="审核方式" min-width="100" align="center">
                      <template>提交自动通过</template>
                    </el-table-column>
                    <el-table-column label="总分" min-width="60" align="center">
                      <template>60</template>
                    </el-table-column>
                    <el-table-column label="未通过作答次数" width="160" align="center">
                      <template>3次</template>
                    </el-table-column>
                  </el-table>
                  <!--分页-->
                  <el-pagination
                    background
                    class="f-mt15 f-tr"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage4"
                    :page-sizes="[100, 200, 300, 400]"
                    :page-size="100"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="400"
                  >
                  </el-pagination>
                </div>
              </el-card>

              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-small bg-gray is-border-bottom">
                  <span class="tit-txt">学习心得要求</span>
                </div>
                <div class="f-p20">
                  <el-row type="flex" justify="center" class="width-limit">
                    <el-col :md="20" :lg="16" :xl="13">
                      <el-form ref="form" :model="form" label-width="150px" class="m-form f-mt10">
                        <el-form-item label="班级心得前置条件：" required>
                          可直接参加
                        </el-form-item>
                        <el-form-item label="课程心得前置条件：" required>
                          完成班级指定课程学习方可参加
                        </el-form-item>

                        <el-form-item label="整体要求：" required>
                          至少需要参加 <i class="f-cr">Z</i> 个学习心得
                        </el-form-item>

                        <el-form-item label="成绩要求：" required>
                          成绩 ≥ <i class="f-cr">Z</i> 分（总分：100分）视为通过。
                        </el-form-item>

                        <el-form-item label="是否纳入考核：" required>
                          是
                        </el-form-item>

                        <el-form-item label="考核要求：">
                          <p>1. 各项学习心得要求以具体配置为准</p>
                          <p>
                            2. 学习心得纳入考核，至少参加
                            <i class="f-cr">Z</i>
                            个心得，且每项心得均为通过。
                          </p>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-card>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="sixth">
          <div slot="label">
            <i class="f-pl5">培训期别</i>
          </div>
          <el-card shadow="never" class="m-card f-mb15">
            <el-row :gutter="20" class="m-farewell">
              <el-col :sm="8" :md="6">
                <div class="item">
                  <div class="item-tit">
                    <div class="tit">
                      <span class="txt" title="xx">这里显示期别名称</span><el-tag type="warning">未开始</el-tag>
                    </div>
                    <div class="num">01</div>
                  </div>
                  <div class="item-hd">
                    <div class="f-flex f-justify-between">
                      <div class="ele">
                        <div class="ele-t">培训时段</div>
                        <div class="ele-info f-to">204-10-15至204-10-19</div>
                      </div>
                      <div class="ele">
                        <div class="ele-t">开放报名人数</div>
                        <div class="ele-info f-to">15人</div>
                      </div>
                    </div>
                    <div class="ele">
                      <div class="ele-t">开放报名时间</div>
                      <div class="ele-info f-to">2024-10-11 10:00:00 至 2024-10-21 11:00:00</div>
                    </div>
                    <div class="ele">
                      <div class="ele-t">培训地点</div>
                      <div class="ele-info f-to-two">
                        安徽水利和顺大酒店（合肥市肥东经济开发区龙脊山路1号，长江东路与龙脊山路交口）
                      </div>
                    </div>
                  </div>
                  <div class="item-bd">
                    <div class="bd-l">
                      <el-tooltip effect="dark" placement="top" content="复制">
                        <span class="btn-icon el-icon-document-copy"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="编辑期别">
                        <span class="btn-icon el-icon-edit-outline"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="添加课程">
                        <span class="btn-icon p-icon icon-2"></span>
                      </el-tooltip>
                    </div>
                    <div class="bd-r">
                      <el-tooltip effect="dark" placement="top" content="删除">
                        <span class="btn-icon el-icon-delete"></span>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :sm="8" :md="6">
                <div class="item">
                  <div class="item-tit">
                    <div class="tit">
                      <span class="txt" title="xx">这里显示期别名称</span><el-tag type="success">进行中</el-tag>
                    </div>
                    <div class="num">02</div>
                  </div>
                  <div class="item-hd">
                    <div class="f-flex f-justify-between">
                      <div class="ele">
                        <div class="ele-t">培训时段</div>
                        <div class="ele-info f-to">204-10-15至204-10-19</div>
                      </div>
                      <div class="ele">
                        <div class="ele-t">开放报名人数</div>
                        <div class="ele-info f-to">15人</div>
                      </div>
                    </div>
                    <div class="ele">
                      <div class="ele-t">开放报名时间</div>
                      <div class="ele-info f-to">2024-10-11 10:00:00 至 2024-10-21 11:00:00</div>
                    </div>
                    <div class="ele">
                      <div class="ele-t">培训地点</div>
                      <div class="ele-info f-to-two">
                        安徽水利和顺大酒店（合肥市肥东经济开发区龙脊山路1号，长江东路与龙脊山路交口）
                      </div>
                    </div>
                  </div>
                  <div class="item-bd">
                    <div class="bd-l">
                      <el-tooltip effect="dark" placement="top" content="复制">
                        <span class="btn-icon el-icon-document-copy"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="编辑期别">
                        <span class="btn-icon el-icon-edit-outline"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="添加课程">
                        <span class="btn-icon p-icon icon-2"></span>
                      </el-tooltip>
                    </div>
                    <div class="bd-r">
                      <el-tooltip effect="dark" placement="top" content="删除">
                        <span class="btn-icon el-icon-delete"></span>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :sm="8" :md="6">
                <div class="item">
                  <div class="item-tit">
                    <div class="tit">
                      <span class="txt" title="xx"
                        >这里显示期别名称这里显示期别名称这里显示期别名称这里显示期别名称</span
                      ><el-tag type="info">已结束</el-tag>
                    </div>
                    <div class="num">03</div>
                  </div>
                  <div class="item-hd">
                    <div class="f-flex f-justify-between">
                      <div class="ele">
                        <div class="ele-t">培训时段</div>
                        <div class="ele-info f-to">204-10-15至204-10-19</div>
                      </div>
                      <div class="ele">
                        <div class="ele-t">开放报名人数</div>
                        <div class="ele-info f-to">15人</div>
                      </div>
                    </div>
                    <div class="ele">
                      <div class="ele-t">开放报名时间</div>
                      <div class="ele-info f-to">2024-10-11 10:00:00 至 2024-10-21 11:00:00</div>
                    </div>
                    <div class="ele">
                      <div class="ele-t">培训地点</div>
                      <div class="ele-info f-to-two">
                        安徽水利和顺大酒店（合肥市肥东经济开发区龙脊山路1号，长江东路与龙脊山路交口）
                      </div>
                    </div>
                  </div>
                  <div class="item-bd">
                    <div class="bd-l">
                      <el-tooltip effect="dark" placement="top" content="复制">
                        <span class="btn-icon el-icon-document-copy"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="编辑期别">
                        <span class="btn-icon el-icon-edit-outline"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="添加课程">
                        <span class="btn-icon p-icon icon-2"></span>
                      </el-tooltip>
                    </div>
                    <div class="bd-r">
                      <el-tooltip effect="dark" placement="top" content="删除">
                        <span class="btn-icon el-icon-delete"></span>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-card shadow="never" class="m-card is-header">
              <div class="m-tit is-small bg-gray is-border-bottom">
                <span class="tit-txt">学习要求</span>
              </div>
              <div class="f-p20">
                <el-row type="flex" justify="center" class="width-limit">
                  <el-col :md="20" :lg="16" :xl="13">
                    <el-form ref="form" label-width="180px" class="m-form f-mt10">
                      <el-form-item label="是否开启报到：">开启</el-form-item>
                      <el-form-item label="报到配置生效范围：">指定期别生效</el-form-item>
                      <el-form-item label="是否开启住宿信息采集：">开启</el-form-item>
                      <el-form-item label="住宿信息采集配置生效范围：">全期别生效</el-form-item>
                      <el-form-item label="是否开启考勤：">开启</el-form-item>
                      <el-form-item label="考勤考核要求：">考勤率不低于85%</el-form-item>
                      <el-form-item label="是否开启结业测试：">开启</el-form-item>
                      <el-form-item label="结业测试配置生效范围：">指定期别生效</el-form-item>
                      <el-form-item label="学员须知：">无</el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="seventh">
          <div slot="label">
            <i class="f-pl5">调研问卷</i>
          </div>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-p20">
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="问卷名称" min-width="240" fixed="left">
                  <template>读取问卷名称</template>
                </el-table-column>
                <el-table-column label="问卷开放时间" min-width="240">
                  <template>2024-10-01 08:08:08至2024-10-01 08:08:08</template>
                </el-table-column>
                <el-table-column label="问卷结果" min-width="120">
                  <template>开放</template>
                </el-table-column>
                <el-table-column label="问卷状态" min-width="120" align="center">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-badge is-dot type="success" class="badge-status">启用</el-badge>
                    </div>
                    <div v-else>
                      <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="应用范围" min-width="120" align="center">
                  <template>培训方案</template>
                </el-table-column>
                <el-table-column label="考核要求" min-width="120" align="center">
                  <template>纳入考核</template>
                </el-table-column>
                <el-table-column label="操作" width="200" align="center" fixed="right">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-button type="text">预览</el-button>
                      <el-button type="text">查看统计报告</el-button>
                    </div>
                    <div v-else>
                      <el-button type="text">预览</el-button>
                      <el-button type="text">二维码</el-button>
                      <el-button type="text">查看统计报告</el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <!--分页-->
              <el-pagination
                background
                class="f-mt15 f-tr"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
            <div class="f-p20">
              <el-card shadow="never" class="m-card is-header">
                <div class="m-tit is-small bg-gray is-border-bottom">
                  <span class="tit-txt">调研问卷要求</span>
                </div>
                <div class="f-p20">
                  <el-row type="flex" justify="center" class="width-limit">
                    <el-col :md="20" :lg="16" :xl="13">
                      <el-form ref="form" label-width="180px" class="m-form f-mt10">
                        <el-form-item label="是否纳入考核：">否</el-form-item>
                        <el-form-item label="是否强制调研：">是</el-form-item>
                        <el-form-item label="触发问卷环节：">打印证明前</el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-card>
            </div>
          </el-card>
        </el-tab-pane>
      </el-tabs>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">培训要求</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="150px" class="m-form f-mt10">
              <el-form-item label="考核规则：">
                <p>1. 课程学习要求不低于 <i class="f-cr">0</i> 学时。</p>
                <div class="f-ml15">
                  <p>① 每门课程学习进度=100%</p>
                  <p>
                    ② 课程测验纳入考核，每门课程学习进度达
                    <i class="f-cr">30%</i>
                    可参加，测验及格分 ≥
                    <i class="f-cr">60</i>
                    分，次数不限次。
                  </p>
                </div>
                <p class="f-mt10">
                  2. 班级考试考核成绩 ≥
                  <el-input v-model="form.name" size="small" class="input-num f-mlr5" />
                  分（总分100分，考试次数：<i class="f-cr">60</i> 次）
                </p>
                <p>3. 学习心得纳入考核</p>
                <div class="f-ml15">
                  <p>① 各项学习心得要求以具体配置为准</p>
                  <p>②学习心得纳入考核，至少参加 <i class="f-cr">Z</i> 个心得，且每项心得均为通过。</p>
                </div>
                <p>
                  4. 调研问卷纳入考核，按具体问卷要求提交。<el-button type="text" class="f-ml10">[查看详情]</el-button>
                </p>
                <p>
                  5. 培训期别：至少完成一个期别并考核通过。<el-button type="text" class="f-ml10">[查看详情]</el-button>
                </p>
              </el-form-item>
              <el-form-item label="获得学时：">考核通过后将获得100学时</el-form-item>
              <el-form-item label="培训成果：">提供培训证明，达到培训要求后可打印</el-form-item>
              <el-form-item label="证明模板配置：">按期别指定模板</el-form-item>
              <el-form-item label="培训证明模板：">证明模板名称</el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">学习有效期配置</span>
        </div>
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">学习时间配置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="150px" class="m-form f-mt20">
              <el-form-item label="学习起止时间：">指定培训时间 </el-form-item>
              <!--选择 指定培训时间 后出现-->
              <el-form-item label="选择时间："
                >xxxx-xx-xx
                <div class="f-ci f-mt5">
                  注：此项为网授部分学习起止时间，面授部分学习时间以不同期别配置的培训时段为准
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">销售配置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="180px" class="m-text-form is-column f-mt20">
              <el-form-item label="展示在门户：">
                展示
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-info m-tooltip-icon f-c9"></i>
                  <div slot="content">
                    配置培训方案是否展示在网校报名培训列表。展示门户分为学员门户报名列表和集体报名管理员查看培训班，设置为集体报名管理员可见，只生效于有开启线上集体报名的网校。<br />
                    不展示门户的情况下，如该方案有纳入专题，则会显示在专题门户上。默认为专题学员门户报名列表和集体报名管理员查看专题内培训班。集体报名管理员仅生效有开启线上集体报名的网校。
                  </div>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="展示用户：">学员门户可见、集体报名管理员可见</el-form-item>
              <el-form-item label="开放学员报名：">开放</el-form-item>
              <el-form-item label="方案上架状态：">立即上架</el-form-item>
              <el-form-item label="方案计划上架时间：">-</el-form-item>
              <el-form-item label="方案计划下架时间：">-</el-form-item>
              <el-form-item label="配置开启报名时间：">2025-01-01 10:00:00</el-form-item>
              <el-form-item label="关闭报名时间：">无关闭时间</el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">合并报名配置</span>
        </div>
        <el-row type="flex" justify="center">
          <el-col :span="23">
            <el-form ref="form" :model="form" label-width="265px" class="m-form f-mt20">
              <el-form-item label="合并本方案报名的主方案：">
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="方案名称" min-width="240" fixed="left">
                    <template>
                      <div>课程名称课程名称课程名称课程名称</div>
                      <div>
                        <el-tag type="danger" effect="dark" size="mini">已下架</el-tag>
                        <el-tag type="danger" effect="dark" size="mini">不开放学员报名</el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="培训属性" min-width="220">
                    <template>
                      <p>某某行业</p>
                      <p>属性1：属性值</p>
                      <p>属性2：属性值</p>
                      <p>属性3：属性值</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="报名学时" min-width="100">
                    <template>50</template>
                  </el-table-column>
                  <el-table-column label="价格" min-width="100">
                    <template>50.00</template>
                  </el-table-column>
                  <el-table-column label="学习起止时间" min-width="120">
                    <template>长期有效</template>
                  </el-table-column>
                  <el-table-column label="报名起止时间" min-width="200">
                    <template>
                      <p>起始：2021-10-15 00:21:21</p>
                      <p>结束：2021-10-15 00:21:21</p>
                    </template>
                  </el-table-column>
                </el-table>
                <!--通用空数据-->
                <div class="m-no-date">
                  <img class="img" src="./assets/images/no-data-normal.png" alt="" />
                  <div class="date-bd">
                    <p class="f-f15 f-c9">暂无数据~</p>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="报名本方案时需合并报名以下方案：">
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="方案名称" min-width="240" fixed="left">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <div>课程名称课程名称课程名称课程名称</div>
                        <div>
                          <el-tag type="danger" effect="dark" size="mini">已下架</el-tag>
                        </div>
                      </div>
                      <div v-else>
                        <div>课程名称课程名称课程名称课程名称</div>
                        <div>
                          <el-tag type="danger" effect="dark" size="mini">不开放学员报名</el-tag>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="培训属性" min-width="220">
                    <template>
                      <p>某某行业</p>
                      <p>属性1：属性值</p>
                      <p>属性2：属性值</p>
                      <p>属性3：属性值</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="报名学时" min-width="100">
                    <template>50</template>
                  </el-table-column>
                  <el-table-column label="价格" min-width="100">
                    <template>50.00</template>
                  </el-table-column>
                  <el-table-column label="学习起止时间" min-width="120">
                    <template>长期有效</template>
                  </el-table-column>
                  <el-table-column label="报名起止时间" min-width="200">
                    <template>
                      <p>起始：2021-10-15 00:21:21</p>
                      <p>结束：2021-10-15 00:21:21</p>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">报名费用</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="150px" class="m-form f-mt20">
              <el-form-item label="培训费：">100元 / 人</el-form-item>
              <el-form-item label="选择收款主体：">机构收款</el-form-item>
              <el-form-item label="收款机构：">
                <div>1.第一期：xxx</div>
                <div class="f-mt5">2.第二期：xxx</div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      const data = [
        {
          id: 1,
          label: '一级 1',
          children: [
            {
              id: 4,
              label: '二级 1-1'
            },
            {
              id: 4,
              label: '二级 1-1'
            },
            {
              id: 4,
              label: '二级 1-1'
            },
            {
              id: 4,
              label: '二级 1-1'
            }
          ]
        }
      ]
      return {
        data,
        num: 1,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        input1: '1',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        tableData1: [
          {
            id: 1,
            field01: '这里是分类名称',
            hasChildren: true
          },
          {
            id: 2,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 3,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 4,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 5,
            field01: '分类名称1',
            hasChildren: true
          }
        ],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      },
      load(tree, treeNode, resolve) {
        setTimeout(() => {
          resolve([
            {
              id: 11,
              field01: '分类名称1-1'
            },
            {
              id: 12,
              field01: '分类名称1-2'
            }
          ])
        }, 1000)
      }
    }
  }
</script>
