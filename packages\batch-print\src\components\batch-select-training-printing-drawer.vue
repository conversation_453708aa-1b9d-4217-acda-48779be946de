<template>
  <div>
    <el-drawer
      title="选择培训证明进行打印"
      :visible.sync="visible"
      size="700px"
      custom-class="m-drawer"
      :append-to-body="true"
      destroy-on-close
      @close="cancel"
    >
      <div class="drawer-bd">
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form ref="form" :model="printCertificationsVo" label-width="auto" class="m-form f-mt50">
              <!-- 模版类型 -->
              <el-form-item label="模板类型：" required>
                <el-radio-group v-model="printCertificationsVo.printSource" @change="printSourceChange">
                  <el-radio :label="1">班级配置模板</el-radio>
                  <el-radio :label="2">其他模板</el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 培训证明模版 -->
              <div v-if="printCertificationsVo.printSource === 2">
                <el-form-item label="培训证明模板：" required v-if="!templateInfo.id">
                  <el-button type="primary" plain @click="chooseTemplate">选择模板</el-button>
                </el-form-item>
                <el-form-item label="培训证明模板：" required v-if="templateInfo.id">
                  <i class="f-mr20">{{ templateInfo.name }}</i>
                  <el-button type="text" @click="chooseTemplate">替换模板</el-button>
                </el-form-item>
              </div>

              <!-- 文件类型 -->
              <el-form-item>
                <div slot="label">
                  <span class="f-cr f-mr5">*</span>
                  <span class="f-vm">文件类型</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>支持配置批量打印的类型：</p>
                      <p>1.连贯打印：本次批量打印的所有合格记录，是一份PDF，命名方式同导出文件名。</p>
                      <p>2.单个文件：每个学员对应的一条合格记录，是一份PDF，且以学员的姓名、证件号命名。</p>
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                <el-radio-group v-model="printCertificationsVo.printType" @change="changePrintType">
                  <el-radio :label="1">连贯打印</el-radio>
                  <el-radio :label="2">单个文件</el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 是否合并打印 -->
              <el-form-item v-if="printCertificationsVo.printType === 1 && printCertificationsVo.printSource !== 1">
                <div slot="label">
                  <span class="f-cr f-mr5">*</span>
                  <span class="f-vm">是否合并打印</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>
                        连贯打印模式下，如打印的证明尺寸为四分之一的A4纸张或二分之一的A4纸，
                        如需合并在一张A4纸上进行打印，是否合并打印请选择“是”；
                        若模板类型为“班级配置模板”时则不支持合并打印。
                      </p>
                      <p class="f-mt5">
                        <i class="f-co">注意：</i>如本次需要打印的证明尺寸超过四分之一的A4纸张或
                        二分之一的A4纸，无法选择合并打印。
                      </p>
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                <el-radio-group v-model="printCertificationsVo.isMerge">
                  <el-radio label="1" :disabled="!isMerge">是</el-radio>
                  <el-radio label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="cancel">取消</el-button>
                <el-button type="primary" @click="confirm" :loading="sureBtnLoading">确定</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>

    <!-- 选择培训证明模版组件 -->
    <student-batch-print-proof ref="studentPrintRef" @getTemplateInfo="getTemplateInfo"></student-batch-print-proof>

    <el-dialog :visible.sync="errorDialog" width="400px" class="m-dialog">
      <div class="dialog-alert is-big">
        <div class="txt">
          <p class="f-f13 f-mt5">{{ errormsg }}</p>
        </div>
      </div>
      <div slot="footer">
        <el-button type="info" @click="errorDialog = false">确定</el-button>
      </div>
    </el-dialog>

    <!-- 导出成功弹窗 -->
    <el-dialog :visible.sync="exportDialog" width="400px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务管理-批量打印证明</p>
        </div>
      </div>
      <div slot="footer">
        <el-button type="info" @click="exportDialog = false">暂 不</el-button>
        <el-button type="primary" @click="toDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { FileTypesEnum } from '@api/service/common/enums/personal-leaning/FileTypes'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import { ImportStatusEnum } from '@api/service/management/personal-leaning/import-print/enums/ImportStatus'
  import ImportPrintStudent from '@api/service/management/personal-leaning/import-print/ImportPrintStudent'
  import QueryStudentParams from '@api/service/management/personal-leaning/import-print/model/QueryStudentParams'
  import { ServiceTypeEnum } from '@api/service/management/personal-leaning/mutation/enums/ServiceType'
  import MutationBatchPrintTraining from '@api/service/management/personal-leaning/mutation/MutationBatchPrintTraining'
  import PrintCertificationsVo from '@api/service/management/personal-leaning/mutation/vo/PrintCertificationsVo'
  import QueryCertificateTemplateIsMerge from '@api/service/management/personal-leaning/query/QueryCertificateTemplateIsMerge'
  import CertificateTemplateResponseVo from '@api/service/management/personal-leaning/query/vo/CertificateTemplateResponseVo'
  import LearningArcjovesRequest from '@api/service/management/personal-leaning/query/vo/LearningArcjovesRequest'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import StudentBatchPrintProof from '@hbfe/jxjy-admin-batchPrint/src/student-batch-print-proof.vue'
  import { bind, debounce } from 'lodash-decorators'
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'

  @Component({
    components: {
      StudentBatchPrintProof
    }
  })
  export default class extends Vue {
    @Ref('studentPrintRef')
    studentPrintRef: StudentBatchPrintProof

    @Prop({
      type: Object,
      default: () => {
        return new LearningArcjovesRequest()
      }
    })
    certifyParam: LearningArcjovesRequest

    @Prop({
      type: Object,
      default: () => {
        return new QueryStudentParams()
      }
    })
    queryStudentParams: QueryStudentParams

    /**
     * 是否显示抽屉控制
     */
    visible = false

    /**
     * 导出成功弹窗
     */
    exportDialog = false

    // 错误提示
    errorDialog = false
    // 错误信息
    errormsg = ''

    //模版信息
    templateInfo = new CertificateTemplateResponseVo()
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)

    // 按钮加载状态
    sureBtnLoading = false

    // 批量打印证明实例
    batchPrintTrainingModule = new MutationBatchPrintTraining()

    // 数据
    printCertificationsVo = new PrintCertificationsVo()

    importPrintStudent = new ImportPrintStudent()

    // 查询模版是否能合并
    queryCertificateTemplateIsMerge = new QueryCertificateTemplateIsMerge()

    source = ''

    /**
     * @description 选中模版是否支持合并
     * true:支持合并 fasle:不支持合并
     * */
    isMerge = false

    loading = false

    async open(source: string) {
      this.visible = true
      // 来源
      this.source = source
      // 初始化选项
      this.printCertificationsVo.isMerge = ''
      this.printCertificationsVo.printSource = 1
      this.printCertificationsVo.printType = 1
      this.isMerge = false
      this.sureBtnLoading = false
    }

    /**
     * @description 查询模版是否能合并
     * */
    async queryMerge(templateId: string) {
      this.isMerge = await this.queryCertificateTemplateIsMerge.findCertificateTemplate(templateId)
      this.changePrintType()
    }

    // 确定按钮
    @bind
    @debounce(200)
    async confirm() {
      console.log('按学员打印传过来的值this.certifyParam*****', this.certifyParam)
      console.log(this.source, 'batchPrintingStudentsSource')
      //  来源于学员批量打印证明
      if (this.source == 'batchPrintingStudentsSource') {
        if (this.isFxlogin) {
          this.printCertificationsVo.serviceType = ServiceTypeEnum.distributor
        } else if (this.isZtlogin) {
          this.printCertificationsVo.serviceType = ServiceTypeEnum.specialManager
        } else {
          this.printCertificationsVo.serviceType = ServiceTypeEnum.school
        }
        //   按学员打印-批量打印证明
        if (this.printCertificationsVo.printSource === 1) {
          //如果没有选择必填项校验
          if (!this.printCertificationsVo.printType) {
            return this.$message.error('请选择文件类型')
          }

          // 接口参数
          this.printCertificationsVo.templateId = ''
          this.printCertificationsVo.fileType = FileTypesEnum.PDF

          // 调用接口
          try {
            this.sureBtnLoading = true
            const res = await this.batchPrintTrainingModule.doBatchPrintStudentCertificates(
              this.printCertificationsVo,
              this.certifyParam
            )

            if (res.status.code === 200) {
              this.visible = false
              this.exportDialog = true
            }
          } catch (e) {
            console.log(e)
          } finally {
            this.sureBtnLoading = false
          }
        } else {
          if (!this.templateInfo.id) {
            return this.$message.error('请选择培训证明模版')
          }
          if (this.printCertificationsVo.printType === 1 && !this.printCertificationsVo.isMerge) {
            return this.$message.error('请选择是否合并打印')
          }
          // 接口参数
          this.printCertificationsVo.templateId = this.templateInfo.id
          this.printCertificationsVo.fileType = FileTypesEnum.PDF

          // 调用接口
          try {
            this.sureBtnLoading = true
            const res = await this.batchPrintTrainingModule.doBatchPrintStudentCertificates(
              this.printCertificationsVo,
              this.certifyParam
            )
            if (res.status.code === 200) {
              this.visible = false
              this.$emit('cancelDownload')
              this.exportDialog = true
            } else {
              this.errorDialog = true
              this.errormsg = res.status.message[0].toString()
            }
          } catch (e) {
            console.log(e)
          } finally {
            this.sureBtnLoading = false
          }
        }
      }

      // 来源导入学员名单
      if (this.source == 'importListPrint') {
        if (this.isFxlogin) {
          this.queryStudentParams.serviceType = ServiceTypeEnum.distributor
        } else if (this.isZtlogin) {
          this.printCertificationsVo.serviceType = ServiceTypeEnum.specialManager
        } else {
          this.queryStudentParams.serviceType = ServiceTypeEnum.school
        }
        // 如果选择模版类型为班级配置模版
        if (this.printCertificationsVo.printSource === 1) {
          //如果没有选择必填项校验
          if (!this.printCertificationsVo.printType) {
            return this.$message.error('请选择文件类型')
          }
          // 接口参数
          this.printCertificationsVo.templateId = ''
          this.printCertificationsVo.fileType = FileTypesEnum.PDF
          this.queryStudentParams.type = ImportStatusEnum.success
          // 调用接口
          try {
            this.sureBtnLoading = true
            const res = await this.importPrintStudent.batchPrintStudentCertificates(
              this.queryStudentParams,
              this.printCertificationsVo
            )
            if (res.isSuccess()) {
              this.visible = false
              this.$emit('cancelDownload')
              this.exportDialog = true
            } else if (res.errors[0].code === 50002) {
              this.errorDialog = true
              this.errormsg = '单次批量打印证明上限500条，如超过将无法正常执行打印任务，请分批次打印。'
            } else {
              this.errorDialog = true
              this.errormsg = res.errors[0].message
            }
          } catch (e) {
            console.log(e)
          } finally {
            this.sureBtnLoading = false
          }
        } else {
          console.log('选择点击了其他模版')
          if (!this.templateInfo.id) {
            return this.$message.error('请选择培训证明模版')
          }
          // 接口参数
          this.printCertificationsVo.templateId = this.templateInfo.id
          this.printCertificationsVo.fileType = FileTypesEnum.PDF
          this.queryStudentParams.type = ImportStatusEnum.success
          // 调用接口
          try {
            this.sureBtnLoading = true
            const res = await this.importPrintStudent.batchPrintStudentCertificates(
              this.queryStudentParams,
              this.printCertificationsVo
            )
            if (res.isSuccess()) {
              this.visible = false
              this.exportDialog = true
            } else if (res.errors[0].code === 50002) {
              this.errorDialog = true
              this.errormsg = '单次批量打印证明上限500条，如超过将无法正常执行打印任务，请分批次打印。'
            } else {
              this.errorDialog = true
              this.errormsg = res.errors[0].message
            }
          } catch (e) {
            console.log(e)
          } finally {
            this.sureBtnLoading = false
          }
        }
      }
    }

    getTemplateInfo(value: CertificateTemplateResponseVo) {
      this.templateInfo = value
      this.queryMerge(this.templateInfo.id)
    }

    /**
     * @description 模版类型变更
     * */
    printSourceChange() {
      // 是否为班级配置模版
      const isClassTemp = this.printCertificationsVo.printSource == 1
      if (isClassTemp) {
        // 班级配置模版 清空合并打印选项
        this.printCertificationsVo.isMerge = ''
      }
      this.changePrintType()
    }

    /**
     * @description 文件类型变更
     * */
    changePrintType() {
      // 是否为班级配置模版
      const isClassTemp = this.printCertificationsVo.printSource == 1
      // 是否选择单个文件打印
      const isSinglePrint = this.printCertificationsVo.printType == 2
      // 班级配置模版不做处理
      if (isClassTemp) return
      //  其他模版根据选中文件类型判断能否默认选中合并
      this.printCertificationsVo.isMerge = isSinglePrint ? '' : this.isMerge ? '1' : '0'
    }

    // 取消按钮
    cancel() {
      //   选择的清空
      this.templateInfo = new CertificateTemplateResponseVo()
      this.printCertificationsVo.printSource = 1
      this.printCertificationsVo.isMerge = ''
      this.printCertificationsVo.printType = 0
      this.sureBtnLoading = false
      //   抽屉隐藏
      this.visible = false
    }

    // 选择模版按钮
    chooseTemplate() {
      // 打开选择证明模版的弹框
      this.studentPrintRef.showTemplateListDrawer(this.templateInfo.name)
    }

    // 前往导出任务查看页面
    toDownloadPage() {
      this.exportDialog = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: {
          type: 'exportCertificatePrintFile'
        }
      })
    }
  }
</script>

<style></style>
