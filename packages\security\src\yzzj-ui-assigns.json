[{"name": "WXGLY", "meta": {"title": "网校管理员", "isMenu": true, "ownerGroup": ["WXGLY"], "roles": ["FXS", "GYS", "NZGYS", "NZFXS", "NZFXSJCB", "NZGYSJCB", "ZTGLY", "JTJFGLY", "WXGLY", "DQGLY"], "permissionMap": {"query": {"name": "查询(必选)", "ownerGroup": ["WXGLY.query"], "graphql": ["ms-config-v1.query.getCurrentFrontendConfig:{\"serviceName\":\"ms-config-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForServiceByDomainName:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.query.getDistributionService:{\"authorizationRequired\":false}", "ms-identity-authentication-v1.query.getAccessTokenValue:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getAdminInfoInMyself:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjy-distributor-admin-v1.query.isOnlineSchoolContractExpired:{\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.isOnlineSchoolContractExpired:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.query.findFunctionalAuthorityByRoleIdsNew:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.query.findRoleByAccountId:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-autolearning-online-school-smart-learning-service-v1.query.queryOnlineSchoolSmartLearningServiceConfigByServicerId:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applyCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptchaForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCodeForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwd:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptcha:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountId:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCode:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-account-v1.mutation.changeAuthorizationUnitInfoList:{\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applySmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validSmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentUser:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.validCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getOnlineSchoolConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getSmsCodeAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getFailedAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.forgetPassword:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCoursePreview:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "platform-training-channel-v1.mutation.updateSaleSetting:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursePackageInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseInSchemeInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageCoursewareSupplierInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyChangeIdentityAuthentication:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticate:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyProxyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticateBasic:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryPayFlowStatus:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-payment-v1.mutation.getBatchPreParePayResult:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryQrScanPromptByPayFlowNo:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.deliveredOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":true}", "ms-assess-v1.mutation.batchRePushSchemeIndicator:{\"serviceName\":\"ms-assess-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.createJxjyCommonMenu:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.temporaryOrderUpdateOrderInfoInSubject:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.enableOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForService:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.tryComparePhoto:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.updateUserDatumCodePhoto:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.refreshConfigAndUpdate:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.executeAntiInspection:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":false}", "platform-account-v1.mutation.compensatingStudentAccount:{\"serviceName\":\"platform-account-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.mutation.openDistributionService:{\"authorizationRequired\":false}", "ms-order-repair-data-v1.mutation.orderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-repair-data-v1.mutation.batchOrderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.distributionAuthBatchProduct:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.createPricePlan:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.pricePlanSetSaleChannel:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissReturnOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissExchangeOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.clearStudentLearningRule:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-teachingplan-studentattendance-sds-v1.mutation.oneClickClock:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":false}"], "roles": ["FXS", "GYS", "NZGYS", "NZFXS", "NZFXSJCB", "NZGYSJCB", "ZTGLY", "JTJFGLY", "WXGLY", "DQGLY"], "ext": {"diffSchool": ["yzzj"]}}}, "sort": "2", "diffSchool": ["yzzj"]}, "specifier": "WXGLY", "path": "", "pathSegments": ["WXGLY"], "children": [{"name": "statistic", "specifier": "Statistic", "path": "/statistic", "pathSegments": ["WXGLY", "statistic"], "component": "@/packages/routers/src/basic-router/statistic.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "统计报表", "sort": 5, "icon": "icon-shu<PERSON>", "roles": ["WXGLY"], "permissionMap": {}, "ownerGroup": ["WXGLY.statistic"], "diffSchool": ["yzzj"]}, "children": [{"name": "statistic-learning-statistic-index", "specifier": "StatisticLearningStatisticIndex", "path": "learning-statistic", "pathSegments": ["WXGLY", "statistic", "learning-statistic"], "component": "@/packages/routers/src/basic-router/statistic/learning-statistic/index.vue", "meta": {"isMenu": true, "title": "学员学习明细", "sort": 5, "icon": "icon-mingxi", "roles": ["WXGLY"], "permissionMap": {"exportData": {"name": "导出对接数据", "ownerGroup": ["WXGLY.statistic.learning-statistic.exportData"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "yzzj-data-export-gateway-backstage.query.exportStudentSchemeLearningIntegrationDataExcelInServicerManageRegion:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "yzzj-data-export-gateway-backstage.query.exportStudentSchemeLearningIntegrationDataExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}"], "roles": ["WXGLY"], "ext": {"diffSchool": ["yzzj"]}}}, "ownerGroup": ["WXGLY.statistic.learning-statistic"], "diffSchool": ["yzzj"]}}]}]}]