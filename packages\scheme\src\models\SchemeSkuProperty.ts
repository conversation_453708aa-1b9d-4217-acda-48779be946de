import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * sku绑定模型
 */
export default class SchemeSkuProperty {
  year: string
  region: string[]
  industry: string
  subjectType: string
  trainingCategory: string
  societyTrainingMajor: string[]
  constructionTrainingMajor: string
  technicalGrade: string
  trainingObject: string
  positionCategory: string
  jobLevel: string
  //学段Id
  studyPeriodId: string
  //学科id
  subjectId: string
  //药师id
  pharmacistIndustry: string[]

  trainingMode: TrainingModeEnum
}
