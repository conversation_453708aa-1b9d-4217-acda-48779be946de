import { IssueLogRequest, SkuPropertyRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

export default class QueryIssueChangeRecordParam {
  /**
   * 买家id（学员为学员userId）
   */
  buyerId = ''

  /**
   * 年度
   */
  year = ''

  /**
   * 方案名称
   */
  schemeName = ''

  /**
   * 期别名称
   */
  issueName = ''
  /**
   * 培训形式
   */
  trainingMode: TrainingModeEnum = null

  toRequest() {
    const request = new IssueLogRequest()
    request.issueName = this.issueName
    request.skuProperty = new SkuPropertyRequest()
    request.skuProperty.year = [this.year]
    request.skuProperty.trainingForm = [this.trainingMode]
    request.saleTitle = this.schemeName
    request.buyerId = this.buyerId
    return request
  }
}
