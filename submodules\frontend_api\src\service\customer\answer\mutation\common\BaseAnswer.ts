/**
 * 作答抽象类
 */
class BaseAnswer {
  /**
   * 试卷信息
   */
  answerPaperInfo: AnswerPaperInfo
  /**
   * 准考证信息
   */
  admissionToKen: string

  /**
   * @description: 获取试卷详情
   * @param {boolean} hasAnswer  是否有答案
   * @return {*}
   */

  async getAnswerPaper(hasAnswer: boolean): Promise<Response> {
    return
  }

  /**
   * @description: 进入业务场景校验
   * @param {*}
   * @return {*}
   */

  validateEnter(): Promise<Response> {
    return
  }

  /**
   * @description: 整卷试卷提交
   * @param {*}
   * @return {*}
   */

  async submitAnswerPaper(): Promise<Response> {
    return
  }
}
export default BaseAnswer
