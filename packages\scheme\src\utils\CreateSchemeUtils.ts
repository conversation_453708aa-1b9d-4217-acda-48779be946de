import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'
import Classification from '@api/service/management/train-class/mutation/vo/Classification'

/**
 * 课程分类大纲学时
 */
export class TopLevelOutlinePeriod {
  // 要求学时
  requirePeriod = 0
  // 必学课程总学时
  compulsoryCoursePeriodTotal = 0
  // 分类名称
  name = ''
}

/**
 * 课程重复详情
 */
export class CourseRepeatListDetail {
  // 课程名称
  courseName: string
  // 课程分类信息
  courseCategoryInfo: Array<string>
}

/**
 * 是否要求学时必填
 */
export class RequirePeriod {
  // 是否要求学时必填
  isRequirePeriod = false
}

/**
 * 课程列表信息
 */
export class SchemeCourseDetailInCoursePackage extends CourseInCoursePackage {
  // 所属课程包名称
  coursePackageName: string
  // 分类信息
  courseCategoryInfo: Array<string>
}

/**
 * 必学课程信息
 */
export class CompulsoryCourseInfo {
  /**
   * 课程列表长度
   */
  length = 0

  /**
   * 课程总学时
   */
  coursePeriodTotal = 0
  /**
   * 课程Map结构
   */
  courseMap: Map<string, string[]> = new Map()
}
/**
 * @description 培训方案操作
 */
export class CreateSchemeUtils {
  // 创建模式列表（课程包下课程读取最新的课程包）
  static createModeList = [1, 2]

  // 修改模式列表（课程包下课程读取大纲下的课程）
  static modifyModeList = [3]

  /**
   * 长期有效 - 开始时间
   */
  static defaultBeginDate = '1900-01-01 00:00:00'
  /**
   * 长期有效 - 结束时间
   */
  static defaultEndDate = '2100-01-01 00:00:00'

  /**
   * 查找节点
   * @param {Array<T>} tree - 数结构
   * @param {any} func - 条件表达式
   * @param {string} childKey - 子节点key值，默认：'children'
   * @return {T|null} 节点
   */
  static treeFind<T>(tree: Array<T>, func: any, childKey = 'children'): T | null {
    for (const item of tree) {
      if (func(item)) return item
      if (item[childKey] && item[childKey].length) {
        const res: T | null = this.treeFind(item[childKey], func, childKey)
        if (res) return res
      }
    }
    return null
  }

  /**
   * 查找节点路径
   * @param {T[]} tree - 数结构
   * @param {any} func - 条件表达式
   * @param {string} key - 单层节点路径，默认：'id'
   * @param {string} childKey - 子节点key值，默认：'children'
   * @return {string[]} 节点路径
   */
  static treeFindPath<T>(tree: T[], func: any, key = 'id', childKey = 'children'): string[] {
    if (!Array.isArray(tree) || tree.length === 0) return [] as string[]
    const path = [] as string[]
    const findPath = (tree: T[], key: string, path: string[]): string[] => {
      for (const item of tree) {
        path.push(item[key])
        if (func(item)) return path
        if (item[childKey] && item[childKey].length) {
          const findChildren = findPath(item[childKey], key, path)
          if (findChildren.length) {
            return findChildren as string[]
          }
        }
        path.pop()
      }
      return [] as string[]
    }
    return findPath(tree, key, path)
  }

  /**
   * 查找树上所有叶子节点
   * @param {Array<T>} tree - 数结构
   * @param {string} childKey - 子节点key值，默认：'children'
   * @return {T[]} 叶子节点列表
   */
  static treeFindAllLeaves<T>(tree: Array<T>, childKey = 'children'): T[] {
    const result = [] as T[]
    const getLeaves = (tree: Array<T>) => {
      tree.forEach((item: T) => {
        if (!item[childKey] || !item[childKey].length) {
          result.push(item)
        } else {
          getLeaves(item[childKey])
        }
      })
    }
    getLeaves(tree)
    return result
  }

  /**
   * 比较开始时间是否早于截止时间
   * @param {string} beginDateStr - 开始时间
   * @param {string} endDateStr - 结束时间
   * @return {boolean} 比较结果
   */
  static validTwoDateOrder(beginDateStr: string, endDateStr: string) {
    const beginDate = new Date(beginDateStr)
    const endDate = new Date(endDateStr)
    return endDate.getTime() >= beginDate.getTime()
  }

  /**
   * 校验时间有效性，如果是长期有效的开始或结束时间，则时间无效
   */
  static checkTimeValidity(time: string) {
    return time && time !== this.defaultBeginDate && time !== this.defaultBeginDate ? true : false
  }

  /**
   * 获取mfs上图片
   */
  static getMFSImage(imageUrl: string) {
    const mfsHeadReg = /^\/mfs\/\.*/
    return mfsHeadReg.test(imageUrl) ? imageUrl : `/mfs${imageUrl}`
  }

  /**
   * 校验数组内是否有内容（有质量）
   */
  static isWeightyArray<T>(params: Array<T>): boolean {
    return Array.isArray(params) && params.length ? true : false
  }

  /**
   * 获取必学课程相关信息
   */
  static getOutlineCompulsoryCourseInfo(item: Classification): CompulsoryCourseInfo {
    const compulsoryCourseInfo: CompulsoryCourseInfo = new CompulsoryCourseInfo()
    // 获取所有叶子节点
    const leaves = this.treeFindAllLeaves<Classification>([item], 'childOutlines')
    leaves?.forEach((el: Classification) => {
      const courseIds = el.compulsoryCourseIdList
      if (CreateSchemeUtils.isWeightyArray(el.compulsoryCourseInfoList)) {
        el.compulsoryCourseInfoList.forEach((subEl) => {
          const index = courseIds.indexOf(subEl.id)
          if (index > -1) {
            compulsoryCourseInfo.courseMap.set(subEl.id, subEl.courseCategoryInfo)
            compulsoryCourseInfo.coursePeriodTotal = CalculatorObj.add(
              compulsoryCourseInfo.coursePeriodTotal,
              subEl.period
            )
          }
        })
      }
    })
    compulsoryCourseInfo.length = compulsoryCourseInfo.courseMap.size || 0
    return compulsoryCourseInfo
  }

  /**
   * 获取必学课程学时
   */
  static getOutlineCompulsoryCoursePeriod(item: Classification): number {
    // 获取所有叶子节点
    const leaves = this.treeFindAllLeaves<Classification>([item], 'childOutlines')
    const period: number =
      leaves.reduce((prev, cur) => {
        return CalculatorObj.add(cur.compulsoryCoursePeriodTotal, prev)
      }, 0) || 0
    console.log(period, 'period')

    return period
  }
  /**
   * 获取必学课程课程数
   */
  static getOutlineCompulsoryCourseTotal(item: Classification): number {
    // 获取所有叶子节点
    const leaves = this.treeFindAllLeaves<Classification>([item], 'childOutlines')
    const total: number =
      leaves.reduce((prev, cur) => {
        return CalculatorObj.add(cur.compulsoryCourseIdList.length, prev)
      }, 0) || 0

    return total
  }
}
