import { BatchOrderPayModeEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderPayMode'
import { PaymentChannelEnum } from '@api/service/common/enums/trade-configuration/PaymentChannelType'
import BatchOrderRemittanceVoucherInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderRemittanceVoucherInfoVo'
import { OrderTerminalTypeEnum } from '@api/service/common/enums/order/OrderTerminalTypes'

/**
 * @description 【集体报名订单】订单详情-支付信息
 */
class BatchOrderDetailPaymentInfoVo {
  /**
   * 支付方式 1：线上支付 2：线下支付
   */
  payMode: BatchOrderPayModeEnum = null

  /**
   * 终端（销售渠道）、Web:Web端 H5:H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
   */
  terminalCode: OrderTerminalTypeEnum = null

  /**
   * 支付渠道名称 支付宝：支付宝
   */
  payChannelName = ''

  /**
   * 交易号
   */
  tradeNo = ''

  /**
   * 付款时间
   */
  applyPayDate = ''

  /**
   * 付款成功时间
   */
  paySuccessDate = ''

  /**
   * 汇款凭证信息
   */
  remittanceVoucherList: BatchOrderRemittanceVoucherInfoVo[] = []
}

export default BatchOrderDetailPaymentInfoVo
