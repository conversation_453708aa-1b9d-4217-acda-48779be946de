<template>
  <div>
    <el-drawer
      title="必学课程管理"
      :visible.sync="visible"
      size="1200px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd">
        <div class="m-tit">
          <span class="tit-txt">待选必学课程</span>
        </div>
        <div class="f-ml20">
          <el-row :gutter="16" class="m-query f-mt10">
            <el-form :inline="true" label-width="auto">
              <el-col :span="10">
                <el-form-item label="课程名称">
                  <el-input v-model="waitChooseCourseNameNew" clearable placeholder="请输入课程名称" />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item>
                  <el-button type="primary" @click="searchWaitChooseBase">查询</el-button>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
          <div class="f-mb15" v-if="hasChildren"><i class="f-fb">所属分类：</i>{{ nodePathName }}</div>
          <!--表格-->
          <el-table
            stripe
            :data="pageWaitChooseListNew"
            max-height="500px"
            class="m-table f-mt10"
            v-loading="waitLoading"
          >
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="课程名称" min-width="300">
              <template slot-scope="scope">{{ scope.row.name }}</template>
            </el-table-column>
            <el-table-column label="学时" min-width="120" align="center">
              <template slot-scope="scope">{{ scope.row.period }}</template>
            </el-table-column>
            <el-table-column label="查看" width="80" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="viewCourse(scope.row)">查看</el-button>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <!--                <el-checkbox v-model="checked" label="选择"></el-checkbox>-->
                <el-button type="text" size="mini" @click="changeCheckStatus(scope.row)">
                  {{ isChecked(scope.row) ? '取消选择' : '选择' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="waitChoosePageNew" v-bind="waitChoosePageNew"> </hb-pagination>
        </div>
        <div class="m-tit f-mt20">
          <span class="tit-txt">已选必学课程</span>
          <span class="f-ml10">
            一共 <i class="f-cr"> {{ selectCourseTotal }}</i> 门，<i class="f-cr">
              {{ selectedCoursePeriodTotal }}
            </i>
            学时
          </span>
        </div>
        <div class="f-ml20">
          <el-row :gutter="16" class="m-query f-mt10">
            <el-form :inline="true" label-width="auto">
              <el-col :span="10">
                <el-form-item label="课程名称">
                  <el-input v-model="selectedCourseName" clearable placeholder="请输入课程名称" />
                </el-form-item>
              </el-col>
              <el-col :span="14">
                <el-form-item>
                  <el-button type="primary" @click="searchSelectedBase">查询</el-button>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
          <div class="f-mb15" v-if="hasChildren"><i class="f-fb">所属分类：</i>{{ nodePathName }}</div>
          <!--表格-->
          <el-table stripe :data="pageSelectedList" max-height="500px" class="m-table f-mt10" v-loading="selectLoading">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="课程名称" min-width="300">
              <template slot-scope="scope">{{ scope.row.name }}</template>
            </el-table-column>
            <el-table-column label="学时" min-width="120" align="center">
              <template slot-scope="scope">{{ scope.row.period }}</template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="viewCourse(scope.row)">查看</el-button>
                <el-button type="text" size="mini" @click="deleteSelectedCourse(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSelectedSizeChange"
            @current-change="handleSelectedCurrentChange"
            :current-page="selectedPage.pageNo"
            :page-sizes="[5, 10, 15, 20, 25]"
            :page-size="selectedPage.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="selectedPage.totalSize"
          >
          </el-pagination>
        </div>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button @click="cancelConfigCompulsoryCourse">取消</el-button>
        <el-button type="primary" @click="confirmConfigCompulsoryCourse">确定</el-button>
      </div>
    </el-drawer>
    <el-dialog
      title="提示"
      :visible.sync="uiConfig.dialog.courseRepeatDialogVisible"
      width="450px"
      class="m-dialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div>同一个方案内的必学课程不允许重复，添加失败。</div>
      <!--<div>这是一段信息，这是<span class="f-cr">重点信息</span>，这是<span class="f-fb">加粗信息</span></div>-->
      <div class="content">
        <div v-for="(item, index) in courseRepeatListNew" :key="index">
          {{ item.name }}已作为{{ item.courseCategoryInfo }}分类下的必学课程
        </div>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="uiConfig.dialog.courseRepeatDialogVisible = false"> 确 定 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'
  import UtilsClass from '@hbfe/jxjy-admin-common/src/util'
  import {
    CourseRepeatListDetail,
    SchemeCourseDetailInCoursePackage,
    CreateSchemeUtils
  } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import QueryCoursePackage from '@api/service/management/resource/course-package/query/QueryCoursePackage'
  import { Query, UiPage } from '@hbfe/common'
  import QueryCourseM from '@api/service/management/resource/course/query/QueryCourse'
  import { CourseLoadModeEnum } from '@api/service/management/train-class/mutation/Enum/CourseLoadMode'
  import TrainingOutlineCourse from '@api/service/customer/course/query/vo/TrainingOutlineCourse'
  import { CompulsoryCourseInfo } from '@api/service/management/train-class/mutation/vo/CompulsoryCourseInfo'
  class Page {
    // 当前页数
    pageNo: number
    // 当前条数
    pageSize: number
    // 总数
    totalSize: number

    constructor() {
      this.pageNo = 1
      this.pageSize = 10
      this.totalSize = 0
    }
  }

  @Component
  export default class extends Vue {
    /**
     * 是否展示
     */
    @Prop({
      required: true,
      type: Boolean,
      default: false
    })
    value: boolean

    @Watch('value', {
      immediate: true,
      deep: true
    })
    valueChange(val: boolean) {
      this.visible = cloneDeep(val)
    }

    @Emit('input')
    @Watch('visible', {
      immediate: true,
      deep: true
    })
    visibleChange(val: number) {
      return val
    }

    path: string[] = [] as string[]
    @Prop({
      type: String,
      default: ''
    })
    schemeId: string
    /**
     * 是否有分类
     */
    @Prop({
      type: Boolean,
      default: false
    })
    hasChildren: boolean

    // 整颗大纲树
    outlineTree: Array<Classification> = new Array<Classification>()
    // 节点
    resource: Classification = new Classification()
    // 查包下课程
    queryCoursePackageM: QueryCoursePackage = new QueryCoursePackage()
    // 查必学课程
    queryCourseM: QueryCourseM = new QueryCourseM()
    // 是否显示抽屉
    visible = false

    // 必学课程id集合
    compulsoryCourseIdList: string[] = [] as string[]

    // 已选分页
    selectedPage: Page = new Page()
    // 已选课程名称
    selectedCourseName = ''
    // 已选必学课程列表
    selectedList: CompulsoryCourseInfo[] = [] as CompulsoryCourseInfo[]
    // 过滤已选必学课程
    filterSelectedList: CompulsoryCourseInfo[] = [] as CompulsoryCourseInfo[]
    // 过滤已选必学课程 - 分页
    pageSelectedList: CompulsoryCourseInfo[] = [] as CompulsoryCourseInfo[]
    // 重复课程列表
    courseRepeatList: CourseRepeatListDetail[] = [] as CourseRepeatListDetail[]
    waitLoading = false
    // 待选分页
    waitChoosePageNew: UiPage = new UiPage()
    // 待选课程名称
    waitChooseCourseNameNew = ''
    // 待选必学课程列表
    waitChooseListNew: SchemeCourseDetailInCoursePackage[] | TrainingOutlineCourse[] = []
    // 过滤待选必学课程 - 分页
    pageWaitChooseListNew: SchemeCourseDetailInCoursePackage[] | TrainingOutlineCourse[] = []
    // 重复课程
    courseRepeatListNew: Array<{ id: string; name: string; courseCategoryInfo: string }> = new Array<{
      id: string
      name: string
      courseCategoryInfo: string
    }>()
    // 已选分页
    selectLoading = false
    // 必学学时
    selectedPeriodTotal = 0
    /**
     *  ui相关的变量控制
     */
    uiConfig = {
      // 对话框是否展示
      dialog: {
        courseRepeatDialogVisible: false
      }
    }

    /**
     * 是否被选中 - 待选可选列表
     */
    get isChecked() {
      return (item: SchemeCourseDetailInCoursePackage) => {
        return this.compulsoryCourseIdList.indexOf(item.id) > -1
      }
    }

    /**
     * 路径展示名称
     */
    get nodePathName() {
      return CreateSchemeUtils.isWeightyArray(this.path) ? this.path.join(' > ') : ''
    }

    /**
     * 已选必学课程数量
     */
    get selectCourseTotal() {
      return this.selectedList?.length || 0
    }

    /**
     * 已选必学课程数量 -分页
     */
    get selectCoursePageTotal() {
      return this.pageSelectedList?.length || 0
    }

    /**
     * 已选必学课程学时总数
     */
    get selectedCoursePeriodTotal() {
      return (
        this.selectedList?.reduce((prev, cur) => {
          return CalculatorObj.add(cur.period, prev)
        }, 0) || 0
      )
    }

    /**
     * 已选必学课程学时总数
     */
    get selectedCoursePagePeriodTotal() {
      return (
        this.pageSelectedList?.reduce((prev, cur) => {
          return CalculatorObj.add(cur.period, prev)
        }, 0) || 0
      )
    }

    /**
     * 获取课程分类信息
     */
    getCourseCategory(arr: Array<string>) {
      return arr.join('>') || ''
    }

    /**
     * 查找节点路径 - 课程大纲树通用
     */
    outlineTreeFindPath(func: any, key: string) {
      return CreateSchemeUtils.treeFindPath<Classification>(this.outlineTree, func, key, 'childOutlines')
    }

    /**
     * 校验是否重复
     */
    async validCompulsoryCourseMultiple() {
      const tree = new Classification()
      tree.childOutlines = this.outlineTree
      const compulsoryCourseIdMap: Map<string, string> = new Map<string, string>()
      // // 获取所有叶子节点
      const leaves = CreateSchemeUtils.treeFindAllLeaves<Classification>(this.outlineTree, 'childOutlines')
      leaves?.forEach((el: Classification) => {
        if (el.coursePackageId !== this.resource.coursePackageId) {
          const courseIds = el.compulsoryCourseIdList
          const courseCategoryInfo =
            CreateSchemeUtils.treeFindPath<Classification>(
              this.outlineTree,
              (node: Classification) => {
                return node.coursePackageId === el.coursePackageId
              },
              'name',
              'childOutlines'
            )?.join('>') || ''
          courseIds.forEach((subEl) => {
            compulsoryCourseIdMap.set(subEl, courseCategoryInfo)
          })
        }
      })
      console.log(compulsoryCourseIdMap, Array.from(compulsoryCourseIdMap.keys()), 'compulsoryCourseIdMap')

      this.courseRepeatListNew = []
      this.compulsoryCourseIdList.forEach((item) => {
        const flag = Array.from(compulsoryCourseIdMap.keys()).find((ite) => ite === item)
        if (flag) {
          //
          this.courseRepeatListNew.push({ id: item, name: '', courseCategoryInfo: compulsoryCourseIdMap.get(item) })
        }
      })
      const idList = this.courseRepeatListNew.map((val) => val.id)
      const courseRepeatNameList = await this.queryCourseM.queryCourseByIdList(idList)

      this.courseRepeatListNew.forEach((ite) => {
        ite.name = courseRepeatNameList.find((item) => item.id === ite.id).name
      })
      return CreateSchemeUtils.isWeightyArray(this.courseRepeatListNew) ? true : false
    }

    constructor() {
      super()
      this.waitChoosePageNew = new UiPage(this.pageCourseList, this.pageCourseList)
    }

    // 待选课程分页
    async pageCourseList() {
      this.waitLoading = true
      const couresName = this.waitChooseCourseNameNew ? this.waitChooseCourseNameNew : undefined
      if (this.resource.courseLoadMode === CourseLoadModeEnum.BY_COURSE_PACKAGE_ID) {
        this.pageWaitChooseListNew = await this.queryCoursePackageM.pageQueryCourseListInCoursePackage(
          this.waitChoosePageNew,
          this.resource.coursePackageId,
          couresName
        )
      }
      if (this.resource.courseLoadMode === CourseLoadModeEnum.BY_OUTLINE_ID) {
        this.pageWaitChooseListNew = await this.queryCourseM.queryCourseListInSchemeByOutline(
          this.waitChoosePageNew,
          [this.resource.idCopy],
          this.schemeId,
          couresName
        )
      }
      this.waitLoading = false
    }

    /**
     * 配置数据
     */
    /**
     * 配置数据
     */
    async setData() {
      this.path = this.outlineTreeFindPath((node: Classification) => {
        return node.id === this.resource.id
      }, 'name')
      this.compulsoryCourseIdList = cloneDeep(this.resource.compulsoryCourseIdList)
      this.selectedList = cloneDeep(this.resource.compulsoryCourseInfoList)
      // 待选(新)
      this.waitChooseListNew = new Array<SchemeCourseDetailInCoursePackage>()
      this.waitChooseCourseNameNew = ''
      await this.searchWaitChooseBase()
      // 已选
      this.selectedList = []
      if (
        this.resource.courseLoadMode === CourseLoadModeEnum.BY_OUTLINE_ID &&
        this.resource.compulsoryCourseIdList?.length &&
        !this.resource.compulsoryCourseInfoList.length
      ) {
        this.selectLoading = true
        this.resource.compulsoryCourseInfoList = await this.queryCourseM.queryAllCourseListInSchemeByOutline(
          this.schemeId,
          this.resource.idCopy,
          this.resource.compulsoryCourseIdList
        )
        this.selectLoading = false
      }
      this.selectedList = cloneDeep(this.resource.compulsoryCourseInfoList)
      this.selectedCourseName = ''
      this.selectedPage = new Page()
      this.searchSelectedBase()
    }

    /**
     * 查询可选课程列表 - 指定首页
     */
    async searchWaitChooseBase() {
      this.waitChoosePageNew.pageNo = 1
      await this.pageCourseList()
    }

    /**
     * 查询已选课程列表 - 指定首页
     */
    searchSelectedBase() {
      this.selectedPage.pageNo = 1
      this.pageSelected()
    }

    /**
     * 查询已选课程列表
     */
    pageSelected() {
      this.filterSelectedList = []
      this.pageSelectedList = []
      this.selectedList.forEach((el) => {
        if (el.name.includes(this.selectedCourseName)) {
          this.filterSelectedList.push(el)
        }
      })
      this.pageSelectedList = this.pageBySize(
        this.filterSelectedList,
        this.selectedPage.pageNo,
        this.selectedPage.pageSize
      )
      console.log('filterSelectedList', this.filterSelectedList)
      console.log('pageSelectedList', this.pageSelectedList)
      this.selectedPage.totalSize = this.filterSelectedList.length
    }

    /**
     * 手动分页
     */
    pageBySize(arr: CompulsoryCourseInfo[], pageNo: number, pageSize: number) {
      const offset = (pageNo - 1) * pageSize
      const maxSize = pageNo * pageSize
      return maxSize >= arr.length ? arr.slice(offset, arr.length) : arr.slice(offset, offset + pageSize)
    }

    /**
     * 切换已选课程分页 - 每页条数
     */
    handleSelectedSizeChange(pageSize: number) {
      this.selectedPage.pageSize = pageSize
      this.pageSelected()
    }

    /**
     * 切换已选选课程分页 - 第几页
     */
    handleSelectedCurrentChange(pageNo: number) {
      this.selectedPage.pageNo = pageNo
      this.pageSelected()
    }

    /**
     * 取消配置必学课程
     */
    cancelConfigCompulsoryCourse() {
      this.visible = false
    }

    /**
     * 确认配置必学课程
     */
    async confirmConfigCompulsoryCourse() {
      try {
        if (!this.resource.coursePackageId) {
          this.$message.error('请选择一个课程包，才能添加课程')
          throw new Error('中断函数执行')
        }
        if (this.hasChildren) {
          const isRepeat = await this.validCompulsoryCourseMultiple()
          if (isRepeat) {
            this.uiConfig.dialog.courseRepeatDialogVisible = true
            throw new Error('中断函数执行')
          }
          this.$emit(
            'updateNodeCompulsoryCourse',
            this.resource.id,
            this.compulsoryCourseIdList,
            this.selectedCoursePeriodTotal,
            this.selectedList
          )
        } else {
          this.$emit(
            'updateNodeCompulsoryCourse',
            '',
            this.compulsoryCourseIdList,
            this.selectedCoursePeriodTotal,
            this.selectedList
          )
        }
        this.visible = false
      } catch (e) {
        //
      }
    }

    /**
     * 切换可选列表选中情况
     */
    changeCheckStatus(item: SchemeCourseDetailInCoursePackage) {
      const id = item.id
      const index = this.compulsoryCourseIdList.indexOf(id)
      if (index > -1) {
        this.compulsoryCourseIdList.splice(index, 1)
        this.selectedPeriodTotal = CalculatorObj.subtract(this.selectedPeriodTotal, item.period)
        const selectIndex = this.selectedList.findIndex((ite) => ite.id === item.id)
        if (selectIndex > -1) this.selectedList.splice(selectIndex, 1)
      } else {
        this.compulsoryCourseIdList.push(id)
        this.selectedPeriodTotal = CalculatorObj.add(this.selectedPeriodTotal, item.period)
        const course = new CompulsoryCourseInfo()
        course.id = item.id
        course.name = item.name
        course.period = item.period
        course.courseCategoryInfo = item.courseCategoryInfo
        this.selectedList.push(course)
      }
      this.selectedCourseName = ''
      this.searchSelectedBase()
    }

    /**
     * 查看课程
     */
    viewCourse(item: SchemeCourseDetailInCoursePackage) {
      const targetUrl = `/resource/course/detail/${item.id}`
      UtilsClass.openUrl(targetUrl)
    }

    /**
     * 删除已选课程
     */
    deleteSelectedCourse(item: SchemeCourseDetailInCoursePackage) {
      const id = item.id
      const index = this.compulsoryCourseIdList.indexOf(id)
      if (index > -1) {
        this.compulsoryCourseIdList.splice(index, 1)
        this.selectedPeriodTotal = CalculatorObj.subtract(this.selectedPeriodTotal, item.period)
        const selectIndex = this.selectedList.findIndex((ite) => ite.id === item.id)
        if (selectIndex > -1) this.selectedList.splice(selectIndex, 1)
      }
      this.selectedCourseName = ''
      this.searchSelectedBase()
    }
  }
</script>
