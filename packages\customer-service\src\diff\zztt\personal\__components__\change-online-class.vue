<template>
  <div>
    <template
      v-if="$hasPermission('queryExchangeClass')"
      desc="查看换班信息"
      actions="searchWaitExchangeBase,searchExchangedRecordsBase,@ExchangeTrainClassDistributor"
    >
      <div class="f-p15">
        <el-card shadow="never" class="m-card is-header f-mb15">
          <div slot="header" class="f-flex f-align-center">
            <span class="tit-txt">待更换班级</span>
            <span class="f-cr f-ml5">
              （换班的前提条件为：培训班同等价格！）此功能操作仅支持【培训班】类型的培训方案。
            </span>
          </div>
          <div class="f-p20">
            <hb-search-wrapper @reset="resetWaitExchangeQueryParams" class="m-query">
              <el-form-item label="年度：">
                <biz-year-select v-model="waitExchangeQueryParams.year" placeholder="请选择年度"></biz-year-select>
              </el-form-item>
              <el-form-item label="班级名称：">
                <el-input
                  v-model="waitExchangeQueryParams.schemeName"
                  clearable
                  placeholder="请输入班级名称"
                  @clear="waitExchangeQueryParams.schemeName = ''"
                />
              </el-form-item>
              <template slot="actions">
                <el-button type="primary" @click="searchWaitExchangeBase">查询</el-button>
              </template>
            </hb-search-wrapper>
            <!--表格-->
            <el-table
              stripe
              :data="waitExchangeList"
              max-height="500px"
              class="m-table"
              v-loading="waitExchangeQuery.loading"
            >
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="培训班信息" min-width="300">
                <template slot-scope="scope">
                  <p>
                    <el-tag type="primary" effect="dark" size="mini">{{ waitExchangeSchemeType(scope.row) }}</el-tag
                    >{{ scope.row.schemeName }}
                  </p>
                  <p class="f-f13 f-c9 f-ml5">
                    <!-- 【{{ scope.row.industry }} / {{ scope.row.year }}年 / {{ scope.row.subjectType }}】 -->
                    <el-tag type="warning" size="mini" v-if="scope.row.distributorId">分销推广</el-tag>
                    <el-tag type="danger" v-if="scope.row.thirdPartyPlatform">{{ scope.row.thirdPartyPlatform }}</el-tag
                    >{{ getPrototype(scope.row) }}
                    <!-- {{ scope.row.professionalName }} -->
                  </p>
                </template>
              </el-table-column>
              <el-table-column label="单价(元)" min-width="140" align="right">
                <template slot-scope="scope">
                  {{ scope.row.price }}
                </template>
              </el-table-column>
              <el-table-column label="考核情况" min-width="140" align="center">
                <template slot-scope="scope">
                  <div v-if="assessmentStatusStyle(scope.row)">
                    <el-tag :type="assessmentStatusStyle(scope.row)">{{ assessmentStatus(scope.row) }}</el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="状态" min-width="100">
                <template slot-scope="scope">
                  <div v-if="statusStyle(scope.row)">
                    <el-badge is-dot :type="statusStyle(scope.row)" class="badge-status">
                      {{ status(scope.row) }}
                    </el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="来源类型" min-width="120" align="center">
                <template slot-scope="scope">{{ resourceType(scope.row) }}</template>
              </el-table-column>
              <el-table-column label="操作" min-width="100" align="center" fixed="right">
                <template slot-scope="scope">
                  <template
                    v-if="$hasPermission('exchangeClass')"
                    desc="换班"
                    actions="exchangeClass,@ExchangeTrainClass"
                  >
                    <el-button
                      type="text"
                      size="mini"
                      @click="exchangeClass(scope.row)"
                      :disabled="!enableExchangeTrainClass(scope.row)"
                    >
                      换班
                    </el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <hb-pagination :page="waitExchangePage" v-bind="waitExchangePage"> </hb-pagination>
          </div>
        </el-card>
        <el-card shadow="never" class="m-card is-header f-mb15">
          <div slot="header" class="f-flex f-align-center">
            <span class="tit-txt">换班记录</span>
          </div>
          <div class="f-p20">
            <hb-search-wrapper @reset="resetExchangedRecordsQueryParams" class="m-query">
              <el-form-item label="年度：">
                <biz-year-select v-model="exchangedRecordsQueryParams.year" placeholder="请选择年度"></biz-year-select>
              </el-form-item>
              <el-form-item label="班级名称：">
                <el-input
                  v-model="exchangedRecordsQueryParams.schemeName"
                  clearable
                  placeholder="请输入培训班名称"
                  @clear="exchangedRecordsQueryParams.schemeName = ''"
                />
              </el-form-item>
              <el-form-item label="换班状态：">
                <el-select
                  v-model="exchangedRecordsQueryParams.exchangeTrainClassStatus"
                  clearable
                  filterable
                  placeholder="请选择换班状态"
                >
                  <el-option
                    v-for="(item, index) in exchangeTrainClassStatusList"
                    :key="index"
                    :value="item.code"
                    :label="item.desc"
                  ></el-option>
                </el-select>
              </el-form-item>
              <template slot="actions">
                <el-button type="primary" @click="searchExchangedRecordsBase">查询</el-button>
              </template>
            </hb-search-wrapper>
            <!--表格-->
            <el-table
              stripe
              :data="exchangedRecordsList"
              max-height="500px"
              class="m-table"
              v-loading="exchangedRecordsQuery.loading"
              ref="exchangedRecordsUITableRef"
            >
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="初始班级（换班前）" width="300">
                <template slot-scope="scope">
                  <p>
                    <el-tag type="primary" effect="dark" size="mini">{{
                      exchangeRecordCommoditySchemeType(scope.row.originalCommodity)
                    }}</el-tag>
                    {{ scope.row.originalCommodity.saleTitle }}
                  </p>
                  <p class="f-f13 f-c9 f-ml5">
                    <!-- 【{{ scope.row.originalCommodity.skuValueNameProperty.industry.skuPropertyName }} /
                    {{ scope.row.originalCommodity.skuValueNameProperty.year.skuPropertyName }}年 /
                    {{ scope.row.originalCommodity.skuValueNameProperty.subjectType.skuPropertyName }}】 -->
                    {{ getChangeClassPrototype(scope.row.originalCommodity.skuValueNameProperty) }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column label="新班级（换班后）" min-width="300">
                <template slot-scope="scope">
                  <p>
                    <el-tag type="primary" effect="dark" size="mini">{{
                      exchangeRecordCommoditySchemeType(scope.row.exchangeCommodity)
                    }}</el-tag>
                    {{ scope.row.exchangeCommodity.saleTitle }}
                  </p>

                  <p class="f-f13 f-c9 f-ml5">
                    <!-- 【{{ scope.row.exchangeCommodity.skuValueNameProperty.industry.skuPropertyName }} /
                    {{ scope.row.exchangeCommodity.skuValueNameProperty.year.skuPropertyName }}年 /
                    {{ scope.row.exchangeCommodity.skuValueNameProperty.subjectType.skuPropertyName }}】 -->
                    {{ getChangeClassPrototype(scope.row.exchangeCommodity.skuValueNameProperty) }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column label="换班状态" min-width="150" align="center">
                <template slot-scope="scope">
                  <div>
                    <el-tag
                      :type="exchangeTrainClassStatusStyle(scope.row)"
                      v-if="exchangeTrainClassStatusStyle(scope.row)"
                    >
                      {{ exchangeTrainClassStatus(scope.row) }}
                    </el-tag>
                    <p class="f-mt5">
                      <a class="f-link f-underline f-cb f-f12" @click="viewExchangeDetail(scope.row)">查看详情</a>
                    </p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="换班时间" min-width="180">
                <template slot-scope="scope">{{ scope.row.exchangeTrainClassTime }}</template>
              </el-table-column>
              <el-table-column label="操作帐号" min-width="120">
                <template slot-scope="scope">{{ scope.row.operatorName }}</template>
              </el-table-column>
              <el-table-column label="操作" min-width="100" align="center">
                <template slot-scope="scope">
                  <template
                    v-if="$hasPermission('continueExchangeClass')"
                    desc="继续换班"
                    actions="continueExchangeTrainClass"
                  >
                    <el-popconfirm
                      v-if="scope.row.enableExchangeTrainClass"
                      confirm-button-text="确认"
                      title="是否确认继续换班？"
                      @confirm="continueExchangeTrainClass(scope.row)"
                    >
                      <el-button type="text" size="mini" slot="reference">继续换班</el-button>
                    </el-popconfirm>
                  </template>
                  <span v-if="!scope.row.enableExchangeTrainClass">—</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
        <!--查看换班详情-->
        <el-drawer
          title="查看详情"
          :visible.sync="uiConfig.dialog.viewExchangeTrainClassDetailVisible"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd f-mt20 f-mlr40">
            <el-timeline>
              <el-timeline-item v-for="(item, index) in selectedExchangeOrderStatusList" :key="index">
                <p class="f-mb10 f-fb f-f15">
                  {{ item.date }} <span class="f-ml30">{{ exchangeOrderStatus(item.status) }}</span>
                </p>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-drawer>
        <exchange-train-class
          ref="exchangeTrainClassRef"
          :visible.sync="uiConfig.dialog.replaceTrainClassVisible"
          @reloadData="handleReloadData"
        />
        <exchange-train-class-distributor
          ref="exchangeDistributorTrainClassRef"
          :visible.sync="uiConfig.dialog.replaceDistributorIdTrainClassVisible"
          @confirmExchangeTrainClass="confirmExchangeTrainClass"
        />
      </div>
    </template>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import TrainClassResourceType, {
    TrainClassResourceEnum
  } from '@api/service/management/train-class/query/enum/TrainClassResourceType'
  import QueryWaitExchangeTrainClassListVo from '@api/service/management/train-class/query/vo/QueryWaitExchangeTrainClassListVo'
  import QueryExchangeOrderRecordListVo from '@api/service/management/train-class/query/vo/QueryExchangeOrderRecordListVo'
  import ExchangeOrderRecordDetailVo from '@api/service/management/train-class/query/vo/ExchangeOrderRecordDetailVo'
  import BizYearSelect from '@hbfe/jxjy-admin-components/src/biz/biz-year-select.vue'
  import TrainClassSchemeType, {
    TrainClassSchemeEnum
  } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
  import TrainClassAssessmentStatusType, {
    TrainClassAssessmentStatusEnum
  } from '@api/service/management/train-class/query/enum/TrainClassAssessmentStatusType'
  import TrainClassStatusType, {
    TrainClassStatusEnum
  } from '@api/service/management/train-class/query/enum/TrainClassStatusType'
  import ExchangeTrainClassStatusType, {
    ExchangeTrainClassStatusEnum
  } from '@api/service/management/train-class/query/enum/ExchangeTrainClassStatusType'
  import QueryExchangeTrainClass from '@api/service/management/train-class/query/QueryExchangeTrainClass'
  import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
  import { MutationCreateExchangeOrder } from '@api/service/management/trade/single/order/mutation/MutationCreateExchangeOrder'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import ExchangeOrderStatus from '@api/service/management/train-class/query/vo/ExchangeOrderStatus'
  import ExchangeOrderStatusType, {
    ExchangeOrderStatusEnum
  } from '@api/service/management/train-class/query/enum/ExchangeOrderStatusType'
  import ExchangeTrainClass from '@hbfe/jxjy-admin-customerService/src/diff/zztt/personal/__components__/exchange-train-class.vue'
  import { bind, debounce } from 'lodash-decorators'
  import { cloneDeep } from 'lodash'
  import OrderCommodityVo from '@api/service/management/train-class/query/vo/OrderCommodityVo'
  import CreateExchangeOrderModel from '@hbfe/jxjy-admin-customerService/src/personal/components/model/CreateExchangeOrderModel'
  import QueryOrderDetail from '@api/service/management/trade/single/order/query/QueryOrderDetail'
  import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
  //由于换班弹窗需要查询退款中班级信息
  import QueryRefundList from '@api/service/management/trade/single/order/query/QueryRefundList'
  import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import DiffQueryExchangeTrainClass from '@api/service/diff/management/zztt/train-class/QueryExchangeTrainClass'
  import WaitExchangeTrainClassDetailVo from '@api/service/diff/management/zztt/train-class/model/WaitExchangeTrainClassDetailVo'
  import SchemeType, { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import MutationExchangeOrder from '@api/service/management/trade/single/order/mutation/MutationExchangeOrder'
  import ExchangeTrainClassDistributor from '@hbfe/fx-manage/src/components/pricing-scheme-dialog/exchange-train-class.vue'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  @Component({
    components: { ExchangeTrainClass, BizYearSelect, ExchangeTrainClassDistributor }
  })
  export default class extends Vue {
    @Prop({
      type: String,
      default: ''
    })
    userId = ''

    @Watch('userId', {
      immediate: true,
      deep: true
    })
    async userIdChange(val: string) {
      // console.log('changeClass_userId', val)
      await this.resetWaitExchangeQueryParams()
      await this.resetExchangedRecordsQueryParams()
    }

    /**
     * 【组件】更换班级
     */
    @Ref('exchangeTrainClassRef') exchangeTrainClassRef: ExchangeTrainClass
    @Ref('exchangeDistributorTrainClassRef') exchangeDistributorTrainClassRef: ExchangeTrainClassDistributor
    /**
     * 【组件】换班记录表格
     */
    @Ref('exchangedRecordsUITableRef') exchangedRecordsUITableRef: any

    /**
     * ui控制组
     */
    uiConfig = {
      dialog: {
        // 查看换班详情
        viewExchangeTrainClassDetailVisible: false,
        // 更换培训班
        replaceTrainClassVisible: false,
        // 更换培训班
        replaceDistributorIdTrainClassVisible: false
      }
    }
    // 订单详情查询实例对象
    orderDetailObj: QueryOrderDetail = TradeModule.singleTradeBatchFactor.orderFactor.getQueryOrderDetail()
    // 分页 - 待更换班级
    waitExchangePage: UiPage
    // 查询 - 待更换班级
    waitExchangeQuery: Query = new Query()
    // 查询参数 - 待更换班级
    waitExchangeQueryParams: QueryWaitExchangeTrainClassListVo = new QueryWaitExchangeTrainClassListVo()
    // 列表 - 待更换班级
    waitExchangeList: WaitExchangeTrainClassDetailVo[] = []

    //退款查询接口请求
    queryRefundOrder: QueryRefundList = TradeModule.singleTradeBatchFactor.orderFactor.getQueryRefundOrder()
    // 分页 - 换班记录
    exchangedRecordsPage: UiPage

    //分页 - 退款班级记录 （筛选可更换班级）
    returnOrderPage: UiPage
    // 查询 - 换班记录
    exchangedRecordsQuery: Query = new Query()
    // 查询参数 - 换班记录
    exchangedRecordsQueryParams: QueryExchangeOrderRecordListVo = new QueryExchangeOrderRecordListVo()
    // 列表 - 换班记录
    exchangedRecordsList: ExchangeOrderRecordDetailVo[] = []

    // 培训方案类型列表
    schemeTypeList: EnumOption<TrainClassSchemeEnum>[] = TrainClassSchemeType.list()

    // 考核情况列表 - 待更换班级
    assessmentStatusList: EnumOption<TrainClassAssessmentStatusEnum>[] = TrainClassAssessmentStatusType.list()
    // 考核情况列表 - 待更换班级
    assessmentStatusMap: Map<TrainClassAssessmentStatusEnum, string> = new Map<TrainClassAssessmentStatusEnum, string>()
      .set(TrainClassAssessmentStatusEnum.Qualified, 'success')
      .set(TrainClassAssessmentStatusEnum.Unqualified, 'danger')
      .set(TrainClassAssessmentStatusEnum.For_Inspection, 'warning')

    // 状态列表 - 待更换班级
    statusList: EnumOption<TrainClassStatusEnum>[] = TrainClassStatusType.list()
    // 状态样式列表 - 待更换班级
    statusMap: Map<TrainClassStatusEnum, string> = new Map<TrainClassStatusEnum, string>()
      .set(TrainClassStatusEnum.Effective, 'success')
      .set(TrainClassStatusEnum.Frozen, 'primary')
      .set(TrainClassStatusEnum.Failure, 'info')

    // 来源类型列表 - 待更换班级
    resourceTypeList: EnumOption<TrainClassResourceEnum>[] = TrainClassResourceType.list()

    // 换班状态 - 换班记录
    exchangeTrainClassStatusList: EnumOption<ExchangeTrainClassStatusEnum>[] = ExchangeTrainClassStatusType.list()
    // 换班状态样式列表 - 换班记录
    exchangeTrainClassStatusStyleMap: Map<ExchangeTrainClassStatusEnum, string> = new Map<
      ExchangeTrainClassStatusEnum,
      string
    >()
      .set(ExchangeTrainClassStatusEnum.Exchanging, 'warning')
      .set(ExchangeTrainClassStatusEnum.Complete_Exchange, 'success')

    // 换货单状态 - 换班记录
    exchangeOrderStatusList: EnumOption<ExchangeOrderStatusEnum>[] = ExchangeOrderStatusType.list()

    // 查询接口入口
    queryRequestEntrance: QueryExchangeTrainClass =
      TrainClassManagerModule.queryTrainClassFactory.getQueryExchangeTrainClass()
    /**
     * 差异化查询入口
     */
    queryRequestEntranceDiff: DiffQueryExchangeTrainClass = new DiffQueryExchangeTrainClass()

    // 业务接口入口（发起换货）
    mutationRequestEntrance: MutationCreateExchangeOrder =
      TradeModule.singleTradeBatchFactor.mutationFactory.getMutationCreateExchangeOrder()

    // 换班详情列表（选中后）
    selectedExchangeOrderStatusList: ExchangeOrderStatus[] = []

    // 创建换货单
    createExchangeOrder: CreateExchangeOrderModel = new CreateExchangeOrderModel()

    // 剔除培训班商品id集合
    excludeCommoditySkuIdList: string[] = []

    @Watch('waitExchangeQueryParams', {
      deep: true
    })
    waitExchangeQueryParamsChange(val: any) {
      // console.log('waitExchangeQueryParams', val)
    }

    @Watch('exchangedRecordsQueryParams', {
      deep: true
    })
    exchangedRecordsQueryParamsChange(val: any) {
      // console.log('exchangedRecordsQueryParams', val)
    }

    /**
     * 【待更换班级】培训方案类型
     */
    get waitExchangeSchemeType() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return SchemeType.getSchemeType(item.schemeType, true)
      }
    }

    /**
     * 【待更换班级】考核情况
     */
    get assessmentStatus() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return this.assessmentStatusList.find((el) => el.code === item.assessmentStatus)?.desc || ''
      }
    }

    /**
     * 【待更换班级】考核情况样式
     */
    get assessmentStatusStyle() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return this.assessmentStatusMap.get(item.assessmentStatus)
      }
    }

    /**
     * 【待更换班级】状态
     */
    get status() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return this.statusList.find((el) => el.code === item.status)?.desc || ''
      }
    }

    /**
     * 【待更换班级】状态样式
     */
    get statusStyle() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return this.statusMap.get(item.status)
      }
    }

    /**
     * 【待更换班级】来源类型
     */
    get resourceType() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        // 华医网
        if (item.resourceType == 5) return '个人报名'
        return this.resourceTypeList.find((el) => el.code === item.resourceType)?.desc || ''
      }
    }

    /**
     * 【待更换班级】允许换班
     */
    get enableExchangeTrainClass() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return item.status == TrainClassStatusEnum.Effective ? true : false
      }
    }

    /**
     * 【换班记录】培训班方案类型
     */
    get exchangeRecordCommoditySchemeType() {
      return (item: OrderCommodityVo) => {
        const schemeType = item.resourceVo?.schemeTypeVo || null
        const schemeTypeName = this.schemeTypeList.find((el) => el.code === schemeType)?.desc || ''
        const showName = schemeTypeName ? '培训班-' + schemeTypeName : ''
        return showName
      }
    }

    /**
     * 【换班记录】换班状态
     */
    get exchangeTrainClassStatus() {
      return (item: ExchangeOrderRecordDetailVo) => {
        return this.exchangeTrainClassStatusList.find((el) => el.code === item.exchangeTrainClassStatus)?.desc || ''
      }
    }

    /**
     * 【换班记录】换班状态样式
     */
    get exchangeTrainClassStatusStyle() {
      return (item: ExchangeOrderRecordDetailVo) => {
        return this.exchangeTrainClassStatusStyleMap.get(item.exchangeTrainClassStatus)
      }
    }

    /**
     * 【换班记录】换货单状态
     */
    get exchangeOrderStatus() {
      return (status: ExchangeOrderStatusEnum) => {
        return this.exchangeOrderStatusList.find((el) => el.code === status)?.desc || ''
      }
    }
    isTrainCooperate(type: number) {
      return type == SchemeTypeEnum.trainingCooperation
    }

    constructor() {
      super()
      this.waitExchangePage = new UiPage(this.pageWaitExchangeList, this.pageWaitExchangeList)
      this.exchangedRecordsPage = new UiPage(this.pageExchangedRecordsList, this.pageExchangedRecordsList)
      this.returnOrderPage = new UiPage(this.getRefundOrderList, this.getRefundOrderList)
    }

    /**
     * 页面初始化
     */
    async created() {
      // 给表格内滚动条滚动增加监听事件
      await this.$nextTick(async () => {
        const element = this.exchangedRecordsUITableRef.bodyWrapper
        element.addEventListener('scroll', this.infiniteScroll)
      })
    }

    /**
     * 加载第一页数据标志位
     */
    firstPageLoadFlag = false

    /**
     * 【换班记录】无限加载
     */
    @bind
    @debounce(200)
    async infiniteScroll() {
      const element = this.exchangedRecordsUITableRef.bodyWrapper
      const scrollDistance = element.scrollHeight - element.scrollTop - element.clientHeight
      // console.log('scrollInfo', scrollDistance, element.scrollHeight, element.scrollTop, element.clientHeight)
      if (scrollDistance <= 0 && !this.firstPageLoadFlag) {
        if (this.exchangedRecordsList.length >= this.exchangedRecordsPage.totalSize) {
          this.$message.warning('没有更多数据')
        } else {
          this.exchangedRecordsPage.pageNo++
          await this.loadMoreExchangedRecords()
        }
      }
    }

    /**
     * 查询待换班列表
     */
    async searchWaitExchangeBase() {
      this.waitExchangePage.pageNo = 1
      await this.pageWaitExchangeList()
    }

    /**
     * 查询待换班列表
     */
    async pageWaitExchangeList() {
      this.waitExchangeQuery.loading = true

      try {
        this.waitExchangeList = [] as WaitExchangeTrainClassDetailVo[]
        this.excludeCommoditySkuIdList = [] as string[]
        // 未选中用户 拦截查询全部数据
        if (!this.waitExchangeQueryParams.buyerId) {
          this.waitExchangeList = []
          this.excludeCommoditySkuIdList = []
          return
        }
        this.waitExchangeQueryParams.trainingMode = TrainingModeEnum.online
        const response = await this.queryRequestEntranceDiff.queryAllWaitExchangeTrainClassList(
          this.waitExchangePage,
          this.waitExchangeQueryParams
        )
        this.waitExchangeList = response.pageList
        this.excludeCommoditySkuIdList = response.commoditySkuIdList
      } catch (e) {
        console.log(e)
      } finally {
        this.waitExchangeQuery.loading = false
      }
    }

    /**
     * 查询换班记录列表
     */
    async searchExchangedRecordsBase() {
      this.firstPageLoadFlag = true
      this.exchangedRecordsPage.pageNo = 1
      await this.pageExchangedRecordsList()
      this.firstPageLoadFlag = false
    }

    /**
     * 查询换班记录列表
     */
    async pageExchangedRecordsList() {
      if (!this.exchangedRecordsQueryParams.buyerIdList.length) {
        return
      }
      this.exchangedRecordsQuery.loading = true
      try {
        this.exchangedRecordsList = [] as ExchangeOrderRecordDetailVo[]
        this.exchangedRecordsQueryParams.trainingMode = TrainingModeEnum.online
        this.exchangedRecordsList = await this.queryRequestEntrance.queryExchangeTrainClassRecordList(
          this.exchangedRecordsPage,
          this.exchangedRecordsQueryParams
        )
        // console.log('exchangedRecordsList', this.exchangedRecordsList)
      } catch (e) {
        console.log(e)
      } finally {
        this.exchangedRecordsQuery.loading = false
      }
    }
    /**
     *查询退款中班级，筛选出班级ID
     */
    async getRefundOrderList() {
      const returnOrderRequestVo: ReturnOrderRequestVo = new ReturnOrderRequestVo()
      this.returnOrderPage.pageSize = 200
      this.returnOrderPage.pageNo = 1
      returnOrderRequestVo.basicData.returnOrderStatus = [0, 1, 2]
      const RefundOrderList = await this.queryRefundOrder.queryRefundOrderList(
        this.returnOrderPage,
        returnOrderRequestVo
      )

      if (RefundOrderList?.length > 0) {
        const RefundOrderIdList = RefundOrderList.map((item) => {
          //退款中的ID如果没有在筛选项中 就返回
          const RefundOrderIndex = this.excludeCommoditySkuIdList.findIndex((index) => {
            return index === item.refundCommodity.commoditySku.commoditySkuId
          })
          if (RefundOrderIndex === -1) {
            return item.refundCommodity.commoditySku.commoditySkuId
          }
        })
        // 将没有在筛选项里的添加到筛选项中
        this.excludeCommoditySkuIdList = this.excludeCommoditySkuIdList.concat(RefundOrderIdList)
      }
    }

    /**
     * 【换班记录】加载更多
     */
    async loadMoreExchangedRecords() {
      this.exchangedRecordsQuery.loading = true
      try {
        const origin = this.exchangedRecordsList.slice()
        const result = await this.queryRequestEntrance.queryExchangeTrainClassRecordList(
          this.exchangedRecordsPage,
          this.exchangedRecordsQueryParams
        )
        this.exchangedRecordsList = [...origin, ...result]
      } catch (e) {
        console.log(e)
      } finally {
        this.exchangedRecordsQuery.loading = false
      }
    }

    /**
     * 重置 - 待更换班级
     */
    async resetWaitExchangeQueryParams() {
      this.waitExchangeQueryParams = new QueryWaitExchangeTrainClassListVo()
      if (this.userId) {
        this.waitExchangeQueryParams.buyerId = this.userId
        await this.searchWaitExchangeBase()
      } else {
        this.waitExchangeList = [] as WaitExchangeTrainClassDetailVo[]
        this.waitExchangePage.totalSize = 0
      }
    }

    /**
     * 重置 - 换班记录
     */
    async resetExchangedRecordsQueryParams() {
      this.exchangedRecordsQueryParams = new QueryExchangeOrderRecordListVo()
      if (this.userId) {
        this.exchangedRecordsQueryParams.buyerIdList = this.userId ? [this.userId] : ([] as string[])
        await this.searchExchangedRecordsBase()
      } else {
        this.exchangedRecordsList = [] as ExchangeOrderRecordDetailVo[]
      }
    }

    /**
     * 查看换班详情
     */
    viewExchangeDetail(row: ExchangeOrderRecordDetailVo) {
      this.selectedExchangeOrderStatusList = row.exchangeOrderStatusList || ([] as ExchangeOrderStatus[])
      this.uiConfig.dialog.viewExchangeTrainClassDetailVisible = true
    }

    /**
     * 换班
     */
    async exchangeClass(row: WaitExchangeTrainClassDetailVo) {
      if (row.schemeType == (SchemeTypeEnum.trainingCooperation as number)) {
        return this.$confirm('当前订单参与合作办学，暂不支持换班', '提示', {
          confirmButtonText: '我知道了',
          showCancelButton: false,
          showClose: false
        })
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        this.orderDetailObj.orderNo = row.orderNo
        await this.orderDetailObj.queryOrderDetailByOrderNo()
        // const orderDetail = this.orderDetailObj.orderDetail
        // if (orderDetail.subOrderItems[0].returnStatus != 0) {
        //   return this.$message.warning('该订单处于已经退货或者正在退货中，无法更换培训班')
        // }
        this.createExchangeOrder = new CreateExchangeOrderModel()
        this.createExchangeOrder.schemeName = row.schemeName
        this.createExchangeOrder.orderNo = row.orderNo
        this.createExchangeOrder.subOrderNo = row.subOrderNo
        this.createExchangeOrder.price = row.price
        this.createExchangeOrder.isSpecialOrder = row.saleChannel == 2 ? true : false
        this.createExchangeOrder.specialOrderName = row.saleChannelName
        this.createExchangeOrder.saleChannelId = row.saleChannelId
        this.createExchangeOrder.distributorId = row.distributorId
        this.createExchangeOrder.excludeCommoditySkuIdList = cloneDeep(this.excludeCommoditySkuIdList)
        if (this.createExchangeOrder.distributorId) {
          this.mutationRequestEntrance = new MutationCreateExchangeOrder()
          this.mutationRequestEntrance.orderNo = this.createExchangeOrder.orderNo || ''
          this.mutationRequestEntrance.subOrderNo = this.createExchangeOrder.subOrderNo || ''
          await this.exchangeDistributorTrainClassRef.searchByExternal(this.createExchangeOrder)
          this.uiConfig.dialog.replaceDistributorIdTrainClassVisible = true
        } else {
          await this.exchangeTrainClassRef.searchByExternal(this.createExchangeOrder)
          this.uiConfig.dialog.replaceTrainClassVisible = true
        }
      } catch (e) {
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }

    /**
     * 继续换班
     */
    @bind
    @debounce(200)
    async continueExchangeTrainClass(row: ExchangeOrderRecordDetailVo) {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        const exchangeStatus = row.remote?.basicData?.status ?? null
        const queryRemote = TradeModule.singleTradeBatchFactor.mutationFactory.getMutationExchangeOrder()
        queryRemote.exchangeOrderNo = row.remote.exchangeOrderNo ?? ''
        // 退货失败 => 继续退货
        if (exchangeStatus === 3) {
          const response = await queryRemote.retryRecycleResouce()
          if (response?.isSuccess()) {
            this.$message.success('操作成功！')
            await this.handleReloadData()
          }
        }
        // 发货失败 => 继续发货
        if (exchangeStatus === 6) {
          const response = await queryRemote.retryDelivery()
          if (response?.isSuccess()) {
            this.$message.success('操作成功！')
            await this.handleReloadData()
          }
        }
      } catch (e) {
        // console.log(e)
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }

    /**
     * 响应刷新事件
     */
    async handleReloadData() {
      // console.log('重新加载')
      await this.resetWaitExchangeQueryParams()
      await this.resetExchangedRecordsQueryParams()
    }

    // 待更换班级获取属性
    getPrototype(val: WaitExchangeTrainClassDetailVo) {
      let arrList = new Array<any>()
      if (val && val.industry) arrList.push(val.industry)
      if (val && val.year) arrList.push(val.year + '年')
      if (val && val.industryId == 'industry0221018501809dc4d43e0002') {
        arrList.push(val.subjectType)
        arrList.push(val.trainingCategory)
        arrList.push(val.trainingProfessional)
      }
      if (val && val.industryId == 'industry0221018501809dc4d43e0003') {
        arrList.push(val.subjectType)
        arrList.push(val.trainingProfessional)
      }
      if (val && val.industryId == 'industry0221018501809dc4d43e0004') {
        arrList.push(val.trainingCategory)
        arrList.push(val.trainingObject)
        arrList.push(val.positionCategory)
      }
      if (val && val.industryId == 'industry0221018501809dc4d43e0005') {
        arrList.push(val.jobLevel)
      }
      // 教师行业
      if (val && val.industryId == 'industry0221018501809dc4d43e0006') {
        arrList.push(val.grade)
        arrList.push(val.subject)
      }
      if (!arrList.length) return
      arrList = arrList.filter((item) => {
        // 这里检查item是否为非空字符串，即它既不是空字符串、null，也不是undefined
        return typeof item === 'string' && item.trim().length > 0
      })
      return '【' + arrList.join(' / ') + '】'
    }

    //换班记录获取属性
    getChangeClassPrototype(val: SkuPropertyResponseVo) {
      const arrList = new Array<any>()
      if (!val) return
      if (val.industry && val.industry.skuPropertyName) arrList.push(val.industry.skuPropertyName)
      if (val.year && val.year.skuPropertyName) arrList.push(val.year.skuPropertyName + '年')
      if (val.subjectType && val.subjectType.skuPropertyName) arrList.push(val.subjectType.skuPropertyName)
      if (val.trainingMajor && val.trainingMajor.skuPropertyName) arrList.push(val.trainingMajor.skuPropertyName)
      if (val.trainingCategory && val.trainingCategory.skuPropertyName)
        arrList.push(val.trainingCategory.skuPropertyName)
      if (val.trainingObject && val.trainingObject.skuPropertyName) arrList.push(val.trainingObject.skuPropertyName)
      if (val.positionCategory && val.positionCategory.skuPropertyName)
        arrList.push(val.positionCategory.skuPropertyName)
      if (val.jobLevel && val.jobLevel.skuPropertyName) arrList.push(val.jobLevel.skuPropertyName)
      if (val.learningPhase && val.learningPhase.skuPropertyName) arrList.push(val.learningPhase.skuPropertyName)
      if (val.discipline && val.discipline.skuPropertyName) arrList.push(val.discipline.skuPropertyName)

      if (!arrList.length) return
      return '【' + arrList.join(' / ') + '】'
    }
    /**
     * 确认换班
     */
    async confirmExchangeTrainClass(commoditySkuId: string, pricingSchemeId?: string, schemeType?: SchemeTypeEnum) {
      if (this.isTrainCooperate(schemeType)) {
        return this.$confirm('选择的班级参与合作办学，暂不支持换班', '提示', {
          confirmButtonText: '我知道了',
          showCancelButton: false,
          showClose: false
        })
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      this.mutationRequestEntrance.commoditySkuId = commoditySkuId
      this.mutationRequestEntrance.exchangePricingPolicyId = pricingSchemeId
      try {
        const status = await this.mutationRequestEntrance.sellerApplyExchange()
        if (status.isSuccess()) {
          this.$message.success('操作成功')
          this.handleReloadData()
          this.uiConfig.dialog.replaceDistributorIdTrainClassVisible = false
        } else {
          if (status.code == 3002) {
            this.$message.error('定价方案已停用，无法更换班级')
          } else if (status.code == 3005) {
            this.$message.error('培训方案已下架，无法更换班级')
          } else if (status.code == 3006) {
            this.$message.error('分销商品周期已失效，无法更换班级')
          } else if (status.code == 3007) {
            this.$message.error('该班级已经有存在报名的订单，无法换班。')
          } else if (status.code == 3008) {
            //TODO 待定
            this.$message.error('因当前订单正在办理退货/款业务，不能进行换班')
          } else if (status.code == 3009) {
            //TODO 待定
            this.$message.error('因当前订单已办理部分退款，系统暂不支持课程更换服务')
          } else if ([99996, 99997, 99998, 99999, 30000, 30001, 30002].includes(Number(status.code))) {
            this.$message.error('不允许换班')
          } else {
            this.$message.error(status?.message.toString() || status?.errors?.[0]?.message || '操作失败')
          }
        }
      } catch (e) {
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }
  }
</script>
