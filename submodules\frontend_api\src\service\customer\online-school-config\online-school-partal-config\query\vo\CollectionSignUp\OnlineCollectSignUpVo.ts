import {
  OnlineCollectiveRegisterConfigResponse,
  OnlineCollectiveRegisterConfigSaveRequest
} from '@api/ms-gateway/ms-servicer-series-v1'
import CollectSignUpVo from './CollectSignUpVo'

class OnlineCollectSignUpVo extends CollectSignUpVo {
  from(res: OnlineCollectiveRegisterConfigResponse) {
    this.enable = res.enabled
    this.templatePath = res.templatePath
    this.templateName = res.templateFileName
    this.signUpClassUrl = `${location.protocol}//${location.host}/collective-registry`
  }

  to() {
    const request = new OnlineCollectiveRegisterConfigSaveRequest()
    request.enabled = this.enable
    request.templatePath = this.templatePath
    request.templateFileName = this.templateName
    return request
  }
}
export default OnlineCollectSignUpVo
