<template>
  <el-drawer
    title="补开发票"
    :visible.sync="dialogVisible"
    size="800px"
    custom-class="m-drawer"
    :before-close="drawerClose"
  >
    <div class="drawer-bd">
      <el-form
        ref="modifyInvoiceForm"
        :model="batchInvoiceParam"
        :rules="offlineInvoiceRules"
        label-width="150px"
        class="m-form f-mt10"
      >
        <el-form-item label="发票类型：" class="is-text">
          <el-radio-group v-model="batchInvoiceParam.invoiceCategory" @change="$forceUpdate()">
            <el-radio :label="2" v-if="invoiceMethod == 1">增值税电子普通发票（自动开票）</el-radio>
            <el-radio :label="2" v-if="invoiceMethod == 2">增值税电子普通发票（线下开票）</el-radio>
            <el-radio :label="3">增值税专用发票</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发票抬头：" prop="title">
          <el-radio-group v-model="batchInvoiceParam.titleType" @change="changeTitleType">
            <!-- <el-radio class="f-mb15" :label="1">
              <span class="f-mr10">个人</span>
            </el-radio> -->
            <el-radio :label="2">
              <span class="f-mr10">单位</span>
            </el-radio>
          </el-radio-group>
          <el-input v-model="batchInvoiceParam.title" clearable placeholder="请输入抬头" />
        </el-form-item>
        <el-form-item
          label="统一社会信用代码："
          prop="taxpayerNo"
          ref="taxpayerNoRef"
          v-if="batchInvoiceParam.titleType == 2"
        >
          <el-input
            v-model="batchInvoiceParam.taxpayerNo"
            @input="upperCase"
            clearable
            placeholder="请输入18位统一社会信用代码"
            class="form-l"
          />
        </el-form-item>
        <el-form-item label=" " class="is-text" v-show="batchInvoiceParam.titleType == 2">
          <span class="f-co">注：开企业抬头发票须填写统一社会信用代码，以免影响报销。</span>
        </el-form-item>
        <div class="bg-gray f-plr20 f-mb20 f-pb10" v-show="batchInvoiceParam.titleType == 2">
          <div class="f-pt5 f-pb10">
            <el-divider content-position="left" v-if="batchInvoiceParam.invoiceCategory == 2">
              <span class="f-cr">* 以下内容请根据需要填写，请全部填写或者全部不填写。</span>
            </el-divider>
          </div>
          <el-form-item label="开户银行：">
            <el-input v-model="batchInvoiceParam.bankName" clearable placeholder="请输入开户银行" class="form-l" />
          </el-form-item>
          <el-form-item label="开户帐号：">
            <el-input v-model="batchInvoiceParam.account" clearable placeholder="请输入开户帐号" class="form-l" />
          </el-form-item>
          <el-form-item label="注册电话：">
            <el-input
              v-model="batchInvoiceParam.phone"
              clearable
              maxlength="20"
              show-word-limit
              placeholder="请输入单位注册电话"
              class="form-m"
            />
          </el-form-item>
          <el-form-item label="注册地址：">
            <el-input
              v-model="batchInvoiceParam.address"
              clearable
              maxlength="100"
              show-word-limit
              placeholder="请输入单位注册地址"
            />
          </el-form-item>
<!--          <el-form-item-->
<!--            label="营业执照："-->
<!--            prop="businessLicenseUrl"-->
<!--            ref="businessLicensePicture"-->
<!--            v-if="batchInvoiceParam.invoiceCategory == InvoiceCategoryEnum.VATSPECIALPLAININVOICE"-->
<!--          >-->
<!--            <upload-images v-model="businessLicenseUrlList" :limit="1"></upload-images>-->
<!--          </el-form-item>-->
<!--          <el-form-item-->
<!--            label="开户许可证："-->
<!--            prop="permitUrl"-->
<!--            ref="permitPicture"-->
<!--            v-if="batchInvoiceParam.invoiceCategory == InvoiceCategoryEnum.VATSPECIALPLAININVOICE"-->
<!--          >-->
<!--            <upload-images v-model="permitUrlList" :limit="1"></upload-images>-->
<!--          </el-form-item>-->
        </div>
        <el-form-item
          label="手机号："
          prop="contactPhone"
          v-if="invoiceMethod == 2 && batchInvoiceParam.invoiceCategory == 2"
        >
          <el-input
            v-model="batchInvoiceParam.contactPhone"
            clearable
            placeholder="请输入11位手机号码"
            class="form-l"
          />
        </el-form-item>
        <el-form-item
          label="电子邮箱："
          prop="contactEmail"
          v-if="invoiceMethod == 2 && batchInvoiceParam.invoiceCategory == 2"
        >
          <el-input v-model="batchInvoiceParam.contactEmail" clearable placeholder="请输入电子邮箱" class="form-l" />
        </el-form-item>
      </el-form>
      <div class="m-tit" v-if="batchInvoiceParam.invoiceCategory == 3">
        <span class="tit-txt">配送信息</span>
      </div>
      <el-form
        label-width="130px"
        :model="courierInfo"
        :rules="rules"
        ref="formRef"
        v-if="batchInvoiceParam.invoiceCategory == 3"
        v-show="courierInfo.expressWay === 1"
      >
        <div class="f-ml30 u-w-form">
          <!--邮寄-->
          <el-form-item label="配送方式" required>
            <el-radio-group size="medium" v-model="courierInfo.expressWay">
              <el-radio v-for="(item, index) in detailList" :key="index" :label="item.code">{{ item.desc }}</el-radio>
            </el-radio-group>
            <div class="f-ci">{{ remark }}</div>
          </el-form-item>
          <el-form-item label="收件人" prop="name">
            <el-input placeholder="请填写收件人姓名" v-model="courierInfo.name"></el-input>
          </el-form-item>
          <el-form-item label="手机号码" prop="phone">
            <el-input placeholder="请填写收件人手机号码" maxlength="11" v-model="courierInfo.phone"></el-input>
          </el-form-item>
          <el-form-item label="所在地区" prop="region">
            <region-select v-model="courierInfo.region"></region-select>
          </el-form-item>
          <el-form-item label="详细地址" prop="detailedRegion">
            <el-input type="textarea" placeholder="请填写详细地址" v-model="courierInfo.detailedRegion"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <el-form label-width="130px" v-if="batchInvoiceParam.invoiceCategory == 3" v-show="courierInfo.expressWay === 2">
        <el-form-item label="配送方式" required>
          <el-radio-group size="medium" v-model="courierInfo.expressWay">
            <el-radio v-for="(item, index) in detailList" :key="index" :label="item.code">{{ item.desc }}</el-radio>
          </el-radio-group>
          <!-- <div class="f-ci">您选择邮寄到付，快递费用由收件人自行支付。</div> -->
        </el-form-item>
        <div>
          <div
            v-for="(item, index) in pickUpList"
            :key="index"
            class="take-address"
            @click="choosePickUp(index)"
            :class="index == mark ? 'is-checked' : ''"
          >
            <div class="label"><i class="hb-iconfont icon-finish"></i></div>
            <el-form-item label="领取时间" class="is-text">
              <div class="con">{{ item.openTakeTime }}</div>
            </el-form-item>
            <el-form-item label="领取地点" class="is-text">
              <div class="con">{{ item.address }}</div>
            </el-form-item>
            <el-form-item label="备注" class="is-text">
              <div class="con">{{ item.remark }}</div>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="drawerClose">取消</el-button>
      <el-button type="primary" @click="commitApply">保存发票信息</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import {
    InvoiceTypeEnum,
    TitleTypeEnum,
    InvoiceCategoryEnum
  } from '@api/service/management/trade/batch/invoice/enum/InvoiceEnum'
  import RegionSelect from '@hbfe/jxjy-admin-components/src/national-region/national-region-cascader.vue'
  import InvoiceDetail from '@api/service/management/trade/batch/invoice/mutation/dto/InvoiceDetail'
  import UploadImages from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/upload-images.vue'
  import OffLinePageInvoiceResponseVo from '@api/service/management/trade/batch/invoice/query/vo/OffLinePageInvoiceResponseVo'
  import NationalRegionCascader from '@hbfe/jxjy-admin-components/src/national-region/national-region-cascader.vue'
  import TradeConfigModule from '@api/service/common/trade-config/TradeConfigModule'
  import { Form } from 'element-ui'
  import BatchInvoiceParam from '@api/service/centre/trade/batch/invoice/mutation/vo/BatchInvoiceParam'
  import QueryDeliverWayTypeList from '@api/service/common/trade-config/query/QueryDeliverWayTypeList'
  import QueryDeliverWayList from '@api/service/common/trade-config/query/QueryDeliverWayList'

  import { BatchInvoiceRequest, DeliveryAddress, TakePoint } from '@api/ms-gateway/ms-order-v1'
  import { ElForm } from 'element-ui/types/form'
  import QueryStudentDetail from '@api/service/management/user/query/student/QueryStudentDetail'
  import TakePlaceDetailVo from '@api/service/management/online-school-config/distribution-channels-config/query/vo/TakePlaceDetailVo'
  class DistrButionData {
    distributionConfig: number
    deliveryAddressRemark: string
    consignee: string
    deliveryAddressPhone: string
    region: string
    address: string
    pickupLocation: string
    pickupTime: string
    takePointRemark?: string
  }
  export class UploadImageFile {
    name: string
    url: string
  }
  enum ExpressWayEnum {
    /**
     * 邮件到付
     */
    MAILTOPAY = 'MAILTOPAY',
    /**
     * 自取
     */
    INVITE = 'INVITE'
  }

  @Component({
    components: { NationalRegionCascader, UploadImages, RegionSelect }
  })
  export default class extends Vue {
    @Ref('modifyInvoiceForm') modifyInvoiceForm: ElForm
    @Ref('deliveryForm') deliveryForm: ElForm
    @Ref('courierFormRef') courierFormRef: ElForm

    @Prop({
      type: Boolean,
      default: false
    })
    dialogCtrl: boolean

    @Prop({
      type: String,
      default: '2'
    })
    invoiceType: string

    @Prop({
      type: String,
      default: ''
    })
    batchOrderNo = ''

    @Prop({
      type: String,
      default: ''
    })
    userId = ''

    invoiceMapType = {
      ['1']: '增值税电子普通发票（自动开票）',
      ['2']: '增值税电子发票（线下开票）'
    }
    InvoiceCategoryEnum = InvoiceCategoryEnum
    /**
     * 配置信息
     */
    courierInfo = {
      name: '',
      phone: '',
      region: ['', ''],
      detailedRegion: '',
      expressWay: 1 // 根据接口返回赋初始值
    }

    takePoint = new TakePoint()
    distribution = new DistrButionData()

    remark = ''
    QueryDeliverWayList = new QueryDeliverWayList()
    QueryDeliverWayTypeList = new QueryDeliverWayTypeList()
    //校验规则
    rules = {
      name: [{ required: true, message: '请填写收件人姓名', trigger: 'blur' }],
      phone: [
        { required: true, message: '请填写11位手机号码', trigger: 'blur' },
        { min: 11, max: 11, message: '请填写11位手机号码', trigger: 'blur' }
      ],
      region: [{ required: true, message: '请填写所在地区', trigger: ['change', 'blur'] }],
      detailedRegion: [{ required: true, message: '请填写详细地址', trigger: 'blur' }]
    }
    mark = 0
    area = ''
    pickUpList: Array<TakePlaceDetailVo> = new Array<TakePlaceDetailVo>()
    detailList = {}

    /**
     * 保存信息展示地区名字
     */
    region = ''
    /**
     * 保存信息控制按钮
     */
    infoKey = false
    batchInvoiceParam = new BatchInvoiceParam()

    getQueryTradeConfig = TradeConfigModule.queryTradeConfigFactory.getQueryTradeConfig()
    // businessLicenseUrlList: Array<UploadImageFile> = new Array<UploadImageFile>()
    // permitUrlList: Array<UploadImageFile> = new Array<UploadImageFile>()
    //专票
    specialTicket = false
    //普通票
    ordinaryTicket = false
    //开票方式：线上1，线下2
    invoiceMethod = 1
    hasConfig = 0

    invoiceTitleMapType = {
      // [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }

    checkPhone = (rule: any, value: any, callback: (arg?: Error) => void) => {
      const reg = /^[\d]{11}$/
      if (value) {
        if (!reg.test(value)) {
          return callback(new Error('请输入11位手机号码'))
        } else {
          return callback()
        }
      }
    }

    //自动开票校验规则
    autoInvoiceRules = {
      title: [
        {
          required: true,
          message: '请填写抬头',
          trigger: ['change', 'blur']
        }
      ],
      taxpayerNo: [
        {
          required: true,
          message: '请输入统一社会信用代码',
          trigger: ['change', 'blur']
        }
      ]
    }

    //线下开票校验规则
    offlineInvoiceRules = {
      invoiceTitle: [
        {
          required: true,
          message: '请填写抬头',
          trigger: ['change', 'blur']
        }
      ],
      taxpayerNo: [
        {
          required: true,
          message: '请输入统一社会信用代码',
          trigger: ['change', 'blur']
        }
      ],
      contactPhone: [
        {
          required: true,
          message: '请输入手机号码',
          trigger: ['change', 'blur']
        },
        {
          validator: this.checkPhone,
          trigger: ['change', 'blur']
        }
      ],
      contactEmail: [
        {
          required: true,
          message: '请输入电子邮箱',
          trigger: ['change', 'blur']
        }
      ]
    }

    rulesMapType = {
      [InvoiceTypeEnum.ONLINE]: this.autoInvoiceRules,
      [InvoiceTypeEnum.OFFLINE]: this.offlineInvoiceRules
    }

    dialogVisible = false

    @Watch('dialogCtrl')
    changeDialogCtrl() {
      if (this.dialogCtrl) {
        console.log(11)
      }
      this.dialogVisible = this.dialogCtrl
    }
    @Watch('batchOrderNo')
    changebatchOrderNo() {
      console.log(this.batchOrderNo)
    }
    @Watch('dialogVisible')
    changeDialogVisible() {
      this.$emit('update:dialogCtrl', this.dialogVisible)
    }
    // @Watch('businessLicenseUrlList', {
    //   deep: true
    // })
    // changeBusinessLicenseUrl(val: any) {
    //   const el: any = this.$refs['businessLicensePicture']
    //   if (el) {
    //     if (val.length) {
    //       this.batchInvoiceParam.businessLicensePath = val[0].url
    //       el.clearValidate()
    //     } else {
    //       this.batchInvoiceParam.businessLicensePath = null
    //       el.validate()
    //     }
    //   }
    // }
    @Watch('distribution', {
      deep: true
    })
    changedistribution(val: any) {
      console.log(this.distribution)
    }

    @Watch('userId')
    async userIdChange(val: any) {
      if (this.userId) {
        const el: any = this.$refs['businessLicensePicture']

        const queryStudentDetail = new QueryStudentDetail(this.userId)
        const userInfo = await queryStudentDetail.queryDetail()
        this.$set(this.batchInvoiceParam, 'contactPhone', userInfo?.data?.phone)
        // this.batchInvoiceParam.contactPhone = userInfo?.data?.phone
        // el.clearValidate()
      }
    }

    // @Watch('permitUrlList', {
    //   deep: true
    // })
    // changePermitUrl(val: any) {
    //   const el: any = this.$refs['permitPicture']
    //   if (el) {
    //     if (val.length) {
    //       this.batchInvoiceParam.accountOpeningLicensePath = val[0].url
    //       el.clearValidate()
    //     } else {
    //       this.batchInvoiceParam.accountOpeningLicensePath = null
    //       el.validate()
    //     }
    //   }
    // }

    changeTitleType() {
      const el: any = this.$refs['taxpayerNoRef']
      if (el) {
        if (this.batchInvoiceParam.titleType == 1) {
          el.clearValidate()
        } else {
          el.validate()
        }
      }
    }

    upperCase() {
      const arr = this.batchInvoiceParam.taxpayerNo.split('')
      let newStr = ''
      arr.forEach((value) => {
        if (value >= 'a' && value <= 'z') {
          newStr += value.toUpperCase()
        } else {
          newStr += value
        }
      })
      this.batchInvoiceParam.taxpayerNo = newStr
    }

    doSave() {
      this.modifyInvoiceForm.validate((valid: boolean) => {
        this.deliveryForm.validate((deliveryValid: boolean) => {
          if (valid && deliveryValid && this.validUnitInfo()) {
            this.doModify()
          }
        })
      })
      this.modifyInvoiceForm.validate((valid: boolean) => {
        if (valid) {
          this.doModify()
        }
      })
    }

    async doModify() {
      if (this.batchInvoiceParam.invoiceCategory == 3) {
        this.batchInvoiceParam.invoiceMethod = 2
        // 工勤专票只有纸质票
        this.batchInvoiceParam.invoiceType = 2
      } else {
        this.batchInvoiceParam.invoiceType = 1
        this.batchInvoiceParam.invoiceMethod = this.invoiceMethod
      }
      //差异化
      this.batchInvoiceParam.invoiceVerifyStrategy = 2
      const res = await TradeModule.batchTradeBatchFactor.invoiceFactor.mutationInvoice.applyInoice(
        this.batchOrderNo,
        this.batchInvoiceParam
      )
      if (res.code == 200) {
        this.$message.success('补开发票成功')
        this.dialogVisible = false
        this.$emit('callBack')
      } else {
        this.$message.error(res.errors[0]?.message || '修改发票失败')
      }
    }

    //四个全填和全不填的以及格式校验
    validUnitInfo() {
      const titleReg = /^[0-9a-zA-Z\u4e00-\u9fa5（）()《》—-]+$/
      const taxpayerNoReg = /^[A-Za-z0-9]{18}$/
      if (
        this.batchInvoiceParam.titleType === TitleTypeEnum.UNIT &&
        (this.batchInvoiceParam.invoiceCategory === InvoiceCategoryEnum.VATSPECIALPLAININVOICE ||
          this.batchInvoiceParam.bankName ||
          this.batchInvoiceParam.account ||
          this.batchInvoiceParam.phone ||
          this.batchInvoiceParam.address)
      ) {
        if (!this.batchInvoiceParam.bankName) {
          this.$message.warning('请填写开户银行')
          return false
        } else if (!this.batchInvoiceParam.account) {
          this.$message.warning('请填写开户账号')
          return false
        } else if (!this.batchInvoiceParam.phone) {
          this.$message.warning('请填写注册电话')
          return false
        } else if (!this.batchInvoiceParam.address) {
          this.$message.warning('请填写注册地址')
          return false
        } else if (this.batchInvoiceParam.address.indexOf('·') != -1) {
          this.$message.warning('注册地址暂不支持特殊字符“ · ”')
          return false
        }
      }
      if (this.batchInvoiceParam.titleType === TitleTypeEnum.UNIT && !titleReg.test(this.batchInvoiceParam.title)) {
        this.$message.warning('单位名称特殊符号仅支持《》、—、-、（）')
        return false
      }
      if (
        this.batchInvoiceParam.titleType === TitleTypeEnum.UNIT &&
        !taxpayerNoReg.test(this.batchInvoiceParam.taxpayerNo)
      ) {
        this.$message.warning('请输入正确的18位统一社会信用代码')
        return false
      }
      return true
    }

    choosePickUp(index: number) {
      this.mark = index
      this.takePoint.pickupLocation = this.pickUpList[index].address
      this.takePoint.pickupTime = this.pickUpList[index].openTakeTime
      this.takePoint.remark = this.pickUpList[index].remark
    }
    //修改发票信息
    commitApply() {
      if (this.batchInvoiceParam.invoiceCategory === 3) {
        if (this.courierInfo.expressWay == 1) {
          this.batchInvoiceParam.shippingMethod = 2
          this.batchInvoiceParam.deliveryAddress.consignee = this.courierInfo.name
          this.batchInvoiceParam.deliveryAddress.phone = this.courierInfo.phone
          if (this.courierInfo.region) {
            const id = '/' + this.courierInfo.region.join('/')
            this.batchInvoiceParam.deliveryAddress.region = id
          }
          this.batchInvoiceParam.deliveryAddress.address = this.courierInfo.detailedRegion
          this.batchInvoiceParam.takePoint = new TakePoint()
        } else {
          this.batchInvoiceParam.shippingMethod = 1
          this.batchInvoiceParam.takePoint = this.takePoint
          this.batchInvoiceParam.deliveryAddress = new DeliveryAddress()
        }
      } else {
        this.batchInvoiceParam.shippingMethod = 0
      }

      this.modifyInvoiceForm.validate((valid: boolean) => {
        if (valid && this.validUnitInfo()) {
          this.doModify()
        }
      })
    }

    async created() {
      await this.QueryDeliverWayList.queryList()
      await this.QueryDeliverWayTypeList.query()
      const res = await this.getQueryTradeConfig.hasBatchOpenInvoice()

      const special = res.allowInvoiceCategoryList.find((item) => {
        return item.invoiceCategory == 2
      })
      this.invoiceMethod = special.invoiceMethod
      if (special.invoiceCategory == 3) {
        this.specialTicket = true
      }
      const ordinary = res.allowInvoiceCategoryList.find((item) => {
        return item.invoiceCategory == 2
      })
      if (ordinary.invoiceCategory == 2) {
        this.ordinaryTicket = true
      }

      this.init()
    }

    init() {
      const hasConfig = this.QueryDeliverWayList.hasConfig()

      if (hasConfig) {
        this.remark = this.QueryDeliverWayTypeList?.expressList[0]?.remark || ''
        this.pickUpList = this.QueryDeliverWayTypeList.pickUpList
        if (this.pickUpList.length) {
          this.takePoint.pickupLocation = this.pickUpList[0].address
          this.takePoint.pickupTime = this.pickUpList[0].openTakeTime
          this.takePoint.remark = this.pickUpList[0].remark
        }
        this.detailList = this.QueryDeliverWayList.detailList
        this.courierInfo.expressWay = this.detailList[0].code
      }

      this.dialogVisible = this.dialogCtrl
      this.batchInvoiceParam.titleType = 2
      this.batchInvoiceParam.invoiceCategory = 2
    }

    drawerClose() {
      //    @Ref('modifyInvoiceForm') modifyInvoiceForm: ElForm
      // @Ref('deliveryForm') deliveryForm: ElForm
      // @Ref('courierFormRef') courierFormRef: ElForm
      this.courierInfo = {
        name: '',
        phone: '',
        region: ['', ''],
        detailedRegion: '',
        expressWay: 1 // 根据接口返回赋初始值
      }
      this.batchInvoiceParam = new BatchInvoiceParam()
      // this.businessLicenseUrlList = new Array<UploadImageFile>()
      // this.permitUrlList = new Array<UploadImageFile>()

      this.init()

      this.dialogVisible = false
    }
  }
</script>
