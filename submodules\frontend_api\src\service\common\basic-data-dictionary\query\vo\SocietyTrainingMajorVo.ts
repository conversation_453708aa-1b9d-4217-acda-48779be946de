import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
import TrainingMajorVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingMajorVo'

/**
 * @description 【人社行业】培训专业模型
 */
class SocietyTrainingMajorVo extends TrainingCategoryVo {
  children: Array<TrainingMajorVo>
  // 是否为叶子节点
  leaf = false

  get optionName() {
    return this.showName ? this.name + '（' + this.showName + '）' : this.name
  }

  constructor() {
    super()
    this.children = undefined
  }
}

export default SocietyTrainingMajorVo
