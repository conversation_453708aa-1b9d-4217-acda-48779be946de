import BasicData from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
import BasicDataGateway, {
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'

/**
 * 运营域行业查询接口
 */
class QueryOperationIndustry {
  /**
   * 行业类型缓存
   */
  IndustryCache = new Array<IndustryVo>()

  /**
   * 科目类型缓存
   */
  private subjectCache = new Map<string, Array<TrainingPropertyResponse>>()

  /**
   * 专业类型缓存
   */
  private trainingCache = new Map<string, Array<TrainingPropertyResponse>>()

  /**
   * 具体专业信息缓存
   */
  private trainingDetailCache = new Map<string, Array<TrainingPropertyResponse>>()

  /**
   * 培训对象缓存
   */
  private trainingObjectCache = new Map<string, Array<TrainingPropertyResponse>>()

  /**
   * 岗位类别缓存
   */
  private positionCategoryCache = new Map<string, Array<TrainingPropertyResponse>>()

  /**
   * 技术等级缓存
   */
  private jobLevelCache = new Map<string, Array<TrainingPropertyResponse>>()

  /**
   * 技术工种缓存
   */
  private jobCategoryCache = new Map<string, Array<TrainingPropertyResponse>>()
  /**
   * 学段缓存
   */
  private sectionCache = new Map<string, Array<TrainingPropertyResponse>>()
  /**
   * 学科缓存
   */
  private subjectsCache = new Map<string, Array<TrainingPropertyResponse>>()
  /**
   * 证书类型缓存
   */
  private certificatesTypeCache = new Map<string, Array<TrainingPropertyResponse>>()
  /**
   * 执业类别缓存
   */
  private practitionerCategoryCache = new Map<string, Array<TrainingPropertyResponse>>()
  /**
   * 运营域超管根据字典 获取行业类型（过滤掉交通运输行业类型）
   */
  async getOperationIndustry() {
    if (!this.IndustryCache.length) {
      const response = await BasicData.listBusinessDataDictionaryInSubProject({
        businessDataDictionaryType: 'INDUSTRY'
      })
      const result = new Array<IndustryVo>()
      if (response.status.isSuccess()) {
        // 过滤掉交通运输行业
        response.data = response.data.filter(item => {
          return item.id != 'industry0221018501809dc4d43e0001'
        })
        response.data.map(item => {
          const industryInfo = new IndustryVo()
          industryInfo.id = item.id
          industryInfo.sort = item.sort
          industryInfo.name = item.name
          result.push(industryInfo)
        })
      }
      this.IndustryCache = result
    }
    return this.IndustryCache
  }

  /**
   * 获取指定分类列表
   */
  getAppointIndustry(id: string) {
    return this.IndustryCache.filter(industry => {
      return industry.id === id
    })
  }
  /**
   * 获取指定分类列表 通过名称
   */
  getAppointIndustryProperty(industry: string) {
    return BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustry.industryList.filter(item => {
      return item.id === industry
    })
  }

  /**
   * 查询科目类型列表
   * @param id
   */
  async getOperationSubjectList(id: string) {
    if (!this.subjectCache.get(id)) {
      if (this.getAppointIndustryProperty(id)[0]?.propertyId) {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategoryV2({
          industryId: id,
          industryPropertyId: this.getAppointIndustryProperty(id)[0]?.propertyId,
          categoryCode: IndustryPropertyCodeEnum.SUBJECT_TYPE
        })
        this.subjectCache.set(id, data)
      } else {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategory({
          industryId: id,
          categoryCode: IndustryPropertyCodeEnum.SUBJECT_TYPE
        })
        this.subjectCache.set(id, data)
      }
    }

    return this.subjectCache.get(id)
  }

  /**
   * 查询专业类型列表
   */
  async getOperationTraining(id: string) {
    if (!this.trainingCache.get(id)) {
      if (this.getAppointIndustryProperty(id)[0]?.propertyId) {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategoryV2({
          industryId: id,
          industryPropertyId: this.getAppointIndustryProperty(id)[0]?.propertyId,
          categoryCode: IndustryPropertyCodeEnum.TRAINING_CATEGORY
        })
        this.trainingCache.set(id, data)
      } else {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategory({
          industryId: id,
          categoryCode: IndustryPropertyCodeEnum.TRAINING_CATEGORY
        })
        this.trainingCache.set(id, data)
      }
    }
    return this.trainingCache.get(id)
  }

  /**
   * 查询类型具体专业
   */
  async getIndustryDetail(id: string) {
    // if (!this.trainingDetailCache.get(id)) {
    // if (this.getAppointIndustryProperty(id)[0]?.propertyId) {
    //   const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategoryV2({
    //     industryId: id,
    //     industryPropertyId:
    //       this.getAppointIndustryProperty(id)[0]?.propertyId,
    //     categoryCode: IndustryPropertyCodeEnum.TRAINING_PROFESSIONAL
    //   })
    //   this.trainingDetailCache.set(id, data)
    // } else {
    const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategory({
      industryId: id,
      categoryCode: IndustryPropertyCodeEnum.TRAINING_PROFESSIONAL
    })
    this.trainingDetailCache.set(id, data)
    // }
    // }
    return this.trainingDetailCache.get(id)
  }

  /**
   * 查询培训对象
   */
  async getTrainingObject(id: string) {
    if (!this.trainingObjectCache.get(id)) {
      if (this.getAppointIndustryProperty(id)[0]?.propertyId) {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategoryV2({
          industryId: id,
          industryPropertyId: this.getAppointIndustryProperty(id)[0]?.propertyId,
          categoryCode: IndustryPropertyCodeEnum.TRAINING_OBJECT
        })
        this.trainingObjectCache.set(id, data)
      } else {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategory({
          industryId: id,
          categoryCode: IndustryPropertyCodeEnum.TRAINING_OBJECT
        })
        this.trainingObjectCache.set(id, data)
      }
    }

    return this.trainingObjectCache.get(id)
  }

  /**
   * 查询岗位类别
   */
  async getPositionCategory(id: string) {
    if (!this.positionCategoryCache.get(id)) {
      if (this.getAppointIndustryProperty(id)[0]?.propertyId) {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategoryV2({
          industryId: id,
          industryPropertyId: this.getAppointIndustryProperty(id)[0]?.propertyId,
          categoryCode: IndustryPropertyCodeEnum.POSITION_CATEGORY
        })
        this.positionCategoryCache.set(id, data)
      } else {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategory({
          industryId: id,
          categoryCode: IndustryPropertyCodeEnum.POSITION_CATEGORY
        })
        this.positionCategoryCache.set(id, data)
      }
    }
    return this.positionCategoryCache.get(id)
  }

  /**
   * 查询技术等级
   */
  async getJobLevel(id: string) {
    if (!this.jobLevelCache.get(id)) {
      if (this.getAppointIndustryProperty(id)[0]?.propertyId) {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategoryV2({
          industryId: id,
          industryPropertyId: this.getAppointIndustryProperty(id)[0]?.propertyId,
          categoryCode: IndustryPropertyCodeEnum.JOB_LEVEL
        })
        this.jobLevelCache.set(id, data)
      } else {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategory({
          industryId: id,
          categoryCode: IndustryPropertyCodeEnum.JOB_LEVEL
        })
        this.jobLevelCache.set(id, data)
      }
    }

    return this.jobLevelCache.get(id)
  }

  /**
   * 查询工种
   */
  async getJobCategory(id: string) {
    if (!this.jobCategoryCache.get(id)) {
      if (this.getAppointIndustryProperty(id)[0]?.propertyId) {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategoryV2({
          industryId: id,
          industryPropertyId: this.getAppointIndustryProperty(id)[0]?.propertyId,
          categoryCode: IndustryPropertyCodeEnum.JOB_CATEGORY
        })
        this.jobCategoryCache.set(id, data)
      } else {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategory({
          industryId: id,
          categoryCode: IndustryPropertyCodeEnum.JOB_CATEGORY
        })
        this.jobCategoryCache.set(id, data)
      }
    }

    return this.jobCategoryCache.get(id)
  }
  /**
   * 查询学段
   */
  async getSection(id: string) {
    if (!this.sectionCache.get(id)) {
      if (this.getAppointIndustryProperty(id)[0]?.propertyId) {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategoryV2({
          industryId: id,
          industryPropertyId: this.getAppointIndustryProperty(id)[0]?.propertyId,
          categoryCode: IndustryPropertyCodeEnum.LEARNING_PHASE
        })
        this.sectionCache.set(id, data)
      } else {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategory({
          industryId: id,
          categoryCode: IndustryPropertyCodeEnum.LEARNING_PHASE
        })
        this.sectionCache.set(id, data)
      }
    }
    return this.sectionCache.get(id)
  }
  /**
   * 查询学科
   */
  async getSubjects(id: string) {
    if (!this.subjectsCache.get(id)) {
      // if (this.getAppointIndustryProperty(id)[0]?.propertyId) {
      //   const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategoryV2({
      //     industryId: id,
      //     industryPropertyId:
      //       this.getAppointIndustryProperty(id)[0]?.propertyId,
      //     categoryCode: IndustryPropertyCodeEnum.DISCIPLINE
      //   })
      //   this.subjectsCache.set(id, data)
      // } else {
      const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategory({
        industryId: id,
        categoryCode: IndustryPropertyCodeEnum.DISCIPLINE
      })
      this.subjectsCache.set(id, data)
    }
    // }

    return this.subjectsCache.get(id)
  }
  /**
   * 查询证书类型
   */
  async getCertificatesType(id: string) {
    if (!this.certificatesTypeCache.get(id)) {
      if (this.getAppointIndustryProperty(id)[0]?.propertyId) {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategoryV2({
          industryId: id,
          industryPropertyId: this.getAppointIndustryProperty(id)[0]?.propertyId,
          categoryCode: IndustryPropertyCodeEnum.CERTIFICATES_TYPE
        })
        this.certificatesTypeCache.set(id, data)
      } else {
        const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategory({
          industryId: id,
          categoryCode: IndustryPropertyCodeEnum.CERTIFICATES_TYPE
        })
        this.certificatesTypeCache.set(id, data)
      }
    }
    return this.certificatesTypeCache.get(id)
  }
  /**
   * 查询执业类别
   */
  async getPractitionerCategory(id: string) {
    if (!this.practitionerCategoryCache.get(id)) {
      // if (this.getAppointIndustryProperty(id)[0]?.propertyId) {
      //   const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategoryV2({
      //     industryId: id,
      //     industryPropertyId:
      //       this.getAppointIndustryProperty(id)[0]?.propertyId,
      //     categoryCode: IndustryPropertyCodeEnum.DISCIPLINE
      //   })
      //   this.practitionerCategoryCache.set(id, data)
      // } else {
      const { data } = await BasicDataGateway.listALLIndustryPropertyRootByCategory({
        industryId: id,
        categoryCode: IndustryPropertyCodeEnum.PRACTITIONER_CATEGORY
      })
      this.practitionerCategoryCache.set(id, data)
    }
    // }

    return this.practitionerCategoryCache.get(id)
  }
}
export default new QueryOperationIndustry()
