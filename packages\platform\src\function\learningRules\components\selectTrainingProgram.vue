<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 16:12:23
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-04-15 09:05:54
 * @Description: 选择培训方案
-->
<template>
  <el-drawer
    title="选择培训方案"
    :visible.sync="openTrainSchemeDrawer"
    direction="rtl"
    size="1000px"
    custom-class="m-drawer"
  >
    <div class="drawer-bd">
      <el-row :gutter="16" class="m-query f-mt10">
        <el-form :inline="true" label-width="100px">
          <el-col :span="8">
            <el-form-item label="培训方案形式">
              <biz-scheme-type v-model="schemeTypeInfo"></biz-scheme-type>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="培训方案名称">
              <el-input clearable placeholder="请输入培训方案名称" v-model="queryParams.schemeName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button @click="search" type="primary">查询</el-button>
              <el-checkbox v-model="removeExistScheme" class="f-ml20">剔除已配置学习规则的方案</el-checkbox>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <!--表格-->
      <el-table stripe :data="allSchemeList" class="m-table" ref="schemeList" v-loading="loading">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="培训方案名称" min-width="280">
          <template slot-scope="scope">{{ scope.row.schemeName }}</template>
        </el-table-column>
        <el-table-column label="属性" min-width="220">
          <template slot-scope="scope">
            <sku-display :sku-item="scope.row"></sku-display>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="changeCheckStatus(scope.row)" v-if="!scope.row.hasAntiConfig">
              {{ isChecked(scope.row) ? '取消选择' : '选择' }}
            </el-button>
            <el-button v-else type="text" size="mini" disabled style="color: gray"> 已配置其他方案监管规则 </el-button>
            <!-- <el-checkbox-group v-model="schemeIdList">
              <el-checkbox :checked="changeCheckStatus(scope.row)">选择</el-checkbox>
            </el-checkbox-group> -->
            <!-- <el-checkbox-group v-model="idList" @change="changeShow">
              <el-checkbox :label="scope.row.id" :checked="itemIsSelected(scope.row.id)">选择</el-checkbox>
            </el-checkbox-group> -->
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
      <div class="m-btn-bar f-tc f-mt20">
        <el-button @click="openTrainSchemeDrawer = false">取消</el-button>
        <el-button @click="save" type="primary">确定</el-button>
      </div>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Prop, PropSync } from 'vue-property-decorator'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'

  import { UiPage } from '@hbfe/common'
  import { cloneDeep } from 'lodash'
  import BizIndustrySelect from '@hbfe/jxjy-admin-components/src/biz/biz-industry-select.vue'
  import RuleSchemeParams from '@api/service/management/train-class/query/vo/RuleSchemeParams'
  import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'
  import BasicInfo from '@api/service/management/learning-rule/model/BasicInfo'
  import SkuDisplay from '@hbfe/jxjy-admin-platform/src/function/components/skuDisplay.vue'

  @Component({
    components: { BizIndustrySelect, SkuDisplay }
  })
  export default class extends Vue {
    // 学习方案列表(传入已选)
    @PropSync('schemeList', {
      type: Array,
      default: () => Array<RuleSchemeItem>()
    })
    schemeItemList: Array<RuleSchemeItem>
    // 接收基础信息
    @Prop({
      type: BasicInfo,
      default: () => new BasicInfo()
    })
    basicInfo: BasicInfo

    // 是否打开抽屉
    openTrainSchemeDrawer = false
    // 加载动画
    loading = false
    /**
     * 培训方案类型（形式）
     */
    schemeTypeInfo: Array<string> = new Array<string>()
    // 查询方案列表方法
    queryTrainClassCommodityList = new QueryTrainClassCommodityList()
    // 方案名称
    schemeName = ''
    // 是否剔除已存在方案
    removeExistScheme = false
    // 方案列表
    allSchemeList = new Array<RuleSchemeItem>()
    // 选中的方案
    newSchemeItemList = new Array<RuleSchemeItem>()
    // 查询参数
    queryParams = new RuleSchemeParams()
    // 是否选择方案
    isCheck = false
    // 分页
    page: UiPage
    constructor() {
      super()
      this.page = new UiPage(this.querySchemeList, this.querySchemeList)
    }

    // async created() {
    //   this.querySchemeList()
    // }
    async init() {
      this.openTrainSchemeDrawer = true
      this.schemeTypeInfo = new Array<string>()
      this.querySchemeList()
      this.newSchemeItemList = cloneDeep(this.schemeItemList)
      console.log(this.basicInfo, 'basicInfo')
    }

    // 查询方案列表
    async querySchemeList() {
      this.configureTrainSchemeQueryParam()
      this.loading = true
      this.queryParams.industryId = this.basicInfo.industryId
      this.queryParams.isIncludeHasStudyRule = !this.removeExistScheme
      this.allSchemeList = await this.queryTrainClassCommodityList.pageRuleSchemeList(this.page, this.queryParams)
      this.loading = false
      ;(this.$refs['schemeList'] as any)?.doLayout()
    }

    // 查询
    async search() {
      this.page.pageNo = 1
      this.querySchemeList()
    }

    // 提交
    save() {
      this.$emit('isCheckIdList', this.newSchemeItemList)
      this.removeExistScheme = false
      this.openTrainSchemeDrawer = false
    }

    // 是否选中
    get isChecked() {
      return (item: RuleSchemeItem) => {
        const res = this.newSchemeItemList.map((scheme) => scheme.schemeId).indexOf(item.schemeId) > -1
        return res
      }
    }
    // 选中状态
    changeCheckStatus(item: RuleSchemeItem) {
      let index: number
      this.newSchemeItemList.map((ite, inde) => {
        if (ite.schemeId == item.schemeId) {
          index = inde
        }
      })

      if (index > -1) {
        this.newSchemeItemList.splice(index, 1)
      } else {
        this.newSchemeItemList.push(item)
      }
    }
    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: RuleSchemeItem, type: string): string {
      if (row.sku[type]?.skuPropertyName) {
        return row.sku[type].skuPropertyName

        // const valuesArr = value.split('/'),
        //   lastIndex = valuesArr.length - 1
        // return type === 'trainingMajor' ? valuesArr[lastIndex] : value
      }
      return ''
    }

    /**
     * 配置查询参数
     */
    configureTrainSchemeQueryParam() {
      // 处理培训方案类型
      if (!this.schemeTypeInfo.length) {
        this.queryParams.trainType = undefined
      }
      const schemeType = this.schemeTypeInfo[this.schemeTypeInfo.length - 1]
      console.log(schemeType, '培训方案类型')
      // if (schemeType === 'chooseCourseLearning' || schemeType === 'autonomousCourseLearning') {
      this.queryParams.trainType = schemeType
      // } else {
      //   this.queryParams.trainType = undefined
      // }
    }
  }
</script>
