function BasicData() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data.vue'
  )
}
function BasicDataAccount() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account.vue'
  )
}
function BasicDataAccountAdministratorAccountIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-administrator-account-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/administrator-account/index.vue'
  )
}
function BasicDataAccountDistributionAdministratorAccountIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-distribution-administrator-account-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/distribution-administrator-account/index.vue'
  )
}
function BasicDataAccountMonographicAccountIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-monographic-account-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/monographic-account/index.vue'
  )
}
function BasicDataAccountRegionAdministratorIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-region-administrator-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/region-administrator/index.vue'
  )
}
function BasicDataAccountRoleIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-role-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/role/index.vue'
  )
}
function BasicDataAccountUnitAccountIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-unit-account-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/unit-account/index.vue'
  )
}
function BasicDataAccountAdministratorAccountCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-administrator-account-create" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/administrator-account/create.vue'
  )
}
function BasicDataAccountAdministratorAccountDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-administrator-account-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/administrator-account/detail.vue'
  )
}
function BasicDataAccountAdministratorAccountModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-administrator-account-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/administrator-account/modify.vue'
  )
}
function BasicDataAccountDistributionAdministratorAccountCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-distribution-administrator-account-create" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/distribution-administrator-account/create.vue'
  )
}
function BasicDataAccountDistributionAdministratorAccountModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-distribution-administrator-account-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/distribution-administrator-account/modify.vue'
  )
}
function BasicDataAccountMonographicAccountCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-monographic-account-create" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/monographic-account/create.vue'
  )
}
function BasicDataAccountMonographicAccountDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-monographic-account-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/monographic-account/detail.vue'
  )
}
function BasicDataAccountMonographicAccountModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-monographic-account-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/monographic-account/modify.vue'
  )
}
function BasicDataAccountRoleCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-role-create" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/role/create.vue'
  )
}
function BasicDataAccountRoleDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-role-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/role/detail.vue'
  )
}
function BasicDataAccountRoleModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-role-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/role/modify.vue'
  )
}
function BasicDataAccountUnitAccountCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-unit-account-create" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/unit-account/create.vue'
  )
}
function BasicDataAccountUnitAccountImport() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-unit-account-import" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/unit-account/import.vue'
  )
}
function BasicDataAccountUnitAccountModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-account-unit-account-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/account/unit-account/modify.vue'
  )
}
function BasicDataPlatform() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-platform" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/platform.vue'
  )
}
function BasicDataPlatformBasicInfoIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-platform-basic-info-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/platform/basic-info/index.vue'
  )
}
function BasicDataPlatformFunctionIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-platform-function-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/platform/function/index.vue'
  )
}
function BasicDataPlatformOperationLogIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-platform-operation-log-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/platform/operation-log/index.vue'
  )
}
function BasicDataPlatformFunctionOnlineLearningRulesIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-platform-function-online-learning-rules-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/platform/function/online-learning-rules/index.vue'
  )
}
function BasicDataPlatformFunctionSupervisionIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-platform-function-supervision-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/platform/function/supervision/index.vue'
  )
}
function BasicDataPlatformFunctionOnlineLearningRulesAddOnlineLearningRules() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-platform-function-online-learning-rules-add-online-learning-rules" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/platform/function/online-learning-rules/add-online-learning-rules.vue'
  )
}
function BasicDataPlatformFunctionSupervisionPlatformSupervisionEdit() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-platform-function-supervision-platform-supervision-edit" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/platform/function/supervision/platform-supervision-edit.vue'
  )
}
function BasicDataPlatformFunctionSupervisionSupervisionRulesDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-platform-function-supervision-supervisionRulesDetail" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/platform/function/supervision/supervisionRulesDetail.vue'
  )
}
function BasicDataTrade() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-trade" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/trade.vue'
  )
}
function BasicDataTradeAccountIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-trade-account-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/trade/account/index.vue'
  )
}
function BasicDataTradeApplyIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-trade-apply-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/trade/apply/index.vue'
  )
}
function BasicDataTradeAccountCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-trade-account-create" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/trade/account/create.vue'
  )
}
function BasicDataTradeAccountDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-trade-account-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/trade/account/detail.vue'
  )
}
function BasicDataTradeAccountModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-trade-account-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/trade/account/modify.vue'
  )
}
function BasicDataDistributionChannelIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-distribution-channel-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/distribution-channel/index.vue'
  )
}
function BasicDataInfoIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-info-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/info/index.vue'
  )
}
function BasicDataInfoCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-info-create" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/info/create.vue'
  )
}
function BasicDataInfoDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-info-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/info/detail.vue'
  )
}
function BasicDataInfoModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/basic-data-info-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/basic-data/info/modify.vue'
  )
}
function Home() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/home" */ '@hbfe/jxjy-admin-routers/src/basic-router/home.vue'
  )
}
function HomeHome() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/home-home" */ '@hbfe/jxjy-admin-routers/src/basic-router/home/<USER>'
  )
}
function HomePersonInfoIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/home-person-info-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/home/<USER>/index.vue'
  )
}
function MarketingCenter() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/marketing-center" */ '@hbfe/jxjy-admin-routers/src/basic-router/marketing-center.vue'
  )
}
function MarketingCenterMarketingCenter() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/marketing-center-marketing-center" */ '@hbfe/jxjy-admin-routers/src/basic-router/marketing-center/marketing-center.vue'
  )
}
function Resource() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource.vue'
  )
}
function ResourceCourseIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-course-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/course/index.vue'
  )
}
function ResourceCoursewareIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-courseware-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/courseware/index.vue'
  )
}
function ResourceExamPaperIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-exam-paper-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/exam-paper/index.vue'
  )
}
function ResourceQuestionIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-question-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/question/index.vue'
  )
}
function ResourceQuestionLibraryIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-question-library-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/question-library/index.vue'
  )
}
function ResourceQuestionnaireIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-questionnaire-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/questionnaire/index.vue'
  )
}
function ResourceTrainingPointsIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-training-points-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/training-points/index.vue'
  )
}
function ResourceCourseCategory() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-course-category" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/course/category.vue'
  )
}
function ResourceCourseCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-course-create" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/course/create.vue'
  )
}
function ResourceCourseDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-course-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/course/detail.vue'
  )
}
function ResourceCourseModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-course-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/course/modify.vue'
  )
}
function ResourceCoursewareCategory() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-courseware-category" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/courseware/category.vue'
  )
}
function ResourceCoursewareCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-courseware-create" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/courseware/create.vue'
  )
}
function ResourceCoursewareDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-courseware-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/courseware/detail.vue'
  )
}
function ResourceCoursewareModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-courseware-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/courseware/modify.vue'
  )
}
function ResourceExamPaperCategory() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-exam-paper-category" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/exam-paper/category.vue'
  )
}
function ResourceExamPaperCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-exam-paper-create" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/exam-paper/create.vue'
  )
}
function ResourceExamPaperDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-exam-paper-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/exam-paper/detail.vue'
  )
}
function ResourceExamPaperModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-exam-paper-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/exam-paper/modify.vue'
  )
}
function ResourceQuestionCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-question-create" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/question/create.vue'
  )
}
function ResourceQuestionDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-question-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/question/detail.vue'
  )
}
function ResourceQuestionImport() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-question-import" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/question/import.vue'
  )
}
function ResourceQuestionModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-question-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/question/modify.vue'
  )
}
function ResourceQuestionnaireModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-questionnaire-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/questionnaire/modify.vue'
  )
}
function ResourceQuestionnairePreview() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-questionnaire-preview" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/questionnaire/preview.vue'
  )
}
function ResourceTrainingPointsDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-training-points-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/training-points/detail.vue'
  )
}
function ResourceTrainingPointsModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/resource-training-points-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/resource/training-points/modify.vue'
  )
}
function SchoolManagement() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/school-management" */ '@hbfe/jxjy-admin-routers/src/basic-router/school-management.vue'
  )
}
function SchoolManagementManagement() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/school-management-management" */ '@hbfe/jxjy-admin-routers/src/basic-router/school-management/management.vue'
  )
}
function SchoolManagementModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/school-management-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/school-management/modify.vue'
  )
}
function SchoolManagementRegisterSchool() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/school-management-register-school" */ '@hbfe/jxjy-admin-routers/src/basic-router/school-management/register-school.vue'
  )
}
function Settings() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/settings" */ '@hbfe/jxjy-admin-routers/src/basic-router/settings.vue'
  )
}
function Statistic() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic.vue'
  )
}
function StatisticCommodityStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-commodity-statistic-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/commodity-statistic/index.vue'
  )
}
function StatisticCourseSelectionStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-course-selection-statistic-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/course-selection-statistic/index.vue'
  )
}
function StatisticDistributionGoodsOpeningStatisticsIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-distribution-goods-opening-statistics-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/distribution-goods-opening-statistics/index.vue'
  )
}
function StatisticExportTaskIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-export-task-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/export-task/index.vue'
  )
}
function StatisticHuayiSellStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-huayi-sell-statistic-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/huayi-sell-statistic/index.vue'
  )
}
function StatisticLearningStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-learning-statistic-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/learning-statistic/index.vue'
  )
}
function StatisticPromotionStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-promotion-statistic-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/promotion-statistic/index.vue'
  )
}
function StatisticRegionLearningStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-region-learning-statistic-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/region-learning-statistic/index.vue'
  )
}
function StatisticRegionSellStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-region-sell-statistic-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/region-sell-statistic/index.vue'
  )
}
function StatisticRegionalSituationStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-regional-situation-statistic-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/regional-situation-statistic/index.vue'
  )
}
function StatisticSchemeLearningStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-scheme-learning-statistic-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/scheme-learning-statistic/index.vue'
  )
}
function StatisticSchemeSellStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-scheme-sell-statistic-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/scheme-sell-statistic/index.vue'
  )
}
function StatisticSupplierDistributionOfGoodsOpenStatisticsIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-supplier-distribution-of-goods-open-statistics-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/supplier-distribution-of-goods-open-statistics/index.vue'
  )
}
function StatisticSupplierDistributorSalesStatisticsIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-supplier-distributor-sales-statistics-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/supplier-distributor-sales-statistics/index.vue'
  )
}
function StatisticTrainingunitSalesStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-trainingunit-sales-statistic-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/trainingunit-sales-statistic/index.vue'
  )
}
function StatisticStatisticsReportLearningLog() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-statistics-report-learning-log" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/statistics-report/learning-log.vue'
  )
}
function StatisticStatisticsReportStudyLog() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/statistic-statistics-report-study-log" */ '@hbfe/jxjy-admin-routers/src/basic-router/statistic/statistics-report/study-log.vue'
  )
}
function TestUserInfo() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/test-user-info" */ '@hbfe/jxjy-admin-routers/src/basic-router/test-user-info.vue'
  )
}
function Training() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training" */ '@hbfe/jxjy-admin-routers/src/basic-router/training.vue'
  )
}
function TrainingCustomerService() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-customer-service" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/customer-service.vue'
  )
}
function TrainingCustomerServiceCollectiveIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-customer-service-collective-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/customer-service/collective/index.vue'
  )
}
function TrainingCustomerServicePersonalIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-customer-service-personal-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/customer-service/personal/index.vue'
  )
}
function TrainingImport() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-import" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/import.vue'
  )
}
function TrainingImportSchemeIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-import-scheme-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/import/scheme/index.vue'
  )
}
function TrainingImportStudentIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-import-student-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/import/student/index.vue'
  )
}
function TrainingImportTaskTrackIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-import-task-track-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/import/task-track/index.vue'
  )
}
function TrainingImportTaskTrackImportTask() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-import-task-track-import-task" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/import/task-track/import-task.vue'
  )
}
function TrainingIntelligentLearning() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-intelligent-learning" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/intelligent-learning.vue'
  )
}
function TrainingIntelligentLearningImportOpenIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-intelligent-learning-import-open-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/intelligent-learning/import-open/index.vue'
  )
}
function TrainingIntelligentLearningLearningTaskTrackingIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-intelligent-learning-learning-task-tracking-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/intelligent-learning/learning-task-tracking/index.vue'
  )
}
function TrainingLogManagement() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-log-management" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/log-management.vue'
  )
}
function TrainingLogManagementLoginLogIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-log-management-login-log-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/log-management/login-log/index.vue'
  )
}
function TrainingScheme() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme.vue'
  )
}
function TrainingSchemeAcademicAdministration() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-academicAdministration" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/academicAdministration.vue'
  )
}
function TrainingSchemeActivityManagement() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-activity-management" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/activity-management.vue'
  )
}
function TrainingSchemeCourseMaintenance() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-course-maintenance" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/course-maintenance.vue'
  )
}
function TrainingSchemeCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-create" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/create.vue'
  )
}
function TrainingSchemeDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/detail.vue'
  )
}
function TrainingSchemeHolisticReport() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-holisticReport" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/holisticReport.vue'
  )
}
function TrainingSchemeImplementingManagement() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-implementingManagement" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/implementingManagement.vue'
  )
}
function TrainingSchemeManage() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-manage" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/manage.vue'
  )
}
function TrainingSchemeMergeRegistration() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-merge-registration" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/merge-registration.vue'
  )
}
function TrainingSchemeModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/modify.vue'
  )
}
function TrainingSchemePreTrainingImplementation() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-preTrainingImplementation" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/preTrainingImplementation.vue'
  )
}
function TrainingSchemeQuestionAnswer() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-questionAnswer" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/questionAnswer.vue'
  )
}
function TrainingSchemeTrainingRequireField() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-training-require-field" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/training-require-field.vue'
  )
}
function TrainingSchemeTrainingProcess() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-trainingProcess" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/trainingProcess.vue'
  )
}
function TrainingSchemeTrainingResults() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-scheme-trainingResults" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/scheme/trainingResults.vue'
  )
}
function TrainingSpecialTopics() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-special-topics" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/special-topics.vue'
  )
}
function TrainingSpecialTopicsAddIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-special-topics-add-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/special-topics/add/index.vue'
  )
}
function TrainingSpecialTopicsManageIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-special-topics-manage-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/special-topics/manage/index.vue'
  )
}
function TrainingSpecialTopicsThematicInformationManagementIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-special-topics-thematic-information-management-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/special-topics/thematic-information-management/index.vue'
  )
}
function TrainingSpecialTopicsManageEdit() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-special-topics-manage-edit" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/special-topics/manage/edit.vue'
  )
}
function TrainingSpecialTopicsThematicInformationManagementCreated() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-special-topics-thematic-information-management-created" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/special-topics/thematic-information-management/created.vue'
  )
}
function TrainingSpecialTopicsThematicInformationManagementModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-special-topics-thematic-information-management-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/special-topics/thematic-information-management/modify.vue'
  )
}
function TrainingTask() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-task" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/task.vue'
  )
}
function TrainingTaskExporttaskIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-task-exporttask-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/task/exporttask/index.vue'
  )
}
function TrainingTaskImporttaskIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-task-importtask-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/task/importtask/index.vue'
  )
}
function TrainingTrade() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade.vue'
  )
}
function TrainingTradeInvoice() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-invoice" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/invoice.vue'
  )
}
function TrainingTradeInvoiceCollectiveIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-invoice-collective-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/invoice/collective/index.vue'
  )
}
function TrainingTradeInvoicePersonalIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-invoice-personal-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/invoice/personal/index.vue'
  )
}
function TrainingTradeOrder() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-order" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/order.vue'
  )
}
function TrainingTradeOrderCollectiveIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-order-collective-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/order/collective/index.vue'
  )
}
function TrainingTradeOrderPersonalIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-order-personal-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/order/personal/index.vue'
  )
}
function TrainingTradeOrderCollectiveDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-order-collective-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/order/collective/detail.vue'
  )
}
function TrainingTradeOrderCollectiveSubDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-order-collective-sub-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/order/collective/sub-detail.vue'
  )
}
function TrainingTradeOrderPersonalDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-order-personal-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/order/personal/detail.vue'
  )
}
function TrainingTradeReconciliation() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-reconciliation" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/reconciliation.vue'
  )
}
function TrainingTradeReconciliationCollectiveIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-reconciliation-collective-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/reconciliation/collective/index.vue'
  )
}
function TrainingTradeReconciliationPersonalIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-reconciliation-personal-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/reconciliation/personal/index.vue'
  )
}
function TrainingTradeRefund() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-refund" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/refund.vue'
  )
}
function TrainingTradeRefundCollectiveIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-refund-collective-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/refund/collective/index.vue'
  )
}
function TrainingTradeRefundPersonalIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-refund-personal-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/refund/personal/index.vue'
  )
}
function TrainingTradeRefundCollectiveDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-refund-collective-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/refund/collective/detail.vue'
  )
}
function TrainingTradeRefundCollectiveSubDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-refund-collective-sub-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/refund/collective/sub-detail.vue'
  )
}
function TrainingTradeRefundPersonalDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-trade-refund-personal-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/trade/refund/personal/detail.vue'
  )
}
function TrainingUser() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-user" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/user.vue'
  )
}
function TrainingUserInfo() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-user-info" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/user/info.vue'
  )
}
function TrainingUserList() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-user-list" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/user/list.vue'
  )
}
function TrainingUserCollectiveAccountIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-user-collective-account-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/user/collective-account/index.vue'
  )
}
function TrainingUserStudentIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-user-student-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/user/student/index.vue'
  )
}
function TrainingUserStudentDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-user-student-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/user/student/detail.vue'
  )
}
function TrainingBatchPrintIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-batch-print-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/batch-print/index.vue'
  )
}
function TrainingCoursePackageIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-course-package-index" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/course-package/index.vue'
  )
}
function TrainingBatchPrintImportListPrinting() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-batch-print-import-list-printing" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/batch-print/import-list-printing.vue'
  )
}
function TrainingCoursePackageCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-course-package-create" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/course-package/create.vue'
  )
}
function TrainingCoursePackageDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-course-package-detail" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/course-package/detail.vue'
  )
}
function TrainingCoursePackageImport() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-course-package-import" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/course-package/import.vue'
  )
}
function TrainingCoursePackageModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-course-package-modify" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/course-package/modify.vue'
  )
}
function TrainingCoursePackageSyncScheme() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/basic-router/training-course-package-sync-scheme" */ '@hbfe/jxjy-admin-routers/src/basic-router/training/course-package/sync-scheme.vue'
  )
}

export default [
  {
    name: 'basic-data',
    path: '/basic-data',
    component: BasicData,
    meta: {
      permissionMap: {},
      openWhenInit: false,
      closeAble: false,
      isMenu: true,
      title: '系统基础配置',
      sort: 2,
      icon: 'icon-peizhi',
      ownerGroup: [],
      group: 'basic-data'
    },
    children: [
      {
        name: 'basic-data-account',
        path: 'account',
        component: BasicDataAccount,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '运营帐号管理',
          sort: 5,
          icon: 'icon-yunyingrenyuan',
          ownerGroup: [],
          group: 'basic-data.account'
        },
        children: [
          {
            name: 'basic-data-account-administrator-account-index',
            path: 'administrator-account',
            component: BasicDataAccountAdministratorAccountIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '平台管理员账号管理',
              sort: 1,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'basic-data.account.administrator-account'
            }
          },
          {
            name: 'basic-data-account-distribution-administrator-account-index',
            path: 'distribution-administrator-account',
            component: BasicDataAccountDistributionAdministratorAccountIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '分销管理员账号管理',
              sort: 6,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'basic-data.account.distribution-administrator-account'
            }
          },
          {
            name: 'basic-data-account-monographic-account-index',
            path: 'monographic-account',
            component: BasicDataAccountMonographicAccountIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '专题管理员管理',
              sort: 7,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'basic-data.account.monographic-account'
            }
          },
          {
            name: 'basic-data-account-region-administrator-index',
            path: 'region-administrator',
            component: BasicDataAccountRegionAdministratorIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '地区管理员管理',
              sort: 5,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'basic-data.account.region-administrator'
            }
          },
          {
            name: 'basic-data-account-role-index',
            path: 'role',
            component: BasicDataAccountRoleIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '角色管理',
              sort: 2,
              ownerGroup: [],
              group: 'basic-data.account.role'
            }
          },
          {
            name: 'basic-data-account-unit-account-index',
            path: 'unit-account',
            component: BasicDataAccountUnitAccountIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '单位管理员管理',
              sort: 7,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'basic-data.account.unit-account'
            }
          },
          {
            name: 'basic-data-account-administrator-account-create',
            path: 'administrator-account/create',
            component: BasicDataAccountAdministratorAccountCreate,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.account.administrator-account.create'
            }
          },
          {
            name: 'basic-data-account-administrator-account-detail',
            path: 'administrator-account/detail/:id',
            component: BasicDataAccountAdministratorAccountDetail,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.account.administrator-account.detail'
            }
          },
          {
            name: 'basic-data-account-administrator-account-modify',
            path: 'administrator-account/modify/:id',
            component: BasicDataAccountAdministratorAccountModify,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.account.administrator-account.modify'
            }
          },
          {
            name: 'basic-data-account-distribution-administrator-account-create',
            path: 'distribution-administrator-account/create',
            component: BasicDataAccountDistributionAdministratorAccountCreate,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.account.distribution-administrator-account.create'
            }
          },
          {
            name: 'basic-data-account-distribution-administrator-account-modify',
            path: 'distribution-administrator-account/modify/:id',
            component: BasicDataAccountDistributionAdministratorAccountModify,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.account.distribution-administrator-account.modify'
            }
          },
          {
            name: 'basic-data-account-monographic-account-create',
            path: 'monographic-account/create',
            component: BasicDataAccountMonographicAccountCreate,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.account.monographic-account.create'
            }
          },
          {
            name: 'basic-data-account-monographic-account-detail',
            path: 'monographic-account/detail/:id',
            component: BasicDataAccountMonographicAccountDetail,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.account.monographic-account.detail'
            }
          },
          {
            name: 'basic-data-account-monographic-account-modify',
            path: 'monographic-account/modify/:id',
            component: BasicDataAccountMonographicAccountModify,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.account.monographic-account.modify'
            }
          },
          {
            name: 'basic-data-account-role-create',
            path: 'role/create',
            component: BasicDataAccountRoleCreate,
            meta: {
              permissionMap: {},
              title: '创建角色',
              ownerGroup: [],
              group: 'basic-data.account.role.create'
            }
          },
          {
            name: 'basic-data-account-role-detail',
            path: 'role/detail/:id',
            component: BasicDataAccountRoleDetail,
            meta: {
              permissionMap: {},
              title: '角色详情',
              ownerGroup: [],
              group: 'basic-data.account.role.detail'
            }
          },
          {
            name: 'basic-data-account-role-modify',
            path: 'role/modify/:id',
            component: BasicDataAccountRoleModify,
            meta: {
              permissionMap: {},
              title: '修改角色信息',
              ownerGroup: [],
              group: 'basic-data.account.role.modify'
            }
          },
          {
            name: 'basic-data-account-unit-account-create',
            path: 'unit-account/create',
            component: BasicDataAccountUnitAccountCreate,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.account.unit-account.create'
            }
          },
          {
            name: 'basic-data-account-unit-account-import',
            path: 'unit-account/import/:id',
            component: BasicDataAccountUnitAccountImport,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.account.unit-account.import'
            }
          },
          {
            name: 'basic-data-account-unit-account-modify',
            path: 'unit-account/modify/:id',
            component: BasicDataAccountUnitAccountModify,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.account.unit-account.modify'
            }
          }
        ]
      },
      {
        name: 'basic-data-platform',
        path: 'platform',
        component: BasicDataPlatform,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '网校配置',
          sort: 1,
          icon: 'icon-weiwangxiao',
          ownerGroup: [],
          group: 'basic-data.platform'
        },
        children: [
          {
            name: 'basic-data-platform-basic-info-index',
            path: 'basic-info',
            component: BasicDataPlatformBasicInfoIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '基础信息配置',
              sort: 1,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'basic-data.platform.basic-info'
            }
          },
          {
            name: 'basic-data-platform-function-index',
            path: 'function',
            component: BasicDataPlatformFunctionIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '功能设置',
              sort: 2,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'basic-data.platform.function'
            }
          },
          {
            name: 'basic-data-platform-operation-log-index',
            path: 'operation-log',
            component: BasicDataPlatformOperationLogIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '操作日志',
              sort: 3,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'basic-data.platform.operation-log'
            }
          },
          {
            name: 'basic-data-platform-function-online-learning-rules-index',
            path: 'function/online-learning-rules',
            component: BasicDataPlatformFunctionOnlineLearningRulesIndex,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.platform.function.online-learning-rules'
            }
          },
          {
            name: 'basic-data-platform-function-supervision-index',
            path: 'function/supervision',
            component: BasicDataPlatformFunctionSupervisionIndex,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.platform.function.supervision'
            }
          },
          {
            name: 'basic-data-platform-function-online-learning-rules-add-online-learning-rules',
            path: 'function/online-learning-rules/add-online-learning-rules',
            component: BasicDataPlatformFunctionOnlineLearningRulesAddOnlineLearningRules,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.platform.function.online-learning-rules.add-online-learning-rules'
            }
          },
          {
            name: 'basic-data-platform-function-supervision-platform-supervision-edit',
            path: 'function/supervision/platform-supervision-edit',
            component: BasicDataPlatformFunctionSupervisionPlatformSupervisionEdit,
            meta: {
              permissionMap: {},
              title: '网校级别监管编辑页',
              ownerGroup: [],
              group: 'basic-data.platform.function.supervision.platform-supervision-edit'
            }
          },
          {
            name: 'basic-data-platform-function-supervision-supervisionRulesDetail',
            path: 'function/supervision/supervisionRulesDetail',
            component: BasicDataPlatformFunctionSupervisionSupervisionRulesDetail,
            meta: {
              permissionMap: {},
              title: '监管规则配置',
              ownerGroup: [],
              group: 'basic-data.platform.function.supervision.supervisionRulesDetail'
            }
          }
        ]
      },
      {
        name: 'basic-data-trade',
        path: 'trade',
        component: BasicDataTrade,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '交易信息配置',
          sort: 3,
          icon: 'icon-xuanzeleixing',
          ownerGroup: [],
          group: 'basic-data.trade'
        },
        children: [
          {
            name: 'basic-data-trade-account-index',
            path: 'account',
            component: BasicDataTradeAccountIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '收款账户管理',
              sort: 1,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'basic-data.trade.account'
            }
          },
          {
            name: 'basic-data-trade-apply-index',
            path: 'apply',
            component: BasicDataTradeApplyIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '报名方式配置',
              sort: 2,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'basic-data.trade.apply'
            }
          },
          {
            name: 'basic-data-trade-account-create',
            path: 'account/create',
            component: BasicDataTradeAccountCreate,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.trade.account.create'
            }
          },
          {
            name: 'basic-data-trade-account-detail',
            path: 'account/detail/:id',
            component: BasicDataTradeAccountDetail,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'basic-data.trade.account.detail'
            }
          },
          {
            name: 'basic-data-trade-account-modify',
            path: 'account/modify/:id',
            component: BasicDataTradeAccountModify,
            meta: {
              permissionMap: {},
              title: '修改收款账号',
              ownerGroup: [],
              group: 'basic-data.trade.account.modify'
            }
          }
        ]
      },
      {
        name: 'basic-data-distribution-channel-index',
        path: 'distribution-channel',
        component: BasicDataDistributionChannelIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '配送渠道配置',
          sort: 4,
          icon: 'icon-peisong',
          ownerGroup: [],
          group: 'basic-data.distribution-channel'
        }
      },
      {
        name: 'basic-data-info-index',
        path: 'info',
        component: BasicDataInfoIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '资讯管理',
          sort: 2,
          icon: 'icon-zixun',
          ownerGroup: [],
          group: 'basic-data.info'
        }
      },
      {
        name: 'basic-data-info-create',
        path: 'info/create',
        component: BasicDataInfoCreate,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'basic-data.info.create'
        }
      },
      {
        name: 'basic-data-info-detail',
        path: 'info/detail/:id',
        component: BasicDataInfoDetail,
        meta: {
          permissionMap: {},
          title: '资讯详情',
          ownerGroup: [],
          group: 'basic-data.info.detail'
        }
      },
      {
        name: 'basic-data-info-modify',
        path: 'info/modify/:id',
        component: BasicDataInfoModify,
        meta: {
          permissionMap: {},
          title: '修改资讯',
          ownerGroup: [],
          group: 'basic-data.info.modify'
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: Home,
    meta: {
      permissionMap: {},
      openWhenInit: true,
      closeAble: false,
      isMenu: true,
      title: '首页',
      sort: 1,
      icon: 'icon-shouye',
      ownerGroup: [],
      group: 'home'
    },
    children: [
      {
        name: 'home-home',
        path: 'home',
        component: HomeHome,
        meta: {
          permissionMap: {},
          isMenu: true,
          openWhenInit: true,
          closeAble: false,
          title: '首页',
          sort: 1,
          icon: 'icon-shouye1',
          ownerGroup: [],
          group: 'home.home'
        }
      },
      {
        name: 'home-person-info-index',
        path: 'person-info',
        component: HomePersonInfoIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          onlyShowOnTab: true,
          title: '个人帐号设置',
          sort: 2,
          hideMenu: true,
          icon: 'icon_guanli',
          ownerGroup: [],
          group: 'home.person-info'
        }
      }
    ]
  },
  {
    name: 'marketing-center',
    path: '/marketing-center',
    component: MarketingCenter,
    meta: {
      permissionMap: {},
      openWhenInit: false,
      closeAble: false,
      isMenu: true,
      title: '营销中心',
      sort: 6,
      icon: 'icon-shuju',
      ownerGroup: [],
      group: 'marketing-center'
    },
    children: [
      {
        name: 'marketing-center-marketing-center',
        path: 'marketing-center',
        component: MarketingCenterMarketingCenter,
        meta: {
          permissionMap: {},
          isMenu: true,
          openWhenInit: false,
          closeAble: false,
          title: '营销中心',
          sort: 1,
          icon: 'icon_guanli',
          ownerGroup: [],
          group: 'marketing-center.marketing-center'
        }
      }
    ]
  },
  {
    name: 'resource',
    path: '/resource',
    component: Resource,
    meta: {
      permissionMap: {},
      openWhenInit: false,
      closeAble: false,
      isMenu: true,
      title: '教学资源管理',
      sort: 3,
      icon: 'icon-kecheng',
      ownerGroup: [],
      group: 'resource'
    },
    children: [
      {
        name: 'resource-course-index',
        path: 'course',
        component: ResourceCourseIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '课程管理',
          sort: 2,
          icon: 'icon-xuexi',
          ownerGroup: [],
          group: 'resource.course'
        }
      },
      {
        name: 'resource-courseware-index',
        path: 'courseware',
        component: ResourceCoursewareIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '课件管理',
          sort: 1,
          icon: 'icon-kejian',
          ownerGroup: [],
          group: 'resource.courseware'
        }
      },
      {
        name: 'resource-exam-paper-index',
        path: 'exam-paper',
        component: ResourceExamPaperIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '试卷管理',
          sort: 6,
          icon: 'icon-shijuan',
          ownerGroup: [],
          group: 'resource.exam-paper'
        }
      },
      {
        name: 'resource-question-index',
        path: 'question',
        component: ResourceQuestionIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '试题管理',
          sort: 5,
          icon: 'icon-shenqing',
          ownerGroup: [],
          group: 'resource.question'
        }
      },
      {
        name: 'resource-question-library-index',
        path: 'question-library',
        component: ResourceQuestionLibraryIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '题库管理',
          sort: 4,
          icon: 'icon-tiku',
          ownerGroup: [],
          group: 'resource.question-library'
        }
      },
      {
        name: 'resource-questionnaire-index',
        path: 'questionnaire',
        component: ResourceQuestionnaireIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '问卷管理',
          sort: 7,
          icon: 'icon-zixun',
          ownerGroup: [],
          group: 'resource.questionnaire'
        }
      },
      {
        name: 'resource-training-points-index',
        path: 'training-points',
        component: ResourceTrainingPointsIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '培训点管理',
          sort: 8,
          icon: 'icon-danwei1',
          ownerGroup: [],
          group: 'resource.training-points'
        }
      },
      {
        name: 'resource-course-category',
        path: 'course/category',
        component: ResourceCourseCategory,
        meta: {
          permissionMap: {},
          title: '课程分类管理',
          isMenu: false,
          ownerGroup: [],
          group: 'resource.course.category'
        }
      },
      {
        name: 'resource-course-create',
        path: 'course/create',
        component: ResourceCourseCreate,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.course.create'
        }
      },
      {
        name: 'resource-course-detail',
        path: 'course/detail/:id',
        component: ResourceCourseDetail,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.course.detail'
        }
      },
      {
        name: 'resource-course-modify',
        path: 'course/modify/:id',
        component: ResourceCourseModify,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.course.modify'
        }
      },
      {
        name: 'resource-courseware-category',
        path: 'courseware/category',
        component: ResourceCoursewareCategory,
        meta: {
          permissionMap: {},
          isMenu: false,
          title: '课件分类管理',
          ownerGroup: [],
          group: 'resource.courseware.category'
        }
      },
      {
        name: 'resource-courseware-create',
        path: 'courseware/create',
        component: ResourceCoursewareCreate,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.courseware.create'
        }
      },
      {
        name: 'resource-courseware-detail',
        path: 'courseware/detail/:id',
        component: ResourceCoursewareDetail,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.courseware.detail'
        }
      },
      {
        name: 'resource-courseware-modify',
        path: 'courseware/modify/:id',
        component: ResourceCoursewareModify,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.courseware.modify'
        }
      },
      {
        name: 'resource-exam-paper-category',
        path: 'exam-paper/category',
        component: ResourceExamPaperCategory,
        meta: {
          permissionMap: {},
          isMenu: false,
          onlyShowOnTab: true,
          title: '试卷分类',
          ownerGroup: [],
          group: 'resource.exam-paper.category'
        }
      },
      {
        name: 'resource-exam-paper-create',
        path: 'exam-paper/create',
        component: ResourceExamPaperCreate,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.exam-paper.create'
        }
      },
      {
        name: 'resource-exam-paper-detail',
        path: 'exam-paper/detail/:id',
        component: ResourceExamPaperDetail,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.exam-paper.detail'
        }
      },
      {
        name: 'resource-exam-paper-modify',
        path: 'exam-paper/modify/:id',
        component: ResourceExamPaperModify,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.exam-paper.modify'
        }
      },
      {
        name: 'resource-question-create',
        path: 'question/create',
        component: ResourceQuestionCreate,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.question.create'
        }
      },
      {
        name: 'resource-question-detail',
        path: 'question/detail/:id/:questionType',
        component: ResourceQuestionDetail,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.question.detail'
        }
      },
      {
        name: 'resource-question-import',
        path: 'question/import',
        component: ResourceQuestionImport,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.question.import'
        }
      },
      {
        name: 'resource-question-modify',
        path: 'question/modify/:id/:questionType',
        component: ResourceQuestionModify,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.question.modify'
        }
      },
      {
        name: 'resource-questionnaire-modify',
        path: 'questionnaire/modify/:type',
        component: ResourceQuestionnaireModify,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.questionnaire.modify'
        }
      },
      {
        name: 'resource-questionnaire-preview',
        path: 'questionnaire/preview',
        component: ResourceQuestionnairePreview,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.questionnaire.preview'
        }
      },
      {
        name: 'resource-training-points-detail',
        path: 'training-points/detail/:id',
        component: ResourceTrainingPointsDetail,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.training-points.detail'
        }
      },
      {
        name: 'resource-training-points-modify',
        path: 'training-points/modify/:type',
        component: ResourceTrainingPointsModify,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'resource.training-points.modify'
        }
      }
    ]
  },
  {
    name: 'school-management',
    path: '/school-management',
    component: SchoolManagement,
    meta: {
      permissionMap: {},
      openWhenInit: false,
      closeAble: false,
      isMenu: true,
      title: '网校管理',
      sort: 6,
      icon: 'icon-weiwangxiao',
      ownerGroup: [],
      group: 'school-management'
    },
    children: [
      {
        name: 'school-management-management',
        path: 'management',
        component: SchoolManagementManagement,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '网校管理',
          sort: 2,
          icon: 'icon-daoru',
          ownerGroup: [],
          group: 'school-management.management'
        }
      },
      {
        name: 'school-management-modify',
        path: 'modify/:schoolId',
        component: SchoolManagementModify,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '修改网校',
          hideMenu: true,
          ownerGroup: [],
          group: 'school-management.modify'
        }
      },
      {
        name: 'school-management-register-school',
        path: 'register-school',
        component: SchoolManagementRegisterSchool,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '开通网校',
          sort: 1,
          icon: 'icon-daoru',
          ownerGroup: [],
          group: 'school-management.register-school'
        }
      }
    ]
  },
  {
    name: 'settings',
    path: '/settings',
    component: Settings,
    meta: {
      permissionMap: {},
      onlyShowOnTab: true,
      isMenu: true,
      title: '设置',
      ownerGroup: [],
      group: 'settings'
    }
  },
  {
    name: 'statistic',
    path: '/statistic',
    component: Statistic,
    meta: {
      permissionMap: {},
      openWhenInit: false,
      closeAble: false,
      isMenu: true,
      title: '统计报表',
      sort: 5,
      icon: 'icon-shuju',
      ownerGroup: [],
      group: 'statistic'
    },
    children: [
      {
        name: 'statistic-commodity-statistic-index',
        path: 'commodity-statistic',
        component: StatisticCommodityStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: false,
          title: '分销商品销售统计',
          sort: 7,
          icon: 'icon-mingxi',
          ownerGroup: [],
          group: 'statistic.commodity-statistic'
        }
      },
      {
        name: 'statistic-course-selection-statistic-index',
        path: 'course-selection-statistic',
        component: StatisticCourseSelectionStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '选课统计',
          sort: 8,
          icon: 'icon-ribaotongji',
          ownerGroup: [],
          group: 'statistic.course-selection-statistic'
        }
      },
      {
        name: 'statistic-distribution-goods-opening-statistics-index',
        path: 'distribution-goods-opening-statistics',
        component: StatisticDistributionGoodsOpeningStatisticsIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '分销商品开通统计',
          sort: 11,
          icon: 'icon-mingxi',
          ownerGroup: [],
          group: 'statistic.distribution-goods-opening-statistics'
        }
      },
      {
        name: 'statistic-export-task-index',
        path: 'export-task',
        component: StatisticExportTaskIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '导出任务查看',
          sort: 6,
          icon: 'icon-chakan',
          ownerGroup: [],
          group: 'statistic.export-task'
        }
      },
      {
        name: 'statistic-huayi-sell-statistic-index',
        path: 'huayi-sell-statistic',
        component: StatisticHuayiSellStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '华医网组合开通统计',
          sort: 15,
          icon: 'icon-mingxi',
          ownerGroup: [],
          group: 'statistic.huayi-sell-statistic'
        }
      },
      {
        name: 'statistic-learning-statistic-index',
        path: 'learning-statistic',
        component: StatisticLearningStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '学员学习明细',
          sort: 5,
          icon: 'icon-mingxi',
          ownerGroup: [],
          group: 'statistic.learning-statistic'
        }
      },
      {
        name: 'statistic-promotion-statistic-index',
        path: 'promotion-statistic',
        component: StatisticPromotionStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: false,
          title: '分销商销售统计',
          sort: 6,
          icon: 'icon-mingxi',
          ownerGroup: [],
          group: 'statistic.promotion-statistic'
        }
      },
      {
        name: 'statistic-region-learning-statistic-index',
        path: 'region-learning-statistic',
        component: StatisticRegionLearningStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '地区学习统计',
          sort: 4,
          icon: 'icon-tongjibaobiao',
          ownerGroup: [],
          group: 'statistic.region-learning-statistic'
        }
      },
      {
        name: 'statistic-region-sell-statistic-index',
        path: 'region-sell-statistic',
        component: StatisticRegionSellStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '地区开通统计',
          sort: 2,
          icon: 'icon-cptj',
          ownerGroup: [],
          group: 'statistic.region-sell-statistic'
        }
      },
      {
        name: 'statistic-regional-situation-statistic-index',
        path: 'regional-situation-statistic',
        component: StatisticRegionalSituationStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '地区学情统计',
          sort: 4,
          icon: 'icon-ribaotongji',
          ownerGroup: [],
          group: 'statistic.regional-situation-statistic'
        }
      },
      {
        name: 'statistic-scheme-learning-statistic-index',
        path: 'scheme-learning-statistic',
        component: StatisticSchemeLearningStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '方案学习统计',
          sort: 3,
          icon: 'icon-tongjiyuce',
          ownerGroup: [],
          group: 'statistic.scheme-learning-statistic'
        }
      },
      {
        name: 'statistic-scheme-sell-statistic-index',
        path: 'scheme-sell-statistic',
        component: StatisticSchemeSellStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '方案开通统计',
          sort: 1,
          icon: 'icon-ribaotongji',
          ownerGroup: [],
          group: 'statistic.scheme-sell-statistic'
        }
      },
      {
        name: 'statistic-supplier-distribution-of-goods-open-statistics-index',
        path: 'supplier-distribution-of-goods-open-statistics',
        component: StatisticSupplierDistributionOfGoodsOpenStatisticsIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '分销商品开通统计',
          sort: 14,
          icon: 'icon-mingxi',
          ownerGroup: [],
          group: 'statistic.supplier-distribution-of-goods-open-statistics'
        }
      },
      {
        name: 'statistic-supplier-distributor-sales-statistics-index',
        path: 'supplier-distributor-sales-statistics',
        component: StatisticSupplierDistributorSalesStatisticsIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '分销商销售统计',
          sort: 12,
          icon: 'icon-mingxi',
          ownerGroup: [],
          group: 'statistic.supplier-distributor-sales-statistics'
        }
      },
      {
        name: 'statistic-trainingunit-sales-statistic-index',
        path: 'trainingunit-sales-statistic',
        component: StatisticTrainingunitSalesStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '培训单位销售统计',
          sort: 7,
          icon: 'icon-ribaotongji',
          ownerGroup: [],
          group: 'statistic.trainingunit-sales-statistic'
        }
      },
      {
        name: 'statistic-statistics-report-learning-log',
        path: 'statistics-report/learning-log',
        component: StatisticStatisticsReportLearningLog,
        meta: {
          permissionMap: {},
          isMenu: true,
          hideMenu: true,
          title: '学习日志',
          sort: 99,
          icon: 'icon-ribaotongji',
          ownerGroup: [],
          group: 'statistic.statistics-report.learning-log'
        }
      },
      {
        name: 'statistic-statistics-report-study-log',
        path: 'statistics-report/study-log',
        component: StatisticStatisticsReportStudyLog,
        meta: {
          permissionMap: {},
          isMenu: true,
          hideMenu: true,
          title: '监管日志',
          sort: 99,
          icon: 'icon-ribaotongji',
          ownerGroup: [],
          group: 'statistic.statistics-report.study-log'
        }
      }
    ]
  },
  {
    name: 'test-user-info',
    path: '/test-user-info',
    component: TestUserInfo,
    meta: {
      permissionMap: {},
      title: '测试用户信息',
      isMenu: false,
      ownerGroup: [],
      group: 'test-user-info'
    }
  },
  {
    name: 'training',
    path: '/training',
    component: Training,
    meta: {
      permissionMap: {},
      openWhenInit: false,
      closeAble: false,
      isMenu: true,
      title: '培训管理',
      sort: 4,
      icon: 'icon-peixun',
      ownerGroup: [],
      group: 'training'
    },
    children: [
      {
        name: 'training-customer-service',
        path: 'customer-service',
        component: TrainingCustomerService,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '客服管理',
          sort: 9,
          icon: 'icon-kefu',
          ownerGroup: [],
          group: 'training.customer-service'
        },
        children: [
          {
            name: 'training-customer-service-collective-index',
            path: 'collective',
            component: TrainingCustomerServiceCollectiveIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '集体报名咨询',
              sort: 2,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.customer-service.collective'
            }
          },
          {
            name: 'training-customer-service-personal-index',
            path: 'personal',
            component: TrainingCustomerServicePersonalIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '业务咨询',
              sort: 1,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.customer-service.personal'
            }
          }
        ]
      },
      {
        name: 'training-import',
        path: 'import',
        component: TrainingImport,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '导入开通',
          sort: 6,
          icon: 'icon-daoru',
          ownerGroup: [],
          group: 'training.import'
        },
        children: [
          {
            name: 'training-import-scheme-index',
            path: 'scheme',
            component: TrainingImportSchemeIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '导入学员并开班',
              sort: 2,
              ownerGroup: [],
              group: 'training.import.scheme'
            }
          },
          {
            name: 'training-import-student-index',
            path: 'student',
            component: TrainingImportStudentIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '导入学员',
              sort: 1,
              ownerGroup: [],
              group: 'training.import.student'
            }
          },
          {
            name: 'training-import-task-track-index',
            path: 'task-track',
            component: TrainingImportTaskTrackIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '导入开通结果跟踪',
              sort: 3,
              ownerGroup: [],
              group: 'training.import.task-track'
            }
          },
          {
            name: 'training-import-task-track-import-task',
            path: 'task-track/import-task',
            component: TrainingImportTaskTrackImportTask,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'training.import.task-track.import-task'
            }
          }
        ]
      },
      {
        name: 'training-intelligent-learning',
        path: 'intelligent-learning',
        component: TrainingIntelligentLearning,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '智能学习',
          sort: 7,
          icon: 'icon-trainprocess',
          ownerGroup: [],
          group: 'training.intelligent-learning'
        },
        children: [
          {
            name: 'training-intelligent-learning-import-open-index',
            path: 'import-open',
            component: TrainingIntelligentLearningImportOpenIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '导入学员开班并学习',
              sort: 1,
              ownerGroup: [],
              group: 'training.intelligent-learning.import-open'
            }
          },
          {
            name: 'training-intelligent-learning-learning-task-tracking-index',
            path: 'learning-task-tracking',
            component: TrainingIntelligentLearningLearningTaskTrackingIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '智能学习任务跟踪',
              sort: 2,
              ownerGroup: [],
              group: 'training.intelligent-learning.learning-task-tracking'
            }
          }
        ]
      },
      {
        name: 'training-log-management',
        path: 'log-management',
        component: TrainingLogManagement,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '日志管理',
          sort: 12,
          icon: 'icon-zixun',
          ownerGroup: [],
          group: 'training.log-management'
        },
        children: [
          {
            name: 'training-log-management-login-log-index',
            path: 'login-log',
            component: TrainingLogManagementLoginLogIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '登录登出日志',
              sort: 1,
              ownerGroup: [],
              group: 'training.log-management.login-log'
            }
          }
        ]
      },
      {
        name: 'training-scheme',
        path: 'scheme',
        component: TrainingScheme,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '培训方案管理',
          sort: 2,
          icon: 'icon-fangan',
          ownerGroup: [],
          group: 'training.scheme'
        },
        children: [
          {
            name: 'training-scheme-academicAdministration',
            path: 'academicAdministration/:schemeId/:periodId',
            component: TrainingSchemeAcademicAdministration,
            meta: {
              permissionMap: {},
              title: '教务管理',
              ownerGroup: [],
              group: 'training.scheme.academicAdministration'
            }
          },
          {
            name: 'training-scheme-activity-management',
            path: 'activity-management',
            component: TrainingSchemeActivityManagement,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'training.scheme.activity-management'
            }
          },
          {
            name: 'training-scheme-course-maintenance',
            path: 'course-maintenance',
            component: TrainingSchemeCourseMaintenance,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '公需课课程维护',
              sort: 5,
              ownerGroup: [],
              group: 'training.scheme.course-maintenance'
            }
          },
          {
            name: 'training-scheme-create',
            path: 'create/:schemeId',
            component: TrainingSchemeCreate,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '新建培训方案',
              sort: 1,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'training.scheme.create'
            }
          },
          {
            name: 'training-scheme-detail',
            path: 'detail/:id',
            component: TrainingSchemeDetail,
            meta: {
              permissionMap: {},
              title: '查看培训方案',
              ownerGroup: [],
              group: 'training.scheme.detail'
            }
          },
          {
            name: 'training-scheme-holisticReport',
            path: 'holisticReport/:id',
            component: TrainingSchemeHolisticReport,
            meta: {
              permissionMap: {},
              title: '整体报告',
              ownerGroup: [],
              group: 'training.scheme.holisticReport'
            }
          },
          {
            name: 'training-scheme-implementingManagement',
            path: 'implementingManagement/:schemeId',
            component: TrainingSchemeImplementingManagement,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '实施管理',
              sort: 2,
              icon: 'icon_guanli',
              hideMenu: true,
              ownerGroup: [],
              group: 'training.scheme.implementingManagement'
            }
          },
          {
            name: 'training-scheme-manage',
            path: 'manage',
            component: TrainingSchemeManage,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '培训方案管理',
              sort: 2,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'training.scheme.manage'
            }
          },
          {
            name: 'training-scheme-merge-registration',
            path: 'merge-registration/:id',
            component: TrainingSchemeMergeRegistration,
            meta: {
              permissionMap: {},
              title: '合并报名管理',
              ownerGroup: [],
              group: 'training.scheme.merge-registration'
            }
          },
          {
            name: 'training-scheme-modify',
            path: 'modify/:schemeId',
            component: TrainingSchemeModify,
            meta: {
              permissionMap: {},
              title: '修改培训方案',
              ownerGroup: [],
              group: 'training.scheme.modify'
            }
          },
          {
            name: 'training-scheme-preTrainingImplementation',
            path: 'preTrainingImplementation',
            component: TrainingSchemePreTrainingImplementation,
            meta: {
              permissionMap: {},
              title: '训前实施设置',
              ownerGroup: [],
              group: 'training.scheme.preTrainingImplementation'
            }
          },
          {
            name: 'training-scheme-questionAnswer',
            path: 'questionAnswer',
            component: TrainingSchemeQuestionAnswer,
            meta: {
              permissionMap: {},
              ownerGroup: [],
              group: 'training.scheme.questionAnswer'
            }
          },
          {
            name: 'training-scheme-training-require-field',
            path: 'training-require-field',
            component: TrainingSchemeTrainingRequireField,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '培训属性值管理',
              sort: 3,
              ownerGroup: [],
              group: 'training.scheme.training-require-field'
            }
          },
          {
            name: 'training-scheme-trainingProcess',
            path: 'trainingProcess',
            component: TrainingSchemeTrainingProcess,
            meta: {
              permissionMap: {},
              title: '培训过程管理',
              ownerGroup: [],
              group: 'training.scheme.trainingProcess'
            }
          },
          {
            name: 'training-scheme-trainingResults',
            path: 'trainingResults',
            component: TrainingSchemeTrainingResults,
            meta: {
              permissionMap: {},
              title: '培训成果管理',
              ownerGroup: [],
              group: 'training.scheme.trainingResults'
            }
          }
        ]
      },
      {
        name: 'training-special-topics',
        path: 'special-topics',
        component: TrainingSpecialTopics,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '专题管理',
          sort: 3,
          icon: 'hb-iconfont icon-grade',
          ownerGroup: [],
          group: 'training.special-topics'
        },
        children: [
          {
            name: 'training-special-topics-add-index',
            path: 'add',
            component: TrainingSpecialTopicsAddIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '新建专题',
              sort: 1,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.special-topics.add'
            }
          },
          {
            name: 'training-special-topics-manage-index',
            path: 'manage',
            component: TrainingSpecialTopicsManageIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '专题管理',
              sort: 2,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.special-topics.manage'
            }
          },
          {
            name: 'training-special-topics-thematic-information-management-index',
            path: 'thematic-information-management',
            component: TrainingSpecialTopicsThematicInformationManagementIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '专题资讯管理',
              sort: 3,
              ownerGroup: [],
              group: 'training.special-topics.thematic-information-management'
            }
          },
          {
            name: 'training-special-topics-manage-edit',
            path: 'manage/edit/:id',
            component: TrainingSpecialTopicsManageEdit,
            meta: {
              permissionMap: {},
              isMenu: false,
              title: '专题管理',
              ownerGroup: [],
              group: 'training.special-topics.manage.edit'
            }
          },
          {
            name: 'training-special-topics-thematic-information-management-created',
            path: 'thematic-information-management/created',
            component: TrainingSpecialTopicsThematicInformationManagementCreated,
            meta: {
              permissionMap: {},
              isMenu: false,
              title: '新建专题资讯',
              ownerGroup: [],
              group: 'training.special-topics.thematic-information-management.created'
            }
          },
          {
            name: 'training-special-topics-thematic-information-management-modify',
            path: 'thematic-information-management/modify/:id',
            component: TrainingSpecialTopicsThematicInformationManagementModify,
            meta: {
              permissionMap: {},
              title: '修改资讯',
              ownerGroup: [],
              group: 'training.special-topics.thematic-information-management.modify'
            }
          }
        ]
      },
      {
        name: 'training-task',
        path: 'task',
        component: TrainingTask,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '导入导出任务管理',
          sort: 11,
          icon: 'icon-dingdan',
          ownerGroup: [],
          group: 'training.task'
        },
        children: [
          {
            name: 'training-task-exporttask-index',
            path: 'exporttask',
            component: TrainingTaskExporttaskIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '导出任务管理',
              sort: 2,
              ownerGroup: [],
              group: 'training.task.exporttask'
            }
          },
          {
            name: 'training-task-importtask-index',
            path: 'importtask',
            component: TrainingTaskImporttaskIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '导入任务管理',
              sort: 1,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.task.importtask'
            }
          }
        ]
      },
      {
        name: 'training-trade',
        path: 'trade',
        component: TrainingTrade,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '交易管理',
          sort: 4,
          icon: 'icon-jiaoyi',
          ownerGroup: [],
          group: 'training.trade'
        },
        children: [
          {
            name: 'training-trade-invoice',
            path: 'invoice',
            component: TrainingTradeInvoice,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '发票管理',
              sort: 3,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.trade.invoice'
            },
            children: [
              {
                name: 'training-trade-invoice-collective-index',
                path: 'collective',
                component: TrainingTradeInvoiceCollectiveIndex,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  title: '集体报名发票',
                  sort: 2,
                  icon: 'icon_guanli',
                  ownerGroup: [],
                  group: 'training.trade.invoice.collective'
                }
              },
              {
                name: 'training-trade-invoice-personal-index',
                path: 'personal',
                component: TrainingTradeInvoicePersonalIndex,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  title: '个人报名发票',
                  sort: 1,
                  ownerGroup: [],
                  group: 'training.trade.invoice.personal'
                }
              }
            ]
          },
          {
            name: 'training-trade-order',
            path: 'order',
            component: TrainingTradeOrder,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '订单管理',
              sort: 1,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.trade.order'
            },
            children: [
              {
                name: 'training-trade-order-collective-index',
                path: 'collective',
                component: TrainingTradeOrderCollectiveIndex,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  title: '集体报名订单',
                  sort: 2,
                  icon: 'icon_guanli',
                  ownerGroup: [],
                  group: 'training.trade.order.collective'
                }
              },
              {
                name: 'training-trade-order-personal-index',
                path: 'personal',
                component: TrainingTradeOrderPersonalIndex,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  title: '个人报名订单',
                  sort: 1,
                  icon: 'icon_guanli',
                  ownerGroup: [],
                  group: 'training.trade.order.personal'
                }
              },
              {
                name: 'training-trade-order-collective-detail',
                path: 'collective/detail/:id',
                component: TrainingTradeOrderCollectiveDetail,
                meta: {
                  permissionMap: {},
                  isMenu: false,
                  onlyShowOnTab: true,
                  title: '集体报名订单详情',
                  ownerGroup: [],
                  group: 'training.trade.order.collective.detail'
                }
              },
              {
                name: 'training-trade-order-collective-sub-detail',
                path: 'collective/sub-detail/:id/:subId',
                component: TrainingTradeOrderCollectiveSubDetail,
                meta: {
                  permissionMap: {},
                  isMenu: false,
                  onlyShowOnTab: true,
                  title: '集体订单详情',
                  ownerGroup: [],
                  group: 'training.trade.order.collective.sub-detail'
                }
              },
              {
                name: 'training-trade-order-personal-detail',
                path: 'personal/detail/:id',
                component: TrainingTradeOrderPersonalDetail,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  hideMenu: true,
                  onlyShowOnTab: true,
                  title: '个人订单详情',
                  ownerGroup: [],
                  group: 'training.trade.order.personal.detail'
                }
              }
            ]
          },
          {
            name: 'training-trade-reconciliation',
            path: 'reconciliation',
            component: TrainingTradeReconciliation,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '对账管理',
              sort: 4,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.trade.reconciliation'
            },
            children: [
              {
                name: 'training-trade-reconciliation-collective-index',
                path: 'collective',
                component: TrainingTradeReconciliationCollectiveIndex,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  title: '集体报名对账',
                  sort: 2,
                  icon: 'icon_menhuxinxiguanli',
                  ownerGroup: [],
                  group: 'training.trade.reconciliation.collective'
                }
              },
              {
                name: 'training-trade-reconciliation-personal-index',
                path: 'personal',
                component: TrainingTradeReconciliationPersonalIndex,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  title: '个人报名对账',
                  sort: 1,
                  icon: 'icon_menhuxinxiguanli',
                  ownerGroup: [],
                  group: 'training.trade.reconciliation.personal'
                }
              }
            ]
          },
          {
            name: 'training-trade-refund',
            path: 'refund',
            component: TrainingTradeRefund,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '退款管理',
              sort: 2,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.trade.refund'
            },
            children: [
              {
                name: 'training-trade-refund-collective-index',
                path: 'collective',
                component: TrainingTradeRefundCollectiveIndex,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  title: '集体报名退款订单',
                  sort: 2,
                  icon: 'icon_menhuxinxiguanli',
                  ownerGroup: [],
                  group: 'training.trade.refund.collective'
                }
              },
              {
                name: 'training-trade-refund-personal-index',
                path: 'personal',
                component: TrainingTradeRefundPersonalIndex,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  title: '个人报名退款订单',
                  sort: 1,
                  icon: 'icon_menhuxinxiguanli',
                  ownerGroup: [],
                  group: 'training.trade.refund.personal'
                }
              },
              {
                name: 'training-trade-refund-collective-detail',
                path: 'collective/detail/:id',
                component: TrainingTradeRefundCollectiveDetail,
                meta: {
                  permissionMap: {},
                  title: '集体退款详情',
                  ownerGroup: [],
                  group: 'training.trade.refund.collective.detail'
                }
              },
              {
                name: 'training-trade-refund-collective-sub-detail',
                path: 'collective/sub-detail/:id/:subId',
                component: TrainingTradeRefundCollectiveSubDetail,
                meta: {
                  permissionMap: {},
                  title: '集体退款详情',
                  ownerGroup: [],
                  group: 'training.trade.refund.collective.sub-detail'
                }
              },
              {
                name: 'training-trade-refund-personal-detail',
                path: 'personal/detail/:id',
                component: TrainingTradeRefundPersonalDetail,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  hideMenu: true,
                  onlyShowOnTab: true,
                  title: '个人订单退款详情',
                  ownerGroup: [],
                  group: 'training.trade.refund.personal.detail'
                }
              }
            ]
          }
        ]
      },
      {
        name: 'training-user',
        path: 'user',
        component: TrainingUser,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '用户管理',
          sort: 8,
          icon: 'icon-guanliyuan',
          ownerGroup: [],
          group: 'training.user'
        },
        children: [
          {
            name: 'training-user-info',
            path: 'info/:id',
            component: TrainingUserInfo,
            meta: {
              permissionMap: {},
              title: '用户详情',
              requiresAuth: true,
              permission: 'user.view',
              isMenu: false,
              ownerGroup: [],
              group: 'training.user.info'
            }
          },
          {
            name: 'training-user-list',
            path: 'list',
            component: TrainingUserList,
            meta: {
              permissionMap: {},
              title: '用户列表',
              requiresAuth: true,
              permission: 'user.view',
              isMenu: true,
              ownerGroup: [],
              group: 'training.user.list'
            }
          },
          {
            name: 'training-user-collective-account-index',
            path: 'collective-account',
            component: TrainingUserCollectiveAccountIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '集体报名账号管理',
              sort: 2,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'training.user.collective-account'
            }
          },
          {
            name: 'training-user-student-index',
            path: 'student',
            component: TrainingUserStudentIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '学员管理',
              sort: 1,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'training.user.student'
            }
          },
          {
            name: 'training-user-student-detail',
            path: 'student/detail/:id',
            component: TrainingUserStudentDetail,
            meta: {
              permissionMap: {},
              title: '学员详情',
              ownerGroup: [],
              group: 'training.user.student.detail'
            }
          }
        ]
      },
      {
        name: 'training-batch-print-index',
        path: 'batch-print',
        component: TrainingBatchPrintIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '批量打印证明',
          sort: 10,
          icon: 'icon-piliangdayin',
          ownerGroup: [],
          group: 'training.batch-print'
        }
      },
      {
        name: 'training-course-package-index',
        path: 'course-package',
        component: TrainingCoursePackageIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '课程包管理',
          sort: 1,
          icon: 'icon-dabao',
          ownerGroup: [],
          group: 'training.course-package'
        }
      },
      {
        name: 'training-batch-print-import-list-printing',
        path: 'batch-print/import-list-printing',
        component: TrainingBatchPrintImportListPrinting,
        meta: {
          permissionMap: {},
          isMenu: false,
          title: '导入名单打印',
          sort: 8,
          ownerGroup: [],
          group: 'training.batch-print.import-list-printing'
        }
      },
      {
        name: 'training-course-package-create',
        path: 'course-package/create',
        component: TrainingCoursePackageCreate,
        meta: {
          permissionMap: {},
          title: '新建课程包',
          ownerGroup: [],
          group: 'training.course-package.create'
        }
      },
      {
        name: 'training-course-package-detail',
        path: 'course-package/detail/:id',
        component: TrainingCoursePackageDetail,
        meta: {
          permissionMap: {},
          title: '课程包详情',
          ownerGroup: [],
          group: 'training.course-package.detail'
        }
      },
      {
        name: 'training-course-package-import',
        path: 'course-package/import',
        component: TrainingCoursePackageImport,
        meta: {
          permissionMap: {},
          ownerGroup: [],
          group: 'training.course-package.import'
        }
      },
      {
        name: 'training-course-package-modify',
        path: 'course-package/modify/:id',
        component: TrainingCoursePackageModify,
        meta: {
          permissionMap: {},
          title: '修改课程包',
          ownerGroup: [],
          group: 'training.course-package.modify'
        }
      },
      {
        name: 'training-course-package-sync-scheme',
        path: 'course-package/sync-scheme/:id',
        component: TrainingCoursePackageSyncScheme,
        meta: {
          permissionMap: {},
          title: '同步培训方案页面',
          ownerGroup: [],
          group: 'training.course-package.sync-scheme'
        }
      }
    ]
  }
]
