import DateScope from '@api/service/common/models/DateScope'
import PriceScope from '@api/service/common/models/PriceScope'

export default class DistributedGoodsStatisticsOnSalesParams {
  /**
   * 培训商品名称
   */
  trainingProductName = ''
  /**
   * 销售价格
   */
  salesPrice: PriceScope = new PriceScope()
  /**
   * 查询时间
   */
  queryTime: DateScope = new DateScope()
  /**
   * 分销商名称
   */
  distributorName = ''
  /**
   * 供应商名称
   */
  vendorName = ''
  /**
   * 推广门户名称
   */
  portalPromoteTheName = ''
}
