<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-04-11 14:55:45
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2025-01-03 10:36:23
 * @Description:
-->
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/batch-print' }">按学员打印</el-breadcrumb-item>
      <el-breadcrumb-item>导入名单打印</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15 m-table-auto">
      <div class="f-mb15">
        <el-button type="primary" plain @click="downloadTemplate">下载导入模板</el-button>
        <el-button type="primary" @click="importVerify()">导入学员名单</el-button>
      </div>
      <el-tabs v-model="activeName" type="card" class="m-tab-card" @tab-click="handleTabClick">
        <!-- 上传成功 -->
        <el-tab-pane :label="SuccessTotal" name="successTab">
          <el-card shadow="never" class="m-card f-mb15">
            <hb-search-wrapper :model="queryStudentParams" @reset="reset">
              <el-form-item label="姓名">
                <el-input v-model="queryStudentParams.name" clearable placeholder="请输入姓名" />
              </el-form-item>
              <el-form-item label="证件号">
                <el-input v-model="queryStudentParams.idCard" clearable placeholder="请输入证件号" />
              </el-form-item>
              <el-form-item label="培训方案">
                <el-input v-model="queryStudentParams.scheme" clearable placeholder="请输入培训方案名称" />
              </el-form-item>
              <template slot="actions">
                <el-button type="primary" @click="getSuccessList">查询</el-button>
              </template>
            </hb-search-wrapper>
            <div class="f-mb10 f-tr f-mr10">
              <el-button type="danger" plain size="small" @click="removeAll">移除所有人员</el-button>
            </div>
            <!--表格-->
            <el-table stripe :data="tableSuccessData" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="学员姓名" min-width="140" fixed="left">
                <template slot-scope="scope">{{ scope.row.name }}</template>
              </el-table-column>
              <el-table-column label="证件号" min-width="200">
                <template slot-scope="scope">{{ scope.row.idCard }}</template>
              </el-table-column>
              <el-table-column label="所属方案" min-width="240">
                <template slot-scope="scope">{{ scope.row.schemeName }}</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button type="text" size="mini" @click="doDelete(scope.row)">移除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <hb-pagination style="margin-bottom: 25px" :page="successTabPage" v-bind="successTabPage"></hb-pagination>
            <div class="m-btn-bar f-tr is-sticky-1 f-mt20">
              <span class="f-mr20"
                >成功导入 <i class="f-co f-fb">{{ successTotal }}</i> 人</span
              >
              <!-- 成功导入人数为0人，批量打印证明按钮禁用 -->
              <el-button type="warning" class="f-mr20" @click="batchPrintingCertificates" :disabled="successTotal === 0"
                >批量打印证明
              </el-button>
            </div>
          </el-card>
        </el-tab-pane>

        <!-- 上传失败 -->
        <el-tab-pane :label="FailTotal" name="failTab">
          <el-card shadow="never" class="m-card f-mb15">
            <hb-search-wrapper :model="queryStudentParams" @reset="reset">
              <el-form-item label="姓名">
                <el-input v-model="queryStudentParams.name" clearable placeholder="请输入姓名" />
              </el-form-item>
              <el-form-item label="证件号">
                <el-input v-model="queryStudentParams.idCard" clearable placeholder="请输入证件号" />
              </el-form-item>
              <el-form-item label="培训方案">
                <el-input v-model="queryStudentParams.scheme" clearable placeholder="请输入培训方案名称" />
              </el-form-item>
              <template slot="actions">
                <el-button type="primary" @click="getFailList">查询</el-button>
                <el-button type="primary" @click="exportFailData" :loading="exportLoading">导出</el-button>
              </template>
            </hb-search-wrapper>
            <div class="f-mb10 f-tr f-mr10">
              <el-button type="danger" plain size="small" @click="removeAll">移除所有人员</el-button>
            </div>
            <!--表格-->
            <el-table stripe :data="tableFailData" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="学员姓名" min-width="140" fixed="left">
                <template slot-scope="scope">{{ scope.row.name }}</template>
              </el-table-column>
              <el-table-column label="证件号" min-width="200">
                <template slot-scope="scope">{{ scope.row.idCard }}</template>
              </el-table-column>
              <el-table-column label="所属方案" min-width="240">
                <template slot-scope="scope">{{ scope.row.schemeName }}</template>
              </el-table-column>
              <el-table-column label="失败原因" min-width="180">
                <template slot-scope="scope">
                  <span style="color: red">{{ scope.row.failReason }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button type="text" size="mini" @click="doDelete(scope.row)">移除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <hb-pagination :page="failTabPage" v-bind="failTabPage"></hb-pagination>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 批量导入弹窗 -->
    <el-dialog title="批量导入" :visible.sync="openImportDialog" width="500px" class="m-dialog" @close="handleClose">
      <div class="f-mb20 f-mlr50">
        <el-alert type="warning" show-icon :closable="false" class="m-alert">
          操作提示：单次导入上限200条，如超过请分次导入，否则无法操作。
        </el-alert>
      </div>
      <!-- 上传组件 -->
      <batch-import-upload-file
        ref="batchImportUploadFileRef"
        v-model="hbFileUploadResponse"
        :file-type="1"
        @batchUpload="batchUpload"
      ></batch-import-upload-file>
      <div slot="footer">
        <el-button @click.stop="downloadTemplate">下载模板</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="batchImport()">确定</el-button>
      </div>
    </el-dialog>

    <!-- 异步任务-进度条弹窗 -->
    <el-dialog :show-close="false" :visible.sync="progressDialog" width="500px" class="m-dialog" @close="clearTimer()">
      <p class="f-tc f-mb20">{{ SubtasksTotal }} 条人员信息上传中，请稍等…</p>
      <el-progress :percentage="Math.floor(uploadProcess)" class="f-mlr50 f-pb40"></el-progress>
    </el-dialog>
    <!-- 上传数据异常-异常弹窗 -->
    <el-dialog :show-close="false" :visible.sync="ErrorDialog" width="450px" class="m-dialog" @close="clearTimer()">
      <div style="display: flex; flex-direction: column; align-items: end">
        <div class="dialog-alert is-big f-pb30">
          <i class="icon el-icon-warning warning"></i>
          <div class="txt">
            <p class="f-fb f-f16">{{ erroMessage }}</p>
          </div>
        </div>
        <div></div>
        <el-button v-if="isMaxError" type="primary" @click="ErrorDialog = false">确定</el-button>
      </div>
    </el-dialog>
    <!-- 批量打印证书弹窗 -->
    <batch-select-training-printing-drawer
      :queryStudentParams="queryStudentParams"
      ref="batchSelectTrainingPrintingDrawerRef"
    ></batch-select-training-printing-drawer>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import { bind, debounce } from 'lodash-decorators'
  import { UiPage } from '@hbfe/common'
  import ImportPrintStudent from '@api/service/management/personal-leaning/import-print/ImportPrintStudent'
  import BatchImportUploadFile from '@hbfe/jxjy-admin-batchPrint/src/components/batch-import-upload-file.vue'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import QueryStudentParams from '@api/service/management/personal-leaning/import-print/model/QueryStudentParams'
  import ImportStudentItem from '@api/service/management/personal-leaning/import-print/model/ImportStudentItem'
  import { ImportStatusEnum } from '@api/service/management/personal-leaning/import-print/enums/ImportStatus'
  import BatchSelectTrainingPrintingDrawer from '@hbfe/jxjy-admin-batchPrint/src/diff/scjzs/__components__/batch-select-training-printing-drawer.vue'
  import UserModule from '@api/service/management/user/UserModule'
  import Downloader from '@api/service/common/utils/Downloader'
  import QueryShowOffline from '@api/service/common/config/QueryShowOffline'

  @Component({
    components: { BatchImportUploadFile, BatchSelectTrainingPrintingDrawer }
  })
  export default class extends Vue {
    @Ref('batchImportUploadFileRef') batchImportUploadFileRef: BatchImportUploadFile

    @Ref('batchSelectTrainingPrintingDrawerRef')
    batchSelectTrainingPrintingDrawerRef: BatchSelectTrainingPrintingDrawer

    // 上传成功tab分页
    successTabPage = new UiPage()
    // 上传失败tab分页
    failTabPage = new UiPage()

    constructor() {
      super()
      this.failTabPage = new UiPage(this.getFailList, this.getFailList)
      this.successTabPage = new UiPage(this.getSuccessList, this.getSuccessList)
    }

    // 搜索参数
    queryStudentParams = new QueryStudentParams()
    // 导入业务类
    importPrintStudent = new ImportPrintStudent()
    // 上传文件
    importListVerificationRequest = new HBFileUploadResponse()
    // 文件上传信息类
    hbFileUploadResponse = new HBFileUploadResponse()
    // 激活tab
    activeName = 'successTab'
    // 上传成功人数
    successTotal = 0
    // 上传失败人数
    failTotal = 0
    // 批量导入弹窗
    openImportDialog = false
    // 文件解析进度状态
    uploadProcess = 0
    // 进度条弹窗
    progressDialog = false
    // 导出列表数据按钮loading加载
    exportLoading = false
    // 导入任务id
    mainTaskID = ''
    // 列表成功数据
    tableSuccessData = new Array<ImportStudentItem>()
    // 列表失败数据
    tableFailData = new Array<ImportStudentItem>()
    // 主任务状态
    taskStatus = 0
    // 子任务总数
    SubtasksTotal = 0
    // 子任务完成数
    SubtasksFinishNumber = 0
    // 用户id
    userId = ''
    // 选择培训证明进行打印弹窗 数据源
    source = 'importListPrint'
    // 延时器id
    timerId: any = null
    //上传数据异常弹窗显示
    ErrorDialog = false
    //异常信息
    erroMessage = '系统异常，请联系平台技术支撑方'
    //是否是上传数据条数超200条
    isMaxError = false

    // 列表成功数据总数
    get SuccessTotal() {
      return '上传成功：' + '（' + this.successTotal + '）'
    }

    // 列表失败数据总数
    get FailTotal() {
      return '上传失败：' + '（' + this.failTotal + '）'
    }

    /**
     * 是否展示面网授
     */
    get isOnlineClassSupport() {
      const show = QueryShowOffline.getShowOfflineApolloConfig()
      return !show
    }

    async activated() {
      // // 初始化查询最新进度任务
      await this.queryLatestProgress()
      // 初始化查询列表
      this.activeName == 'successTab' ? await this.getSuccessList() : await this.getFailList()
      // 获取下单的成功和失败条数
      await this.getTotal()
    }

    deactivated() {
      this.clearTimer()
    }

    beforeDestroy() {
      this.clearTimer()
    }

    //下载导入模板
    @bind
    @debounce(1000)
    async downloadTemplate() {
      const link = document.createElement('a')
      let resolverName = ''
      if (this.isOnlineClassSupport) {
        resolverName =
          '/mfs/ms-file/public/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/template/导入学员名单打印证明模板.xls'
      } else {
        resolverName =
          '/mfs/ms-file/public/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/template/批量导入人员名单打印.xls'
      }
      const resolver = this.$router.resolve({
        name: resolverName
      })
      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      const urlArr = link.href.split('.'),
        typeName = urlArr.pop()
      link.setAttribute('download', '批量导入人员名单打印')
      document.body.appendChild(link)
      link.click()
      link.remove()
    }

    // 导入学员名单前置校验
    async importVerify() {
      const mainTaskId = await this.queryLatestProgress()
      this.openImportDialog = mainTaskId ? false : true
    }

    // 批量导入确定按钮
    async batchImport() {
      try {
        const res = await this.importPrintStudent.importStudentList(
          this.importListVerificationRequest.url,
          this.importListVerificationRequest.fileName
        )
        if (res.isSuccess()) {
          // 导入任务id赋值
          this.mainTaskID = this.importPrintStudent.mainTaskId
          console.log('主任务id', this.mainTaskID)
          // 关闭导入弹窗
          this.openImportDialog = false
          // 打开进度弹窗
          this.progressDialog = true
          // 查询处理进度
          this.timerId = setTimeout(async () => {
            await this.getProcess()
          }, 1000)
        } else {
          this.erroMessage = res.getMessage()
          this.isMaxError = true
          this.ErrorDialog = true
          console.log('报错了')
        }
      } catch (e) {
        console.log('e', e)
      }
    }

    // 查询
    async searchList() {
      await this.getSuccessList()
      await this.getFailList()
    }

    // 重置
    async reset() {
      this.successTabPage.pageNo = 1
      this.failTabPage.pageNo = 1
      this.queryStudentParams = new QueryStudentParams()
      await this.searchList()
    }

    batchUpload(value: HBFileUploadResponse) {
      this.importListVerificationRequest = value
    }

    // 查询处理进度-进度信息
    async getProcess() {
      const res = await this.importPrintStudent.queryProcessingProgress()
      console.log('进度信息res', res)
      this.taskStatus = res.taskStatus
      this.SubtasksFinishNumber = res.SubtasksFinishNumber
      this.SubtasksTotal = res.SubtasksTotal

      // 获取异步任务进度
      this.uploadProcess = this.SubtasksTotal === 0 ? 0 : (this.SubtasksFinishNumber / this.SubtasksTotal) * 100
      //   如果进度到100%的时候，弹窗隐藏
      if (this.uploadProcess === 100 && this.taskStatus === 3) {
        this.progressDialog = false
        // 获取下单的成功和失败条数
        await this.getTotal()
        await this.getSuccessList()
        await this.getFailList()
        return
      }
      if (res.result != 1 && this.taskStatus === 3) {
        this.progressDialog = false
        this.ErrorDialog = true
      }
      //   已经完成的任务数不等于总数或者完成的任务数为0，就要开始轮询
      if (Number(res.SubtasksFinishNumber) != Number(res.SubtasksTotal) || res.SubtasksFinishNumber === 0) {
        this.timerId = setTimeout(async () => {
          await this.getProcess()
        }, 1000)
      }
    }

    // 删除单行
    async doDelete(item: ImportStudentItem) {
      this.$confirm('请确认是否要移除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true
      }).then(async () => {
        try {
          const res = await item.remove()
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '移除成功!'
            })
            // 查询成功和失败的条数
            await this.getTotal()
            await this.searchList()
          }
        } catch (e) {
          console.log(e)
        } finally {
          console.log('删除单行数据')
        }
      })
    }

    //切换tab
    async handleTabClick() {
      if (this.activeName === 'successTab') {
        this.queryStudentParams = new QueryStudentParams()
        this.successTabPage.pageNo = 1
        await this.getSuccessList()
      } else {
        this.queryStudentParams = new QueryStudentParams()
        this.failTabPage.pageNo = 1
        await this.getFailList()
      }
    }

    // 移除所有人员
    async removeAll() {
      if (this.activeName === 'successTab') {
        this.$confirm('确认移除所有人员?', '提示', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          center: true
        }).then(async () => {
          this.queryStudentParams.type = ImportStatusEnum.success
          const res = await this.importPrintStudent.removeAll(this.queryStudentParams)
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            setTimeout(() => {
              this.getTotal()
              this.getSuccessList()
            }, 1000)
          }
        })
      } else {
        this.$confirm('请确认是否删除所有失败数据，建议您在删除前导出备份', '提示', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          center: true
        }).then(async () => {
          this.queryStudentParams.type = ImportStatusEnum.fail
          const res = await this.importPrintStudent.removeAll(this.queryStudentParams)
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            setTimeout(() => {
              this.getTotal()
              this.getFailList()
            }, 1000)
          }
        })
      }
    }

    // 导出
    async exportFailData() {
      // loading开启
      this.exportLoading = true
      this.queryStudentParams.type = ImportStatusEnum.fail
      try {
        const res = await this.importPrintStudent.exportFailData(this.queryStudentParams)
        //   如果有文件导出路径
        if (res) {
          const fileNamePathList = res.split('/')
          const fileName = fileNamePathList[fileNamePathList.length - 1]
          const authorization = localStorage.getItem('admin.Access-Token')
          const downloader = new Downloader('/mfs' + res, fileName, {
            ['Authorization']: authorization
          })
          downloader.download()
        } else {
          this.$message.error('文件不存在！')
        }
      } catch (e) {
        console.log('e', e)
      } finally {
        this.exportLoading = false
      }
    }

    // 获取下单成功的报名人数
    async getTotal() {
      const res = await this.importPrintStudent.queryTotalStatistics()
      this.successTotal = res.success
      this.failTotal = res.fail
    }

    // 查询成功列表
    async getSuccessList() {
      this.queryStudentParams.type = ImportStatusEnum.success
      this.tableSuccessData = await this.importPrintStudent.queryList(this.successTabPage, this.queryStudentParams)
    }

    // 查询失败列表
    async getFailList() {
      this.queryStudentParams.type = ImportStatusEnum.fail
      this.tableFailData = await this.importPrintStudent.queryList(this.failTabPage, this.queryStudentParams)
    }

    // 批量打印证明
    batchPrintingCertificates() {
      // 打开抽屉
      this.batchSelectTrainingPrintingDrawerRef.open(this.source)
    }

    // 关闭弹窗
    handleClose() {
      this.batchImportUploadFileRef.handleRemove(null, undefined)
      this.openImportDialog = false
    }

    // 查询最新进展进度任务
    async queryLatestProgress() {
      this.userId = UserModule.queryUserFactory.queryManagerDetail.adminInfo.userInfo.userId
      await this.importPrintStudent.queryLatestProgress(this.userId)
      //判断主任务id是否存在
      if (this.importPrintStudent.mainTaskId) {
        // 查进度
        this.progressDialog = true
        this.timerId = setTimeout(async () => {
          await this.getProcess()
        }, 1000)
      } else {
        this.clearTimer()
      }
      return this.importPrintStudent.mainTaskId
    }

    /**
     * @description 清除延时器
     * */
    clearTimer() {
      clearTimeout(this.timerId)
      this.timerId = null
      this.progressDialog = false
    }
  }
</script>
