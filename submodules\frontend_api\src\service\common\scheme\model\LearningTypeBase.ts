import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'

/**
 * @description 学习方式基础信息
 */
class LearningTypeBase {
  /**
   * 配置id
   */
  configId = ''
  /**
   * 学习方式id
   */
  learningTypeId = ''
  /**
   * 学习方式考核id
   */
  assessId = ''
  /**
   * 学习方式考核名称
   */
  assessName = ''
  /**
   *是否有配置
   */
  isSelected = false
  /**
   *是否有考核
   */
  isAssess = false
  /**
   * 操作类型1创建2修改3删除
   */
  operation: OperationTypeEnum = undefined
}

export default LearningTypeBase
