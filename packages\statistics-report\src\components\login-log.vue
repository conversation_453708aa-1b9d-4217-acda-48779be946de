<!-- 登录日志 -->
<template>
  <!-- 登录日志 -->
  <div class="f-p20">
    <el-table stripe :data="tableData" max-height="500" highlight-current-row class="m-table">
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="登录时间" min-width="240">
        <template>2020-04-16 15:58</template>
      </el-table-column>
      <el-table-column label="登录IP" min-width="240">
        <template>**************</template>
      </el-table-column>
      <el-table-column label="拍摄照片" min-width="150">
        <template>
          <img src="@design/admin/assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
        </template>
      </el-table-column>
      <el-table-column label="人脸识别结果" min-width="140" align="center">
        <template slot-scope="scope">
          <div v-if="scope.$index === 0">
            <el-badge is-dot type="danger" class="badge-status">不匹配</el-badge>
          </div>
          <div v-else>
            <el-badge is-dot type="success" class="badge-status">匹配</el-badge>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    // 登录日志
    @Prop({
      type: Object,
      default: () => {
        return {}
      }
    })
    loginLog: object

    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
  }
</script>
