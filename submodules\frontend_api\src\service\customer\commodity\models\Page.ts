/**
 * 分页对象
 * @<PERSON> fangkunsen
 * @Date 2021/2/23/0023 9:36
 * newPage对象给机构主页，渠道商主页分页使用
 */
export class NewPage {
  pageNo?: number
  pageSize?: number
}
export class Page {
  pageNo: number
  pageSize: number
  totalSize?: number
  currentPageData?: Array<any>

  /**
   * 手动分页
   * @param list
   * @param pageNo
   * @param pageSize
   */
  static buildPage<T>(list: Array<T>, pageNo: number, pageSize: number) {
    const page = new Page()
    let pageList = new Array<T>()
    const startIndex = pageSize * (pageNo - 1)
    if (list.length >= startIndex + 1) {
      if (list.length >= startIndex + pageSize + 1) {
        pageList = list.slice(startIndex, startIndex + pageSize)
        page.pageNo = pageNo
      } else {
        pageList = list.slice(startIndex, list.length)
        page.pageNo = pageNo
      }
    } else {
      if (list.length >= pageSize) {
        pageList = list.slice(0, pageSize)
        page.pageNo = 1
      } else {
        pageList = list.slice(0, list.length)
        page.pageNo = 1
      }
    }
    page.pageSize = pageSize
    page.totalSize = list.length
    page.currentPageData = pageList
    return page
  }
}
