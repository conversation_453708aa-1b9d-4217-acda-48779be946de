import QueryCoursePlayResource from '@api/service/customer/learning/course/QueryCoursePlayResource'
import { bind, debounce } from 'lodash-decorators'
import { EventEmitter } from 'events'
import PlayCourseEventTypeEnum from '@api/service/customer/learning/course/play-course/enums/PlayCourseEventTypeEnum'
import MediaPlayResource from '@api/service/customer/learning/course/vo/MediaPlayResource'
import Courseware from '@api/service/customer/learning/course/vo/Courseware'
import Chapter from '@api/service/customer/learning/course/vo/Chapter'
import PlayIndex from '@api/service/customer/learning/course/vo/PlayIndex'
import LearningCourseDetail from '@api/service/customer/learning/course/vo/LearningCourseDetail'
import CourseDetail from '@api/service/customer/learning/course/vo/CourseDetail'
import MsCoursePlayResourceV1 from '@api/ms-gateway/ms-course-play-resource-v1'
import PlayCourseResultStatus from '@api/service/customer/learning/course/vo/PlayCourseResultStatus'
import LearningEventStore from '@api/service/customer/learning/LearningEventStore'
import { ResponseStatus } from '@hbfe/common'
import PlayCourseError from '@api/service/customer/learning/course/play-events/PlayCourseError'
import MsCourseLearningQueryFrontGatewayCourseLearningForestage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

/**
 * 用户播放课程
 */
abstract class UserPlayCourse extends EventEmitter {
  constructor(token?: string) {
    super()
    this.coursePlayToken = token
  }

  playCourseResultStatus: PlayCourseResultStatus = new PlayCourseResultStatus()

  // 当前课程播放章节、课件的索引
  currentPlayIndex: PlayIndex = new PlayIndex()
  // 标识是否学习过
  isLearned = false
  // 是否自动播放下一个， 默认给 true
  autoPlayNext = true
  // 课程播放 token
  coursePlayToken = ''
  // 查询课程学习资源
  queryCourseLearningResource: QueryCoursePlayResource
  // 课程详情
  courseDetail: CourseDetail | LearningCourseDetail = new CourseDetail()
  // 课程播放资源
  playCourseResource: MediaPlayResource = new MediaPlayResource()
  // 当前是否是音频播放
  isAudioPlay = false

  /**
   * 初始化播放
   * @param coursePlayToken 课程播放的 token
   */
  async init(coursePlayToken: string, withoutVerify = false) {
    this.coursePlayToken = coursePlayToken
    // 初始化查询课程学习资源
    this.queryCourseLearningResource = new QueryCoursePlayResource(this.coursePlayToken)
    try {
      this.courseDetail = await this.queryCourseLearningResource.queryCourseResource(withoutVerify)
    } catch (e) {
      this.emit(
        PlayCourseEventTypeEnum.error,
        new PlayCourseError(PlayCourseEventTypeEnum.loadCourseResourceError, '课程内容异常')
      )
    }

    // 获取学过该课程人数
    const countRes = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.getCourseLearnStatistics(
      this.courseDetail.id
    )
    if (countRes.status.code === 200) {
      if (countRes.data?.courseLearnPeopleCount) {
        this.courseDetail.learnedCount =
          countRes.data?.courseLearnPeopleCount > 10000
            ? countRes.data?.courseLearnPeopleCount / 10000 + '万'
            : countRes.data?.courseLearnPeopleCount.toString()
      }
    }
  }

  /**
   * 是否有音频内容
   */
  hasAudio() {
    return this.playCourseResource?.hasAudio()
  }

  /**
   * 切换播放媒体，目录切换的时候会将播放的索引传递到上面。
   * @param event
   */
  async changeCurrentPlayMedia(event: {
    chapterIndex: number
    coursewareIndex: number
    playTime?: number
    isPause?: boolean
  }) {
    // 切换当前在播放的课件将无效
    const chapter = this.courseDetail.chapters[event.chapterIndex]
    const courseware = chapter.coursewares[event.coursewareIndex]
    this.currentPlayIndex.chapterIndex = event.chapterIndex
    this.currentPlayIndex.coursewareIndex = event.coursewareIndex
    this.emit(PlayCourseEventTypeEnum.beforeChangePlayCourseware)
    try {
      // 获取课件播放资源
      this.playCourseResource = await this.applyCoursewarePlayResource({
        coursePlayToken: this.coursePlayToken,
        chapterId: courseware.belongChapterId,
        coursewareId: courseware.id,
        mediaResourceId: courseware.mediaResourceId
      })

      if (!this.playCourseResource.hasSupportResources()) {
        return this.emit(
          PlayCourseEventTypeEnum.error,
          new PlayCourseError(PlayCourseEventTypeEnum.sourceNotSupport, '当前课件资源类型暂不支持')
        )
      }

      LearningEventStore.emit(PlayCourseEventTypeEnum.playNext)
      this.emit(PlayCourseEventTypeEnum.afterChangePlayCourseware)
      // 重置错误
      this.playCourseResultStatus.reset()
      this.emit(PlayCourseEventTypeEnum.prepareSuccess, event)
    } catch (e) {
      console.log(e)
      this.emit(PlayCourseEventTypeEnum.error, e.message)
    }
  }

  /**
   * 获取课件播放资源、配置信息
   * @param applyRequest
   * @private
   */
  private async applyCoursewarePlayResource(applyRequest: {
    coursePlayToken: string
    chapterId: string
    coursewareId: string
    mediaResourceId: string
  }) {
    const { status, data } = await MsCoursePlayResourceV1.applyCoursewareMediaPlayAntiTheftChainResource(applyRequest)
    if (status.isSuccess()) {
      return MediaPlayResource.from(data)
    }
    return Promise.reject(new ResponseStatus(500, status.getMessage()))
  }

  /**
   * 根据担课件 id， 媒体 id， 定位当前播放的位置
   * @param coursewareId
   * @param multiMediaId
   */
  getLastPlayIndexByCoursewareId(coursewareId: string, multiMediaId: string) {
    let outSideChapterIndex = 0
    let outSideCoursewareIndex = 0
    this.courseDetail.chapters.forEach((chapter: Chapter, chapterIndex: number) => {
      const coursewareIndex = chapter.coursewares.findIndex((courseware: Courseware) => {
        return courseware.id === coursewareId && courseware.multiMediaId === multiMediaId
      })
      if (coursewareIndex !== -1) {
        outSideChapterIndex = chapterIndex
        outSideCoursewareIndex = coursewareIndex
      }
    })
    return {
      chapterIndex: outSideChapterIndex,
      coursewareIndex: outSideCoursewareIndex
    }
  }

  /**
   * 播放下一个课件
   *  自动跳过章节
   */
  @bind
  @debounce(300)
  async playNext() {
    const getNextIndex = this.getNextIndex()
    if (!getNextIndex) return
    await this.changeCurrentPlayMedia(getNextIndex)
  }

  /**
   * 播放上一个课件
   *  自动跳过章节
   */
  @bind
  @debounce(300)
  async playPrev() {
    const getPrevIndex = this.getPrevIndex()
    if (!getPrevIndex) return
    await this.changeCurrentPlayMedia(getPrevIndex)
  }

  /**
   * 判断是否有下一个课程
   */
  hasNext() {
    if (!this.courseDetail.chapters.length) return true
    const { coursewareIndex, chapterIndex } = this.currentPlayIndex
    const chapterLength = this.courseDetail.chapters.length - 1
    const lastChapterCoursewareLength = this.courseDetail.chapters[chapterLength].coursewares.length - 1
    const flag = chapterIndex === chapterLength && coursewareIndex < lastChapterCoursewareLength
    return flag || chapterIndex < chapterLength
  }

  /**
   * 判断是否有上一个课程
   */
  hasPrev() {
    const { coursewareIndex, chapterIndex } = this.currentPlayIndex
    return (!chapterIndex && coursewareIndex > 0) || chapterIndex > 0
  }

  getNextIndex() {
    if (!this.hasNext()) return undefined
    const { coursewareIndex, chapterIndex } = this.currentPlayIndex
    let newCoursewareIndex = coursewareIndex
    let newChapterIndex = chapterIndex
    if (coursewareIndex === this.courseDetail.chapters[chapterIndex].coursewares.length - 1) {
      newChapterIndex += 1
      newCoursewareIndex = 0
    } else {
      newCoursewareIndex += 1
    }
    return {
      chapterIndex: newChapterIndex,
      coursewareIndex: newCoursewareIndex
    }
  }

  getPrevIndex() {
    if (!this.hasPrev()) return undefined
    const { coursewareIndex, chapterIndex } = this.currentPlayIndex
    let newCoursewareIndex = coursewareIndex
    let newChapterIndex = chapterIndex
    if (coursewareIndex === 0) {
      newChapterIndex -= 1
      newCoursewareIndex = this.courseDetail.chapters[newChapterIndex].coursewares.length - 1
    } else {
      newCoursewareIndex -= 1
    }
    return {
      chapterIndex: newChapterIndex,
      coursewareIndex: newCoursewareIndex
    }
  }

  /**
   * 切换为音频播放
   */
  @bind
  @debounce(200)
  toggleAudioPlay() {
    this.isAudioPlay = !this.isAudioPlay
    this.emit(PlayCourseEventTypeEnum.toggleAudioPlay, this.isAudioPlay)
  }

  /**
   * 切换为视频播放状态
   */
  changeToVideoPlay() {
    this.isAudioPlay = false
  }

  /**
   * 销毁吧
   */
  destroy() {
    this.removeAllListeners()
    this.eventNames().forEach((key: any) => {
      this.removeListener(key, null as any)
    })
  }

  /**
   * 获取当前播放的课件信息
   */
  get currentPlayCourseware(): Courseware {
    const { chapters } = this.courseDetail
    if (
      !chapters[this.currentPlayIndex.chapterIndex] ||
      !chapters[this.currentPlayIndex.chapterIndex].coursewares ||
      !chapters[this.currentPlayIndex.chapterIndex].coursewares.length
    ) {
      return new Courseware()
    }
    return chapters[this.currentPlayIndex.chapterIndex].coursewares[this.currentPlayIndex.coursewareIndex]
  }
}

export default UserPlayCourse
