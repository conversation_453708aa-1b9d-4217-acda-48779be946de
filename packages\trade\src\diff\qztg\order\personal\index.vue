<route-meta>
  {
  "isMenu": true,
  "title": "个人报名订单",
  "sort": 1,
  "icon": "icon_guanli"
  }
</route-meta>
<template>
  <el-main>
    <div class="f-p15">
      <template
        v-if="$hasPermission('query,queryFx,queryZt')"
        desc="query:查询,queryFx:查询（分销）,queryZt:查询（专题）"
        query
        actions="query:queryPageList,@LearningSchemeSelect,@BizReceiveAccountDrawer,@BizDistributorSelect,@BizPortalSelect,restQuery
        #queryFx:queryPageListFXS,@FxLearningSchemeSelect,@BizReceiveAccountDrawer,@BizPortalDistributorSelect,restQuery
        #queryZt:queryPageListZt,@LearningSchemeSelect,restQuery"
      >
        <el-card shadow="never" class="m-card f-mb15">
          <hb-search-wrapper @reset="restQuery">
            <template
              v-if="$hasPermission('editInvoicePopup')"
              query
              desc="选择收款账号"
              actions="@BizReceiveAccountDrawer"
            >
              <el-form-item label="收款账号" v-if="!isZtlogin">
                <biz-receive-account-drawer
                  ref="receiveAccountDrawerRef"
                  v-model="receiveAccountList"
                ></biz-receive-account-drawer>
              </el-form-item>
            </template>
            <el-form-item label="订单号">
              <el-input v-model="queryParams.orderNo" clearable placeholder="请输入订单号" />
            </el-form-item>
            <el-form-item label="登录账号" v-if="queryShowLoginAccount.isShowLoginAccount">
              <el-input v-model="queryParams.loginAccount" clearable placeholder="请输入请输入省平台ID" />
            </el-form-item>
            <el-form-item label="证件号">
              <el-input v-model="queryParams.idCard" clearable placeholder="请输入证件号" />
            </el-form-item>
            <el-form-item label="姓名">
              <el-input v-model="queryParams.userName" clearable placeholder="请输入姓名" />
            </el-form-item>
            <el-form-item label="交易状态">
              <el-select v-model="queryParams.orderStatus" clearable filterable placeholder="请选择交易状态">
                <el-option
                  v-for="item in tradeStatusList"
                  :key="item.code"
                  :value="item.code"
                  :label="item.desc"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="缴费渠道">
              <el-select v-model="queryParams.terminalCode" clearable filterable placeholder="请选择缴费渠道">
                <el-option
                  v-for="item in terminalCodeList"
                  :key="item.code"
                  :value="item.code"
                  :label="item.desc"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="交易流水号">
              <el-input v-model="queryParams.flowNo" clearable placeholder="请输入交易流水号" />
            </el-form-item>
            <el-form-item label="培训方案" v-if="!isFxlogin && !isZtlogin">
              <biz-learning-scheme-select v-model="commoditySkuIdList"></biz-learning-scheme-select>
              <!-- <learning-scheme-select v-model="commoditySkuIdList"></learning-scheme-select> -->
            </el-form-item>

            <el-form-item label="培训方案" v-if="isFxlogin">
              <fx-learning-scheme-select v-model="commoditySkuIdList"></fx-learning-scheme-select>
              <!-- <learning-scheme-select v-model="commoditySkuIdList"></learning-scheme-select> -->
            </el-form-item>
            <el-form-item label="培训方案" v-if="isZtlogin">
              <zt-learning-scheme-select v-model="commoditySkuIdList"></zt-learning-scheme-select>
            </el-form-item>
            <el-form-item label="期别名称" v-if="showPeriodName && !isFxlogin">
              <biz-period-select :scheme-id="commoditySkuIdList[0].id" v-model="queryParams.periodId" />
            </el-form-item>
            <el-form-item label="期别名称" v-if="showPeriodName && isFxlogin">
              <biz-fx-period-select :scheme-id="commoditySkuIdList[0].id" v-model="queryParams.periodId" />
            </el-form-item>
            <el-form-item label="销售渠道" v-if="!isFxlogin && !isZtlogin">
              <el-select v-model="queryParams.saleChannel" clearable filterable placeholder="请选择销售渠道">
                <el-option
                  v-for="item in SaleChannelType"
                  :key="item.code"
                  :value="item.code"
                  :label="item.desc"
                ></el-option>
              </el-select>
            </el-form-item>

            <!--  v-if="isFXshow" -->
            <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="分销商">
              <biz-distributor-select
                v-model="queryParams.distributorId"
                :disabled="queryParams.isDistributionExcludePortal"
                :name="distributorName"
              ></biz-distributor-select>
            </el-form-item>
            <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
              <biz-portal-select
                v-model="queryParams.promotionPortalId"
                :disabled="queryParams.isDistributionExcludePortal"
                :name="promotionPortalName"
              ></biz-portal-select>
            </el-form-item>
            <el-form-item v-if="isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
              <biz-portal-distributor-select
                :disabled="queryParams.isDistributionExcludePortal"
                v-model="queryParams.promotionPortalId"
                :name="promotionPortalName"
              ></biz-portal-distributor-select>
            </el-form-item>
            <el-form-item v-if="!isZtlogin">
              <el-checkbox
                label="查看非门户推广数据"
                name="type"
                @change="queryParams.promotionPortalId = ''"
                v-model="queryParams.isDistributionExcludePortal"
              ></el-checkbox>
            </el-form-item>

            <el-form-item label="专题名称" v-if="topPicNameFilterShow && !isFxlogin">
              <el-input v-model="queryParams.specialSubjectName" clearable placeholder="请输入专题进行查询" />
            </el-form-item>

            <!--  v-if="isFXshow" -->
            <el-form-item label="订单创建时间">
              <double-date-picker
                :begin-create-time.sync="queryParams.orderCreateTime[0]"
                :end-create-time.sync="queryParams.orderCreateTime[1]"
                begin-time-placeholder="订单创建时间"
                end-time-placeholder="订单创建时间"
              ></double-date-picker>
            </el-form-item>
            <el-form-item label="交易成功时间">
              <double-date-picker
                :begin-create-time.sync="queryParams.paymentCompleteTime[0]"
                :end-create-time.sync="queryParams.paymentCompleteTime[1]"
                begin-time-placeholder="交易成功时间"
                end-time-placeholder="交易成功时间"
              ></double-date-picker>
            </el-form-item>
            <el-form-item>
              <el-checkbox label="剔除 0 元订单" name="type" v-model="queryParams.isRemoveZeroOrder"></el-checkbox>
            </el-form-item>
            <template slot="actions">
              <template
                v-if="$hasPermission('search,searchFx,searchZt')"
                query
                desc="search:查询,searchFx:查询（分销）,searchZt:查询（专题）"
                actions="search:queryPageList#searchFx:queryPageListFXS#searchZt:queryPageListZt"
              >
                <el-button type="primary" @click="initRequest">查询</el-button>
              </template>
              <template
                v-if="$hasPermission('export,exportFx,exportZt')"
                query
                desc="export:导出,exportFx:导出（分销）,exportZt:导出（专题）"
                actions="export:doExportListTy#exportFx:doExportListFx#exportZt:doExportListZt"
              >
                <el-button @click="doExportList" :loading="uiConfig.loadBtn.exporting">导出列表数据</el-button>
              </template>
              <template v-if="$hasPermission('exportFinanceData')" desc="导出财务数据" actions="exportFinanceData">
                <el-button @click="exportFinanceData">导出中心财务数据表</el-button>
              </template>
            </template>
          </hb-search-wrapper>
          <!--操作栏-->
          <div class="f-mt20">
            <el-alert
              type="warning"
              :closable="false"
              class="m-alert"
              v-loading="uiConfig.loadStatus.loadTotalOrderAndMoney"
            >
              <div class="f-c6">
                当前共有
                <span class="f-fb f-co">{{ page.totalSize }}</span> 笔订单
                <span v-show="uiConfig.showStatus.isShowTotalOrderAmount">
                  ，成交总额
                  <span class="f-fb f-co">¥ {{ OrderTotalPriceAndCount.totalOrderAmount }}</span>
                </span>
              </div>
            </el-alert>
          </div>
          <!--表格-->
          <el-table
            stripe
            :data="tableOrderData"
            ref="tableOrderRef"
            max-height="500px"
            class="m-table f-mt10"
            v-loading="uiConfig.loadStatus.loadPage"
          >
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="订单号" min-width="220">
              <template slot-scope="scope">
                <div>
                  <div>
                    {{ scope.row.orderNo }}
                    <hb-copy :content="scope.row.orderNo"></hb-copy>
                  </div>
                  <!-- TODO 是否显示专题 -->
                  <el-tag type="success" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.topic"
                    >专题
                  </el-tag>
                  <!-- 是否显示换班 -->
                  <el-tag
                    size="small"
                    v-if="
                      scope.row.changeOrderStatus && scope.row.changeOrderStatus.includes(ChangeOrderType.CLASS_TYPE)
                    "
                    >换班</el-tag
                  >
                  <el-tag
                    type="warning"
                    size="small"
                    v-if="
                      scope.row.changeOrderStatus && scope.row.changeOrderStatus.includes(ChangeOrderType.PERIOD_TYPE)
                    "
                    >换期</el-tag
                  >
                  <el-tag type="warning" size="small" v-if="scope.row.commodityAuthInfo">分销推广</el-tag>
                  <el-tag type="danger" size="small" v-if="scope.row.thirdPartyPlatform">华医网</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="购买内容" min-width="280">
              <template slot-scope="scope">
                {{ scope.row.commodityName.join(';') }}
                <p v-if="scope.row.trainingPeriodName">
                  <el-tag type="info" size="mini">培训期别</el-tag>
                  {{ scope.row.trainingPeriodName }}
                </p>
              </template>
            </el-table-column>
            <el-table-column label="购买人信息" min-width="240">
              <template slot-scope="scope">
                <p>姓名：{{ scope.row.buyerInfo.userName }}</p>
                <p v-if="queryShowLoginAccount.isShowLoginAccount">
                  登录账号：{{ scope.row.buyerInfo.loginAccount ? scope.row.buyerInfo.loginAccount : '-' }}
                </p>
                <p>证件号：{{ scope.row.buyerInfo.userIdCard }}</p>
                <p>手机号：{{ scope.row.buyerInfo.userPhoneNumber ? scope.row.buyerInfo.userPhoneNumber : '-' }}</p>
              </template>
            </el-table-column>
            <el-table-column label="订单创建时间" min-width="180" prop="createTime"></el-table-column>
            <el-table-column label="交易成功时间" min-width="180" prop="transactionCompletionTime"></el-table-column>
            <el-table-column label="状态" min-width="120">
              <template slot-scope="scope">
                <div v-if="scope.row.orderStatus === 2">
                  <el-badge is-dot type="primary" class="badge-status">支付中</el-badge>
                </div>
                <div v-else-if="scope.row.orderStatus === 4">
                  <el-badge is-dot type="success" class="badge-status">交易成功</el-badge>
                </div>
                <div v-else-if="scope.row.orderStatus === 1">
                  <el-badge is-dot type="warning" class="badge-status">等待付款</el-badge>
                </div>
                <div v-else-if="scope.row.orderStatus === 3">
                  <el-badge is-dot type="primary" class="badge-status">开通中</el-badge>
                </div>
                <div v-else>
                  <el-badge is-dot type="info" class="badge-status">交易关闭</el-badge>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="实付金额(元)" width="140" align="right">
              <template slot-scope="scope">
                <div v-if="!scope.row.commodityAuthInfo">{{ scope.row.payAmount }}</div>
                <div v-else>{{ scope.row.payAmount }}</div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130" align="center">
              <template slot-scope="scope">
                <template v-if="$hasPermission('detail')" query desc="详情（入口控制）">
                  <el-button type="text" size="mini" @click="goDetail(scope.row.orderNo)">详情</el-button>
                </template>
                <template v-if="$hasPermission('closeOrder')" mutation desc="关闭订单" actions="closeOrder">
                  <el-button
                    type="text"
                    size="mini"
                    v-if="scope.row.orderStatus === 2"
                    @click="closeOrder(scope.row.orderNo)"
                    >强制关闭订单
                  </el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="page" v-bind="page"></hb-pagination>
        </el-card>
      </template>
      <!--提示弹窗-->
      <el-dialog :visible.sync="uiConfig.exportDialog" width="400px" class="m-dialog">
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb">导出成功，是否前往下载数据？</p>
            <p class="f-f13 f-mt5">下载入口：导出任务管理-个人报名订单</p>
          </div>
        </div>
        <div slot="footer">
          <el-button type="info" @click="uiConfig.exportDialog = false">暂 不</el-button>
          <el-button type="primary" @click="toDownloadPage">前往下载</el-button>
        </div>
      </el-dialog>
      <el-dialog :visible.sync="uiConfig.exportFinanceDataDialog" width="400px" class="m-dialog">
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb">导出成功，是否前往下载数据？</p>
            <p class="f-f13 f-mt5">下载入口：导出任务查看-中心财务数据表</p>
          </div>
        </div>
        <div slot="footer">
          <el-button type="info" @click="uiConfig.exportFinanceDataDialog = false">暂 不</el-button>
          <el-button type="primary" @click="toDownloadFinanceDataPage">前往下载</el-button>
        </div>
      </el-dialog>
    </div>
  </el-main>
</template>
<script lang="ts">
  import BizReceiveAccountDrawer from '@hbfe/jxjy-admin-components/src/biz/biz-receive-account-drawer.vue'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { HasSelectAccountMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectAccountMode'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import { SellerCancelOrderRequest } from '@api/ms-gateway/ms-order-v1'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import OrderFactor from '@api/service/management/trade/single/order/OrderFactor'
  import MutationOrder from '@api/service/management/trade/single/order/mutation/MutationOrder'
  import { QueryOrderPayStatue } from '@api/service/management/trade/single/order/query/QueryOrderPayStatue'
  import OrderTransactionStatus, {
    OrderTransaction
  } from '@api/service/management/trade/single/order/query/enum/OrderTransactionStatus'
  import OrderDetailVo from '@api/service/management/trade/single/order/query/vo/OrderDetailVo'
  import OrderStatisticResponseVo from '@api/service/management/trade/single/order/query/vo/OrderStatisticResponseVo'
  import QueryOrderListVo from '@api/service/diff/management/qztg/trade/order/model/QueryOrderListVo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'
  import { UiPage } from '@hbfe/common'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import { bind, debounce } from 'lodash-decorators'
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import LearningSchemeSelect from '@hbfe/jxjy-admin-trade/src/order/personal/components/learning-scheme-select.vue'
  import FxLearningSchemeSelect from '@hbfe/fx-manage/src/components/biz/biz-learning-scheme-select.vue'
  import ExportCentralFinancialDataInServicer from '@api/service/diff/management/qztg/trade/order/exportCentralFinancial'
  import QueryOrderInTrainingChannel from '@api/service/management/trade/single/order/query/QueryOrderInTrainingChannel'
  import MutationExportOrderInTrainingChannel from '@api/service/management/trade/single/order/mutation/MutationExportOrderInTrainingChannel'
  import ZtLearningSchemeSelect from '@hbfe/jxjy-admin-trade/src/order/personal/components/zt-learning-scheme-select.vue'
  import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/qztg/trade/enums/SaleChannelType'
  import QueryOrder from '@api/service/diff/management/qztg/trade/order/QueryOrder'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import { ChangeOrderType } from '@api/service/common/trade/ChangeOrderType'

  @Component({
    components: {
      DoubleDatePicker,
      LearningSchemeSelect,
      BizReceiveAccountDrawer,
      BizPortalSelect,
      BizDistributorSelect,
      BizPortalDistributorSelect,
      FxLearningSchemeSelect,
      ZtLearningSchemeSelect
    }
  })
  export default class extends Vue {
    // ui控制变量
    uiConfig = {
      loadStatus: { loadPage: true, loadTotalOrderAndMoney: true },
      showStatus: { isShowTotalOrderAmount: true },
      loadBtn: { exporting: false },
      exportDialog: false,
      exportFinanceDataDialog: false
    }

    SaleChannelEnum = SaleChannelEnum
    ChangeOrderType = ChangeOrderType

    // 订单工厂实例
    queryOrder = new QueryOrder()
    // 订单支付对象
    orderPayStatue: QueryOrderPayStatue = TradeModule.singleTradeBatchFactor.orderFactor.getQueryOrderPayStatue()
    // 订单业务对象
    mutationOrder: MutationOrder = TradeModule.singleTradeBatchFactor.mutationFactory.getMutationOrder()
    page: UiPage
    // 导出订单实例
    exportOrderObj = new QueryOrder()
    exportFinanceDataObj = new ExportCentralFinancialDataInServicer()
    exportZtOrderObj = new MutationExportOrderInTrainingChannel()
    // 订单表单数据
    tableOrderData = new Array<OrderDetailVo>()
    queryParams = new QueryOrderListVo()
    OrderTotalPriceAndCount = new OrderStatisticResponseVo()
    // 查询条件是否改变
    isQueryParamsChanged = false
    //是否 分销登录
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable
    //是否 专题登录
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    //专题请求
    queryZtOrder = new QueryOrderInTrainingChannel()
    promotionPortalName = ''
    distributorName = ''

    // 获取缴费渠道列表
    terminalCodeList = [
      {
        code: 1,
        desc: 'web端'
      },
      {
        code: 2,
        desc: 'H5'
      },
      {
        code: 3,
        desc: '导入开通'
      }
    ]
    // 获取销售渠道列表
    SaleChannelType = SaleChannelType.list()

    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount
    // 专题名称筛选显示
    get topPicNameFilterShow() {
      return (
        this.queryParams.saleSource === SaleChannelEnum.topic ||
        (!this.queryParams.saleSource && this.queryParams.saleSource !== SaleChannelEnum.self)
      )
    }

    // 收款账号id数组
    receiveAccountList = new Array<HasSelectAccountMode>()
    // 商品id数组
    commoditySkuIdList = new Array<HasSelectSchemeMode>()

    TrainingModeEnum = TrainingModeEnum

    @Ref('receiveAccountDrawerRef')
    receiveAccountDrawerRef: BizReceiveAccountDrawer

    // 收款账号入参
    @Watch('receiveAccountList', {
      deep: true
    })
    changeVal(val: Array<HasSelectAccountMode>) {
      if (val?.length && val[0]?.accountId) {
        this.queryParams.receiveAccountIdList = [] as string[]
        this.queryParams.receiveAccountIdList.push(val[0].accountId)
      } else {
        this.queryParams.receiveAccountIdList = undefined
      }
    }

    // 培训方案入参
    @Watch('commoditySkuIdList', {
      deep: true
    })
    changeScheme(val: Array<HasSelectSchemeMode>) {
      if (val?.length && val[0]?.schemeId) {
        this.queryParams.commoditySkuIdList = [] as string[]
        this.queryParams.commoditySkuIdList.push(val[0].schemeId)
      } else {
        this.queryParams.commoditySkuIdList = undefined
      }
    }

    constructor() {
      super()
      if (this.isFxlogin && this.isHadFxAbility) {
        this.page = new UiPage(this.doQueryPageFX, this.doQueryPageFX)
      } else if (this.isZtlogin) {
        this.page = new UiPage(this.doQueryPageZt, this.doQueryPageZt)
      } else {
        this.page = new UiPage(this.doQueryPage, this.doQueryPage)
      }
    }

    // 获取交易状态列表
    get tradeStatusList(): Array<EnumOption<OrderTransaction>> {
      return OrderTransactionStatus.list()
    }
    get showPeriodName() {
      return [this.TrainingModeEnum.mixed, this.TrainingModeEnum.offline].includes(
        this.commoditySkuIdList[0]?.trainingMode?.skuPropertyValueId
      )
    }

    async created() {
      await this.initRequest()
    }

    // 页面初始化调用的请求
    async initRequest() {
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.queryPageListFXS()
      } else if (this.isZtlogin) {
        await this.queryPageListZt()
      } else {
        await this.queryPageList()
      }
    }

    // 点击查询分销商订单列表
    @bind
    @debounce(200)
    async queryPageListZt() {
      this.page.pageNo = 1
      // 如果开启了分销增值服务且登录角色是分销商
      this.queryTotalOrderListAndMoneyZt()
      await this.doQueryPageZt()
    }
    // 点击查询订单列表
    @bind
    @debounce(200)
    async queryPageList() {
      this.page.pageNo = 1
      this.queryTotalOrderListAndMoney()
      await this.doQueryPage()
    }

    // 点击查询分销商订单列表
    @bind
    @debounce(200)
    async queryPageListFXS() {
      this.page.pageNo = 1
      // 如果开启了分销增值服务且登录角色是分销商
      this.queryTotalOrderListAndMoneyFX()
      await this.doQueryPageFX()
    }

    // 导出订单列表
    @bind
    @debounce(100)
    async doExportList() {
      this.$confirm('是否确认导出数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(
        async () => {
          this.uiConfig.loadBtn.exporting = true
          let res
          if (this.isFxlogin && this.isHadFxAbility) {
            res = await this.doExportListFx()
          } else if (this.isZtlogin) {
            res = await this.doExportListZt()
          } else {
            res = await this.doExportListTy()
          }
          this.uiConfig.loadBtn.exporting = false
          if (!res?.status?.isSuccess()) {
            this.$message.error('导出列表请求失败！')
          } else {
            this.uiConfig.exportDialog = true
          }
        },
        () => {
          // nothing
        }
      )
    }

    async doExportListTy() {
      return await this.exportOrderObj.exportOrder(this.queryParams)
    }

    async doExportListFx() {
      return await this.exportOrderObj.exportFxOrder(this.queryParams)
    }
    async doExportListZt() {
      return await this.exportZtOrderObj.exportOrder(this.queryParams)
    }

    // 重置查询条件
    @bind
    @debounce(200)
    async restQuery() {
      this.page.pageNo = 1
      this.commoditySkuIdList = new Array<HasSelectSchemeMode>()
      this.receiveAccountList = new Array<HasSelectAccountMode>()
      this.promotionPortalName = ''
      this.distributorName = ''
      if (!this.isZtlogin) {
        this.receiveAccountDrawerRef?.clear()
      }
      this.queryParams = new QueryOrderListVo()
      await this.initRequest()
    }

    // 查询订单列表
    async doQueryPage() {
      try {
        this.uiConfig.loadStatus.loadPage = true
        if (this.queryParams.orderStatus === OrderTransaction.Complete_Transaction || !this.queryParams.orderStatus) {
          this.uiConfig.showStatus.isShowTotalOrderAmount = true
        } else {
          this.uiConfig.showStatus.isShowTotalOrderAmount = false
        }
        this.tableOrderData = await this.queryOrderList()
      } catch (e) {
        this.$message.error('查询订单列表失败！')
        console.error(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['tableOrderRef'] as any)?.doLayout()
        this.uiConfig.loadStatus.loadPage = false
      }
    }

    /**
     * 抽离出去，用于适配差异化
     */
    async queryOrderList() {
      return await this.queryOrder.queryOrderList(this.page, this.queryParams, false)
    }

    // 查询分销订单列表
    async doQueryPageFX() {
      try {
        this.uiConfig.loadStatus.loadPage = true
        if (this.queryParams.orderStatus === OrderTransaction.Complete_Transaction || !this.queryParams.orderStatus) {
          this.uiConfig.showStatus.isShowTotalOrderAmount = true
        } else {
          this.uiConfig.showStatus.isShowTotalOrderAmount = false
        }
        this.tableOrderData = await this.queryFxOrderList()
      } catch (e) {
        this.$message.error('查询订单列表失败！')
        console.error(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['tableOrderRef'] as any)?.doLayout()
        this.uiConfig.loadStatus.loadPage = false
      }
    }

    /**
     * 抽离出去，用于适配差异化
     */
    async queryFxOrderList() {
      return await this.queryOrder.queryFxOrderList(this.page, this.queryParams, false)
    }
    // 查询分销订单列表
    async doQueryPageZt() {
      try {
        this.uiConfig.loadStatus.loadPage = true
        if (this.queryParams.orderStatus === OrderTransaction.Complete_Transaction || !this.queryParams.orderStatus) {
          this.uiConfig.showStatus.isShowTotalOrderAmount = true
        } else {
          this.uiConfig.showStatus.isShowTotalOrderAmount = false
        }
        this.tableOrderData = await this.queryZtOrderList()
      } catch (e) {
        this.$message.error('查询订单列表失败！')
        console.error(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['tableOrderRef'] as any)?.doLayout()
        this.uiConfig.loadStatus.loadPage = false
      }
    }

    /**
     * 抽离出去，用于适配差异化
     */
    async queryZtOrderList() {
      return await this.queryZtOrder.queryOrderList(this.page, this.queryParams, false)
    }

    // 查询指定筛选条件下的订单总数，订单总额
    async queryTotalOrderListAndMoney() {
      try {
        this.uiConfig.loadStatus.loadTotalOrderAndMoney = true
        // this.queryOrder.queryOrderListStatistic(this.queryParams, false).then(res => {
        //   this.uiConfig.loadStatus.loadTotalOrderAndMoney = false
        //   this.OrderTotalPriceAndCount = res
        // })
        this.queryOrderListStatisticRemoveTotalPeriod()
      } catch (e) {
        this.$message.error('获取订单总数以及金额失败！')
        console.error(e)
      } finally {
        // this.uiConfig.loadStatus.loadTotalOrderAndMoney = false
      }
    }

    /**
     * 抽离出去，用于适配差异化
     */
    queryOrderListStatisticRemoveTotalPeriod() {
      this.queryOrder.queryOrderListStatisticRemoveTotalPeriod(this.queryParams, false).then((res) => {
        this.uiConfig.loadStatus.loadTotalOrderAndMoney = false
        this.OrderTotalPriceAndCount = res
      })
    }
    // 查询指定筛选条件下的订单总数，订单总额
    async queryTotalOrderListAndMoneyZt() {
      try {
        this.uiConfig.loadStatus.loadTotalOrderAndMoney = true
        this.queryZtOrderListStatistic()
      } catch (e) {
        this.$message.error('获取订单总数以及金额失败！')
        console.error(e)
      } finally {
        // this.uiConfig.loadStatus.loadTotalOrderAndMoney = false
      }
    }
    // 清空期别
    clearPeriod() {
      this.queryParams.periodId = undefined
    }

    // 查询指定筛选条件下的订单总数，订单总额
    async queryTotalOrderListAndMoneyFX() {
      try {
        this.uiConfig.loadStatus.loadTotalOrderAndMoney = true
        this.queryFxOrderListStatistic()
      } catch (e) {
        this.$message.error('获取订单总数以及金额失败！')
        console.error(e)
      } finally {
        // this.uiConfig.loadStatus.loadTotalOrderAndMoney = false
      }
    }

    /**
     * 抽离出去，用于适配差异化
     */
    queryFxOrderListStatistic() {
      this.queryOrder.queryFxOrderListStatistic(this.queryParams, false).then((res) => {
        this.uiConfig.loadStatus.loadTotalOrderAndMoney = false
        this.OrderTotalPriceAndCount = res
      })
    }
    /**
     * 抽离出去，用于适配差异化
     */
    queryZtOrderListStatistic() {
      this.queryZtOrder.queryOrderListStatisticRemoveTotalPeriod(this.queryParams, false).then((res) => {
        this.uiConfig.loadStatus.loadTotalOrderAndMoney = false
        this.OrderTotalPriceAndCount = res
      })
    }
    // 前往导出任务查看页面
    toDownloadPage() {
      this.uiConfig.exportDialog = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: {
          type: 'exportOrder'
        }
      })
    }
    // 前往导出任务查看页面
    toDownloadFinanceDataPage() {
      this.uiConfig.exportFinanceDataDialog = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: {
          type: 'exportCentralFinancial'
        }
      })
    }
    // 订单详情
    goDetail(id: string) {
      this.$router.push('/training/trade/order/personal/detail/' + id)
    }

    // 强制关闭订单
    @bind
    @debounce(200)
    closeOrder(orderNo: string) {
      if (!orderNo) {
        this.$message.error('当前订单号不存在！')
        return
      }
      this.$confirm(
        '请与学员达成一致的前提下，使用关闭订单功能。关闭订单后，不能恢复需要重新下单！确认要关闭？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'error'
        }
      )
        .then(async () => {
          this.orderPayStatue.orderNo = orderNo
          const payResponse = await this.orderPayStatue.queryPayStatue()
          if (!payResponse.status?.isSuccess()) {
            this.$message.error('请求订单支付状态失败！')
            return
          }
          // 订单是否已支付
          if (payResponse.data.paymentStatus === 2) {
            // 已支付
            this.$message.error('当前订单已支付无法删除！')
            return
          }
          const params = new SellerCancelOrderRequest()
          params.orderNo = orderNo
          const res = await this.mutationOrder.applyCancelOrder(params)
          if (!res.status?.isSuccess()) {
            this.$message.error('关闭订单请求失败！')
            return
          }
          if (res.data?.success) {
            this.$message.success('关闭订单成功！')
            await this.doQueryPage()
            return
          }
          this.$message.success('关闭订单失败！')
        })
        .catch(() => {
          console.log('已取消删除')
        })
    }

    /**
     * 导出财务数据
     */
    @bind
    @debounce(200)
    async exportFinanceData() {
      const res = await this.exportFinanceDataObj.export(this.queryParams)
      if (res.status?.isSuccess()) {
        this.uiConfig.exportFinanceDataDialog = true
      } else {
        this.$message.error('导出财务数据失败！')
      }
    }
  }
</script>

<style scoped>
  ::v-deep .el-form-item--small .el-form-item__content {
    line-height: 31px;
  }
</style>
