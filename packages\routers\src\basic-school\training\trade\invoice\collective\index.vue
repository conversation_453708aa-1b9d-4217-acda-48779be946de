<route-meta>
{
"isMenu": true,
"title": "集体报名发票",
"sort": 2,
"icon": "icon_guanli"
}
</route-meta>
<script lang="ts">
  import InvoiceCollectiveIndex from '@hbfe/jxjy-admin-trade/src/invoice/collective/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, FXS, GYS, ZTGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    collectiveBill: [WXGLY, FXS, GYS],
    collectiveBillZt: [ZTGLY],
    invoiceAuto: [WXGLY, FXS, GYS],
    invoiceAutoZt: [ZTGLY],
    invoiceOffline: [WXGLY, FXS, GYS],
    invoiceOfflineZt: [ZTGLY],
    specialInvoice: [WXGLY, FXS, GYS],
    specialInvoiceZt: [ZTGLY],
    invoiceDistribution: [WXGLY, FXS, GYS],
    invoiceDistributionZt: [ZTGLY],
    autoInvoice: [WXGLY, FXS, GYS],
    autoInvoiceZt: [ZTGLY],
    redInvoice: [WXGLY, FXS, GYS],
    redInvoiceZt: [ZTGLY],
    electronicSpecialInvoice: [WXGLY, FXS, GYS],
    electronicSpecialInvoiceZt: [ZTGLY]
  })
  export default class extends InvoiceCollectiveIndex {}
</script>
