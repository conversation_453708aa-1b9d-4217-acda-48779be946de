import MutationResetAccountAdmin from '@api/service/management/user/mutation/MutationResetAccountAdmin'
import MutationResetPwdAdmin from '@api/service/management/user/mutation/MutationResetPwdAdmin'
import MutationResetNameAdmin from '@api/service/management/user/mutation/MutationResetNameAdmin'
import MutationResetPwdBussiness from '@api/service/management/user/mutation/MutationResetPwdBussiness'
import ManagerBusinessAction from '@api/service/management/user/mutation/manager/ManagerBusinessAction'
import MutationUpdateCollectiveInfo from '@api/service/management/user/mutation/manager/MutationUpdateCollectiveInfo'
import CreateRegionManager from '@api/service/management/user/mutation/manager/region-manager/CreateRegionManager'
import UpdateRegionManager from '@api/service/management/user/mutation/manager/region-manager/UpdateRegionManager'
import CreateSystemManager from '@api/service/management/user/mutation/manager/system-manager/CreateSystemManager'
import UpdateSystemManager from '@api/service/management/user/mutation/manager/system-manager/UpdateSystemManager'
import MutationUpdateStudentInfo from '@api/service/management/user/mutation/student/MutationUpdateStudentInfo'
import StudentBusinessAction from '@api/service/management/user/mutation/student/StudentBusinessAction'
/**
 * 业务工厂类
 */
class UserMutationFactory {
  /* 
    获取修改学员信息实例
  */
  get mutationUpdateStudentInfo() {
    return new MutationUpdateStudentInfo()
  }

  /**
   *获取创建系统管理员实例
   */
  get createSystemManager() {
    return new CreateSystemManager()
  }
  /**
   *获取修改系统管理员实例
   */
  get updateSystemManager() {
    return new UpdateSystemManager()
  }

  /**
   *获取创建地区管理员实例
   */
  get createRegionManager() {
    return new CreateRegionManager()
  }
  /**
   *获取修改地区管理员实例
   */
  get updateRegionManager() {
    return new UpdateRegionManager()
  }

  /**
   *获取操作管理员业务类 【系统管理员， 地区管理员】
   * 入参： 账号id
   */
  get mutationManagerBusiness() {
    return (id: string) => {
      return new ManagerBusinessAction(id)
    }
  }

  /**
   * @description: 获取学员信息操作实例
   * @param {String} id 学员id
   */
  get studentBusinessAction() {
    return (id: string) => {
      return new StudentBusinessAction(id)
    }
  }

  /* 
    获取修改当前登录账号密码
  */
  get resetPwdAdmin() {
    return new MutationResetPwdAdmin()
  }
  /* 
    获取修改账号
  */
  get mutationResetAccountAdmin() {
    return new MutationResetAccountAdmin()
  }

  /* 
    获取重置密码实例
  */
  get resetPwdBussiness() {
    return new MutationResetPwdBussiness()
  }
  /**
   * 获取更新集体用户信息
   */
  get mutationUpdateCollectiveInfo() {
    return new MutationUpdateCollectiveInfo()
  }

  /* 
    获取修改当前登录账号密码
  */
  get resetNameAdmin() {
    return new MutationResetNameAdmin()
  }
}

export default new UserMutationFactory()
