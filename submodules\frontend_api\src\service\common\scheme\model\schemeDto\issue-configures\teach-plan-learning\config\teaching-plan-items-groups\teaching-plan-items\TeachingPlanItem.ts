import TeachingPlanItemTeacher from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/teaching-plan-items-groups/teaching-plan-items/teachers/TeachingPlanItemTeacher'

/**
 * @description 教学计划项
 */
class TeachingPlanItem {
  /**
   * 教学计划项id
   */
  id: string
  /**
   * 教学计划id
   */
  planId: string
  /**
   * 教学计划组id
   */
  planGroupId: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 教学计划项类型
   */
  planMode: number
  /**
   * 学时
   */
  period: number
  /**
   * 教师
   */
  teachers: TeachingPlanItemTeacher[]
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 操作类型
   */
  operation: number
}

export default TeachingPlanItem
