import PlatformJxjypxtyptSchool, {
  ImportCourseSubjectRequest,
  CreCourseSubjectResponse
} from '@api/diff-gateway/platform-jxjypxtypt-gstyb-school'
import CourseMaintenanceInfoVoMain from '@api/service/diff/management/byzj/train-class/model/CourseMaintenanceInfoVo'
class CourseMaintenanceInfoVo extends CourseMaintenanceInfoVoMain {
  static from(item: CreCourseSubjectResponse) {
    const params = super.from(item)
    let data = new CourseMaintenanceInfoVo()
    data = params
    return data
  }
  /**
   * 删除公需课接口
   */
  async deleteCourseSubject() {
    return await PlatformJxjypxtyptSchool.deleteCourseSubjectByIdInServicer(this.id)
  }
}

export default CourseMaintenanceInfoVo
