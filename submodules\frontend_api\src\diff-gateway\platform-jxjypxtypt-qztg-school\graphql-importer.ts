import encryptedContent from './queries/encryptedContent.graphql'
import pageSchemeLearningInMyself from './queries/pageSchemeLearningInMyself.graphql'
import pageStudentSchemeLearningInDistributor from './queries/pageStudentSchemeLearningInDistributor.graphql'
import pageStudentSchemeLearningInServicerV2 from './queries/pageStudentSchemeLearningInServicerV2.graphql'
import validStudentInfo from './queries/validStudentInfo.graphql'
import createStudentAndPlaceOrder from './mutates/createStudentAndPlaceOrder.graphql'
import syncStudentSignUpForApp from './mutates/syncStudentSignUpForApp.graphql'
import syncStudentSignUpUnregistered from './mutates/syncStudentSignUpUnregistered.graphql'

export {
  encryptedContent,
  pageSchemeLearningInMyself,
  pageStudentSchemeLearningInDistributor,
  pageStudentSchemeLearningInServicerV2,
  validStudentInfo,
  createStudentAndPlaceOrder,
  syncStudentSignUpForApp,
  syncStudentSignUpUnregistered
}
