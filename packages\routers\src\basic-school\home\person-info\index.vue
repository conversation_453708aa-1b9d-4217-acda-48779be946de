<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2024-09-11 19:07:04
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-02-24 11:45:06
-->
<route-meta>
{
"isMenu": true,
"onlyShowOnTab": true,
"title": "个人帐号设置",
"sort": 2,
"hideMenu": true,
"icon": "icon_guanli"
}
</route-meta>

<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import PersonInfo from '@hbfe/jxjy-admin-basicSchoolPersonInfo/src/index.vue'

  import { DQGLY, NZFXS, NZFXSJCB, NZGYS, NZGYSJCB, WXGLY, ZTGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    personalInfo: [WXGLY, DQGLY, NZFXS, NZFXSJCB, NZGYS, NZGYSJCB, ZTGLY],
    changePassword: [WXGLY, DQGLY, NZFXS, NZFXSJCB, NZGYS, NZGYSJCB, ZTGLY],
    getPhoneCapture: [WXGLY, DQGLY, NZFXS, NZFXSJCB, NZGYS, NZGYSJCB, ZTGLY],
    submit: [WXGLY, DQGLY, NZFXS, NZFXSJCB, NZGYS, NZGYSJCB, ZTGLY]
  })
  export default class extends PersonInfo {}
</script>
