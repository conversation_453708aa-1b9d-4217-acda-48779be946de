/*
 * @Description: 发票配送业务
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:33:37
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-25 15:02:32
 */
import Offlineinvoice from '@api/ms-gateway/ms-offlineinvoice-v1'
import { ResponseStatus } from '@hbfe/common'
import { DeliveryWayEnum } from '../enum/DeliveryInvoiceEnum'
import DeliveryInvoiceParam from './vo/DeliveryInvoiceParam'
export default class MutationDeliveryInvoice {
  /**
   * 确认自取or确认配送
   * @param invoiceId 发票编号
   * @param deliveryWay 配送方式
   */
  async confirmStatus(
    deliveryInvoiceParam: DeliveryInvoiceParam,
    deliveryWay: DeliveryWayEnum
  ): Promise<ResponseStatus> {
    if (deliveryWay === DeliveryWayEnum.COURIER) {
      //   kd
      const confirmDeliveryRequest = DeliveryInvoiceParam.toCourier(deliveryInvoiceParam)
      const { status } = await Offlineinvoice.deliveryInvoice(confirmDeliveryRequest)
      return status
    } else {
      const confirmDeliveryRequest = DeliveryInvoiceParam.toSelffetched(deliveryInvoiceParam)
      const { status } = await Offlineinvoice.pickupInvoice(confirmDeliveryRequest)
      return status
    }
  }
  /**
   * 导入线下纸质发票配送、自取信息
   * @param filePath 文件路径
   * @returns
   */
  async importOfflineInvoice(filePath: string): Promise<ResponseStatus> {
    const { status } = await Offlineinvoice.importOfflineInvoiceDeliveryInfoWithServiceId({ filePath, importType: 1 })
    return status
  }
}
