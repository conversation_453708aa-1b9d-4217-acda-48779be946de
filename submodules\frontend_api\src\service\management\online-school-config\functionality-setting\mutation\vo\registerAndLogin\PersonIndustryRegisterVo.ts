import { FieldConstraintResponse } from '@api/ms-gateway/ms-servicer-series-v1'
import FieldConstraintVo from './FieldConstraintVo'

class PersonIndustryRegisterVo {
  /**
   * 专业类别
   */
  professionalQualification: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 职称等级
   */
  firstProfessionalCategory: FieldConstraintVo = new FieldConstraintVo()

  from(res: Array<FieldConstraintResponse>) {
    const resMap = new Map()
    res?.forEach(item => {
      resMap.set(item.field, item)
    })
    this.professionalQualification = FieldConstraintVo.from(resMap.get('professionalQualification'))
    this.firstProfessionalCategory = FieldConstraintVo.from(resMap.get('firstProfessionalCategory'))
  }
}
export default PersonIndustryRegisterVo
