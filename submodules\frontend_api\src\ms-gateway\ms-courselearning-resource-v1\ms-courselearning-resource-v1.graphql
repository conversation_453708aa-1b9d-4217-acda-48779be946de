"""独立部署的微服务,K8S服务名:ms-courselearning-resource-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	repairCoursePackageUseData(request:RepairCoursePackageUseDataRequest):Void @optionalLogin
	"""同步课程包信息
		@param ids 课程包使用情况id集合
	"""
	syncCoursePackageUsed(ids:[String]):Void
}
"""<AUTHOR>
input RepairCoursePackageUseDataRequest @type(value:"com.fjhb.ms.courselearning.resource.v1.kernel.gateway.graphql.request.RepairCoursePackageUseDataRequest") {
	repairCoursePackageUseDatas:[RepairCoursePackageUseData]
}
input RepairCoursePackageUseData @type(value:"com.fjhb.ms.courselearning.resource.v1.kernel.gateway.graphql.request.RepairCoursePackageUseDataRequest$RepairCoursePackageUseData") {
	schemeId:String
	learningId:String
	outlineId:String
	packageId:String
}

scalar List
