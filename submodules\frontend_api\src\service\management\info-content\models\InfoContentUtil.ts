/**
 *
 * 资讯工具类
 * @author: eleven
 * @date: 2020/4/19
 */
import { Marker } from '@api/service/common/models/Marker'
import { InfoContentConst } from './InfoContentConst'

export class InfoContentUtil {
  /**
   * 资讯状态
   */
  static statusOptionList = new Array<Marker>()
  /**
   * 是否弹窗
   */
  static popOptionList = new Array<Marker>()

  /**
   * 资讯状态选项
   */
  static productStatusOptionList() {
    if (this.statusOptionList.length) {
      return this.statusOptionList
    }
    this.statusOptionList.push(Marker.build(InfoContentConst.STATUS_ALL_TITLE, InfoContentConst.STATUS_ALL))
    this.statusOptionList.push(Marker.build(InfoContentConst.STATUS_DRAFT_TITLE, InfoContentConst.STATUS_DRAFT))
    this.statusOptionList.push(Marker.build(InfoContentConst.STATUS_PUBLISH_TITLE, InfoContentConst.STATUS_PUBLISH))
    return this.statusOptionList
  }

  /**
   * 是否弹窗
   */
  static productPopOptionList() {
    if (this.popOptionList.length) {
      return this.popOptionList
    }
    this.popOptionList.push(Marker.build('是', true))
    this.popOptionList.push(Marker.build('否', false))
    return this.popOptionList
  }
}
