import SkuPropertyVo from '@api/service/centre/train-class/query/vo/SkuPropertyVo'
import SkuPropertyConvertUtils from '@api/service/centre/train-class/util/SkuPropertyConvertUtils'
import { StudentSchemeLearningStatisticsResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import {
  CertificateLearningConfigResultResponse,
  GradeLearningConfigResultResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

export default class StudentSchemeItem {
  /**
   * 方案id
   */
  schemeId = ''
  /**
   * 方案名称
   */
  schemeName = ''
  /**
   * 学时
   */
  period = 0
  /**
   * 是否开放打印证书
   */
  openPrintCertificate = false
  /**
   * sku属性
   */
  skuProperty: SkuPropertyVo = new SkuPropertyVo()
  /**
   * 合格人次
   */
  qualifiedCount = 0
  /**
   * 未合格人次
   */
  unQualifiedCount = 0

  static async from(dto: StudentSchemeLearningStatisticsResponse) {
    const vo = new StudentSchemeItem()
    vo.schemeId = dto.scheme.schemeId
    vo.schemeName = dto.scheme.schemeName
    vo.skuProperty = await SkuPropertyConvertUtils.convertSkuPropertyToValueName(dto?.scheme?.skuProperty)
    vo.qualifiedCount = dto?.qualifiedCount
    vo.unQualifiedCount = dto?.unQualifiedCount
    const periodInfo = dto?.scheme?.learningResult?.find(
      (item) => item.resultType === 1
    ) as GradeLearningConfigResultResponse
    const certificateInfo = dto?.scheme?.learningResult?.find(
      (item) => item.resultType === 2
    ) as CertificateLearningConfigResultResponse
    vo.period = periodInfo?.grade
    vo.openPrintCertificate = certificateInfo?.openPrintTemplate
    return vo
  }
}
