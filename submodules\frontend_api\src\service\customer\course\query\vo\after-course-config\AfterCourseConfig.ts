import Precondition from '@api/service/customer/course/query/vo/after-course-config/Precondition'
import QuizConfig from '@api/service/customer/course/query/vo/after-course-config/QuizConfig'

class AfterCourseConfig {
  id = ''
  precondition: Precondition = new Precondition()
  operation: number
  quizConfig: QuizConfig = new QuizConfig()

  static from(afterCourseConfig: AfterCourseConfig): AfterCourseConfig {
    return Object.assign({}, afterCourseConfig)
  }
}

export default AfterCourseConfig
