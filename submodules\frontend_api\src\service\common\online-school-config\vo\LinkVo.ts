import { FriendLinkTypeEnum } from '@api/service/customer/online-school-config/online-school-partal-config/query/enum/FriendLinkTypeEnum'
import { FriendLinkInfo } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { FriendLink } from '@api/ms-gateway/ms-servicer-v1'

/**
 * @description 友情链接
 */
class LinkVo {
  /**
   * 唯一标识
   */
  id: string
  /**
   * 链接标题
   */
  title: string
  /**
   * 链接图片
   */
  picture: string
  /**
   * 链接类型
   */
  type: FriendLinkTypeEnum
  /**
   * 链接文本
   */
  link: string
  /**
   * 排序
   */
  sort: number

  static from(response: FriendLinkInfo) {
    const link = new LinkVo()
    link.id = response.id
    link.title = response.title
    link.picture = response.picture
    link.type = response.friendLinkType
    link.link = response.link
    link.sort = response.sort
    return link
  }

  static to(link: LinkVo) {
    const linkDto = new FriendLink()
    linkDto.id = link.id
    linkDto.title = link.title
    linkDto.picture = link.picture
    linkDto.friendLinkType = link.type
    linkDto.link = link.link
    linkDto.sort = link.sort
    return linkDto
  }
}
export default LinkVo
