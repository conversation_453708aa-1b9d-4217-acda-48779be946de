import getBatchOrderInMySelf from './queries/getBatchOrderInMySelf.graphql'
import getBatchReturnOrderInMySelf from './queries/getBatchReturnOrderInMySelf.graphql'
import getCommoditySkuCollectivePurchaseInServicer from './queries/getCommoditySkuCollectivePurchaseInServicer.graphql'
import getCommoditySkuCustomerPurchaseInServicer from './queries/getCommoditySkuCustomerPurchaseInServicer.graphql'
import getCommoditySkuTrainingChannelInServicer from './queries/getCommoditySkuTrainingChannelInServicer.graphql'
import getExchangeOrderInMySelf from './queries/getExchangeOrderInMySelf.graphql'
import getIssueCommoditySkuCustomerPurchaseInServicer from './queries/getIssueCommoditySkuCustomerPurchaseInServicer.graphql'
import getOfflineInvoiceInMySelf from './queries/getOfflineInvoiceInMySelf.graphql'
import getOnlineInvoiceInMySelf from './queries/getOnlineInvoiceInMySelf.graphql'
import getOrderInMyself from './queries/getOrderInMyself.graphql'
import getPortalCommoditySkuCustomerPurchaseInServicer from './queries/getPortalCommoditySkuCustomerPurchaseInServicer.graphql'
import getReturnOrderInMyself from './queries/getReturnOrderInMyself.graphql'
import listBatchRegistrationSources from './queries/listBatchRegistrationSources.graphql'
import listIssueCommoditySkuPropertyCustomerPurchaseInServicer from './queries/listIssueCommoditySkuPropertyCustomerPurchaseInServicer.graphql'
import listIssueLogInMySelf from './queries/listIssueLogInMySelf.graphql'
import listOrderTopNBySkuInServicer from './queries/listOrderTopNBySkuInServicer.graphql'
import listPortalCommoditySkuPropertyCustomerPurchaseInServicer from './queries/listPortalCommoditySkuPropertyCustomerPurchaseInServicer.graphql'
import listSkuPropertyCollectivePurchaseInServicer from './queries/listSkuPropertyCollectivePurchaseInServicer.graphql'
import listSkuPropertyCustomerPurchaseInServicer from './queries/listSkuPropertyCustomerPurchaseInServicer.graphql'
import listSkuPropertyTrainingChannelCollectiveInServicer from './queries/listSkuPropertyTrainingChannelCollectiveInServicer.graphql'
import listSkuPropertyTrainingChannelCustomerInServicer from './queries/listSkuPropertyTrainingChannelCustomerInServicer.graphql'
import listSkuPropertyTrainingChannelInServicer from './queries/listSkuPropertyTrainingChannelInServicer.graphql'
import pageBatchOrderInMySelf from './queries/pageBatchOrderInMySelf.graphql'
import pageBatchReturnOrderInMySelf from './queries/pageBatchReturnOrderInMySelf.graphql'
import pageCommoditySkuCustomerCollectivePurchaseInServicer from './queries/pageCommoditySkuCustomerCollectivePurchaseInServicer.graphql'
import pageCommoditySkuCustomerPurchaseInServicer from './queries/pageCommoditySkuCustomerPurchaseInServicer.graphql'
import pageCommoditySkuTrainingChannelCollectiveInServicer from './queries/pageCommoditySkuTrainingChannelCollectiveInServicer.graphql'
import pageCommoditySkuTrainingChannelCustomerInServicer from './queries/pageCommoditySkuTrainingChannelCustomerInServicer.graphql'
import pageCommoditySkuTrainingChannelInServicer from './queries/pageCommoditySkuTrainingChannelInServicer.graphql'
import pageExchangeOrderInMySelf from './queries/pageExchangeOrderInMySelf.graphql'
import pageIssueCommoditySkuCollectivePurchaseInServicer from './queries/pageIssueCommoditySkuCollectivePurchaseInServicer.graphql'
import pageIssueCommoditySkuCustomerPurchaseInServicer from './queries/pageIssueCommoditySkuCustomerPurchaseInServicer.graphql'
import pageIssueCommoditySkuTrainingChannelInServicer from './queries/pageIssueCommoditySkuTrainingChannelInServicer.graphql'
import pageOfflineInvoiceInMySelf from './queries/pageOfflineInvoiceInMySelf.graphql'
import pageOnlineInvoiceInMySelf from './queries/pageOnlineInvoiceInMySelf.graphql'
import pageOrderInMyself from './queries/pageOrderInMyself.graphql'
import pagePortalCommoditySkuCustomerPurchaseInServicer from './queries/pagePortalCommoditySkuCustomerPurchaseInServicer.graphql'
import pageReturnOrderInMyself from './queries/pageReturnOrderInMyself.graphql'
import statisticOrderInMyself from './queries/statisticOrderInMyself.graphql'
import statisticReturnOrderInMyself from './queries/statisticReturnOrderInMyself.graphql'
import statisticSignUpCountInServicer from './queries/statisticSignUpCountInServicer.graphql'

export {
  getBatchOrderInMySelf,
  getBatchReturnOrderInMySelf,
  getCommoditySkuCollectivePurchaseInServicer,
  getCommoditySkuCustomerPurchaseInServicer,
  getCommoditySkuTrainingChannelInServicer,
  getExchangeOrderInMySelf,
  getIssueCommoditySkuCustomerPurchaseInServicer,
  getOfflineInvoiceInMySelf,
  getOnlineInvoiceInMySelf,
  getOrderInMyself,
  getPortalCommoditySkuCustomerPurchaseInServicer,
  getReturnOrderInMyself,
  listBatchRegistrationSources,
  listIssueCommoditySkuPropertyCustomerPurchaseInServicer,
  listIssueLogInMySelf,
  listOrderTopNBySkuInServicer,
  listPortalCommoditySkuPropertyCustomerPurchaseInServicer,
  listSkuPropertyCollectivePurchaseInServicer,
  listSkuPropertyCustomerPurchaseInServicer,
  listSkuPropertyTrainingChannelCollectiveInServicer,
  listSkuPropertyTrainingChannelCustomerInServicer,
  listSkuPropertyTrainingChannelInServicer,
  pageBatchOrderInMySelf,
  pageBatchReturnOrderInMySelf,
  pageCommoditySkuCustomerCollectivePurchaseInServicer,
  pageCommoditySkuCustomerPurchaseInServicer,
  pageCommoditySkuTrainingChannelCollectiveInServicer,
  pageCommoditySkuTrainingChannelCustomerInServicer,
  pageCommoditySkuTrainingChannelInServicer,
  pageExchangeOrderInMySelf,
  pageIssueCommoditySkuCollectivePurchaseInServicer,
  pageIssueCommoditySkuCustomerPurchaseInServicer,
  pageIssueCommoditySkuTrainingChannelInServicer,
  pageOfflineInvoiceInMySelf,
  pageOnlineInvoiceInMySelf,
  pageOrderInMyself,
  pagePortalCommoditySkuCustomerPurchaseInServicer,
  pageReturnOrderInMyself,
  statisticOrderInMyself,
  statisticReturnOrderInMyself,
  statisticSignUpCountInServicer
}
