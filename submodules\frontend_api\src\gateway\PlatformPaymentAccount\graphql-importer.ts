import allowUpdate from './queries/allowUpdate.graphql'
import enableElectronInvoice from './queries/enableElectronInvoice.graphql'
import get from './queries/get.graphql'
import page from './queries/page.graphql'
import pageByarams from './queries/pageByarams.graphql'
import pageTaxPayer from './queries/pageTaxPayer.graphql'
import validateName from './queries/validateName.graphql'
import create from './mutates/create.graphql'
import remove from './mutates/remove.graphql'
import update from './mutates/update.graphql'
import updateEnable from './mutates/updateEnable.graphql'

export {
  allowUpdate,
  enableElectronInvoice,
  get,
  page,
  pageByarams,
  pageTaxPayer,
  validateName,
  create,
  remove,
  update,
  updateEnable
}
