/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2024-04-23 10:45:22
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-05-11 12:05:28
 */
import TemplateItem from '@api/service/common/template-school/TemplateItem'
import { ClientTypeEnum } from '@api/service/common/template-school/enums/ClientTypeEnum'
import { IndustryTypeEnum } from '@api/service/common/template-school/enums/IndustryTypeEnum'
import { ColorEnum } from '@api/service/common/template-school/enums/ColorEnum'
class TemplateModule {
  /**
   * 模板列表-人社PC
   */
  private _templatePCList: Array<TemplateItem> = [
    {
      id: 'Temlpate-Web-01',
      clientType: ClientTypeEnum.PC,
      industryType: IndustryTypeEnum.RS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      logoSize: [850, 110],
      iconSize: [16, 16],
      customerPhoneSize: [340, 110],
      bannerSize: [1920, 400],
      trainingProcessSize: [1200, 120],
      groupRegistrationSize: [200, 360],
      singleOnlineCollectSize: [1200, 100],
      doubleOnlineCollectSize: [590, 100],
      singleOfflineCollectSize: [1200, 100],
      doubleOfflineCollectSize: [590, 100],
      wechatCustomerServiceSize: [160, 160],
      doubleOnlineExampleImgSrc: 'demo-specialenter-xs-default-2.jpg',
      singleOnlineExampleImgSrc: 'demo-specialenter-xs-default-2.jpg',
      doubleOfflineExampleImgSrc: 'demo-specialenter-xx-default-2.jpg',
      singleOfflineExampleImgSrc: 'demo-specialenter-xx-default-2.jpg',
      reviewPath: 'demo-special-web-001.png'
    },
    {
      id: 'Temlpate-Web-02',
      clientType: ClientTypeEnum.PC,
      industryType: IndustryTypeEnum.RS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      logoSize: [850, 110],
      iconSize: [16, 16],
      customerPhoneSize: [340, 110],
      bannerSize: [1920, 400],
      trainingProcessSize: [1200, 120],
      groupRegistrationSize: [200, 360],
      singleOnlineCollectSize: [1200, 100],
      doubleOnlineCollectSize: [590, 100],
      singleOfflineCollectSize: [1200, 100],
      doubleOfflineCollectSize: [590, 100],
      wechatCustomerServiceSize: [160, 160],
      doubleOnlineExampleImgSrc: 'demo-specialenter-xs-default-2.jpg',
      singleOnlineExampleImgSrc: 'demo-specialenter-xs-default-2.jpg',
      doubleOfflineExampleImgSrc: 'demo-specialenter-xx-default-2.jpg',
      singleOfflineExampleImgSrc: 'demo-specialenter-xx-default-2.jpg',
      reviewPath: 'demo-special-web-002.jpg'
    },
    {
      id: 'Temlpate-Web-03',
      clientType: ClientTypeEnum.PC,
      industryType: IndustryTypeEnum.RS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      logoSize: [850, 110],
      iconSize: [16, 16],
      customerPhoneSize: [340, 110],
      bannerSize: [1920, 400],
      trainingProcessSize: [1200, 120],
      groupRegistrationSize: [200, 360],
      singleOnlineCollectSize: [1200, 100],
      doubleOnlineCollectSize: [590, 100],
      singleOfflineCollectSize: [1200, 100],
      doubleOfflineCollectSize: [590, 100],
      wechatCustomerServiceSize: [160, 160],
      doubleOnlineExampleImgSrc: 'demo-specialenter-xs-default-2.jpg',
      singleOnlineExampleImgSrc: 'demo-specialenter-xs-default-2.jpg',
      doubleOfflineExampleImgSrc: 'demo-specialenter-xx-default-2.jpg',
      singleOfflineExampleImgSrc: 'demo-specialenter-xx-default-2.jpg',
      reviewPath: 'demo-special-web-003.jpg'
    }
  ]
  /**
   * 模板列表-人社H5
   */
  private _templateH5List: Array<TemplateItem> = [
    {
      id: 'H5TemplateId-1',
      clientType: ClientTypeEnum.H5,
      industryType: IndustryTypeEnum.RS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      bannerSize: [700, 240],
      groupRegistrationSize: [200, 360],
      reviewPath: 'demo-special-h5-001.png'
    }
  ]

  get templateH5List(): Array<TemplateItem> {
    return this._templateH5List
  }
  get templatePCList(): Array<TemplateItem> {
    return this._templatePCList
  }

  /**
   * 所有模板列表
   * @returns {TemplateItem[]}
   */
  get templateList(): Array<TemplateItem> {
    return [...this._templatePCList, ...this._templateH5List]
  }

  /**
   * 根据id获取模板
   * @param templateId
   * @returns {TemplateItem}
   */
  getTemplate(templateId: string): TemplateItem {
    return this.templateList.find((item) => item.id === templateId) || new TemplateItem()
  }
}
export default new TemplateModule()
