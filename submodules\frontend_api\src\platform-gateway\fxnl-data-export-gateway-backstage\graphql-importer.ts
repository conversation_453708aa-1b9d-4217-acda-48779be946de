import exportCommodityDistributorOpenReportInDistributor from './queries/exportCommodityDistributorOpenReportInDistributor.graphql'
import exportCommodityOpenStatisticsDetailExcelInSupplier from './queries/exportCommodityOpenStatisticsDetailExcelInSupplier.graphql'
import exportCommodityOpenStatisticsExcelInSupplier from './queries/exportCommodityOpenStatisticsExcelInSupplier.graphql'
import exportCommodityOpenStatisticsSummaryExcelInSupplier from './queries/exportCommodityOpenStatisticsSummaryExcelInSupplier.graphql'
import exportDistributionCommodityManageExcelInSupplier from './queries/exportDistributionCommodityManageExcelInSupplier.graphql'
import exportDistributorSalesStatisticsExcelInSupplier from './queries/exportDistributorSalesStatisticsExcelInSupplier.graphql'
import exportPricingPolicyinPortalInDistributor from './queries/exportPricingPolicyinPortalInDistributor.graphql'
import exportPricingPolicyinPortalInSupplier from './queries/exportPricingPolicyinPortalInSupplier.graphql'
import exportProductPricingSchemeInSDistributor from './queries/exportProductPricingSchemeInSDistributor.graphql'
import exportProductPricingSchemeInSupplier from './queries/exportProductPricingSchemeInSupplier.graphql'
import exportStudentSchemeLearningExcelInSDistributor from './queries/exportStudentSchemeLearningExcelInSDistributor.graphql'
import exportStudentSchemeLearningExcelInSupplier from './queries/exportStudentSchemeLearningExcelInSupplier.graphql'
import listExportTaskGroupInfoInDistributor from './queries/listExportTaskGroupInfoInDistributor.graphql'
import listExportTaskGroupInfoInSupplier from './queries/listExportTaskGroupInfoInSupplier.graphql'
import pageExportTaskInfoInMyself from './queries/pageExportTaskInfoInMyself.graphql'

export {
  exportCommodityDistributorOpenReportInDistributor,
  exportCommodityOpenStatisticsDetailExcelInSupplier,
  exportCommodityOpenStatisticsExcelInSupplier,
  exportCommodityOpenStatisticsSummaryExcelInSupplier,
  exportDistributionCommodityManageExcelInSupplier,
  exportDistributorSalesStatisticsExcelInSupplier,
  exportPricingPolicyinPortalInDistributor,
  exportPricingPolicyinPortalInSupplier,
  exportProductPricingSchemeInSDistributor,
  exportProductPricingSchemeInSupplier,
  exportStudentSchemeLearningExcelInSDistributor,
  exportStudentSchemeLearningExcelInSupplier,
  listExportTaskGroupInfoInDistributor,
  listExportTaskGroupInfoInSupplier,
  pageExportTaskInfoInMyself
}
