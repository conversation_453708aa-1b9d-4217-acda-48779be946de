import LearningTypeBase from '@api/service/customer/train-class/query/vo/LearningTypeBase'
import Classification from '@api/service/customer/train-class/query/vo/Classification'
import QuizConfig from '@api/service/customer/train-class/query/vo/QuizConfig'
import ChooseCourseRule from '@api/service/management/train-class/mutation/vo/ChooseCourseRule'

/**
 * 课程学习
 */
class CourseLearningLearningType extends LearningTypeBase {
  // region properties

  /**
   * 总要求学时，类型为number 仅限自主选课模式使用
   */
  requirePeriod = 0

  /**
   *必修课要求学时，类型为number仅限选课规则模式使用
   */
  compulsoryRequirePeriod = 0
  /**
   *选修课要求学时，类型为number仅限选课规则模式使用
   */
  electiveRequirePeriod = 0

  /**
   *选课规则，类型为ChooseCourseRule仅限选课规则模式使用
   */
  chooseCourseRule = new ChooseCourseRule()
  /**
   *分类+课程包id  其中第一层是顶级分类，如果第一级无children则代表无分类，类型为Classification[]
   */
  classification = new Classification()

  /**
   *课后测验要求的课程进度，类型为number
   */
  courseExamRequireCourseSchedule = 0
  /**
   *课程完成评定配置-进度要求，类型为number
   */
  courseSchedule = 0
  /**
   *课程完成评定配置-是否要求课后测验合格，类型为boolean
   */
  courseQuizPagerStandard = false
  /**
   *是否启用课程评价，类型为boolean
   */
  enableAppraisal = false
  /**
   *是否强制课程评价，类型为boolean
   */
  enableCompulsoryAppraisal = false
  /**
   *进行课程评价课程进度要求，-1,不限制，类型为number
   */
  preconditionCourseSchedule = 0
  // /**
  //  *课后测验是否纳入考核，类型为boolean
  //  */
  // incorporateCourseQuiz = false
  /**
   * 是否配置课程测验
   */
  configCourseQuiz = false

  /**
   *每门课课后测验合格分数，类型为number
   */
  eachCourseQuizPassScore = 0
  /**
   *课后测验配置，类型为QuizConfig
   */
  quizConfigModel = new QuizConfig()
  // endregion
  // region methods

  // endregion
}
export default CourseLearningLearningType
