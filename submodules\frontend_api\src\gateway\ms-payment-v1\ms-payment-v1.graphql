"""独立部署的微服务,K8S服务名:ms-payment-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""管理员确认付款单已线下付款
		@param paymentOrderNo 付款单号
	"""
	confirmPaymentOrderOfflinePaid(paymentOrderNo:String):Void
	"""创建收款账号
		@param info 收款账号信息
	"""
	createReceiveAccount(info:DefaultCreateReceiveAccountRequest):Void
	"""删除收款账号
		@param receiveAccountId 收款账号编号
	"""
	deleteReceiveAccount(receiveAccountId:String):Void
	"""禁用收款账号
		@param receiveAccountId 收款账号编号
	"""
	disableReceiveAccount(receiveAccountId:String):Void
	"""启用收款账号
		@param receiveAccountId 收款账号编号
	"""
	enableReceiveAccount(receiveAccountId:String):Void
	"""更新线下支付凭证
		@param request 凭证信息
	"""
	updateOfflinePaymentVoucher(request:OfflinePaymentOrderUpdateVoucherRequest):Void
	"""更新收款账号
		@param info 更新的收款账号信息
	"""
	updateReceiveAccount(info:DefaultUpdateReceiveAccountRequest):Void
}
"""创建收款账号
	<AUTHOR>
	@since 2021/2/6
"""
input DefaultCreateReceiveAccountRequest @type(value:"com.fjhb.ms.payment.v1.kernel.gateway.graphql.request.DefaultCreateReceiveAccountRequest") {
	"""收款账户类型
		<pre>
		1-线上支付
		2-线下支付
		</pre>
	"""
	accountType:Int
	"""支付渠道ID，由支付服务提供的通用支付渠道编号"""
	paymentChannelId:String
	"""收款帐号名称"""
	name:String
	"""所属商户名称"""
	merchantName:String
	"""所属商户电话"""
	merchantPhone:String
}
"""创建收款账号
	<AUTHOR>
	@since 2021/2/6
"""
input DefaultUpdateReceiveAccountRequest @type(value:"com.fjhb.ms.payment.v1.kernel.gateway.graphql.request.DefaultUpdateReceiveAccountRequest") {
	"""收款账号编号"""
	receiveAccountId:String
	"""收款帐号名称"""
	name:String
	"""所属商户名称，null表示不更新"""
	merchantName:String
	"""所属商户电话，null表示不更新"""
	merchantPhone:String
}
"""订单线下付款更新凭证命令
	<AUTHOR>
	@since 2021/1/29
"""
input OfflinePaymentOrderUpdateVoucherRequest @type(value:"com.fjhb.ms.payment.v1.kernel.gateway.graphql.request.OfflinePaymentOrderUpdateVoucherRequest") {
	"""付款单号"""
	paymentOrderNo:String
	"""付款凭证列表"""
	paymentVouchers:[PaymentVoucherRequest]
}
"""
	<AUTHOR>
	@since 2021/2/1
"""
input PaymentVoucherRequest @type(value:"com.fjhb.ms.payment.v1.kernel.gateway.graphql.request.PaymentVoucherRequest") {
	"""付款凭证文件路径，通常是图片的路径"""
	path:String
	"""凭证上传时间"""
	uploadTime:DateTime
}

scalar List
