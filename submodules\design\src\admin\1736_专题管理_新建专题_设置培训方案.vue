<template>
  <el-main>
    <div class="f-tr f-pt15">
      <el-button type="primary" size="medium" class="f-mr5"
        ><i class="hb-iconfont icon-complelearn f-mr5"></i>预览专题web</el-button
      >
      <el-button type="primary" size="medium" class="f-mr15"
        ><i class="hb-iconfont icon-complelearn f-mr5"></i>预览专题h5</el-button
      >
    </div>
    <div class="f-p15">
      <!--第三步-->
      <el-card shadow="never" class="m-card is-header">
        <el-row type="flex" justify="center">
          <el-col :sm="20" :lg="12">
            <el-steps :active="3" align-center class="m-steps f-pt40 f-pb10">
              <el-step title="设置专题基础信息"></el-step>
              <el-step title="设置专题门户信息"></el-step>
              <el-step title="设置专题培训方案"></el-step>
              <el-step title="保存专题"></el-step>
            </el-steps>
          </el-col>
        </el-row>
        <div class="m-tit is-border-bottom f-justify-between">
          <el-button type="primary">选择培训方案</el-button>
          <el-link type="primary" :underline="false" class="m-specialimg-pop"
            ><i class="el-icon-picture f-f20 f-mr5 f-vm"></i>查看专题示例<el-image
              class="transparent-pic"
              src="/assets/images/transparent-pic.png"
              :preview-src-list="['/assets/images/demo-special-web-001.png']"
          /></el-link>
        </div>
        <div class="f-p15">
          <!--条件查询-->
          <el-row :gutter="16" class="m-query is-border-bottom f-mt15">
            <el-form :inline="true" label-width="auto">
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="年度">
                  <el-date-picker v-model="value3" type="year" placeholder="请选择培训年度" class="f-wf">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="地区">
                  <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" />
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="行业">
                  <el-select v-model="select" clearable placeholder="请选择行业">
                    <el-option value="人社"></el-option>
                    <el-option value="建设"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="科目类型">
                  <el-select v-model="select" clearable placeholder="请选择科目类型">
                    <el-option value="科目类型1"></el-option>
                    <el-option value="科目类型2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="培训专业">
                  <el-select v-model="select" clearable placeholder="请选择培训专业">
                    <el-option value="培训专业1"></el-option>
                    <el-option value="培训专业2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="培训类别">
                  <el-select v-model="select" clearable placeholder="请选择培训类别">
                    <el-option value="培训类别1"></el-option>
                    <el-option value="培训类别2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="培训对象">
                  <el-select v-model="select" clearable placeholder="请选择培训对象">
                    <el-option value="培训对象1"></el-option>
                    <el-option value="培训对象2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="岗位类别">
                  <el-select v-model="select" clearable placeholder="请选择岗位类别">
                    <el-option value="岗位类别1"></el-option>
                    <el-option value="岗位类别2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="技术等级">
                  <el-select v-model="select" clearable placeholder="请选择技术等级">
                    <el-option value="技术等级1"></el-option>
                    <el-option value="技术等级2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="学段">
                  <el-select v-model="select" clearable placeholder="请选择学段">
                    <el-option value="学段1"></el-option>
                    <el-option value="学段2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="学科">
                  <el-select v-model="select" clearable placeholder="请选择学科">
                    <el-option value="学科1"></el-option>
                    <el-option value="学科2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="培训方案类型">
                  <el-select v-model="select" clearable placeholder="请选择培训方案类型">
                    <el-option value="培训方案类型1"></el-option>
                    <el-option value="培训方案类型2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="培训方案名称">
                  <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="报名状态">
                  <el-select v-model="select" clearable placeholder="请选择报名状态">
                    <el-option value="报名状态1"></el-option>
                    <el-option value="报名状态2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="是否展示在专题门户">
                  <el-select v-model="select" clearable placeholder="请选择"></el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                <el-form-item class="f-tr">
                  <el-button type="primary">查询</el-button>
                  <el-button>重置</el-button>
                  <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                  <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
          <el-alert type="warning" :closable="false" class="m-alert f-mb15">
            当前已选中 <span class="f-fb">2</span> 个培训方案
          </el-alert>
          <!--表格-->
          <el-table stripe :data="tableData" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="排序" min-width="80" align="center" fixed="left">
              <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
            </el-table-column>
            <el-table-column label="培训方案名称" min-width="300" fixed="left">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  <p>
                    <el-tag type="primary" effect="dark" size="mini">面网授-培训班-选课规则</el-tag
                    >培训方案名称培训方案名称培训方案名称培训方案名称
                  </p>
                </div>
                <div v-else>
                  <p>
                    <el-tag type="primary" effect="dark" size="mini">面授-培训班-选课规则</el-tag
                    >培训方案名称培训方案名称培训方案名称培训方案名称
                  </p>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="报名学时" min-width="110" align="center">
              <template>50</template>
            </el-table-column>
            <el-table-column label="价格" min-width="100" align="right">
              <template>50.00</template>
            </el-table-column>
            <el-table-column label="培训属性" min-width="240">
              <template>
                <p>行业：行业行业</p>
                <p>地区：为空，不展示</p>
                <p>科目类型：科目类型</p>
                <p>培训类别：培训类别</p>
                <p>培训专业：培训专业培训专业</p>
                <p>培训年度：2019</p>
              </template>
            </el-table-column>
            <el-table-column label="报名状态" min-width="140">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  <el-badge is-dot type="info" class="badge-status">报名关闭</el-badge>
                </div>
                <div v-else>
                  <el-badge is-dot type="success" class="badge-status">报名开启</el-badge>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="学习起止时间" min-width="220">
              <template>
                <p>起始：2021-10-15 00:21:21</p>
                <p>结束：2021-10-15 00:21:21</p>
              </template>
            </el-table-column>
            <el-table-column label="报名起止时间" min-width="220">
              <template>
                <p>起始：2021-10-15 00:21:21</p>
                <p>结束：2021-10-15 00:21:21</p>
              </template>
            </el-table-column>
            <el-table-column label="最新修改时间" sortable min-width="180">
              <template>2021-10-15 00:21:21</template>
            </el-table-column>
            <el-table-column label="是否展示在专题门户" min-width="180">
              <template>展示</template>
            </el-table-column>
            <el-table-column label="展示用户" min-width="180">
              <template>
                <p>学员门户可见</p>
                <p>集体报名管理员可见</p>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center" fixed="right">
              <template>
                <el-button type="text">取消选择</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="m-btn-bar f-tc is-sticky f-pt15">
          <el-button>取 消</el-button>
          <el-button>返回上一步</el-button>
          <el-button type="primary">保 存</el-button>
        </div>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
