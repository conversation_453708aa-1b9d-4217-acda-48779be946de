import MsAccountGateway, { UserUpdateRequest } from '@api/ms-gateway/ms-account-v1'
import { ResponseStatus } from '@hbfe/common'

/**
 * 更新用户信息
 */
class MutationUpdateUserInfo {
  updateStudentRequestVo = new UserUpdateRequest()

  /**
   * 更新集体信息方法
   */
  async doUpdate(): Promise<ResponseStatus> {
    const { status } = await MsAccountGateway.updateUser(this.updateStudentRequestVo)
    return status
  }
}
export default MutationUpdateUserInfo
