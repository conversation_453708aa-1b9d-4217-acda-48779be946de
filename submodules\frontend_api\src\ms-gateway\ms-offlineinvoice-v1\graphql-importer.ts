import commonQueryOfflineInvoiceImportResult from './queries/commonQueryOfflineInvoiceImportResult.graphql'
import getAllImportData from './queries/getAllImportData.graphql'
import getImportFailedData from './queries/getImportFailedData.graphql'
import queryForImportBatchPayOfflineInvoice from './queries/queryForImportBatchPayOfflineInvoice.graphql'
import queryForImportBatchPaySpecialInvoiceDelivery from './queries/queryForImportBatchPaySpecialInvoiceDelivery.graphql'
import queryForImportBatchPaySpecialPaperOfflineInvoice from './queries/queryForImportBatchPaySpecialPaperOfflineInvoice.graphql'
import queryForImportOfflineInvoice from './queries/queryForImportOfflineInvoice.graphql'
import queryForImportOfflineInvoiceWithServiceId from './queries/queryForImportOfflineInvoiceWithServiceId.graphql'
import queryForImportOfflineSpecialElectronicInvoice from './queries/queryForImportOfflineSpecialElectronicInvoice.graphql'
import queryForImportSpecialInvoiceDelivery from './queries/queryForImportSpecialInvoiceDelivery.graphql'
import queryForImportSpecialInvoiceDeliveryWithServiceId from './queries/queryForImportSpecialInvoiceDeliveryWithServiceId.graphql'
import queryForImportSpecialPaperOfflineInvoice from './queries/queryForImportSpecialPaperOfflineInvoice.graphql'
import queryForImportSpecialPaperOfflineInvoiceWithServiceId from './queries/queryForImportSpecialPaperOfflineInvoiceWithServiceId.graphql'
import queryShippingMethodsForSchool from './queries/queryShippingMethodsForSchool.graphql'
import createChannel from './mutates/createChannel.graphql'
import createChannelShippingMethods from './mutates/createChannelShippingMethods.graphql'
import createOfflineSpecialElectronicInvoice from './mutates/createOfflineSpecialElectronicInvoice.graphql'
import deleteChannel from './mutates/deleteChannel.graphql'
import deliveryInvoice from './mutates/deliveryInvoice.graphql'
import freezeInvoice from './mutates/freezeInvoice.graphql'
import importOfflineInvoice from './mutates/importOfflineInvoice.graphql'
import importOfflineInvoiceDeliveryInfo from './mutates/importOfflineInvoiceDeliveryInfo.graphql'
import importOfflineInvoiceDeliveryInfoWithServiceId from './mutates/importOfflineInvoiceDeliveryInfoWithServiceId.graphql'
import importOfflineInvoiceWithServiceId from './mutates/importOfflineInvoiceWithServiceId.graphql'
import issueOfflineInvoice from './mutates/issueOfflineInvoice.graphql'
import pickupInvoice from './mutates/pickupInvoice.graphql'
import resetInvoice from './mutates/resetInvoice.graphql'
import updateChannel from './mutates/updateChannel.graphql'
import updateChannelShippingMethods from './mutates/updateChannelShippingMethods.graphql'
import updateChannelStatus from './mutates/updateChannelStatus.graphql'
import updateOfflineInvoice from './mutates/updateOfflineInvoice.graphql'
import updateOfflinePaperInvoice from './mutates/updateOfflinePaperInvoice.graphql'
import updateOfflineSpecialPaperElectronicInvoice from './mutates/updateOfflineSpecialPaperElectronicInvoice.graphql'

export {
  commonQueryOfflineInvoiceImportResult,
  getAllImportData,
  getImportFailedData,
  queryForImportBatchPayOfflineInvoice,
  queryForImportBatchPaySpecialInvoiceDelivery,
  queryForImportBatchPaySpecialPaperOfflineInvoice,
  queryForImportOfflineInvoice,
  queryForImportOfflineInvoiceWithServiceId,
  queryForImportOfflineSpecialElectronicInvoice,
  queryForImportSpecialInvoiceDelivery,
  queryForImportSpecialInvoiceDeliveryWithServiceId,
  queryForImportSpecialPaperOfflineInvoice,
  queryForImportSpecialPaperOfflineInvoiceWithServiceId,
  queryShippingMethodsForSchool,
  createChannel,
  createChannelShippingMethods,
  createOfflineSpecialElectronicInvoice,
  deleteChannel,
  deliveryInvoice,
  freezeInvoice,
  importOfflineInvoice,
  importOfflineInvoiceDeliveryInfo,
  importOfflineInvoiceDeliveryInfoWithServiceId,
  importOfflineInvoiceWithServiceId,
  issueOfflineInvoice,
  pickupInvoice,
  resetInvoice,
  updateChannel,
  updateChannelShippingMethods,
  updateChannelStatus,
  updateOfflineInvoice,
  updateOfflinePaperInvoice,
  updateOfflineSpecialPaperElectronicInvoice
}
