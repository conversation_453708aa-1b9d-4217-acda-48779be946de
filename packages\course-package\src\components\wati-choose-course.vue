<template>
  <div>
    <el-table
      :data="createCoursePackage.addedList"
      row-key="id"
      ref="dragWebTable"
      max-height="500px"
      class="m-table"
      stripe
    >
      <el-table-column prop="index" type="index" label="No." align="center" width="60"></el-table-column>
      <el-table-column label="排序" width="70" align="center">
        <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
      </el-table-column>
      <!-- <el-table-column label="排序" width="70" align="center" class-name="hb-iconfont"></el-table-column> -->

      <el-table-column prop="name" label="课程名称" min-width="300"></el-table-column>

      <el-table-column prop="physicsPeriod" label="物理学时" min-width="100" align="center"></el-table-column>
      <el-table-column prop="period" label="学习学时" min-width="100" align="center">
        <template slot-scope="scope">
          <el-input-number
            :min="0.1"
            class="f-input-num"
            :precision="1"
            style="width: 70%"
            v-model="scope.row.period"
            :max="99999"
            size="mini"
            :controls="false"
            @blur="updatePeriod(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column label="操作" width="140" align="center">
        <template slot-scope="scope">
          <el-button size="mini" @click="previewCourse(scope.row.id)" type="text">预览</el-button>
          <el-button v-if="showOperation" size="mini" @click="cancelChoose(scope.row)" type="text"> 取消 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="调整学时" :visible.sync="dialogChangePeriod">
      <el-row :gutter="15">
        <el-form
          size="medium"
          ref="changePeriodForm"
          label-width="140px"
          :model="changePeriodModel"
          :rules="periodRule"
        >
          <el-col :span="20">
            <el-form-item label="学习学时">
              <span class="ci fs16">{{ changePeriodModel.originPeriodValue }}</span
              >学时
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="选课学时" prop="changePeriodValue">
              <el-input
                v-model="changePeriodModel.changePeriodValue"
                placeholder="请输入调整学时数"
                clearable
                :style="{ width: '100%' }"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveChangePeriod(true)">保 存</el-button>
        <el-button @click="saveChangePeriod(false)">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style lang="scss">
  td.hb-iconfont {
    cursor: move;

    &:hover {
      background: #bfbfbf !important;
      color: white;
    }

    &::after {
      content: '::';
    }
  }
</style>
<script lang="ts">
  import { Component, Emit, Prop, Watch, Ref, Mixins } from 'vue-property-decorator'
  import { ResponseStatus } from '@hbfe/common'
  import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'
  import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'
  import CreateCoursePackageVo from '@api/service/management/resource/course-package/mutation/vo/CreateCoursePackageVo'
  import Sortable from 'sortablejs'
  import { ElTable } from 'element-ui/types/table'
  import PreviewCourseMixins from '@hbfe/jxjy-admin-common/src/mixins/PreviewCourseMixins'

  /**
   * 调整学时使用的model
   */
  class ChangePeriodModel {
    changePeriodValue = 0
    originPeriodValue = 0
    index = 0
    id = ''
  }

  @Component
  export default class WaitChooseCourse extends Mixins(PreviewCourseMixins) {
    // 保存学时的方法

    @Prop({
      type: Array,
      default: () => new Array<CourseInCoursePackage>()
    })
    addedList: Array<CourseInCoursePackage>

    @Ref('dragWebTable')
    dragWebTable: any

    @Watch('createCoursePackage.addedList', {
      immediate: false,
      deep: true
    })
    addedListChange(val: any) {
      if (val) {
        console.log(val, 'addedListChangeaddedListChangeaddedListChange')
        this.dragElement(this.dragWebTable)
      }
    }

    dragElement(table: ElTable) {
      const el = table.$el.querySelector('.el-table__body-wrapper > table > tbody') as HTMLElement
      return new Sortable(el, {
        // Class name for the drop placeholder,// 抓取的元素的类名，简单来说就是抓取元素的时候给他加一个类名blue-background-class
        ghostClass: 'blue-background-class',
        handle: '.hb-iconfont',
        onEnd: ({ newIndex, oldIndex }) => {
          const curRow = this.createCoursePackage.addedList.splice(oldIndex, 1)[0]
          // if (newIndex < oldIndex) {
          // 行上移
          this.createCoursePackage.addedList.splice(newIndex, 0, curRow)
          const newArray = this.createCoursePackage.addedList.slice(0)
          this.createCoursePackage.addedList = []
          this.$nextTick(function () {
            this.createCoursePackage.addedList = newArray
          })
          // } else {
          // 行下移
          // table.data.splice(newIndex, 0, curRow)
          // }
        }
      })
    }

    @Prop({
      type: Object,
      default: () => new CreateCoursePackageVo()
    })
    createCoursePackage: CreateCoursePackageVo

    @Prop({
      type: Function,
      required: false,
      default: function () {
        return new ResponseStatus(200, '')
      }
    })
    savePeriod: Function
    // 是否展示操作相关的按钮

    @Prop({
      type: Boolean,
      required: false,
      default: true
    })
    showOperation: boolean

    @Prop({
      type: Array,
      required: false
    })
    chooseData: Array<CourseListDetail>

    @Watch('chooseData', {
      immediate: false,
      deep: true
    })
    chooseDataChange(val: any) {
      if (val) {
        console.log(val, '已选待确认课程')
      }
    }

    periodRule = {
      changePeriodValue: [
        { required: true, message: '学时不能为空' },
        {
          pattern: /^(\d){1,}(\.5)?$/,
          message: '学时最小一位小数,0.5的倍数',
          trigger: ['change', 'blur']
        },
        {
          validator: this.checkPeriod,
          trigger: 'change'
        }
      ]
    }

    // 调整学时弹窗控制
    dialogChangePeriod = false
    // 调整学时的model
    changePeriodModel = new ChangePeriodModel()

    checkPeriod(rule: any, value: number, callback: any) {
      if (value <= 0) {
        callback(new Error('学时必须大于0'))
      }
      callback()
    }

    /**
     * 保存调整学时
     * @param operation
     */
    saveChangePeriod(operation: boolean) {
      if (operation) {
        const el: any = this.$refs.changePeriodForm
        el.validate(async (valid: any) => {
          if (valid) {
            // const courses = new Array<CourseInPoolOperate>()
            // const course = new CourseInPoolOperate()
            // course.courseId = this.changePeriodModel.id
            // course.quantitative = -1
            // course.sequence = this.changePeriodModel.index
            // course.period = this.changePeriodModel.changePeriodValue
            // courses.push(course)
            // const status = await this.savePeriod(courses)
            // if (status.isSuccess()) {
            // CoursePoolUIModule.CHANGE_COURSE_PERIOD_BY_INDEX({
            //   index: this.changePeriodModel.index,
            //   period: this.changePeriodModel.changePeriodValue
            // })
            //   if (status.message) {
            //     this.$message.success(status.message)
            //   }
            // } else {
            //   this.$message.error('修改学时失败！')
            // }
            this.dialogChangePeriod = false
          }
        })
      } else {
        this.dialogChangePeriod = false
      }
    }

    /**
     * 取消选择
     * @param item
     */
    cancelChoose(item: CourseInCoursePackage) {
      // CoursePoolUIModule.SPLICE_COURSE_BY_ID(data.courseId)
      // // 调用取消选择事件发送，通知父组件
      // this.sendSpliceWaitChooseCourseEvent(data)

      const index = this.createCoursePackage.addedList.findIndex((itm) => {
        if (itm.id == item.id) {
          this.createCoursePackage.cancelChoose(item)
          this.$emit('packageChange')
        }
      })
    }

    // 修复period清空回显问题
    updatePeriod(row: any) {
      if (isNaN(row?.period)) row.period = 0.1
    }
  }
</script>
