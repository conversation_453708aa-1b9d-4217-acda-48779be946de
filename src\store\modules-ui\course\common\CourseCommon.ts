class CourseCommon {
  static statusValues = [
    {
      name: '全部',
      value: -1
    },
    {
      name: '正常',
      value: 1
    },
    {
      name: '停用',
      value: 0
    }
  ]

  static changeStatusValues = [
    {
      name: '请选择转换状态',
      value: null
    },
    {
      name: '转换中',
      value: 1
    },
    {
      name: '转换成功',
      value: 2
    },
    {
      name: '转换失败',
      value: 3
    }
  ]

  static filterTitle(type: string) {
    switch (type) {
      case 'create':
        return '新建分类'
      case 'modify':
        return '编辑分类'
      case 'detail':
        return '分类详情'
    }
  }
}

export default CourseCommon
