query getMockExaminationReport($statisticParam: UserExamStatisticParamDTO!,$baseStatisticParam: UserExamStatisticParamDTO!,$toCompareStatisticParam: UserExamStatisticParamDTO!,$allTypeCorrectRateParam:UserExamQuestionCorrectRateStatisticParamDTO!,$answerRecordBaseParam:ExamRecordParamDTO!,$answerRecordToCompareParam:ExamRecordParamDTO!) {
  statistic:statisticUserExamInfo(paramDTO: $statisticParam) {
		_ALL_
  }
  baseStatistic:statisticUserExamInfo(paramDTO: $baseStatisticParam) {
        _ALL_
  }
  toCompareStatistic:statisticUserExamInfo(paramDTO: $toCompareStatisticParam) {
        _ALL_
  }
  allTypeCorrectRate:statisticUserExamQuestionCorrectRateByQuestionType(paramDTO: $allTypeCorrectRateParam) {
  		_ALL_
  }
  answerRecordBase:listExamRecord(paramDTO: $answerRecordBaseParam) {
        _ALL_
  }
  answerRecordToCompareParam:listExamRecord(paramDTO: $answerRecordToCompareParam) {
        _ALL_
  }

}
