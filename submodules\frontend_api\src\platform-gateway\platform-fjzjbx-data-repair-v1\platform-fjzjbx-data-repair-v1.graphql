schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	fjzjBxDataRepair(request:FjzjBxDataRepairRequest):String @optionalLogin
	fjzjBxDataRepairTestPage(request:FjzjBxDataRepairTestPageRequest):String @optionalLogin
	fjzjBxDataRepairTestUser(request:FjzjBxDataRepairTestUserRequest):String @optionalLogin
	fjzjHandleStudentNoList(request:FjzjHandleStudentNoListRequest):String @optionalLogin
	fjzjRetryArrangeFailedTask(request:FjzjRetryArrangeFailedTaskRequest):String @optionalLogin
}
"""@Author: chenzeyu
	@CreateTime: 2024-10-12  14:58
	@Description: TODO
"""
input FjzjBxDataRepairRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.fjzjdatarepair.FjzjBxDataRepairRequest") {
	pageSize:Int!
}
"""@Author: chenzeyu
	@CreateTime: 2024-10-11  13:48
	@Description: TODO
"""
input FjzjBxDataRepairTestPageRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.fjzjdatarepair.FjzjBxDataRepairTestPageRequest") {
	pageNo:Int!
	pageSize:Int!
}
"""@Author: chenzeyu
	@CreateTime: 2024-10-11  13:49
	@Description: TODO
"""
input FjzjBxDataRepairTestUserRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.fjzjdatarepair.FjzjBxDataRepairTestUserRequest") {
	userIdList:[String]
}
"""@Author: chenzeyu
	@CreateTime: 2024-10-17  11:42
	@Description: TODO
"""
input FjzjHandleStudentNoListRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.fjzjdatarepair.FjzjHandleStudentNoListRequest") {
	studentNoList:[String]
}
"""@Author: chenzeyu
	@CreateTime: 2024-10-16  11:01
	@Description: TODO
"""
input FjzjRetryArrangeFailedTaskRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.fjzjdatarepair.FjzjRetryArrangeFailedTaskRequest") {
	mainTaskId:String
}

scalar List
