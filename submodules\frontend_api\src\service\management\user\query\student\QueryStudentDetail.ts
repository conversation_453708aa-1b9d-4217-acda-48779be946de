import { Response } from '@hbfe/common'
import MsBasicDataQueryBackstageGateway, {
  SectionAndSubjects,
  StudentIndustryResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import UserDetailVo from './vo/UserDetailVo'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import QueryTechnologyLevel from '@api/service/common/basic-data-dictionary/query/QueryTechnologyLevel'
import Occupation from '@api/service/common/basic-data-dictionary/query/QueryOccupation'
import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import QueryPropertyDetail from '@api/service/common/basic-data-dictionary/query/QueryPropertyDetail'
import QueryGrade from '@api/service/common/basic-data-dictionary/query/QueryGrade'
import QuerySubject from '@api/service/common/basic-data-dictionary/query/QuerySubject'
import QueryCategoryInfoByIds from '@api/service/common/basic-data-dictionary/query/QueryCategoryInfo'
import QueryDasicdata from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import QueryCategoryInfo from '@api/service/common/basic-data-dictionary/query/QueryCategoryInfo'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
import QueryDictionaryAcrossType from '@api/service/common/basic-data-dictionary/query/QueryDictionaryAcrossType'
/**
 * 查询用户详情
 */

class QueryStudentDetail {
  // 学员用户id
  protected studentUserId = ''
  protected trainingCategoryObj = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryTrainingCategory
  protected trainingMajorObj = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryTrainingMajor

  constructor(id: string) {
    this.studentUserId = id
  }

  /**
   * 查询用户详情
   */
  async queryDetail(): Promise<Response<UserDetailVo>> {
    const res = await MsBasicDataQueryBackstageGateway.getStudentInfoInSubProject(this.studentUserId)

    const response = new Response<UserDetailVo>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.data = new UserDetailVo()
    let nameMap = new Map<string, string>()
    if (res.data?.userInfo?.userIndustryList?.length) {
      // 人设的一级专业id后端后续不再返回，故需要我们根据二级id查父亲并填充
      const jsIndustry = res.data.userInfo.userIndustryList.find((it) => it.industryId === IndustryIdEnum.RS)
      if (jsIndustry && jsIndustry.secondProfessionalCategory) {
        const findParentRes = await QueryDictionaryAcrossType.queryCategoryInfoByIds(
          jsIndustry.secondProfessionalCategory
        )
        if (findParentRes?.length) {
          jsIndustry.firstProfessionalCategory = findParentRes[0].masterId
        }
      }
      nameMap = await this.queryTrainingCategoryAndMajorNameById(res.data?.userInfo?.userIndustryList)
    }
    response.data.from(res.data, nameMap)
    response.status = res.status
    return response
  }

  // 获取行业列表
  protected async queryIndustryList(): Promise<IndustryVo[]> {
    const res = await QueryIndustry.queryIndustry()
    const list = res.isSuccess() ? QueryIndustry.industryList : ([] as IndustryVo[])
    return list
  }
  // 获取技术等级
  async queryTechnologyLevel(id: string) {
    const res = await QueryTechnologyLevel.query()
    let name = ''
    QueryTechnologyLevel.data.forEach((item) => {
      if (parseInt(id) == item.code) {
        name = item.showName
      }
    })
    return name
  }

  /*
   根据培训类别，专业id查名称
 */
  protected async queryTrainingCategoryAndMajorNameById(
    data: Array<StudentIndustryResponse>
  ): Promise<Map<string, string>> {
    const nameMap = new Map<string, string>()
    const industryPropertyIds: Array<string> = []
    data.forEach((item) => {
      Object.keys(item).forEach((key) => {
        if (!['industryId', 'userIndustryId'].includes(key) && typeof item[key] == 'string' && item[key]) {
          industryPropertyIds.push(item[key])
        }
      })
      if (item?.industryId === IndustryIdEnum.JS) {
        item.userCertificateList?.forEach((ite) => {
          ite.certificateCategory && industryPropertyIds.push(ite.certificateCategory)
          ite.registerProfessional && industryPropertyIds.push(ite.registerProfessional)
        })
      }
    })
    const industryPropertyInfos = await QueryCategoryInfo.queryCategoryInfoByIds(industryPropertyIds)
    industryPropertyInfos?.forEach((item) => {
      nameMap.set(item.id, item.name)
    })
    const lsIndustry = data.find((item) => item.industryId === IndustryIdEnum.LS)
    if (lsIndustry && lsIndustry.sectionAndSubjects?.length) {
      await Promise.all(
        lsIndustry.sectionAndSubjects.map(async (item) => {
          const sectionRes = await QueryCategoryInfo.queryCategoryInfoByCode(
            IndustryPropertyCodeEnum.LEARNING_PHASE,
            item.section
          )
          const subjectsRes = await QueryCategoryInfo.queryCategoryInfoByCode(
            IndustryPropertyCodeEnum.DISCIPLINE,
            item.subjects
          )
          nameMap.set('section' + item.section, sectionRes?.name)
          nameMap.set('subject' + item.subjects, subjectsRes?.name)
        })
      )
    }
    return nameMap
  }
}
export default QueryStudentDetail
