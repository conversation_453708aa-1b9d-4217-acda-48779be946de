import { Response, Page, ResponseStatus } from '@hbfe/common'
import CourseMaintenanceQueryVo from '@api/service/diff/management/byzj/train-class/model/CourseMaintenanceQueryVo'
import CourseMaintenanceInfoVo from '@api/service/diff/management/byzj/train-class/model/CourseMaintenanceInfoVo'
import PlatformJxjypxtyptSchool, {
  CreCourseSubjectResponse,
  ImportCourseSubjectRequest
} from '@api/diff-gateway/platform-jxjypxtypt-byzj-school'
import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'

class QueryCourseMaintenance {
  /**
   * 分页查询公需课课程维护列表
   */
  async pageCourseSubject(page: Page, params: CourseMaintenanceQueryVo) {
    return await PlatformJxjypxtyptSchool.pageCourseSubjectInServicer({
      page,
      request: params.toDto()
    })
  }
  /**
   * @description: 根据筛选条件查询公需课课程维护列表
   * @param {Page} page
   * @param {CourseMaintenanceQueryVo} params
   */
  async queryMaintenanceList(page: Page, params: CourseMaintenanceQueryVo): Promise<CourseMaintenanceInfoVo[]> {
    const { data, status } = await this.pageCourseSubject(page, params)

    if (!status.isSuccess() || !data.currentPageData?.length) {
      return []
    }

    // 一次性提取所有区域ID并映射对象
    const regionList = data.currentPageData.map((item) => item.regional)
    const regionNameMap = await QueryBusinessRegion.queryRegionsNameByIds(regionList)
    page.pageSize = data?.pageSize
    page.totalSize = data?.totalSize
    return data.currentPageData.map((item) => {
      const vo = CourseMaintenanceInfoVo.from(item)
      vo.regionName = regionNameMap?.get(vo?.region)
      return vo
    })
  }

  /**
   * 导入公需课课程接口
   */
  async importCourseSubjectByExcel(filePath: string, fileName: string) {
    const importCourseSubjectRequest = new ImportCourseSubjectRequest()
    importCourseSubjectRequest.filePath = filePath
    importCourseSubjectRequest.fileName = fileName
    return await PlatformJxjypxtyptSchool.importCourseSubjectByExcelInServicer(importCourseSubjectRequest)
  }
  /**
   * 导入公需课课程
   */
  async importPublicCourses(filePath: string, fileName: string): Promise<ResponseStatus> {
    const { data, status } = await this.importCourseSubjectByExcel(filePath, fileName)
    if (status.isSuccess()) {
      return new ResponseStatus(Number(data.code), status.getMessage())
    }

    return new ResponseStatus(status.code, status.getMessage())
  }
}

export default QueryCourseMaintenance
