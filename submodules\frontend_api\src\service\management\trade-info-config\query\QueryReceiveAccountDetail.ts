import msTradeQueryFrontGatewayTradeQueryBackstage, {
  ReceiveAccountConfigResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { AccountTypeEunm } from '@api/service/common/enums/trade-configuration/AccountType'
import AliPayReceiveAccountDetailVo from './vo/AliPayReceiveAccountDetailVo'
import OfflinePayReceiveAccountDetailVo from './vo/OfflinePayReceiveAccountDetailVo'
import ReceiveAccountDetailVo from './vo/ReceiveAccountDetailVo'
import WXPayReceiveAccountDetailVo from './vo/WXPayReceiveAccountDetailVo'
import { PayAccountTypeEnum } from '@api/service/management/trade-info-config/enums/PayAccountTypeEnum'
import { XYPayReceiveAccountDetailVo } from '@api/service/management/trade-info-config/query/vo/XYPayReceiveAccountDetailVo'
import { JSPayReceiveAccountDetailVo } from '@api/service/management/trade-info-config/query/vo/JSPayReceiveAccountDetailVo'
import { WFTPayReceiveAccountDetailVo } from '@api/service/management/trade-info-config/query/vo/WFTPayReceiveAccountDetailVo'
import { XDLPayReceiveAccountDetailVo } from '@api/service/management/trade-info-config/query/vo/XDLPayReceiveAccountDetailVo'
class QueryReceiveAccountDetail {
  receiveAccountId = ''
  receiveAccountDetail = new ReceiveAccountDetailVo()

  constructor(receiveAccountId: string) {
    this.receiveAccountId = receiveAccountId
  }
  /**
   * 查询收款账户详情
   * @returns
   */
  async queryDetail(): Promise<ReceiveAccountDetailVo> {
    const msRes = await msTradeQueryFrontGatewayTradeQueryBackstage.getReceiveAccountInServicer(this.receiveAccountId)
    if (msRes.status.isSuccess()) {
      this.detailByPaymentChannelType(msRes.data)
    }
    return this.receiveAccountDetail
  }

  /**
   * 根据线上、线下，支付账号类型进行创建类的初始化
   * @param accountType
   * @param paymentChannelId
   */
  async detailByPaymentChannelType(res: ReceiveAccountConfigResponse) {
    const PaymentChannelTtype = (type: string) => {
      if (type.indexOf(PayAccountTypeEnum.ALIPAY) > -1) {
        return new AliPayReceiveAccountDetailVo()
      }
      if (type.indexOf(PayAccountTypeEnum.WXPAY) > -1) {
        return new WXPayReceiveAccountDetailVo()
      }
      if (type.indexOf(PayAccountTypeEnum.CIB_PAY) > -1) {
        return new XYPayReceiveAccountDetailVo()
      }
      if (type.indexOf(PayAccountTypeEnum.CCB_PAY) > -1) {
        return new JSPayReceiveAccountDetailVo()
      }
      if (type.indexOf(PayAccountTypeEnum.SWIFT_PASS_PAY) > -1) {
        return new WFTPayReceiveAccountDetailVo()
      }
      if (type.indexOf(PayAccountTypeEnum.NEW_LAND_PAY) > -1) {
        return new XDLPayReceiveAccountDetailVo()
      }
    }
    if (res.accountType === AccountTypeEunm.OFFLINE) {
      this.receiveAccountDetail = new OfflinePayReceiveAccountDetailVo()
    } else {
      this.receiveAccountDetail = PaymentChannelTtype(res.paymentChannelId)
    }
    this.receiveAccountDetail?.from(res)
    if (this.receiveAccountDetail?.taxPayerId) {
      const taxerMsRes = await msTradeQueryFrontGatewayTradeQueryBackstage.getTaxpayerInServicer(
        this.receiveAccountDetail.taxPayerId
      )
      if (taxerMsRes.status.isSuccess()) {
        this.receiveAccountDetail.taxPayerName = taxerMsRes.data?.name || ''
      }
    }
  }
}
export default QueryReceiveAccountDetail
