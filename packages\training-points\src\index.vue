<template slot-scope="scope">
  <el-main>
    <div class="f-p15" v-if="$hasPermission('query')" desc="查询" actions="doQueryPage,created">
      <div class="f-mb15">
        <el-button
          type="primary"
          icon="el-icon-plus"
          v-if="$hasPermission('create')"
          desc="新建"
          actions="@hbfe/jxjy-admin-trainingPoints/src/modify.vue"
          @click="toUrl('add')"
          >新增培训点</el-button
        >
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训点名称">
                <el-input
                  clearable
                  v-model="trainingPointManage.params.trainingPlaceName"
                  placeholder="请输入培训点名称"
                />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="是否被引用">
                <el-select clearable placeholder="请选择是否被引用" v-model="trainingPointManage.params.isUsed">
                  <el-option v-for="item in YesOrNoEnumsList" :key="item.code" :value="item.code" :label="item.desc">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训点状态">
                <el-select
                  clearable
                  placeholder="请选择培训点状态"
                  v-model="trainingPointManage.params.trainingPlaceStatus"
                >
                  <el-option v-for="item in courseStatusList" :key="item.code" :value="item.code" :label="item.desc">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary" @click="doSearch">查询</el-button>
                <el-button @click="doReset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table stripe :data="trainingPointManage.list" max-height="500px" class="m-table" v-loading="tableLoading">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>

          <el-table-column label="培训点名称" min-width="280" fixed="left">
            <template v-slot="{ row }">{{ row.trainingPlaceName }}</template>
          </el-table-column>
          <el-table-column label="培训点所在地区" min-width="200">
            <template v-slot="{ row }">{{ row.trainingPlaceRegionNames }}</template>
          </el-table-column>
          <el-table-column label="培训教室" min-width="200" align="center">
            <template v-slot="{ row }">{{ row.classroomName }}</template>
          </el-table-column>
          <el-table-column label="是否被引用" min-width="120" align="center">
            <template v-slot="{ row }">{{ row.isUsed }}</template>
          </el-table-column>
          <el-table-column label="培训点状态" min-width="100" align="center">
            <template v-slot="{ row }">
              <div v-if="row.trainingPlaceStatus.equal(CourseStatusEnum.DISABLE)">
                <el-tag type="info">{{ row.trainingPlaceStatus }}</el-tag>
              </div>
              <div v-if="row.trainingPlaceStatus.equal(CourseStatusEnum.ENABLE)">
                <el-tag type="success"> {{ row.trainingPlaceStatus }}</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" align="center" fixed="right">
            <template v-slot="{ row }">
              <el-button
                type="text"
                @click="toUrl('modify', row)"
                v-if="$hasPermission('modify')"
                desc="编辑"
                actions="@hbfe/jxjy-admin-trainingPoints/src/modify.vue"
                >编辑</el-button
              >
              <el-button
                type="text"
                @click="toDetailUrl(row)"
                v-if="$hasPermission('detail')"
                desc="详情"
                actions="@hbfe/jxjy-admin-trainingPoints/src/detail.vue"
                >详情</el-button
              >
              <!--              @click="deactivate(row)"-->

              <el-button
                type="text"
                @click="deactivate(row)"
                v-if="$hasPermission('deactivate')"
                desc="停用"
                actions="deactivate"
              >
                {{ row.trainingPlaceStatus.equal(CourseStatusEnum.ENABLE) ? '停用' : '启用' }}
              </el-button>
              <el-button
                type="text"
                @click="delClick(row)"
                v-if="$hasPermission('remove')"
                desc="删除"
                :disabled="row.isUsed.equal(YesOrNoEnums.yes)"
                actions="delClick"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Watch } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import TrainingPlaceManage from '@api/service/management/resource/training-place-manage/TrainingPlaceManage'
  import TrainingPlaceInfo from '@api/service/management/resource/training-place-manage/TrainingPlaceInfo'
  import TrainingPointManageParams from '@api/service/management/resource/training-place-manage/models/TrainingPlaceManageParams'
  import YesOrNoEnumsClass, { YesOrNoEnums } from '@api/service/common/enums/general/YesOrNoEnums'
  import CourseStatus, { CourseStatusEnum } from '@api/service/common/enums/course/CourseStatus'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import { bind, debounce } from 'lodash-decorators'
  @Component
  export default class extends Vue {
    page: UiPage
    query: Query = new Query()
    trainingPointManage = new TrainingPlaceManage()
    trainingPointInfoInfo = new TrainingPlaceInfo()

    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    input = ''
    select = ''
    tableLoading = false
    YesOrNoEnums = YesOrNoEnums

    created() {
      this.doQueryPage()
    }
    // 页面激活
    activated() {
      this.doQueryPage()
    }
    CourseStatusEnum = CourseStatusEnum
    yesOrNoEnumsInstance = new YesOrNoEnumsClass()
    YesOrNoEnumsList: EnumOption<YesOrNoEnums>[] = this.yesOrNoEnumsInstance.list()
    courseStatus = new CourseStatus()
    courseStatusList = this.courseStatus.list()

    /**
     * 重置
     * */
    @bind
    @debounce(200)
    doReset() {
      this.trainingPointManage.params = new TrainingPointManageParams()
      this.doQueryPage()
    }

    /**
     * 查询列表
     * */
    async doQueryPage() {
      this.tableLoading = true
      await this.trainingPointManage.queryList(this.page)
      this.tableLoading = false
    }
    //
    /**
     * 查询
     */
    @bind
    @debounce(200)
    doSearch() {
      this.doQueryPage()
    }

    /**
     * 停用
     */
    @bind
    @debounce(200)
    deactivate(item: TrainingPlaceInfo) {
      let msg = ''
      if (item.trainingPlaceStatus.equal(CourseStatusEnum.DISABLE)) {
        msg = '确认启用？'
      } else {
        msg = '确认停用？'
      }
      this.$confirm(`${msg}`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        distinguishCancelAndClose: true, // 重要，设置为true才会把右上角X和取消区分开来
        center: false,
        type: 'warning'
      })
        .then(async () => {
          try {
            const res = await item.changeStatus()

            if (res.status.code === 200) {
              await this.$message.success('操作成功')
              //延迟500ms 查询
              setTimeout(async () => {
                await this.doQueryPage()
              }, 500)
            }
          } catch (e) {
            console.log(e)
          }
        })
        .catch((action) => {
          //
        })
    }
    /**
     * 删除
     */
    @bind
    @debounce(200)
    delClick(item: TrainingPlaceInfo) {
      this.$confirm(`删除后无法恢复，是否继续删除？`, {
        confirmButtonText: '继续操作',
        cancelButtonText: '取消',
        distinguishCancelAndClose: true, // 重要，设置为true才会把右上角X和取消区分开来
        center: false,
        type: 'warning'
      })
        .then(async () => {
          const res = await item.deleteTrainingPlace()
          if (res.data.code === 'E501') {
            this.$alert('提示', {
              message: '培训点已被引用，不可删除！',
              confirmButtonText: '确定',
              type: 'warning'
            })
          } else if (res.data.code === '200') {
            this.$message.success('操作成功')
            this.doQueryPage()
          }
        })
        .catch((action) => {
          //
        })
    }
    /**
     * 详情
     */
    @bind
    @debounce(200)
    toDetailUrl(row: any) {
      this.$router.push(`/resource/training-points/detail/${row.id}`)
    }
    //
    /**
     * 修改/
     */
    @bind
    @debounce(200)
    toUrl(type: string, item?: any) {
      if (item) {
        this.$router.push(`/resource/training-points/modify/${type}?id=${item.id}`)
      } else {
        this.$router.push(`/resource/training-points/modify/${type}`)
      }
    }
  }
</script>
