import { CoursewareCategoryResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class CoursewareTreeListDetail {
  id: string
  name: string
  parentId: string

  hasChildren = true

  children: Array<CoursewareTreeListDetail>

  static from(response: CoursewareCategoryResponse) {
    const detail = new CoursewareTreeListDetail()
    detail.id = response.id
    detail.name = response.name
    return detail
  }
}

export default CoursewareTreeListDetail
