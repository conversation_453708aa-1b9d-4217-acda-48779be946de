import findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType from './queries/findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType.graphql'
import getAreaTrainingChannelStudentInfoInMyself from './queries/getAreaTrainingChannelStudentInfoInMyself.graphql'
import getBannerListByPortalId from './queries/getBannerListByPortalId.graphql'
import getBannerListByPortalType from './queries/getBannerListByPortalType.graphql'
import getBannerListByPortalTypeForFxpt from './queries/getBannerListByPortalTypeForFxpt.graphql'
import getBusinessRegionById from './queries/getBusinessRegionById.graphql'
import getBusinessRegionTree from './queries/getBusinessRegionTree.graphql'
import getClientInfoByServicerId from './queries/getClientInfoByServicerId.graphql'
import getCollectiveInfoInMyself from './queries/getCollectiveInfoInMyself.graphql'
import getCommonNewsDetailWithPreviousAndNext from './queries/getCommonNewsDetailWithPreviousAndNext.graphql'
import getCommonNewsDetailWithPreviousAndNextInDistributor from './queries/getCommonNewsDetailWithPreviousAndNextInDistributor.graphql'
import getFriendLinkListByPortalId from './queries/getFriendLinkListByPortalId.graphql'
import getFriendLinkListByPortalType from './queries/getFriendLinkListByPortalType.graphql'
import getIndustryInfo from './queries/getIndustryInfo.graphql'
import getIndustryInfoV2 from './queries/getIndustryInfoV2.graphql'
import getIndustryPropertyByIdInSubProject from './queries/getIndustryPropertyByIdInSubProject.graphql'
import getIndustryType from './queries/getIndustryType.graphql'
import getLeaderPositionLevel from './queries/getLeaderPositionLevel.graphql'
import getMenusByPortalType from './queries/getMenusByPortalType.graphql'
import getNewsCategoryId from './queries/getNewsCategoryId.graphql'
import getNewsDetailWithPreviousAndNext from './queries/getNewsDetailWithPreviousAndNext.graphql'
import getPhysicalRegionById from './queries/getPhysicalRegionById.graphql'
import getPortalInfoInSubProject from './queries/getPortalInfoInSubProject.graphql'
import getServiceOrIndustryRegion from './queries/getServiceOrIndustryRegion.graphql'
import getServiceOrIndustryRegionByQuery from './queries/getServiceOrIndustryRegionByQuery.graphql'
import getStudentInfoInMyself from './queries/getStudentInfoInMyself.graphql'
import getTrainingCategoryInfo from './queries/getTrainingCategoryInfo.graphql'
import getTrainingChannelCommonNewsDetailWithPreviousAndNext from './queries/getTrainingChannelCommonNewsDetailWithPreviousAndNext.graphql'
import getTrainingInstitutionPortalInfo from './queries/getTrainingInstitutionPortalInfo.graphql'
import getUnitType from './queries/getUnitType.graphql'
import getYearById from './queries/getYearById.graphql'
import listALLIndustryPropertyRootByCategory from './queries/listALLIndustryPropertyRootByCategory.graphql'
import listALLIndustryPropertyRootByCategoryV2 from './queries/listALLIndustryPropertyRootByCategoryV2.graphql'
import listAllIndustryProperty from './queries/listAllIndustryProperty.graphql'
import listBusinessDictionaryAcrossTypeBySalveId from './queries/listBusinessDictionaryAcrossTypeBySalveId.graphql'
import listBusinessRegionChildById from './queries/listBusinessRegionChildById.graphql'
import listBusinessRegionListById from './queries/listBusinessRegionListById.graphql'
import listBusinessRegionNameMap from './queries/listBusinessRegionNameMap.graphql'
import listBusinessRegionTreeChild from './queries/listBusinessRegionTreeChild.graphql'
import listBusinessRegionTreeRoot from './queries/listBusinessRegionTreeRoot.graphql'
import listChildNewsCategory from './queries/listChildNewsCategory.graphql'
import listChildNewsCategoryInServicer from './queries/listChildNewsCategoryInServicer.graphql'
import listEducationProperty from './queries/listEducationProperty.graphql'
import listIndustryInfo from './queries/listIndustryInfo.graphql'
import listIndustryInfoV2 from './queries/listIndustryInfoV2.graphql'
import listIndustryPropertyByOnlineSchool from './queries/listIndustryPropertyByOnlineSchool.graphql'
import listIndustryPropertyByOnlineSchoolV2 from './queries/listIndustryPropertyByOnlineSchoolV2.graphql'
import listIndustryPropertyCategory from './queries/listIndustryPropertyCategory.graphql'
import listIndustryPropertyCategoryV2 from './queries/listIndustryPropertyCategoryV2.graphql'
import listIndustryPropertyChildByCategory from './queries/listIndustryPropertyChildByCategory.graphql'
import listIndustryPropertyChildByCategoryV2 from './queries/listIndustryPropertyChildByCategoryV2.graphql'
import listIndustryPropertyRootByCategory from './queries/listIndustryPropertyRootByCategory.graphql'
import listIndustryPropertyRootByCategoryV2 from './queries/listIndustryPropertyRootByCategoryV2.graphql'
import listIndustryTypeChild from './queries/listIndustryTypeChild.graphql'
import listIndustryTypeRoot from './queries/listIndustryTypeRoot.graphql'
import listLeaderPositionLevel from './queries/listLeaderPositionLevel.graphql'
import listLeaderPositionLevelRoot from './queries/listLeaderPositionLevelRoot.graphql'
import listNewsCategoryTree from './queries/listNewsCategoryTree.graphql'
import listPhysicalRegionChildById from './queries/listPhysicalRegionChildById.graphql'
import listPhysicalRegionListById from './queries/listPhysicalRegionListById.graphql'
import listPopUpsNews from './queries/listPopUpsNews.graphql'
import listPopUpsNewsByPortalId from './queries/listPopUpsNewsByPortalId.graphql'
import listReviewTopNews from './queries/listReviewTopNews.graphql'
import listRootNewsCategory from './queries/listRootNewsCategory.graphql'
import listSalveDictionaryByMasterId from './queries/listSalveDictionaryByMasterId.graphql'
import listServicerContractPropertyByCategory from './queries/listServicerContractPropertyByCategory.graphql'
import listStudentInfoByUserIdInServicer from './queries/listStudentInfoByUserIdInServicer.graphql'
import listTopNewsCategory from './queries/listTopNewsCategory.graphql'
import listTrainingCategoryChild from './queries/listTrainingCategoryChild.graphql'
import listTrainingCategoryInfo from './queries/listTrainingCategoryInfo.graphql'
import listTrainingCategoryRoot from './queries/listTrainingCategoryRoot.graphql'
import listTrainingChannelPopUpsNews from './queries/listTrainingChannelPopUpsNews.graphql'
import listTrainingChannelReviewTopNews from './queries/listTrainingChannelReviewTopNews.graphql'
import listUnitTypeChild from './queries/listUnitTypeChild.graphql'
import listUnitTypeRoot from './queries/listUnitTypeRoot.graphql'
import listYearListById from './queries/listYearListById.graphql'
import pageALLIndustryPropertyRootByCategoryV2 from './queries/pageALLIndustryPropertyRootByCategoryV2.graphql'
import pageCommonSimpleNewsByPublish from './queries/pageCommonSimpleNewsByPublish.graphql'
import pageCommonSimpleNewsByPublishByPortalId from './queries/pageCommonSimpleNewsByPublishByPortalId.graphql'
import pageCommonSimpleNewsByPublishInServicer from './queries/pageCommonSimpleNewsByPublishInServicer.graphql'
import pageCompleteNewsByCodeList from './queries/pageCompleteNewsByCodeList.graphql'
import pageCompleteNewsByRootCategoryCode from './queries/pageCompleteNewsByRootCategoryCode.graphql'
import pageIndustryPropertyByCategoryInSubProject from './queries/pageIndustryPropertyByCategoryInSubProject.graphql'
import pageIndustryPropertyInSubProject from './queries/pageIndustryPropertyInSubProject.graphql'
import pagePortalInfoInSubProject from './queries/pagePortalInfoInSubProject.graphql'
import pageSimpleNewsByPublish from './queries/pageSimpleNewsByPublish.graphql'
import pageSimpleNewsByPublishAndAreaCodePath from './queries/pageSimpleNewsByPublishAndAreaCodePath.graphql'
import pageSimpleNewsByPublishAndAreaCodePathInServicer from './queries/pageSimpleNewsByPublishAndAreaCodePathInServicer.graphql'
import pageSimpleNewsByPublishForOrder from './queries/pageSimpleNewsByPublishForOrder.graphql'
import pageTrainingChannelCommonSimpleNewsByPublish from './queries/pageTrainingChannelCommonSimpleNewsByPublish.graphql'
import pageTrainingChannelCompleteNewsByCodeList from './queries/pageTrainingChannelCompleteNewsByCodeList.graphql'
import statisticStudentCountInServicer from './queries/statisticStudentCountInServicer.graphql'

export {
  findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType,
  getAreaTrainingChannelStudentInfoInMyself,
  getBannerListByPortalId,
  getBannerListByPortalType,
  getBannerListByPortalTypeForFxpt,
  getBusinessRegionById,
  getBusinessRegionTree,
  getClientInfoByServicerId,
  getCollectiveInfoInMyself,
  getCommonNewsDetailWithPreviousAndNext,
  getCommonNewsDetailWithPreviousAndNextInDistributor,
  getFriendLinkListByPortalId,
  getFriendLinkListByPortalType,
  getIndustryInfo,
  getIndustryInfoV2,
  getIndustryPropertyByIdInSubProject,
  getIndustryType,
  getLeaderPositionLevel,
  getMenusByPortalType,
  getNewsCategoryId,
  getNewsDetailWithPreviousAndNext,
  getPhysicalRegionById,
  getPortalInfoInSubProject,
  getServiceOrIndustryRegion,
  getServiceOrIndustryRegionByQuery,
  getStudentInfoInMyself,
  getTrainingCategoryInfo,
  getTrainingChannelCommonNewsDetailWithPreviousAndNext,
  getTrainingInstitutionPortalInfo,
  getUnitType,
  getYearById,
  listALLIndustryPropertyRootByCategory,
  listALLIndustryPropertyRootByCategoryV2,
  listAllIndustryProperty,
  listBusinessDictionaryAcrossTypeBySalveId,
  listBusinessRegionChildById,
  listBusinessRegionListById,
  listBusinessRegionNameMap,
  listBusinessRegionTreeChild,
  listBusinessRegionTreeRoot,
  listChildNewsCategory,
  listChildNewsCategoryInServicer,
  listEducationProperty,
  listIndustryInfo,
  listIndustryInfoV2,
  listIndustryPropertyByOnlineSchool,
  listIndustryPropertyByOnlineSchoolV2,
  listIndustryPropertyCategory,
  listIndustryPropertyCategoryV2,
  listIndustryPropertyChildByCategory,
  listIndustryPropertyChildByCategoryV2,
  listIndustryPropertyRootByCategory,
  listIndustryPropertyRootByCategoryV2,
  listIndustryTypeChild,
  listIndustryTypeRoot,
  listLeaderPositionLevel,
  listLeaderPositionLevelRoot,
  listNewsCategoryTree,
  listPhysicalRegionChildById,
  listPhysicalRegionListById,
  listPopUpsNews,
  listPopUpsNewsByPortalId,
  listReviewTopNews,
  listRootNewsCategory,
  listSalveDictionaryByMasterId,
  listServicerContractPropertyByCategory,
  listStudentInfoByUserIdInServicer,
  listTopNewsCategory,
  listTrainingCategoryChild,
  listTrainingCategoryInfo,
  listTrainingCategoryRoot,
  listTrainingChannelPopUpsNews,
  listTrainingChannelReviewTopNews,
  listUnitTypeChild,
  listUnitTypeRoot,
  listYearListById,
  pageALLIndustryPropertyRootByCategoryV2,
  pageCommonSimpleNewsByPublish,
  pageCommonSimpleNewsByPublishByPortalId,
  pageCommonSimpleNewsByPublishInServicer,
  pageCompleteNewsByCodeList,
  pageCompleteNewsByRootCategoryCode,
  pageIndustryPropertyByCategoryInSubProject,
  pageIndustryPropertyInSubProject,
  pagePortalInfoInSubProject,
  pageSimpleNewsByPublish,
  pageSimpleNewsByPublishAndAreaCodePath,
  pageSimpleNewsByPublishAndAreaCodePathInServicer,
  pageSimpleNewsByPublishForOrder,
  pageTrainingChannelCommonSimpleNewsByPublish,
  pageTrainingChannelCompleteNewsByCodeList,
  statisticStudentCountInServicer
}
