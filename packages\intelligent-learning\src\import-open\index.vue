<template>
  <div
    v-if="$hasPermission('studyImport')"
    desc="导入开班并学习"
    actions="importStudentAndOpenClass,@QueryTrainingScheme,downloadTemplate,@ChooseSpecialSubject,created"
  >
    <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
      <p>温馨提示：</p>
      <p>1.导入学员开班并学习，将由系统以证件号（登录帐号）和报名的培训班为依据，进行模拟学习。</p>
      <p>
        2.对未存在于平台中的学员自动生成帐号，对于已存在于平台内的学员如需更新除证件号外的基础信息，请填写该字段，为空默认为不更新。已存在的用户不支持修改密码；
      </p>
      <p>
        3.填写的信息，请按下载的模板填写要求，严格填写。导入表格一次最多支持1000条记录，若超过1000条则不能正常导入；
      </p>
      <p>
        4.批量创建失败的原因。请查阅“导入任务查阅”并下载失败数据！你可以下载未导入成功的信息表，修改正确后再试。你仅需再次上传出错的记录即可。
      </p>
      <p>
        5.如网校开启工作单位配置天眼查/企查查，通过第三方没有查询到对应的单位无法录入系统，如需跳过查询环节，请在对应的单位字段前添加*，例如：*单位名称。
      </p>
      <p>6.培训形式为面授、面网授的班级暂不支持操作导入学员开班并学习。</p>
    </el-alert>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-row type="flex" justify="center">
          <el-col :sm="14" :lg="10">
            <el-steps direction="vertical" :active="5" class="m-vertical-steps">
              <el-step title="下载批量创建学员模版，填写要求信息">
                <div slot="description">
                  <el-button
                    type="primary"
                    size="small"
                    plain
                    class="f-mt5"
                    @click="downloadTemplate"
                    icon="el-icon-download"
                  >
                    下载模板
                  </el-button>
                  <el-button @click="isTrainingSchemeDialog" type="text" size="small" class="f-mt5 f-pl20">
                    查询平台可报培训方案
                  </el-button>
                </div>
              </el-step>
              <el-step title="上传填写好的开通学习权限表格">
                <div slot="description">
                  <min-upload-file v-model="hbFileUploadResponse" :file-type="1" :is-protected="true">
                    <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                      选择文件
                    </el-button>
                  </min-upload-file>
                </div>
              </el-step>
              <el-step>
                <div slot="title">
                  设置默认密码：
                  <div class="f-flex">
                    <span class="m-radio-group-title">默认密码：</span>
                    <el-radio-group v-model="radio" class="default-psw">
                      <el-radio :label="PasswordModelEnum.password_model_zero">000000</el-radio>
                      <el-radio :label="PasswordModelEnum.password_model_abc">abc123</el-radio>
                      <el-radio :label="PasswordModelEnum.password_model_id_card">证件号后六位</el-radio>
                      <el-radio :label="PasswordModelEnum.password_model_custom">
                        <span class="f-mr10">自定义密码</span>
                        <!--选中后出现输入框-->
                        <el-input
                          v-model="password"
                          v-if="radio === PasswordModelEnum.password_model_custom"
                          clearable
                          placeholder="请输入自定义密码"
                        />
                      </el-radio>
                    </el-radio-group>
                  </div>
                  <div class="f-flex f-mt20">
                    <span class="m-radio-group-title">密码生效范围：</span>
                    <el-radio-group v-model="radio1" class="default-psw">
                      <el-radio :label="5">仅新用户</el-radio>
                      <el-radio :label="6">全部用户（含已注册）</el-radio>
                    </el-radio-group>
                  </div>
                </div>
              </el-step>
              <el-step>
                <div slot="title">
                  已注册学员更新基础信息：
                  <el-radio-group v-model="radio2">
                    <el-radio :label="7" class="f-mr50">是</el-radio>
                    <el-radio :label="8">否</el-radio>
                  </el-radio-group>
                </div>
              </el-step>

              <el-step>
                <div slot="title">
                  请选择是否按照指定专题导入
                  <el-radio-group v-model="radioSpecial" class="default-psw">
                    <el-radio :label="9">不按照专题导入</el-radio>
                    <el-radio :label="10">指定专题导入</el-radio>
                  </el-radio-group>
                </div>
                <div slot="description" v-if="radioSpecial === 10">
                  <el-button type="primary" size="small" plain class="f-mt5" @click="isSpecialSubjectDialog">
                    选择专题
                  </el-button>
                  <div class="f-c6 f-f14 f-mt20" v-if="thematicManagementItem">
                    <div class="f-fb">已选专题名称：</div>
                    <div class="">
                      <div class="f-mt10">{{ thematicManagementItem.basicInfo.subjectName }}</div>
                    </div>
                  </div>
                </div>
              </el-step>
            </el-steps>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <template v-if="$hasPermission('WXImport')" desc="网校导入开班" actions="doImportOpen">
          <el-button type="primary" v-loading="loading" @click="importStudentAndOpenClass">导入</el-button>
        </template>
      </div>
    </div>

    <el-dialog title="提示" :visible.sync="importSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导入成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导入任务查看-导入学员开班并学习</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="importSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goImportDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>

    <!-- 查询平台可报培训方案抽屉 -->
    <query-training-scheme ref="queryTrainingScheme" :isOnlyOnline="true"></query-training-scheme>

    <!-- 选择专题的抽屉 -->
    <choose-special-subject ref="chooseSpecialSubject" @select="select"></choose-special-subject>
  </div>
</template>

<script lang="ts">
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import ChooseSpecialSubject from '@hbfe/jxjy-admin-import/src/scheme/components/choose-special-subject.vue'
  import QueryTrainingScheme from '@hbfe/jxjy-admin-import/src/scheme/components/query-training-scheme.vue'
  import MinUploadFile from '@hbfe/jxjy-admin-platform/src/function/components/min-upload-file.vue'
  import { bind, debounce } from 'lodash-decorators'
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import ImportOpenLearningInTrainingChannel from '@api/service/management/intelligence-learning/ImportOpenLearningInTrainingChannel'
  import ImportOpenLearningInServicer from '@api/service/management/intelligence-learning/ImportOpenLearningInServicer'
  import ImportOpenLearningParam from '@api/service/management/intelligence-learning/model/ImportOpenLearningParam'
  import { PasswordModelEnum } from '@api/service/management/intelligence-learning/enum/PasswordModelEnum'
  import { EffectiveRangeEnum } from '@api/service/management/intelligence-learning/enum/EffectiveRangeEnum'

  @Component({
    computed: {
      PasswordModelEnum() {
        return PasswordModelEnum
      }
    },
    components: { MinUploadFile, QueryTrainingScheme, ChooseSpecialSubject }
  })
  export default class extends Vue {
    @Ref('queryTrainingScheme') queryTrainingScheme: any
    @Ref('chooseSpecialSubject') chooseSpecialSubject: any
    @Ref('importTabRef') importTabRef: any

    radio: PasswordModelEnum = null
    radio1: number = null
    radio2: number = null
    radioSpecial: number = null
    //文件上传之后的回调参数
    hbFileUploadResponse = new HBFileUploadResponse()

    /**
     * 专题导入开班并学习充血模型
     */
    importOpenLearningInTrainingChannel = new ImportOpenLearningInTrainingChannel()
    /**
     * 网校导入开班并学习充血模型
     */
    importOpenLearningInServicer = new ImportOpenLearningInServicer()

    /**
     * 请求参数
     */
    importOpenLearningParam = new ImportOpenLearningParam()

    thematicManagementItem: ThematicManagementItem = null

    // 自定义密码
    password = ''
    // 导入弹窗显隐
    importSuccessVisible = false

    loading = false

    async created() {
      try {
        // 查询智能学习配置
        // const res = await this.intelligenceLearningModule.doQueryServiceConfigByServicerId(
        //   Context.businessEnvironment.serviceToken.tokenMeta.servicerId
        // )
      } catch (error) {
        console.log(error)
      }
    }

    //下载导入开班模板
    @bind
    @debounce(1000)
    async downloadTemplate() {
      const link = document.createElement('a')
      let res
      try {
        res = await this.importOpenLearningInServicer.queryImportOpenTemplatePathByCategory()
      } catch (e) {
        return this.$message.error('下载模板失败')
      }

      if (!res.status.isSuccess()) {
        return this.$message.error('下载模板失败')
      }
      const resolver = this.$router.resolve({
        name: '/mfs' + res.data
      })
      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      const urlArr = link.href.split('.'),
        typeName = urlArr.pop()
      const fileName = '导入学员开班并学习'
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      link.remove()
    }

    //导入学员
    @bind
    @debounce(1000)
    async importStudentAndOpenClass() {
      if (this.hbFileUploadResponse.url) {
        // 导入前校验-默认密码
        if (!this.radio) {
          return this.$confirm('请选择默认密码。', '系统提醒', {
            confirmButtonText: '我知道了',
            showCancelButton: false,
            showClose: false,
            center: true
          }).then(() => {
            this.loading = false
          })
        }
        // 导入前校验-密码生效范围
        if (!this.radio1) {
          return this.$confirm('请选择密码生效范围。', '系统提醒', {
            confirmButtonText: '我知道了',
            showCancelButton: false,
            showClose: false,
            center: true
          }).then(() => {
            this.loading = false
          })
        }
        // 导入前校验-是否更新已注册学员基础信息
        if (!this.radio2) {
          return this.$confirm('请选择是否更新已注册学员基础信息。', '系统提醒', {
            confirmButtonText: '我知道了',
            showCancelButton: false,
            showClose: false,
            center: true
          }).then(() => {
            this.loading = false
          })
        }
        // 导入前校验-是否按照指定专题导入
        if (!this.radioSpecial) {
          return this.$confirm('请选择是否按照指定专题导入。', '系统提醒', {
            confirmButtonText: '我知道了',
            showCancelButton: false,
            showClose: false,
            center: true
          }).then(() => {
            this.loading = false
          })
        }

        this.loading = true
        this.importOpenLearningParam = new ImportOpenLearningParam()
        this.importOpenLearningParam.filePath = this.hbFileUploadResponse.url
        this.importOpenLearningParam.fileName = this.hbFileUploadResponse.fileName
        this.importOpenLearningParam.terminalCode = 'Web'
        if (this.radio === PasswordModelEnum.password_model_zero) {
          this.importOpenLearningParam.password = PasswordModelEnum.password_model_zero
          this.importOpenLearningParam.passwordModel = PasswordModelEnum.password_model_zero
        } else if (this.radio === PasswordModelEnum.password_model_abc) {
          this.importOpenLearningParam.password = PasswordModelEnum.password_model_abc
          this.importOpenLearningParam.passwordModel = PasswordModelEnum.password_model_abc
        } else if (this.radio === PasswordModelEnum.password_model_custom) {
          if (this.password) {
            this.importOpenLearningParam.password = this.password
            this.importOpenLearningParam.passwordModel = PasswordModelEnum.password_model_custom
          } else {
            return this.$message.warning('请填写自定义密码！')
          }
        } else {
          this.importOpenLearningParam.password = ''
          this.importOpenLearningParam.passwordModel = PasswordModelEnum.password_model_id_card
        }

        // 密码生效范围
        if (this.radio1 === 5) {
          this.importOpenLearningParam.passwordEffectiveRange = EffectiveRangeEnum.new_user
        } else {
          this.importOpenLearningParam.passwordEffectiveRange = EffectiveRangeEnum.all_user
        }
        // 注册学员更新基础信息
        if (this.radio2 === 7) {
          this.importOpenLearningParam.updateBasicInfo = true
        } else {
          this.importOpenLearningParam.updateBasicInfo = false
        }

        if (this.thematicManagementItem && this.thematicManagementItem.topicID) {
          this.importOpenLearningParam.specialId = this.thematicManagementItem.topicID

          this.importOpenLearningParam.importBySpecial = true
        }

        if (this.radioSpecial === 9) {
          this.importOpenLearningParam.importBySpecial = false
          this.importOpenLearningParam.specialId = null
        } else {
          this.importOpenLearningParam.importBySpecial = true
        }
        if (this.importOpenLearningParam.importBySpecial && !this.importOpenLearningParam.specialId) {
          this.$message.warning('请选择指定专题！')
          this.loading = false
          return
        }
        let res = await this.doImportOpen()
        if (res.status.code == 3003) {
          this.$message.warning('导入表与模板不匹配，无法导入，请检查选择的文件是否有误。')
        } else if (res.status?.isSuccess()) {
          //   this.$message.success('操作成功！')
          this.hbFileUploadResponse.fileName = ''
          this.hbFileUploadResponse.url = ''
          this.importSuccessVisible = true
          this.radio = null
          this.radio1 = null
          this.radio2 = null
          this.radioSpecial = null
        } else {
          this.$message.warning('操作失败！')
          // this.$message.warning((res as any)?.errors[0]?.message || '操作失败！')
        }
      } else {
        this.$message.warning('请先上传文件')
      }
      this.loading = false
    }

    // 网校管理员导入开班
    async doImportOpen() {
      this.importOpenLearningInServicer.params = this.importOpenLearningParam
      return await this.importOpenLearningInServicer.doImportOpen()
    }

    // 专题管理员导入开班
    async doImportOpenByThemeManager() {
      this.importOpenLearningInTrainingChannel.params = this.importOpenLearningParam
      return await this.importOpenLearningInTrainingChannel.doImportOpen()
    }

    // 显示培训班
    isTrainingSchemeDialog() {
      this.queryTrainingScheme.isShow = true
    }

    // 是否显示选择专题抽屉
    isSpecialSubjectDialog() {
      this.chooseSpecialSubject.isShow = true
    }

    /**
     * 选择专题
     */
    select(item: ThematicManagementItem) {
      this.thematicManagementItem = item
    }

    changeRadioSpecial(item: string) {
      //   如果选中不按照专题，指定专题导入需清空
      if (item == '5') {
        this.thematicManagementItem = null
        this.importOpenLearningParam.specialId = undefined
        this.importOpenLearningParam.importBySpecial = false
      }
    }

    @bind
    @debounce(1000)
    goImportDownloadPage() {
      this.importSuccessVisible = false
      // 导入开班并学习前往下载
      this.$router.push({ path: '/training/task/importtask', query: { type: '导入学员开班并学习' } })
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-card__body {
    padding: 0px !important;
  }
</style>
