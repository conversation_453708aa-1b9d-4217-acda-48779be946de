import RecordUserLearningMediaEventsTypeEnum from '@api/service/customer/learning/course/play-course/enums/RecordUserLearningMediaEventsTypeEnum'
import PlayCourseEventTypeEnum from '@api/service/customer/learning/course/play-course/enums/PlayCourseEventTypeEnum'
import ApplyCoursewarePlayResourceEventEnum from '@api/service/customer/learning/course/play-course/enums/ApplyCoursewarePlayResourceEventEnum'

export { ApplyCoursewarePlayResourceEventEnum, PlayCourseEventTypeEnum, RecordUserLearningMediaEventsTypeEnum }

enum LearningEventEnum {
  heatBeatError = 'heat-beat-error',
  sourceNotSupport = 'source-not-support'
}

export default LearningEventEnum
