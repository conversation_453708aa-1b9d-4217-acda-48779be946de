import * as htmlToImage from 'html-to-image'

interface Config {
  width?: number
  height?: number
}
export function domToPic(domId: string, config: Config = { width: 2105, height: 3035 }) {
  const node = document.getElementById(domId)
  htmlToImage
    .toPng(node)
    .then(pngUrl => {
      const img = new Image()
      img.src = pngUrl
      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        canvas.width = config.width
        canvas.height = config.height
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        canvas.toBlob((blob: Blob) => {
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = '预览海报.jpeg'
          document.body.appendChild(link)
          link.click()
          window.URL.revokeObjectURL(url)
        })
      }
    })
    .catch(function(error) {
      console.error('oops, something went wrong!', error)
    })
}
