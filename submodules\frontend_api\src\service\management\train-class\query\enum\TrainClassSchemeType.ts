import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 培训类型枚举
 * 1：选课规则
 * 2：自主选课
 */
export enum TrainClassSchemeEnum {
  Choose_Course_Learning = 1,
  Autonomous_Course_Learning = 2
}

class TrainClassSchemeType extends AbstractEnum<TrainClassSchemeEnum> {
  static enum = TrainClassSchemeEnum
  constructor(status?: TrainClassSchemeEnum) {
    super()
    this.current = status
    this.map.set(TrainClassSchemeEnum.Choose_Course_Learning, '选课规则')
    this.map.set(TrainClassSchemeEnum.Autonomous_Course_Learning, '自主选课')
  }
}

export default new TrainClassSchemeType()
