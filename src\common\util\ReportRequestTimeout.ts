import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import UserModule from '@api/service/management/user/UserModule'
import BaseEventTracking, { Env } from '@hbfe/base-event-tracking/src/index'
import Environment from '@api/service/common/utils/Env'
interface EventTrack {
  projectId: string
  pointId: string
  projectKey: string
  timeoutSecond: string
}
class ReportRequestTimeout {
  /**
   * 上报工具
   */
  private baseEventTracking = new BaseEventTracking()
  /***
   * 是否初始化完成
   */
  private initOver = false
  /**
   * 是否启用 字符串的布尔值
   */
  private webfunnySwitch = 'false'
  /**
   * webfunny体系下的项目ID
   */
  private projectId = ''
  /**
   * 业务系统的项目Key(当前项目名称)projectKey 业务系统的项目Key(当前项目名称)
   */
  private projectKey = ''
  /**
   * 定位ID
   */
  private pointId = 215
  /**
   * 上报慢请求时间
   */
  private timeoutSecond = 2000
  /**
   * 最初初始化
   */
  init() {
    this.webfunnySwitch = ConfigCenterModule.getFrontendApplication(frontendApplication.webfunnySwitch)

    if (this.webfunnySwitch === 'false') {
      console.log('不启用')

      return
    }

    const adminEventTrack = ConfigCenterModule.getFrontendApplication(frontendApplication.adminEventTrack)
    if (adminEventTrack) {
      const customerEventTrack = JSON.parse(adminEventTrack) as EventTrack
      this.projectId = customerEventTrack.projectId
      this.pointId = Number(customerEventTrack.pointId)
      this.projectKey = customerEventTrack.projectKey
      this.timeoutSecond = Number(customerEventTrack.timeoutSecond)
    }

    if (Environment.isProxyInnerNetworkEnv) {
      this.baseEventTracking.init(this.projectId, this.projectKey, 'tourist', Env.dev)
    } else {
      this.baseEventTracking.init(this.projectId, this.projectKey, 'tourist', Env.release)
    }
    this.initOver = true
  }
  /**
   * 登录后 重新初始化 只有在上报时去重新初始化
   */
  private login(userId: string) {
    if (Environment.isProxyInnerNetworkEnv) {
      this.baseEventTracking.init(this.projectId, this.projectKey, userId, Env.dev)
    } else {
      this.baseEventTracking.init(this.projectId, this.projectKey, userId, Env.release)
    }
  }
  /**
   * 上报
   */
  upSlowRQEvent(duration: number, url: string) {
    if (this.initOver) {
      if (this.webfunnySwitch === 'false') {
        console.log('不启用')
        return
      }
      const userModule = UserModule.queryUserFactory.queryManagerDetail
      if (userModule.adminInfo.userInfo?.userId) {
        this.login(userModule.adminInfo.userInfo?.userId)
      } else {
        this.login(`tourist`)
      }

      if (duration > this.timeoutSecond) {
        this.baseEventTracking.upSlowRQEvent(duration, url, this.pointId)

        console.log(`Request ${url} duration: ${duration} ms`)
      }
    }
  }
}
export default new ReportRequestTimeout()
