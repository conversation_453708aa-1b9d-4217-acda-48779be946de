import { Page, Response, ResponseStatus } from '@hbfe/common'
import QueryBatchRefundListParamVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchRefundListParamVo'
import {
  BatchReturnOrderSortRequest,
  ReturnSortRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import BatchRefoundListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchRefoundListDetailVo'
import UserModule from '@api/service/management/user/UserModule'
import CollectiveManagerInfoVo from '@api/service/management/user/query/manager/vo/CollectiveManagerInfoVo'

export default abstract class QueryBatchRefundBase {
  /**
   * 【集体报名退款】查询列表
   * @param {Page} page - 分页参数
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<BatchRefoundListDetailVo[]>} - 【集体报名订单】列表
   */
  abstract queryBatchRefoundList(
    page: Page,
    queryParams: QueryBatchRefundListParamVo,
    sortRequest?: Array<BatchReturnOrderSortRequest>
  ): Promise<BatchRefoundListDetailVo[]>

  /**
   * 【集体报名退款订单】查询列表统计数据 statisticBatchReturnOrderInServicer
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<ResponseStatus>}
   */
  abstract queryBatchRefoundListStatistic(queryParams: QueryBatchRefundListParamVo): Promise<ResponseStatus>

  /**
   * 【集体报名退款】导出批次退货单
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<BatchRefoundListDetailVo[]>} - 【集体报名订单】列表
   */
  abstract exportBatchReturnOrderExcelInServicer(
    queryParams: QueryBatchRefundListParamVo,
    sortRequest?: Array<ReturnSortRequest>
  ): Promise<Response<boolean>>

  /**
   * 【集体报名退款】导出批次退货单明细
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<BatchRefoundListDetailVo[]>} - 【集体报名订单】列表
   */
  abstract exportBatchReturnOrderDetailExcelInServicer(
    queryParams: QueryBatchRefundListParamVo,
    sortRequest?: Array<ReturnSortRequest>
  ): Promise<Response<boolean>>

  /**
   * 获取用户信息
   * @param ids id数组
   */
  protected async getUserInfo(ids: Array<string>) {
    // 根据用户ID获取用户信息
    const response = await UserModule.queryUserFactory.queryCollectiveManagerList.queryCollectiveManagerInfoList(ids)
    const userIdMap: Map<string, CollectiveManagerInfoVo> = new Map()
    response.forEach(item => {
      userIdMap.set(item.userId, item)
    })
    return userIdMap
  }
}
