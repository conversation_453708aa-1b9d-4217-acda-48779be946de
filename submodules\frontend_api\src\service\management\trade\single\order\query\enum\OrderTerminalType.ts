import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * @description 终端类型
 */
export enum OrderTerminal {
  Web = 1,
  IOS = 2,
  Android = 3,
  WechatMini = 4,
  WechatOfficial = 5,
  ExternalSystemManage = 6,
  H5 = 7
}

class OrderTerminalType extends AbstractEnum<OrderTerminal> {
  static enum = OrderTerminal

  constructor(status?: OrderTerminal) {
    super()
    this.current = status
    this.map.set(OrderTerminal.Web, 'Web端')
    this.map.set(OrderTerminal.IOS, 'IOS端')
    this.map.set(OrderTerminal.Android, '安卓端')
    this.map.set(OrderTerminal.WechatMini, '微信小程序')
    this.map.set(OrderTerminal.WechatOfficial, '微信公众号')
    this.map.set(OrderTerminal.ExternalSystemManage, '外部管理系统')
    this.map.set(OrderTerminal.H5, 'H5')
  }
}

export default new OrderTerminalType()
