import { InvoiceServiceAuthCreateRequest } from '@api/ms-gateway/ms-trade-configuration-v1'
import { InvoiceProviderEnum } from '@api/service/common/enums/online-school-config/InvoiceProviderTypes'

/*
 * 平台授权信息
 */
class ServiceAuthVo extends InvoiceServiceAuthCreateRequest {
  /**
   * 开票提供商编号
5 - 诺诺，当前只支持诺诺配置
   */
  invoiceProviderId: string = InvoiceProviderEnum.NUONUO
  /**
   * 授权码
   */
  authCode?: string = ''
  /**
   * 部门ID
   */
  deptId?: string = ''
}

export default ServiceAuthVo
