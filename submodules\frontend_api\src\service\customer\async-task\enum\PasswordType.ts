import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 密码类型枚举
 */
export enum PasswordTypeEnum {
  // 1、默认密码
  Default,
  // 2、abc123
  Abc123,
  // 3、自定义密码
  Custom
}

/**
 * @description 密码类型
 */
class PasswordType extends AbstractEnum<PasswordTypeEnum> {
  static enum = PasswordTypeEnum
  constructor(status?: PasswordTypeEnum) {
    super()
    this.current = status
    this.map.set(PasswordTypeEnum.Default, '默认密码')
    this.map.set(PasswordTypeEnum.Abc123, 'abc123')
    this.map.set(PasswordTypeEnum.Custom, '自定义密码')
  }
}

export default PasswordType
