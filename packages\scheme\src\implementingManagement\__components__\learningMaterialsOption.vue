<template>
  <el-card shadow="never" class="m-card f-mb15" id="section3">
    <div slot="header">
      <span class="tit-txt">学习资料设置</span>
    </div>
    <template>
      <!--      <div class="m-sub-tit">-->
      <!--        <span class="tit-txt">班级内所有面授期别统一设置</span>-->
      <!--      </div>-->
      <!--      <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">-->
      <!--        针对班级内所有面授期别统一设置学习资料。-->
      <!--      </el-alert>-->
      <!--      <el-row type="flex" justify="center" class="width-limit">-->
      <!--        <el-col :md="20" :lg="16" :xl="13">-->
      <!--          <upload-material-list :table-data="learningMaterialsConfig.allAnnexInfoConfig"></upload-material-list>-->
      <!--        </el-col>-->
      <!--      </el-row>-->
      <div class="m-sub-tit">
        <span class="tit-txt">指定面授期别学习资料设置</span>
      </div>
      <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
        针对指定期别上传学习资料。设置完成后报名期别的学员可下载对应的学习资料。
      </el-alert>

      <el-table
        stripe
        :data="learningMaterialsConfig.periodInfoList"
        v-loading="uiLoading.periodLoading"
        max-height="500px"
        class="m-table f-mt15"
      >
        <el-table-column label="期别编号" min-width="100" fixed="left" prop="no"></el-table-column>
        <el-table-column label="期别名称" width="200">
          <template v-slot="{ row }">
            <div class="flex">
              <el-tooltip effect="dark" :content="row.name" placement="top">
                <div class="text-ellipsis">{{ row.name }}</div>
              </el-tooltip>
              <el-tag v-if="!row.annexSetted" type="danger" size="mini" class="f-ml5" style="min-width: 46px"
                >未上传
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="培训报到时段" min-width="200">
          <template v-slot="{ row }">
            <p>
              <el-tag type="info" size="mini">起始</el-tag>
              {{ row.checkInTime.begin || '-' }}
            </p>
            <p>
              <el-tag type="info" size="mini">结束</el-tag>
              {{ row.checkInTime.end || '-' }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="培训时段" min-width="200">
          <template v-slot="{ row }">
            <p>
              <el-tag type="info" size="mini">起始</el-tag>
              {{ row.trainingTime.begin || '-' }}
            </p>
            <p>
              <el-tag type="info" size="mini">结束</el-tag>
              {{ row.trainingTime.end || '-' }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="资料数量" width="200" align="center" prop="annexNum"></el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template v-slot="{ row }">
            <el-button type="text" @click="learningSetDialog(row)">上传资料</el-button>
          </template>
        </el-table-column>
      </el-table>
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
      <!--通用空数据-->
      <!--      <div class="m-btn-bar f-tc f-mt30">-->
      <!--        <el-button>取消</el-button>-->
      <!--        <el-button type="primary" :loading="uiLoading.saveLoading" @click="saveLearningMaterialsConfig">保存</el-button>-->
      <!--      </div>-->
    </template>
    <stage-learning-material-dialog
      :period-config="periodConfig"
      @refresh="doRefresh"
      ref="stageLearningMaterialDialogRef"
    ></stage-learning-material-dialog>
  </el-card>
</template>

<script lang="ts">
  import { Component, Inject, Prop, Ref, Vue } from 'vue-property-decorator'
  import StageLearningMaterialDialog from '@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/stageLearningMaterialDialog.vue'
  import LearningMaterialsConfig from '@api/service/management/implement/LearningMaterialsConfig'
  import PeriodConfig from '@api/service/management/implement/models/PeriodConfig'
  import { ElTable } from 'element-ui/types/table'
  import { UiPage } from '@hbfe/common'
  import UploadMaterialList from '@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/upload-material-list.vue'
  import ImplementConfig from '@api/service/management/implement/ImplementConfig'
  import SchemeStepProcess from '@api/service/management/train-class/Utils/SchemeStepProcess'
  import { SchemeProcessStatusEnum } from '@api/service/management/train-class/query/enum/SchemeProcessStatusEnum'

  @Component({
    components: {
      StageLearningMaterialDialog,
      UploadMaterialList
    }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.pageSearch, this.pageSearch)
    }

    @Ref('stageLearningMaterialDialogRef') stageLearningMaterialDialogRef: StageLearningMaterialDialog
    @Ref('materialTable') materialTable: ElTable
    /**
     * 接收训前考勤设置
     */
    @Prop({
      required: true
    })
    implementConfig: ImplementConfig

    @Inject('SchemeStepProcess')
    SchemeStepProcess: SchemeStepProcess

    learningMaterialsConfig: LearningMaterialsConfig = null

    periodConfig: PeriodConfig = new PeriodConfig()

    uiLoading = {
      materialLoading: false,
      periodLoading: false,
      saveLoading: false
    }
    /**
     * 期别分页
     */
    page: UiPage

    async pageSearch() {
      await this.learningMaterialsConfig.pagePeriodLearningMaterialsConfig(this.page)
    }

    /**
     * 资料设置查询
     */
    async doMaterialQuery() {
      this.uiLoading.materialLoading = true
      await this.learningMaterialsConfig.getLearningMaterialsConfig()
      this.uiLoading.materialLoading = false
    }

    /**
     * 期别配置查询
     */
    async doPeriodQuery() {
      this.uiLoading.periodLoading = true
      this.page.pageNo = 1
      await this.pageSearch()
      this.uiLoading.periodLoading = false
    }

    /**
     * 期别配置列表刷新
     */
    async doRefresh() {
      const schemeId = this.$route.params.schemeId
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        const res = await this.SchemeStepProcess.getSchemeTaskStatus(schemeId)
        if (res.code === SchemeProcessStatusEnum.finish) {
          this.uiLoading.periodLoading = true
          this.page.pageNo = 1
          await this.pageSearch()
          this.$message.success('添加学习资料成功')
          this.uiLoading.periodLoading = false
        } else {
          this.$message.error(res.message as string)
        }
      } catch (e) {
        console.error(e)
        this.$message.error(e.message || '添加学习资料失败。')
      } finally {
        loading.close()
      }
    }

    /**
     * 学习资料设置
     */
    learningSetDialog(row: PeriodConfig) {
      console.log(row)
      this.periodConfig = row
      this.stageLearningMaterialDialogRef.openDialog = true
    }

    created() {
      this.learningMaterialsConfig = this.implementConfig.learningMaterialsConfig
      // this.doMaterialQuery()
      this.doPeriodQuery()
    }
  }
</script>
<style lang="scss" scoped>
  .flex {
    display: flex;
    align-items: center;

    .text-ellipsis {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
</style>
