import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/QuestionCorrection-default'

// 枚举
export enum ScaleType {
  CUSTOM = 'CUSTOM',
  SATISFACTION = 'SATISFACTION',
  RECOGNITION = 'RECOGNITION',
  IMPORTANCE = 'IMPORTANCE',
  WILLING = 'WILLING',
  CONFORMITY = 'CONFORMITY'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 附件请求
<AUTHOR> create 2020/5/21 19:26
 */
export class AttachmentRequest {
  /**
   * 附件名称
   */
  name?: string
  /**
   * 文件后缀名
   */
  suffix?: string
  /**
   * 文件资源路径
   */
  resPath?: string
}

/**
 * 创建试题纠错
<AUTHOR> create 2020/5/21 16:05
 */
export class QuestionCorrectionCreateRequest {
  /**
   * 试题id
   */
  questionId?: string
  /**
   * 纠错分类id
   */
  categoryId?: string
  /**
   * 描述
   */
  describe?: string
  /**
   * 附件
   */
  attachments?: Array<AttachmentRequest>
}

/**
 * 试题纠错查询请求
<AUTHOR> create 2020/5/22 8:36
 */
export class QuestionCorrectionQueryRequest {
  /**
   * 试题题目
   */
  title?: string
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 分类id
   */
  categoryId?: string
  /**
   * 创建时间起
   */
  createTimeBegin?: string
  /**
   * 创建时间止
   */
  createTimeEnd?: string
}

/**
 * 标记试题纠错
<AUTHOR> create 2020/5/21 19:45
 */
export class SignQuestionCorrectionRequest {
  /**
   * 试题纠错id
   */
  questionCorrectionId?: string
  /**
   * 标记内容
   */
  content?: string
  /**
   * 标记试题纠错状态
@see QuestionCorrectionStatus
   */
  singStatus: number
}

/**
 * <AUTHOR> create 2020/5/21 19:23
 */
export class AttachmentResponse {
  /**
   * 附件名称
   */
  name: string
  /**
   * 文件后缀名
   */
  suffix: string
  /**
   * 文件资源路径
   */
  resPath: string
}

/**
 * <AUTHOR> create 2020/5/21 19:04
 */
export class OperatorResponse {
  /**
   * 操作人信息
   */
  id: string
  /**
   * 操作人昵称
   */
  nickName: string
}

/**
 * 试题纠错分类
<AUTHOR> create 2020/5/22 16:08
 */
export class QuestionCorrectionCategoryResponse {
  /**
   * 问题分类id
   */
  id: string
  /**
   * 父级分类id
   */
  parentId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人信息
   */
  creatorId: string
}

/**
 * <AUTHOR> create 2020/5/21 19:50
 */
export class QuestionCorrectionPageItemResponse {
  /**
   * id
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题id
   */
  questionId: string
  /**
   * 纠错分类id
   */
  categoryId: string
  /**
   * 描述
   */
  describe: string
  /**
   * 附件
   */
  attachments: Array<AttachmentResponse>
  /**
   * 试题纠错状态
@see QuestionCorrectionStatus
   */
  status: number
  /**
   * 标记记录集合
   */
  signList: Array<SignItemResponse>
  question: QuestionResponse
}

/**
 * <AUTHOR> create 2020/5/21 19:22
 */
export class QuestionCorrectionResponse {
  /**
   * id
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 创建人id
   */
  creatorId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题id
   */
  questionId: string
  /**
   * 纠错分类id
   */
  categoryId: string
  /**
   * 描述
   */
  describe: string
  /**
   * 附件
   */
  attachments: Array<AttachmentResponse>
  /**
   * 试题纠错状态
@see QuestionCorrectionStatus
   */
  status: number
  /**
   * 标记记录集合
   */
  signList: Array<SignResponse>
}

/**
 * 试题纠错结果
<AUTHOR> create 2020/5/23 10:23
 */
export class QuestionCorrectionResultResponse {
  /**
   * 试题id
   */
  questionId: string
  /**
   * 试题纠错状态
@see QuestionCorrectionStatus
   */
  status: number
}

/**
 * 试题信息
<AUTHOR> create 2019/12/23 15:56
 */
export class QuestionResponse {
  /**
   * 试题id
   */
  id: string
  /**
   * 试题应用类型
@see QuestionApplyType
   */
  applyTypes: Array<number>
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位ID
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 题库ID
   */
  libraryId: string
  /**
   * 题目
   */
  title: string
  /**
   * 判断题
   */
  judgement: JudgementResponse
  /**
   * 单选题
   */
  singleChoice: SingleChoiceResponse
  /**
   * 多选
   */
  multipleChoice: MultipleChoiceResponse
  /**
   * 填空
   */
  blankFilling: BlankFillingResponse
  /**
   * 问答题
   */
  essay: EssayResponse
  /**
   * 量表题
   */
  scale: ScaleResponse
  /**
   * 综合题
   */
  comprehensive: ComprehensiveResponse
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 试题难度
@see QuestionMode
   */
  mode: number
  /**
   * 难度值
   */
  difficulty: number
  /**
   * 试题解析
   */
  description: string
  /**
   * 创建人id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 最后修改时间
   */
  lastChangeTime: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 资源记录(数据)的授权源id
a 授权 b, b 授权 c, c的sourceId是b, c的rootId是a
   */
  rootId: string
  /**
   * 数据授权的Token, 并不需要默认值
   */
  token: string
}

/**
 * <AUTHOR> create 2020/5/21 19:51
 */
export class SignItemResponse {
  /**
   * 标记人id
   */
  signerId: string
  /**
   * 标记人信息
   */
  signer: OperatorResponse
  /**
   * 标记时间
   */
  signTime: string
  /**
   * 标记内容
   */
  content: string
  /**
   * 标记试题纠错状态
@see QuestionCorrectionStatus
   */
  singStatus: number
}

/**
 * <AUTHOR> create 2020/5/21 19:23
 */
export class SignResponse {
  /**
   * 标记人id
   */
  signerId: string
  /**
   * 标记时间
   */
  signTime: string
  /**
   * 标记内容
   */
  content: string
  /**
   * 标记试题纠错状态
@see QuestionCorrectionStatus
   */
  singStatus: number
}

/**
 * <AUTHOR> create 2019/12/11 10:54
 */
export class BlankFillingResponse {
  /**
   * 答案数量
   */
  answerCount: number
  /**
   * 当填空题类型为 精确匹配时，最外层集合为答案组（也就是每一组都是一道填空题的答案，满足当中的任意一组表示回答正确）第二层集合为空
当填空题类似为 每空多答案时，最外层的集合为每个空的答案，第二层集合为每个空的备选答案
   */
  answersGroup: Array<string>
  /**
   * 答案项分值
当填空题类型为 精确匹配时此项值无效
   */
  answersItemScore: Array<number>
  /**
   * 答案类型
@see BlankFillingAnswerType
   */
  answerType: number
  /**
   * 答案是否有顺序.当{@link #answerType } &#x3D; {@link BlankFillingAnswerType#MULTIPLE_PER_BLANK} 时，
即每空多答案的情况下，答案是否是按照填空顺序排列。
   */
  sequence: boolean
  /**
   * 评分标准
   */
  standard: string
}

/**
 * 选项
<AUTHOR> create 2019/12/23 16:05
 */
export class ChoiceItemResponse {
  /**
   * 选项ID
   */
  id: string
  /**
   * 选项内容
   */
  content: string
}

/**
 * 综合题子题
<AUTHOR> create 2019/12/11 13:36
 */
export class ComprehensiveChildQuestionResponse {
  /**
   * 子试题id
   */
  questionId: string
  /**
   * 题目
   */
  title: string
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 判断题
   */
  judgement: JudgementResponse
  /**
   * 单选题
   */
  singleChoice: SingleChoiceResponse
  /**
   * 多选
   */
  multipleChoice: MultipleChoiceResponse
  /**
   * 填空
   */
  blankFilling: BlankFillingResponse
  /**
   * 问答题
   */
  essay: EssayResponse
  /**
   * 量表题
   */
  scale: ScaleResponse
  /**
   * 试题难度
@see QuestionMode
   */
  mode: number
  /**
   * 难度值
   */
  difficultyValue: number
  /**
   * 试题解析
   */
  description: string
}

/**
 * <AUTHOR> create 2019/12/11 13:54
 */
export class ComprehensiveResponse {
  /**
   * 子题
   */
  children: Array<ComprehensiveChildQuestionResponse>
}

/**
 * 问答题
<AUTHOR> create 2019/12/11 11:42
 */
export class EssayResponse {
  /**
   * 参考答案
   */
  referenceAnswer: string
  /**
   * 评分标准
   */
  standard: string
  /**
   * 是否限制作答长度
   */
  limitAnswerLength: boolean
  /**
   * 允许作答的文本字符最少长度
   */
  permitAnswerLengthMin: number
  /**
   * 允许作答的文本字符最大长度
   */
  permitAnswerLengthMax: number
}

/**
 * 判断题试题内容dto
<AUTHOR> create 2019/12/23 16:03
 */
export class JudgementResponse {
  /**
   * 正确答案
   */
  correctAnswer: boolean
  /**
   * 正确文本
   */
  correctText: string
  /**
   * 错误文本
   */
  incorrectText: string
}

/**
 * <AUTHOR> create 2019/12/23 16:09
 */
export class MultipleChoiceResponse {
  /**
   * 选项
   */
  choiceItems: Array<ChoiceItemResponse>
  /**
   * 正确答案
   */
  correctAnswers: Array<string>
}

/**
 * 量表题
<AUTHOR> create 2019/12/11 14:04
 */
export class ScaleResponse {
  /**
   * 量表类型
   */
  scaleType: ScaleType
  /**
   * 程度_始，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写
   */
  startDegree: string
  /**
   * 程度_止，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写
   */
  endDegree: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
}

/**
 * 单选题dto
<AUTHOR> create 2019/12/23 16:03
 */
export class SingleChoiceResponse {
  /**
   * 选项
   */
  choiceItems: Array<ChoiceItemResponse>
  /**
   * 标准答案
   */
  correctAnswer: string
}

export class QuestionCorrectionPageItemResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<QuestionCorrectionPageItemResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取试题纠错分页信息
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findMyQuestionCorrectionResult(
    query: DocumentNode = GraphqlImporter.findMyQuestionCorrectionResult,
    operation?: string
  ): Promise<Response<Array<QuestionCorrectionResultResponse>>> {
    return commonRequestApi<Array<QuestionCorrectionResultResponse>>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 获取试题纠错完成的试题结果
   * @return
   * @param query 查询 graphql 语法文档
   * @param questionIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findQuestionCorrectionDone(
    questionIds: Array<string>,
    query: DocumentNode = GraphqlImporter.findQuestionCorrectionDone,
    operation?: string
  ): Promise<Response<Array<QuestionCorrectionResultResponse>>> {
    return commonRequestApi<Array<QuestionCorrectionResultResponse>>(SERVER_URL, {
      query: query,
      variables: { questionIds },
      operation: operation
    })
  }

  /**   * 获取试题纠错分页信息
   * @param page
   * @param query
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findQuestionCorrectionPage(
    params: { page?: Page; query?: QuestionCorrectionQueryRequest },
    query: DocumentNode = GraphqlImporter.findQuestionCorrectionPage,
    operation?: string
  ): Promise<Response<QuestionCorrectionPageItemResponsePage>> {
    return commonRequestApi<QuestionCorrectionPageItemResponsePage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取所有试题纠错分类
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listAllQuestionCorrectionCategory(
    query: DocumentNode = GraphqlImporter.listAllQuestionCorrectionCategory,
    operation?: string
  ): Promise<Response<Array<QuestionCorrectionCategoryResponse>>> {
    return commonRequestApi<Array<QuestionCorrectionCategoryResponse>>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 创建试题纠错信息
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createQuestionCorrection(
    request: QuestionCorrectionCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createQuestionCorrection,
    operation?: string
  ): Promise<Response<QuestionCorrectionResponse>> {
    return commonRequestApi<QuestionCorrectionResponse>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }

  /**   * 标记试题纠错信息
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async signQuestionCorrection(
    request: SignQuestionCorrectionRequest,
    mutate: DocumentNode = GraphqlImporter.signQuestionCorrection,
    operation?: string
  ): Promise<Response<QuestionCorrectionResponse>> {
    return commonRequestApi<QuestionCorrectionResponse>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }
}

export default new DataGateway()
