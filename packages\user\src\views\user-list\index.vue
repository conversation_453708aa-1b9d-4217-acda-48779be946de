<template>
  <div class="user-list-page">
    <!-- 搜索区域 -->
    <el-card shadow="never" class="m-card f-mb15">
      <hb-search-wrapper @reset="restQuery" class="m-query is-border-bottom">
        <el-form-item label="用户名">
          <el-input v-model="searchForm.userName" placeholder="请输入用户名" clearable></el-input>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable></el-input>
        </el-form-item>
        <el-form-item label="身份证号">
          <el-input v-model="searchForm.idCard" placeholder="请输入身份证号" clearable></el-input>
        </el-form-item>
        <el-form-item label="注册时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <template slot="actions">
          <el-button type="primary" @click="handleSearch" :loading="loading">搜索</el-button>
          <el-button v-if="$hasPermission('user.export')" @click="handleExport">导出</el-button>
        </template>
      </hb-search-wrapper>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <div slot="header" class="table-header">
        <span>用户列表</span>
        <div class="header-actions">
          <el-button v-if="$hasPermission('user.create')" type="primary" @click="handleCreate"> 新增用户 </el-button>
        </div>
      </div>

      <el-table
        :data="userList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="userName" label="用户名" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.userName }}
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" width="130"></el-table-column>
        <el-table-column prop="idCard" label="身份证号" width="180">
          <template slot-scope="scope">
            {{ scope.row.idCard | hideIdCard }}
          </template>
        </el-table-column>
        <el-table-column prop="gender" label="性别" width="80">
          <template slot-scope="scope">
            {{ scope.row.gender ? '男' : '女' }}
          </template>
        </el-table-column>
        <el-table-column prop="companyName" label="工作单位" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="注册时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="bindWX" label="微信绑定" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.bindWX ? 'success' : 'info'" size="small">
              {{ scope.row.bindWX ? '已绑定' : '未绑定' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click="handleView(scope.row)">查看</el-button>
            <el-button v-if="$hasPermission('user.edit')" type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button
              v-if="$hasPermission('user.delete')"
              type="text"
              class="danger-text"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        ></el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import moment from 'moment'
  import UserModule from '@api/service/management/user/UserModule'

  import StudentUserInfoVo from '@api/service/management/user/query/student/vo/StudentUserInfoVo'
  import StudentQueryVo from '@api/service/management/user/query/student/vo/StudentQueryVo'
  import { Page } from '@hbfe/common'

  // 搜索表单接口
  interface SearchForm {
    userName: string
    phone: string
    idCard: string
    dateRange: string[]
  }

  // 分页接口
  interface Pagination {
    currentPage: number
    pageSize: number
    total: number
  }

  @Component({
    filters: {
      // 隐藏身份证中间部分
      hideIdCard(idCard: string) {
        if (!idCard) return ''
        return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
      }
    }
  })
  export default class UserListPage extends Vue {
    // 数据属性
    private userList: StudentUserInfoVo[] = []
    private loading = false
    private selectedUsers: StudentUserInfoVo[] = []

    // 搜索表单
    private searchForm: SearchForm = {
      userName: '',
      phone: '',
      idCard: '',
      dateRange: []
    }

    // API查询对象
    private queryStudentList = UserModule.queryUserFactory.queryStudentList

    // 分页信息
    private pagination: Pagination = {
      currentPage: 1,
      pageSize: 20,
      total: 0
    }

    // 生命周期
    async mounted() {
      await this.loadUserList()
    }

    // 方法
    private async loadUserList() {
      this.loading = true
      try {
        // 构建查询参数
        const queryParams = new StudentQueryVo()
        queryParams.userName = this.searchForm.userName
        queryParams.phone = this.searchForm.phone
        queryParams.idCard = this.searchForm.idCard

        // 处理日期范围
        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
          queryParams.beginTime = this.searchForm.dateRange[0] + ' 00:00:00'
          queryParams.endTime = this.searchForm.dateRange[1] + ' 23:59:59'
        }

        // 构建分页参数
        const page = new Page()
        page.pageNo = this.pagination.currentPage
        page.pageSize = this.pagination.pageSize

        const response = await this.queryStudentList.queryStudentListByCondition(page, queryParams)

        if (response.status.isSuccess()) {
          this.userList = response.data || []
          this.pagination.total = page.totalSize || 0
        } else {
          this.$message.error(`查询失败: ${response.status.message}`)
          // 如果API失败，使用模拟数据作为后备
          await this.mockLoadUserList()
        }
      } catch (error) {
        console.error('加载用户列表失败:', error)
        this.$message.error('网络错误，使用模拟数据')
        // 网络错误时使用模拟数据
        await this.mockLoadUserList()
      } finally {
        this.loading = false
      }
    }

    // 模拟加载用户列表
    private async mockLoadUserList() {
      return new Promise((resolve) => {
        setTimeout(() => {
          // 模拟用户数据
          const mockUsers = Array.from({ length: this.pagination.pageSize }, (_, index) => {
            const user = new StudentUserInfoVo()
            const id = (this.pagination.currentPage - 1) * this.pagination.pageSize + index + 1
            user.userId = `user_${id}`
            user.userName = `用户${id}`
            user.loginAccount = `user${id}`
            user.phone = `138${String(id).padStart(8, '0')}`
            user.idCard = `${String(id).padStart(6, '0')}********${String(id).padStart(4, '0')}`
            user.genderName = index % 2 === 0 ? '男' : '女'
            user.companyName = `测试公司${id}`
            user.createTime = moment().subtract(id, 'days').format('YYYY-MM-DD HH:mm:ss')
            user.bindWX = index % 3 === 0
            return user
          })

          this.userList = mockUsers
          this.pagination.total = 156 // 模拟总数
          resolve(true)
        }, 800)
      })
    }

    // 重置
    restQuery() {
      this.searchForm = {
        userName: '',
        phone: '',
        idCard: '',
        dateRange: []
      }
      this.pagination.currentPage = 1
      this.loadUserList()
    }

    // 搜索
    private async handleSearch() {
      this.pagination.currentPage = 1
      await this.loadUserList()
    }

    // 重置搜索
    private handleReset() {
      this.searchForm = {
        userName: '',
        phone: '',
        idCard: '',
        dateRange: []
      }
      this.pagination.currentPage = 1
      this.loadUserList()
    }

    // 导出
    private handleExport() {
      this.$message.info('导出功能开发中...')
    }

    // 新增用户
    private handleCreate() {
      this.$message.info('新增用户功能开发中...')
    }

    // 查看用户详情
    private handleView(user: StudentUserInfoVo) {
      this.$router.push(`/training/user/info/${user.userId}`)
    }

    // 编辑用户
    private handleEdit(user: StudentUserInfoVo) {
      this.$message.info(`编辑用户: ${user.userName}`)
    }

    // 删除用户
    private async handleDelete(user: StudentUserInfoVo) {
      try {
        await this.$confirm(`确定要删除用户 "${user.userName}" 吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // TODO: 调用删除API 暂时没见
        this.$message.success('删除成功')
        await this.loadUserList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    }

    // 选择变化
    private handleSelectionChange(selection: StudentUserInfoVo[]) {
      this.selectedUsers = selection
    }

    // 分页大小变化
    private handleSizeChange(size: number) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadUserList()
    }

    // 当前页变化
    private handleCurrentChange(page: number) {
      this.pagination.currentPage = page
      this.loadUserList()
    }

    // 格式化日期
    private formatDate(dateString: string) {
      if (!dateString) return ''
      return moment(dateString).format('YYYY-MM-DD HH:mm')
    }
  }
</script>

<style scoped>
  .user-list-page {
    padding: 20px;
  }

  .search-card {
    margin-bottom: 20px;
  }

  .search-form {
    margin-bottom: 0;
  }

  .table-card {
    min-height: 600px;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .user-info {
    display: flex;
    align-items: center;
  }

  .user-details {
    margin-left: 10px;
  }

  .user-name {
    font-weight: bold;
    color: #303133;
  }

  .user-account {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }

  .danger-text {
    color: #f56c6c;
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }
</style>
