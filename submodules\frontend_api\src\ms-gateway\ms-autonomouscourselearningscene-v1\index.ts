import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-autonomouscourselearningscene-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-autonomouscourselearningscene-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum LifeCycle {
  create = 'create',
  update = 'update',
  delete = 'delete'
}

// 类

export class LSContext {
  lifeCycle?: LifeCycle
}

/**
 * 自主学习场景提交删除命令
<AUTHOR>
@since 2023/7/3
 */
export class AutonomousCourseLearningSceneCommitRemoveCommand {
  /**
   * 自主学习场景，配置json体
@see com.fjhb.ms.clscene.autonomous.v1.api.config.AutonomousCourseLearningSceneConfig
   */
  config: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商ID
   */
  servicerId: string
  /**
   * 删除人id
   */
  deleteUserId: string
  /**
   * 删除时间
   */
  deleteTime: string
  lsContext?: LSContext
  currentTxId?: string
}

/**
 * 自主学习场景提交更新命令
<AUTHOR>
@since 2023/7/3
 */
export class AutonomousCourseLearningSceneCommitUpdateCommand {
  /**
   * 对应资源的学习方案配置json体
@see com.fjhb.ms.clscene.autonomous.v1.api.config.AutonomousCourseLearningSceneConfig
   */
  config: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商ID
   */
  servicerId: string
  /**
   * 更新人id
   */
  updateUserId: string
  /**
   * 更新时间
   */
  updateTime: string
  lsContext?: LSContext
  currentTxId?: string
}

/**
 * 自主学习场景预创建命令
<AUTHOR>
@since 2023/7/3
 */
export class AutonomousCourseLearningScenePreCreateCommand {
  /**
   * 自主学习场景，配置json体
@see com.fjhb.ms.clscene.autonomous.v1.api.config.AutonomousCourseLearningSceneConfig
   */
  config: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商ID
   */
  servicerId: string
  /**
   * 创建人id
   */
  createUserId: string
  /**
   * 预创建时间
   */
  preCreateTime: string
  lsContext?: LSContext
  currentTxId?: string
}

/**
 * 自主学习场景预删除命令
<AUTHOR>
@since 2023/7/3
 */
export class AutonomousCourseLearningScenePreRemoveCommand {
  /**
   * 自主学习场景，配置json体
@see com.fjhb.ms.clscene.autonomous.v1.api.config.AutonomousCourseLearningSceneConfig
   */
  config: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商ID
   */
  servicerId: string
  /**
   * 删除人id
   */
  deleteUserId: string
  /**
   * 预删除时间
   */
  preDeleteTime: string
  lsContext?: LSContext
  currentTxId?: string
}

/**
 * 自主学习场景预更新命令
<AUTHOR>
@since 2023/7/3
 */
export class AutonomousCourseLearningScenePreUpdateCommand {
  /**
   * 对应资源的学习方案配置json体
@see com.fjhb.ms.clscene.autonomous.v1.api.config.AutonomousCourseLearningSceneConfig
   */
  config: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商ID
   */
  servicerId: string
  /**
   * 更新人id
   */
  updateUserId: string
  /**
   * 预更新时间
   */
  preUpdateTime: string
  lsContext?: LSContext
  currentTxId?: string
}

export class NeedRecalculateProcessResult {
  learningId: string
  learningResourceId: string
  recalculateRequests: Array<RecalculateRequest>
  code: string
  message: string
  configJson: string
  resourceId: string
  asyncExecute: boolean
  resultType: number
}

export class RecalculateRequest {
  dataType: string
  priority: number
  recalculate: boolean
  updatedCode: string
  metadata: Map<string, string>
}

/**
 * 课程一键学习凭证响应
<AUTHOR>
@since 2022/1/20
 */
export class CourseImdlyLearningTokenResponse {
  /**
   * 申请结果
200-成功
500-内部异常
501001-学员课程不存在
501002-学员课程已失效
501003-课后测验分数不可为空
   */
  applyResult: TokenResponse
  /**
   * 课程学习凭证
   */
  token: string
}

/**
 * 课件一键学习凭证响应
<AUTHOR>
@since 2022/1/20
 */
export class CoursewareImdlyLearningTokenResponse {
  /**
   * 申请结果
200-成功
500-内部异常
501001-学员课程不存在
501002-学员课程已失效
   */
  applyResult: TokenResponse
  /**
   * 课程学习凭证
   */
  token: string
}

/**
 * 课程学习凭证响应
<AUTHOR>
@since 2022/1/20
 */
export class StudentCourseLearningTokenResponse {
  /**
   * 申请结果
   */
  applyResult: TokenResponse
  /**
   * 课程学习凭证
   */
  token: string
  /**
   * applyResult.code&#x3D;&#x3D;L90001 有这个值
已学习(时长/课时)
   */
  timeLength: number
  /**
   * applyResult.code&#x3D;&#x3D;L90001 有这个值
学习规则类型1&#x3D;时长(秒) 2&#x3D;课时
   */
  ruleType: number
}

/**
 * 凭证响应基类
<AUTHOR>
@since 2022/1/20
 */
export class TokenResponse {
  /**
   * 代码：
200-成功
   */
  code: string
  /**
   * 信息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请课程一键学习token
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCourseImmediatelyLearning(
    params: { studentLearningToken: string; studentCourseId: string; quizScore?: number },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCourseImmediatelyLearning,
    operation?: string
  ): Promise<Response<CourseImdlyLearningTokenResponse>> {
    return commonRequestApi<CourseImdlyLearningTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请课程学习
   * 200-成功
   * 60001-学习token解析异常
   * 60002-学习场景不存在
   * 60003-该大纲下课程不存在
   * 60004-选课失败
   * 60005-必修课暂未完全合格，无法学习选修
   * 60006-查询必修课学习情况异常
   * @param studentLearningToken 学员学习凭证
   * @param outlineId            所属课程学习大纲编号
   * @param courseId             课程编号
   * @return 课程学习凭证
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCourseLearning(
    params: { studentLearningToken: string; outlineId: string; courseId: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCourseLearning,
    operation?: string
  ): Promise<Response<StudentCourseLearningTokenResponse>> {
    return commonRequestApi<StudentCourseLearningTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请课件一键学习token
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCoursewareImmediatelyLearning(
    params: { studentLearningToken: string; studentCourseId: string; coursewareId: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCoursewareImmediatelyLearning,
    operation?: string
  ): Promise<Response<CoursewareImdlyLearningTokenResponse>> {
    return commonRequestApi<CoursewareImdlyLearningTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async invalidStudentCourse(
    params: { studentReLearnToken: string; studentCourseId: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.invalidStudentCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async relearnStudentCourse(
    params: { studentReLearnToken: string; studentCourseId: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.relearnStudentCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 操作场景-测试接口
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sceneDelete(
    params: {
      preCommand?: AutonomousCourseLearningScenePreRemoveCommand
      commitCommand?: AutonomousCourseLearningSceneCommitRemoveCommand
    },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sceneDelete,
    operation?: string
  ): Promise<Response<NeedRecalculateProcessResult>> {
    return commonRequestApi<NeedRecalculateProcessResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 操作场景-测试接口
   * @param mutate 查询 graphql 语法文档
   * @param command 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sceneOperate(
    command: AutonomousCourseLearningScenePreCreateCommand,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sceneOperate,
    operation?: string
  ): Promise<Response<NeedRecalculateProcessResult>> {
    return commonRequestApi<NeedRecalculateProcessResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: { command },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 操作场景-测试接口
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sceneUpdate(
    params: {
      preCommand?: AutonomousCourseLearningScenePreUpdateCommand
      commitCommand?: AutonomousCourseLearningSceneCommitUpdateCommand
    },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sceneUpdate,
    operation?: string
  ): Promise<Response<NeedRecalculateProcessResult>> {
    return commonRequestApi<NeedRecalculateProcessResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
