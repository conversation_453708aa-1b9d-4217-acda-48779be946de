import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/sds-platform-jxjy-learningscheme-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'sds-platform-jxjy-learningscheme-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 修改住宿信息
<AUTHOR>
 */
export class ChangeAccommodationInfoRequest {
  /**
   * 参训资格ID
   */
  trainingQualificationId?: string
  /**
   * 住宿类型
0-无需住宿
1-单人住宿
2-合住
   */
  accommodationType: number
}

/**
 * 校验期别编号是否存在
<AUTHOR>
@date 2022/12/5 16:56
 */
export class JudgeIssueNumExistRequest {
  /**
   * 年度
   */
  year: string
  /**
   * 网校id
   */
  servicerId: string
  /**
   * 期别编号
   */
  issueNum: string
}

/**
 * 导入课程
<AUTHOR>
@date 2025/4/27 16:37
 */
export class ImportCourseBatchRequest {
  /**
   * excel文件路径
   */
  filePath?: string
}

/**
 * 导入课程返回
异常code
500-接口异常
3003-表头校验失败
3004-excel表最大长度校验失败
 */
export class ImportCourseResponse {
  /**
   * 成功的导入课程数据
   */
  successCourseInfos: Array<CourseInfo>
  /**
   * 失败的导入课程数据
   */
  failureCourseInfos: Array<CourseInfo>
  /**
   * 如果有失败数据，查询失败数据文件路径
   */
  excelFilePath: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * 课程信息
 */
export class CourseInfo {
  /**
   * 校验code
   */
  code: string
  /**
   * 校验信息
   */
  message: string
  /**
   * 课程名称
   */
  courseName: string
  /**
   * 授课教师
   */
  teacherName: string
  /**
   * 教师职称
   */
  teacherTitle: string
  /**
   * 教师所在单位
   */
  teacherUnit: string
  /**
   * 课程学时
   */
  period: string
  /**
   * 授课日期
   */
  teachingDate: string
  /**
   * 授课开始时间
   */
  teachingStartTime: string
  /**
   * 授课结束时间
   */
  teachingEndTime: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 为期别批量创建课程
   * @param request:
   * <AUTHOR> By Cb
   * @since 2024/11/26 20:04
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchImportCourse(
    request: ImportCourseBatchRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchImportCourse,
    operation?: string
  ): Promise<Response<ImportCourseResponse>> {
    return commonRequestApi<ImportCourseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验期别编号在网校是否重复
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async judgeIssueNumExist(
    request: JudgeIssueNumExistRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.judgeIssueNumExist,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新参训资格住宿信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateAccommodationType(
    request: ChangeAccommodationInfoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateAccommodationType,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
