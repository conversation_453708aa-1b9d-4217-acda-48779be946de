import getStudentPayStatus from './queries/getStudentPayStatus.graphql'
import applyStudentLearningToken from './mutates/applyStudentLearningToken.graphql'
import createOrder from './mutates/createOrder.graphql'
import enterIndexResponse from './mutates/enterIndexResponse.graphql'
import getTrainSupervisionForAHZJ from './mutates/getTrainSupervisionForAHZJ.graphql'
import getVerificationCodeForAHZJ from './mutates/getVerificationCodeForAHZJ.graphql'
import sendCodeAnswerForAHZJ from './mutates/sendCodeAnswerForAHZJ.graphql'
import validAllowToCreateOrder from './mutates/validAllowToCreateOrder.graphql'
import validAllowToLearning from './mutates/validAllowToLearning.graphql'

export {
  getStudentPayStatus,
  applyStudentLearningToken,
  createOrder,
  enterIndexResponse,
  getTrainSupervisionForAHZJ,
  getVerificationCodeForAHZJ,
  sendCodeAnswerForAHZJ,
  validAllowToCreateOrder,
  validAllowToLearning
}
