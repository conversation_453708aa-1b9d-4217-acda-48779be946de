import { OfflineInvoiceDeliveryChannelResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

class TakePlaceDetailVo {
  /**
   * 唯一标识
   */
  id = ''
  /**
   * 自取点名称
   */
  name = ''
  /**
   * 领取地点
   */
  address = ''
  /**
   * 领取时间
   */
  openTakeTime = ''
  /**
   * 备注
   */
  remark = ''
  /**
   * 状态
   */
  status = true

  static from(res: OfflineInvoiceDeliveryChannelResponse) {
    const takePlaceDetail = new TakePlaceDetailVo()
    takePlaceDetail.id = res.channelId
    takePlaceDetail.name = res.channelName
    takePlaceDetail.address = res.address
    takePlaceDetail.openTakeTime = res.deliveryDate
    takePlaceDetail.remark = res.remark
    takePlaceDetail.status = res.enable
    return takePlaceDetail
  }
}
export default TakePlaceDetailVo
