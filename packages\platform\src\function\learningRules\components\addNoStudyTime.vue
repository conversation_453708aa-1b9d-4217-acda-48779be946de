<template>
  <el-drawer title="设置课程学习时间" :visible.sync="openDialog" size="700px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-alert type="warning" show-icon :closable="false" class="m-alert">
        设置每天不学习的时间段，设置完成后，学员课程学习不会在这些时间段内进行学习
      </el-alert>
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form ref="dialogRef" :rules="rules" :model="timeRange" label-width="auto" class="m-form f-mt30">
            <el-form-item label="每天不学习时间段：" prop="time">
              <el-time-picker
                is-range
                v-model="timeRange.time"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围"
                value-format="HH:mm:ss"
              >
              </el-time-picker>
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="openDialog = false">取消</el-button>
              <el-button type="primary" @click="sure()">确认</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'
  import { ElForm } from 'element-ui/types/form'
  import RuleInfo, { TimeRange } from '@api/service/management/learning-rule/model/RuleInfo'

  @Component
  export default class extends Vue {
    @Ref('dialogRef') dialogRef: ElForm

    // 接收规则信息
    @Prop({
      type: RuleInfo,
      default: () => new RuleInfo()
    })
    ruleInfo: RuleInfo

    // 打开抽屉
    openDialog = false

    // 年度模型
    timeRange = new TimeRange()

    // 规则
    rules = {
      time: [{ required: true, validator: this.validateTime, message: '请设置年度培训时间', trigger: 'blur' }]
    }

    // 初始化
    init() {
      this.timeRange = new TimeRange()
      this.openDialog = true
      this.dialogRef.resetFields()
    }

    // 确认添加时间区间
    async sure() {
      const res = await this.dialogRef.validate()
      if (!res) return
      this.ruleInfo.addNoStudyTime(this.timeRange)
      this.openDialog = false
    }

    // 自定义时间校验
    validateTime(rule: any, value: any, callback: any) {
      if (!value[0] || !value[1]) {
        callback(new Error('请设置年度培训时间'))
      } else {
        callback()
      }
    }
  }
</script>
