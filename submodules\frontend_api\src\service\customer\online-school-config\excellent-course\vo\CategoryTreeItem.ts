import { CourseCategoryResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

export default class CategoryTreeItem extends CourseCategoryResponse {
  /**
   * 子节点
   */
  children: Array<CategoryTreeItem> = new Array<CategoryTreeItem>()

  /**
   * 是否有子节点 默认为true查询一次之后children为空再置false
   */
  haveChildren = true

  static form(dto: CourseCategoryResponse) {
    const categoryTreeItem = new CategoryTreeItem()
    categoryTreeItem.name = dto.name
    categoryTreeItem.id = dto.id
    categoryTreeItem.parentId = dto.parentId
    categoryTreeItem.sort = dto.sort

    return categoryTreeItem
  }
}
