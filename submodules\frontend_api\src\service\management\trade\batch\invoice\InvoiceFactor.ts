/*
 * @Description: 发票工厂
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-25 14:27:32
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-15 16:19:31
 */
import QueryInvoice from '@api/service/management/trade/batch/invoice/query/QueryInvoice'
import QueryDeliveryInvoice from '@api/service/management/trade/batch/invoice/query/QueryDeliveryInvoice'
import MutationInvoice from '@api/service/management/trade/batch/invoice/mutation/MutationInvoice'
import MutationDeliveryInvoice from './mutation/MutationDeliveryInvoice'
import QueryOffLineInvoice from './query/QueryOffLineInvoice'
import MutationOffLineInvoice from './mutation/MutationOffLineInvoice'
class InvoiceFactor {
  /**
   * 发票线上查询
   */
  get queryInvoice() {
    return new QueryInvoice()
  }
  /**
   * 发票线上业务
   */
  get mutationInvoice() {
    return new MutationInvoice()
  }
  /**
   * 发票线下查询
   */
  get queryOffLineInvoice() {
    return new QueryOffLineInvoice()
  }
  /**
   * 发票线下业务
   */
  get mutationOffLineInvoice() {
    return new MutationOffLineInvoice()
  }
  /**
   * 发票配送查询
   */
  get queryDeliveryInvoice() {
    return new QueryDeliveryInvoice()
  }
  /**
   * 发票配送业务
   */
  get mutationDeliveryInvoice() {
    return new MutationDeliveryInvoice()
  }
}
export default InvoiceFactor
