import { OperationEnum } from '@api/service/management/train-class/mutation/Enum/OperationEnum'
import MutationCreateTrainClassCommodity from '@api/service/management/train-class/mutation/MutationCreateTrainClassCommodity'
import TemplateNameManager from '@api/service/management/train-class/mutation/dto/TemplateNameManager'
import Classification from '@api/service/management/train-class/mutation/vo/Classification'
import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
import { default as CommonLearningType } from '@api/service/common/scheme/model/LearningType'

import { ClassificationAssess } from '@api/service/management/train-class/mutation/vo/ClassificationAssess'
import { cloneDeep } from 'lodash'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import ExperienceItem from '@api/service/management/train-class/mutation/vo/ExperienceItem'
import JoinTimeType, { JoinTimeTypeEnum } from '@api/service/management/train-class/mutation/Enum/JoinTimeTypeEnum'
import CheckType, { CheckTypeEnum } from '@api/service/management/train-class/mutation/Enum/CheckTypeEnum'
import AnswerType from '@api/service/management/train-class/mutation/Enum/AnswerTypeEnum'
import ExperienceCourseItem from '@api/service/management/train-class/mutation/vo/ExperienceCourseItem'
import ExperienceType, {
  LearningExperienceEnum
} from '@api/service/management/train-class/mutation/Enum/LearningExperienceEnum'
import getServicerIsDocking from '@api/service/management/online-school-config/portal/query/QueryPortal'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import { ExamMethodEnum } from '@api/service/common/enums/train-class/ExamMethodEnum'
import OfflinePartJsonUtil from '@api/service/management/train-class/offlinePart/util/OfflinePartJsonUtil'
import LoopCount from '@api/service/management/train-class/offlinePart/model/LoopCount'

class TrainClassConfigJsonManager {
  /**
   * 循环计数器
   * @private
   */
  loopCount = new LoopCount()
  /**
   * 期别idMap，<状态层的期别id, 后端的期别id（可能是生成的占位字符串）>
   */
  issueIdMap = new Map<string, string>()

  idMap = new Map<string, string>()
  schemeType = 1

  calculatorSchemeConfigJson(schemeConfigJsonString: string) {
    return JSON.parse(schemeConfigJsonString)
  }

  jsonConfigConvertToLearningType(schemeConfigJsonString: string, schemeType = 1): LearningType {
    const jsonObject = this.calculatorSchemeConfigJson(schemeConfigJsonString)
    const learningType = new LearningType()
    this.schemeType = schemeType
    this.configCourseLearning(learningType, jsonObject)
    this.configExam(learningType, jsonObject)
    this.configInterest(learningType, jsonObject)
    this.configPractice(learningType, jsonObject)
    this.configExperience(learningType, jsonObject)
    // 心得数据填充
    // 期别、问卷填充
    const { issue, questionnaire } = CommonLearningType.configIssueAndQuestionnaire(jsonObject)
    learningType.issue = issue
    learningType.questionnaire = questionnaire
    return learningType
  }

  //配置兴趣课
  configInterest(learningType: LearningType, classConfigJson: any) {
    //填充兴趣课
    const interestCourseLearningConfig: any = classConfigJson.interestCourseLearning
    const interestCourse = learningType.interestCourse

    if (interestCourseLearningConfig) {
      interestCourse.isSelected = true
      interestCourse.learningTypeId = interestCourseLearningConfig.id
      interestCourse.configId = interestCourseLearningConfig.config.id
      interestCourse.operation = interestCourseLearningConfig.config.operation
      if (
        interestCourseLearningConfig.config.courseTrainingOutlines &&
        interestCourseLearningConfig.config.courseTrainingOutlines.length
      ) {
        const interestTopLevelClassFication: any = {
          id: '9999',
          name: '顶级节点',
          sort: 1,
          coursePackageId: '',
          category: 0,
          compulsoryCourseIdList: [],
          childOutlines: interestCourseLearningConfig.config.courseTrainingOutlines
        }
        const classification = this.getClassficationArr(interestTopLevelClassFication, true)
        const firstClassFication = interestCourseLearningConfig.config.courseTrainingOutlines[0]
        //无分类
        if (firstClassFication.name == TemplateNameManager.NOCourseTrainingOutlinesName) {
          interestCourse.classification = classification.childOutlines[0]
        } else {
          interestCourse.classification = classification
        }
      }
    }
  }

  //   配置练习
  configPractice(learningType: LearningType, classConfigJson: any) {
    //填充练习
    const practiceLearningConfig: any = classConfigJson.practiceLearning
    const practiceLearning = learningType.practiceLearning

    if (practiceLearningConfig) {
      practiceLearning.isSelected = true
      practiceLearning.learningTypeId = practiceLearningConfig.id

      practiceLearning.type = practiceLearningConfig.config.type
      if (practiceLearning.type == 1 || practiceLearning.type == 3) {
        if (practiceLearning.type == 1 && practiceLearningConfig.config.questionSource) {
          practiceLearning.libraryIds = practiceLearningConfig.config.questionSource.libraryIds
        }
        if (practiceLearning.type == 3 && practiceLearningConfig.config.questionSource) {
          practiceLearning.paperPublishConfigureId =
            practiceLearningConfig.config.questionSource.paperPublishConfigureId
        }
      }
      practiceLearning.configId = practiceLearningConfig.config.id
      practiceLearning.operation = practiceLearningConfig.config.operation

      practiceLearning.openDissects = practiceLearningConfig.config.openDissects

      practiceLearning.multipleQuestionMissScorePatterns =
        practiceLearningConfig.config.multipleQuestionMissScorePatterns
    }
  }

  // 学习心得数据填充
  configExperience(learningType: LearningType, classConfigJson: any) {
    const learningExperienceConfig: any = classConfigJson.learningExperienceLearning
    const learningExperience = learningType.learningExperience
    if (learningExperienceConfig) {
      learningExperience.isSelected = true
      learningExperience.learningTypeId = learningExperienceConfig.id
      learningExperience.experienceName = learningExperienceConfig.config.showName
      if (learningExperienceConfig.assessSetting) {
        learningExperience.isExamine = true
        learningExperience.assessId = learningExperienceConfig.assessSetting.id
        learningExperience.assessName = learningExperienceConfig.assessSetting.name
        learningExperience.joinCount =
          learningExperienceConfig.assessSetting.lessParticipateLearningExperienceTopicCount
        learningExperience.score = learningExperienceConfig.assessSetting.eachLearningExperienceTopicPassScore
      }
      if (learningExperienceConfig.config.schemeParticipateRequired) {
        learningExperience.classCondition = learningExperienceConfig.config.schemeParticipateRequired.requiredType === 1
      }
      if (learningExperienceConfig.config.courseParticipateRequired) {
        learningExperience.courseCondition =
          learningExperienceConfig.config.courseParticipateRequired.requiredType === 1
      }
      learningExperience.configId = learningExperienceConfig.config.id
      if (
        learningExperienceConfig.config.schemeLearningExperienceTopics &&
        learningExperienceConfig.config.schemeLearningExperienceTopics.length
      ) {
        learningExperienceConfig.config.schemeLearningExperienceTopics.forEach((experienceItem: any) => {
          const item = new ExperienceItem(experienceItem.id)
          this.getLearningExperienceItem(experienceItem, item)
          item.experienceType = new ExperienceType(LearningExperienceEnum.class_experience)
          learningExperience.score = experienceItem.passScore || learningExperience.score
          learningExperience.experienceList.push(item)
        })
      }
      if (
        learningExperienceConfig.config.courseLearningExperienceTopics &&
        learningExperienceConfig.config.courseLearningExperienceTopics.length
      ) {
        learningExperienceConfig.config.courseLearningExperienceTopics.forEach((experienceItem: any) => {
          const item = new ExperienceItem(experienceItem.id)
          this.getLearningExperienceItem(experienceItem, item)
          item.experienceType = new ExperienceType(LearningExperienceEnum.course_experience)
          learningExperience.score = experienceItem.passScore || learningExperience.score
          const course = new ExperienceCourseItem()
          course.courseId = experienceItem.courses[0].courseId
          course.outlineId = experienceItem.courses[0].outlineId
          course.coursePackageId = experienceItem.courses[0].packageId
          item.courseList.push(course)
          learningExperience.experienceList.push(item)
        })
      }
    }
  }

  //   配置Exam
  configExam(learningType: LearningType, classConfigJson: any) {
    //填充考试
    const examLearningConfig: any = classConfigJson.examLearning
    const exam = learningType.exam

    if (examLearningConfig) {
      exam.learningTypeId = examLearningConfig.id
      exam.examPassScore = examLearningConfig.config.qualifiedScore
      if (examLearningConfig.assessSetting) {
        exam.assessId = examLearningConfig.assessSetting.id
        exam.assessName = examLearningConfig.assessSetting.name
        exam.examPassScore = examLearningConfig.assessSetting.examPassScore
        exam.isExamAssessed = examLearningConfig.assessSetting.operation != 3
      }
      exam.configId = examLearningConfig.config.id
      exam.preconditionId = examLearningConfig.precondition?.id || ''
      exam.preconditionName = examLearningConfig.precondition?.name || ''
      exam.operation = examLearningConfig.config.operation
      exam.gradesWhetherHide = examLearningConfig.config.gradesWhetherHide

      exam.isSelected = true
      exam.isAssess = true
      exam.name = examLearningConfig.config.name
      exam.description = examLearningConfig.config.description
      exam.paperPublishConfigureId = examLearningConfig.config.paperPublishConfigureId
      //TODO 去试卷模块拿模板名称
      exam.allowCount = examLearningConfig.config.allowCount
      exam.allowStartTime = examLearningConfig.config.allowStartTime
      exam.allowEndTime = examLearningConfig.config.allowEndTime
      exam.timeLength = examLearningConfig.config.timeLength / 60
      exam.qualifiedScore = examLearningConfig.config.qualifiedScore
      exam.allowAnswerIfQualified = examLearningConfig.config.allowAnswerIfQualified
      exam.openDissects = examLearningConfig.config.openDissects
      exam.questionAgainAnswer = examLearningConfig.config.questionAgainAnswer
      exam.questionDisplay = examLearningConfig.config.questionDisplay
      exam.multipleMissScorePattern = examLearningConfig.config.multipleMissScorePattern
      exam.examPattern = examLearningConfig.config.examType
        ? examLearningConfig.config.examType
        : ExamMethodEnum.on_call_exam
      if (examLearningConfig.config.examType === 1) {
        exam.allowAnswerIfQualified = false
      } else {
        exam.allowAnswerIfQualified = examLearningConfig.config.allowAnswerIfQualified
      }
      // exam.examPassScore = examLearningConfig.assessSetting.examPassScore

      exam.preCondition = examLearningConfig.precondition && examLearningConfig.precondition.operation != 3 ? 1 : 0
    }
  }

  //   配置courseLearning
  configCourseLearning(learningType: LearningType, classConfigJson: any) {
    const courseLearning = learningType.courseLearning
    let learningKey = 'chooseCourseLearning'
    //    jsonObj.type = this.schemeType == 1 ? 'chooseCourseLearning':'autonomousCourseLearning'
    if (classConfigJson.chooseCourseLearning || classConfigJson.autonomousCourseLearning) {
      if (classConfigJson.type == 'chooseCourseLearning') {
        courseLearning.learningTypeId = classConfigJson.chooseCourseLearning.id
        courseLearning.assessId = classConfigJson.chooseCourseLearning.assessSetting.id
        courseLearning.assessName = classConfigJson.chooseCourseLearning.assessSetting.name
        courseLearning.configId = classConfigJson.chooseCourseLearning.config.id
        courseLearning.operation = classConfigJson.chooseCourseLearning.config.operation
        courseLearning.isSelected = true
        courseLearning.isAssess = true
        // trainClassDetail.trainClassBaseInfo.schemeType = 1
        //填充选课规则
        const configChooseRule: any = classConfigJson.chooseCourseLearning.config.chooseCourseRule
        const configChooseRuleConfig =
          typeof configChooseRule.config === 'string' ? JSON.parse(configChooseRule.config) : configChooseRule.config
        courseLearning.chooseCourseRule.id = configChooseRule.id
        courseLearning.chooseCourseRule.name = configChooseRule.name

        courseLearning.chooseCourseRule.allowLastChooseOver = configChooseRuleConfig.allowLastChooseOver
        courseLearning.chooseCourseRule.compulsoryPeriod = configChooseRuleConfig.compulsoryPeriod
        courseLearning.chooseCourseRule.secondElectiveMaxPeriod = configChooseRuleConfig.secondElectiveMaxPeriod || []
        courseLearning.chooseCourseRule.electiveMaxPeriod = configChooseRuleConfig.electiveMaxPeriod
        courseLearning.chooseCourseRule.constrainedRepeatSelection = configChooseRuleConfig.constrainedRepeatSelection
        courseLearning.chooseCourseRule.constrainedRangeKeyList = configChooseRuleConfig.constrainedRangeKeyList || []
        //填充必修选修要求完成学时
        courseLearning.compulsoryRequirePeriod =
          classConfigJson.chooseCourseLearning.assessSetting.compulsoryRequirePeriod
        courseLearning.electiveRequirePeriod = classConfigJson.chooseCourseLearning.assessSetting.electiveRequirePeriod
      } else if (classConfigJson.type == 'autonomousCourseLearning') {
        learningKey = 'autonomousCourseLearning'
        courseLearning.isSelected = true
        courseLearning.isAssess = true
        courseLearning.learningTypeId = classConfigJson.autonomousCourseLearning.id
        courseLearning.assessId = classConfigJson.autonomousCourseLearning.assessSetting.id
        courseLearning.assessName = classConfigJson.autonomousCourseLearning.assessSetting.name
        courseLearning.configId = classConfigJson.autonomousCourseLearning.config.id
        // trainClassDetail.trainClassBaseInfo.schemeType = 2
        courseLearning.requirePeriod = classConfigJson.autonomousCourseLearning.assessSetting.requirePeriod
      }
      //填充课程学习中的学习大纲
      const courseTrainingOutlines: any = classConfigJson[learningKey].config.courseTrainingOutlines

      if (courseTrainingOutlines && courseTrainingOutlines.length) {
        const topLevelClassFication: any = {
          id: '9999',
          name: '顶级节点',
          sort: 1,
          coursePackageId: '',
          category: 0,
          compulsoryCourseIdList: [],
          childOutlines: courseTrainingOutlines
        }
        const classification = this.getClassficationArr(topLevelClassFication)
        const firstClassFication = courseTrainingOutlines[0]
        //无分类
        if (firstClassFication.name == TemplateNameManager.NOCourseTrainingOutlinesName) {
          courseLearning.classification = classification.childOutlines[0]
        } else {
          courseLearning.classification = classification
        }
      }

      //填充课程学习中课后测验
      if (classConfigJson[learningKey].config.courseQuizConfig) {
        courseLearning.configCourseQuiz = true
        const courseQuizConfig: any = classConfigJson[learningKey].config.courseQuizConfig
        const quizConfigModel = courseLearning.quizConfigModel
        quizConfigModel.minCourseSchedule = courseQuizConfig.precondition.minCourseSchedule
        quizConfigModel.courseQuizPaperStandard = courseQuizConfig.precondition.courseQuizPaperStandard
        quizConfigModel.name = courseQuizConfig.quizConfig.name
        quizConfigModel.publishPattern = courseQuizConfig.quizConfig.publishPattern
        quizConfigModel.openDissects = courseQuizConfig.quizConfig.openDissects
        quizConfigModel.limitCourseQuizNum = courseQuizConfig.quizConfig.limitCourseQuizNum
        quizConfigModel.allowCourseQuizNum = courseQuizConfig.quizConfig.allowCourseQuizNum
        quizConfigModel.multipleMissScorePattern = courseQuizConfig.quizConfig.multipleMissScorePattern
        quizConfigModel.questionCount = courseQuizConfig.quizConfig.userCoursePaperPublishConfigure.questionCount
        quizConfigModel.totalScore = courseQuizConfig.quizConfig.userCoursePaperPublishConfigure.totalScore
        quizConfigModel.passScore = courseQuizConfig.quizConfig.passScore
        quizConfigModel.id = courseQuizConfig.id
        quizConfigModel.configName = courseQuizConfig.quizConfig.name
        quizConfigModel.configId = courseQuizConfig.quizConfig.id
        quizConfigModel.questionCountConfigureType =
          courseQuizConfig.quizConfig.userCoursePaperPublishConfigure.questionCountConfigureType
        quizConfigModel.questionCountPerPeriod =
          courseQuizConfig.quizConfig.userCoursePaperPublishConfigure.questionCountPerPeriod
        quizConfigModel.timeLengthPerQuestion =
          courseQuizConfig.quizConfig.userCoursePaperPublishConfigure.timeLengthPerQuestion
      }
      //填充评价
      if (classConfigJson[learningKey].config.courseAppraisalConfig) {
        const courseAppraisalConfig = classConfigJson[learningKey].config.courseAppraisalConfig
        courseLearning.enableAppraisal = courseAppraisalConfig.enableAppraisal
        courseLearning.enableCompulsoryAppraisal = courseAppraisalConfig.enableCompulsoryAppraisal
        courseLearning.preconditionCourseSchedule = courseAppraisalConfig.preconditionCourseSchedule
      }

      //填充课程完成评定
      const courseCompleteEvaluateConfig = classConfigJson[learningKey].config.courseCompleteEvaluateConfig
      courseLearning.courseSchedule = courseCompleteEvaluateConfig.courseSchedule
      courseLearning.courseQuizPagerStandard = courseCompleteEvaluateConfig.courseQuizPagerStandard
    }
  }

  //将大纲json转换为类
  private getClassficationArr(item: any, isInterest = false, parentId?: string): Classification {
    const classification = new Classification()
    classification.name = item.name
    classification.id = item.id
    classification.idCopy = item.id
    classification.sort = item.sort
    classification.coursePackageId = item.coursePackageId
    classification.operation = item.operation
    classification.parentId = parentId
    if (!isInterest) {
      if (this.schemeType == 1) {
        classification.category = item.category
      }
      if (this.schemeType == 2) {
        classification.compulsoryCourseIdList = item.compulsoryCourseIdList
        if (item.assessSetting) {
          classification.assessSetting.id = item.assessSetting.id
          classification.assessSetting.rangeKey = item.assessSetting.rangeKey
          classification.assessSetting.requirePeriod = item.assessSetting.requirePeriod
          classification.assessSetting.name = item.assessSetting.name
          classification.assessSetting.operation = item.assessSetting.operation
        }
      }
    }

    item.childOutlines.forEach((tmpItem: any) => {
      classification.childOutlines.push(this.getClassficationArr(tmpItem, isInterest, classification.id))
    })
    return classification
  }

  convertJsonObj: any = {}

  // 课程对比
  compareWay(type: string, createCommodity: MutationCreateTrainClassCommodity, learningTypeModelCopy: LearningType) {
    if (learningTypeModelCopy[type].configId) {
      if (createCommodity.learningTypeModel[type]?.isSelected) {
        createCommodity.learningTypeModel[type].operation = OperationEnum.UPDATE
      } else {
        createCommodity.learningTypeModel[type] = cloneDeep(learningTypeModelCopy[type])
        createCommodity.learningTypeModel[type].operation = OperationEnum.REMOVE
      }
    }
    if ((type === 'courseLearning' || type === 'interestCourse') && learningTypeModelCopy[type].configId) {
      //
      let courseLearningClassification
      if (
        learningTypeModelCopy[type]?.classification.id === '9999' &&
        createCommodity.learningTypeModel[type].classification.id !== '9999'
      ) {
        const classification: any = {
          id: '9999',
          name: '顶级节点',
          sort: 1,
          coursePackageId: '',
          category: 0,
          compulsoryCourseIdList: [],
          assessSetting: new ClassificationAssess(),
          childOutlines: [cloneDeep(createCommodity.learningTypeModel[type].classification)]
        }
        classification.childOutlines[0].id = TemplateNameManager.NOCourseTrainingOutlinesId
        classification.childOutlines[0].name = TemplateNameManager.NOCourseTrainingOutlinesName
        courseLearningClassification = classification
      } else {
        courseLearningClassification = cloneDeep(createCommodity.learningTypeModel[type].classification)
      }
      const classificationCopyList = new Array<Classification>()
      this.getClassificationList(
        classificationCopyList,
        [learningTypeModelCopy[type]?.classification],
        OperationEnum.REMOVE
      )
      this.getCreateClassification([courseLearningClassification], classificationCopyList)
      this.pushRemoveClassification([courseLearningClassification], [learningTypeModelCopy[type]?.classification])
      createCommodity.learningTypeModel[type].classification = cloneDeep(courseLearningClassification)
    }
    if (
      type === 'learningExperience' &&
      createCommodity.learningTypeModel[type].operation === OperationEnum.UPDATE &&
      learningTypeModelCopy[type].configId
    ) {
      //
      learningTypeModelCopy.learningExperience.experienceList.forEach((item) => {
        const itemIndex = createCommodity.learningTypeModel.learningExperience.experienceList.findIndex(
          (tmpItem) => tmpItem.id === item.id
        )
        if (itemIndex === -1) {
          createCommodity.learningTypeModel.learningExperience.experienceList.push(item)
        }
      })
    }

    // 比较期别信息
    OfflinePartJsonUtil.compareIssue(type, createCommodity, learningTypeModelCopy)
    // 比较问卷信息
    OfflinePartJsonUtil.compareQuestionnaire(type, createCommodity, learningTypeModelCopy)
  }

  /**
   * 打平分类
   */
  getClassificationList(
    classificationList: Array<Classification>,
    classification: Array<Classification>,
    operationEnum: OperationEnum
  ) {
    //
    classification.forEach((item) => {
      item.operation = operationEnum
      if (item?.assessSetting?.operation) item.assessSetting.operation = operationEnum
      classificationList.push(item)
      if (item?.childOutlines?.length) this.getClassificationList(classificationList, item.childOutlines, operationEnum)
    })
  }

  /**
   * 当前比对旧大纲判断新增
   */
  getCreateClassification(current: Array<Classification>, old: Array<Classification>) {
    current.forEach((item) => {
      const has = old?.some((ite) => ite.id === item.id)
      if (!has) {
        item.operation = OperationEnum.CREATE
        if (item.assessSetting) item.assessSetting.operation = OperationEnum.CREATE
      } else {
        item.operation = OperationEnum.UPDATE
        if (item.assessSetting) item.assessSetting.operation = OperationEnum.UPDATE
      }
      if (item?.childOutlines?.length) this.getCreateClassification(item.childOutlines, old)
    })
  }

  /**
   * 赋值操作类型
   */
  assignmentOperation(classification: Array<Classification>, operationEnum: OperationEnum) {
    classification.forEach((item) => {
      item.operation = operationEnum
      if (item.assessSetting) item.assessSetting.operation = OperationEnum.CREATE
    })
  }

  /**
   * 填充删除节点
   */
  pushRemoveClassification(current: Array<Classification>, old: Array<Classification>) {
    old.forEach((item) => {
      console.log(item, item.name, 'item')

      const classification = this.treeFind(current, (node: Classification) => {
        return node.id === item.id
      })
      if (classification) {
        if (item?.childOutlines?.length) this.pushRemoveClassification(current, item?.childOutlines)
      } else {
        const removeParentClassification =
          this.treeFind(current, (node: Classification) => {
            return node.id === item.parentId
          }) ||
          this.treeFind(current, (node: Classification) => {
            return node.id === '9999'
          }) ||
          this.treeFind(current, (node: Classification) => {
            return node.id === ''
          })
        console.log(removeParentClassification, current, 'removeParentClassification')

        this.assignmentOperation([item], OperationEnum.REMOVE)
        if (!removeParentClassification.childOutlines)
          removeParentClassification.childOutlines = new Array<Classification>()
        removeParentClassification.childOutlines.push(item)
      }
    })
  }

  /**
   * 查找节点
   */
  treeFind(tree: Array<Classification>, func: any, childKey = 'childOutlines'): Classification | undefined {
    for (const item of tree) {
      if (func(item)) return item
      if (item[childKey] && item[childKey].length) {
        const res: Classification | undefined = this.treeFind(item[childKey], func, childKey)
        if (res) return res
      }
    }
    return undefined
  }

  convertCreateCommodityToJsonString(
    createCommodity: MutationCreateTrainClassCommodity,
    learningTypeModelCopy?: LearningType
  ): string {
    const isUpdate = !!learningTypeModelCopy
    // 重置计数，每个方案都从头开始
    this.loopCount = new LoopCount()
    if (isUpdate) {
      this.compareWay('courseLearning', createCommodity, learningTypeModelCopy)
      this.compareWay('exam', createCommodity, learningTypeModelCopy)
      this.compareWay('practiceLearning', createCommodity, learningTypeModelCopy)
      this.compareWay('interestCourse', createCommodity, learningTypeModelCopy)
      this.compareWay('learningExperience', createCommodity, learningTypeModelCopy)
      this.compareWay('issue', createCommodity, learningTypeModelCopy)
      this.compareWay('questionnaire', createCommodity, learningTypeModelCopy)
    }
    const jsonObj: any = {
      commoditySale: {}
    }
    this.convertJsonObj = jsonObj
    const basInfo = createCommodity.trainClassBaseInfo
    jsonObj.id = basInfo.id ? basInfo.id : TemplateNameManager.schemeID
    jsonObj.name = basInfo.name
    jsonObj.provideRelearn = basInfo.provideRelearn
    jsonObj.picture = basInfo.picture
    jsonObj.comment = basInfo.comment
    jsonObj.registerBeginDate = basInfo.registerBeginDate
    jsonObj.registerEndDate = basInfo.registerEndDate
    jsonObj.trainingBeginDate = basInfo.trainingBeginDate
    jsonObj.trainingEndDate = basInfo.trainingEndDate
    jsonObj.notice = basInfo.notice
    jsonObj.issueNotice = basInfo.issueNotice
    jsonObj.introId = basInfo.introId ? basInfo.introId : undefined
    jsonObj.introContent = basInfo.introContent
    this.schemeType = basInfo.schemeType
    // jsonObj.type = SchemeType.getSchemeType(this.schemeType)
    jsonObj.type = SchemeTypeEnum[this.schemeType] ?? SchemeTypeEnum.chooseCourseLearning
    // } else {
    //   jsonObj.type = ''
    // }

    jsonObj.commoditySale.id = createCommodity.commoditySkuId
      ? createCommodity.commoditySkuId
      : TemplateNameManager.commoditySaleId
    jsonObj.commoditySale.saleTitle = createCommodity.trainClassBaseInfo.name
    jsonObj.commoditySale.price = createCommodity.price
    jsonObj.commoditySale.categoryId = createCommodity.categoryId
      ? createCommodity.categoryId
      : TemplateNameManager.commoditySaleCategoryId
    jsonObj.commoditySale.closeCustomerPurchase = createCommodity.closeCustomerPurchase
    jsonObj.commoditySale.visibleChannelList = createCommodity.visibleChannelList
    // jsonObj.commoditySale.taxCode = createCommodity.taxCode

    jsonObj.commoditySale.onOrOffShelvesPlan = {}
    jsonObj.commoditySale.onOrOffShelvesPlan.onShelve = createCommodity.onShelves
    jsonObj.commoditySale.onOrOffShelvesPlan.onShelvePlanTime = createCommodity.onShelvesPlanTime
    jsonObj.commoditySale.onOrOffShelvesPlan.offShelvePlanTime = createCommodity.offShelvesPlanTime

    //配置学习类型
    this.convertCourseLearning(jsonObj, createCommodity, isUpdate, learningTypeModelCopy)
    this.convertExamLearning(jsonObj, createCommodity)
    this.convertPracticeLearning(jsonObj, createCommodity)
    this.convertInterestCourseLearning(jsonObj, createCommodity, isUpdate)
    // 心得转换
    this.convertLearningExperience(jsonObj, createCommodity, isUpdate)

    const skuProperty = createCommodity.trainClassBaseInfo.skuProperty
    jsonObj.year = parseInt(skuProperty.year.skuPropertyValueId)
    jsonObj.region = skuProperty.region.skuPropertyValueId
    jsonObj.extendProperties = []
    if (skuProperty.industry.skuPropertyValueId) {
      jsonObj.extendProperties.push({
        name: 'industry',
        value: skuProperty.industry.skuPropertyValueId
      })
    }
    if (skuProperty.subjectType.skuPropertyValueId) {
      jsonObj.extendProperties.push({
        name: 'subjectType',
        value: skuProperty.subjectType.skuPropertyValueId
      })
    }
    if (skuProperty.trainingCategory.skuPropertyValueId) {
      jsonObj.extendProperties.push({
        name: 'trainingCategory',
        value: skuProperty.trainingCategory.skuPropertyValueId
      })
    }
    if (skuProperty.trainingMajor.skuPropertyValueId) {
      jsonObj.extendProperties.push({
        name: 'trainingProfessional',
        value: skuProperty.trainingMajor.skuPropertyValueId
      })
    }
    if (skuProperty.technicalGrade.skuPropertyValueId) {
      jsonObj.extendProperties.push({
        name: 'technicalGrade',
        value: skuProperty.technicalGrade.skuPropertyValueId
      })
    }
    if (skuProperty.trainingObject.skuPropertyValueId) {
      jsonObj.extendProperties.push({
        name: 'trainingObject',
        value: skuProperty.trainingObject.skuPropertyValueId
      })
    }
    if (skuProperty.positionCategory.skuPropertyValueId) {
      jsonObj.extendProperties.push({
        name: 'positionCategory',
        value: skuProperty.positionCategory.skuPropertyValueId
      })
    }
    if (skuProperty.jobLevel.skuPropertyValueId) {
      jsonObj.extendProperties.push({
        name: 'jobLevel',
        value: skuProperty.jobLevel.skuPropertyValueId
      })
    }
    // sku转换,// todo
    if (skuProperty.learningPhase.skuPropertyValueId) {
      jsonObj.extendProperties.push({
        name: 'learningPhase',
        value: skuProperty.learningPhase.skuPropertyValueId
      })
    }
    if (skuProperty.discipline.skuPropertyValueId) {
      jsonObj.extendProperties.push({
        name: 'discipline',
        value: skuProperty.discipline.skuPropertyValueId
      })
    }
    if (skuProperty.certificatesType.skuPropertyValueId) {
      jsonObj.extendProperties.push({
        name: 'certificatesType',
        value: skuProperty.certificatesType.skuPropertyValueId
      })
    }
    if (skuProperty.practitionerCategory.skuPropertyValueId) {
      jsonObj.extendProperties.push({
        name: 'practitionerCategory',
        value: skuProperty.practitionerCategory.skuPropertyValueId
      })
    }
    if (createCommodity.trainClassBaseInfo) {
      const isConnectSchool = getServicerIsDocking.getServicerIsDocking()
      if (isConnectSchool) {
        jsonObj.extendProperties.push({
          name: 'needDataSync',
          value: createCommodity.trainClassBaseInfo.needDataSync
        })
        if (createCommodity.trainClassBaseInfo.needDataSync) {
          jsonObj.extendProperties.push({
            name: 'certificatesTrainingStartTimeSource',
            value: createCommodity.trainClassBaseInfo.achievementExhibition
          })
        }
      }
      if (typeof createCommodity.trainClassBaseInfo.showNoticeDialog === 'boolean') {
        jsonObj.extendProperties.push({
          name: 'showNoticeDialog',
          value: createCommodity.trainClassBaseInfo.showNoticeDialog
        })
      }
    }

    //配置学习成果
    OfflinePartJsonUtil.convertOfflinePart(jsonObj, createCommodity, learningTypeModelCopy)

    return JSON.stringify(jsonObj)
  }

  loopNum = 1

  //  将课程学习转换为json
  convertCourseLearning(
    jsonObj: any,
    createCommodity: MutationCreateTrainClassCommodity,
    isUpdate = false,
    learningTypeModelCopy?: LearningType
  ) {
    let learningKey = 'chooseCourseLearning'
    const courseLearningType = createCommodity.learningTypeModel.courseLearning
    let courseLearningConfig: any = {}
    if (courseLearningType.isSelected) {
      if (createCommodity.trainClassBaseInfo.schemeType == 1) {
        jsonObj.chooseCourseLearning = {}
        jsonObj.chooseCourseLearning.id = courseLearningType.learningTypeId
          ? courseLearningType.learningTypeId
          : TemplateNameManager.chooseCourseLearningID
        jsonObj.chooseCourseLearning.assessSetting = {}
        jsonObj.chooseCourseLearning.assessSetting.id = courseLearningType.assessId
          ? courseLearningType.assessId
          : TemplateNameManager.chooseCourseLearningAssessSettingID
        jsonObj.chooseCourseLearning.assessSetting.name = courseLearningType.assessName
          ? courseLearningType.assessName
          : TemplateNameManager.chooseCourseLearningAssessSettingNAME
        jsonObj.chooseCourseLearning.assessSetting.compulsoryRequirePeriod = courseLearningType.compulsoryRequirePeriod
        jsonObj.chooseCourseLearning.assessSetting.electiveRequirePeriod = courseLearningType.electiveRequirePeriod
        jsonObj.chooseCourseLearning.config = {}
        jsonObj.chooseCourseLearning.config.chooseCourseRule = {}
        const chooseCourseRule = jsonObj.chooseCourseLearning.config.chooseCourseRule
        chooseCourseRule.id = courseLearningType.chooseCourseRule.id
          ? courseLearningType.chooseCourseRule.id
          : TemplateNameManager.chooseCourseRuleId
        chooseCourseRule.name = courseLearningType.chooseCourseRule.name
          ? courseLearningType.chooseCourseRule.name
          : TemplateNameManager.chooseCourseRuleName
        chooseCourseRule.config = {}
        chooseCourseRule.config.compulsoryPeriod = courseLearningType.compulsoryRequirePeriod
        chooseCourseRule.config.compulsoryPeriod = courseLearningType.compulsoryRequirePeriod
        chooseCourseRule.config.electiveMaxPeriod = courseLearningType.electiveRequirePeriod
        chooseCourseRule.config.allowLastChooseOver = true
        chooseCourseRule.config.secondElectiveMaxPeriod = courseLearningType.chooseCourseRule.secondElectiveMaxPeriod

        chooseCourseRule.config.constrainedRepeatSelection = true
        chooseCourseRule.config.constrainedRangeKeyList = ['schemeId']
      } else if (createCommodity.trainClassBaseInfo.schemeType == 2) {
        learningKey = 'autonomousCourseLearning'
        jsonObj.autonomousCourseLearning = {}
        jsonObj.autonomousCourseLearning.id = courseLearningType.learningTypeId
          ? courseLearningType.learningTypeId
          : TemplateNameManager.autonomousCourseLearningId
        jsonObj.autonomousCourseLearning.assessSetting = {}
        jsonObj.autonomousCourseLearning.assessSetting.id = courseLearningType.assessId
          ? courseLearningType.assessId
          : TemplateNameManager.autonomousCourseLearningAssessSettingId
        jsonObj.autonomousCourseLearning.assessSetting.name = courseLearningType.assessName
          ? courseLearningType.assessName
          : TemplateNameManager.autonomousCourseLearningAssessSettingNAME
        jsonObj.autonomousCourseLearning.assessSetting.requirePeriod = courseLearningType.requirePeriod
        jsonObj.autonomousCourseLearning.assessSetting.relateAssessIds = []
        jsonObj[learningKey].config = {}
      }
      // jsonObj[learningKey].assessSetting.incorporateCourseQuiz = courseLearningType.incorporateCourseQuiz
      // jsonObj[learningKey].assessSetting.eachCourseQuizPassScore = courseLearningType.eachCourseQuizPassScore
      //config配置
      // jsonObj[learningKey].config = {}
      courseLearningConfig = jsonObj[learningKey].config
      if (courseLearningType.configId) courseLearningConfig.operation = courseLearningType.operation
      courseLearningConfig.id = courseLearningType.configId
        ? courseLearningType.configId
        : this.schemeType == 1
        ? TemplateNameManager.chooseCourseLearningConfigID
        : TemplateNameManager.autonomousCourseLearningConfigID
      // 没有子节点，代表是无分类
      this.loopNum = 1
      console.log('courseLearningType.classification.childOutlines', courseLearningType.classification)
      if (
        courseLearningType.classification.childOutlines &&
        courseLearningType.classification.childOutlines.length == 0
      ) {
        if (
          courseLearningType.classification.id &&
          courseLearningType.classification.id.indexOf(Classification.classificationIdPre) !== -1
        ) {
          this.idMap.set(courseLearningType.classification.id, TemplateNameManager.NOCourseTrainingOutlinesId)
        }
        courseLearningType.classification.id = isUpdate
          ? courseLearningType.classification.id || TemplateNameManager.NOCourseTrainingOutlinesId
          : TemplateNameManager.NOCourseTrainingOutlinesId
        courseLearningType.classification.name = TemplateNameManager.NOCourseTrainingOutlinesName
        courseLearningConfig.courseTrainingOutlines = [
          this.getClassFicationJsonString(courseLearningType.classification, isUpdate)
        ]
      } else {
        courseLearningConfig.courseTrainingOutlines = this.getClassFicationJsonString(
          courseLearningType.classification,
          isUpdate
        ).childOutlines
      }

      //  课后测验配置
      if (courseLearningType.configCourseQuiz) {
        courseLearningConfig.courseQuizConfig = {}
        const courseQuizConfig = courseLearningConfig.courseQuizConfig
        const quizClass = courseLearningType.quizConfigModel
        // 外层不传id
        // courseQuizConfig.id = quizClass.id ? quizClass.id : TemplateNameManager.courseQuizConfigId
        courseQuizConfig.precondition = {}
        courseQuizConfig.precondition.minCourseSchedule = quizClass.minCourseSchedule
        courseQuizConfig.precondition.courseQuizPaperStandard = quizClass.courseQuizPaperStandard
        courseQuizConfig.quizConfig = {}
        courseQuizConfig.quizConfig.id = quizClass.configId
          ? quizClass.configId
          : TemplateNameManager.courseQuizConfigQuizConfigId
        courseQuizConfig.quizConfig.name = quizClass.configName
          ? quizClass.configName
          : TemplateNameManager.courseQuizConfigQuizConfigNAME
        courseQuizConfig.operation = quizClass.configId ? OperationEnum.UPDATE : OperationEnum.CREATE
        courseQuizConfig.quizConfig.operation = quizClass.configId ? OperationEnum.UPDATE : OperationEnum.CREATE
        courseQuizConfig.quizConfig.publishPattern = quizClass.publishPattern
        courseQuizConfig.quizConfig.openDissects = quizClass.openDissects
        courseQuizConfig.quizConfig.limitCourseQuizNum = quizClass.limitCourseQuizNum
        courseQuizConfig.quizConfig.allowCourseQuizNum = quizClass.allowCourseQuizNum
        courseQuizConfig.quizConfig.multipleMissScorePattern = quizClass.multipleMissScorePattern
        courseQuizConfig.quizConfig.passScore = quizClass.passScore
        courseQuizConfig.quizConfig.userCoursePaperPublishConfigure = {}
        const userCoursePaperPublishConfigure = courseQuizConfig.quizConfig.userCoursePaperPublishConfigure
        userCoursePaperPublishConfigure.questionCount = quizClass.questionCount
        userCoursePaperPublishConfigure.totalScore = quizClass.totalScore
        userCoursePaperPublishConfigure.questionCountConfigureType = quizClass.questionCountConfigureType
        userCoursePaperPublishConfigure.questionCountPerPeriod = quizClass.questionCountPerPeriod
        userCoursePaperPublishConfigure.timeLengthPerQuestion = quizClass.timeLengthPerQuestion
      }
      if (isUpdate && !courseLearningType.configCourseQuiz && learningTypeModelCopy.courseLearning?.configCourseQuiz) {
        courseLearningConfig.courseQuizConfig = {}
        const courseQuizConfig = courseLearningConfig.courseQuizConfig
        const quizClass = learningTypeModelCopy.courseLearning.quizConfigModel
        courseQuizConfig.precondition = {}
        courseQuizConfig.precondition.minCourseSchedule = quizClass.minCourseSchedule
        courseQuizConfig.precondition.courseQuizPaperStandard = quizClass.courseQuizPaperStandard
        courseQuizConfig.quizConfig = {}
        courseQuizConfig.operation = OperationEnum.REMOVE
        courseQuizConfig.quizConfig.operation = OperationEnum.REMOVE
        courseQuizConfig.quizConfig.id = quizClass.configId
          ? quizClass.configId
          : TemplateNameManager.courseQuizConfigQuizConfigId
        courseQuizConfig.quizConfig.name = quizClass.configName
          ? quizClass.configName
          : TemplateNameManager.courseQuizConfigQuizConfigNAME
        courseQuizConfig.quizConfig.publishPattern = quizClass.publishPattern
        courseQuizConfig.quizConfig.openDissects = quizClass.openDissects
        courseQuizConfig.quizConfig.limitCourseQuizNum = quizClass.limitCourseQuizNum
        courseQuizConfig.quizConfig.allowCourseQuizNum = quizClass.allowCourseQuizNum
        courseQuizConfig.quizConfig.multipleMissScorePattern = quizClass.multipleMissScorePattern
        courseQuizConfig.quizConfig.passScore = quizClass.passScore
        courseQuizConfig.quizConfig.userCoursePaperPublishConfigure = {}
        const userCoursePaperPublishConfigure = courseQuizConfig.quizConfig.userCoursePaperPublishConfigure
        userCoursePaperPublishConfigure.questionCount = quizClass.questionCount
        userCoursePaperPublishConfigure.totalScore = quizClass.totalScore
        userCoursePaperPublishConfigure.questionCountConfigureType = quizClass.questionCountConfigureType
        userCoursePaperPublishConfigure.questionCountPerPeriod = quizClass.questionCountPerPeriod
        userCoursePaperPublishConfigure.timeLengthPerQuestion = quizClass.timeLengthPerQuestion
      }
      //课程通过考核认定
      courseLearningConfig.courseCompleteEvaluateConfig = {}
      courseLearningConfig.courseCompleteEvaluateConfig.courseSchedule = courseLearningType.courseSchedule
      courseLearningConfig.courseCompleteEvaluateConfig.courseQuizPagerStandard =
        courseLearningType.courseQuizPagerStandard
      //  课程评价
      if (courseLearningType.enableAppraisal) {
        courseLearningConfig.courseAppraisalConfig = {}
        courseLearningConfig.courseAppraisalConfig.enableAppraisal = courseLearningType.enableAppraisal
        courseLearningConfig.courseAppraisalConfig.enableCompulsoryAppraisal =
          courseLearningType.enableCompulsoryAppraisal
        courseLearningConfig.courseAppraisalConfig.preconditionCourseSchedule =
          courseLearningType.preconditionCourseSchedule
      }
    }
    jsonObj.chooseCourseLearning?.config?.chooseCourseRule?.config?.secondElectiveMaxPeriod?.forEach((item: any) => {
      if (this.idMap.get(item.id)) {
        item.id = this.idMap.get(item.id)
      }
    })
  }

  //  将考试转换为json
  convertExamLearning(jsonObj: any, createCommodity: MutationCreateTrainClassCommodity) {
    const exam = createCommodity.learningTypeModel.exam
    if (exam.isSelected) {
      jsonObj.examLearning = {}
      const examJson = jsonObj.examLearning
      examJson.id = exam.learningTypeId ? exam.learningTypeId : TemplateNameManager.examLearningId
      examJson.config = {}
      const examJsonConfig = examJson.config
      if (exam.configId) examJsonConfig.operation = exam.operation
      examJsonConfig.id = exam.configId ? exam.configId : TemplateNameManager.examLearningConfigId
      examJsonConfig.name = exam.name
      examJsonConfig.description = exam.description
      examJsonConfig.paperPublishConfigureId = exam.paperPublishConfigureId
      examJsonConfig.allowCount = exam.allowCount
      examJsonConfig.allowStartTime = exam.allowStartTime
      examJsonConfig.allowEndTime = exam.allowEndTime
      examJsonConfig.timeLength = exam.timeLength * 60
      examJsonConfig.qualifiedScore = exam.examPassScore
      examJsonConfig.allowAnswerIfQualified = exam.allowAnswerIfQualified
      examJsonConfig.openDissects = exam.openDissects
      examJsonConfig.questionAgainAnswer = exam.questionAgainAnswer
      examJsonConfig.questionDisplay = exam.questionDisplay
      examJsonConfig.multipleMissScorePattern = exam.multipleMissScorePattern
      examJsonConfig.gradesWhetherHide = exam.gradesWhetherHide
      examJsonConfig.examType = exam.examPattern
      if (exam.assessId || exam.isExamAssessed) {
        examJson.assessSetting = {}
        const examJsonAssessSetting = examJson.assessSetting
        examJsonAssessSetting.id = exam.assessId ? exam.assessId : TemplateNameManager.examLearningAssessId
        examJsonAssessSetting.name = exam.assessName ? exam.assessName : TemplateNameManager.examLearningAssessName
        examJsonAssessSetting.examPassScore = exam.examPassScore
        if (exam.isExamAssessed) {
          examJsonAssessSetting.operation = exam.assessId ? OperationEnum.UPDATE : OperationEnum.CREATE
        } else {
          if ([OperationEnum.UPDATE, OperationEnum.REMOVE].includes(exam.operation)) {
            examJsonAssessSetting.operation = OperationEnum.REMOVE
          }
        }
      }
      //配置前置条件
      // 如果课程不存在且不是移除则不添加前置条件
      if (
        !(
          !createCommodity.learningTypeModel.courseLearning.isSelected &&
          createCommodity.learningTypeModel.courseLearning.operation !== OperationEnum.REMOVE
        ) &&
        (exam.preCondition == 1 || exam.preconditionId)
      ) {
        examJson.precondition = {}
        const examJsonPrecondition = examJson.precondition
        examJsonPrecondition.id = exam.preconditionId
          ? exam.preconditionId
          : TemplateNameManager.examLearningPreconditionId
        examJsonPrecondition.name = exam.preconditionName
          ? exam.preconditionName
          : this.schemeType == 1
          ? TemplateNameManager.examLearningChooseCoursePreconditionName
          : TemplateNameManager.examLearningAutoCoursePreconditionName
        if (exam.preCondition == 1) {
          examJsonPrecondition.operation = exam.preconditionId ? OperationEnum.UPDATE : OperationEnum.CREATE
        } else {
          if ([OperationEnum.UPDATE, OperationEnum.REMOVE].includes(exam.operation)) {
            examJsonPrecondition.operation = OperationEnum.REMOVE
          }
        }
        // examJsonPrecondition.needPassQuiz = createCommodity.learningTypeModel.courseLearning.incorporateCourseQuiz
        // examJsonPrecondition.everyCourseQuizPassScore =
        //   createCommodity.learningTypeModel.courseLearning.eachCourseQuizPassScore
        let defaultReferLearningId = ''
        if (this.schemeType == 1) {
          defaultReferLearningId = TemplateNameManager.chooseCourseLearningID
        } else if (this.schemeType == 2) {
          defaultReferLearningId = TemplateNameManager.autonomousCourseLearningId
        }
        let courseLearning = null
        // 判断是否创建存在课程及考试但修改移除课程
        if (
          createCommodity.learningTypeModel.exam.operation === OperationEnum.UPDATE &&
          createCommodity.learningTypeModel.courseLearning.operation === OperationEnum.REMOVE
        ) {
          // 考试更新，课程移除
          courseLearning = createCommodity.learningTypeModelCopy.courseLearning
          // examJsonPrecondition.operation = OperationEnum.REMOVE
        } else if (
          createCommodity.learningTypeModel.exam.operation === OperationEnum.UPDATE &&
          createCommodity.learningTypeModel.courseLearning.operation === OperationEnum.UPDATE
        ) {
          // 考试和课程都更新
          courseLearning = createCommodity.learningTypeModel.courseLearning
          // examJsonPrecondition.operation = OperationEnum.UPDATE
        } else {
          courseLearning = createCommodity.learningTypeModel.courseLearning
        }
        examJsonPrecondition.referLearningId = courseLearning.learningTypeId
          ? courseLearning.learningTypeId
          : defaultReferLearningId
        // examJsonPrecondition.referLearningId = courseLearning.learningTypeId
        if (this.schemeType == 1) {
          examJsonPrecondition.compulsoryRequirePeriod = courseLearning.compulsoryRequirePeriod
          examJsonPrecondition.electiveRequirePeriod = courseLearning.electiveRequirePeriod
        } else if (this.schemeType == 2) {
          const childOutlines = courseLearning.classification?.childOutlines?.filter(
            (item) => item.operation != OperationEnum.REMOVE
          )
          examJsonPrecondition.compulsoryRequirePeriod = childOutlines?.length
            ? childOutlines.reduce((prev, cur) => {
                return CalculatorObj.add(cur.compulsoryCoursePeriodTotal || 0, prev)
              }, 0) || 0
            : courseLearning.classification.compulsoryCoursePeriodTotal || 0
          examJsonPrecondition.requirePeriod = courseLearning.requirePeriod
          examJsonPrecondition.outlineRequires = []
          const learningKey = 'autonomousCourseLearning'
          if (jsonObj[learningKey] && jsonObj[learningKey].config) {
            const childOutlinesArr = jsonObj[learningKey].config.courseTrainingOutlines
            childOutlinesArr.forEach((item: any) => {
              if (item.assessSetting && item.assessSetting.requirePeriod && item.operation != OperationEnum.REMOVE) {
                examJsonPrecondition.outlineRequires.push({
                  outLineId: item.id,
                  requirePeriod: item.assessSetting.requirePeriod
                })
              }
            })
          }
        }
      }
    }
  }

  //  将练习转换为json
  convertPracticeLearning(jsonObj: any, createCommodity: MutationCreateTrainClassCommodity) {
    const practiceLearning = createCommodity.learningTypeModel.practiceLearning
    if (practiceLearning.isSelected) {
      jsonObj.practiceLearning = {}
      const practiceLearningJson = jsonObj.practiceLearning
      practiceLearningJson.id = practiceLearning.learningTypeId
        ? practiceLearning.learningTypeId
        : TemplateNameManager.practiceLearningId
      practiceLearningJson.config = {}
      if (practiceLearning.configId) practiceLearningJson.config.operation = practiceLearning.operation
      practiceLearningJson.config.id = practiceLearning.configId
        ? practiceLearning.configId
        : TemplateNameManager.practiceConfigId
      practiceLearningJson.config.type = practiceLearning.type
      practiceLearningJson.config.openDissects = practiceLearning.openDissects
      practiceLearningJson.config.multipleQuestionMissScorePatterns = practiceLearning.multipleQuestionMissScorePatterns
      if (practiceLearning.type == 1 || practiceLearning.type == 3) {
        practiceLearningJson.config.questionSource = {}
        if (practiceLearning.type == 1) {
          practiceLearningJson.config.questionSource.libraryIds = practiceLearning.libraryIds
        }
        if (practiceLearning.type == 3) {
          practiceLearningJson.config.questionSource.paperPublishConfigureId =
            createCommodity.learningTypeModel.exam.paperPublishConfigureId
        }
      }
    }
  }

  //  将兴趣课转换为json
  convertInterestCourseLearning(jsonObj: any, createCommodity: MutationCreateTrainClassCommodity, isUpdate = false) {
    const interestCourse = createCommodity.learningTypeModel.interestCourse
    if (interestCourse.isSelected) {
      jsonObj.interestCourseLearning = {}
      const interestCourseLearningJson = jsonObj.interestCourseLearning
      interestCourseLearningJson.id = interestCourse.learningTypeId
        ? interestCourse.learningTypeId
        : TemplateNameManager.interestCourseLearningId
      interestCourseLearningJson.config = {}
      if (interestCourse.configId) interestCourseLearningJson.config.operation = interestCourse.operation
      interestCourseLearningJson.config.id = interestCourse.configId
        ? interestCourse.configId
        : TemplateNameManager.interestCourseConfigId
      if (interestCourse.classification.childOutlines && interestCourse.classification.childOutlines.length == 0) {
        interestCourse.classification.id = TemplateNameManager.NOCourseTrainingOutlinesId
        interestCourse.classification.name = TemplateNameManager.NOCourseTrainingOutlinesName
        interestCourseLearningJson.config.courseTrainingOutlines = [
          this.getClassFicationJsonString(interestCourse.classification, isUpdate, true)
        ]
      } else {
        interestCourseLearningJson.config.courseTrainingOutlines = this.getClassFicationJsonString(
          interestCourse.classification,
          isUpdate,
          true
        ).childOutlines
      }
    }
  }

  //将类转换为大纲json
  private getClassFicationJsonString(item: Classification, isUpdate = false, isInterest = false, index = 0) {
    const classification: any = {}
    classification.name = item.name
    console.log('loopNum=', this.loopNum, '当前大纲是=', item)
    if (item.id.indexOf(Classification.classificationIdPre) !== -1) {
      this.idMap.set(item.id, TemplateNameManager.courseTrainingOutlinesPrefix + this.pad(this.loopNum))
    }
    classification.id = item.id
      ? item.id.indexOf(Classification.classificationIdPre) == -1
        ? item.id
        : TemplateNameManager.courseTrainingOutlinesPrefix + this.pad(this.loopNum)
      : TemplateNameManager.courseTrainingOutlinesPrefix + this.pad(this.loopNum)
    classification.sort = index
    classification.coursePackageId = item.coursePackageId
    if (isUpdate && item.operation) classification.operation = item.operation
    if (!isInterest) {
      if (this.schemeType == 1) {
        classification.category = item.category
      }
      if (this.schemeType == 2) {
        classification.compulsoryCourseIdList = item.compulsoryCourseIdList
        if (item.assessSetting.requirePeriod && item.assessSetting.requirePeriod > 0) {
          classification.assessSetting = {}
          classification.assessSetting.id = item.assessSetting.id
            ? item.assessSetting.id
            : TemplateNameManager.courseTrainingOutlinesAssessPrefix + this.pad(this.loopNum)
          // if (classification.assessSetting.id.indexOf(TemplateNameManager.courseTrainingOutlinesAssessPrefix) != -1) {
          if (item.operation != OperationEnum.REMOVE) {
            this.convertJsonObj.autonomousCourseLearning.assessSetting.relateAssessIds.push(
              classification.assessSetting.id
            )
          }
          // }
          classification.assessSetting.rangeKey = item.assessSetting.rangeKey
          classification.assessSetting.requirePeriod = item.assessSetting.requirePeriod
          classification.assessSetting.name = item.assessSetting.name
            ? item.assessSetting.name
            : TemplateNameManager.courseTrainingOutlinesAssessName
        }
      }
    }
    classification.childOutlines = []
    if (item.childOutlines) {
      item.childOutlines.forEach((tmpItem: any, itemIndex: number) => {
        this.loopNum++
        classification.childOutlines.push(this.getClassFicationJsonString(tmpItem, isUpdate, isInterest, itemIndex))
      })
    }
    return classification
  }

  /**
   * 学习心得转换
   * @param jsonObj 转换成需要的模型
   * @param createCommodity 要转换的模型
   */
  convertLearningExperience(jsonObj: any, createCommodity: MutationCreateTrainClassCommodity, isUpdate: boolean) {
    const learningExperience = createCommodity.learningTypeModel.learningExperience
    if (learningExperience.isSelected) {
      jsonObj.learningExperienceLearning = {}
      jsonObj.learningExperienceLearning.id = learningExperience.learningTypeId
        ? learningExperience.learningTypeId
        : TemplateNameManager.schemeLearningExperienceLearningId
      if (learningExperience.isExamine) {
        jsonObj.learningExperienceLearning.assessSetting = {}
        jsonObj.learningExperienceLearning.assessSetting.id = learningExperience.assessId
          ? learningExperience.assessId
          : TemplateNameManager.learningExperienceLearningSettingId
        jsonObj.learningExperienceLearning.assessSetting.name = learningExperience.assessName
          ? learningExperience.assessName
          : TemplateNameManager.learningExperienceLearningAssessSettingName
        jsonObj.learningExperienceLearning.assessSetting.lessParticipateLearningExperienceTopicCount =
          learningExperience.joinCount
        jsonObj.learningExperienceLearning.assessSetting.eachLearningExperienceTopicPassScore = learningExperience.score
      }

      jsonObj.learningExperienceLearning.config = {}
      jsonObj.learningExperienceLearning.config.showName = learningExperience.experienceName
      this.loopNum = 1
      jsonObj.learningExperienceLearning.config.id = learningExperience.configId
        ? learningExperience.configId
        : TemplateNameManager.schemeLearningExperienceConfigId + this.pad(this.loopNum)
      if (learningExperience.configId) {
        jsonObj.learningExperienceLearning.config.operation = learningExperience.operation
      }
      //   班级学习心得
      jsonObj.learningExperienceLearning.config.schemeParticipateRequired = {}
      jsonObj.learningExperienceLearning.config.schemeParticipateRequired.isRequiredFirst = true
      jsonObj.learningExperienceLearning.config.schemeParticipateRequired.requiredType =
        learningExperience.classCondition ? 1 : 2
      if (learningExperience.classExperienceList.length) {
        jsonObj.learningExperienceLearning.config.schemeLearningExperienceTopics = []
        learningExperience.classExperienceList.forEach((item) => {
          this.loopNum++
          const schemeLearningExperienceTopic = {} as any
          this.learningExperienceTopics(schemeLearningExperienceTopic, item, jsonObj, isUpdate)
          schemeLearningExperienceTopic.passScore = learningExperience.score
          jsonObj.learningExperienceLearning.config.schemeLearningExperienceTopics.push(schemeLearningExperienceTopic)
        })
      } else {
        jsonObj.learningExperienceLearning.config.schemeParticipateRequired.requiredType = 1
      }
      //   课程学习心得
      jsonObj.learningExperienceLearning.config.courseParticipateRequired = {}
      jsonObj.learningExperienceLearning.config.courseParticipateRequired.isRequiredFirst = true
      jsonObj.learningExperienceLearning.config.courseParticipateRequired.requiredType =
        learningExperience.courseCondition ? 1 : 2
      if (learningExperience.courseExperienceList.length) {
        jsonObj.learningExperienceLearning.config.courseLearningExperienceTopics = []
        learningExperience.courseExperienceList.forEach((item) => {
          this.loopNum++
          const courseLearningExperienceTopic = {} as any
          this.learningExperienceTopics(courseLearningExperienceTopic, item, jsonObj, isUpdate)
          courseLearningExperienceTopic.passScore = learningExperience.score
          // 目前只关联一个课程
          courseLearningExperienceTopic.courses = [
            {
              outlineId:
                item.courseList[0].outlineId.indexOf(Classification.classificationIdPre) !== -1
                  ? this.idMap.get(item.courseList[0].outlineId)
                  : item.courseList[0].outlineId || TemplateNameManager.NOCourseTrainingOutlinesId,
              packageId: item.courseList[0].coursePackageId,
              courseId: item.courseList[0].courseId
            }
          ]
          jsonObj.learningExperienceLearning.config.courseLearningExperienceTopics.push(courseLearningExperienceTopic)
        })
      } else {
        jsonObj.learningExperienceLearning.config.courseParticipateRequired.requiredType = 1
      }
    }
  }

  /**
   * 学习心得item转换
   * @param experienceItem
   * @param item
   * @param jsonObj
   */
  learningExperienceTopics(experienceItem: any, item: ExperienceItem, jsonObj: any, isUpdate: boolean) {
    experienceItem.id = item.id.includes(ExperienceItem.experienceIdPre)
      ? TemplateNameManager.schemeLearningExperienceConfigId + this.pad(this.loopNum)
      : item.id
    experienceItem.experienceTopicName = item.theme || ''
    if (item.content) experienceItem.descriptionContent = item.content
    experienceItem.participateTimeType = item.joinTimeType.current
    if (item.joinTimeType.equal(JoinTimeTypeEnum.designate_time)) {
      experienceItem.startTime = item.joinTime[0]
      experienceItem.endTime = item.joinTime[1]
    } else {
      // 同班级时间
      experienceItem.startTime = jsonObj.trainingBeginDate
      experienceItem.endTime = jsonObj.trainingEndDate
    }
    experienceItem.participateType = item.answerType.current
    experienceItem.submitLimitNum = item.submitLimitNum
    experienceItem.auditType = item.checkType.current
    experienceItem.submitLimitCount = item.checkType.equal(CheckTypeEnum.auto)
      ? -1
      : item.submitCountType
      ? item.submitCount
      : -1
    experienceItem.totalScore = item.score
    experienceItem.isRequired = item.isRequired
    if (isUpdate) {
      experienceItem.operation = item.operation
    }
  }

  /**
   * 获取学习心得
   * @param experienceItem
   * @param item
   */
  getLearningExperienceItem(experienceItem: any, item: ExperienceItem) {
    item.theme = experienceItem.experienceTopicName || ''
    item.content = experienceItem.descriptionContent
    item.joinTimeType = new JoinTimeType(Number(experienceItem.participateTimeType))
    item.joinTime = [experienceItem.startTime, experienceItem.endTime]
    item.answerType = new AnswerType(Number(experienceItem.participateType))
    item.submitLimitNum = experienceItem.submitLimitNum
    item.checkType = new CheckType(Number(experienceItem.auditType))
    item.submitCountType = experienceItem.submitLimitCount !== -1
    item.submitCount = experienceItem.submitLimitCount !== -1 ? experienceItem.submitLimitCount : 0
    item.score = experienceItem.totalScore
    item.isRequired = experienceItem.isRequired
    item.operation = OperationEnum.UPDATE
    item.isInfoShow = false
  }

  pad(num: number, n = 6) {
    let len = num.toString().length
    let newNum = num + ''
    while (len < n) {
      newNum = '0' + newNum
      len++
    }
    return newNum
  }
}

export default new TrainClassConfigJsonManager()
