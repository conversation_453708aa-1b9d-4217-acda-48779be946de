<template>
  <div v-if="$hasPermission('trainingResultMixListQuery')" desc="期别培训成果查询" actions="created">
    <el-card shadow="never" class="m-card">
      <!--条件查询-->
      <el-row :gutter="24" class="m-query f-mt10">
        <el-form :inline="true" label-width="auto">
          <el-col :span="6">
            <el-form-item label="期别名称">
              <el-input v-model="trainingResultsManage.params.periodName" clearable placeholder="请输入期别名称" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="期别编号">
              <el-input v-model="trainingResultsManage.params.periodNo" clearable placeholder="请输入期别编号" />
            </el-form-item>
          </el-col>
          <el-col class="f-fr" :span="4">
            <el-form-item class="f-tr">
              <el-button type="primary" @click="doQuery">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <!--表格-->
      <el-table ref="table" stripe :data="tableData" v-loading="uiLoading.tableLoading" class="m-table">
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="期别名称" width="300" fixed="left" prop="name" show-overflow-tooltip></el-table-column>
        <el-table-column label="期别编号" width="240" prop="no" show-overflow-tooltip></el-table-column>
        <el-table-column label="已报名人数" min-width="110" align="center" prop="signedNumber"></el-table-column>
        <el-table-column label="合格人数" min-width="110" align="center" prop="qualifiedNum"></el-table-column>
        <el-table-column label="报名起止时间" min-width="220">
          <template #default="scope">
            <p>
              <el-tag type="info" size="mini">起始</el-tag>
              {{ scope.row.applicationData.begin || '--' }}
            </p>
            <p>
              <el-tag type="info" size="mini">结束</el-tag>
              {{ scope.row.applicationData.end || '--' }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="培训起止时间" min-width="220">
          <template #default="scope">
            <p>
              <el-tag type="info" size="mini">起始</el-tag>
              {{ scope.row.trainingTime.begin || '--' }}
            </p>
            <p>
              <el-tag type="info" size="mini">结束</el-tag>
              {{ scope.row.trainingTime.end || '--' }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button
              v-if="$hasPermission('viewLearningStatistic')"
              desc="查看期别学员学习明细权限"
              actions="@/unit-share/network-school/statistic/learning-statistic"
              type="text"
              @click="viewLearningStatistic(scope.row)"
            >
              查看学员学习明细
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </el-card>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import TrainingResultsManage from '@api/service/management/implement/TrainingResultsManage'
  import PeriodResult from '@api/service/management/implement/models/PeriodResult'
  import { ElTable } from 'element-ui/types/table'

  @Component
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.pageSearch, this.pageSearch)
    }

    /**
     * 培训形式
     */
    @Prop({
      type: String,
      default: ''
    })
    trainingMode: string

    @Prop({
      type: TrainingResultsManage,
      required: true
    })
    trainingResultsManage: TrainingResultsManage

    page: UiPage

    tableData: PeriodResult[] = []
    /**
     * ui loading效果
     */
    uiLoading = {
      tableLoading: false
    }

    /**
     * 分页查询
     */
    async pageSearch() {
      await this.trainingResultsManage.queryList(this.page)
      this.tableData = this.trainingResultsManage.list
      ;(this.$refs['table'] as ElTable).doLayout()
    }

    async doQuery() {
      this.uiLoading.tableLoading = true
      this.page.pageNo = 1
      await this.pageSearch()
      this.uiLoading.tableLoading = false
    }

    async reset() {
      this.trainingResultsManage.params.periodName = undefined
      this.trainingResultsManage.params.periodNo = undefined
      this.doQuery()
    }

    viewLearningStatistic(row: PeriodResult) {
      this.$router.push(
        `/statistic/learning-statistic?periodId=${row.id}&periodName=${row.name}&trainingMode=${this.trainingMode}`
      )
    }

    created() {
      this.doQuery()
    }
  }
</script>
