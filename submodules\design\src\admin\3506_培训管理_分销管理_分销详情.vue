<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--详情-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">详情</el-button>
        <el-drawer title="详情" :visible.sync="dialog2" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-plr30">
              <el-form-item label="培训方案名称：">
                读取方案名称名称名称名称名称名称名称名称名称名称名称名称名称名称名称名称名称名称名称名称名称名称名称
              </el-form-item>
              <el-form-item label="培训方案类型：" class="is-text">培训班-选课规则 </el-form-item>
              <el-form-item label="培训属性值：" class="is-text">
                <el-tag type="warning" size="small" class="f-mr5 f-mb5">2023年度</el-tag>
                <el-tag size="small" class="f-mr5 f-mb5">建设行业</el-tag>
                <el-tag size="small" class="f-mr5 f-mb5">公需科目</el-tag>
                <el-tag size="small" class="f-mr5 f-mb5">二级注册建造师</el-tag>
                <el-tag size="small" class="f-mr5 f-mb5">建筑工程</el-tag>
                <el-tag type="success" size="small" class="f-mr5 f-mb5">福州市鼓楼区</el-tag>
              </el-form-item>
              <el-form-item label="方案价格：" class="is-text"><i class="f-mr5">¥</i>100</el-form-item>
              <el-form-item label="报名起止时间：" class="is-text">
                2023-08-26 00:00:00<i class="f-mlr10">~</i>2023-12-30 00:00:00
              </el-form-item>
              <el-form-item label="学习起止时间：" class="is-text">
                2023-08-26 00:00:00<i class="f-mlr10">~</i>2023-12-30 00:00:00
              </el-form-item>
              <el-form-item label="授权供应商：" class="is-text">
                <el-tooltip effect="dark" placement="right" popper-class="m-tooltip">
                  <div class="f-vm">读取供应商简称，鼠标悬停显示全称<i class="el-icon-document f-ml5 f-c9"></i></div>
                  <div slot="content">
                    <p>北京爱迪科森教育科技股份有限公司</p>
                    <p>913101175821220588</p>
                  </div>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="合作状态：">
                <el-tag type="success" size="small">合作中</el-tag>
                <!--<el-tag>未开始</el-tag>-->
                <!--<el-tag type="danger">中止合作</el-tag>-->
                <!--<el-tag type="info">已结束</el-tag>-->
                <!--<el-tag type="warning">即将到期</el-tag>-->
              </el-form-item>
              <el-form-item label="合作周期：" class="is-text">
                2023-08-26 00:00:00<i class="f-mlr10">~</i>2023-12-30 00:00:00
              </el-form-item>
              <el-form-item class="is-text">
                <div slot="label">
                  <span class="f-vm">指导价格类型</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>
                        设置方案授权给供应商进行分销指导价格。供应商可基于指导价格设置方案的分销价格并授权给分销商进行推广，支持按固定价格和按价格区间两种类型设置。
                      </p>
                      <p>① 按照固定价格：方案的分销价格不能低于指导价格。</p>
                      <p>② 按价格区间设置：方案的分销价格不能低于指导价格最低价，不能高于分销价格的最高价。</p>
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                按价格区间设置
              </el-form-item>
              <el-form-item label="指导价格：" class="is-text">
                <i class="f-mr5">¥</i>100<i class="f-mlr10">~</i><i class="f-mr5">¥</i>900
                <span class="f-ml5">元 / 人</span>
              </el-form-item>
              <el-form-item class="is-text">
                <div slot="label">
                  <span class="f-vm">销售开始日期</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      设置方案授权给指定供应商进行分销的开始时间。如分销有效日期未开始，方案在网校开放报名时间已开始，此时该方案不能进行分销。如需开展方案的分销，请开启分销开始日期。
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                2023-08-26 00:00:00
              </el-form-item>
              <el-form-item class="is-text">
                <div slot="label">
                  <span class="f-vm">销售关闭日期</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      设置方案授权给指定供应商进行分销的开始时间。如分销有效日期未开始，方案在网校开放报名时间已开始，此时该方案不能进行分销。如需开展方案的分销，请开启分销开始日期。
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                2023-08-26 00:00:00
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>关闭</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        num: '100.00',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: true,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
