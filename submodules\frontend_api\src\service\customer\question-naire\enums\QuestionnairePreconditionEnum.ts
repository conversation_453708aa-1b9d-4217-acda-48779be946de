import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 问卷前置条件
 */
export enum QuestionnairePreconditionEnum {
  /**
   * 选课规则 完成线上考核
   */
  online_assess = 1,
  /**
   * 自主选课 完成线上考核 整体要求学时+课程大纲学时要求
   */
  hours_assess = 2,
  /**
   * 要求当前时间晚于期别培训的结束时间
   * 等同于完成期别课程
   */
  time_assess = 3,
  /**
   * 无
   */
  none = 4
}
class QuestionnairePrecondition extends AbstractEnum<QuestionnairePreconditionEnum> {
  static enum = QuestionnairePreconditionEnum

  constructor(status?: QuestionnairePreconditionEnum) {
    super()
    this.current = status
    this.map.set(QuestionnairePreconditionEnum.online_assess, '完成线上考核')
    this.map.set(QuestionnairePreconditionEnum.hours_assess, '整体要求学时+课程大纲学时要求')
    this.map.set(QuestionnairePreconditionEnum.time_assess, '要求当前时间晚于期别培训的结束时间')
    this.map.set(QuestionnairePreconditionEnum.none, '无')
  }
}
export default QuestionnairePrecondition
