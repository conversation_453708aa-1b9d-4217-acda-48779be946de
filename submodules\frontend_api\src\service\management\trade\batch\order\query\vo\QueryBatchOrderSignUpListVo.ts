import { CollectiveSignQueryRequest, MetaProperty, MetaSchema } from '@api/ms-gateway/ms-collectivesign-v1'

/**
 * @description 【集体报名订单】报名列表查询参数
 */
class QueryBatchOrderSignUpListVo {
  /**
   * 批次单id
   */
  batchOrderNo = ''

  /**
   * 证件号
   */
  userAccount = ''

  /**
   * 培训方案名称
   */
  schemeName = ''

  to(): CollectiveSignQueryRequest {
    const to = new CollectiveSignQueryRequest()
    to.collectiveSignupNo = this.batchOrderNo
    if (this.schemeName || this.userAccount) {
      to.metaPropertyList = new Array<MetaProperty>()
      // 查询培训方案名称
      const querySchemeName = new MetaProperty()
      querySchemeName.key = 'signUp_schemeName'
      querySchemeName.value = this.schemeName ?? ''
      // 查询证件号
      const queryUserAccount = new MetaProperty()
      queryUserAccount.key = 'userInfo_idCard'
      queryUserAccount.value = this.userAccount ?? ''
      if (!this.schemeName && this.userAccount) {
        to.metaPropertyList = new Array<MetaProperty>()
        to.metaPropertyList.push(queryUserAccount)
      } else if (this.schemeName && !this.userAccount) {
        to.metaPropertyList = new Array<MetaProperty>()
        to.metaPropertyList.push(querySchemeName)
      } else {
        to.metaPropertyList = new Array<MetaProperty>()
        to.metaPropertyList.push(queryUserAccount, querySchemeName)
      }
    } else {
      to.metaPropertyList = undefined
    }
    return to
  }
}

export default QueryBatchOrderSignUpListVo
