import { TerminalCreateRequest } from '@api/ms-gateway/ms-trade-configuration-v1'
import { TerminalResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import Terminal from '@api/service/common/enums/trade/Terminal'
import ReceiveAccountVo from './ReceiveAccountVo'

class TerminalVo {
  // 终端代码
  terminalCode = ''
  // 终端名称
  terminalName = ''
  // 终端所属收款账号列表
  receiveAccountList: Array<ReceiveAccountVo> = new Array<ReceiveAccountVo>()

  hasReceiveAccountList() {
    return this.receiveAccountList?.length > 0
  }

  /**
   * 移除收款账号
   * @param receiveAccountId
   */
  remove(receiveAccountId: string) {
    const index = this.receiveAccountList.findIndex(receive => receive.id === receiveAccountId)
    if (index > -1) {
      this.receiveAccountList.splice(index, 1)
    }
  }

  /**
   * 添加收款账号
   * @param receiveAccount
   */
  add(receiveAccount: ReceiveAccountVo) {
    const index = this.receiveAccountList.findIndex(receive => receive.id === receiveAccount.id)
    if (index === -1) {
      this.receiveAccountList.push(receiveAccount)
    }
  }

  static from(res: TerminalResponse) {
    const terminal = new TerminalVo()
    terminal.terminalCode = res.terminalCode
    terminal.terminalName = Terminal[res.terminalCode]
    terminal.receiveAccountList = new Array<ReceiveAccountVo>()
    return terminal
  }

  static to(terminal: TerminalVo) {
    const terminalCreateRequest = new TerminalCreateRequest()
    terminalCreateRequest.terminalCode = terminal.terminalCode
    terminalCreateRequest.receiveAccountIdList = terminal.receiveAccountList?.map(receive => receive.id)
    terminalCreateRequest.isClosed = false
    return terminalCreateRequest
  }
}
export default TerminalVo
