import { ResponseStatus } from '@hbfe/common'
import LearningModule from '@api/service/customer/learning/LearningModule'
import ApplyStudentChooseCourseSceneProof from '@api/service/customer/learning/scene/proofs/ApplyStudentChooseCourseSceneProof'

/**
 * @description 选课凭证（token）获取
 */
class MutationEnterChooseCourse {
  token = ''

  /**
   * 获取选课Token
   * @param qualificationId === studentNo - 获取选课凭证（token）入参
   * @return {ResponseStatus}
   */
  async queryChooseCourseToken(qualificationId: string, learningId: string): Promise<ResponseStatus> {
    // 通过网关层获取选课凭证（token）后不缓存在本业务状态层 => 选课凭证（token）存取在token管理模块上 - LWF
    const applyStudentChooseCourseSceneProof = new ApplyStudentChooseCourseSceneProof(qualificationId, learningId)
    const enterChooseCourseToken = await LearningModule.sceneFactory
      .studentChooseCourseScene(applyStudentChooseCourseSceneProof)
      .applyEnterTicket()
    this.token = enterChooseCourseToken.ticket
    return Promise.resolve(new ResponseStatus(200, ''))
  }
}

export default new MutationEnterChooseCourse()
