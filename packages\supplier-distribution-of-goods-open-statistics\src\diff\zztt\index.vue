//供应商分销商品开通统计

<template>
  <div
    class="f-p15"
    v-if="$hasPermission('querySupplieDistribution')"
    query
    desc="查询分销开通统计"
    actions="search,@BizDistributorSelect,@BizPortalSelect"
  >
    <el-card shadow="never" class="m-card f-mb15">
      <hb-search-wrapper class="m-query is-border-bottom" @reset="resetSearch">
        <el-form-item label="分销商品">
          <el-input
            v-model="distributionGoodsOpeningStatistics.param.distributedTradeName"
            clearable
            placeholder="请输入分销商品名称"
          />
        </el-form-item>
        <el-form-item label="分销商">
          <biz-distributor-select
            v-model="distributionGoodsOpeningStatistics.param.distributorId"
            :name="distributionGoodsOpeningStatistics.param.distributorName"
          ></biz-distributor-select>
        </el-form-item>
        <el-form-item label="推广门户简称">
          <biz-portal-select
            :disabled="distributionGoodsOpeningStatistics.param.isPortalData"
            v-model="distributionGoodsOpeningStatistics.param.portalPromoteId"
            :name="distributionGoodsOpeningStatistics.param.portalPromoteTheName"
          ></biz-portal-select>
        </el-form-item>
        <el-form-item label="报名时间">
          <el-date-picker
            v-model="dates"
            type="datetimerange"
            range-separator="至"
            start-placeholder="报名成功时间"
            end-placeholder="报名成功时间"
            :value-format="'yyyy-MM-dd HH:mm:ss'"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="分销价格">
          <div class="f-flex">
            <el-input
              v-model="distributionGoodsOpeningStatistics.param.distributionPrice.min"
              placeholder="请输入价格"
              class="input-num f-flex-sub"
            />
            <i class="f-mlr10">~</i>
            <el-input
              v-model="distributionGoodsOpeningStatistics.param.distributionPrice.max"
              placeholder="请输入价格"
              class="input-num f-flex-sub"
            />
          </div>
        </el-form-item>
        <el-form-item label="剔除培训方案">
          <biz-remove-training-plan
            v-model="removePlan"
            placeholder="请选择不纳入统计的培训方案"
          ></biz-remove-training-plan>
        </el-form-item>
        <el-form-item>
          <el-checkbox
            label="查看非门户推广数据"
            name="type"
            v-model="distributionGoodsOpeningStatistics.param.isPortalData"
          ></el-checkbox>
        </el-form-item>
        <template slot="actions">
          <el-button type="primary" @click="init">查询</el-button>
          <el-button
            @click="exportData"
            v-if="$hasPermission('exportDataSupplieDistribution')"
            mutaion
            desc="导出分销开通统计"
            actions="exportData"
            >导出列表数据
          </el-button>
        </template>
      </hb-search-wrapper>
      <!--操作栏-->
      <div class="f-mt20">
        <el-alert type="warning" :closable="false" class="m-alert f-clear">
          <div class="f-c6 f-fl">
            搜索结果合计：当前分销推广净开通
            <span class="f-fb f-co">{{ distributionGoodsOpeningStatistics.staticData.netTurnOn }}</span> 人次，成交总额
            <span class="f-fb f-co">¥ {{ distributionGoodsOpeningStatistics.staticData.transactionTotal }}</span>
          </div>
          <div class="f-fr f-csp f-flex f-align-center" @click="statisticalCaliberDeclarationDrawer.openDrawer()">
            <i class="el-icon-info f-f16 f-mr5"></i>统计说明
          </div>
        </el-alert>
      </div>
      <!--表格-->
      <el-table
        stripe
        :data="distributionGoodsOpeningStatistics.list"
        border
        class="m-table f-mt10"
        ref="distributionGoodsOpeningStatisticsTable"
        :span-method="objectSpanMethod"
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left">
          <template v-slot="{ row }">{{ row.no }}</template>
        </el-table-column>
        <el-table-column label="分销商品" min-width="160" fixed="left">
          <template v-slot="{ row }">{{ row.distributorInformation.goodsName }}</template>
        </el-table-column>
        <el-table-column label="培训属性" min-width="160">
          <template v-slot="{ row }">
            <p class="f-mb5">{{ getindustry(row) }}</p>
            <div class="f-c9 f-f12">
              <p v-for="(item, index) in getProcessedTrainingAttributes(row)" :key="index">
                {{ item }}
              </p>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="分销商" min-width="160">
          <template v-slot="{ row }">
            {{ row.distributorInformation.distributorsInfoIdName }}
            <el-tag class="f-ml10" type="primary" effect="dark" size="mini">
              {{ getDistributorRegistration(row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="定价方式" min-width="160" align="center">
          <template v-slot="{ row }">
            <div>
              <el-tag type="warning" size="mini" class="f-ml5 f-mr5">{{ getPricingMethodn(row) }}</el-tag>
              ￥{{ row.price }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="合计" header-align="center">
          <el-table-column label="开通" min-width="90" align="right">
            <template v-slot="{ row }">
              {{ row.total.open }}
            </template>
          </el-table-column>
          <el-table-column label="退班" min-width="90" align="right">
            <template v-slot="{ row }">
              {{ row.total.return }}
            </template>
          </el-table-column>
          <el-table-column label="换入(换班)" min-width="110" align="right">
            <template v-slot="{ row }">
              {{ row.total.swapIntoAClass }}
            </template>
          </el-table-column>
          <el-table-column label="换出(换班)" min-width="110" align="right">
            <template v-slot="{ row }">
              {{ row.total.swapOutOfClass }}
            </template>
          </el-table-column>
          <el-table-column label="净开通" min-width="90" align="right">
            <template v-slot="{ row }">
              {{ row.total.count }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="个人缴费" header-align="center">
          <el-table-column label="线上支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template v-slot="{ row }">
                {{ row.individualOnline.open }}
              </template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template v-slot="{ row }">
                {{ row.individualOnline.return }}
              </template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="110" align="right">
              <template v-slot="{ row }">
                {{ row.individualOnline.swapIntoAClass }}
              </template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="110" align="right">
              <template v-slot="{ row }">
                {{ row.individualOnline.swapOutOfClass }}
              </template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template v-slot="{ row }">
                {{ row.individualOnline.count }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
        <el-table-column label="集体报名" header-align="center">
          <el-table-column label="线上支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template v-slot="{ row }">
                {{ row.collectivelyOnline.open }}
              </template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template v-slot="{ row }">
                {{ row.collectivelyOnline.return }}
              </template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="110" align="right">
              <template v-slot="{ row }">
                {{ row.collectivelyOnline.swapIntoAClass }}
              </template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="110" align="right">
              <template v-slot="{ row }">
                {{ row.collectivelyOnline.swapOutOfClass }}
              </template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template v-slot="{ row }">
                {{ row.collectivelyOnline.count }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="线下支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template v-slot="{ row }">
                {{ row.collectivelyOffline.open }}
              </template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template v-slot="{ row }">
                {{ row.collectivelyOffline.return }}
              </template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="110" align="right">
              <template v-slot="{ row }">
                {{ row.collectivelyOffline.swapIntoAClass }}
              </template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="110" align="right">
              <template v-slot="{ row }">
                {{ row.collectivelyOffline.swapOutOfClass }}
              </template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template v-slot="{ row }">
                {{ row.collectivelyOffline.count }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
        <el-table-column label="导入开通" header-align="center">
          <el-table-column label="线下支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template v-slot="{ row }">
                {{ row.importOffline.open }}
              </template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template v-slot="{ row }">
                {{ row.importOffline.return }}
              </template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="110" align="right">
              <template v-slot="{ row }">
                {{ row.importOffline.swapIntoAClass }}
              </template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="110" align="right">
              <template v-slot="{ row }">
                {{ row.importOffline.swapOutOfClass }}
              </template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template v-slot="{ row }">
                {{ row.importOffline.count }}
              </template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table>

      <!--分页-->
      <hb-pagination :page="page" v-bind="page" />
    </el-card>

    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务查看，分销商品开通统计！</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
    <statistical-caliber-declaration-drawer
      ref="statisticalCaliberDeclarationDrawer"
      :search-data="distributionGoodsOpeningStatisticsSearchData"
      :field-data="distributionGoodsOpeningStatisticsFieldData"
    />
  </div>
</template>

<script lang="ts">
  import StatisticalCaliberDeclarationDrawer from '@hbfe/jxjy-admin-components/src/statistical-caliber-declaration-drawer/index.vue'
  import {
    distributionGoodsOpeningStatisticsFieldData,
    distributionGoodsOpeningStatisticsSearchData
  } from '@hbfe/jxjy-admin-components/src/statistical-caliber-declaration-drawer/statisticalExplanatoryData'
  import DistributionGoodsOpeningStatistics from '@api/service/diff/management/zztt/statistical-report/DistributionGoodsOpeningStatistics/DistributionGoodsOpeningStatistics'
  import DistributionGoodsOpeningStatisticsItem from '@api/service/management/statisticalReport/DistributionGoodsOpeningStatistics/model/DistributionGoodsOpeningStatisticsItem'
  import DistributionGoodsOpeningStatisticsParams from '@api/service/diff/management/zztt/statistical-report/DistributionGoodsOpeningStatistics/model/DistributionGoodsOpeningStatisticsParams'
  import { UiPage } from '@hbfe/common'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import { ElTable } from 'element-ui/types/table'
  import { Component, Ref, Vue } from 'vue-property-decorator'

  @Component({
    components: {
      StatisticalCaliberDeclarationDrawer,
      BizDistributorSelect,
      BizPortalSelect
    }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.search, this.search)
    }

    // 统计口径说明抽屉ref
    @Ref('statisticalCaliberDeclarationDrawer') statisticalCaliberDeclarationDrawer: StatisticalCaliberDeclarationDrawer
    @Ref('distributionGoodsOpeningStatisticsTable') distributionGoodsOpeningStatisticsTable: ElTable

    // 分销商品开通统计——搜索字段说明
    get distributionGoodsOpeningStatisticsSearchData() {
      return distributionGoodsOpeningStatisticsSearchData
    }

    // 分销商品开通统计——列表字段说明
    get distributionGoodsOpeningStatisticsFieldData() {
      return distributionGoodsOpeningStatisticsFieldData
    }

    page: UiPage // 分页
    //导出成功弹窗
    exportSuccessVisible = false
    // 分销商品开通统计 模型
    distributionGoodsOpeningStatistics = new DistributionGoodsOpeningStatistics()
    dates: Array<string> = []
    //  剔除培训方案
    removePlan: string[] = []

    spanList: { rowspan: number; colspan: number }[] = []

    async created() {
      await this.init()
    }

    /**
     * 初始化
     */
    async init() {
      this.page.pageNo = 1
      await this.search()
    }

    getProcessedTrainingAttributes(row: DistributionGoodsOpeningStatisticsItem) {
      const arr = row.distributorInformation.trainingAttributes
      const filteredArr = arr.filter((item) => !item.includes('行业'))
      return filteredArr
    }

    getindustry(row: DistributionGoodsOpeningStatisticsItem) {
      const arr = row.distributorInformation.trainingAttributes
      const industry = arr.find((item) => item.includes('行业'))
      return industry
    }

    /**
     * 计算合并行方法
     */
    calculationSpan() {
      this.spanList = []
      this.distributionGoodsOpeningStatistics.list.map((res) => {
        if (res.colspan > 0) {
          this.spanList.push({
            rowspan: res.colspan,
            colspan: 1
          })
        } else {
          this.spanList.push({ rowspan: 0, colspan: 0 })
        }
      })
    }

    /**
     * 搜索列表
     */
    async search() {
      if (this.dates?.length) {
        this.distributionGoodsOpeningStatistics.param.registrationPeriod.begin = this.dates[0]
        this.distributionGoodsOpeningStatistics.param.registrationPeriod.end = this.dates[1]
      } else {
        this.distributionGoodsOpeningStatistics.param.registrationPeriod.begin = ''
        this.distributionGoodsOpeningStatistics.param.registrationPeriod.end = ''
      }
      this.distributionGoodsOpeningStatistics.param.excludeCommodityIdList = this.removePlan
      console.log(this.removePlan, ' 剔除培训方案')
      await this.distributionGoodsOpeningStatistics.queryList(this.page)
      await this.distributionGoodsOpeningStatistics.queryTotalStatic()

      this.$nextTick(() => {
        this.calculationSpan()
        this.distributionGoodsOpeningStatisticsTable.doLayout()
      })
    }

    getDistributorRegistration(row: DistributionGoodsOpeningStatisticsItem) {
      return row.distributorInformation.distributorRegistration
    }

    getPricingMethodn(row: DistributionGoodsOpeningStatisticsItem) {
      return row.pricingMethod
    }

    /**
     * 导出数据
     */
    async exportData() {
      try {
        if (this.dates?.length) {
          this.distributionGoodsOpeningStatistics.param.registrationPeriod.begin = this.dates[0]
          this.distributionGoodsOpeningStatistics.param.registrationPeriod.end = this.dates[1]
        } else {
          this.distributionGoodsOpeningStatistics.param.registrationPeriod.begin = ''
          this.distributionGoodsOpeningStatistics.param.registrationPeriod.end = ''
        }
        this.distributionGoodsOpeningStatistics.param.excludeCommodityIdList = this.removePlan
        console.log(this.removePlan, ' 剔除培训方案')
        const res = await await this.distributionGoodsOpeningStatistics.exportList()
        if (res.status.code == 200 && res.data) {
          //   this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        //console.log(e)
      } finally {
        //todo
      }
    }

    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportCommodityOpenSummary' }
      })
    }

    /**
     * 重置
     */
    async resetSearch() {
      this.distributionGoodsOpeningStatistics.param = new DistributionGoodsOpeningStatisticsParams()
      this.distributionGoodsOpeningStatistics.param.isPortalData = null
      this.dates = []
      this.removePlan = []
      await this.init()
    }

    /**
     * 合并坊方法
     * @param item
     */
    objectSpanMethod(item: { row: any; column: any; rowIndex: number; columnIndex: number }) {
      if (this.spanList.length) {
        if (item.columnIndex <= 3) {
          return this.spanList[item.rowIndex]
        }
      }
    }
  }
</script>
