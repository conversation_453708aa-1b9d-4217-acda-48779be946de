import { CommodityCode, PrepareElectronicInvoiceResponse } from '@api/ms-gateway/ms-trade-configuration-v1'
import CommodityCodeVo from '@api/service/management/online-school-config/functionality-setting/query/vo/CommodityCodeVo'

/*
 * 税务信息
 */
class ElectronicInvoiceResponseVo extends PrepareElectronicInvoiceResponse {
  /**
   * 开票平台类型
   * 5 - 诺诺   7 - 诺诺V2
   */
  providerList: Array<string> = []
  /**
   * 商品税务编码列表
   */
  commodityCodeList: Array<CommodityCodeVo> = []

  from(item: PrepareElectronicInvoiceResponse) {
    this.providerList = item.providerList
    this.commodityCodeList = item.commodityCodeList?.map((sub: CommodityCode) => {
      return {
        code: sub.code,
        name: sub.name
      }
    })
  }
}

export default ElectronicInvoiceResponseVo
