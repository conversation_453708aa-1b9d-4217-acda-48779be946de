<template>
  <el-card shadow="never" class="m-card f-mb15" id="section2">
    <div slot="header">
      <span class="tit-txt">考勤设置</span>
    </div>
    <template v-if="attendanceConfig.isOpenAttendanceConfig">
      <div class="m-sub-tit">
        <span class="tit-txt f-flex-sub">指定面授期别考勤设置</span>
        <el-button type="primary" size="mini" @click="clockingTemSetDialog">考勤规则模板</el-button>
      </div>
      <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
        设置期别的考勤规则，可选择指定面授期别单独设置。设置完成后期别按指定规则执行。
      </el-alert>

      <el-table
        stripe
        ref="tableRef"
        :data="attendanceConfig.periodInfoList"
        max-height="500px"
        class="m-table f-mt15"
        v-loading="uiLoading.tableLoading"
      >
        <el-table-column label="期别编号" min-width="100" fixed="left">
          <template v-slot="{ row }">{{ row.no }}</template>
        </el-table-column>
        <el-table-column label="期别名称" width="200">
          <template v-slot="{ row }">
            <div class="flex">
              <el-tooltip effect="dark" :content="row.name" placement="top">
                <div class="text-ellipsis">{{ row.name }}</div>
              </el-tooltip>
              <el-tag
                v-if="!row.attendanceSetted && row.isSetAttendanceConfig"
                type="danger"
                size="mini"
                class="f-ml5"
                style="min-width: 46px"
                >未设置
              </el-tag>
              <el-tag v-if="!row.isSetAttendanceConfig" type="danger" size="mini" class="f-ml5" style="min-width: 70px"
                >未开启考勤
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="培训报到时段" min-width="200">
          <template v-slot="{ row }">
            <p>
              <el-tag type="info" size="mini">起始</el-tag>
              {{ row.checkInTime.begin || '-' }}
            </p>
            <p>
              <el-tag type="info" size="mini">结束</el-tag>
              {{ row.checkInTime.end || '-' }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="培训时段" min-width="200">
          <template v-slot="{ row }">
            <p>
              <el-tag type="info" size="mini">起始</el-tag>
              {{ row.trainingTime.begin || '-' }}
            </p>
            <p>
              <el-tag type="info" size="mini">结束</el-tag>
              {{ row.trainingTime.end || '-' }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="考勤规则" width="200" align="center">
          <template v-slot="{ row }">
            <p v-if="row.signIn.isOpen">①开启签到考勤</p>
            <p v-if="row.signOut.isOpen">{{ row.signIn.isOpen ? '②' : '①' }}开启签退考勤</p>
            <p v-if="!row.signIn.isOpen && !row.signOut.isOpen">-</p>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template v-slot="{ row }">
            <el-button type="text" @click="clockingSetDialog(row)" :disabled="!row.isSetAttendanceConfig"
              >设置
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </template>

    <!--通用空数据-->
    <div class="m-no-date" v-else>
      <img class="img" src="@design/admin/assets/images/no-data-normal.png" alt="" />
      <div class="date-bd">
        <p class="f-f15 f-c9">班级未开启考勤~</p>
      </div>
    </div>
    <stage-clocking-dialog
      ref="stageClockingDialogRef"
      :attendance-config="attendanceConfig"
      @updateSuccess="doRefresh"
    ></stage-clocking-dialog>
  </el-card>
</template>

<script lang="ts">
  import { Component, Inject, Prop, Ref, Vue } from 'vue-property-decorator'
  import StageClockingDialog from '@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/stageClockingDialog.vue'
  import AttendanceConfig from '@api/service/management/implement/AttendanceConfig'
  import { AttendanceTypeEnum } from '@api/service/management/implement/enums/AttendanceTypeEnum'
  import { CheckInFrequencyEnum } from '@api/service/common/implement/enums/CheckInFrequencyEnum'
  import ImplementConfig from '@api/service/management/implement/ImplementConfig'
  import PeriodConfig from '@api/service/management/implement/models/PeriodConfig'
  import { UiPage } from '@hbfe/common'
  import { bind, debounce } from 'lodash-decorators'
  import SchemeStepProcess from '@api/service/management/train-class/Utils/SchemeStepProcess'
  import { SchemeProcessStatusEnum } from '@api/service/management/train-class/query/enum/SchemeProcessStatusEnum'
  import AttendanceTemplateConfig from '@api/service/management/implement/AttendanceTemplateConfig'

  @Component({
    components: {
      StageClockingDialog
    }
  })
  export default class extends Vue {
    @Ref('stageClockingDialogRef') stageClockingDialogRef: StageClockingDialog

    // 接收考勤设置
    @Prop({
      required: true,
      default: () => {
        return new AttendanceConfig()
      }
    })
    attendanceConfig: AttendanceConfig

    // 接收训前考勤设置
    @Prop({
      required: true
    })
    implementConfig: ImplementConfig

    @Inject('SchemeStepProcess')
    SchemeStepProcess: SchemeStepProcess

    constructor() {
      super()
      this.page = new UiPage(this.pageSearch, this.pageSearch)
    }

    attendanceTemplateConfig = new AttendanceTemplateConfig()

    uiLoading = {
      tableLoading: false,
      periodLoading: false,
      saveLoading: false
    }
    // todo
    form = {}
    tableData = [{}]

    // 考勤设置枚举赋值
    AttendanceTypeEnum = AttendanceTypeEnum
    CheckInFrequencyEnum = CheckInFrequencyEnum

    // 是否展示配置
    isShowOptions = false
    /**
     * 期别分页
     */
    page: UiPage

    @bind
    @debounce(200)
    async pageSearch() {
      this.uiLoading.tableLoading = true
      try {
        this.attendanceConfig.periodInfoList = []
        await this.attendanceConfig.pagePeriodAttendanceConfig(this.page)
      } catch (e) {
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['tableRef'] as any)?.doLayout()
        this.uiLoading.tableLoading = false
      }
    }

    /**
     * 期别配置查询
     */
    async doPeriodQuery() {
      this.page.pageNo = 1
      await this.pageSearch()
    }

    /**
     * 期别配置查询
     */
    async doRefresh() {
      const schemeId = this.$route.params.schemeId
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        const res = await this.SchemeStepProcess.getSchemeTaskStatus(schemeId)
        if (res.code === SchemeProcessStatusEnum.finish) {
          this.page.pageNo = 1
          await this.pageSearch()
          this.$message.success('设置考勤成功。')
        } else {
          this.$message.error(res.message as string)
        }
      } catch (e) {
        console.error(e)
        this.$message.error(e.message || '设置考勤失败。')
      } finally {
        loading.close()
      }
    }

    /**
     * 考勤设置
     */
    async clockingSetDialog(row: PeriodConfig) {
      this.stageClockingDialogRef.CheckInType = 'edit'
      this.stageClockingDialogRef.settingTemplate = this.attendanceTemplateConfig.settingTemplate
      await this.attendanceTemplateConfig.queryTemplate()

      this.stageClockingDialogRef.open(row)
      this.stageClockingDialogRef.doQuery()
    }

    /**
     * 考勤模版设置
     */
    async clockingTemSetDialog() {
      this.stageClockingDialogRef.CheckInType = 'tem'
      await this.attendanceTemplateConfig.queryTemplate()
      const { attendanceTemplateId, signIn, signOut } = this.attendanceTemplateConfig
      const row = new PeriodConfig()
      row.attendanceSettingId = attendanceTemplateId
      row.signIn = signIn
      row.signOut = signOut

      this.stageClockingDialogRef.open(row)
    }

    async created() {
      await this.doPeriodQuery()
      await this.attendanceTemplateConfig.queryTemplate()
    }
  }
</script>
<style lang="scss" scoped>
  .flex {
    display: flex;
    align-items: center;

    .text-ellipsis {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
</style>
