import SignTime from '@api/service/customer/sign-center/models/SignTime'
import DateScope from '@api/service/common/models/DateScope'
import {
  PlanItemAttendanceResponse,
  PlanTeacherResponse,
  SignInPointResponse,
  SignInRecordResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import { SignTypeEnum } from '@api/service/customer/sign-center/enums/SignTypeEnum'
import { SignStatusEnum } from '@api/service/customer/sign-center/enums/SignStatusEnum'
import { SignResCodeEnum } from '@api/service/customer/sign-center/enums/SignResCodeEnum'
import { AttendanceTypeEnum } from '@api/service/management/implement/enums/AttendanceTypeEnum'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

export default class CourseSignItem {
  /**
   * 教学计划id
   */
  planId: string = undefined

  /**
   * 教学计划组id
   */
  planGroupId: string = undefined

  /**
   * 课程id
   */
  courseId: string = undefined

  /**
   * 课程名称
   */
  courseName: string = undefined

  /**
   * 签到记录
   */
  signInLog: SignTime = new SignTime(SignTypeEnum.singIn)

  /**
   * 签退记录
   */
  signOutLog: SignTime = new SignTime(SignTypeEnum.signOut)

  /**
   * 讲师
   */
  teacher: string = undefined

  /**
   * 培训时段
   */
  trainingTime: DateScope = new DateScope()

  /**
   * 是否绕过时间限制允许签到签退
   */
  get bypassTimeCheck() {
    return ConfigCenterModule.getFrontendApplication(frontendApplication.skipTimeVerification) === 'true'
  }

  /**
   * 当前签到 / 签退类型
   */
  get currentSignType() {
    if (
      [SignStatusEnum.waitSign, SignStatusEnum.signing].includes(this.signInLog.timeSignStatus) ||
      (this.signInLog.timeSignStatus === SignStatusEnum.notSign && this.bypassTimeCheck)
    ) {
      return AttendanceTypeEnum.signIn
    }
    if (
      [SignStatusEnum.waitSign, SignStatusEnum.signing].includes(this.signOutLog.timeSignStatus) ||
      (this.signOutLog.timeSignStatus === SignStatusEnum.notSign && this.bypassTimeCheck)
    ) {
      return AttendanceTypeEnum.signOut
    }
    return undefined
  }

  static from(dto: PlanItemAttendanceResponse) {
    const vo = new CourseSignItem()
    const { planItem, signInPoint, signInRecord } = dto
    if (planItem) {
      vo.planId = planItem.planId
      vo.courseName = planItem.name
      vo.planGroupId = planItem.planItemGroupId
      vo.trainingTime.begin = planItem.startTime
      vo.trainingTime.end = planItem.endTime
      if (planItem.teachers?.length) {
        planItem.teachers.map((teacher: PlanTeacherResponse, index: number) => {
          if (index == 0) {
            vo.teacher = teacher.teacherName
          } else {
            vo.teacher += `、${teacher.teacherName}`
          }
        })
      }
    }
    // 填充签到点
    if (signInPoint?.length) {
      vo.courseId = signInPoint[0].planItemId
      signInPoint.map((signItem: SignInPointResponse) => {
        if (signItem.signType === SignTypeEnum.singIn) {
          vo.signInLog.signScope.beginTime = signItem.startTime
          vo.signInLog.signScope.endTime = signItem.endTime
          vo.signInLog.signScope.beginTimestamp = new Date(vo.signInLog.signScope.beginTime).getTime()
          vo.signInLog.signScope.endTimestamp = new Date(vo.signInLog.signScope.endTime).getTime()
          vo.signInLog.id = signItem.id
        }
        if (signItem.signType === SignTypeEnum.signOut) {
          vo.signOutLog.signScope.beginTime = signItem.startTime
          vo.signOutLog.signScope.endTime = signItem.endTime
          vo.signOutLog.signScope.beginTimestamp = new Date(vo.signOutLog.signScope.beginTime).getTime()
          vo.signOutLog.signScope.endTimestamp = new Date(vo.signOutLog.signScope.endTime).getTime()
          vo.signOutLog.id = signItem.id
        }
      })
    }

    // 匹配签到点记录
    if (signInRecord?.length) {
      signInRecord.map((signRecord: SignInRecordResponse) => {
        if (signRecord.pointKey) {
          if (signRecord.pointKey === vo.signInLog.id) {
            if (signRecord.signResultCode == SignResCodeEnum.success) {
              vo.signInLog.timeSignStatus = SignStatusEnum.signed
              vo.signInLog.signedTime = signRecord.signTime
            }
          }
          if (signRecord.pointKey === vo.signOutLog.id) {
            if (signRecord.signResultCode == SignResCodeEnum.success) {
              vo.signOutLog.timeSignStatus = SignStatusEnum.signed
              vo.signOutLog.signedTime = signRecord.signTime
            }
          }
        }
      })
    }
    // 没有签到点代表无需签到/签退
    if (!vo.signInLog.id) {
      vo.signInLog.timeSignStatus = SignStatusEnum.unSign
    }
    if (!vo.signOutLog.id) {
      vo.signOutLog.timeSignStatus = SignStatusEnum.unSign
    }
    return vo
  }
}
