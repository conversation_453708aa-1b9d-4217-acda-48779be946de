/*
 * @Description: 获取登录人员基本信息接口
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-28 10:31:48
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-02-28 10:35:33
 */

export class Authorization {
  principal: string
}

export class Authority {
  authority: string
  authorization: Authorization
}

class BasicUserInfoDto {
  account: string
  accountDomain: number
  accountId: string
  accountNonExpired: boolean
  accountNonLocked: boolean
  accountType: number
  authorities: Array<Authority>
  credentialExpireTime: string
  credentialsNonExpired: boolean
  enabled: boolean
  extAttribute: any
  loginType: number
  organizationId: number
  password: string
  platformId: string
  platformVersionId: string
  projectId: string
  rootAccountId: string
  subProjectId: string
  test: boolean
  unitId: number
  userId: string
  userSystemType: string
  username: string
}

export default BasicUserInfoDto
