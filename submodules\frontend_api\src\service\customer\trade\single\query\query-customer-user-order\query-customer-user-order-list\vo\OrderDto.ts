import { OrderDTO as GqlOrderDto } from '@api/gateway/PlatformTrade'
import OrderInfo, {
  OrderInfoParse
} from '@api/service/customer/trade/single/query/query-customer-user-order/vo/OrderInfo'
import SchemeCommodity, {
  SchemeCommodityParse
} from '@api/service/customer/trade/single/query/query-customer-user-order/vo/SchemeCommodity'
export default class OrderDTO {
  // 订单基础信息
  orderInfo: OrderInfo = new OrderInfo()
  // 商品列表
  schemeCommodityList: SchemeCommodity[] = []
  schemeCommodity: SchemeCommodity = new SchemeCommodity()

  // get schemeCommodity() {
  //   const schemeCommodity =
  //     this.schemeCommodityList && this.schemeCommodityList.length > 0
  //       ? this.schemeCommodityList[0]
  //       : new SchemeCommodity()
  //   console.log(schemeCommodity, '>>>>>')
  //   return schemeCommodity
  // }
  /**
   * @description: 转化成OrderDetail模型
   * @param {*}
   * @return {*}
   */

  static toOrderDto(gqlOrderDto: GqlOrderDto): OrderDTO {
    const orderDTO = new OrderDTO()
    orderDTO.orderInfo = OrderInfoParse.parseOrderInfo(gqlOrderDto)
    orderDTO.schemeCommodityList = gqlOrderDto.subOrderList?.map(subOrder => {
      return SchemeCommodityParse.parseSchemeCommodity(subOrder)
    })
    orderDTO.schemeCommodity =
      orderDTO.schemeCommodityList && orderDTO.schemeCommodityList.length > 0
        ? orderDTO.schemeCommodityList[0]
        : new SchemeCommodity()
    console.log(12312)
    return orderDTO
  }
}
