schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""关闭产品优惠申请"""
	closeDistributionDiscountApply(request:CloseDiscountApplyRequest):Void
	"""创建优惠申请
		@param createRequest
		@return
	"""
	createDistributionDiscountApply(createRequest:CreateDiscountApplyRequest):DistributionDiscountApplyCommonResponse
	"""开启产品优惠申请"""
	openDistributionDiscountApply(request:OpenDiscountApplyRequest):Void
	"""操作优惠申请
		1-通过
		2-驳回/拒绝
		@param operateRequest
		@return
	"""
	operateDistributionDiscountApply(operateRequest:OperateDiscountApplyRequest):DistributionDiscountApplyCommonResponse
}
input ApplyAttach @type(value:"com.fjhb.domain.trade.api.distribution.events.entities.discount.ApplyAttach") {
	fileName:String
	filePath:String
}
"""关闭产品优惠申请"""
input CloseDiscountApplyRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.discount.CloseDiscountApplyRequest") {
	"""优惠申请id"""
	applyId:String
}
"""创建产品优惠申请请求命令"""
input CreateDiscountApplyRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.discount.CreateDiscountApplyRequest") {
	"""优惠申请id"""
	applyId:String
	"""产品分销授权id"""
	productDistributionAuthId:String
	"""申请人id"""
	applyUserId:String
	"""申请方id(分销商id)"""
	applicantId:String
	"""审批方id(供应商id)"""
	auditPartyId:String
	"""优惠地区【必传】"""
	discountRegionList:[String]
	"""允许报名人数(数量限制)
		1-不限
		2-区间
		@see QuantityConstraints
	"""
	quantityConstraint:Int
	"""最少允许报名人数"""
	minQuantity:Int
	"""最大允许报名人数"""
	maxQuantity:Int
	"""优惠价格"""
	discountPrice:BigDecimal
	"""优惠时间段类型
		1-周期
		2-长期
		@see TimeConstraints
	"""
	discountDateConstraint:Int
	"""【必填】是否立即开始"""
	immediatelyStart:Boolean!
	"""【非立即开始时必填】优惠开始日期"""
	discountStartDate:DateTime
	"""【周期必填】优惠结束日期"""
	discountEndDate:DateTime
	"""申请原因"""
	applyReason:String
	"""附件"""
	applyAttachList:[ApplyAttach]
}
"""开启优惠申请请求"""
input OpenDiscountApplyRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.discount.OpenDiscountApplyRequest") {
	"""产品优惠申请"""
	applyId:String
}
"""操作产品优惠申请"""
input OperateDiscountApplyRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.discount.OperateDiscountApplyRequest") {
	"""申请id"""
	applyId:String
	"""操作
		1-通过
		2-未通过
	"""
	operate:Int
	"""审批人id"""
	auditUserId:String
	"""审核说明"""
	auditReason:String
}
"""优惠申请通用返回类"""
type DistributionDiscountApplyCommonResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distribution.discount.DistributionDiscountApplyCommonResponse") {
	"""状态码：
		200-成功
		500-失败
	"""
	code:String
	"""响应信息"""
	message:String
	"""响应数据(目前没用)"""
	data:String
	"""产品优申请id"""
	applyId:String
}

scalar List
