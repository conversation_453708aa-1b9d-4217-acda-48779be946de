<route-meta>
{
"isMenu": true,
"title": "学员学习明细",
"sort": 5,
"icon": "icon-mingxi"
}
</route-meta>
<template>
  <div>
    <LearningStatisticDiff ref="learningStatisticDiff" v-bind="$attrs" v-on="$listeners">
      <template #disSynchronized>
        <el-option :value="syncResultEnmu.DisableSynchronized" label="不同步"></el-option>
      </template>
      <template #more-filtration="{ localSkuProperty }">
        <el-form-item label="是否补学">
          <el-select v-model="localSkuProperty.supplementaryStudy" clearable filterable placeholder="请选择">
            <el-option label="是" :value="1"></el-option>
            <el-option label="否" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="下单地区">
          <biz-national-region
            v-model="localSkuProperty.payRegion"
            :check-strictly="true"
            placeholder="请选择地区"
          ></biz-national-region>
        </el-form-item>
      </template>
      <template #export-docking-data>
        <el-button
          v-if="$hasPermission('exportStatementsData')"
          query
          desc="导出报盘数据"
          actions="doExportStatementsData"
          @click="doExportStatementsData"
          >导出报盘数据</el-button
        >
      </template>
    </LearningStatisticDiff>
    <el-dialog title="提示" :visible.sync="exportDataSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务查看-培训成果报盘数据</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportDataSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadDataPage">前往下载</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import LearningStatistic from '@hbfe/jxjy-admin-learningStatistic/src/index.vue'
  import UserModule from '@api/service/management/user/UserModule'
  import ExportStatementsData from '@api/service/diff/management/gszj/learning-statistic/ExportStatementsData'
  import {
    SyncResultEnmu,
    StudentSchemeLearningRequestVoDiff
  } from '@api/service/diff/management/gszj/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
  import OnlineClassTable from '@hbfe/jxjy-admin-learningStatistic/src/diff/gszj/__components__/online-class-table.vue'
  import QueryStudentLearningList from '@api/service/diff/management/gszj/statisticalReport/query/QueryStudentLearningList'
  import QueryStudentLearningManagerRegionListDiff from '@api/service/diff/management/gszj/statisticalReport/query/QueryStudentLearningManagerRegionList'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-learningStatistic/src/models/index'
  import { RegionSkuPropertyRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { cloneDeep } from 'lodash'
  import StudentLearningStaticsVoDiff from '@api/service/diff/management/gszj/statisticalReport/query/vo/StudentLearningStaticsVo'
  class SchemeSkuPropertyDiff extends SchemeSkuProperty {
    /**
     * 是否补学
     */
    supplementaryStudy: number = null
    /**
     *下单地区
     */
    payRegion: string[] = []
  }
  @Component({ components: { OnlineClassTable } })
  class LearningStatisticDiff extends LearningStatistic {
    queryStudentLearningList = new QueryStudentLearningList()
    queryStudentLearningManagerRegionList = new QueryStudentLearningManagerRegionListDiff()
    /**
     * 列表数据
     */
    tableData: Array<StudentLearningStaticsVoDiff> = new Array<StudentLearningStaticsVoDiff>()
    /**
     * 筛选项
     */
    filter: StudentSchemeLearningRequestVoDiff = new StudentSchemeLearningRequestVoDiff()
    /**
     * 本地sku
     */
    localSkuProperty = new SchemeSkuPropertyDiff()
    /**
     * 地区管理员导出
     */
    async exportExcelRegion() {
      this.addLocalSkuPropertyDiff()
      return await this.queryStudentLearningManagerRegionList.exportExcel(this.filter)
    }
    /**
     * 分销管理员导出
     */
    async exportStudentSchemeLearningExcelInSDistributor() {
      this.addLocalSkuPropertyDiff()
      return await this.queryStudentLearningList.exportStudentSchemeLearningExcelInSDistributor(this.filter)
    }
    /**
     * 专题管理员导出
     */
    async exportExcelTrainingChannel() {
      this.addLocalSkuPropertyDiff()
      return await this.queryStudentLearningList.exportExcelTrainingChannel(this.filter)
    }
    /**
     * 超级管理员导出
     */
    async exportExcel() {
      this.addLocalSkuPropertyDiff()
      return await this.queryStudentLearningList.exportExcel(this.filter)
    }
    /**
     * 地区管理员查询
     */
    async listRegionLearningReportFormsInServicerRegion() {
      this.addLocalSkuPropertyDiff()
      return await this.queryStudentLearningManagerRegionList.listRegionLearningReportFormsInServicer(
        this.page,
        this.filter
      )
    }
    /**
     * 查询列表——分销商
     */
    async pageStudentSchemeLearningInDistributor() {
      this.addLocalSkuPropertyDiff()
      return await this.queryStudentLearningList.pageStudentSchemeLearningInDistributor(this.page, this.filter)
    }

    /**
     * 学员学习统计列表--专题管理员
     */
    async listRegionLearningReportFormsInTrainingChannel() {
      this.addLocalSkuPropertyDiff()
      return await this.queryStudentLearningList.listRegionLearningReportFormsInTrainingChannel(this.page, this.filter)
    }

    /**
     * 学员学习统计列表
     */
    async listRegionLearningReportFormsInServicer() {
      this.addLocalSkuPropertyDiff()
      return await this.queryStudentLearningList.listRegionLearningReportFormsInServicer(this.page, this.filter)
    }

    addLocalSkuPropertyDiff() {
      //添加是否补学入参
      if (this.$route.query.isReplace) {
        this.localSkuProperty.supplementaryStudy = Number(this.$route.query.isReplace)
      }
      this.filter.isReplace = this.localSkuProperty.supplementaryStudy
      //添加下单地区入参
      if (this.localSkuProperty.payRegion) {
        this.filter.payOrderUnitRegion = new RegionSkuPropertyRequest()
        const localRegion = cloneDeep(this.localSkuProperty.payRegion)
        if (Array.isArray(localRegion) && localRegion.length) {
          //物理地区改为业务地区
          this.filter.payOrderUnitRegion.province = localRegion ? localRegion[0] : undefined
          this.filter.payOrderUnitRegion.city = localRegion ? localRegion[1] : undefined
          this.filter.payOrderUnitRegion.county = localRegion ? localRegion[2] : undefined
        } else {
          this.filter.payOrderUnitRegion = new RegionSkuPropertyRequest()
        }
      }
      //添加科目类型入参
      if (this.$route.query.accountType) {
        this.localSkuProperty.industry = 'industry0221018501809dc4d43e0003'
        this.localSkuProperty.subjectType = this.$route.query.accountType as string
        if (this.localSkuProperty.industry) {
          this.schemeRequest.skuProperty.industry = [this.localSkuProperty.industry]
        }
        if (this.localSkuProperty.subjectType) {
          this.schemeRequest.skuProperty.subjectType = [this.localSkuProperty.subjectType]
        }
        this.filter.scheme = this.schemeRequest
      }
      //添加是否合格入参
      if (this.$route.query.trainingResult) {
        this.localSkuProperty.trainingResultList = [Number(this.$route.query.trainingResult)]
        this.studentLearningRequest.trainingResultList = cloneDeep(this.localSkuProperty.trainingResultList)
        if (this.localSkuProperty.trainingResultList && this.localSkuProperty.trainingResultList.indexOf(0) > -1) {
          this.studentLearningRequest.trainingResultList.push(-1)
        }
      }
    }
  }
  @Component({ components: { LearningStatisticDiff } })
  export default class extends Vue {
    @Ref('learningStatisticDiff') learningStatisticDiff: LearningStatisticDiff

    statementsData: ExportStatementsData = new ExportStatementsData()

    // 对接数据导出弹窗
    exportDataSuccessVisible = false
    /**
     * 重写同步枚举类
     */
    syncResultEnmu = SyncResultEnmu
    /**
     * 导出对接数据
     */
    async doExportStatementsData() {
      this.$confirm('导出后成果同步状态将标记为“已同步”，是否确认导出报盘数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(async () => {
          this.learningStatisticDiff.getLocalSkuProperty()
          this.learningStatisticDiff.addLocalSkuPropertyDiff()
          const res = await this.statementsData.exportStatementsDiff(this.learningStatisticDiff.filter)
          if (res.status.code == 200 && res.data) {
            this.exportDataSuccessVisible = true
          } else {
            this.$message.warning('导出失败')
          }
        })
        .catch(() => {
          // do nothing
        })
    }

    /**
     * 导出对接数据任务下载
     */
    goDownloadDataPage() {
      this.exportDataSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportStudentLearningQuotation' }
      })
    }
  }
</script>
