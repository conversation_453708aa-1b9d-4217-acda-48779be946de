import { txHost } from '@api/service/common/utils/requestTxMap'

export default class QueryRegionInfo {
  lat: number = null
  lng: number = null
  address: string = null

  private key = ''

  constructor() {
    this.key = process.env.VUE_APP_MAP_KEY
  }

  /**
   * web 有跨域问题，使用 jsonp 来请求。
   */
  async queryCurrentIpJsonp(txRequestUrl: string): Promise<QueryRegionInfo> {
    return new Promise((resolve, reject) => {
      const key = `tx_callback_${+new Date()}`
      // 定义回调函数名称
      window[`${key}`] = (config: any) => {
        console.group(
          '%c%s',
          'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#800080, #C71585)',
          'config调试输出'
        )
        console.log(config)
        console.count('config输出次数')
        console.groupEnd()
        this.setProperties(config)
        // 删除函数
        delete window[`${key}`]
        resolve(this)
      }

      const script = document.createElement('script')
      script.src = `${txHost}${txRequestUrl}&` + ['output=jsonp', `callback=${key}`].join('&')

      script.onerror = reject
      const body = document.body
      body.appendChild(script)
    })
  }
  async queryCurrentPlaceJsonp(txRequestUrl: string): Promise<QueryRegionInfo> {
    return new Promise((resolve, reject) => {
      const key = `tx_callback_${+new Date()}`
      // 定义回调函数名称
      window[`${key}`] = (config: any) => {
        const placeList = config.data

        // 删除函数
        delete window[`${key}`]
        resolve(placeList)
      }

      const script = document.createElement('script')
      script.src = `${txHost}${txRequestUrl}&` + ['output=jsonp', `callback=${key}`].join('&')

      script.onerror = reject
      const body = document.body
      body.appendChild(script)
    })
  }

  /**
   * 根据经纬度获取地址
   * @param txRequestUrl 拼接好的url
   */
  async queryCurrentAddressJsonp(txRequestUrl: string): Promise<QueryRegionInfo> {
    return new Promise((resolve, reject) => {
      const key = `tx_callback_${Math.random().toString(36).substr(2, 9)}`
      // 定义回调函数名称
      ;(window as any)[`${key}`] = (config: any) => {
        this.setNameProperties(config)
        // 删除函数
        delete (window as any)[`${key}`]
        resolve(this)
      }

      const script = document.createElement('script')
      script.src = `${txHost}${txRequestUrl}&` + ['output=jsonp', `callback=${key}`].join('&')

      script.onerror = () => {
        reject(new Error('Script load failed'))
      }
      const body = document.body
      body.appendChild(script)
    })
  }

  private setNameProperties(config: any) {
    const { result } = config
    const { address_reference } = result
    const title = address_reference?.landmark_l2.title
    this.address = result.address + title
  }

  private setProperties(config: any) {
    console.log(config)
    const { result } = config
    this.lat = result?.location?.lat
    this.lng = result?.location?.lng
  }
}
