import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-autolearning-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-autolearning-online-school-smart-learning-service-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class IntegerRange {
  min: number
  max: number
}

export class TimeRange {
  min: string
  max: string
}

/**
 * 网校智能学习服务配置返回
<AUTHOR>
@date 2024/5/10 16:41
 */
export class OnlineSchoolSmartLearningServiceConfigResponse {
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商ID(所属网校ID)
   */
  servicerId: string
  /**
   * 网校智能学习服务ID
   */
  onlineSchoolSmartLearningServiceId: string
  /**
   * 课程学习配置
   */
  courseLearningConfigure: CourseLearningConfigure
  /**
   * 课程测验配置
   */
  courseQuizConfigure: CourseQuizConfigure
  /**
   * 考试配置
   */
  examConfigure: ExamConfigure
  /**
   * 服务状态
@see ServiceStatus
   */
  status: number
  /**
   * 创建用户ID
   */
  createUserId: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
}

/**
 * 课程学习配置
<AUTHOR> By Cb
@since 2024/05/10 9:06
 */
export class CourseLearningConfigure {
  /**
   * 每天学习时间区间列表
   */
  everyDayLearningTimeList: Array<TimeRange>
  /**
   * 每天不学习时间列表
   */
  everyDayNotLearningTimeList: Array<TimeRange>
  /**
   * 首次开始学习时间 (报名后的X天 - X天)
   */
  firstLearningDayRange: IntegerRange
  /**
   * 每天学习时长区间(秒)
   */
  everyDayLearningTimeRange: IntegerRange
  /**
   * 每次学习时长区间(秒)
   */
  everyLearningTimeRange: IntegerRange
  /**
   * 休息时长区间(秒) 达到每次学习时长区间后休息-随机休息60~180分钟
   */
  restTimeRange: IntegerRange
  /**
   * 是否启用
默认:true
   */
  enable: boolean
  /**
   * 每天学习学时区间
   */
  everyDayLearningPeriodRange: IntegerRange
  /**
   * 每次学习学时区间
   */
  everyLearningPeriodRange: IntegerRange
  /**
   * 规则类型
0-按课程物理时长 1-按课程学习学时 2-固定时长 3-按学习约束限制
   */
  ruleType: number
}

/**
 * 课程测验配置
<AUTHOR> By Cb
@since 2024/05/10 9:12
 */
export class CourseQuizConfigure {
  /**
   * 答题时长区间(秒)  默认 900s - 3600s (15-60分钟)
   */
  answerTimeRange: IntegerRange
  /**
   * 是否启用
默认:true
   */
  enable: boolean
}

/**
 * 考试配置
<AUTHOR> By Cb
@since 2024/05/10 9:14
 */
export class ExamConfigure {
  /**
   * 答题时长区间(秒)  (最少考试总时长1/3)
   */
  answerTimeRange: IntegerRange
  /**
   * 时长占比(占配置时长的比例)
   */
  durationRatio: number
  /**
   * 是否启用
默认:true
   */
  enable: boolean
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 通过servicerId查询网校智能学习服务配置（状态正常）
   * @param query 查询 graphql 语法文档
   * @param servicerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryOnlineSchoolSmartLearningServiceConfig(
    servicerId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryOnlineSchoolSmartLearningServiceConfig,
    operation?: string
  ): Promise<Response<OnlineSchoolSmartLearningServiceConfigResponse>> {
    return commonRequestApi<OnlineSchoolSmartLearningServiceConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { servicerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 通过servicerId查询网校智能学习服务配置(查询是否开通)
   * @param query 查询 graphql 语法文档
   * @param servicerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryOnlineSchoolSmartLearningServiceConfigByServicerId(
    servicerId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryOnlineSchoolSmartLearningServiceConfigByServicerId,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { servicerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询网校智能学习服务配置(查询是否开通)
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryOnlineSchoolSmartLearningServiceConfigExist(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryOnlineSchoolSmartLearningServiceConfigExist,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
