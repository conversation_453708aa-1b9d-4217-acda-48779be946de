import exportMsgatewayDiff from '@api/platform-gateway/diff-ms-data-export-front-gateway-DataExportBackstage'
import {
  CertificateLearningConfigResultResponse,
  GradeLearningConfigResultResponse,
  LearningResultResponse,
  default as MsSchemeQueryFrontGatewayCourseLearningBackstage,
  StudentSchemeLearningRequest,
  StudentSchemeLearningResponse,
  UserRequest,
  default as schemeLearningMsGateway
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { ConnectManageSystemRequest } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import tradeQueryGateway, {
  OrderResponse,
  RegionModel
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { getOrderInServicer } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage/graphql-importer'
import studentCourseLearningQuery, {
  SimulateStudentSchemeLearningRequest,
  StudentTrainingResultSimulateRequest,
  StudentTrainingResultSimulateResponse
} from '@api/platform-gateway/student-course-learning-query-back-gateway'
import studentCourseLearningQueryDiff from '@api/platform-gateway/platform-jxjypxtypt-student-learning-backstage'
import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { StudentLearningStaticsVo } from '@api/service/diff/management/jxgx/statisticalReport/StudentLearningStaticsVo'
import {
  StudentSchemeLearningRequestVo,
  SyncResultEnmu
} from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
import {
  ComplexSkuPropertyResponse,
  SkuPropertyConvertUtils
} from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import { Constants } from '@api/service/management/train-class/query/vo/Constants'
import UserModule from '@api/service/management/user/UserModule'
import { Page } from '@hbfe/common'
export class QueryStudentLearningManagerRegionList {
  totalSize = 0
  /**
   * 学员学习统计列表
   */
  async listRegionLearningReportFormsInServicer(
    page: Page,
    filter: StudentSchemeLearningRequestVo
  ): Promise<Array<StudentLearningStaticsVo>> {
    try {
      console.log('page参数=', page, 'filter参数=', filter)
      let userIdList: string[] = []
      if (filter.name || filter.idCard || filter.phone) {
        userIdList = await this.getUsers(filter)
        if (!userIdList.length) return []
      }
      const studentLearningRequest = new StudentSchemeLearningRequest()
      Object.assign(studentLearningRequest, StudentSchemeLearningRequestVo)
      studentLearningRequest.student = new UserRequest()
      filter.connectManageSystem = new ConnectManageSystemRequest()
      if (userIdList && userIdList.length) {
        filter.student.userIdList = userIdList
      }
      if (filter.syncResult) {
        if (filter.syncResult === SyncResultEnmu.Unsynchronized) {
          filter.connectManageSystem.syncStatus = 0
        }
        if (filter.syncResult === SyncResultEnmu.Synchronized) {
          filter.connectManageSystem.syncStatus = 1
        }
        if (filter.syncResult === SyncResultEnmu.SynchronizationFailure) {
          filter.connectManageSystem.syncStatus = 2
        }
        if (filter.syncResult === SyncResultEnmu.Waitsynchronized) {
          filter.connectManageSystem.syncStatus = 3
        }
      }
      filter.learningRegister.status = [1, 2]
      const res = await schemeLearningMsGateway.pageStudentSchemeLearningInServicerManageRegion({
        page: page,
        request: filter
      })
      const tmpArr = []

      const studentNoList = new Array<string>()
      const schemeIds = new Array<string>()

      if (res.status.isSuccess()) {
        const orderNoList = new Array<string>()
        this.totalSize = res.data.totalSize
        page.totalSize = res.data.totalSize
        for (const tmpArrElement of res.data.currentPageData) {
          if (tmpArrElement?.learningRegister?.orderNo) orderNoList.push(tmpArrElement.learningRegister.orderNo)

          if (tmpArrElement.studentNo && !studentNoList.includes(tmpArrElement.studentNo)) {
            studentNoList.push(tmpArrElement.studentNo)
          }

          if (tmpArrElement.scheme?.schemeId && !schemeIds.includes(tmpArrElement.scheme.schemeId)) {
            schemeIds.push(tmpArrElement.scheme.schemeId)
          }

          const trainClassDetail = await this.convertToTrainClassDetailClassVo(tmpArrElement) // const reportVo = new RegionLearningStatisticsResponseVo()
          // Object.assign(reportVo, tmpArrElement)

          await trainClassDetail.addUserDetail(tmpArrElement.student.userId)
          const configJson = await this.queryConfig(trainClassDetail.trainClassBaseInfo.id)

          if (configJson) {
            trainClassDetail.trainClassBaseInfo.registerBeginDate = configJson.registerBeginDate
            trainClassDetail.trainClassBaseInfo.registerEndDate = configJson.registerEndDate
            trainClassDetail.trainClassBaseInfo.trainingBeginDate = configJson.trainingBeginDate
            trainClassDetail.trainClassBaseInfo.trainingEndDate = configJson.trainingEndDate
            trainClassDetail.trainClassBaseInfo.name = configJson.name
            trainClassDetail.trainClassBaseInfo.schemeType = configJson.type == 'chooseCourseLearning' ? 1 : 2
            const creditResult = configJson.assessSetting.learningResults.find((item: any) => {
              return item.type == 1
            })
            trainClassDetail.trainClassBaseInfo.period = creditResult.grade
          }
          if (tmpArrElement?.studentLearning?.courseLearning?.userAssessResult?.length) {
            ;(trainClassDetail as any).userAssessResult = JSON.parse(
              tmpArrElement?.studentLearning?.courseLearning?.userAssessResult[0] as any
            )
          }
          tmpArr.push(trainClassDetail)
        }
        const resOrderInfo = new RewriteGraph<OrderResponse, string>(tradeQueryGateway._commonQuery, getOrderInServicer)
        await resOrderInfo.request(orderNoList)
        const regionList = new Array<string>()
        resOrderInfo.itemMap.forEach((ite) => {
          if (ite.buyer.userArea?.path) regionList.push(...ite.buyer.userArea.path.slice(1).split('/'))
        })
        const regionInfoList = regionList.length
          ? await QueryPhysicalRegion.querRegionDetil([...new Set(regionList)])
          : []
        const findRegion = (region: RegionModel) => {
          const findRegionName = (code: string) => regionInfoList.find((ite) => ite.id === code)?.name
          if (region?.county) {
            return (
              findRegionName(region.province) + '-' + findRegionName(region.city) + '-' + findRegionName(region.county)
            )
          } else if (region?.city) {
            return findRegionName(region.province) + '-' + findRegionName(region.city)
          } else {
            return findRegionName(region.province) || '-'
          }
        }

        // 查询模拟数据
        const simulateRequest = new StudentTrainingResultSimulateRequest()
        simulateRequest.studentNos = studentNoList
        simulateRequest.studentSchemeLearning = new SimulateStudentSchemeLearningRequest()
        simulateRequest.studentSchemeLearning.schemeIds = schemeIds
        const simulateResult = await studentCourseLearningQuery.getStudentTrainingResultSimulateResponseInServicer(
          simulateRequest
        )
        const simulateData =
          (simulateResult?.data?.length && simulateResult.data) || new Array<StudentTrainingResultSimulateResponse>()

        tmpArr.map((item) => {
          const managementUnitRegion = resOrderInfo.itemMap.get(item.orderNo)?.buyer.userArea
          item.managementUnitRegionName = managementUnitRegion ? findRegion(managementUnitRegion) : ''
          // 查找班级模拟数据
          const findSimulateData = simulateData.find(
            (it) => it.studentNo === item.studentNo && it.studentSchemeLearning?.schemeId === item.trainClassBaseInfo.id
          )
          if (findSimulateData) {
            item.setSimulateData(findSimulateData)
          }
        })
      }

      console.log('调用了listRegionLearningReportFormsInServicer方法，返回值=', tmpArr)
      return tmpArr
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，listRegionLearningReportFormsInServicer',
        e
      )
    }
  }

  /**
   * 批量重推学院学习成果
   * @param studentNoList 学号列表
   */
  async batchRePushStudentTraining(studentNoList: Array<string>) {
    const res = await studentCourseLearningQueryDiff.rePushStudentTrainingResultToGatewayInServicerV2(studentNoList)

    return res
  }

  private async getUsers(filter: StudentSchemeLearningRequestVo) {
    let userIdList: string[] = []

    const queryUser = UserModule.queryUserFactory.queryStudentList
    queryUser.queryStudentIdParams.idCard = filter.idCard
    queryUser.queryStudentIdParams.userName = filter.name
    queryUser.queryStudentIdParams.phone = filter.phone
    const res = await queryUser.queryStudentIdList()
    userIdList = res.data

    return userIdList
  }
  /**
   * 导出
   */

  async exportExcel(param: StudentSchemeLearningRequestVo) {
    try {
      console.log('param参数=', param)
      let userIdList: string[] = []
      if (param.name || param.idCard || param.phone) {
        userIdList = await this.getUsers(param)
      }
      if (userIdList && userIdList.length) {
        param.student.userIdList = userIdList
      }
      param.learningRegister.status = [1, 2]
      if (param.syncResult) {
        if (param.syncResult === SyncResultEnmu.Unsynchronized) {
          param.connectManageSystem.syncStatus = 0
        }
        if (param.syncResult === SyncResultEnmu.Synchronized) {
          param.connectManageSystem.syncStatus = 1
        }
        if (param.syncResult === SyncResultEnmu.SynchronizationFailure) {
          param.connectManageSystem.syncStatus = 2
        }
        if (param.syncResult === SyncResultEnmu.Waitsynchronized) {
          param.connectManageSystem.syncStatus = 3
        }
      }
      const res = await exportMsgatewayDiff.exportStudentSchemeLearningExcelInServicerManageRegion(param)
      console.log('调用了exportExcel方法，返回值=', res)
      return res
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，exportExcel',
        e
      )
    }
  }
  /**
   * 获取培训班属性
   */

  async queryConfig(schemeId: string): Promise<any> {
    try {
      console.log('schemeId参数=', schemeId) //获取培训班配置模板jsonString

      const res = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInServicer({
        schemeId: schemeId,
        needField: [
          'name',
          'registerBeginDate',
          'registerEndDate',
          'trainingBeginDate',
          'trainingEndDate',
          'type',
          'assessSetting.learningResults'
        ]
      })
      let jsonObj

      try {
        jsonObj = JSON.parse(res.data.schemeConfig)
      } catch (e) {
        return ''
      }

      console.log('调用了queryConfig方法，返回值=', jsonObj)
      return jsonObj
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，queryConfig',
        e
      )
    }
  } //转换成Vo对象

  async convertToTrainClassDetailClassVo(commodityDetail: StudentSchemeLearningResponse) {
    try {
      console.log('commodityDetail参数=', commodityDetail)
      const trainClassDetail = new StudentLearningStaticsVo()

      try {
        trainClassDetail.trainClassBaseInfo.skuProperty = await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
          commodityDetail.scheme.skuProperty as ComplexSkuPropertyResponse
        )
      } catch (e) {
        console.log(e)
      }
      trainClassDetail.syncStatus = commodityDetail?.connectManageSystem?.syncStatus
      trainClassDetail.syncMessage = commodityDetail?.connectManageSystem?.syncMessage
      trainClassDetail.studentNo = commodityDetail.studentNo
      trainClassDetail.orderNo = commodityDetail.learningRegister.orderNo
      trainClassDetail.trainClassBaseInfo.schemeType = Constants.getSchemeType(commodityDetail.scheme.schemeType)
      trainClassDetail.trainClassBaseInfo.id = commodityDetail.scheme.schemeId // trainClassDetail.trainClassBaseInfo.qualificationId = commodityDetail.qualificationId
      // trainClassDetail.trainClassBaseInfo.studentNo = commodityDetail.studentNo
      trainClassDetail.trainClassBaseInfo.qualificationId = commodityDetail.qualificationId
      trainClassDetail.userGetLearning.trainingResult = commodityDetail.studentLearning.trainingResult // trainClassDetail.trainClassBaseInfo.trainingResultTime = commodityDetail.studentLearning.trainingResultTime

      trainClassDetail.userGetLearning.trainingResultTime = commodityDetail.studentLearning.trainingResultTime
      if (commodityDetail.studentLearning.courseLearning) {
        trainClassDetail.userGetLearning.selectedCoursePeriod =
          commodityDetail.studentLearning.courseLearning.selectedCoursePeriod
        trainClassDetail.userGetLearning.courseQualifiedTime =
          commodityDetail.studentLearning.courseLearning.courseQualifiedTime
      }

      if (commodityDetail.studentLearning.examLearning) {
        const examLearnRes = commodityDetail.studentLearning.examLearning
        trainClassDetail.userGetLearning.committedExam = examLearnRes.committedExam
        trainClassDetail.userGetLearning.examQualified = examLearnRes.examAssessResult == 1 ? true : false
        trainClassDetail.userGetLearning.examQualifiedTime = examLearnRes.examQualifiedTime
        trainClassDetail.userGetLearning.maxExamScore = examLearnRes.maxExamScore || 0
        trainClassDetail.userGetLearning.examCount = examLearnRes.examCount
      }
      if (
        commodityDetail.studentLearning.learningExperienceLearning &&
        commodityDetail.studentLearning.learningExperienceLearning.userAssessResult &&
        commodityDetail.studentLearning.learningExperienceLearning.userAssessResult.length
      ) {
        const learningExperienceConfig = JSON.parse(
          commodityDetail.studentLearning.learningExperienceLearning.userAssessResult[0]
        )
        if (learningExperienceConfig.lessParticipateLearningExperienceTopicCount) {
          trainClassDetail.userGetLearning.learningExperienceRequireCount =
            learningExperienceConfig.lessParticipateLearningExperienceTopicCount.config ?? 0
          trainClassDetail.userGetLearning.learningExperienceCount =
            learningExperienceConfig.lessParticipateLearningExperienceTopicCount.current ?? 0
        }
      }
      if (commodityDetail.studentLearning.learningResult) {
        //配置培训证明
        const templateResult = commodityDetail.studentLearning.learningResult.find(
          (learnResult: LearningResultResponse) => learnResult.learningResultConfig.resultType == 2
        )

        if (templateResult) {
          trainClassDetail.userGetLearning.learningResult.learningResultId = (
            templateResult.learningResultConfig as CertificateLearningConfigResultResponse
          ).certificateTemplateId
          trainClassDetail.userGetLearning.learningResult.learningResultName = ''
        } //获取培训成果中学分

        const creditLearningResult = commodityDetail.studentLearning.learningResult.find(
          (learnResult: LearningResultResponse) => learnResult.learningResultConfig.resultType == 1
        )

        if (creditLearningResult) {
          // trainClassDetail.trainClassBaseInfo.period = (creditLearningResult.learningResultConfig as GradeLearningConfigResultResponse).grade
          trainClassDetail.userGetLearning.credit = (
            creditLearningResult.learningResultConfig as GradeLearningConfigResultResponse
          ).grade
        }
      }

      console.log('调用了convertToTrainClassDetailClassVo方法，返回值=', trainClassDetail)
      return trainClassDetail
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，convertToTrainClassDetailClassVo',
        e
      )
    }
  }
}
