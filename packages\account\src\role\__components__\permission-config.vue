<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: Lin yt
 * @Date: 2022-02-15 16:01:36
 * @LastEditors: <PERSON> yt
 * @LastEditTime: 2022-06-09 16:57:14
-->
<template>
  <div class="f-p20 m-authority" v-loading="queryPermissionLoading">
    <div class="selected-all">
      <el-checkbox label="超管（全选）" v-model="isAllChecked"></el-checkbox>
    </div>
    <el-tree
      :data="permissionList"
      size="mini"
      show-checkbox
      node-key="id"
      @check="currentChecked"
      ref="tree"
      highlight-current
      :props="defaultProps"
    >
    </el-tree>
    <!-- <el-collapse v-model="activeNames" @change="handleChange">
      <el-collapse-item :name="permission.id" v-for="permission in permissionList" :key="permission.id">
        <template slot="title">
          <el-checkbox :label="permission.name"></el-checkbox>
        </template>
        <el-cascader-panel :props="props" :options="permission.children"></el-cascader-panel>
      </el-collapse-item>
    </el-collapse> -->
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Emit, Watch } from 'vue-property-decorator'
  import SecurityFactory from '@api/service/management/authority/security/SecurityFactory'
  import { SecurityGroupTree } from '@api/service/management/authority/security/query/vo/SecurityGroupTree'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import IntelligenceLearningModule from '@api/service/management/intelligence-learning/IntelligenceLearningModule'
  @Component
  export default class extends Vue {
    /**
     * el-tree取值规则
     */
    defaultProps = {
      children: 'children',
      label: 'name'
    }

    @Prop({
      type: Boolean,
      default: false
    })
    editable: boolean
    @Prop({ type: Number, default: CategoryEnums.wxgly }) category: CategoryEnums
    getQueryAllPermission = new SecurityFactory()
    permissionList = new Array<SecurityGroupTree>()
    isAllChecked = false
    permission: any[] = []
    queryPermissionLoading = false

    /**
     * 判断是否全选
     */
    isAllSelected = false

    filterznxx(obj: SecurityGroupTree) {
      if (obj.children && obj.children.length) {
        let targetIndex = obj.children.findIndex(
          (item) => item.content && item.content == 'WXGLY.training.intelligent-learning' && item?.isMenu
        )

        if (targetIndex >= 0) {
          obj.children.splice(targetIndex, 1)
        }
        obj.children.forEach((item) => {
          this.filterznxx(item)
        })
      }
    }

    /**
     * 监听isAllChecked值变化，触发全选/取消全选
     */
    @Watch('isAllChecked', { deep: true })
    handleWatch() {
      const tree: any = this.$refs.tree
      const bol = this.isAllChecked
      this.isAllSelected = this.isAllChecked
      const nodeIds: string[] = []
      this.permissionList.forEach(function (data: SecurityGroupTree) {
        // setCheckedKeys 会更快，但是这个通用取消全选
        tree.setChecked(data.id, bol, true)
      })
      const checkedNodes = tree.getCheckedNodes(false)
      checkedNodes.forEach((node: SecurityGroupTree) => {
        if (node.securityAuthorizationId) {
          nodeIds.push(node.securityAuthorizationId)
        } else {
          nodeIds.push(node.id)
        }
      })
      this.getCheckList(nodeIds)
    }
    async created() {
      await this.queryPermission(this.category)
    }

    async queryPermission(category: CategoryEnums) {
      const IntelligenceLearningModuleObj = new IntelligenceLearningModule()
      this.queryPermissionLoading = true
      let permissionList: SecurityGroupTree[] = []
      try {
        if (category === CategoryEnums.ztgly) {
          const specialAdminRoleId = ConfigCenterModule.getFrontendApplication(frontendApplication.specialAdminRoleId)
          permissionList = await this.getQueryAllPermission.querySecurity.getSecurityGroupByRoleId(
            [specialAdminRoleId],
            category
          )
        } else {
          permissionList = await this.getQueryAllPermission.querySecurity.getCurrentUserSecurityGroupByCategory(
            category
          )
        }
        const smartLearning = await IntelligenceLearningModuleObj.doQueryServiceConfig()
        if (smartLearning !== 1) {
          this.filterznxx(permissionList[0])
        }
        this.permissionList = permissionList
      } catch (e) {
        this.permissionList = []
        console.log(e)
      } finally {
        this.queryPermissionLoading = false
      }
    }

    @Emit('getCheckList')
    getCheckList(checkedKeys: any) {
      return checkedKeys
    }
    currentChecked() {
      const allNodes = this.getAllNodes(this.permissionList)
      this.isAllSelected = allNodes.every((node) => node.isSelected === true)
      const tree: any = this.$refs.tree
      const nodeIds = new Array<string>()
      const permission = tree.getCheckedNodes(false, true)
      permission.forEach(function (node: SecurityGroupTree) {
        if (node.securityAuthorizationId) {
          nodeIds.push(node.securityAuthorizationId)
        } else {
          nodeIds.push(node.id)
        }
      })
      this.permission = permission
      this.getCheckList(nodeIds)
    }

    getAllNodes(nodes: any) {
      let allNodes: any[] = []
      nodes.forEach((node: any) => {
        allNodes.push(node)
        if (node.children && node.children.length > 0) {
          allNodes = allNodes.concat(this.getAllNodes(node.children))
        }
      })
      return allNodes
    }
    // checkPermission(permission: Permission) {
    //   const storeIds = new Array<string>()
    //   this.getSelectIds(permission, storeIds)
    // }
  }
</script>
