import PracticeMode from '../common/enums/PracticeMode'

/**
 * 做题记录分页项
 */
class AnswerRecordItem {
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方式id
   */
  learningId: string
  /**
   * 答题记录卷id
   */
  answerExamRecordId: string
  /**
   * 答卷id
   */
  answerPaperId: string
  /**
   * 引用的试卷id
   */
  examPaperId: string
  /**
   * 练习模式
   */
  practiceMode: PracticeMode
  /**
   * 抽题所在的标签
   当practiceMode &#x3D; PracticeMode.OUTLINE 有效
   */
  tagId: string
  /**
   * 抽题题型
   当practiceMode &#x3D;  PracticeMode.QUESTION_TYPE 有效
   0/1/2/3/4/5/6/7 未知/判断/单选/多选/填空/简答/父子/混合
   */
  questionType: number
  /**
   * 抽题数
   */
  questionCount: number
  /**
   * 答题数
   */
  answerCount: number
  /**
   * 正确数
   */
  correctCount: number
  /**
   * 答卷完成时间
   */
  answerCompleteTime: string
}

export default AnswerRecordItem
