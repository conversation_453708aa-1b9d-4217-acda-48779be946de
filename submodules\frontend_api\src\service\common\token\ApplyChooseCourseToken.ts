import AbstractApplyToken from '@api/service/common/token/AbstractApplyToken'
import ApplyStudentLearningToken from '@api/service/common/token/ApplyStudentLearningToken'
import MsChooseCourseLearningSceneV1 from '@api/ms-gateway/ms-choosecourselearningscene-v1'
import { ResponseStatus } from '@hbfe/common'

/**
 * 学习课程token
 */
class ApplyChooseCourseToken extends AbstractApplyToken {
  private applyStudentLearningToken: ApplyStudentLearningToken

  constructor(applyStudentLearningToken: ApplyStudentLearningToken) {
    super()
    this.applyStudentLearningToken = applyStudentLearningToken
  }

  async apply(): Promise<ResponseStatus> {
    // 学习方案场景申请用户 token
    const tokenValue = await MsChooseCourseLearningSceneV1.applyChooseCourse(this.applyStudentLearningToken.token)
    this.token = tokenValue.data.token
    return new ResponseStatus(tokenValue.status.code, tokenValue.status.errors && tokenValue.status.getMessage())
  }
}

export default ApplyChooseCourseToken
