<template>
  <div>
    <!--日志详情-->
    <el-drawer
      title="日志详情"
      :visible.sync="showLog"
      size="800px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd f-mt20 f-mlr40">
        <el-timeline>
          <el-timeline-item v-for="(item, index) in operateLogList" :key="index">
            <p class="f-mb10 f-fb f-f15">
              {{ item.operateDate }} <span class="f-ml30">{{ item.operator }}</span>
            </p>
            <div class="f-c6">
              <div class="f-mt5" v-for="(subItem, subIndex) in item.operateDetail" :key="subIndex">
                <span v-if="subItem.operateType === FakeOperateTypeEnum.text">
                  修改{{ subItem.operateProperty }}【{{ subItem.primalValue }}】改为【{{ subItem.modifiedValue }}】
                </span>
                <span v-if="subItem.operateType === FakeOperateTypeEnum.radio">
                  将【{{ subItem.primalValue }}】改为【{{ subItem.modifiedValue }}】
                </span>
                <span v-if="subItem.operateType === FakeOperateTypeEnum.image">
                  修改{{ subItem.operateProperty }}
                </span>
              </div>
              <div class="f-mt5">
                <span class="f-co">方案重算记录：共处理学员 10 人。</span>
                <el-button type="warning" size="mini" class="f-ml5" @click="studentListDialog = true"
                  >查看详情</el-button
                >
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-drawer>
    <el-drawer title="重算学员名单详情" :visible.sync="studentListDialog" size="600px" custom-class="m-drawer">
      <div class="drawer-bd">
        <!--表格-->
        <el-table stripe :data="studentList" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="姓名" min-width="100">
            <template>林晓</template>
          </el-table-column>
          <el-table-column label="登录帐号" min-width="240">
            <template>350128199909096129</template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="studentListPage" v-bind="studentListPage"> </hb-pagination>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import FakeSchemeOperateLog from '@hbfe/jxjy-admin-scheme/src/models/FakeSchemeOperateLog'
  import Mockjs from 'mockjs'
  import { Page, Query, UiPage } from '@hbfe/common'
  import { FakeOperateTypeEnum } from '@hbfe/jxjy-admin-scheme/src/models/FakeOperateTypeEnum'
  import UITrainClassCommodityDetail from '@hbfe/jxjy-admin-scheme/src/models/UITrainClassCommodityDetail'
  import FakeSchemeOperateLogDetail from '@hbfe/jxjy-admin-scheme/src/models/FakeSchemeOperateLogDetail'

  @Component
  export default class extends Vue {
    FakeOperateTypeEnum = FakeOperateTypeEnum
    showLog = false
    operateLogList: Array<FakeSchemeOperateLog> = new Array<FakeSchemeOperateLog>()
    studentListDialog = false
    studentList = new Array<{ name: string; account: string }>()
    studentListPage: UiPage = new UiPage()

    /**
     * 查看修改日志
     */
    viewLog(row: UITrainClassCommodityDetail) {
      console.log('rowInfo', row)
      const randomNum = Mockjs.Random.integer(10, 20)
      this.operateLogList = new Array<FakeSchemeOperateLog>()
      for (let i = 0; i < randomNum; i++) {
        const option = new FakeSchemeOperateLog()
        option.operateDate = Mockjs.Random.date('yyyy-MM-dd HH:mm:ss')
        option.operator = Mockjs.Random.cname()
        option.operateDetail = new Array<FakeSchemeOperateLogDetail>()
        for (let i = 0; i < 5; i++) {
          const randomSubNum = Mockjs.Random.integer(0, 2)
          const subOption = new FakeSchemeOperateLogDetail()
          subOption.operateProperty = Mockjs.Random.csentence(5)
          subOption.operateType =
            randomSubNum == 0
              ? FakeOperateTypeEnum.text
              : randomSubNum == 1
              ? FakeOperateTypeEnum.image
              : FakeOperateTypeEnum.radio
          subOption.primalValue = Mockjs.Random.csentence(6)
          subOption.modifiedValue = Mockjs.Random.csentence(6)
          option.operateDetail[i] = subOption
        }
        this.operateLogList[i] = option
      }
      this.showLog = true
    }
  }
</script>
