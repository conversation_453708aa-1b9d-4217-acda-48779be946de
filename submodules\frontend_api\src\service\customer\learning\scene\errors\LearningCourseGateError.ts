import { ResponseStatus } from '@hbfe/common'
import ErrorCodeEnum from '@api/service/customer/learning/scene/enums/ErrorCodeEnum'

class LearningCourseGateError extends ResponseStatus {
  constructor(code: number, message: string) {
    super(code, message)
    if (this.isMustLearnNotCompleteYet()) {
      this.message = '您还有必学课程未完成，请先学习必学课程。'
    }
  }

  message: string

  isMustLearnNotCompleteYet() {
    return this.code === ErrorCodeEnum.mustLearnNotCompleteYet
  }
}

export default LearningCourseGateError
