import FjzjStudentLearningGateway, {
  FJZJApplyStudentLearningTokenRequest
} from '@api/diff-gateway/platform-jxjypxtypt-fjzj-student-learning'
import { ResponseStatus } from '@hbfe/common'

/**
 * 申请学员学习 token
 */
class ApplyStudentLearningTokenDiff {
  // 参训资格 id
  private readonly qualificationId: string
  // 学习方式 id
  private readonly learningId: string
  // 学员 token
  token = ''

  /**
   * @param qualificationId 参训资格 id
   * @param learningId 学习方式 id
   */
  constructor(qualificationId: string, learningId: string) {
    this.qualificationId = qualificationId
    this.learningId = learningId
  }

  /**
   * 用学习方案换取学员 token
   */
  async apply(): Promise<ResponseStatus> {
    const requestStudentLearningTokenParams = new FJZJApplyStudentLearningTokenRequest()
    requestStudentLearningTokenParams.learningId = this.learningId
    requestStudentLearningTokenParams.qualificationId = this.qualificationId
    requestStudentLearningTokenParams.learnType = 2
    const result = await FjzjStudentLearningGateway.applyStudentLearningToken(requestStudentLearningTokenParams)
    let errorMsg = ''

    if (result.status.code != 200) {
      errorMsg = result.status.getMessage()
      result.status.code = result.status.errors[0].code
    } else {
      // if (result.data.code != 200) {
      //   errorMsg = result.data.message
      //   result.status.code = result.data.code
      // } else {
      this.token = result.data.token
      // }
    }

    return new ResponseStatus(result.status.code, errorMsg)
  }
}

export default ApplyStudentLearningTokenDiff
