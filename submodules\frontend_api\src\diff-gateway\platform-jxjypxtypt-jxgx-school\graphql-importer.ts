import enterIndex from './mutates/enterIndex.graphql'
import getStudyCourseNum from './mutates/getStudyCourseNum.graphql'
import invalidStudentCourses from './mutates/invalidStudentCourses.graphql'
import verifyPermissionsToCourse from './mutates/verifyPermissionsToCourse.graphql'
import verifyPersonZcInfo from './mutates/verifyPersonZcInfo.graphql'
import verifyStudyStatus from './mutates/verifyStudyStatus.graphql'

export {
  enterIndex,
  getStudyCourseNum,
  invalidStudentCourses,
  verifyPermissionsToCourse,
  verifyPersonZcInfo,
  verifyStudyStatus
}
