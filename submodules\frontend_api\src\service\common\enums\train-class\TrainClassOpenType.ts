import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 培训方案开通方式
 */
export enum TrainClassOpenTypeEnum {
  // 学员自主报名
  SELF_APPLY = 1,
  // 集体报名
  COLLECTIVITY_APPLY = 2,
  // 管理员导入
  MANAGER_IMPORT = 3
}
/**
 * @description 物品状态枚举
 */
class TrainClassOpenType extends AbstractEnum<TrainClassOpenTypeEnum> {
  static enum = TrainClassOpenTypeEnum
  constructor(status?: TrainClassOpenTypeEnum) {
    super()
    this.current = status
    this.map.set(TrainClassOpenTypeEnum.SELF_APPLY, '个人报名')
    this.map.set(TrainClassOpenTypeEnum.COLLECTIVITY_APPLY, '集体报名')
    this.map.set(TrainClassOpenTypeEnum.MANAGER_IMPORT, '管理员导入')
  }
}

export default new TrainClassOpenType()
