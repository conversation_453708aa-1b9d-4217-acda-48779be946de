import {
  PaymentTypeStatisticDto,
  PurchaseChannelStatisticDto
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
/**
 * 子订单变更记录统计情况
 <AUTHOR>
 @date 2022/05/10
 */
export class SubOrderStatisticDto {
  /**
   * 交易成功数量
   */
  tradeSuccessCount = 0
  /**
   * 退货数量
   */
  returnCount = 0
  /**
   * 换入数量
   */
  exchangeInCount = 0
  /**
   * 换出数量
   */
  exchangeOutCount = 0
  /**
   * 净交易成功数量
   <p> (交易成功数量 + 换入数量) - (退货数量 + 换出数量)
   */
  netTradeSuccessCount = 0
}
/**
 * 统计报表合计数据
 <AUTHOR>
 @date 2022/05/10
 */
export class ReportSummaryResponse {
  /**
   * 净成交总额
   <p> 交易成功订单总金额 - 已退款订单总额
   */
  totalNetAmount: number
  /**
   * 交易次数合计数据
   */
  tradeCountSummaryInfo: SubOrderStatisticDto = new SubOrderStatisticDto()
  /**
   * 各渠道统计信息
   */
  purchaseChannelStatisticInfoList: Array<PurchaseChannelStatisticDto> = []
  async fillData() {
    if (!this.purchaseChannelStatisticInfoList) {
      this.purchaseChannelStatisticInfoList = []
    }
    const channels = [1, 2, 3]
    for (const channel of channels) {
      this.fillPurchaseChannelStatisticInfoList(channel)
    }
  }
  /**
   * 购买渠道 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
   @see PurchaseChannelTypes
   */
  private fillPurchaseChannelStatisticInfoList(purchaseChannel: number) {
    let findChannel = this.purchaseChannelStatisticInfoList.find(item => item.purchaseChannel == purchaseChannel)
    if (!findChannel) {
      findChannel = new PurchaseChannelStatisticDto()
      findChannel.purchaseChannel = purchaseChannel
      findChannel.paymentTypeStatisticInfoList = []
      this.purchaseChannelStatisticInfoList.push(findChannel)
    }
    if (purchaseChannel == 1) {
      if (!findChannel.paymentTypeStatisticInfoList.find(item => item.paymentType == 1)) {
        findChannel.paymentTypeStatisticInfoList.push(this.addPayment(1))
      }
    } else if (findChannel.purchaseChannel == 2) {
      if (!findChannel.paymentTypeStatisticInfoList.find(item => item.paymentType == 1)) {
        findChannel.paymentTypeStatisticInfoList.push(this.addPayment(1))
      }
      if (!findChannel.paymentTypeStatisticInfoList.find(item => item.paymentType == 2)) {
        findChannel.paymentTypeStatisticInfoList.push(this.addPayment(2))
      }
    } else if (findChannel.purchaseChannel == 3) {
      if (!findChannel.paymentTypeStatisticInfoList.find(item => item.paymentType == 1)) {
        findChannel.paymentTypeStatisticInfoList.push(this.addPayment(1))
      }
    }
  }
  private addPayment(paymentType: number) {
    const paymentModel = new PaymentTypeStatisticDto()
    paymentModel.paymentType = paymentType
    paymentModel.statisticInfo = new SubOrderStatisticDto()
    paymentModel.statisticInfo.exchangeInCount = 0
    paymentModel.statisticInfo.exchangeOutCount = 0
    paymentModel.statisticInfo.netTradeSuccessCount = 0
    paymentModel.statisticInfo.returnCount = 0
    paymentModel.statisticInfo.tradeSuccessCount = 0
    return paymentModel
  }
}
