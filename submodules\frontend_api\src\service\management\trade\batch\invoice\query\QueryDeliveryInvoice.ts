/*
 * @Description: 发票配送
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-06 08:20:04
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-06-07 16:34:58
 */
import {
  OfflineInvoiceDeliveryInfoResponse,
  OfflineInvoiceRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { Page, UiPage } from '@hbfe/common'
import DeliveryInvoiceParamVo from './vo/DeliveryInvoiceParam'
import QueryOffLineInvoice from './QueryOffLineInvoice'
import QueryOffLinePageInvoiceParam from './vo/QueryOffLinePageInvoiceParam'
import OffLinePageInvoiceVo from '@api/service/management/trade/batch/invoice/query/vo/OffLinePageInvoiceResponseVo'

import UserModule from '@api/service/management/user/UserModule'
import ExportGateWay from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import Backstage from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import BasicDataQueryBackstage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import QueryRegion from '@api/service/common/basic-data-dictionary/query/QueryRegion'
import QueryDeliveryInvoiceBase from '@api/service/management/trade/batch/invoice/query/vo/QueryDeliveryInvoiceBase'
export default class QueryDeliveryInvoice extends QueryDeliveryInvoiceBase {
  /**
   * 分页查询发票配送
   * @param page 页数
   * @returns  Array<OffLinePageInvoiceVo>
   */
  async queryPageDeliveryInvoice(
    page: Page,
    deliveryInvoiceParamVo?: DeliveryInvoiceParamVo
  ): Promise<Array<OffLinePageInvoiceVo>> {
    const offlineInvoiceRequest = new QueryOffLinePageInvoiceParam()
    offlineInvoiceRequest.takePerson = deliveryInvoiceParamVo.recipient
    offlineInvoiceRequest.idCard = deliveryInvoiceParamVo.idCard
    offlineInvoiceRequest.deliveryStatusList = deliveryInvoiceParamVo.deliveryStatus
    offlineInvoiceRequest.orderNoList = deliveryInvoiceParamVo.invoiceNo
    offlineInvoiceRequest.deliveryStartTime = deliveryInvoiceParamVo.startDate
    offlineInvoiceRequest.deliveryEndTime = deliveryInvoiceParamVo.endDate
    offlineInvoiceRequest.shippingMethodList = deliveryInvoiceParamVo.deliveryWay
    offlineInvoiceRequest.expressNo = deliveryInvoiceParamVo.theAwb
    offlineInvoiceRequest.consignee = deliveryInvoiceParamVo.name
    offlineInvoiceRequest.invoiceFreezeStatus = deliveryInvoiceParamVo.frozenState
    offlineInvoiceRequest.invoiceStatusList = 2
    offlineInvoiceRequest.userId = deliveryInvoiceParamVo.createUserId
    const queryOffLineInvoice = new QueryOffLineInvoice()

    const listData = await queryOffLineInvoice.offLinePageVatspecialplaInvoiceInServicer(page, offlineInvoiceRequest)
    if (listData.length === 0) return listData
    const regionS = listData
      .map(item => {
        if (item.deliveryInfo.deliveryAddress) {
          return item.deliveryInfo.deliveryAddress?.region
        }
      })
      .filter(item => item)

    if (regionS?.length) {
      const map = await QueryRegion.querRegionDetilAll(regionS)
      listData.forEach(item => {
        if (item.deliveryInfo?.deliveryAddress && item.deliveryInfo.deliveryAddress?.region) {
          if (item.deliveryInfo.deliveryAddress.region[0] === '/') {
            item.deliveryInfo.deliveryAddress.region = map.get(item.deliveryInfo.deliveryAddress.region)
              ? map.get(item.deliveryInfo.deliveryAddress.region)
              : item.deliveryInfo.deliveryAddress.region
          } else {
            item.deliveryInfo.deliveryAddress.region = map.get('/' + item.deliveryInfo.deliveryAddress.region)
              ? map.get('/' + item.deliveryInfo.deliveryAddress.region)
              : item.deliveryInfo.deliveryAddress.region
          }
        }
      })
    }
    return listData
  }
  /**
   * 导出发票配送
   */
  async exportPageDeliveryInvoice(deliveryInvoiceParamVo: DeliveryInvoiceParamVo): Promise<boolean> {
    const offlineInvoiceRequest = new QueryOffLinePageInvoiceParam()
    offlineInvoiceRequest.takePerson = deliveryInvoiceParamVo.name
    offlineInvoiceRequest.idCard = deliveryInvoiceParamVo.idCard
    offlineInvoiceRequest.deliveryStatusList = deliveryInvoiceParamVo.deliveryStatus
    offlineInvoiceRequest.orderNoList = deliveryInvoiceParamVo.invoiceNo
    offlineInvoiceRequest.deliveryStartTime = deliveryInvoiceParamVo.startDate
    offlineInvoiceRequest.deliveryEndTime = deliveryInvoiceParamVo.endDate
    offlineInvoiceRequest.shippingMethodList = deliveryInvoiceParamVo.deliveryWay
    offlineInvoiceRequest.expressNo = deliveryInvoiceParamVo.theAwb
    offlineInvoiceRequest.takePerson = deliveryInvoiceParamVo.recipient
    offlineInvoiceRequest.invoiceFreezeStatus = deliveryInvoiceParamVo.frozenState
    const queryOffLineInvoice = new QueryOffLineInvoice()

    const data = await this.offLinePageVatspecialplaInvoiceInExport(offlineInvoiceRequest)
    return data
  }

  /**
   * 集体线下发票导出 - 专票
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  async offLinePageVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean> {
    queryOffLinePageInvoiceParam.invoiceType = 2
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const page = new UiPage()
      page.pageNo = 1
      page.pageSize = 200
      const idList = await queryStudentIdList.queryPageCollectiveList(page, {
        name: queryOffLinePageInvoiceParam.userName ? queryOffLinePageInvoiceParam.userName : undefined,
        idCard: queryOffLinePageInvoiceParam.idCard ? queryOffLinePageInvoiceParam.idCard : undefined,
        phone: queryOffLinePageInvoiceParam.phone ? queryOffLinePageInvoiceParam.phone : undefined
      })
      if (idList.length === 0) {
        request.associationInfo.buyerIdList = ['-1']
      } else {
        request.associationInfo.buyerIdList = idList.map(item => item.userInfo.userId)
      }
    }
    const result = await ExportGateWay.exportInvoiceDeliveryInServicer(request)
    return result.data
  }
}
