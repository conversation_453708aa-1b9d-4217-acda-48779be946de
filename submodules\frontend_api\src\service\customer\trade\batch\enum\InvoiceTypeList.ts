import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 发票类型枚举
 */
export enum InvoiceTypeEnum {
  // 1：增值税电子普通发票
  VAT_NORMAL_INVOICE = 1,
  // 2：增值税专用发票
  VAT_SPECIAL_INVOICE
}

/**
 * @description 发票类型列表
 */
class InvoiceTypeList extends AbstractEnum<InvoiceTypeEnum> {
  static enum = InvoiceTypeEnum
  constructor(status?: InvoiceTypeEnum) {
    super()
    this.current = status
    this.map.set(InvoiceTypeEnum.VAT_NORMAL_INVOICE, '增值税电子普通发票')
    this.map.set(InvoiceTypeEnum.VAT_SPECIAL_INVOICE, '增值税专用发票')
  }
}

export default new InvoiceTypeList()
