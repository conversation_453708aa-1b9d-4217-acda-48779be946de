import InvoiceFactor from '@api/service/management/trade/single/invoice/InvoiceFactor'
import OrderFactor from '@api/service/management/trade/single/order/OrderFactor'
import MutationTradeFactory from '@api/service/management/trade/single/order/MutationTradeFactory'
import CheckAccountFactor from './checkAccount/CheckAccountFactor'
class TradeSingleFactor {
  /**
   * 个人 发票工厂
   */
  invoiceFactor = new InvoiceFactor()
  /**
   * 个人 订单工厂
   */
  orderFactor = new OrderFactor()
  /**
   * 个人 交易业务工厂
   */
  mutationFactory = new MutationTradeFactory()
  /**
   * 个人 对账查询
   */
  checkAccountFactor = new CheckAccountFactor()
}
export default TradeSingleFactor
