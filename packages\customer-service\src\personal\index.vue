<template>
  <div v-if="$hasPermission('query')" desc="查询" actions="created,doQuery,searchBase,activated">
    <el-card shadow="never" class="m-card is-bg is-overflow-hidden">
      <div class="f-plr20 f-pt20">
        <!--条件查询-->
        <hb-search-wrapper @reset="resetQueryParams" class="m-query is-border-bottom">
          <el-form-item label="姓名">
            <el-input v-model="queryParams.userName" clearable placeholder="请输入姓名" />
          </el-form-item>
          <el-form-item label="登录账号" v-if="queryShowLoginAccount.isShowLoginAccount">
            <el-input v-model="queryParams.loginAccount" clearable placeholder="请输入省平台ID" />
          </el-form-item>
          <el-form-item label="证件号">
            <el-input v-model="queryParams.idCard" clearable placeholder="请输入证件号" />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="queryParams.phone" clearable placeholder="请输入手机号" />
          </el-form-item>
          <el-form-item label="订单号">
            <el-input v-model="queryParams.orderNo" clearable placeholder="请输入订单号" @clear="clearUserId" />
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="searchBase">查询</el-button>
          </template>
        </hb-search-wrapper>
        <!--人员信息-->
        <el-collapse v-model="activeNames" class="m-collapse no-border">
          <el-collapse-item name="1">
            <div slot="title" class="m-tit">
              <span class="tit-txt">人员信息</span>
            </div>
            <!--表格-->
            <el-table
              :data="userTableData"
              v-loading="uiStatus.query.loadingPage"
              max-height="240"
              class="m-table"
              highlight-current-row
              @current-change="currentChangeRow"
              ref="userTableRef"
              v-if="userTableData.length"
            >
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="姓名" min-width="160" fixed="left">
                <template slot-scope="scope"> {{ scope.row.userName }} </template>
              </el-table-column>
              <el-table-column
                label="登录账号"
                min-width="160"
                fixed="left"
                v-if="queryShowLoginAccount.isShowLoginAccount"
              >
                <template slot-scope="scope"> {{ scope.row.loginAccount }} </template>
              </el-table-column>
              <el-table-column label="证件号" min-width="200">
                <template slot-scope="scope">{{ scope.row.idCard }}</template>
              </el-table-column>
              <el-table-column label="手机号" min-width="200">
                <template slot-scope="scope">{{ scope.row.phone }}</template>
              </el-table-column>
              <el-table-column label="单位地区" min-width="200">
                <template slot-scope="scope">
                  {{ transfromRegion(scope.row.region) }}
                </template>
              </el-table-column>
              <el-table-column label="注册时间" min-width="180">
                <template slot-scope="scope">{{ scope.row.createTime }}</template>
              </el-table-column>
            </el-table>
            <div class="m-no-date" v-if="!userTableData.length && isExecuteSearch">
              <div class="date-bd">
                <p class="f-f15 f-c9">暂无数据</p>
              </div>
            </div>
            <div class="m-no-date" v-if="!isExecuteSearch">
              <div class="date-bd">
                <p class="f-f15 f-c9">请先输入人员信息后进行查询</p>
              </div>
            </div>
            <!--分页-->
            <hb-pagination :page="page" v-bind="page"></hb-pagination>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    <!--顶部tab标签-->
    <el-tabs
      v-model="activeName"
      class="m-tab-top is-border-top is-sticky"
      @tab-click="handleClick"
      v-loading="tabLoading"
    >
      <template v-if="$hasPermission('basicData')" desc="学员信息" actions="@BasicData">
        <el-tab-pane label="学员信息" name="basic-data" :lazy="true">
          <basic-data ref="basicData"></basic-data>
        </el-tab-pane>
      </template>
      <template v-if="$hasPermission('studyDontent')" desc="学习内容" actions="@StudyContent">
        <el-tab-pane label="学习内容" name="study-content" :lazy="true">
          <study-content ref="studyContent" :user-id="selectedId" :baseConfig="baseConfig"></study-content>
        </el-tab-pane>
      </template>

      <el-tab-pane
        label="售后信息"
        name="change-class"
        v-if="$hasPermission('queryExchangeClass')"
        desc="查看换班信息"
        actions="@ChangeClass"
        :lazy="true"
      >
        <change-class ref="changeClass" :user-id="selectedId"></change-class>
      </el-tab-pane>
      <template v-if="$hasPermission('orderInfo')" desc="订单详情" actions="@OrderInfo">
        <el-tab-pane label="订单信息" name="order-info" :lazy="true">
          <order-info ref="orderInfo" :user-id="selectedId"></order-info>
        </el-tab-pane>
      </template>
      <template v-if="$hasPermission('invoiceInfo')" desc="发票信息" actions="@InvoiceInfo">
        <el-tab-pane label="发票信息" name="invoice-info" :lazy="true">
          <invoice-info ref="invoiceInfo" :user-id="selectedId"></invoice-info>
        </el-tab-pane>
      </template>
      <template v-if="$hasPermission('refundInfo')" desc="退款信息" actions="@RefundInfo">
        <el-tab-pane label="退款信息" name="refund-info" :lazy="true">
          <refund-info ref="refundInfo" :user-id="selectedId"></refund-info>
        </el-tab-pane>
      </template>

      <template v-if="$hasPermission('studyRecords')" desc="学习档案" actions="@StudyRecords">
        <el-tab-pane label="学习档案" name="study-records" :lazy="true">
          <study-records ref="studyRecords" :user-id="selectedId"></study-records>
        </el-tab-pane>
      </template>
    </el-tabs>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import BasicData from '@hbfe/jxjy-admin-customerService/src/personal/components/basic-data.vue'
  import ChangeClass from '@hbfe/jxjy-admin-customerService/src/personal/components/change-class.vue'
  import InvoiceInfo from '@hbfe/jxjy-admin-customerService/src/personal/components/invoice-info.vue'
  import OrderInfo from '@hbfe/jxjy-admin-customerService/src/personal/components/order-info.vue'
  import RefundInfo from '@hbfe/jxjy-admin-customerService/src/personal/components/refund-info.vue'
  import StudyContent from '@hbfe/jxjy-admin-customerService/src/personal/components/study-content.vue'
  import StudyRecords from '@hbfe/jxjy-admin-customerService/src/personal/components/study-records.vue'
  import { ElTable } from 'element-ui/types/table'
  import UserModule from '@api/service/management/user/UserModule'
  import StudentQueryVo from '@api/service/management/user/query/student/vo/StudentQueryVo'
  import StudentUserInfoVo from '@api/service/management/user/query/student/vo/StudentUserInfoVo'
  import { RegionModel } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import QueryStudentList from '@api/service/management/user/query/student/QueryStudentList'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'
  import BaseConfig from '@hbfe-biz/biz-anticheat/dist/config/BaseConfig'

  @Component({
    components: { BasicData, ChangeClass, InvoiceInfo, OrderInfo, RefundInfo, StudyContent, StudyRecords }
  })
  export default class extends Vue {
    @Ref('userTableRef')
    userTableRef: ElTable
    @Ref('orderInfo') orderInfo: OrderInfo
    @Ref('basicData') basicData: BasicData
    @Ref('studyContent') studyContent: StudyContent
    @Ref('changeClass') changeClass: ChangeClass
    @Ref('invoiceInfo') invoiceInfo: InvoiceInfo
    @Ref('refundInfo') refundInfo: RefundInfo
    @Ref('studyRecords') studyRecords: StudyRecords

    // 页面状态控制
    uiStatus = {
      query: {
        loadingPage: false
      }
    }

    page: UiPage
    activeNames = ['1']
    activeName = 'basic-data'
    userTableData = new Array<StudentUserInfoVo>()
    // 查询用户列表实例
    queryStudentList = new QueryStudentList()
    queryParams = new StudentQueryVo()
    baseConfig = new BaseConfig()
    // 用户id
    selectedId = ''
    // 是否执行了查询操作
    isExecuteSearch = false
    // tab加载
    tabLoading = false

    constructor() {
      super()
      this.page = new UiPage(this.doQuery, this.doQuery)
    }
    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount
    // 表单数据首次加载结束，默认设置第一行数据
    @Watch('userTableData', {
      deep: true
    })
    loadingPageChange(val: Array<StudentUserInfoVo>) {
      if (val?.length) {
        this.selectedId = val[0]?.userId
        this.$nextTick(() => {
          this.userTableRef.setCurrentRow(val[0])
        })
      } else {
        this.selectedId = ''
      }
    }

    // 转换单位地区
    transfromRegion(region: RegionModel) {
      const temp = new Array<string>()
      if (region?.provinceName) {
        temp.push(region.provinceName)
      }
      if (region?.cityName) {
        temp.push(region.cityName)
      }
      if (region?.countyName) {
        temp.push(region.countyName)
      }
      return temp.join('-') || '-'
    }

    // 选中当前行
    async currentChangeRow(val: StudentUserInfoVo) {
      if (val) {
        this.selectedId = val?.userId
        this.changeSelect()
      }
    }

    // 点击查询
    async searchBase() {
      this.page.pageNo = 1
      await this.doQuery()
      if (!this.userTableData.length) {
        if (this.activeName == 'basic-data') {
          this.$nextTick(async () => {
            this.basicData.userId = ''
            await this.basicData.userIdChange('')
          })
        }
        if (this.activeName == 'study-content') {
          this.$nextTick(async () => {
            this.studyContent.userId = ''
            await this.studyContent.selectIdChange('')
          })
        }
        if (this.activeName == 'change-class') {
          this.$nextTick(async () => {
            this.changeClass.userId = ''
            await this.changeClass.userIdChange('')
          })
        }
        if (this.activeName == 'order-info') {
          this.$nextTick(async () => {
            this.orderInfo.userId = ''
            await this.orderInfo.userIdChange('')
          })
        }
        if (this.activeName == 'invoice-info') {
          this.$nextTick(async () => {
            this.invoiceInfo.userId = ''
            await this.invoiceInfo.userIdChange('')
          })
        }
        if (this.activeName == 'refund-info') {
          this.$nextTick(async () => {
            this.refundInfo.userId = ''
            await this.refundInfo.userIdChange('')
          })
        }
        if (this.activeName == 'study-records') {
          this.$nextTick(async () => {
            this.studyRecords.userId = ''
            await this.studyRecords.userIdChange('')
          })
        }
      }
    }

    // tab切换响应事件
    async handleClick() {
      await this.changeSelect()
    }

    async changeSelect() {
      this.tabLoading = true
      if (this.activeName == 'basic-data') {
        this.$nextTick(async () => {
          this.basicData.userId = this.selectedId
          await this.basicData.userIdChange(this.selectedId)
        })
      }
      if (this.activeName == 'study-content') {
        this.$nextTick(async () => {
          this.studyContent.userId = this.selectedId
          await this.studyContent.selectIdChange(this.selectedId)
        })
      }
      if (this.activeName == 'change-class') {
        this.$nextTick(async () => {
          this.changeClass.userId = this.selectedId
          await this.changeClass.userIdChange(this.selectedId)
        })
      }
      if (this.activeName == 'order-info') {
        this.$nextTick(async () => {
          this.orderInfo.userId = this.selectedId
          await this.orderInfo.userIdChange(this.selectedId)
        })
      }
      if (this.activeName == 'invoice-info') {
        this.$nextTick(async () => {
          this.invoiceInfo.userId = this.selectedId
          await this.invoiceInfo.userIdChange(this.selectedId)
        })
      }
      if (this.activeName == 'refund-info') {
        this.$nextTick(async () => {
          this.refundInfo.userId = this.selectedId
          await this.refundInfo.userIdChange(this.selectedId)
        })
      }
      if (this.activeName == 'study-records') {
        this.$nextTick(async () => {
          this.studyRecords.userId = this.selectedId
          await this.studyRecords.userIdChange(this.selectedId)
        })
      }
      this.tabLoading = false
    }

    // 查询学员列表
    async doQuery() {
      if (
        !this.queryParams.userName &&
        !this.queryParams.idCard &&
        !this.queryParams.phone &&
        !this.queryParams.orderNo &&
        !this.queryParams.loginAccount
      ) {
        this.userTableData = new Array<StudentUserInfoVo>()
        return
      }
      this.uiStatus.query.loadingPage = true

      const res = await this.queryStudentList.queryStudentListByCondition(this.page, this.queryParams)
      if (!res.status?.isSuccess()) {
        this.$message.error('学员列表获取失败！')
        return
      }
      this.userTableData = res.data
      this.isExecuteSearch = true
      this.uiStatus.query.loadingPage = false
    }

    activated() {
      this.searchBase()
      this.baseConfig.queryDetail()
    }

    // 重置查询条件
    resetQueryParams() {
      this.page = new UiPage()
      this.queryParams = new StudentQueryVo()
      this.searchBase()
    }
    clearUserId() {
      this.queryParams.userId = ''
    }
  }
</script>
