import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjy-distribution-pricing-strategy-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjy-distribution-pricing-strategy-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum Direction {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum DistributorCommoditySortEnum {
  RECORD_UPDATED_TIME = 'RECORD_UPDATED_TIME',
  RECORD_CREATED_TIME = 'RECORD_CREATED_TIME',
  DISTRIBUTION_END_TIME = 'DISTRIBUTION_END_TIME',
  SALE_CHANNEL_SORT = 'SALE_CHANNEL_SORT'
}

// 类

/**
 * 定价策略批量操作推广请求, 根据条件
<AUTHOR>
@date 2024-08-28
 */
export class PricingStrategyBatchOperatorPromotionByConditionRequest {
  /**
   * 未选择时 根据查询条件操作数据
   */
  distributorCommodityAndRelationInfo?: DistributorCommodityAndRelationRequest
  /**
   * 未选择时 根据查询条件操作数据
   */
  sortRequests?: Array<DistributorCommoditySortRequest>
  /**
   * 当列表不为空则只操作该列表 - 定价策略ID 列表
   */
  pricingSchemeIds?: Array<string>
  /**
   * 【必填】销售渠道ID
   */
  saleChannelId?: string
  /**
   * 【必填】操作状态
   */
  promotionStatus?: boolean
}

/**
 * 定价策略批量移除销售渠道的定价方案请求, 根据条件
<AUTHOR>
@date 2024-08-28
 */
export class PricingStrategyBatchRemoveSaleChannelByConditionRequest {
  /**
   * 未选择时 根据查询条件操作数据
   */
  distributorCommodityAndRelationInfo?: DistributorCommodityAndRelationRequest
  /**
   * 未选择时 根据查询条件操作数据
   */
  sortRequests?: Array<DistributorCommoditySortRequest>
  /**
   * 当列表不为空则只操作该列表 - 定价策略ID 列表
   */
  pricingSchemeIds?: Array<string>
  /**
   * 【必填】销售渠道ID
   */
  saleChannelId?: string
}

/**
 * 商品sku属性查询条件
 */
export class PropertyRequest {
  /**
   * 商品skuKey
   */
  propertyKey?: string
  /**
   * 商品skuValue
   */
  propertyValue?: string
}

/**
 * 供应商授权出的商品
 */
export class DistributorCommodityAndRelationRequest {
  /**
   * 分销商商品名称
   */
  saleTitle?: string
  /**
   * 分销商id集合
   */
  distributorIdList?: Array<string>
  /**
   * 分销商等级
   */
  distributorLevel?: number
  /**
   * 销售状态 1-有效 2-无效
   */
  saleStatus?: number
  /**
   * 分销地区路径
   */
  contractDistributionRegionPathList?: Array<string>
  /**
   * 商品sku属性
   */
  propertyList?: Array<PropertyRequest>
  /**
   * 培训方案类型
   */
  schemeTypeList?: Array<string>
  /**
   * 授权价格类型 1-固定 2-区间
   */
  priceType?: number
  /**
   * 分销价格范围查询-最大价格
   */
  maxPrice?: number
  /**
   * 分销价格范围查询-最小价格
   */
  minPrice?: number
  /**
   * 分销价格-最大价格
   */
  policyMaxPrice?: number
  /**
   * 分销价格-最小价格
   */
  policyMinPrice?: number
  /**
   * 定价方案状态
   */
  statusList?: Array<number>
  /**
   * 分销是否有效
0-有效 1-无效
商品的分销开始时间、结束时间作为判断
   */
  distributionStatus?: number
  /**
   * 分销商商品id集合
   */
  distributorCommodityIdList?: Array<string>
  /**
   * 商品id集合
   */
  commodityIdList?: Array<string>
  /**
   * 网校原始商品id集合
   */
  rootCommodityIdList?: Array<string>
  /**
   * 需要排除的网校原始商品id集合
   */
  excludedRootCommodityIdList?: Array<string>
  /**
   * 网校id
   */
  onlineSchoolId?: string
  /**
   * 培训方案名称
   */
  schemeName?: string
  /**
   * 网校销售状态
0-开启 1-关闭
商品的网校销售开始时间、结束时间作为判断
   */
  onlineSchoolStatus?: number
  /**
   * 授权商品来源类型
   */
  commoditySourceTypeList?: Array<number>
  /**
   * 定价方案id
   */
  productPricingSchemeIdList?: Array<string>
  /**
   * 需要排除的定价方案id
   */
  excludedPricingSchemeIdList?: Array<string>
  /**
   * 是否存在定价方案
   */
  existPricingScheme?: boolean
  /**
   * 是否已启用定价方案
   */
  enablePricingScheme?: boolean
  /**
   * 是否已启用优惠申请
   */
  enableDiscountScheme?: boolean
  /**
   * 推广门户标识id
   */
  portalIdentify?: string
  /**
   * 推广门户展示名称
   */
  showName?: string
  /**
   * 推广门户简称
   */
  shortName?: string
  /**
   * 门户域名
   */
  domainName?: string
  /**
   * 门户状态
0-停用 1-启用
   */
  portalStatus?: number
  /**
   * 门户展示 (0-不展示, 1-展示）
   */
  showPortal?: number
  /**
   * 门户推广 (0-不推广, 1-推广）
   */
  portalPromotion?: number
  /**
   * 商品上架状态
0-下架 1-上架
   */
  shelveStatus?: number
  /**
   * 销售渠道类型
0-自营渠道 1-分销渠道 2-专题渠道 3-华医网 4-推广门户渠道
   */
  saleChannelType?: number
  /**
   * 销售渠道id
   */
  saleChannelId?: string
  /**
   * 优惠申请审批状态
0-待处理 1-通过 2-未通过
   */
  auditStatusList?: Array<number>
  /**
   * 优惠状态
1-开启 2-关闭
与优惠申请内容状态有关、与优惠周期约束、优惠开始时间、优惠结束时间有关
   */
  discountStatusList?: Array<number>
}

export class DistributorCommoditySortRequest {
  /**
   * 排序字段
   */
  field?: DistributorCommoditySortEnum
  /**
   * 正序或倒序
   */
  policy?: Direction
}

export class PricingStrategyMessageRecord {
  pricingSchemeId: string
  message: string
}

/**
 * 定价方案 批量操作结果响应
 */
export class PricingStrategyBatchResultResponse {
  /**
   * 定价策略ID以及对应的错误信息
   */
  pricingStrategyMessageRecords: Array<PricingStrategyMessageRecord>
  /**
   * 状态码
@see com.fjhb.ms.distribution.v1.api.consts.PricingStrategyStatusEnum
枚举如下：
(&quot;200&quot;, &quot;成功&quot;)
(&quot;500&quot;, &quot;失败&quot;)
(&quot;501&quot;, &quot;存在销售价格相同的定价方案&quot;) &#x3D;> 返回已存在的相同定价方案ID
(&quot;502&quot;, &quot;定价方案已有关联订单，无法删除&quot;)
(&quot;503&quot;, &quot;销售价格不在分销商品定价范围内，无法启用&quot;)
(&quot;504&quot;, &quot;无法同时符合三个条件&quot;) &#x3D;> 返回定价方案无法设置推广的 定价方案ID 以及原因
(&quot;505&quot;, &quot;同一个分销商品存在其他定价方案展示在门户&quot;) &#x3D;> 返回已设置推广的 定价方案ID
   */
  code: string
  /**
   * 响应信息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量将定价方案开启/关闭推广
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pricePlanBatchOperatorPromotion(
    request: PricingStrategyBatchOperatorPromotionByConditionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.pricePlanBatchOperatorPromotion,
    operation?: string
  ): Promise<Response<PricingStrategyBatchResultResponse>> {
    return commonRequestApi<PricingStrategyBatchResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量移除销售渠道的定价方案
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pricePlanBatchRemoveSaleChannel(
    request: PricingStrategyBatchRemoveSaleChannelByConditionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.pricePlanBatchRemoveSaleChannel,
    operation?: string
  ): Promise<Response<PricingStrategyBatchResultResponse>> {
    return commonRequestApi<PricingStrategyBatchResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
