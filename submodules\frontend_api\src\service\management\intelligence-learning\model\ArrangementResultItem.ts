import TrackingItem from '@api/service/management/intelligence-learning/model/TrackingItem'
import OrderStateType from '@api/service/management/intelligence-learning/enum/OrderStatusEnum'
import { MetaRow } from '@api/ms-gateway/ms-importopen-v1'
import LearningStatusType from '@api/service/management/intelligence-learning/enum/LearningStatusEnum'

/**
 * 编排结果Item
 */
export default class ArrangementResultItem extends TrackingItem {
  /**
   * 开通状态/订单状态
   */
  orderState: OrderStateType = null
  /**
   * 开通失败原因
   */
  errorMessage = ''
  /**
   * 完成时间
   */
  endTime = ''
  /**
   * 是否更新密码
   */
  updatePassword = false
  /**
   * 是否更新基础信息
   */
  updateBasicInfo = false
  /**
   * 智能选课/学习编排状态
   */
  learningState: LearningStatusType = null

  /**
   * 学员状态
   */
  private newStudent: number = null
  /**
   * 是否更新密码/基础信息
   */
  get isRejuvenation() {
    if (this.newStudent === null) {
      return '暂无记录'
    } else if (this.newStudent === 1) {
      return '-'
    } else {
      return `${this.updatePassword ? '是' : '否'}/${this.updateBasicInfo ? '是' : '否'}`
    }
  }

  /**
   * 后端返回数据转换前端模型
   */
  static from(metaRow: MetaRow) {
    const temp = new ArrangementResultItem()
    metaRow.row.map((item) => {
      if (item.key == 'userInfo_name') {
        temp.name = item.value
      }
      if (item.key == 'userInfo_idCard') {
        temp.idCard = item.value
      }
      if (item.key == 'userInfo_phone') {
        temp.phone = item.value
      }
      if (item.key == 'signUp_schemeName') {
        temp.trainingSchemeName = item.value
      }
    })
    temp.newStudent = metaRow.studentState
    temp.orderNo = metaRow.orderNo
    temp.endTime = metaRow.completeTime
    temp.startTime = metaRow.createTime
    temp.errorMessage = metaRow.errorMessage
    temp.orderState = new OrderStateType(metaRow.orderState)
    temp.learningState = new LearningStatusType(metaRow.learningState)
    return temp
  }
}
