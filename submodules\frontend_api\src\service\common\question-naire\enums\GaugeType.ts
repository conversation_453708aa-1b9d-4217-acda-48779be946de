import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum GaugeTypeEnum {
  /**
   * 自定义
   */
  customer = 0,
  /**
   * 满意度
   */
  satisfaction = 1,
  /**
   * 认同度
   */
  degree = 2,
  /**
   * 重要度
   */
  importance = 3,
  /**
   * 愿意度
   */
  want = 4,
  /**
   * 符合度
   */
  conformity = 5
}
class GaugeType extends AbstractEnum<GaugeTypeEnum> {
  static enum = GaugeTypeEnum

  constructor(status?: GaugeTypeEnum) {
    super()
    this.current = status
    this.map.set(GaugeTypeEnum.customer, '自定义')
    this.map.set(GaugeTypeEnum.satisfaction, '满意度')
    this.map.set(GaugeTypeEnum.degree, '认同度')
    this.map.set(GaugeTypeEnum.importance, '重要度')
    this.map.set(GaugeTypeEnum.want, '愿意度')
    this.map.set(GaugeTypeEnum.conformity, '符合度')
  }
}
export default GaugeType
