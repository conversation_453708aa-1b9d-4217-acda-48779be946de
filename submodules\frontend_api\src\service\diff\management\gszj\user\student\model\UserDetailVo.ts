import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
import TopicInformationVo from '@api/service/diff/management/gszj/user/student/model/TopicInformationVo'
import { UpdateStudentSystemDifferentiationRequest } from '@api/platform-gateway/platform-training-channel-user-v1'
import { AreaTrainingChannelInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { Response } from '@hbfe/common'
import { DictionaryTypeEnum } from '@api/service/diff/common/gszj/basic-data-dictionary/query/enum/DictionaryType'
import QueryDictionaryType from '@api/service/diff/common/gszj/basic-data-dictionary/query/QueryDictionaryType'

/*
 * 用户详情
 */
class UserDetailVoDiff extends UserDetailVo {
  /**
   * 专题信息
   */
  topicInformationVo = new TopicInformationVo()

  static from(userDetailVo: Response<UserDetailVo>, userInfoListDiff: Array<AreaTrainingChannelInfoResponse>) {
    const response = new Response<UserDetailVoDiff>()
    response.data = Object.assign(new UserDetailVoDiff(), userDetailVo?.data)
    const userInfoDiff = userInfoListDiff.find((item) => {
      return item.regionId === '620300'
    })
    if (userInfoDiff?.userId) {
      response.data.topicInformationVo.natureWorkUnit = userInfoDiff.unitNature
      response.data.topicInformationVo.natureWorkUnitName = this.getDictionaryTypeName(
        DictionaryTypeEnum.GSZJ_UNIT_NATURE,
        userInfoDiff.unitNature
      )
      response.data.topicInformationVo.currentSituation = userInfoDiff.staffingStatus
      response.data.topicInformationVo.currentSituationName = this.getDictionaryTypeName(
        DictionaryTypeEnum.GSZJ_STAFFING_STATUS,
        userInfoDiff.staffingStatus
      )
      response.data.topicInformationVo.isOnTechnicalPost = userInfoDiff.isZJPosition
      
      if (userInfoDiff.isZJPosition === '1') {
        response.data.topicInformationVo.isOnTechnicalPostName = '是'
      } else if (userInfoDiff.isZJPosition === '0') {
        response.data.topicInformationVo.isOnTechnicalPostName = '否'
      } else {
        response.data.topicInformationVo.isOnTechnicalPostName = ''
      }

      response.data.topicInformationVo.titleSeries = userInfoDiff.titleSeries
      response.data.topicInformationVo.titleSeriesName = this.getDictionaryTypeName(
        DictionaryTypeEnum.GSZJ_TITLE_SERIES,
        userInfoDiff.titleSeries
      )

      response.data.topicInformationVo.professionalTitle = userInfoDiff.titleProfessional

      response.data.topicInformationVo.currentTitleLevel = userInfoDiff.titleGrade
      response.data.topicInformationVo.currentTitleLevelName = this.getDictionaryTypeName(
        DictionaryTypeEnum.GSZJ_TITLE_GRADE,
        userInfoDiff.titleGrade
      )
      response.data.topicInformationVo.currentTitleQualification = userInfoDiff.titleQualificationName

      response.data.topicInformationVo.currentTitleValidRange = userInfoDiff.titleEffectiveRange
      response.data.topicInformationVo.currentTitleValidRangeName = this.getDictionaryTypeName(
        DictionaryTypeEnum.GSZJ_TITLE_EFFECTIVE_RANGE,
        userInfoDiff.titleEffectiveRange
      )

      response.data.topicInformationVo.highestEducation = userInfoDiff.highestEducationLevel
    }

    return response
  }

  static getDictionaryTypeName(type: DictionaryTypeEnum, value: string) {
    const data = QueryDictionaryType.dictionaryTypeMap.get(type)
    return data.find((item) => {
      return item.id === value
    })?.name
  }
}

export default UserDetailVoDiff
