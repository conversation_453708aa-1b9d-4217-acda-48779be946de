import { ServicerTokenResponse } from '@api/ms-gateway/ms-servicer-v1'
import { TrainingChannelPageResponse } from '@api/platform-gateway/platform-training-channel-fore-gateway'

class BusinessEnvironment {
  static ContentTypeKey = 'App-Service-Provider'
  serviceToken = new ServicerTokenResponse()
  // * 专题信息
  specialTopicsInfo = new TrainingChannelPageResponse()
  // * 上级网校信息
  superiorServiceInfo = new ServicerTokenResponse()
}

export default BusinessEnvironment
