import AbstractApplyToken from '@api/service/common/token/AbstractApplyToken'
import MsChooseCourseLearningSceneV1 from '@api/ms-gateway/ms-choosecourselearningscene-v1'
import { ResponseStatus } from '@hbfe/common'

/**
 * 学习课程token
 */
class ApplyCourseLearningToken extends AbstractApplyToken {
  private readonly applyStudentLearningToken: string
  private readonly courseId: string

  constructor(courseId: string, applyStudentLearningToken: string) {
    super()
    this.applyStudentLearningToken = applyStudentLearningToken
    this.courseId = courseId
  }

  async apply(): Promise<ResponseStatus> {
    // 学习方案场景申请用户 token
    const tokenValue = await MsChooseCourseLearningSceneV1.applyCourseLearning({
      studentLearningToken: this.applyStudentLearningToken,
      studentCourseId: this.courseId
    })
    this.token = tokenValue.data.token
    return new ResponseStatus(tokenValue.status.code, tokenValue.status.message)
  }
}

export default ApplyCourseLearningToken
