import MsMediaResourceLearningV1, {
  LearningCourseRecordBeTokenResponse,
  LearningCoursewareRecordResponse,
  LearningMediaRecordResponse
} from '@api/ms-gateway/ms-media-resource-learning-v1'
import { ResponseStatus } from '@hbfe/common'
import AntiCheatModule from '@api/service/customer/learning/anti-cheat/AntiCheatModule'
import { AntiCheatEventEnum } from '@api/service/customer/learning/anti-cheat/enums/AntiCheatEventEnum'
import AntiResponseHeaderModel from '@api/service/customer/learning/anti-cheat/models/AntiResponseHeaderModel'

class LearningCourseRecord extends LearningCourseRecordBeTokenResponse {
  private readonly getCourseResourceToken: string

  constructor(getCourseResourceToken: string) {
    super()
    this.getCourseResourceToken = getCourseResourceToken
  }

  // 获取最后一个播放的媒体资源
  get lastPlayMedia(): LearningMediaRecordResponse {
    let lastLearningMediaRecord = new LearningMediaRecordResponse()
    if (this.learningCoursewareRecords) {
      this.learningCoursewareRecords.forEach((coursewareRecord: LearningCoursewareRecordResponse) => {
        coursewareRecord.learningMediaRecords.forEach((learningMedia: LearningMediaRecordResponse) => {
          if (learningMedia.isLastLearning) {
            lastLearningMediaRecord = learningMedia
          }
        })
      })
      if (lastLearningMediaRecord.coursewareId) {
        return lastLearningMediaRecord
      }
    }
    return undefined
  }

  /**
   * 查询课程学习资源的学习记录
   */
  async queryCoursewareLearningRecords() {
    const result = await MsMediaResourceLearningV1.prepareCourseLearningTimingBeToken(this.getCourseResourceToken)
    if (result.status.isSuccess()) {
      return this.from(result.data)
    }
    return Promise.reject(new ResponseStatus(500, '准备视频播放过程异常'))
  }

  /**
   * 根据课件 id 查询课件学习记录
   * @param coursewareId
   */
  getCoursewareRecordByCoursewareId(coursewareId: string) {
    return this.learningCoursewareRecords?.find((learningResponse: LearningCoursewareRecordResponse) => {
      return learningResponse.coursewareId === coursewareId
    })
  }

  from(lcrResponse: LearningCourseRecordBeTokenResponse) {
    Object.assign(this, lcrResponse)
  }
}

export default LearningCourseRecord
