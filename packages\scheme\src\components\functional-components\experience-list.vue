<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <div class="f-p20">
      <div class="f-flex f-align-center">
        <div class="f-mr10">
          <el-button @click="addStudyNotes()" type="primary" size="small" plain>新增学习心得</el-button>
          <el-button @click="removeAllCourse" type="primary" size="small" plain>一键移除</el-button>
          <el-button @click="requireManage()" type="primary" size="small" plain>必选管理</el-button>
        </div>
        <div class="f-fb f-flex-sub">
          一共 <i class="f-cr">{{ learningFeelInfo.experienceList.length }}</i> 个，必选<i class="f-cr">{{
            learningFeelInfo.requireCount
          }}</i
          >个
        </div>
      </div>
      <el-table
        stripe
        max-height="500px"
        class="m-table f-mt15"
        ref="experienceTableRef"
        :data="experienceList"
        @selection-change="handleSelectionChange"
        @select="handleSelect"
        :row-key="(row) => row.id"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="主题" min-width="240" fixed="left" prop="theme">
          <template v-slot="{ row }">
            <el-tag type="primary" effect="dark" size="mini" v-if="row.isRequired">必选</el-tag>
            {{ row.theme }}
          </template>
        </el-table-column>
        <el-table-column label="参加时间" min-width="240" align="center" prop="joinTime">
          <template v-slot="{ row }">
            {{ timeChange(row.joinTime) }}
          </template>
        </el-table-column>
        <el-table-column label="学习心得类型" min-width="100" align="center" prop="experienceType">
          <template v-slot="{ row }">
            {{ row.experienceType }}
          </template>
        </el-table-column>
        <el-table-column label="作答形式" min-width="100" align="center" prop="answerType">
          <template v-slot="{ row }">
            {{ row.answerType }}
          </template>
        </el-table-column>
        <el-table-column label="审核方式" min-width="100" align="center" prop="checkType">
          <template v-slot="{ row }">
            {{ row.checkType }}
          </template>
        </el-table-column>
        <el-table-column label="总分" min-width="60" align="center" prop="score"></el-table-column>
        <el-table-column label="提交次数" width="160" align="center" prop="submitCount">
          <template v-slot="{ row }">
            {{ submitNumber(row) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template v-slot="{ row }">
            <el-button type="text" size="mini" @click="editStudyNotes(row)">编辑</el-button>
            <el-button type="text" size="mini" @click="removeStudyNotes(row.id)" v-if="routerMode != 3">移除</el-button>
            <el-tooltip placement="bottom" v-else>
              <div slot="content">
                心得移除对已合格学员不影响，未合格学员如<br />已参加此心得，参加记录仍保留且可进行操作
              </div>
              <el-button type="text" size="mini" @click="removeStudyNotes(row.id)">移除</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <hb-pagination :page="experiencePage" v-bind="experiencePage"></hb-pagination>
    </div>
    <add-study-notes-drawer
      ref="addStudyNotesDrawer"
      @refreshPage="pageData"
      :routerMode="routerMode"
      :classification="classification"
      :course-learning="courseLearning"
      :trainSchemeDetail.sync="schemeDetail"
      :learningFeelInfo="learningFeelInfo"
    ></add-study-notes-drawer>
    <require-manage-drawer ref="requireManageDrawer" :learningFeelInfo="learningFeelInfo"></require-manage-drawer>
  </el-card>
</template>

<script lang="ts">
  import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator'
  import ExperienceItem from '@api/service/management/train-class/mutation/vo/ExperienceItem'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import LearningExperience from '@api/service/management/train-class/mutation/vo/LearningExperience'
  import { UiPage } from '@hbfe/common'
  import AddStudyNotesDrawer from '@hbfe/jxjy-admin-scheme/src/components/functional-components/add-study-notes-drawer.vue'
  import RequireManageDrawer from '@hbfe/jxjy-admin-scheme/src/components/functional-components/require-manage-drawer.vue'
  import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
  import ModifyChooseCourseOutline from '@hbfe/jxjy-admin-scheme/src/components/functional-components/modify-choose-course-outline.vue'
  import { LearningExperienceEnum } from '@api/service/management/train-class/mutation/Enum/LearningExperienceEnum'
  import { ElTable } from 'element-ui/types/table'
  import CourseLearningLearningType from '@api/service/management/train-class/mutation/vo/CourseLearningLearningType'

  @Component({
    components: { ModifyChooseCourseOutline, AddStudyNotesDrawer, RequireManageDrawer }
  })
  export default class extends Vue {
    @Ref('addStudyNotesDrawer') addStudyNotesDrawer: AddStudyNotesDrawer
    @Ref('requireManageDrawer') requireManageDrawer: RequireManageDrawer
    @Ref('experienceTableRef') experienceTableRef: ElTable
    /**
     * 学习心得配置 - 双向绑定
     */
    @Prop({
      type: LearningExperience,
      default: () => {
        return new LearningExperience()
      }
    })
    learningFeelInfo!: LearningExperience

    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number

    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: TrainClassBaseModel }) schemeDetail!: TrainClassBaseModel

    /**
     * 接收大纲树
     */
    @Prop({
      type: Object,
      default: () => new Classification()
    })
    classification: Classification

    /**
     * 课程学习信息
     */
    @Prop({
      required: true,
      type: CourseLearningLearningType
    })
    courseLearning: CourseLearningLearningType

    /**
     * 学习心得列表
     */
    experienceList = new Array<ExperienceItem>()
    experiencePage: UiPage

    /**
     * 勾选列表
     */
    checkedList = new Array<ExperienceItem>()

    /**
     * 勾选列表存储
     */
    checkedNoList = new Array<string>()

    constructor() {
      super()
      this.experiencePage = new UiPage(this.pageData, this.pageData)
    }

    get timeChange() {
      return (item: string[]) => {
        if (!item.length) {
          return '-'
        } else if (item[0] == '1900-01-01 00:00:00' && item[1] == '2100-01-01 00:00:00') {
          return '长期有效'
        }
        return item[0] + '至' + item[1]
      }
    }

    /**
     * 心得列表分页
     */
    async pageData() {
      this.experienceList = this.learningFeelInfo.pageData(this.experiencePage)
      ;(this.$refs['experienceTableRef'] as any)?.doLayout()
    }

    /**
     * 添加学习心得
     */
    addStudyNotes() {
      if (!this.validatePermission()) return
      this.addStudyNotesDrawer.showAddStudyNotesDialog()
    }

    /**
     * 打开必选管理
     */
    requireManage() {
      if (!this.validatePermission()) return
      this.learningFeelInfo.selectRequireInit()
      this.requireManageDrawer.showRequireManageDialog()
    }

    /**
     * 编辑学习心得
     */
    async editStudyNotes(item: ExperienceItem) {
      if (!item.isInfoShow) {
        if (item.experienceType.equal(LearningExperienceEnum.course_experience)) {
          await item.getCourseInfo()
        }
      }
      item.isInfoShow = true
      this.addStudyNotesDrawer.showAddStudyNotesDialog(item, true)
    }

    /**
     * 移除单个学习心得
     */
    removeStudyNotes(id: string) {
      if (this.routerMode == 3 && this.experienceList.length - 1 < this.learningFeelInfo.joinCount) {
        this.$alert('移除后配置的心得数不足考核要求，需要重新添加心得或减少考核数，是否确认移除?', '提示', {
          confirmButtonText: '确认',
          type: 'warning'
        })
          .then(() => {
            const res = this.checkedNoList.indexOf(id)
            if (res > -1 && this.checkedNoList.length == 1) {
              ;(this.$refs['experienceTableRef'] as any)?.clearSelection()
            } else {
              this.checkedNoList.splice(res, 1)
            }
            this.learningFeelInfo.remove([id])

            this.pageData()
          })
          .catch(() => {
            console.log('用户点击取消')
          })
      } else {
        this.$alert('移除后学习心得内容不作保留，确认要移除？', '提示', {
          confirmButtonText: '确认',
          type: 'warning'
        })
          .then(() => {
            const res = this.checkedNoList.indexOf(id)
            if (res > -1 && this.checkedNoList.length == 1) {
              ;(this.$refs['experienceTableRef'] as any)?.clearSelection()
            } else {
              this.checkedNoList.splice(res, 1)
            }
            this.learningFeelInfo.remove([id])

            this.pageData()
          })
          .catch(() => {
            console.log('用户点击取消')
          })
      }
    }

    /**
     * 一键移除课程
     */
    removeAllCourse() {
      if (!this.validatePermission()) {
        return
      }
      if (!this.experienceList.length) {
        this.$message.error('当前暂无学习心得，移除失败。')
        return
      }
      if (this.experienceList.length && !this.checkedNoList.length) {
        this.$message.error('请选择需要移除的学习心得。')
        return
      }
      this.$alert('一键移除后学习心得内容不作保留，确认要移除？', '提示', {
        confirmButtonText: '确认',
        type: 'warning'
      })
        .then(() => {
          this.learningFeelInfo.remove(this.checkedNoList)
          this.pageData()
          // 选中复选框清除
          ;(this.$refs['experienceTableRef'] as any)?.clearSelection()
          console.log('用户点击确定')
        })
        .catch(() => {
          console.log('用户点击取消')
        })
    }

    handleSelectionChange(selection: Array<ExperienceItem>) {
      this.checkedList = selection
      this.checkedNoList = this.checkedList.map((item) => {
        return item.id
      })
    }

    handleSelect(selection: Array<ExperienceItem>, row: any) {
      console.log('选择方法', selection, row, this.checkedNoList)
    }

    /**
     * 校验是否允许操作
     */
    validatePermission() {
      let result = true
      if (!this.learningFeelInfo.isSelected) {
        result = false
        this.$message.error('请先勾选“学习心得”的内容模块，再配置对应内容！')
      }
      return result
    }

    /**
     * 判断提交次数
     */
    submitNumber(item: ExperienceItem) {
      if (item.checkType.current == 1) {
        return '--'
      } else {
        if (item.submitCountType) {
          return item.submitCount
        } else {
          return '不限次'
        }
      }
    }

    init() {
      // 选中复选框清除
      ;(this.$refs['experienceTableRef'] as any)?.clearSelection()
      this.experiencePage.currentChange(1)
    }

    async created() {
      this.init()
    }
  }
</script>
