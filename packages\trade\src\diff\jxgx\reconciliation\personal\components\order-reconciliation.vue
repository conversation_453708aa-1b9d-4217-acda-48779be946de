<template>
  <jxgx-order-reconciliation ref="orderReconciliationRef"></jxgx-order-reconciliation>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import OrderReconciliation from '@hbfe/jxjy-admin-trade/src/reconciliation/personal/components/order-reconciliation.vue'
  import TradeExport from '@api/service/diff/management/jxgx/trade/TradeExport'

  @Component
  class JxgxOrderReconciliation extends OrderReconciliation {
    /**
     * 订单导出
     */
    tradeExport = new TradeExport()

    /**
     * 导出数据
     */
    async exportDataty() {
      return await this.tradeExport.listExport(this.exportParam)
    }

    /**
     * 导出数据分销
     */
    async exportDatafx() {
      return await this.tradeExport.listFxExport(this.exportParam)
    }
  }
  @Component({
    components: {
      JxgxOrderReconciliation
    }
  })
  export default class extends Vue {
    @Ref('orderReconciliationRef') orderReconciliationRef: JxgxOrderReconciliation

    /**
     * 搜索分销
     */
    doSearchfx() {
      this.orderReconciliationRef.doSearchfx()
    }

    /**
     * 搜索专题
     */
    doSearchzt() {
      this.orderReconciliationRef.doSearchzt()
    }

    /**
     * 搜索网校
     */
    doSearch() {
      this.orderReconciliationRef.doSearch()
    }
  }
</script>
