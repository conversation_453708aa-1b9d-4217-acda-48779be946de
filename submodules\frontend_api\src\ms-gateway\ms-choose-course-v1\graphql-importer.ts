import chooseCourse from './mutates/chooseCourse.graphql'
import createStudentCourse from './mutates/createStudentCourse.graphql'
import deleteCourseTrainingOutlineAndUsage from './mutates/deleteCourseTrainingOutlineAndUsage.graphql'
import prepareChooseCourse from './mutates/prepareChooseCourse.graphql'
import smartChooseCourse from './mutates/smartChooseCourse.graphql'

export {
  chooseCourse,
  createStudentCourse,
  deleteCourseTrainingOutlineAndUsage,
  prepareChooseCourse,
  smartChooseCourse
}
