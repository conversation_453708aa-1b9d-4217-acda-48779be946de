import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 考试方式枚举
 * on_call_exam 随到随考
 * fixed_exam 固定考试
 */
export enum ExamMethodEnum {
  on_call_exam = 0,
  fixed_exam = 1
}

/**
 * @description 考试方式
 */
class ExamMethod extends AbstractEnum<ExamMethodEnum> {
  static enum = ExamMethodEnum

  constructor(status?: ExamMethodEnum) {
    super()
    this.current = status
    this.map.set(ExamMethodEnum.on_call_exam, '随到随考')
    this.map.set(ExamMethodEnum.fixed_exam, '固定考试')
  }
}

export default new ExamMethod()
