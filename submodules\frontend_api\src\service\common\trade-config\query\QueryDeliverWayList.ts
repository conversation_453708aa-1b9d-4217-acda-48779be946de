import MsOfflineinvoiceV1 from '@api/ms-gateway/ms-offlineinvoice-v1'
import { ResponseStatus } from '@hbfe/common'
import PickUpWayListDetail from '@api/service/common/trade-config/query/vo/PickUpWayListDetail'

/**
 * 查询网校配送方式列表
 */
class QueryDeliverWayList {
  detailList: Array<PickUpWayListDetail> = new Array<PickUpWayListDetail>()

  get typeList() {
    return this.detailList.map((detail: PickUpWayListDetail) => {
      return detail.code
    })
  }

  /**
   * 是否有配置信息
   */
  hasConfig() {
    return this.detailList.length
  }

  async queryList(): Promise<ResponseStatus> {
    const { data, status } = await MsOfflineinvoiceV1.queryShippingMethodsForSchool()
    this.detailList = data.shippingMethods.map(method => {
      return PickUpWayListDetail.from(method)
    })
    return status
  }
}

export default QueryDeliverWayList
