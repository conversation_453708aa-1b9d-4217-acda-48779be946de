/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-12-13 10:12:57
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-12-21 14:39:59
 * @Description:
 */

/**
 * 培训形式
 */
import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum BizTypeEnum {
  TYPT_KCXX = 'TYPT_KCXX' //课程信息
}
class BizType extends AbstractEnum<BizTypeEnum> {
  static enum = BizTypeEnum
  constructor(status?: BizTypeEnum) {
    super()
    this.current = status
    this.map.set(BizTypeEnum.TYPT_KCXX, '课程信息	') // owner:网校ID
  }
}
export default new BizType()
