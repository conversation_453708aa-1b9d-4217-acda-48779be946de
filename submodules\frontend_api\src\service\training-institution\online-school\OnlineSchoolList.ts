import OnlineSchoolListFilterModel from '@api/service/training-institution/online-school/models/OnlineSchoolListFilterModel'
import OnlineSchoolItemModule from '@api/service/training-institution/online-school/models/OnlineSchoolItemModule'
import { Page } from '@hbfe/common'
import OnlineSchoolListStatisticsModel from '@api/service/training-institution/online-school/models/OnlineSchoolListStatisticsModel'
import MsBasicdata, {
  OnlineSchoolInfoSortRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'

export default class OnlineSchoolList {
  /**
   * 筛选入参
   */
  filterParam: OnlineSchoolListFilterModel = new OnlineSchoolListFilterModel()

  /**
   * 统计
   */
  statistics: OnlineSchoolListStatisticsModel = new OnlineSchoolListStatisticsModel()

  /**
   * 列表项
   */
  items: Array<OnlineSchoolItemModule> = new Array<OnlineSchoolItemModule>()

  /**
   * 排序
   */
  sort: OnlineSchoolInfoSortRequest = new OnlineSchoolInfoSortRequest()

  /**
   * 查询列表
   */
  async queryList(page: Page) {
    const request = this.filterParam.toQueryRequest()
    request.onlineSchoolInfoSort = this.sort

    const res = await MsBasicdata.pageOnlineSchoolInfoResponseInSubProject({ page, request })
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    const statisticsRes = await MsBasicdata.getOnlineSchoolInfoCount(request)
    this.statistics = new OnlineSchoolListStatisticsModel()
    this.statistics.openSchoolNum = statisticsRes?.data?.openCount
    this.statistics.operateSchoolNum = statisticsRes?.data?.usingCount
    this.statistics.disableSchoolNum = statisticsRes?.data?.unableCount
    this.statistics.overSchoolNum = statisticsRes?.data?.expireCount
    const resList = (res?.data?.currentPageData?.length && res.data.currentPageData) || []

    // 地区处理
    const regionCodeList = new Array<string>()
    resList.map(item => {
      item?.basicInfo?.serviceAreas?.length &&
        item.basicInfo.serviceAreas.map(region => {
          const findRegion = regionCodeList.find(it => it === region)
          if (!findRegion) {
            regionCodeList.push(region)
          }
        })
    })

    const regionMap = await QueryBusinessRegion.queryRegionsNameByIds(regionCodeList)

    this.items = resList.map(item => OnlineSchoolItemModule.from(item, regionMap))

    return this.items
  }
}
