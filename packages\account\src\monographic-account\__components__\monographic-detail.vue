<template>
  <div>
    <!-- 查看详情抽屉 -->
    <el-drawer :title="typeTitle" :visible.sync="isShowDrawer"   size="1200px" custom-class="m-drawer"  :wrapper-closable="false"
               :close-on-press-escape="false">
      <div class="drawer-bd">
        <div  v-if="typeDrawer === 'detail'">
        <div class="m-tit"><span class="tit-txt">基本信息</span></div>
        <el-form ref="form" :model="userDetail" label-width="110px" class="m-text-form f-mt10">
          <el-form-item label="帐号：">{{ userDetail.account }}</el-form-item>
          <el-form-item label="姓名：">{{ userDetail.name }}</el-form-item>
          <el-form-item label="手机号：">{{ userDetail.phone || '-' }}</el-form-item>
        </el-form>
        </div>
        <div class="m-tit f-mt20"><span class="tit-txt" v-if="typeDrawer === 'detail'">管理的专题</span></div>
        <el-table
          :data="userDetail.thematicList"
          max-height="500px"
          class="m-table"
          v-if="userDetail.thematicList.length"
        >
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="专题名称" min-width="230">
            <template v-slot="{ row }">{{ row.basicInfo.subjectName }}</template>
          </el-table-column>
          <el-table-column label="专题类型" min-width="230">
            <template v-slot="{ row }">
              <el-tooltip placement="top" effect="light" v-if="row.basicInfo.suiteIndustry">
                <div slot="content">
                  <i class="f-c4">
                    <el-tag type="warning" size="mini" class="f-mr5">行业</el-tag>{{ row.basicInfo.suiteIndustry }}</i
                  >
                </div>
                <el-button type="text" class="f-to-three">
                  <i class="f-c4">
                    <el-tag type="warning" size="mini" class="f-mt5"> 行业 </el-tag>
                    {{ row.basicInfo.suiteIndustry }}</i
                  >
                </el-button>
              </el-tooltip>
              <el-tooltip placement="top" effect="light" v-if="row.basicInfo.suiteArea">
                <div slot="content">
                  <i class="f-c4"
                    ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag>{{ row.basicInfo.suiteArea }}</i
                  >
                </div>
                <el-button type="text" class="f-to-three"
                  ><i class="f-c4"
                    ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag>{{ row.basicInfo.suiteArea }}</i
                  ></el-button
                >
              </el-tooltip>
              <el-tooltip placement="top" effect="light" v-if="row.basicInfo.unitName">
                <div slot="content">
                  <i class="f-c4">
                    <el-tag type="warning" size="mini" class="f-mr5">单位</el-tag>{{ row.basicInfo.unitName }}</i
                  >
                </div>
                <el-button type="text" class="f-to-three">
                  <i class="f-c4">
                    <el-tag type="warning" size="mini" class="f-mt5"> 单位 </el-tag>
                    {{ row.basicInfo.unitName }}</i
                  >
                </el-button>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="是否显示在网校" min-width="230">
            <template v-slot="{ row }">{{ row.basicInfo.displayInSchool ? '显示' : '不显示' }}</template>
          </el-table-column>
          <el-table-column label="状态" min-width="250">
            <template v-slot="{ row }">
              <div v-if="row.enable">
                <el-badge is-dot type="success" class="badge-status">启用</el-badge>
              </div>
              <div v-else>
                <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-empty
          slot="empty"
          :image-size="40"
          description="暂无数据，请添加专题~"
          v-if="!userDetail.thematicList.length"
        />
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
      </div>
    </el-drawer>

  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import PageAdminInfoResponse from '@api/service/management/user/query/manager/vo/PageAdminInfoResponse'
  import RoleInfoResponseVo from '@api/service/management/authority/role/query/vo/RoleInfoResponseVo'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import { UiPage } from '@hbfe/common'
  import { QueryRoleList } from '@api/service/management/authority/role/query/QueryRoleList'
  import ThematicAdministratorItem from '@api/service/management/user/thematic-administrator/ThematicAdministratorItem'
  @Component
  export default class extends Vue {
    getQueryRoleList: QueryRoleList = AuthorityModule.roleFactory.getQueryRoleList()
    page: UiPage
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    // 请求列表
    async doQueryPage() {
      try {
         await this.userDetail.pageCurrentThematicList(this.page)
      } catch (e) {
        console.log(e)
        this.$message.error('请求失败')
      }
    }

    drawerForm = {
      name: 'sdfs'
    }
    /**
     * 是否展示
     */
    @Prop({
      required: true,
      type: Boolean,
      default: false
    })
    value: boolean
    @Prop({
      required: true,
      type: ThematicAdministratorItem
    })
    userDetail: ThematicAdministratorItem
    @Prop({
      required: true,
      type: String
    })
    typeDrawer: string
    @Watch('value', {
      immediate: true,
      deep: true
    })
    valueChange(val: boolean) {
      this.isShowDrawer = cloneDeep(val)
    }
    @Emit('input')
    @Watch('isShowDrawer', {
      immediate: true,
      deep: true
    })
    showRoleDialogChange(val: number) {
      return val
    }
    isShowDrawer = false

    get typeTitle() {
      let title = '管理员详情'
      if (this.typeDrawer === 'thematicCount') {
        title = '当前账号管理的专题'
      }
      return title
    }
    async created() {
      await this.doQueryPage()
    }
  }
</script>

<style scoped></style>
