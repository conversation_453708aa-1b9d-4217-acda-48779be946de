import { Appraise, EveryStartHit } from '@api/service/common/models/statistic/course-appraise-statistic/Appraise'
import { SchemeCourseAppraiseStatisticDTO } from '@api/gateway/PlatformStatisticReportQuery'

/**
 * 方案维度课程评价统计分页数据
 * <AUTHOR>
 * @Date 2021/5/7/0007 10:24
 */
export class SchemeCourseAppraiseStatistic {
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方案名称
   */
  schemeName: string
  /**
   * 综合评价
   */
  averageComprehensive: Appraise
}

/**
 * 分页
 * <AUTHOR>
 * @Date 2021/5/7/0007 10:16
 */
export class SchemeCourseAppraiseStatisticPage {
  list: Array<SchemeCourseAppraiseStatistic>

  /**
   * 转换远程对象
   * @param remotes
   */
  static fromRemote(remotes: Array<SchemeCourseAppraiseStatisticDTO>) {
    const list = new Array<SchemeCourseAppraiseStatistic>()
    remotes.forEach(remote => {
      const item = new SchemeCourseAppraiseStatistic()
      item.schemeId = remote.schemeId
      item.schemeName = remote.schemeName
      item.averageComprehensive = new Appraise()
      item.averageComprehensive.average = remote.average
      item.averageComprehensive.everyStartHit = new EveryStartHit()
      item.averageComprehensive.everyStartHit.one = remote.one
      item.averageComprehensive.everyStartHit.two = remote.two
      item.averageComprehensive.everyStartHit.three = remote.three
      item.averageComprehensive.everyStartHit.four = remote.four
      item.averageComprehensive.everyStartHit.five = remote.five
      list.push(item)
    })
    return list
  }
}
