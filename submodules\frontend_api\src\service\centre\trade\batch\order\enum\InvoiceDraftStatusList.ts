import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 开票状态枚举
 */
export enum InvoiceDraftStatusEnum {
  // 1：未开票
  Wait_For_Draft = 1,
  // 2：开票中
  Drafting,
  // 3：开票成功
  Complete,
  // 4：冻结中
  Frozen,
  // 5：已作废
  Invalid
}

/**
 * @description 开票状态
 */
class InvoiceDraftStatusList extends AbstractEnum<InvoiceDraftStatusEnum> {
  static enum = InvoiceDraftStatusEnum
  constructor(status?: InvoiceDraftStatusEnum) {
    super()
    this.current = status
    this.map.set(InvoiceDraftStatusEnum.Wait_For_Draft, '未开票')
    this.map.set(InvoiceDraftStatusEnum.Drafting, '开票中')
    this.map.set(InvoiceDraftStatusEnum.Complete, '开票成功')
    this.map.set(InvoiceDraftStatusEnum.Frozen, '冻结中')
    this.map.set(InvoiceDraftStatusEnum.Invalid, '已作废')
  }
}

export default new InvoiceDraftStatusList()
