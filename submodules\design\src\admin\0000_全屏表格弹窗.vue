<template>
  <el-container>
    <el-main>
      <div class="f-p15">
        <el-card shadow="never" class="m-card f-mb15">
          <!--全屏表格-->
          <el-button type="primary" @click="dialog9 = true" class="f-mr20">全屏表格</el-button>
          <el-dialog
            :visible.sync="dialog9"
            :show-close="false"
            :fullscreen="true"
            :append-to-body="true"
            class="m-dialog is-full"
          >
            <div slot="title" class="header-cont">
              <span class="txt">查看表格</span>
              <el-button type="text" class="close" icon="el-icon-error"></el-button>
            </div>
            <!--显示5s后 添加 hide 进行隐藏-->
            <div class="close-tips">按<i class="key">F11</i>即可退出全屏模式</div>
            <div class="m-table-auto">
              <!--条件查询-->
              <el-row :gutter="16" class="m-query">
                <el-form :inline="true" label-width="auto">
                  <el-col :sm="12" :md="8" :lg="6">
                    <el-form-item label="分销商品">
                      <el-input v-model="input" clearable placeholder="请输入分销商品" />
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item label="商品状态">
                      <el-select v-model="select" clearable placeholder="请选择商品状态">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item label="分销商层级">
                      <el-select v-model="select" clearable placeholder="请选择分销商层级">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                    <el-form-item class="f-tr">
                      <el-button type="primary">查询</el-button>
                      <el-button>重置</el-button>
                      <el-button>导出列表</el-button>
                      <el-button type="text">展开<i class="el-icon-arrow-down el-icon--right"></i></el-button>
                      <!--<el-button type="text">收起<i class="el-icon-arrow-up el-icon&#45;&#45;right"></i></el-button>-->
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <!--表格-->
              <el-table stripe :data="tableData" class="m-table">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="培训方案名称" min-width="300" fixed="left">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <p><el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag>培训方案名称</p>
                      <el-tooltip class="item" effect="dark" placement="bottom" popper-class="m-tooltip">
                        <el-tag type="warning">处理中<i class="el-icon-info icon"></i></el-tag>
                        <div slot="content">系统正在处理上一次修改方案的重算任务，暂时不支持再次修改</div>
                      </el-tooltip>
                    </div>
                    <div v-else-if="scope.$index === 1">
                      <p>
                        <el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag
                        >培训方案名称培训方案名称
                      </p>
                      <el-tooltip class="item" effect="dark" placement="bottom" popper-class="m-tooltip">
                        <el-tag type="danger">异常<i class="el-icon-info icon"></i></el-tag>
                        <div slot="content">异常原因：显示异常原因</div>
                      </el-tooltip>
                    </div>
                    <div v-else>
                      <p>
                        <el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag
                        >培训方案名称培训方案名称培训方案名称培训方案名称
                      </p>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="报名学时" min-width="100" align="center">
                  <template>50</template>
                </el-table-column>
                <el-table-column label="价格" min-width="100" align="right">
                  <template>50.00</template>
                </el-table-column>
                <el-table-column label="培训属性" min-width="240">
                  <template>
                    <p>行业：行业行业</p>
                    <p>地区：为空，不展示</p>
                    <p>科目类型：科目类型</p>
                    <p>培训类别：培训类别</p>
                    <p>培训专业：培训专业培训专业</p>
                    <p>培训年度：2019</p>
                  </template>
                </el-table-column>
                <el-table-column label="报名状态" min-width="140">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-badge is-dot type="info" class="badge-status">报名关闭</el-badge>
                    </div>
                    <div v-else>
                      <el-badge is-dot type="success" class="badge-status">报名开启</el-badge>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="学习起止时间" min-width="220">
                  <template>
                    <p>起始：2021-10-15 00:21:21</p>
                    <p>结束：2021-10-15 00:21:21</p>
                  </template>
                </el-table-column>
                <el-table-column label="报名起止时间" min-width="220">
                  <template>
                    <p>起始：2021-10-15 00:21:21</p>
                    <p>结束：2021-10-15 00:21:21</p>
                  </template>
                </el-table-column>
                <el-table-column label="最新修改时间" sortable min-width="180">
                  <template>2021-10-15 00:21:21</template>
                </el-table-column>
                <el-table-column label="操作" width="200" align="center" fixed="right">
                  <template>
                    <el-button type="text" size="mini">修改</el-button>
                    <el-button type="text" size="mini">详情</el-button>
                    <el-button type="text" size="mini">复制</el-button>
                    <el-button type="text" size="mini">修改日志</el-button>
                    <el-button type="text" size="mini">报名关闭</el-button>
                    <el-button type="text" size="mini">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!--分页-->
              <el-pagination
                background
                class="f-mt15 f-tr"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
          </el-dialog>
        </el-card>
      </div>
    </el-main>
  </el-container>
</template>
<script>
  export default {
    data() {
      return {
        dialog: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: true,
        activeName: 'fourth',
        activeName1: 'first',
        activeName2: 'first',
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
