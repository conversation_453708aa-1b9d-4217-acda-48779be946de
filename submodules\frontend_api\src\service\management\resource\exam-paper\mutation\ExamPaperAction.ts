import { ResponseStatus } from '@hbfe/common'
import ExamPaperGateway from '@api/ms-gateway/ms-examextraction-v1'
class ExamPaperAction {
  // 试卷id
  private examPaperId = ''

  constructor(id: string) {
    this.examPaperId = id
  }

  // 预览
  async preview(): Promise<ResponseStatus> {
    // todo 暂放ui做 btpx Utils/index
    return new ResponseStatus(200, '请求成功')
  }

  /**
   * @description: 禁用试卷
   * @param {*}
   * @return {*}
   */
  async doDisabled(): Promise<ResponseStatus> {
    const { status } = await ExamPaperGateway.disablePaperPublishConfigure(this.examPaperId)
    return status
  }

  /**
   * @description: 启用试卷
   */
  async doEnable(): Promise<ResponseStatus> {
    const { status } = await ExamPaperGateway.enablePaperPublishConfigure(this.examPaperId)
    return status
  }
}
export default ExamPaperAction
