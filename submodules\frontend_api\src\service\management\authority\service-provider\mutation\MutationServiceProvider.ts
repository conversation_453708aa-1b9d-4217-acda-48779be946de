/**
 * 查询服务商
 */
import BasicdataDomain, {
  ImmediateResetPasswordRequest,
  UnbindPhoneRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'

class MutationServiceProvider {
  // /**
  //  * 新增课件供应商
  //  * @param params
  //  */
  // async addProvider(params: CreateCoursewareSupplierRequest) {
  //   const response = await CoursewareSupplier.createCoursewareSupplier(params)
  //   if (response.status.code !== 200 && !response.status.isSuccess()) {
  //     console.error('创建课件供应商失败', response)
  //     return Promise.reject(response)
  //   }
  //   return response
  // }
  //
  // /**
  //  * 修改课件供应商
  //  * @param params
  //  */
  // async modifyProvider(params: UpdateCoursewareSupplierRequest) {
  //   const response = await CoursewareSupplier.updateCoursewareSupplier(params)
  //   if (response.status.code !== 200 && !response.status.isSuccess()) {
  //     console.error('修改课件供应商失败', response)
  //     return Promise.reject(response)
  //   }
  //   return response
  // }
  //
  // /**
  //  * 禁、启用课件供应商
  //  * @param disable true 停用 false 启用
  //  * @param id
  //  */
  // async disableEnableProvider(disable: boolean, id: string) {
  //   let response
  //   if (disable) {
  //     response = await BasicdataDomain.freezeAccount(id)
  //     if (response.status.code !== 200 && !response.status.isSuccess()) {
  //       console.error('停用失败', response)
  //       return Promise.reject(response)
  //     }
  //   } else {
  //     response = await BasicdataDomain.resumeAccount(id)
  //     if (response.status.code !== 200 && !response.status.isSuccess()) {
  //       console.error('启用失败', response)
  //       return Promise.reject(response)
  //     }
  //   }
  //   return response
  // }
  //
  // /**
  //  * 重置密码课件供应商
  //  * @param accountId
  //  */
  // async resetPasswordProvider(accountId: string) {
  //   const request = new ImmediateResetPasswordRequest()
  //   request.businessType = 'COURSEWARE_SUPPLIER'
  //   request.accountId = accountId
  //   const response = await BasicdataDomain.immediateResetPassword(request)
  //   if (response.status.code !== 200 && !response.status.isSuccess()) {
  //     console.error('重置密码失败', response)
  //     return Promise.reject(response)
  //   }
  //   return response
  // }

  /**
   * * 解绑手机
   * @param request
   */
  async unbindPhone(request: UnbindPhoneRequest) {
    const response = await BasicdataDomain.unbindPhone(request)
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('解绑手机失败', response)
      return Promise.reject(response)
    }
    return response
  }
}

export default MutationServiceProvider
