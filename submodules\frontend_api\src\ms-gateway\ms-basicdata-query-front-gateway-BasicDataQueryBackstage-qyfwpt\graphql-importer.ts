import getEnterpriseUnitAdminInfoInMyself from './queries/getEnterpriseUnitAdminInfoInMyself.graphql'
import getEnterpriseUnitAdminInfoInSubProject from './queries/getEnterpriseUnitAdminInfoInSubProject.graphql'
import getEnterpriseUnitAdminUserInfoInSubProject from './queries/getEnterpriseUnitAdminUserInfoInSubProject.graphql'
import pageEnterpriseUnitAdminInfoInGovernmentUnit from './queries/pageEnterpriseUnitAdminInfoInGovernmentUnit.graphql'
import pageEnterpriseUnitAdminInfoInMyself from './queries/pageEnterpriseUnitAdminInfoInMyself.graphql'
import pageEnterpriseUnitAdminInfoInSubProject from './queries/pageEnterpriseUnitAdminInfoInSubProject.graphql'

export {
  getEnterpriseUnitAdminInfoInMyself,
  getEnterpriseUnitAdminInfoInSubProject,
  getEnterpriseUnitAdminUserInfoInSubProject,
  pageEnterpriseUnitAdminInfoInGovernmentUnit,
  pageEnterpriseUnitAdminInfoInMyself,
  pageEnterpriseUnitAdminInfoInSubProject
}
