import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformCourse'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformCourse'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举

// 类

/**
 * 课程学习统计参数
@author: eleven
@date: 2020/4/13
 */
export class CourseLearningStatisticParam {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 学习方式id
   */
  learningId?: string
  /**
   * 课程id集合
   */
  courseIdList?: Array<string>
}

/**
 * 课程分页查询参数对象
 */
export class CoursePageParamDTO {
  /**
   * 课程名称
   */
  name?: string
  /**
   * 课程考纲
   */
  tagIds?: Array<string>
  /**
   * 课程状态
-1不查
0表示解析中，1表示解析成功，2表示解析失败
   */
  status: number
  /**
   * 创建时间查询的起始时间
   */
  startCreateTime?: string
  /**
   * 创建时间查询的截止时间
   */
  endCreateTime?: string
  /**
   * 是否启用， -1表示不查询，0表示不启用，1表示启用
   */
  isEnabled: number
  /**
   * 分类编号
   */
  categoryId?: string
  /**
   * 分类编号集合(当categoryId有值时，categoryIdList设置无效)
   */
  categoryIdList?: Array<string>
}

export class CoursePoolPageParamDTO {
  /**
   * 课程池名称
   */
  poolName?: string
  /**
   * 筛选课程池编号列表
   */
  poolIdList?: Array<string>
}

/**
 * 用户课程学习进度查询
@author: eleven
@date: 2020/3/5
 */
export class UserCourseLearningScheduleParamDTO {
  /**
   * 课程id集合
   */
  courseIdList?: Array<string>
  /**
   * 用户id -运营域参数，学员端忽略该参数
   */
  userId?: string
  /**
   * 选课类型
@see com.fjhb.platform.core.courselearning.v1.api.constants.UserCourseSource
   */
  source?: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * 用户已选课程查询参数
@author: eleven
@date: 2020/3/5
 */
export class UserCourseParamDTO {
  /**
   * 所属课程包id
   */
  coursePoolId?: string
  /**
   * 选课类型
@see com.fjhb.platform.core.courselearning.v1.api.constants.UserCourseSource
   */
  source?: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * 用户最后学习所在的课程查询条件
@author: eleven
@date: 2020/3/5
 */
export class UserLastLearningCourseParamDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * 用户课程
@author: eleven
@date: 2020/3/5
 */
export class UserSchemeCourseLearningStatisticParamDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * 用户课程学习记录查询参数
@author: eleven
@date: 2020/5/18
 */
export class UserCourseRecordParam {
  userId?: string
  schemeId?: string
  issueId?: string
  learningId?: string
}

export class Page {
  pageNo?: number
  pageSize?: number
}

export class Sort {
  field?: string
  dir?: string
}

export class CourseWareQueryDTO {
  name?: string
  type: number
  categoryId?: string
  supplierId?: string
  needHasQuestion: number
  isUsable: number
  status: number
  startCreateTime?: string
  endCreateTime?: string
  statusList?: Array<number>
  sort?: Array<Sort>
  unitId?: string
  courseId?: string
}

export class TeacherQueryDTO {
  name?: string
  createUserId?: string
}

export class ResAuthorizedQuery {
  authorizedState: number
  hasAuthorize?: boolean
  forbidAuthorize: boolean
  rangeType?: string
  belongsType?: string
  authorizeToUnitId?: string
  authorizedFromUnitId?: string
  objectId?: string
  useType?: string
  targetUnitId?: string
}

/**
 * 精品课程
<AUTHOR> create 2020/3/5 9:48
 */
export class ExcellentCourseListDTO {
  /**
   * 课程ID
   */
  id: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 权重,表示学时,学分等
   */
  period: number
  /**
   * 课程简介
   */
  abouts: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 是否被逻辑删除
   */
  delete: boolean
  /**
   * 课程的课件状态，0表示解析中，1表示解析成功，2表示解析失败
   */
  status: number
  /**
   * 完成时间
   */
  completedTime: string
  /**
   * 课程详情
   */
  contents: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建者编号
   */
  createUsrId: string
  /**
   * 计划授课讲数
   */
  plannedLecturesNum: number
  /**
   * 多少人学过
   */
  studyCount: number
  /**
   * 教师集合
   */
  teachers: Array<TeacherInfoDTO>
}

/**
 * <AUTHOR>
@date 2020/8/18
@description
 */
export class CourseCategoryDTO {
  /**
   * 分类id
   */
  id: string
  /**
   * 分类名称
   */
  name: string
}

/**
 * <AUTHOR>
@date 2020/8/18
@description
 */
export class CourseCategoryList {
  /**
   * 课程id
   */
  courseId: string
  /**
   * 所属分类
   */
  categoryList: Array<CourseCategoryDTO>
}

/**
 * 课程信息 -不包括课程目录信息
@author: eleven
@date: 2020/3/4
 */
export class CourseDTO {
  /**
   * 课程ID
   */
  id: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 权重,表示学时,学分等
当查询未选课列表时，为课程在课程包的学时
当查询已选课时，用户选课时，课程包配置的学时
   */
  period: number
  /**
   * 课程简介
   */
  abouts: string
  /**
   * 课程的课件状态，0表示解析中，1表示解析成功，2表示解析失败
   */
  status: number
  /**
   * 课程时长
   */
  timeLength: number
  /**
   * 是否支持试听
   */
  supportAudition: boolean
  /**
   * 计划课件数量
   */
  courseWareCount: number
  /**
   * 已更新的课件数量
   */
  courseWareUpdateCount: number
  /**
   * 课件教师id集合
   */
  teacherIdList: Array<string>
  /**
   * 所属课程分类集合（正常只有一个）
   */
  courseCategoryDtoList: Array<CourseCategoryDto>
  /**
   * 课程所属考纲id集合
   */
  tagIdList: Array<string>
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 是否启用
   */
  enabled: boolean
}

/**
 * 课程包被方案引用的对象
<AUTHOR> create 2020/3/31 16:59
 */
export class CoursePoolReferenceDTO {
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方案名称
   */
  schemeName: string
}

export class CoursewareCategoryInfoDTO {
  /**
   * 分类编号
   */
  id: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 备注
   */
  remarks: string
  /**
   * 父分类id
   */
  parentId: string
  /**
   * 父分类
   */
  parent: CoursewareCategoryInfoDTO
  /**
   * 创建者ID
   */
  createUsrId: string
  /**
   * 创建人
   */
  creator: Operator
  /**
   * 创建时间
   */
  createTime: string
}

export class CoursewareDTO {
  /**
   * 课件ID
   */
  id: string
  /**
   * 课件名称
   */
  name: string
  /**
   * 课件类型，1表示文档，2表示视频，3表示多媒体
   */
  type: number
  /**
   * 课件原始类型, 1表示文档，2表示单视频，3表示串流大师，4表示汉博尔，5表示会计靠前，6表示Power+，7表示网视宝，8表示新华网，9表示地税网络学院，10表示中经网
   */
  originalType: number
  /**
   * 媒体时长，单位秒
   */
  timeLength: number
  /**
   * 教师名称
   */
  teacherName: Array<string>
  /**
   * 教师简介
   */
  teacherAbouts: string
  /**
   * 课件分类ID
   */
  cwyId: string
  /**
   * 课件分类
   */
  category: CoursewareCategoryInfoDTO
  /**
   * 供应商ID
   */
  supplierId: string
  /**
   * 供应商
   */
  supplier: SupplierInfoDTO
  /**
   * 课件状态 0解析中，1解析成功，2解析失败
   */
  status: number
  /**
   * 是否可用
   */
  usable: boolean
  /**
   * 创建者ID
   */
  createUsrId: string
  /**
   * 创建人
   */
  creator: Operator
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 自定义拓展信息
   */
  expandData: string
  /**
   * 创建类型，0表示自建,1表示内置,2表示共享，3表示迁移，4表示购买
   */
  createType: number
  /**
   * 创建类型相应时间
   */
  createTypeTime: string
}

export class SupplierInfoDTO {
  /**
   * 主键 课件提供商ID
   */
  id: string
  /**
   * 提供商名称
   */
  name: string
  /**
   * 创建人id
   */
  creator: string
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 考纲对应的课程数量
<AUTHOR>
@date 2020/4/1
@since 1.0.0
 */
export class SyllabusCourseCountDTO {
  /**
   * 考纲中的章节编号
   */
  chapterId: string
  /**
   * 章节下课程数量
   */
  courseCount: number
}

/**
 * 教师信息对象
 */
export class TeacherInfoDTO {
  id: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  organizationId: string
  name: string
  photo: string
  abouts: string
  contents: string
  gender: number
  professionalTitle: string
  createUsrId: string
  creator: OperatorDTO
  createTime: string
}

/**
 * 课程池内课程信息对象
 */
export class CourseInPoolDetailDTO {
  /**
   * 课程编号
   */
  courseId: string
  /**
   * 课程名称
   */
  courseName: string
  /**
   * 课程序号
   */
  sequence: number
  /**
   * 课程标量值|在课程池规则中，课程在课程池中权重值
   */
  quantitative: number
  /**
   * 课程学时|课程在课程池中的学时
   */
  period: number
  /**
   * 课程过期时间|课程在课程池中到期时间，null表示该课程在课程池中无期限限制
   */
  courseExpireTime: string
  /**
   * 创建人编号
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 课程学时|课程中的学时
   */
  coursePeriod: number
  /**
   * 所属课程池编号
   */
  ccpId: string
  /**
   * 计划课件数量
   */
  courseWareCount: number
  /**
   * 已更新的课件数量
   */
  courseWareUpdateCount: number
  /**
   * 标签id集合
   */
  tagIdList: Array<string>
}

/**
 * 课程被学习次数统计
@author: eleven
@date: 2020/4/13
 */
export class CourseLearningStatisticDTO {
  /**
   * 课程id
   */
  courseId: string
  /**
   * 被选课次数
   */
  selectedCount: number
  /**
   * 待学习课程数
   */
  waitStudyCount: number
  /**
   * 学习中课程数
   */
  studyCount: number
  /**
   * 学习完成课程数
   */
  studyFinishCount: number
}

/**
 * 课程目录信息
@author: eleven
@date: 2020/3/6
 */
export class CourseOutlineDTO {
  /**
   * 所属课程编号
   */
  courseId: string
  /**
   * 目录信息
   */
  outlineDTOList: Array<OutlineDTO>
  /**
   * 课件目录信息
   */
  courseWareOutlineDTOList: Array<CourseWareOutlineDTO>
}

/**
 * 课程包基础信息
Author:FangKunSen
Time:2020-06-06,11:17
 */
export class CoursePoolBaseInfoDTO {
  /**
   * 课程包id
   */
  coursePoolId: string
  /**
   * 课程包名
   */
  showName: string
}

export class CoursePoolInfoDTO {
  /**
   * 课程池编号
   */
  id: string
  /**
   * 课程池名称
   */
  poolName: string
  /**
   * 课程展示名称
   */
  showName: string
  /**
   * 课程池状态|0/1/2，正常/无效/过期
   */
  poolState: number
  /**
   * 排序序号
   */
  sequence: number
  /**
   * 创建人编号
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime: string
  /**
   * 过期时间,null表示不设置过期
   */
  expireTime: string
  /**
   * 课程池描述
   */
  poolDescription: string
  /**
   * 课程池内课程数量
   */
  courseCount: number
  /**
   * 课程池内课程权重值总和
   */
  totalQuantitative: number
  /**
   * 课程池内课程学时或学分总和
   */
  totalPeriod: number
}

/**
 * 课件目录 - 从课程目录信息取数，绑定父节点，前端自拼凑成树的结构
@author: eleven
@date: 2020/3/6
 */
export class CourseWareOutlineDTO {
  /**
   * 课件所在的目录id
   */
  outlineId: string
  /**
   * 课件名称
   */
  name: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 课件id
   */
  courseWareId: string
  /**
   * 课件类型，1表示文档，2表示视频，3表示多媒体
   */
  type: number
  /**
   * 媒体时长，单位秒
   */
  timeLength: number
}

/**
 * 课程目录-不含课件信息
@author: eleven
@date: 2020/3/5
 */
export class OutlineDTO {
  /**
   * 课程目录ID
   */
  id: string
  /**
   * 目录名称
   */
  name: string
  /**
   * 父类ID
   */
  parentId: string
  /**
   * 课程id
   */
  courseId: string
}

/**
 * 用户已选课程
@author: eleven
@date: 2020/3/5
 */
export class UserCourseDTO {
  /**
   * 所属课程包编号
   */
  poolId: string
  /**
   * 所属课程类型
1:必修课；2：选修；
   */
  courseType: number
  /**
   * 课程ID
   */
  id: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 权重,表示学时,学分等
当查询未选课列表时，为课程在课程包的学时
当查询已选课时，用户选课时，课程包配置的学时
   */
  period: number
  /**
   * 课程简介
   */
  abouts: string
  /**
   * 课程的课件状态，0表示解析中，1表示解析成功，2表示解析失败
   */
  status: number
  /**
   * 课程时长
   */
  timeLength: number
  /**
   * 是否支持试听
   */
  supportAudition: boolean
  /**
   * 计划课件数量
   */
  courseWareCount: number
  /**
   * 已更新的课件数量
   */
  courseWareUpdateCount: number
  /**
   * 课件教师id集合
   */
  teacherIdList: Array<string>
  /**
   * 所属课程分类集合（正常只有一个）
   */
  courseCategoryDtoList: Array<CourseCategoryDto>
  /**
   * 课程所属考纲id集合
   */
  tagIdList: Array<string>
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 是否启用
   */
  enabled: boolean
}

/**
 * 课程学习记录，来自于清洗中间表
<AUTHOR>
@date 2020/8/25
@description
 */
export class UserCourseLearningRecordResponse {
  /**
   * 用户
   */
  userId: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方式id
   */
  learningId: string
  /**
   * 期别编号
   */
  stageId: string
  /**
   * 期数编号
   */
  issueId: string
  /**
   * 年度
   */
  year: number
  /**
   * 选课规则id
   */
  ruleId: string
  /**
   * 包id
   */
  poolId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 选课类型
@see UserChooseCourseType
   */
  chooseCourseType: number
  /**
   * 课程学时
   */
  period: number
  /**
   * 课程时长
   */
  timeLength: number
  /**
   * 课程学习进度
   */
  schedule: number
  /**
   * 课程学习时长
   */
  learningTimeLength: number
  /**
   * 课程学习状态
@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
   */
  studyState: number
  /**
   * 开始学习时间
   */
  startStudyTime: string
  /**
   * 最后学习时间
   */
  lastStudyTime: string
  /**
   * 学习完成时间
   */
  studyCompleteTime: string
}

/**
 * 用户指定学习方案内指定课程的学习进度
@author: eleven
@date: 2020/3/5
 */
export class UserCourseLearningScheduleDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 学习进度
   */
  schedule: number
  /**
   * 学习状态
0/1/2，未学习/学习中/学习完成
@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
   */
  studyState: number
  /**
   * 最新学习时间
   */
  lastStudyTime: string
}

/**
 * 用户课件学习记录编号
<AUTHOR>
@date 2020/3/14
@since 1.0.0
 */
export class UserCoursewareLearningScheduleDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 课件编号
   */
  courseWareId: string
  /**
   * 课件学习进度
   */
  schedule: number
  /**
   * 课件学习状态
0/1/2，未学习/学习中/学习完成
@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
   */
  studyState: number
  /**
   * 最新学习时间
   */
  lastStudyTime: string
}

/**
 * 用户最后一次学习的课程
@author: eleven
@date: 2020/3/5
 */
export class UserLastLearningCourseDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 包id
   */
  poolId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 课程学习进度
   */
  schedule: number
  /**
   * 最后一次学习时间
   */
  lastStudyTime: string
}

/**
 * 用户方案内正在的学习的课程数统计
@author: eleven
@date: 2020/3/5
 */
export class UserSchemeCourseLearningStatisticDTO {
  /**
   * 待学习课程数
   */
  waitStudyCount: number
  /**
   * 学习中课程数
   */
  studyCount: number
  /**
   * 学习完成课程数
   */
  studyFinishCount: number
}

export class MarkerDTO {
  key: string
  value: string
}

export class OperatorDTO {
  userId: string
  name: string
  uniqueData: string
}

export class SupplierDTO {
  id: string
  name: string
  creator: string
  createTime: string
}

export class UnitDTO {
  id: string
  name: string
}

export class CoursePoolDTO {
  id: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  unit: UnitDTO
  organizationId: string
  poolName: string
  sequence: number
  markers: Array<MarkerDTO>
  createUsrId: string
  expireTime: string
  poolDescription: string
  showName: string
  creator: OperatorDTO
  createTime: string
  courseCount: number
  totalPeriod: number
}

export class CourseWareDTO {
  id: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  organizationId: string
  name: string
  type: number
  originalType: number
  timeLength: number
  teacherId: string
  teacher: TeacherDTO
  abouts: string
  cwyId: string
  category: CourseWareCategoryDTO
  coursewareResourcePath: string
  resourceMD5: string
  supplierId: string
  supplier: SupplierDTO
  usable: boolean
  status: number
  createUsrId: string
  creator: OperatorDTO
  createTime: string
  expandData: string
  createType: number
  createTypeTime: string
}

export class CourseWareCategoryDTO {
  id: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  organizationId: string
  name: string
  sort: number
  enabled: boolean
  remarks: string
  parentId: string
  createUsrId: string
  creator: OperatorDTO
  createTime: string
}

export class TeacherDTO {
  id: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  organizationId: string
  name: string
  photo: string
  abouts: string
  contents: string
  gender: number
  professionalTitle: string
  createUsrId: string
  creator: OperatorDTO
  createTime: string
}

export class Operator {
  userId: string
  name: string
  nickName: string
  uniqueData: string
}

export class CourseCategoryDto {
  id: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  organizationId: string
  name: string
  parentId: string
  sort: number
  remarks: string
  objectId: string
}

export class CoursewareDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursewareDTO>
}

export class UserCourseDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserCourseDTO>
}

export class CourseDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseDTO>
}

export class CoursePoolInfoDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursePoolInfoDTO>
}

export class TeacherInfoDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TeacherInfoDTO>
}

export class UserCourseLearningRecordResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserCourseLearningRecordResponse>
}

export class CourseLearningStatisticDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseLearningStatisticDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 检查课程池被引用的情况
   * @param query 查询 graphql 语法文档
   * @param poolId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async checkCoursePoolReference(
    poolId: string,
    query: DocumentNode = GraphqlImporter.checkCoursePoolReference,
    operation?: string
  ): Promise<Response<Array<CoursePoolReferenceDTO>>> {
    return commonRequestApi<Array<CoursePoolReferenceDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { poolId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 课件名称是否存在
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async courseWareNameExists(
    params: { name?: string; id?: string },
    query: DocumentNode = GraphqlImporter.courseWareNameExists,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课程信息  --课程服务
   * @param courseId 课程编号
   * @return 课程信息
   * @param query 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourse(
    courseId: string,
    query: DocumentNode = GraphqlImporter.getCourse,
    operation?: string
  ): Promise<Response<CourseDTO>> {
    return commonRequestApi<CourseDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { courseId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询指定考纲下课程数量
   * @param chapterIdList 考纲编号列表
   * @return 章节对应的课程数量
   * @param query 查询 graphql 语法文档
   * @param chapterIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseCountBySyllabus(
    chapterIdList: Array<string>,
    query: DocumentNode = GraphqlImporter.getCourseCountBySyllabus,
    operation?: string
  ): Promise<Response<Array<SyllabusCourseCountDTO>>> {
    return commonRequestApi<Array<SyllabusCourseCountDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { chapterIdList },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课程被学习的人次 - 来源中间表(已实现)
   * @param courseId
   * @return
   * @param query 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseLearningCount(
    courseId: string,
    query: DocumentNode = GraphqlImporter.getCourseLearningCount,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { courseId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询课程池dto
   * @param poolId
   * @return
   * @param query 查询 graphql 语法文档
   * @param poolId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCoursePoolDTO(
    poolId: string,
    query: DocumentNode = GraphqlImporter.getCoursePoolDTO,
    operation?: string
  ): Promise<Response<CoursePoolDTO>> {
    return commonRequestApi<CoursePoolDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { poolId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课件
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseWare(
    id: string,
    query: DocumentNode = GraphqlImporter.getCourseWare,
    operation?: string
  ): Promise<Response<CoursewareDTO>> {
    return commonRequestApi<CoursewareDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 课件分页
   * @param page
   * @param query
   * @param authorizedQuery
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseWarePage(
    params: { page?: Page; query?: CourseWareQueryDTO; authorizedQuery?: ResAuthorizedQuery },
    query: DocumentNode = GraphqlImporter.getCourseWarePage,
    operation?: string
  ): Promise<Response<CoursewareDTOPage>> {
    return commonRequestApi<CoursewareDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课件提供商
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseWareSupplier(
    query: DocumentNode = GraphqlImporter.getCourseWareSupplier,
    operation?: string
  ): Promise<Response<Array<SupplierInfoDTO>>> {
    return commonRequestApi<Array<SupplierInfoDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询讲师详情
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTeacherById(
    id: string,
    query: DocumentNode = GraphqlImporter.getTeacherById,
    operation?: string
  ): Promise<Response<TeacherInfoDTO>> {
    return commonRequestApi<TeacherInfoDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取最后一次学习的课程 - 来源中间表(已实现)
   * @param paramDTO
   * @return UserLastLearningCourseDTO || null:未选课的时候
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUserLastCourseLearning(
    paramDTO: UserLastLearningCourseParamDTO,
    query: DocumentNode = GraphqlImporter.getUserLastCourseLearning,
    operation?: string
  ): Promise<Response<UserLastLearningCourseDTO>> {
    return commonRequestApi<UserLastLearningCourseDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量获取指定课程信息  -课程服务
   * @param courseIdList 课程编号列表
   * @return 课程信息列表
   * @param query 查询 graphql 语法文档
   * @param courseIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listCourse(
    courseIdList: Array<string>,
    query: DocumentNode = GraphqlImporter.listCourse,
    operation?: string
  ): Promise<Response<Array<CourseDTO>>> {
    return commonRequestApi<Array<CourseDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { courseIdList },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量获取指定课程的课程分类
   * @param courseIdList
   * @return
   * @param query 查询 graphql 语法文档
   * @param courseIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listCourseCategory(
    courseIdList: Array<string>,
    query: DocumentNode = GraphqlImporter.listCourseCategory,
    operation?: string
  ): Promise<Response<Array<CourseCategoryList>>> {
    return commonRequestApi<Array<CourseCategoryList>>(
      SERVER_URL,
      {
        query: query,
        variables: { courseIdList },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据课程包id查询课程包内课程集合
   * @param query 查询 graphql 语法文档
   * @param poolId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listCourseInPool(
    poolId: string,
    query: DocumentNode = GraphqlImporter.listCourseInPool,
    operation?: string
  ): Promise<Response<Array<CourseInPoolDetailDTO>>> {
    return commonRequestApi<Array<CourseInPoolDetailDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { poolId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课程目录信息  -课程服务
   * @param courseId 课程编号
   * @return 课程目录及包含课件列表
   * @param query 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listCourseOutline(
    courseId: string,
    query: DocumentNode = GraphqlImporter.listCourseOutline,
    operation?: string
  ): Promise<Response<CourseOutlineDTO>> {
    return commonRequestApi<CourseOutlineDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { courseId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量获取课程包
   * @param ids
   * @return
   * @param query 查询 graphql 语法文档
   * @param ids 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listCoursePoolInfoByIds(
    ids: Array<string>,
    query: DocumentNode = GraphqlImporter.listCoursePoolInfoByIds,
    operation?: string
  ): Promise<Response<Array<CoursePoolInfoDTO>>> {
    return commonRequestApi<Array<CoursePoolInfoDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { ids },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据课件id集合批量获取课件信息
   * @param ids
   * @return
   * @param query 查询 graphql 语法文档
   * @param ids 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listCourseWareListByIds(
    ids: Array<string>,
    query: DocumentNode = GraphqlImporter.listCourseWareListByIds,
    operation?: string
  ): Promise<Response<Array<CourseWareDTO>>> {
    return commonRequestApi<Array<CourseWareDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { ids },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询正常状态的（解析成功的，未被删除的）精品课程
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExcellentCourse(
    query: DocumentNode = GraphqlImporter.listExcellentCourse,
    operation?: string
  ): Promise<Response<Array<ExcellentCourseListDTO>>> {
    return commonRequestApi<Array<ExcellentCourseListDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据教师id集合 批量获取讲师信息
   * @param query 查询 graphql 语法文档
   * @param teacherIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listTeachersByIds(
    teacherIds: Array<string>,
    query: DocumentNode = GraphqlImporter.listTeachersByIds,
    operation?: string
  ): Promise<Response<Array<TeacherInfoDTO>>> {
    return commonRequestApi<Array<TeacherInfoDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { teacherIds },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户方案内的已选课程-课程服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserCourse(
    paramDTO: UserCourseParamDTO,
    query: DocumentNode = GraphqlImporter.listUserCourse,
    operation?: string
  ): Promise<Response<Array<UserCourseDTO>>> {
    return commonRequestApi<Array<UserCourseDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户指定课程的学习进度 -课程服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserCourseLearningSchedule(
    paramDTO: UserCourseLearningScheduleParamDTO,
    query: DocumentNode = GraphqlImporter.listUserCourseLearningSchedule,
    operation?: string
  ): Promise<Response<Array<UserCourseLearningScheduleDTO>>> {
    return commonRequestApi<Array<UserCourseLearningScheduleDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据方案id和课程学习方式id获取所有可选课程包
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserCoursePool(
    paramDTO: UserCourseParamDTO,
    query: DocumentNode = GraphqlImporter.listUserCoursePool,
    operation?: string
  ): Promise<Response<Array<CoursePoolBaseInfoDTO>>> {
    return commonRequestApi<Array<CoursePoolBaseInfoDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取指定课程下的所有课件进度
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserCoursewareLearningSchedule(
    paramDTO: UserCourseLearningScheduleParamDTO,
    query: DocumentNode = GraphqlImporter.listUserCoursewareLearningSchedule,
    operation?: string
  ): Promise<Response<Array<UserCoursewareLearningScheduleDTO>>> {
    return commonRequestApi<Array<UserCoursewareLearningScheduleDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户方案内未选择的课程 -课程服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserUnSelectCourse(
    params: { page?: Page; paramDTO?: UserCourseParamDTO },
    query: DocumentNode = GraphqlImporter.listUserUnSelectCourse,
    operation?: string
  ): Promise<Response<UserCourseDTOPage>> {
    return commonRequestApi<UserCourseDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询课程
   * @param page
   * @param queryParam
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCourse(
    params: { page?: Page; queryParam?: CoursePageParamDTO },
    query: DocumentNode = GraphqlImporter.pageCourse,
    operation?: string
  ): Promise<Response<CourseDTOPage>> {
    return commonRequestApi<CourseDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询课程池
   * @param page
   * @param queryPram
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCoursePool(
    params: { page?: Page; queryPram?: CoursePoolPageParamDTO },
    query: DocumentNode = GraphqlImporter.pageCoursePool,
    operation?: string
  ): Promise<Response<CoursePoolInfoDTOPage>> {
    return commonRequestApi<CoursePoolInfoDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询讲师分页
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageTeachers(
    params: { page?: Page; query?: TeacherQueryDTO },
    query: DocumentNode = GraphqlImporter.pageTeachers,
    operation?: string
  ): Promise<Response<TeacherInfoDTOPage>> {
    return commonRequestApi<TeacherInfoDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户课程学习记录
   * @param page
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUserCourseLearning(
    params: { page?: Page; param?: UserCourseRecordParam },
    query: DocumentNode = GraphqlImporter.pageUserCourseLearning,
    operation?: string
  ): Promise<Response<UserCourseLearningRecordResponsePage>> {
    return commonRequestApi<UserCourseLearningRecordResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 课程学习统计
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticCourseLearning(
    params: { page?: Page; param?: CourseLearningStatisticParam },
    query: DocumentNode = GraphqlImporter.statisticCourseLearning,
    operation?: string
  ): Promise<Response<CourseLearningStatisticDTOPage>> {
    return commonRequestApi<CourseLearningStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 统计用户学习方案内的课程学习情况  - 来源中间表(已实现)
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserLearningCourseLearningInfo(
    paramDTO: UserSchemeCourseLearningStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserLearningCourseLearningInfo,
    operation?: string
  ): Promise<Response<UserSchemeCourseLearningStatisticDTO>> {
    return commonRequestApi<UserSchemeCourseLearningStatisticDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
