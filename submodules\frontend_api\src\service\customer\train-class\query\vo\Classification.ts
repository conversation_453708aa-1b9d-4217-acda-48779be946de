/**
 * 分类大纲
 */
import { ClassificationAssess } from '@api/service/customer/train-class/query/vo/ClassificationAssess'

class Classification {
  // region properties
  /**
   * 父节点id
   */
  parentId = ''
  /**
   *id，类型为string
   */
  id = ''
  /**
   *课程学习大纲名称，类型为string
   */
  name = ''
  /**
   *课程包id，类型为string
   */
  coursePackageId = ''
  /**
   *当前课程学习大纲下的必学课程列表，仅当没有childOutlines时生效，类型为string[]
   */
  compulsoryCourseIdList: string[] = []
  /**
   *课程学习大纲序号，同级序号，类型为number
   */
  sort = 0
  /**
   *大纲类型，1-必修，2-选修 仅限选课规则时候选用，类型为number
   */
  category = 0
  /**
   *大纲考核对象
   */
  assessSetting = new ClassificationAssess()
  /**
   *子节点，类型为Classification[]
   */
  childOutlines: Classification[] = []
  // endregion
  // region methods

  // endregion
}
export default Classification
