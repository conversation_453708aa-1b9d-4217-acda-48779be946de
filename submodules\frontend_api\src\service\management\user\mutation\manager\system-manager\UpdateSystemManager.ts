import MsAccountGateway from '@api/ms-gateway/ms-account-v1'
import basicdata from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import UpdateOnlineSchoolSubAdminRequestVo from '@api/service/management/user/mutation/manager/system-manager/vo/UpdateOnlineSchoolSubAdminRequestVo'
import { ResponseStatus } from '@hbfe/common'
import UpdateSubAdminRequestVo from './vo/UpdateSubAdminRequestVo'

import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
import platformJxjyDistributor from '@api/platform-gateway/platform-jxjy-distributor-admin-v1'

/**
 * 修改系统管理员
 */
class UpdateSystemManager {
  updateSubAdminParams = new UpdateSubAdminRequestVo()
  updateOnlineSchoolSubAdminParams = new UpdateOnlineSchoolSubAdminRequestVo()

  async doUpdateSystemManager(): Promise<ResponseStatus> {
    const { status } = await MsAccountGateway.updateAdmin(this.updateSubAdminParams)
    return status
  }

  async doUpdateOnlineSchoolManager() {
    return await basicdata.updateOnlineSchoolSubAdmin(
      UpdateOnlineSchoolSubAdminRequestVo.to(this.updateOnlineSchoolSubAdminParams)
    )
  }
  /**
   *
   * @returns 8.0网校使用 --- 多网校
   */
  async updateOnlineSchoolSubAdminByToken() {
    let token = ''
    if (QueryManagerDetail.hasCategory(CategoryEnums.fxs)) {
      // 分销商创建管理员切换token
      token = CommonConfigCenter.getFrontendApplication(frontendApplication.distributionAdministratorPwdLoginToken)
    } else {
      token = CommonConfigCenter.getFrontendApplication(frontendApplication.superLoginToken)
    }
    const param = UpdateOnlineSchoolSubAdminRequestVo.toV2(this.updateOnlineSchoolSubAdminParams)
    param.token = token
    return await basicdata.updateOnlineSchoolSubAdminByToken(param)
  }

  /**
   * 修改分销商管理员
   */
  async updateDistributorAdmin() {
    const token = CommonConfigCenter.getFrontendApplication(frontendApplication.distributionAdministratorPwdLoginToken)
    const params = UpdateOnlineSchoolSubAdminRequestVo.toUpdateDistributorAdminRequest(
      this.updateOnlineSchoolSubAdminParams
    )
    params.token = token
    return platformJxjyDistributor.updateDistributorAdmin(params)
  }
}

export default UpdateSystemManager
