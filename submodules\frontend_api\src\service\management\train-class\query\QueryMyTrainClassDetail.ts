import { ResponseStatus } from '@hbfe/common'
import MyTrainClassDetailClassVo from '@api/service/management/train-class/query/vo/MyTrainClassDetailClassVo'
import TrainClassConfigJsonManager from '@api/service/management/train-class/Utils/TrainClassConfigJsonManager'
import {
  ComplexSkuPropertyResponse,
  SkuPropertyConvertUtils
} from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import MsMySchemeQueryFrontGatewayCourseLearningForeStage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import MsSchemeQueryFrontGatewayCourseLearningForestage, {
  CertificateLearningConfigResultResponse,
  GradeLearningConfigResultResponse,
  LearningResultResponse,
  SchemeConfigResponse,
  StudentSchemeLearningResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import CalculatorObj from '@api/service/management/train-class/Utils/CalculatorObj'
import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
import { ClassFicationEnum } from '@api/service/management/train-class/query/enum/ClassFicationEnum'
import Classification from '@api/service/management/train-class/mutation/vo/Classification'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import QueryTrainClass from '@api/service/management/train-class/offlinePart/QueryTrainClass'

/**
 * 用户域获取我的培训班详情
 */
class QueryMyTrainClassDetail {
  // region properties
  jsonString = ''
  /**
   *培训班详情，类型为MyTrainClassDetailClassVo
   */
  trainClassDetail = new MyTrainClassDetailClassVo()
  /**
   *参训资格id，类型为string
   */
  qualificationId = ''

  studentSchemeLearningResponse = new StudentSchemeLearningResponse()
  // endregion
  // region methods

  /**
   * 培训方案id
   */
  schemeId = ''

  /**
   * 获取培训班详情 由详情和配置信息组合而成
   */
  async queryTrainClassDetail(): Promise<ResponseStatus> {
    //获取班级详情
    let status = await this.requestClassDetail()
    if (status.isSuccess()) {
      //获取班级模板配置信息
      status = await this.requestClassConfig()
    }
    return status
  }
  getInfoFromJSON(commodityDetail: SchemeConfigResponse) {
    const trainClassDetail = this.trainClassDetail
    this.jsonString = commodityDetail.schemeConfig
    const classConfigJson: any = TrainClassConfigJsonManager.calculatorSchemeConfigJson(commodityDetail.schemeConfig)
    if (classConfigJson) {
      const commodityJson = classConfigJson.commoditySale
      this.trainClassDetail.trainClassBaseInfo.picture = classConfigJson.picture
      this.trainClassDetail.price = commodityJson.price
      this.trainClassDetail.trainClassBaseInfo.name = commodityJson.saleTitle
      this.trainClassDetail.commoditySkuId = commodityJson.id
      trainClassDetail.trainClassBaseInfo.provideRelearn = classConfigJson.provideRelearn
      trainClassDetail.onShelvesPlanTime = classConfigJson.commoditySale.onOrOffShelvesPlan.onShelvePlanTime
      trainClassDetail.trainClassBaseInfo.registerBeginDate = classConfigJson.registerBeginDate
      trainClassDetail.trainClassBaseInfo.registerEndDate = classConfigJson.registerEndDate
      trainClassDetail.trainClassBaseInfo.trainingBeginDate = classConfigJson.trainingBeginDate
      trainClassDetail.trainClassBaseInfo.trainingEndDate = classConfigJson.trainingEndDate

      // 解析方案json配置
      QueryTrainClass.parseMyTrainClassDetailJsonConfig(this.jsonString, this.trainClassDetail)

      this.trainClassDetail.trainClassBaseInfo.schemeType = SchemeTypeEnum[classConfigJson.type as string]
      this.trainClassDetail.learningTypeModel = TrainClassConfigJsonManager.jsonConfigConvertToLearningType(
        this.jsonString,
        this.trainClassDetail.trainClassBaseInfo.schemeType
      )
    }

    //设置学员当前获取的学习数据
    let totalRequirePeriod = 0
    if (this.trainClassDetail.trainClassBaseInfo.schemeType == 1) {
      const courseLearn = this.trainClassDetail.learningTypeModel.courseLearning
      totalRequirePeriod = CalculatorObj.add(courseLearn.compulsoryRequirePeriod, courseLearn.electiveRequirePeriod)
      this.trainClassDetail.userGetLearning.compulsoryRequirePeriod = courseLearn.compulsoryRequirePeriod || 0
      this.trainClassDetail.userGetLearning.electiveRequirePeriod = courseLearn.electiveRequirePeriod || 0
      console.log(
        totalRequirePeriod,
        'this.trainClassDetail.userGetLearning.selectedCoursePeriod',
        this.trainClassDetail.userGetLearning.selectedCoursePeriod
      )
      this.trainClassDetail.userGetLearning.needSelectedCoursePeriod = CalculatorObj.subtract(
        totalRequirePeriod,
        this.trainClassDetail.userGetLearning.selectedCoursePeriod
      )
    } else if (this.trainClassDetail.trainClassBaseInfo.schemeType == 2) {
      totalRequirePeriod = this.trainClassDetail.learningTypeModel.courseLearning.requirePeriod
    }
    this.trainClassDetail.userGetLearning.requirePeriod = totalRequirePeriod
    if (this.trainClassDetail.learningTypeModel.courseLearning.isAssess) {
      if (
        this.studentSchemeLearningResponse.studentLearning.courseLearning?.userAssessResult &&
        this.studentSchemeLearningResponse.studentLearning.courseLearning.userAssessResult.length
      ) {
        const courseAssessResultJson = JSON.parse(
          this.studentSchemeLearningResponse.studentLearning.courseLearning.userAssessResult[0]
        )
        if (this.trainClassDetail.trainClassBaseInfo.schemeType == 1) {
          this.trainClassDetail.userGetLearning.currentPeriod = CalculatorObj.add(
            courseAssessResultJson.compulsoryRequirePeriod.current,
            courseAssessResultJson.electiveRequirePeriod.current
          )
          this.trainClassDetail.userGetLearning.courseQualified =
            this.trainClassDetail.userGetLearning.currentPeriod >= this.trainClassDetail.userGetLearning.requirePeriod
        } else {
          this.trainClassDetail.userGetLearning.currentPeriod = courseAssessResultJson.requirePeriod.current

          // const classficationAssess = []
          let allClassficationsAssess = true
          this.studentSchemeLearningResponse.studentLearning.courseLearning.userAssessResult.forEach((item) => {
            const assesModel = JSON.parse(item)
            //代表是大纲考核
            if (assesModel.name && assesModel.name == 'Course_Assess_003') {
              const classification = this.findClassficationById(
                assesModel.ownerId,
                this.trainClassDetail.learningTypeModel.courseLearning.classification
              )
              if (classification) {
                classification.assessSetting.completePeriod = assesModel.requirePeriod.current
              }
              if (assesModel.requirePeriod.current < assesModel.requirePeriod.config) {
                allClassficationsAssess = false
              }
            }
          })
          if (
            allClassficationsAssess &&
            this.trainClassDetail.userGetLearning.currentPeriod >= this.trainClassDetail.userGetLearning.requirePeriod
          ) {
            this.trainClassDetail.userGetLearning.courseQualified = true
          }
        }
      }
    }
    if (this.trainClassDetail.learningTypeModel.exam.isAssess) {
      if (this.trainClassDetail.learningTypeModel.exam.allowCount == -1) {
        this.trainClassDetail.userGetLearning.surplusExamCount = -1
      } else {
        this.trainClassDetail.userGetLearning.surplusExamCount = CalculatorObj.subtract(
          this.trainClassDetail.learningTypeModel.exam.allowCount,
          this.trainClassDetail.userGetLearning.examCount
        )
        this.trainClassDetail.userGetLearning.surplusExamCount < 0 &&
          (this.trainClassDetail.userGetLearning.surplusExamCount = 0)
      }
    }
    // console.log('转换的详情对=', this.trainClassDetail)
  }

  //获取班级模板配置信息
  async requestClassConfig() {
    //获取培训班配置模板jsonString
    const res = await MsMySchemeQueryFrontGatewayCourseLearningForeStage.getSchemeConfigInServicer({
      schemeId: this.trainClassDetail.trainClassBaseInfo.id
    })
    // console.log('班级详情内的配置json', res.data.schemeConfig)
    if (res.status.isSuccess()) {
      this.getInfoFromJSON(res.data)
    }
    return res.status
  }
  findClassficationById(id: string, classification: Classification): Classification | undefined {
    if (classification.id == id) {
      return classification
    }
    if (classification.childOutlines && classification.childOutlines.length) {
      for (let i = 0; i < classification.childOutlines.length; i++) {
        return this.findClassficationById(id, classification.childOutlines[i])
      }
    }
    return undefined
  }
  //获取所有分类
  async getAllClassFi(
    qualificationId: string,
    classEnum: ClassFicationEnum = ClassFicationEnum.ClassFicationEnumChooseLearning
  ) {
    //获取培训班配置模板jsonString
    let fileString = []
    if (classEnum == ClassFicationEnum.ClassFicationEnumChooseLearning) {
      fileString = ['chooseCourseLearning']
    } else if (classEnum == ClassFicationEnum.ClassFicationEnumAutoLearning) {
      fileString = ['autonomousCourseLearning']
    } else {
      fileString = ['interestCourseLearning']
    }
    const res = await MsSchemeQueryFrontGatewayCourseLearningForestage.getMySchemeConfig({
      qualificationId: qualificationId,
      needField: fileString
    })
    if (res.status.isSuccess()) {
      const commodityDetail: SchemeConfigResponse = res.data
      const classConfigJson: any = TrainClassConfigJsonManager.calculatorSchemeConfigJson(commodityDetail.schemeConfig)
      const learningType = new LearningType()
      if (
        classEnum == ClassFicationEnum.ClassFicationEnumChooseLearning ||
        classEnum == ClassFicationEnum.ClassFicationEnumAutoLearning
      ) {
        TrainClassConfigJsonManager.configCourseLearning(learningType, classConfigJson)
        return learningType.courseLearning.classification.childOutlines
      } else {
        TrainClassConfigJsonManager.configInterest(learningType, classConfigJson)
        return learningType.interestCourse.classification.childOutlines
      }
    }
    return []
  }
  //获取班级详情
  async requestClassDetail() {
    // const queryGql = gql`
    //   query getSchemeLearningInMyself($qualificationId: String) {
    //     getSchemeLearningInMyself(qualificationId: $qualificationId) {
    //       status
    //     }
    //   }
    // `
    const res = await MsMySchemeQueryFrontGatewayCourseLearningForeStage.getStudentSchemeLearningInServicer(
      this.qualificationId
      // queryGql
    )
    if (res.status.isSuccess()) {
      const commodityDetail: StudentSchemeLearningResponse = res.data
      this.studentSchemeLearningResponse = commodityDetail
      this.schemeId = commodityDetail?.scheme?.schemeId
      this.getInfoFromStudentResponse(commodityDetail)
      await this.convertToTrainClassDetailClassVo(commodityDetail)
    }
    return res.status
  }

  /**
   * 从学员学习信息填充
   * @param commodityDetail
   */
  getInfoFromStudentResponse(commodityDetail: StudentSchemeLearningResponse) {
    this.trainClassDetail.trainClassBaseInfo.schemeType = SchemeTypeEnum[commodityDetail.scheme.schemeType]
    this.trainClassDetail.trainClassBaseInfo.id = commodityDetail.scheme.schemeId
    // this.trainClassDetail.trainClassBaseInfo.qualificationId = commodityDetail.qualificationId
    // this.trainClassDetail.trainClassBaseInfo.studentNo = commodityDetail.studentNo
    this.trainClassDetail.userGetLearning.trainingResult = commodityDetail.studentLearning.trainingResult
    // this.trainClassDetail.trainClassBaseInfo.trainingResultTime = commodityDetail.studentLearning.trainingResultTime
    if (commodityDetail.studentLearning.courseLearning) {
      this.trainClassDetail.userGetLearning.selectedCoursePeriod =
        commodityDetail.studentLearning.courseLearning.selectedCoursePeriod
    }
    if (commodityDetail.studentLearning.examLearning) {
      const examLearnRes = commodityDetail.studentLearning.examLearning
      this.trainClassDetail.userGetLearning.committedExam = examLearnRes.committedExam
      this.trainClassDetail.userGetLearning.examQualified = examLearnRes.examAssessResult == 1 ? true : false
      this.trainClassDetail.userGetLearning.examQualifiedTime = examLearnRes.examQualifiedTime
      this.trainClassDetail.userGetLearning.maxExamScore = examLearnRes.maxExamScore || 0
      this.trainClassDetail.userGetLearning.examCount = examLearnRes.examCount
    }
    if (commodityDetail.studentLearning.learningResult) {
      //配置培训证明
      const templateResult = commodityDetail.studentLearning.learningResult.find(
        (learnResult: LearningResultResponse) => learnResult.learningResultConfig.resultType == 2
      )
      if (templateResult) {
        // this.trainClassDetail.trainClassBaseInfo.hasLearningResult = true
        // this.trainClassDetail.trainClassBaseInfo.learningResultId = (templateResult.learningResultConfig as CertificateLearningConfigResultResponse).certificateTemplateId
        // this.trainClassDetail.trainClassBaseInfo.openPrintTemplate = true
        this.trainClassDetail.userGetLearning.learningResult.learningResultId = (
          templateResult.learningResultConfig as CertificateLearningConfigResultResponse
        ).certificateTemplateId
        this.trainClassDetail.userGetLearning.learningResult.learningResultName = ''
        //  TODO  去获取培训证明的详情
      }

      //获取培训成果中学分
      const creditLearningResult = commodityDetail.studentLearning.learningResult.find(
        (learnResult: LearningResultResponse) => learnResult.learningResultConfig.resultType == 1
      )
      if (creditLearningResult) {
        // this.trainClassDetail.trainClassBaseInfo.period = (creditLearningResult.learningResultConfig as GradeLearningConfigResultResponse).grade
        this.trainClassDetail.userGetLearning.credit = (
          creditLearningResult.learningResultConfig as GradeLearningConfigResultResponse
        ).grade
      }
    }
  }

  //转换成Vo对象
  async convertToTrainClassDetailClassVo(commodityDetail: StudentSchemeLearningResponse) {
    try {
      this.trainClassDetail.trainClassBaseInfo.skuProperty =
        await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
          commodityDetail.scheme.skuProperty as ComplexSkuPropertyResponse
        )
    } catch (e) {
      console.log(e)
    }
  }

  // endregion
}
export default QueryMyTrainClassDetail
