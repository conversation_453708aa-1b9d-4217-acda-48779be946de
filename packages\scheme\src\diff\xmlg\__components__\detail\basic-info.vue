<template>
  <xmlgBasicInfo ref="BasicInfoRef" v-bind="$attrs" v-on="$listeners">
    <template #third-platform>
      <el-form-item label="指定平台：" v-if="isDockThirdParty">
        {{ getQueryPlatformName }}
      </el-form-item>
    </template>
  </xmlgBasicInfo>
</template>

<script lang="ts">
  import { Component, Vue, PropSync, Ref } from 'vue-property-decorator'
  import TrainClassDetailClassVo from '@api/service/diff/management/xmlg/train-class/model/TrainClassDetailClassVo'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import getServicerIsDocking from '@api/service/management/online-school-config/portal/query/QueryPortal'
  import { isBoolean } from 'lodash'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
  import Context from '@api/service/common/context/Context'
  import QueryPlatform from '@api/service/diff/common/xmlg/dictionary/QueryPlatform'

  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import AchievementExhibition from '@api/service/common/enums/train-class/AchievementExhibitionEnum'
  import BasicInfo from '@hbfe/jxjy-admin-scheme/src/components/detail/basic-info.vue'
  @Component
  class xmlgBasicInfo extends BasicInfo {
    @PropSync('trainSchemeDetail', { type: TrainClassDetailClassVo }) schemeDetail: TrainClassDetailClassVo
  }

  @Component({
    components: {
      xmlgBasicInfo
    }
  })
  export default class extends Vue {
    @Ref('BasicInfoRef') BasicInfoRef: xmlgBasicInfo
    /**
     * 方案信息
     */
    schoolConfigFlag = false
    isDockThirdParty = false
    // 指定网校
    queryPlatform = QueryPlatform
    /**
     * 基础信息
     */
    get trainClassBaseInfo() {
      return this.BasicInfoRef?.schemeDetail?.trainClassBaseInfo
    }
    /**
     * 获取指定平台名称
     */
    get getQueryPlatformName() {
      return this.queryPlatform?.map?.get(this.BasicInfoRef.schemeDetail.trainClassBaseInfo.thirdPartyId)?.name || '-'
    }
    async mounted() {
      // 是否对接第三方平台
      const dockThirdPartyServicerIds = CommonConfigCenter.getFrontendApplication(
        frontendApplication.dockThirdPartyServicerIds
      )
      if (dockThirdPartyServicerIds) {
        this.isDockThirdParty =
          dockThirdPartyServicerIds.includes(Context.servicerInfo.id) && this.trainClassBaseInfo.schemeType === 3
      }
      // 指定平台字典口查询
      await this.queryPlatform.queryList()
      console.log('this.queryPlatform.list', this.queryPlatform.list)

      this.schoolConfigFlag = await getServicerIsDocking.getServicerIsDocking()
    }
  }
</script>
