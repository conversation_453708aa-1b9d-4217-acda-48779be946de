import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 证明模板使用范围
 */
export enum UsageRangeEnum {
  'ALL_SERVICER' = 'allServicer'
}
class UsageRange extends AbstractEnum<UsageRangeEnum> {
  static enum = UsageRangeEnum

  constructor(status?: UsageRangeEnum) {
    super()
    this.current = status
    this.map.set(UsageRangeEnum.ALL_SERVICER, '全网校')
  }
}

export default new UsageRange()
