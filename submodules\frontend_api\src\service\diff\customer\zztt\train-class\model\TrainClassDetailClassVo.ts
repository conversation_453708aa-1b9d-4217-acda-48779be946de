import MSCommodity from '@api/ms-gateway/ms-commodity-v1'
import MsLearningScheme from '@api/ms-gateway/ms-learningscheme-v1'
import MsLearningQueryFrontGatewayCourseLearningForestage, {
  LearningRegisterRequest,
  StudentSchemeLearningRequest,
  SchemeRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import MsTradeQueryFrontGatewayCourseLearningForestage, {
  OrderRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import LearningType from '@api/service/customer/train-class/query/vo/LearningType'
import SkuPropertyResponseVo from '@api/service/customer/train-class/query/vo/SkuPropertyResponseVo'
import TrainClassConfigVo from '@api/service/customer/train-class/query/vo/TrainClassConfigVo'
import { Response } from '@hbfe/common'
import { SourceEnum } from '@api/service/customer/train-class/query/Enum/SourceEnum'
import { SubjectType } from '@api/service/customer/thematic-config/enum/SubjectType'
import ThirdPartyItem from '@api/service/diff/customer/zztt/train-class/model/ThirdPartyItem'
import zzttGateway, { DropClassExtendedInfoResponse } from '@api/diff-gateway/platform-jxjypxtypt-zztt-school'
import HywSchemeRefundStatus, {
  HywSchemeRefundEnum
} from '@api/service/diff/customer/fjzj/train-class/enums/HywSchemeRefundStatus'
import zzttTradeGateway from '@api/diff-gateway/platform-jxjypxtypt-zztt-trade'
import { ResponseStatus } from '@hbfe/common'
import { CourseType } from '@api/service/diff/customer/zztt/train-class/enums/CourseType'
import SchemeUtil, { CheckTrainClassParam } from '@api/service/customer/train-class/offlinePart/util/SchemeUtil'
import PeriodInfo from '@api/service/customer/trade/single/query/vo/PeriodInfo'
/**
 * 培训班详情Vo
 */
class TrainClassDetailClassVo {
  // region properties
  /**
   * 商品来源类型
   <br> 0：子订单 1：换货单
   @see com.fjhb.ms.trade.query.order.constants.CommoditySkuSourceType
   */
  sourceType = 0
  /**
   * 商品来源ID
   <p>
   <br> 如果来源类型是子订单，那么来源ID是子订单号
   <br> 如果来源类型是换货单，那么来源ID是换货单
   */
  sourceId = ''
  /**
   * 培训方案类型
   <p>
   1: 选课规则
   2: 自主选课
   3: 培训合作
   @see SchemeType
   */
  schemeType?: number = 1
  /**
   * 商品id
   */
  commoditySkuId = ''
  /**
   *学习方式，类型为LearningType
   */
  learningTypeModel = new LearningType()

  /**
   * 培训方案id
   */
  schemeId = ''
  /**
   *班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuProperty = new SkuPropertyResponseVo()
  /**
   *方案图片，类型为string
   */
  picturePath = ''
  /**
   *方案名称，类型为string
   */
  trainClassName = ''
  /**
   *学时，类型为number
   */
  period = 0
  /**
   *价格，类型为number
   */
  price = 0
  /**
   *是否已购买，类型为boolean
   */
  bought = false
  /**
   * 子订单号
   */
  subOrderNo = ''
  /**
   *报名开始时间，类型为string
   */
  registerBeginDate = ''
  /**
   *报名结束时间，类型为string
   */
  registerEndDate = ''
  /**
   *培训开始时间，类型为string
   */
  trainingBeginDate = ''
  /**
   *培训结束时间，类型为string
   */
  trainingEndDate = ''
  /**
   *培训班考核配置信息，类型为TrainClassConfigVo
   */
  trainClassConfig = new TrainClassConfigVo()
  /**
   * 门户商品 - 商品id
   */
  portalCommoditySkuId = ''
  /**
   * 门户商品 - 来源类型
   1-网校商品 2-专题商品
   */
  portalCommoditySkuSourceType: SourceEnum = null
  /**
   * 门户商品 - 来源id(服务商id,专题id)
   */
  portalCommoditySkuSourceId = ''
  /**
   * 专题类型-门户商品来源为专题商品时
   */
  specialType: Array<SubjectType> = new Array<SubjectType>()
  /**
   * 第三方平台信息
   */
  thirdPartyInfo: ThirdPartyItem = null

  /**
   * 培训方案须知
   */
  notice = ''

  /**
   * 培训期别-须知
   */
  issueNotice = ''
  /**
   * 培训方案简介id
   */
  introId = ''

  /**
   * 培训方案简介
   */
  introContent = ''
  /**
   * 是否弹窗展示须知
   */
  showNoticeDialog = false
  /**
   * 校验口退款状态
   */
  refundStatus: HywSchemeRefundStatus = new HywSchemeRefundStatus()
  // endregion
  // region methods
  // region properties
  /**
   * 期别信息 （订单那边使用）
   */
  periodInfo = new PeriodInfo()

  /**
   * 住宿信息须知 （订单那边使用）
   */
  accommodationInstruction = ''
  /**
   * 是否开启住宿采集信息配置 （订单那边使用）
   */
  isOpenAccommodationGather = false
  /**
   * 是否是上架状态，true-已上架，false-未上架
   */
  isOnShelve = false
  /*
   *    获取参训资格id，仅在已报名状态才能获取
   * */
  async getQualificationId() {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.sourceType = this.sourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
    request.learningRegister.sourceId = this.sourceId
    const res = await MsLearningQueryFrontGatewayCourseLearningForestage.pageSchemeLearningInMyself({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      return res.data.currentPageData[0].qualificationId
    }
    return ''
  }
  /*
   *    获取参训资格id及学号（h5用），仅在已报名状态才能获取
   * */
  async getGoStudyInfo() {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    if (this.sourceId) {
      request.learningRegister.sourceType = this.sourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
      request.learningRegister.sourceId = this.sourceId
    } else {
      request.scheme = new SchemeRequest()
      request.scheme.schemeId = this.schemeId
    }
    const res = await MsLearningQueryFrontGatewayCourseLearningForestage.pageSchemeLearningInMyself({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      return {
        qualificationId: res.data.currentPageData[0].qualificationId,
        studentNo: res.data.currentPageData[0].studentNo
      }
    }
    return { qualificationId: '', studentNo: '' }
  }
  /**
   * 获取订单号，仅在已报名状态才能获取
   */
  async getOrderNo() {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    if (this.sourceId) {
      request.learningRegister.sourceType = this.sourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
      request.learningRegister.sourceId = this.sourceId
    } else {
      request.scheme = new SchemeRequest()
      request.scheme.schemeId = this.schemeId
    }

    const res = await MsLearningQueryFrontGatewayCourseLearningForestage.pageSchemeLearningInMyself({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      return res.data.currentPageData[0].learningRegister.orderNo
    }
    return ''
  }
  /**
   * 购买前校验
   */
  async checkTrainClass(channelType = 1, terminalCode = 'Web'): Promise<Response<string>> {
    const res = await MSCommodity.validateCommodity({
      commoditySkuId: this.commoditySkuId,
      channelType: channelType,
      terminalCode: terminalCode
    })
    let orderNo = ''
    if (res.status.isSuccess()) {
      if (res.data.code == '200') {
        const schemeRes = await MsLearningScheme.reservingSchemeValidate({
          schemeId: this.schemeId
        })
        if (res.status.isSuccess()) {
          res.status.code = parseInt(schemeRes.data.code)
          res.status.message = schemeRes.data.message
        } else {
          res.status = schemeRes.status
        }
        if (res.status.code == 50001) {
          try {
            const subOrderNo = schemeRes.data.duplicateReservingInfos[0].sourceId
            const request = new OrderRequest()
            request.subOrderNoList = [subOrderNo]
            const orderListRes = await MsTradeQueryFrontGatewayCourseLearningForestage.pageOrderInMyself({
              page: {
                pageNo: 1,
                pageSize: 1
              },
              request: request
            })
            if (orderListRes.status.isSuccess()) {
              const order = orderListRes.data.currentPageData[0]
              if ([0, 1].includes(order.basicData.orderPaymentStatus)) {
                if (order.basicData.channelType == 2) {
                  res.status.code = 52009
                  orderNo = order.orderNo
                } else {
                  res.status.code = 52001
                  orderNo = order.orderNo
                }
              }
            }
          } catch (e) {
            console.log('获取订单信息失败，', e)
          }
        }
      } else {
        res.status.code = parseInt(res.data.code)
        res.status.message = res.data.message
      }
    }
    const newRes = new Response<string>()
    Object.assign(newRes, res)
    if (newRes.status.code == 52001) {
      newRes.data = orderNo
    }
    return newRes
  }

  // endregion
  /**
   * 获取退款状态，仅在已报名状态才能获取
   */
  async getCourseRefund() {
    const request = new StudentSchemeLearningRequest()
    const response = new DropClassExtendedInfoResponse()
    request.learningRegister = new LearningRegisterRequest()
    if (this.sourceId) {
      request.learningRegister.sourceType = this.sourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
      request.learningRegister.sourceId = this.sourceId
    }
    const res = await zzttGateway.pageSchemeLearningInMyself({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      response.publicSign = res.data.currentPageData[0].dropClassExtendedInfoResponse.publicSign
      response.professionalSign = res.data.currentPageData[0].dropClassExtendedInfoResponse.professionalSign
      return response
    }
    return response
  }
  /**
   * 购买前校验
   * @param param.channelType 渠道类型
   * @param param.terminalCode 终端类型
   * @param param.issueId 期别id，面网授班、面授班必传
   */
  async checkTrainClassNew(param: { channelType: 1; terminalCode: 'Web'; issueId?: string }) {
    const request = new CheckTrainClassParam()
    request.commoditySkuId = this.commoditySkuId
    request.schemeId = this.schemeId
    request.channelType = param.channelType
    request.terminalCode = param.terminalCode
    if (param.issueId) {
      request.issueId = param.issueId
    }
    return await SchemeUtil.checkTrainClass(request)
  }
  /**
   *前往课程方案退款状态
   * @param orderNo 订单号
   * @param type 退课类型
   */
  async checkPublicRefundStatus(orderNo: string, type: CourseType): Promise<Response<HywSchemeRefundStatus>> {
    const response = new Response<HywSchemeRefundStatus>()
    const res = await zzttTradeGateway.queryHymSchemeRefundInfo(orderNo)
    if (res.status.isSuccess() && res.data) {
      const publicCourseRefundStatus = res.data.publicCourse
      const professionalCourseRefundStatus = res.data.professionalCourse
      if (publicCourseRefundStatus.refund && publicCourseRefundStatus.completed && type === CourseType.public_course) {
        //公需课退款完成
        this.refundStatus.current = HywSchemeRefundEnum.public_refunded
        response.status = new ResponseStatus(Number(res.data.code), res.data.message)
        response.data = this.refundStatus
        return response
      }
      if (
        professionalCourseRefundStatus.refund &&
        professionalCourseRefundStatus.completed &&
        type === CourseType.professional_course
      ) {
        //专业课退款完成
        this.refundStatus.current = HywSchemeRefundEnum.professional_refunded
        response.status = new ResponseStatus(Number(res.data.code), res.data.message)
        response.data = this.refundStatus
        return response
      }
      if (
        (professionalCourseRefundStatus.refund && !professionalCourseRefundStatus.completed) ||
        (publicCourseRefundStatus.refund && !publicCourseRefundStatus.completed)
      ) {
        //有课程处于退款状态中
        this.refundStatus.current = HywSchemeRefundEnum.scheme_refunding
      }
      response.status = new ResponseStatus(Number(res.data.code), res.data.message)
      response.data = this.refundStatus
    }
    return response
  }
}
export default TrainClassDetailClassVo
