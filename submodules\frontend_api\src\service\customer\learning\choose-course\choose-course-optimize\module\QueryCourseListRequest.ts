import {
  CourseInfoRequest,
  CourseOfCourseTrainingOutlineRequest,
  ExtInfoRequest,
  StudentChooseCourseRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

export default class QueryCourseListRequest {
  /**
   * 排除课程学习大纲id
   */
  excludeOutlineIds: string[] = []
  /**
   * 课程名称
   */
  courseName: string = undefined
  /**
   * 学号
   */
  studentNo: string = undefined
  /**
   * 内容提供方ID集合
   */
  contentIds: string[] = []
  static from(vo: QueryCourseListRequest) {
    const dto = new StudentChooseCourseRequest()
    dto.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    // dto.courseOfCourseTrainingOutline.outlineIds = vo.courseOutlineIdList
    dto.courseOfCourseTrainingOutline.excludeOutlineIds = vo.excludeOutlineIds
    dto.course = new CourseInfoRequest()
    dto.course.courseName = vo.courseName
    dto.studentNo = vo.studentNo
    dto.extInfo = new ExtInfoRequest()
    dto.extInfo.supplierIds = []
    return dto
  }
}
