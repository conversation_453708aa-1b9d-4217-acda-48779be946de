<route-meta>
{
"isMenu": true,
"title": "导出任务管理",
"sort": 2
}
</route-meta>

<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import {
    NZFXS,
    NZFXSJCB,
    NZGYS,
    NZGYSJCB,
    // 施教机构管理员
    WXGLY,
    // 专题管理员
    ZTGLY
  } from '@/models/RoleTypes'
  import ExportTask from '@hbfe/jxjy-admin-task/src/exporttask/index.vue'
  @RoleTypeDecorator({
    queryTask: [WXGLY, NZFXS, NZFXSJCB, NZGYS, NZGYSJCB, ZTGLY],
    location: [WXGLY, NZFXS, NZFXSJCB, NZGYS, NZGYSJCB, ZTGLY],
    viewLog: [WXGLY, NZFXS, NZFXSJCB, NZGYS, NZGYSJCB, ZTGLY],
    downErrorData: [WXGLY, NZFXS, NZFXSJCB, NZGYS, NZGYSJCB, ZTGLY],
    queryTaskListZt: [ZTGLY],
    queryTaskList: [WXGLY, NZFXS, NZFXSJCB, NZGYS, NZGYSJCB]
  })
  export default class extends ExportTask {}
</script>
