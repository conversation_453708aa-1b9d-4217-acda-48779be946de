<route-meta>
{ "title": "课程分类下拉搜索选择器" }
</route-meta>
<template>
  <div>
    <el-cascader
      class="biz-common"
      v-model="categoryIdList"
      :clearable="clearable"
      :filterable="filterable"
      @clear="categoryIdList = undefined"
      :props="props"
      :options="options"
      :placeholder="placeholder"
      @change="selectedChange"
      @focus="handleFocus"
      v-loading="loading"
    />
  </div>
</template>

<script lang="ts">
  import { Watch, Prop, Emit, Component, Vue } from 'vue-property-decorator'
  import { CascaderOptions } from '@hbfe/jxjy-admin-components/src/models/CascaderOptions'
  import QueryCourseCategory from '@api/service/management/resource/course-category/query/QueryCourseCategory'
  import CourseCategoryListDetail from '@api/service/management/resource/course-category/query/vo/CourseCategoryListDetail'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { rootCourseCategory } from '@api/service/common/config/CommonConfig'
  import { cloneDeep } from 'lodash'

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    multiple: boolean
    keyCount = 0
    activated() {
      console.log(this.options, 'this.options')
      console.log(this, 'this')

      console.log('this.activated')
    }
    deactivated() {
      console.log('this.deactivated')
    }
    @Prop({
      type: String,
      default: '请选择课程分类'
    })
    placeholder: string

    @Prop({
      type: Boolean,
      default: true
    })
    clearable: boolean

    @Prop({
      type: [Array, String]
    })
    value: Array<string>

    @Prop({
      type: Boolean,
      default: false
    })
    isArray: boolean

    @Prop({
      type: Boolean,
      default: true
    })
    showRootNode: boolean

    @Prop({
      type: String,
      default: ''
    })
    leafId: string

    @Prop({
      type: Boolean,
      default: true
    })
    checkStrictly: boolean

    @Prop({
      type: Boolean,
      default: false
    })
    filterable: boolean

    categoryIdList: Array<string> = new Array<string>()
    props = {
      multiple: false,
      label: 'name',
      value: 'id',
      lazy: true,
      leaf: 'leaf',
      checkStrictly: false,
      lazyLoad: async (node: { data: CourseCategoryListDetail }, resolve: any) => {
        if (node?.data?.id) {
          if (node?.data?.children?.length) {
            resolve(null)
          } else {
            const result = await this.query(node?.data?.id)
            resolve(result)
          }
        }
      }
    }
    options: any = []

    loading = false

    @Watch('value', {
      deep: true,
      immediate: true
    })
    async valueChange() {
      // 修复回显问题
      if (!this.options.length) {
        try {
          this.loading = true
          await this.handleFocus()
        } finally {
          this.loading = false
        }
      }
      this.categoryIdList = this.value
    }
    updated() {
      console.log('updated')
    }
    beforeUpdate() {
      console.log('beforeUpdate')
    }
    beforeDestroy() {
      console.log('beforeDestroy')
    }
    destroyed() {
      console.log('destroyed')
    }
    selectedChange() {
      if (this.categoryIdList && this.categoryIdList.length) {
        return this.categoryIdList
      }
    }

    get courseCategoryTreeList() {
      return this.queryCourseCategory.courseCategoryTreeList
    }

    queryCourseCategory = new QueryCourseCategory()

    async handleFocus() {
      this.setProps()
      if (this.filterable) {
        this.props.lazy = false
        const label = this.options[0]?.label ? 'label' : 'name'
        const value = this.options[0]?.value ? 'value' : 'id'
        this.props.label = label
        this.props.value = value
        if (this.showRootNode) {
          this.options = [
            {
              value: '-1',
              label: '课程分类',
              children: []
            }
          ]
          await this.getAllCategoryData(this.options[0].children)
        } else {
          this.options = []
          await this.getAllCategoryData(this.options)
        }
      } else {
        await this.loadDataList()
        if (this.value) await this.echo()
        this.categoryIdList = cloneDeep(this.value)
      }
    }

    //获取所有数据
    async getAllCategoryData(option: Array<CascaderOptions>) {
      const tree = await this.queryAll()
      tree
        .filter((res) => res.parentId === '-1')
        .forEach((item) => {
          const category = new CascaderOptions()
          category.label = item.name
          category.value = item.id
          category.parentId = item.parentId
          category.children = []
          option.push(category)
        })
      this.assembleCategory(tree, option)
    }

    /**
     * 组装树
     * @param tree
     * @param option
     */
    assembleCategory(tree: CourseCategoryListDetail[], option: Array<CascaderOptions>) {
      // * 递归处理组装数据
      option.forEach((item) => {
        const treeTemp = tree
          .filter((res) => res.parentId === item.value)
          .map((child) => {
            const category = new CascaderOptions()
            category.label = child.name
            category.value = child.id
            category.parentId = child.parentId
            category.children = []
            return category
          })
        if (!treeTemp.length) {
          item.children = []
          return
        }
        item.children = [...treeTemp]
        this.assembleCategory(tree, item.children)
      })
    }

    @Watch('categoryIdList', {
      deep: true
    })
    @Emit('input')
    changeValue() {
      console.log(this.categoryIdList, 'this.categoryIdList')

      return this.categoryIdList
    }

    async loadDataList() {
      if (this.showRootNode) {
        this.options = [rootCourseCategory]
      } else {
        this.options = await this.query('-1')
      }
    }

    async echo() {
      const list: string[] = cloneDeep(this.value)
      // 递归查询 拼装数据
      await this.formatTree(this.options, list.shift(), list)
    }

    /**
     * @description: 格式化这颗课件树
     * @param {*} optionList 当前层级的可选项
     * @param {*} searchNode 需要查找的节点
     * @param {*} nodes 当前节点集合
     * @return {*}
     */
    async formatTree(optionList: CourseCategoryListDetail[], searchNode: string, nodes: string[]) {
      if (!searchNode) {
        return
      }
      const currentOptions = optionList.filter((el) => {
        return el.id === searchNode
      })
      const currentOption: any = currentOptions.length ? currentOptions[0] : undefined
      if (!currentOption) return
      const subNodes = await this.query(currentOption?.id)
      currentOption.hasChildren = true
      currentOption.children = subNodes
      if (this.checkStrictly) {
        currentOption.leaf = false
      } else {
        currentOption.leaf = !nodes.length
      }
      await this.formatTree(subNodes, nodes.shift(), nodes)
    }

    async query(id?: string) {
      return await ResourceModule.courseCategoryFactory.query.queryChildrenById(id)
    }

    /**
     * 查询全部课程类别口
     */
    async queryAll() {
      return await ResourceModule.courseCategoryFactory.query.queryChildrenAll()
    }

    //未使用懒加载时使用的格式化数据方法
    formatCategoryTree(tree: Array<CourseCategoryListDetail>, option: Array<CascaderOptions>) {
      tree.forEach((item) => {
        const category = new CascaderOptions()
        category.label = item.name
        category.value = item.id
        if (item.children && item.children.length) {
          category.children = []
        }
        option.push(category)
        if (item.children && item.children.length) {
          this.formatCategoryTree(item.children, option[option.length - 1].children)
        }
      })
    }

    setProps() {
      this.props.multiple = this.multiple
      this.props.checkStrictly = this.checkStrictly
    }
  }
</script>
