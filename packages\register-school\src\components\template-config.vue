<template>
  <el-card shadow="never" class="m-card f-mb15 is-header-sticky">
    <el-collapse v-model="activeNames" accordion>
      <el-collapse-item name="3" class="m-collapse-item">
        <template slot="title">
          <span class="el-collapse-item__header">
            模板配置
          </span>
        </template>
        <div class="f-plr20">
          <!-- 人社行业 -->
          <template v-if="TemplateBaseConfig.haveRSIndustry">
            <template-industry
              :TemplateBaseConfig="TemplateBaseConfig"
              :pcCheckedList="pcCheckedList"
              :h5CheckedList="h5CheckedList"
              :TemplateConfigObj="TemplateConfigObj"
              @getList="getList"
              industryType="rs"
            ></template-industry>
          </template>
          <!-- 建设行业 -->
          <template v-if="TemplateBaseConfig.haveJSIndustry">
            <template-industry
              :TemplateBaseConfig="TemplateBaseConfig"
              :pcCheckedList="pcCheckedList"
              :h5CheckedList="h5CheckedList"
              :TemplateConfigObj="TemplateConfigObj"
              @getList="getList"
              industryType="js"
            ></template-industry>
          </template>
          <!-- 卫生行业 -->
          <template v-if="TemplateBaseConfig.haveWSIndustry">
            <template-industry
              :TemplateBaseConfig="TemplateBaseConfig"
              :pcCheckedList="pcCheckedList"
              :h5CheckedList="h5CheckedList"
              :TemplateConfigObj="TemplateConfigObj"
              @getList="getList"
              industryType="ws"
            ></template-industry>
          </template>
          <!-- 工勤行业 -->
          <template v-if="TemplateBaseConfig.haveGQIndustry">
            <template-industry
              :TemplateBaseConfig="TemplateBaseConfig"
              :pcCheckedList="pcCheckedList"
              :h5CheckedList="h5CheckedList"
              :TemplateConfigObj="TemplateConfigObj"
              @getList="getList"
              industryType="gq"
            ></template-industry>
          </template>
          <!-- 教师行业 -->
          <template v-if="TemplateBaseConfig.haveLSIndustry">
            <template-industry
              :TemplateBaseConfig="TemplateBaseConfig"
              :pcCheckedList="pcCheckedList"
              :h5CheckedList="h5CheckedList"
              :TemplateConfigObj="TemplateConfigObj"
              @getList="getList"
              industryType="ls"
            ></template-industry>
          </template>
          <template v-if="TemplateBaseConfig.haveYSIndustry">
            <template-industry
              :TemplateBaseConfig="TemplateBaseConfig"
              :pcCheckedList="pcCheckedList"
              :h5CheckedList="h5CheckedList"
              :TemplateConfigObj="TemplateConfigObj"
              @getList="getList"
              industryType="ys"
            ></template-industry>
          </template>
          <template
            v-if="
              !TemplateBaseConfig.haveRSIndustry &&
                !TemplateBaseConfig.haveJSIndustry &&
                !TemplateBaseConfig.haveWSIndustry &&
                !TemplateBaseConfig.haveGQIndustry &&
                !TemplateBaseConfig.haveLSIndustry &&
                !TemplateBaseConfig.haveYSIndustry
            "
          >
            <div>
              勾选具体培训行业后可选择模板
            </div>
          </template>
        </div>
      </el-collapse-item>
    </el-collapse>
  </el-card>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'
  import TemplateModule from '@api/service/common/template-school/TemplateModule'
  import templateIndustry from '@hbfe/jxjy-admin-registerSchool/src/components/template-industry.vue'
  class TemplateBaseConfig {
    haveRSIndustry: boolean
    haveJSIndustry: boolean
    haveWSIndustry: boolean
    haveGQIndustry: boolean
    haveLSIndustry: boolean
    haveYSIndustry: boolean
    provideWebService: boolean
    provideH5Service: boolean
  }

  @Component({
    components: {
      templateIndustry
    }
  })
  export default class extends Vue {
    @Prop({
      required: false,
      default: () => {
        return {
          haveRSIndustry: false,
          haveJSIndustry: false,
          haveWSIndustry: false,
          haveGQIndustry: false,
          haveLSIndustry: false,
          haveYSIndustry: false,
          provideWebService: false,
          provideH5Service: false
        }
      }
    })
    TemplateBaseConfig: TemplateBaseConfig
    TemplateConfigObj = TemplateModule
    activeNames: Array<string> = ['3']
    pcCheckedList: Array<string> = []
    h5CheckedList: Array<string> = []
    created() {
      this.$emit('childrenThis', this, 3)
    }
    getList(pclist: Array<string>, h5list: Array<string>) {
      this.pcCheckedList = pclist
      this.h5CheckedList = h5list
    }
    handleCheck() {
      return new Promise(resolve => {
        let result = {
          status: true,
          msg: '',
          data: {}
        }
        if (
          (!this.pcCheckedList.length && this.TemplateBaseConfig.provideWebService) ||
          (!this.h5CheckedList.length && this.TemplateBaseConfig.provideH5Service)
        ) {
          result = {
            status: false,
            msg: '网校模板未选择完整，请检查！',
            data: {}
          }
        } else {
          result.data = {
            webPortalTemplateId: this.pcCheckedList[0],
            H5PortalTemplateId: this.h5CheckedList[0]
          }
        }
        resolve(result)
      })
    }
  }
</script>

<style lang="scss" scoped>
  .el-collapse-item__header {
    font-size: 16px;
    font-weight: bold;
    padding-left: 20px;
    padding-right: 10px;
  }
  //模板配置-模版图片列表
  .m-demo-pic {
    li {
      &:nth-child(3n) {
        margin-right: 0;
      }
    }
    .demo-pic-info {
      height: 124px;
      color: #666;
      font-size: 14px;
      // p {
      //   line-height: 18px;
      //   margin-bottom: 8px;

      //   .t {
      //     display: inline-block;
      //     font-weight: bold;
      //   }

      //   &:last-child {
      //     margin-bottom: 0;
      //   }
      // }
      // }
    }
    .phone-text {
      height: 20px;
    }
    .template-config--pic-color {
      display: flex;
      align-items: center;
      .t {
        font-size: 14px;
        color: #666;
        font-weight: bold;
        width: 70px;
      }
      .color-template-con {
        display: flex;
        width: 200px;
        justify-content: space-evenly;
        .color-tem {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          cursor: pointer;
          // margin: 0 20px;
        }
      }
    }
  }
</style>
