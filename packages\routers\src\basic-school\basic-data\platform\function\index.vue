<!--
 * @Author: lixinye
 * @Date: 2023-06-05 09:35:52
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-03-21 15:42:43
 * @Description:
-->
<route-meta>
{
"isMenu": true,
"title": "功能设置",
"sort": 2,
"icon": "icon_guanli"
}
</route-meta>
<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  import functionInfo from '@hbfe/jxjy-admin-platform/src/function/index.vue'
  @RoleTypeDecorator({
    // 功能设置
    config: [WXGLY],
    electronicInvoice: [WXGLY],
    trainingCert: [WXGLY],
    qualityCourse: [WXGLY],
    courseSortConfig: [WXGLY],
    registLogin: [WXGLY],
    registConfig: [WXGLY],
    loginConfig: [WXGLY],
    applyConfig: [WXGLY],
    protocolConfig: [WXGLY],
    unitApply: [WXGLY],

    // 精品课程
    courseSelectComponent: [WXGLY],
    add: [WXGLY],
    remove: [WXGLY],
    change: [WXGLY],
    query: [WXGLY],

    // 课程排序
    check: [WXGLY],
    redace: [WXGLY],
    // courseSortConfig: [ WXGLY],

    // 培训证明
    cert: [WXGLY],
    seal: [WXGLY],

    // 监管规则
    trainingSupervision: [WXGLY],
    basicDataDraw: [WXGLY],
    supervisionBasicTableData: [WXGLY],
    addSupervisionRules: [WXGLY],
    supervisionRules: [WXGLY],

    //在线学习规则
    onlineLearningRules: [WXGLY],
    addOnlineLearningRules: [WXGLY],
    onlineLearningRuleList: [WXGLY],

    // 学习规则
    learningRules: [WXGLY],
    addLearningRules: [WXGLY],
    learningRuleList: [WXGLY],

    // 智能学习
    intelligentLearning: [WXGLY]
  })
  export default class extends functionInfo {}
</script>
