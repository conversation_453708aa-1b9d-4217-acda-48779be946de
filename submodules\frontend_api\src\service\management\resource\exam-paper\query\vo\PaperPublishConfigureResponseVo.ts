import {
  AutomaticPublishPattern,
  PaperPublishConfigureResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import { PublishPatternTypes } from '../../enum/ExamPaperPublishPatternTypes'
import { QuestionScopeSettingTypes } from '../../enum/ExamScopeSettingTypes'

/*
  试卷列表返回值
*/
class PaperPublishConfigureResponseVo extends PaperPublishConfigureResponse {
  /**
   * 出卷配置ID
   */
  id = ''

  /**
   * 出卷类型（组卷方式）
   */
  paperPublishPatterns: number = null

  /**
   * 出卷配置名称（试卷名称）
   */
  name = ''

  /*
    试卷分类名称
  */
  categoryName = ''

  /**
   * 创建人Id
   */
  createUserId = ''

  /**
   * 创建人名称
   */
  createUserName = ''

  /**
   * 创建时间
   */
  createdTime = ''

  /**
   * 是否启用 1 启用 2禁用
   */
  status: number = null

  /*
    出题方式
    【默认 按题库出题】
  */
  type: number = QuestionScopeSettingTypes.LibraryQuestionScopeSetting

  /*
    是否选中当前项
    【给页面用】
  */
  isSelected = false

  /*
    考试时长
  */
  suggestionTimeLength = 0

  static from(item: PaperPublishConfigureResponse) {
    const paper = new PaperPublishConfigureResponseVo()
    paper.id = item.id
    paper.paperPublishPatterns = item.paperPublishPatterns
    paper.categoryName = item.paperPublishConfigureCategory?.name
    paper.status = item.status
    paper.createdTime = item.createdTime
    paper.name = item.name
    paper.createUserId = item.createUserId
    // 智能卷
    if (item.paperPublishPatterns === PublishPatternTypes.AutomaticPublishPattern) {
      const pattern = item.publishPattern as AutomaticPublishPattern
      paper.suggestionTimeLength = pattern.suggestionTimeLength / 60
      paper.type = pattern.questionExtractRule?.questionScopes[0]?.type
    }
    return paper
  }
}

export default PaperPublishConfigureResponseVo
