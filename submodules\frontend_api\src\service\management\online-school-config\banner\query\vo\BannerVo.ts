import { BannerInfo } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { TrainingInstitutionBannerSaveRequest } from '@api/ms-gateway/ms-servicer-v1'

class BannerVo {
  /**
   * 唯一标识
   */
  id = ''
  /**
   * 排序
   */
  sort = 0
  /**
   * 图片地址
   */
  imgUrl = ''
  /**
   * 链接地址
   */
  link = ''
  /**
   * 创建时间
   */
  createTime = ''

  static from(res: BannerInfo) {
    const banner = new BannerVo()
    banner.id = res.id
    banner.imgUrl = res.path
    banner.link = res.link
    banner.sort = res.sort
    banner.createTime = res.createdTime
    return banner
  }

  static to(data: BannerVo) {
    const request = new TrainingInstitutionBannerSaveRequest()
    request.bannerLink = data.link
    request.bannerPath = data.imgUrl
    request.sort = data.sort
    return request
  }
}
export default BannerVo
