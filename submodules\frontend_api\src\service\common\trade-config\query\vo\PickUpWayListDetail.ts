import DeliverWayType, { DeliverWayTypeEnum } from '@api/service/common/trade-config/query/enums/DeliverWayType'

class PickUpWayListDetail {
  code: DeliverWayTypeEnum
  desc: string

  static from(type: DeliverWayTypeEnum) {
    const detail = new PickUpWayListDetail()
    detail.code = type
    detail.desc = new DeliverWayType().map.get(type)
    return detail
  }
}

export default PickUpWayListDetail
