import Classification from '@api/service/management/train-class/mutation/vo/Classification'
import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
import Mockjs from 'mockjs'

/**
 * @description 课程大纲分类工具类
 */

export class CreateOutlineUtils {
  /**
   * 递归设置大纲分类名称
   * @param {Classification[]} tree
   * @param
   */
  static setOutlineNameRecursion(tree: Classification[], parentId?: string) {
    if (!CreateSchemeUtils.isWeightyArray(tree)) return
    tree.map((el: Classification) => {
      el.parentId = parentId ? parentId : undefined
      //  修改培训方案时，课程大纲节点使用原来的id
      const lastId = el.id
      el.id = lastId ? lastId : Classification.classificationIdPre + '_' + Mockjs.Random.guid()
      el.childOutlines = CreateSchemeUtils.isWeightyArray(el.childOutlines) ? el.childOutlines : undefined
      if (el.childOutlines) {
        this.setOutlineNameRecursion(el.childOutlines, el.id)
      }
    })
  }

  /**
   * 同级节点重名校验
   * @param {Classification} originNode - 根元素
   * @param {Classification} item - 当前元素
   * @param {string} name - 要校验的节点名称
   * @return {boolean} 是否重名，true-是 | false-否
   */
  static validNodeNameIsRepeatInTheSameLevel(originNode: Classification, item: Classification, name: string): boolean {
    let validResult = false
    const parentId = item.parentId
    const id = item.id
    // 查找父级节点
    let parentNode = new Classification()
    if (!parentId) {
      //一级节点
      parentNode = originNode
    } else {
      // 二级及以后节点
      parentNode = CreateSchemeUtils.treeFind<Classification>(
        originNode.childOutlines,
        (node: Classification) => {
          return node.id === item.parentId
        },
        'childOutlines'
      )
    }
    if (CreateSchemeUtils.isWeightyArray(parentNode.childOutlines)) {
      const childOutlines = parentNode.childOutlines
      childOutlines.map((el) => {
        if (el.name === name && el.id !== id) {
          validResult = true
        }
      })
      return validResult
    }
  }
}
