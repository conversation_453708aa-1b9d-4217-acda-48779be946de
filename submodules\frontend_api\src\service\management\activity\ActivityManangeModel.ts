import ActivityManangeDetailModel from '@api/service/management/activity/models/ActivityManangeDetailModel'
import KnowledgeDataGateway, { AuditStudentLearningExperienceRequest } from '@api/ms-gateway/ms-knowledge-v1'
import CourseLearningForestage, {
  StudentLearningExperienceRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import CourseLearningBackstage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { AnswerMethodEnum, ApproveResultEnum } from '@api/service/management/activity/enum/ActivityEnum'
import UserModule from '@api/service/management/user/UserModule'
import { Page } from '@hbfe/common'
import MsMySchemeQueryFrontGatewayCourseLearningForeStage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
export default class ActivityManangeModel {
  /**
   * 活动管理详情id
   */
  ActivityManangeId = ''
  /**
   * 活动管理详情
   */
  ActivityManange: ActivityManangeDetailModel = new ActivityManangeDetailModel()

  /**
   * 查询详情
   */
  async queryDetail() {
    const request = new StudentLearningExperienceRequest()
    request.studentLearningExperienceIds = [this.ActivityManangeId]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 1
    const res = await CourseLearningForestage.pageLearningExperienceInStudent({ page, request })
    if (res.status.isSuccess() && res.data.currentPageData.length) {
      try {
        const data = res.data.currentPageData[0]
        this.ActivityManange = ActivityManangeDetailModel.from(data)
        // this.ActivityManangeId = data.studentLearningExperienceId
        // const course = await this.queryCourseDetail(data.experienceTypeInfo[0])
        // this.ActivityManange.courseDetail.id = course.id
        // this.ActivityManange.courseDetail.name = course.name
        const module = UserModule.queryUserFactory.queryStudentList
        if (data.student && data.student.userId) {
          const user = await module.queryStudentListInSubject([data.student.userId])
          this.ActivityManange.idCard = user.data[0].idCard
          this.ActivityManange.name = user.data[0].userName
        }
      } catch (error) {
        console.error(error, '学习心得列表 error')
      }
      // 提交内容（如果为文本时，存储关联文本id，如果是附件是一个json结构，包含名称和地址） fileName fileUrl
      if (this.ActivityManange.answerMethod == AnswerMethodEnum.UPLOAD) {
        try {
          const obj = JSON.parse(this.ActivityManange.content)
          this.ActivityManange.fileName = obj.fileName
          this.ActivityManange.LearningExperience = obj.fileUrl
        } catch (error) {
          console.log(error, 'fileName fileUrl error')
        }
      } else if (this.ActivityManange.answerMethod == AnswerMethodEnum.EDIT) {
        const res = await CourseLearningBackstage.getLearningExperienceContentInServicer(this.ActivityManange.content)
        this.ActivityManange.LearningExperience = res.data
      }
      return this.ActivityManange
    } else {
      return new ActivityManangeDetailModel()
    }
  }
  /**
   * 查询课程详情
   */
  async queryCourseDetail(id: string) {
    const response = await CourseLearningBackstage.getCourseInServicer(id)
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('获取课程详情失败', response)
      return Promise.reject(response)
    }
    return response.data
  }
  /* *
   * 提交审核
   */
  async doAudit() {
    const request = new AuditStudentLearningExperienceRequest()
    request.studentExperienceId = this.ActivityManangeId
    request.remark = this.ActivityManange.approveComment
    request.score = this.ActivityManange.approveResultScore
    request.learningExperienceTopicType = this.ActivityManange.learningExperienceType
    const result = await KnowledgeDataGateway.auditStudentLearningExperience(request)
    return result
  }
  /* *
   * id获取文本 管理端
   */
  async getText() {
    const res = await CourseLearningBackstage.getLearningExperienceContentInServicer(
      this.ActivityManange.LearningExperience
    )
    return res.data
  }
  //获取班级模板配置信息
  async requestClassConfig(schemeId: string) {
    //获取培训班配置模板jsonString
    const res = await MsMySchemeQueryFrontGatewayCourseLearningForeStage.getSchemeConfigInServicer({
      schemeId
    })
    // console.log('班级详情内的配置json', res.data.schemeConfig)
    let score = 0
    if (res.status.isSuccess()) {
      try {
        const config = JSON.parse(res.data.schemeConfig)
        if (config.learningExperienceLearning?.config?.courseLearningExperienceTopics) {
          score = config.learningExperienceLearning?.config?.courseLearningExperienceTopics[0]?.passScore
        } else {
          score = config.learningExperienceLearning?.config?.schemeLearningExperienceTopics[0]?.passScore || 0
        }
      } catch (error) {
        console.log(error, '获取培训班配置模板jsonString')
      }
      // this.getInfoFromJSON(res.data)
    }
    return score
  }
}
