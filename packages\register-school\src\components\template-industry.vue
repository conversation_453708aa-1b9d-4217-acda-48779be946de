<template>
  <div>
    <div class="m-tit is-small is-border-bottom f-pl0">
      <span class="tit-txt">{{ title }}行业</span>
    </div>
    <el-row :gutter="20" type="flex" justify="center">
      <el-col :lg="20" :xl="18">
        <el-form label-width="auto" class="m-form f-clear f-mt40">
          <el-form-item v-if="TemplateBaseConfig.provideWebService" label="PC端模板：">
            <el-checkbox-group
              v-for="(item, index) in TemplateConfigObj[pclistkey]"
              :key="item.id"
              v-model="pclist"
              @change="handleChange"
            >
              <ul class="m-demo-pic">
                <!-- nth-child(3n)失效 -->
                <li :style="(index + 1) % 3 == 0 ? 'margin-right:0' : ''">
                  <div class="demo-pic">
                    <div class="mask" style="z-index: 999">
                      <i class="icon el-icon-zoom-in" @click="maskPc(index)"></i>
                    </div>
                    <div class="pic">
                      <el-image
                        ref="imageRef"
                        :src="getPath(item.reviewPath)"
                        :preview-src-list="[getPath(item.reviewPath)]"
                      ></el-image>
                    </div>
                    <el-checkbox style="z-index: 1000" :label="item.id">{{
                      pclist.indexOf(item.id) != -1 ? '当前已选' : '请选择'
                    }}</el-checkbox>
                  </div>
                  <div class="demo-pic-info">
                    <p><span class="t">bananer轮播图片尺寸：</span>{{ item.bannerSize[0] }}*{{ item.bannerSize[1] }}</p>
                    <p>
                      <span class="t">客服电话图片尺寸：</span>{{ item.customerPhoneSize[0] }}*{{
                        item.customerPhoneSize[1]
                      }}
                    </p>
                    <p>
                      <span class="t">集体报名图片尺寸：</span>
                      <i v-if="item.groupRegistrationSize.length"
                        >{{ item.groupRegistrationSize[0] }}*{{ item.groupRegistrationSize[1] }}</i
                      >
                      <i v-else>-</i>
                    </p>
                    <p>
                      <span class="t">移动端悬浮图片尺寸：</span>{{ item.H5QRCodeSize[0] }}*{{ item.H5QRCodeSize[1] }}
                    </p>
                    <p><span class="t">友情链接图片尺寸：</span>{{ item.linksSize[0] }}*{{ item.linksSize[1] }}</p>
                  </div>
                </li>
              </ul>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item v-if="TemplateBaseConfig.provideH5Service" label="移动端（H5）模板：">
            <el-checkbox-group
              v-for="(item, index) in TemplateConfigObj[h5listkey]"
              :key="item.id"
              v-model="h5list"
              @change="handleChange"
            >
              <ul class="m-demo-pic">
                <li :style="(index + 1) % 3 == 0 ? 'margin-right:0' : ''">
                  <div class="demo-pic">
                    <div class="mask" style="z-index: 999">
                      <i class="icon el-icon-zoom-in" @click="maskH5(index)"></i>
                    </div>
                    <div class="pic">
                      <el-image
                        ref="imageRefH5"
                        :src="getPath(item.reviewPath)"
                        :preview-src-list="[getPath(item.reviewPath)]"
                      ></el-image>
                    </div>
                    <el-checkbox style="z-index: 1000" :label="item.id">{{
                      h5list.indexOf(item.id) != -1 ? '当前已选' : '请选择'
                    }}</el-checkbox>
                  </div>
                  <div class="demo-pic-info phone-text">
                    <p><span class="t">bananer轮播图片尺寸：</span>{{ item.bannerSize[0] }}*{{ item.bannerSize[1] }}</p>
                  </div>
                </li>
              </ul>
            </el-checkbox-group>
          </el-form-item>
          <div v-if="!TemplateBaseConfig.provideWebService && !TemplateBaseConfig.provideH5Service">
            勾选提供终端后可选择模板
          </div>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import TemplateModule from '@api/service/common/template-school/TemplateModule'
  class TemplateBaseConfig {
    haveRSIndustry: boolean
    haveJSIndustry: boolean
    haveWSIndustry: boolean
    haveGQIndustry: boolean
    haveLSIndustry: boolean
    haveYSIndustry: boolean
    provideWebService: boolean
    provideH5Service: boolean
  }

  @Component
  export default class extends Vue {
    @Prop({
      required: false,
      default: () => {
        return {
          haveRSIndustry: false,
          haveJSIndustry: false,
          haveWSIndustry: false,
          haveGQIndustry: false,
          haveLSIndustry: false,
          haveYSIndustry: false,
          provideWebService: false,
          provideH5Service: false
        }
      }
    })
    TemplateBaseConfig: TemplateBaseConfig
    @Prop({
      required: false,
      default: () => {
        return new Array<string>()
      }
    })
    pcCheckedList: Array<string>
    @Prop({
      required: false,
      default: () => {
        return new Array<string>()
      }
    })
    h5CheckedList: Array<string>
    @Prop({
      required: false,
      default: () => {
        return 'rs'
      }
    })
    industryType: string
    @Watch('industryType', { immediate: true, deep: true })
    getValue(val: any) {
      switch (val) {
        case 'rs':
          this.title = '人社'
          this.pclistkey = '_templateJSPCList'
          this.h5listkey = '_templateJSH5List'
          break
        case 'js':
          this.title = '建设'
          this.pclistkey = '_templateJSPCList'
          this.h5listkey = '_templateJSH5List'
          break
        case 'ws':
          this.title = '职业卫生'
          this.pclistkey = '_templateJSPCList'
          this.h5listkey = '_templateJSH5List'
          break
        case 'gq':
          this.title = '工勤'
          this.pclistkey = '_templateJSPCList'
          this.h5listkey = '_templateJSH5List'
          break
        case 'ls':
          this.title = '教师'
          this.pclistkey = '_templateJSPCList'
          this.h5listkey = '_templateJSH5List'
          break
        case 'ys':
          this.title = '药师'
          this.pclistkey = '_templateJSPCList'
          this.h5listkey = '_templateJSH5List'
          break
        default:
          break
      }
    }
    @Watch('pcCheckedList', { immediate: true, deep: true })
    @Watch('h5CheckedList', { immediate: true, deep: true })
    pcChangeValue(val: any) {
      this.changeValue()
    }
    TemplateConfigObj = TemplateModule
    title = ''
    pclistkey = ''
    h5listkey = ''
    pclist = new Array<string>()
    h5list = new Array<string>()

    changeValue() {
      this.pclist = this.pcCheckedList
      this.h5list = this.h5CheckedList
    }
    getPath(url: string) {
      if (url.indexOf('s-') != -1 || url == 'demo-h5-homepage-1') {
        return require('@design/trainingInstitution/assets/images/' + url + '.jpg')
      } else {
        return require('@design/trainingInstitution/assets/images/' + url + '.png')
      }
    }
    handleChange(val: Array<string>) {
      if (this.pclist.length > 1) {
        this.pclist.shift()
        this.pclist = val
      }
      if (this.h5list.length > 1) {
        this.h5list.shift()
        this.h5list = val
      }
      this.$emit('getList', this.pclist, this.h5list)
    }
    getColor(item: any, sitem: any) {
      item.color = sitem
    }
    maskPc(index: number) {
      this.$refs['imageRef'][index].$el.children[0].click()
    }

    maskH5(index: number) {
      this.$refs['imageRefH5'][index].$el.children[0].click()
    }
  }
</script>

<style lang="scss" scoped>
  .m-demo-pic {
    li {
      &:nth-child(3n) {
        margin-right: 0;
      }
    }
    .demo-pic-info {
      height: 124px;
      color: #666;
      font-size: 14px;
    }
  }
</style>
