import detail from './queries/detail.graphql'
import page from './queries/page.graphql'
import pageApplyRecordForTrainingInstitution from './queries/pageApplyRecordForTrainingInstitution.graphql'
import pageForTrainingInstitution from './queries/pageForTrainingInstitution.graphql'
import agreeApplyForTrainingInstitution from './mutates/agreeApplyForTrainingInstitution.graphql'
import apply from './mutates/apply.graphql'
import createChannelVendor from './mutates/createChannelVendor.graphql'
import createChannelVendorByAccount from './mutates/createChannelVendorByAccount.graphql'
import refuseApplyForTrainingInstitution from './mutates/refuseApplyForTrainingInstitution.graphql'

export {
  detail,
  page,
  pageApplyRecordForTrainingInstitution,
  pageForTrainingInstitution,
  agreeApplyForTrainingInstitution,
  apply,
  createChannelVendor,
  createChannelVendorByAccount,
  refuseApplyForTrainingInstitution
}
