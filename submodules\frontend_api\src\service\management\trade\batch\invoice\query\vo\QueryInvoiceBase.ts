import { Page } from '@hbfe/common'
import QueryPageInvoiceParam from '@api/service/management/trade/batch/invoice/query/dto/QueryPageInvoiceParam'
import {
  OnlineInvoiceRequest,
  OnlineInvoiceSortRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import InvoiceListResponse from '@api/service/management/trade/batch/invoice/query/vo/InvoiceListResponse'
import UserModule from '@api/service/management/user/UserModule'
import CollectiveManagerInfoVo from '@api/service/management/user/query/manager/vo/CollectiveManagerInfoVo'
import MsBill from '@api/ms-gateway/ms-bill-v1'

export default abstract class QueryInvoiceBase {
  /**
   * 分页查询发票 【增值税电子普通发票 自动开票】
   * @param page 页数
   * @param queryPageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<InvoiceListResponse>
   */
  abstract onLinePageInvoiceInServicer(
    page: Page,
    queryPageInvoiceParam: QueryPageInvoiceParam,
    sort: Array<OnlineInvoiceSortRequest>
  ): Promise<Array<InvoiceListResponse>>

  /**
   * 线上发票导出
   * @param queryPageInvoiceParam 查询参数
   */
  abstract onLinePageInvoiceInExport(queryPageInvoiceParam?: QueryPageInvoiceParam): Promise<boolean>

  /**
   * statisticInvoiceInServicer
   */
  abstract onLineStatisticInvoiceInServicer(
    page: Page,
    request?: OnlineInvoiceRequest,
    sort?: Array<OnlineInvoiceSortRequest>
  ): Promise<void>

  /**
   * 获取用户信息
   * @param ids id数组
   */
  protected async getUserInfo(ids: Array<string>) {
    if (ids.length === 0) {
      return new Map()
    }
    // 根据用户ID获取用户信息
    const response = await UserModule.queryUserFactory.queryCollectiveManagerList.queryCollectiveManagerInfoList(ids)
    const userIdMap: Map<string, CollectiveManagerInfoVo> = new Map()
    response.forEach(item => {
      userIdMap.set(item.userId, item)
    })
    return userIdMap
  }

  /**
   * 查询蓝票开票中异常信息
   */
  async queryBlueInvoiceException(invoiceIdList: Array<string>) {
    const invoiceResponseMap = new Map<string, string>()
    if (!invoiceIdList?.length) return invoiceResponseMap
    const { status, data } = await MsBill.getInvoicingServiceLogOfBlueTicket(invoiceIdList)
    if (status?.isSuccess()) {
      data?.forEach(item => {
        invoiceResponseMap.set(item.invoiceId, item.responseMessage)
      })
    }
    return invoiceResponseMap
  }

  /**
   * 查询红票开票中异常信息
   */
  async queryRedInvoiceException(invoiceIdList: Array<string>) {
    const invoiceResponseMap = new Map<string, string>()
    if (!invoiceIdList?.length) return invoiceResponseMap
    const { status, data } = await MsBill.getInvoicingServiceLogOfRedTicket(invoiceIdList)
    if (status?.isSuccess()) {
      data?.forEach(item => {
        invoiceResponseMap.set(item.invoiceId, item.responseMessage)
      })
    }
    return invoiceResponseMap
  }
}
