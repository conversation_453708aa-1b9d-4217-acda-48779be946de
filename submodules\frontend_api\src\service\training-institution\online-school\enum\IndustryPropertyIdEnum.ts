import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 行业属性id枚举
 */

export enum IndustryPropertyIdEnum {
  /**
   * 人社行业 福建省
   */
  RS = '052E23B08935457DBE2FF77D7661C0C2',
  /**
   * 河南省
   */
  HN = '6A87FBC4104849AAB531BAC8DCBBDFC9',
  /**
   * 安徽省
   */
  AH = '550e8400e29b41d4a716446655440000',
  /**
   * 甘肃
   */
  GS = 'CED2F70A98FE4501A5DEE4AE1055F969',
  /**
   * 江苏省
   */
  JX = '052E23B08935457DBE2FF77D727180E2',

  /**
   * 四川省人社
   */
  SCRS = 'CB5DF8BC2B6340E19D9CBC347D0D1A01',

  /**
   * 建设行业
   */
  JS = 'F5713750547111EFA5F937C05DB78097',

  /**
   * 四川建设
   */
  SC = '2DF1D015EC9B4CB1A47E98B7A7C1B634',

  /**
   * 卫生行业
   */
  WS = '98708b3ee0b74fda88a71a893086e792',

  /**
   * 工勤行业
   */
  GQ = '8ddf8ca7eeab418f97f654b2561bf493',
  /**
   * 教师行业
   */
  LS = '1c770e5e56784da588a2a9dc40490e29',

  /**
   * 江苏省
   */
  JSS = 'CC10F3CED1E54F45B09CD13307950651',

  /**
   * 药师行业
   */
  YS = '25BE32F6463E4A6E83A4971BDF51C3F4'
}
export default class IndustryPropertyId extends AbstractEnum<IndustryPropertyIdEnum> {
  static enum = IndustryPropertyIdEnum

  constructor(status?: IndustryPropertyIdEnum) {
    super()
    this.current = status
    this.map.set(IndustryPropertyIdEnum.RS, '福建省')
    this.map.set(IndustryPropertyIdEnum.HN, '河南省')
    this.map.set(IndustryPropertyIdEnum.AH, '安徽省')
    this.map.set(IndustryPropertyIdEnum.GS, '甘肃省')
    this.map.set(IndustryPropertyIdEnum.JSS, '江苏省')
    this.map.set(IndustryPropertyIdEnum.JX, '江西省')
    this.map.set(IndustryPropertyIdEnum.JS, '建设')
    this.map.set(IndustryPropertyIdEnum.SC, '四川省')
    this.map.set(IndustryPropertyIdEnum.WS, '职业卫生')
    this.map.set(IndustryPropertyIdEnum.GQ, '工勤')
    this.map.set(IndustryPropertyIdEnum.LS, '教师')
    this.map.set(IndustryPropertyIdEnum.YS, '药师')
    this.map.set(IndustryPropertyIdEnum.SCRS, '四川省人社')
  }
}
