import learningResultCommitCreatedEventRetry from './mutates/learningResultCommitCreatedEventRetry.graphql'
import learningResultCommitRemovedEventEventRetry from './mutates/learningResultCommitRemovedEventEventRetry.graphql'
import learningResultCommitUpdatedEventEventRetry from './mutates/learningResultCommitUpdatedEventEventRetry.graphql'
import learningResultRelateUserAssessIndicatorQualifiedEventHandle from './mutates/learningResultRelateUserAssessIndicatorQualifiedEventHandle.graphql'
import learningResultRetryGain from './mutates/learningResultRetryGain.graphql'
import reGainLearningResult from './mutates/reGainLearningResult.graphql'
import reGainStudentsLearningResult from './mutates/reGainStudentsLearningResult.graphql'
import retrySendEvent from './mutates/retrySendEvent.graphql'
import revokeLearningResult from './mutates/revokeLearningResult.graphql'
import revokeStudentsLearningResult from './mutates/revokeStudentsLearningResult.graphql'
import traineesAchievementRevocation from './mutates/traineesAchievementRevocation.graphql'

export {
  learningResultCommitCreatedEventRetry,
  learningResultCommitRemovedEventEventRetry,
  learningResultCommitUpdatedEventEventRetry,
  learningResultRelateUserAssessIndicatorQualifiedEventHandle,
  learningResultRetryGain,
  reGainLearningResult,
  reGainStudentsLearningResult,
  retrySendEvent,
  revokeLearningResult,
  revokeStudentsLearningResult,
  traineesAchievementRevocation
}
