<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!--屏幕分辨率 > 1680 的查询条件超过3个的，隐藏起来-->
        <!--屏幕分辨率 ≤ 1680 的查询条件超过2个的，隐藏起来-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="分销商品">
                <el-input v-model="input" clearable placeholder="请输入分销商品名称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="分销商">
                <el-input v-model="input" clearable placeholder="请输入分销商名称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="推广门户简称">
                <el-input v-model="input" clearable placeholder="请输入分销商推广门户简称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="报名时间">
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="起始时间"
                  end-placeholder="结束时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="分销价格">
                <div class="f-flex">
                  <el-input v-model="input" class="input-num f-flex-sub" />
                  <i class="f-mlr10">-</i>
                  <el-input v-model="input" class="input-num f-flex-sub" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="剔除培训方案">
                <el-select v-model="select" clearable placeholder="请选择不纳入统计的培训方案">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>导出列表数据</el-button>
                <el-button>重置</el-button>
                <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--操作栏-->
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert f-clear">
            <div class="f-c6 f-fl">
              搜索结果合计：当前分销推广净开通 <span class="f-fb f-co">8</span> 人次，成交总额
              <span class="f-fb f-co">¥ 99</span>
            </div>
            <div class="f-fr f-csp f-flex f-align-center"><i class="el-icon-info f-f16 f-mr5"></i>统计说明</div>
          </el-alert>
        </div>
        <!--表格-->
        <el-table stripe :data="tableData" border show-summary class="m-table f-mt10" :span-method="objectSpanMethod">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="分销商品" min-width="160" fixed="left">
            <template>公需课</template>
          </el-table-column>
          <el-table-column label="培训属性" min-width="160">
            <template>
              <p class="f-mb5">人社行业</p>
              <div class="f-c9 f-f12">
                <p>地区：读取培训方案的地区属性值</p>
                <p>科目类型：读取方案的科目属性值</p>
                <p>培训专业：方案的专业属性值</p>
                <p>培训年度：读取方案的培训年度</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="授权分销商" min-width="160">
            <template>
              分销商A
              <el-tag class="f-ml10" type="primary" effect="dark" size="mini">二级</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="分销定价方式" min-width="140" align="center">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-tag type="warning" size="mini" class="f-ml5 f-mr5">优惠申请</el-tag> ￥100
              </div>
              <div v-else><el-tag type="success" size="mini" class="f-ml5 f-mr5">定价方案</el-tag> ￥100</div>
            </template>
          </el-table-column>
          <el-table-column label="合计" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template>26252</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template>26</template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="110" align="right">
              <template>26</template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="110" align="right">
              <template>26</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template>26</template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="个人缴费" header-align="center">
            <el-table-column label="线上支付" header-align="center">
              <el-table-column label="开通" min-width="90" align="right">
                <template>26252</template>
              </el-table-column>
              <el-table-column label="退班" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换入(换班)" min-width="110" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换出(换班)" min-width="110" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="净开通" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
            </el-table-column>
          </el-table-column>
          <el-table-column label="集体报名" header-align="center">
            <el-table-column label="线上支付" header-align="center">
              <el-table-column label="开通" min-width="90" align="right">
                <template>26252</template>
              </el-table-column>
              <el-table-column label="退班" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换入(换班)" min-width="110" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换出(换班)" min-width="110" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="净开通" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="线下支付" header-align="center">
              <el-table-column label="开通" min-width="90" align="right">
                <template>26252</template>
              </el-table-column>
              <el-table-column label="退班" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换入(换班)" min-width="110" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换出(换班)" min-width="110" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="净开通" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
            </el-table-column>
          </el-table-column>
          <el-table-column label="导入开通" header-align="center">
            <el-table-column label="线下支付" header-align="center">
              <el-table-column label="开通" min-width="90" align="right">
                <template>26252</template>
              </el-table-column>
              <el-table-column label="退班" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换入(换班)" min-width="110" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="换出(换班)" min-width="110" align="right">
                <template>26</template>
              </el-table-column>
              <el-table-column label="净开通" min-width="90" align="right">
                <template>26</template>
              </el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-pagination
          background
          class="f-mt15 f-tr"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage4"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
        >
        </el-pagination>
      </el-card>
    </div>
  </el-main>
</template>

<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        isCollapse: false,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
        tableData1: [
          {
            id: 1,
            date: '2016-05-02',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1518 弄'
          },
          {
            id: 2,
            date: '2016-05-04',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1517 弄'
          },
          {
            id: 3,
            date: '2016-05-01',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1519 弄',
            children: [
              {
                id: 31,
                date: '2016-05-01',
                name: '王小虎',
                address: '上海市普陀区金沙江路 1519 弄'
              },
              {
                id: 32,
                date: '2016-05-01',
                name: '王小虎',
                address: '上海市普陀区金沙江路 1519 弄'
              }
            ]
          },
          {
            id: 4,
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄'
          }
        ],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },

    methods: {
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 1) {
          if (rowIndex % 2 === 0) {
            return {
              rowspan: 2,
              colspan: 1
            }
          } else {
            return {
              rowspan: 0,
              colspan: 0
            }
          }
        }
        if (columnIndex === 2) {
          if (rowIndex % 2 === 0) {
            return {
              rowspan: 2,
              colspan: 1
            }
          } else {
            return {
              rowspan: 0,
              colspan: 0
            }
          }
        }
        if (columnIndex === 3) {
          if (rowIndex % 2 === 0) {
            return {
              rowspan: 2,
              colspan: 1
            }
          } else {
            return {
              rowspan: 0,
              colspan: 0
            }
          }
        }
        if (columnIndex === 0) {
          if (rowIndex % 2 === 0) {
            return {
              rowspan: 2,
              colspan: 1
            }
          } else {
            return {
              rowspan: 0,
              colspan: 0
            }
          }
        }
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
