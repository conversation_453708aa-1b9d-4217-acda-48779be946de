const path = require('path')

module.exports = function() {
  return {
    configureWebpack: {
      devServer: {
        proxy: require('./build/config/proxyTable')
      },
      resolve: {
        alias: {
          '@packages': path.resolve(__dirname, 'packages'),
          '@api': path.resolve(__dirname, 'src'),
          '@/store': path.resolve(__dirname, 'src/store')
        }
      },
      module: {
        rules: [
          {
            test: /\.(graphql|gql)/,
            loader: 'raw-loader'
          }
        ]
      }
    },
    chainWebpack(config) {
      config.module
        .rule('eslint')
        .use('eslint-loader')
        .options({
          formatter: require('eslint-formatter-pretty')
        })
    }
  }
}
