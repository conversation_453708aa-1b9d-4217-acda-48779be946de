import { QuestionMode, QuestionType } from '@api/service/common/models/exam/enums'
/**
 * <AUTHOR> update 2021/1/28  TODO
 */
class QuestionCreate {
  /**
   * 题库id
   */
  libraryId: string
  /**
   * 题目
   */
  title: string
  /**
   * 试题类型
   */
  questionType: QuestionType
  /**
   * 难度
   */
  mode: QuestionMode
  /**
   * 难度值
   */
  difficulty = 0.0
  /**
   * 试题解析
   */
  description = ''

  /**
   * 试题内容
   * @see #questionType
   */
  questionContent: any
  /**
   * 是否启用
   */
  enabled: boolean

  /**
   * 关联课程id
   */
  relateCourseId: string
}

export default QuestionCreate
