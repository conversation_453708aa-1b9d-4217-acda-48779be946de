<template>
  <span>
    <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
      <span class="el-icon-edit-outline f-c9 edit-icon" @click="isShowEditbox = true"></span>
      <div slot="content">编辑</div>
    </el-tooltip>
    <div class="edit-box" v-if="isShowEditbox">
      <el-cascader
        v-model="editInputValue"
        clearable
        :options="gradeSelectorList"
        placeholder="请选择学段学科"
        :props="props"
        class="f-flex-sub"
        separator="-"
        @change="valChange"
      />
      <div class="op">
        <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
          <span class="el-icon-circle-check f-cb edit-icon" @click="saveEdit"></span>
          <div slot="content">保存</div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
          <span class="el-icon-circle-close f-c9 edit-icon" @click="cancelEdit"></span>
          <div slot="content">取消</div>
        </el-tooltip>
      </div>
    </div>
  </span>
</template>

<script lang="ts">
  import { Component, Vue, Prop, PropSync, Watch } from 'vue-property-decorator'
  import QueryGrade from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryGrade'
  import QueryPersonIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustry'
  import QuerySubject from '@api/service/common/basic-data-dictionary/query/person-dictionary/QuerySubject'

  @Component
  export default class extends Vue {
    // 修改的值
    editInputValue: Array<number> = []
    // 输入框显隐
    isShowEditbox = false
    // 修改后的值
    newValue = ''
    props = {}
    QueryGrade = QueryGrade

    setProps() {
      this.props = {
        label: 'name',
        value: 'code',
        children: 'children',
        leaf: 'leaf',
        checkStrictly: true
      }
    }

    @PropSync('learningPhase', {
      type: Number,
      default: 0
    })
    learningPhaseSync: number
    @PropSync('disciplin', {
      type: Number,
      default: 0
    })
    disciplinSync: number

    // 输入框提示语
    @Prop({
      type: String,
      default: '请选择学段学科'
    })
    placeholder: string

    @Prop({
      type: Array,
      default: []
    })
    value: Array<number>

    //行业属性Id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string

    //行业Id
    @Prop({
      type: String,
      default: ''
    })
    industryId: string

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valChange(val: Array<number>) {
      this.editInputValue = val
      this.learningPhaseSync = this.editInputValue[0]
      this.disciplinSync = this.editInputValue[1]
    }

    // 修改的值
    @Watch('editInputValue', {
      deep: true,
      immediate: true
    })
    inputValueChange(val: Array<number>) {
      this.$emit('input', val)
      this.editInputValue = val
    }

    /**
     * 后续维护补充类型
     */
    gradeSelectorList: Array<any> = []

    // 确认修改属性值
    saveEdit() {
      if (!this.editInputValue.includes(null) && this.editInputValue.length > 0) {
        this.$emit('saveEdit', true)
        this.isShowEditbox = false
      } else {
        return this.$message.error('请选择学段学科')
      }
    }

    isShowEditBtn() {
      this.isShowEditbox = true
      this.editInputValue = this.value || []
    }

    // 放弃修改属性值
    cancelEdit() {
      this.newValue = ''
      this.editInputValue = this.value || []
      this.isShowEditbox = false
    }
    async created() {
      this.setProps()
      this.gradeSelectorList = await QueryPersonIndustry.getSection(this.industryId)
      await Promise.all(
        this.gradeSelectorList.map(async item => {
          const res = await QueryPersonIndustry.getSubjects(item.propertyId)
          res?.map(ite => {
            ite['leaf'] = true
          })
          if (res?.length) {
            item.leaf = false
            item.children = res
          } else {
            item.leaf = true
          }
        })
      )
    }
  }
</script>
