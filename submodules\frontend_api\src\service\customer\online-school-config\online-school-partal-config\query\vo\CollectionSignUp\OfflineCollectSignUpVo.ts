import {
  OfflineCollectiveRegisterConfigResponse,
  OfflineCollectiveRegisterConfigSaveRequest
} from '@api/ms-gateway/ms-servicer-series-v1'
import CollectSignUpVo from './CollectSignUpVo'
import StepVo from './StepVo'

class OfflineCollectSignUpVo extends CollectSignUpVo {
  /**
   * 报名名称
   */
  title = '集体报名说明'
  /**
   * 底部文字说明
   */
  footContent = ''
  /**
   * 报名步骤
   */
  steps: Array<StepVo> = new Array<StepVo>()

  addStep() {
    const step = new StepVo()
    step.no = this.steps.length + 1
    this.steps.push(step)
  }

  removeStep(no: number) {
    // 删除下标为no-1的步骤
    this.steps.splice(no - 1, 1)
    this.steps?.forEach((step, index) => {
      step.no = index + 1
    })
  }

  from(res: OfflineCollectiveRegisterConfigResponse) {
    this.enable = res.enabled
    this.footContent = res.footContent
    this.templatePath = res.templatePath
    this.templateName = res.templateFileName
    this.title = res.title
    this.steps = res.steps
    this.signUpClassUrl = `${location.protocol}//${location.host}/manage/view-train-class`
  }

  to() {
    const request = new OfflineCollectiveRegisterConfigSaveRequest()
    request.enabled = this.enable
    request.footContent = this.footContent
    request.templatePath = this.templatePath
    request.templateFileName = this.templateName
    request.title = this.title
    request.steps = new Array<StepVo>()
    request.steps = this.steps
    return request
  }
}
export default OfflineCollectSignUpVo
