"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""生成华医网学员/报名信息请求内容"""
	encryptedContent(request:CreateSecretSignUpInfoRequest):EncryptedContentResponse
	"""分页获取我的培训方案学习列表
		@param page
		@param request
		@return
	"""
	pageSchemeLearningInMyself(page:Page,request:StudentSchemeLearningRequest,sort:[StudentSchemeLearningSortRequest]):ZZTTStudentSchemeLearningResponsePage @page(for:"ZZTTStudentSchemeLearningResponse")
	"""分销商-分页获取当前服务商管辖地区下的学员培训方案学习列表
		@param page
		@param request
		@param sort
		@param dataFetchingEnvironment
		@return
	"""
	pageStudentSchemeLearningInDistributor(page:Page,request:StudentSchemeLearningRequest,sort:[StudentSchemeLearningSortRequest]):ZZTTStudentSchemeLearningResponsePage @page(for:"ZZTTStudentSchemeLearningResponse")
	"""分页获取当前服务商管辖地区下的学员培训方案学习列表
		@param page
		@param request
		@param dataFetchingEnvironment
		@return
	"""
	pageStudentSchemeLearningInServicerV2(page:Page,request:StudentSchemeLearningRequest):ZZTTStudentSchemeLearningResponsePage @page(for:"ZZTTStudentSchemeLearningResponse")
	"""验证华医网是否存在学员信息/查寻学员信息"""
	validStudentInfo(request:ValidHymStudentInfoRequest!):ValidHymStudentInfoResponse
}
type Mutation {
	"""学员自主下华医网订单
		@return 订单创建序列号
	"""
	createHYWOrder(createOrderInfo:CreateHYWOrderRequest):CreateHYWOrderResultResponse
	"""注册学员并报班"""
	createStudentAndPlaceOrder(signUpRequest:CreateStudentAndPlaceOrderRequest):CreateStudentAndPlaceOrderResponse @optionalLogin
	"""推送学员报名信息"""
	syncStudentSignUpForApp(request:SyncStudentSignUpForAppRequest):SyncStudentSignUpForAppResponse
	"""推送学员报名信息-学员未注册"""
	syncStudentSignUpUnregistered(request:SyncStudentSignUpRegisteredRequest):SyncStudentSignUpForAppResponse
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input DeliveryAddress @type(value:"com.fjhb.domain.trade.api.offlineinvoice.events.entities.DeliveryAddress") {
	consignee:String!
	phone:String!
	region:String!
	address:String!
}
input TakePoint @type(value:"com.fjhb.domain.trade.api.offlineinvoice.events.entities.TakePoint") {
	pickupLocation:String!
	pickupTime:String!
	remark:String
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
input StudentSchemeLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningRequest") {
	studentNoList:[String]
	student:UserRequest
	learningRegister:LearningRegisterRequest
	scheme:SchemeRequest
	studentLearning:StudentLearningRequest
	dataAnalysis:DataAnalysisRequest
	connectManageSystem:ConnectManageSystemRequest
	extendedInfo:ExtendedInfoRequest
	openPrintTemplate:Boolean
	saleChannels:[Int]
	trainingChannelName:String
	trainingChannelId:String
	notDistributionPortal:Boolean
	trainingType:String
	issueId:String
}
input StudentSchemeLearningSortRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningSortRequest") {
	field:StudentSchemeLearningSortField
	policy:SortPolicy
}
input ConnectManageSystemRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ConnectManageSystemRequest") {
	syncStatus:Int
}
input DataAnalysisRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.DataAnalysisRequest") {
	trainingResultPeriod:DoubleScopeRequest
	requirePeriod:DoubleScopeRequest
	acquiredPeriod:DoubleScopeRequest
}
input ExtendedInfoRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ExtendedInfoRequest") {
	whetherToPrint:Boolean
	applyCompanyCode:String
	policyTrainingSchemeId:String
	policyTrainingSchemeName:String
}
input LearningRegisterRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.LearningRegisterRequest") {
	registerType:Int
	sourceType:String
	sourceId:String
	status:[Int]
	registerTime:DateScopeRequest
	saleChannels:[Int]
	orderNoList:[String]
	subOrderNoList:[String]
	batchOrderNoList:[String]
	distributorId:String
	portalId:String
}
input RegionRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.RegionRequest") {
	province:String
	city:String
	county:String
}
input StudentLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.learning.StudentLearningRequest") {
	trainingResultList:[Int]
	trainingResultTime:DateScopeRequest
	notLearningTypeList:[Int]
	courseScheduleStatus:Int
	examAssessResultList:[Int]
}
input RegionSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertySearchRequest") {
	region:[RegionSkuPropertyRequest]
	regionSearchType:Int
}
input SchemeRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeRequest") {
	schemeId:String
	schemeIdList:[String]
	schemeType:String
	schemeName:String
	skuProperty:SchemeSkuPropertyRequest
}
input SchemeSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeSkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	positionCategory:[String]
	trainingObject:[String]
	jobLevel:[String]
	jobCategory:[String]
	subject:[String]
	grade:[String]
	learningPhase:[String]
	discipline:[String]
	qualificationCategory:[String]
	trainingWay:[String]
	trainingInstitution:[String]
	mainAdditionalItem:[String]
}
input UserPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserPropertyRequest") {
	regionList:[RegionRequest]
	companyName:String
	payOrderRegionList:[RegionRequest]
}
input UserRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserRequest") {
	userIdList:[String]
	accountIdList:[String]
	userProperty:UserPropertyRequest
}
"""发票信息
	<AUTHOR>
	@since 2021/3/23
"""
input InvoiceInfoRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.request.InvoiceInfoRequest") {
	"""发票抬头"""
	title:String
	"""发票抬头类型
		<pre>
		1-个人
		2-企业
		</pre>
	"""
	titleType:Int
	"""发票类型
		<pre>
		1-电子发票
		2-纸质发票
		</pre>
	"""
	invoiceType:Int
	"""发票种类
		<pre>
		1-普通发票
		2-增值税普通发票
		3-增值税专用发票
		</pre>
	"""
	invoiceCategory:Int
	"""购买方纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账户"""
	account:String
	"""发票票面备注"""
	remark:String
	"""开票方式
		1 - 线上开票
		2 - 线下开票
	"""
	invoiceMethod:Int
	"""联系电子邮箱"""
	email:String
	"""联系电话"""
	contactPhone:String
	"""营业执照"""
	businessLicensePath:String
	"""开户许可"""
	accountOpeningLicensePath:String
	"""配送方式
		0/1/2,无/自取/快递
		@see OfflineShippingMethods
	"""
	shippingMethod:Int
	"""配送地址信息"""
	deliveryAddress:DeliveryAddress
	"""自取点信息"""
	takePoint:TakePoint
}
"""@Description
	<AUTHOR>
	@Date 2025/5/26 20:58
"""
input Commodity @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.Commodity") {
	"""商品sku编号"""
	skuId:String
	"""商品数量"""
	quantity:Int
}
"""请求创建订单
	<AUTHOR>
	@since 2021/1/22
"""
input CreateHYWOrderRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.CreateHYWOrderRequest") {
	"""买家编号"""
	buyerId:String!
	"""商品列表"""
	commodities:[Commodity]!
	"""是否需要发票"""
	needInvoice:Boolean!
	"""发票信息"""
	invoiceInfo:InvoiceInfoRequest
	"""终端类型
		<p>
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	terminalCode:String!
}
"""@Description
	<AUTHOR>
	@Date 2024/7/2 20:16
"""
input CreateSecretSignUpInfoRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.CreateSecretSignUpInfoRequest") {
	"""订单号"""
	orderNo:String
}
"""请求创建订单
	<AUTHOR>
	@since 2021/1/22
"""
input CreateStudentAndPlaceOrderRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.CreateStudentAndPlaceOrderRequest") {
	"""密文"""
	ciphertext:String!
	"""13位时间戳，用于请求过期校验"""
	timer:Long
	"""签名"""
	sign:String
}
"""@Description
	<AUTHOR>
	@Date 2024/7/2 20:16
"""
input SyncStudentSignUpForAppRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.SyncStudentSignUpForAppRequest") {
	"""订单号"""
	orderNo:String
}
input SyncStudentSignUpRegisteredRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.SyncStudentSignUpRegisteredRequest") {
	"""加密信息
		@see  HywSyncStudentSignUpRegisteredParam
	"""
	params:String
	"""时间戳"""
	time_span:String
	"""签名"""
	sign:String
}
input ValidHymStudentInfoRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.request.ValidHymStudentInfoRequest") {
	"""学员姓名"""
	userName:String!
	"""证件号"""
	certificateNumber:String!
}
type ConnectManageSystemResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.ConnectManageSystemResponse") {
	syncStatus:Int
	syncMessage:String
}
type BatchOwnerResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.BatchOwnerResponse") {
	unitId:String
	userId:String
}
type ExtendedInfoResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.ExtendedInfoResponse") {
	whetherToPrint:Boolean
	printTime:DateTime
	pdfUrl:String
	certificateId:String
	certificateNo:String
}
type LearningRegisterResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.LearningRegisterResponse") {
	registerType:Int
	sourceType:String
	sourceId:String
	status:Int
	statusChangeTime:DateTime
	registerTime:DateTime
	saleChannel:Int
	orderNo:String
	subOrderNo:String
	batchOrderNo:String
	frozenAndInvalidSourceType:String
	frozenAndInvalidSourceId:String
}
type OwnerResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.OwnerResponse") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerType:Int
	servicerId:String
	batchOwner:BatchOwnerResponse
}
type CourseLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.CourseLearningResponse") {
	courseScheduleStatus:Int
	courseQualifiedTime:DateTime
	selectedCourseCount:Int
	selectedCoursePeriod:Double
	learningId:String
	learningType:Int
	enabled:Boolean
	learningResourceType:Int
	learningResourceId:String
	userAssessResult:[String]
	learningResult:[LearningResultResponse]
}
type DataAnalysisResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.DataAnalysisResponse") {
	trainingResultPeriod:Double
	requirePeriod:Double
	acquiredPeriod:Double
}
type ExamLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.ExamLearningResponse") {
	committedExam:Boolean
	examAssessResult:Int
	examQualifiedTime:DateTime
	examCount:Int
	maxExamScore:Double
	learningId:String
	learningType:Int
	enabled:Boolean
	learningResourceType:Int
	learningResourceId:String
	userAssessResult:[String]
	learningResult:[LearningResultResponse]
}
type LearningExperienceLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.LearningExperienceLearningResponse") {
	committedLearningExperience:Boolean
	learningExperienceAssessResult:Int
	learningExperienceQualifiedTime:DateTime
	maxLearningExperienceScore:Double
	learningExperiencePassCount:Long
	learningId:String
	learningType:Int
	enabled:Boolean
	learningResourceType:Int
	learningResourceId:String
	userAssessResult:[String]
	learningResult:[LearningResultResponse]
}
type StudentLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.StudentLearningResponse") {
	trainingResult:Int
	trainingResultTime:DateTime
	courseLearning:CourseLearningResponse
	examLearning:ExamLearningResponse
	learningExperienceLearning:LearningExperienceLearningResponse
	userAssessResult:[String]
	learningResult:[LearningResultResponse]
}
type CertificateLearningConfigResultResponse implements LearningResultConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.result.CertificateLearningConfigResultResponse") {
	certificateTemplateId:String
	openPrintTemplate:Boolean!
	resultType:Int
}
type GradeLearningConfigResultResponse implements LearningResultConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.result.GradeLearningConfigResultResponse") {
	gradeType:String
	grade:Double
	resultType:Int
}
interface LearningResultConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.result.LearningResultConfigResponse") {
	resultType:Int
}
type LearningResultResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.result.LearningResultResponse") {
	learningResultId:String
	gainedTime:DateTime
	learningResultConfig:LearningResultConfigResponse
}
type SchemeResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.scheme.SchemeResponse") {
	schemeId:String
	schemeType:String
	skuProperty:SchemeSkuPropertyResponse
	schemeName:String
	learningResult:[LearningResultConfigResponse]
}
type SchemeSkuPropertyResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.scheme.SchemeSkuPropertyResponse") {
	year:SchemeSkuPropertyValueResponse
	province:SchemeSkuPropertyValueResponse
	city:SchemeSkuPropertyValueResponse
	county:SchemeSkuPropertyValueResponse
	industry:SchemeSkuPropertyValueResponse
	subjectType:SchemeSkuPropertyValueResponse
	trainingCategory:SchemeSkuPropertyValueResponse
	trainingProfessional:SchemeSkuPropertyValueResponse
	technicalGrade:SchemeSkuPropertyValueResponse
	positionCategory:SchemeSkuPropertyValueResponse
	trainingObject:SchemeSkuPropertyValueResponse
	jobLevel:SchemeSkuPropertyValueResponse
	jobCategory:SchemeSkuPropertyValueResponse
	subject:SchemeSkuPropertyValueResponse
	grade:SchemeSkuPropertyValueResponse
	learningPhase:SchemeSkuPropertyValueResponse
	discipline:SchemeSkuPropertyValueResponse
	certificatesType:SchemeSkuPropertyValueResponse
	practitionerCategory:SchemeSkuPropertyValueResponse
	trainingInstitution:SchemeSkuPropertyValueResponse
	mainAdditionalItem:SchemeSkuPropertyValueResponse
	trainingWay:SchemeSkuPropertyValueResponse
}
type SchemeSkuPropertyValueResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.scheme.SchemeSkuPropertyValueResponse") {
	skuPropertyValueId:String
}
type RegionResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.user.RegionResponse") {
	province:String
	city:String
	county:String
}
type UserPropertyResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.user.UserPropertyResponse") {
	region:RegionResponse
	payOrderRegion:RegionResponse
}
type UserResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.user.UserResponse") {
	userId:String
	accountId:String
	userProperty:UserPropertyResponse
}
enum SortPolicy @type(value:"com.fjhb.ms.scheme.learning.query.kernel.constants.SortPolicy") {
	ASC
	DESC
}
enum StudentSchemeLearningSortField @type(value:"com.fjhb.ms.scheme.learning.query.kernel.constants.StudentSchemeLearningSortField") {
	REGISTER_TIME
	SCHEME_YEAR
}
"""校验结果返回
	<AUTHOR> create 2021/2/3 10:53
"""
type VerifyResultResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.response.VerifyResultResponse") {
	"""校验结果"""
	message:String
	"""校验code"""
	code:String
	"""订单内的商品skuId"""
	skuId:String
	"""目前是(ms-learningscheme_reservingScheme)返回的子订单"""
	subOrderNo:String
	"""@see StudentSourceTypes"""
	sourceType:String
	sourceId:String
}
"""创建订单结果
	<AUTHOR> create 2021/1/29 17:27
"""
type CreateHYWOrderResultResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.response.CreateHYWOrderResultResponse") {
	"""是否创建成功"""
	success:Boolean!
	"""订单号，仅当{@link #success}为{@code true}时有值"""
	orderNo:String
	"""子订单号"""
	subOrderNoList:[String]
	"""订单创建时间，仅当{@link #success}为{@code true}时有值"""
	createTime:DateTime
	"""下单结果信息"""
	message:String
	"""- 200成功
		- 4002 用户未登录
		- 4003 主方案或者合并方案选择错误
		- 5001 订单创建异常
	"""
	code:Int
	"""商品验证结果信息"""
	resultList:[VerifyResultResponse]
}
"""创建订单结果
	返回的code
	<p>
	200      成功
	500	    程序内部异常
	6001     密文解析失败
	90001    数据延迟
	50001 	不可重复报名同一个班级
	30001 	物品不存在
	30002	商品处于下架状态
	30003	购买渠道已关闭
	30004	购买终端已关闭
	50002	培训未开始
	50003	培训已结束
	50006 	当前用户此班级正在开班中
	50007	当前用户此班级正在退班中
"""
type CreateStudentAndPlaceOrderResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.response.CreateStudentAndPlaceOrderResponse") {
	"""学员ID"""
	userId:String
	"""订单号"""
	orderNo:String
	"""子订单号"""
	subOrderNoList:[String]
	"""登录token"""
	token:String
	"""参训资格id"""
	qualificationId:String
	"""跳转页面
		@see ZZTTKeyEntity
		商品下单页：PLACEORDER
		学习页：LEARNING
		支付页面：PAY
	"""
	key:String
	"""是否学员自主下单"""
	isSelfOrder:Boolean!
	"""skuId"""
	skuId:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
type DropClassExtendedInfoResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.response.DropClassExtendedInfoResponse") {
	"""已退公需学习内容标识 (1为专业课，2为公需课)"""
	publicSign:Boolean!
	"""已退专业学习内容标识"""
	professionalSign:Boolean!
}
"""@Description
	<AUTHOR>
	@Date 2024/7/2 20:17
"""
type EncryptedContentResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.response.EncryptedContentResponse") {
	"""对接平台编号"""
	unitCode:String
	"""参数加密"""
	params:String
	"""13位时间戳，用于url过期校验"""
	timer:String
	"""签名"""
	sign:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""code如下：
	200  正常
	500  异常
	20001    人员不存在
"""
type SyncStudentSignUpForAppResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.response.SyncStudentSignUpForAppResponse") {
	"""加密信息"""
	data:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""H5验证华医网是否存在学员信息返回结果"""
type ValidHymStudentInfoResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.response.ValidHymStudentInfoResponse") {
	"""1.如是登录的身份为姓名+身份证+福建完全匹配单点登录，且用户开通权限（未开通权限由华医网开通权限）则进入掌上华医-专业课学习（选课列表）
		2.如学员存在姓名+身份证，但注册地址非福建地区，则返回信息：您的当前地区不可学习该项目，请联系客服进行地区修改！
		3.如学员身份证与姓名不符，则返回信息：尊敬的学员您好，您目前登录的身份1111111111******1234，不是您本人的账号，信息有误。
		4.如登录角色未进行注册，则直接跳转至厦门理工平台注册页进行注册
	"""
	type:String
	"""提示信息"""
	TipMessage:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
type ZZTTStudentSchemeLearningResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.response.ZZTTStudentSchemeLearningResponse") {
	"""退课扩展消息"""
	dropClassExtendedInfoResponse:DropClassExtendedInfoResponse
	qualificationId:String
	studentNo:String
	owner:OwnerResponse
	student:UserResponse
	learningRegister:LearningRegisterResponse
	scheme:SchemeResponse
	studentLearning:StudentLearningResponse
	dataAnalysis:DataAnalysisResponse
	connectManageSystem:ConnectManageSystemResponse
	extendedInfo:ExtendedInfoResponse
	schemeQuestionnaireRequirementCount:Int
	schemeQuestionnaireNoAssessSubmittedCount:Int
	schemeQuestionnaireSubmittedCount:Int
	issueName:String
}

scalar List
type ZZTTStudentSchemeLearningResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ZZTTStudentSchemeLearningResponse]}
