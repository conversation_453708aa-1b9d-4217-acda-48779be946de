import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import systemContext from '@api/service/common/context/Context'
//安溪进修校差异化
class Axjxx {
  isAnxi = false
  //培训证明文字差异化
  certText = '培训证明'
  courseSelection = '还需选课'
  studyWay = '继续学习'
  idCardType = '请输入证件号/手机号'
  isShowCardIdPassWord = false

  buildAnxiInfo() {
    this.isAnxi =
      ConfigCenterModule.getFrontendApplication(frontendApplication.anxiServicerId) == systemContext.servicerInfo.id
    if (this.isAnxi) {
      this.certText = '结业证'
      this.courseSelection = '专业课'
      this.studyWay = '立即学习'
      this.idCardType = '请输入身份证号'
      this.isShowCardIdPassWord = true
    }
  }
  //通过服务商ID区分安溪和当前网校
}
export default new Axjxx()
