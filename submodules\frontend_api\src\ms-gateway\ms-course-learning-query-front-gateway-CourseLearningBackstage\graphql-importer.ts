import clearStudentLearningRule from './queries/clearStudentLearningRule.graphql'
import countCourseInSubProject from './queries/countCourseInSubProject.graphql'
import countCoursewareInSubProject from './queries/countCoursewareInSubProject.graphql'
import countPeriodCountOfCourseTrainingOutlineInSchemeInServicer from './queries/countPeriodCountOfCourseTrainingOutlineInSchemeInServicer.graphql'
import getCourseCategoryInServicer from './queries/getCourseCategoryInServicer.graphql'
import getCourseCountBySchemeIdInServicer from './queries/getCourseCountBySchemeIdInServicer.graphql'
import getCourseInServicer from './queries/getCourseInServicer.graphql'
import getCoursePackageInServicer from './queries/getCoursePackageInServicer.graphql'
import getCourseSortRuleConfigDetailInServicer from './queries/getCourseSortRuleConfigDetailInServicer.graphql'
import getCourseTrainingOutlineInServicer from './queries/getCourseTrainingOutlineInServicer.graphql'
import getCoursewareCategoryInServicer from './queries/getCoursewareCategoryInServicer.graphql'
import getCoursewareInServicer from './queries/getCoursewareInServicer.graphql'
import getLearningExperienceContentInServicer from './queries/getLearningExperienceContentInServicer.graphql'
import getRepeatedCoursesInSpecifiedCoursePackageInServicer from './queries/getRepeatedCoursesInSpecifiedCoursePackageInServicer.graphql'
import getStudentCourseAppraiseStarStatisticsInCourseSupplier from './queries/getStudentCourseAppraiseStarStatisticsInCourseSupplier.graphql'
import getStudentCourseAppraiseStarStatisticsInServicer from './queries/getStudentCourseAppraiseStarStatisticsInServicer.graphql'
import getStudentCourseLearningInServicer from './queries/getStudentCourseLearningInServicer.graphql'
import getStudentCourseLearningInSubProject from './queries/getStudentCourseLearningInSubProject.graphql'
import getStudentLearningRule from './queries/getStudentLearningRule.graphql'
import isBeingUsedAsCompulsory from './queries/isBeingUsedAsCompulsory.graphql'
import listCourseCategoryInServicer from './queries/listCourseCategoryInServicer.graphql'
import listCourseInSchemeInServicer from './queries/listCourseInSchemeInServicer.graphql'
import listLearningExperienceLatestInServicer from './queries/listLearningExperienceLatestInServicer.graphql'
import listLearningExperienceTopic from './queries/listLearningExperienceTopic.graphql'
import listTeacherInServicer from './queries/listTeacherInServicer.graphql'
import pageChooseCourseStatistics from './queries/pageChooseCourseStatistics.graphql'
import pageChooseCourseStatisticsInCourseSupplier from './queries/pageChooseCourseStatisticsInCourseSupplier.graphql'
import pageChooseCourseStatisticsInServicer from './queries/pageChooseCourseStatisticsInServicer.graphql'
import pageChooseCourseStatisticsMyself from './queries/pageChooseCourseStatisticsMyself.graphql'
import pageCourseDistributionResponse from './queries/pageCourseDistributionResponse.graphql'
import pageCourseInCourseSupplier from './queries/pageCourseInCourseSupplier.graphql'
import pageCourseInPackageInServicer from './queries/pageCourseInPackageInServicer.graphql'
import pageCourseInPackageV2InServicer from './queries/pageCourseInPackageV2InServicer.graphql'
import pageCourseInSchemeInServicer from './queries/pageCourseInSchemeInServicer.graphql'
import pageCourseInServicer from './queries/pageCourseInServicer.graphql'
import pageCoursePackageInServicer from './queries/pageCoursePackageInServicer.graphql'
import pageCourseSalesStatistics from './queries/pageCourseSalesStatistics.graphql'
import pageCourseSortRuleConfigInServicer from './queries/pageCourseSortRuleConfigInServicer.graphql'
import pageCourseV2InServicer from './queries/pageCourseV2InServicer.graphql'
import pageCourserPackageSyncSchemeInServicer from './queries/pageCourserPackageSyncSchemeInServicer.graphql'
import pageCoursewareByStudentNoInSubProject from './queries/pageCoursewareByStudentNoInSubProject.graphql'
import pageCoursewareCategoryInServicer from './queries/pageCoursewareCategoryInServicer.graphql'
import pageCoursewareInServicer from './queries/pageCoursewareInServicer.graphql'
import pageCoursewareLearningRecordInServicer from './queries/pageCoursewareLearningRecordInServicer.graphql'
import pageCoursewareLearningRecordInSubProject from './queries/pageCoursewareLearningRecordInSubProject.graphql'
import pageCoursewareSupplierInSubProject from './queries/pageCoursewareSupplierInSubProject.graphql'
import pageExcellentCourseInServicer from './queries/pageExcellentCourseInServicer.graphql'
import pageLearningExperienceInServicer from './queries/pageLearningExperienceInServicer.graphql'
import pageLearningExperienceParticipatedInServicer from './queries/pageLearningExperienceParticipatedInServicer.graphql'
import pageLearningLogsInDistributor from './queries/pageLearningLogsInDistributor.graphql'
import pageLearningLogsInServicer from './queries/pageLearningLogsInServicer.graphql'
import pagePlanItemAttendanceInServicer from './queries/pagePlanItemAttendanceInServicer.graphql'
import pageStudentCourseAppraiseInCourseSupplier from './queries/pageStudentCourseAppraiseInCourseSupplier.graphql'
import pageStudentCourseAppraiseInServicer from './queries/pageStudentCourseAppraiseInServicer.graphql'
import pageStudentCourseInServicer from './queries/pageStudentCourseInServicer.graphql'
import pageStudentCourseInSubProject from './queries/pageStudentCourseInSubProject.graphql'
import pageStudentLearningRule from './queries/pageStudentLearningRule.graphql'
import statisticCourserPackageSyncSchemeInServicer from './queries/statisticCourserPackageSyncSchemeInServicer.graphql'
import statisticsCourseInSchemeInMyself from './queries/statisticsCourseInSchemeInMyself.graphql'
import statisticsCourseInSchemeInServicer from './queries/statisticsCourseInSchemeInServicer.graphql'
import statisticsLearningExperienceParticipatedBySchemeIdInServicer from './queries/statisticsLearningExperienceParticipatedBySchemeIdInServicer.graphql'
import createCourseSortRuleConfigInServicer from './mutates/createCourseSortRuleConfigInServicer.graphql'
import updateCourseSortRuleConfigInServicer from './mutates/updateCourseSortRuleConfigInServicer.graphql'

export {
  clearStudentLearningRule,
  countCourseInSubProject,
  countCoursewareInSubProject,
  countPeriodCountOfCourseTrainingOutlineInSchemeInServicer,
  getCourseCategoryInServicer,
  getCourseCountBySchemeIdInServicer,
  getCourseInServicer,
  getCoursePackageInServicer,
  getCourseSortRuleConfigDetailInServicer,
  getCourseTrainingOutlineInServicer,
  getCoursewareCategoryInServicer,
  getCoursewareInServicer,
  getLearningExperienceContentInServicer,
  getRepeatedCoursesInSpecifiedCoursePackageInServicer,
  getStudentCourseAppraiseStarStatisticsInCourseSupplier,
  getStudentCourseAppraiseStarStatisticsInServicer,
  getStudentCourseLearningInServicer,
  getStudentCourseLearningInSubProject,
  getStudentLearningRule,
  isBeingUsedAsCompulsory,
  listCourseCategoryInServicer,
  listCourseInSchemeInServicer,
  listLearningExperienceLatestInServicer,
  listLearningExperienceTopic,
  listTeacherInServicer,
  pageChooseCourseStatistics,
  pageChooseCourseStatisticsInCourseSupplier,
  pageChooseCourseStatisticsInServicer,
  pageChooseCourseStatisticsMyself,
  pageCourseDistributionResponse,
  pageCourseInCourseSupplier,
  pageCourseInPackageInServicer,
  pageCourseInPackageV2InServicer,
  pageCourseInSchemeInServicer,
  pageCourseInServicer,
  pageCoursePackageInServicer,
  pageCourseSalesStatistics,
  pageCourseSortRuleConfigInServicer,
  pageCourseV2InServicer,
  pageCourserPackageSyncSchemeInServicer,
  pageCoursewareByStudentNoInSubProject,
  pageCoursewareCategoryInServicer,
  pageCoursewareInServicer,
  pageCoursewareLearningRecordInServicer,
  pageCoursewareLearningRecordInSubProject,
  pageCoursewareSupplierInSubProject,
  pageExcellentCourseInServicer,
  pageLearningExperienceInServicer,
  pageLearningExperienceParticipatedInServicer,
  pageLearningLogsInDistributor,
  pageLearningLogsInServicer,
  pagePlanItemAttendanceInServicer,
  pageStudentCourseAppraiseInCourseSupplier,
  pageStudentCourseAppraiseInServicer,
  pageStudentCourseInServicer,
  pageStudentCourseInSubProject,
  pageStudentLearningRule,
  statisticCourserPackageSyncSchemeInServicer,
  statisticsCourseInSchemeInMyself,
  statisticsCourseInSchemeInServicer,
  statisticsLearningExperienceParticipatedBySchemeIdInServicer,
  createCourseSortRuleConfigInServicer,
  updateCourseSortRuleConfigInServicer
}
