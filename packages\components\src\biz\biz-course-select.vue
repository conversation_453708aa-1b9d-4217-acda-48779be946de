<template>
  <div class="drawer-bd">
    <div>
      <el-row :gutter="16">
        <el-form :inline="true" label-width="auto" @keyup.enter.native="doSearch">
          <el-col :md="10" :xl="6">
            <el-form-item label="课程名称">
              <el-input clearable placeholder="请输入课程名称" v-model="pageQueryParam.name" />
            </el-form-item>
          </el-col>
          <el-col :md="10" :xl="6">
            <el-form-item label="课程分类">
              <biz-course-category v-model="pageQueryParam.categoryIdList"></biz-course-category>
            </el-form-item>
          </el-col>
          <el-col :md="4" :xl="4" class="f-fr">
            <el-form-item class="f-tr">
              <el-button type="primary" @click="doSearch">查询</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <el-table :data="pageList" v-loading="query.loading" size="mini">
        <el-table-column type="index" label="No." width="50" align="center" header-align="center"></el-table-column>
        <el-table-column prop="name" label="课程名称">
          <template slot-scope="{ row }">
            <div class="is-cache" v-if="idMap[row.id].isCache"></div>
            {{ row.name }}
          </template>
        </el-table-column>
        <el-table-column prop="period" label="学时" width="100"></el-table-column>
        <el-table-column label="操作" width="100" align="center" header-align="center">
          <template slot-scope="{ row }">
            <el-button type="text" @click="select(row)" size="mini">
              {{ idMap[row.id].item ? '取消选择' : '选择' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </div>

    <div class="select-course-foot">
      <el-button @click="closeDrawer">取 消</el-button>
      <el-badge :value="getCachesKey().length">
        <el-button class="f-ml10" type="primary" @click="chooseCertain">确 定</el-button>
      </el-badge>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .select-course-foot {
    text-align: center;
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 10px;
  }

  .is-cache {
    width: 8px;
    background: #e89820;
    height: 8px;
    border-radius: 50%;
    display: inline-flex;
    align-items: flex-start;
    justify-content: flex-start;
    margin-right: 5px;
  }
</style>

<script lang="ts">
  import { Component, Emit, Inject, Prop, Vue, Watch } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import QueryCourseListParam from '@api/service/management/resource/course//query/vo/QueryCourseListParam'
  import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'
  import BizCourseCategory from '@hbfe/jxjy-admin-components/src/biz/biz-course-category.vue'

  interface Selected {
    [key: string]: {
      isCache: boolean
      item: CourseListDetail
    }
  }

  @Component({
    components: { BizCourseCategory }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    @Inject({
      from: 'validateChooseCourse',
      default() {
        return () => {
          return true
        }
      }
    })
    validateChooseCourse: (courses: Array<CourseListDetail>) => {}

    page: UiPage

    query: Query = new Query()

    pageQueryParam: QueryCourseListParam = new QueryCourseListParam()

    pageList: Array<CourseListDetail> = new Array<CourseListDetail>()

    @Prop({
      type: String,
      default: '请选择相关课程'
    })
    placeholder: string
    @Prop({
      type: String,
      default: ''
    })
    categoryId: string

    isShow = false
    showClear = false
    loading = false
    idMap: Selected = {}

    // 显示
    async created() {
      this.isShow = true
      await this.doSearch()
    }

    // 选中的时候
    @Emit('update:value')
    select(course: CourseListDetail) {
      this.showClear = true
      if (this.idMap[course.id].item) {
        this.idMap[course.id] = {
          isCache: false,
          item: undefined
        }
      } else {
        this.idMap[course.id] = {
          isCache: true,
          item: course
        }
      }
      return course.id
    }

    @Prop({
      type: Array,
      default: []
    })
    selected: Array<string>

    @Emit('cancel')
    closeDrawer() {
      this.getCachesKey().forEach((key: string) => {
        this.idMap[key].item = undefined
        this.idMap[key].isCache = false
      })
      this.isShow = false
    }

    getCachesKey() {
      const caches = new Set()

      Object.keys(this.idMap).forEach((key: string) => {
        const selected = this.idMap[key]
        if (selected.isCache) {
          caches.add(key)
        }
      })

      return Array.from(caches.values())
    }

    // 清除
    clear() {
      this.isShow = false
    }

    @Watch('selected')
    selectedChange() {
      this.pageList.forEach((item) => {
        const hasInclude = this.selected.find((id) => id === item.id)
        const setItem = hasInclude ? item : undefined
        if (this.idMap[item.id]) {
          if (!this.idMap[item.id].item) {
            this.$set(this.idMap, item.id, {
              isCache: false,
              item: setItem
            })
          }
        } else {
          this.$set(this.idMap, item.id, {
            isCache: false,
            item: setItem
          })
        }
      })
    }

    async doSearch() {
      this.query.loading = true
      try {
        this.pageList = await ResourceModule.courseFactory.queryCourse.queryCoursePage(this.page, this.pageQueryParam)
        this.selectedChange()
      } catch (e) {
        // nothing
      } finally {
        this.query.loading = false
      }
    }

    chooseCertain() {
      const newest = {}
      Object.keys(this.idMap).forEach((id) => {
        if (!this.selected.includes(id)) {
          newest[id] = this.idMap[id]
        }
      })
      const selected = Object.keys(newest)
        .map((id: string) => {
          return this.idMap[id].item
        })
        .filter((item) => !!item)

      if (this.validateChooseCourse(selected)) {
        this.$emit('confirm', selected)
        this.closeDrawer()
      }
    }
  }
</script>
