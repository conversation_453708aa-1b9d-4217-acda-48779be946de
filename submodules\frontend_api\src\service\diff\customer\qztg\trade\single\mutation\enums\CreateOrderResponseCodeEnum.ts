export enum CreateOrderResponseCodeEnum {
  /**
   * 成功
   */
  success = '200',
  /**
   * 商品不存在
   */
  productNotFound = '30001',

  /**
   * 商品已下架
   */
  productOffShelf = '30002',

  /**
   * 不支持当前渠道购买
   */
  unsupportedPurchaseChannel = '30003',

  /**
   * 当前渠道已关闭
   */
  purchaseChannelClosed = '30004',

  /**
   * 商品资源不可用
   */
  productResourceUnavailable = '30005',

  /**
   * 不开放学员报名
   */
  productRegistrationClosed = '30007',

  /**
   * 不可重复报名同一个班级
   */
  duplicateRegistration = '50001',

  /**
   * 培训未开始
   */
  trainingNotStarted = '50002',

  /**
   * 培训已结束
   */
  trainingEnded = '50003',

  /**
   * 报名未开始
   */
  registrationNotStarted = '50004',

  /**
   * 报名已结束
   */
  registrationEnded = '50005',

  /**
   * 学习方案不存在（属于报名业务子模块）
   */
  studyPlanNotFound = '50008',

  /**
   * 用户已经预约过该学习方案
   */
  studyPlanAlreadyBooked = '60003',

  /**
   * 存在未支付的集体报名订单
   */
  unpaidGroupOrderExists = '52009',

  /**
   * 存在未支付的订单
   */
  unpaidOrderExists = '52001'
}
