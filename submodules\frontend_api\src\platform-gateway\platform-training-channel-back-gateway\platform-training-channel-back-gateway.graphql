schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""根据专题Id查找专题线下集体报名配置"""
	getOfflineCollectiveByTrainingChannelIdInSubject(trainingChannelId:String!):TrainingChannelOfflineCollectiveSignUpSettingResponse @optionalLogin
	"""根据专题Id查找专题线上集体报名配置"""
	getOnlineCollectiveByTrainingChannelIdInSubject(trainingChannelId:String!):TrainingChannelOnlineCollectiveSignUpSettingResponse @optionalLogin
	"""子项目-根据专题id查询专题详情"""
	getTrainingChannelDetailById(id:String):TrainingChannelDetailResponse
	"""管理员-查询专题管理员下的专题数量"""
	listTrainingChannelCount(trainingChannelCountRequest:TrainingChannelCountRequest):[TrainingChannelCountResponse]
	"""功能描述 : 专题管理员列表"""
	pageOnlineSchoolAdminInfoInServicer(page:Page,request:ContractProviderAdminQueryRequest):ContractProviderAdminInfoResponsePage @page(for:"ContractProviderAdminInfoResponse")
	"""管理员-分页查询专题信息"""
	pageTrainingChannelInfo(page:Page,request:TrainingChannelRequest):TrainingChannelPageResponsePage @page(for:"TrainingChannelPageResponse")
	"""专题管理员 - 专题分页"""
	pageTrainingChannelInfoInTrainingChannelAdmin(page:Page,request:TrainingChannelRequest):TrainingChannelPageResponsePage @page(for:"TrainingChannelPageResponse")
	"""分页查询精品课程"""
	pageTrainingChannelSelectCourseInSubject(page:Page,request:TrainingChannelSelectCourseRequest):TrainingChannelSelectCourseResponsePage @page(for:"TrainingChannelSelectCourseResponse") @optionalLogin
	"""专题管理员 - 咨询分页"""
	pageTrainingChannelSimpleNewsInTrainingChannelAdmin(page:Page,queryRequest:TrainingChannelNewsSimpleQueryRequest):NewsSimpleResponsePage @page(for:"NewsSimpleResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input ContractProviderInfoAdminOwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.admin.nested.ContractProviderInfoAdminOwnerRequest") {
	servicerIdList:[String]
}
input TrainingChannelSortKParam @type(value:"com.fjhb.ms.basicdata.query.kernel.service.param.account.nested.TrainingChannelSortKParam") {
	sortField:TrainingChannelEnum
	sortType:SortTypeEnum
}
input DateScopeRequest @type(value:"com.fjhb.ms.basicdata.repository.DateScopeRequest") {
	beginTime:DateTime
	endTime:DateTime
}
"""精品课程查询请求参数"""
input TrainingChannelSelectCourseRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.TrainingChannelSelectCourseRequest") {
	"""专题ID"""
	trainingChannelId:String
	"""课程分类ID"""
	selectedCourseCategoryId:String
	"""课程id集合"""
	courseIds:[String]
}
input ContractProviderAdminQueryRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.contractProviderAdmin.ContractProviderAdminQueryRequest") {
	"""合同服务商管理员归属信息"""
	owner:ContractProviderInfoAdminOwnerRequest
	"""专题名称"""
	trainingChannelName:String
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:AdminUserRequest
	"""登录认证信息"""
	authentication:AuthenticationRequest
	"""角色信息查询"""
	role:RoleRequest
	"""排序"""
	sortList:[AdminSortRequest]
}
"""功能描述：账户信息查询条件
	@Author： wtl
	@Date： 2022年5月11日 15:30:56
"""
input AccountRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.contractProviderAdmin.nested.AccountRequest") {
	"""账户状态 1：正常，2：冻结
		@see AccountStatus
	"""
	statusList:[Int]
	"""创建时间范围"""
	createTimeScope:DateScopeRequest
	"""创建人用户id"""
	createdUserId:String
	"""账户类型 1：企业帐户，2：企业个人帐户，3：个人帐户"""
	accountTypeList:[Int]
	"""单位id （原始单位id，不会随着id 人员与单位的关系变化而变化）"""
	unitIdList:[String]
	"""单位id匹配方式 默认-1、and匹配 2、or匹配
		@see MatchTypeConstant
	"""
	unitIdMatchType:Int
	"""来源类型
		0-内置 1-项目主网站 2-安卓 3-IOS 4-后台导入 5-迁移数据
	"""
	sourceTypes:[Int]
}
"""功能描述：管理员排序
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
input AdminSortRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.contractProviderAdmin.nested.AdminSortRequest") {
	"""管理员排序字段"""
	sortField:EnterprisePersonSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
"""功能描述：管理员查询条件
	@Author： wtl
	@Date： 2022年1月25日 15:24:10
"""
input AdminUserRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.contractProviderAdmin.nested.AdminUserRequest") {
	"""默认为右模糊(0：完全匹配 1：模糊查询，*manageRegionPath* 2：左模糊查询，*manageRegionPath 3:右模糊查询，manageRegionPath*)
		管辖地区路径匹配方式
	"""
	manageRegionPathType:Int
	"""管理地区路径集合"""
	manageRegionPathList:[String]
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	phoneMatchType:Int
	"""工作单位名称"""
	companyName:String
	"""工作单位统一社会信用代码"""
	companyCode:String
}
"""功能描述：登录认证查询条件
	@Author： wtl
	@Date： 2022年1月26日 09:30:12
"""
input AuthenticationRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.contractProviderAdmin.nested.AuthenticationRequest") {
	"""帐号"""
	identity:String
	"""用户名"""
	userName:String
}
"""功能描述：角色查询条件
	@Author： wtl
	@Date： 2022年5月11日 11:46:41
"""
input RoleRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.contractProviderAdmin.nested.RoleRequest") {
	"""角色id集合"""
	roleIdList:[String]
	"""角色类型
		（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
		@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
	"""
	roleTypeList:[String]
	"""角色类别
		（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管 10:人社超管
		11:人社职建处 12:人社就业局 13:超级管理员 14:合同供应商 15:专家 16:电子劳动合同业务角色 320:地区管理员 410:人社行业管理员
		411:人社行业省级管理员 412:人社行业市级管理员 413:人社行业区县级管理员 420:人社地区管理员 430:人社业务管理员 440:省级人社主管 450:市级人社主管
		460:区县级人社主管 510:培训机构管理员 520:技工院校管理员 530:职业院校管理员 540:政策参与者 550:线上培训机构管理员 560:课件供应商
		5001:学徒制_企业管理员 5101:学徒制_培训机构管理员 5201:学徒制_技工院校管理员 5301:学徒制_职业院校管理员 4011:学徒制_人社_省级管理员
		4021:学徒制_人社_市级管理员 4031:学徒制_人社_区/县级管理员 6001:揭榜挂帅_企业管理员 6101:揭榜挂帅_培训机构管理员
		6201:揭榜挂帅_技工院校管理员 6301:揭榜挂帅_职业院校管理员 6401:揭榜挂帅_人社_省级管理员 6402:揭榜挂帅_人社_市级管理员 6403:揭榜挂帅_人社_区/县级管理员）
		@see RoleCategories
	"""
	roleCategoryList:[Int]
	"""排除的角色类别集合
		（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管 10:人社超管
		11:人社职建处 12:人社就业局 13:超级管理员 14:合同供应商 15:专家 16:电子劳动合同业务角色 320:地区管理员 410:人社行业管理员
		411:人社行业省级管理员 412:人社行业市级管理员 413:人社行业区县级管理员 420:人社地区管理员 430:人社业务管理员 440:省级人社主管 450:市级人社主管
		460:区县级人社主管 510:培训机构管理员 520:技工院校管理员 530:职业院校管理员 540:政策参与者 550:线上培训机构管理员 560:课件供应商
		5001:学徒制_企业管理员 5101:学徒制_培训机构管理员 5201:学徒制_技工院校管理员 5301:学徒制_职业院校管理员 4011:学徒制_人社_省级管理员
		4021:学徒制_人社_市级管理员 4031:学徒制_人社_区/县级管理员 6001:揭榜挂帅_企业管理员 6101:揭榜挂帅_培训机构管理员
		6201:揭榜挂帅_技工院校管理员 6301:揭榜挂帅_职业院校管理员 6401:揭榜挂帅_人社_省级管理员 6402:揭榜挂帅_人社_市级管理员 6403:揭榜挂帅_人社_区/县级管理员）
		@see RoleCategories
	"""
	excludeRoleCategoryList:[Int]
	"""角色应用方Id集合"""
	applicationMemberIdList:[String]
	"""授予性质 | 1.系统授予 2.用户授予"""
	natureList:[Int]
	"""排除的角色Code | JGGLY-机构管理员"""
	excludeRoleCodeList:[String]
}
"""简略资讯查询条件"""
input TrainingChannelNewsSimpleQueryRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingChannelAdmin.TrainingChannelNewsSimpleQueryRequest") {
	"""资讯标题"""
	title:String
	"""资讯分类编号"""
	necId:String
	"""发布地区编码"""
	areaCodePath:String
	"""弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]"""
	popUpsStatus:Int!
	"""资讯状态 [-1-全部，0-草稿，1-发布 默认全部]"""
	status:Int!
	"""专题ID"""
	specialSubjectIds:[String]
}
input TrainingChannelCountRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.TrainingChannelCountRequest") {
	"""专题管理员用户id"""
	userIds:[String]
}
input TrainingChannelRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchannel.TrainingChannelRequest") {
	"""专题ID集合"""
	ids:[String]
	"""专题名称"""
	name:String
	"""专题入口名称"""
	entryName:String
	"""专题类型：
		1-地区  2-行业  3-班级
	"""
	types:[Int]
	"""行业id"""
	industryId:String
	"""地区路径"""
	regionPath:String
	"""启用状态（0：停用 1：启用）"""
	enable:Boolean
	"""是否显示在网校"""
	showOnNetSchool:Boolean
	"""排序"""
	sort:Int
	"""编辑时间范围"""
	createdDateScope:DateScopeRequest
	"""排序"""
	sortList:[TrainingChannelSortKParam]
	"""专题管理员用户id"""
	userIdList:[String]
	"""单位名称"""
	unitName:String
}
enum SortTypeEnum @type(value:"com.fjhb.ms.basicdata.enums.SortTypeEnum") {
	ASC
	DESC
}
type RegionModel @type(value:"com.fjhb.ms.basicdata.model.RegionModel") {
	regionId:String
	regionPath:String
	provinceId:String
	provinceName:String
	cityId:String
	cityName:String
	countyId:String
	countyName:String
}
enum EnterprisePersonSortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.EnterprisePersonSortFieldEnum") {
	createdTime
	accountType
	userNameFirstLetter
	nature
}
enum TrainingChannelEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.TrainingChannelEnum") {
	publishedTime
}
"""@Description 附件请求
	<AUTHOR>
	@Date 2024/3/20 9:56
"""
type Attachment @type(value:"com.fjhb.platform.jxjy.v1.api.trainingchannel.event.trainingchannelonlinecollective.Attachment") {
	"""附件名称"""
	name:String
	"""附件地址"""
	url:String
}
"""专题线下集体报名配置返回值"""
type TrainingChannelOfflineCollectiveSignUpSettingResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.TrainingChannelOfflineCollectiveSignUpSettingResponse") {
	"""专题线下集体报名配置信息"""
	id:String
	"""专题线下集体报名入口开关"""
	openEntrySwitch:Boolean!
	"""专题线下集体报名入口图片附件"""
	entryPictureAttachments:[Attachment]
	"""线下集体报名名称"""
	name:String
	"""专题线下集体报名模板地址"""
	templateAttachment:Attachment
	"""访问链接"""
	accessUrl:String
	"""底部文本说明内容id"""
	bottomDescriptionId:String
	"""底部文本说明内容"""
	bottomDescription:String
	"""报名步骤信息"""
	signUpSteps:[SignUpStepDto]
	"""创建时间"""
	createdTime:DateTime
}
"""专题线上集体报名配置返回值"""
type TrainingChannelOnlineCollectiveSignUpSettingResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.TrainingChannelOnlineCollectiveSignUpSettingResponse") {
	"""专题线上集体报名配置信息ID"""
	id:String
	"""专题线上集体报名入口开关"""
	openEntrySwitch:Boolean!
	"""专题线上集体报名入口图片开关"""
	openEntryPictureSwitch:Boolean!
	"""专题线上集体报名入口图片附件"""
	entryPictureAttachments:[Attachment]
	"""专题线上集体报名模版地址 json字符串"""
	templateAttachment:Attachment
	"""展示报名班级链接地址"""
	showSignUpClassUrl:String
	"""创建时间"""
	createdTime:DateTime
}
"""精品课程返回值"""
type TrainingChannelSelectCourseResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.TrainingChannelSelectCourseResponse") {
	"""专题精品课程ID"""
	id:String
	"""专题ID"""
	trainingChannelId:String
	"""课程ID"""
	courseId:String
	"""精品课程分类ID"""
	courseCategoryId:String
	"""排序"""
	sort:Int!
	"""创建时间"""
	createdTime:DateTime
	"""创建人ID"""
	createdUserId:String
	"""修改时间"""
	updatedTime:DateTime
	"""课程名称"""
	courseName:String
	"""课程分类名称"""
	categoryName:String
}
type ContractProviderAdminInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.contractProviderAdmin.ContractProviderAdminInfoResponse") {
	"""账户信息"""
	accountInfo:AccountResponse
	"""管理员用户信息"""
	userInfo:AdminUserInfoResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
	"""角色信息集合"""
	roleList:[RoleResponse]
}
"""功能描述：账户信息"""
type AccountResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.contractProviderAdmin.nested.AccountResponse") {
	"""账户id"""
	accountId:String
	"""帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
		@see AccountTypes
	"""
	accountType:Int
	"""单位信息"""
	unitInfo:UnitInfoResponse
	"""所属顶级企业帐户Id"""
	rootAccountId:String
	"""帐户状态 1：正常，2：冻结，3：注销
		@see AccountStatus
	"""
	status:Int
	"""注册方式
		0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
		@see AccountRegisterTypes
	"""
	registerType:Int
	"""来源类型
		0、内置，1、项目主网站，2、安卓，3、IOS，4、后台导入，5、迁移数据，6、分销平台项目主网站，7、专题，8、华医网，9、江西管理平台
		@see AccountSourceTypes
	"""
	sourceType:Int
	"""创建时间"""
	createdTime:DateTime
}
"""功能描述：管理员用户信息"""
type AdminUserInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.contractProviderAdmin.nested.AdminUserInfoResponse") {
	"""管辖地区集合"""
	manageRegionList:[RegionModel]
	"""办公室（所在处/科室）"""
	office:String
	"""头像"""
	photo:String
	"""用户id"""
	userId:String
	"""用户名称"""
	userName:String
	"""性别
		-1未知，0女，1男
		@see Genders
	"""
	gender:Int
	"""证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）"""
	idCardType:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""邮箱"""
	email:String
	"""工作单位名称"""
	companyName:String
	"""工作单位统一社会信用代码"""
	companyCode:String
}
"""功能描述：帐户认证信息
	@Author： wtl
	@Date： 2022年5月11日 14:23:18
"""
type AuthenticationResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.contractProviderAdmin.nested.AuthenticationResponse") {
	"""帐号"""
	identity:String
	"""认证标识类型
		1用户名,2手机,3身份证,4邮箱,5第三方OpenId
	"""
	identityType:Int
	"""认证方式状态 1启用，2禁用
		@see AuthenticationStatusEnum
	"""
	status:Int
}
"""功能描述：角色信息
	@Author： wtl
	@Date： 2022/1/24 20:17
"""
type RoleResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.contractProviderAdmin.nested.RoleResponse") {
	"""角色id"""
	roleId:String
	"""角色名称"""
	roleName:String
	"""角色类型
		（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
		@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
	"""
	roleType:String
	"""角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
		@see RoleCategories
	"""
	roleCategory:Int
	"""应用方类型(4:子项目 5：单位 6：服务商)
		@see SystemMemberTypes
	"""
	applicationMemberType:Int
	"""是否冻结，1代表该账户的角色被冻结，其他情况均为未冻结
		@see com.fjhb.ms.basicdata.constants.AccountRoleFrozeStatusConstants
	"""
	frozeStatus:Int
	"""应用方ID"""
	applicationMemberId:String
	"""角色说明"""
	description:String
}
"""单位信息模型"""
type UnitInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.contractProviderAdmin.nested.UnitInfoResponse") {
	"""单位ID"""
	unitId:String
}
"""附件返回值"""
type AttachmentResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.nested.AttachmentResponse") {
	"""附件名称"""
	name:String
	"""附件地址"""
	url:String
}
"""简略资讯信息"""
type NewsSimpleResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingChannelAdmin.NewsSimpleResponse") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""是否置顶"""
	isTop:Boolean!
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""摘要"""
	summary:String
	"""分类名称"""
	name:String
	"""分类id"""
	categoryId:String
	"""父级id"""
	parentCategoryId:String
	"""资讯状态 0 草稿 1正常"""
	status:Int!
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""发布地区编码"""
	areaCodePath:String
	"""专题Id"""
	specialSubjectId:String
}
type IndustryModel @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.IndustryModel") {
	"""行业id"""
	industryId:String
	"""行业名称"""
	industryName:String
}
type NetSchoolResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.NetSchoolResponse") {
	"""门户类型（1：web端 2：移动端）"""
	portalType:Int
	"""网校域名"""
	netSchoolDomainName:String
}
type TrainingChannelCountResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.TrainingChannelCountResponse") {
	"""用户id (专题管理员用户id)"""
	userId:String
	"""专题数量"""
	trainingChannelCount:Long
}
type TrainingChannelDetailResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.TrainingChannelDetailResponse") {
	"""专题id"""
	id:String
	"""网校id"""
	servicerId:String
	"""网校名称"""
	netSchoolName:String
	"""网校域名"""
	netSchoolDoMain:[NetSchoolResponse]
	"""专题入口名称"""
	entryName:String
	"""专题名称"""
	name:String
	"""域名类型（1：系统默认域名 2：自定义）"""
	domainNameType:Int
	"""专题域名名称"""
	domainName:String
	"""专题类型：
		1-地区  2-行业  3-班级
	"""
	types:[Int]
	"""是否显示在网校"""
	showOnNetSchool:Boolean
	"""PC端专题模板编号"""
	pcTemplateNo:String
	"""H5端专题模板编号"""
	h5TemplateNo:String
	"""启用状态（0：停用 1：启用）"""
	enable:Boolean
	"""状态（1：草稿 2：正常）"""
	status:Int
	"""排序号码"""
	sort:Int
	"""适用地区"""
	regions:[RegionModel]
	"""适用行业"""
	industrys:[String]
	"""专题门户"""
	topic:TrainingChannelTopicResponse
	"""已配置方案数"""
	configuredPlans:Long!
	"""单位名称"""
	unitName:String
	"""是否允许访问"""
	allowAccess:Boolean
}
type TrainingChannelPageResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.TrainingChannelPageResponse") {
	"""专题id"""
	id:String
	"""专题名称"""
	name:String
	"""入口名称"""
	entryName:String
	"""专题类型：
		1-地区  2-行业  3-班级
	"""
	types:[Int]
	"""是否展示在网校"""
	showOnNetSchool:Boolean
	"""是否允许访问"""
	allowAccess:Boolean
	"""网校id"""
	netSchoolId:String
	"""网校名称"""
	netSchoolName:String
	"""网校域名"""
	netSchoolDomainName:[NetSchoolResponse]
	"""专题域名"""
	domainName:String
	"""PC端专题模板编号"""
	pcTemplateNo:String
	"""H5端专题模板编号"""
	h5TemplateNo:String
	"""已配置方案数"""
	configuredPlans:Long!
	"""启用状态（0：停用 1：启用）"""
	enable:Boolean
	"""排序"""
	sort:Int
	"""适用地区"""
	regions:[RegionModel]
	"""适用行业"""
	industrys:[IndustryModel]
	"""最后编辑时间"""
	updatedTime:String
	"""单位名称"""
	unitName:String
}
type TrainingChannelTopicPhotoResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.TrainingChannelTopicPhotoResponse") {
	"""专题门户轮播图id"""
	id:String
	"""专题门户轮播图类型：1 web端、2 H5端"""
	type:Int
	"""专题门户轮播图地址"""
	pictureUrl:String
	"""链接地址"""
	linkUrl:String
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:String
}
type TrainingChannelTopicResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.TrainingChannelTopicResponse") {
	"""专题门户id"""
	id:String
	"""logo类型（1：文字、2：图片）"""
	logoType:Int
	"""专题门户logo名称（专题门户logo类型为文字时，有值）"""
	logoName:String
	"""专题门户logo图片地址（专题门户logo类型为图片时，有值）"""
	logoPictureUrl:String
	"""客服电话类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
		com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.CustomerServicePhoneTypes
	"""
	customerServicePhoneType:Int
	"""客服电话"""
	customerServicePhone:String
	"""客服电话图片路径"""
	customerServicePhonePictureUrl:String
	"""培训流程类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
		@see com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.TrainingProcessTypes
	"""
	trainingProcessType:Int
	"""培训流程图片
		@see com.fjhb.platform.jxjy.v1.api.trainingchannel.event.trainingchannelonlinecollective.Attachment
	"""
	trainingProcessAttachments:[AttachmentResponse]
	"""企业微信客服类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
	"""
	enterpriseWechatCustomerType:Int
	"""企业微信客服图片"""
	enterpriseWechatCustomerAttachments:[AttachmentResponse]
	"""咨询时间"""
	seekTime:String
	"""专题门户底部落款类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
		com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.PortalBottomShowTypes
	"""
	bottomShowType:Int
	"""专题门户底部落款内容（专题门户底部落款类型为自定义时 有值）"""
	bottomShowContent:String
	"""轮播图集合"""
	photos:[TrainingChannelTopicPhotoResponse]
}
type SignUpStepDto @type(value:"com.fjhb.platform.jxjy.v1.kernel.service.impl.dto.SignUpStepDto") {
	"""步骤内容"""
	content:String
	"""序号"""
	index:Int!
	"""步骤标题"""
	title:String
	"""步骤内容 Id"""
	contentId:String
}

scalar List
type ContractProviderAdminInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ContractProviderAdminInfoResponse]}
type TrainingChannelPageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TrainingChannelPageResponse]}
type TrainingChannelSelectCourseResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TrainingChannelSelectCourseResponse]}
type NewsSimpleResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [NewsSimpleResponse]}
