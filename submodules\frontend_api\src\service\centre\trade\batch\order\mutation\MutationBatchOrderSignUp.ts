import Collectivesign, {
  CollectiveSignImportInfoForVerifyRequest,
  CollectiveSignImportInfoRequest,
  CollectiveSignQueryRequest,
  FindCountGroupByKeyRequest,
  ImportCollectiveSignupResponse,
  MetaProperty,
  RunTimeProperty,
  SignupDataAnalysisResponse,
  UpdateSignupDataRequest
} from '@api/ms-gateway/ms-collectivesign-v1'
import PCollectivesign from '@api/platform-gateway/jxjy-collectivesign-v1'
import { Page, ResponseStatus } from '@hbfe/common'

import MsOrder from '@api/ms-gateway/ms-order-v1'
import msTradeQuery, {
  CommoditySkuForestageResponse,
  CommoditySkuForestageResponsePage,
  CommoditySkuRequest,
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  SchemeRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import DataExportForestage from '@api/platform-gateway/jxjy-data-export-gateway-forestage'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import { Response } from '@hbfe/common'
export interface TabItem {
  [key: string]: string | number
}
export class QueryListRequest {
  /**
   * 姓名
   */
  userInfo_name: string
  /**
   * 证件号
   */
  userInfo_idCard: string
  /**
   * 技术等级
   */
  TECHNICAL_GRADE: string
  /**
   * 年度
   */
  signUp_year: string
  /**
   * 技术等级ID
   */
  technology_ID: string
  /**
   * 技术等级
   */
  technology: string
  /**
   * 地区Code
   */
  region_code: string
  /**
   * 地区
   */
  region: string
  /**
   * 学时
   */
  classHourse: string
  /**
   * 价格
   */
  price: string
  /**
   * 期别名称
   */
  signUp_issueName: string
  /**
   * 是否住宿
   */
  signUp_needAccommodation: string
  /**
   * 住宿方式
   */
  signUp_accommodationType: string
}
class MutationBatchOrderSignUp {
  collectiveSignupNo = '' // 集体报名编号
  trainInfo = new Array<CommoditySkuForestageResponse>()
  /**
   * 批量上传报名列表  //初次需要先调用这个口
   * @returns 集体报名编号
   */
  async importCollectiveSignUp(collectiveSignImportInfoRequest: CollectiveSignImportInfoRequest): Promise<string> {
    collectiveSignImportInfoRequest.terminalCode = collectiveSignImportInfoRequest.terminalCode
      ? collectiveSignImportInfoRequest.terminalCode
      : 'Web'
    const response = await Collectivesign.importCollectiveSignup(collectiveSignImportInfoRequest)
    this.collectiveSignupNo = response.data
    return response.data
  }
  /**
   * 用于导入模板
   * @returns 集体报名编号
   */
  async importCollectiveSignupForVerify(
    collectiveSignImportInfoRequest: CollectiveSignImportInfoForVerifyRequest
  ): Promise<ImportCollectiveSignupResponse> {
    collectiveSignImportInfoRequest.terminalCode = collectiveSignImportInfoRequest.terminalCode
      ? collectiveSignImportInfoRequest.terminalCode
      : 'Web'
    const response = await Collectivesign.importCollectiveSignupForVerify(collectiveSignImportInfoRequest)
    this.collectiveSignupNo = response.data.batchOrderNo
    return response.data
  }

  /**
   * 查询批量上传报名列表的进度
   * @param collectiveSignupNo  集体报名编号
   * @returns
   */
  async queryImportProcess(): Promise<SignupDataAnalysisResponse> {
    const response = await Collectivesign.collectiveSignupDataAnalysis(this.collectiveSignupNo)
    return response.data
  }
  /**
   * 查询批量上传主任务进度--- 处理如果数量超过1000，后端会停止解析
   * 返回值是的result如果为FAILURE 那么停止queryImportProcess的轮询并提示异常信息为返回值中的message
   * @param collectiveSignupNo  集体报名编号
   * @returns
   */
  async findLastRecordByBatch() {
    const response = await Collectivesign.findLastRecordByBatch(this.collectiveSignupNo)
    return response.data
  }
  /**
   * 查询报名成功学员--总价
   * @param ids 方案ID集合
   * @returns
   */
  async querySignUpShopAllPrice(trainingChannelId?: string): Promise<number> {
    const findCountGroupByKeyRequest = new FindCountGroupByKeyRequest()
    findCountGroupByKeyRequest.collectiveSignupNo = this.collectiveSignupNo
    findCountGroupByKeyRequest.keyName = 'SCHEME_ID'
    findCountGroupByKeyRequest.type = 1
    const response = await Collectivesign.findCountGroupByKey(findCountGroupByKeyRequest)
    // TODO 按班级名称数组查询商品价格
    const list = new Array<string>()
    await response.data.forEach((className) => {
      list.push(className.key)
    })
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    // 默认按上架时间倒序
    const sortOption = new CommoditySkuSortRequest()
    sortOption.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(sortOption) as CommoditySkuSortRequest[]
    const queryRequest = new CommoditySkuRequest()
    queryRequest.schemeRequest = new SchemeRequest()
    queryRequest.schemeRequest.schemeIdList = list
    // 修复bug  JXJYPXTYPT-18163
    queryRequest.isShowAll = true
    if (queryRequest.schemeRequest.schemeIdList.length == 0) {
      return 0
    }
    let response1: Response<CommoditySkuForestageResponsePage> = null
    if (trainingChannelId) {
      queryRequest.trainingChannelIds = [trainingChannelId]
      response1 = await msTradeQuery.pageCommoditySkuTrainingChannelInServicer({
        page,
        queryRequest,
        sortRequest
      })
    } else {
      response1 = await msTradeQuery.pageCommoditySkuCustomerCollectivePurchaseInServicer({
        page,
        queryRequest,
        sortRequest
      })
    }
    this.trainInfo = response1.data.currentPageData
    let price = 0 // 总价累计

    // TODO 按班级名称数组查询商品价格
    await response.data.forEach((className) => {
      const schemePrice =
        response1.data.currentPageData.find((item) => (item.resource as any).schemeId === className.key)
          ?.commodityBasicData.price ?? 0
      price = CalculatorObj.add(price, CalculatorObj.multiply(className.count, schemePrice)) ?? 0
    })
    return price
  }
  /**
   * 查询表头数据
   * @returns
   */
  async findCollectiveSignupMetaSchema() {
    const response = await Collectivesign.findCollectiveSignupMetaSchema()
    return response.data.metaSchemas
  }
  /**
   * 删除所有失败报名数据
   * @returns
   */
  async deleteAllFailedSignUpData(): Promise<ResponseStatus> {
    const { status } = await Collectivesign.clearFailureData(this.collectiveSignupNo)
    return status
  }
  /**
   * 导出失败报名数据
   * @returns 文件路径
   */
  async exportCollectiveSignupImportFailExcel() {
    const { data } = await Collectivesign.exportCollectiveSignupImportFailExcel(this.collectiveSignupNo)
    return data
  }
  /**
   * 导出失败报名数据 --- 提交订单后
   * @returns 文件路径
   */
  async exportCollectiveSignupExcuteFailExcel() {
    const { data } = await PCollectivesign.asyncExportCollectiveSignupExcuteFailExcel(this.collectiveSignupNo)
    return data
  }
  /**
   * 导出成功报名数据 --- 提交订单后
   * @returns 文件路径
   */
  async exportBatchOrderDetailInMyself() {
    const { data } = await DataExportForestage.exportBatchOrderDetailInMyself({
      request: {
        batchOrderNoList: [this.collectiveSignupNo]
      }
    })
    return data
  }
  /**
   * 报名成功数据列表（由业务微服务提供）
   * @returns
   */
  async querySuccessSignUpData(
    page: Page,
    queryListRequest?: QueryListRequest,
    trainingChannelId?: string
  ): Promise<Array<TabItem>> {
    const metaPropertyList = new Array<MetaProperty>()
    const runTimePropertyList = Array<RunTimeProperty>()
    for (const i in queryListRequest) {
      if (Object.prototype.hasOwnProperty.call(queryListRequest, i)) {
        const element = queryListRequest[i]
        if (i === 'TECHNICAL_GRADE') {
          if (element) {
            runTimePropertyList.push({
              key: i,
              value: element
            })
          }
          continue
        }
        if (element) {
          metaPropertyList.push({
            key: i,
            value: element
          })
        }
      }
    }
    const collectiveSignQueryRequest = new CollectiveSignQueryRequest()
    collectiveSignQueryRequest.collectiveSignupNo = this.collectiveSignupNo
    collectiveSignQueryRequest.metaPropertyList = metaPropertyList
    collectiveSignQueryRequest.runTimePropertyList = runTimePropertyList
    const request = { request: collectiveSignQueryRequest, page }
    const response = await Collectivesign.findImportCollectiveSignupCompleteSuccessDataByPage(request)
    const data = response.data.currentPageData.map((item) => {
      const obj: TabItem = {}
      item.row.forEach((res) => {
        obj[res.key] = res.value
      })
      item.runtimeProperties?.forEach((res) => {
        obj[res.key] = res.value
      })
      return obj
    })
    page.totalPageSize = response.data.totalPageSize
    page.totalSize = response.data.totalSize
    const queryRequest = new CommoditySkuRequest()
    queryRequest.schemeRequest = new SchemeRequest()
    // 修复bug  JXJYPXTYPT-18163
    queryRequest.isShowAll = true
    queryRequest.schemeRequest.schemeIdList = [
      ...new Set(data.filter((item) => item.SCHEME_ID).map((item) => item.SCHEME_ID) as string[])
    ]
    if (queryRequest.schemeRequest.schemeIdList.length == 0) {
      return data
    }
    let response1 = null
    if (trainingChannelId) {
      queryRequest.trainingChannelIds = [trainingChannelId]
      response1 = await msTradeQuery.pageCommoditySkuTrainingChannelInServicer({
        page: {
          pageNo: 1,
          pageSize: 200
        },
        queryRequest
      })
    } else {
      response1 = await msTradeQuery.pageCommoditySkuCustomerCollectivePurchaseInServicer({
        page: {
          pageNo: 1,
          pageSize: 200
        },
        queryRequest
      })
    }
    const classInfoMap = new Map<string, CommoditySkuForestageResponse>()
    response1.data.currentPageData.forEach((item) => {
      classInfoMap.set((item.resource as any).schemeId, item)
    })
    data.forEach((item) => {
      if (classInfoMap.get(item.SCHEME_ID as string)) {
        const info = classInfoMap.get(item.SCHEME_ID as string)
        // item.technology_ID = info.skuProperty.technicalGrade.skuPropertyValueId
        // item.technology = info.skuProperty.technicalGrade.skuPropertyValueName
        item.region_code =
          info.skuProperty.province.skuPropertyValueId +
          (info.skuProperty.city.skuPropertyValueId ? '/' + info.skuProperty.city.skuPropertyValueId : '') +
          (info.skuProperty.county.skuPropertyValueId ? '/' + info.skuProperty.county.skuPropertyValueId : '')
        item.region =
          info.skuProperty.province.skuPropertyValueName +
          (info.skuProperty.city.skuPropertyValueName ? '/' + info.skuProperty.city.skuPropertyValueName : '') +
          (info.skuProperty.county.skuPropertyValueName ? '/' + info.skuProperty.county.skuPropertyValueName : '')
        item.classHourse = (info.resource as any).period
        item.price = info.commodityBasicData.price
      }
    })
    return data
  }
  /**
   * 获取下单的成功报名人次
   */
  async getSuccessTotalCount(): Promise<number> {
    let result = 0
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    const queryParams = new CollectiveSignQueryRequest()
    queryParams.collectiveSignupNo = this.collectiveSignupNo
    const response = await Collectivesign.findImportCollectiveSignupCompleteSuccessDataByPage({
      request: queryParams,
      page
    })
    if (response.status?.isSuccess()) {
      result = response.data?.totalSize ?? 0
    }
    return result
  }
  /**
   * 获取下单的失败报名人次
   */
  async getSignUpFailTotalCount(): Promise<number> {
    let result = 0
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    const queryParams = new CollectiveSignQueryRequest()
    queryParams.collectiveSignupNo = this.collectiveSignupNo
    const response = await Collectivesign.findImportCollectiveSignupFailDataByPage({
      request: queryParams,
      page
    })
    if (response.status?.isSuccess()) {
      result = response.data?.totalSize ?? 0
    }
    return result
  }
  /**
   * 报名失败数据列表（由业务微服务提供）
   * @returns
   */
  async queryFailSignUpData(page: Page, queryListRequest?: QueryListRequest): Promise<Array<TabItem>> {
    const metaPropertyList = new Array<MetaProperty>()
    for (const i in queryListRequest) {
      if (Object.prototype.hasOwnProperty.call(queryListRequest, i)) {
        const element = queryListRequest[i]
        if (element) {
          metaPropertyList.push({
            key: i,
            value: element
          })
        }
      }
    }
    const collectiveSignQueryRequest = new CollectiveSignQueryRequest()
    collectiveSignQueryRequest.collectiveSignupNo = this.collectiveSignupNo
    collectiveSignQueryRequest.metaPropertyList = metaPropertyList
    const request = { request: collectiveSignQueryRequest, page }
    const response = await Collectivesign.findImportCollectiveSignupFailDataByPage(request)
    const data = response.data.currentPageData.map((item) => {
      const obj: TabItem = {}
      item.row.forEach((res) => {
        obj[res.key] = res.value
        obj['errorMessage'] = item.errorMessage
      })
      item.runtimeProperties?.forEach((res) => {
        obj[res.key] = res.value
      })
      return obj
    })
    page.totalPageSize = response.data.totalPageSize
    page.totalSize = response.data.totalSize
    return data
  }
  /**
   * 修改报名数据
   * @returns
   */
  async updateSignupData(updateSignupDataRequest: UpdateSignupDataRequest): Promise<ResponseStatus> {
    const { status } = await Collectivesign.updateSignupData({
      collectiveSignupNo: this.collectiveSignupNo,
      signupDataList: [updateSignupDataRequest]
    })
    return status
  }
  /**
   * 删除报名数据
   * @param subTaskId      子任务数据id
   * @returns
   */
  async deleteSignUpData(subTaskId: string): Promise<ResponseStatus> {
    const { status } = await Collectivesign.deleteSignupData({
      collectiveSignupNo: this.collectiveSignupNo,
      subTaskId
    })
    return status
  }

  /**   获取集体报名状态 --- 校验
   * 1 - 正常
   * 2 - 交易完成
   * 3 - 交易关闭
   * 4 - 提交处理中
   * 5 - 取消处理中
   * 当状态码是1或者4时，不走提交集体报名
   */
  async findCollectiveSignupStatus(): Promise<number> {
    const { data } = await MsOrder.findBatchOrderBatchPayStatus(this.collectiveSignupNo)
    return data
  }
  /**
   * 提交集体报名
   */
  async commitCollectiveSignup(): Promise<ResponseStatus> {
    const { status } = await Collectivesign.commitCollectiveSignup(this.collectiveSignupNo)
    return status
  }
}
export default MutationBatchOrderSignUp
