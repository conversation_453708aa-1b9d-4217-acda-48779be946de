schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""变更产品供销合同的信息
		@param request 变更产品供销合同合作周期请求参数
	"""
	applySupplyDistributionContractInfoChanged(request:SupplyDistributionContractInfoChangedRequest):DistributionContractInfoChangedResultResponse
	"""供应商分销关系签订
		@param request 签订供销合同请求参数
		@return 签订供销合同结果
	"""
	applySupplyDistributionContractSigned(request:CreateSupplyDistributionRelationshipRequest):SupplyDistributionContractSignedResultResponse
	"""校验供应商分销关系签订是否存在（一级/二级合同）
		@param request 签订分销合同请求参数
		@return 校验结果
	"""
	verifySupplyDistributionContractSignedStatus(request:VerifySupplyDistributionContractSignedStatusRequest):VerifyContractSignedResultResponse
}
"""供应商创建分销关系
	<AUTHOR>
	@since 2023/12/19  17:43
"""
input CreateSupplyDistributionRelationshipRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.CreateSupplyDistributionRelationshipRequest") {
	"""【必填】分销商id
		当无上级分销商时 传一级分销商ID
		当有上级分销商是 传二级分销商ID（暂无二级分销商）
	"""
	distributorId:String
	"""【必填】分销服务商ID(分配的网校ID)集合"""
	distributorServiceIds:[String]
	"""【必填】分销地区集台(区级Code)"""
	distributionRegionList:[String]
	"""【必填】期限类型
		1:周期  2：长期
	"""
	contractDurationType:Int!
	"""【必填】是否立即开始"""
	immediatelyStart:Boolean!
	"""【非立即开始时必填】合同开始时间"""
	contractStartTime:DateTime
	"""【周期必填】合同结束时间"""
	contractEndTime:DateTime
	"""【非必填项】业务员ID"""
	salespersonId:String
}
"""供销合同信息变更请求
	<AUTHOR>
	@since 2023/12/4  15:28
"""
input SupplyDistributionContractInfoChangedRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.SupplyDistributionContractInfoChangedRequest") {
	"""【必填】产品供销合同id"""
	productSupplyDistributionContractId:String
	"""【必填】供销地区集台(区级Code)"""
	supplyDistributionRegionList:[String]
	"""【必填】是否立即开始"""
	immediatelyStart:Boolean!
	"""【非立即开始时必填】合同开始时间"""
	contractStartTime:DateTime
	"""合同结束时间
		如果是长期，则不填
	"""
	contractEndTime:DateTime
	"""【必填】 期限类型
		1:周期  2：长期
	"""
	contractDurationType:Int!
	"""【非必填项】业务员ID"""
	salespersonId:String
}
"""校验供应商分销关系签订是否存在（一级/二级合同）
	<AUTHOR>
	@since 2024/1/12  15:49
"""
input VerifySupplyDistributionContractSignedStatusRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.VerifySupplyDistributionContractSignedStatusRequest") {
	"""【必填】分销商id
		当无上级分销商时 传一级分销商ID
		当有上级分销商是 传二级分销商ID（暂无二级分销商）
	"""
	distributorId:String
	"""【必填】分销服务商ID(分配的网校ID)"""
	distributorServiceIds:[String]
}
"""<AUTHOR>
	@since 2024/1/26  9:14
"""
type DistributionContractInfoChangedResultResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distribution.DistributionContractInfoChangedResultResponse") {
	"""状态码
		//SUCCESS("200","成功"),
		//ERROR("50001","异常情况"),
		//CURRENT_DISTRIBUTOR_EXISTS_ONE_RELATIONSHIP("50002","当前分销商在网校已经存在一条有效的一级分销关系，无法续期。"),
		//CURRENT_DISTRIBUTOR_EXISTS_TWO_RELATIONSHIP("50003","当前分销商在网校已经存在一条有效的二级分销关系，无法续期。"),
		//PARENT_DISTRIBUTION_CONTRACT_NOT_EXIST("50004","上级分销合同不存在"),
		//PARENT_DISTRIBUTION_CONTRACT_EXPIRED("50005","上级分销合同周期已过期"),
		//SECONDARY_DISTRIBUTION_CONTRACT_VALID_TIME_NOT_IN_PRIMARY_DISTRIBUTION_VALID_TIME("50006","二级分销周期时间未在一级分销周期的有效时间范围内"),
		//SECONDARY_DISTRIBUTION_CONTRACT_AREA_NOT_IN_PRIMARY_DISTRIBUTION_AREA("50007","二级分销地区不包含在一级分销地区范围内");
	"""
	code:String
	"""授权结果信息"""
	message:String
}
"""签署供销合同响应结果
	<AUTHOR>
	@since 2023/12/4  14:46
"""
type SupplyDistributionContractSignedResultResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distribution.SupplyDistributionContractSignedResultResponse") {
	"""状态码"""
	code:String
	"""签署结果信息"""
	message:String
	"""签署供销合同的ID"""
	productSupplyDistributionContractId:String
	"""【当合同关系已存在时候返回已存在的合同ID】
		已存在的合同ID
	"""
	existContractId:String
	"""二级与一级分销地区 不匹配 地区描述信息"""
	distributionAreaNotMatch:DistributionAreaNotMatch
	"""二级与一级分销周期 不匹配 周期描述信息"""
	distributionPeriodNotMatch:DistributionPeriodNotMatch
}
"""二级与一级分销地区 不匹配 地区描述信息"""
type DistributionAreaNotMatch @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distribution.SupplyDistributionContractSignedResultResponse$DistributionAreaNotMatch") {
	primaryDistributionArea:[String]
	secondaryDistributionArea:[String]
}
"""二级与一级分销周期 不匹配 周期描述信息"""
type DistributionPeriodNotMatch @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distribution.SupplyDistributionContractSignedResultResponse$DistributionPeriodNotMatch") {
	"""【展示上级的分销周期】上级周期开始时间"""
	primaryDistributionPeriodStartDate:DateTime
	"""【展示上级的分销周期】上级周期结束时间"""
	primaryDistributionPeriodEndDate:DateTime
}
"""签署供销合同响应结果
	<AUTHOR>
	@since 2023/12/4  14:46
"""
type VerifyContractSignedResultResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distribution.VerifyContractSignedResultResponse") {
	"""状态码"""
	code:String
	"""签署结果信息"""
	message:String
	"""【当合同关系已存在时候返回已存在的合同ID】
		已存在的合同ID
	"""
	existContractId:String
}

scalar List
