import { RefundOrderStatusEnum } from '@api/service/common/trade/RefundOrderStatusEnum'
import {
  ReturnApprovalInfoResponse,
  ReturnCloseReasonResponse,
  ReturnOrderApplyInfoResponse,
  ReturnOrderBasicDataResponse,
  ReturnOrderResponse,
  ReturnOrderStatusChangeTimeResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import StudentUserInfoVo from '@api/service/management/user/query/student/vo/StudentUserInfoVo'
import { AdminInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import {
  OrderRefundTypeEnum,
  OnlyReturnList,
  OnlyRefundList
} from '@api/service/common/return-order/enums/OrderRefundType'

export class ReturnOrderRecordVo {
  /*
   *  操作人名称
   * */
  name = ''
  /**
   * 操作人用户ID
   */
  userId = ''
  /**
   * 操作人角色 17分销商 18供应商
   */
  roleCa = 0
  /*
   *  操作时间
   * */
  time = ''
  /*
   * 备注信息
   * */
  tipMsg = ''

  /*
   * 退款金额
   * */
  money = 0

  //UI列表展示的状态
  UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumApproving

  /**
   * 模型转化
   */
  static from(
    type: RefundOrderStatusEnum,
    dto: ReturnOrderResponse,
    studentUserInfoList: Array<StudentUserInfoVo>,
    adminUserInfo: Array<AdminInfoResponse>
  ) {
    const record = new ReturnOrderRecordVo()
    record.UIReturnOrderStatue = type
    const basicData = dto?.basicData || new ReturnOrderBasicDataResponse()
    const applyInfo = basicData.applyInfo || new ReturnOrderApplyInfoResponse()
    const approveInfo = dto?.approvalInfo || new ReturnApprovalInfoResponse()
    const returnClose = basicData.returnCloseReason || new ReturnCloseReasonResponse()
    const returnOrderStatusChangeTime =
      basicData.returnOrderStatusChangeTime || new ReturnOrderStatusChangeTimeResponse()

    if (type === RefundOrderStatusEnum.RefundOrderStatusEnumApproving) {
      record.userId = applyInfo.applyUser?.userId
      record.time = returnOrderStatusChangeTime.applied
      record.tipMsg = applyInfo.description
      record.money = basicData.refundAmount
    }
    if (
      [
        RefundOrderStatusEnum.RefundOrderStatusEnumRefuse,
        RefundOrderStatusEnum.RefundOrderStatusEnumHandling,
        RefundOrderStatusEnum.RefundOrderStatusEnumFail
      ].includes(type)
    ) {
      record.userId = approveInfo.approveUser?.userId
      record.time = approveInfo.approveTime
      record.tipMsg = approveInfo.approveComment
    }
    if (type === RefundOrderStatusEnum.RefundOrderStatusEnumCancel) {
      record.userId = returnClose.cancelUser?.userId
      record.time = returnOrderStatusChangeTime.closed
      record.tipMsg = returnClose?.cancelReason
    }
    if (type === RefundOrderStatusEnum.RefundOrderStatusEnumSuccess) {
      const refundType = basicData.returnOrderType as OrderRefundTypeEnum
      record.userId = dto.confirmUser?.userId
      if (OnlyReturnList.includes(refundType)) {
        record.time = returnOrderStatusChangeTime.returned
      } else if (OnlyRefundList.includes(refundType)) {
        record.time = returnOrderStatusChangeTime.refunded
      } else {
        record.time = returnOrderStatusChangeTime.returnedAndRefunded
      }
    }

    if (record.userId) {
      const findStudentInfo = studentUserInfoList.find((it) => it.userId === record.userId)
      if (findStudentInfo) {
        record.userId = findStudentInfo.userId
        record.name = findStudentInfo.userName
      } else {
        const findAdminInfo = adminUserInfo.find((it) => it.userInfo.userId === record.userId)
        if (findAdminInfo) {
          record.userId = findAdminInfo.userInfo?.userId
          record.name = findAdminInfo.userInfo?.userName
          if (findAdminInfo.roleList?.length) {
            findAdminInfo.roleList.forEach((role) => {
              if (role.roleCategory === 17 || role.roleCategory === 18) {
                record.roleCa = role.roleCategory
              }
            })
          }
        }
      }
    }

    return record
  }
}
