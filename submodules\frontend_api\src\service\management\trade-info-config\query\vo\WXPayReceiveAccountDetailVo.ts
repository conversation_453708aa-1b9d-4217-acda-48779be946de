import {
  ReceiveAccountConfigResponse,
  WechatPayEncryptionKeyDataResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import ReceiveAccountDetailVo from './ReceiveAccountDetailVo'

class WXPayReceiveAccountDetailVo extends ReceiveAccountDetailVo {
  /**
   * 支付账号类型id
   * 支付宝:ALIPAY
   * 微信：WXPAY
   * 支付宝H5:ALIPAYH5
   * 微信H5：WXPAYH5
   */
  paymentChannelId = ''
  /**
   * API密钥
   */
  privateKeyPWD = ''
  /**
   * 公众账号ID
   */
  appId = ''
  /**
   * 微信证书密钥
   */
  merchantKey = ''
  /**
   * 微信证书文件名称
   */
  privateKeyFileName = ''
  /**
   * 微信证书路径
   */
  privateKeyPath = ''

  from(res: ReceiveAccountConfigResponse) {
    this.accountType = res.accountType
    this.accountNo = res.accountNo
    this.paymentChannelId = res.paymentChannelId
    this.accountName = res.name
    this.refundWay = res.returnType
    this.taxPayerId = res.taxPayerId
    this.qrScanPrompt = res.qrScanPrompt
    if (res.encryptionKeyData.encryptionKeyType === 'WechatPay') {
      const temp = res.encryptionKeyData as WechatPayEncryptionKeyDataResponse
      this.appId = temp.appId
      this.merchantKey = temp.merchantKey
      this.privateKeyPWD = temp.privateKeyPWD
      this.privateKeyFileName = temp.privateKeyFileName
      this.privateKeyPath = temp.privateKeyPath
    }
  }
}
export default WXPayReceiveAccountDetailVo
