<template>
  <el-drawer
    title="选择培训证明进行打印"
    :visible.sync="studentDialog"
    size="1000px"
    custom-class="m-drawer"
    @close="close"
  >
    <div class="drawer-bd">
      <!--表格-->
      <el-table
        stripe
        :data="certTemplateList"
        max-height="500px"
        class="m-table"
        v-loading="query.loading"
        ref="drawerRef"
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="模板名称" min-width="160" fixed="left">
          <template slot-scope="scope">{{ scope.row.name || '-' }}</template>
        </el-table-column>
        <el-table-column label="所属行业" min-width="100">
          <template slot-scope="scope">{{ scope.row.belongsIndustryName || '-' }}</template>
        </el-table-column>
        <el-table-column label="模板说明" min-width="140">
          <template slot-scope="scope">{{ scope.row.describe || '-' }}</template>
        </el-table-column>
        <el-table-column label="创建时间" min-width="170">
          <template slot-scope="scope">{{ scope.row.createdTime || '-' }}</template>
        </el-table-column>
        <el-table-column label="查看" width="80" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="preview(scope.row)" :loading="previewing">预览</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template slot-scope="scope">
            <el-radio v-model="radio" :label="scope.row.name" @input="select(scope.row)">选择</el-radio>
          </template>
        </el-table-column>
      </el-table>

      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="determine" :loading="determineBtnLoading">确定</el-button>
    </div>
    <print-type-component ref="printType" @downloadDialog="downloadDialog" />
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Ref } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import TrainingCertificateModule from '@api/service/management/personal-leaning/TrainingCertificateModule'
  import CertificateTemplateResponseVo from '@api/service/management/personal-leaning/query/vo/CertificateTemplateResponseVo'
  import { UsageRangeEnum } from '@api/service/common/enums/personal-leaning/UsageRange'
  import { TrainingSchemeFormEnum } from '@api/service/common/enums/personal-leaning/TrainingSchemeForm'
  import MutationBatchPrintTraining from '@api/service/management/personal-leaning/mutation/MutationBatchPrintTraining'
  import { PrintCertificateTemplateRequest } from '@api/ms-gateway/ms-certificate-v1'
  import LearningArcjovesResponse from '@api/service/management/personal-leaning/query/vo/LearningArcjovesResponse'
  import PrintCertificationsVo from '@api/service/management/personal-leaning/mutation/vo/PrintCertificationsVo'
  import { ChooseTemplateCertificatePrintRequest } from '@api/platform-gateway/platform-certificate-v1'
  import PrintTypeComponent from '@hbfe/jxjy-admin-batchPrint/src/components/print-type-component.vue'
  import LearningArcjovesRequest from '@api/service/management/personal-leaning/query/vo/LearningArcjovesRequest'
  import { debounce, bind } from 'lodash-decorators'

  @Component({
    components: {
      PrintTypeComponent
    }
  })
  export default class extends Vue {
    @Ref('printType') printTypeRef: PrintTypeComponent
    studentDialog = false

    @Prop({
      type: Array,
      default: () => {
        return new Array<LearningArcjovesResponse>()
      }
    })
    tableData: LearningArcjovesResponse[]

    //  接收到的学员id数组
    @Prop({
      type: Array,
      default: () => {
        return new Array<string>()
      }
    })
    studentNoList: string[]

    //  接收到的学员id数组
    @Prop({
      type: String,
      default: ''
    })
    printType: string

    //  接收到的学员参训资格id
    @Prop({
      type: String,
      default: ''
    })
    studentQualificationId: string

    page: UiPage
    query: Query = new Query()

    printStudentParam = new PrintCertificationsVo()

    oneStudentParm = new ChooseTemplateCertificatePrintRequest()

    certTemplateList = new Array<CertificateTemplateResponseVo>()

    mutationBatchPrintTraining: MutationBatchPrintTraining =
      TrainingCertificateModule.mutationBatchPrintTrainingFactory.batchPrintTraining

    // 批量打印证明实例
    batchPrintTrainingModule = new MutationBatchPrintTraining()

    //防抖
    downloading = false
    previewing = false
    radio = ''

    // 模版信息
    tempalteInfo = new CertificateTemplateResponseVo()

    // 模板id
    templateId = ''

    // 模版名字
    templateName = ''

    // 是否批量能合并
    isMerge = ''

    // 抽屉组件的loading加载效果
    determineBtnLoading = false

    // 查询参数
    studentParams = new LearningArcjovesRequest()

    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    async open(studentParams?: LearningArcjovesRequest) {
      this.studentDialog = true
      this.studentParams = studentParams || new LearningArcjovesRequest()
      await this.doSearch()
    }

    showTemplateListDrawer(val: string) {
      this.radio = val
      this.studentDialog = true
      this.doSearch()
    }

    // async created() {
    //   //   await this.doSearch()
    // }

    rangeMapName = {
      [UsageRangeEnum.ALL_SERVICER]: '全网校'
    }

    classMapName = {
      [TrainingSchemeFormEnum.TRAINING_CLASS]: '培训班学习'
    }

    // 抽屉的确定按钮
    @bind
    @debounce(200)
    async determine() {
      //   // this.printStudentParam.studentNos = []
      //   this.printStudentParam.fileType = FileTypesEnum.PDF

      //   //  如果没有选中模板，那么模板id是空的
      //   if (this.templateId == '') {
      //     this.$message.warning('请先选择模板名称！')
      //     return
      //   } else {
      //     this.printStudentParam.templateId = this.templateId
      //     this.oneStudentParm.templateId = this.templateId
      //   }
      //   // this.printStudentParam.studentNos = this.studentNoList

      //   //   oneStudentParm是单个打印的入参，printStudentParam是批量打印的入参
      //   this.oneStudentParm.studentNo = this.studentNoList[0] as string
      //   this.oneStudentParm.qualificationId = this.studentQualificationId
      //   this.oneStudentParm.fileType = FileTypesEnum.PDF

      //   //   分批量打印还是单个打印
      //   if (this.printType == 'batch') {
      //     // 判断是否为安溪教师
      //     if (this.isMerge == '1') {
      //       this.printTypeRef.showDrower(this.printStudentParam, this.studentParams, true)
      //       return
      //     }
      //     // 判断是否为安溪教师end
      //     // 走批量打印接口

      //     this.determineBtnLoading = true
      //     const result = await this.batchPrintTrainingModule.doBatchPrintStudentCertificates(
      //       this.printStudentParam,
      //       this.studentParams
      //     )
      //     //   抽屉的loading加载效果关闭
      //     this.determineBtnLoading = false

      //     if (result?.status?.code === 200) {
      //       //   下载入口弹窗展示
      //       this.$emit('downloadDialog', true)
      //     } else {
      //       this.$message.error('批量打印失败')
      //     }
      //   } else {
      //     // 走单个打印口
      //     const result = await this.batchPrintTrainingModule.doPrintTrainingStudent(this.oneStudentParm)

      //     //   抽屉的loading加载效果关闭
      //     this.determineBtnLoading = false

      //     if (result?.status?.code === 200) {
      //       this.$message({
      //         message: '打印成功',
      //         type: 'success'
      //       })
      //     } else {
      //       this.$message.error('打印失败')
      //     }
      //   }

      //   批量打印证明按钮loading效果关闭
      //   this.$emit('getLoadingResult', false)

      //如果没有选中模版，进行校验
      if (this.templateId == '') {
        this.$message.warning('请先选择模板名称！')
        return
      } else {
        console.log('模板id', this.templateId)
      }

      //   this.$emit('getTemplateName', this.templateName)
      //   this.$emit('getTemplateId', this.templateId)

      this.$emit('getTemplateInfo', this.tempalteInfo)
      //   抽屉关闭
      this.studentDialog = false
    }

    // 类别打印回调
    downloadDialog() {
      this.$emit('downloadDialog', true)
      this.$emit('getLoadingResult', false)
      this.studentDialog = false
    }

    close() {
      // 关闭的时候清空选中的证书模板
      this.radio = ''
      this.$emit('getLoadingResult', false)
      this.studentDialog = false
    }

    async doSearch() {
      this.query.loading = true
      try {
        const res =
          await TrainingCertificateModule.queryTrainingCertificateFactory.certificateTemplate.queryCertificateTemplateList(
            this.page
          )
        if (res.status.isSuccess()) {
          this.certTemplateList = res.data
        }
        //搜索请求
      } catch (e) {
        //报错处理
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['drawerRef'] as any)?.doLayout()
        this.query.loading = false
      }
    }

    //预览
    async preview(item: CertificateTemplateResponseVo) {
      this.previewing = true
      window.open('/mfs/Cooper/templates/' + item.previewUrl, '_blank')
      this.previewing = false
      /*if (resolver) {
        window.open(resolver.location.name, '_blank')
      }*/
    }

    select(item: CertificateTemplateResponseVo) {
      this.tempalteInfo = item
      console.log('🚀 ~ extends ~ select ~ this.tempalteInfo:', this.tempalteInfo)

      //模板id
      this.templateId = item.id
      this.isMerge = item.isMerge
    }

    //下载
    async download(item: CertificateTemplateResponseVo) {
      const params = new PrintCertificateTemplateRequest()
      params.certificateTemplateId = item.id
      params.printFileType = 1
      let url
      try {
        url = await this.mutationBatchPrintTraining.doDownloadCertificateTemplate(params)
      } catch (e) {
        console.log(e)
        this.$message.error('请求cooper失败')
      }
      this.downloading = true
      //下载文件
      const link = document.createElement('a')
      const resolver = this.$router.resolve({
        name: url
      })

      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      const urlArr = link.href.split('.'),
        typeName = urlArr.pop()
      link.setAttribute('download', item.name)
      document.body.appendChild(link)
      link.click()
      link.remove()
      this.downloading = false
    }
  }
</script>
