<template>
  <el-main>
    <!--顶部tab标签-->
    <div class="f-p15" v-if="$hasPermission('courseSortConfig')" desc="课程排序配置" actions="doQueryPage,activated">
      <el-card shadow="never" class="m-card">
        <!--表格-->
        <el-table stripe :data="courseConfigList.configItems" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="规则名称" min-width="240">
            <template slot-scope="scope">{{ scope.row.ruleName }}</template>
          </el-table-column>
          <el-table-column label="是否乱序" min-width="100">
            <template slot-scope="scope">{{ scope.row.unSort ? '是' : '否' }}</template>
          </el-table-column>
          <el-table-column label="排序更新周期" min-width="150">
            <template slot-scope="scope">{{ scope.row.period }}</template>
          </el-table-column>
          <el-table-column label="使用范围" min-width="180">
            <template slot-scope="scope">{{ scope.row.useRange }}</template>
          </el-table-column>
          <el-table-column label="状态" min-width="120">
            <!-- <template v-if="isEnable">启用</template>
            <template v-else>禁用</template> -->
            <template slot-scope="scope">
              <div v-if="scope.row.status == 1">启用</div>
              <div v-else>禁用</div>
            </template>
          </el-table-column>
          <el-table-column label="编辑" width="160" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button
                v-if="$hasPermission('check')"
                desc="查看"
                actions="@hbfe/jxjy-admin-platform/src/function/check.vue"
                type="text"
                size="mini"
                @click="check(scope.row.ruleId)"
                >查看</el-button
              >
              <el-button
                v-if="$hasPermission('redace')"
                desc="编辑"
                actions="@hbfe/jxjy-admin-platform/src/function/redace.vue"
                type="text"
                size="mini"
                @click="redace(scope.row.ruleId)"
                >编辑</el-button
              >
              <!-- <el-button type="text" size="mini" v-if="scope.row.state" @click="enableDialog(scope.row, true)"
                >启用</el-button
              >
              <el-button type="text" size="mini" v-else @click="enableDialog(scope.row, false)">禁用</el-button> -->
              <el-popconfirm
                :title="scope.row.status === 1 ? '确定停用该规则吗？' : '确定启用该规则吗？'"
                confirm-button-text="确定"
                cancel-button-text="取消"
                @confirm="againEnableDialog(scope.row.status, scope.row)"
              >
                <el-button slot="reference" type="text" size="mini">{{
                  scope.row.status === 1 ? '禁用' : '启用'
                }}</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
        <el-dialog title="系统提醒" :visible.sync="twoDialog" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">{{
              againTitle
                ? '启用后该排序规则将按照定时周期执行，是否确认启用？'
                : '停用后该排序规则将失效，是否确认停用？'
            }}</span>
          </div>
          <div slot="footer">
            <el-button @click="twoDialog = false">取 消</el-button>
            <el-button type="primary" @click="doChangeStatus()">确 定</el-button>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import CourseConfigList from '@api/service/management/resource/course-sort-config/CourseConfigList'
  import CourseConfigItem from '@api/service/management/resource/course-sort-config/model/CourseConfigItem'
  @Component({})
  export default class extends Vue {
    isEnable = true // 是否启用排序
    againTitle = true // 是否启用排序再次提示
    page: UiPage = new UiPage()
    twoDialog = false // 是否启用排序第二次弹窗显隐
    ruleId = '' // 规则id
    courseConfigList = new CourseConfigList()
    ruleItem: CourseConfigItem
    courseConfigItem = new CourseConfigItem()
    async activated() {
      await this.doQueryPage()
    }
    // 获取排序规则列表数据
    async doQueryPage() {
      this.page.pageNo = 1
      this.page.pageSize = 10
      await this.courseConfigList.queryConfigItems(this.page)
    }
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    // 查看排序配置
    check(id: string) {
      this.$router.push(`/basic-data/platform/function/check/${id}`)
    }
    // 编辑排序配置
    redace(id: string) {
      this.$router.push(`/basic-data/platform/function/redace/${id}`)
    }
    // 启用排序弹窗-第二次弹窗
    againEnableDialog(status: number, item: CourseConfigItem) {
      this.ruleItem = item
      this.againTitle = status == 1 ? false : true
      this.twoDialog = true
    }
    // 启用或停用排序
    async doChangeStatus() {
      await this.ruleItem.changeStatus()

      if (this.isEnable) {
        this.twoDialog = false
        this.$message({
          message: '启用成功',
          type: 'success'
        })
      } else {
        this.twoDialog = false
        this.$message({
          message: '停用成功',
          type: 'success'
        })
      }
      setTimeout(async () => {
        await this.doQueryPage()
      }, 800)
    }
  }
</script>
