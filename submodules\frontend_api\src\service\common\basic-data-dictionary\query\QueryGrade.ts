import BasicDataGateway, {
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
import Basicdata from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import QuerySubject from '@api/service/common/basic-data-dictionary/query/QuerySubject'
class Llst extends TrainingPropertyResponse {
  children = new Array<TrainingPropertyResponse>()
  leaf = false
}

export default new (class QueryGrade {
  /**
   * 学段列表
   */
  gradeList: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()
  /**
   * 联级列表数据
   */
  gradeSelectorList = new Array<Llst>()

  /**
   * 学段Map key: 属性id
   */
  private gradeMap: Map<string, TrainingPropertyResponse> = new Map<string, TrainingPropertyResponse>()
  /**
   * 学段Map key: 字典code
   */
  private gradeCodeMap: Map<number, TrainingPropertyResponse> = new Map<number, TrainingPropertyResponse>()
  /**
   * 查询对应行业下学段
   */
  async queryGradeByIndustry(): Promise<Array<TrainingPropertyResponse>> {
    this.gradeList = new Array<TrainingPropertyResponse>()
    this.gradeMap = new Map<string, TrainingPropertyResponse>()

    const res = await Basicdata.listBusinessDataDictionaryInSubProject({
      businessDataDictionaryType: IndustryPropertyCodeEnum.LEARNING_PHASE
    })
    if (res.data?.length) {
      this.gradeList = res.data.map(item => {
        const temp = new TrainingPropertyResponse()
        temp.propertyId = item.id
        temp.code = item.code
        temp.parentId = item.parentId
        temp.name = item.name
        temp.sort = item.sort
        return temp
      })

      this.gradeList.map(item => {
        this.gradeMap.set(item.propertyId, item)
      })

      this.gradeList.map(item => {
        this.gradeCodeMap.set(item.code, item)
      })
    }

    return this.gradeList
  }

  /**
   * 查询对应行业下学段V2
   * @param industryId 行业id
   * @param industryPropertyId 行业属性id
   */
  async queryGradeByIndustryV2(industryId: string, industryPropertyId: string) {
    this.gradeList = new Array<TrainingPropertyResponse>()
    this.gradeMap = new Map<string, TrainingPropertyResponse>()

    const res = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
      industryPropertyId,
      industryId,
      categoryCode: IndustryPropertyCodeEnum.LEARNING_PHASE
    })
    if (res.data?.length) {
      this.gradeList = res.data
      this.gradeList.map(item => {
        this.gradeMap.set(item.propertyId, item)
      })
      this.gradeList.map(item => {
        this.gradeCodeMap.set(item.code, item)
      })
    }

    return this.gradeList
  }

  /**
   * 获取详情表
   * @param propertyId 属性id
   */
  getGradeDetail(propertyId: string): TrainingPropertyResponse {
    return this.gradeMap.get(propertyId)
  }
  /**
   * 通过code获取详情
   * @param code 字典code
   */
  getGradeCodeDetail(code: number): TrainingPropertyResponse {
    return this.gradeCodeMap.get(code)
  }
  /**
   * 联级选择器
   */
  async QueryGradeSelector() {
    await this.queryGradeByIndustry()
    this.gradeSelectorList = this.gradeList as Array<Llst>
    await Promise.all(
      this.gradeSelectorList.map(async item => {
        const res = await QuerySubject.querySubjectByIndustry(item.propertyId)
        res?.map(ite => {
          ite['leaf'] = true
        })
        if (res?.length) {
          item.leaf = false
          item.children = res
        } else {
          item.leaf = true
        }
      })
    )
    return this.gradeSelectorList
  }
})()
