import CourseInPoolOperate from '@api/service/common/models/course-pool/add/CourseInPoolOperate'

class CoursePoolOperate {
  /**
   * 名称
   */
  poolName = ''
  /**
   * 展示名称
   */
  showName = ''
  /**
   * 过期时间
   */
  expireTime: Date
  /**
   * 课程池描述
   */
  poolDescription = ''
  /**
   * 课程池内课程
   */
  courseInPoolList: Array<CourseInPoolOperate> = new Array<CourseInPoolOperate>()
  /**
   * 排序，暂时没用
   */
  sequence = -1
}

export default CoursePoolOperate
