"""独立部署的微服务,K8S服务名:ms-servicercontract-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""创建合同供应商合约并开通
		@param request 创建合同供应商合约请求
	"""
	createAndOpenContractProvider(request:CreateContractProviderContractRequest):Void @optionalLogin
	"""创建并开通网校开通合约
		@param request 网校合约通用请求对象
	"""
	createAndOpenOSContract(request:CreateOSContractRequest):Void @optionalLogin
	"""创建网校开通合约
		@param request 网校合约通用请求对象
	"""
	createOSContractDraft(request:CreateOSContractRequest):Void @optionalLogin
	"""创建网校草稿，根据状态决定是否开通
		校验同一个行业集合是否多个相同行业id，有异常抛出
		@param request
	"""
	createOnlineSchoolContract(request:CreateOSContractByStatusRequest):OnlineSchoolContractResponse @optionalLogin
	"""完成交付"""
	deliveredOSContract(id:String):Void @optionalLogin
	"""停用网校开通合约
		@param id 网校开通合约id
	"""
	disbaleOSContract(id:String):OnlineSchoolContractResponse @optionalLogin
	"""启用网校开通合约
		@param id 网校开通合约id
	"""
	enableOSContract(id:String):OnlineSchoolContractResponse @optionalLogin
	"""获取ui主题颜色列表
		@return 主题颜色列表
	"""
	findUiThemeColorList:[UiThemeColorResponse]
	"""当前服务商合约是否到期 -（新使用到期时间比较目前时间，进行比较。作为延期补丁使用）
		【注意】调用前需要调用applyForServiceByDomainName获取服务商上下文信息
		@return 服务商合约是否已到期
	"""
	isOnlineSchoolContractExpired:Boolean! @optionalLogin
	"""创建并开通网校开通合约并开通网校(新，传入网校id、)
		@param request 新网校合约通用请求对象
	"""
	newCreateAndOpenOSContract(request:NewCreateOSContractRequest):Void @optionalLogin
	"""开通网校开通合约
		@param id 网校开通合约id
	"""
	openOSContract(id:String):Void @optionalLogin
	"""删除网校开通合约
		@param id 网校开通合约id
	"""
	removeOSContract(id:String):Void @optionalLogin
	"""续期网校开通合约
		@param request 网校合约续期请求
	"""
	renewOSContract(request:RenewedOSContractRequest):Void @optionalLogin
	"""更新服务商合约基础信息 信息
		@param request
	"""
	updateOSBasicInfo(request:UpdateOSBasicInfoRequest):OnlineSchoolContractResponse @optionalLogin
	"""更新网校开通合约
		@param request 更新网校开通合约请求
	"""
	updateOSContract(request:UpdateOSContractRequest):Void @optionalLogin
	"""更新模板id"""
	updateOSTemplate(request:UpdateOnlineSchoolTemplateRequest):OnlineSchoolContractResponse @optionalLogin
	"""更新网校信息"""
	updateOnlineSchool(request:UpdateOnlineSchoolRequest):OnlineSchoolContractResponse @optionalLogin
	"""创建网校时，根据网校名称和网校类型查询网校名称是否重复
		@param
		@return
	"""
	validOnlineSchoolNameHasResponse(name:String,type:Int!):OnlineSchoolContractResponse
	"""修改网校时，根据传入的网校名称和网校类型查询网校名称是否重复,不包含当前网校
		@param
		@return
	"""
	validUpdateOnlineSchoolNameHasResponse(name:String,type:Int!,servicerId:String):OnlineSchoolContractResponse
}
"""管理人
	<AUTHOR>
"""
input Administrator @type(value:"com.fjhb.ms.servicercontract.v1.kernel.aggregate.entities.Administrator") {
	"""姓名"""
	name:String
	"""管理员账号"""
	authenticationIdentity:String
	"""手机号"""
	phone:String
	"""密码"""
	password:String
}
"""@author: linxiquan
	@Date: 2024/5/17 17:24
	@Description: 增值服务
"""
input AddValueService @type(value:"com.fjhb.ms.servicercontract.v1.kernel.aggregate.onlineschoolcontract.entities.AddValueService") {
	"""学习规则"""
	learnRule:Boolean
	"""智能学习"""
	intelligentlearning:Boolean
	"""分销服务"""
	distributionService:Boolean
	"""分销服务类型
		// * @see com.fjhb.domain.basicdata.api.servicercontract.consts.DistributionServiceType
		基础版 0 专业版 1
	"""
	distributionServiceType:Int
}
"""客户端
	<AUTHOR>
"""
input Client @type(value:"com.fjhb.ms.servicercontract.v1.kernel.aggregate.onlineschoolcontract.entities.Client") {
	"""客户端类型
		@see ClientTypes
	"""
	clientType:Int!
	"""门户类型
		@see PortalTypes
	"""
	portalType:Int!
	"""域名类型
		@see DomainNameTypes
	"""
	domainNameType:Int!
	"""域名"""
	domainName:String
	"""前端模板id"""
	portalTemplateId:String
	"""cnzz信息"""
	cnzz:String
	"""目录名"""
	dirName:String
}
"""行业培训属性
	<AUTHOR>
"""
input Industry @type(value:"com.fjhb.ms.servicercontract.v1.kernel.aggregate.onlineschoolcontract.entities.Industry") {
	"""行业id"""
	id:String
	"""行业属性编号"""
	properties:String
	"""网校业务行业属性id"""
	onlineSchoolProperty:String
}
"""网校实体类
	<AUTHOR>
"""
input OnlineSchool @type(value:"com.fjhb.ms.servicercontract.v1.kernel.aggregate.onlineschoolcontract.entities.OnlineSchool") {
	"""id"""
	id:String
	"""名称"""
	name:String
	"""培训属性"""
	trainingProperties:TrainingProperties
	"""人员行业培训属性"""
	personIndustries:[PersonIndustry]
	"""客户端"""
	clients:[Client]
	"""是否提供短信服务"""
	offerSmsService:Boolean!
	"""短信配置"""
	smsConfig:SmsConfig
	"""强制完善信息：
		true：当学员信息不全时，强制触发完善信息页面
		false：当学员信息不全时，强制跳过完善信息页面
	"""
	enabledForceCompleteInfo:Boolean
}
"""人员行业属性
	<AUTHOR>
"""
input PersonIndustry @type(value:"com.fjhb.ms.servicercontract.v1.kernel.aggregate.onlineschoolcontract.entities.PersonIndustry") {
	"""行业id"""
	id:String
	"""行业属性编号"""
	properties:String
	"""网校业务行业属性id"""
	onlineSchoolProperty:String
}
"""短信配置
	<AUTHOR>
"""
input SmsConfig @type(value:"com.fjhb.ms.servicercontract.v1.kernel.aggregate.onlineschoolcontract.entities.SmsConfig") {
	"""短信账号"""
	smsAccount:String
	"""短信密码"""
	smsPassword:String
	"""提供商id"""
	providerId:String
	"""短信网关授权配置名称"""
	ispName:String
	"""短信网关appId(非必填)"""
	ispAppId:String
	"""短信授权仅账号扩展信息，存放第三方额外信息，例如证书(非必填)"""
	extension:String
	"""短信模板列表"""
	messageTemplateList:[MessageTemplate]
}
"""培训属性
	<AUTHOR>
"""
input TrainingProperties @type(value:"com.fjhb.ms.servicercontract.v1.kernel.aggregate.onlineschoolcontract.entities.TrainingProperties") {
	"""年份"""
	years:[String]
	"""地区"""
	areas:[String]
	"""补贴培训：培训类别"""
	trainingCategoryIds:[String]
	"""行业培训属性"""
	industries:[Industry]
}
"""创建合同供应商合约请求
	<AUTHOR>
"""
input CreateContractProviderContractRequest @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.graphql.request.CreateContractProviderContractRequest") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerId:String
	userId:String
	"""业主单位全称"""
	unitName:String
	"""业主单位简称"""
	unitShotName:String
	"""统一社会信用代码"""
	code:String
	"""所属地区编号"""
	region:String
	"""合同供应商id"""
	contractProviderId:String
	"""是否已线下签署合约"""
	isOfflineContractSigned:Boolean!
	"""线下签署合约日期"""
	offlineContractSignedDate:DateTime
	"""到期时间"""
	expireDate:DateTime
	"""管理人"""
	administrator:Administrator
}
"""<AUTHOR> [2023/6/6 17:03]"""
input CreateOSContractByStatusRequest @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.graphql.request.CreateOSContractByStatusRequest") {
	"""服务商合约id"""
	id:String
	"""0-保存草稿
		1-正式创建
	"""
	status:Int
	"""业主单位全称"""
	unitName:String
	"""业主单位简称"""
	unitShotName:String
	"""手机号"""
	phone:String
	"""服务域名"""
	serviceAreas:[String]
	"""网校模式   1-正式实施  2-demo"""
	onlineSchoolModes:Int!
	"""是否已线下签署合约"""
	isOfflineContractSigned:Boolean
	"""线下签署合约日期"""
	offlineContractSignedDate:DateTime
	"""办理人（业主负责人）"""
	transactor:String
	"""培训周期模式  1-长期  2-短期"""
	trainingPeriodModes:Int
	"""到期时间"""
	expireDate:DateTime
	"""网校实体"""
	onlineSchool:NewCreateOnlineSchoolRequest
	"""管理员"""
	administrator:Administrator
	"""网校背景描述"""
	description:String
	"""归属市场经办"""
	marketTransactor:String
	"""增值服务"""
	addValueService:AddValueService
	"""培训机构类型
		@see PartnerType  ：1-个人  2-企业
	"""
	partnerType:Int
	"""统一社会信用代码"""
	code:String
	"""培训机构logo"""
	logo:String
}
"""网校合约通用请求对象
	<AUTHOR>
"""
input CreateOSContractRequest @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.graphql.request.CreateOSContractRequest") {
	"""业主单位全称"""
	unitName:String
	"""业主单位简称"""
	unitShotName:String
	"""手机号"""
	phone:String
	"""服务域名"""
	serviceAreas:[String]
	"""网校模式   1-正式实施  2-demo"""
	onlineSchoolModes:Int!
	"""是否已线下签署合约"""
	isOfflineContractSigned:Boolean
	"""线下签署合约日期"""
	offlineContractSignedDate:DateTime
	"""办理人（业主负责人）"""
	transactor:String
	"""培训周期模式  1-长期  2-短期"""
	trainingPeriodModes:Int
	"""到期时间"""
	expireDate:DateTime
	"""网校实体"""
	onlineSchool:CreateOnlineSchoolRequest
	"""管理员"""
	administrator:Administrator
	"""网校背景描述"""
	description:String
}
"""创建请求网校模型
	<AUTHOR>
"""
input CreateOnlineSchoolRequest @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.graphql.request.CreateOnlineSchoolRequest") {
	"""名称"""
	name:String
	"""培训属性"""
	trainingProperties:TrainingProperties
	"""客户端"""
	clients:[Client]
	"""是否提供短信服务"""
	offerSmsService:Boolean!
	"""短信配置"""
	smsConfig:SmsConfig
}
"""网校合约通用请求对象(新)
	<AUTHOR> [2023/5/24 9:28]
"""
input NewCreateOSContractRequest @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.graphql.request.NewCreateOSContractRequest") {
	"""业主单位全称"""
	unitName:String
	"""业主单位简称"""
	unitShotName:String
	"""手机号"""
	phone:String
	"""服务域名"""
	serviceAreas:[String]
	"""网校模式   1-正式实施  2-demo"""
	onlineSchoolModes:Int!
	"""是否已线下签署合约"""
	isOfflineContractSigned:Boolean
	"""线下签署合约日期"""
	offlineContractSignedDate:DateTime
	"""办理人（业主负责人）"""
	transactor:String
	"""培训周期模式  1-长期  2-短期"""
	trainingPeriodModes:Int
	"""到期时间"""
	expireDate:DateTime
	"""网校实体"""
	onlineSchool:NewCreateOnlineSchoolRequest
	"""管理员"""
	administrator:Administrator
	"""网校背景描述"""
	description:String
	"""归属市场经办"""
	marketTransactor:String
	"""增值服务"""
	addValueService:AddValueService
	"""培训机构类型
		@see PartnerType  ：1-个人  2-企业
	"""
	partnerType:Int
	"""统一社会信用代码"""
	code:String
	"""培训机构logo"""
	logo:String
}
"""<AUTHOR> [2023/5/24 9:47]"""
input NewCreateOnlineSchoolRequest @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.graphql.request.NewCreateOnlineSchoolRequest") {
	"""网校id"""
	id:String
	"""名称"""
	name:String
	"""培训属性"""
	trainingProperties:TrainingProperties
	"""人员行业培训属性"""
	personIndustries:[PersonIndustry]
	"""客户端"""
	clients:[Client]
	"""是否提供短信服务"""
	offerSmsService:Boolean!
	"""短信配置"""
	smsConfig:SmsConfig
	"""cnzz信息"""
	cnzz:String
	"""目录名"""
	dirName:String
	"""强制完善信息：默认为true
		true：当学员信息不全时，强制触发完善信息页面
		false：当学员信息不全时，强制跳过完善信息页面
	"""
	enabledForceCompleteInfo:Boolean
}
"""网校合约续期请求
	<AUTHOR>
"""
input RenewedOSContractRequest @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.graphql.request.RenewedOSContractRequest") {
	"""id"""
	id:String!
	"""续期后的到期时间"""
	expiredDate:DateTime!
}
"""<AUTHOR> [2023/6/28 14:51]"""
input UpdateOSBasicInfoRequest @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.graphql.request.UpdateOSBasicInfoRequest") {
	"""id 【必填】"""
	id:String
	"""业主单位全称"""
	unitName:String
	"""业主单位简称"""
	unitShotName:String
	"""手机号"""
	phone:String
	"""服务地区"""
	serviceAreas:[String]
	"""网校模式   1-正式实施  2-demo"""
	onlineSchoolModes:Int!
	"""是否已线下签署合约"""
	isOfflineContractSigned:Boolean
	"""线下签署合约日期"""
	offlineContractSignedDate:DateTime
	"""办理人（业主负责人）"""
	transactor:String
	"""培训周期模式  1-长期  2-短期 （下个版本去除，原型上没有这个字段）"""
	trainingPeriodModes:Int!
	"""培训属性"""
	trainingProperties:TrainingProperties
	"""人员行业培训属性"""
	personIndustries:[PersonIndustry]
	"""到期时间"""
	expireDate:DateTime
	"""归属市场经办"""
	marketTransactor:String
	"""网校背景描述"""
	description:String
	"""网校名称"""
	name:String
	"""增值服务"""
	addValueService:AddValueService
	"""培训机构类型
		@see PartnerType  ：1-个人  2-企业
	"""
	partnerType:Int
	"""统一社会信用代码"""
	code:String
	"""培训机构logo"""
	logo:String
}
"""更新网校开通合约请求
	<AUTHOR>
"""
input UpdateOSContractRequest @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.graphql.request.UpdateOSContractRequest") {
	"""id 【必填】"""
	id:String
	"""业主单位全称"""
	unitName:String
	"""业主单位简称"""
	unitShotName:String
	"""手机号"""
	phone:String
	"""服务地区"""
	serviceAreas:[String]
	"""网校模式   1-正式实施  2-demo"""
	onlineSchoolModes:Int!
	"""是否已线下签署合约"""
	isOfflineContractSigned:Boolean
	"""线下签署合约日期"""
	offlineContractSignedDate:DateTime
	"""办理人（业主负责人）"""
	transactor:String
	"""培训周期模式  1-长期  2-短期"""
	trainingPeriodModes:Int!
	"""到期时间"""
	expireDate:DateTime
	"""网校是否已签约【必填】"""
	signed:Boolean!
	"""网校实体"""
	onlineSchool:OnlineSchool
	"""管理员"""
	administrator:Administrator
	"""网校背景描述"""
	description:String
}
"""<AUTHOR> [2023/6/28 17:39]"""
input UpdateOnlineSchoolRequest @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.graphql.request.UpdateOnlineSchoolRequest") {
	"""服务商合约id"""
	id:String
	"""客户端"""
	clients:[Client]
	"""是否提供短信服务"""
	offerSmsService:Boolean!
	"""短信配置"""
	smsConfig:SmsConfig
	"""培训周期模式  1-长期  2-短期"""
	trainingPeriodModes:Int
	"""到期时间"""
	expireDate:DateTime
	"""强制完善信息：默认为true
		true：当学员信息不全时，强制触发完善信息页面
		false：当学员信息不全时，强制跳过完善信息页面
	"""
	enabledForceCompleteInfo:Boolean
}
"""<AUTHOR> [2023/7/6 14:06]"""
input UpdateOnlineSchoolTemplateRequest @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.graphql.request.UpdateOnlineSchoolTemplateRequest") {
	"""服务商合约id"""
	id:String!
	"""前端模板id（Web）"""
	portalTemplateId:String!
	"""前端模板id（H5）"""
	portalH5TemplateId:String!
}
"""短信模板
	<AUTHOR>
"""
input MessageTemplate @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.zeebe.message.MessageTemplate") {
	"""模板名称"""
	templateName:String
	"""模板内容"""
	content:String
	"""模板分类"""
	category:String
}
"""<AUTHOR> [2023/7/18 9:07]"""
type OnlineSchoolContractResponse @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.graphql.response.OnlineSchoolContractResponse") {
	"""状态码"""
	code:String
	"""响应消息"""
	message:String
}
"""Ui主题颜色
	<AUTHOR> [2023/6/6 16:13]
"""
type UiThemeColorResponse @type(value:"com.fjhb.ms.servicercontract.v1.kernel.gateway.graphql.response.UiThemeColorResponse") {
	"""颜色id"""
	id:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""帐户ID"""
	servicerId:String
	"""颜色值"""
	colorRef:String
	"""创建时间"""
	createdTime:DateTime
}

scalar List
