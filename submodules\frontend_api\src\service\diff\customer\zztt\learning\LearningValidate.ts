/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-05-24 17:03:04
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-05-25 18:00:41
 * @Description:
 */
import PlatformJxjypxtyptzzttSchool from '@api/diff-gateway/platform-jxjypxtypt-zztt-student-learning'
import CoursePlayTicket from '@api/service/customer/learning/scene/tickets/CoursePlayTicket'
import { ResponseStatus } from '@hbfe/common'
export default class LearningValidate {
  /**
   * 参训资格ID
   */
  qualificationId = ''
  /**
   * 校验失败code码
   */
  failCode: number
  async validate() {
    const res = await PlatformJxjypxtyptzzttSchool.validAllowToLearning({
      qualificationId: this.qualificationId,
      learnType: 2
    })
    if (res.status.code != 200) {
      res.status.code = res.status.errors[0].code
      res.status.message = res.status.getMessage()
      return res
    }
    return res
  }
}
