import SelectedCourseTreeVo from '@api/service/customer/learning/choose-course/mutation/vo/SelectedCourseTreeVo'
import { ResponseStatus } from '@hbfe/common'
import MutationWaitChooseCourse from '@api/service/customer/learning/choose-course/mutation/MutationWaitChooseCourse'
import MutationChooseCourseRule from '@api/service/customer/learning/choose-course/mutation/MutationChooseCourseRule'
import ChooseCourseVo from '@api/service/customer/learning/choose-course/mutation/vo/ChooseCourseVo'
// import ChooseCourseGateway, { ChooseCourseRequest } from '@api/ms-gateway/ms-choose-course-v1'
import ChooseCourseGateway, { ChooseCourseRequest } from '@api/ms-gateway/ms-choose-course-v1'
import PendingSubmitCourseVo from '@api/service/customer/learning/choose-course/mutation/vo/PendingSubmitCourseVo'
import MutationEnterChooseCourse from '@api/service/customer/learning/choose-course/mutation/MutationEnterChooseCourse'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import Utils from '@api/service/customer/learning/choose-course/mutation/utils'
import WaitChooseCourseTreeCacheVo from './vo/WaitChooseCourseTreeCacheVo'

/**
 * @description 选课清单列表
 */
class MutationSelectedCourse {
  /**
   * 打平的已选未提交课程id、课程学习大纲id列表
   */
  private _selectedCourseIdList: Array<PendingSubmitCourseVo> = new Array<PendingSubmitCourseVo>()

  /**
   * 已选未提交课程列表
   */
  selectedCourseTreeList = new Array<SelectedCourseTreeVo>()

  /**
   * 已选学时
   */
  totalSelectedPeriod = 0

  /**
   * 还需学时
   */
  totalNeedPeriod = CalculatorObj.subtract(MutationChooseCourseRule.needPeriod, this.totalSelectedPeriod)

  /**
   * 增加课程的校验
   * @param {ChooseCourseVo} course 课程对象
   * @return {ResponseStatus} 选择课程时的学时校验进行弹窗展示
   */
  handleCourseVerify(course: ChooseCourseVo) {
    if (!course.isSelected) {
      this.totalNeedPeriod = CalculatorObj.subtract(MutationChooseCourseRule.needPeriod, this.totalSelectedPeriod)
      if (MutationChooseCourseRule.needPeriod > 0) {
        if (this.totalNeedPeriod === 0) {
          return new ResponseStatus(1001, '选择失败，提示无需选课')
        } else if (course.period === this.totalNeedPeriod) {
          return new ResponseStatus(1002, '选课成功，提示确认选课')
        } else if (course.period > this.totalNeedPeriod) {
          return new ResponseStatus(1003, '选课成功，提示是否抹平')
        } else {
          return new ResponseStatus(1004, '选课成功无提示')
        }
      } else {
        return new ResponseStatus(1001, '选择失败，提示无需选课')
      }
    }
    return new ResponseStatus(1000, '移除课程无提示')
  }

  /**
   * 选中/移除已选课程 在调用此方法之前先调用handleCourseVerify
   * @param {string} courseId - 课程Id
   * @param {string} outlineId - 课程学习大纲Id
   * @return void
   */
  handleCourse(courseId: string, outlineId: string) {
    // 选课清单移除实际是操作可选课程列表，找到该课程并将选中状态置为未选中，
    // 然后根据可选课程列表重新获取选课清单数据并返回选课清单、还需选课学时、已选学时 - LWF
    const courseTreeTemp = Utils.getOutlineTreeNode(
      MutationWaitChooseCourse.waitChooseCourseTreeCache,
      outlineId,
      'outlineId'
    )

    if (courseTreeTemp['outlineId']) {
      const course = courseTreeTemp.chooseCourseList?.find(course => course.courseId === courseId)
      if (course) {
        // 将课程选中状态进行取反
        course.isSelected = !course.isSelected
        // todo 抹平学时的动作
        this.floatSelectedCoursePeriod(course.isSelected, course)
        // 进行已选课程id的移除和增加
        this.setSelectedCourseIdList(courseId, outlineId)
        return this.getSelectedCourseList(courseTreeTemp)
      } else {
        // 在已存在的树查不到 到这边只有删除已选课程列表的情况
        this.setSelectedCourseIdList(courseId, outlineId)
        this.selectedCourseTreeList.forEach(item => {
          if (item.outlineId === outlineId) {
            for (let i = 0; i < item.selectedList.length; i++) {
              const element = item.selectedList[i]
              if (element.courseId === courseId) {
                item.selectedList.splice(i, 1)
                break
              }
            }
          }
        })
        this.selectedCourseTreeList = []
        const arr = [...this.outlineIdMap.keys()]
        arr.forEach(item => {
          const temp = this.outlineIdMap.get(item)
          temp.outlinePeriod = temp.selectedList?.reduce(
            (total, secondCourse) => (total = CalculatorObj.add(total, secondCourse.floatPeriod)),
            0
          )
          if (temp.selectedList.length !== 0) {
            this.selectedCourseTreeList.push(this.outlineIdMap.get(item))
          }
        })
        this.totalSelectedPeriod = 0
        this.selectedCourseTreeList.forEach(item => {
          item.selectedList.forEach(temp => {
            this.totalSelectedPeriod += temp.period
          })
        })
        // 计算还需学时
        this.totalNeedPeriod = CalculatorObj.subtract(MutationChooseCourseRule.needPeriod, this.totalSelectedPeriod)
      }
    } else {
      console.log('未找到该大纲下的课程，操作课程失败！！！！！！！！！！！！！')
    }
  }

  /**
   * 提交选课内容
   * @return {ResponseStatus}
   */
  async doChooseCourse(): Promise<ResponseStatus> {
    // 选课内容真正提交，提交成功同时联动可选课程列表、学时重算 -LWF
    const param = new ChooseCourseRequest()
    param.chooseToken = MutationEnterChooseCourse.token
    param.courseList = this._selectedCourseIdList
    const res = await ChooseCourseGateway.chooseCourse(param)

    // if (!res.status.isSuccess() && res.data.code != '200') {
    if (res.data.code != '200') {
      // 可能是因为勾选课程被方案剔除 是否需要重新获取新的课程列表
      if (res.data.code === '51001') {
        this.removeErrorCourse(res.data.chooseErrorResult)
        return new ResponseStatus(500, '课程大纲内不存在该课程')
      }
      if (res.data.code === '51002') {
        return new ResponseStatus(500, '重复选课')
      }
      if (res.data.code === '41005') {
        return new ResponseStatus(500, '选课失败，选课范围不满足选课规则要求选课')
      }
      if (res.data.code === '51004') {
        return new ResponseStatus(500, '三级大纲不符合工种要求')
      }
      if (res.data.code === '41003') {
        return new ResponseStatus(500, '超出选修课允许最大学时')
      }
      return new ResponseStatus(500, '部分课程发生变化，请重新选课')
    } else {
      // 清空已选清单
      this._selectedCourseIdList = new Array<PendingSubmitCourseVo>()
      this.selectedCourseTreeList = new Array<SelectedCourseTreeVo>()
      if (this.totalNeedPeriod === 0) {
        return new ResponseStatus(200, '选课成功，您已完成选课，请尽快学习')
      } else {
        const result = Number(this.totalNeedPeriod.toFixed(2))
        return new ResponseStatus(200, `选课成功，您还需选课${result}学时`)
      }
    }
  }
  outlineIdMap: Map<string, SelectedCourseTreeVo> = new Map()

  /**
   * 获取已选待提交课程清单（以二级分类形式展示、并且显示二级分类累计学时）
   * @return {SelectedCourseTreeVo}
   */
  private getSelectedCourseList(tree: WaitChooseCourseTreeCacheVo): Array<SelectedCourseTreeVo> {
    if (this.outlineIdMap.get(tree.outlineId)) {
      const selectArr = tree.chooseCourseList.filter(item => item.isSelected)
      const noSelectArr = tree.chooseCourseList.filter(item => !item.isSelected)

      // const temp = SelectedCourseTreeVo.from(tree, selectArr)
      this.selectedCourseTreeList.forEach(item => this.outlineIdMap.set(item.outlineId, item))
      selectArr.forEach(item => {
        const temp = this.outlineIdMap.get(tree.outlineId)
        const ids = temp.selectedList.map(item => item.courseId)
        const tempL: ChooseCourseVo[] = []
        // temp.selectedList.forEach(seTemp => {
        if (!ids.includes(item.courseId)) {
          tempL.push(item)
        }
        // })
        temp.selectedList = temp.selectedList.concat(tempL)
      })
      noSelectArr.forEach(item => {
        const tempL: string[] = []
        const temp = this.outlineIdMap.get(tree.outlineId)
        const ids = temp.selectedList.map(item => item.courseId)
        // temp.selectedList.forEach(seTemp => {
        if (ids.includes(item.courseId)) {
          tempL.push(item.courseId)
        }
        const cloneD: ChooseCourseVo[] = []
        temp.selectedList.forEach(item => {
          if (!tempL.includes(item.courseId)) {
            cloneD.push(item)
          }
        })
        temp.selectedList = cloneD
      })
    } else {
      const selectArr = tree.chooseCourseList.filter(item => item.isSelected)
      this.outlineIdMap.set(tree.outlineId, SelectedCourseTreeVo.from(tree, selectArr))
    }
    this.selectedCourseTreeList = []
    const arr = [...this.outlineIdMap.keys()]
    arr.forEach(item => {
      const temp = this.outlineIdMap.get(item)
      temp.outlinePeriod = temp.selectedList?.reduce(
        (total, secondCourse) => (total = CalculatorObj.add(total, secondCourse.floatPeriod)),
        0
      )
      if (temp.selectedList.length !== 0) {
        this.selectedCourseTreeList.push(this.outlineIdMap.get(item))
      }
    })
    // let key = false
    // this.selectedCourseTreeList.forEach(item => {
    //   if (item.outlineId === temp.outlineId) {
    //     key = true
    //     const ids = item.selectedList.map(item => item.courseId) // 旧的
    //     temp.selectedList.forEach(tempC => {
    //       if (!ids.includes(tempC.courseId)) {
    //         //
    //         const tem = item.selectedList.filter(fiItem => fiItem.courseId === tempC.courseId)[0]
    //         temp.selectedList.push(tem)
    //       }
    //     })
    //   }
    // })
    // if (!key) {
    //   this.selectedCourseTreeList.push(temp)
    // }
    // 计算总学时（注意已抹平）
    // this.totalSelectedPeriod = this.selectedCourseTreeList.reduce(
    //   (total, secondCourse) => (total += secondCourse.outlinePeriod),
    //   0
    // )
    // if (selectedCourseTreeListTemp[0].selectedList[0].isSearch) {
    //   this.selectedCourseTreeList = selectedCourseTreeListTemp
    //   if (this.selectedCourseTreeList.length >= selectedCourseTreeListTemp.length) {
    //     let key = false
    //     this.selectedCourseTreeList.forEach(item => {
    //       selectedCourseTreeListTemp.forEach(temp => {
    //         if (item.outlineId === temp.outlineId) {
    //           key = true
    //           item.selectedList.push(...temp.selectedList)
    //         }
    //       })
    //     })
    //     if (!key) {
    //       this.selectedCourseTreeList.push(...selectedCourseTreeListTemp)
    //     }
    //   } else {
    //     let key = false
    //     selectedCourseTreeListTemp.forEach(item => {
    //       this.selectedCourseTreeList.forEach(temp => {
    //         if (item.outlineId === temp.outlineId) {
    //           key = true
    //           temp.selectedList.push(...temp.selectedList)
    //         }
    //       })
    //     })
    //     if (!key) {
    //       this.selectedCourseTreeList.push(...selectedCourseTreeListTemp)
    //     }
    //   }
    // } else {
    //   this.selectedCourseTreeList = selectedCourseTreeListTemp
    // }
    this.totalSelectedPeriod = 0
    this.selectedCourseTreeList.forEach(item => {
      item.selectedList.forEach(temp => {
        this.totalSelectedPeriod += temp.period
      })
    })

    // 计算还需学时
    this.totalNeedPeriod = CalculatorObj.subtract(MutationChooseCourseRule.needPeriod, this.totalSelectedPeriod)
    return this.selectedCourseTreeList
  }

  /**
   * 获取已选课程的课程id列表
   * @return idList
   */
  get selectedCourseIdList() {
    return this._selectedCourseIdList
  }

  /**
   * 设置已选的打平的课程id、课程学习大纲id的列表
   * @param {string} courseId 课程id
   * @param {string} outlineId 课程学习大纲id
   */
  private setSelectedCourseIdList(courseId: string, outlineId: string) {
    const idObjIndex = this._selectedCourseIdList?.findIndex(
      idObj => idObj.courseId === courseId && idObj.outlineId === outlineId
    )
    if (idObjIndex > -1) {
      this._selectedCourseIdList.splice(idObjIndex, 1)
    } else {
      this._selectedCourseIdList.push({ courseId: courseId, outlineId: outlineId })
    }
  }

  /**
   * 抹平学时（添加时调用）附带对本地缓存的还需学时进行校验 对于选课成功后的判断有用
   * @param {ChooseCourseVo} course 课程
   */
  private floatSelectedCoursePeriod(isSelect: boolean, course: ChooseCourseVo) {
    if (isSelect && course.period > this.totalNeedPeriod) {
      course.floatPeriod = this.totalNeedPeriod
    }
    if (!isSelect) {
      // 清除后还原学时
      course.floatPeriod = course.period
    }
  }

  /**
   * 移除异常课程
   */
  private removeErrorCourse(courseList: Array<PendingSubmitCourseVo>) {
    // todo 已选课程和待选课列表的移除
    courseList?.forEach(course => {
      // 移除已选课程中的异常课程
      const selectedIndex = this._selectedCourseIdList.findIndex(
        selectedCourse => selectedCourse.courseId === course.courseId && selectedCourse.outlineId === course.outlineId
      )
      if (selectedIndex > -1) {
        this._selectedCourseIdList.splice(selectedIndex, 1)
      }
      // 移除待选课列表中的异常课程
      const courseTreeTemp = Utils.getOutlineTreeNode(
        MutationWaitChooseCourse.waitChooseCourseTreeCache,
        course.outlineId,
        'outlineId'
      )
      if (courseTreeTemp['outlineId']) {
        const chooseIndex = courseTreeTemp.chooseCourseList.findIndex(
          chooseCourse => chooseCourse.courseId === course.courseId
        )
        if (chooseIndex > -1) {
          courseTreeTemp.chooseCourseList.splice(chooseIndex, 1)
        }
      }
    })
  }
}

export default MutationSelectedCourse
