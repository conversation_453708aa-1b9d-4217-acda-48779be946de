import BasicDataGateway, {
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'

export default new (class QueryJobCategory {
  /**
   * 工种列表
   */
  jobCategoryList: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()

  /**
   * 工种map key: 属性id
   */
  private jobCategoryMap: Map<string, TrainingPropertyResponse> = new Map<string, TrainingPropertyResponse>()

  /**
   * 查询对应行业下工种
   * @param industryId 行业id
   * @param industryPropertyId 行业属性id
   */
  async QueryJobCategoryByIndustry(industryId: string, industryPropertyId: string) {
    this.jobCategoryList = new Array<TrainingPropertyResponse>()
    this.jobCategoryMap = new Map<string, TrainingPropertyResponse>()

    const res = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
      industryPropertyId,
      industryId,
      categoryCode: IndustryPropertyCodeEnum.JOB_CATEGORY
    })
    if (res.data?.length) {
      this.jobCategoryList = res.data
      res.data.map(item => {
        this.jobCategoryMap.set(item.propertyId, item)
      })
    }

    return this.jobCategoryList
  }

  /**
   * 获取详情表
   * @param propertyId 属性id
   */
  getJobCategoryDetail(propertyId: string): TrainingPropertyResponse {
    return this.jobCategoryMap.get(propertyId)
  }
})()
