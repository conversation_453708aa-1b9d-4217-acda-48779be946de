/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-11-01 15:48:08
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-11-01 17:15:03
 * @Description:
 */
import Basicdata, { JobCategoryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import Page from '../../models/page-query/Page'
/**
 * 字典
 */
class Occupation {
  /**
   * 工种全量数据
   */
  data = new Array<JobCategoryResponse>()

  /**
   * 工种字典缓存
   */
  private jobCategoryCachae = new Map<string, JobCategoryResponse>()
  /**
   * 工种列表
   */
  async pageJobCategoryInSubProject() {
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    const response = await Basicdata.pageJobCategoryInSubProject({
      page
    })
    let index = 0
    if (response.data.totalSize > 200) {
      index = Math.ceil((response.data.totalSize - 200) / 200)
    }
    const data = response.data.currentPageData
    for (let i = 0; i < index; i++) {
      page.pageNo = page.pageNo + 1
      page.pageSize = 200
      const temp = await Basicdata.pageJobCategoryInSubProject({ page })
      data.push(...temp.data.currentPageData)
    }
    this.data = data
  }
  /**
   * 工种详情
   */
  async getJobCategoryInSubProject(id: string) {
    if (!this.jobCategoryCachae.get(id)) {
      const { data } = await Basicdata.getJobCategoryInSubProject(id)
      this.jobCategoryCachae.set(id, data)
    }
    return this.jobCategoryCachae.get(id)
  }
}
export default new Occupation()
