<template>
  <el-main>
    <div class="f-p15">
      <el-row :gutter="15" class="is-height">
        <el-col :span="7">
          <el-card shadow="never" class="m-card is-header-sticky f-mb15">
            <div slot="header" class="is-sticky">
              <span class="tit-txt">导出任务类型</span>
            </div>
            <div class="f-plr20 f-pb20">
              <el-row :gutter="5" class="m-query no-gutter f-pt15">
                <el-form :inline="true" label-width="auto">
                  <el-col :span="18">
                    <el-form-item>
                      <el-input v-model="input" placeholder="请输入任务类型关键字" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item>
                      <el-button type="primary">查询</el-button>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <div class="m-plan-list">
                <el-table
                  stripe
                  :data="tableData"
                  max-height="500px"
                  highlight-current-row
                  class="m-table is-body is-arrow"
                >
                  <el-table-column>
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">导入学员并开班</div>
                      <div v-else-if="scope.$index === 0">导入学员</div>
                      <div v-else>个人报名增值税电子普通发票（线下开票）</div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="17">
          <!--方案开通统计-->
          <el-card shadow="never" class="m-card is-header-sticky f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">方案开通统计</span>
            </div>
            <div class="f-plr20 f-pt20">
              <el-row :gutter="16" class="m-query is-border-bottom no-gutter">
                <el-form :inline="true" label-width="auto">
                  <el-col :sm="12" :xl="6">
                    <el-form-item label="任务名称">
                      <el-input v-model="input" clearable placeholder="请输入任务名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :xl="5">
                    <el-form-item label="执行状态">
                      <el-select v-model="select" clearable filterable placeholder="请选择">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :xl="9">
                    <el-form-item label="执行时间">
                      <el-date-picker
                        v-model="form.date1"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="起始时间"
                        end-placeholder="结束时间"
                      >
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :xl="4" class="f-fr">
                    <el-form-item class="f-tr">
                      <el-button type="primary">查询</el-button>
                      <el-button>重置</el-button>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500" class="m-table">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="任务名称" min-width="240" fixed="left">
                  <template>读取导入的文件名.xlsx.2021-12-09 13:56:04</template>
                </el-table-column>
                <el-table-column label="操作人" min-width="140">
                  <template>操作人名称</template>
                </el-table-column>
                <el-table-column label="任务处理时间" min-width="170">
                  <template>2020-11-11 12:20:20</template>
                </el-table-column>
                <el-table-column label="任务结束时间" min-width="170">
                  <template>2020-11-11 12:20:20</template>
                </el-table-column>
                <el-table-column label="任务执行状态" min-width="120">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-tag type="info">未执行</el-tag>
                    </div>
                    <div v-else>
                      <el-tag type="success">已执行</el-tag>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="任务处理结果" min-width="120">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-badge is-dot type="danger" class="badge-status">失败</el-badge>
                    </div>
                    <div v-else>
                      <el-badge is-dot type="success" class="badge-status">成功</el-badge>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="140" align="center" fixed="right">
                  <template>
                    <el-button type="text" size="mini">下载导出数据</el-button>
                    <el-button type="text" size="mini">查看日志</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!--分页-->
              <el-pagination
                background
                class="f-mt15 f-tr"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
          </el-card>
          <!--问卷整体报告-->
          <el-card shadow="never" class="m-card is-header-sticky f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">问卷整体报告</span>
            </div>
            <div class="f-plr20 f-pt20">
              <div class="f-f24 f-fb f-tc f-p20">标题区域标题区域标题区域标题区域标题区域标题区域</div>
              <el-row type="flex">
                <el-col :span="18">
                  <el-form ref="form" :model="form" label-width="auto" class="m-form">
                    <el-form-item label="培训班名称：">这里是一个培训班名称</el-form-item>
                    <el-form-item label="授课教师名称：">张三、李四</el-form-item>
                    <el-form-item label="授课起止时间：">2024-10-01 08:08:08</el-form-item>
                  </el-form>
                </el-col>
              </el-row>
              <div class="f-mb10">
                <div class="m-sub-tit">
                  <span class="tit-txt"
                    >第1题：<span>读取试题题目</span><el-tag size="small" class="f-ml5">单选题</el-tag></span
                  >
                </div>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column label="选项" min-width="160">
                    <template>选项A：读取选项A内容</template>
                  </el-table-column>
                  <el-table-column label="选择数" min-width="160" align="center">
                    <template>10</template>
                  </el-table-column>
                  <el-table-column label="比例" min-width="160" align="center">
                    <template>10%</template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="f-mb10">
                <div class="m-sub-tit">
                  <span class="tit-txt"
                    >第2题：<span>读取试题题目</span><el-tag size="small" class="f-ml5">多选题</el-tag></span
                  >
                </div>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column label="选项" min-width="160">
                    <template>选项A：读取选项A内容</template>
                  </el-table-column>
                  <el-table-column label="选择数" min-width="160" align="center">
                    <template>10</template>
                  </el-table-column>
                  <el-table-column label="比例" min-width="160" align="center">
                    <template>10%</template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="f-mb10">
                <div class="m-sub-tit">
                  <span class="tit-txt"
                    >第3题：<span>读取试题题目</span><el-tag size="small" class="f-ml5">问答题</el-tag></span
                  >
                </div>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="答案文本" min-width="240" align="center">
                    <template>读取提交的回答内容</template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="f-mb10">
                <div class="m-sub-tit">
                  <span class="tit-txt"
                    >第4题：<span>读取试题题目</span><el-tag size="small" class="f-ml5">量表题</el-tag></span
                  >
                </div>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column label="选项" min-width="160">
                    <template>1（非常不满意）</template>
                  </el-table-column>
                  <el-table-column label="选择数" min-width="160" align="center">
                    <template>10</template>
                  </el-table-column>
                  <el-table-column label="比例" min-width="160" align="center">
                    <template>10%</template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-card>
          <!--试题选项问答内容-->
          <el-card shadow="never" class="m-card is-header-sticky f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">试题选项问答内容</span>
            </div>
            <div class="f-plr20 f-pt20">
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column label="题型" min-width="100" align="center">
                  <template>问答题</template>
                </el-table-column>
                <el-table-column label="选项" min-width="100" align="center">
                  <template>A</template>
                </el-table-column>
                <el-table-column label="选项内容" min-width="140">
                  <template>这是A选项的内容</template>
                </el-table-column>
                <el-table-column label="提交时间" min-width="160" align="center">
                  <template>2024-10-01 08:08:08</template>
                </el-table-column>
                <el-table-column label="姓名" min-width="100" align="center">
                  <template>学员姓名</template>
                </el-table-column>
                <el-table-column label="证件号" min-width="180" align="center">
                  <template>350103200010010000</template>
                </el-table-column>
                <el-table-column label="答案文本" min-width="160">
                  <template>回答的内容</template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
          <!--问答文本-->
          <el-card shadow="never" class="m-card is-header-sticky f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">问答文本</span>
            </div>
            <div class="f-plr20 f-pt20">
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column label="题型" min-width="100" align="center">
                  <template>问答题</template>
                </el-table-column>
                <el-table-column label="提交时间" min-width="160" align="center">
                  <template>2024-10-01 08:08:08</template>
                </el-table-column>
                <el-table-column label="姓名" min-width="100" align="center">
                  <template>学员姓名</template>
                </el-table-column>
                <el-table-column label="证件号" min-width="180" align="center">
                  <template>350103200010010000</template>
                </el-table-column>
                <el-table-column label="答案文本" min-width="160">
                  <template>回答的内容</template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
