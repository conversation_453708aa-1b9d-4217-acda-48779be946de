import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-student-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-student-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 学员信息查询接口请求类
 */
export class StudentInfoQueryRequest {
  /**
   * 学员信息key
由服务平台传给培训机构的培训平台入参， 过期后将取不到数据
   */
  studentInfoKey?: string
  /**
   * 初始token
   */
  token?: string
}

/**
 * 学员信息查询接口返回类
 */
export class StudentInfoQueryResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 返回状态信息
   */
  message: string
  /**
   * 学员账户id
   */
  accountId: string
  /**
   * 学员姓名
   */
  name: string
  /**
   * 学员身份证号
   */
  idCard: string
  /**
   * 单点登入token
   */
  loginToken: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 学员信息查询
   * @param request 学员信息查询请求信息（学员信息key）
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async StudentInfoQuery(
    request: StudentInfoQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.StudentInfoQuery,
    operation?: string
  ): Promise<Response<StudentInfoQueryResponse>> {
    return commonRequestApi<StudentInfoQueryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
