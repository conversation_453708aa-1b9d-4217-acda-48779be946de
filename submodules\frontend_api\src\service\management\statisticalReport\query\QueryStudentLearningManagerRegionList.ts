import exportMsgateway from '@api/platform-gateway/diff-ms-data-export-front-gateway-DataExportBackstage'
import {
  CertificateLearningConfigResultResponse,
  default as MsSchemeQueryFrontGatewayCourseLearningBackstage,
  default as schemeLearningMsGateway,
  GradeLearningConfigResultResponse,
  LearningResultResponse,
  SchemeConfigResponse,
  StudentSchemeLearningInOnlineResponse,
  StudentSchemeLearningRequest,
  StudentSchemeLearningResponse,
  UserRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { ConnectManageSystemRequest } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import tradeQueryGateway, {
  OrderResponse,
  RegionModel
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { getOrderInServicer } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage/graphql-importer'
import studentCourseLearningQuery, {
  SimulateStudentSchemeLearningRequest,
  StudentTrainingResultSimulateRequest,
  StudentTrainingResultSimulateResponse
} from '@api/platform-gateway/student-course-learning-query-back-gateway'
import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { StudentLearningStaticsVo } from '@api/service/management/statisticalReport/query/vo/StudentLearningStaticsVo'
import {
  StudentSchemeLearningRequestVo,
  SyncResultEnmu
} from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
import { SkuPropertyConvertUtils } from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import { Constants } from '@api/service/management/train-class/query/vo/Constants'
import UserModule from '@api/service/management/user/UserModule'
import { Page } from '@hbfe/common'
import SkuPropertyConvertUtilsV2 from '@api/service/management/train-class/Utils/SkuPropertyConvertUtilsV2'
import MsBasicDataQueryBackstageGateway, {
  StudentInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { getStudentInfoInSubProject } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage/graphql-importer'
import QueryDasicdata, {
  BusinessDataDictionaryCodeRequest,
  BusinessDataDictionaryResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
import { getBusinessDataDictionaryInSubProject } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage/graphql-importer'
import studentCourseLearningQueryV2 from '@api/platform-gateway/platform-jxjypxtypt-student-learning-backstage'
import { GraduationStatusEnum } from '@api/service/common/scheme/enum/GraduationStatusEnum'

export class QueryStudentLearningManagerRegionList {
  totalSize = 0
  /**
   * 成果同步对象
   */
  // [SyncResultEnmu.None]: -1,
  syncResult: Record<SyncResultEnmu, number> = {
    [SyncResultEnmu.Unsynchronized]: 0,
    [SyncResultEnmu.Synchronized]: 1,
    [SyncResultEnmu.SynchronizationFailure]: 2,
    [SyncResultEnmu.Waitsynchronized]: 3
  }
  /**
   * 学员学习统计列表
   */
  async listRegionLearningReportFormsInServicer(
    page: Page,
    filter: StudentSchemeLearningRequestVo
  ): Promise<Array<StudentLearningStaticsVo>> {
    try {
      console.log('page参数=', page, 'filter参数=', filter)
      let userIdList: string[] = []
      if (filter.name || filter.idCard || filter.phone) {
        userIdList = await this.getUsers(filter)
        if (!userIdList.length) return []
      }
      const studentLearningRequest = new StudentSchemeLearningRequest()
      Object.assign(studentLearningRequest, StudentSchemeLearningRequestVo)
      studentLearningRequest.student = new UserRequest()
      filter.connectManageSystem = new ConnectManageSystemRequest()
      if (userIdList && userIdList.length) {
        filter.student.userIdList = userIdList
      }
      if (filter.syncResult) {
        filter.connectManageSystem.syncStatus = this.syncResult[filter.syncResult]
      }
      filter.learningRegister.status = [1, 2]
      let res
      if (filter.trainingType == 'trainingWay0001') {
        // 地区网授
        res = await this.pageStudentSchemeLearningInServicerManageRegion(page, filter)
      } else {
        // 地区面授/面网授
        res = await this.pageStudentSchemeLearningDetailInServicerManageRegion(page, filter)
      }
      const tmpArr: StudentLearningStaticsVo[] = []

      const studentNoList = new Array<string>()
      const schemeIds = new Array<string>()
      const orderNoList = new Array<string>()
      const userIds: string[] = []
      await SkuPropertyConvertUtilsV2.queryAllSku(res.data.currentPageData.map((item) => item.scheme.skuProperty))

      if (res.status.isSuccess()) {
        page.totalSize = res.data.totalSize
        for (const tmpArrElement of res.data.currentPageData) {
          if (tmpArrElement?.learningRegister?.orderNo) orderNoList.push(tmpArrElement.learningRegister.orderNo)

          if (tmpArrElement.studentNo && !studentNoList.includes(tmpArrElement.studentNo)) {
            studentNoList.push(tmpArrElement.studentNo)
          }

          if (tmpArrElement.scheme?.schemeId && !schemeIds.includes(tmpArrElement.scheme.schemeId)) {
            schemeIds.push(tmpArrElement.scheme.schemeId)
          }

          const trainClassDetail = await this.convertToTrainClassDetailClassVoV2(
            tmpArrElement as StudentSchemeLearningInOnlineResponse
          )
          trainClassDetail.studentDetail.userId = tmpArrElement.student.userId
          userIds.push(tmpArrElement.student.userId)
          if (tmpArrElement?.studentLearning?.courseLearning?.userAssessResult?.length) {
            ;(trainClassDetail as any).userAssessResult = JSON.parse(
              tmpArrElement?.studentLearning?.courseLearning?.userAssessResult[0]
            )
          }
          tmpArr.push(trainClassDetail)
        }
        const schemeRes = await MsSchemeQueryFrontGatewayCourseLearningBackstage.pageSchemeConfigInServicer({
          page: {
            pageNo: 1,
            pageSize: schemeIds.length
          },
          schemeIds,
          needField: [
            'name',
            'registerBeginDate',
            'registerEndDate',
            'trainingBeginDate',
            'trainingEndDate',
            'type',
            'assessSetting.learningResults',
            'extendProperties'
          ]
        })
        const schemeMap = new Map<string, SchemeConfigResponse>()
        schemeRes.data.currentPageData.forEach((item) => {
          schemeMap.set(item.schemeId, item)
        })
        tmpArr.forEach(async (item) => {
          const scheme = schemeMap.get(item.trainClassBaseInfo.id)
          if (scheme) {
            const configJson = JSON.parse(scheme.schemeConfig)
            item.trainClassBaseInfo.registerBeginDate = configJson.registerBeginDate
            item.trainClassBaseInfo.registerEndDate = configJson.registerEndDate
            item.trainClassBaseInfo.trainingBeginDate = configJson.trainingBeginDate
            item.trainClassBaseInfo.trainingEndDate = configJson.trainingEndDate
            item.trainClassBaseInfo.name = configJson.name
            item.trainClassBaseInfo.schemeType = configJson.type == 'chooseCourseLearning' ? 1 : 2
            item.trainClassBaseInfo.needDataSync = configJson.extendProperties.find((item: any) => {
              return item.name == 'needDataSync'
            })?.value

            if (configJson.assessSetting?.learningResults) {
              const creditResult = configJson.assessSetting.learningResults.find((item: any) => {
                return item.type == 1
              })
              item.trainClassBaseInfo.period = creditResult.grade
            }
          }
        })

        const userRew = new RewriteGraph<StudentInfoResponse, string>(
          MsBasicDataQueryBackstageGateway._commonQuery,
          getStudentInfoInSubProject
        )
        const lsSectionParam: BusinessDataDictionaryCodeRequest[] = []
        const lsSubjectsParam: BusinessDataDictionaryCodeRequest[] = []
        await userRew.request(userIds)
        tmpArr.forEach(async (item) => {
          const temp = userRew.itemMap.get(item.studentDetail.userId)

          if (temp) {
            if (temp.userInfo) {
              item.studentDetail.userName = temp.userInfo.userName
              item.studentDetail.phone = temp.userInfo.phone
              item.studentDetail.idCard = temp.userInfo.idCard
              item.studentDetail.companyName = temp.userInfo.companyName
              item.studentDetail.companyRegion = temp.userInfo.companyRegion
              item.studentDetail.companyRegion = temp.userInfo.companyRegion
              const lsIndustry = temp.userInfo.userIndustryList?.find((item) => item.industryId === IndustryIdEnum.LS)
              if (lsIndustry && lsIndustry.sectionAndSubjects?.length) {
                lsIndustry.sectionAndSubjects.forEach((lsItem) => {
                  if (lsItem.section) {
                    item.studentDetail.lsStudentIndustryInfo.sectionAndSubjectsName.push({
                      sectionCode: lsItem.section,
                      subjectsCode: lsItem.subjects,
                      section: '',
                      subjects: ''
                    })
                    const temp = new BusinessDataDictionaryCodeRequest()
                    temp.code = lsItem.section
                    temp.businessDataDictionaryType = IndustryPropertyCodeEnum.LEARNING_PHASE
                    const temp2 = new BusinessDataDictionaryCodeRequest()
                    temp2.code = lsItem.subjects
                    temp2.businessDataDictionaryType = IndustryPropertyCodeEnum.DISCIPLINE
                    lsSectionParam.push(temp)
                    lsSubjectsParam.push(temp2)
                  }
                })
              }
            }
          }
        })
        if (lsSectionParam.length > 0) {
          const rew = new RewriteGraph<BusinessDataDictionaryResponse, BusinessDataDictionaryCodeRequest>(
            QueryDasicdata._commonQuery,
            getBusinessDataDictionaryInSubProject
          )
          await rew.request(lsSectionParam, 'code')
          tmpArr.forEach((item) => {
            if (item.studentDetail.lsStudentIndustryInfo.sectionAndSubjectsName.length > 0) {
              item.studentDetail.lsStudentIndustryInfo.sectionAndSubjectsName.forEach((lsItem) => {
                lsItem.section = rew.itemMap.get(lsItem.sectionCode).name
              })
            }
          })
        }
        if (lsSubjectsParam.length > 0) {
          const rew = new RewriteGraph<BusinessDataDictionaryResponse, BusinessDataDictionaryCodeRequest>(
            QueryDasicdata._commonQuery,
            getBusinessDataDictionaryInSubProject
          )
          await rew.request(lsSubjectsParam, 'code')
          tmpArr.forEach((item) => {
            if (item.studentDetail.lsStudentIndustryInfo.sectionAndSubjectsName.length > 0) {
              item.studentDetail.lsStudentIndustryInfo.sectionAndSubjectsName.forEach((lsItem) => {
                lsItem.subjects = rew.itemMap.get(lsItem.subjectsCode).name
              })
            }
          })
        }

        const resOrderInfo = new RewriteGraph<OrderResponse, string>(tradeQueryGateway._commonQuery, getOrderInServicer)
        await resOrderInfo.request(orderNoList)
        const regionList = new Array<string>()
        resOrderInfo?.itemMap?.forEach((ite) => {
          if (ite?.buyer?.userArea?.path) regionList.push(...ite.buyer.userArea.path.slice(1).split('/'))
        })
        const regionInfoList = regionList.length
          ? await QueryPhysicalRegion.querRegionDetil([...new Set(regionList)])
          : []
        const findRegion = (region: RegionModel) => {
          const findRegionName = (code: string) => regionInfoList.find((ite) => ite.id === code)?.name
          if (region?.county) {
            return (
              findRegionName(region.province) + '-' + findRegionName(region.city) + '-' + findRegionName(region.county)
            )
          } else if (region?.city) {
            return findRegionName(region.province) + '-' + findRegionName(region.city)
          } else {
            return findRegionName(region.province) || '-'
          }
        }

        // 查询模拟数据
        const simulateRequest = new StudentTrainingResultSimulateRequest()
        simulateRequest.studentNos = studentNoList
        simulateRequest.studentSchemeLearning = new SimulateStudentSchemeLearningRequest()
        simulateRequest.studentSchemeLearning.schemeIds = schemeIds
        const simulateResult = await studentCourseLearningQuery.getStudentTrainingResultSimulateResponseInServicer(
          simulateRequest
        )
        const simulateData =
          (simulateResult?.data?.length && simulateResult.data) || new Array<StudentTrainingResultSimulateResponse>()

        tmpArr.map((item) => {
          const managementUnitRegion = resOrderInfo.itemMap.get(item.orderNo)?.buyer.userArea
          item.managementUnitRegionName = managementUnitRegion ? findRegion(managementUnitRegion) : ''
          // 查找班级模拟数据
          const findSimulateData = simulateData.find(
            (it) => it.studentNo === item.studentNo && it.studentSchemeLearning?.schemeId === item.trainClassBaseInfo.id
          )
          if (findSimulateData) {
            item.setSimulateData(findSimulateData)
          }
        })
      }

      console.log('调用了listRegionLearningReportFormsInServicer方法，返回值=', tmpArr)
      return tmpArr
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，listRegionLearningReportFormsInServicer',
        e
      )
    }
  }
  /**
   * 地区网授
   */
  async pageStudentSchemeLearningInServicerManageRegion(page: Page, filter: StudentSchemeLearningRequestVo) {
    return await schemeLearningMsGateway.pageStudentSchemeLearningInServicerManageRegion({
      page: page,
      request: filter
    })
  }
  /**
   * 地区面授/面网授
   */
  async pageStudentSchemeLearningDetailInServicerManageRegion(page: Page, filter: StudentSchemeLearningRequestVo) {
    return await schemeLearningMsGateway.pageStudentSchemeLearningDetailInServicerManageRegion({
      page: page,
      request: filter
    })
  }

  /**
   * 批量重推学院学习成果
   * @param studentNoList 学号列表
   */
  async batchRePushStudentTraining(studentNoList: Array<string>) {
    const res = await studentCourseLearningQueryV2.rePushStudentTrainingResultToGatewayInServicerV2(studentNoList)

    return res
  }

  async getUsers(filter: StudentSchemeLearningRequestVo) {
    let userIdList: string[] = []

    const queryUser = UserModule.queryUserFactory.queryStudentList
    queryUser.queryStudentIdParams.idCard = filter.idCard
    queryUser.queryStudentIdParams.userName = filter.name
    queryUser.queryStudentIdParams.phone = filter.phone
    const res = await queryUser.queryStudentIdList()
    userIdList = res.data

    return userIdList
  }
  /**
   * 导出
   */

  async exportExcel(param: StudentSchemeLearningRequestVo) {
    try {
      console.log('param参数=', param)
      let userIdList: string[] = []
      if (param.name || param.idCard || param.phone) {
        userIdList = await this.getUsers(param)
      }
      if (userIdList && userIdList.length) {
        param.student.userIdList = userIdList
      }
      param.learningRegister.status = [1, 2]
      if (param.syncResult) {
        param.connectManageSystem.syncStatus = this.syncResult[param.syncResult]
      }
      let res
      if (param.trainingType == 'trainingWay0001') {
        res = this.exportStudentSchemeLearningExcelInServicerManageRegion(param)
      } else if (param.trainingType == 'trainingWay0002') {
        res = await exportMsgateway.exportBlendedStudentSchemeLearningExcelInServicerManageRegion(param)
      } else {
        res = await exportMsgateway.exportFaceToFaceStudentSchemeLearningExcelInServicerManageRegion(param)
      }

      console.log('调用了exportExcel方法，返回值=', res)
      return res
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，exportExcel',
        e
      )
    }
  }

  async exportStudentSchemeLearningExcelInServicerManageRegion(param: StudentSchemeLearningRequestVo) {
    return await exportMsgateway.exportStudentSchemeLearningExcelInServicerManageRegion(param)
  }

  /**
   * 获取培训班属性
   */

  async queryConfig(schemeId: string): Promise<any> {
    try {
      console.log('schemeId参数=', schemeId) //获取培训班配置模板jsonString

      const res = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInServicer({
        schemeId: schemeId,
        needField: [
          'name',
          'registerBeginDate',
          'registerEndDate',
          'trainingBeginDate',
          'trainingEndDate',
          'type',
          'assessSetting.learningResults'
        ]
      })
      let jsonObj

      try {
        jsonObj = JSON.parse(res.data.schemeConfig)
      } catch (e) {
        return ''
      }

      console.log('调用了queryConfig方法，返回值=', jsonObj)
      return jsonObj
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，queryConfig',
        e
      )
    }
  } //转换成Vo对象

  // async convertToTrainClassDetailClassVo(commodityDetail: StudentSchemeLearningResponse) {
  //   try {
  //     console.log('commodityDetail参数=', commodityDetail)
  //     const trainClassDetail = new StudentLearningStaticsVo()

  //     try {
  //       trainClassDetail.trainClassBaseInfo.skuProperty = await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
  //         commodityDetail.scheme.skuProperty
  //       )
  //     } catch (e) {
  //       console.log(e)
  //     }
  //     trainClassDetail.syncStatus = commodityDetail?.connectManageSystem?.syncStatus
  //     trainClassDetail.syncMessage = commodityDetail?.connectManageSystem?.syncMessage
  //     trainClassDetail.studentNo = commodityDetail.studentNo
  //     trainClassDetail.orderNo = commodityDetail.learningRegister.orderNo
  //     trainClassDetail.trainClassBaseInfo.schemeType = Constants.getSchemeType(commodityDetail.scheme.schemeType)
  //     trainClassDetail.trainClassBaseInfo.id = commodityDetail.scheme.schemeId // trainClassDetail.trainClassBaseInfo.qualificationId = commodityDetail.qualificationId
  //     // trainClassDetail.trainClassBaseInfo.studentNo = commodityDetail.studentNo
  //     trainClassDetail.trainClassBaseInfo.qualificationId = commodityDetail.qualificationId
  //     trainClassDetail.userGetLearning.trainingResult = commodityDetail.studentLearning.trainingResult // trainClassDetail.trainClassBaseInfo.trainingResultTime = commodityDetail.studentLearning.trainingResultTime

  //     trainClassDetail.userGetLearning.trainingResultTime = commodityDetail.studentLearning.trainingResultTime
  //     if (commodityDetail.studentLearning.courseLearning) {
  //       trainClassDetail.userGetLearning.selectedCoursePeriod =
  //         commodityDetail.studentLearning.courseLearning.selectedCoursePeriod
  //       trainClassDetail.userGetLearning.courseQualifiedTime =
  //         commodityDetail.studentLearning.courseLearning.courseQualifiedTime
  //     }

  //     if (commodityDetail.studentLearning.examLearning) {
  //       const examLearnRes = commodityDetail.studentLearning.examLearning
  //       trainClassDetail.userGetLearning.committedExam = examLearnRes.committedExam
  //       trainClassDetail.userGetLearning.examQualified = examLearnRes.examAssessResult == 1 ? true : false
  //       trainClassDetail.userGetLearning.examQualifiedTime = examLearnRes.examQualifiedTime
  //       trainClassDetail.userGetLearning.maxExamScore = examLearnRes.maxExamScore || 0
  //       trainClassDetail.userGetLearning.examCount = examLearnRes.examCount
  //     }
  //     if (
  //       commodityDetail.studentLearning.learningExperienceLearning &&
  //       commodityDetail.studentLearning.learningExperienceLearning.userAssessResult &&
  //       commodityDetail.studentLearning.learningExperienceLearning.userAssessResult.length
  //     ) {
  //       const learningExperienceConfig = JSON.parse(
  //         commodityDetail.studentLearning.learningExperienceLearning.userAssessResult[0]
  //       )
  //       if (learningExperienceConfig.lessParticipateLearningExperienceTopicCount) {
  //         trainClassDetail.userGetLearning.learningExperienceRequireCount =
  //           learningExperienceConfig.lessParticipateLearningExperienceTopicCount.config ?? 0
  //         trainClassDetail.userGetLearning.learningExperienceCount =
  //           learningExperienceConfig.lessParticipateLearningExperienceTopicCount.current ?? 0
  //       }
  //     }
  //     if (commodityDetail.studentLearning.learningResult) {
  //       //配置培训证明
  //       const templateResult = commodityDetail.studentLearning.learningResult.find(
  //         (learnResult: LearningResultResponse) => learnResult.learningResultConfig.resultType == 2
  //       )

  //       if (templateResult) {
  //         trainClassDetail.userGetLearning.learningResult.learningResultId = (templateResult.learningResultConfig as CertificateLearningConfigResultResponse).certificateTemplateId
  //         trainClassDetail.userGetLearning.learningResult.learningResultName = ''
  //       } //获取培训成果中学分

  //       const creditLearningResult = commodityDetail.studentLearning.learningResult.find(
  //         (learnResult: LearningResultResponse) => learnResult.learningResultConfig.resultType == 1
  //       )

  //       if (creditLearningResult) {
  //         // trainClassDetail.trainClassBaseInfo.period = (creditLearningResult.learningResultConfig as GradeLearningConfigResultResponse).grade
  //         trainClassDetail.userGetLearning.credit = (creditLearningResult.learningResultConfig as GradeLearningConfigResultResponse).grade
  //       }
  //     }

  //     console.log('调用了convertToTrainClassDetailClassVo方法，返回值=', trainClassDetail)
  //     return trainClassDetail
  //   } catch (e) {
  //     console.log(
  //       '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，convertToTrainClassDetailClassVo',
  //       e
  //     )
  //   }
  // }

  // 转换成Vo对象
  convertToTrainClassDetailClassVoV2(commodityDetail: StudentSchemeLearningInOnlineResponse) {
    try {
      const trainClassDetail = new StudentLearningStaticsVo()

      try {
        const skuProperty = trainClassDetail.trainClassBaseInfo.skuProperty
        const commodityDetailSkuProperty = commodityDetail.scheme.skuProperty
        if (commodityDetailSkuProperty.year) {
          skuProperty.year.skuPropertyValueId = commodityDetailSkuProperty.year.skuPropertyValueId
          skuProperty.year.skuPropertyName = commodityDetailSkuProperty.year.skuPropertyValueId
        }
        // ****************
        const regionId = commodityDetailSkuProperty.province?.skuPropertyValueId
          ? commodityDetailSkuProperty.city?.skuPropertyValueId
            ? commodityDetailSkuProperty.county?.skuPropertyValueId
              ? commodityDetailSkuProperty.province?.skuPropertyValueId +
                '/' +
                commodityDetailSkuProperty.city?.skuPropertyValueId +
                '/' +
                commodityDetailSkuProperty.county?.skuPropertyValueId
              : commodityDetailSkuProperty.province?.skuPropertyValueId +
                '/' +
                commodityDetailSkuProperty.city?.skuPropertyValueId
            : commodityDetailSkuProperty.province?.skuPropertyValueId
          : ''
        skuProperty.region.skuPropertyValueId = regionId
        skuProperty.region.skuPropertyName = regionId
          .split('/')
          .map((item) => SkuPropertyConvertUtilsV2.resigonMap.get(item)?.skuPropertyName)
          .join('/')
        // ****************
        if (commodityDetailSkuProperty.industry) {
          skuProperty.industry.skuPropertyValueId = commodityDetailSkuProperty.industry.skuPropertyValueId
          skuProperty.industry.skuPropertyName = SkuPropertyConvertUtilsV2.industryMap.get(
            commodityDetailSkuProperty.industry.skuPropertyValueId
          ).skuPropertyName
        }
        // ****************
        if (commodityDetailSkuProperty.technicalGrade) {
          skuProperty.technicalGrade.skuPropertyValueId = commodityDetailSkuProperty.technicalGrade.skuPropertyValueId
          skuProperty.technicalGrade.skuPropertyName = SkuPropertyConvertUtilsV2.technologyLevelMap.get(
            commodityDetailSkuProperty.technicalGrade.skuPropertyValueId
          ).skuPropertyName
        }
        // ****************
        if (commodityDetailSkuProperty.subjectType) {
          skuProperty.subjectType.skuPropertyValueId = commodityDetailSkuProperty.subjectType.skuPropertyValueId
          skuProperty.subjectType.skuPropertyName = SkuPropertyConvertUtilsV2.industryChildrenMap
            .get(trainClassDetail.trainClassBaseInfo.skuProperty.industry.skuPropertyValueId)
            .get(commodityDetailSkuProperty.subjectType.skuPropertyValueId).skuPropertyName
        }
        // ****************
        if (commodityDetailSkuProperty.trainingCategory) {
          skuProperty.trainingCategory.skuPropertyValueId =
            commodityDetailSkuProperty.trainingCategory.skuPropertyValueId
          skuProperty.trainingCategory.skuPropertyName = SkuPropertyConvertUtilsV2.industryChildrenMap
            .get(trainClassDetail.trainClassBaseInfo.skuProperty.industry.skuPropertyValueId)
            .get(commodityDetailSkuProperty.trainingCategory.skuPropertyValueId).skuPropertyName
        }
        // ****************
        if (commodityDetailSkuProperty.trainingProfessional) {
          skuProperty.trainingMajor.skuPropertyValueId =
            commodityDetailSkuProperty.trainingProfessional.skuPropertyValueId
          skuProperty.trainingMajor.skuPropertyName = SkuPropertyConvertUtilsV2.industryChildrenMap
            .get(trainClassDetail.trainClassBaseInfo.skuProperty.industry.skuPropertyValueId)
            .get(commodityDetailSkuProperty.trainingProfessional.skuPropertyValueId).skuPropertyName
        }
        // ****************
        if (commodityDetailSkuProperty.positionCategory) {
          skuProperty.positionCategory.skuPropertyValueId =
            commodityDetailSkuProperty.positionCategory.skuPropertyValueId
          skuProperty.positionCategory.skuPropertyName = SkuPropertyConvertUtilsV2.industryChildrenMap
            .get(trainClassDetail.trainClassBaseInfo.skuProperty.industry.skuPropertyValueId)
            .get(commodityDetailSkuProperty.positionCategory.skuPropertyValueId).skuPropertyName
        }
        // ****************
        if (commodityDetailSkuProperty.jobLevel) {
          skuProperty.jobLevel.skuPropertyValueId = commodityDetailSkuProperty.jobLevel.skuPropertyValueId
          skuProperty.jobLevel.skuPropertyName = SkuPropertyConvertUtilsV2.industryChildrenMap
            .get(trainClassDetail.trainClassBaseInfo.skuProperty.industry.skuPropertyValueId)
            .get(commodityDetailSkuProperty.jobLevel.skuPropertyValueId).skuPropertyName
        }
        // ****************
        if (commodityDetailSkuProperty.learningPhase) {
          skuProperty.learningPhase.skuPropertyValueId = commodityDetailSkuProperty.learningPhase.skuPropertyValueId
          skuProperty.learningPhase.skuPropertyName = SkuPropertyConvertUtilsV2.industryChildrenMap
            .get(trainClassDetail.trainClassBaseInfo.skuProperty.industry.skuPropertyValueId)
            .get(commodityDetailSkuProperty.learningPhase.skuPropertyValueId).skuPropertyName
        }
        // ****************
        if (commodityDetailSkuProperty.discipline) {
          skuProperty.discipline.skuPropertyValueId = commodityDetailSkuProperty.discipline.skuPropertyValueId
          skuProperty.discipline.skuPropertyName = SkuPropertyConvertUtilsV2.industryChildrenMap
            .get(trainClassDetail.trainClassBaseInfo.skuProperty.industry.skuPropertyValueId)
            .get(commodityDetailSkuProperty.discipline.skuPropertyValueId).skuPropertyName
        }
        // ****************
        // = await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
        //   commodityDetail.scheme.skuProperty
        // )
      } catch (e) {
        console.log(e)
      }
      trainClassDetail.syncStatus = commodityDetail?.connectManageSystem?.syncStatus
      trainClassDetail.syncMessage = commodityDetail?.connectManageSystem?.syncMessage
      trainClassDetail.studentNo = commodityDetail.studentNo
      trainClassDetail.orderNo = commodityDetail.learningRegister.orderNo
      trainClassDetail.trainClassBaseInfo.schemeType = Constants.getSchemeType(commodityDetail.scheme.schemeType)
      trainClassDetail.trainClassBaseInfo.id = commodityDetail.scheme.schemeId // trainClassDetail.trainClassBaseInfo.qualificationId = commodityDetail.qualificationId
      // trainClassDetail.trainClassBaseInfo.studentNo = commodityDetail.studentNo
      trainClassDetail.trainClassBaseInfo.qualificationId = commodityDetail.qualificationId
      trainClassDetail.userGetLearning.trainingResult = commodityDetail.studentLearning.trainingResult // trainClassDetail.trainClassBaseInfo.trainingResultTime = commodityDetail.studentLearning.trainingResultTime
      trainClassDetail.registerTime = commodityDetail.learningRegister.registerTime

      trainClassDetail.userGetLearning.trainingResultTime = commodityDetail.studentLearning.trainingResultTime
      if (commodityDetail.studentLearning.courseLearning) {
        trainClassDetail.userGetLearning.selectedCoursePeriod =
          commodityDetail.studentLearning.courseLearning.selectedCoursePeriod
        trainClassDetail.userGetLearning.courseQualifiedTime =
          commodityDetail.studentLearning.courseLearning.courseQualifiedTime
        trainClassDetail.userGetLearning.courseQualified =
          commodityDetail.studentLearning.courseLearning.courseScheduleStatus == 2
      }

      if (commodityDetail.studentLearning.examLearning) {
        const examLearnRes = commodityDetail.studentLearning.examLearning
        trainClassDetail.userGetLearning.committedExam = examLearnRes.committedExam
        trainClassDetail.userGetLearning.examQualified = examLearnRes.examAssessResult == 1 ? true : false
        trainClassDetail.userGetLearning.examQualifiedTime = examLearnRes.examQualifiedTime
        trainClassDetail.userGetLearning.maxExamScore = examLearnRes.maxExamScore || 0
        trainClassDetail.userGetLearning.examCount = examLearnRes.examCount
      }
      if (
        commodityDetail.studentLearning.learningExperienceLearning &&
        commodityDetail.studentLearning.learningExperienceLearning.userAssessResult &&
        commodityDetail.studentLearning.learningExperienceLearning.userAssessResult.length
      ) {
        trainClassDetail.learningTypeModel.learningExperience.isExamine = true
        const learningExperienceConfig = JSON.parse(
          commodityDetail.studentLearning.learningExperienceLearning.userAssessResult[0]
        )
        if (learningExperienceConfig.lessParticipateLearningExperienceTopicCount) {
          trainClassDetail.userGetLearning.learningExperienceRequireCount =
            learningExperienceConfig.lessParticipateLearningExperienceTopicCount.config ?? 0
          trainClassDetail.userGetLearning.learningExperienceCount =
            learningExperienceConfig.lessParticipateLearningExperienceTopicCount.current ?? 0
        }
      } else {
        trainClassDetail.userGetLearning.learningExperienceCount =
          commodityDetail.studentLearning.learningExperienceLearning?.learningExperiencePassCount || 0
      }

      if (commodityDetail.studentLearning.learningResult) {
        //配置培训证明
        const templateResult = commodityDetail.studentLearning.learningResult.find(
          (learnResult: LearningResultResponse) => learnResult.learningResultConfig.resultType == 2
        )

        if (templateResult) {
          trainClassDetail.userGetLearning.learningResult.learningResultId = (
            templateResult.learningResultConfig as CertificateLearningConfigResultResponse
          ).certificateTemplateId
          trainClassDetail.userGetLearning.learningResult.learningResultName = ''
        } //获取培训成果中学分

        const creditLearningResult = commodityDetail.studentLearning.learningResult.find(
          (learnResult: LearningResultResponse) => learnResult.learningResultConfig.resultType == 1
        )

        if (creditLearningResult) {
          // trainClassDetail.trainClassBaseInfo.period = (creditLearningResult.learningResultConfig as GradeLearningConfigResultResponse).grade
          trainClassDetail.userGetLearning.credit = (
            creditLearningResult.learningResultConfig as GradeLearningConfigResultResponse
          ).grade
        }
      }

      if (commodityDetail.connectManageSystem) {
        trainClassDetail.connectManageSystem = commodityDetail.connectManageSystem
      }
      // 以下为面网授、面授字段
      trainClassDetail.periodName = commodityDetail.issueName
      trainClassDetail.periodQualificationId = commodityDetail.issueQualificationId
      trainClassDetail.periodStudy.periodId = commodityDetail.issueId
      let json: any
      let issdetail: any
      let haveGradeAccess = false
      if (commodityDetail.schemeConfig) {
        json = JSON.parse(commodityDetail.schemeConfig)

        if (json.issueConfigures && json.issueConfigures.length) {
          issdetail = json.issueConfigures.find((item: any) => item.id == commodityDetail.issueId)
          haveGradeAccess = issdetail?.trainingConfigConfigure?.openCompletionTest
        }
      }

      // 结业测试
      if (commodityDetail.issueTrainingResult?.length) {
        // 结业测试是否合格
        let gradeResult = false
        // 是否开启结业测试
        commodityDetail.issueTrainingResult.map((item) => {
          if (item.gradeType == 'IMPORT_GRADE_RESULT') {
            if (item.grade == 1) {
              gradeResult = true
            }
          }
        })
        if (gradeResult) {
          trainClassDetail.periodStudy.graduationStatus = GraduationStatusEnum.qualified
        } else {
          trainClassDetail.periodStudy.graduationStatus = GraduationStatusEnum.unqualified
        }
      }
      trainClassDetail.graduationAccess = haveGradeAccess
      // 学员考勤
      if (commodityDetail.trainingRequirements?.length) {
        const attendanceResult = JSON.parse(commodityDetail.trainingRequirements[0])
        trainClassDetail.periodStudy.attendance.totalNum = attendanceResult.totalSignCount
        trainClassDetail.periodStudy.attendance.completedNum = attendanceResult.studentSignCount
        trainClassDetail.periodStudy.studentAttendanceRate = attendanceResult.signRate
      }
      // 问卷
      // 期别
      // 需要考核的要求数
      trainClassDetail.periodStudy.questionnaire.totalNum = commodityDetail.questionnaireRequirementCount
      // 需要考核的已提交数
      trainClassDetail.periodStudy.questionnaire.completedNum = commodityDetail.questionnaireSubmittedCount
      // 已提交总数（考核+不考核）
      trainClassDetail.periodStudy.questionnaireCompletedNum =
        (commodityDetail.questionnaireSubmittedCount || 0) + (commodityDetail.questionnaireNoAssessSubmittedCount || 0)
      // 方案
      // 要求数
      trainClassDetail.userGetLearning.questionnaireRequirementCount =
        commodityDetail.schemeQuestionnaireRequirementCount
      // 考核提交
      trainClassDetail.userGetLearning.questionnaireSubmittedCount = commodityDetail.schemeQuestionnaireSubmittedCount
      // 不考核提交
      trainClassDetail.userGetLearning.questionnaireNoAssessSubmittedCount =
        commodityDetail.schemeQuestionnaireNoAssessSubmittedCount
      // 总数
      trainClassDetail.userGetLearning.questionnairTotalCount =
        (commodityDetail.schemeQuestionnaireSubmittedCount || 0) +
        (commodityDetail.schemeQuestionnaireNoAssessSubmittedCount || 0)
      return trainClassDetail
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，convertToTrainClassDetailClassVo',
        e
      )
    }
  }
}
