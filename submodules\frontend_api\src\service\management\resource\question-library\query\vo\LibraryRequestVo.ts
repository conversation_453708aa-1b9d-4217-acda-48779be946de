import { LibraryRequest } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'

class LibraryRequestVo extends LibraryRequest {
  /**
   * 题库ID集合
   */
  libraryIdList?: Array<string> = []
  /**
   * 题库名称
   */
  libraryName?: string = ''
  /**
   * 父题库ID
   */
  parentLibraryId?: string = '-1'
  /**
   * 排除的题库ID集合
   */
  excludeLibraryIdList?: Array<string> = []
  /**
   * 是否可用
   */
  enabled?: boolean = undefined
}
export default LibraryRequestVo
