import findMyQuestionCorrectionResult from './queries/findMyQuestionCorrectionResult.graphql'
import findQuestionCorrectionDone from './queries/findQuestionCorrectionDone.graphql'
import findQuestionCorrectionPage from './queries/findQuestionCorrectionPage.graphql'
import listAllQuestionCorrectionCategory from './queries/listAllQuestionCorrectionCategory.graphql'
import createQuestionCorrection from './mutates/createQuestionCorrection.graphql'
import signQuestionCorrection from './mutates/signQuestionCorrection.graphql'

export {
  findMyQuestionCorrectionResult,
  findQuestionCorrectionDone,
  findQuestionCorrectionPage,
  listAllQuestionCorrectionCategory,
  createQuestionCorrection,
  signQuestionCorrection
}
