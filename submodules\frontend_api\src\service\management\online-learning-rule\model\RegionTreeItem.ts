import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'

export default class RegionTreeItem extends RegionTreeVo {
  /**
   * 是否选中
   */
  checked = false
  /**
   * 子地区
   */
  children: Array<RegionTreeItem> = undefined
  /**
   * 地区名称
   */
  names: Array<string> = []

  /**
   * 等级
   */
  level = 0
  /**
   * 选择排序
   */
  selectSort: number = undefined
  /**
   * 是否服务地区
   */
  isServiceArea = false
  /**
   * 下一级是否全部叶子节点
   */
  get isAllLeaf(): boolean {
    return this.children?.some(item => !item.leaf) || false
  }
  /**
   * 子集是否有选中的值
   */
  get hasChildrenChecked(): boolean {
    return this.children?.some(item => item.checked || item.hasChildrenChecked) || false
  }

  /**
   * 子集名称数组
   */
  get childrenNames(): Array<string> {
    return this.children?.map(item => item.name) || []
  }
}
