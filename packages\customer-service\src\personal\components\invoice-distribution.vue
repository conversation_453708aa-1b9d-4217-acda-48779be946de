<template>
  <el-card shadow="never" class="m-card f-mb15">
    <!--条件查询-->
    <el-row :gutter="16" class="m-query">
      <el-form :inline="true" label-width="auto">
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item label="配送状态">
            <el-select v-model="deliveryInvoiceParam.deliveryStatus" clearable filterable placeholder="请选择">
              <el-option
                v-for="item in deliveryStatusList"
                :label="item.name"
                :value="item.value"
                :key="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item label="订单号">
            <el-input v-model="deliveryInvoiceParam.invoiceNo" clearable placeholder="请输入订单号" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item>
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="restQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <!--表格-->
    <el-table stripe :data="tableData" max-height="500px" class="m-table">
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="订单号" min-width="220" fixed="left">
        <template slot-scope="scope">
          {{ scope.row.associationId }}
          <p><el-tag type="success" size="mini" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag></p>
        </template>
      </el-table-column>
      <el-table-column label="发票号" min-width="120">
        <template slot-scope="scope">{{ scope.row.invoiceNo }}</template>
      </el-table-column>
      <el-table-column label="收件信息" min-width="400">
        <template slot-scope="scope">
          <p class="f-flex" v-if="scope.row.deliveryInfo.shippingMethod === 2">
            <span>收货地址：</span>
            <span class="f-flex-sub">{{ scope.row.deliveryInfo.deliveryAddress ? getAddress(scope.row) : '' }}</span>
          </p>
          <p v-if="scope.row.deliveryInfo.shippingMethod === 2">
            收货人：{{ scope.row.deliveryInfo.deliveryAddress ? scope.row.deliveryInfo.deliveryAddress.consignee : '' }}
          </p>
          <p v-if="scope.row.deliveryInfo.shippingMethod === 1">购买人：{{ scope.row.name }}</p>
          <p v-if="scope.row.deliveryInfo.shippingMethod === 1">身份证号：{{ scope.row.idCard }}</p>
          <p>
            手机号: {{ scope.row.deliveryInfo.deliveryAddress ? scope.row.deliveryInfo.deliveryAddress.phone : '' }}
          </p>
          <p v-if="scope.row.deliveryInfo.shippingMethod === 1">
            自取地址: {{ scope.row.deliveryInfo.takePoint ? scope.row.deliveryInfo.takePoint.pickupLocation : '' }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="配送方式" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.deliveryInfo.shippingMethod === 0">-</div>
          <div v-if="scope.row.deliveryInfo.shippingMethod === 1">自取</div>
          <div v-if="scope.row.deliveryInfo.shippingMethod === 2">邮寄</div>
        </template>
      </el-table-column>
      <el-table-column label="配送状态" min-width="130">
        <template slot-scope="scope">
          <div v-if="scope.row.deliveryInfo.deliveryStatus === 1">
            <el-badge is-dot type="primary" class="badge-status">就绪</el-badge>
          </div>
          <div v-if="scope.row.deliveryInfo.deliveryStatus === 0">
            <el-badge is-dot type="info" class="badge-status">未就绪</el-badge>
          </div>
          <div v-if="scope.row.deliveryInfo.deliveryStatus === 3 || scope.row.deliveryInfo.deliveryStatus === 2">
            <el-badge is-dot type="success" class="badge-status">已配送</el-badge>
          </div>
          <!-- <div v-if="scope.row.deliveryInfo.deliveryStatus === 2">
              <el-badge is-dot type="success" class="badge-status">已自取</el-badge>
            </div> -->
        </template>
      </el-table-column>
      <el-table-column label="配送信息 / 取件信息" min-width="260">
        <template slot-scope="scope">
          <div v-if="scope.row.deliveryInfo.shippingMethod === 1 && scope.row.deliveryInfo.deliveryStatus === 2">
            <p>领取人: {{ scope.row.deliveryInfo.takeResult.takePerson }}</p>
            <p>手机号: {{ scope.row.deliveryInfo.takeResult.phone }}</p>
            <p>取货时间: {{ scope.row.taken }}</p>
          </div>
          <div v-if="scope.row.deliveryInfo.shippingMethod === 2 && scope.row.deliveryInfo.deliveryStatus === 3">
            <p>快递公司: {{ scope.row.deliveryInfo.express.expressCompanyName }}</p>
            <p>运单号: {{ scope.row.deliveryInfo.express.expressNo }}</p>
            <p>发货时间: {{ scope.row.shipped }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否冻结" min-width="120">
        <template slot-scope="scope">{{ scope.row.invoiceFreezeStatus ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column label="操作" width="140" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            v-if="
              scope.row.deliveryInfo.shippingMethod === 1 &&
              !scope.row.invoiceFreezeStatus &&
              scope.row.deliveryInfo.deliveryStatus != 0 &&
              scope.row.deliveryInfo.deliveryStatus != 2
            "
            @click="isShowDeliveryDialog(scope.row, 1)"
            >确认自取</el-button
          >
          <el-button
            type="text"
            size="mini"
            v-if="
              scope.row.deliveryInfo.shippingMethod === 2 &&
              !scope.row.invoiceFreezeStatus &&
              scope.row.deliveryInfo.deliveryStatus != 0 &&
              scope.row.deliveryInfo.deliveryStatus != 3
            "
            @click="isShowDeliveryDialog(scope.row, 2)"
            >确认配送</el-button
          >
          <el-button
            type="text"
            size="mini"
            class="fontColor"
            v-if="scope.row.deliveryInfo.deliveryStatus === 3 && !scope.row.invoiceFreezeStatus"
            >已配送</el-button
          >
          <el-button
            type="text"
            size="mini"
            class="fontColor"
            v-if="scope.row.deliveryInfo.deliveryStatus === 2 && !scope.row.invoiceFreezeStatus"
            >已自取</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="page" v-bind="page"></hb-pagination>
    <delivery-dialog
      :dialogType="dialogType"
      @confirmOrder="confirmOrder"
      @confirmDeliveryOrder="confirmDeliveryOrder"
      ref="deliveryDialog"
    ></delivery-dialog>
    <import-parcel-waybill
      ref="importParcelWaybill"
      :import-dialog.sync="importInvoiceVisible"
      @importSuccess="importSuccessVisible = true"
    ></import-parcel-waybill>
  </el-card>
</template>
<script lang="ts">
  import { Component, Vue, Ref, Prop, Watch } from 'vue-property-decorator'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { UiPage } from '@hbfe/common'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import DeliveryInvoiceParamVo from '@api/service/management/trade/single/invoice/query/vo/DeliveryInvoiceParam'
  import {
    DeliveryStatusEnum,
    DeliveryWayEnum
  } from '@api/service/management/trade/single/invoice/enum/DeliveryInvoiceEnum'
  import DeliveryDialog from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/delivery-dialog.vue'
  import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
  import ImportParcelWaybill from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/import-parcel-waybill.vue'
  import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
  import { RegionResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import QueryDeliveryRegion from '@api/service/common/basic-data-dictionary/query/QueryDeliveryRegion'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'

  @Component({
    components: { DoubleDatePicker, DeliveryDialog, ImportParcelWaybill }
  })
  export default class extends Vue {
    @Ref('deliveryDialog') deliveryDialog: DeliveryDialog
    @Ref('importParcelWaybill') importParcelWaybill: ImportParcelWaybill
    // 发票配送查询实例
    queryDeliveryInvoiceVo = TradeModule.singleTradeBatchFactor.invoiceFactor.queryDeliveryInvoice
    // // 发票配送业务实例
    mutationDeliveryInvoiceVo = TradeModule.singleTradeBatchFactor.invoiceFactor.mutationDeliveryInvoice
    loading = false
    deliveryInvoiceParam = new DeliveryInvoiceParamVo()
    // 弹窗类型
    dialogType = 0
    // 选中的发票详情
    checkedItemDetail = new OffLinePageInvoiceVo()
    //导出成功弹窗
    exportSuccessVisible = false
    //导入弹窗
    importInvoiceVisible = false
    //导入成功弹窗
    importSuccessVisible = false
    // 订单来源枚举
    SaleChannelEnum = SaleChannelEnum
    // 用户id
    userId = ''
    // 配送状态
    deliveryStatusList = [
      { name: '未就绪', value: DeliveryStatusEnum.NOTREADY },
      { name: '就绪', value: DeliveryStatusEnum.READY },
      { name: '已配送', value: DeliveryStatusEnum.DELIVERED }
    ]
    // 配送状态
    deliveryWayList = [
      { name: '快递', value: DeliveryWayEnum.COURIER },
      { name: '自取', value: DeliveryWayEnum.SELFFETCHED }
    ]
    deliveryWay = DeliveryWayEnum
    tableData = new Array<OffLinePageInvoiceVo>()
    page: UiPage
    regionList: Array<RegionResponse> = new Array<RegionResponse>()
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }

    @Prop({
      type: UserDetailVo,
      default: new UserDetailVo()
    })
    userData!: UserDetailVo
    @Watch('userData', {
      deep: true,
      immediate: true
    })
    async userDataChange({ userName, idCard, userId, loginAccount }: UserDetailVo) {
      this.deliveryInvoiceParam.name = userName
      this.deliveryInvoiceParam.idCard = idCard
      this.deliveryInvoiceParam.loginAccount = loginAccount
      this.userId = userId
      if (userName || idCard || loginAccount) {
        await this.search()
      } else {
        this.tableData = []
      }
    }

    // 列表请求
    async doQueryPage() {
      if (!this.userId) {
        return
      }
      try {
        this.loading = true
        this.tableData = await this.queryDeliveryInvoiceVo.queryPageDeliveryInvoice(
          this.page,
          this.deliveryInvoiceParam
        )
        console.log(this.tableData, 'this.tableData')
        const regionIdList = new Array<string>()
        this.tableData.map((item) => {
          const region = item.deliveryInfo?.deliveryAddress?.region || ''
          regionIdList.push(...region.split('/').filter((item) => item))
        })
        this.regionList = await QueryDeliveryRegion.querRegionDetil([...new Set(regionIdList)])
      } catch (e) {
        this.$message.error('请求列表失败！')
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    // 重置条件
    async restQuery() {
      this.deliveryInvoiceParam = new DeliveryInvoiceParamVo()
      this.doQueryPage()
    }

    // 是否显示弹窗
    isShowDeliveryDialog(item: any, type: number) {
      this.checkedItemDetail = item
      this.dialogType = type
      this.deliveryDialog.isShowDialog()
    }

    // 确认自取
    async confirmOrder(item: any) {
      console.log(item, 'item')
      item.offlineInvoiceId = this.checkedItemDetail.invoiceId
      const res = await this.mutationDeliveryInvoiceVo.confirmStatus(item, this.deliveryWay.SELFFETCHED)
      if (res.isSuccess()) {
        this.$message.success('确认自取成功')
        this.deliveryDialog.isShowDialog()
        this.doQueryPage()
      } else {
        this.$message.warning('请求失败！')
      }
    }

    // 确认配送
    async confirmDeliveryOrder(item: any) {
      console.log(item, 'item')
      item.offlineInvoiceId = this.checkedItemDetail.invoiceId
      const res = await this.mutationDeliveryInvoiceVo.confirmStatus(item, this.deliveryWay.COURIER)
      if (res.isSuccess()) {
        this.$message.success('确认配送成功')
        this.deliveryDialog.isShowDialog()
        this.doQueryPage()
      } else {
        this.$message.warning('请求失败！')
      }
    }

    async search() {
      this.page.pageNo = 1
      await this.doQueryPage()
    }

    async created() {
      this.deliveryInvoiceParam.deliveryStatus = DeliveryStatusEnum.READY
      //   await this.doQueryPage()
    }

    // 前往下载
    goDownloadPage() {
      this.$router.push('/training/task/export')
      this.exportSuccessVisible = false
    }

    // 导出信息
    async exportFile() {
      const res = await this.queryDeliveryInvoiceVo.exportPageDeliveryInvoice(this.deliveryInvoiceParam)
      if (res) {
        this.$message.success('导出成功')
        this.exportSuccessVisible = true
      } else {
        this.$message.warning('导出失败')
      }
    }

    // 导入
    importFile() {
      this.importInvoiceVisible = true
    }

    // 下载导入数据
    goImportDownloadPage() {
      this.importSuccessVisible = false
      this.$router.push('/training/task/importtask')
    }
    // 获取收货地址
    getAddress(item: OffLinePageInvoiceVo) {
      if (item.deliveryInfo?.deliveryAddress?.region) {
        const regionNameList = new Array<string>()
        item.deliveryInfo?.deliveryAddress?.region?.split('/').map((ite) => {
          const regionName = this.regionList?.find((it) => it.code === ite)
          if (regionName) regionNameList.push(regionName.name)
        })
        return regionNameList.join('') + item.deliveryInfo?.deliveryAddress?.address
      }
      return item.deliveryInfo?.deliveryAddress?.address || ''
    }
  }
</script>
