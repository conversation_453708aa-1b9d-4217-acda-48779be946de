<route-params content="/:id"></route-params>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/course')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/course' }">课程管理</el-breadcrumb-item>
      <el-breadcrumb-item>课程详情</el-breadcrumb-item>
    </el-breadcrumb>
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="课程详情" name="detail">
        <detail :course-id="$route.params.id"></detail>
      </el-tab-pane>
      <el-tab-pane label="课程评价" name="evaluation">
        <evaluation></evaluation>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import Detail from '@hbfe/jxjy-admin-course/src/components/detail.vue'
  import Evaluation from '@hbfe/jxjy-admin-course/src/components/evaluation.vue'

  @Component({
    components: { Detail, Evaluation }
  })
  export default class extends Vue {
    activeName = 'detail'

    created() {
      if (this.$route.query.detail && this.$route.query.detail === 'evaluation') {
        this.activeName = 'evaluation'
      }
    }
  }
</script>
