import MsTradeConfigurationGateway, {
  OperateEITaxpayerUserLogonConfigRequest
} from '@api/ms-gateway/ms-trade-configuration-v1'
import { Response, ResponseStatus } from '@hbfe/common'
import EleInvoiceVo from './vo/common/EleInvoiceVo'
import ElectronicInvoiceUpdateVo from './vo/ElectronicInvoiceUpdateVo'
import ElectronicInvoiceCreateVo from './vo/ElectronicInvoiceCreateVo'
import { InvoiceProviderEnum } from '@api/service/common/enums/online-school-config/InvoiceProviderTypes'

/*
 *增值税电子票
 */
class MutationElectronicInvoice {
  // 电子发票请求入参
  eleInvoiceParams = new EleInvoiceVo()

  /**
   * @description: 创建增值税电子发票
   */
  async doCreateElectronicInvoice(): Promise<Response<string>> {
    const temp = new ElectronicInvoiceCreateVo()
    let param
    //rate特殊处理，前端输入的是6，传给后端的要是0.06
    const rate = this.eleInvoiceParams.rate / 100
    if (this.eleInvoiceParams.invoiceProviderId === InvoiceProviderEnum.BWJF) {
      param = Object.assign(temp, this.eleInvoiceParams, { rate }).toBWJFDto()
    } else if (
      this.eleInvoiceParams.invoiceProviderId === InvoiceProviderEnum.NST ||
      this.eleInvoiceParams.invoiceProviderId === InvoiceProviderEnum.NSTQDP
    ) {
      param = Object.assign(temp, this.eleInvoiceParams, { rate }).toNST()
    } else {
      param = Object.assign(temp, this.eleInvoiceParams, { rate }).toDto()
    }
    const res = await MsTradeConfigurationGateway.createElectronicInvoiceTaxpayer(param)
    const response = new Response<string>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }

    if (this.eleInvoiceParams.invoiceProviderId === InvoiceProviderEnum.BWJF) {
      const loginConfigRes = await this.setBWJFTaxLoginConfig(res.data)

      if (!loginConfigRes.status?.isSuccess()) {
        response.status = loginConfigRes.status
        return response
      }
    }

    response.status = res.status
    response.data = res.data
    return response
  }

  /**
   * @description: 修改增值税电子发票
   */
  async doUpdateElectronicInvoice(): Promise<ResponseStatus> {
    const temp = new ElectronicInvoiceUpdateVo()
    let params
    //rate特殊处理，前端输入的是6，传给后端的要是0.06
    const rate = this.eleInvoiceParams.rate / 100
    if (this.eleInvoiceParams.invoiceProviderId === InvoiceProviderEnum.BWJF) {
      params = Object.assign(temp, this.eleInvoiceParams, { rate }).toBWJFDto()
    } else if (
      this.eleInvoiceParams.invoiceProviderId === InvoiceProviderEnum.NST ||
      this.eleInvoiceParams.invoiceProviderId === InvoiceProviderEnum.NSTQDP
    ) {
      params = Object.assign(temp, this.eleInvoiceParams, { rate }).toNST()
    } else {
      params = Object.assign(temp, this.eleInvoiceParams, { rate }).toDto()
    }
    const { status } = await MsTradeConfigurationGateway.updateElectronicInvoiceTaxpayer(params)

    if (status?.isSuccess() && this.eleInvoiceParams.invoiceProviderId === InvoiceProviderEnum.BWJF) {
      // 获取网校纳税人id
      const taxIdRes = await MsTradeConfigurationGateway.findElectronicInvoiceTaxpayerList()
      if (taxIdRes?.data?.length) {
        const loginConfigRes = await this.setBWJFTaxLoginConfig(taxIdRes.data[0])
        if (!loginConfigRes?.status?.isSuccess()) {
          return loginConfigRes?.status
        }
      }
    }

    return status
  }

  /**
   * 设置百旺税局登录账号密码
   * @param taxpayerId 纳税人id
   */
  private async setBWJFTaxLoginConfig(taxpayerId: string) {
    const createLoginConfigParam = new OperateEITaxpayerUserLogonConfigRequest()
    createLoginConfigParam.taxpayerId = taxpayerId
    createLoginConfigParam.taxpayerNo = this.eleInvoiceParams.taxpayerNo
    createLoginConfigParam.loginAccount = this.eleInvoiceParams.taxLoginAccount
    createLoginConfigParam.loginPassword = this.eleInvoiceParams.taxLoginPassword

    const res = await MsTradeConfigurationGateway.operateUserLoginConfig(createLoginConfigParam)

    return res
  }
}

export default MutationElectronicInvoice
