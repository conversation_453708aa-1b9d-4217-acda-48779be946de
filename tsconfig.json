{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "strict": true,
    // 启用 vuex-class 需要开启此选项
    "strictFunctionTypes": false,
    "jsx": "preserve",
    "importHelpers": true,
    "noImplicitThis": false,
    "strictPropertyInitialization": false,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "suppressImplicitAnyIndexErrors": true,
    "esModuleInterop": true,
    "noImplicitAny": true,
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "allowJs": true,
    "strictNullChecks": false,
    "baseUrl": ".",
    "types": [
      "webpack-env"
    ],
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@packages/*": [
        "packages/*"
      ],
      "@api/*": [
        "submodules/frontend_api/src/*"
      ]
    },
    "lib": [
      "esnext",
      "dom",
      "dom.iterable",
      "scripthost"
    ]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "packages/**/*.ts",
    "packages/**/*.tsx",
    "packages/**/*.vue",
    "tests/**/*.ts",
    "tests/**/*.tsx"
  ],
  "exclude": [
    "node_modules"
  ]
}
