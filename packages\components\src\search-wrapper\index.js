import './index.scss'
import { merge } from 'lodash'

export default {
  props: {
    size: {
      type: String,
      default: 'medium'
    },
    expand: {
      type: Boolean,
      default: process.env.NODE_ENV === 'development'
    },
    model: {
      type: Object,
      default() {
        return {}
      }
    },
    reset: {
      type: Boolean,
      default: true
    },
    responsiveProps: {
      type: Object,
      default() {
        return {
          sm: 12,
          md: 8,
          xl: 6
        }
      }
    }
  },
  data() {
    return {
      advanced: this.expand,
      defaultNum: 0
    }
  },
  computed: {
    advancedClass() {
      return this.advanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
    },
    advancedText() {
      return this.advanced ? '收起' : '展开'
    }
  },
  mounted() {
    this.getWindowWidth()
    window.onresize = () => {
      return this.getWindowWidth()
    }
  },
  methods: {
    showAdvanced() {
      this.advanced = !this.advanced
    },
    resetFiles() {
      this.$refs.searchForm.resetFields()
      this.$emit('reset', this.$refs.searchForm)
    },
    getWindowWidth() {
      this.defaultNum =
        document.documentElement.clientWidth >= 1800
          ? 2
          : 1400 <= document.documentElement.clientWidth && document.documentElement.clientWidth < 1800
          ? 1
          : 0
    }
  },
  render() {
    const defaults = []
    const responsiveProps = {
      props: this.responsiveProps
    }
    this.$slots.default = this.$slots?.default?.filter((item) => {
      return item.tag
    })
    this.$slots.default.forEach((vNode, index) => {
      const itemProps = merge({}, responsiveProps, { props: vNode.data?.attrs })
      if (index <= this.defaultNum) defaults.push(<el-col {...itemProps}>{vNode}</el-col>)
    })
    const advanced = []
    if (this.$slots.default.length > this.defaultNum) {
      this.$slots.default.forEach((vNode, index) => {
        const itemProps = merge({}, responsiveProps, { props: vNode.data?.attrs, class: 'advanced-item' })
        if (index > this.defaultNum) advanced.push(<el-col {...itemProps}>{vNode}</el-col>)
      })
    }
    // console.log(defaults, advanced)
    let actionResponsive = {
      props: {
        sm: 12,
        md: 8,
        xl: 6
      }
    }
    if (this.$slots.actions?.length === 1) {
      actionResponsive = merge({}, actionResponsive, { props: this.$slots.actions[0].data?.attrs })
    }
    if (this.advanced) {
      actionResponsive.props.xl = 6
      actionResponsive.attrs = {
        style: 'text-align: right;'
      }
    }

    if (this.$slots.default.length < 3 && this.advanced === false) {
      actionResponsive.attrs = {
        style: 'text-align: right;float: left;'
      }
    }
    return (
      <div class="search-form-wrapper" {...{ class: this.advanced ? 'show-advanced' : '' }}>
        <el-row gutter={16}>
          <el-form ref="searchForm" {...{ props: this.$props }} label-width="auto" inline={true}>
            {defaults}
            {advanced}
            <el-col {...actionResponsive} class="f-fr">
              <el-form-item
                class="f-fr"
                {...{ style: this.$slots.default.length < 3 && this.advanced === false ? 'float: left;' : '' }}
              >
                {this.$slots.actions}
                {this.reset ? <el-button onClick={this.resetFiles}>重置</el-button> : ''}
                {advanced.length ? (
                  <el-button type="text" onClick={this.showAdvanced}>
                    {this.advancedText}
                    <i class={this.advancedClass} />
                  </el-button>
                ) : (
                  ''
                )}
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </div>
    )
  }
}
