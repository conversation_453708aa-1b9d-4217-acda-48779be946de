[{"name": "super", "meta": {"title": "超管", "isMenu": true, "ownerGroup": ["super"], "roles": ["ZXMGLY"], "permissionMap": {"query": {"name": "查询(必选)", "ownerGroup": ["super.query"], "graphql": ["ms-config-v1.query.getCurrentFrontendConfig:{\"serviceName\":\"ms-config-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForServiceByDomainName:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.query.getDistributionService:{\"authorizationRequired\":false}", "ms-identity-authentication-v1.query.getAccessTokenValue:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getAdminInfoInMyself:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjy-distributor-admin-v1.query.isOnlineSchoolContractExpired:{\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.isOnlineSchoolContractExpired:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.query.findFunctionalAuthorityByRoleIdsNew:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.query.findRoleByAccountId:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-autolearning-online-school-smart-learning-service-v1.query.queryOnlineSchoolSmartLearningServiceConfigByServicerId:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applyCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptchaForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCodeForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwd:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptcha:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountId:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCode:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-account-v1.mutation.changeAuthorizationUnitInfoList:{\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applySmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validSmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentUser:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.validCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getOnlineSchoolConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getSmsCodeAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getFailedAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.forgetPassword:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCoursePreview:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "platform-training-channel-v1.mutation.updateSaleSetting:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursePackageInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseInSchemeInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageCoursewareSupplierInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyChangeIdentityAuthentication:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticate:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyProxyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticateBasic:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryPayFlowStatus:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-payment-v1.mutation.getBatchPreParePayResult:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryQrScanPromptByPayFlowNo:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.deliveredOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":true}", "ms-assess-v1.mutation.batchRePushSchemeIndicator:{\"serviceName\":\"ms-assess-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.createJxjyCommonMenu:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.temporaryOrderUpdateOrderInfoInSubject:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.enableOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForService:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.tryComparePhoto:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.updateUserDatumCodePhoto:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.refreshConfigAndUpdate:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.executeAntiInspection:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":false}", "platform-account-v1.mutation.compensatingStudentAccount:{\"serviceName\":\"platform-account-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.mutation.openDistributionService:{\"authorizationRequired\":false}", "ms-order-repair-data-v1.mutation.orderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-repair-data-v1.mutation.batchOrderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.distributionAuthBatchProduct:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.createPricePlan:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.pricePlanSetSaleChannel:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissReturnOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissExchangeOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.clearStudentLearningRule:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-teachingplan-studentattendance-sds-v1.mutation.oneClickClock:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}}, "sort": "1"}, "specifier": "super", "path": "", "pathSegments": ["super"], "children": [{"name": "basic-data", "specifier": "BasicData", "path": "/basic-data", "pathSegments": ["super", "basic-data"], "component": "@/packages/routers/src/basic-router/basic-data.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "系统基础配置", "sort": 2, "icon": "icon-pei<PERSON>", "roles": [], "permissionMap": {}, "ownerGroup": ["super.basic-data"]}, "children": [{"name": "basic-data-account", "specifier": "BasicDataAccount", "path": "account", "pathSegments": ["super", "basic-data", "account"], "component": "@/packages/routers/src/basic-router/basic-data/account.vue", "meta": {"isMenu": true, "title": "运营帐号管理", "sort": 5, "icon": "icon-y<PERSON><PERSON><PERSON><PERSON>", "roles": [], "permissionMap": {}, "ownerGroup": ["super.basic-data.account"]}, "children": []}, {"name": "basic-data-platform", "specifier": "BasicDataPlatform", "path": "platform", "pathSegments": ["super", "basic-data", "platform"], "component": "@/packages/routers/src/basic-router/basic-data/platform.vue", "meta": {"isMenu": true, "title": "网校配置", "sort": 1, "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON>", "roles": [], "permissionMap": {}, "ownerGroup": ["super.basic-data.platform"]}, "children": [{"name": "basic-data-platform-operation-log-index", "specifier": "BasicDataPlatformOperationLogIndex", "path": "operation-log", "pathSegments": ["super", "basic-data", "platform", "operation-log"], "component": "@/packages/routers/src/basic-router/basic-data/platform/operation-log/index.vue", "meta": {"isMenu": true, "title": "操作日志", "sort": 3, "icon": "icon_guanli", "roles": [], "permissionMap": {}, "ownerGroup": ["super.basic-data.platform.operation-log"]}}]}, {"name": "basic-data-trade", "specifier": "BasicDataTrade", "path": "trade", "pathSegments": ["super", "basic-data", "trade"], "component": "@/packages/routers/src/basic-router/basic-data/trade.vue", "meta": {"isMenu": true, "title": "交易信息配置", "sort": 3, "icon": "icon-xuanzelei<PERSON>", "roles": [], "permissionMap": {}, "ownerGroup": ["super.basic-data.trade"]}, "children": []}]}, {"name": "home", "specifier": "Home", "path": "/home", "pathSegments": ["super", "home"], "component": "@/packages/routers/src/basic-router/home.vue", "meta": {"openWhenInit": true, "closeAble": false, "isMenu": true, "title": "首页", "sort": 1, "icon": "icon-shouye", "roles": ["ZXMGLY"], "permissionMap": {}, "ownerGroup": ["super.home"]}, "children": [{"name": "home-person-info-index", "specifier": "HomePersonInfoIndex", "path": "person-info", "pathSegments": ["super", "home", "person-info"], "component": "@/packages/routers/src/basic-router/home/<USER>/index.vue", "meta": {"isMenu": true, "onlyShowOnTab": true, "title": "个人帐号设置", "sort": 2, "hideMenu": true, "icon": "icon_guanli", "roles": ["ZXMGLY"], "permissionMap": {"personalInfo": {"name": "个人账号设置", "ownerGroup": ["super.home.person-info.personalInfo"], "graphql": ["ms-basicdata-domain-gateway-v1.mutation.applyCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.query.findRoleByAccountId:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["ZXMGLY"]}, "editName": {"name": "修改姓名", "ownerGroup": ["super.home.person-info.editName"], "graphql": ["ms-basicdata-domain-gateway-v1.mutation.updateAdministratorAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["ZXMGLY"]}, "changePassword": {"name": "修改密码", "ownerGroup": ["super.home.person-info.changePassword"], "graphql": ["ms-basicdata-domain-gateway-v1.mutation.changePasswordByCurrent:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["ZXMGLY"]}, "getChangePhoneCapture": {"name": "获取短信验证码", "ownerGroup": ["super.home.person-info.getChangePhoneCapture"], "graphql": ["ms-basicdata-domain-gateway-v1.mutation.applyCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applySmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}, "submitChange": {"name": "确认提交手机换绑", "ownerGroup": ["super.home.person-info.submitChange"], "graphql": ["ms-basicdata-domain-gateway-v1.mutation.validSmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentUser:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.changeNewPhone:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["ZXMGLY"]}, "getEditPhoneCapture": {"name": "获取短信验证码", "ownerGroup": ["super.home.person-info.getEditPhoneCapture"], "graphql": ["ms-basicdata-domain-gateway-v1.mutation.applyCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applySmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}, "submitEdit": {"name": "确认提交手机修改", "ownerGroup": ["super.home.person-info.submitEdit"], "graphql": ["ms-basicdata-domain-gateway-v1.mutation.validSmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentUser:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.changeNewPhone:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["ZXMGLY"]}}, "ownerGroup": ["super.home.person-info"]}}]}, {"name": "marketing-center", "specifier": "MarketingCenter", "path": "/marketing-center", "pathSegments": ["super", "marketing-center"], "component": "@/packages/routers/src/basic-router/marketing-center.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "营销中心", "sort": 6, "icon": "icon-shu<PERSON>", "roles": [], "permissionMap": {}, "ownerGroup": ["super.marketing-center"]}, "children": []}, {"name": "resource", "specifier": "Resource", "path": "/resource", "pathSegments": ["super", "resource"], "component": "@/packages/routers/src/basic-router/resource.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "教学资源管理", "sort": 3, "icon": "icon-kecheng", "roles": [], "permissionMap": {}, "ownerGroup": ["super.resource"]}, "children": [{"name": "resource-courseware-index", "specifier": "ResourceCoursewareIndex", "path": "courseware", "pathSegments": ["super", "resource", "courseware"], "component": "@/packages/routers/src/basic-router/resource/courseware/index.vue", "meta": {"isMenu": true, "title": "课件管理", "sort": 1, "icon": "icon-kejian", "roles": [], "permissionMap": {}, "ownerGroup": ["super.resource.courseware"]}}, {"name": "resource-question-library-index", "specifier": "ResourceQuestionLibraryIndex", "path": "question-library", "pathSegments": ["super", "resource", "question-library"], "component": "@/packages/routers/src/basic-router/resource/question-library/index.vue", "meta": {"isMenu": true, "title": "题库管理", "sort": 4, "icon": "icon-tiku", "roles": [], "permissionMap": {}, "ownerGroup": ["super.resource.question-library"]}}]}, {"name": "school-management", "specifier": "SchoolManagement", "path": "/school-management", "pathSegments": ["super", "school-management"], "component": "@/packages/routers/src/basic-router/school-management.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "网校管理", "sort": 6, "icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON>", "roles": ["ZXMGLY"], "permissionMap": {}, "ownerGroup": ["super.school-management"]}, "children": [{"name": "school-management-management", "specifier": "SchoolManagementManagement", "path": "management", "pathSegments": ["super", "school-management", "management"], "component": "@/packages/routers/src/basic-router/school-management/management.vue", "meta": {"isMenu": true, "title": "网校管理", "sort": 2, "icon": "icon-da<PERSON>u", "roles": ["ZXMGLY"], "permissionMap": {"query": {"name": "查询", "ownerGroup": ["super.school-management.management.query"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageOnlineSchoolInfoResponseInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getOnlineSchoolInfoCount:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}, "update": {"name": "修改", "ownerGroup": ["super.school-management.management.update"], "graphql": [], "roles": ["ZXMGLY"]}, "modifyQuery": {"name": "查询", "ownerGroup": ["super.school-management.management.modifyQuery"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getOnlineSchoolInfoResponseInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-autolearning-online-school-smart-learning-service-v1.query.queryOnlineSchoolSmartLearningServiceConfigByServicerId:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "platform-supplement-study-rules-setting-v1.query.getSupplementStudyRuleSettingByServiceId:{\"authorizationRequired\":true}", "platform-jxjypxtypt-distribution-service-v1.query.getDistributionService:{\"authorizationRequired\":false}"], "roles": []}, "BasicInfo": {"name": "修改基础信息", "ownerGroup": ["super.school-management.management.BasicInfo"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.updateOSBasicInfo:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}"], "roles": []}, "SchoolConfig": {"name": "修改网校配置", "ownerGroup": ["super.school-management.management.SchoolConfig"], "graphql": ["ms-servicercontract-v1.mutation.updateOnlineSchool:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}"], "roles": []}, "TemplateConfig": {"name": "修改模板配置", "ownerGroup": ["super.school-management.management.TemplateConfig"], "graphql": ["ms-servicercontract-v1.mutation.updateOSTemplate:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}"], "roles": []}, "ManageAccount": {"name": "修改管理员", "ownerGroup": ["super.school-management.management.ManageAccount"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageOnlineSchoolAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateOnlineAdministratorAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.ResetAdminPasswordWithResponse:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.freezeAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.resumeAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": []}, "AddService": {"name": "增值服务", "ownerGroup": ["super.school-management.management.AddService"], "graphql": ["ms-servicercontract-v1.mutation.updateOSBasicInfo:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}"], "roles": []}}, "ownerGroup": ["super.school-management.management"]}}, {"name": "school-management-modify", "specifier": "SchoolManagementModify", "path": "modify/:schoolId", "pathSegments": ["super", "school-management", "modify"], "component": "@/packages/routers/src/basic-router/school-management/modify.vue", "meta": {"isMenu": true, "title": "修改网校", "hideMenu": true, "roles": ["ZXMGLY"], "permissionMap": {"modifyQuery": {"name": "查询", "ownerGroup": ["super.school-management.modify.modifyQuery"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getOnlineSchoolInfoResponseInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-autolearning-online-school-smart-learning-service-v1.query.queryOnlineSchoolSmartLearningServiceConfigByServicerId:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "platform-supplement-study-rules-setting-v1.query.getSupplementStudyRuleSettingByServiceId:{\"authorizationRequired\":true}", "platform-jxjypxtypt-distribution-service-v1.query.getDistributionService:{\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}, "BasicInfo": {"name": "修改基础信息", "ownerGroup": ["super.school-management.modify.BasicInfo"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.updateOSBasicInfo:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}, "SchoolConfig": {"name": "修改网校配置", "ownerGroup": ["super.school-management.modify.SchoolConfig"], "graphql": ["ms-servicercontract-v1.mutation.updateOnlineSchool:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}, "TemplateConfig": {"name": "修改模板配置", "ownerGroup": ["super.school-management.modify.TemplateConfig"], "graphql": ["ms-servicercontract-v1.mutation.updateOSTemplate:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}, "ManageAccount": {"name": "修改管理员", "ownerGroup": ["super.school-management.modify.ManageAccount"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageOnlineSchoolAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateOnlineAdministratorAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.ResetAdminPasswordWithResponse:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.freezeAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.resumeAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["ZXMGLY"]}, "AddService": {"name": "增值服务", "ownerGroup": ["super.school-management.modify.AddService"], "graphql": ["ms-servicercontract-v1.mutation.updateOSBasicInfo:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}}, "ownerGroup": ["super.school-management.modify"]}}, {"name": "school-management-register-school", "specifier": "SchoolManagementRegisterSchool", "path": "register-school", "pathSegments": ["super", "school-management", "register-school"], "component": "@/packages/routers/src/basic-router/school-management/register-school.vue", "meta": {"isMenu": true, "title": "开通网校", "sort": 1, "icon": "icon-da<PERSON>u", "roles": ["ZXMGLY"], "permissionMap": {"query": {"name": "查询", "ownerGroup": ["super.school-management.register-school.query"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageOnlineSchoolInfoResponseInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getOnlineSchoolInfoCount:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}, "BasicInfo": {"name": "基础信息", "ownerGroup": ["super.school-management.register-school.BasicInfo"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.updateOSBasicInfo:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}, "SchoolConfig": {"name": "网校配置", "ownerGroup": ["super.school-management.register-school.SchoolConfig"], "graphql": ["ms-servicercontract-v1.mutation.updateOnlineSchool:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}, "TemplateConfig": {"name": "模板配置", "ownerGroup": ["super.school-management.register-school.TemplateConfig"], "graphql": ["ms-servicercontract-v1.mutation.updateOSTemplate:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}, "ManageAccount": {"name": "管理员信息", "ownerGroup": ["super.school-management.register-school.ManageAccount"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageOnlineSchoolAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateOnlineAdministratorAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.ResetAdminPasswordWithResponse:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.freezeAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.resumeAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["ZXMGLY"]}, "AddService": {"name": "增值服务", "ownerGroup": ["super.school-management.register-school.AddService"], "graphql": ["ms-servicercontract-v1.mutation.updateOSBasicInfo:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}, "cancel": {"name": "取消", "ownerGroup": ["super.school-management.register-school.cancel"], "graphql": [], "roles": ["ZXMGLY"]}, "update": {"name": "修改", "ownerGroup": ["super.school-management.register-school.update"], "graphql": [], "roles": []}, "modifyQuery": {"name": "查询", "ownerGroup": ["super.school-management.register-school.modifyQuery"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getOnlineSchoolInfoResponseInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-autolearning-online-school-smart-learning-service-v1.query.queryOnlineSchoolSmartLearningServiceConfigByServicerId:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "platform-supplement-study-rules-setting-v1.query.getSupplementStudyRuleSettingByServiceId:{\"authorizationRequired\":true}", "platform-jxjypxtypt-distribution-service-v1.query.getDistributionService:{\"authorizationRequired\":false}"], "roles": []}, "submit": {"name": "确认开通", "ownerGroup": ["super.school-management.register-school.submit"], "graphql": ["ms-servicercontract-v1.mutation.createOnlineSchoolContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}"], "roles": ["ZXMGLY"]}}, "ownerGroup": ["super.school-management.register-school"]}}]}, {"name": "statistic", "specifier": "Statistic", "path": "/statistic", "pathSegments": ["super", "statistic"], "component": "@/packages/routers/src/basic-router/statistic.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "统计报表", "sort": 5, "icon": "icon-shu<PERSON>", "roles": [], "permissionMap": {}, "ownerGroup": ["super.statistic"]}, "children": []}, {"name": "training", "specifier": "Training", "path": "/training", "pathSegments": ["super", "training"], "component": "@/packages/routers/src/basic-router/training.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "培训管理", "sort": 4, "icon": "icon-peixun", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training"]}, "children": [{"name": "training-customer-service", "specifier": "TrainingCustomerService", "path": "customer-service", "pathSegments": ["super", "training", "customer-service"], "component": "@/packages/routers/src/basic-router/training/customer-service.vue", "meta": {"isMenu": true, "title": "客服管理", "sort": 9, "icon": "icon-kefu", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.customer-service"]}, "children": []}, {"name": "training-import", "specifier": "TrainingImport", "path": "import", "pathSegments": ["super", "training", "import"], "component": "@/packages/routers/src/basic-router/training/import.vue", "meta": {"isMenu": true, "title": "导入开通", "sort": 6, "icon": "icon-da<PERSON>u", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.import"]}, "children": [{"name": "training-import-student-index", "specifier": "TrainingImportStudentIndex", "path": "student", "pathSegments": ["super", "training", "import", "student"], "component": "@/packages/routers/src/basic-router/training/import/student/index.vue", "meta": {"isMenu": true, "title": "导入学员", "sort": 1, "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.import.student"]}}]}, {"name": "training-intelligent-learning", "specifier": "TrainingIntelligentLearning", "path": "intelligent-learning", "pathSegments": ["super", "training", "intelligent-learning"], "component": "@/packages/routers/src/basic-router/training/intelligent-learning.vue", "meta": {"isMenu": true, "title": "智能学习", "sort": 7, "icon": "icon-trainprocess", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.intelligent-learning"]}, "children": [{"name": "training-intelligent-learning-import-open-index", "specifier": "TrainingIntelligentLearningImportOpenIndex", "path": "import-open", "pathSegments": ["super", "training", "intelligent-learning", "import-open"], "component": "@/packages/routers/src/basic-router/training/intelligent-learning/import-open/index.vue", "meta": {"isMenu": true, "title": "导入学员开班并学习", "sort": 1, "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.intelligent-learning.import-open"]}}]}, {"name": "training-log-management", "specifier": "TrainingLogManagement", "path": "log-management", "pathSegments": ["super", "training", "log-management"], "component": "@/packages/routers/src/basic-router/training/log-management.vue", "meta": {"isMenu": true, "title": "日志管理", "sort": 12, "icon": "icon-zixun", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.log-management"]}, "children": []}, {"name": "training-scheme", "specifier": "TrainingScheme", "path": "scheme", "pathSegments": ["super", "training", "scheme"], "component": "@/packages/routers/src/basic-router/training/scheme.vue", "meta": {"isMenu": true, "title": "培训方案管理", "sort": 2, "icon": "icon-fangan", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.scheme"]}, "children": []}, {"name": "training-special-topics", "specifier": "TrainingSpecialTopics", "path": "special-topics", "pathSegments": ["super", "training", "special-topics"], "component": "@/packages/routers/src/basic-router/training/special-topics.vue", "meta": {"isMenu": true, "title": "专题管理", "sort": 3, "icon": "hb-iconfont icon-grade", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.special-topics"]}, "children": []}, {"name": "training-task", "specifier": "TrainingTask", "path": "task", "pathSegments": ["super", "training", "task"], "component": "@/packages/routers/src/basic-router/training/task.vue", "meta": {"isMenu": true, "title": "导入导出任务管理", "sort": 11, "icon": "icon-ding<PERSON>", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.task"]}, "children": []}, {"name": "training-trade", "specifier": "TrainingTrade", "path": "trade", "pathSegments": ["super", "training", "trade"], "component": "@/packages/routers/src/basic-router/training/trade.vue", "meta": {"isMenu": true, "title": "交易管理", "sort": 4, "icon": "icon-<PERSON><PERSON><PERSON>", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.trade"]}, "children": [{"name": "training-trade-invoice", "specifier": "TrainingTradeInvoice", "path": "invoice", "pathSegments": ["super", "training", "trade", "invoice"], "component": "@/packages/routers/src/basic-router/training/trade/invoice.vue", "meta": {"isMenu": true, "title": "发票管理", "sort": 3, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.trade.invoice"]}, "children": []}, {"name": "training-trade-order", "specifier": "TrainingTradeOrder", "path": "order", "pathSegments": ["super", "training", "trade", "order"], "component": "@/packages/routers/src/basic-router/training/trade/order.vue", "meta": {"isMenu": true, "title": "订单管理", "sort": 1, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.trade.order"]}, "children": []}, {"name": "training-trade-reconciliation", "specifier": "TrainingTradeReconciliation", "path": "reconciliation", "pathSegments": ["super", "training", "trade", "reconciliation"], "component": "@/packages/routers/src/basic-router/training/trade/reconciliation.vue", "meta": {"isMenu": true, "title": "对账管理", "sort": 4, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.trade.reconciliation"]}, "children": []}, {"name": "training-trade-refund", "specifier": "TrainingTradeRefund", "path": "refund", "pathSegments": ["super", "training", "trade", "refund"], "component": "@/packages/routers/src/basic-router/training/trade/refund.vue", "meta": {"isMenu": true, "title": "退款管理", "sort": 2, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.trade.refund"]}, "children": []}]}, {"name": "training-user", "specifier": "TrainingUser", "path": "user", "pathSegments": ["super", "training", "user"], "component": "@/packages/routers/src/basic-router/training/user.vue", "meta": {"isMenu": true, "title": "用户管理", "sort": 8, "icon": "icon-guan<PERSON><PERSON>", "roles": [], "permissionMap": {}, "ownerGroup": ["super.training.user"]}, "children": []}]}]}]