<template>
  <div>
    <!-- <el-card shadow="never" class="m-card f-mb15"> -->
    <!--查询可报培训方案-->
    <el-drawer title="培训方案" :visible.sync="isShow" size="1200px" custom-class="m-drawer">
      <div class="drawer-bd">
        <!--显示5个，超出部分隐藏-->
        <el-row :gutter="16" class="m-query f-mt10">
          <el-form :inline="true" label-width="auto">
            <el-col :span="8">
              <el-form-item label="年度">
                <biz-year-select v-model="localSkuProperty.year" placeholder="请选择培训年度"></biz-year-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="地区">
                <biz-national-region
                  v-model="localSkuProperty.region"
                  :check-strictly="true"
                  placeholder="请选择地区"
                ></biz-national-region>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="培训方案类型">
                <biz-scheme-type v-model="schemeTypeInfo"></biz-scheme-type>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="行业">
                <biz-industry-select
                  v-model="localSkuProperty.industry"
                  @clearIndustrySelect="handleClearIndustrySelect"
                  @industryPropertyId="handleIndustryPropertyId"
                  @industryInfos="handleIndustryInfos"
                  ref="industrySelect"
                ></biz-industry-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="培训类别"
                v-if="
                  skuVisible.trainingCategory &&
                  localSkuProperty.industry &&
                  (localSkuProperty.industry === envConfig.constructionIndustryId ||
                    localSkuProperty.industry === envConfig.occupationalHealthId)
                "
              >
                <biz-training-category-select
                  v-model="localSkuProperty.trainingCategory"
                  :industry-property-id="industryPropertyId"
                  @updateTrainingCategory="handleUpdateTrainingCategory"
                  :industryId="localSkuProperty.industry"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="培训对象"
                v-if="
                  skuVisible.trainingCategory &&
                  localSkuProperty.industry &&
                  localSkuProperty.industry === envConfig.occupationalHealthId
                "
              >
                <biz-training-object-select
                  v-model="localSkuProperty.trainingObject"
                  placeholder="请选择培训对象"
                  :industry-property-id="industryPropertyId"
                  :industry-id="localSkuProperty.industry"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="岗位类别"
                v-if="
                  skuVisible.trainingCategory &&
                  localSkuProperty.industry &&
                  localSkuProperty.industry === envConfig.occupationalHealthId
                "
              >
                <biz-obj-category-select
                  v-model="localSkuProperty.positionCategory"
                  placeholder="请选择岗位类别"
                  :industry-property-id="industryPropertyId"
                  :industryId="localSkuProperty.industry"
                  :training-object-id="localSkuProperty.trainingObject"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="技术等级"
                v-if="
                  skuVisible.jobLevel &&
                  localSkuProperty.industry &&
                  localSkuProperty.industry === envConfig.workServiceId
                "
              >
                <biz-technical-grade-select
                  v-model="localSkuProperty.jobLevel"
                  :industry-id="localSkuProperty.industry"
                  :industry-property-id="industryPropertyId"
                ></biz-technical-grade-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="学段"
                v-if="
                  skuVisible.grade &&
                  localSkuProperty.industry &&
                  localSkuProperty.industry === envConfig.teacherIndustryId
                "
              >
                <biz-study-period
                  v-model="localSkuProperty.grade"
                  placeholder="请选择学段"
                  :industry-property-id="industryPropertyId"
                  :industry-id="localSkuProperty.industry"
                  @updateStudyPeriod="updateGrade"
                  @clearSubject="clearSubject"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="学科"
                v-if="
                  skuVisible.subject &&
                  localSkuProperty.industry &&
                  localSkuProperty.industry === envConfig.teacherIndustryId
                "
              >
                <biz-subject
                  v-model="localSkuProperty.subject"
                  placeholder="请选择学科"
                  :industry-property-id="industryPropertyId"
                  :industryId="localSkuProperty.industry"
                  :studyPeriodId="localSkuProperty.grade"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="培训方案名称">
                <el-input clearable placeholder="请输入培训方案名称" v-model="localSkuProperty.schemeName" />
              </el-form-item>
            </el-col>
            <el-col :span="8" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary" @click="searchBase">查询</el-button>
                <el-button @click="resetCondition">重置</el-button>
                <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <el-table stripe :data="replaceableTrainClassList" max-height="500px" class="m-table">
          <el-table-column label="培训方案名称" min-width="300">
            <template slot-scope="scope">
              <!-- <p>{{ scope.row.schemeName }}</p>
                <p class="f-f13 f-c9" v-if="scope.row.schemeType">
                  【培训班-{{ scope.row.schemeType === 1 ? '选课规则' : '自主选课' }}】
                </p> -->
              <p>
                <el-tag type="primary" effect="dark" size="mini">{{ getSchemeType(scope.row) }}</el-tag
                >{{ scope.row.schemeName }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="培训属性" min-width="300">
            <template slot-scope="scope">
              <p v-if="getSkuPropertyName(scope.row, 'industry')">
                行业：{{ getSkuPropertyName(scope.row, 'industry') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'region')">地区：{{ getSkuPropertyName(scope.row, 'region') }}</p>
              <p v-if="getSkuPropertyName(scope.row, 'jobLevel')">
                技术等级：{{ getSkuPropertyName(scope.row, 'jobLevel') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'learningPhase')">
                学段：{{ getSkuPropertyName(scope.row, 'learningPhase') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'discipline')">
                学科：{{ getSkuPropertyName(scope.row, 'discipline') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'subjectType')">
                科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
              </p>
              <p v-show="getSkuPropertyName(scope.row, 'practitionerCategory')">
                执业类别：{{ getSkuPropertyName(scope.row, 'certificatesType') }}-{{
                  getSkuPropertyName(scope.row, 'practitionerCategory')
                }}
              </p>
              <p v-if="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
                培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'trainingMajor')">
                培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'trainingObject')">
                培训对象：{{ getSkuPropertyName(scope.row, 'trainingObject') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'positionCategory')">
                岗位类别：{{ getSkuPropertyName(scope.row, 'positionCategory') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'year')">培训年度：{{ getSkuPropertyName(scope.row, 'year') }}</p>
              <!-- <p>行业：{{ scope.row.skuValueNameProperty.industry.skuPropertyName }}</p>
                <p v-if="scope.row.skuValueNameProperty.region.skuPropertyName">
                  地区：{{ scope.row.skuValueNameProperty.region.skuPropertyName }}
                </p> -->
              <!-- <p>科目类型：{{ scope.row.skuValueNameProperty.subjectType.skuPropertyName }}</p>
                <p v-if="scope.row.skuValueNameProperty.trainingCategory.skuPropertyName">
                  培训类别：{{ scope.row.skuValueNameProperty.trainingCategory.skuPropertyName }}
                </p>
                <p v-if="scope.row.skuValueNameProperty.trainingMajor.skuPropertyName">
                  培训专业：{{ scope.row.skuValueNameProperty.trainingMajor.skuPropertyName }}
                </p> -->
              <!-- <p>技术等级：{{ scope.row.skuValueNameProperty.technicalGrade.skuPropertyName }}</p>
                <p>培训年度：{{ scope.row.skuValueNameProperty.year.skuPropertyName }}</p> -->
            </template>
          </el-table-column>
          <el-table-column label="报名状态" min-width="140">
            <template>
              <div>
                <el-badge is-dot type="success" class="badge-status">开放报名</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学习起止时间" min-width="220">
            <template slot-scope="scope">
              <span v-if="longTerm(scope.row)">
                <p>长期有效</p>
              </span>
              <span v-else>
                <p>起始时间：{{ scope.row.learningDateScope.begin }}</p>
                <p>结束时间：{{ scope.row.learningDateScope.end }}</p>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
      </div>
    </el-drawer>
    <!-- </el-card> -->
  </div>
</template>
<script lang="ts">
  import { Component, Prop, Vue, Watch, PropSync, Ref } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import QueryReplaceableTrainClassListVo from '@api/service/management/train-class/query/vo/QueryReplaceableTrainClassListVo'
  import ReplaceableTrainClassDetailVo from '@api/service/management/train-class/query/vo/ReplaceableTrainClassDetailVo'
  import QueryExchangeTrainClass from '@api/service/management/train-class/query/QueryExchangeTrainClass'
  import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import TrainClassSchemeType, {
    TrainClassSchemeEnum
  } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
  import BizYearSelect from '@hbfe/jxjy-admin-components/src/biz/biz-year-select.vue'
  import BizNationalRegion from '@hbfe/jxjy-admin-components/src/biz/biz-national-region.vue'
  import BizIndustrySelect from '@hbfe/jxjy-admin-components/src/biz/biz-industry-select.vue'
  import BizAccounttypeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-accounttype-select.vue'
  import BizMajorCascader from '@hbfe/jxjy-admin-components/src/biz/biz-major-cascader.vue'
  import BizTrainingCategorySelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-category-select.vue'
  import BizMajorSelect from '@hbfe/jxjy-admin-components/src/biz/biz-major-select.vue'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import CreateExchangeOrderModel from '@hbfe/jxjy-admin-customerService/src/personal/components/model/CreateExchangeOrderModel'
  import BizTechnicalGradeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-technical-grade-select.vue'
  import UITrainClassCommodityDetail from '@hbfe/jxjy-admin-scheme/src/models/UITrainClassCommodityDetail'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'

  // sku绑定模型
  class SchemeSkuProperty {
    year: string
    region: string[]
    industry: string
    subjectType: string
    trainingCategory: string
    societyTrainingMajor: string[]
    constructionTrainingMajor: string
    jobLevel: string
    schemeName: string
    positionCategory: string
    trainingObject: string
    grade: string
    subject: string
  }

  @Component({
    components: {
      BizMajorSelect,
      BizTrainingCategorySelect,
      BizMajorCascader,
      BizAccounttypeSelect,
      BizIndustrySelect,
      BizNationalRegion,
      BizYearSelect,
      BizTechnicalGradeSelect
    }
  })
  export default class extends Vue {
    @Ref('industrySelect') industrySelect: BizIndustrySelect
    isShow = false
    schemeTypeInfo = new Array<string>()
    tableData = [{}]

    // 查询接口入口
    queryRequestEntrance: QueryExchangeTrainClass =
      TrainClassManagerModule.queryTrainClassFactory.getQueryExchangeTrainClass()

    // 分页 - 可换班列表
    page: UiPage
    // 查询 - 可换班列表
    query: Query = new Query()
    // 查询参数 - 可换班列表
    queryParams: QueryReplaceableTrainClassListVo = new QueryReplaceableTrainClassListVo()
    // 列表 - 可换班列表
    replaceableTrainClassList: ReplaceableTrainClassDetailVo[] = []

    // 培训方案类型列表
    trainClassSchemeTypeList: EnumOption<TrainClassSchemeEnum>[] = TrainClassSchemeType.list()

    // 剔除培训班商品id集合
    excludeCommoditySkuIdList: string[] = []

    /**
     * 【培训班列表】价格
     */
    price: number = null

    /**
     * 培训方案名称
     */
    schemeName = ''

    //region sku

    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuProperty = {
      /**
       * 年度
       */
      year: '',
      /**
       * 培训方案名称
       */
      schemeName: '',
      /**
       * 地区
       */
      region: [] as string[],
      /**
       * 行业
       */
      industry: '',
      /**
       * 科目类型
       */
      subjectType: '',
      /**
       * 培训类别
       */
      trainingCategory: '',
      /**
       * 培训专业 - 建设行业
       */
      constructionTrainingMajor: '',
      /**
       * 培训专业 - 人社行业
       */
      societyTrainingMajor: [] as string[],
      /**
       * 技术等级 - 工勤行业
       **/
      jobLevel: '',
      /**
       * 培训对象
       */
      trainingObject: '',
      /**
       * 岗位类别
       */
      positionCategory: '',
      /**
       * 学段
       */
      grade: '',
      /**
       * 学科
       */
      subject: ''
    } as SchemeSkuProperty

    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''

    /**
     * 培训类别Id
     */
    trainingCategoryId = ''

    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: true,
      // 培训类别
      trainingCategory: true,
      // 技术等级
      jobLevel: true,
      // 培训对象
      trainingObject: true,
      //   岗位类别
      positionCategory: true,
      //学段
      grade: true,
      //学科
      subject: true
    }
    /**
     * 当前网校信息
     */
    envConfig = {
      // 工勤行业
      workServiceId: '',
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 职业卫生行业Id
      occupationalHealthId: '',
      //教师行业
      teacherIndustryId: ''
    }

    // 培训方案列表
    trainSchemeList: Array<UITrainClassCommodityDetail> = new Array<UITrainClassCommodityDetail>()

    //endregion

    /**
     * 获取培训方案类型
     */
    getSchemeType(row: ReplaceableTrainClassDetailVo) {
      return SchemeType.getSchemeType(row.schemeType, true)
    }

    longTerm(row: ReplaceableTrainClassDetailVo) {
      if (row.learningDateScope.begin == '1900-01-01 00:00:00' && row.learningDateScope.end == '2100-01-01 00:00:00') {
        return true
      } else {
        return false
      }
    }

    constructor() {
      super()
      this.page = new UiPage(this.pageReplaceableTrainClass, this.pageReplaceableTrainClass)
    }

    /**
     * 查询（由外部触发）
     */
    async searchByExternal(params: CreateExchangeOrderModel) {
      //   console.log('createExchangeOrder', params)
      this.price = QueryReplaceableTrainClassListVo.validateIsEmpty(params.price) ? params.price : null
      this.schemeName = params.schemeName || ''
      this.excludeCommoditySkuIdList = [] as string[]
      this.excludeCommoditySkuIdList = params.excludeCommoditySkuIdList || ([] as string[])
      // 清空页面绑定属性
      this.localSkuProperty = new SchemeSkuProperty()
      // 清空接口查询属性
      this.queryParams = new QueryReplaceableTrainClassListVo()
      this.queryParams.price = this.price
      await this.searchBase()
    }

    /**
     * 查询分页
     */
    async searchBase() {
      this.page.pageNo = 1
      await this.pageReplaceableTrainClass()
    }

    /**
     * 查询分页
     */
    async pageReplaceableTrainClass() {
      this.query.loading = true
      try {
        this.queryParams = this.getQueryParams()
        this.replaceableTrainClassList = await this.queryRequestEntrance.queryReplaceableTrainClassList(
          this.page,
          this.queryParams
        )
      } catch (e) {
        console.log('获取可更换列表失败！', e)
      } finally {
        this.query.loading = false
      }
    }

    /**
     * 获取查询参数
     */
    getQueryParams(): QueryReplaceableTrainClassListVo {
      //   console.log(this.schemeTypeInfo, 'this.schemeTypeInfo')
      const schemeName = this.queryParams.schemeName
      const price = this.queryParams.price
      const queryParams = new QueryReplaceableTrainClassListVo()
      if (this.schemeTypeInfo && this.schemeTypeInfo[1] === 'chooseCourseLearning') {
        queryParams.schemeType = 1
      }
      if (this.schemeTypeInfo && this.schemeTypeInfo[1] === 'autonomousCourseLearning') {
        queryParams.schemeType = 2
      }
      if (this.schemeTypeInfo && this.schemeTypeInfo[0] === 'trainingCooperation') {
        queryParams.schemeType = 3
      }
      queryParams.schemeName = this.localSkuProperty.schemeName || ''
      queryParams.price = price || null
      queryParams.price = this.price
      queryParams.jobLevel = this.localSkuProperty.jobLevel ? [this.localSkuProperty.jobLevel] : ([] as string[])
      queryParams.year = this.localSkuProperty.year ? [this.localSkuProperty.year] : ([] as string[])
      queryParams.region = this.localSkuProperty.region || ([] as string[])
      queryParams.industry = this.localSkuProperty.industry ? [this.localSkuProperty.industry] : ([] as string[])
      queryParams.subjectType = this.localSkuProperty.subjectType
        ? [this.localSkuProperty.subjectType]
        : ([] as string[])
      queryParams.trainingCategory = this.localSkuProperty.trainingCategory
        ? [this.localSkuProperty.trainingCategory]
        : ([] as string[])
      queryParams.trainingProfessional = this.getTrainingProfessional()
      queryParams.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      queryParams.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      queryParams.learningPhase = !this.localSkuProperty.grade ? ([] as string[]) : [this.localSkuProperty.grade]
      queryParams.discipline = !this.localSkuProperty.subject ? ([] as string[]) : [this.localSkuProperty.subject]
      queryParams.excludeCommoditySkuIdList = this.excludeCommoditySkuIdList
      console.log(this.queryParams.trainingObject, '1111')

      console.log(this.queryParams, 'queryParams')
      return queryParams
    }

    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人社行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }
    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: UITrainClassCommodityDetail, type: string): string {
      if (row.skuValueNameProperty[type]?.skuPropertyName) {
        const value = row.skuValueNameProperty[type].skuPropertyName
        const valuesArr = value.split('/'),
          lastIndex = valuesArr.length - 1
        return type === 'trainingMajor' && !row.isSocietyIndustry ? valuesArr[lastIndex] : value
      }
      return ''
    }

    /**
     * 重置条件
     */
    async resetCondition() {
      this.localSkuProperty = new SchemeSkuProperty()
      this.queryParams = new QueryReplaceableTrainClassListVo()
      this.queryParams.price = this.price
      await this.searchBase()
    }

    //region sku-Correlation

    // @Watch('localSkuProperty', {
    //   immediate: true,
    //   deep: true
    // })
    // localSkuPropertyChange(val: any) {
    //   //   console.log('localSkuProperty', val)
    // }

    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      this.localSkuProperty.jobLevel = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.grade = ''
      this.localSkuProperty.subject = ''
    }

    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.SUBJECT_TYPE)
      const configTrainingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.TRAINING_CATEGORY
      )
      const jobLevel = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.JOB_LEVEL)
      const trainingObject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.TRAINING_OBJECT)
      const positionCategory = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.POSITION_CATEGORY)
      const grade = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.LEARNING_PHASE)
      const subject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.DISCIPLINE)
      this.skuVisible.subjectType = configSubjectType > -1
      this.skuVisible.trainingCategory = configTrainingCategory > -1
      this.skuVisible.jobLevel = jobLevel > -1
      this.skuVisible.trainingObject = trainingObject > -1
      this.skuVisible.positionCategory = positionCategory > -1
      this.skuVisible.grade = grade > -1
      this.skuVisible.subject = subject > -1
    }

    /**
     * 响应组件行业Id集合传参
     */
    async handleIndustryInfos(values: any) {
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.occupationalHealthId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
      // 仅当单个行业，需要默认选中
      if (this.industrySelect?.industryOptions?.length === 1) {
        this.localSkuProperty.industry = this.industrySelect.industryOptions[0].id
      }
      await this.searchBase()
    }

    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
    }

    clearSubject() {
      this.localSkuProperty.grade = ''
      this.localSkuProperty.subject = ''
    }

    updateGrade(val: string) {
      this.$set(this.localSkuProperty, 'grade', val)
      this.$set(this.localSkuProperty, 'subject', '')
    }
    //endregion
  }
</script>
