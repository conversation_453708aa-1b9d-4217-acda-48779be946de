import {
  AdminInfoResponse,
  RoleResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

export default class PageAdminInfoResponse {
  /**
   * 管理员ID
   */
  userId: string
  /**
   * 管理员姓名
   */
  userName: string
  /**
   * 管理员账号  NOTE 后端字段为提供
   */
  adminAccount: string
  /**
   * 管理员状态
   */
  status: number
  /**
   * 角色列表
   */
  roleList: Array<RoleResponse>
  /**
   * 注册时间
   */
  createTime: string
  static from(adminInfoResponse: AdminInfoResponse) {
    const {
      user: { userId, userName, manageRegionList },
      account: { createTime },
      roleList,
      authenticationList: [{ status }]
    } = adminInfoResponse
    const pageAdminInfoResponse = new PageAdminInfoResponse()
    pageAdminInfoResponse.userId = userId
    pageAdminInfoResponse.userName = userName
    pageAdminInfoResponse.createTime = createTime
    pageAdminInfoResponse.roleList = roleList
    pageAdminInfoResponse.status = status
    return pageAdminInfoResponse
  }
}
