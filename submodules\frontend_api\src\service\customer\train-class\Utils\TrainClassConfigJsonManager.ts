import { default as CommonLearningType } from '@api/service/common/scheme/model/LearningType'
import LearningType from '@api/service/customer/train-class/query/vo/LearningType'
import Classification from '@api/service/customer/train-class/query/vo/Classification'
import TemplateNameManager from '@api/service/management/train-class/mutation/dto/TemplateNameManager'
import CourseLearningLearningType from '@api/service/customer/train-class/query/vo/CourseLearningLearningType'
import { LearningFeelEnum } from '@api/service/management/train-class/mutation/Enum/LearningFeelEnum'
import Scheme from '@api/service/common/scheme/model/schemeDto/Scheme'

class TrainClassConfigJsonManager {
  schemeType = 1
  calculatorSchemeConfigJson(schemeConfigJsonString: string): Scheme {
    try {
      return JSON.parse(schemeConfigJsonString)
    } catch (e) {
      console.log('转换json出错', e, 'json=', schemeConfigJsonString)
      return {} as Scheme
    }
  }
  jsonConfigConvertToLearningType(schemeConfigJsonString: string, schemeType = 1, schemeConfig?: any): LearningType {
    const jsonObject = this.calculatorSchemeConfigJson(schemeConfigJsonString)
    const learningType = new LearningType()
    this.schemeType = schemeType
    this.configCourseLearning(learningType, jsonObject, schemeConfig)
    this.configExam(learningType, jsonObject)
    this.configInterest(learningType, jsonObject)
    this.configPractice(learningType, jsonObject)
    this.configExperience(learningType, jsonObject)
    // 期别、问卷填充
    const { issue, questionnaire } = CommonLearningType.configIssueAndQuestionnaire(
      schemeConfig ? schemeConfig : jsonObject
    )
    learningType.issue = issue
    learningType.questionnaire = questionnaire
    return learningType
  }
  // 配置学习心得
  configExperience(learningType: LearningType, classConfigJson: any) {
    const experienceCourseLearningConfig: any = classConfigJson.learningExperienceLearning
    const experienceCourse = learningType.learningExperience
    if (experienceCourseLearningConfig) {
      experienceCourse.isSelected = true
      experienceCourse.learningTypeId = experienceCourseLearningConfig.id
      experienceCourse.configId = experienceCourseLearningConfig.config.id
      experienceCourse.showName = experienceCourseLearningConfig.config.showName
      if (experienceCourseLearningConfig.assessSetting) {
        experienceCourse.isExamed = true
        experienceCourse.learningExperienceNeedNum =
          experienceCourseLearningConfig.assessSetting.lessParticipateLearningExperienceTopicCount
        if (experienceCourseLearningConfig.config?.schemeParticipateRequired?.requiredType == 2) {
          experienceCourse.classCondition = LearningFeelEnum.COMPLETE_ALL_COURSES
        } else if (experienceCourseLearningConfig.config?.schemeParticipateRequired?.requiredType == 1) {
          experienceCourse.classCondition = LearningFeelEnum.STRAIGHTWAY
        } else {
          experienceCourse.classCondition = null
        }
        if (experienceCourseLearningConfig.config?.courseParticipateRequired?.requiredType == 2) {
          experienceCourse.courseCondition = LearningFeelEnum.COMPLETE_ALL_COURSES
        } else if (experienceCourseLearningConfig.config?.courseParticipateRequired?.requiredType == 1) {
          experienceCourse.courseCondition = LearningFeelEnum.STRAIGHTWAY
        } else {
          experienceCourse.courseCondition = null
        }
      } else {
        experienceCourse.classCondition = LearningFeelEnum.STRAIGHTWAY
        experienceCourse.courseCondition = LearningFeelEnum.STRAIGHTWAY
      }
    }
    // 缺是否达标，方案考核要求中至少需参加的心得数，学员已参加且通过的学习心得数
  }
  //配置兴趣课
  configInterest(learningType: LearningType, classConfigJson: any) {
    //填充兴趣课
    const interestCourseLearningConfig: any = classConfigJson.interestCourseLearning
    const interestCourse = learningType.interestCourse
    if (interestCourseLearningConfig) {
      interestCourse.isSelected = true
      interestCourse.learningTypeId = interestCourseLearningConfig.id
      interestCourse.configId = interestCourseLearningConfig.config.id
      if (
        interestCourseLearningConfig.config.courseTrainingOutlines &&
        interestCourseLearningConfig.config.courseTrainingOutlines.length
      ) {
        const interestTopLevelClassFication: any = {
          id: '9999',
          name: '顶级节点',
          sort: 1,
          coursePackageId: '',
          category: 0,
          compulsoryCourseIdList: [],
          childOutlines: interestCourseLearningConfig.config.courseTrainingOutlines
        }
        const classification = this.getClassficationArr(interestTopLevelClassFication, true)
        const firstClassFication = interestCourseLearningConfig.config.courseTrainingOutlines[0]
        //无分类
        if (firstClassFication.name == TemplateNameManager.NOCourseTrainingOutlinesName) {
          interestCourse.classification = classification.childOutlines[0]
        } else {
          interestCourse.classification = classification
        }
      }
      // interestCourse.classification = this.getClassficationArr(interestTopLevelClassFication, true)
    }
  }
  //   配置练习
  configPractice(learningType: LearningType, classConfigJson: any) {
    //填充练习
    const practiceLearningConfig: any = classConfigJson.practiceLearning
    const practiceLearning = learningType.practiceLearning

    if (practiceLearningConfig) {
      practiceLearning.isSelected = true
      practiceLearning.learningTypeId = practiceLearningConfig.id

      practiceLearning.type = practiceLearningConfig.config.type
      if (practiceLearning.type == 1 || practiceLearning.type == 3) {
        if (practiceLearning.type == 1 && practiceLearningConfig.config.questionSource) {
          practiceLearning.libraryIds = practiceLearningConfig.config.questionSource.libraryIds
        }
        if (practiceLearning.type == 3 && practiceLearningConfig.config.questionSource) {
          practiceLearning.paperPublishConfigureId =
            practiceLearningConfig.config.questionSource.paperPublishConfigureId
        }
      }
      practiceLearning.openDissects = practiceLearningConfig.config.openDissects

      practiceLearning.multipleQuestionMissScorePatterns =
        practiceLearningConfig.config.multipleQuestionMissScorePatterns
    }
  }
  //   配置Exam
  configExam(learningType: LearningType, classConfigJson: any) {
    //填充考试
    const examLearningConfig: any = classConfigJson.examLearning
    const exam = learningType.exam

    if (examLearningConfig) {
      exam.learningTypeId = examLearningConfig.id
      exam.examPassScore = examLearningConfig.config.qualifiedScore
      if (examLearningConfig.assessSetting) {
        exam.assessId = examLearningConfig.assessSetting.id
        exam.assessName = examLearningConfig.assessSetting.name
        exam.examPassScore = examLearningConfig.assessSetting.examPassScore
        exam.isExamAssessed = examLearningConfig.assessSetting.operation != 3
      }
      exam.configId = examLearningConfig.config.id
      exam.preconditionId = examLearningConfig.precondition?.id
      exam.preconditionName = examLearningConfig.precondition?.name

      if (examLearningConfig.config.gradesWhetherHide === undefined) {
        exam.gradesWhetherHide = true
      } else {
        exam.gradesWhetherHide = examLearningConfig.config.gradesWhetherHide
      }

      exam.isSelected = true
      exam.isAssess = true
      exam.name = examLearningConfig.config.name
      exam.description = examLearningConfig.config.description
      exam.paperPublishConfigureId = examLearningConfig.config.paperPublishConfigureId
      exam.allowCount = examLearningConfig.config.allowCount
      exam.allowStartTime = examLearningConfig.config.allowStartTime
      exam.allowEndTime = examLearningConfig.config.allowEndTime
      exam.timeLength = examLearningConfig.config.timeLength
      exam.qualifiedScore = examLearningConfig.config.qualifiedScore
      exam.timeLength = examLearningConfig.config.timeLength
      exam.openDissects = examLearningConfig.config.openDissects
      exam.questionAgainAnswer = examLearningConfig.config.questionAgainAnswer
      exam.questionDisplay = examLearningConfig.config.questionDisplay
      exam.multipleMissScorePattern = examLearningConfig.config.multipleMissScorePattern
      exam.examPattern = examLearningConfig.config.examType
      if (examLearningConfig.config.examType === 1) {
        exam.allowAnswerIfQualified = false
      } else {
        exam.allowAnswerIfQualified = examLearningConfig.config.allowAnswerIfQualified
      }

      exam.preCondition = examLearningConfig.precondition && examLearningConfig.precondition.operation != 3 ? 1 : 0
    }
  }
  //   配置courseLearning
  configCourseLearning(learningType: LearningType, classConfigJson: any, schemeConfig?: any) {
    const courseLearning = learningType.courseLearning
    let learningKey = 'chooseCourseLearning'
    //    jsonObj.type = this.schemeType == 1 ? 'chooseCourseLearning':'autonomousCourseLearning'
    if (classConfigJson.chooseCourseLearning || classConfigJson.autonomousCourseLearning) {
      if (classConfigJson.type == 'chooseCourseLearning') {
        // trainClassDetail.trainClassBaseInfo.schemeType = 1
        //填充选课规则
        const configChooseRule: any = classConfigJson.chooseCourseLearning.config.chooseCourseRule.config
        courseLearning.chooseCourseRule.id = configChooseRule.id
        courseLearning.chooseCourseRule.name = configChooseRule.name

        courseLearning.chooseCourseRule.allowLastChooseOver = configChooseRule.allowLastChooseOver
        courseLearning.chooseCourseRule.compulsoryPeriod = configChooseRule.compulsoryPeriod
        courseLearning.chooseCourseRule.electiveMaxPeriod = configChooseRule.electiveMaxPeriod
        courseLearning.chooseCourseRule.constrainedRepeatSelection = configChooseRule.constrainedRepeatSelection
        courseLearning.chooseCourseRule.constrainedRangeKeyList = configChooseRule.constrainedRangeKeyList
        //填充必修选修要求完成学时
        courseLearning.compulsoryRequirePeriod =
          classConfigJson.chooseCourseLearning.assessSetting.compulsoryRequirePeriod
        courseLearning.electiveRequirePeriod = classConfigJson.chooseCourseLearning.assessSetting.electiveRequirePeriod
      } else if (classConfigJson.type == 'autonomousCourseLearning') {
        learningKey = 'autonomousCourseLearning'
        courseLearning.isSelected = true
        courseLearning.isAssess = true
        // trainClassDetail.trainClassBaseInfo.schemeType = 2
        courseLearning.requirePeriod = classConfigJson.autonomousCourseLearning.assessSetting.requirePeriod
      }
      courseLearning.learningTypeId = classConfigJson[learningKey].id
      courseLearning.assessId = classConfigJson[learningKey].assessSetting.id
      courseLearning.assessName = classConfigJson[learningKey].assessSetting.name
      courseLearning.configId = classConfigJson[learningKey].config.id

      courseLearning.isSelected = true
      courseLearning.isAssess = true
      //填充课程学习中的学习大纲
      let courseTrainingOutlines: any = classConfigJson[learningKey].config.courseTrainingOutlines
      if (schemeConfig?.[learningKey]?.config?.courseTrainingOutlines)
        courseTrainingOutlines = schemeConfig?.[learningKey].config.courseTrainingOutlines
      this.updateClassification(courseLearning, courseTrainingOutlines)

      //填充课程学习中课后测验
      if (classConfigJson[learningKey].config.courseQuizConfig) {
        courseLearning.configCourseQuiz = true
        const courseQuizConfig: any = classConfigJson[learningKey].config.courseQuizConfig
        const quizConfigModel = courseLearning.quizConfigModel
        quizConfigModel.minCourseSchedule = courseQuizConfig.precondition.minCourseSchedule
        quizConfigModel.courseQuizPaperStandard = courseQuizConfig.precondition.courseQuizPaperStandard
        quizConfigModel.name = courseQuizConfig.quizConfig.name
        quizConfigModel.publishPattern = courseQuizConfig.quizConfig.publishPattern
        quizConfigModel.openDissects = courseQuizConfig.quizConfig.openDissects
        quizConfigModel.limitCourseQuizNum = courseQuizConfig.quizConfig.limitCourseQuizNum
        quizConfigModel.allowCourseQuizNum = courseQuizConfig.quizConfig.allowCourseQuizNum
        quizConfigModel.multipleMissScorePattern = courseQuizConfig.quizConfig.multipleMissScorePattern
        quizConfigModel.questionCount = courseQuizConfig.quizConfig.userCoursePaperPublishConfigure.questionCount
        quizConfigModel.totalScore = courseQuizConfig.quizConfig.userCoursePaperPublishConfigure.totalScore
        quizConfigModel.passScore = courseQuizConfig.quizConfig.passScore
        quizConfigModel.id = courseQuizConfig.id
        quizConfigModel.configName = courseQuizConfig.quizConfig.name
        quizConfigModel.configId = courseQuizConfig.quizConfig.id
        quizConfigModel.questionCountConfigureType =
          courseQuizConfig.quizConfig.userCoursePaperPublishConfigure.questionCountConfigureType
        quizConfigModel.questionCountPerPeriod =
          courseQuizConfig.quizConfig.userCoursePaperPublishConfigure.questionCountPerPeriod
        quizConfigModel.timeLengthPerQuestion =
          courseQuizConfig.quizConfig.userCoursePaperPublishConfigure.timeLengthPerQuestion
      }
      //填充评价
      if (classConfigJson[learningKey].config.courseAppraisalConfig) {
        const courseAppraisalConfig = classConfigJson[learningKey].config.courseAppraisalConfig
        courseLearning.enableAppraisal = courseAppraisalConfig.enableAppraisal
        courseLearning.enableCompulsoryAppraisal = courseAppraisalConfig.enableCompulsoryAppraisal
        courseLearning.preconditionCourseSchedule = courseAppraisalConfig.preconditionCourseSchedule
      }

      //填充课程完成评定
      const courseCompleteEvaluateConfig = classConfigJson[learningKey].config.courseCompleteEvaluateConfig
      courseLearning.courseSchedule = courseCompleteEvaluateConfig.courseSchedule
      courseLearning.courseQuizPagerStandard = courseCompleteEvaluateConfig.courseQuizPagerStandard
    }
  }

  /**
   * 更新分类
   * @param courseLearning 课程学习
   * @param courseTrainingOutlines 课程大纲
   */
  updateClassification(courseLearning: CourseLearningLearningType, courseTrainingOutlines: any) {
    if (courseTrainingOutlines && courseTrainingOutlines.length) {
      const topLevelClassFication: any = {
        id: '9999',
        name: '顶级节点',
        sort: 1,
        coursePackageId: '',
        category: 0,
        compulsoryCourseIdList: [],
        childOutlines: courseTrainingOutlines
      }
      const classification = this.getClassficationArr(topLevelClassFication)
      const firstClassFication = courseTrainingOutlines[0]
      //无分类
      if (firstClassFication.name == TemplateNameManager.NOCourseTrainingOutlinesName) {
        courseLearning.classification = classification.childOutlines[0]
      } else {
        courseLearning.classification = classification
      }
    }
  }
  //将大纲json转换为类
  private getClassficationArr(item: any, isInterest = false): Classification {
    const classification = new Classification()
    classification.name = item.name
    classification.id = item.id
    classification.sort = item.sort
    classification.coursePackageId = item.coursePackageId
    if (!isInterest) {
      if (this.schemeType == 1) {
        classification.category = item.category
      }
      if (this.schemeType == 2) {
        classification.compulsoryCourseIdList = item.compulsoryCourseIdList
        if (item.assessSetting) {
          classification.assessSetting.id = item.assessSetting.id
          classification.assessSetting.rangeKey = item.assessSetting.rangeKey
          classification.assessSetting.requirePeriod = item.assessSetting.requirePeriod
          classification.assessSetting.name = item.assessSetting.name
        }
      }
    }

    item.childOutlines.forEach((tmpItem: any) => {
      classification.childOutlines.push(this.getClassficationArr(tmpItem, isInterest))
    })
    return classification
  }
}
export default new TrainClassConfigJsonManager()
