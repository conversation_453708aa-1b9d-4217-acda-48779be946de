/**
 *
 * 试卷的ui模型
 * @author: eleven
 * @date: 2020/4/12
 */
import { RandomConfigurationItemUI } from '@/store/modules-ui/exam/models/RandomConfigurationItemUI'
import { ExamPaperBasicInfoUI } from '@/store/modules-ui/exam/models/ExamPaperBasicInfoUI'
import {
  PreExamMockExaminationPaperCreateRequest,
  PreExamMockExaminationPaperUpdateDTO,
  // Random,
  // RandomConfigurationItem,
  RandomTakeObjectConfigurationItem
} from '@api/gateway/PreExam-default'
import { ExamConstant } from '@/store/module/exam/common/ExamConstant'
import { ExamPaper } from '@/store/module/exam/mode/exam-paper/ExamPaper'
import Random, {
  RandomConfigurationItem
} from '@api/service/common/models/exam/paper/section/configuration/random/Random'
export class ExamPaperUI extends ExamPaperBasicInfoUI {
  /**
   * 所属试卷分类名称
   */
  paperType = ''
  /**
   *  大题集合
   */
  randomConfigurationItemList = new Array<RandomConfigurationItem>()

  /**
   * 将ui模型转为远端的创建模型
   */
  public toRemoteCreateModel(draft: boolean) {
    const remote = new PreExamMockExaminationPaperCreateRequest()
    remote.name = this.name
    remote.configType = this.configType
    remote.passScore = this.passScore
    remote.paperTypeId = this.paperTypeId
    remote.timeLength = this.timeLength
    remote.draft = draft
    remote.enabled = true
    remote.totalScore = this.totalScore
    remote.timeType = ExamConstant.PAPER_TIME_TYPE_WHOLE_PAPER

    const random = new Random()
    remote.random = random
    random.randomType = ExamConstant.RANDOM_EXAM_PAPER_TYPE_OBJECT_SUPPORT_RATIO
    random.recursive = false
    random.randomWay = ExamConstant.RANDOM_WAY_BIGQUESTION
    random.ratio = false

    let totalCount = 0
    random.configurationItems = new Array<RandomConfigurationItem>()
    if (this.randomConfigurationItemList.length) {
      random.configurationItems.push(
        ...this.randomConfigurationItemList.map(p => {
          const item = new RandomConfigurationItem()
          item.count = p.count
          item.name = p.name
          item.totalScore = p.totalScore
          item.type = p.type
          item.scoreWay = ExamConstant.SCORE_WAY_AVG
          item.singleAnswerableTimeLength = -1
          item.ratio = false
          totalCount += p.count
          const takeObjectConfigurationItems = Array<RandomTakeObjectConfigurationItem>()
          // item.takeObjectConfigurationItems = takeObjectConfigurationItems

          // takeObjectConfigurationItems.push(
          //   ...p.questionCategoryList.map(q => {
          //     const questionCategoryObjectItem = new RandomTakeObjectConfigurationItem()
          //     questionCategoryObjectItem.type = ExamConstant.QUESTION_CATEGORY_KEY
          //     questionCategoryObjectItem.objectId = q
          //     questionCategoryObjectItem.ratioValue = 0
          //     questionCategoryObjectItem.extractCount = 0
          //     return questionCategoryObjectItem
          //   })
          // )
          return item
        })
      )
    }
    remote.totalQuestionCount = totalCount
    return remote
  }

  /**
   * 将ui模型转为远端的更新模型
   */
  public toRemoteUpdateModel(draft: boolean) {
    const res = this.toRemoteCreateModel(draft)
    const remote = new PreExamMockExaminationPaperUpdateDTO()
    Object.assign(remote, res)
    // remote.id = this.id
    return remote
  }

  /**
   * 数据模型转换
   * @param remote
   */
  public static from(remote: ExamPaper) {
    const result = new ExamPaperUI()
    // result.id = remote.id
    result.name = remote.name
    result.totalScore = remote.totalScore
    result.configType = remote.configType
    result.passScore = remote.passScore
    result.timeLength = remote.timeLength
    result.enabled = remote.enabled
    result.draft = remote.draft
    result.paperTypeId = remote.paperTypeId
    result.paperType = remote.paperType
    if (remote.configType === ExamConstant.CONFIG_TYPE_RANDOM) {
      const randomConfigurationItemList = new Array<RandomConfigurationItemUI>()
      // result.randomConfigurationItemList = randomConfigurationItemList

      const configurationItems = remote.random.configurationItems
      randomConfigurationItemList.push(
        ...configurationItems.map(p => {
          const item = new RandomConfigurationItemUI()
          item.totalScore = p.totalScore
          item.count = p.count
          item.type = p.type
          item.name = p.name
          item.questionCategoryList = p.takeObjectConfigurationItems.map(p => p.objectId)
          return item
        })
      )
    }
    return result
  }

  /**
   * 转换模型
   * @param draft
   */
  public toRemoteCreateModelforPaper(draft: boolean) {
    const remote = new PreExamMockExaminationPaperCreateRequest()
    remote.name = this.name
    remote.configType = this.configType
    remote.passScore = this.passScore
    remote.paperTypeId = this.paperTypeId
    remote.timeLength = this.timeLength
    remote.draft = draft
    remote.enabled = true
    remote.totalScore = this.totalScore
    remote.timeType = ExamConstant.PAPER_TIME_TYPE_WHOLE_PAPER

    const random = new Random()
    remote.random = random
    random.randomType = ExamConstant.RANDOM_EXAM_PAPER_TYPE_OBJECT_SUPPORT_RATIO
    random.recursive = false
    random.randomWay = ExamConstant.RANDOM_WAY_BIGQUESTION
    random.ratio = false

    let totalCount = 0
    random.configurationItems = new Array<RandomConfigurationItem>()
    if (this.randomConfigurationItemList.length) {
      random.configurationItems.push(
        ...this.randomConfigurationItemList.map(p => {
          const item = new RandomConfigurationItem()
          item.count = p.count
          item.name = p.name
          item.totalScore = p.totalScore
          item.type = p.type
          item.scoreWay = ExamConstant.SCORE_WAY_AVG
          item.singleAnswerableTimeLength = -1
          item.ratio = false
          totalCount += p.count
          const takeObjectConfigurationItems = Array<RandomTakeObjectConfigurationItem>()
          // item.takeObjectConfigurationItems = takeObjectConfigurationItems

          // takeObjectConfigurationItems.push(
          //   ...p.questionCategoryList.map(q => {
          //     const questionCategoryObjectItem = new RandomTakeObjectConfigurationItem()
          //     questionCategoryObjectItem.type = ExamConstant.QUESTION_CATEGORY_KEY
          //     questionCategoryObjectItem.objectId = q
          //     questionCategoryObjectItem.ratioValue = 0
          //     questionCategoryObjectItem.extractCount = 0
          //     return questionCategoryObjectItem
          //   })
          // )
          return item
        })
      )
    }
    remote.totalQuestionCount = totalCount
    return remote
  }
}
