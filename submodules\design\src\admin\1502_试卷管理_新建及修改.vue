<template>
  <el-main>
    <div class="f-p15">
      <!--第一步-->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <el-row type="flex" justify="center">
          <el-col :sm="20" :lg="12">
            <el-steps :active="1" align-center class="m-steps f-pt40 f-pb10">
              <el-step title="填写基础信息"></el-step>
              <el-step title="配置试题"></el-step>
              <el-step title="创建成功"></el-step>
            </el-steps>
          </el-col>
        </el-row>
        <el-divider class="m-divider"></el-divider>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20 f-mb40">
              <el-form-item label="试卷名称：" required>
                <el-input v-model="form.name" clearable placeholder="请输入" />
              </el-form-item>
              <el-form-item label="组卷方式：" required>
                <el-select v-model="form.region" clearable placeholder="请选择组卷方式" class="form-m">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="所属试卷分类：" required>
                <el-cascader
                  clearable
                  :show-all-levels="false"
                  :options="cascader"
                  placeholder="请选择所属试卷分类"
                  class="form-m"
                />
                <el-button type="text" class="f-ml15">新建分类</el-button>
              </el-form-item>
              <el-form-item label="出题范围：">
                <el-radio-group v-model="form.resource">
                  <el-radio label="按题库出题"></el-radio>
                  <el-radio label="按学院课程ID出图"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="试卷总分："> <span class="f-cr f-f20 f-fb">100</span> 分 </el-form-item>
              <el-form-item label="考试时长：" required>
                <el-input v-model="form.name" class="input-num" />
                <span class="f-ml5">分钟</span>
              </el-form-item>
              <el-form-item label="出题题库：">
                <el-button type="primary" plain>新增题库</el-button>
                <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="题库名称" min-width="250">
                    <template>题库名称题库名称</template>
                  </el-table-column>
                  <el-table-column label="题库类型" min-width="150">
                    <template>题库类型</template>
                  </el-table-column>
                  <el-table-column label="已启用的试题数量" width="180" align="center">
                    <template>23</template>
                  </el-table-column>
                  <el-table-column label="操作" width="80" align="center" fixed="right">
                    <template>
                      <el-button type="text" size="mini">移除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1 f-mb30">
        <el-button>放弃编辑</el-button>
        <el-button>保存为草稿</el-button>
        <el-button type="primary">保存并进入下一步</el-button>
      </div>
      <!--第二步-->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <el-row type="flex" justify="center">
          <el-col :sm="20" :lg="12">
            <el-steps :active="2" align-center class="m-steps f-pt40 f-pb10">
              <el-step title="填写基础信息"></el-step>
              <el-step title="配置试题"></el-step>
              <el-step title="创建成功"></el-step>
            </el-steps>
          </el-col>
        </el-row>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">基本信息</span>
        </div>
        <el-row type="flex" justify="center">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form :inline="true" label-width="150px" class="m-text-form f-mt30">
              <el-col :span="12">
                <el-form-item label="试卷名称：">试卷名称试卷名称试卷名称</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="试卷分类：">试卷分类</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="组卷方式：">智能组卷</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="试卷总分：">100 分</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="考试时长：">120 分钟</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="出题范围：">出题范围</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="已选题库：">已选题库已选题库已选题库</el-form-item>
              </el-col>
            </el-form>
          </el-col>
        </el-row>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">配置试题</span>
        </div>
        <el-row type="flex" justify="center" class="f-mt30 f-pb30">
          <el-col :md="20" :lg="16" :xl="13">
            <el-button type="primary" icon="el-icon-plus" plain>添加大题</el-button>
            <el-form label-width="90px" class="m-form">
              <div class="m-question-set f-clear f-mt20">
                <el-form-item class="is-text">
                  <div slot="label" class="f-f16 f-cb">第一大题</div>
                  <div class="f-f16">
                    请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。
                  </div>
                  <el-row :gutter="20" class="f-mt20">
                    <el-col :span="6">题型： <span class="f-fb">单选题</span></el-col>
                    <el-col :span="6">大题总分：<span class="f-fb">40分</span></el-col>
                    <el-col :span="6">大题数量：<span class="f-fb">40道题</span></el-col>
                  </el-row>
                  <div class="f-mt20">
                    <el-button icon="el-icon-edit" size="mini" type="primary" plain>编辑</el-button>
                    <el-button icon="el-icon-delete" size="mini" type="primary" plain>删除</el-button>
                  </div>
                </el-form-item>
              </div>
              <div class="m-question-set f-clear f-mt20">
                <el-form-item class="is-text">
                  <div slot="label" class="f-f16 f-cb">第二大题</div>
                  <div class="f-f16">
                    请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。
                  </div>
                  <el-row :gutter="20" class="f-mt20">
                    <el-col :span="6">题型： <span class="f-fb">单选题</span></el-col>
                    <el-col :span="6">大题总分：<span class="f-fb">40分</span></el-col>
                    <el-col :span="6">大题数量：<span class="f-fb">40道题</span></el-col>
                  </el-row>
                  <div class="f-mt20">
                    <el-button icon="el-icon-edit" size="mini" type="primary" plain>编辑</el-button>
                    <el-button icon="el-icon-delete" size="mini" type="primary" plain>删除</el-button>
                  </div>
                </el-form-item>
              </div>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1 f-mb30">
        <el-button>放弃编辑</el-button>
        <el-button>返回上一步</el-button>
        <el-button>保存为草稿</el-button>
        <el-button type="primary">保存为试卷</el-button>
      </div>
      <!--第三步-->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <el-row type="flex" justify="center">
          <el-col :sm="20" :lg="12">
            <el-steps :active="3" align-center class="m-steps f-pt40 f-pb10">
              <el-step title="填写基础信息"></el-step>
              <el-step title="配置试题"></el-step>
              <el-step title="创建成功"></el-step>
            </el-steps>
          </el-col>
        </el-row>
        <el-divider class="m-divider"></el-divider>
        <el-result icon="success" title="试卷创建成功！" class="f-mb20">
          <template slot="extra">
            <el-button type="primary" size="medium">返回</el-button>
          </template>
        </el-result>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
