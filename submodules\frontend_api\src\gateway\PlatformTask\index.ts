import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/PlatformTask'

// 枚举
export enum TaskTypeEnum {
  IMPORT_TASK = 'IMPORT_TASK',
  COLLECTIVE_REGISTER = 'COLLECTIVE_REGISTER'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 导入开通主任务查询信息
 */
export class TaskQueryRequest {
  /**
   * 主任务状态集合
   */
  statusList?: Array<number>
  /**
   * 创建时间 开始时间查询
   */
  startCreateTime?: string
  /**
   * 创建时间 结束时间查询
   */
  endCreateTime?: string
  /**
   * 任务的类型
   */
  taskType?: TaskTypeEnum
  /**
   * 批次号
   */
  batchNo?: string
}

/**
 * 导入开通任务执行结果信息
 */
export class ResultResponse {
  /**
   * 子任务总数
   */
  totalCount: number
  /**
   * 处于创建状态的子任务数量
   */
  createdStatusCount: number
  /**
   * 处于执行中状态的子任务数量
   */
  executingStatusCount: number
  /**
   * 处于执行完成状态的子任务数量
   */
  executedCount: number
  /**
   * 处于执行失败状态的子任务数量
   */
  executeFailedCount: number
  /**
   * 执行到第一步完成的任务的数量
   */
  executeStepOneCount: number
}

/**
 * 导入开通主任务
 */
export class TaskResponse {
  /**
   * 主任务ID
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务状态
@see com.fjhb.platform.component.task.constants.TaskStatusConst
   */
  status: number
  /**
   * 任务执行开始时间
   */
  startTime: string
  /**
   * 任务执行完成时间
   */
  completedTime: string
  /**
   * 创建人ID
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 失败信息
   */
  failMessage: string
  /**
   * excel 文件路径
   */
  filePath: string
  /**
   * 执行结果信息
   */
  result: ResultResponse
}

export class TaskResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TaskResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 导致一个导入开通任务的结果
   * @param taskId 主任务ID
   * @param status 状态集合
   * @param type   1: 导入开通 2:集体缴费
   * @return 下载路径
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportImportOpenedSubTask(
    params: { taskId?: string; status?: Array<number>; type: number },
    query: DocumentNode = GraphqlImporter.exportImportOpenedSubTask,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTaskPage(
    params: { page?: Page; query?: TaskQueryRequest },
    query: DocumentNode = GraphqlImporter.findTaskPage,
    operation?: string
  ): Promise<Response<TaskResponsePage>> {
    return commonRequestApi<TaskResponsePage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }
}

export default new DataGateway()
