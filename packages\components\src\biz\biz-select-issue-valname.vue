<!--选择期别名称-->
<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    class="el-input"
    @change="selectChange"
    :disabled="disabled"
    filterable
    clearable
  >
    <el-option v-for="item in issueList" :label="item.issueName" :value="item.issueName" :key="item.id"></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import QueryTrainClassIssue, {
    QueryTrainClassIssueListParam
  } from '@api/service/management/train-class/offlinePart/QueryTrainClassIssue'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'

  @Component({})
  export default class extends Vue {
    /**
     * 商品id
     */
    @Prop({ type: String, default: '' }) commodityId: string

    /**
     * 提示语
     */
    @Prop({ type: String, default: '请选择期别' }) placeholder: string

    /**
     * 是否禁用
     */
    @Prop({ type: Boolean, default: false }) disabled: boolean

    /**
     * 绑定的值
     */
    @Prop({ type: String, default: '' }) value: string

    /**
     * 培训形式
     */
    @Prop({ type: String, default: null }) trainMode: TrainingModeEnum
    @Prop({
      type: Array,
      default: () => {
        return new Array<IssueConfigDetail>()
      }
    })
    initOptions: Array<IssueConfigDetail>
    /**
     * 期别列表
     */
    issueList = new Array<IssueConfigDetail>()

    /**
     * 查询方法
     */
    queryTrainClassIssue = new QueryTrainClassIssue()

    /**
     * 选中的值
     */
    selectedValue = ''

    @Watch('value')
    valueChange() {
      this.selectedValue = this.value
    }

    @Watch('initOptions', { immediate: true })
    optionChange(val: Array<IssueConfigDetail>) {
      this.issueList = this.initOptions
    }

    /**
     * 监听方案变化
     * @param value
     */
    @Watch('commodityId', { immediate: true })
    async schemeChange(value: string) {
      if (!value || this.trainMode === TrainingModeEnum.online) {
        this.issueList = new Array<IssueConfigDetail>()
        this.selectedValue = ''
        this.$emit('input', this.selectedValue)
        return
      }
      await this.queryIssueList()
    }

    /**
     * 查询期别列表
     */
    async queryIssueList() {
      try {
        const params = new QueryTrainClassIssueListParam()
        params.commoditySkuId = this.commodityId
        this.issueList = await this.queryTrainClassIssue.queryTrainClassIssueList(params)
      } catch (e) {
        console.log(e)
      }
    }

    /**
     * 选中期别
     */
    selectChange() {
      this.$emit('input', this.selectedValue)
    }
  }
</script>
