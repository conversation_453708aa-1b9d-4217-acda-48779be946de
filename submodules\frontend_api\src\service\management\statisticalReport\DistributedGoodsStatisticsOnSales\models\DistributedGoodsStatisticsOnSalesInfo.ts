import StatisticInfoItem from '@api/service/management/statisticalReport/DistributedGoodsStatisticsOnSales/models/StatisticInfoItem'
import DistributionPricingMethodsEnumsClass, {
  DistributionPricingMethodsEnum
} from '@api/service/management/statisticalReport/enums/DistributionPricingMethodsEnumsClass'

export default class DistributedGoodsStatisticsOnSalesInfo {
  /**
   * 分销商品名称
   */
  distributedTradeName = ''
  /**
   * 授权分销商
   */
  authorizedDistributors = ''
  /**
   * 分销定价方式
   */
  distributionPricingMethods: DistributionPricingMethodsEnumsClass = new DistributionPricingMethodsEnumsClass()
  /**
   * 授权定价
   */
  licensingPricing = 0
  /**
   * 优惠申请
   */
  concessionApplication = 0
  /**
   * 合计
   */
  total: StatisticInfoItem = new StatisticInfoItem()
  /**
   * 个人缴费
   */
  individualContributions: StatisticInfoItem = new StatisticInfoItem()
  /**
   * 集体线上支付
   */
  collectiveOnlinePayment: StatisticInfoItem = new StatisticInfoItem()
  /**
   * 集体线下支付
   */
  collectiveOfflinePayment: StatisticInfoItem = new StatisticInfoItem()
  /**
   * 导入开通
   */
  importAndActivate: StatisticInfoItem = new StatisticInfoItem()
}
