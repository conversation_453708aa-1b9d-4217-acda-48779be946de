<template>
  <el-drawer title="当前专题选中的培训方案" :visible.sync="showDialog" size="800px" custom-class="m-drawer">
    <div class="drawer-bd">
      <!--表格-->
      <el-table
        stripe
        :data="trainClassCommodityList"
        class="m-table"
        max-height="600px"
        ref="trainClassCommodityListRef"
        v-loading="loading"
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="培训方案名称" min-width="240" fixed="left">
          <template slot-scope="scope">{{ scope.row.commodityBasicData.saleTitle }}</template>
        </el-table-column>
        <el-table-column label="培训属性" min-width="240">
          <template slot-scope="scope">
            <p v-show="getSkuPropertyName(scope.row, 'industry')">
              行业：{{ getSkuPropertyName(scope.row, 'industry') }}
            </p>
            <p v-show="getSkuPropertyName(scope.row, 'region')">地区：{{ getSkuPropertyName(scope.row, 'region') }}</p>
            <p v-show="getSkuPropertyName(scope.row, 'subjectType')">
              科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
            </p>
            <p v-show="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
              培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
            </p>
            <p v-show="getSkuPropertyName(scope.row, 'trainingMajor')">
              培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
            </p>
            <p v-show="getSkuPropertyName(scope.row, 'jobLevel')">
              技术等级：{{ getSkuPropertyName(scope.row, 'jobLevel') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'trainingObject')">
              培训对象：{{ getSkuPropertyName(scope.row, 'trainingObject') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'positionCategory')">
              岗位类别：{{ getSkuPropertyName(scope.row, 'positionCategory') }}
            </p>
            <p v-show="getSkuPropertyName(scope.row, 'year')">培训年度：{{ getSkuPropertyName(scope.row, 'year') }}</p>
            <p v-show="getSkuPropertyName(scope.row, 'learningPhase')">
              学段：{{ getSkuPropertyName(scope.row, 'learningPhase') }}
            </p>
            <p v-show="getSkuPropertyName(scope.row, 'discipline')">
              学科：{{ getSkuPropertyName(scope.row, 'discipline') }}
            </p>
          </template>
        </el-table-column>
      </el-table>
      <div class="f-mt10">
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </div>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button type="primary" @click="close">确 定</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { CommoditySkuRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { UiPage } from '@hbfe/common'
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  class UITrainClassCommodityDetail extends TrainClassCommodityVo {
    // 是否长期有效
    isLongTerm: boolean
    // 是否是人社行业
    isSocietyIndustry: boolean
    // 是否不开放报名
  }
  @Component
  export default class extends Vue {
    showDialog = false
    loading = false
    page: UiPage
    tableData = [{}]
    topicID = ''
    queryTrainClassCommodityList = new QueryTrainClassCommodityList()
    //培训班商品列表
    trainClassCommodityList: TrainClassCommodityVo[] = []
    //培训班商品请求参数
    filterCommodity = new CommoditySkuRequest()
    isZtGly = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    @Ref('trainClassCommodityListRef') trainClassCommodityListRef: any
    @Watch('showDialog')
    async doQuery(val: boolean) {
      if (val) {
        this.pageScheme()
      }
    }
    constructor() {
      super()
      this.page = new UiPage(this.pageScheme, this.pageScheme)
    }

    // 查询网校方案
    async querySchemeWx() {
      return await this.queryTrainClassCommodityList.queryTrainClassCommodityList(
        this.page,
        this.filterCommodity,
        undefined,
        undefined,
        false,
        false
      )
    }
    // 查询专题方案
    async querySchemeZt() {
      return await this.queryTrainClassCommodityList.queryTrainClassCommodityListInTrainingChannel(
        this.page,
        this.filterCommodity,
        undefined,
        undefined,
        false,
        false
      )
    }

    async pageScheme() {
      this.loading = true
      const arr = [this.topicID]
      this.filterCommodity.trainingChannelIds = arr
      //查询培训方案列表
      if (this.isZtGly) {
        this.trainClassCommodityList = await this.querySchemeZt()
      } else {
        this.trainClassCommodityList = await this.querySchemeWx()
      }
      //   ;(this.$refs['trainClassCommodityListRef'] as any)?.doLayout()
      this.trainClassCommodityListRef.doLayout()
      this.loading = false
      console.warn('查询培训方案列表', this.trainClassCommodityList)
      console.warn('查询培训方案参数', this.filterCommodity)
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: UITrainClassCommodityDetail, type: string): string {
      if (row.skuValueNameProperty[type]?.skuPropertyName) {
        const value = row.skuValueNameProperty[type].skuPropertyName
        const valuesArr = value.split('/'),
          lastIndex = valuesArr.length - 1
        return type === 'trainingMajor' && !row.isSocietyIndustry ? valuesArr[lastIndex] : value
      }
      return ''
    }
    close() {
      this.showDialog = false
    }
  }
</script>
