import { RegionResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import RegionVo from '@api/service/common/basic-data-dictionary/query/vo/RegionVo'

class RegionTreeVo {
  /**
   * 唯一标识
   */
  id: string
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 上级地区编号
   */
  parentId: string
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 地区名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
  /**
   * 是否为叶子节点
   */
  leaf: boolean
  /**
   * 子节点
   */
  code: string
  children: Array<RegionTreeVo> = undefined
  parentCode: string
  codePath: string
  /**
   * 是否被禁选
   */
  disabled: boolean = undefined
  static from(response: RegionVo, isLeaf?: boolean) {
    const regionTree = new RegionTreeVo()
    regionTree.id = response.id
    regionTree.name = response.name
    regionTree.parentId = response.parentId
    regionTree.regionPath = response.regionPath
    regionTree.enable = response.enable
    regionTree.sort = response.sort
    regionTree.leaf = isLeaf ? true : false
    regionTree.children = isLeaf ? undefined : new Array<RegionTreeVo>()
    return regionTree
  }
  /**
   * 只存在市级
   * @param dto
   * @returns
   */
  static fromBusiness(dto: RegionResponse) {
    const regionTree = new RegionTreeVo()
    regionTree.id = dto?.code
    regionTree.name = dto?.name
    regionTree.parentId = dto?.parentCode
    regionTree.regionPath = dto?.codePath
    regionTree.enable = true
    regionTree.sort = dto?.sort

    return regionTree
  }
}

export default RegionTreeVo
