import { ExcellentCourseItemResponse } from '@api/ms-gateway/ms-servicer-series-v1'

class ExcellentCourseConfigDetail {
  id: string
  sort: number
  createTime: string
  name: string
  categoryName: string
  cacheRemove = false

  static from(response: ExcellentCourseItemResponse) {
    const detail = new ExcellentCourseConfigDetail()
    detail.id = response.courseId
    detail.sort = response.sort
    detail.createTime = response.createdTime
    return detail
  }

  toJSON() {
    return {
      courseId: this.id,
      sort: this.sort
    }
  }

  doRemove() {
    this.cacheRemove = true
  }
}

export default ExcellentCourseConfigDetail
