<!--
 * @Author: z张仁榕
 * @Date: 2025-03-20 09:23:22
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-03-27 10:23:07
 * @Description:
-->
<template>
  <div>
    <el-card shadow="never" class="m-card f-mb15">
      <div class="m-attribute">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="registProtocol" class="m-collapse-item">
            <template slot="title">
              <div class="m-sub-tit f-align-center">
                <span class="tit-txt">注册协议</span>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                  <div slot="content">
                    <p>注册协议适用于学员账号、集体报名管理员账号注册环节。</p>
                    <p>开启后，学员、集体报名管理员注册时需勾选本协议。</p>
                  </div>
                </el-tooltip>
              </div>
            </template>
            <el-row type="flex" justify="center" class="width-limit f-mt20">
              <el-col :md="20" :lg="16" :xl="13">
                <el-form
                  ref="registerAgreementRef"
                  :model="saveParams.registerAgreement"
                  :rules="registRules"
                  label-width="140px"
                  class="m-form"
                >
                  <el-form-item label="是否开启注册协议：">
                    <el-switch
                      v-model="saveParams.registerAgreement.isOpen"
                      active-text="开启"
                      inactive-text="关闭"
                      class="m-switch"
                    />
                  </el-form-item>
                  <el-form-item label="协议名称：" prop="name" v-if="saveParams.registerAgreement.isOpen">
                    <el-input
                      v-model="saveParams.registerAgreement.name"
                      clearable
                      placeholder="请输入协议名称（不超过30个字）"
                    />
                  </el-form-item>
                  <el-form-item label="协议内容：" prop="content" v-if="saveParams.registerAgreement.isOpen">
                    <hb-tinymce-editor v-model="saveParams.registerAgreement.content"></hb-tinymce-editor>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item name="loginProtocol" class="m-collapse-item">
            <template slot="title">
              <div class="m-sub-tit f-align-center">
                <span class="tit-txt">登录协议</span>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                  <div slot="content">
                    <p>登录协议适用于学员账号、集体报名管理员账号登录环节（含账号登录及微信扫码登录）</p>
                    <p>开启后，学员、集体报名管理员登录时需勾选本协议。</p>
                  </div>
                </el-tooltip>
              </div>
            </template>
            <el-row type="flex" justify="center" class="width-limit f-mt20">
              <el-col :md="20" :lg="16" :xl="13">
                <el-form
                  ref="loginAgreementRef"
                  :model="saveParams.loginAgreement"
                  :rules="loginRules"
                  label-width="140px"
                  class="m-form"
                >
                  <el-form-item label="是否开启登录协议：">
                    <el-switch
                      v-model="saveParams.loginAgreement.isOpen"
                      active-text="开启"
                      inactive-text="关闭"
                      class="m-switch"
                    />
                  </el-form-item>
                  <el-form-item label="协议名称：" prop="name" v-if="saveParams.loginAgreement.isOpen">
                    <el-input
                      v-model="saveParams.loginAgreement.name"
                      clearable
                      placeholder="请输入协议名称（不超过30个字）"
                    />
                  </el-form-item>
                  <el-form-item label="协议内容：" prop="content" v-if="saveParams.loginAgreement.isOpen">
                    <hb-tinymce-editor v-model="saveParams.loginAgreement.content"></hb-tinymce-editor>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    <div class="m-btn-bar f-tc is-sticky-1">
      <el-button @click="doCancel">取消</el-button>
      <el-button type="primary" @click="saveProtocol" :loading="saveLoading">保存</el-button>
    </div>
    <el-dialog :visible.sync="cancelDialog" width="400px" class="m-dialog" title="提示" :close-on-click-modal="false">
      <div class="dialog-alert is-big">
        <p>确定要放弃编辑吗?</p>
      </div>
      <div slot="footer">
        <el-button type="info" @click="cancelDialog = false">取消</el-button>
        <el-button type="primary" @click="comfirm" :loading="cancelLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts">
  import MutationRegisterAndLogin from '@api/service/management/online-school-config/functionality-setting/mutation/MutationRegisterAndLogin'
  import AgreementSettingVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/registerAndLogin/AgreementSettingVo'
  import { Component, Ref, Vue } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    @Ref('registerAgreementRef') registerAgreementRef: any
    @Ref('loginAgreementRef') loginAgreementRef: any
    /**
     * 展开模块
     */
    activeNames = ['registProtocol', 'loginProtocol']
    // 查询
    mutationRegisterAndLogin = new MutationRegisterAndLogin()
    // 保存入参
    saveParams = new AgreementSettingVo()
    saveLoading = false
    // 取消弹窗
    cancelDialog = false
    cancelLoading = false
    // 注册协议校验规则
    registRules = {
      name: [
        { required: true, message: '请输入协议名称', trigger: 'blur' },
        { max: 30, message: '协议名称不可超过30个字', trigger: 'change' }
      ],
      content: [{ required: true, message: '请输入协议内容', trigger: 'blur' }]
    }

    // 登录协议校验规则
    loginRules = {
      name: [
        { required: true, message: '请输入协议名称', trigger: 'blur' },
        { max: 30, message: '协议名称不可超过30个字', trigger: 'change' }
      ],
      content: [{ required: true, message: '请输入协议内容', trigger: 'blur' }]
    }

    /**
     * 查询网校协议设置
     */
    async getOnlineSchoolAgreementSetting() {
      try {
        const res = await this.mutationRegisterAndLogin.getOnlineSchoolAgreementSetting()
        this.saveParams.registerAgreement = res.registerAgreement
        this.saveParams.loginAgreement = res.loginAgreement
      } catch (error) {
        console.log(error)
      } finally {
        this.cancelDialog = false
        this.cancelLoading = false
      }
    }

    /**
     * 保存网校协议设置
     */
    async saveProtocol() {
      try {
        this.saveLoading = true
        const registerValid = await this.registerAgreementRef.validate()
        const loginValid = await this.loginAgreementRef.validate()

        if (registerValid && loginValid) {
          if (!this.saveParams.registerAgreement.isOpen) {
            this.saveParams.registerAgreement.content = ''
            this.saveParams.registerAgreement.name = ''
          }
          if (!this.saveParams.loginAgreement.isOpen) {
            this.saveParams.loginAgreement.content = ''
            this.saveParams.loginAgreement.name = ''
          }

          const res = await this.mutationRegisterAndLogin.saveOnlineSchoolAgreementSetting(this.saveParams)
          if (res.isSuccess()) {
            this.$message.success('保存成功')
          } else {
            this.$message.error('保存失败')
          }
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.saveLoading = false
      }
    }

    async comfirm() {
      this.cancelLoading = true
      await this.getOnlineSchoolAgreementSetting()
    }

    // 取消操作
    doCancel() {
      this.cancelDialog = true
    }

    async created() {
      await this.getOnlineSchoolAgreementSetting()
    }
  }
</script>
