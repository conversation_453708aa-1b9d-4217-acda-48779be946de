import { StudentCourseAppraiseResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import Appraiser from '@api/service/customer/appraise/query/vo/Appraiser'

class CourseAppraiseListDetail {
  id = ''
  content = ''
  courseScore = 0
  teacherScore = 0
  appraiser: Appraiser = new Appraiser()
  appraisalTime = ''

  static from(remoteDto: StudentCourseAppraiseResponse) {
    const detail = new CourseAppraiseListDetail()
    detail.id = remoteDto.courseAppraisalInfo.studentCourseAppraisalId
    detail.content = remoteDto.courseAppraisalInfo.content
    detail.appraisalTime = remoteDto.courseAppraisalInfo.appraisalTime
    detail.appraiser = new Appraiser()
    detail.appraiser.id = remoteDto.courseAppraisalInfo.appraisalUserId
    detail.courseScore = remoteDto.courseAppraisalInfo.courseAppraise
    detail.teacherScore = remoteDto.courseAppraisalInfo.teacherAppraise
    return detail
  }
}

export default CourseAppraiseListDetail
