import { ReceiveAccountExtProperty, UpdateReceiveAccountRequest } from '@api/ms-gateway/ms-trade-configuration-v1'
import {
  ReceiveAccountConfigResponse,
  WechatPayEncryptionKeyDataResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import UpdateReceiveAccountVo from './UpdateReceiveAccountVo'

class UpdateWXPayReceiveAccountVo extends UpdateReceiveAccountVo {
  /**
   * API密钥
   */
  privateKeyPWD = ''
  /**
   * 公众账号ID
   */
  appId = ''
  /**
   * 微信证书密钥
   */
  merchantKey = ''
  /**
   * 微信证书文件名称
   */
  privateKeyFileName = ''
  /**
   * 微信证书路径
   */
  privateKeyPath = ''

  from(res: ReceiveAccountConfigResponse) {
    this.id = res.id
    this.accountType = res.accountType
    this.accountNo = res.accountNo
    this.paymentChannelId = res.paymentChannelId
    this.accountName = res.name
    this.refundWay = res.returnType
    this.taxPayerId = res.taxPayerId
    this.qrScanPrompt = res.qrScanPrompt
    if (res.encryptionKeyData.encryptionKeyType === 'WechatPay') {
      const temp = res.encryptionKeyData as WechatPayEncryptionKeyDataResponse
      this.appId = temp.appId
      this.merchantKey = temp.merchantKey
      this.privateKeyPWD = temp.privateKeyPWD
      this.privateKeyFileName = temp.privateKeyFileName
      this.privateKeyPath = temp.privateKeyPath
    }
  }

  to() {
    const updateReceiveAccountRequest = new UpdateReceiveAccountRequest()
    updateReceiveAccountRequest.receiveAccountId = this.id
    updateReceiveAccountRequest.name = this.accountName
    updateReceiveAccountRequest.qrScanPrompt = this.qrScanPrompt
    updateReceiveAccountRequest.refundWay = this.refundWay
    updateReceiveAccountRequest.taxPayerId = this.taxPayerId
    updateReceiveAccountRequest.properties = new Array<ReceiveAccountExtProperty>()
    this.updateProperties('appId', this.appId, updateReceiveAccountRequest.properties)
    this.updateProperties('merchantKey', this.merchantKey, updateReceiveAccountRequest.properties)
    this.updateProperties('privateKeyPWD', this.privateKeyPWD, updateReceiveAccountRequest.properties)
    this.updateProperties('privateKeyFileName', this.privateKeyFileName, updateReceiveAccountRequest.properties)
    this.updateProperties('privateKeyPath', this.privateKeyPath, updateReceiveAccountRequest.properties)
    return updateReceiveAccountRequest
  }

  private updateProperties(propertyName: string, propertyValue: string, properties: Array<ReceiveAccountExtProperty>) {
    const property = properties?.find((item) => item.name === propertyName)
    if (property) {
      property.value = propertyValue
    } else {
      const item = new ReceiveAccountExtProperty()
      item.name = propertyName
      item.value = propertyValue
      properties.push(item)
    }
    return properties
  }
}
export default UpdateWXPayReceiveAccountVo
