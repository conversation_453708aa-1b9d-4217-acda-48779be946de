import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 试卷出卷(组卷)方式
 */
export enum PublishPatternTypes {
  'AutomaticPublishPattern' = 0,
  'FixedPaper' = 1,
  'MultipleFixedPaperPublishPattern' = 2
}

class ExamPaperPublishPatternTypes extends AbstractEnum<PublishPatternTypes> {
  static enum = PublishPatternTypes

  constructor(status?: PublishPatternTypes) {
    super()
    this.current = status
    this.map.set(PublishPatternTypes.AutomaticPublishPattern, '智能组卷')
    this.map.set(PublishPatternTypes.FixedPaper, '固定组卷')
    this.map.set(PublishPatternTypes.MultipleFixedPaperPublishPattern, 'AB组卷')
  }
}

export default new ExamPaperPublishPatternTypes()
