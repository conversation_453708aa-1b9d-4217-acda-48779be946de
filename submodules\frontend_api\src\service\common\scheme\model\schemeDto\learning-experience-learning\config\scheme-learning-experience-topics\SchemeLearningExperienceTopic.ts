/**
 * @description 心得详情
 */
class SchemeLearningExperienceTopic {
  /**
   * 资源id
   */
  id: string
  /**
   * 心得名称
   */
  experienceTopicName: string
  /**
   * 心得描述
   */
  descriptionContent: string
  /**
   * 心得类型
   */
  participateTimeType: number
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 作答形式
   */
  participateType: number
  /**
   * 限制文件大小
   */
  submitLimitNum: number
  /**
   * 审核方式
   */
  auditType: number
  /**
   * 限制提交次数
   */
  submitLimitCount: number
  /**
   * 心得总分
   */
  totalScore: number
  /**
   * 是否纳入考核
   */
  isRequired: boolean
  /**
   * 心得成绩要求
   */
  passScore: number
}

export default SchemeLearningExperienceTopic
