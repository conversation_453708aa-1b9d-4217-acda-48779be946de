import { ResponseStatus } from '@hbfe/common'
import MsCourseResourceV1 from '@api/ms-gateway/ms-course-resource-v1'

class BizCoursePackage {
  constructor(coursePackageId: string) {
    this.id = coursePackageId
  }

  /*
   课程包id
   */
  id: string

  async changeStatus(type: boolean) {
    console.log(type)
    return new ResponseStatus(200)
  }

  async doEnable(): Promise<ResponseStatus> {
    return this.changeStatus(true)
  }

  async doDisable(): Promise<ResponseStatus> {
    return this.changeStatus(false)
  }

  /**
   * 删除课程包
   * @param id
   */
  async doRemove(id: string): Promise<ResponseStatus> {
    const result = await MsCourseResourceV1.removeCoursePackage(id)
    // 状态码 10001 删除失败，课程包已被使用
    return new ResponseStatus(result.status.code, result.status.getMessage())
  }

  /**
   * 拷贝课程包
   * @param id
   * @param newName
   */
  async doCopy(id: string, newName: string): Promise<ResponseStatus> {
    const result = await MsCourseResourceV1.copyCoursePackage({ id, newName })
    return new ResponseStatus(result.status.code, result.status.getMessage())
  }
}

export default BizCoursePackage
