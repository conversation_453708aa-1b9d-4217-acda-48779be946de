schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""添加地区管理员
		@param request 添加地区管理员请求
		@return
	"""
	addRegionAdministrator(request:RegionAdministratorAddRequest):GenernalResponse
	"""修改地区管理员
		@param request 修改地区管理员请求
		@return
	"""
	updateRegionAdministrator(request:RegionAdministratorUpdateRequest):GenernalResponse
}
"""添加地区管理员
	<AUTHOR> [2023/3/20 16:38]
"""
input RegionAdministratorAddRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.RegionAdministratorAddRequest") {
	"""姓名"""
	name:String!
	"""手机号"""
	phone:String!
	"""认证标识【必填】默认为行政区划代码"""
	area:String!
	"""账号"""
	account:String!
}
"""修改地区管理员
	<AUTHOR> [2023/3/20 16:41]
"""
input RegionAdministratorUpdateRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.RegionAdministratorUpdateRequest") {
	"""账号"""
	name:String!
	"""手机号"""
	phone:String
	"""行政区划"""
	theirArea:String!
	"""账号Id"""
	accountId:String!
}
"""<AUTHOR> [2023/7/11 20:54]"""
type GenernalResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.GenernalResponse") {
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}

scalar List
