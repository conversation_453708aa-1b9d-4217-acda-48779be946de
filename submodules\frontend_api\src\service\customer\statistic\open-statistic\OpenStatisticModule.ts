import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import platformStatisticReportQueryGateway, {
  OpenStatisticQueryParamsDTO
} from '@api/gateway/PlatformStatisticReportQuery'
import { ResponseStatus } from '@api/Response'
import { UnAuthorize } from '@api/Secure'
import { ChannelVendorCommodityOpenNumberQueryParamDTO } from '@api/gateway/PlatformCommodity'
import PlatformCommodity from '@api/gateway/PlatformCommodity'

export interface IOpenStatisticState {
  /**
   * 机构下对应的学员数
   * 代替countByTeachUnit: number
   */

  countByTrainingInstitution: number

  /**
   * 渠道商对某个机构的学员开通数
   */
  countByChannelVendor: number
}

@Module({ namespaced: true, store, dynamic: true, name: 'CustomerOpenStatisticModule' })
class OpenStatistic extends VuexModule implements IOpenStatisticState {
  //region state
  // 代替countByTeachUnit = 0
  countByTrainingInstitution = 0
  countByChannelVendor = 0
  reload = true
  //endregion

  //region action

  /**
   * 通过单位Id获取开通统计总开通数
   * 替换 getCountByTeachUnit
   */
  @Action
  @UnAuthorize
  async getCountByTrainingInstitution(trainingInstitutionId: string) {
    if (!this.reload) {
      return new ResponseStatus(200)
    }
    const queryParams = new OpenStatisticQueryParamsDTO()
    queryParams.trainingInstitutionIdList = [trainingInstitutionId]
    const response = await platformStatisticReportQueryGateway.getTotalOpenCount(queryParams)
    if (response.status.isSuccess()) {
      this.SET_COUNT_BY_TRAINING_INSTITUTION(response.data.realUserOpenNumber)
      this.SET_RELOAD(false)
    }
    return response.status
  }

  /**
   * 通过机构Id、渠道商id获取开通数 【渠道商主页】
   */
  @Action
  @UnAuthorize
  async getCountByChannelVendor(param: { trainingInstitutionId: string; channelVendorId: string }) {
    const queryParams = new ChannelVendorCommodityOpenNumberQueryParamDTO()
    queryParams.trainingInstitutionIdList = [param.trainingInstitutionId]
    queryParams.channelVendorIds = [param.channelVendorId]
    const response = await PlatformCommodity.countCommodityChannelVendorOpenNumber(queryParams)
    if (response.status.isSuccess()) {
      this.SET_COUNT_BY_CHANNEL_VENDOR(response.data)
    }
    return response.status
  }

  /**
   *  获取开通统计合计结果
   * 为渠道商提供不强制设置上下文接口
   */
  @Action
  @UnAuthorize
  async getCountByTrainingInstitutionWithoutContext(trainingInstitutionId: string) {
    if (!this.reload) {
      return new ResponseStatus(200)
    }
    const queryParams = new OpenStatisticQueryParamsDTO()
    queryParams.trainingInstitutionIdList = [trainingInstitutionId]
    const response = await platformStatisticReportQueryGateway.getTotalOpenStatisticForChannelVendorWithoutContext(
      queryParams
    )
    if (response.status.isSuccess()) {
      this.SET_COUNT_BY_TRAINING_INSTITUTION(response.data.realUserOpenNumber)
      this.SET_RELOAD(false)
    }
    return response.status
  }
  //endregion

  //region mutation
  @Mutation
  SET_RELOAD(reload: boolean) {
    this.reload = reload
  }
  @Mutation
  // 代替SET_COUNT_BY_TEACH_UNIT
  SET_COUNT_BY_TRAINING_INSTITUTION(count: number) {
    this.countByTrainingInstitution = count
  }

  @Mutation
  SET_COUNT_BY_CHANNEL_VENDOR(count: number) {
    this.countByChannelVendor = count
  }
  //endregion
}

export default getModule(OpenStatistic)
