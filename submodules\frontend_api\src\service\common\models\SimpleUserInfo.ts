import { TeacherResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

/**
 * 简单用户对象
 */
class SimpleUserInfo {
  id = ''
  // 名称
  name = ''
  // 头像
  avatar = ''
  // 描述信息
  description = ''

  static from(response: any) {
    const simpleUserInfo = new SimpleUserInfo()
    Object.assign(simpleUserInfo, response)
    return simpleUserInfo
  }

  static newSimpleUserInfoById(teacherId: string) {
    const simpleUserInfo = new SimpleUserInfo()
    simpleUserInfo.id = teacherId
    return simpleUserInfo
  }

  from(response: TeacherResponse) {
    Object.assign(this, response)
    this.id = response.id
    this.name = response.name
    this.avatar = `${ConfigCenterModule.getFrontendApplication(frontendApplication.mfsHost)}${response.photo}`
    this.description = response.aboutsContent
  }
}

export default SimpleUserInfo
