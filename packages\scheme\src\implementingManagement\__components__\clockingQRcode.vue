<template>
  <el-drawer title="考勤二维码" :visible.sync="openDialog" size="900px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
        通过使用微信扫一扫二维码签到/签退，不校验定位地址
      </el-alert>
      <!--条件查询-->
      <el-row :gutter="16" class="m-query f-mt10">
        <el-form :inline="true" label-width="auto">
          <el-col :span="10">
            <el-form-item label="考勤类型">
              <el-select placeholder="请选择考勤类型" v-model="attendanceQRCodeManage.attendanceType">
                <el-option v-for="item in attendanceType" :key="item.code" :label="item.desc" :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button type="primary" @click="doQuery()">查询</el-button>
              <el-button @click="doReset()">重置</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <!--表格-->
      <el-table stripe :data="attendanceQRCodeManage.list" max-height="500px" class="m-table f-mt10">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="日期" min-width="120">
          <template v-slot="{ row }">{{ row.date }}</template>
        </el-table-column>
        <el-table-column label="课程名称" min-width="160">
          <template v-slot="{ row }">{{ row.courseName }}</template>
        </el-table-column>
        <el-table-column label="考勤类型" min-width="160" align="center">
          <template v-slot="{ row }">{{ getAttendanceTypeDesc(row.attendanceType) }}</template>
        </el-table-column>
        <el-table-column label="签到/签退时段" min-width="180">
          <template v-slot="{ row }"> {{ row.checkTime.begin || '-' }} 至 {{ row.checkTime.end || '-' }} </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template v-slot="{ row }">
            <el-button type="text" @click="downloadPoster(row)">下载二维码</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button type="primary" @click="openDialog = false">关闭</el-button>
    </div>
    <ClockingQRcodeDialog ref="clockingQRcodeDialogRef" :schemeId="schemeId"></ClockingQRcodeDialog>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Ref, Prop } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import ClockingQRcodeDialog from './clockingQRcodeDialog.vue'
  import AttendanceQRCodeDto from '@api/service/management/implement/models/AttendanceQRCodeDto'
  import AttendanceQRCodeManage from '@api/service/management/implement/AttendanceQRCodeManage'
  import { AttendanceTypeEnum } from '@api/service/management/implement/enums/AttendanceTypeEnum'

  @Component({
    components: { ClockingQRcodeDialog }
  })
  export default class extends Vue {
    @Ref('clockingQRcodeDialogRef') clockingQRcodeDialogRef: ClockingQRcodeDialog
    @Prop({
      type: String,
      default: ''
    })
    schemeId: string
    periodId = ''
    openDialog = false
    attendanceQRCodeManage: AttendanceQRCodeManage = new AttendanceQRCodeManage('')
    // 分页
    page = new UiPage(this.doQuery, this.doQuery)
    // 考勤类型
    attendanceType = [
      { code: 1, desc: '签到' },
      { code: 2, desc: '签退' }
    ]

    async open(row: AttendanceQRCodeManage, periodId: string) {
      this.openDialog = true
      this.attendanceQRCodeManage = row
      this.periodId = periodId
      this.doQuery()
    }
    // 查询
    async doQuery() {
      await this.attendanceQRCodeManage.queryList(this.page)
    }
    //重置
    doReset() {
      this.attendanceQRCodeManage = new AttendanceQRCodeManage(this.periodId)
      this.page.currentChange(1)
    }

    downloadPoster(row: AttendanceQRCodeDto) {
      this.clockingQRcodeDialogRef.periodId = this.periodId
      this.clockingQRcodeDialogRef.open(row)
    }
    // 获取考勤类型
    getAttendanceTypeDesc(type: AttendanceTypeEnum) {
      return type == AttendanceTypeEnum.signIn ? '签到' : '签退'
    }
  }
</script>
