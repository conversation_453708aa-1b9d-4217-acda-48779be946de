import { MenuTypeEnum } from '@api/service/customer/online-school-config/online-school-partal-config/query/enum/MenuTypeEnum'
import { MenuInfo } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'

/**
 * @description 栏目
 */
class MenuVo {
  /**
   * 唯一标识
   */
  id: string
  /**
   * 栏目名称
   */
  name: string
  /**
   * 父级id
   */
  parentId: string
  /**
   * 栏目类型
   */
  type: MenuTypeEnum
  /**
   * 跳转链接（或许需要前端提供）
   */
  link: string
  /**
   * 关联id
   */
  referenceId: string
  /**
   * 是否有子栏目
   */
  allowChildren: string
  /**
   * 排序
   */
  sort: number
  /**
   * code 表征唯一性
   */
  code: string

  static from(response: MenuInfo) {
    const menu = new MenuVo()
    menu.id = response.id
    menu.parentId = response.parentId
    menu.name = response.name
    menu.type = response.menuType
    menu.link = response.link
    menu.referenceId = response.referenceId
    menu.code = response.code
    return menu
  }
}

export default MenuVo
