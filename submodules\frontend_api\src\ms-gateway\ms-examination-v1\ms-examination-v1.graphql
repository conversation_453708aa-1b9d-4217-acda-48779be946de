"""独立部署的微服务,K8S服务名:ms-examination-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getServiceTime:Long @optionalLogin
	"""学员考试id查询
		@param token 作答token
		@return
	"""
	getSurplusExamDegree(token:String):Int
}
type Mutation {
	"""申请考试
		@param studentToken 学员token
		@return 考试token
	"""
	applyExam(studentToken:String):String
	"""考试一键作答
		@param request 学员考试自动作答请求
	"""
	autoExam(request:StudentExaminationAutoAnswerRequest):Void
	"""删除考试
		@param examId 考试id[必填]
	"""
	deleteExam(examId:String):Void
	"""作废学员方案下全部答卷
		@param studentReLearnToken 学员重学token
	"""
	invalidSchemeExamAnswerPapers(studentReLearnToken:String):Void
	"""作废学员考试答卷
		@param request 作废学员考试答卷请求
	"""
	invalidStudentExamAnswerPaper(request:InvalidStudentExamAnswerPaperRequest):Void
}
"""作废学员考试答卷请求
	<AUTHOR>
"""
input InvalidStudentExamAnswerPaperRequest @type(value:"com.fjhb.ms.examination.v1.kernel.gateway.graphql.request.InvalidStudentExamAnswerPaperRequest") {
	"""学员考试id"""
	studentExamId:String
	"""答卷id集合"""
	answerPaperIds:[String]
}
"""学员考试自动作答请求
	<AUTHOR>
"""
input StudentExaminationAutoAnswerRequest @type(value:"com.fjhb.ms.examination.v1.kernel.gateway.graphql.request.StudentExaminationAutoAnswerRequest") {
	"""学员学习token"""
	studentToken:String!
	"""合格分"""
	qualifiedScore:Double!
}

scalar List
