<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2024-09-11 19:07:04
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-09-25 15:14:09
-->
<route-params content="/:id"></route-params>
<route-meta>
{
"isMenu":true,
"hideMenu": true,
"onlyShowOnTab":true,
"title": "个人订单退款详情"
}
</route-meta>

<script lang="ts">
  import RefundPersonalDetail from '@hbfe/jxjy-admin-trade/src/refund/personal/detail.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    refundDetail: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    approve: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    retryRecycleRefund: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    confirmRefund: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    continueRefund: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY]
  })
  export default class extends RefundPersonalDetail {}
</script>
