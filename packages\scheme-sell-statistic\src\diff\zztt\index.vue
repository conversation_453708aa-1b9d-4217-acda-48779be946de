<route-meta>
{
"isMenu": true,
"title": "方案开通统计",
"sort": 1,
"icon": "icon-ribaotongji"
}
</route-meta>
<template>
  <SchemeSellStatisticZztt ref="schemeSellStatisticZzttRef">
    <template #promotion-select="{ localSkuProperty }">
      <el-form-item label="分销推广">
        <biz-promotion-select v-model="localSkuProperty.isFx"></biz-promotion-select>
      </el-form-item>
    </template>
    <template #sale-channel="{ localSkuProperty }">
      <el-form-item label="销售渠道">
        <biz-sale-channel-select v-model="localSkuProperty.tradeChannels"></biz-sale-channel-select>
      </el-form-item>
    </template>

    <template #remove-plan="{ localSkuProperty }">
      <el-form-item label="剔除培训方案">
        <biz-remove-training-plan
          v-model="localSkuProperty.removePlan"
          placeholder="请选择不纳入统计的培训方案"
        ></biz-remove-training-plan>
      </el-form-item>
    </template>
  </SchemeSellStatisticZztt>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import SchemeSellStatistic from '@hbfe/jxjy-admin-schemeSellStatistic/src/index.vue'
  import { TradeReportRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import bizSaleChannelSelect from '@hbfe/jxjy-admin-components/src/biz-sale-channel-select.vue'
  import bizRemoveTrainingPlan from '@hbfe/jxjy-admin-components/src/biz/biz-remove-training-plan.vue'
  import { string } from 'fast-glob/out/utils'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-common/src/models/sku'

  class NewSchemeSkuProperty extends SchemeSkuProperty {
    // 销售渠道
    tradeChannels: number = null
    // 是否是分销
    isFx: number = null
    // 移除的方案
    removePlan: string[] = []
  }
  @Component
  class SchemeSellStatisticZztt extends SchemeSellStatistic {
    /**
     * 本地筛选项
     */
    localSkuProperty: NewSchemeSkuProperty = new NewSchemeSkuProperty()
    getPageQueryParams() {
      this.getLocalSkuProperty()
      this.tradeReportRequest.commoditySku.saleTitle = this.schemeName || undefined
      this.tradeReportRequest.commoditySku.excludeCommodityIdlist = this.localSkuProperty.removePlan || []
      this.configureTrainSchemeQueryParam()
      const tradeFlag = this.localSkuProperty.tradeChannels ?? null //销售渠道
      const isFx = this.localSkuProperty.isFx ?? null //是否分销推广
      // 分销选了否 排初传1
      if (isFx === 0 && tradeFlag === null) {
        //
        this.tradeReportRequest.saleChannel = null
        this.tradeReportRequest.excludedSaleChannels = [1]
      }
      // 分销选了是 saleChannel传1
      if (isFx === 1 && tradeFlag === null) {
        this.tradeReportRequest.saleChannel = 1
        this.tradeReportRequest.excludedSaleChannels = null
        //
      }
      // 分销选了是 华医网也选了 saleChannel不穿 排初传[0123]
      if (isFx === 1 && tradeFlag != null) {
        this.tradeReportRequest.saleChannel = null
        this.tradeReportRequest.excludedSaleChannels = [0, 1, 2, 3]
      }
      // 分销选了否 华医网也选了  saleChannel穿3 排初传[1]
      if (isFx === 0 && tradeFlag != null) {
        this.tradeReportRequest.saleChannel = 3
        this.tradeReportRequest.excludedSaleChannels = [1]
      }
      // 单选华医网 saleChannel穿3
      if (isFx === null && tradeFlag != null) {
        this.tradeReportRequest.saleChannel = 3
        this.tradeReportRequest.excludedSaleChannels = null
      }
      if (isFx === null && tradeFlag == null) {
        this.tradeReportRequest.saleChannel = null
        this.tradeReportRequest.excludedSaleChannels = null
      }
    }
  }

  @Component({
    components: {
      SchemeSellStatisticZztt,
      bizSaleChannelSelect,
      bizRemoveTrainingPlan
    }
  })
  export default class extends Vue {
    @Ref('schemeSellStatisticZzttRef') schemeSellStatisticZzttRef: SchemeSellStatisticZztt
  }
</script>
