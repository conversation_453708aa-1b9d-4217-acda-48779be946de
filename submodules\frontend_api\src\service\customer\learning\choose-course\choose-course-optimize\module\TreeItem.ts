import CourseDetail from './CourseDetail'
import { Page, UiPage } from '@hbfe/common'
import QueryCourseListRequest from './QueryCourseListRequest'
import SelectModule from '../SelectModule'
import MsCourseLearning, {
  CanChooseCourseSortRequest,
  CanStudentChooseCourseRequest,
  CanStudentChooseCourseSortEnum,
  CourseInfoRequest,
  CourseOfCourseTrainingOutlineRequest,
  SortTypeEnum
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import ChooseCourseGateway, { ChooseCourseRequest } from '@api/ms-gateway/ms-choose-course-v1'
import CourseLearningForestage, {
  StudentChooseCourseTrainingOutlineRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
/**
 * 列表枚举
 */
export enum SortEnum {
  /**
   * 默认
   */
  DEFAULT = 'DEFAULT',
  /**
   * 学时
   */
  PERIOD = 'PERIOD',
  /**
   * 综合评价
   */
  TOTAL_APPRAISE = 'TOTAL_APPRAISE',
  /**
   * 报名人数
   */
  COURSE_COUNT = 'COURSE_COUNT'
}
export class SortType {
  sortEnum: SortEnum
  sort: SortTypeEnum
}
export default class TreeItem {
  /**
   *  节点ID
   */
  outlineId: string
  /**
   * 父节点ID
   */
  fatherOutlineId: string
  /**
   * 父节点Name
   */
  fatherOutlineName: string
  /**
   * 课程包ID
   */
  coursePackageId: string
  /**
   * 属于第几级分类
   */
  level: number
  /**
   * 节点名称
   */
  name: string
  /**
   * 子节点集合
   */
  childrenTreeItem: TreeItem[] = []
  /**
   * 特殊属性 用于处理更多课程（除自己本身工种外的工种)
   */
  gatherTreeItem: TreeItem[] = []
  /**
   * 已选学时 -- 提交
   */
  selectCourseSumbit = 0
  /**
   * 总要求学时 -- 配置
   */
  courseConfig = 0
  /**
   * 课程列表
   */
  courseList: CourseDetail[] = []
  /**
   * 智能选课大纲下的课程id数组
   */
  outlineCourseIdList: string[] = []
  /**
   * 智能选的课程id数组
   */
  courseIdList: string[] = []
  /**
   * 还有未选课程的大纲id数组（最低一级）
   * 用于UI筛选还存在未选课程的课程分类
   */
  hasCourseOutlineIdList: string[] = []
  /**
   * 查询
   * @param page 分页
   * @param param 入参
   * @param selectCourse 已选但未提交的树
   * @param sort
   PERIOD = 'PERIOD', 学时
   TOTAL_APPRAISE = 'TOTAL_APPRAISE', 综合评价
   CHOOSE_COURSE_COUNT = 'CHOOSE_COURSE_COUNT',课程人数
   OUTLINE_ID = 'OUTLINE_ID', 大纲ID
   COURSE_TYPE = 'COURSE_TYPE', 课程类型
   SORT = 'SORT'
   * @returns
   */
  async queryCourseList(page: Page, param: QueryCourseListRequest, selectCourse: SelectModule, sortType?: SortType[]) {
    this.courseList = await this.query(page, param, selectCourse, sortType)
  }

  /**
   * 滚动加载课程数据
   * @param page 分页
   * @param param 入参
   * @param selectCourse 已选但未提交的树
   * @param sort
   PERIOD = 'PERIOD', 学时
   TOTAL_APPRAISE = 'TOTAL_APPRAISE', 综合评价
   CHOOSE_COURSE_COUNT = 'CHOOSE_COURSE_COUNT',课程人数
   OUTLINE_ID = 'OUTLINE_ID', 大纲ID
   COURSE_TYPE = 'COURSE_TYPE', 课程类型
   SORT = 'SORT'
   */
  async rollingLoad(page: Page, param: QueryCourseListRequest, selectCourse: SelectModule, sortType?: SortType[]) {
    const result = await this.query(page, param, selectCourse, sortType)
    this.courseList.push(...result)
  }
  /**
   * 移除课程
   */
  changeSelect(course: CourseDetail) {
    this.courseList.forEach(item => {
      if (item.courseId === course.courseId && item.outlineId === course.outlineId) {
        item.isSelect = false
      }
    })
  }
  /**
   * 查询方法
   */
  private async query(page: Page, param: QueryCourseListRequest, selectCourse: SelectModule, sortType?: SortType[]) {
    const request = QueryCourseListRequest.from(param)

    if (this.outlineId.includes('all')) {
      request.courseOfCourseTrainingOutline.outlineIds = this.gatherTreeItem.map(item => item.outlineId)
    } else {
      request.courseOfCourseTrainingOutline.outlineIds = [this.outlineId]
    }

    // request.course.excludeCourseIds = excludeOutlineIds
    const requeseSort = new Array<CanChooseCourseSortRequest>()
    sortType.forEach(item => {
      switch (item.sortEnum) {
        case SortEnum.COURSE_COUNT:
          requeseSort.push({
            chooseCourseSort: CanStudentChooseCourseSortEnum.CHOOSE_COURSE_COUNT,
            sortType: item.sort
          })
          break
        case SortEnum.DEFAULT:
          requeseSort.push({
            chooseCourseSort: CanStudentChooseCourseSortEnum.COURSE_TYPE,
            sortType: item.sort
          })
          requeseSort.push({
            chooseCourseSort: CanStudentChooseCourseSortEnum.COURSE_CONFIG_SORT,
            sortType: item.sort
          })
          break
        case SortEnum.PERIOD:
          requeseSort.push({
            chooseCourseSort: CanStudentChooseCourseSortEnum.PERIOD,
            sortType: item.sort
          })
          break
        case SortEnum.TOTAL_APPRAISE:
          requeseSort.push({
            chooseCourseSort: CanStudentChooseCourseSortEnum.TOTAL_APPRAISE,
            sortType: item.sort
          })
          break
      }
    })

    // 请求课程列表
    const response = await MsCourseLearning.pageCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself(
      {
        page: page,
        request,
        sort: requeseSort
      }
    )
    page.totalPageSize = response.data.totalPageSize
    page.totalSize = response.data.totalSize
    // const courseMap: Map<string, CourseInSchemeResponse> = new Map()
    // const courseIds = response.data.currentPageData.map(item => {
    //   courseMap.set(item.course.courseId, item)
    // })
    // 请求具体课程信息
    // const rqNum = courseIds.length / 200
    // const rqList = new Array<CourseV2Request>()
    // for (let i = 0; i < rqNum; i++) {
    //   const request = new CourseV2Request()
    //   request.pageNo = 1
    //   request.pageSize = 200
    //   request.courseIdList = courseIds.slice(i * 200, i * 200 + 200)
    //   rqList.push(request)
    // }
    // const req = new RewriteGraph<CourseResponsePage, CourseV2Request>(
    //   MsCourseLearning._commonQuery,
    //   pageCourseV2InServicer
    // )
    // await req.request(rqList)
    // const keys = [...req.indexMap.keys()]
    // const lastQueryResult = new Array<CourseResponse>()
    // keys.map((key, index) => {
    //   lastQueryResult.push(...req.indexMap.get(index).currentPageData)
    // })
    const result = new Array<CourseDetail>()
    // const teacherIds: string[] = []
    response.data.currentPageData.forEach(item => {
      let name = ''
      if (this.outlineId.includes('all')) {
        name = this.gatherTreeItem.find(it => it.outlineId === item.courseOfCourseTrainingOutline.outlineId)?.name
      } else {
        name = this.name
      }
      const detail = CourseDetail.from(item, name)
      //   const temp = courseMap.get(item.course.courseId)
      //   detail.outlineIdInBackstage = temp.courseOfCourseTrainingOutline.outlineId
      //   detail.score = temp.totalAppraise === 0 ? 500 : temp.totalAppraise
      //   detail.chooseCoursePeopleCount = temp.chooseCoursePeopleCount
      if (this.outlineId.includes('all')) {
        const node = this.gatherTreeItem.find(it => it.outlineId === item.courseOfCourseTrainingOutline.outlineId)
        detail.fatherOutlineId = node.fatherOutlineId
        detail.fatherOutlineName = node.fatherOutlineName
        detail.level = node.level
      } else {
        detail.fatherOutlineId = this.fatherOutlineId
        detail.fatherOutlineName = this.fatherOutlineName
        detail.level = this.level
      }
      selectCourse.isSelect.forEach(item => {
        if (item.outlineId === detail.outlineId) {
          item.courseList.forEach(it => {
            if (it.courseId === detail.courseId) {
              detail.isSelect = true
            }
          })
        }
      })
      //   teacherIds.push(...detail.teacherIds)
      result.push(detail)
    })
    // 获取教师
    // teacherIds = [...new Set(teacherIds)]
    // if (teacherIds.length > 0) {
    //   const response = await MsCourseLearning.listTeacherInServicer(teacherIds)
    //   const teacherMap: Map<string, string> = new Map()
    //   response.data.forEach(item => teacherMap.set(item.id, item.name))
    //   result.forEach(item => {
    //     const teasherNames: string[] = []
    //     item.teacherIds.forEach(temp => teasherNames.push(teacherMap.get(temp)))
    //     item.teacher = teasherNames.join('，')
    //   })
    // }
    return result
  }
  /**
   * 智能选课一键选课获取课程id
   */
  async getSmartChooseCourse(chooseToken: string) {
    const res = await ChooseCourseGateway.smartChooseCourse(chooseToken)
    this.outlineCourseIdList = (res?.data.recommendInfo?.currentChooseCourseList || []).map(item => {
      return item.courseOfOutlineId
    })
    this.courseIdList = (res?.data.recommendInfo?.currentChooseCourseList || []).map(item => {
      return item.courseId
    })
    const outlineIdList = (res?.data?.recommendInfo?.currentChooseCourseList || []).map(item => {
      return item.outlineId
    })
    this.hasCourseOutlineIdList = outlineIdList.filter((item, index) => {
      return outlineIdList.indexOf(item) === index
    })
    // console.log(this.hasCourseOutlineIdList, 'lzh hasCourseOutlineIdList')
    return this.outlineCourseIdList
  }
  /**
   * 大纲下的课程id转换课程详情
   */
  async intelligentLearningQuickChooseCourse(
    studentNo: string,
    selectCourse: SelectModule,
    sortType?: SortType[],
    courseName?: string
  ) {
    const requeseSort = this.pushSortType(sortType)
    const request = new CanStudentChooseCourseRequest()
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    request.course = new CourseInfoRequest()
    if (this.outlineId.includes('all')) {
      request.courseOfCourseTrainingOutline.outlineIds = this.gatherTreeItem.map(item => item.outlineId)
    } else {
      request.courseOfCourseTrainingOutline.outlineIds = [this.outlineId]
    }
    request.studentNo = studentNo
    // 从更多课程获取的课程信息中取outlineCourseId添加到this.outlineCourseIdList中查询课程详情
    const oldArr = this.outlineCourseIdList
    // 数组去重
    const newArr = oldArr.filter(function(item, index) {
      return oldArr.indexOf(item) === index
    })
    request.courseOfCourseTrainingOutline.courseOfCourseTrainingOutlineIds = newArr
    request.course.courseName = courseName
    const response = await MsCourseLearning.listCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself(
      {
        request,
        sort: requeseSort
      }
    )
    const result = new Array<CourseDetail>()
    const outlineIdList = Array<string>()
    response.data?.forEach(item => {
      let name = ''
      if (this.outlineId.includes('all')) {
        name = this.gatherTreeItem.find(it => it.outlineId === item.courseOfCourseTrainingOutline.outlineId)?.name
      } else {
        name = this.name
      }
      const detail = CourseDetail.from(item, name)
      if (this.outlineId.includes('all')) {
        const node = this.gatherTreeItem.find(it => it.outlineId === item.courseOfCourseTrainingOutline.outlineId)
        detail.fatherOutlineId = node.fatherOutlineId
        detail.fatherOutlineName = node.fatherOutlineName
        detail.level = node.level
      } else {
        detail.fatherOutlineId = this.fatherOutlineId
        detail.fatherOutlineName = this.fatherOutlineName
        detail.level = this.level
      }
      // console.log(selectCourse.isSelect, 'selectCourse.isSelect')
      // console.log(detail.courseId, 'courseId')
      // console.log(detail.outlineId, 'outlineId')

      selectCourse.isSelect.forEach(item => {
        if (item.outlineId === detail.outlineId) {
          item.courseList.forEach(it => {
            if (it.courseId === detail.courseId) {
              detail.isSelect = true
            }
          })
        }
      })
      result.push(detail)
      outlineIdList.push(item.courseOfCourseTrainingOutline.outlineId)
    })
    if (this.outlineId.includes('all')) {
      this.hasCourseOutlineIdList = outlineIdList.filter((item, index) => {
        return outlineIdList.indexOf(item) === index
      })
    }
    this.courseList = result
    console.log(result, 'resultresultresult')

    return result
  }
  /**
   * 获取更多课程的大纲id列表
   * 类似智能选课开始获取有数据的课程分类（只调一次）
   */
  async moreCourseGetOutLineId(courseIdList: string[], studentNo: string, schemeId: string) {
    const request = new StudentChooseCourseTrainingOutlineRequest()
    request.studentNo = studentNo
    request.schemeId = schemeId
    request.excludeCourseIds = courseIdList
    const response = await CourseLearningForestage.listCanChooseCourseTrainingOutlineOfChooseCourseLearningSceneInMyself(
      request
    )
    this.hasCourseOutlineIdList = []
    response.data?.forEach(item => {
      this.hasCourseOutlineIdList.push(item.outlineId)
    })
  }

  /**
   * 课程id转换课程详情
   * @param courseIdList 课程id列表
   * @param isReverse 是否反向查询可选课里没选中的课程
   * @param studentNo 学号
   * @param selectCourse 用于切换课程分类时记忆选中状态
   * isReverse为true是更多课程 courseId为智能选课里的课程/false是更多课程跳转智能选课后添加的课程 courseId为从更多课程添加的课程
   */
  async getCourseDetailById(
    page: Page,
    courseIdList: string[],
    isReverse: boolean,
    studentNo: string,
    selectCourse?: SelectModule,
    sortType?: SortType[]
  ) {
    const requeseSort = this.pushSortType(sortType)
    const request = new CanStudentChooseCourseRequest()
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    if (this.outlineId.includes('all')) {
      request.courseOfCourseTrainingOutline.outlineIds = this.gatherTreeItem.map(item => item.outlineId)
    } else {
      request.courseOfCourseTrainingOutline.outlineIds = [this.outlineId]
    }
    request.studentNo = studentNo
    request.course = new CourseInfoRequest()
    if (isReverse) {
      request.course.excludeCourseIds = courseIdList
    } else {
      // 用于更多课程选完后添加到智能选课的idList
      request.course.courseIdList = courseIdList
    }
    const response = await MsCourseLearning.pageCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself(
      {
        page: page,
        request,
        sort: requeseSort
      }
    )
    page.totalSize = response?.data?.totalSize
    page.totalPageSize = response?.data?.totalPageSize
    const result = new Array<CourseDetail>()
    // const teacherIds: string[] = []
    const outlineIdList = Array<string>()
    response.data?.currentPageData.forEach(item => {
      let name = ''
      if (this.outlineId.includes('all')) {
        name = this.gatherTreeItem.find(it => it.outlineId === item.courseOfCourseTrainingOutline.outlineId)?.name
      } else {
        name = this.name
      }

      const detail = CourseDetail.from(item, name)
      if (this.outlineId.includes('all')) {
        const node = this.gatherTreeItem.find(it => it.outlineId === item.courseOfCourseTrainingOutline.outlineId)
        detail.fatherOutlineId = node.fatherOutlineId
        detail.fatherOutlineName = node.fatherOutlineName
        detail.level = node.level
      } else {
        detail.fatherOutlineId = this.fatherOutlineId
        detail.fatherOutlineName = this.fatherOutlineName
        detail.level = this.level
      }
      selectCourse.isSelect.forEach(item => {
        if (item.outlineId === detail.outlineId) {
          item.courseList.forEach(it => {
            if (it.courseId === detail.courseId) {
              detail.isSelect = true
            }
          })
        }
      })

      result.push(detail)
      outlineIdList.push(item.courseOfCourseTrainingOutline.outlineId)
    })
    if (this.outlineId.includes('all')) {
      this.hasCourseOutlineIdList = outlineIdList.filter((item, index) => {
        return outlineIdList.indexOf(item) === index
      })
    }
    if (page.pageNo === 1) {
      this.courseList = result
    } else {
      this.courseList.push(...result)
    }
    return result
  }

  /**
   * 智能选课排序传参push
   */
  pushSortType(sortType: SortType[]) {
    const requeseSort = new Array<CanChooseCourseSortRequest>()
    if (!sortType.length) return
    sortType.forEach(item => {
      switch (item.sortEnum) {
        case SortEnum.COURSE_COUNT:
          requeseSort.push({
            chooseCourseSort: CanStudentChooseCourseSortEnum.CHOOSE_COURSE_COUNT,
            sortType: item.sort
          })
          break
        case SortEnum.DEFAULT:
          requeseSort.push({
            chooseCourseSort: CanStudentChooseCourseSortEnum.COURSE_TYPE,
            sortType: item.sort
          })
          requeseSort.push({
            chooseCourseSort: CanStudentChooseCourseSortEnum.COURSE_CONFIG_SORT,
            sortType: item.sort
          })
          break
        case SortEnum.PERIOD:
          requeseSort.push({
            chooseCourseSort: CanStudentChooseCourseSortEnum.PERIOD,
            sortType: item.sort
          })
          break
        case SortEnum.TOTAL_APPRAISE:
          requeseSort.push({
            chooseCourseSort: CanStudentChooseCourseSortEnum.TOTAL_APPRAISE,
            sortType: item.sort
          })
          break
      }
    })
    return requeseSort
  }

  static from(dto: DTOTreeModule, fatherOutline: DTOTreeModule, level: number) {
    const treeItem = new TreeItem()
    treeItem.outlineId = dto.id
    treeItem.fatherOutlineId = fatherOutline.id
    treeItem.fatherOutlineName = fatherOutline.name
    treeItem.coursePackageId = dto.coursePackageId
    treeItem.level = level
    treeItem.name = dto.name
    return treeItem
  }
}
/**
 * 后端栏目模型
 */
export class DTOTreeModule {
  /**
   * 必修1 选修2
   */
  category: 1 | 2
  /**
   * 节点上的课程包ID
   */
  coursePackageId: string
  /**
   * 节点ID
   */
  id: string
  /**
   * 节点名称
   */
  name: string
  /**
   * 操作
   */
  operation: 1
  /**
   * 排序
   */
  sort: 0
  /**
   * 子节点
   */
  childOutlines: DTOTreeModule[]
}
