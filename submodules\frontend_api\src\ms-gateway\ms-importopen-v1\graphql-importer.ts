import exportExcel from './queries/exportExcel.graphql'
import exportExcelAllData from './queries/exportExcelAllData.graphql'
import findImportDataByPage from './queries/findImportDataByPage.graphql'
import findImportDataWithSelfByPage from './queries/findImportDataWithSelfByPage.graphql'
import findImportDataWithServicerByPage from './queries/findImportDataWithServicerByPage.graphql'
import findTaskExecuteResponseByPage from './queries/findTaskExecuteResponseByPage.graphql'
import findTaskExecuteWithSelfResponseByPage from './queries/findTaskExecuteWithSelfResponseByPage.graphql'
import findTaskExecuteWithServicerResponseByPage from './queries/findTaskExecuteWithServicerResponseByPage.graphql'
import queryImportOpenTemplatePath from './queries/queryImportOpenTemplatePath.graphql'
import queryImportOpenTemplatePathByCategory from './queries/queryImportOpenTemplatePathByCategory.graphql'
import importOpen from './mutates/importOpen.graphql'
import importOpenForVerify from './mutates/importOpenForVerify.graphql'
import importOpenSignUpUnitForVerify from './mutates/importOpenSignUpUnitForVerify.graphql'
import trainingChannelAdministratorImportOpenForVerify from './mutates/trainingChannelAdministratorImportOpenForVerify.graphql'

export {
  exportExcel,
  exportExcelAllData,
  findImportDataByPage,
  findImportDataWithSelfByPage,
  findImportDataWithServicerByPage,
  findTaskExecuteResponseByPage,
  findTaskExecuteWithSelfResponseByPage,
  findTaskExecuteWithServicerResponseByPage,
  queryImportOpenTemplatePath,
  queryImportOpenTemplatePathByCategory,
  importOpen,
  importOpenForVerify,
  importOpenSignUpUnitForVerify,
  trainingChannelAdministratorImportOpenForVerify
}
