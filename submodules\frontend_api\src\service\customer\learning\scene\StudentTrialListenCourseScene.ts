/**
 * 学员学习课程场景
 */
import { EventEmitter } from 'events'
import ApplyTrialListenCourseToken from '@api/service/customer/learning/course/token-provider/ApplyTrialListenCourseToken'

class StudentTrialListenCourseScene extends EventEmitter {
  courseId: string

  constructor(courseId: string) {
    super()
    this.courseId = courseId
  }

  // 申请学习课件的 token
  private applyTrialListenCourseToken: ApplyTrialListenCourseToken

  /**
   * 进入上一次的场景
   */
  async enterPrevScene() {
    this.applyTrialListenCourseToken = new ApplyTrialListenCourseToken(this.courseId)
    // 流程
    // 申请课件学习 token
    await this.applyTrialListenCourseToken.apply()
  }
}

export default StudentTrialListenCourseScene
