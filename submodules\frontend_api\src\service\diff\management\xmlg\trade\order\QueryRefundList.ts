/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-05-07 09:09:57
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-19 10:13:47
 */
import DataExportBackstage, {
  ReturnSortRequest as DiffReturnSortRequest
} from '@api/diff-gateway/xmlg-data-export-gateway-backstage'
import MsTradeQueryFrontGatewayCourseLearningBacktage, {
  IssueInfo1,
  ReturnOrderRequest,
  ReturnOrderSortField,
  ReturnOrderStatisticResponse,
  ReturnSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import UserModule from '@api/service/management/user/UserModule'
import QueryStudentList from '@api/service/management/user/query/student/QueryStudentList'
import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/xmlg/trade/enums/SaleChannelType'
import ReturnOrderResponseVo from '@api/service/diff/management/xmlg/trade/order/model/ReturnOrderResponseVo'
import QueryPlatform from '@api/service/diff/common/xmlg/dictionary/QueryPlatform'
import { Page } from '@hbfe/common'
import fxnlQuery from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import TradeQueryFrontGatewayTradeQueryBackstage, {
  ReturnOrderRequest as ReturnOrderRequestDiff
} from '@api/diff-gateway/xmlg-trade-query-front-gateway-TradeQueryBackstage'
import {
  ReturnOrderBasicDataRequest,
  ReturnOrderRequestVo
} from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
import { ChangeOrderType } from '@api/service/common/trade/ChangeOrderType'
export default class QueryRefundList {
  totalSize = 0
  //统计数据
  returnOrderStatisic = new ReturnOrderStatisticResponse()
  /**
   * 获取退款单列表
   *
   */
  async queryRefundOrderList(page: Page, request: ReturnOrderRequestVo): Promise<Array<ReturnOrderResponseVo>> {
    let userIdList: string[] = []
    if (request.name || request.idCard || request.loginAccount) {
      const queryUser = UserModule.queryUserFactory.queryStudentList
      queryUser.queryStudentIdParams.idCard = request.idCard
      queryUser.queryStudentIdParams.userName = request.name
      queryUser.queryStudentIdParams.loginAccount = request.loginAccount
      const res = await queryUser.queryStudentIdList()
      userIdList = res.data
      if (!userIdList.length) {
        page.totalSize = 0
        page.totalPageSize = 0
        this.returnOrderStatisic.totalReturnOrderCount = 0
        this.returnOrderStatisic.totalRefundAmount = 0
        return []
      }
    }

    request.subOrderInfo.orderInfo.buyerIdList = userIdList

    if (request.saleSource || request.saleSource === SaleChannelEnum.self) {
      request.subOrderInfo.orderInfo.saleChannels = [request.saleSource]
    } else {
      request.subOrderInfo.orderInfo.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic,
        SaleChannelEnum.huayi
      ]
    }
    if (request.refundType) {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = [request.refundType]
    } else {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = []
    }
    request.returnCommodity.issueInfo = new IssueInfo1()
    if (request.periodId) {
      request.returnCommodity.issueInfo.issueId = request.periodId
    }

    request.fillDtoReturnStatusWithVo()

    const sort = new ReturnSortRequest()
    sort.policy = SortPolicy.DESC
    sort.field = ReturnOrderSortField.APPLIED_TIME
    const res = await TradeQueryFrontGatewayTradeQueryBackstage.pageReturnOrderInServicer({
      page: page,
      request: request as ReturnOrderRequestDiff,
      sort: [sort]
    })
    page.totalSize = res.data?.totalSize
    page.totalPageSize = res.data?.totalPageSize
    await this.queryStatisticReturnOrder(request)
    await QueryPlatform.queryList()
    if (res.status.isSuccess()) {
      const dataArr = [] as ReturnOrderResponseVo[]
      for (const item of res.data.currentPageData) {
        const tmpItem = new ReturnOrderResponseVo()
        Object.assign(tmpItem, item)
        // 处理换班、换期标签
        const { subOrderInfo } = item
        if (subOrderInfo) {
          const isExistExchangeScheme = subOrderInfo.exchanged
          const isExistExchangeIssue = subOrderInfo.isExchangeIssue
          if (isExistExchangeScheme) {
            tmpItem.changeOrderStatus.push(ChangeOrderType.CLASS_TYPE)
          }
          if (isExistExchangeIssue) {
            tmpItem.changeOrderStatus.push(ChangeOrderType.PERIOD_TYPE)
          }
        }
        tmpItem.getPeriodMessage(item)
        if (item.subOrderInfo.saleChannel == SaleChannelEnum.huayi) {
          tmpItem.thirdPartyPlatform = SaleChannelType.map.get(item.subOrderInfo.saleChannel)
        } else if (item.returnCommodity?.commoditySku?.tppTypeId) {
          tmpItem.thirdPartyPlatform = QueryPlatform.map.get(item.returnCommodity.commoditySku.tppTypeId)?.name
        }
        const ext = item.ext as any
        tmpItem.courseType = ext?.courseType || ''
        tmpItem.changeStatus()
        dataArr.push(tmpItem)
      }
      const userIds = [...new Set(dataArr.map((item) => item.subOrderInfo.orderInfo.buyer.userId))]
      if (userIds && userIds.length) {
        const queryM = new QueryStudentList()
        const studentDetailMap = await queryM.batchQueryStudentDetailMapByUserId(userIds)
        dataArr.forEach((item) => {
          if (!item.subOrderInfo.orderInfo.buyer.userId) return
          item.buyer = studentDetailMap.get(item.subOrderInfo.orderInfo.buyer.userId) || new UserDetailVo()
        })
      }
      this.totalSize = res.data.totalSize
      return dataArr
    }
    return []
  }
  async queryStatisticReturnOrder(request: ReturnOrderRequestVo) {
    if (request.refundType) {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = [request.refundType]
    } else {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = []
    }
    request.returnCommodity.issueInfo = new IssueInfo1()
    if (request.periodId) {
      request.returnCommodity.issueInfo.issueId = request.periodId
    }

    request.fillDtoReturnStatusWithVo()

    const totalRes = await MsTradeQueryFrontGatewayCourseLearningBacktage.statisticReturnOrderInServicer(
      request as ReturnOrderRequest
    )
    if (totalRes.status.isSuccess()) {
      if (totalRes.data) {
        this.returnOrderStatisic = totalRes.data
      } else {
        this.returnOrderStatisic.totalRefundAmount = 0
        this.returnOrderStatisic.totalReturnOrderCount = 0
      }
    }
  }
  /**
   * 获取分销退款单列表
   *
   */
  async queryFxRefundOrderList(page: Page, request: ReturnOrderRequestVo): Promise<Array<ReturnOrderResponseVo>> {
    let userIdList: string[] = []
    if (request.name || request.idCard) {
      const queryUser = UserModule.queryUserFactory.queryStudentList
      queryUser.queryStudentIdParams.idCard = request.idCard
      queryUser.queryStudentIdParams.userName = request.name
      const res = await queryUser.queryStudentIdList()
      userIdList = res.data
      if (!userIdList.length) {
        this.returnOrderStatisic.totalReturnOrderCount = 0
        this.returnOrderStatisic.totalRefundAmount = 0
        return []
      }
    }
    request.subOrderInfo.orderInfo.buyerIdList = userIdList

    if (request.saleSource || request.saleSource === SaleChannelEnum.self) {
      request.subOrderInfo.orderInfo.saleChannels = [request.saleSource]
    } else {
      request.subOrderInfo.orderInfo.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic
      ]
    }

    const sort = new ReturnSortRequest()
    sort.policy = SortPolicy.DESC
    sort.field = ReturnOrderSortField.APPLIED_TIME
    if (request.refundType) {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = [request.refundType]
    } else {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = []
    }
    request.returnCommodity.issueInfo = new IssueInfo1()
    if (request.periodId) {
      request.returnCommodity.issueInfo.issueId = request.periodId
    }

    request.fillDtoReturnStatusWithVo()

    const res = await TradeQueryFrontGatewayTradeQueryBackstage.pageReturnOrderInDistributor({
      page: page,
      request: request as ReturnOrderRequest,
      sort: [sort]
    })
    page.totalSize = res.data?.totalSize
    page.totalPageSize = res.data?.totalPageSize
    await this.queryFxStatisticReturnOrder(request)
    if (res.status.isSuccess()) {
      const dataArr = [] as ReturnOrderResponseVo[]
      for (const item of res.data.currentPageData) {
        const tmpItem = new ReturnOrderResponseVo()
        Object.assign(tmpItem, item)
        // 处理换班、换期标签
        const { subOrderInfo } = item
        if (subOrderInfo) {
          const isExistExchangeScheme = subOrderInfo.exchanged
          const isExistExchangeIssue = subOrderInfo.isExchangeIssue
          if (isExistExchangeScheme) {
            tmpItem.changeOrderStatus.push(ChangeOrderType.CLASS_TYPE)
          }
          if (isExistExchangeIssue) {
            tmpItem.changeOrderStatus.push(ChangeOrderType.PERIOD_TYPE)
          }
        }
        tmpItem.getPeriodMessage(item)
        const ext = item.ext as any
        tmpItem.courseType = ext?.courseType || ''
        tmpItem.changeStatus()
        dataArr.push(tmpItem)
      }
      const userIds = [...new Set(dataArr.map((item) => item.subOrderInfo.orderInfo.buyer.userId))]
      if (userIds && userIds.length) {
        const queryM = new QueryStudentList()
        const studentDetailMap = await queryM.batchQueryStudentDetailMapByUserId(userIds)
        dataArr.forEach((item) => {
          if (!item.subOrderInfo.orderInfo.buyer.userId) return
          item.buyer = studentDetailMap.get(item.subOrderInfo.orderInfo.buyer.userId) || new UserDetailVo()
        })
      }
      this.totalSize = res.data.totalSize
      return dataArr
    }
    return []
  }
  /**
   * 查询分销退款统计
   * @param request
   */
  async queryFxStatisticReturnOrder(request: ReturnOrderRequestVo) {
    if (request.refundType) {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = [request.refundType]
    } else {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = []
    }
    request.returnCommodity.issueInfo = new IssueInfo1()
    if (request.periodId) {
      request.returnCommodity.issueInfo.issueId = request.periodId
    }

    request.fillDtoReturnStatusWithVo()

    const totalRes = await MsTradeQueryFrontGatewayCourseLearningBacktage.statisticReturnOrderInDistributor(
      request as ReturnOrderRequest
    )
    if (totalRes.status.isSuccess()) {
      if (totalRes.data) {
        this.returnOrderStatisic = totalRes.data
      } else {
        this.returnOrderStatisic.totalRefundAmount = 0
        this.returnOrderStatisic.totalReturnOrderCount = 0
      }
    }
  }
  /**
   * 导出个人退货单
   */
  async exportReturnOrderExcelInServicer(request: ReturnOrderRequestVo, sort?: Array<ReturnSortRequest>) {
    let userIdList: string[] = []
    if (request.name || request.idCard || request.loginAccount) {
      const queryUser = UserModule.queryUserFactory.queryStudentList
      queryUser.queryStudentIdParams.idCard = request.idCard
      queryUser.queryStudentIdParams.userName = request.name
      queryUser.queryStudentIdParams.loginAccount = request.loginAccount
      const res = await queryUser.queryStudentIdList()
      userIdList = res.data
      if (!userIdList.length) {
        this.returnOrderStatisic.totalReturnOrderCount = 0
        this.returnOrderStatisic.totalRefundAmount = 0
        return false
      }
    }
    request.subOrderInfo.orderInfo.buyerIdList = userIdList
    if (request.saleSource || request.saleSource === SaleChannelEnum.self) {
      request.subOrderInfo.orderInfo.saleChannels = [request.saleSource]
    } else {
      request.subOrderInfo.orderInfo.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic,
        SaleChannelEnum.huayi
      ]
    }
    const DiffSort = sort as unknown as DiffReturnSortRequest[]
    if (request.refundType) {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = [request.refundType]
    } else {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = []
    }
    request.returnCommodity.issueInfo = new IssueInfo1()
    if (request.periodId) {
      request.returnCommodity.issueInfo.issueId = request.periodId
    }

    request.fillDtoReturnStatusWithVo()

    const response = await DataExportBackstage.exportReturnOrderExcelInServicer({
      request: request as ReturnOrderRequest,
      sort: DiffSort
    })
    return response
  }
  /**
   * 导出个人退货单（分销）
   */
  async exportFxReturnOrderExcelInDistributor(request: ReturnOrderRequestVo, sort?: Array<ReturnSortRequest>) {
    let userIdList: string[] = []
    if (request.name || request.idCard || request.loginAccount) {
      const queryUser = UserModule.queryUserFactory.queryStudentList
      queryUser.queryStudentIdParams.idCard = request.idCard
      queryUser.queryStudentIdParams.userName = request.name
      queryUser.queryStudentIdParams.loginAccount = request.loginAccount
      const res = await queryUser.queryStudentIdList()
      userIdList = res.data
      if (!userIdList.length) {
        this.returnOrderStatisic.totalReturnOrderCount = 0
        this.returnOrderStatisic.totalRefundAmount = 0
        return false
      }
    }
    request.subOrderInfo.orderInfo.buyerIdList = userIdList
    if (request.saleSource || request.saleSource === SaleChannelEnum.self) {
      request.subOrderInfo.orderInfo.saleChannels = [request.saleSource]
    } else {
      request.subOrderInfo.orderInfo.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic
      ]
    }
    if (request.refundType) {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = [request.refundType]
    } else {
      if (!request.basicData) {
        request.basicData = new ReturnOrderBasicDataRequest()
      }
      request.basicData.returnOrderTypes = []
    }
    request.returnCommodity.issueInfo = new IssueInfo1()
    if (request.periodId) {
      request.returnCommodity.issueInfo.issueId = request.periodId
    }

    request.fillDtoReturnStatusWithVo()

    const response = await DataExportBackstage.exportReturnOrderExcelInDistributor({
      request: request as ReturnOrderRequest,
      sort
    })
    return response
  }
}
