import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
import { cloneDeep } from 'lodash'
@Component
export default class extends Vue {
  @Prop({
    type: [String, Array],
    default: ''
  })
  value: string

  @Prop({
    type: Boolean,
    default: false
  })
  multiple: boolean

  select = ''

  @Watch('value')
  // string or string[]
  watchValue(val?: any) {
    this.select = cloneDeep(val)
  }

  @Emit('input')
  change() {
    console.log(this.select)
    return this.select
  }
}
