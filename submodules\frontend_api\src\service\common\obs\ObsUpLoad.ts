import Obsfile from '@api/platform-gateway/platform-jxjy-obsfile-v1'
import { BizTypeEnum } from './BizTypeEnum'

/*
 * @Author: Zhu<PERSON>ong
 * @Date: 2022-12-13 09:56:51
 * @LastEditors: ZhuSong
 * @LastEditTime: 2022-12-13 13:48:35
 * @Description:
 * 上传-状态层
 *
 */
export default class ObsUpLoad {
  /**
   * 获取token
   * bizType 和 owner 参考 http://*************:8090/pages/viewpage.action?pageId=45501963
   * @param bizType "文件业务归类"
   * @param owner "文件的所属方"
   * @param sign "说明是什么文件，粒度至每个具体的上传内容，用于二期进行文件的人工识别、划归可见性权限；如：在上传企业营业执照的上传中，sign值填“企业的营业执照”"
   */
  async getToken(metaData: { bizType: BizTypeEnum; owner?: string; sign: string; ownerType?: string }) {
    const re = await Obsfile.generateUploadToken(metaData)
    return re.data
  }
}
