import store from '@/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import QueryTrainClassFactory from '@api/service/management/train-class/QueryTrainClassFactory'
import MutationTrainClassFactory from '@api/service/management/train-class/MutationTrainClassFactory'

/**
 * 培训班中控层
 */
@Module({
  name: 'TrainClassManagerModuleModule',
  dynamic: true,
  namespaced: true,
  store
})
class TrainClassManagerModule extends VuexModule {
  // region properties

  /**
   *培训班查询工厂类，类型为QueryTrainClassFactory
   */
  queryTrainClassFactory = new QueryTrainClassFactory()
  /**
   *培训班业务工厂类，类型为MutationTrainClassFactory
   */
  mutationTrainClassFactory = new MutationTrainClassFactory()
  // endregion
  // region methods

  // endregion
}
export default getModule(TrainClassManagerModule)
