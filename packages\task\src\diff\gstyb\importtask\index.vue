<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import ImportTask from '@hbfe/jxjy-admin-task/src/importtask/index.vue'
  import Downloader, { HeaderObj } from '@api/service/common/utils/Downloader'
  import QueryImportAsyncTask from '@api/service/diff/management/gstyb/async-task/query/QueryImportAsyncTask'
  @Component
  export default class extends ImportTask {
    queryImportAsyncTask = new QueryImportAsyncTask()

    importObj = {
      id: 99,
      name: '批量导入公需课课程'
    }

    async activated() {
      const index = this.taskList.findIndex((item) => item.name === '批量导入公需课课程')
      if (index === -1) {
        this.taskList.push(this.importObj)
        this.taskTypeList.push(this.importObj)
      }
      console.log('ImportTask component activated')
    }

    /**
     * 查询
     */
    doTaskDiff(val: any) {
      if (val.name === '批量导入公需课课程') {
        this.queryFindBatchImportByPageInServicer()
        return '批量导入公需课课程'
      } else {
        return ''
      }
    }

    /**
     * 下载导出数据
     */
    locationDiff(item: any) {
      if (this.currentTaskType === '批量导入公需课课程') {
        this.queryBatchExportAllChooseDataInServicer(item)
        return '批量导入公需课课程'
      } else {
        return ''
      }
    }

    /**
     * 下载失败数据
     */
    locationfailDiff(item: any) {
      if (this.currentTaskType === '批量导入公需课课程') {
        this.queryBatchExportFailChooseDataInServicer(item)
        return '批量导入公需课课程'
      } else {
        return ''
      }
    }

    /**
     * 查询 批量导入公需课课程
     */
    async queryFindBatchImportByPageInServicer() {
      this.tableData = await this.queryImportAsyncTask.findBatchImportByPageInServicer(this.page, this.pageQueryParam)
      this.totalSize = this.page.totalSize
    }

    /**
     * 下载全部数据 批量导入公需课课程
     */
    async queryBatchExportAllChooseDataInServicer(item: any) {
      const res = await this.queryImportAsyncTask.batchExportAllChooseDataInServicer(item.id)
      const fileUrl = '/mfs' + res
      const fileName = res.split('/')[res.split('/').length - 1].split('?')[0]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 下载 批量导入公需课课程失败数据
     */
    async queryBatchExportFailChooseDataInServicer(item: any) {
      const res = await this.queryImportAsyncTask.batchExportFailChooseDataInServicer(item.id)
      if (!this.hasDownLoadLink(res)) return
      const fali = location.origin + '/mfs' + res
      const fileUrl = fali
      const fileName = res.split('/')[res.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }
  }
</script>
