import BasicDataGateway, {
  IndustryPropertyCategoryResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
import Vue from 'vue'

class QueryIndustryPropertyCategory {
  /**
   * 行业属性分类列表缓存（科目类型、培训类别）
   */
  industryPropertyCategoryCache: { [key: string]: Array<IndustryPropertyCategoryVo> } = {}

  async queryIndustryProperty() {
    await Promise.all(
      QueryIndustry.industryList?.map(async industry => {
        await this.queryIndustryPropertyByPropertyId(industry.propertyId)
      })
    )
  }
  /**
   * 查询行业属性
   * @param industryPropertyId 行业属性编号 在行业列表中有
   * @return {ResponseStatus}
   */
  private async queryIndustryPropertyByPropertyId(industryPropertyId: string) {
    const res = await BasicDataGateway.listIndustryPropertyCategoryV2(industryPropertyId)
    if (res.status.isSuccess()) {
      this.setIndustryPropertyCategoryCache(industryPropertyId, res.data)
    }
    return res.status
  }

  /**
   * 设置行业属性分类的缓存
   * @param industryPropertyId_code 作为key值
   */
  private setIndustryPropertyCategoryCache(
    industryPropertyId: string,
    categoryList: Array<IndustryPropertyCategoryResponse>
  ) {
    if (!this.industryPropertyCategoryCache[industryPropertyId]) {
      Vue.set(this.industryPropertyCategoryCache, industryPropertyId, categoryList)
    }
  }

  /**
   * 【组件】获取行业属性分类列表
   * @param
   * @return
   */
  async getIndustryPropertyCategoryList(industryPropertyId: string) {
    if (this.industryPropertyCategoryCache[industryPropertyId]) {
      return this.industryPropertyCategoryCache[industryPropertyId] || new Array<IndustryPropertyCategoryVo>()
    } else {
      await this.queryIndustryProperty()
      return this.industryPropertyCategoryCache[industryPropertyId] || new Array<IndustryPropertyCategoryVo>()
    }
  }
}

export default new QueryIndustryPropertyCategory()
