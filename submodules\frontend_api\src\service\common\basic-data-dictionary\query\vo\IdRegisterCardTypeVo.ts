import { IdCardTypeModelRequest, IdCardTypeModelResponse } from '@api/ms-gateway/ms-servicer-series-v1'
import IdCardTypeVo from '@api/service/common/basic-data-dictionary/query/vo/IdCardTypeVo'

class IdRegisterCardTypeVo extends IdCardTypeVo {
  /**
   * 【必填】字段名称
   */
  idCardTypeName: string = null
  /**
   * 【必填】字段值
   */
  idCardTypeValue: number = null
  /**
   * 是否可选
   */
  select = false

  static fromRegister(dto: IdCardTypeModelResponse, dictionarys: IdCardTypeVo[]) {
    const vo = Object.assign(new IdRegisterCardTypeVo(), dto)
    const dictionary = dictionarys.find(item => item.code === dto.idCardTypeValue)
    Object.assign(vo, dictionary)
    return vo
  }

  static toRegister(vo: IdRegisterCardTypeVo) {
    const dto = new IdCardTypeModelRequest()
    dto.idCardTypeName = vo.idCardTypeName
    dto.idCardTypeValue = vo.idCardTypeValue
    dto.select = vo.select
    return dto
  }
}

export default IdRegisterCardTypeVo
