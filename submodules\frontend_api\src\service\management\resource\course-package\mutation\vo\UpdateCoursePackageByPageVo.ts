import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'
import CoursePackageDetailVo from '@api/service/management/resource/course-package/query/vo/CoursePackageDetailVo'
import CreateCoursePackageVo from '@api/service/management/resource/course-package/mutation/vo/CreateCoursePackageVo'
import { CourseInPackageRequest, CoursePackageUpdateRequest } from '@api/ms-gateway/ms-course-resource-v1'
import QueryCoursePackage from '@api/service/management/resource/course-package/query/QueryCoursePackage'
import { cloneDeep } from 'lodash'

/**
 * @description 更新课程包-分页版
 */
class UpdateCoursePackageByPageVo extends CreateCoursePackageVo {
  id: string

  // 查询课程包接口
  private queryCoursePackage = new QueryCoursePackage()

  /**
   * 包内原始课程列表 - 查询课程包下原课程列表
   */
  private sourceCourseList: Array<CourseInCoursePackage> = new Array<CourseInCoursePackage>()

  /**
   * ui绑定的课程列表
   */
  uiBindCourseList: Array<CourseInCoursePackage> = new Array<CourseInCoursePackage>()

  /**
   * 已选课程列表
   */
  addedList: Array<CourseInCoursePackage> = new Array<CourseInCoursePackage>()
  /**
   * 课程数量
   */
  courseCount = 0
  /**
   * 总学时
   */
  totalPeriod = 0

  /**
   * 已选课程列表长度
   */
  get totalSize() {
    return this.addedList.length || 0
  }

  /**
   * 初始化资源课程列表
   * @param coursePackageId 课程包id
   */
  async initData(coursePackageId: string) {
    const queryCoursePackage = new QueryCoursePackage()
    /** 获取源数据 */
    const sourceCourseList = await queryCoursePackage.queryCourseBaseListInCoursePackage(coursePackageId)
    this.sourceCourseList = sourceCourseList.sort((a, b) => {
      return a.sort - b.sort
    })
    this.addedList = cloneDeep(this.sourceCourseList)
    /** 获取ui课程列表 */
    // 先获取前十个包内课程信息
    const addedCourseList = this.sourceCourseList.slice(0, 10)
    const courseIdList = [...new Set(addedCourseList.map(item => item.id).filter(Boolean))]
    const courseMap = await queryCoursePackage.queryCourseMapById(courseIdList)
    // 填充课程信息（课件提供商、物理学时、创建时间）
    this.uiBindCourseList = addedCourseList.map(item => {
      const courseInfo = courseMap.get(item.id)
      item.name = courseInfo?.name
      item.physicsPeriod = courseInfo?.physicsPeriod
      item.createTime = courseInfo?.createTime
      item.providerId = courseInfo?.providerId
      item.providerName = courseInfo?.providerName
      return item
    })
  }

  /**
   * 加载更多课程信息
   */
  async loadMore() {
    // 获取最大索引
    const loadStartIndex = this.findMaxIndex() + 1
    // 获取要加载的列表数据
    const addItemList = this.sourceCourseList.slice(loadStartIndex, loadStartIndex + 10)
    if (addItemList.length) {
      // 获取课程id集合
      const courseIdList = addItemList?.map(item => item.id)
      // 获取课程信息Map
      const courseMap = await this.queryCoursePackage.queryCourseMapById(courseIdList)
      // 填充课程信息
      const list = addItemList.map(item => {
        const courseInfo = courseMap.get(item.id)
        item.name = courseInfo?.name
        item.physicsPeriod = courseInfo?.physicsPeriod
        item.createTime = courseInfo?.createTime
        item.providerId = courseInfo?.providerId
        item.providerName = courseInfo?.providerName
        return item
      })
      // 获取合并后的课程列表
      this.uiBindCourseList = this.uiBindCourseList.concat(list)
    }
  }

  static from(detail: CoursePackageDetailVo) {
    const updateVo = new UpdateCoursePackageByPageVo()
    updateVo.id = detail.id
    updateVo.name = detail.name
    updateVo.showName = detail.showName
    updateVo.courseCount = detail.courseCount
    updateVo.totalPeriod = detail.totalPeriod
    return updateVo
  }

  /**
   * 查找最大的索引
   * @description ui数据与源数据重复项的最大索引，为了判断数据裁剪的临界点
   * @private
   */
  private findMaxIndex() {
    let maxIndex = -1
    this.uiBindCourseList.forEach(item => {
      const index = this.sourceCourseList.findIndex(subItem => subItem.id === item.id)
      if ((index || index === 0) && index > maxIndex) {
        maxIndex = index
      }
    })
    return maxIndex
  }

  /**
   * 获取更新课程包请求参数字段
   */
  getUpdateCoursePackageRequest() {
    const request = new CoursePackageUpdateRequest()
    request.id = this.id
    request.name = this.name
    request.displayName = this.showName
    // 计算课程包下课程信息
    request.courseInPackageList = new Array<CourseInPackageRequest>()
    const maxIndex = this.findMaxIndex()
    const sliceStartIndex = maxIndex + 1
    const sliceArr = this.sourceCourseList.slice(sliceStartIndex)
    const result = [...this.uiBindCourseList, ...sliceArr]
    result.forEach((item, index) => {
      const opt = new CourseInPackageRequest()
      opt.courseId = item.id
      opt.period = Number(item.period)
      opt.sort = index + 1
      request.courseInPackageList.push(opt)
    })
    console.group('getUpdateCoursePackage')
    console.log('maxIndex', maxIndex)
    console.log('sliceStartIndex', sliceStartIndex)
    console.log('sliceArr', sliceArr, this.uiBindCourseList, this.sourceCourseList)
    console.log('result', result)
    console.log('request', request)
    console.groupEnd()
    return request
  }

  /**
   * 根据 id 查询待选列表中的课程
   * @param id
   */
  getCourseById(id: string) {
    return this.addedList.find((course: CourseInCoursePackage) => course.id === id)
  }

  /**
   * 往待选确认列表中增加课程
   * @param courseInPackage
   */
  addCourse(courseInPackage: CourseInCoursePackage) {
    if (!this.getCourseById(courseInPackage.id)) {
      courseInPackage.period = courseInPackage.physicsPeriod
      this.addedList.unshift(courseInPackage)
      this.uiBindCourseList.unshift(courseInPackage)
    }
  }

  /**
   * 批量增加课程
   * @param courseInPackage
   */
  batchAddCourse(courseInPackage: Array<CourseInCoursePackage>) {
    courseInPackage.forEach(course => {
      this.addCourse(course)
    })
  }

  /**
   * 取消选择课程
   * @param course
   */
  cancelChoose(course: CourseInCoursePackage) {
    // 取消课程时，已选待提交课程数据同步删除对应项
    const findItemIndex = this.addedList.findIndex(itemCourse => course.id === itemCourse.id)
    if (findItemIndex !== -1) {
      this.addedList.splice(findItemIndex, 1)
    }
    // 取消课程时，ui绑定课程数据同步删除对应项
    const uiIndex = this.uiBindCourseList.findIndex(itemCourse => course.id === itemCourse.id)
    if (findItemIndex !== -1) {
      this.uiBindCourseList.splice(uiIndex, 1)
    }
    const sourceItemIndex = this.sourceCourseList.findIndex(itemCourse => course.id === itemCourse.id)
    // 取消课程时，源课程数据同步删除对应项
    if (sourceItemIndex !== -1) {
      this.sourceCourseList.splice(uiIndex, 1)
    }
  }

  /**
   * 批量删除
   * @param courseInPackage
   */
  batchRemoveCourse(courseInPackage: Array<CourseInCoursePackage> = []) {
    courseInPackage.forEach(course => {
      this.cancelChoose(course)
    })
  }
}

export default UpdateCoursePackageByPageVo
