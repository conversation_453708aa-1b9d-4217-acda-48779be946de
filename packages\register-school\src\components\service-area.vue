<template>
  <div class="print-sign-wrapper">
    <div class="block">
      <el-cascader
        ref="cascader"
        class="cascader"
        v-model="areaList"
        :options="options"
        :props="props"
        collapse-tags
        clearable
        @change="handleChange"
        :show-all-levels="false"
      >
      </el-cascader>
    </div>
  </div>
</template>

<script>
  export default {
    components: {},
    props: {
      options: {
        type: Array,
        default: () => {
          return []
        }
      },
      serviceRegionCodesList: {
        type: Array,
        default: () => {
          return []
        }
      }
    },
    watch: {
      serviceRegionCodesList: {
        handler(newvalue, oldvalue) {
          if (!newvalue.length) {
            this.areaList = []
            return
          }
          if (this.options && this.options.length > 0) {
            this.handleReData(this.options, newvalue)
          }
        },
        deep: true,
        immediate: true
      },
      options: {
        handler(newvalue, oldvalue) {
          if (this.serviceRegionCodesList && this.serviceRegionCodesList.length > 0) {
            this.handleReData(newvalue, this.serviceRegionCodesList)
          }
        },
        deep: true,
        immediate: true
      }
    },
    data() {
      return {
        props: {
          multiple: true,
          emitPath: false,
          value: 'id',
          label: 'name'
          //   checkStrictly: true
        },
        areaList: [],
        dropDownFlag: true
      }
    },
    methods: {
      handleReData(options, codelist) {
        this.areaList = []
        codelist.map(item => {
          if (item.substr(2, 4) == '0000') {
            // 省级 地区编码
            const province = options.filter(oitem => oitem.id == item)
            if (province.length <= 0) return
            province[0].children.map(citem => {
              if (citem.children && citem.children.length) {
                citem.children.map(sitem => {
                  this.areaList.push(sitem.id)
                })
              } else {
                // 省直
                this.areaList.push(citem.id)
              }
            })
          } else if (item.substr(4, 2) == '00') {
            // 市级 地区编码
            const province = options.filter(oitem => oitem.id == item.substr(0, 2) + '0000')
            if (province.length <= 0) return
            const city = province[0].children.filter(mitem => mitem.id == item)
            if (city[0].children?.length) {
              city[0].children.map(nitem => {
                this.areaList.push(nitem.id)
              })
            } else {
              this.areaList.push(city[0].id)
            }
          } else {
            // 区县 或 省直 地区编码
            this.areaList.push(item)
          }
        })
      },
      handleChange(value) {
        // 去掉父级被选中的子级数据
        let checkedList = this.$refs.cascader.getCheckedNodes()
        checkedList = checkedList.filter(item => !(item.parent && item.parent.checked))
        const area = checkedList.map(item => {
          return item.value
        })
        //强行显示一级菜单
        // const cascaderElement = document.querySelector('div.el-cascader__tags')
        // const Selectobserver = new MutationObserver((a, b) => {
        //   const text = document.querySelector('span.el-tag--info')
        //   const checkedList = this.$refs.cascader.getCheckedNodes()
        //   Selectobserver.disconnect()
        //   if (text && checkedList.length && text.firstChild && checkedList[0].label == '福建省') {
        //     text.firstChild.innerText = '福建省'
        //   }
        // })
        // Selectobserver.observe(cascaderElement, {
        //   characterData: true,
        //   childList: true,
        //   subtree: true
        // })

        this.$emit('getArea', area)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .print-sign-wrapper {
    // padding: 20px;
    .block {
      position: relative;
      .cascader {
        width: 60%;
      }
      .area {
        font-size: 12px;
        position: absolute;
        width: 70px;
        height: 20px;
        background: rgb(240, 242, 245);
        // border-radius: 2px;
        top: 7px;
        left: 8px;
        text-align: center;
        line-height: 22px;
        color: rgb(156, 147, 143);
      }
      .num {
        font-size: 12px;
        position: absolute;
        width: 42px;
        height: 24px;
        background: rgb(240, 242, 245);
        border-radius: 4px;
        top: 8px;
        left: 138px;
        text-align: center;
        line-height: 22px;
        color: rgb(156, 147, 143);
      }
    }
  }
</style>
