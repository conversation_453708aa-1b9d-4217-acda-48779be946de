schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取所有工种信息"""
	getAllWorkTypeList(name:String,matchingPattern:SearchMatchingPattern):[WorkTypeResponse] @NotAuthenticationRequired
	"""获取工种信息"""
	getWorkType(id:String):WorkTypeResponse @NotAuthenticationRequired
	"""获取工种分类下的所有工种
		@param containsChildren 代表是否包含其子分类对应的工种,false的话就是该分类直接对应的工种
	"""
	getWorkTypeListByCategoryId(categoryId:String,containsChildren:Boolean!):[WorkTypeResponse] @NotAuthenticationRequired
	"""根据工种分类id集合批量获取对应的工种信息,该查询不会递归到子分类,就是以传入的当前分类id直接关联的工种信息"""
	getWorkTypeListByCategoryIds(categoryIds:[String]):[WorkTypeCategoryRelationResponse] @NotAuthenticationRequired
	"""根据工种id集合批量获取工种信息"""
	getWorkTypeListByIds(ids:[String]):[WorkTypeResponse] @NotAuthenticationRequired
	"""获取门户课程分类下的所有工种
		@param containsChildren 代表是否包含其子分类对应的工种,false的话就是该分类直接对应的工种
	"""
	getWorkTypeListByPortalCourseCategoryId(portalCourseCategoryId:String,containsChildren:Boolean!):[WorkTypeResponse] @NotAuthenticationRequired
	"""根据门户课程分类id集合批量获取对应的工种信息,该查询不会递归到子分类,就是以传入的当前分类id直接关联的工种信息"""
	getWorkTypeListByPortalCourseCategoryIds(portalCourseCategoryIds:[String]):[PortalCourseCategoryRelationWorkTypeResponse] @NotAuthenticationRequired
	"""获取工种分页"""
	getWorkTypePage(page:Page,query:WorkTypeQueryDTO):WorkTypeResponsePage @page(for:"WorkTypeResponse")
}
type Mutation {
	"""工种下移指定偏移量"""
	moveDown(id:String,offset:Int!):WorkTypeResponse
	"""工种上移指定偏移量"""
	moveUp(id:String,offset:Int!):WorkTypeResponse
}
input WorkTypeQueryDTO @type(value:"com.fjhb.btpx.platform.service.worktype.dto.WorkTypeQueryDTO") {
	"""工种名称模糊查询"""
	name:String
	"""工种所在的门户课程分类ID"""
	portalCourseCategoryIds:[String]
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
type PortalCourseCategoryRelationWorkTypeResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.PortalCourseCategoryRelationWorkTypeResponse") {
	portalCourseCategoryId:String
	workTypeList:[WorkTypeResponse]
}
type WorkTypeCategoryRelationResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.WorkTypeCategoryRelationResponse") {
	categoryId:String
	workTypeList:[WorkTypeResponse]
}
type WorkTypeResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.WorkTypeResponse") {
	id:String
	name:String
	code:String
	sort:Long
	creatorId:String
	createTime:DateTime
}
"""查询匹配方式
	<AUTHOR>
	@version 1.2
	@date 2021/6/21 20:06
"""
enum SearchMatchingPattern @type(value:"com.fjhb.btpx.support.constants.SearchMatchingPattern") {
	"""查询匹配方式"""
	RLIKE
	LLIKE
	EQUAL
	LIKE
}

scalar List
type WorkTypeResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [WorkTypeResponse]}
