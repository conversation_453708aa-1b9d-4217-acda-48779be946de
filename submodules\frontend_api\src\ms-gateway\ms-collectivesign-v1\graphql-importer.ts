import asyncExportCollectiveSignupExcuteFailExcel from './queries/asyncExportCollectiveSignupExcuteFailExcel.graphql'
import collectiveSignupDataAnalysis from './queries/collectiveSignupDataAnalysis.graphql'
import collectiveSignupDataProcess from './queries/collectiveSignupDataProcess.graphql'
import exportCollectiveSignupImportFailExcel from './queries/exportCollectiveSignupImportFailExcel.graphql'
import findCollectiveSignupMetaSchema from './queries/findCollectiveSignupMetaSchema.graphql'
import findCommitCompleteAndFailSubTuskFailDataByPage from './queries/findCommitCompleteAndFailSubTuskFailDataByPage.graphql'
import findCommitCompleteAndSuccessSubTuskSuccessDataByPage from './queries/findCommitCompleteAndSuccessSubTuskSuccessDataByPage.graphql'
import findCountGroupByKey from './queries/findCountGroupByKey.graphql'
import findImportCollectiveSignupCompleteSuccessDataByPage from './queries/findImportCollectiveSignupCompleteSuccessDataByPage.graphql'
import findImportCollectiveSignupFailDataByPage from './queries/findImportCollectiveSignupFailDataByPage.graphql'
import findLastRecordByBatch from './queries/findLastRecordByBatch.graphql'
import findTaskExecuteResponsePage from './queries/findTaskExecuteResponsePage.graphql'
import queryTemplatePathByCategory from './queries/queryTemplatePathByCategory.graphql'
import clearFailureData from './mutates/clearFailureData.graphql'
import commitCollectiveSignup from './mutates/commitCollectiveSignup.graphql'
import deleteCollectiveSignup from './mutates/deleteCollectiveSignup.graphql'
import deleteSignupData from './mutates/deleteSignupData.graphql'
import importCollectiveSignup from './mutates/importCollectiveSignup.graphql'
import importCollectiveSignupForCompatible from './mutates/importCollectiveSignupForCompatible.graphql'
import importCollectiveSignupForVerify from './mutates/importCollectiveSignupForVerify.graphql'
import signupByOriginalCollectiveSignup from './mutates/signupByOriginalCollectiveSignup.graphql'
import signupByOriginalCollectiveSignupWithInfo from './mutates/signupByOriginalCollectiveSignupWithInfo.graphql'
import updateSignupData from './mutates/updateSignupData.graphql'

export {
  asyncExportCollectiveSignupExcuteFailExcel,
  collectiveSignupDataAnalysis,
  collectiveSignupDataProcess,
  exportCollectiveSignupImportFailExcel,
  findCollectiveSignupMetaSchema,
  findCommitCompleteAndFailSubTuskFailDataByPage,
  findCommitCompleteAndSuccessSubTuskSuccessDataByPage,
  findCountGroupByKey,
  findImportCollectiveSignupCompleteSuccessDataByPage,
  findImportCollectiveSignupFailDataByPage,
  findLastRecordByBatch,
  findTaskExecuteResponsePage,
  queryTemplatePathByCategory,
  clearFailureData,
  commitCollectiveSignup,
  deleteCollectiveSignup,
  deleteSignupData,
  importCollectiveSignup,
  importCollectiveSignupForCompatible,
  importCollectiveSignupForVerify,
  signupByOriginalCollectiveSignup,
  signupByOriginalCollectiveSignupWithInfo,
  updateSignupData
}
