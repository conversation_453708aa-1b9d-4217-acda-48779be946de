<route-meta>
{
"isMenu": true,
"title": "专题管理",
"sort": 2,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>
<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, ZTGLY } from '@/models/RoleTypes'
  import SpecialTopicsManageIndex from '@hbfe/jxjy-admin-specialTopics/src/manage/index.vue'

  @RoleTypeDecorator({
    //TODO
    topicsManage: [WXGLY],
    configuration: [WXGLY],
    batchUpadteScheme: [WXGLY],
    sortWx: [WXGLY],
    operation: [WXGLY],
    visibilityConfig: [WXGLY, ZTGLY],
    h5Paper: [WXGLY, ZTGLY],
    hasTraining: [WXGLY, ZTGLY],
    topicsManageZt: [ZTGLY]
  })
  export default class extends SpecialTopicsManageIndex {}
</script>
