interface EventHandle {
  pageSizeChange: (size: number) => void
  currentChange: (page: number) => void
}

class Page {
  eventHandle?: EventHandle

  constructor(params?: EventHandle) {
    this.eventHandle = params
  }

  page = 0
  pageNo = 1
  pageSize = 10
  total = 0

  currentChange(pageNo: number) {
    this.pageNo = pageNo
    this.page = pageNo
    if (this.eventHandle) {
      this.eventHandle.currentChange(pageNo)
    }
  }

  pageSizeChange(pageSize: number) {
    this.pageSize = pageSize
    if (this.eventHandle) {
      this.eventHandle.pageSizeChange(pageSize)
    }
  }

  params() {
    return {
      pageSize: this.pageSize,
      pageNo: this.pageNo
    }
  }
}

export default Page
