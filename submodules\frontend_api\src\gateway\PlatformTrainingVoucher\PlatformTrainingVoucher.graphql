schema {
	query:Query
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	findUserTrainingVoucherPageList(page:Page!,request:UserTrainingVoucherRequest!):[TrainingVoucherInfoResponse]
}
"""用户培训券查询条件
	<AUTHOR>
	@since 2021/2/22
"""
input UserTrainingVoucherRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.trainingvoucher.UserTrainingVoucherRequest") {
	"""身份证号"""
	identityNumber:String
	"""用户培训券状态（可选），默认0
		<pre>
		0 - 不限
		1 - 可用
		2 - 激活
		3 - 已使用
		4 - 已结算（补贴）
		-5 - 已作废
		-2 - 过期
		</pre>
	"""
	status:Int!
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""培训券信息
	<AUTHOR>
	@since 2021/2/22
"""
type TrainingVoucherInfoResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.trainingvoucher.TrainingVoucherInfoResponse") {
	"""培训券编码"""
	couponCode:String
	"""身份证号"""
	idCard:String
	"""用户姓名"""
	name:String
	"""培训券名称"""
	couponName:String
	"""用户培训券状态
		<pre>
		1 - 可用
		2 - 激活
		3 - 已使用
		4 - 已结算（补贴）
		-5 - 已作废
		-2 - 已过期
		</pre>
	"""
	couponStatus:Int!
	"""用户培训券状态说明"""
	couponStatusName:String
	"""发券机构（属地人社局）"""
	publishOrgName:String
	"""区域编码（行政编码）"""
	areaCode:String
	"""培训券面值（单位分）"""
	amount:Int!
	"""当前券剩余余额（单位分）"""
	nowAmount:Int!
	"""可用工种"""
	job:String
	"""使用有效期（年月日时分）"""
	endTime:DateTime
	"""适用人群"""
	personScope:[String]
	"""适用工种范围，工种名称数组"""
	jobScope:[String]
	"""适用培训机构范围，机构信息数组"""
	orgScope:[String]
	"""适用培训模式
		<pre>
		0-不限
		1-线上
		2-线上+线下
		3-线下
		</pre>
	"""
	trainMode:Int!
	"""机构编码（未兑换时则为空）"""
	educationCode:String
	"""培训机构名称（未兑换时则为空）"""
	educationName:String
	"""使用模式（未兑换时则为空）
		<pre>
		1-自助使用
		2-扫码使用
		</pre>
	"""
	useMode:Int!
	"""使用日期（未兑换时则为空）"""
	useDate:DateTime
	"""补贴时间（未兑换时则为空）"""
	checkDate:DateTime
}

scalar List
