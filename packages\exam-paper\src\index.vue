<template>
  <el-main>
    <div class="f-p15" v-if="$hasPermission('query')" desc="查询" actions="doQueryPage,searchBase,activated">
      <div class="f-mb15">
        <template
          v-if="$hasPermission('create')"
          desc="创建"
          actions="@hbfe/jxjy-admin-examPaper/src/create.vue"
        >
          <el-button type="primary" icon="el-icon-plus" @click="create">新建试卷</el-button>
        </template>
        <template
          v-if="$hasPermission('category')"
          desc="分类"
          actions="@hbfe/jxjy-admin-examPaper/src/category.vue"
        >
          <el-button type="primary" plain @click="category">试卷分类管理</el-button>
        </template>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <hb-search-wrapper :model="requestParams" @reset="resetCondition">
          <el-form-item label="试卷名称关键字">
            <el-input placeholder="请输入试卷名称关键字" v-model="requestParams.name" clearable></el-input>
          </el-form-item>

          <el-form-item label="组卷方式">
            <el-select v-model="requestParams.paperPublishPatterns" clearable placeholder="请选择组卷方式">
              <el-option
                v-for="(item, index) in configTypeList"
                :key="index"
                :label="item.title"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="试卷分类">
            <paper-classify-tree v-model="examCategory" ref="paperClassifyRef" style="width:100%" v-if="classifyTree" />
          </el-form-item>

          <el-form-item label="试卷状态">
            <el-select v-model="requestParams.status" placeholder="请选择试卷状态" clearable style="width:100%">
              <el-option
                v-for="(item, index) in paperStatusOption"
                :key="index"
                :label="item.title"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-form-item>

          <template slot="actions">
            <el-button type="primary" @click="searchBase" :loading="uiConfig.loading">查询</el-button>
          </template>
        </hb-search-wrapper>

        <!--表格-->
        <el-table
          stripe
          :data="paperPublishConfigureResponse"
          v-loading="uiConfig.loading"
          max-height="500px"
          class="m-table"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="试卷名称" min-width="300" fixed="left" prop="name"> </el-table-column>
          <el-table-column label="组卷方式" min-width="180">
            <template slot-scope="scope">
              <span>{{ getPaperPublishPatternName(scope.row.paperPublishPatterns) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="categoryName" label="试卷分类" width="150" />

          <el-table-column label="状态" min-width="120">
            <template slot-scope="scope">
              <hb-badge v-if="scope.row.status == 1" text="启用" status="success"></hb-badge>
              <hb-badge v-else text="停用" status="error"></hb-badge>
            </template>
          </el-table-column>

          <el-table-column prop="createUserName" label="创建人" width="240" align="center"></el-table-column>
          <el-table-column prop="createdTime" label="创建时间" width="180" align="center"></el-table-column>
          <el-table-column label="操作" width="240" align="center" fixed="right">
            <template slot-scope="scope">
              <template
                v-if="$hasPermission('detail')"
                desc="详情"
                actions="@hbfe/jxjy-admin-examPaper/src/detail.vue"
              >
                <el-button type="text" size="mini" @click="detail(scope.row)">详情</el-button>
              </template>
              <template
                v-if="$hasPermission('preview')"
                desc="预览"
                actions="@hbfe/jxjy-admin-exam/src/mockPreview.vue"
              >
                <el-button type="text" size="mini" @click="preview(scope.row)">预览</el-button>
              </template>
              <template
                v-if="$hasPermission('modify')"
                desc="修改"
                actions="@hbfe/jxjy-admin-examPaper/src/modify.vue"
              >
                <el-button type="text" size="mini" @click="modify(scope.row)">修改</el-button>
              </template>

              <!-- {{ scope.row.status }} -->
              <template v-if="$hasPermission('enable')" desc="启用" actions="enable">
                <hb-popconfirm placement="top" title="确定启用该试卷？" @confirm="enable(scope.row)">
                  <el-button type="text" slot="reference" size="mini" v-show="scope.row.status == 2">启用</el-button>
                </hb-popconfirm>
              </template>
              <template v-if="$hasPermission('disable')" desc="停用" actions="disable">
                <hb-popconfirm placement="top" title="确定停用该试卷？" @confirm="disable(scope.row)">
                  <el-button type="text" slot="reference" size="mini" v-show="scope.row.status == 1">停用</el-button>
                </hb-popconfirm>
              </template>

              <!-- <el-button
                size="mini"
                type="text"
                v-if="scope.row.status == 2"
                @click="afterEnable(scope.row)"
                class="ml10"
                >启用</el-button
              >
              <el-button
                size="mini"
                type="text"
                v-if="scope.row.status == 1"
                @click="afterDisable(scope.row)"
                class="ml10"
                >停用</el-button
              > -->
              <template v-if="$hasPermission('copy')" desc="复制" actions="copy">
                <el-button type="text" size="mini" @click="copy(scope.row.id)">复制</el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->

        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </el-card>
      <el-dialog :visible.sync="enableDialog" :lock-scroll="true" :append-to-body="true" width="30%">
        <span> 确定启用该试卷?</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="enableDialog = false">取 消</el-button>
          <el-button type="primary" @click="enable(currentId)">
            确 定
          </el-button>
        </span>
      </el-dialog>
      <el-dialog :visible.sync="disableDialog" :lock-scroll="true" :append-to-body="true" width="30%">
        <span> 确定停用该试卷?</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="disableDialog = false">取 消</el-button>
          <el-button type="primary" @click="disable(currentId)">
            确 定
          </el-button>
        </span>
      </el-dialog>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import BizExamPaperCategory from '@hbfe/jxjy-admin-components/src/biz/biz-exam-paper-category.vue'
  import PaperClassifyTree from '@hbfe/jxjy-admin-components/src/paper-classify-tree.vue'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { UiPage, Query } from '@hbfe/common'
  import { PublishPatternTypes } from '@api/service/management/resource/exam-paper/enum/ExamPaperPublishPatternTypes'
  import PaperPublishConfigureRequestVo from '@api/service/management/resource/exam-paper/query/vo/PaperPublishConfigureRequestVo'
  import PaperPublishConfigureResponseVo from '@api/service/management/resource/exam-paper/query/vo/PaperPublishConfigureResponseVo'
  @Component({
    components: { BizExamPaperCategory, PaperClassifyTree }
  })
  export default class extends Vue {
    /**
     *  ui控制相关
     */
    uiConfig = {
      loading: false
    }
    page: UiPage
    query: Query = new Query()
    currentId = new PaperPublishConfigureResponseVo()
    requestParams = new PaperPublishConfigureRequestVo()
    mutationExamPaper = ResourceModule.mutationExamPaperFactory
    paperPublishConfigureResponse = new Array<PaperPublishConfigureResponseVo>()
    classifyTree = true // 主要是 tree这个组件生产了数据树之后无法重新更新，只能这样删除了
    enableDialog = false
    disableDialog = false
    examCategory = ''
    isReload = true
    // 表单刷新延时
    refreshTime = 1500

    /**
     *  选中的试卷分类id
     */
    paperClassifyId = ''
    /**
     * 组卷配置类型
     */
    configTypeList = [
      {
        title: '智能组卷',
        value: '0'
      }
    ]
    /**
     *  试卷状态
     */
    paperStatusOption = [
      {
        title: '启用',
        value: 1
      },
      {
        title: '禁用',
        value: 2
      }
    ]

    @Ref('paperClassifyRef')
    paperClassifyComponent: any
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    async activated() {
      // this.init()
      await this.doQueryPage()
    }
    /**
     * 初始化加载模块
     */
    async init() {
      await this.doQueryPage()
    }

    getPaperPublishPatternName(type: string) {
      const obj = {
        [PublishPatternTypes.AutomaticPublishPattern]: '智能组卷'
      }
      if (!obj[type]) {
        return
      }
      return obj[type]
    }

    pageSizeChange(val: number) {
      this.page.pageSize = val
      this.page.pageNo = 1
      this.doQueryPage()
    }

    currentPageChange(val: number) {
      this.page.pageNo = val
      this.doQueryPage()
    }
    rate = 4.5
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    /**
     * 创建试卷
     */
    create() {
      this.$router.push('/resource/exam-paper/create')
    }

    /**
     * 修改试卷
     */
    modify(item: PaperPublishConfigureResponseVo) {
      this.$router.push(`/resource/exam-paper/modify/${item.id}`)
    }

    /**
     * 试卷详情
     */
    detail(item: PaperPublishConfigureResponseVo) {
      // const randomId = Mock.Random.guid()
      this.$router.push(`/resource/exam-paper/detail/${item.id}`)
    }

    /**
     * 试卷分类管理
     */
    category() {
      this.$router.push('/resource/exam-paper/category')
    }
    async searchBase() {
      this.page.pageNo = 1
      await this.doQueryPage()
    }

    /**
     * 重置条件
     */
    resetCondition() {
      this.page.pageNo = 1
      this.requestParams = new PaperPublishConfigureRequestVo()
      this.classifyTree = false
      this.$nextTick(() => {
        // this.isReload = true
        this.classifyTree = true
      })
      this.examCategory = ''

      this.doQueryPage()
      // this.paperClassifyComponent.clearChoose()
    }
    async doQueryPage() {
      this.uiConfig.loading = true

      //   if (this.examCategory) {
      //     this.requestParams.categoryIdList = [this.examCategory]
      //   }
      this.requestParams.categoryIdList = this.examCategory ? [this.examCategory] : []
      const res = await ResourceModule.queryExamPaperFactory.queryExamPaperList.queryExamPaperList(
        this.page,
        this.requestParams
      )
      this.paperPublishConfigureResponse = res.data
      console.log(this.paperPublishConfigureResponse, 'paperPublishConfigureResponse')
      this.uiConfig.loading = false
    }
    /**
     * 启用试卷
     * @param item
     */
    async enable(item: PaperPublishConfigureResponseVo) {
      const res = await this.mutationExamPaper.getExamPaperAction(item.id).doEnable()
      if (res.code == 200) {
        this.$message.success('启用成功')
        this.enableDialog = false
        // setTimeout(() => {
        this.delayToRefreshTable(() => {
          this.doQueryPage()
        })
        // }, 100)
      } else {
        this.enableDialog = false

        this.$message.error('启用失败')
      }
    }

    /**
     * 停用试卷
     * @param item
     */
    async disable(item: PaperPublishConfigureResponseVo) {
      const res = await this.mutationExamPaper.getExamPaperAction(item.id).doDisabled()
      if (res.code == 200) {
        this.$message.success('停用成功')
        this.disableDialog = false
        // setTimeout(() => {
        this.delayToRefreshTable(() => {
          this.doQueryPage()
        })
        // }, 100)
      } else {
        this.$message.error('停用失败')
      }
    }
    /**
     * 复制试卷
     * @param item
     */
    async copy(id: string) {
      // let res
      // try {
      //   res = await ExamPaperModule.copyExamPaper({ id: item.id, newName: item.name + '_复制' })
      // } catch (error) {
      //   console.error(error)
      //   this.$message.error('试卷复制失败')
      // }
      // if (res?.isSuccess()) {
      //   this.$message.success('试卷复制成功')
      //   this.doQueryPage()
      // } else {
      //   this.$message.error('试卷复制失败' + res?.errors[0].message)
      // }
      const tempObject = await ResourceModule.mutationExamPaperFactory.getCopyExamPaper(id)
      const queryRes = await tempObject.doQueryExamPaper()
      const res = await tempObject.doCopyExamPaper()
      if (res.isSuccess()) {
        this.$message.success('试卷复制成功')
        // setTimeout(() => {
        this.delayToRefreshTable(() => {
          this.searchBase()
        })
        // }, 100)
      } else {
        this.$message.error('试卷复制失败')
      }
    }
    afterEnable(item: PaperPublishConfigureResponseVo) {
      this.enableDialog = true
      this.currentId = item
    }
    afterDisable(item: PaperPublishConfigureResponseVo) {
      this.disableDialog = true
      this.currentId = item
    }
    /**
     * 试卷预览
     */
    async preview(item: PaperPublishConfigureResponseVo) {
      if (item.type === 2) {
        this.$message.warning('当前试卷按照学员选课ID出题，暂不支持预览')
        return
      }
      const time = (item.suggestionTimeLength * 60).toString()
      // const routeData = this.$router.resolve({
      //   path: '/exam/mockPreview',
      //   query: { id: item.id, timeLength: time }
      // })
      // window.open(routeData.href, '_blank')
      window.open([`/exam/mockPreview`, `?id=${item.id}&timeLength=${time}&examName=${item.name}`].join(''))
    }

    //延时刷新表单
    delayToRefreshTable(f: Function) {
      setTimeout(() => {
        f()
      }, this.refreshTime)
    }
  }
</script>
