<template>
  <div>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/special-topics/thematic-information-management' }"
        >专题资讯管理</el-breadcrumb-item
      >
      <el-breadcrumb-item>修改专题资讯</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" class="m-form">
                <el-form-item label="资讯类别：" prop="categoryType">
                  <information-classification
                    v-model="form.categoryType"
                    ref="classificationRef"
                    :checkStrictly="false"
                    @getNewsCode="getNewsCode"
                  ></information-classification>
                </el-form-item>
                <el-form-item label="资讯标题：" prop="title">
                  <el-input v-model="form.title" clearable placeholder="请输入资讯标题" />
                </el-form-item>
                <!--编辑时-->
                <el-form-item label="发布至：" class="is-text" required="">
                  {{ form.specialList.length ? form.specialList[0].specialSubjectName : '-' }}
                </el-form-item>
                <el-form-item label="摘要信息：">
                  <el-input type="textarea" :rows="6" v-model="form.abstract" placeholder="请输入摘要信息" />
                </el-form-item>
                <el-form-item label="封面图片：">
                  <cover-image-upload
                    :dialogStyleOpation="{
                      width: '500px',
                      height: '300px'
                    }"
                    :ratioArr="['420:225']"
                    :initWidth="420"
                    v-model="form.bgImage"
                  ></cover-image-upload>
                </el-form-item>
                <el-form-item label="资讯内容：" prop="content">
                  <div class="rich-text">
                    <hb-tinymce-editor v-if="active" v-model="form.content" ref="tinymceEditorRef"></hb-tinymce-editor>
                  </div>
                </el-form-item>
                <el-form-item label="资讯来源：">
                  <el-input v-model="form.source" clearable placeholder="请输入资讯来源" />
                </el-form-item>
                <el-form-item label="发布时间：">
                  <el-date-picker
                    v-model="form.time"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    type="datetime"
                    placeholder="选择日期时间"
                    class="form-s"
                  />
                </el-form-item>
                <el-form-item label="弹窗公告：" v-if="isShowPopup">
                  <el-radio-group v-model="form.isPopup">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="弹窗时间：" v-if="form.isPopup">
                  <double-date-picker
                    :begin-create-time.sync="form.popupBeginTime"
                    :end-create-time.sync="form.popupEndTime"
                    :isLimitEndTimeSecond="isLimitEndTimeSecond"
                  ></double-date-picker>
                </el-form-item>
                <el-form-item label="是否置顶：">
                  <el-radio-group v-model="form.top">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="cancel">取消</el-button>
        <el-button @click="saveDraft" :loading="saveDraftLoading">保存草稿</el-button>
        <el-button type="primary" @click="publish" :loading="publishLoading">发布</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import InformationClassification from '@hbfe/jxjy-admin-specialTopics/src/thematic-information-management/components/information-classification.vue'
  import SelectedSpecial from '@hbfe/jxjy-admin-specialTopics/src/thematic-information-management/components/selected-special.vue'
  import CoverImageUpload from '@hbfe/jxjy-admin-platform/src/components/cover-image-upload.vue'
  import DoubleDatePicker from '@hbfe/jxjy-admin-specialTopics/src/thematic-information-management/components/news-date-picker.vue'
  import { NewsCategoryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import CreateDraftNewsVo from '@api/service/management/news/query/query-news-detail/vo/NewsDetail'
  import NewsModule from '@api/service/management/news/NewsModule'
  import MutationNewsFactory from '@api/service/management/news/mutation/MutationNewsFactory'
  import MutationNewsChangeStatus from '@api/service/management/news/mutation/mutation-news-change-status/MutationNewsChangeStatus'
  import MutationCreateNews from '@api/service/management/news/mutation/mutation-news-created/MutationCreateNews'
  import { ElForm } from 'element-ui/types/form'
  import { debounce, bind } from 'lodash-decorators'
  class Category extends NewsCategoryResponse {
    children?: Array<Category>
    fatherId?: string
  }
  @Component({
    components: {
      InformationClassification,
      CoverImageUpload,
      SelectedSpecial,
      DoubleDatePicker
    }
  })
  export default class extends Vue {
    /**
     * 表单ref
     */
    @Ref('formRef')
    formRef: ElForm
    /**
     * 资讯分类组件ref
     */
    @Ref('classificationRef')
    classificationRef: InformationClassification
    /**
     * 是否显示弹窗选项
     */
    isShowPopup = true
    /**
     * 富文本显示框
     */
    active = false
    /**
     * 表单验证规则
     */
    rules = {
      categoryType: [{ required: true, message: '请选择资讯类别', trigger: ['blur', 'change'] }],
      title: [{ required: true, message: '请输入资讯标题', trigger: 'blur' }],
      content: [{ required: true, message: '请输入资讯内容', trigger: 'blur' }]
    }
    /**
     * 表单数据
     */
    form = new CreateDraftNewsVo()
    /**
     * 通知栏目id
     */
    notificationId = ''
    /**
     * 资讯栏目列表
     */
    informationClassificationOptions = new Array<Category>()
    /**
     * 创建专题实例化
     */
    mutationCreateNews = new MutationCreateNews()
    /**
     * 发布按钮loading
     */
    publishLoading = false
    /**
     * 保存草稿按钮loading
     */
    saveDraftLoading = false
    /**
     * 默认结束时分秒
     */
    isLimitEndTimeSecond = '23:59:59'
    @Watch('form.categoryType', { deep: true, immediate: true })
    formWatchIsPopup(val: Array<string>) {
      if (val && val[0] == this.notificationId) {
        this.isShowPopup = false
        this.form.isPopup = false
      }
    }
    async created() {
      this.init()
      this.informationClassificationOptions = await NewsModule.queryNewsFactory
        .getQueryNewsList()
        .queryNewsRootCategory()
      this.notificationId = this.informationClassificationOptions.filter((item) => {
        return item.code == 'MESSAGE'
      })[0].newsCategoryId
      await this.classificationRef.getInformationClassificationStair()
      this.classificationRef.echo()
      this.form = await NewsModule.queryNewsFactory.getQueryNewsDetail(this.$route.params.id).queryNewsDetail()
      let secondCategoryType: Category
      this.classificationRef.informationClassificationOptions.map((item) => {
        const secondCategoryTypeChild = item.children?.find((ite) => ite.newsCategoryId === this.form.categoryType[0])
        if (secondCategoryTypeChild) secondCategoryType = secondCategoryTypeChild
      })
      if (secondCategoryType) {
        this.form.categoryType = [secondCategoryType.fatherId, this.form.categoryType[0]]
      }
      for (let i = 0; i < this.informationClassificationOptions.length; i++) {
        if (
          this.informationClassificationOptions[i].newsCategoryId ===
            this.form.categoryType[this.form.categoryType.length - 1] &&
          this.informationClassificationOptions[i].code === 'MESSAGE'
        ) {
          this.form.isPopup = false
          return (this.isShowPopup = false)
        } else {
          this.isShowPopup = true
        }
      }
    }
    async init() {
      this.active = false
      setTimeout(() => {
        this.active = true
      }, 100)
    }
    /**
     * 获取资讯分类code
     */
    async getNewsCode(informationClassificationOptions: Array<Category>, id: string) {
      for (let i = 0; i < informationClassificationOptions.length; i++) {
        if (
          informationClassificationOptions[i].newsCategoryId === id &&
          informationClassificationOptions[i].code === 'MESSAGE'
        ) {
          return (this.isShowPopup = false)
        } else {
          this.isShowPopup = true
        }
      }
    }
    /**
     * 取消新建
     */
    cancel() {
      this.$router.push('/training/special-topics/thematic-information-management')
    }
    /**
     * 发布时间
     */
    getCurrentDateTime() {
      const now = new Date()
      const year = now.getFullYear()
      let month = now.getMonth() + 1 // 月份是从0开始的，所以需要+1
      let day = now.getDate()
      let hours = now.getHours()
      let minutes = now.getMinutes()
      let seconds = now.getSeconds()

      // 补零操作，确保月、日、时、分、秒都是两位数
      month = Number((month < 10 ? '0' : '') + month)
      day = Number((day < 10 ? '0' : '') + day)
      hours = Number((hours < 10 ? '0' : '') + hours)
      minutes = Number((minutes < 10 ? '0' : '') + minutes)
      seconds = Number((seconds < 10 ? '0' : '') + seconds)

      return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
    }
    /**
     * 保存草稿
     */
    @bind
    @debounce(200)
    async saveDraft() {
      const mutationNewsObj = new MutationNewsFactory()
      const upDataNewsObj = new MutationNewsChangeStatus(this.$route.params.id)
      this.formRef.validate(async (valid: boolean) => {
        if (valid) {
          this.saveDraftLoading = true
          if (this.form.isPopup) {
            if (!this.form.popupBeginTime || !this.form.popupEndTime) {
              this.saveDraftLoading = false
              return this.$message.warning('请选择弹窗时间！')
            }
            if (new Date(this.form.popupBeginTime).getTime() >= new Date(this.form.popupEndTime).getTime()) {
              this.saveDraftLoading = false
              return this.$message.warning('请调整正确的弹窗时间区间！')
            }
          } else {
            this.form.popupBeginTime = undefined
            this.form.popupEndTime = undefined
          }
          this.form.verifyPopUps = false
          try {
            const res = await mutationNewsObj.mutationNewsUpdate().doUpdateSpecialNews(this.form)
            if (res.isSuccess()) {
              const result = await upDataNewsObj.doDraftNews()
              if (result.isSuccess()) {
                this.$message.success('修改成功')
                this.cancel()
              } else {
                this.$message.warning(result.getMessage())
              }
            } else {
              this.$message.warning(res.getMessage())
            }
          } catch (e) {
            console.log(e)
            this.saveDraftLoading = false
          }
        }
      })
    }
    /**
     * 发布
     */
    @bind
    @debounce(200)
    async publish() {
      const mutationNewsObj = new MutationNewsFactory()
      this.formRef.validate(async (valid: boolean) => {
        if (valid) {
          this.publishLoading = true
          if (this.form.isPopup) {
            if (!this.form.popupBeginTime || !this.form.popupEndTime) {
              this.publishLoading = false
              return this.$message.warning('请选择弹窗时间！')
            }
            if (new Date(this.form.popupBeginTime).getTime() >= new Date(this.form.popupEndTime).getTime()) {
              this.publishLoading = false
              return this.$message.warning('请调整正确的弹窗时间区间！')
            }
          } else {
            this.form.popupBeginTime = undefined
            this.form.popupEndTime = undefined
          }
          if (!this.form.time) {
            this.form.time = this.getCurrentDateTime()
          }
          this.form.content = this.form.content.replace(/"..\/mfs/g, '"/mfs')
          this.form.verifyPopUps = true
          try {
            const res = await mutationNewsObj.mutationNewsUpdate().doUpdateSpecialNews(this.form)
            if (res.isSuccess()) {
              const result = await mutationNewsObj.mutationNewsChangeStatus(this.$route.params.id).doPublishNews()
              if (result.isSuccess()) {
                this.$message.success('修改成功')
                this.cancel()
              } else {
                this.$message.warning(result.getMessage())
              }
            } else {
              this.$message.warning(res.getMessage())
            }
          } catch (e) {
            console.log(e)
          } finally {
            this.publishLoading = false
          }
        }
      })
    }
    deactivated() {
      ;(this.$refs['tinymceEditorRef'] as any).activated = false
    }
  }
</script>
