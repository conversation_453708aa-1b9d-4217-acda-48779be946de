<template>
  <jxgx-special-invoice ref="electronicInvoiceOffline"></jxgx-special-invoice>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import TradeExport from '@api/service/diff/management/jxgx/trade/TradeExport'
  import SpecialInvoice from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/special-invoice.vue'

  @Component
  class JxgxSpecialInvoice extends SpecialInvoice {
    //
    tradeExport = new TradeExport()

    /**
     * 线下专票导出
     */
    async offLinePageVatspecialplaInvoiceInExport() {
      return await this.tradeExport.offLinePageVatspecialplaInvoiceInExport(this.exportQueryParam)
    }
  }

  @Component({
    components: {
      JxgxSpecialInvoice
    }
  })
  export default class extends Vue {
    @Ref('electronicInvoiceOffline') electronicInvoiceOffline: JxgxSpecialInvoice

    /**
     * 线下专票查询
     */
    offLinePageInvoiceInServicer() {
      return this.electronicInvoiceOffline.offLinePageVatspecialplaInvoiceInServicer()
    }

    /**
     * 专题线下专票查询
     */
    offLinePageInvoiceInZtServicer() {
      return this.electronicInvoiceOffline.offLinePageVatspecialplaInvoiceInZtServicer()
    }
  }
</script>
