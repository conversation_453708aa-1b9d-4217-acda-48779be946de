<!--
 * @Author: lixin<PERSON> <EMAIL>
 * @Date: 2023-07-24 20:12:34
 * @LastEditors: lixinye <EMAIL>
 * @LastEditTime: 2023-07-25 11:27:51
 * @FilePath: \jxjyv2_frontend_web_admin\src\unit-share\network-school\statistic\statistics-report\__components__\class-info.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!-- 培训班信息 -->
<template>
  <!-- 培训班信息 -->
  <div class="f-p20" v-loading="loadStatus">
    <el-row class="no-gutter">
      <el-form :inline="true" label-width="100px" class="m-text-form f-mt10">
        <el-col :span="8">
          <el-form-item label="学时：">{{ period }}</el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="班级名称：">{{ schemeName }}</el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import { UiPage } from '@hbfe/common'
  import AntiSchemeParams from '@api/service/management/train-class/query/vo/AntiSchemeParams'
  import AntiSchemeItem from '@api/service/management/train-class/query/vo/AntiSchemeItem'

  @Component
  export default class extends Vue {
    // 加载状态
    loadStatus = false

    // 查询对象
    queryObj = new QueryTrainClassCommodityList()

    page: UiPage = new UiPage()
    constructor() {
      super()
    }

    // 培训班数据
    classDetail: any = null

    // 加载培训班信息
    async loadData(schemeId: string) {
      this.loadStatus = true
      try {
        if (!schemeId) return new Error('schemeId为空')
        const params = new AntiSchemeParams()
        params.schemeId = schemeId
        this.classDetail = (await this.queryObj.pageAntiSchemeList(this.page, params))[0]
      } catch (error) {
        console.log(error)
      } finally {
        this.loadStatus = false
      }
    }

    // 学时
    get period() {
      if (this.classDetail && !isNaN(this.classDetail.period)) return this.classDetail.period
      return '-'
    }

    // 课程名称
    get schemeName() {
      if (this.classDetail && this.classDetail.schemeName) return this.classDetail.schemeName
      return '-'
    }
  }
</script>
