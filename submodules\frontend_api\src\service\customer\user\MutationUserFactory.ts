import MutationForgetResetPwd from '@api/service/customer/user/mutation/MutationForgetResetPwd'
import MutationResetPwd from '@api/service/customer/user/mutation/MutationResetPwd'
import MutationUnbindThirdAccount from '@api/service/customer/user/mutation/MutationUnbindThirdAccount'
import MutationRegisterUser from '@api/service/customer/user/mutation/MutationRegisterUser'
import MutationUpdateUserInfo from '@api/service/customer/user/mutation/MutationUpdateUserInfo'
import MutationRegisterBatch from '@api/service/customer/user/mutation/MutationRegisterBatch'
import MutationUpdateCollectiveInfo from './mutation/MutationUpdateCollectiveInfo'
/**
 * 业务工厂类
 */
class MutationUserFactory {
  /**
   *获取忘记密码-重置密码实例
   */
  getMutationForgetResetPwd() {
    return new MutationForgetResetPwd()
  }
  /**
   *修改密码实例
   */
  getMutationResetPwd() {
    return new MutationResetPwd()
  }
  /**
   *获取注册学员实例
   */
  getMutationRegisterUser() {
    return new MutationRegisterUser()
  }
  /**
   *获取集体注册实例
   */
  getMutationRegisterBatch() {
    return new MutationRegisterBatch()
  }

  /**
   *获取第三方平台解绑业务对象
   */
  getMutationUnbindThirdAccount() {
    return new MutationUnbindThirdAccount()
  }

  /**
   *获取更新学员用户信息
   */
  getMutationUpdateUserInfo() {
    return new MutationUpdateUserInfo()
  }
  /**
   * 获取更新集体用户信息
   */
  getMutationUpdateCollectiveInfo() {
    return new MutationUpdateCollectiveInfo()
  }
}
export default MutationUserFactory
