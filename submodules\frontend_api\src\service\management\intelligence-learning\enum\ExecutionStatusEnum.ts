import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum ExecutionStatusEnum {
  /**
   * 执行失败
   */
  fail,
  /**
   * 终止
   */
  stop,
  /**
   * 已完成
   */
  complete
}
export default class ExecutionStatusType extends AbstractEnum<ExecutionStatusEnum> {
  static enum = ExecutionStatusEnum
  constructor(status?: ExecutionStatusEnum) {
    super()
    this.current = status
    this.map.set(ExecutionStatusEnum.fail, '执行失败')
    this.map.set(ExecutionStatusEnum.stop, '终止')
    this.map.set(ExecutionStatusEnum.complete, '已完成')
  }
}
