import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 题库资源来源类型
 */
enum LibrarySourceTypesEnum {
  CREATE = 1,
  IMPORT = 2
}

export { LibrarySourceTypesEnum }
class LibrarySourceTypes extends AbstractEnum<LibrarySourceTypesEnum> {
  static enum = LibrarySourceTypesEnum

  constructor(status?: LibrarySourceTypesEnum) {
    super()
    this.current = status
    this.map.set(LibrarySourceTypesEnum.CREATE, '创建')
    this.map.set(LibrarySourceTypesEnum.IMPORT, '导入')
  }
}

export default new LibrarySourceTypes()
