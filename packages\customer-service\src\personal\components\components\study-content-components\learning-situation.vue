<template>
  <div
    v-if="$hasPermission('learningSituation')"
    desc="学习情况"
    actions="init,queryTrainingPeriod,queryCoursePeriodList,visibilityCourseQuizRecordConfig,visibilityCoursePeriodTableConfig"
  >
    <el-tabs v-model="activeName" class="m-tab-custom" v-if="showTab">
      <el-tab-pane label="线上课程" name="online"> </el-tab-pane>
      <el-tab-pane label="培训期别" name="traingPeriod"> </el-tab-pane>
    </el-tabs>
    <!--  线上课程  -->
    <div v-if="showContext('online')">
      <div class="m-tit is-small">
        <span class="tit-txt">考核情况</span>
      </div>
      <!-- 选课规则 -->
      <el-form label-width="120px" class="m-form f-ml20 f-mt10">
        <el-form-item label="整体考核进度：">
          <el-progress
            :show-text="false"
            :stroke-width="26"
            :percentage="
              getPercentageProgress(
                baseInfoDetail.course.totalRequirePeriod,
                baseInfoDetail.course.totalCompletePeriod
              ) || 0
            "
            class="f-mt5 form-l"
          />
          <div class="f-mt5">
            <span class="f-mr40 f-fb f-co">要求：{{ baseInfoDetail.course.totalRequirePeriod }}学时</span>
            <span class="f-mr40"
              ><i class="f-dot f-mr5"></i>已完成：{{ baseInfoDetail.course.totalCompletePeriod }}学时</span
            >
            <span class="f-mr40"
              ><i class="f-dot gray f-mr5"></i>还差：{{ baseInfoDetail.course.totalUnCompletePeriod }}学时</span
            >
          </div>
        </el-form-item>
        <!--
                班内选课规则时展示
                培训班自主选课时隐藏
              -->
        <el-form-item label="选课情况：" v-if="baseInfoDetail.basicInfo.schemeType === 1">
          <el-progress
            :show-text="false"
            :stroke-width="26"
            :percentage="
              getPercentageProgress(
                baseInfoDetail.course.courseRequirePeriod,
                baseInfoDetail.course.courseCompletePeriod
              ) || 0
            "
            class="f-mt5 form-l"
          />
          <div class="f-mt5">
            <span class="f-mr40 f-fb f-co">要求：{{ baseInfoDetail.course.courseRequirePeriod }}学时</span>
            <span class="f-mr40"
              ><i class="f-dot f-mr5"></i>已选：{{ baseInfoDetail.course.courseCompletePeriod }}学时</span
            >
            <span class="f-mr40"><i class="f-dot gray f-mr5"></i>还差：{{ courseUnCompletePeriod }}学时</span>
          </div>
        </el-form-item>
      </el-form>

      <div class="m-tit is-small f-flex">
        <span class="tit-txt">课程学时</span>
        <span class="f-ml10"
          >（共 <i class="f-cr">{{ coursePeriodCount }}</i> 门课程）</span
        >
      </div>

      <el-table
        :data="coursePeriodTableData"
        ref="coursePeriodTableRef"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        max-height="500px"
        class="m-table"
        v-loading="uiStatus.query.tableLoading"
      >
        <el-table-column type="index" width="5">
          <template slot-scope="scope">
            <span :data-index="scope.$index + 1" v-observe-visibility="visibilityCoursePeriodTableConfig"></span>
          </template>
        </el-table-column>

        <el-table-column label="课程名称" min-width="300">
          <template slot-scope="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="课程分类" min-width="150">
          <template slot-scope="scope">{{
            scope.row.classifyInfo ? classifyInfo(scope.row.classifyInfo) : ''
          }}</template>
        </el-table-column>
        <el-table-column label="时长" min-width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.resourceType === 1">
              {{ formatTimeToHours(scope.row.courseTimeLength) }}
            </span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="学时" min-width="100" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.resourceType === 1">
              {{ scope.row.period }}
            </span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="测验" min-width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.resourceType === 1">
              {{ scope.row.quizScore }}分/{{ getQuizStatusName(scope.row.courseQuizStatus) }}
            </span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="进度" min-width="110" align="right">
          <template slot-scope="scope">{{ scope.row.schedule }}%</template>
        </el-table-column>
        <el-table-column label="开始学习时间" min-width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.haveSimulate && !scope.row.onlyHaveExamSimulate">
              <el-tooltip
                popper-class="study-content-tooltip-style"
                class="item"
                effect="dark"
                :content="
                  '同步第三方数据：' + scope.row.simulateCourseStartTime + '/' + scope.row.simulateCourseEndTime
                "
                placement="top"
              >
                <i class="f-dot red f-mr5"></i>
              </el-tooltip>
              {{ formatTime(scope.row.startLearningTime) }}
            </div>
            <div v-else>
              {{ formatTime(scope.row.startLearningTime) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="最后学习时间" min-width="170">
          <template slot-scope="scope">{{ formatTime(scope.row.lastLearningTime) }}</template>
        </el-table-column>
        <el-table-column label="操作" prop="field02" width="230" align="center" fixed="right">
          <template slot-scope="scope">
            <template v-if="$hasPermission('studty')" desc="一键学习" actions="oneKeyStudy">
              <el-button
                type="text"
                size="mini"
                @click="oneKeyStudy(scope.row)"
                :disabled="baseInfoDetail.schemeStatus === 2"
                >一键学习</el-button
              >
            </template>
            <!-- 课件的话隐藏以下按钮 -->
            <template v-if="scope.row.resourceType === 1">
              <el-button type="text" size="mini" @click="lookExamDetail(scope.row)">测验详情</el-button>
              <template v-if="$hasPermission('deleteCourse')" desc="删除课程" actions="deleteCourse">
                <el-button type="text" size="mini" @click="deleteCourse(scope.row)">删除课程</el-button>
              </template>
            </template>
            <template
              v-if="$hasPermission('toLog')"
              desc="监管日志"
              actions="@hbfe/jxjy-admin-statisticsReport/src/study-log.vue"
            >
              <el-button type="text" size="mini" @click="toStudyLog">监管日志</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <!-- 学习情况 课程的一键学习抽屉 -->
      <el-drawer
        title="一键学习"
        :visible.sync="uiStatus.isShow.oneKeyStudyDrawer"
        size="800px"
        custom-class="m-drawer"
        :close-on-press-escape="false"
        :wrapper-closable="false"
      >
        <div class="drawer-bd">
          <el-alert type="warning" show-icon :closable="false" class="m-alert">
            课程存在测验考核要求，是否确认一键学习？一键学习会同步合格课后测验。
          </el-alert>
          <el-form ref="oneKeyStudyFormRef" label-width="auto" class="m-form f-mt40">
            <el-form-item label="设定测验合格分：" required>
              <el-input v-model="courseParams.courseTestPoint" class="input-num" placeholder="请输入" />
              <span class="f-ml5">分</span>
              <span class="f-cb">（及格 / 满分：{{ passingScore }} / 100分）</span>
            </el-form-item>
            <el-form-item>
              <span class="f-co">注：请设置合格成绩介于合格分和满分之间。</span>
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="closeOneKeyStudyDrawer">取消</el-button>
              <el-button type="primary" @click="confirmOneKeyStudyDrawer">确认</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-drawer>
      <!--学习情况 课程的测验详情列表抽屉-->
      <el-drawer
        title="测验详情"
        ref="ExaminationDetailDrawerRef"
        :visible.sync="uiStatus.isShow.courseExamDrawer"
        size="800px"
        custom-class="m-drawer"
        v-loading="uiStatus.query.loadCourseExamTest"
      >
        <div class="drawer-bd">
          <el-table stripe :data="courseQuizRecordTableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center">
              <template slot-scope="scope">
                <span :data-index="scope.$index + 1" v-observe-visibility="visibilityCourseQuizRecordConfig">{{
                  scope.$index + 1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="测验提交时间" min-width="300" prop="quizSubmitTime"> </el-table-column>
            <el-table-column label="测验成绩" min-width="180">
              <template slot-scope="scope">{{ scope.row.quizScore }}分</template>
            </el-table-column>
            <el-table-column label="是否合格" min-width="120">
              <template slot-scope="scope">
                <div v-if="!scope.row.isQuizQualified">
                  <el-badge is-dot type="danger" class="badge-status">未合格</el-badge>
                </div>
                <div v-else>
                  <el-badge is-dot type="success" class="badge-status">合格</el-badge>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-drawer>
    </div>
    <!--  培训期别  -->
    <div v-if="showContext('traingPeriod')" v-show="this.userId !== ''">
      <div class="m-offline-course" v-loading="uiStatus.query.loadTraingPeriod">
        <div class="item" v-if="issueInfo.isOpenAttendance">
          <div class="status" v-if="trainingPeriodTableData.attendance.qualified">
            <el-tag type="success">已合格</el-tag>
          </div>
          <div class="status" v-else><el-tag type="danger">未合格</el-tag></div>
          <div class="require">
            考勤要求：扫码/GPS签到/签退，考勤率>={{ trainingPeriodTableData.requireAttendanceRate }}%（要求签到/签退
            {{ trainingPeriodTableData.attendanceRequireNum }} 次）
          </div>
          <div class="rate">
            考勤率：{{ trainingPeriodTableData.studentAttendanceRate }}%（完成签到/签退
            {{ trainingPeriodTableData.attendance.completedNum }} 次）
          </div>
          <div class="op"><el-button type="text" @click="showAttendanceDetails()">查看考勤详情</el-button></div>
        </div>
        <div class="item" v-if="issueInfo.isOpenGraduationTest">
          <div class="status" v-if="trainingPeriodTableData.graduationStatus == GraduationStatusEnum.qualified">
            <el-tag type="success">已合格</el-tag>
          </div>
          <div class="status" v-if="trainingPeriodTableData.graduationStatus == GraduationStatusEnum.unqualified">
            <el-tag type="danger">未合格</el-tag>
          </div>
          <div class="status" v-if="trainingPeriodTableData.graduationStatus == GraduationStatusEnum.notAssessed">
            <el-tag type="danger">未考核</el-tag>
          </div>
          <div class="status" v-if="trainingPeriodTableData.graduationStatus == GraduationStatusEnum.unAssessed">
            <el-tag type="warning">不纳入考核</el-tag>
          </div>
          <div class="require">
            结业测试：<span v-if="showData && trainingPeriodTableData.graduationQualifiedTime">线下认定</span
            ><span v-else>-</span>
          </div>
          <div class="rate">合格时间：{{ trainingPeriodTableData.graduationQualifiedTime || '-' }}</div>
        </div>
      </div>
    </div>
    <template v-if="$hasPermission('attendanceDetailsDrawer')" desc="考勤详情" actions="@AttendanceDetailsDrawer">
      <attendance-details-drawer ref="attendanceDetailsDrawerRef"></attendance-details-drawer>
    </template>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import StudentTrainClassDetailVo from '@api/service/management/train-class/query/vo/StudentTrainClassDetailVo'
  import SchemeLearningInfoVo from '@api/service/management/train-class/query/vo/SchemeLearningInfoVo'
  import { ElTable } from 'element-ui/types/table'
  import { bind, debounce } from 'lodash-decorators'
  import StudentCourseVo from '@api/service/management/resource/course/query/vo/StudentCourseVo'
  import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
  import QueryCourseQuizRecordListVo from '@api/service/management/train-class/query/vo/QueryCourseQuizRecordListVo'
  import { Page } from '@hbfe/common'
  import QueryStudentTrainClass from '@api/service/management/train-class/query/QueryStudentTrainClass'
  import MutationDeleteCourse from '@api/service/management/train-class/mutation/MutationDeleteCourse'
  import MutationQualifiedCourseware from '@api/service/management/train-class/mutation/MutationQualifiedCourseware'
  import MutationQualifiedCourse from '@api/service/management/train-class/mutation/MutationQualifiedCourse'
  import CourseQuizRecordDetailVo from '@api/service/management/train-class/query/vo/CourseQuizRecordDetailVo'
  import AttendanceDetailsDrawer from '@hbfe/jxjy-admin-customerService/src/personal/components/components/study-content-components/attendance-details-drawer.vue'
  import StudentPeriodLog from '@api/service/management/train-class/offlinePart/model/StudentPeriodLog'
  import { GraduationStatusEnum } from '@api/service/common/scheme/enum/GraduationStatusEnum'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import QueryStudentStudy from '@api/service/management/train-class/offlinePart/QueryStudentStudy'
  @Component({
    components: {
      AttendanceDetailsDrawer
    }
  })
  export default class extends Vue {
    activeName = 'online'

    /**
     * @description 存在线上课程
     * */
    isOnline = true

    /**
     * @description 存在培训讯期别
     * */
    isTrainingPeriod = false

    /**
     * @description 展示数据
     * */
    showData = false

    // 查询课程分类实例
    coursePage: Page
    constructor() {
      super()
      this.coursePage = new Page(1, 20)
    }

    TrainingModeEnum = TrainingModeEnum

    uiStatus = {
      query: {
        loadCourseExamTest: false,
        loadCoursePeriodPage: false,
        loadTraingPeriod: false,
        tableLoading: false
      },
      isShow: {
        courseExamDrawer: false,
        oneKeyStudyDrawer: false
      }
    }
    @Prop({
      type: Object,
      default: () => {
        return new StudentTrainClassDetailVo()
      }
    })
    baseInfoDetail: StudentTrainClassDetailVo

    coursePeriodTableData = new Array<SchemeLearningInfoVo>()

    /**
     * @description 获取期别信息
     * */
    get issueInfo() {
      if (!this.baseInfoDetail?.periodStudy?.periodId) return new IssueConfigDetail()
      return (
        this.baseInfoDetail?.getIssueConfigById(this.baseInfoDetail.periodStudy.periodId) ?? new IssueConfigDetail()
      )
    }

    /**
     * @description 培训期别数据
     * */
    trainingPeriodTableData = new StudentPeriodLog()

    GraduationStatusEnum = GraduationStatusEnum

    // 获取课程总数
    get coursePeriodCount() {
      return this.coursePage.totalSize
    }

    /**
     * @description 判断学习情况是否有tab展示对应内容
     * */
    get showContext() {
      return (tabName: string) => {
        if (this.showTab) {
          // 展示tab，根据激活项判断显示
          return this.activeName == tabName
        } else {
          // 不展示tab 展示有配置的内容
          if (tabName == 'online' && this.isOnline) return true
          if (tabName == 'traingPeriod' && this.isTrainingPeriod) return true
          return false
        }
      }
    }

    @Watch('showContext', { deep: true, immediate: true })
    resetActive(val: boolean) {
      this.fixedActiveStyle()
    }

    @Prop({
      type: String,
      default: 0
    })
    userId: string

    @Prop({
      type: String,
      default: ''
    })
    qualificationId: string

    @Prop({
      type: Boolean,
      default: 0
    })
    hasConfigCourseQuiz: boolean

    @Prop({
      type: String,
      default: ''
    })
    studentNo: string

    // 用于查询课程详情列表的课程id
    courseQuizId = ''

    // 课程测验分页
    courseQuizPage = new Page(1, 10)

    // 查询培训班列表实例
    queryStudentTrainClassObj = new QueryStudentTrainClass()

    // 删除课程实例
    deleteCourseObj = new MutationDeleteCourse()
    deleteCourseParams = this.deleteCourseObj.deleteParams

    // 一键学习实例 【课件】
    qualifiedCoursewareObj = new MutationQualifiedCourseware()
    qualifiedCoursewareParams = this.qualifiedCoursewareObj.qualifiedParams

    // 一键学习实例 【课程】
    qualifiedCourseObj = new MutationQualifiedCourse()
    qualifiedCourseParams = this.qualifiedCourseObj.qualifiedParams

    courseQuizRecordTableData = new Array<CourseQuizRecordDetailVo>()

    // 一键合格 【课程】
    courseParams = {
      courseTestPoint: null as number,
      hasConfigQuiz: false,
      courseId: '',
      studentCourseId: ''
    }

    @Ref('coursePeriodTableRef')
    coursePeriodTableRef: ElTable
    @Ref('attendanceDetailsDrawerRef')
    attendanceDetailsDrawerRef: AttendanceDetailsDrawer

    get courseUnCompletePeriod() {
      return this.baseInfoDetail.course.courseUnCompletePeriod > 0
        ? this.baseInfoDetail.course.courseUnCompletePeriod
        : 0
    }

    get passingScore() {
      return (
        Number(this.baseInfoDetail?.trainClassDetail?.learningTypeModel?.courseLearning?.quizConfigModel?.passScore) ||
        60
      )
    }

    /*
    【课程 和 课程下的课件】  一键学习
    判断是课程还是课件
  */
    oneKeyStudy(item: SchemeLearningInfoVo) {
      //A. 课程的一键学习
      this.qualifiedCourseParams.studentCourseId = item.studentCourseId
      if (item.resourceType === 1) {
        if (!this.hasConfigCourseQuiz) {
          // 1.无课后测验
          this.$confirm('是否确认一键学习？', '一键学习', {
            confirmButtonText: '确认',
            cancelButtonText: '取消'
          }).then(async () => {
            // 确认之后，课程下的所有课件进度100%
            this.qualifiedCourseParams.qualificationId = this.qualificationId
            this.qualifiedCourseParams.learningId = this.baseInfoDetail?.learningId
            this.qualifiedCourseParams.schemeType = this.baseInfoDetail?.basicInfo?.schemeType
            this.qualifiedCourseParams.hasConfigCourseQuiz = this.hasConfigCourseQuiz
            this.qualifiedCourseParams.courseId = item.id
            await this.qualifiedCourseObj.doQualifiedCourse(this.qualifiedCourseParams)
            const res = this.qualifiedCourseObj?.applyResult
            if (!res.data?.applyResult) {
              this.$message.error('一键合格请求失败！')
            } else {
              this.$message.success('操作成功！')
              this.$emit('updateBaseInfoDetail')
            }
          })
        } else {
          // 2.有课后测验，需要填写测验分数
          // 展示一键学习抽屉
          this.courseParams.hasConfigQuiz = this.hasConfigCourseQuiz
          this.courseParams.courseId = item.id
          this.uiStatus.isShow.oneKeyStudyDrawer = true
        }
      }
      // B.课件的一键学习
      if (item.resourceType === 2) {
        this.$confirm('是否确认一键学习？', '一键学习', {
          confirmButtonText: '确认',
          cancelButtonText: '取消'
        }).then(async () => {
          // 确认之后，课程下的所有课件进度100%
          this.qualifiedCoursewareParams.qualificationId = this.qualificationId
          this.qualifiedCoursewareParams.learningId = this.baseInfoDetail?.learningId
          this.qualifiedCoursewareParams.schemeType = this.baseInfoDetail?.basicInfo?.schemeType
          this.qualifiedCoursewareParams.courseId = item.parentId
          this.qualifiedCoursewareParams.coursewareId = item.id
          await this.qualifiedCoursewareObj.doQualifiedCourseware()
          const res = this.qualifiedCoursewareObj.applyResult
          if (!res.data?.applyResult) {
            this.$message.error('一键合格请求失败！')
          } else {
            this.$message.success('操作成功！')
            this.$emit('updateBaseInfoDetail')
          }
        })
      }
    }

    formatTime(val: string) {
      if (!val) return '-'
      const res = this.$moment(new Date(val)).format('YYYY.MM.DD HH:mm:ss')
      return res
    }

    getQuizStatusName(val: number) {
      // 0：未评定 1：未合格 2：合格
      const obj = {
        0: '未评定',
        1: '未合格',
        2: '合格'
      }
      return obj[val] || '-'
    }

    getPercentageProgress(total: number, complete: number, expand = 100) {
      if (total === 0) return 0
      if (total && complete) {
        const res = ((((complete * expand) / total) * expand) / expand).toFixed()
        const resToNumber = Number(res)
        return resToNumber > 100 ? 100 : resToNumber
      } else {
        return 0
      }
    }

    // 查看测验详情 【有配置测验的课程】
    @bind
    @debounce(150)
    async lookExamDetail(item: StudentCourseVo) {
      if (!this.hasConfigCourseQuiz) {
        this.$message.error('该课程还没有配置测验！')
        return
      }
      this.courseQuizId = item.id
      this.courseQuizPage.pageNo = 1
      await this.queryStudentCourseQuizList()
      this.uiStatus.isShow.courseExamDrawer = true
    }

    // 查询课验详情数据
    async queryStudentCourseQuizList() {
      const params = new QueryCourseQuizRecordListVo()
      params.courseId = this.courseQuizId
      params.qualificationId = this.qualificationId
      try {
        this.uiStatus.query.loadCourseExamTest = true
        this.courseQuizRecordTableData = await this.queryStudentTrainClassObj.queryStudentCourseQuizList(
          this.courseQuizPage,
          params
        )
      } catch (e) {
        console.log(e)
        this.$message.error('查询课程测验详情请求失败！')
      } finally {
        this.uiStatus.query.loadCourseExamTest = false
      }
    }

    // 删除课程 判断1.选课规则培训班 2.自主选课培训班
    @bind
    @debounce(150)
    deleteCourse(item: StudentCourseVo) {
      if (this.baseInfoDetail?.basicInfo?.schemeType === TrainClassSchemeEnum.Choose_Course_Learning) {
        /*
          选课规则培训班
          1.判断课程是否为必修课， 提示： 课程为必修课程，无法删除
          2.培训班是否已合格， 合格提示： 培训班已考核通过，无法删除课程
          3.以上都不符合，支持删除操作
        */
        if (item.courseType === 1) {
          //  必修课
          this.$message.error('课程为必修课程，无法删除!')
          return
        }
        if (this.baseInfoDetail?.basicInfo?.assessResult === 1) {
          this.$message.error('培训班已考核通过，无法删除课程!')
          return
        }
        const statusMsg =
          this.baseInfoDetail?.basicInfo?.assessResult === 1
            ? '该学员已合格，是否确认删除课程'
            : '是否删除学员已选课程？删除后此课程将从学员学习列表中清除。'
        this.$confirm(statusMsg, '删除课程', {
          confirmButtonText: '确认',
          cancelButtonText: '取消'
        }).then(async () => {
          this.deleteCourseParams.qualificationId = this.qualificationId
          this.deleteCourseParams.learningId = this.baseInfoDetail?.learningId
          this.deleteCourseParams.courseId = item.id
          this.deleteCourseParams.studentCourseId = item.studentCourseId
          this.deleteCourseParams.schemeType = TrainClassSchemeEnum.Choose_Course_Learning
          const res = await this.deleteCourseObj.doDeleteStudentCourse()
          if (!res?.isSuccess()) {
            this.$message.error('删除课程请求失败！')
          } else {
            this.$message.success('删除成功，请稍后刷新列表!')
          }
        })
      } else if (this.baseInfoDetail?.basicInfo?.schemeType === TrainClassSchemeEnum.Autonomous_Course_Learning) {
        /*
          自主选课培训班
          1.判断课程是否为必修课， 提示： 课程为必修课程，无法删除
          2.以上不符合，支持删除操作
        */
        if (item.courseType === 1) {
          //  必修课
          this.$message.error('课程为必修课程，无法删除!')
          return
        }
        this.$confirm('是否删除学员已学课程？删除后此课程的获得学分清除。', '删除课程', {
          confirmButtonText: '确认',
          cancelButtonText: '取消'
        }).then(async () => {
          this.deleteCourseParams.qualificationId = this.qualificationId
          this.deleteCourseParams.learningId = this.baseInfoDetail?.learningId
          this.deleteCourseParams.courseId = item.id
          this.deleteCourseParams.studentCourseId = item.studentCourseId
          this.deleteCourseParams.schemeType = TrainClassSchemeEnum.Autonomous_Course_Learning
          const res = await this.deleteCourseObj.doDeleteStudentCourse()
          if (!res?.isSuccess()) {
            this.$message.error('删除课程请求失败！')
          } else {
            this.$message.success('删除成功，请稍后刷新列表!')
          }
        })
      }
    }

    /**
     * @description 是否展示tab
     * */
    get showTab() {
      return this.isOnline && this.isTrainingPeriod
    }

    /**
     * @description 解决时序问题
     * @mark 由于外部使用ref调用，在初始化时，ref获取为undefined导致查询报错，补充此钩子，在初始化时手动调用查询方法，后续操作直接触发init，不会触发此钩子
     * */
    created() {
      this.init()
    }

    /**
     * @description 初始化查询
     * */
    async init() {
      try {
        await this.queryCoursePeriodList()
        await this.queryTrainingPeriod()
      } catch (e) {
        console.log(e, '==========err')
      } finally {
        // 是否配置网授面授
        // 网授
        if (this.baseInfoDetail?.scheme?.skuProperty?.trainingWay?.skuPropertyValueId == TrainingModeEnum.online) {
          this.isOnline = true
          this.isTrainingPeriod = false
        }
        // 网面授班
        if (this.baseInfoDetail?.scheme?.skuProperty?.trainingWay?.skuPropertyValueId == TrainingModeEnum.mixed) {
          this.isOnline = true
          this.isTrainingPeriod = true
        }
        // 面授
        if (this.baseInfoDetail?.scheme?.skuProperty?.trainingWay?.skuPropertyValueId == TrainingModeEnum.offline) {
          this.isOnline = false
          this.isTrainingPeriod = true
        }

        this.fixedActiveStyle()
      }
    }

    @bind
    @debounce(250)
    // 查询课程学时列表
    queryCoursePeriodList() {
      if (!this.studentNo) {
        this.coursePeriodTableData = new Array<SchemeLearningInfoVo>()
        return
      }
      try {
        // TODO 批量请求分页口？？？
        this.coursePage.pageNo = 1
        this.uiStatus.query.tableLoading = true
        this.uiStatus.query.loadCoursePeriodPage = false
        console.log('这是课程学时：', this.coursePage, this.studentNo, this.baseInfoDetail.scheme.schemeId)
        this.queryStudentTrainClassObj
          .queryStudentLearningInfoList(
            this.coursePage,
            this.studentNo,
            this.baseInfoDetail.scheme.schemeId,
            this.baseInfoDetail.scheme.schemeType
          )
          .then((res) => {
            this.coursePeriodTableData = res
          })
        this.$nextTick(() => {
          this.coursePeriodTableRef.doLayout()
        })
        if (this.coursePeriodTableData) this.uiStatus.query.tableLoading = false
        console.log('这是课程学时：', this.coursePeriodTableData)
      } catch (error) {
        console.log(error)
        this.uiStatus.query.tableLoading = false
        this.$message.error('获取课程学时列表失败！')
      } finally {
        this.uiStatus.query.loadCoursePeriodPage = false
      }
    }

    @bind
    @debounce(250)
    // 查询培训期别
    async queryTrainingPeriod() {
      this.showData = false
      if (!this.studentNo) {
        this.trainingPeriodTableData = new StudentPeriodLog()
        return
      }
      try {
        this.uiStatus.query.loadTraingPeriod = true
        // TODO
        // this.trainingPeriodTableData = await this.queryStudentTrainClassObj.queryPeriodStudyLog(this.studentNo)
        this.trainingPeriodTableData = await QueryStudentStudy.queryPeriodStudyLog(this.studentNo)
        this.showData = true
      } catch (error) {
        this.$message.error('获取培训期别失败！')
      } finally {
        this.uiStatus.query.loadTraingPeriod = false
      }
    }

    // 前往学习日志
    toStudyLog() {
      const userId = this.userId
      const schemeId = this.baseInfoDetail.scheme.schemeId
      const qualificationId = this.qualificationId
      const schemeType = this.baseInfoDetail.basicInfo.schemeType
      const studentNo = this.studentNo
      this.$router.push({
        path: '/statistic/statistics-report/study-log',
        query: { userId, schemeId, qualificationId, schemeType: String(schemeType), studentNo }
      })
    }

    /* 滚动查询课程学时列表 */
    async visibilityCoursePeriodTableConfig(isVisible: boolean, entry: any) {
      // console.log('滑动加载课程学时列表')
      if (isVisible) {
        if (entry.target.dataset.index >= this.coursePage.totalSize) {
          //   最大值时不请求
          return
        }
        if (parseInt(entry.target.dataset.index) == this.coursePeriodTableData?.length) {
          this.coursePage.pageNo++
          // TODO
          const list = await this.queryStudentTrainClassObj.queryStudentLearningInfoList(
            this.coursePage,
            this.studentNo,
            this.baseInfoDetail.scheme.schemeId,
            this.baseInfoDetail.scheme.schemeType
          )
          this.coursePeriodTableData = this.coursePeriodTableData.concat(list)
          this.$nextTick(() => {
            this.coursePeriodTableRef.doLayout()
          })
        }
      }
    }

    /* 滚动加载课程测验列表 */
    async visibilityCourseQuizRecordConfig(isVisible: boolean, entry: any) {
      if (isVisible) {
        if (entry.target.dataset.index >= this.courseQuizPage.totalSize) {
          //   最大值时不请求
          return
        }
        if (parseInt(entry.target.dataset.index) == this.courseQuizRecordTableData?.length) {
          this.courseQuizPage.pageNo++
          const params = new QueryCourseQuizRecordListVo()
          params.courseId = this.courseQuizId
          params.qualificationId = this.qualificationId
          const list = await this.queryStudentTrainClassObj.queryStudentCourseQuizList(this.courseQuizPage, params)
          this.courseQuizRecordTableData = this.courseQuizRecordTableData.concat(list)
        }
      }
    }

    // 确认一键学习
    async confirmOneKeyStudyDrawer() {
      if (!this.courseParams.courseTestPoint) {
        this.$message.warning('测验分数不能为空！')
        return
      }
      if (this.courseParams.courseTestPoint < this.passingScore || this.courseParams.courseTestPoint > 100) {
        this.$message.warning('请设置合格成绩介于合格分和满分之间!')
        return
      }
      this.qualifiedCourseParams.qualificationId = this.qualificationId
      this.qualifiedCourseParams.learningId = this.baseInfoDetail?.learningId
      this.qualifiedCourseParams.schemeType = this.baseInfoDetail?.basicInfo?.schemeType
      this.qualifiedCourseParams.hasConfigCourseQuiz = this.courseParams.hasConfigQuiz
      this.qualifiedCourseParams.courseId = this.courseParams.courseId
      this.qualifiedCourseParams.quizScore = this.courseParams.courseTestPoint
      await this.qualifiedCourseObj.doQualifiedCourse(this.qualifiedCourseParams)
      const res = this.qualifiedCourseObj?.applyResult
      if (!res.data?.applyResult) {
        this.$message.error('一键合格请求失败！')
      } else {
        this.$message.success('操作成功！')
        this.$emit('updateBaseInfoDetail')
      }
      this.uiStatus.isShow.oneKeyStudyDrawer = false
    }

    // 取消一键学习
    closeOneKeyStudyDrawer() {
      this.courseParams.courseTestPoint = undefined
      this.courseParams.hasConfigQuiz = false
      this.courseParams.courseId = ''
      this.uiStatus.isShow.oneKeyStudyDrawer = false
    }

    // 课程分类
    classifyInfo(classify: string) {
      return classify.split('>').join(' / ')
    }

    /**
     * @description 时间转换
     * */
    formatTimeToHours(time: number) {
      if (!time) return '-'
      const res = this.$moment.utc(time * 1000).format('HH:mm:ss')
      return res
    }

    /**
     * @description 清除数据
     * */
    clearList() {
      this.coursePage.totalSize = 0
      this.coursePeriodTableData = new Array<SchemeLearningInfoVo>()
    }

    /**
     * @description 打开考勤详情
     * */
    showAttendanceDetails() {
      try {
        if (!this.baseInfoDetail.scheme?.schemeId) throw new Error('schemeId is not exist')
        this.attendanceDetailsDrawerRef.open(this.trainingPeriodTableData, this.baseInfoDetail.scheme.schemeId)
      } catch (e) {
        // this.$message.error('打开考勤详情失败！')
        console.log(e, '======e')
      }
    }

    /**
     * @description 修复样式
     * */
    fixedActiveStyle() {
      // 修复激活样式
      this.activeName = 'traingPeriod'
      const resetActiveName = setTimeout(() => {
        this.activeName = 'online'
        clearTimeout(resetActiveName)
      }, 200)
    }
  }
</script>
