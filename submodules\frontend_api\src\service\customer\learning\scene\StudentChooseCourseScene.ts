/**
 * 学员学习课程场景
 */
import ApplyStudentLearningToken from '@api/service/common/token/ApplyStudentLearningToken'
import ApplyChooseCourseToken from '@api/service/common/token/ApplyChooseCourseToken'
import ApplyStudentChooseCourseSceneProof from '@api/service/customer/learning/scene/proofs/ApplyStudentChooseCourseSceneProof'
import ChooseCourseTicket from '@api/service/customer/learning/scene/tickets/ChooseCourseTicket'

class StudentChooseCourseScene {
  private proof: ApplyStudentChooseCourseSceneProof

  constructor(proof: ApplyStudentChooseCourseSceneProof) {
    this.proof = proof
  }

  /**
   * 申请进入场景门票
   * 进入场景预演：
   * 1. 申请学员token
   * 2.【学员 token】申请课程学习 token
   * @return ChooseCourseTicket
   */
  async applyEnterTicket(): Promise<ChooseCourseTicket> {
    const applyStudentLearningToken = new ApplyStudentLearningToken(this.proof.qualificationId, this.proof.learningId)
    await applyStudentLearningToken.applyInterruptAutoStudy()
    const applyCourseLearningToken = new ApplyChooseCourseToken(applyStudentLearningToken)
    await applyCourseLearningToken.apply()
    // 申请失败不进行下一步
    return new ChooseCourseTicket(applyCourseLearningToken.token)
  }
}

export default StudentChooseCourseScene
