import { StudentInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import StudentInfoVo from '@api/service/management/user/query/student/vo/StudentUserInfoVo'
import AccountSourceTypes from '@api/service/diff/management/fjzj/user/student/enums/AccountSourceTypes'

class StudentUserInfoVo extends StudentInfoVo {
  /**
   * 用户来源
   * @param item
   */
  sourceTypeName = ''
  from(item: StudentInfoResponse) {
    super.from(item)
    this.sourceTypeName = AccountSourceTypes.map.get(item.accountInfo?.sourceType)
  }
}

export default StudentUserInfoVo
