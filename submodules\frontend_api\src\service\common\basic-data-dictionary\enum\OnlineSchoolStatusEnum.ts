import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * 网校状态
 */
export enum OnlineSchoolStatusEnum {
  ALL = 'ALL',
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
  EXPIRE = 'EXPIRE',
  OPENING = 'OPENING'
}

class OnlineSchoolStatus extends AbstractEnum<OnlineSchoolStatusEnum> {
  static enum = OnlineSchoolStatusEnum

  constructor(status?: OnlineSchoolStatusEnum) {
    super()
    this.current = status
    this.map.set(OnlineSchoolStatusEnum.ALL, '全部')
    this.map.set(OnlineSchoolStatusEnum.ENABLE, '在服')
    this.map.set(OnlineSchoolStatusEnum.DISABLE, '停用')
    this.map.set(OnlineSchoolStatusEnum.EXPIRE, '到期')
    this.map.set(OnlineSchoolStatusEnum.OPENING, '开通中')
  }
}

export default OnlineSchoolStatus
