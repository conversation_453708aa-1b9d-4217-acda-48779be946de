import msPay, { PreparePayInfoResponse } from '@api/ms-gateway/ms-payment-v1'
// 加载支付界面
export default class QueryPayOrderConfig {
  // 订单号
  private orderNo = ''

  payModel = new PreparePayInfoResponse()

  // order业务对象实例
  // private _orderInstance: CustomerOrderAction = undefined
  constructor(orderNo: string) {
    this.orderNo = orderNo
  }

  /**
   * @description: 支付界面初始化
   * @param {*}
   * @return {*}
   */

  async queryPayConfigInfo() {
    // 加载商品信息
    // const res = await this.queryCommodity()
    // return res
    const res = await msPay.getPreParePayResult(this.orderNo)
    if (res.status.isSuccess()) {
      this.payModel = res.data
    }
    return res.status
  }

  // getMutationPay() {}
}
