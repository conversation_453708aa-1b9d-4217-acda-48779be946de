import { FieldConstraintResponse } from '@api/ms-gateway/ms-servicer-series-v1'
import FieldConstraintVo from './FieldConstraintVo'

class TeacherIndustryRegisterVo {
  /**
   * 学段
   */
  section: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 学时
   */
  subjects: FieldConstraintVo = new FieldConstraintVo()

  from(res: Array<FieldConstraintResponse>) {
    const resMap = new Map()
    res?.forEach(item => {
      resMap.set(item.field, item)
    })
    this.section = FieldConstraintVo.from(resMap.get('section'))
    this.subjects = FieldConstraintVo.from(resMap.get('subjects'))
  }
}
export default TeacherIndustryRegisterVo
