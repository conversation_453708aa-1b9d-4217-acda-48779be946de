/**
 * 学员试听课程场景
 */
import CoursePlayTicket from '@api/service/customer/learning/scene/tickets/CoursePlayTicket'
import ApplyPreviewCourseToken from '@api/service/customer/learning/course/token-provider/ApplyPreviewCourseToken'
import ApplyPreviewCourseSceneProof from '@api/service/customer/learning/scene/proofs/ApplyPreviewCourseSceneProof'

class PreviewCourseSceneGate {
  private proof: ApplyPreviewCourseSceneProof

  constructor(proof: ApplyPreviewCourseSceneProof) {
    this.proof = proof
  }

  /**
   * 申请进入场景门票
   * 进入场景预演：
   * 1. 申请学员token
   * 2.【学员 token】申请课程学习 token
   * 3.【课程学习 token】申请课程播放 token
   */
  async applyEnterTicket(): Promise<CoursePlayTicket> {
    const applyPreviewCourseToken = new ApplyPreviewCourseToken(this.proof.courseId)
    await applyPreviewCourseToken.apply()
    // 申请失败不进行下一步
    return new CoursePlayTicket(applyPreviewCourseToken.token)
  }

  /**
   * 拒绝进入场景
   */
  rejectEnter() {
    // 拒绝原因
  }
}

export default PreviewCourseSceneGate
