import createSalesman from './mutates/createSalesman.graphql'
import disable<PERSON><PERSON><PERSON> from './mutates/disableSales<PERSON>.graphql'
import enableSalesman from './mutates/enableSalesman.graphql'
import updateSalesman from './mutates/updateSalesman.graphql'

export { create<PERSON><PERSON><PERSON>, disable<PERSON><PERSON><PERSON>, enable<PERSON><PERSON><PERSON>, update<PERSON><PERSON><PERSON> }
