import OnOrOffShelvesPlan from '@api/service/common/scheme/model/schemeDto/commodity-sale/on-or-off-shelves-plan/OnOrOffShelvesPlan'

/**
 * @description 商品销售信息
 */
class CommoditySale {
  /**
   * 商品id
   */
  id: string
  /**
   * 商品名称
   */
  saleTitle: string
  /**
   * 价格
   */
  price: number
  /**
   * 分类id
   */
  categoryId: string
  /**
   * 是否关闭学员报名
   */
  closeCustomerPurchase: boolean
  /**
   * 可见销售渠道
   */
  visibleChannelList: number[]
  /**
   * 商品上下架计划
   */
  onOrOffShelvesPlan = new OnOrOffShelvesPlan()
  /**
   * 培训机构id
   */
  payeeId: string
  /**
   * 收款主体，0-平台 1-机构
   */
  payWay: number
  /**
   * 税号
   */
  taxCode: string
}

export default CommoditySale
