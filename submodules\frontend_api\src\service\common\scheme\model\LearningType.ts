import IssueLearningType from '@api/service/common/scheme/model/IssueLearningType'
import QuestionnaireLearningType from '@api/service/common/scheme/model/QuestionnaireLearningType'
import CourseLearningType from '@api/service/common/scheme/model/CourseLearningType'
import ExamLearningType from '@api/service/common/scheme/model/ExamLearningType'
import PracticeLearningType from '@api/service/common/scheme/model/PracticeLearningType'
import InterestCourseLearningType from '@api/service/common/scheme/model/InterestCourseLearningType'
import ExperienceLearningType from '@api/service/common/scheme/model/ExperienceLearningType'
import Scheme from '@api/service/common/scheme/model/schemeDto/Scheme'
import {
  IssueAssessTypeEnum,
  QuestionnaireAssessTypeEnum,
  TeachPlanAssessTypeEnum
} from '@api/service/common/scheme/enum/AssessType'
import { AttendanceAssessRequireTypeEnum } from '@api/service/common/scheme/enum/AttendanceAssessRequireType'
import { BeginTimeTypeEnum } from '@api/service/common/scheme/enum/BeginTimeType'
import { EndTimeTypeEnum } from '@api/service/common/scheme/enum/EndTimeType'
import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
import { QuestionnaireAppliedRangeTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'
import { QuestionnaireOpenDateTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireOpenDateType'
import { QuestionnairePreconditionTypeEnum } from '@api/service/common/scheme/enum/QuestionnairePreconditionType'
import AssessSettingInfo from '@api/service/common/scheme/model/AssessSettingInfo'
import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
import IssueCourseDetail from '@api/service/common/scheme/model/IssueCourseDetail'
import QuestionnaireConfigDetail from '@api/service/common/scheme/model/QuestionnaireConfigDetail'
import IssueConfigure from '@api/service/common/scheme/model/schemeDto/issue-configures/IssueConfigure'
import PlaceholderConstant from '@api/service/common/scheme/model/schemeDto/common/PlaceholderConstant'
import QuestionnaireLearning from '@api/service/common/scheme/model/schemeDto/common/questionnaire-learning/QuestionnaireLearning'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import { IssueTrainingDateTypeEnum } from '@api/service/common/scheme/enum/IssueTrainingDateType'
import TemplateNameManager from '@api/service/management/train-class/mutation/dto/TemplateNameManager'

/**
 * @description 学习方式
 */
class LearningType {
  /**
   * 课程学习方式
   */
  courseLearning = new CourseLearningType()
  /**
   * 考试学习方式
   */
  exam = new ExamLearningType()
  /**
   * 练习学习方式
   */
  practiceLearning = new PracticeLearningType()
  /**
   * 兴趣课学习方式
   */
  interestCourse = new InterestCourseLearningType()
  /**
   * 学习心得学习方式
   */
  learningExperience = new ExperienceLearningType()
  /**
   * 期别学习方式
   */
  issue = new IssueLearningType()
  /**
   * 调研问卷学习方式
   */
  questionnaire = new QuestionnaireLearningType()

  /**
   * 配置期别和问卷
   * @param classConfigJson json对象
   */
  static configIssueAndQuestionnaire(classConfigJson: Scheme): {
    issue: IssueLearningType
    questionnaire: QuestionnaireLearningType
  } {
    const issueConfigures: IssueConfigure[] = classConfigJson.issueConfigures
    // 方案级别问卷 - 放在最外层的questionnaireLearning
    const schemeQuestionnaires: QuestionnaireLearning[] = classConfigJson.questionnaireLearning
    const issue = new IssueLearningType()
    const questionnaire = new QuestionnaireLearningType()
    const someIssueConfigQuestionnaire =
      issueConfigures &&
      issueConfigures.length &&
      issueConfigures.some((issueConfigure) => {
        return issueConfigure.questionnaireLearning.length
      })
    const someSchemeConfigQuestionnaire = schemeQuestionnaires && schemeQuestionnaires.length
    questionnaire.questionnaireConfigList = [] as QuestionnaireConfigDetail[]
    if (someSchemeConfigQuestionnaire || someIssueConfigQuestionnaire) {
      // 问卷
      // 判断是否配置问卷：方案级别问卷不为空Or期别下有配置问卷
      questionnaire.isSelected = true
      questionnaire.isAssess = true
      // 方案级别问卷直接转换
      if (someSchemeConfigQuestionnaire) {
        schemeQuestionnaires.forEach((schemeQuestionnaire) => {
          const questionnaireConfigItem = LearningType.configQuestionnaireItem(schemeQuestionnaire, classConfigJson)
          questionnaire.questionnaireConfigList.push(questionnaireConfigItem)
        })
      }
    }
    if (issueConfigures && issueConfigures.length) {
      // 全期别问卷需要特殊处理，需从每个期别里面捞
      const uniqueQnList: { uniqueKey: string; questionnaire: QuestionnaireConfigDetail }[] = []
      // 期别
      issue.isSelected = true
      issue.isAssess = true
      const { issueAssessItem } = Scheme.parseTrainClassAssess(classConfigJson)
      if (issueAssessItem) {
        issue.assessId = issueAssessItem.id
        issue.assessName = issueAssessItem.name
      }
      issue.issueConfigList = [] as IssueConfigDetail[]
      issueConfigures.forEach((issueConfigure) => {
        const issueDetail = new IssueConfigDetail()
        issueDetail.operationType = OperationTypeEnum.update
        const { assessSetting, teachPlanLearning, questionnaireLearning } = issueConfigure
        // 期别问卷配置
        // 全期别问卷要进行备份
        if (questionnaireLearning && questionnaireLearning.length) {
          questionnaireLearning.forEach((issueConfigQuestionnaire) => {
            const dtoUseRange = issueConfigQuestionnaire.config.useRange
            const questionnaireItem = LearningType.configQuestionnaireItem(
              issueConfigQuestionnaire,
              classConfigJson,
              issueConfigure
            )
            if (dtoUseRange === QuestionnaireAppliedRangeTypeEnum.assign_issue) {
              // 指定期别问卷
              questionnaire.questionnaireConfigList.push(questionnaireItem)
              issueDetail.questionnaireList.push(questionnaireItem)
            }
            if (dtoUseRange === QuestionnaireAppliedRangeTypeEnum.per_issue) {
              // 全期别问卷要进行备份
              const uniqueKey = issueConfigQuestionnaire.extendProperties?.find(
                (el) => el.name === PlaceholderConstant.questionnaireExtendPropertyUniqueKeyName
              )?.value as string
              if (uniqueKey) {
                issueDetail.perQuestionnaireBackupMap.set(uniqueKey, questionnaireItem)
                if (!uniqueQnList.some((el) => el.uniqueKey === uniqueKey)) {
                  uniqueQnList.push({
                    uniqueKey,
                    questionnaire: questionnaireItem
                  })
                }
              }
              issueDetail.questionnaireList.push(questionnaireItem)
            }
          })
        }
        if (assessSetting && assessSetting.length) {
          assessSetting.forEach((assessItem) => {
            if (assessItem.name === PlaceholderConstant.issueConfigureExamAssessSettingName) {
              // 期别-exam考核项
              const opt = new AssessSettingInfo<IssueAssessTypeEnum>()
              opt.type = IssueAssessTypeEnum.exam
              opt.id = assessItem.id
              opt.name = assessItem.name
              issueDetail.assessSettings.push(opt)
            }
            if (assessItem.name === PlaceholderConstant.issueConfigureIssueAssessSettingName) {
              // 期别-期别考核项
              const opt = new AssessSettingInfo<IssueAssessTypeEnum>()
              opt.type = IssueAssessTypeEnum.issue
              opt.id = assessItem.id
              opt.name = assessItem.name
              opt.relateAssessIds = assessItem.relateAssessIds
              issueDetail.assessSettings.push(opt)
              const { learningResults } = assessItem
              if (learningResults && learningResults.length) {
                const issuePeriodCredit = learningResults.find((el) => el.type === 1)
                if (issuePeriodCredit) {
                  issueDetail.periods = issuePeriodCredit.grade
                  issueDetail.periodCreditId = issuePeriodCredit.id
                }
              }
            }
            if (assessItem.name === PlaceholderConstant.issueConfigureIssueAssessSettingTime) {
              const opt = new AssessSettingInfo<IssueAssessTypeEnum>()
              opt.type = IssueAssessTypeEnum.issue_time
              opt.id = assessItem.id
              opt.name = assessItem.name
              issueDetail.assessSettings.push(opt)
            }
          })
        }
        // 教学计划
        if (teachPlanLearning) {
          issueDetail.relateTeachPlanLearning.id = teachPlanLearning.id
          const { config, assessSetting } = teachPlanLearning
          if (config) {
            issueDetail.relateTeachPlanLearning.configId = config.id
            issueDetail.planMode = config.planMode
            const { address, extendProperties, teachingPlanItemsGroups } = config
            if (address) {
              issueDetail.trainingPointId = address.trainingPointId
              issueDetail.trainingPointConfigId = address.id
              issueDetail.trainingPointLng = address.lng
              issueDetail.trainingPointLat = address.lat
              issueDetail.trainingPointNature = address.nature
            }
            if (extendProperties && extendProperties.length) {
              const headTeacherNameItem = extendProperties.find(
                (el) => el.name === PlaceholderConstant.teachingPlanHeadTeacherName
              )
              const headTeacherPhoneItem = extendProperties.find(
                (el) => el.name === PlaceholderConstant.teachingPlanHeadTeacherPhone
              )
              const hotelContactsNameItem = extendProperties.find(
                (el) => el.name === PlaceholderConstant.teachingPlanHotelLiaisonName
              )
              const hotelContactsPhoneItem = extendProperties.find(
                (el) => el.name === PlaceholderConstant.teachingPlanHotelLiaisonPhone
              )
              if (headTeacherNameItem) {
                issueDetail.headTeacherName = headTeacherNameItem.value as string
              }
              if (headTeacherPhoneItem) {
                issueDetail.headTeacherPhone = headTeacherPhoneItem.value as string
              }
              if (hotelContactsNameItem) {
                issueDetail.hotelContactsName = hotelContactsNameItem.value as string
              }
              if (hotelContactsPhoneItem) {
                issueDetail.hotelContactsPhone = hotelContactsPhoneItem.value as string
              }
            }
            if (teachingPlanItemsGroups && teachingPlanItemsGroups.length) {
              teachingPlanItemsGroups.forEach((group) => {
                const { teachingPlanItems } = group
                if (teachingPlanItems && teachingPlanItems.length) {
                  if (!group.groupTypeData) {
                    // 没有组日期，从组下第一门课获取
                    const firstTeachingPlanItem = teachingPlanItems[0]
                    group.groupTypeData = firstTeachingPlanItem.startTime.split(' ')[0]
                  }
                }
              })
              issueDetail.teachingPlanItemsGroupsBackup = teachingPlanItemsGroups
              issueDetail.issueCourseList = [] as IssueCourseDetail[]
              teachingPlanItemsGroups.forEach((teachingPlanItemGroup) => {
                const { teachingPlanItems } = teachingPlanItemGroup
                if (teachingPlanItems && teachingPlanItems.length) {
                  teachingPlanItems.forEach((teachingPlanItem) => {
                    const issueCourseDetail = IssueCourseDetail.configIssueCourseDetailByDto(
                      teachingPlanItemGroup,
                      teachingPlanItem,
                      OperationTypeEnum.update
                    )
                    issueDetail.issueCourseList.push(issueCourseDetail)
                  })
                }
              })
            }
          }
          if (assessSetting && assessSetting.name === PlaceholderConstant.teachPlanLearningAssessSettingName) {
            const assessItem = new AssessSettingInfo<TeachPlanAssessTypeEnum>()
            assessItem.type = TeachPlanAssessTypeEnum.plan
            assessItem.id = assessSetting.id
            assessItem.name = assessSetting.name
            issueDetail.relateTeachPlanLearning.assessSettings.push(assessItem)
          }
        } else {
          // 没有教学计划，需要从期别扩展属性里拿数据
          const extendProperties = issueConfigure.extendProperties
          if (extendProperties && extendProperties.length) {
            issueDetail.headTeacherName = extendProperties.find(
              (el) => el.name === TemplateNameManager.teachingPlanHeadTeacherName
            )?.value as string
            issueDetail.headTeacherPhone = extendProperties.find(
              (el) => el.name === TemplateNameManager.teachingPlanHeadTeacherPhone
            )?.value as string
            issueDetail.hotelContactsName = extendProperties.find(
              (el) => el.name === TemplateNameManager.teachingPlanHotelLiaisonName
            )?.value as string
            issueDetail.hotelContactsPhone = extendProperties.find(
              (el) => el.name === TemplateNameManager.teachingPlanHotelLiaisonPhone
            )?.value as string
            issueDetail.trainingPointId = extendProperties.find(
              (el) => el.name === TemplateNameManager.teachingPlanAddressTrainingPointId
            )?.value as string
          }
        }

        issueDetail.id = issueConfigure.id
        issueDetail.issueNo = issueConfigure.issueNum
        issueDetail.issueName = issueConfigure.issueName
        issueDetail.checkDateRange.startDate = issueConfigure.startReportTime
        issueDetail.checkDateRange.endDate = issueConfigure.endReportTime
        // 如果有期别报名开始时间，就是指定时间
        issueDetail.registerBeginTimeType = issueConfigure.startSignUpTime
          ? BeginTimeTypeEnum.assign
          : BeginTimeTypeEnum.open_now
        issueDetail.registerBeginTime = issueConfigure.startSignUpTime
        issueDetail.registerEndTimeType = !issueConfigure.endSignUpTime
          ? EndTimeTypeEnum.no_end
          : EndTimeTypeEnum.assign
        issueDetail.registerEndTime = issueConfigure.endSignUpTime
        issueDetail.trainingDateRange.startDate = issueConfigure.startTrainingTime
        issueDetail.trainingDateRange.endDate = issueConfigure.endTrainingTime
        issueDetail.registeredNumDisplayType = issueConfigure.signUpNumRevealType
        issueDetail.openRegistrationNum = issueConfigure.allowSignUpNum
        issueDetail.fixedRegisteredNum = issueConfigure.fixedSignUpRevealNum
        issueDetail.isShowInPortal = issueConfigure.portalDisplay
        if (issueDetail.isShowInPortal) {
          // 展示在门户时，按JSON赋值
          issueDetail.visibleChannelList = issueConfigure.portalDisplayScope
            ? issueConfigure.portalDisplayScope
            : ([] as number[])
        } else {
          issueDetail.visibleChannelList = [] as number[]
        }
        issueDetail.isEnableStudentEnroll = issueConfigure.openSignUp
        const commonNotice = classConfigJson.issueNotice
        if (commonNotice) {
          issueDetail.commonNotice = commonNotice
        }
        issueDetail.issueTrainingDateType = issueConfigure.trainingPeriodType as IssueTrainingDateTypeEnum
        issueDetail.notice = issueConfigure.trainingNotice
        // 期别考勤考核要求类型一定是期别自定义
        issueDetail.attendanceAssessRequireType = AttendanceAssessRequireTypeEnum.custom
        const trainingConfigConfigure = issueConfigure.trainingConfigConfigure
        if (trainingConfigConfigure) {
          issueDetail.trainingConfigConfigureId = trainingConfigConfigure.id
          issueDetail.isOpenAttendance = trainingConfigConfigure.openAttendance
          issueDetail.isOpenCheck = trainingConfigConfigure.openReport
          issueDetail.attendanceRate = LearningType.convertDecimalToPercentage(
            trainingConfigConfigure.minAttendanceRate
          )
          issueDetail.isOpenGraduationTest = trainingConfigConfigure.openCompletionTest
          issueDetail.isOpenAccommodationInfoCollect = trainingConfigConfigure.openStayInfoCollect
          issueDetail.accommodationInfoCollectNotice = trainingConfigConfigure.stayNotice
        }
        issue.issueConfigList.push(issueDetail)
      })
      if (uniqueQnList.length) {
        uniqueQnList.forEach((uniqueQn) => {
          const qn = new QuestionnaireConfigDetail()
          Object.assign(qn, uniqueQn.questionnaire)
          // 2025-06-05 由于全期别问卷前置条件受具体期别下有无课表影响，故遍历所有期别问卷，重新计算前置条件类型
          const uniqueQnKey = uniqueQn.uniqueKey
          issue.issueConfigList.forEach((item) => {
            const backUpQn = item.perQuestionnaireBackupMap.get(uniqueQnKey)
            if (backUpQn) {
              if (backUpQn.preconditionType === QuestionnairePreconditionTypeEnum.pass_issue_assess) {
                qn.preconditionType = QuestionnairePreconditionTypeEnum.pass_issue_assess
              }
            }
          })
          // --/--
          questionnaire.questionnaireConfigList.push(qn)
        })
      }
    }
    issue.issueCount = issue.issueConfigList.length
    return {
      issue,
      questionnaire
    }
  }

  /**
   * 配置问卷项
   * @param questionnaire 问卷配置
   * @param classConfigJson json对象
   * @param appliedRangeType 问卷应用范围
   * @param issueConfigure 期别配置
   */
  static configQuestionnaireItem(
    questionnaire: QuestionnaireLearning,
    classConfigJson: any,
    issueConfigure = new IssueConfigure()
  ): QuestionnaireConfigDetail {
    const jsonObj = classConfigJson as Scheme
    const trainingBeginTime = jsonObj.trainingBeginDate
    const trainingEndTime = jsonObj.trainingEndDate
    const configItem = new QuestionnaireConfigDetail()
    configItem.operationType = OperationTypeEnum.update
    configItem.id = questionnaire.id
    configItem.assessSettings = [] as AssessSettingInfo<QuestionnaireAssessTypeEnum>[]
    const { assessSetting, config, precondition } = questionnaire
    if (assessSetting && assessSetting.length) {
      assessSetting.forEach((assessDto) => {
        if (
          assessDto.name === PlaceholderConstant.questionnaireLearningAssessSettingName &&
          assessDto.operation !== OperationTypeEnum.remove
        ) {
          const assessItem = new AssessSettingInfo<QuestionnaireAssessTypeEnum>()
          assessItem.type = QuestionnaireAssessTypeEnum.questionary
          assessItem.id = assessDto.id
          assessItem.name = assessDto.name
          configItem.assessSettings.push(assessItem)
        }
      })
    }
    if (config) {
      configItem.configId = config.id
      configItem.status = config.status
      configItem.templateId = config.paperPublishConfigureId
      configItem.questionnaireName = config.name
      const { allowStartTime, allowEndTime } = config
      const dateIsSameAsSchemeTraining = allowStartTime === trainingBeginTime && allowEndTime === trainingEndTime
      configItem.openDateType = dateIsSameAsSchemeTraining
        ? QuestionnaireOpenDateTypeEnum.same_as_scheme_training_time
        : QuestionnaireOpenDateTypeEnum.assign
      configItem.openDateRange.startDate = allowStartTime
      configItem.openDateRange.endDate = allowEndTime
      configItem.isOpenStatistic = config.openResults
      configItem.appliedRangeType = config.useRange
      configItem.allowCount = config.allowCount
      configItem.isAssessed = config.needAssessment
      configItem.isForced = config.forceQuestionnaire
      configItem.triggerType = config.questionnaireTriggerLink
      if (configItem.appliedRangeType === QuestionnaireAppliedRangeTypeEnum.assign_issue) {
        configItem.curIssueId = issueConfigure.id
        configItem.curIssueNo = issueConfigure.issueNum
        configItem.curIssueName = issueConfigure.issueName
      }
    }
    if (precondition && precondition.operation !== OperationTypeEnum.remove) {
      configItem.preconditionId = precondition.id
      configItem.preconditionName = precondition.name
      if (
        precondition.name === PlaceholderConstant.questionnaireChooseCoursePreconditionName ||
        precondition.name === PlaceholderConstant.questionnaireAutonomousCourseLearningPreconditionName
      ) {
        // 选课规则Or自主选课
        configItem.preconditionType = QuestionnairePreconditionTypeEnum.pass_online_course
      }
      if (precondition.name === PlaceholderConstant.questionnaireIssueLearningPreconditionName) {
        // 期别
        configItem.preconditionType = QuestionnairePreconditionTypeEnum.pass_issue_assess
      }
    }
    const uniqueKey = questionnaire.extendProperties?.find(
      (el) => el.name === PlaceholderConstant.questionnaireExtendPropertyUniqueKeyName
    )?.value as string
    if (uniqueKey) {
      configItem.uniqueKey = uniqueKey
    }

    return configItem
  }

  /**
   * 将小数转换为百分比
   * @param decimal 小数值
   * @returns 返回对应的百分比值
   */
  static convertDecimalToPercentage(decimal: number): number {
    if (decimal > 0) {
      return CalculatorObj.multiply(decimal, 100)
    } else {
      return 0
    }
  }
}

export default LearningType
