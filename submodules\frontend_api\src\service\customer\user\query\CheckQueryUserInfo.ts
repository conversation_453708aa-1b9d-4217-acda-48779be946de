import BasicDataQueryForestage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'

/**
 * 用户信息
 */
class CheckQueryUserInfo {
  /**
   * 循环次数
   */
  private count = 0
  /**
   * 循环上限次数
   */
  private readonly maxCount = 6
  /**
   * 每次循环毫秒数
   */
  private readonly loopMilliseconds = 500
  async checkUserInfo(): Promise<boolean> {
    if (this.count === this.maxCount) {
      return false
    } else {
      const { data } = await BasicDataQueryForestage.getStudentInfoInMyself()

      if (data?.userInfo && data?.userInfo?.userId) {
        // 有数据，返回
        return true
      } else {
        // 无数据，继续循环
        this.count++
        await new Promise((resolve) => setTimeout(resolve, this.loopMilliseconds))
        return await this.checkUserInfo()
      }
    }
  }
}

export default CheckQueryUserInfo
