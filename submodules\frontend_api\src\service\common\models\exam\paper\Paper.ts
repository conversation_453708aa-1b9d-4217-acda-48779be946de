import { ExamConfigType, PaperTimeType } from '@api/service/customer/my-question/question-practice/models/enums'

abstract class Paper {
  /**
   * 试卷id
   */
  id = ''
  /**
   * 试卷名称
   */
  name = ''
  /**
   * 试卷分类id
   */
  paperTypeId = ''
  /**
   * 试卷分类 名称
   */
  paperTypeName = ''
  /**
   * 配置类型，来至ConfigType
   * 1：固定卷 2：AB卷 3：智能卷
   **/
  configType: ExamConfigType = 0
  /**
   * 试卷分数
   */
  totalScore = 0.0
  /**
   * 建议及格分数
   **/
  passScore = 0.0
  /**
   * 试卷总题数
   */
  totalQuestionCount = 0
  /**
   * 计时方式
   * @see PaperTimeType
   */
  timeType: PaperTimeType = 0
  /**
   * 建议考试时长
   */
  timeLength = 0.0
  /**
   * 描述
   */
  description = ''
  /**
   * 是否草稿
   */
  draft = false
  /**
   * 是否启用
   */
  enabled = false
  /**
   * 创建时间
   */
  createTime: Date = new Date()
  /**
   * 创建人
   */
  createUserId = ''
  /**
   * 创建人姓名
   */
  createUserName = ''
  /**
   * 最后修改时间
   */
  lastChangeTime: Date = new Date()
}

export default Paper
