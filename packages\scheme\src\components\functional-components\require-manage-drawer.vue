<template>
  <el-drawer
    title="必选管理"
    :visible.sync="requireManageDialog"
    size="1200px"
    custom-class="m-drawer"
    :wrapper-closable="false"
    :close-on-press-escape="false"
  >
    <div class="drawer-bd">
      <el-alert type="warning" show-icon :closable="false" class="m-alert"> 提示：必选的心得学员需优先完成。 </el-alert>
      <div class="m-tit">
        <span class="tit-txt">待选心得</span>
      </div>
      <el-row :gutter="16" class="m-query f-mt10">
        <el-form :inline="true" label-width="auto">
          <el-col :span="10">
            <el-form-item label="学习心得主题">
              <el-input clearable placeholder="请输入学习心得主题名称" v-model="waitSelectedExperienceValue" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-button type="primary" @click="queryWaitSelectedExperience()">查询</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <!--表格-->
      <el-table
        stripe
        max-height="500px"
        class="m-table f-mt10"
        ref="waitSelectedExperienceRef"
        :data="waitSelectedExperienceList"
        v-loading="loading"
      >
        <el-table-column type="index" label="No." width="60" align="center" prop=""></el-table-column>
        <el-table-column label="主题" min-width="240" prop="theme">
          <template v-slot="{ row }">
            {{ row.theme }}
          </template>
        </el-table-column>
        <el-table-column label="参加时间" min-width="120" align="center" prop="joinTime">
          <template v-slot="{ row }">
            {{ timeChange(row.joinTime) }}
          </template>
        </el-table-column>
        <el-table-column label="心得类型" min-width="120" align="center" prop="answerType">
          <template v-slot="{ row }">
            {{ row.experienceType }}
          </template>
        </el-table-column>
        <el-table-column label="作答形式" min-width="80" align="center" prop="answerType">
          <template v-slot="{ row }">
            {{ row.answerType }}
          </template>
        </el-table-column>
        <el-table-column label="审核方式" width="100" align="center" fixed="right" prop="checkType">
          <template v-slot="{ row }">
            {{ row.checkType }}
          </template>
        </el-table-column>
        <el-table-column label="总分" width="80" align="center" fixed="right" prop="score">
          <template v-slot="{ row }">
            {{ row.score }}
          </template>
        </el-table-column>
        <el-table-column label="提交次数" width="140" align="center" fixed="right" prop="submitCount">
          <template v-slot="{ row }">
            {{ submitNumber(row) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template v-slot="{ row }">
            <el-button type="text" size="mini" @click="selected(row)"
              >{{ row.isRequired ? '取消选择' : '选择' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <hb-pagination :page="waitSelectedExperiencePage" v-bind="waitSelectedExperiencePage"></hb-pagination>
    </div>
    <div class="drawer-bd">
      <div class="m-tit">
        <span class="tit-txt">已选的必选心得</span>
        <span class="f-ml20"
          >共<span class="f-ci">{{ selectedExperienceList.length }}</span
          >个</span
        >
      </div>
      <el-row :gutter="16" class="m-query f-mt10">
        <el-form :inline="true" label-width="auto">
          <el-col :span="10">
            <el-form-item label="学习心得主题">
              <el-input clearable placeholder="请输入学习心得主题名称" v-model="selectedExperienceValue" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-button type="primary" @click="querySelectedExperience()">查询</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <!--表格-->
      <el-table
        stripe
        max-height="500px"
        class="m-table f-mt10"
        ref="selectedExperienceRef"
        :data="selectedExperienceList"
      >
        <el-table-column type="index" label="No." width="60" align="center" prop=""></el-table-column>
        <el-table-column label="主题" min-width="240" prop="theme">
          <template v-slot="{ row }">
            {{ row.theme }}
          </template>
        </el-table-column>
        <el-table-column label="参加时间" min-width="120" align="center" prop="joinTime">
          <template v-slot="{ row }">
            {{ timeChange(row.joinTime) }}
          </template>
        </el-table-column>
        <el-table-column label="心得类型" min-width="120" align="center" prop="answerType">
          <template v-slot="{ row }">
            {{ row.experienceType }}
          </template>
        </el-table-column>
        <el-table-column label="作答形式" min-width="80" align="center" prop="answerType">
          <template v-slot="{ row }">
            {{ row.answerType }}
          </template>
        </el-table-column>
        <el-table-column label="审核方式" width="100" align="center" fixed="right" prop="checkType">
          <template v-slot="{ row }">
            {{ row.checkType }}
          </template>
        </el-table-column>
        <el-table-column label="总分" width="80" align="center" fixed="right" prop="score">
          <template v-slot="{ row }">
            {{ row.score }}
          </template>
        </el-table-column>
        <el-table-column label="提交次数" width="140" align="center" fixed="right" prop="submitCount">
          <template v-slot="{ row }">
            {{ submitNumber(row) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template v-slot="{ row }">
            <el-button type="text" size="mini" @click="selected(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="selectedExperiencePage" v-bind="selectedExperiencePage"></hb-pagination>
    </div>
    <div class="m-btn-bar drawer-ft">
      <el-button @click="requireManageDialog = false">取消</el-button>
      <el-button @click="saveRequireManage()" type="primary">确定</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import ExperienceItem from '@api/service/management/train-class/mutation/vo/ExperienceItem'
  import { UiPage } from '@hbfe/common'
  import LearningExperience from '@api/service/management/train-class/mutation/vo/LearningExperience'
  import { bind, debounce } from 'lodash-decorators'

  @Component
  export default class extends Vue {
    constructor() {
      super()
      this.waitSelectedExperiencePage = new UiPage(this.waitSelectedPageData, this.waitSelectedPageData)
      this.selectedExperiencePage = new UiPage(this.selectedPageData, this.selectedPageData)
    }

    /**
     * 学习心得配置 - 双向绑定
     */
    @Prop({
      type: LearningExperience,
      default: () => {
        return new LearningExperience()
      }
    })
    learningFeelInfo!: LearningExperience
    requireManageDialog = false

    //待选心得查询文本
    waitSelectedExperienceValue = ''
    //已选心得查询文本
    selectedExperienceValue = ''
    // 添加课程添加loading动画
    loading = false

    /**
     * 待选心得列表
     */
    waitSelectedExperienceList = new Array<ExperienceItem>()
    waitSelectedExperiencePage: UiPage
    /**
     * 已选心得列表
     */
    selectedExperienceList = new Array<ExperienceItem>()
    selectedExperiencePage: UiPage

    /**
     * 待选列表分页
     */
    async waitSelectedPageData() {
      this.waitSelectedExperienceList = this.learningFeelInfo.waitSelectedPageData(
        this.waitSelectedExperiencePage,
        this.waitSelectedExperienceValue
      )
      ;(this.$refs['waitSelectedExperienceRef'] as any)?.doLayout()
    }

    /**
     * 已选列表分页
     */
    async selectedPageData() {
      this.selectedExperienceList = this.learningFeelInfo.selectedPageData(
        this.selectedExperiencePage,
        this.selectedExperienceValue
      )
      ;(this.$refs['selectedExperienceRef'] as any)?.doLayout()
    }

    /**
     * 查询待选心得主题
     */
    queryWaitSelectedExperience() {
      this.waitSelectedExperiencePage.pageNo = 1

      this.waitSelectedPageData()
    }

    /**
     * 判断提交次数
     */
    submitNumber(item: ExperienceItem) {
      if (item.checkType.current == 1) {
        return '--'
      } else {
        if (item.submitCountType) {
          return item.submitCount
        } else {
          return '不限次'
        }
      }
    }

    /**
     * 查询已选心得主题
     */
    querySelectedExperience() {
      this.selectedExperiencePage.pageNo = 1
      this.selectedPageData()
    }

    /**
     * 确定保存必选心得
     */
    @bind
    @debounce(200)
    saveRequireManage() {
      this.learningFeelInfo.saveRequired()
      this.requireManageDialog = false
    }

    showRequireManageDialog() {
      this.learningFeelInfo.selectRequireInit()
      this.waitSelectedPageData()
      this.selectedPageData()
      this.requireManageDialog = true
    }

    selected(item: ExperienceItem) {
      item.selectExperience()
      this.selectedPageData()
    }

    get timeChange() {
      return (item: string[]) => {
        if (!item.length) {
          return '-'
        } else if (item[0] == '1900-01-01 00:00:00' && item[1] == '2100-01-01 00:00:00') {
          return '长期有效'
        }
        return item[0] + '至' + item[1]
      }
    }
  }
</script>
