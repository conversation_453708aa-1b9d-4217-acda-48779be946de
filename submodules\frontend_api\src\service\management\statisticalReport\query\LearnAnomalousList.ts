import LearnAnomalousParams from '@api/service/management/statisticalReport/query/vo/LearnAnomalousParams'
import { Page } from '@hbfe/common'
import LearnAnomalousItem from '@api/service/management/statisticalReport/query/vo/LearnAnomalousItem'
import StudentCourseLearning from '@api/platform-gateway/student-course-learning-query-back-gateway'
import { SchemeSkuPropertyResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import {
  ComplexSkuPropertyResponse,
  SchemeSkuInfo,
  SkuPropertyConvertUtils
} from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
export default class LearnAnomalousList {
  /**
   * 查询参数
   */
  queryParams: LearnAnomalousParams = new LearnAnomalousParams()
  /**
   * 查询列表
   */
  list: Array<LearnAnomalousItem> = []
  /**
   * 查询
   */
  async queryList(page: Page) {
    const params = LearnAnomalousParams.to(this.queryParams)
    this.list = []
    const res = await StudentCourseLearning.pageLearningResultErrorInServicer({
      page,
      request: params
    })
    if (res.status.isSuccess()) {
      page.totalSize = res.data.totalSize
      page.totalPageSize = res.data.totalPageSize
      if (res.data.currentPageData.length) {
        const schemeSkuInfos: SchemeSkuInfo[] = []
        this.list = res.data.currentPageData.map((item, index) => {
          // 解决报错
          schemeSkuInfos.push(new SchemeSkuInfo(index.toString(), item.skuProperty as ComplexSkuPropertyResponse))
          return LearnAnomalousItem.from(item)
        })
        const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(schemeSkuInfos)
        this.list.forEach((item, index) => {
          const skuInfo = skuInfos?.find((el) => el.id === index.toString())
          if (skuInfo) item.skuProperty = skuInfo.skuName
        })
      }
    } else {
      page.totalSize = 0
      page.totalPageSize = 0
    }
    return res.status
  }

  /**
   * 统计
   */
  async queryStatistics() {
    const res = await StudentCourseLearning.getLearningResultErrorStatisticsInServicer()
    return {
      supplementStudyErrorCount: res.data?.supplementStudyErrorCount ?? 0,
      autoLearningErrorCount: res.data?.autoLearningErrorCount ?? 0
    }
  }
  /**
   * 查询-专题管理员
   */
  async queryListTrainingChannel(page: Page) {
    const params = LearnAnomalousParams.to(this.queryParams)
    this.list = []
    const res = await StudentCourseLearning.pageLearningResultErrorInTrainingChannel({
      page,
      request: params
    })
    if (res.status.isSuccess()) {
      page.totalSize = res.data.totalSize
      page.totalPageSize = res.data.totalPageSize
      if (res.data.currentPageData.length) {
        const schemeSkuInfos: SchemeSkuInfo[] = []
        this.list = res.data.currentPageData.map((item, index) => {
          // 解决报错
          schemeSkuInfos.push(new SchemeSkuInfo(index.toString(), item.skuProperty as ComplexSkuPropertyResponse))
          return LearnAnomalousItem.from(item)
        })
        const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(schemeSkuInfos)
        this.list.forEach((item, index) => {
          const skuInfo = skuInfos?.find((el) => el.id === index.toString())
          if (skuInfo) item.skuProperty = skuInfo.skuName
        })
      }
    } else {
      page.totalSize = 0
      page.totalPageSize = 0
    }
    return res.status
  }

  /**
   * 统计-专题管理员
   */
  async queryStatisticsTrainingChannel() {
    const res = await StudentCourseLearning.getLearningResultErrorStatisticsInTrainingChannel()
    return {
      supplementStudyErrorCount: res.data?.supplementStudyErrorCount ?? 0,
      autoLearningErrorCount: res.data?.autoLearningErrorCount ?? 0
    }
  }
}
