/*
 * @Description: 发票枚举
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-01 08:38:19
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-06 17:23:18
 */
/**
 * 发票抬头类型  PERSONAL：个人  UNIT：企业
 */
export enum TitleTypeEnum {
  PERSONAL = 1,
  UNIT = 2
}
/**
 * 发票状态 NOTPTOOPEN:未开具 OPENING：开票中 OPEMSUCCESS：开票成功 OPENERROR：开票失败
 */
export enum InvoiceStatusEnum {
  NOTPTOOPEN = 0,
  OPENING = 1,
  OPEMSUCCESS = 2,
  OPENERROR = 3
}
/**
 * 发票种类 PLAININVOICE:普通发票 VATPLAININVOICE:增值税普通发票 VATSPECIALPLAININVOICE:增值税专用发票
 */
export enum InvoiceCategoryEnum {
  PLAININVOICE = 1,
  VATPLAININVOICE = 2,
  VATSPECIALPLAININVOICE = 3
}
/**
 * 发票类型 ONLINE:线上 OFFLINE:线下
 */
export enum InvoiceTypeEnum {
  ONLINE = 1,
  OFFLINE = 2
}
/**
 * 开票方式
 */
export enum InvoiceMethodEnum {
  /**
   * 电子
   */
  ELECT = 1,
  /**
   * 纸质
   */
  PAPER = 1
}
/**
 * 订单退货状态
 *  0:未退货 1：退货中 2：退货成功
 */
export enum OrderReturnStatusEnum {
  DIDNOTRETURN = 0,
  RETURNSING = 1,
  RETURNSUCCESS = 2
}

/**
 * 发票种类（UI用）
 */
export enum InvoiceIdentificationEnum {
  /**
   * 普通发票
   */
  PLAIN_INVOICE = 1,
  /**
   * 增值税普通发票
   */
  VAT_PLAIN_INVOICE = 2,
  /**
   * 增值税专用发票（纸质）
   */
  VAT_SPECIAL_PLAIN_INVOICE = 3,
  /**
   * 增值税专用发票（电子）
   */
  VAT_SPECIAL_ELECT_PLAIN_INVOICE = 4
}
