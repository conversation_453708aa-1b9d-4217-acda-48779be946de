import LearningRuleItem from './LearningRuleItem'
import { Page } from '@hbfe/common'
import StudyRulesSetting, {
  OnlineSchoolSupplementStudyServiceResponse
} from '@api/platform-gateway/platform-supplement-study-rules-setting-v1'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import QueryManager from '@api/service/management/user/query/manager/QueryManager'
export default class LearningRuleList {
  /**
   * 学习规则列表
   */
  learningRuleList: Array<LearningRuleItem> = []
  /**
   * 查询列表
   */
  async queryLearningRuleList(page: Page) {
    this.learningRuleList = []
    const res = await StudyRulesSetting.pageSupplementStudyRuleSettingInServicer(page)
    await QueryIndustry.getIndustryDICT()
    this.learningRuleList = res.data?.currentPageData?.map(item => LearningRuleItem.from(item)) || []
    this.learningRuleList.forEach(item => {
      item.basicInfo.industryName =
        QueryIndustry.industryDICTList.find(i => i.id === item.basicInfo.industryId)?.name || ''
    })
    page.totalSize = res.data?.totalSize || 0
    page.totalPageSize = res.data?.totalPageSize || 0
    return res.status
  }

  /**
   * 查询操作人
   */
  async queryOperationUser() {
    const createUserIds: string[] = this.learningRuleList.map(item => item.createUserId)
    if (createUserIds.length) {
      const queryManager = new QueryManager()
      const res = await queryManager.batchQueryUserInfo([...new Set(createUserIds)])
      this.learningRuleList.forEach(item => {
        item.operator = res.get(item.createUserId)?.userName || ''
      })
    }
  }

  /**
   * 根据网校ID查询学习规则
   */
  async queryRuleConfigByServicerId(servicerId: string) {
    const res = await StudyRulesSetting.getSupplementStudyRuleSettingByServiceId(servicerId)
    if (res.status.isSuccess()) {
      return res.data
    } else {
      return new OnlineSchoolSupplementStudyServiceResponse()
    }
  }
}
