/*
 * @Description: 更新发票请求参数
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-01 14:31:28
 * @LastEditors: <PERSON> <PERSON>
 * @LastEditTime: 2022-04-30 07:48:57
 */

import { UpdateElectronicInvoiceRequest } from '@api/ms-gateway/ms-bill-v1'
import { TitleTypeEnum } from '../../enum/InvoiceEnum'
import { InvoiceDto, UpdateBatchOrderInvoiceRequest } from '@api/ms-gateway/ms-order-v1'

export default class InvoiceDetail {
  /**
   * 发票编号
   */
  invoiceId?: string
  /**
   * 批次号（批次订单使用）
   */
  batchOrderNo?: string = undefined
  /**
   * 发票抬头，null表示不更新
   */
  title?: string
  /**
     * 发票抬头类型，null表示不更新
  <pre>
  发票抬头类型  PERSONAL：个人  UNIT：企业
  </pre>
     */
  titleType?: TitleTypeEnum
  /**
   * 购买方纳税人识别号，null表示不更新
   */
  taxpayerNo?: string
  /**
   * 购买方地址，null表示不更新
   */
  address?: string
  /**
   * 购买方电话号码，null表示不更新
   */
  rePhone?: string
  /**
   * 购买方开户行名称，null表示不更新
   */
  bankName?: string
  /**
   * 购买方银行账户，null表示不更新
   */
  account?: string
  /**
   * 购买方电子邮箱，null表示不更新
   */
  email?: string
  /**
   * 发票票面备注，null表示不更新
   */
  remark?: string
  /**
   * 用户电话号码
   */
  phone?: string
  static to(invoiceDetail: InvoiceDetail) {
    const updateElectronicInvoiceRequest = new UpdateElectronicInvoiceRequest()
    updateElectronicInvoiceRequest.invoiceId = invoiceDetail.invoiceId
    updateElectronicInvoiceRequest.title = invoiceDetail.title
    updateElectronicInvoiceRequest.titleType = invoiceDetail.titleType
    updateElectronicInvoiceRequest.taxpayerNo = invoiceDetail.taxpayerNo
    updateElectronicInvoiceRequest.address = invoiceDetail.address
    updateElectronicInvoiceRequest.bankName = invoiceDetail.bankName
    updateElectronicInvoiceRequest.phone = invoiceDetail.rePhone
    updateElectronicInvoiceRequest.account = invoiceDetail.account
    updateElectronicInvoiceRequest.email = invoiceDetail.email
    updateElectronicInvoiceRequest.remark = invoiceDetail.remark
    return updateElectronicInvoiceRequest
  }

  static toAutoBatch(invoiceDetail: InvoiceDetail) {
    const req = new UpdateBatchOrderInvoiceRequest()
    req.invoiceId = invoiceDetail.invoiceId
    req.batchOrderNo = invoiceDetail.batchOrderNo
    req.invoiceInfo = new InvoiceDto()
    // 目前这里只为了线上发票自动开票服务 故将发票类型写死 若后续要拓展则需要将参数开出
    req.invoiceInfo.invoiceMethod = 1
    req.invoiceInfo.invoiceType = 1
    req.invoiceInfo.invoiceCategory = 2

    req.invoiceInfo.title = invoiceDetail.title
    req.invoiceInfo.titleType = invoiceDetail.titleType
    req.invoiceInfo.taxpayerNo = invoiceDetail.taxpayerNo
    req.invoiceInfo.address = invoiceDetail.address
    req.invoiceInfo.bankName = invoiceDetail.bankName
    req.invoiceInfo.phone = invoiceDetail.rePhone
    req.invoiceInfo.account = invoiceDetail.account
    req.invoiceInfo.contactEmail = invoiceDetail.email
    req.invoiceInfo.remark = invoiceDetail.remark

    return req
  }
}
