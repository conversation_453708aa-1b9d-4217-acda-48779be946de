<template>
  <el-main>
    <template v-if="$hasPermission('questionAnswer')" desc="查看问卷详情" actions="created">
      <div class="m-questionnaire" ref="previewRef">
        <div class="content">
          <div class="header">{{ questionnaire.questionnaireName }}</div>
          <div class="questionnaire-bd">
            <!--单选题-->
            <div class="question" v-for="(item, index) in questionnaire.questions" :key="index">
              <div class="tit" style="display: flex; align-items: center" :id="`${index}`">
                <span class="num">{{ index + 1 }}、</span>

                <div class="f-flex-sub" style="display: flex; align-items: center">
                  <span class="type">{{ QuestionType.map.get(item.type) }}</span>
                  <span class="text" v-if="item.type === QuestionTypeEnum.single">
                    <component :is="richValue(item.optionQuestion.described)"></component>
                  </span>
                  <span class="text" v-if="item.type === QuestionTypeEnum.multiple">
                    <component :is="richValue(item.multipleOptionQuestion.described)"></component>
                  </span>
                  <template v-if="item.type == QuestionTypeEnum.single || item.type == QuestionTypeEnum.multiple">
                    <span v-if="item.isTeacherEvaluate">{{
                      item.onlineOrOffline == '1' ? '（线上课程）' : '（线下学习）'
                    }}</span>
                  </template>

                  <span class="text" v-if="item.type === QuestionTypeEnum.answer">
                    <component :is="richValue(item.answerQuestion.described)"></component>
                  </span>
                  <span class="text" v-if="item.type === QuestionTypeEnum.gauge">
                    <component :is="richValue(item.gaugeQuestion.described)"></component>
                  </span>
                </div>
                <div class="tag" v-if="item.isMustAnswered"><i class="icon el-icon-warning-outline"></i>本题必选</div>
              </div>
              <div class="option" v-if="item.optionQuestion.options && item.type === QuestionTypeEnum.single">
                <el-radio-group>
                  <el-col :span="24" v-for="(itm, idx) in item.optionQuestion.options" :key="idx">
                    <el-radio-group v-model="item.optionQuestion.selectedContent" disabled>
                      <el-radio :label="itm.id" style="display: flex; align-items: center">
                        <div style="display: flex">
                          <span class="f-cb f-fb f-mr5">{{ resolverIndexToCharCode(idx) }}.</span>
                          <component :is="richValue(itm.content)"></component>
                          <div v-if="itm.completion">
                            ({{ itm.isRequire ? '必填' : '选填' }})
                            <span class="u-underline">{{ itm.completionContent || '请输入内容' }}</span>
                          </div>
                        </div>
                      </el-radio>
                    </el-radio-group>
                  </el-col>
                </el-radio-group>
              </div>

              <div class="option" v-if="item.multipleOptionQuestion.options && item.type === QuestionTypeEnum.multiple">
                <el-radio-group>
                  <el-col :span="24" v-for="(itm, idx) in item.multipleOptionQuestion.options" :key="idx">
                    <el-checkbox-group v-model="item.multipleOptionQuestion.selectedContentArr" disabled>
                      <el-checkbox :label="itm.id" style="display: flex; align-items: center">
                        <div style="display: flex">
                          <span class="f-cb f-fb f-mr5">{{ resolverIndexToCharCode(idx) }}.</span>
                          <component :is="richValue(itm.content)"></component>
                          <div v-if="itm.completion">
                            ({{ itm.isRequire ? '必填' : '选填' }})
                            <span class="u-underline">{{ itm.completionContent || '请输入内容' }}</span>
                          </div>
                        </div>
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-col>
                </el-radio-group>
              </div>
              <div class="option" v-if="item.optionQuestion && item.type === QuestionTypeEnum.gauge">
                <el-row :gutter="20">
                  <el-col :span="24" class="f-mt10" style="display: flex; align-items: center">
                    <span class="f-mr30" v-if="item.gaugeQuestion.type !== GaugeTypeEnum.customer">
                      非常不{{ GaugeQuestionTypeName(item.gaugeQuestion.type) }}
                    </span>
                    <span class="f-mr30" v-else>{{ item.gaugeQuestion.minDeepTip }}</span>
                    <el-radio
                      v-for="value in GaugeQuestionRow(item.gaugeQuestion).array"
                      :key="value"
                      v-model="item.gaugeQuestion.selectedContent"
                      :label="value"
                      class="f-mr20"
                      style="padding-top: 0px"
                      disabled
                      >{{ value }}
                    </el-radio>
                    <span v-if="item.gaugeQuestion.type !== GaugeTypeEnum.customer">
                      非常{{ GaugeQuestionTypeName(item.gaugeQuestion.type) }}
                    </span>
                    <span v-else>{{ item.gaugeQuestion.maxDeepTip }}</span>
                  </el-col>
                </el-row>
              </div>
              <div class="option" v-if="item.answerQuestion.described && item.type === QuestionTypeEnum.answer">
                <el-input type="textarea" :rows="8" v-model="item.answerQuestion.selectedContent" disabled></el-input>
              </div>
            </div>

            <!-- <div class="f-tc f-mt25">
            <el-button type="primary" round class="submit">提交问卷</el-button>
          </div> -->
          </div>
        </div>
        <div class="question-card" :class="isShow ? 'is-show' : ''" @click.stop="isShow = true">
          <div class="cont">
            <!--打开按钮-->
            <div class="btn"></div>
            <div class="tips">
              <div class="item"><span class="type"></span>未答</div>
              <div class="item"><span class="type is-answered"></span>已答</div>
              <div class="item"><span class="type is-choose"></span>选答</div>
              <div class="item"><span class="type is-must"></span>必答</div>
              <div class="item">
                <el-button type="primary" size="mini" v-if="isShow" @click.stop="isShow = false">收起答题卡</el-button>
              </div>
            </div>
            <div class="nums">
              <span
                class="num"
                v-for="(item, index) in questionnaire.questions"
                :key="index"
                @click="scrollToAnchor(index)"
                :class="{
                  'is-answered': item.isAnswered,
                  'is-choose': !item.isMustAnswered,
                  'is-must': item.isMustAnswered
                }"
                >{{ index + 1 }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </template>
  </el-main>
</template>
<script lang="ts">
  import Question from '@api/service/common/question-naire/Question'
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import QuestionType, { QuestionTypeEnum } from '@api/service/common/enums/question-naire/QuestionType'
  import GaugeType, { GaugeTypeEnum } from '@api/service/common/question-naire/enums/GaugeType'
  import GaugeQuestion from '@api/service/common/question-naire/GaugeQuestion'
  import Questionnaire from '@api/service/management/resource/question-naire/Questionnaire'
  import RootModule from '@/store/RootModule'
  import { ExamUtil } from '@/store/module/exam/common/ExamUtil'

  @Component
  export default class extends Vue {
    @Ref('previewRef') previewRef: any

    questionList: Question[] = new Array<Question>()
    questionnaire = new Questionnaire()
    QuestionType = new QuestionType()
    GaugeType = new GaugeType()

    QuestionTypeEnum = QuestionTypeEnum
    GaugeTypeEnum = GaugeTypeEnum

    isShow = false
    id = ''
    issueId = ''

    //////////////////////////////////////////////////////////////////////////////////
    get richValue() {
      return (row: string) => {
        return {
          template: '<div>' + this.testFwbImg(row) + '</div>'
        }
      }
    }

    get GaugeQuestionTypeName() {
      return (row: GaugeTypeEnum) => {
        return this.GaugeType.map.get(row).replace(/度/g, '')
      }
    }

    get GaugeQuestionRow() {
      return (row: GaugeQuestion) => {
        return {
          minRow: row.startLevel,
          maxRow: row.startLevel + row.levelNum - 1,
          array: Array.from({ length: row.levelNum }, (_, index) => row.startLevel + index)
        }
      }
    }

    //////////////////////////////////////////////////////////////////////////////////
    async created() {
      if (this.$route?.query?.id) {
        this.id = this.$route?.query?.id as string
        this.issueId = this.$route?.query?.issueId as string
        this.questionnaire.answerPaperId = this.id
        await this.questionnaire.queryAnsweredQuestions(this.issueId)
        console.log('煞笔煞笔煞笔', this.questionnaire.questions)
      }
    }

    async mounted() {
      RootModule.changeMenuStatus(true)
    }

    testFwbImg(str: string) {
      return str?.replace(/<img.*?>/g, function (img) {
        const regex = /<img[^>]+src="([^">]+)"/
        const match = img.match(regex)
        return `
              <el-popover
              placement="top-start"
              width="200"
              trigger="hover">
                <img src="${match[1]}" style="width: 200px; height: 200px" />
                <span slot="reference" style="color:#1f86f0">[查看图片]</span>
            </el-popover>
           `.trim()
      })
    }

    resolverIndexToCharCode(index: number) {
      return ExamUtil.matchCharCode(index)
    }

    scrollToAnchor(index: number) {
      const anchors = document.querySelectorAll('.tit')
      const target = anchors[index]
      if (target) {
        target.scrollIntoView(true)
      }
    }
  }
</script>
