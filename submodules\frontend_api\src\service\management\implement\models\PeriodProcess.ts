import PeriodImplementBase from '@api/service/management/implement/models/PeriodImplementBase'
import AttendanceQRCodeManage from '@api/service/management/implement/AttendanceQRCodeManage'
import {
  IssueStudyConfigResponse,
  SchemeIssueRegistrationResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

export default class PeriodProcess extends PeriodImplementBase {
  /*
   * 已报名人数
   */
  signedNumber: number = undefined

  /**
   * 报道是否已设置过
   */
  reportSetted: boolean = undefined

  /**
   * 报道开始时间
   */
  reportStartTime: string = undefined

  /**
   * 报道结束时间
   */
  reportEndTime: string = undefined

  /**
   * 考勤是否已设置过
   */
  attendanceSetted: boolean = undefined

  /**
   * 学习资料是否已设置过
   */
  annexSetted: boolean = undefined

  /**
   * 考勤二维码
   */
  qrCodeManage: AttendanceQRCodeManage = new AttendanceQRCodeManage(this.id)

  /**
   * @param dto 过程信息dto
   * @param configMap 期别实施配置map
   */
  static from(dto: SchemeIssueRegistrationResponse, configMap: Map<string, IssueStudyConfigResponse>) {
    const vo = new PeriodProcess()
    const periodDto = dto?.issueConfig
    if (periodDto) {
      vo.id = periodDto.issueId
      vo.no = periodDto.issueNum
      vo.schemeId = periodDto.schemeId
      vo.name = periodDto.issueName
      vo.applicationData.begin = periodDto.startSignUpTime
      vo.applicationData.end = periodDto.endSignUpTime
      vo.applicantsNumber = periodDto.allowSignUpNum
      vo.trainingTime.begin = periodDto.startTrainTime
      vo.trainingTime.end = periodDto.endTrainTime
      vo.signedNumber = dto.signUpNum || 0
      vo.qrCodeManage = new AttendanceQRCodeManage(vo.id)
      vo.reportStartTime = periodDto.startReportTimePeriod
      vo.reportEndTime = periodDto.endReportTimePeriod

      const config = configMap.get(vo.id)
      if (config) {
        vo.isSetAttendanceConfig = config.trainingConfigResponse?.openAttendance
        vo.isSetReportConfig = config.trainingConfigResponse?.openReport
        vo.hasQuestionnaire = config.trainingConfigResponse?.openQuestionnaireAssessment
        if (config.attendanceConfigResponse) {
          vo.attendanceSetted = true
        } else {
          vo.attendanceSetted = false
        }
        if (config.reportRuleResponse) {
          vo.reportSetted = true
        } else {
          vo.reportSetted = false
        }
        if (config.teachResourceConfigResponse) {
          vo.annexSetted = true
        } else {
          vo.annexSetted = false
        }
      }
    }

    return vo
  }
}
