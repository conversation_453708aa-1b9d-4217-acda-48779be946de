import msTradeConfigurationV1 from '@api/ms-gateway/ms-trade-configuration-v1'
import { ResponseStatus, UiPage } from '@hbfe/common'
import UpdatePurchaseChannelVo from './vo/UpdatePurchaseChannelVo'
import msTradeQueryFrontGatewayTradeQueryBackstage, {
  ReceiveAccountConfigRequest,
  ReceiveAccountConfigResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import MutationPreparePurchaseChannel from './MutationPreparePurchaseChannel'
import ReceiveAccountVo from './vo/ReceiveAccountVo'
import OnlineSchoolConfigModule from '../../online-school-config/OnlineSchoolConfigModule'
import { TerminalTypeEnum } from '@api/service/common/enums/trade-configuration/TerminalType'
import { PurchaseChannelEnum } from '@api/service/common/enums/trade-configuration/PurchaseChannelType'
class MutationUpdatePurchaseChannel {
  private purchaseChannelId = ''
  private purchaseChannelType = 0
  updatePurchaseChannelParam = new UpdatePurchaseChannelVo()

  constructor(id?: string, purchaseChannelType?: number) {
    console.log(id, 'id')
    this.purchaseChannelId = id
    this.purchaseChannelType = purchaseChannelType
    this.updatePurchaseChannelParam = new UpdatePurchaseChannelVo(this.purchaseChannelType)
  }

  /**
   * 获取购买渠道的创建/更新类
   * @returns
   */
  async getupdatePurchaseChannelParam(): Promise<UpdatePurchaseChannelVo> {
    if (!this.purchaseChannelId) {
      this.updatePurchaseChannelParam.channelType = this.purchaseChannelType
      return this.updatePurchaseChannelParam
    }
    // const msRes = await msTradeQueryFrontGatewayTradeQueryBackstage.getPurchaseChannel(this.purchaseChannelId)
    // if (msRes.status.isSuccess()) {
    const currentPurchaseChannel = MutationPreparePurchaseChannel.purchaseChannelList.find(
      (purchaseChannel) => purchaseChannel.type === this.purchaseChannelType
    )
    console.log(currentPurchaseChannel, 'currentPurchaseChannel')
    currentPurchaseChannel.terminalList = currentPurchaseChannel.terminalList.sort((a, b) => {
      return a.terminalCode == 'Web' ? -1 : 1
    })
    const receiveAccountIds = new Array<string>()
    if (!currentPurchaseChannel) return
    currentPurchaseChannel.terminalList?.forEach((terminal) => {
      if (terminal?.receiveAccountIdList) {
        receiveAccountIds.push(...terminal.receiveAccountIdList)
      }
    })
    this.updatePurchaseChannelParam = UpdatePurchaseChannelVo.from(currentPurchaseChannel)
    const receiveAccountMap = await this.getReceiveAccountMap(receiveAccountIds)

    currentPurchaseChannel.terminalList?.forEach((terminal, index) => {
      if (terminal?.receiveAccountIdList?.length) {
        terminal.receiveAccountIdList?.forEach((id) => {
          const receiveAccount = receiveAccountMap.get(id)
          if (receiveAccount) {
            this.updatePurchaseChannelParam.terminalList[index].receiveAccountList.push(
              ReceiveAccountVo.form(receiveAccountMap.get(id))
            )
          }
        })
      }
    })
    // }
    return this.updatePurchaseChannelParam
  }

  /**
   * 根据收款账户id获取详情
   * @param receiveAccountIdList
   * @returns
   */
  private async getReceiveAccountMap(receiveAccountIdList: Array<string>) {
    if (!receiveAccountIdList.length) return
    const receiveAccountIds = new Set(receiveAccountIdList)
    const remoteList = await msTradeQueryFrontGatewayTradeQueryBackstage.listReceiveAccountInServicer(
      Array.from(receiveAccountIds)
    )
    const resultMap = new Map<string, ReceiveAccountConfigResponse>()
    remoteList.data?.forEach((receiveAccount: ReceiveAccountConfigResponse) => {
      resultMap.set(receiveAccount.id, receiveAccount)
    })
    return resultMap
  }

  /**
   * 请求收款账号列表
   * @param page
   */
  async queryPageReceiveAccountForOnline(page: UiPage, terminalCode: string): Promise<Array<ReceiveAccountVo>> {
    const request = new ReceiveAccountConfigRequest()
    request.status = 1
    if (this.purchaseChannelType === PurchaseChannelEnum.STUDENT_PURCHASE) {
      // 学员缴费的收款账号 只选择线上
      request.accountType = 1
    }
    const msRes = await msTradeQueryFrontGatewayTradeQueryBackstage.pageReceiveAccountInServicer({
      page: page,
      request: request
    })
    if (msRes.status.isSuccess()) {
      page.totalSize = msRes.data.totalSize
      page.totalPageSize = msRes.data.totalPageSize
      const accounts = this.updatePurchaseChannelParam.terminalList?.filter(
        (terminal) => terminal.terminalCode === terminalCode
      )[0]?.receiveAccountList
      const resultList = msRes.data.currentPageData?.map(ReceiveAccountVo.form) || new Array<ReceiveAccountVo>()
      resultList?.forEach((receiveAccount) => {
        const index = accounts?.findIndex((account) => {
          return account.id === receiveAccount.id
        })
        receiveAccount.isSelected = index > -1
        receiveAccount.disableSelect = index > -1
      })
      return resultList
    }

    return new Array<ReceiveAccountVo>()
  }

  /**
   * 请求收款账号列表（分销商）
   * @param page
   */
  async queryPageFxReceiveAccountForOnline(page: UiPage, terminalCode: string): Promise<Array<ReceiveAccountVo>> {
    const request = new ReceiveAccountConfigRequest()
    request.status = 1
    if (this.purchaseChannelType === PurchaseChannelEnum.STUDENT_PURCHASE) {
      // 学员缴费的收款账号 只选择线上
      request.accountType = 1
    }
    const msRes = await msTradeQueryFrontGatewayTradeQueryBackstage.pageReceiveAccountInDistribution({
      page: page,
      request: request
    })
    if (msRes.status.isSuccess()) {
      page.totalSize = msRes.data.totalSize
      page.totalPageSize = msRes.data.totalPageSize
      const accounts = this.updatePurchaseChannelParam.terminalList?.filter(
        (terminal) => terminal.terminalCode === terminalCode
      )[0]?.receiveAccountList
      const resultList = msRes.data.currentPageData?.map(ReceiveAccountVo.form) || new Array<ReceiveAccountVo>()
      resultList?.forEach((receiveAccount) => {
        const index = accounts?.findIndex((account) => {
          return account.id === receiveAccount.id
        })
        receiveAccount.isSelected = index > -1
        receiveAccount.disableSelect = index > -1
      })
      return resultList
    }

    return new Array<ReceiveAccountVo>()
  }

  /**
   * 选择收款账号
   * @param terminalCode web/h5
   * @param receiveAccount 收款账户详情
   */
  chooseReceiveAccount(terminalCode: string, receiveAccount: ReceiveAccountVo) {
    const currentTerminal = this.updatePurchaseChannelParam.terminalList.find((terminal) => {
      return terminal.terminalCode === terminalCode
    })
    // 页面传值时已改变状态
    receiveAccount.isSelected ? currentTerminal.add(receiveAccount) : currentTerminal.remove(receiveAccount.id)
  }

  /**
   * 清除已选收款账号
   * @param terminalCode
   * @param receiveAccount
   */
  clearReceiveAccount(terminalCode: string) {
    const currentTerminal = this.updatePurchaseChannelParam.terminalList.find((terminal) => {
      return terminal.terminalCode === terminalCode
    })
    // 关闭抽屉弹窗时 将已选的收款账号清除
    const newReceiveAccountList = JSON.parse(
      JSON.stringify(currentTerminal.receiveAccountList)
    ) as Array<ReceiveAccountVo>
    newReceiveAccountList.forEach((receiveAccount) => {
      if (!receiveAccount.disableSelect && receiveAccount.isSelected) {
        currentTerminal.remove(receiveAccount.id)
      }
    })
  }

  /**
   * 移除收款账号
   * @param terminalCode web/h5
   * @param receiveAccount 收款账户详情
   */
  removeReceiveAccount(terminalCode: string, receiveAccount: ReceiveAccountVo) {
    const currentTerminal = this.updatePurchaseChannelParam.terminalList.find((terminal) => {
      return terminal.terminalCode === terminalCode
    })
    currentTerminal.remove(receiveAccount.id)
  }

  /**
   * 创建购买渠道
   * @returns
   */
  private async doCreate(isChannel?: boolean): Promise<ResponseStatus> {
    const createPurchaseChannelParam = isChannel
      ? UpdatePurchaseChannelVo.toChannelCreateDto(this.updatePurchaseChannelParam)
      : UpdatePurchaseChannelVo.toInvoiceCreateDto(this.updatePurchaseChannelParam)
    const msRes = await msTradeConfigurationV1.createPurchaseChannel(createPurchaseChannelParam)
    // 将创建成功后返回的购买渠道id赋值
    // MutationPreparePurchaseChannel.purchaseChannelList.find(
    //   channel => channel.type === PurchaseChannelType[this.updatePurchaseChannelParam.channelType]
    // ).id = msRes?.data
    return msRes.status
  }

  /**
   * 更新购买渠道
   * @returns
   */
  private async doUpdate(isChannel?: boolean): Promise<ResponseStatus> {
    const updatePurchaseChannelParam = isChannel
      ? UpdatePurchaseChannelVo.toChannelUpdateDto(this.updatePurchaseChannelParam)
      : UpdatePurchaseChannelVo.toInvoiceUpdateDto(this.updatePurchaseChannelParam)
    const msRes = await msTradeConfigurationV1.updatePurchaseChannel(updatePurchaseChannelParam)
    return msRes.status
  }

  /**
   * 保存渠道配置
   * @returns
   */
  async doReceiveAccountSave(): Promise<ResponseStatus> {
    if (!this.purchaseChannelId) {
      return this.doCreate(true)
    } else {
      return this.doUpdate(true)
    }
  }

  /**
   * 保存发票配置
   * @returns
   */
  async doInvoiceConfigSave(): Promise<ResponseStatus> {
    if (!this.purchaseChannelId) {
      return this.doCreate()
    } else {
      return this.doUpdate()
    }
  }

  /**
   * 是否完成自动开票配置
   * @returns
   */
  async hasAutomInvoice() {
    const res =
      await OnlineSchoolConfigModule.queryFunctionalitySettingFactory.queryElectronicInvoiceConfig.queryElectronicInvoiceExisted()
    return res.data
  }

  /**
   * 销售渠道配置中的各种终端的收款账号列表展示
   * @param terminalCode
   * @returns
   */
  receiveAccountListByTerminalCode(terminalCode: TerminalTypeEnum) {
    const currentTerminal = this.updatePurchaseChannelParam.terminalList.find(
      (terminal) => terminal.terminalCode === terminalCode
    )
    return (
      currentTerminal?.receiveAccountList.filter((receiveAccount) => receiveAccount.disableSelect) ||
      new Array<ReceiveAccountVo>()
    )
  }
}
export default MutationUpdatePurchaseChannel
