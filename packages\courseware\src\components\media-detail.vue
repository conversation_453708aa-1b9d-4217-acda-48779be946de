<template>
  <div class="f-p30">
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :md="20" :lg="16" :xl="13">
        <div class="m-as-form">
          <div class="item">
            <div class="tit">媒体源：</div>
            <div class="con">{{ isOffChanin }}</div>
            <el-button type="text" class="f-fl" v-if="this.mediaType == 2" @click="preview" style="margin-left: 20px"
              >预览</el-button
            >
          </div>
        </div>
        <!--华为云-->
        <el-form
          :model="formData"
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          class="m-form"
          v-show="mediaType == 1"
        >
          <el-form-item label="媒体文件：">
            <upload-file
              @setFileItem="setFileItem"
              @fileFuncall="fileFuncall"
              :file-type.sync="fileType"
              v-if="!isDetail"
            ></upload-file>
            <span>
              <div class="m-file-upload f-mt15" v-for="(item, index) in fileList[0].courseWares" :key="index">
                <!--文档-->
                <i class="icon el-icon-document" v-if="fileType === 1"></i>
                <!--视频-->
                <i class="icon el-icon-film" v-else-if="fileType === 2"></i>
                <span class="name">{{ item.name }}</span>
                <el-progress :text-inside="true" :stroke-width="18" :percentage="item.percen || 100"></el-progress>
                <span class="time">
                  <template v-if="item.timeLength != -1">{{ formatDuring(item.timeLength) }}</template>
                </span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  placement="top"
                  popper-class="m-tooltip"
                  v-if="allDetail.status.current != CoursewareStatusEnum.AVAILABLE"
                >
                  <el-button type="text" style="color: #C0C4CC;cursor:not-allowed">预览</el-button>
                  <div slot="content">课件转换状态为转换中/转换失败，暂不支持预览</div>
                </el-tooltip>
                <el-button type="text" @click="preview" v-else>预览</el-button>
              </div>
            </span>
          </el-form-item>
        </el-form>
        <!--外链-->
        <el-form
          :model="outsideChainForm"
          :rules="outsideChainRules"
          ref="outsideChain"
          label-width="120px"
          class="m-form"
          v-if="mediaType == 2"
        >
          <el-form-item label="标清地址：" v-if="outsideChainForm.standardAddress">
            {{ outsideChainForm.standardAddress }}
          </el-form-item>
          <el-form-item label="高清地址：" v-if="outsideChainForm.highAddress">
            {{ outsideChainForm.highAddress }}
          </el-form-item>
          <el-form-item label="超清地址：" v-if="outsideChainForm.superAddress">
            {{ outsideChainForm.superAddress }}
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-dialog title="删除确认" :visible.sync="sureDeleteVisible" width="450px" class="m-dialog">
      <div>确认删除课件文件</div>
      <div slot="footer">
        <el-button @click="sureDeleteVisible = false">取 消</el-button>
        <el-button type="primary" @click="deleteFile">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import UploadFile from '@hbfe/jxjy-admin-courseware/src/components/upload-file.vue'
  import CourseWareCreate from '@api/service/common/models/course/course-ware/CourseWareCreate'
  import CourseChapterCreate from '@api/service/common/models/course/create/CourseChapterCreate'
  import previewCourseware from '@hbfe/jxjy-admin-components/src/previewer/Previewer'
  import CoursewareType, { CoursewareTypeEnum } from '@api/service/management/resource/courseware/enum/CoursewareType'
  import CoursewareDetail from '@api/service/management/resource/courseware/query/vo/CoursewareDetail'
  import { MediaSourceTypeEnum } from '@api/service/management/resource/courseware/enum/MediaSourceType'
  import { CoursewareStatusEnum } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
  class CourseChapterPercen extends CourseWareCreate {
    percen = 0
    uid = ''
    mediaType = 1
    standardAddress = ''
    highAddress = ''
    superAddress = ''
  }

  @Component({
    components: { UploadFile }
  })
  export default class extends Vue {
    @Ref('ruleForm') ruleForm: any
    @Ref('outsideChain') outsideChain: any
    @Ref('aa') aa: any

    @Prop({
      type: Array,
      default: () => {
        return new Array<any>()
      }
    })
    value: Array<any>

    // @Prop({
    //   type: Number,
    //   default: 1
    // })
    // fileType: number
    // 文档:1 视频:2 压缩包:3
    fileType = 2
    @Prop({
      type: String,
      default: ''
    })
    name: string

    // 1：文件类型 2：视频格式
    @Prop({
      type: CoursewareType,
      default: () => new CoursewareType()
    })
    detailType: CoursewareType

    @Prop({
      type: Number,
      default: 1
    })
    mediaType: number

    //详情数据
    @Prop({
      type: CoursewareDetail,
      default: {}
    })
    allDetail: CoursewareDetail
    timeLength = -1
    // 标清
    standardAddress = ''
    // 高清
    highAddress = ''
    // 超清
    superAddress = ''
    index = 0
    isCreate = false
    isDetail = false

    sureDeleteVisible = false

    CoursewareTypeEnum = CoursewareTypeEnum
    CoursewareStatusEnum = CoursewareStatusEnum
    formData = {
      hour: 0,
      minute: 0,
      second: 0
    }

    outsideChainForm = {
      standardAddress: '',
      highAddress: '',
      superAddress: ''
    }

    rules = {
      hour: [{ required: true, message: '请选择时间' }],
      second: [{ required: true, message: '请选择时间' }],
      minute: [{ required: true, message: '请选择时间' }]
    }
    outsideChainRules = {
      standardAddress: [{ validator: this.validatorComment, trigger: 'blur' }],
      highAddress: [{ validator: this.validatorComment, trigger: 'blur' }],
      superAddress: [{ validator: this.validatorComment, trigger: 'blur' }]
    }

    // 自定义校验
    validatorComment(rule: any, value: string, callback: any) {
      if (value) {
        const m3u8Regex = /\.m3u8$/
        const isMatch = m3u8Regex.test(value)
        if (isMatch) {
          callback()
        } else {
          callback(new Error('播放链接需提供.m3u8地址'))
        }
      } else {
        callback()
      }
    }

    // m3u8正则

    get fileList() {
      console.log(this.value, 'this.value')
      return this.value
    }

    set fileList(val: Array<any>) {
      this.$emit('input', val)
    }

    @Watch('name')
    changeFileName() {
      const curFileList = [
        {
          courseWares: [
            {
              abouts: '',
              coursewareResourcePath: '',
              cwyId: '',
              name: this.name + `${this.deatilTypeAll}`,
              percen: this.allDetailData('percen'),
              resourceMD5: '',
              timeLength: this.allDetailData('timeLength'),
              uid: 111111111,
              usable: true,
              mediaType: this.allDetailData('mediaType'),
              standardAddress: this.allDetailData('standardAddress'),
              highAddress: this.allDetailData('highAddress'),
              superAddress: this.allDetailData('superAddress')
            }
          ]
        }
      ]
      this.fileList = curFileList
    }
    allDetailData(target: string) {
      let callBack: any = ''
      switch (target) {
        case 'standardAddress':
          callBack = this.allDetail?.standardAddress == undefined ? '' : this.allDetail?.standardAddress
          break
        case 'highAddress':
          callBack = this.allDetail?.highAddress == undefined ? '' : this.allDetail?.highAddress
          break
        case 'superAddress':
          callBack = this.allDetail?.superAddress == undefined ? '' : this.allDetail?.superAddress
          break
        case 'percen':
          callBack = this.isCreate ? 0 : 100
          break
        case 'mediaType':
          callBack = this.allDetail.isOuter ? MediaSourceTypeEnum.outer : MediaSourceTypeEnum.huawei
          break
        case 'timeLength':
          callBack = this.allDetail.timeLength ? this.allDetail.timeLength : -1
          break
      }
      if (this.allDetail?.isOuter) {
        this.outsideChainForm.standardAddress = this.allDetail?.standardAddress
        this.outsideChainForm.highAddress = this.allDetail?.highAddress
        this.outsideChainForm.superAddress = this.allDetail?.superAddress
      }
      this.mediaType = this.allDetail?.isOuter ? MediaSourceTypeEnum.outer : MediaSourceTypeEnum.huawei
      this.fileType = this.allDetail?.type.current == CoursewareTypeEnum.document ? 1 : 2
      if (this.allDetail.timeLength > 0) {
        this.formData.hour = Math.floor(this.allDetail.timeLength / 3600)
        this.formData.minute = Math.floor((this.allDetail.timeLength % 3600) / 60)
        this.formData.second = Math.floor((this.allDetail.timeLength % 3600) % 60)
      }
      return callBack
    }

    created() {
      if (this.$route.path.indexOf('detail') != -1) {
        this.isDetail = true
      } else {
        this.isDetail = false
      }
      // 如果是外链 给对应清晰度赋值
      if (this.allDetail.isOuter) {
        this.outsideChainForm.standardAddress = this.allDetail?.standardAddress
        this.outsideChainForm.highAddress = this.allDetail?.highAddress
        this.outsideChainForm.superAddress = this.allDetail?.superAddress
      }
    }

    // 设置上传的内容
    setFileItem(file: any) {
      const obj = new CourseChapterPercen()
      obj.name = file.name
      obj.uid = file.uid
      obj.percen = 0
      obj.timeLength = this.timeLength

      if (this.fileList[this.index]?.courseWares) {
        //this.fileList[this.index].courseWares.push(obj)
        const oldFileList = Object.assign([], this.fileList)
        oldFileList[this.index].courseWares[0] = obj
        this.fileList = oldFileList
      } else {
        const arr = new CourseChapterCreate()
        arr.sort = this.fileList.length
        arr.courseWares = new Array<CourseChapterPercen>()
        this.fileList.push(arr)
        this.fileList[this.index].courseWares.push(obj)
      }
      // 也可设置为-1 到时候页面上用v-if进行判断
      this.timeLength = -1 //设置完毕时间后重置时间长度
    }

    fileFuncall(uid: string, callBack: any) {
      this.fileList.forEach(item => {
        item.courseWares.forEach((subItem: CourseChapterPercen, subIndex: number) => {
          if (subItem.uid === uid) {
            callBack(subItem, subIndex, item)
          }
        })
      })
    }

    // 格式化时间
    formatDuring(value: number) {
      let secondTime = parseInt(String(value)) // 秒
      let minuteTime = 0 // 分
      let hourTime = 0 // 小时
      if (secondTime > 60) {
        //如果秒数大于60，将秒数转换成整数
        //获取分钟，除以60取整数，得到整数分钟
        minuteTime = Math.floor(secondTime / 60)
        //获取秒数，秒数取佘，得到整数秒数
        secondTime = Math.floor(secondTime % 60)
        //如果分钟大于60，将分钟转换成小时
        if (minuteTime > 60) {
          //获取小时，获取分钟除以60，得到整数小时
          hourTime = Math.floor(minuteTime / 60)
          //获取小时后取佘的分，获取分钟除以60取佘的分
          minuteTime = Math.floor(minuteTime % 60)
        }
      }
      return (
        hourTime.toString().padStart(2, '0') +
        ':' +
        minuteTime.toString().padStart(2, '0') +
        ':' +
        secondTime.toString().padStart(2, '0')
      )
    }

    deleteFile() {
      this.fileList[this.index].courseWares = new Array<CourseChapterPercen>()
      this.fileType = 0
      this.sureDeleteVisible = false
    }

    preview() {
      previewCourseware.previewCourseware(this.$route.params.id, this.detailType)
    }

    validateMedia() {
      if (this.fileType === 1 && this.mediaType == 1) {
        this.ruleForm.validate((valid: any) => {
          const time: any =
            Number(this.formData.hour) * 60 * 60 + Number(this.formData.minute) * 60 + Number(this.formData.second)
          if (valid && time > 0) {
            this.$emit('update:time', time)
          } else {
            this.$message({
              message: '文档需要设置时长',
              type: 'warning'
            })
            this.$emit('update:time', 0)
            return false
          }
        })
      } else {
        this.$emit('update:time', -1)
        return true
      }
    }

    get isOffChanin() {
      return this.mediaType == 1 ? '华为云' : '外链课件'
    }

    get deatilTypeAll() {
      return this.detailType.current == CoursewareTypeEnum.document ? '.pdf' : '.m3u8'
    }
  }
</script>
