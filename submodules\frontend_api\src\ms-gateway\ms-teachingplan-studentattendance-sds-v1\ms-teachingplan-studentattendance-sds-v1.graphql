"""独立部署的微服务,K8S服务名:ms-teachingplan-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	clockIn(request:StudentClockInRequest):ClockResponse
	clockOut(request:StudentClockOutRequest):ClockResponse
	"""一键打卡: 便捷测试使用"""
	oneClickClock(request:OneClickClockRequest):Map @optionalLogin
}
"""<AUTHOR>
	@since 2025-06-06
"""
input OneClickClockRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.OneClickClockRequest") {
	studentLearningToken:String!
	planItemGroupIds:[String]!
	planItemIds:[String]!
	"""纬度"""
	lat:Double!
	"""经度"""
	lng:Double!
}
"""签到
	<AUTHOR>
"""
input StudentClockInRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.StudentClockInRequest") {
	studentLearningToken:String!
	planId:String!
	planItemGroupId:String!
	planItemId:String!
	"""纬度"""
	lat:Double!
	"""经度"""
	lng:Double!
}
"""签退
	<AUTHOR>
"""
input StudentClockOutRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.StudentClockOutRequest") {
	studentLearningToken:String!
	planId:String!
	planItemGroupId:String!
	planItemId:String!
	"""纬度"""
	lat:Double!
	"""经度"""
	lng:Double!
}
"""打卡结果
	<AUTHOR>
"""
type ClockResponse @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.response.ClockResponse") {
	"""状态码
		@see com.fjhb.domain.teachingplan.api.sign.consts.attendance.PlanAttendanceResultCodes
		成功 SUCCESS = 200;
		重复考勤 REPEATED = 300;
		迟到 CLOCK_IN_LATE = 301;
		早退 CLOCK_OUT_EARLY = 302;
		不在地点范围 OUT_OF_LOCATION_RANGE = 400;
		系统异常 FAIL = 500;
	"""
	code:Int!
	"""消息"""
	message:String
}

scalar List
