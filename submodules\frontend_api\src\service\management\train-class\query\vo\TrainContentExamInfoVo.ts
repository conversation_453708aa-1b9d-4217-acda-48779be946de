import DateScope from '@api/service/common/models/DateScope'

/**
 * @description 学员培训方案-班级考试
 */
class TrainContentExamInfoVo {
  /**
   * 考试名称
   */
  examName = ''

  /**
   * 考试总分
   */
  examTotalScore = 0

  /**
   * 考试合格成绩
   */
  examQualifiedScore = 0

  /**
   * 考试时间（周期）
   */
  examTime: DateScope = new DateScope()

  /**
   * 考试时长
   */
  examTimeLength = 0

  /**
   * 考试剩余次数 -1：无限次
   */
  examUnCompleteCount = 0

  /**
   * 考试最高分
   */
  examHighestScore = 0
}

export default TrainContentExamInfoVo
