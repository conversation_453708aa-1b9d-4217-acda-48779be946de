import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum DistributionPricingMethodsEnum {
  // 授权定价
  licensing_pricing,
  // 优惠申请
  concession_application
}

export default class DistributionPricingMethodsEnumsClass extends AbstractEnum<DistributionPricingMethodsEnum> {
  // enum: DistributionPricingMethodsEnum = null
  constructor(status?: DistributionPricingMethodsEnum) {
    super()
    this.map.set(DistributionPricingMethodsEnum.licensing_pricing, '授权定价')
    this.map.set(DistributionPricingMethodsEnum.concession_application, '优惠申请')
    this.current = status
  }
}
