import { OrderStatusEnum } from '@api/service/diff/customer/anhui/train-class/enums/OrderStatus'

export default class SignUpNumberInfoItem {
  /**
   * 报名序号，培训平台报名成功保存用于后续使用
   */
  signUpNumber = ''
  /**
   * 培训班名称
   */
  trainingClassName = ''
  /**
   * 培训形式
   1：网授-培训班
   */
  trainingType: number = null
  /**
   * 报名年度
   */
  trainingYear: number = null
  /**
   * 科目类型
   0：公需课
   */
  subjectType: number = null
  /**
   * 科目名称
   */
  subject: string
  /**
   * 公需学时，班级科目类型为专业，该值为空
   */
  publicPeriod: number = null
  /**
   * 培训开始时间
   */
  trainingStartTime = ''
  /**
   * 培训结束时间
   */
  trainingEndTime = ''
  /**
   * 对应订单状态
   * 0 未支付
   * 1 支付中
   * 2 已支付
   */
  orderStatus: OrderStatusEnum = null
}
