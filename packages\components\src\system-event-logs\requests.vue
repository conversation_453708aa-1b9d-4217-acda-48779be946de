<template>
  <div>
    <el-table :data="errors">
      <el-table-column label="No." type="index" width="80" align="center"></el-table-column>
      <el-table-column label="标题" header-align="center" prop="title"></el-table-column>
      <el-table-column label="路由路径" header-align="center">
        <template slot-scope="{ row }">【{{ row.router.meta.title }}】{{ row.router.fullPath }}</template>
      </el-table-column>
      <el-table-column label="内容" header-align="center" prop="content">
        <template slot-scope="scope">
          <hb-copy :content="scope.row.content"></hb-copy>
          {{ scope.row.content }}
        </template>
      </el-table-column>
      <el-table-column label="类型" header-align="center" align="center" prop="type" width="200">
        <template slot-scope="{ row }">
          <el-tag type="warning" effect="dark">{{ row.type | systemLogType }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="耗时" header-align="center" align="center" width="150">
        <template slot-scope="{ row }">
          <span>{{ row.spentTime }}</span>
          s
        </template>
      </el-table-column>
      <!--      <el-table-column label="操作" width="100">-->
      <!--        <el-button type="text">删除</el-button>-->
      <!--      </el-table-column>-->
    </el-table>
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import DevToolsModule from '@/store/devtools/DevToolsModule'
  import {
    systemLogTransformer,
    SystemLogType
  } from '@hbfe/jxjy-admin-components/src/system-event-logs/models/SystemLog'

  @Component({
    filters: {
      systemLogType(systemLogType: SystemLogType) {
        return systemLogTransformer(systemLogType)
      }
    }
  })
  export default class extends Vue {
    get errors() {
      return DevToolsModule.systemLogs
    }
  }
</script>
<style lang="scss" scoped>
  /deep/.cell {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
</style>
