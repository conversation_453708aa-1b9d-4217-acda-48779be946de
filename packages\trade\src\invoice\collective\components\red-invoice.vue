<template>
  <el-card shadow="never" class="m-card f-mb15">
    <!--条件查询-->
    <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
    <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
    <hb-search-wrapper :model="pageQueryParam" @reset="resetCondition">
      <el-form-item label="冲红发票号">
        <el-input v-model="pageQueryParam.redBillNo" clearable placeholder="请输入冲红发票号" />
      </el-form-item>
      <el-form-item label="冲红状态">
        <el-select
          v-model="pageQueryParam.redInvoiceItemBillStatusList"
          clearable
          filterable
          placeholder="请选择冲红状态"
        >
          <el-option
            v-for="item in redInvoiceStatus"
            :label="item.name"
            :value="item.value"
            :key="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="购买人姓名">
        <el-input v-model="pageQueryParam.userName" clearable placeholder="请输入购买人姓名" />
      </el-form-item>
      <el-form-item label="集体报名批次号">
        <el-input v-model="pageQueryParam.orderNoList" clearable placeholder="请输入集体报名批次号" />
      </el-form-item>
      <el-form-item label="开票时间">
        <double-date-picker
          :begin-create-time.sync="pageQueryParam.redBillStatusChangeTime.success.begin"
          :end-create-time.sync="pageQueryParam.redBillStatusChangeTime.success.end"
          begin-time-placeholder="红票开具时间"
          end-time-placeholder="红票开具时间"
        ></double-date-picker>
      </el-form-item>

      <!-- <el-form-item label="专题">
        <el-select
          v-model="pageQueryParam.isFromSpecialSubject"
          clearable
          filterable
          placeholder="请选择订单是否来源专题"
        >
          <el-option :value="true" label="是"></el-option>
          <el-option :value="false" label="否"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="专题名称">
        <el-input v-model="pageQueryParam.specialSubjectName" clearable placeholder="请输入专题进行查询" />
      </el-form-item>
      <el-form-item label="培训方案">
        <biz-learning-scheme-select v-model="hasSelectSchemeMode"></biz-learning-scheme-select>
      </el-form-item>
      <el-form-item label="期别名称" v-if="showPeriodName">
        <biz-period-select :scheme-id="hasSelectSchemeMode[0].id" v-model="pageQueryParam.periodId" />
      </el-form-item>
      <template slot="actions">
        <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
        <el-button @click="exportSpecialInvoice">导出数据</el-button>
      </template>
    </hb-search-wrapper>
    <div class="f-mt20">
      <el-alert type="warning" :closable="false" class="m-alert">
        <div class="f-c6">
          统计： 冲红发票总金额
          <span class="f-fb f-co">¥ {{ totalAmount }}</span>
          ，冲红税额总金额
          <span class="f-fb f-co">¥ {{ totalTax }}</span>
          ，冲红发票仅统计普通电子发票退款或重新开票产生。
        </div>
      </el-alert>
    </div>
    <!--表格-->
    <el-table stripe :data="pageData" class="m-table f-mt10" v-loading="query.loading" ref="redInvoiceTable">
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="集体报名批次号" min-width="220" fixed="left" prop="orderNo">
        <template slot-scope="scope"
          >{{ scope.row.orderNo || '-' }}
          <p><el-tag type="success" size="mini" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag></p>
        </template>
      </el-table-column>
      <el-table-column label="蓝票发票号" min-width="220" ixed="left" prop="blueInvoiceNo">
        <template slot-scope="scope">{{ scope.row.blueInvoiceNo || '-' }}</template>
      </el-table-column>
      <el-table-column label="红票发票号" min-width="220" ixed="left" prop="redInvoiceNo">
        <template slot-scope="scope"> {{ scope.row.redInvoiceNo || '-' }}</template>
      </el-table-column>
      <el-table-column label="购买人信息" min-width="240">
        <template slot-scope="scope">
          <p>姓名：{{ scope.row.name || '-' }}</p>
          <p>手机号：{{ scope.row.phone || '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="发票内容" min-width="400">
        <template slot-scope="scope">
          <p class="f-flex">
            <span>发票抬头：</span>
            <span class="f-flex-sub"
              >【{{ invoiceTitleMapType[scope.row.titleType] }}】{{ scope.row.title || '-' }}</span
            >
          </p>
          <p>统一社会信用代码：{{ scope.row.taxpayerNo || '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="冲红金额(元)" width="140" align="right" prop="redTotalAmount">
        <template slot-scope="scope">
          {{ scope.row.redTotalAmount || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="申请开票时间" min-width="180" prop="applyForDate">
        <template slot-scope="scope">
          {{ scope.row.applyForDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="冲红状态" min-width="130">
        <template slot-scope="scope">
          <el-tooltip
            class="item"
            effect="dark"
            :content="scope.row.redInvoiceExceptionMsg ? `异常说明：${scope.row.redInvoiceExceptionMsg}` : '暂无数据'"
            placement="top"
            v-if="scope.row.redStatus === InvoiceStatusEnum.OPENING"
          >
            <el-badge is-dot :type="redInvoiceStatusMapType[scope.row.redStatus]" class="badge-status">
              {{ redInvoiceStatusMapName[scope.row.redStatus] }}
            </el-badge>
          </el-tooltip>
          <el-badge
            is-dot
            :type="redInvoiceStatusMapType[scope.row.redStatus]"
            class="badge-status"
            v-else-if="redInvoiceStatusMapName[scope.row.redStatus]"
          >
            {{ redInvoiceStatusMapName[scope.row.redStatus] }}
          </el-badge>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="160" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="downloadRedInvoice(scope.row.redFilePath)">下载冲红发票</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="page" v-bind="page"></hb-pagination>
    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务管理管理-集体报名冲红发票</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goExportDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, Watch } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import QueryPageInvoiceParam from '@api/service/management/trade/batch/invoice/query/dto/QueryPageInvoiceParam'
  import {
    BillStatusChangeTimeRequest,
    DateScopeRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import InvoiceListResponse from '@api/service/management/trade/batch/invoice/query/vo/InvoiceListResponse'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { InvoiceStatusEnum, TitleTypeEnum } from '@api/service/management/trade/batch/invoice/enum/InvoiceEnum'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import FileModule from '@api/service/common/file/FileModule'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryInvoiceInTrainingChannel from '@api/service/management/trade/batch/invoice/query/QueryInvoiceInTrainingChannel'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

  @Component({
    components: { DoubleDatePicker }
  })
  export default class extends Vue {
    select = ''
    input = ''
    pageData: Array<InvoiceListResponse> = new Array<InvoiceListResponse>()
    SaleChannelEnum = SaleChannelEnum
    InvoiceStatusEnum = InvoiceStatusEnum
    form = {
      data1: ''
    }
    pageQueryParam: QueryPageInvoiceParam = new QueryPageInvoiceParam()
    exportQueryParam: QueryPageInvoiceParam = new QueryPageInvoiceParam()
    hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
    page: UiPage
    query: Query = new Query()
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    //专题查询请求
    queryZtInvoice = new QueryInvoiceInTrainingChannel()
    //开票总金额
    totalAmount = 0
    //发票总税额
    totalTax = 0
    exportSuccessVisible = false
    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }

    redInvoiceStatus = [
      {
        name: '冲红中',
        value: InvoiceStatusEnum.OPENING
      },
      {
        name: '冲红失败',
        value: InvoiceStatusEnum.OPENERROR
      },
      {
        name: '冲红成功',
        value: InvoiceStatusEnum.OPEMSUCCESS
      }
    ]

    redInvoiceStatusMapName = {
      [InvoiceStatusEnum.OPENING]: '冲红中',
      [InvoiceStatusEnum.OPENERROR]: '冲红失败',
      [InvoiceStatusEnum.OPEMSUCCESS]: '冲红成功'
    }

    redInvoiceStatusMapType = {
      [InvoiceStatusEnum.OPENING]: 'primary',
      [InvoiceStatusEnum.OPENERROR]: 'danger',
      [InvoiceStatusEnum.OPEMSUCCESS]: 'success'
    }

    TrainingModeEnum = TrainingModeEnum

    constructor() {
      super()
      this.pageQueryParam.redBillStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.redBillStatusChangeTime.billing = new DateScopeRequest()
      this.pageQueryParam.redBillStatusChangeTime.success = new DateScopeRequest()
      if (this.isZtlogin) {
        this.page = new UiPage(this.doSearchZt, this.doSearchZt)
      } else {
        this.page = new UiPage(this.doSearch, this.doSearch)
      }
    }
    get showPeriodName() {
      return [this.TrainingModeEnum.mixed, this.TrainingModeEnum.offline].includes(
        this.hasSelectSchemeMode[0]?.trainingMode?.skuPropertyValueId
      )
    }
    // 培训方案入参
    @Watch('hasSelectSchemeMode', {
      deep: true
    })
    changeScheme(val: Array<HasSelectSchemeMode>) {
      this.pageQueryParam.periodId = ''
    }
    async resetCondition() {
      this.page.pageNo = 1
      this.pageQueryParam = new QueryPageInvoiceParam()
      this.pageQueryParam.redBillStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.redBillStatusChangeTime.success = new DateScopeRequest()
      this.hasSelectSchemeMode = new Array<HasSelectSchemeMode>()

      if (this.isZtlogin) {
        await this.doSearchZt()
      } else {
        await this.doSearch()
      }
    }

    async doSearch() {
      this.query.loading = true
      const queryInvoice = TradeModule.batchTradeBatchFactor.invoiceFactor.queryInvoice
      try {
        this.pageQueryParam.flushed = true
        this.pageData = await queryInvoice.onLinePageInvoiceInServicer(this.page, this.pageQueryParam)
        this.totalAmount = queryInvoice.totalAmount
        this.totalTax = queryInvoice.totalTax
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['redInvoiceTable'] as any).doLayout()
        this.query.loading = false
        this.exportQueryParam = Object.assign(new QueryPageInvoiceParam(), this.pageQueryParam)
      }
    }
    async doSearchZt() {
      this.query.loading = true
      const queryInvoice = TradeModule.batchTradeBatchFactor.invoiceFactor.queryInvoice
      try {
        this.pageQueryParam.flushed = true
        this.pageData = await this.queryZtInvoice.onLinePageInvoiceInServicer(this.page, this.pageQueryParam)
        this.totalAmount = this.queryZtInvoice.totalAmount
        this.totalTax = this.queryZtInvoice.totalTax
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['redInvoiceTable'] as any).doLayout()
        this.query.loading = false
        this.exportQueryParam = Object.assign(new QueryPageInvoiceParam(), this.pageQueryParam)
      }
    }

    //下载冲红发票
    async downloadRedInvoice(baseUrl: string) {
      await FileModule.applyResourceAccessToken()
      let url = ''
      if (baseUrl && baseUrl.substring(0, 4) !== '/mfs') {
        url = this.$util.imgUrlWithToken('/mfs' + baseUrl)
      } else {
        url = this.$util.imgUrlWithToken(baseUrl)
      }
      window.open(url, '_blank')
      // window.open('/mfs' + url, '_blank')
    }
    // 导出列表数据
    async exportSpecialInvoice() {
      this.pageQueryParam.flushed = true
      try {
        let res
        if (this.isZtlogin) {
          res = await this.onLinePageInvoiceInZtExport()
        } else {
          res = await this.onLinePageInvoiceInExport()
        }
        if (res) {
          this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }
    /**
     * 导出列表数据
     */
    async onLinePageInvoiceInExport() {
      return await TradeModule.batchTradeBatchFactor.invoiceFactor.queryInvoice.onLinePageInvoiceInExport(
        this.exportQueryParam
      )
    }
    async onLinePageInvoiceInZtExport() {
      return await this.queryZtInvoice.onLinePageInvoiceInExport(this.exportQueryParam)
    }
    // 下载导出数据
    goExportDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportBatchRedVatSpecialOnlineInvoice' }
      })
    }
    async created() {
      if (this.isZtlogin) {
        await this.doSearchZt()
      } else {
        await this.doSearch()
      }
    }
  }
</script>
