import BasicDataGateway, {
  SchoolTrainingPropertyQueryRequest,
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { listIndustryPropertyByOnlineSchoolV2 } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage/graphql-importer'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
import MajorParam from '@api/service/common/basic-data-dictionary/query/vo/majorParam'
import Context from '@api/service/common/context/Context'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import Vue from 'vue'
import { IndustryPropertyCodeEnum } from '../../enum/IndustryPropertyCodeEnum'

/**
 * @description 培训专业请求
 */
export class TrainingProfessionalRequest {
  /**
   * 行业id
   */
  industryId = ''
  /**
   * 培训专业id集合
   */
  trainingProfessionalIdList: string[] = []

  constructor(industryId?: string, trainingProfessionalIdList?: string[]) {
    this.industryId = industryId
    this.trainingProfessionalIdList = trainingProfessionalIdList
  }
}

class QueryTrainingMajor {
  /**
   * 培训专业列表
   */
  trainingMajorList: Array<TrainingCategoryVo>

  /**
   * 培训专业列表缓存
   */
  trainingMajorCache: { [key: string]: TrainingCategoryVo } = {}

  /**
   * 查询培训专业列表
   * @param 行业属性id 行业属性分类id 培训类别id
   * @return
   */
  async queryTrainingMajor(params: MajorParam) {
    // todo 是否需要缓存
    const res = await BasicDataGateway.listIndustryPropertyChildByCategoryV2({
      industryPropertyId: params.industryPropertyId,
      categoryCode: IndustryPropertyCodeEnum.PERSON_TRAINING_CATEGORY,
      propertyId: params.parentPropertyId
    })
    if (res.status.isSuccess()) {
      const currentIndustry = await BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustry.getIndustryByIdList(
        [params.industryPropertyId]
      )
      this.setTrainingMajorCache(currentIndustry[0].id, res.data)
      this.trainingMajorList = res.data
    }
    return res.status
  }

  /**
   * 查询培训专业通过idList
   */
  async queryTrainingMajorByIdList(param: { industryId: string; majorIdList: Array<string> }) {
    const schoolTrainingPropertyQueryRequest = new SchoolTrainingPropertyQueryRequest()
    schoolTrainingPropertyQueryRequest.propertyId = param.majorIdList
    schoolTrainingPropertyQueryRequest.industryId = param.industryId
    schoolTrainingPropertyQueryRequest.schoolId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
    const res = await BasicDataGateway.listIndustryPropertyByOnlineSchoolV2(schoolTrainingPropertyQueryRequest)
    if (res.status.isSuccess()) {
      const list = res.data?.map(TrainingCategoryVo.from)
      this.setTrainingMajorCache(param.industryId, list)
      return list
    }
  }

  /**
   * 设置培训专业缓存
   */
  setTrainingMajorCache(industryId: string, trainingMajorList: Array<TrainingCategoryVo>) {
    trainingMajorList?.forEach(trainingMajor => {
      if (!this.trainingMajorCache[`${industryId}_${trainingMajor.propertyId}`]) {
        Vue.set(this.trainingMajorCache, `${industryId}_${trainingMajor.propertyId}`, trainingMajor)
      }
    })
  }

  /**
   * 获取培训专业列表
   */
  async getTrainingMajorByIdList(param: { industryId: string; trainingMajorIdList: Array<string> }) {
    const trainingMajorList = new Array<TrainingCategoryVo>()
    const idList = new Array<string>()
    param.trainingMajorIdList?.forEach(id => {
      const trainingMajor = this.trainingMajorCache[`${param.industryId}_${id}`]
      if (trainingMajor) {
        trainingMajorList.push(trainingMajor)
      } else {
        // 不在缓存中时会发起请求获取
        idList.push(id)
      }
    })
    if (idList.length) {
      const list = await this.queryTrainingMajorByIdList({
        industryId: param.industryId,
        majorIdList: idList
      })
      trainingMajorList.push(...list)
    }
    return trainingMajorList
  }

  /**
   * 批量查询培训类别
   * @param request 请求入参
   */
  async batchGetTrainingMajorByIdList(request: TrainingProfessionalRequest[]) {
    const result = [] as TrainingCategoryVo[]
    const params = request.map(item => {
      const opt = new SchoolTrainingPropertyQueryRequest()
      opt.propertyId = item.trainingProfessionalIdList
      opt.industryId = item.industryId
      opt.schoolId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
      return opt
    })
    const reWriteGQL = new RewriteGraph<TrainingPropertyResponse[], SchoolTrainingPropertyQueryRequest>(
      BasicDataGateway._commonQuery,
      listIndustryPropertyByOnlineSchoolV2
    )
    await reWriteGQL.request(params)
    for (const [key, value] of reWriteGQL.itemMap.entries()) {
      if (value && value.length) {
        value.forEach(el => {
          result.push(TrainingCategoryVo.from(el))
        })
      }
    }
    return result
  }
}

export default new QueryTrainingMajor()
