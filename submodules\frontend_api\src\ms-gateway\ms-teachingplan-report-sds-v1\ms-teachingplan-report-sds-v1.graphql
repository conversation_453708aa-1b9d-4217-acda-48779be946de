"""独立部署的微服务,K8S服务名:ms-teachingplan-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""学员扫码报到，前端请求这个接口即可
		@param request {@link StudentReportCheckInRequest}
		@return {@link StudentReportCheckInResponse}
	"""
	studentReportCheckIn(request:StudentReportCheckInRequest):StudentReportCheckInResponse
	"""学员扫码报到"""
	studentReportCheckInWithStudentNo(request:StudentReportCheckInWithStudentNoRequest):StudentReportCheckInResponse
}
"""学员报到请求体"""
input StudentReportCheckInRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.StudentReportCheckInRequest") {
	"""期别的学员学习token"""
	studentLearningToken:String!
	"""教学计划ID"""
	planId:String!
	"""经度"""
	longitude:Double
	"""维度"""
	latitude:Double
	"""是否开启范围校验"""
	enableRangeCheck:Boolean!
}
"""学员报到请求体"""
input StudentReportCheckInWithStudentNoRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.StudentReportCheckInWithStudentNoRequest") {
	"""学员学号"""
	studentNo:String!
	"""期别ID"""
	issueId:String!
	"""参训资格ID"""
	qualificationId:String!
	"""教学计划学习方式ID, 没有值时请提供-1"""
	learningId:String!
	"""教学计划ID, 没有值时请提供 -1"""
	planId:String!
	"""经度"""
	longitude:Double
	"""维度"""
	latitude:Double
	"""是否开启范围校验"""
	enableRangeCheck:Boolean!
}
"""学员报到响应体"""
type StudentReportCheckInResponse @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.response.StudentReportCheckInResponse") {
	"""状态码"""
	code:String
	"""消息"""
	message:String
}

scalar List
