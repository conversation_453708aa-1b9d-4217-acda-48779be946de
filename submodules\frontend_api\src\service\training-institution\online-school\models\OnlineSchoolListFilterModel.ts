import {
  ClientTypesEnum,
  OnlineSchoolEnum,
  SchoolStatusEnum,
  ServicePeriodEnum
} from '@api/service/training-institution/online-school/enum/OnlineSchoolEnum'
import {
  OnlineSchoolInfoQueryRequest,
  OnlineSchoolInfoRequest,
  OnlineSchoolInfoSortRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

export default class OnlineSchoolListFilterModel {
  /**
   * 网校名称
   */
  schoolName: string = undefined

  /**
   * 服务地区code
   */
  serviceRegionCodes: Array<string> = new Array<string>()

  /**
   * 培训行业id
   */
  industryIds: Array<string> = new Array<string>()

  /**
   * 网校状态
   */
  schoolStatus: SchoolStatusEnum = undefined

  /**
   * 业主名称
   */
  ownerUnitName: string = undefined

  /**
   * 网校性质
   */
  schoolModel: OnlineSchoolEnum = undefined

  /**
   * 开通筛选起始时间
   */
  openStartTime: string = undefined

  /**
   * 开通筛选结尾时间
   */
  openEndTime: string = undefined

  /**
   * 服务到期筛选起始时间
   */
  serviceOverStartTime: string = undefined

  /**
   * 服务到期筛选结束时间
   */
  serviceOverEndTime: string = undefined

  /**
   * 提供终端
   */
  providerService: ClientTypesEnum = undefined

  /**
   * 服务期限
   */
  servicePeriodModel: ServicePeriodEnum = undefined

  /**
   * 是否延长服务
   */
  extendedService: boolean = undefined

  /**
   * 转化为查询参数
   */
  toQueryRequest(): OnlineSchoolInfoRequest {
    const dto = new OnlineSchoolInfoRequest()
    dto.onlineSchoolInfoQuery = new OnlineSchoolInfoQueryRequest()
    dto.onlineSchoolInfoSort = new OnlineSchoolInfoSortRequest()
    dto.onlineSchoolInfoQuery.name = this.schoolName
    dto.onlineSchoolInfoQuery.serviceAreas = this.serviceRegionCodes
    dto.onlineSchoolInfoQuery.industryIds = this.industryIds
    dto.onlineSchoolInfoQuery.unitName = this.ownerUnitName
    dto.onlineSchoolInfoQuery.onlineSchoolModes = this.schoolModel
    dto.onlineSchoolInfoQuery.openTimeBegin = this.openStartTime
    dto.onlineSchoolInfoQuery.openTimeEnd = this.openEndTime
    dto.onlineSchoolInfoQuery.isRenewed = this.extendedService
    dto.onlineSchoolInfoQuery.expireDateBegin = this.serviceOverStartTime
    dto.onlineSchoolInfoQuery.expireDateEnd = this.serviceOverEndTime
    dto.onlineSchoolInfoQuery.trainingPeriodModes = this.servicePeriodModel
    dto.onlineSchoolInfoQuery.clientType = new Array<number>()
    dto.onlineSchoolInfoQuery.clientType.push(this.providerService)
    switch (this.schoolStatus) {
      case SchoolStatusEnum.OVER:
        dto.onlineSchoolInfoQuery.status = 2
        dto.onlineSchoolInfoQuery.isEnable = undefined
        dto.onlineSchoolInfoQuery.isExpired = true
        break
      case SchoolStatusEnum.DISABLE:
        dto.onlineSchoolInfoQuery.status = 2
        dto.onlineSchoolInfoQuery.isEnable = false
        dto.onlineSchoolInfoQuery.isExpired = false
        break
      case SchoolStatusEnum.OPERATE:
        dto.onlineSchoolInfoQuery.status = 2
        dto.onlineSchoolInfoQuery.isEnable = true
        dto.onlineSchoolInfoQuery.isExpired = false
        break
      case SchoolStatusEnum.OPENING:
        dto.onlineSchoolInfoQuery.status = 1
        dto.onlineSchoolInfoQuery.isEnable = undefined
        dto.onlineSchoolInfoQuery.isExpired = undefined
        break
      default:
        dto.onlineSchoolInfoQuery.status = undefined
        dto.onlineSchoolInfoQuery.isEnable = undefined
        dto.onlineSchoolInfoQuery.isExpired = undefined
    }

    return dto
  }
}
