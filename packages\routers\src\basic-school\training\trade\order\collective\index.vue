<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2024-09-11 19:07:04
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2024-12-30 15:03:20
-->
<route-meta>
{
"isMenu": true,
"title": "集体报名订单",
"sort": 2,
"icon": "icon_guanli"
}
</route-meta>
<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { FXS, GYS, WXGLY, ZTGLY } from '@/models/RoleTypes'
  import CollectiveIndex from '@hbfe/jxjy-admin-trade/src/order/collective/index.vue'
  @RoleTypeDecorator({
    query: [WXGLY, FXS, GYS],
    queryZt: [ZTGLY],
    export: [WXGLY, FXS, GYS],
    exportZt: [ZTGLY],
    search: [WXGLY, FXS, GYS],
    searchZt: [ZTGLY],
    exportDetail: [WXGLY, FXS, GYS],
    exportDetailZt: [ZTGLY],
    placeOrderResult: [WXGLY, FXS, GYS, ZTGLY],
    detail: [WXGLY, FXS, GYS, ZTGLY],
    closeBatch: [WXGLY, FXS, GYS],
    viewRemittanceVoucher: [WXGLY, FXS, GYS, ZTGLY],
    applyRefund: [WXGLY, FXS, GYS],
    confirmReceipt: [WXGLY, FXS, GYS],
    refundDetail: [WXGLY, FXS, GYS, ZTGLY],
    editInvoicePopup: [WXGLY, FXS, GYS]
  })
  export default class extends CollectiveIndex {}
</script>
