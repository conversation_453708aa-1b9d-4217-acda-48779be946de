<template>
  <el-drawer title="选择收款账号" :visible.sync="show" size="800px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-table stripe :data="receiveAccountVo" max-height="650px" class="m-table">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="收款账号别名" prop="accountName" min-width="180"> </el-table-column>
        <el-table-column label="操作" width="140" align="center">
          <template slot-scope="scope">
            <el-link type="primary" @click="changeSelect(scope.row)"
              >{{ itemIsSelected(scope.row) ? '取消选择' : '选择' }}
            </el-link>
          </template>
        </el-table-column>
      </el-table>
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import TradeInfoConfigModule from '@api/service/management/trade-info-config/TradeInfoConfigModule'
  import QueryReceiveAccount from '@api/service/management/trade-info-config/query/QueryReceiveAccount'
  import ReceiveAccountVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountVo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import { log } from 'lodash-decorators/utils'
  import { cloneDeep } from 'lodash'
  @Component
  export default class extends Vue {
    /**
     * 外部传过来打开关闭
     */
    @Prop({
      required: true
    })
    visible: boolean

    @Prop({
      default: () => new ReceiveAccountVo()
    })
    getData: ReceiveAccountVo
    @Watch('getData', {
      deep: true,
      immediate: true
    })
    getDataChange(val: any) {
      if (val) {
        // console.log(this.getData, 'getData')
        this.idList = [this.getData]
      }
    }
    // @PropSync('visible', { type: Boolean }) show!: boolean
    // 页面分页控件
    page: UiPage
    // 分页查询
    query: Query = new Query()
    //接口请求
    queryReceiveAccount: QueryReceiveAccount =
      TradeInfoConfigModule.queryTradeInfoConfigFactory.getQueryReceiveAccount()
    //收款账号列表
    receiveAccountVo: Array<ReceiveAccountVo> = new Array<ReceiveAccountVo>()
    // 选中的列表
    idList: Array<ReceiveAccountVo> = new Array<ReceiveAccountVo>()
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable

    constructor() {
      super()
      if (this.isFxlogin && this.isHadFxAbility) {
        this.page = new UiPage(this.doQueryPagefx, this.doQueryPagefx)
      } else {
        this.page = new UiPage(this.doQueryPage, this.doQueryPage)
      }
    }
    get show() {
      //该抽屉的显隐

      return this.visible
    }
    set show(val: boolean) {
      this.$emit('update:visible', val)
    }
    /**
     * 是否选中
     */
    get itemIsSelected() {
      return (item: ReceiveAccountVo) => {
        // return this.idList.indexOf(item) > -1 ? true : false
        const include = this.idList.find((el) => el.id === item.id)
        // console.log(include, this.idList)
        return include ? true : false
      }
    }

    // @Watch('idList', {
    //   immediate: true,
    //   deep: true
    // })
    // idListChange(val: ReceiveAccountVo[]) {
    //   console.log('111', cloneDeep(JSON.stringify(val)))
    // }
    // @Watch('receiveAccountVo', {
    //   immediate: true,
    //   deep: true
    // })
    // receiveAccountVoChange(val: ReceiveAccountVo[]) {
    //   console.log('222', cloneDeep(val))
    // }

    /**
     * 选择
     */
    changeSelect(item: ReceiveAccountVo) {
      const index = this.idList.findIndex((el) => el.id === item.id)
      if (index > -1) {
        this.idList.splice(index, 1)
      } else {
        this.idList = [] as ReceiveAccountVo[]
        this.idList.push(item)
        console.log(JSON.stringify(cloneDeep(this.idList)))
        this.$emit('getAccountNumber', this.idList)
        this.show = false
      }
    }
    async doQueryPage() {
      try {
        this.receiveAccountVo = await this.queryReceiveAccount.queryPage(this.page)
      } catch (e) {
        console.log(e, '获取收款账户失败')
      } finally {
        11
      }
    }

    async doQueryPagefx() {
      try {
        this.receiveAccountVo = await this.queryReceiveAccount.queryFxPage(this.page)
      } catch (e) {
        console.log(e, '获取分销收款账户失败')
      } finally {
        11
      }
    }
  }
</script>
