import { ExchangeTrainClassStatusEnum } from '@api/service/common/enums/train-class/ExchangeTrainClassStatusType'
import CommodityDetailVo from '@api/service/centre/trade/batch/order/query/vo/CommodityDetailVo'
import { ExchangeOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import BatchOrderUtils from '@api/service/centre/trade/batch/order/query/util/BatchOrderUtils'

/**
 * @description 换班列表详情
 */
class ExchangeTrainClassListDetailVo {
  /**
   * 原始商品（换出商品）
   */
  originCommodity: CommodityDetailVo = new CommodityDetailVo()

  /**
   * 当前商品（换入商品）
   */
  currentCommodity: CommodityDetailVo = new CommodityDetailVo()

  /**
   * 换班状态
   */
  exchangeTrainClassStatus: ExchangeTrainClassStatusEnum = null

  /**
   * 换班成功时间
   */
  exchangeSuccessTime = ''

  static async from(response: ExchangeOrderResponse): Promise<ExchangeTrainClassListDetailVo> {
    const detail = new ExchangeTrainClassListDetailVo()
    // 填充换出商品
    detail.originCommodity = BatchOrderUtils.fillCommodityInfo(response.originalCommodity?.commoditySku)
    // 填充换入商品
    detail.currentCommodity = BatchOrderUtils.fillCommodityInfo(response.exchangeCommodity?.commoditySku)
    const exchangeStatus = response.basicData?.status ?? undefined
    if ([0, 2, 3, 4, 5, 6].includes(exchangeStatus)) {
      detail.exchangeTrainClassStatus = ExchangeTrainClassStatusEnum.Exchanging
    }
    if (exchangeStatus === 7) {
      detail.exchangeTrainClassStatus = ExchangeTrainClassStatusEnum.Complete_Exchange
    }
    if (exchangeStatus === 8) {
      detail.exchangeTrainClassStatus = ExchangeTrainClassStatusEnum.Cancle_Exchange
    }
    detail.exchangeSuccessTime = response.basicData?.statusChangeTime?.applied ?? ''
    return detail
  }
}

export default ExchangeTrainClassListDetailVo
