import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 报名状态枚举
 */
export enum SignUpStatusEnum {
  // 未完成
  Unfinished = 1,
  // 已完成
  Finished,
  // 已失效
  Invalided
}

/**
 * @description 报名状态列表
 */
class SignUpStatusList extends AbstractEnum<SignUpStatusEnum> {
  static enum = SignUpStatusEnum

  constructor(status?: SignUpStatusEnum) {
    super()
    this.current = status
    this.map.set(SignUpStatusEnum.Unfinished, '未完成')
    this.map.set(SignUpStatusEnum.Finished, '已完成')
    this.map.set(SignUpStatusEnum.Invalided, '已失效')
  }
}

export default new SignUpStatusList()
