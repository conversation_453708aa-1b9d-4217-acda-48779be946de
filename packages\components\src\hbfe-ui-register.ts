import { HbPagination, HbEllipsis, HbCopy } from '@hbfe/hbfe-ui'
import Vue from 'vue'

class HBUIRegister {
  /**
   * 注册全局组件
   *
   * 此函数在Vue应用中注册了一个名为'PasswordStrengthIndicator'的全局组件
   * 它将`PasswordStrengthIndicator`组件与字符串标识符关联起来，使该组件可以在应用的任何地方被引用
   * 这样做可以简化组件的使用，避免在需要的地方重复导入
   */
  registerComponents() {
    Vue.component('HbEllipsis', HbEllipsis)
    Vue.component('HbCopy', HbCopy)
    Vue.component('HbPagination', HbPagination)
    //后续有组件需要注册可以放在这里
  }
}

// 导出 HBUIRegister 类的实例作为默认模块导出
export default new HBUIRegister()
