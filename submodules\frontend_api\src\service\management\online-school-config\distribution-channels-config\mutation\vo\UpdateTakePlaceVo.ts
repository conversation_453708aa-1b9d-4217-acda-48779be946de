import CreateTakePlaceVo from '@api/service/management/online-school-config/distribution-channels-config/mutation/vo/CreateTakePlaceVo'
import { UpdateOfflineInvoiceDeliveryChannelRequest } from '@api/ms-gateway/ms-offlineinvoice-v1'
import { OfflineInvoiceDeliveryChannelResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

/**
 * @description 创建自取点
 */
class UpdateTakePlaceVo extends CreateTakePlaceVo {
  toUpdateDto(): UpdateOfflineInvoiceDeliveryChannelRequest {
    const dto = new UpdateOfflineInvoiceDeliveryChannelRequest()
    dto.channelId = this.id
    dto.channelName = this.name
    dto.address = this.address
    dto.shippingMethod = 1
    dto.remark = this.remark
    dto.enable = this.status
    dto.deliveryDate = this.openTakeTime
    return dto
  }

  static from(response: OfflineInvoiceDeliveryChannelResponse) {
    const detail = new UpdateTakePlaceVo()
    detail.id = response.channelId
    detail.name = response.channelName
    detail.address = response.address
    detail.remark = response.remark
    detail.openTakeTime = response.deliveryDate
    detail.status = response.enable
    return detail
  }
}

export default UpdateTakePlaceVo
