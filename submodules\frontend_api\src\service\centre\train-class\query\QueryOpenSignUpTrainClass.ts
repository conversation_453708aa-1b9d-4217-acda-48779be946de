import { Page, ResponseStatus } from '@hbfe/common'
import QueryOpenSignUpTrainClassListVo from '@api/service/centre/train-class/query/vo/QueryOpenSignUpTrainClassListVo'
import OpenSignUpTrainClassListDetailVo from '@api/service/centre/train-class/query/vo/OpenSignUpTrainClassListDetailVo'
import msTradeQuery, {
  CommoditySkuPropertyResponse,
  CommoditySkuRequest,
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  PortalCommoditySkuPropertyResponse,
  SchemeResourceResponse,
  SkuPropertyRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import DataResolve from '@api/service/common/utils/DataResolve'
import SkuPropertyConvertUtils from '@api/service/centre/train-class/util/SkuPropertyConvertUtils'
import SkuPropertyListVo from '@api/service/centre/train-class/query/vo/SkuPropertyListVo'
import TrainClassUtils from '@api/service/centre/train-class/util/TrainClassUtils'
import SkuPropertyConvertUtilsNew, {
  SchemeSkuInfo
} from '@api/service/customer/train-class/Utils/SkuPropertyConvertUtils'
import { SchemeSkuPropertyResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'

/**
 * @description 查询开放报名的方案列表
 */
class QueryOpenSignUpTrainClass {
  /**
   * 当前选中的行业id
   */
  private currentInId = ''

  /**
   * 筛选
   */
  skuProperties: SkuPropertyListVo = new SkuPropertyListVo()
  /**
   *培训班商品列表，类型为TrainClassCommodityVo[]
   */
  trainClassCommodityList: OpenSignUpTrainClassListDetailVo[] = []
  /**
   *总数目，类型为number
   */
  totalSize = 0

  /**
   * 查询开放报名的方案列表
   * @param {Page} page - 分页参数
   * @param {QueryOpenSignUpTrainClassListVo} queryParams - 查询参数
   * @return {Promise<OpenSignUpTrainClassListDetailVo[]>} - 开放报名的方案列表
   */
  async queryOpenSignUpTrainClassList(
    page: Page,
    queryParams: QueryOpenSignUpTrainClassListVo
  ): Promise<OpenSignUpTrainClassListDetailVo[]> {
    // 默认按上架时间倒序
    const sortOption = new CommoditySkuSortRequest()
    sortOption.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(sortOption) as CommoditySkuSortRequest[]
    const queryRequest = queryParams.to()
    const response = await msTradeQuery.pageCommoditySkuCustomerCollectivePurchaseInServicer({
      page,
      queryRequest,
      sortRequest
    })
    page.totalSize = response.data?.totalSize ?? 0
    this.totalSize = response.data?.totalSize
    page.totalPageSize = response.data?.totalPageSize ?? 0
    const schemeIds = response.data.currentPageData.map(
      (item) => (item.resource as SchemeResourceResponse).schemeId ?? ''
    )
    const schemeConfigs = await TrainClassUtils.pageTrainClassConfig(schemeIds)

    let result = new Array<OpenSignUpTrainClassListDetailVo>()
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      const skuList = new Array<SchemeSkuInfo>()

      response.data.currentPageData.map((item) => {
        const resource = item.resource as SchemeResourceResponse
        const skuDtoObj = Object.assign(new SchemeSkuPropertyResponse(), new PortalCommoditySkuPropertyResponse())
        const sku = new SchemeSkuInfo(resource.schemeId, Object.assign(skuDtoObj, item.skuProperty))
        skuList.push(sku)
      })

      const skuVoList = await SkuPropertyConvertUtilsNew.batchConvertToSkuPropertyResponseVo(skuList)

      result = response.data.currentPageData.map((item) => {
        return OpenSignUpTrainClassListDetailVo.fromBatch(item, schemeConfigs, skuVoList)
      })
    }
    this.trainClassCommodityList = result
    return result
  }

  /**
   * 获取培训班属性
   */
  async querySku(sku: SkuPropertyRequest = new SkuPropertyRequest(), saleTitleMatchLike = ''): Promise<ResponseStatus> {
    if (DataResolve.isWeightyArr(sku.industry)) {
      this.currentInId = sku.industry[0]
    }
    const queryParams = new CommoditySkuRequest()
    queryParams.skuPropertyRequest = sku
    if (saleTitleMatchLike) {
      queryParams.saleTitleMatchLike = saleTitleMatchLike
    }
    const response = await msTradeQuery.listSkuPropertyCollectivePurchaseInServicer(queryParams)
    if (response.status?.isSuccess()) {
      try {
        this.skuProperties = await SkuPropertyConvertUtils.convertToSkuPropertyVo(response.data, this.currentInId)
      } catch (e) {
        console.log(e)
      }
    }
    return response.status
  }
}

export default QueryOpenSignUpTrainClass
