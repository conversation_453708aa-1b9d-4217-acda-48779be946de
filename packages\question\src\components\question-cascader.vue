<template>
  <div v-loading="!show">
    <el-cascader
      v-model="select"
      :props="props"
      style="width: 100%"
      :options="categoryCache"
      :popper-class="'hb-cascader-exam'"
      placeholder="请选择题库"
      v-bind="$attrs"
      v-if="show"
    >
    </el-cascader>
  </div>
</template>
<script lang="ts">
  import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { UiPage, Query } from '@hbfe/common'
  import LibraryResponseVo from '@api/service/management/resource/question-library/query/vo/LibraryResponseVo'
  import { isEmpty } from '@hbfe/jxjy-admin-common/src/util/util'
  import LibraryRequestVo from '@api/service/management/resource/question-library/query/vo/LibraryRequestVo'
  class NewLibraryResponseVo extends LibraryResponseVo {
    children? = new Array<any>()
    leaf = false
  }
  @Component
  export default class extends Vue {
    page: UiPage
    query: Query = new Query()
    libraryResponseVo = new LibraryResponseVo()
    LibraryRequestVo: LibraryRequestVo = new LibraryRequestVo()

    @Prop({
      type: Boolean,
      default: false
    })
    multiple: boolean // 是否多选

    // 双向绑定
    @Prop({
      type: String,
      required: true,
      default: ''
    })
    value: string

    //判断是否更新
    @Prop({
      type: Boolean,
      default: false
    })
    isUpdate: boolean

    show = true // 组件显示 回显用

    questionDetail = ''

    // 存储所有题库数据
    categoryCache = new Array<LibraryResponseVo>()
    // 查询题库实例
    questionLibrary = ResourceModule.queryQuestionLibraryFactory.queryQuestionLibraryMultiton

    //级联组件所需要传参
    props = {
      multiple: false,
      value: 'id',
      label: 'name'
    }

    //获取数据
    get select(): string | Array<string> {
      return this.value
    }
    set select(val: Array<string> | string) {
      let select = ''
      if (typeof val === 'object') {
        select = (val[val.length - 1] || '') as string
        this.$emit('input', select)
      } else {
        select = val as string
      }
      this.$emit('input', select)
    }

    constructor() {
      super()
      this.page = new UiPage(this.lazyLoad, this.lazyLoad)
    }

    async created() {
      // 查询
      await this.lazyLoad()
    }

    async lazyLoad() {
      this.show = false
      this.page.pageSize = 200
      this.LibraryRequestVo.parentLibraryId = ''
      const res = await this.questionLibrary.queryQuestionBankLibrary(this.page, this.LibraryRequestVo)
      if (!res?.status?.isSuccess()) {
        console.error('获取题库信息失败！')
        return
      }
      // 全部题库数据
      const arr = cloneDeep(res?.data)
      arr?.map((item) => {
        //假数据  根节点parenId为undefined
        if (item.parentId == undefined) {
          item.parentId = '-1'
        }
      })
      // console.log(arr, '试题递归的数组1')
      this.categoryCache = this.recursion('-1', arr)
      this.show = true
    }
    // 递归
    recursion(id: string, arr: Array<LibraryResponseVo>) {
      const parentList = new Array<NewLibraryResponseVo>()
      const childrenList = new Array<LibraryResponseVo>()
      // console.log(arr, '试题递归的数组2')
      arr?.forEach((item: LibraryResponseVo) => {
        if (item.parentId === id) {
          const temp = new NewLibraryResponseVo()
          Object.assign(temp, item)
          temp.children = new Array<any>()
          temp.leaf = false
          parentList.push(temp)
        } else {
          childrenList.push(item)
        }
      })

      parentList?.forEach((sub: NewLibraryResponseVo) => {
        const res = this.recursion(sub.id, childrenList)
        if (isEmpty(res)) {
          sub.leaf = true
          delete sub.children
        } else {
          sub.children = this.recursion(sub.id, childrenList)
        }
      })

      return parentList
    }
  }
</script>
<style>
  /*css方法：使用css将其他的选项框给干掉。*/
  /*以下样式将单选框隐藏 除了第三个单选框不隐藏*/
  .hb-cascader-exam .el-cascader-panel .el-scrollbar:first-of-type .el-checkbox {
    display: none;
  }
  .hb-cascader-exam .el-cascader-panel .el-scrollbar:nth-of-type(2) .el-checkbox {
    display: none;
  }
</style>
