/**
 * @description 期别学习方式考核项类型枚举
 * exam 后端内置考核类型
 * issue 期别考核类型
 */
export enum IssueAssessTypeEnum {
  exam = 'exam',
  issue = 'issue',
  issue_time = 'issue_time'
}

/**
 * @description 教学计划学习方式考核项类型枚举
 * plan 教学计划考核类型
 */
export enum TeachPlanAssessTypeEnum {
  plan = 'plan'
}

/**
 * @description 问卷学习方式考核类型枚举
 * questionary 问卷考核类型
 */
export enum QuestionnaireAssessTypeEnum {
  questionary = 'questionary'
}
