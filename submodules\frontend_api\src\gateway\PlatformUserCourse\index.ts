import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformUserCourse'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformUserCourse'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举

// 类

/**
 * 课程学习统计参数
@author: eleven
@date: 2020/4/13
 */
export class CourseLearningStatisticParam {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 学习方式id
   */
  learningId?: string
  /**
   * 课程id集合
   */
  courseIdList?: Array<string>
}

/**
 * 用户课程学习进度查询
@author: eleven
@date: 2020/3/5
 */
export class UserCourseLearningScheduleParamDTO {
  /**
   * 课程id集合
   */
  courseIdList?: Array<string>
  /**
   * 用户id -运营域参数，学员端忽略该参数
   */
  userId?: string
  /**
   * 选课类型
@see com.fjhb.platform.core.courselearning.v1.api.constants.UserCourseSource
   */
  source?: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * 用户已选课程查询参数
@author: eleven
@date: 2020/3/5
 */
export class UserCourseParamDTO {
  /**
   * 所属课程包id
   */
  coursePoolId?: string
  /**
   * 选课类型
@see com.fjhb.platform.core.courselearning.v1.api.constants.UserCourseSource
   */
  source?: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * 用户最后学习所在的课程查询条件
@author: eleven
@date: 2020/3/5
 */
export class UserLastLearningCourseParamDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * 用户课程
@author: eleven
@date: 2020/3/5
 */
export class UserSchemeCourseLearningStatisticParamDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * 用户课程学习记录查询参数
@author: eleven
@date: 2020/5/18
 */
export class UserCourseRecordParam {
  userId?: string
  schemeId?: string
  issueId?: string
  learningId?: string
}

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 课程被学习次数统计
@author: eleven
@date: 2020/4/13
 */
export class CourseLearningStatisticDTO {
  /**
   * 课程id
   */
  courseId: string
  /**
   * 总数被选课次数
   */
  selectedCount: number
  /**
   * 课程待学习总数
   */
  waitStudyCount: number
  /**
   * 本课程学习中总数
   */
  studyCount: number
  /**
   * 本课程学习完成总数
   */
  studyFinishCount: number
}

/**
 * 课程包基础信息
Author:FangKunSen
Time:2020-06-06,11:17
 */
export class CoursePoolBaseInfoDTO {
  /**
   * 课程包id
   */
  coursePoolId: string
  /**
   * 课程包名
   */
  showName: string
}

/**
 * 用户已选课程
@author: eleven
@date: 2020/3/5
 */
export class UserCourseDTO {
  /**
   * 所属课程包编号
   */
  poolId: string
  /**
   * 所属课程类型
1:必修课；2：选修；
   */
  courseType: number
  /**
   * 课程ID
   */
  id: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 权重,表示学时,学分等
当查询未选课列表时，为课程在课程包的学时
当查询已选课时，用户选课时，课程包配置的学时
   */
  period: number
  /**
   * 课程简介
   */
  abouts: string
  /**
   * 课程的课件状态，0表示解析中，1表示解析成功，2表示解析失败
   */
  status: number
  /**
   * 课程时长
   */
  timeLength: number
  /**
   * 是否支持试听
   */
  supportAudition: boolean
  /**
   * 计划课件数量
   */
  courseWareCount: number
  /**
   * 已更新的课件数量
   */
  courseWareUpdateCount: number
  /**
   * 课件教师id集合
   */
  teacherIdList: Array<string>
  /**
   * 所属课程分类集合（正常只有一个）
   */
  courseCategoryDtoList: Array<CourseCategoryDto>
  /**
   * 课程所属考纲id集合
   */
  tagIdList: Array<string>
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 是否启用
   */
  enabled: boolean
}

/**
 * 课程学习记录，来自于清洗中间表
<AUTHOR>
@date 2020/8/25
@description
 */
export class UserCourseLearningRecordResponse {
  /**
   * 用户
   */
  userId: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方式id
   */
  learningId: string
  /**
   * 期别编号
   */
  stageId: string
  /**
   * 期数编号
   */
  issueId: string
  /**
   * 年度
   */
  year: number
  /**
   * 选课规则id
   */
  ruleId: string
  /**
   * 包id
   */
  poolId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 选课类型
@see UserChooseCourseType
   */
  chooseCourseType: number
  /**
   * 课程学时
   */
  period: number
  /**
   * 课程时长
   */
  timeLength: number
  /**
   * 课程学习进度
   */
  schedule: number
  /**
   * 课程学习时长
   */
  learningTimeLength: number
  /**
   * 课程学习状态
@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
   */
  studyState: number
  /**
   * 开始学习时间
   */
  startStudyTime: string
  /**
   * 最后学习时间
   */
  lastStudyTime: string
  /**
   * 学习完成时间
   */
  studyCompleteTime: string
}

/**
 * 用户指定学习方案内指定课程的学习进度
@author: eleven
@date: 2020/3/5
 */
export class UserCourseLearningScheduleDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 学习进度
   */
  schedule: number
  /**
   * 学习状态
0/1/2，未学习/学习中/学习完成
@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
   */
  studyState: number
  /**
   * 最新学习时间
   */
  lastStudyTime: string
}

/**
 * 用户课件学习记录编号
<AUTHOR>
@date 2020/3/14
@since 1.0.0
 */
export class UserCoursewareLearningScheduleDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 课件编号
   */
  courseWareId: string
  /**
   * 课件学习进度
   */
  schedule: number
  /**
   * 课件学习状态
0/1/2，未学习/学习中/学习完成
@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
   */
  studyState: number
  /**
   * 最新学习时间
   */
  lastStudyTime: string
  /**
   * 课件学习创建时间
   */
  createTime: string
}

/**
 * 用户最后一次学习的课程
@author: eleven
@date: 2020/3/5
 */
export class UserLastLearningCourseDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 包id
   */
  poolId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 课程学习进度
   */
  schedule: number
  /**
   * 最后一次学习时间
   */
  lastStudyTime: string
}

/**
 * 用户方案内正在的学习的课程数统计
@author: eleven
@date: 2020/3/5
 */
export class UserSchemeCourseLearningStatisticDTO {
  /**
   * 待学习课程数
   */
  waitStudyCount: number
  /**
   * 学习中课程数
   */
  studyCount: number
  /**
   * 学习完成课程数
   */
  studyFinishCount: number
}

export class CourseCategoryDto {
  id: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  organizationId: string
  name: string
  parentId: string
  sort: number
  remarks: string
  objectId: string
}

export class UserCourseDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserCourseDTO>
}

export class UserCourseLearningRecordResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserCourseLearningRecordResponse>
}

export class CourseLearningStatisticDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseLearningStatisticDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取课程被学习的人次 - 来源中间表(已实现)
   * @param courseId
   * @return
   * @param query 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseLearningCount(
    courseId: string,
    query: DocumentNode = GraphqlImporter.getCourseLearningCount,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { courseId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取最后一次学习的课程 - 来源中间表(已实现)
   * @param paramDTO
   * @return UserLastLearningCourseDTO || null:未选课的时候
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUserLastCourseLearning(
    paramDTO: UserLastLearningCourseParamDTO,
    query: DocumentNode = GraphqlImporter.getUserLastCourseLearning,
    operation?: string
  ): Promise<Response<UserLastLearningCourseDTO>> {
    return commonRequestApi<UserLastLearningCourseDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户方案内的已选课程-课程服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserCourse(
    paramDTO: UserCourseParamDTO,
    query: DocumentNode = GraphqlImporter.listUserCourse,
    operation?: string
  ): Promise<Response<Array<UserCourseDTO>>> {
    return commonRequestApi<Array<UserCourseDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户指定课程的学习进度 -课程服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserCourseLearningSchedule(
    paramDTO: UserCourseLearningScheduleParamDTO,
    query: DocumentNode = GraphqlImporter.listUserCourseLearningSchedule,
    operation?: string
  ): Promise<Response<Array<UserCourseLearningScheduleDTO>>> {
    return commonRequestApi<Array<UserCourseLearningScheduleDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据方案id和课程学习方式id获取所有可选课程包
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserCoursePool(
    paramDTO: UserCourseParamDTO,
    query: DocumentNode = GraphqlImporter.listUserCoursePool,
    operation?: string
  ): Promise<Response<Array<CoursePoolBaseInfoDTO>>> {
    return commonRequestApi<Array<CoursePoolBaseInfoDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取指定课程下的所有课件进度
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserCoursewareLearningSchedule(
    paramDTO: UserCourseLearningScheduleParamDTO,
    query: DocumentNode = GraphqlImporter.listUserCoursewareLearningSchedule,
    operation?: string
  ): Promise<Response<Array<UserCoursewareLearningScheduleDTO>>> {
    return commonRequestApi<Array<UserCoursewareLearningScheduleDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户方案内未选择的课程 -课程服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserUnSelectCourse(
    params: { page?: Page; paramDTO?: UserCourseParamDTO },
    query: DocumentNode = GraphqlImporter.listUserUnSelectCourse,
    operation?: string
  ): Promise<Response<UserCourseDTOPage>> {
    return commonRequestApi<UserCourseDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户课程学习记录
   * @param page
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUserCourseLearning(
    params: { page?: Page; param?: UserCourseRecordParam },
    query: DocumentNode = GraphqlImporter.pageUserCourseLearning,
    operation?: string
  ): Promise<Response<UserCourseLearningRecordResponsePage>> {
    return commonRequestApi<UserCourseLearningRecordResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 课程学习统计
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticCourseLearning(
    params: { page?: Page; param?: CourseLearningStatisticParam },
    query: DocumentNode = GraphqlImporter.statisticCourseLearning,
    operation?: string
  ): Promise<Response<CourseLearningStatisticDTOPage>> {
    return commonRequestApi<CourseLearningStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 统计用户学习方案内的课程学习情况  - 来源中间表(已实现)
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserLearningCourseLearningInfo(
    paramDTO: UserSchemeCourseLearningStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserLearningCourseLearningInfo,
    operation?: string
  ): Promise<Response<UserSchemeCourseLearningStatisticDTO>> {
    return commonRequestApi<UserSchemeCourseLearningStatisticDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
