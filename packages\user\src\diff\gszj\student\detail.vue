<route-params content="/:id"></route-params>
<route-meta>
{
"title": "学员详情"
}
</route-meta>
<template>
  <detail-diff ref="detailRef">
    <template #topicinformation="{ UserDetail }">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">专题信息</span>
        </div>
        <div class="f-pb20 f-pt10">
          <div class="m-sub-tit is-border-bottom">
            <span class="tit-txt">金昌市</span>
          </div>
          <div class="f-plr40">
            <el-row :gutter="16">
              <el-form :inline="true" label-width="auto" class="m-text-form is-edit f-mt20">
                <el-col :sm="12" :md="8">
                  <el-form-item label="工作单位性质：">
                    {{ getDisplayValue(UserDetail.topicInformationVo.natureWorkUnitName) }}
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="在编情况：">
                    {{ getDisplayValue(UserDetail.topicInformationVo.currentSituationName) }}
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="是否在专技岗位工作：">
                    {{ getDisplayValue(UserDetail.topicInformationVo.isOnTechnicalPostName) }}
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="职称系列：">
                    {{ getDisplayValue(UserDetail.topicInformationVo.titleSeriesName) }}
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="职称专业：">
                    {{ getDisplayValue(UserDetail.topicInformationVo.professionalTitle) }}
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="现有职称等级：">
                    {{ getDisplayValue(UserDetail.topicInformationVo.currentTitleLevelName) }}
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="现有职称资格名称：">
                    {{ getDisplayValue(UserDetail.topicInformationVo.currentTitleQualification) }}
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="现有职称有效范围：">
                    {{ getDisplayValue(UserDetail.topicInformationVo.currentTitleValidRangeName) }}
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="最高学历：">
                    {{ getDisplayValue(UserDetail.topicInformationVo.highestEducation) }}
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
          </div>
        </div>
      </el-card>
    </template>
  </detail-diff>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import Detail from '@hbfe/jxjy-admin-user/src/student/detail.vue'
  import UserDetailVoDiff from '@api/service/diff/management/gszj/user/student/model/UserDetailVo'
  import QueryStudentDetail from '@api/service/diff/management/gszj/user/student/QueryStudentDetail'
  import { ProxyRef } from '@api/service/common/utils/ProxyRefMethods'

  @Component
  class DetailDiff extends Detail {
    UserDetail = new UserDetailVoDiff()

    async handleQueryDetail() {
      const queryStudentDetailDiff = new QueryStudentDetail(this.id)
      const res = await queryStudentDetailDiff.queryDetailDiff()
      this.UserDetail = res.data
    }
  }

  @Component({
    components: { DetailDiff }
  })
  @ProxyRef('detailRef', true)
  export default class extends Vue {
    @Ref('detailRef') detailRef: DetailDiff

    getDisplayValue(value: string | null): string {
      return value ? value : '-'
    }
  }
</script>
