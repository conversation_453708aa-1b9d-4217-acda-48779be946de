import FieldConstraintVo from '@api/service/customer/online-school-config/online-school-partal-config/query/vo/FieldConstraintVo'
import UnitConfigVo from '@api/service/customer/online-school-config/online-school-partal-config/query/vo/UnitConfigVo'

/**
 * @description 登录注册
 */
class RegisterSettingVo {
  /**
   * 学员账号注册配置启停
   */
  studentRegisterEnable = true
  /**
   * 集体报名账号注册配置启停
   */
  collectiveRegisterEnable = true
  /**
   * 学员账号注册配置
   */
  studentRegister: Array<FieldConstraintVo> = new Array<FieldConstraintVo>()
  /**
   * 人设行业注册配置
   */
  personIndustryRegister: Array<FieldConstraintVo> = new Array<FieldConstraintVo>()
  /**
   * 建设行业注册配置
   */
  constructionIndustryRegister: Array<FieldConstraintVo> = new Array<FieldConstraintVo>()
  /**
   * 加密token
   */
  token: string
  /**
   * 单位配置
   */
  unitConfig: UnitConfigVo = new UnitConfigVo()
}
export default RegisterSettingVo
