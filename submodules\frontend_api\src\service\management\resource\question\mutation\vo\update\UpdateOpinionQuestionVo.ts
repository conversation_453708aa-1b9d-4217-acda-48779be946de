import { UpdateOpinionQuestionRequest } from '@api/ms-gateway/ms-examquestion-v1'
import { OpinionQuestionResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import QuestionType from '@api/service/common/enums/question/QuestionType'
import UpdateQuestionVo from './UpdateQuestionVo'

/*
 * 判断题
 */
class UpdateOpinionQuestionVo extends UpdateQuestionVo {
  questionType = QuestionType.enum.opinion
  /**
   * 正确答案【必填】
   */
  correctAnswer = true
  /**
   * 正确文本【必填】
   */
  correctAnswerText = ''
  /**
   * 不正确文本【必填】
   */
  incorrectAnswerText = ''

  // 模型转换Vo
  from(data: OpinionQuestionResponse) {
    this.id = data.questionId
    this.topic = data.topic
    this.questionType = data.questionType
    this.libraryId = data.libraryInfo.libraryId
    this.dissects = data.dissects
    this.relateCourseId = data.relateCourseIds?.join('')
    this.correctAnswer = data.opinionCorrectAnswer
    this.correctAnswerText = data.correctAnswerText
    this.incorrectAnswerText = data.incorrectAnswerText
    this.questionDifficulty = data.questionDifficulty
  }

  // 模型转换
  toDto() {
    const updateQuestionDto = new UpdateOpinionQuestionRequest()
    updateQuestionDto.id = this.id
    updateQuestionDto.topic = this.topic
    updateQuestionDto.questionType = this.questionType
    updateQuestionDto.libraryId = this.libraryId
    updateQuestionDto.dissects = this.dissects
    updateQuestionDto.relateCourseIds = [this.relateCourseId]
    updateQuestionDto.correctAnswer = this.correctAnswer
    updateQuestionDto.correctAnswerText = this.correctAnswerText
    updateQuestionDto.incorrectAnswerText = this.incorrectAnswerText
    updateQuestionDto.questionDifficulty = this.questionDifficulty
    return updateQuestionDto
  }
}

export default UpdateOpinionQuestionVo
