<template>
  <div>
    <el-drawer
      title="一键合格"
      :visible.sync="isOneKeyQualifiedDrawer"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      size="800px"
      custom-class="m-drawer"
    >
      <div class="drawer-bd">
        <el-form
          ref="oneKeyQualifyForm"
          :model="oneKeyDrawerForm"
          :rules="oneKeyDrawerRules"
          label-width="auto"
          class="m-form f-mt20"
        >
          <el-form-item label="合格时间配置：" prop="qualifiedTime">
            <el-radio-group v-model="radioValue" @change="radioValueChange">
              <el-radio :label="1">
                指定合格时间
                <!--选中后出现输入框-->
                <el-date-picker
                  v-model="oneKeyDrawerForm.qualifiedTime"
                  type="datetime"
                  placeholder="请输入指定合格时间"
                  class="f-ml10"
                >
                </el-date-picker>
              </el-radio>
              <el-radio :label="2">按系统当前操作成功时间</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="考试合格分：" prop="examPoint">
            <el-input v-model="oneKeyDrawerForm.examPoint" class="input-num" placeholder="请输入" />
            <span class="f-ml5">分</span>
            <span class="f-cb">（及格 / 满分：60 / 100分）</span>
          </el-form-item>
          <el-form-item label="课程测验合格分：" prop="testPoint">
            <el-input v-model="oneKeyDrawerForm.testPoint" class="input-num" placeholder="请输入" />
            <span class="f-ml5">分</span>
            <span class="f-cb">（及格 / 满分：60 / 100分）</span>
          </el-form-item>
          <el-form-item>
            <span class="f-co">注：请设置合格成绩介于合格分和满分之间。</span>
          </el-form-item>
          <el-form-item class="m-btn-bar">
            <el-button @click="isOneKeyQualifiedDrawer = false">取消</el-button>
            <el-button type="primary" @click="confirmOneKey">确认</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { ElForm } from 'element-ui/types/form'
  import { Vue, Component, Ref } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    @Ref('oneKeyQualifyForm')
    oneKeyQualifyForm: ElForm

    // 考试合格分校验规则
    checkExamPoint = (rule: any, value: any, callback: (arg: Error) => void) => {
      if (!value) {
        return callback(new Error('考试合格分不能为空'))
      } else if (value < 60 || value > 100) {
        callback(new Error('请设置合格成绩介于合格分和满分之间'))
      } else {
        return
      }
    }
    // 课程测验合格分校验规则
    checkTestPoint = (rule: any, value: any, callback: (arg: Error) => void) => {
      if (!value) {
        return callback(new Error('课程测验合格分不能为空'))
      } else if (value < 60 || value > 100) {
        callback(new Error('请设置合格成绩介于合格分和满分之间'))
      } else {
        return
      }
    }
    checkQualifiedTime = (rule: any, value: any, callback: (arg: Error) => void) => {
      // const res = this.radioValue
      if (!value) {
        return callback(new Error('指定合格时间不能为空！'))
      } else {
        return
      }
    }
    oneKeyDrawerRules = {
      qualifiedTime: [{ required: true, validator: this.checkQualifiedTime, trigger: 'blur' }],
      examPoint: [{ required: true, validator: this.checkExamPoint, trigger: 'blur' }],
      testPoint: [{ required: true, validator: this.checkTestPoint, trigger: 'blur' }]
    }
    // 合格时间配置 默认选中系统时间
    radioValue = 2
    // 抽屉显隐
    isOneKeyQualifiedDrawer = false

    oneKeyDrawerForm = {
      qualifiedTime: '',
      examPoint: '',
      testPoint: ''
    }

    created() {
      // 按系统操作时间成功时间初始值
      this.oneKeyDrawerForm.qualifiedTime = '777-77-77'
    }

    radioValueChange(val: number) {
      this.radioValue = val
      //  TODO 默认选中系统时间时，获取系统时间给时间赋值
      if (val === 2) {
        // 从自定义时间切换回系统时间时，重新赋值
        this.oneKeyDrawerForm.qualifiedTime = '7777-77-77'
      } else if (val === 1) {
        this.oneKeyDrawerForm.qualifiedTime = ''
      } else {
        return
      }
    }

    // 确认一键合格
    confirmOneKey() {
      this.oneKeyQualifyForm.validate((valid: any) => {
        if (valid) {
          // TODO
        }
      })
    }
  }
</script>
