/*
 * @Description: 资讯管理-中控层
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-24 17:29:40
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-12 15:07:12
 */
import store from '@/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import MutationNewsFactory from './mutation/MutationNewsFactory'
import QueryNewsFactory from './query/QueryNewsFactory'

/**
 * @description 资讯中控层
 */
@Module({
  name: 'NewsModule',
  dynamic: true,
  namespaced: true,
  store
})
class NewsModule extends VuexModule {
  //  查询
  queryNewsFactory = new QueryNewsFactory()
  //  业务
  mutationNewsFactory = new MutationNewsFactory()
}

export default getModule(NewsModule)
