import { StudentCourseAppraiseResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { StudentInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { TechnologyLevelVo } from '@api/service/common/basic-data-dictionary/query/QueryTechnologyLevel'

class CourseEvaluateListDetail {
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 学员ID
   */
  studentId: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 学员名称
   */
  studentName: string
  /**
   * 评价编号
   */
  id: string
  /**
   * 课程评价值
   */
  courseScore: number
  /**
   * 教师评价值
   */
  teacherScore: number
  /**
   * 评价时间
   */
  createTime: string

  /**
   * 评价内容
   */
  contents: string

  /**
   * 学员技术等级名称
   */
  studentProfessionalLevelName: string

  static from(
    remoteResponse: StudentCourseAppraiseResponse,
    studentInfoResponse?: StudentInfoResponse,
    technologyLevelVo?: TechnologyLevelVo[]
  ) {
    const detail = new CourseEvaluateListDetail()
    detail.id = remoteResponse.courseAppraisalInfo.studentCourseAppraisalId
    detail.courseScore = parseFloat((remoteResponse.courseAppraisalInfo.courseAppraise / 100).toFixed(1))
    detail.teacherScore = parseFloat((remoteResponse.courseAppraisalInfo.teacherAppraise / 100).toFixed(1))
    detail.studentId = remoteResponse.courseAppraisalInfo.appraisalUserId
    // detail.studentNo = remoteResponse.studentNo
    detail.courseId = remoteResponse.course.courseId
    detail.createTime = remoteResponse.courseAppraisalInfo.appraisalTime
    detail.contents = remoteResponse.courseAppraisalInfo.content
    detail.studentName = studentInfoResponse?.userInfo?.userName
    detail.studentProfessionalLevelName =
      (technologyLevelVo?.length &&
        technologyLevelVo.find(item => item.code === studentInfoResponse?.userInfo?.professionalLevel)?.showName) ||
      ''

    return detail
  }
}

export default CourseEvaluateListDetail
