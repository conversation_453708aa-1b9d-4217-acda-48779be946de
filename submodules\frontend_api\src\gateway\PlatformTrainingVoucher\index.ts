import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformTrainingVoucher'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformTrainingVoucher'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举

// 类

/**
 * 用户培训券查询条件
<AUTHOR>
@since 2021/2/22
 */
export class UserTrainingVoucherRequest {
  /**
   * 身份证号
   */
  identityNumber?: string
  /**
   * 用户培训券状态（可选），默认0
<pre>
0 - 不限
1 - 可用
2 - 激活
3 - 已使用
4 - 已结算（补贴）
-5 - 已作废
-2 - 过期
</pre>
   */
  status: number
}

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 培训券信息
<AUTHOR>
@since 2021/2/22
 */
export class TrainingVoucherInfoResponse {
  /**
   * 培训券编码
   */
  couponCode: string
  /**
   * 身份证号
   */
  idCard: string
  /**
   * 用户姓名
   */
  name: string
  /**
   * 培训券名称
   */
  couponName: string
  /**
   * 用户培训券状态
<pre>
1 - 可用
2 - 激活
3 - 已使用
4 - 已结算（补贴）
-5 - 已作废
-2 - 已过期
</pre>
   */
  couponStatus: number
  /**
   * 用户培训券状态说明
   */
  couponStatusName: string
  /**
   * 发券机构（属地人社局）
   */
  publishOrgName: string
  /**
   * 区域编码（行政编码）
   */
  areaCode: string
  /**
   * 培训券面值（单位分）
   */
  amount: number
  /**
   * 当前券剩余余额（单位分）
   */
  nowAmount: number
  /**
   * 可用工种
   */
  job: string
  /**
   * 使用有效期（年月日时分）
   */
  endTime: string
  /**
   * 适用人群
   */
  personScope: Array<string>
  /**
   * 适用工种范围，工种名称数组
   */
  jobScope: Array<string>
  /**
   * 适用培训机构范围，机构信息数组
   */
  orgScope: Array<string>
  /**
   * 适用培训模式
<pre>
0-不限
1-线上
2-线上+线下
3-线下
</pre>
   */
  trainMode: number
  /**
   * 机构编码（未兑换时则为空）
   */
  educationCode: string
  /**
   * 培训机构名称（未兑换时则为空）
   */
  educationName: string
  /**
   * 使用模式（未兑换时则为空）
<pre>
1-自助使用
2-扫码使用
</pre>
   */
  useMode: number
  /**
   * 使用日期（未兑换时则为空）
   */
  useDate: string
  /**
   * 补贴时间（未兑换时则为空）
   */
  checkDate: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findUserTrainingVoucherPageList(
    params: { page: Page; request: UserTrainingVoucherRequest },
    query: DocumentNode = GraphqlImporter.findUserTrainingVoucherPageList,
    operation?: string
  ): Promise<Response<Array<TrainingVoucherInfoResponse>>> {
    return commonRequestApi<Array<TrainingVoucherInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
