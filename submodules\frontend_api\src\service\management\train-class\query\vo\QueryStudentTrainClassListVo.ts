import {
  LearningRegisterRequest,
  SchemeRequest,
  SchemeSkuPropertyRequest,
  StudentLearningRequest,
  StudentSchemeLearningRequest,
  UserRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

/**
 * @description 查询学员培训方案列表
 */
class QueryStudentTrainClassListVo {
  /**
   * 用户id
   */
  userId = ''

  /**
   * 年度
   */
  year = ''

  /**
   * 培训方案名称
   */
  schemeName = ''

  /**
   * 是否合格 true：是 false：否
   */
  assessResult: boolean = null
  /**
   * 是否查询已冻结订单 true：是 false：否
   */
  queryFreezed = false

  to(): StudentSchemeLearningRequest {
    const to = new StudentSchemeLearningRequest()
    to.student = new UserRequest()
    to.student.userIdList = this.userId ? [this.userId] : undefined
    to.scheme = new SchemeRequest()
    to.scheme.schemeName = this.schemeName ?? undefined
    to.scheme.skuProperty = new SchemeSkuPropertyRequest()
    to.scheme.skuProperty.year = this.year ? [this.year] : undefined
    if ((this.assessResult ?? '') !== '') {
      to.studentLearning = new StudentLearningRequest()
      to.studentLearning.trainingResultList = this.assessResult ? [1] : [0, -1]
    }
    to.learningRegister = new LearningRegisterRequest()
    to.learningRegister.status = this.queryFreezed ? [1, 2] : [1]
    return to
  }
}

export default QueryStudentTrainClassListVo
