import TakePlaceDetailVo from './vo/TakePlaceDetailVo'
import MsTradeQueryFrontGatewayTradeQueryBackstage from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

class QueryTakePlace {
  list: Array<any> = new Array<any>()

  async search() {
    const result = await MsTradeQueryFrontGatewayTradeQueryBackstage.getDeliveryChannelListInServicer()
    return result.data.filter(deliver => deliver.shippingMethod).map(TakePlaceDetailVo.from)
  }

  async queryDetail(id: string) {
    const result = await MsTradeQueryFrontGatewayTradeQueryBackstage.getDeliveryChannelInServicer(id)
    return TakePlaceDetailVo.from(result.data)
  }
}

export default QueryTakePlace
