/*
 * @Description: 集体对账业务
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:32:55
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-27 16:02:47
 */
import ExportGateway, {
  BatchOrderSortRequest,
  BatchReturnOrderSortRequest
} from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import CheckAccountParam from '../query/vo/CheckAccountParam'
import RefundCheckAccountParam from '../query/vo/RefundCheckAccountParam'
import fxnlQuery, {
  BatchReturnOrderSortRequest as BatchReturnOrderSortRequestv2
} from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import MutationCheckAccountBase from '@api/service/management/trade/batch/checkAccount/mutation/MutationCheckAccountBase'

export default class MutationCheckAccount extends MutationCheckAccountBase {
  /**
   * 集体报名对账导出
   */
  async listExport(checkAccountParam: CheckAccountParam, sortRequest?: Array<BatchOrderSortRequest>): Promise<boolean> {
    const request = CheckAccountParam.to(checkAccountParam)
    const { data } = await ExportGateway.exportBatchReconciliationInServicer({ request, sort: sortRequest })
    return data
  }
  /**
   * 集体报名对账导出（分销）
   */
  async listFxExport(
    checkAccountParam: CheckAccountParam,
    sortRequest?: Array<BatchOrderSortRequest>
  ): Promise<boolean> {
    const request = CheckAccountParam.to(checkAccountParam)
    const { data } = await fxnlQuery.exportBatchReconciliationInDistributor({ request, sort: sortRequest })
    return data
  }

  /**
   * 集体退款对账导出
   */
  async listReturnExport(
    checkAccountParam: RefundCheckAccountParam,
    sortRequest?: Array<BatchReturnOrderSortRequest>
  ): Promise<boolean> {
    const request = RefundCheckAccountParam.refurnTo(checkAccountParam)
    const { data } = await ExportGateway.exportBatchReturnReconciliationExcelInServicer({
      request,
      sort: sortRequest
    })
    return data
  }
  /**
   * 集体退款对账导出（分销）
   */
  async listRxReturnExport(
    checkAccountParam: RefundCheckAccountParam,
    sortRequest?: Array<BatchReturnOrderSortRequestv2>
  ): Promise<boolean> {
    const request = RefundCheckAccountParam.refurnTo(checkAccountParam)
    const { data } = await fxnlQuery.exportBatchReturnReconciliationExcelInDistributor({
      request,
      sort: sortRequest
    })
    return data
  }
}
