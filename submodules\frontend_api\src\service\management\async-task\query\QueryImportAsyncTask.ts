import MsAccountGateway, {
  PageStudentImportTaskRequest,
  StudentImportTaskResponse
} from '@api/ms-gateway/ms-account-v1'
import DataGateway, { ImportPrintTaskQueryRequest, ImportTaskQueryResponse } from '@api/ms-gateway/ms-certificate-v1'
import MsCourseResourceGateway, {
  CoursePackageImportTaskResponse,
  CoursewareImportTaskResponse,
  ExportCoursePackageParam,
  PageCoursePackageImportTaskRequest,
  PageCoursewareImportTaskRequest
} from '@api/ms-gateway/ms-course-resource-v1'
import MsExamquestionGateway, {
  BatchImportQuestionQueryRequest,
  ExportQuestionDataRequest,
  FindBatchImportQuestionByPageResponse
} from '@api/ms-gateway/ms-examquestion-v1'
import MsImportopenGateway, {
  TaskExecuteByPageResponse,
  TaskExecuteParamRequest
} from '@api/ms-gateway/ms-importopen-v1'
import MsOfflineinvoiceGateway, {
  QueryForImportInfoRequest,
  QueryImportInfoResponse
} from '@api/ms-gateway/ms-offlineinvoice-v1'
import MsLearningScheme, {
  LearningSchemeImportTaskResponse,
  LearningSchemeImportTaskResponsePage,
  PageLearningSchemeImportTaskRequest
} from '@api/ms-gateway/ms-learningscheme-v1'
import PlatformJxjyLearningResult, {
  ImportGradeResultTaskInfoResponse
} from '@api/platform-gateway/platform-jxjy-learning-result-v1'
import AccountGateway from '@api/platform-gateway/platform-account-v1'
import PlatformCertificate from '@api/platform-gateway/platform-certificate-v1'
import ObsReceive from '@api/service/common/obs/ObsReceive'
import { CategoryEnum } from '@api/service/management/async-task/enum/CateoryEnum'
import { Page } from '@hbfe/common'
import PlatformTrainingChannel, {
  ExportUpdateSchemeShowResultResponse,
  ImportSchemePortalShowTaskQueryRequest,
  UpdateSchemePortalResultTaskInfoResponse
} from '@api/platform-gateway/platform-training-channel-v1'

/**
 * 查询导入任务
 */

class QueryImportAsyncTask {
  /**
   * 查询分批课程导入
   * @param page
   * @param request
   */
  /*async pageCourseImportTaskQuery(page: Page, request: AllocateCourseTaskQueryRequest) {
    const response = await Course.pageCourseImportTaskQuery({ page, request })
    page.totalSize = response.data.totalSize
    page.totalPageSize = response.data.totalPageSize
    return response
  }*/
  /**
   * 下载分批课程导入数据
   */
  /*async exportCourseAllocateImportInfo(request: AllocateCourseExportRequest) {
    const response = await Course.exportCourseAllocateImportInfo(request)
    return response
  }*/

  /*
   * @description: 查询导入试题任务列表
   */
  async queryImportQuestionByPage(
    page: Page,
    params: BatchImportQuestionQueryRequest
  ): Promise<Array<FindBatchImportQuestionByPageResponse>> {
    const res = await MsExamquestionGateway.findBatchImportQuestionByPage({
      page: page,
      request: params
    })
    if (!res?.status?.isSuccess()) {
      console.error('获取导入试题列表请求失败！')
      return new Array<FindBatchImportQuestionByPageResponse>()
    }
    page.totalPageSize = res.data?.totalPageSize
    page.totalSize = res.data?.totalSize
    return res.data?.currentPageData
  }

  /**
   * @description: 查询导入试题任务所有数据
   * @param {string} mainTaskId 主任务id
   */
  async queryBatchExportAllChooseQuestionData(mainTaskId: string): Promise<string> {
    const parmas = new ExportQuestionDataRequest()
    parmas.mainTaskId = mainTaskId
    const res = await MsExamquestionGateway.batchExportAllChooseQuestionData(parmas)
    if (!res?.status?.isSuccess()) {
      console.error('查询导入试题任务所有数据请求失败！')
      return null
    }
    return res.data
  }

  /**
   * @description: 查询导入试题任务失败数据
   * @param {string} mainTaskId 主任务id
   */
  async querybatchExportFailChooseQuestionData(mainTaskId: string): Promise<string> {
    const parmas = new ExportQuestionDataRequest()
    parmas.mainTaskId = mainTaskId
    const res = await MsExamquestionGateway.batchExportFailChooseQuestionData(parmas)
    if (!res?.status?.isSuccess()) {
      console.error('查询导入试题任务失败数据请求失败！')
      return null
    }
    return res.data
  }

  /*
   * @description: 查询导入学员列表
   */
  async queryImportStudentByPage(
    page: Page,
    params: PageStudentImportTaskRequest
  ): Promise<Array<StudentImportTaskResponse>> {
    const res = await MsAccountGateway.pageStudentImportTask({
      page: page,
      request: params
    })
    if (!res?.status?.isSuccess()) {
      console.error('获取导入学员列表请求失败！')
      return new Array<StudentImportTaskResponse>()
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return res?.data?.currentPageData
  }

  /**
   * @description: 查询导入学员所有数据
   * @param {string} mainTaskId 主任务id
   */
  async doExportAllStudentResult(mainTaskId: string): Promise<string> {
    const res = await AccountGateway.exportAllStudentResult(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('获取导入学员所有数据请求失败！')
      return null
    }
    return res.data?.fileUrl
  }

  /**
   * @description: 查询导入学员失败数据
   * @param {string} mainTaskId 主任务id
   */
  async doExportErrorStudentResult(mainTaskId: string): Promise<string> {
    const res = await AccountGateway.exportErrorStudentResult(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('获取导入学员失败数据请求失败！')
      return null
    }
    return res.data?.fileUrl
  }

  /*
   * @description: 查询导入学员并开班列表
   */
  async queryQueryImportList(params: TaskExecuteParamRequest, page: Page): Promise<Array<TaskExecuteByPageResponse>> {
    const res = await MsImportopenGateway.findTaskExecuteWithServicerResponseByPage({
      param: params,
      page: page
    })
    if (!res?.status?.isSuccess()) {
      console.error('请求导入学员并开班列表失败！')
      return new Array<TaskExecuteByPageResponse>()
    }
    page.totalPageSize = res.data?.totalPageSize
    page.totalSize = res.data?.totalSize
    return res.data?.currentPageData
  }

  /*
   * @description: 专题管理员-查询导入学员并开班列表
   */
  async queryQueryImportListByThemeManeger(
    params: TaskExecuteParamRequest,
    page: Page
  ): Promise<Array<TaskExecuteByPageResponse>> {
    const res = await MsImportopenGateway.findTaskExecuteWithSelfResponseByPage({
      param: params,
      page: page
    })
    if (!res?.status?.isSuccess()) {
      console.error('请求导入学员并开班列表失败！')
      return new Array<TaskExecuteByPageResponse>()
    }
    page.totalPageSize = res.data?.totalPageSize
    page.totalSize = res.data?.totalSize
    return res.data?.currentPageData
  }

  /**
   * @description: 查询导入学员并开班列表 所有数据
   */
  async queryExportStudentAllData(mainTaskId: string): Promise<string> {
    const res = await MsImportopenGateway.exportExcelAllData(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('查询导入学员并开班列表所有数据请求失败!')
      return null
    }
    return res?.data
  }

  /**
   * @description: 查询导入学员并开班列表 失败数据
   * @param {string} batchNo 批次号
   * @param {boolean} onlyFail true则查询失败数据
   */
  async queryExportStudentFailData(batchNo: string): Promise<string> {
    const res = await MsImportopenGateway.exportExcel({
      batchNo,
      onlyFail: true
    })
    if (!res?.status?.isSuccess()) {
      console.error('查询导入学员并开班列表失败数据请求失败!')
      return null
    }
    return res?.data
  }

  /*
   * @description: 查询导入课程包任务列表
   */
  async queryImportCoursebagByPage(
    page: Page,
    params: PageCoursePackageImportTaskRequest
  ): Promise<Array<CoursePackageImportTaskResponse>> {
    const res = await MsCourseResourceGateway.pageCoursePackageImportTask({
      page: page,
      request: params
    })
    if (!res?.status?.isSuccess()) {
      console.error('获取导入学员列表请求失败！')
      return new Array<CoursePackageImportTaskResponse>()
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return res?.data?.currentPageData
  }

  /**
   * @description: 查询导入课程包所有数据
   * @param {string} mainTaskId 主任务id
   */
  async queryImportCoursebagAllData(mainTaskId: string): Promise<string> {
    const res = await MsCourseResourceGateway.exportAllCoursePackageImportResult(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('获取导入课程包所有数据请求失败！')
      return null
    }
    return res.data?.fileUrl
  }

  /**
   * @description: 查询导入课程包所有数据
   * @param {string} mainTaskId 主任务id
   */
  async queryImportCoursebagFailData(mainTaskId: string): Promise<string> {
    const res = await MsCourseResourceGateway.exportErrorCoursePackageImportResult(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('获取导入课程包失败数据请求失败！')
      return null
    }
    return res.data?.fileUrl
  }

  /**
   * @description: 查询导入个人报名增值税电子普通发票（线下开票）
   */
  async queryImportElectronicInvoice(
    params: QueryForImportInfoRequest,
    page: Page
  ): Promise<Array<QueryImportInfoResponse>> {
    const res = await MsOfflineinvoiceGateway.queryForImportOfflineInvoiceWithServiceId({
      request: params,
      page
    })
    if (!res?.status?.isSuccess()) {
      console.error('获取导入个人报名增值税电子普通发票列表请求失败！')
      return new Array<QueryImportInfoResponse>()
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return res?.data?.currentPageData
  }

  /**
   * @description: 查询导入个人报名专用纸质发票
   */
  async queryImportSpecialPaperOfflineInvoice(
    params: QueryForImportInfoRequest,
    page: Page
  ): Promise<Array<QueryImportInfoResponse>> {
    const res = await MsOfflineinvoiceGateway.queryForImportSpecialPaperOfflineInvoiceWithServiceId({
      request: params,
      page
    })
    if (!res?.status?.isSuccess()) {
      console.error('获取导入个人报名专用普通发票列表请求失败！')
      return new Array<QueryImportInfoResponse>()
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return res?.data?.currentPageData
  }

  /**
   * @description: 查询导入个人报名专用发票配送
   */
  async queryImportDeliverySpecialInvoice(
    params: QueryForImportInfoRequest,
    page: Page
  ): Promise<Array<QueryImportInfoResponse>> {
    const res = await MsOfflineinvoiceGateway.queryForImportSpecialInvoiceDeliveryWithServiceId({
      request: params,
      page
    })
    if (!res?.status?.isSuccess()) {
      console.error('获取导入个人报名专用发票配送列表请求失败！')
      return new Array<QueryImportInfoResponse>()
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return res?.data?.currentPageData
  }

  /**
   * @description: 查询导入个人报名增值税电子专用发票（线下开票）
   */
  async queryForImportOfflineSpecialElectronicInvoice(
    params: QueryForImportInfoRequest,
    page: Page
  ): Promise<Array<QueryImportInfoResponse>> {
    const res = await MsOfflineinvoiceGateway.queryForImportOfflineSpecialElectronicInvoice({
      request: params,
      page
    })
    if (!res?.status?.isSuccess()) {
      console.error('获取导入个人报名增值税电子专用发票（线下开票）列表请求失败！')
      return new Array<QueryImportInfoResponse>()
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return res?.data?.currentPageData
  }

  /**
   * @description: 查询导入集体报名增值税电子专用发票（线下开票）
   */
  async queryCommonQueryOfflineInvoiceImportResult(
    params: QueryForImportInfoRequest,
    page: Page
  ): Promise<Array<QueryImportInfoResponse>> {
    params.category = CategoryEnum.BATCH_OFFLINE_SPECIAL_ELECTRONIC_INVOICE_IMPORT
    const res = await MsOfflineinvoiceGateway.commonQueryOfflineInvoiceImportResult({
      request: params,
      page
    })
    if (!res?.status?.isSuccess()) {
      console.error('获取集体报名增值税电子专用发票（线下开票）列表请求失败！')
      return new Array<QueryImportInfoResponse>()
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return res?.data?.currentPageData
  }

  /**
   * @description: 查询外链课件导入任务
   */
  async queryOuterCoursewareImportResult(
    params: PageCoursewareImportTaskRequest,
    page: Page
  ): Promise<Array<CoursewareImportTaskResponse>> {
    const res = await MsCourseResourceGateway.pageCoursewareImportTask({ request: params, page })
    if (!res?.status?.isSuccess()) {
      console.error('查询外链课件导入任务请求失败！')
      return new Array<CoursewareImportTaskResponse>()
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return res?.data?.currentPageData || new Array<CoursewareImportTaskResponse>()
  }

  /**
   * @description: 查询外链课件导入列表 所有数据
   * @param {string} mainTaskId 主任务id
   */
  async queryOuterCoursewareAllData(mainTaskId: string): Promise<string> {
    const request = new ExportCoursePackageParam()
    request.mainTaskId = mainTaskId
    const res = await MsCourseResourceGateway.exportCoursewareImportResult(request)
    if (!res?.status?.isSuccess()) {
      console.error('查询外链课件导入列表所有数据请求失败!')
      return null
    }
    if (res.data) {
      const obsReceive = new ObsReceive()
      return (await obsReceive.getResolveFilePath(res.data)).path
    }
    return res.data
  }

  /**
   * @description: 查询外链课件导入列表 失败数据
   * @param {string} mainTaskId 主任务id
   */
  async queryOuterCoursewareFailData(mainTaskId: string): Promise<string> {
    const request = new ExportCoursePackageParam()
    request.mainTaskId = mainTaskId
    request.dataType = 0
    const res = await MsCourseResourceGateway.exportCoursewareImportResult(request)
    if (!res?.status?.isSuccess()) {
      console.error('查询外链课件导入列表失败数据请求失败!')
      return null
    }
    if (res.data) {
      const obsReceive = new ObsReceive()
      return (await obsReceive.getResolveFilePath(res.data)).path
    }
    return res.data
  }

  /**
   * @description: 查询导入集体报名增值税电子普通发票（线下开票）
   */
  async queryImportBatchPayOfflineInvoice(
    params: QueryForImportInfoRequest,
    page: Page
  ): Promise<Array<QueryImportInfoResponse>> {
    const res = await MsOfflineinvoiceGateway.queryForImportBatchPayOfflineInvoice({
      request: params,
      page
    })
    if (!res?.status?.isSuccess()) {
      console.error('获取导入集体报名增值税电子普通发票列表请求失败！')
      return new Array<QueryImportInfoResponse>()
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return res?.data?.currentPageData
  }

  /**
   * @description: 查询导入集体报名增值税专用发票
   */
  async queryImportBatchPaySpecialPaperOfflineInvoice(
    params: QueryForImportInfoRequest,
    page: Page
  ): Promise<Array<QueryImportInfoResponse>> {
    const res = await MsOfflineinvoiceGateway.queryForImportBatchPaySpecialPaperOfflineInvoice({
      request: params,
      page
    })
    if (!res?.status?.isSuccess()) {
      console.error('获取导入集体报名增值税专用发票列表请求失败！')
      return new Array<QueryImportInfoResponse>()
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return res?.data?.currentPageData
  }

  /**
   * @description: 查询导入集体报名发票配送
   */
  async queryImportDeliveryBatchPaySpecialInvoice(
    params: QueryForImportInfoRequest,
    page: Page
  ): Promise<Array<QueryImportInfoResponse>> {
    const res = await MsOfflineinvoiceGateway.queryForImportBatchPaySpecialInvoiceDelivery({
      request: params,
      page
    })
    if (!res?.status?.isSuccess()) {
      console.error('获取导入集体报名增值税专用配送列表请求失败！')
      return new Array<QueryImportInfoResponse>()
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return res?.data?.currentPageData
  }

  /**
   * @description: 查询导入发票所有数据
   * @param {string} mainTaskId 主任务id
   */
  async queryInvoiceImportAllData(mainTaskId: string): Promise<string> {
    const res = await MsOfflineinvoiceGateway.getAllImportData(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('查询导入发票所有数据请求失败！')
      return null
    }
    return res?.data
  }

  /**
   * @description: 查询导入发票失败数据
   * @param {string} mainTaskId 主任务id
   */
  async queryInvoiceImportFailData(mainTaskId: string): Promise<string> {
    const res = await MsOfflineinvoiceGateway.getImportFailedData(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('查询导入发票失败数据请求失败！')
      return null
    }
    return res?.data
  }

  /**
   * 查询按人员名单打印证明列表
   * @param page
   * @returns {Promise<QueryExportRecordsVo[]>}
   */
  async queryExportList(page: Page, param: ImportPrintTaskQueryRequest) {
    param.category = 'BATCH_IMPORT_STUDENTS_PRINT_LIST'
    const res = await DataGateway.findImportPrintTaskExecuteResponsePage({ page, request: param })

    if (res.status.isSuccess()) {
      page.totalSize = res.data?.totalSize
      page.totalPageSize = res.data?.totalPageSize
      return res.data.currentPageData
    }
    return [] as Array<ImportTaskQueryResponse>
  }

  /**
   * @description: 查询导入学员所有数据
   * @param {string} mainTaskId 主任务id
   */
  async findImportPrintData(mainTaskId: string): Promise<string> {
    const res = await PlatformCertificate.findImportPrintData(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('获取导入学员所有数据请求失败！')
      return null
    }
    return res.data
  }

  /**
   * @description: 查询导入学员失败数据
   * @param {string} mainTaskId 主任务id
   */
  async findImportPrintFailData(mainTaskId: string): Promise<string> {
    const res = await PlatformCertificate.findImportPrintFailData(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('获取导入学员失败数据请求失败！')
      return null
    }
    return res.data
  }

  /**
   * @description: 查询批量更新方案导入任务
   */
  async queryBatchUpdateSchemeImportResult(
    params: PageLearningSchemeImportTaskRequest,
    page: Page
  ): Promise<Array<CoursewareImportTaskResponse>> {
    const res = await MsLearningScheme.pageImportLearningSchemeImportTask({ request: params, page })
    if (!res?.status?.isSuccess()) {
      console.error('查询批量更新方案导入任务请求失败！')
      return new Array<LearningSchemeImportTaskResponse>()
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return res?.data?.currentPageData || new Array<LearningSchemeImportTaskResponse>()
  }

  /**
   * @description: 查询批量更新方案导入列表 所有数据
   * @param {string} mainTaskId 主任务id
   */
  async queryBatchUpdateSchemeAllData(mainTaskId: string): Promise<string> {
    const res = await MsLearningScheme.batchUpdateLearningSchemeExport(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('查询批量更新方案导入列表所有数据请求失败!')
      return null
    }
    return res.data?.fileUrl
  }

  /**
   * @description: 查询批量更新方案导入列表 失败数据
   * @param {string} mainTaskId 主任务id
   */
  async queryBatchUpdateSchemeFailData(mainTaskId: string): Promise<string> {
    const res = await MsLearningScheme.batchUpdateLearningSchemeExportFail(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('查询批量更新方案导入列表失败数据请求失败!')
      return null
    }
    return res.data?.fileUrl
  }

  /**
   * @description: 查询结业成果导入任务
   */
  async queryGraduationResultImportResult(
    params: PageLearningSchemeImportTaskRequest,
    page: Page
  ): Promise<Array<CoursewareImportTaskResponse>> {
    const res = await PlatformJxjyLearningResult.pageImportGradeResultTaskInfo({ request: params, page })
    if (!res?.status?.isSuccess()) {
      console.error('查询结业成果导入任务请求失败！')
      return new Array<ImportGradeResultTaskInfoResponse>()
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return res?.data?.currentPageData || new Array<ImportGradeResultTaskInfoResponse>()
  }

  /**
   * @description: 查询结业成果导入列表 所有数据
   * @param {string} mainTaskId 主任务id
   */
  async queryGraduationResultAllData(mainTaskId: string) {
    const res = await PlatformJxjyLearningResult.exportTaskExcelAllResultWithName(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('查询结业成果导入列表所有数据请求失败!')
      return null
    }
    return res.data
  }

  /**
   * @description: 查询结业成果导入列表 失败数据
   * @param {string} mainTaskId 主任务id
   */
  async queryGraduationResultFailData(mainTaskId: string) {
    const res = await PlatformJxjyLearningResult.exportTaskExcelFailedResultWithName(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('查询结业成果导入列表失败数据请求失败!')
      return null
    }
    return res.data
  }

  /**
   * 查询批量更新方案展示 全部数据
   * @param mainTaskId 主任务id
   */
  async queryBatchUpdatePlanShowAllData(mainTaskId: string): Promise<ExportUpdateSchemeShowResultResponse> {
    const res = await PlatformTrainingChannel.exportAllUpdateSchemeShowResult(mainTaskId)

    return res?.data || new ExportUpdateSchemeShowResultResponse()
  }

  /**
   * 查询批量更新方案展示 失败数据
   * @param mainTaskId 主任务id
   */
  async queryBatchUpdatePlanShowFailData(mainTaskId: string): Promise<ExportUpdateSchemeShowResultResponse> {
    const res = await PlatformTrainingChannel.exportFailUpdateSchemeShowResult(mainTaskId)

    return res?.data || new ExportUpdateSchemeShowResultResponse()
  }

  /**
   * 查询批量更新方案展示任务列表
   */
  async queryBatchUpdatePlanShowTaskList(page: Page, param: ImportSchemePortalShowTaskQueryRequest) {
    param.caetgory = 'UPDATE_SCHEME_SHOW_IMPORT'
    const res = await PlatformTrainingChannel.pageUpdateSchemePortalResultTaskInfo({
      page,
      request: param
    })

    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize

    return res?.data?.currentPageData || new Array<UpdateSchemePortalResultTaskInfoResponse>()
  }

  // todo 后续看情况调整
  // async queryImportAsyncTaskListByType(taskType: number) {
  //   const ImportAsyncTaskTypes = {
  //     [AsyncTaskTypeEnum.IMPORT_STUDENT_CLASS]: async () => {
  //       return await this.queryQueryImportList()
  //     },
  //     [AsyncTaskTypeEnum.IMPORT_STUDENT]: async () => {
  //       return await this.queryImportStudentByPage()
  //     },
  //     [AsyncTaskTypeEnum.COURSEBAG_IMPORT]: async () => {
  //       return await this.queryImportCoursebagByPage()
  //     },
  //     [AsyncTaskTypeEnum.QUESTION_IMPORT]: async () => {
  //       return await this.queryImportQuestionByPage()
  //     },
  //     [AsyncTaskTypeEnum.SINGLE_ELEC_INVOICE]: async () => {
  //       return await this.queryImportElectronicInvoice()
  //     },
  //     [AsyncTaskTypeEnum.SINGLE_SPECIAL_INVOICE]: async () => {
  //       return await this.queryImportSpecialPaperOfflineInvoice()
  //     },
  //     [AsyncTaskTypeEnum.SINGLE_DELIVERY_INVOICE]: async () => {
  //       return await this.queryImportDeliverySpecialInvoice()
  //     },
  //     [AsyncTaskTypeEnum.COLLECTIVE_ELEC_INVOICE]: async () => {
  //       return await this.queryImportBatchPayOfflineInvoice()
  //     },
  //     [AsyncTaskTypeEnum.COLLECTIVE_SPECIAL_INVOICE]: async () => {
  //       return await this.queryImportBatchPaySpecialPaperOfflineInvoice()
  //     },
  //     [AsyncTaskTypeEnum.COLLECTIVE_DELIVERY_INVOICE]: async () => {
  //       return await this.queryImportDeliveryBatchPaySpecialInvoice()
  //     }
  //   }
  //   return await ImportAsyncTaskTypes[taskType]()
  // }
}

export default QueryImportAsyncTask
