<template>
  <div class="f-p15" v-if="$hasPermission('H5Set')" desc="移动端" actions="created">
    <el-card shadow="never" class="m-card f-mb15">
      <el-form ref="form" label-width="auto" class="m-form">
        <el-form-item label="提供服务号：">
          <template v-if="$hasPermission('editH5')" desc="移动端（编辑）" actions="changeIsProvideServiceAccount">
            <div slot="label">
              <span class="f-vm">提供服务号</span>
              <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                <i
                  class="el-icon-question m-tooltip-icon f-co f-mlr5"
                  style="position: relative; overflow: hidden; z-index: 100"
                ></i>
                <div slot="content">
                  <p>1. 注册微信服务号</p>
                  <p>2. 提供微信开发者和微信服务号帐号信息</p>
                  <p class="f-mt5">微信开放平台：（帐号、密码、绑定人）</p>
                  <p>服务号：（服务号名称、服务号帐号、密码、绑定人姓名）</p>
                </div>
              </el-tooltip>
              <span>：</span>
            </div>
            <el-radio-group
              v-model="queryPortal.h5PortalInfo.isProvideServiceAccount"
              @change="changeIsProvideServiceAccount"
            >
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </template>
        </el-form-item>
      </el-form>
      <div class="tab-right" style="right: 20px; top: 35px">
        <el-button
          type="primary"
          size="medium"
          class="f-mr15"
          @click="preview"
          v-if="$hasPermission('h5Preview')"
          desc="h5预览"
          actions="preview"
        >
          <i class="hb-iconfont icon-complelearn f-mr5"></i>预览
        </el-button>
      </div>
      <template v-if="$hasPermission('editH5')" desc="移动端（编辑）" actions="addRotation">
        <el-button type="primary" icon="el-icon-plus" class="f-mb20" @click="addRotation">添加轮播图</el-button>
      </template>
      <!--表格-->
      <el-table stripe :data="h5BannerList" ref="dragWebTable" max-height="500px" class="m-table">
        <el-table-column :key="flag" type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="排序" min-width="80" align="center">
          <template v-if="$hasPermission('editH5')" desc="移动端（编辑）" actions="dragElement"
            ><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i
          ></template>
        </el-table-column>
        <el-table-column label="轮播图" min-width="300">
          <template slot-scope="scope">
            <el-image class="h5-banner" :src="scope.row.url" :preview-src-list="[scope.row.url]" />
          </template>
        </el-table-column>
        <el-table-column label="链接地址" min-width="280">
          <template slot-scope="scope">{{ scope.row.link || '-' }}</template>
        </el-table-column>
        <el-table-column label="创建时间" min-width="180">
          <template slot-scope="scope">{{ scope.row.createdTime || '-' }}</template>
        </el-table-column>
        <el-table-column label="操作" width="160" align="center" fixed="right">
          <template
            slot-scope="scope"
            v-if="$hasPermission('editH5')"
            desc="移动端（编辑）"
            actions="editRotation,deleteItem"
          >
            <el-button type="text" size="mini" @click="editRotation(scope.row, scope.$index)">修改</el-button>
            <el-button type="text" size="mini" @click="deleteItem(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-drawer title="添加轮播图" :visible.sync="addRotationDialog" size="1000px" custom-class="m-drawer">
        <div class="drawer-bd">
          <el-form ref="form" label-width="auto" class="m-form f-mt20">
            <el-form-item label="门户轮播图：">
              <rotation-image-upload-new1
                :dialogStyleOpation="{
                  width: '800px',
                  height: '400px'
                }"
                ref="uploadRef"
                :ratioArr="[`${H5TemplateModuleObj.bannerSize[0]}:${H5TemplateModuleObj.bannerSize[1]}`]"
                :initWidth="H5TemplateModuleObj.bannerSize[0]"
                v-model="pictureUrl"
                @callback="callback"
                :link="link"
                :isEdit="isEdit"
                :has-preview="false"
                :shortText="
                  '移动端首页轮播图建议尺寸 ：' +
                  H5TemplateModuleObj.bannerSize[0] +
                  'px * ' +
                  H5TemplateModuleObj.bannerSize[1] +
                  'px'
                "
                @linkChange="linkChange"
                :mode="`${H5TemplateModuleObj.bannerSize[0]}px ${H5TemplateModuleObj.bannerSize[1]}px`"
              ></rotation-image-upload-new1>
            </el-form-item>

            <el-form-item class="m-btn-bar">
              <el-button @click="cancelSave">取消</el-button>
              <el-button type="primary" @click="save">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-drawer>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Watch, Prop } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import Sortable from 'sortablejs'
  import { ElTable } from 'element-ui/types/table'
  import rotationImageUploadNew1 from '@hbfe/jxjy-admin-platform/src/components/rotation-image-upload-new1.vue'
  import BannerVo from '@api/service/common/online-school-config/vo/BannerVo'
  import TemplateModule from '@api/service/common/template-school/TemplateModule'
  import TemplateItem from '@api/service/common/template-school/TemplateItem'
  @Component({
    components: {
      rotationImageUploadNew1
    }
  })
  export default class extends Vue {
    @Ref('uploadRef') uploadRef: any
    @Ref('dragWebTable')
    dragWebTable: any
    page: UiPage
    query: Query = new Query()
    //添加轮播图标识
    addRotationDialog = false
    //查询请求
    queryBanner = OnlineSchoolConfigModule.queryBanner
    h5BannerList: Array<BannerVo> = new Array<BannerVo>()
    pictureUrl = ''

    //业务请求

    mutationBanner = OnlineSchoolConfigModule.mutationBanner
    saveParams: any[] = new Array<any>()
    editIndex = 0
    //是否修改
    isEdit = false
    link = ''
    //门户信息配置查询
    queryPortal = OnlineSchoolConfigModule.queryPortal
    //门户信息配置业务
    mutationPortal = OnlineSchoolConfigModule.mutationPortal
    flag = 1
    @Prop({
      required: true,
      default: () => {
        return new TemplateItem()
      }
    })
    H5TemplateModuleObj: TemplateItem
    @Watch('pictureUrl', {
      deep: true,
      immediate: true
    })
    pictureUrlChange(val: any) {
      if (val) {
        console.log(val, '门户轮播图')

        // this.getRotationList.push(val)
        // this.uiPictureList.push(new UIPicture())
      }
    }
    addRotation() {
      this.pictureUrl = ''
      this.link = ''
      if (this.isEdit) {
        this.uploadRef.reset()
      }
      this.isEdit = false
      this.addRotationDialog = true
    }
    editRotation(item: BannerVo, idx: number) {
      this.editIndex = idx
      const index = item.url.indexOf('/mfs/')
      this.pictureUrl = item.url.slice(index)
      this.link = item.link
      this.isEdit = true
      this.addRotationDialog = true
    }
    callback(val: any) {
      console.log(val, '传出来值的变化')
      this.link = val[0]?.address
      this.saveParams = val
    }
    async save() {
      this.saveParams.map((item, index) => {
        const i = new BannerVo()
        i.sort = this.h5BannerList.length + index + 1
        i.url = item.url
        i.link = item.address ? item.address : ''
        if (!this.isEdit) {
          this.h5BannerList.push(i)
        } else {
          i.sort = this.h5BannerList[this.editIndex]?.sort
          this.h5BannerList.splice(this.editIndex, 1, i)
        }
      })

      const res = await this.mutationBanner.doSaveH5Banner()
      if (res.isSuccess()) {
        this.$message.success('保存成功')
        this.addRotationDialog = false
        for (let i = 0; i <= 2; i++) {
          setTimeout(async () => {
            await this.getPortalConfig()
            await this.doQueryPage()
          }, i * 1000)
        }
      }
    }
    async deleteItem(index: number) {
      this.h5BannerList.splice(index, 1)
      const res = await this.mutationBanner.doSaveH5Banner()
      if (res.isSuccess()) {
        this.$message.success('删除成功')
      } else {
        this.$message.error('删除失败')
      }
    }
    async doQueryPage() {
      this.query.loading = true
      try {
        // const res =
        const res = await this.queryBanner.queryH5BannerList()
        if (res.isSuccess()) {
          this.h5BannerList = this.queryBanner.h5BannerList
        }
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        this.query.loading = false
      }
    }
    dragElement(table: ElTable) {
      const el = table.$el.querySelector('.el-table__body-wrapper > table > tbody') as HTMLElement
      return new Sortable(el, {
        ghostClass: 'blue-background-class',
        handle: '.hb-iconfont',
        onEnd: ({ newIndex, oldIndex }) => {
          const curRow = this.h5BannerList.splice(oldIndex, 1)[0]
          this.h5BannerList.splice(newIndex, 0, curRow)
          let newArray = this.h5BannerList.slice(0)
          this.h5BannerList = []
          this.$nextTick(async () => {
            newArray = newArray.map((res, index) => {
              res.sort = index + 1
              return res
            })
            this.h5BannerList = newArray
            this.flag += 1
            await this.mutationBanner.doSaveH5Banner()
          })
        }
      })
    }
    linkChange() {
      this.link = ''
    }
    cancelSave() {
      this.$confirm('提示', {
        message: '确定要放弃编辑吗？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.addRotationDialog = false
          // this.uploadRef.fileList = new Array<any>()
        })
        .catch(() => {
          console.log('点个der...')
        })
    }
    async preview() {
      await this.getPreviewConfigure()
      switch (this.H5TemplateModuleObj.id) {
        case 'TestTemplateId-1':
          // 旧数据
          window.open(window.location.origin + '/h5/#/platforms/h5/tab/preview', '_blank')
          // window.open('http://fjjsjxjy.dev.59iedu.com:8081' + '/preview2', '_blank')
          break
        case 'TestH5TemplateId-1':
          window.open(window.location.origin + '/h5/#/platforms/h5/tab/preview', '_blank')
          break
        case 'js-h5-001':
          window.open(window.location.origin + '/h5/#/platforms/h5/tab/preview2', '_blank')
          break
        case 'rs-h5-001':
          window.open(window.location.origin + '/h5/#/platforms/h5/tab/preview2', '_blank')
          break
        default:
          break
      }
      // window.open(window.location.origin + '/h5/#/platforms/h5/tab/preview', '_blank')
    }
    async getPreviewConfigure() {
      await OnlineSchoolConfigModule.mutationPreview.doWebPreview()
    }
    //获取门户信息配置
    async getPortalConfig() {
      try {
        await this.queryPortal.queryDetail()
      } catch (e) {
        console.log(e, '获取提供服务号信息失败')
      } finally {
        console.log('获取门户信息配置成功')
        // this.queryPortal.h5PortalInfo.isProvideServiceAccount
      }
    }
    async changeIsProvideServiceAccount() {
      try {
        // this.queryPortal.h5PortalInfo.isProvideServiceAccount
        await this.mutationPortal.doSave(2)
      } catch (e) {
        console.log(e, '基础数据调整失败')
      }
    }
    async created() {
      // await this.getPortalConfig()
      await this.doQueryPage()
      this.dragElement(this.dragWebTable)
    }
  }
</script>
