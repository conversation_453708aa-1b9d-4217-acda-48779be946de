import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/diff-ms-data-export-front-gateway-DataExportBackstage'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'diff-ms-data-export-front-gateway-DataExportBackstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum AuditType {
  AUTO_AUDIT = 'AUTO_AUDIT',
  MANUAL_AUDIT = 'MANUAL_AUDIT'
}
export enum ExperienceType {
  SCHEME = 'SCHEME',
  COURSE = 'COURSE'
}
export enum ParticipateType {
  SUBMIT_FILE = 'SUBMIT_FILE',
  EDIT_ONLINE = 'EDIT_ONLINE'
}
export enum StudentLearningExperienceStatus {
  SUBMITING = 'SUBMITING',
  SUBMITTED = 'SUBMITTED',
  PASS = 'PASS',
  RETURNED = 'RETURNED'
}
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum StudentSchemeLearningSortField {
  REGISTER_TIME = 'REGISTER_TIME',
  SCHEME_YEAR = 'SCHEME_YEAR'
}

// 类

export class OwnerInfoModel {
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  unitId?: string
  servicerId?: string
}

export class ObsFileMetaData {
  bizType?: string
  owner?: string
  sign?: string
}

export class DateScopeRequest1 {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

export class StudentSchemeLearningSortRequest {
  field?: StudentSchemeLearningSortField
  policy?: SortPolicy
}

export class ConnectManageSystemRequest1 {
  syncStatus?: number
}

export class DataAnalysisRequest1 {
  trainingResultPeriod?: DoubleScopeRequest
  requirePeriod?: DoubleScopeRequest
  acquiredPeriod?: DoubleScopeRequest
}

export class ExtendedInfoRequest1 {
  whetherToPrint?: boolean
  applyCompanyCode?: string
  policyTrainingSchemeId?: string
  policyTrainingSchemeName?: string
}

export class LearningRegisterRequest1 {
  registerType?: number
  sourceType?: string
  sourceId?: string
  status?: Array<number>
  registerTime?: DateScopeRequest1
  saleChannels?: Array<number>
  orderNoList?: Array<string>
  subOrderNoList?: Array<string>
  batchOrderNoList?: Array<string>
  distributorId?: string
  portalId?: string
}

export class RegionRequest1 {
  province?: string
  city?: string
  county?: string
}

export class StudentLearningRequest1 {
  trainingResultList?: Array<number>
  trainingResultTime?: DateScopeRequest1
  notLearningTypeList?: Array<number>
  courseScheduleStatus?: number
  examAssessResultList?: Array<number>
}

export class RegionSkuPropertyRequest1 {
  province?: string
  city?: string
  county?: string
}

export class RegionSkuPropertySearchRequest1 {
  region?: Array<RegionSkuPropertyRequest1>
  regionSearchType?: number
}

export class SchemeRequest1 {
  schemeId?: string
  schemeIdList?: Array<string>
  schemeType?: string
  schemeName?: string
  skuProperty?: SchemeSkuPropertyRequest1
}

export class SchemeSkuPropertyRequest1 {
  year?: Array<string>
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest1
  industry?: Array<string>
  subjectType?: Array<string>
  trainingCategory?: Array<string>
  trainingProfessional?: Array<string>
  technicalGrade?: Array<string>
  positionCategory?: Array<string>
  trainingObject?: Array<string>
  jobLevel?: Array<string>
  jobCategory?: Array<string>
  subject?: Array<string>
  grade?: Array<string>
  learningPhase?: Array<string>
  discipline?: Array<string>
  qualificationCategory?: Array<string>
  trainingWay?: Array<string>
  trainingInstitution?: Array<string>
  mainAdditionalItem?: Array<string>
}

export class UserInfoRequest {
  name?: string
  idCard?: string
  phoneNumber?: string
}

export class UserPropertyRequest1 {
  regionList?: Array<RegionRequest1>
  companyName?: string
  payOrderRegionList?: Array<RegionRequest1>
}

export class UserRequest1 {
  userIdList?: Array<string>
  accountIdList?: Array<string>
  userProperty?: UserPropertyRequest1
}

export class AHJSBDYStudentSchemeLearningInOnlineRequest {
  /**
   * 资源供应商
   */
  resourceSupplier?: string
  /**
   * 报名点机构
   */
  registerOrg?: string
  studentNoList?: Array<string>
  student?: UserRequest1
  learningRegister?: LearningRegisterRequest1
  scheme?: SchemeRequest1
  studentLearning?: StudentLearningRequest1
  dataAnalysis?: DataAnalysisRequest1
  connectManageSystem?: ConnectManageSystemRequest1
  extendedInfo?: ExtendedInfoRequest1
  openPrintTemplate?: boolean
  saleChannels?: Array<number>
  trainingChannelName?: string
  trainingChannelId?: string
  notDistributionPortal?: boolean
  trainingType?: string
  issueId?: string
}

/**
 * 试题查询条件
 */
export class QuestionRequest {
  /**
   * 试题ID集合
   */
  questionIdList?: Array<string>
  /**
   * 题库ID集合
   */
  libraryIdList?: Array<string>
  /**
   * 关联课程ID集合
   */
  relateCourseIds?: Array<string>
  /**
   * 试题题目
   */
  topic?: string
  /**
   * 试题类型（1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题）
   */
  questionType?: number
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest
  /**
   * 是否启用
   */
  isEnabled?: boolean
  jobName?: string
  metaData?: ObsFileMetaData
}

/**
 * 功能描述：时间范围查询条件
@Author： wtl
@Date： 2022/1/25 15:30
 */
export class DateScopeRequest {
  /**
   * 开始时间
查询大于等于开始时间的结果
   */
  beginTime?: string
  /**
   * 结束时间
查询小于等于结束时间的结果
   */
  endTime?: string
}

/**
 * 地区学习统计查询条件
<AUTHOR>
@version 1.0
@date 2022/1/17 14:17
 */
export class LearningReportFormsRequest {
  /**
   * 是否统计暂无地区的学员班级
   */
  isStatisticsNullArea?: boolean
  /**
   * 学员信息
   */
  student?: UserRequest
  /**
   * 报名信息
   */
  learningRegister?: LearningRegisterRequest
  /**
   * 培训班信息
   */
  scheme?: SchemeRequest
  /**
   * 学习信息
   */
  studentLearning?: StudentLearningRequest
  /**
   * 数据分析信息
   */
  dataAnalysis?: DataAnalysisRequest
  /**
   * 是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  trainingChannelName?: string
  /**
   * 非分销门户
   */
  notDistributionPortal?: boolean
  /**
   * 培训类型（全部、网授、面网授）
   */
  trainingType?: string
}

export class StudentLearningExperienceRequest {
  /**
   * 学员学习心得ID
   */
  studentLearningExperienceIds?: Array<string>
  /**
   * 数据归属
   */
  owner?: OwnerInfoModel
  /**
   * 学习心得主题
   */
  learningExperienceTopic?: LearningExperienceTopicRequest
  /**
   * 学员方案学习
   */
  studentLearning?: StudentExperienceSchemeLearningRequest
  /**
   * 用户信息
   */
  user?: UserRequest
  /**
   * 学习心得类型（班级心得，课程心得）
   */
  experienceType?: ExperienceType
  /**
   * 状态
@see StudentLearningExperienceStatus
   */
  status?: Array<StudentLearningExperienceStatus>
  /**
   * 不传值默认全查  是否要心得被删除的数据  true只要被删除的 false 只要未被删除的  null全查
   */
  isDelete?: boolean
}

/**
 * 学员培训方案学习查询条件
<AUTHOR>
@version 1.0
@date 2022/1/17 11:40
 */
export class StudentSchemeLearningRequest {
  /**
   * 学员信息
   */
  student?: UserRequest
  /**
   * 报名信息
   */
  learningRegister?: LearningRegisterRequest
  /**
   * 培训班信息
   */
  scheme?: SchemeRequest
  /**
   * 学习信息
   */
  studentLearning?: StudentLearningRequest
  /**
   * 数据分析信息
   */
  dataAnalysis?: DataAnalysisRequest
  /**
   * 对接管理系统
   */
  connectManageSystem?: ConnectManageSystemRequest
  /**
   * 扩展信息
   */
  extendedInfo?: ExtendedInfoRequest
  /**
   * 是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  trainingChannelName?: string
  /**
   * 非分销门户
   */
  notDistributionPortal?: boolean
  /**
   * 培训类型
   */
  trainingType?: string
  /**
   * 期别id
   */
  issueId?: string
}

/**
 * @version: 1.0
@description: 对接管理系统
@author: sugs
@create: 2022-11-15 11:27
 */
export class ConnectManageSystemRequest {
  /**
   * 同步状态
@see SyncStatus
0 未同步
1 已同步
2 同步失败
-1不同步
   */
  syncStatus?: number
}

/**
 * 数据分析信息
<AUTHOR>
@version 1.0
@date 2022/1/20 15:14
 */
export class DataAnalysisRequest {
  /**
   * 成果配置可获得学时
   */
  trainingResultPeriod?: DoubleScopeRequest
  /**
   * 考核要求学时
   */
  requirePeriod?: DoubleScopeRequest
  /**
   * 已获得总学时
   */
  acquiredPeriod?: DoubleScopeRequest
}

export class ExtendedInfoRequest {
  /**
   * 是否打印
true 打印 false 未打印
   */
  whetherToPrint?: boolean
}

/**
 * 学员学习报名信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:08
 */
export class LearningRegisterRequest {
  /**
   * 报名方式
<p>
1:学员自主报名
2:集体报名
3:管理员导入
@see StudentRegisterTypes
   */
  registerType?: number
  /**
   * 报名来源类型(ORDER：订单 SUB_ORDER：子订单 EXCHANGE_ORDER：换货单)
@see StudentSourceTypes
   */
  sourceType?: string
  /**
   * 报名来源ID
   */
  sourceId?: string
  /**
   * 学员状态(1:正常 2：冻结 3：失效)
@see StudentStatus
   */
  status?: Array<number>
  /**
   * 报名时间
   */
  registerTime?: DateScopeRequest1
  /**
   * 订单销售渠道
   */
  saleChannels?: Array<number>
  /**
   * 来源订单号
   */
  orderNoList?: Array<string>
  /**
   * 来源子订单号
   */
  subOrderNoList?: Array<string>
  /**
   * 来源批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 分销门户id
   */
  portalId?: string
}

/**
 * 地区模型
<AUTHOR>
@version 1.0
@date 2022/2/27 20:01
 */
export class RegionRequest {
  /**
   * 地区：省
   */
  province?: string
  /**
   * 地区：市
   */
  city?: string
  /**
   * 地区：区
   */
  county?: string
}

/**
 * 学员学习信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class StudentLearningRequest {
  /**
   * 培训结果
<p>
-1:未知，培训尚未完成
1:培训合格
0:培训不合格
@see StudentTrainingResults
   */
  trainingResultList?: Array<number>
  /**
   * 培训结果时间
   */
  trainingResultTime?: DateScopeRequest1
  /**
   * 无需学习的学习方式类型
<p>
1: 选课学习方式
2: 考试学习方式
3: 练习学习方式
4：自主学习课程学习方式
@see LearningTypes
   */
  notLearningTypeList?: Array<number>
  /**
   * 课程学习状态（0：未学习 1：学习中 2：学习完成）
   */
  courseScheduleStatus?: number
  /**
   * 考试结果（-1：未考核 0：不合格 1：合格）
@see AssessCalculateResults
   */
  examAssessResultList?: Array<number>
}

/**
 * 地区sku属性查询条件
<AUTHOR>
@version 1.0
@date 2022/2/25 10:55
 */
export class RegionSkuPropertyRequest {
  /**
   * 地区: 省
   */
  province?: string
  /**
   * 地区: 市
   */
  city?: string
  /**
   * 地区: 区县
   */
  county?: string
}

/**
 * 地区匹配查询
<AUTHOR>
@version 1.0
@date 2022/2/25 14:19
 */
export class RegionSkuPropertySearchRequest {
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyRequest>
  /**
   * 地区匹配条件
<p>
ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回，如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
PART部分匹配：查询结果返回的地区与查询条件有给值的地区就会返回 如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
@see com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.common.request.skuProperty.RegionSkuPropertySearchRequest.RegionSearchType
   */
  regionSearchType?: number
}

/**
 * 培训方案信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class SchemeRequest {
  /**
   * 培训方案id
   */
  schemeId?: string
  /**
   * 培训方案id
   */
  schemeIdList?: Array<string>
  /**
   * 培训方案类型
<p>
chooseCourseLearning: 选课规则
autonomousCourseLearning: 自主选课
@see SchemeType
   */
  schemeType?: string
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 培训属性
   */
  skuProperty?: SchemeSkuPropertyRequest
}

/**
 * 培训属性
<AUTHOR>
@version 1.0
@date 2022/1/17 10:22
 */
export class SchemeSkuPropertyRequest {
  /**
   * 年度
   */
  year?: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 科目类型
   */
  subjectType?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string>
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>
  /**
   * 岗位类别
   */
  positionCategory?: Array<string>
  /**
   * 培训对象
   */
  trainingObject?: Array<string>
  /**
   * 技术等级
   */
  jobLevel?: Array<string>
  /**
   * 工种
   */
  jobCategory?: Array<string>
  /**
   * 科目
   */
  subject?: Array<string>
  /**
   * 年级
   */
  grade?: Array<string>
  /**
   * 学段
   */
  learningPhase?: Array<string>
  /**
   * 学科
   */
  discipline?: Array<string>
  /**
   * 培训类型 （网授、面授）
   */
  trainingWay?: Array<string>
}

/**
 * 学习心得主题
 */
export class LearningExperienceTopicRequest {
  /**
   * 心得主题id
   */
  topicIds?: Array<string>
  /**
   * 参与活动时间
   */
  dateScopeRequest?: DateScopeRequest1
  /**
   * 审核方式
   */
  auditType?: AuditType
  /**
   * 心得参与形式
   */
  participateType?: ParticipateType
  /**
   * 是否要心得被删除的数据 默认要
   */
  isDelete?: boolean
}

export class StudentExperienceSchemeLearningRequest {
  /**
   * 参训资格ID
   */
  qualificationIds?: Array<string>
  /**
   * 学号
   */
  studentNos?: Array<string>
  /**
   * 方案id
   */
  schemeIds?: Array<string>
  /**
   * 学习方式id
   */
  learningIds?: Array<string>
}

/**
 * 用户属性
<AUTHOR>
@version 1.0
@date 2022/1/15 11:01
 */
export class UserPropertyRequest {
  /**
   * 所属地区路径
   */
  regionList?: Array<RegionRequest>
  /**
   * 工作单位名称
   */
  companyName?: string
}

/**
 * 用户信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:00
 */
export class UserRequest {
  /**
   * 用户id
   */
  userIdList?: Array<string>
  /**
   * 账户id
   */
  accountIdList?: Array<string>
  /**
   * 用户属性
   */
  userProperty?: UserPropertyRequest
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量生成学员学习心得pdf
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async BatchStudentLearningExperienceExportPdf(
    request: StudentLearningExperienceRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.BatchStudentLearningExperienceExportPdf,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前服务商下方案学习统计详情（面网授）
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportBlendedSchemeLearningReportFormsDetailExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBlendedSchemeLearningReportFormsDetailExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商下方案学习统计（面网授）
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportBlendedSchemeLearningReportFormsExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBlendedSchemeLearningReportFormsExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前分销商下学员方案学习 - 面网授
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportBlendedStudentSchemeLearningExcelInDistributor(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBlendedStudentSchemeLearningExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商下学员方案学习（面网授）
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportBlendedStudentSchemeLearningExcelInServicer(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBlendedStudentSchemeLearningExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商地区管理员下学员方案学习 - 面网授
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBlendedStudentSchemeLearningExcelInServicerManageRegion(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBlendedStudentSchemeLearningExcelInServicerManageRegion,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前专题管理员下学员方案学习  - 面网授
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBlendedStudentSchemeLearningExcelInTrainingChannel(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBlendedStudentSchemeLearningExcelInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前服务商下方案学习统计详情（面授）
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportFaceToFaceSchemeLearningReportFormsDetailExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportFaceToFaceSchemeLearningReportFormsDetailExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商下方案学习统计（面授）
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportFaceToFaceSchemeLearningReportFormsExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportFaceToFaceSchemeLearningReportFormsExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前分销商下学员方案学习 - 面授
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportFaceToFaceStudentSchemeLearningExcelInDistributor(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportFaceToFaceStudentSchemeLearningExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商下学员方案学习（面授）
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportFaceToFaceStudentSchemeLearningExcelInServicer(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportFaceToFaceStudentSchemeLearningExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商地区管理员下学员方案学习 -面授
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportFaceToFaceStudentSchemeLearningExcelInServicerManageRegion(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportFaceToFaceStudentSchemeLearningExcelInServicerManageRegion,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前专题管理员下学员方案学习  - 面授
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportFaceToFaceStudentSchemeLearningExcelInTrainingChannel(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportFaceToFaceStudentSchemeLearningExcelInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前网校下面网授班合格学员学习明细
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportQualificationStudentSchemeLearningDetailInServicer(
    params: { request?: AHJSBDYStudentSchemeLearningInOnlineRequest; sort?: Array<StudentSchemeLearningSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportQualificationStudentSchemeLearningDetailInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出试题
   * @param request 查询参数对象
   * @return true 成功 false 失败
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportQuestionExcelInServicer(
    request: QuestionRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportQuestionExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前服务商下地区学习统计详情
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportRegionLearningReportFormsDetailExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportRegionLearningReportFormsDetailExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商地区管理员下地区学习统计详情
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportRegionLearningReportFormsDetailExcelInServicerManageRegion(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportRegionLearningReportFormsDetailExcelInServicerManageRegion,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前服务商下地区学习统计
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportRegionLearningReportFormsExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportRegionLearningReportFormsExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商地区管理员下地区学习统计
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportRegionLearningReportFormsExcelInServicerManageRegion(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportRegionLearningReportFormsExcelInServicerManageRegion,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出查询当前网校下方案期别下的学员报名情况列表
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportSchemeLearningEnrollmentInServicer(
    params: { issueId?: string; request?: UserInfoRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportSchemeLearningEnrollmentInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前服务商下方案学习统计详情（网授）
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportSchemeLearningReportFormsDetailExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportSchemeLearningReportFormsDetailExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商下方案学习统计
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportSchemeLearningReportFormsExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportSchemeLearningReportFormsExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出学员学习心得
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudentLearningExperienceInServicer(
    request: StudentLearningExperienceRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentLearningExperienceInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前分销商下学员方案学习 - 网授
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportStudentSchemeLearningExcelInDistributor(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商下学员方案学习（网授）
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportStudentSchemeLearningExcelInServicer(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商地区管理员下学员方案学习 - 网授
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudentSchemeLearningExcelInServicerManageRegion(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInServicerManageRegion,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前专题管理员下学员方案学习  - 网授
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudentSchemeLearningExcelInTrainingChannel(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
