// sku绑定模型
export default class SchemeSkuProperty {
  year: string[] = []
  region: string[] = []
  industry = ''
  subjectType = ''
  trainingCategory = ''
  societyTrainingMajor: string[] = []
  constructionTrainingMajor = ''
  technicalGrade = ''
  trainingObject = ''
  positionCategory = ''
  // 学段
  learningPhase = ''
  // 学科
  discipline = ''
  // 执业类别和证书类型
  pharmacistIndustry: string[] = []
  jobLevel = ''
  studyPeriodId = ''
  subjectId = ''
  trainingForm = ''
}
