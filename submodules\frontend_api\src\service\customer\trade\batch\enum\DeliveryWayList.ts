import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 配送方式
 */
export enum DeliveryWayEnum {
  // 1：自取
  Self_Fetched = 1,
  // 2：快递
  Courier
}
/**
 * @description
 */
class DeliveryWayList extends AbstractEnum<DeliveryWayEnum> {
  static enum = DeliveryWayEnum
  constructor(status?: DeliveryWayEnum) {
    super()
    this.current = status
    this.map.set(DeliveryWayEnum.Self_Fetched, '自取')
    this.map.set(DeliveryWayEnum.Courier, '快递')
  }
}

export default new DeliveryWayList()
