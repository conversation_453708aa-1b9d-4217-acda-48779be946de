/**
 * 学员试听课程场景
 */
import ApplyStudentListenCourseSceneProof from '@api/service/customer/learning/scene/proofs/ApplyStudentListenCourseSceneProof'
import CoursePlayTicket from '@api/service/customer/learning/scene/tickets/CoursePlayTicket'
import ApplyTrialListenCourseToken from '@api/service/customer/learning/course/token-provider/ApplyTrialListenCourseToken'

class StudentTrialListenCourseSceneGate {
  private proof: ApplyStudentListenCourseSceneProof

  constructor(proof: ApplyStudentListenCourseSceneProof) {
    this.proof = proof
  }

  /**
   * 申请进入场景门票
   * 进入场景预演：
   * 1. 申请学员token
   * 2.【学员 token】申请课程学习 token
   * 3.【课程学习 token】申请课程播放 token
   */
  async applyEnterTicket(needValidate = true): Promise<CoursePlayTicket> {
    const applyStudentLearningToken = new ApplyTrialListenCourseToken(this.proof.courseId)
    if (needValidate) {
      await applyStudentLearningToken.apply()
    } else {
      await applyStudentLearningToken.applyWithoutValidate()
    }
    // 申请失败不进行下一步
    return new CoursePlayTicket(applyStudentLearningToken.token)
  }

  /**
   * 拒绝进入场景
   */
  rejectEnter() {
    // 拒绝原因
  }
}

export default StudentTrialListenCourseSceneGate
