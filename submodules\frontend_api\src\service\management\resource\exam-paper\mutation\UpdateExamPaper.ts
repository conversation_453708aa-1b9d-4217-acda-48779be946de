import { ResponseStatus } from '@hbfe/common'
import ExamPaperGateway from '@api/ms-gateway/ms-examextraction-v1'

import ExamPaper from './vo/common/ExamPaper'

class UpdateExamPaper extends ExamPaper {
  /**
   * @description: 修改试卷
   * @return {*}
   */
  async doUpdateExamPaper(): Promise<ResponseStatus> {
    const params = this.examPaperParams.toDto()
    const { status } = await ExamPaperGateway.updatePaperPublishConfigure(params)
    return status
  }
}
export default UpdateExamPaper
