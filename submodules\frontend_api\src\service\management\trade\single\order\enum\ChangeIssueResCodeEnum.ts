import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum ChangeIssueResCodeEnum {
  /**
   * 期别报名未开放
   */
  noOpenRegister = 60007,

  /**
   * 期别报名未开始
   */
  noStartRegister = 60004,

  /**
   * 期别报名已结束
   */
  registerOver = 60005,

  /**
   * 报名人数已满
   */
  full = 60001,

  /**
   * 期别已开始培训
   */
  issueStartStudy = 10005,

  /**
   * 期别已结束
   */
  issueOver = 10006,

  /**
   * 报名时段重叠
   */
  studyRepeat = 10007,

  /**
   * 换期中
   */
  inChange = 81001,

  /**
   * 学员已更换到其他期别
   */
  changeOther = 81002,
  /**
   * 期别培训已经开始
   */
  issueStart = 60011
}

class ChangeIssueResCode extends AbstractEnum<ChangeIssueResCodeEnum> {
  static enum = ChangeIssueResCodeEnum
  constructor(status?: ChangeIssueResCodeEnum) {
    super()
    this.current = status
    this.map.set(ChangeIssueResCodeEnum.noOpenRegister, '目标期别未开启报名，请重新选择')
    this.map.set(ChangeIssueResCodeEnum.noStartRegister, '目标期别报名时间未开始，请重新选择')
    this.map.set(ChangeIssueResCodeEnum.registerOver, '目标期别报名时间已过，请重新选择')
    this.map.set(ChangeIssueResCodeEnum.issueStartStudy, '目标期别报名时间已过，请重新选择')
    this.map.set(ChangeIssueResCodeEnum.issueOver, '目标期别培训时间已过，请重新选择')
    this.map.set(ChangeIssueResCodeEnum.studyRepeat, '目标期别与学员已报期别报名时段重叠，请重新选择')
    this.map.set(ChangeIssueResCodeEnum.full, '目标期别已报满，请重新选择')
    this.map.set(ChangeIssueResCodeEnum.inChange, '换期失败，当前学员已换至其他期别，请刷新页面后重新选择。')
    this.map.set(ChangeIssueResCodeEnum.changeOther, '换期失败，当前学员已换至其他期别，请刷新页面后重新选择。')
    this.map.set(ChangeIssueResCodeEnum.issueStart, '期别已开始培训')
  }
}

export default new ChangeIssueResCode()
