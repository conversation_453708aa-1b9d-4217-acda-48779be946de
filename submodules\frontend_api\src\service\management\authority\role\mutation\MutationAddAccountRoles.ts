import { ResponseStatus } from '@hbfe/common'
import roleGateWay from '@api/ms-gateway/ms-role-v1'
export class MutationAddAccountRoles {
  /*
   *  账号id
   * */
  accountId = ''
  roleIds: Array<string> = []

  async doAdd() {
    try {
      if (!this.accountId) {
        return new ResponseStatus(500, '用户账号id不可为空')
      }

      if (!this.roleIds.length) {
        return new ResponseStatus(500, '添加的角色不可为空')
      }

      const res = await roleGateWay.addUserOwnRoles({
        accountId: this.accountId,
        roleIds: this.roleIds
      })
      console.log('调用了doAdd方法，返回值=', res.status)
      return res.status
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/mutation/MutationAddAccountRoles.ts所处方法，doAdd',
        e
      )
    }
  }
}
