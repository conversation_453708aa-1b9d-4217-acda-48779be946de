import MsTradeQueryFrontGatewayCourseLearningForestage, {
  ExchangeOrderRequest,
  IssueLogRequest,
  CommoditySkuResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import ExchangeOrderResponseVo from '@api/service/management/trade/single/order/query/vo/ExchangeOrderResponseVo'
import UserModule from '@api/service/management/user/UserModule'
import ExchangeIssueDetail from '@api/service/management/trade/single/order/query/vo/ExchangeIssueDetail'
import { IssueCommodityDetail } from '@api/service/management/trade/single/order/query/vo/ExchangeIssueDetail'
import { Page } from '@hbfe/common'
export default class QueryExchangeCommodity {
  //  pageExchangeOrderInMySelf
  //  子订单号
  subOrderNo = ''
  /**
   * 获取换货单列表
   *
   */

  async queryExchangeOrderList(page: Page): Promise<Array<ExchangeOrderResponseVo>> {
    const request = new ExchangeOrderRequest()
    request.subOrderNoList = [this.subOrderNo]
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pageExchangeOrderInServicer({
      page: page,
      request: request
    })
    const userIds: string[] = []
    if (res.status.isSuccess()) {
      let tmpArr = []
      for (const item of res.data.currentPageData) {
        userIds.push(item.basicData.applyInfo.applyUser.userId)
        const tmpItem = new ExchangeOrderResponseVo()
        Object.assign(tmpItem, item)
        tmpItem.changeStatue()
        tmpArr.push(tmpItem)
      }
      const userMap = await UserModule.queryUserFactory.queryManager.batchQueryUserInfo(userIds)
      tmpArr = tmpArr.map((res) => ExchangeOrderResponseVo.addUserInfo(res, userMap))
      return tmpArr
    } else {
      return []
    }
  }

  /**
   * 查询订单换期信息列表
   * @param page 分页参数
   */
  async queryExchangeIssueList(page: Page): Promise<ExchangeIssueDetail[]> {
    let result = [] as ExchangeIssueDetail[]
    const request = new IssueLogRequest()
    request.subOrderNoList = [this.subOrderNo]
    const { status, data } = await MsTradeQueryFrontGatewayCourseLearningForestage.pageIssueLogInServicer({
      page,
      request
    })
    if (status && status.isSuccess() && data) {
      page.totalPageSize = data.totalPageSize
      page.totalSize = data.totalSize
      const { currentPageData } = data
      if (currentPageData && currentPageData.length) {
        result = currentPageData.map((el) => {
          const opt = new ExchangeIssueDetail()
          opt.operateType = el.exchangeType
          opt.operateTime = el.createTime
          const { creator, originalCommodity, exchangeCommodity } = el
          if (creator) {
            opt.operatorId = creator.userId
            opt.operatorName = creator.name
          }
          if (originalCommodity) {
            opt.originalIssueCommodity = this.getIssueCommodity(originalCommodity)
          }
          if (exchangeCommodity) {
            opt.exchangeIssueCommodity = this.getIssueCommodity(exchangeCommodity)
          }
          return opt
        })
      }
    }
    return result
  }

  /**
   * 查询订单换期信息列表
   * @param page 分页参数
   */
  async queryExchangeIssueListInDistributor(page: Page): Promise<ExchangeIssueDetail[]> {
    let result = [] as ExchangeIssueDetail[]
    const request = new IssueLogRequest()
    request.subOrderNoList = [this.subOrderNo]
    const { status, data } = await MsTradeQueryFrontGatewayCourseLearningForestage.pageIssueLogInDistributor({
      page,
      request
    })
    if (status && status.isSuccess() && data) {
      page.totalPageSize = data.totalPageSize
      page.totalSize = data.totalSize
      const { currentPageData } = data
      if (currentPageData && currentPageData.length) {
        result = currentPageData.map((el) => {
          const opt = new ExchangeIssueDetail()
          opt.operateType = el.exchangeType
          opt.operateTime = el.createTime
          const { creator, originalCommodity, exchangeCommodity } = el
          if (creator) {
            opt.operatorId = creator.userId
            opt.operatorName = creator.name
          }
          if (originalCommodity) {
            opt.originalIssueCommodity = this.getIssueCommodity(originalCommodity)
          }
          if (exchangeCommodity) {
            opt.exchangeIssueCommodity = this.getIssueCommodity(exchangeCommodity)
          }
          return opt
        })
      }
    }
    return result
  }

  /**
   * 获取换货单列表-分销商
   */
  async queryExchangeOrderListInDistributor(page: Page): Promise<Array<ExchangeOrderResponseVo>> {
    const request = new ExchangeOrderRequest()
    request.subOrderNoList = [this.subOrderNo]
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pageExchangeOrderInDistributor({
      page: page,
      request: request
    })
    const userIds: string[] = []
    if (res.status.isSuccess()) {
      let tmpArr = []
      for (const item of res.data.currentPageData) {
        userIds.push(item.basicData.applyInfo.applyUser.userId)
        const tmpItem = new ExchangeOrderResponseVo()
        Object.assign(tmpItem, item)
        tmpItem.changeStatue()
        tmpArr.push(tmpItem)
      }
      const userMap = await UserModule.queryUserFactory.queryManager.batchQueryUserInfoInDistributor(userIds)
      tmpArr = tmpArr.map((res) => ExchangeOrderResponseVo.addUserInfo(res, userMap))
      return tmpArr
    } else {
      return []
    }
  }

  /**
   * 获取期别商品详情
   * @param response 接口返回值
   * @private
   */
  private getIssueCommodity(response: CommoditySkuResponse): IssueCommodityDetail {
    const detail = new IssueCommodityDetail()
    const { issueInfo } = response
    if (issueInfo) {
      detail.issueName = issueInfo.issueName
    }
    detail.schemeName = response.saleTitle
    return detail
  }
}
