<template>
  <el-select v-model="selected" :placeholder="placeholder" @clear="selected = undefined" class="el-input" clearable>
    <el-option v-for="item in yearOptions" :label="item.year" :value="item.id" :key="item.id"></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import QueryYear from '@api/service/common/basic-data-dictionary/query/QueryYear'
  import YearVo from '@api/service/common/basic-data-dictionary/query/vo/YearVo'

  @Component
  export default class extends Vue {
    selected = ''

    // 年度选项
    yearOptions = new Array<YearVo>()

    // 是否倒序
    @Prop({
      type: Boolean,
      default: true
    })
    isSort: boolean

    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Prop({
      type: String,
      default: '请选择培训年度'
    })
    placeholder: string

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.$emit('checkSecond')
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }

    async created() {
      await this.queryYearOptions()
    }

    // 获取年度列表
    async queryYearOptions() {
      const res = await QueryYear.queryYearList()
      const options = res.isSuccess() ? QueryYear.yearList : ([] as YearVo[])
      if (this.isSort) {
        this.sortByYear(options)
      }
      this.yearOptions = options
    }

    // 倒序排列
    sortByYear(options: YearVo[]) {
      if (options?.length > 1) {
        // 倒序
        for (let i = 0; i < options?.length - 1; i++) {
          for (let j = 0; j < options?.length - i; j++) {
            if (options[j]?.sort < options[j + 1]?.sort) {
              ;[options[j], [options[j + 1]]] = [options[j + 1], [options[j]]]
            }
          }
        }
      }
    }
  }
</script>
