import getUserCurrentLearningInfo from './queries/getUserCurrentLearningInfo.graphql'
import applyCoursewareLearningToken from './mutates/applyCoursewareLearningToken.graphql'
import applyCoursewareLearningTokenByToken from './mutates/applyCoursewareLearningTokenByToken.graphql'
import applyMediaLearningTiming from './mutates/applyMediaLearningTiming.graphql'
import applyMediaLearningTimingWithAnti from './mutates/applyMediaLearningTimingWithAnti.graphql'
import commitMediaLearningTiming from './mutates/commitMediaLearningTiming.graphql'
import commitMediaLearningTimingWithAnti from './mutates/commitMediaLearningTimingWithAnti.graphql'
import courseRelatedCompleteReissue from './mutates/courseRelatedCompleteReissue.graphql'
import endMediaLearningTiming from './mutates/endMediaLearningTiming.graphql'
import keepAliveHeartbeat from './mutates/keepAliveHeartbeat.graphql'
import prepareCourseLearningTiming from './mutates/prepareCourseLearningTiming.graphql'
import prepareCourseLearningTimingBeToken from './mutates/prepareCourseLearningTimingBeToken.graphql'

export {
  getUserCurrentLearningInfo,
  applyCoursewareLearningToken,
  applyCoursewareLearningTokenByToken,
  applyMediaLearningTiming,
  applyMediaLearningTimingWithAnti,
  commitMediaLearningTiming,
  commitMediaLearningTimingWithAnti,
  courseRelatedCompleteReissue,
  endMediaLearningTiming,
  keepAliveHeartbeat,
  prepareCourseLearningTiming,
  prepareCourseLearningTimingBeToken
}
