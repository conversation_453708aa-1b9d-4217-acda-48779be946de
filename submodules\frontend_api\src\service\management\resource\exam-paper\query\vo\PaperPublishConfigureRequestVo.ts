import { PaperPublishConfigureRequest } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import { PublishPatternTypes } from '../../enum/ExamPaperPublishPatternTypes'

class PaperPublishConfigureRequestVo extends PaperPublishConfigureRequest {
  /**
   * 出卷配置分类ID
   */
  categoryIdList?: Array<string> = []
  /**
   * 出卷配置名称
   */
  name?: string = ''
  /**
   * 出卷模式
   * 默认智能卷
   */
  paperPublishPatterns?: PublishPatternTypes = null
  /**
   * 是否启用 1 启用 2禁用
   */
  status?: number = undefined
}

export default PaperPublishConfigureRequestVo
