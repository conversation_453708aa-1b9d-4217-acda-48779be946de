import { ResponseStatus } from '@hbfe/common'
import BasicdataDomain, { UpdateCollectiveRegisterRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'

/**
 * 更新集体用户信息
 */
class MutationUpdateUserInfo {
  userUpdateRequest = new UpdateCollectiveRegisterRequest()

  /**
   * 更新集体用户信息
   */
  async doUpdate(): Promise<ResponseStatus> {
    const { status } = await BasicdataDomain.updateCollectiveRegister(this.userUpdateRequest)
    return status
  }
}
export default MutationUpdateUserInfo
