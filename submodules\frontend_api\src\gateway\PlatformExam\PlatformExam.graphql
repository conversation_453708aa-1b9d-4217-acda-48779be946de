schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""统计每日一练参与人次 - 来源中间表(已实现)
		@return
	"""
	countDailyPracticePartInUser(paramDTO:DailyPracticeParamDTO):Double!
	"""统计易错题总数
		@return
		@param tagIdList
	"""
	countTotalErrorProne(tagIdList:[String]):Int!
	"""获取指定考试学习方式下面的考试参考人次
		练习模式的也记录参考人次
		@param learningId
		@return
	"""
	countTotalUserEnterExam(learningId:String):Int!
	"""统计用户已答过的易错题数量 - 易错题-累计消灭提数 -  - 考试服务
		@param userId
		@return
	"""
	countUserHasAnswerErrorProneQuestion(userId:String):Long!
	"""导出试题答题记录明细
		@return
	"""
	exportUserAnswerRecord:String @NotAuthenticationRequired
	"""获取内置题库1
		@return
	"""
	getDefaultQuestionLibraryId:String
	"""获取场次基本信息
		@param schemeId
		@param learningId
		@return
	"""
	getExamRoundBaseInfo(schemeId:String!,learningId:String!):ExamRoundBaseDTO
	"""统计练习次数最多的练习
		@param param
		@return
	"""
	getMaxPracticeCountAnswerPaper(param:AnswerPracticeParamDTO):AnswerPracticeDTO
	"""用户最后一次答题记录 - 来源中间表
		@return  UserLastPracticeRecordDTO || null:不存在答题记录
	"""
	getUserLastPracticeRecord(paramDTO:UserLastPracticeRecordParamDTO):UserLastPracticeRecordDTO
	"""获取当前登录用户的每日一练 - 来源中间表(已实现)
		@return
	"""
	listDailyPractice(paramDTO:DailyPracticeParamDTO):[DailyPracticeDTO]
	"""获取指定学习方案下面的考试学习方式 -目前只有一个考试学习方式
		@param schemeId 学习方案id
		@return
	"""
	listExamLearning(schemeId:String):ExamLearningDTO
	"""获取题库数据
		@param idList
		@return
	"""
	listExamLibrary(idList:[String]):[ExamLibraryDto]
	"""获取试卷分类详情
		@param idList
		@return
	"""
	listExamPaperClassify(idList:[String]):[ExamPaperClassifyDTO]
	"""查询历史考试记录
		@param paramDTO
		@return
	"""
	listExamRecord(paramDTO:ExamRecordParamDTO):[ExamRecordDTO]
	"""获取制定条件的场次信息
		@return
	"""
	listExamRound(examRoundList:[String]):[ExamRoundBaseDTO]
	"""获取用户答题次数排行榜-Dashboard接口
		@param param
		@return
	"""
	listUserQuestionAnswerCountRank(param:DashboardRankParam):[UserQuestionAnswerCountDTO]
	"""获取用户答题正确率排行榜-Dashboard接口
		@param param
		@return
	"""
	listUserQuestionCorrectRateRank(param:DashboardRankParam):[UserQuestionAnswerCorrectRateDTO]
	"""分页获取题库
		@param page
		@param requestDTO
		@return
	"""
	pageExamLibrary(page:Page,requestDTO:QuestionLibraryQueryRequestDTO):ExamLibraryDtoPage @page(for:"ExamLibraryDto")
	"""获取用户做题记录
		@param paramDTO
		@return
	"""
	pageUserAnswerRecord(page:Page,paramDTO:AnswerRecordParamDTO):AnswerRecordDTOPage @page(for:"AnswerRecordDTO")
	"""统计指定答题记录卷在同场次中的考试报告
		@param answerExamRecordId
		@return 如果考试报告尚未生成，则返回null
	"""
	statisticAnswerExamPaperReport(answerExamRecordId:String!):AnswerExamPaperReportDTO
	"""统计试题数量
		@return
	"""
	statisticLibraryQuestionCount(request:LibraryQuestionStatisticRequest):[LibraryQuestionCountResponse]
	"""统计平台的考试相关数据总览
		@return
	"""
	statisticPlatformExamInfo:PlatformExamStatisticDTO
	"""统计试题答题次数 - 按日期分组统计 -Dashboard接口
		@param param
		@return
	"""
	statisticQuestionAnswerCountGroupDate(param:DashboardParam):[QuestionAnswerCountDTO]
	"""统计试题正确率 - 按题类分组统计-Dashboard接口
		@param param
		@return
	"""
	statisticQuestionCorrectRate(param:DashboardParam):QuestionCorrectRateDTO
	"""统计试题数量
		支持的试题类型：真题练习、模拟题、练习题
		@param paramDTO
		@return
	"""
	statisticQuestionGroupByQuestionCategory(paramDTO:QuestionStatisticParamDTO):QuestionCategoryCountDTO
	"""统计试题数量
		支持的试题类型：真题练习、模拟题、练习题
		@param paramDTO
		@return
	"""
	statisticQuestionGroupByQuestionType(paramDTO:QuestionStatisticParamDTO):[QuestionStatisticDTO]
	"""统计试题数量
		支持的试题类型：真题练习、模拟题、练习题
		@param paramDTO
		@return
	"""
	statisticQuestionGroupByTagIds(paramDTO:QuestionStatisticParamDTO):[QuestionStatisticDTO]
	"""统计指定试题的答题情况  -考试结果解析页  -- 考试服务
		@param paramDTO
		@return
	"""
	statisticSingleQuestionAnswerInfo(paramDTO:SingleQuestionAnswerParamDTO):[SingleQuestionAnswerStatisticDTO]
	"""统计用户试题类型的答题重量
		@param paramDTO
	"""
	statisticUserAnswerPerDay(paramDTO:AnswerQuestionStatisticPerDayParamDTO):[AnswerQuestionStatisticPerDayDTO]
	"""
		统计用户指定试题类型指定考纲下的答题情况  -考纲维度- 考试服务
		支持的试题类型：真题练习、模拟题、练习题
		@param paramDTO
		@return
	"""
	statisticUserAnswerQuestionByTag(paramDTO:AnswerQuestionStatisticParamDTO):[AnswerQuestionStatisticDTO]
	"""统计用户试题类型的答题重量
		@param paramDTO
	"""
	statisticUserAnswerQuestionCountByQuestionCategory(paramDTO:AnswerQuestionCountParamDTO):UserAnswerQuestionCountDTO
	"""统计用户指定试题类型下的答题汇总情况  -题类下的汇总统计- 来源中间表（已实现）
		支持的试题类型：真题练习、模拟题、练习题
		@param paramDTO
		@return
	"""
	statisticUserAnswerQuestionSummaryInfo(paramDTO:AnswerQuestionStatisticParamDTO):AnswerQuestionStatisticSummaryDTO
	"""统计用户的模考数据  - 来源中间表(部分实现，剩余排名为实现)
		1、对于评估报告首行汇总数据考虑单独开一个口？
		2、首页的模拟试卷统计也使用该接口？
		@param paramDTO
		@return
	"""
	statisticUserExamInfo(paramDTO:UserExamStatisticParamDTO):UserExamStatisticDTO
	"""用户模拟考试题型正确率统计- 来源中间表(已实现)
		@return
	"""
	statisticUserExamQuestionCorrectRateByQuestionType(paramDTO:UserExamQuestionCorrectRateStatisticParamDTO):UserExamQuestionCorrectRateStatisticDTO
	"""统计用户收藏题数量 - 总数 - 考试服务
		@param paramDTO
		@return
	"""
	statisticUserFavoriteQuestionCount(paramDTO:UserFavoriteQuestionStatisticParamDTO):Long!
	"""统计用户收藏题数量 - 题类分组- 考试服务
		@param paramDTO
		@return
	"""
	statisticUserFavoriteQuestionGroupByQuestionCategory(paramDTO:UserFavoriteQuestionStatisticParamDTO):[UserFavoriteQuestionStatisticDTO]
	"""统计用户收藏题数量 - 题型分组- 考试服务
		@param paramDTO
		@return
	"""
	statisticUserFavoriteQuestionGroupByQuestionType(paramDTO:UserFavoriteQuestionStatisticParamDTO):[UserFavoriteQuestionStatisticDTO]
	"""统计用户收藏题数量 - 标签分组 - 考试服务
		@param paramDTO
		@return
	"""
	statisticUserFavoriteQuestionGroupByTag(paramDTO:UserFavoriteQuestionStatisticParamDTO):[UserFavoriteQuestionStatisticDTO]
	"""用户练习统计 评估报告-试题练习、首页统计  - 中间表(已实现)
		计算现在的数据并且比对7天前的数据
		入参 statisticPracticeTypeList 应该包括 REAL、SIMULATION、PRACTICE、DAILY、ERROR_PRONE
		@param paramDTO
		@return
	"""
	statisticUserPractice(paramDTO:UserPracticeStatisticParamDTO):UserPracticeStatisticDTO
	"""练习正确率趋势统计- 来源中间表(已实现)
		@param paramDTO
		@return
	"""
	statisticUserPracticeCorrectRateTrend(paramDTO:PracticeTrendStatisticParamDTO):[PracticeCorrectRateTrendDTO]
	"""练习题量趋势统计- 来源中间表(已实现)
		评估报告-练习-展示最近15天的练习量
		评估报告-错题重答-展示最近15天的练习量
		@param paramDTO
		@return
	"""
	statisticUserPracticeCountTrend(paramDTO:PracticeTrendStatisticParamDTO):[PracticeCountTrendDTO]
	"""统计用户练习频率（天数） - 来源中间表(已实现)
		学员中心首页-每日一练累计练习的天数，入参 statisticPracticeTypeList 应该 DAILY
		@param paramDTO
		@return
	"""
	statisticUserPracticeFrequency(paramDTO:UserPracticeStatisticParamDTO):PracticeFrequencyStatisticDTO
	"""统计用户剩余的易错题数量 --未做对(在易错题场景下)- - 按试题题型维度 - 考试服务
		@param paramDTO
		@return
	"""
	statisticUserRemainErrorProneGroupByQuestionType(paramDTO:ErrorProneStatisticParamDTO):[ErrorProneStatisticDTO]
	"""统计用户剩余的易错题数量  --未做对(在易错题场景下)- - 考纲维度  -- 考试服务
		@param paramDTO
		@return
	"""
	statisticUserRemainErrorProneGroupByTag(paramDTO:ErrorProneStatisticParamDTO):[ErrorProneStatisticDTO]
	"""用户单题试题答题统计  --考试结果解析页  -- 考试服务
		@param paramDTO
		@return
	"""
	statisticUserSingleQuestionAnswerInfo(paramDTO:SingleQuestionAnswerParamDTO):[SingleQuestionAnswerStatisticDTO]
	"""统计用户错题数量，按题类分组统计 考试服务
		@param paramDTO
		@return
	"""
	statisticUserWrongQuestionGroupByQuestionCategory(paramDTO:UserWrongQuestionStatisticParamDTO):[UserWrongQuestionStatisticDTO]
	"""统计用户错题数量，按题型分组统计,  错题重答、错题评估报告 考试服务
		@param paramDTO
		@return
	"""
	statisticUserWrongQuestionGroupByQuestionType(paramDTO:UserWrongQuestionStatisticParamDTO):[UserWrongQuestionStatisticDTO]
	"""统计用户错题数量，按标签分组统计 考试服务
		@param paramDTO
		@return
	"""
	statisticUserWrongQuestionGroupByTag(paramDTO:UserWrongQuestionStatisticParamDTO):[UserWrongQuestionStatisticDTO]
	"""统计用户错题相关的合计数据 考试服务
		@param paramDTO
		@return
	"""
	statisticUserWrongQuestionSummary(paramDTO:UserWrongQuestionSummaryParamDTO):UserWrongQuestionStatisticSummaryDTO
}
type Mutation {
	"""导入案例题
		@param request
	"""
	importComprehensiveQuestion(request:QuestionImportRequest):Boolean!
}
"""Author:FangKunSen
	Time:2021-03-01,10:20
"""
input QuestionLibraryQueryRequestDTO @type(value:"com.fjhb.btpx.integrative.gateway.graphql.request.QuestionLibraryQueryRequestDTO") {
	"""题库名"""
	name:String
	"""单位id"""
	unitId:String
	"""包含的题库ID集合"""
	libraryIds:[String]
	"""是否可用 -1/0/1 不查/可用/不可用"""
	enable:Int
}
"""@author: eleven
	@date: 2020/4/13
"""
input AnswerPracticeParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.AnswerPracticeParamDTO") {
	"""统计的试题类型"""
	statisticPracticeTypeList:[PracticeType]!
	"""期数id"""
	issueId:String
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""答题数量统计
	@author: eleven
	@date: 2020/3/23
"""
input AnswerQuestionCountParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.AnswerQuestionCountParamDTO") {
	"""统计的试题类型"""
	statisticPracticeTypeList:[PracticeType]!
	"""统计的期数id"""
	issueId:String
	"""用户id，学员端忽略该参数"""
	userId:String
	"""答卷完成时间 -起"""
	completeTimeStart:DateTime
	"""答卷完成时间 -止"""
	completeTimeEnd:DateTime
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""*用户答题统计入参
	@author: eleven
	@date: 2020/3/4
"""
input AnswerQuestionStatisticParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.AnswerQuestionStatisticParamDTO") {
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String!
	"""试题类型
		@see PracticeType#getValue()
	"""
	practiceType:PracticeType
	"""考纲id # 标签id"""
	tagIdList:[String]
}
"""做题记录分页查询参数
	@author: eleven
	@date: 2020/3/14
"""
input AnswerRecordParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.AnswerRecordParamDTO") {
	"""学习方案id"""
	schemeId:String!
	"""查询的用户id
		学员端忽略该参数
	"""
	userId:String
	"""查询的试题类型"""
	practiceTypeList:[PracticeType]
	"""期数id"""
	issueId:String
	"""查询的答题记录卷状态
		如果没传，查询所有
	"""
	answerStatusEnum:AnswerStatusEnum
}
"""每日一练查询参数
	@author: eleven
	@date: 2020/3/9
"""
input DailyPracticeParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.DailyPracticeParamDTO") {
	"""获取近N日的数据
		目前前端应该传 7
	"""
	recentDay:Int!
	"""统计每日一练参与人次的参数
		入参为 listDailyPractice返回的日期
		date 2020-03-20
	"""
	date:String
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""首页统计数据统计参数
	@author: eleven
	@date: 2020/4/18
"""
input DashboardParam @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.DashboardParam") {
	"""作答提交时间起 >="""
	submitAnswerTimeBegin:DateTime
	"""作答提交时间止 <="""
	submitAnswerTimeEnd:DateTime
	"""单位"""
	unitId:String
	"""用户id集合"""
	userIdList:[String]
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""@author: eleven
	@date: 2020/4/18
"""
input DashboardRankParam @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.DashboardRankParam") {
	"""统计返回多少条的记录"""
	size:Int!
	"""作答提交时间起 >="""
	submitAnswerTimeBegin:DateTime
	"""作答提交时间止 <="""
	submitAnswerTimeEnd:DateTime
	"""单位"""
	unitId:String
	"""用户id集合"""
	userIdList:[String]
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""易错题统计
	@author: eleven
	@date: 2020/3/4
"""
input ErrorProneStatisticParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.ErrorProneStatisticParamDTO") {
	"""标签id集合"""
	tagIdList:[String]
}
"""考试记录查询参数
	@author: eleven
	@date: 2020/3/4
"""
input ExamRecordParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.ExamRecordParamDTO") {
	"""答题记录卷id"""
	answerExamRecordIdList:[String]
	"""用户id - 运营域参数"""
	userId:String
	"""查询的学习方案"""
	schemeId:String
	"""学习方式id"""
	learningId:String
	"""期数id"""
	issueId:String
	"""统计返回多少的长度
		目前都是前端都传15
		-1：查询全部
	"""
	statisticSize:Int!
	"""答卷完成时间 -起"""
	completeTimeStart:DateTime
	"""答卷完成时间 -止"""
	completeTimeEnd:DateTime
	"""模拟考卷应用类型
		@see ExamPaperApplyType#getValue()
	"""
	examPaperApplyType:ExamPaperApplyType
}
"""<AUTHOR>
	@date 2020/7/9
	@description
"""
input LibraryQuestionStatisticRequest @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.LibraryQuestionStatisticRequest") {
	"""题库id集合"""
	libraryIdList:[String]
	"""题目"""
	topic:String
	"""指定试题类型
		1：判断 2：单选 3：多选 4：填空 5：问答 6：父子
	"""
	questionTypes:[Int]
	"""是否可用 -1/0/1 不查/可用/不可用"""
	enable:Int!
}
"""练习趋势统计
	@author: eleven
	@date: 2020/3/5
"""
input PracticeTrendStatisticParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.PracticeTrendStatisticParamDTO") {
	"""统计的练习类型
		评估报告-试题练习
		这里入参应该包括 REAL、SIMULATION、PRACTICE、DAILY、ERROR_PRONE
		收藏题原型没备注，需要？待和产品确认
		评估报告-错题 最近15天的错题重答数量
	"""
	statisticPracticeTypeList:[PracticeType]!
	"""统计返回多少的长度
		目前都是前端都传15
	"""
	statisticSize:Int!
	"""答卷完成时间 -起"""
	completeTimeStart:DateTime
	"""答卷完成时间 -止"""
	completeTimeEnd:DateTime
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""@author: eleven
	@date: 2020/3/4
"""
input QuestionStatisticParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.QuestionStatisticParamDTO") {
	"""试题类型
		@see PracticeType#getValue()
		支持是其中的 真题练习、模拟题、练习题三种
	"""
	questionType:PracticeType
	"""考纲id # 标签id"""
	tagIdList:[String]
	"""是否可用
		-1:不查  0:可用  1:不可用
	"""
	enable:Int!
}
"""单题试题答题统计
	@author: eleven
	@date: 2020/3/4
"""
input SingleQuestionAnswerParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.SingleQuestionAnswerParamDTO") {
	"""试题id"""
	questionId:[String]
}
"""用户模考正确率统计
	@author: eleven
	@date: 2020/3/5
"""
input UserExamQuestionCorrectRateStatisticParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.UserExamQuestionCorrectRateStatisticParamDTO") {
	"""答卷完成时间 -起"""
	completeTimeStart:DateTime
	"""答卷完成时间 -止"""
	completeTimeEnd:DateTime
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""用户模考数据统计
	@author: eleven
	@date: 2020/3/5
"""
input UserExamStatisticParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.UserExamStatisticParamDTO") {
	"""答卷完成时间 -起"""
	completeTimeStart:DateTime
	"""答卷完成时间 -止"""
	completeTimeEnd:DateTime
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""收藏题统计参数
	@author: eleven
	@date: 2020/3/5
"""
input UserFavoriteQuestionStatisticParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.UserFavoriteQuestionStatisticParamDTO") {
	"""学习方案id"""
	schemeId:String
	"""期数id"""
	issueId:String
	"""统计的标签id集合"""
	tagIdList:[String]
	"""收藏记录创建时间 起 >"""
	createTimeStart:DateTime
	"""收藏记录创建时间 止 <="""
	createTimeEnd:DateTime
	"""有效时间截至 ，有效时间值得是在时间内创建且未删除的记录"""
	validTimeEnd:DateTime
}
"""用户最后一次答题记录参数
	@author: eleven
	@date: 2020/3/5
"""
input UserLastPracticeRecordParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.UserLastPracticeRecordParamDTO") {
	"""期数id"""
	issueId:String
	"""查询的答题记录卷状态
		如果没传，查询所有
	"""
	answerStatusEnum:AnswerStatusEnum
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""练习统计查询参数  - 评估报告-试题练习
	@author: eleven
	@date: 2020/3/5
"""
input UserPracticeStatisticParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.UserPracticeStatisticParamDTO") {
	"""统计的期数id"""
	issueId:String
	"""统计的练习类型
		评估报告-试题练习
	"""
	statisticPracticeTypeList:[PracticeType]
	"""答卷完成时间 -起"""
	completeTimeStart:DateTime
	"""答卷完成时间 -止"""
	completeTimeEnd:DateTime
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""@author: eleven
	@date: 2020/3/5
"""
input UserWrongQuestionStatisticParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.UserWrongQuestionStatisticParamDTO") {
	"""学习方案id"""
	schemeId:String!
	"""错题类型"""
	type:WrongQuestionType!
	"""答对次数{@link #type} = {@link WrongQuestionType#CORRECT_IN_CORRECTION} 有在错题重练中答对
		大于
	"""
	correctCountBegin:Long
	"""首次错题时间 起 >"""
	firstTimeStart:DateTime
	"""首次错题时间 止 <="""
	firstTimeEnd:DateTime
	"""考纲id # 标签id"""
	tagIdList:[String]
}
"""统计用户错题相关的合计数据
	<AUTHOR> create 2020/3/14 11:14
"""
input UserWrongQuestionSummaryParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.UserWrongQuestionSummaryParamDTO") {
	"""学习方案id"""
	schemeId:String!
	"""作答截至起始 >"""
	answerTimeStart:DateTime
	"""作答截至时间 <="""
	answerTimeEnd:DateTime
	"""错题类型"""
	type:WrongQuestionType
	"""答对次数{@link #type} = {@link WrongQuestionType#CORRECT_IN_CORRECTION} 有在错题重练中答对
		大于
	"""
	correctCountBegin:Long
	"""考纲id # 标签id"""
	tagIdList:[String]
}
"""作答试题统计参数
	<AUTHOR> create 2020/3/25 10:12
"""
input AnswerQuestionStatisticPerDayParamDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.exam.answered.param.AnswerQuestionStatisticPerDayParamDTO") {
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
	"""统计的标签id集合"""
	tagIdList:[String]
	"""作答提交时间起 <="""
	submitAnswerTimeBegin:DateTime!
	"""作答提交时间止 <="""
	submitAnswerTimeEnd:DateTime!
}
"""@author: eleven
	@date: 2020/5/7
"""
input QuestionImportRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.QuestionImportRequest") {
	"""导入文件路径"""
	filePath:String
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""错题类型
	<AUTHOR> create 2020/3/19 16:16
"""
enum WrongQuestionType @type(value:"com.fjhb.btpx.integrative.gateway.graphql.enums.WrongQuestionType") {
	ALL
	NEVER_CORRECT_IN_CORRECTION
	CORRECT_IN_CORRECTION
}
"""答题记录卷在对应场次的考试报告
	@author: eleven
	@date: 2020/3/4
"""
type AnswerExamPaperReportDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.AnswerExamPaperReportDTO") {
	"""答题记录卷id"""
	answerExamRecordId:String
	"""考试成绩"""
	examScore:Double!
	"""本场考试平均分"""
	avgScore:Double!
	"""本次考试在同场次的排名
		分数相同的，则并列排名同分数第一名
	"""
	examRank:Double!
	"""累计参考人次"""
	totalAnswerPaperCount:Double!
	"""统计考试成绩小于当前分数的人次，用户计算击败考生的比例"""
	lessCurrentScoreAnswerPaperCount:Double!
	"""击败考生比例 = lessCurrentScoreAnswerPaperCount /totalAnswerPaperCount"""
	examRankRatio:Double!
	"""答卷耗时时间"""
	totalUseTime:Double!
	"""已答题数"""
	answeredCount:Double!
	"""答对题数"""
	correctCount:Long!
	"""答错题数"""
	wrongCount:Long!
	"""试卷总题数"""
	totalQuestionCount:Double!
}
"""练习答卷
	@author: eleven
	@date: 2020/4/13
"""
type AnswerPracticeDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.AnswerPracticeDTO") {
	"""标签id"""
	tagId:String
	"""练习次数"""
	count:Double!
}
"""用户答题统计-考纲维度
	@author: eleven
	@date: 2020/3/4
"""
type AnswerQuestionStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.AnswerQuestionStatisticDTO") {
	"""标签id集合"""
	tagIds:[String]
	"""已答试题数"""
	answeredCount:Long!
	"""已答题次"""
	answeredTimes:Long!
	"""答对题次"""
	correctTimes:Long!
	"""正确率"""
	correctRate:Double!
}
"""用户答题统计- 题类维度
	@author: eleven
	@date: 2020/3/4
"""
type AnswerQuestionStatisticSummaryDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.AnswerQuestionStatisticSummaryDTO") {
	"""做答总题次"""
	answeredCount:Double!
	"""答对题次"""
	correctCount:Double!
	"""答错题次"""
	wrongCount:Double!
	"""正确率"""
	correctRate:Double!
}
"""做题记录对象
	@author: eleven
	@date: 2020/3/14
"""
type AnswerRecordDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.AnswerRecordDTO") {
	"""方案id"""
	schemeId:String
	"""方式id"""
	learningId:String
	"""答题记录卷id"""
	answerExamRecordId:String
	"""答卷id"""
	answerPaperId:String
	"""引用的试卷id"""
	examPaperId:String
	"""场次id"""
	examRoundId:String
	"""练习模式"""
	practiceMode:PracticeMode
	"""抽题所在的标签
		当practiceMode = PracticeMode.OUTLINE 有效
	"""
	tagId:String
	"""抽题题型
		当practiceMode =  PracticeMode.QUESTION_TYPE 有效
		0/1/2/3/4/5/6/7 未知/判断/单选/多选/填空/简答/父子/混合
		@see com.fjhb.platform.component.exam.commons.api.consts.question.QuestionType
	"""
	questionType:Int!
	"""抽题数"""
	questionCount:Double!
	"""答题数"""
	answerCount:Double!
	"""正确数"""
	correctCount:Double!
	"""答卷完成时间"""
	answerCompleteTime:String
}
"""每日一练数据对象
	@author: eleven
	@date: 2020/3/4
"""
type DailyPracticeDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.DailyPracticeDTO") {
	"""每日一练日期
		格式：2020-03-04
	"""
	date:String
	"""星期几
		MONDAY
		TUESDAY
		WEDNESDAY
		THURSDAY
		FRIDAY
		SATURDAY
		SUNDAY
		@see java.time.DayOfWeek
	"""
	week:String
	"""是否已经有答题记录"""
	hasAnswerRecord:Boolean!
	"""当前登录用练习次数"""
	count:Double!
}
"""易错题统计
	@author: eleven
	@date: 2020/3/4
"""
type ErrorProneStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.ErrorProneStatisticDTO") {
	"""分组key,tag集合  - 按标签分组统计，该字段才生效"""
	tags:[String]
	"""试题题型 - 按题型分组统计，该字段才生效
		0/1/2/3/4/5/6/7 未知/判断/单选/多选/填空/简答/父子/混合
		@see com.fjhb.platform.component.exam.commons.api.consts.question.QuestionType
	"""
	questionType:String
	"""剩余未答的易错题总数"""
	remainCount:Int!
}
"""考试学习方式的场次试卷信息
	@author: eleven
	@date: 2020/3/4
"""
type ExamLearningDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.ExamLearningDTO") {
	"""学习方式ID"""
	learningId:String
	"""考试名称"""
	name:String
	"""试卷ID"""
	examPaperId:String
	"""考试时长，单位分钟"""
	examTimeLength:Int!
	"""考试次数 0表示不限制次数"""
	examCount:Int!
	"""试卷组卷类型"""
	paperConfigType:PaperConfigType
}
"""试卷分类
	@author: eleven
	@date: 2020/4/3
"""
type ExamPaperClassifyDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.ExamPaperClassifyDTO") {
	"""分类id"""
	id:String
	"""分类名称"""
	name:String
	"""父试卷分类id"""
	parentId:String
	"""描述"""
	description:String
}
"""用户历史考试记录
	@author: eleven
	@date: 2020/3/4
"""
type ExamRecordDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.ExamRecordDTO") {
	"""答题记录卷id"""
	answerExamRecordId:String
	"""答卷id"""
	answerPaperId:String
	"""引用的试卷id"""
	examPaperId:String
	"""方案id"""
	schemeId:String
	"""方式id"""
	learningId:String
	"""场次id"""
	examRoundId:String
	"""考试成绩"""
	examScore:Double!
	"""考试考试时间（用户开始考试的时间）"""
	answerPaperStartTime:String
	"""答卷完成时间"""
	answerPaperCompleteTime:String
	"""答卷耗时时间"""
	totalUseTime:Double!
}
"""平台级考试场次信息
	Author:FangKunSen
	Time:2020-04-21,09:38
"""
type ExamRoundBaseDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.ExamRoundBaseDTO") {
	"""场次id"""
	id:String
	"""场次名称"""
	name:String
	"""开考时间"""
	beginTime:String
	"""结束时间"""
	endTime:String
	"""试卷总分"""
	totalScore:Double!
	"""考试时长"""
	examTimeLength:Int!
	"""考试次数 0表示不限制次数"""
	examCount:Int!
}
"""<AUTHOR>
	@date 2020/7/9
	@description
"""
type LibraryQuestionCountResponse @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.LibraryQuestionCountResponse") {
	"""题库id"""
	libraryId:String
	"""试题数量"""
	questionCount:Int!
}
"""平台考试统计数据
	@author: eleven
	@date: 2020/3/14
"""
type PlatformExamStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.PlatformExamStatisticDTO") {
	"""累计考试次数"""
	totalExamCount:Double!
	"""累计练习刷题次数"""
	totalPracticeCount:Double!
}
"""练习正确率趋势统计
	@author: eleven
	@date: 2020/3/5
"""
type PracticeCorrectRateTrendDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.PracticeCorrectRateTrendDTO") {
	"""答题记录卷id"""
	answerExamRecordId:String
	"""答题时间"""
	answerPaperTime:String
	"""总题数"""
	totalCount:Double!
	"""已做答题数"""
	answeredCount:Double!
	"""答对题数"""
	correctCount:Double!
	"""答错题数"""
	wrongCount:Double!
	"""正确率"""
	correctRate:Double!
}
"""练习题量趋势统计
	@author: eleven
	@date: 2020/3/5
"""
type PracticeCountTrendDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.PracticeCountTrendDTO") {
	"""答题时间"""
	answerPaperDate:String
	"""答题量"""
	answerCount:Double!
}
"""练习频率统计
	@author: eleven
	@date: 2020/3/5
"""
type PracticeFrequencyStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.PracticeFrequencyStatisticDTO") {
	"""累计练习天数"""
	practiceDayCount:Int!
	"""近N日增加的练习天数"""
	recentAddPracticeDayCount:Int!
}
"""首页数据统计 - 试题答题次数统计
	@author: eleven
	@date: 2020/4/18
"""
type QuestionAnswerCountDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.QuestionAnswerCountDTO") {
	"""答题日期"""
	date:String
	"""真题答题数"""
	real:Double!
	"""练习答题数"""
	practice:Double!
	"""模拟题答题数"""
	simulation:Double!
}
"""试题类型统计
	@author: eleven
	@date: 2020/4/20
"""
type QuestionCategoryCountDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.QuestionCategoryCountDTO") {
	"""真题数量"""
	real:Long!
	"""练习题数量"""
	practice:Long!
	"""模拟题数量"""
	simulation:Long!
	"""合计数量"""
	total:Long!
}
"""试题正确率统计
	@author: eleven
	@date: 2020/4/18
"""
type QuestionCorrectRateDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.QuestionCorrectRateDTO") {
	"""真题答题情况"""
	real:QuestionStatisticBaseDTO
	"""练习题答题情况"""
	practice:QuestionStatisticBaseDTO
	"""模拟题答题情况"""
	simulation:QuestionStatisticBaseDTO
	"""合计答题情况"""
	total:QuestionStatisticBaseDTO
}
type QuestionStatisticBaseDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.QuestionStatisticBaseDTO") {
	"""已做答题数"""
	answeredCount:Double!
	"""答对题数"""
	correctCount:Double!
	"""对题率"""
	correctRate:Double!
}
"""@author: eleven
	@date: 2020/3/4
"""
type QuestionStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.QuestionStatisticDTO") {
	"""标签id - 按标签分组统计，该字段才生效"""
	tags:[String]
	"""试题类型 - 按题型分组统计，该字段才生效"""
	questionType:Int!
	"""题类  - 按题类分组统计，该字段才生效
		支持的试题类型：真题练习、模拟题、练习题
	"""
	questionCategory:String
	"""试题数量"""
	count:Long!
}
"""用户单题试题的答题情况
	@author: eleven
	@date: 2020/3/4
"""
type SingleQuestionAnswerStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.SingleQuestionAnswerStatisticDTO") {
	"""试题id"""
	questionId:String
	"""已答题次数"""
	answeredTimes:Int!
	"""答对次数"""
	correctTimes:Int!
	"""答错次数"""
	wrongTimes:Int!
	"""未答次数,未作答"""
	unknownTimes:Int!
	"""正确率"""
	correctRate:Double!
	"""全站已答题次数"""
	allAnsweredTimes:Int!
	"""全站答对次数"""
	allCorrectTimes:Int!
	"""全站答错次数"""
	allWrongTimes:Int!
	"""全站未答次数,未作答"""
	allUnknownTimes:Int!
	"""全站正确率"""
	allCorrectRate:Double!
}
"""试题类型答题统计
	@author: eleven
	@date: 2020/3/23
"""
type UserAnswerQuestionCountDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.UserAnswerQuestionCountDTO") {
	"""真题答题数"""
	real:Double!
	"""练习答题数"""
	practice:Double!
	"""模拟题答题数"""
	simulation:Double!
}
"""用户模考答题正确率统计
	@author: eleven
	@date: 2020/3/5
"""
type UserExamQuestionCorrectRateStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.UserExamQuestionCorrectRateStatisticDTO") {
	"""单选"""
	single:UserQuestionCorrectStatistic
	"""多选"""
	multiple:UserQuestionCorrectStatistic
	"""判断"""
	judgement:UserQuestionCorrectStatistic
	"""案例题（综合题）"""
	comprehensive:UserQuestionCorrectStatistic
}
"""用户模考据统计
	@author: eleven
	@date: 2020/3/5
"""
type UserExamStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.UserExamStatisticDTO") {
	"""考试次数"""
	examCount:Double!
	"""平均分"""
	aveScore:Double!
	"""最高分"""
	maxScore:Double!
	"""平均分网校排名"""
	examRank:Int!
	"""做题总量"""
	totalAnswerCount:Double!
	"""答对题量"""
	correctAnswerCount:Double!
	"""正确率"""
	correctRate:Double!
	"""平均用时"""
	avgUseTime:Double!
	"""最短用时"""
	minUseTime:Double!
}
"""@author: eleven
	@date: 2020/3/5
"""
type UserFavoriteQuestionStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.UserFavoriteQuestionStatisticDTO") {
	"""标签id - 按标签分组统计，该字段才生效"""
	tags:[String]
	"""试题题型 - 按题型分组统计，该字段才生效
		0/1/2/3/4/5/6/7 未知/判断/单选/多选/填空/简答/父子/混合
		@see com.fjhb.platform.component.exam.commons.api.consts.question.QuestionType
	"""
	questionType:String
	"""题类  - 按题类分组统计，该字段才生效
		支持的试题类型：真题练习、模拟题、练习题
	"""
	questionCategory:String
	"""试题数量"""
	questionCount:Long!
}
"""用户最后一次的练习答题记录数据
	@author: eleven
	@date: 2020/3/5
"""
type UserLastPracticeRecordDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.UserLastPracticeRecordDTO") {
	"""练习类型"""
	practiceType:PracticeType
	"""抽题题型
		当practiceMode =  PracticeMode.QUESTION_TYPE 有效
		@see com.fjhb.platform.component.exam.commons.api.consts.question.QuestionType
	"""
	questionType:Int!
	"""练习模式"""
	practiceMode:PracticeMode
	"""方案id"""
	schemeId:String
	"""方式id"""
	learningId:String
	"""答题记录卷id"""
	answerExamRecordId:String
	"""答卷id"""
	answerPaperId:String
	"""引用的试卷"""
	examPaperId:String
	"""标签id"""
	tagId:String
	"""已答题数（答题记录卷的已作答题数）"""
	hasAnswerCount:Double!
	"""总题数（答题记录卷的总题数）"""
	totalCount:Double!
	"""答题进度 - 完成度
		已答题数/总题数
	"""
	answerSchedule:Double!
	"""作答完成时间 -可能为空
		当当前答题记录卷还在作答中的话，则该字段为空，对应取更新时间updateTime
	"""
	answerPaperCompleteTime:String
	"""数据更新时间"""
	updateTime:String
}
"""练习相关统计 -
	@author: eleven
	@date: 2020/3/5
"""
type UserPracticeStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.UserPracticeStatisticDTO") {
	"""累计做题总量"""
	totalAnswerCount:Double!
	"""正确题次"""
	totalCorrectCount:Double!
	"""正确率"""
	correctRate:Double!
}
"""用户答题正确率统计
	@author: eleven
	@date: 2020/4/18
"""
type UserQuestionAnswerCorrectRateDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.UserQuestionAnswerCorrectRateDTO") {
	"""用户名"""
	userName:String
	"""用戶id"""
	userId:String
	"""累计答题次数"""
	answerCount:Double!
	"""答对题数"""
	correctCount:Double!
	"""对题率"""
	correctRate:Double!
}
"""用户答题次数统计
	@author: eleven
	@date: 2020/4/18
"""
type UserQuestionAnswerCountDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.UserQuestionAnswerCountDTO") {
	"""用户名称"""
	userName:String
	"""用戶id"""
	userId:String
	"""累计答题次数"""
	answerCount:Double!
}
"""统计用户对题率 - 因为通过题数获取对题率，所以连带答题情况一起返回
	@author: eleven
	@date: 2020/3/8
"""
type UserQuestionCorrectStatistic @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.UserQuestionCorrectStatistic") {
	"""已做答题数"""
	answeredCount:Double!
	"""答对题数"""
	correctCount:Double!
	"""答错题数"""
	wrongCount:Double!
	"""对题率"""
	correctRate:Double!
}
"""用户错题统计数据
	@author: eleven
	@date: 2020/3/5
"""
type UserWrongQuestionStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.UserWrongQuestionStatisticDTO") {
	"""标签id集合 - 按标签分组统计，该字段才生效"""
	tagIds:[String]
	"""试题题型 - 按题型分组统计，该字段才生效"""
	questionType:String
	"""题类  - 按题类分组统计，该字段才生效
		支持的试题类型：真题练习、模拟题、练习题
	"""
	questionCategory:String
	"""试题数量"""
	questionCount:Long!
}
"""我的错题汇总统计
	做题记录 - 错题重答
	错题相关统计的是题数，而非题次
	@author: eleven
	@date: 2020/3/5
"""
type UserWrongQuestionStatisticSummaryDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.UserWrongQuestionStatisticSummaryDTO") {
	"""错题总数"""
	wrongQuestionCount:Long!
	"""作答试题总次数"""
	questionAnsweredTimes:Long!
	"""错题总次数"""
	wrongQuestionTimes:Long!
	"""错题率 错题总次数/累计做题总次数"""
	wrongRate:Double!
}
"""用户答题统计-每天
	@author: linj
	@date: 2020/3/25
"""
type AnswerQuestionStatisticPerDayDTO @type(value:"com.fjhb.btpx.integrative.service.exam.dto.response.exam.answered.statistic.AnswerQuestionStatisticPerDayDTO") {
	"""日期 yyyy-MM-dd"""
	day:String
	"""已答试题数"""
	answeredCount:Long!
	"""已答题次"""
	answeredTimes:Long!
	"""答对题次"""
	correctTimes:Long!
	"""答错题次"""
	errorTimes:Long!
	"""正确率"""
	correctRate:Double!
	"""错误率"""
	errorRate:Double!
}
"""试卷类型
	@see com.fjhb.platform.component.exam.commons.api.consts.exam.ExamConfigType
	@author: eleven
	@date: 2020/3/4
"""
enum PaperConfigType @type(value:"com.fjhb.btpx.integrative.service.exam.enums.PaperConfigType") {
	"""固定卷"""
	FIXED
	"""AB卷"""
	GROUP
	"""固定卷"""
	RANDOM
}
"""答题记录卷状态
	@author: eleven
	@date: 2020/3/1
"""
enum AnswerStatusEnum @type(value:"com.fjhb.btpx.platform.dao.elasticsearch.enums.AnswerStatusEnum") {
	"""进入答卷"""
	ENTER
	"""阅卷结束"""
	MARKED_FINISH
	"""逻辑删除"""
	DELETE
}
"""@author: eleven
	@date: 2020/3/1
"""
enum ExamPaperApplyType @type(value:"com.fjhb.btpx.platform.dao.elasticsearch.enums.ExamPaperApplyType") {
	"""考试"""
	EXAM
	"""练习"""
	PRACTICE
}
"""@author: eleven
	@date: 2020/3/1
"""
enum PracticeMode @type(value:"com.fjhb.btpx.platform.dao.elasticsearch.enums.PracticeMode") {
	"""考纲抽题
		@see PreExamFetchParamDTO#fetchWay
	"""
	OUTLINE
	"""随机题"""
	RANDOM
	"""题型抽题"""
	QUESTION_TYPE
}
"""@author: eleven
	@date: 2020/3/1
"""
enum PracticeType @type(value:"com.fjhb.btpx.platform.dao.elasticsearch.enums.PracticeType") {
	"""真题练习
		@see com.fjhb.platform.core.exam.kq.v1.api.consts.PreExamQuestionCategory
	"""
	REAL
	"""模拟题"""
	SIMULATION
	"""练习题"""
	PRACTICE
	"""随机题"""
	RANDOM
	"""每日一练"""
	DAILY
	"""收藏题"""
	FAVORITE
	"""易错题"""
	ERROR_PRONE
	"""错题重练"""
	CORRECTION
	"""考试"""
	EXAM
}
type ExamObjectDto @type(value:"com.fjhb6.ability.exam.v1.south.service.dto.ExamObjectDto") {
	objectId:String
	type:String
}
type ExamLibraryDto @type(value:"com.fjhb6.ability.exam.v1.south.service.dto.examLib.ExamLibraryDto") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	organizationId:String
	name:String
	id:String
	share:Boolean!
	examObjects:[ExamObjectDto]
	description:String
	parentId:String
	enabled:Boolean!
	createTime:String
	createUserId:String
	lastChangeTime:String
	sourceType:Int!
	sourceId:String
	rootId:String
	token:String
}

scalar List
type ExamLibraryDtoPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ExamLibraryDto]}
type AnswerRecordDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [AnswerRecordDTO]}
