class Utils {
  /**
   * 查找节点路径
   * @param {T[]} tree - 数结构
   * @param {any} func - 条件表达式
   * @param {string} key - 单层节点路径，默认：'id'
   * @param {string} childKey - 子节点key值，默认：'children'
   * @return {string[]} 节点路径
   */
  static treeFindPath<T>(tree: T[], func: any, key = 'id', childKey = 'children'): string[] {
    if (!Array.isArray(tree) || tree.length === 0) return [] as string[]
    const path = [] as string[]
    const findPath = (tree: T[], key: string, path: string[]): string[] => {
      for (const item of tree) {
        path.push(item[key])
        if (func(item)) return path
        if (item[childKey] && item[childKey].length) {
          const findChildren = findPath(item[childKey], key, path)
          if (findChildren.length) {
            return findChildren as string[]
          }
        }
        path.pop()
      }
      return [] as string[]
    }
    return findPath(tree, key, path)
  }
}

export default Utils
