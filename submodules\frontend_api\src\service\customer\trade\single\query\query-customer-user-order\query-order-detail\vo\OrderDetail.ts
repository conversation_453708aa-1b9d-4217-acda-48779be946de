import OrderInfo, {
  OrderInfoParse
} from '@api/service/customer/trade/single/query/query-customer-user-order/vo/OrderInfo'
import { OrderDTO as GqlOrderDto } from '@api/gateway/PlatformTrade'
import PayInfo, { PayInfoParse } from '@api/service/customer/trade/single/query/query-customer-user-order/vo/PayInfo'
import SchemeCommodity, {
  SchemeCommodityParse
} from '@api/service/customer/trade/single/query/query-customer-user-order/vo/SchemeCommodity'
export default class OrderDetail {
  // 订单基础信息
  orderInfo: OrderInfo = new OrderInfo()
  // 商品列表
  schemeCommodityList: SchemeCommodity[] = []
  // 支付信息
  payInfo: PayInfo = new PayInfo()

  get schemeCommodity() {
    return this.schemeCommodityList && this.schemeCommodityList.length > 0
      ? this.schemeCommodityList[0]
      : new SchemeCommodity()
  }
  /**
   * @description: 转化成OrderDetail模型
   * @param {*}
   * @return {*}
   */

  static toOrderDetail(gqlOrderDto: GqlOrderDto): OrderDetail {
    const orderDetail = new OrderDetail()
    orderDetail.orderInfo = OrderInfoParse.parseOrderInfo(gqlOrderDto)
    orderDetail.schemeCommodityList = gqlOrderDto.subOrderList?.map(subOrder => {
      return SchemeCommodityParse.parseSchemeCommodity(subOrder)
    })
    orderDetail.payInfo = PayInfoParse.parsePayInfo(gqlOrderDto)
    return orderDetail
  }
}
