import ReceiveAccountDetailVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountDetailVo'
import { PayAccountTypeEnum } from '@api/service/management/trade-info-config/enums/PayAccountTypeEnum'
import {
  CIBPayEncryptionKeyDataResponse,
  ReceiveAccountConfigResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

export class XYPayReceiveAccountDetailVo extends ReceiveAccountDetailVo {
  /**
   * 账号类型
   */
  paymentChannelId: PayAccountTypeEnum = undefined

  /**
   * 应用id
   */
  xyPayAppId = ''
  /**
   * 终端编号
   */
  terminalId = ''
  /**
   * SM2签名私钥
   */
  sm2key = ''
  /**
   * 响应公钥
   */
  resPublicKey = ''
  /**
   * 请求私钥
   */
  reqKey = ''
  /**
   * 公众号或小程序id
   */
  xyPaySubAppId = ''

  from(res: ReceiveAccountConfigResponse) {
    this.accountType = res.accountType
    this.accountNo = res.accountNo
    this.accountName = res.name
    this.taxPayerId = res.taxPayerId
    this.refundWay = res.returnType
    this.qrScanPrompt = res.qrScanPrompt
    this.paymentChannelId = res.paymentChannelId as PayAccountTypeEnum
    if (res.encryptionKeyData.encryptionKeyType == 'CIBPay') {
      const temp = res.encryptionKeyData as CIBPayEncryptionKeyDataResponse
      this.xyPayAppId = temp.appId
      this.terminalId = temp.terminalId
      this.sm2key = temp.requestPrivateKey
      this.resPublicKey = temp.responsePublicKey
      this.reqKey = temp.requestParamEncryptKey
      this.xyPaySubAppId = temp.subAppid
    }
  }
}
