.bare-nav-wrapper {
  height: 40px;
  line-height: 40px;
  padding: 0;
  position: relative;
  background: #f5f7f9;
  z-index: 100;
  box-sizing: content-box;
  flex: none;

  .bare-nav-left, .bare-nav-right, .bare-nav-operation {
    height: 100%;
    background: #fff;
    position: absolute;
    z-index: 100;

    button {
      line-height: 1.9;

      &:focus {
        box-shadow: none;
      }

      padding: 6px 4px;
    }
  }

  .bare-nav-operation {
    right: 0;
  }

  .bare-nav-right {
    right: 25px;
  }

  .bare-nav-container {
    position: absolute;
    overflow: hidden;
    left: 25px;
    right: 50px;
    bottom: 0;
    top: 0;
    user-select: none;
    height: 40px;

    .bare-nav-scroll-body {
      display: inline-block;
      height: calc(100% - 1px);
      overflow: visible;
      position: absolute;
      transition: all .3s ease;
      white-space: nowrap;

      .el-tag {
        border: none;
        border-right: 1px solid #e4e8f2;
        background-color: transparent;
        height: 40px;
        line-height: 40px;
        padding: 0 15px;
        color: #999;
        font-size: 13px;
        cursor: pointer;

        -webkit-transition: all ease .3s;
        -moz-transition: all ease .3s;
        -ms-transition: all ease .3s;
        -o-transition: all ease .3s;
        transition: all ease .3s;

        &:hover {
          color: #2d8cf0;

          .dot {
            background: #2d8cf0;
          }
        ;
        }

        .currentText {
          color: #2d8cf0;
        }

        .dot {
          border-radius: 50%;
          display: inline-block;
          background-color: #dddddd;
          height: 10px;
          width: 10px;
          margin-right: 5px;

          &.primary {
            background: #2d8cf0;
          }
        }

        i {
          color: #bdc0c7;
          font-weight: bold;

          &:hover {
            color: #ffffff;
          }
        }
      }
    }
  }
}
