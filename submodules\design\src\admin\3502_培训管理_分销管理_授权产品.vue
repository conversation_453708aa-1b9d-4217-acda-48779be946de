<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">分销管理</el-breadcrumb-item>
      <el-breadcrumb-item>授权产品</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">可选培训方案</span>
        </div>
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训方案名称">
                <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训方案类型">
                <el-select v-model="select" clearable placeholder="请选择培训方案类型">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="年度">
                <el-select v-model="select" clearable placeholder="请选择年度">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!--<el-col :sm="12" :md="8" :xl="6">-->
            <!--  <el-form-item label="地区">-->
            <!--    <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" />-->
            <!--  </el-form-item>-->
            <!--</el-col>-->
            <!--<el-col :sm="12" :md="8" :xl="6">-->
            <!--  <el-form-item label="行业">-->
            <!--    <el-select v-model="select" clearable placeholder="请选择行业">-->
            <!--      <el-option value="选项1"></el-option>-->
            <!--      <el-option value="选项2"></el-option>-->
            <!--    </el-select>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->
            <!--选择行业后展示原型上对应的内容-->
            <!--<el-col :sm="12" :md="8" :xl="6">-->
            <!--  <el-form-item label="科目类型">-->
            <!--    <el-select v-model="select" clearable placeholder="请选择行业">-->
            <!--      <el-option value="选项1"></el-option>-->
            <!--      <el-option value="选项2"></el-option>-->
            <!--    </el-select>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->
            <!--<el-col :sm="12" :md="8" :xl="6">-->
            <!--  <el-form-item label="培训专业">-->
            <!--    <el-select v-model="select" clearable placeholder="请选择培训专业">-->
            <!--      <el-option value="选项1"></el-option>-->
            <!--      <el-option value="选项2"></el-option>-->
            <!--    </el-select>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->
            <!--<el-col :sm="12" :md="8" :xl="6">-->
            <!--  <el-form-item label="培训类别">-->
            <!--    <el-select v-model="select" clearable placeholder="请选择培训类别">-->
            <!--      <el-option value="选项1"></el-option>-->
            <!--      <el-option value="选项2"></el-option>-->
            <!--    </el-select>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->
            <!--<el-col :sm="12" :md="8" :xl="6">-->
            <!--  <el-form-item label="培训对象">-->
            <!--    <el-select v-model="select" clearable placeholder="请选择培训对象">-->
            <!--      <el-option value="选项1"></el-option>-->
            <!--      <el-option value="选项2"></el-option>-->
            <!--    </el-select>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->
            <!--<el-col :sm="12" :md="8" :xl="6">-->
            <!--  <el-form-item label="岗位类别">-->
            <!--    <el-select v-model="select" clearable placeholder="请选择岗位类别">-->
            <!--      <el-option value="选项1"></el-option>-->
            <!--      <el-option value="选项2"></el-option>-->
            <!--    </el-select>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->
            <!--<el-col :sm="12" :md="8" :xl="6">-->
            <!--  <el-form-item label="报名状态">-->
            <!--    <el-select v-model="select" clearable placeholder="请选择报名状态">-->
            <!--      <el-option value="选项1"></el-option>-->
            <!--      <el-option value="选项2"></el-option>-->
            <!--    </el-select>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->
            <el-col :sm="12" :md="8" :xl="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>重置</el-button>
                <el-button type="text">展开<i class="el-icon-arrow-down el-icon--right"></i></el-button>
                <!--<el-button type="text">收起<i class="el-icon-arrow-up el-icon&#45;&#45;right"></i></el-button>-->
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="培训方案名称" width="320" fixed="left">
            <template>
              <el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag>培训方案名称培训方案名称
            </template>
          </el-table-column>
          <el-table-column label="方案学时" min-width="120" align="right">
            <template>5 </template>
          </el-table-column>
          <el-table-column label="方案价格" min-width="120" align="right">
            <template><i class="f-mr5">¥</i>99</template>
          </el-table-column>
          <el-table-column label="培训属性" min-width="240">
            <template>
              <p class="f-mb5">人社行业</p>
              <div class="f-c9 f-f12">
                <p>培训年度：读取方案的培训年度</p>
                <p>培训地区：读取培训方案的地区属性值</p>
                <p>科目类别：读取方案的科目属性值</p>
                <p>培训类别：方案的专业属性值</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学习起止时间" min-width="220">
            <template>
              <p><el-tag type="info" size="mini" class="f-mr5">开始</el-tag>2023-08-14 09:56:20</p>
              <p><el-tag type="info" size="mini" class="f-mr5">结束</el-tag>2023-08-14 09:56:20</p>
            </template>
          </el-table-column>
          <el-table-column label="报名起止时间" sortable min-width="220">
            <template>
              <p><el-tag type="info" size="mini" class="f-mr5">开始</el-tag>2023-08-14 09:56:20</p>
              <p><el-tag type="info" size="mini" class="f-mr5">结束</el-tag>2023-08-14 09:56:20</p>
            </template>
          </el-table-column>
          <el-table-column label="查看" width="80" align="center" fixed="right">
            <template>
              <el-button type="text" size="mini">详情</el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template>
              <el-checkbox v-model="checked">选择</el-checkbox>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-pagination
          background
          class="f-mt15 f-tr"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage4"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
        >
        </el-pagination>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">合作供应商信息</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="授权供应商名称：" class="is-text">很会带货的公司</el-form-item>
              <el-form-item label="合作状态：">
                <el-tag type="success" size="small">合作中</el-tag>
                <!--<el-tag size="small">未开始</el-tag>-->
                <!--<el-tag type="danger" size="small">中止合作</el-tag>-->
                <!--<el-tag type="info" size="small">已结束</el-tag>-->
                <!--<el-tag type="warning" size="small">即将到期</el-tag>-->
              </el-form-item>
              <el-form-item label="合作周期：" class="is-text">
                2023-08-26 00:00:00<i class="f-mlr10">~</i>2023-12-30 00:00:00
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">已选培训方案授权供应商信息（已选 X 个方案）</span>
        </div>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="培训方案名称" width="320" fixed="left">
            <template>
              <el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag>培训方案名称培训方案名称
            </template>
          </el-table-column>
          <el-table-column label="培训属性" min-width="240">
            <template>
              <p class="f-mb5">人社行业</p>
              <div class="f-c9 f-f12">
                <p>培训年度：读取方案的培训年度</p>
                <p>培训地区：读取培训方案的地区属性值</p>
                <p>科目类别：读取方案的科目属性值</p>
                <p>培训类别：方案的专业属性值</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="指导价格类型" width="200">
            <template slot="header">
              指导价格类型
              <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-c9"></i>
                <div slot="content">
                  <p>
                    设置方案授权给供应商进行分销指导价格。供应商可基于指导价格设置方案的分销价格并授权给分销商进行推广，支持按固定价格和按价格区间两种类型设置。
                  </p>
                  <p>① 按照固定价格：方案的分销价格不能低于指导价格。</p>
                  <p>② 按价格区间设置：方案的分销价格不能低于指导价格最低价，不能高于分销价格的最高价。</p>
                </div>
              </el-tooltip>
            </template>
            <template>
              <el-form :rules="rules" class="m-form is-table">
                <el-form-item>
                  <el-radio-group v-model="form.resource">
                    <el-radio label="按固定价格设置"></el-radio>
                    <el-radio label="按价格区间设置"></el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column label="指导价格(元/人)" width="240" align="center">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-form :rules="rules" class="m-form is-table">
                  <el-form-item prop="name">
                    最低：
                    <el-input-number size="mini" v-model="rules.name" :precision="2"></el-input-number>
                  </el-form-item>
                  <el-form-item prop="name">
                    最高：
                    <el-input-number size="mini" v-model="rules.name" :precision="2"></el-input-number>
                  </el-form-item>
                </el-form>
              </div>
              <div v-else>
                <el-form :rules="rules" class="m-form is-table">
                  <el-form-item prop="name">
                    <el-input-number size="mini" v-model="rules.name" :precision="2"></el-input-number>
                  </el-form-item>
                </el-form>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="销售开始时间" width="330">
            <template slot="header">
              销售开始时间
              <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-c9"></i>
                <div slot="content">
                  设置方案授权给指定供应商进行分销的开始时间。如分销有效日期未开始，方案在网校开放报名时间已开始，此时该方案不能进行分销。如需开展方案的分销，请开启分销开始日期。
                </div>
              </el-tooltip>
            </template>
            <template>
              <el-form :rules="rules" class="m-form is-table">
                <el-form-item prop="name">
                  <el-radio-group v-model="form.resource">
                    <el-radio label="立即开启"></el-radio>
                    <el-radio>
                      指定开启日期
                      <el-date-picker
                        v-model="form.date1"
                        type="datetime"
                        size="mini"
                        placeholder="请选择开始时间"
                        class="form-l f-ml10"
                      >
                      </el-date-picker>
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column label="销售结束时间" width="330">
            <template slot="header">
              销售结束时间
              <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-c9"></i>
                <div slot="content">
                  设置方案授权给指定供应商进行分销的截止时间。如分销有效日期未截止，方案在网校开放报名时间已截止，不影响方案的分销。如需暂停方案的分销，请关闭分销截止日期。
                </div>
              </el-tooltip>
            </template>
            <template>
              <el-form :rules="rules" class="m-form is-table">
                <el-form-item prop="name">
                  <el-radio-group v-model="form.resource">
                    <el-radio label="无关闭时间"></el-radio>
                    <el-radio>
                      指定关闭日期
                      <el-date-picker
                        v-model="form.date1"
                        type="datetime"
                        size="mini"
                        placeholder="请选择关闭日期"
                        class="form-l f-ml10"
                      >
                      </el-date-picker>
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column label="方案学时" min-width="120" align="right">
            <template>5 </template>
          </el-table-column>
          <el-table-column label="方案价格" min-width="120" align="right">
            <template><i class="f-mr5">¥</i>99</template>
          </el-table-column>
          <el-table-column label="学习起止时间" min-width="220">
            <template>
              <p><el-tag type="info" size="mini" class="f-mr5">开始</el-tag>2023-08-14 09:56:20</p>
              <p><el-tag type="info" size="mini" class="f-mr5">结束</el-tag>2023-08-14 09:56:20</p>
            </template>
          </el-table-column>
          <el-table-column label="报名起止时间" min-width="220">
            <template>
              <p><el-tag type="info" size="mini" class="f-mr5">开始</el-tag>2023-08-14 09:56:20</p>
              <p><el-tag type="info" size="mini" class="f-mr5">结束</el-tag>2023-08-14 09:56:20</p>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template>
              <el-button type="text" size="mini">取消授权</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-pagination
          background
          class="f-mt15 f-tr"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage4"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
        >
        </el-pagination>
        <!--空数据-->
        <div class="m-no-date">
          <img class="img" src="./assets/images/no-data-normal.png" alt="" />
          <div class="date-bd">
            <p class="f-f15 f-c9">请先选择培训方案</p>
          </div>
        </div>
      </el-card>
    </div>
  </el-main>
</template>

<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        isCollapse: false,
        checked: false,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        rules: {
          name: [{ required: true, message: '这里是错误提示', trigger: 'blur' }]
        },
        value1: '',
        tableData: [{}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
