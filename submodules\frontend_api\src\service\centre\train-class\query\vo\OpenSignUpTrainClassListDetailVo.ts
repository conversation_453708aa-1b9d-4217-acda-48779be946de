import {
  CommoditySkuForestageResponse,
  CommoditySkuPropertyResponse,
  PortalCommoditySkuPropertyResponse,
  SchemeResourceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import SkuPropertyConvertUtils from '@api/service/centre/train-class/util/SkuPropertyConvertUtils'
import TrainClassUtils from '@api/service/centre/train-class/util/TrainClassUtils'
import SkuPropertyResponseVo from '@api/service/customer/train-class/query/vo/SkuPropertyResponseVo'
import SkuPropertyConvertUtilsNew, {
  SchemeSkuInfo
} from '@api/service/customer/train-class/Utils/SkuPropertyConvertUtils'
import { SchemeSkuPropertyResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'

/**
 * @description 【无需登录】开放报名的方案列表详情
 */
class OpenSignUpTrainClassListDetailVo extends CommoditySkuForestageResponse {
  /**
   * 培训方案Id
   */
  schemeId = ''

  /**
   * 培训班商品Id
   */
  commoditySkuId = ''

  /**
   * 培训方案名称
   */
  schemeName = ''

  /**
   * 商品属性信息
   */
  skuProperty: CommoditySkuPropertyResponse = new CommoditySkuPropertyResponse()

  /**
   * 班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuValueNameProperty: SkuPropertyResponseVo = new SkuPropertyResponseVo()

  /**
   * 学时
   */
  period: number = null

  /**
   * 培训班价格
   */
  price: number = null

  /**
   * 培训班封面
   */
  imgPath = ''

  /**
   * 转换远端模型为本地vo模型
   */
  static async from(response: CommoditySkuForestageResponse): Promise<OpenSignUpTrainClassListDetailVo> {
    const detail = new OpenSignUpTrainClassListDetailVo()
    detail.commoditySkuId = response.commoditySkuId ?? ''
    detail.skuProperty = response.skuProperty ?? null
    detail.schemeId = (response.resource as SchemeResourceResponse).schemeId ?? ''
    detail.schemeName = response.commodityBasicData?.saleTitle ?? ''
    detail.price = response.commodityBasicData?.price ?? null
    detail.imgPath = response.commodityBasicData?.commodityPicturePath ?? ''
    try {
      const skuDto = Object.assign(new SchemeSkuPropertyResponse(), new PortalCommoditySkuPropertyResponse())
      const skuVo = await SkuPropertyConvertUtilsNew.convertToSkuPropertyResponseVo(
        Object.assign(skuDto, detail.skuProperty)
      )
      detail.skuValueNameProperty = skuVo
      const configJson = await TrainClassUtils.queryTrainClassConfig(detail.schemeId)
      if (configJson) {
        const creditResult = configJson.assessSetting.learningResults.find((item: any) => {
          return item.type == 1
        })
        detail.period = creditResult.grade
      }
    } catch (e) {
      console.log(e)
    }
    return detail
  }

  /**
   * 转换远端模型为本地vo模型
   */
  static fromBatch(
    response: CommoditySkuForestageResponse,
    schemeConfigs: Map<string, any>,
    skuVoList: SchemeSkuInfo[]
  ): OpenSignUpTrainClassListDetailVo {
    const detail = new OpenSignUpTrainClassListDetailVo()
    const resource = (response.resource as SchemeResourceResponse) || new SchemeResourceResponse()
    detail.commoditySkuId = response.commoditySkuId ?? ''
    detail.skuProperty = response.skuProperty ?? null
    detail.schemeId = resource.schemeId ?? ''
    detail.schemeName = response.commodityBasicData?.saleTitle ?? ''
    detail.price = response.commodityBasicData?.price ?? null
    detail.imgPath = response.commodityBasicData?.commodityPicturePath ?? ''
    detail.period = resource.period ?? null
    let findSku: SchemeSkuInfo = null
    if (skuVoList) {
      findSku = skuVoList.find((item) => {
        return item.id == detail.schemeId
      })
    }
    if (findSku) {
      detail.skuValueNameProperty = findSku.skuName
    } else {
      detail.skuValueNameProperty = new SkuPropertyResponseVo()
    }
    return detail
  }
}

export default OpenSignUpTrainClassListDetailVo
