import { QuestionnairePreconditionEnum } from '@api/service/customer/question-naire/enums/QuestionnairePreconditionEnum'
import QuestionAnswer from '@api/service/customer/question-naire/models/QuestionAnswer'
import MsQuestionnaire from '@api/ms-gateway/ms-questionnaire-v1'
import MsExamAnswer, {
  ApplyPaperAnswerResponse,
  AskQuestionAnswer,
  MultipleQuestionAnswer,
  PreviewPaperPublishConfigureResponse,
  BaseQuestionResponse as BaseQuestionnaireResponse,
  RadioQuestionAnswer,
  ScaleQuestionAnswer,
  AskQuestionResponse1,
  MultipleQuestionResponse1,
  RadioQuestionResponse1,
  ScaleQuestionResponse1
} from '@api/ms-gateway/ms-exam-answer-v1'
import MsStudentLearning, {
  AskQuestionResponse as AskResponse,
  MultipleQuestionResponse as MultipleResponse,
  RadioQuestionResponse as RadioResponse,
  ScaleQuestionResponse as ScaleResponse
} from '@api/ms-gateway/ms-studentlearning-v1'

import { QuestionnaireStatisEnum } from '@api/service/customer/question-naire/enums/QuestionnaireStatisEnum'
import MsExamQueryFrontGatewayQuestionnaireQueryForeStage, {
  FixedPaper,
  GetQuestionnaireDetailRequest,
  SurveyQuestionnaireResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryForeStage'
import MsExamQueryFrontGatewayExamQueryForeStage, {
  BaseQuestionResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryForeStage'
import { QuestionTypeEnum } from '@api/service/common/enums/question-naire/QuestionType'
import { ResponseStatus } from '@hbfe/common'

/**
 * 问卷详情
 */
export default class Questionnaire {
  /**
   * 问卷Token
   */
  token = ''
  /**
   * 问卷id
   */
  questionnaireId = ''
  /**
   * 问卷模板id
   */
  templateId = ''
  /**
   * 答卷id
   */
  answerPaperId = ''
  /**
   * 问卷学习方式id
   */
  questionnaireLearningId = ''
  /**
   * 问卷名称
   */
  questionnaireName = ''
  /**
   * 问卷开放时间
   */
  questionnaireBeginTime = ''

  /**
   * 问卷截止时间
   */
  questionnaireEndTime = ''
  /**
   * 问卷状态
   */
  questionnaireStatis: QuestionnaireStatisEnum = null
  /**
   * 问卷前置条件
   */
  questionnairePrecondition: QuestionnairePreconditionEnum = null
  /**
   * 问卷是否已作答
   */
  isSubmitQuestionnaire = false
  /**
   * 是否开放问卷统计配置
   */
  isOpenQuestionnaireStatistics = false
  /**
   * 应用范围
   */
  usedRange = 0
  /**
   * 问卷说明
   */
  questionnaireDescription = ''
  /**
   * 问卷内试题
   */
  questions: Array<QuestionAnswer> = new Array<QuestionAnswer>()
  /**
   * 时长
   */
  timeLength = 0
  /**
   * 总分
   */
  totalScore = 0
  /**
   * 题目总数
   */
  totalQuestionCount = 0
  /**
   * 试卷类型
   * 后端模型使用
   */
  paperType = 0
  /**
   * 出卷时间
   */
  answerExtractionTime = ''
  /**
   * 问卷问题存储对象Map
   * 交卷时后端返回的问题模型需要交还给后端
   */
  questionMap = new Map<string, BaseQuestionnaireResponse>()
  /**
   * 是否纳入考核
   */
  includedInAssessment = false
  /**
   * 申请问卷调查
   * 进入页面前需要先调用
   */
  async doApplyQuestionnaire(studentToken: string) {
    const res = await MsStudentLearning.applyQuestionnaire({ studentToken })
    if (res.status.isSuccess() && res.data) {
      this.token = res.data.answerToken
      this.answerExtractionTime = res.data.answerExtractionTime
      if (res.data.answerPaperViewResponse) {
        const data = res.data.answerPaperViewResponse
        this.questionnaireName = data.name
        this.questionnaireDescription = data.description
        this.timeLength = data.timeLength
        this.totalScore = data.totalScore
        this.totalQuestionCount = data.totalQuestionCount
        this.paperType = data.paperType
        this.questions = (data.questions || []).map((item) => {
          this.questionMap.set(item.id, item)
          return QuestionAnswer.from(item as AskResponse | MultipleResponse | RadioResponse | ScaleResponse)
        })
      }
    }
    return res
  }
  /**
   * 查询问卷试题
   * 第一次答题时获取问卷题目
   */
  async queryQuestions(studentToken: string, issueId?: string) {
    const tokenRes = await this.doApplyQuestionnaire(studentToken)
    return tokenRes.status
  }
  /**
   * 查看问卷
   * 获取已答完的作答详情
   */
  async queryAnsweredQuestions(issueId?: string) {
    const request = new GetQuestionnaireDetailRequest()
    request.id = this.answerPaperId
    const res = await MsExamQueryFrontGatewayQuestionnaireQueryForeStage.getQuestionnaireDetailInMyself(request)
    const paper = res.data
    this.questionnaireName = paper.name
    this.questions = (paper.questions || []).map((item: any) => {
      // 四种返回格式 故用any
      return QuestionAnswer.fromAnswer(item)
    })
    const questionIdList = this.questions.map((item) => {
      return item.id
    })
    if (!questionIdList.length) {
      this.questions = []
      return
    }
    const newMap = new Map<string, BaseQuestionResponse>()
    const currentPageData = new Array<BaseQuestionResponse>()
    const fetchQuestionList = async (ids: string[]) => {
      const questRes = await MsExamQueryFrontGatewayExamQueryForeStage.pageQuestionInMySelf({
        page: { pageNo: 1, pageSize: ids.length },
        request: {
          queryScope: 2,
          questionIdList: ids
        }
      })
      if (questRes.status.isSuccess() && questRes.data.currentPageData?.length) {
        currentPageData.push(...questRes.data.currentPageData)
      }
    }
    for (let i = 0; i < questionIdList.length; i += 200) {
      const chunk = questionIdList.slice(i, i + 200)
      await fetchQuestionList(chunk)
    }
    if (currentPageData?.length) {
      // 试题信息
      currentPageData.map((item) => {
        newMap.set(item.questionId, item)
      })
      const newArray = this.questions.map((temp) => {
        //返回值可能有四种模型 故用any
        const ite: any = newMap.get(temp.id)
        return QuestionAnswer.fromQuestionAnwer(temp, ite)
      })

      const map = new Map(newArray.map((item) => [item.id, item]))

      // 需要按照试卷获取的id排序
      const mergedArray = this.questions.map((item1) => {
        const temp = map.get(item1.id)
        temp.id = item1.id
        temp.type = item1.type
        temp.isAnswered = item1.isAnswered
        temp.isMustAnswered = item1.isMustAnswered
        temp.answers = item1.answers
        temp.fillContentMap = item1.fillContentMap
        // 填充UI使用的绑定数据
        if (item1.type === QuestionTypeEnum.single) {
          temp.optionQuestion.options.map((it) => {
            it.isSelect = item1.answers.some((answer) => answer == it.id)
            it.completionContent = item1.fillContentMap.get(it.id)
          })
          temp.optionQuestion.selectedContent = item1.answers[0]
          // }
        } else if (item1.type === QuestionTypeEnum.multiple) {
          temp.multipleOptionQuestion.options.map((it) => {
            it.isSelect = item1.answers.some((answer) => answer == it.id)
            it.completionContent = item1.fillContentMap.get(it.id)
          })
          temp.multipleOptionQuestion.selectedContentArr = item1.answers
          // }
        } else if (item1.type === QuestionTypeEnum.answer) {
          temp.answerQuestion.selectedContent = item1.answers[0]
        } else if (item1.type === QuestionTypeEnum.gauge) {
          temp.gaugeQuestion.selectedContent = Number(item1.answers[0])
        }
        temp.onlineOrOffline = item1.onlineOrOffline
        return temp
      })
      this.questions = mergedArray as QuestionAnswer[]
    }
    return this
  }
  /**
   * 调查问卷预提交作答
   */
  async preSubmit() {
    const previewPaperPublishConfigureResponse = new PreviewPaperPublishConfigureResponse()
    previewPaperPublishConfigureResponse.description = this.questionnaireDescription
    previewPaperPublishConfigureResponse.groups = []
    previewPaperPublishConfigureResponse.paperType = this.paperType
    previewPaperPublishConfigureResponse.name = this.questionnaireName
    previewPaperPublishConfigureResponse.timeLength = this.timeLength
    previewPaperPublishConfigureResponse.totalQuestionCount = this.totalQuestionCount
    previewPaperPublishConfigureResponse.totalScore = this.totalScore
    const questionList = new Array<
      RadioQuestionResponse1 | MultipleQuestionResponse1 | AskQuestionResponse1 | ScaleQuestionResponse1
    >()
    const obj = {}
    this.questions.map((item) => {
      switch (item.type) {
        /**
         * 单选题
         */
        case QuestionTypeEnum.single:
          // 填空答案汇总（选项上带填空内容方便UI绑值）
          item.optionQuestion.options.map((ite) => {
            if (item.answers.includes(ite.id) && ite.completion && ite.completionContent) {
              item.fillContentMap.set(ite.id, ite.completionContent)
            }
          })
          if (item.answers.length) {
            const radioAnswer =
              (this.questionMap.get(item.id) as RadioQuestionResponse1) || new RadioQuestionResponse1()
            radioAnswer.radioAnswer = item.answers.length ? item.answers[0] : ''
            radioAnswer.questionType = 1
            radioAnswer.id = item.id
            radioAnswer.fillContent = item.fillContentMap.get(item.answers.length ? item.answers[0] : '')
            questionList.push(radioAnswer)
          }
          break
        /**
         * 多选题
         */
        case QuestionTypeEnum.multiple:
          // 填空答案汇总（选项上带填空内容方便UI绑值）
          item.multipleOptionQuestion.options.map((ite) => {
            if (item.answers.includes(ite.id) && ite.completion && ite.completionContent) {
              item.fillContentMap.set(ite.id, ite.completionContent)
            }
          })
          for (const [key, value] of item.fillContentMap) {
            obj[key] = value
          }
          if (item.answers.length) {
            const multipleAnswer =
              (this.questionMap.get(item.id) as MultipleQuestionResponse1) || new MultipleQuestionResponse1()
            multipleAnswer.multipleAnswer = item.answers
            multipleAnswer.questionType = 2
            multipleAnswer.id = item.id
            multipleAnswer.fillContentMap = obj as any
            questionList.push(multipleAnswer)
          }
          break
        /**
         * 问答题
         */
        case QuestionTypeEnum.answer:
          if (item.answers.length) {
            const askAnswer = (this.questionMap.get(item.id) as AskQuestionResponse1) || new AskQuestionResponse1()
            askAnswer.askAnswer = item.answers.length ? item.answers[0] : ''
            askAnswer.questionType = 5
            askAnswer.id = item.id
            questionList.push(askAnswer)
          }
          break
        /**
         * 量表题
         */
        case QuestionTypeEnum.gauge:
          if (item.answers.length) {
            const scaleAnswer =
              (this.questionMap.get(item.id) as ScaleQuestionResponse1) || new ScaleQuestionResponse1()
            scaleAnswer.scaleAnswer = item.answers.length ? Number(item.answers[0]) : 1
            scaleAnswer.questionType = 7
            scaleAnswer.id = item.id
            questionList.push(scaleAnswer)
          }
          break
        default:
          break
      }
    })
    previewPaperPublishConfigureResponse.questions = questionList
    const res = await MsExamAnswer.handedQuestionnaireAnswerPaper({
      token: this.token,
      answerPaperViewRequest: previewPaperPublishConfigureResponse,
      answerExtractionTime: this.answerExtractionTime || ''
    })
    return res
  }
  /**
   * 提交试卷
   */
  async submit() {
    const res = await this.preSubmit()
    if (res.status.isSuccess()) {
      // 这换了口，下面的报错提示还对吗
      if (res.data.code == '40001') {
        return new ResponseStatus(40001, '请勿重复提交')
      } else if (res.data.code == '40002') {
        return new ResponseStatus(40002, '当前答卷已作废，无法继续作答')
      } else if (res.data.code == '40003') {
        return new ResponseStatus(40003, '问卷已停用，无需作答')
      } else if (res.data.code == '40004') {
        return new ResponseStatus(40004, '必答题未作答')
      } else if (res.data.code == '40005') {
        return new ResponseStatus(40005, '选项存在必填填空未填写')
      } else if (res.data.code == '40006') {
        return new ResponseStatus(40006, '答卷尚未开始作答无法交卷')
      }
    }
    return res.status
  }

  /**
   * 校验问卷必填项是否都已填写
   */
  validateMustFill() {
    let result = true
    this.questions.map((item) => {
      if (item.isMustAnswered) {
        // 必答题未答
        if (item.answers.length == 0 || !item.answers[0]) {
          result = false
        } else if (item.type == QuestionTypeEnum.single) {
          ;(item.optionQuestion?.options || []).map((ite) => {
            // 单选填空题必填未填
            if (ite.isRequire && ite.id == item.answers[0] && !ite.completionContent) {
              result = false
            }
          })
        } else if (item.type == QuestionTypeEnum.multiple) {
          ;(item.multipleOptionQuestion?.options || []).map((ite) => {
            // 多选选择题选中但填空题必填未填
            if (ite.isRequire && item.answers.indexOf(ite.id) > -1 && !ite.completionContent) {
              result = false
            }
          })
        }
      }
    })
    return result
  }

  /**
   * 列表校验口
   * @param studentToken
   */
  async doValid(studentToken: string) {
    const res = await MsQuestionnaire.applyQuestionnaireVerify(studentToken)
    return res.data
  }
  /**
   * 查询问卷说明
   */
  async doQueryQuestionnaireDescription() {
    if (!this.templateId) return ''
    const res = await MsExamQueryFrontGatewayQuestionnaireQueryForeStage.getQuestionnaireTemplateDetailInMyself({
      templateId: this.templateId
    })
    if (res.status.isSuccess() && res.data) {
      return (res.data?.publishPattern as FixedPaper)?.description || ''
    } else {
      return ''
    }
  }

  static from(dto: SurveyQuestionnaireResponse) {
    const vo = new Questionnaire()
    vo.questionnaireId = dto.surveyInformationResponse.questionnaireId
    vo.questionnaireName = dto.surveyInformationResponse.questionnaireName
    vo.questionnaireBeginTime = dto.surveyInformationResponse.questionnaireStartTime
    vo.questionnaireEndTime = dto.surveyInformationResponse.questionnaireEndTime
    vo.questionnaireDescription = dto.surveyInformationResponse.description
    switch (dto.surveyInformationResponse?.precondition?.name) {
      case 'Questionnaire_Precondition_001':
        vo.questionnairePrecondition = QuestionnairePreconditionEnum.online_assess
        break
      case 'Questionnaire_Precondition_002':
        vo.questionnairePrecondition = QuestionnairePreconditionEnum.hours_assess
        break
      case 'Questionnaire_Precondition_003':
        vo.questionnairePrecondition = QuestionnairePreconditionEnum.time_assess
        break
      default:
        vo.questionnairePrecondition = QuestionnairePreconditionEnum.none
        break
    }
    vo.answerPaperId = dto.basicInformationofUserResponse?.answerPaperId
    vo.isSubmitQuestionnaire = dto.basicInformationofUserResponse?.answerStatus == 2
    vo.questionnaireLearningId = dto.surveyInformationResponse.learningId
    vo.isOpenQuestionnaireStatistics = dto.surveyInformationResponse.openResults
    vo.usedRange = dto.surveyInformationResponse.usedRange
    vo.includedInAssessment = dto.surveyInformationResponse.includedInAssessment
    vo.templateId = dto.surveyInformationResponse.templateId
    return vo
  }
}
