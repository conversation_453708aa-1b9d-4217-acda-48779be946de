import TeachPlanLearningConfig from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/TeachPlanLearningConfig'
import TeachPlanLearningAssessSetting from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/assess-setting/TeachPlanLearningAssessSetting'

/**
 * @description 教学计划学习方式
 */
class TeachPlanLearning {
  /**
   * 操作类型
   */
  operation: number
  /**
   * 学习方式id
   */
  id: string
  /**
   *  教学计划-学习方式配置
   */
  config: TeachPlanLearningConfig
  /**
   * 考核配置
   */
  assessSetting: TeachPlanLearningAssessSetting
}

export default TeachPlanLearning
