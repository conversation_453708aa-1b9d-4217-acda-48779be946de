/**
 * 通用筛选组件抽离的方法
 */
import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
import PageQuery from '@hbfe/jxjy-admin-common/src/models/PageQuery'
import { cloneDeep } from 'lodash'
import { ServicerDto } from '@api/gateway/PlatformServicer'

@Component
export default class extends Vue {
  /**
   * 选择的数据
   */
  @Prop({ type: [Array, String], default: undefined })
  value: any

  @Prop({ type: Boolean, default: true })
  multiple: boolean // 是否多选

  @Prop({ type: Boolean, default: false })
  disabled: boolean

  @Prop({
    type: String,
    default: '请选择'
  })
  placeholder: string

  @Prop({
    type: String,
    default: '选择'
  })
  title: string

  @Prop({
    type: [Array, String],
    default: undefined
  })
  trainingInstitutionIdList: Array<string> | string

  query: PageQuery
  //  展示
  isShow = false

  key = 'biz'

  /**
   * 已选择的
   */
  selectValue: any = []
  oldSelectValue: any = []

  async created() {
    await this.query.searchBase()
    this.valueChange(this.value)
  }

  valueChange(val: any) {
    // TODO
  }
  // 取消
  cancel() {
    this.selectValue = cloneDeep(this.oldSelectValue)
    this.isShow = false
    this.complete()
  }

  // 选值改变
  clear() {
    this.selectValue = new Array<ServicerDto>()
    this.complete()
  }

  // 确定选择后向父组件发送数据变化
  complete() {
    if (this.multiple) {
      this.emit(this.getIdList())
    } else {
      this.emit(this.getIdList()[0])
    }
    this.oldSelectValue = [...this.selectValue]
    this.isShow = false
  }

  /**
   * 取消选择
   * @sure 确定删除 非弹窗情况下点击删除后需要触发complete方法
   */
  remove(id: string, sure?: boolean) {
    const index = this.findIndex(id)
    this.selectValue.splice(index, 1)
    if (!sure) {
      this.complete()
    }
  }

  /*
   * 方法类 是否选中
   */
  findIndex(id: string) {
    if (this.selectValue.length === 0) {
      return -1
    }

    return this.selectValue?.findIndex((el: any) => {
      return el.id === id
    })
  }

  getIdList() {
    return this.selectValue?.map((el: any) => {
      return el.id
    })
  }

  openDialog() {
    this.isShow = true
  }

  /**
   * 点击选中 row 类型之后需要补上
   */
  handleClick(row: ServicerDto) {
    const index = this.findIndex(row.id)
    if (index < 0) {
      if (this.multiple) {
        this.selectValue.push(row)
      } else {
        this.selectValue = [row]
        // 非多选的情况关闭弹窗
        this.complete()
      }

      return
    }
    this.selectValue.splice(index, 1)
  }

  @Emit('input')
  emit(value: any) {
    return value
  }
}
