<route-meta>
{
"isMenu": true,
"title": "收款账户管理",
"sort": 1,
"icon": "icon_guanli"
}
</route-meta>

<script lang="ts">
  import Accountfrom from '@hbfe/jxjy-admin-tradeConfig/src/account/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY],
    create: [WXGLY],
    collectionDetail: [WXGLY],
    collectionEdit: [WXGLY],
    disable: [WXGLY],
    enable: [WXGLY],
    remove: [WXGLY]
  })
  export default class extends Accountfrom {}
</script>
