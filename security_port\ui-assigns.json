[{"name": "jxgx", "meta": {"title": "网校管理员", "isMenu": true, "ownerGroup": ["jxgx"], "roles": ["FXS", "GYS", "NZGYS", "NZFXS", "NZFXSJCB", "NZGYSJCB", "ZTGLY", "JTJFGLY", "WXGLY", "DQGLY"], "permissionMap": {"query": {"name": "查询(必选)", "ownerGroup": ["jxgx.query"], "graphql": ["ms-config-v1.query.getCurrentFrontendConfig:{\"serviceName\":\"ms-config-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForServiceByDomainName:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.query.getDistributionService:{\"authorizationRequired\":false}", "ms-identity-authentication-v1.query.getAccessTokenValue:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getAdminInfoInMyself:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjy-distributor-admin-v1.query.isOnlineSchoolContractExpired:{\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.isOnlineSchoolContractExpired:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.query.findFunctionalAuthorityByRoleIdsNew:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.query.findRoleByAccountId:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applyCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptchaForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCodeForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwd:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptcha:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountId:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCode:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-account-v1.mutation.changeAuthorizationUnitInfoList:{\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applySmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validSmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentUser:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.validCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getOnlineSchoolConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getSmsCodeAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getFailedAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.forgetPassword:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCoursePreview:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursePackageInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseInSchemeInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageCoursewareSupplierInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyChangeIdentityAuthentication:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticate:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyProxyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticateBasic:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryPayFlowStatus:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-payment-v1.mutation.getBatchPreParePayResult:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.deliveredOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":true}", "ms-assess-v1.mutation.batchRePushSchemeIndicator:{\"serviceName\":\"ms-assess-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.createJxjyCommonMenu:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.temporaryOrderUpdateOrderInfoInSubject:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.enableOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForService:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.tryComparePhoto:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.updateUserDatumCodePhoto:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.refreshConfigAndUpdate:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.executeAntiInspection:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":false}", "platform-account-v1.mutation.compensatingStudentAccount:{\"serviceName\":\"platform-account-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.mutation.openDistributionService:{\"authorizationRequired\":false}", "ms-order-repair-data-v1.mutation.orderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-repair-data-v1.mutation.batchOrderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.distributionAuthBatchProduct:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.createPricePlan:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.pricePlanSetSaleChannel:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissReturnOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissExchangeOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["FXS", "GYS", "NZGYS", "NZFXS", "NZFXSJCB", "NZGYSJCB", "ZTGLY", "JTJFGLY", "WXGLY", "DQGLY"]}}, "sort": "2"}, "specifier": "jxgx", "path": "", "pathSegments": ["jxgx"], "children": [{"name": "statistic", "specifier": "Statistic", "path": "/statistic", "pathSegments": ["jxgx", "statistic"], "component": "@/packages/routers/src/basic-router/statistic.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "统计报表", "sort": 5, "icon": "icon-shu<PERSON>", "roles": ["WXGLY", "DQGLY"], "permissionMap": {}, "ownerGroup": ["jxgx.statistic"]}, "children": [{"name": "statistic-learning-statistic-index", "specifier": "StatisticLearningStatisticIndex", "path": "learning-statistic", "pathSegments": ["jxgx", "statistic", "learning-statistic"], "component": "@/packages/routers/src/basic-router/statistic/learning-statistic/index.vue", "meta": {"isMenu": true, "title": "学员学习明细", "sort": 5, "icon": "icon-mingxi", "roles": ["WXGLY", "DQGLY"], "permissionMap": {"query": {"roles": ["WXGLY", "DQGLY"], "name": "查询", "ownerGroup": ["jxgx.statistic.learning-statistic.query"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegionInDistribution:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicerManageRegion:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiBasicConfigInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiModuleConfigurationInfoInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.getLearningResultErrorStatisticsInServicer:{\"authorizationRequired\":true}", "platform-supplement-study-rules-setting-v1.query.getSupplementStudyRuleSettingByServiceId:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getDistributorPortalInfosInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pagePromotionPortalInfoInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageDistributorInSupplier:{\"authorizationRequired\":true}", "fxnl-query-common-disposition-information.query.getIndustriesByServiceIdInDistributor:{\"authorizationRequired\":true}", "fxnl-query-common-disposition-information.query.getYearsByServiceIdInDistributor:{\"authorizationRequired\":true}"]}, "exceptionManagement": {"name": "学习数据异常管理", "ownerGroup": ["jxgx.statistic.learning-statistic.exceptionManagement"], "graphql": ["student-course-learning-query-back-gateway.query.pageLearningResultErrorInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.reGenerateStudentTrainingResultSimulateInServicer:{\"authorizationRequired\":true}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryByPage:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-autolearning-student-auto-learning-task-result-v1.query.findLastFailSubTaskByMainTaskIdList:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY"]}, "export": {"name": "导出", "ownerGroup": ["jxgx.statistic.learning-statistic.export"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportStudentSchemeLearningExcelInServicerManageRegion:{\"authorizationRequired\":true}", "ms-data-export-front-gateway-DataExportBackstage.query.exportStudentSchemeLearningExcelInDistributor:{\"serviceName\":\"ms-data-export-front-gateway-v1\",\"authorizationRequired\":false}", "jxgx-data-export-gateway-backstage.query.exportStudentSchemeLearningExcelInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "DQGLY"]}, "allSync": {"name": "批量同步", "ownerGroup": ["jxgx.statistic.learning-statistic.allSync"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicerManageRegion:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-student-learning-backstage.query.rePushStudentTrainingResultToGatewayInServicerV2:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY"]}, "examDetail": {"name": "考试详情", "ownerGroup": ["jxgx.statistic.learning-statistic.examDetail"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "DQGLY"]}, "testDetail": {"name": "测验详情", "ownerGroup": ["jxgx.statistic.learning-statistic.testDetail"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "DQGLY"]}, "toLog": {"name": "查阅监管日志", "ownerGroup": ["jxgx.statistic.learning-statistic.toLog"], "graphql": [], "roles": ["WXGLY", "DQGLY"]}, "userInfo": {"name": "用户信息组件", "ownerGroup": ["jxgx.statistic.learning-statistic.userInfo"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "DQGLY"]}, "classInfo": {"name": "培训班信息组件", "ownerGroup": ["jxgx.statistic.learning-statistic.classInfo"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigByRequestInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "DQGLY"]}, "studyLog": {"name": "学习日志组件", "ownerGroup": ["jxgx.statistic.learning-statistic.studyLog"], "graphql": ["ms-general-supervision-query-front-gateway-Backstage.query.pageSupervisionCourseDetailsInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.listLearningSupervisionBehaviorInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY"]}, "toLearningLog": {"name": "查阅学习日志", "ownerGroup": ["jxgx.statistic.learning-statistic.toLearningLog"], "graphql": [], "roles": ["WXGLY", "DQGLY"]}, "queryInfo": {"name": "查询学习日志", "ownerGroup": ["jxgx.statistic.learning-statistic.queryInfo"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningLogsInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": []}}, "ownerGroup": ["jxgx.statistic.learning-statistic"]}}]}, {"name": "training", "specifier": "Training", "path": "/training", "pathSegments": ["jxgx", "training"], "component": "@/packages/routers/src/basic-router/training.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "培训管理", "sort": 4, "icon": "icon-peixun", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {}, "ownerGroup": ["jxgx.training"]}, "children": [{"name": "training-customer-service", "specifier": "TrainingCustomerService", "path": "customer-service", "pathSegments": ["jxgx", "training", "customer-service"], "component": "@/packages/routers/src/basic-router/training/customer-service.vue", "meta": {"isMenu": true, "title": "客服管理", "sort": 6, "icon": "icon-kefu", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {}, "ownerGroup": ["jxgx.training.customer-service"]}, "children": [{"name": "training-customer-service-personal-index", "specifier": "TrainingCustomerServicePersonalIndex", "path": "personal", "pathSegments": ["jxgx", "training", "customer-service", "personal"], "component": "@/packages/routers/src/basic-router/training/customer-service/personal/index.vue", "meta": {"isMenu": true, "title": "业务咨询", "sort": 1, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {"query": {"name": "查询", "ownerGroup": ["jxgx.training.customer-service.personal.query"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiBasicConfigInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiModuleConfigurationInfoInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.statisticsLearningExperienceParticipatedBySchemeIdInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryByPage:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "basicData": {"name": "学员信息", "ownerGroup": ["jxgx.training.customer-service.personal.basicData"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getDockingTycAndQcc:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"]}, "editCompany": {"name": "编辑基础信息", "ownerGroup": ["jxgx.training.customer-service.personal.editCompany"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "confirmResetPwd": {"name": "重置密码", "ownerGroup": ["jxgx.training.customer-service.personal.confirmResetPwd"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.ResetPassword:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "editProfessionalCategoryName": {"name": "编辑人社专业类别", "ownerGroup": ["jxgx.training.customer-service.personal.editProfessionalCategoryName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "editProfessionalQualification": {"name": "编辑人社职称等级", "ownerGroup": ["jxgx.training.customer-service.personal.editProfessionalQualification"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "deleteAttachmentInfoItem": {"name": "删除行业证书", "ownerGroup": ["jxgx.training.customer-service.personal.deleteAttachmentInfoItem"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.deleteCertificateInfo:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "editPersonnelCategoryName": {"name": "编辑卫生人员类别", "ownerGroup": ["jxgx.training.customer-service.personal.editPersonnelCategoryName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "editPositionCategoryName": {"name": "编辑卫生岗位类别", "ownerGroup": ["jxgx.training.customer-service.personal.editPositionCategoryName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "editProfessionalLevelName": {"name": "编辑工勤技术等级", "ownerGroup": ["jxgx.training.customer-service.personal.editProfessionalLevelName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "editJobCategoryIdName": {"name": "编辑工勤工种", "ownerGroup": ["jxgx.training.customer-service.personal.editJobCategoryIdName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "editStudyperiodSubjectName": {"name": "编辑学段学科", "ownerGroup": ["jxgx.training.customer-service.personal.editStudyperiodSubjectName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "updateAndEditCertifition": {"name": "新增/编辑证书抽屉", "ownerGroup": ["jxgx.training.customer-service.personal.updateAndEditCertifition"], "graphql": ["ms-basicdata-domain-gateway-v1.mutation.addStudentCertificateInfo:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentCertificateInfo:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "unbundle": {"name": "微信解绑", "ownerGroup": ["jxgx.training.customer-service.personal.unbundle"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.unbindWeChatOpenPlatform:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "revisePhotoTime": {"name": "调整基准照次数", "ownerGroup": ["jxgx.training.customer-service.personal.revisePhotoTime"], "graphql": ["ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.changeAllowUpdateCount:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "deletePhoto": {"name": "删除基准照", "ownerGroup": ["jxgx.training.customer-service.personal.deletePhoto"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.doDeleteUserDatum:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "studyDontent": {"name": "学习内容", "ownerGroup": ["jxgx.training.customer-service.personal.studyDontent"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.statisticsLearningExperienceParticipatedBySchemeIdInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryByPage:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningExperienceParticipatedInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.listLearningExperienceTopic:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageStudentCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareLearningRecordInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Forestage.query.getAntiBasicConfigDetailInScheme:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "studyQuery": {"name": "查询", "ownerGroup": ["jxgx.training.customer-service.personal.studyQuery"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.statisticsLearningExperienceParticipatedBySchemeIdInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryByPage:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"]}, "qualified": {"name": "一键合格", "ownerGroup": ["jxgx.training.customer-service.personal.qualified"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageStudentCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareLearningRecordInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningExperienceParticipatedInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.listLearningExperienceTopic:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-learningscheme-v1.mutation.oneKeyPass:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "studty": {"name": "一键学习", "ownerGroup": ["jxgx.training.customer-service.personal.studty"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageStudentCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareLearningRecordInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningExperienceParticipatedInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.listLearningExperienceTopic:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.applyAutoLearningTokenForManage:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-choosecourselearningscene-v1.mutation.applyCourseImmediatelyLearning:{\"serviceName\":\"ms-choosecourselearningscene-v1\",\"authorizationRequired\":true}", "ms-autonomouscourselearningscene-v1.mutation.applyCourseImmediatelyLearning:{\"serviceName\":\"ms-autonomouscourselearningscene-v1\",\"authorizationRequired\":true}", "ms-studentcourselearning-v1.mutation.commitImmediatelyCourseLearning:{\"serviceName\":\"ms-studentcourselearning-v1\",\"authorizationRequired\":true}", "ms-choosecourselearningscene-v1.mutation.applyCoursewareImmediatelyLearning:{\"serviceName\":\"ms-choosecourselearningscene-v1\",\"authorizationRequired\":true}", "ms-autonomouscourselearningscene-v1.mutation.applyCoursewareImmediatelyLearning:{\"serviceName\":\"ms-autonomouscourselearningscene-v1\",\"authorizationRequired\":true}", "ms-studentcourselearning-v1.mutation.commitImmediatelyCoursewareLearning:{\"serviceName\":\"ms-studentcourselearning-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "deleteCourse": {"name": "删除课程", "ownerGroup": ["jxgx.training.customer-service.personal.deleteCourse"], "graphql": ["ms-learningscheme-v1.mutation.applyRelearnTokenForManage:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-autonomouscourselearningscene-v1.mutation.invalidStudentCourse:{\"serviceName\":\"ms-autonomouscourselearningscene-v1\",\"authorizationRequired\":true}", "ms-choosecourselearningscene-v1.mutation.invalidStudentCourse:{\"serviceName\":\"ms-choosecourselearningscene-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "toLog": {"name": "监管日志", "ownerGroup": ["jxgx.training.customer-service.personal.toLog"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"]}, "userInfo": {"name": "用户信息组件", "ownerGroup": ["jxgx.training.customer-service.personal.userInfo"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": []}, "classInfo": {"name": "培训班信息组件", "ownerGroup": ["jxgx.training.customer-service.personal.classInfo"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigByRequestInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": []}, "studyLog": {"name": "学习日志组件", "ownerGroup": ["jxgx.training.customer-service.personal.studyLog"], "graphql": ["ms-general-supervision-query-front-gateway-Backstage.query.pageSupervisionCourseDetailsInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.listLearningSupervisionBehaviorInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": []}, "toLearningLog": {"name": "学习日志", "ownerGroup": ["jxgx.training.customer-service.personal.toLearningLog"], "graphql": [], "roles": ["WXGLY"]}, "queryInfo": {"name": "查询学习日志", "ownerGroup": ["jxgx.training.customer-service.personal.queryInfo"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningLogsInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": []}, "deleteExamRecord": {"name": "删除考试记录", "ownerGroup": ["jxgx.training.customer-service.personal.deleteExamRecord"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-examination-v1.mutation.invalidStudentExamAnswerPaper:{\"serviceName\":\"ms-examination-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "deleteExperience": {"name": "删除学习心得", "ownerGroup": ["jxgx.training.customer-service.personal.deleteExperience"], "graphql": ["ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningExperienceParticipatedInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.listLearningExperienceTopic:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-knowledge-v1.mutation.removeStudentLearningExperience:{\"serviceName\":\"ms-knowledge-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "queryExchangeClass": {"name": "查看换班信息", "ownerGroup": ["jxgx.training.customer-service.personal.queryExchangeClass"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.pageProductPricingSchemeInSupplier:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "exchangeClass": {"name": "换班", "ownerGroup": ["jxgx.training.customer-service.personal.exchangeClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.sellerApplyExchange:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "continueExchangeClass": {"name": "继续换班", "ownerGroup": ["jxgx.training.customer-service.personal.continueExchangeClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryRecycleResouce:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryDelivery:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "orderInfo": {"name": "订单详情", "ownerGroup": ["jxgx.training.customer-service.personal.orderInfo"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"]}, "detail": {"name": "详情", "ownerGroup": ["jxgx.training.customer-service.personal.detail"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listReturnReasonInfoInSubProject:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"]}, "queryRelevancyOrder": {"name": "详情-查看关联换班订单", "ownerGroup": ["jxgx.training.customer-service.personal.queryRelevancyOrder"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInInDistributor:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getExchangeOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "queryChangeClassDetail": {"name": "详情-查看换班详情", "ownerGroup": ["jxgx.training.customer-service.personal.queryChangeClassDetail"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getExchangeOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "refundOrder": {"name": "详情-退款操作", "ownerGroup": ["jxgx.training.customer-service.personal.refundOrder"], "graphql": ["ms-order-v1.mutation.sellerApplyReturn:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "refundDetail": {"name": "详情-查看退款详情", "ownerGroup": ["jxgx.training.customer-service.personal.refundDetail"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"]}, "approve": {"name": "退款审批", "ownerGroup": ["jxgx.training.customer-service.personal.approve"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.sellerCancelReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.rejectReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.agreeReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "retryRecycleRefund": {"name": "重新回收资源", "ownerGroup": ["jxgx.training.customer-service.personal.retryRecycleRefund"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.retryRecycleResource:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "confirmRefund": {"name": "确认退款", "ownerGroup": ["jxgx.training.customer-service.personal.confirmRefund"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.confirmRefund:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "continueRefund": {"name": "继续退款", "ownerGroup": ["jxgx.training.customer-service.personal.continueRefund"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.retryRefund:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "fillToInvoice": {"name": "补要发票", "ownerGroup": ["jxgx.training.customer-service.personal.fillToInvoice"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryShippingMethodsForSchool:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.applyInvoice:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "invoiceInfo": {"name": "发票信息", "ownerGroup": ["jxgx.training.customer-service.personal.invoiceInfo"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.mutation.updateElectronicInvoice:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-offlineinvoice-v1.mutation.updateOfflinePaperInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-offlineinvoice-v1.mutation.importOfflineInvoiceDeliveryInfoWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.deliveryInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.pickupInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportInvoiceDeliveryInServicer:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "invoiceAutomatic": {"name": "电子发票", "ownerGroup": ["jxgx.training.customer-service.personal.invoiceAutomatic"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.mutation.updateElectronicInvoice:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "invoiceOffline": {"name": "电子发票（线下开票）", "ownerGroup": ["jxgx.training.customer-service.personal.invoiceOffline"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "electronicSpecialInvoice": {"name": "增值税电子专用发票（线下开票）", "ownerGroup": ["jxgx.training.customer-service.personal.electronicSpecialInvoice"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineSpecialPaperElectronicInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "invoiceIncrement": {"name": "增值税专票", "ownerGroup": ["jxgx.training.customer-service.personal.invoiceIncrement"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflinePaperInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"]}, "invoiceDistribution": {"name": "发票配送", "ownerGroup": ["jxgx.training.customer-service.personal.invoiceDistribution"], "graphql": ["ms-offlineinvoice-v1.mutation.importOfflineInvoiceDeliveryInfoWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-offlineinvoice-v1.mutation.deliveryInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.pickupInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportInvoiceDeliveryInServicer:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "refundInfo": {"name": "退款信息", "ownerGroup": ["jxgx.training.customer-service.personal.refundInfo"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "studyRecords": {"name": "学习档案", "ownerGroup": ["jxgx.training.customer-service.personal.studyRecords"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.statisticsLearningExperienceParticipatedBySchemeIdInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryByPage:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "lookCourseCertification": {"name": "查阅学时证明，重新推送", "ownerGroup": ["jxgx.training.customer-service.personal.lookCourseCertification"], "graphql": ["platform-jxjypxtypt-jxgx-certificate.mutation.singlePrintCertificate:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-student-learning-backstage.query.rePushStudentTrainingResultToGatewayInServicerV2:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}}, "ownerGroup": ["jxgx.training.customer-service.personal"]}}]}, {"name": "training-trade", "specifier": "TrainingTrade", "path": "trade", "pathSegments": ["jxgx", "training", "trade"], "component": "@/packages/routers/src/basic-router/training/trade.vue", "meta": {"isMenu": true, "title": "交易管理", "sort": 3, "icon": "icon-<PERSON><PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {}, "ownerGroup": ["jxgx.training.trade"]}, "children": [{"name": "training-trade-invoice", "specifier": "TrainingTradeInvoice", "path": "invoice", "pathSegments": ["jxgx", "training", "trade", "invoice"], "component": "@/packages/routers/src/basic-router/training/trade/invoice.vue", "meta": {"isMenu": true, "title": "发票管理", "sort": 3, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {}, "ownerGroup": ["jxgx.training.trade.invoice"]}, "children": [{"name": "training-trade-invoice-personal-index", "specifier": "TrainingTradeInvoicePersonalIndex", "path": "personal", "pathSegments": ["jxgx", "training", "trade", "invoice", "personal"], "component": "@/packages/routers/src/basic-router/training/trade/invoice/personal/index.vue", "meta": {"isMenu": true, "title": "个人报名发票", "sort": 1, "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {"personalBill": {"name": "个人报名发票", "ownerGroup": ["jxgx.training.trade.invoice.personal.personalBill"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"]}, "autoBill": {"name": "自动开票列表", "ownerGroup": ["jxgx.training.trade.invoice.personal.autoBill"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "autoInvoice": {"name": "电子普通发票（自动开票）", "ownerGroup": ["jxgx.training.trade.invoice.personal.autoInvoice"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getInvoiceAutoBillPolicyInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"]}, "autoInvoiceExport": {"name": "电子普通发票（自动开票）-导出数据", "ownerGroup": ["jxgx.training.trade.invoice.personal.autoInvoiceExport"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "autoInvoiceBatch": {"name": "批量开票", "ownerGroup": ["jxgx.training.trade.invoice.personal.autoInvoiceBatch"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getInvoiceAutoBillPolicyInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-bill-v1.mutation.issueElectronicInvoice:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "reBatchOpenInvoice": {"name": "重新批量开通发票", "ownerGroup": ["jxgx.training.trade.invoice.personal.reBatchOpenInvoice"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getInvoiceAutoBillPolicyInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-bill-v1.mutation.retryIssueElectronicInvoice:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "autoInvoiceTime": {"name": "设置自动开票时间", "ownerGroup": ["jxgx.training.trade.invoice.personal.autoInvoiceTime"], "graphql": ["ms-bill-v1.mutation.addOrUpdateElectronicInvoiceAutoConfig:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "editBill": {"name": "修改电子普通发票信息", "ownerGroup": ["jxgx.training.trade.invoice.personal.editBill"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.mutation.updateElectronicInvoice:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "reOpenInvoice": {"name": "重新开通发票", "ownerGroup": ["jxgx.training.trade.invoice.personal.reOpenInvoice"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"]}, "operationRecord": {"name": "操作记录", "ownerGroup": ["jxgx.training.trade.invoice.personal.operationRecord"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listOnlineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "redInvoice": {"name": "冲红发票", "ownerGroup": ["jxgx.training.trade.invoice.personal.redInvoice"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "invoiceOffline": {"name": "线下开票", "ownerGroup": ["jxgx.training.trade.invoice.personal.invoiceOffline"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.issueOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.resetInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.importOfflineInvoiceWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOnlineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOfflineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "electronicSpecialInvoice": {"name": "增值税电子专用发票(线下开票)", "ownerGroup": ["jxgx.training.trade.invoice.personal.electronicSpecialInvoice"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineSpecialPaperElectronicInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.issueOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.resetInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.importOfflineInvoiceWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOnlineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOfflineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "specialInvoice": {"name": "专用发票", "ownerGroup": ["jxgx.training.trade.invoice.personal.specialInvoice"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflinePaperInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.issueOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.resetInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.importOfflineInvoiceWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOnlineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOfflineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "jxgx-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "invoiceDistribution": {"name": "发票配送", "ownerGroup": ["jxgx.training.trade.invoice.personal.invoiceDistribution"], "graphql": ["ms-offlineinvoice-v1.mutation.importOfflineInvoiceDeliveryInfoWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-offlineinvoice-v1.mutation.deliveryInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.pickupInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportInvoiceDeliveryInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}}, "ownerGroup": ["jxgx.training.trade.invoice.personal"]}}]}, {"name": "training-trade-order", "specifier": "TrainingTradeOrder", "path": "order", "pathSegments": ["jxgx", "training", "trade", "order"], "component": "@/packages/routers/src/basic-router/training/trade/order.vue", "meta": {"isMenu": true, "title": "订单管理", "sort": 1, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {}, "ownerGroup": ["jxgx.training.trade.order"]}, "children": [{"name": "training-trade-order-personal-index", "specifier": "TrainingTradeOrderPersonalIndex", "path": "personal", "pathSegments": ["jxgx", "training", "trade", "order", "personal"], "component": "@/packages/routers/src/basic-router/training/trade/order/personal/index.vue", "meta": {"isMenu": true, "title": "个人报名订单", "sort": 1, "icon": "icon_guanli", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {"query": {"roles": ["WXGLY", "FXS", "GYS"], "name": "查询", "ownerGroup": ["jxgx.training.trade.order.personal.query"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInDistribution:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.pageDistributorInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getDistributorPortalInfosInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}"]}, "queryFx": {"name": "查询（分销）", "ownerGroup": ["jxgx.training.trade.order.personal.queryFx"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.listMyDistributorCommoditySkuPropertyInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageMyDistributorCommodityInDistributor:{\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInDistribution:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.pagePromotionPortalInfoInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}"], "roles": []}, "editInvoicePopup": {"name": "选择收款账号", "ownerGroup": ["jxgx.training.trade.order.personal.editInvoicePopup"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInDistribution:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": []}, "search": {"name": "查询", "ownerGroup": ["jxgx.training.trade.order.personal.search"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"]}, "searchFx": {"name": "查询（分销）", "ownerGroup": ["jxgx.training.trade.order.personal.searchFx"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": []}, "export": {"name": "导出", "ownerGroup": ["jxgx.training.trade.order.personal.export"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "exportFx": {"name": "导出（分销）", "ownerGroup": ["jxgx.training.trade.order.personal.exportFx"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": []}, "detail": {"name": "详情（入口控制）", "ownerGroup": ["jxgx.training.trade.order.personal.detail"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"]}, "closeOrder": {"name": "关闭订单", "ownerGroup": ["jxgx.training.trade.order.personal.closeOrder"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.prepareRepay:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.sellerCancelOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}}, "ownerGroup": ["jxgx.training.trade.order.personal"]}}]}, {"name": "training-trade-reconciliation", "specifier": "TrainingTradeReconciliation", "path": "reconciliation", "pathSegments": ["jxgx", "training", "trade", "reconciliation"], "component": "@/packages/routers/src/basic-router/training/trade/reconciliation.vue", "meta": {"isMenu": true, "title": "对账管理", "sort": 4, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {}, "ownerGroup": ["jxgx.training.trade.reconciliation"]}, "children": [{"name": "training-trade-reconciliation-personal-index", "specifier": "TrainingTradeReconciliationPersonalIndex", "path": "personal", "pathSegments": ["jxgx", "training", "trade", "reconciliation", "personal"], "component": "@/packages/routers/src/basic-router/training/trade/reconciliation/personal/index.vue", "meta": {"isMenu": true, "title": "个人报名对账", "sort": 1, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {"query": {"name": "个人报名对账", "ownerGroup": ["jxgx.training.trade.reconciliation.personal.query"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"]}, "orderReconciliation": {"name": "报名订单对账", "ownerGroup": ["jxgx.training.trade.reconciliation.personal.orderReconciliation"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getDistributorPortalInfosInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageDistributorInSupplier:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "orderReconciliationFx": {"name": "报名订单对账（分销）", "ownerGroup": ["jxgx.training.trade.reconciliation.personal.orderReconciliationFx"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.pagePromotionPortalInfoInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}"], "roles": []}, "export": {"name": "导出", "ownerGroup": ["jxgx.training.trade.reconciliation.personal.export"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportReturnReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "exportFx": {"name": "导出（分销）", "ownerGroup": ["jxgx.training.trade.reconciliation.personal.exportFx"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportReconciliationExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportReturnReconciliationExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportReturnReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": []}, "editInvoicePopup": {"name": "选择收款账号", "ownerGroup": ["jxgx.training.trade.reconciliation.personal.editInvoicePopup"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInDistribution:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": []}, "refundReconciliation": {"name": "退款订单对账", "ownerGroup": ["jxgx.training.trade.reconciliation.personal.refundReconciliation"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getDistributorPortalInfosInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageDistributorInSupplier:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "refundReconciliationFx": {"name": "分销退款订单对账", "ownerGroup": ["jxgx.training.trade.reconciliation.personal.refundReconciliationFx"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.pagePromotionPortalInfoInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}"], "roles": []}}, "ownerGroup": ["jxgx.training.trade.reconciliation.personal"]}}]}, {"name": "training-trade-refund", "specifier": "TrainingTradeRefund", "path": "refund", "pathSegments": ["jxgx", "training", "trade", "refund"], "component": "@/packages/routers/src/basic-router/training/trade/refund.vue", "meta": {"isMenu": true, "title": "退款管理", "sort": 2, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {}, "ownerGroup": ["jxgx.training.trade.refund"]}, "children": [{"name": "training-trade-refund-personal-index", "specifier": "TrainingTradeRefundPersonalIndex", "path": "personal", "pathSegments": ["jxgx", "training", "trade", "refund", "personal"], "component": "@/packages/routers/src/basic-router/training/trade/refund/personal/index.vue", "meta": {"isMenu": true, "title": "个人报名退款订单", "sort": 1, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {"query": {"roles": ["WXGLY", "FXS", "GYS"], "name": "查询", "ownerGroup": ["jxgx.training.trade.refund.personal.query"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getDistributorPortalInfosInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageDistributorInSupplier:{\"authorizationRequired\":true}"]}, "queryfx": {"name": "查询分销", "ownerGroup": ["jxgx.training.trade.refund.personal.queryfx"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.listMyDistributorCommoditySkuPropertyInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageMyDistributorCommodityInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pagePromotionPortalInfoInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}"], "roles": []}, "batchAgreeRefund": {"name": "批量同意退款", "ownerGroup": ["jxgx.training.trade.refund.personal.batchAgreeRefund"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.agreeReturnBatchApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "batchConfirmRefund": {"name": "批量确认退款", "ownerGroup": ["jxgx.training.trade.refund.personal.batchConfirmRefund"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.confirmBatchRefund:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "export": {"name": "导出", "ownerGroup": ["jxgx.training.trade.refund.personal.export"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportReturnOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "exportfx": {"name": "导出（分销）", "ownerGroup": ["jxgx.training.trade.refund.personal.exportfx"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportReturnOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportReturnOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": []}, "refundDetail": {"name": "退款详情(入口控制)", "ownerGroup": ["jxgx.training.trade.refund.personal.refundDetail"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"]}, "refundApproval": {"name": "退款审批", "ownerGroup": ["jxgx.training.trade.refund.personal.refundApproval"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.sellerCancelReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.rejectReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.agreeReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"]}, "editInvoicePopup": {"name": "选择收款账号", "ownerGroup": ["jxgx.training.trade.refund.personal.editInvoicePopup"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInDistribution:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": []}}, "ownerGroup": ["jxgx.training.trade.refund.personal"]}}]}]}, {"name": "training-user", "specifier": "TrainingUser", "path": "user", "pathSegments": ["jxgx", "training", "user"], "component": "@/packages/routers/src/basic-router/training/user.vue", "meta": {"isMenu": true, "title": "用户管理", "sort": 5, "icon": "icon-guan<PERSON><PERSON>", "roles": ["WXGLY"], "permissionMap": {}, "ownerGroup": ["jxgx.training.user"]}, "children": [{"name": "training-user-student-index", "specifier": "TrainingUserStudentIndex", "path": "student", "pathSegments": ["jxgx", "training", "user", "student"], "component": "@/packages/routers/src/basic-router/training/user/student/index.vue", "meta": {"isMenu": true, "title": "学员管理", "sort": 1, "icon": "icon_guanli", "roles": ["WXGLY"], "permissionMap": {"query": {"roles": [], "name": "查询", "ownerGroup": ["jxgx.training.user.student.query"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"]}, "participantsManagement": {"name": "查询", "ownerGroup": ["jxgx.training.user.student.participantsManagement"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY"]}, "queryTask": {"name": "导出任务查看", "ownerGroup": ["jxgx.training.user.student.queryTask"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportStudentExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.query.findTaskExecuteResponsePage:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "fxnl-data-export-gateway-backstage.query.pageExportTaskInfoInMyself:{\"authorizationRequired\":true}"], "roles": ["WXGLY"]}, "queryWx": {"name": "网校查询", "ownerGroup": ["jxgx.training.user.student.queryWx"], "graphql": ["jxjy-data-export-gateway-backstage.query.listExportTaskGroupInfoInServicer:{\"authorizationRequired\":true}"], "roles": []}, "queryZt": {"name": "专题查询", "ownerGroup": ["jxgx.training.user.student.queryZt"], "graphql": ["jxjy-data-export-gateway-backstage.query.listExportTaskGroupInfoInTrainingChannel:{\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.listExportTaskGroupInfoInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.query.findTaskExecuteResponsePage:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "fxnl-data-export-gateway-backstage.query.pageExportTaskInfoInMyself:{\"authorizationRequired\":true}"], "roles": []}, "location": {"name": "下载导出数据", "ownerGroup": ["jxgx.training.user.student.location"], "graphql": [], "roles": []}, "downErrorData": {"name": "下载失败数据", "ownerGroup": ["jxgx.training.user.student.downErrorData"], "graphql": ["platform-certificate-v1.mutation.exportCertificateFailedData:{\"authorizationRequired\":true}"], "roles": []}, "viewLog": {"name": "查看日志", "ownerGroup": ["jxgx.training.user.student.viewLog"], "graphql": [], "roles": []}, "detali": {"name": "详情", "ownerGroup": ["jxgx.training.user.student.detali"], "graphql": [], "roles": ["WXGLY"]}}, "ownerGroup": ["jxgx.training.user.student"]}}]}, {"name": "training-batch-print-index", "specifier": "TrainingBatchPrintIndex", "path": "batch-print", "pathSegments": ["jxgx", "training", "batch-print"], "component": "@/packages/routers/src/basic-router/training/batch-print/index.vue", "meta": {"isMenu": true, "title": "批量打印证明", "sort": 7, "icon": "icon-p<PERSON><PERSON><PERSON><PERSON>", "roles": ["WXGLY"], "permissionMap": {"query": {"roles": [], "name": "查询", "ownerGroup": ["jxgx.training.batch-print.query"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"]}, "batchPrintCertify": {"name": "查询批量打印证明", "ownerGroup": ["jxgx.training.batch-print.batchPrintCertify"], "graphql": ["platform-jxjypxtypt-jxgx-certificate.mutation.singlePrintCertificate:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.chooseTemplatePrintCertificate:{\"authorizationRequired\":true}"], "roles": ["WXGLY"]}, "schemeQuery": {"name": "方案回调请求", "ownerGroup": ["jxgx.training.batch-print.schemeQuery"], "graphql": ["ms-certificate-v1.query.findCertificateTemplate:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY"]}, "batchPrint": {"name": "批量打印", "ownerGroup": ["jxgx.training.batch-print.batchPrint"], "graphql": [], "roles": ["WXGLY"]}, "importListPrinting": {"name": "导入名单打印", "ownerGroup": ["jxgx.training.batch-print.importListPrinting"], "graphql": ["platform-certificate-v1.mutation.studentBatchPrintCertificates:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.batchPrintCertificates:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageCertificateTemplate:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.mutation.printCertificateTemplate:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.query.findCertificateTemplate:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":false}", "platform-certificate-v1.mutation.learnerImportBatchPrintCertificates:{\"authorizationRequired\":true}", "ms-certificate-v1.query.queryExportTaskResponsePage:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.returnImportedData:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.findUploadSuccessFailureQuantity:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.batchImportStudentList:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.checkImportPrintListTheUploadProgress:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.printTheListRemoveStudent:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.printTheListRemoveAllStudent:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.exportFailedData:{\"authorizationRequired\":true}"], "roles": ["WXGLY"]}, "preview": {"name": "打印", "ownerGroup": ["jxgx.training.batch-print.preview"], "graphql": ["platform-jxjypxtypt-jxgx-certificate.mutation.singlePrintCertificate:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY"]}, "downLoad": {"name": "下载", "ownerGroup": ["jxgx.training.batch-print.downLoad"], "graphql": ["platform-jxjypxtypt-jxgx-certificate.mutation.singlePrintCertificate:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.chooseTemplatePrintCertificate:{\"authorizationRequired\":true}"], "roles": ["WXGLY"]}, "studentBatchPrint": {"name": "学员批量打印证明", "ownerGroup": ["jxgx.training.batch-print.studentBatchPrint"], "graphql": ["platform-certificate-v1.mutation.batchPrintCertificates:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.studentBatchPrintCertificates:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageCertificateTemplate:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.mutation.printCertificateTemplate:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.query.findCertificateTemplate:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":false}", "platform-certificate-v1.mutation.learnerImportBatchPrintCertificates:{\"authorizationRequired\":true}"], "roles": ["WXGLY"]}, "axPrintType": {"name": "安溪批量打印证明", "ownerGroup": ["jxgx.training.batch-print.axPrintType"], "graphql": ["platform-certificate-v1.mutation.studentBatchPrintCertificates:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.batchPrintCertificates:{\"authorizationRequired\":true}"], "roles": ["WXGLY"]}}, "ownerGroup": ["jxgx.training.batch-print"]}}]}]}]