import ExamPaper from '@api/service/common/models/exam/paper/exam-paper/ExamPaper'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
// import { ExamPaperBasicInfoUI } from '@/store/modules-ui/exam/models/ExamPaperBasicInfoUI'
import { ExamPaperUI } from '@/store/modules-ui/exam/models/ExamPaperUI'
// import { RandomConfigurationItemUI } from '@/store/modules-ui/exam/models/RandomConfigurationItemUI'
import ExamPaperCreate from '@api/service/common/models/exam/paper/exam-paper/ExamPaperCreate'
import { ExamLibrary } from '@api/service/common/models/exam/library/ExamLibrary'
import { RandomConfigurationItem } from '@api/service/common/models/exam/paper/section/configuration/random/Random'
import { ExamRandomFetchWay } from '@api/service/common/models/exam/enums'
export class SelectQuestionLibrary extends ExamLibrary {
  /**
   * 是否被选
   */
  reallyChoose = false
  /**
   * 选中的下标
   */
  index = 0
}
export class UpdateQuestionLibrary {
  id: string
  enableQuestionCount: number | undefined
  pathName: string | undefined
}
/**
 * 试卷模型
 */
export interface ExamPaperUIState {
  /**
   * 当前步骤
   */
  currentStep: number
  /**
   * 当前操作类型
   * 1.新增
   * 2.编辑
   */
  operationType: number
  /**
   * 试卷模型
   */
  paperUI: ExamPaperUI
}

@Module({ namespaced: true, store, dynamic: true, name: 'ExamPaperUIModule' })
class ExamPaperUIModule extends VuexModule implements ExamPaperUIState {
  /**
   * 当前步骤
   */
  currentStep = 1
  /**
   * 当前操作类型
   * 1.新增
   * 2.编辑
   */
  operationType = 1
  /**
   * 试卷模型
   */
  paperUI = new ExamPaperUI()

  papaer = new ExamPaper()
  /**
   * 新增时为false,返回上一步时为true
   */
  isFirst = false
  /**
   * 修改时的题库列表
   */
  updateQuestionLibrary: UpdateQuestionLibrary[] = new Array<UpdateQuestionLibrary>()
  /**
   * 选中的题库
   */
  questionLibrarys = new Array<SelectQuestionLibrary>()
  // 试题类型名
  paperTypeName = ''

  // 选题方式
  fetchWay = ExamRandomFetchWay.LIBRARY
  /**
   * 模块初始化动作
   * 1.新增
   * 2.编辑
   */
  @Action
  async init(type: number) {
    this.SET_ADD_EXAM_STEP(1)
    this.RESET_PAPER_UI()
    this.SET_OPERATION_TYPE(type)
    this.FIRSTINIT(false)
    this.INIT()
  }

  @Mutation
  INIT() {
    this.paperUI = new ExamPaperUI()
    this.papaer = new ExamPaper()
    this.updateQuestionLibrary = new Array<UpdateQuestionLibrary>()
    this.questionLibrarys = new Array<SelectQuestionLibrary>()
    // this.paperTypeName = ''
  }
  /**
   * 第一步初始化（决定要不要刷新）
   * @param
   */

  @Action
  async firstInit(isFirst: boolean) {
    this.FIRSTINIT(isFirst)
  }
  @Mutation
  FIRSTINIT(isFirst: boolean) {
    this.isFirst = isFirst
  }
  @Action
  async setPapersetTypeName(name: string) {
    this.SET_PAPER_TYPE_NAME(name)
  }
  /**
   * 设置考卷
   * @param paper
   */
  @Action
  async setPaper(paper: ExamPaper) {
    this.SET_PAPER(paper)
  }
  /**
   * 修改时的题库
   * @param
   */
  @Action
  async setQuestionLibraryUpdate(UpdateQuestionLibrarys: UpdateQuestionLibrary[]) {
    this.SET_QUESTION_LIBRARY_UPDATE(UpdateQuestionLibrarys)
  }
  @Mutation
  SET_QUESTION_LIBRARY_UPDATE(UpdateQuestionLibrarys: UpdateQuestionLibrary[]) {
    this.updateQuestionLibrary = [...UpdateQuestionLibrarys]
  }
  /**
   * 取消修改的题库
   * @param id
   */
  @Action
  spliceUpdateExamlibraryById(id: string) {
    this.SPLICE_UPDATE_EXAMLIBRARY_BY_ID(id)
  }
  /**
   * 修改时的题库取消选择
   * @param id
   */
  @Mutation
  SPLICE_UPDATE_EXAMLIBRARY_BY_ID(id: string) {
    const index = this.updateQuestionLibrary.findIndex(function(value: any) {
      return value.id === id
    })
    if (index !== -1) {
      this.updateQuestionLibrary.splice(index, 1)
    }
  }
  /**
  /**
   * 设置选中题库
   * @param questionLibrarys
   */
  @Action
  setQuestionLibrary(questionLibrarys: Array<SelectQuestionLibrary>) {
    this.SET_QUESTION_LIBRARY(questionLibrarys)
  }
  /**
   * 取消题库
   * @param id
   */
  @Action
  spliceExamlibraryById(id: string) {
    this.SPLICE_EXAMLIBRARY_BY_ID(id)
  }
  /**
   * 选中题库
   * @param examLibrary
   */
  @Action
  pushExamLibrary(examLibrary: SelectQuestionLibrary) {
    this.PUSH_EXAMLIBRARY(examLibrary)
  }

  /**
   * 设置大题
   * @param examLibrary
   */
  @Action
  setRandom(random: RandomConfigurationItem[]) {
    this.SET_RANDOM(random)
  }

  @Mutation
  SET_RANDOM(random: RandomConfigurationItem[]) {
    this.paperUI.randomConfigurationItemList = [...random]
  }

  @Mutation
  SET_PAPER_TYPE_NAME(name: string) {
    this.paperTypeName = name
  }

  @Mutation
  SET_PAPER(paaer: ExamPaper) {
    this.papaer = paaer
  }

  /**
   * 设置选中的题库
   * @param questionLibrarys
   */
  @Mutation
  SET_QUESTION_LIBRARY(questionLibrarys: Array<SelectQuestionLibrary>) {
    this.questionLibrarys = questionLibrarys
  }
  /**
   * 题库取消选择
   * @param id
   */
  @Mutation
  SPLICE_EXAMLIBRARY_BY_ID(id: string) {
    const index = this.questionLibrarys.findIndex((value: any) => {
      return value.id === id
    })
    if (index !== -1) {
      this.questionLibrarys.splice(index, 1)
    }
    // 修改模式下 同步删除
    if (this.operationType !== 2) {
      return
    }
    const idx = this.updateQuestionLibrary.findIndex((value: any) => {
      return value.id === id
    })
    if (idx > -1) {
      this.updateQuestionLibrary.splice(index, -1)
    }
  }

  @Mutation
  PUSH_EXAMLIBRARY(examLibrary: SelectQuestionLibrary) {
    this.questionLibrarys.push(examLibrary)
    if (this.operationType !== 2) {
      return
    }
    const index = this.updateQuestionLibrary.findIndex(function(value: any) {
      return value.id === examLibrary.id
    })
    if (index > -1) {
      this.updateQuestionLibrary.splice(index, -1)
    }
  }
  @Mutation
  SET_ADD_EXAM_STEP(step: number) {
    this.currentStep = step
  }

  /**
   * 保存试卷的基本信息
   * @param basicInfo
   * @constructor
   */
  @Mutation
  SET_EXAM_PAPER_BASIC_INFO(basicInfo: ExamPaperCreate) {
    Object.assign(this.paperUI, basicInfo)
  }

  /**
   *  为随机智能卷添加大题配置
   * @constructor
   */
  @Mutation
  ADD_RANDOM_CONFIGURATION_ITEM(item: RandomConfigurationItem) {
    this.paperUI.randomConfigurationItemList.push(item)
  }

  /**
   * 编辑大题配置
   * @param item
   * @constructor
   */
  @Mutation
  EDIT_RANDOM_CONFIGURATION_ITEM(item: RandomConfigurationItem) {
    const list = this.paperUI.randomConfigurationItemList
    const index = list.findIndex(p => p.type === item.type)
    list.splice(index, 1, item)
  }

  /**
   * 删除大题配置
   * @param item
   * @constructor
   */
  @Mutation
  DELETE_RANDOM_CONFIGURATION_ITEM(item: RandomConfigurationItem) {
    const list = this.paperUI.randomConfigurationItemList
    const index = list.findIndex(p => p.type === item.type)
    list.splice(index, 1)
  }

  /**
   * 移动大题配置
   * @param param
   * @constructor
   */
  @Mutation
  MOVE_RANDOM_CONFIGURATION_ITEM(param: { item: RandomConfigurationItem; type: number }) {
    const list = this.paperUI.randomConfigurationItemList
    const index = list.findIndex(p => p.type === param.item.type)
    const index2 = param.type === 1 ? index - 1 : index + 1
    list.splice(index2, 1, ...list.splice(index, 1, list[index2]))
  }

  /**
   * 设置试卷回显的详情
   * @param paperUI
   * @constructor
   */
  @Mutation
  SET_EXAM_PAPER_UI(paperUI: ExamPaperUI) {
    this.paperUI = paperUI
  }

  /**
   * 重新设置试卷初始化
   * @constructor
   */
  @Mutation
  RESET_PAPER_UI() {
    this.paperUI = new ExamPaperUI()
    this.paperUI.draft = false
  }

  /**
   * 设置试卷为草稿状态
   * @constructor
   */
  @Mutation
  SET_PAPER_TO_DRAFT() {
    this.paperUI.draft = true
  }

  /**
   * 设置当前操作类型
   * @param type
   * @constructor
   */
  @Mutation
  private SET_OPERATION_TYPE(type: number) {
    this.operationType = type
  }
}

export default getModule(ExamPaperUIModule)
