<route-params content="/:id/:questionType"></route-params>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/question')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/question' }">试题管理</el-breadcrumb-item>
      <el-breadcrumb-item>试题详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="elForm" label-width="120px" class="m-text-form is-column">
                <el-form-item label="试题题目：" prop="">
                  <span class="b" v-html="questionDetail.topic" style="font-weight:bold"></span>
                </el-form-item>
                <el-form-item label="试题类型：">
                  <span>{{ statusMapType[questionDetail.questionType] }}</span>
                </el-form-item>
                <el-form-item label="试题选项：" v-if="questionDetail.questionType != 4">
                  <el-form style="padding-top: 2px;">
                    <template>
                      <el-form-item v-for="(item, index) in questionDetail.answerOptions" :key="index">
                        <p class="f-mb10" style="display:inline-block">
                          <span class="f-cb f-fb f-mr5">{{ resolverIndexToCharCode(index) }}</span>
                        </p>
                        <div class="f-mb10" style="display:inline-block" v-html="item.content"></div>
                      </el-form-item>
                    </template>
                  </el-form>
                </el-form-item>
                <el-form-item label="正确答案：">
                  <!-- <el-radio label="1">{{ questionDetailData.judgement.correctAnswer ? '真项A' : '假项:B' }}</el-radio> -->
                  <!-- <el-radio label="1">{{ questionDetail.correctAnswerId }}</el-radio> -->
                  {{ correctAnswer }}
                </el-form-item>
                <el-form-item label="所属题库：">
                  <span>{{ questionDetail.libraryName }}</span>
                </el-form-item>
                <el-form-item label="关联课程：">
                  <span v-for="(item, index) in questionDetail.relateCourseNames" :key="index">{{ item }}</span>
                </el-form-item>
                <el-form-item label="题目题析：">
                  <div class="lh180 mt7" v-html="questionDetail.dissects"></div>
                </el-form-item>
                <el-form-item label="状态：">{{ questionDetail.isEnable == true ? '启用' : '停用' }}</el-form-item>
                <el-form-item label="创建时间：">{{ questionDetail.createTime }}</el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { ExamUtil } from '@/store/module/exam/common/ExamUtil'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { QuestionTypeEnum } from '@api/service/common/enums/question/QuestionType'
  import QueryQuestionDetail from '@api/service/management/resource/question/query/QueryQuestionDetail'
  import QuestionDetail from '@api/service/management/resource/question/query/common/QuestionDetail'
  import OpinionQuestionDetailVo from '@api/service/management/resource/question/query/vo/OpinionQuestionDetailVo'
  enum QuestionType {
    // 判断题
    'JUDGEMENT' = '判断题',
    // 单选题
    'SINGLE_SELECTION' = '单选题',
    // 多选题
    'MULTIPLE_SELECTION' = '多选题'
  }
  @Component
  export default class extends Vue {
    form = {}
    questionId = ''
    questionType = 1
    statusMapType = {
      [QuestionTypeEnum.radio]: '单选题',
      [QuestionTypeEnum.multiple]: '多选题',
      [QuestionTypeEnum.fill]: '填空题',
      [QuestionTypeEnum.opinion]: '判断题',
      [QuestionTypeEnum.father]: '父子题',
      [QuestionTypeEnum.ask]: '问答题'
    }
    /**
     * 试题详情回显
     */
    // questionDetail: Question | undefined = new Question()

    teachUnitName = '' //机构名称
    questionDetailData = {}

    // 试题详情类实例
    queryQuestionDetail: QueryQuestionDetail
    /**
     * 试题详情回显
     */
    questionDetail = new QuestionDetail()
    // questionDetail = {}

    /**
     * 解析正确答案-详情回显
     * @param children
     */
    get correctAnswer() {
      const reg = /^[0-9]*$/
      console.log(this.questionDetail, 'this.questionDetailthis.questionDetailthis.questionDetail')
      if (this.questionDetail?.questionType === 1) {
        //单选 获取
        let optionsName = 0
        if (reg.test(this.questionDetail?.correctAnswerId)) {
          return this.getCorrectAnswer(this.questionDetail?.correctAnswerId)
        } else {
          this.questionDetail.answerOptions.map((item: any, index: number) => {
            console.log(index)
            if (item.id == this.questionDetail?.correctAnswerId) {
              optionsName = index + 1
            }
          })
          return this.getCorrectAnswer(optionsName)
        }
      }
      if (this.questionDetail?.questionType === 2) {
        const arr = new Array<string>()
        const list = this.questionDetail?.correctAnswerIds
        this.questionDetail.answerOptions.map((item: any, i: number) => {
          const optionsName = item.content
          console.log(item, '===selectoP')
          // 如果正确答案列表与试题选项相符，拿到对应渲染试题选项的序号
          list.map((answerId: any) => {
            if (item.id == answerId) {
              const answer = this.resolverIndexToCharCode(i)
              arr.push(answer)
            }
            // if (reg.test(element) && item.id == element) {
            //   arr.push(this.getCorrectAnswer(element))
            // } else if (item.id == element) {
            //   arr.push(optionsName)
            // }
          })
        })

        return arr.join('、')
      }
      if (this.questionDetail.questionType == 4) {
        const res = (this.questionDetail as OpinionQuestionDetailVo).correctAnswer == true ? '真项A' : '假项B'
        return res
      }
      return ''
    }

    async created() {
      await this.init()
    }
    async init() {
      const questionId = this.$route?.params?.id
      const questionType = Number(this.$route?.params?.questionType)
      this.queryQuestionDetail = ResourceModule.queryQuestionFactory.getQueryQuestionDetail(questionId, questionType)
      // 接口请求，返回的是状态
      const res = await this.queryQuestionDetail.queryQuestionDetail()
      // 详情数据
      this.questionDetail = this.queryQuestionDetail.questionDetail
      console.log(this.questionDetail, 'resdata')
    }

    // 获取正确答案
    getCorrectAnswer(num: number) {
      return ExamUtil.matchCharCode(num - 1)
    }
    // 比较选项id顺序
    compare(property: string) {
      return (a: any, b: any) => {
        const value1: number = a[property]
        const value2: number = b[property]
        return value1 - value2
      }
    }
    /**
     * 解析题型
     * @param questionType
     */
    resolverQuestionType(questionType: number) {
      return QuestionType[questionType]
    }
    resolverIndexToCharCode(index: number) {
      return ExamUtil.matchCharCode(index)
    }
  }
</script>
