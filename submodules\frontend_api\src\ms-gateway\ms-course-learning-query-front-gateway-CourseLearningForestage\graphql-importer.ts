import countCourseInServicer from './queries/countCourseInServicer.graphql'
import countCoursewareInServicer from './queries/countCoursewareInServicer.graphql'
import countPeriodCountOfCourseTrainingOutlineInSchemeInServicer from './queries/countPeriodCountOfCourseTrainingOutlineInSchemeInServicer.graphql'
import getCourseAppraiseInMyself from './queries/getCourseAppraiseInMyself.graphql'
import getCourseAppraiseStatisticsInServicer from './queries/getCourseAppraiseStatisticsInServicer.graphql'
import getCourseCategoryInServicer from './queries/getCourseCategoryInServicer.graphql'
import getCourseCountBySchemeIdInServicer from './queries/getCourseCountBySchemeIdInServicer.graphql'
import getCourseInServicer from './queries/getCourseInServicer.graphql'
import getCourseLearnStatistics from './queries/getCourseLearnStatistics.graphql'
import getCourseLearningRecordInMyself from './queries/getCourseLearningRecordInMyself.graphql'
import getCourseTrainingOutlineInServicer from './queries/getCourseTrainingOutlineInServicer.graphql'
import getLastStudentCourseLearningInMyself from './queries/getLastStudentCourseLearningInMyself.graphql'
import getLastStudentCourseLearningInServicer from './queries/getLastStudentCourseLearningInServicer.graphql'
import getLastStudentCourseLearningInSubProject from './queries/getLastStudentCourseLearningInSubProject.graphql'
import getLastStudyQualificationInMyself from './queries/getLastStudyQualificationInMyself.graphql'
import getLearningExperienceContentInServicer from './queries/getLearningExperienceContentInServicer.graphql'
import getSchemeLearningRule from './queries/getSchemeLearningRule.graphql'
import getStudentCourseAppraiseStarStatisticsInCourseSupplier from './queries/getStudentCourseAppraiseStarStatisticsInCourseSupplier.graphql'
import getStudentCourseAppraiseStarStatisticsInServicer from './queries/getStudentCourseAppraiseStarStatisticsInServicer.graphql'
import listCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself from './queries/listCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself.graphql'
import listCanChooseCourseTrainingOutlineOfAutonomousCourseLearningSceneInMyself from './queries/listCanChooseCourseTrainingOutlineOfAutonomousCourseLearningSceneInMyself.graphql'
import listCanChooseCourseTrainingOutlineOfChooseCourseLearningSceneInMyself from './queries/listCanChooseCourseTrainingOutlineOfChooseCourseLearningSceneInMyself.graphql'
import listCourseCategoryInServicer from './queries/listCourseCategoryInServicer.graphql'
import listCourseInSchemeInServicer from './queries/listCourseInSchemeInServicer.graphql'
import listLearningExperienceTopic from './queries/listLearningExperienceTopic.graphql'
import listTeacherInServicer from './queries/listTeacherInServicer.graphql'
import pageCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself from './queries/pageCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself.graphql'
import pageCanChooseCourseOfChooseCourseLearningSceneInMyself from './queries/pageCanChooseCourseOfChooseCourseLearningSceneInMyself.graphql'
import pageCourseDistributionResponse from './queries/pageCourseDistributionResponse.graphql'
import pageCourseInCourseSupplier from './queries/pageCourseInCourseSupplier.graphql'
import pageCourseInPackageV2InServicer from './queries/pageCourseInPackageV2InServicer.graphql'
import pageCourseInSchemeInServicer from './queries/pageCourseInSchemeInServicer.graphql'
import pageCourseInServicer from './queries/pageCourseInServicer.graphql'
import pageCourseOfAutonomousCourseLearningSceneInMyself from './queries/pageCourseOfAutonomousCourseLearningSceneInMyself.graphql'
import pageCourseTrainingOutlineOfAutonomousCourseLearningSceneInMyself from './queries/pageCourseTrainingOutlineOfAutonomousCourseLearningSceneInMyself.graphql'
import pageCourseTrainingOutlineOfInterestCourseLearningSceneInMyself from './queries/pageCourseTrainingOutlineOfInterestCourseLearningSceneInMyself.graphql'
import pageCourseV2InServicer from './queries/pageCourseV2InServicer.graphql'
import pageCoursewareLearningRecordInMyself from './queries/pageCoursewareLearningRecordInMyself.graphql'
import pageExcellentCourseInServicer from './queries/pageExcellentCourseInServicer.graphql'
import pageLearningExperienceInStudent from './queries/pageLearningExperienceInStudent.graphql'
import pageLearningExperienceNotParticipatedInStudent from './queries/pageLearningExperienceNotParticipatedInStudent.graphql'
import pageLearningExperienceParticipatedInStudent from './queries/pageLearningExperienceParticipatedInStudent.graphql'
import pagePlanItemAttendanceInMyself from './queries/pagePlanItemAttendanceInMyself.graphql'
import pageStudentCourseAppraiseInCourseSupplier from './queries/pageStudentCourseAppraiseInCourseSupplier.graphql'
import pageStudentCourseAppraiseInServicer from './queries/pageStudentCourseAppraiseInServicer.graphql'
import pageStudentCourseAppraisedByCoursePackageIdInServicer from './queries/pageStudentCourseAppraisedByCoursePackageIdInServicer.graphql'
import pageStudentCourseInServicer from './queries/pageStudentCourseInServicer.graphql'
import pageStudentCourseOfChooseCourseLearningSceneInMyself from './queries/pageStudentCourseOfChooseCourseLearningSceneInMyself.graphql'
import pageStudentCourseOfChooseCourseLearningSceneV2InMyself from './queries/pageStudentCourseOfChooseCourseLearningSceneV2InMyself.graphql'
import pageStudentCourseOfInterestCourseLearningSceneInMyself from './queries/pageStudentCourseOfInterestCourseLearningSceneInMyself.graphql'
import statisticsCourseInSchemeInMyself from './queries/statisticsCourseInSchemeInMyself.graphql'
import statisticsCourseInSchemeInServicer from './queries/statisticsCourseInSchemeInServicer.graphql'
import statisticsCourseLearningSceneChooseCourseByOutlineOfInMyself from './queries/statisticsCourseLearningSceneChooseCourseByOutlineOfInMyself.graphql'
import statisticsIssueByDateInMyself from './queries/statisticsIssueByDateInMyself.graphql'
import testPageStudentCourseOfInterestCourseLearningSceneInMyself from './queries/testPageStudentCourseOfInterestCourseLearningSceneInMyself.graphql'

export {
  countCourseInServicer,
  countCoursewareInServicer,
  countPeriodCountOfCourseTrainingOutlineInSchemeInServicer,
  getCourseAppraiseInMyself,
  getCourseAppraiseStatisticsInServicer,
  getCourseCategoryInServicer,
  getCourseCountBySchemeIdInServicer,
  getCourseInServicer,
  getCourseLearnStatistics,
  getCourseLearningRecordInMyself,
  getCourseTrainingOutlineInServicer,
  getLastStudentCourseLearningInMyself,
  getLastStudentCourseLearningInServicer,
  getLastStudentCourseLearningInSubProject,
  getLastStudyQualificationInMyself,
  getLearningExperienceContentInServicer,
  getSchemeLearningRule,
  getStudentCourseAppraiseStarStatisticsInCourseSupplier,
  getStudentCourseAppraiseStarStatisticsInServicer,
  listCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself,
  listCanChooseCourseTrainingOutlineOfAutonomousCourseLearningSceneInMyself,
  listCanChooseCourseTrainingOutlineOfChooseCourseLearningSceneInMyself,
  listCourseCategoryInServicer,
  listCourseInSchemeInServicer,
  listLearningExperienceTopic,
  listTeacherInServicer,
  pageCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself,
  pageCanChooseCourseOfChooseCourseLearningSceneInMyself,
  pageCourseDistributionResponse,
  pageCourseInCourseSupplier,
  pageCourseInPackageV2InServicer,
  pageCourseInSchemeInServicer,
  pageCourseInServicer,
  pageCourseOfAutonomousCourseLearningSceneInMyself,
  pageCourseTrainingOutlineOfAutonomousCourseLearningSceneInMyself,
  pageCourseTrainingOutlineOfInterestCourseLearningSceneInMyself,
  pageCourseV2InServicer,
  pageCoursewareLearningRecordInMyself,
  pageExcellentCourseInServicer,
  pageLearningExperienceInStudent,
  pageLearningExperienceNotParticipatedInStudent,
  pageLearningExperienceParticipatedInStudent,
  pagePlanItemAttendanceInMyself,
  pageStudentCourseAppraiseInCourseSupplier,
  pageStudentCourseAppraiseInServicer,
  pageStudentCourseAppraisedByCoursePackageIdInServicer,
  pageStudentCourseInServicer,
  pageStudentCourseOfChooseCourseLearningSceneInMyself,
  pageStudentCourseOfChooseCourseLearningSceneV2InMyself,
  pageStudentCourseOfInterestCourseLearningSceneInMyself,
  statisticsCourseInSchemeInMyself,
  statisticsCourseInSchemeInServicer,
  statisticsCourseLearningSceneChooseCourseByOutlineOfInMyself,
  statisticsIssueByDateInMyself,
  testPageStudentCourseOfInterestCourseLearningSceneInMyself
}
