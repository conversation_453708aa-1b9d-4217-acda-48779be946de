import store from '@/store'
import { Module as Mod } from 'vuex'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import MockExaminationPaper from '../../../../service/customer/my-question/mockexamination/models/MockExaminationPaper'
import PlatformExamGateway, { ExamRecordDTO } from '@api/gateway/PlatformExam'
import MockExaminationAnswer from '../../../../service/customer/my-question/mockexamination/models/MockExaminationAnswer'
import { ResponseStatus } from '../../../../Response'
import { ExamConfigType } from '../../../../service/customer/my-question/question-practice/models/enums'
import PreExamLS, {
  ApplyExamLearningTokenRequest,
  NormalIssueClassLSResponse
} from '@api/gateway/NormalIssueClassLS-default'
import PreExamGateway from '@api/gateway/btpx@GeneralExam-default'
import { Role, RoleType } from '../../../../Secure'
import UserModule from '@api/service/customer/user/query-user/UserModule'
import LearningSchemeModule from '@api/service/customer/myscheme/LearningSchemeModule'

class StateCache {
  constructor(schemeId: string, learningId: string) {
    this.schemeId = schemeId
    this.learningId = learningId
  }

  // 方案id
  schemeId: string
  // 方式id
  learningId: string
  // 是否启用考试学习方式
  enabled: boolean
  /**
   * 模拟考试卷id
   */
  paperId = ''
  /**
   * 试卷信息
   */
  paper: MockExaminationPaper = new MockExaminationPaper()
  /**
   * 及格分数
   */
  passScore = 0
  /**
   * 考试时长(分钟)
   **/
  examTimeLength = 0

  /**
   * 参与作答人数
   */
  joinUserNum = 0
  /**
   * 是否开放题解析
   */
  openResolvedExam = false
  answerRecord: Array<ExamRecordDTO> = new Array<ExamRecordDTO>()
  // 每个模拟考最新一份答卷
  latestExaminationAnswer: MockExaminationAnswer = new MockExaminationAnswer()
  // 是否需要重载，在学员触发去作答练习是置为true，在下次取数时先清空数据然后加载新数据
  needReload = false
  // 最新一次加载时间，从apollo配置的超时时间与当前时间判断，在下次取数时清空当前取数数据然后加载新数据
  latestLoadTime: Date = new Date()
}

export interface IState {
  // 各学习方式状态缓存
  learningMockExaminationListMap: Array<StateCache>
  currentExamRoundId: string
  latestExaminationAnswer: MockExaminationAnswer
}

@Module({
  namespaced: true,
  name: 'CustomerMockExaminationModule',
  store,
  dynamic: true
})
class MockExaminationModule extends VuexModule implements IState {
  // 各学习方式状态缓存
  learningMockExaminationListMap = new Array<StateCache>()
  currentExamRoundId = ''
  latestExaminationAnswer: MockExaminationAnswer = new MockExaminationAnswer()

  constructor(module: Mod<ThisType<any>, any>) {
    super(module)
    store.subscribe(mutation => {
      if (mutation.type?.indexOf('ALERT_EXAM_RELOAD') !== -1) {
        this.processSubmitPaperMessage(mutation.payload)
      }
    })
  }

  @Role([RoleType.user])
  @Action
  async init(payload: { schemeId: string; learningId: string }) {
    if (
      !this.learningMockExaminationListMap.find(p => p.learningId === payload.learningId) ||
      this.learningMockExaminationListMap.find(p => p.learningId === payload.learningId)?.needReload
    ) {
      if (!this.learningMockExaminationListMap.find(p => p.learningId === payload.learningId)) {
        const stateCache = new StateCache(payload.schemeId, payload.learningId)
        this.setStateCacheToLearningMockExaminationListMap(stateCache)
      }

      // 获取考试学习信息
      const examResponse = await LearningSchemeModule.loadSchemeDetailInfo(payload.schemeId)
      if (!examResponse.isSuccess()) {
        return examResponse
      }
      const examResult: NormalIssueClassLSResponse = LearningSchemeModule.getSchemeDetailInfo(payload.schemeId)
      const stateCache = new StateCache(payload.schemeId, payload.learningId)
      stateCache.enabled = examResult.examLearning?.enabled
      stateCache.paperId = examResult.examLearning?.examPaperId
      stateCache.passScore = examResult.examLearning?.examTimeLength
      stateCache.examTimeLength = examResult.examLearning?.examTimeLength
      stateCache.openResolvedExam = examResult.examLearning?.openResolvedExam
      const ExamPaperResponse = await PreExamGateway.getExamPaperByLearningId({
        schemeId: payload.schemeId,
        learningId: payload.learningId
      })
      if (!ExamPaperResponse.status.isSuccess()) {
        return ExamPaperResponse.status
      }
      const examPaper: any = ExamPaperResponse.data
      stateCache.paper = examPaper
      switch (examPaper?.configType) {
        case ExamConfigType.RANDOM:
          stateCache.paper.allTypeQuestionCount = examPaper.random?.configurationItems?.reduce(
            (result: any, current: any) => {
              if (result[current.type]) {
                result[current.type] += current.count
              } else {
                result[current.type] = current.count
              }
              return result
            },
            {}
          )
          break
        default:
          console.log('暂未实现题数信息获取')
      }

      const AnswerPaperResponse = await PreExamGateway.getExamAnswerPaperByLearningId({
        schemeId: payload.schemeId,
        learningId: payload.learningId
      })
      if (!AnswerPaperResponse.status.isSuccess()) {
        return AnswerPaperResponse.status
      }
      const answerPaper: any = AnswerPaperResponse.data
      stateCache.latestExaminationAnswer = answerPaper

      const UserEnterExamResponse = await PlatformExamGateway.countTotalUserEnterExam(payload.learningId)
      if (!UserEnterExamResponse.status.isSuccess()) {
        return UserEnterExamResponse.status
      }
      stateCache.joinUserNum = UserEnterExamResponse.data

      const AnswerRecordResponse = await PlatformExamGateway.listExamRecord({
        schemeId: payload.schemeId,
        learningId: payload.learningId,
        userId: UserModule.userInfo.userId,
        statisticSize: -1
      })
      if (!AnswerRecordResponse.status.isSuccess()) {
        return AnswerRecordResponse.status
      }
      stateCache.answerRecord = AnswerRecordResponse.data
      this.setStateCacheToLearningMockExaminationListMap(stateCache)
    }
    return new ResponseStatus(200)
  }

  @Role([RoleType.user])
  @Action
  async getExamAnswerInfo(param: { schemeId: string; learningId: string }) {
    const AnswerPaperResponse = await PreExamGateway.getExamAnswerPaperByLearningId({
      schemeId: param.schemeId,
      learningId: param.learningId
    })
    if (!AnswerPaperResponse.status.isSuccess()) {
      return AnswerPaperResponse.status
    }
    const answerPaper: any = AnswerPaperResponse.data
    this.setLatestExaminationAnswer(answerPaper)
    return AnswerPaperResponse.status
  }

  /**
   * 如果返回
   * @param payload
   */
  @Role([RoleType.user])
  @Action
  async goExam(payload: { schemeId: string; issueId: string; learningId: string; userId: string }) {
    if (!payload || !payload.learningId) {
      return new ResponseStatus(500, '请传入学习方式id')
    }
    const stateCache = this.learningMockExaminationListMap.find(p => p.learningId === payload.learningId)
    if (!stateCache) {
      return new ResponseStatus(500, '当前期别对应考试信息未初始化，请先init')
    }
    const param: ApplyExamLearningTokenRequest = new ApplyExamLearningTokenRequest()
    param.schemeId = payload.schemeId
    param.issueId = payload.issueId
    // param.userId = payload.userId
    const examTokenResponse = await PreExamLS.applyExamLearningToken(param)
    if (!examTokenResponse.status.isSuccess()) {
      return examTokenResponse.status
    }
    // 判断是否可以进入考试，即考试前置条件是否通过
    if ('403001' === examTokenResponse.data) {
      return new ResponseStatus(500, '请在学完所有课程后进入考试。')
    }

    const goExamResponse = await PreExamGateway.goExaminationByToken(examTokenResponse.data)
    if (!goExamResponse.status.isSuccess()) {
      return goExamResponse.status
    }
    this.setNeedReload({
      schemeId: stateCache.schemeId,
      learningId: stateCache.learningId
    })
    this.setExamRoundId(goExamResponse.data)
    return new ResponseStatus(200)
  }

  /**
   * 处理交卷消息
   * @param ctx
   * @param payload
   */
  @Action
  async processSubmitPaperMessage(payload: { schemeId: string; issueId: string; answersId: string }) {
    this.learningMockExaminationListMap.forEach((stateCache: StateCache) => {
      if (
        stateCache?.schemeId === payload.schemeId &&
        (!payload.answersId ||
          this.getLatestExaminationAnswer(stateCache.schemeId, stateCache.learningId)?.answerId === payload.answersId)
      ) {
        this.setNeedReload({
          schemeId: stateCache.schemeId,
          learningId: stateCache.learningId
        })
      }
    })
  }

  @Mutation
  setStateCacheToLearningMockExaminationListMap(payload: StateCache) {
    this.learningMockExaminationListMap = this.learningMockExaminationListMap.filter(
      p => p.learningId !== payload.learningId
    )
    this.learningMockExaminationListMap.push(payload)
  }

  @Mutation
  setNeedReload(payload: { schemeId: string; learningId: string }) {
    const stateCache = this.learningMockExaminationListMap.find(p => p.learningId === payload.learningId)
    if (stateCache) {
      stateCache.needReload = true
    }
  }

  @Mutation
  setExamRoundId(examRoundId: string) {
    this.currentExamRoundId = examRoundId
  }

  @Mutation
  setLatestExaminationAnswer(latestExaminationAnswer: MockExaminationAnswer) {
    this.latestExaminationAnswer = latestExaminationAnswer
  }

  /**
   * 获取最新一份答卷
   */
  get getLatestExaminationAnswerInfo() {
    return () => {
      return this.latestExaminationAnswer
    }
  }

  /**
   * 获取试卷id
   * @param schemeId
   * @param learningId
   */
  get getPaperId() {
    return (schemeId: string, learningId: string) => {
      return this.learningMockExaminationListMap.find(p => p.learningId === learningId)?.paperId
    }
  }

  /**
   * 获取试卷
   * @param state
   */
  get getPaper() {
    return (schemeId: string, learningId: string) => {
      return this.learningMockExaminationListMap.find(p => p.learningId === learningId)?.paper
    }
  }

  /**
   * 获取及格分
   * @param state
   */
  get getPassScore() {
    return (schemeId: string, learningId: string) => {
      return this.learningMockExaminationListMap.find(p => p.learningId === learningId)?.passScore
    }
  }

  /**
   * 是否开放题析
   * @param state
   */
  get isOpenResolvedExam() {
    return (schemeId: string, learningId: string) => {
      return this.learningMockExaminationListMap.find(p => p.learningId === learningId)?.openResolvedExam
    }
  }

  /**
   * 获取及格分
   * @param state
   */
  get getExamTimeLength() {
    return (schemeId: string, learningId: string) => {
      return this.learningMockExaminationListMap.find(p => p.learningId === learningId)?.examTimeLength
    }
  }

  /**
   * 获取参与作答人数
   * @param state
   */
  get getJoinUserNum() {
    return (schemeId: string, learningId: string) => {
      return this.learningMockExaminationListMap.find(p => p.learningId === learningId)?.joinUserNum
    }
  }

  /**
   * 获取作答记录
   * @param state
   */
  get getAnswerRecord() {
    return (schemeId: string, learningId: string) => {
      return this.learningMockExaminationListMap.find(p => p.learningId === learningId)?.answerRecord
    }
  }

  /**
   * 获取最新一份答卷
   * @param state
   */
  get getLatestExaminationAnswer() {
    return (schemeId: string, learningId: string) => {
      return this.learningMockExaminationListMap.find(p => p.learningId === learningId)?.latestExaminationAnswer
    }
  }

  /**
   * 答卷状态
   * @deprecated
   * @return answerStatus 1:未开始做题 2：正在作答 3：已交卷
   */
  get getAnswerStatus() {
    return (schemeId: string, learningId: string) => {
      return this.getLatestExaminationAnswer(schemeId, learningId)?.id
        ? this.getLatestExaminationAnswer(schemeId, learningId)?.complete
          ? 3
          : 2
        : 1
    }
  }
}

export default getModule(MockExaminationModule)
