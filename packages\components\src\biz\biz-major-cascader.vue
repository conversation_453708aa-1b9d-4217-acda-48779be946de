<route-meta>
{
"title": "人社行业培训专业级联选择器"
}
</route-meta>
<template>
  <el-cascader
    ref="elCascaderRef"
    v-if="show"
    :props="props"
    v-model="selctValue"
    :options="options"
    :placeholder="placeholder"
    :clearable="clearable"
    collapse-tags
    @change="onInput"
    v-bind="$attrs"
    :disabled="disabled"
    filterable
  ></el-cascader>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Emit, Watch, Ref } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import QueryTrainingMajor from '@api/service/common/basic-data-dictionary/query/QueryTrainingMajor'
  import TrainingMajorVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingMajorVo'
  import QueryTrainingCategory from '@api/service/common/basic-data-dictionary/query/QueryTrainingCategory'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import MajorParam from '@api/service/common/basic-data-dictionary/query/vo/majorParam'
  import SocietyTrainingMajorVo from '@api/service/common/basic-data-dictionary/query/vo/SocietyTrainingMajorVo'
  import AssembleTree from '@api/service/common/utils/AssembleTree'
  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    disabled: boolean

    @Prop({
      default: true
    })
    clearable: boolean

    @Prop({
      default: false
    })
    multiple: boolean

    // 传入的必须是数组
    @Prop({
      type: [Array, String]
    })
    value: string[]

    @Prop({
      default: '请选择培训专业',
      type: String
    })
    placeholder: string

    /**
     * 省份id、用于过滤省份
     */
    @Prop({
      default: '0',
      type: String
    })
    provinceId: string

    @Prop({
      default: false
    })
    checkStrictly: boolean

    //行业属性id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string

    // 行业id
    @Prop({
      type: String,
      default: ''
    })
    industryId: string

    @Ref('elCascaderRef') elCascaderRef: any

    // 初始内容 无回显的情况 string[]
    // 回显的数据结构参见 https://element.eleme.cn/#/zh-CN/component/cascader
    options = new Array<SocietyTrainingMajorVo>()
    societyMajorOptions = new Array<SocietyTrainingMajorVo>()
    // 当前选中的值
    selctValue: string[] = []
    show = true
    props = {}
    toParent = {
      id: '',
      name: ''
    }

    @Watch('industryPropertyId', {
      immediate: true,
      deep: true
    })
    industryPropertyIdChange(val: any) {
      if (val === this.industryPropertyId) return
      this.getTrainingMajorOptions()
    }

    /**
     * 培训专业列表
     */
    trainingMajorList: Array<TrainingCategoryVo>
    @Watch('value')
    setValue() {
      this.selctValue = this.value
    }
    @Emit('input')
    onInput(values: any) {
      this.$emit('toTrainingMajordata', values)
    }

    get optionName() {
      return (item: TrainingMajorVo) => {
        const showName = item.showName
        return showName ? item.name + '（' + showName + '）' : item.name
      }
    }

    async created() {
      this.setProps()

      await this.getTrainingMajorOptions()
      await this.echo()
    }
    /**
     * 获取培训类别
     */
    async getTrainingMajorOptions() {
      const res = await QueryTrainingCategory.queryTrainingCategoryV2(this.industryPropertyId, this.industryId)
      const majorList = res.isSuccess() ? QueryTrainingCategory.trainingCategoryListV2 : ([] as TrainingCategoryVo[])
      this.societyMajorOptions = [] as SocietyTrainingMajorVo[]
      const list = majorList.map((item) => {
        const option = new SocietyTrainingMajorVo()
        Object.assign(option, item)
        return option
      })
      const tree = new AssembleTree(list, 'propertyId', 'parentId')
      this.societyMajorOptions = tree.assembleTree()
    }
    /**
     * 获取子节点列表
     */
    async getAllDataList(propertyId: string) {
      const params: MajorParam = new MajorParam()
      params.industryPropertyId = this.industryPropertyId
      params.parentPropertyId = propertyId
      const res = await QueryTrainingMajor.queryTrainingMajor(params)
      const tree = res.isSuccess() ? QueryTrainingMajor.trainingMajorList : ([] as TrainingCategoryVo[])
      // 设置对应子节点名称
      tree.map(async (item) => {
        Object.assign(item, { leaf: true, optionName: this.optionName(item) })
      })

      return tree.length > 0 ? tree : undefined
    }

    setProps() {
      const that = this as any
      this.props = {
        lazy: false,
        value: 'propertyId',
        label: 'optionName',
        multiple: this.multiple,
        checkStrictly: this.checkStrictly
        /**
         * 懒加载方法
         * */
        // async lazyLoad(node: any, resolve: (val: any) => {}) {
        //   if (node.level < 1) {
        //     return
        //   } else {
        //     if (node.level <= 2) {
        //       const params: MajorParam = new MajorParam()
        //       params.industryPropertyId = that.industryPropertyId
        //       params.parentPropertyId = node.value
        //       const res = await QueryTrainingMajor.queryTrainingMajor(params)
        //       if (res.isSuccess()) {
        //         const arr = cloneDeep(QueryTrainingMajor.trainingMajorList)
        //         const nodes = arr.map(item =>
        //           Object.assign(item, { leaf: node.level == 1, optionName: that.optionName(item) })
        //         )
        //         resolve(nodes)
        //       }
        //     } else {
        //       resolve(undefined)
        //     }
        //   }
        // }
      }
    }

    /**
     * 数据回显（回显需要把options的路径拼全，具体看源码吧）
     * 这边回显只考虑单选。多选需要另外封装。本项目暂无
     * 原理类似
     */
    async echo() {
      if (this.value?.length !== 2) {
        return (this.options = this.societyMajorOptions)
      }
      this.options = this.societyMajorOptions

      this.show = true
    }
  }
</script>
