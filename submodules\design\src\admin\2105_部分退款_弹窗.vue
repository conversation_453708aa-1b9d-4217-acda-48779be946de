<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--发起退货/款-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">发起退货/款</el-button>
        <el-drawer
          title="发起退货/款"
          :visible.sync="dialog1"
          :direction="direction"
          size="640px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              请根据实际情况选择退货/款类型，请仔细确认！
            </el-alert>
            <el-row>
              <el-col :span="24">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="退货/款物品" required>
                    <el-checkbox class="m-refund-good">
                      <div class="tit f-to">
                        <i class="el-icon-s-goods"></i
                        >培训方案名称名称培训方案名称名称培训方案名称名称培训方案名称名称培训方案名称名称
                      </div>
                      <div class="tag">
                        <el-tag type="info" size="small">选课规则</el-tag>
                        <el-tag type="info" size="small">2024年</el-tag>
                        <el-tag type="info" size="small">人社行业</el-tag>
                        <el-tag type="info" size="small">福建省</el-tag>
                        <el-tag type="info" size="small">公需科目</el-tag>
                        <el-tag type="info" size="small">60学时</el-tag>
                      </div>
                      <div class="bottom">
                        <div class="price">实付金额：<span class="f-cr">500.00</span>元</div>
                      </div>
                    </el-checkbox>
                  </el-form-item>
                  <el-form-item label="退货/款物品" required>
                    <el-checkbox class="m-refund-good" disabled>
                      <div class="tit f-to">
                        <i class="el-icon-s-goods"></i
                        >培训方案名称名称培训方案名称名称培训方案名称名称培训方案名称名称培训方案名称名称
                      </div>
                      <div class="tag">
                        <el-tag type="info" size="small">选课规则</el-tag>
                        <el-tag type="info" size="small">2024年</el-tag>
                        <el-tag type="info" size="small">人社行业</el-tag>
                        <el-tag type="info" size="small">福建省</el-tag>
                        <el-tag type="info" size="small">公需科目</el-tag>
                        <el-tag type="info" size="small">60学时</el-tag>
                      </div>
                      <div class="bottom">
                        <div class="price">实付金额：<span class="f-cr">500.00</span>元</div>
                      </div>
                      <div class="status">已退货</div>
                    </el-checkbox>
                  </el-form-item>
                  <el-form-item label="退货/款类型" required>
                    <el-select clearable placeholder="请选择">
                      <el-option label="选项1" value=""></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="退款金额" required>
                    <el-input v-model="input" clearable placeholder="退款金额上限¥500，可自行修改" />
                  </el-form-item>
                  <el-form-item label="退款原因" required>
                    <el-select clearable placeholder="请选择">
                      <el-option label="选项1" value=""></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="退款说明" required>
                    <el-input type="textarea" :rows="5" placeholder="请输入退款说明"> </el-input>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--查看退货/款记录-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20 f-mb20">查看退货/款记录</el-button>
        <el-dialog title="查看退货/款记录" :visible.sync="dialog2" width="1000px" class="m-dialog">
          <el-descriptions title="">
            <el-descriptions-item label="物品名称">XXXXXXX</el-descriptions-item>
            <el-descriptions-item label="金额">¥500</el-descriptions-item>
            <el-descriptions-item label="数量">1</el-descriptions-item>
            <el-descriptions-item label="当前商品状态">已部分退款，未退货</el-descriptions-item>
          </el-descriptions>
          <el-table stripe :data="tableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="发起退款时间" min-width="150">
              <template>2025-02-07 17:34:18</template>
            </el-table-column>
            <el-table-column label="退款原因" min-width="150">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  不想要了
                </div>
                <div v-else>
                  不想要了
                </div>
              </template>
            </el-table-column>
            <el-table-column label="不想要了" min-width="120">
              <template>仅部分退款</template>
            </el-table-column>
            <el-table-column label="退款金额" min-width="100">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  100
                </div>
                <div v-else>
                  100
                </div>
              </template>
            </el-table-column>
            <el-table-column label="退货内容" min-width="100">
              <template>退货内容</template>
            </el-table-column>
            <el-table-column label="退款状态" min-width="100">
              <template>退款成功</template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template>
                <el-button type="text">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>
          <div slot="footer">
            <el-button type="primary">立即前往</el-button>
          </div>
        </el-dialog>
        <!--发起批量退货/款-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20">发起批量退货/款</el-button>
        <el-drawer
          title="发起批量退货/款"
          :visible.sync="dialog3"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd" style="height: calc(100% - 80px);">
            <div class="m-refund-bat">
              <div class="hd">
                <el-alert type="warning" show-icon :closable="false" class="m-alert">
                  当前集体报名批次内剩余可退货/款的订单共 N 笔，请根据实际情况选择退货/款类型！
                </el-alert>
                <el-row>
                  <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                    <el-form-item label="退货/款类型" required>
                      <el-col :span="8">
                        <el-select clearable placeholder="请选择">
                          <el-option label="选项1" value=""></el-option>
                        </el-select>
                      </el-col>
                    </el-form-item>
                    <el-form-item label="退款原因" required>
                      <el-col :span="8">
                        <el-select clearable placeholder="请选择" class="u-w180">
                          <el-option label="选项1" value=""></el-option>
                        </el-select>
                      </el-col>
                    </el-form-item>
                    <el-form-item label="退款说明" required>
                      <el-col :span="8">
                        <el-input type="textarea" :rows="3" placeholder="请输入退款说明"> </el-input>
                      </el-col>
                    </el-form-item>
                    <el-form-item label="退货/款订单" required>
                      当前退货/款订单 共 3 笔，累计退款金额 共 800 元。<el-button type="text">查看明细</el-button>
                    </el-form-item>
                    <el-form-item label="退货/款订单" required>
                      当前退货/款订单 共 3 笔，累计退款金额 共 800 元。
                      <el-button type="primary" size="small" class="f-fr">批量设置退款金额</el-button>
                    </el-form-item>
                  </el-form>
                </el-row>
              </div>
              <div class="bd">
                <el-table stripe :data="tableData" class="m-table">
                  <el-table-column type="selection" width="60" align="center"></el-table-column>
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="购买人" min-width="120">
                    <template>
                      <p>姓名：AAA</p>
                      <p>手机：13023801427</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="购买物品" min-width="120">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        2025年公需课
                      </div>
                      <div v-else>
                        2025年公需课
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="数量" min-width="80">
                    <template>1</template>
                  </el-table-column>
                  <el-table-column label="实付金额（元）" min-width="120">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        500
                      </div>
                      <div v-else>
                        500
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="子订单当前退货/款状态" min-width="140">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        已部分退货，未退货
                      </div>
                      <div v-else>
                        -
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="本次退款金额（元）" width="260" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-input placeholder="退款金额上限¥500，可自行修改" style="width: 232px;"></el-input>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <span class="f-co">当前订单状态不支持退货且退款</span>
                      </div>
                      <div v-else>
                        <span class="f-co">未选中，本次退货/款不含当前订单</span>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--批量设置退款金额-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20 f-mb20">批量设置退款金额</el-button>
        <el-dialog title="批量设置退款金额" :visible.sync="dialog4" width="360px" class="m-dialog">
          <el-radio-group v-model="radio">
            <div>
              <el-radio :label="3">批量设置为可退上限</el-radio>
            </div>
            <div class="f-mt10">
              <el-radio :label="6"
                >批量设置为<el-input placeholder="请输入退款金额" class="f-ml10 u-w180"></el-input
              ></el-radio>
            </div>
          </el-radio-group>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确认提交</el-button>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' }
        ],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
