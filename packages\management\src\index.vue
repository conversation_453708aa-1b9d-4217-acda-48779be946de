<route-meta>
  {
  "isMenu": true,
  "title": "网校管理",
  "sort": 2,
  "icon": "icon-weiwang<PERSON>o"
  }
</route-meta>
<template>
  <div>
    <el-main v-if="$hasPermission('query')" desc="查询" actions="created,handleLoadData">
      <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
        <p>温馨提示：</p>
        <p>
          1.若是需开通新网校， 请输入点击【<a class="f-cb" href="#/school-management/register-school">开通网校</a
          >】进行配置网校开通信息。
        </p>
      </el-alert>
      <!--顶部tab标签-->
      <el-tabs v-model="activeName" class="m-tab-top is-sticky" ref="tabs">
        <el-tab-pane label="已创建网校" name="first">
          <div class="f-p15">
            <el-card shadow="never" class="m-card">
              <!--条件查询-->
              <el-row :gutter="16" class="m-query is-border-bottom">
                <el-form :inline="true" label-width="auto">
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item label="网校名称" prop="schoolName">
                      <el-input v-model="OnlineSchoolQueryParams.schoolName" clearable placeholder="请输入网校名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item label="服务地区" prop="serviceRegionCodes">
                      <el-cascader
                        ref="areaCascader"
                        :options="indexOptions"
                        v-model="serviceArea"
                        :props="props"
                        @change="handleAreaChange"
                        collapse-tags
                        clearable
                      ></el-cascader>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item label="培训行业" prop="industryIds">
                      <el-select
                        v-model="OnlineSchoolQueryParams.industryIds"
                        placeholder="请选择网校培训行业"
                        clearable
                      >
                        <el-option
                          v-for="item in industryList"
                          :key="item.label"
                          :value="item.ids"
                          :label="item.label"
                          >{{ item.label }}</el-option
                        >
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item label="网校状态" prop="schoolStatus">
                      <el-select v-model="OnlineSchoolQueryParams.schoolStatus" clearable placeholder="请选择网校状态">
                        <el-option
                          v-for="item in SchoolStatusList"
                          :key="item.value"
                          :value="item.value"
                          :label="item.name"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item label="业主单位" prop="ownerUnitName">
                      <el-input
                        v-model="OnlineSchoolQueryParams.ownerUnitName"
                        clearable
                        placeholder="请输入业主单位名称"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item label="网校性质" prop="schoolModel">
                      <el-select v-model="OnlineSchoolQueryParams.schoolModel" clearable placeholder="请选择网校性质">
                        <el-option value="" label="全部"></el-option>
                        <el-option :value="1" label="正式实施"></el-option>
                        <el-option :value="2" label="DEMO"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item label="开通时间" prop="openTimeList">
                      <el-date-picker
                        v-model="openTimeList"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                      >
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <template v-if="showFlag">
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="服务期限" prop="">
                        <el-select
                          v-model="OnlineSchoolQueryParams.servicePeriodModel"
                          clearable
                          placeholder="请选择服务期限"
                        >
                          <el-option value="" label="全部"></el-option>
                          <el-option :value="1" label="长期"></el-option>
                          <el-option :value="2" label="短期（指定）"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="服务到期时间">
                        <el-date-picker
                          v-model="serviceOverTime"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          value-format="yyyy-MM-dd HH:mm:ss"
                        >
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="提供终端">
                        <el-select
                          v-model="OnlineSchoolQueryParams.providerService"
                          clearable
                          placeholder="请选择提供终端"
                        >
                          <el-option :value="1" label="web端"></el-option>
                          <el-option :value="2" label="H5端"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="是否延长服务">
                        <el-select
                          v-model="OnlineSchoolQueryParams.extendedService"
                          clearable
                          placeholder="请选择是否延长服务"
                        >
                          <el-option value="" label="全部"></el-option>
                          <el-option :value="true" label="是"></el-option>
                          <el-option :value="false" label="否"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </template>

                  <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                    <el-form-item class="f-tr">
                      <el-button type="primary" @click="handleSearch">查询</el-button>
                      <el-button @click="handleReset">重置</el-button>
                      <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                      <el-button v-if="showFlag == true" type="text" @click="showFlag = false"
                        >收起<i class="el-icon-arrow-up el-icon--right"></i
                      ></el-button>
                      <el-button v-else type="text" @click="showFlag = true"
                        >展开<i class="el-icon-arrow-up el-icon--right"></i
                      ></el-button>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <!--操作栏-->
              <el-alert type="warning" :closable="false" class="m-alert f-mb15">
                <div class="f-c6">
                  当前共已开通
                  <span class="f-co f-fb">{{ OnlineSchoolList.statistics.openSchoolNum }}个</span> 网校，其中
                  <span class="f-co f-fb"
                    >使用中{{ OnlineSchoolList.statistics.operateSchoolNum }}个，停用{{
                      OnlineSchoolList.statistics.disableSchoolNum
                    }}个，已到期{{ OnlineSchoolList.statistics.overSchoolNum }}个</span
                  >
                </div>
              </el-alert>
              <!--表格-->
              <el-table
                stripe
                :data="tableData"
                max-height="500px"
                class="m-table"
                @sort-change="handleOpenTimeChange"
                v-loading="loading"
                ref="schemeTable"
              >
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column width="100">
                  <template slot-scope="scope">
                    <div v-if="scope.row.schoolModel == 1">
                      <div class="tag-round">正式实施</div>
                    </div>
                    <div v-else>
                      <div class="tag-round is-bg-gray">DMEO</div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="网校信息" min-width="300">
                  <template slot-scope="scope">
                    <div>
                      <p class="f-fb">{{ scope.row.schoolName }}</p>
                      <p class="f-c9 f-f13">域名：{{ scope.row.domainName }}</p>
                      <p class="f-c9 f-f13">业主：{{ scope.row.ownerUnitName }}</p>
                      <p class="f-c9 f-f13">
                        <el-tag v-if="scope.row.industryNames.includes('人社行业')" size="small" class="f-mr10"
                          >人社行业</el-tag
                        >
                        <el-tag v-if="scope.row.industryNames.includes('建设行业')" size="small" class="f-mr10"
                          >建设行业</el-tag
                        >
                        <el-tag v-if="scope.row.industryNames.includes('职业卫生行业')" size="small" class="f-mr10"
                          >职业卫生行业</el-tag
                        >
                        <el-tag v-if="scope.row.industryNames.includes('工勤行业')" size="small" class="f-mr10"
                          >工勤行业</el-tag
                        >
                        <el-tag v-if="scope.row.industryNames.includes('教师行业')" size="small" class="f-mr10"
                          >教师行业</el-tag
                        >
                        <el-tag v-if="scope.row.industryNames.includes('药师行业')" size="small" class="f-mr10"
                          >药师行业</el-tag
                        >
                      </p>
                      <p class="f-c9 f-f13">
                        <el-tag
                          v-for="item in scope.row.serviceRegionNames"
                          :key="item"
                          type="info"
                          size="small"
                          class="f-mr10"
                          >{{ item }}</el-tag
                        >
                      </p>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="提供终端" min-width="120">
                  <template slot-scope="scope">
                    <div v-if="scope.row.providerWebService"><el-tag type="warning" size="small">PC端</el-tag></div>
                    <div v-if="scope.row.providerH5Service"><el-tag type="warning" size="small">H5</el-tag></div>
                  </template>
                </el-table-column>
                <el-table-column label="服务期限" min-width="120">
                  <template slot-scope="scope">
                    <div v-if="scope.row.servicePeriodModel == 1">
                      <div><el-tag type="warning" size="small">长期培训</el-tag></div>
                    </div>
                    <div v-else-if="scope.row.servicePeriodModel == 2">
                      <div><el-tag type="warning" size="small">指定期限</el-tag></div>
                      <div class="f-c9 f-f13 f-mt5">{{ scope.row.serviceOverTime }}</div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="状态" min-width="120">
                  <template slot-scope="scope">
                    <div v-if="scope.row.schoolStatus == SchoolStatusEnum.OPERATE">
                      <el-badge is-dot type="success" class="badge-status">在服</el-badge>
                    </div>
                    <div v-else-if="scope.row.schoolStatus == SchoolStatusEnum.DISABLE">
                      <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
                    </div>
                    <div v-else-if="scope.row.schoolStatus == SchoolStatusEnum.OVER">
                      <el-badge is-dot type="warning" class="badge-status">到期</el-badge>
                    </div>
                    <div v-else-if="scope.row.schoolStatus == SchoolStatusEnum.OPENING">
                      <el-badge is-dot type="warning" class="badge-status">开通中</el-badge>
                    </div>
                    <!-- <div v-else>
                      <el-badge is-dot type="primary" class="badge-status">在服</el-badge>
                    </div> -->
                  </template>
                </el-table-column>
                <el-table-column label="开通时间" min-width="180" sortable="custom">
                  <template slot-scope="scope">{{ scope.row.openTime }}</template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center">
                  <template slot-scope="{ row }">
                    <el-button
                      v-if="$hasPermission('update')"
                      desc="修改"
                      actions="@hbfe/jxjy-admin-management/src/updateSchool.vue"
                      type="text"
                      size="mini"
                      @click="handleUpdate(row)"
                      >修改</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
              <hb-pagination :page="page" v-bind="page"></hb-pagination>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-main>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  // import onlineSchool from '@api/service/training-institution/online-school/OnlineSchoolModule'
  import OnlineSchoolListFilterModel from '@api/service/training-institution/online-school/models/OnlineSchoolListFilterModel'
  import OnlineSchoolItemModule from '@api/service/training-institution/online-school/models/OnlineSchoolItemModule'
  import OnlineSchoolList from '@api/service/training-institution/online-school/OnlineSchoolList'
  import OnlineSchoolListStatisticsModel from '@api/service/training-institution/online-school/models/OnlineSchoolListStatisticsModel'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryOperationIndustry'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  // import { Page } from '@hbfe/common'
  import { UiPage } from '@hbfe/common'
  import { SchoolStatusEnum } from '@api/service/training-institution/online-school/enum/OnlineSchoolEnum'
  import { Tabs } from 'element-ui'
  @Component
  export default class extends Vue {
    // areaCascader: any
    constructor() {
      super()
      this.page = new UiPage(this.handleLoadData, this.handleLoadData)
    }
    SchoolStatusEnum = SchoolStatusEnum
    SchoolStatusList = [
      {
        name: '全部',
        value: null
      },
      {
        name: '停用',
        value: SchoolStatusEnum.DISABLE
      },
      {
        name: '在服',
        value: SchoolStatusEnum.OPERATE
      },
      {
        name: '过期',
        value: SchoolStatusEnum.OVER
      },
      {
        name: '开通中',
        value: SchoolStatusEnum.OPENING
      }
    ]
    // onlineSchoolObj: onlineSchool = new onlineSchool()
    //模型
    OnlineSchoolList: OnlineSchoolList = new OnlineSchoolList()
    //筛选入参
    OnlineSchoolQueryParams: OnlineSchoolListFilterModel = new OnlineSchoolListFilterModel()
    //列表
    CreatedOnlineSchoolList: Array<OnlineSchoolItemModule> = new Array<OnlineSchoolItemModule>()
    //统计数据
    StatisticsData: OnlineSchoolListStatisticsModel = new OnlineSchoolListStatisticsModel()
    industryList: any = []
    activeName = 'first'
    openTimeList: Array<string> = new Array<string>()
    serviceOverTime: Array<string> = new Array<string>()
    props = { multiple: true, emitPath: false, value: 'id', label: 'name' }
    page = new UiPage()
    cascader: Array<string> = []
    tableData: Array<OnlineSchoolItemModule> = []
    indexOptions: Array<RegionTreeVo> = []
    areaCascader: any
    serviceArea: Array<string> = []
    showFlag = true
    isAllAreaFlag = true
    loading = false
    @Ref('tabs') tabs: Tabs
    activated() {
      const active = this.tabs.$el.querySelector('.el-tabs__active-bar') as HTMLElement
      active.style.width = '75%'
    }
    async created() {
      //获取培训行业id
      const industryList = await QueryIndustry.getOperationIndustry()
      // this.industryList[0] = {}
      this.industryList[0] = {}
      // this.industryList[0].ids = []
      // this.industryList[0].label = '全部'
      // 原型排序需求 全部》人社》建设》卫生》工勤》教师
      this.industryList[0].ids = [industryList.filter(item => item.name == '人社行业')[0].id]
      this.industryList[0].label = '人社行业'
      industryList.map(item => {
        // this.industryList[0].ids.push(item.id)
        if (item.name != '人社行业') {
          this.industryList.push({ ids: [item.id], label: item.name })
        }
      })
      // this.OnlineSchoolQueryParams.industryIds = this.industryList[0].ids
      // 默认降序
      this.OnlineSchoolList.sort.openTimeSort = 2
      // 下面那个全国基本就是显示用 查询口可以先行
      await this.handleLoadData()
      //获取地区列表
      this.indexOptions = await QueryBusinessRegion.getCountrywideRegion()
      const allArea = new RegionTreeVo()
      allArea.id = null
      allArea.name = '全国'
      this.indexOptions = [allArea].concat(this.indexOptions)
      this.serviceArea = ['']
      console.log(this.indexOptions, 'this.indexOptions')

      this.areaCascader = this.$refs.areaCascader
    }
    // 查询
    async handleLoadData() {
      this.loading = true
      if (!this.OnlineSchoolQueryParams.providerService) {
        this.OnlineSchoolQueryParams.providerService = undefined
      }
      // 远端筛选项回填有问题
      if (!this.OnlineSchoolQueryParams.industryIds || !this.OnlineSchoolQueryParams.industryIds.length) {
        this.OnlineSchoolQueryParams.industryIds = []
        // this.OnlineSchoolQueryParams.industryIds = this.industryList[0].ids
      }
      // 开通时间
      if (this.openTimeList?.length) {
        this.OnlineSchoolQueryParams.openStartTime = this.openTimeList[0]
        this.OnlineSchoolQueryParams.openEndTime = this.openTimeList[1]
      } else {
        this.OnlineSchoolQueryParams.openStartTime = undefined
        this.OnlineSchoolQueryParams.openEndTime = undefined
      }
      //服务到期时间
      if (this.serviceOverTime?.length) {
        this.OnlineSchoolQueryParams.serviceOverStartTime = this.serviceOverTime[0]
        this.OnlineSchoolQueryParams.serviceOverEndTime = this.serviceOverTime[1]
      } else {
        this.OnlineSchoolQueryParams.serviceOverStartTime = undefined
        this.OnlineSchoolQueryParams.serviceOverEndTime = undefined
      }
      this.OnlineSchoolList.filterParam = this.OnlineSchoolQueryParams
      try {
        this.tableData = await this.OnlineSchoolList.queryList(this.page)
        // 初始化表格
      } catch (error) {
        // this.tableData = []
      } finally {
        this.$nextTick(() => {
          console.log('表格处理')
          ;(this.$refs['schemeTable'] as any)?.doLayout()
        })
        this.loading = false
      }
    }
    // 搜索
    async handleSearch() {
      this.page.pageNo = 1
      await this.handleLoadData()
    }
    // 地区筛选项处理
    handleAreaChange(val: any) {
      console.log(this.isAllAreaFlag, 'this.isAllAreaFlag')
      console.log(val, 'val')
      if (val.length == 0) {
        this.OnlineSchoolQueryParams.serviceRegionCodes = []
        return
      }
      if (val[0] == null && val.length > 1 && this.isAllAreaFlag) {
        console.log(1)

        this.isAllAreaFlag = !this.isAllAreaFlag
        this.serviceArea = this.serviceArea.splice(1, this.serviceArea.length)
        let checkedList = this.areaCascader.getCheckedNodes()
        checkedList = checkedList.filter((item: any) => !(item.parent && item.parent.checked))
        this.OnlineSchoolQueryParams.serviceRegionCodes = checkedList.map((item: any) => {
          return item.value
        })
      } else if (val[0] == null && val.length > 1 && !this.isAllAreaFlag) {
        this.isAllAreaFlag = !this.isAllAreaFlag
        this.OnlineSchoolQueryParams.serviceRegionCodes = []
        this.serviceArea = [null]
      } else if (val[0] !== null && val.length > 1) {
        let checkedList = this.areaCascader.getCheckedNodes()
        checkedList = checkedList.filter((item: any) => !(item.parent && item.parent.checked))
        this.OnlineSchoolQueryParams.serviceRegionCodes = checkedList.map((item: any) => {
          return item.value
        })
      }
    }
    // 开通时间排序
    async handleOpenTimeChange(val: any) {
      if (val.order == 'descending') {
        this.OnlineSchoolList.sort.openTimeSort = 2
      } else if (val.order == 'ascending') {
        this.OnlineSchoolList.sort.openTimeSort = 1
      } else {
        this.OnlineSchoolList.sort.openTimeSort = 0
      }
      await this.handleLoadData()
    }
    // 修改
    handleUpdate(row: OnlineSchoolItemModule) {
      this.$router.push('/school-management/modify/' + row.schoolId)
    }
    // 重置
    handleReset() {
      this.OnlineSchoolQueryParams = new OnlineSchoolListFilterModel()
      this.openTimeList = []
      this.serviceOverTime = []
      this.serviceArea = []
      this.OnlineSchoolQueryParams.industryIds = []
      this.handleSearch()
    }
  }
</script>
<style lang="scss" scoped>
  .tag-round {
    width: 64px;
    height: 64px;
    border-radius: 64px;
    background-color: #1f86f0;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    line-height: 16px;

    &.is-bg-gray {
      background-color: #999;
      color: #fff;
    }
  }
</style>
