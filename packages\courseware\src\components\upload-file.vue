<template>
  <div>
    <el-upload
      ref="upload"
      :action="actionUrl"
      :headers="headersObj"
      :before-upload="beforeUpload"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-progress="handleProgress"
      :on-error="onError"
      :on-change="handleChange"
      :on-success="onSuccess"
      :http-request="changeUpload"
      :file-list="fileList"
      :auto-upload="false"
      :show-file-list="false"
      :disabled="loading"
      :data="{
        bizType: BizTypeEnum.TYPT_KCXX,
        owner: this.serverId,
        sign: '课件资源上传',
        isPublic: false
      }"
    >
      <el-button type="primary" plain :loading="loading" :disabled="loading">选择文件</el-button>
      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
        <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
        <div slot="content">
          <p>媒体支持文件类型与格式：</p>
          <p>1.文档格式：doc、docx、xls、xlsx、pdf</p>
          <p>2.视频格式：mp4、avi、wmv</p>
        </div>
      </el-tooltip>
    </el-upload>
    <el-dialog title="提示" :visible.sync="dialog" width="450px" class="m-dialog">
      <div>上传失败，请检查网络环境后，重新上传文件</div>
      <div slot="footer">
        <el-button @click="dialog = false">取消</el-button>
        <el-button @click="dialog = false" type="primary">知道了</el-button>
      </div>
    </el-dialog>
    <!-- <el-dialog
      title=""
      :visible.sync="dialog"
      :modal-append-to-body="false"
      width="320px"
      :append-to-body="true"
      :show-close="false"
      center
    >
      <div class="tc">上传失败，请检查网络环境后，重新上传文件</div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialog = false">知道了</el-button>
        <el-button @click="dialog = false">取消</el-button>
      </span>
    </el-dialog> -->
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Ref, Prop } from 'vue-property-decorator'
  import CourseWareCreate from '@api/service/common/models/course/course-ware/CourseWareCreate'
  import CourseChapterCreate from '@api/service/common/models/course/create/CourseChapterCreate'
  import valueComponents from '@/store/modules-ui/course/valueComponents'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import ElementUI from 'element-ui'
  import { ElUploadProgressEvent, HttpRequestOptions } from 'element-ui/types/upload'
  import UploadUtil from '@api/service/common/obs/UploadUtil'
  import ObsUpLoad from '@api/service/common/obs/ObsUpLoad'
  import ObsReceive from '@api/service/common/obs/ObsReceive'
  import { ElUploadInternalFileDetail } from 'element-ui/types/upload'
  import FileInfo from '@api/service/common/file/FileInfo'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import { BizTypeEnum } from '@api/service/common/obs/BizTypeEnum'
  import systemContext from '@api/service/common/context/Context'
  class CourseChapterPercen extends CourseWareCreate {
    percen = 0
    uid = ''
    time = ''
    type = 1 //1 表示文档，2表示视频，3表示多媒体
  }

  @Component
  export default class User extends Vue {
    @Ref('upload') upload: any
    // 文档:1 视频:2 压缩包:3
    @Prop({
      type: Number,
      default: ''
    })
    fileType: number
    @Prop({
      type: Boolean,
      default: false
    })
    @Prop({
      type: Boolean,
      default: false
    })
    isBeginUsed: boolean
    // 上传地址
    actionUrl = ''
    // 请求头
    headersObj = {
      'App-Authentication': '',
      Authorization: ''
    }
    fileList: Array<any> = []
    dialog = false
    type = 1 //1 表示文档，2表示视频，3表示多媒体
    // 上传实例化
    @Prop({
      type: UploadUtil,
      default: () => new UploadUtil()
    })
    uploadUtil: UploadUtil
    // 平台id
    platformId: string
    uploadToken = ''
    applyForm = new FileInfo()
    // * obs文件访问
    obsReceive: ObsReceive = new ObsReceive()
    obsUpLoad = new ObsUpLoad()
    // obs枚举
    BizTypeEnum = BizTypeEnum
    // 网校id
    serverId = ''
    // 加载动画
    loading = false
    // 上传文件之前的钩子
    async beforeUpload(file: ElUploadInternalFileDetail) {
      console.log(file, '上传文件之前的钩子')
      this.$emit('setFileItem', file)
      valueComponents.setUploadingNum(1)
    }

    // 文件预览
    handlePreview() {
      console.log('预览')
    }
    // 文件移除
    handleRemove(file: any) {
      this.$emit('fileFuncall', file.uid, function(
        subItem: CourseChapterPercen,
        subIndex: number,
        item: CourseChapterCreate
      ): void {
        item.courseWares.splice(subIndex, 1)
      })
    }
    cancel() {
      const length = this.fileList.length
      this.fileList.splice(length, 1)
    }
    save() {
      this.upload.submit()
    }
    // 上传中的回掉
    handleProgress(event: any, file: any) {
      this.$emit('fileFuncall', file.uid, (subItem: CourseChapterPercen) => {
        subItem.percen = this.uploadUtil.progress
        subItem.type = this.type
      })
    }
    onError(err: any, file: any) {
      this.loading = false
      console.log(err, file, '失败回调')
      this.dialog = true
      this.$emit('fileFuncall', file.uid, function(
        subItem: CourseChapterPercen,
        subIndex: number,
        item: CourseChapterCreate
      ): void {
        item.courseWares.splice(subIndex, 1)
      })
      valueComponents.setUploadingNum(-1)
    }
    // <p>媒体支持文件类型与格式：</p>
    // <p>1.文档格式：doc、xls、xlsx、pdf</p>
    // <p>2.视频格式：mp4、avi、wmv</p>
    //上传文件
    async handleChange(file: ElUploadInternalFileDetail) {
      try {
        console.log(file, 'el上传的文件信息')
        //const type = file.raw.type.split('/')[0]
        const type = file.raw.type
        const suffix = file.name.substring(file.name.lastIndexOf('.') + 1)
        // TODO 修改 文件类型 和之前的不一致 走到对应校验
        if (!this.checkFileType(this.fileType, suffix) && this.isBeginUsed) {
          this.upload.clearFiles()
          this.$message.error('格式错误，需和替换前的媒体格式一致')
          this.loading = false
          return
        }
        if (
          type.indexOf('video') != -1 &&
          (suffix.indexOf('mp4') != -1 || suffix.indexOf('avi') != -1 || suffix.indexOf('wmv') != -1)
        ) {
          this.type = 2
        } else if (suffix == 'doc' || suffix == 'docx' || suffix == 'xls' || suffix == 'xlsx' || suffix == 'pdf') {
          this.type = 1
        } else {
          this.upload.clearFiles()
          this.$message.error('格式错误，仅支持单视频、文档')
          this.loading = false
          return
        }
        await this.upload.submit()
        this.$emit('update:file-type', this.type)

        this.loading = false
      } catch (e) {
        this.upload.clearFiles()
        this.loading = false
        console.error(e)
      }
    }
    // 成功回调
    onSuccess(response: any, file: ElUploadInternalFileDetail) {
      this.loading = false
      const resUrl = this.applyForm.url
      console.log(file, '成功的回调')
      this.$emit('fileFuncall', file.uid, function(subItem: CourseChapterPercen): void {
        subItem.coursewareResourcePath = resUrl
        subItem.percen = 100
      })
      valueComponents.setUploadingNum(-1)
    }

    async mounted() {
      // 使用obs上传组件
      this.serverId = systemContext.servicerInfo.id
      const biz: { bizType: BizTypeEnum; owner?: string; sign: string; ownerType?: string } = {
        bizType: BizTypeEnum.TYPT_KCXX,
        owner: this.serverId,
        sign: '课件资源上传'
      }
      this.uploadToken = await this.obsUpLoad.getToken(biz)
      console.log('获取token')
    }

    /**
     * 课件格式校验比对
     */
    checkFileType(type: number, suffix: string) {
      if (type == 1) {
        return suffix == 'doc' || suffix == 'docx' || suffix == 'xls' || suffix == 'xlsx' || suffix == 'pdf'
      } else {
        return suffix == 'mp4' || suffix == 'avi' || suffix == 'wmv'
      }
    }

    /**
     * 重写upload上传方法
     */
    async changeUpload(file: HttpRequestOptions) {
      try {
        console.log(file, '重写上传方法')
        this.uploadUtil.progress = 0
        //******示例重点代码****** */
        const request = new ObsUpLoad()
        let token
        if ((file.data as any)?.bizType && (file.data as any)?.sign) {
          token = await request.getToken({
            bizType: (file.data as any).bizType,
            owner: (file.data as any).owner,
            sign: (file.data as any).sign,
            ownerType: (file.data as any).ownerType
          })
        } else {
          return Promise.reject({ code: 500, message: '缺少token必要入参' })
        }
        if ((file.data as any).isPublic === undefined) {
          return Promise.reject({ code: 500, message: '缺少isPublic字段' })
        }
        // 文件名转换防止中文文件名上传报错
        const newFile = new File([file.file], encodeURIComponent(file.file.name), {
          type: file.file.type
        })
        this.loading = true
        const data = await this.uploadUtil.uploadFile(file.file, false, this.serverId, this.uploadToken)
        // this.uploadUtil.progress = 100
        console.log(data, 'data')
        this.loading = false
        this.applyForm.url = data.data.data
        this.applyForm.name = file.file.name
        this.applyForm.type = file.file.type
        console.debug(file.file)
        console.debug(newFile)
      } catch (e) {
        this.loading = false
      }
    }
  }
</script>

<style lang="scss" scoped>
  .course-ware-upload {
    ::v-deep.el-upload--text {
      width: 100%;
    }
  }
  ::v-deep.el-upload-list {
    display: none;
  }
</style>
