import {
  RegionResponse,
  TrainingPointResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

/**
 * 培训点模型
 */
export default class TrainingPlaceInfoDto {
  /**
   * 培训点id
   */
  id = ''
  /**
   * 培训点名称
   */
  trainingPlaceName = ''
  /**
   * 培训点名称
   */
  trainingPlaceRegionNames = ''
  /**
   * 教室名称
   */
  classroomName = ''
  /**
   * 选中的培训地址
   */
  selectAddress = ''
  /**
   * 纬度
   */
  latitude = 0
  /**
   * 经度
   */
  longitude = 0
  /**
   * 培训点地区名称
   */
  regionName = new RegionResponse()

  static toTrainingPlaceInfoDto(dto: TrainingPointResponse) {
    const vo = new TrainingPlaceInfoDto()
    vo.id = dto.id
    vo.trainingPlaceName = dto.name
    vo.classroomName = dto.classRoom
    vo.selectAddress = dto.specificAddressData
    vo.latitude = dto.latitude
    vo.longitude = dto.longitude
    vo.regionName = dto.areaName
    return vo
  }
}
