import BasicData from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
import BasicDataGateway, {
  IndustryPropertyInfoQueryRequest,
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'

/**
 * 运营域行业查询接口
 */
class QueryPersonIndustry {
  /**
   * 行业类型缓存
   */
  IndustryCache = new Array<IndustryVo>()

  /**
   * 科目类型缓存
   */
  private subjectCache = new Map<string, Array<TrainingPropertyResponse>>()

  /**
   * 专业类型缓存
   */
  private trainingCache = new Map<string, Array<TrainingPropertyResponse>>()

  /**
   * 具体专业信息缓存
   */
  private trainingDetailCache = new Map<string, Array<TrainingPropertyResponse>>()

  /**
   * 培训对象缓存
   */
  private trainingObjectCache = new Map<string, Array<TrainingPropertyResponse>>()

  /**
   * 岗位类别缓存
   */
  private positionCategoryCache = new Map<string, Array<TrainingPropertyResponse>>()

  /**
   * 技术等级缓存
   */
  private jobLevelCache = new Map<string, Array<TrainingPropertyResponse>>()

  /**
   * 技术工种缓存
   */
  private jobCategoryCache = new Map<string, Array<TrainingPropertyResponse>>()
  /**
   * 学段缓存
   */
  private sectionCache = new Map<string, Array<TrainingPropertyResponse>>()
  /**
   * 学科缓存
   */
  private subjectsCache = new Map<string, Array<TrainingPropertyResponse>>()
  /**
   * 证书类型缓存
   */
  private certificatesTypeCache = new Map<string, Array<TrainingPropertyResponse>>()
  /**
   * 执业类别缓存
   */
  private practitionerCategoryCache = new Map<string, Array<TrainingPropertyResponse>>()
  /**
   * 运营域超管根据字典 获取行业类型（过滤掉交通运输行业类型）
   */
  async getOperationIndustry() {
    if (!this.IndustryCache.length) {
      const response = await BasicData.listBusinessDataDictionaryInSubProject({
        businessDataDictionaryType: 'INDUSTRY'
      })
      const result = new Array<IndustryVo>()
      if (response.status.isSuccess()) {
        // 过滤掉交通运输行业
        response.data = response.data.filter(item => {
          return item.id != 'industry0221018501809dc4d43e0001'
        })
        response.data.map(item => {
          const industryInfo = new IndustryVo()
          industryInfo.id = item.id
          industryInfo.sort = item.sort
          industryInfo.name = item.name
          result.push(industryInfo)
        })
      }
      this.IndustryCache = result
    }
    return this.IndustryCache
  }

  /**
   * 获取指定分类列表
   */
  getAppointIndustry(id: string) {
    return this.IndustryCache.filter(industry => {
      return industry.id === id
    })
  }

  /**
   * 查询科目类型列表
   * @param id
   */
  async getOperationSubjectList(id: string, industryPropertyId: string) {
    if (!this.subjectCache.get(id + industryPropertyId)) {
      const { data } = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
        industryId: id,
        industryPropertyId: industryPropertyId,
        categoryCode: IndustryPropertyCodeEnum.SUBJECT_TYPE
      })
      this.subjectCache.set(id + industryPropertyId, data)
    }

    return this.subjectCache.get(id + industryPropertyId)
  }

  /**
   * 查询专业类型列表
   */
  async getOperationTraining(id: string, industryPropertyId: string) {
    if (!this.trainingCache.get(id + industryPropertyId)) {
      const { data } = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
        industryId: id,
        industryPropertyId: industryPropertyId,
        categoryCode: IndustryPropertyCodeEnum.TRAINING_CATEGORY
      })
      this.trainingCache.set(id + industryPropertyId, data)
    }

    return this.trainingCache.get(id + industryPropertyId)
  }

  /**
   * 查询类型具体专业
   */
  async getIndustryDetail(id: string, industryPropertyId: string) {
    if (!this.trainingDetailCache.get(id + industryPropertyId)) {
      const { data } = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
        industryId: id,
        industryPropertyId: industryPropertyId,
        categoryCode: IndustryPropertyCodeEnum.TRAINING_PROFESSIONAL
      })
      this.trainingDetailCache.set(id + industryPropertyId, data)
    }

    return this.trainingDetailCache.get(id + industryPropertyId)
  }

  /**
   * 查询培训对象
   */
  async getTrainingObject(id: string, industryPropertyId: string) {
    if (!this.trainingObjectCache.get(id + industryPropertyId)) {
      const { data } = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
        industryId: id,
        industryPropertyId: industryPropertyId,
        categoryCode: IndustryPropertyCodeEnum.TRAINING_OBJECT
      })
      this.trainingObjectCache.set(id + industryPropertyId, data)
    }

    return this.trainingObjectCache.get(id + industryPropertyId)
  }

  /**
   * 查询岗位类别
   */
  async getPositionCategory(id: string, industryPropertyId: string) {
    if (!this.positionCategoryCache.get(id + industryPropertyId)) {
      const { data } = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
        industryId: id,
        industryPropertyId: industryPropertyId,
        categoryCode: IndustryPropertyCodeEnum.POSITION_CATEGORY
      })
      this.positionCategoryCache.set(id + industryPropertyId, data)
    }

    return this.positionCategoryCache.get(id + industryPropertyId)
  }

  /**
   * 查询技术等级
   */
  async getJobLevel(id: string, industryPropertyId: string) {
    if (!this.jobLevelCache.get(id + industryPropertyId)) {
      const { data } = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
        industryId: id,
        industryPropertyId: industryPropertyId,
        categoryCode: IndustryPropertyCodeEnum.JOB_LEVEL
      })
      this.jobLevelCache.set(id + industryPropertyId, data)
    }

    return this.jobLevelCache.get(id + industryPropertyId)
  }

  /**
   * 查询工种
   */
  async getJobCategory(id: string, industryPropertyId: string) {
    if (!this.jobCategoryCache.get(id + industryPropertyId)) {
      const { data } = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
        industryId: id,
        industryPropertyId: industryPropertyId,
        categoryCode: IndustryPropertyCodeEnum.JOB_CATEGORY
      })
      this.jobCategoryCache.set(id + industryPropertyId, data)
    }

    return this.jobCategoryCache.get(id + industryPropertyId)
  }
  /**
   * 查询学段
   */
  async getSection(id: string, industryPropertyId: string) {
    if (!this.sectionCache.get(id + industryPropertyId)) {
      const { data } = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
        industryId: id,
        industryPropertyId: industryPropertyId,
        categoryCode: IndustryPropertyCodeEnum.LEARNING_PHASE
      })
      this.sectionCache.set(id + industryPropertyId, data)
    }

    return this.sectionCache.get(id + industryPropertyId)
  }
  /**
   * 查询学科
   */
  async getSubjects(id: string, industryPropertyId: string) {
    if (!this.subjectsCache.get(id + industryPropertyId)) {
      const { data } = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
        industryId: id,
        industryPropertyId: industryPropertyId,

        categoryCode: IndustryPropertyCodeEnum.DISCIPLINE
      })
      this.subjectsCache.set(id + industryPropertyId, data)
    }

    return this.subjectsCache.get(id + industryPropertyId)
  }

  /**
   * 查询证书类型
   */
  async getCertificatesType(id: string, industryPropertyId: string) {
    if (!this.certificatesTypeCache.get(id + industryPropertyId)) {
      const { data } = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
        industryId: id,
        industryPropertyId: industryPropertyId,
        categoryCode: IndustryPropertyCodeEnum.CERTIFICATES_TYPE
      })
      this.certificatesTypeCache.set(id + industryPropertyId, data)
    }

    return this.certificatesTypeCache.get(id + industryPropertyId)
  }
  /**
   * 查询执业类别
   */
  async getPractitionerCategory(id: string, industryPropertyId: string) {
    if (!this.practitionerCategoryCache.get(id + industryPropertyId)) {
      const { data } = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
        industryId: id,
        industryPropertyId: industryPropertyId,

        categoryCode: IndustryPropertyCodeEnum.PRACTITIONER_CATEGORY
      })
      this.practitionerCategoryCache.set(id + industryPropertyId, data)
    }

    return this.practitionerCategoryCache.get(id + industryPropertyId)
  }
  /**
   * 根据服务商(网校id)、行业id、行业属性类别（业务、人员类别）查询行业属性id
   */
  async getIndustryPropertyId(servicerId?: string, industryIdList?: Array<string>, propertyType?: number) {
    const request = new IndustryPropertyInfoQueryRequest()
    if (servicerId) {
      request.servicerId = servicerId
    }
    request.industryIdList = industryIdList
    request.propertyType = propertyType
    const res = await BasicDataGateway.findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType(request)

    return res?.data || []
  }
}
export default new QueryPersonIndustry()
