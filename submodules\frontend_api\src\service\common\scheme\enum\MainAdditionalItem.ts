import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 培训形式枚举
 * online 网授班
 * offline 面授班
 * mixed 面网授班
 */
export enum MainAdditionalItemEnum {
  MAIN_ITEM = 'qualificationCategory9d43e5a0001',
  ADDITIONAL_ITEM = 'qualificationCategory9d43e5a0002'
}

/**
 * @description 培训形式
 */
class MainAdditionalItem extends AbstractEnum<MainAdditionalItemEnum> {
  static enum = MainAdditionalItemEnum

  constructor(status?: MainAdditionalItemEnum) {
    super()
    this.current = status
    this.map.set(MainAdditionalItemEnum.MAIN_ITEM, '主项')
    this.map.set(MainAdditionalItemEnum.ADDITIONAL_ITEM, '增项')
  }
}

export default new MainAdditionalItem()
