import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-basicdata-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-basicdata-query-front-gateway-forestage-zhjy'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum PersonAccountSortFieldEnum {
  createdTime = 'createdTime',
  nature = 'nature'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 功能描述：账户信息查询条件
@Author： wtl
@Date： 2022年5月11日 15:30:56
 */
export class AccountRequest {
  /**
   * 账户类型 1：企业帐户，2：企业个人帐户，3：个人帐户
@see AccountTypes
   */
  accountTypeList?: Array<number>
  /**
   * 账户状态 1：正常，2：冻结
@see AccountStatus
   */
  statusList?: Array<number>
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest
  /**
   * 创建人用户id
   */
  createdUserId?: string
  /**
   * 单位id （原始单位id，不会随着id 人员与单位的关系变化而变化）
   */
  unitId?: Array<string>
  /**
   * 单位id匹配方式 默认-1、and匹配 2、or匹配
@see MatchTypeConstant
   */
  unitIdMatchType?: number
}

/**
 * 功能描述：登录认证查询条件
@Author： wtl
@Date： 2022年1月26日 09:30:12
 */
export class AuthenticationRequest {
  /**
   * 帐号
   */
  identity?: string
}

/**
 * 功能描述：用户基本查询条件
@Author： wtl
@Date： 2022年1月26日 09:30:12
 */
export class UserRequest {
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  userNameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  phoneMatchType?: number
}

/**
 * 首页资讯查询条件
 */
export class NewsFrontQueryByCodeRequest {
  /**
   * 资讯标题 可为空
   */
  title?: string
  /**
   * 资讯分类代码  必填
   */
  codes?: Array<string>
  /**
   * 顶级分类代码  必填
   */
  rootCategoryCode?: string
}

/**
 * 首页资讯查询条件
 */
export class NewsFrontQueryRequest {
  /**
   * 资讯标题 可为空
   */
  title?: string
  /**
   * 资讯分类编代码  可为空
   */
  code?: string
  /**
   * 顶级分类代码  必填
   */
  rootCategoryCode?: string
}

/**
 * 资讯查询条件
 */
export class NewsQueryByAreaCodePathRequest {
  /**
   * 资讯分类编号
   */
  necId: string
  /**
   * 资讯发布的地区编码
   */
  areaCodePath: string
}

/**
 * 资讯查询条件
 */
export class NewsQueryCommonRequest {
  /**
   * 标题
   */
  title?: string
  /**
   * 资讯分类编号
   */
  necId?: string
  /**
   * 资讯发布的地区编码
   */
  areaCodePath?: string
  /**
   * 是否包含下级地区 -默认 false不包含 比如/350000 只查询 /350000的
   */
  includeSubordinates?: boolean
  /**
   * 排序
   */
  order?: Array<OrderRequest>
}

/**
 * 资讯查询条件，支持针对不同字段
 */
export class NewsQueryForOrderRequest {
  /**
   * 资讯分类编号
   */
  necId: string
  /**
   * 排序字段名称，格式如下
发布时间：0
浏览数量：1
<p>
若排序字段为-1，默认按照从置顶到非置顶，发布时间从新到旧顺序排列
   */
  orderFiled?: number
  /**
   * 排序方式
0 升序
1 降序
   */
  orderType?: number
}

/**
 * 资讯查询条件
 */
export class NewsWithPreviousAndNextCommonRequest {
  /**
   * 资讯编号
   */
  newId?: string
  /**
   * 资讯发布的地区编码
   */
  areaCodePath?: string
}

export class OrderRequest {
  /**
   * 排序字段
发布时间：0
浏览数量：1
置顶(0-不置顶 1-置顶)：   2
发布地区编码: 3
   */
  orderField?: number
  /**
   * 排序方式  0 升序   1 降序
   */
  orderType?: number
}

/**
 * 功能描述：学员集体缴费信息
@Author： wtl
@Date： 2022年4月21日 08:58:49
 */
export class CollectiveRequest {
  /**
   * 集体缴费管理员用户id集合
   */
  collectiveUserIdList?: Array<string>
}

/**
 * 功能描述：学员查询条件
@Author： wtl
@Date： 2022年1月26日 10:10:33
 */
export class UserInfoRequest {
  /**
   * 工作单位名称（模糊）
   */
  companyName?: string
  /**
   * 单位所属地区路径集合（模糊，右like）
   */
  regionPathList?: Array<string>
  /**
   * 单位所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  regionPathListMatchType?: number
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  userNameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  phoneMatchType?: number
}

/**
 * 功能描述：学员查询条件
@Author： wtl
@Date： 2022年1月26日 10:10:33
 */
export class UserQueryRequest {
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: UserInfoRequest
  /**
   * 用户认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 集体缴费信息
   */
  collective?: CollectiveRequest
  /**
   * 排序
   */
  sortList?: Array<UserSortRequest>
}

/**
 * 功能描述 : 学员排序参数
@date : 2022/4/1 17:15
 */
export class UserSortRequest {
  /**
   * 排序字段
   */
  sortField?: PersonAccountSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

export class DateScopeRequest {
  beginTime?: string
  endTime?: string
}

export class RegionModel {
  regionId: string
  regionPath: string
  provinceId: string
  provinceName: string
  cityId: string
  cityName: string
  countyId: string
  countyName: string
}

/**
 * 功能描述：账户信息
@Author： wtl
@Date： 2022年5月11日 15:30:56
 */
export class AccountResponse {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
@see AccountTypes
   */
  accountType: number
  /**
   * 单位信息
   */
  unitInfo: UnitInfoResponse
  /**
   * 所属顶级企业帐户Id
   */
  rootAccountId: string
  /**
   * 帐户状态 1：正常，2：冻结，3：注销
@see AccountStatus
   */
  status: number
  /**
   * 注册方式
0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
@see AccountRegisterTypes
   */
  registerType: number
  /**
   * 来源类型
0内置，1项目主网站，2安卓，3IOS
@see AccountSourceTypes
   */
  sourceType: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 最后更新时间
   */
  lastUpdateTime: string
}

/**
 * 功能描述：帐户认证信息
@Author： wtl
@Date： 2022年5月11日 14:23:18
 */
export class AuthenticationResponse {
  /**
   * 帐号
   */
  identity: string
  /**
   * 认证标识类型
1用户名,2手机,3身份证,4邮箱,5第三方OpenId
   */
  identityType: number
  /**
   * 认证方式状态 1启用，2禁用
@see AuthenticationStatusEnum
   */
  status: number
}

/**
 * 功能描述：角色信息
@Author： wtl
@Date： 2022/1/24 20:17
 */
export class RoleResponse {
  /**
   * 角色id
   */
  roleId: string
  /**
   * 角色名称
   */
  roleName: string
  /**
   * 角色类型
（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
   */
  roleType: string
  /**
   * 角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
@see RoleCategories
   */
  roleCategory: number
}

/**
 * 功能描述 : 企业单位管理员信息
@date : 2022/6/18 12:24
 */
export class EnterpriseUnitAdminInfoResponse {
  /**
   * 企业单位管理员归属信息
   */
  enterpriseUnitAdminOwner: EnterpriseUnitAdminOwnerResponse
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 管理员用户信息
   */
  userInfo: AdminUserInfoResponse
  /**
   * 人员信息
   */
  personInfo: EnterpriseUnitPersonInfoResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
  /**
   * 角色信息集合
   */
  roleList: Array<RoleResponse>
}

/**
 * 功能描述：管理员用户信息
@Author： wtl
@Date： 2022年1月25日 15:48:48
 */
export class AdminUserInfoResponse {
  /**
   * 管辖地区集合
   */
  manageRegionList: Array<RegionModel>
  /**
   * 办公室（所在处/科室）
   */
  office: string
  /**
   * 岗位/职位
   */
  position: string
  /**
   * 备注
   */
  remark: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 性别
-1未知，0女，1男
@see Genders
   */
  gender: number
  /**
   * 证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string
}

/**
 * 功能描述 : 企业单位管理员归属查询条件
@date : 2022年9月2日 10:53:02
 */
export class EnterpriseUnitAdminOwnerResponse {
  /**
   * 企业单位id
   */
  enterpriseUnitIdList: Array<string>
}

/**
 * 人员信息模型
 */
export class EnterpriseUnitPersonInfoResponse {
  /**
   * 是否法人帐号
   */
  isCorporateAccount: boolean
  /**
   * 人员实名认证信息
   */
  personIdentityVerificationInfo: PersonIdentityVerificationResponse
}

/**
 * 人员实名认证信息模型
 */
export class PersonIdentityVerificationResponse {
  /**
   * 是否已认证
   */
  identityVerification: boolean
  /**
   * 认证渠道(1:闽政通 2：腾讯)
   */
  identityVerificationChannel: number
  /**
   * 认证时间
   */
  identityVerificationTime: string
}

/**
 * 单位信息模型
 */
export class UnitInfoResponse {
  /**
   * 单位ID
   */
  unitId: string
}

/**
 * 网校前端查询的资讯信息
 */
export class CompleteNewsByPublishResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 摘要
   */
  summary: string
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 图片路径
   */
  coverPath: string
  /**
   * 分类名称
   */
  name: string
}

/**
 * 资讯分类信息
 */
export class NewsCategoryResponse {
  /**
   * 资讯分类编号
   */
  newsCategoryId: string
  /**
   * 分类名称
   */
  categoryName: string
  /**
   * 分类代码
   */
  code: string
}

/**
 * 详细资讯信息
 */
export class NewsDetailResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 平台id
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目id
   */
  projectId: string
  /**
   * 子项目id
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商id
   */
  serviceId: string
  /**
   * 分类id
   */
  necId: string
  /**
   * 标题
   */
  title: string
  /**
   * 摘要
   */
  summary: string
  /**
   * 内容
   */
  content: string
  /**
   * 封面图片路径
   */
  coverPath: string
  /**
   * 来源
   */
  source: string
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 发布地区编码
   */
  areaCodePath: string
  /**
   * 资讯状态 0 草稿 1正常
   */
  status: number
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 浏览数量
   */
  reviewCount: number
  /**
   * 弹窗起始时间
   */
  popupBeginTime: string
  /**
   * 弹窗截止时间
   */
  popupEndTime: string
}

/**
 * 详细资讯信息及上下资讯id
 */
export class NewsDetailWithPreviousAndNext {
  /**
   * 详细资讯信息
   */
  newsDetail: NewsDetailResponse
  /**
   * 上一条资讯id
   */
  previousId: string
  /**
   * 下一条资讯id
   */
  nextId: string
}

/**
 * 详细资讯信息及上下资讯id
 */
export class NewsDetailWithPreviousAndNextCommonResponse {
  /**
   * 详细资讯信息
   */
  newsDetail: NewsDetailResponse
  /**
   * 上一条资讯id
   */
  previousId: string
  /**
   * 下一条资讯id
   */
  nextId: string
}

/**
 * 资讯信息
 */
export class NewsInfoResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 资讯分类id
   */
  necId: string
}

/**
 * 网校前端查询的资讯信息
 */
export class SimpleNewsByPublishCommonResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 摘要
   */
  summary: string
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
}

/**
 * 网校前端查询的资讯信息
 */
export class SimpleNewsByPublishResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 摘要
   */
  summary: string
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
}

/**
 * 网校前端查询的资讯信息
 */
export class SimpleNewsByPublishResponseWithReviewCount {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 摘要
   */
  summary: string
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 浏览数量
   */
  reviewCount: number
}

/**
 * 功能描述：学员信息
@Author： wtl
@Date： 2022年1月26日 10:38:15
 */
export class UserInfoResponse {
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 用户信息
   */
  userInfo: UserInformationResponse
  /**
   * 人员信息
   */
  personInfo: PersonInfoResponse
  /**
   * 第三方绑定信息
   */
  openPlatformBind: OpenPlatformBindResponse
}

/**
 * 功能描述：附件信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class AttachmentInfoResponse {
  /**
   * 附件名称
   */
  name: string
  /**
   * 附件地址
   */
  url: string
}

/**
 * 功能描述：学员绑定信息
@Author： wtl
@Date： 2022年5月12日 14:42:51
 */
export class OpenPlatformBindResponse {
  /**
   * 是否绑定微信
   */
  bindWX: boolean
  /**
   * 微信昵称
   */
  nickNameByWX: string
}

export class PersonInfoResponse {
  /**
   * 人员ID
   */
  personId: string
  /**
   * 姓名
   */
  name: string
  /**
   * 证件类型
@see IdCardTypes
   */
  idCardType: number
  /**
   * 身份证号
   */
  idCard: string
  /**
   * 性别
@see Genders
   */
  gender: number
  /**
   * 出生日期
   */
  birthday: string
  /**
   * 民族（字典）
   */
  ethnicity: string
  /**
   * 居住地区
   */
  resideCityArea: RegionResponse
  /**
   * 联系地址
   */
  address: string
  /**
   * 手机号
   */
  phone: string
}

export class RegionResponse {
  /**
   * 地区ID
   */
  regionId: string
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 省份ID
   */
  provinceId: string
  /**
   * 省份名称
   */
  provinceName: string
  /**
   * 地市ID
   */
  cityId: string
  /**
   * 地市名称
   */
  cityName: string
  /**
   * 区县ID
   */
  countyId: string
  /**
   * 区县名称
   */
  countyName: string
}

/**
 * 功能描述：学员证书信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class StudentCertificateResponse {
  /**
   * 证书id
   */
  certificateId: string
  /**
   * 证书编号
   */
  certificateNo: string
  /**
   * 证书类别
   */
  certificateCategory: string
  /**
   * 注册专业
   */
  registerProfessional: string
  /**
   * 发证日期
   */
  releaseStartTime: string
  /**
   * 证书有效期
   */
  certificateEndTime: string
  /**
   * 证书附件信息
   */
  attachmentList: Array<AttachmentInfoResponse>
}

/**
 * 功能描述：学员行业信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class StudentIndustryResponse {
  /**
   * 用户行业id
   */
  userIndustryId: string
  /**
   * 行业id
   */
  industryId: string
  /**
   * 一级专业类别id
   */
  firstProfessionalCategory: string
  /**
   * 二级专业类别id
   */
  secondProfessionalCategory: string
  /**
   * 职称等级
   */
  professionalQualification: string
  /**
   * 学员证书信息集合
   */
  userCertificateList: Array<StudentCertificateResponse>
}

/**
 * 功能描述：用户信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class UserInformationResponse {
  /**
   * 用户昵称
   */
  nickName: string
  /**
   * 单位所属地区
   */
  region: RegionModel
  /**
   * 工作单位名称
   */
  companyName: string
  /**
   * 头像地址
   */
  photo: string
  /**
   * 联系地址
   */
  address: string
  /**
   * 学员行业信息集合
   */
  userIndustryList: Array<StudentIndustryResponse>
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 性别
-1未知，0女，1男
@see Genders
   */
  gender: number
  /**
   * 证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string
}

/**
 * 功能描述：企业单位信息
@Author： wtl
@Date： 2022年6月9日 14:20:55
 */
export class EnterpriseUnitInfoResponse {
  /**
   * 企业单位业务归属信息
   */
  businessOwnerInfo: EnterpriseUnitBusinessOwnerResponse
  /**
   * 单位基本信息
   */
  unitBase: EnterpriseUnitBaseResponse
  /**
   * 经营信息
   */
  businessInfo: BusinessInfoResponse
  /**
   * 单位认证信息
   */
  unitIdentityVerificationInfo: UnitIdentityVerificationResponse
}

/**
 * 功能描述：附件信息
 */
export class AttachmentResponse {
  /**
   * 附件id
   */
  attachmentId: string
  /**
   * 附件类型
1《营业执照 / 民办法人登记证书/非民办企业法人登记证书》
2《开展短期职业培训承诺书》（加盖公司公章）》
3《培训单位基本信息表》（加盖公司公章）》
4《培训单位办学许可证》（加盖公司公章）》
5《年度年审合格证书》（加盖公司公章）》
6 开通依据
   */
  attachmentType: number
  /**
   * 附件名称
   */
  attachmentName: string
  /**
   * 附件路径
   */
  attachmentPath: string
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 功能描述：企业经营信息
@Author： wtl
@Date： 2022年6月9日 14:31:03
 */
export class BusinessInfoResponse {
  /**
   * 营业期限起始日期
   */
  operatingBeginDate: string
  /**
   * 营业期限截止日期
   */
  operatingEndDZhjyEnterpriseUnitBackStageQueryResolverate: string
  /**
   * 行业信息
   */
  industry: IndustryResponse
  /**
   * 经营范围,例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。
   */
  businessScope: string
  /**
   * 主营业务
例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。
   */
  mainBusiness: string
}

/**
 * 功能描述：单位联系人信息
 */
export class ContactPersonInfoResponse {
  /**
   * 联系人
   */
  contact: string
  /**
   * 联系电话
   */
  contactPhone: string
}

/**
 * 功能描述：企业单位信息
@Author： wtl
@Date： 2022年6月9日 14:23:04
 */
export class EnterpriseUnitBaseResponse {
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 单位英文名称
   */
  enName: string
  /**
   * 统一社会信用代码
   */
  code: string
  /**
   * 单位简称
   */
  shortName: string
  /**
   * 单位业务类型
说明：
1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
11地区管理单位,12行业主管单位,13技工院校,14职业院校.15线上培训机构,10000实名制报表补贴单位,10001评价机构
@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
@see UnitBusinessQueryTypes
   */
  businessType: number
  /**
   * logo
   */
  logo: string
  /**
   * 法人信息
   */
  legalPersonInfo: LegalPersonResponse
  /**
   * 单位类型（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）
   */
  unitType: UnitTypeResponse
  /**
   * 单位规模（1：微型 2：小型 3：中型 4：大型）
@see com.fjhb.domain.basicdata.api.unit.consts.UnitScales
   */
  scale: number
  /**
   * 企业经济类型(国有经济、联营经济、私营企业、股份制、港澳台投资、外商投资、其他经济)
@see com.fjhb.domain.basicdata.api.unit.consts.UnitEconomicTypes
   */
  economicTypes: number
  /**
   * 产业类别
   */
  industrialCategory: string
  /**
   * 成立日期
   */
  foundedDate: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 邮政编码
   */
  postCode: string
  /**
   * 传真
   */
  faxNumber: string
  /**
   * 注册地区
   */
  region: RegionModel
  /**
   * 联系地址
   */
  address: string
  /**
   * 注册地址
   */
  registerAddress: string
  /**
   * 登记机关
   */
  registeredOrgan: string
  /**
   * 注册资金
   */
  registeredCapital: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 单位状态
说明：1正常,2冻结
   */
  status: number
  /**
   * 工商注册号
   */
  businessRegistrationNumber: string
  /**
   * 纳税人资质
   */
  taxpayerQualification: string
  /**
   * 邮箱地址
   */
  emailAddress: string
  /**
   * 联系人信息
   */
  contactPersonInfo: ContactPersonInfoResponse
  /**
   * 单位资质附件类型
   */
  attachmentList: Array<AttachmentResponse>
}

/**
 * 企业单位业务归属信息
 */
export class EnterpriseUnitBusinessOwnerResponse {
  /**
   * 企业归属信息路径
单位路径（若单位为福州市企业，则该值为:&quot;/福建省企业id/福州市企业id&quot;）
   */
  unitIdPath: string
}

/**
 * 行业信息
<AUTHOR>
@date 2022-06-18
 */
export class IndustryResponse {
  /**
   * 行业信息ID路径
   */
  industryIdPath: string
  /**
   * 门类
   */
  firstLevelIndustryId: string
  /**
   * 大类
   */
  secondLevelIndustryId: string
  /**
   * 中类
   */
  thirdLevelIndustryId: string
  /**
   * 小类
   */
  fourthLevelIndustryId: string
}

/**
 * 功能描述：企业法人信息
@Author： wtl
@Date： 2022年6月9日 14:31:03
 */
export class LegalPersonResponse {
  /**
   * 法定代表人
   */
  legalPerson: string
  /**
   * 证件类型
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
}

/**
 * 单位认证信息模型
 */
export class UnitIdentityVerificationResponse {
  /**
   * 是否已认证
   */
  identityVerification: boolean
  /**
   * 认证渠道（单位认证渠道：UnitIdentityVerificationChannels 人员认证渠道：PersonIdentityVerificationChannels）
@see PersonIdentityVerificationChannels
@see UnitIdentityVerificationChannels
   */
  identityVerificationChannel: number
  /**
   * 认证时间
   */
  identityVerificationTime: string
}

/**
 * 单位类型:（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）
<AUTHOR>
@date : 2022/6/18 14:15
 */
export class UnitTypeResponse {
  /**
   * 单位类型ID路径
   */
  unitTypeIdPath: string
  /**
   * 一级
   */
  firstLevelUnitTypeId: string
  /**
   * 二级
   */
  secondLevelUnitTypeId: string
  /**
   * 三级
   */
  thirdLevelUnitTypeId: string
}

export class SimpleNewsByPublishCommonResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SimpleNewsByPublishCommonResponse>
}

export class CompleteNewsByPublishResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CompleteNewsByPublishResponse>
}

export class SimpleNewsByPublishResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SimpleNewsByPublishResponse>
}

export class SimpleNewsByPublishResponseWithReviewCountPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SimpleNewsByPublishResponseWithReviewCount>
}

export class UserInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserInfoResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * @Description  身份证姓名获取角色id
   * <AUTHOR>
   * @Date 2023/5/8 15:33
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async findRoleListByIdentity(
    request: UserRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findRoleListByIdentity,
    operation?: string
  ): Promise<Response<Array<RoleResponse>>> {
    return commonRequestApi<Array<RoleResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯展示详细
   * @param commonRequest 通用查询条件，与查询列表相同的查询条件
   * @return NewsDetailWithPreviousAndNext 详细资讯信息及上下资讯id
   * @param query 查询 graphql 语法文档
   * @param commonRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCommonNewsDetailWithPreviousAndNext(
    commonRequest: NewsWithPreviousAndNextCommonRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCommonNewsDetailWithPreviousAndNext,
    operation?: string
  ): Promise<Response<NewsDetailWithPreviousAndNextCommonResponse>> {
    return commonRequestApi<NewsDetailWithPreviousAndNextCommonResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { commonRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：通用 根据账户ID查询企业单位管理员详情查询接口
   * 描述：查询当前企业单位下指定管理员的信息，如不存在返回null
   * @param accountId               : 帐户ID
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
   * @date : 2022年6月9日 16:50:10
   * @param query 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnterpriseUnitAdminInfoByPublish(
    accountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitAdminInfoByPublish,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponse>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：企业-当前登录企业管理员信息
   * 描述：查询当前登录管理员的信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnterpriseUnitAdminInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitAdminInfoInMyself,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponse>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：人社讲师-查询企业单位信息-详情接口
   * 描述：人社讲师-查询企业单位信息-详情接口
   * @param unitId                  :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse
   * @date : 2022年6月9日 10:49:47
   * @param query 查询 graphql 语法文档
   * @param unitId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnterpriseUnitInfoInLibraryTeacher(
    unitId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitInfoInLibraryTeacher,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponse>> {
    return commonRequestApi<EnterpriseUnitInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { unitId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：学员-查询企业单位信息-详情接口
   * 描述：学员-查询企业单位信息-详情接口
   * @param unitId                  :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse
   * @date : 2022年6月9日 10:49:47
   * @param query 查询 graphql 语法文档
   * @param unitId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getEnterpriseUnitInfoInStudent(
    unitId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitInfoInStudent,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponse>> {
    return commonRequestApi<EnterpriseUnitInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { unitId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据分类id获取顶级分类信息
   * @param rootCategoryCode 顶级分类代码
   * @param code 分类代码
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getNewsCategoryId(
    params: { rootCategoryCode: string; code: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getNewsCategoryId,
    operation?: string
  ): Promise<Response<NewsCategoryResponse>> {
    return commonRequestApi<NewsCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯展示详细
   * @param newId 资讯id
   * @return NewsDetailWithPreviousAndNext 详细资讯信息及上下资讯id
   * @param query 查询 graphql 语法文档
   * @param newId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getNewsDetailWithPreviousAndNext(
    newId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getNewsDetailWithPreviousAndNext,
    operation?: string
  ): Promise<Response<NewsDetailWithPreviousAndNext>> {
    return commonRequestApi<NewsDetailWithPreviousAndNext>(
      SERVER_URL,
      {
        query: query,
        variables: { newId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述 :用户-查询当前登录用户-详情接口
   * 描述：查询当前登录用户的详细信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.StudentInfoResponse
   * @date : 2022/3/31 16:54
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUserInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getUserInfoInMyself,
    operation?: string
  ): Promise<Response<UserInfoResponse>> {
    return commonRequestApi<UserInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listChildNewsCategory(
    params: { status: number; necId: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listChildNewsCategory,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询弹窗公告列表
   * @param topNum top数量,在1~50之间
   * @return 资讯信息列表
   * @param query 查询 graphql 语法文档
   * @param topNum 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listPopUpsNews(
    topNum: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listPopUpsNews,
    operation?: string
  ): Promise<Response<Array<NewsInfoResponse>>> {
    return commonRequestApi<Array<NewsInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { topNum },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询浏览数最多的资讯列表
   * @param topNum top数量,在1~50之间
   * @return 资讯信息列表
   * @param query 查询 graphql 语法文档
   * @param topNum 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listReviewTopNews(
    topNum: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listReviewTopNews,
    operation?: string
  ): Promise<Response<Array<NewsInfoResponse>>> {
    return commonRequestApi<Array<NewsInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { topNum },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param status 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listRootNewsCategory(
    status: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listRootNewsCategory,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { status },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询最多资讯的资讯分类列表
   * @param necId  排除的资讯分类id
   * @param topNum top数量,在1~50之间
   * @return NewsCategoryResponse 资讯分类信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listTopNewsCategory(
    params: { necId: string; topNum: number },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTopNewsCategory,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯分页列表-通用
   * @param request 信息中心资讯查询条件(资讯分类id与资讯发布的地区编码)
   * @return 资讯分页列表信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCommonSimpleNewsByPublish(
    params: { request?: NewsQueryCommonRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommonSimpleNewsByPublish,
    operation?: string
  ): Promise<Response<SimpleNewsByPublishCommonResponsePage>> {
    return commonRequestApi<SimpleNewsByPublishCommonResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 首页根据条件查询资讯列表
   * @param newsFrontQueryRequest 首页资讯查询条件
   * @param page                  分页信息
   * @return
   * @throws InvalidProtocolBufferException
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCompleteNewsByCodeList(
    params: { newsFrontQueryRequest?: NewsFrontQueryByCodeRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCompleteNewsByCodeList,
    operation?: string
  ): Promise<Response<CompleteNewsByPublishResponsePage>> {
    return commonRequestApi<CompleteNewsByPublishResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 首页根据顶级分类代码查询资讯列表
   * @param newsFrontQueryRequest 首页资讯查询条件
   * @param page                  分页信息
   * @return
   * @throws InvalidProtocolBufferException
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCompleteNewsByRootCategoryCode(
    params: { newsFrontQueryRequest?: NewsFrontQueryRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCompleteNewsByRootCategoryCode,
    operation?: string
  ): Promise<Response<CompleteNewsByPublishResponsePage>> {
    return commonRequestApi<CompleteNewsByPublishResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯分页列表
   * @param necId 资讯分类id
   * @return 资讯分页列表信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSimpleNewsByPublish(
    params: { necId: string; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSimpleNewsByPublish,
    operation?: string
  ): Promise<Response<SimpleNewsByPublishResponsePage>> {
    return commonRequestApi<SimpleNewsByPublishResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯分页列表
   * @param request 信息中心资讯查询条件(资讯分类id与资讯发布的地区编码)
   * @return 资讯分页列表信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSimpleNewsByPublishAndAreaCodePath(
    params: { request?: NewsQueryByAreaCodePathRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSimpleNewsByPublishAndAreaCodePath,
    operation?: string
  ): Promise<Response<SimpleNewsByPublishResponsePage>> {
    return commonRequestApi<SimpleNewsByPublishResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯分页列表，可以根据排序字段进行排序
   * @param request 信息中心资讯查询条件
   * @return 资讯分页列表信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSimpleNewsByPublishForOrder(
    params: { request?: NewsQueryForOrderRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSimpleNewsByPublishForOrder,
    operation?: string
  ): Promise<Response<SimpleNewsByPublishResponseWithReviewCountPage>> {
    return commonRequestApi<SimpleNewsByPublishResponseWithReviewCountPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：查询用户信息列表-分页接口
   * 描述：查询子项目下的学员分页信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 : 查询参数
   * @param dataFetchingEnvironment :
   * @date : 2022年11月7日 09:37:49
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUserInfoInGeneral(
    params: { page?: Page; request?: UserQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageUserInfoInGeneral,
    operation?: string
  ): Promise<Response<UserInfoResponsePage>> {
    return commonRequestApi<UserInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
