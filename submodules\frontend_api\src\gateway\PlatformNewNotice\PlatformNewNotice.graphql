schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""检查分类名称是否重复
		@param name
		@return true:重复；false:不重复
	"""
	checkCategoryNameExist(name:String):Boolean!
	"""根据资讯id查询上一条或下一条资讯信息
		@param id             当前资讯id
		@param queryParameter 查询条件
		@param location       查询位置 0 上一条 1下一条
		@return 资讯信息
	"""
	getLastOrNextNewsNotice(id:String,queryParameter:NewsNoticeCondition,location:Int!):NewsNoticeDetailDTO @NotAuthenticationRequired
	"""根据咨询id查询资讯详情
		@param id
		@return
	"""
	getNewsNotice(id:String):NewsNoticeDetailDTO @NotAuthenticationRequired
	"""获取当前时间的弹窗公告
		@return
	"""
	getPopNewsNotice:PopNewsNoticeDTO @NotAuthenticationRequired
	"""获取资讯分类详情
		@return
	"""
	listAllCategory:[CategoryDetailDTO] @NotAuthenticationRequired
	"""获取子分类
		@return
	"""
	listSubCategoryByParentId(parentId:String):[CategoryDetailDTO] @NotAuthenticationRequired
	pageNewsNotice(page:Page,queryParam:NewsNoticeCondition):NewsNoticeDTOPage @page(for:"NewsNoticeDTO") @NotAuthenticationRequired
}
type Mutation {
	"""校验草稿状态的资讯是否可以发布
		@param id
		@return
	"""
	checkPublishAble(id:String):Boolean!
	"""创建资讯
		@param request
		@return
	"""
	create(request:NewsDTO):Boolean!
	"""删除资讯
		@param id
		@return
	"""
	deleteById(id:String):Boolean!
	"""删除资讯类别
		@param id
		@return
	"""
	deleteNewsCategory(id:String):Boolean!
	"""发布草稿状态的资讯
		@param id
		@return
	"""
	publish(id:String):Boolean!
	"""置为草稿
		@param id
		@return
	"""
	toDraft(id:String):Boolean!
	"""编辑资讯
		@param request
		@return
	"""
	update(request:NewsDTO):Boolean!
	"""创建、更新资讯类别，当id为空时创建
		@param newsCategoryUpdateDTO
		@return
	"""
	updateNewsCategory(newsCategoryUpdateDTO:NewsCategoryUpdateDTO):Boolean!
}
"""@author: eleven
	@date: 2020/2/12
	@description:
"""
input NewsNoticeCondition @type(value:"com.fjhb.btpx.platform.gateway.web.admin.dto.condition.NewsNoticeCondition") {
	"""资讯标题"""
	title:String
	"""资讯模糊查询（标题或内容）"""
	titleOrContent:String
	"""关联展示的施教机构"""
	associateUnitId:String
	"""所挂的施教机构分类id"""
	categoryId:String
	"""资讯分类类别
		@See com.fjhb.btpx.platform.service.news.NoticeCategoryEnum
	"""
	categoryType:String
	"""资讯状态 -1 不查 0-草稿 1-发布
		如果是查询启用的接口，该参数无效
	"""
	status:Int!
	"""类型 1-资讯 2-公告"""
	type:Int!
	"""资讯所属的单位id （培训机构id）"""
	unitId:String
	"""地区"""
	regionId:String
	"""排序方式 默认1 1按发布时间倒序 2浏览量倒序"""
	sortType:Int
}
"""@author: eleven
	@date: 2020/2/12
	@description:
"""
input NewsDTO @type(value:"com.fjhb.btpx.platform.gateway.web.admin.dto.request.NewsDTO") {
	"""主键id 修改时有值 新增时没值"""
	id:String
	"""标题"""
	title:String
	"""资讯、公告类型（1-资讯 2-公告）"""
	type:Int!
	"""封面图片"""
	photoUrl:String
	"""内容"""
	content:String
	"""分类id"""
	categoryId:String
	"""发布状态 0-草稿 1-发布 2-定时发布"""
	status:Int!
	"""发布时间"""
	publishTime:String
	"""资讯来源"""
	origin:String
	"""资讯作者"""
	author:String
	"""弹窗起始时间"""
	popStart:String
	"""弹窗结束时间"""
	popOver:String
	"""关联的单位集合"""
	associateUnitList:[NewsAssociateUnitDTO1]
	"""是否为重要咨询"""
	isImportance:Int!
	"""在发布的地区"""
	regionIds:[String]
}
"""@author: eleven
	@date: 2020/4/21
"""
input NewsAssociateUnitDTO1 @type(value:"com.fjhb.btpx.platform.service.news.dto.NewsAssociateUnitDTO") {
	"""关联展示的施教机构"""
	associateUnitId:String
	"""所挂的施教机构分类id"""
	categoryId:String
}
"""资讯更新/创建dto
	Author:FangKunSen
	Time:2020-06-09,17:23
"""
input NewsCategoryUpdateDTO @type(value:"com.fjhb.btpx.platform.service.news.dto.NewsCategoryUpdateDTO") {
	"""当id为空时执行创建"""
	id:String
	"""父分类id，0为顶级分类"""
	parentId:String
	"""分类名称"""
	name:String
	"""分类说明"""
	description:String
	"""状态 0-停用 1-启用"""
	status:Int!
	"""是否内置"""
	internal:Boolean!
	"""资讯类别类型
		@see com.fjhb.btpx.platform.service.news.NoticeCategoryEnum
	"""
	categoryType:String
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""@author: eleven
	@date: 2020/2/13
	@description:
"""
type CategoryDetailDTO @type(value:"com.fjhb.btpx.platform.gateway.web.admin.dto.response.CategoryDetailDTO") {
	"""id"""
	id:String
	"""父分类id"""
	parentId:String
	"""分类名称"""
	name:String
	"""分类说明"""
	description:String
	"""状态 0-停用 1-启用"""
	status:Int!
	"""是否内置"""
	internal:Boolean!
	"""资讯类别类型
		@see com.fjhb.btpx.platform.service.news.NoticeCategoryEnum
	"""
	categoryType:String
}
"""@author: eleven
	@date: 2020/2/12
	@description:
"""
type NewsNoticeDTO @type(value:"com.fjhb.btpx.platform.gateway.web.admin.dto.response.NewsNoticeDTO") {
	"""资讯id"""
	id:String
	"""标题"""
	title:String
	"""封面图片路径"""
	photoUrl:String
	"""公告、资讯发布状态（0-草稿 1-已发布）"""
	status:Int!
	"""发布时间"""
	publishTime:DateTime
	"""发布者Id"""
	publishPersonId:String
	"""精简资讯内容
		分页返回默认10个字
	"""
	content:String
	"""被阅读次数"""
	readCount:Long!
	"""是否为重要咨询"""
	isImportance:Int!
	"""关联的单位集合"""
	associateUnitList:[NewsAssociateUnitDTO]
	"""在发布的地区"""
	regionIds:[String]
	"""是否弹窗公告"""
	pop:Boolean!
}
"""@author: eleven
	@date: 2020/2/13
	@description:
"""
type NewsNoticeDetailDTO @type(value:"com.fjhb.btpx.platform.gateway.web.admin.dto.response.NewsNoticeDetailDTO") {
	"""资讯、公告类型（1-资讯 2-公告）"""
	type:Int!
	"""一级分类id"""
	firstLevelCategoryId:String
	"""二级分类id"""
	secondLevelCategoryId:String
	"""资讯来源"""
	origin:String
	"""弹窗起始时间"""
	popStart:DateTime
	"""弹窗结束时间"""
	popOver:DateTime
	"""资讯id"""
	id:String
	"""标题"""
	title:String
	"""封面图片路径"""
	photoUrl:String
	"""公告、资讯发布状态（0-草稿 1-已发布）"""
	status:Int!
	"""发布时间"""
	publishTime:DateTime
	"""发布者Id"""
	publishPersonId:String
	"""精简资讯内容
		分页返回默认10个字
	"""
	content:String
	"""被阅读次数"""
	readCount:Long!
	"""是否为重要咨询"""
	isImportance:Int!
	"""关联的单位集合"""
	associateUnitList:[NewsAssociateUnitDTO]
	"""在发布的地区"""
	regionIds:[String]
	"""是否弹窗公告"""
	pop:Boolean!
}
"""弹窗公告
	Author:FangKunSen
	Time:2020-06-26,10:56
"""
type PopNewsNoticeDTO @type(value:"com.fjhb.btpx.platform.gateway.web.admin.dto.response.PopNewsNoticeDTO") {
	"""当前时间段是否存在弹窗公告"""
	hasPopNewsNoticeNow:Boolean!
	"""弹窗公告主体
		当不存在弹窗公告时，该实体为空
	"""
	newsNoticeDTO:NewsNoticeDTO
}
"""@author: eleven
	@date: 2020/4/21
"""
type NewsAssociateUnitDTO @type(value:"com.fjhb.btpx.platform.service.news.dto.NewsAssociateUnitDTO") {
	"""关联展示的施教机构"""
	associateUnitId:String
	"""所挂的施教机构分类id"""
	categoryId:String
}

scalar List
type NewsNoticeDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [NewsNoticeDTO]}
