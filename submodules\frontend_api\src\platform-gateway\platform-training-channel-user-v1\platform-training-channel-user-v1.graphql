schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""注册学员账号
		@param createInfo 注册学员账号信息 {输入的短信验证码、输入手机号、输入的图片验证码、token(申请短信返回的token)}
	"""
	registerStudent(createInfo:CreateStudentDifferentiationRequest):TokenResponse @optionalLogin
	"""注册学员账号（2024.02.20 ·新）
		@param createInfo 注册学员账号信息 {输入的短信验证码、输入手机号、输入的图片验证码、token(申请短信返回的token)}
	"""
	registerStudentV2(createInfo:CreateStudentDifferentiationRequest):TokenResponse @optionalLogin
	"""更新学员账号信息（学员端）
		@param updateInfo 更新学员账号信息
	"""
	updateStudent(updateInfo:UpdateStudentDifferentiationRequest):TokenResponse
	"""更新学员专题信息（管理端）
		@param updateInfo 更新学员专题信息
	"""
	updateStudentByAdmin(updateInfo:UpdateStudentSystemDifferentiationRequest):TokenResponse
}
"""@author: linxiquan
	@Date: 2023/11/3 17:23
	@Description: 教师行业专门字段： 用户学段、学科信息
"""
input SectionAndSubjects @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.dto.SectionAndSubjects") {
	"""学段"""
	section:Int
	"""学科"""
	subjects:Int
}
"""@Description 注册学员差异化请求
	<AUTHOR>
	@Date 2025/6/17 17:42
"""
input CreateStudentDifferentiationRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchanneluserinfo.CreateStudentDifferentiationRequest") {
	"""地区code"""
	areaCode:String!
	"""【必填】证件号码"""
	idCard:String!
	"""【必填】证件类型"""
	idCardType:Int!
	"""【必填】密码"""
	password:String!
	"""【必填】短信验证码"""
	smsCode:String
	"""【必填】用户输入的图片验证码"""
	captcha:String
	"""【必填】申请短信返回的token"""
	token:String
	"""单点登录Token"""
	loginToken:String
	"""【必填】用户名称"""
	name:String
	"""性别
		@see com.fjhb.domain.basicdata.api.user.consts.Genders
	"""
	gender:Int
	"""【必填】手机号"""
	phone:String
	"""【必填】所属区域"""
	area:String
	"""工作单位"""
	companyName:String
	"""统一社会信用码"""
	companyCode:String
	"""[必填]加密值"""
	encrypt:String
	"""行业信息"""
	userIndustryInfos:[CreateUserIndustryRequest]
	"""工作单位所在地区"""
	companyRegionCode:String
	"""【必填-前端校验】邮箱地址"""
	email:String
	"""工作单位性质"""
	unitNature:String
	"""在编情况"""
	staffingStatus:String
	"""是否在专技岗位工作"""
	isZjPosition:String
	"""职称系列"""
	titleSeries:String
	"""职称专业"""
	titleProfessional:String
	"""现有职称等级"""
	titleGrade:String
	"""现有职称资格名称"""
	titleQualificationName:String
	"""现有职称有效范围"""
	titleEffectiveRange:String
	"""最高学历"""
	highestEducationLevel:String
}
"""@Description 更新学员差异化请求
	<AUTHOR>
	@Date 2025/6/18 10:02
"""
input UpdateStudentDifferentiationRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchanneluserinfo.UpdateStudentDifferentiationRequest") {
	"""地区code"""
	areaCode:String!
	"""证件类别"""
	idCardType:Int
	"""证件号码"""
	idCard:String
	"""短信验证码"""
	smsCode:String
	"""用户输入的图片验证码"""
	captcha:String
	"""申请短信返回的token"""
	token:String
	"""【必填】默认为普通更新false，强制更新true则会携带短信验证码和token"""
	forcedUpdate:Boolean
	"""【必填】用户名称"""
	name:String
	"""性别
		@see com.fjhb.domain.basicdata.api.user.consts.Genders
	"""
	gender:Int
	"""【必填】手机号"""
	phone:String
	"""【必填】所属区域"""
	area:String
	"""工作单位"""
	companyName:String
	"""统一社会信用码"""
	companyCode:String
	"""[必填]加密值"""
	encrypt:String
	"""行业信息"""
	userIndustryInfos:[CreateUserIndustryRequest]
	"""工作单位所在地区"""
	companyRegionCode:String
	"""【必填-前端校验】邮箱地址"""
	email:String
	"""工作单位性质"""
	unitNature:String
	"""在编情况"""
	staffingStatus:String
	"""是否在专技岗位工作"""
	isZjPosition:String
	"""职称系列"""
	titleSeries:String
	"""职称专业"""
	titleProfessional:String
	"""现有职称等级"""
	titleGrade:String
	"""现有职称资格名称"""
	titleQualificationName:String
	"""现有职称有效范围"""
	titleEffectiveRange:String
	"""最高学历"""
	highestEducationLevel:String
}
"""@Description 管理端更新学员差异化请求
	<AUTHOR>
	@Date 2025/6/18 10:09
"""
input UpdateStudentSystemDifferentiationRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchanneluserinfo.UpdateStudentSystemDifferentiationRequest") {
	"""地区code"""
	areaCode:String!
	"""用户id"""
	userId:String!
	"""工作单位性质"""
	unitNature:String
	"""在编情况"""
	staffingStatus:String
	"""是否在专技岗位工作"""
	isZjPosition:String
	"""职称系列"""
	titleSeries:String
	"""职称专业"""
	titleProfessional:String
	"""现有职称等级"""
	titleGrade:String
	"""现有职称资格名称"""
	titleQualificationName:String
	"""现有职称有效范围"""
	titleEffectiveRange:String
	"""最高学历"""
	highestEducationLevel:String
}
"""@author: zhengp 2022/1/24 15:27"""
input CreateUserIndustryRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchanneluserinfo.student.CreateUserIndustryRequest") {
	"""所属行业"""
	industryId:String
	"""一级专业类别"""
	firstProfessionalCategory:String
	"""二级专业类别"""
	secondProfessionalCategory:String
	"""职称等级"""
	professionalQualification:String
	"""证书信息"""
	certificateInfos:[CertificateInfo]
	"""人员类别（职业卫生行业）"""
	personnelCategory:String
	"""岗位类别（职业卫生行业）"""
	positionCategory:String
	"""技术等级（工勤行业）"""
	professionalLevel:String
	"""工种（工勤行业）"""
	jobCategoryId:String
	"""教师行业 学段、学科信息 ，例：[{"学段1":"学科1"}, {"学段1":"学科2"}]"""
	sectionAndSubjects:[SectionAndSubjects]
	"""证书类型（药师行业）"""
	certificatesType:String
	"""执证类别（药师行业）"""
	practitionerCategory:String
}
input CertificateAttachment @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchanneluserinfo.student.CreateUserIndustryRequest$CertificateAttachment") {
	certificateUrl:String
	name:String
}
input CertificateInfo @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingchanneluserinfo.student.CreateUserIndustryRequest$CertificateInfo") {
	"""更新时必填"""
	certificateId:String
	"""证书编号"""
	certificateNo:String
	"""证书类别"""
	certificateCategory:String
	"""注册专业"""
	registerProfessional:String
	"""发证日期（起）"""
	releaseStartTime:DateTime
	"""证书有效期（止）"""
	certificateEndTime:DateTime
	"""证书附件"""
	certificateAttachments:[CertificateAttachment]
}
"""Token 响应对象
	<AUTHOR>
"""
type TokenResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchanneluserinfo.TokenResponse") {
	"""请求结果状态码"""
	code:Int!
	"""token"""
	token:String
	"""响应消息"""
	message:String
}

scalar List
