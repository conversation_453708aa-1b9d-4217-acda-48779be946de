import { UiPage } from '@hbfe/common'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage, {
  CourseV2Request,
  CourseRequest,
  CourseResponse,
  CourseResponsePage
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { uniq } from 'lodash'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import * as CourseGraphqlImporter from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage/graphql-importer'

class QueryCourseList {
  /**
   * 根据 id 集合查询课程信息
   * @param idList
   */
  async queryCoursePageByIdList(idList: Array<string>): Promise<Array<CourseResponse>> {
    const req = new RewriteGraph<CourseResponsePage, CourseV2Request>(
      MsCourseLearningQueryFrontGatewayCourseLearningBackstage._commonQuery,
      CourseGraphqlImporter.pageCourseV2InServicer
    )
    const rqList = new Array<CourseV2Request>()
    for (let index = 0; index < idList.length / 200; index++) {
      const rq = new CourseV2Request()
      rq.pageNo = 1
      rq.pageSize = 200
      rq.courseIdList = idList.slice(index * 200, index * 200 + 200)
      rqList.push(rq)
    }
    await req.request(rqList)
    const keys = [...req.indexMap.keys()]
    const lastQueryResult = new Array<CourseResponse>()
    keys.map((key, index) => {
      lastQueryResult.push(...req.indexMap.get(index).currentPageData)
    })
    return lastQueryResult
  }
}

export default QueryCourseList
