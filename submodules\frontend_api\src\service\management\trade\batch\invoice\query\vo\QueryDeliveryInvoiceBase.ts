import { Page } from '@hbfe/common'
import DeliveryInvoiceParamVo from './DeliveryInvoiceParam'
import QueryOffLinePageInvoiceParam from './QueryOffLinePageInvoiceParam'
import OffLinePageInvoiceVo from '@api/service/management/trade/batch/invoice/query/vo/OffLinePageInvoiceResponseVo'

export default abstract class QueryDeliveryInvoiceBase {
  /**
   * 分页查询发票配送
   * @param page 页数
   * @returns  Array<OffLinePageInvoiceVo>
   */
  abstract queryPageDeliveryInvoice(
    page: Page,
    deliveryInvoiceParamVo?: DeliveryInvoiceParamVo
  ): Promise<Array<OffLinePageInvoiceVo>>
  /**
   * 导出发票配送
   */
  abstract exportPageDeliveryInvoice(deliveryInvoiceParamVo: DeliveryInvoiceParamVo): Promise<boolean>

  /**
   * 集体线下发票导出 - 专票
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  abstract offLinePageVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean>
}
