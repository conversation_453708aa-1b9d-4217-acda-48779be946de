import { LodgingTypeEnum } from '@api/service/common/implement/enums/LodgingTypeEnum'
import { AppointSchemeIssueRegistrationResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { TrainingResultEnum } from '@api/service/common/implement/enums/TrainingResultEnum'
import { ResponseStatus } from '@hbfe/common'
import SdsPlatformLearningscheme, {
  ChangeAccommodationInfoRequest
} from '@api/ms-gateway/ms-learningscheme-training-qualification-v1'
import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'

export default class EducationalInfo {
  /**
   * 期别参训资格id
   */
  periodQualificationId: string = undefined

  /**
   * 学号
   */
  studentNo: string = undefined

  /**
   * 业务学号（学员展示学号）
   */
  businessStudentNo: string = undefined

  /**
   * 学员id
   */
  userId: string = undefined

  /*
   * 学员姓名
   */
  name: string = undefined

  /*
   * 证件号
   */
  idCard: string = undefined

  /*
   * 手机号
   */
  phoneNum: string = undefined

  /*
   * 工作单位
   */
  workUnit: string = undefined

  /*
   * 是否住宿
   */
  isLodging: boolean = undefined

  /*
   * 住宿方式
   */
  lodgingType: LodgingTypeEnum = undefined

  /*
   * 签到次数
   */
  signInTimes: number = undefined

  /*
   * 签退次数
   */
  signOutTimes: number = undefined

  /*
   * 结业测试结果
   */
  completionTestResults: TrainingResultEnum = undefined

  /*
   * 是否开启考勤
   */
  isOpenAttendance: boolean = undefined

  /*
   * 是否开启签到
   */
  isOpenCheckIn: boolean = undefined

  /*
   * 是否开启签退
   */
  isOpenCheckOut: boolean = undefined

  /**
   * 是否开启报道
   */
  isOpenReport: boolean = undefined

  /**
   * 是否报道
   */
  reported: boolean = undefined

  /**
   * 报道时间
   */
  reportTime: string = undefined

  /**
   * 学员报名时注册等级
   */
  trainingLevel: string = undefined

  /**
   * 获取学员在当前班级的sku
   */
  get studentCurrentProfessional() {
    // sku: 班级身上的sku
    return (sku: SkuPropertyResponseVo) => {
      // if (sku.trainingCategory.skuPropertyValueId == TrainingCategoryEnum.EJJZS) {
      //   return `${sku.trainingMajor.skuPropertyName}（${sku.mainOrAdditional.skuPropertyName}）`
      // }
      // if (sku.trainingCategory.skuPropertyValueId == TrainingCategoryEnum.EJGCS) {
      //   return `${sku.trainingMajor.skuPropertyName}`
      // }
      // if (
      //   [TrainingCategoryEnum.RSE, TrainingCategoryEnum.CAT].includes(
      //     sku.trainingCategory.skuPropertyValueId as TrainingCategoryEnum
      //   )
      // ) {
      //   if (this.trainingLevel) {
      //     return `${this.trainingLevel}${sku.trainingCategory.skuPropertyName}`
      //   } else {
      //     return `-`
      //   }
      // }
      // 默认返回培训类别
      return `${sku.trainingCategory.skuPropertyName}`
    }
  }

  /**
   * @param dto 后端dto
   */
  static from(dto: AppointSchemeIssueRegistrationResponse) {
    const vo = new EducationalInfo()

    const { user, issueTrainingQualification, learningCondition } = dto
    if (user) {
      vo.userId = user.userId
      vo.name = user.name
      vo.idCard = user.idCard
      vo.phoneNum = user.phoneNumber
      vo.workUnit = user.workUnit
    }
    if (issueTrainingQualification) {
      vo.periodQualificationId = issueTrainingQualification.qualificationId
      vo.studentNo = issueTrainingQualification.studentNo
      vo.businessStudentNo = issueTrainingQualification.businessStudentNo
      vo.isLodging = issueTrainingQualification.isAccommodation !== LodgingTypeEnum.noNeed
      vo.lodgingType = Number(issueTrainingQualification.isAccommodation)

      // if (issueTrainingQualification.trainingLevel) {
      //   vo.trainingLevel = RegisterLevel.map.get(Number(issueTrainingQualification.trainingLevel)) || ''
      // }
    }

    if (learningCondition) {
      vo.isOpenAttendance = learningCondition.isAttendanceOpen
      vo.isOpenCheckIn = learningCondition.isSignInOpen
      vo.isOpenCheckOut = learningCondition.isSignOutOpen
      vo.signInTimes = learningCondition.signInTimes
      vo.signOutTimes = learningCondition.signOutTimes
      vo.isOpenReport = learningCondition.openReport
      vo.reported = learningCondition.isReport
      vo.reportTime = learningCondition.reportTime
    }
    vo.completionTestResults = dto.examAssessResult
    return vo
  }

  /**
   * 更新住宿信息
   */
  async updateLodgingInfo() {
    const request = new ChangeAccommodationInfoRequest()
    request.trainingQualificationId = this.periodQualificationId
    if (this.isLodging) {
      request.accommodationType = this.lodgingType
    } else {
      request.accommodationType = 0
    }
    const res = await SdsPlatformLearningscheme.updateQualificationProperties(request)

    if (res?.status && res.status.isSuccess()) {
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }
}
