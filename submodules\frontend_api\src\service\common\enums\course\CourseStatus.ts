import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum CourseStatusEnum {
  DISABLE = 0,
  ENABLE = 1
}

class CourseStatus extends AbstractEnum<CourseStatusEnum> {
  static enum = CourseStatusEnum

  constructor(status?: CourseStatusEnum) {
    super()
    // 0.草稿 1.转换中 2.转换成功 3.转换失败
    this.current = status
    this.map.set(CourseStatusEnum.ENABLE, '启用')
    this.map.set(CourseStatusEnum.DISABLE, '停用')
  }
}

export default CourseStatus
