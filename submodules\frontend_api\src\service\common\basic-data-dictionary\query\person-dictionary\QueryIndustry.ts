import BasicDataGateway from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import BasicData, { BusinessDataDictionaryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import BasicDataForestage from '@api/ms-gateway/ms-basicdata-query-front-gateway-forestage'
import ServicerSeriesV1Gateway from '@api/ms-gateway/ms-servicer-series-v1'
import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
import { ResponseStatus } from '@hbfe/common'
import QueryPersonIndustryV2 from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustryV2'
import Context from '@api/service/common/context/Context'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import { PersonIndustryIdEnum } from '@api/service/training-institution/online-school/enum/PersonIndustryIdEnum'
import { IndustryPropertyIdEnum } from '@api/service/training-institution/online-school/enum/IndustryPropertyIdEnum'

/**
 * @description 查询行业类
 */
class QueryIndustry {
  /**
   * 行业列表（带有行业属性编号）
   */
  industryList: Array<IndustryVo> = []
  /**
   * 行业字典列表（回显名称用）
   */
  industryDICTList: Array<BusinessDataDictionaryResponse> = []
  industryDICTNewList: Array<BusinessDataDictionaryResponse> = []
  /**
   * 专题行业map
   */
  industryMap = new Map<string, BusinessDataDictionaryResponse>()

  /**
   * 行业列表id集合
   */
  private industryListIds = new Array<string>()

  /**
   * 查询行业列表 ----------- 数据参与业务使用
   * resIndustry 获取网校配置的行业列表（缺少名称）
   * resInfo 带有名称的行业详细信息
   * industryVo 返回给UI展示
   * @return status
   */
  async queryIndustry() {
    if (this.industryListIds.length) {
      return new ResponseStatus(200, '行业信息使用状态层缓存')
    } else {
      const resIndustry = await ServicerSeriesV1Gateway.getIndustries()
      if (resIndustry.status.isSuccess()) {
        const industryIdList = new Array<string>()
        resIndustry.data?.forEach(item => {
          industryIdList.push(item.id)
        })
        const resInfo = await QueryPersonIndustryV2.getIndustryPropertyId(
          Context.businessEnvironment.serviceToken.tokenMeta.servicerId,
          industryIdList,
          1
        )
        const resNameInfo = await BasicDataGateway.listIndustryInfoV2(industryIdList)

        this.industryListIds = industryIdList
        const industryList = new Array<IndustryVo>()
        if (resInfo.length) {
          Object.entries(IndustryIdEnum).forEach(([key, value]) => {
            if (this.industryListIds.find(item => item === value)) {
              const industryVo = new IndustryVo()
              const industry = resIndustry.data.find(item => item.id === value)
              Object.assign(industryVo, industry)
              industryVo.name = resNameInfo.data?.find(item => item.id === value)?.name
              if (key == 'RS') {
                industryVo.propertyId =
                  resInfo.find(item =>
                    [
                      PersonIndustryIdEnum.RS,
                      PersonIndustryIdEnum.AH,
                      PersonIndustryIdEnum.JSS,
                      PersonIndustryIdEnum.JX,
                      PersonIndustryIdEnum.HN,
                      PersonIndustryIdEnum.GS,
                      PersonIndustryIdEnum.SCRS
                    ].includes(item.sourceId as PersonIndustryIdEnum)
                  )?.industryPropertyId ||
                  resInfo.find(item =>
                    [
                      IndustryPropertyIdEnum.RS,
                      IndustryPropertyIdEnum.AH,
                      IndustryPropertyIdEnum.JSS,
                      IndustryPropertyIdEnum.JX,
                      IndustryPropertyIdEnum.HN,
                      IndustryPropertyIdEnum.GS,
                      IndustryPropertyIdEnum.SCRS
                    ].includes(item.sourceId as IndustryPropertyIdEnum)
                  )?.industryPropertyId
                industryList.unshift(industryVo)
              } else if (key == 'JS') {
                industryVo.propertyId =
                  resInfo.find(item =>
                    [PersonIndustryIdEnum.JS, PersonIndustryIdEnum.SC].includes(item.sourceId as PersonIndustryIdEnum)
                  )?.industryPropertyId ||
                  resInfo.find(item =>
                    [IndustryPropertyIdEnum.JS, IndustryPropertyIdEnum.SC].includes(
                      item.sourceId as IndustryPropertyIdEnum
                    )
                  )?.industryPropertyId
                industryList.unshift(industryVo)
              } else if (key == 'YS') {
                industryVo.propertyId =
                  resInfo.find(item => [PersonIndustryIdEnum.YS].includes(item.sourceId as PersonIndustryIdEnum))
                    ?.industryPropertyId ||
                  resInfo.find(item => [IndustryPropertyIdEnum.YS].includes(item.sourceId as IndustryPropertyIdEnum))
                    ?.industryPropertyId
                industryList.unshift(industryVo)
              } else {
                industryVo.propertyId =
                  resInfo.find(item => item.sourceId == PersonIndustryIdEnum[key])?.industryPropertyId ||
                  resInfo.find(item => item.sourceId == IndustryPropertyIdEnum[key])?.industryPropertyId
                industryList.push(industryVo)
              }
            }
          })
        }
        this.industryList = industryList
        // console.log(this.industryList, '555')
        return resIndustry.status
      }
    }
  }

  /**
   * 查询行业信息通过行业id列表----------- 数据参与业务使用
   * @param {Array<string>} industryIdList 行业id列表
   * @return
   */
  async queryIndustryByIdList(industryIdList: Array<string>) {
    const response = await BasicDataGateway.listIndustryInfoV2(industryIdList)
    if (response.status.isSuccess()) {
      return response.data
    }
    return new Array<IndustryVo>()
  }

  /**
   * 【本地】获取行业信息根据行业id----------- 数据参与业务使用
   * @param {Array<string>} industryIdList 行业id列表
   * @return
   */
  async getIndustryByIdList(industryIdList: Array<string>) {
    const industryList = new Array<IndustryVo>()
    if (!this.industryList?.length) {
      // 存在行业列表为空时 先加载一遍
      await this.queryIndustry()
    }
    industryIdList?.forEach(id => {
      this.industryList?.forEach(industry => {
        if (id === industry.id || id === industry.propertyId) {
          industryList.push(industry)
        }
      })
    })
    return industryList.sort((a, b) => {
      return a.sort - b.sort
    })
  }

  /**
   * 行业字典 ---- 只提供ID映射名称使用(后台用)
   */
  async getIndustryDICT() {
    if (this.industryDICTList?.length) return this.industryDICTList
    const response = await BasicData.listBusinessDataDictionaryInSubProject({ businessDataDictionaryType: 'INDUSTRY' })
    this.industryDICTList = response?.data || []
    return response.data
  }
  async getIndustryDICTZT() {
    if (this.industryDICTNewList?.length) return this.industryDICTNewList
    const response = await BasicData.listBusinessDataDictionaryInSubProject({
      businessDataDictionaryType: 'TRAINING_CHANNEL_INDUSTRY'
    })
    this.industryDICTNewList = response?.data || []
    return response.data
  }
  /**
   * 行业字典 ---- 只提供ID映射名称使用(前台用)
   */
  async getIndustryForestage() {
    if (this.industryDICTList?.length) return this.industryDICTList
    const response = await BasicDataForestage.listBusinessDataDictionaryInSubProject({
      businessDataDictionaryType: 'INDUSTRY'
    })
    this.industryDICTList = response?.data || []
    return response.data
  }
  async getIndustryDICTZTForestage(idList: Array<string>) {
    const cacheIds = [...this.industryMap.keys()]
    const noCacheIds = idList.filter(id => !cacheIds.includes(id))
    if (noCacheIds.length) {
      const response = await BasicDataForestage.listBusinessDataDictionaryByIdInSubProject(idList)
      response.data?.forEach(item => {
        this.industryMap.set(item.id, item)
      })
    }
  }
}

export default new QueryIndustry()
