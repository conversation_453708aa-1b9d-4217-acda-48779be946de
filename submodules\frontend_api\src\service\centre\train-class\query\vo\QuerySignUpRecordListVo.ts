import DateRangeVo from '@api/service/centre/train-class/query/vo/DateRangeVo'
import { ExamResultStatusEnum } from '@api/service/centre/train-class/enum/ExamResultStatus'
import { LearningScheduleStatusEnum } from '@api/service/centre/train-class/enum/LearningScheduleStatus'
import {
  DateScopeRequest,
  LearningRegisterRequest,
  RegionSkuPropertyRequest,
  RegionSkuPropertySearchRequest,
  SchemeRequest,
  SchemeSkuPropertyRequest,
  StudentLearningRequest,
  StudentSchemeLearningRequest,
  UserRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import TrainClassUtils from '@api/service/centre/train-class/util/TrainClassUtils'
import DataResolve from '@api/service/common/utils/DataResolve'

/**
 * @description 报名记录列表查询条件
 */
class QuerySignUpRecordListVo {
  /**
   * 学员姓名
   */
  studentName = ''

  /**
   * 学员身份证号
   */
  studentAccount = ''

  /**
   * 年度
   */
  year = ''

  /**
   * 行业
   */
  industry = ''

  /**
   * 地区
   */
  region: string[] = null

  /**
   * 科目类型
   */
  subjectType = ''

  /**
   * 培训类别
   */
  trainingCategory = ''

  /**
   * 培训专业
   */
  trainingMajor = ''

  /**
   * 卫生行业-培训对象
   */
  trainingObject = ''
  /**
   * 卫生行业-岗位类别
   */
  positionCategory = ''
  /**
   * 工勤行业-技术等级
   */
  jobLevel = ''
  /**
   * 工勤行业-工种
   */
  jobCategory = ''

  /**
   * 培训班名称
   */
  schemeName = ''

  /**
   * 学习进度 1：未学习 2：学习中 3：已完成 4：无需学习
   */
  learningSchedule: LearningScheduleStatusEnum = null

  /**
   * 考试结果 1：已合格 2：未合格 3：无需考试
   */
  examResult: ExamResultStatusEnum = null

  /**
   * 是否完成培训
   */
  hasCompleteTraining: boolean = null

  /**
   * 报名日期
   */
  signUpDate: DateRangeVo = new DateRangeVo()

  /**
   * 合格日期
   */
  qualifiedDate: DateRangeVo = new DateRangeVo()
  /**
   * 技术等级
   */
  technicalGrade = ''

  /**
   * 是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1
   */
  saleChannels?: Array<number> = new Array<number>()
  /**
   * 专题名称
   */
  trainingChannelName?: string = ''
  /**
   * 专题Id  用于不同专题域名 查询对应专题的培训班
   */
  trainingChannelId?: string = ''
  async to(): Promise<StudentSchemeLearningRequest> {
    const to = new StudentSchemeLearningRequest()
    if (this.studentName || this.studentAccount) {
      to.student = new UserRequest()
      to.student.userIdList = await TrainClassUtils.getUserIdList(this.studentName, this.studentAccount)
    }
    to.scheme = new SchemeRequest()
    to.scheme.skuProperty = new SchemeSkuPropertyRequest()
    to.scheme.skuProperty.year = DataResolve.validIsNotEmpty(this.year) ? [this.year] : undefined
    to.scheme.skuProperty.industry = DataResolve.validIsNotEmpty(this.industry) ? [this.industry] : undefined
    to.scheme.skuProperty.subjectType = DataResolve.validIsNotEmpty(this.subjectType) ? [this.subjectType] : undefined
    to.scheme.skuProperty.trainingObject = DataResolve.validIsNotEmpty(this.trainingObject)
      ? [this.trainingObject]
      : undefined
    to.scheme.skuProperty.positionCategory = DataResolve.validIsNotEmpty(this.positionCategory)
      ? [this.positionCategory]
      : undefined
    to.scheme.skuProperty.jobLevel = DataResolve.validIsNotEmpty(this.jobLevel) ? [this.jobLevel] : undefined
    to.scheme.skuProperty.jobCategory = DataResolve.validIsNotEmpty(this.jobCategory) ? [this.jobCategory] : undefined
    to.scheme.skuProperty.trainingCategory = DataResolve.validIsNotEmpty(this.trainingCategory)
      ? [this.trainingCategory]
      : undefined
    to.scheme.skuProperty.technicalGrade = DataResolve.validIsNotEmpty(this.technicalGrade)
      ? [this.technicalGrade]
      : undefined
    to.scheme.skuProperty.trainingProfessional = DataResolve.validIsNotEmpty(this.trainingMajor)
      ? [this.trainingMajor]
      : undefined
    // 地区查询
    if (DataResolve.isWeightyArr(this.region)) {
      to.scheme.skuProperty.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      to.scheme.skuProperty.regionSkuPropertySearch.regionSearchType = 1
      to.scheme.skuProperty.regionSkuPropertySearch.region = [] as RegionSkuPropertyRequest[]
      const option = new RegionSkuPropertyRequest()
      option.province = this.region.length >= 1 ? this.region[0] : undefined
      option.city = this.region.length >= 2 ? this.region[1] : undefined
      option.county = this.region.length >= 3 ? this.region[2] : undefined
      to.scheme.skuProperty.regionSkuPropertySearch.region.push(option)
    } else {
      to.scheme.skuProperty.regionSkuPropertySearch = undefined
    }
    to.scheme.schemeName = DataResolve.validIsNotEmpty(this.schemeName) ? this.schemeName : undefined
    to.studentLearning = new StudentLearningRequest()
    this.convertCourseLearningAndExamLearning(to)
    // 是否完成培训 && 合格时间联合查询
    if (this.qualifiedDate.begin || this.qualifiedDate.end) {
      // 合格时间 && (完成培训 || 未完成培训 || null)
      to.studentLearning.trainingResultList = [1]
      to.studentLearning.trainingResultTime = new DateScopeRequest()
      to.studentLearning.trainingResultTime.begin = DataResolve.validIsNotEmpty(this.qualifiedDate.begin)
        ? this.qualifiedDate.begin
        : undefined
      to.studentLearning.trainingResultTime.end = DataResolve.validIsNotEmpty(this.qualifiedDate.end)
        ? this.qualifiedDate.end
        : undefined
    } else {
      to.studentLearning.trainingResultTime = undefined
      if (this.hasCompleteTraining) {
        to.studentLearning.trainingResultList = [1]
      } else if (this.hasCompleteTraining === false) {
        to.studentLearning.trainingResultList = [-1, 0]
      } else {
        to.studentLearning.trainingResultList = undefined
      }
    }
    if (this.signUpDate.begin || this.signUpDate.end) {
      to.learningRegister = new LearningRegisterRequest()
      to.learningRegister.registerTime = new DateScopeRequest()
      to.learningRegister.registerTime.begin = DataResolve.validIsNotEmpty(this.signUpDate.begin)
        ? this.signUpDate.begin
        : undefined
      to.learningRegister.registerTime.end = DataResolve.validIsNotEmpty(this.signUpDate.end)
        ? this.signUpDate.end
        : undefined
    } else {
      to.learningRegister = new LearningRegisterRequest()
    }
    to.learningRegister.status = [1, 2]
    to.saleChannels = this.saleChannels
    to.trainingChannelName = this.trainingChannelName
    to.trainingChannelId = this.trainingChannelId
    return to
  }

  /**
   * 转换课程、考试学习参数
   */
  convertCourseLearningAndExamLearning(to: StudentSchemeLearningRequest) {
    if (this.learningSchedule || this.examResult) {
      if (this.learningSchedule && !this.examResult) {
        // 学习进度-未学习
        if (this.learningSchedule === LearningScheduleStatusEnum.Unlearned) {
          to.studentLearning.notLearningTypeList = undefined
          to.studentLearning.examAssessResultList = undefined
          to.studentLearning.courseScheduleStatus = 0
        }
        // 学习进度-学习中
        if (this.learningSchedule === LearningScheduleStatusEnum.Learning) {
          to.studentLearning.notLearningTypeList = undefined
          to.studentLearning.examAssessResultList = undefined
          to.studentLearning.courseScheduleStatus = 1
        }
        // 学习进度-已完成
        if (this.learningSchedule === LearningScheduleStatusEnum.Complete_Learning) {
          to.studentLearning.notLearningTypeList = undefined
          to.studentLearning.examAssessResultList = undefined
          to.studentLearning.courseScheduleStatus = 2
        }
        // 学习进度-无需学习
        if (this.learningSchedule === LearningScheduleStatusEnum.Innate) {
          to.studentLearning.notLearningTypeList = [1, 4]
          to.studentLearning.examAssessResultList = undefined
          to.studentLearning.courseScheduleStatus = undefined
        }
      } else if (!this.learningSchedule && this.examResult) {
        // 考试结果-已合格
        if (this.examResult === ExamResultStatusEnum.Qualified) {
          to.studentLearning.notLearningTypeList = undefined
          to.studentLearning.examAssessResultList = [1]
          to.studentLearning.courseScheduleStatus = undefined
        }
        // 考试结果-未合格
        if (this.examResult === ExamResultStatusEnum.Unqualified) {
          to.studentLearning.notLearningTypeList = undefined
          to.studentLearning.examAssessResultList = [-1, 0]
          to.studentLearning.courseScheduleStatus = undefined
        }
        // 考试结果-无需考试
        if (this.examResult === ExamResultStatusEnum.Innate) {
          to.studentLearning.notLearningTypeList = [2]
          to.studentLearning.examAssessResultList = undefined
          to.studentLearning.courseScheduleStatus = undefined
        }
      } else {
        /** 既有学习进度又有考试结果 */
        // 学习-未学习 && 考试-已合格
        if (
          this.learningSchedule === LearningScheduleStatusEnum.Unlearned &&
          this.examResult === ExamResultStatusEnum.Qualified
        ) {
          to.studentLearning.notLearningTypeList = undefined
          to.studentLearning.examAssessResultList = [1]
          to.studentLearning.courseScheduleStatus = 0
        }
        // 学习-未学习 && 考试-未合格
        if (
          this.learningSchedule === LearningScheduleStatusEnum.Unlearned &&
          this.examResult === ExamResultStatusEnum.Unqualified
        ) {
          to.studentLearning.notLearningTypeList = undefined
          to.studentLearning.examAssessResultList = [-1, 0]
          to.studentLearning.courseScheduleStatus = 0
        }
        // 学习-未学习 && 考试-无需考试
        if (
          this.learningSchedule === LearningScheduleStatusEnum.Unlearned &&
          this.examResult === ExamResultStatusEnum.Innate
        ) {
          to.studentLearning.notLearningTypeList = [2]
          to.studentLearning.examAssessResultList = undefined
          to.studentLearning.courseScheduleStatus = 0
        }
        // 学习-学习中 && 考试-已合格
        if (
          this.learningSchedule === LearningScheduleStatusEnum.Learning &&
          this.examResult === ExamResultStatusEnum.Qualified
        ) {
          to.studentLearning.notLearningTypeList = undefined
          to.studentLearning.examAssessResultList = [1]
          to.studentLearning.courseScheduleStatus = 1
        }
        // 学习-学习中 && 考试-未合格
        if (
          this.learningSchedule === LearningScheduleStatusEnum.Learning &&
          this.examResult === ExamResultStatusEnum.Unqualified
        ) {
          to.studentLearning.notLearningTypeList = undefined
          to.studentLearning.examAssessResultList = [-1, 0]
          to.studentLearning.courseScheduleStatus = 1
        }
        // 学习-学习中 && 考试-无需考试
        if (
          this.learningSchedule === LearningScheduleStatusEnum.Learning &&
          this.examResult === ExamResultStatusEnum.Innate
        ) {
          to.studentLearning.notLearningTypeList = [2]
          to.studentLearning.examAssessResultList = undefined
          to.studentLearning.courseScheduleStatus = 1
        }
        // 学习-已完成 && 考试-已合格
        if (
          this.learningSchedule === LearningScheduleStatusEnum.Complete_Learning &&
          this.examResult === ExamResultStatusEnum.Qualified
        ) {
          to.studentLearning.notLearningTypeList = undefined
          to.studentLearning.examAssessResultList = [1]
          to.studentLearning.courseScheduleStatus = 2
        }
        // 学习-已完成 && 考试-未合格
        if (
          this.learningSchedule === LearningScheduleStatusEnum.Complete_Learning &&
          this.examResult === ExamResultStatusEnum.Unqualified
        ) {
          to.studentLearning.notLearningTypeList = undefined
          to.studentLearning.examAssessResultList = [-1, 0]
          to.studentLearning.courseScheduleStatus = 2
        }
        // 学习-已完成 && 考试-无需考试
        if (
          this.learningSchedule === LearningScheduleStatusEnum.Complete_Learning &&
          this.examResult === ExamResultStatusEnum.Innate
        ) {
          to.studentLearning.notLearningTypeList = [2]
          to.studentLearning.examAssessResultList = undefined
          to.studentLearning.courseScheduleStatus = 2
        }
        // 学习-无需学习 && 考试-已合格
        if (
          this.learningSchedule === LearningScheduleStatusEnum.Innate &&
          this.examResult === ExamResultStatusEnum.Qualified
        ) {
          to.studentLearning.notLearningTypeList = [1, 4]
          to.studentLearning.examAssessResultList = [1]
          to.studentLearning.courseScheduleStatus = undefined
        }
        // 学习-无需学习 && 考试-未合格
        if (
          this.learningSchedule === LearningScheduleStatusEnum.Innate &&
          this.examResult === ExamResultStatusEnum.Unqualified
        ) {
          to.studentLearning.notLearningTypeList = [1, 4]
          to.studentLearning.examAssessResultList = [-1, 0]
          to.studentLearning.courseScheduleStatus = undefined
        }
        // 学习-无需学习 && 考试-无需考试
        if (
          this.learningSchedule === LearningScheduleStatusEnum.Innate &&
          this.examResult === ExamResultStatusEnum.Innate
        ) {
          to.studentLearning.notLearningTypeList = [1, 2, 4]
          to.studentLearning.examAssessResultList = undefined
          to.studentLearning.courseScheduleStatus = undefined
        }
      }
    } else {
      to.studentLearning.notLearningTypeList = undefined
      to.studentLearning.examAssessResultList = undefined
      to.studentLearning.courseScheduleStatus = undefined
    }
  }
}

export default QuerySignUpRecordListVo
