import CourseStatus from '@api/service/management/resource/course/enums/CourseStatus'
import MutationCourse from '@api/service/management/resource/course/mutation/MutationCourse'
import { CourseResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import CourseCategory from '@api/service/management/resource/course/query/vo/CourseCategory'
import CourseTransformStatus, { TransformStatusEnum } from '@api/service/common/enums/course/CourseTransformStatus'
import { CoursewareSupplierResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

/**
 * 课程列表详情对象
 */
class CourseListDetail {
  id: string
  name: string
  status: CourseStatus
  transformStatus: CourseTransformStatus
  enable: boolean
  categories: Array<CourseCategory>

  get enableLabel() {
    return this.enable ? '启用' : '禁用'
  }

  // 学时
  period = 0

  // 创建时间
  createTime = ''

  // 综合评价
  evaluation = 0

  // 课程数量
  questionCount = 0

  // * 课件供应商
  courseProviderName = ''
  // * 课件供应商Id
  courseProviderId = ''

  /**
   * 操作时间
   */
  updateTime = ''

  /**
   * 排序
   */
  sort = 0
  /**
   * 分类名称
   */
  categoriesName? = ''

  /**
   * 教师id
   */
  teacherIds: string[] = []

  /**
   * 教师名称
   */
  teacherNames: string[] = []

  /**
   * 封面图片
   */
  iconPath = ''
  static from(coursePageResponse: CourseResponse) {
    const courseListDetail = new CourseListDetail()
    courseListDetail.id = coursePageResponse.id
    courseListDetail.name = coursePageResponse.name
    courseListDetail.courseProviderId = coursePageResponse.supplierId
    const enumValue = coursePageResponse.enable ? CourseStatus.enum.enable : CourseStatus.enum.disable
    courseListDetail.status = new CourseStatus(enumValue)
    if (coursePageResponse.status.toString() == TransformStatusEnum.EMPTY) {
      courseListDetail.transformStatus = new CourseTransformStatus(TransformStatusEnum.UNAVAILABLE)
    } else {
      courseListDetail.transformStatus = new CourseTransformStatus(
        `${coursePageResponse.status}` as TransformStatusEnum
      )
    }
    courseListDetail.period = coursePageResponse.period
    courseListDetail.enable = coursePageResponse.enable === 1
    courseListDetail.createTime = coursePageResponse.createTime
    courseListDetail.teacherIds = coursePageResponse.teacherIds
    courseListDetail.iconPath = coursePageResponse.iconPath
    if (coursePageResponse.categorys) {
      courseListDetail.categories = coursePageResponse.categorys.map(CourseCategory.from)
    }
    courseListDetail.evaluation =
      (coursePageResponse.courseAppraiseInfo?.totalAppraise &&
        parseFloat((coursePageResponse.courseAppraiseInfo?.totalAppraise / 100).toFixed(1))) ||
      0
    return courseListDetail
  }

  static addProvider(vo: CourseListDetail, dto: CoursewareSupplierResponse[]) {
    vo.courseProviderName =
      dto.find(res => res?.servicerBase?.servicerId === vo?.courseProviderId)?.servicerBase?.servicerName || ''
    return vo
  }

  get mutationCourse() {
    return new MutationCourse(this.id)
  }
}

export default CourseListDetail
