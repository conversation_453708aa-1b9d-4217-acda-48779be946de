import ImportUserAsyncTaskVo from '@api/service/management/async-task/mutation/vo/ImportUserAsyncTaskVo'
import { Response } from '@hbfe/common'
import PlatformAccount from '@api/platform-gateway/platform-account-v1'

/**
 * @description 导入学员
 */
class MutationImportUser {
  importParams: ImportUserAsyncTaskVo = new ImportUserAsyncTaskVo()

  /**
   * 导入学员
   */
  async importStudent(): Promise<Response<void>> {
    const result = new Response<void>()
    this.importParams.filePath = `/mfs${this.importParams.filePath}`
    const response = await PlatformAccount.batchImportStudent(this.importParams)
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
}

export default MutationImportUser
