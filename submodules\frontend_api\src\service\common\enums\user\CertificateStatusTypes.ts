import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 证书状态
 */
enum CertificateStatusEnum {
  COMMON = 1,
  LOGOUT = 2
}

export { CertificateStatusEnum }
class CertificateStatusTypes extends AbstractEnum<CertificateStatusEnum> {
  static enum = CertificateStatusEnum

  constructor(status?: CertificateStatusEnum) {
    super()
    this.current = status
    this.map.set(CertificateStatusEnum.COMMON, '正常')
    this.map.set(CertificateStatusEnum.LOGOUT, '注销')
  }
}

export default new CertificateStatusTypes()
