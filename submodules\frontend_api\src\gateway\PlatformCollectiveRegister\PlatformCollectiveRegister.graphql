schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	findBatchOrderPage(page:Page,query:CollectiveregisterQueryDto):CollectiveregisterOrderDtoPage @page(for:"CollectiveregisterOrderDto")
	"""查询批次单的任务状态信息
		@return
	"""
	findLastRecordByBatch(batchNo:String):CollectiveTaskInfo
	findOrderListByBatchNo(batchNo:String,page:Page):OrderInBatchInfoPage @page(for:"OrderInBatchInfo")
	"""集体报名订单详情
		@return
	"""
	getBatchOrder(batchNo:String):CollectiveregisterOrderDto
	"""获取批次订单号集合获取批次订单的发票信息"""
	getBillByOrderNo(batchNos:[String]):[BillOrderInfoDTO]
	"""根据批次订单号查询批次订单状态
		@param batchNo
		@return
	"""
	getCollectiveOrderStatus(batchNo:String):String
	getCollectiveRegisterRecord(page:Page,batchNo:String,query:CollectiveRegisterRecordQuery):CollectiveRegisterRecordPage @page(for:"CollectiveRegisterRecord")
	"""根据批次号集合获取批次下报名人数信息
		@param batchNos
		@return
	"""
	getSignUpCountByBatchNos(batchNos:[String]):[CollectiveregisterSignUpCountDto]
	"""预提交批次订单
		@param batchNo 批次单号
		@return 不能报名的记录
	"""
	preCommitCollectiveRegister(batchNo:String):[CollectiveRegisterRecord]
}
type Mutation {
	cancelBatchOrder(cancelInfo:BatchOrderCancelDTO):BatchOrderDTO
	"""提交报名
		@return
	"""
	commitCollectiveRegister(batchNo:String):Void
	"""导入集体报名
		@return
	"""
	createCollectiveRegister(createInfo:CollectiveRegisterCreateInfo):String
	"""删除报名订单
		@return
	"""
	doDelCollectiveRegister(batchNo:String):Void
	"""移除批次订单中的部分订单
		@param batchNo
		@param recordIds
	"""
	removeCollectiveRegister(batchNo:String,recordIds:[String]):Void
	"""存在批次订单的情况下，再次
		导入集体报名
		@return
	"""
	updateCollectiveRegister(createInfo:CollectiveRegisterCreateInfo,batchNo:String):String
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""集体缴费报名订单查询参数
	<AUTHOR> create 2020/8/11 17:42
"""
input CollectiveregisterQueryDto @type(value:"com.fjhb.fjszyws.integrative.service.collectiveregister.dto.CollectiveregisterQueryDto") {
	"""批次号"""
	batchNo:String
	"""交易状态
		@see BatchOrderState
	"""
	orderStatus:[String]
	"""是否需要发票"""
	needInvoice:Int!
	"""1 线上 2 线下"""
	firstType:Int!
	"""提交批次单时间段- 开始"""
	commitStartTime:DateTime
	"""提交批次单时间段- 结束"""
	commitEndTime:DateTime
	"""创建批次单时间段- 开始"""
	createStartTime:DateTime
	"""创建批次单时间段- 结束"""
	createEndTime:DateTime
	"""单位id"""
	unitId:String
	"""是否排除 支付中并且是线下收款的订单
		true 排除
		false 不排除
	"""
	exsitPayingOffline:Boolean
}
"""集体报名创建信息"""
input CollectiveRegisterCreateInfo @type(value:"com.fjhb.fjszyws.platform.service.collectiveregister.args.CollectiveRegisterCreateInfo") {
	"""文件路径"""
	filePath:String
	"""文件名称"""
	fileName:String
}
"""集体报名记录查询条件"""
input CollectiveRegisterRecordQuery @type(value:"com.fjhb.fjszyws.platform.service.collectiveregister.args.CollectiveRegisterRecordQuery") {
	"""是否允许报名"""
	isAllowRegister:Boolean
	"""学员姓名"""
	userName:String
	"""身份证号码"""
	identity:String
	"""手机号码"""
	phone:String
	"""培训班"""
	schemeId:String
}
input BatchOrderCancelDTO @type(value:"com.fjhb.platform.core.v1.order.api.dto.BatchOrderCancelDTO") {
	batchNo:String!
	reasonId:String!
	reasonDescription:String!
}
"""集体缴费任务的详细信息
	<AUTHOR> create 2020/8/18 14:21
"""
type CollectiveTaskInfo @type(value:"com.fjhb.fjszyws.integrative.service.collectiveregister.dto.CollectiveTaskInfo") {
	"""任务对应的批次号"""
	batchNo:String
	"""最新任务的创建时间"""
	creatTime:DateTime
	"""最新任务的id"""
	taskId:String
	"""最新任务的导入的状态
		初始状态 1:批次任务创建完成
		@see CollectiveOrderImportStatus
	"""
	taskImportStatus:Int!
	"""最新任务完成时间
		没完成是null
	"""
	completeTime:DateTime
	"""批次单下 所有任务的成功条数"""
	totalSuccessCount:Int!
	"""批次单下 所有任务的失败条数"""
	totalFaildCount:Int!
	"""批次单下 所有任务的总条数"""
	totalCount:Int!
	"""最新任务下的总条数"""
	currentTotalCount:Int!
	"""最新任务当前执行到的条数"""
	currentCount:Int!
	"""最新任务执行失败的条数"""
	failCount:Int!
	"""最新任务执行成功的条数
		只要第一步导入成功就算成功，不需要真正下订单
	"""
	successCount:Int!
}
"""集体缴费订单返回值
	<AUTHOR> create 2020/8/11 17:49
"""
type CollectiveregisterOrderDto @type(value:"com.fjhb.fjszyws.integrative.service.collectiveregister.dto.CollectiveregisterOrderDto") {
	"""批次订单号"""
	no:String
	"""批次单总金额(该字段会在批次单提交后计算总金额)"""
	totalMoney:Double!
	"""批次单报名总人数"""
	signUpCount:Int!
	"""批次单状态"""
	state:BatchOrderState
	"""支付方式1:线上,2:线下"""
	payType:Int!
	"""创建方式
		1:系统创建
		2:用户创建
		3:管理员创建
		4:历史迁移
		5:外部接口
	"""
	createType:Int!
	"""创建人id"""
	creatorId:String
	"""创建时间"""
	createTime:DateTime
	"""开始追加订单时间"""
	startAddOrderTime:DateTime
	"""提交时间"""
	commitTime:DateTime
	"""去支付时间"""
	preparePayTime:DateTime
	"""支付完成时间"""
	payFinishTime:DateTime
	"""发货完成时间"""
	deliverySuccessTime:DateTime
	"""交易完成时间,如果最终是交易成功就是交易成功时间,如果最终是交易关闭就是交易关闭时间"""
	tradeFinishTime:DateTime
	"""最后一次更新时间"""
	lastUpdateTime:DateTime
	"""收款账户ID"""
	receiveAccountId:String
	"""备注"""
	remark:String
	"""自动关闭时间"""
	autoCloseTime:DateTime
	"""是否为测试数据"""
	test:Boolean!
	"""是否需要发票"""
	needInvoice:Boolean!
	"""汇款凭证地址"""
	paths:[String]
}
"""集体缴费报名人次统计接口
	<AUTHOR> create 2020/8/12 14:38
"""
type CollectiveregisterSignUpCountDto @type(value:"com.fjhb.fjszyws.integrative.service.collectiveregister.dto.CollectiveregisterSignUpCountDto") {
	"""报名人次"""
	signUpCount:Int!
	"""批次单号"""
	batchNo:String
}
"""集体缴费内的订单信息
	<AUTHOR> create 2020/8/12 20:47
"""
type OrderInBatchInfo @type(value:"com.fjhb.fjszyws.integrative.service.collectiveregister.dto.OrderInBatchInfo") {
	"""批次号"""
	batchNo:String
	"""订单号"""
	orderNo:String
	"""买家ID"""
	buyerId:String
	"""订单总金额"""
	totalAmount:Double!
	"""订单创建时间"""
	createTime:DateTime
	"""订单交易完成时间"""
	completeTime:DateTime
	"""主订单状态"""
	orderStatus:Int!
	"""子订单列表"""
	subOrderList:[SubOrderItemDTO]
}
"""订单的发票信息"""
type BillOrderInfoDTO @type(value:"com.fjhb.fjszyws.integrative.service.trade.dto.BillOrderInfoDTO") {
	"""主键 主订单号
		非批次订单才有值
	"""
	orderNo:String
	"""买家ID
		非批次订单才有值
	"""
	buyerId:String
	"""发票号"""
	billNo:String
	"""主键 发票ID"""
	id:String
	"""发票抬头"""
	title:String
	"""发票抬头类型 1：个人 2：企业"""
	titleType:String
	"""统一社会信用代码"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账号"""
	account:String
	"""发票类型：1普通发票，2增值税普通发票，3增值税专用发票"""
	invoiceType:String
	"""是否测试数据"""
	test:Boolean!
	"""发票状态 com.fjhb.platform.core.v1.bill.api.constants.InvoiceBillState
		未开票： 0
		开票中： 1
		开票成功： 2
		开票失败： 3
	"""
	state:String
	"""红票状态
		未开票： 0
		开票中： 1
		开票成功： 2
		开票失败： 3
	"""
	redState:String
	"""电子邮件"""
	email:String
	"""发票代码"""
	billCode:String
	"""发票验证码"""
	billVeriCode:String
	"""用于接收通知的电话"""
	noticePhone:String
	"""是否冻结"""
	frozen:Boolean!
	"""开票金额"""
	money:BigDecimal
	"""发票对应的批次号
		批次订单才有值
	"""
	batchNo:String
	"""创建时间"""
	createTime:DateTime
}
type SubOrderItemDTO @type(value:"com.fjhb.fjszyws.integrative.service.trade.dto.response.SubOrderItemDTO") {
	"""子订单号"""
	subOrderNo:String
	"""商品ID"""
	commodityId:String
	"""商品名称"""
	commodityName:String
	"""商品图片地址"""
	photoPath:String
	"""商品规格"""
	specification:String
	"""是否为虚拟物品"""
	virtualGoods:Boolean!
	"""商品标价"""
	labelPrice:BigDecimal
	"""成交单价"""
	dealPrice:BigDecimal
	"""购买数量"""
	purchaseQuantity:Int!
	"""子订单实付总价"""
	totalAmount:BigDecimal
	"""子订单状态
		待付款 1
		未发货 2
		发货中 3
		已发货 4
		买家已签收 5
		已换货 6
		退货中 7
		已退货 8
		已取消 9
		发货失败 10
		已退款(指退货并退款) 11
		退货时商品回收失败 12
		退货中 13
		换货单退货中 14
		换货单退货中 15
		换货单退货中 16
		退货单退款失败 17
		退货不退款成功 18
		退款不退货成功 19
	"""
	orderStatus:Int!
	"""是否需要发票"""
	needBill:Boolean!
}
"""集体报名记录"""
type CollectiveRegisterRecord @type(value:"com.fjhb.fjszyws.platform.service.collectiveregister.args.CollectiveRegisterRecord") {
	"""集体报名记录ID"""
	id:String
	"""集体报名单号"""
	batchNo:String
	"""用户名称"""
	userName:String
	"""用户手机"""
	phone:String
	"""用户身份证"""
	identity:String
	"""人员类别"""
	userCategory:String
	"""开通的学习方案名称"""
	schemeName:String
	"""开通的期数名称"""
	issueName:String
	"""是否进行报名"""
	register:Boolean!
	"""用户ID"""
	userId:String
	"""销售渠道ID"""
	marketingChannelId:String
	"""订单号"""
	orderNo:String
	"""商品id"""
	commoditySkuId:String
	"""班级id"""
	schemeId:String
	"""错误信息"""
	errorMessage:String
}
type BatchOrderDTO @type(value:"com.fjhb.platform.core.v1.order.api.dto.BatchOrderDTO") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	organizationId:String
	no:String
	status:Int!
	totalMoney:BigDecimal
	needInvoice:Boolean!
	invoiceInfo:InvoiceRequestDTO
	receiveAccountId:String
	commitTime:DateTime
	tradeFinishTime:DateTime
	createType:Int!
	payType:Int!
	creatorId:String
	createTime:DateTime
}
type InvoiceRequestDTO @type(value:"com.fjhb.platform.core.v1.order.api.dto.InvoiceRequestDTO") {
	title:String
	titleType:Int!
	type:Int!
	taxpayerNo:String
	address:String
	phone:String
	bankName:String
	account:String
	email:String
	remark:String
	objectList:[InvoiceObjDTO]
	electron:Boolean!
	noTaxBill:Boolean!
}
type InvoiceObjDTO @type(value:"com.fjhb.platform.core.v1.order.api.dto.InvoiceRequestDTO$InvoiceObjDTO") {
	objectType:String
	objectId:String
}
enum BatchOrderState @type(value:"com.fjhb6.ability.order.v1_2_0.enums.BatchOrderState") {
	beginning
	addingOrder
	commited
	paying
	tradeClosing
	payFail
	paySuccess
	delivering
	deliverySuccess
	tradeSuccess
	tradeClose
}

scalar List
type CollectiveregisterOrderDtoPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CollectiveregisterOrderDto]}
type OrderInBatchInfoPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [OrderInBatchInfo]}
type CollectiveRegisterRecordPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CollectiveRegisterRecord]}
