import { PlaceBatchOrderResultEnum } from '@api/service/management/trade/batch/order/enum/PlaceBatchOrderResult'
import { BatchOrderPayModeEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderPayMode'
/**
 * @description 【集体报名订单列表】下单结果
 */
class BatchOrderListPlaceOrderResultVo {
  /**
   * 缴费批次号
   */
  batchOrderNo = ''

  /**
   * 缴费人次
   */
  payPersonTime: number = null

  /**
   * 实付金额
   */
  amount: number = null

  /**
   * 支付方式
   */
  payMode: BatchOrderPayModeEnum = null

  /**
   * 销售渠道
   */
  payTerminal = ''

  /**
   * 下单结果
   */
  placeOrderResult: PlaceBatchOrderResultEnum = null

  /**
   * 下单时间
   */
  placeOrderTime = ''

  /**
   * 付款成功时间
   */
  paySuccessTime = ''

  /**
   * 当前批次缴费人次
   */
  currentBatchPayPersonTime: number = null

  /**
   * 处理成功人次
   */
  successPersonTime: number = null

  /**
   * 处理失败人次
   */
  failPersonTime: number = null
}

export default BatchOrderListPlaceOrderResultVo
