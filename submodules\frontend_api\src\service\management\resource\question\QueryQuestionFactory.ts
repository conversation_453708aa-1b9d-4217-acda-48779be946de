import QueryQuestionList from './query/QueryQuestionList'
import QueryQuestionDetail from './query/QueryQuestionDetail'
import { QuestionTypeEnum } from '@api/service/common/enums/question/QuestionType'
import QueryQuestionAsynTask from './query/QueryQuestionAsynTask'
class QueryQuestionFactory {
  /**
   * @description: 获取试题列表查询
   * @param {*}
   * @return {*}
   */
  get queryQuestionList() {
    return new QueryQuestionList()
  }

  /**
   * @description: 获取试题详情
   * @param {string} id 试题id
   * @param {QuestionTypeEnum} 试题类型
   */
  getQueryQuestionDetail(id: string, questionType: QuestionTypeEnum) {
    return new QueryQuestionDetail(id, questionType)
  }

  /* 
    导入开通结果跟踪---导入任务列表实例
  */
  get queryQuestionAsynTask() {
    return new QueryQuestionAsynTask()
  }
}

export default new QueryQuestionFactory()
