import SchemeSkuPropertyRequest from '@api/service/customer/scheme/models/SchemeSkuPropertyRequest'
import { Page } from '@hbfe/common'
import SchemeDetail from '@api/service/customer/scheme/models/SchemeDetail'
import MsTradeQueryFrontGatewayTradeQueryForestage, {
  CommoditySkuRequest,
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
/**
 * @description
 */
class QuerySchemeList {
  /**
   * 分页查询方案列表
   * @param page
   * @param skuQuery
   * @param type
   */
  async pageQuerySchemeList(page: Page, skuQuery: SchemeSkuPropertyRequest) {
    // let result = [] as SchemeDetail[]
    // const request = new CommoditySkuRequest()
    // request.skuPropertyRequest = skuQuery.toSkuPropertyRequest()
    // // 默认只搜索上架的培训班商品
    // request.onShelveRequest = {
    //   onShelveStatus: 1
    // }
    // // 按上架时间降序排
    // const sortRequest = [] as CommoditySkuSortRequest[]
    // const sortOption = new CommoditySkuSortRequest()
    // sortOption.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    // sortOption.policy = SortPolicy.DESC
    // sortRequest.push(sortOption)
    // const response = await MsTradeQueryFrontGatewayTradeQueryForestage.pageCommoditySkuCustomerPurchaseInServicer({
    //   page,
    //   queryRequest: request,
    //   sortRequest
    // })
    // page.totalPageSize = response.data?.totalPageSize
    // page.totalSize = response.data?.totalSize
    // if (response.status?.isSuccess() && response.data?.currentPageData && response.data.currentPageData?.length) {
    //   const resource = response.data.currentPageData
    //   result = resource.map(SchemeDetail.from)
    // }
    // console.log('###schemeList', result)
    // return result
  }
}

export default QuerySchemeList
