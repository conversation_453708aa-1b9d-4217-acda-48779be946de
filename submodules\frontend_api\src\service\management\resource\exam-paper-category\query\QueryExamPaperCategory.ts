import { Response, UiPage } from '@hbfe/common'
import MsExamQueryBackStageGateway, {
  PaperPublishConfigureCategoryRequest
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import PaperPublishCategoryResponseVo from './vo/PaperPublishCategoryResponseVo'

/**
 * @description: 获取试卷分类
 */
class QueryExamPaperCategory {
  /**
   * @description: 根据父级id拿到试卷分类列表
   * @param {id} 默认返回所有分类，-1 为顶级分类
   */
  async queryExamPaperCategoryList(
    page: UiPage,
    parentId = ''
  ): Promise<Response<Array<PaperPublishCategoryResponseVo>>> {
    const params = new PaperPublishConfigureCategoryRequest()
    params.parentId = parentId
    const res = await MsExamQueryBackStageGateway.pagePaperPublishConfigureCategoryInServicer({
      page: page,
      request: params
    })
    const response = new Response<Array<PaperPublishCategoryResponseVo>>()
    if (!res?.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.status = res.status
    response.data = new Array<PaperPublishCategoryResponseVo>()
    response.data = res.data?.currentPageData?.map(PaperPublishCategoryResponseVo.from)
    return response
  }
}

export default QueryExamPaperCategory
