import {
  CourseInfoRequest,
  CourseResponsePage,
  CourseV2Request
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import * as CourseGraphqlImporter from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage/graphql-importer'
import MsCourseLearningQueryFrontGatewayCourseLearningForestage, {
  CourseInSchemeRequest,
  CourseInSchemeResponse,
  CourseOfCourseTrainingOutlineRequest,
  CourseRequest,
  CourseResponse,
  CourseTrainingOutlineResponse,
  ExtInfoRequest,
  StudentChooseCourseRequest,
  StudentChooseCourseTrainingOutlineRequest,
  StudentCourseLearningCommonRequest,
  StudentCourseLearningRangeRequest,
  StudentCourseLearningRequest,
  StudentCourseLearningResponse,
  StudentCourseLearningResponsePage,
  StudentCourseLearningV2ResponsePage,
  StudentCourseRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import {
  default as MsMySchemeQueryFrontGatewayCourseLearningForeStage,
  default as MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import { CourseStatusEnum } from '@api/service/common/enums/course/CourseStatus'
import { TransformStatusEnum } from '@api/service/common/enums/course/CourseTransformStatus'
import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import DataResolve from '@api/service/common/utils/DataResolve'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import AutonomousCourseOutlineResp, {
  UserAssessResultResp
} from '@api/service/customer/course/models/AutonomousCourseOutlineResp'
import QueryCourseList from '@api/service/customer/course/query/QueryCourseList'
import CategoryTypeEnum from '@api/service/customer/course/query/enum/CategoryTypeEnum'
import BatchCourseQueryUtil from '@api/service/customer/course/query/utils/BatchCourseQueryUtil'
import CourseListDetail from '@api/service/customer/course/query/vo/CourseListDetail'
import MyLearningCourse from '@api/service/customer/course/query/vo/MyLearningCourse'
import QuerySchemePackageCourseListParams from '@api/service/customer/course/query/vo/QuerySchemePackageCourseListParams'
import TrainingCourseOutline from '@api/service/customer/course/query/vo/TrainingCourseOutline'
import TrainingOutlineCourse from '@api/service/customer/course/query/vo/TrainingOutlineCourse'
import StudentLearningCourseTypeEnum from '@api/service/customer/learning/scene/gates/enum/StudentLearningCourseTypeEnum'
import { TrainingSchemeTypeEnum } from '@api/service/customer/scheme/enums/TrainingSchemeType'
import ClassOutline, { ChildOutlineResp } from '@api/service/customer/scheme/models/ClassOutline'
import ClassOutlineUtil from '@api/service/customer/scheme/utils/ClassOutlineUtil'
import SchemeUtil from '@api/service/customer/scheme/utils/SchemeUtil'
import { LearningProcessEnum } from '@api/service/customer/train-class/query/Enum/LearningProcess'
import { Page, Response, UiPage } from '@hbfe/common'
import { clone } from 'lodash'
import StudentCourseVo from './vo/StudentCourseVo'

export class ResponsePage<T> {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<T>
}
export class SecondElectiveMaxPeriod {
  /**
   * 学时要求
   */
  electiveMaxPeriod: string
  /**
   * 选修分类二ID
   */
  id: string
  /**
   * 选修分类名称
   */
  name: string
  /**
   * 操作
   */
  operation: number
}
class QueryCourse {
  courseOutlineTreeList: Array<TrainingCourseOutline> = new Array<TrainingCourseOutline>()

  /**
   * 获取特殊的大纲节点
   * @param learningProcess 学习进度
   */
  get specialOutlineTree() {
    return (learningProcess: LearningProcessEnum) => {
      if (learningProcess === LearningProcessEnum.ALL) {
        return this.courseOutlineTreeList
      } else {
        return this.getSpecialCourseOutlineTreeList(learningProcess)
      }
    }
  }

  trainingCourseOutlineCourseMapList: {
    // 培训方案 key 课程大纲
    [key: string]: Array<TrainingOutlineCourse>
  } = {}
  /**
   * 选修要求
   */
  secondElectiveMaxPeriod: SecondElectiveMaxPeriod[] = []
  /**
   * 根据课程 id 集合查询课程名称
   * @param idList
   * @private
   */
  private async queryCourseNameByIdList(
    idList: Array<string>
  ): Promise<Map<string, { name: string; isAllowAudition: boolean }>> {
    const queryCourseList = new QueryCourseList()
    const result = await queryCourseList.queryCoursePageByIdList(idList)
    const map = new Map<string, { name: string; isAllowAudition: boolean }>()
    result.forEach((course: CourseResponse) => {
      map.set(course.id, {
        name: course.name,
        isAllowAudition: course.auditionStatus === 1
      })
    })
    return map
  }

  /**
   * 获取培训方案大纲树
   * @param schemeId
   * @param categoryType 类型
   * @private
   */
  private async querySchemaConfig(
    schemeId: string,
    needField: Array<string>,
    categoryType?: CategoryTypeEnum
  ): Promise<Array<TrainingCourseOutline>> {
    const result = await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage.getSchemeConfigInServicer({
      schemeId,
      needField: needField
    })
    const fieldArr = needField[0].split('.')
    const courseLearningConstructors = {
      chooseCourseLearning: () => {
        return JSON.parse(result.data.schemeConfig).chooseCourseLearning.config.courseTrainingOutlines
      },
      interestCourseLearning: () => {
        return JSON.parse(result.data.schemeConfig).interestCourseLearning.config.courseTrainingOutlines
      },
      autonomousCourseLearning: () => {
        return JSON.parse(result.data.schemeConfig).autonomousCourseLearning.config.courseTrainingOutlines
      }
    }
    if (!courseLearningConstructors[fieldArr[0]]) {
      throw new Error('传入的学习类型不存在')
    }
    const remoteOutlines = courseLearningConstructors[fieldArr[0]]()

    const regenerate = (parentId: string, list: any) => {
      list.forEach((item: any) => {
        item.parentId = parentId
        if (item.childOutlines) {
          regenerate(item.id, item.childOutlines)
        }
        item.children = item.childOutlines
        delete item.childOutlines
      })
    }
    this.secondElectiveMaxPeriod = JSON.parse(
      result.data.schemeConfig
    ).chooseCourseLearning?.config?.chooseCourseRule?.config.secondElectiveMaxPeriod
    const map = this.getLevelNameMap(
      JSON.parse(result.data.schemeConfig).chooseCourseLearning?.config?.courseTrainingOutlines
    )
    this.secondElectiveMaxPeriod?.forEach(item => {
      item.name = map.get(item.id)
    })
    const filterList = categoryType
      ? remoteOutlines.filter((remoteOutline: any) => {
          return remoteOutline.category === categoryType
        })
      : remoteOutlines
    regenerate('-1', filterList)
    return filterList
  }
  private getLevelNameMap(arr: any[], map = new Map<string, string>()) {
    if (arr?.length) {
      for (let i = 0; i < arr.length; i++) {
        const element = arr[i]
        map.set(element.id, element.name)
        if (element.childOutlines?.length > 0) {
          this.getLevelNameMap(element.childOutlines, map)
        }
      }
    }

    return map
  }
  /**
   * 获取可选课程 id 集合
   * @param studentNo
   * @param schemeId
   * @private
   */
  private async getCanChooseOutlineIdSet(studentNo: string, schemeId: string): Promise<Set<string>> {
    const queryParams = new StudentChooseCourseTrainingOutlineRequest()
    queryParams.studentNo = studentNo
    queryParams.schemeId = schemeId
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.listCanChooseCourseTrainingOutlineOfChooseCourseLearningSceneInMyself(
      queryParams
    )
    // 将可以选择的大纲 id 集合构造出来
    const canChooseItemMap = new Set<string>()
    result.data.forEach((outline: CourseTrainingOutlineResponse) => {
      canChooseItemMap.add(outline.outlineId)
    })
    return canChooseItemMap
  }

  /**
   * 根据学员学号获取方案可选课程大纲
   * @param studentNo 学号
   * @param schemeId 培训方案id
   * @param categoryType 包类型
   */
  async queryCanChooseTrainingSchemeCourseOutlineTree(
    studentNo: string,
    schemeId: string,
    categoryType?: CategoryTypeEnum
  ) {
    const outlineList = await this.querySchemaConfig(schemeId, ['chooseCourseLearning'], categoryType)
    const canChooseItemMap = await this.getCanChooseOutlineIdSet(studentNo, schemeId)

    // 递归检索，从自己到子节点是否有可选的记录
    const meOrSubIsInCanChooseList = (outline: TrainingCourseOutline, inArray: Array<boolean>) => {
      if (canChooseItemMap.has(outline.id)) {
        inArray.push(true)
        return true
      }
      if (outline.children.length) {
        outline.children.forEach((outline: TrainingCourseOutline) => {
          const has = meOrSubIsInCanChooseList(outline, inArray)
          if (has) {
            inArray.push(true)
          }
        })
      }
    }

    // 一个空的集合，用来填充最后的结果
    const newList: Array<TrainingCourseOutline> = []

    // 挑选
    const pick = (list: Array<TrainingCourseOutline>, inNewList: Array<TrainingCourseOutline>) => {
      list.forEach((outline: TrainingCourseOutline) => {
        const inArray: Array<boolean> = []
        meOrSubIsInCanChooseList(outline, inArray)
        const newOutline = new TrainingCourseOutline()
        newOutline.id = outline.id
        newOutline.name = outline.name
        newOutline.parentId = outline.parentId
        newOutline.children = new Array<TrainingCourseOutline>()
        // if (inArray.length) {
        inNewList.push(newOutline)
        // }
        pick(outline.children, newOutline.children)
      })
    }
    pick(outlineList, newList)
    this.courseOutlineTreeList = newList
  }

  /**
   * 根据学员学号获取方案兴趣课程大纲
   * @param studentNo 学号
   * @param schemeId 培训方案id
   * @param categoryType 包类型
   */
  async queryCanInterestTrainingSchemeCourseOutlineTree(
    studentNo: string,
    schemeId: string,
    categoryType?: CategoryTypeEnum
  ) {
    // todo 字段记得切回兴趣课学习 interestCourseLearning
    const outlineList = await this.querySchemaConfig(
      schemeId,
      ['interestCourseLearning.config.courseTrainingOutlines'],
      categoryType
    )
    this.courseOutlineTreeList = this.formatOutlineTreeForAll(outlineList)
  }

  /**
   * 【23-6-16已废弃】根据学员学号获取方案自主选课课程大纲
   * @param studentNo 学号
   * @param schemeId 培训方案id
   * @param categoryType 包类型
   */
  async queryCanAutonomousTrainingSchemeCourseOutlineTree(
    studentNo: string,
    schemeId: string,
    categoryType?: CategoryTypeEnum
  ) {
    // todo 字段记得切回自主选课学习 autonomousCourseLearning
    const outlineList = await this.querySchemaConfig(
      schemeId,
      ['autonomousCourseLearning.config.courseTrainingOutlines'],
      categoryType
    )
    this.courseOutlineTreeList = this.formatOutlineTreeForAll(outlineList)?.map(TrainingCourseOutline.fromSchemeConfig)
  }

  /**
   * 根据学员参训资格id获取方案自主选课课程大纲
   * @param qualificationId 参训资格id
   * @param studentNo 学号
   * @param schemeId 培训方案id
   * @param categoryType 包类型
   * @param isQualified 是否合格
   * @return {Promise<void>}
   */
  async queryCanAutonomousTrainingSchemeCourseOutline(
    qualificationId: string,
    studentNo: string,
    schemeId: string,
    isQualified = false,
    categoryType?: CategoryTypeEnum
  ) {
    // todo 字段记得切回自主选课学习 autonomousCourseLearning
    // 学员端用getMySchemaConfig
    let outlineList: Array<TrainingCourseOutline>
    isQualified
      ? (outlineList = await this.querySchemaConfig(
          schemeId,
          ['autonomousCourseLearning.config.courseTrainingOutlines'],
          categoryType
        ))
      : (outlineList = await this.queryMySchemaConfig(
          qualificationId,
          ['autonomousCourseLearning.config.courseTrainingOutlines'],
          categoryType
        ))

    this.courseOutlineTreeList = this.formatOutlineTreeForAll(outlineList)?.map(TrainingCourseOutline.fromSchemeConfig)
    // const courseOutlineConfigList = await this.getCourseOutlineConfigList(qualificationId)
    const courseOutlineLearningInfoList = await this.getCourseOutlineLearningInfo(qualificationId)
    this.courseOutlineTreeList.forEach(item => {
      const learningInfo = courseOutlineLearningInfoList?.find(
        el => el.name && el.name === 'Course_Assess_003' && el.ownerId === item.id
      )
      item.learnedPeriod = learningInfo?.requirePeriod?.current ?? item.learnedPeriod
      item.requirePeriod = learningInfo?.requirePeriod?.config ?? item.requirePeriod
    })
    /** 查询必学课程要求学时和已学学时 **/
    const queryCompulsoryM = new BatchCourseQueryUtil()
    // 1-获取具体大纲节点的必学课程信息列表
    const compulsoryCourseList = await queryCompulsoryM.queryCompulsoryCourseListByStudentNo(studentNo)
    // 2-填充对应大纲节点的必学课程信息
    if (compulsoryCourseList.length) {
      compulsoryCourseList?.forEach(item => {
        const target = this.findNodeInTreeArr(
          this.courseOutlineTreeList,
          (node: TrainingCourseOutline) => node.id === item.outlineId
        )
        if (target) {
          target.compulsoryLearnedPeriod = item.learnedTotalPeriod
          target.compulsoryRequirePeriod = item.allTotalPeriod
        }
      })
    }
    // 3-重算大纲的必学课程信息
    this.reCalCompulsoryInScheme(this.courseOutlineTreeList)
  }

  /**
   * 获取课程大纲json信息[一级分类要求学时、已学学时]
   * @param qualificationId 参训资格id
   * @private
   */
  private async getCourseOutlineConfigList(qualificationId: string) {
    let courseTrainingOutlines: AutonomousCourseOutlineResp[] = []
    const response = await MsMySchemeQueryFrontGatewayCourseLearningForeStage.getMySchemeConfig({
      qualificationId
    })
    if (response.status?.isSuccess()) {
      const jsonString = response.data?.schemeConfig
      if (jsonString) {
        const jsonConfig = JSON.parse(jsonString)
        courseTrainingOutlines =
          (jsonConfig['autonomousCourseLearning']?.config?.courseTrainingOutlines as AutonomousCourseOutlineResp[]) ||
          ([] as AutonomousCourseOutlineResp[])
      }
    }
    return courseTrainingOutlines
  }

  /**
   * 获取课程大纲学习信息
   * @param qualificationId
   * @private
   */
  private async getCourseOutlineLearningInfo(qualificationId: string): Promise<UserAssessResultResp[]> {
    const result = [] as UserAssessResultResp[]
    const response = await MsMySchemeQueryFrontGatewayCourseLearningForeStage.getSchemeLearningInMyself(qualificationId)
    if (response.status?.isSuccess()) {
      const metadata = response.data
      metadata?.studentLearning?.courseLearning?.userAssessResult?.forEach(item => {
        const jsonConfig = JSON.parse(item)
        result.push(jsonConfig)
      })
    }
    return result
  }

  /**
   * 根据学号、大纲、内容供应方获取大纲课程列表
   * @param studentNo
   * @param courseOutLineId
   * @param contentIds
   * @param uiPage
   * @param courseName
   * @param excludeOutlineIds
   */
  async queryTrainingOutlineCourse(
    studentNo: string,
    courseOutLineId: string,
    contentIds?: Array<string>,
    uiPage?: Page,
    courseName?: string,
    excludeOutlineIds?: Array<string>
  ) {
    await this.queryCourseByTrainingCourseOutline(
      courseOutLineId,
      studentNo,
      contentIds,
      uiPage,
      courseName,
      excludeOutlineIds
    )
  }

  /**
   * 查询大纲下面的课程列表
   * @param courseOutlineId
   * @param studentNo
   * @param contentIds 内容提供方id数组
   * @param uiPage
   * @param courseName 课程名称
   * @param excludeOutlineIds 要排除的大纲id列表
   * @private
   */
  private async queryCourseByTrainingCourseOutline(
    courseOutlineId: string,
    studentNo: string,
    contentIds?: Array<string>,
    uiPage?: Page,
    courseName?: string,
    excludeOutlineIds?: Array<string>
  ) {
    const request = new StudentChooseCourseRequest()
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()

    // 为了处理多OutlineId的情况，将OutLineId数组拼成字符串作为Key值，以##作为切割标识
    const courseOutlineIdList = courseOutlineId.split('##')
    if (courseOutlineIdList?.length) {
      request.courseOfCourseTrainingOutline.outlineIds = courseOutlineIdList
    } else {
      request.courseOfCourseTrainingOutline.outlineIds = [courseOutlineId]
    }

    if (excludeOutlineIds && excludeOutlineIds?.length) {
      request.courseOfCourseTrainingOutline.excludeOutlineIds = excludeOutlineIds
    }

    if (courseName) {
      request.course = new CourseInfoRequest()
      request.course.courseName = courseName
    }
    request.studentNo = studentNo
    request.extInfo = new ExtInfoRequest()
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCanChooseCourseOfChooseCourseLearningSceneInMyself(
      {
        page: uiPage,
        request
      }
    )

    uiPage.totalSize = result.data?.totalSize
    uiPage.totalPageSize = result.data?.totalPageSize

    const courseIdSet = new Set<string>()
    this.trainingCourseOutlineCourseMapList[courseOutlineId] = result.data.currentPageData.map(
      (response: CourseInSchemeResponse) => {
        courseIdSet.add(response.course.courseId)
        return TrainingOutlineCourse.from(response)
      }
    )
    if (result?.data?.currentPageData?.length) {
      // 填充课程详细信息
      const courseList = await this.queryCourseListByIdList(Array.from(courseIdSet.values()))
      this.trainingCourseOutlineCourseMapList[courseOutlineId].forEach((course: TrainingOutlineCourse) => {
        const courseDetail = courseList.find((detail: CourseListDetail) => {
          return detail.id === course.id
        })
        if (courseDetail) {
          course.name = courseDetail.name
          course.teachers = courseDetail.teachers
          course.isAllowAudition = courseDetail.canListen
          course.courseTypeName = courseDetail.courseTypeName.join('、')
          course.logoUrl = courseDetail.coverImage
        }
      })
    }

    return this.trainingCourseOutlineCourseMapList[courseOutlineId]
  }

  /**
   * 根据课程 id 集合查
   * @param idList
   */
  async queryCourseListByIdList(idList: Array<string>): Promise<Array<CourseListDetail>> {
    if (!idList?.length) {
      return []
    }
    // const page = new UiPage()
    // page.pageNo = 1
    // page.pageSize = 1
    // const request = new CourseRequest()
    // request.courseIdList = idList
    // let result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseInServicer({
    //   page,
    //   request
    // })

    // if (result.data.totalSize) {
    //   page.pageSize = result.data.totalSize
    //   result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseInServicer({
    //     page,
    //     request
    //   })
    // }
    const rqNum = idList.length / 200

    const rqList = new Array<CourseV2Request>()
    for (let i = 0; i < rqNum; i++) {
      const request = new CourseV2Request()
      request.pageNo = 1
      request.pageSize = 200
      request.courseIdList = idList.slice(i * 200, i * 200 + 200)
      rqList.push(request)
    }
    const req = new RewriteGraph<CourseResponsePage, CourseV2Request>(
      MsCourseLearningQueryFrontGatewayCourseLearningForestage._commonQuery,
      CourseGraphqlImporter.pageCourseV2InServicer
    )

    await req.request(rqList)
    const keys = [...req.indexMap.keys()]
    const lastQueryResult = new Array<CourseResponse>()
    keys.map((key, index) => {
      lastQueryResult.push(...req.indexMap.get(index).currentPageData)
    })

    // 获取教师 id 集合
    const teacherIdSet = new Set<string>()
    const list = lastQueryResult.map((response: CourseResponse) => {
      if (response.teacherIds) {
        response.teacherIds.forEach((id: string) => {
          teacherIdSet.add(id)
        })
      }
      return CourseListDetail.from(response)
    })

    // 加载教师信息
    if (teacherIdSet.size) {
      const listTeacher = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.listTeacherInServicer(
        Array.from(teacherIdSet.values())
      )
      lastQueryResult.forEach((item: CourseResponse, index: number) => {
        item.teacherIds?.length &&
          item.teacherIds.map(id => {
            const findTeacher = listTeacher.data.find(it => it.id === id)
            if (findTeacher) {
              list[index].teachers.push(SimpleUserInfo.from(findTeacher))
            }
          })
      })
    }
    return list
  }

  /**
   * 根据课程包 id 查询课程分页
   * @param page
   * @param idList
   */
  async queryCoursePageByCategoryId(page: UiPage, idList: Array<string>) {
    const request = new CourseRequest()
    request.categoryIdList = idList
    request.status = (TransformStatusEnum.AVAILABLE as unknown) as number
    request.enable = CourseStatusEnum.ENABLE
    const lastResult = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseInServicer({
      page,
      request
    })
    return lastResult.data.currentPageData.map(CourseListDetail.from)
  }

  /**
   * 查询方案下面的课程集合
   * @param page
   * @param params
   */
  async queryCoursePageInSchemeByByPackage(
    page: UiPage,
    params: QuerySchemePackageCourseListParams
  ): Promise<Array<TrainingOutlineCourse>> {
    const request = new CourseInSchemeRequest()
    request.schemeId = params.schemeId
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    request.courseOfCourseTrainingOutline.outlineIds = params.outlineIdList
    // 获取服务端返回的数据
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseInSchemeInServicer({
      page,
      request
    })
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize

    // 构造出一个只有课程 id 的集合
    const courseIdLst = result.data.currentPageData.map((course: CourseInSchemeResponse) => course.course.courseId)
    let flatClassOutlineList: ClassOutline[] = []
    if (result.status?.isSuccess() && result.data?.currentPageData?.length) {
      const metadata = result.data.currentPageData
      const schemeId = metadata[0].range?.schemeId
      const _schemeUtil = new SchemeUtil()
      const jsonConfigMap = await _schemeUtil.batchQuerySchemeConfig([schemeId])
      const jsonConfig = jsonConfigMap.get(schemeId)
      const schemeType = jsonConfig.type as TrainingSchemeTypeEnum
      const _courseTrainingOutlines =
        (jsonConfig[schemeType]?.config?.courseTrainingOutlines as ChildOutlineResp[]) || ([] as ChildOutlineResp[])
      const _classOutlineUtil = new ClassOutlineUtil()
      const classOutlines = _classOutlineUtil.reShapeTree(_courseTrainingOutlines, schemeId)
      flatClassOutlineList = _classOutlineUtil.treeToList(classOutlines)
    }

    // 将集合去查询课程名称，填充并且返回 map 结构 { courseId: name }
    const courseInfoMap = await this.queryCourseNameByIdList(courseIdLst)
    return result.data.currentPageData.map((response: CourseInSchemeResponse) => {
      const course = TrainingOutlineCourse.from(response)
      const findItem = (courseInfoMap.get(course.id) || {}) as { name: string; isAllowAudition: boolean }
      course.name = findItem.name
      course.isAllowAudition = findItem.isAllowAudition
      const compulsoryCourseIdList = flatClassOutlineList?.find(el => el.id === course.outlineId)
        ?.compulsoryCourseIdList
      if (compulsoryCourseIdList?.length && compulsoryCourseIdList.includes(course.id)) {
        course.isCompulsory = true
      }
      return course
    })
  }

  /**
   * 课程分页【培训证明明细展示】
   * @param page
   * @param courseLearningResourceType 1.选课规则课程学习场景 2.自主选课课程学习场景 3.兴趣课课程学习场景
   */
  async queryStudentCoursePage(page: UiPage, studentNo: string, schemeType: string): Promise<Array<StudentCourseVo>> {
    const pageQueryParam = new StudentCourseLearningCommonRequest()
    pageQueryParam.studentNo = studentNo
    pageQueryParam.studentCourse = new StudentCourseRequest()
    pageQueryParam.studentCourse.courseLearningStatus = [2]
    pageQueryParam.studentCourseLearningRange = new StudentCourseLearningRangeRequest()
    // 根据方案类型来判断课程学习场景
    pageQueryParam.studentCourseLearningRange.courseLearningResourceType =
      schemeType === 'autonomousCourseLearning' ? 2 : 1
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseInServicer({
      page,
      request: pageQueryParam
    })
    const courseIdSet = new Set<string>()
    if (!result.data.currentPageData) return new Array<StudentCourseVo>()
    const resultList = result.data.currentPageData?.map((course: StudentCourseLearningResponse) => {
      courseIdSet.add(course.course.courseId)
      return StudentCourseVo.from(course)
    })

    const queryCourseList = new QueryCourseList()
    const courseList = await queryCourseList.queryCoursePageByIdList(Array.from(courseIdSet.values()))
    const resultMap = new Map<string, CourseResponse>()
    courseList.forEach((course: CourseResponse) => {
      resultMap.set(course.id, course)
    })
    resultList.forEach((learningCourse: StudentCourseVo) => {
      const responseCourse = resultMap.get(learningCourse.id)
      learningCourse.id = responseCourse.id
      learningCourse.name = responseCourse.name
    })
    return resultList
  }

  private formatOutlineTreeForAll(outlineTree: Array<TrainingCourseOutline>): Array<TrainingCourseOutline> {
    outlineTree?.forEach((outline, index) => {
      if (outline?.children?.length) {
        const allOutline = new TrainingCourseOutline()
        allOutline.name = '全部'
        allOutline.isSpecialNode = true
        allOutline.id = `${index + 1}-${index}`
        allOutline.parentId = outline.id
        allOutline.children = undefined
        outline.children.unshift(allOutline)
        this.formatOutlineTreeForAll(outline.children)
      }
    })
    return outlineTree
  }

  private outlineIdsForAll(
    outlineTree: Array<TrainingCourseOutline>,
    outlineParentId: string,
    outlineIds: Array<string>
  ): Array<string> {
    outlineTree?.forEach(outline => {
      if (outline.parentId === outlineParentId) {
        outlineIds.push(outline.id)
        if (outline?.children?.length) {
          this.outlineIdsForAll(outline.children, outline.id, outlineIds)
        }
      }
    })
    return outlineIds
  }

  /**
   * 查询培训班是否有 100 但是未评价的课程
   * @param studentNo
   * @param schemeType
   */
  async queryCourseListIsCompleteButNotEvaluated(studentNo: string, schemeType: string) {
    const request = new StudentCourseLearningRequest()
    request.studentNo = studentNo
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    let result: Response<StudentCourseLearningV2ResponsePage>
    const query = async () => {
      if (schemeType === StudentLearningCourseTypeEnum.ChooseCourseRule) {
        result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneV2InMyself(
          {
            page,
            request
          }
        )
      } else if (schemeType === StudentLearningCourseTypeEnum.AutonomousCourse) {
        result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneV2InMyself(
          {
            page,
            request
          }
        )
      } else if (schemeType === StudentLearningCourseTypeEnum.InterestCourse) {
        result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneV2InMyself(
          {
            page,
            request
          }
        )
      }
    }

    await query()
    if (result && result?.data) {
      if (result?.data?.totalSize) {
        page.pageSize = result?.data?.totalSize
        await query()
      }
    }

    const courseIdList = result.data.currentPageData.map(course => course.course.courseId)
    const getCourseInfoList = await this.queryCourseListByIdList(courseIdList)

    // 过滤 100 但是未评价
    return result?.data?.currentPageData
      .filter(item => {
        return item.studentCourseMediaLearningRecord.schedule === 100 && !item.studentCourseAppraised?.appraisalCourse
      })
      .map(course => {
        const resultCourse = MyLearningCourse.from(course)
        const detail = getCourseInfoList.find(remoteCourse => remoteCourse.id === course.course.courseId)
        resultCourse.detail.teachers = detail.teachers
        resultCourse.detail.name = detail.name
        return resultCourse
      })
  }

  /**
   * 查询自主选课模式下培训班是否有 100 但是未评价的课程
   */
  async queryCourseListIsCompleteButNotEvaluatedInAutoMode(studentNo: string) {
    const request = new StudentCourseLearningRequest()
    request.studentNo = studentNo
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 1
    let result: Response<StudentCourseLearningResponsePage>
    const query = async () => {
      result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseOfAutonomousCourseLearningSceneInMyself(
        {
          page,
          request
        }
      )
    }
    await query()
    if (result && result?.data) {
      if (result?.data?.totalSize) {
        page.pageSize = result?.data?.totalSize
        await query()
      }
    }
    return result?.data?.currentPageData
      ?.filter(item => {
        return item.studentCourseMediaLearningRecord.schedule === 100 && !item.studentCourseAppraised?.appraisalCourse
      })
      ?.map(course => {
        return MyLearningCourse.from(course)
      })
  }

  /**
   * 根据条件查找大纲节点
   * @param outlineId 大纲id
   * @param treeArr 树形结构
   * @private
   */
  private findNodeInTreeArr(treeArr: TrainingCourseOutline[], func: Function): TrainingCourseOutline | null {
    treeArr?.forEach(item => {
      if (func(item)) return item
      if (item.children && item.children.length) {
        const subRes = this.findNodeInTreeArr(item.children, func)
        if (subRes) return subRes
      }
    })
    return null
  }

  /**
   * 查找大纲的所有叶子节点
   * @param treeArr 树形结构
   * @private
   */
  private findAllLeavesInTreeArr(treeArr: TrainingCourseOutline[]): TrainingCourseOutline[] {
    const result = [] as TrainingCourseOutline[]
    const getLeaves = (nodeList: TrainingCourseOutline[]) => {
      nodeList.forEach(item => {
        if (!item.children || !item.children.length) {
          result.push(item)
        } else {
          getLeaves(item.children)
        }
      })
    }
    getLeaves(treeArr)
    return result
  }

  /**
   * 重算节点的必学课程信息
   * @param outlineId 大纲节点id
   * @param treeArr 树形结构
   * @private
   */
  private reCalCompulsoryInNode(outlineId: string, treeArr: TrainingCourseOutline[]) {
    const target = this.findNodeInTreeArr(treeArr, (node: TrainingCourseOutline) => node.id === outlineId)
    if (target) {
      target.compulsoryRequirePeriod =
        target.children?.reduce((prev, cur) => {
          return CalculatorObj.add(prev, cur.compulsoryRequirePeriod || 0)
        }, 0) || 0
      target.compulsoryLearnedPeriod =
        target.children?.reduce((prev, cur) => {
          return CalculatorObj.add(prev, cur.compulsoryLearnedPeriod || 0)
        }, 0) || 0
      const parentNode = this.findNodeInTreeArr(treeArr, (node: TrainingCourseOutline) => node.id === target.parentId)
      if (parentNode) {
        this.reCalCompulsoryInNode(target.parentId, treeArr)
      }
    }
  }

  /**
   * 重算大纲的必学课程信息
   */
  private reCalCompulsoryInScheme(treeArr: TrainingCourseOutline[]) {
    const leaves = this.findAllLeavesInTreeArr(treeArr)
    leaves.forEach(item => {
      this.reCalCompulsoryInNode(item.parentId, treeArr)
    })
  }

  /**
   * 树形结构转为列表
   * @param treeArr
   * @private
   */
  private treeToList(treeArr: TrainingCourseOutline[]): TrainingCourseOutline[] {
    let result = [] as TrainingCourseOutline[]
    treeArr?.forEach(node => {
      result.push(node)
      if (node.children && node.children?.length) {
        const res = this.treeToList(node.children)
        result = result.concat(res)
      }
    })
    return result
  }

  /**
   * 获取第二次过滤的数组
   * @param flatList 打平的数组
   * @param firstFilterList 第一次过滤的数组
   * @param notConformList 不符合的数组
   * @private
   */
  private getSecondFilterList(
    flatList: TrainingCourseOutline[],
    firstFilterList: TrainingCourseOutline[],
    notConformList: TrainingCourseOutline[]
  ): TrainingCourseOutline[] {
    const result = clone(firstFilterList) as TrainingCourseOutline[]
    // 1-将不符合的项分组：父节点id下的哪些节点id不符合
    const groupArr =
      notConformList.reduce((prev, cur) => {
        const target = prev.find(el => el.parentId === cur.parentId)
        if (target) {
          if (!target.ids.includes(cur.id)) {
            target.ids.push(cur.id)
          }
        } else {
          if (cur.id) {
            prev.push({
              parentId: cur.parentId,
              ids: [cur.id]
            })
          }
        }
        return prev
      }, [] as { parentId: string; ids: string[] }[]) || []
    // 2-遍历判断是否父节点下的子节点id集合是否与分组一致，一致则删除该项
    groupArr?.forEach(el => {
      const parentNode = flatList.find(itm => itm.id === el.parentId)
      if (parentNode) {
        const childIds = parentNode.children?.map(itm => itm.id)?.filter(Boolean)
        if (DataResolve.compareEquals(childIds, el.ids)) {
          const spliceIndex = result.findIndex(itm => itm.id === el.parentId)
          if (spliceIndex >= 0) {
            result.splice(spliceIndex, 1)
          }
        }
      }
    })
    return result
  }

  /**
   * 数组转树形结构
   * @param treeArr
   * @param parentId 父节点id
   * @private
   */
  private listToTree(treeArr: TrainingCourseOutline[], parentId = '0'): TrainingCourseOutline[] {
    const nodeList = treeArr.filter(node => node.parentId === parentId)
    nodeList?.forEach(node => {
      const subRes = this.listToTree(treeArr, node.id)
      if (!subRes || !subRes.length) {
        node.children = undefined
      } else {
        node.children = subRes
      }
    })
    return nodeList
  }

  /**
   * 根据学习进度获取树
   * @param learningProcess 学习进度
   * @private
   */
  private getSpecialCourseOutlineTreeList(learningProcess: LearningProcessEnum) {
    let firstFilterList: TrainingCourseOutline[]
    // 平铺的列表
    const flatList = this.treeToList(this.courseOutlineTreeList)
    const notConformList: TrainingCourseOutline[] = []
    if (learningProcess === LearningProcessEnum.LEARNING) {
      // 学习中 => 要求学时>0 && 完成学时>0 && 完成学时<要求学时
      firstFilterList = flatList.filter(el => {
        if (el.children?.length) return true
        if (!el.children || !el.children.length) {
          if (el.learnedPeriod > 0 && el.requirePeriod > 0 && el.learnedPeriod < el.requirePeriod) {
            return true
          } else {
            notConformList.push(el)
            return false
          }
        }
      })
    }
    if (learningProcess === LearningProcessEnum.UNLEARNED) {
      // 未学习 => 要求学时>0 && 完成学时===0
      firstFilterList = flatList.filter(el => {
        if (el.children?.length) return true
        if (!el.children || !el.children.length) {
          if (el.requirePeriod > 0 && el.learnedPeriod === 0) {
            return true
          } else {
            notConformList.push(el)
            return false
          }
        }
      })
    }
    if (learningProcess === LearningProcessEnum.COMPLETED) {
      // 已完成 => 要求学时>0 && 要求学时===完成学时
      firstFilterList = flatList.filter(el => {
        if (el.children?.length) return true
        if (!el.children || !el.children.length) {
          if (el.requirePeriod > 0 && el.requirePeriod === el.learnedPeriod) {
            return true
          } else {
            notConformList.push(el)
            return false
          }
        }
      })
    }
    const secondFilterList: TrainingCourseOutline[] = this.getSecondFilterList(
      flatList,
      firstFilterList,
      notConformList
    )
    const newTree = this.listToTree(secondFilterList)
    console.log(`${learningProcess}-flatList`, flatList)
    console.log(`${learningProcess}-firstFilterList`, firstFilterList)
    console.log(`${learningProcess}-notConformList`, notConformList)
    console.log(`${learningProcess}-secondFilterList`, secondFilterList)
    console.log(`${learningProcess}-newTree`, newTree)
    return newTree
  }

  /**
   * 获取学员班级大纲树
   * @param categoryType 类型
   * @private
   * @param qualificationId 参选资格id
   * @param needField
   * @return {Promise<any>}
   */
  private async queryMySchemaConfig(
    qualificationId: string,
    needField: Array<string>,
    categoryType?: CategoryTypeEnum
  ): Promise<Array<TrainingCourseOutline>> {
    const result = await MsMySchemeQueryFrontGatewayCourseLearningForeStage.getMySchemeConfig({
      qualificationId,
      needField
    })
    const fieldArr = needField[0].split('.')
    const courseLearningConstructors = {
      chooseCourseLearning: () => {
        return JSON.parse(result.data.schemeConfig).chooseCourseLearning.config.courseTrainingOutlines
      },
      interestCourseLearning: () => {
        return JSON.parse(result.data.schemeConfig).interestCourseLearning.config.courseTrainingOutlines
      },
      autonomousCourseLearning: () => {
        return JSON.parse(result.data.schemeConfig).autonomousCourseLearning.config.courseTrainingOutlines
      }
    }
    if (!courseLearningConstructors[fieldArr[0]]) {
      throw new Error('传入的学习类型不存在')
    }
    const remoteOutlines = courseLearningConstructors[fieldArr[0]]()

    const regenerate = (parentId: string, list: any) => {
      list.forEach((item: any) => {
        item.parentId = parentId
        if (item.childOutlines) {
          regenerate(item.id, item.childOutlines)
        }
        item.children = item.childOutlines
        delete item.childOutlines
      })
    }
    this.secondElectiveMaxPeriod = JSON.parse(
      result.data.schemeConfig
    ).chooseCourseLearning?.config?.chooseCourseRule?.config.secondElectiveMaxPeriod
    const map = this.getLevelNameMap(
      JSON.parse(result.data.schemeConfig).chooseCourseLearning?.config?.courseTrainingOutlines
    )
    this.secondElectiveMaxPeriod?.forEach(item => {
      item.name = map.get(item.id)
    })
    const filterList = categoryType
      ? remoteOutlines.filter((remoteOutline: any) => {
          return remoteOutline.category === categoryType
        })
      : remoteOutlines
    regenerate('-1', filterList)
    return filterList
  }
}

export default QueryCourse
