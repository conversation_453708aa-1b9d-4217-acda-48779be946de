class QuestionAnswerConfiguration {
  /**
   * 是否必答
   */
  required = false
}

class FixedQuestion {
  /**
   * 试题id
   */
  questionId = ''
  /**
   * 控制学员在作答试题的可活动范围
   */
  answerConfiguration: QuestionAnswerConfiguration = new QuestionAnswerConfiguration()
  /**
   * 分值
   */
  score = 0.0
  /**
   * 综合题子题的配置
   */
  children: Array<FixedQuestion> = new Array<FixedQuestion>()
}

export default FixedQuestion
