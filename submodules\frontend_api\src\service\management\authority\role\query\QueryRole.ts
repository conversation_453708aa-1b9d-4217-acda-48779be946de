import { Page } from '@hbfe/common'
import Permission from '@api/service/management/authority/role/query/vo/Permission'
import RoleDetail from '@api/service/management/authority/role/query/vo/RoleDetail'
import Mockjs from 'mockjs'
/**
 * 角色查询
 */

class QueryRole {
  permissionList: Array<Permission> = new Array<Permission>()
  /**
   * 查询角色分页
   * @param page
   */

  async pageRole(page: Page): Promise<Array<RoleDetail>> {
    try {
      console.log('page参数=', page)
      page.totalSize = Mockjs.Random.integer(50, 100)
      page.totalPageSize = Math.ceil(page.totalSize / page.pageSize)
      console.log(
        '调用了pageRole方法，返回值=',
        new Promise(resolve => {
          setTimeout(() => {
            resolve(
              Mockjs.mock({
                [`data|${page.pageSize}`]: [
                  {
                    id: '@uuid',
                    name: '@csentence(30)',
                    createTime: '@date("yyyy-MM-dd HH:mm:ss")',
                    enable: '@boolean',
                    description: '@csentence(100)'
                  }
                ]
              }).data
            )
          }, 300)
        })
      )
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(
            Mockjs.mock({
              [`data|${page.pageSize}`]: [
                {
                  id: '@uuid',
                  name: '@csentence(30)',
                  createTime: '@date("yyyy-MM-dd HH:mm:ss")',
                  enable: '@boolean',
                  description: '@csentence(100)'
                }
              ]
            }).data
          )
        }, 300)
      })
    } catch (e) {
      console.log('报错了，所处位置/service/management/authority/role/query/QueryRole.ts所处方法，pageRole', e)
    }
  }

  async getPermissionList(): Promise<Array<Permission>> {
    try {
      const list = Mockjs.mock({
        [`data|10`]: [
          {
            id: '@uuid',
            name: '@csentence(10)',
            createTime: '@date("yyyy-MM-dd HH:mm:ss")',
            enable: '@boolean',
            children: Mockjs.mock({
              'data|5': [
                {
                  id: '@uuid',
                  name: '@csentence(10)',
                  children: Mockjs.mock({
                    'data|5': [
                      {
                        id: '@uuid',
                        name: '@csentence(10)',
                        children: Mockjs.mock({
                          'data|5': [
                            {
                              id: '@uuid',
                              name: '@csentence(10)'
                            }
                          ]
                        }).data
                      }
                    ]
                  }).data
                }
              ]
            }).data
          }
        ]
      }).data
      this.permissionList = list
      console.log('调用了getPermissionList方法，返回值=', list)
      return list
    } catch (e) {
      console.log('报错了，所处位置/service/management/authority/role/query/QueryRole.ts所处方法，getPermissionList', e)
    }
  }

  async findPermissionList(roleId: string): Promise<Array<Permission>> {
    try {
      console.log('roleId参数=', roleId)
      console.log('调用了findPermissionList方法，返回值=', new Array<Permission>())
      return new Array<Permission>()
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/query/QueryRole.ts所处方法，findPermissionList',
        e
      )
    }
  }

  async findPermissionListByRoleId(roleId: string): Promise<Array<Permission>> {
    try {
      console.log('roleId参数=', roleId)
      console.log('调用了findPermissionListByRoleId方法，返回值=', new Array<Permission>())
      return new Array<Permission>()
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/query/QueryRole.ts所处方法，findPermissionListByRoleId',
        e
      )
    }
  }

  async findUserSecurityList(): Promise<Array<Permission>> {
    try {
      console.log('调用了findUserSecurityList方法，返回值=', new Array<Permission>())
      return new Array<Permission>()
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/query/QueryRole.ts所处方法，findUserSecurityList',
        e
      )
    }
  }

  async getRoleDetail(roleId: string): Promise<RoleDetail> {
    try {
      console.log('roleId参数=', roleId)
      console.log(
        '调用了getRoleDetail方法，返回值=',
        Mockjs.mock({
          [`data`]: {
            id: '@uuid',
            name: '@csentence(30)',
            createTime: '@date("yyyy-MM-dd HH:mm:ss")',
            enable: '@boolean'
          }
        }).data
      )
      return Mockjs.mock({
        [`data`]: {
          id: '@uuid',
          name: '@csentence(30)',
          createTime: '@date("yyyy-MM-dd HH:mm:ss")',
          enable: '@boolean'
        }
      }).data
    } catch (e) {
      console.log('报错了，所处位置/service/management/authority/role/query/QueryRole.ts所处方法，getRoleDetail', e)
    }
  }
}

export default QueryRole
