import { ResponseStatus } from '@hbfe/common'
import MsSchemeQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import MsLearningScheme from '@api/ms-gateway/ms-learningscheme-v1'

export default class MutationTrainClassOnSheles {
  constructor(schemeId?: string, onShelve?: boolean) {
    this.schemeId = schemeId || ''
    this.onShelve = onShelve
  }

  /**
   * 方案配置
   * json解析出的对象
   */
  private schemeConfig: any = null

  /**
   * 方案id
   */
  schemeId: string

  /**
   * 上架状态
   */
  onShelve: boolean = null

  /**
   * 计划上架时间
   */
  onShelvePlanTime = ''

  /**
   * 计划下架时间
   */
  offShelvePlanTime = ''

  /**
   * 查询当前方案配置
   */
  async querySchemeConfig() {
    if (!this.schemeId) {
      return new ResponseStatus(8011, '方案id不能为空')
    }
    //   获取方案配置
    const configRes = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInServicer({
      schemeId: this.schemeId
    })
    if (configRes.status.isSuccess() && configRes.data?.schemeConfig) {
      this.schemeConfig = JSON.parse(configRes.data.schemeConfig)
      this.onShelvePlanTime = this.schemeConfig['commoditySale'].onOrOffShelvesPlan.onShelvePlanTime ?? ''
      this.offShelvePlanTime = this.schemeConfig['commoditySale'].onOrOffShelvesPlan.offShelvePlanTime ?? ''
    } else {
      return new ResponseStatus(8012, '获取方案配置失败')
    }
  }

  /**
   * 修改上架状态及计划时间
   */
  async changeStatusAndTime() {
    if (!this.schemeConfig) {
      return new ResponseStatus(8013, '方案配置不存在')
    }
    this.schemeConfig['commoditySale'].onOrOffShelvesPlan = {
      onShelve: this.onShelve,
      onShelvePlanTime: this.onShelvePlanTime,
      offShelvePlanTime: this.offShelvePlanTime
    }
    const specialUpdateRes = await MsLearningScheme.specialUpdateLearningScheme({
      token: 'W10=',
      configJson: JSON.stringify(this.schemeConfig)
    })
    return specialUpdateRes.status
  }
}
