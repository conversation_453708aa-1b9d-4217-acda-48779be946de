import {
  AdminInfoResponse,
  AdminUserInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { GendersEnum } from '@api/service/common/enums/user/GenderTypes'
import RegionModelVo from '../../student/vo/RegionModelVo'

class AdminUserInfoVo extends AdminUserInfoResponse {
  /**
   * 用户地区
   */
  region: RegionModelVo = new RegionModelVo()
  /*
    用户id
  */
  userId = ''
  /**
   * 用户名称
   */
  userName = ''
  /**
   * 性别
    -1未知，0女，1男
    @see Genders
   */
  gender: GendersEnum = null
  /**
   * 证件号
   */
  idCard = ''
  /**
   * 手机号
   */
  phone = ''
  /**
   * 注册时间
   */
  createTime = ''
  /**
   * 邮箱
   */
  email = ''

  from(item: AdminInfoResponse) {
    if (item.userInfo?.manageRegionList?.length) {
      this.region = item.userInfo?.manageRegionList[0]
    } else {
      this.region = null
    }
    this.userId = item.userInfo?.userId
    this.userName = item.userInfo?.userName
    this.gender = item.userInfo?.gender
    this.idCard = item.userInfo?.idCard
    this.createTime = item.accountInfo?.createdTime
    this.phone = item.userInfo?.phone
    this.email = item.userInfo?.email
  }
}

export default AdminUserInfoVo
