"""独立部署的微服务,K8S服务名:ms-basicdata-domain-gateway-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询内置性质的角色
		目前不按照登录账户角色进行过滤，由前端进行控制，只要能看到新建角色管理能进行角色添加这个菜单栏的都查询出网校管理员和专题管理员
		根据Apollo配置获取展示的角色
	"""
	findBuildInRole(request:FindBuildInRoleRequest):[RoleResponse]
	"""获取当前可分配角色列表
		@return 可分配角色列表
	"""
	findCurrentAssignableRoleList:[RoleToAccountCountResponse]
	"""查询当前所有功能权限
		@return
	"""
	findCurrentFunctionalAuthorityList:[SecurityObjectResponse]
	"""查询当前所有角色列表
		@return 角色对应账户数量列表
	"""
	findCurrentRoleList:[RoleToAccountCountResponse]
	"""查询当前登录应用域下的所有角色列表
		可根据传参指定应用类型来获取角色列表，传参未给值时，默认取上下文中的应用类型进行查询
		@param request
		@return
	"""
	findCurrentRoleListByArgs(request:FindCurrentRoleListByArgsRequest):[RoleToAccountCountResponse]
	"""查询当前所有角色列表 new
		调用仓储联表查时去除多余无用联表操作
		@return 角色对应账户数量列表
	"""
	findCurrentRoleListNew:[RoleToAccountCountResponse]
	findCurrentRoleListPlus(request:FindCurrentRoleListPlusRequest):[RoleToAccountCountResponse]
	"""角色id查询功能权限
		@param roleId 角色id
		@return 功能权限对象列表
	"""
	findFunctionalAuthorityByRoleId(roleId:String):[SecurityObjectResponse]
	"""角色id查询功能权限【返回来源id】
		@param roleId
		@return
	"""
	findFunctionalAuthorityByRoleIdNew(roleId:String):FindFunctionalAuthorityByRoleIdNewResponse
	"""角色id集合查询功能权限
		@param roleIds 角色id集合
		@return 角色对应功能权限集合
	"""
	findFunctionalAuthorityByRoleIds(roleIds:[String]):[RoleToFunctionalAuthorityResponse]
	"""角色id集合查询功能权限
		@param roleIds 角色id集合
		@return 角色对应功能权限集合【来源id】
	"""
	findFunctionalAuthorityByRoleIdsNew(roleIds:[String]):[RoleToFunctionalAuthorityNewResponse]
	"""查询当前登录用户的openId
		@return
	"""
	findLoginUserOpenId:FindLoginUserOpenIdResponse
	"""账户id查询角色详情
		@return 角色响应类
	"""
	findRoleByAccountId:[RoleResponse]
	"""查询当前账户的角色权限 仅供个人账户使用
		@param unitId 单位id 单位id未传则查询当前账户下的所有角色权限
		@return
	"""
	findRoleByAccountIdOrUnitIdForPersonAccount(unitId:String):[RoleResponseForPersonAccount]
	"""根据业务域的单位ID查询角色
		@return {@link RoleResponse}
	"""
	findRoleByBusinessDomainUnitId:[RoleResponse]
	"""角色分类查询角色列表
		@param category
		@return
	"""
	findRoleByCategory(category:Int!):[RoleResponse]
	"""id查询角色详情
		@param roleId 角色id【必填】
		@return 角色响应类
	"""
	findRoleById(roleId:String!):RoleResponse
	"""查询所属方角色（包含内置角色）
		@param request 查询所属方角色请求（包含内置角色）
		@return 角色列表
	"""
	findRoleByOwner(request:FindRoleByOwnerRequest):[RoleResponse]
	"""获取当前登录用户服务商列表"""
	getCurrentUserServicer:GetCurrentUserServicerResponse
}
type Mutation {
	"""管理员换绑学员手机号
		@param request
	"""
	ChangePhoneByAdmin(request:ChangePhoneByAdminRequest):Void
	"""重置网校管理员密码-兼容跨域问题
		@param request 重置密码，密码由前端传入
	"""
	ResetAdminPasswordWithResponse(request:ResetAdminPasswordRequest):GenernalResponse
	"""重置密码 - 没有返回值作废
		@param request 重置密码，密码由前端传入
	"""
	ResetPassword(request:ResetPasswordRequest):Void
	"""重置密码
		@param request 重置密码，密码由前端传入
	"""
	ResetPasswordWithResponse(request:ResetPasswordRequest):GenernalResponse
	"""新增学员证书信息（单条）"""
	addStudentCertificateInfo(createStudentCertificateRequest:CreateStudentCertificateRequest):TokenResponse
	"""绑定/换绑微信
		响应码
		200:成功
		400：错误的请求 通常是token错误或者入参错误
		60005:该openId已被其他账号绑定
		60004：当前用户已绑定微信开放平台
		@param request
		@return
	"""
	applyBindAndReBindWeChatOpenPlatform(request:JobSeekerBindPlatformAccountRequest):TokenResponse
	"""申请绑定微信开放平台并返回账户ID登陆token
		@param request {@link ApplyBindAndLoginRequest},{token,roleCategory,account,password,openId}
		@return {code,message,token}
	"""
	applyBindWeChatOpenPlatformAndValidLogin(request:ApplyBindAndLoginRequest):TokenExtendResponse @optionalLogin
	"""当前登录用户绑定微信开放平台
		@param request
		@return
	"""
	applyBindWeChatOpenPlatformLoginAccount(request:ApplyBindWeChatOpenPlatformLoginAccountRequest):ApplyBindWeChatOpenPlatformLoginAccountResponse
	"""申请图形验证码
		@param applyInfo 申请信息
		@return 图形验证码信息
	"""
	applyCaptcha(applyInfo:CaptchaApplyRequest):CaptchaResponse @optionalLogin
	"""申请切换用工企业Token列表
		@return 切换用工企业Token列表
	"""
	applyChangeEnterpriseTokenList:[ChangeEnterpriseTokenResponse]
	"""申请切换政策参与者关联单位Token列表
		@return {@link ChangePolicyActorUnitAuthorizeTokenResponse}
	"""
	applyChangePolicyActorUnitAuthorizeTokenList:ChangePolicyActorUnitAuthorizeTokenResponse
	"""获取登录帐户手机短信验证码
		@param request 请求参数
	"""
	applyCurrentPhoneSmsCode(request:ApplyCurrentPhoneSmsRequest):TokenResponse
	"""申请用工企业Metadata
		@param request 申请企业元数据请求
		@return {name,code}
	"""
	applyEnterpriseMetadata(request:EnterpriseMetadataApplyRequest):EnterpriseMetadataApplyResponse
	"""申请微信开放平台账户ID登陆token
		@param request {@link ApplyLoginByOpenIdRequest},{token,roleCategory,openId}
		@return {code,message,token}
	"""
	applyLoginByOpenId(request:ApplyLoginByOpenIdRequest):TokenExtendResponse @optionalLogin
	"""申请获取H5授权登录openId-服务商
		@param request {code,servicerId}
		@return {code,message,openId}
	"""
	applyOpenIdByServicerId(request:ApplyOpenIdRequest):ApplyOpenIdResponse @optionalLogin
	"""申请获取web扫码登录openId-服务商
		@param request {code,servicerId}
		@return {code,message,openId}
	"""
	applyScanCodeOpenIdByServicerId(request:ApplyOpenIdRequest):ApplyOpenIdResponse @optionalLogin
	"""申请安全性高的验证码
		@param applyInfo 申请信息
		@return 图形验证码信息
	"""
	applySecureCaptcha(applyInfo:SecureCaptchaApplyRequest):SecureCaptchaResponse @optionalLogin
	"""申请手机短信验证码
		@param applyInfo 申请信息
		@return 手机短信验证码信息
		异常状态码
		400:token无效
		409:短信验证码获取次数超过限制
		410:短信验证码发送异常
		509:未绑定手机号
		514:token中未携带手机号
		515:不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)
		701:已经处理,请勿重复提交
	"""
	applySmsCode(applyInfo:SmsCodeApplyRequest):TokenResponse @optionalLogin
	"""申请手机短信验证码-----换绑手机号使用
		@param applyInfo 申请信息
		@return 手机短信验证码信息
		异常状态码
		400:token无效
		409:短信验证码获取次数超过限制
		410:短信验证码发送异常
		509:未绑定手机号
		514:token中未携带手机号
		515:不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)
		701:已经处理,请勿重复提交
		702:用户不存在手机号,无法换绑
		703:业务类型有误或用户未登入
	"""
	applySmsCodeForChangingPhone(applyInfo:SmsCodeForChangingPhoneApplyRequest):TokenResponse @optionalLogin
	"""申请手机短信验证码【未登录使用】
		原因：由于网关插件改版，登录后的分销商取的servicerProvider中的服务商id为分销商id，发送短信时，取的是对应网校服务商id的模板
		@param applyInfo 申请信息
		@return 手机短信验证码信息
		异常状态码
		400:token无效
		409:短信验证码获取次数超过限制
		410:短信验证码发送异常
		509:未绑定手机号
		514:token中未携带手机号
		515:不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)
		701:已经处理,请勿重复提交
	"""
	applySmsCodeForLoginDistributor(applyInfo:SmsCodeApplyRequest):TokenResponse @optionalLogin
	"""申请手机短信验证码【已登录使用】
		原因：由于网关插件改版，登录后的分销商取的servicerProvider中的服务商id为分销商id，发送短信时，取的是对应网校服务商id的模板
		@param applyInfo 申请信息
		@return 手机短信验证码信息
		异常状态码
		400:token无效
		409:短信验证码获取次数超过限制
		410:短信验证码发送异常
		509:未绑定手机号
		514:token中未携带手机号
		515:不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)
		701:已经处理,请勿重复提交
	"""
	applySmsCodeForLoginDistributorNeedLogin(applyInfo:SmsCodeApplyRequest):TokenResponse
	"""申请校验人脸核身认证V1 (目标账户为单账户)
		@param request 申请校验人脸核身认证请求
		@return 登陆token
	"""
	applyValidFacialRecognitionV1(request:ApplyValidFacialRecognitionV1Request):TokenResponse @optionalLogin
	"""申请校验人脸核身认证V1 WEB端
		@param request 申请校验人脸核身认证V1 Web端请求
		@return {code,message}
	"""
	applyValidFacialRecognitionV1Web(request:ApplyValidFacialRecognitionV1WebRequest):GenernalResponse @optionalLogin
	"""申请校验人脸核身认证结果(目标账户为单账户)
		@param request 申请校验人脸核身认证结果请求
		@return 登陆token
	"""
	applyValidFacialRecognitionVerifyResult(request:ApplyValidFacialRecognitionVerifyResultRequest):TokenResponse @optionalLogin
	"""申请校验人脸核身认证结果(目标账户为单账户)并绑定微信开放平台
		1.人脸结果二次核验(可选)
		2.身份证、姓名验证
		3.验证openId对应账户与身份证对应账户是否相同
		4.若openId不存在对应账户且身份证账户未绑定微信平台则进行绑定操作
		@param request {token,idCardNumber,name,verifyResult,openId}
		@return 登陆token
		状态码说明：
		505:账户不存在
		506:可用账户不唯一
		701:账户已绑定openId，但与当前授权进入的微信用户不是同一个人
		702:openId关联的账户与当前身份证对应的账户不是同一个
	"""
	applyValidFacialRecognitionVerifyResultAndBindWeChatOpenPlatform(request:ApplyValidFacialRecognitionVerifyResultExtendRequest):TokenResponse @optionalLogin
	"""申请校验人脸核身认证结果(目标账户为多账户) - 移除
		@param request 申请校验人脸核身认证结果请求
		@return {code,切换的token列表}
	"""
	applyValidFacialRecognitionVerifyResultTokenList(request:ApplyValidFacialRecognitionVerifyResultRequest):ChangePolicyActorUnitAuthorizeTokenResponse @optionalLogin
	"""当前登录用户绑定微信开发平台
		@param request
		@return
	"""
	bindLoginAccountOpenPlatform(request:BindLoginAccountOpenPlatformRequest):BindLoginAccountOpenPlatformResponse
	"""【服务商】给当前登陆账户绑定手机号 - 免登录
		@param
		@return
	"""
	bindPhoneForCurrentAccount(request:BindPhoneForCurrentAccountRequest):GenernalResponse @optionalLogin
	"""给当前登陆用户绑定手机号
		@param token token
		@return {code,message}
	"""
	bindPhoneForCurrentUser(token:String):GenernalResponse
	"""管理中心 - 集体管理员个人帐号管理 - 换绑手机
		@param request
		@return
	"""
	changeCollectiveRegisterPhone(request:CollectiveRegisterChangePhoneRequest):GenernalResponse
	"""【服务商】换绑手机号
		@param newPhoneToken 新手机号token
	"""
	changeNewPhone(newPhoneToken:String):Void
	"""【服务商】换绑手机号
		用于分销商
		@param newPhoneToken 新手机号token
	"""
	changeNewPhoneForFxs(newPhoneToken:String):Void
	"""【服务商】换绑手机号
		@param newPhoneToken 新手机号token
		@return
	"""
	changeNewPhoneWithResponse(newPhoneToken:String):GenernalResponse
	"""变更当前登录帐户的密码 - 无返回值已废弃
		@param changeInfo 更新信息
	"""
	changePasswordByCurrent(changeInfo:CurrentAccountChangePasswordRequest):Void
	"""变更当前登录帐户的密码
		@param changeInfo
	"""
	changePasswordByCurrentWithResponse(changeInfo:CurrentAccountChangePasswordRequest):GenernalResponse
	"""登陆时强制修改初始密码
		@param changeInfo 入参新密码
	"""
	changePasswordByForceModifyInitPassword(changeInfo:ForceModifyInitPasswordRequest):GenernalResponse
	"""换绑手机号
		@param applyInfo 申请信息
	"""
	changePhone(applyInfo:ChangePhoneRequest):Void
	checkExistUnitByName(unitName:String):Void
	checkOnlineAdminAuthentication(identity:String,applicationMemberType:Int,currentUserId:String,categoryTypeList:[Int],identityType:Int,serviceId:String):Void
	checkUserAuthentication(identity:String,applicationMemberType:Int,currentUserId:String,categoryTypeList:[Int],identityType:Int):Void
	"""创建管理员账户
		@param request 请求信息
	"""
	createAdministratorAccount(request:CreateAdministratorAccountRequest):Void
	"""创建用工企业
		@param createInfo 创建信息
	"""
	createEnterprise(createInfo:EnterpriseCreateRequest):ChangeEnterpriseTokenResponse
	"""创建用工企业经办
		@param createInfo 创建信息
	"""
	createEnterpriseManager(createInfo:EnterpriseManagerCreateRequest):Void
	"""创建人社管理员
		@param createInfo 创建信息
		手机号已存在-100001
		身份证已存在-100002
		登录名称已存在-100003
	"""
	createMOHRSSAdmin(createInfo:MOHRSSAdminCreateRequest):Void
	"""创建下属人社管理员
		@param createInfo 创建信息
		手机号已存在-100001
		身份证已存在-100002
		登录名称已存在-100003
	"""
	createMOHRSSSubordinateAdmin(createInfo:MOHRSSSubordinateAdminCreateRequest):Void
	"""当前登录人为分销平台学员
		创建网校学员帐户并绑定成员资格领域
		200 成功
		402 核验异常
		400 token解析异常
		408 验证码过期，请重新获取验证码
		500 验证码错误，请重新输入
		100002 身份证已存在
		100001 手机号已存在
		200001 源账户已被其他账户绑定
		@param request
		@return
	"""
	createOnlineSchoolStudent(request:CreateOnlineSchoolStudentRequest):RegisterStudentResponse
	"""创建网校子管理员
		@param request 创建网校子管理员请求
		@return {code,message}
	"""
	createOnlineSchoolSubAdmin(request:CreateOnlineSchoolSubAdminRequest):GenernalResponse
	"""创建网校子管理员（使用初始token）
		@param request {token,...}
		@return {code,message}
	"""
	createOnlineSchoolSubAdminByToken(request:CreateOnlineSchoolSubAdminByTokenRequest):GenernalResponse
	"""创建角色请求
		@param request
	"""
	createRole(request:CreateRoleRequest):String
	createRoleByAdminType(request:CreateRoleByAdminTypeRequest):String
	"""创建角色请求(新)
		@param request 创建角色请求
		@return 角色ID
	"""
	createRoleNew(request:CreateRoleRequest):String
	"""创建服务提供商账户
		@param request 请求信息
	"""
	createServiceProviderAccount(request:CreateServiceProviderAccountRequest):Void
	"""新增子项目管理内置接口"""
	createSubProjectAdministrator(request:CreateSubProjectAdministratorRequest):Void @optionalLogin
	"""创建内置角色请求
		@param request JSON.toJSONString(CreateRoleRequestCommand... command)
	"""
	createSystemInternalRole(request:String):Void @optionalLogin
	"""创建内置角色
		@param request
	"""
	createSystemInternalRoles(request:CreateSystemInternalRoleRequest):Void @optionalLogin
	"""删除学员证书信息（单条）"""
	deleteCertificateInfo(deleteStudentCertificateRequest:DeleteStudentCertificateRequest):TokenResponse
	"""禁用用工企业经办
		@param accountId 帐户ID
	"""
	disableEnterpriseManager(accountId:String):Void
	"""启用用工企业经办
		@param accountId 帐户ID
	"""
	enableEnterpriseManager(accountId:String):Void
	"""依据认证标识判断是否已经存在学员
		509:未绑定手机
		402:identityType、identity为空，格式不正确
		512：手机号已存在
		@param request 入参
		@return
	"""
	existUser(request:ExistUserRequest):ExistUserResponse @optionalLogin
	"""修改当前账户密码(用户端忘记密码)
		@param request 修改当前账户密码请求
		@return Token响应对象
	"""
	forgetPassword(request:CurrentAccountChangePasswordCauseForgetRequest):TokenResponse @optionalLogin
	"""冻结帐户
		@param accountId 【必填】帐户ID
	"""
	freezeAccount(accountId:String):Void
	"""立即重置密码
		@param resetInfo 重置密码信息
	"""
	immediateResetPassword(resetInfo:ImmediateResetPasswordRequest):Void
	"""初始化单位树
		@param request
	"""
	initUnitTree(request:UnitCreateRequest):Void @optionalLogin
	"""登录并绑定平台账号【微信】
		@param request {identity,password,captcha,token,loginToken}
		@return {code,message,token}
	"""
	loginAndBindOpenPlatform(request:BindPlatformAccountRequest):TokenResponse @optionalLogin
	"""登录并绑定平台账号【微信】（2024.02.20 ·新）
		@param request {identity,password,captcha,token,loginToken}
		@return {code,message,token}
	"""
	loginAndBindOpenPlatformV2(request:BindPlatformAccountRequest):TokenResponse @optionalLogin
	"""集体报名管理员 注册
		@param request
		@return
	"""
	registerCollectiveRegisterAdmin(request:RegisterCollectiveRegisterAdminRequest):TokenResponse @optionalLogin
	"""通过闽政通注册用工企业经办
		@param registerInfo 注册信息
	"""
	registerEnterPriseManagerForMZT(registerInfo:EnterpriseManagerRegisterForMztRequest):EnterpriseManagerRegisterResponse @optionalLogin
	"""创建企业账户
		@param request 请求信息
	"""
	registerEnterpriseAccount(request:RegisterEnterpriseAccountRequest):EnterpriseAccountRegisterResponse @optionalLogin
	"""注册用工企业经办
		@param registerInfo 注册信息
	"""
	registerEnterpriseManager(registerInfo:EnterpriseManagerRegisterRequest):EnterpriseManagerRegisterResponse @optionalLogin
	"""注册学员账号
		@param createInfo 注册学员账号信息 {输入的短信验证码、输入手机号、输入的图片验证码、token(申请短信返回的token)}
	"""
	registerStudent(createInfo:CreateStudentRequest):TokenResponse @optionalLogin
	"""注册学员账号（2024.02.20 ·新）
		@param createInfo 注册学员账号信息 {输入的短信验证码、输入手机号、输入的图片验证码、token(申请短信返回的token)}
	"""
	registerStudentV2(createInfo:CreateStudentRequest):TokenResponse @optionalLogin
	removeRoleWithAdminTypeById(roleId:String!):GenernalResponse
	"""删除角色(没有角色账户授权关系则删除)
		@param roleId 角色id
	"""
	removeRoleWithNoAuthRelById(roleId:String!):GenernalResponse
	"""删除内置角色请求
		@param request
	"""
	removeSystemInternalRole(request:RemoveSystemInternalRoleRequest):Void @optionalLogin
	"""恢复冻结的帐户
		@param accountId 【必填】帐户ID
	"""
	resumeAccount(accountId:String):Void
	"""解绑手机号
		@param request 解绑请求
	"""
	unbindPhone(request:UnbindPhoneRequest):Void
	"""解绑手机号(当前登陆用户)
		@param request 解绑请求
	"""
	unbindPhoneForCurrentUser(request:UnbindPhoneForCurrentUserRequest):Void
	"""解绑用户微信开放平台
		@param userId 用户ID
	"""
	unbindWeChatOpenPlatform(userId:String!):Void
	"""修改管理员账户
		@param request 请求信息
	"""
	updateAdministratorAccount(request:UpdateAdministratorAccountRequest):Void
	"""管理中心 - 集体管理员个人帐号管理 - 更新个人资料
		@param request
		@return
	"""
	updateCollectiveRegister(request:UpdateCollectiveRegisterRequest):GenernalResponse
	"""管理域 -培训管理 - 集体报名咨询 - 修改登录账号"""
	updateCollectiveRegisterAccountInfo(request:UpdateCollectiveRegisterAccountInfoRequest):GenernalResponse
	"""更新用工企业
		@param updateInfo 更新信息
	"""
	updateEnterprise(updateInfo:EnterpriseUpdateRequest):Void
	"""更新用工企业经办
		@param updateInfo 更新信息
	"""
	updateEnterpriseManager(updateInfo:EnterpriseManagerUpdateRequest):Void
	"""更新人社管理员信息
		@param updateInfo 更新信息
		手机号已存在-100001
		身份证已存在-100002
		登录名称已存在-100003
	"""
	updateMOHRSSAdmin(updateInfo:MOHRSSAdminUpdateRequest):Void
	"""更新下属人社管理员信息
		@param updateInfo 更新信息
		手机号已存在-100001
		身份证已存在-100002
		登录名称已存在-100003
	"""
	updateMOHRSSSubordinateAdmin(updateInfo:MOHRSSSubordinateAdminUpdateRequest):Void
	"""运营域 修改网校管理员账户
		@param request 请求信息
	"""
	updateOnlineAdministratorAccount(request:UpdateOnlineAdministratorAccountRequest):TokenResponse
	"""修改网校子管理员
		@param request 创建网校子管理员请求
		@return {code,message}
	"""
	updateOnlineSchoolSubAdmin(request:UpdateOnlineSchoolSubAdminRequest):GenernalResponse
	"""修改网校子管理员（使用初始token）
		@param request {token,...}
		@return {code,message}
	"""
	updateOnlineSchoolSubAdminByToken(request:UpdateOnlineSchoolSubAdminByTokenRequest):GenernalResponse
	"""修改角色请求
		@param request
	"""
	updateRole(request:UpdateRoleRequest):Void
	updateRoleByAdminType(request:UpdateRoleByAdminTypeRequest):Void
	"""修改服务提供商账户
		@param request 请求信息
	"""
	updateServiceProviderAccount(request:UpdateServiceProviderAccountRequest):Void
	"""更新学员账号信息（学员端）
		@param updateInfo 更新学员账号信息
	"""
	updateStudent(updateInfo:UpdateStudentRequest):TokenResponse
	"""更新当前登录学员的基础信息
		@param request
		@return
	"""
	updateStudentBasicInfo(request:UpdateStudentBasicInfoRequest):GenernalResponse
	"""更新学员账号信息（管理端）
		@param updateInfo 更新学员账号信息
	"""
	updateStudentByAdmin(updateInfo:UpdateStudentSystemRequest):TokenResponse
	"""更新学员证书信息（单条）"""
	updateStudentCertificateInfo(updateStudentCertificateRequest:UpdateStudentCertificateRequest):TokenResponse
	"""修改内置角色请求
		@param request
	"""
	updateSystemInternalRole(request:UpdateSystemInternalRoleRequest):Void @optionalLogin
	"""验证图形验证码
		@param validInfo 验证信息
		@return 验证结果
	"""
	validCaptcha(validInfo:CaptchaValidRequest):TokenResponse @optionalLogin
	"""验证账号/手机号
		@param validInfo 验证账号/手机号是否存在请求
		@return 验证结果
	"""
	validIdentity(validInfo:ValidIdentityRequest):IdentityValidResponse @optionalLogin
	"""验证账号/手机号
		用于分销商忘记密码验证
		@param validInfo 验证账号/手机号是否存在请求
		@return 验证结果
	"""
	validIdentityForFxs(validInfo:ValidIdentityFxsRequest):IdentityValidResponse @optionalLogin
	"""验证账号/手机号
		分销平台中，一个账户存在多种不同应用方类型的角色时，会查询出重复的账户信息（只有角色信息不一致）
		添加新口去除重复账户id
		@param validInfo 验证账号/手机号是否存在请求
		@return 验证结果
	"""
	validIdentityRemoveDuplicates(validInfo:ValidIdentityRequest):IdentityValidResponse @optionalLogin
	"""校验是否绑定微信开放平台
		@param request {@link ValidIsBindWeChatOpenPlatformRequest},{token,roleCategory,openId}
		@return {code,message,isBind[true:false]}
	"""
	validIsBindWeChatOpenPlatform(request:ValidIsBindWeChatOpenPlatformRequest):ValidIsBindOpenPlatformResponse @optionalLogin
	"""验证手机短信验证码
		@param validInfo 验证信息
		@return 验证结果
	"""
	validSmsCode(validInfo:SmsCodeValidRequest):TokenResponse @optionalLogin
}
"""@author: xucenhao
	@time: 2024-10-09
	@description:
"""
input ImageCaptchaTrack @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.api.command.dto.ImageCaptchaTrack") {
	"""背景图片宽度."""
	bgImageWidth:Int
	"""背景图片高度."""
	bgImageHeight:Int
	"""滑块图片宽度."""
	sliderImageWidth:Int
	"""滑块图片高度."""
	sliderImageHeight:Int
	"""滑动开始时间."""
	startSlidingTime:Long!
	"""滑动结束时间."""
	endSlidingTime:Long!
	"""滑动的轨迹."""
	trackList:[Track]
}
input Track @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.api.command.dto.ImageCaptchaTrack$Track") {
	"""x."""
	x:Int
	"""y."""
	y:Int
	"""时间."""
	t:Int
	"""类型."""
	type:String
}
"""@author: linxiquan
	@Date: 2023/11/3 17:23
	@Description: 教师行业专门字段： 用户学段、学科信息
"""
input SectionAndSubjects @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.dto.SectionAndSubjects") {
	"""学段"""
	section:Int
	"""学科"""
	subjects:Int
}
"""获取当前登录手机号短信请求信息
	<AUTHOR> 2022/6/27 19:28
"""
input ApplyCurrentPhoneSmsRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.ApplyCurrentPhoneSmsRequest") {
	"""获取当前登录手机号短信token"""
	token:String
	"""业务类型
		登录  10000
		帐户注册 20000
		忘记密码  30000
		换绑手机号 40000
	"""
	businessType:Int
}
"""<AUTHOR>
	@description:【服务商】给当前登陆账户绑定手机号 - 免登录
	@date 2024/12/19 19:49
"""
input BindPhoneForCurrentAccountRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.BindPhoneForCurrentAccountRequest") {
	"""链式token-账户Id"""
	token:String
	"""绑定的手机号"""
	phone:String
}
"""图形验证码申请信息
	<AUTHOR>
"""
input CaptchaApplyRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.CaptchaApplyRequest") {
	"""【必填】图形验证码业务Token"""
	token:String
	"""【必填】图形验证码业务类型
		@see ValidationBusinessTypes
	"""
	businessType:Int!
}
"""图形验证码验证信息
	<AUTHOR>
"""
input CaptchaValidRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.CaptchaValidRequest") {
	"""【必填】申请图形验证码获得的token"""
	token:String
	"""【必填】图形验证码"""
	captchaCode:String
}
"""管理员更改手机号
	<AUTHOR> [2023/4/17 18:46]
"""
input ChangePhoneByAdminRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.ChangePhoneByAdminRequest") {
	"""修改手机号账户id[必填]"""
	userId:String
	"""新手机号【必填】"""
	newPhone:String
	"""旧手机号【必填】"""
	oldPhone:String
}
"""换绑手机号请求信息
	<AUTHOR>
"""
input ChangePhoneRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.ChangePhoneRequest") {
	"""【必填】旧手机号token"""
	oldPhoneToken:String
	"""【必填】新手机号token"""
	newPhoneToken:String
}
"""创建内置角色请求
	<AUTHOR>
"""
input CoerceCreateRoleRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.CoerceCreateRoleRequest") {
	id:String
	"""应用方类型（不传则取上下文数据）【创建内置角色需赋值】
		@see SystemMemberTypes
	"""
	applicationMemberType:Int
	"""应用方类别
		@see com.fjhb.domain.basicdata.api.consts.ApplicationMemberCategories
	"""
	applicationMemberCategory:Int!
	"""角色类别
		@see RoleCategories
	"""
	category:Int
	"""所属方类型（不传则取上下文数据）【创建内置角色需赋值】
		@see SystemMemberTypes
	"""
	ownerMemberType:Int
	"""所属方id（不传则取上下文数据）【创建内置角色需赋值】"""
	ownerMemberId:String
	"""名称"""
	name:String
	"""应用类型，标明当前角色用于什么客户域（人社/用工企业）
		@see BusinessDomainIdentity#applicationType
		@see UnitBusinessTypes
		@see SubProjectBusinessTypes
	"""
	applicationType:Int
	"""角色描述"""
	description:String
	"""角色code"""
	code:String
	"""安全对象id"""
	securityObjectIds:[String]
}
input CollectiveRegisterChangePhoneRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.CollectiveRegisterChangePhoneRequest") {
	"""【必填】新手机号码"""
	newPhone:String!
	"""【必填】短信验证码"""
	smsCode:String!
	"""【必填】申请短信返回的token"""
	token:String!
}
"""创建超级管理员账户请求信息
	<AUTHOR> 2022/7/18 11:26
"""
input CreateAdministratorAccountRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.CreateAdministratorAccountRequest") {
	"""管理员姓名【必填】"""
	name:String
	"""登录账号【必填】"""
	accountName:String
	"""手机号【必填】"""
	phone:String
	"""角色列表【必填】"""
	roleIds:[String]
}
"""角色响应类
	<AUTHOR>
"""
input CreateRoleByAdminTypeRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.CreateRoleByAdminTypeRequest") {
	"""名称"""
	name:String
	"""角色描述"""
	description:String
	"""角色code"""
	code:String
	"""角色分类
		1-学员  2-集体报名管理员  3-管理员  4-人设管理员  5-企业管理员  6-人社审批管理员  7-企业经办  8-企业法人  9-企业超管  10-人社超管
		11-人社职建处  12-人社就业局  13-企业超管  14-合同供应商  15-专家
	"""
	category:Int
	"""功能权限id集合"""
	functionalAuthorityIds:[String]
}
"""角色响应类
	<AUTHOR>
"""
input CreateRoleRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.CreateRoleRequest") {
	"""名称"""
	name:String
	"""角色描述"""
	description:String
	"""角色code"""
	code:String
	"""角色分类
		1-学员  2-集体报名管理员  3-管理员  4-人设管理员  5-企业管理员  6-人社审批管理员  7-企业经办  8-企业法人  9-企业超管  10-人社超管
		11-人社职建处  12-人社就业局  13-企业超管  14-合同供应商  15-专家
	"""
	category:Int
	"""功能权限id集合"""
	functionalAuthorityIds:[String]
}
"""创建服务提供商请求
	<AUTHOR> 2022/7/19
"""
input CreateServiceProviderAccountRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.CreateServiceProviderAccountRequest") {
	"""姓名【必填】"""
	name:String
	"""用户名【必填】"""
	userName:String
	"""手机号【必填】"""
	phone:String
	"""密码 【必填】"""
	password:String
	"""顶级账户Id 【超管创建时必填】"""
	rootAccountId:String
}
input CreateSubProjectAdministratorRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.CreateSubProjectAdministratorRequest") {
	"""平台名称，做为企业"""
	platformName:String!
	"""平台管理员名称【非必填】"""
	platformManagerName:String
	"""平台管理员角色名"""
	platformManagerRoleId:String!
	"""平台管理员账号"""
	platformManagerLoginAccount:String!
	"""平台管理员密码"""
	platformManagerLoginPassword:String!
	"""手机号【非必填】"""
	phone:String
}
"""创建内置角色请求
	<AUTHOR>
"""
input CreateSystemInternalRoleRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.CreateSystemInternalRoleRequest") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerId:String
	coerceCreateRoleRequests:[CoerceCreateRoleRequest]
}
"""修改当前账户密码请求(用户端忘记密码)
	<AUTHOR>
"""
input CurrentAccountChangePasswordCauseForgetRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.CurrentAccountChangePasswordCauseForgetRequest") {
	"""认证token"""
	token:String
	"""新密码"""
	password:String
}
"""变更当前登录帐户的密码信息
	<AUTHOR>
"""
input CurrentAccountChangePasswordRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.CurrentAccountChangePasswordRequest") {
	"""【必填】原始密码"""
	originalPassword:String
	"""【必填】新密码"""
	newPassword:String
}
"""用工企业创建信息
	<AUTHOR>
"""
input EnterpriseCreateRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.EnterpriseCreateRequest") {
	"""【必填】加密的元数据"""
	metadata:String
	"""【必填】企业名称"""
	name:String
	"""【必填】统一社会信用代码"""
	code:String
	"""【必填】单位类型"""
	type:String
	"""【必填】所属行业"""
	industry:String
	"""【必填】注册地区"""
	area:String
	"""【必填】联系地址"""
	address:String
	"""注册地址"""
	registerAddress:String
	"""【必填】邮编"""
	postcode:String
	"""【必填】注册资金"""
	registeredCapital:String
	"""【必填】注册登记机关"""
	registeredOrgan:String
	"""营业期限起始日期"""
	operatingBeginDate:DateTime
	"""营业期限截止日期"""
	operatingEndDate:DateTime
	"""【必填】成立日期"""
	foundedDate:DateTime
	"""法定代表人/负责人"""
	legalPerson:String
	"""【必填】法定代表人证件类型"""
	idCardType:Int!
	"""【必填】法定代表人证件号"""
	idCard:String
}
"""用工企业经办创建信息
	<AUTHOR>
"""
input EnterpriseManagerCreateRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.EnterpriseManagerCreateRequest") {
	"""【必填】姓名"""
	name:String
	"""【必填】身份证号"""
	idCard:String
	"""手机号"""
	phone:String
	"""要授权的角色的ID集合"""
	roleIds:[String]
}
"""用工企业经办通过闽政通注册信息
	<AUTHOR>
"""
input EnterpriseManagerRegisterForMztRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.EnterpriseManagerRegisterForMztRequest") {
	"""【必填】登录路口标识token"""
	token:String
	"""【必填】认证中心闽政通登录后返回的token"""
	mztToken:String
}
"""用工企业经办注册信息
	<AUTHOR>
"""
input EnterpriseManagerRegisterRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.EnterpriseManagerRegisterRequest") {
	"""【必填】手机短信验证码验证token"""
	token:String
	"""【必填】手机号码"""
	phone:String
	"""【必填】密码"""
	password:String
}
"""用工企业经办创建信息
	<AUTHOR>
"""
input EnterpriseManagerUpdateRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.EnterpriseManagerUpdateRequest") {
	"""【必填】账户ID"""
	accountId:String
	"""【必填】姓名"""
	name:String
	"""【必填】身份证号"""
	idCard:String
	"""手机号"""
	phone:String
	"""要新增授权的角色的ID集合"""
	addRoleIds:[String]
	"""要移除授权的角色的ID集合"""
	removeRoleIds:[String]
}
"""申请企业元数据请求
	<AUTHOR>
"""
input EnterpriseMetadataApplyRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.EnterpriseMetadataApplyRequest") {
	"""加密的元数据"""
	metadata:String
}
"""用工企业更新信息
	<AUTHOR>
"""
input EnterpriseUpdateRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.EnterpriseUpdateRequest") {
	"""【必填】企业id"""
	id:String
	"""【必填】企业名称"""
	name:String
	"""【必填】统一社会信用代码"""
	code:String
	"""【必填】单位类型"""
	type:String
	"""【必填】所属行业"""
	industry:String
	"""【必填】注册地区"""
	area:String
	"""【必填】联系地址"""
	address:String
	"""【必填】注册地址"""
	registerAddress:String
	"""【必填】邮编"""
	postcode:String
	"""【必填】注册资金"""
	registeredCapital:String
	"""【必填】注册登记机关"""
	registeredOrgan:String
	"""营业期限起始日期"""
	operatingBeginDate:DateTime
	"""营业期限截止日期"""
	operatingEndDate:DateTime
	"""【必填】成立日期"""
	foundedDate:DateTime
	"""法定代表人/负责人"""
	legalPerson:String
	"""【必填】法定代表人证件类型"""
	idCardType:Int!
	"""【必填】法定代表人证件号"""
	idCard:String
}
"""@BelongsProject: fjhb-microservice-basicdata-domain-gateway
	@BelongsPackage: com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request
	@Author: xucenhao
	@CreateTime: 2024-06-13  14:59
	@Description:
"""
input ExistUserRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.ExistUserRequest") {
	"""认证标识"""
	identity:String
	"""认证标识类型
		@see AuthenticationIdentityTypes
	"""
	identityType:Int
}
input FindBuildInRoleRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.FindBuildInRoleRequest") {
	"""默认网校管理员，查询出网校管理员和专题管理员角色
		角色code
		com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.consts.RoleCodeConstans
	"""
	code:String
}
input FindCurrentRoleListByArgsRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.FindCurrentRoleListByArgsRequest") {
	"""应用类型数组
		不传的话默认从上下文中去应用类型
		7 专题管理员
		1002 网校
	"""
	applicationTypes:[Int]
}
"""@author: xucenhao
	@time: 2024-09-05
	@description:
"""
input FindCurrentRoleListPlusRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.FindCurrentRoleListPlusRequest") {
	"""应用方id"""
	applicationMemberId:String
}
"""查询所属方角色请求（包含内置角色）
	<AUTHOR>
"""
input FindRoleByOwnerRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.FindRoleByOwnerRequest") {
	"""所属方类型【必填】
		4-子项目  5-单位  6-服务商
	"""
	ownerMemberType:Int
	"""所属方id【必填】"""
	ownerMemberId:String
}
"""强制修改初始密码请求"""
input ForceModifyInitPasswordRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.ForceModifyInitPasswordRequest") {
	"""【必填】新密码"""
	newPassword:String
}
"""立即重置密码
	<AUTHOR>
"""
input ImmediateResetPasswordRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.ImmediateResetPasswordRequest") {
	"""【必填】帐户ID"""
	accountId:String
	"""【必填】要重置密码的业务类型
		人社管理员-MOHRSS  系统管理员-SYSTEM  管理员-SUPER_ADMINISTRATOR  供应商-SERVICE_PROVIDER
		课件供应商-COURSEWARE_SUPPLIER,网校(培训机构)-TRAINING_INSTITUTION,学员-STUDENT,地区管理员-REGION_ADMINISTRATOR
		集体报名管理员-COLLECTIVE_REGISTER
	"""
	businessType:String
}
"""人社管理员创建信息
	<AUTHOR>
"""
input MOHRSSAdminCreateRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.MOHRSSAdminCreateRequest") {
	"""【必填】姓名"""
	name:String
	"""【必填】身份证号"""
	idCard:String
	"""登录账号"""
	userName:String
	"""【必填】手机号"""
	phone:String
	"""【必填】所在处/科室"""
	belongDivision:String
	"""【必填】要授权的角色的ID集合"""
	roleIds:[String]
}
"""人社管理员创建信息
	<AUTHOR>
"""
input MOHRSSAdminUpdateRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.MOHRSSAdminUpdateRequest") {
	"""【必填】管理员账户ID"""
	accountId:String
	"""【必填】姓名"""
	name:String
	"""登录账号"""
	userName:String
	"""【必填】身份证号"""
	idCard:String
	"""【必填】手机号"""
	phone:String
	"""【必填】所在处/科室"""
	belongDivision:String
	"""要新增授权的角色的ID集合"""
	addRoleIds:[String]
	"""要移除授权的角色的ID集合"""
	removeRoleIds:[String]
}
"""下属人社管理员创建信息
	<AUTHOR>
"""
input MOHRSSSubordinateAdminCreateRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.MOHRSSSubordinateAdminCreateRequest") {
	"""【必填】下属人社单位ID"""
	unitId:String
	"""【必填】姓名"""
	name:String
	"""【必填】身份证号"""
	idCard:String
	"""【必填】手机号"""
	phone:String
	"""所在处/科室"""
	belongDivision:String
	"""【必填】角色Id列表"""
	roles:[String]
}
"""下属人社管理员更新信息
	<AUTHOR>
"""
input MOHRSSSubordinateAdminUpdateRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.MOHRSSSubordinateAdminUpdateRequest") {
	"""【必填】管理员账户ID"""
	accountId:String
	"""【必填】下属人社单位ID"""
	unitId:String
	"""【必填】姓名"""
	name:String
	"""【必填】身份证号"""
	idCard:String
	"""【必填】手机号"""
	phone:String
	"""所在处/科室"""
	belongDivision:String
	"""添加的角色Id列表"""
	addRoles:[String]
	"""移除的角色Id列表"""
	removeRoles:[String]
}
input RegisterCollectiveRegisterAdminRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.RegisterCollectiveRegisterAdminRequest") {
	"""【必填】姓名"""
	name:String!
	"""【必填】手机号码"""
	phone:String!
	"""【必填】用户输入的图片验证码"""
	captcha:String
	"""【必填】短信验证码"""
	smsCode:String!
	"""【必填】密码"""
	password:String!
	"""【必填】申请短信返回的token"""
	token:String!
}
"""创建企业账户请求信息
	<AUTHOR> 2022/6/27 11:26
"""
input RegisterEnterpriseAccountRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.RegisterEnterpriseAccountRequest") {
	"""单位名称"""
	name:String
	"""统一社会信用代码"""
	code:String
	"""注册地址"""
	registerAddress:String
	"""登录账号"""
	accountName:String
	"""单位姓名"""
	userName:String
	"""登录密码"""
	password:String
	"""手机号验证token"""
	token:String
}
"""删除内置角色请求
	<AUTHOR>
"""
input RemoveSystemInternalRoleRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.RemoveSystemInternalRoleRequest") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerId:String
	"""角色id"""
	ids:[String]
}
"""<AUTHOR>
	@description:重置Admin账户密码请求参数（跨域）
	@date 2024/12/23 16:17
"""
input ResetAdminPasswordRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.ResetAdminPasswordRequest") {
	"""【必填】帐户ID"""
	accountId:String
	"""【必填】前端传入的密码"""
	password:String
}
"""<AUTHOR> [2023/5/24 15:29]"""
input ResetPasswordRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.ResetPasswordRequest") {
	"""【必填】帐户ID"""
	accountId:String
	"""【必填】前端传入的密码"""
	password:String
}
"""@author: xucenhao
	@time: 2024-10-08
	@description: 安全的验证码申请请求
"""
input SecureCaptchaApplyRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.SecureCaptchaApplyRequest") {
	"""【必填】验证码业务Token"""
	token:String
	"""【必填】验证码业务类型
		@see ValidationBusinessTypes
	"""
	businessType:Int!
	"""【必填】验证码类型
		@see CaptchaTypeConstant
	"""
	captchaType:String
}
"""手机短信验证码申请信息
	<AUTHOR>
"""
input SmsCodeApplyRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.SmsCodeApplyRequest") {
	"""【必填】申请token"""
	token:String
	"""【必填】手机短信验证码业务类型
		@see ValidationBusinessTypes
	"""
	businessType:Int!
	"""手机号"""
	phone:String
	"""验证验证码数据"""
	verifyCaptchaData:ImageCaptchaTrack
}
"""手机短信验证码申请信息
	<AUTHOR>
"""
input SmsCodeForChangingPhoneApplyRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.SmsCodeForChangingPhoneApplyRequest") {
	"""【必填】申请token"""
	token:String
	"""【必填】手机短信验证码业务类型
		@see ValidationBusinessTypes
	"""
	businessType:Int!
}
"""手机短信验证码
	<AUTHOR>
"""
input SmsCodeValidRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.SmsCodeValidRequest") {
	"""【必填】申请手机短信验证码的Token"""
	token:String
	"""【必填】手机号"""
	phone:String
	"""【必填】手机短信验证码"""
	smsCode:String
}
"""解绑手机-给当前登陆用户
	<AUTHOR>
"""
input UnbindPhoneForCurrentUserRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.UnbindPhoneForCurrentUserRequest") {
	"""原手机号【必填】"""
	phone:String!
}
"""解绑手机
	<AUTHOR>
"""
input UnbindPhoneRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.UnbindPhoneRequest") {
	"""用户ID【必填】"""
	userId:String!
	"""原手机号【必填】"""
	phone:String!
}
"""<AUTHOR> 2022/6/16 16:37"""
input UnitCreateRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.UnitCreateRequest") {
	id:String
	"""单位名称"""
	name:String
	"""所辖地区"""
	theirArea:String
	"""用户姓名"""
	userName:String
	"""【必填】认证标识"""
	identity:String
	"""【必填】密码"""
	password:String
	"""授予角色id（企业账户）"""
	addEnterpriseRoleIds:[String]
	"""授予角色id（企业个人账户）"""
	addEnterprisePersonRoleIds:[String]
	"""下级单位"""
	subUnitInfoList:[UnitCreateRequest]
}
"""修改超级管理员账户请求信息
	<AUTHOR> 2022/7/18 11:26
"""
input UpdateAdministratorAccountRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.UpdateAdministratorAccountRequest") {
	"""账户id【必填】"""
	accountId:String
	"""管理员姓名 【必填】"""
	name:String
	"""登录账号"""
	accountName:String
	"""手机号"""
	phone:String
	"""新增角色列表"""
	addRoleIds:[String]
	"""删除角色列表"""
	removeRoleIds:[String]
	"""是否更新全部"""
	updateAll:Boolean!
}
input UpdateCollectiveRegisterAccountInfoRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.UpdateCollectiveRegisterAccountInfoRequest") {
	"""【必填】用户ID"""
	userId:String
	"""【必填】认证标识类型 1用户名，2手机，3身份证，4电子邮箱
		@see com.fjhb.domain.basicdata.api.account.consts.AuthenticationIdentityTypes
	"""
	identityType:Int!
	"""【必填】认证标识：帐号"""
	identity:String
}
input UpdateCollectiveRegisterRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.UpdateCollectiveRegisterRequest") {
	"""【必填】姓名"""
	name:String
}
"""修改超级管理员账户请求信息
	<AUTHOR> 2022/7/18 11:26
"""
input UpdateOnlineAdministratorAccountRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.UpdateOnlineAdministratorAccountRequest") {
	"""账户id【必填】"""
	accountId:String
	"""管理员姓名 【必填】"""
	name:String
	"""登录账号【必填】"""
	accountName:String
	"""服务商id【必填】"""
	serviceId:String
	"""手机号"""
	phone:String
	"""是否更新全部"""
	updateAll:Boolean!
}
"""修改角色请求
	<AUTHOR>
"""
input UpdateRoleByAdminTypeRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.UpdateRoleByAdminTypeRequest") {
	id:String
	"""名称"""
	name:String
	"""角色来源,来源与哪个角色"""
	source:String
	"""角色描述"""
	description:String
	"""功能权限id集合"""
	functionalAuthorityIds:[String]
}
"""修改角色请求
	<AUTHOR>
"""
input UpdateRoleRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.UpdateRoleRequest") {
	id:String
	"""名称"""
	name:String
	"""角色描述"""
	description:String
	"""功能权限id集合"""
	functionalAuthorityIds:[String]
}
"""更新服务提供商请求
	<AUTHOR> 2022/7/19
"""
input UpdateServiceProviderAccountRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.UpdateServiceProviderAccountRequest") {
	"""账号【必填】"""
	accountId:String
	"""姓名 【必填】"""
	name:String
	"""用户名【必填】"""
	userName:String
	"""手机号【必填】"""
	phone:String
	"""顶级账户Id 【超管修改时必填】"""
	rootAccountId:String
}
"""修改内置角色请求
	<AUTHOR>
"""
input UpdateSystemInternalRoleRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.UpdateSystemInternalRoleRequest") {
	id:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerId:String
	"""应用方类型（不传则取上下文数据）【创建内置角色需赋值】
		@see SystemMemberTypes
	"""
	applicationMemberType:Int
	"""应用方类别
		@see com.fjhb.domain.basicdata.api.consts.ApplicationMemberCategories
	"""
	applicationMemberCategory:Int!
	"""角色类别
		@see RoleCategories
	"""
	category:Int
	"""所属方类型（不传则取上下文数据）【创建内置角色需赋值】
		@see SystemMemberTypes
	"""
	ownerMemberType:Int
	"""所属方id（不传则取上下文数据）【创建内置角色需赋值】"""
	ownerMemberId:String
	"""名称"""
	name:String
	"""应用类型，标明当前角色用于什么客户域（人社/用工企业）
		@see BusinessDomainIdentity#applicationType
		@see UnitBusinessTypes
		@see SubProjectBusinessTypes
	"""
	applicationType:Int
	"""角色描述"""
	description:String
	"""角色code"""
	code:String
}
"""验证账号/手机号是否存在请求
	<AUTHOR>
"""
input ValidIdentityFxsRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.ValidIdentityFxsRequest") {
	"""姓名"""
	name:String
	"""【必填】账号/手机号"""
	identity:String
	"""【必填】图形验证码验证通过生成的token 或 验证身份的初始token"""
	token:String
	"""【必填】业务类型
		@see ValidationBusinessTypes
		忘记密码:30000
	"""
	businessType:Int!
}
"""验证账号/手机号是否存在请求
	<AUTHOR>
"""
input ValidIdentityRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.ValidIdentityRequest") {
	"""姓名"""
	name:String
	"""【必填】账号/手机号"""
	identity:String
	"""【必填】图形验证码验证通过生成的token 或 验证身份的初始token"""
	token:String
	"""【必填】业务类型
		@see ValidationBusinessTypes
		忘记密码:30000
	"""
	businessType:Int!
}
"""申请校验人脸核身认证V1
	<AUTHOR>
"""
input ApplyValidFacialRecognitionV1Request @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.account.ApplyValidFacialRecognitionV1Request") {
	"""登录路口标识token【必填】"""
	token:String!
	"""姓名【必填】"""
	name:String!
	"""身份证号【必填】"""
	idCardNumber:String!
}
"""申请校验人脸核身认证V1 Web端请求
	<AUTHOR>
"""
input ApplyValidFacialRecognitionV1WebRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.account.ApplyValidFacialRecognitionV1WebRequest") {
	"""登录路口标识token【必填】"""
	token:String!
	"""登陆随机码唯一标识【必填】"""
	loginRandomCode:String!
	"""姓名【必填】"""
	name:String!
	"""身份证号【必填】"""
	idCardNumber:String!
}
"""申请校验人脸核身认证结果请求
	<AUTHOR>
"""
input ApplyValidFacialRecognitionVerifyResultExtendRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.account.ApplyValidFacialRecognitionVerifyResultExtendRequest") {
	"""登录路口标识token【必填】"""
	token:String!
	"""姓名【必填】"""
	name:String!
	"""身份证号【必填】"""
	idCardNumber:String!
	"""本次认证结果凭据【选填】，若填写则会进行人脸二次核验"""
	verifyResult:String
	"""openId/unionId"""
	openId:String!
}
"""申请校验人脸核身认证结果请求
	<AUTHOR>
"""
input ApplyValidFacialRecognitionVerifyResultRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.account.ApplyValidFacialRecognitionVerifyResultRequest") {
	"""登录路口标识token【必填】"""
	token:String!
	"""姓名【必填】"""
	name:String!
	"""身份证号【必填】"""
	idCardNumber:String!
	"""本次认证结果凭据，第三方可以选择根据这个凭据获取相关信息【必填】"""
	verifyResult:String!
}
"""@author: linxiquan
	@Date: 2023/7/10 17:52
	@Description: 求职者请求
"""
input JobSeekerBindPlatformAccountRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.jobseeker.JobSeekerBindPlatformAccountRequest") {
	"""微信开放平台  openId  【必填】"""
	openId:String
	"""昵称 nickname 【必填】"""
	nickname:String
	"""是否换绑  为 true 则走换绑  false 则走绑定"""
	changeBind:Boolean
}
"""创建网校子管理员请求
	<AUTHOR>
"""
input CreateOnlineSchoolSubAdminByTokenRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.onlineschool.CreateOnlineSchoolSubAdminByTokenRequest") {
	"""初始token【必传】"""
	token:String!
	"""登录账户【必填】"""
	identity:String!
	"""姓名【必填】"""
	name:String!
	"""性别  女: 0 男:1【必填】"""
	gender:Int!
	"""手机"""
	phone:String
	"""邮箱"""
	email:String
	"""启用状态  正常: 1 禁用: 2【必填】"""
	status:Int!
	"""密码【必填】"""
	password:String!
	"""添加的角色id集合【必填】"""
	addRoleIds:[String]!
}
"""创建网校子管理员请求
	<AUTHOR>
"""
input CreateOnlineSchoolSubAdminRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.onlineschool.CreateOnlineSchoolSubAdminRequest") {
	"""登录账户【必填】"""
	identity:String!
	"""姓名【必填】"""
	name:String!
	"""性别  女: 0 男:1【必填】"""
	gender:Int!
	"""手机"""
	phone:String
	"""邮箱"""
	email:String
	"""启用状态  正常: 1 禁用: 2【必填】"""
	status:Int!
	"""密码【必填】"""
	password:String!
	"""添加的角色id集合【必填】"""
	addRoleIds:[String]!
}
"""更新网校子管理员请求
	<AUTHOR>
"""
input UpdateOnlineSchoolSubAdminByTokenRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.onlineschool.UpdateOnlineSchoolSubAdminByTokenRequest") {
	"""初始token【必传】"""
	token:String!
	"""被修改的管理员账户ID【必填】"""
	accountId:String!
	"""登录账户【为null，表示不更新】"""
	identity:String
	"""姓名【为null，表示不更新】"""
	name:String
	"""性别【为null，表示不更新】"""
	gender:Int
	"""手机【为null，表示不更新】"""
	phone:String
	"""邮箱【为null，表示不更新】"""
	email:String
	"""启用状态  正常: 1 禁用: 2【必填】"""
	status:Int!
	"""添加的角色id集合"""
	addRoleIds:[String]
	"""移除的角色id集合"""
	removeRoleIds:[String]
}
"""更新网校子管理员请求
	<AUTHOR>
"""
input UpdateOnlineSchoolSubAdminRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.onlineschool.UpdateOnlineSchoolSubAdminRequest") {
	"""被修改的管理员账户ID【必填】"""
	accountId:String!
	"""登录账户【为null，表示不更新】"""
	identity:String
	"""姓名【为null，表示不更新】"""
	name:String
	"""性别【为null，表示不更新】"""
	gender:Int
	"""手机【为null，表示不更新】"""
	phone:String
	"""邮箱【为null，表示不更新】"""
	email:String
	"""启用状态  正常: 1 禁用: 2【必填】"""
	status:Int!
	"""添加的角色id集合"""
	addRoleIds:[String]
	"""移除的角色id集合"""
	removeRoleIds:[String]
}
"""申请绑定微信开放平台并返回账户ID登陆token
	<AUTHOR>
"""
input ApplyBindAndLoginRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.openplatform.wechat.ApplyBindAndLoginRequest") {
	"""初始token【必填】"""
	token:String!
	"""角色类别
		@see com.fjhb.domain.basicdata.api.role.consts.RoleCategories
		1:学员
	"""
	roleCategory:Int!
	"""登陆账号【必填】"""
	account:String!
	"""登陆密码【必填】"""
	password:String!
	"""OpenId【必填】"""
	openId:String!
	"""微信用户nickname"""
	nickname:String
}
input ApplyBindWeChatOpenPlatformLoginAccountRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.openplatform.wechat.ApplyBindWeChatOpenPlatformLoginAccountRequest") {
	"""【必填】token(初始化token)"""
	token:String!
	"""unionId【必填】"""
	unionId:String!
	"""微信用户nickname"""
	nickname:String
	"""【必填】是否换绑  为 true 则走换绑  false 则走绑定"""
	changeBind:Boolean!
}
"""申请登陆微信开放平台
	<AUTHOR>
"""
input ApplyLoginByOpenIdRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.openplatform.wechat.ApplyLoginByOpenIdRequest") {
	"""初始token【必填】"""
	token:String!
	"""角色类别
		@see com.fjhb.domain.basicdata.api.role.consts.RoleCategories
		1:学员
	"""
	roleCategory:Int!
	"""OpenId【必填】"""
	openId:String!
}
"""<AUTHOR>
input ApplyOpenIdRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.openplatform.wechat.ApplyOpenIdRequest") {
	"""授权临时票据code【必填】"""
	code:String!
	"""服务商ID【必填】"""
	servicerId:String!
}
"""@author: linxiquan
	@Date: 2024/6/18 11:58
	@Description: 当前登录账户绑定微信开放平台
"""
input BindLoginAccountOpenPlatformRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.openplatform.wechat.BindLoginAccountOpenPlatformRequest") {
	"""【必填】token(初始化token)"""
	token:String!
	"""【必填】单点登录Token"""
	loginToken:String!
	"""【必填】是否换绑  为 true 则走换绑  false 则走绑定"""
	changeBind:Boolean!
}
"""校验是否绑定微信开放平台
	<AUTHOR>
"""
input ValidIsBindWeChatOpenPlatformRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.openplatform.wechat.ValidIsBindWeChatOpenPlatformRequest") {
	"""初始token【必填】"""
	token:String!
	"""角色类别
		@see com.fjhb.domain.basicdata.api.role.consts.RoleCategories
		1:学员
	"""
	roleCategory:Int!
	"""OpenId【必填】"""
	openId:String!
}
"""绑定平台账号【微信】的请求
	<AUTHOR>
"""
input BindPlatformAccountRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.student.BindPlatformAccountRequest") {
	"""【必填】认证标识(证件号或手机号)"""
	identity:String!
	"""【必填】密码"""
	password:String!
	"""用户输入的图片验证码"""
	captcha:String
	"""token(图片验证码校验成功生成的)"""
	token:String
	"""【必填】单点登录Token"""
	loginToken:String!
	"""验证验证码数据"""
	verifyCaptchaData:ImageCaptchaTrack
}
input CreateOnlineSchoolStudentRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.student.CreateOnlineSchoolStudentRequest") {
	"""【必填】证件号码"""
	idCard:String!
	"""【必填】证件类型"""
	idCardType:Int!
	"""【必填】密码"""
	password:String!
	"""【必填】短信验证码"""
	smsCode:String!
	"""【必填】用户输入的图片验证码"""
	captcha:String!
	"""【必填】申请短信返回的token"""
	token:String!
	"""单点登录Token"""
	loginToken:String
	"""网校服务商id"""
	onlineSchoolServicerId:String
	"""【必填】用户名称"""
	name:String
	"""性别
		@see com.fjhb.domain.basicdata.api.user.consts.Genders
	"""
	gender:Int
	"""【必填】手机号"""
	phone:String
	"""【必填】所属区域"""
	area:String
	"""工作单位"""
	companyName:String
	"""统一社会信用码"""
	companyCode:String
	"""[必填]加密值"""
	encrypt:String
	"""行业信息"""
	userIndustryInfos:[CreateUserIndustryRequest]
	"""工作单位所在地区"""
	companyRegionCode:String
	"""【必填-前端校验】邮箱地址"""
	email:String
}
"""@author: linxiquan
	@Date: 2023/6/8 10:41
	@Description:新增证书信息
"""
input CreateStudentCertificateRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.student.CreateStudentCertificateRequest") {
	"""【必填】用户id"""
	userId:String
	"""【必填】行业id"""
	industryId:String
	"""【必填】证书编号"""
	certificateNo:String
	"""【必填】证书类别"""
	certificateCategory:String
	"""【必填】注册专业"""
	registerProfessional:String
	"""【必填】发证日期（起）"""
	releaseStartTime:DateTime
	"""【必填】证书有效期（止）"""
	certificateEndTime:DateTime
	"""【必填】证书附件"""
	certificateAttachments:[CertificateAttachment]
}
"""<AUTHOR>
input CreateStudentRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.student.CreateStudentRequest") {
	"""【必填】证件号码"""
	idCard:String!
	"""【必填】证件类型"""
	idCardType:Int!
	"""【必填】密码"""
	password:String!
	"""【必填】短信验证码"""
	smsCode:String!
	"""【必填】用户输入的图片验证码"""
	captcha:String!
	"""【必填】申请短信返回的token"""
	token:String!
	"""单点登录Token"""
	loginToken:String
	"""【必填】用户名称"""
	name:String
	"""性别
		@see com.fjhb.domain.basicdata.api.user.consts.Genders
	"""
	gender:Int
	"""【必填】手机号"""
	phone:String
	"""【必填】所属区域"""
	area:String
	"""工作单位"""
	companyName:String
	"""统一社会信用码"""
	companyCode:String
	"""[必填]加密值"""
	encrypt:String
	"""行业信息"""
	userIndustryInfos:[CreateUserIndustryRequest]
	"""工作单位所在地区"""
	companyRegionCode:String
	"""【必填-前端校验】邮箱地址"""
	email:String
}
"""@author: zhengp 2022/1/24 15:27"""
input CreateUserIndustryRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.student.CreateUserIndustryRequest") {
	"""所属行业"""
	industryId:String
	"""一级专业类别"""
	firstProfessionalCategory:String
	"""二级专业类别"""
	secondProfessionalCategory:String
	"""职称等级"""
	professionalQualification:String
	"""证书信息"""
	certificateInfos:[CertificateInfo]
	"""人员类别（职业卫生行业）"""
	personnelCategory:String
	"""岗位类别（职业卫生行业）"""
	positionCategory:String
	"""技术等级（工勤行业）"""
	professionalLevel:String
	"""工种（工勤行业）"""
	jobCategoryId:String
	"""教师行业 学段、学科信息 ，例：[{"学段1":"学科1"}, {"学段1":"学科2"}]"""
	sectionAndSubjects:[SectionAndSubjects]
	"""证书类型（药师行业）"""
	certificatesType:String
	"""执证类别（药师行业）"""
	practitionerCategory:String
}
input CertificateAttachment @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.student.CreateUserIndustryRequest$CertificateAttachment") {
	certificateUrl:String
	name:String
}
input CertificateInfo @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.student.CreateUserIndustryRequest$CertificateInfo") {
	"""更新时必填"""
	certificateId:String
	"""证书编号"""
	certificateNo:String
	"""证书类别"""
	certificateCategory:String
	"""注册专业"""
	registerProfessional:String
	"""发证日期（起）"""
	releaseStartTime:DateTime
	"""证书有效期（止）"""
	certificateEndTime:DateTime
	"""证书附件"""
	certificateAttachments:[CertificateAttachment]
}
"""@author: linxiquan
	@Date: 2023/6/8 10:46
	@Description:删除证书信息
"""
input DeleteStudentCertificateRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.student.DeleteStudentCertificateRequest") {
	"""【必填】证书id"""
	certificateId:String
	"""【必填】用户id"""
	userId:String
	"""【必填】行业id"""
	industryId:String
}
input UpdateStudentBasicInfoRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.student.UpdateStudentBasicInfoRequest") {
	"""姓名"""
	name:String
	"""性别"""
	gender:Int
}
"""@author: linxiquan
	@Date: 2023/6/8 10:46
	@Description:更新证书信息
"""
input UpdateStudentCertificateRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.student.UpdateStudentCertificateRequest") {
	"""【必填】证书id"""
	certificateId:String
	"""【必填】用户id"""
	userId:String
	"""【必填】行业id"""
	industryId:String
	"""【必填】证书编号"""
	certificateNo:String
	"""【必填】证书类别"""
	certificateCategory:String
	"""【必填】注册专业"""
	registerProfessional:String
	"""【必填】发证日期（起）"""
	releaseStartTime:DateTime
	"""【必填】证书有效期（止）"""
	certificateEndTime:DateTime
	"""【必填】证书附件"""
	certificateAttachments:[CertificateAttachment]
}
"""<AUTHOR>
input UpdateStudentRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.student.UpdateStudentRequest") {
	"""证件类别"""
	idCardType:Int
	"""证件号码"""
	idCard:String
	"""短信验证码"""
	smsCode:String
	"""用户输入的图片验证码"""
	captcha:String
	"""申请短信返回的token"""
	token:String
	"""【必填】默认为普通更新false，强制更新true则会携带短信验证码和token"""
	forcedUpdate:Boolean
	"""【必填】用户名称"""
	name:String
	"""性别
		@see com.fjhb.domain.basicdata.api.user.consts.Genders
	"""
	gender:Int
	"""【必填】手机号"""
	phone:String
	"""【必填】所属区域"""
	area:String
	"""工作单位"""
	companyName:String
	"""统一社会信用码"""
	companyCode:String
	"""[必填]加密值"""
	encrypt:String
	"""行业信息"""
	userIndustryInfos:[CreateUserIndustryRequest]
	"""工作单位所在地区"""
	companyRegionCode:String
	"""【必填-前端校验】邮箱地址"""
	email:String
}
"""<AUTHOR>
input UpdateStudentSystemRequest @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.student.UpdateStudentSystemRequest") {
	"""用户id"""
	userId:String
	"""【必填】证件号码"""
	idCard:String
	"""【必填】证件类别"""
	idCardType:Int
	"""【必填】用户名称"""
	name:String
	"""性别
		@see com.fjhb.domain.basicdata.api.user.consts.Genders
	"""
	gender:Int
	"""【必填】手机号"""
	phone:String
	"""【必填】所属区域"""
	area:String
	"""工作单位"""
	companyName:String
	"""统一社会信用码"""
	companyCode:String
	"""[必填]加密值"""
	encrypt:String
	"""行业信息"""
	userIndustryInfos:[CreateUserIndustryRequest]
	"""工作单位所在地区"""
	companyRegionCode:String
	"""【必填-前端校验】邮箱地址"""
	email:String
}
"""@author: xucenhao
	@time: 2024-10-08
	@description:
"""
type ImageCaptchaVO @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.dto.ImageCaptchaVO") {
	"""验证码类型."""
	type:String
	"""背景图."""
	backgroundImage:String
	"""移动图."""
	templateImage:String
	"""背景图片所属标签."""
	backgroundImageTag:String
	"""模板图片所属标签."""
	templateImageTag:String
	"""背景图片宽度."""
	backgroundImageWidth:Int
	"""背景图片高度."""
	backgroundImageHeight:Int
	"""滑动图片宽度."""
	templateImageWidth:Int
	"""滑动图片高度."""
	templateImageHeight:Int
}
"""<AUTHOR>
type ApplyOpenIdResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request.openplatform.wechat.ApplyOpenIdResponse") {
	"""code为200时，openId才有值"""
	openId:String
	"""昵称"""
	nickname:String
	unionId:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""图形验证码响应信息
	<AUTHOR>
"""
type CaptchaResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.CaptchaResponse") {
	"""图形验证码"""
	captcha:String
	"""请求结果状态码"""
	code:Int!
	"""token"""
	token:String
	"""响应消息"""
	message:String
}
"""切换企业token响应信息
	<AUTHOR>
"""
type ChangeEnterpriseTokenResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.ChangeEnterpriseTokenResponse") {
	"""企业单位ID"""
	unitId:String
	"""企业名称"""
	name:String
	"""企业统一社会信用代码"""
	code:String
	"""是否已认证"""
	identityVerification:Boolean!
	"""管理员与企业关系状态 1为正常可用  0为冻结不可用
		@see PersonUnitRelationshipStatus
	"""
	status:Int!
	"""切换企业上下文的token"""
	token:String
}
"""切换企业管理员单位token响应信息
	<AUTHOR>
"""
type ChangePolicyActorUnitAuthorizeTokenResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.ChangePolicyActorUnitAuthorizeTokenResponse") {
	changeTokenInfos:[ChangeEnterpriseAdminUnitInfo]
	"""请求结果状态码"""
	code:Int!
	"""token"""
	token:String
	"""响应消息"""
	message:String
}
type ChangeEnterpriseAdminUnitInfo @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.ChangePolicyActorUnitAuthorizeTokenResponse$ChangeEnterpriseAdminUnitInfo") {
	"""单位ID"""
	unitId:String
	"""单位名称"""
	unitName:String
	"""统一社会信用代码"""
	unitCode:String
	"""单位状态 1正常,2冻结
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitStatus
	"""
	unitStatus:Int!
	"""管理员与单位关系状态 1为正常可用  0为冻结不可用
		@see com.fjhb.domain.basicdata.api.unit.consts.PersonUnitRelationshipStatus
	"""
	personUnitRelationStatus:Int!
	"""账户状态"""
	accountStatus:Int!
	"""账户类型"""
	accountType:Int!
	"""切换单位上下文的token(认证授权用)"""
	token:String
}
"""企业账户注册信息
	<AUTHOR>
"""
type EnterpriseAccountRegisterResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.EnterpriseAccountRegisterResponse") {
	"""请求结果状态码"""
	code:Int!
	"""token"""
	token:String
	"""响应消息"""
	message:String
}
"""用工企业经办注册信息
	<AUTHOR>
"""
type EnterpriseManagerRegisterResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.EnterpriseManagerRegisterResponse") {
	"""第三方原数据"""
	metadata:String
	"""请求结果状态码"""
	code:Int!
	"""token"""
	token:String
	"""响应消息"""
	message:String
}
"""申请企业元数据响应
	<AUTHOR>
"""
type EnterpriseMetadataApplyResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.EnterpriseMetadataApplyResponse") {
	"""企业名称"""
	name:String
	"""企业统一社会信用代码"""
	code:String
}
"""@BelongsProject: fjhb-microservice-basicdata-domain-gateway
	@BelongsPackage: com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response
	@Author: xucenhao
	@CreateTime: 2024-06-13  15:12
	@Description:
"""
type ExistUserResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.ExistUserResponse") {
	code:Int!
	message:String
	userId:String
	accountId:String
}
type FindFunctionalAuthorityByRoleIdNewResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.FindFunctionalAuthorityByRoleIdNewResponse") {
	"""安全对象模型列表"""
	securityObjectResponses:[SecurityObjectNewResponse]
}
type FindLoginUserOpenIdResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.FindLoginUserOpenIdResponse") {
	openId:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""<AUTHOR>
type GenernalResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.GenernalResponse") {
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""<AUTHOR>
type GetCurrentUserServicerResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.GetCurrentUserServicerResponse") {
	"""当前登录用户可用的被授权的服务商id列表"""
	servicerIdList:[String]
}
"""验证账号/手机号是否存在响应
	<AUTHOR>
"""
type IdentityValidResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.IdentityValidResponse") {
	"""当前账户已绑定的数据脱敏的手机号"""
	boundDesensitizationPhone:String
	"""请求结果状态码"""
	code:Int!
	"""token"""
	token:String
	"""响应消息"""
	message:String
}
type RegisterStudentResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.RegisterStudentResponse") {
	"""账户id"""
	accountId:String
	"""用户id"""
	userId:String
	"""请求结果状态码"""
	code:Int!
	"""token"""
	token:String
	"""响应消息"""
	message:String
}
"""角色响应类
	<AUTHOR>
"""
type RoleResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.RoleResponse") {
	id:String
	"""名称"""
	name:String
	"""应用方类型
		4-子项目  5-单位  6-服务商
	"""
	applicationMemberType:Int!
	"""角色类别
		1-学员  2-集体报名管理员  3-管理员  4-人设管理员  5-企业管理员  6-人社审批管理员  7-企业经办  8-企业法人  9-企业超管  10-人社超管
		11-人社职建处  12-人社就业局  13-企业超管  14-合同供应商  15-专家
	"""
	category:Int
	"""所属方类型
		4-子项目  5-单位  6-服务商
	"""
	ownerMemberType:Int
	"""所属方id"""
	ownerMemberId:String
	"""所属角色名称"""
	belongRoleName:String
	"""角色性质
		1-系统内置  2-自定义
	"""
	nature:Int!
	"""角色描述"""
	description:String
	"""角色code"""
	code:String
	"""创建时间"""
	createdTime:DateTime
	"""修改时间"""
	updatedTime:DateTime
}
"""角色响应类
	<AUTHOR>
"""
type RoleResponseForPersonAccount @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.RoleResponseForPersonAccount") {
	id:String
	"""名称"""
	name:String
	"""应用方类型
		4-子项目  5-单位  6-服务商
	"""
	applicationMemberType:Int!
	"""应用方id"""
	applicationMemberId:String
	"""角色类别
		1-学员  2-集体报名管理员  3-管理员  4-人设管理员  5-企业管理员  6-人社审批管理员  7-企业经办  8-企业法人  9-企业超管  10-人社超管
		11-人社职建处  12-人社就业局  13-企业超管  14-合同供应商  15-专家
	"""
	category:Int
	"""所属方类型
		4-子项目  5-单位  6-服务商
	"""
	ownerMemberType:Int
	"""所属方id"""
	ownerMemberId:String
	"""角色性质
		1-系统内置  2-自定义
	"""
	nature:Int!
	"""角色描述"""
	description:String
	"""角色code"""
	code:String
	"""创建时间"""
	createdTime:DateTime
	"""修改时间"""
	updatedTime:DateTime
}
"""角色信息对应账户数量响应
	<AUTHOR>
"""
type RoleToAccountCountResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.RoleToAccountCountResponse") {
	"""角色信息"""
	roleResponse:RoleResponse
	"""账户数量"""
	accountCount:Int!
}
"""菜单响应类
	<AUTHOR>
"""
type RoleToFunctionalAuthorityNewResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.RoleToFunctionalAuthorityNewResponse") {
	"""角色id"""
	roleId:String
	"""功能权限对象集合"""
	functionalAuthorityList:[SecurityObjectNewResponse]
}
"""菜单响应类
	<AUTHOR>
"""
type RoleToFunctionalAuthorityResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.RoleToFunctionalAuthorityResponse") {
	"""角色id"""
	roleId:String
	"""功能权限对象集合"""
	functionalAuthorityList:[SecurityObjectResponse]
}
"""安全性高的验证码响应信息
	<AUTHOR>
"""
type SecureCaptchaResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.SecureCaptchaResponse") {
	"""token"""
	token:String
	"""验证码展示数据"""
	displayCaptchaData:ImageCaptchaVO
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""安全对象模型
	<AUTHOR>
"""
type SecurityObjectNewResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.SecurityObjectNewResponse") {
	"""安全对象ID"""
	id:String
	"""安全对象授权id【定制角色才有值】
		当前角色为定制角色|勾选定制角色创建的自定义角色 返回授权ID
	"""
	securityAuthorizationId:String
	"""能力服务版本id【弃用】"""
	avmId:String
	"""安全对象类型
		0-所有  1-url  2-方法  3-安全对象组
	"""
	type:String
	"""是否为菜单"""
	isMenu:Boolean
	"""安全对象名称"""
	name:String
	"""安全对象名称 用于UI显示的名称"""
	viewName:String
	"""URL内容"""
	urlContent:String
	"""URL唯一标识"""
	urlMark:String
	"""安全对象内容"""
	content:String
	"""安全对象内容的CRC32"""
	contentCRC32:Long!
	"""安全对象描述"""
	description:String
	"""安全对象扩展信息"""
	ext:String
	"""父级安全对象Id"""
	parentId:String
	"""业务对象ID"""
	objectId:String
	"""排序号"""
	sortNo:Int
	"""创建人员ID"""
	creatorId:String
	"""创建时间"""
	createTime:DateTime
	"""安全对象动作类型"""
	actionType:Int
}
"""安全对象模型
	<AUTHOR>
"""
type SecurityObjectResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.SecurityObjectResponse") {
	"""安全对象ID"""
	id:String
	"""能力服务版本id【弃用】"""
	avmId:String
	"""安全对象类型
		0-所有  1-url  2-方法  3-安全对象组
	"""
	type:String
	"""是否为菜单"""
	isMenu:Boolean
	"""安全对象名称"""
	name:String
	"""安全对象名称 用于UI显示的名称"""
	viewName:String
	"""URL内容"""
	urlContent:String
	"""URL唯一标识"""
	urlMark:String
	"""安全对象内容"""
	content:String
	"""安全对象内容的CRC32"""
	contentCRC32:Long!
	"""安全对象描述"""
	description:String
	"""安全对象扩展信息"""
	ext:String
	"""父级安全对象Id"""
	parentId:String
	"""业务对象ID"""
	objectId:String
	"""排序号"""
	sortNo:Int
	"""创建人员ID"""
	creatorId:String
	"""创建时间"""
	createTime:DateTime
	"""安全对象动作类型"""
	actionType:Int
}
"""<AUTHOR>
type TokenExtendResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.TokenExtendResponse") {
	token:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""Token 响应对象
	<AUTHOR>
"""
type TokenResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.TokenResponse") {
	"""请求结果状态码"""
	code:Int!
	"""token"""
	token:String
	"""响应消息"""
	message:String
}
type ApplyBindWeChatOpenPlatformLoginAccountResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.openplatform.ApplyBindWeChatOpenPlatformLoginAccountResponse") {
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
type BindLoginAccountOpenPlatformResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.openplatform.BindLoginAccountOpenPlatformResponse") {
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""校验是否啊绑定开放平台响应
	<AUTHOR>
"""
type ValidIsBindOpenPlatformResponse @type(value:"com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response.openplatform.ValidIsBindOpenPlatformResponse") {
	"""是否绑定"""
	isBind:Boolean!
	"""所属用户ID"""
	userId:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}

scalar List
