import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'
import CheckType, { CheckTypeEnum } from '@api/service/management/train-class/mutation/Enum/CheckTypeEnum'
import AnswerType, { AnswerTypeEnum } from '@api/service/management/train-class/mutation/Enum/AnswerTypeEnum'
import ExperienceType, {
  LearningExperienceEnum
} from '@api/service/management/train-class/mutation/Enum/LearningExperienceEnum'
import JoinTimeType, { JoinTimeTypeEnum } from '@api/service/management/train-class/mutation/Enum/JoinTimeTypeEnum'
import TrainingOutlineCourse from '@api/service/customer/course/query/vo/TrainingOutlineCourse'
import ExperienceCourseItem from '@api/service/management/train-class/mutation/vo/ExperienceCourseItem'
import Classification from '@api/service/management/train-class/mutation/vo/Classification'
import Mockjs from 'mockjs'
import { Page } from '@hbfe/common'

import MsCourseLearningQueryFrontGatewayCourseLearningBackstage, {
  CourseInfoRequest,
  CourseInPackageRequest,
  CourseInSchemeRequest,
  CourseOfCourseTrainingOutlineRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { OperationEnum } from '@api/service/management/train-class/mutation/Enum/OperationEnum'

/**
 * 学习心得模型
 */
class ExperienceItem {
  /**
   * 心得id
   */
  id = ''
  /**
   * 默认id前缀
   */
  static experienceIdPre = 'experienceIdPre'
  /**
   * 主题
   */
  theme = ''
  /**
   * 是否必选
   */
  isRequired = false
  /**
   * 参加时间类型
   */
  joinTimeType: JoinTimeType = new JoinTimeType(JoinTimeTypeEnum.class_time)
  /**
   * 学习心得内容
   */
  content = ''
  /**
   * 参加时间
   */
  joinTime: string[] = []
  /**
   * 心得类型
   */
  experienceType: ExperienceType = new ExperienceType(LearningExperienceEnum.class_experience)
  /**
   * 作答形式
   */
  answerType: AnswerType = new AnswerType(AnswerTypeEnum.attachments)
  /**
   * 审核方式
   */
  checkType: CheckType = new CheckType(CheckTypeEnum.auto)
  /**
   * 分数
   */
  score = 100
  /**
   * 提交文件大小/字数
   */
  submitLimitNum = 0
  /**
   * 提交次数类型 true限制次数 false不限次数
   */
  submitCountType = false
  /**
   * 提交次数/未通过作答次数
   */
  submitCount = 0
  /**
   * 指定课程
   */
  courseList: ExperienceCourseItem[] = []
  /**
   * 操作类型1创建2修改3删除
   */
  operation?: OperationEnum = OperationEnum.CREATE
  /**
   * 课程内容及课程信息是否已回显
   */
  isInfoShow = true

  /**
   * 是否查到课程
   */
  hasCourse = true

  constructor(id = `${ExperienceItem.experienceIdPre}${Mockjs.Random.guid()}`, isInfoShow = true) {
    // id占位符
    this.id = id
    // 默认已有
    this.isInfoShow = isInfoShow
  }

  /**
   * 选择数据/取消选择
   */
  selectExperience() {
    this.isRequired = !this.isRequired
  }

  /**
   * 添加课程
   */
  addCourse(course: CourseInCoursePackage | TrainingOutlineCourse, classification: Classification) {
    // 当前只一个新的只允许添加一个课程，添加前清空
    this.courseList = new Array<ExperienceCourseItem>()
    const courseItem = new ExperienceCourseItem()
    courseItem.courseId = course.id
    courseItem.name = course.name
    courseItem.period = course.period
    courseItem.coursePackageId = classification.coursePackageId
    courseItem.outlineId = classification.id
    this.courseList.push(courseItem)
    this.hasCourse = true
  }

  /**
   * 移除课程
   */
  removeCourse(courseId: string) {
    const courseIndex = this.courseList.findIndex(item => item.courseId === courseId)
    courseIndex > -1 && this.courseList.splice(courseIndex, 1)
  }

  /**
   * 打开弹窗填充课程数据
   */
  async getCourseInfo() {
    if (this.experienceType.equal(LearningExperienceEnum.course_experience)) {
      if (this.id.includes(ExperienceItem.experienceIdPre)) {
        const request = new CourseInPackageRequest()
        request.pageNo = 1
        request.pageSize = 1
        request.coursePackageId = this.courseList[0].coursePackageId
        request.courseIds = [this.courseList[0].courseId]
        const res = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInPackageV2InServicer(
          request
        )
        if (res.status.isSuccess() && res.data.currentPageData.length) {
          this.courseList[0].name = res.data.currentPageData[0].course.courseName
          this.courseList[0].period = res.data.currentPageData[0].courseInPackage.period
        } else {
          this.hasCourse = false
        }
        return res.status
      } else {
        const request = new CourseInSchemeRequest()
        const page = new Page(1, 1)
        request.course = new CourseInfoRequest()
        request.course.courseId = this.courseList[0].courseId
        request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
        request.courseOfCourseTrainingOutline.outlineIds = [this.courseList[0].outlineId]
        const res = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInSchemeInServicer({
          page,
          request
        })
        if (res.status.isSuccess() && res.data.currentPageData.length) {
          this.courseList[0].name = res.data.currentPageData[0].course.courseName
          this.courseList[0].period = res.data.currentPageData[0].courseOfCourseTrainingOutline.period
        } else {
          this.hasCourse = false
        }
        return res.status
      }
    }
  }
}

export default ExperienceItem
