<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--当前方案所属的专题-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20 f-mb20">当前方案所属的专题</el-button>
        <el-drawer
          title="当前方案所属的专题"
          :visible.sync="dialog4"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table" max-height="600px">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="专题名称" min-width="240" fixed="left">
                <template>专题名称专题名称专题名称专题名称</template>
              </el-table-column>
              <el-table-column label="操作" width="150" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">移除</el-button>
                  <el-button type="text" size="mini">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="f-mt10">
              <!--分页-->
              <el-pagination
                background
                class="f-tc"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确 定</el-button>
          </div>
        </el-drawer>
        <!--停用提示-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20 f-mb20">停用提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog1" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">停用后该专题将不展示在网校门户，对应的推广链接和二维码将自动失效，是否确认停用？</span>
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确 定</el-button>
          </div>
        </el-dialog>
        <!--启用提示-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20 f-mb20">启用提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog2" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">启用后该专题将展示在网校门户，对应的推广链接和二维码将可正常使用，是否确认启用？</span>
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确 定</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog3 = true" type="primary" class="f-mr20 f-mb20">已配置方案数弹窗</el-button>
        <el-drawer
          title="当前专题选中的培训方案"
          :visible.sync="dialog3"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table" max-height="600px">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="培训方案名称" min-width="240" fixed="left">
                <template>培训方案名称</template>
              </el-table-column>
              <el-table-column label="方案属性" min-width="240">
                <template>
                  <div>行业：建设行业</div>
                  <div>地区：福建省/福州市/鼓楼区</div>
                  <div>科目类型：科目类型1</div>
                  <div>培训类别：科目类型1</div>
                  <div>培训专业：科目类型1</div>
                  <div>培训年度：2023年</div>
                </template>
              </el-table-column>
            </el-table>
            <div class="f-mt10">
              <!--分页-->
              <el-pagination
                background
                class="f-tc"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确 定</el-button>
          </div>
        </el-drawer>
        <el-button @click="dialog5 = true" type="primary" class="f-mr20 f-mb20">web端访问专题弹窗</el-button>
        <el-dialog title="web端访问专题" :visible.sync="dialog5" width="400px" class="m-dialog">
          <div class="dialog-alert">
            https://zhongzhi.btpxv2.test1.59iedu.com:9443/unit/2ca40662786c0db601786d76445202d9?channelVendorId=2ca4ef5089b0612f0189cf8b541d0018
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">复 制</el-button>
          </div>
        </el-dialog>
        <!--查看期别-->
        <el-button @click="dialog7 = true" type="primary" class="f-mr20 f-mb20">查看期别</el-button>
        <el-drawer title="查看期别" :visible.sync="dialog7" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <!--条件查询-->
            <el-row :gutter="0" class="m-query">
              <el-form :inline="true" label-width="auto">
                <el-col :span="10">
                  <el-form-item label="期别名称">
                    <el-input v-model="input" clearable placeholder="请输入期别名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="报名时间">
                    <el-date-picker
                      v-model="value1"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table" max-height="600px">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="期别信息" min-width="240">
                <template>期别名称/编号</template>
              </el-table-column>
              <el-table-column label="开放报名时间" min-width="240">
                <template>
                  <p><el-tag type="info" size="mini">开始</el-tag>2025-02-01 10:00:00</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>2025-03-01 18:00:00</p>
                </template>
              </el-table-column>
              <el-table-column label="剩余报名人数" min-width="180">
                <template>50</template>
              </el-table-column>
            </el-table>
            <div class="f-mt10">
              <!--分页-->
              <el-pagination
                background
                class="f-tc"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确 定</el-button>
          </div>
        </el-drawer>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <!--web添加轮播图-->
        <el-button @click="dialog6 = true" type="primary" class="f-mr20">生成海报</el-button>
        <el-drawer
          title="读取专题名称"
          :visible.sync="dialog6"
          :direction="direction"
          size="700px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-poster">
              <div class="poster-tab">
                <div class="item current"><img src="./assets/images/poster-thumb-01.jpg" /></div>
                <div class="item"><img src="./assets/images/poster-thumb-02.jpg" /></div>
                <div class="item"><img src="./assets/images/poster-thumb-03.jpg" /></div>
              </div>
              <div class="poster-cont">
                <!--图片下载后尺寸为 1040 * 1477px-->
                <!--模板1 - 默认样式-->
                <div class="poster-box">
                  <div class="poster poster-01">
                    <div class="name">福建华博教育科技股份有限公司</div>
                    <div class="tel">
                      <img class="tel-icon" src="./assets/images/poster-01-tel.png" />
                      <div class="cont">
                        <p>客服热线：</p>
                        <div class="tel-num">4000-968823</div>
                      </div>
                    </div>
                    <div class="qrcode-box">
                      <div class="item">
                        <div class="code"><img src="./assets/images/qr-code.png" /></div>
                        <div class="txt">扫码立即访问</div>
                      </div>
                    </div>
                  </div>
                </div>
                <!--模板1 - 编辑样式-->
                <div class="poster-box">
                  <div class="poster poster-01">
                    <div class="name">福建华博教育科技股份有限公司</div>
                    <div class="custom-text">
                      <el-input
                        type="textarea"
                        v-model="input"
                        placeholder="可输入自定义文字"
                        maxlength="18"
                        show-word-limit
                      ></el-input>
                    </div>
                    <!--保存后-->
                    <!--<div class="custom-text">这里是自定义的文字</div>-->
                    <div class="tel">
                      <img class="tel-icon" src="./assets/images/poster-01-tel.png" />
                      <div class="cont">
                        <p>客服热线：</p>
                        <div class="tel-num">4000-968823</div>
                      </div>
                    </div>
                    <div class="qrcode-box">
                      <div class="item">
                        <div class="code"><img src="./assets/images/qr-code.png" /></div>
                        <el-input
                          v-model="input1"
                          placeholder="请输入二维码名称"
                          maxlength="8"
                          show-word-limit
                        ></el-input>
                        <!--保存后-->
                        <!--<div class="txt">扫码立即访问</div>-->
                      </div>
                      <div class="item">
                        <el-upload :show-file-list="false" class="code">
                          <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                          <div v-else class="tips">
                            <i class="el-icon-plus avatar-uploader-icon"></i>
                            <span class="txt">点击上传二维码</span>
                          </div>
                        </el-upload>
                        <el-input
                          v-model="input"
                          placeholder="请输入二维码名称"
                          maxlength="8"
                          show-word-limit
                        ></el-input>
                        <!--保存后-->
                        <!--<div class="txt">扫码立即访问</div>-->
                      </div>
                    </div>
                  </div>
                </div>

                <!--模板2 - 默认样式-->
                <div class="poster-box">
                  <div class="poster poster-02">
                    <div class="name">福建华博教育科技股份有限公司</div>
                    <!--<div class="custom-text">自定义文字</div>-->
                    <div class="tel">
                      <img class="tel-icon" src="./assets/images/poster-01-tel.png" />
                      <div class="cont">
                        <p>客服热线：</p>
                        <div class="tel-num">4000-968823</div>
                      </div>
                    </div>
                    <div class="qrcode-box">
                      <div class="item">
                        <div class="code"><img src="./assets/images/qr-code.png" /></div>
                        <p class="txt">扫码立即访问</p>
                      </div>
                    </div>
                  </div>
                </div>
                <!--模板2 - 编辑样式-->
                <div class="poster-box">
                  <div class="poster poster-02">
                    <div class="name">福建华博教育科技股份有限公司</div>
                    <div class="custom-text">
                      <el-input
                        v-model="input"
                        placeholder="可输入自定义文字"
                        maxlength="24"
                        show-word-limit
                      ></el-input>
                    </div>
                    <!--保存后-->
                    <!--<div class="custom-text">这里是自定义的文字</div>-->
                    <div class="tel">
                      <img class="tel-icon" src="./assets/images/poster-01-tel.png" />
                      <div class="cont">
                        <p>客服热线：</p>
                        <div class="tel-num">4000-968823</div>
                      </div>
                    </div>
                    <div class="qrcode-box">
                      <div class="item">
                        <div class="code"><img src="./assets/images/qr-code.png" /></div>
                        <el-input
                          v-model="input1"
                          placeholder="请输入二维码名称"
                          maxlength="8"
                          show-word-limit
                        ></el-input>
                        <!--保存后-->
                        <!--<div class="txt">扫码立即访问</div>-->
                      </div>
                      <div class="item">
                        <el-upload :show-file-list="false" class="code">
                          <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                          <div v-else class="tips">
                            <i class="el-icon-plus avatar-uploader-icon"></i>
                            <span class="txt">点击上传二维码</span>
                          </div>
                        </el-upload>
                        <el-input
                          v-model="input"
                          placeholder="请输入二维码名称"
                          maxlength="8"
                          show-word-limit
                        ></el-input>
                        <!--保存后-->
                        <!--<div class="txt">扫码立即访问</div>-->
                      </div>
                    </div>
                  </div>
                </div>

                <!--模板3 - 默认样式-->
                <div class="poster-box">
                  <div class="poster poster-03">
                    <div class="name">福建华博教育科技股份有限公司</div>
                    <!--<div class="custom-text">自定义文字</div>-->
                    <div class="tel">
                      <img class="tel-icon" src="./assets/images/poster-03-tel.png" />
                      <div class="cont">
                        <p>客服热线：</p>
                        <div class="tel-num">4000-968823</div>
                      </div>
                    </div>
                    <div class="qrcode-box">
                      <div class="item">
                        <div class="code"><img src="./assets/images/qr-code.png" /></div>
                        <p class="txt">扫码立即访问</p>
                      </div>
                    </div>
                  </div>
                </div>
                <!--模板3 - 编辑样式-->
                <div class="poster-box">
                  <div class="poster poster-03">
                    <div class="name">福建华博教育科技股份有限公司</div>
                    <div class="custom-text">
                      <el-input
                        v-model="input"
                        placeholder="可输入自定义文字"
                        maxlength="24"
                        show-word-limit
                      ></el-input>
                    </div>
                    <!--保存后-->
                    <!--<div class="custom-text">自定义文字</div>-->
                    <div class="tel">
                      <img class="tel-icon" src="./assets/images/poster-03-tel.png" />
                      <div class="cont">
                        <p>客服热线：</p>
                        <div class="tel-num">4000-968823</div>
                      </div>
                    </div>
                    <!--如果有3个，需要添加 is-more-->
                    <div class="qrcode-box">
                      <div class="item">
                        <div class="code"><img src="./assets/images/qr-code.png" /></div>
                        <el-input
                          v-model="input1"
                          placeholder="请输入二维码名称"
                          maxlength="8"
                          show-word-limit
                        ></el-input>
                        <!--保存后-->
                        <!--<div class="txt">扫码立即访问</div>-->
                      </div>
                      <div class="item">
                        <el-upload :show-file-list="false" class="code is-dashed">
                          <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                          <div v-else class="tips">
                            <i class="el-icon-plus avatar-uploader-icon"></i>
                            <span class="txt">点击上传二维码</span>
                          </div>
                        </el-upload>
                        <!--保存后-->
                        <!--<div class="code"><img src="./assets/images/qr-code.png" /></div>-->
                        <el-input
                          v-model="input"
                          placeholder="请输入二维码名称"
                          maxlength="8"
                          show-word-limit
                        ></el-input>
                        <!--保存后-->
                        <!--<div class="txt">扫码立即访问</div>-->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>关闭</el-button>
            <el-button type="primary">下载图片</el-button>
            <el-button type="primary">编辑</el-button>
          </div>
        </el-drawer>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <!--批量更新方案展示-->
        <el-button @click="dialog8 = true" type="primary" class="f-mr20 f-mb20">批量更新方案展示</el-button>
        <el-drawer
          title="批量更新方案展示"
          :visible.sync="dialog8"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="24">
                <el-alert type="warning" :closable="false" class="m-alert">
                  <p>温馨提示：</p>
                  <p>1. 如需批量导入培训班，请先下载 <span class="f-ci">更新培训方案展示模板</span>。</p>
                  <p>2. 支持批量更新培训方案是否展示在专题门户上。</p>
                </el-alert>
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-steps direction="vertical" :active="4" class="m-vertical-steps" style="padding: 20px 0;">
                    <el-step title="下载更新培训方案展示模板，按表格中提示说明填写信息。">
                      <div slot="description">
                        <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-download">
                          下载模板
                        </el-button>
                      </div>
                    </el-step>
                    <el-step title="上传填写好的更新方案表格">
                      <div slot="description">
                        <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                          <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                            选择文件
                          </el-button>
                        </el-upload>
                        <div slot="tip" class="el-upload__tip">
                          <i class="el-icon-warning"></i>
                          <span class="txt">导入的文件格式必须为xls,xlsx文件</span>
                        </div>
                      </div>
                    </el-step>
                  </el-steps>
                  <el-form-item class="m-btn-bar f-tc">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--导入成功提示-->
        <el-button type="primary" @click="dialog9 = true" class="f-mr20">导入成功提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog9" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导入成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导入任务管理-批量更新方案展示</p>
            </div>
          </div>
          <div slot="footer">
            <el-button>暂 不</el-button>
            <el-button type="primary">前往下载</el-button>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames: ['1'],
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleChange(val) {
        console.log(val)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
