import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 培训方案考核结果
 */
export enum TrainClassExamResultEnum {
  // 未知 培训未完成
  UN_KNOW = -1,
  // 培训不合格
  QUALIFICATION = 0,
  // 培训合格
  UN_QUALIFICATION = 1
}
/**
 * @description 物品状态枚举
 */
class TrainClassExamResult extends AbstractEnum<TrainClassExamResultEnum> {
  static enum = TrainClassExamResultEnum
  constructor(status?: TrainClassExamResultEnum) {
    super()
    this.current = status
    this.map.set(TrainClassExamResultEnum.UN_KNOW, '未知')
    this.map.set(TrainClassExamResultEnum.UN_QUALIFICATION, '未合格')
    this.map.set(TrainClassExamResultEnum.QUALIFICATION, '合格')
  }
}

export default new TrainClassExamResult()
