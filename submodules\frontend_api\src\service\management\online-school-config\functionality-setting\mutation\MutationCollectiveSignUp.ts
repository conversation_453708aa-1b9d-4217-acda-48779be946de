import ServicerSeriesV1Gateway, {
  OfflineCollectiveRegisterConfigSaveRequest,
  OnlineCollectiveRegisterConfigSaveRequest
} from '@api/ms-gateway/ms-servicer-series-v1'
import { CollectiveSignUpTypeEnum } from '@api/service/common/enums/online-school-config/CollectiveSignUpType'
import { ResponseStatus } from '@hbfe/common'
import CollectSignUpVo from './vo/CollectiveSignUp/CollectSignUpVo'
import OfflineCollectSignUpVo from './vo/CollectiveSignUp/OfflineCollectSignUpVo'
import OnlineCollectSignUpVo from './vo/CollectiveSignUp/OnlineCollectSignUpVo'
import MsCollectivesign, { CollectiveSignUpModelQueryRequest } from '@api/ms-gateway/ms-collectivesign-v1'

class MutationCollectiveSignUp {
  private type = CollectiveSignUpTypeEnum.ONLINE
  collectiveSignUp = new CollectSignUpVo()

  constructor(type?: CollectiveSignUpTypeEnum) {
    this.type = type
  }
  /**
   * 异步获取集体报名的模板路径
   * @returns {Promise<string>} 返回一个Promise，解析为模板路径
   */
  async getTemplate() {
    const request = new CollectiveSignUpModelQueryRequest()
    request.category = 'NORMAL_IMPORT'
    const res = await MsCollectivesign.queryTemplatePathByCategory(request)
    this.collectiveSignUp.templatePath = res.data || ''
    return this.collectiveSignUp.templatePath
  }
  /**
   * 保存集体报名配置
   * @returns
   */
  async queryDetail(): Promise<ResponseStatus> {
    if (this.type === CollectiveSignUpTypeEnum.ONLINE) {
      const msRes = await ServicerSeriesV1Gateway.getOnlineCollectiveRegisterConfig()
      if (msRes.status.isSuccess()) {
        this.collectiveSignUp = new OnlineCollectSignUpVo()
        this.collectiveSignUp.from(msRes.data)
      }
      return msRes.status
    } else {
      const msRes = await ServicerSeriesV1Gateway.getOfflineCollectiveRegisterConfig()
      if (msRes.status.isSuccess()) {
        this.collectiveSignUp = new OfflineCollectSignUpVo()
        this.collectiveSignUp.from(msRes.data)
      }
      return msRes.status
    }
  }

  /**
   * 保存集体报名配置
   * @returns
   */
  async doSave(): Promise<ResponseStatus> {
    if (this.type === CollectiveSignUpTypeEnum.ONLINE) {
      const msRes = await ServicerSeriesV1Gateway.saveOnlineCollectiveRegisterConfig(
        this.collectiveSignUp.to() as OnlineCollectiveRegisterConfigSaveRequest
      )
      return msRes.status
    } else {
      const msRes = await ServicerSeriesV1Gateway.saveOfflineCollectiveRegisterConfig(
        this.collectiveSignUp.to() as OfflineCollectiveRegisterConfigSaveRequest
      )
      return msRes.status
    }
  }

  /**
   * 预览线下集体报名配置
   * @returns
   */
  doPreview(): Promise<ResponseStatus> {
    return Promise.resolve(new ResponseStatus(200, ''))
  }
}
export default MutationCollectiveSignUp
