"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""申请学习"""
	applyStudentLearningToken(request:QZTGApplyStudentLearningTokenRequest):QZTGApplyStudentLearningTokenResponse
	"""验证是否允许学习"""
	validAllowToLearning(request:QZTGValidAllowToLearningRequest):Boolean
}
input QZTGApplyStudentLearningTokenRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.QZTGApplyStudentLearningTokenRequest") {
	"""参训资格ID"""
	qualificationId:String!
	"""学习方式ID"""
	learningId:String!
	"""学习课程所属类别
		1：专业课，2：公需课
	"""
	learnType:Int!
}
input QZTGValidAllowToLearningRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.QZTGValidAllowToLearningRequest") {
	qualificationId:String!
	"""学习课程所属类别
		1：专业课，2：公需课
	"""
	learnType:Int!
}
type QZTGApplyStudentLearningTokenResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.response.QZTGApplyStudentLearningTokenResponse") {
	token:String
}

scalar List
