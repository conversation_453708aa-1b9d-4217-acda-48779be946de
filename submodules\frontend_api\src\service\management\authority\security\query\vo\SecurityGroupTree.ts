import { SecurityObjectNewResponse, SecurityObjectResponse } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'

export class SecurityGroupTree {
  /**
   * 安全对象唯一标识
   */
  id: string

  /**
   * 是否为菜单
   */
  isMenu: boolean

  /**
   * 安全对象名称
   */
  name: string

  /**
   * 安全对象名称 用于UI显示的名称
   */
  viewName: string

  /**
   * URL内容
   */
  urlContent: string

  /**
   * URL唯一标识
   */
  url: string

  /**
   * 安全对象内容
   */
  content: string

  /**
   * 安全对象描述
   */
  description: string

  /**
   * 父级安全对象Id
   */
  parentId: string

  /**
   * 安全对象子节点
   */
  children: Array<SecurityGroupTree> = []

  /**
   * 是否被选中
   */
  isSelected: boolean

  /**
   * 拓展信息
   */
  ext: any = null
  /**
   * 授权关系ID
   */
  securityAuthorizationId = ''
  /**
   * 排序序号
   */
  sortNo = 0

  static from(response: SecurityObjectNewResponse) {
    const tree = new SecurityGroupTree()
    tree.id = response.id
    tree.isMenu = response.isMenu
    tree.name = response.name
    tree.viewName = response.viewName
    tree.urlContent = response.urlContent
    tree.url = response.urlMark
    tree.content = response.content
    tree.description = response.description
    tree.parentId = response.parentId
    tree.ext = response.ext
    tree.securityAuthorizationId = response.securityAuthorizationId
    tree.sortNo = response.sortNo
    return tree
  }
}
