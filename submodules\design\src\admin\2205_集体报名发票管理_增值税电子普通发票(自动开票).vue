<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="增值税电子普通发票（自动开票）" name="first">
        <div class="f-p15">
          <el-tabs v-model="activeName2" type="card" class="m-tab-card">
            <el-tab-pane label="增值税电子普通发票（自动开票）" name="first">
              <el-card shadow="never" class="m-card f-mb15">
                <!--条件查询-->
                <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
                <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
                <el-row :gutter="16" class="m-query is-border-bottom">
                  <el-form :inline="true" label-width="auto">
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="培训方案">
                        <el-select v-model="select" clearable filterable placeholder="请选择">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="期别名称">
                        <el-input v-model="input" clearable placeholder="请输入期别名称" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="批次号">
                        <el-input v-model="input" clearable placeholder="请输入集体报名批次号" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="姓名">
                        <el-input v-model="input" clearable placeholder="请输入姓名" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="证件号">
                        <el-input v-model="input" clearable placeholder="请输入证件号" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="手机号">
                        <el-input v-model="input" clearable placeholder="请输入购买人手机号" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="发票状态">
                        <el-select v-model="select" clearable filterable placeholder="请选择">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="冻结状态">
                        <el-select v-model="select" clearable filterable placeholder="请选择">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="电子票类型">
                        <el-select v-model="select" clearable filterable placeholder="请选择电子票类型">
                          <el-option value="电子发票（纸电票）"></el-option>
                          <el-option value="电子发票（全电票）"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="发票号">
                        <el-input v-model="input" clearable placeholder="请输入发票号" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="是否冲红">
                        <el-select v-model="select" clearable filterable placeholder="请选择">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="申请开票时间">
                        <el-date-picker
                          v-model="form.date1"
                          type="datetimerange"
                          range-separator="至"
                          start-placeholder="起始时间"
                          end-placeholder="结束时间"
                        >
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="开票时间">
                        <el-date-picker
                          v-model="form.date1"
                          type="datetimerange"
                          range-separator="至"
                          start-placeholder="起始时间"
                          end-placeholder="结束时间"
                        >
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                      <el-form-item class="f-tr">
                        <el-button type="primary">查询</el-button>
                        <el-button>导出列表数据</el-button>
                        <el-button>重置</el-button>
                        <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                        <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
                <!--操作栏-->
                <div class="f-mt20">
                  <el-button type="primary">批量开票</el-button>
                  <el-button type="primary">重试开票</el-button>
                  <el-button type="primary" plain>设置自动开票时间</el-button>
                  <span class="f-ml15 f-cr">注：请设置自动开票时间。</span>
                </div>
                <div class="f-mt20">
                  <el-alert type="warning" :closable="false" class="m-alert">
                    <div class="f-c6">
                      搜索结果合计：开票总金额
                      <span class="f-fb f-co">¥ 5</span>
                      ，开票税额总金额
                      <span class="f-fb f-co">¥ 999</span>
                    </div>
                  </el-alert>
                </div>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
                  <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="集体报名批次号" min-width="220" fixed="left">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <p>2112071509467489926</p>
                        <el-tag type="primary" size="small">拆票</el-tag>
                        <el-tag type="primary" size="small">分销推广</el-tag>
                        <el-tag type="success" size="small">专题</el-tag>
                      </div>
                      <div v-else>
                        <p>2112071509467489926</p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="退款状态" min-width="130">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="primary" class="badge-status">待审核</el-badge>
                        <!--<el-badge is-dot type="primary" class="badge-status">待退货/款</el-badge>-->
                        <!--<el-badge is-dot type="primary" class="badge-status">待退货</el-badge>-->
                        <!--<el-badge is-dot type="primary" class="badge-status">待确认退款</el-badge>-->
                        <!--<el-badge is-dot type="primary" class="badge-status">待退款</el-badge>-->
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-badge is-dot type="danger" class="badge-status">退货失败</el-badge>
                        <!--<el-badge is-dot type="danger" class="badge-status">退款失败</el-badge>-->
                        <!--<el-badge is-dot type="danger" class="badge-status">已拒绝</el-badge>-->
                      </div>
                      <div v-else-if="scope.$index === 2">
                        <el-badge is-dot type="info" class="badge-status">已取消</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="success" class="badge-status">退货/款成功</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="付款金额(元)" width="140" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">3.15</div>
                      <div v-else-if="scope.$index === 1">52.36</div>
                      <div v-else>158.15</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="开票金额(元)" width="140" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">3.15</div>
                      <div v-else-if="scope.$index === 1">52.36</div>
                      <div v-else>158.15</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="税额(元)" width="120" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">3.15</div>
                      <div v-else-if="scope.$index === 1">52.36</div>
                      <div v-else>158.15</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="购买人信息" min-width="240">
                    <template>
                      <p>姓名：张依依</p>
                      <p>证件号：354875965412365896</p>
                      <p>手机号：15847412365</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="发票抬头" min-width="300">
                    <template>【单位】福建华博教育教育科技股份有限公司</template>
                  </el-table-column>
                  <el-table-column label="统一社会信用代码" min-width="180">
                    <template>32514568758965542</template>
                  </el-table-column>
                  <el-table-column label="申请开票时间" min-width="180">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="电子票类型" min-width="180">
                    <template>电子发票（纸电票）</template>
                  </el-table-column>
                  <el-table-column label="发票状态" min-width="130">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="warning" class="badge-status">待开票</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-badge is-dot type="danger" class="badge-status">开票失败</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 2">
                        <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                          <el-badge is-dot type="primary" class="badge-status">开票中</el-badge>
                          <div slot="content">
                            <p>暂无数据</p>
                          </div>
                        </el-tooltip>
                      </div>
                      <div v-else-if="scope.$index === 3">
                        <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                          <el-badge is-dot type="primary" class="badge-status">开票中</el-badge>
                          <div slot="content">
                            <p>异常说明：读取第三方接口同步的失败原因</p>
                          </div>
                        </el-tooltip>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="success" class="badge-status">开票成功</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="开票时间" min-width="180">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="发票号" min-width="120">
                    <template>26541122</template>
                  </el-table-column>
                  <el-table-column label="是否冻结" min-width="120">
                    <template>是</template>
                  </el-table-column>
                  <el-table-column label="操作" width="200" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-button type="text" size="mini">修改发票信息</el-button>
                        <el-button type="text" size="mini">记录</el-button>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-button type="text" size="mini">下载发票</el-button>
                        <el-button type="text" size="mini">记录</el-button>
                        <el-button type="text" size="mini">重新开票</el-button>
                      </div>
                      <div v-else>
                        <el-button type="text" size="mini">冲红发票</el-button>
                        <el-button type="text" size="mini">记录</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-card>
            </el-tab-pane>
            <el-tab-pane label="冲红发票" name="second">
              <el-card shadow="never" class="m-card f-mb15">
                <!--条件查询-->
                <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
                <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
                <el-row :gutter="16" class="m-query is-border-bottom">
                  <el-form :inline="true" label-width="auto">
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="培训方案">
                        <el-select v-model="select" clearable filterable placeholder="请选择">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="期别名称">
                        <el-input v-model="input" clearable placeholder="请输入期别名称" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="冲红发票号">
                        <el-input v-model="input" clearable placeholder="请输入冲红发票号" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="冲红状态">
                        <el-select v-model="select" clearable filterable placeholder="请选择">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="姓名">
                        <el-input v-model="input" clearable placeholder="请输入姓名" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="证件号">
                        <el-input v-model="input" clearable placeholder="请输入证件号" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="手机号">
                        <el-input v-model="input" clearable placeholder="请输入购买人手机号" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="订单号">
                        <el-input v-model="input" clearable placeholder="请输入订单号" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="发票状态">
                        <el-select v-model="select" clearable filterable placeholder="请选择">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="开票时间">
                        <el-date-picker
                          v-model="form.date1"
                          type="datetimerange"
                          range-separator="至"
                          start-placeholder="起始时间"
                          end-placeholder="结束时间"
                        >
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                      <el-form-item class="f-tr">
                        <el-button>重置</el-button>
                        <el-button>导出数据</el-button>
                        <el-button type="primary">查询</el-button>
                        <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                        <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
                <div class="f-mt20">
                  <el-alert type="warning" :closable="false" class="m-alert">
                    <div class="f-c6">
                      统计： 冲红发票总金额
                      <span class="f-fb f-co">¥ 5</span>
                      ，冲红税额总金额
                      <span class="f-fb f-co">¥ 999</span>
                      ，冲红发票仅统计增值税电子普通发票退款产生。
                    </div>
                  </el-alert>
                </div>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="集体报名批次号" min-width="220" fixed="left">
                    <template>
                      <p>2112071509467489926</p>
                      <el-tag type="primary" size="small">分销推广</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="蓝票发票号" min-width="220" ixed="left">
                    <template>2112071509467489926</template>
                  </el-table-column>
                  <el-table-column label="红票发票号" min-width="220" ixed="left">
                    <template>2112071509467489926</template>
                  </el-table-column>
                  <el-table-column label="购买人信息" min-width="240">
                    <template>
                      <p>姓名：张依依</p>
                      <p>证件号：354875965412365896</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="发票内容" min-width="400">
                    <template>
                      <p class="f-flex">
                        <span>发票抬头：</span>
                        <span class="f-flex-sub">【单位】福建华博教育教育科技股份有限公司</span>
                      </p>
                      <p>统一社会信用代码：32514568758965542</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="冲红金额(元)" width="140" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">3.15</div>
                      <div v-else-if="scope.$index === 1">52.36</div>
                      <div v-else>158.15</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="申请开票时间" min-width="180">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="冲红状态" min-width="130">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="primary" class="badge-status">冲红中</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-badge is-dot type="danger" class="badge-status">冲红失败</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="success" class="badge-status">冲红成功</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="160" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0"></div>
                      <div v-else-if="scope.$index === 1"></div>
                      <div v-else>
                        <el-button type="text" size="mini">下载冲红发票</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      <el-tab-pane label="增值税电子普通发票（线下开票）" name="second">
        <div class="f-p15">详见 2206_集体报名发票管理_电子票（线下开票） .vue</div>
      </el-tab-pane>
      <el-tab-pane label="增值税专用发票" name="third">
        <div class="f-p15">详见 2207_集体报名发票管理_专票.vue</div>
      </el-tab-pane>
      <el-tab-pane label="发票配送" name="four">
        <div class="f-p15">详见 2208_集体报名发票管理_发票配送 .vue</div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
