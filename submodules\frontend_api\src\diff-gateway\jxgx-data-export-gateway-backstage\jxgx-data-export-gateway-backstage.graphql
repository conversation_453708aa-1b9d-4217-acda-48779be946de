"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""发票配送导出
		@return
	"""
	exportInvoiceDeliveryInServicer(request:OfflineInvoiceExportRequest):Boolean!
	"""线下发票导出-继续教育
		@return
	"""
	exportOfflineInvoiceInServicerForJxjy(request:OfflineInvoiceExportRequest):Boolean!
	"""线上发票导出-继续教育
		@return
	"""
	exportOnlineInvoiceInServicerForJxjy(request:OnlineInvoiceRequest):Boolean!
	"""导出个人订单
		@param request
		@param sort
		@return
	"""
	exportOrderExcelInDistributor(request:OrderRequest1,sort:[OrderSortRequest]):Boolean!
	"""导出个人订单
		@param request
		@param sort
		@return
	"""
	exportOrderExcelInServicer(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出个人对账
		@param request
		@param sort
		@return
	"""
	exportReconciliationExcelInDistributor(request:OrderRequest1,sort:[OrderSortRequest]):Boolean!
	"""导出个人对账
		@param request
		@param sort
		@return
	"""
	exportReconciliationExcelInServicer(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出个人退货单"""
	exportReturnOrderExcelInDistributor(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""导出个人退货单"""
	exportReturnOrderExcelInServicer(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""导出个人报名退货对账"""
	exportReturnReconciliationExcelInDistributor(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""导出个人报名退货对账"""
	exportReturnReconciliationExcelInServicer(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""功能描述：学员导出
		@return : void
		@Author： wtl
		@Date： 2022/1/18 15:14
	"""
	exportStudentExcelInServicer(request:StudentQueryRequest):Boolean!
	"""功能描述：项目级-学员导出
		@return : void
		@Author： wtl
		@Date： 2022年11月17日 15:25:00
	"""
	exportStudentExcelInSubProject(request:StudentQueryRequest):Boolean!
	"""导出当前服务商下学员方案学习
		@param request
		@return
	"""
	exportStudentSchemeLearningExcelInServicerForJxjy(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""导出当前服务商地区管理员下学员方案学习
		@return
	"""
	exportStudentSchemeLearningExcelInServicerManageRegion(request:StudentSchemeLearningRequest):Boolean!
}
input ObsFileMetaData @type(value:"com.fjhb.ms.data.export.commons.utils.async.ObsFileMetaData") {
	bizType:String
	owner:String
	sign:String
}
input BigDecimalScopeRequest @type(value:"com.fjhb.ms.query.commons.BigDecimalScopeRequest") {
	begin:BigDecimal
	end:BigDecimal
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
input ConnectManageSystemRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ConnectManageSystemRequest") {
	syncStatus:Int
}
input DataAnalysisRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.DataAnalysisRequest") {
	trainingResultPeriod:DoubleScopeRequest
	requirePeriod:DoubleScopeRequest
	acquiredPeriod:DoubleScopeRequest
}
input ExtendedInfoRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ExtendedInfoRequest") {
	whetherToPrint:Boolean
	applyCompanyCode:String
	policyTrainingSchemeId:String
	policyTrainingSchemeName:String
}
input LearningRegisterRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.LearningRegisterRequest") {
	registerType:Int
	sourceType:String
	sourceId:String
	status:[Int]
	registerTime:DateScopeRequest
	saleChannels:[Int]
	orderNoList:[String]
	subOrderNoList:[String]
	batchOrderNoList:[String]
	distributorId:String
	portalId:String
}
input RegionRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.RegionRequest") {
	province:String
	city:String
	county:String
}
input StudentLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.learning.StudentLearningRequest") {
	trainingResultList:[Int]
	trainingResultTime:DateScopeRequest
	notLearningTypeList:[Int]
	courseScheduleStatus:Int
	examAssessResultList:[Int]
}
input RegionSkuPropertyRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest1 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertySearchRequest") {
	region:[RegionSkuPropertyRequest1]
	regionSearchType:Int
}
input SchemeRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeRequest") {
	schemeId:String
	schemeIdList:[String]
	schemeType:String
	schemeName:String
	skuProperty:SchemeSkuPropertyRequest
}
input SchemeSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeSkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest1
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	positionCategory:[String]
	trainingObject:[String]
	jobLevel:[String]
	jobCategory:[String]
	subject:[String]
	grade:[String]
	learningPhase:[String]
	discipline:[String]
	qualificationCategory:[String]
	trainingWay:[String]
	trainingInstitution:[String]
	mainAdditionalItem:[String]
}
input UserPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserPropertyRequest") {
	regionList:[RegionRequest]
	companyName:String
	payOrderRegionList:[RegionRequest]
}
input UserRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserRequest") {
	userIdList:[String]
	accountIdList:[String]
	userProperty:UserPropertyRequest
}
input OnShelveRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.request.nested.onshevl.OnShelveRequest") {
	onShelveStatus:Int
}
input CommodityAuthInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.CommodityAuthInfoRequest") {
	distributorId:String
	distributionLevel:Int
	superiorDistributorId:String
	supplierId:String
	salesmanId:String
}
input CommoditySkuRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.CommoditySkuRequest") {
	commoditySkuIdList:[String]
	saleTitle:String
	issueInfo:IssueInfo
	skuProperty:SkuPropertyRequest
	externalTrainingPlatform:[String]
	trainingInstitution:[String]
}
input RegionSkuPropertyRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertySearchRequest") {
	regionSearchType:Int
	region:[RegionSkuPropertyRequest]
}
input SkuPropertyRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.SkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	trainingObject:[String]
	positionCategory:[String]
	jobLevel:[String]
	jobCategory:[String]
	grade:[String]
	subject:[String]
	learningPhase:[String]
	discipline:[String]
	trainingChannelIds:[String]
	certificatesType:[String]
	practitionerCategory:[String]
	qualificationCategory:[String]
	trainingForm:[String]
}
input IssueInfo @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.IssueInfo") {
	issueId:String
	issueName:String
	issueNum:String
	trainStartTime:DateTime
	trainEndTime:DateTime
	sourceType:String
	sourceId:String
}
input OrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.OrderRequest") {
	orderNoList:[String]
	subOrderNoList:[String]
	subOrderReturnStatus:[Int]
	orderBasicData:OrderBasicDataRequest
	subOrderBasicData:SubOrderBasicDataRequest
	payInfo:OrderPayInfoRequest
	buyerIdList:[String]
	deliveryCommodity:CommoditySkuRequest
	currentCommodity:CommoditySkuRequest
	saleChannel:Int
	saleChannels:[Int]
	excludeSaleChannels:[Int]
	saleChannelIds:[String]
	saleChannelName:String
	cardTypeId:String
	distributorId:String
	portalId:String
	orderFixQuery:OrderFixQueryRequest
	isDistributionExcludePortal:Boolean
	externalTrainingPlatform:[String]
	unitIds:[String]
	issueId:[String]
	policyTrainingSchemeIdList:[String]
	declarationUnitCodeList:[String]
	settlementStatus:Int
	settlementDate:DateScopeRequest
}
input OrderSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.OrderSortRequest") {
	field:OrderSortField
	policy:SortPolicy
}
input OrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderBasicDataRequest") {
	orderType:Int
	batchOrderNoList:[String]
	orderStatusList:[Int]
	orderPaymentStatusList:[Int]
	orderDeliveryStatusList:[Int]
	orderStatusChangeTime:OrderStatusChangeTimeRequest
	channelTypesList:[Int]
	excludeChannelTypesList:[Int]
	terminalCodeList:[String]
	orderAmountScope:BigDecimalScopeRequest
}
input OrderFixQueryRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderFixQueryRequest") {
	excludeChannelTypesList:[Int]
	excludeSaleChannels:[Int]
}
input OrderPayInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderPayInfoRequest") {
	receiveAccountIdList:[String]
	flowNoList:[String]
	paymentOrderTypeList:[Int]
}
input OrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderStatusChangeTimeRequest") {
	normalDateScope:DateScopeRequest
	completedDatesScope:DateScopeRequest
}
input SubOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.SubOrderBasicDataRequest") {
	discountType:Int
	discountSourceId:String
	useDiscount:Boolean
	commodityAuthInfo:CommodityAuthInfoRequest
}
input ReturnOrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.ReturnOrderRequest") {
	unitIdList:[String]
	returnOrderNoList:[String]
	basicData:ReturnOrderBasicDataRequest
	approvalInfo:ReturnOrderApprovalInfoRequest
	returnCommoditySkuIdList:[String]
	returnCommodity:CommoditySkuRequest
	refundCommoditySkuIdList:[String]
	refundCommodity:CommoditySkuRequest
	subOrderInfo:SubOrderInfoRequest
	commodityAuthInfo:CommodityAuthInfoRequest
	distributorId:String
	portalId:String
	isDistributionExcludePortal:Boolean
}
input ReturnSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.ReturnSortRequest") {
	field:ReturnOrderSortField
	policy:SortPolicy
}
input OrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.OrderInfoRequest") {
	orderNoList:[String]
	batchOrderNoList:[String]
	buyerIdList:[String]
	receiveAccountIdList:[String]
	flowNoList:[String]
	channelTypesList:[Int]
	terminalCodeList:[String]
	saleChannel:Int
	saleChannels:[Int]
	saleChannelName:String
	saleChannelIds:[String]
	policyTrainingSchemeIdList:[String]
	declarationUnitCodeList:[String]
}
input ReturnCloseReasonRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnCloseReasonRequest") {
	closeTypeList:[Int]
}
input ReturnOrderApprovalInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderApprovalInfoRequest") {
	approveTime:DateScopeRequest
}
input ReturnOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderBasicDataRequest") {
	returnOrderStatus:[Int]
	returnOrderTypes:[Int]
	applySourceType:String
	applySourceIdList:[String]
	returnCloseReason:ReturnCloseReasonRequest
	returnStatusChangeTime:ReturnOrderStatusChangeTimeRequest
	refundAmountScope:BigDecimalScopeRequest
}
input ReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderStatusChangeTimeRequest") {
	applied:DateScopeRequest
	returnCompleted:DateScopeRequest
}
input SubOrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.SubOrderInfoRequest") {
	subOrderNoList:[String]
	orderInfo:OrderInfoRequest
	discountType:Int
	useDiscount:Boolean
}
"""<AUTHOR> linq
	@date : 2024-05-24 10:32
	@description：商品查询请求入参
"""
input CommoditySkuRequest1 @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.orderInfo.param.CommoditySkuRequest") {
	"""商品id"""
	commoditySkuIdList:[String]
	"""商品名称（模糊查询）"""
	saleTitleMatchLike:String
	"""商品上下架信息"""
	onShelveRequest:OnShelveRequest
	"""商品sku属性查询"""
	skuPropertyRequest:SkuPropertyRequest
	"""是否展示资源不可用的商品"""
	isDisabledResourceShow:Boolean
	"""是否展示所有资源
		（该字段会屏蔽可见渠道、商品资源是否可用、商品上下架状态三个条件）
	"""
	isShowAll:Boolean
	"""商品购买渠道配置"""
	purchaseChannelConfig:[PurchaseChannelConfigRequest]
}
"""<AUTHOR> linq
	@date : 2024-05-24 09:54
	@description：订单信息请求入参
"""
input OrderRequest1 @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.orderInfo.param.OrderRequest") {
	"""订单号集合"""
	orderNoList:[String]
	"""子订单号集合"""
	subOrderNoList:[String]
	"""子订单退货状态
		0: 未退货
		1: 退货申请中
		2: 退货中
		3: 退货成功
		4: 退款中
		5: 退款成功
		@see SubOrderReturnStatus
	"""
	subOrderReturnStatus:[Int]
	"""订单基本信息查询参数"""
	orderBasicData:OrderBasicDataRequest
	"""子订单基本信息查询参数"""
	subOrderBasicData:SubOrderBasicDataRequest
	"""订单支付信息查询"""
	payInfo:OrderPayInfoRequest
	"""买家查询参数"""
	buyerIdList:[String]
	"""发货商品信息"""
	deliveryCommodity:CommoditySkuRequest1
	"""现有商品信息"""
	currentCommodity:CommoditySkuRequest1
	"""销售渠道
		0-自营 1-分销 不传则查全部
	"""
	saleChannel:Int
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""<AUTHOR> linq
	@date : 2024-05-27 16:17
	@description：商品购买渠道配置查询参数
"""
input PurchaseChannelConfigRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.orderInfo.param.nests.PurchaseChannelConfigRequest") {
	"""购买渠道类型
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes;
	"""
	purchaseChannel:Int
	"""是否可见"""
	couldSee:Boolean
	"""是否可购买"""
	couldBuy:Boolean
}
"""发票关联订单查询参数
	<AUTHOR>
	@date 2022/3/18
"""
input InvoiceAssociationInfoRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.InvoiceAssociationInfoRequest") {
	"""关联订单类型
		0:订单号
		1:批次单号
		@see AssociationTypes
	"""
	associationType:Int
	"""订单号 | 批次单号"""
	associationIdList:[String]
	"""买家信息"""
	buyerIdList:[String]
	"""企业id"""
	unitBuyerUnitIdList:[String]
	"""收款账号"""
	receiveAccountIdList:[String]
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
}
"""线下发票查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceExportRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.OfflineInvoiceExportRequest") {
	"""发票ID集合"""
	invoiceIdList:[String]
	"""发票基本信息"""
	basicData:OfflineInvoiceBasicDataRequest
	"""发票关联订单查询参数"""
	associationInfo:InvoiceAssociationInfoRequest
	"""发票配送信息"""
	invoiceDeliveryInfo:OfflineInvoiceDeliveryInfoRequest
	"""所属单位id集合"""
	unitIds:[String]
	"""收款账号id"""
	receiveAccountId:[String]
	"""期别名称"""
	issueId:[String]
	jobName:String
	metaData:ObsFileMetaData
}
"""发票查询参数
	<AUTHOR>
	@date 2022/03/23
"""
input OnlineInvoiceRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.OnlineInvoiceRequest") {
	"""发票id集合"""
	invoiceIdList:[String]
	"""发票基础信息查询参数"""
	basicData:OnlineInvoiceBasicDataRequest
	"""发票关联订单查询参数"""
	associationInfoList:[InvoiceAssociationInfoRequest]
	"""蓝票票据查询参数"""
	blueInvoiceItem:OnlineInvoiceItemRequest
	"""红票票据查询参数"""
	redInvoiceItem:OnlineInvoiceItemRequest
	"""所属单位id集合"""
	unitIds:[String]
	"""收款账号id"""
	receiveAccountId:[String]
	"""期别名称"""
	issueId:[String]
	jobName:String
	metaData:ObsFileMetaData
}
"""功能描述：学员查询条件
	@Author： wtl
	@Date： 2022年1月26日 10:10:33
"""
input StudentQueryRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.StudentQueryRequest") {
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:StudentUserRequest
	"""用户认证信息"""
	authentication:AuthenticationRequest
	"""排序"""
	sortList:[StudentSortRequest]
}
input StudentSchemeLearningRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.StudentSchemeLearningRequest") {
	"""学员信息"""
	student:UserRequest
	"""报名信息"""
	learningRegister:LearningRegisterRequest
	"""培训班信息"""
	scheme:SchemeRequest
	"""学习信息"""
	studentLearning:StudentLearningRequest
	"""数据分析信息"""
	dataAnalysis:DataAnalysisRequest
	"""对接管理系统"""
	connectManageSystem:ConnectManageSystemRequest
	"""扩展信息"""
	extendedInfo:ExtendedInfoRequest
	"""方案是否提供培训证明"""
	openPrintTemplate:Boolean
	"""是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1"""
	saleChannels:[Int]
	"""专题名称"""
	trainingChannelName:String
	"""专题Id  用于不同专题域名 查询对应专题的培训班"""
	trainingChannelId:String
}
input AccountRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.AccountRequest") {
	"""账户状态 1：正常，2：冻结，3：注销
		@see AccountStatus
	"""
	statusList:[Int]
	"""创建时间范围"""
	createTimeScope:DateScopeRequest
	"""创建人用户id"""
	createdUserId:String
	"""来源类型
		0-内置 1-项目主网站 2-安卓 3-IOS 4-后台导入 5-迁移数据 6-分销平台项目主网站 7-专题 8-华医网
	"""
	sourceTypes:[Int]
}
"""功能描述：账户认证查询条件
	@Author： wtl
	@Date： 2022年1月26日 09:30:12
"""
input AuthenticationRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.AuthenticationRequest") {
	"""帐号"""
	identity:String
	"""用户名"""
	userName:String
}
"""发票开具状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input BillStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.BillStatusChangeTimeRequest") {
	"""未开具"""
	unBill:DateScopeExcelRequest
	"""开票中"""
	billing:DateScopeExcelRequest
	"""开票成功"""
	success:DateScopeExcelRequest
	"""开票失败"""
	failure:DateScopeExcelRequest
}
"""功能描述：学员集体缴费信息
	@Author： wtl
	@Date： 2022年4月21日 08:58:49
"""
input CollectiveRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.CollectiveRequest") {
	"""集体缴费管理员用户id集合"""
	collectiveUserIdList:[String]
}
"""范围查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/5/7 15:34
"""
input DateScopeExcelRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.DateScopeExcelRequest") {
	"""result >= begin"""
	begin:DateTime
	"""result <= end"""
	end:DateTime
}
"""配送地址信息
	<AUTHOR>
	@date 2022/05/07
"""
input DeliveryAddressRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.DeliveryAddressRequest") {
	"""收件人"""
	consignee:String
}
"""配送状态变更时间查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input DeliveryStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.DeliveryStatusChangeTimeRequest") {
	"""未就绪"""
	unReady:DateScopeExcelRequest
	"""已就绪"""
	ready:DateScopeExcelRequest
	"""已配送"""
	shipped:DateScopeExcelRequest
	"""已自取"""
	taken:DateScopeExcelRequest
}
"""快递信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input ExpressRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.ExpressRequest") {
	"""快递单号"""
	expressNo:String
}
"""发票开票状态变更时间记录查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input InvoiceBillStatusChangTimeRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.InvoiceBillStatusChangTimeRequest") {
	"""发票申请开票时间"""
	unBillDateScope:DateScopeExcelRequest
	"""发票开票时间"""
	successDateScope:DateScopeExcelRequest
}
"""发票状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input InvoiceStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.InvoiceStatusChangeTimeRequest") {
	"""正常"""
	normal:DateScopeExcelRequest
	"""作废"""
	invalid:DateScopeExcelRequest
}
"""线下发票基本信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceBasicDataRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.OfflineInvoiceBasicDataRequest") {
	"""发票类型
		1:电子发票 2:纸质发票
		@see InvoiceTypes
	"""
	invoiceTypeList:[Int]
	"""发票种类
		1:普通发票 2:增值税普通发票 3:增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategory:[Int]
	"""发票状态
		1:正常
		2:作废
		@see InvoiceStatus
	"""
	invoiceStatus:Int
	"""发票状态变更时间记录"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeRequest
	"""发票开票状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	billStatusList:[Int]
	"""发票开票状态变更时间记录"""
	billStatusChangTime:InvoiceBillStatusChangTimeRequest
	"""发票是否冻结"""
	freeze:Boolean
	"""发票号集合"""
	invoiceNoList:[String]
	"""商品id集合"""
	commoditySkuIdList:[String]
}
"""线下发票配送信息
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceDeliveryInfoRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.OfflineInvoiceDeliveryInfoRequest") {
	"""配送状态
		0:未就绪 1：已就绪 2：已自取 3：已配送
		@see OfflineDeliveryStatus
	"""
	deliveryStatusList:[Int]
	"""配送状态变更时间记录
		0:未就绪 1：已就绪 2：已自取 3：已配送
		key值 {@link OfflineDeliveryStatus}
	"""
	deliveryStatusChangeTime:DeliveryStatusChangeTimeRequest
	"""配送方式
		0:无 1：自取 2：快递
		@see OfflineShippingMethods
	"""
	shippingMethodList:[Int]
	"""快递信息"""
	express:ExpressRequest
	"""自取信息"""
	takeResult:TakeResultRequest
	"""配送地址信息"""
	deliveryAddress:DeliveryAddressRequest
}
"""发票基础信息查询参数
	<AUTHOR>
	@date 2022/03/23
"""
input OnlineInvoiceBasicDataRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.OnlineInvoiceBasicDataRequest") {
	"""发票类型
		1:电子发票 2:纸质发票
		@see InvoiceTypes
	"""
	invoiceType:Int
	"""发票种类
		1:普通发票 2:增值税普通发票 3:增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategoryList:[Int]
	"""发票状态变更时间
		@see InvoiceStatus
	"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeRequest
	"""发票状态
		1：正常 2：作废
		@see InvoiceStatus
	"""
	invoiceStatusList:[Int]
	"""蓝票票据开具状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	blueInvoiceItemBillStatusList:[Int]
	"""红票票据开具状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	redInvoiceItemBillStatusList:[Int]
	"""发票是否已冲红"""
	flushed:Boolean
	"""发票是否已生成红票票据"""
	redInvoiceItemExist:Boolean
	"""商品id集合"""
	commoditySkuIdList:[String]
	"""发票是否冻结"""
	freeze:Boolean
}
"""发票票据
	<AUTHOR>
	@date 2022/03/18
"""
input OnlineInvoiceItemRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.OnlineInvoiceItemRequest") {
	"""票据开具状态变更时间"""
	billStatusChangeTime:BillStatusChangeTimeRequest
	"""发票号码"""
	billNoList:[String]
}
"""功能描述 : 学员排序参数
	@date : 2022/4/1 17:15
"""
input StudentSortRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.StudentSortRequest") {
	"""学员排序字段"""
	studentSortField:StudentSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
input StudentUserRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.StudentUserRequest") {
	"""工作单位名称（模糊）"""
	companyName:String
	"""用户所属地区路径集合（模糊，右like）"""
	regionPathList:[String]
	"""集体缴费信息"""
	collective:CollectiveRequest
	"""单位所属地区路径集合（模糊，右like）"""
	companyRegionPathList:[String]
	"""单位所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	companyRegionPathListMatchType:Int
	"""是否工勤人员  (0:非工勤人员  1:工勤人员)"""
	isWorker:String
	"""是否退休   (0:非退休人员 1:退休人员)"""
	isRetire:String
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
}
"""取件信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input TakeResultRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.TakeResultRequest") {
	"""领取人"""
	takePerson:String
}
enum SortPolicy @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.SortPolicy") {
	ASC
	DESC
}
enum OrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderSortField") {
	ORDER_NORMAL_TIME
	ORDER_COMPLETED_TIME
}
enum ReturnOrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderSortField") {
	APPLIED_TIME
}
"""功能描述：排序类型
	@Author： wtl
	@Date： 2021/12/27 10:30
"""
enum SortTypeEnum @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.SortTypeEnum") {
	"""升序"""
	ASC
	"""降序"""
	DESC
}
"""功能描述：学员排序字段
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
enum StudentSortFieldEnum @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.nested.StudentSortFieldEnum") {
	"""创建时间"""
	createdTime
}

scalar List
