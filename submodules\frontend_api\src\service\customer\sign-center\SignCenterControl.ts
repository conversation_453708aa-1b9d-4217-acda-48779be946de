import GeoLocation from '@api/service/customer/sign-center/models/GeoLocation'
import PeriodSignLog from '@api/service/customer/sign-center/models/PeriodSignLog'
import MsCourseLearningQuery from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import MsCourseLearningQueryFront, {
  PlanItemAttendanceRequest,
  PlanItemAttendanceResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import DateGroupItem from '@api/service/customer/sign-center/models/DateGroupItem'
import { Page, Response, ResponseStatus } from '@hbfe/common'
import MsTeachingplanAttendance, { GetServerTimeRequest } from '@api/ms-gateway/ms-teachingplan-attendance-sds-v1'
import QueryMyTrainClassDetail from '@api/service/customer/train-class/query/QueryMyTrainClassDetail'
import MyTrainClassDetailClassVo from '@api/service/customer/train-class/query/vo/MyTrainClassDetailClassVo'
import ApplyStudentIssueLearningToken from '@api/service/common/token/ApplyStudentIssueLearningToken'
import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
import CourseSignItem from '@api/service/customer/sign-center/models/CourseSignItem'
import MsTeachingplanStudentattendance, {
  ClockResponse,
  StudentClockInRequest,
  StudentClockOutRequest
} from '@api/ms-gateway/ms-teachingplan-studentattendance-sds-v1'
import { SignResCodeEnum } from '@api/service/customer/sign-center/enums/SignResCodeEnum'
import { SignStatusEnum } from '@api/service/customer/sign-center/enums/SignStatusEnum'
import StudentFaceStudyLog from '@api/service/customer/implement/StudentFaceStudyLog'
import PeriodImplementConfig from '@api/service/customer/implement/PeriodImplementConfig'
import CurrentDaySignStatisticRes from '@api/service/customer/sign-center/models/CurrentDaySignStatisticRes'
import { SignTypeEnum } from '@api/service/customer/sign-center/enums/SignTypeEnum'
import { CheckInFrequencyEnum } from '@api/service/common/implement/enums/CheckInFrequencyEnum'
import MsSchemeLearning, {
  CountSignInRecordRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'

/**
 * 签到中心
 */
export default class SignCenterControl {
  /**
   * 期别id
   */
  private periodId: string = undefined

  /**
   * 期别参训资格id
   */
  private periodQualificationId: string = undefined

  /**
   * 学号
   */
  private studentNo: string = undefined

  /**
   * 方案参训资格id
   */
  private qualificationId: string = undefined

  /**
   * 当前定位点
   */
  private currentPoint: GeoLocation = new GeoLocation()

  /**
   * 签到点位置
   */
  private signLocation: GeoLocation = new GeoLocation()

  /**
   * 当前服务器时间戳
   */
  private currentServiceTimestamp: number = undefined

  /**
   * 计时器
   */
  private interval: NodeJS.Timeout = undefined

  /**
   * 是否处在倒计时
   */
  private isClock = false

  /**
   * 学员学习token
   */
  private studentLearningToken = ''

  /**
   * 多少分钟开始距离签到时间倒计时时间戳
   */
  private fixTipTimestamp = 30 * 60000

  /**
   * 签到点距离
   */
  private signDistance: number = undefined

  /**
   * 教学计划id（来源于期别配置）
   */
  planId = ''

  /**
   * 当前日期
   */
  currentDate: DateGroupItem = new DateGroupItem()

  /**
   * 期别打卡次数
   */
  periodSignLog: PeriodSignLog = new PeriodSignLog()

  /**
   * 方案配置信息
   */
  schemeConfig: MyTrainClassDetailClassVo = new MyTrainClassDetailClassVo()

  /**
   * 当前期别配置（来源于方案，包含期别培训地点等信息）
   */
  currentPeriodConfig: IssueConfigDetail = new IssueConfigDetail()

  /**
   * 期别实施配置（取考勤具体配置）
   */
  periodImplementConfig: PeriodImplementConfig = new PeriodImplementConfig(this.periodId)

  /**
   * 日期组
   */
  dateGroup: Array<DateGroupItem> = new Array<DateGroupItem>()

  /**
   * 当前日期已签到数
   */
  currentDateSignNum: number = undefined

  /**
   * 当前日期需签到数
   */
  currentDateTotalSignNum: number = undefined

  /**
   * 签到课程列表
   */
  courseList: Array<CourseSignItem> = new Array<CourseSignItem>()

  /**
   * 是否临近签到点
   */
  get nearSignIn() {
    if (
      typeof this.signDistance === 'number' &&
      typeof this.periodImplementConfig.signInConfig.checkInLocationRadius === 'number'
    ) {
      return this.signDistance <= this.periodImplementConfig.signInConfig.checkInLocationRadius
    } else {
      return false
    }
  }

  /**
   * 是否临近签退点
   */
  get nearSignOut() {
    if (
      typeof this.signDistance === 'number' &&
      typeof this.periodImplementConfig.signOutConfig.checkInLocationRadius === 'number'
    ) {
      return this.signDistance <= this.periodImplementConfig.signOutConfig.checkInLocationRadius
    } else {
      return false
    }
  }

  /**
   * 获取当前位置
   */
  get currentLocation() {
    return this.currentPoint
  }

  /**
   * 设置当前位置
   * @param location 位置点
   */
  set currentLocation(location: GeoLocation) {
    this.currentPoint = location
    if (this.currentPoint.longitude && this.currentPoint.latitude) {
      this.signDistance = this.calculateDistance(
        this.signLocation.latitude,
        this.signLocation.longitude,
        location.latitude,
        location.longitude
      )
    }
  }

  /**
   * @param studentNo 学号
   * @param qualificationId 方案参训资格id
   */
  constructor(studentNo: string, qualificationId: string) {
    this.studentNo = studentNo
    this.qualificationId = qualificationId
    this.currentLocation = new GeoLocation()
  }

  /**
   * 查询学员最近在学期别信息
   */
  static async getStudentLastStudyQualification() {
    const res = await MsCourseLearningQueryFront.getLastStudyQualificationInMyself()

    return res?.data
  }

  /**
   * 根据日期查询当日签到统计
   * @param date 当日日期 YYYY-MM-DD
   * @param periodQualificationId 期别参训资格id
   */
  static async queryCurrentDaySignStatistic(periodQualificationId: string, date: string) {
    /**
     * 后端没有开当日签到的统计口，依赖于前端自己取当日列表进行统计，故需要把列表所有数据全查出来
     * 后端说需求最多就10条，到时候超过200条找他们问罪
     */
    const request = new PlanItemAttendanceRequest()
    request.qualificationId = periodQualificationId
    request.beginTime = `${date} 00:00:00`
    request.endTime = `${date} 23:59:59`

    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    let statisticList = new Array<CourseSignItem>()
    const res = await MsCourseLearningQuery.pagePlanItemAttendanceInMyself({ page, request })

    if (res?.data?.currentPageData?.length) {
      statisticList = res.data.currentPageData.map((item: PlanItemAttendanceResponse) => {
        return CourseSignItem.from(item)
      })
    }

    let currentCheck = 0
    let currentTotalCheck = 0
    statisticList.map((item: CourseSignItem) => {
      if (item.signInLog.timeSignStatus != SignStatusEnum.unSign) {
        currentTotalCheck++
        if (item.signInLog.timeSignStatus === SignStatusEnum.signed) {
          currentCheck++
        }
      }
      if (item.signOutLog.timeSignStatus != SignStatusEnum.unSign) {
        currentTotalCheck++
        if (item.signOutLog.timeSignStatus === SignStatusEnum.signed) {
          currentCheck++
        }
      }
    })
    const result = new CurrentDaySignStatisticRes()
    result.dateSignNum = currentCheck
    result.dateTotalSignNum = currentTotalCheck
    result.courseNum = statisticList.length
    return result
  }

  /**
   * 初始化
   */
  async init() {
    /**
     * 初始化做以下内容
     * 1.查询学员方案配置，捞取当前期别配置信息（核心字段：期别id、教学计划id（签到、获取系统时间使用），培训点id，学号（获取考勤数据使用），期别考勤记录）
     * 2.获取学习token（获取系统时间、签到使用）
     * 3.获取期别实施配置（取签到、签退范围）
     * 4.获取日期组
     * 5.取系统时间，默认初始化勾选当天日期
     */
    // 获取班级信息（需要取班级身上的考勤配置以及面授学习方式id以及教学计划id）
    await this.getCurrentSchemeConfig()

    // 获取学员学习Token
    const applyStudentLearning = new ApplyStudentIssueLearningToken(
      this.periodQualificationId,
      this.currentPeriodConfig.relateTeachPlanLearning.id
    )
    await applyStudentLearning.apply()
    this.studentLearningToken = applyStudentLearning.token

    // 取期别实施配置
    this.periodImplementConfig = new PeriodImplementConfig(this.periodId)
    await this.periodImplementConfig.getPeriodImplementConfig()

    // 获取日期组
    await this.getSignDateGroup()

    if (this.dateGroup.length) {
      // 初始化默认选中并查询当前日期
      // 获取一次系统时间 用于判断当前日期是哪个一个 默认选中当前日期的groupItem
      await this.getServiceTime()

      const now = new Date(this.currentServiceTimestamp)
      // 获取年、月、日
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0') // 月份从0开始，需要加1
      const day = String(now.getDate()).padStart(2, '0')
      // 拼接成后端日期组的格式 方便比较勾选
      const formattedDate = `${year}/${month}/${day}`

      const findDateItem = this.dateGroup.find(item => item.fullDate === formattedDate)

      if (findDateItem) {
        this.chooseDate(findDateItem)
      } else {
        this.chooseDate(this.dateGroup[0])
      }
    }
  }

  /**
   * 分页查询当前选中日期下的课程数据
   * @param page 分页
   * @param isPolling 是否滚动加载
   */
  async queryCurrentCourseItem(page: Page, isPolling = false) {
    const request = new PlanItemAttendanceRequest()
    const scope = this.currentDate.toDateScope()
    request.qualificationId = this.periodQualificationId
    request.beginTime = scope.begin
    request.endTime = scope.end
    const res = await MsCourseLearningQuery.pagePlanItemAttendanceInMyself({ page, request })

    const resList = res?.data?.currentPageData?.length
      ? res.data.currentPageData
      : new Array<PlanItemAttendanceResponse>()
    if (isPolling) {
      resList.map((item: PlanItemAttendanceResponse) => {
        this.courseList.push(CourseSignItem.from(item))
      })
    } else {
      this.courseList = resList.map((item: PlanItemAttendanceResponse) => {
        return CourseSignItem.from(item)
      })

      // 获取当日签到统计
      if (res?.data?.totalPageSize && res.data.totalPageSize > 1) {
        await this.statisticCurrentSignCount(res.data.totalSize)
      } else {
        await this.statisticCurrentSignCount()
      }
    }

    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
  }

  /**
   * 选中日期
   * @param date 日期
   */
  chooseDate(date: DateGroupItem) {
    this.currentDate = date
  }

  /**
   * 定位点签到
   * @param item 课程项
   */
  async signInWithLocation(item: CourseSignItem) {
    const request = new StudentClockInRequest()
    request.planId = this.planId
    if (this.periodImplementConfig.signInConfig.checkInFrequency == CheckInFrequencyEnum.halfDay) {
      request.planItemId = '-1'
      request.planItemGroupId = item.planGroupId
    } else {
      request.planItemId = item.courseId
      request.planItemGroupId = '-1'
    }
    request.studentLearningToken = this.studentLearningToken
    request.lat = this.currentPoint.latitude
    request.lng = this.currentPoint.longitude

    const res = await MsTeachingplanStudentattendance.clockIn(request)

    if (res.status && res.status.isSuccess()) {
      if (res.data.code == SignResCodeEnum.success) {
        return Promise.resolve(new ResponseStatus(SignResCodeEnum.success, ''))
      } else {
        return Promise.reject(new ResponseStatus(res.data.code as SignResCodeEnum, '签到失败'))
      }
    } else {
      return Promise.reject(new ResponseStatus(SignResCodeEnum.error, '系统异常'))
    }
  }

  /**
   * 定位点签退
   * @param item 课程项
   */
  async signOutWithLocation(item: CourseSignItem) {
    const request = new StudentClockOutRequest()
    request.planId = this.planId
    if (this.periodImplementConfig.signOutConfig.checkInFrequency == CheckInFrequencyEnum.halfDay) {
      request.planItemId = '-1'
      request.planItemGroupId = item.planGroupId
    } else {
      request.planItemId = item.courseId
      request.planItemGroupId = '-1'
    }
    request.studentLearningToken = this.studentLearningToken
    request.lat = this.currentPoint.latitude
    request.lng = this.currentPoint.longitude

    const res = await MsTeachingplanStudentattendance.clockOut(request)

    if (res.status && res.status.isSuccess()) {
      if (res.data.code == SignResCodeEnum.success) {
        return Promise.resolve(new ResponseStatus(SignResCodeEnum.success, ''))
      } else {
        return Promise.reject(new ResponseStatus(res.data.code as SignResCodeEnum, '签退失败'))
      }
    } else {
      return Promise.reject(new ResponseStatus(SignResCodeEnum.error, '系统异常'))
    }
  }

  /**
   * 快速签到/签退（不校验地点）
   * @param planId 教学计划id
   * @param planItemId 教学计划项id
   * @param planGroupId 教学计划项组id
   * @param signType 签到类型
   */
  async quickSign(planId: string, planItemId: string, planGroupId: string, signType: SignTypeEnum) {
    const signConfig =
      signType == SignTypeEnum.signOut
        ? this.periodImplementConfig.signOutConfig
        : this.periodImplementConfig.signInConfig
    const request = new StudentClockOutRequest()
    request.planId = planId
    if (signConfig.checkInFrequency === CheckInFrequencyEnum.halfDay) {
      request.planItemId = '-1'
      request.planItemGroupId = planGroupId
    } else {
      request.planItemId = planItemId
      request.planItemGroupId = '-1'
    }
    request.studentLearningToken = this.studentLearningToken

    // 无需校验定位直接拿期别培训点经纬度签到
    request.lat = this.signLocation.latitude
    request.lng = this.signLocation.longitude

    let res: Response<ClockResponse>

    if (signType === SignTypeEnum.signOut) {
      res = await MsTeachingplanStudentattendance.clockOut(request)

      if (res.status && res.status.isSuccess()) {
        if (res.data.code == SignResCodeEnum.success) {
          return Promise.resolve(new ResponseStatus(SignResCodeEnum.success, ''))
        } else {
          return Promise.reject(new ResponseStatus(res.data.code as SignResCodeEnum, '签退失败'))
        }
      } else {
        return Promise.reject(new ResponseStatus(SignResCodeEnum.error, '系统异常'))
      }
    } else {
      res = await MsTeachingplanStudentattendance.clockIn(request)

      if (res.status && res.status.isSuccess()) {
        if (res.data.code == SignResCodeEnum.success) {
          return Promise.resolve(new ResponseStatus(SignResCodeEnum.success, ''))
        } else {
          return Promise.reject(new ResponseStatus(res.data.code as SignResCodeEnum, '签退失败'))
        }
      } else {
        return Promise.reject(new ResponseStatus(SignResCodeEnum.error, '系统异常'))
      }
    }
  }

  /**
   * 根据服务器时间启动倒计时
   */
  async startClockWithService() {
    if (this.isClock) {
      console.error('已有倒计时计时器在执行')
      return
    }
    await this.getServiceTime()
    this.isClock = true
    this.interval = setInterval(() => {
      if (this.currentServiceTimestamp) {
        this.currentServiceTimestamp += 1000
        // 更新课程倒计时
        this.updateCourseClockTime(this.currentServiceTimestamp, this.fixTipTimestamp)
      }
    }, 1000)
  }

  /**
   * 根据服务器时间更新倒计时
   */
  async updateClock() {
    this.stopClock()
    await this.startClockWithService()
  }

  /**
   * 停止倒计时
   */
  stopClock() {
    clearInterval(this.interval)
    this.interval = undefined
    this.isClock = false
  }

  /**
   * 统计当日签到数
   * @param totalNum 当日课程总数 （如果不传则以当前的课程列表进行统计）
   */
  private async statisticCurrentSignCount(totalNum?: number) {
    /**
     * 后端没有开当日签到的统计口，依赖于前端自己取当日列表进行统计，故需要把列表所有数据全查出来
     * 后端说需求最多就10条，到时候超过200条找他们问罪
     */
    let statisticList = new Array<CourseSignItem>()
    if (totalNum) {
      const request = new PlanItemAttendanceRequest()
      const scope = this.currentDate.toDateScope()
      request.qualificationId = this.periodQualificationId
      request.beginTime = scope.begin
      request.endTime = scope.end

      const page = new Page()
      page.pageNo = 1
      page.pageSize = totalNum

      const res = await MsCourseLearningQuery.pagePlanItemAttendanceInMyself({ page, request })

      if (res?.data?.currentPageData?.length) {
        statisticList = res.data.currentPageData.map((item: PlanItemAttendanceResponse) => {
          return CourseSignItem.from(item)
        })
      }
    } else {
      statisticList = this.courseList
    }

    let currentCheck = 0
    let currentTotalCheck = 0
    statisticList.map((item: CourseSignItem) => {
      if (item.signInLog.timeSignStatus != SignStatusEnum.unSign) {
        currentTotalCheck++
        if (item.signInLog.timeSignStatus === SignStatusEnum.signed) {
          currentCheck++
        }
      }
      if (item.signOutLog.timeSignStatus != SignStatusEnum.unSign) {
        currentTotalCheck++
        if (item.signOutLog.timeSignStatus === SignStatusEnum.signed) {
          currentCheck++
        }
      }
    })
    this.currentDateSignNum = currentCheck
    this.currentDateTotalSignNum = currentTotalCheck

    // 刷新当日完成次数
    const studentLog = new StudentFaceStudyLog(this.studentNo)
    await studentLog.queryStudentFaceStudyLog()
    // this.periodSignLog.currentSignNum = studentLog.attendance.completedNum
    this.periodSignLog.totalSignNum = studentLog.attendance.totalNum
    // 最少考勤次数：要求考勤率 * 总需考勤次数并向上取证
    this.periodSignLog.leastSignNum = Math.ceil(
      (studentLog.attendance.totalNum * studentLog.attendanceRequireRate) / 100
    )

    // 已完成签到次数由于查学习记录会有延迟，后端新开了接口查询
    const staticReq = new CountSignInRecordRequest()
    staticReq.qualificationId = this.periodQualificationId
    staticReq.isInvalid = true

    const staticRes = await MsSchemeLearning.getStudentPlanSignRecordCountInMyself(staticReq)
    this.periodSignLog.currentSignNum = staticRes?.data || 0
  }

  /**
   * 获取签到日历列表
   */
  private async getSignDateGroup() {
    const res = await MsCourseLearningQuery.statisticsIssueByDateInMyself(this.periodQualificationId)
    if (res?.data?.length) {
      this.dateGroup = res.data.map((item: string) => {
        const result = new DateGroupItem()
        // 处理日期格式 （safari不支持-的日期格式）
        result.fullDate = item.replaceAll('-', '/')

        const date = new Date(result.fullDate)
        result.showDate = `${date.getMonth() + 1}月${date.getDate()}日`
        return result
      })
    } else {
      this.dateGroup = new Array<DateGroupItem>()
    }
  }

  /**
   * 获取系统时间（依赖于学员学习token）
   */
  private async getServiceTime() {
    // 没有教学计划id不查
    if (!this.planId) {
      console.error('教学计划id为空！')
      return
    }
    const request = new GetServerTimeRequest()
    request.studentLearningToken = this.studentLearningToken
    request.planId = this.planId
    const serviceTimeRes = await MsTeachingplanAttendance.getServerTime(request)
    this.currentServiceTimestamp = serviceTimeRes?.data
  }

  /**
   * 获取方案信息
   */
  private async getCurrentSchemeConfig() {
    const queryMyTrainClassDetail = new QueryMyTrainClassDetail()
    queryMyTrainClassDetail.studentNo = this.studentNo
    queryMyTrainClassDetail.qualificationId = this.qualificationId
    await queryMyTrainClassDetail.queryTrainClassDetail()

    this.schemeConfig = queryMyTrainClassDetail.trainClassDetail
    this.periodQualificationId = queryMyTrainClassDetail.issueLearningDetail.periodQualificationId
    this.periodId = queryMyTrainClassDetail.issueLearningDetail.periodId
    // 获取当前期别配置
    this.currentPeriodConfig = queryMyTrainClassDetail.queryIssueConfigDetailByIssueId(this.periodId)

    // 获取期别计划id
    this.planId = this.currentPeriodConfig.relateTeachPlanLearning.configId

    // 获取期别面授地点信息
    this.signLocation.longitude = Number(this.currentPeriodConfig.trainingPointLng)
    this.signLocation.latitude = Number(this.currentPeriodConfig.trainingPointLat)

    // 获取考勤配置
    if (queryMyTrainClassDetail.issueLearningDetail.attendance.require) {
      this.periodSignLog.currentSignNum = queryMyTrainClassDetail.issueLearningDetail.attendance.completedNum
      this.periodSignLog.totalSignNum = queryMyTrainClassDetail.issueLearningDetail.attendance.totalNum
      // 最少考勤次数：要求考勤率 * 总需考勤次数并向上取证
      this.periodSignLog.leastSignNum = Math.ceil(
        (queryMyTrainClassDetail.issueLearningDetail.attendance.totalNum *
          queryMyTrainClassDetail.issueLearningDetail.attendanceRequireRate) /
          100
      )
    }
  }

  /**
   * 时间更新
   * @param timestamp 当前时间戳
   * @param fixTipTimestamp 开始前几分钟开始倒计时
   */
  private updateCourseClockTime(timestamp: number, fixTipTimestamp: number) {
    this.courseList.forEach(item => {
      if (item.signInLog.needCountDown) {
        item.signInLog.countDown(timestamp, fixTipTimestamp)
      }
      if (item.signOutLog.needCountDown) {
        item.signOutLog.countDown(timestamp, fixTipTimestamp)
      }
    })
  }

  /**
   * 换算经纬度距离
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number) {
    const result = 6371e3 // meters
    const a1 = (lat1 * Math.PI) / 180
    const a2 = (lat2 * Math.PI) / 180
    const a3 = ((lat2 - lat1) * Math.PI) / 180
    const a4 = ((lon2 - lon1) * Math.PI) / 180
    const a = Math.sin(a3 / 2) * Math.sin(a3 / 2) + Math.cos(a1) * Math.cos(a2) * Math.sin(a4 / 2) * Math.sin(a4 / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    const d = result * c // in meters
    return d
  }
}
