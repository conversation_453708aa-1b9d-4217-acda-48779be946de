import LearningTeacher from './LearningTeacher'

/**
 * 学习中的课程
 */
class LearningCourse {
  /**
   * 课程编号
   */
  courseId = ''
  /**
   * 课程名称
   */
  courseName = ''
  /**
   * 课程计划总章节数
   */
  totalLecture = 0
  /**
   * 已更新章节数
   */
  alreadyUpdateLecture = 0
  /**
   * 课程简介
   */
  description = ''
  /**
   * 课件教师id集合
   */
  teacherList: Array<LearningTeacher> = new Array<LearningTeacher>()
  /**
   * 总时长
   */
  timeTotalLength = 0
  /**
   * 课程学时
   */
  period = 0
  /**
   * 是否支持试听
   */
  supportAudition: boolean
  /**
   * 当前学习的进度
   */
  currentLearningSchedule = 0
  /**
   * 最后学习时间
   */
  lastLearningTime: Date
  /**
   * 课程图片
   */
  iconPath = ''
}

export default LearningCourse
