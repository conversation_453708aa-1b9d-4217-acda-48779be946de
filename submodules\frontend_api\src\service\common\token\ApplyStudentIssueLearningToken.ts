import AbstractApplyToken from '@api/service/common/token/AbstractApplyToken'
import { ResponseStatus } from '@hbfe/common'
import MsStudentLearningV1, { ApplyLearningTokenRequest } from '@api/ms-gateway/ms-studentlearning-v1'

export default class ApplyStudentIssueLearningToken extends AbstractApplyToken {
  // 期别参训资格 id
  private readonly issueQualificationId: string
  // 学习方式 id
  private readonly learningId: string

  /**
   * @param issueQualificationId 期别参训资格 id
   * @param learningId 学习方式 id
   */
  constructor(issueQualificationId: string, learningId: string) {
    super()
    this.issueQualificationId = issueQualificationId
    this.learningId = learningId
  }

  /**
   * 用学习方案换取学员 token
   */
  async apply(): Promise<ResponseStatus> {
    const requestStudentLearningTokenParams = new ApplyLearningTokenRequest()
    requestStudentLearningTokenParams.learningId = this.learningId
    requestStudentLearningTokenParams.qualificationId = this.issueQualificationId
    const result = await MsStudentLearningV1.applyStudentLearningTokenInIssue(requestStudentLearningTokenParams)
    this.token = result.data
    let errorMsg = ''

    if (result.status.code != 200) {
      errorMsg = result.status.getMessage() || result.status?.errors[0]?.message
      result.status.code = result.status.errors[0]?.code
    }
    return new ResponseStatus(result.status.code, errorMsg)
  }
}
