schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取轮播图详情
		@return
	"""
	findById(id:String):RollPictureDetailResponse @NotAuthenticationRequired
	"""获取所有轮播图
		@return
	"""
	listAll:[RollPictureDetailResponse] @NotAuthenticationRequired
	"""通过类别获取轮播图列表
		@return
	"""
	listByType(rollPictureType:RollPictureTypeEnum):[RollPictureDetailResponse] @NotAuthenticationRequired
}
type Mutation {
	"""新增轮播图
		@param dto
		@return
	"""
	create(dto:RollPictureRequest):RollPictureDetailResponse
	"""删除轮播图(设置isDeleted为1)
		@param id
		@return
	"""
	deleteById(id:String):Boolean!
	"""删除轮播图(设置isDeleted为1)
		@param idList
		@return
	"""
	deleteByIdList(idList:[String]):Boolean!
	"""更新轮播图，Id为空时新增
		@param requestList
		@return
	"""
	modify(requestList:[RollPictureRequest]):[RollPictureDetailResponse]
	"""轮播图下移指定偏移量
		@param id
		@param offset
		@return
	"""
	moveDown(id:String,offset:Int!):RollPictureDetailResponse
	"""轮播图上移指定偏移量
		@param id
		@param offset
		@return
	"""
	moveUp(id:String,offset:Int!):RollPictureDetailResponse
	"""更新轮播图
		@param dto
		@return
	"""
	update(dto:RollPictureRequest):RollPictureDetailResponse
}
"""@author: puxf
	@date: 2020/2/3
	@description:
"""
input RollPictureRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.RollPictureRequest") {
	"""主键id 修改时有值 新增时没值"""
	id:String
	"""轮播图类型
		@see com.fjhb.btpx.platform.service.rollpicture.RollPictureTypeEnum
	"""
	type:String
	"""轮播图附件地址"""
	attachmentUrl:String
	"""链接地址"""
	url:String
	"""轮播图描述"""
	description:String
	"""轮播图状态（0-停用 1-启用）"""
	status:Int
	"""轮播图排序"""
	sort:Int
}
"""@author: puxf
	@date: 2021/2/2
	@description: 轮播图
"""
type RollPictureDetailResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.RollPictureDetailResponse") {
	"""id"""
	id:String
	"""轮播图类型
		@see com.fjhb.btpx.platform.service.rollpicture.RollPictureTypeEnum
	"""
	type:String
	"""轮播图附件地址"""
	attachmentUrl:String
	"""链接地址"""
	url:String
	"""轮播图描述"""
	description:String
	"""创建人"""
	creator:String
	"""创建时间"""
	createTime:DateTime
	"""更新时间"""
	updateTime:DateTime
	"""轮播图状态 0-停用 1-启用"""
	status:Int!
	"""轮播图排序"""
	sort:Int!
	"""是否内置"""
	internal:Boolean!
}
"""轮播图类别
	@author: puxf
	@date: 2020/2/2
	@description:
"""
enum RollPictureTypeEnum @type(value:"com.fjhb.btpx.platform.service.rollpicture.RollPictureTypeEnum") {
	"""默认，查询全部"""
	DEFAULT
	"""WEB门户"""
	PORTAL
	"""小程序"""
	MINI_PROGRAM
}

scalar List
