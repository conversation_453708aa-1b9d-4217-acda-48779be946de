/*
 * 操作试题
 */
class QuestionOperation {
  /**
   * @description: 试题删除
   * @param {Array} arr 操作的数组
   * @param {number} idx 选中项下标
   * @return {*}
   */
  doQuestionDelete(arr: Array<any>, idx: number) {
    if (!arr.length) {
      console.error('没有可删除的试题！')
      return
    }
    arr?.splice(idx, 1)
    return arr
  }

  /**
   * @description: 试题上移
   * @param {Array} arr 操作的数组
   * @param {number} idx 选中项下标
   * @return {*}
   */
  doQuestionUp(arr: Array<any>, idx: number) {
    if (arr?.length < 2) {
      console.error('当前情况不支持移动试题！')
      return
    }
    // 当前选中元素下标
    const curIdx = idx
    // 前一个元素的下标
    const preIdx = idx - 1
    const curItem = arr[curIdx]
    arr?.splice(curIdx, 1)
    arr?.splice(preIdx, 0, curItem)
    return arr
  }

  /**
   /**
   * @description: 试题下移
   * @param {Array} arr 操作的数组
   * @param {number} idx 选中项下标
   * @return {*}
   */
  doQuestionDown(arr: Array<any>, idx: number) {
    if (arr?.length < 2) {
      console.error('当前情况不支持移动试题！')
      return
    }
    // 当前选中元素下标
    const curIdx = idx
    // 后一个
    const lastIdx = idx + 1
    const curItem = arr[curIdx]
    arr?.splice(curIdx, 1)
    arr?.splice(lastIdx, 0, curItem)
    return arr
  }
}

export default QuestionOperation
