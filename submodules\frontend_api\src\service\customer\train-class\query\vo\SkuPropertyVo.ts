import SkuVo from '@api/service/customer/train-class/query/vo/SkuVo'
import { SkuPropertyRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { RegionVo } from '@api/service/customer/train-class/query/vo/RegionVo'
import { cloneDeep } from 'lodash'

/**
 * 培训属性
 */
class SkuPropertyVo {
  // region properties

  /**
   *年度，类型为SkuVo[]
   */
  year: SkuVo[] = []
  /**
   *地区，类型为SkuVo[]
   */
  region: SkuVo[] = []
  /**
   * 树状地区，RegionVo[]
   */
  RegionVos: RegionVo[] = []
  /**
   *行业，类型为SkuVo[]
   */
  industry: SkuVo[] = []
  /**
   *科目类型，类型为SkuVo[]
   */
  subjectType: SkuVo[] = []
  /**
   *培训类别，类型为SkuVo[]
   */
  trainingCategory: SkuVo[] = []
  /**
   *培训专业，类型为SkuVo[]
   */
  trainingMajor: SkuVo[] = []
  /**
   * 培训对象，类型为SkuVo
   */
  trainingObject: SkuVo[] = []
  /**
   * 岗位类别，类型为SkuVo
   */
  positionCategory: SkuVo[] = []
  /**
   * 技术等级-新
   */
  jobLevel: SkuVo[] = []
  /**
   * 学段
   */
  learningPhase: SkuVo[] = []
  /**
   * 科目
   */
  discipline: SkuVo[] = []
  /**
   * 证书类型
   */
  certificatesType: SkuVo[] = []
  /**
   * 执业类别
   */
  practitionerCategory: SkuVo[] = []
  /**
   * 学时
   */
  period: SkuVo[] = []
  /**
   * 专题行业
   */
  specialIndustry: SkuVo[] = []
  /**
   * 门户商品地区，类型为SkuVo[]
   */
  regionForPortal: SkuVo[] = []
  /**
   * 门户商品树状地区，RegionVo[]
   */
  regionVoForPortal: RegionVo[] = []

  /**
   * 培训形式
   */
  trainingMode: SkuVo[] = []

  // endregion
  // region methods
  // 过滤培训专业
  filterTrainMajor(cate: SkuVo) {
    this.trainingMajor.forEach((item) => {
      if (cate.skuPropertyValueId == '') {
        item.hidden = false
        return true
      }
      if (item.parentId == cate.skuPropertyValueId) {
        item.hidden = false
      } else {
        item.hidden = true
      }
    })
    this.trainingMajor = cloneDeep(this.trainingMajor)
    // this.trainingMajor = this.trainingMajor.filter(item => item.parentId == cate.skuPropertyValueId)
  }
  /**
   * 转换成请求需要的dto
   */
  convertToDto(): SkuPropertyRequest {
    const skuPro = new SkuPropertyRequest()
    skuPro.year = this.year.map((item: SkuVo) => {
      return item.skuPropertyValueId
    })
    // skuPro.region = this.region.map((item: SkuVo) => {
    //   return item.skuPropertyValueId
    // })
    skuPro.industry = this.industry.map((item: SkuVo) => {
      return item.skuPropertyValueId
    })
    skuPro.subjectType = this.subjectType.map((item: SkuVo) => {
      return item.skuPropertyValueId
    })
    skuPro.trainingCategory = this.trainingCategory.map((item: SkuVo) => {
      return item.skuPropertyValueId
    })
    skuPro.trainingProfessional = this.trainingMajor.map((item: SkuVo) => {
      return item.skuPropertyValueId
    })
    return skuPro
  }

  // endregion
}
export default SkuPropertyVo
