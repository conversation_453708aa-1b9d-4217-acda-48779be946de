import { Page } from '@hbfe/common'
import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
import ReturnOrderResponseVo from '@api/service/diff/management/qztg/trade/order/model/ReturnOrderResponseVo'
import QueryRefundListInTrainingChannel from '@api/service/management/trade/single/order/query/QueryRefundListInTrainingChannel'

/**
 * 查询个人退款单 - 专题管理员
 */
export default class QueryQztgRefundListInTrainingChannel extends QueryRefundListInTrainingChannel {
  /**
   * 获取退款单列表
   */
  async queryFjzjRefundOrderList(page: Page, request: ReturnOrderRequestVo): Promise<Array<ReturnOrderResponseVo>> {
    const data = await this.queryRefundOrderList(page, request)
    return data.map((item) => {
      const returnOrderResponseVo = new ReturnOrderResponseVo()
      Object.assign(returnOrderResponseVo, item)
      return returnOrderResponseVo
    })
  }
}
