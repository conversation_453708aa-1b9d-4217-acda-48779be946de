import MockUtil from '@api/service/common/utils/MockUtil'
import PeriodConfig from '@api/service/management/implement/models/PeriodConfig'
import AttendanceConfigDto from '@api/service/common/implement/models/AttendanceConfigDto'
import { Page } from '@hbfe/common'
import MsSchemeLearningQuery, {
  IssueStudyConfigResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

/**
 * 考勤配置
 */
export default class AttendanceConfig {
  /**
   * 方案id
   */
  private schemeId: string = undefined

  /**
   * 签到配置
   */
  signIn: AttendanceConfigDto = new AttendanceConfigDto()

  /**
   * 签退配置
   */
  signOut: AttendanceConfigDto = new AttendanceConfigDto()

  /**
   * 期别签到配置列表
   */
  periodInfoList: Array<PeriodConfig> = new Array<PeriodConfig>()

  /**
   * 是否开启考勤
   */
  isOpenAttendanceConfig: boolean = undefined

  /**
   * @param schemeId 方案id
   */
  constructor(schemeId?: string) {
    if (schemeId) {
      this.schemeId = schemeId
    }
  }

  /**
   * 获取考勤配置
   */
  async getAttendanceConfig() {
    MockUtil(this.signIn)
    MockUtil(this.signOut)
    this.isOpenAttendanceConfig = true
  }

  /**
   * 分页查询方案下期别配置项
   */
  async pagePeriodAttendanceConfig(page: Page) {
    const res = await MsSchemeLearningQuery.pageIssueStudyConfigInServicer({ page, schemeId: this.schemeId })
    if (res?.data?.currentPageData?.length) {
      this.periodInfoList = res.data.currentPageData.map((item: IssueStudyConfigResponse) => {
        return PeriodConfig.from(item)
      })
    } else {
      this.periodInfoList = new Array<PeriodConfig>()
    }

    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize

    return
  }

  /**
   * 保存考勤配置
   */
  async saveAttendanceConfig() {
    // todo
  }
}
