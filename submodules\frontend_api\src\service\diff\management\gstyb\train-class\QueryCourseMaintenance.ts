import { Response, Page, ResponseStatus } from '@hbfe/common'
import CourseMaintenanceQueryVo from '@api/service/diff/management/gstyb/train-class/model/CourseMaintenanceQueryVo'
import CourseMaintenanceInfoVo from '@api/service/diff/management/gstyb/train-class/model/CourseMaintenanceInfoVo'
import PlatformJxjypxtyptSchool, {
  CreCourseSubjectResponse,
  ImportCourseSubjectRequest
} from '@api/diff-gateway/platform-jxjypxtypt-gstyb-school'
import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'

import QueryCourseMaintenanceMain from '@api/service/diff/management/byzj/train-class/QueryCourseMaintenance'

class QueryCourseMaintenance extends QueryCourseMaintenanceMain {
  /**
   * 分页查询公需课课程维护列表
   */
  async pageCourseSubject(page: Page, params: CourseMaintenanceQueryVo) {
    return await PlatformJxjypxtyptSchool.pageCourseSubjectInServicer({
      page,
      request: params.toDto()
    })
  }

  /**
   * 导入公需课课程接口
   */
  async importCourseSubjectByExcel(filePath: string, fileName: string) {
    const importCourseSubjectRequest = new ImportCourseSubjectRequest()
    importCourseSubjectRequest.filePath = filePath
    importCourseSubjectRequest.fileName = fileName
    return await PlatformJxjypxtyptSchool.importCourseSubjectByExcelInServicer(importCourseSubjectRequest)
  }
}

export default QueryCourseMaintenance
