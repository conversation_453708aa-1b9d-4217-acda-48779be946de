import ReceiveAccountDetailVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountDetailVo'
import { PayAccountTypeEnum } from '@api/service/management/trade-info-config/enums/PayAccountTypeEnum'
import {
  CIBPayEncryptionKeyDataResponse,
  ReceiveAccountConfigResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { CCBPayEncryptionKeyDataResponse } from '../../mutation/vo/CCBPayEncryptionKeyData'

export class JSPayReceiveAccountDetailVo extends ReceiveAccountDetailVo {
  /**
   * 账号类型
   */
  paymentChannelId: PayAccountTypeEnum = undefined

  /**
   * 商户柜台代码
   */
  posId = ''
  /**
   * 分行代码
   */
  branchId = ''
  /**
   * 建行网银支付接口的公钥
   */
  jsPublicKey = ''
  /**
   * 建行的操作员账号不能为空
   */
  operator = ''
  /**
   * 建行操作员的登陆密码
   */
  password = ''
  /**
   * 是否使用防钓鱼,如果1表示使用防钓鱼接口,其他则不使用
   */
  phishing = 0
  /**
   * 小程序/公众号的 APPID 当前调起支付的小程序/公众号 APPID
   */
  jsSubAppid = ''
  /**
   * 文件证书路径
   */
  certFilePath = ''
  /**
   * 文件证书密码
   */
  certPassword = ''

  from(res: ReceiveAccountConfigResponse) {
    this.accountType = res.accountType
    this.accountNo = res.accountNo
    this.accountName = res.name
    this.taxPayerId = res.taxPayerId
    this.refundWay = res.returnType
    this.qrScanPrompt = res.qrScanPrompt
    this.paymentChannelId = res.paymentChannelId as PayAccountTypeEnum
    if (res.encryptionKeyData.encryptionKeyType == 'CCB_PAY') {
      const temp = res.encryptionKeyData as CCBPayEncryptionKeyDataResponse
      this.posId = temp.POS_ID
      this.branchId = temp.BRANCH_ID
      this.jsPublicKey = temp.PUBLIC_KEY
      this.operator = temp.OPERATOR
      this.password = temp.PASSWORD
      this.phishing = Number(temp.PHISHING)
      this.jsSubAppid = temp.SUB_APP_ID
      this.certFilePath = temp.CERT_FILE_PATH
      this.certPassword = temp.CERT_PASS_WORD
    }
  }
}
