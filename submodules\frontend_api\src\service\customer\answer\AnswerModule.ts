import store from '@/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import MutationAnswerFactor from './MutationAnswerFactor'
import QueryAnswerFactor from './QueryAnswerFactor'

/**
 * 作答状态层
 */
@Module({
  name: 'AnswerModule',
  dynamic: true,
  namespaced: true,
  store
})
class AnswerModule extends VuexModule {
  /**
   * @description: 获取作答业务工厂
   */

  get mutationAnswerFactor() {
    return MutationAnswerFactor
  }

  /**
   * @description: 获取查询业务工厂
   */
  get queryAnswerFactor() {
    return QueryAnswerFactor
  }
}
export default getModule(AnswerModule)
