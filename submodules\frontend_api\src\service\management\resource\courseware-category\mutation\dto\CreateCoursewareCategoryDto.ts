import MsCourseResourceV1 from '@api/ms-gateway/ms-course-resource-v1'
import { ResponseStatus } from '@hbfe/common'

class CreateCoursewareCategoryDto {
  /*
   分类名称
   */
  name: string

  /*
   父分类 id
   */
  parentId: string

  sort = 1

  description: string

  async save(): Promise<ResponseStatus> {
    const { status } = await MsCourseResourceV1.createCoursewareCategory(this)
    return new ResponseStatus(status.code, status.getMessage())
  }
  //用于重置表单数据
  reset() {
    this.parentId = ''
    this.name = ''
  }
}

export default CreateCoursewareCategoryDto
