"""独立部署的微服务,K8S服务名:ms-trade-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取商品详情"""
	getCommoditySkuInSubProject(commoditySkuId:String):CommoditySkuBackstageResponse
	"""获取发票详情"""
	getOfflineInvoiceInSubProject(invoiceId:String):OfflineInvoiceResponse
	"""获取订单详情"""
	getOrderInServicer(orderNo:String!):OrderResponse @optionalLogin
	"""获取订单详情"""
	getOrderInSubProject(orderNo:String!):OrderResponse @optionalLogin
	"""获取退货单详情"""
	getReturnOrderInServicer(returnOrderNo:String):ReturnOrderResponse @optionalLogin
	"""获取退货单详情"""
	getReturnOrderInSubProject(returnOrderNo:String):ReturnOrderResponse @optionalLogin
	"""获取报表统计合计数据"""
	getTradeReportSummaryInSubProject(request:TradeReportRequest):ReportSummaryResponse @optionalLogin
	"""获取商品价格变更记录
		<p> 如果商品自创建后价格没有发生过变更，该接口返回空数组
	"""
	listCommodityPriceUpdateRecordInSubProject(commoditySkuId:String):[CommodityPriceUpdateRecordResponse]
	"""获取退货原因列表"""
	listReturnReasonInfoInSubProject:[ReturnReasonInfoResponse] @optionalLogin
	"""获取服务商收益统计列表"""
	listServicerProfitReportInSubProject(request:TradeReportRequest):[ServicerProfitFormResponse] @optionalLogin
	"""商品分页查询"""
	pageCommoditySkuInSubProject(page:Page,queryRequest:CommoditySkuRequest,sortRequest:[CommoditySkuSortRequest]):CommoditySkuBackstageResponsePage @page(for:"CommoditySkuBackstageResponse")
	"""分页查询发票"""
	pageOfflineInvoiceInSubProject(page:Page,request:OfflineInvoiceRequest,sort:[OfflineInvoiceSortRequest]):OfflineInvoiceResponsePage @page(for:"OfflineInvoiceResponse")
	"""分页获取订单"""
	pageOrderInServicer(page:Page,request:OrderRequest,sortRequest:[OrderSortRequest]):OrderResponsePage @page(for:"OrderResponse") @optionalLogin
	"""分页获取订单"""
	pageOrderInSubProject(page:Page,request:OrderRequest,sortRequest:[OrderSortRequest]):OrderResponsePage @page(for:"OrderResponse") @optionalLogin
	"""退货单分页查询"""
	pageReturnOrderInServicer(page:Page,request:ReturnOrderRequest,sort:[ReturnSortRequest]):ReturnOrderResponsePage @page(for:"ReturnOrderResponse") @optionalLogin
	"""退货单分页查询"""
	pageReturnOrderInSubProject(page:Page,request:ReturnOrderRequest,sort:[ReturnSortRequest]):ReturnOrderResponsePage @page(for:"ReturnOrderResponse") @optionalLogin
	"""获取线下发票开票总金额"""
	statisticOfflineInvoiceInSubProject(request:OfflineInvoiceRequest):OfflineInvoiceStatisticResponse
	"""获取订单总金额、总数量"""
	statisticOrderInServicer(request:OrderRequest):OrderStatisticResponse @optionalLogin
	"""获取订单总金额、总数量"""
	statisticOrderInSubProject(request:OrderRequest):OrderStatisticResponse @optionalLogin
	"""获取退货单总数量、退款总金额"""
	statisticReturnOrderInServicer(request:ReturnOrderRequest):ReturnOrderStatisticResponse @optionalLogin
	"""获取退货单总数量、退款总金额"""
	statisticReturnOrderInSubProject(request:ReturnOrderRequest):ReturnOrderStatisticResponse @optionalLogin
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input BigDecimalScopeRequest @type(value:"com.fjhb.ms.query.commons.BigDecimalScopeRequest") {
	begin:BigDecimal
	end:BigDecimal
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
"""商品查询条件
	<AUTHOR>
	@date 2022/01/25
"""
input CommoditySkuRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.request.CommoditySkuRequest") {
	"""商品id"""
	commoditySkuIdList:[String]
	"""商品名称（精确匹配）"""
	saleTitleList:[String]
	"""商品名称（模糊查询）"""
	saleTitleMatchLike:String
	"""要从查询结果中剔除的商品ID集合"""
	notShowCommoditySkuIdList:[String]
	"""商品售价，精确匹配"""
	price:Double
	"""商品原价，范围查询"""
	originalPriceScope:DoubleScopeRequest
	"""商品优惠价，范围查询"""
	specialPriceScope:DoubleScopeRequest
	"""是否启用优惠价"""
	enableSpecialPrice:Boolean
	"""商品上下架信息"""
	onShelveRequest:OnShelveRequest
	"""电子劳动合同产品查询参数"""
	electronicLaborContractProduct:ElectronicLaborContractProductRequest
	"""商品sku属性查询"""
	skuPropertyRequest:SkuPropertyRequest
	"""服务商ID"""
	severcerIdList:[String]
}
"""商品排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input CommoditySkuSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.request.CommoditySkuSortRequest") {
	"""用来排序的字段"""
	sortField:CommoditySkuSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""电子劳动合同产品信息
	<AUTHOR>
"""
input ElectronicLaborContractProductRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.request.nested.ElectronicLaborContractProductRequest") {
	"""电子劳动合同产品ID"""
	productIdList:[String]
}
"""商品上下架相关查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input OnShelveRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.request.nested.OnShelveRequest") {
	"""商品上下架状态
		<br> 0:已下架 1：已上架
	"""
	onShelveStatus:Int
}
"""商品查询条件
	<AUTHOR>
	@date 2022/01/25
"""
input CommoditySkuRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.request.CommoditySkuRequest") {
	"""商品id"""
	commoditySkuIdList:[String]
	"""商品Sku名称"""
	saleTitle:String
	"""商品sku属性查询"""
	skuProperty:SkuPropertyRequest
}
"""发票关联订单查询参数
	<AUTHOR>
	@date 2022/3/18
"""
input InvoiceAssociationInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.request.InvoiceAssociationInfoRequest") {
	"""关联订单类型
		0:订单号
		1:批次单号
		@see AssociationTypes
	"""
	associationType:Int
	"""订单号 | 批次单号"""
	associationIdList:[String]
	"""单位买家单位ID"""
	unitBuyerUnitIdList:[String]
	"""单位买家经办人ID"""
	unitBuyerAgentUserIdList:[String]
	"""收款账号"""
	receiveAccountIdList:[String]
}
"""商品sku属性查询参数
	<AUTHOR>
"""
input SkuPropertyRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.request.skuProperty.SkuPropertyRequest") {
	"""套餐类型
		<p> single：单份 combo：套餐
	"""
	comboType:[String]
}
"""线下发票查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.request.OfflineInvoiceRequest") {
	"""发票ID集合"""
	invoiceIdList:[String]
	"""发票基本信息"""
	basicData:OfflineInvoiceBasicDataRequest
	"""发票关联订单查询参数"""
	associationInfo:InvoiceAssociationInfoRequest
	"""发票配送信息"""
	invoiceDeliveryInfo:OfflineInvoiceDeliveryInfoRequest
}
"""发票排序条件
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.request.OfflineInvoiceSortRequest") {
	"""用于排序的字段"""
	field:OfflineInvoiceSortField
	"""正序或倒序"""
	policy:SortPolicy1
}
"""配送地址信息
	<AUTHOR>
	@date 2022/05/07
"""
input DeliveryAddressRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.request.nested.DeliveryAddressRequest") {
	"""收件人"""
	consignee:String
}
"""配送状态变更时间查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input DeliveryStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.request.nested.DeliveryStatusChangeTimeRequest") {
	"""未就绪"""
	unReady:DateScopeRequest
	"""已就绪"""
	ready:DateScopeRequest
	"""已配送"""
	shipped:DateScopeRequest
	"""已自取"""
	taken:DateScopeRequest
}
"""快递信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input ExpressRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.request.nested.ExpressRequest") {
	"""快递单号"""
	expressNo:String
}
"""发票开票状态变更时间记录查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input InvoiceBillStatusChangTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.request.nested.InvoiceBillStatusChangTimeRequest") {
	"""发票申请开票时间"""
	unBillDateScope:DateScopeRequest
	"""发票开票时间"""
	successDateScope:DateScopeRequest
}
"""线下发票基本信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.request.nested.OfflineInvoiceBasicDataRequest") {
	"""发票类型
		1:电子发票 2:纸质发票
		@see InvoiceTypes
	"""
	invoiceTypeList:[Int]
	"""发票种类
		1:普通发票 2:增值税普通发票 3:增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategory:[Int]
	"""发票状态
		1:正常
		2:作废
		@see InvoiceStatus
	"""
	invoiceStatus:Int
	"""发票状态变更时间记录"""
	invoiceStatusChangeTime:OfflineInvoiceStatusChangeTimeRequest
	"""发票开票状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	billStatusList:[Int]
	"""发票开票状态变更时间记录"""
	billStatusChangTime:InvoiceBillStatusChangTimeRequest
	"""发票是否冻结"""
	freeze:Boolean
	"""发票号集合"""
	invoiceNoList:[String]
	"""商品id集合"""
	commoditySkuIdList:[String]
}
"""线下发票配送信息
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceDeliveryInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.request.nested.OfflineInvoiceDeliveryInfoRequest") {
	"""配送状态
		0:未就绪 1：已就绪 2：已自取 3：已配送
		@see OfflineDeliveryStatus
	"""
	deliveryStatusList:[Int]
	"""配送状态变更时间记录 or 匹配
		0:未就绪 1：已就绪 2：已自取 3：已配送
		key值 {@link OfflineDeliveryStatus}
	"""
	deliveryStatusChangeTime:DeliveryStatusChangeTimeRequest
	"""配送方式
		0:无 1：自取 2：快递
		@see OfflineShippingMethods
	"""
	shippingMethodList:[Int]
	"""快递信息"""
	express:ExpressRequest
	"""自取信息"""
	takeResult:TakeResultRequest
	"""配送地址信息"""
	deliveryAddress:DeliveryAddressRequest
}
"""发票状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input OfflineInvoiceStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.request.nested.OfflineInvoiceStatusChangeTimeRequest") {
	"""正常"""
	normal:DateScopeRequest
	"""作废"""
	invalid:DateScopeRequest
}
"""取件信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input TakeResultRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.request.nested.TakeResultRequest") {
	"""领取人"""
	takePerson:String
}
"""订单查询参数
	<AUTHOR>
	@date 2022/01/26
"""
input OrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.request.OrderRequest") {
	"""订单号集合"""
	orderNoList:[String]
	"""子订单号集合"""
	subOrderNoList:[String]
	"""子订单退货状态
		0: 未退货
		1: 退货申请中
		2: 退货中
		3: 退货成功
		4: 退款中
		5: 退款成功
		@see SubOrderReturnStatus
	"""
	subOrderReturnStatus:[Int]
	"""订单基本信息查询参数"""
	orderBasicData:OrderBasicDataRequest
	"""订单支付信息查询"""
	payInfo:OrderPayInfoRequest
	"""单位买家单位ID"""
	unitBuyerUnitIdList:[String]
	"""发货商品信息"""
	deliveryCommodity:CommoditySkuRequest1
	"""订单服务商信息"""
	orderServicers:OrderServicersRequest
}
"""订单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input OrderSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.request.OrderSortRequest") {
	"""需要排序的字段"""
	field:OrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""订单基本信息查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input OrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.request.nested.OrderBasicDataRequest") {
	"""订单类型
		1:常规订单 2:批次关联订单
		@see com.fjhb.domain.trade.api.order.consts.OrderTypes
	"""
	orderType:Int
	"""批次单号"""
	batchOrderNoList:[String]
	"""订单状态
		<br> 1:正常 2：交易完成 3：交易关闭
		@see OrderStatus
	"""
	orderStatusList:[Int]
	"""订单支付状态
		<br> 0:未支付 1：支付中 2：已支付
		@see com.fjhb.domain.trade.api.order.consts.OrderPaymentStatus
	"""
	orderPaymentStatusList:[Int]
	"""订单发货状态
		<br> 0:未发货 1：发货中 2：已发货
		@see com.fjhb.domain.trade.api.order.consts.OrderDeliveryStatus
	"""
	orderDeliveryStatusList:[Int]
	"""订单状态变更时间"""
	orderStatusChangeTime:OrderStatusChangeTimeRequest
	"""购买渠道
		<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelTypesList:[Int]
	"""终端
		<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
		@see PurchaseChannelTerminalCodes
	"""
	terminalCodeList:[String]
	"""订单价格范围
		<br> 查询非0元订单 begin填0.01
	"""
	orderAmountScope:BigDecimalScopeRequest
}
"""订单支付信息相关查询参数
	<AUTHOR>
	@date 2022/01/27
"""
input OrderPayInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.request.nested.OrderPayInfoRequest") {
	"""收款账号ID"""
	receiveAccountIdList:[String]
	"""交易流水号"""
	flowNoList:[String]
	"""付款类型
		1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
	"""
	paymentOrderTypeList:[Int]
}
"""订单服务商查询参数
	<AUTHOR>
"""
input OrderServicersRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.request.nested.OrderServicersRequest") {
	"""合同签订服务商ID"""
	contractProviderIdList:[String]
}
"""订单状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input OrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.request.nested.OrderStatusChangeTimeRequest") {
	"""订单处于正常状态时间范围(创建时间范围)"""
	normalDateScope:DateScopeRequest
	"""订单创建时间范围"""
	completedDatesScope:DateScopeRequest
}
"""退货单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.request.ReturnOrderRequest") {
	"""退货单号"""
	returnOrderNoList:[String]
	"""基本信息"""
	basicData:ReturnOrderBasicDataRequest
	"""审批信息"""
	approvalInfo:ReturnOrderApprovalInfoRequest
	"""退货商品名称（精确匹配）"""
	returnCommoditySaleTitle:String
	"""sku属性"""
	skuProperty:SkuPropertyRequest
	"""退货商品id集合"""
	returnCommoditySkuIdList:[String]
	"""退款商品id集合"""
	refundCommoditySkuIdList:[String]
	"""退货单关联子订单查询参数"""
	subOrderInfo:SubOrderInfoRequest
	"""服务商"""
	servicers:ReturnOrderServicersRequest
}
"""订单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input ReturnSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.request.ReturnSortRequest") {
	"""需要排序的字段"""
	field:ReturnOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""退货单关联子订单的主订单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input OrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.request.nested.OrderInfoRequest") {
	"""订单号集合"""
	orderNoList:[String]
	"""关联批次单号"""
	batchOrderNoList:[String]
	"""单位买家单位ID集合"""
	unitBuyerUnitIdList:[String]
	"""收款账号ID"""
	receiveAccountIdList:[String]
	"""原始订单交易流水号"""
	flowNoList:[String]
	"""购买渠道
		<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelTypesList:[Int]
	"""终端
		<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
		@see PurchaseChannelTerminalCodes
	"""
	terminalCodeList:[String]
}
"""退货单关闭信息
	<AUTHOR>
	@date 2022年4月11日 11:33:35
"""
input ReturnCloseReasonRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.request.nested.ReturnCloseReasonRequest") {
	"""退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
		@see ReturnOrderCloseTypes
	"""
	closeTypeList:[Int]
}
"""退货单审批信息查询参数
	<AUTHOR>
	@date 2022/03/18
"""
input ReturnOrderApprovalInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderApprovalInfoRequest") {
	"""审批时间"""
	approveTime:DateScopeRequest
}
"""退货单基本信息查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderBasicDataRequest") {
	"""退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)"""
	returnOrderStatus:[Int]
	"""退货单申请来源类型
		SUB_ORDER
		BATCH_RETURN_ORDER
		@see ReturnOrderApplySourceTypes
	"""
	applySourceType:String
	"""来源ID集合"""
	applySourceIdList:[String]
	"""退货单关闭信息"""
	returnCloseReason:ReturnCloseReasonRequest
	"""退货单状态变更时间"""
	returnStatusChangeTime:ReturnOrderStatusChangeTimeRequest
	"""退款金额范围
		<br> 查询非0元  begin填0.01
	"""
	refundAmountScope:BigDecimalScopeRequest
}
"""订单服务商查询参数
	<AUTHOR>
"""
input ReturnOrderServicersRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderServicersRequest") {
	"""合同签订服务商ID"""
	contractProviderIdList:[String]
}
"""退货单状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderStatusChangeTimeRequest") {
	"""申请退货时间"""
	applied:DateScopeRequest
	"""退货单完成时间
		<br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
	"""
	returnCompleted:DateScopeRequest
}
"""<AUTHOR>
	@date 2022/03/24
"""
input SubOrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.request.nested.SubOrderInfoRequest") {
	"""子订单号集合"""
	subOrderNoList:[String]
	"""订单查询参数"""
	orderInfo:OrderInfoRequest
}
"""商品开通统计报表查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input TradeReportRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.tradereport.gateway.graphql.request.TradeReportRequest") {
	"""交易时间范围"""
	tradeTime:DateScopeRequest
	"""买家所在地区路径"""
	buyerAreaPath:[String]
	"""商品查询条件"""
	commoditySku:CommoditySkuRequest12
}
"""商品查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input CommoditySkuRequest12 @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.tradereport.gateway.graphql.request.nested.CommoditySkuRequest") {
	"""商品Sku名称"""
	saleTitle:String
	"""商品sku属性查询"""
	skuProperty:SkuPropertyRequest
}
type UserModel @type(value:"com.fjhb.ms.trade.query.common.model.UserModel") {
	userId:String
}
"""商品可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum CommoditySkuSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.request.nested.CommoditySkuSortField") {
	"""上架时间"""
	ON_SHELVE_TIME
	"""最新编辑时间"""
	LAST_EDIT_TIME
	"""商品销售数"""
	SALE_TOTAL_NUMBER
}
"""商品变更记录
	<AUTHOR>
"""
type CommodityPriceUpdateRecordResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.response.CommodityPriceUpdateRecordResponse") {
	"""原价变更记录
		<p> 如果原价没有发生变化 该值为Null
	"""
	originalPrice:NumberUpdateRecord
	"""优惠价价变更记录
		<p> 如果优惠价价没有发生变化 该值为Null
	"""
	specialPrice:NumberUpdateRecord
	"""是否展示原价
		<p> 如果是否展示原价没有发生变化 该值为Null
	"""
	showPrice:SwitchUpdateRecord
	"""是否启用优惠价
		<p> 如果是否展示原价没有发生变化 该值为Null
	"""
	enableSpecialPrice:SwitchUpdateRecord
	"""修改时间"""
	updateTime:DateTime
	"""修改人用户ID"""
	modifierUserId:String
}
"""后台商品返回值模型
	<AUTHOR>
	@date 2022/01/25
"""
type CommoditySkuBackstageResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.response.CommoditySkuBackstageResponse") {
	"""商品id"""
	commoditySkuId:String
	"""商品基本信息"""
	commodityBasicData:CommodityBasicDataResponse
	"""商品描述信息"""
	description:CommodityDescriptionResponse
	"""商品属性信息"""
	skuProperty:CommoditySkuPropertyResponse
	"""上下架信息"""
	onShelve:OnShelveResponse
	"""商品所有渠道的配置信息"""
	commodityPurchaseChannelConfig:CommodityPurchaseChannelConfigResponse
	"""商品关联资源信息"""
	resource:ResourceResponse
	"""资源数量"""
	resourceQuality:Long
	"""库存"""
	stocks:Int
	"""最后编辑时间"""
	commodityLastEditTime:DateTime
	"""最后编辑用户"""
	commodityLastEditor:UserResponse
}
"""商品基本信息返回值
	<AUTHOR>
	@date 2022/1/25
"""
type CommodityBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.response.nested.CommodityBasicDataResponse") {
	"""商品销售标题"""
	saleTitle:String
	"""商品原价"""
	originalPrice:BigDecimal
	"""商品优惠价"""
	specialPrice:BigDecimal
	"""是否展示原价"""
	showOriginalPrice:Boolean
	"""是否启用优惠价"""
	enableSpecialPrice:Boolean
	"""商品封面图路径"""
	commodityPicturePath:String
}
"""商品描述返回值
	<AUTHOR>
"""
type CommodityDescriptionResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.response.nested.CommodityDescriptionResponse") {
	"""Web端商品描述介绍"""
	commodityDescriptionWeb:String
	"""小程序端商品描述介绍"""
	commodityDescriptionUniApp:String
	"""商品推荐语列表"""
	keywords:[String]
}
"""商品渠道配置
	<AUTHOR>
	@date 2022/01/26
"""
type CommodityPurchaseChannelConfigResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.response.nested.CommodityPurchaseChannelConfigResponse") {
	"""用户自主购买"""
	customerPurchase:PurchaseChannelConfigResponse
	"""集体缴费"""
	collectivePurchase:PurchaseChannelConfigResponse
	"""管理员导入"""
	administratorImport:PurchaseChannelConfigResponse
	"""集体报名个人缴费渠道"""
	collectiveSignUpPersonalPay:PurchaseChannelConfigResponse
}
"""商品sku属性返回值
	<AUTHOR>
	@date 2022/01/25
"""
type CommoditySkuPropertyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.response.nested.CommoditySkuPropertyResponse") {
	"""套餐类型
		<p> single：单份 combo：套餐
	"""
	comboType:SkuPropertyResponse
}
"""商品上下架信息
	<AUTHOR>
	@date 2022/1/25
"""
type OnShelveResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.response.nested.OnShelveResponse") {
	"""商品当前上架状态
		<br> 0:已下架 1：已上架
		@see CommoditySkuShelveStatus
	"""
	shelveStatus:Int
	"""最新上架时间"""
	lastOnShelveTime:DateTime
	"""最新下架时间"""
	offShelveTime:DateTime
	"""上架方式
		<p>  1:立即上架 2:指定时间上架
	"""
	onShelveType:Int
	"""下架方式
		<p>  1:立即下架 2:指定时间下架
	"""
	offShelveType:Int
	"""最新计划上架时间"""
	onShelvePlanTime:DateTime
	"""最新计划下架时间"""
	offShelvePlanTime:DateTime
	"""发布时间"""
	publishTime:DateTime
}
"""购买渠道配置
	<AUTHOR>
	@date 2022/01/26
"""
type PurchaseChannelConfigResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.response.nested.PurchaseChannelConfigResponse") {
	"""是否开启可见"""
	couldSee:Boolean
	"""是否开启可购买"""
	couldBuy:Boolean
}
"""商品sku属性
	<AUTHOR>
	@date 2022/01/25
"""
type SkuPropertyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.response.nested.SkuPropertyResponse") {
	"""sku属性值id"""
	skuPropertyValueId:String
}
"""数值型变更记录
	<AUTHOR>
"""
type NumberUpdateRecord @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.response.nested.priceupdaterecord.NumberUpdateRecord") {
	original:BigDecimal
	now:BigDecimal
}
"""开关型配置变更记录
	<AUTHOR>
"""
type SwitchUpdateRecord @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.commodity.gateway.graphql.response.nested.priceupdaterecord.SwitchUpdateRecord") {
	original:Boolean
	now:Boolean
}
"""排序参数
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum SortPolicy @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.request.SortPolicy") {
	"""正序"""
	ASC
	"""倒序"""
	DESC
}
"""商品快照sku属性信息
	<AUTHOR>
	@date 2022/01/26
"""
type CommoditySkuPropertyResponse1 @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.response.CommoditySkuPropertyResponse") {
	"""套餐类型
		<p> single：单份 combo：套餐
	"""
	comboType:SkuPropertyResponse1
}
"""商品快照
	<AUTHOR>
	@date 2022/1/26
"""
type CommoditySkuResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.response.CommoditySkuResponse") {
	"""商品id"""
	commoditySkuId:String
	"""商品Sku名称"""
	saleTitle:String
	"""商品封面图路径"""
	commodityPicturePath:String
	"""商品原价"""
	originalPrice:BigDecimal
	"""商品优惠价"""
	specialPrice:BigDecimal
	"""是否展示原价"""
	showPrice:Boolean
	"""是否启用优惠价"""
	enableSpecialPrice:Boolean
	"""商品sku 配置"""
	skuProperty:CommoditySkuPropertyResponse1
	"""商品关联资源"""
	resource:ResourceResponse
	"""商品资源数量"""
	resourceQuality:Long
}
"""电子劳动合同产品
	<AUTHOR>
"""
type ElectronicLaborContractProductResponse implements ResourceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.response.ElectronicLaborContractProductResponse") {
	"""产品ID"""
	productId:String
	"""产品名"""
	productName:String
	"""有效期类型"""
	validTermType:Int
	"""具体有效期限"""
	validTerm:Int
	"""合同签订服务商ID"""
	serverId:String
	"""资源类型
		<br> ElectronicLaborContractProduct：电子劳动合同产品
		@see com.fjhb.domain.trade.api.commodity.consts.CommoditySaleResourceTypes
	"""
	resourceType:String
}
"""发票申请信息
	<AUTHOR>
	@date 2022/03/17
"""
type InvoiceApplyInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.response.InvoiceApplyInfoResponse") {
	"""开票方式
		1: 线上开票
		2: 线下开票
		@see InvoiceMethods
	"""
	invoiceMethod:Int
	"""发票类型
		1：电子发票
		2：纸质发票
		@see InvoiceTypes
	"""
	invoiceType:Int
	"""发票种类
		1：普通发票
		2：增值税普通发票
		3：增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategory:Int
	"""发票抬头"""
	title:String
	"""发票抬头类型
		1：个人
		2：企业
		@see InvoiceTitleTypes
	"""
	titleType:Int
	"""纳税人识别号（统一社会信用代码）"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账户"""
	account:String
	"""联系邮箱"""
	contactEmail:String
	"""联系电话"""
	contactPhone:String
	"""发票票面备注"""
	remark:String
	"""营业执照"""
	businessLicensePath:String
	"""开户许可"""
	accountOpeningLicensePath:String
	"""配送方式
		0：无
		1：自取
		2：快递
		@see OfflineShippingMethods
	"""
	shippingMethod:Int
	"""配送地址信息"""
	deliveryAddress:DeliveryAddressResponse
	"""自取点信息"""
	takePoint:TakePointResponse
	"""发票申请时间"""
	appliedTime:DateTime
	"""关联发票id集合"""
	invoiceIdList:[String]
}
"""发票票面信息
	<AUTHOR>
	@date 2022/3/18
"""
type InvoiceFaceInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.response.InvoiceFaceInfoResponse") {
	"""发票抬头"""
	title:String
	"""发票抬头类型
		@see InvoiceTitleTypes
	"""
	titleType:Int
	"""购买方纳税人识别号"""
	taxpayerNo:String
	"""购买方地址"""
	address:String
	"""购买方电话号码"""
	phone:String
	"""购买方开户行名称"""
	bankName:String
	"""购买方银行账户"""
	account:String
	"""购买方电子邮箱"""
	email:String
	"""购买方营业执照"""
	businessLicensePath:String
	"""购买方开户许可"""
	accountOpeningLicensePath:String
	"""联系电子邮箱"""
	contactEmail:String
	"""联系电话"""
	contactPhone:String
	"""发票票面备注"""
	remark:String
}
"""退货单/批次退货单的原订单支付信息
	<AUTHOR>
	@date 2022/03/18
"""
type PaymentInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.response.PaymentInfoResponse") {
	"""支付金额"""
	payAmount:BigDecimal
	"""支付流水号"""
	flowNo:String
	"""收款账号"""
	receiveAccountId:String
}
"""收款账号返回值
	<AUTHOR>
	@date 2022/01/26
"""
type ReceiveAccountResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.response.ReceiveAccountResponse") {
	"""收款账号id"""
	receiveAccountId:String
	"""收款账号类型（1:线上收款账号 2:线下收款账号）
		@see ReceiveAccountTypes
	"""
	receiveAccountType:Int
	"""支付渠道id"""
	payChannelId:String
	"""支付渠道名称"""
	payChannelName:String
}
"""退货单退款信息
	<AUTHOR>
	@date 2022/03/18
"""
type RefundInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.response.RefundInfoResponse") {
	"""退款单号"""
	refundOrderNo:String
	"""退款单类型（1：线上退款 2：线下退款）
		@see RefundOrderTypes
	"""
	refundOrderType:Int
	"""退款单状态（0：等待退款 1：退款中 2：已退款 3：退款失败）
		@see com.fjhb.domain.trade.api.payment.consts.RefundOrderStatus
	"""
	refundOrderStatus:Int
	"""退款单状态变更时间"""
	refundOrderStatusChangeTime:RefundOrderStatusChangeTimeResponse
	"""退款流水"""
	refundFlow:String
	"""退款金额"""
	refundAmount:BigDecimal
	"""退款失败原因"""
	refundFailReason:String
}
"""退款单状态变更时间
	<AUTHOR>
	@date 2022/03/23
"""
type RefundOrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.response.RefundOrderStatusChangeTimeResponse") {
	"""等待退款"""
	waiting:DateTime
	"""退款中"""
	refunding:DateTime
	"""已退款"""
	refunded:DateTime
	"""退款失败"""
	failed:DateTime
}
"""商品资源
	<AUTHOR>
	@date 2022/03/02
"""
interface ResourceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.response.ResourceResponse") {
	"""资源类型
		<br> ElectronicLaborContractProduct：电子劳动合同产品
		@see com.fjhb.domain.trade.api.commodity.consts.CommoditySaleResourceTypes
	"""
	resourceType:String
}
"""商品快照sku属性
	<AUTHOR>
	@date 2022/1/25
"""
type SkuPropertyResponse1 @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.response.SkuPropertyResponse") {
	"""sku属性值id"""
	skuPropertyValueId:String
	"""sku属性值名"""
	skuPropertyValueName:String
	"""sku属性值展示名"""
	skuPropertyValueShowName:String
}
"""单位买家信息返回值
	<AUTHOR>
"""
type UnitBuyerResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.response.UnitBuyerResponse") {
	"""经办人用户ID"""
	agentUserId:String
	"""单位买家单位ID"""
	unitBuyerUnitId:String
}
"""用户相关返回值
	<AUTHOR>
	@date 2022/01/26
"""
type UserResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.common.response.UserResponse") {
	"""用户ID"""
	userId:String
}
"""线下发票可用于排序的字段
	<AUTHOR> xmj
	@date : 2022/04/06
"""
enum OfflineInvoiceSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.request.nested.OfflineInvoiceSortField") {
	"""发票创建时间"""
	INVOICE_CREAT_TIME
}
"""线下发票网关模型
	<AUTHOR>
	@date 2022/3/18
"""
type OfflineInvoiceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.response.OfflineInvoiceResponse") {
	"""发票id"""
	offlineInvoiceId:String
	"""发票基本信息"""
	basicData:OfflineInvoiceBasicDataResponse
	"""发票关联订单信息"""
	associationInfo:OfflineInvoiceAssociationInfoResponse
	"""发票配送信息"""
	deliveryInfo:OfflineInvoiceDeliveryInfoResponse
	"""发票配送记录"""
	deliveryRecordList:[OfflineDeliveryRecord]
}
"""线下发票统计信息
	<AUTHOR>
"""
type OfflineInvoiceStatisticResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.response.OfflineInvoiceStatisticResponse") {
	"""发票开票总金额"""
	totalAmount:BigDecimal
}
"""发票开票状态变更时间记录
	<AUTHOR>
	@date 2022/04/06
"""
type BillStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.response.nested.BillStatusChangeTimeResponse") {
	"""未开票"""
	unBill:DateTime
	"""已开票"""
	success:DateTime
}
"""配送地址信息
	<AUTHOR>
	@date 2022/04/06
"""
type DeliveryAddressResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.response.nested.DeliveryAddressResponse") {
	"""收件人"""
	consignee:String
	"""手机号"""
	phone:String
	"""所在物理地区"""
	region:String
	"""详细地址"""
	address:String
}
"""发票配送状态变更时间记录
	<AUTHOR>
	@date 2022/04/06
"""
type DeliveryStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.response.nested.DeliveryStatusChangeTimeResponse") {
	"""未就绪"""
	unReady:DateTime
	"""已就绪"""
	ready:DateTime
	"""已配送"""
	shipped:DateTime
	"""已自取"""
	taken:DateTime
}
"""快递信息
	<AUTHOR>
	@date 2022/04/06
"""
type ExpressResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.response.nested.ExpressResponse") {
	"""快递公司名称"""
	expressCompanyName:String
	"""快递单号"""
	expressNo:String
}
"""发票状态变更时间记录
	<AUTHOR>
	@date 2022/04/06
"""
type InvoiceStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.response.nested.InvoiceStatusChangeTimeResponse") {
	"""正常"""
	normal:DateTime
	"""作废"""
	invalid:DateTime
}
"""发票配送记录
	<AUTHOR>
	@date 2022/05/17
"""
type OfflineDeliveryRecord @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.response.nested.OfflineDeliveryRecord") {
	"""发票号"""
	invoiceNoList:[String]
	"""配送方式
		<p>
		0:无 1:自取 2：快递
		@see OfflineShippingMethods
	"""
	shippingMethod:Int
	"""自取结果"""
	takeResult:TakeResultResponse
	"""快递信息"""
	express:ExpressResponse
	"""配送时间"""
	deliveryTime:DateTime
}
"""发票关联订单信息
	<AUTHOR>
	@date 2022/3/18
"""
type OfflineInvoiceAssociationInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.response.nested.OfflineInvoiceAssociationInfoResponse") {
	"""关联订单类型 | 批次单、普通订单
		@see AssociationTypes
	"""
	associationType:Int
	"""订单号 | 批次单号"""
	associationId:String
	"""付款金额"""
	payAmount:BigDecimal
	"""单位买家信息"""
	unitBuyer:UnitBuyerResponse
	"""收款账号ID"""
	receiveAccountId:String
	"""订单退货状态
		0:未退货 1：退货中 2：退货成功
	"""
	orderReturnStatus:Int
}
"""发票基本信息
	<AUTHOR>
	@date 2022/3/18
"""
type OfflineInvoiceBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.response.nested.OfflineInvoiceBasicDataResponse") {
	"""发票类型
		@see InvoiceTypes
	"""
	invoiceType:Int
	"""发票种类
		@see InvoiceCategories
	"""
	invoiceCategory:Int
	"""发票状态
		@see InvoiceStatus
	"""
	invoiceStatus:Int
	"""发票状态变更时间记录"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeResponse
	"""发票开票状态
		@see InvoiceBillStatus
	"""
	billStatus:Int
	"""发票开票状态变更时间记录"""
	billStatusChangeTime:BillStatusChangeTimeResponse
	"""发票是否冻结"""
	freeze:Boolean
	"""冻结来源类型
		@see InvoiceFrozenSourceTypes
	"""
	freezeSourceType:Int
	"""冻结来源编号"""
	freezeSourceId:String
	"""发票票面信息"""
	invoiceFaceInfo:InvoiceFaceInfoResponse
	"""发票号集合"""
	invoiceNoList:[String]
	"""总金额（开票金额）"""
	amount:BigDecimal
}
"""线下发票配送信息
	<AUTHOR>
	@date 2022/04/06
"""
type OfflineInvoiceDeliveryInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.response.nested.OfflineInvoiceDeliveryInfoResponse") {
	"""配送状态
		<p>
		0:未就绪 1：已就绪 2：已自取 3：已配送
		@see OfflineDeliveryStatus
	"""
	deliveryStatus:Int
	"""配送状态变更时间记录"""
	deliveryStatusChangeTime:DeliveryStatusChangeTimeResponse
	"""配送方式
		<p>
		0:无 1:自取 2：快递
		@see OfflineShippingMethods
	"""
	shippingMethod:Int
	"""配送地址信息"""
	deliveryAddress:DeliveryAddressResponse
	"""自取点信息"""
	takePoint:TakePointResponse
	"""快递信息"""
	express:ExpressResponse
	"""自取信息"""
	takeResult:TakeResultResponse
}
"""自取点信息
	<AUTHOR>
	@date 2022/04/06
"""
type TakePointResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.response.nested.TakePointResponse") {
	"""领取地点"""
	pickupLocation:String
	"""领取时间"""
	pickupTime:String
	"""备注"""
	remark:String
}
"""取件信息
	<AUTHOR>
	@date 2022/04/06
"""
type TakeResultResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.offlineinvoice.gateway.graphql.response.nested.TakeResultResponse") {
	"""领取人"""
	takePerson:String
	"""手机号"""
	phone:String
}
"""订单可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum OrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.request.nested.OrderSortField") {
	"""订单创建时间"""
	ORDER_NORMAL_TIME
	"""订单交易完成时间"""
	ORDER_COMPLETED_TIME
}
"""订单返回值
	<AUTHOR>
	@date 2022/1/25
"""
type OrderResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.response.OrderResponse") {
	"""订单号"""
	orderNo:String
	"""基本信息"""
	basicData:OrderBasicDataResponse
	"""支付信息"""
	payInfo:PayInfoResponse
	"""子订单信息"""
	subOrderItems:[SubOrderResponse]
	"""单位买家信息"""
	unitBuyer:UnitBuyerResponse
	"""订单创建人"""
	creator:UserResponse
	"""订单是否已经申请发票"""
	isInvoiceApplied:Boolean
	"""发票申请信息"""
	invoiceApplyInfo:InvoiceApplyInfoResponse
}
"""订单统计信息返回值
	<AUTHOR>
	@date 2022/01/26
"""
type OrderStatisticResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.response.OrderStatisticResponse") {
	"""订单总数量"""
	totalOrderCount:Long
	"""订单总金额"""
	totalOrderAmount:BigDecimal
	"""订单培训方案商品总学时数"""
	totalPeriod:BigDecimal
}
"""订单基本信息返回值
	<AUTHOR>
	@date 2022/01/26
"""
type OrderBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.response.nested.OrderBasicDataResponse") {
	"""订单类型
		<br> 1：常规订单 2：批次关联订单
		@see com.fjhb.domain.trade.api.order.consts.OrderTypes
	"""
	orderType:Int
	"""关联批次单号"""
	batchOrderNo:String
	"""购买渠道
		<br> 1:用户自主购买 2:集体缴费 3：管理员导入 4：集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelType:Int
	"""终端
		<br> Web:Web端 H5:H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTerminalCodes
	"""
	terminalCode:String
	"""订单总金额"""
	amount:BigDecimal
	"""订单状态
		0:未确认，批次单订单初始状态
		1:正常
		2:交易完成
		3:交易关闭
		<p>
		ui订单状态：数据微服务订单状态
		等待付款：订单状态正常&支付状态未支付
		支付中：订单状态正常&支付状态支付中
		开通中：订单状态正常&支付状态已支付
		交易成功：订单状态交易成功
		交易关闭：订单状态交易关闭
		@see OrderStatus
	"""
	orderStatus:Int
	"""订单支付状态
		0:未支付
		1:支付中
		2:已支付
		@see OrderPaymentStatus
	"""
	orderPaymentStatus:Int
	"""订单发货状态
		0：未发货
		1：发货中
		2：已发货
		@see OrderDeliveryStatus
	"""
	orderDeliveryStatus:Int
	"""订单状态变更时间"""
	orderStatusChangeTime:OrderStatusChangeTimeResponse
	"""订单支付状态变更时间"""
	orderPaymentStatusChangeTime:OrderPaymentStatusChangeTimeResponse
	"""自动关闭时间"""
	autoCloseTime:DateTime
	"""交易关闭原因"""
	closeReason:OrderCloseReasonResponse
}
"""订单交易关闭返回值
	<AUTHOR>
	@date 2022/01/26
"""
type OrderCloseReasonResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.response.nested.OrderCloseReasonResponse") {
	"""交易关闭类型
		<br> 1:买家取消 2:卖家取消 3：超时取消 4：批次关联取消
		@see com.fjhb.domain.trade.api.order.consts.OrderClosedTypes
	"""
	closedType:Int
	"""交易关闭原因ID"""
	reasonId:String
	"""交易关闭原因说明"""
	reason:String
}
"""订单支付状态变更时间
	<AUTHOR>
	@date 2022/01/26
"""
type OrderPaymentStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.response.nested.OrderPaymentStatusChangeTimeResponse") {
	"""支付中"""
	paying:DateTime
	"""已支付"""
	paid:DateTime
}
"""订单状态变更时间
	<AUTHOR>
	@date 2022/01/26
"""
type OrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.response.nested.OrderStatusChangeTimeResponse") {
	"""订单处于正常状态的时间（订单创建时间）"""
	normal:DateTime
	"""订单交易完成时间"""
	completed:DateTime
	"""订单交易关闭时间"""
	closed:DateTime
}
"""订单支付信息
	<AUTHOR>
	@date 2022/1/26
"""
type PayInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.response.nested.PayInfoResponse") {
	"""付款单号"""
	paymentOrderNo:String
	"""付款单类型
		<p>
		1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
		@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderTypes
	"""
	paymentOrderType:Int
	"""付款单状态
		0:待付款
		1:付款中
		2:已支付
		3:已取消
		@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderStatus
	"""
	paymentOrderStatus:Int
	"""支付流水号"""
	flowNo:String
	"""支付金额"""
	payAmount:BigDecimal
	"""支付金额类型
		<br> 0:人民币现金支付（线下人民币现金、微信支付、支付宝） 1:华博电子钱包虚拟币 2:消费券(培训券)
		@see CurrencyType
	"""
	currencyType:Int
	"""收款账号"""
	receiveAccount:ReceiveAccountResponse
	"""汇款凭证url"""
	paymentVoucherList:[PaymentVoucherResponse]
	"""汇款凭证确认人"""
	paymentVoucherConfirmUser:UserModel
}
"""付款凭证
	<AUTHOR>
	@date 2022/03/07
"""
type PaymentVoucherResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.response.nested.PaymentVoucherResponse") {
	"""凭证ID"""
	id:String
	"""凭证文件路径"""
	path:String
	"""创建人"""
	createUserId:UserModel
	"""创建时间"""
	createdTime:DateTime
}
"""子订单发货状态变更时间
	<AUTHOR>
	@date 2022/01/26
"""
type SubOrderDeliveryStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.response.nested.SubOrderDeliveryStatusChangeTimeResponse") {
	"""等待发货"""
	waiting:DateTime
	"""发货中"""
	delivering:DateTime
	"""发货成功"""
	successDelivered:DateTime
	"""发货失败"""
	failDelivered:DateTime
}
"""子订单返回值
	<AUTHOR>
	@date 2022/1/26
"""
type SubOrderResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.order.gateway.graphql.response.nested.SubOrderResponse") {
	"""子订单号"""
	subOrderNo:String
	"""订单号"""
	orderNo:String
	"""子订单发货状态
		0:等待发货 100:发货中 200:发货成功 400:发货失败
		@see DeliveryOrderSkuStatus
	"""
	deliveryStatus:Int
	"""子订单换货状态
		<p>
		0:未换货
		1:换货申请中
		2:换货中
		3:已换货
		@see com.fjhb.domain.trade.api.order.consts.SubOrderExchangeStatus
	"""
	exchangeStatus:Int
	"""子订单退货状态
		0：未退货
		1：退货申请中
		2：退货中
		3：退货成功
		4：退款中
		5：退款成功
		@see SubOrderReturnStatus
	"""
	returnStatus:Int
	"""该子订单的最后一笔退货单号"""
	lastReturnOrderNo:String
	"""子订单发货状态变更时间
		0: 等待发货
		100: 发货中
		200: 发货成功
		400: 发货失败
		<br> key值 {@link DeliveryOrderSkuStatus}
	"""
	deliveryStatusChangeTime:SubOrderDeliveryStatusChangeTimeResponse
	"""发货失败信息"""
	deliverFailMessage:String
	"""子订单商品数量"""
	quantity:BigDecimal
	"""商品单价"""
	price:BigDecimal
	"""子订单总金额"""
	amount:BigDecimal
	"""发货商品信息"""
	deliveryCommoditySku:CommoditySkuResponse
	"""当前商品信息"""
	currentCommoditySku:CommoditySkuResponse
	"""当前商品来源类型
		@see CommoditySkuSourceType
		子订单发货: 0
		换货单换货：1
	"""
	currentCommoditySourceType:Int
	"""当前商品来源ID
		<p>
		如果来源类型是子订单，那么来源ID是子订单号
		如果来源类型是换货单，那么来源ID是换货单
	"""
	currentCommoditySourceId:String
}
"""退货单可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum ReturnOrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderSortField") {
	"""退货单申请时间"""
	APPLIED_TIME
}
"""退货单网关模型
	<AUTHOR>
	@date 2022/03/23
"""
type ReturnOrderResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.response.ReturnOrderResponse") {
	"""退货单号"""
	returnOrderNo:String
	"""退货单基本信息"""
	basicData:ReturnOrderBasicDataResponse
	"""退货单是否需要审批"""
	needApprove:Boolean
	"""退货单审批信息"""
	approvalInfo:ReturnApprovalInfoResponse
	"""退款确认人"""
	confirmUser:UserResponse
	"""退货单关联退款单信息"""
	refundInfo:RefundInfoResponse
	"""退货商品信息"""
	returnCommodity:ReturnCommodityResponse
	"""退款商品信息"""
	refundCommodity:RefundCommodityResponse
	"""退货子订单信息"""
	subOrderInfo:SubOrderInfoResponse
}
"""退货单统计信息返回值
	<AUTHOR>
"""
type ReturnOrderStatisticResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.response.ReturnOrderStatisticResponse") {
	"""退货单总数"""
	totalReturnOrderCount:Long
	"""退货单退款总额"""
	totalRefundAmount:BigDecimal
}
"""功能描述：退货原因信息
	@Author： wtl
	@Date： 2022/4/1 10:45
"""
type ReturnReasonInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.response.ReturnReasonInfoResponse") {
	"""退货原因id"""
	reasonId:String
	"""退货原因内容"""
	reasonContent:String
}
"""退货单关联订单信息
	<AUTHOR>
	@date 2022/3/23
"""
type OrderInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.response.nested.OrderInfoResponse") {
	"""订单号"""
	orderNo:String
	"""订单类型（1：常规订单 2：批次关联订单）
		@see com.fjhb.domain.trade.api.order.consts.OrderTypes
	"""
	orderType:Int
	"""关联批次单号"""
	batchOrderNo:String
	"""购买渠道（1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道）
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelType:Int
	"""终端（Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统）
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTerminalCodes
	"""
	terminalCode:String
	"""订单支付信息"""
	orderPaymentInfo:PaymentInfoResponse
	"""单位买家信息"""
	unitBuyer:UnitBuyerResponse
	"""创建人"""
	creator:UserResponse
}
"""退款商品信息
	<AUTHOR>
	@date 2022/03/28
"""
type RefundCommodityResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.response.nested.RefundCommodityResponse") {
	"""商品数量"""
	quantity:BigDecimal
	"""商品信息"""
	commoditySku:CommoditySkuResponse
}
"""退货审批信息
	<AUTHOR>
	@date 2022/03/18
"""
type ReturnApprovalInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.response.nested.ReturnApprovalInfoResponse") {
	"""审批状态（0：未审批 1：已审批）
		@see ReturnApprovalReportStatus
	"""
	approveStatus:Int
	"""审批结果（-1：无 0：拒绝 1：同意）
		@see ReturnApprovalReportResults
	"""
	approveResult:Int
	"""审批人"""
	approveUser:UserResponse
	"""审批意见"""
	approveComment:String
	"""审批时间"""
	approveTime:DateTime
}
"""退货单关闭信息
	<AUTHOR>
	@date 2022年3月29日 15:48:33
"""
type ReturnCloseReasonResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.response.nested.ReturnCloseReasonResponse") {
	"""退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
		@see ReturnOrderCloseTypes
	"""
	closeType:Int
	"""退货单取消人"""
	cancelUser:UserModel
	"""取消原因"""
	cancelReason:String
}
"""退货商品信息
	<AUTHOR>
	@date 2022/03/28
"""
type ReturnCommodityResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.response.nested.ReturnCommodityResponse") {
	"""商品数量"""
	quantity:BigDecimal
	"""商品信息"""
	commoditySku:CommoditySkuResponse
	"""商品退货资源数量"""
	resourceQuantity:Int
}
"""退货申请信息返回值
	<AUTHOR>
	@date 2022/03/24
"""
type ReturnOrderApplyInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.response.nested.ReturnOrderApplyInfoResponse") {
	"""申请人"""
	applyUser:UserResponse
	"""来源人类型
		<p>1：买家，2：卖家
		@see com.fjhb.domain.trade.api.returnorder.consts.SourcePersonType
	"""
	sourcePersonType:Int
	"""申请原因内容id"""
	reasonId:String
	"""申请原因内容"""
	reasonContent:String
	"""申请描述"""
	description:String
}
"""退货单基本信息
	<AUTHOR>
	@date 2022/3/18
"""
type ReturnOrderBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.response.nested.ReturnOrderBasicDataResponse") {
	"""退货单类型（1：仅退货 2：仅退款 3：退货并退款）
		@see ReturnOrderTypes
	"""
	returnOrderType:Int
	"""退款总额"""
	refundAmount:BigDecimal
	"""退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
		@see ReturnOrderStatus
	"""
	returnOrderStatus:Int
	"""退货单状态变更时间"""
	returnOrderStatusChangeTime:ReturnOrderStatusChangeTimeResponse
	"""退货单申请信息"""
	applyInfo:ReturnOrderApplyInfoResponse
	"""退货单退货失败信息"""
	returnFailReason:String
	"""退货单关闭信息"""
	returnCloseReason:ReturnCloseReasonResponse
	"""退货单申请来源类型
		SUB_ORDER
		BATCH_RETURN_ORDER
		@see ReturnOrderApplySourceTypes
	"""
	applySourceType:String
	"""退货单申请来源id
		当来源类型为子订单时,该申请来源id为子订单号,为批次退货单申请来源的,该申请来源id为批次退货单号
	"""
	applySourceId:String
}
"""退货单状态变更时间
	<AUTHOR>
	@date 2022/03/23
"""
type ReturnOrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.response.nested.ReturnOrderStatusChangeTimeResponse") {
	"""申请退货时间"""
	applied:DateTime
	"""申请退货取消处理中时间"""
	cancelApplying:DateTime
	"""退货处理中时间"""
	returning:DateTime
	"""退货失败时间"""
	returnFailed:DateTime
	"""正在申请退款时间"""
	refundApplying:DateTime
	"""已申请退款时间"""
	refundApplied:DateTime
	"""退款处理中时间"""
	refunding:DateTime
	"""退款失败"""
	refundFailed:DateTime
	"""退货完成时间"""
	returned:DateTime
	"""退款完成时间"""
	refunded:DateTime
	"""退货退款完成时间"""
	returnedAndRefunded:DateTime
	"""退货单完成时间"""
	returnCompleted:DateTime
	"""已关闭时间"""
	closed:DateTime
}
"""退货子订单信息
	<AUTHOR>
	@date 2022/3/18
"""
type SubOrderInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.returnorder.gateway.graphql.response.nested.SubOrderInfoResponse") {
	"""子订单号"""
	subOrderNo:String
	"""子订单商品购买数量"""
	commodityQuantity:BigDecimal
	"""子订单是否有换货"""
	exchanged:Boolean
	"""主订单信息"""
	orderInfo:OrderInfoResponse
}
"""统计报表合计数据
	<AUTHOR>
	@date 2022/05/10
"""
type ReportSummaryResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.tradereport.gateway.graphql.response.ReportSummaryResponse") {
	"""交易成功订单总金额"""
	tradeSuccessTotalAmount:BigDecimal
	"""已退款订单总金额"""
	refundTotalAmount:BigDecimal
	"""净成交总额
		<p> 交易成功订单总金额 - 已退款订单总额
	"""
	totalNetAmount:BigDecimal
	"""交易次数合计数据"""
	tradeCountSummaryInfo:SubOrderStatisticDto
	"""各渠道统计信息"""
	purchaseChannelStatisticInfoList:[PurchaseChannelStatisticDto]
}
"""服务商收益统计
	<AUTHOR>
"""
type ServicerProfitFormResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.tradereport.gateway.graphql.response.ServicerProfitFormResponse") {
	"""服务商ID"""
	servicerId:String
	"""成交总金额"""
	tradeSuccessTotalAmount:BigDecimal
	"""退货总金额"""
	returnTotalAmount:BigDecimal
	"""净成交总金额"""
	tradeSuccessNetTotalAmount:BigDecimal
}
"""支付方式统计信息
	<AUTHOR>
	@date 2022/05/10
"""
type PaymentTypeStatisticDto @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.tradereport.gateway.graphql.response.nested.PaymentTypeStatisticDto") {
	"""支付方式 	1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
		@see PaymentOrderTypes
	"""
	paymentType:Int
	"""统计信息"""
	statisticInfo:SubOrderStatisticDto
}
"""购买渠道统计信息
	<AUTHOR>
	@date 2022/05/10
"""
type PurchaseChannelStatisticDto @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.tradereport.gateway.graphql.response.nested.PurchaseChannelStatisticDto") {
	"""购买渠道 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
		@see PurchaseChannelTypes
	"""
	purchaseChannel:Int
	"""各支付方式统计信息"""
	paymentTypeStatisticInfoList:[PaymentTypeStatisticDto]
}
"""子订单变更记录统计情况
	<AUTHOR>
	@date 2022/05/10
"""
type SubOrderStatisticDto @type(value:"com.fjhb.ms.trade.query.front.gateway.ldhtqdpt.kernel.tradereport.gateway.graphql.response.nested.SubOrderStatisticDto") {
	"""交易成功数量"""
	tradeSuccessCount:Long
	"""退货数量"""
	returnCount:Long
	"""换入数量"""
	exchangeInCount:Long
	"""换出数量"""
	exchangeOutCount:Long
	"""净交易成功数量
		<p> (交易成功数量 + 换入数量) - (退货数量 + 换出数量)
	"""
	netTradeSuccessCount:Long
}
"""排序参数
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum SortPolicy1 @type(value:"com.fjhb.ms.trade.query.kernel.service.common.param.SortPolicy") {
	"""正序"""
	ASC
	"""倒序"""
	DESC
}

scalar List
type CommoditySkuBackstageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CommoditySkuBackstageResponse]}
type OfflineInvoiceResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [OfflineInvoiceResponse]}
type OrderResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [OrderResponse]}
type ReturnOrderResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ReturnOrderResponse]}
