import { ResponseStatus } from '@hbfe/common'
//轮询工具类-hj
export class TimeOut {
  static timeout(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
  /*
   * @param requestFunc请求方法
   * @param judgeFunc轮询中断校验方法
   *  @param requestParams请求参数
   *  @param ms轮询间隔时间 默认1.5s
   * @param count轮询次数 默认 5次
   * */
  static async timeoutWithCount<T>(
    requestFunc: any,
    judgeFunc: any,
    requestParams: any[] = [],
    ms = 1500,
    count = 5
  ): Promise<T> {
    const res: T = await requestFunc(...requestParams)
    count--
    if (await judgeFunc(res)) {
      return res
    } else {
      if (count > 0) {
        await TimeOut.timeout(1500)
        return await TimeOut.timeoutWithCount(requestFunc, judgeFunc, requestParams, ms, count)
      }
      console.log('次数结束，结束轮询次数=', count)
    }

    return res
  }
}
