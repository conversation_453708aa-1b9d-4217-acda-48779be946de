<template>
  <el-drawer
    :title="`${business === 'add' ? '添加' : '修改'}试题`"
    :visible.sync="questionDialog"
    size="800px"
    custom-class="m-drawer"
    v-if="questionDialog"
    :wrapperClosable="false"
  >
    <div class="drawer-bd">
      <div id="top-anchor" ref="topAnchor" style="height: 1px"></div>
      <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
        <el-form-item label="试题题型：" required>
          <div v-if="questionType === 1">
            <el-radio-group v-model="form.type" :disabled="isDisabled">
              <el-radio v-for="item in questionTypeList" @change="typeChange" :key="item.value" :label="item.value">
                {{ item.title }}
              </el-radio>
            </el-radio-group>
          </div>
          <div v-if="questionType === 2">
            <el-radio-group v-model="form.type" :disabled="isDisabled">
              <el-radio v-for="item in scalaList" :key="item.value" :label="item.value">
                {{ item.title }}
              </el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
        <el-form-item label="教师评价：" v-if="[1, 2].includes(form.type)" required>
          <el-radio-group v-model="form.isTeacherEvaluate" :disabled="isDisabled">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
            <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
              <i class="el-icon-warning m-tooltip-icon f-co"></i>
              <div slot="content">
                选择“是”，试题选项将读取引用此问卷模板的方案或期别中，课程授课教师名称无需进行配置。
              </div>
            </el-tooltip>
          </el-radio-group>
        </el-form-item>
        <single
          v-if="form.type === 1"
          ref="singleRef"
          :createQuestion="form.optionQuestion"
          :ability="form.isTeacherEvaluate"
          :key="`2${basicKey}`"
        ></single>
        <multiple
          v-if="form.type === 2"
          ref="multipleRef"
          :createQuestion="form.multipleOptionQuestion"
          :ability="form.isTeacherEvaluate"
          :key="`3${basicKey}`"
        ></multiple>
        <el-form-item label="试题题目：" v-if="form.type === 3" required>
          <div class="rich-text">
            <!-- <el-input type="textarea" :rows="6" v-model="form.answerQuestion.described" placeholder="请输入试题题目" /> -->
            <hb-tinymce-editor v-model="form.answerQuestion.described" token=""></hb-tinymce-editor>
          </div>
        </el-form-item>
        <scala v-if="form.type === 4" ref="scalaRef" :createQuestion="form.gaugeQuestion" :key="`4${basicKey}`"></scala>
        <el-form-item label="试题选项：" required v-if="form.isTeacherEvaluate && form.type !== 3">
          <span class="f-cb">选项为引用此问卷模板的方案或期别中，课程授课教师名称</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="questionDialog = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="saveContinue">保存并继续添加</el-button>
      <el-button type="primary" :loading="loading" @click="questionSave">保存</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import QuestionType, { QuestionTypeEnum } from '@api/service/common/enums/question-naire/QuestionType'
  import Single from './single.vue'
  import Multiple from './multiple.vue'
  import Scala from './scala.vue'
  import QuestionSingleOption from '@api/service/common/question-naire/QuestionSingleOption'
  import Question from '@api/service/common/question-naire/Question'
  import OptionsQuestion from '@api/service/common/question-naire/OptionsQuestion'
  import AnswerQuestion from '@api/service/common/question-naire/AnswerQuestion'
  import GaugeQuestion from '@api/service/common/question-naire/GaugeQuestion'
  import HbTinyMceEditor from '@hbfe/jxjy-admin-components/src/tinymce-editor/index.vue'
  import { cloneDeep, includes } from 'lodash'
  @Component({
    components: {
      Single,
      Multiple,
      Scala,
      HbTinyMceEditor
    }
  })
  export default class extends Vue {
    @Ref('singleRef') singleRef: Single //单选ref
    @Ref('multipleRef') multipleRef: Multiple //单选ref
    @Ref('scalaRef') scalaRef: Scala //量表ref

    @Prop({ type: Number, default: 1 }) questionType: number //问卷类型
    @Prop({ type: Boolean, default: null }) isDisableDraft: boolean //发布但违背引用
    @Prop({ type: Boolean, default: null }) isDisabled: boolean
    @Prop({
      type: String,
      default: 'add'
    })
    business: string
    @Watch('questionDialog')
    questionDialogChange(val: boolean) {
      if (val) {
        if (this.business === 'edit') {
          this.form = cloneDeep(this.form) //放置抽屉里的数据变化影响到外部的改变 数据相互隔离不干扰
        } else if (this.business === 'add') {
          this.typeChange(this.questionType)
        }
      }
    }
    loading = false
    questionDialog = false
    form = new Question() //题目模型
    QuestionType = new QuestionType() //类型枚举
    currentChoiceIndex = 0 //当前选项的索引
    basicKey = new Date().getTime() //key 解决数据不同步问题
    ability = false //评价
    /**
     *  获取系统提供的试题题型
     */
    questionTypeList = [
      { value: QuestionTypeEnum.single, title: '单选题' },
      { value: QuestionTypeEnum.multiple, title: '多选题' },
      { value: QuestionTypeEnum.answer, title: '问答题' }
    ]
    scalaList = [{ value: QuestionTypeEnum.gauge, title: '量表题' }]
    /**
     * 试题题型切换设置默认值
     */
    typeChange(val: QuestionTypeEnum) {
      switch (val) {
        case QuestionTypeEnum.single:
          this.form.optionQuestion = new OptionsQuestion()
          this.form.optionQuestion.options = [
            //试题切换时 默认给两项
            ...this.form.optionQuestion.options,
            new QuestionSingleOption(),
            new QuestionSingleOption()
          ]
          break
        case QuestionTypeEnum.multiple:
          this.form.multipleOptionQuestion = new OptionsQuestion()
          this.form.multipleOptionQuestion.options = [
            //试题切换时 默认给两项
            ...this.form.multipleOptionQuestion.options,
            new QuestionSingleOption(),
            new QuestionSingleOption()
          ]
          break
        case QuestionTypeEnum.answer:
          this.form.answerQuestion = new AnswerQuestion()
          break
        default:
          this.form.gaugeQuestion = new GaugeQuestion()
      }
    }
    /**
     * 初始化
     */
    init(type?: string) {
      this.form = new Question()
      if (type === 'continue' && this.questionType === 2) {
        const arr = Array.from(this.QuestionType.map.keys())
        this.form.type = arr[arr.length - 1] as QuestionTypeEnum //默认选中最后一项
      } else {
        this.form.type = this.QuestionType.map.keys().next().value as QuestionTypeEnum //默认单选第一项
      }
    }

    /**
     * 当前Item
     */
    productChoiceItem(index: number) {
      const choice = new QuestionSingleOption()
      choice.id = index + ''
      return choice
    }
    /**
     * 表单校验
     */
    public validForm() {
      if (this.form.type === 1) {
        return this.singleRef.validForm()
      } else if (this.form.type === 2) {
        return this.multipleRef.validForm()
      } else if (this.form.type == 3) {
        if (!this.form.answerQuestion.described) {
          this.$message.warning('请填写试题题目，不可为空。')
          return false
        }
      } else {
        return this.scalaRef.validForm()
      }
      return true
    }
    questionSave() {
      this.loading = true
      try {
        const success = this.validForm()
        if (!success) {
          this.loading = false
          return
        } else {
          if (this.form.type === QuestionTypeEnum.answer) {
            this.form.isTeacherEvaluate = false
          }
          this.form.isContinue = false
          this.$emit('handleChange', this.form)
          this.loading = false
        }
      } catch (e) {
        this.loading = false
      }
    }
    /**
     * 保存并继续添加
     */
    async saveContinue() {
      this.loading = true
      try {
        const success = this.validForm()
        if (!success) {
          this.loading = false
          return
        }
        if (this.form.type === QuestionTypeEnum.answer) {
          this.form.isTeacherEvaluate = false
        }

        this.form.isContinue = true
        this.basicKey = new Date().getTime() //key 解决数据不同步问题
        this.$emit('handleChange', this.form)
        setTimeout(() => {
          this.scrollToTop()
        }, 500)

        this.loading = false
      } catch (e) {
        this.loading = false
      }

      // this.init('continue')  //要是接口请求失败也会走到这里来  改到外部用ref请求成功时调用
      // this.typeChange(this.questionType)
    }
    scrollToTop() {
      //
      this.$nextTick(() => {
        // 方案1: 优先使用锚点滚动
        const anchor = this.$refs.topAnchor as HTMLElement
        if (anchor) {
          anchor.scrollIntoView({ behavior: 'smooth' })
          return
        }

        // 方案2: 查找滚动容器
        const drawerContent = document.querySelector('.el-drawer__body') as HTMLElement
        if (drawerContent) {
          drawerContent.scrollTo({ top: 0, behavior: 'smooth' })
          return
        }

        // 方案3: 滚动到页面顶部（备选）
        window.scrollTo({ top: 0, behavior: 'smooth' })
      })
    }
  }
</script>
