<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--选择培训方案-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20">选择培训方案</el-button>
        <el-drawer
          title="选择培训方案"
          :visible.sync="dialog4"
          :direction="direction"
          size="800px"
          :append-to-body="true"
          custom-class="m-drawer m-table-auto"
        >
          <div class="drawer-bd">
            <div class="f-mlr10">
              <!--条件查询-->
              <el-row :gutter="16" class="m-query is-border-bottom">
                <el-form :inline="true" label-width="auto">
                  <el-col :span="12">
                    <el-form-item label="培训方案形式">
                      <el-cascader :options="options" :props="props" collapse-tags clearable></el-cascader>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="培训方案名称">
                      <el-cascader
                        placeholder="请输入培训方案名称"
                        :options="options"
                        :props="{ multiple: true }"
                        filterable
                      ></el-cascader>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="培训行业">
                      <el-select v-model="select" clearable placeholder="请选择行业">
                        <el-option value="人社"></el-option>
                        <el-option value="建设"></el-option>
                        <el-option value="职业卫生"></el-option>
                        <el-option value="工勤"></el-option>
                        <el-option value="教师"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="7">
                    <el-form-item>
                      <el-checkbox v-model="checked">剔除已配置监管规则的方案</el-checkbox>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5" class="f-fr">
                    <el-form-item class="f-tr">
                      <el-button type="primary">查询</el-button>
                      <el-button>重置</el-button>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="培训方案名称" width="260">
                <template>任务名称任务名称任务名称</template>
              </el-table-column>
              <el-table-column label="属性" min-width="300">
                <template>
                  <div>行业：人社行业</div>
                  <div>地区：福建省/福州市/鼓楼区</div>
                  <div>科目类型：公需科目</div>
                  <div>培训专业：行政</div>
                  <div>培训年度：2023年</div>
                </template>
              </el-table-column>
              <el-table-column label="操作" min-width="100" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text" size="mini">取消选择</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text" size="mini">选择</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-tr f-mt10"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>

        <!--添加年度-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">添加年度</el-button>
        <el-drawer title="添加年度" :visible.sync="dialog2" :direction="direction" size="700px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
                  <el-form-item label="年度：" required>
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="设置年度培训时间：" required>
                    <el-date-picker
                      v-model="value1"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确认</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>

        <!--编辑年度学习区间-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">编辑年度学习区间</el-button>
        <el-drawer title="编辑" :visible.sync="dialog1" :direction="direction" size="700px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
                  <el-form-item label="年度：">2024</el-form-item>
                  <el-form-item label="设置年度培训时间：" required>
                    <el-date-picker
                      v-model="value1"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确认</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>

        <!--选择地区-->
        <el-button @click="dialog6 = true" type="primary" class="f-mr20">选择地区</el-button>
        <el-drawer
          title="选择地区"
          :visible.sync="dialog6"
          :direction="direction"
          size="900px"
          :append-to-body="true"
          custom-class="m-drawer m-table-auto"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
              同一个适用行业下，一个地区只能配置一个规则，地区规则按区县级设置。
            </el-alert>
            <el-row :gutter="0" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-col :span="12">
                  <el-form-item label="地区">
                    <el-cascader
                      clearable
                      :options="cascader"
                      :props="{ checkStrictly: true }"
                      placeholder="请选择地区"
                    />
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>

            <div class="f-flex f-justify-between f-mb10">
              <div class="f-c3">包含地区的区县共 <span class="f-fb f-co">5</span> 个</div>
              <el-checkbox v-model="checked" class="f-pr10">剔除已配置监管规则的方案</el-checkbox>
            </div>
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table">
              <el-table-column type="index" label="No." width="60"></el-table-column>
              <el-table-column label="省份" min-width="150">
                <template>福建省</template>
              </el-table-column>
              <el-table-column label="地市" min-width="150">
                <template>福州市</template>
              </el-table-column>
              <el-table-column label="区县" min-width="240">
                <template>
                  鼓楼区、马尾区、XX区
                </template>
              </el-table-column>
              <el-table-column label="操作" min-width="100" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text" size="mini">取消选择</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text" size="mini">取消选择</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt10 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>

        <!--编辑地区-->
        <el-button @click="dialog7 = true" type="primary" class="f-mr20">编辑地区</el-button>
        <el-drawer
          title="编辑地区"
          :visible.sync="dialog7"
          :direction="direction"
          size="900px"
          :append-to-body="true"
          custom-class="m-drawer m-table-auto"
        >
          <div class="drawer-bd">
            <el-alert type="warning" :closable="false" class="m-alert f-mb10">
              <div class="f-flex f-justify-between">
                <p class="f-c3">地区：上海市</p>
                <div class="f-c3">包含地区的区县共 <span class="f-fb f-co">5</span> 个</div>
              </div>
            </el-alert>
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table">
              <el-table-column type="index" label="No." width="60"></el-table-column>
              <el-table-column label="省份" min-width="150">
                <template>福建省</template>
              </el-table-column>
              <el-table-column label="地市" min-width="150">
                <template>福州市</template>
              </el-table-column>
              <el-table-column label="区县" min-width="240">
                <template>
                  鼓楼区、马尾区、XX区
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text" size="mini">取消选择</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text" size="mini">选择</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>

        <!--设置学习时间段-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20">设置学习时间段</el-button>
        <el-drawer
          title="设置课程学习时间"
          :visible.sync="dialog3"
          :direction="direction"
          size="700px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              设置每天课程学习的时间段，设置完成后，学员的课程学习会在指定时间段内
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
                  <el-form-item label="每天学习时间段：" required>
                    <el-time-picker
                      is-range
                      v-model="value1"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="选择时间范围"
                    >
                    </el-time-picker>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确认</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>

        <!--设置学习时间段-->
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">设置不学习时间段</el-button>
        <el-drawer
          title="设置课程学习时间"
          :visible.sync="dialog5"
          :direction="direction"
          size="700px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              设置每天不学习的时间段，设置完成后，学员课程学习不会在这些时间段内进行学习
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
                  <el-form-item label="每天不学习时间段：" required>
                    <el-time-picker
                      is-range
                      v-model="value1"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="选择时间范围"
                    >
                    </el-time-picker>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确认</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
