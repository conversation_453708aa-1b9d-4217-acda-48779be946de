import { StudentSchemeLearningResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

/*
 * @Author: ZhuSong
 * @Date: 2022-11-07 11:25:22
 * @LastEditors: ZhuSong
 * @LastEditTime: 2022-12-06 08:30:29
 * @Description:
 */
export default class LearningArcjovesResponse {
  /**
   * 后端业务Id -- 用户ID
   */
  id: string = undefined
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学号id
   */
  studentNo: string
  /**
   * 姓名
   */
  name: string = undefined
  /**
   * 证件号
   */
  idCard: string = undefined
  /**
   * 培训方案 -> ID
   */
  trainingId: string = undefined
  /**
   * 培训方案 -> 名称
   */
  trainingName: string = undefined
  /**
   * 工作单位
   */
  unit: string = undefined
  /**
   * 订单号
   */
  order: string = undefined
  /**
   * 是否已打印
   */
  status: boolean = undefined
  /**
   * 考核通过时间
   */
  learningSuccessTime: string = undefined
  /**
   * 打印时间
   */
  printTime: string = undefined
  /**
   * 下载地址
   */
  url: string = undefined
  /**
   * 期别名称
   */
  issueName = ''
  static from(dto: StudentSchemeLearningResponse) {
    //
    const vo = new LearningArcjovesResponse()
    vo.id = dto.student.userId
    vo.qualificationId = dto.qualificationId
    vo.studentNo = dto.studentNo
    vo.trainingId = dto.scheme.schemeId
    vo.order = dto.learningRegister?.orderNo
    vo.status = dto.extendedInfo?.whetherToPrint
    vo.learningSuccessTime = dto.studentLearning?.trainingResultTime
    vo.printTime = dto.extendedInfo?.printTime
    vo.url = dto.extendedInfo?.pdfUrl
    vo.issueName = dto.issueName
    return vo
  }
}
