import BasicDataQueryBackstage, {
  RegionResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { Response } from '@hbfe/common'
class Region {
  /**
   * 地区缓存
   */
  private cacheRegion = new Map<string, Response<Array<RegionResponse>>>()
  /**
   * 同graph接口名 查地区
   * @param type 0 行业 1服务
   */
  async getServiceOrIndustryRegion(type: number) {
    const cacheKey = `region_${type}`
    if (this.cacheRegion.has(cacheKey)) {
      return this.cacheRegion.get(cacheKey)
    }
    const res = await BasicDataQueryBackstage.getServiceOrIndustryRegion(1)
    this.cacheRegion.set(cacheKey, res)
    return res
  }
  /**
   * 同graph接口名 查地区
   * @param type 0 行业 1服务
   */
  async getServiceOrIndustryRegionInDistribution(type: number) {
    const cacheKey = `region_${type}_InDistribution`
    if (this.cacheRegion.has(cacheKey)) {
      return this.cacheRegion.get(cacheKey)
    }
    const res = await BasicDataQueryBackstage.getServiceOrIndustryRegionInDistribution(1)
    this.cacheRegion.set(cacheKey, res)
    return res
  }
}
export default new Region()
