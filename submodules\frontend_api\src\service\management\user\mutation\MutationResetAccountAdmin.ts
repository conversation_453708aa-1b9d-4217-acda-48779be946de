/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-05-26 11:19:13
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-26 11:22:48
 */
import MsAccountGateway, { AccountPwdAuthIdentityResetInfo } from '@api/ms-gateway/ms-account-v1'
import { ResponseStatus } from '@hbfe/common'

/**
 * 修改账号
 */
class MutationResetAccountAdmin {
  async doResetAdminAccount(request: AccountPwdAuthIdentityResetInfo): Promise<ResponseStatus> {
    const { status } = await MsAccountGateway.resetAccountPwdAuthIdentity(request)
    return status
  }
}

export default MutationResetAccountAdmin
