import { ResponseStatus } from '@hbfe/common'
import MSCommodity from '@api/ms-gateway/ms-commodity-v1'

/**
 * 商品业务对象
 */
class CommodityBaseClass {
  // region properties
  /**
   * 商品id
   */
  commoditySkuId = ''
  // endregion
  // region methods

  /**
   * 上架商品
   */
  async doPutOn(): Promise<ResponseStatus> {
    if (!this.commoditySkuId) {
      return new ResponseStatus(8011, '商品id不能为空')
    }
    const res = await MSCommodity.onShelve({
      id: this.commoditySkuId
    })
    return res.status
  }

  /**
   * 下架商品
   */
  async doPuff(): Promise<ResponseStatus> {
    if (!this.commoditySkuId) {
      return new ResponseStatus(8011, '商品id不能为空')
    }
    const res = await MSCommodity.offShelve({
      id: this.commoditySkuId
    })
    return res.status
  }

  /**
   * 删除商品
   */
  async doDeleteCommodity(): Promise<ResponseStatus> {
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  // endregion
}
export default CommodityBaseClass
