import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 换货单状态
 * 1：发起换期
 * 2：退期处理中
 * 3：退期失败
 * 4：申请发货
 * 5：发货处理中
 * 6：换期成功
 */
export enum ExchangePeriodStatusEnum {
  Apply_Exchange = 1,
  Off_Shift_Processing = 2,
  Off_Shift_Fail = 3,
  Apply_Delivery = 4,
  Delivery_Processing = 5,
  Complete_Exchange = 6
}

class ExchangePeriodStatusType extends AbstractEnum<ExchangePeriodStatusEnum> {
  static enum = ExchangePeriodStatusEnum
  constructor(status?: ExchangePeriodStatusEnum) {
    super()
    this.current = status
    this.map.set(ExchangePeriodStatusEnum.Apply_Exchange, '发起换期')
    this.map.set(ExchangePeriodStatusEnum.Off_Shift_Processing, '退期处理中')
    this.map.set(ExchangePeriodStatusEnum.Off_Shift_Fail, '退期失败')
    this.map.set(ExchangePeriodStatusEnum.Apply_Delivery, '申请发货')
    this.map.set(ExchangePeriodStatusEnum.Delivery_Processing, '发货处理中')
    this.map.set(ExchangePeriodStatusEnum.Complete_Exchange, '换期成功')
  }
}

export default new ExchangePeriodStatusType()
