<template>
  <el-main>
    <el-card shadow="never" class="m-card f-mb15">
      <!--选择收款账号-->
      <el-button @click="dialog1 = true" type="primary" class="f-mr20">选择收款账号</el-button>
      <el-drawer
        title="选择收款账号"
        :visible.sync="dialog1"
        :direction="direction"
        size="1200px"
        custom-class="m-drawer"
      >
        <div class="drawer-bd">
          <!--表格-->
          <el-table stripe :data="tableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="支付方式" min-width="100" align="center">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  <el-tag type="warning" size="small">线下</el-tag>
                </div>
                <div v-else>
                  <el-tag type="primary" size="small">线上</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="支付账号类型" min-width="120">
              <template>微信支付</template>
            </el-table-column>
            <el-table-column label="收款账号别名" min-width="150">
              <template>别名</template>
            </el-table-column>
            <el-table-column label="开户账户信息" min-width="250">
              <template>商户号：<EMAIL></template>
            </el-table-column>
            <el-table-column label="分行号" min-width="180">
              <template>-</template>
            </el-table-column>
            <el-table-column label="柜台号" min-width="180">
              <template>-</template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template>
                <el-checkbox v-model="checked">选择</el-checkbox>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>
          <div class="m-btn-bar f-tc f-mt20">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </div>
      </el-drawer>
    </el-card>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
