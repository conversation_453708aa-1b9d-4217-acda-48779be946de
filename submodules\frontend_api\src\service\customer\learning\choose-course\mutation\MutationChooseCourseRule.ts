import { ResponseStatus } from '@hbfe/common'
import ChooseCourseGateway from '@api/ms-gateway/ms-choose-course-v1'
import MutationEnterChooseCourse from '@api/service/customer/learning/choose-course/mutation/MutationEnterChooseCourse'

/**
 * @description 培训方案选课规则
 */
class MutationChooseCourseRule {
  /**
   * 方案已选学时
   */
  selectedPeriod = 0

  /**
   * 方案还需选学时
   */
  needPeriod = 0

  /**
   * 根据培训方案商品Id获取学时
   * @param {string} schemeId - 培训方案商品Id
   * @return {ResponseStatus}
   */
  async querySelectedPeriod(): Promise<ResponseStatus> {
    // 从token状态层获取到选课token
    const beforeEnterChooseCourseToken = MutationEnterChooseCourse.token
    // todo token获取失败的处理
    if (!beforeEnterChooseCourseToken) return
    const response = await ChooseCourseGateway.prepareChooseCourse(beforeEnterChooseCourseToken)
    if (response.status.isSuccess()) {
      // 将json转成js对象
      const propertiesObj = JSON.parse(response.data.properties)
      // console.log(propertiesObj, '=====')
      // todo 根据返回的name判断一次再进行赋值 具体命名还要再确认下
      // todo 增加一个allowLastChooseOver的判断
      // compulsoryPeriod  currentCompulsoryPeriod  selectivePeriod  currentSelectivePeriod
      console.log(propertiesObj.electiveMaxPeriod.current, '000')
      this.selectedPeriod = propertiesObj.electiveMaxPeriod.current
      this.needPeriod = propertiesObj.electiveMaxPeriod.config - propertiesObj.electiveMaxPeriod.current
      return new ResponseStatus(200, '')
    }
  }
}

export default new MutationChooseCourseRule()
