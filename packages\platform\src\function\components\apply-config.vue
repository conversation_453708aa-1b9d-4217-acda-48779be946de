<template>
  <div>
    <el-card shadow="never" class="m-card f-mb15">
      <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
        1.用于配置web端和h5端网校中的登录、注册、忘记密码模块中的验证方式。<br />
        2.专题门户中的登录、注册、忘记模块中的验证方式也同样生效。
      </el-alert>
      <el-row type="flex" justify="center" class="width-limit">
        <el-col :md="20" :lg="16" :xl="13">
          <!--右侧输入框及选择器默认长度为100%，中长.form-l，短.form-s-->
          <el-form ref="form" label-width="auto" class="m-form">
            <el-form-item label="验证方式选择：">
              <el-radio v-model="selected" :label="VerifyTypeEnum.captcha" class="f-mr30">验证码</el-radio>
              <el-radio v-model="selected" :label="VerifyTypeEnum.slide" class="f-mr10">滑动拼图</el-radio>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
    <div class="m-btn-bar f-tc is-sticky-1">
      <el-button @click="doCancel">取消</el-button>
      <el-button type="primary" @click="save" :loading="loading">保存</el-button>
    </div>

    <give-up-dialog :give-up-model="uiConfig.giveUpModel" @callBack="getVerificationCodeSetting"></give-up-dialog>
  </div>
</template>
<script lang="ts">
  import { Vue, Component } from 'vue-property-decorator'
  import OnlineSchoolModule from '@api/service/training-institution/online-school/OnlineSchoolModule'
  import { VerifyTypeEnum } from '@api/service/management/online-school-config/functionality-setting/enum/VerifyEnum'
  import MutationRegisterAndLogin from '@api/service/management/online-school-config/functionality-setting/mutation/MutationRegisterAndLogin'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'

  @Component({
    components: { GiveUpDialog }
  })
  export default class extends Vue {
    /**
     * 初始化对象
     */
    onlineSchoolModule = new OnlineSchoolModule()

    /**
     * 枚举
     */
    VerifyTypeEnum = VerifyTypeEnum

    /**
     * 选中项
     */
    selected: VerifyTypeEnum = null

    /**
     * 加载状态
     */
    loading = true

    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }

    /**
     * 提交验证方法
     */
    mutationRegisterAndLogin: MutationRegisterAndLogin =
      OnlineSchoolConfigModule.mutationFunctionalitySettingFactory.registerAndLogin

    async created() {
      await this.getVerificationCodeSetting()
    }
    /**
     * 获取验证方式
     */
    async getVerificationCodeSetting() {
      try {
        this.loading = true
        const res = await this.onlineSchoolModule.getOnlineSchoolConfig()
        if (res.status.isSuccess()) {
          const verify = res.data.validateMethodConfig
          if (verify.enabledCaptcha && !verify.enabledSlideCircus) {
            this.selected = VerifyTypeEnum.captcha
          } else if (!verify.enabledCaptcha && verify.enabledSlideCircus) {
            this.selected = VerifyTypeEnum.slide
          }
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 保存
     */
    async save() {
      try {
        this.loading = true
        const res = await this.mutationRegisterAndLogin.saveVerificationCodeSetting(this.selected)
        if (res.isSuccess()) {
          this.$message.success('保存成功')
        }
      } catch (e) {
        console.log(e)
        this.$message.error('保存失败')
      } finally {
        this.loading = false
      }
    }

    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
    }
  }
</script>
