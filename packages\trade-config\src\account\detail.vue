<route-params content="/:id"></route-params>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/basic-data/trade/account')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/basic-data/trade/account' }">收款账户管理</el-breadcrumb-item>
      <el-breadcrumb-item>收款账户详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="formRef" :model="form" label-width="auto" class="m-text-form is-column">
                <el-form-item label="支付账号类型：" v-if="form.paymentChannelId">{{
                  paymentChannelTypeTitle(form.paymentChannelId)
                }}</el-form-item>
                <el-form-item label="账户别名：">{{ form.accountName }}</el-form-item>
                <!--微信-->
                <div
                  v-if="form.accountType == 1 && form.paymentChannelId && form.paymentChannelId.indexOf('WXPAY') != -1"
                >
                  <el-form-item label="商户号：">{{ form.accountNo }}</el-form-item>
                  <el-form-item label="API密钥：">{{ form.merchantKey }}</el-form-item>
                  <el-form-item label="公众帐号ID：">{{ form.appId }}</el-form-item>
                  <el-form-item label="微信证书密钥：">{{ form.privateKeyPWD }}</el-form-item>
                  <el-form-item label="微信证书：">{{ form.privateKeyFileName }}</el-form-item>
                </div>
                <!-- 支付宝 -->
                <div
                  v-if="form.accountType == 1 && form.paymentChannelId && form.paymentChannelId.indexOf('ALIPAY') != -1"
                >
                  <el-form-item label="支付宝帐号：">{{ form.accountNo }}</el-form-item>
                  <el-form-item label="合作者身份ID：">{{ form.partner }}</el-form-item>
                  <el-form-item label="支付宝应用私钥：">{{ form.privateKey }}</el-form-item>
                  <el-form-item label="支付宝公钥：">{{ form.publicKey }}</el-form-item>
                  <el-form-item label="支付宝应用ID：">{{ form.appId }}</el-form-item>
                </div>
                <!--兴业银行支付-->
                <div
                  v-if="
                    form.accountType == 1 && form.paymentChannelId && form.paymentChannelId.indexOf('CIB_PAY') != -1
                  "
                >
                  <el-form-item label="商户号：">{{ form.accountNo }}</el-form-item>
                  <el-form-item label="应用ID：">{{ form.xyPayAppId }}</el-form-item>
                  <el-form-item label="终端编号(收款APP终端编号)：">{{ form.terminalId }}</el-form-item>
                  <el-form-item label="请求报文签名私钥(SM2签名私钥)：">{{ form.sm2key }}</el-form-item>
                  <el-form-item label="响应报文验证公钥：">{{ form.resPublicKey }}</el-form-item>
                  <el-form-item label="请求字段加密私钥(字段加密密钥)：">{{ form.reqKey }}</el-form-item>
                  <el-form-item label="微信公众号id：">{{
                    form.xyPaySubAppId ? form.xyPaySubAppId : '—'
                  }}</el-form-item>
                </div>
                <!--建设银行支付-->
                <div
                  v-if="
                    form.accountType == 1 && form.paymentChannelId && form.paymentChannelId.indexOf('CCB_PAY') != -1
                  "
                >
                  <el-form-item label="商户号：" prop="accountNo">
                    {{ form.accountNo }}
                  </el-form-item>
                  <el-form-item label="商户柜台代码：">{{ form.posId }}</el-form-item>
                  <el-form-item label="分行代码：">{{ form.branchId }}</el-form-item>
                  <el-form-item label="网银支付接口公钥：">{{ form.jsPublicKey }}</el-form-item>
                  <el-form-item label="操作员账号：">{{ form.operator }}</el-form-item>
                  <el-form-item label="操作员交易密码：">{{ form.password }}</el-form-item>
                  <el-form-item label="是否使用防钓鱼：">{{ form.phishing == 1 ? '使用' : '不使用' }}</el-form-item>
                  <el-form-item label="文件证书路径：">{{ form.certFilePath }}</el-form-item>
                  <el-form-item label="文件证书密码：">{{ form.certPassword }}</el-form-item>
                  <el-form-item label="微信公众号id：">{{ form.jsSubAppid ? form.jsSubAppid : '—' }}</el-form-item>
                </div>
                <!--兴业银行(威富通)支付-->
                <div
                  v-if="
                    form.accountType == 1 &&
                    form.paymentChannelId &&
                    form.paymentChannelId.indexOf('SWIFT_PASS_PAY') != -1
                  "
                >
                  <el-form-item label="商户号：">{{ form.accountNo }}</el-form-item>
                  <el-form-item label="商户私钥：">{{ form.mchPrivateKey }}</el-form-item>
                  <el-form-item label="平台公钥：">{{ form.platPublicKey }}</el-form-item>
                </div>
                <!-- 新大陆-聚合支付-->
                <div
                  v-if="
                    form.accountType == 1 &&
                    form.paymentChannelId &&
                    form.paymentChannelId.indexOf('NEW_LAND_PAY') != -1
                  "
                >
                  <el-form-item label="支付商户号：">{{ form.payMerchantId }}</el-form-item>
                  <el-form-item label="代理商户号：">{{ form.proxyId }}</el-form-item>
                  <el-form-item label="秘钥：">{{ form.xdlPrivateKey }}</el-form-item>
                </div>
                <!--线下 -->
                <div v-if="form.accountType == 2">
                  <el-form-item label="开户号：">{{ form.accountNo }}</el-form-item>
                  <el-form-item label="开户银行：">{{ form.depositBank }}</el-form-item>
                  <el-form-item label="开户户名：">{{ form.merchantName }}</el-form-item>
                  <el-form-item label="柜台号：">{{ form.counterNumber }}</el-form-item>
                </div>
                <el-form-item label="纳税人识别号：">{{ form.taxPayerName }}</el-form-item>
                <el-form-item v-if="form.accountType == 1">
                  <div slot="label">
                    <span class="f-vm">退款方式</span>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                      <div slot="content">
                        <p>退款方式说明：</p>
                        <p>线下退款，方式需要登录微信/支付宝商户后台退款，系统只记录退款状态；</p>
                        <p>线上退款，确认退款后系统会将款项返回原账户。</p>
                      </div>
                    </el-tooltip>
                    <span>：</span>
                  </div>
                  {{ form.refundWay == 1 ? '线上退款' : '线下退款' }} </el-form-item
                ><el-form-item
                  label="扫码引导提示语："
                  prop="qrScanPrompt"
                  v-show="
                    form.paymentChannelId === PayAccountTypeEnum.CIB_PAY ||
                    form.paymentChannelId === PayAccountTypeEnum.CCB_PAY ||
                    form.paymentChannelId === PayAccountTypeEnum.SWIFT_PASS_PAY ||
                    form.paymentChannelId === PayAccountTypeEnum.NEW_LAND_PAY
                  "
                >
                  <el-input
                    type="textarea"
                    v-model="form.qrScanPrompt"
                    clearable
                    class="form-l"
                    maxlength="50"
                    show-word-limit
                    disabled
                  />
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import TradeInfoConfigModule from '@api/service/management/trade-info-config/TradeInfoConfigModule'
  import ReceiveAccountDetailVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountDetailVo'
  import { PayAccountTypeEnum } from '@api/service/management/trade-info-config/enums/PayAccountTypeEnum'
  @Component
  export default class extends Vue {
    id = ''
    getQueryReceiveAccountVo = TradeInfoConfigModule.queryTradeInfoConfigFactory
    form = new ReceiveAccountDetailVo()
    PayAccountTypeEnum = PayAccountTypeEnum
    async created() {
      this.id = this.$route.params.id
      // 请求详情
      this.form = await this.getQueryReceiveAccountVo.getQueryReceiveAccountDetail(this.id).queryDetail()
    }
    // 支付账号类型
    paymentChannelTypeTitle(paymentChannelId: string) {
      if (paymentChannelId.indexOf('WXPAY') != -1) {
        return '微信支付'
      }
      if (paymentChannelId.indexOf('ALIPAY') != -1) {
        return '支付宝支付'
      }
      if (paymentChannelId.indexOf('CIB_PAY') != -1) {
        return '兴业银行聚合支付'
      }
      if (paymentChannelId.indexOf('CCB_PAY') != -1) {
        return '建设银行聚合支付'
      }
      if (paymentChannelId.indexOf('SWIFT_PASS_PAY') != -1) {
        return '兴业银行聚合支付（威富通）'
      }
      return '银行支付'
    }
  }
</script>
