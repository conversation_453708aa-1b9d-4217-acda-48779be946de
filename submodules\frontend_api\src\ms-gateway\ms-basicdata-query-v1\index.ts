import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-basicdata-query-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-basicdata-query-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 查询服务地区/行业地区请求信息
@author: zhengp 2022/5/31 10:23
 */
export class GetRegionNewRequest {
  /**
   * 0 行业地区 1服务地区
   */
  queryType: number
}

/**
 * 查询服务地区/行业地区请求信息
@author: zhengp 2022/5/31 10:23
 */
export class GetRegionRequest {
  /**
   * 0 行业地区 1服务地区
   */
  queryType: number
  /**
   * 查询层级 [1,2,3]
   */
  level: number
  /**
   * 父级id
   */
  parentId?: string
}

/**
 * 业务地区信息
@author: zhengp 2022/5/31 10:29
 */
export class BusinessRegionInfoDto {
  /**
   * 地区编号
   */
  id: string
  /**
   * 上级地区编号
为-1时，表示当前为根地区
   */
  parentId: string
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 地区名称
   */
  name: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enable: boolean
}

/**
 * <AUTHOR> 2022/11/1 17:34
 */
export class RegionDto {
  /**
   * 编号
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 是否是全国 为TRUE时 region为空
   */
  isAllRegion: boolean
  /**
   * 地区 末级地区集合 字符数组
   */
  region: string
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 查询服务地区/行业地区响应信息
@author: zhengp 2022/5/31 10:27
 */
export class GetRegionNewResponse {
  regionDto: RegionDto
}

/**
 * 查询服务地区/行业地区响应信息
@author: zhengp 2022/5/31 10:27
 */
export class GetRegionResponse {
  /**
   * 业务地区信息
   */
  businessRegionInfoDtoList: Array<BusinessRegionInfoDto>
}

/**
 * Ui主题颜色
<AUTHOR>
 */
export class UiThemeColorResponse {
  /**
   * 颜色id
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 帐户ID
   */
  servicerId: string
  /**
   * 颜色值
   */
  colorRef: string
  /**
   * 创建时间
   */
  createdTime: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取ui主题颜色列表
   * @return 主题颜色列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findUiThemeColorList(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findUiThemeColorList,
    operation?: string
  ): Promise<Response<Array<UiThemeColorResponse>>> {
    return commonRequestApi<Array<UiThemeColorResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getRegion(
    request: GetRegionRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRegion,
    operation?: string
  ): Promise<Response<GetRegionResponse>> {
    return commonRequestApi<GetRegionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getRegionNew(
    request: GetRegionNewRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRegionNew,
    operation?: string
  ): Promise<Response<GetRegionNewResponse>> {
    return commonRequestApi<GetRegionNewResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 当前服务商合约是否到期
   * 【注意】调用前需要调用applyForServiceByDomainName获取服务商上下文信息
   * @return 服务商合约是否已到期
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async isOnlineSchoolContractExpired(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.isOnlineSchoolContractExpired,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
