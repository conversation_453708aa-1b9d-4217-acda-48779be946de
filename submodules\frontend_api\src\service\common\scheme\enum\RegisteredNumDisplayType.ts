import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 已报名人数展示类型枚举
 * read_enrollment 读取实际报名人数
 * fixed_value 固定显示值
 */
export enum RegisteredNumDisplayTypeEnum {
  read_enrollment = 0,
  fixed_value = 1
}

/**
 * @description 已报名人数展示类型
 */
class RegisteredNumDisplayType extends AbstractEnum<RegisteredNumDisplayTypeEnum> {
  static enum = RegisteredNumDisplayTypeEnum

  constructor(status?: RegisteredNumDisplayTypeEnum) {
    super()
    this.current = status
    this.map.set(RegisteredNumDisplayTypeEnum.read_enrollment, '读取实际报名人数')
    this.map.set(RegisteredNumDisplayTypeEnum.fixed_value, '固定显示值')
  }
}

export default new RegisteredNumDisplayType()
