/**
 * 学习媒体信息
 */
import Lecture from '@api/service/common/models/learning/Lecture'
import VideoChapter from '@api/service/common/models/learning/VideoChapter'
import VideoResource from '@api/service/common/models/learning/VideoResource'
import LearningResourceType from '@api/service/common/models/learning/LearningResourceType'

class LearningMedia {
  /**
   * 媒体编号
   */
  mediaId = ''
  /**
   * 媒体时长
   */
  mediaTimeLength = 0
  /**
   * 当前媒体学习时长
   */
  currentTimeLength = 0
  /**
   * 当前媒体学习的进度
   */
  currentLearningSchedule = 0
  /**
   * 当前学习状态，0/1/2，未学习/学习中/学习完成
   */
  currentLearningState = 0
  /**
   * 媒体最后播放刻度
   */
  lastMediaPlayScale = 0
  /**
   * 最后学习时间
   */
  lastLearningTime: Date
  /**
   * 课件类型，1表示文档，2表示视频，3表示多媒体
   */
  type: number
  /**
   * 讲义列表
   */
  lectureList: Array<Lecture>
  /**
   * 视频章节信息
   */
  catalogList: Array<VideoChapter>
  /**
   * 资源
   */
  videoResourceList: Array<VideoResource> = new Array<VideoResource>()
  /**
   * 试听时长
   */
  listenTime: number

  /**
   * 是否多媒体或视频类课件
   */
  isVideo() {
    return this.type === 2 || this.type === 3
  }

  /**
   * 是否文档类课件
   */
  isDocument() {
    return this.type === 1
  }

  /**
   * 获取华为云音频资源
   */
  getHuaWeiAudioResource(): VideoResource | undefined {
    return this.videoResourceList.find(x => x.resourceType === LearningResourceType.RESOURCE_HUA_WEI_AUDIO)
  }

  /**
   * 获取(华为云、外链地址）媒体资源列表
   */
  getVideoResources(): Array<VideoResource> {
    const videoResources = this.videoResourceList.filter(
      x =>
        x.resourceType === LearningResourceType.RESOURCE_HUA_WEI_VIDEO ||
        x.resourceType === LearningResourceType.EXTERNAL_LINKS_VIDEO
    )
    if (videoResources?.length) {
      return videoResources
    }
    return []
  }

  /**
   * 获取PDF文档相对路径
   */
  getDocumentRelativePath(): string {
    const documentResource = this.videoResourceList.find(
      x => x.resourceType === LearningResourceType.RESOURCE_PDF_DOCUMENT
    )
    if (documentResource) {
      return documentResource.url
    }
    return ''
  }
}

export default LearningMedia
