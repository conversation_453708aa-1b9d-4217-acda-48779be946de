function Statistic() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/statistic" */ '@hbfe/jxjy-admin-routers/src/fjzj/statistic.vue'
  )
}
function StatisticHuayiSellStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/statistic-huayi-sell-statistic-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/statistic/huayi-sell-statistic/index.vue'
  )
}
function StatisticLearningStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/statistic-learning-statistic-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/statistic/learning-statistic/index.vue'
  )
}
function StatisticRegionLearningStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/statistic-region-learning-statistic-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/statistic/region-learning-statistic/index.vue'
  )
}
function StatisticRegionSellStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/statistic-region-sell-statistic-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/statistic/region-sell-statistic/index.vue'
  )
}
function StatisticSchemeLearningStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/statistic-scheme-learning-statistic-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/statistic/scheme-learning-statistic/index.vue'
  )
}
function StatisticSchemeSellStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/statistic-scheme-sell-statistic-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/statistic/scheme-sell-statistic/index.vue'
  )
}
function StatisticSupplierDistributionOfGoodsOpenStatisticsIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/statistic-supplier-distribution-of-goods-open-statistics-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/statistic/supplier-distribution-of-goods-open-statistics/index.vue'
  )
}
function StatisticSupplierDistributorSalesStatisticsIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/statistic-supplier-distributor-sales-statistics-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/statistic/supplier-distributor-sales-statistics/index.vue'
  )
}
function Training() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training" */ '@hbfe/jxjy-admin-routers/src/fjzj/training.vue'
  )
}
function TrainingCustomerService() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-customer-service" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/customer-service.vue'
  )
}
function TrainingCustomerServicePersonalIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-customer-service-personal-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/customer-service/personal/index.vue'
  )
}
function TrainingScheme() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-scheme" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/scheme.vue'
  )
}
function TrainingSchemeCreate() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-scheme-create" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/scheme/create.vue'
  )
}
function TrainingSchemeDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-scheme-detail" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/scheme/detail.vue'
  )
}
function TrainingSchemeManage() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-scheme-manage" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/scheme/manage.vue'
  )
}
function TrainingSchemeModify() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-scheme-modify" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/scheme/modify.vue'
  )
}
function TrainingTrade() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-trade" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/trade.vue'
  )
}
function TrainingTradeInvoice() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-trade-invoice" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/trade/invoice.vue'
  )
}
function TrainingTradeInvoicePersonalIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-trade-invoice-personal-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/trade/invoice/personal/index.vue'
  )
}
function TrainingTradeOrder() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-trade-order" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/trade/order.vue'
  )
}
function TrainingTradeOrderPersonalIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-trade-order-personal-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/trade/order/personal/index.vue'
  )
}
function TrainingTradeOrderPersonalDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-trade-order-personal-detail" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/trade/order/personal/detail.vue'
  )
}
function TrainingTradeReconciliation() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-trade-reconciliation" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/trade/reconciliation.vue'
  )
}
function TrainingTradeReconciliationPersonalIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-trade-reconciliation-personal-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/trade/reconciliation/personal/index.vue'
  )
}
function TrainingTradeRefund() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-trade-refund" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/trade/refund.vue'
  )
}
function TrainingTradeRefundPersonalIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-trade-refund-personal-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/trade/refund/personal/index.vue'
  )
}
function TrainingTradeRefundPersonalDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-trade-refund-personal-detail" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/trade/refund/personal/detail.vue'
  )
}
function TrainingUser() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-user" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/user.vue'
  )
}
function TrainingUserStudentIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-user-student-index" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/user/student/index.vue'
  )
}
function TrainingUserStudentDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/fjzj/training-user-student-detail" */ '@hbfe/jxjy-admin-routers/src/fjzj/training/user/student/detail.vue'
  )
}

export default [
  {
    name: 'statistic',
    path: '/statistic',
    component: Statistic,
    meta: {
      permissionMap: {},
      openWhenInit: false,
      closeAble: false,
      isMenu: true,
      title: '统计报表',
      sort: 5,
      icon: 'icon-shuju',
      ownerGroup: [],
      group: 'statistic',
    },
    children: [
      {
        name: 'statistic-huayi-sell-statistic-index',
        path: 'huayi-sell-statistic',
        component: StatisticHuayiSellStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '华医网组合开通统计',
          sort: 15,
          icon: 'icon-ribaotongji',
          ownerGroup: [],
          group: 'statistic.huayi-sell-statistic',
        },
      },
      {
        name: 'statistic-learning-statistic-index',
        path: 'learning-statistic',
        component: StatisticLearningStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '学员学习明细',
          sort: 5,
          icon: 'icon-mingxi',
          ownerGroup: [],
          group: 'statistic.learning-statistic',
        },
      },
      {
        name: 'statistic-region-learning-statistic-index',
        path: 'region-learning-statistic',
        component: StatisticRegionLearningStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '地区学习统计',
          sort: 4,
          icon: 'icon-tongjibaobiao',
          ownerGroup: [],
          group: 'statistic.region-learning-statistic',
        },
      },
      {
        name: 'statistic-region-sell-statistic-index',
        path: 'region-sell-statistic',
        component: StatisticRegionSellStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '地区开通统计',
          sort: 2,
          icon: 'icon-cptj',
          ownerGroup: [],
          group: 'statistic.region-sell-statistic',
        },
      },
      {
        name: 'statistic-scheme-learning-statistic-index',
        path: 'scheme-learning-statistic',
        component: StatisticSchemeLearningStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '方案学习统计',
          sort: 3,
          icon: 'icon-tongjiyuce',
          ownerGroup: [],
          group: 'statistic.scheme-learning-statistic',
        },
      },
      {
        name: 'statistic-scheme-sell-statistic-index',
        path: 'scheme-sell-statistic',
        component: StatisticSchemeSellStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '方案开通统计',
          sort: 1,
          icon: 'icon-ribaotongji',
          ownerGroup: [],
          group: 'statistic.scheme-sell-statistic',
        },
      },
      {
        name: 'statistic-supplier-distribution-of-goods-open-statistics-index',
        path: 'supplier-distribution-of-goods-open-statistics',
        component: StatisticSupplierDistributionOfGoodsOpenStatisticsIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '分销商品开通统计',
          sort: 14,
          icon: 'icon-mingxi',
          ownerGroup: [],
          group: 'statistic.supplier-distribution-of-goods-open-statistics',
        },
      },
      {
        name: 'statistic-supplier-distributor-sales-statistics-index',
        path: 'supplier-distributor-sales-statistics',
        component: StatisticSupplierDistributorSalesStatisticsIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '分销商销售统计',
          sort: 12,
          icon: 'icon-mingxi',
          ownerGroup: [],
          group: 'statistic.supplier-distributor-sales-statistics',
        },
      },
    ],
  },
  {
    name: 'training',
    path: '/training',
    component: Training,
    meta: {
      permissionMap: {},
      openWhenInit: false,
      closeAble: false,
      isMenu: true,
      title: '培训管理',
      sort: 4,
      icon: 'icon-peixun',
      ownerGroup: [],
      group: 'training',
    },
    children: [
      {
        name: 'training-customer-service',
        path: 'customer-service',
        component: TrainingCustomerService,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '客服管理',
          sort: 6,
          icon: 'icon-kefu',
          ownerGroup: [],
          group: 'training.customer-service',
        },
        children: [
          {
            name: 'training-customer-service-personal-index',
            path: 'personal',
            component: TrainingCustomerServicePersonalIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '业务咨询',
              sort: 1,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.customer-service.personal',
            },
          },
        ],
      },
      {
        name: 'training-scheme',
        path: 'scheme',
        component: TrainingScheme,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '培训方案管理',
          sort: 2,
          icon: 'icon-fangan',
          ownerGroup: [],
          group: 'training.scheme',
        },
        children: [
          {
            name: 'training-scheme-create',
            path: 'create/:schemeId',
            component: TrainingSchemeCreate,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '新建培训方案',
              sort: 1,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'training.scheme.create',
            },
          },
          {
            name: 'training-scheme-detail',
            path: 'detail/:id',
            component: TrainingSchemeDetail,
            meta: {
              permissionMap: {},
              title: '查看培训方案',
              ownerGroup: [],
              group: 'training.scheme.detail',
            },
          },
          {
            name: 'training-scheme-manage',
            path: 'manage',
            component: TrainingSchemeManage,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '培训方案管理',
              sort: 2,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'training.scheme.manage',
            },
          },
          {
            name: 'training-scheme-modify',
            path: 'modify/:schemeId',
            component: TrainingSchemeModify,
            meta: {
              permissionMap: {},
              title: '修改培训方案',
              ownerGroup: [],
              group: 'training.scheme.modify',
            },
          },
        ],
      },
      {
        name: 'training-trade',
        path: 'trade',
        component: TrainingTrade,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '交易管理',
          sort: 3,
          icon: 'icon-jiaoyi',
          ownerGroup: [],
          group: 'training.trade',
        },
        children: [
          {
            name: 'training-trade-invoice',
            path: 'invoice',
            component: TrainingTradeInvoice,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '发票管理',
              sort: 3,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.trade.invoice',
            },
            children: [
              {
                name: 'training-trade-invoice-personal-index',
                path: 'personal',
                component: TrainingTradeInvoicePersonalIndex,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  title: '个人报名发票',
                  sort: 1,
                  ownerGroup: [],
                  group: 'training.trade.invoice.personal',
                },
              },
            ],
          },
          {
            name: 'training-trade-order',
            path: 'order',
            component: TrainingTradeOrder,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '订单管理',
              sort: 1,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.trade.order',
            },
            children: [
              {
                name: 'training-trade-order-personal-index',
                path: 'personal',
                component: TrainingTradeOrderPersonalIndex,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  title: '个人报名订单',
                  sort: 1,
                  icon: 'icon_guanli',
                  ownerGroup: [],
                  group: 'training.trade.order.personal',
                },
              },
              {
                name: 'training-trade-order-personal-detail',
                path: 'personal/detail/:id',
                component: TrainingTradeOrderPersonalDetail,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  hideMenu: true,
                  onlyShowOnTab: true,
                  title: '个人订单详情',
                  ownerGroup: [],
                  group: 'training.trade.order.personal.detail',
                },
              },
            ],
          },
          {
            name: 'training-trade-reconciliation',
            path: 'reconciliation',
            component: TrainingTradeReconciliation,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '对账管理',
              sort: 4,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.trade.reconciliation',
            },
            children: [
              {
                name: 'training-trade-reconciliation-personal-index',
                path: 'personal',
                component: TrainingTradeReconciliationPersonalIndex,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  title: '个人报名对账',
                  sort: 1,
                  icon: 'icon_menhuxinxiguanli',
                  ownerGroup: [],
                  group: 'training.trade.reconciliation.personal',
                },
              },
            ],
          },
          {
            name: 'training-trade-refund',
            path: 'refund',
            component: TrainingTradeRefund,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '退款管理',
              sort: 2,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.trade.refund',
            },
            children: [
              {
                name: 'training-trade-refund-personal-index',
                path: 'personal',
                component: TrainingTradeRefundPersonalIndex,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  title: '个人报名退款订单',
                  sort: 1,
                  icon: 'icon_menhuxinxiguanli',
                  ownerGroup: [],
                  group: 'training.trade.refund.personal',
                },
              },
              {
                name: 'training-trade-refund-personal-detail',
                path: 'personal/detail/:id',
                component: TrainingTradeRefundPersonalDetail,
                meta: {
                  permissionMap: {},
                  isMenu: true,
                  hideMenu: true,
                  onlyShowOnTab: true,
                  title: '个人订单退款详情',
                  ownerGroup: [],
                  group: 'training.trade.refund.personal.detail',
                },
              },
            ],
          },
        ],
      },
      {
        name: 'training-user',
        path: 'user',
        component: TrainingUser,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '用户管理',
          sort: 5,
          icon: 'icon-guanliyuan',
          ownerGroup: [],
          group: 'training.user',
        },
        children: [
          {
            name: 'training-user-student-index',
            path: 'student',
            component: TrainingUserStudentIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '学员管理',
              sort: 1,
              icon: 'icon_guanli',
              ownerGroup: [],
              group: 'training.user.student',
            },
          },
          {
            name: 'training-user-student-detail',
            path: 'student/detail/:id',
            component: TrainingUserStudentDetail,
            meta: {
              permissionMap: {},
              title: '学员详情',
              ownerGroup: [],
              group: 'training.user.student.detail',
            },
          },
        ],
      },
    ],
  },
]
