import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-teachingplan-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-teachingplan-report-sds-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 学员报到请求体
 */
export class StudentReportCheckInRequest {
  /**
   * 期别的学员学习token
   */
  studentLearningToken: string
  /**
   * 教学计划ID
   */
  planId: string
  /**
   * 经度
   */
  longitude?: number
  /**
   * 维度
   */
  latitude?: number
  /**
   * 是否开启范围校验
   */
  enableRangeCheck: boolean
}

/**
 * 学员报到请求体
 */
export class StudentReportCheckInWithStudentNoRequest {
  /**
   * 学员学号
   */
  studentNo: string
  /**
   * 期别ID
   */
  issueId: string
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 教学计划学习方式ID, 没有值时请提供-1
   */
  learningId: string
  /**
   * 教学计划ID, 没有值时请提供 -1
   */
  planId: string
  /**
   * 经度
   */
  longitude?: number
  /**
   * 维度
   */
  latitude?: number
  /**
   * 是否开启范围校验
   */
  enableRangeCheck: boolean
}

/**
 * 学员报到响应体
 */
export class StudentReportCheckInResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 消息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 学员扫码报到，前端请求这个接口即可
   * @param request {@link StudentReportCheckInRequest}
   * @return {@link StudentReportCheckInResponse}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async studentReportCheckIn(
    request: StudentReportCheckInRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.studentReportCheckIn,
    operation?: string
  ): Promise<Response<StudentReportCheckInResponse>> {
    return commonRequestApi<StudentReportCheckInResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员扫码报到
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async studentReportCheckInWithStudentNo(
    request: StudentReportCheckInWithStudentNoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.studentReportCheckInWithStudentNo,
    operation?: string
  ): Promise<Response<StudentReportCheckInResponse>> {
    return commonRequestApi<StudentReportCheckInResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
