import AttendanceConfig from '@api/service/management/implement/AttendanceConfig'
import AttendanceReportItem from '@api/service/management/statisticalReport/query/vo/AttendanceReportItem'
import { Page } from '@hbfe/common'
import PlatCourseLearningBackstage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import StudentPeriodLog from '@api/service/management/train-class/offlinePart/model/StudentPeriodLog'
import AttendanceConfigDto from '@api/service/common/implement/models/AttendanceConfigDto'
/**
 * 查看考勤详情
 */
export default class QueryAttendanceReportList extends AttendanceConfig {
  /**
   * 学号
   */
  studentNo = ''
  /**
   * 期别参训资格id
   */
  qualificationId = ''
  /**
   * 期别id
   */
  periodId = ''
  /**
   * 考勤记录列表
   */
  reportList = new Array<AttendanceReportItem>()

  /**
   * 查询期别规则
   */
  async queryStudentPeriodLog() {
    const newStudentPeriodLog = new StudentPeriodLog(this.periodId)
    const res = await newStudentPeriodLog.queryAttendanceConfig()
    if (res.isSuccess()) {
      this.signIn = newStudentPeriodLog.signIn
      this.signOut = newStudentPeriodLog.signOut
    } else {
      this.signIn = new AttendanceConfigDto()
      this.signOut = new AttendanceConfigDto()
    }
  }

  /**
   * 查询考勤记录
   */
  async queryReportList(page: Page) {
    const res = await PlatCourseLearningBackstage.pagePlanItemAttendanceInServicer({
      page,
      qualificationId: this.qualificationId
    })
    this.reportList = []
    if (res.status.isSuccess() && res.data.currentPageData?.length) {
      this.reportList = res.data.currentPageData.map((item) => AttendanceReportItem.from(item))
    }
    page.totalSize = res.data?.totalSize || 0
    page.totalPageSize = res.data?.totalPageSize || 0
    return this.reportList
  }
}
