import store from '@/store'
import { Module as Mod } from 'vuex'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import PreExamGateway from '@api/gateway/btpx@GeneralExam-default'
import PlatformExamGateway, {
  AnswerRecordDTO,
  AnswerRecordParamDTO,
  AnswerStatusEnum,
  UserLastPracticeRecordDTO,
  UserLastPracticeRecordParamDTO,
  UserPracticeStatisticDTO,
  UserPracticeStatisticParamDTO
} from '@api/gateway/PlatformExam'
import { ResponseStatus } from '../../../Response'
import { AnswerRecordPracticeType } from '../answer-record/report/enums/enums'
import { Role, RoleType } from '../../../Secure'
import PracticeType from '@api/service/common/models/common/enums/PracticeType'
import AnswerRecordItem from '@api/service/common/models/answer-record/AnswerRecordItem'

const getAnswerRecordPageParam = function(payload: {
  schemeId: string
  issueId: string
  practiceType: AnswerRecordPracticeType
  pageNo: number
  pageSize: number
}): AnswerRecordParamDTO {
  const paramDTO: AnswerRecordParamDTO = new AnswerRecordParamDTO()
  paramDTO.schemeId = payload.schemeId
  paramDTO.issueId = payload.issueId
  paramDTO.answerStatusEnum = AnswerStatusEnum.MARKED_FINISH
  switch (payload.practiceType) {
    case AnswerRecordPracticeType.PRACTICE:
      paramDTO.practiceTypeList = [
        PracticeType.RANDOM,
        PracticeType.REAL,
        PracticeType.SIMULATION,
        PracticeType.PRACTICE
      ]
      break
    case AnswerRecordPracticeType.EXAM:
      paramDTO.practiceTypeList = [PracticeType.EXAM]
      break
    case AnswerRecordPracticeType.DAILY:
      paramDTO.practiceTypeList = [PracticeType.DAILY]
      break
    case AnswerRecordPracticeType.ERROR_PRONE:
      paramDTO.practiceTypeList = [PracticeType.ERROR_PRONE]
      break
    case AnswerRecordPracticeType.CORRECTION:
      paramDTO.practiceTypeList = [PracticeType.CORRECTION]
      break
    case AnswerRecordPracticeType.FAVORITE:
      paramDTO.practiceTypeList = [PracticeType.FAVORITE]
      break
  }
  return paramDTO
}

export class UserLastAnswerRecord extends UserLastPracticeRecordDTO {
  examPaperName: string
}

class StateCache {
  constructor(schemeId: string, issueId: string) {
    this.schemeId = schemeId
    this.issueId = issueId
  }

  // 方案id
  schemeId: string
  // 期别id
  issueId: string
  // 最后作答记录
  userLastPracticeRecord: UserLastAnswerRecord = new UserLastAnswerRecord()
  totalAnswerSummary: UserPracticeStatisticDTO = new UserPracticeStatisticDTO()
  // 是否需要重载，在学员触发去作答练习是置为true，在下次取数时先清空数据然后加载新数据
  needReload = false
  // 最新一次加载时间，从apollo配置的超时时间与当前时间判断，在下次取数时清空当前取数数据然后加载新数据
  latestLoadTime: Date = new Date()
}

class AnswerRecordItemCache {
  constructor(schemeId: string, issueId: string, practiceType: AnswerRecordPracticeType, size: number) {
    this.schemeId = schemeId
    this.issueId = issueId
    this.practiceType = practiceType
    this.currentPageNo = 1
    this.size = size
    this.answerRecordList = new Array<AnswerRecordItem>()
  }

  // 方案id
  schemeId: string
  // 期别id
  issueId: string
  // 练习类型
  practiceType: AnswerRecordPracticeType
  // 总数
  totalSize: number
  totalPageSize: number
  currentPageNo: number
  // 每页数量
  size: number
  // 作答记录
  answerRecordList: Array<AnswerRecordItem>

  public nextPage() {
    this.currentPageNo += 1
  }

  public previousPage() {
    this.currentPageNo -= 1
  }
}

export interface IState {
  // 各期别数据缓存
  issueStateCacheMap: Array<StateCache>
  issueAndTypeAnswerRecordItemCache: Array<AnswerRecordItemCache>
  currentAnswerId: string
}

@Module({
  namespaced: true,
  name: 'CustomerAnswerRecordModule',
  store,
  dynamic: true
})
class AnswerRecordModule extends VuexModule implements IState {
  // 各期别数据缓存
  issueStateCacheMap = new Array<StateCache>()
  issueAndTypeAnswerRecordItemCache = new Array<AnswerRecordItemCache>()
  currentAnswerId = ''

  constructor(module: Mod<ThisType<any>, any>) {
    super(module)
    store.subscribe(mutation => {
      if (mutation.type?.indexOf('ALERT_EXAM_RELOAD') !== -1) {
        this.processSubmitPaperMessage(mutation.payload)
      }
    })
  }

  @Role([RoleType.user])
  @Action
  async init(payload: { schemeId: string; issueId: string }) {
    if (
      !this.issueStateCacheMap.find(p => p.issueId === payload.issueId) ||
      this.issueStateCacheMap.find(p => p.issueId === payload.issueId)?.needReload
    ) {
      // let stateCache = new StateCache(payload.schemeId, payload.issueId)
      // ctx.commit(mutations.SET_LEARNING_STATE_CACHE_MAP, stateCache)

      const stateCache = new StateCache(payload.schemeId, payload.issueId)
      const paramDTO: UserLastPracticeRecordParamDTO = new UserLastPracticeRecordParamDTO()
      paramDTO.schemeId = payload.schemeId
      paramDTO.issueId = payload.issueId
      let LastRecordResponse = await PlatformExamGateway.getUserLastPracticeRecord(paramDTO)
      if (!LastRecordResponse.status.isSuccess()) {
        return LastRecordResponse.status
      }
      let userLastAnswerRecord: any = LastRecordResponse.data
      delete paramDTO.issueId
      LastRecordResponse = await PlatformExamGateway.getUserLastPracticeRecord(paramDTO)
      if (!LastRecordResponse.status.isSuccess()) {
        return LastRecordResponse.status
      }
      if (LastRecordResponse.data?.practiceType === PracticeType.CORRECTION) {
        userLastAnswerRecord = LastRecordResponse.data
      }

      stateCache.userLastPracticeRecord = userLastAnswerRecord
      if (stateCache.userLastPracticeRecord && stateCache.userLastPracticeRecord.practiceType === PracticeType.EXAM) {
        const examPaperResponse = await PreExamGateway.getExamPaper(stateCache.userLastPracticeRecord.examPaperId)
        if (!examPaperResponse.status.isSuccess()) {
          return examPaperResponse.status
        }
        stateCache.userLastPracticeRecord.examPaperName = examPaperResponse.data.name
      }
      // console.log('>>>' + JSON.stringify(stateCache.userLastPracticeRecord))

      // 统计 答题数和正确率
      const param: UserPracticeStatisticParamDTO = new UserPracticeStatisticParamDTO()
      param.schemeId = payload.schemeId
      param.issueId = payload.issueId
      param.statisticPracticeTypeList = [PracticeType.PRACTICE]
      const statisticResponse = await PlatformExamGateway.statisticUserPractice(param)
      if (!statisticResponse.status.isSuccess()) {
        return statisticResponse.status
      }
      stateCache.totalAnswerSummary = statisticResponse.data
      // delete param.issueId
      // param.statisticPracticeTypeList = [PracticeType.CORRECTION]
      // const correctionStatisticResponse = await PlatformExamGateway.statisticUserPractice(param)
      // if (!correctionStatisticResponse.status.isSuccess()) {
      //   return correctionStatisticResponse.status
      // }
      // stateCache.totalAnswerSummary.totalAnswerCount += correctionStatisticResponse.data.totalAnswerCount
      // stateCache.totalAnswerSummary.totalCorrectCount += correctionStatisticResponse.data.totalCorrectCount
      // stateCache.totalAnswerSummary.correctRate =
      //   stateCache.totalAnswerSummary.totalAnswerCount === 0
      //     ? 0
      //     : stateCache.totalAnswerSummary.totalCorrectCount / stateCache.totalAnswerSummary.totalAnswerCount

      this.setStateCacheToIssueStateCacheMap(stateCache)
    }

    return new ResponseStatus(200)
  }
  // 重做某场考试
  @Role([RoleType.user])
  @Action
  async redoExam(payload: { answersId: string; recordId: string }) {
    const params: { answersId?: string; answerRecordId?: string } = {}
    params.answersId = payload.answersId
    params.answerRecordId = payload.recordId
    const response = await PreExamGateway.redoExamination(params)
    if (response.status.isSuccess()) {
      this.setAnswerId(response.data)
    }
    return response.status
  }
  // 重做练习
  @Role([RoleType.user])
  @Action
  async redoPractice(payload: { answersId: string; recordId: string }) {
    const params: { answersId?: string; answerRecordId?: string } = {}
    params.answersId = payload.answersId
    params.answerRecordId = payload.recordId
    const response = await PreExamGateway.redoPractice(params)
    if (response.status.isSuccess()) {
      this.setAnswerId(response.data)
    }
    return response.status
  }

  @Role([RoleType.user])
  @Action
  async loadAnswerRecordPage(payload: {
    schemeId: string
    issueId: string
    practiceType: AnswerRecordPracticeType
    pageNo: number
    pageSize: number
    append: boolean
  }) {
    const oldCache =
      this.issueAndTypeAnswerRecordItemCache.find(
        p => p.issueId === payload.issueId && p.practiceType === payload.practiceType
      ) || new AnswerRecordItemCache(payload.schemeId, payload.issueId, payload.practiceType, payload.pageSize)

    const cache = new AnswerRecordItemCache(oldCache.schemeId, oldCache.issueId, oldCache.practiceType, oldCache.size)
    cache.currentPageNo = oldCache.currentPageNo
    cache.totalPageSize = oldCache.totalPageSize
    cache.answerRecordList = JSON.parse(JSON.stringify(oldCache.answerRecordList))

    cache.currentPageNo = payload.pageNo

    const paramDTO = getAnswerRecordPageParam({
      schemeId: cache.schemeId,
      issueId: cache.issueId,
      practiceType: cache.practiceType,
      pageNo: cache.currentPageNo,
      pageSize: cache.size
    })

    const pageResponse = await PlatformExamGateway.pageUserAnswerRecord({
      page: {
        pageNo: cache.currentPageNo,
        pageSize: cache.size
      },
      paramDTO: paramDTO
    })
    if (pageResponse.status.isSuccess()) {
      if (payload.practiceType === AnswerRecordPracticeType.EXAM) {
        const map: Map<string, string> = new Map<string, string>()
        const examPaperIds: Array<string> = pageResponse.data.currentPageData
          .filter((p: AnswerRecordDTO) => p.examPaperId && !map.has(p.examPaperId) && map.set(p.examPaperId, ''))
          .map((p: AnswerRecordDTO) => p.examPaperId)
        for (const key in examPaperIds) {
          const examPaperId = examPaperIds[key]
          const examPaperResponse = await PreExamGateway.getExamPaper(examPaperId)
          if (!examPaperResponse.status.isSuccess()) {
            pageResponse.status = examPaperResponse.status
            return pageResponse.status
          }
          map.set(examPaperId, examPaperResponse.data.name)
        }

        pageResponse.data.currentPageData.forEach((value: any) => {
          value.name = map.get(value.examPaperId)
        })
      }
    }

    if (pageResponse.status.isSuccess()) {
      cache.totalSize = pageResponse.data.totalSize
      cache.totalPageSize = pageResponse.data.totalPageSize
      if (payload.append) {
        cache.answerRecordList.push(...pageResponse.data.currentPageData)
      } else {
        cache.answerRecordList = pageResponse.data.currentPageData
      }
    }
    this.setAnswerRecordItemCache(cache)
    return pageResponse.status
  }

  @Role([RoleType.user])
  @Action
  async startScrollAnswerRecordItem(payload: {
    schemeId: string
    issueId: string
    practiceType: AnswerRecordPracticeType
    size: number
  }) {
    const cache = new AnswerRecordItemCache(payload.schemeId, payload.issueId, payload.practiceType, payload.size)

    const paramDTO = getAnswerRecordPageParam({
      schemeId: cache.schemeId,
      issueId: cache.issueId,
      practiceType: cache.practiceType,
      pageNo: cache.currentPageNo,
      pageSize: cache.size
    })

    const pageResponse = await PlatformExamGateway.pageUserAnswerRecord({
      page: {
        pageNo: cache.currentPageNo,
        pageSize: cache.size
      },
      paramDTO: paramDTO
    })
    if (pageResponse.status.isSuccess()) {
      if (payload.practiceType === AnswerRecordPracticeType.EXAM) {
        const map: Map<string, string> = new Map<string, string>()
        const examPaperIds: Array<string> = pageResponse.data.currentPageData
          .filter((p: AnswerRecordDTO) => p.examPaperId && !map.has(p.examPaperId) && map.set(p.examPaperId, ''))
          .map((p: AnswerRecordDTO) => p.examPaperId)
        for (const key in examPaperIds) {
          const examPaperId = examPaperIds[key]
          const examPaperResponse = await PreExamGateway.getExamPaper(examPaperId)
          if (!examPaperResponse.status.isSuccess()) {
            pageResponse.status = examPaperResponse.status
            return pageResponse
          }
          map.set(examPaperId, examPaperResponse.data.name)
        }

        pageResponse.data.currentPageData.forEach((value: any) => {
          value.name = map.get(value.examPaperId)
        })
      }
    }

    if (pageResponse.status.isSuccess()) {
      cache.totalSize = pageResponse.data.totalSize
      cache.totalPageSize = pageResponse.data.totalPageSize
      cache.answerRecordList.push(...pageResponse.data.currentPageData)
    }
    this.setAnswerRecordItemCache(cache)
    return pageResponse.status
  }

  @Role([RoleType.user])
  @Action
  async continueScroll(payload: { schemeId: string; issueId: string; practiceType: AnswerRecordPracticeType }) {
    const oldCache = this.issueAndTypeAnswerRecordItemCache.find(
      p => p.issueId === payload.issueId && p.practiceType === payload.practiceType
    )

    if (!oldCache) {
      return new ResponseStatus(500, '请先初始化')
    }

    const cache = new AnswerRecordItemCache(oldCache.schemeId, oldCache.issueId, oldCache.practiceType, oldCache.size)
    cache.currentPageNo = oldCache.currentPageNo
    cache.totalPageSize = oldCache.totalPageSize
    cache.answerRecordList = oldCache.answerRecordList
    cache.nextPage()
    const paramDTO = getAnswerRecordPageParam({
      schemeId: cache.schemeId,
      issueId: cache.issueId,
      practiceType: cache.practiceType,
      pageNo: cache.currentPageNo,
      pageSize: cache.size
    })

    const pageResponse = await PlatformExamGateway.pageUserAnswerRecord({
      page: {
        pageNo: cache.currentPageNo,
        pageSize: cache.size
      },
      paramDTO: paramDTO
    })
    if (pageResponse.status.isSuccess()) {
      if (payload.practiceType === AnswerRecordPracticeType.EXAM) {
        const map: Map<string, string> = new Map<string, string>()
        const examPaperIds: Array<string> = pageResponse.data.currentPageData
          .filter((p: AnswerRecordDTO) => p.examPaperId && !map.has(p.examPaperId) && map.set(p.examPaperId, ''))
          .map((p: AnswerRecordDTO) => p.examPaperId)
        for (const key in examPaperIds) {
          const examPaperId = examPaperIds[key]
          const examPaperResponse = await PreExamGateway.getExamPaper(examPaperId)
          if (!examPaperResponse.status.isSuccess()) {
            pageResponse.status = examPaperResponse.status
            return pageResponse
          }
          map.set(examPaperId, examPaperResponse.data.name)
        }

        pageResponse.data.currentPageData.forEach((value: any) => {
          value.name = map.get(value.examPaperId)
        })
      }
    }

    if (pageResponse.status.isSuccess()) {
      cache.totalSize = pageResponse.data.totalSize
      cache.totalPageSize = pageResponse.data.totalPageSize
      cache.answerRecordList.push(...pageResponse.data.currentPageData)
    } else {
      cache.previousPage()
    }
    this.setAnswerRecordItemCache(cache)
    return pageResponse.status
  }

  /**
   * 处理交卷消息
   * @param ctx
   * @param payload
   */
  @Action
  async processSubmitPaperMessage(payload: { schemeId: string; issueId: string; answersId: string }) {
    console.log('答题记录处理交卷消息')
    this.issueStateCacheMap.forEach((stateCache: StateCache) => {
      if (stateCache?.schemeId === payload.schemeId) {
        console.log('答题记录匹配到' + JSON.stringify(payload))
        this.setNeedReload({
          schemeId: stateCache.schemeId,
          issueId: stateCache.issueId
        })
        // ctx.dispatch('init', {
        //   schemeId: stateCache.schemeId,
        //   issueId: stateCache.issueId
        // })
      }
    })
  }

  @Mutation
  setStateCacheToIssueStateCacheMap(payload: StateCache) {
    this.issueStateCacheMap = this.issueStateCacheMap.filter(p => p.issueId !== payload.issueId)
    this.issueStateCacheMap.push(payload)
  }

  @Mutation
  setNeedReload(payload: any) {
    const stateCache = this.issueStateCacheMap.find(p => p.issueId === payload.issueId)
    if (stateCache) {
      stateCache.needReload = true
    } else {
      this.issueStateCacheMap.forEach(p => {
        if (p.schemeId === payload.schemeId) {
          p.needReload = true
        }
      })
    }
  }

  @Mutation
  setAnswerId(answerId: string) {
    this.currentAnswerId = answerId
  }

  @Mutation
  setAnswerRecordItemCache(payload: AnswerRecordItemCache) {
    this.issueAndTypeAnswerRecordItemCache = this.issueAndTypeAnswerRecordItemCache.filter(
      p => p.issueId !== payload.issueId || p.practiceType !== payload.practiceType
    )
    this.issueAndTypeAnswerRecordItemCache.push(payload)
  }

  /**
   * 获取用户最后练习记录
   * @param state
   */
  get getUserLastPracticeRecord() {
    return (schemeId: string, issueId: string) => {
      const record = this.issueStateCacheMap.find(p => p.issueId === issueId)?.userLastPracticeRecord
      return record || new UserLastAnswerRecord()
    }
  }

  /**
   * 获取用户最后练习记录
   * @param state
   */
  get getTotalAnswerSummary() {
    return (schemeId: string, issueId: string) => {
      return this.issueStateCacheMap.find(p => p.issueId === issueId)?.totalAnswerSummary
    }
  }

  get getTotalPageSize() {
    return (schemeId: string, issueId: string, practiceType: AnswerRecordPracticeType) => {
      return (
        this.issueAndTypeAnswerRecordItemCache.find(p => p.issueId === issueId && p.practiceType === practiceType)
          ?.totalPageSize || 0
      )
    }
  }

  /**
   * 获取作答记录列表
   */
  get getAnswerRecordList() {
    return (schemeId: string, issueId: string, practiceType: AnswerRecordPracticeType) => {
      return (
        this.issueAndTypeAnswerRecordItemCache.find(p => p.issueId === issueId && p.practiceType === practiceType)
          ?.answerRecordList || []
      )
    }
  }
}

export default getModule(AnswerRecordModule)
