import { LoginLogResponse } from '@api/platform-gateway/platform-login-log-v1'
export class LoginNoteItem {
  /**
   * 用户Id
   */
  userId = ''
  /**
   * 用户名称
   */
  userName = ''
  /**
   * 登录IP地址
   */
  ip = ''
  /**
   * 登录成功时间
   */
  loginSuccessTime = ''
  /**
   * 登录失败时间
   */
  logoutTime = ''
  static from(dto: LoginLogResponse) {
    const vo = new LoginNoteItem()
    vo.userId = dto.userId
    vo.userName = dto.userName
    vo.ip = dto.loginIp
    vo.loginSuccessTime = dto.loginTime
    vo.logoutTime = dto.logoutTime
    return vo
  }
}
