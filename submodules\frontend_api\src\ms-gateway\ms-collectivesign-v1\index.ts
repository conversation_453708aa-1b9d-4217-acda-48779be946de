import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-collectivesign-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-collectivesign-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum ProcessResult {
  UNTREATED = 'UNTREATED',
  SUCCESS = 'SUCCESS',
  FAILURE = 'FAILURE',
  READY_FAILURE = 'READY_FAILURE'
}
export enum TaskState {
  CREATED = 'CREATED',
  ALREADY = 'ALREADY',
  EXECUTING = 'EXECUTING',
  COMPLETED = 'COMPLETED'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 单个属性元数据
<AUTHOR>
@since 2022/4/20
 */
export class MetaProperty {
  /**
   * 属性键
   */
  key?: string
  /**
   * 属性值
   */
  value?: string
}

/**
 * 单个属性元数据-任务执行期间获取
<AUTHOR>
@since 2022/4/20
 */
export class RunTimeProperty {
  /**
   * 属性键
   */
  key?: string
  /**
   * 属性值
   */
  value?: string
}

/**
 * 集体报名导入信息
 */
export class CollectiveSignImportInfoForVerifyRequest {
  /**
   * 集体报名编号
允许为空，若为空则默认创建新的批次
   */
  collectiveNo?: string
  /**
   * 文件路径
   */
  filePath: string
  /**
   * 文件名称
   */
  fileName?: string
  /**
   * 终端类型
<p>
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  terminalCode?: string
  /**
   * 默认密码
   */
  password?: string
  /**
   * 密码模式
1-默认密码——password
2-使用身份证后六位
   */
  passwordModel: number
  /**
   * 密码生效范围，1-默认仅新用户，2-全部用户（含已注册）
   */
  passwordEffectiveRange: number
  /**
   * 销售渠道类型
0-自营渠道
2-专题渠道
   */
  saleChannel: number
  /**
   * 销售渠道Id（自营渠道为空，专题渠道时必填）
   */
  saleChannelId?: string
}

/**
 * 集体报名导入信息
 */
export class CollectiveSignImportInfoRequest {
  /**
   * 集体报名编号
允许为空，若为空则默认创建新的批次
   */
  collectiveNo?: string
  /**
   * 文件路径
   */
  filePath?: string
  /**
   * 文件名称
   */
  fileName?: string
  /**
   * 终端类型
<p>
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  terminalCode?: string
  /**
   * 默认密码
   */
  password?: string
  /**
   * 密码模式
1-默认密码——password
2-使用身份证后六位
   */
  passwordModel?: number
  /**
   * 密码生效范围，1-默认仅新用户，2-全部用户（含已注册）
   */
  passwordEffectiveRange?: number
  /**
   * 销售渠道类型
0-自营渠道
2-专题渠道
   */
  saleChannel: number
  /**
   * 销售渠道Id（自营渠道为空，专题渠道时必填）
   */
  saleChannelId?: string
}

/**
 * 集体缴费查询对象
<AUTHOR>
 */
export class CollectiveSignQueryRequest {
  /**
   * 集体报名编号
   */
  collectiveSignupNo: string
  /**
   * 查询属性集合-模版中存在的数据
   */
  metaPropertyList?: Array<MetaProperty>
  /**
   * 运行时获取的属性-运行时获取的新数据
   */
  runTimePropertyList?: Array<RunTimeProperty>
}

/**
 * 集体报名模板查询
 */
export class CollectiveSignUpByOriginalCollectiveSignupRequest {
  originalCollectiveSignupNo: string
  /**
   * 默认密码
   */
  password?: string
  /**
   * 密码模式
1-默认密码——password
2-使用身份证后六位
   */
  passwordModel?: number
  /**
   * 密码生效范围，1-默认仅新用户，2-全部用户（含已注册）
   */
  passwordEffectiveRange?: number
}

/**
 * 集体报名模板查询
 */
export class CollectiveSignUpModelQueryRequest {
  /**
   * 主任务分类
集体报名分类-NORMAL_IMPORT
   */
  category?: string
}

/**
 * 属性名分组获取数量请求
<AUTHOR>
 */
export class FindCountGroupByKeyRequest {
  /**
   * 集体报名编号
   */
  collectiveSignupNo: string
  /**
   * 指定需要分组统计的key名称
   */
  keyName: string
  /**
   * 0 - 查询所有
1 - 筛选导入成功数据
2 - 筛序提交成功数据
   */
  type: number
}

/**
 * 请求更新的报名数据
<AUTHOR>
@since 2022/4/20
 */
export class UpdateSignupDataRequest {
  /**
   * 子任务数据编号
   */
  subTaskDataId?: string
  /**
   * 一条（或行）数据
   */
  dateRow?: Array<MetaProperty>
}

/**
 * 单个属性元数据
<AUTHOR>
@since 2022/4/20
 */
export class MetaProperty1 {
  /**
   * 属性键
   */
  key: string
  /**
   * 属性值
   */
  value: string
}

/**
 * 数据行对象
<AUTHOR>
@since 2022/4/24
 */
export class MetaRow {
  /**
   * 每一行的数据-初始数据属性
   */
  row: Array<MetaProperty1>
  /**
   * 运行时获取的属性
   */
  runtimeProperties: Array<RunTimeProperty1>
  /**
   * @see RunTimeDefineType
运行时获取的属性定义
SCHEME_ID，学习方案ID，就绪成功时获取
   */
  runTimeDefineList: Array<string>
  /**
   * 处理结果是否成功
true/false，成功/失败
   */
  success: boolean
  /**
   * 失败原因，仅当success&#x3D;false时，有值
   */
  errorMessage: string
}

/**
 * 查询数据的数据架构
<AUTHOR>
@since 2022/4/24
 */
export class MetaSchema {
  /**
   * 列属性key名称
   */
  propertyName: string
  /**
   * 列编号
   */
  columnNumber: number
  /**
   * 是否必填
   */
  require: boolean
  /**
   * 是否唯一项
   */
  unique: boolean
  /**
   * 展示名称
   */
  field: string
  /**
   * 校验器集合
   */
  validators: Array<string>
}

/**
 * 单个属性元数据-任务执行期间获取
<AUTHOR>
@since 2022/4/20
 */
export class RunTimeProperty1 {
  /**
   * 属性键
   */
  key: string
  /**
   * 属性值
   */
  value: string
}

/**
 * @Description 最新的任务状态
<AUTHOR>
@Date 2023/9/5 10:31
 */
export class CollectiveSignTaskInfoResponse {
  /**
   * 任务分类
   */
  category: string
  /**
   * 状态
   */
  state: TaskState
  /**
   * 处理结果
   */
  result: ProcessResult
  /**
   * 处理信息
   */
  message: string
  /**
   * 来源批次号
   */
  originalCollectiveSignup: string
  /**
   * excel解析状态
@see TaskConstant.AnalysisState
   */
  analysisState: number
  /**
   * 主任务编号
   */
  mainTaskId: string
  /**
   * 总主任务数
   */
  total: number
}

/**
 * 批次单请求线下支付结果
异常code
500-接口异常
3003-表头校验失败
3004-excel表最大长度校验失败
20001-当前正在导入报名学员信息，请等导入任务完成后再上传新的报名表
20002-该集体缴费已提交或已完成，无法继续报名
<AUTHOR>
@since 2022/5/12
 */
export class ImportCollectiveSignupResponse {
  /**
   * 批次订单号
   */
  batchOrderNo: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * 获取指定的属性名分组的数量响应对象
<AUTHOR>
 */
export class KeyGroupResponse {
  /**
   * 指定的属性名
   */
  key: string
  /**
   * 指定的属性名分组的数量
   */
  count: number
}

/**
 * 数据架构返回值
<AUTHOR>
@since 2022/4/24
 */
export class SchemaResponse {
  /**
   * 不同列的数据架构
   */
  metaSchemas: Array<MetaSchema>
}

/**
 * 报名数据解析响应
<AUTHOR>
 */
export class SignupDataAnalysisResponse {
  /**
   * 解析状态
0&#x3D;未解析，1-解析中，2&#x3D;解析完成
   */
  processStatus: number
  /**
   * 总数
   */
  totalCount: number
  /**
   * 已执行数
   */
  executedCount: number
}

/**
 * 报名数据执行进度响应
<AUTHOR>
 */
export class SignupDataProcessResponse {
  /**
   * 指定批次下是否全部所有主任务执行成功
   */
  completed: boolean
  /**
   * 总数
   */
  totalCount: number
  /**
   * 已执行数
   */
  executedCount: number
}

/**
 * 任务执行情况
<AUTHOR>
@since 2022/5/5
 */
export class TaskExecuteResponse {
  /**
   * 任务编号
   */
  taskId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 所属批次单编号
   */
  batchNo: string
  /**
   * 状态
0- 已创建
1- 已就绪
2- 执行中
3- 执行完成
   */
  state: number
  /**
   * 处理结果
0- 未处理
1- 成功
2- 失败
3- 就绪失败
   */
  result: number
  /**
   * 处理信息
   */
  message: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 就绪时间
   */
  alreadyTime: string
  /**
   * 执行时间
   */
  executingTime: string
  /**
   * 完成时间
   */
  completedTime: string
  /**
   * 总数
   */
  totalCount: number
  /**
   * 成功数量
   */
  successCount: number
  /**
   * 失败数量
   */
  failCount: number
}

export class MetaRowPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<MetaRow>
}

export class TaskExecuteResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TaskExecuteResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 异步导出提交集体报名失败数据
   * @param collectiveSignupNo 集体报名编号
   * @return 导出数据ID
   * @param query 查询 graphql 语法文档
   * @param collectiveSignupNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async asyncExportCollectiveSignupExcuteFailExcel(
    collectiveSignupNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.asyncExportCollectiveSignupExcuteFailExcel,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { collectiveSignupNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 报名数据excel解析进度
   * @param collectiveSignupNo 集体报名编号
   * @return 报名数据解析响应
   * @param query 查询 graphql 语法文档
   * @param collectiveSignupNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async collectiveSignupDataAnalysis(
    collectiveSignupNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.collectiveSignupDataAnalysis,
    operation?: string
  ): Promise<Response<SignupDataAnalysisResponse>> {
    return commonRequestApi<SignupDataAnalysisResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { collectiveSignupNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 提交后报名数据执行进度
   * @param collectiveSignupNo 集体报名编号
   * @return 报名数据执行响应
   * @param query 查询 graphql 语法文档
   * @param collectiveSignupNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async collectiveSignupDataProcess(
    collectiveSignupNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.collectiveSignupDataProcess,
    operation?: string
  ): Promise<Response<SignupDataProcessResponse>> {
    return commonRequestApi<SignupDataProcessResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { collectiveSignupNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出导入集体报名失败数据
   * @param collectiveSignupNo 集体报名编号
   * @return excel文件路径
   * @param query 查询 graphql 语法文档
   * @param collectiveSignupNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCollectiveSignupImportFailExcel(
    collectiveSignupNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCollectiveSignupImportFailExcel,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { collectiveSignupNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询当前网校下的数据架构
   * @return 数据架构结构列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findCollectiveSignupMetaSchema(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findCollectiveSignupMetaSchema,
    operation?: string
  ): Promise<Response<SchemaResponse>> {
    return commonRequestApi<SchemaResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findCommitCompleteAndFailSubTuskFailDataByPage(
    params: { request?: CollectiveSignQueryRequest; page: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findCommitCompleteAndFailSubTuskFailDataByPage,
    operation?: string
  ): Promise<Response<MetaRowPage>> {
    return commonRequestApi<MetaRowPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findCommitCompleteAndSuccessSubTuskSuccessDataByPage(
    params: { request?: CollectiveSignQueryRequest; page: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findCommitCompleteAndSuccessSubTuskSuccessDataByPage,
    operation?: string
  ): Promise<Response<MetaRowPage>> {
    return commonRequestApi<MetaRowPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 属性名分组获取数量
   * @param request 属性名
   * @return 属性名分组获取数量响应对象集合
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findCountGroupByKey(
    request: FindCountGroupByKeyRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findCountGroupByKey,
    operation?: string
  ): Promise<Response<Array<KeyGroupResponse>>> {
    return commonRequestApi<Array<KeyGroupResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findImportCollectiveSignupCompleteSuccessDataByPage(
    params: { request?: CollectiveSignQueryRequest; page: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findImportCollectiveSignupCompleteSuccessDataByPage,
    operation?: string
  ): Promise<Response<MetaRowPage>> {
    return commonRequestApi<MetaRowPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findImportCollectiveSignupFailDataByPage(
    params: { request?: CollectiveSignQueryRequest; page: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findImportCollectiveSignupFailDataByPage,
    operation?: string
  ): Promise<Response<MetaRowPage>> {
    return commonRequestApi<MetaRowPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 报名数据excel最新主任务情况
   * @param collectiveSignupNo 集体报名编号
   * @return 报名数据解析响应
   * @param query 查询 graphql 语法文档
   * @param collectiveSignupNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findLastRecordByBatch(
    collectiveSignupNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findLastRecordByBatch,
    operation?: string
  ): Promise<Response<CollectiveSignTaskInfoResponse>> {
    return commonRequestApi<CollectiveSignTaskInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { collectiveSignupNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTaskExecuteResponsePage(
    params: { collectiveSignupNo: string; page: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findTaskExecuteResponsePage,
    operation?: string
  ): Promise<Response<TaskExecuteResponsePage>> {
    return commonRequestApi<TaskExecuteResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询模板地址
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryTemplatePathByCategory(
    request: CollectiveSignUpModelQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryTemplatePathByCategory,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 清空失败数据
   * @param collectiveSignupNo 集体报名编号
   * @param mutate 查询 graphql 语法文档
   * @param collectiveSignupNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async clearFailureData(
    collectiveSignupNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.clearFailureData,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { collectiveSignupNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 提交集体报名
   * @param collectiveSignupNo 集体报名编号
   * @param mutate 查询 graphql 语法文档
   * @param collectiveSignupNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async commitCollectiveSignup(
    collectiveSignupNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.commitCollectiveSignup,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { collectiveSignupNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除集体报名
   * @param collectiveSignupNo 集体报名编号
   * @param mutate 查询 graphql 语法文档
   * @param collectiveSignupNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteCollectiveSignup(
    collectiveSignupNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteCollectiveSignup,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { collectiveSignupNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除报名数据
   * @param collectiveSignupNo 集体报名编号
   * @param subTaskId          子任务id
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteSignupData(
    params: { collectiveSignupNo: string; subTaskId: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteSignupData,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导入集体缴费报名，目前江苏工考项目，国资人才项目在用
   * 后续如果需要返回错误码，这个接口弃用，改为importCollectiveSignupForCompatible或importCollectiveSignupForVerify
   * @param importRequest 导入信息
   * @return 集体报名编号
   * @param mutate 查询 graphql 语法文档
   * @param importRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async importCollectiveSignup(
    importRequest: CollectiveSignImportInfoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.importCollectiveSignup,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { importRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导入集体缴费报名，闽西平台使用，不包含密码优化版本入参
   * @param importRequest 导入信息
   * @return 集体报名编号
   * @param mutate 查询 graphql 语法文档
   * @param importRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async importCollectiveSignupForCompatible(
    importRequest: CollectiveSignImportInfoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.importCollectiveSignupForCompatible,
    operation?: string
  ): Promise<Response<ImportCollectiveSignupResponse>> {
    return commonRequestApi<ImportCollectiveSignupResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { importRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导入集体缴费报名，通用平台使用，包含密码优化版本
   * @param importRequest 导入信息
   * @return 集体报名编号
   * @param mutate 查询 graphql 语法文档
   * @param importRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async importCollectiveSignupForVerify(
    importRequest: CollectiveSignImportInfoForVerifyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.importCollectiveSignupForVerify,
    operation?: string
  ): Promise<Response<ImportCollectiveSignupResponse>> {
    return commonRequestApi<ImportCollectiveSignupResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { importRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 集体缴费重新报名
   * @param originalCollectiveSignupNo 指定需要重新报名的集体缴费报名编号
   * @return 新生成的集体缴费报名编号
   * @param mutate 查询 graphql 语法文档
   * @param originalCollectiveSignupNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async signupByOriginalCollectiveSignup(
    originalCollectiveSignupNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.signupByOriginalCollectiveSignup,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { originalCollectiveSignupNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 集体缴费重新报名
   * @param request 请求
   * @return 新生成的集体缴费报名编号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async signupByOriginalCollectiveSignupWithInfo(
    request: CollectiveSignUpByOriginalCollectiveSignupRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.signupByOriginalCollectiveSignupWithInfo,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改报名数据
   * @param collectiveSignupNo 集体报名编号
   * @param signupDataList     一批的报名数据修改
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateSignupData(
    params: { collectiveSignupNo: string; signupDataList: Array<UpdateSignupDataRequest> },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateSignupData,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
