import getCertificateTemplate from './queries/getCertificateTemplate.graphql'
import getIssueConfigureInfoInDistributor from './queries/getIssueConfigureInfoInDistributor.graphql'
import getIssueConfigureInfoInServicer from './queries/getIssueConfigureInfoInServicer.graphql'
import getMySchemeConfig from './queries/getMySchemeConfig.graphql'
import getMySchemeConfigByRequest from './queries/getMySchemeConfigByRequest.graphql'
import getMySchemeIssueConfigInMySelf from './queries/getMySchemeIssueConfigInMySelf.graphql'
import getRecentTrainingQualificationInMyself from './queries/getRecentTrainingQualificationInMyself.graphql'
import getSchemeConfigByRequestInServicer from './queries/getSchemeConfigByRequestInServicer.graphql'
import getSchemeConfigInDistributor from './queries/getSchemeConfigInDistributor.graphql'
import getSchemeConfigInServicer from './queries/getSchemeConfigInServicer.graphql'
import getSchemeConfigInSubject from './queries/getSchemeConfigInSubject.graphql'
import getSchemeIssuePlanItemInDistributor from './queries/getSchemeIssuePlanItemInDistributor.graphql'
import getSchemeIssuePlanItemInServicer from './queries/getSchemeIssuePlanItemInServicer.graphql'
import getSchemeLearningBySubOrderInMyself from './queries/getSchemeLearningBySubOrderInMyself.graphql'
import getSchemeLearningDetailInMyself from './queries/getSchemeLearningDetailInMyself.graphql'
import getSchemeLearningInMyself from './queries/getSchemeLearningInMyself.graphql'
import getStudentPlanSignRecordCountInMyself from './queries/getStudentPlanSignRecordCountInMyself.graphql'
import getTeacherListInServicer from './queries/getTeacherListInServicer.graphql'
import listSchemeSkuBatchedInMyself from './queries/listSchemeSkuBatchedInMyself.graphql'
import listSchemeSkuBatchedInMyselfBTXPXPT from './queries/listSchemeSkuBatchedInMyselfBTXPXPT.graphql'
import listSchemeSkuForStudentInMyself from './queries/listSchemeSkuForStudentInMyself.graphql'
import listStudentReportRecordInMyself from './queries/listStudentReportRecordInMyself.graphql'
import pageSchemeConfigByRequestInServicer from './queries/pageSchemeConfigByRequestInServicer.graphql'
import pageSchemeConfigInDistributor from './queries/pageSchemeConfigInDistributor.graphql'
import pageSchemeConfigInServicer from './queries/pageSchemeConfigInServicer.graphql'
import pageSchemeIssueConfigListInDistributor from './queries/pageSchemeIssueConfigListInDistributor.graphql'
import pageSchemeIssueConfigListOptionalLoginInServicer from './queries/pageSchemeIssueConfigListOptionalLoginInServicer.graphql'
import pageSchemeLearningInMyself from './queries/pageSchemeLearningInMyself.graphql'
import pageStatisticsStudentSchemeLearningInCollective from './queries/pageStatisticsStudentSchemeLearningInCollective.graphql'
import pageStudentSchemeConfigInMySelf from './queries/pageStudentSchemeConfigInMySelf.graphql'
import pageStudentSchemeLearningBatchedInMyself from './queries/pageStudentSchemeLearningBatchedInMyself.graphql'
import pageStudentSchemeLearningInCollective from './queries/pageStudentSchemeLearningInCollective.graphql'
import pageStudentSchemeLearningInCollectiveBTXPXPT from './queries/pageStudentSchemeLearningInCollectiveBTXPXPT.graphql'
import pageTrainingClassroomInDistributor from './queries/pageTrainingClassroomInDistributor.graphql'
import pageTrainingClassroomInMyself from './queries/pageTrainingClassroomInMyself.graphql'
import pageTrainingPointInDistributor from './queries/pageTrainingPointInDistributor.graphql'

export {
  getCertificateTemplate,
  getIssueConfigureInfoInDistributor,
  getIssueConfigureInfoInServicer,
  getMySchemeConfig,
  getMySchemeConfigByRequest,
  getMySchemeIssueConfigInMySelf,
  getRecentTrainingQualificationInMyself,
  getSchemeConfigByRequestInServicer,
  getSchemeConfigInDistributor,
  getSchemeConfigInServicer,
  getSchemeConfigInSubject,
  getSchemeIssuePlanItemInDistributor,
  getSchemeIssuePlanItemInServicer,
  getSchemeLearningBySubOrderInMyself,
  getSchemeLearningDetailInMyself,
  getSchemeLearningInMyself,
  getStudentPlanSignRecordCountInMyself,
  getTeacherListInServicer,
  listSchemeSkuBatchedInMyself,
  listSchemeSkuBatchedInMyselfBTXPXPT,
  listSchemeSkuForStudentInMyself,
  listStudentReportRecordInMyself,
  pageSchemeConfigByRequestInServicer,
  pageSchemeConfigInDistributor,
  pageSchemeConfigInServicer,
  pageSchemeIssueConfigListInDistributor,
  pageSchemeIssueConfigListOptionalLoginInServicer,
  pageSchemeLearningInMyself,
  pageStatisticsStudentSchemeLearningInCollective,
  pageStudentSchemeConfigInMySelf,
  pageStudentSchemeLearningBatchedInMyself,
  pageStudentSchemeLearningInCollective,
  pageStudentSchemeLearningInCollectiveBTXPXPT,
  pageTrainingClassroomInDistributor,
  pageTrainingClassroomInMyself,
  pageTrainingPointInDistributor
}
