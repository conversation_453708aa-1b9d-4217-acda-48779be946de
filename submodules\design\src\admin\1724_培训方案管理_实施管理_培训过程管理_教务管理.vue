<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">实施管理</el-breadcrumb-item>
      <el-breadcrumb-item>教务管理</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">培训方案信息</span>
        </div>
        <el-form ref="form" :model="form" label-width="150px" class="m-form">
          <el-form-item label="方案名称：">
            <el-tag class="f-mr10">2024</el-tag>
            <el-tag type="success" class="f-mr10">地区</el-tag>
            <el-tag type="warning" class="f-mr10">培训类别</el-tag>
            <el-tag type="danger" class="f-mr10">培训专业</el-tag>
            读取方案名称
          </el-form-item>
          <el-form-item label="期别名称：">读取期别名称</el-form-item>
          <el-form-item label="报到时间段：">xxxx-xx-xx至xxxx-xx-xx</el-form-item>
          <el-form-item label="培训时间段：">xxxx-xx-xx至xxxx-xx-xx</el-form-item>
        </el-form>
      </el-card>
      <!--
<el-card shadow="never" class="m-card f-mb15">
  <div class="m-edu-admin">
    <div class="section">
      <div class="title">
        <div class="icon el-icon-user-solid"></div>
        <div class="text">培训报到管理</div>
      </div>
      <div class="content">
        <div class="item">
          <div class="tit">报名人数</div>
          <div class="con"> 50</div>
        </div>
        <div class="item">
          <div class="tit">已报到人数</div>
          <div class="con"> 49</div>
        </div>
      </div>
    </div>
    <div class="section">
      <div class="title">
        <div class="icon el-icon-s-grid"></div>
        <div class="text">学员住宿管理</div>
      </div>
      <div class="content">
        <div class="item">
          <div class="tit">需要住宿学员</div>
          <div class="con"> 50</div>
        </div>
        <div class="item">
          <div class="tit">无需住宿学员</div>
          <div class="con"> 49</div>
        </div>
      </div>
    </div>
    <div class="section">
      <div class="title">
        <div class="icon el-icon-s-claim"></div>
        <div class="text">培训考勤管理</div>
      </div>
      <div class="content">
        <div class="item">
          <div class="tit">考勤要求</div>
          <div class="con"> 考勤率 > X%</div>
        </div>
      </div>
    </div>
    <div class="section">
      <div class="title">
        <div class="icon el-icon-s-management"></div>
        <div class="text">结业测试管理</div>
      </div>
      <div class="content">
        <div class="item">
          <div class="tit">报名人数</div>
          <div class="con"> 50</div>
        </div>
        <div class="item">
          <div class="tit">合格人数</div>
          <div class="con"> 49</div>
        </div>
      </div>
    </div>
    <div class="section z-cur">
      <div class="title">
        <div class="icon el-icon-s-data"></div>
        <div class="text">问卷管理</div>
      </div>
      <div class="content">
        <div class="item">
          <div class="tit">期别问卷数量</div>
          <div class="con"> 5</div>
        </div>
        <div class="item">
          <div class="tit">提交问卷人数</div>
          <div class="con"> 200</div>
        </div>
      </div>
    </div>
  </div>
</el-card>
-->
      <el-tabs v-model="activeName2" type="card" class="m-tab-card">
        <el-tab-pane label="教务管理" name="first">
          <el-card shadow="never" class="m-card">
            <!--条件查询-->
            <el-row :gutter="16" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="姓名">
                    <el-input v-model="input" clearable placeholder="请输入姓名" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="证件号">
                    <el-input v-model="input" clearable placeholder="请输入证件号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="手机号">
                    <el-input v-model="input" clearable placeholder="请输入手机号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="8" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>导出列表数据</el-button>
                    <el-button>导入结业成果</el-button>
                    <el-button>重置</el-button>
                    <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                    <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="姓名" min-width="160">
                <template>这是学员姓名</template>
              </el-table-column>
              <el-table-column label="证件号" min-width="160">
                <template>352203198812290022</template>
              </el-table-column>
              <el-table-column label="学号" min-width="140">
                <template>123456</template>
              </el-table-column>
              <el-table-column label="手机号" min-width="140">
                <template>18888888888</template>
              </el-table-column>
              <el-table-column label="专业类别" min-width="200">
                <template></template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">建筑工程<el-tag size="mini" class="f-ml5">主项</el-tag></div>
                  <div v-else>建筑工程<el-tag type="warning" size="mini" class="f-ml5">增项</el-tag></div>
                </template>
              </el-table-column>
              <el-table-column label="工作单位" min-width="200">
                <template>读取单位名称</template>
              </el-table-column>
              <el-table-column label="是否住宿" min-width="100" align="center">
                <template>单住</template>
              </el-table-column>
              <el-table-column label="是否报到" min-width="180" align="center">
                <template>
                  <p>已打卡报到</p>
                  <p>2024-10-30 15:33:33</p>
                </template>
              </el-table-column>
              <el-table-column label="签到/签退次数" min-width="200" align="center">
                <template>
                  <p>GPS定位签到次数：0次</p>
                  <p>GPS定位签退次数：0次</p>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
            <!--空数据-->
            <div class="m-no-date">
              <img class="img" src="./assets/images/no-data-normal.png" alt="" />
              <div class="date-bd">
                <p class="f-f15 f-c9">暂无数据~</p>
              </div>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="问卷管理" name="second">
          查看1725_培训方案管理_实施管理_培训过程管理_问卷管理.vue
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      const data = [
        {
          id: 1,
          label: '一级 1',
          children: [
            {
              id: 4,
              label: '二级 1-1'
            }
          ]
        }
      ]
      return {
        num: 1,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        input1: '1',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        tableData1: [
          {
            id: 1,
            field01: '这里是分类名称',
            hasChildren: true
          },
          {
            id: 2,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 3,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 4,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 5,
            field01: '分类名称1',
            hasChildren: true
          }
        ],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down'],
        data
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      },
      load(tree, treeNode, resolve) {
        setTimeout(() => {
          resolve([
            {
              id: 11,
              field01: '分类名称1-1'
            },
            {
              id: 12,
              field01: '分类名称1-2'
            }
          ])
        }, 1000)
      }
    }
  }
</script>
