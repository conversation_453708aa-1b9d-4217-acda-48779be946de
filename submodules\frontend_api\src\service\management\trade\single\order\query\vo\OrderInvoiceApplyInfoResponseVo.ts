import {
  InvoiceApplyInfoResponse,
  OfflineInvoiceDeliveryInfoResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { InvoiceStatusEnum } from '@api/service/customer/trade/single/invoice/enum/InvoiceEnum'

export default class OrderInvoiceApplyInfoResponseVo extends InvoiceApplyInfoResponse {
  /**
   * 开票时间
   */
  invoiceDate = ''
  /**
   * 发票状态 0:未开具 1：开票中 2：开票成功 3：开票失败4 冻结中
   */
  invoiceStatus?: InvoiceStatusEnum
  //发票号
  orderNum = ''
  /**
   * 蓝票下载地址
   */
  blueFilePath: string
  /**
   * 蓝票下载地址 xml
   */
  blueFileXmlPath: string
  /**
   * 蓝票下载地址 ofd
   */
  blueFileOfdPath: string
  /**
   * 发票配送信息
   */
  deliveryInfo: OfflineInvoiceDeliveryInfoResponse
}
