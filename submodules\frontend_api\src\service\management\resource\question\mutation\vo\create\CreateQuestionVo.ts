import { QuestionDifficulty } from './../../enum/QuestionDifficultyType'
import { CreateQuestionRequest } from '@api/ms-gateway/ms-examquestion-v1'
import { QuestionTypeEnum } from '@api/service/common/enums/question/QuestionType'
import QuestionOperation from '../../../helper/QuestionOperation'

class CreateQuestionRequestDto extends QuestionOperation {
  /**
   * 试题题目【必填】
   */
  topic = ''
  /**
   * 试题类型【必填】
   */
  questionType: number = QuestionTypeEnum.radio
  /**
   * 所属题库ID【必填】
   */
  libraryId = ''
  /**
   * 是否启用
   */
  enabled = true
  /**
   * 试题解析
   */
  dissects?: string = ''
  /**
   * 关联课程id
   */
  relateCourseId?: string = ''

  /* 
    试题难度
    1简单 2 中等 3难
  */
  questionDifficulty: number = QuestionDifficulty.Simple

  toDto(): CreateQuestionRequest {
    //子类模型实现该方法
    return
  }
}

export default CreateQuestionRequestDto
