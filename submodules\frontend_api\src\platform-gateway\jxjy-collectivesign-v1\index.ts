import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/jxjy-collectivesign-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'jxjy-collectivesign-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 导出任务数据地址请求
 */
export class ExportTaskDataRequest {
  /**
   * 任务编号
   */
  taskId: string
}

/**
 * 查询任务信息
 */
export class QueryForTaskInfoRequest {
  /**
   * 所属批次单编号
   */
  batchNo: string
  /**
   * 分类
   */
  category: string
}

/**
 * 导出任务数据地址响应
<AUTHOR>
@date 2022/11/7 14:12
 */
export class ExportTaskDataResponse {
  /**
   * 文件名称
   */
  fileName: string
  /**
   * 文件路径
   */
  filePath: string
}

/**
 * 查询任务信息
 */
export class QueryTaskInfoResponse {
  /**
   * 任务编号
   */
  mainTaskId: string
  /**
   * 起始执行时间
   */
  executeStartTime: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
}

export class QueryTaskInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<QueryTaskInfoResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 异步导出提交集体报名失败数据
   * @param collectiveSignupNo 集体报名编号
   * @return 导出数据ID
   * @param query 查询 graphql 语法文档
   * @param collectiveSignupNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async asyncExportCollectiveSignupExcuteFailExcel(
    collectiveSignupNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.asyncExportCollectiveSignupExcuteFailExcel,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { collectiveSignupNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出异常数据
   * @param request 导出异常数据请求
   * @return 导出异常数据响应
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportFail(
    request: ExportTaskDataRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportFail,
    operation?: string
  ): Promise<Response<ExportTaskDataResponse>> {
    return commonRequestApi<ExportTaskDataResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询导入/导出任务执行情况
   * @param request 任务查询参数
   * @param page    分页数据
   * @return 任务执行情况分页
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryForTask(
    params: { request?: QueryForTaskInfoRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryForTask,
    operation?: string
  ): Promise<Response<QueryTaskInfoResponsePage>> {
    return commonRequestApi<QueryTaskInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
