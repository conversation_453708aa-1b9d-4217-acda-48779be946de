import { OrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { OrderStatusEnum } from '@api/service/common/trade/OrderStatusEnum'
import TradeModule from '@api/service/management/trade/TradeModule'
import { Page } from '@hbfe/common'
import InvoiceListResponse from '@api/service/management/trade/single/invoice/query/vo/InvoiceListResponse'
import OrderInvoiceApplyInfoResponseVo from '@api/service/management/trade/single/order/query/vo/OrderInvoiceApplyInfoResponseVo'
import { InvoiceStatusEnum } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'

export default class MyOrderVo extends OrderResponse {
  //UI层展示的状态
  orderStatue = OrderStatusEnum.QueryOrderStatusEnumWaitPay
  // * 发票列表
  invoiceApplyInfoList: OrderInvoiceApplyInfoResponseVo[] = []

  changeStatue() {
    if (this.basicData.orderStatus == 1) {
      if (this.basicData.orderPaymentStatus == 0) {
        this.orderStatue = OrderStatusEnum.QueryOrderStatusEnumWaitPay
      } else if (this.basicData.orderPaymentStatus == 1) {
        this.orderStatue = OrderStatusEnum.QueryOrderStatusEnumPaying
      } else if (this.basicData.orderPaymentStatus == 2) {
        this.orderStatue = OrderStatusEnum.QueryOrderStatusEnumOpening
      }
    } else if (this.basicData.orderStatus == 2) {
      this.orderStatue = OrderStatusEnum.QueryOrderStatusEnumCompleted
    } else if (this.basicData.orderStatus == 3) {
      this.orderStatue = OrderStatusEnum.QueryOrderStatusEnumInvalid
    }
  }
  async addInvoice() {
    //如果关联发票有值，那么取关联发票
    if (this.invoiceApplyInfo && this.invoiceApplyInfo.invoiceIdList && this.invoiceApplyInfo.invoiceIdList.length) {
      try {
        let invoiceList
        if (this.invoiceApplyInfo.invoiceMethod == 1) {
          invoiceList = await TradeModule.singleTradeBatchFactor.invoiceFactor.queryInvoice.onLineGetInvoiceInServicer(
            this.invoiceApplyInfo.invoiceIdList
          )
        } else {
          invoiceList = await TradeModule.singleTradeBatchFactor.invoiceFactor.queryOffLineInvoice.offLineGetInvoiceInServicer(
            this.invoiceApplyInfo.invoiceIdList,
            new Page(1, this.invoiceApplyInfo.invoiceIdList.length)
          )
        }
        let invoiceNums: string[] = []
        if (this.invoiceApplyInfo.invoiceMethod == 2) {
          invoiceNums = (invoiceList as OffLinePageInvoiceVo[]).map(item => item.invoiceNo)
        } else {
          invoiceNums = (invoiceList as InvoiceListResponse[]).map(item => item.blueInvoiceNo)
        }

        ;(invoiceList as any[]).map((res: any) => {
          const temp = new OrderInvoiceApplyInfoResponseVo()
          temp.account = res?.account
          // temp.invoiceType = res?.invoiceType
          temp.invoiceCategory = res?.invoiceCategory
          temp.title = res?.title
          temp.titleType = res?.titleType
          temp.taxpayerNo = res?.taxpayerNo
          temp.address = res?.address
          temp.phone = res?.invoiceFaceInfo.phone

          temp.bankName = res?.bankName
          temp.contactPhone = res?.contactPhone
          temp.contactEmail = res?.contactEmail
          temp.email = res?.email
          temp.remark = res?.remark
          temp.appliedTime = res?.applyForDate
          temp.orderNum = invoiceNums.join(',')
          temp.invoiceDate = res?.invoiceDate
          temp.invoiceStatus = res?.invoiceStatus
          if (res?.invoiceFreezeStatus) {
            temp.invoiceStatus = 4
          }
          if (temp.invoiceMethod == 2) {
            temp.deliveryInfo = (res as OffLinePageInvoiceVo)?.deliveryInfo
          }
          temp.blueFilePath = (res as InvoiceListResponse)?.blueFilePath
          console.log('有关联发票')
          this.invoiceApplyInfoList.push(temp)
        })

        const firstInvoice = invoiceList[0]

        this.invoiceApplyInfo.account = firstInvoice.account
        // this.invoiceApplyInfo.invoiceType = firstInvoice.invoiceType
        this.invoiceApplyInfo.invoiceCategory = firstInvoice.invoiceCategory
        this.invoiceApplyInfo.title = firstInvoice.title
        this.invoiceApplyInfo.titleType = firstInvoice.titleType
        this.invoiceApplyInfo.taxpayerNo = firstInvoice.taxpayerNo
        this.invoiceApplyInfo.address = firstInvoice.address
        this.invoiceApplyInfo.phone = firstInvoice.invoiceFaceInfo.phone

        this.invoiceApplyInfo.bankName = firstInvoice.bankName
        this.invoiceApplyInfo.contactPhone = firstInvoice.contactPhone
        this.invoiceApplyInfo.contactEmail = firstInvoice?.contactEmail
        this.invoiceApplyInfo.email = firstInvoice?.email
        this.invoiceApplyInfo.remark = firstInvoice.remark
        this.invoiceApplyInfo.appliedTime = firstInvoice.applyForDate
        ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).orderNum = invoiceNums.join(',')
        ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceDate = firstInvoice.invoiceDate
        ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceStatus = firstInvoice.invoiceStatus
        if (firstInvoice.invoiceFreezeStatus) {
          ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceStatus = 4
        }
        if (this.invoiceApplyInfo.invoiceMethod == 2) {
          ;(this
            .invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).deliveryInfo = (firstInvoice as OffLinePageInvoiceVo).deliveryInfo
        }
        ;(this
          .invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).blueFilePath = (firstInvoice as InvoiceListResponse).blueFilePath
        console.log('有关联发票')
      } catch (e) {
        console.log(e)
      }
    } else {
      if (this.invoiceApplyInfo) {
        ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceStatus = InvoiceStatusEnum.NOTPTOOPEN
      }
    }
    return this
  }
}
