/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-05-19 09:16:05
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-01-26 10:15:14
 */
import msBasicDataQuery, {
  AccountRequest,
  AuthenticationRequest,
  CollectiveInfoResponse,
  CollectiveQueryRequest,
  DateScopeRequest,
  EnterprisePersonSortFieldEnum,
  SortTypeEnum,
  UserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import DataResolve from '@api/service/common/utils/DataResolve'
import CollectiveManagerInfoVo from '@api/service/management/user/query/manager/vo/CollectiveManagerInfoVo'
import CollectiveManagerQueryIdVo from '@api/service/management/user/query/manager/vo/CollectiveManagerQueryIdVo'
import { UiPage } from '@hbfe/common'

/**
 * @description
 */
class QueryCollectiveManagerList {
  /**
   * 查询集体缴费管理员id集合
   * @param {CollectiveManagerQueryIdVo} queryParams
   */
  async queryCollectiveManagerIdList(queryParams: CollectiveManagerQueryIdVo): Promise<string[]> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 200
    const res = await msBasicDataQuery.pageCollectiveInfoInServicer({
      page,
      request: queryParams.to()
    })
    const idList = [] as string[]
    if (res.status?.isSuccess() && DataResolve.isWeightyArr(res.data?.currentPageData)) {
      res.data.currentPageData.map((item: CollectiveInfoResponse) => {
        idList.push(item.userInfo?.userId ?? '')
      })
    }
    const result = [...new Set(idList)]
    return result
  }

  /**
   * 根据用户id集合查询集体缴费管理员信息列表
   */
  async queryCollectiveManagerInfoList(userIds: string[]): Promise<CollectiveManagerInfoVo[]> {
    const params = new CollectiveQueryRequest()
    params.user = new UserRequest()
    params.account = new AccountRequest()
    params.account.accountTypeList = [3]
    params.user.userIdList = [...new Set(userIds)]
    params.sortList = [{ sortField: EnterprisePersonSortFieldEnum.accountType, sortType: SortTypeEnum.DESC }]
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = params.user.userIdList?.length || 1
    const res = await msBasicDataQuery.pageCollectiveInfoInServicer({
      page,
      request: params
    })
    const result = [] as CollectiveManagerInfoVo[]
    if (res.status?.isSuccess() && DataResolve.isWeightyArr(res.data?.currentPageData)) {
      res.data.currentPageData.forEach(item => {
        result.push(CollectiveManagerInfoVo.from(item))
      })
    }
    return result
  }
  /**
   * 查询集体报名管理员列表
   * @param page
   * @param request
   * @param sort
   */
  async queryPageCollectiveList(
    page: UiPage,
    param?: {
      name: string
      phone?: string
      createStartTime?: string
      createEndTime?: string
      adminAccount?: string
      idCard?: string
    }
  ) {
    const collectiveQueryRequest = new CollectiveQueryRequest()
    collectiveQueryRequest.user = new UserRequest()
    collectiveQueryRequest.authentication = new AuthenticationRequest()
    collectiveQueryRequest.authentication.identity = param.adminAccount
    collectiveQueryRequest.user.userName = param.name
    collectiveQueryRequest.user.userNameMatchType = 2
    collectiveQueryRequest.user.phone = param.phone
    collectiveQueryRequest.user.idCard = param.idCard
    collectiveQueryRequest.account = new AccountRequest()
    collectiveQueryRequest.account.createTimeScope = new DateScopeRequest()
    collectiveQueryRequest.account.createTimeScope.beginTime = param.createStartTime
    collectiveQueryRequest.account.createTimeScope.endTime = param.createEndTime
    // 江苏固定传3
    collectiveQueryRequest.account.accountTypeList = [3]
    collectiveQueryRequest.sortList = [
      { sortField: EnterprisePersonSortFieldEnum.accountType, sortType: SortTypeEnum.DESC }
    ]
    const { data } = await msBasicDataQuery.pageCollectiveInfoInServicer({
      page,
      request: collectiveQueryRequest
    })
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    return data.currentPageData
  }
  /**
   * 查询集体报名管理员详情
   * @param collectiveId 集体管理员ID
   */
  async queryCollectiveInfoInServicer(collectiveId: string) {
    const { data } = await msBasicDataQuery.getCollectiveInfoInServicer(collectiveId)
    return data
  }
}

export default QueryCollectiveManagerList
