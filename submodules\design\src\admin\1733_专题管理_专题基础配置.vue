<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-button @click="dialog1 = true" type="primary" class="f-mr20 f-mb20">专题基础配置</el-button>
        <el-drawer
          title="专题基础配置"
          :visible.sync="dialog1"
          :direction="direction"
          size="640px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="f-tr">
              <el-link type="primary" :underline="false"
                ><i class="el-icon-picture f-f20 f-mr5 f-vm"></i>查看专题操作示例</el-link
              >
            </div>
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10">
              <el-form-item label="专题是否开启：" required>
                <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
              </el-form-item>
              <el-form-item label="网校展示专题方案：" required>
                <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                  <div slot="content">
                    <p>支持设置专题内方案展示的规则：</p>
                    <p></p>
                    <p>1.仅对设置展示在网校门户专题生效。</p>
                    <p></p>
                    <p>2.如开启网校展示专题方案，对应的报名培训列表筛选项将启用专题的行业、地区。</p>
                    <p></p>
                    <p>
                      3.开启专题下，支持按照网校的培训行业配置在网校报名培训列表报名专题的方案，订单上的来源显示为专题，不标记则统一显示网校名称。
                    </p>
                  </div>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="行业规则：" required>
                <div class="m-industry-rules">
                  <div class="item">
                    <div class="tit"><el-checkbox>人社行业</el-checkbox></div>
                    <div class="con">
                      <div class="tt"><i>*</i>订单来源标记专题：</div>
                      <div class="cc">
                        <el-checkbox label="">专业科目</el-checkbox>
                        <el-checkbox label="">公需科目</el-checkbox>
                      </div>
                    </div>
                  </div>
                  <div class="item">
                    <div class="tit"><el-checkbox>建设行业</el-checkbox></div>
                    <div class="con">
                      <div class="tt"><i>*</i>订单来源标记专题：</div>
                      <div class="cc">
                        <el-checkbox label="">统一标记</el-checkbox>
                        <el-checkbox label="">不标记</el-checkbox>
                      </div>
                    </div>
                  </div>
                  <div class="item">
                    <div class="tit"><el-checkbox>职业卫生行业</el-checkbox></div>
                    <div class="con">
                      <div class="tt"><i>*</i>订单来源标记专题：</div>
                      <div class="cc">
                        <el-checkbox label="">统一标记</el-checkbox>
                        <el-checkbox label="">不标记</el-checkbox>
                      </div>
                    </div>
                  </div>
                  <div class="item">
                    <div class="tit"><el-checkbox>工勤行业</el-checkbox></div>
                    <div class="con">
                      <div class="tt"><i>*</i>订单来源标记专题：</div>
                      <div class="cc">
                        <el-checkbox label="">统一标记</el-checkbox>
                        <el-checkbox label="">不标记</el-checkbox>
                      </div>
                    </div>
                  </div>
                  <div class="item">
                    <div class="tit"><el-checkbox>教师行业</el-checkbox></div>
                    <div class="con">
                      <div class="tt"><i>*</i>订单来源标记专题：</div>
                      <div class="cc">
                        <el-checkbox label="">统一标记</el-checkbox>
                        <el-checkbox label="">不标记</el-checkbox>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="网校展示入口：" required>
                <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
              </el-form-item>
              <el-form-item label="名称：">
                <el-input
                  v-model="input"
                  placeholder="请输入专题名称，例：专题培训"
                  maxlength="8"
                  show-word-limit
                ></el-input>
              </el-form-item>
              <el-form-item label="引导语：" required>
                <el-input
                  type="textarea"
                  placeholder="请输入专题的引导语，例：请选择地区/行业进行报名"
                  maxlength="50"
                  rows="3"
                  show-word-limit
                ></el-input>
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button>取消</el-button>
                <el-button type="primary">保存</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <el-button @click="dialog2 = true" type="primary" class="f-mr20 f-mb20">查看专题操作示例</el-button>
        <el-dialog title="专题操作示例" :visible.sync="dialog2" width="1060px" class="m-dialog-pic">
          <div class="f-tc"><img src="./assets/images/zt-load-pic.png" width="1160" alt=" " /></div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames: ['1'],
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleChange(val) {
        console.log(val)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
