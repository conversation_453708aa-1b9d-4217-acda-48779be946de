<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import EditElectronicSpecialInvoiceDialog from '@hbfe/jxjy-admin-trade/src/diff/qztg/invoice/collective/components/edit-electronic-special-invoice-dialog.vue'
  import ElectronicSpecialInvoice from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/electronic-special-invoice.vue'

  @Component({
    components: {
      EditElectronicSpecialInvoiceDialog
    }
  })
  export default class extends ElectronicSpecialInvoice {}
</script>
