/**
 * 评价统计数据
 * <AUTHOR>
 * @Date 2021/5/7/0007 10:09
 */
export class Appraise {
  average: number
  everyStartHit: EveryStartHit
}

/**
 * 每个星数命中次数
 * <AUTHOR>
 * @Date 2021/5/7/0007 10:10
 */
export class EveryStartHit {
  one: number
  two: number
  three: number
  four: number
  five: number

  /**
   * 获取星数总和
   */
  getTotal() {
    return this.one + this.two + this.three + this.four + this.five
  }

  /**
   * 获取星级数组，正序
   */
  getStarList() {
    return [this.one, this.two, this.three, this.four, this.five]
  }

  /**
   * 获取星级数组，倒序
   */
  getStarListDesc() {
    return [this.five, this.four, this.three, this.two, this.one]
  }
}
