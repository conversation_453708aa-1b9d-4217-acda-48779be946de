import { SubOrderRefundStatusEnum } from '@api/service/centre/trade/batch/order/enum/SubOrderRefundStatusList'
import RefundStatusInfoVo from '@api/service/centre/trade/batch/order/query/vo/RefundStatusInfoVo'
/**
 * @description 退款信息
 */
class RefundInfoVo {
  /**
   * 退款状态 1：退款审核中 2：退款中 3：退款成功  4：退款申请被拒绝 5：退款审核通过 6：取消退款
   */
  refundStatus: SubOrderRefundStatusEnum = null

  /**
   * 来源批次单退款状态
   */
  fromBatchRefundStatus: number = undefined

  /**
   * 退货信息
   */
  refundStatusInfoList: RefundStatusInfoVo[] = []
}

export default RefundInfoVo
