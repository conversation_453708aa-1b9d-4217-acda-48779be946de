import UserPlayCourse from '@api/service/customer/learning/course/UserPlayCourse'
import { PlayCourseEventTypeEnum } from '@api/service/customer/learning/LearningEventEnum'
import { bind, debounce } from 'lodash-decorators'

/**
 * 试听课程
 */
class UserTrialViewCourse extends UserPlayCourse {
  async init(coursePlayToken: string, withoutVerify = false) {
    await super.init(coursePlayToken, withoutVerify)
    await this.changeCurrentPlayMedia({ chapterIndex: 0, coursewareIndex: 0 })
  }

  @bind
  @debounce(200)
  async changeCurrentPlayMedia(event: { chapterIndex: number; coursewareIndex: number }): Promise<boolean> {
    const chapter = this.courseDetail.chapters[event.chapterIndex]
    const courseware = chapter.coursewares[event.coursewareIndex]
    this.currentPlayIndex.chapterIndex = event.chapterIndex
    this.currentPlayIndex.coursewareIndex = event.coursewareIndex
    this.emit(PlayCourseEventTypeEnum.beforeChangePlayCourseware)
    if (!courseware.isAllowAudition) {
      return this.emit(PlayCourseEventTypeEnum.trialViewIsNotSupport)
    }
    return super.changeCurrentPlayMedia(event)
  }
}

export default UserTrialViewCourse
