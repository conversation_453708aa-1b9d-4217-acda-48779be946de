import SchoolServiceIdStrategy from '@api/service/common/diffSchool/SchoolServiceIdStrategy'
import mergeRoutes from '@hbfe/jxjy-customer-common/src/util/mergeRoutes'
import SchoolEnum from '@api/service/common/diffSchool/enums/SchoolEnum'
import SpecialTopicStrategy from '@api/service/common/diffSchool/SpecialTopicStrategy'
async function getDiffRoute(module: string, isTopic = false, isDistribution = false) {
  let school = SchoolServiceIdStrategy.currentSchool()
  // 当前所处差异化专题
  const specialTopic = SpecialTopicStrategy.getCurrentSpecialTopic()

  const diffDistribution = 'xxxx'

  if (school && school === SchoolEnum.ANHUI) {
    // 针对安徽文件夹地址进行适配，后端标识为 ahzj 前端为 anhui 不一致，需要转为同一表示
    school = 'ANHUI'
  }
  // 不存在差异化网校直接返回null
  if (!school && !specialTopic) {
    return null
  }

  try {
    if (school) {
      if (isTopic) {
        // case 1 获取差异化网校下差异化专题路由
        return await import(
          `@packages/${module}/src/diff/${school.toLowerCase()}/special-topic/${specialTopic}/index`
        ).then((module) => {
          return module.default().router
        })
      } else if (isDistribution) {
        // case 1 获取差异化网校下差异化专题路由
        return await import(
          `@packages/${module}/src/diff/${school.toLowerCase()}/spacial-diff-distribution/${diffDistribution}/index`
        ).then((module) => {
          return module.default().router
        })
      } else {
        // case 2 获取差异化网校下的路由
        return await import(`@packages/${module}/src/diff/${school.toLowerCase()}/index`).then((module) => {
          return module.default().router
        })
      }
    } else {
      if (isTopic) {
        // case 3 获取通用网校下差异化专题路由
        return await import(`@packages/${module}/src/diff/common-special/${specialTopic}/index`).then((module) => {
          return module.default().router
        })
      } else if (isDistribution) {
        // case 1 获取差异化网校下差异化专题路由
        return await import(`@packages/${module}/src/diff/common-diff-distribution/${diffDistribution}/index`).then(
          (module) => {
            return module.default().router
          }
        )
      } else {
        // case 4 不存在差异化网校、专题 直接返回 null
        return null
      }
    }
  } catch (e) {
    // console.warn(e, `加载${module}模块下${school}差异化路由失败`)
    return null
  }
}
export async function updateRouter(route: any, module: string) {
  const school = SchoolServiceIdStrategy.currentSchool()
  const diffRoute = await getDiffRoute(module)
  let resultRoute = route
  if (module === 'distribution') {
    const diffDistribution: any = getDiffRoute(module, false, false)
    if (school) {
      if (diffRoute && diffRoute.length) {
        // case 1 无差异化专题 返回差异化网校路由
        resultRoute = mergeRoutes(resultRoute, diffRoute)
      }
      if (diffDistribution && diffDistribution.length) {
        // case 2 有差异化专题且专题路由存在且差异化网校路由存在，先合并差异化路由，在合并专题差异化路由。
        resultRoute = mergeRoutes(resultRoute, diffDistribution)
      }
    } else {
      if (diffDistribution && diffDistribution.length) {
        // case 3 有差异化专题且专题路由存在，但不存在差异化网校路由，直接用通用路由与差异化专题路由进行合并
        resultRoute = mergeRoutes(resultRoute, diffDistribution)
      }
    }
  } else {
    // 差异化专题路由
    const diffTopic = await getDiffRoute(module, true)
    if (school) {
      if (diffRoute && diffRoute.length) {
        // case 1 无差异化专题 返回差异化网校路由
        resultRoute = mergeRoutes(resultRoute, diffRoute)
      }
      if (diffTopic && diffTopic.length) {
        // case 2 有差异化专题且专题路由存在且差异化网校路由存在，先合并差异化路由，在合并专题差异化路由。
        resultRoute = mergeRoutes(resultRoute, diffTopic)
      }
    } else {
      if (diffTopic && diffTopic.length) {
        // case 3 有差异化专题且专题路由存在，但不存在差异化网校路由，直接用通用路由与差异化专题路由进行合并
        resultRoute = mergeRoutes(resultRoute, diffTopic)
      }
    }
  }
}
