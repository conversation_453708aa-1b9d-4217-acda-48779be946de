<template>
  <el-main>
    <el-drawer
      :title="dialogTitle"
      :visible.sync="isShow"
      :wrapperClosable="false"
      :size="dialogWidth"
      :before-close="isShowDialog"
      custom-class="m-drawer"
    >
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert">
          {{ titleMsg }}
        </el-alert>
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form
              ref="formRef"
              :rules="rules"
              :model="mutationCreateReturnOrderObj"
              label-width="auto"
              class="m-form f-mt20"
            >
              <el-form-item label="退货/款物品" v-if="refundType === 'single'" prop="refundedGoods">
                <div class="m-refund-good-group">
                  <el-checkbox-group
                    v-model="mutationCreateReturnOrderObj.refundedGoods"
                    @change="handleRefundTypeChange"
                  >
                    <el-checkbox class="m-refund-good" label="2" :disabled="hasSpecificValues(['2', '3'])">
                      <div class="tit f-to"><i class="el-icon-s-goods"></i> 2025公需课</div>
                      <div class="tag">
                        <el-tag type="info" size="small">选课规则</el-tag>
                        <el-tag type="info" size="small">2025年</el-tag>
                        <el-tag type="info" size="small">人社行业</el-tag>
                        <el-tag type="info" size="small">福建省</el-tag>
                        <el-tag type="info" size="small">公需科目</el-tag>
                        <el-tag type="info" size="small">30学时</el-tag>
                      </div>
                      <div class="bottom" v-if="hasSpecificValues(['2', '3'])">
                        <div class="status">已退货</div>
                      </div>
                    </el-checkbox>
                    <el-checkbox class="m-refund-good f-mt10" label="1" :disabled="hasSpecificValues(['1', '3'])">
                      <div class="tit f-to"><i class="el-icon-s-goods"></i>华医网2025年专业课</div>
                      <div class="tag">
                        <el-tag type="info" size="small">合作办学</el-tag>
                        <el-tag type="info" size="small">2025年</el-tag>
                        <el-tag type="info" size="small">人社行业</el-tag>
                        <el-tag type="info" size="small">专业科目</el-tag>
                        <el-tag type="info" size="small">60学时</el-tag>
                      </div>
                      <div class="bottom" v-if="hasSpecificValues(['1', '3'])">
                        <div class="status">已退货</div>
                      </div>
                    </el-checkbox>
                  </el-checkbox-group>
                  <div class="total">
                    合计实付金额<span class="f-cr">{{ realMoney }}</span
                    >元
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="退货/款类型" class="is-text" prop="refundType">
                <refund-category
                  ref="refundCategoryRef"
                  v-model="mutationCreateReturnOrderObj.refundType"
                  placeholder="请选择退货/款类型"
                  :typesof="typesof"
                />
              </el-form-item>
              <el-form-item
                label="退款金额"
                v-if="
                  refundType === 'single' && !isHasMoney && realMoney > 0 && mutationCreateReturnOrderObj.refundType
                "
                prop="refundAmount"
              >
                <el-input
                  v-model="mutationCreateReturnOrderObj.refundAmount"
                  min="0"
                  clearable
                  :placeholder="placeholderText"
                  :disabled="isDisabled"
                />
              </el-form-item>

              <el-form-item label="退款原因" prop="reasonId">
                <el-select v-model="mutationCreateReturnOrderObj.reasonId" clearable placeholder="请选择退款原因">
                  <el-option
                    v-for="itm in refundReasonList"
                    :key="itm.reasonId"
                    :label="itm.reasonContent"
                    :value="itm.reasonId"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="退款说明" prop="description">
                <el-input
                  type="textarea"
                  :rows="6"
                  maxlength="500"
                  v-model="mutationCreateReturnOrderObj.description"
                  placeholder="请输入退款说明"
                />
              </el-form-item>
              <el-form-item label="退货/款订单" v-if="refundType === 'batch'">
                {{ batchItemized
                }}<el-button type="text" @click="seeMore" v-if="isShowItemized">{{
                  seeMoreDialog ? '收起明细' : '查看明细'
                }}</el-button>
                <el-button type="primary" size="small" class="f-fr" v-if="seeMoreDialog" @click="setRefundMoney"
                  >批量设置退款金额</el-button
                >
              </el-form-item>
              <div class="bd" v-if="refundType === 'batch' && seeMoreDialog">
                <el-table stripe :data="tableData" class="m-table">
                  <el-table-column type="selection" width="60" align="center"></el-table-column>
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="购买人" min-width="120">
                    <template>
                      <p>姓名：AAA</p>
                      <p>手机：13023801427</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="购买物品" min-width="120">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">2025年公需课</div>
                      <div v-else>2025年公需课</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="数量" min-width="80">
                    <template>1</template>
                  </el-table-column>
                  <el-table-column label="实付金额（元）" min-width="120">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">500</div>
                      <div v-else>500</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="子订单当前退货/款状态" min-width="140">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">已部分退货，未退货</div>
                      <div v-else>-</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="本次退款金额（元）"
                    v-if="!isHasMoney"
                    width="260"
                    align="center"
                    fixed="right"
                  >
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-input placeholder="退款金额上限¥500，可自行修改" style="width: 232px"></el-input>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <span class="f-co">当前订单状态不支持退货且退款</span>
                      </div>
                      <div v-else>
                        <span class="f-co">未选中，本次退货/款不含当前订单</span>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <el-form-item class="m-btn-bar">
                <el-button @click="isShowDialog">取消</el-button>
                <el-button type="primary" @click="submit" :loading="loading">确认退款</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
    <el-dialog title="批量设置退款金额" :visible.sync="refundMoneyDialog" width="360px" class="m-dialog">
      <el-radio-group v-model="setMoneyType">
        <div>
          <el-radio :label="0">批量设置为可退上限</el-radio>
        </div>
        <div class="f-mt10">
          <el-radio :label="1"
            >批量设置为<el-input placeholder="请输入退款金额" v-model="setBatchMoney" class="f-ml10 u-w180"></el-input
          ></el-radio>
        </div>
      </el-radio-group>
      <div slot="footer">
        <el-button @click="refundMoneyDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchMoney">确认提交</el-button>
      </div>
    </el-dialog>
  </el-main>
</template>

<script lang="ts">
  import { Form } from 'element-ui'
  import { Component, Vue, Ref, Prop, Watch } from 'vue-property-decorator'
  import QueryRefundReasonList from '@api/service/management/trade/single/order/query/QueryRefundReasonList'
  import MutationCreateReturnOrder from '@api/service/diff/management/zztt/trade/order/mutation/MutationCreateReturnOrder'
  import {
    ReturnReasonInfoResponse,
    SubOrderResponse,
    SchemeResourceResponse
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { OrderRefundTypeEnum } from '@api/service/common/return-order/enums/OrderRefundType'
  import OrderDetailVo from '@api/service/management/trade/single/order/query/vo/OrderDetailVo'
  import RefundCategory from '@hbfe/jxjy-admin-trade/src/diff/zztt/order/components/refund-category.vue'
  import QueryRefundDetailDiff from '@api/service/diff/management/zztt/trade/order/query/SingleQueryRefundDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import UserOrderDetailVo from '@api/service/management/trade/single/order/query/vo/UserOrderDetailVo'

  @Component({
    components: {
      RefundCategory
    }
  })
  export default class extends Vue {
    @Ref('formRef') formRef!: Form // 表单ref
    @Ref('refundCategoryRef') refundCategoryRef: RefundCategory //退货款类型Ref
    @Prop({ default: '', type: String }) selectClassRuleName: string //培训方案名称名称
    @Prop({ type: Object }) orderDetail: OrderDetailVo | UserOrderDetailVo //订单详情

    isShow = false //打开抽屉弹窗

    seeMoreDialog = false //查看更多

    loading = false //loading标识

    refundMoneyDialog = false

    total = 1 //可退货/款的订单总数

    refundType = '' //退款类型

    setMoneyType = 0 //批量设置退款金额类型

    setBatchMoney = 0 //批量设置退款金额

    tableData = [{}] //列表返回数据

    totalOrders = 3 //退货/款订单总数

    totalOrderNum = 123 //退货/款订单总数

    maxMoney = 0 //退款金额上限 TODO

    subOrderResponse = new SubOrderResponse() //退货/款物品

    OrderRefundTypeEnum = OrderRefundTypeEnum //退货/款类型枚举

    refundReasonList = new Array<ReturnReasonInfoResponse>() // 退款原因

    mutationCreateReturnOrderObj = new MutationCreateReturnOrder() // 创建退货单对象

    typesof = 4

    queryRefundOrderDetail = new QueryRefundDetailDiff()

    courseType: Array<string> = []

    //是否 分销登录
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)

    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable

    returnStatus = 0
    /**
     * 弹窗标题
     */
    get dialogTitle() {
      return this.refundType === 'single' ? '发起退货/款' : '发起批量退货/款'
    }
    /**
     * 弹窗宽度
     */
    get dialogWidth() {
      return this.refundType === 'single' ? '640px' : '1200px'
    }
    /**
     * 弹窗提示语
     */
    get titleMsg() {
      return this.refundType === 'single'
        ? '请根据实际情况选择退货/款类型，请仔细确认！'
        : `当前集体报名批次内剩余可退货/款的订单共 ${this.total}笔，请根据实际情况选择退货/款类型！`
    }
    /**
     * 商品实付金额
     */
    get realMoney() {
      this.maxMoney =
        this.orderDetail.subOrderItems && this.orderDetail.subOrderItems.length
          ? this.orderDetail.subOrderItems[0].leftAmount
          : 0
      // 检查 orderDetail 和 basicData 是否存在，避免读取未定义属性
      if (this.orderDetail && (this.orderDetail as UserOrderDetailVo).basicData) {
        // 如果 orderDetail.basicData.amount 存在，使用它作为实付金额 交易管理的模型
        return (this.orderDetail as UserOrderDetailVo).basicData.amount || 0
      } else if ((this.orderDetail as OrderDetailVo).payAmount) {
        // 否则使用 orderDetail.payAmount 作为实付金额 业务咨询这边的模型
        return (this.orderDetail as OrderDetailVo).payAmount || 0
      }
      // 如果 orderDetail 或 basicData 不存在，返回 0
      return 0
    }
    /**
     * 总学时
     */
    get totalPeriod() {
      const { subOrderItems } = this.orderDetail
      if (subOrderItems && subOrderItems.length) {
        return subOrderItems.reduce((pre, cur) => {
          return pre + (cur.currentCommoditySku.resource as SchemeResourceResponse).period
        }, 0)
      }
      return '--'
    }
    /**
     * 退款金额提示语
     */
    get placeholderText() {
      return `退款金额上限¥${this.maxMoney}，可自行修改`
    }
    /**
     * 判断是否选择无金额的退款
     */
    get isHasMoney() {
      return (
        this.mutationCreateReturnOrderObj.refundType === OrderRefundTypeEnum.returnOnly ||
        this.mutationCreateReturnOrderObj.refundType === OrderRefundTypeEnum.partialReturnNoRefund
      )
    }

    /**
     * 是否显示明细
     */
    get isShowItemized() {
      if (this.mutationCreateReturnOrderObj.refundType === OrderRefundTypeEnum.returnAndRefund) {
        return false
      }
      return true
    }
    /**
     * 判断是否需要退款
     *
     * 根据实际金额判断是否需要进行退款操作如果实际金额大于0，则表明需要退款
     *
     * @returns {boolean} 返回一个布尔值，表示是否需要退款true代表需要退款，false代表不需要退款
     */
    get isNeedRefundMoney() {
      return this.realMoney > 0
    }
    /**
     * 批量提示语说明
     */
    get batchItemized() {
      const orderCountMessage = `当前退货/款订单 共 ${this.totalOrders} 笔`
      if (!this.isShowItemized) {
        return ''
      } else {
        if (this.isHasMoney) {
          return `${orderCountMessage}，不退款。`
        } else {
          return `${orderCountMessage}，累计退款金额 共 ${this.totalOrderNum} 元。`
        }
      }
    }

    /**
     * 判断“退货/款类型”选中「退货且退款」
     */
    get isMoney() {
      return this.mutationCreateReturnOrderObj.refundType === OrderRefundTypeEnum.returnAndRefund
    }

    rules = {
      refundedGoods: [{ required: true, message: '请选择退货/款物品', trigger: 'blur' }],
      reasonId: [{ required: true, message: '请选择退款原因', trigger: ['blur', 'change'] }],
      description: [{ required: true, message: '请填写退款说明', trigger: ['blur', 'change'] }],
      refundType: [
        {
          required: true,
          message: '请选择退货/款类型',
          trigger: ['blur', 'change']
        }
      ],
      refundAmount: [{ required: true, validator: this.validateMoney, trigger: 'blur' }]
    }

    /**
     * 金额输入框是否禁用
     */
    get isDisabled() {
      if (this.mutationCreateReturnOrderObj.refundType === OrderRefundTypeEnum.returnAndRefund) {
        this.$nextTick(() => {
          this.formRef.clearValidate(['refundAmount']) //手动触发
        })
        return true
      } else {
        return false
      }
    }

    @Watch('mutationCreateReturnOrderObj.refundType', {
      immediate: true,
      deep: true
    })
    reasonChange(val: OrderRefundTypeEnum) {
      if (val === OrderRefundTypeEnum.returnAndRefund) {
        this.mutationCreateReturnOrderObj.refundAmount = this.realMoney
      } else if (val === OrderRefundTypeEnum.returnOnly) {
        this.mutationCreateReturnOrderObj.refundAmount = 0
      }
    }
    /**
     * 验证单词发起退款金额效验 TODO
     */
    validateMoney(rule: object, value: number, callback: (error?: Error) => void) {
      if (!value) {
        return callback(new Error('请填写退款金额'))
      }
      if ((value <= 0 && typeof value !== 'number') || isNaN(value)) {
        return callback(new Error('请填写正确的金额！'))
      }
      if (value > this.maxMoney) {
        return callback(new Error(`当前填写的退款金额超出上限 ￥ ${this.maxMoney}`))
      }
      // 检查小数点后是否超过两位
      if (!/^\d+(\.\d{1,2})?$/.test(value.toString())) {
        return callback(new Error('退款金额最多保留两位小数，请调整后重新提交'))
      }
      // 新增逻辑：如果退款金额等于实付总金额，提示用户选择全额退款
      if (
        this.maxMoney == this.realMoney &&
        value == this.maxMoney &&
        ![
          OrderRefundTypeEnum.refundOnly,
          OrderRefundTypeEnum.returnAndRefund,
          OrderRefundTypeEnum.partialReturnFullRefund
        ].includes(this.mutationCreateReturnOrderObj.refundType)
      ) {
        return callback(new Error('当前填写的退款金额等于实付总金额，请选择全额退款'))
      }
      // 部分退货 并且金额填写等于子订单所有物品的实付金额合计
      if (
        this.mutationCreateReturnOrderObj.refundType === OrderRefundTypeEnum.partialReturnPartialRefund &&
        value == this.realMoney
      ) {
        return callback(new Error(`当前填写的退款金额等于实付总金额，请选择全额退款`))
      }

      callback()
    }
    /**
     * 退款商品sku属性转换 获取数组  方便展示
     */
    skuProperties(item: SubOrderResponse) {
      if (!item || !item.currentCommoditySku || !item.currentCommoditySku.skuProperty) {
        return []
      }

      const skuProperty = item.currentCommoditySku.skuProperty

      // 辅助函数：安全获取属性值
      const safeGetProperty = (property: any, defaultValue = '-') => {
        return (
          property?.skuPropertyValueName ||
          property?.skuPropertyValueId ||
          property?.skuPropertyValueShowName ||
          defaultValue
        )
      }
      return [
        {
          condition: skuProperty.year,
          value: `${safeGetProperty(skuProperty.year)}年`
        },
        {
          condition: skuProperty.industry,
          value: safeGetProperty(skuProperty.industry)
        },
        {
          condition: skuProperty.subjectType,
          value: safeGetProperty(skuProperty.subjectType)
        },
        {
          condition:
            this.setDistrictSkuName(skuProperty.province.skuPropertyValueName, 1) +
            this.setDistrictSkuName(skuProperty.city.skuPropertyValueName, 2) +
            this.setDistrictSkuName(skuProperty.county.skuPropertyValueName, 3),
          value:
            this.setDistrictSkuName(skuProperty.province.skuPropertyValueName, 1) +
            this.setDistrictSkuName(skuProperty.city.skuPropertyValueName, 2) +
            this.setDistrictSkuName(skuProperty.county.skuPropertyValueName, 3)
        },
        {
          condition: skuProperty.trainingProfessional,
          value: safeGetProperty(skuProperty.trainingProfessional) || '-'
        },
        {
          condition: this.totalPeriod,
          value: `${this.totalPeriod}学时`
        }
      ].filter((entry) => entry.condition !== undefined && entry.condition !== null)
    }
    /**
     *
     * @param name 地区名称
     * @param type 区域类型：1:省/2:市/3:区
     * @returns str 返回名称
     */
    setDistrictSkuName(name: string, type: number) {
      let str = ''
      if (!name) return str
      if (type === 1) {
        str = name
      } else {
        str = '>' + name
      }

      return str
    }
    /**
     * 查看更多
     */
    seeMore() {
      this.seeMoreDialog = !this.seeMoreDialog
    }
    /**
     *  退货/款联动效果
     * @description: 退货/款物品 change事件
     */
    handleRefundTypeChange() {
      // 调整“退货/款物品”时，该选项重置为未选中
      this.mutationCreateReturnOrderObj.refundType = undefined
      if (this.refundCategoryRef) {
        this.refundCategoryRef.handleClear()
      }
      const refundItemLength = this.mutationCreateReturnOrderObj.refundedGoods.length
      const realMoney = this.realMoney
      if (refundItemLength === 1 && realMoney > 0) {
        //实付金额大于0时，“退货/款物品”的培训方案选中一个 部分退货且部分退款
        this.typesof = 1
      } else if (refundItemLength === 2 && realMoney > 0) {
        //实付金额大于0时，“退货/款物品”的培训方案全部选中时 退货且退款
        this.typesof = 2
      } else if (realMoney === 0) {
        //实付金额等于0时，“退货/款物品”的培训方案全部选中时 仅退货（不退款）
        this.typesof = 3
      } else if (this.mutationCreateReturnOrderObj.refundedGoods.length === 0) {
        this.typesof = 4
      } else {
        this.typesof = 5
      }
    }
    /**
     * 批量设置金额
     */
    async isShowDialog(type: string, item?: SubOrderResponse) {
      this.formRef && this.formRef.clearValidate()
      this.mutationCreateReturnOrderObj = new MutationCreateReturnOrder()
      this.refundType = type
      this.subOrderResponse = item
      // this.maxMoney = this.realMoney
      this.isShow = !this.isShow
      console.log(item, 'item')
      if (item) {
        this.mutationCreateReturnOrderObj.orderNo = item.orderNo //订单号
        this.mutationCreateReturnOrderObj.subOrderNo = item.subOrderNo //子订单号
        this.returnStatus = item.returnStatus
        this.doQueryPage()
      }
    }

    /**
     * 异步执行查询退款单列表
     *
     */
    async doQueryPage() {
      let res
      if (this.isFxlogin && this.isHadFxAbility) {
        res = await this.queryRefundOrderDetail.queryFxRefundOrderList(this.mutationCreateReturnOrderObj.subOrderNo)
      } else {
        res = await this.queryRefundOrderDetail.queryRefundOrderList(this.mutationCreateReturnOrderObj.subOrderNo)
      }
      console.log(res, 'res')
      if (res && res.length) {
        this.courseType = res.map((item) => {
          return item.courseType ? item.courseType : ''
        })
        if (!this.hasSpecificValues(['2', '3'])) {
          this.mutationCreateReturnOrderObj.refundedGoods.push('2')
        }
        if (!this.hasSpecificValues(['1', '3'])) {
          this.mutationCreateReturnOrderObj.refundedGoods.push('1')
        }
      } else {
        this.mutationCreateReturnOrderObj.refundedGoods = ['1', '2']
      }
      this.handleRefundTypeChange()
    }

    /**
     * 打开批量设置金额
     */
    setRefundMoney() {
      this.setMoneyType = 0
      this.setBatchMoney = 0
      this.refundMoneyDialog = true
    }
    /**
     * 批量设置金额
     */
    confirmBatchMoney() {
      if (this.setMoneyType === 1) {
        if (this.setBatchMoney === 0) {
          this.$message.error('请输入退款金额')
          return
        } else {
          this.$message.success('设置成功')
        }
      }
      //TODO 批量设置金额
      this.refundMoneyDialog = false
    }
    /**
     * 确认退款
     */
    async submit() {
      if (Number(this.mutationCreateReturnOrderObj.refundAmount) < 0.01) {
        return this.$message.warning('请输入正确的退款金额')
      }
      // 初始化变量
      let isLearningComplete = false

      // 表单验证
      this.formRef.validate(async (valid: boolean) => {
        if (!valid) return
        console.log(this.mutationCreateReturnOrderObj)
        console.log(this.realMoney, '实付金额')
        console.log(this.courseType, 'courseType')
        if ([1, 2, 4].includes(this.returnStatus)) {
          return this.$message.warning('订单状态发生变更，请刷新页面重新提交！')
        }
        // 退货/款类型是否为退货且退款 金额是否等于子订单实付金额
        if (
          this.mutationCreateReturnOrderObj.refundType === OrderRefundTypeEnum.returnAndRefund &&
          Number(this.mutationCreateReturnOrderObj.refundAmount) !== Number(this.realMoney)
        ) {
          return this.$message.warning(
            '您选择的操作为全部退货且全额退款，当前退款金额有误，请将退款金额调整为全额，以便继续完成流程。'
          )
        }
        // 判断当前子订单之前是否已退款退货其中一门培训方案
        if (
          this.hasSpecificValues(['1', '2', '3']) &&
          Number(this.mutationCreateReturnOrderObj.refundAmount) !== Number(this.maxMoney)
        ) {
          return this.$message.warning(
            '该订单已完成其中单个科目的退货退款，本次需一次性完成剩余课程的退货退款。请在退款金额输入框内输入全部剩余款项。'
          )
        }
        // amountSource 0 用户输入 1 系统计算 2 剩余全部
        if (this.mutationCreateReturnOrderObj.refundedGoods.length == 1) {
          // 已经被退款一次了
          if (this.hasSpecificValues(['1', '2', '3'])) {
            this.mutationCreateReturnOrderObj.amountSource = 2
          } else {
            this.mutationCreateReturnOrderObj.amountSource = 0
          }
        }
        if (this.mutationCreateReturnOrderObj.refundedGoods.length == 2) {
          this.mutationCreateReturnOrderObj.amountSource = 2
        }
        this.loading = true
        try {
          // 获取强制状态
          await this.orderDetail.getForceStatue()

          // 检查学习进度是否达到100%
          if (this.orderDetail.foreseReasonArr.includes(1)) {
            if (!(await this.confirmAction('该培训班的学习进度已达到100%，是否强制退货/款'))) {
              return
            }
          }

          // 检查是否已开票
          if (this.orderDetail.foreseReasonArr.includes(2)) {
            if (!(await this.confirmAction('此订单已开票，是否强制退款'))) {
              return
            }
          }
          // 执行退款逻辑
          const refundResult = await this.refund()
          console.log(refundResult, 'refundResult')
          if (refundResult && refundResult.isSuccess()) {
            this.$message.success('退款申请提交成功')
            this.$emit('refresh')
            this.isShowDialog('single')
          } else {
            const message = (refundResult.errors && refundResult.errors[0].message) || '未知错误'
            this.$message.error(`退款失败:${message}`)
          }
        } catch (e) {
          // 异常处理
          console.error(e, '退款失败')
          let errorMessage = '退款失败'
          if (e instanceof Error) {
            errorMessage += `: ${e.message}`
          } else {
            errorMessage += `: ${String(e)}`
          }
          this.$message.error(errorMessage)
        } finally {
          this.loading = false
        }
      })
    }

    /**
     * 确认用户操作
     * @param {string} message - 提示信息
     * @returns {Promise<boolean>} - 用户是否确认
     */
    async confirmAction(message: string): Promise<boolean> {
      try {
        // 调用 $confirm 并捕获返回值
        await this.$confirm(message, '系统提醒', {
          confirmButtonText: '继续',
          showClose: false,
          closeOnClickModal: false,
          dangerouslyUseHTMLString: true // 如果 message 包含 HTML，需启用此选项
        })
        return true // 用户点击了“继续”
      } catch (e) {
        return false // 用户点击了“取消”或关闭了对话框
      }
    }

    /**
     *  确认退款事件
     */
    async refund() {
      const res = await this.mutationCreateReturnOrderObj.agreeReturnApply()
      this.loading = false
      return res
    }

    /**
     * 检查给定的值数组中是否至少有一个元素在课程类型数组中
     * 此方法用于确定课程类型与给定值之间是否存在特定的交集
     *
     * @param {string[]} values - 一个字符串数组，包含需要检查的值
     * @returns {boolean} 如果课程类型数组中至少包含一个给定的值，则返回true，否则返回false
     */
    hasSpecificValues(values: string[]) {
      return values.some((value) => this.courseType.includes(value))
    }

    async created() {
      // 退款原因
      this.refundReasonList = await QueryRefundReasonList.queryRefundReasonList()
    }
  }
</script>
