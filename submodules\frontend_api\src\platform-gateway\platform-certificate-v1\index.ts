import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-certificate-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-certificate-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum StudentSchemeLearningSortField {
  REGISTER_TIME = 'REGISTER_TIME',
  SCHEME_YEAR = 'SCHEME_YEAR'
}

// 类

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

export class StudentSchemeLearningRequest {
  studentNoList?: Array<string>
  student?: UserRequest
  learningRegister?: LearningRegisterRequest
  scheme?: SchemeRequest
  studentLearning?: StudentLearningRequest
  dataAnalysis?: DataAnalysisRequest
  connectManageSystem?: ConnectManageSystemRequest
  extendedInfo?: ExtendedInfoRequest
  openPrintTemplate?: boolean
  saleChannels?: Array<number>
  trainingChannelName?: string
  trainingChannelId?: string
  notDistributionPortal?: boolean
  trainingType?: string
  issueId?: string
}

export class StudentSchemeLearningSortRequest {
  field?: StudentSchemeLearningSortField
  policy?: SortPolicy
}

export class ConnectManageSystemRequest {
  syncStatus?: number
}

export class DataAnalysisRequest {
  trainingResultPeriod?: DoubleScopeRequest
  requirePeriod?: DoubleScopeRequest
  acquiredPeriod?: DoubleScopeRequest
}

export class ExtendedInfoRequest {
  whetherToPrint?: boolean
  applyCompanyCode?: string
  policyTrainingSchemeId?: string
  policyTrainingSchemeName?: string
}

export class LearningRegisterRequest {
  registerType?: number
  sourceType?: string
  sourceId?: string
  status?: Array<number>
  registerTime?: DateScopeRequest
  saleChannels?: Array<number>
  orderNoList?: Array<string>
  subOrderNoList?: Array<string>
  batchOrderNoList?: Array<string>
  distributorId?: string
  portalId?: string
}

export class RegionRequest {
  province?: string
  city?: string
  county?: string
}

export class StudentLearningRequest {
  trainingResultList?: Array<number>
  trainingResultTime?: DateScopeRequest
  notLearningTypeList?: Array<number>
  courseScheduleStatus?: number
  examAssessResultList?: Array<number>
}

export class RegionSkuPropertyRequest {
  province?: string
  city?: string
  county?: string
}

export class RegionSkuPropertySearchRequest {
  region?: Array<RegionSkuPropertyRequest>
  regionSearchType?: number
}

export class SchemeRequest {
  schemeId?: string
  schemeIdList?: Array<string>
  schemeType?: string
  schemeName?: string
  skuProperty?: SchemeSkuPropertyRequest
}

export class SchemeSkuPropertyRequest {
  year?: Array<string>
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  industry?: Array<string>
  subjectType?: Array<string>
  trainingCategory?: Array<string>
  trainingProfessional?: Array<string>
  technicalGrade?: Array<string>
  positionCategory?: Array<string>
  trainingObject?: Array<string>
  jobLevel?: Array<string>
  jobCategory?: Array<string>
  subject?: Array<string>
  grade?: Array<string>
  learningPhase?: Array<string>
  discipline?: Array<string>
  qualificationCategory?: Array<string>
  trainingWay?: Array<string>
  trainingInstitution?: Array<string>
  mainAdditionalItem?: Array<string>
}

export class UserPropertyRequest {
  regionList?: Array<RegionRequest>
  companyName?: string
  payOrderRegionList?: Array<RegionRequest>
}

export class UserRequest {
  userIdList?: Array<string>
  accountIdList?: Array<string>
  userProperty?: UserPropertyRequest
}

/**
 * 批量打印证书请求
<AUTHOR>
@date 2022/12/5 16:56
 */
export class BatchPrintCertificatesRequest {
  /**
   * 文件类型 1-PDF   2-IMAGE
@see FileTypes
   */
  fileType: number
  /**
   * 文件打印方式 1-连贯打印 2-单个打印
   */
  printType: number
  /**
   * 是否合并打印（有连贯打印才有合并打印选项）
1-是，0-否
   */
  merge?: string
  /**
   * 打印模板来源  (打印模板是读取方案配置，还是读取学员端选择模板)
读方案 &#x3D; 1  读学员选择 &#x3D; 2
   */
  printSource: number
  /**
   * 打印端口
方案端 &#x3D; 1  学员端 &#x3D; 2
   */
  printPort: number
  /**
   * 服务商类型
1 网校
5 分销商
6 专题管理员
   */
  servicerType: number
}

/**
 * 批量打印证书请求
<AUTHOR>
@date 2022/12/15 21:01
 */
export class BatchPrintCertificatesSyncRequest {
  /**
   * 学号集合
   */
  studentNos?: Array<string>
  /**
   * 文件类型 1-PDF   2-IMAGE
@see FileTypes
   */
  fileType: number
}

/**
 * 证书打印请求
<AUTHOR>
 */
export class CertificatePrintRequest {
  /**
   * 参训资格id
   */
  qualificationId?: string
  /**
   * 学号
   */
  studentNo?: string
  /**
   * 文件类型 1-PDF   2-IMAGE
@see FileTypes
   */
  fileType: number
  /**
   * 扩展数据
key 对应courseId
   */
  data?: Array<Extend>
}

/**
 * 证书打印请求 - 选择模板
<AUTHOR>
@date 2023/11/07
 */
export class ChooseTemplateCertificatePrintRequest {
  /**
   * 参训资格id
   */
  qualificationId?: string
  /**
   * 学号
   */
  studentNo?: string
  /**
   * 证书模板id
   */
  templateId?: string
  /**
   * 文件类型 1-PDF   2-IMAGE
@see FileTypes
   */
  fileType: number
}

/**
 * 旧平台迁移数据打印请求
<AUTHOR>
@date 2024/1/31
 */
export class DataMigrationCertificatePrintRequest {
  /**
   * 历史学习数据id
   */
  learningDataId?: string
  /**
   * 文件类型 1-PDF  2-IMAGE
@see FileTypes
   */
  fileType: number
  /**
   * 扩展数据
   */
  data?: Array<Extend>
}

export class DataMigrationCertificatePrintWithOutLoginRequest {
  userId?: string
  /**
   * 历史学习数据id
   */
  learningDataId?: string
  /**
   * 文件类型 1-PDF  2-IMAGE
@see FileTypes
   */
  fileType: number
  /**
   * 扩展数据
   */
  data?: Array<Extend>
}

/**
 * 扩展数据
<AUTHOR>
@date 2024/1/31
 */
export class Extend {
  /**
   * 键
   */
  key?: string
  /**
   * 值
   */
  value?: string
}

/**
 * 获取证书快照请求
<AUTHOR>
 */
export class GetCertificateSnapshotRequest {
  /**
   * 证书id
   */
  certificateId?: string
  /**
   * 证书快照id
   */
  snapshotId?: string
}

/**
 * 导入名单批量打印
<AUTHOR>
@date 2024/4/11
 */
export class ImportListBatchPrintCertificatesRequest {
  /**
   * 证书模板id
   */
  templateId?: string
  /**
   * 文件类型 1-PDF   2-IMAGE
@see FileTypes
   */
  fileType: number
  /**
   * 文件打印方式 1-连贯打印 2-单个打印
   */
  printType: number
  /**
   * 是否合并打印（有连贯打印才有合并打印选项）
1-是，0-否
   */
  merge?: string
  /**
   * 打印模板来源  (打印模板是读取方案配置，还是读取学员端选择模板)
读方案 &#x3D; 1  读学员选择 &#x3D; 2
   */
  printSource: number
  /**
   * 打印端口 - 导入名单打印为学员端
方案端 &#x3D; 1  学员端 &#x3D; 2
   */
  printPort: number
}

/**
 * 方案信息
<AUTHOR>
@date 2023/9/11
 */
export class SchemeMessage {
  /**
   * 学习方案id
   */
  schemeId?: string
  /**
   * 学习方案名字
   */
  schemeName?: string
}

/**
 * 根据学号批量打印
 */
export class StudentBatchPrintCertificatesRequest {
  /**
   * 证书模板id
   */
  templateId?: string
  /**
   * 文件类型 1-PDF   2-IMAGE
@see FileTypes
   */
  fileType: number
  /**
   * 文件打印方式 1-连贯打印 2-单个打印
   */
  printType: number
  /**
   * 是否合并打印（有连贯打印才有合并打印选项）
1-是，0-否
   */
  merge?: string
  /**
   * 打印模板来源  (打印模板是读取方案配置，还是读取学员端选择模板)
读方案 &#x3D; 1  读学员选择 &#x3D; 2
   */
  printSource: number
  /**
   * 打印端口
方案端 &#x3D; 1  学员端 &#x3D; 2
   */
  printPort: number
  /**
   * 服务商类型
1 网校
5 分销商
6 专题管理员
   */
  servicerType: number
}

/**
 * 学员端打印请求
<AUTHOR>
@date 2025/4/15 15:40
 */
export class StudentPrintCertificateRequest {
  /**
   * 参训资格id
   */
  qualificationId?: string
  /**
   * 学号
   */
  studentNo?: string
  /**
   * 文件类型 1-PDF   2-IMAGE
@see FileTypes
   */
  fileType: number
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 扩展数据
key 对应courseId
   */
  data?: Array<Extend>
}

/**
 * <AUTHOR>
@date 2024/5/23
 */
export class BatchPrintingRequest {
  /**
   * 姓名
   */
  name?: string
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 学习方案名称
   */
  schemeName?: string
  /**
   * 导入状态
1 &#x3D; 成功
2 &#x3D; 失败
   */
  importStatus: number
  /**
   * 服务商类型
1 网校
5 分销商
   */
  servicerType: number
}

/**
 * 证书打印导出失败数据请求
<AUTHOR>
@date 2024/7/9 11:12
 */
export class ExportCertificateFailedDataRequest {
  /**
   * 主任务id
   */
  mainTaskId: string
}

/**
 * 导出失败数据请求
<AUTHOR>
@date 2024/5/17
 */
export class ExportFailedDataRequest {
  /**
   * 姓名
   */
  name?: string
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 学习方案名称
   */
  schemeName?: string
  /**
   * 导入状态
1 &#x3D; 成功
2 &#x3D; 失败
   */
  importStatus: number
}

/**
 * 导入列表数据请求
<AUTHOR>
@date 2024/5/17
 */
export class ImportListDataRequest {
  /**
   * 起始页码
   */
  pageNo: number
  /**
   * 每页记录数
默认10
   */
  pageSize: number
  /**
   * 姓名
   */
  name?: string
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 学习方案名称
   */
  schemeName?: string
  /**
   * 导入状态
1 &#x3D; 成功
2 &#x3D; 失败
   */
  importStatus: number
}

/**
 * 导入名单校验请求
<AUTHOR>
@date 2024/05/13
 */
export class ImportListVerificationRequest {
  /**
   * 文件路径
   */
  filePath: string
  /**
   * 文件名字
   */
  fileName?: string
}

/**
 * 打印列表删除所有学员请求
<AUTHOR>
@date 2024/5/13
 */
export class PrintTheListRemoveAllStudentRequest {
  /**
   * 姓名
   */
  name?: string
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 学习方案名称
   */
  schemeName?: string
  /**
   * 导入状态
1 &#x3D; 成功列表
2 &#x3D; 失败列表
   */
  importStatus: number
}

/**
 * 打印列表删除学生请求
<AUTHOR>
@date 2024/5/13
 */
export class PrintTheListRemoveStudentRequest {
  /**
   * 导入学员打印表id
   */
  id: string
}

/**
 * 筛选批量打印证书请求
<AUTHOR>
@date 2023/8/29
 */
export class ScreeningBatchPrintCertificatesRequest {
  /**
   * 学习方案id
   */
  schemeMessages?: Array<SchemeMessage>
  /**
   * 是否筛选已导出证明
   */
  screening: boolean
  /**
   * 文件类型 1-PDF   2-IMAGE
@see FileTypes
   */
  fileType: number
  /**
   * 专题id
   */
  specialId?: string
}

/**
 * 批量任务信息
<AUTHOR>
@date 2024/5/17
 */
export class BatchTaskInformation {
  /**
   * 主任务状态
已创建 &#x3D; 0
已就绪 &#x3D; 1
执行中 &#x3D; 2
已完成 &#x3D; 3
   */
  taskStatus: number
  /**
   * 主任务状态
未处理 &#x3D; 0
成功 &#x3D; 1
失败 &#x3D; 2
就绪失败 &#x3D; 3
   */
  result: number
  /**
   * 子任务总数
   */
  SubtasksTotal: number
  /**
   * 子任务完成数
   */
  SubtasksFinishNumber: number
}

/**
 * 导入学员数据  ImportedSuccessData
<AUTHOR>
@date 2024/5/15
 */
export class ImportedData {
  /**
   * 导入学员打印表id
   */
  id: string
  /**
   * 姓名
   */
  name: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 学习方案名称
   */
  schemeName: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 导入状态
   */
  importStatus: number
  /**
   * 失败原因
   */
  reasonForFailure: string
}

/**
 * 上传成功失败数量
<AUTHOR>
@date 2024/5/21
 */
export class SuccessFailureQuantityRequest {
  /**
   * 成功条数
   */
  success: number
  /**
   * 失败条数
   */
  fail: number
}

/**
 * 打印前置校验返回值
<AUTHOR>
@date 2025/3/5 8:52
 */
export class CheckPrintConditionResponse {
  /**
   * code
200 成功
50001 成果数据未同步成功
   */
  code: string
  /**
   * message
   */
  message: string
  /**
   * 路径(单个打印)
   */
  path: string
  /**
   * 主任务id
   */
  taskId: string
  /**
   * 用户信息
   */
  userInfo: Array<UserInfo>
  /**
   * 是否需要强制完成调查问卷
   */
  needForceQuestionnaire: boolean
  /**
   * 需要强制完成的调查问卷信息
   */
  unaccomplishedQuestionnaire: Array<UnaccomplishedQuestionnaire>
}

export class UserInfo {
  /**
   * 姓名
   */
  userName: string
  /**
   * 身份证
   */
  idCard: string
  /**
   * 学号
   */
  studentNo: string
}

/**
 * 未完成问卷信息
<AUTHOR>
@date 2025/4/15 10:15
 */
export class UnaccomplishedQuestionnaire {
  /**
   * 未完成问卷id
   */
  unaccomplishedQuestionnaireId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 允许开始时间
   */
  allowStartTime: string
  /**
   * 允许结束时间
   */
  allowEndTime: string
}

export class ImportedDataPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ImportedData>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量导入学员名单
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchImportStudentList(
    request: ImportListVerificationRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchImportStudentList,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量打印证书sync（没用）
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchPrintCertificateSync(
    request: BatchPrintCertificatesSyncRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchPrintCertificateSync,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量打印证书（按方案打印）
   * @param request
   * @param studentSchemeLearningRequest
   * @param sort
   * @param dataFetchingEnvironment
   * @return zip压缩包路径
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchPrintCertificates(
    params: {
      request?: BatchPrintCertificatesRequest
      studentSchemeLearningRequest?: StudentSchemeLearningRequest
      sort?: Array<StudentSchemeLearningSortRequest>
    },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchPrintCertificates,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询导入打印名单上传进度
   * @param mainTaskId 主任务ID
   * @return {@link String}
   * @param mutate 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async checkImportPrintListTheUploadProgress(
    mainTaskId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.checkImportPrintListTheUploadProgress,
    operation?: string
  ): Promise<Response<BatchTaskInformation>> {
    return commonRequestApi<BatchTaskInformation>(
      SERVER_URL,
      {
        query: mutate,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 打印单个证书 - 选择模板
   * @param request
   * @return {@link String}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async chooseTemplatePrintCertificate(
    request: ChooseTemplateCertificatePrintRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.chooseTemplatePrintCertificate,
    operation?: string
  ): Promise<Response<CheckPrintConditionResponse>> {
    return commonRequestApi<CheckPrintConditionResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 集体报名管理员导出批量证明打印失败数据
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async collectivelyAdministratorExportCertificateFailedData(
    request: ExportCertificateFailedDataRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.collectivelyAdministratorExportCertificateFailedData,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 历史证明打印，无需登录，泉州提高需求，使用时需确认内部逻辑
   * @param request 请求体
   * @return 文件路径
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async dataMigrationCertificatePrintWithOutLogin(
    request: DataMigrationCertificatePrintWithOutLoginRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.dataMigrationCertificatePrintWithOutLogin,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 旧平台迁移数据打印
   * @param request
   * @return {@link String}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async dataMigrationPrintCertificate(
    request: DataMigrationCertificatePrintRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.dataMigrationPrintCertificate,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量删除试题
   * @param mutate 查询 graphql 语法文档
   * @param ids 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteQuestion(
    ids: Array<string>,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteQuestion,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { ids },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批量证明打印失败数据
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCertificateFailedData(
    request: ExportCertificateFailedDataRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.exportCertificateFailedData,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出失败数据
   * @param request
   * @return {@link String}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportFailedData(
    request: ExportFailedDataRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.exportFailedData,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查看导入任务数据
   * @return {@link String}
   * @param mutate 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findImportPrintData(
    mainTaskId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.findImportPrintData,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查看导入任务失败数据
   * @return {@link String}
   * @param mutate 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findImportPrintFailData(
    mainTaskId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.findImportPrintFailData,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 上传成功失败数量
   * @return {@link String}
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findUploadSuccessFailureQuantity(
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.findUploadSuccessFailureQuantity,
    operation?: string
  ): Promise<Response<SuccessFailureQuantityRequest>> {
    return commonRequestApi<SuccessFailureQuantityRequest>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取证书快照
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCertificateSnapshot(
    request: GetCertificateSnapshotRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.getCertificateSnapshot,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 集体报名打印单个证书  文件名:名字+证件号
   * <p>已弃用，统一换成 printCertificate 接口</p>
   * @param request
   * @return {@link String}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async groupRegistrationPrintCertificate(
    request: CertificatePrintRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.groupRegistrationPrintCertificate,
    operation?: string
  ): Promise<Response<CheckPrintConditionResponse>> {
    return commonRequestApi<CheckPrintConditionResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导入名单打印
   * @param request
   * @return {@link String}
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async learnerImportBatchPrintCertificates(
    params: { batchPrintingRequest?: BatchPrintingRequest; request?: ImportListBatchPrintCertificatesRequest },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.learnerImportBatchPrintCertificates,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 单个打印证书
   * @param request 请求体
   * @return 证书路径
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async printCertificate(
    request: CertificatePrintRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.printCertificate,
    operation?: string
  ): Promise<Response<CheckPrintConditionResponse>> {
    return commonRequestApi<CheckPrintConditionResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 打印名单列表移除所有学员
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async printTheListRemoveAllStudent(
    request: PrintTheListRemoveAllStudentRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.printTheListRemoveAllStudent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 打印名单列表移除单个学员
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async printTheListRemoveStudent(
    request: PrintTheListRemoveStudentRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.printTheListRemoveStudent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 返回导入名单信息
   * @param request
   * @return {@link ImportedData}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async returnImportedData(
    request: ImportListDataRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.returnImportedData,
    operation?: string
  ): Promise<Response<ImportedDataPage>> {
    return commonRequestApi<ImportedDataPage>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 筛选批量打印证书（集体管理员报名）
   * @param request 请求
   * @return zip压缩包路径
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async screeningBatchPrintCertificates(
    request: ScreeningBatchPrintCertificatesRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.screeningBatchPrintCertificates,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量打印证书 学号（按学员打印）
   * @param request
   * @param studentSchemeLearningRequest
   * @param sort
   * @param dataFetchingEnvironment
   * @return zip压缩包路径
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async studentBatchPrintCertificates(
    params: {
      request?: StudentBatchPrintCertificatesRequest
      studentSchemeLearningRequest?: StudentSchemeLearningRequest
      sort?: Array<StudentSchemeLearningSortRequest>
    },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.studentBatchPrintCertificates,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员端打印证书(需要判断是否强制调查问卷)
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async studentPrintCertificate(
    request: StudentPrintCertificateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.studentPrintCertificate,
    operation?: string
  ): Promise<Response<CheckPrintConditionResponse>> {
    return commonRequestApi<CheckPrintConditionResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
