"""独立部署的微服务,K8S服务名:ms-studentcourselearning-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""申请课程评价
		@param studentCourseLearningToken 课程学习凭证
		@return 课程评价凭证
	"""
	applyCourseAppraisal(studentCourseLearningToken:String):CourseAppraisalTokenResponse
	"""申请课程学习播放
		@param studentCourseLearningToken 课程学习凭证
		@return 课程学习播放凭证
	"""
	applyCourseLearningPlay(studentCourseLearningToken:String):CourseLearningPlayTokenResponse
	"""申请课后测验
		60001-查询不到课后测验配置信息，检查是否有配置课后测验
		@param studentCourseLearningToken 课程学习凭证
		@return 课后测验凭证
	"""
	applyCourseQuizLearning(studentCourseLearningToken:String):CourseQuizTokenResponse
	"""提交课程一键学习
		@param courseImmediatelyLearningToken 学员课程一键学习token
		@return 结果
	"""
	commitImmediatelyCourseLearning(courseImmediatelyLearningToken:String):Boolean!
	"""提交课件一键学习
		@param coursewareImmediatelyLearningToken 学员课件一键学习token
		@return 结果
	"""
	commitImmediatelyCoursewareLearning(coursewareImmediatelyLearningToken:String):Boolean!
}
"""课程评价凭证响应
	<AUTHOR>
	@since 2022/1/20
"""
type CourseAppraisalTokenResponse @type(value:"com.fjhb.ms.studentcourselearning.v1.kernel.gateway.graphql.response.CourseAppraisalTokenResponse") {
	"""申请结果"""
	applyResult:TokenResponse
	"""课程评价凭证"""
	token:String
}
"""课程学习播放凭证响应
	<AUTHOR>
	@since 2022/1/20
"""
type CourseLearningPlayTokenResponse @type(value:"com.fjhb.ms.studentcourselearning.v1.kernel.gateway.graphql.response.CourseLearningPlayTokenResponse") {
	"""申请结果"""
	applyResult:TokenResponse
	"""课程学习播放凭证"""
	token:String
}
"""课后测验凭证响应
	<AUTHOR>
	@since 2022/1/20
"""
type CourseQuizTokenResponse @type(value:"com.fjhb.ms.studentcourselearning.v1.kernel.gateway.graphql.response.CourseQuizTokenResponse") {
	"""申请结果
		60001 查询不到课后测验配置
		60002 课后测验已达标，不可进入
		21001 试题不足，自动合格
		60003 不大于最小学习进度
	"""
	applyResult:TokenResponse
	"""课后测验凭证"""
	token:String
	"""最小课程学习进度"""
	minCourseSchedule:Double!
}
"""凭证响应基类
	<AUTHOR>
	@since 2022/1/20
"""
type TokenResponse @type(value:"com.fjhb.ms.studentcourselearning.v1.kernel.gateway.graphql.response.TokenResponse") {
	"""代码：
		200-成功
		50001 - 已评价过，无法评价
		50002-评价配置未启用
		50003-学习进度未达标
	"""
	code:String
	"""信息"""
	message:String
}

scalar List
