<route-params content="/:schemeId"></route-params>
<route-meta>
{
"title": "修改培训方案"
}
</route-meta>

<template>
  <Operation></Operation>
</template>

<script lang="ts">
  import Operation from '@hbfe/jxjy-admin-scheme/src/diff/zztt/__components__/operation.vue'
  import { Component, Vue } from 'vue-property-decorator'

  @Component({
    components: {
      Operation
    }
  })
  export default class extends Vue {
    constructor() {
      super()
      // ***差异化***
    }
  }
</script>
