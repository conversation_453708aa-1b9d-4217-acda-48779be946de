import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-bill-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-bill-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum GatewayType {
  BAI_WANG = 'BAI_WANG',
  EASY_API = 'EASY_API',
  ANHUI_HANG_XING = 'ANHUI_HANG_XING',
  NUO_NUO = 'NUO_NUO',
  FUJIAN_BAI_WANG = 'FUJIAN_BAI_WANG',
  NUO_NUO_V2 = 'NUO_NUO_V2',
  NUO_SHUI_TONG_V2_ALL_ELECTRONIC = 'NUO_SHUI_TONG_V2_ALL_ELECTRONIC',
  NUO_SHUI_TONG_V2 = 'NUO_SHUI_TONG_V2'
}
export enum IssueMode {
  SYNC = 'SYNC',
  ASYNC = 'ASYNC'
}

// 类

/**
 * 电子发票自动开票配置
<AUTHOR>
@since 2022/3/30
 */
export class ElectronicInvoiceAutoConfigRequest {
  /**
   * 是否自动开票
   */
  auto: boolean
  /**
   * 间隔小时|自生成发票日期算起多少小时后自动开票
   */
  intervalHours: number
  /**
   * 配置类型，1为个人缴费开票配置，2为集体缴费开票配置
   */
  configType: number
}

/**
 * 电子发票自动开票配置
<AUTHOR>
@since 2022/3/30
 */
export class ElectronicInvoiceRetryIssueRequest {
  /**
   * 发票编号
   */
  invoiceId?: string
  /**
   * 发票类型，1-蓝票，2-红票
   */
  billType: number
}

/**
 * 冲红发票请求
<AUTHOR>
@since 2023/5/8
 */
export class RushRedInvoiceItemRequest {
  /**
   * 发票编号
   */
  invoiceId?: string
  /**
   * 发票实体编号
   */
  invoiceItemId?: string
  /**
   * 原始发票代码，仅红票有值
   */
  originalBillCode?: string
  /**
   * 原始发票号码，仅红票有值
   */
  originalBillNo?: string
  /**
   * 开票方式 1-开具红票,2-重试开票
   */
  issueMethod: number
  /**
   * 流水号。仅当 开票方式为：重试开票 有值
   */
  flowNo?: string
}

/**
 * 查询开票结果
<AUTHOR>
@since 2023/5/9
 */
export class SearchAndSaveTicketRequest {
  /**
   * 发票类型
   */
  billType: number
  /**
   * pdf下载标识
   */
  invoiceSerialNum?: string
  /**
   * 销售方纳税人识别号
   */
  sellerTaxpayerNo?: string
  /**
   * 发票流水号
   */
  flowNo?: string
  /**
   * 发票编号
   */
  invoiceId?: string
  /**
   * 纳税人编号
   */
  taxpayerId?: string
}

/**
 * 更新电子发票信息
<AUTHOR>
@since 2021/3/26
 */
export class UpdateElectronicInvoiceRequest {
  /**
   * 发票编号
   */
  invoiceId?: string
  /**
   * 发票抬头，null表示不更新
   */
  title?: string
  /**
   * 发票抬头类型，null表示不更新
<pre>
1-个人
2-企业
</pre>
   */
  titleType?: number
  /**
   * 购买方纳税人识别号，null表示不更新
   */
  taxpayerNo?: string
  /**
   * 购买方地址，null表示不更新
   */
  address?: string
  /**
   * 购买方电话号码，null表示不更新
   */
  phone?: string
  /**
   * 购买方开户行名称，null表示不更新
   */
  bankName?: string
  /**
   * 购买方银行账户，null表示不更新
   */
  account?: string
  /**
   * 购买方电子邮箱，null表示不更新
   */
  email?: string
  /**
   * 发票票面备注，null表示不更新
   */
  remark?: string
}

/**
 * 用户登记信息
<AUTHOR>
@since 2023/8/10
 */
export class UserRegistrationInfoRequest {
  /**
   * 纳税人识别号
   */
  taxpayerId?: string
  /**
   * 纳税人名称
   */
  taxpayerName?: string
  /**
   * 登录身份
1:财务负责人,
2:法定代表人,
3:办税人,
4:购票员,
5:普通管理员,
7:开票员
99:其他。
不能使用办税人，必须用开票员、法人、财务负责人
   */
  loginIdentity?: string
  /**
   * 办税人员登录密码
电子税局登录身份密码
   */
  taxpayerPassword?: string
  /**
   * 登录方式
&quot;2&quot;:&quot;账密或者中间号&quot;,
&quot;3&quot;:&quot;短信验证码登录
   */
  loginMethod?: string
  /**
   * 办税人员姓名
   */
  taxpayerFullName?: string
  /**
   * 办税人员身份证件号码
   */
  taxpayerIdNumber?: string
  /**
   * 办税人员手机号码
   */
  taxpayerPhoneNumber?: string
  /**
   * 中间号码
   */
  middleNo?: string
  /**
   * 登录类型
用于区分新版登录和旧版登录，部分地区只有新版可忽略此字段，传1时为新版登录否则为旧版登录。
   */
  loginType?: string
  /**
   * 登录失效标志
默认false,为true时登录信息失效时会直接返回错误,不再重试登录。
   */
  loginFailFlag?: string
  /**
   * 用户id
   */
  userId?: string
  /**
   * 地区编码
福建：fujian
   */
  areaCode?: string
  /**
   * 登录账号
电子税局登录账号
   */
  loginAccount?: string
  /**
   * 登录密码
电子税局登录密码
   */
  loginPassword?: string
  /**
   * 平台服务商下的纳税人
   */
  servicerTaxpayerId?: string
}

/**
 * 冲红结果
<AUTHOR>
@since 2023/5/8
 */
export class RushRedInvoiceItemResult {
  /**
   * code
   */
  code: string
  /**
   * 信息
   */
  message: string
  /**
   * 下载标识
   */
  invoiceSerialNum: string
  /**
   * 销售方纳税人识别号
   */
  sellerTaxpayerNo: string
  /**
   * 发票流水号
   */
  flowNo: string
  /**
   * 发票编号
   */
  invoiceId: string
  /**
   * 纳税人编号
   */
  taxpayerId: string
}

/**
 * 用户登记结果
<AUTHOR>
@since 2021/3/19
 */
export class UserRegistrationResult {
  /**
   * 登记结果
   */
  success: boolean
  /**
   * 信息
   */
  message: string
}

/**
 * 票据信息
<AUTHOR>
@since 2021/3/17
 */
export class InvoiceTicketResult {
  /**
   * 下载pdf地址
   */
  pdfOriginalUrl: string
  /**
   * 下载ofd地址
   */
  ofdOriginalUrl: string
  /**
   * 下载xml地址
   */
  xmlOriginalUrl: string
  /**
   * pdf存储相对路径
   */
  pdfFilePath: string
  /**
   * xml存储相对路径
   */
  ofdFilePath: string
  /**
   * ofd存储相对路径
   */
  xmlFilePath: string
  /**
   * 发票提供商类型
   */
  gatewayType: GatewayType
  /**
   * 发票号码
   */
  billNo: string
  /**
   * 发票代码
   */
  billCode: string
  /**
   * 校验码
   */
  checkNo: string
  /**
   * 开票时间
   */
  billTime: string
  /**
   * 开票序列号
   */
  serialNumber: string
}

/**
 * 查询结果
<AUTHOR>
@since 2021/3/18
 */
export class SearchResult {
  /**
   * 返回结果代码，详见{@link ServiceResult#SUCCESS} 和 {@link ServiceResult#ERROR}
   */
  code: string
  /**
   * 返回结果信息
   */
  message: string
  /**
   * 发票流水号
   */
  flowNo: string
  /**
   * 开票方式
   */
  mode: IssueMode
  /**
   * 发票票据信息，仅当{@link #mode}等于{@link IssueMode#SYNC},有值
   */
  ticket: InvoiceTicketResult
}

/**
 * 开票结果
<AUTHOR>
@since 2021/3/26
 */
export class ElectronicInvoiceResultResponse {
  /**
   * 开票结果
   */
  success: boolean
  /**
   * 信息
   */
  message: string
}

/**
 * 开票中的日志结果
<AUTHOR>
@since 2024/10/16
 */
export class InvoicingServiceLogResult {
  /**
   * 发票id
   */
  invoiceId: string
  /**
   * 日志编号
   */
  id: string
  /**
   * 开票流水号
   */
  flowNo: string
  /**
   * 服务接口类型|1-请求开票，2-查询发票，3-下载发票
   */
  serviceType: number
  /**
   * 服务接口类型名称
   */
  serviceName: string
  /**
   * 请求时间
   */
  requestTime: string
  /**
   * 响应时间
   */
  responseTime: string
  /**
   * 请求报文
   */
  requestData: string
  /**
   * 响应报文
   */
  responseData: string
  /**
   * 响应状态码
   */
  responseCode: string
  /**
   * 响应消息
   */
  responseMessage: string
  /**
   * 服务商名称
   */
  providerName: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量获取发票状态为开票中的蓝票日志
   * @param query 查询 graphql 语法文档
   * @param invoiceIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getInvoicingServiceLogOfBlueTicket(
    invoiceIdList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getInvoicingServiceLogOfBlueTicket,
    operation?: string
  ): Promise<Response<Array<InvoicingServiceLogResult>>> {
    return commonRequestApi<Array<InvoicingServiceLogResult>>(
      SERVER_URL,
      {
        query: query,
        variables: { invoiceIdList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量获取发票状态为开票中的红票日志
   * @param query 查询 graphql 语法文档
   * @param invoiceIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getInvoicingServiceLogOfRedTicket(
    invoiceIdList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getInvoicingServiceLogOfRedTicket,
    operation?: string
  ): Promise<Response<Array<InvoicingServiceLogResult>>> {
    return commonRequestApi<Array<InvoicingServiceLogResult>>(
      SERVER_URL,
      {
        query: query,
        variables: { invoiceIdList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 添加或更新当前网校自动开票配置
   * 网校id从上下文中获取
   * @param autoConfigRequest 配置
   * @param mutate 查询 graphql 语法文档
   * @param autoConfigRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addOrUpdateElectronicInvoiceAutoConfig(
    autoConfigRequest: ElectronicInvoiceAutoConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.addOrUpdateElectronicInvoiceAutoConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { autoConfigRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 请求冲红电子发票
   * @param invoiceId 发票编号
   * @return 开票结果
   * @param mutate 查询 graphql 语法文档
   * @param invoiceId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async flushElectronicInvoice(
    invoiceId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.flushElectronicInvoice,
    operation?: string
  ): Promise<Response<ElectronicInvoiceResultResponse>> {
    return commonRequestApi<ElectronicInvoiceResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { invoiceId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 请求开具电子发票
   * @param invoiceId 发票编号
   * @return 开票结果
   * @param mutate 查询 graphql 语法文档
   * @param invoiceId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async issueElectronicInvoice(
    invoiceId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.issueElectronicInvoice,
    operation?: string
  ): Promise<Response<ElectronicInvoiceResultResponse>> {
    return commonRequestApi<ElectronicInvoiceResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { invoiceId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 作废发票
   * <pre>
   * 补偿接口，用于将未开具的发票作废
   * @param invoiceId 发票编号
   * @param mutate 查询 graphql 语法文档
   * @param invoiceId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async obsoleteElectronicInvoice(
    invoiceId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.obsoleteElectronicInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { invoiceId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 取消开票（谨慎使用！！！）
   * <pre>
   * 补偿接口，用于标记电子发票蓝票开票失败，以便后续重新开票；
   * 仅当已确认发票已经开具失败后使用的台账处理接口
   * @param invoiceId 发票编号
   * @param mutate 查询 graphql 语法文档
   * @param invoiceId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async reimburseCancelElectronicInvoice(
    invoiceId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.reimburseCancelElectronicInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { invoiceId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重试开具发票
   * @param request 发票编号, 发票类型：1-蓝票，2-红票
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async retryIssueElectronicInvoice(
    request: ElectronicInvoiceRetryIssueRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.retryIssueElectronicInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 直接冲红发票
   * <pre>
   * 补偿接口，用于冲红发票
   * 获取发票信息直接请求第三方进行冲红，不生成红票实体
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rushRedInvoiceItem(
    request: RushRedInvoiceItemRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.rushRedInvoiceItem,
    operation?: string
  ): Promise<Response<RushRedInvoiceItemResult>> {
    return commonRequestApi<RushRedInvoiceItemResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询发票结果并下载发票
   * <pre>
   * 补偿接口，用于查询开票结果并下载发票
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async searchInvoiceResultAndDownload(
    request: SearchAndSaveTicketRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.searchInvoiceResultAndDownload,
    operation?: string
  ): Promise<Response<SearchResult>> {
    return commonRequestApi<SearchResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新电子发票信息
   * @param request 更新发票信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateElectronicInvoice(
    request: UpdateElectronicInvoiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateElectronicInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 用户登记
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async userRegistration(
    request: UserRegistrationInfoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.userRegistration,
    operation?: string
  ): Promise<Response<UserRegistrationResult>> {
    return commonRequestApi<UserRegistrationResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
