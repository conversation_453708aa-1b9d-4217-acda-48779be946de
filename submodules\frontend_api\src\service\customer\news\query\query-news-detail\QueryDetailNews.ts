/*
 * @Description: 资讯详情
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-09 15:01:58
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-17 11:31:42
 */
import BasicDataQueryForestage, {
  NewsDetailWithPreviousAndNextCommonResponse,
  NewsWithPreviousAndNextCommonRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { getCommonNewsDetailWithPreviousAndNext } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage/graphql-importer'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import MutationRecordNews from '@api/service/customer/news/mutation/MutationNewsFactory'
import QueryNewsDetailVo from '@api/service/customer/news/query/query-news-detail/vo/NewsDetail'
import PlatformTrainingChannel from '@api/platform-gateway/platform-training-channel-fore-gateway'
import NewsDetail from '@api/service/customer/news/query/query-news-detail/vo/NewsDetail'

class QueryNewsDetail {
  private id = ''
  constructor(id: string) {
    this.id = id
  }
  async queryNewsDetail(isH5?: boolean): Promise<QueryNewsDetailVo> {
    const { data } = await BasicDataQueryForestage.getCommonNewsDetailWithPreviousAndNext({ newId: this.id })
    const newsDetai = QueryNewsDetailVo.from(data)
    const promiseArr = []
    if (newsDetai.prevNews.id) {
      promiseArr.push(
        BasicDataQueryForestage.getCommonNewsDetailWithPreviousAndNext({
          newId: newsDetai.prevNews.id
        })
      )
    }
    if (newsDetai.nestNews.id) {
      promiseArr.push(BasicDataQueryForestage.getCommonNewsDetailWithPreviousAndNext({ newId: newsDetai.nestNews.id }))
    }
    const response = await Promise.all(promiseArr)
    for (let i = 0; i < response.length; i++) {
      const element = response[i].data
      if (element.newsDetail.newId === newsDetai.prevNews.id) {
        newsDetai.prevNews.title = element.newsDetail.title
      }
      if (element.newsDetail.newId === newsDetai.nestNews.id) {
        newsDetai.nestNews.title = element.newsDetail.title
      }
    }
    const mutationRecordNews = new MutationRecordNews()
    if (!isH5) {
      await mutationRecordNews.doMutationRecordNews().doRecordNews(this.id) // 记录次数
    }
    return newsDetai
  }

  async querySpecialNewsDetail(specialId: string, isH5?: boolean) {
    const { data } = await BasicDataQueryForestage.getTrainingChannelCommonNewsDetailWithPreviousAndNext({
      newId: this.id,
      specialSubjectId: specialId
    })
    const newsDetai = QueryNewsDetailVo.from(data)
    const promiseArr = []
    if (newsDetai.prevNews.id) {
      promiseArr.push(
        BasicDataQueryForestage.getTrainingChannelCommonNewsDetailWithPreviousAndNext({
          newId: newsDetai.prevNews.id,
          specialSubjectId: specialId
        })
      )
    }
    if (newsDetai.nestNews.id) {
      promiseArr.push(
        BasicDataQueryForestage.getTrainingChannelCommonNewsDetailWithPreviousAndNext({
          newId: newsDetai.nestNews.id,
          specialSubjectId: specialId
        })
      )
    }
    const response = await Promise.all(promiseArr)
    for (let i = 0; i < response.length; i++) {
      const element = response[i].data
      if (element.newsDetail.newId === newsDetai.prevNews.id) {
        newsDetai.prevNews.title = element.newsDetail.title
      }
      if (element.newsDetail.newId === newsDetai.nestNews.id) {
        newsDetai.nestNews.title = element.newsDetail.title
      }
    }
    const mutationRecordNews = new MutationRecordNews()
    if (!isH5) {
      await mutationRecordNews.doMutationRecordNews().doRecordNews(this.id) // 记录次数
    }
    return newsDetai
  }

  /**
   * 批量查询没有上一个下一个的资讯详情H5专用
   */
  async queryNewsDetailNoPreAndNoNextByH5(ids: string[]) {
    const param: NewsWithPreviousAndNextCommonRequest[] = []
    ids.forEach(item => {
      const request = new NewsWithPreviousAndNextCommonRequest()
      request.newId = item
      param.push(request)
    })
    const rewGra = new RewriteGraph<NewsDetailWithPreviousAndNextCommonResponse, NewsWithPreviousAndNextCommonRequest>(
      BasicDataQueryForestage._commonQuery,
      getCommonNewsDetailWithPreviousAndNext
    )
    await rewGra.request(param)
    const map = new Map<string, NewsDetailWithPreviousAndNextCommonResponse>()
    rewGra.indexMap.forEach(value => {
      map.set(value.newsDetail.newId, value)
    })
    return map
  }
}
export default QueryNewsDetail
