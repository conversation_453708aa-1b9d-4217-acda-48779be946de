import store from '../../../../store'
import { Module as Mod } from 'vuex'
import { Module, VuexModule, getModule, Action, Mutation } from 'vuex-module-decorators'
import PlatformExamGateway, { ErrorProneStatisticDTO, ErrorProneStatisticParamDTO } from '@api/gateway/PlatformExam'
import Response, { ResponseStatus } from '../../../../Response'
import ChapterTree from '../../../common/syllabus/model/ChapterTree'
import { Role, RoleType } from '../../../../Secure'
import ErrorPronePaper from '@api/service/customer/my-question/errorprone/models/ErrorPronePaper'
import ErrorPronePracticeAnswer from '@api/service/customer/my-question/errorprone/models/ErrorPronePracticeAnswer'
import SyllabusModule from '@api/service/customer/syllabus/SyllabusModule'
import Chapter from '@api/service/common/models/syllabus/Chapter'

export class ErrorProneQuestionSyllabusTree {
  /**
   * 章节编号，对应标签编号
   */
  id: string
  /**
   * 章节名称，对应标签名称
   */
  name: string
  /**
   * 当前节点关系编号
   * @description 由于同一个标签可能存在在同一个关系中的不同节点位置,
   * 只有节点关系编号才能定位一个章节的位置
   *
   */
  relationId: string
  /**
   * 同一级序号
   */
  sequence: number
  /**
   * 子章节
   */
  children: Array<ErrorProneQuestionSyllabusTree> = new Array<ErrorProneQuestionSyllabusTree>()

  /**
   * 是否正在作答
   */
  answering: boolean

  /**
   * 剩余试题数量
   */
  restQuestionCount: number

  hasChild() {
    return this.children && this.children.length > 0
  }
}

export class ErrorProneQuestionTypeItem {
  /**
   * 试题题型 - 按题型分组统计，该字段才生效
   */
  questionType: string
  /**
   * 剩余试题数量
   */
  restQuestionCount: number
  /**
   * 是否正在作答
   */
  answering: boolean
}

class StateCache {
  constructor(schemeId: string, learningId: string, majorId: string) {
    this.schemeId = schemeId
    this.learningId = learningId
    this.majorId = majorId
  }

  // 方案id
  schemeId: string
  // 方式id
  learningId: string
  // 专业id
  majorId: string
  // 错题重练卷
  questionPractice: ErrorPronePaper = new ErrorPronePaper()
  // 答卷列表
  latestErrorProneAnswer: Array<ErrorPronePracticeAnswer> = new Array<ErrorPronePracticeAnswer>()
  // 各章节剩余试题统计
  allChapterRestQuestionStatistic: Array<ErrorProneStatisticDTO> = new Array<ErrorProneStatisticDTO>()
  // 易错题总数
  questionStatistic = 0
  // 各试题类型剩余试题统计
  allQuestionTypeRestQuestionStatistic: Array<ErrorProneStatisticDTO> = new Array<ErrorProneStatisticDTO>()
  // 是否需要重载，在学员触发去作答练习是置为true，在下次取数时先清空数据然后加载新数据
  needReload = false
  // 最新一次加载时间，从apollo配置的超时时间与当前时间判断，在下次取数时清空当前取数数据然后加载新数据
  latestLoadTime: Date = new Date()
}

export interface IState {
  /**
   * 各学习方式错题重练数据
   */
  learningErrorProneListMap: Array<StateCache>
  // 作答id
  currentAnswerId: string
}

@Module({
  namespaced: true,
  store,
  name: 'CustomerErrorProneModule',
  dynamic: true
})
class ErrorProneModule extends VuexModule implements IState {
  /**
   * 各学习方式错题重练数据
   */
  learningErrorProneListMap = new Array<StateCache>()
  // 作答id
  currentAnswerId = ''

  constructor(module: Mod<ThisType<any>, any>) {
    super(module)
    store.subscribe(mutation => {
      if (mutation.type?.indexOf('ALERT_EXAM_RELOAD') !== -1) {
        this.processSubmitPaperMessage(mutation.payload)
      }
    })
  }

  @Role([RoleType.user])
  @Action
  async init(payload: any) {
    if (
      !this.learningErrorProneListMap.find(p => p.learningId === payload.learningId) ||
      this.learningErrorProneListMap.find(p => p.learningId === payload.learningId)?.needReload
    ) {
      if (!this.learningErrorProneListMap.find(p => p.learningId === payload.learningId)) {
        const stateCache = new StateCache(payload.schemeId, payload.learningId, payload.majorId)
        this.setStateCacheToLearningErrorProneListMap(stateCache)
      }

      // 获取易错题练习卷
      const stateCache = new StateCache(payload.schemeId, payload.learningId, payload.majorId)
      // const practicePaperResponse = await PreExamGateway.getErrorPronePracticePaper(payload.learningId)
      // stateCache.questionPractice = practicePaperResponse.data
      //
      // // 获取最新的答卷
      // const latestAnswerPaperResponse = await PreExamGateway.getPreExamErrorPronePracticeList(payload.learningId)
      // if (!latestAnswerPaperResponse.status.isSuccess()) {
      //   return latestAnswerPaperResponse.status
      // }
      // stateCache.latestErrorProneAnswer = latestAnswerPaperResponse.data
      // 初始化考纲信息
      const status = await SyllabusModule.init()
      if (!status.isSuccess()) {
        return status
      }
      const leafSyllabus: Array<Chapter> = SyllabusModule.getLeafSyllabusByMajorId(payload.majorId)

      const errorProneParam: ErrorProneStatisticParamDTO = new ErrorProneStatisticParamDTO()
      errorProneParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      // 获取各章节剩余题数
      let response: Response<any> = await PlatformExamGateway.statisticUserRemainErrorProneGroupByTag(errorProneParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allChapterRestQuestionStatistic = response.data

      // 获取各题型剩余题数
      response = await PlatformExamGateway.statisticUserRemainErrorProneGroupByQuestionType(errorProneParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionTypeRestQuestionStatistic = response.data

      response = await PlatformExamGateway.countTotalErrorProne(leafSyllabus.map(leaf => leaf.id))
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.questionStatistic = response.data

      this.setStateCacheToLearningErrorProneListMap(stateCache)
    }
  }

  @Role([RoleType.user])
  @Action
  async goExamOutlinePractice(payload: any) {
    if (!payload || !payload.learningId) {
      return new ResponseStatus(500, '请传入学习方式id')
    }
    const stateCache = this.learningErrorProneListMap.find(p => p.learningId === payload.learningId)
    if (!stateCache) {
      return new ResponseStatus(500, '当前期别对应易错题信息未初始化，请先init')
    }

    // const examOutlineParam: ApplyExamOutlineErrorPronePracticeLearningTokenRequest = new ApplyExamOutlineErrorPronePracticeLearningTokenRequest()
    // examOutlineParam.schemeId = payload.schemeId
    // examOutlineParam.issueId = payload.issueId
    // examOutlineParam.chaptersId = payload.chapterId
    // const tokenResponse = await PreExamLSGateway.applyExamOutlineErrorPronePracticeLearningToken(examOutlineParam)
    //
    // const goPracticeResponse = await PreExamGateway.goPracticeByToken(tokenResponse.data)

    this.setNeedReload({
      schemeId: stateCache.schemeId,
      learningId: stateCache.learningId,
      majorId: stateCache.majorId
    })
    // this.setAnswerId(goPracticeResponse.data)
    // return goPracticeResponse.status
  }

  @Role([RoleType.user])
  @Action
  async goQuestionTypePractice(payload: any) {
    if (!payload || !payload.learningId) {
      return new ResponseStatus(500, '请传入学习方式id')
    }
    const stateCache = this.learningErrorProneListMap.find(p => p.learningId === payload.learningId)
    if (!stateCache) {
      return new ResponseStatus(500, '当前期别对应易错题信息未初始化，请先init')
    }

    // const examOutlineParam: ApplyQuestionTypeErrorPronePracticeLearningTokenRequest = new ApplyQuestionTypeErrorPronePracticeLearningTokenRequest()
    // // examOutlineParam.userId = payload.userId
    // examOutlineParam.schemeId = payload.schemeId
    // examOutlineParam.issueId = payload.issueId
    // examOutlineParam.professionId = payload.majorId
    // examOutlineParam.questionType = payload.questionType
    // const tokenResponse = await PreExamLSGateway.applyQuestionTypeErrorPronePracticeLearningToken(examOutlineParam)
    // const goPracticeResponse = await PreExamGateway.goPracticeByToken(tokenResponse.data)

    this.setNeedReload({
      schemeId: stateCache.schemeId,
      learningId: stateCache.learningId,
      majorId: stateCache.majorId
    })

    // this.setAnswerId(goPracticeResponse.data)
    // return goPracticeResponse.status
  }

  @Role([RoleType.user])
  @Action
  async goExamOutlinePracticeWithSpecifyCount(payload: any) {
    if (!payload || !payload.learningId) {
      return new ResponseStatus(500, '请传入学习方式id')
    }
    const stateCache = this.learningErrorProneListMap.find(p => p.learningId === payload.learningId)
    if (!stateCache) {
      return new ResponseStatus(500, '当前期别对应易错题信息未初始化，请先init')
    }

    // const examOutlineParam: ApplyExamOutlineErrorPronePracticeLearningTokenRequest = new ApplyExamOutlineErrorPronePracticeLearningTokenRequest()
    // examOutlineParam.schemeId = payload.schemeId
    // examOutlineParam.issueId = payload.issueId
    // examOutlineParam.chaptersId = payload.chapterId
    // const tokenResponse = await PreExamLSGateway.applyExamOutlineErrorPronePracticeLearningToken(examOutlineParam)

    // const goPracticeResponse = await PreExamGateway.goPracticeByTokenAndSpecifyQuestionCount({
    //   token: tokenResponse.data,
    //   questionCount: payload.questionCount
    // })

    this.setNeedReload({
      schemeId: stateCache.schemeId,
      learningId: stateCache.learningId,
      majorId: stateCache.majorId
    })

    // this.setAnswerId(goPracticeResponse.data)
    // return goPracticeResponse.status
  }

  @Role([RoleType.user])
  @Action
  async goQuestionTypePracticeWithSpecifyCount(payload: any) {
    if (!payload || !payload.learningId) {
      return new ResponseStatus(500, '请传入学习方式id')
    }
    const stateCache = this.learningErrorProneListMap.find(p => p.learningId === payload.learningId)
    if (!stateCache) {
      return new ResponseStatus(500, '当前期别对应易错题信息未初始化，请先init')
    }

    // const examOutlineParam: ApplyQuestionTypeErrorPronePracticeLearningTokenRequest = new ApplyQuestionTypeErrorPronePracticeLearningTokenRequest()
    // // examOutlineParam.userId = payload.userId
    // examOutlineParam.schemeId = payload.schemeId
    // examOutlineParam.issueId = payload.issueId
    // examOutlineParam.professionId = payload.majorId
    // examOutlineParam.questionType = payload.questionType
    // const tokenResponse = await PreExamLSGateway.applyQuestionTypeErrorPronePracticeLearningToken(examOutlineParam)
    // const goPracticeResponse = await PreExamGateway.goPracticeByTokenAndSpecifyQuestionCount({
    //   token: tokenResponse.data,
    //   questionCount: payload.questionCount
    // })

    this.setNeedReload({
      schemeId: stateCache.schemeId,
      learningId: stateCache.learningId,
      majorId: stateCache.majorId
    })
    // this.setAnswerId(goPracticeResponse.data)
    // return goPracticeResponse.status
  }

  @Action
  async processSubmitPaperMessage(payload: { schemeId: string; issueId: string; answersId: string }) {
    this.learningErrorProneListMap.forEach((stateCache: StateCache) => {
      if (
        stateCache?.schemeId === payload.schemeId &&
        (!payload.answersId || this.currentAnswerId === payload.answersId)
      ) {
        console.log('易错题匹配到，needReload置为true' + JSON.stringify(payload))
        this.setNeedReload({
          schemeId: stateCache.schemeId,
          learningId: stateCache.learningId,
          majorId: stateCache.majorId
        })
      }
    })
  }

  @Mutation
  setStateCacheToLearningErrorProneListMap(payload: StateCache) {
    this.learningErrorProneListMap = this.learningErrorProneListMap.filter(p => p.learningId !== payload.learningId)
    this.learningErrorProneListMap.push(payload)
  }

  @Mutation
  setNeedReload(payload: any) {
    const stateCache = this.learningErrorProneListMap.find(p => p.learningId === payload.learningId)
    if (stateCache) {
      stateCache.needReload = true
    }
  }

  @Mutation
  setAnswerId(answerId: string) {
    this.currentAnswerId = answerId
  }

  /**
   * 获取易错题试卷
   */
  get getQuestionPractice() {
    return (schemeId: string, learningId: string) => {
      return this.learningErrorProneListMap.find(p => p.learningId === learningId)?.questionPractice
    }
  }

  /**
   * 获取答卷列表
   */
  get getLatestErrorProneAnswer() {
    return (schemeId: string, learningId: string) => {
      return this.learningErrorProneListMap.find(p => p.learningId === learningId)?.latestErrorProneAnswer
    }
  }

  /**
   * 获取各章节剩余试题统计
   */
  get getAllChapterRestQuestionStatistic() {
    return (schemeId: string, learningId: string) => {
      return this.learningErrorProneListMap.find(p => p.learningId === learningId)?.allChapterRestQuestionStatistic
    }
  }

  /**
   * 获取易错题总数
   */
  get getQuestionStatistic() {
    return (schemeId: string, learningId: string) => {
      return this.learningErrorProneListMap.find(p => p.learningId === learningId)?.questionStatistic
    }
  }

  /**
   * 获取各试题类型剩余试题统计
   */
  get getAllQuestionTypeRestQuestionStatistic() {
    return (schemeId: string, learningId: string) => {
      return this.learningErrorProneListMap.find(p => p.learningId === learningId)?.allQuestionTypeRestQuestionStatistic
    }
  }

  /**
   * 当前章节或题型答卷是否正在作答
   */
  get isAnswering() {
    return (
      schemeId: string,
      learningId: string,
      fetchWay: number,
      examinationOutlineId: string | null,
      questionType: string
    ) => {
      const practiceAnswer = this.getLatestErrorProneAnswer(schemeId, learningId)?.find(
        (p: ErrorPronePracticeAnswer) => {
          if (fetchWay === 3) {
            return p.fetchWay === fetchWay && p.questionType === questionType
          } else {
            return p.fetchWay === fetchWay && p.examinationOutlineId === examinationOutlineId
          }
        }
      )
      return !(practiceAnswer && practiceAnswer.complete)
    }
  }

  /**
   * 各错题题型错题统计
   */
  get allQuestionTypeQuestionStatistic() {
    return (schemeId: string, learningId: string) => {
      return this.getAllQuestionTypeRestQuestionStatistic(schemeId, learningId)?.map((p: ErrorProneStatisticDTO) => {
        const item: ErrorProneQuestionTypeItem = new ErrorProneQuestionTypeItem()
        item.questionType = p.questionType
        item.restQuestionCount = p.remainCount
        item.answering = this.isAnswering(schemeId, learningId, 3, null, p.questionType)
        return item
      })
    }
  }

  /**
   * 指定章节的错题总数
   */
  get specifyExaminationOutlineQuestionCount() {
    return (schemeId: string, learningId: string, leafSyllabus: Array<string>) => {
      return (
        this.getAllChapterRestQuestionStatistic(schemeId, learningId)
          ?.filter(
            (p: ErrorProneStatisticDTO) => p.tags && p.tags.some((tagId: string) => leafSyllabus.includes(tagId))
          )
          .map((p: ErrorProneStatisticDTO) => p.remainCount)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }

  /**
   * 根据考纲树获取试题及作答统计信息
   */
  get allChapterQuestionStatisticWithSyllabus() {
    return (schemeId: string, learningId: string, syllabusTree: Array<ChapterTree>) => {
      // 自定义函数，填充已答试题信息
      const fillQuestionAnswerStatistic = (syllabusTree: Array<ChapterTree>) => {
        const questionPracticeSyllabusTree: Array<ErrorProneQuestionSyllabusTree> = new Array<
          ErrorProneQuestionSyllabusTree
        >()
        syllabusTree.forEach((syllabus: ChapterTree) => {
          const questionPracticeSyllabus: ErrorProneQuestionSyllabusTree = new ErrorProneQuestionSyllabusTree()
          questionPracticeSyllabus.id = syllabus.id
          questionPracticeSyllabus.name = syllabus.name
          questionPracticeSyllabus.relationId = syllabus.relationId
          questionPracticeSyllabus.sequence = syllabus.sequence
          questionPracticeSyllabus.answering = this.isAnswering(schemeId, learningId, 1, syllabus.id, '')
          questionPracticeSyllabus.restQuestionCount = this.specifyExaminationOutlineQuestionCount(
            schemeId,
            learningId,
            syllabus.getLeafChapterIds()
          )
          if (syllabus.children) {
            questionPracticeSyllabus.children = fillQuestionAnswerStatistic(syllabus.children)
          }
          questionPracticeSyllabusTree.push(questionPracticeSyllabus)
        })
        return questionPracticeSyllabusTree
      }
      return fillQuestionAnswerStatistic(syllabusTree)
    }
  }

  /**
   * 易错题总数
   */
  get totalErrorProneQuestionCount() {
    return (schemeId: string, learningId: string) => {
      return this.getQuestionStatistic(schemeId, learningId) || 0
    }
  }

  /**
   * 消灭易错题数
   */
  get eliminateErrorProneQuestionCount() {
    return (schemeId: string, learningId: string) => {
      const eliminateCount =
        this.totalErrorProneQuestionCount(schemeId, learningId) -
        (this.getAllQuestionTypeRestQuestionStatistic(schemeId, learningId)
          ?.map((p: ErrorProneStatisticDTO) => p.remainCount)
          .reduce((a: number, b: number) => a + b, 0) || 0)
      return eliminateCount < 0 ? 0 : eliminateCount
    }
  }
}

export default getModule(ErrorProneModule)
