import QueryBatchOrderListBase from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderListBase'
import { Page } from '@hbfe/common'
import QueryBatchOrderListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderListVo'
import BatchOrderListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderListDetailVo'
import BatchOrderUtils from '@api/service/management/trade/batch/order/query/utils/BatchOrderUtils'
import DataResolve from '@api/service/common/utils/DataResolve'
import msTradeQuery, {
  BatchOrderSortField,
  BatchOrderSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { BatchOrderTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
import BatchOrderListStatisticVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderListStatisticVo'
import { cloneDeep } from 'lodash'

/**
 * 查询批次订单 - 专题管理员
 */
export default class QueryBatchOrderListInTrainingChannel extends QueryBatchOrderListBase {
  totalSize: number

  /**
   * 【集体报名订单】查询列表
   * @param {Page} page - 分页参数
   * @param {QueryBatchOrderListVo} queryParams - 查询参数
   * @return {Promise<BatchOrderListDetailVo[]>} - 【集体报名订单】列表
   */
  async queryBatchOrderList(page: Page, queryParams: QueryBatchOrderListVo): Promise<BatchOrderListDetailVo[]> {
    if (queryParams.buyerName || queryParams.buyerAccount) {
      const userIds = await BatchOrderUtils.getBuyerIdList(queryParams)
      if (!DataResolve.isWeightyArr(userIds)) {
        page.totalSize = 0
        page.totalPageSize = 0
        return [] as BatchOrderListDetailVo[]
      }
    }
    const remoteQueryParams = await queryParams.to()
    // 默认按批次单创建时间降序排
    const sortOption = new BatchOrderSortRequest()
    sortOption.field = BatchOrderSortField.BATCH_ORDER_UN_CONFIRMED_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = [] as BatchOrderSortRequest[]
    sortRequest.push(sortOption)
    const response = await msTradeQuery.pageBatchOrderInTrainingChannel({
      page,
      request: remoteQueryParams,
      sortRequest
    })
    page.totalSize = response.data?.totalSize || 0
    page.totalPageSize = response.data?.totalPageSize || 0
    this.totalSize = page.totalSize
    const batchOrderNos: string[] = []
    const successBatch: string[] = []
    const res =
      response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)
        ? await Promise.all(
            response.data.currentPageData.map(async (item) => {
              batchOrderNos.push(item.batchOrderNo)
              // 发票信息
              if (BatchOrderUtils.getBatchOrderStatus(item) === BatchOrderTradeStatusEnum.Pay_Success) {
                successBatch.push(item.batchOrderNo)
                // detail.hasRefundRecord = await BatchOrderUtils.getBatchHasRefundRecord(detail.batchOrderNo)
              }
              return await BatchOrderListDetailVo.fromList(item)
            })
          )
        : ([] as BatchOrderListDetailVo[])
    const billMap = await BatchOrderUtils.getBatchOrderInvoiceInfoListS(response.data.currentPageData)
    const reMap = await BatchOrderUtils.getTrainingChannelBatchHasRefundRecords(successBatch)
    // const map = await BatchOrderUtils.getTrainingChannelRefundInfoS(batchOrderNos)
    res.forEach((item) => {
      item.refundInfo.enableRefundPersonTime = item.payTimes ?? 0
      item.refundInfo.enableRefundAmount = item.orderAmount ?? 0
      item.refundInfo.enableRefund = item.refundInfo.enableRefundPersonTime > 0
      if (billMap.get(item.batchOrderNo)) {
        item.invoiceInfoList = billMap.get(item.batchOrderNo)
      }
      item.hasRefundRecord = reMap.get(item.batchOrderNo)
    })
    return await this.fillCollectiveInfoForList(res)
  }

  /**
   * 【集体报名订单】查询列表统计数据
   * @param {QueryBatchOrderListVo} queryParams - 查询参数
   * @return {Promise<BatchOrderListStatisticVo>}
   */
  async queryBatchOrderListStatistic(queryParams: QueryBatchOrderListVo): Promise<BatchOrderListStatisticVo> {
    const result = new BatchOrderListStatisticVo()
    if (queryParams.buyerName || queryParams.buyerAccount) {
      const userIds = await BatchOrderUtils.getBuyerIdList(queryParams)
      if (!DataResolve.isWeightyArr(userIds)) return result
    }
    result.totalOrderCount = this.totalSize
    result.totalOrderAmount = await this.getBatchOrderTotalAmount(queryParams)
    return result
  }

  /**
   * 获取批次单列表交易成功总金额
   */
  async getBatchOrderTotalAmount(queryParams: QueryBatchOrderListVo): Promise<number> {
    let result = 0
    const params = cloneDeep(queryParams)
    params.orderStatus = BatchOrderTradeStatusEnum.Pay_Success
    const remoteQueryParams = await params.to()
    const response = await msTradeQuery.statisticBatchOrderInTrainingChannel(remoteQueryParams)
    if (response.status?.isSuccess()) {
      result = response.data?.totalBatchOrderAmount ?? 0
    }
    return result
  }
}
