// import studentCourseLearningQuery from '@api/platform-gateway/platform-jxjypxtypt-ahzj-student-learning-backstage'
import studentCourseLearningQueryDiff from '@api/platform-gateway/platform-jxjypxtypt-student-learning-backstage'
export default class NYYZStudentTrainClassDetailVo {
  /**
   * 重新推送 南阳医专
   */
  async rePush(studentNoList: string[]) {
    const res = await studentCourseLearningQueryDiff.rePushStudentTrainingResultToGatewayInServicerV2(studentNoList)
    return res
  }
}
