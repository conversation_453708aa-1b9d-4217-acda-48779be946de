<template>
  <div></div>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'

  interface User {
    name: string
    age: number
  }
  @Component
  export default class extends Vue {
    @Prop({
      type: String,
      default: ''
    })
    userId!: string // 感叹号表示这个prop一定会有值

    private user: User | null = null // 明确类型可以是null
    private loading = false

    get userName() {
      return this.user ? this.user.name : '未知用户'
    }

    @Watch('userId', {
      immediate: true
    })
    handler(newId: string) {
      this.fetchUser(newId)
    }

    fetchUser(id: string) {
      this.loading = true
      // 获取用户信息
      this.loading = false
    }
  }
</script>
