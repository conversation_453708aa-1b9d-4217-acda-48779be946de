<template>
  <div></div>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'

    interface User {
        name: string
        age: number
    }
  @Component
  export default class extends Vue {
    @Prop({
      type: String,
      default: ''
    })
    userId: string

    private user: User = null
    private loading = false

    get userName() {
      return this.user ? this.user.name : '未知用户'
    }

    @Watch({
        
    })

  }
</script>
