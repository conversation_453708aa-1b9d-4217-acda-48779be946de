import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-basicdata-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-basicdata-query-front-gateway-backstage-zhjy'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum TimeUnitEnum {
  YEARS = 'YEARS',
  MONTHS = 'MONTHS',
  DAYS = 'DAYS',
  HOURS = 'HOURS'
}
export enum EnterprisePersonSortFieldEnum {
  createdTime = 'createdTime',
  accountType = 'accountType',
  userNameFirstLetter = 'userNameFirstLetter',
  nature = 'nature'
}
export enum EnterpriseUnitSortFieldEnum {
  createdTime = 'createdTime',
  unitName = 'unitName',
  pinyinName = 'pinyinName',
  regionId = 'regionId'
}
export enum GovernmentUnitSortFieldEnum {
  unitRegionPath = 'unitRegionPath',
  unitName = 'unitName',
  createdTime = 'createdTime'
}
export enum PersonAccountSortFieldEnum {
  createdTime = 'createdTime',
  nature = 'nature'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 功能描述：账户信息查询条件
@Author： wtl
@Date： 2022年5月11日 15:30:56
 */
export class AccountRequest {
  /**
   * 账户类型 1：企业帐户，2：企业个人帐户，3：个人帐户
@see AccountTypes
   */
  accountTypeList?: Array<number>
  /**
   * 账户状态 1：正常，2：冻结
@see AccountStatus
   */
  statusList?: Array<number>
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest
  /**
   * 创建人用户id
   */
  createdUserId?: string
  /**
   * 单位id （原始单位id，不会随着id 人员与单位的关系变化而变化）
   */
  unitId?: Array<string>
  /**
   * 单位id匹配方式 默认-1、and匹配 2、or匹配
@see MatchTypeConstant
   */
  unitIdMatchType?: number
}

/**
 * 功能描述：登录认证查询条件
@Author： wtl
@Date： 2022年1月26日 09:30:12
 */
export class AuthenticationRequest {
  /**
   * 帐号
   */
  identity?: string
}

/**
 * 功能描述 : 直方图统计查询条件
 */
export class DateHistogramRequest {
  /**
   * 开始时间（必填）
   */
  startTime?: string
  /**
   * 结束时间（必填）
   */
  endTime?: string
  /**
   * 时间单位枚举（必填）
   */
  timeUnit?: TimeUnitEnum
}

/**
 * 功能描述：角色查询条件
@Author： wtl
@Date： 2022年5月11日 11:46:41
 */
export class RoleRequest {
  /**
   * 角色id集合
   */
  roleIdList?: Array<string>
  /**
   * 角色类型
（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
   */
  roleTypeList?: Array<string>
  /**
   * 角色类别
（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管 10:人社超管
11:人社职建处 12:人社就业局 13:超级管理员 14:合同供应商 15:专家 16:电子劳动合同业务角色 320:地区管理员 410:人社行业管理员
411:人社行业省级管理员 412:人社行业市级管理员 413:人社行业区县级管理员 420:人社地区管理员 430:人社业务管理员 440:省级人社主管 450:市级人社主管
460:区县级人社主管 510:培训机构管理员 520:技工院校管理员 530:职业院校管理员 540:政策参与者 550:线上培训机构管理员 560:课件供应商
5001:学徒制_企业管理员 5101:学徒制_培训机构管理员 5201:学徒制_技工院校管理员 5301:学徒制_职业院校管理员 4011:学徒制_人社_省级管理员
4021:学徒制_人社_市级管理员 4031:学徒制_人社_区/县级管理员 6001:揭榜挂帅_企业管理员 6101:揭榜挂帅_培训机构管理员
6201:揭榜挂帅_技工院校管理员 6301:揭榜挂帅_职业院校管理员 6401:揭榜挂帅_人社_省级管理员 6402:揭榜挂帅_人社_市级管理员 6403:揭榜挂帅_人社_区/县级管理员）
@see RoleCategories
   */
  roleCategoryList?: Array<number>
  /**
   * 排除的角色类别集合
（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管 10:人社超管
11:人社职建处 12:人社就业局 13:超级管理员 14:合同供应商 15:专家 16:电子劳动合同业务角色 320:地区管理员 410:人社行业管理员
411:人社行业省级管理员 412:人社行业市级管理员 413:人社行业区县级管理员 420:人社地区管理员 430:人社业务管理员 440:省级人社主管 450:市级人社主管
460:区县级人社主管 510:培训机构管理员 520:技工院校管理员 530:职业院校管理员 540:政策参与者 550:线上培训机构管理员 560:课件供应商
5001:学徒制_企业管理员 5101:学徒制_培训机构管理员 5201:学徒制_技工院校管理员 5301:学徒制_职业院校管理员 4011:学徒制_人社_省级管理员
4021:学徒制_人社_市级管理员 4031:学徒制_人社_区/县级管理员 6001:揭榜挂帅_企业管理员 6101:揭榜挂帅_培训机构管理员
6201:揭榜挂帅_技工院校管理员 6301:揭榜挂帅_职业院校管理员 6401:揭榜挂帅_人社_省级管理员 6402:揭榜挂帅_人社_市级管理员 6403:揭榜挂帅_人社_区/县级管理员）
@see RoleCategories
   */
  excludeRoleCategoryList?: Array<number>
  /**
   * 角色应用方ID
   */
  applicationMemberIdList?: Array<string>
}

/**
 * 功能描述：管理员查询条件
@Author： wtl
@Date： 2022年1月25日 15:24:10
 */
export class AdminQueryRequest {
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: AdminUserRequest
  /**
   * 登录认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 角色信息查询
   */
  role?: RoleRequest
  /**
   * 排序
   */
  sortList?: Array<AdminSortRequest>
}

/**
 * 功能描述：管理员排序
@Author： wtl
@Date： 2021/12/27 10:32
 */
export class AdminSortRequest {
  /**
   * 管理员排序字段
   */
  sortField?: EnterprisePersonSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 功能描述：管理员查询条件
@Author： wtl
@Date： 2022年1月25日 15:24:10
 */
export class AdminUserRequest {
  /**
   * 管理地区路径集合（模糊，右like）
   */
  manageRegionPathList?: Array<string>
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  userNameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  phoneMatchType?: number
}

/**
 * 功能描述 : 企业单位管理员查询条件
@date : 2022/6/17 17:32
 */
export class EnterpriseUnitAdminQueryRequest {
  /**
   * 企业单位管理员归属信息
   */
  owner?: EnterpriseUnitAdminOwnerRequest
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: AdminUserRequest
  /**
   * 登录认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 角色信息查询
   */
  role?: RoleRequest
  /**
   * 排序
   */
  sortList?: Array<AdminSortRequest>
}

/**
 * 功能描述 : 政府单位管理员查询条件
@date : 2022/6/17 17:32
 */
export class GovernmentUnitAdminQueryRequest {
  /**
   * 政府单位管理员归属信息
   */
  owner?: GovernmentUnitAdminOwnerRequest
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: AdminUserRequest
  /**
   * 登录认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 角色信息查询
   */
  role?: RoleRequest
  /**
   * 排序
   */
  sortList?: Array<AdminSortRequest>
}

/**
 * 功能描述 : 企业单位管理员归属查询条件
@date : 2022/6/17 17:42
 */
export class EnterpriseUnitAdminOwnerRequest {
  /**
   * 企业单位id路径集合
String：&quot;/福建电信id/福州电信分公司id&quot;
   */
  enterpriseUnitIdPathList?: Array<string>
  /**
   * 企业单位id路径匹配方式，默认为右模糊查询(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  enterpriseUnitIdPathMatchType?: number
}

/**
 * 功能描述 : 政府单位管理员归属查询条件
@date : 2022/6/17 17:42
 */
export class GovernmentUnitAdminOwnerRequest {
  /**
   * 政府单位id路径集合
String：&quot;/福建省人社id/福州市人社id&quot;
   */
  governmentUnitIdPathList?: Array<string>
  /**
   * 政府单位id路径匹配方式，默认为右模糊查询(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  governmentUnitIdPathMatchType?: number
}

/**
 * @Description 银行查询
<AUTHOR>
@Date 2022/9/28 11:14
@Version 1.0
 */
export class BankRequest {
  /**
   * 银行编码集合
   */
  codeList?: Array<string>
  /**
   * 银行id集合
   */
  idList?: Array<string>
  /**
   * 父级id
   */
  parentId?: string
  /**
   * 银行类型|对应常量BankTypeConstant
工商银行:ICBC_BANK
@see BankTypeConstant
   */
  bankType?: string
}

/**
 * @Description 获取单个业务数据字典信息的查询
<AUTHOR>
@Date 2022/9/27 9:33
@Version 1.0
 */
export class BusinessDataDictionaryCodeRequest {
  /**
   * 字典编码
   */
  code?: number
  /**
   * 业务数据字典类型（必填项）
DEGREE(&quot;学位&quot;, &quot;DEGREE&quot;),
EDUCATION_BACKGROUND(&quot;学历&quot;, &quot;EDUCATION_BACKGROUND&quot;),
ENTERPRISE_ECONOMIC_TYPE(&quot;企业经济类型&quot;, &quot;ENTERPRISE_ECONOMIC_TYPE&quot;),
ENTERPRISE_TYPE(&quot;企业类型&quot;, &quot;ENTERPRISE_TYPE&quot;),
EXECUTIVES_UNIT_TYPE(&quot;主管单位类型&quot;, &quot;EXECUTIVES_UNIT_TYPE&quot;),
GENDER(&quot;性别&quot;, &quot;GENDER&quot;),
HOUSEHOLD_REGISTRATION_TYPE(&quot;户口性质&quot;, &quot;HOUSEHOLD_REGISTRATION_TYPE&quot;),
ID_CARD_TYPE(&quot;证件类型&quot;, &quot;ID_CARD_TYPE&quot;),
INDUSTRY_EXECUTIVES_TYPE(&quot;行业主管类型&quot;, &quot;INDUSTRY_EXECUTIVES_TYPE&quot;),
INDUSTRY_TYPE(&quot;行业类型&quot;, &quot;INDUSTRY_TYPE&quot;),
NATIONALITY(&quot;民族&quot;, &quot;NATIONALITY&quot;),
PERSON_TYPE(&quot;人员类型&quot;, &quot;PERSON_TYPE&quot;),
PERSON_TYPE_GROUP(&quot;人员类型分组&quot;, &quot;PERSON_TYPE_GROUP&quot;),
POLITICS_STATUS(&quot;政治面貌&quot;, &quot;POLITICS_STATUS&quot;),
USER_REGISTER_TYPE(&quot;注册方式&quot;, &quot;USER_REGISTER_TYPE&quot;),
USER_SOURCE_TYPE(&quot;注册来源&quot;, &quot;USER_SOURCE_TYPE&quot;);
@see BusinessDataDictionaryTypeEnum
   */
  businessDataDictionaryType?: string
}

/**
 * 业务数据字典查询
 */
export class BusinessDataDictionaryRequest {
  /**
   * 字典编码
   */
  code?: number
  /**
   * 业务数据字典id集合
   */
  idList?: Array<string>
  /**
   * 父级业务数据字典id
   */
  parentId?: string
  /**
   * 业务数据字典类型（必填项）
DEGREE(&quot;学位&quot;, &quot;DEGREE&quot;),
EDUCATION_BACKGROUND(&quot;学历&quot;, &quot;EDUCATION_BACKGROUND&quot;),
ENTERPRISE_ECONOMIC_TYPE(&quot;企业经济类型&quot;, &quot;ENTERPRISE_ECONOMIC_TYPE&quot;),
ENTERPRISE_TYPE(&quot;企业类型&quot;, &quot;ENTERPRISE_TYPE&quot;),
EXECUTIVES_UNIT_TYPE(&quot;主管单位类型&quot;, &quot;EXECUTIVES_UNIT_TYPE&quot;),
GENDER(&quot;性别&quot;, &quot;GENDER&quot;),
HOUSEHOLD_REGISTRATION_TYPE(&quot;户口性质&quot;, &quot;HOUSEHOLD_REGISTRATION_TYPE&quot;),
ID_CARD_TYPE(&quot;证件类型&quot;, &quot;ID_CARD_TYPE&quot;),
INDUSTRY_EXECUTIVES_TYPE(&quot;行业主管类型&quot;, &quot;INDUSTRY_EXECUTIVES_TYPE&quot;),
INDUSTRY_TYPE(&quot;行业类型&quot;, &quot;INDUSTRY_TYPE&quot;),
NATIONALITY(&quot;民族&quot;, &quot;NATIONALITY&quot;),
PERSON_TYPE(&quot;人员类型&quot;, &quot;PERSON_TYPE&quot;),
PERSON_TYPE_GROUP(&quot;人员类型分组&quot;, &quot;PERSON_TYPE_GROUP&quot;),
POLITICS_STATUS(&quot;政治面貌&quot;, &quot;POLITICS_STATUS&quot;),
USER_REGISTER_TYPE(&quot;注册方式&quot;, &quot;USER_REGISTER_TYPE&quot;),
USER_SOURCE_TYPE(&quot;注册来源&quot;, &quot;USER_SOURCE_TYPE&quot;);
@see BusinessDataDictionaryTypeEnum
   */
  businessDataDictionaryType?: string
  /**
   * 业务数据字典业务配置id
PERSON_TYPE_EMPLOYED_PERSON(&quot;就业人员&quot;, &quot;PERSON_TYPE_EMPLOYED_PERSON&quot;),
PERSON_TYPE_APPRENTICESHIP_SUBDIVISION_ENTERPRISE_WORKERS(&quot;学徒制细分企业职工&quot;, &quot;PERSON_TYPE_APPRENTICESHIP_SUBDIVISION_ENTERPRISE_WORKERS&quot;),
PERSON_TYPE_UNEMPLOYED_PERSON(&quot;失业人员&quot;, &quot;PERSON_TYPE_UNEMPLOYED_PERSON&quot;),
PERSON_TYPE_GRADUATE(&quot;毕业生&quot;, &quot;PERSON_TYPE_GRADUATE&quot;),
PERSON_TYPE_OVERCOME_POVERTY_PERSON(&quot;脱贫人员&quot;, &quot;PERSON_TYPE_OVERCOME_POVERTY_PERSON&quot;),
PERSON_TYPE_ELSE(&quot;其他&quot;, &quot;PERSON_TYPE_ELSE&quot;);
   */
  businessId?: string
}

/**
 * @Description 地区code集合查询条件
<AUTHOR>
@Date 2022/9/27 10:57
@Version 1.0
 */
export class RegionCodeListRequest {
  /**
   * 地区字典业务配置id
   */
  businessId?: string
  /**
   * 地区编码集合
   */
  codeList?: Array<string>
}

/**
 * @Description 地区code查询
<AUTHOR>
@Date 2022/9/27 10:44
@Version 1.0
 */
export class RegionCodeRequest {
  /**
   * 地区字典业务配置id
   */
  businessId?: string
  /**
   * 地区编码
   */
  code?: string
}

export class RegionListRequest {
  /**
   * 地区字典业务配置id
   */
  businessId?: string
  /**
   * 地区路径
   */
  codePath?: string
  /**
   * 地区编码路径查询匹配模式 0:全匹配 1:模糊查询 2:左模糊 3:右模糊, 默认全等
   */
  codeMatchType?: number
}

/**
 * 地区查询
 */
export class RegionRequest {
  /**
   * 地区字典业务配置id
   */
  businessId?: string
  /**
   * 地区编码
   */
  code?: string
  /**
   * 级别|1省级 2市级 3区县级
   */
  level?: number
}

/**
 * 简略资讯查询条件
 */
export class NewsCompleteQueryRequest {
  /**
   * 资讯标题
   */
  title?: string
  /**
   * 资讯分类编号
   */
  necId?: string
  /**
   * 顶级分类代码
   */
  rootCategoryCode?: string
  /**
   * 弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]
   */
  popUpsStatus: number
  /**
   * 资讯状态 [-1-全部，0-草稿，1-发布 默认全部]
   */
  status: number
}

/**
 * 简略资讯查询条件
 */
export class NewsSimpleQueryRequest {
  /**
   * 资讯标题
   */
  title?: string
  /**
   * 资讯分类编号
   */
  necId?: string
  /**
   * 发布地区编码
   */
  areaCodePath?: string
  /**
   * 弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]
   */
  popUpsStatus: number
  /**
   * 资讯状态 [-1-全部，0-草稿，1-发布 默认全部]
   */
  status: number
  /**
   * 排序
默认按照从置顶到非置顶，发布时间从新到旧顺序，发布地区编码降序排列
   */
  order?: Array<OrderRequest>
}

export class OrderRequest {
  /**
   * 排序字段
发布时间：0
浏览数量：1
置顶(0-不置顶 1-置顶)：   2
发布地区编码: 3
   */
  orderField?: number
  /**
   * 排序方式  0 升序   1 降序
   */
  orderType?: number
}

/**
 * 功能描述：学员集体缴费信息
@Author： wtl
@Date： 2022年4月21日 08:58:49
 */
export class CollectiveRequest {
  /**
   * 集体缴费管理员用户id集合
   */
  collectiveUserIdList?: Array<string>
}

/**
 * 功能描述：学员查询条件
@Author： wtl
@Date： 2022年1月26日 10:10:33
 */
export class UserInfoRequest {
  /**
   * 工作单位名称（模糊）
   */
  companyName?: string
  /**
   * 单位所属地区路径集合（模糊，右like）
   */
  regionPathList?: Array<string>
  /**
   * 单位所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  regionPathListMatchType?: number
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  userNameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  phoneMatchType?: number
}

/**
 * 功能描述：学员查询条件
@Author： wtl
@Date： 2022年1月26日 10:10:33
 */
export class UserQueryRequest {
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: UserInfoRequest
  /**
   * 用户认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 集体缴费信息
   */
  collective?: CollectiveRequest
  /**
   * 排序
   */
  sortList?: Array<UserSortRequest>
}

/**
 * 功能描述 : 学员排序参数
@date : 2022/4/1 17:15
 */
export class UserSortRequest {
  /**
   * 排序字段
   */
  sortField?: PersonAccountSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 时间纬度下单位数量统计查询条件
@date 2022-07-27
 */
export class EnterpriseUnitDateHistogramRequest {
  /**
   * 时间纬度查询条件
   */
  dateHistogram?: DateHistogramRequest
  /**
   * 企业单位基本信息
   */
  unitBase?: EnterpriseUnitBaseRequest
}

/**
 * 功能描述：企业单位查询条件
@Author： wtl
@Date： 2022年6月9日 11:56:47
 */
export class EnterpriseUnitRequest {
  /**
   * 企业单位基本信息
   */
  unitBase?: EnterpriseUnitBaseRequest
  /**
   * 排序集合
   */
  sortList?: Array<EnterpriseUnitSortRequest>
}

/**
 * 企业单位统计查询条件
<AUTHOR>
@date 2022-07-23
 */
export class EnterpriseUnitStatisticRequest {
  /**
   * 企业单位基本信息
   */
  unitBase?: EnterpriseUnitBaseRequest
}

/**
 * 功能描述：政府单位查询条件
@Author： wtl
@Date： 2022年6月9日 10:24:09
 */
export class GovernmentUnitRequest {
  /**
   * 基本归属信息
   */
  owner?: OwnerRequest
  /**
   * 单位业务归属信息
   */
  businessOwner?: GovernmentUnitOwnerRequest
  /**
   * 单位基本信息
   */
  unitBase?: GovernmentUnitBaseRequest
  /**
   * 排序集合
   */
  sortList?: Array<GovernmentUnitSortRequest>
}

/**
 * 功能描述：评价机构查询条件
@Author： wtl
@Date： 2022年6月9日 11:56:47
 */
export class RatingAgenciesUnitRequest {
  /**
   * 企业单位基本信息
   */
  unitBase?: RatingAgenciesUnitBaseRequest
}

/**
 * 功能描述：企业单位基本查询条件
@Author： wtl
@Date： 2022年6月9日 10:24:09
 */
export class EnterpriseUnitBaseRequest {
  /**
   * 单位id集合
   */
  unitIdList?: Array<string>
  /**
   * 单位名称
   */
  unitName?: string
  /**
   * 单位名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  unitNameMatchType?: number
  /**
   * 单位简称
   */
  shortName?: string
  /**
   * 单位简称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  shortNameMatchType?: number
  /**
   * 统一社会信用代码
   */
  code?: string
  /**
   * 单位业务类型
说明：
1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
11地区管理单位,12行业主管单位,13技工院校,14职业院校.15线上培训机构,10000实名制报表补贴单位,10001评价机构
@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
@see UnitBusinessQueryTypes
   */
  businessTypeList?: Array<number>
  /**
   * 单位类型id路径集合
   */
  unitTypeIdPathList?: Array<string>
  /**
   * 单位类型id路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*unitTypeIdPathList* 2：左模糊查询，*unitTypeIdPathList 3:右模糊查询，unitTypeIdPathList*)
@see MatchTypeConstant
   */
  unitTypeIdPathListMatchType?: number
  /**
   * 单位地区路径集合
   */
  regionPathList?: Array<string>
  /**
   * 单位地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  regionPathListMatchType?: number
  /**
   * 单位创建时间范围
   */
  createdDateScope?: DateScopeRequest
  /**
   * 企业法人
   */
  legalPerson?: LegalPersonRequest
  /**
   * 单位状态
说明：1正常,2冻结
   */
  statusList?: Array<number>
}

/**
 * 企业单位排序
<AUTHOR>
@date 2022-06-18
 */
export class EnterpriseUnitSortRequest {
  /**
   * 排序字段
   */
  sortField?: EnterpriseUnitSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 功能描述：政府单位基本查询条件
@Author： wtl
@Date： 2022年6月9日 10:24:09
 */
export class GovernmentUnitBaseRequest {
  /**
   * 单位id集合
   */
  unitIdList?: Array<string>
  /**
   * 单位名称
   */
  unitName?: string
  /**
   * 单位名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  unitNameMatchType?: number
  /**
   * 单位简称
   */
  shortName?: string
  /**
   * 单位简称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  shortNameMatchType?: number
  /**
   * 单位业务类型
说明：
1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
11地区管理单位,12行业主管单位
@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
   */
  businessTypeList?: Array<number>
  /**
   * 所属地区路径（行政区划）
   */
  regionPathList?: Array<string>
  /**
   * 所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  regionPathListMatchType?: number
  /**
   * 管辖地区路径
   */
  manageRegionPathList?: Array<string>
  /**
   * 管辖地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  manageRegionPathListMatchType?: number
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest
  /**
   * 单位状态
说明：1正常,2冻结
   */
  statusList?: Array<number>
}

/**
 * 功能描述：政府单位归属查询条件
@Author： wtl
@Date： 2022年6月9日 12:08:14
 */
export class GovernmentUnitOwnerRequest {
  /**
   * 单位id路径集合
   */
  unitIdPathList?: Array<string>
  /**
   * 单位id路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  unitIdPathMatchType?: number
}

/**
 * 政府单位排序
<AUTHOR>
@date 2022-06-18
 */
export class GovernmentUnitSortRequest {
  /**
   * 排序字段
   */
  sortField?: GovernmentUnitSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 功能描述：企业法人查询条件
@Author： wtl
@Date： 2022年6月9日 12:08:14
 */
export class LegalPersonRequest {
  /**
   * 法人姓名
   */
  name?: string
  /**
   * 法人姓名匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  nameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 证件号匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  idCardMatchType?: number
}

/**
 * 基本归属信息
 */
export class OwnerRequest {
  /**
   * 所属平台ID
   */
  platformId?: string
  /**
   * 所属平台版本ID
   */
  platformVersionId?: string
  /**
   * 所属项目ID
   */
  projectId?: string
  /**
   * 所属子项目ID
   */
  subProjectId?: string
}

/**
 * 功能描述：企业单位基本查询条件
@Author： wtl
@Date： 2022年6月9日 10:24:09
 */
export class RatingAgenciesUnitBaseRequest {
  /**
   * 单位id集合
   */
  unitIdList?: Array<string>
  /**
   * 单位名称
   */
  unitName?: string
  /**
   * 单位名称匹配方式，默认为equals(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  unitNameMatchType?: number
  /**
   * 单位简称
   */
  shortName?: string
  /**
   * 单位简称匹配方式，默认为equals(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  shortNameMatchType?: number
  /**
   * 统一社会信用代码
   */
  code?: string
  /**
   * 单位地区路径集合
   */
  regionPathList?: Array<string>
  /**
   * 单位地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  regionPathListMatchType?: number
  /**
   * 单位创建时间范围
   */
  createdDateScope?: DateScopeRequest
  /**
   * 评价机构类型
SOCIAL_EVALUATION_ORGANIZATION:社会评价组织机构
EMPLOYER:用人单位
@see RatingAgenciesType
   */
  ratingAgenciesTypeList?: Array<string>
}

export class DateScopeRequest {
  beginTime?: string
  endTime?: string
}

export class DateHistogramItemModel {
  date: string
  count: number
}

export class RegionModel {
  regionId: string
  regionPath: string
  provinceId: string
  provinceName: string
  cityId: string
  cityName: string
  countyId: string
  countyName: string
}

/**
 * 功能描述：账户信息
@Author： wtl
@Date： 2022年5月11日 15:30:56
 */
export class AccountResponse {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
@see AccountTypes
   */
  accountType: number
  /**
   * 单位信息
   */
  unitInfo: UnitInfoResponse
  /**
   * 所属顶级企业帐户Id
   */
  rootAccountId: string
  /**
   * 帐户状态 1：正常，2：冻结，3：注销
@see AccountStatus
   */
  status: number
  /**
   * 注册方式
0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
@see AccountRegisterTypes
   */
  registerType: number
  /**
   * 来源类型
0内置，1项目主网站，2安卓，3IOS
@see AccountSourceTypes
   */
  sourceType: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 最后更新时间
   */
  lastUpdateTime: string
}

/**
 * 功能描述：帐户认证信息
@Author： wtl
@Date： 2022年5月11日 14:23:18
 */
export class AuthenticationResponse {
  /**
   * 帐号
   */
  identity: string
  /**
   * 认证标识类型
1用户名,2手机,3身份证,4邮箱,5第三方OpenId
   */
  identityType: number
  /**
   * 认证方式状态 1启用，2禁用
@see AuthenticationStatusEnum
   */
  status: number
}

/**
 * 功能描述：时间直方图统计结果
@Author： wtl
@Date： 2021/12/30 9:58
 */
export class DateHistogramResponse {
  /**
   * 时间单位
   */
  timeUnit: TimeUnitEnum
  /**
   * 统计结果元素
   */
  histogram: Array<DateHistogramItemModel>
  /**
   * 总计
   */
  totalCount: number
}

/**
 * 功能描述：角色信息
@Author： wtl
@Date： 2022/1/24 20:17
 */
export class RoleResponse {
  /**
   * 角色id
   */
  roleId: string
  /**
   * 角色名称
   */
  roleName: string
  /**
   * 角色类型
（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
   */
  roleType: string
  /**
   * 角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
@see RoleCategories
   */
  roleCategory: number
}

/**
 * 功能描述 : 企业单位管理员信息
@date : 2022/6/18 12:24
 */
export class EnterpriseUnitAdminInfoResponse {
  /**
   * 企业单位管理员归属信息
   */
  enterpriseUnitAdminOwner: EnterpriseUnitAdminOwnerResponse
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 管理员用户信息
   */
  userInfo: AdminUserInfoResponse
  /**
   * 人员信息
   */
  personInfo: EnterpriseUnitPersonInfoResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
  /**
   * 角色信息集合
   */
  roleList: Array<RoleResponse>
}

/**
 * 功能描述 : 政府单位管理员信息
@date : 2022/6/18 12:24
 */
export class GovernmentUnitAdminInfoResponse {
  /**
   * 政府单位管理员归属信息
   */
  governmentUnitAdminOwner: GovernmentUnitAdminOwnerResponse
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 管理员用户信息
   */
  userInfo: AdminUserInfoResponse
  /**
   * 人员信息
   */
  personInfo: PersonInfoBasicResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
  /**
   * 角色信息集合
   */
  roleList: Array<RoleResponse>
}

/**
 * 功能描述：管理员用户信息
@Author： wtl
@Date： 2022年1月25日 15:48:48
 */
export class AdminUserInfoResponse {
  /**
   * 管辖地区集合
   */
  manageRegionList: Array<RegionModel>
  /**
   * 办公室（所在处/科室）
   */
  office: string
  /**
   * 岗位/职位
   */
  position: string
  /**
   * 备注
   */
  remark: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 性别
-1未知，0女，1男
@see Genders
   */
  gender: number
  /**
   * 证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string
}

/**
 * 功能描述 : 企业单位管理员归属查询条件
@date : 2022年9月2日 10:53:02
 */
export class EnterpriseUnitAdminOwnerResponse {
  /**
   * 企业单位id
   */
  enterpriseUnitIdList: Array<string>
}

/**
 * 人员信息模型
 */
export class EnterpriseUnitPersonInfoResponse {
  /**
   * 是否法人帐号
   */
  isCorporateAccount: boolean
  /**
   * 人员实名认证信息
   */
  personIdentityVerificationInfo: PersonIdentityVerificationResponse
}

/**
 * 功能描述 : 政府单位管理员归属查询条件
@date : 2022年8月30日 08:41:13
 */
export class GovernmentUnitAdminOwnerResponse {
  /**
   * 政府单位id
   */
  governmentUnitIdList: Array<string>
}

/**
 * 人员实名认证信息模型
 */
export class PersonIdentityVerificationResponse {
  /**
   * 是否已认证
   */
  identityVerification: boolean
  /**
   * 认证渠道(1:闽政通 2：腾讯)
   */
  identityVerificationChannel: number
  /**
   * 认证时间
   */
  identityVerificationTime: string
}

/**
 * 人员信息模型
 */
export class PersonInfoBasicResponse {
  /**
   * 人员实名认证信息
   */
  personIdentityVerificationInfo: PersonIdentityVerificationResponse
}

/**
 * 单位信息模型
 */
export class UnitInfoResponse {
  /**
   * 单位ID
   */
  unitId: string
}

/**
 * @Description 银行字典信息
<AUTHOR>
@Date 2022/9/28 11:57
@Version 1.0
 */
export class BankResponse {
  /**
   * 银行id
   */
  id: string
  /**
   * 银行类型
   */
  type: string
  /**
   * 父级ID
   */
  parentId: string
  /**
   * 银行编码
   */
  code: string
  /**
   * 银行名称
   */
  name: string
  /**
   * 是否可用（0：禁用 1：启用）
   */
  available: number
  /**
   * 排序
   */
  sort: number
}

/**
 * 功能描述：业务数据字典信息
@Author： wtl
@Date： 2022年8月12日 17:25:23
 */
export class BusinessDataDictionaryResponse {
  /**
   * 字典id
   */
  id: string
  /**
   * 字典类型
   */
  type: string
  /**
   * 父级字典id
   */
  parentId: string
  /**
   * 字典编码
   */
  code: number
  /**
   * 字典名称
   */
  name: string
  /**
   * 是否可用（0：禁用 1：启用）
   */
  available: number
  /**
   * 排序
   */
  sort: number
}

/**
 * 功能描述：地区字典信息
@Author： wtl
@Date： 2022年8月12日 11:54:38
 */
export class RegionResponse {
  /**
   * 地区编码
   */
  code: string
  /**
   * 父级地区编码
   */
  parentCode: string
  /**
   * 地区编码路径
   */
  codePath: string
  /**
   * 地区名称
   */
  name: string
  /**
   * 级别|1省级 2市级 3区县级
   */
  level: number
  /**
   * 地区排序
   */
  sort: number
}

/**
 * 资讯分类信息
 */
export class NewsCategoryResponse {
  /**
   * 资讯分类编号
   */
  newsCategoryId: string
  /**
   * 分类名称
   */
  categoryName: string
  /**
   * 分类代码
   */
  code: string
}

/**
 * 简略资讯信息
 */
export class NewsCompleteResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 所属业务平台
   */
  businessPlatform: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 资讯状态 0 草稿 1正常
   */
  status: number
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 分类id
   */
  necId: string
}

/**
 * 详细资讯信息
 */
export class NewsDetailResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 平台id
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目id
   */
  projectId: string
  /**
   * 子项目id
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商id
   */
  serviceId: string
  /**
   * 分类id
   */
  necId: string
  /**
   * 标题
   */
  title: string
  /**
   * 摘要
   */
  summary: string
  /**
   * 内容
   */
  content: string
  /**
   * 封面图片路径
   */
  coverPath: string
  /**
   * 来源
   */
  source: string
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 发布地区编码
   */
  areaCodePath: string
  /**
   * 资讯状态 0 草稿 1正常
   */
  status: number
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 浏览数量
   */
  reviewCount: number
  /**
   * 弹窗起始时间
   */
  popupBeginTime: string
  /**
   * 弹窗截止时间
   */
  popupEndTime: string
}

/**
 * 简略资讯信息
 */
export class NewsSimpleResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 摘要
   */
  summary: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 资讯状态 0 草稿 1正常
   */
  status: number
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 发布地区编码
   */
  areaCodePath: string
}

/**
 * 功能描述：学员信息
@Author： wtl
@Date： 2022年1月26日 10:38:15
 */
export class UserInfoResponse {
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 用户信息
   */
  userInfo: UserInformationResponse
  /**
   * 人员信息
   */
  personInfo: PersonInfoResponse
  /**
   * 第三方绑定信息
   */
  openPlatformBind: OpenPlatformBindResponse
}

/**
 * 功能描述：附件信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class AttachmentInfoResponse {
  /**
   * 附件名称
   */
  name: string
  /**
   * 附件地址
   */
  url: string
}

/**
 * 功能描述：学员绑定信息
@Author： wtl
@Date： 2022年5月12日 14:42:51
 */
export class OpenPlatformBindResponse {
  /**
   * 是否绑定微信
   */
  bindWX: boolean
  /**
   * 微信昵称
   */
  nickNameByWX: string
}

export class PersonInfoResponse {
  /**
   * 人员ID
   */
  personId: string
  /**
   * 姓名
   */
  name: string
  /**
   * 证件类型
@see IdCardTypes
   */
  idCardType: number
  /**
   * 身份证号
   */
  idCard: string
  /**
   * 性别
@see Genders
   */
  gender: number
  /**
   * 出生日期
   */
  birthday: string
  /**
   * 民族（字典）
   */
  ethnicity: string
  /**
   * 居住地区
   */
  resideCityArea: RegionResponse1
  /**
   * 联系地址
   */
  address: string
  /**
   * 手机号
   */
  phone: string
}

export class RegionResponse1 {
  /**
   * 地区ID
   */
  regionId: string
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 省份ID
   */
  provinceId: string
  /**
   * 省份名称
   */
  provinceName: string
  /**
   * 地市ID
   */
  cityId: string
  /**
   * 地市名称
   */
  cityName: string
  /**
   * 区县ID
   */
  countyId: string
  /**
   * 区县名称
   */
  countyName: string
}

/**
 * 功能描述：学员证书信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class StudentCertificateResponse {
  /**
   * 证书id
   */
  certificateId: string
  /**
   * 证书编号
   */
  certificateNo: string
  /**
   * 证书类别
   */
  certificateCategory: string
  /**
   * 注册专业
   */
  registerProfessional: string
  /**
   * 发证日期
   */
  releaseStartTime: string
  /**
   * 证书有效期
   */
  certificateEndTime: string
  /**
   * 证书附件信息
   */
  attachmentList: Array<AttachmentInfoResponse>
}

/**
 * 功能描述：学员行业信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class StudentIndustryResponse {
  /**
   * 用户行业id
   */
  userIndustryId: string
  /**
   * 行业id
   */
  industryId: string
  /**
   * 一级专业类别id
   */
  firstProfessionalCategory: string
  /**
   * 二级专业类别id
   */
  secondProfessionalCategory: string
  /**
   * 职称等级
   */
  professionalQualification: string
  /**
   * 学员证书信息集合
   */
  userCertificateList: Array<StudentCertificateResponse>
}

/**
 * 功能描述：用户信息
@Author： wtl
@Date： 2022年1月26日 10:30:20
 */
export class UserInformationResponse {
  /**
   * 用户昵称
   */
  nickName: string
  /**
   * 单位所属地区
   */
  region: RegionModel
  /**
   * 工作单位名称
   */
  companyName: string
  /**
   * 头像地址
   */
  photo: string
  /**
   * 联系地址
   */
  address: string
  /**
   * 学员行业信息集合
   */
  userIndustryList: Array<StudentIndustryResponse>
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 性别
-1未知，0女，1男
@see Genders
   */
  gender: number
  /**
   * 证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string
}

/**
 * 行业信息下-企业单位统计
<AUTHOR>
@date 2022-07-25
 */
export class EnterpriseUnitIndustryStatisticResponse {
  /**
   * 行业类型id，可能是门类id、大类id、中类id、小类id
   */
  industryId: string
  /**
   * 企业单位统计结果
   */
  statisticInfo: EnterpriseUnitStatisticResponse
}

/**
 * 功能描述：企业单位信息
@Author： wtl
@Date： 2022年6月9日 14:20:55
 */
export class EnterpriseUnitInfoResponse {
  /**
   * 企业单位业务归属信息
   */
  businessOwnerInfo: EnterpriseUnitBusinessOwnerResponse
  /**
   * 单位基本信息
   */
  unitBase: EnterpriseUnitBaseResponse
  /**
   * 经营信息
   */
  businessInfo: BusinessInfoResponse
  /**
   * 单位认证信息
   */
  unitIdentityVerificationInfo: UnitIdentityVerificationResponse
}

/**
 * 行业信息下-企业单位统计
<AUTHOR>
@date 2022-07-25
 */
export class EnterpriseUnitRegionStatisticResponse {
  /**
   * 注册地区
   */
  region: RegionModel
  /**
   * 企业单位统计结果
   */
  statisticInfo: EnterpriseUnitStatisticResponse
}

/**
 * 单位类型下-企业单位统计
<AUTHOR>
@date 2022-07-25
 */
export class EnterpriseUnitTypeStatisticResponse {
  /**
   * 单位类型id,可能是一级id、二级id、三级id
   */
  unitTypeId: string
  /**
   * 企业单位统计结果
   */
  statisticInfo: EnterpriseUnitStatisticResponse
}

/**
 * 功能描述：政府单位信息
@Author： wtl
@Date： 2022年6月9日 10:41:26
 */
export class GovernmentUnitInfoResponse {
  /**
   * 单位归属信息
   */
  ownerInfo: GovernmentUnitOwnerResponse
  /**
   * 单位业务归属信息
   */
  businessOwnerInfo: GovernmentUnitBusinessOwnerResponse
  /**
   * 政府单位基本信息
   */
  unitBase: GovernmentUnitBaseResponse
  /**
   * 政策许可证（业务模块）
   */
  licenseIssuedList: Array<GovernmentUnitLicenseIssuedResponse>
}

/**
 * 功能描述：评价机构信息
@Author： wtl
@Date： 2022年6月9日 14:20:55
 */
export class RatingAgenciesUnitInfoResponse {
  /**
   * 单位基本信息
   */
  unitBase: RatingAgenciesUnitBaseResponse
}

/**
 * 功能描述：附件信息
 */
export class AttachmentResponse {
  /**
   * 附件id
   */
  attachmentId: string
  /**
   * 附件类型
1《营业执照 / 民办法人登记证书/非民办企业法人登记证书》
2《开展短期职业培训承诺书》（加盖公司公章）》
3《培训单位基本信息表》（加盖公司公章）》
4《培训单位办学许可证》（加盖公司公章）》
5《年度年审合格证书》（加盖公司公章）》
6 开通依据
   */
  attachmentType: number
  /**
   * 附件名称
   */
  attachmentName: string
  /**
   * 附件路径
   */
  attachmentPath: string
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 功能描述：企业经营信息
@Author： wtl
@Date： 2022年6月9日 14:31:03
 */
export class BusinessInfoResponse {
  /**
   * 营业期限起始日期
   */
  operatingBeginDate: string
  /**
   * 营业期限截止日期
   */
  operatingEndDZhjyEnterpriseUnitBackStageQueryResolverate: string
  /**
   * 行业信息
   */
  industry: IndustryResponse
  /**
   * 经营范围,例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。
   */
  businessScope: string
  /**
   * 主营业务
例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。
   */
  mainBusiness: string
}

/**
 * 功能描述：单位联系人信息
 */
export class ContactPersonInfoResponse {
  /**
   * 联系人
   */
  contact: string
  /**
   * 联系电话
   */
  contactPhone: string
}

/**
 * 功能描述：企业单位信息
@Author： wtl
@Date： 2022年6月9日 14:23:04
 */
export class EnterpriseUnitBaseResponse {
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 单位英文名称
   */
  enName: string
  /**
   * 统一社会信用代码
   */
  code: string
  /**
   * 单位简称
   */
  shortName: string
  /**
   * 单位业务类型
说明：
1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
11地区管理单位,12行业主管单位,13技工院校,14职业院校.15线上培训机构,10000实名制报表补贴单位,10001评价机构
@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
@see UnitBusinessQueryTypes
   */
  businessType: number
  /**
   * logo
   */
  logo: string
  /**
   * 法人信息
   */
  legalPersonInfo: LegalPersonResponse
  /**
   * 单位类型（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）
   */
  unitType: UnitTypeResponse
  /**
   * 单位规模（1：微型 2：小型 3：中型 4：大型）
@see com.fjhb.domain.basicdata.api.unit.consts.UnitScales
   */
  scale: number
  /**
   * 企业经济类型(国有经济、联营经济、私营企业、股份制、港澳台投资、外商投资、其他经济)
@see com.fjhb.domain.basicdata.api.unit.consts.UnitEconomicTypes
   */
  economicTypes: number
  /**
   * 产业类别
   */
  industrialCategory: string
  /**
   * 成立日期
   */
  foundedDate: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 邮政编码
   */
  postCode: string
  /**
   * 传真
   */
  faxNumber: string
  /**
   * 注册地区
   */
  region: RegionModel
  /**
   * 联系地址
   */
  address: string
  /**
   * 注册地址
   */
  registerAddress: string
  /**
   * 登记机关
   */
  registeredOrgan: string
  /**
   * 注册资金
   */
  registeredCapital: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 单位状态
说明：1正常,2冻结
   */
  status: number
  /**
   * 工商注册号
   */
  businessRegistrationNumber: string
  /**
   * 纳税人资质
   */
  taxpayerQualification: string
  /**
   * 邮箱地址
   */
  emailAddress: string
  /**
   * 联系人信息
   */
  contactPersonInfo: ContactPersonInfoResponse
  /**
   * 单位资质附件类型
   */
  attachmentList: Array<AttachmentResponse>
}

/**
 * 企业单位业务归属信息
 */
export class EnterpriseUnitBusinessOwnerResponse {
  /**
   * 企业归属信息路径
单位路径（若单位为福州市企业，则该值为:&quot;/福建省企业id/福州市企业id&quot;）
   */
  unitIdPath: string
}

/**
 * 企业单位统计结果
<AUTHOR>
@date 2022-07-25
 */
export class EnterpriseUnitStatisticResponse {
  /**
   * 企业单位数量统计
   */
  enterpriseUnitCount: number
}

/**
 * 人社许可权限信息
 */
export class GovernmentUnitAuthorizationInfoResponse {
  /**
   * 权限描述
   */
  desc: string
  /**
   * 权限授予角色列表
   */
  authorizeRoles: Array<string>
}

/**
 * 功能描述：政府单位信息
@Author： wtl
@Date： 2022年6月9日 10:41:26
 */
export class GovernmentUnitBaseResponse {
  /**
   * 单位id
   */
  unitId: string
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 单位简称
   */
  shortName: string
  /**
   * 单位业务类型
说明：
1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
11地区管理单位,12行业主管单位
@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
   */
  businessType: number
  /**
   * 电话
   */
  phone: string
  /**
   * 传真
   */
  faxNumber: string
  /**
   * 所属地区
   */
  region: RegionModel
  /**
   * 地址
   */
  address: string
  /**
   * 管辖地区
   */
  manageRegion: RegionModel
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 单位状态
说明：1正常,2冻结
   */
  status: number
  /**
   * 单位类别（行业主管类型）
@see com.fjhb.domain.basicdata.api.unit.consts.UnitCategories
   */
  category: number
  /**
   * 单位资质附件类型
   */
  attachmentList: Array<AttachmentResponse>
}

/**
 * 功能描述：政府单位业务归属信息
@Author： wtl
@Date： 2022年6月24日 19:11:30
 */
export class GovernmentUnitBusinessOwnerResponse {
  /**
   * 单位路径（若单位为福州市人社，则该值为:&quot;/福建省人社id/福州市人社id&quot;）
   */
  unitIdPath: string
}

/**
 * 功能描述：政府部门政策许可证
 */
export class GovernmentUnitLicenseIssuedResponse {
  /**
   * 许可证编号Id
   */
  licenseId: string
  /**
   * 授予者id
   */
  authorizationId: string
  /**
   * 授予者类型
1单位
   */
  authorizationType: number
  /**
   * 单位业务类型
说明：
1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
11地区管理单位,12行业主管单位
@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
   */
  businessType: number
  /**
   * 政策类型(大类)
   */
  policyType: string
  /**
   * 颁发时间
   */
  issueTime: string
  /**
   * 人社许可权限信息
   */
  authorizationInfo: GovernmentUnitAuthorizationInfoResponse
  /**
   * 许可证状态
0已回收,1已颁发,2已失效
   */
  status: number
  /**
   * 状态变更时间
   */
  statusUpdateTime: string
}

/**
 * 功能描述：政府单位信息
@Author： wtl
@Date： 2022年6月9日 10:41:26
 */
export class GovernmentUnitOwnerResponse {
  /**
   * 上级单位id（若为顶级单位，该值为空）
   */
  parentUnitId: string
}

/**
 * 行业信息
<AUTHOR>
@date 2022-06-18
 */
export class IndustryResponse {
  /**
   * 行业信息ID路径
   */
  industryIdPath: string
  /**
   * 门类
   */
  firstLevelIndustryId: string
  /**
   * 大类
   */
  secondLevelIndustryId: string
  /**
   * 中类
   */
  thirdLevelIndustryId: string
  /**
   * 小类
   */
  fourthLevelIndustryId: string
}

/**
 * 功能描述：企业法人信息
@Author： wtl
@Date： 2022年6月9日 14:31:03
 */
export class LegalPersonResponse {
  /**
   * 法定代表人
   */
  legalPerson: string
  /**
   * 证件类型
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
}

/**
 * 功能描述：企业单位信息
@Author： sjm
@Date： 2023年5月8日 14:23:04
 */
export class RatingAgenciesUnitBaseResponse {
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 单位英文名称
   */
  enName: string
  /**
   * 统一社会信用代码
   */
  code: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 注册地区
   */
  region: RegionModel
  /**
   * 注册地址
   */
  registerAddress: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 单位状态
说明：1正常,2冻结
   */
  status: number
  /**
   * 工商注册号
   */
  businessRegistrationNumber: string
  /**
   * 评价机构类型
SOCIAL_EVALUATION_ORGANIZATION:社会评价组织机构
EMPLOYER:用人单位
@see RatingAgenciesType
   */
  ratingAgenciesType: string
  /**
   * 所属机构
   */
  belongInstitutions: string
}

/**
 * 单位认证信息模型
 */
export class UnitIdentityVerificationResponse {
  /**
   * 是否已认证
   */
  identityVerification: boolean
  /**
   * 认证渠道（单位认证渠道：UnitIdentityVerificationChannels 人员认证渠道：PersonIdentityVerificationChannels）
@see PersonIdentityVerificationChannels
@see UnitIdentityVerificationChannels
   */
  identityVerificationChannel: number
  /**
   * 认证时间
   */
  identityVerificationTime: string
}

/**
 * 单位类型:（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）
<AUTHOR>
@date : 2022/6/18 14:15
 */
export class UnitTypeResponse {
  /**
   * 单位类型ID路径
   */
  unitTypeIdPath: string
  /**
   * 一级
   */
  firstLevelUnitTypeId: string
  /**
   * 二级
   */
  secondLevelUnitTypeId: string
  /**
   * 三级
   */
  thirdLevelUnitTypeId: string
}

export class BankResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BankResponse>
}

export class BusinessDataDictionaryResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BusinessDataDictionaryResponse>
}

export class NewsCompleteResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<NewsCompleteResponse>
}

export class EnterpriseUnitAdminInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<EnterpriseUnitAdminInfoResponse>
}

export class EnterpriseUnitInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<EnterpriseUnitInfoResponse>
}

export class GovernmentUnitAdminInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<GovernmentUnitAdminInfoResponse>
}

export class GovernmentUnitInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<GovernmentUnitInfoResponse>
}

export class RatingAgenciesUnitInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<RatingAgenciesUnitInfoResponse>
}

export class NewsSimpleResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<NewsSimpleResponse>
}

export class UserInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserInfoResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 功能描述：项目级-根据用户id获取管理员信息-详细接口
   * @param userId                  :用户id
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.AdminInfoResponse
   * @date : 2022年10月11日 17:02:18
   * @param query 查询 graphql 语法文档
   * @param userId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getAdminInfoInSubProject(
    userId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<AdminUserInfoResponse>> {
    return commonRequestApi<AdminUserInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { userId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * @Description 项目级-根据银行编码查询单个银行字典信息-明细接口
   * 根据银行编码查询当前子项目下的银行信息
   * <AUTHOR>
   * @Date 2022/9/29 12:01
   * @param code    :银行编码
   * @return com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.BankResponse
   * @param query 查询 graphql 语法文档
   * @param code 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBankInSubProject(
    code: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBankInSubProject,
    operation?: string
  ): Promise<Response<BankResponse>> {
    return commonRequestApi<BankResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { code },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据code和业务数据字典类型查询单个业务数据字典信息接口-明细接口
   * 描述：根据code和业务数据字典类型查询当前子项目下的业务数据字典信息
   * @param request :业务数据字典查询条件
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.BusinessDataDictionaryResponse
   * @date : 2022年8月12日 15:56:50
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBusinessDataDictionaryInSubProject(
    request: BusinessDataDictionaryCodeRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBusinessDataDictionaryInSubProject,
    operation?: string
  ): Promise<Response<BusinessDataDictionaryResponse>> {
    return commonRequestApi<BusinessDataDictionaryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：企业-当前登录企业管理员信息
   * 描述：查询当前登录管理员的信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnterpriseUnitAdminInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitAdminInfoInMyself,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponse>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：项目级-企业单位管理员详情查询接口
   * 描述：查询当前企业单位下指定管理员的信息，如不存在返回null
   * @param accountId               : 帐户ID
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
   * @date : 2022年6月9日 16:50:10
   * @param query 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getEnterpriseUnitAdminInfoInSubProject(
    accountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponse>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 政府单位下-按条件统计企业单位总数量
   * @param request 企业单位统计查询条件
   * @return 统计数量结果
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnterpriseUnitCountInGovernmentUnit(
    request: EnterpriseUnitStatisticRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitCountInGovernmentUnit,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 子项目下-按条件统计企业单位总数量
   * @param request 企业单位统计查询条件
   * @return 统计数量结果
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getEnterpriseUnitCountInSubProject(
    request: EnterpriseUnitStatisticRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitCountInSubProject,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：企业单位-当前登录企业单位详情查询接口
   * 描述：查询当前企业单位信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse
   * @date : 2022年6月9日 10:49:47
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnterpriseUnitInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitInfoInMyself,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponse>> {
    return commonRequestApi<EnterpriseUnitInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：项目级-企业单位详情查询接口
   * 描述：查询当前子项目下指定企业单位的信息
   * @param unitId                  :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse
   * @date : 2022年6月9日 10:49:47
   * @param query 查询 graphql 语法文档
   * @param unitId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getEnterpriseUnitInfoInSubProject(
    unitId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitInfoInSubProject,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponse>> {
    return commonRequestApi<EnterpriseUnitInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { unitId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：政府单位-当前登录政府单位管理员信息
   * 描述：查询当前登录管理员的信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getGovernmentUnitAdminInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getGovernmentUnitAdminInfoInMyself,
    operation?: string
  ): Promise<Response<GovernmentUnitAdminInfoResponse>> {
    return commonRequestApi<GovernmentUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：政府单位管理员详情查询接口
   * 描述：查询当前政府单位下指定管理员的信息，如不存在返回null
   * @param accountId               : 帐户ID
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.GovernmentUnitAdminInfoResponse
   * @date : 2022年6月9日 16:50:10
   * @param query 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getGovernmentUnitAdminInfoInSubProject(
    accountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getGovernmentUnitAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<GovernmentUnitAdminInfoResponse>> {
    return commonRequestApi<GovernmentUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：政府单位-获取当前登录政府单位信息接口
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.GovernmentUnitInfoResponse
   * @date : 2022年6月9日 10:49:47
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getGovernmentUnitInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getGovernmentUnitInfoInMyself,
    operation?: string
  ): Promise<Response<GovernmentUnitInfoResponse>> {
    return commonRequestApi<GovernmentUnitInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：获取政府单位信息接口
   * 描述：查询当前子项目下指定政府单位的信息
   * @param unitId                  :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.GovernmentUnitInfoResponse
   * @date : 2022年6月9日 10:49:47
   * @param query 查询 graphql 语法文档
   * @param unitId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getGovernmentUnitInfoInSubProject(
    unitId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getGovernmentUnitInfoInSubProject,
    operation?: string
  ): Promise<Response<GovernmentUnitInfoResponse>> {
    return commonRequestApi<GovernmentUnitInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { unitId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据分类id获取顶级分类信息
   * @param rootCategoryCode 顶级分类代码
   * @param code 分类代码
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getNewsCategoryId(
    params: { rootCategoryCode: string; code: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getNewsCategoryId,
    operation?: string
  ): Promise<Response<NewsCategoryResponse>> {
    return commonRequestApi<NewsCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询详细资讯
   * @param newId 资讯id
   * @return 资讯信息
   * @param query 查询 graphql 语法文档
   * @param newId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getNewsDetail(
    newId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getNewsDetail,
    operation?: string
  ): Promise<Response<NewsDetailResponse>> {
    return commonRequestApi<NewsDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { newId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：项目级-根据地区编码查询单个地区字典信息-明细接口
   * 描述：根据地区编码查询当前子项目下的地区信息
   * @param request : 地区查询条件
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.RegionResponse
   * @date : 2022年8月12日 15:56:50
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getRegionInSubProject(
    request: RegionCodeRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRegionInSubProject,
    operation?: string
  ): Promise<Response<RegionResponse>> {
    return commonRequestApi<RegionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据项目id查询顶级资讯分类
   * @return 资讯分类信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRootNewsCategory(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRootNewsCategory,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据分类id获取顶级分类信息
   * @param necId 分类id
   * @return
   * @param query 查询 graphql 语法文档
   * @param necId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRootNewsCategoryById(
    necId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRootNewsCategoryById,
    operation?: string
  ): Promise<Response<NewsCategoryResponse>> {
    return commonRequestApi<NewsCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { necId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 :子项目-获取用户详细信息接口-详细接口
   * 描述：查询子项目下指定的用户信息，如不存在返回null
   * @param userId           :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.StudentInfoResponse
   * @date : 2022年11月9日 14:48:20
   * @param query 查询 graphql 语法文档
   * @param userId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getUserInSubProject(
    userId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getUserInSubProject,
    operation?: string
  ): Promise<Response<UserInfoResponse>> {
    return commonRequestApi<UserInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { userId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询指定业务数据字典类型的字典信息-列表接口
   * 描述：查询指定业务数据字典类型的字典列表，默认按排序字段升序排
   * @param request :业务数据字典查询条件
   * @return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.BusinessDataDictionaryResponse>
   * @date : 2022年8月12日 17:28:02
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessDataDictionaryInSubProject(
    request: BusinessDataDictionaryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessDataDictionaryInSubProject,
    operation?: string
  ): Promise<Response<Array<BusinessDataDictionaryResponse>>> {
    return commonRequestApi<Array<BusinessDataDictionaryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listChildNewsCategory(
    params: { status: number; necId: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listChildNewsCategory,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询下一级地区信息-列表接口
   * 描述：根据父级地区编码查询当前子项目下的地区信息，默认按排序字段升序排
   * @param request : 地区查询条件
   * @return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.RegionResponse>
   * @date : 2022年8月12日 14:08:53
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listChildRegionInSubProject(
    request: RegionCodeRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listChildRegionInSubProject,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据地区编码集合查询地区信息-列表接口
   * 描述：根据地区编码集合查询当前子项目下的地区信息集合，默认按排序字段升序排
   * @param request : 地区集合查询条件
   * @return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.RegionResponse>
   * @date : 2022年8月12日 14:08:53
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listRegionByCodeInSubProject(
    request: RegionCodeListRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listRegionByCodeInSubProject,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询地区信息-列表接口
   * 描述：根据地区路径编码查询当前子项目下的地区信息，默认按排序字段升序排
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listRegionByCodePathInSubProject(
    request: RegionListRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listRegionByCodePathInSubProject,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据地区编码查询指定级别的地区信息-列表接口
   * 描述：根据地区编码查询指定级别的地区信息，默认按排序字段升序排
   * @param request : 地区查询条件
   * @return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.RegionResponse>
   * @date : 2022年9月22日 20:00:00
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listRegionInSubProject(
    request: RegionRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listRegionInSubProject,
    operation?: string
  ): Promise<Response<Array<RegionResponse>>> {
    return commonRequestApi<Array<RegionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param status 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listRootNewsCategory(
    status: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listRootNewsCategory,
    operation?: string
  ): Promise<Response<Array<NewsCategoryResponse>>> {
    return commonRequestApi<Array<NewsCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { status },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * @Description 项目级-查询银行字典信息-分页列表接口
   * 查询银行字典信息分页列表，默认按排序字段升序排
   * <AUTHOR>
   * @Date 2022/9/29 11:53
   * @param page    :分页对象
   * @param request :银行字典查询条件
   * @return com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.BankResponse>
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageBankInSubProject(
    params: { page?: Page; request?: BankRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageBankInSubProject,
    operation?: string
  ): Promise<Response<BankResponsePage>> {
    return commonRequestApi<BankResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询指定业务数据字典类型的字典信息-分页列表接口
   * 描述：查询指定业务数据字典类型的字典分页列表，默认按排序字段升序排
   * @param page    :分页对象
   * @param request :业务数据字典查询条件
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.BusinessDataDictionaryResponse>
   * @date : 2022年8月12日 17:28:02
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageBusinessDataDictionaryInSubProject(
    params: { page?: Page; request?: BusinessDataDictionaryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageBusinessDataDictionaryInSubProject,
    operation?: string
  ): Promise<Response<BusinessDataDictionaryResponsePage>> {
    return commonRequestApi<BusinessDataDictionaryResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询资讯列表(用于顶级分类的业务平台)
   * @param queryRequest 简略资讯查询条件  todo
   * @return 资讯分页信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCompleteNews(
    params: { queryRequest?: NewsCompleteQueryRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCompleteNews,
    operation?: string
  ): Promise<Response<NewsCompleteResponsePage>> {
    return commonRequestApi<NewsCompleteResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述： 政府单位-查询本级及下属企业单位管理员-分页列表接口
   * 描述：查询政府单位管辖下的企业单位管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:10
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageEnterpriseUnitAdminInfoInGovernmentUnit(
    params: { page?: Page; request?: EnterpriseUnitAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitAdminInfoInGovernmentUnit,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：企业单位-查询当前登录企业单位下属管理员-分页列表接口
   * 描述：查询当前企业单位下的管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:10
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageEnterpriseUnitAdminInfoInMyself(
    params: { page?: Page; request?: AdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitAdminInfoInMyself,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询企业单位管理员-分页列表接口
   * 描述：查询当前子项目下的企业单位管理员信息，默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:21
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageEnterpriseUnitAdminInfoInSubProject(
    params: { page?: Page; request?: EnterpriseUnitAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：政府单位-查询本级及下属企业单位-分页列表接口
   * 描述：查询当前政府单位下管辖的企业单位信息，默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse>
   * @date : 2022年6月9日 10:47:54
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageEnterpriseUnitInfoInGovernmentUnit(
    params: { page?: Page; request?: EnterpriseUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitInfoInGovernmentUnit,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：服务商-查询企业单位-分页列表接口
   * 描述：默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse>
   * @date : 2022年7月29日15:59:49
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageEnterpriseUnitInfoInServicer(
    params: { page?: Page; request?: EnterpriseUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitInfoInServicer,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：项目级-查询企业单位-分页列表接口
   * 描述：查询当前子项目下的企业单位信息，默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse>
   * @date : 2022年6月9日 10:47:54
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageEnterpriseUnitInfoInSubProject(
    params: { page?: Page; request?: EnterpriseUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitInfoInSubProject,
    operation?: string
  ): Promise<Response<EnterpriseUnitInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：政府单位-查询本级及下属政府单位管理员-分页列表接口
   * 描述：查询当前政府单位下管辖的政府单位管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.GovernmentUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:21
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageGovernmentUnitAdminInfoInGovernmentUnit(
    params: { page?: Page; request?: GovernmentUnitAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageGovernmentUnitAdminInfoInGovernmentUnit,
    operation?: string
  ): Promise<Response<GovernmentUnitAdminInfoResponsePage>> {
    return commonRequestApi<GovernmentUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：政府单位-查询当前登录政府单位下属管理员-分页列表接口
   * 描述：查询当前政府单位下的政府单位管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.GovernmentUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:21
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageGovernmentUnitAdminInfoInMyself(
    params: { page?: Page; request?: AdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageGovernmentUnitAdminInfoInMyself,
    operation?: string
  ): Promise<Response<GovernmentUnitAdminInfoResponsePage>> {
    return commonRequestApi<GovernmentUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询政府单位管理员-分页列表接口
   * 描述：查询当前子项目下的政府单位管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.GovernmentUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:21
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageGovernmentUnitAdminInfoInSubProject(
    params: { page?: Page; request?: GovernmentUnitAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageGovernmentUnitAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<GovernmentUnitAdminInfoResponsePage>> {
    return commonRequestApi<GovernmentUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询政府单位-分页列表接口
   * 描述：查询当前子项目下的政府单位信息，默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.GovernmentUnitInfoResponse>
   * @date : 2022/9/21 19:10
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageGovernmentUnitInfoInEnterpriseUnit(
    params: { page?: Page; request?: GovernmentUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageGovernmentUnitInfoInEnterpriseUnit,
    operation?: string
  ): Promise<Response<GovernmentUnitInfoResponsePage>> {
    return commonRequestApi<GovernmentUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：政府单位-查询本级及下属政府单位-分页列表接口
   * 描述：查询当前政府单位以及下级政府单位信息，默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.GovernmentUnitInfoResponse>
   * @date : 2022年6月9日 10:47:54
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageGovernmentUnitInfoInGovernmentUnit(
    params: { page?: Page; request?: GovernmentUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageGovernmentUnitInfoInGovernmentUnit,
    operation?: string
  ): Promise<Response<GovernmentUnitInfoResponsePage>> {
    return commonRequestApi<GovernmentUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询政府单位-分页列表接口
   * 描述：查询当前子项目下的政府单位信息，默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.GovernmentUnitInfoResponse>
   * @date : 2022年6月9日 10:47:54
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageGovernmentUnitInfoInSubProject(
    params: { page?: Page; request?: GovernmentUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageGovernmentUnitInfoInSubProject,
    operation?: string
  ): Promise<Response<GovernmentUnitInfoResponsePage>> {
    return commonRequestApi<GovernmentUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询评价机构-分页列表接口
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse>
   * @date : 2022年6月9日 10:47:54
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageRatingAgenciesUnitInfoInGovernmentUnit(
    params: { page?: Page; request?: RatingAgenciesUnitRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageRatingAgenciesUnitInfoInGovernmentUnit,
    operation?: string
  ): Promise<Response<RatingAgenciesUnitInfoResponsePage>> {
    return commonRequestApi<RatingAgenciesUnitInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询简略资讯列表
   * @param queryRequest 简略资讯查询条件
   * @return 资讯分页信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSimpleNews(
    params: { queryRequest?: NewsSimpleQueryRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSimpleNews,
    operation?: string
  ): Promise<Response<NewsSimpleResponsePage>> {
    return commonRequestApi<NewsSimpleResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：查询用户信息列表-分页接口
   * 描述：查询子项目下的学员分页信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 : 查询参数
   * @param dataFetchingEnvironment :
   * @date : 2022年11月7日 09:37:49
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageUserInfoInGeneral(
    params: { page?: Page; request?: UserQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageUserInfoInGeneral,
    operation?: string
  ): Promise<Response<UserInfoResponsePage>> {
    return commonRequestApi<UserInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 政府单位下-统计各时间段内企业单位总数量
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticEnterpriseUnitGroupByTimeInGovernmentUnit(
    request: EnterpriseUnitDateHistogramRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticEnterpriseUnitGroupByTimeInGovernmentUnit,
    operation?: string
  ): Promise<Response<DateHistogramResponse>> {
    return commonRequestApi<DateHistogramResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 政府单位下-统计各行业类型下企业单位总数量
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticEnterpriseUnitIndustryInGovernmentUnit(
    request: EnterpriseUnitStatisticRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticEnterpriseUnitIndustryInGovernmentUnit,
    operation?: string
  ): Promise<Response<Array<EnterpriseUnitIndustryStatisticResponse>>> {
    return commonRequestApi<Array<EnterpriseUnitIndustryStatisticResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 政府单位下-统计各地区下企业单位总数量
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticEnterpriseUnitRegionInGovernmentUnit(
    request: EnterpriseUnitStatisticRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticEnterpriseUnitRegionInGovernmentUnit,
    operation?: string
  ): Promise<Response<Array<EnterpriseUnitRegionStatisticResponse>>> {
    return commonRequestApi<Array<EnterpriseUnitRegionStatisticResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 政府单位下-统计各单位类型下企业单位总数量
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticEnterpriseUnitTypeInGovernmentUnit(
    request: EnterpriseUnitStatisticRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticEnterpriseUnitTypeInGovernmentUnit,
    operation?: string
  ): Promise<Response<Array<EnterpriseUnitTypeStatisticResponse>>> {
    return commonRequestApi<Array<EnterpriseUnitTypeStatisticResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
