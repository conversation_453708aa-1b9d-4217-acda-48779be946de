<template>
  <el-drawer title="增值服务说明" :visible.sync="dialog" size="600px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-table stripe border :data="tableData" max-height="500px" class="m-table f-mt10">
        <el-table-column label="增值服务内容" min-width="100" align="center">
          <template>学习规则</template>
          <template slot-scope="scope">
            <div v-if="scope.$index === 0">学习规则</div>
            <div v-else-if="scope.$index === 1">智能学习</div>
            <div v-else-if="scope.$index === 2">分销服务（专业版）</div>
            <div v-else>分销服务（基础版）</div>
          </template>
        </el-table-column>
        <el-table-column label="规则说明" min-width="300">
          <template slot-scope="scope">
            <div v-if="scope.$index === 0">
              指提供网校学习规则配置功能，当学员的学习数据不符合学习规则，系统将按照学习规则生成另外的同步/模拟数据供网校使用。
            </div>
            <div v-else-if="scope.$index === 1">
              指在提供完成课程学习、完成测验、完成考试的规则下，按照指定的开始学习时间和合格时间，模拟真实用户登录平台进行培训的过程。同时为学员提供一键选课功能。
            </div>
            <div v-else-if="scope.$index === 2">
              指网校分销能力，开启网校分销服务后，新增供应商和分销商角色，供应商角色相关功能赋予超管，分销商角色不会默认授予，支持创建分销单位和分销商管理员，支持分销商管理员登录网校管理域开展分销业务。网校新增一级功能菜单营销中心-分销管理，新增分销商销售统计、分销商品销售统计报表。开启专业版服务后，供应商和分销商拥有完整的分销服务权限。
            </div>
            <div v-else>
              指网校分销能力，开启网校分销服务后，新增供应商和分销商角色，供应商角色相关功能赋予超管，分销商角色不会默认授予，支持创建分销单位和分销商管理员，网校新增一级功能菜单营销中心-分销管理，新增分销商销售统计、分销商品销售统计报表。开启基础版服务后，分销商不支持管理分销业务，仅支持查看交易数据和报表。
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    dialog = false
    tableData = [{}, {}, {}, {}]
  }
</script>
