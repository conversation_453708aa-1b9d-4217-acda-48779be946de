import PlatformJxjypxtyptSchool, {
  ImportCourseSubjectRequest,
  CreCourseSubjectResponse
} from '@api/diff-gateway/platform-jxjypxtypt-byzj-school'
import { ResponseStatus } from '@hbfe/common'

class CourseMaintenanceInfoVo {
  /**
   * 年度
   */
  year = ''
  /**
   * 地区末级
   */
  region = ''
  /**
   * 地区末级
   */
  regionName = ''
  /**
   * 课程名称
   */
  courseName = ''
  /**
   * 课程id
   */
  id = ''

  static from(item: CreCourseSubjectResponse) {
    const data = new CourseMaintenanceInfoVo()
    data.year = item.year
    data.courseName = item.subjectType
    data.region = item.regional
    data.id = item.id
    return data
  }

  /**
   * 删除公需课接口
   */
  async deleteCourseSubject() {
    return await PlatformJxjypxtyptSchool.deleteCourseSubjectByIdInServicer(this.id)
  }
  /**
   * 删除公需课
   */
  async deleteCourse() {
    const { status, data } = await this.deleteCourseSubject()
    if (status.isSuccess()) {
      return new ResponseStatus(200, '成功')
    }
    return new ResponseStatus(status.code, status.getMessage())
  }
}

export default CourseMaintenanceInfoVo
