import QueryDasicdata, {
  BusinessDataDictionaryResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'

/**
 * 根据行业属性id列表查询对应行业属性信息
 */
class QueryCategoryInfo {
  /**
   * 专业映射
   */
  private professionalMap = new Map<string, BusinessDataDictionaryResponse>()

  /**
   * 学科映射
   */
  private industryPropertyMap = new Map<string, Map<number, BusinessDataDictionaryResponse>>()

  /**
   * 查询接口 通过id
   */
  async queryCategoryInfoByIds(industryPropertyIds: string[]): Promise<BusinessDataDictionaryResponse[]> {
    if (!industryPropertyIds || industryPropertyIds.length === 0) {
      return []
    }
    const result = new Array<BusinessDataDictionaryResponse>()
    const queryIds = new Array<string>()
    industryPropertyIds.forEach(id => {
      if (this.professionalMap.has(id)) {
        result.push(this.professionalMap.get(id))
      } else {
        queryIds.push(id)
      }
    })
    if (queryIds.length) {
      const res = await QueryDasicdata.listBusinessDataDictionaryByIdInSubProject(queryIds)
      if (res.status.isSuccess()) {
        res.data.forEach(item => {
          this.professionalMap.set(item.id, item)
          result.push(item)
        })
      }
    }
    return result || []
  }

  /**
   * 通过code
   */
  async queryCategoryInfoByCode(businessDataDictionaryType: IndustryPropertyCodeEnum, code: number) {
    // 检查是否提供了有效的参数
    if (!businessDataDictionaryType || !code) {
      return new BusinessDataDictionaryResponse()
    }

    // 确保对应类型的内部映射存在
    let insideMap = this.industryPropertyMap.get(businessDataDictionaryType)
    if (!insideMap) {
      insideMap = new Map<number, BusinessDataDictionaryResponse>()
      this.industryPropertyMap.set(businessDataDictionaryType, insideMap)
    } else {
      if (insideMap.has(code)) {
        return insideMap.get(code)
      }
    }

    const res = await QueryDasicdata.getBusinessDataDictionaryInSubProject({
      businessDataDictionaryType,
      code
    })

    if (res.status.isSuccess()) {
      insideMap.set(code, res.data)
    }
    return insideMap.get(code)
  }
}
export default new QueryCategoryInfo()
