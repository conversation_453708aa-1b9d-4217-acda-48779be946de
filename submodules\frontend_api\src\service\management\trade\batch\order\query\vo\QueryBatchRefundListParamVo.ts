import {
  BatchOrderInfoRequest,
  BatchReturnOrderBasicDataRequest,
  BatchReturnOrderRequest,
  BatchReturnOrderStatusChangeTimeRequest,
  DateScopeRequest,
  ReturnCloseReasonRequest,
  ReturnOrderApprovalInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { BatchRefundTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
import SaleChannelType, { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import { PaymentMethodEnum } from '@api/service/management/trade/batch/order/enum/PaymentMethod'
/**
 * @description 【集体报名订单列表】查询参数
 */
class QueryBatchRefundListParamVo {
  /**
   * 批次号
   */
  batchOrderNo = ''

  /**
   * 退款单号
   */
  refundNo = ''
  /**
   * 交易流水号
   */
  flowNo = ''
  /**
   * 购买人账号
   */
  buyerAccount = ''

  /**
   * 购买人姓名
   */
  buyerName = ''
  /**
   * 退款申请时间
   */
  refoundStartDate: string
  refoundEndDate: string
  /**
   * 退款状态
   */
  refundStatus: BatchRefundTradeStatusEnum

  /**
   * 审批时间
   */
  approvalStartDate: string
  approvalEndDate: string
  /**
   * 退款成功时间
   */
  refoundSuccessStartDate: string
  refoundSuccessEndDate: string

  /**
   * 创建人id
   */
  createdUserId: Array<string> = new Array<string>()
  /**
   * 专题名称
   */
  specialSubjectName = ''

  /**
   * 是否来源专题
   */
  isFromSpecialSubject: boolean = undefined

  /**
   * 销售渠道
   */
  saleSource: SaleChannelEnum = null

  /**
   * 分销商Id
   */
  distributorId = ''

  /**
   * 推广门户Id
   */
  promotionPortalId = ''
  /**
   * 缴费方式
   */
  paymentMethod: PaymentMethodEnum = null

  /**
   * 转换远端查询模型
   */
  static to(queryBatchRefundListParamVo: QueryBatchRefundListParamVo) {
    const batchOrderRequest = new BatchReturnOrderRequest()
    batchOrderRequest.batchReturnOrderList = queryBatchRefundListParamVo.refundNo
      ? [queryBatchRefundListParamVo.refundNo]
      : undefined

    batchOrderRequest.batchOrderInfo = new BatchOrderInfoRequest()
    batchOrderRequest.batchOrderInfo.batchOrderNoList = queryBatchRefundListParamVo.batchOrderNo
      ? [queryBatchRefundListParamVo.batchOrderNo]
      : undefined
    // creatorIdList 在外部判断
    batchOrderRequest.batchOrderInfo.flowNoList = queryBatchRefundListParamVo.flowNo
      ? [queryBatchRefundListParamVo.flowNo]
      : undefined
    batchOrderRequest.basicData = new BatchReturnOrderBasicDataRequest()
    switch (Number(queryBatchRefundListParamVo.refundStatus)) {
      /**
       * 退款审批中
       */
      case BatchRefundTradeStatusEnum.REFUNDING:
        batchOrderRequest.basicData.batchReturnOrderStatus = [0, 1, 2]
        break
      /**
       * 退款处理中
       */
      case BatchRefundTradeStatusEnum.REFUNDDISPOSE:
        batchOrderRequest.basicData.batchReturnOrderStatus = [3, 4, 5, 6, 7]
        break
      /**
       * 退款成功
       */
      case BatchRefundTradeStatusEnum.REFUNDSUCCESS:
        batchOrderRequest.basicData.batchReturnOrderStatus = [9, 10, 11]
        break
      /**
       * 退款失败
       */
      case BatchRefundTradeStatusEnum.REFUNDFAIL:
        batchOrderRequest.basicData.batchReturnOrderStatus = [8]
        break
      /**
       * 拒绝退款
       */
      case BatchRefundTradeStatusEnum.REFUSEDREFUND:
        batchOrderRequest.basicData.batchReturnOrderStatus = [12]
        batchOrderRequest.basicData.batchReturnCloseReason = new ReturnCloseReasonRequest()
        batchOrderRequest.basicData.batchReturnCloseReason.closeTypeList = [2]
        break
      /**
       * 取消退款
       */
      case BatchRefundTradeStatusEnum.CANCELREFUND:
        batchOrderRequest.basicData.batchReturnOrderStatus = [12]
        batchOrderRequest.basicData.batchReturnCloseReason = new ReturnCloseReasonRequest()
        batchOrderRequest.basicData.batchReturnCloseReason.closeTypeList = [1, 3, 4]
        break

      default:
        batchOrderRequest.basicData.batchReturnOrderStatus = []
        break
    }
    // batchOrderRequest.basicData.batchReturnOrderStatus =
    //   queryBatchRefundListParamVo.refundStatus === 0 || queryBatchRefundListParamVo.refundStatus
    //     ? [queryBatchRefundListParamVo.refundStatus]
    //     : undefined
    batchOrderRequest.basicData.batchReturnStatusChangeTime = new BatchReturnOrderStatusChangeTimeRequest()
    batchOrderRequest.basicData.batchReturnStatusChangeTime.applied = new DateScopeRequest()
    batchOrderRequest.basicData.batchReturnStatusChangeTime.applied.begin = queryBatchRefundListParamVo.refoundStartDate
      ? queryBatchRefundListParamVo.refoundStartDate
      : undefined
    batchOrderRequest.basicData.batchReturnStatusChangeTime.applied.end = queryBatchRefundListParamVo.refoundEndDate
      ? queryBatchRefundListParamVo.refoundEndDate
      : undefined
    batchOrderRequest.basicData.batchReturnStatusChangeTime.returnCompleted = new DateScopeRequest()
    batchOrderRequest.basicData.batchReturnStatusChangeTime.returnCompleted.begin = queryBatchRefundListParamVo.refoundSuccessStartDate
      ? queryBatchRefundListParamVo.refoundSuccessStartDate
      : undefined
    batchOrderRequest.basicData.batchReturnStatusChangeTime.returnCompleted.end = queryBatchRefundListParamVo.refoundSuccessEndDate
      ? queryBatchRefundListParamVo.refoundSuccessEndDate
      : undefined
    batchOrderRequest.approvalInfo = new ReturnOrderApprovalInfoRequest()
    batchOrderRequest.approvalInfo.approveTime = new DateScopeRequest()
    batchOrderRequest.approvalInfo.approveTime.begin = queryBatchRefundListParamVo.approvalStartDate
      ? queryBatchRefundListParamVo.approvalStartDate
      : undefined
    batchOrderRequest.approvalInfo.approveTime.end = queryBatchRefundListParamVo.approvalEndDate
      ? queryBatchRefundListParamVo.approvalEndDate
      : undefined
    if (queryBatchRefundListParamVo.saleSource || queryBatchRefundListParamVo.saleSource === SaleChannelEnum.self) {
      batchOrderRequest.basicData.saleChannels = [queryBatchRefundListParamVo.saleSource]
    } else {
      batchOrderRequest.basicData.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic
      ]
    }
    batchOrderRequest.basicData.saleChannelName = batchOrderRequest.basicData.saleChannels.includes(
      SaleChannelEnum.topic
    )
      ? queryBatchRefundListParamVo.specialSubjectName
      : ''

    if (queryBatchRefundListParamVo.distributorId) {
      batchOrderRequest.distributorId = queryBatchRefundListParamVo.distributorId
    }
    if (queryBatchRefundListParamVo.promotionPortalId) {
      batchOrderRequest.portalId = queryBatchRefundListParamVo.promotionPortalId
    }
    if (queryBatchRefundListParamVo.paymentMethod) {
      batchOrderRequest.batchOrderInfo.paymentOrderTypeList = [queryBatchRefundListParamVo.paymentMethod]
    } else {
      batchOrderRequest.batchOrderInfo.paymentOrderTypeList = null
    }
    return batchOrderRequest
  }
}

export default QueryBatchRefundListParamVo
