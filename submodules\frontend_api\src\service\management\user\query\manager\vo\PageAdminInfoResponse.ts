import {
  AdminInfoResponse,
  AuthenticationResponse,
  ContractProviderAdminInfoResponse,
  RegionModel,
  RoleResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import RoleVo from '@api/service/management/user/query/manager/vo/RoleVo'
import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
import { DistributionServiceTypeEnum } from '@api/service/common/capability-service-config/enum/DistributionServiceTypeEnum'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'

export default class PageAdminInfoResponse {
  /**
   * 分销商专业版前缀
   */
  static readonly DISTRIBUTOR_PREFIX = 'DZ_NZFXS_'
  /**
   * 分销商基础版前缀
   */
  static readonly DISTRIBUTOR_BASIC_PREFIX = 'DZ_NZFXSJCB_'
  /**
   * 管理员ID
   */
  userId: string
  /**
   * 账户ID
   */
  accountId: string
  /**
   * 管理员姓名
   */
  userName: string
  /**
   * 管理员账号
   */
  adminAccount: string
  /**
   * 管理员状态 帐户状态 1：正常，2：冻结，3：注销
   */
  status: number
  /**
   * 角色列表
   */
  roleList: Array<RoleVo>
  /**
   * 地区管理员管辖地区集合
   */
  manageRegionList: Array<RegionModel>
  /**
   * 注册时间
   */
  createTime: string
  /**
   * 性别
-1未知，0女，1男
   */
  gender: number
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string

  /**
   * 当前分销账号状态（取当前分销商且当前版本的角色）
   */
  get currentDistributorStatus() {
    // 有一个角色是启用当前为启用状态
    return this.currentDistributorRoles.some((item) => item.frozeStatus !== 1)
  }

  /**
   * 当前分销角色（当前分销商与基础版专业版判断）
   */
  get currentDistributorRoles() {
    let roleList =
      this.roleList?.filter(
        (item) => item.applicationMemberId === QueryManagerDetail.currentUnitInfo?.applicationMemberId
      ) ?? []
    if (CapabilityServiceConfig.distributionType === DistributionServiceTypeEnum.basic) {
      roleList = roleList.filter((item) => !item.roleCode?.includes(PageAdminInfoResponse.DISTRIBUTOR_PREFIX))
    } else if (CapabilityServiceConfig.distributionType === DistributionServiceTypeEnum.professional) {
      roleList = roleList.filter((item) => !item.roleCode?.includes(PageAdminInfoResponse.DISTRIBUTOR_BASIC_PREFIX))
    }
    return roleList
  }

  static from(adminInfoResponse: AdminInfoResponse | ContractProviderAdminInfoResponse) {
    const {
      userInfo: { userId, userName, manageRegionList, gender, phone, email },
      accountInfo: { createdTime, status, accountId },
      roleList
    } = adminInfoResponse
    const pageAdminInfoResponse = new PageAdminInfoResponse()
    pageAdminInfoResponse.userId = userId
    const identityFilter = adminInfoResponse?.authenticationList?.filter((item) => item.identityType == 1)
    if (identityFilter?.length) {
      pageAdminInfoResponse.adminAccount = identityFilter[0].identity
    }
    pageAdminInfoResponse.accountId = accountId
    pageAdminInfoResponse.userName = userName
    pageAdminInfoResponse.createTime = createdTime
    pageAdminInfoResponse.roleList = roleList?.map((item) => Object.assign(new RoleVo(), item)) ?? []
    pageAdminInfoResponse.status = status
    pageAdminInfoResponse.manageRegionList = manageRegionList
    pageAdminInfoResponse.gender = gender
    pageAdminInfoResponse.phone = phone
    pageAdminInfoResponse.email = email
    return pageAdminInfoResponse
  }
}
