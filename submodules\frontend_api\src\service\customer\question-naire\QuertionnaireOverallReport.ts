import QuetionReport from '@api/service/common/question-naire/QuetionReport'
import { Response } from '@hbfe/common'
import MsExamQueryFrontGatewayQuestionnaireQueryForeStage, {
  QuestionnaireAnswerStaitsticsRequest
} from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryForeStage'
import MsExamQueryFrontGatewayExamQueryForeStage, {
  BaseQuestionResponse,
  MultipleQuestionResponse,
  RadioQuestionResponse,
  ScaleQuestionResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryForeStage'
import Question from '@api/service/common/question-naire/Question'
import MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import { AnswerStaitsticsResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryForeStage'
import DiffQuestionnaireQueryForeStage, {
  QuestionnaireAnswerAllTeacherResponse
} from '@api/platform-gateway/diff-exam-query-front-gateway-QuestionnaireQueryForestage'
/**
 * 学员端-问卷报告
 */
export default class QuestionnaireOverallReport {
  /**
   * 问卷模板id
   */
  id = ''
  /**
   * 方案id
   */
  schemeId = ''
  /**
   * 试题报告列表
   */
  quetionsReportList = new Array<QuetionReport>()
  /**
   * 问卷名称
   */
  queationnaireName = ''
  /**
   * 报名总数
   */
  totalNum = 0

  /**
   * 获取整体报告
   */
  async doQuery(issueId?: string) {
    const request = new QuestionnaireAnswerStaitsticsRequest()
    request.questionnaireId = this.id
    const res = await MsExamQueryFrontGatewayQuestionnaireQueryForeStage.getQuestionnaireAnswerStaitsticsInMyself(
      request
    )
    if (res.status.isSuccess() && res.data) {
      this.queationnaireName = res.data.surveyInformationResponse.questionnaireName
      this.id = res.data.surveyInformationResponse.questionnaireId
      this.totalNum = res.data.totalAnswerNum
      this.schemeId = res.data.surveyInformationResponse.schemeId
      let teacherRes = new Response<QuestionnaireAnswerAllTeacherResponse>()
      if (issueId) {
        teacherRes = await DiffQuestionnaireQueryForeStage.getCourseTeachersWithPeriodAndOnlineInMyself({
          issueId
        })
      } else {
        // 如果期别和方案冲突 就优先调期别的口
        teacherRes = await DiffQuestionnaireQueryForeStage.getCourseTeachersWithPeriodAndOnlineInMyself({
          schemeId: this.schemeId
        })
      }
      this.quetionsReportList = res.data.answerStaitsticsList?.map((item) => {
        const temp = QuetionReport.from(
          item as AnswerStaitsticsResponse,
          teacherRes.data?.offlineTeachers,
          teacherRes.data?.onlineTeachers
        )
        return temp
      })
      const questionIdList = this.quetionsReportList.map((item) => item.id)
      if (!questionIdList.length) {
        this.quetionsReportList = []
        return
      }
      const currentPageData = new Array<BaseQuestionResponse>()
      // 获取试题 防止分页口超过200条
      const fetchQuestionList = async (ids: string[]) => {
        const questionRes = await MsExamQueryFrontGatewayExamQueryForeStage.pageQuestionInMySelf({
          page: {
            pageNo: 1,
            pageSize: ids.length
          },
          request: {
            queryScope: 2,
            questionIdList: ids
          }
        })
        if (questionRes.status.isSuccess() && questionRes.data.currentPageData?.length) {
          currentPageData.push(...questionRes.data.currentPageData)
        }
      }
      for (let i = 0; i < questionIdList.length; i += 200) {
        const chunk = questionIdList.slice(i, i + 200)
        await fetchQuestionList(chunk)
      }
      if (currentPageData?.length) {
        const newArray = currentPageData.map((item) => {
          const temp = Question.fromQuestionReport(
            item as RadioQuestionResponse | MultipleQuestionResponse | ScaleQuestionResponse
          )
          return temp
        })
        const map = new Map(newArray.map((item) => [item.id, item]))
        const mergedArray = this.quetionsReportList.map((item1) => {
          const temp = map.get(item1.id)
          temp.submitNum = item1.submitNum
          temp.submitProportion = Number((item1.submitNum / this.totalNum).toFixed(4))
          temp.scaleStatisc = temp.scaleStatisc.map((ite) => {
            ite.selectNum = 0
            ite.selectProportion = 0
            item1.scaleStatisc.map((it) => {
              if (it.levelNum == ite.levelNum) {
                ite.selectNum = it.selectNum
                ite.selectProportion = it.selectProportion
              }
            })
            return ite
          })
          if (!item1.isTeacherEvaluate) {
            // 非教师题
            temp.optionsStatisc = temp.optionsStatisc.map((ite) => {
              ite.selectNum = 0
              ite.selectProportion = 0
              item1.optionsStatisc.map((it) => {
                // 对得上id的赋值，对不上id的赋0
                if (it.id == ite.id) {
                  ite.selectNum = it.selectNum
                  ite.selectProportion = it.selectProportion
                }
              })
              return ite
            })
          } else {
            // 教师题
            temp.optionsStatisc = item1.optionsStatisc
          }
          return temp
        })
        this.quetionsReportList = mergedArray?.filter((item) => item != undefined)
      }
    }
    return res.status
  }
}
