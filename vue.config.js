const path = require('path')
const VueAutoRoutingPlugin = require('./build/libs/vue-auto-routing/lib/webpack-plugin')
const address = require('address')
const currentIp = address.ip()
const unitTypeAnalyser = require('./build/libs/unit-type-analyse')
const { alias } = require('./build/libs/unit-type-analyse/util')
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin')
const ESLintPlugin = require('eslint-webpack-plugin')

const gitInfo = require('./.cache/git-info.json')

module.exports = {
  publicPath: './',
  productionSourceMap: false,
  transpileDependencies: ['@hbfe/hbfe-ui'],
  configureWebpack: () => {
    const plugins = []
    return {
      optimization: {
        splitChunks: {
          automaticNameDelimiter: '-'
        }
      },
      resolve: {
        alias: Object.assign(
          {
            vue$: path.resolve(__dirname, 'node_modules/vue/dist/vue.esm.js'),
            '@packages': path.resolve(__dirname, './packages'),
            '@api': path.resolve(__dirname, './submodules/frontend_api/src'),
            '@design': path.resolve(__dirname, './submodules/design/src'),
            axios: path.resolve(__dirname, 'node_modules/axios'),
            '@hbfe/request': path.resolve(__dirname, 'packages/request/request.ts')
          },
          alias
        )
      },
      module: {
        rules: [
          {
            test: /\.(graphql|gql)/,
            loader: 'raw-loader'
          },
          {
            test: /\.(png|jpe?g|gif|svg|ico)$/,
            type: 'asset/resource', // 确保所有资源都作为独立文件
            generator: {
              filename: 'assets/images/[name].[hash:8][ext]' // 输出目录和文件命名
            }
          }
        ]
      },
      plugins: [
        // 自动化生成 packages 下的模块路由，待 packages 下的路由模块提交解除注释
        new VueAutoRoutingPlugin({
          importPrefix: '@hbfe/jxjy-admin-routers/src/basic-router/',
          pages: path.resolve(__dirname, 'packages/routers/src/basic-router'),
          chunkNamePrefix: '@hbfe/jxjy-admin-routers/src/basic-router/'
        }),
        new VueAutoRoutingPlugin({
          importPrefix: '@hbfe/jxjy-admin-routers/src/basic-school/',
          cacheDir: 'basic-school-routes',
          pages: path.resolve(__dirname, 'packages/routers/src/basic-school'),
          chunkNamePrefix: '@hbfe/jxjy-admin-routers/src/basic-school/'
        }),
        // new VueAutoRoutingPlugin({
        //   importPrefix: '@hbfe/jxjy-admin-routers/src/basic-school/',
        //   cacheDir: 'basic-school-routes',
        //   pages: path.resolve(__dirname, 'packages/routers/src/basic-school'),
        //   chunkNamePrefix: '@hbfe/jxjy-admin-routers/src/basic-school/'
        // }),
        ...unitTypeAnalyser,
        new ESLintPlugin({
          extensions: ['js', 'vue', 'ts'],
          formatter: require('eslint-formatter-pretty')
        }),
        new NodePolyfillPlugin(),
        ...plugins
      ],
      cache: {
        type: 'filesystem'
      }
    }
  },
  chainWebpack: (config) => {
    if (process.env.NODE_ENV === 'production') {
      config.optimization.minimizer('terser').tap((args) => {
        const terserOptions = args[0].terserOptions
        terserOptions.compress.drop_console = true
        console.log(terserOptions, 'options')
        return args
      })
    }
    const sassOptions = {
      sourceMap: true,
      sassOptions: {
        // 解决用 dart-sass 的情况下面图标乱码问题。
        // 主要是默认 outputStyle 设置为 compressed 会把 content: 内容压缩空
        outputStyle: 'expanded'
      }
    }
    config.module

    config.module
      .rule('scss')
      .test(/\.scss$/)

      .oneOf('normal')
      .use('sass-loader')
      .options(sassOptions)
      .end()

      .use('scss')
      .before('sass-loader')
      .loader('resolve-url-loader')
      .end()
      .end()

      .oneOf('normal-modules')
      .use('sass-loader')
      .options(sassOptions)
      .end()

      .use('scss')
      .before('sass-loader')
      .loader('resolve-url-loader')
      .end()
      .end()

      .oneOf('vue-modules')
      .use('sass-loader')
      .options(sassOptions)
      .end()

      .use('scss')
      .before('sass-loader')
      .loader('resolve-url-loader')
      .end()
      .end()

      .oneOf('vue')
      .use('sass-loader')
      .options(sassOptions)
      .end()

      .use('scss')
      .before('sass-loader')
      .loader('resolve-url-loader')
      .end()
      .end()

    config.plugins.delete('prefetch')

    config.plugin('define').tap((args) => {
      const env = args[0]['process.env']
      env.CURRENT_IP = JSON.stringify(currentIp)
      env.GIT_INFO_BRANCH = JSON.stringify((gitInfo || {}).branch || '')
      return args
    })
  },
  devServer: {
    allowedHosts: 'all',
    proxy: require('./build/config/proxyTable')
  }
}
