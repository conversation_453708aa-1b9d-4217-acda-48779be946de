<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">培训过程管理</span>
        </div>
        <!--问卷二维码-->
        <el-button @click="dialog001 = true" type="primary" class="f-mr20 f-mb20">问卷二维码</el-button>
        <el-drawer
          title="问卷二维码"
          :visible.sync="dialog001"
          :direction="direction"
          size="480px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
              通过使用微信扫一扫二维码打开问卷
            </el-alert>
            <div class="m-view-qrcode">
              <div class="item">
                <div class="content">
                  <div class="tit">问卷名称问卷名称问卷名称问卷名称问卷名称</div>
                  <div class="cate">读取问卷类型</div>
                  <div class="code"><img src="./assets/images/qr-code.png" class="u-qr-code" /></div>
                </div>
                <div class="op"><el-button type="primary" round size="medium">保存至本地</el-button></div>
              </div>
              <div class="item">
                <div class="content">
                  <div class="tit">问卷名称问卷名称问卷名称问卷名称问卷名称</div>
                  <div class="cate">读取问卷类型</div>
                  <div class="code"><img src="./assets/images/qr-code.png" class="u-qr-code" /></div>
                </div>
                <div class="op"><el-button type="primary" round size="medium">保存至本地</el-button></div>
              </div>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">关闭</el-button>
          </div>
        </el-drawer>
        <!--考勤二维码-->
        <el-button @click="dialog002 = true" type="primary" class="f-mr20 f-mb20">考勤二维码</el-button>
        <el-drawer
          title="考勤二维码"
          :visible.sync="dialog002"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
              通过使用微信扫一扫二维码签到/签退，不校验定位地址
            </el-alert>
            <!--条件查询-->
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="10">
                  <el-form-item label="考勤类型">
                    <el-select placeholder="请选择考勤类型"></el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <el-button>重置</el-button>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="日期" min-width="120">
                <template>2024-10-30</template>
              </el-table-column>
              <el-table-column label="课程名称" min-width="160">
                <template>读取课程最新名称</template>
              </el-table-column>
              <el-table-column label="考勤类型" min-width="160" align="center">
                <template>签到</template>
              </el-table-column>
              <el-table-column label="签到/签退时段" min-width="180">
                <template>
                  08:00:00 至 10:00:00
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-button type="text">下载二维码</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">关闭</el-button>
          </div>
        </el-drawer>
        <!--考勤二维码（下载）-->
        <el-button @click="dialog003 = true" type="primary" class="f-mr20 f-mb20">考勤二维码（下载）</el-button>
        <el-drawer
          title="考勤二维码"
          :visible.sync="dialog003"
          :direction="direction"
          size="480px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-view-qrcode">
              <div class="item z-no-border">
                <div class="content">
                  <div class="tit">读取课程名称读取课程名称读取课程名称</div>
                  <div class="cate">课程日期</div>
                  <div class="date">签到：08:00:00 至 10:00:00</div>
                  <div class="code"><img src="./assets/images/qr-code.png" class="u-qr-code" /></div>
                </div>
              </div>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>关闭</el-button>
            <el-button type="primary">保存至本地</el-button>
          </div>
        </el-drawer>
        <!--报到二维码-->
        <el-button @click="dialog004 = true" type="primary" class="f-mr20 f-mb20">报到二维码</el-button>
        <el-drawer
          title="报到二维码"
          :visible.sync="dialog004"
          :direction="direction"
          size="480px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-tit" style="flex-direction: column;">
              <span class="tit-txt">报到二维码</span>
              <div class="f-mt5 f-c6">学员可以通过微信扫一扫进入期别进行报到打卡</div>
            </div>
            <div class="m-view-qrcode">
              <div class="item">
                <div class="content">
                  <div class="tit">读取期别名称读取期别名称读取期别名称</div>
                  <div class="cate">培训报到时段：YYYY-MM-DD至YYYY-MM-DD</div>
                  <div class="code"><img src="./assets/images/qr-code.png" class="u-qr-code" /></div>
                </div>
                <div class="op"><el-button type="primary" round size="medium">下载二维码</el-button></div>
              </div>
            </div>
            <div class="m-tit" style="flex-direction: column;">
              <span class="tit-txt">报到二维码（不校验定位）</span>
              <div class="f-mt5 f-c6">
                学员可以通过微信扫一扫进入期别进行报到打卡并且不校验学员当前位置是否抵达报到范围
              </div>
            </div>
            <div class="m-view-qrcode">
              <div class="item">
                <div class="content">
                  <div class="tit">读取期别名称读取期别名称读取期别名称</div>
                  <div class="cate">培训报到时段：YYYY-MM-DD至YYYY-MM-DD</div>
                  <div class="code"><img src="./assets/images/qr-code.png" class="u-qr-code" /></div>
                </div>
                <div class="op"><el-button type="primary" round size="medium">下载二维码</el-button></div>
              </div>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">关闭</el-button>
          </div>
        </el-drawer>
        <!--修改住宿信息-->
        <el-button @click="dialog005 = true" type="primary" class="f-mr20 f-mb20">修改住宿信息</el-button>
        <el-drawer
          title="修改住宿信息"
          :visible.sync="dialog005"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
              学员若需住宿，食宿统一安排，费用另行自理。
            </el-alert>
            <el-form ref="form" :model="form" label-width="150px" class="m-form">
              <el-form-item label="是否住宿：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="需要安排住宿"></el-radio>
                  <el-radio label="无需安排住宿"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="住宿方式：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="单住"></el-radio>
                  <el-radio label="合住"></el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">保存</el-button>
          </div>
        </el-drawer>
        <!--导入结业成果-->
        <el-button @click="dialog006 = true" type="primary" class="f-mr20 f-mb20">导入结业成果</el-button>
        <el-drawer
          title="导入结业成果"
          :visible.sync="dialog006"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
              学员若需住宿，食宿统一安排，费用另行自理。
            </el-alert>
            <el-steps direction="vertical" :active="4" class="m-vertical-steps">
              <el-step title="下载模板并按要求填写">
                <div slot="description">
                  <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-download">
                    下载模板
                  </el-button>
                </div>
              </el-step>
              <el-step title="上传填写好的表格">
                <div slot="description">
                  <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                    <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                      选择文件
                    </el-button>
                  </el-upload>
                </div>
              </el-step>
            </el-steps>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">导入</el-button>
          </div>
        </el-drawer>
        <!--导出数据-->
        <el-button @click="dialog006 = true" type="primary" class="f-mr20">导出列表数据</el-button>
        <el-dialog title="提示" :visible.sync="dialog006" width="400px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb f-f16">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">导出入口：XXXXXXX</p>
            </div>
          </div>
          <div slot="footer">
            <el-button>暂不</el-button>
            <el-button type="primary">前往下载</el-button>
          </div>
        </el-dialog>
        <!--学习心得-详情-->
        <el-button @click="dialog007 = true" type="primary" class="f-mr20 f-mb20">学习心得-详情</el-button>
        <el-drawer
          title="学习心得详情"
          :visible.sync="dialog007"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10 f-mlr20">
              <el-form-item label="用户姓名：">姓名（352203198812290022）</el-form-item>
              <el-form-item label="培训方案名称：">方案名称方案名称方案名称方案名称方案名称</el-form-item>
              <el-form-item label="学习心得类型：">班级心得班级心得班级心得</el-form-item>
              <el-form-item label="主题：">主题名称</el-form-item>
              <el-form-item label="内容：">--</el-form-item>
              <el-form-item label="参加时间：">--</el-form-item>
              <el-form-item label="作答形式：">--</el-form-item>
              <el-form-item label="审核方式：">--</el-form-item>
              <el-form-item label="学习心得："
                >--
                <el-button type="text" class="f-ml10">查看</el-button>
                <el-button type="text" class="f-ml10">下载</el-button>
              </el-form-item>
              <el-form-item label="提交时间：">--</el-form-item>
              <el-form-item label="审核状态：">--</el-form-item>
              <el-form-item label="审核结果：">--</el-form-item>
              <el-form-item label="审核时间：">--</el-form-item>
              <el-form-item label="审核意见：">--</el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">关闭</el-button>
          </div>
        </el-drawer>
        <!--审核学习心得-->
        <el-button @click="dialog008 = true" type="primary" class="f-mr20 f-mb20">审核学习心得</el-button>
        <el-drawer
          title="审核学习心得"
          :visible.sync="dialog008"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10 f-mlr20">
              <el-form-item label="用户姓名：">姓名（352203198812290022）</el-form-item>
              <el-form-item label="培训方案名称：">方案名称方案名称方案名称方案名称方案名称</el-form-item>
              <el-form-item label="学习心得类型：">班级心得班级心得班级心得</el-form-item>
              <el-form-item label="主题：">主题名称</el-form-item>
              <el-form-item label="内容：">--</el-form-item>
              <el-form-item label="参加时间：">--</el-form-item>
              <el-form-item label="作答形式：">--</el-form-item>
              <el-form-item label="审核方式：">--</el-form-item>
              <el-form-item label="学习心得："
                >--
                <el-button type="text" class="f-ml10">查看</el-button>
                <el-button type="text" class="f-ml10">下载</el-button>
              </el-form-item>
              <el-form-item label="提交时间：">--</el-form-item>
              <el-form-item label="审核状态：">--</el-form-item>
              <el-form-item label="审核结果：" required>
                <el-input v-model="form.name" size="small" class="input-num f-mr5" /> 分（总分：100分，通过分：
                <i class="f-cr">60</i> 分）</el-form-item
              >
              <el-form-item label="审核意见：" required>
                <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="不合格时需填写审核意见" />
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>关闭</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--查看统计报告-整体报告-问答题-查看详细信息-->
        <el-button @click="dialog009 = true" type="primary" class="f-mr20 f-mb20">查看详细信息</el-button>
        <el-drawer
          title="查看详细信息"
          :visible.sync="dialog009"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="提交时间" min-width="160" align="center">
                <template>2023-10-23 00:00:00</template>
              </el-table-column>
              <el-table-column label="答案文本" min-width="300">
                <template>读取学员提交的答案</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>关闭</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        num: 100,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        activeNames: ['1'],
        props: { multiple: true },
        radio: 3,
        checked: false,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog01: false,
        dialog02: false,
        dialog03: false,
        dialog04: false,
        dialog05: false,
        dialog06: false,
        dialog07: false,
        dialog08: false,
        dialog09: false,
        dialog10: false,
        dialog001: false,
        dialog002: false,
        dialog003: false,
        dialog004: false,
        dialog005: false,
        dialog006: false,
        dialog007: false,
        dialog008: false,
        dialog009: false,
        dialog0001: false,
        dialog0002: false,
        dialog0003: false,
        dialog0004: false,
        dialog0005: false,
        dialog00001: false,
        dialog00002: false,
        dialog00003: false,
        dialog00004: false,
        dialog00005: false,
        dialog000001: false,
        dialog000002: false,
        dialog000003: false,
        dialog000004: false,
        dialog000005: false,
        dialog0000001: false,
        dialog0000002: false,
        dialog0000003: false,
        dialog0000004: false,
        dialog0000005: false,
        dialog00000001: false,
        dialog00000002: false,
        dialog00000003: false,
        dialog00000004: false,
        dialog00000005: false,

        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      open3() {
        this.$message({
          message: '本次修改内容如涉及到考核重算，重算任务于程序后台自动执行，即将自动为您跳转到方案管理列表页。',
          type: 'warning',
          duration: 5000,
          customClass: 'm-message'
        })
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
