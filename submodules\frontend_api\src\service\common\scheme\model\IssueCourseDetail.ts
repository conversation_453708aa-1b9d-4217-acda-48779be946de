import Mockjs from 'mockjs'
import MultipleAssessLearningTypeInfo from '@api/service/common/scheme/model/MultipleAssessLearningTypeInfo'
import moment from 'moment'
import TeachingPlanItemGroupInfo from '@api/service/common/scheme/model/TeachingPlanItemGroupInfo'
import TeachingPlanItemsGroup from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/teaching-plan-items-groups/TeachingPlanItemsGroup'
import TeachingPlanItem from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/teaching-plan-items-groups/teaching-plan-items/TeachingPlanItem'
import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
import PlaceholderConstant from '@api/service/common/scheme/model/schemeDto/common/PlaceholderConstant'

/**
 * @description 期别课程详情
 */
class IssueCourseDetail extends MultipleAssessLearningTypeInfo {
  /**
   * 所属教学计划组信息
   */
  belongGroupInfo = new TeachingPlanItemGroupInfo()
  /**
   * 自定义前缀
   * @description 用于识别是否是自定义创建的项
   */
  static customPrefixId = 'issueCourse'
  /**
   * 是否已编辑
   * @description 供UI使用，默认值false
   */
  isModified = false
  /**
   * 教师适用范围 1公共
   */
  teacherNature = 1
  /**
   * 教学计划教学模式 1-线下教学计划  2-线上教学计划  3-直播教学计划  10-混合教学计划
   */
  planMode = 1
  /**
   * 期别课程id
   */
  id = ''
  /**
   * 课程名称
   */
  courseName = ''
  /**
   * 教师id
   */
  teacherId = ''
  /**
   * 教师名称
   */
  teacherName = ''
  /**
   * 教师职称
   */
  teacherPositionTile = ''
  /**
   * 教师单位
   */
  teacherUnitName = ''
  /**
   * 课程学时
   */
  coursePeriod: number = null
  /**
   * 授课日期
   */
  teachingDate = ''
  /**
   * 课程开始时间
   */
  courseBeginTime = ''
  /**
   * 课程结束时间
   */
  courseEndTime = ''

  /**
   * 完整的课程开始时间，格式为：YYYY-MM-dd HH:mm:ss
   */
  get fullBeginDate() {
    return this.teachingDate && this.courseBeginTime ? `${this.teachingDate} ${this.courseBeginTime}` : ''
  }

  /**
   * 完整的课程结束时间，格式为：YYYY-MM-dd HH:mm:ss
   */
  get fullEndDate() {
    return this.teachingDate && this.courseEndTime ? `${this.teachingDate} ${this.courseEndTime}` : ''
  }

  /**
   * 课程时长，单位：秒
   */
  get courseTimeLength() {
    return this.fullBeginDate && this.fullEndDate
      ? moment(this.fullEndDate).diff(moment(this.fullBeginDate), 'seconds')
      : 0
  }

  /**
   * 构造函数
   */
  constructor() {
    super()
    this.id = IssueCourseDetail.customPrefixId + Mockjs.Random.guid()
  }

  /**
   * 是否是自定义创建id的项
   */
  get isCustomPrefixIdItem() {
    return this.id.startsWith(IssueCourseDetail.customPrefixId)
  }

  /**
   * 是否是下午时间
   */
  get isAfternoon(): boolean {
    let result = null as boolean
    const noon = moment('12:00:00', 'HH:mm:ss')
    if (this.courseBeginTime) {
      const curTime = moment(this.courseBeginTime, 'HH:mm:ss', 'h')
      if (curTime.isSame(noon)) {
        // 超过或等于12:00:00都是下午时间
        result = true
      } else {
        result = curTime.isAfter(noon)
      }
    }
    return result
  }

  /**
   * 通过Dto配置期别课程详情
   * @param teachingPlanItemGroup 教学计划组
   * @param teachingPlanItem 教学计划项
   * @param operationType 操作类型
   */
  static configIssueCourseDetailByDto(
    teachingPlanItemGroup: TeachingPlanItemsGroup,
    teachingPlanItem: TeachingPlanItem,
    operationType: OperationTypeEnum
  ) {
    const issueCourseDetail = new IssueCourseDetail()
    issueCourseDetail.belongGroupInfo = {
      groupId: teachingPlanItemGroup.id,
      groupName: teachingPlanItemGroup.name,
      planId: teachingPlanItemGroup.planId,
      groupType: teachingPlanItemGroup.groupType,
      groupTypeCode: teachingPlanItemGroup.groupTypeCode
    }
    issueCourseDetail.operationType = operationType
    issueCourseDetail.id = teachingPlanItem.id
    issueCourseDetail.courseName = teachingPlanItem.name
    issueCourseDetail.coursePeriod = teachingPlanItem.period
    issueCourseDetail.planMode = teachingPlanItem.planMode
    issueCourseDetail.teachingDate = moment(teachingPlanItem.startTime).format(
      PlaceholderConstant.momentYearMonthDayFormat
    )
    issueCourseDetail.courseBeginTime = moment(teachingPlanItem.startTime).format(
      PlaceholderConstant.momentHourMinuteSecondFormat
    )
    issueCourseDetail.courseEndTime = moment(teachingPlanItem.endTime).format(
      PlaceholderConstant.momentHourMinuteSecondFormat
    )
    const { teachers } = teachingPlanItem
    if (teachers && teachers.length) {
      const teacherItem = teachers[0]
      const { extendProperties } = teacherItem
      if (extendProperties && extendProperties.length) {
        const teacherProfessionalTitleItem = extendProperties.find(
          (el) => el.name === PlaceholderConstant.teachingPlanItemTeacherProfessionalTitle
        )
        if (teacherProfessionalTitleItem) {
          issueCourseDetail.teacherPositionTile = teacherProfessionalTitleItem.value as string
        }
        const teacherUnitNameItem = extendProperties.find(
          (el) => el.name === PlaceholderConstant.teachingPlanItemTeacherUnitName
        )
        if (teacherUnitNameItem) {
          issueCourseDetail.teacherUnitName = teacherUnitNameItem.value as string
        }
      }
      issueCourseDetail.teacherNature = teacherItem.nature
      issueCourseDetail.teacherId = teacherItem.id
      issueCourseDetail.teacherName = teacherItem.teacherName
    }
    return issueCourseDetail
  }
}

export default IssueCourseDetail
