<route-meta>
{
"isMenu": true,
"title": "培训点管理",
"sort": 7,
"icon": "icon-tiku"
}
</route-meta>
<script lang="ts">
  import TraininPoints from '@hbfe/jxjy-admin-trainingPoints/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY],
    create: [WXGLY],
    detail: [WXGLY],
    remove: [WXGLY],
    modify: [WXGLY],
    deactivate: [WXGLY]
  })
  export default class extends TraininPoints {}
</script>
