<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="年度">
                <el-select v-model="select" clearable placeholder="请选择年度">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="地区">
                <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="行业">
                <el-select v-model="select" clearable placeholder="请选择行业">
                  <el-option value="人社"></el-option>
                  <el-option value="建设"></el-option>
                  <el-option value="职业卫生"></el-option>
                  <el-option value="工勤"></el-option>
                  <el-option value="教师"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="科目类型">
                <el-select v-model="select" clearable placeholder="请选择科目类型">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训类别">
                <el-select v-model="select" clearable placeholder="请选择培训类别">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训专业">
                <el-select v-model="select" clearable filterable placeholder="请选择">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训方案类型">
                <el-select clearable placeholder="请选择培训方案类型">
                  <el-option label="选项1" value=""></el-option>
                  <el-option label="选项2" value=""></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训方案名称">
                <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="销售状态">
                <el-select clearable placeholder="请选择销售状态">
                  <el-option label="选项1" value=""></el-option>
                  <el-option label="选项2" value=""></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="1" :md="8" :xl="12" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">表格全屏</el-button>
                <el-button type="primary">导出列表数据</el-button>
                <el-button type="primary">批量更新方</el-button>
                <el-button type="primary">查询</el-button>
                <el-button>重置</el-button>
                <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </el-card>
      <el-tabs v-model="activeName2" type="card" class="m-tab-card">
        <el-tab-pane label="网授" name="first">
          <el-card shadow="never" class="m-card">
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="培训方案名称" min-width="300" fixed="left">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <p><el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag>培训方案名称</p>
                    <el-tag type="warning">处理中</el-tag>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <p>
                      <el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag>培训方案名称培训方案名称
                    </p>
                    <el-tooltip class="item" effect="dark" placement="bottom" popper-class="m-tooltip">
                      <el-tag type="danger">异常<i class="el-icon-info icon"></i></el-tag>
                      <div slot="content">异常原因：显示异常原因</div>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    <p>
                      <el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag
                      >培训方案名称培训方案名称培训方案名称培训方案名称
                    </p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="报名学时" min-width="110" align="center">
                <template>50</template>
              </el-table-column>
              <el-table-column label="价格" min-width="100" align="right">
                <template>50.00</template>
              </el-table-column>
              <el-table-column label="培训属性" min-width="240">
                <template>
                  <p>行业：行业行业</p>
                  <p>地区：为空，不展示</p>
                  <p>科目类型：科目类型</p>
                  <p>培训类别：培训类别</p>
                  <p>培训专业：培训专业培训专业</p>
                  <p>培训年度：2019</p>
                </template>
              </el-table-column>
              <el-table-column label="销售状态" min-width="140">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="info" class="badge-status">已下架</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">已上架</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="学习起止时间" min-width="220">
                <template>
                  <p>起始：2021-10-15 00:21:21</p>
                  <p>结束：2021-10-15 00:21:21</p>
                </template>
              </el-table-column>
              <el-table-column label="报名起止时间" min-width="220">
                <template>
                  <p>起始：2021-10-15 00:21:21</p>
                  <p>结束：2021-10-15 00:21:21</p>
                </template>
              </el-table-column>
              <el-table-column label="最新修改时间" sortable min-width="180">
                <template>2021-10-15 00:21:21</template>
              </el-table-column>
              <el-table-column label="是否仅导入开通" min-width="180">
                <template>否</template>
              </el-table-column>
              <el-table-column label="成果是否同步" min-width="180">
                <template>同步</template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text" size="mini">修改</el-button>
                    <el-button type="text" size="mini">详情</el-button>
                    <el-button type="text" size="mini">复制</el-button>
                    <el-popconfirm
                      confirm-button-text="确定下架"
                      cancel-button-text="取消"
                      icon="el-icon-info"
                      icon-color="red"
                      title="确定立即下架该培训方案？"
                    >
                      <el-button type="text" size="mini" slot="reference">立即下架</el-button>
                    </el-popconfirm>
                    <el-popconfirm
                      confirm-button-text="确定下架"
                      cancel-button-text="取消"
                      icon="el-icon-info"
                      icon-color="red"
                      title="删除后需要重新创建培训班，是否确认删除？"
                    >
                      <el-button type="text" size="mini" slot="reference">删除</el-button>
                    </el-popconfirm>
                    <el-button type="text" size="mini">合并报名管理</el-button>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <el-button type="text" size="mini">修改</el-button>
                    <el-button type="text" size="mini">详情</el-button>
                    <el-button type="text" size="mini">复制</el-button>
                    <el-popconfirm
                      confirm-button-text="确定下架"
                      cancel-button-text="取消"
                      icon="el-icon-info"
                      icon-color="red"
                      title="当前方案已被其他方案选择为合并报名方案，下架后将导致学员端无法合并报名。确定立即下架该培训方案？"
                    >
                      <el-button type="text" size="mini" slot="reference">立即下架</el-button>
                    </el-popconfirm>
                    <el-popconfirm
                      confirm-button-text="确定下架"
                      cancel-button-text="取消"
                      icon="el-icon-info"
                      icon-color="red"
                      title="当前方案已被其他方案选择为合并报名方案，删除后将从合并报名方案中移除。是否确认删除？"
                    >
                      <el-button type="text" size="mini" slot="reference">删除</el-button>
                    </el-popconfirm>
                  </div>
                  <div v-else-if="scope.$index === 2">
                    <el-button type="text" size="mini" disabled>修改</el-button>
                    <el-button type="text" size="mini">详情</el-button>
                    <el-button type="text" size="mini">复制</el-button>
                    <el-button type="text" size="mini">报名关闭</el-button>
                    <el-button type="text" size="mini">删除</el-button>
                    <el-button type="text" size="mini">分销详情</el-button>
                    <el-button type="text" size="mini">实施管理</el-button>
                    <el-button type="text" size="mini">上移</el-button>
                    <el-button type="text" size="mini">下移</el-button>
                  </div>
                  <div v-else-if="scope.$index === 3">
                    <el-button type="text" size="mini">修改</el-button>
                    <el-button type="text" size="mini">详情</el-button>
                    <el-button type="text" size="mini">复制</el-button>
                    <el-button type="text" size="mini">报名开启</el-button>
                    <el-button type="text" size="mini">删除</el-button>
                    <el-button type="text" size="mini">分销详情</el-button>
                    <el-button type="text" size="mini">实施管理</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text" size="mini">修改</el-button>
                    <el-button type="text" size="mini">详情</el-button>
                    <el-button type="text" size="mini">复制</el-button>
                    <el-button type="text" size="mini">报名关闭</el-button>
                    <el-button type="text" size="mini">删除</el-button>
                    <el-button type="text" size="mini">分销详情</el-button>
                    <el-button type="text" size="mini">实施管理</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="面授" name="second">
          <el-card shadow="never" class="m-card">
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="培训方案名称" min-width="300" fixed="left">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <p>培训方案名称</p>
                    <el-tag type="warning">处理中</el-tag>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <p>培训方案名称培训方案名称</p>
                    <el-tooltip class="item" effect="dark" placement="bottom" popper-class="m-tooltip">
                      <el-tag type="danger">异常<i class="el-icon-info icon"></i></el-tag>
                      <div slot="content">异常原因：显示异常原因</div>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    <p>培训方案名称培训方案名称培训方案名称培训方案名称</p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="期别数量" min-width="110" align="center">
                <template>3</template>
              </el-table-column>
              <el-table-column label="报名学时" min-width="110" align="center">
                <template>50</template>
              </el-table-column>
              <el-table-column label="价格" min-width="100" align="right">
                <template>50.00</template>
              </el-table-column>
              <el-table-column label="培训属性" min-width="240">
                <template>
                  <p>行业：行业行业</p>
                  <p>地区：为空，不展示</p>
                  <p>科目类型：科目类型</p>
                  <p>培训类别：培训类别</p>
                  <p>培训专业：培训专业培训专业</p>
                  <p>培训年度：2019</p>
                </template>
              </el-table-column>
              <el-table-column label="销售状态" min-width="140">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="info" class="badge-status">已下架</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">已上架</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="最新修改时间" sortable min-width="180">
                <template>2021-10-15 00:21:21</template>
              </el-table-column>
              <el-table-column label="是否仅导入开通" min-width="180">
                <template>否</template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text" disabled>修改</el-button>
                    <el-button type="text">详情</el-button>
                    <el-button type="text">复制</el-button>
                    <el-button type="text">立即下架</el-button>
                    <el-button type="text">删除</el-button>
                    <el-button type="text">查看期别</el-button>
                    <el-button type="text">实施管理</el-button>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <el-button type="text">修改</el-button>
                    <el-button type="text">详情</el-button>
                    <el-button type="text">复制</el-button>
                    <el-button type="text">立即上架</el-button>
                    <el-button type="text">删除</el-button>
                    <el-button type="text">查看期别</el-button>
                    <el-button type="text">实施管理</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text">修改</el-button>
                    <el-button type="text">详情</el-button>
                    <el-button type="text">复制</el-button>
                    <el-button type="text">立即下架</el-button>
                    <el-button type="text">删除</el-button>
                    <el-button type="text">查看期别</el-button>
                    <el-button type="text">实施管理</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="面网授" name="third">
          <el-card shadow="never" class="m-card">
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="培训方案名称" min-width="300" fixed="left">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <p><el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag>培训方案名称</p>
                    <el-tag type="warning">处理中</el-tag>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <p>
                      <el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag>培训方案名称培训方案名称
                    </p>
                    <el-tooltip class="item" effect="dark" placement="bottom" popper-class="m-tooltip">
                      <el-tag type="danger">异常<i class="el-icon-info icon"></i></el-tag>
                      <div slot="content">异常原因：显示异常原因</div>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    <p>
                      <el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag
                      >培训方案名称培训方案名称培训方案名称培训方案名称
                    </p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="期别数量" min-width="110" align="center">
                <template>3</template>
              </el-table-column>
              <el-table-column label="报名学时" min-width="110" align="center">
                <template>50</template>
              </el-table-column>
              <el-table-column label="价格" min-width="100" align="right">
                <template>50.00</template>
              </el-table-column>
              <el-table-column label="培训属性" min-width="240">
                <template>
                  <p>行业：行业行业</p>
                  <p>地区：为空，不展示</p>
                  <p>科目类型：科目类型</p>
                  <p>培训类别：培训类别</p>
                  <p>培训专业：培训专业培训专业</p>
                  <p>培训年度：2019</p>
                </template>
              </el-table-column>
              <el-table-column label="销售状态" min-width="140">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="info" class="badge-status">已下架</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">已上架</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="学习起止时间" min-width="220">
                <template>
                  <p><el-tag type="info" size="mini">起始</el-tag>2021-10-15 00:21:21</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>2021-10-15 00:21:21</p>
                </template>
              </el-table-column>
              <el-table-column label="最新修改时间" sortable min-width="180">
                <template>2021-10-15 00:21:21</template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text" disabled>修改</el-button>
                    <el-button type="text">详情</el-button>
                    <el-button type="text">复制</el-button>
                    <el-button type="text">立即下架</el-button>
                    <el-button type="text">删除</el-button>
                    <el-button type="text">查看期别</el-button>
                    <el-button type="text">实施管理</el-button>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <el-button type="text">修改</el-button>
                    <el-button type="text">详情</el-button>
                    <el-button type="text">复制</el-button>
                    <el-button type="text">立即上架</el-button>
                    <el-button type="text">删除</el-button>
                    <el-button type="text">查看期别</el-button>
                    <el-button type="text">实施管理</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text">修改</el-button>
                    <el-button type="text">详情</el-button>
                    <el-button type="text">复制</el-button>
                    <el-button type="text">立即下架</el-button>
                    <el-button type="text">删除</el-button>
                    <el-button type="text">查看期别</el-button>
                    <el-button type="text">实施管理</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
