/**
 *
 *
 * @author: puxf
 * @date: 2021/2/4
 */
export class RollPictureDetail {
  /**
   * id
   */
  id: string
  /**
   * 轮播图类型
   @see com.fjhb.btpx.platform.service.rollpicture.RollPictureTypeEnum
   */
  type: string
  /**
   * 轮播图附件地址
   */
  attachmentUrl: string
  /**
   * 链接地址
   */
  url: string
  /**
   * 轮播图描述
   */
  description: string
  /**
   * 创建人
   */
  creator: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 轮播图状态 0-停用 1-启用
   */
  status: number
  /**
   * 轮播图排序
   */
  sort?: number
  /**
   * 是否内置
   */
  internal: boolean
}
