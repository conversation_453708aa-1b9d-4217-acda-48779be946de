import { BatchOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { BatchOrderStatusEnum } from '@api/service/centre/trade/batch/order/enum/BatchOrderStatusList'
import BatchOrderUtils from '@api/service/centre/trade/batch/order/query/util/BatchOrderUtils'
import BatchOrderDetailInvoiceInfoVo from '@api/service/centre/trade/batch/order/query/vo/BatchOrderDetailInvoiceInfoVo'
import TradeModule from '@api/service/centre/trade/TradeModule'

/**
 * @description 集体报名列表详情
 */
class BatchOrderListDetailVo {
  /**
   * 集体报名批次号
   */
  batchOrderNo = ''

  /**
   * 报名时间
   */
  signUpDate = ''

  /**
   * 交易完成时间
   */
  paymentDate = ''

  /**
   * 报名人次
   */
  signUpTimes: number = null

  /**
   * 金额
   */
  amount: number = null

  /**
   * 实付金额
   */
  paymentAmount: number = null
  /**
   * 是否为线下支付
    1: 线上付款单
    2: 线下付款单
    3: 无需付款的付款单
   */
  paymentOrderType = -1
  /**
   * 订单状态 1：待报名 2：报名中 3：待付款 4：支付中 5：汇款凭证待审核 6：开通中 7：交易成功 8：交易关闭
   */
  orderStatus: BatchOrderStatusEnum = null

  /**
   * 是否已经申请发票
   */
  isInvoiceApplied: boolean = null

  /**
   * 发票信息
   */
  invoiceInfoList: BatchOrderDetailInvoiceInfoVo[] = []

  /**
   * 批次单退款状态
   * 0: 未退款 1: 退款中 2: 已部分退款 3: 退款完成
   */
  batchOrderRefundStatus: number = null

  /**
   * 批次单退货状态
   * 0: 未退货 1: 退货中 2: 已部分退货 3: 退货完成
   */
  batchOrderReturnStatus: number = null

  /**
   * 是否存在退款成功的子订单
   */
  isExistRefundedSubOrder: boolean = null

  /**
   * 关闭原因：0.未关闭 1. 管理员取消报名 2：系统关闭
   */
  closeType: number = null

  /**
   * 远端信息
   */
  remote: BatchOrderResponse = new BatchOrderResponse()

  /**
   * 提交批次时间
   */
  committing = ''

  /**
   * 转换远端模型为本地vo模型
   */
  static async from(response: BatchOrderResponse): Promise<BatchOrderListDetailVo> {
    const detail = new BatchOrderListDetailVo()
    detail.batchOrderNo = response.batchOrderNo ?? ''
    detail.orderStatus = BatchOrderUtils.getBatchOrderStatus(response)
    detail.signUpDate = response.basicData?.batchOrderStatusChangeTime?.unConfirmed ?? null
    detail.committing = response.basicData?.batchOrderStatusChangeTime?.committing ?? null
    detail.paymentDate = response.basicData?.batchOrderStatusChangeTime?.completed ?? null
    detail.paymentOrderType = response.payInfo?.paymentOrderType
    // 是否已开票
    detail.isInvoiceApplied = response.isInvoiceApplied ?? false
    // 报名人次、金额、实付金额
    if (detail.orderStatus === BatchOrderStatusEnum.Wait_Sign_Up) {
      detail.signUpTimes = await BatchOrderUtils.getSignUpTotalCount(detail.batchOrderNo)
      const queryRemote = TradeModule.batchOrderFactory.mutationBatchOrderFactory.getBeforeBatchOrderSignUp()
      queryRemote.collectiveSignupNo = detail.batchOrderNo
      detail.amount = (await queryRemote.querySignUpShopAllPrice()) ?? 0
      detail.paymentAmount = 0
    } else {
      detail.signUpTimes = response.basicData?.orderForBatchCount ?? 0
      detail.amount = response.basicData?.amount ?? 0
      detail.paymentAmount = detail.amount
    }
    // 开票信息【付款完成即可查询】
    if (response.basicData?.paymentStatus === 2 || detail.isInvoiceApplied) {
      detail.invoiceInfoList = await BatchOrderUtils.getBatchOrderInvoiceInfoList(response)
    }
    // 批次单退款状态
    detail.batchOrderRefundStatus = response.basicData?.batchOrderRefundStatus ?? null
    // 批次单退货状态
    detail.batchOrderReturnStatus = response.basicData?.batchOrderReturnStatus ?? null
    // 是否存在退款成功的子订单
    if (detail.orderStatus === BatchOrderStatusEnum.Pay_Success) {
      const response =
        await TradeModule.batchOrderFactory.queryBatchOrderFactory.queryBatchOrderList.queryBatchOrderRefundInfoListStatistic(
          detail.batchOrderNo
        )
      detail.isExistRefundedSubOrder = response.refundAmount > 0 ? true : false
    }
    detail.remote = response ?? new BatchOrderResponse()
    detail.closeType = response?.basicData?.batchOrderCloseReason?.closedType ?? null
    return detail
  }
}

export default BatchOrderListDetailVo
