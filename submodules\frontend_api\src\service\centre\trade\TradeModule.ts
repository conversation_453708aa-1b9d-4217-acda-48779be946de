import store from '@/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import BatchOrderFactory from '@api/service/centre/trade/batch/BatchOrderFactory'
/**
 * @description 交易状态层
 */
@Module({ namespaced: true, dynamic: true, name: 'CentreTradeModule', store })
class TradeModule extends VuexModule {
  /**
   * 集体订单工厂
   */
  batchOrderFactory = new BatchOrderFactory()
}

export default getModule(TradeModule)
