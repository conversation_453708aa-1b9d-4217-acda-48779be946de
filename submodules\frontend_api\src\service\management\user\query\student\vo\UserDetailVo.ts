import {
  RegionModel,
  SectionAndSubjects,
  StudentCertificateResponse,
  StudentIndustryResponse,
  StudentInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import AccountSourceTypes, { AccountSourceEnum } from '@api/service/common/enums/user/AccountSourceTypes'
import GenderTypes, { GendersEnum } from '@api/service/common/enums/user/GenderTypes'
import JsStudentIndustryInfoVo from './JsStudentIndustryInfoVo'
import RsStudentIndustryInfoVo from './RsStudentIndustryInfoVo'
import StudentCertificateInfoVo from './StudentCertificateInfoVo'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import GqStudentIndustryInfoVo from '@api/service/management/user/query/student/vo/GqStudentIndustryInfoVo'
import WsStudentIndustryInfoVo from '@api/service/management/user/query/student/vo/WsStudentIndustryInfoVo'
import LsStudentIndustryInfoVo from '@api/service/management/user/query/student/vo/LsStudentIndustryInfoVo'
import YsStudentIndustryInfoVo from '@api/service/management/user/query/student/vo/YsStudentIndustryInfoVo'
import { IdentityCardType } from '@api/service/management/online-school-config/functionality-setting/enum/IdentityCardTypeEnum'
import { IdentityTypeEnum } from '@api/service/common/enums/user/IdentityType'

/*
 * 用户详情
 */
class UserDetailVo {
  /*
   * 基本信息 */
  /**
   * 用户昵称
   */
  nickName: string = undefined
  /**
   * 单位所属地区
   */
  region: RegionModel = new RegionModel()
  /**
   * 单位所属地区（新）
   */
  companyRegion: RegionModel = new RegionModel()
  /**
   * 工作单位名称
   */
  companyName: string = undefined
  /**
   * 工作单位统一社会信用代码
   */
  companyCode: string = undefined
  /**
   * 头像地址
   */
  photo: string = undefined
  /**
   * 联系地址
   */
  address: string = undefined

  /*
    账户id
  */
  accountId: string = undefined

  /**
   * 用户id
   */
  userId: string = undefined
  /**
   * 用户名称
   */
  userName: string = undefined
  /**
   * 性别
   */
  gender: GendersEnum = undefined
  /**
   * 性别名称
   * 【页面用】
   */
  genderName: string = undefined
  /**
   * 登录账号
   */
  loginAccount: string = undefined
  /**
   * 证件号
   */
  idCard: string = undefined
  /**
   * 证件类型
   */
  idCardType: number = undefined
  /**
   * 手机号
   */
  phone: string = undefined
  /**
   * 注册时间
   */
  createTime: string = undefined
  /**
   * 邮箱
   */
  email: string = undefined
  /**
   * 注册方式
   */
  registerType: number = undefined
  /**
   * 来源类型
   */
  sourceType: AccountSourceEnum = undefined
  /**
   * 来源类型
   * 【页面用】
   */
  sourceTypeName: string = undefined

  /*
   * 微信相关信息 */
  /**
   * 是否绑定微信
   */
  bindWX: boolean = undefined
  /**
   * 微信昵称
   */
  nickNameByWX: string = undefined
  /**
   * 人社行业信息
   */
  rsStudentIndustryInfo = new RsStudentIndustryInfoVo()
  /**
   * 建设行业信息
   */
  jsStudentIndustryInfo = new JsStudentIndustryInfoVo()
  /**
   * 卫生行业信息
   */
  wsStudentIndustryInfo = new WsStudentIndustryInfoVo()
  /**
   * 工勤行业信息
   */
  gqStudentIndustryInfo = new GqStudentIndustryInfoVo()
  /**
   * 教师行业信息
   */
  lsStudentIndustryInfo = new LsStudentIndustryInfoVo()
  /**
   * 药师行业信息
   */
  ysStudentIndustryInfo = new YsStudentIndustryInfoVo()
  from(item: StudentInfoResponse, nameMap?: Map<string, string>) {
    this.accountId = item.accountInfo?.accountId
    this.idCardType = Number(item.userInfo?.idCardType)
    this.nickName = item.userInfo?.nickName
    this.region = item.userInfo?.region
    this.companyName = item.userInfo?.companyName
    this.companyCode = item.userInfo?.companyCode
    this.photo = item.userInfo?.photo
    this.address = item.userInfo?.address
    this.userId = item.userInfo?.userId
    this.userName = item.userInfo?.userName
    this.gender = item.userInfo?.gender
    this.genderName = GenderTypes.map.get(item.userInfo?.gender)
    this.idCard = item.userInfo?.idCard
    this.loginAccount = item.authenticationList?.find(item => {
      return item.identityType
    })?.identity
    this.phone = item.userInfo?.phone
    this.createTime = item.accountInfo?.createdTime
    this.email = item.userInfo?.email
    this.registerType = item.accountInfo?.registerType
    this.sourceType = item.accountInfo?.sourceType
    this.sourceTypeName = AccountSourceTypes.map.get(item.accountInfo?.sourceType)
    this.bindWX = item.openPlatformBind?.bindWX
    this.nickNameByWX = item.openPlatformBind?.nickNameByWX
    this.companyRegion = item.userInfo?.companyRegion
    this.loginAccount = item.authenticationList?.find(
      item => item.identityType === IdentityTypeEnum.user_name
    )?.identity
    // 行业信息外面处理
    if (!item.userInfo?.userIndustryList?.length) {
      this.rsStudentIndustryInfo = null
      this.jsStudentIndustryInfo = null
    } else {
      item.userInfo?.userIndustryList?.forEach((sub: StudentIndustryResponse) => {
        if (sub.industryId === IndustryIdEnum.RS) {
          // 人社
          this.rsStudentIndustryInfo.industryId = sub.industryId
          this.rsStudentIndustryInfo.firstProfessionalCategory = sub.firstProfessionalCategory
          this.rsStudentIndustryInfo.secondProfessionalCategory = sub.secondProfessionalCategory
          this.rsStudentIndustryInfo.firstProfessionalCategoryName = nameMap?.get(sub.firstProfessionalCategory) || ''
          this.rsStudentIndustryInfo.secondProfessionalCategoryName = nameMap?.get(sub.secondProfessionalCategory) || ''
          this.rsStudentIndustryInfo.professionalQualification = sub.professionalQualification
          this.rsStudentIndustryInfo.professionalQualificationName = nameMap?.get(sub.professionalQualification) || ''
        }
        if (sub.industryId === IndustryIdEnum.JS) {
          // 建设
          this.jsStudentIndustryInfo.industryId = sub.industryId
          sub.userCertificateList?.length &&
            sub.userCertificateList.forEach(item => {
              const certificate = new StudentCertificateInfoVo()
              certificate.certificateId = item.certificateId
              certificate.certificateCode = item.certificateNo
              certificate.certificateCategory = item.certificateCategory
              certificate.registerProfessional = item.registerProfessional
              certificate.releaseStartTime = item.releaseStartTime
              certificate.certificateEndTime = item.certificateEndTime
              certificate.attachmentInfoList = item.attachmentList
              certificate.certificateCategoryName = (nameMap && nameMap.get(item.certificateCategory)) || ''
              certificate.registerProfessionalName = (nameMap && nameMap.get(item.registerProfessional)) || ''
              certificate.certificateNo = item.certificateNo
              certificate.attachmentList = item.attachmentList
              this.jsStudentIndustryInfo.userCertificateList.push(certificate)
            })
        }
        if (sub.industryId === IndustryIdEnum.GQ) {
          // 工勤
          this.gqStudentIndustryInfo.industryId = sub.industryId
          this.gqStudentIndustryInfo.jobCategoryId = sub.jobCategoryId
          this.gqStudentIndustryInfo.professionalLevel = sub.professionalLevel
          this.gqStudentIndustryInfo.jobCategoryName = nameMap && nameMap.get(sub.jobCategoryId)
          this.gqStudentIndustryInfo.professionalLevelName = nameMap && nameMap.get(sub.professionalLevel)
        }
        if (sub.industryId === IndustryIdEnum.WS) {
          // 卫生
          this.wsStudentIndustryInfo.industryId = sub.industryId
          this.wsStudentIndustryInfo.personnelCategory = sub.personnelCategory
          this.wsStudentIndustryInfo.positionCategory = sub.positionCategory
          this.wsStudentIndustryInfo.personnelCategoryName = nameMap && nameMap.get(sub.personnelCategory)
          this.wsStudentIndustryInfo.positionCategoryName = nameMap && nameMap.get(sub.positionCategory)
        }
        if (sub.industryId === IndustryIdEnum.LS) {
          // 教师
          this.lsStudentIndustryInfo.industryId = sub.industryId
          this.lsStudentIndustryInfo.sectionAndSubjects = new Array<SectionAndSubjects>()
          sub.sectionAndSubjects?.length &&
            sub.sectionAndSubjects.forEach(item => {
              this.lsStudentIndustryInfo.sectionAndSubjectsName.push({
                section: nameMap && nameMap.get('section' + item.section),
                subjects: nameMap && nameMap.get('subject' + item.subjects)
              })
              this.lsStudentIndustryInfo.sectionAndSubjects.push({
                section: item.section,
                subjects: item.subjects
              })
            })
        }
        if (sub.industryId === IndustryIdEnum.YS) {
          this.ysStudentIndustryInfo.industryId = sub.industryId
          this.ysStudentIndustryInfo.certificatesType = sub.certificatesType
          this.ysStudentIndustryInfo.practitionerCategory = sub.practitionerCategory
          this.ysStudentIndustryInfo.certificatesTypeName = nameMap && nameMap.get(sub.certificatesType)
          this.ysStudentIndustryInfo.practitionerCategoryName = nameMap && nameMap.get(sub.practitionerCategory)
        }
      })
    }
  }
}

export default UserDetailVo
