import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-training-channel-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-training-channel-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * @Description 附件请求
<AUTHOR>
@Date 2024/3/20 9:56
 */
export class Attachment {
  /**
   * 附件名称
   */
  name?: string
  /**
   * 附件地址
   */
  url?: string
}

/**
 * 从专题中移除方案请求
 */
export class RemoveSchemeOfTopicRequest {
  /**
   * 专题ID
   */
  ptcId: string
  /**
   * 学习方案ID
   */
  schemeId: string
}

/**
 * @author: linxiquan
@Date: 2024/1/11 19:00
@Description: 保存专题基础信息请求
 */
export class SaveTrainingChannelInfoRequest {
  /**
   * 专题入口名称
   */
  entryName: string
  /**
   * 专题名称
   */
  name: string
  /**
   * 域名类型
DEFAULT &#x3D; 1; 系统默认域名
CUSTOM &#x3D; 2；自定义域名
   */
  domainNameType: number
  /**
   * 专题域名
   */
  domainName: string
  /**
   * 专题类型(已废弃，改用新专题类型字段complexTypes,适配类型多选场景)
   */
  type: Array<number>
  /**
   * 复合专题类型
1-地区  2-行业  3-单位
   */
  complexTypes: Array<number>
  /**
   * 所属单位
   */
  unitName?: string
  /**
   * 专题适用地区 类型为地区时，存储地区code集合
   */
  suiteAreaList?: Array<string>
  /**
   * 专题适用行业  类型为行业时，存储行业id集合
   */
  suiteIndustryList?: Array<string>
  /**
   * H5端专题模板编号
   */
  h5TemplateNo: string
  /**
   * PC端专题模板编号
   */
  pcTemplateNo: string
  /**
   * 是否显示在网校
   */
  showOnNetSchool: boolean
  /**
   * 是否不允许访问 true-不允许 false -允许
   */
  notAllowAccess: boolean
}

/**
 * @Description 保存专题线下集体报名配置信息
<AUTHOR>
@Date 2024/3/26 15:42
 */
export class SaveTrainingChannelOfflineCollectiveSignUpSettingsRequest {
  /**
   * 专题ID
   */
  trainingChannelId: string
  /**
   * 专题线下集体报名入口开关
   */
  openEntrySwitch: boolean
  /**
   * 专题线下集体报名入口图片附件
   */
  entryPictureAttachments?: Array<Attachment>
  /**
   * 线下集体报名名称
   */
  name?: string
  /**
   * 专题线下集体报名模板地址
   */
  templateAttachment?: Attachment
  /**
   * 访问链接
   */
  accessUrl?: string
  /**
   * 底部文本说明内容
   */
  bottomDescription?: string
  /**
   * 报名步骤信息
   */
  signUpSteps?: Array<SignUpStep>
}

/**
 * @Description 保存专题线上集体报名配置信息
<AUTHOR>
@Date 2024/3/20 9:51
 */
export class SaveTrainingChannelOnlineCollectiveSignUpSettingsRequest {
  /**
   * 专题ID
   */
  trainingChannelId: string
  /**
   * 专题线上集体报名入口开关
   */
  openEntrySwitch: boolean
  /**
   * 专题线上集体报名入口图片开关
   */
  openEntryPictureSwitch: boolean
  /**
   * 专题线上集体报名入口图片附件
   */
  entryPictureAttachments?: Array<Attachment>
  /**
   * 专题线上集体报名模版地址
   */
  templateAttachment?: Attachment
  /**
   * 展示报名班级链接地址
   */
  showSignUpClassUrl?: string
}

/**
 * @author: linxiquan
@Date: 2024/1/11 20:20
@Description: 保存专题门户信息请求
 */
export class SaveTrainingChannelPortalInfoRequest {
  /**
   * 专题id
   */
  id: string
  /**
   * 专题门户logo类型
CHARACTER &#x3D; 1; 文字
PICTURE &#x3D; 2；图片
com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.LogoTypes
   */
  logoType: number
  /**
   * 专题门户logo名称  专题门户logo类型为文字时，有值
   */
  logoName?: string
  /**
   * 专题门户logo图片地址  专题门户logo类型为图片时，有值
   */
  logoPictureUrl?: string
  /**
   * 客服电话类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.CustomerServicePhoneTypes
   */
  customerServicePhoneType?: number
  /**
   * 客服电话
   */
  customerServicePhone?: string
  /**
   * 客服电话图片
   */
  customerServicePhonePictureUrl?: string
  /**
   * 培训流程类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
@see com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.TrainingProcessTypes
   */
  trainingProcessType?: number
  /**
   * 培训流程图片
   */
  trainingProcessAttachments?: Array<Attachment>
  /**
   * 企业微信客服类型
@see  com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.CustomerServicePhoneTypes
   */
  enterpriseWechatCustomerType?: number
  /**
   * 企业微信客服类型
   */
  enterpriseWechatCustomerAttachments?: Array<Attachment>
  /**
   * 咨询时间
   */
  seekTime?: string
  /**
   * 专题门户底部落款类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.PortalBottomShowTypes
   */
  bottomShowType: number
  /**
   * 专题门户底部落款内容Id 专题门户底部落款类型为自定义时 有值
   */
  bottomShowContentId?: string
  /**
   * 专题门户底部落款内容
   */
  bottomShowContent?: string
  /**
   * 专题门户轮播图
   */
  trainingChannelPortalBannerList?: Array<TrainingChannelPortalBanner>
}

/**
 * @author: linxiquan
@Date: 2024/1/11 19:47
@Description: 设置专题基础配置
 */
export class SetTrainingChannelSettingRequest {
  /**
   * 专题基础配置id （首次保存为空）
   */
  id?: string
  /**
   * 是否开启专题
   */
  isOpenTrainingChannel: boolean
  /**
   * 是否开启网校展示入口
   */
  isShowEntry: boolean
  /**
   * 网校展示专题方案
   */
  isShowTrainingChannelScheme: boolean
  /**
   * 专题入口名称
   */
  name?: string
  /**
   * 专题引导语
   */
  guideExpression?: string
  /**
   * 专题配置拓展属性
   */
  trainingChannelSettingExtReq?: Array<TrainingChannelSettingExtReq>
}

/**
 * @Description 线下集体报名的步骤信息
<AUTHOR>
@Date 2024/3/26 16:07
 */
export class SignUpStep {
  /**
   * 步骤序号
   */
  index: number
  /**
   * 步骤标题
   */
  title?: string
  /**
   * 步骤内容
   */
  content?: string
  /**
   * 步骤内容ID【更新时需上传】
   */
  contentId?: string
}

/**
 * 专题配置拓展信息
 */
export class TrainingChannelSettingExtReq {
  /**
   * 专题配置id
   */
  trainingChannelSettingId?: string
  /**
   * 拓展属性id
   */
  objectId?: string
  /**
   * 拓展属性类型
   */
  type?: number
  /**
   * 拓展属性key
   */
  key?: string
  /**
   * 拓展属性value
   */
  value?: string
}

/**
 * @author: linxiquan
@Date: 2024/1/11 19:26
@Description: 更新专题基础信息请求
 */
export class UpdateTrainingChannelInfoRequest {
  /**
   * 专题基础信息id
   */
  id: string
  /**
   * 专题入口名称
   */
  entryName: string
  /**
   * 专题名称
   */
  name: string
  /**
   * 域名类型
DEFAULT &#x3D; 1; 系统默认域名
CUSTOM &#x3D; 2；自定义域名
   */
  domainNameType: number
  /**
   * 专题域名
   */
  domainName: string
  /**
   * 专题类型(已废弃，改用新专题类型字段complexTypes,适配类型多选场景)
   */
  type: Array<number>
  /**
   * 复合专题类型
1-地区  2-行业  3-单位
   */
  complexTypes: Array<number>
  /**
   * 所属单位
   */
  unitName?: string
  /**
   * 专题适用地区 类型为地区时，存储地区code集合
   */
  suiteAreaList?: Array<string>
  /**
   * 专题适用行业  类型为行业时，存储行业id集合
   */
  suiteIndustryList?: Array<string>
  /**
   * H5端专题模板编号
   */
  h5TemplateNo: string
  /**
   * PC端专题模板编号
   */
  pcTemplateNo: string
  /**
   * 是否显示在网校
   */
  showOnNetSchool: boolean
  /**
   * 是否不允许访问 true-不允许 false -允许
   */
  notAllowAccess: boolean
}

/**
 * @Description 更新专题线下集体报名配置信息
<AUTHOR>
@Date 2024/3/26 15:42
 */
export class UpdateTrainingChannelOfflineCollectiveSignUpSettingsRequest {
  /**
   * 专题线上集体报名配置id
   */
  id?: string
  /**
   * 专题线下集体报名入口开关
   */
  openEntrySwitch: boolean
  /**
   * 专题线下集体报名入口图片附件
   */
  entryPictureAttachments?: Array<Attachment>
  /**
   * 线下集体报名名称
   */
  name?: string
  /**
   * 专题线下集体报名模板地址
   */
  templateAttachment?: Attachment
  /**
   * 访问链接
   */
  accessUrl?: string
  /**
   * 底部文本说明内容
   */
  bottomDescription?: string
  /**
   * 报名步骤信息
   */
  signUpSteps?: Array<SignUpStep>
}

/**
 * @Description TODO
<AUTHOR>
@Date 2024/3/22 16:05
 */
export class UpdateTrainingChannelOnlineCollectiveSignUpSettingsRequest {
  /**
   * 线上集体报名配置ID
   */
  id: string
  /**
   * 专题线上集体报名入口开关
   */
  openEntrySwitch: boolean
  /**
   * 专题线上集体报名入口图片开关
   */
  openEntryPictureSwitch: boolean
  /**
   * 专题线上集体报名入口图片附件
   */
  entryPictureAttachments?: Array<Attachment>
  /**
   * 专题线上集体报名模版地址
   */
  templateAttachment?: Attachment
  /**
   * 展示报名班级链接地址
   */
  showSignUpClassUrl?: string
}

/**
 * @author: linxiquan
@Date: 2024/1/11 20:20
@Description: 更新专题门户信息请求
 */
export class UpdateTrainingChannelPortalInfoRequest {
  /**
   * 专题id
   */
  id: string
  /**
   * 专题门户id
   */
  trainingChannelPortalId: string
  /**
   * 专题门户logo类型
CHARACTER &#x3D; 1; 文字
PICTURE &#x3D; 2；图片
com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.LogoTypes
   */
  logoType: number
  /**
   * 专题门户logo名称  专题门户logo类型为文字时，有值
   */
  logoName?: string
  /**
   * 专题门户logo图片地址  专题门户logo类型为图片时，有值
   */
  logoPictureUrl?: string
  /**
   * 客服电话类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.CustomerServicePhoneTypes
   */
  customerServicePhoneType?: number
  /**
   * 客服电话
   */
  customerServicePhone?: string
  /**
   * 客服电话图片
   */
  customerServicePhonePictureUrl?: string
  /**
   * 培训流程类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
@see com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.TrainingProcessTypes
   */
  trainingProcessType?: number
  /**
   * 培训流程图片
   */
  trainingProcessAttachments?: Array<Attachment>
  /**
   * 企业微信客服类型
@see  com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.CustomerServicePhoneTypes
   */
  enterpriseWechatCustomerType?: number
  /**
   * 企业微信客服类型
   */
  enterpriseWechatCustomerAttachments?: Array<Attachment>
  /**
   * 咨询时间
   */
  seekTime?: string
  /**
   * 专题门户底部落款类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.PortalBottomShowTypes
   */
  bottomShowType: number
  /**
   * 专题门户底部落款内容Id 专题门户底部落款类型为自定义时 有值
   */
  bottomShowContentId?: string
  /**
   * 专题门户底部落款内容
   */
  bottomShowContent?: string
  /**
   * 专题门户轮播图
   */
  trainingChannelPortalBannerList?: Array<TrainingChannelPortalBanner>
}

/**
 * @author: linxiquan
@Date: 2024/1/16 8:42
@Description: 更新专题排序请求
 */
export class UpdateTrainingChannelSortRequest {
  /**
   * 专题id
   */
  id?: string
  /**
   * 排序值
   */
  sort?: number
}

/**
 * @author: linxiquan
@Date: 2024/1/23 18:46
@Description: 校验专题入口名称是否唯一请求
 */
export class ValidTrainingChannelEntryNameUniqueRequest {
  /**
   * 专题入口名称
   */
  entryName?: string
  /**
   * 专题id
   */
  trainingChannelId?: string
}

/**
 * @Description 批量更新方案展示请求
<AUTHOR>
@Date 2025/7/2 16:00
 */
export class BatchUpdateSchemeRequest {
  /**
   * 文件路径
   */
  filePath?: string
  /**
   * 文件名
   */
  fileName?: string
}

export class DeleteScheme {
  /**
   * 学习方案id
   */
  schemeId?: string
}

/**
 * @Description 导入更新方案门户展示任务查询请求
<AUTHOR>
@Date 2025/7/8 11:50
 */
export class ImportSchemePortalShowTaskQueryRequest {
  /**
   * 任务类型
   */
  caetgory?: string
  /**
   * 任务名称
   */
  taskName?: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState?: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult?: number
  /**
   * 执行时间（起始）
   */
  executeStartTime?: string
  /**
   * 执行时间（终止）
   */
  executeEndTime?: string
}

/**
 * @author: linxiquan
@Date: 2024/1/11 20:25
@Description: 保存专题学习方案信息
 */
export class SaveTrainingChannelSchemeRequest {
  /**
   * 专题id
   */
  id: string
  /**
   * 新增方案
   */
  addScheme?: Array<SchemeList>
  /**
   * 更新方案
   */
  updateScheme?: Array<SchemeList>
  /**
   * 删除方案
   */
  deleteScheme?: Array<DeleteScheme>
}

export class SchemeList {
  /**
   * 学习方案id
   */
  schemeId?: string
  /**
   * 排序
   */
  sort?: number
}

/**
 * @Description 修改销售配置请求
<AUTHOR>
@Date 2025/7/2 16:43
 */
export class UpdateSaleSettingRequest {
  /**
   * 专题id【必填】
   */
  id?: string
  /**
   * 学习方案id【必填】
   */
  schemeId?: string
  /**
   * 是否展示专题门户【必填】
   */
  showPortal: boolean
  /**
   * 展示对象类型【必填】 1-用户 2-集体报名管理员
   */
  showObjectTypes?: Array<number>
}

/**
 * @Description 删除时的精品课程
<AUTHOR>
@Date 2024/3/21 9:02
 */
export class DeleteSelectedCourse {
  /**
   * 课程id
   */
  courseId?: string
}

/**
 * @Description 保存专题精品课程信息请求
<AUTHOR>
@Date 2024/3/21 8:31
 */
export class SaveTrainingChannelSelectedCourseRequest {
  /**
   * 专题id
   */
  id?: string
  /**
   * 专题精品课程
   */
  trainingChannelSelectedCourses?: Array<TrainingChannelSelectedCourse>
}

/**
 * @Description 选择的课程信息
<AUTHOR>
@Date 2024/3/21 8:42
 */
export class SelectedCourse {
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 排序
   */
  sort: number
}

/**
 * @Description 各个分类的精品课程
<AUTHOR>
@Date 2024/3/21 8:57
 */
export class TrainingChannelSelectedCourse {
  /**
   * 精品课程分类id
   */
  selectedCourseCategoryId?: string
  /**
   * 添加的精品课程
   */
  addSelectedCourse?: Array<SelectedCourse>
  /**
   * 修改的精品课程
   */
  updateSelectedCourse?: Array<SelectedCourse>
  /**
   * 删除的精品课程
   */
  deleteSelectedCourse?: Array<DeleteSelectedCourse>
}

/**
 * @author: linxiquan
@Date: 2024/1/11 20:12
@Description: 专题门户轮播图
 */
export class TrainingChannelPortalBanner {
  /**
   * 专题门户轮播图id，在新增时不填
   */
  id?: string
  /**
   * 专题门户轮播图类型
WEB &#x3D; 1; web端
H5 &#x3D; 2；H5端
com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.BannerTypes
   */
  type: number
  /**
   * 专题门户轮播图地址
   */
  pictureUrl?: string
  /**
   * 链接地址
   */
  linkUrl?: string
  /**
   * 排序值
   */
  sort: number
}

export class EachStateCount {
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  state: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 数量
   */
  count: number
}

/**
 * <AUTHOR> [2023/7/11 20:54]
 */
export class GenernalResponse {
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * @Description 导出更新培训方案展示信息结果
<AUTHOR>
@Date 2025/7/4 9:51
 */
export class ExportUpdateSchemeShowResultResponse {
  fileUrl: string
}

/**
 * @author: linxiquan
@Date: 2024/1/11 19:53
@Description: 获取网校专题基础配置信息响应
 */
export class GetTrainingChannelSettingResponse {
  /**
   * 专题基础配置id
   */
  id: string
  /**
   * 是否开启专题
   */
  isOpenTrainingChannel: boolean
  /**
   * 网校展示专题方案
   */
  isShowTrainingChannelScheme: boolean
  /**
   * 是否开启网校展示入口
   */
  isShowEntry: boolean
  /**
   * 专题入口名称
   */
  name: string
  /**
   * 专题引导语
   */
  guideExpression: string
  /**
   * 专题配置拓展信息
   */
  trainingChannelSettingExtList: Array<TrainingChannelSettingExt>
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * 保存专题基础信息响应
 */
export class SaveTrainingChannelInfoResponse {
  /**
   * 专题id
   */
  id: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * @Description 保存专题精品课程信息响应
<AUTHOR>
@Date 2024/3/21 10:03
 */
export class SaveTrainingChannelSelectedCourseResponse {
  /**
   * 课程id 集合
   */
  ids: Array<string>
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * @Description 更新方案门户显示结果信息
<AUTHOR>
@Date 2025/7/8 11:43
 */
export class UpdateSchemePortalResultTaskInfoResponse {
  /**
   * 任务编号
   */
  id: string
  /**
   * 【必填】平台编号
   */
  platformId: string
  /**
   * 【必填】平台版本编号
   */
  platformVersionId: string
  /**
   * 【必填】项目编号
   */
  projectId: string
  /**
   * 【必填】子项目编号
   */
  subProjectId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务分类
   */
  category: string
  /**
   * 所属批次单编号
   */
  batchNo: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
  /**
   * 处理信息
   */
  message: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 就绪时间
   */
  alreadyTime: string
  /**
   * 执行时间
   */
  executingTime: string
  /**
   * 完成时间
   */
  completedTime: string
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount>
}

/**
 * 门户信息响应
 */
export class TrainingChannelPortalInfoResponse {
  /**
   * 专题门户id
   */
  trainingChannelPortalId: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * @Date: 2024/1/12 15:16
@Description: 专题配置拓展信息仓储模型
 */
export class TrainingChannelSettingExt {
  /**
   * 专题配置拓展信息id
   */
  id: string
  /**
   * 专题配置id
   */
  trainingChannelSettingId: string
  /**
   * 拓展属性id
   */
  objectId: string
  /**
   * 拓展属性类型
   */
  type: number
  /**
   * 拓展属性key
   */
  key: string
  /**
   * 拓展属性value
   */
  value: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updateTime: string
  /**
   * 创建人(userId)
   */
  createdUserId: string
}

export class UpdateSchemePortalResultTaskInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UpdateSchemePortalResultTaskInfoResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出批量更新方案展示全部结果
   * @param mainTaskId 主任务id
   * @return 导出结果url
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportAllUpdateSchemeShowResult(
    mainTaskId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportAllUpdateSchemeShowResult,
    operation?: string
  ): Promise<Response<ExportUpdateSchemeShowResultResponse>> {
    return commonRequestApi<ExportUpdateSchemeShowResultResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批量更新方案展失败结果
   * @param mainTaskId 主任务id
   * @return 导出结果url
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportFailUpdateSchemeShowResult(
    mainTaskId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportFailUpdateSchemeShowResult,
    operation?: string
  ): Promise<Response<ExportUpdateSchemeShowResultResponse>> {
    return commonRequestApi<ExportUpdateSchemeShowResultResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取专题方案门户展示模版地址
   * @return 专题方案门户展示模版地址
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTainingChannelSchemePortalShowTempletePath(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTainingChannelSchemePortalShowTempletePath,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取专题方案门户展示模版导入结果
   * @param page 分页参数
   * @param request 查询参数
   * @return 专题方案门户展示导入结果
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUpdateSchemePortalResultTaskInfo(
    params: { page?: Page; request?: ImportSchemePortalShowTaskQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageUpdateSchemePortalResultTaskInfo,
    operation?: string
  ): Promise<Response<UpdateSchemePortalResultTaskInfoResponsePage>> {
    return commonRequestApi<UpdateSchemePortalResultTaskInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验专题入口名称是否唯一
   * @param request
   * @return
   * 200002 专题入口名称已存在
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validTrainingChannelEntryNameUnique(
    request: ValidTrainingChannelEntryNameUniqueRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.validTrainingChannelEntryNameUnique,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量更新方案展示
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchUpdateSchemeShow(
    request: BatchUpdateSchemeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchUpdateSchemeShow,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验学员工作单位与专题所属单位是否一致
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async compareTrainingChannelUnitWithStudentUnit(
    params: { userId?: string; trainingChannelId?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.compareTrainingChannelUnitWithStudentUnit,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 停用专题信息
   * @param id 专题id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableTrainingChannelInfo(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disableTrainingChannelInfo,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用专题信息
   * @param id 专题id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableTrainingChannelInfo(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableTrainingChannelInfo,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取网校专题基础配置信息
   * todo 疑问H5端需要提供服务商id进行查询，
   * 管理端不需要服务商id，根据上下文进行查询
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTrainingChannelSetting(
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.getTrainingChannelSetting,
    operation?: string
  ): Promise<Response<GetTrainingChannelSettingResponse>> {
    return commonRequestApi<GetTrainingChannelSettingResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 从专题里移除方案
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeSchemeOfTopic(
    request: RemoveSchemeOfTopicRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeSchemeOfTopic,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存专题基础信息
   * @param request
   * @return
   * 200001 域名已存在
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveTrainingChannelInfo(
    request: SaveTrainingChannelInfoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveTrainingChannelInfo,
    operation?: string
  ): Promise<Response<SaveTrainingChannelInfoResponse>> {
    return commonRequestApi<SaveTrainingChannelInfoResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存专题线下集体报名配置信息
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveTrainingChannelOfflineCollectiveSignUpSetting(
    request: SaveTrainingChannelOfflineCollectiveSignUpSettingsRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveTrainingChannelOfflineCollectiveSignUpSetting,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存专题线上集体报名配置信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveTrainingChannelOnlineCollectiveSignUpSetting(
    request: SaveTrainingChannelOnlineCollectiveSignUpSettingsRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveTrainingChannelOnlineCollectiveSignUpSetting,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存专题门户信息
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveTrainingChannelPortalInfo(
    request: SaveTrainingChannelPortalInfoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveTrainingChannelPortalInfo,
    operation?: string
  ): Promise<Response<TrainingChannelPortalInfoResponse>> {
    return commonRequestApi<TrainingChannelPortalInfoResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存专题学习方案信息
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveTrainingChannelScheme(
    request: SaveTrainingChannelSchemeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveTrainingChannelScheme,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存专题精品课程信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveTrainingChannelSelectedCourse(
    request: SaveTrainingChannelSelectedCourseRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveTrainingChannelSelectedCourse,
    operation?: string
  ): Promise<Response<SaveTrainingChannelSelectedCourseResponse>> {
    return commonRequestApi<SaveTrainingChannelSelectedCourseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 设置专题基础配置
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async setTrainingChannelSetting(
    request: SetTrainingChannelSettingRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.setTrainingChannelSetting,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改销售配置
   * @param request  UpdateSaleSettingRequest 修改销售配置请求
   * @return GenernalResponse 修改销售配置结果
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateSaleSetting(
    request: UpdateSaleSettingRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateSaleSetting,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新专题基础信息
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingChannelInfo(
    request: UpdateTrainingChannelInfoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingChannelInfo,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新专题线下集体报名配置信息
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingChannelOfflineCollectiveSignUpSetting(
    request: UpdateTrainingChannelOfflineCollectiveSignUpSettingsRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingChannelOfflineCollectiveSignUpSetting,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新专题线上集体报名配置信息
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingChannelOnlineCollectiveSignUpSetting(
    request: UpdateTrainingChannelOnlineCollectiveSignUpSettingsRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingChannelOnlineCollectiveSignUpSetting,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新专题门户信息
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingChannelPortalInfo(
    request: UpdateTrainingChannelPortalInfoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingChannelPortalInfo,
    operation?: string
  ): Promise<Response<TrainingChannelPortalInfoResponse>> {
    return commonRequestApi<TrainingChannelPortalInfoResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新专题排序
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingChannelSort(
    request: UpdateTrainingChannelSortRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingChannelSort,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
