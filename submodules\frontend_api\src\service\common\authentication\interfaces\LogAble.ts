import { LoginParams } from './LoginParams'
import { Response } from '@hbfe/common'

export default interface LoginAble {
  /**
   * 登录
   * @param loginParams#LoginParams
   */
  login(loginParams: LoginParams): Promise<Response<any>>

  /**
   * 不带验证码的登录
   * @param loginParams#LoginParams
   */
  loginWithoutCaptcha(loginParams: LoginParams): Promise<any>

  /**
   * 退出登录
   */
  logout(): Promise<any>
}
