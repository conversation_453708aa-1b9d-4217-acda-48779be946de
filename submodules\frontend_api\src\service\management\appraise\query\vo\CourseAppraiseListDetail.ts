import { StudentCourseAppraiseResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import MsBasicdataQueryFrontGatewayBasicDataQueryBackstage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import Appraiser from '@api/service/management/appraise/query/vo/Appraiser'

class CourseAppraiseListDetail {
  id: string
  courseId: string
  courseName: string
  content: string
  courseScore: number
  teacherScore: number
  appraiser: Appraiser = new Appraiser()
  appraisalTime: string

  static from(remoteDto: StudentCourseAppraiseResponse) {
    const detail = new CourseAppraiseListDetail()
    detail.id = remoteDto.courseAppraisalInfo.studentCourseAppraisalId
    detail.courseId = remoteDto.course.courseId
    detail.content = remoteDto.courseAppraisalInfo.content
    detail.appraisalTime = remoteDto.courseAppraisalInfo.appraisalTime
    detail.appraiser = new Appraiser()
    detail.appraiser.id = remoteDto.courseAppraisalInfo.appraisalUserId
    detail.courseScore = remoteDto.courseAppraisalInfo.courseAppraise
    detail.teacherScore = remoteDto.courseAppraisalInfo.teacherAppraise
    return detail
  }

  /**
   * 查询评价人的姓名
   */
  async queryAppraiserName() {
    const result = await MsBasicdataQueryFrontGatewayBasicDataQueryBackstage.getStudentInfoInServicer(this.appraiser.id)
    if (result.status.isSuccess()) {
      this.appraiser.name = result.data.userInfo.userName
    }
  }

  /**
   * 查询评价课程信息
   */
  async queryCourseName() {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.getCourseInServicer(this.courseId)
    if (result.status.isSuccess()) {
      this.courseName = result.data.name
    }
  }
}

export default CourseAppraiseListDetail
