/**
 * 由于用户状态层适配微服务，前端不做适配
 * 一部分枚举值不匹配的地方在这里做适配
 */
export default class UserConstantsAdapter {
  /**
   * 第三方绑定类型
   * @param openPlatformType
   */
  static getConnectType(openPlatformType: number) {
    switch (openPlatformType) {
      case 21:
        return 43
      case 2:
        return 41
      default:
        return -1
    }
  }

  /**
   * 第三方绑定类型
   * @param connectType
   */
  static getOpenPlatformType(connectType: number) {
    switch (connectType) {
      case 43:
        return 21
      case 41:
        return 2
      default:
        return -1
    }
  }

  /**
   * 性别
   * @param gender
   */
  static getSex(gender: number): number {
    switch (gender) {
      case 0: //女
        return 2
      case 1: //男
        return 1
      default:
        return -1 //未知
    }
  }

  /**
   * 性别
   * @param sex
   */
  static getGender(sex: number): number {
    switch (sex) {
      case 2: //女
        return 0
      case 1: //男
        return 1
      default:
        return -1 //未知
    }
  }
}
