/**
 *
 * @author: eleven
 * @date: 2020/4/10
 */
export class ExamConstant {
  /**
   * 试题类型key
   */
  static QUESTION_CATEGORY_KEY = 'questionCategory'
  /**
   * 真题
   */
  static QUESTION_CATEGORY_REAL = 'real'
  /**
   * 练习题
   */
  static QUESTION_CATEGORY_PRACTICE = 'practice'
  /**
   * 练习题
   */
  static QUESTION_CATEGORY_SIMULATION = 'simulation'

  /**
   * 真题
   */
  static QUESTION_CATEGORY_REAL_TITLE = '真题'
  /**
   * 练习题
   */
  static QUESTION_CATEGORY_PRACTICE_TITLE = '练习题'
  /**
   * 模拟题
   */
  static QUESTION_CATEGORY_SIMULATION_TITLE = '模拟题'

  /**
   * 判断
   */
  static QUESTION_TYPE_JUDGEMENT = 1
  /**
   * 单选
   */
  static QUESTION_TYPE_SINGLE = 2
  /**
   * 多选
   */
  static QUESTION_TYPE_MULTIPLE = 3
  /**
   * 填空
   */
  static QUESTION_TYPE_BLANK_FILLING = 4
  /**
   * 问答
   */
  static QUESTION_TYPE_ESSAY = 5
  /**
   * 案例
   */
  static QUESTION_TYPE_COMPREHENSIVE = 6
  /**
   * 量表
   */
  static QUESTION_TYPE_SCALE = 7

  /**
   * 判断
   */
  static QUESTION_TYPE_JUDGEMENT_TITLE = '判断'
  /**
   * 单选
   */
  static QUESTION_TYPE_SINGLE_TITLE = '单选'
  /**
   * 多选
   */
  static QUESTION_TYPE_MULTIPLE_TITLE = '多选'
  /**
   * 填空
   */
  static QUESTION_TYPE_BLANK_FILLING_TITLE = '填空'
  /**
   * 问答
   */
  static QUESTION_TYPE_ESSAY_TITLE = '问答'
  /**
   * 案例
   */
  static QUESTION_TYPE_COMPREHENSIVE_TITLE = '案例'
  /**
   * 量表
   */
  static QUESTION_TYPE_SCALE_TITLE = '量表'

  /**
   * 组卷方式-固定卷
   */
  static CONFIG_TYPE_FIXED = 1
  /**
   * 组卷方式-AB卷
   */
  static CONFIG_TYPE_GROUP = 2
  /**
   * 组卷方式-智能卷
   */
  static CONFIG_TYPE_RANDOM = 3
  /**
   * 组卷方式-固定卷
   */
  static CONFIG_TYPE_FIXED_TITLE = '手动组卷'
  /**
   * 组卷方式-AB卷
   */
  static CONFIG_TYPE_GROUP_TITLE = 'AB卷'
  /**
   * 组卷方式-智能卷
   */
  static CONFIG_TYPE_RANDOM_TITLE = '智能组卷'

  /**
   * 分数分配类型 - 平均分配
   */
  static SCORE_WAY_AVG = 1
  /**
   *分数分配类型 -  平均分配
   */
  static SCORE_WAY_ALONG = 2

  /**
   * 试卷计时方式 - 整卷计时
   */
  static PAPER_TIME_TYPE_WHOLE_PAPER = 0
  /**
   *试卷计时方式 -  单题计时
   */
  static PAPER_TIME_TYPE_SINGLE = 1

  /**
   * 随机卷类型 -题库卷
   */
  static RANDOM_EXAM_PAPER_TYPE_LIBRARY = 1

  /**
   * 随机卷类型 -对象卷
   * 对象匹配方式为或
   */
  static RANDOM_EXAM_PAPER_TYPE_OBJECT = 2
  /**
   * 随机卷类型 - 标签卷
   */
  static RANDOM_EXAM_PAPER_TYPE_TAG = 3

  /**
   * 随机卷类型 -对象卷
   * 对象匹配方式为且
   */
  static RANDOM_EXAM_PAPER_TYPE_MATCH_ALL_OBJECT = 4

  /**
   * 随机卷类型 - 对象卷.
   * 支持按比率（按比率时目前只支持单个object对象筛选）。
   * 当ratio=false且extractCount存在不为0的情况下，会按具体指定的数量抽题，且会校验extractCount之和是否等于count（大题的试题数量）
   */
  static RANDOM_EXAM_PAPER_TYPE_OBJECT_SUPPORT_RATIO = 5

  /**
   * 随机卷类型 -标签卷.
   * 由标签服务定义的标签抽题的卷子。
   */
  static RANDOM_EXAM_PAPER_TYPE_NEW_TAG = 6
  /**
   * 随机抽取试卷方式 -按试卷抽取
   */
  static RANDOM_WAY_EXAM_PAPER = 1
  /**
   * 随机抽取试卷方式 -按配置项（大题）抽取
   */
  static RANDOM_WAY_BIGQUESTION = 2
}
