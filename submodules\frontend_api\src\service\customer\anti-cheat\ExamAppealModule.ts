import ExamUserAppealCountModel from '@api/service/common/models/anticheat/ExamUserAppealCountModel'
import ExamUserAppealModel from '@api/service/common/models/anticheat/ExamUserAppealModel'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { ResponseStatus } from '@api/Response'
import AntiCheatGateway, {
  CurrentUserExamAppealCountInfoQueryRequest,
  CurrentUserExamAppealQueryRequest,
  ExamUserAppealCountInfoResponse,
  ExamUserAppealCreateRequest,
  ExamUserAppealResponse
} from '@api/gateway/AntiCheat-default'
import { Role, RoleType } from '@api/Secure'

interface ExamAppealState {
  /**
   * 用户考试申诉次数信息
   */
  userAppealCount: ExamUserAppealCountModel
  /**
   * 用户申诉集合
   */
  userAppealList: Array<ExamUserAppealModel>
  /**
   * 用户申诉
   */
  userAppeal: ExamUserAppealModel
}

export class ExamAppealQuery {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 考试场次ID
   */
  examRoundId: string
}

export class ExamAppealCountQuery {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 考试场次ID
   */
  examRoundId: string
}

export class ExamAppealCreateInfo {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 考试场次ID
   */
  examRoundId: string
  /**
   * 考试作答记录ID
   */
  examAnswerId: string
  /**
   * 申诉原因
   */
  reason: string
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerExamAppealModule', store })
class ExamAppealModule extends VuexModule implements ExamAppealState {
  /**
   * 用户考试申诉次数信息
   */
  public userAppealCount: ExamUserAppealCountModel = new ExamUserAppealCountModel()
  /**
   * 用户申诉集合
   */
  public userAppealList: Array<ExamUserAppealModel> = new Array<ExamUserAppealModel>()
  /**
   * 用户申诉
   */
  public userAppeal: ExamUserAppealModel = new ExamUserAppealModel()

  /**
   * 进行申诉
   * @param param 申诉信息
   */
  @Action
  @Role([RoleType.user])
  public async doAppeal(param: ExamAppealCreateInfo): Promise<ResponseStatus> {
    const createInfo = new ExamUserAppealCreateRequest()
    Object.assign(createInfo, param)
    const { data, status } = await AntiCheatGateway.createExamAppeal(createInfo)
    if (status.isSuccess()) {
      const m = new ExamUserAppealModel()
      Object.assign(m, data)
      this.SET_USER_APPEAL(m)
    }
    return Promise.resolve(status)
  }

  /**
   * 加载当前用户指定条件的申诉次数信息
   * @param param 查询条件
   */
  @Action
  @Role([RoleType.user])
  public async loadUserAppealCountInfo(param: ExamAppealCountQuery): Promise<ResponseStatus> {
    const query = new CurrentUserExamAppealCountInfoQueryRequest()
    Object.assign(query, param)
    const { data, status } = await AntiCheatGateway.findExamAppealCountInfoByCurrentUser(query)
    if (status.isSuccess()) {
      this.SET_USER_APPEAL_COUNT(data)
    }
    return Promise.resolve(status)
  }

  /**
   * 加载当前用户指定条件的考试申诉
   * @param param 查询条件
   */
  @Action
  @Role([RoleType.user])
  public async loadUserAppealList(param: ExamAppealQuery): Promise<ResponseStatus> {
    const query = new CurrentUserExamAppealQueryRequest()
    Object.assign(query, param)
    const { data, status } = await AntiCheatGateway.findExamAppealListByCurrentUser(query)
    if (status.isSuccess()) {
      this.SET_USER_APPEAL_LIST(data)
    }
    return Promise.resolve(status)
  }

  /**
   * 加载指定ID的用户申诉
   * @param id 申诉ID
   */
  @Action
  @Role([RoleType.user])
  public async loadUserAppeal(id: string): Promise<ResponseStatus> {
    const item = this.userAppealList.find(e => e.id === id)
    if (!item) {
      const { data, status } = await AntiCheatGateway.findExamUserAppealById(id)

      if (status.isSuccess()) {
        if (data) {
          const m = new ExamUserAppealModel()
          Object.assign(m, data)
          this.SET_USER_APPEAL(m)
        }
      }
      return Promise.resolve(status)
    }

    this.SET_USER_APPEAL(item)
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  @Mutation
  private SET_USER_APPEAL_LIST(param: Array<ExamUserAppealResponse>) {
    if (!param || param.length == 0) return

    const appeals = new Array<ExamUserAppealModel>()
    param.forEach(e => {
      const item = new ExamUserAppealModel()
      Object.assign(item, e)
      appeals.push(item)
    })
    this.userAppealList = appeals
  }

  @Mutation
  private SET_USER_APPEAL_COUNT(param: ExamUserAppealCountInfoResponse) {
    const m = new ExamUserAppealCountModel()
    if (param) Object.assign(m, param)

    this.userAppealCount = m
  }

  @Mutation
  private SET_USER_APPEAL(param: ExamUserAppealModel) {
    this.userAppeal = param
  }

  /**
   * 获取申诉次数信息
   */
  get getUserAppealCountInfo() {
    return (): ExamUserAppealCountModel | undefined => {
      return this.userAppealCount
    }
  }

  /**
   * 获取申诉列表
   */
  get getUserAppealList() {
    return (): Array<ExamUserAppealModel> | undefined => {
      return this.userAppealList
    }
  }

  /**
   * 获取申诉
   */
  get getUserAppeal() {
    return (): ExamUserAppealModel | undefined => {
      return this.userAppeal
    }
  }
}

export default getModule(ExamAppealModule)
