import { ResponseStatus } from '@hbfe/common'
import CommodityBaseClass from '@api/service/management/train-class/mutation/vo/CommodityBaseClass'
import MsLearningScheme from '@api/ms-gateway/ms-learningscheme-v1'
import MSCommodity from '@api/ms-gateway/ms-commodity-v1'

/**
 * 培训班商品业务对象
 */
class MutationTrainClassCommodityClass extends CommodityBaseClass {
  // region properties
  /**
   * 培训班id
   */
  schemeId = ''
  // endregion
  // region methods

  /**
   * 删除培训班
   */
  async doDeleteTrainClass(): Promise<ResponseStatus> {
    if (!this.schemeId) {
      return new ResponseStatus(8011, '培训班id不能为空')
    }
    const res = await MsLearningScheme.asyncRemoveLearningScheme({ schemeId: this.schemeId })
    return res.status
  }

  /**
   * 暂时处理
   */
  async doPutOn(): Promise<ResponseStatus> {
    if (!this.commoditySkuId) {
      return new ResponseStatus(8011, '商品id不能为空')
    }
    const res = await MSCommodity.onShelve({
      id: this.commoditySkuId
    })
    return res.status
  }

  /**
   * 下架商品
   */
  async doPuff(): Promise<ResponseStatus> {
    if (!this.commoditySkuId) {
      return new ResponseStatus(8011, '商品id不能为空')
    }
    const res = await MSCommodity.offShelve({
      id: this.commoditySkuId
    })
    return res.status
  }

  /**
   * 删除商品
   */
  async doDeleteCommodity(): Promise<ResponseStatus> {
    return Promise.resolve(new ResponseStatus(200, ''))
  }
  // endregion
}
export default MutationTrainClassCommodityClass
