import exportStudent from './queries/exportStudent.graphql'
import findAdminPage from './queries/findAdminPage.graphql'
import findAdminPageForChannelVendor from './queries/findAdminPageForChannelVendor.graphql'
import findAdminPageForCoursewareSupplier from './queries/findAdminPageForCoursewareSupplier.graphql'
import findAdminPageForTrainingInstitution from './queries/findAdminPageForTrainingInstitution.graphql'
import findStudentPage from './queries/findStudentPage.graphql'
import getAdminInfo from './queries/getAdminInfo.graphql'
import getCurrentUserBindedThirdAccounts from './queries/getCurrentUserBindedThirdAccounts.graphql'
import getCurrentUserInfo from './queries/getCurrentUserInfo.graphql'
import getStudent from './queries/getStudent.graphql'
import getStudentCount from './queries/getStudentCount.graphql'
import getUserBindedThirdAccounts from './queries/getUserBindedThirdAccounts.graphql'
import getWXUserInfo from './queries/getWXUserInfo.graphql'
import haveBindWXByOpenIdAndUnionIdForOpenAPI from './queries/haveBindWXByOpenIdAndUnionIdForOpenAPI.graphql'
import identityExists from './queries/identityExists.graphql'
import isPhoneNumberExists from './queries/isPhoneNumberExists.graphql'
import isUserPerfectedPhoneNumber from './queries/isUserPerfectedPhoneNumber.graphql'
import listUserInfo from './queries/listUserInfo.graphql'
import preValidServicerLogin from './queries/preValidServicerLogin.graphql'
import userExistsWithIdentityAndName from './queries/userExistsWithIdentityAndName.graphql'
import bindUserWXMiniProgramForOpenAPI from './mutates/bindUserWXMiniProgramForOpenAPI.graphql'
import bindWXMiniProgramForOpenAPI from './mutates/bindWXMiniProgramForOpenAPI.graphql'
import bindWXOfficialAccountForOpenAPI from './mutates/bindWXOfficialAccountForOpenAPI.graphql'
import createServicerSubAdmin from './mutates/createServicerSubAdmin.graphql'
import createSubAdmin from './mutates/createSubAdmin.graphql'
import sendBindingSuccessMessage from './mutates/sendBindingSuccessMessage.graphql'
import unBindCurrentUserWX from './mutates/unBindCurrentUserWX.graphql'
import unBindStudentWX from './mutates/unBindStudentWX.graphql'
import updateCurrentPhoneWithPhoneCaptchaToken from './mutates/updateCurrentPhoneWithPhoneCaptchaToken.graphql'
import updateCurrentServicerAdminPhoneForChannelVendor from './mutates/updateCurrentServicerAdminPhoneForChannelVendor.graphql'
import updateCurrentServicerAdminPhoneForCoursewareSupplier from './mutates/updateCurrentServicerAdminPhoneForCoursewareSupplier.graphql'
import userPerfectPhoneNumber from './mutates/userPerfectPhoneNumber.graphql'
import xwMiniProgramUserPerfectPhoneNumber from './mutates/xwMiniProgramUserPerfectPhoneNumber.graphql'

export {
  exportStudent,
  findAdminPage,
  findAdminPageForChannelVendor,
  findAdminPageForCoursewareSupplier,
  findAdminPageForTrainingInstitution,
  findStudentPage,
  getAdminInfo,
  getCurrentUserBindedThirdAccounts,
  getCurrentUserInfo,
  getStudent,
  getStudentCount,
  getUserBindedThirdAccounts,
  getWXUserInfo,
  haveBindWXByOpenIdAndUnionIdForOpenAPI,
  identityExists,
  isPhoneNumberExists,
  isUserPerfectedPhoneNumber,
  listUserInfo,
  preValidServicerLogin,
  userExistsWithIdentityAndName,
  bindUserWXMiniProgramForOpenAPI,
  bindWXMiniProgramForOpenAPI,
  bindWXOfficialAccountForOpenAPI,
  createServicerSubAdmin,
  createSubAdmin,
  sendBindingSuccessMessage,
  unBindCurrentUserWX,
  unBindStudentWX,
  updateCurrentPhoneWithPhoneCaptchaToken,
  updateCurrentServicerAdminPhoneForChannelVendor,
  updateCurrentServicerAdminPhoneForCoursewareSupplier,
  userPerfectPhoneNumber,
  xwMiniProgramUserPerfectPhoneNumber
}
