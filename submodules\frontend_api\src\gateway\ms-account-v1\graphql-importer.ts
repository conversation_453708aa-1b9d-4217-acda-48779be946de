import changePasswordByCurrent from './mutates/changePasswordByCurrent.graphql'
import disableNoticeForWebChatApplet from './mutates/disableNoticeForWebChatApplet.graphql'
import enableNoticeForWebChatApplet from './mutates/enableNoticeForWebChatApplet.graphql'
import freezeAccount from './mutates/freezeAccount.graphql'
import immediateResetPassword from './mutates/immediateResetPassword.graphql'
import resetAccountPwdAuthIdentity from './mutates/resetAccountPwdAuthIdentity.graphql'
import resumeAccount from './mutates/resumeAccount.graphql'
import updateUser from './mutates/updateUser.graphql'
import updateUserByCurrent from './mutates/updateUserByCurrent.graphql'

export {
  changePasswordByCurrent,
  disableNoticeForWebChatApplet,
  enableNoticeForWebChatApplet,
  freezeAccount,
  immediateResetPassword,
  resetAccountPwdAuthIdentity,
  resumeAccount,
  updateUser,
  updateUserByCurrent
}
