import BasicDataGateway, {
  TrainingPropertyQueryRequest,
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
import SkuDictType from '@api/service/common/basic-data-dictionary/model/SkuDictType'
/**
 * 获取sku属性（非一级）
 */
class QueryDictList {
  /**
   * sku Map (根据sku code映射)
   */
  private skuCategoryCodeMap = new Map<string, SkuDictType[]>()
  /**
   * 获取sku列表
   */
  async queryList(
    categoryCode: IndustryPropertyCodeEnum,
    industryPropertyId: string,
    gradePropertyId: string[],
    propertyIdList?: string[]
  ): Promise<Array<TrainingPropertyResponse>> {
    if (categoryCode) {
      const skuList = this.getMapSkuList(categoryCode)
      if (!skuList.length) {
        const request = new TrainingPropertyQueryRequest()
        request.industryPropertyId = industryPropertyId
        // request.propertyId = gradePropertyId
        request.propertyIdList = propertyIdList ? propertyIdList : undefined
        request.categoryCode = categoryCode
        const res = await BasicDataGateway.listIndustryPropertyChildByCategoryV2(request)
        this.skuCategoryCodeMap.set(categoryCode, res.data)
        if (gradePropertyId.length && !gradePropertyId.includes('-1')) {
          return res.data.filter(item => {
            return gradePropertyId.includes(item.parentId)
          })
        }
        return res.data || new Array<TrainingPropertyResponse>()
      } else {
        if (gradePropertyId.length && !gradePropertyId.includes('-1')) {
          return skuList.filter(item => {
            return gradePropertyId.includes(item.parentId)
          })
        }
        return skuList
      }
    }
  }

  /**
   * 获取sku列表 在线学习规则 学科
   */
  async queryListNew(
    categoryCode: IndustryPropertyCodeEnum,
    industryPropertyId: string,
    gradePropertyId: string[],
    propertyIdList?: string[]
  ): Promise<Array<TrainingPropertyResponse>> {
    if (categoryCode) {
      const skuList = this.getMapSkuListNew(categoryCode + gradePropertyId.join('') + (propertyIdList || []).join(''))
      if (!skuList.length) {
        const request = new TrainingPropertyQueryRequest()
        request.industryPropertyId = industryPropertyId
        // request.propertyId = gradePropertyId
        request.propertyIdList = propertyIdList ? propertyIdList : undefined
        request.categoryCode = categoryCode
        const res = await BasicDataGateway.listIndustryPropertyChildByCategoryV2(request)
        this.skuCategoryCodeMap.set(categoryCode + gradePropertyId.join('') + (propertyIdList || []).join(''), res.data)
        // if (gradePropertyId.length && !gradePropertyId.includes('-1')) {
        //   return res.data.filter(item => {
        //     return gradePropertyId.includes(item.parentId)
        //   })
        // }
        return res.data || new Array<TrainingPropertyResponse>()
      }
      // else {
      // if (gradePropertyId.length && !gradePropertyId.includes('-1')) {
      //   return skuList.filter(item => {
      //     return gradePropertyId.includes(item.parentId)
      //   })
      // }
      return skuList
      // }
    }
  }

  private getMapSkuList(categoryCode: IndustryPropertyCodeEnum) {
    return this.skuCategoryCodeMap.get(categoryCode) || new Array<TrainingPropertyResponse>()
  }
  private getMapSkuListNew(code: string) {
    return this.skuCategoryCodeMap.get(code) || new Array<TrainingPropertyResponse>()
  }
}

export default new QueryDictList()
