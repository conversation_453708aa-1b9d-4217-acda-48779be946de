import OrderRefundStatus, { OrderRefundStatusEnum } from '@api/service/common/return-order/enums/OrderRefundStatus'
import { OrderRefundTypeEnum, OnlyReturnList } from '@api/service/common/return-order/enums/OrderRefundType'
import {
  CommoditySkuResponse,
  ReturnOrderBasicDataResponse,
  ReturnOrderResponse,
  SchemeResourceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

import { ReturnOrderResponse as CustomerReturnOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'

export default class RefundRecordItem {
  /**
   * 退货单id
   */
  returnOrderId = ''

  /**
   * 发起退款时间（ISO格式）
   */
  applyTime = ''

  /**
   * 退款原因
   */
  reason = ''

  /**
   * 退款类型
   */
  type: OrderRefundTypeEnum = undefined

  /**
   * 退款方式
   */
  refundWay = ''

  /**
   * 退款金额（单位：元）
   */
  amount = 0

  /**
   * 退货内容
   */
  returnContent = ''

  /**
   * 退款状态
   */
  status: OrderRefundStatusEnum = undefined

  /**
   * 退款说明
   */
  refundInfo = ''

  static from(dto: ReturnOrderResponse) {
    const vo = new RefundRecordItem()
    const basicData = dto?.basicData || new ReturnOrderBasicDataResponse()
    let dtoCommodity = new CommoditySkuResponse()
    if (OnlyReturnList.includes(basicData.returnOrderType)) {
      dtoCommodity = dto.returnCommodity.commoditySku
    } else {
      dtoCommodity = dto.refundCommodity.commoditySku
    }

    vo.returnOrderId = dto.returnOrderNo
    vo.applyTime = basicData.returnOrderStatusChangeTime?.applied

    vo.reason = basicData.applyInfo?.reasonContent
    vo.type = basicData.returnOrderType
    vo.amount = basicData.refundAmount

    const schemeInfo = dtoCommodity.resource as SchemeResourceResponse
    if (dto?.subOrderInfo?.exchanged) {
      vo.returnContent = dto?.returnCommodity?.commoditySku?.saleTitle
    } else {
      vo.returnContent = schemeInfo.schemeName || dtoCommodity.saleTitle
    }
    vo.status = OrderRefundStatus.transferDtoToCurrentEnum(
      basicData.returnOrderStatus,
      basicData.returnCloseReason.closeType
    )
    vo.refundInfo = basicData.applyInfo?.description
    return vo
  }

  static fromCustomer(dto: CustomerReturnOrderResponse) {
    const vo = new RefundRecordItem()
    const basicData = dto?.basicData || new ReturnOrderBasicDataResponse()
    let dtoCommodity = new CommoditySkuResponse()
    if (OnlyReturnList.includes(basicData.returnOrderType)) {
      dtoCommodity = dto.returnCommodity.commoditySku
    } else {
      dtoCommodity = dto.refundCommodity.commoditySku
    }

    vo.returnOrderId = dto.returnOrderNo
    vo.applyTime = basicData.returnOrderStatusChangeTime?.applied

    vo.reason = basicData.applyInfo?.reasonContent
    vo.type = basicData.returnOrderType
    vo.amount = basicData.refundAmount

    const schemeInfo = dtoCommodity.resource as SchemeResourceResponse
    if (dto?.subOrderInfo?.exchanged) {
      vo.returnContent = dto?.returnCommodity?.commoditySku?.saleTitle
    } else {
      vo.returnContent = schemeInfo.schemeName || dtoCommodity.saleTitle
    }
    vo.status = OrderRefundStatus.transferDtoToCurrentEnum(
      basicData.returnOrderStatus,
      basicData.returnCloseReason.closeType
    )
    vo.refundInfo = basicData.applyInfo?.description

    return vo
  }
}
