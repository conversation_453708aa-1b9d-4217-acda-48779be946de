import Transform from './queries/Transform.graphql'
import checkDistributorCommodityRelationValid from './queries/checkDistributorCommodityRelationValid.graphql'
import getDistributorCommodityInDistributor from './queries/getDistributorCommodityInDistributor.graphql'
import getDistributorCommoditySkuPropertyCollectionInDistributor from './queries/getDistributorCommoditySkuPropertyCollectionInDistributor.graphql'
import getTrainingInstitutionPortalInfoByPortalId from './queries/getTrainingInstitutionPortalInfoByPortalId.graphql'
import mapPropertyList from './queries/mapPropertyList.graphql'
import pageDistributorCommodityInDistributor from './queries/pageDistributorCommodityInDistributor.graphql'
import pagePortalDistributorIssueCommodityInDistributor from './queries/pagePortalDistributorIssueCommodityInDistributor.graphql'

export {
  Transform,
  checkDistributorCommodityRelationValid,
  getDistributorCommodityInDistributor,
  getDistributorCommoditySkuPropertyCollectionInDistributor,
  getTrainingInstitutionPortalInfoByPortalId,
  mapPropertyList,
  pageDistributorCommodityInDistributor,
  pagePortalDistributorIssueCommodityInDistributor
}
