import { ImportTaskQueryResponse } from '@api/ms-gateway/ms-certificate-v1'
import { UserJobLogResponse } from '@api/ms-gateway/ms-data-export-front-gateway-DataExportBackstage'

/*
 * 导出任务列表项
 */
class AsyncTaskItemVo extends UserJobLogResponse {
  /**
   * 任务id
   */
  jobId = ''

  /**
   * 任务名称
   */
  jobName = ''
  /**
   * 任务创建时间
   */
  createTime = ''
  /**
   * 任务结束时间
   */
  endTime = ''
  /**
   * 任务状态
   * (executing:运行中 executed:运行完成 fail:运行失败)
   */
  jobState = ''
  /**
   * 异步任务处理结果（true:成功 false:失败）
   */
  jobResult: boolean = null

  /**
   * 导出文件路径
   */
  exportFilePath = ''
  /**
   * 操作人id
   */
  operatorUserId = ''
  /*
    操作人姓名
  */
  operateUser = ''
  /**
   * 任务执行成功或失败的信息
   */
  message = ''
  /**
   * 任务处理结果
   */
  importProgress: number = null

  from(item: UserJobLogResponse) {
    this.jobId = item.jobId
    this.jobName = item.jobName
    this.createTime = item.beginTime
    this.endTime = item.endTime
    this.jobState = item.jobState
    this.jobResult = item.jobResult
    this.message = item.message
    this.exportFilePath = item.exportFilePath
    this.operatorUserId = item.operatorUserId
  }

  static fromTaskExecuteResponse(from: ImportTaskQueryResponse) {
    //     /**
    //      * 导出文件路径
    //      */
    //     exportFilePath = ''
    //     /**
    //      * 操作人id
    //      */
    //     operatorUserId = ''
    //     /*
    //     操作人姓名
    //   */
    //     operateUser = ''
    const vo = new AsyncTaskItemVo()
    vo.jobId = from.id
    vo.jobName = from.name
    vo.createTime = from.ceratedTime
    vo.endTime = from.completedTime
    vo.jobState = from.taskState === 3 ? 'executed' : 'executing'
    vo.jobResult = from.processResult === 1
    vo.message = from.message
    vo.exportFilePath = from.zipPath
    vo.operatorUserId = from.createUserId
    vo.importProgress = from.importProgress
    return vo
    //
  }
}

export default AsyncTaskItemVo
