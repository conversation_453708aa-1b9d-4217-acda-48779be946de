import { Page } from '@hbfe/common'
import SkuPropertyVo from '@api/service/management/train-class/query/vo/SkuPropertyVo'
import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
import MsTradeQueryFrontGatewayCourseLearningBackstage, {
  CommoditySkuRequest,
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  SchemeResourceResponse,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import {
  ComplexSkuPropertyResponse,
  SchemeSkuInfo,
  SkuPropertyConvertUtils
} from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import MsSchemeQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import SkuVo from '@api/service/management/train-class/query/vo/SkuVo'
import ConfigJsonUtil from '@api/service/management/train-class/Utils/ConfigJsonUtil'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import * as MsLearningschemeGraphqlImporter from '@api/ms-gateway/ms-learningscheme-v1/graphql-importer'
import MsLearningscheme, { LearningSchemeProcessStatusResponse } from '@api/ms-gateway/ms-learningscheme-v1'
import MsLearningScheme, { IsProcessedByTransactionRequest } from '@api/ms-gateway/ms-learningscheme-v1'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import {
  CommoditySkuSortField as CommoditySkuSortFieldV2,
  CommoditySkuSortRequest as CommoditySkuSortRequestv2
} from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'
import IntelligenceLearningModule from '@api/service/management/intelligence-learning/IntelligenceLearningModule'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import DataResolve from '@api/service/common/utils/DataResolve'
import FxnlQueryFrontGatewayBackstage from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import QztgDiffCommoditySkuRequest from '@api/service/diff/management/qztg/train-class/query/vo/QztgDiffCommoditySkuRequest'
import QztgDataExport from '@api/diff-gateway/qztg-data-export-gateway-backstage'
/**
 * 运营域获取培训班商品列表
 */
class QueryTrainClassCommodityList {
  // region properties
  /**
   *总数目，类型为number
   */
  totalSize = 0
  /**
   *培训班商品列表，类型为TrainClassCommodityVo[]
   */
  trainClassCommodityList: TrainClassCommodityVo[] = []
  /**
   *刷选条件数组，类型为SkuPropertyVo
   */
  skuProperties = new SkuPropertyVo()
  // endregion
  // region methods

  /**
   * 批量查询培训方案商品名称
   * @param schemeIdList 方案id集合
   */
  async batchQuerySchemeNameMapBySchemeId(schemeIdList: string[]): Promise<Map<string, string>> {
    const result = new Map<string, string>()
    const queryM = new ConfigJsonUtil()
    const schemeJsonConfigMap = await queryM.batchQuerySchemeJsonConfigMapBySchemeId(schemeIdList, ['name'])
    for (const [key, value] of schemeJsonConfigMap.entries()) {
      result.set(key, value.name || '')
    }
    return result
  }

  fromRuleSchemeItem(dto: TrainClassCommodityVo) {
    const vo = new RuleSchemeItem(dto.schemeId, dto.commodityBasicData.saleTitle, dto.skuValueNameProperty)
    return vo
  }

  /**
   * 获取培训班列表
   * @param page 分页参数
   * @param filterCommodity 筛选条件
   * @param sortRequest 排序条件
   * @param distributorId 分销商id，分销相关业务使用
   * @param queryIntelligenceLearning 是否查询智能学习
   * @param querySchemeStatus 是否查询方案状态
   * @description 参数后续优化补充模型
   */
  async queryTrainClassCommodityList(
    page: Page,
    filterCommodity: QztgDiffCommoditySkuRequest,
    sortRequest?: Array<CommoditySkuSortRequest>,
    distributorId?: string,
    querySchemeStatus = true,
    queryIntelligenceLearning = true
  ): Promise<Array<TrainClassCommodityVo>> {
    // let filter = new CommoditySkuRequest()
    filterCommodity.toRequest()
    // filter.skuPropertyRequest = this.filterSkuVo.convertToDto()
    const sortRequestM = new CommoditySkuSortRequest()
    sortRequestM.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortRequestM.policy = SortPolicy.DESC
    !sortRequest && (sortRequest = [])
    sortRequest.push(sortRequestM)
    const res = await MsTradeQueryFrontGatewayCourseLearningBackstage.pageCommoditySkuInServicer({
      page: { pageNo: page.pageNo, pageSize: page.pageSize },
      queryRequest: filterCommodity,
      sortRequest: sortRequest || []
    })
    page.totalPageSize = res.data?.totalPageSize
    page.totalSize = res.data?.totalSize
    if (res.status.isSuccess()) {
      const tmpArr = res.data.currentPageData as TrainClassCommodityVo[]
      tmpArr.forEach((item) => {
        item.schemeId = (item.resource as SchemeResourceResponse)?.schemeId || ''
      })
      const createdTime = new Date(
        ConfigCenterModule.getFrontendApplication(frontendApplication.distinguishSchemeTypeTime)
      ).getTime()
      const statusIdList = tmpArr
        .map((item) => {
          if (createdTime < new Date(item.commodityCreatTime).getTime()) {
            item.isNewScheme = true
            return item.schemeId
          }
        })
        .filter(Boolean)
      // 构建方案id集合
      const schemeIdList = [...new Set(tmpArr.map((item) => item.schemeId).filter(Boolean))]
      const queryM = new ConfigJsonUtil()
      const configJsonMap = await queryM.batchQuerySchemeJsonConfigMapBySchemeId(schemeIdList, [])
      // 智能学习
      let IntelligenceLearningMap = new Map<string, string>()
      if (queryIntelligenceLearning && schemeIdList?.length) {
        IntelligenceLearningMap = await new IntelligenceLearningModule().doBatchCheck(schemeIdList)
      }
      let schemeStatusMap = new Map<string, LearningSchemeProcessStatusResponse>()
      if (querySchemeStatus) {
        schemeStatusMap = await this.getSchemeStatus(statusIdList)
      }
      /** 批量获取方案json配置信息 */
      tmpArr.forEach((item, index) => {
        // if (item.skuProperty) {
        //   item.skuValueNameProperty = SkuPropertyConvertUtils.convertSkuValueNameProperty(item.skuProperty)
        // }
        const configJson = configJsonMap.get(item.schemeId)
        if (configJson) {
          item.resourceServicerId = configJson.resourceServicerId
          item.resourceUnitId = configJson.resourceUnitId
          item.registerBeginDate = configJson.registerBeginDate
          item.registerEndDate = configJson.registerEndDate
          item.trainingBeginDate = configJson.trainingBeginDate
          item.trainingEndDate = configJson.trainingEndDate
          item.schemeType = SchemeTypeEnum[configJson.type as string]
          item.needDataSync = configJson.extendProperties?.find((item: any) => {
            return item.name == 'needDataSync'
          })?.value

          const creditResult = configJson.assessSetting?.learningResults.find((item: any) => {
            return item.type == 1
          })
          const certificateTemplate = configJson.assessSetting?.learningResults.find((item: any) => {
            return item.type == 2
          })
          if (certificateTemplate) {
            item.certificateTemplateId = certificateTemplate.certificateTemplateId
          }
          item.period = creditResult?.grade
          const schemeStatus = schemeStatusMap?.get(item.schemeId)
          item.lastTransactionStep = schemeStatus?.lastTransactionStep || 999
          item.recalculating = schemeStatus?.recalculating || false
          // 智能学习
          const intelligentStatus = IntelligenceLearningMap[item.schemeId]
          console.log(intelligentStatus, 'intelligentStatus')
          item.intelligentLearning = intelligentStatus
          item.hangUp = schemeStatus?.hangUp || false
          if (item?.hangUp) {
            item.hasError = true
            // 可能存在多种异常情况 UI 气泡提示无法完全展示，默认取第一个
            const errMsgList = schemeStatus?.errors?.map((item) => item.message || '')
            item.errorMsg = errMsgList?.length ? errMsgList.join(' ') : '系统异常'
          }
        }
      })
      const schemeSkuInfoList: SchemeSkuInfo[] = tmpArr?.map(
        (item) => new SchemeSkuInfo(item.schemeId, item.skuProperty as ComplexSkuPropertyResponse)
      )
      if (schemeSkuInfoList.length) {
        const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(schemeSkuInfoList)
        tmpArr.forEach((item) => {
          const skyInfo = skuInfos.find((el) => el.id === item.schemeId)
          if (skyInfo) {
            item.skuValueNameProperty = skyInfo.skuName
          }
        })
      }
      /** 叠加分销业务的授权状态 */
      if (distributorId) {
        const commodityIdList = DataResolve.unique(tmpArr.map((item) => item.commoditySkuId))
        if (commodityIdList.length) {
          const { data: authResp } = await FxnlQueryFrontGatewayBackstage.judgeCommodityAuthorized({
            commodityIdList,
            distributorId
          })
          if (authResp && authResp.length) {
            tmpArr.forEach((item) => {
              item.isAuthorize = authResp.includes(item.commoditySkuId)
            })
          }
        }
      }
      this.trainClassCommodityList = tmpArr
    }
    return this.trainClassCommodityList
  }

  /**
   * 获取培训班列表
   * @param page 分页参数
   * @param filterCommodity 筛选条件
   * @param sortRequest 排序条件
   * @param distributorId 分销商id，分销相关业务使用
   * @param queryIntelligenceLearning 是否查询智能学习
   * @param querySchemeStatus 是否查询方案状态
   * @description 参数后续优化补充模型
   */
  async queryTrainClassCommodityListInTrainingChannel(
    page: Page,
    filterCommodity: QztgDiffCommoditySkuRequest,
    sortRequest?: Array<CommoditySkuSortRequest>,
    distributorId?: string,
    querySchemeStatus = true,
    queryIntelligenceLearning = true
  ): Promise<Array<TrainClassCommodityVo>> {
    // let filter = new CommoditySkuRequest()
    // filter.skuPropertyRequest = this.filterSkuVo.convertToDto()
    const sortRequestM = new CommoditySkuSortRequest()
    sortRequestM.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortRequestM.policy = SortPolicy.DESC
    !sortRequest && (sortRequest = [])
    sortRequest.push(sortRequestM)
    const res = await MsTradeQueryFrontGatewayCourseLearningBackstage.pageCommoditySkuInTrainingChannel({
      page: { pageNo: page.pageNo, pageSize: page.pageSize },
      queryRequest: filterCommodity,
      sortRequest: sortRequest || []
    })
    page.totalPageSize = res.data?.totalPageSize
    page.totalSize = res.data?.totalSize
    if (res.status.isSuccess()) {
      const tmpArr = res.data.currentPageData as TrainClassCommodityVo[]
      tmpArr.forEach((item) => {
        item.schemeId = (item.resource as SchemeResourceResponse)?.schemeId || ''
      })
      const createdTime = new Date(
        ConfigCenterModule.getFrontendApplication(frontendApplication.distinguishSchemeTypeTime)
      ).getTime()
      const statusIdList = tmpArr
        .map((item) => {
          if (createdTime < new Date(item.commodityCreatTime).getTime()) {
            item.isNewScheme = true
            return item.schemeId
          }
        })
        .filter(Boolean)
      // 构建方案id集合
      const schemeIdList = [...new Set(tmpArr.map((item) => item.schemeId).filter(Boolean))]
      const queryM = new ConfigJsonUtil()
      const configJsonMap = await queryM.batchQuerySchemeJsonConfigMapBySchemeId(schemeIdList, [])
      // 智能学习
      let IntelligenceLearningMap = new Map<string, string>()
      if (queryIntelligenceLearning && schemeIdList?.length) {
        IntelligenceLearningMap = await new IntelligenceLearningModule().doBatchCheck(schemeIdList)
      }
      let schemeStatusMap = new Map<string, LearningSchemeProcessStatusResponse>()
      if (querySchemeStatus) {
        schemeStatusMap = await this.getSchemeStatus(statusIdList)
      }
      /** 批量获取方案json配置信息 */
      tmpArr.forEach((item, index) => {
        // if (item.skuProperty) {
        //   item.skuValueNameProperty = SkuPropertyConvertUtils.convertSkuValueNameProperty(item.skuProperty)
        // }
        const configJson = configJsonMap.get(item.schemeId)
        if (configJson) {
          item.resourceServicerId = configJson.resourceServicerId
          item.resourceUnitId = configJson.resourceUnitId
          item.registerBeginDate = configJson.registerBeginDate
          item.registerEndDate = configJson.registerEndDate
          item.trainingBeginDate = configJson.trainingBeginDate
          item.trainingEndDate = configJson.trainingEndDate
          item.schemeType = SchemeTypeEnum[configJson.type as string]
          item.needDataSync = configJson.extendProperties?.find((item: any) => {
            return item.name == 'needDataSync'
          })?.value

          const creditResult = configJson.assessSetting?.learningResults.find((item: any) => {
            return item.type == 1
          })
          const certificateTemplate = configJson.assessSetting?.learningResults.find((item: any) => {
            return item.type == 2
          })
          if (certificateTemplate) {
            item.certificateTemplateId = certificateTemplate.certificateTemplateId
          }
          item.period = creditResult?.grade
          const schemeStatus = schemeStatusMap?.get(item.schemeId)
          item.lastTransactionStep = schemeStatus?.lastTransactionStep || 999
          item.recalculating = schemeStatus?.recalculating || false
          // 智能学习
          const intelligentStatus = IntelligenceLearningMap[item.schemeId]
          console.log(intelligentStatus, 'intelligentStatus')
          item.intelligentLearning = intelligentStatus
          item.hangUp = schemeStatus?.hangUp || false
          if (item?.hangUp) {
            item.hasError = true
            // 可能存在多种异常情况 UI 气泡提示无法完全展示，默认取第一个
            const errMsgList = schemeStatus?.errors?.map((item) => item.message || '')
            item.errorMsg = errMsgList?.length ? errMsgList.join(' ') : '系统异常'
          }
        }
      })
      const schemeSkuInfoList: SchemeSkuInfo[] = tmpArr?.map(
        (item) => new SchemeSkuInfo(item.schemeId, item.skuProperty as ComplexSkuPropertyResponse)
      )
      if (schemeSkuInfoList.length) {
        const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(schemeSkuInfoList)
        tmpArr.forEach((item) => {
          const skyInfo = skuInfos.find((el) => el.id === item.schemeId)
          if (skyInfo) {
            item.skuValueNameProperty = skyInfo.skuName
          }
        })
      }
      /** 叠加分销业务的授权状态 */
      if (distributorId) {
        const commodityIdList = DataResolve.unique(tmpArr.map((item) => item.commoditySkuId))
        if (commodityIdList.length) {
          const { data: authResp } = await FxnlQueryFrontGatewayBackstage.judgeCommodityAuthorized({
            commodityIdList,
            distributorId
          })
          if (authResp && authResp.length) {
            tmpArr.forEach((item) => {
              item.isAuthorize = authResp.includes(item.commoditySkuId)
            })
          }
        }
      }
      this.trainClassCommodityList = tmpArr
    }
    return this.trainClassCommodityList
  }

  /**
   * 获取培训班属性
   */
  async queryConfig(schemeId: string): Promise<any> {
    //获取培训班配置模板jsonString
    const res = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInServicer({
      schemeId: schemeId,
      needField: [
        'name',
        'registerBeginDate',
        'registerEndDate',
        'trainingBeginDate',
        'trainingEndDate',
        'type',
        'assessSetting.learningResults'
      ]
    })
    let jsonObj = new Object()
    if (res.status?.isSuccess()) {
      const json = res.data.schemeConfig
      if (json) {
        jsonObj = JSON.parse(res.data.schemeConfig)
      }
    }
    return jsonObj
  }

  /**
   * 查询方案重算状态
   */
  async querySchemeStatus(id: string) {
    const res = await MsLearningscheme.learningSchemeProcessTransactionStepQuery(id)
    return res?.data
  }

  /**
   * 查询方案重算状态
   */
  async getIsProcessedByTransaction(idList: Array<string>) {
    const request = new IsProcessedByTransactionRequest()
    request.schemeIds = idList
    const res = await MsLearningScheme.isProcessedByTransaction(request)
    return res?.data?.results
  }

  /**
   * 查询方案重算状态-批量
   */
  async getSchemeStatus(idList: Array<string>) {
    const req = new RewriteGraph<LearningSchemeProcessStatusResponse, string>(
      MsLearningscheme._commonQuery,
      MsLearningschemeGraphqlImporter.learningSchemeProcessTransactionStepQuery
    )
    await req.request(idList)
    return req.itemMap
  }

  switchTrainCate(cate: SkuVo) {
    // if (cate.skuPropertyValueId) {
    this.skuProperties.filterTrainMajor(cate)
    // }
  }

  /**
   * 导出方案列表（商品）
   */
  exportCommoditySkuInServicer(filterCommodity: CommoditySkuRequest, sortRequest?: Array<CommoditySkuSortRequestv2>) {
    const sortRequestM = new CommoditySkuSortRequestv2()
    sortRequestM.sortField = CommoditySkuSortFieldV2.ON_SHELVE_TIME
    sortRequestM.policy = SortPolicy.DESC
    !sortRequest && (sortRequest = [])
    sortRequest.push(sortRequestM)
    const res = QztgDataExport.exportCommoditySkuInServicer({
      queryRequest: filterCommodity
    })
    return res
  }
}

export default QueryTrainClassCommodityList
