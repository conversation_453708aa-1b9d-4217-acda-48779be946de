<template>
  <jxgx-refund-reconciliation ref="refundReconciliationRef"></jxgx-refund-reconciliation>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import TradeExport from '@api/service/diff/management/jxgx/trade/TradeExport'
  import RefundReconciliation from '@hbfe/jxjy-admin-trade/src/reconciliation/personal/components/refund-reconciliation.vue'

  @Component
  export class JxgxRefundReconciliation extends RefundReconciliation {
    /**
     * 对账导出
     */
    tradeExport = new TradeExport()

    /**
     * 导出网校
     */
    async exportDataty() {
      return await this.tradeExport.listExport(this.exportQueryParam)
    }

    /**
     * 导出分销
     */
    async exportDatafx() {
      return await this.tradeExport.listFxExport(this.exportQueryParam)
    }
  }
  @Component({
    components: {
      JxgxRefundReconciliation
    }
  })
  export default class extends Vue {
    @Ref('refundReconciliationRef') refundReconciliationRef: JxgxRefundReconciliation

    /**
     * 搜索分销
     */
    doSearchfx() {
      this.refundReconciliationRef.doSearchFx()
    }

    /**
     * 搜索专题
     */
    doSearchzt() {
      this.refundReconciliationRef.doSearchZt()
    }

    /**
     * 搜索网校
     */
    doSearch() {
      this.refundReconciliationRef.doSearch()
    }
  }
</script>
