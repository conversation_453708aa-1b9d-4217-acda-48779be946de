import { Response, ResponseStatus } from '@hbfe/common'
import PrintCertificateRequestVo from './vo/PrintCertificateRequestVo'
import PlatformCertificateV1 from '@api/platform-gateway/platform-certificate-v1'
import PrintHistoryCertificateRequestVo from '@api/service/customer/personal-leaning/mutation/vo/PrintHistoryCertificateRequestVo'
import PrintCertResponse from '@api/service/customer/personal-leaning/mutation/vo/PrintCertResponse'

class MutationBatchPrintTraining {
  // 打印证明请求参数
  printCertificateParams = new PrintCertificateRequestVo()

  /**
   * @description: 打印/预览 单个证明
   * @param {String} param 学号
   */
  async doBatchPrintTraining(): Promise<Response<PrintCertResponse>> {
    const result = new Response<PrintCertResponse>()
    result.status = new ResponseStatus(200)
    result.data = new PrintCertResponse()
    const { status, data } = await PlatformCertificateV1.studentPrintCertificate(this.printCertificateParams)
    result.status = status
    if (data) {
      result.data = PrintCertResponse.from(data)
    }
    return result
  }

  /**
   * @description: 打印历史培训证明
   * @date: 2024/02/01 11:54:54
   * @param param
   */
  async doBatchPrintHistoryTraining(param: PrintHistoryCertificateRequestVo): Promise<Response<string>> {
    const { status, data } = await PlatformCertificateV1.dataMigrationPrintCertificate(param.to())
    const response = new Response<string>()
    if (!status || !status.isSuccess()) {
      response.status = status
      return response
    }
    response.status = status
    response.data = data
    return response
  }
  /**
   * @description: 免登录打印历史培训证明
   * @param param
   */
  async doBatchOutLoginPrintHistoryTraining(param: PrintHistoryCertificateRequestVo): Promise<Response<string>> {
    const { status, data } = await PlatformCertificateV1.dataMigrationCertificatePrintWithOutLogin(
      param.toOutLoinRequest()
    )
    const response = new Response<string>()
    if (!status || !status.isSuccess()) {
      response.status = status
      return response
    }
    response.status = status
    response.data = data
    return response
  }
}

export default MutationBatchPrintTraining
