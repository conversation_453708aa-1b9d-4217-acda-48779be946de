<template>
  <el-card shadow="never" class="m-card">
    <el-row type="flex">
      <el-col :span="18">
        <el-form ref="form" label-width="auto" class="m-form">
          <el-form-item label="调研问卷名称：">
            {{ questionnaireOverallReport.queationnaireName }}
          </el-form-item>
          <el-form-item label="调研问卷试题：">
            <el-button type="text" @click="previewQuestion">预览</el-button>
          </el-form-item>
          <el-form-item label="参与问卷情况：">
            参与且提交问卷人数 <span class="f-ci f-fb">{{ questionnaireOverallReport.totalNum }}</span> 人
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="6" class="f-tr">
        <el-button type="primary" @click="exportQuestionnaireReportOptions">下载试题选项内容</el-button>
        <el-button type="primary" @click="exportQuestionnaireReport">下载此报告</el-button>
      </el-col>
    </el-row>
    <div class="f-mb10" v-for="(item, index) in quetionsReportList" :key="index">
      <div class="m-sub-tit">
        <span class="tit-txt">
          第{{ index + 1 }}题：
          <!-- 问题内容 -->
          <span class="text">
            <component :is="richValue(item.described)"></component>
          </span>
          <el-tag size="small" class="f-ml5">{{ questionType(item.type) }}</el-tag>
          <template v-if="item.type == QuestionTypeEnum.single || item.type == QuestionTypeEnum.multiple">
            <span v-if="item.isTeacherEvaluate">{{
              item.onlineOrOffline == '1' ? '（线上课程）' : '（线下学习）'
            }}</span>
          </template>
        </span>
      </div>
      <!--表格-->

      <!-- 选择 -->
      <template v-if="item.type == QuestionTypeEnum.single || item.type == QuestionTypeEnum.multiple">
        <el-table stripe :data="item.optionsStatisc" max-height="500px" class="m-table">
          <el-table-column label="选项" min-width="160">
            <template v-slot="{ row }">
              <component :is="richValue(row.content)"></component>
            </template>
          </el-table-column>
          <el-table-column label="选择数" min-width="160" align="center">
            <template v-slot="{ row }">{{ row.selectNum }}</template>
          </el-table-column>
          <el-table-column label="比例" min-width="160" align="center">
            <template v-slot="{ row }">{{ proportion(row.selectProportion) }}%</template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 量表 -->
      <template v-if="item.type == QuestionTypeEnum.gauge">
        <el-table stripe :data="item.scaleStatisc" max-height="500px" class="m-table">
          <el-table-column label="选项" min-width="160">
            <template v-slot="{ row }">{{ row.levelNum }}</template>
          </el-table-column>
          <el-table-column label="选择数" min-width="160" align="center">
            <template v-slot="{ row }">{{ row.selectNum ? row.selectNum : 0 }}</template>
          </el-table-column>
          <el-table-column label="比例" min-width="160" align="center">
            <template v-slot="{ row }">{{ proportion(row.selectProportion) }}%</template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 问答题 -->
      <template v-if="item.type == QuestionTypeEnum.answer">
        <el-table :data="list" stripe max-height="500px" class="m-table">
          <el-table-column label="答题人数" min-width="160" align="center">
            <template>{{ item.submitNum }}</template>
          </el-table-column>
          <el-table-column label="比例" min-width="160" align="center">
            <template> {{ proportion(item.submitProportion) }}%</template>
          </el-table-column>
        </el-table>
        <div class="f-mt20">
          <el-button type="primary" @click="openDetailDialog(item)">查看详细信息</el-button>
          <el-button type="primary" @click="exportAnswer(item)">导出问答文本到EXCEL</el-button>
        </div>
      </template>
    </div>

    <!--提示弹窗-->
    <el-dialog :visible.sync="exportDialog" width="400px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务管理-{{ exportName }}</p>
        </div>
      </div>
      <div slot="footer">
        <el-button type="info" @click="exportDialog = false">暂 不</el-button>
        <el-button type="primary" @click="toDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>

    <check-detail-dialog ref="CheckDetailDialogRef" :questionId="questionId" :answer="answer"></check-detail-dialog>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Ref } from 'vue-property-decorator'
  import CheckDetailDialog from './check-detail-dialog.vue'
  import QuestionnaireOverallReport from '@api/service/management/resource/question-naire/QuertionnaireOverallReport'
  import QuetionReport from '@api/service/common/question-naire/QuetionReport'
  import { QuestionTypeEnum } from '@api/service/common/enums/question-naire/QuestionType'
  import { bind, debounce } from 'lodash-decorators'

  @Component({
    components: { CheckDetailDialog }
  })
  export default class extends Vue {
    @Ref('CheckDetailDialogRef') CheckDetailDialogRef: CheckDetailDialog
    @Prop({ type: String, default: '' }) questionId: string

    questionnaireOverallReport = new QuestionnaireOverallReport()
    answer = new QuetionReport()
    quetionsReportList = new Array<QuetionReport>()
    QuestionTypeEnum = QuestionTypeEnum

    showCheckDetail = false
    list = [{}]
    exportDialog = false
    exportName = '问卷整体报告'
    exportType = ''

    get proportion() {
      return (num: string | number) => {
        const num1 = Number(num) || 0
        return parseFloat((num1 * 100).toFixed(1))
      }
    }

    get questionType() {
      return (type: QuestionTypeEnum) => {
        const typeMap = {
          [QuestionTypeEnum.single]: '单选题',
          [QuestionTypeEnum.multiple]: '多选题',
          [QuestionTypeEnum.gauge]: '量表题',
          [QuestionTypeEnum.answer]: '问答题'
        }
        return typeMap[type] || ''
      }
    }

    get richValue() {
      return (row: string) => {
        return {
          template: '<div>' + this.testFwbImg(row) + '</div>'
        }
      }
    }

    /////////////////////////////////////////////////////////////////////////////////////////
    async mounted() {
      this.questionnaireOverallReport.id = this.questionId
      const issueId = (this.$route.query.issueId as string) || ''
      const res = await this.questionnaireOverallReport.doQuery(issueId)
      this.quetionsReportList = this.questionnaireOverallReport.quetionsReportList
      console.log('nishi 煞笔煞笔', this.questionnaireOverallReport.quetionsReportList)
    }

    @bind
    @debounce(200)
    async openDetailDialog(item: QuetionReport) {
      //   this.showCheckDetail = true
      this.CheckDetailDialogRef.show = true
      this.answer = item
    }

    previewQuestion() {
      window.open(
        `/admin#/resource/questionnaire/preview?id=${this.questionnaireOverallReport.quertionnaireTemplateId}`,
        '_blank'
      )
    }

    @bind
    @debounce(200)
    async exportAnswer(item: QuetionReport) {
      const res = await item.exportAskQuestionText(this.questionId)
      if (!res) {
        this.$message.error('导出请求失败！')
      } else {
        this.exportName = '问答文本'
        this.exportType = 'answerPaperAskQuestionExport'
        this.exportDialog = true
      }
    }

    @bind
    @debounce(200)
    exportQuestionnaireReportOptions() {
      const res = this.questionnaireOverallReport.exportQuestionnaireReportOptions()
      if (!res) {
        this.$message.error('导出请求失败！')
      } else {
        this.exportName = '试题选项问答内容'
        this.exportType = 'answerPaperAnswerQuestionExport'
        this.exportDialog = true
      }
    }

    @bind
    @debounce(200)
    async exportQuestionnaireReport() {
      const res = await this.questionnaireOverallReport.exportQuestionnaireReport()
      if (!res) {
        this.$message.error('导出请求失败！')
      } else {
        this.exportName = '问卷整体报告'
        this.exportType = 'answerQuestionPdfExport'
        this.exportDialog = true
      }
    }

    // 前往导出任务查看页面
    @bind
    @debounce(200)
    toDownloadPage() {
      this.exportDialog = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: {
          type: `${this.exportType}`
        }
      })
    }

    /**
     * 测试富文本图片
     */
    testFwbImg(str: string) {
      return str?.replace(/<img.*?>/g, function (img) {
        const regex = /<img[^>]+src="([^">]+)"/
        const match = img.match(regex)
        return `
            <el-popover
            placement="top-start"
            width="200"
            trigger="hover">
              <img src="${match[1]}" style="width: 200px; height: 200px" />
              <span slot="reference" style="color:#1f86f0">[查看图片]</span>
          </el-popover>
         `.trim()
      })
    }
  }
</script>
