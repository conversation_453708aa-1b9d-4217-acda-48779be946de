import MsCourseResourceV1 from '@api/ms-gateway/ms-course-resource-v1'
import { ResponseStatus } from '@hbfe/common'

class ImportCoursePackage {
  failList: Array<string>

  filePath: string

  /**
   * 选中文件执行导入课件
   */
  async doImport(): Promise<ResponseStatus> {
    const result = await MsCourseResourceV1.batchImportCoursePackage(this.filePath)
    return new ResponseStatus(result.status.code, result.status.getMessage())
  }
}

export default ImportCoursePackage
