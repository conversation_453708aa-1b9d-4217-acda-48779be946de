import AbstractEnum from '../AbstractEnum'

enum TerminalEnum {
  // Web端 Web
  web = 'Web',
  // IOS端 IOS
  ios = 'IOS',
  // 安卓端  Android
  android = 'Android',
  // 微信小程序  WechatMini
  wechat_mini = 'WechatMini',
  // 微信公众号  WechatOfficial
  wechat_official = 'WechatOfficial',
  // 补贴管理系统
  external_system_manage = 'ExternalSystemManage',
  // 导入开通
  import_open = 'Present'
}

export { TerminalEnum }

class Terminal extends AbstractEnum<TerminalEnum> {
  constructor() {
    super()
    this.map[TerminalEnum.web] = 'Web端'
    this.map[TerminalEnum.ios] = 'IOS端'
    this.map[TerminalEnum.android] = '安卓端'
    this.map[TerminalEnum.wechat_mini] = '微信小程序'
    this.map[TerminalEnum.wechat_official] = '微信公众号'
    this.map[TerminalEnum.external_system_manage] = '补贴管理系统'
    this.map[TerminalEnum.import_open] = '导入开通'
  }
}

export default Terminal
