<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--分销产品统计口径说明-->
        <el-button @click="dialog6 = true" type="primary" class="f-mr20">分销产品统计口径说明</el-button>
        <el-drawer title="统计口径说明" :visible.sync="dialog6" size="900px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
            </el-alert>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">搜索条件说明</span>
              （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
            </p>
            <el-table stripe :data="tableData8" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="170">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">年度</div>
                  <div v-else-if="scope.$index === 1">地区</div>
                  <div v-else-if="scope.$index === 2">培训方案类型</div>
                  <div v-else-if="scope.$index === 3">培训方案名称</div>
                  <div v-else-if="scope.$index === 4">供应商</div>
                  <div v-else-if="scope.$index === 5">行业</div>
                  <div v-else>查询日期</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">查询指定培训年度的培训商品开通数据</div>
                  <div v-else-if="scope.$index === 1">查询指定培训地区的培训商品开通数据</div>
                  <div v-else-if="scope.$index === 2">创建培训方案时定义的类型属性，如培训班等</div>
                  <div v-else-if="scope.$index === 3">查询已授权的培训方案</div>
                  <div v-else-if="scope.$index === 4">选择指定的供应商，查询供应商推广的开通数据</div>
                  <div v-else-if="scope.$index === 5">培训商品所属的培训行业，不同行业可以查询不同的培训属性值</div>
                  <div v-else>查询在某个时间段内培训商品的开通数据</div>
                </template>
              </el-table-column>
            </el-table>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">列表字段及详细说明</span>
              （列表下的数据显示受搜索条件的约束，统计单位：人次）
            </p>
            <el-table stripe :data="tableData18" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">培训产品</div>
                  <div v-else-if="scope.$index === 1">授权供应商</div>
                  <div v-else-if="scope.$index === 2">培训属性</div>
                  <div v-else-if="scope.$index === 3">授权分销商</div>
                  <div v-else-if="scope.$index === 4">分销定价方式</div>
                  <div v-else-if="scope.$index === 5">授权定价</div>
                  <div v-else-if="scope.$index === 6">优惠申请</div>
                  <div v-else-if="scope.$index === 7">合计</div>
                  <div v-else-if="scope.$index === 8">个人缴费</div>
                  <div v-else-if="scope.$index === 9">集体缴费</div>
                  <div v-else-if="scope.$index === 10">导入开通</div>
                  <div v-else-if="scope.$index === 11">线上缴费</div>
                  <div v-else-if="scope.$index === 12">线下缴费</div>
                  <div v-else-if="scope.$index === 13">开通</div>
                  <div v-else-if="scope.$index === 14">退班</div>
                  <div v-else-if="scope.$index === 15">换入（换班）</div>
                  <div v-else-if="scope.$index === 16">换出（换班）</div>
                  <div v-else>净开通</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">培训产品名称，默认显示全部</div>
                  <div v-else-if="scope.$index === 1">培训产品对应授权的供应商名称</div>
                  <div v-else-if="scope.$index === 2">
                    培训产品的培训属性值，根据不同属性值可以查询不同的培训商品开通数据统计
                  </div>
                  <div v-else-if="scope.$index === 3">培训商品所授权的分销商名称</div>
                  <div v-else-if="scope.$index === 4">分销定价方式分为授权定价和优惠申请</div>
                  <div v-else-if="scope.$index === 5">
                    所有授权定价的分销商品的销售数据统计，多个授权定价都在此维度展示销售数据
                  </div>
                  <div v-else-if="scope.$index === 6">
                    所有优惠申请的分销商品的销售数据统计，多个优惠申请都在此维度展示销售数据
                  </div>
                  <div v-else-if="scope.$index === 7">培训商品各渠道开通的数据合计</div>
                  <div v-else-if="scope.$index === 8">学员自主缴费的方式</div>
                  <div v-else-if="scope.$index === 9">学员参与培训通过集体报名的方式</div>
                  <div v-else-if="scope.$index === 10">学员参与培训通过后台教务人员导入开通</div>
                  <div v-else-if="scope.$index === 11">通过网络在线的形式进行缴费</div>
                  <div v-else-if="scope.$index === 12">通过线下的形式进行缴费</div>
                  <div v-else-if="scope.$index === 13">
                    在查询时间段内，学员成功开通培训班的人次。缴费成功订单状态为交易成功的培训班则开通数+1
                  </div>
                  <div v-else-if="scope.$index === 14">在查询时间段内，学员退款成功培训班的人次则退班数+1</div>
                  <div v-else-if="scope.$index === 15">在查询时间段内，发起换入班级且换入成功的人次+1；</div>
                  <div v-else-if="scope.$index === 16">在查询时间段内，发起换出班级且换出成功的人次+1；</div>
                  <div v-else>净开通 = 开通数 + 换入数 - 退班数 - 换出数</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>

        <!--供应商推广统计口径说明-->
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">供应商推广统计口径说明</el-button>
        <el-drawer title="统计口径说明" :visible.sync="dialog5" size="900px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
            </el-alert>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">搜索条件说明</span>
              （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
            </p>
            <el-table stripe :data="tableData8" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="170">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">年度</div>
                  <div v-else-if="scope.$index === 1">地区</div>
                  <div v-else-if="scope.$index === 2">培训方案类型</div>
                  <div v-else-if="scope.$index === 3">培训方案名称</div>
                  <div v-else-if="scope.$index === 4">行业</div>
                  <div v-else-if="scope.$index === 5">科目类型</div>
                  <div v-else-if="scope.$index === 6">培训类别</div>
                  <div v-else-if="scope.$index === 7">培训专业</div>
                  <div v-else-if="scope.$index === 8">方案学时</div>
                  <div v-else-if="scope.$index === 9">供应商</div>
                  <div v-else>查询日期</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">培训产品所属的继续教育年度</div>
                  <div v-else-if="scope.$index === 1">培训产品所属的培训地区</div>
                  <div v-else-if="scope.$index === 2">培训产品方案所定义的类型属性</div>
                  <div v-else-if="scope.$index === 3">已授权的培训产品的方案名称</div>
                  <div v-else-if="scope.$index === 4">培训产品所属的培训行业，不同行业可以查询不同的培训属性值</div>
                  <div v-else-if="scope.$index === 5">
                    培训产品所属的科目类型，分为公需科目、专业科目和公需科目+专业科目
                  </div>
                  <div v-else-if="scope.$index === 6">培训产品所属的培训类别</div>
                  <div v-else-if="scope.$index === 7">培训产品所属的培训专业</div>
                  <div v-else-if="scope.$index === 8">培训方案的学时数</div>
                  <div v-else-if="scope.$index === 9">可以选择平台内的供应商查询培训产品的销售数据</div>
                  <div v-else>查询在某个时间段内培训产品的销售数据</div>
                </template>
              </el-table-column>
            </el-table>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">列表字段及详细说明</span>
              （列表下的数据显示受搜索条件的约束，统计单位：人次）
            </p>
            <el-table stripe :data="tableData9" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">供应商</div>
                  <div v-else-if="scope.$index === 1">合计</div>
                  <div v-else-if="scope.$index === 2">一级分销商</div>
                  <div v-else-if="scope.$index === 3">二级分销商</div>
                  <div v-else-if="scope.$index === 4">开通</div>
                  <div v-else-if="scope.$index === 5">退班</div>
                  <div v-else-if="scope.$index === 6">换入（换班）</div>
                  <div v-else-if="scope.$index === 7">换出（换班）</div>
                  <div v-else>净开通</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">平台内的供应商名称和统一社会信用代码，默认显示所有供应商</div>
                  <div v-else-if="scope.$index === 1">供应商的分销商售销网校培训产品的数据合计</div>
                  <div v-else-if="scope.$index === 2">供应商的一级分销商售销网校培训产品的数据合计</div>
                  <div v-else-if="scope.$index === 3">供应商的二级分销商售销网校培训产品的数据合计</div>
                  <div v-else-if="scope.$index === 4">
                    在查询时间段内，学员成功开通培训班的人次。缴费成功订单状态为交易成功的培训班则开通数+1
                  </div>
                  <div v-else-if="scope.$index === 5">在查询时间段内，学员退款成功培训班的人次则退班数+1</div>
                  <div v-else-if="scope.$index === 6">在查询时间段内，发起换入班级且换入成功的人次+1</div>
                  <div v-else-if="scope.$index === 7">在查询时间段内，发起换出班级且换出成功的人次+1</div>
                  <div v-else>净开通 = 开通数 + 换入数 - 退班数 - 换出数</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>

        <!--统计说明-->
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">统计说明</el-button>
        <el-drawer title="统计说明" :visible.sync="dialog5" size="900px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
            </el-alert>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">搜索条件说明</span>
              （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
            </p>
            <el-table stripe :data="tableData3" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="170">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">分销商品</div>
                  <div v-else-if="scope.$index === 1">分销商</div>
                  <div v-else-if="scope.$index === 2">报名时间</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">输入文字分销商品名称查询分销商品的销售数据</div>
                  <div v-else-if="scope.$index === 1">选择分销商查询分销销售数据</div>
                  <div v-else-if="scope.$index === 2">查询在指定报名时间段内的销售数据</div>
                </template>
              </el-table-column>
            </el-table>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">列表字段及详细说明</span>
              （列表下的数据显示受搜索条件的约束，统计单位：人次）
            </p>
            <el-table stripe :data="tableData11" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">供应商</div>
                  <div v-else-if="scope.$index === 1">合计</div>
                  <div v-else-if="scope.$index === 2">个人缴费</div>
                  <div v-else-if="scope.$index === 3">集体报名</div>
                  <div v-else-if="scope.$index === 4">导入开通</div>
                  <div v-else-if="scope.$index === 5">线上支付</div>
                  <div v-else-if="scope.$index === 6">线下支付</div>
                  <div v-else-if="scope.$index === 7">开通</div>
                  <div v-else-if="scope.$index === 8">退班</div>
                  <div v-else-if="scope.$index === 9">净开通</div>
                  <div v-else-if="scope.$index === 10">分销总额</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">分销商的名称，默认显示全部分销商</div>
                  <div v-else-if="scope.$index === 1">
                    分销商的销售数据合计，包括分销商自己推广的订单数据以及关联的二级分销商推广数据
                  </div>
                  <div v-else-if="scope.$index === 2">学员通过分销商门户自主报名的数据</div>
                  <div v-else-if="scope.$index === 3">通过集体报名管理员报名的数据</div>
                  <div v-else-if="scope.$index === 4">
                    通过后台导入开通报名的数据
                  </div>
                  <div v-else-if="scope.$index === 5">通过网络在线的形式进行缴费</div>
                  <div v-else-if="scope.$index === 6">通过线下的形式进行缴费</div>
                  <div v-else-if="scope.$index === 7">
                    根据查询日期，统计该分销商作为作为一级分销的直接分销数据和与他关联的二级分销数据。学员成功开通培训班的人次。缴费成功订单状态为交易成功的培训班则开通数+1
                  </div>
                  <div v-else-if="scope.$index === 8">
                    根据查询日期，统计该分销商作为作为一级分销的直接分销数据和与他关联的二级分销数据。在查询时间段内，学员退款成功培训班的人次则退班数+1
                  </div>
                  <div v-else-if="scope.$index === 9">净开通= 开通数 + 换入数 - 退班数 - 换出数</div>
                  <div v-else-if="scope.$index === 10">净开通班级的分销商品总金额</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}],
        tableData3: [{}, {}, {}],
        tableData4: [{}, {}, {}, {}],
        tableData5: [{}, {}, {}, {}, {}],
        tableData6: [{}, {}, {}, {}, {}, {}],
        tableData7: [{}, {}, {}, {}, {}, {}, {}],
        tableData8: [{}, {}, {}, {}, {}, {}, {}, {}],
        tableData9: [{}, {}, {}, {}, {}, {}, {}, {}, {}],
        tableData11: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
        tableData12: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
        tableData14: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
        tableData18: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        dialog11: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
