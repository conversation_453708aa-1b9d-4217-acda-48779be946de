schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""查询切换管理员关联单位信息列表
		@return
	"""
	changeAuthorizationUnitInfoList(request:ChangeUnitAuthorizeRequest):ChangeUnitAuthorizeResponse
}
"""@author: xucenh<PERSON>
	@time: 2024-09-14
	@description:
"""
input ChangeUnitAuthorizeRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.ChangeUnitAuthorizeRequest") {
	"""网校id"""
	onlineSchoolId:String
}
"""切换企业管理员信息响应信息
	<AUTHOR>
"""
type ChangeUnitAuthorizeResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.ChangeUnitAuthorizeResponse") {
	changeTokenInfos:[ChangeEnterpriseAdminUnitInfo]
	token:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
type ChangeEnterpriseAdminUnitInfo @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.ChangeUnitAuthorizeResponse$ChangeEnterpriseAdminUnitInfo") {
	"""单位ID"""
	unitId:String
	"""单位名称"""
	unitName:String
	"""统一社会信用代码"""
	unitCode:String
	"""单位状态 1正常,2冻结
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitStatus
	"""
	unitStatus:Int!
	"""管理员与单位关系状态 1为正常可用  0为冻结不可用
		@see com.fjhb.domain.basicdata.api.unit.consts.PersonUnitRelationshipStatus
	"""
	personUnitRelationStatus:Int!
	"""账户状态"""
	accountStatus:Int!
	"""账户类型"""
	accountType:Int!
	"""应用方类型"""
	applicationMemberType:Int
	"""应用方id"""
	applicationMemberId:String
	"""创建时间"""
	createTime:DateTime
}

scalar List
