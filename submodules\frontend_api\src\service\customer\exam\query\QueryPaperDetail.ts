import { ResponseStatus, Response } from '@hbfe/common'
import MsExamQueryFrontGatewayCourseLearningForeStage, {
  MultipleQuestion,
  OpinionQuestion,
  RadioQuestionResponse as RadioQuestionResponseA,
  MultipleQuestionResponse as MultipleQuestionResponseA,
  OpinionQuestionResponse as OpinionQuestionResponseA,
  FillQuestionResponse as FillQuestionResponseA,
  AskQuestionResponse as AskQuestionResponseA,
  Page,
  RadioQuestion,
  AnswerPaperResponse as AnswerPaperResponseA
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryForeStage'
import {
  AnswerPaperResponse,
  FillQuestionResponse,
  MultipleQuestionResponse,
  OpinionQuestionResponse,
  QuestionGroupViewResponse,
  RadioQuestionResponse
} from '@api/service/customer/exam/mutation/vo/AnswerPaper'
import { cloneDeep } from 'lodash'
import { QuestionKeyValue, QuestionTypeEnum } from '@api/service/customer/exam/utils/Constant'
import { TimeOut } from '@api/service/common/utils/TimeOut'

/**
 * 用户域获取培训班商品列表
 */
class QueryPaperDetail {
  // region properties
  /**
   * 卷子id
   */
  paperId = ''
  /**
   * 参训资格id
   */
  qualificationId = ''
  /**
   * 试卷
   */
  answerPaper = new AnswerPaperResponse()
  // endregion
  // region methods
  async judgeFunc(res: Response<AnswerPaperResponseA>) {
    return res.status.isSuccess() && res.data.markStatus == 1
  }
  async getRecord(paperId: string) {
    return MsExamQueryFrontGatewayCourseLearningForeStage.getAnswerPaperRecordInMyself(paperId)
  }
  /**
   * 获取考试记录
   */
  async queryDetail(): Promise<ResponseStatus> {
    // const filter = new CommoditySkuRequest()
    // filter.skuPropertyRequest = this.filterSkuVo.convertToDto()
    // const res = await MsExamQueryFrontGatewayCourseLearningForeStage.getAnswerPaperRecordInMyself(this.paperId)
    if (!this.paperId) {
      return // 终止后续操作
    }
    const res = await TimeOut.timeoutWithCount<Response<AnswerPaperResponseA>>(this['getRecord'], this['judgeFunc'], [
      this.paperId
    ])
    if (res.status.isSuccess()) {
      Object.assign(this.answerPaper, res.data)
      res.data.questions.forEach((item, index) => {
        this.answerPaper.questions[index].id = item.questionId
        this.answerPaper.questions[index].answered =
          item[QuestionKeyValue.getQuestionAnswerKey(item.questionType)] !== null
      })

      const questionIdList = res.data.questions.map((tmpItem) => tmpItem.questionId)

      // Initialize an empty array to hold all fetched question details
      const allQuestionDetails: any[] = []

      const fetchQuestionDetails = async (ids: string[]) => {
        const questionDetailRes = await MsExamQueryFrontGatewayCourseLearningForeStage.pageQuestionInMySelf({
          page: {
            pageNo: 1,
            pageSize: ids.length
          },
          request: {
            questionIdList: ids,
            qualificationId: this.qualificationId,
            answerPaperId: this.paperId
          }
        })
        if (questionDetailRes.status.isSuccess()) {
          allQuestionDetails.push(...questionDetailRes.data.currentPageData)
        }
      }

      for (let i = 0; i < questionIdList.length; i += 200) {
        const chunk = questionIdList.slice(i, i + 200)
        await fetchQuestionDetails(chunk)
      }

      for (let i = 0; i < this.answerPaper.questions.length; i++) {
        const item = this.answerPaper.questions[i]
        const answerM = allQuestionDetails.find((answerTitem) => answerTitem.questionId == item.id) as any

        if (answerM) {
          item.dissects = answerM.dissects
          item.topic = answerM.topic
          item.correctAnswerText = answerM.correctAnswerText
          item.incorrectAnswerText = answerM.incorrectAnswerText
          item.opinionCorrectAnswer = answerM.opinionCorrectAnswer

          if (item.questionType == QuestionTypeEnum.QuestionTypeEnumRadio) {
            ;(item as RadioQuestionResponse).trueRadioAnswer = (answerM as RadioQuestionResponseA).correctAnswerId
            ;(item as RadioQuestionResponse).answerOptions = (answerM as RadioQuestionResponseA).radioAnswerOptions
          }

          if (item.questionType == QuestionTypeEnum.QuestionTypeEnumMul) {
            ;(item as MultipleQuestionResponse).trueMultipleAnswer = (
              answerM as MultipleQuestionResponseA
            ).correctAnswerIds
            ;(item as MultipleQuestionResponse).answerOptions = (
              answerM as MultipleQuestionResponseA
            ).multipleAnswerOptions
          }

          if (item.questionType == QuestionTypeEnum.QuestionTypeEnumOpi) {
            const trueItem = item as OpinionQuestionResponse
            trueItem.answerOptions = [
              {
                id: 'true',
                content: trueItem.correctAnswerText
              },
              {
                id: 'false',
                content: trueItem.incorrectAnswerText
              }
            ]
            trueItem.trueOpinionAnswer = (answerM as OpinionQuestionResponseA).opinionCorrectAnswer
            trueItem.opinionCorrectAnswer = (answerM as OpinionQuestionResponseA).opinionCorrectAnswer
          }
        }
      }

      this.calPaper()
      this.answerPaper = cloneDeep(this.answerPaper)
    }

    return res.status
  }

  async fetchQuestionDetails(questionIdList: any) {
    const MAX_BATCH_SIZE = 199
    const mergedData: any = {
      status: {
        httpCode: 200,
        code: 200,
        type: '',
        source: '',
        errors: null,
        message: ''
      },
      data: {
        pageNo: 1,
        pageSize: questionIdList.length,
        totalPageSize: 0,
        totalSize: 0,
        currentPageData: []
      }
    }
    // 辅助函数：将数组切分为指定大小的小块
    const chunkArray = (array: any, chunkSize: any) => {
      const chunks = []
      for (let i = 0; i < array.length; i += chunkSize) {
        chunks.push(array.slice(i, i + chunkSize))
      }
      return chunks
    }
    const questionIdChunks = chunkArray(questionIdList, MAX_BATCH_SIZE)
    for (const chunk of questionIdChunks) {
      const res = await MsExamQueryFrontGatewayCourseLearningForeStage.pageQuestionInMySelf({
        page: {
          pageNo: 1,
          pageSize: chunk.length
        },
        request: {
          questionIdList: chunk,
          qualificationId: this.qualificationId
        }
      })
      // 检查请求是否成功
      if (res.status.httpCode === 200 && res.status.code === 200) {
        mergedData.data.currentPageData.push(...res.data.currentPageData)
        mergedData.data.totalSize += res.data.totalSize
        mergedData.data.totalPageSize += res.data.totalPageSize
      }
    }
    return mergedData
  }

  calPaper() {
    if (!this.answerPaper.groups) {
      this.answerPaper.groups = []
    }
    this.answerPaper.questions.forEach((item) => {
      const questionGroup = this.answerPaper.groups.find((tmpItem) => tmpItem.questionType == item.questionType)
      if (questionGroup) {
        questionGroup.questionCount++
      } else {
        const insertGroup = new QuestionGroupViewResponse()
        insertGroup.questionCount = 1
        insertGroup.questionType = item.questionType
        insertGroup.groupName = QuestionKeyValue.getQuestionName(insertGroup.questionType)
        insertGroup.sequence = this.answerPaper.groups.length
        this.answerPaper.groups.push(insertGroup)
      }
    })
  }
  // endregion
}
export default QueryPaperDetail
