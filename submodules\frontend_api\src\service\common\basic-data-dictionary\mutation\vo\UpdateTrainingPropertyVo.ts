import { IndustryTrainingPropertyUpdateRequest } from '@api/ms-gateway/ms-dictionary-v1'
import Context from '@api/service/common/context/Context'

class UpdateTrainingPropertyVo {
  /**
   * 所属行业id
   */
  industryId = ''
  /**
   * 培训属性id
   */
  trainingPropertyId = ''
  /**
   * 展示名称
   */
  showName = ''

  to() {
    const request = new IndustryTrainingPropertyUpdateRequest()
    request.bisId = this.industryId
    request.propertyId = this.trainingPropertyId
    request.showName = this.showName
    request.schoolId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
    return request
  }
}
export default UpdateTrainingPropertyVo
