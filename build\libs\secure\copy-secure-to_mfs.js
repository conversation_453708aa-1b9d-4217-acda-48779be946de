require('dotenv').config()
const mkdirp = require('mkdirp')
const path = require('path')
const fsExtra = require('fs-extra')
const simple = require('simple-git')
const config = require('./config.js')
const git = simple()
const ZRoot = path.join(process.env.NODE_ENV === 'production' ? '/mnt/mfs' : 'Z://', 'frontend-security')
const HRoot = path.join(process.env.NODE_ENV === 'production' ? '/mnt/mfs' : 'Z://', 'frontend-security-history')
console.log('运行路径：' + ZRoot)

let maxNum = 5
const getProjectInfo = async () => {
  // 获取当前的 git 地址
  let result = await git.listRemote(['--get-url'])
  // 获取当前的分支信息
  let currentBranch = await git.branch('--show-current')
  if (result) {
    const splitUrl = result
      .replace(/https?:\/\//, '')
      .replace(/\n/, '')
      .replace(/\.git$/, '')
      .split('/')
    let slug = splitUrl[splitUrl.length - 1]
    let project = splitUrl[splitUrl.length - 2]
    if (project.toUpperCase() === 'JXJYPXTYPT_V2' && slug.toLowerCase() === 'jxjyv2_frontend_web_admin') {
      project = 'JXJYPXTYPT'
      slug = 'frontend_web_admin'
    }
    const mfsRoot = path.join(ZRoot, project.toUpperCase(), slug.toLowerCase(), currentBranch.current)
    const mfsHistoryRoot = path.join(HRoot, project.toUpperCase(), slug.toLowerCase(), currentBranch.current)
    // // 生成项目目录
    mkdirp.sync(mfsRoot)
    // fsExtra.copySync(
    //   path.join(config.cachePath, config.groupFileName),
    //   path.join(mfsRoot, config.groupFileName)
    // )

    // fsExtra.copySync(
    //   path.join(config.cachePath, config.permissionFileName),
    //   path.join(mfsRoot, config.permissionFileName)
    // )
    // 获取当前时间并格式化为指定格式
    const now = new Date()
    const timeDir = `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月${String(now.getDate()).padStart(2, '0')}日-${String(now.getHours()).padStart(2, '0')}时${String(now.getMinutes()).padStart(2, '0')}分`



    // 确保历史目录存在
    mkdirp.sync(path.join(mfsHistoryRoot, timeDir))

    // 读取历史目录下的所有子目录
    const historyDirs = fsExtra.readdirSync(mfsHistoryRoot, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => ({
        name: dirent.name,
        path: path.join(mfsHistoryRoot, dirent.name),
        stats: fsExtra.statSync(path.join(mfsHistoryRoot, dirent.name))
      }))
      .sort((a, b) => a.stats.mtime - b.stats.mtime) // 按修改时间排序，最旧的在前面
    let deleteNum = historyDirs.length - maxNum
    if (deleteNum > 0) {
      for (let index = 0; index < deleteNum; index++) {
        let DirName = historyDirs[index].name
        fsExtra.removeSync(path.join(mfsHistoryRoot, DirName))
      }
    }

    fsExtra.copySync(
      path.join(config.cachePath, config.permissionFileName),
      path.join(mfsHistoryRoot, timeDir, config.permissionFileName)
    )
    fsExtra.copySync(
      path.join(config.cachePath, config.permissionFileName),
      path.join(mfsRoot, config.permissionFileName)
    )
    fsExtra.copySync(
      path.join(config.cachePath, `user-${config.permissionFileName}`),
      path.join(mfsRoot, `user-${config.permissionFileName}`)
    )
    fsExtra.copySync(
      path.join(config.cachePath, `user-${config.permissionFileName}`),
      path.join(mfsHistoryRoot, timeDir, `user-${config.permissionFileName}`)
    )
  }
}

  ; (async () => {
    await getProjectInfo()
  })()
