import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum LearningExperienceEnum {
  class_experience = 0,
  course_experience
}

class ExperienceType extends AbstractEnum<LearningExperienceEnum> {
  constructor(status?: LearningExperienceEnum) {
    super()
    this.current = status
    this.map.set(LearningExperienceEnum.class_experience, '班级心得')
    this.map.set(LearningExperienceEnum.course_experience, '课程心得')
  }
}

export default ExperienceType
