<template>
  <el-drawer title="提示" :visible.sync="autoInvoiceVisible" size="600px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-alert type="warning" show-icon :closable="false" class="m-alert">
        设置电子发票，订单索要发票多少天后自动开票！
      </el-alert>
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form ref="form" label-width="auto" class="m-form f-mt40">
            <el-form-item label="设置天数：">
              <el-input
                type="number"
                v-model="curInvoiceDay"
                @keydown.native="
                  (e) => (e.returnValue = ['e', 'E', '-', '+', '.'].includes(e.key) ? false : e.returnValue)
                "
                class="input-num"
              />
              <span class="f-ml10">天</span>
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="autoInvoiceVisible = false">取消</el-button>
              <el-button type="primary" @click="setAutoInvoice">确定</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import TradeModule from '@api/service/management/trade/TradeModule'

  @Component
  export default class extends Vue {
    @Prop({
      type: Number,
      default: 7
    })
    invoiceDay: number

    @Prop({
      type: Boolean,
      default: false
    })
    autoInvoiceDialog: boolean

    autoInvoiceVisible = false
    curInvoiceDay: number | string = 7

    async setAutoInvoice() {
      if (this.curInvoiceDay === '' || this.curInvoiceDay == undefined || this.curInvoiceDay == null) {
        this.$message.warning('请输入设置天数')
        return
      }
      //设置自动开票时间
      const res =
        await TradeModule.batchTradeBatchFactor.invoiceFactor.mutationInvoice.addOrUpdateElectronicInvoiceAutoConfig(
          true,
          (this.curInvoiceDay as number) * 24
        )
      if (res.isSuccess()) {
        this.$message.success('修改发票自动开票时间成功')
        this.$emit('callBack')
        this.autoInvoiceVisible = false
      } else {
        this.$message.error('修改发票自动开票时间失败')
      }
    }

    @Watch('autoInvoiceDialog')
    changeDialogCtrl() {
      this.curInvoiceDay = this.invoiceDay
      this.autoInvoiceVisible = this.autoInvoiceDialog
    }

    @Watch('autoInvoiceVisible')
    changeDialogVisible() {
      this.$emit('update:autoInvoiceDialog', this.autoInvoiceVisible)
    }

    /*checkInvoiceDay(rule: any, value: any, callback: any) {
      if (value < 0) {
      }
    }*/

    created() {
      this.autoInvoiceVisible = this.autoInvoiceDialog
    }
  }
</script>
<style scoped="scoped">
  ::v-deep .el-input input::-webkit-outer-spin-button,
  ::v-deep .el-input input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
  ::v-deep .el-input input[type='number'] {
    -moz-appearance: textfield;
  }
</style>
