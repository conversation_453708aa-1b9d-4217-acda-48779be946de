import PracticeLearningConfigQuestionSource from '@api/service/common/scheme/model/schemeDto/practice-learning/config/question-source/PracticeLearningConfigQuestionSource'

/**
 * @description 练习配置
 */
class PracticeLearningConfig {
  /**
   * 练习id
   */
  id: string
  /**
   * 出题方式 1、按题库id出题，2、按课程id出题，3：同考试
   */
  type: number
  /**
   * 是否开放题析
   */
  openDissects: boolean
  /**
   * 多选题漏选得分方式，0不得分，1得全部分数，2得一半分数，3每个选项按平均得分
   */
  multipleQuestionMissScorePatterns: number
  /**
   * 试题来源
   */
  questionSource: PracticeLearningConfigQuestionSource
  /**
   * 操作类型
   */
  operation: number
}

export default PracticeLearningConfig
