import { BatchOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import TradeModule from '@api/service/centre/trade/TradeModule'
import { InvoiceTypeEnum } from '@api/service/centre/trade/batch/order/enum/InvoiceTypeList'
import { PayModeEnum } from '@api/service/centre/trade/batch/order/enum/PayModeList'
import BatchOrderUtils from '@api/service/centre/trade/batch/order/query/util/BatchOrderUtils'
import BatchOrderDetailDeliveryInfoVo from '@api/service/centre/trade/batch/order/query/vo/BatchOrderDetailDeliveryInfoVo'
import BatchOrderDetailInvoiceInfoVo from '@api/service/centre/trade/batch/order/query/vo/BatchOrderDetailInvoiceInfoVo'
import BatchOrderDetailOrderInfoVo from '@api/service/centre/trade/batch/order/query/vo/BatchOrderDetailOrderInfoVo'
import { InvoiceTitleTypeEnum } from '@api/service/common/enums/trade-configuration/InvoiceTitleType'
import TradeConfigModule from '@api/service/common/trade-config/TradeConfigModule'
import DataResolve from '@api/service/common/utils/DataResolve'

/**
 * @description 集体报名订单详情
 */
class BatchOrderDetailVo {
  /**
   * 批次单id
   */
  batchOrderNo = ''

  /**
   * 提交报名信息时间
   */
  signUpBeginTime = ''

  /**
   * 付款时间
   */
  payTime = ''

  /**
   * 报名成功时间
   */
  signUpEndTime = ''

  /**
   * 交易关闭时间
   */
  tradeCloseTime = ''

  /**
   * 订单信息
   */
  orderInfo: BatchOrderDetailOrderInfoVo = new BatchOrderDetailOrderInfoVo()

  /**
   * 是否允许补要发票
   */
  enableReApplyInvoice = false

  /**
   * 发票信息
   */
  invoiceInfoList: BatchOrderDetailInvoiceInfoVo[] = []

  /**
   * 是否展示配送信息
   */
  deliveryInfoVisible = false

  /**
   * 配送信息
   */
  deliveryInfo: BatchOrderDetailDeliveryInfoVo = new BatchOrderDetailDeliveryInfoVo()

  /**
   * 是否存在退款成功的子订单
   */
  isExistRefundedSubOrder: boolean = null

  /**
   * 批次单退款状态
   * 0: 未退款 1: 退款中 2: 已部分退款 3: 退款完成
   */
  batchOrderRefundStatus: number = null

  /**
   * 批次单退货状态
   * 0: 未退货 1: 退货中 2: 已部分退货 3: 退货完成
   */
  batchOrderReturnStatus: number = null

  /**
   * 关闭原因：0.未关闭 1. 管理员取消报名 2：系统关闭
   */
  closeType: number = null

  /**
   *
   */
  remote: BatchOrderResponse = new BatchOrderResponse()

  static async from(response: BatchOrderResponse): Promise<BatchOrderDetailVo> {
    const detail = new BatchOrderDetailVo()
    detail.remote = response ?? new BatchOrderResponse()
    detail.batchOrderNo = response.batchOrderNo ?? ''
    detail.signUpBeginTime = response.basicData?.batchOrderStatusChangeTime?.committing ?? ''
    detail.payTime = response.basicData?.paymentStatusChangeTime?.paid ?? ''
    detail.signUpEndTime = response.basicData?.batchOrderStatusChangeTime?.completed ?? ''
    detail.tradeCloseTime = response.basicData?.batchOrderStatusChangeTime?.closed ?? ''
    // 订单信息
    detail.orderInfo = this.getOrderInfo(response)
    detail.enableReApplyInvoice = await this.getEnableReApplyInvoice()
    // 发票信息
    detail.invoiceInfoList = await BatchOrderUtils.getBatchOrderInvoiceInfoList(response)
    // 配送信息
    const { deliveryInfoVisible, deliveryInfo } = this.getDeliveryInfo(detail.invoiceInfoList)
    detail.deliveryInfoVisible = deliveryInfoVisible
    detail.deliveryInfo = deliveryInfo
    const res = await TradeModule.batchOrderFactory.queryBatchOrderFactory.queryBatchOrderList.queryBatchOrderRefundInfoListStatistic(
      detail.batchOrderNo
    )
    detail.isExistRefundedSubOrder = res.refundAmount > 0 ? true : false
    // 批次单退款状态
    detail.batchOrderRefundStatus = response.basicData?.batchOrderRefundStatus ?? null
    detail.batchOrderReturnStatus = response.basicData?.batchOrderReturnStatus ?? null
    detail.closeType = response.basicData?.batchOrderCloseReason?.closedType ?? null
    return detail
  }

  /**
   * 获取订单信息
   */
  private static getOrderInfo(response: BatchOrderResponse): BatchOrderDetailOrderInfoVo {
    const orderInfo = new BatchOrderDetailOrderInfoVo()
    orderInfo.batchOrderNo = response.batchOrderNo ?? ''
    orderInfo.payMode = BatchOrderDetailVo.getPayMode(response)
    orderInfo.batchOrderStatus = BatchOrderUtils.getBatchOrderStatus(response)
    orderInfo.payChannelName = response.payInfo?.receiveAccount?.payChannelName ?? ''
    if (DataResolve.isWeightyArr(response.payInfo?.paymentVoucherList)) {
      orderInfo.remittanceVoucherUrlList = response.payInfo.paymentVoucherList.map(item => {
        return item.path
      })
    }
    return orderInfo
  }

  /**
   * 获取支付方式
   */
  static getPayMode(response: BatchOrderResponse): PayModeEnum {
    if (response.payInfo?.paymentOrderType === 2) {
      return PayModeEnum.OFFLINE
    }
    if (response.payInfo?.paymentOrderType === 1) {
      return PayModeEnum.ONLINE
    }
    return null
  }

  /**
   * 判断是否允许补要发票
   */
  private static async getEnableReApplyInvoice(): Promise<boolean> {
    const queryRemote = TradeConfigModule.queryTradeConfigFactory.getQueryTradeConfig()
    const result = await queryRemote.hasBatchOpenInvoiceConfig()
    return result
  }

  /**
   * 获取发票类型
   */
  static getInvoiceType(response: BatchOrderResponse): InvoiceTypeEnum {
    if (response.invoiceApplyInfo?.invoiceCategory === 2) {
      return InvoiceTypeEnum.VAT_NORMAL_INVOICE
    }
    if (response.invoiceApplyInfo?.invoiceCategory === 3) {
      return InvoiceTypeEnum.VAT_SPECIAL_INVOICE
    }
    return null
  }

  /**
   * 获取发票抬头类型
   */
  static getInvoiceTitleType(response: BatchOrderResponse): InvoiceTitleTypeEnum {
    if (response.invoiceApplyInfo?.titleType === 1) {
      return InvoiceTitleTypeEnum.INDIVIDUAL
    }
    if (response.invoiceApplyInfo?.titleType === 2) {
      return InvoiceTitleTypeEnum.UNIT
    }
    return null
  }

  /**
   * 获取配送信息
   */
  static getDeliveryInfo(invoiceInfoList: BatchOrderDetailInvoiceInfoVo[]) {
    const result = {
      deliveryInfoVisible: false,
      deliveryInfo: new BatchOrderDetailDeliveryInfoVo()
    }
    if (DataResolve.isWeightyArr(invoiceInfoList)) {
      const invoiceInfo = invoiceInfoList.find(item => {
        return item.invoiceType && item.isOffLine
      })
      if (invoiceInfo) {
        result.deliveryInfoVisible = true
        result.deliveryInfo = invoiceInfo.deliveryInfo
      }
    }
    return result
  }
}

export default BatchOrderDetailVo
