schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""通过缴费渠道类型获取培训平台下该缴费渠道对应的发票配置信息
		@param placeChannelEnum 订单业务对象之下单渠道
		@return
	"""
	getBillConfigByPaymentChannel(placeChannelEnum:PaymentChannelTypeEnum):BillConfigDto
	"""通过渠道tab类型(即前端tab)获取发票配置
		@param paymentChannelTabType
		@return
	"""
	getInvoiceConfigByPaymentChannelTab(paymentChannelTabType:String):BillConfigDto
	"""通过渠道tab类型(即前端tab)获取发票配置
		@param paymentChannelTabType
		@return
	"""
	getInvoiceConfigByPaymentChannelTabAndSkuId(paymentChannelTabType:String,skuId:String):BillConfigDto
	"""根据渠道和支付方式获取收款账号
		@param placeChannelEnum
		@param payType
		@return
	"""
	getMerchantAccountList(placeChannelEnum:PaymentChannelTypeEnum,payType:Int!):[MerchantAccount]
	"""通过缴费渠道类型获取培训平台下该缴费渠道对应的收款账号信息
		@param placeChannelEnum 渠道
		@param payType          交易类型(1:代表线上;2:代表线下)
		@return
	"""
	getPaymentAccountByPaymentChannel(placeChannelEnum:PaymentChannelTypeEnum,payType:Int!):[PaymentAccount]
	"""通过渠道id获取发票配置
		@return
	"""
	getPaymentAccountByPaymentChannelId(paymentChannelId:String):BillConfigDto
	"""通过渠道Id获取收款账号列表
		@return
	"""
	getPaymentAccountListByPaymentChannelId(paymentChannelId:String):[PaymentAccount]
	"""根据缴费渠道类型和支付方式查询收款账号
		@return
	"""
	getPaymentAccountListByQueryParam(queryParam:ChannelPaymentAccountQueryParam):[PaymentAccount]
	"""获取渠道配置
		@return
	"""
	getPaymentChannelConfiguration:[PaymentChannelConfigurationDto]
	"""获取渠道配置,将web ios 微信合并到个人缴费
		@return
	"""
	getPaymentChannelConfiguration2:[PaymentChannelConfigurationDto]
	"""获取子项目渠道集合
		@return
	"""
	getPaymentChannelList:[PaymentChannelDto]
	"""根据参数 查询支付渠道信息
		@param queryDto
		@return
	"""
	listPaymentChannel(queryParams:PaymentChannelQueryRequest):[PaymentChannelInfoResponse]
}
type Mutation {
	"""为渠道增加收款账号
		@return
	"""
	addPaymentAccount(paymentChannelAccountAddDto:PaymentChannelAccountAddDto):Boolean!
	"""操作发票配置
		@return
	"""
	operaBillConfig(billConfigAddDto:BillConfigAddDto):String
	"""去除收款账号
		@return
	"""
	removePaymentAccount(paymentChannelAccountAddDto:PaymentChannelAccountAddDto):Boolean!
}
"""支付渠道查询对象
	<AUTHOR> create 2021/2/24 10:24
"""
input PaymentChannelQueryRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.PaymentChannelQueryRequest") {
	"""商品skuid"""
	commoditySkuId:String
}
"""缴费渠道收款账号查询参数
	Created by chenwq on 2018/1/22.
"""
input ChannelPaymentAccountQueryParam @type(value:"com.fjhb.btpx.platform.service.paymentchannel.dto.ChannelPaymentAccountQueryParam") {
	"""账户一级类型代表的是支付方式(1:线上,2:线下)"""
	firstType:Int
	"""缴费渠道类型"""
	channelTypeEnum:PaymentChannelTypeEnum
	"""指定查询商品skuId,指定的情况下会查询商品所属单位的对应收款账号"""
	skuId:String
}
"""Created by ljl on 2017/8/10."""
input PaymentChannelAccountAddDto @type(value:"com.fjhb.btpx.platform.service.paymentchannel.dto.PaymentChannelAccountAddDto") {
	"""收款账号Id"""
	accountId:String!
	"""渠道类型"""
	channelType:PaymentChannelTypeEnum
}
"""Created by ljl on 2017/8/10.
	<AUTHOR>
	@ipdate:  2018/04/28 删除冗余的字段
"""
input BillConfigAddDto @type(value:"com.fjhb.btpx.platform.service.paymentchannel.dto.billconfig.BillConfigAddDto") {
	"""发票配置 Id"""
	id:String
	"""收款账号id"""
	accountId:String
	"""tab类型"""
	tabType:String!
	"""是否提供发票|1：不提供 2：提供'"""
	isProvide:Int!
	"""发票提供类型|0：无 1：学员自选是否需要发票 2：强制提供'"""
	provideType:Int!
	"""电子发票查询地址"""
	eInvoiceSearchAddress:String
	"""是否选择增值税普通发票(发票类型)"""
	selectCommonVAT:Boolean
	"""是否选择普通电子发票(发票类型)"""
	selectCommonElectron:Boolean
	"""是否选择增值税专用发票(发票类型)"""
	selectVATOnly:Boolean
	"""是否选择非税务发票(发票类型)"""
	selectNonTax:Boolean
	"""是否选择了个人(发票抬头)"""
	selectPersonal:Boolean
	"""是否选择了单位(发票抬头)"""
	selectUnit:Boolean
}
"""支付渠道信息
	<AUTHOR> create 2021/2/24 10:19
"""
type PaymentChannelInfoResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.PaymentChannelInfoResponse") {
	"""支付渠道id"""
	paymentChannelId:String
}
"""收款账号
	<AUTHOR>
	@date 2018/4/12
"""
type MerchantAccount @type(value:"com.fjhb.btpx.platform.service.paymentchannel.dto.MerchantAccount") {
	"""账户id"""
	id:String
	"""账户别名"""
	accountAlias:String
	"""账户账号"""
	accountNo:String
	"""支付渠道的code"""
	code:String
	"""支付渠道的logoPath"""
	logoPath:String
}
"""Created by ljl on 2017/8/22."""
type PaymentChannelConfigurationDto @type(value:"com.fjhb.btpx.platform.service.paymentchannel.dto.PaymentChannelConfigurationDto") {
	"""@see PaymentChannelTypeEnum
		渠道类型
	"""
	channelType:String
	"""账户一级类型代表的是支付方式(1:线上,2:线下)
		只能是1或2
	"""
	firstTypes:[Int]
}
"""Created by ljl on 2017/8/10."""
type PaymentChannelDto @type(value:"com.fjhb.btpx.platform.service.paymentchannel.dto.PaymentChannelDto") {
	"""渠道Id"""
	id:String
	"""渠道类型"""
	type:String
	"""发票Id"""
	obcId:String
	"""是否可用"""
	enable:Boolean
	"""子项目Id"""
	subProjectId:String
}
"""缴费渠道类型
	<AUTHOR>
"""
enum PaymentChannelTypeEnum @type(value:"com.fjhb.btpx.platform.service.paymentchannel.dto.PaymentChannelTypeEnum") {
	"""web端"""
	WEB
	"""android客户端"""
	ANDROID
	"""ios客户端"""
	IOS
	"""微信公众号（订阅号）"""
	WECHAT_OFFICIAL_ACCOUNTS
	"""微信小程序"""
	WECHAT_MINI_PROGRAMS
	"""管理员现场开通"""
	PRESENT
	"""管理员集体缴费"""
	COLLECTIVE
	"""H5"""
	HTML5
	"""钉钉"""
	DINGDING
	"""渠道开通"""
	CHANNEL_PRESENT_OPEN
}
type BillConfigDto @type(value:"com.fjhb.btpx.platform.service.paymentchannel.dto.billconfig.BillConfigDto") {
	"""发票配置 Id"""
	id:String
	"""是否提供发票|1：不提供 2：提供'"""
	isProvide:Int!
	"""发票提供类型|0：无 1：学员自选是否需要发票 2：强制提供'"""
	provideType:Int!
	"""支持开票的发票类型 (将会是com.fjhb.courseSupermarket.service.bill.dto.InvoiceTypeEnum里面值的组合或者单个值，例如：COMMON_VAT&-&COMMON_ELECTRON)"""
	supportInvoiceTypes:String
	"""支持开票的发票抬头 (将会是com.fjhb.courseSupermarket.service.bill.dto.InvoiceTitleEnum里面值的组合或者单个值，例如：PERSONAL&-&UNIT)
		@see InvoiceTitleEnum
	"""
	supportInvoiceTitles:String
	"""是否选择增值税普通发票(发票类型)"""
	selectCommonVAT:Boolean!
	"""是否选择普通电子发票(发票类型)"""
	selectCommonElectron:Boolean!
	"""是否选择增值税专用发票(发票类型)"""
	selectVATOnly:Boolean!
	"""是否选择非税务发票(发票类型)"""
	selectNonTax:Boolean!
	"""是否选择了个人(发票抬头)"""
	selectPersonal:Boolean!
	"""是否选择了单位(发票抬头)"""
	selectUnit:Boolean!
}
"""@author: eleven
	@since: 2020/02/19 13:01
	@description: 收款账号
"""
type PaymentAccount @type(value:"com.fjhb.btpx.platform.service.paymentchannel.dto.paymentaccount.PaymentAccount") {
	"""收款账户别名"""
	accountAlias:String
	"""账户一级类型代表的是支付方式(1:线上,2:线下)
		只能是1或2
	"""
	firstType:Int
	"""二级类型(在一级类型的基础上拓展的描述在线下支付方式的情况下可能有"对汇"等)
		如果没有填写为空
	"""
	secondType:String
	"""企业名称"""
	merchantName:String
	"""企业联系电话"""
	merchantPhone:String
	"""开户银行"""
	depositBank:String
	"""开户银行的行号"""
	bankNumber:String
	"""柜台号"""
	counterNumber:String
	"""收款账户的第三方接口的账号"""
	accountNo:String
	"""建行接口需要的BRANCHID(分行代码)"""
	branchBankId:String
	"""状态0:启用,1:停用"""
	status:Int
	"""支付网关名称"""
	tradeChannelName:String
	"""支付渠道的code"""
	code:String
	"""授权状态"""
	authorizationState:AuthorizationStateEnum
	"""账户id"""
	id:String
	"""创建单位id"""
	unitId:String
	"""收款账号的创建方式"""
	createType:MerchantAccountCreateType
}
enum AuthorizationStateEnum @type(value:"com.fjhb.platformstandard.common.utils.dataauthorized.enumeration.AuthorizationStateEnum") {
	AUTHORIZATION
	CANCEL_AUTHORIZATION
}
enum MerchantAccountCreateType @type(value:"com.fjhb6.ability.orderPay.v1.commons.model.helper.MerchantAccountCreateType") {
	INTERNAL
	NORMAL
	AUTHORIZE
}

scalar List
