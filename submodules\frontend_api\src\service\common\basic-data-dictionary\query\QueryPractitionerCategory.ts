import BasicDataGateway, {
  TrainingPropertyQueryRequest,
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { listIndustryPropertyChildByCategoryV2 } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage/graphql-importer'
import Basicdata from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
import CertificatesTypeVo from '@api/service/common/basic-data-dictionary/query/vo/CertTypeVo'
import MajorParam from '@api/service/common/basic-data-dictionary/query/vo/majorParam'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { IndustryIdEnum } from '@api/service/training-institution/online-school/enum/IndustryEnum'
import Vue from 'vue'
export default new (class QuerypractitionerCategory {
  /**
   * 执业类别列表
   */
  practitionerCategoryList: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()

  /**
   * 执业类别列表缓存
   */
  practitionerCategoryCache: { [key: string]: CertificatesTypeVo } = {}
  /**
   * 执业类别列表v2
   */
  practitionerCategoryListV2: Array<CertificatesTypeVo>

  /**
   * 执业类别Map key: 字典code
   */
  private practitionerCategoryCodeMap: Map<number, TrainingPropertyResponse> = new Map<
    number,
    TrainingPropertyResponse
  >()

  /**
   * 查询对应行业下执业类别
   * @param gradePropertyId 学段属性id
   */
  async queryPractitionerCategoryByIndustry(gradePropertyId: string): Promise<Array<TrainingPropertyResponse>> {
    const propertyId = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustry.industryList.find(
      item => item.id == IndustryIdEnum.YS
    )?.propertyId
    const res = await BasicDataGateway.listALLIndustryPropertyRootByCategoryV2({
      industryId: gradePropertyId,
      industryPropertyId: propertyId,
      categoryCode: IndustryPropertyCodeEnum.PRACTITIONER_CATEGORY
    })

    return res.data || new Array<TrainingPropertyResponse>()
  }

  /**
   * 查询对应行业下执业类别V2
   * @param industryPropertyId 行业属性id
   * @param gradePropertyId 学段属性id
   */
  async queryPractitionerCategoryByGradeIdV2(
    industryPropertyId: string,
    gradePropertyId: string
  ): Promise<Array<TrainingPropertyResponse>> {
    const request = new TrainingPropertyQueryRequest()
    request.industryPropertyId = industryPropertyId
    request.propertyId = gradePropertyId
    request.categoryCode = IndustryPropertyCodeEnum.PRACTITIONER_CATEGORY
    const res = await BasicDataGateway.listIndustryPropertyChildByCategoryV2(request)
    res.data.map(item => {
      this.practitionerCategoryCodeMap.set(item.code, item)
    })
    return res.data || new Array<TrainingPropertyResponse>()
  }

  /**
   * 查询执业类别列表
   * @param 行业属性id 行业属性分类id 执业类别id
   * @return
   */
  async queryPractitionerCategory(params: MajorParam) {
    // todo 是否需要缓存
    const res = await BasicDataGateway.listIndustryPropertyChildByCategoryV2({
      industryPropertyId: params.industryPropertyId,
      categoryCode: 'PRACTITIONER_CATEGORY',
      propertyId: params.parentPropertyId
    })
    if (res.status.isSuccess()) {
      const currentIndustry = await BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustry.getIndustryByIdList(
        [params.industryPropertyId]
      )
      this.setTrainingMajorCache(currentIndustry[0].id, res.data)
      this.practitionerCategoryListV2 = res.data
    }
    return res.status
  }

  /**
   * 设置培训专业缓存
   */
  setTrainingMajorCache(industryId: string, practitionerCategoryListV2: Array<CertificatesTypeVo>) {
    practitionerCategoryListV2?.forEach(trainingMajor => {
      if (!this.practitionerCategoryCache[`${industryId}_${trainingMajor.propertyId}`]) {
        Vue.set(this.practitionerCategoryCache, `${industryId}_${trainingMajor.propertyId}`, trainingMajor)
      }
    })
  }

  /**
   * 查询对应行业下所有执业类别
   */
  async queryAllpractitionerCategory(): Promise<Array<TrainingPropertyResponse>> {
    this.practitionerCategoryList = new Array<TrainingPropertyResponse>()

    const res = await Basicdata.listBusinessDataDictionaryInSubProject({
      businessDataDictionaryType: IndustryPropertyCodeEnum.PRACTITIONER_CATEGORY
    })
    if (res.data?.length) {
      this.practitionerCategoryList = res.data.map(item => {
        const temp = new TrainingPropertyResponse()
        temp.propertyId = item.id
        temp.parentId = item.parentId
        temp.name = item.name
        temp.code = item.code
        temp.sort = item.sort
        return temp
      })
      this.practitionerCategoryList.map(item => {
        this.practitionerCategoryCodeMap.set(item.code, item)
      })
    }
    return this.practitionerCategoryList
  }

  /**
   * 批量查询对应学段下的执业类别
   * @param industryPropertyId 行业属性id
   * @param gradePropertyIds 学段属性id数组
   */
  async batchQueryPositionCategoryByGradeId(
    industryPropertyId: string,
    gradePropertyIds: Array<string>
  ): Promise<Array<TrainingPropertyResponse>> {
    const requestList = new Array<TrainingPropertyQueryRequest>()
    gradePropertyIds.map(item => {
      const req = new TrainingPropertyQueryRequest()

      req.industryPropertyId = industryPropertyId
      req.propertyId = item
      req.categoryCode = IndustryPropertyCodeEnum.PRACTITIONER_CATEGORY

      requestList.push(req)
    })

    const reWriteGQL = new RewriteGraph<TrainingPropertyResponse, TrainingPropertyQueryRequest>(
      BasicDataGateway._commonQuery,
      listIndustryPropertyChildByCategoryV2
    )
    await reWriteGQL.request(requestList)

    const result = new Array<TrainingPropertyResponse>()

    for (const value of reWriteGQL.itemMap.values()) {
      result.push(value)
    }

    return result
  }
  /**
   * 通过code获取详情
   * @param code 字典code
   */
  getPractitionerCategoryCodeDetail(code: number): TrainingPropertyResponse {
    return this.practitionerCategoryCodeMap.get(code)
  }
})()
