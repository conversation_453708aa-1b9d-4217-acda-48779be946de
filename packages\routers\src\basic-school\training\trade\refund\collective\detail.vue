<route-params content="/:id"></route-params>
<route-meta>
  {
  "isMenu":true,
  "hideMenu": true,
  "onlyShowOnTab":true,
  "title": "集体订单退款详情"
  }
</route-meta>

<script lang="ts">
  import RefundCollectiveDetail from '@hbfe/jxjy-admin-trade/src/refund/collective/detail.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, FXS, GYS, ZTGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    refundDetail: [WXGLY, FXS, GYS, ZTGLY],
    approve: [WXGLY, FXS, GYS, ZTGLY],
    retryRecycleRefund: [WXGLY, FXS, GYS, ZTGLY],
    confirmRefund: [WXGLY, FXS, GYS, ZTGLY],
    continueRefund: [WXGLY, FXS, GYS, ZTGLY]
  })
  export default class extends RefundCollectiveDetail {}
</script>
