schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""创建业务员"""
	createSalesman(request:CreateSalesmanRequest):CreateSalesmanResponse
	"""停用业务员"""
	disableSalesman(request:ChangeSalesmanStatusRequest):GeneralResponse
	"""启用业务员"""
	enableSalesman(request:ChangeSalesmanStatusRequest):GeneralResponse
	"""修改业务员"""
	updateSalesman(request:UpdateSalesmanRequest):GeneralResponse
}
"""启用业务员请求"""
input ChangeSalesmanStatusRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.sales.ChangeSalesmanStatusRequest") {
	"""业务员id"""
	salesmanId:String
}
"""@author: xucenhao
	@time: 2024-08-13
	@description: 创建业务员请求类
"""
input CreateSalesmanRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.sales.CreateSalesmanRequest") {
	"""姓名"""
	name:String
	"""手机号"""
	phone:String
	"""备注"""
	remark:String
}
"""修改业务员请求"""
input UpdateSalesmanRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.sales.UpdateSalesmanRequest") {
	"""业务员id"""
	id:String
	"""姓名"""
	name:String
	"""手机号"""
	phone:String
	"""备注"""
	remark:String
}
"""<AUTHOR> [2023/7/11 20:54]"""
type GeneralResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.GeneralResponse") {
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""@author: xucenhao
	@time: 2024-09-12
	@description:
"""
type CreateSalesmanResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.sales.CreateSalesmanResponse") {
	"""销售员ID"""
	salesmanId:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}

scalar List
