import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/gql/ms-wechat-v1'

// 是否微服务
const isMicroService = true

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-wechat-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: 'ms-wechat-v1'
}

// 枚举

// 类

/**
 * 微信公众号JS SDK 权限签名申请信息
 */
export class ApplyJsSDKSignatureRequest {
  /**
   * 公众号AppId，不填表示当前项目默认只有一个微信公众号，自动从配置中获取
   */
  appId?: string
  /**
   * 【必填】当前网页的URL，不包含#及其后面部分
   */
  url?: string
}

/**
 * 获取二维码
@author: zhengp
@since 2021/9/2 10:16
 */
export class GetAppletCodeRequest {
  /**
   * 微信小程序 appId 不填表示当前项目默认只有一个小程序，自动从配置中获取
   */
  appId?: string
  /**
   * page参数 选填 如果不填写这个字段，默认跳主页面
   */
  page?: string
  /**
   * scene参数 必填
   */
  scene?: string
  /**
   * width参数 选填
   */
  width?: string
  /**
   * 自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调，默认 false 选填
   */
  autoColor?: boolean
  /**
   * auto_color 为 false 时生效，使用 rgb 设置颜色 例如 {&quot;r&quot;:&quot;xxx&quot;,&quot;g&quot;:&quot;xxx&quot;,&quot;b&quot;:&quot;xxx&quot;} 十进制表示 选填
   */
  lineColor?: string
  /**
   * 是否需要透明底色，为 true 时，
生成透明底色的小程序 选填
   */
  isHyaline?: boolean
}

/**
 * 获取解密手机号
@author: zhengp 2021/12/2 16:34
 */
export class GetDecodeTelephoneRequest {
  /**
   * 用户openId [必填]
   */
  openId: string
  /**
   * 加密数据 [必填]
   */
  encryptedData: string
  /**
   * 加密算法的初始向量 [必填]
   */
  iv: string
}

/**
 * 获取微信用户信息
@author: zhengp
@since 2021/9/3 14:16
 */
export class GetUserInfoRequest {
  /**
   * 微信appId，不填表示当前项目默认只有一个微信开发平台程序，自动从配置中获取
   */
  appId?: string
  /**
   * 授权临时票据code
   */
  code?: string
  /**
   * 国家地区语言版本，默认为中文简体
   */
  lang?: string
}

/**
 * 获取微信opneiId和unionId等信息的入参
@author: zhengp
@since 2021/9/9 10:00
 */
export class WXAppletInfoQueryParam {
  appId?: string
  code?: string
  encryptedData?: string
  iv?: string
}

/**
 * @author: zhengp
@since 2021/9/10 9:37
 */
export class GetAppletCodeResponse {
  /**
   * 二维码存储mfs地址
   */
  mfsAddress: string
}

/**
 * 获取微信用户信息
@author: zhengp
@since 2021/9/3 14:16
 */
export class GetUserInfoResponse {
  /**
   * 用户统一标识。针对一个微信开放平台帐号下的应用，同一用户的unionid是唯一的。
   */
  unionId: string
  /**
   * 普通用户的标识，对当前开发者帐号唯一
   */
  openId: string
  /**
   * 普通用户昵称
   */
  nickName: string
  /**
   * 普通用户性别，1为男性，2为女性
   */
  sex: number
  /**
   * 国家，如中国为CN
   */
  country: string
  /**
   * 普通用户个人资料填写的省份
   */
  province: string
  /**
   * 普通用户个人资料填写的城市
   */
  city: string
  /**
   * 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像）
   */
  headImageUrl: string
}

/**
 * 微信公众号JS SKD 权限签名信息
 */
export class JsSDKSignatureResponse {
  /**
   * 生成签名的随机串
   */
  nonceStr: string
  /**
   * 生成签名的时间戳
   */
  timestamp: number
  /**
   * 签名
   */
  signature: string
}

/**
 * 手机号信息
@author: zhengp 2021/12/3 16:21
 */
export class TelephoneData {
  /**
   * 200 成功 500 失败
   */
  code: number
  msg: string
  data: Result
}

export class Result {
  /**
   * 用户绑定的手机号（国外手机号会有区号）
   */
  phoneNumber: string
  /**
   * 没有区号的手机号
   */
  purePhoneNumber: string
  /**
   * 区号
   */
  countryCode: string
}

/**
 * @author: zhengp
@since 2021/9/9 10:02
获取微信必要信息
 */
export class WXAppletIdInfoResponse {
  openId: string
  unionId: string
  accessToken: string
  nickname: string
  refreshToken: string
  sex: string
  headimgurl: string
  purePhoneNumber: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAppletCode(
    request: GetAppletCodeRequest,
    query: DocumentNode = GraphqlImporter.getAppletCode,
    operation?: string
  ): Promise<Response<GetAppletCodeResponse>> {
    return commonRequestApi<GetAppletCodeResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取解密后的手机号
   * @param request 加密的数
   * @return 解密后的json串
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDecodeTelephone(
    request: GetDecodeTelephoneRequest,
    query: DocumentNode = GraphqlImporter.getDecodeTelephone,
    operation?: string
  ): Promise<Response<TelephoneData>> {
    return commonRequestApi<TelephoneData>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUserInfo(
    request: GetUserInfoRequest,
    query: DocumentNode = GraphqlImporter.getUserInfo,
    operation?: string
  ): Promise<Response<GetUserInfoResponse>> {
    return commonRequestApi<GetUserInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getWXAppletUserInfo(
    request: WXAppletInfoQueryParam,
    query: DocumentNode = GraphqlImporter.getWXAppletUserInfo,
    operation?: string
  ): Promise<Response<WXAppletIdInfoResponse>> {
    return commonRequestApi<WXAppletIdInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请微信公众号JS SDK 权限签名
   * <p>微信文档地址：https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#62</p>
   * @param applyInfo 申请信息
   * @return 签名信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyJsSDKSignature(
    applyInfo: ApplyJsSDKSignatureRequest,
    mutate: DocumentNode = GraphqlImporter.applyJsSDKSignature,
    operation?: string
  ): Promise<Response<JsSDKSignatureResponse>> {
    return commonRequestApi<JsSDKSignatureResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
