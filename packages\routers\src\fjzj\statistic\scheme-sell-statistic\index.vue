<route-meta>
{
"isMenu": true,
"title": "方案开通统计",
"sort": 1,
"icon": "icon-ribaotongji"
}
</route-meta>

<script lang="ts">
  import SchemeSellStatistic from '@hbfe/jxjy-admin-schemeSellStatistic/src/diff/fjzj/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import {
    // 施教机构管理员
    WXGLY
  } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY],
    export: [WXGLY]
  })
  export default class extends SchemeSellStatistic {}
</script>
