import QuestionType from '@api/service/common/enums/question/QuestionType'
import UpdateQuestionVo from './UpdateQuestionVo'
import ChooseAnswerOptionVo from '../ChooseAnswerOptionVo'
import { UpdateRadioQuestionRequest } from '@api/ms-gateway/ms-examquestion-v1'
import { RadioQuestionResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
/*
 * 单选题
 */
class UpdateRadioQuestionVo extends UpdateQuestionVo {
  questionType = QuestionType.enum.radio
  /**
   * 可选答案列表【必填】
   */
  answerOptions: Array<ChooseAnswerOptionVo> = new Array<ChooseAnswerOptionVo>()
  /**
   * 正确答案ID【必填】
   */
  correctAnswerId = ''

  // 模型转转Vo
  from(data: RadioQuestionResponse) {
    this.id = data.questionId
    this.topic = data.topic
    this.questionType = data.questionType
    this.libraryId = data.libraryInfo.libraryId
    this.dissects = data.dissects
    this.relateCourseId = data.relateCourseIds?.join('')
    this.questionDifficulty = data.questionDifficulty
    this.answerOptions = data.radioAnswerOptions?.map(item => {
      return {
        id: item.id,
        content: item.content
      }
    })
    this.correctAnswerId = data.correctAnswerId
  }

  // 模型转换为Dto
  toDto() {
    const updateQuestionDto = new UpdateRadioQuestionRequest()
    updateQuestionDto.id = this.id
    updateQuestionDto.topic = this.topic
    updateQuestionDto.questionType = this.questionType
    updateQuestionDto.libraryId = this.libraryId
    updateQuestionDto.dissects = this.dissects
    updateQuestionDto.relateCourseIds = [this.relateCourseId]
    updateQuestionDto.correctAnswerId = this.correctAnswerId
    updateQuestionDto.questionDifficulty = this.questionDifficulty
    updateQuestionDto.answerOptions = this.answerOptions?.map(item => {
      return {
        id: item.id,
        content: item.content
      }
    })
    return updateQuestionDto
  }
}

export default UpdateRadioQuestionVo
