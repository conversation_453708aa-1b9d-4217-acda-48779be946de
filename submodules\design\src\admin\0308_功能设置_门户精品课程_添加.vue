<template>
  <el-main>
    <el-card shadow="never" class="m-card f-mb15">
      <!--添加精品课程-->
      <el-button @click="dialog1 = true" type="primary" class="f-mr20">添加精品课程</el-button>
      <el-drawer
        title="添加精品课程"
        :visible.sync="dialog1"
        :direction="direction"
        size="1000px"
        custom-class="m-drawer"
      >
        <div class="drawer-bd">
          <el-row :gutter="16" class="m-query f-mt10">
            <el-form :inline="true" label-width="auto">
              <el-col :span="10">
                <el-form-item label="课程名称">
                  <el-input v-model="input" clearable placeholder="请输入课程名称" />
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="课程分类">
                  <el-select clearable placeholder="请选择课程分类">
                    <el-option label="选项1" value=""></el-option>
                    <el-option label="选项2" value=""></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item>
                  <el-button type="primary">查询</el-button>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
          <el-alert type="warning" show-icon :closable="false" class="m-alert">
            请选择精品课程分类下的课程，每一个分类最多添加 7 门。请选择最末级的课程分类下的课程。
          </el-alert>
          <!--表格-->
          <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="课程名称" min-width="300">
              <template>课程名称课程名称课程名称课程名称课程名称</template>
            </el-table-column>
            <el-table-column label="课程分类" min-width="180">
              <template>课程分类课程分类</template>
            </el-table-column>
            <el-table-column label="学时" min-width="100" align="center">
              <template>10</template>
            </el-table-column>
            <el-table-column label="查看" width="80" align="center" fixed="right">
              <template>
                <el-button type="text" size="mini">详情</el-button>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template>
                <el-checkbox v-model="checked" label="选择"></el-checkbox>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>
          <div class="m-btn-bar f-tc f-mt20">
            <el-button>取消</el-button>
            <el-button type="primary">保存</el-button>
          </div>
        </div>
      </el-drawer>
      <!--添加分类-->
      <el-button @click="dialog2 = true" type="primary" class="f-mr20">添加分类</el-button>
      <el-drawer
        title="新增精品课程分类"
        :visible.sync="dialog2"
        :direction="direction"
        size="600px"
        custom-class="m-drawer"
      >
        <div class="drawer-bd">
          <el-row type="flex" justify="center">
            <el-col :span="18">
              <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt40">
                <el-form-item label="选择课程分类：" required>
                  <el-cascader :options="cascader" :props="props" collapse-tags clearable />
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-button>取消</el-button>
                  <el-button type="primary">保存</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-drawer>
    </el-card>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        props: { multiple: true },
        activeName: 'six',
        activeName1: 'first',
        activeName2: 'first',
        checked: false,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
