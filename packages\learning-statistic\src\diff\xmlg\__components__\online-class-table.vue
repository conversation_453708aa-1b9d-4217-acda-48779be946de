<template>
  <OnlineClassTable v-bind="$attrs" v-on="$listeners">
    <template #schemeName="{ scope }">
      <div>{{ scope.row.trainClassBaseInfo.name || '-' }}</div>
      <div v-if="isHywSpecialScheme(scope.row)">
        <div v-if="scope.row.dropClassExtendedInfoResponse && scope.row.dropClassExtendedInfoResponse.publicSign">
          (2025年公需课已退)
        </div>
        <div v-if="scope.row.dropClassExtendedInfoResponse && scope.row.dropClassExtendedInfoResponse.professionalSign">
          (华医网2025年专业课已退)
        </div>
      </div>
    </template>
  </OnlineClassTable>
</template>

<script lang="ts">
  import OnlineClassTable from '@hbfe/jxjy-admin-learningStatistic/src/__components__/online-class-table.vue'
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplicationDiff } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import { StudentLearningStaticsVo } from '@api/service/diff/management/xmlg/statistical-report/query/vo/StudentLearningStaticsVo'

  @Component({
    components: {
      OnlineClassTable
    }
  })
  export default class extends Vue {
    /**
     * 获取华医网特殊方案
     */
    get hywSchemeList(): string[] {
      const list: { xmlgSchemeId: string }[] = JSON.parse(
        CommonConfigCenter.getFrontendApplicationDiff(frontendApplicationDiff.hywSchemeId)
      )
      return list.map((item) => item.xmlgSchemeId)
    }

    /**
     * 是否是华医网特殊方案
     */
    get isHywSpecialScheme(): (item: StudentLearningStaticsVo) => boolean {
      const schemeIds = this.hywSchemeList
      return (item: StudentLearningStaticsVo) => schemeIds.includes(item.trainClassBaseInfo.id)
    }
  }
</script>
<style></style>
