<template>
  <div>
    <el-card shadow="never" class="m-card f-mb15">
      <div class="m-tit is-border-bottom f-justify-between">
        <span class="tit-txt">基础信息</span>
        <el-link type="primary" :underline="false" class="m-specialimg-pop"
          ><i class="el-icon-picture f-f20 f-mr5 f-vm"></i>查看专题示例
          <el-image class="transparent-pic" :src="elImage" :preview-src-list="reviewImage" />
        </el-link>
      </div>
      <el-row type="flex" justify="center" class="width-limit">
        <el-col :md="20" :lg="16" :xl="13">
          <el-form ref="form" :rules="rules" :model="basicInfo" label-width="auto" class="m-form f-mt20 f-mb40">
            <el-form-item label="专题入口名称：" prop="entryName">
              <el-input
                v-model="basicInfo.entryName"
                clearable
                placeholder="请输入本专题在网校上呈现的名称，不允许重复，最多8个字"
                maxlength="8"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="专题名称：" prop="subjectName">
              <el-input v-model="basicInfo.subjectName" clearable placeholder="请输入专题名称" />
              <div class="f-c9">
                <i class="el-icon-warning f-f16 f-mr5 f-vm"></i
                >专题名称将同步显示在web端浏览器标签页标题与H5专题的标题。
              </div>
            </el-form-item>
            <el-form-item label="域名类型：" prop="domainType">
              <el-radio-group v-model="basicInfo.domainType">
                <el-radio :label="domainTypeEnum.systemdefault" border class="f-mr10">系统默认域名</el-radio>
                <el-radio :label="domainTypeEnum.owndomain" border>自有域名</el-radio>
              </el-radio-group>
              <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                <div slot="content">
                  <p>域名需联系平台技术方进行域名处理才能访问。如使用自有域名请提前进行华为云备案。</p>
                </div>
              </el-tooltip>
            </el-form-item>
            <el-form-item
              label="专题域名："
              prop="subjectDomain"
              v-if="basicInfo.domainType == domainTypeEnum.systemdefault"
            >
              <el-input placeholder="请输入指定的二级域名" v-model="subjectDomain">
                <template slot="prepend">https://</template>
                <template slot="append">.59iedu.com</template>
              </el-input>
            </el-form-item>
            <el-form-item
              label="专题域名："
              prop="subjectDomain"
              v-if="basicInfo.domainType == domainTypeEnum.owndomain"
            >
              <el-input v-model="basicInfo.subjectDomain" clearable placeholder="请输入指定的专题域名" />
            </el-form-item>
            <el-form-item label="专题类型：" prop="subjectType">
              <el-checkbox-group v-model="basicInfo.subjectType" disabled>
                <el-checkbox :label="subjectType.region" border class="f-mr10">地区</el-checkbox>
                <el-checkbox :label="subjectType.industry" border class="f-mr10">行业</el-checkbox>
                <el-checkbox :label="subjectType.unit" border class="f-mr10">单位</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item v-if="basicInfo.subjectType.includes(subjectType.region)" label="地区：" prop="regionValue">
              <div class="u-w300">
                <biz-region-cascader
                  disabled
                  v-model="region"
                  :check-strictly="true"
                  placeholder="请选择地区"
                ></biz-region-cascader>
                <!-- <biz-special-type v-model="basicInfo.subjectTypeValue"></biz-special-type> -->
              </div>
            </el-form-item>
            <el-form-item
              v-if="basicInfo.subjectType.includes(subjectType.industry)"
              label="行业："
              prop="suiteIndustry"
            >
              <div class="u-w300">
                <industry-select
                  disabled
                  v-model="basicInfo.suiteIndustry"
                  @industryInfos="handleIndustryInfos"
                ></industry-select>
              </div>
            </el-form-item>
            <el-form-item v-if="basicInfo.subjectType.includes(subjectType.unit)" label="单位：" prop="unitName">
              <el-input
                disabled
                v-model="basicInfo.unitName"
                clearable
                placeholder="请输入所属单位（全称）"
                style="width: 360px"
              />
            </el-form-item>
            <el-form-item label="显示在网校：" prop="displayInSchool">
              <el-radio-group v-model="basicInfo.displayInSchool">
                <el-radio :label="true" border class="f-mr10">显示</el-radio>
                <el-radio :label="false" border>不显示</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="是否允许访问：" prop="allowAccess">
              <el-radio-group v-model="basicInfo.allowAccess">
                <el-radio :label="true" border class="f-mr10">允许</el-radio>
                <el-radio :label="false" border>不允许</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div class="m-tit is-border-bottom">
        <span class="tit-txt">专题PC端模板</span>
      </div>
      <el-row type="flex" justify="center">
        <el-col :md="17" :lg="17" :xl="17">
          <el-form label-width="auto" class="m-form f-mt40 f-ml40 f-mr40">
            <el-form-item label="">
              <ul class="m-demo-pic">
                <li v-for="item in TopicTemplateModule['_templatePCList']" :key="item.id">
                  <div class="demo-pic">
                    <div class="mask" @click="preview(getPath(item.reviewPath))">
                      <i class="icon el-icon-zoom-in"></i>
                    </div>
                    <div class="pic">
                      <img :src="getPath(item.reviewPath)" />
                    </div>
                    <el-checkbox v-model="templateId" :true-label="item.id" @change="handleChange(item.id)">{{
                      templateId === item.id ? '当前已选' : '请选择'
                    }}</el-checkbox>
                  </div>
                  <div class="demo-pic-info">
                    <p><span class="t">banner轮播图片尺寸：</span>{{ item.bannerSize[0] }}*{{ item.bannerSize[1] }}</p>
                    <p>
                      <span class="t">客服电话图片尺寸：</span>{{ item.customerPhoneSize[0] }}*{{
                        item.customerPhoneSize[1]
                      }}
                    </p>
                    <p>
                      <span class="t">集体报名图片尺寸：</span>{{ item.singleOnlineCollectSize[0] }}*{{
                        item.singleOnlineCollectSize[1]
                      }}或{{ item.doubleOnlineCollectSize[0] }}*{{ item.doubleOnlineCollectSize[1] }}
                    </p>
                    <p><span class="t">logo尺寸：</span>{{ item.logoSize[0] }}*{{ item.logoSize[1] }}</p>
                    <p>
                      <span class="t">流程图尺寸：</span>{{ item.trainingProcessSize[0] }}*{{
                        item.trainingProcessSize[1]
                      }}
                    </p>
                    <p>
                      <span class="t">企业微信客服图片尺寸：</span>{{ item.wechatCustomerServiceSize[0] }}*{{
                        item.wechatCustomerServiceSize[1]
                      }}
                    </p>
                  </div>
                </li>
              </ul>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div class="m-tit is-border-bottom">
        <span class="tit-txt">专题H5端模板</span>
      </div>
      <el-row type="flex" justify="center">
        <el-col :md="17" :lg="17" :xl="17">
          <el-form label-width="auto" class="m-form f-mt40 f-ml40 f-mr40">
            <el-form-item label="">
              <ul class="m-demo-pic">
                <li v-for="item in TopicTemplateModule['_templateH5List']" :key="item.id">
                  <div class="demo-pic">
                    <!-- <div class="mask" @click="preview(getPath(item.reviewPath))">
                      <i class="icon el-icon-zoom-in"></i>
                    </div> -->
                    <div class="pic">
                      <img :src="getPath(item.reviewPath)" />
                    </div>
                    <!-- <el-checkbox v-model="checked">当前已选</el-checkbox> -->
                  </div>
                  <div class="demo-pic-info">
                    <p><span class="t">banner轮播图片尺寸：</span>{{ item.bannerSize[0] }}*{{ item.bannerSize[1] }}</p>
                  </div>
                </li>
              </ul>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div class="m-btn-bar f-tc is-sticky f-pt15" style="z-index: 8">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="stored" :loading="btLoading">保存</el-button>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts">
  import { DomainTypeEnum } from '@api/service/management/thematic-management/enum/DomainType'
  import { SubjectType } from '@api/service/management/thematic-management/enum/SubjectType'
  import BasicInfo from '@api/service/management/thematic-management/model/BasicInfo'
  import PortalInfo from '@api/service/management/thematic-management/model/PortalInfo'
  import BizIndustrySelect from '@hbfe/jxjy-admin-components/src/biz/biz-industry-select.vue'
  import BizRegionCascader from '@hbfe/jxjy-admin-components/src/biz/biz-region-cascader.vue'
  import IndustrySelect from '@hbfe/jxjy-admin-specialTopics/src/add/components/industry-select.vue'
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import TopicTemplateModule from '@api/service/common/template-school/TopicTemplateModule'

  @Component({
    components: {
      BizRegionCascader,
      BizIndustrySelect,
      IndustrySelect
    }
  })
  export default class extends Vue {
    @Prop({
      type: Object,
      default: () => {
        return new BasicInfo()
      }
    })
    basicInfo: BasicInfo
    @Prop({
      type: Object,
      default: () => {
        return new PortalInfo()
      }
    })
    portalInfo: PortalInfo
    subjectType = SubjectType //专题状态枚举
    domainTypeEnum = DomainTypeEnum
    btLoading = false
    checkedOne = false
    checkedTwo = false
    subjectDomain = '' //绑定二级域名
    region = new Array<string>()
    elImage = require('@design/admin/assets/images/transparent-pic.png')
    src = [
      require('@design/admin/assets/images/demo-special-web-001.png'),
      require('@design/admin/assets/images/demo-special-h5-001.png'),
      require('@design/admin/assets/images/s-template01.jpg'),
      require('@design/admin/assets/images/demo-h5-homepage-2.png')
    ]
    /**
     * 当前网校信息
     */
    envConfig = {
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      //工勤行业
      workServiceId: '',
      // 职业卫生行业Id
      professionHealthIndustryId: '',
      //教师行业
      teacherIndustryId: ''
    }
    rules = {
      entryName: [{ required: true, message: '请输入专题入口名称', max: 8, trigger: 'blur' }],
      subjectName: [{ required: true, message: '请输入专题名称', trigger: 'blur' }],
      domainType: [{ required: true, message: '请选择域名类型', trigger: 'blur' }],
      subjectDomain: [{ required: true, message: '请输入专题域名', trigger: 'blur' }],
      subjectType: [{ required: true, message: '请选择专题类型', trigger: 'blur' }],
      displayInSchool: [{ required: true, message: '请选择是否显示在网校', trigger: 'blur' }],
      allowAccess: [{ required: true, message: '请选择是否允许访问', trigger: 'blur' }],
      regionValue: [{ required: true, validator: this.validateRegion, message: '请选择地区', trigger: 'blur' }],
      suiteIndustry: [{ required: true, message: '请选择行业', trigger: 'blur' }],
      unitName: [{ required: true, message: '请选择单位', trigger: 'blur' }]
    }

    /**
     * 专题模板
     */
    TopicTemplateModule = TopicTemplateModule
    /**
     * PC模板id
     */
    templateId = ''
    @Ref('form') formRef: any

    /**
     * 校验地区是否填写
     */
    validateRegion(rule: any, value: any, callback: any) {
      if (this.region.length === 0) {
        callback(new Error('请选择地区'))
      } else {
        callback()
      }
    }
    /**
     * 获取查看专题示例图片
     */
    get reviewImage() {
      const reviewImageObj = TopicTemplateModule.getTemplate(this.templateId)
      if (reviewImageObj?.reviewPath) {
        return [require(`@design/admin/assets/images/${reviewImageObj?.reviewPath}`)]
      } else {
        return [require('@design/admin/assets/images/demo-special-web-001.png')]
      }
    }
    /**
     * 判断行业或者地区是否已填写
     */
    get judgeSubjectType() {
      if (!this.basicInfo.subjectType.length) return false
      else if (this.basicInfo.subjectType.includes(SubjectType.region) && !this.region.length) return false
      else if (this.basicInfo.subjectType.includes(SubjectType.industry) && !this.basicInfo.suiteIndustry) return false
      else if (this.basicInfo.subjectType.includes(SubjectType.unit) && !this.basicInfo.unitName) return false
      return true
    }

    @Watch('basicInfo', {
      deep: true,
      immediate: true
    })
    basicInfoSet(val: BasicInfo) {
      this.templateId = this.basicInfo.templateWeb
      if (val.subjectType.includes(this.subjectType.region) && val.suiteArea.includes('/')) {
        this.region = val.suiteArea.split('/')
        this.region.shift()
      }
      if (val.templateH5 == 'Temlpate-H5-01') {
        this.checkedOne = true
      }
      if (this.basicInfo.subjectType.includes(this.subjectType.unit)) {
        this.$emit('haveUnit', true)
      } else {
        this.$emit('haveUnit', false)
      }
      if (this.basicInfo.domainType == this.domainTypeEnum.systemdefault) {
        if (this.basicInfo.subjectDomain.includes('https://')) {
          const backString = this.basicInfo.subjectDomain.split('https://')
          const middleString = backString[1].split('.59iedu.com')
          this.subjectDomain = middleString[0]
        } else if (this.basicInfo.subjectDomain.includes('http://')) {
          const backString = this.basicInfo.subjectDomain.split('http://')
          const middleString = backString[1].split('.59iedu.com')
          this.subjectDomain = middleString[0]
        } else {
          const middleString = this.basicInfo.subjectDomain.split('.59iedu.com')
          this.subjectDomain = middleString[0]
        }
      }
    }
    //预览图片
    preview(url: string) {
      window.open(url, '_blank')
    }
    get notEmpty() {
      return this.basicInfo.domainType && (this.checkedOne || this.checkedTwo) && this.judgeSubjectType
    }
    //切换专题模板后判断是否需要提示
    changeCheck() {
      const isChangeOne = this.basicInfo.templateH5 == 'Temlpate-H5-01' && this.checkedTwo
      const isChangeTwo = this.basicInfo.templateH5 == 'Temlpate-H5-02' && this.checkedOne
      //判断门户信息是否不为空,且更换了模板选择
      if (this.portalInfo.logoType && (isChangeOne || isChangeTwo)) {
        this.$confirm('切换专题模板后,将清空所有的门户信息配置,确认要切换?', '系统提示', {
          confirmButtonText: '确定切换模板',
          showCancelButton: true,
          cancelButtonText: '取消'
        })
          .then(() => {
            this.$emit('clearPortalInfo')
          })
          .catch(() => {
            if (this.basicInfo.templateH5 == 'Temlpate-H5-01') {
              this.checkedOne = true
              this.checkedTwo = false
            } else if (this.basicInfo.templateH5 == 'Temlpate-H5-02') {
              this.checkedOne = false
              this.checkedTwo = true
            }
          })
      }
    }
    /**
     * 获取图片路径
     */
    getPath(url: string) {
      return require('@design/admin/assets/images/' + url)
    }
    /**
     * 选择模板
     */
    handleChange(e: any) {
      if (!this.templateId) {
        this.templateId = ''
        this.basicInfo.templateWeb = ''
        this.portalInfo.pcTemplateId = ''
      } else {
        this.templateId = e
        this.basicInfo.templateWeb = this.templateId
        this.portalInfo.pcTemplateId = this.templateId
      }
    }
    /**
     * 响应组件行业Id集合传参
     */
    handleIndustryInfos(values: any) {
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.professionHealthIndustryId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
    }
    //处理传参
    setParams() {
      //   this.basicInfo.templateWeb = this.checkedOne ? 'Temlpate-Web-01' : 'Temlpate-Web-02'
      this.basicInfo.templateH5 = this.checkedOne ? 'Temlpate-H5-01' : 'Temlpate-H5-02'
      if (this.basicInfo.domainType == this.domainTypeEnum.systemdefault) {
        this.basicInfo.subjectDomain = this.subjectDomain + '.59iedu.com'
      }
      if (this.basicInfo.subjectType.includes(SubjectType.region)) {
        this.basicInfo.suiteArea = this.region[this.region.length - 1]
      }
    }
    //保存
    stored() {
      // if (
      //   (this.basicInfo.subjectType == this.subjectType.region && !this.region.length) ||
      //   (this.basicInfo.subjectType == this.subjectType.industry && !this.basicInfo.subjectTypeValue.length)
      // ) {
      //   this.$message.error('请完善信息')
      //   return
      // }
      this.formRef.validate((value: boolean) => {
        this.btLoading = true
        if (value && this.notEmpty) {
          this.setParams()
          this.$emit('saveBasicInfo')
        } else if (!this.checkedOne && !this.checkedTwo) {
          this.btLoading = false
          this.$message.error('请选择专题模板')
        } else {
          this.btLoading = false
          this.$message.error('请完善信息')
        }
      })
    }
    //取消编辑
    cancel() {
      const that = this as any
      //TODO 需要删除编辑的数据
      this.$confirm('取消后,当前调整的内容不保存,确定取消?', '提示', {
        confirmButtonText: '确认',
        showCancelButton: true
      }).then(() => {
        that.$message.success('已取消本次编辑')
        this.$router.push({
          path: '/training/special-topics/manage'
        })
      })
    }
  }
</script>
