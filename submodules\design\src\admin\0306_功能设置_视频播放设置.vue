<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="注册登录" name="first">详见 0301_功能设置_注册登录.vue</el-tab-pane>
      <el-tab-pane label="集体报名" name="second">详见 0303_功能设置_集体报名.vue</el-tab-pane>
      <el-tab-pane label="增值税电子普通发票（自动开票）" name="third">详见 0304_功能设置_增值税发票.vue</el-tab-pane>
      <el-tab-pane label="培训证明" name="fourth">详见 0305_功能设置_培训证明.vue</el-tab-pane>
      <el-tab-pane label="视频播放设置" name="five">
        <div class="f-p15">
          <el-card shadow="never" class="m-card f-mb15">
            <el-row type="flex" justify="center" class="width-limit">
              <el-col :md="20" :lg="16" :xl="13">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="全网校的防录屏跑马灯：">
                    <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                  </el-form-item>
                  <el-form-item label="视频贴片：">
                    <el-upload action="#" list-type="picture-card" :auto-upload="false" class="m-pic-upload long-pic">
                      <div slot="default" class="upload-placeholder">
                        <i class="el-icon-plus"></i>
                        <p class="txt">上传图片</p>
                      </div>
                      <div slot="file" slot-scope="{ file }" class="img-file">
                        <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                        <div class="el-upload-list__item-actions">
                          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                            <i class="el-icon-zoom-in"></i>
                          </span>
                          <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                            <i class="el-icon-delete"></i>
                          </span>
                        </div>
                      </div>
                      <div slot="tip" class="el-upload__tip">
                        <i class="el-icon-warning"></i>
                        <span class="txt">
                          视频贴片是视频播放器在播放前展示的图片。请先设计好后上传，尺寸：700px * 394px。
                        </span>
                      </div>
                    </el-upload>
                    <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                      <img :src="dialogImageUrl" alt="" />
                    </el-dialog>
                  </el-form-item>
                  <el-form-item label="贴片播放时长：">
                    <el-input v-model="form.name" class="input-num" />
                    <span class="f-mlr10">时</span>
                    <el-input v-model="form.name" class="input-num" />
                    <span class="f-mlr10">分</span>
                    <el-input v-model="form.name" class="input-num" />
                    <span class="f-mlr10">秒</span>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">保存</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane label="门户精品课程" name="six">详见 0307_功能设置_门户精品课程.vue</el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'five',
        activeName1: 'first',
        activeName2: 'first',
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
