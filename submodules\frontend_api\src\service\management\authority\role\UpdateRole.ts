import MsBasicdataDomainGatewayV1 from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { ResponseStatus } from '@hbfe/common'
import RoleDetail from '@api/service/management/authority/role/RoleDetail'
import RoleBaseInfo from '@api/service/management/authority/role/RoleBaseInfo'

class UpdateRole extends RoleDetail {
  loadedSelected = false

  async doUpdate() {
    this.onSaving = true
    try {
      const result = await MsBasicdataDomainGatewayV1.updateRole(this)
      return result.status
    } catch (e) {
      return new ResponseStatus(500, '系统异常')
    } finally {
      this.onSaving = false
    }
  }

  toJSON() {
    return Object.assign(super.toJSON(), { id: this.id })
  }

  /**
   * 更新角色
   */
  async updateRoleByAdminType() {
    this.onSaving = true
    try {
      const request = RoleBaseInfo.toUpdateRoleByAdminTypeRequest(this)
      const result = await MsBasicdataDomainGatewayV1.updateRoleByAdminType(request)
      return result.status
    } catch (e) {
      return new ResponseStatus(500, '系统异常')
    } finally {
      this.onSaving = false
    }
  }
}

export default UpdateRole
