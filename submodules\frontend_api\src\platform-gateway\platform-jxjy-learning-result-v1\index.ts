import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjy-learning-result-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjy-learning-result-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 学习成果导入请求
 */
export class IssueLearningResultImportRequest {
  /**
   * 期别id
   */
  issueId: string
  /**
   * 导入文件路径
   */
  importFilePath: string
  /**
   * 导入文件名
   */
  importFileName: string
}

export class ImportGradeResultTaskQueryRequest {
  /**
   * 任务名称
   */
  taskName?: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState?: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult?: number
  /**
   * 执行时间（起始）
   */
  executeStartTime?: string
  /**
   * 执行时间（终止）
   */
  executeEndTime?: string
}

/**
 * 导入成绩excel信息
 */
export class ImportGradeResultExcelResponse {
  /**
   * 文件路径
   */
  filePath: string
  /**
   * 文件名
   */
  fileName: string
}

/**
 * 导入成绩任务信息
 */
export class ImportGradeResultTaskInfoResponse {
  /**
   * 任务编号
   */
  id: string
  /**
   * 【必填】平台编号
   */
  platformId: string
  /**
   * 【必填】平台版本编号
   */
  platformVersionId: string
  /**
   * 【必填】项目编号
   */
  projectId: string
  /**
   * 【必填】子项目编号
   */
  subProjectId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务分类
   */
  category: string
  /**
   * 所属批次单编号
   */
  batchNo: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
  /**
   * 处理信息
   */
  message: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 就绪时间
   */
  alreadyTime: string
  /**
   * 执行时间
   */
  executingTime: string
  /**
   * 完成时间
   */
  completedTime: string
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount>
}

export class EachStateCount {
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  state: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 数量
   */
  count: number
}

export class ImportGradeResultTaskInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ImportGradeResultTaskInfoResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取导入期别学习成果任务excel模板地址
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getImportGradeResultTaskTemplatePath(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getImportGradeResultTaskTemplatePath,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageImportGradeResultTaskInfo(
    params: { page?: Page; request?: ImportGradeResultTaskQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageImportGradeResultTaskInfo,
    operation?: string
  ): Promise<Response<ImportGradeResultTaskInfoResponsePage>> {
    return commonRequestApi<ImportGradeResultTaskInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 将任务的excel的全部数据导出
   * @param mainTaskId
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTaskExcelAllResult(
    mainTaskId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.exportTaskExcelAllResult,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 将任务的excel的全部数据导出
   * @param mainTaskId
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTaskExcelAllResultWithName(
    mainTaskId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.exportTaskExcelAllResultWithName,
    operation?: string
  ): Promise<Response<ImportGradeResultExcelResponse>> {
    return commonRequestApi<ImportGradeResultExcelResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 将任务的失败数据导出excel
   * @param mainTaskId
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTaskExcelFailedResult(
    mainTaskId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.exportTaskExcelFailedResult,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 将任务的失败数据导出excel
   * @param mainTaskId
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTaskExcelFailedResultWithName(
    mainTaskId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.exportTaskExcelFailedResultWithName,
    operation?: string
  ): Promise<Response<ImportGradeResultExcelResponse>> {
    return commonRequestApi<ImportGradeResultExcelResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导入期别学习成果
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async importIssueLearningResult(
    request: IssueLearningResultImportRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.importIssueLearningResult,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
