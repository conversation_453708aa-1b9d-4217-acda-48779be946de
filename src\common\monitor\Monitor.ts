class Config {
  id: string
  sdkVersion: string
  enable = false
  type: 'webfunny'
  host: string
}

export const getEnv = () => {
  const normalEnvList = ['dev', 'sit', 'stag']
  let findIndex = ['.dev', '.test2', '.test1'].findIndex((env: string) => {
    return location.href.indexOf(env) > -1
  })
  let webFunnyEnv = normalEnvList[findIndex] || ''
  if (webFunnyEnv === '') {
    findIndex = ['************', '************', '************'].findIndex((env: string) => {
      return location.href.indexOf(env) > -1
    })
    webFunnyEnv = normalEnvList[findIndex] || ''
    if (webFunnyEnv === '') {
      findIndex = ['192.168.'].findIndex((env: string) => {
        return location.href.indexOf(env) > -1
      })
      webFunnyEnv = normalEnvList[findIndex] || ''
    }
  }
  if (webFunnyEnv === '') {
    webFunnyEnv = 'pro'
  }
  return webFunnyEnv
}

class Monitor {
  config: Config = new Config()

  setConfig(config: string) {
    try {
      const convertConfig = JSON.parse(config) as Config
      sessionStorage.CUSTOMER_MONITOR_HOST = convertConfig.host
      sessionStorage.CUSTOMER_WEB_MONITOR_ID = `${convertConfig.id}_${getEnv()}`
      // sessionStorage.CUSTOMER_WEB_MONITOR_ID = `webfunny_20231108_092921_${this.getEnv()}`
      this.config = convertConfig
    } catch (e) {
      // 忽略
      this.config = new Config()
    }
  }

  setSessionStorageId(id: string) {
    if (!id) {
      sessionStorage.CUSTOMER_WEB_MONITOR_ID = id
    }
  }
}

export default Monitor
