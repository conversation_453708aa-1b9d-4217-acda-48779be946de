import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 学习进度枚举
 */
export enum LearningScheduleStatusEnum {
  // 1：未学习
  Unlearned = 1,
  // 2：学习中
  Learning,
  // 3：已完成
  Complete_Learning,
  // 4：无需学习
  Innate
}

class LearningScheduleStatus extends AbstractEnum<LearningScheduleStatusEnum> {
  static enum = LearningScheduleStatusEnum
  constructor(status?: LearningScheduleStatusEnum) {
    super()
    this.current = status
    this.map.set(LearningScheduleStatusEnum.Unlearned, '未学习')
    this.map.set(LearningScheduleStatusEnum.Learning, '学习中')
    this.map.set(LearningScheduleStatusEnum.Complete_Learning, '已完成')
    this.map.set(LearningScheduleStatusEnum.Innate, '无需学习')
  }
}

export default new LearningScheduleStatus()
