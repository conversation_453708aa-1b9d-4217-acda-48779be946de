<template>
  <el-main>
    <el-card shadow="never" class="m-card f-mb15">
      <!--修改供应商-->
      <el-button @click="dialog2 = true" type="primary" class="f-mr20">修改供应商</el-button>
      <el-drawer
        title="修改供应商"
        :visible.sync="dialog2"
        :direction="direction"
        size="1000px"
        :append-to-body="true"
        custom-class="m-drawer m-table-auto"
      >
        <div class="drawer-bd">
          <el-alert type="info" :closable="false" class="m-alert f-p20">
            <div class="f-flex">
              <el-tag size="small" effect="dark">企业</el-tag>
              <div class="f-ml15">
                <p class="f-f15 f-fb f-c3 f-mb5">供应商A</p>
                <p>统一社会信用代码：913101175821220588</p>
              </div>
            </div>
          </el-alert>
          <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
            <el-form-item label="设置合作周期：" required>
              <el-radio-group v-model="form.resource">
                <el-radio label="长期合作"></el-radio>
                <el-radio>
                  指定周期
                  <!--选中后出现输入框-->
                  <el-date-picker
                    v-model="form.date1"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="起始时间"
                    end-placeholder="结束时间"
                    class="f-ml10 form-l"
                  >
                  </el-date-picker>
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
        <div class="drawer-ft m-btn-bar">
          <el-button>取消</el-button>
          <el-button type="primary">确定</el-button>
        </div>
      </el-drawer>
    </el-card>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        props: { multiple: true },
        activeName: 'six',
        activeName1: 'first',
        activeName2: 'first',
        checked: false,
        input: '',
        radio: 3,
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
