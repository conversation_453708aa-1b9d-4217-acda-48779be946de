import AbstractEnum from '../AbstractEnum'

enum InvoiceMethodTypeEnum {
  ONLINE = 1,
  OFFLINE = 2
}
export { InvoiceMethodTypeEnum }

/**
 * 增值税开票方式
 */
export enum InvoiceMethodWithSpecialEnum {
  /**
   * 线下电子
   */
  OFFLINE_ELECT = 1,

  /**
   * 线下纸质
   */
  OFFLINE_PAPER = 2
}

class InvoiceMethodType extends AbstractEnum<InvoiceMethodTypeEnum> {
  constructor() {
    super()
    this.map[InvoiceMethodTypeEnum.ONLINE] = '自动开票'
    this.map[InvoiceMethodTypeEnum.OFFLINE] = '线下开票'
  }
}

export default new InvoiceMethodType()
