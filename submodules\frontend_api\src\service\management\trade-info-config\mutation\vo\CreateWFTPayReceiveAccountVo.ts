import CreateReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/CreateReceiveAccountVo'
import MutationReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/MutationReceiveAccountVo'
import { CreateReceiveAccountRequest, ReceiveAccountExtProperty } from '@api/ms-gateway/ms-trade-configuration-v1'

export default class CreateJSPayReceiveAccountVo extends CreateReceiveAccountVo {
  /**
   * 支付账号类型id
   * 支付宝:ALIPAY
   * 微信：WXPAY
   * 支付宝H5:ALIPAYH5
   * 微信H5：WXPAYH5
   */
  paymentChannelId = ''
  /**
   * 商户私钥
   */
  mchPrivateKey = ''
  /**
   * 平台公钥
   */
  platPublicKey = ''
  constructor(type: string) {
    super()
    this.paymentChannelId = type
  }

  from(mutationReceiveAccountVo: MutationReceiveAccountVo) {
    this.accountType = mutationReceiveAccountVo.accountType
    this.accountNo = mutationReceiveAccountVo.accountNo
    this.accountName = mutationReceiveAccountVo.accountName
    this.qrScanPrompt = mutationReceiveAccountVo.qrScanPrompt
    this.taxPayerId = mutationReceiveAccountVo.taxPayerId
    this.refundWay = mutationReceiveAccountVo.refundWay
    this.paymentChannelId = mutationReceiveAccountVo.paymentChannelId
    this.mchPrivateKey = mutationReceiveAccountVo.mchPrivateKey
    this.platPublicKey = mutationReceiveAccountVo.platPublicKey
  }

  to(): CreateReceiveAccountRequest {
    const createReceiveAccountRequest = new CreateReceiveAccountRequest()
    createReceiveAccountRequest.accountType = 1
    createReceiveAccountRequest.paymentChannelId = this.paymentChannelId
    createReceiveAccountRequest.accountNo = this.accountNo
    createReceiveAccountRequest.name = this.accountName
    createReceiveAccountRequest.qrScanPrompt = this.qrScanPrompt
    createReceiveAccountRequest.refundWay = this.refundWay
    createReceiveAccountRequest.taxPayerId = this.taxPayerId
    createReceiveAccountRequest.properties = new Array<ReceiveAccountExtProperty>()
    createReceiveAccountRequest.properties.push({ name: 'mchPrivateKey', value: this.mchPrivateKey })
    createReceiveAccountRequest.properties.push({ name: 'platPublicKey', value: this.platPublicKey })
    return createReceiveAccountRequest
  }
}
