.el-aside {
  -webkit-transition: width 0.3s;
  -moz-transition: width 0.3s;
  -ms-transition: width 0.3s;
  -o-transition: width 0.3s;
  transition: width 0.3s;
}

.side-collapsed {
  .logo {
    .logo-txt {
      display: none;
    }
  }

  .el-menu {
    .el-submenu__title {
      span {
        display: none;
      }
    }

    .el-menu-item {
      display: none;
    }
  }

  .download-panel-tip,
  .download-panel-content {
    display: none;
  }
}

.aside-btn {
  transition: all .3s;
}

.fade-right-leave-active,
.fade-right-enter-active {
  transition: all 0.2s;
}

.fade-right-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-right-leave-to {
  opacity: 0;
  transform: translateX(30px);
}


.fade-left-leave-active,
.fade-left-enter-active {
  transition: all 0.2s;
}

.fade-left-enter {
  opacity: 0;
  transform: translateX(30px);
}

.fade-left-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.el-card {
  overflow: unset;
}

.tools {
  display: flex;
  align-content: center;
  align-items: center;
  color: #fff;

  .tool-operate {
    cursor: pointer;
    padding: 10px;

    &:hover {
      background: #999999;
    }

    &.red {
      background: #e72323;
    }
  }
}
