import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum UnitTypeEnum {
  /**
   * 空值
   */
  null = 0,
  /**
   * 次
   */
  turn = 1,

  /**
   * 科
   */
  subject,

  /**
   * 学时
   */
  hour,

  /**
   * 学分
   */
  credit,

  /**
   * 自定义
   */
  custom
}

export default class UnitType extends AbstractEnum<UnitTypeEnum> {
  static enum = UnitTypeEnum

  constructor(status?: UnitTypeEnum) {
    super()
    this.current = status
    this.map.set(UnitTypeEnum.null, '空值')
    this.map.set(UnitTypeEnum.turn, '次')
    this.map.set(UnitTypeEnum.subject, '科')
    this.map.set(UnitTypeEnum.hour, '学时')
    this.map.set(UnitTypeEnum.credit, '学分')
    this.map.set(UnitTypeEnum.custom, '自定义')
  }
}
