<template>
  <div class="range-wrapper">
    <slot></slot>
  </div>
</template>

<style lang="scss">
  .range-wrapper {
    display: flex;
    outline: none;
    border-radius: 4px;
    border: 1px solid #ddd;

    &:focus {
      border-color: #2d8cf0 !important;
    }

    .el-date-editor {
      &.el-input {
        width: 50%;
        text-align: center;
        font-size: 12px;
      }
    }

    :first-child {
      input {
        border: 0;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }

    div:last-child {
      input {
        border: 0;
        border-left: none;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }

      &::before {
        content: '~';
        display: inline-block;
        float: left;
        position: absolute;
        color: #999;
      }

      .el-input__prefix {
        display: none;
      }
    }

    input {
      text-align: center;
    }
  }
</style>
