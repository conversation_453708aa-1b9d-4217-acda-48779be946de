<template>
  <div>
    <!--添加课程-->
    <el-drawer
      :title="title"
      :visible.sync="showCoursePackage"
      size="1200px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert" v-if="showCategory">
          提示：课程只能添加在末级分类下，一个分类只能添加一个课程包。
        </el-alert>
        <el-form ref="form" class="m-form f-mt20" v-if="showCategory">
          <el-form-item label="分类名称：">
            <el-cascader
              clearable
              lazy
              :options="options"
              :props="defaultProps"
              v-model="selectedOutlinePath"
              :key="cascaderKey"
              placeholder="请选择分类"
              class="form-m"
            />
          </el-form-item>
        </el-form>
        <div class="m-tit">
          <span class="tit-txt">选择课程包</span>
        </div>
        <el-row :gutter="16" class="m-query f-mt10">
          <el-form :inline="true">
            <el-col :span="10">
              <el-form-item label="课程包名称：">
                <el-input v-model="coursePackageQueryParam.name" clearable placeholder="请输入课程包名称" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-button type="primary" @click="searchCoursePackage">查询</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table
          stripe
          :data="coursePackageList"
          v-loading="coursePackageQuery.loading"
          class="m-table f-mt10 course-package-list"
          ref="tableList"
        >
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="课程包名称" min-width="300">
            <template slot-scope="scope">
              <span>
                <el-popover
                  placement="top-start"
                  width="200"
                  trigger="hover"
                  :content="scope.row.name"
                  :ref="`popover2-${scope.$index}`"
                >
                  <span slot="reference" v-if="scope.row.isBeingUsedAsCompulsory">
                    <el-tag type="danger" size="mini" class="f-mr5">必修</el-tag> {{ scope.row.name }}
                  </span>
                  <span slot="reference" v-else class="f-mr5">{{ scope.row.name }}</span>
                </el-popover>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="课程数量" min-width="120" align="center">
            <template slot-scope="scope">{{ scope.row.courseCount }}</template>
          </el-table-column>
          <el-table-column label="总学时" min-width="120" align="center">
            <template slot-scope="scope">{{ scope.row.totalPeriod }}</template>
          </el-table-column>
          <el-table-column label="查看" width="80" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="goCoursePackageDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <el-checkbox
                v-model="scope.row.isChecked"
                label="选择"
                @change="
                  (value) => {
                    return handleCoursePackageCheckChange(value, scope.row)
                  }
                "
              >
                选择
              </el-checkbox>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="coursePackagePage" v-bind="coursePackagePage"></hb-pagination>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button @click="cancelChooseCoursePackage">取消</el-button>
        <el-button type="primary" @click="confirmChooseCoursePackage">确定</el-button>
      </div>
    </el-drawer>

    <!--查看课程包内课程详情-->
    <el-drawer
      title="查看课程包内课程详情"
      :visible.sync="showCourse"
      size="800px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd">
        <!--表格-->
        <el-table stripe :data="courseList" max-height="500px" class="m-table course-detail">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="课程名称" min-width="300">
            <template slot-scope="scope">
              <el-popover
                placement="top-start"
                width="200"
                trigger="hover"
                :content="scope.row.name"
                :ref="`popover-${scope.$index}`"
              >
                <span slot="reference">{{ scope.row.name }}</span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="学时" min-width="100" align="center">
            <template slot-scope="scope">{{ scope.row.period }}</template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="coursePage" v-bind="coursePage"> </hb-pagination>
      </div>
    </el-drawer>
    <el-dialog
      title="提示"
      :visible.sync="uiConfig.dialog.courseRepeatDialogVisible"
      width="450px"
      class="m-dialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div>同一个方案内的必修课程不允许重复，添加失败。</div>
      <!--<div>这是一段信息，这是<span class="f-cr">重点信息</span>，这是<span class="f-fb">加粗信息</span></div>-->
      <div class="content">
        <div v-for="(item, index) in courseRepeatList" :key="index">
          {{ item.courseName }}已存在{{ getCourseCategory(item.courseCategoryInfo) }}分类
        </div>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="uiConfig.dialog.courseRepeatDialogVisible = false"> 确 定 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import { UiPage, Query } from '@hbfe/common'
  import QueryCoursePackageListVo from '@api/service/management/resource/course-package/query/vo/QueryCoursePackageListVo'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import CoursePackageDetailVo from '@api/service/management/resource/course-package/query/vo/CoursePackageDetailVo'
  import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import CreateSchemeUIModule from '@/store/modules-ui/scheme/CreateSchemeUIModule'
  import { bind, debounce } from 'lodash-decorators'
  import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'
  import RepeatCourseListInCoursePackage from '@api/service/management/resource/course/query/vo/RepeatCourseListInCoursePackage'
  import { ElTable } from 'element-ui/types/table'
  import Mockjs from 'mockjs'

  class CoursePackageDetail extends CoursePackageDetailVo {
    // 是否被选中
    isChecked: boolean
  }

  @Component
  export default class extends Vue {
    /**
     * 是否展示
     */
    @Prop({
      required: true,
      type: Boolean,
      default: false
    })
    value: boolean

    @Watch('value', {
      immediate: true,
      deep: true
    })
    valueChange(val: boolean) {
      this.showCoursePackage = cloneDeep(val)
    }

    @Emit('input')
    @Watch('showCoursePackage', {
      immediate: true,
      deep: true
    })
    showCoursePackageChange(val: number) {
      return val
    }

    /**
     * 标题
     */
    @Prop({
      type: String,
      default: '添加课程'
    })
    title: string

    @Prop({
      type: Array,
      default: () => new Array<Classification>()
    })
    options: Array<Classification>

    /**
     * 级联选择器值
     */
    @Prop({
      type: Array,
      default: () => new Array<string>()
    })
    path: Array<string>

    /**
     * 是否展示课程分类选项（是否有分类）
     */
    @Prop({
      type: Boolean,
      default: true
    })
    showCategory: boolean

    @Watch('path', {
      immediate: true,
      deep: true
    })
    pathChange(val: Array<string>) {
      this.selectedOutlinePath = cloneDeep(val)
    }

    /**
     * mock uuid
     */
    get cascaderKey() {
      // 依赖options，当options变化时，重新生成key
      this.options
      return Mockjs.Random.guid()
    }

    // 是否展示课程包
    showCoursePackage = false

    // 级联选择器默认配置
    defaultProps = {
      label: 'name',
      children: 'childOutlines',
      value: 'id',
      checkStrictly: true // 取消关联，可选任意级
    }

    // 已选大纲树路径
    selectedOutlinePath: Array<string> = new Array<string>()
    // 是否展示课程列表
    showCourse = false

    // 查询参数 - 课程包
    coursePackageQueryParam: QueryCoursePackageListVo = new QueryCoursePackageListVo()
    // 分页参数 - 课程包
    coursePackagePage: UiPage
    // 分页加载参数 - 课程包
    coursePackageQuery: Query = new Query()
    // 课程包列表
    coursePackageList: Array<CoursePackageDetail> = new Array<CoursePackageDetail>()

    // 选择的课程包id
    selectedCoursePackageId = ''
    // 查看的课程包id
    viewingCoursePackageId = ''

    // 分页参数 - 课程
    coursePage: UiPage
    // 分页加载参数 - 课程包
    courseQuery: Query = new Query()
    // 课程列表
    courseList: Array<CourseInCoursePackage> = new Array<CourseInCoursePackage>()
    // 重复课程列表
    courseRepeatList: Array<RepeatCourseListInCoursePackage> = new Array<RepeatCourseListInCoursePackage>()
    // 防止重复课程
    isChoose = false
    // 查询课程总控
    queryCourseM = new QueryCourse()
    /**
     *  ui相关的变量控制
     */
    uiConfig = {
      // 对话框是否展示
      dialog: {
        courseRepeatDialogVisible: false
      }
    }
    /**
     * 获取所有的课程大纲叶子节点
     */
    get outlineTreeLeaves() {
      return this.outlineTreeFindAllLeaves(this.options)
    }

    constructor() {
      super()
      this.coursePackagePage = new UiPage(this.pageCoursePackage, this.pageCoursePackage)
      this.coursePage = new UiPage(this.pageCourse, this.pageCourse)
    }

    /**
     * 查询符合条件的节点 - 课程大纲树通用
     */
    outlineTreeFind(tree: Array<Classification>, func: any) {
      return CreateSchemeUtils.treeFind<Classification>(tree, func, 'childOutlines')
    }

    /**
     * 查找节点路径 - 课程大纲树通用
     */
    outlineTreeFindPath(func: any, key: string) {
      return CreateSchemeUtils.treeFindPath<Classification>(this.options, func, key, 'childOutlines')
    }

    /**
     * 查找所有叶子节点 - 课程大纲树通用
     */
    outlineTreeFindAllLeaves(tree: Array<Classification>) {
      return CreateSchemeUtils.treeFindAllLeaves<Classification>(tree, 'childOutlines')
    }

    /**
     * 清空已选
     */
    async reset() {
      this.resetCoursePackageQueryParam()
      this.selectedCoursePackageId = ''
      await this.searchCoursePackage()
    }

    /**
     * 查询课程包分页 - UI
     */
    async searchCoursePackage() {
      this.coursePackagePage.pageNo = 1
      await this.pageCoursePackage()
    }

    /**
     * 课程包分页查询
     */
    async pageCoursePackage() {
      this.coursePackageQuery.loading = true
      try {
        this.coursePackageQueryParam.coursePackageId = new Array<string>()
        this.coursePackageQueryParam.excludeCoursePackageId = this.getSelectedCoursePackageIds()
        // 取值
        const list: Array<CoursePackageDetailVo> =
          await ResourceModule.coursePackageFactory.queryCoursePackage.pageCoursePackage(
            this.coursePackagePage,
            this.coursePackageQueryParam
          )
        // 赋值
        this.coursePackageList = new Array<CoursePackageDetail>()
        list?.forEach((el: CoursePackageDetailVo) => {
          const option = new CoursePackageDetail()
          Object.assign(option, el)
          option.isChecked = false
          this.coursePackageList.push(option)
        })
        this.$nextTick(() => {
          const tableListRef = this.$refs['tableList'] as ElTable
          tableListRef?.doLayout()
        })
      } catch (e) {
        console.log('获取课程包分页列表失败！', e)
      } finally {
        this.coursePackageQuery.loading = false
      }
    }

    /**
     * 课程分页查询
     */
    async searchCourse() {
      this.coursePage.pageNo = 1
      await this.pageCourse()
    }

    /**
     * 课程分页查询
     */
    async pageCourse() {
      this.courseQuery.loading = true
      try {
        this.courseList = await ResourceModule.coursePackageFactory.queryCoursePackage.queryCoursePackageCoursePage(
          this.coursePage,
          this.viewingCoursePackageId
        )
      } catch (e) {
        console.log('获取课程分页列表失败！', e)
      } finally {
        this.courseQuery.loading = false
      }
    }

    /**
     * 查看课程包详情
     */
    async goCoursePackageDetail(row: CoursePackageDetail) {
      this.viewingCoursePackageId = row.id
      await this.searchCourse()
      this.showCourse = true
    }

    /**
     * 取消选择课程包
     */
    cancelChooseCoursePackage() {
      this.showCoursePackage = false
    }

    /**
     * 重置查询条件
     */
    resetCoursePackageQueryParam() {
      this.coursePackageQueryParam = new QueryCoursePackageListVo()
    }

    /**
     * 确认选择课程包
     */
    @bind
    @debounce(200)
    async confirmChooseCoursePackage() {
      if (this.isChoose) return
      this.isChoose = true
      try {
        if (this.showCategory && !CreateSchemeUtils.isWeightyArray(this.selectedOutlinePath)) {
          this.$message.error('请选择要添加课程的分类')
          this.isChoose = false
          return
        }
        if (!this.selectedCoursePackageId) {
          this.$message.error('请先选择一个课程包，才能添加课程')
          this.isChoose = false
          return
        }
        // 选中节点路径
        const selectedOutlinePath = cloneDeep(this.selectedOutlinePath)
        const pathLength = selectedOutlinePath.length
        // 选中节点id
        const selectedOutlineId = selectedOutlinePath[pathLength - 1]
        // 选中节点信息
        const selectedOutline = this.outlineTreeFind(this.options, (node: Classification) => {
          return node.id === selectedOutlineId
        })
        // 是否有分类要分别判断
        if (this.showCategory) {
          // 非末级分类下不能添加课程
          if (selectedOutline.childOutlines?.length) {
            this.$message.error('请先选择末级分类，才能添加课程')
            this.isChoose = false
            return
          }
          if (selectedOutline.coursePackageId) {
            this.$message.error('当前分类下已添加课程，请先移除后再添加。')
            this.isChoose = false
            return
          }
          if (selectedOutline.category === 1) {
            // 获取必修课已选课程包id集合
            const leaves = this.outlineTreeLeaves.filter((el) => el.category === 1 && el.coursePackageId)
            const sourceCoursePackageIdList = [...new Set(leaves.map((item) => item.coursePackageId))]
            this.courseRepeatList = new Array<RepeatCourseListInCoursePackage>()
            const courseRepeatList = await this.queryCourseM.queryRepeatCourseListInCoursePackage(
              sourceCoursePackageIdList,
              this.selectedCoursePackageId
            )
            this.courseRepeatList = courseRepeatList.map((item: RepeatCourseListInCoursePackage) => {
              item.courseCategoryInfo = this.outlineTreeFindPath((node: Classification) => {
                return node.coursePackageId === item.coursePackageId
              }, 'name')
              return item
            })
            if (this.courseRepeatList.length) {
              this.uiConfig.dialog.courseRepeatDialogVisible = true
              throw new Error('中断函数执行')
            }
          }
          this.$emit('updateTreeNode', selectedOutlineId, this.selectedCoursePackageId)
          this.showCoursePackage = false
        } else {
          this.$emit('updateTreeNode', '', this.selectedCoursePackageId)
          this.showCoursePackage = false
        }
      } catch (e) {
        console.log(e)
      }
      this.isChoose = false
    }

    /**
     * 课程包选中状态切换响应事件
     */
    handleCoursePackageCheckChange(value: boolean, row: CoursePackageDetail) {
      this.coursePackageList?.forEach((el) => {
        el.isChecked = false
      })
      if (value) {
        row.isChecked = true
        this.selectedCoursePackageId = row.id
      } else {
        this.selectedCoursePackageId = ''
      }
    }

    /**
     * 获取课程分类信息
     */
    getCourseCategory(arr: Array<string>) {
      return arr.join('>') || ''
    }

    /**
     * 获取已选的课程包id集合
     */
    getSelectedCoursePackageIds() {
      const result = [] as string[]
      this.outlineTreeLeaves?.forEach((el: Classification) => {
        if (el.coursePackageId) {
          result.push(el.coursePackageId)
        }
      })
      return result
    }
  }
</script>

<style lang="scss" scoped>
  .content {
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.5;
    text-align: left;
    max-height: 450px;
    margin: 0;
    padding: 0;
  }
  ::v-deep .m-table {
    width: 100%;
    color: #444;
    height: auto;
  }
  ::v-deep .course-detail .el-popover__reference-wrapper span {
    /*禁止换行*/
    white-space: nowrap;
    /*显示省略号*/
    text-overflow: ellipsis;
  }
  ::v-deep .course-package-list .el-popover__reference-wrapper > span {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
</style>
