import store from '../../../../store'
import { Mo<PERSON><PERSON> as Mod } from 'vuex'
import { Module, VuexModule, getModule, Action, Mutation } from 'vuex-module-decorators'
import QuestionPracticePaper from './models/QuestionPracticePaper'
import QuestionPracticeAnswer from './models/QuestionPracticeAnswer'
import PlatformExamGateway, {
  AnswerQuestionStatisticDTO,
  AnswerQuestionStatisticParamDTO,
  PracticeType,
  QuestionStatisticDTO,
  QuestionStatisticParamDTO
} from '@api/gateway/PlatformExam'
import Response, { ResponseStatus } from '../../../../Response'
// import PreExamLSGateway from '@api/gateway/NormalIssueClassLS-default'
import ChapterTree from '../../../common/syllabus/model/ChapterTree'
import { Role, RoleType } from '../../../../Secure'
import SyllabusModule from '@api/service/customer/syllabus/SyllabusModule'
import Chapter from '@api/service/common/models/syllabus/Chapter'

interface MapX<K extends string, V> {
  key: K
  value: V
}

export enum QuestionType {
  REAL = 'real',
  SIMULATION = 'simulation',
  PRACTICE = 'practice'
}

class QuestionPracticeSyllabusTree {
  /**
   * 章节编号，对应标签编号
   */
  id: string
  /**
   * 章节名称，对应标签名称
   */
  name: string
  /**
   * 当前节点关系编号
   * @description 由于同一个标签可能存在在同一个关系中的不同节点位置,
   * 只有节点关系编号才能定位一个章节的位置
   *
   */
  relationId: string
  /**
   * 同一级序号
   */
  sequence: number
  /**
   * 子章节
   */
  children: Array<QuestionPracticeSyllabusTree> = new Array<QuestionPracticeSyllabusTree>()

  /**
   * 是否正在作答
   */
  answering: boolean

  /**
   * 试题数
   */
  questionCount: number

  /**
   * 已答试题数
   */
  questionAnsweredCount: number

  /**
   * 已答试题次数
   */
  questionAnsweredTimes: number

  /**
   * 试题答对次数
   */
  questionCorrectTimes: number

  /**
   * 对题率
   */
  questionCorrectRate: number
}

class StateCache {
  constructor(schemeId: string, learningId: string, majorId: string) {
    this.schemeId = schemeId
    this.learningId = learningId
    this.majorId = majorId
  }

  // 方案id
  schemeId: string
  // 方式id
  learningId: string
  // 专业id
  majorId: string
  // 试题练习卷
  questionPractice: QuestionPracticePaper = new QuestionPracticePaper()
  // 答卷列表
  latestQuestionPracticeAnswer: Array<QuestionPracticeAnswer> = new Array<QuestionPracticeAnswer>()
  // 各试题类型作答统计
  allQuestionCategoryAnswerStatistic: Array<MapX<string, Array<AnswerQuestionStatisticDTO>>> = new Array<
    MapX<string, Array<AnswerQuestionStatisticDTO>>
  >()
  // 各试题类型试题统计
  allQuestionCategoryQuestionStatistic: Array<MapX<string, Array<QuestionStatisticDTO>>> = new Array<
    MapX<string, Array<QuestionStatisticDTO>>
  >()
  // 是否需要重载，在学员触发去作答练习是置为true，在下次取数时先清空数据然后加载新数据
  needReload = false
  // 最新一次加载时间，从apollo配置的超时时间与当前时间判断，在下次取数时清空当前取数数据然后加载新数据
  latestLoadTime: Date = new Date()
}

export interface IState {
  /**
   * 各学习方式试题练习数据
   */
  learningQuestionPracticeListMap: Array<StateCache>
  // 当前作答id
  currentAnswerId: string
}

@Module({
  namespaced: true,
  store,
  name: 'CustomerQuestionPracticeModule',
  dynamic: true
})
class QuestionPracticeModule extends VuexModule implements IState {
  learningQuestionPracticeListMap = new Array<StateCache>()
  // 当前作答id
  currentAnswerId = ''

  constructor(module: Mod<ThisType<any>, any>) {
    super(module)
    store.subscribe(mutation => {
      if (mutation.type?.indexOf('ALERT_EXAM_RELOAD') !== -1) {
        this.processSubmitPaperMessage(mutation.payload)
      }
    })
    console.log('试题练习初始化完成')
  }

  /**
   * 初始化当前培训班试题练习数据
   * @param ctx
   * @param schemeId
   * @param learningId
   */
  @Role([RoleType.user])
  @Action
  async init(payload: { schemeId: string; learningId: string; majorId: string }) {
    if (
      !this.learningQuestionPracticeListMap.find(p => p.learningId === payload.learningId) ||
      this.learningQuestionPracticeListMap.find(p => p.learningId === payload.learningId)?.needReload
    ) {
      if (!this.learningQuestionPracticeListMap.find(p => p.learningId === payload.learningId)) {
        const stateCache = new StateCache(payload.schemeId, payload.learningId, payload.majorId)
        this.setStateCacheToLearningQuestionPracticeListMap(stateCache)
      }
      let stateCache = new StateCache(payload.schemeId, payload.learningId, payload.majorId)
      this.setStateCacheToLearningQuestionPracticeListMap(stateCache)
      stateCache = new StateCache(payload.schemeId, payload.learningId, payload.majorId)
      // 获取试题练习卷
      let response: Response<any>
      // let response: Response<any> = await PreExamGateway.getQuestionPracticePaper(payload.learningId)
      // if (!response.status.isSuccess()) {
      //   return response.status
      // }
      // stateCache.questionPractice = response.data
      // 获取各章节或随机抽题最新一份答卷
      // response = await PreExamGateway.getPreExamQuestionPracticeList(payload.learningId)
      // if (!response.status.isSuccess()) {
      //   return response.status
      // }
      // stateCache.latestQuestionPracticeAnswer = response.data
      // 初始化考纲信息
      const status = await SyllabusModule.init()
      if (!status.isSuccess()) {
        return status
      }
      const leafSyllabus: Array<Chapter> = SyllabusModule.getLeafSyllabusByMajorId(payload.majorId)
      console.log('》》》》》初始化试题练习信息：' + JSON.stringify(payload))
      // 获取真题试题类型作答统计
      const realAnswerStatisticParam: AnswerQuestionStatisticParamDTO = new AnswerQuestionStatisticParamDTO()
      realAnswerStatisticParam.learningId = payload.learningId
      realAnswerStatisticParam.schemeId = payload.schemeId
      realAnswerStatisticParam.practiceType = PracticeType.REAL
      console.log(PracticeType.REAL)
      realAnswerStatisticParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      response = await PlatformExamGateway.statisticUserAnswerQuestionByTag(realAnswerStatisticParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionCategoryAnswerStatistic.push({ key: QuestionType.REAL, value: response.data })
      // 获取模拟试题类型作答统计
      const simulationAnswerStatisticParam: AnswerQuestionStatisticParamDTO = new AnswerQuestionStatisticParamDTO()
      simulationAnswerStatisticParam.learningId = payload.learningId
      simulationAnswerStatisticParam.schemeId = payload.schemeId
      simulationAnswerStatisticParam.practiceType = PracticeType.SIMULATION
      simulationAnswerStatisticParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      response = await PlatformExamGateway.statisticUserAnswerQuestionByTag(simulationAnswerStatisticParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionCategoryAnswerStatistic.push({ key: QuestionType.SIMULATION, value: response.data })
      // 获取练习试题类型作答统计
      const practiceAnswerStatisticParam: AnswerQuestionStatisticParamDTO = new AnswerQuestionStatisticParamDTO()
      practiceAnswerStatisticParam.learningId = payload.learningId
      practiceAnswerStatisticParam.schemeId = payload.schemeId
      practiceAnswerStatisticParam.practiceType = PracticeType.PRACTICE
      practiceAnswerStatisticParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      response = await PlatformExamGateway.statisticUserAnswerQuestionByTag(practiceAnswerStatisticParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionCategoryAnswerStatistic.push({ key: QuestionType.PRACTICE, value: response.data })

      // 获取真题试题类型试题统计
      const realQuestionStatisticParam: QuestionStatisticParamDTO = new QuestionStatisticParamDTO()
      realQuestionStatisticParam.enable = 0
      realQuestionStatisticParam.questionType = PracticeType.REAL
      realQuestionStatisticParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      response = await PlatformExamGateway.statisticQuestionGroupByTagIds(realQuestionStatisticParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionCategoryQuestionStatistic.push({ key: QuestionType.REAL, value: response.data })
      // 获取模拟试题类型试题统计
      const simulationQuestionStatisticParam: QuestionStatisticParamDTO = new QuestionStatisticParamDTO()
      simulationQuestionStatisticParam.enable = 0
      simulationQuestionStatisticParam.questionType = PracticeType.SIMULATION
      simulationQuestionStatisticParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      response = await PlatformExamGateway.statisticQuestionGroupByTagIds(simulationQuestionStatisticParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionCategoryQuestionStatistic.push({ key: QuestionType.SIMULATION, value: response.data })
      // 获取练习试题类型试题统计
      const practiceQuestionStatisticParam: QuestionStatisticParamDTO = new QuestionStatisticParamDTO()
      practiceQuestionStatisticParam.enable = 0
      practiceQuestionStatisticParam.questionType = PracticeType.PRACTICE
      practiceQuestionStatisticParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      response = await PlatformExamGateway.statisticQuestionGroupByTagIds(practiceQuestionStatisticParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionCategoryQuestionStatistic.push({ key: QuestionType.PRACTICE, value: response.data })

      this.setStateCacheToLearningQuestionPracticeListMap(stateCache)
    } else if (
      (this.learningQuestionPracticeListMap.find(p => p.learningId === payload.learningId)?.latestLoadTime?.getTime() ||
        0) <
      new Date().getTime() - 10000
    ) {
      this.setNeedReload({
        schemeId: payload.schemeId,
        learningId: payload.learningId,
        majorId: payload.majorId
      })
      await this.init({
        schemeId: payload.schemeId,
        learningId: payload.learningId,
        majorId: payload.majorId
      })
    }
    return new ResponseStatus(200)
  }

  /**
   * 去做指定章节的练习
   * @param payload
   */
  @Role([RoleType.user])
  @Action
  async goExamOutlinePractice(payload: {
    userId: string
    schemeId: string
    issueId: string
    learningId: string
    questionCategory: string
    chapterId: string
  }) {
    if (!payload || !payload.learningId) {
      return new ResponseStatus(500, '请传入学习方式id')
    }
    const stateCache = this.learningQuestionPracticeListMap.find(p => p.learningId === payload.learningId)
    if (!stateCache) {
      return new ResponseStatus(500, '当前期别对应练习信息未初始化，请先init')
    }

    // const examOutlineParam: ApplyExamOutlinePracticeLearningTokenRequest = new ApplyExamOutlinePracticeLearningTokenRequest()
    // // examOutlineParam.userId = payload.userId
    // examOutlineParam.schemeId = payload.schemeId
    // examOutlineParam.issueId = payload.issueId
    // examOutlineParam.chaptersId = payload.chapterId
    // examOutlineParam.questionCategory = payload.questionCategory
    // const tokenResponse = await PreExamLSGateway.applyExamOutlinePracticeLearningToken(examOutlineParam)
    // if (!tokenResponse.status.isSuccess()) {
    //   return tokenResponse.status
    // }
    // const goPracticeResponse = await PreExamGateway.goPracticeByToken(tokenResponse.data)
    //
    // this.setAnswerId(goPracticeResponse.data)
    this.setNeedReload({
      schemeId: stateCache.schemeId,
      learningId: stateCache.learningId,
      majorId: stateCache.majorId
    })
    // return goPracticeResponse.status
  }

  /**
   * 去做指定章节的练习,可以指定数量
   * @param payload
   */
  @Role([RoleType.user])
  @Action
  async goExamOutlinePracticeWithSpecifyCount(payload: {
    userId: string
    schemeId: string
    issueId: string
    learningId: string
    questionCategory: string
    chapterId: string
    questionCount: number
  }) {
    if (!payload || !payload.learningId) {
      return new ResponseStatus(500, '请传入学习方式id')
    }
    const stateCache = this.learningQuestionPracticeListMap.find(p => p.learningId === payload.learningId)
    if (!stateCache) {
      return new ResponseStatus(500, '当前期别对应练习信息未初始化，请先init')
    }

    // const examOutlineParam: ApplyExamOutlinePracticeLearningTokenRequest = new ApplyExamOutlinePracticeLearningTokenRequest()
    // // examOutlineParam.userId = payload.userId
    // examOutlineParam.schemeId = payload.schemeId
    // examOutlineParam.issueId = payload.issueId
    // examOutlineParam.chaptersId = payload.chapterId
    // examOutlineParam.questionCategory = payload.questionCategory
    // const tokenResponse = await PreExamLSGateway.applyExamOutlinePracticeLearningToken(examOutlineParam)
    // if (!tokenResponse.status.isSuccess()) {
    //   return tokenResponse.status
    // }
    // const goPracticeResponse = await PreExamGateway.goPracticeByTokenAndSpecifyQuestionCount({
    //   token: tokenResponse.data,
    //   questionCount: payload.questionCount
    // })
    //
    // this.setAnswerId(goPracticeResponse.data)
    this.setNeedReload({
      schemeId: stateCache.schemeId,
      learningId: stateCache.learningId,
      majorId: stateCache.majorId
    })
    // return goPracticeResponse.status
  }

  /**
   * 去做随机抽题的练习
   * @param payload
   */
  @Role([RoleType.user])
  @Action
  async goRandomPractice(payload: {
    userId: string
    schemeId: string
    issueId: string
    learningId: string
    extractMode: number
    questionCount: number
    questionType: number
  }) {
    if (!payload || !payload.learningId) {
      return new ResponseStatus(500, '请传入学习方式id')
    }
    const stateCache = this.learningQuestionPracticeListMap.find(p => p.learningId === payload.learningId)
    if (!stateCache) {
      return new ResponseStatus(500, '当前期别对应练习信息未初始化，请先init')
    }
    console.log(payload)
    // const examOutlineParam: ApplyRandomPracticeLearningTokenRequest = new ApplyRandomPracticeLearningTokenRequest()
    // // examOutlineParam.userId = payload.userId
    // examOutlineParam.schemeId = payload.schemeId
    // examOutlineParam.issueId = payload.issueId
    // examOutlineParam.extractMode = payload.extractMode
    // examOutlineParam.questionCount = payload.questionCount
    // examOutlineParam.questionType = payload.questionType
    // const tokenResponse = await PreExamLSGateway.applyRandomPracticeLearningToken(examOutlineParam)
    //
    // if (!tokenResponse.status.isSuccess()) {
    //   return tokenResponse.status
    // }
    //
    // const goPracticeResponse = await PreExamGateway.goPracticeByToken(tokenResponse.data)
    //
    // this.setAnswerId(goPracticeResponse.data)
    this.setNeedReload({
      schemeId: stateCache.schemeId,
      learningId: stateCache.learningId,
      majorId: stateCache.majorId
    })
    // return goPracticeResponse.status
  }

  /**
   * 处理交卷消息
   * @param payload
   */
  async processSubmitPaperMessage(payload: { schemeId: string; issueId: string; answersId: string }) {
    this.learningQuestionPracticeListMap.forEach((stateCache: StateCache) => {
      if (
        stateCache?.schemeId === payload.schemeId &&
        (!payload.answersId || this.currentAnswerId === payload.answersId)
      ) {
        this.setNeedReload({
          schemeId: stateCache.schemeId,
          learningId: stateCache.learningId,
          majorId: stateCache.majorId
        })
      }
    })
  }

  @Mutation
  setStateCacheToLearningQuestionPracticeListMap(payload: StateCache) {
    this.learningQuestionPracticeListMap = this.learningQuestionPracticeListMap.filter(
      p => p.learningId !== payload.learningId
    )
    this.learningQuestionPracticeListMap.push(payload)
  }

  @Mutation
  setNeedReload(payload: { schemeId: string; learningId: string; majorId: string }) {
    console.log('学习方式[' + payload.learningId + ']needReload')
    const stateCache = this.learningQuestionPracticeListMap.find(p => p.learningId === payload.learningId)
    if (stateCache) {
      stateCache.needReload = true
    }
  }

  @Mutation
  setAnswerId(answerId: string) {
    this.currentAnswerId = answerId
  }

  /**
   * 获取当前练习卷
   */
  get currentQuestionPractice() {
    return (schemeId: string, learningId: string) => {
      return this.learningQuestionPracticeListMap.find(p => p.learningId === learningId)?.questionPractice
    }
  }

  /**
   * 获取最新一份答卷
   */
  get getLatestQuestionPracticeAnswer() {
    return (
      schemeId: string,
      learningId: string,
      questionCategory: string,
      fetchWay: number,
      examinationOutlineId: string
    ) => {
      return this.learningQuestionPracticeListMap
        .find(p => p.learningId === learningId)
        ?.latestQuestionPracticeAnswer.find(p => {
          if (fetchWay === 2) {
            return p.fetchWay === fetchWay
          } else {
            return (
              p.fetchWay === fetchWay &&
              p.questionCategory === questionCategory &&
              p.examinationOutlineId === examinationOutlineId
            )
          }
        })
    }
  }

  /**
   * 是否正在作答
   */
  get isAnswering() {
    return (
      schemeId: string,
      learningId: string,
      questionCategory: string,
      fetchWay: number,
      examinationOutlineId: string
    ) => {
      const practiceAnswer = this.learningQuestionPracticeListMap
        .find(p => p.learningId === learningId)
        ?.latestQuestionPracticeAnswer?.find((p: QuestionPracticeAnswer) => {
          if (fetchWay === 2) {
            return p.fetchWay === fetchWay
          } else {
            return (
              p.fetchWay === fetchWay &&
              p.questionCategory === questionCategory &&
              p.examinationOutlineId === examinationOutlineId
            )
          }
        })
      return (practiceAnswer && !practiceAnswer.complete) || false
    }
  }

  /**
   * 获取当前试题类型总题数
   */
  get currentCategoryTotalQuestionCount() {
    return (schemeId: string, learningId: string, questionCategory: string) => {
      return this.learningQuestionPracticeListMap
        .find(p => p.learningId === learningId)
        ?.allQuestionCategoryQuestionStatistic.find(p => p.key === questionCategory)
        ?.value?.map((p: QuestionStatisticDTO) => p.count)
        .reduce((a: number, b: number) => a + b, 0)
    }
  }

  /**
   * 获取当前试题类型已答总次数
   */
  get currentCategoryTotalAnsweredQuestionTimes() {
    return (schemeId: string, learningId: string, questionCategory: string) => {
      return (
        this.learningQuestionPracticeListMap
          .find(p => p.learningId === learningId)
          ?.allQuestionCategoryAnswerStatistic.find(p => p.key === questionCategory)
          ?.value?.map((p: AnswerQuestionStatisticDTO) => p.answeredTimes)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }

  /**
   * 获取当前试题类型答对总次数
   */
  get currentCategoryTotalAnsweredQuestionCorrectTimes() {
    return (schemeId: string, learningId: string, questionCategory: string) => {
      return (
        this.learningQuestionPracticeListMap
          .find(p => p.learningId === learningId)
          ?.allQuestionCategoryAnswerStatistic.find(p => p.key === questionCategory)
          ?.value?.map((p: AnswerQuestionStatisticDTO) => p.correctTimes)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }

  /**
   * 获取总题数
   */
  get totalQuestionCount() {
    return (schemeId: string, learningId: string) => {
      return (
        this.learningQuestionPracticeListMap
          .find(p => p.learningId === learningId)
          ?.allQuestionCategoryQuestionStatistic?.map(p => p.value)
          .flatMap(value => value)
          ?.map((p: QuestionStatisticDTO) => p.count)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }

  /**
   * 获取已答总次数
   */
  get totalAnsweredQuestionTimes() {
    return (schemeId: string, learningId: string) => {
      return (
        this.learningQuestionPracticeListMap
          .find(p => p.learningId === learningId)
          ?.allQuestionCategoryAnswerStatistic?.map(p => p.value)
          .flatMap(value => value)
          ?.map((p: AnswerQuestionStatisticDTO) => p.answeredTimes)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }

  /**
   * 获取答对总次数
   */
  get totalAnsweredQuestionCorrectTimes() {
    return (schemeId: string, learningId: string) => {
      return (
        this.learningQuestionPracticeListMap
          .find(p => p.learningId === learningId)
          ?.allQuestionCategoryAnswerStatistic?.map(p => p.value)
          .flatMap(value => value)
          ?.map((p: AnswerQuestionStatisticDTO) => p.correctTimes)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }

  /**
   * 指定章节的总题数
   */
  get specifyExaminationOutlineQuestionCount() {
    return (schemeId: string, learningId: string, questionCategory: string, leafSyllabus: Array<string>) => {
      return (
        this.learningQuestionPracticeListMap
          .find(p => p.learningId === learningId)
          ?.allQuestionCategoryQuestionStatistic.find(p => p.key === questionCategory)
          ?.value?.filter(
            (p: QuestionStatisticDTO) => p.tags && p.tags.some((tagId: string) => leafSyllabus.includes(tagId))
          )
          .map((p: QuestionStatisticDTO) => p.count)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }

  /**
   * 指定章节的已答总题数
   */
  get specifyExaminationOutlineAnsweredQuestionCount() {
    return (schemeId: string, learningId: string, questionCategory: string, leafSyllabus: Array<string>) => {
      return (
        this.learningQuestionPracticeListMap
          .find(p => p.learningId === learningId)
          ?.allQuestionCategoryAnswerStatistic.find(p => p.key === questionCategory)
          ?.value?.filter(
            (p: AnswerQuestionStatisticDTO) =>
              p.tagIds && p.tagIds.some((tagId: string) => leafSyllabus.includes(tagId))
          )
          .map((p: AnswerQuestionStatisticDTO) => p.answeredCount)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }

  /**
   * 指定章节的已做答次数
   */
  get specifyExaminationOutlineAnsweredQuestionTimes() {
    return (schemeId: string, learningId: string, questionCategory: string, leafSyllabus: Array<string>) => {
      return (
        this.learningQuestionPracticeListMap
          .find(p => p.learningId === learningId)
          ?.allQuestionCategoryAnswerStatistic.find(p => p.key === questionCategory)
          ?.value?.filter(
            (p: AnswerQuestionStatisticDTO) =>
              p.tagIds && p.tagIds.some((tagId: string) => leafSyllabus.includes(tagId))
          )
          .map((p: AnswerQuestionStatisticDTO) => p.answeredTimes)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }

  /**
   * 指定章节的答对次数
   */
  get specifyExaminationOutlineCorrectQuestionTimes() {
    return (schemeId: string, learningId: string, questionCategory: string, leafSyllabus: Array<string>) => {
      return (
        this.learningQuestionPracticeListMap
          .find(p => p.learningId === learningId)
          ?.allQuestionCategoryAnswerStatistic.find(p => p.key === questionCategory)
          ?.value?.filter(
            (p: AnswerQuestionStatisticDTO) =>
              p.tagIds && p.tagIds.some((tagId: string) => leafSyllabus.includes(tagId))
          )
          .map((p: AnswerQuestionStatisticDTO) => p.correctTimes)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }

  /**
   * 根据考纲树获取试题及作答统计信息
   */
  get currentCategoryQuestionAnswerStatisticWithSyllabus() {
    return (schemeId: string, learningId: string, questionCategory: string, syllabusTree: Array<ChapterTree>) => {
      // 自定义函数，填充已答试题信息
      const fillQuestionAnswerStatistic = (schemeId: string, learningId: string, syllabusTree: Array<ChapterTree>) => {
        const questionPracticeSyllabusTree = new Array<QuestionPracticeSyllabusTree>()
        syllabusTree.forEach((syllabus: ChapterTree) => {
          const questionPracticeSyllabus: QuestionPracticeSyllabusTree = new QuestionPracticeSyllabusTree()
          questionPracticeSyllabus.id = syllabus.id
          questionPracticeSyllabus.name = syllabus.name
          questionPracticeSyllabus.relationId = syllabus.relationId
          questionPracticeSyllabus.sequence = syllabus.sequence
          questionPracticeSyllabus.answering = this.isAnswering(schemeId, learningId, questionCategory, 1, syllabus.id)
          questionPracticeSyllabus.questionCount = this.specifyExaminationOutlineQuestionCount(
            schemeId,
            learningId,
            questionCategory,
            syllabus.getLeafChapterIds()
          )
          questionPracticeSyllabus.questionAnsweredCount = this.specifyExaminationOutlineAnsweredQuestionCount(
            schemeId,
            learningId,
            questionCategory,
            syllabus.getLeafChapterIds()
          )
          questionPracticeSyllabus.questionAnsweredTimes = this.specifyExaminationOutlineAnsweredQuestionTimes(
            schemeId,
            learningId,
            questionCategory,
            syllabus.getLeafChapterIds()
          )
          questionPracticeSyllabus.questionCorrectTimes = this.specifyExaminationOutlineCorrectQuestionTimes(
            schemeId,
            learningId,
            questionCategory,
            syllabus.getLeafChapterIds()
          )
          questionPracticeSyllabus.questionCorrectRate =
            questionPracticeSyllabus.questionAnsweredTimes === 0
              ? 0
              : (questionPracticeSyllabus.questionCorrectTimes * 100.0) / questionPracticeSyllabus.questionAnsweredTimes
          if (syllabus.children) {
            questionPracticeSyllabus.children = fillQuestionAnswerStatistic(schemeId, learningId, syllabus.children)
          }
          questionPracticeSyllabusTree.push(questionPracticeSyllabus)
        })
        return questionPracticeSyllabusTree
      }
      return fillQuestionAnswerStatistic(schemeId, learningId, syllabusTree)
    }
  }

  /**
   * 当前试题分类总正确率
   */
  get currentCategoryCorrectRate() {
    return (schemeId: string, learningId: string, questionCategory: string) => {
      const answeredTimes = this.currentCategoryTotalAnsweredQuestionTimes(schemeId, learningId, questionCategory)
      const correctTimes = this.currentCategoryTotalAnsweredQuestionCorrectTimes(schemeId, learningId, questionCategory)
      return answeredTimes === 0 ? 0 : (correctTimes * 100.0) / answeredTimes
    }
  }

  /**
   * 当前学习方式总正确率
   */
  get totalCorrectRate() {
    return (schemeId: string, learningId: string) => {
      const answeredTimes = this.totalAnsweredQuestionTimes(schemeId, learningId)
      const correctTimes = this.totalAnsweredQuestionCorrectTimes(schemeId, learningId)
      return answeredTimes === 0 ? 0 : (correctTimes * 100.0) / answeredTimes
    }
  }
}

export default getModule(QuestionPracticeModule)
