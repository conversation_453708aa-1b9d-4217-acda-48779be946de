"""独立部署的微服务,K8S服务名:ms-data-export-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""批量生成学员学习心得pdf
		@param request
		@return
	"""
	BatchStudentLearningExperienceExportPdf(request:StudentLearningExperienceRequest):Boolean!
	"""导出批次单明细"""
	exportBatchOrderDetailInServicer(request:BatchOrderRequest,sort:[BatchOrderSortRequest]):Boolean!
	"""导出批次单
		@param request
		@param sort
		@return
	"""
	exportBatchOrderInServicer(request:BatchOrderRequest,sort:[BatchOrderSortRequest]):Boolean!
	"""导出批次对账
		@param request
		@param sort
		@return
	"""
	exportBatchReconciliationInServicer(request:BatchOrderRequest,sort:[BatchOrderSortRequest]):Boolean!
	"""导出批次退货单明细"""
	exportBatchReturnOrderDetailExcelInServicer(request:BatchReturnOrderRequest,sort:[BatchReturnOrderSortRequest]):Boolean!
	"""导出批次退货单"""
	exportBatchReturnOrderExcelInServicer(request:BatchReturnOrderRequest,sort:[BatchReturnOrderSortRequest]):Boolean!
	"""导出批次退货报名对账"""
	exportBatchReturnReconciliationExcelInServicer(request:BatchReturnOrderRequest,sort:[BatchReturnOrderSortRequest]):Boolean!
	"""导出中心财务数据表
		@param request
		@param sort
		@return
	"""
	exportCentralFinancialDataInServicer(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出学员选课统计
		@param request
		@return
	"""
	exportChooseCourseStatistic(request:ChooseCourseStatisticsRequest):Boolean!
	"""导出学员选课统计
		@param request
		@return
	"""
	exportChooseCourseStatisticsInServicer(request:ChooseCourseStatisticsRequest):Boolean!
	"""导出商品开通统计列表"""
	exportCommodityOpenReportFormsInServicer(request:TradeReportRequest):Boolean!
	"""商品导出"""
	exportCommoditySkuInServicer(queryRequest:CommoditySkuRequest,sortRequest:[CommoditySkuSortRequest]):Boolean!
	"""导出方案配置专题信息"""
	exportCommoditySkuWithTrainingChannelInServicer(queryRequest:CommoditySkuRequest,sortRequest:[CommoditySkuSortRequest]):Boolean!
	"""功能描述：当前服务商下课程导出
		@return : void
		@Author： lishn
		@Date： 2022/11/25 15:14
	"""
	exportCourseInServicer(request:UnauthorizedCourseRequest):Boolean!
	"""导出培训单位销售统计报表   课件供应商维度
		@return
	"""
	exportCourseSalesStatistics(request:CourseSalesStatisticsRequest):Boolean!
	"""导出培训单位销售统计报表   课件供应商+职业等级维度
		@return
	"""
	exportCourseSalesStatisticsDetail(request:CourseSalesStatisticsRequest):Boolean!
	"""发票配送导出
		@return
	"""
	exportInvoiceDeliveryInServicer(request:OfflineInvoiceExportRequest):Boolean!
	"""线下发票导出
		@return
	"""
	exportOfflineInvoiceInServicer(request:OfflineInvoiceExportRequest):Boolean!
	"""线下发票导出-继续教育
		@return
	"""
	exportOfflineInvoiceInServicerForJxjy(request:OfflineInvoiceExportRequest):Boolean!
	"""线上发票导出
		@return
	"""
	exportOnlineInvoiceInServicer(request:OnlineInvoiceRequest):Boolean!
	"""线上发票导出-继续教育
		@return
	"""
	exportOnlineInvoiceInServicerForJxjy(request:OnlineInvoiceRequest):Boolean!
	"""导出个人订单
		@param request
		@param sort
		@return
	"""
	exportOrderExcelInServicer(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出试题
		@param request 查询参数对象
		@return true 成功 false 失败
	"""
	exportQuestionExcelInServicer(request:QuestionRequest):Boolean!
	"""导出个人对账
		@param request
		@param sort
		@return
	"""
	exportReconciliationExcelInServicer(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出当前服务商下地区学习统计详情
		@return
	"""
	exportRegionLearningReportFormsDetailExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出当前服务商地区管理员下地区学习统计详情
		@return
	"""
	exportRegionLearningReportFormsDetailExcelInServicerManageRegion(request:LearningReportFormsRequest):Boolean!
	"""导出当前服务商下地区学习统计
		@return
	"""
	exportRegionLearningReportFormsExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出当前服务商地区管理员下地区学习统计
		@return
	"""
	exportRegionLearningReportFormsExcelInServicerManageRegion(request:LearningReportFormsRequest):Boolean!
	"""导出当前服务商下地区学习统计-未报名人员
		@return
	"""
	exportRegionLearningReportFormsNoRegisterDetailExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出地区开通统计
		@param request
		@return
	"""
	exportRegionOpenReportFormsInServier(request:TradeReportRequest):Boolean!
	"""导出当前服务商下地区学习统计详情（无考试相关信息）
		@return
	"""
	exportRegionStudyingReportFormsDetailExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出当前服务商下地区学习统计(新增地区人数，无考试相关信息)
		@return
	"""
	exportRegionStudyingReportFormsExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出个人退货单"""
	exportReturnOrderExcelInServicer(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""导出个人报名退货对账"""
	exportReturnReconciliationExcelInServicer(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""导出当前服务商下方案学习统计详情
		@return
	"""
	exportSchemeLearningReportFormsDetailExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""导出当前服务商下方案学习统计
		@return
	"""
	exportSchemeLearningReportFormsExcelInServicer(request:LearningReportFormsRequest):Boolean! @optionalLogin
	"""功能描述：学员导出
		@return : void
		@Author： wtl
		@Date： 2022/1/18 15:14
	"""
	exportStudentExcelInServicer(request:StudentQueryRequest):Boolean!
	"""功能描述：项目级-学员导出
		@return : void
		@Author： wtl
		@Date： 2022年11月17日 15:25:00
	"""
	exportStudentExcelInSubProject(request:StudentQueryRequest):Boolean!
	"""导出学员学习心得
		@param request
		@return
	"""
	exportStudentLearningExperienceInServicer(request:StudentLearningExperienceRequest):Boolean!
	"""导出当前服务商下学员方案学习
		@param request
		@return
	"""
	exportStudentSchemeLearningExcelInDistributor(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""导出当前服务商下学员方案学习
		@param request
		@return
	"""
	exportStudentSchemeLearningExcelInServicer(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""导出当前服务商地区管理员下学员方案学习
		@return
	"""
	exportStudentSchemeLearningExcelInServicerManageRegion(request:StudentSchemeLearningRequest):Boolean!
	"""导出专题的方案"""
	exportTrainingChannelCommoditySkuInServicer(queryRequest:CommoditySkuRequest,sortRequest:[CommoditySkuSortRequest]):Boolean!
	"""功能描述：分页查询导出任务组
		@param jobGroupRequest : 任务查询条件
		@return : void
		@Author： sjm
		@Date： 2022/1/18 15:14
	"""
	listExportTaskGroupInfoInServicer(jobGroupRequest:JobGroupRequest):[JobGroupResponse]
	"""功能描述：分页查询导出任务组
		@param jobGroupRequest : 任务查询条件
		@return : void
		@Author： sjm
		@Date： 2022/1/18 15:14
	"""
	listExportTaskGroupInfoInServicerMangeRegion(jobGroupRequest:JobGroupRequest):[JobGroupResponse]
	"""功能描述：分页查询我的导出任务日志信息列表
		@param jobRequest : 任务查询条件
		@return : void
		@Author： wtl
		@Date： 2022/1/18 15:14
	"""
	pageExportTaskInfoInMyself(page:Page,jobRequest:JobRequest):UserJobLogResponsePage @page(for:"UserJobLogResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input OwnerInfoModel @type(value:"com.fjhb.ms.basicdata.model.OwnerInfoModel") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerId:String
}
"""@Description 范围查询条件
	<AUTHOR>
	@Date 8:51 2022/5/23
"""
input BigDecimalScopeExcelRequest @type(value:"com.fjhb.ms.data.export.common.request.BigDecimalScopeExcelRequest") {
	"""result >= begin"""
	begin:BigDecimal
	"""result <= end"""
	end:BigDecimal
}
"""范围查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/5/7 15:34
"""
input DateScopeExcelRequest @type(value:"com.fjhb.ms.data.export.common.request.DateScopeExcelRequest") {
	"""result >= begin"""
	begin:DateTime
	"""result <= end"""
	end:DateTime
}
"""异步任务组名返回对象"""
input JobGroupRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.asyncTask.request.JobGroupRequest") {
	"""任务组key"""
	group:String
	"""任务组名（模糊查询）"""
	groupName:String
}
"""功能描述：任务查询参数
	@Author： wtl
	@Date： 2022/1/18 15:13
"""
input JobRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.asyncTask.request.JobRequest") {
	"""任务组名（必填）"""
	group:String
	"""任务组名匹配方式（EQ：完全匹配 LIKE：模糊匹配[*group*] LLIKE：左模糊匹配[group*] RLIKE：右模糊匹配[*group]，不传值默认为完全匹配）"""
	groupOperator:String
	"""任务名（模糊查询）"""
	jobName:String
	"""任务状态(executing:运行中 executed:运行完成 fail:运行失败)
		@see UserJobState
	"""
	jobState:String
	"""任务执行时间 yyyy-MM-dd HH:mm:ss"""
	executeTimeScope:DateScopeRequest
	"""异步任务处理结果（true:成功 false:失败）"""
	jobResult:Boolean
}
"""功能描述：学员查询条件
	@Author： wtl
	@Date： 2022年1月26日 10:10:33
"""
input StudentQueryRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.basicdata.request.StudentQueryRequest") {
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:StudentUserRequest
	"""用户认证信息"""
	authentication:AuthenticationRequest
	"""排序"""
	sortList:[StudentSortRequest]
}
input AccountRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.basicdata.request.nested.AccountRequest") {
	"""账户状态 1：正常，2：冻结，3：注销
		@see AccountStatus
	"""
	statusList:[Int]
	"""创建时间范围"""
	createTimeScope:DateScopeRequest
	"""创建人用户id"""
	createdUserId:String
}
"""功能描述：账户认证查询条件
	@Author： wtl
	@Date： 2022年1月26日 09:30:12
"""
input AuthenticationRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.basicdata.request.nested.AuthenticationRequest") {
	"""帐号"""
	identity:String
}
"""功能描述：学员集体缴费信息
	@Author： wtl
	@Date： 2022年4月21日 08:58:49
"""
input CollectiveRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.basicdata.request.nested.CollectiveRequest") {
	"""集体缴费管理员用户id集合"""
	collectiveUserIdList:[String]
}
"""功能描述 : 学员排序参数
	@date : 2022/4/1 17:15
"""
input StudentSortRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.basicdata.request.nested.StudentSortRequest") {
	"""学员排序字段"""
	studentSortField:StudentSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
input StudentUserRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.basicdata.request.nested.StudentUserRequest") {
	"""工作单位名称（模糊）"""
	companyName:String
	"""用户所属地区路径集合（模糊，右like）"""
	regionPathList:[String]
	"""集体缴费信息"""
	collective:CollectiveRequest
	"""单位所属地区路径集合（模糊，右like）"""
	companyRegionPathList:[String]
	"""单位所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	companyRegionPathListMatchType:Int
	"""是否工勤人员  (0:非工勤人员  1:工勤人员)"""
	isWorker:String
	"""是否退休   (0:非退休人员 1:退休人员)"""
	isRetire:String
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
}
"""<AUTHOR>
input ChooseCourseStatisticsRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.course.request.ChooseCourseStatisticsRequest") {
	"""培训班级上的年度"""
	year:String
	"""课件供应商ID"""
	supplierId:[String]
	"""技术等级"""
	technicalGrade:[String]
	"""课程Id"""
	courseId:[String]
	"""选课时间开始"""
	chooseCourseDateStart:DateTime
	"""选课时间结束"""
	chooseCourseDateEnd:DateTime
}
"""<AUTHOR>
input CourseSalesStatisticsRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.course.request.CourseSalesStatisticsRequest") {
	"""课件供应商ID"""
	supplierId:[String]
	"""选课时间开始"""
	chooseCourseDateStart:DateTime
	"""选课时间结束"""
	chooseCourseDateEnd:DateTime
}
"""课程查询条件
	<AUTHOR>
"""
input UnauthorizedCourseRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.course.request.UnauthorizedCourseRequest") {
	"""课程名称"""
	name:String
	"""创建时间开始"""
	createTimeBegin:DateTime
	"""创建时间结束"""
	createTimeEnd:DateTime
}
"""地区学习统计查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 14:17
"""
input LearningReportFormsRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.LearningReportFormsRequest") {
	"""学员信息"""
	student:UserRequest
	"""报名信息"""
	learningRegister:LearningRegisterRequest
	"""培训班信息"""
	scheme:SchemeRequest
	"""学习信息"""
	studentLearning:StudentLearningRequest
	"""数据分析信息"""
	dataAnalysis:DataAnalysisRequest
	"""是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1"""
	saleChannels:[Int]
	"""专题名称"""
	trainingChannelName:String
}
input StudentLearningExperienceRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.StudentLearningExperienceRequest") {
	"""学员学习心得ID"""
	studentLearningExperienceIds:[String]
	"""数据归属"""
	owner:OwnerInfoModel
	"""学习心得主题"""
	learningExperienceTopic:LearningExperienceTopicRequest
	"""学员方案学习"""
	studentLearning:StudentExperienceSchemeLearningRequest
	"""用户信息"""
	user:UserRequest
	"""学习心得类型（班级心得，课程心得）"""
	experienceType:ExperienceType
	"""状态
		@see StudentLearningExperienceStatus
	"""
	status:[StudentLearningExperienceStatus]
	"""不传值默认全查  是否要心得被删除的数据  true只要被删除的 false 只要未被删除的  null全查"""
	isDelete:Boolean
}
"""学员培训方案学习查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 11:40
"""
input StudentSchemeLearningRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.StudentSchemeLearningRequest") {
	"""学员信息"""
	student:UserRequest
	"""报名信息"""
	learningRegister:LearningRegisterRequest
	"""培训班信息"""
	scheme:SchemeRequest
	"""学习信息"""
	studentLearning:StudentLearningRequest
	"""数据分析信息"""
	dataAnalysis:DataAnalysisRequest
	"""对接管理系统"""
	connectManageSystem:ConnectManageSystemRequest
	"""扩展信息"""
	extendedInfo:ExtendedInfoRequest
	"""是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1"""
	saleChannels:[Int]
	"""专题名称"""
	trainingChannelName:String
}
"""@version: 1.0
	@description: 对接管理系统
	@author: sugs
	@create: 2022-11-15 11:27
"""
input ConnectManageSystemRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.ConnectManageSystemRequest") {
	"""同步状态
		@see SyncStatus
		0 未同步
		1 已同步
		2 同步失败
	"""
	syncStatus:Int
}
"""数据分析信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/20 15:14
"""
input DataAnalysisRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.DataAnalysisRequest") {
	"""成果配置可获得学时"""
	trainingResultPeriod:DoubleScopeRequest
	"""考核要求学时"""
	requirePeriod:DoubleScopeRequest
	"""已获得总学时"""
	acquiredPeriod:DoubleScopeRequest
}
input ExtendedInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.ExtendedInfoRequest") {
	"""是否打印
		true 打印 false 未打印
	"""
	whetherToPrint:Boolean
}
"""学员学习报名信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:08
"""
input LearningRegisterRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.LearningRegisterRequest") {
	"""报名方式
		<p>
		1:学员自主报名
		2:集体报名
		3:管理员导入
		@see StudentRegisterTypes
	"""
	registerType:Int
	"""报名来源类型(ORDER：订单 SUB_ORDER：子订单 EXCHANGE_ORDER：换货单)
		@see StudentSourceTypes
	"""
	sourceType:String
	"""报名来源ID"""
	sourceId:String
	"""学员状态(1:正常 2：冻结 3：失效)
		@see StudentStatus
	"""
	status:[Int]
	"""报名时间"""
	registerTime:DateScopeRequest
	"""订单销售渠道"""
	saleChannels:[Int]
	"""来源订单号"""
	orderNoList:[String]
	"""来源子订单号"""
	subOrderNoList:[String]
	"""来源批次单号"""
	batchOrderNoList:[String]
	"""分销商id"""
	distributorId:String
	"""分销门户id"""
	portalId:String
}
"""地区模型
	<AUTHOR>
	@version 1.0
	@date 2022/2/27 20:01
"""
input RegionRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.RegionRequest") {
	"""地区：省"""
	province:String
	"""地区：市"""
	city:String
	"""地区：区"""
	county:String
}
"""学员学习信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
input StudentLearningRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.learning.StudentLearningRequest") {
	"""培训结果
		<p>
		-1:未知，培训尚未完成
		1:培训合格
		0:培训不合格
		@see StudentTrainingResults
	"""
	trainingResultList:[Int]
	"""培训结果时间"""
	trainingResultTime:DateScopeRequest
	"""无需学习的学习方式类型
		<p>
		1: 选课学习方式
		2: 考试学习方式
		3: 练习学习方式
		4：自主学习课程学习方式
		@see LearningTypes
	"""
	notLearningTypeList:[Int]
	"""课程学习状态（0：未学习 1：学习中 2：学习完成）"""
	courseScheduleStatus:Int
	"""考试结果（-1：未考核 0：不合格 1：合格）
		@see AssessCalculateResults
	"""
	examAssessResultList:[Int]
}
"""地区sku属性查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/2/25 10:55
"""
input RegionSkuPropertyRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.scheme.RegionSkuPropertyRequest") {
	"""地区: 省"""
	province:String
	"""地区: 市"""
	city:String
	"""地区: 区县"""
	county:String
}
"""地区匹配查询
	<AUTHOR>
	@version 1.0
	@date 2022/2/25 14:19
"""
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.scheme.RegionSkuPropertySearchRequest") {
	"""地区"""
	region:[RegionSkuPropertyRequest]
	"""地区匹配条件
		<p>
		ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回，如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
		PART部分匹配：查询结果返回的地区与查询条件有给值的地区就会返回 如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
		@see com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.common.request.skuProperty.RegionSkuPropertySearchRequest.RegionSearchType
	"""
	regionSearchType:Int
}
"""培训方案信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
input SchemeRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.scheme.SchemeRequest") {
	"""培训方案id"""
	schemeId:String
	"""培训方案id"""
	schemeIdList:[String]
	"""培训方案类型
		<p>
		chooseCourseLearning: 选课规则
		autonomousCourseLearning: 自主选课
		@see SchemeType
	"""
	schemeType:String
	"""方案名称"""
	schemeName:String
	"""培训属性"""
	skuProperty:SchemeSkuPropertyRequest
}
"""培训属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 10:22
"""
input SchemeSkuPropertyRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.scheme.SchemeSkuPropertyRequest") {
	"""年度"""
	year:[String]
	"""地区"""
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	"""行业"""
	industry:[String]
	"""科目类型"""
	subjectType:[String]
	"""培训类别"""
	trainingCategory:[String]
	"""培训专业"""
	trainingProfessional:[String]
	"""技术等级"""
	technicalGrade:[String]
	"""岗位类别"""
	positionCategory:[String]
	"""培训对象"""
	trainingObject:[String]
	"""技术等级"""
	jobLevel:[String]
	"""工种"""
	jobCategory:[String]
	"""科目"""
	subject:[String]
	"""年级"""
	grade:[String]
	"""学段"""
	learningPhase:[String]
	"""学科"""
	discipline:[String]
}
"""学习心得主题"""
input LearningExperienceTopicRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.studentlearningExperience.LearningExperienceTopicRequest") {
	"""心得主题id"""
	topicIds:[String]
	"""参与活动时间"""
	dateScopeRequest:DateScopeRequest
	"""审核方式"""
	auditType:AuditType
	"""心得参与形式"""
	participateType:ParticipateType
	"""是否要心得被删除的数据 默认要"""
	isDelete:Boolean
}
input StudentExperienceSchemeLearningRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.studentlearningExperience.StudentExperienceSchemeLearningRequest") {
	"""参训资格ID"""
	qualificationIds:[String]
	"""学号"""
	studentNos:[String]
	"""方案id"""
	schemeIds:[String]
	"""学习方式id"""
	learningIds:[String]
}
"""用户属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:01
"""
input UserPropertyRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.user.UserPropertyRequest") {
	"""所属地区路径"""
	regionList:[RegionRequest]
	"""工作单位名称"""
	companyName:String
}
"""用户信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:00
"""
input UserRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.learning.request.nested.user.UserRequest") {
	"""用户id"""
	userIdList:[String]
	"""账户id"""
	accountIdList:[String]
	"""用户属性"""
	userProperty:UserPropertyRequest
}
"""订单查询参数
	<AUTHOR>
	@date 2022/01/26
"""
input BatchOrderRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.batchorder.request.BatchOrderRequest") {
	"""批次单号集合"""
	batchOrderNoList:[String]
	"""批次单基本信息查询参数"""
	basicData:BatchOrderBasicDataRequest
	"""批次单支付信息查询参数"""
	payInfo:OrderPayInfoRequest
	"""批次单创建人查询参数"""
	creatorIdList:[String]
	"""是否已经申请发票"""
	isInvoiceApplied:Boolean
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""批次单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input BatchOrderSortRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.batchorder.request.BatchOrderSortRequest") {
	"""需要排序的字段"""
	field:BatchOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""批次单基本信息查询参数
	<AUTHOR>
	@date 2022/04/17
"""
input BatchOrderBasicDataRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.batchorder.request.nested.BatchOrderBasicDataRequest") {
	"""批次单状态
		0: 未确认，批次单初始状态
		1: 正常
		2: 交易完成
		3: 交易关闭
		4: 提交处理中 提交处理完成后，变更为NORMAl
		5: 取消处理中
		@see BatchOrderStatus
	"""
	batchOrderStatusList:[Int]
	"""批次单状态变更时间"""
	batchOrderStatusChangeTime:BatchOrderStatusChangeTimeRequest
	"""批次单支付状态
		<p>
		0：未支付
		1：支付中
		2：已支付
		@see BatchOrderPaymentStatus
	"""
	batchOrderPaymentStatusList:[Int]
	"""批次单发货状态
		0: 未发货
		1: 发货中
		2: 已发货
		@see BatchOrderDeliveryStatus
	"""
	batchOrderDeliveryStatusList:[Int]
	"""批次单价格范围
		<p> 查询非0元批次单 begin填0.01
	"""
	batchOrderAmountScope:BigDecimalScopeExcelRequest
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
}
"""批次单状态变更时间查询参数
	<AUTHOR>
	@date 2022/04/17
"""
input BatchOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.batchorder.request.nested.BatchOrderStatusChangeTimeRequest") {
	"""未确认"""
	unConfirmed:DateScopeExcelRequest
	"""正常"""
	normal:DateScopeExcelRequest
	"""交易成功"""
	completed:DateScopeExcelRequest
	"""已关闭"""
	closed:DateScopeExcelRequest
	"""提交中"""
	committing:DateScopeExcelRequest
	"""取消处理中"""
	canceling:DateScopeExcelRequest
}
"""批次退货单查询参数
	<AUTHOR>
	@date 2022/04/19
"""
input BatchReturnOrderRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.batchreturnorder.request.BatchReturnOrderRequest") {
	"""批次退货单号集合"""
	batchReturnOrderList:[String]
	"""基本信息"""
	basicData:BatchReturnOrderBasicDataRequest
	"""审批信息"""
	approvalInfo:BatchReturnOrderApprovalInfoRequest
	"""批次退货单关联批次单"""
	batchOrderInfo:BatchOrderInfoRequest
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""批次退货单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input BatchReturnOrderSortRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.batchreturnorder.request.BatchReturnOrderSortRequest") {
	"""需要排序的字段"""
	field:BatchReturnOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""批次退货单关联批次单查询参数
	<AUTHOR>
	@date 2022/04/19
"""
input BatchOrderInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.batchreturnorder.request.nested.BatchOrderInfoRequest") {
	"""批次单号集合"""
	batchOrderNoList:[String]
	"""批次单创建人id集合"""
	creatorIdList:[String]
	"""收款账号ID集合"""
	receiveAccountIdList:[String]
	"""交易流水号集合"""
	flowNoList:[String]
	"""付款类型
		1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
	"""
	paymentOrderTypeList:[Int]
}
"""批次退货单关闭信息
	<AUTHOR>
	@date 2022/4/19
"""
input BatchReturnCloseReasonRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.batchreturnorder.request.nested.BatchReturnCloseReasonRequest") {
	"""批次退货单关闭类型（1：卖家取消 2：卖家拒绝退货 3：买家取消 4：确认失败取消）
		@see BatchReturnCloseTypes
	"""
	closeTypeList:[Int]
}
"""批次退货单审批信息查询参数
	<AUTHOR>
	@date 2022/03/18
"""
input BatchReturnOrderApprovalInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.batchreturnorder.request.nested.BatchReturnOrderApprovalInfoRequest") {
	"""审批时间"""
	approveTime:DateScopeExcelRequest
}
"""批次退货单基本信息查询参数
	<AUTHOR>
	@date 2022/4/19
"""
input BatchReturnOrderBasicDataRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.batchreturnorder.request.nested.BatchReturnOrderBasicDataRequest") {
	"""批次退货单状态
		0: 已创建
		1: 已确认
		2: 取消申请中
		3: 退货处理中
		4: 退货失败
		5: 正在申请退款
		6: 已申请退款
		7: 退款处理中
		8: 退款失败
		9: 退货完成
		10: 退款完成
		11: 退货退款完成
		12: 已关闭
		@see BatchReturnOrderStatus
	"""
	batchReturnOrderStatus:[Int]
	"""批次退货单关闭信息"""
	batchReturnCloseReason:BatchReturnCloseReasonRequest
	"""批次退货单状态变更时间"""
	batchReturnStatusChangeTime:BatchReturnOrderStatusChangeTimeRequest
	"""退款金额范围
		<br> 查询非0元  begin填0.01
	"""
	refundAmountScope:BigDecimalScopeExcelRequest
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
}
"""批次退货单状态变更时间查询参数
	<AUTHOR>
	@date 2022/4/19
"""
input BatchReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.batchreturnorder.request.nested.BatchReturnOrderStatusChangeTimeRequest") {
	"""申请退货时间"""
	applied:DateScopeExcelRequest
	"""批次退货完成时间
		<p> 这个参数包含了退货退款完成（批次退货单类型为退货退款）、仅退货完成（批次退货单类型为仅退货）、仅退款完成（批次退货单类型为仅退款）时间，三个时间之间用or匹配
	"""
	returnCompleted:DateScopeExcelRequest
}
"""商品查询条件
	<AUTHOR>
	@date 2022/01/25
"""
input CommoditySkuRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.commodity.request.CommoditySkuRequest") {
	"""商品id"""
	commoditySkuIdList:[String]
	"""商品名称（精确匹配）"""
	saleTitleList:[String]
	"""商品名称（模糊查询）"""
	saleTitleMatchLike:String
	"""要从查询结果中剔除的商品ID集合"""
	notShowCommoditySkuIdList:[String]
	"""商品售价"""
	price:Double
	"""商品上下架信息"""
	onShelveRequest:OnShelveRequest
	"""培训方案信息"""
	schemeRequest:SchemeRequest1
	"""商品sku属性查询"""
	skuPropertyRequest:SkuPropertyRequest
	"""是否展示资源不可用的商品"""
	isDisabledResourceShow:Boolean
	"""是否展示所有资源
		（该字段会屏蔽可见渠道、商品资源是否可用、商品上下架状态三个条件）
	"""
	isShowAll:Boolean
	"""专题id"""
	trainingChannelIds:[String]
	"""是否存在专题"""
	existTrainingChannel:Boolean
	"""管理系统平台"""
	externalTrainingPlatform:[String]
}
"""商品排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input CommoditySkuSortRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.commodity.request.CommoditySkuSortRequest") {
	"""用来排序的字段"""
	sortField:CommoditySkuSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""商品上下架相关查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input OnShelveRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.commodity.request.nested.onshelve.OnShelveRequest") {
	"""商品上下架状态
		<br> 0:已下架 1：已上架
	"""
	onShelveStatus:Int
}
"""培训方案相关查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input SchemeRequest1 @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.commodity.request.nested.scheme.SchemeRequest") {
	"""培训方案ID"""
	schemeIdList:[String]
	"""培训方案类型
		<br> chooseCourseLearning:选课规则 autonomousCourseLearning:自主学习
	"""
	schemeType:String
	"""培训方案名称(模糊查询)"""
	schemeName:String
	"""培训开始时间"""
	trainingBeginDate:DateScopeRequest
	"""培训结束时间"""
	trainingEndDate:DateScopeRequest
}
"""商品查询条件
	<AUTHOR>
	@date 2022/01/25
"""
input CommoditySkuRequest1 @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.common.request.CommoditySkuRequest") {
	"""商品id"""
	commoditySkuIdList:[String]
	"""商品Sku名称"""
	saleTitle:String
	"""商品sku属性查询"""
	skuProperty:SkuPropertyRequest
}
"""地区查询参数
	<AUTHOR>
	@date 2022/02/25
"""
input RegionSkuPropertyRequest1 @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.common.request.skuProperty.RegionSkuPropertyRequest") {
	"""地区: 省"""
	province:String
	"""地区: 市"""
	city:String
	"""地区: 区县"""
	county:String
}
"""地区查询请求参数
	<AUTHOR>
	@date 2022/02/25
"""
input RegionSkuPropertySearchRequest1 @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.common.request.skuProperty.RegionSkuPropertySearchRequest") {
	"""地区匹配方式
		<p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
		<p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
		@see RegionSearchType
	"""
	regionSearchType:Int
	"""地区"""
	region:[RegionSkuPropertyRequest1]
}
"""商品sku属性查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input SkuPropertyRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.common.request.skuProperty.SkuPropertyRequest") {
	"""年度"""
	year:[String]
	"""地区"""
	regionSkuPropertySearch:RegionSkuPropertySearchRequest1
	"""行业"""
	industry:[String]
	"""科目类型"""
	subjectType:[String]
	"""培训类别"""
	trainingCategory:[String]
	"""培训专业"""
	trainingProfessional:[String]
	"""学段"""
	learningPhase:[String]
	"""学科"""
	discipline:[String]
	"""黑龙江药师-证书类型"""
	certificatesType:[String]
	"""黑龙江药师-执业类别"""
	practitionerCategory:[String]
	"""工勤行业-工种"""
	jobCategory:[String]
	"""卫生行业-培训对象"""
	trainingObject:[String]
}
"""订单查询参数
	<AUTHOR>
	@date 2022/01/26
"""
input OrderRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.order.request.OrderRequest") {
	"""订单号集合"""
	orderNoList:[String]
	"""子订单号集合"""
	subOrderNoList:[String]
	"""子订单退货状态
		0: 未退货
		1: 退货申请中
		2: 退货中
		3: 退货成功
		4: 退款中
		5: 退款成功
		@see SubOrderReturnStatus
	"""
	subOrderReturnStatus:[Int]
	"""订单基本信息查询参数"""
	orderBasicData:OrderBasicDataRequest
	"""订单支付信息查询"""
	payInfo:OrderPayInfoRequest
	"""买家查询参数"""
	buyerIdList:[String]
	"""发货商品信息"""
	deliveryCommodity:CommoditySkuRequest1
	"""销售渠道
		0-自营 1-分销 不传则查全部
	"""
	saleChannel:Int
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""管理系统平台"""
	externalTrainingPlatform:[String]
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""订单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input OrderSortRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.order.request.OrderSortRequest") {
	"""需要排序的字段"""
	field:OrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""订单基本信息查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input OrderBasicDataRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.order.request.nested.OrderBasicDataRequest") {
	"""订单类型
		1:常规订单 2:批次关联订单
		@see com.fjhb.domain.trade.api.order.consts.OrderTypes
	"""
	orderType:Int
	"""批次单号"""
	batchOrderNoList:[String]
	"""订单状态
		<br> 1:正常 2：交易完成 3：交易关闭
		@see OrderStatus
	"""
	orderStatusList:[Int]
	"""订单支付状态
		<br> 0:未支付 1：支付中 2：已支付
		@see com.fjhb.domain.trade.api.order.consts.OrderPaymentStatus
	"""
	orderPaymentStatusList:[Int]
	"""订单发货状态
		<br> 0:未发货 1：发货中 2：已发货
		@see com.fjhb.domain.trade.api.order.consts.OrderDeliveryStatus
	"""
	orderDeliveryStatusList:[Int]
	"""订单状态变更时间"""
	orderStatusChangeTime:OrderStatusChangeTimeRequest
	"""购买渠道
		<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelTypesList:[Int]
	"""终端
		<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
		@see PurchaseChannelTerminalCodes
	"""
	terminalCodeList:[String]
	"""订单价格范围
		<br> 查询非0元订单 begin填0.01
	"""
	orderAmountScope:BigDecimalScopeExcelRequest
}
"""订单支付信息相关查询参数
	<AUTHOR>
	@date 2022/01/27
"""
input OrderPayInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.order.request.nested.OrderPayInfoRequest") {
	"""收款账号ID"""
	receiveAccountIdList:[String]
	"""交易流水号"""
	flowNoList:[String]
	"""付款类型
		1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
	"""
	paymentOrderTypeList:[Int]
}
"""订单状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input OrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.order.request.nested.OrderStatusChangeTimeRequest") {
	"""订单处于正常状态时间范围(创建时间范围)"""
	normalDateScope:DateScopeExcelRequest
	"""订单创建时间范围"""
	completedDatesScope:DateScopeExcelRequest
}
"""退货单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.returnorder.request.ReturnOrderRequest") {
	"""退货单号"""
	returnOrderNoList:[String]
	"""基本信息"""
	basicData:ReturnOrderBasicDataRequest
	"""审批信息"""
	approvalInfo:ReturnOrderApprovalInfoRequest
	"""退货商品id集合"""
	returnCommoditySkuIdList:[String]
	"""退款商品id集合"""
	refundCommoditySkuIdList:[String]
	"""退货单关联子订单查询参数"""
	subOrderInfo:SubOrderInfoRequest
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""退货商品"""
	returnCommodity:CommoditySkuRequest1
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""订单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input ReturnSortRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.returnorder.request.ReturnSortRequest") {
	"""需要排序的字段"""
	field:ReturnOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""退货单关联子订单的主订单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input OrderInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.returnorder.request.nested.OrderInfoRequest") {
	"""订单号集合"""
	orderNoList:[String]
	"""关联批次单号"""
	batchOrderNoList:[String]
	"""买家id集合"""
	buyerIdList:[String]
	"""收款账号ID"""
	receiveAccountIdList:[String]
	"""原始订单交易流水号"""
	flowNoList:[String]
	"""购买渠道
		<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelTypesList:[Int]
	"""终端
		<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
		@see PurchaseChannelTerminalCodes
	"""
	terminalCodeList:[String]
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题id"""
	saleChannelIds:[String]
	"""专题名称"""
	saleChannelName:String
}
"""退货单关闭信息
	<AUTHOR>
	@date 2022年4月11日 11:33:35
"""
input ReturnCloseReasonRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.returnorder.request.nested.ReturnCloseReasonRequest") {
	"""退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
		@see ReturnOrderCloseTypes
	"""
	closeTypeList:[Int]
}
"""退货单审批信息查询参数
	<AUTHOR>
	@date 2022/03/18
"""
input ReturnOrderApprovalInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.returnorder.request.nested.ReturnOrderApprovalInfoRequest") {
	"""审批时间"""
	approveTime:DateScopeExcelRequest
}
"""退货单基本信息查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderBasicDataRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.returnorder.request.nested.ReturnOrderBasicDataRequest") {
	"""退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)"""
	returnOrderStatus:[Int]
	"""退货单申请来源类型
		SUB_ORDER
		BATCH_RETURN_ORDER
		@see ReturnOrderApplySourceTypes
	"""
	applySourceType:String
	"""来源ID集合"""
	applySourceIdList:[String]
	"""退货单关闭信息"""
	returnCloseReason:ReturnCloseReasonRequest
	"""退货单状态变更时间"""
	returnStatusChangeTime:ReturnOrderStatusChangeTimeRequest
	"""退款金额范围
		<br> 查询非0元  begin填0.01
	"""
	refundAmountScope:BigDecimalScopeExcelRequest
}
"""退货单状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.returnorder.request.nested.ReturnOrderStatusChangeTimeRequest") {
	"""申请退货时间"""
	applied:DateScopeExcelRequest
	"""退货单完成时间
		<br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
	"""
	returnCompleted:DateScopeExcelRequest
}
"""<AUTHOR>
	@date 2022/03/24
"""
input SubOrderInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.returnorder.request.nested.SubOrderInfoRequest") {
	"""子订单号集合"""
	subOrderNoList:[String]
	"""订单查询参数"""
	orderInfo:OrderInfoRequest
}
"""商品开通统计报表查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input TradeReportRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.tradereport.request.TradeReportRequest") {
	"""交易时间范围"""
	tradeTime:DateScopeExcelRequest
	"""买家所在地区路径"""
	buyerAreaPath:[String]
	"""商品查询条件"""
	commoditySku:CommoditySkuRequest12
	"""是否包含集体缴费信息"""
	containsCollective:Boolean!
	"""买家所在地区是否需要包含全部下级地区数据"""
	isAllCotained:Boolean
	"""销售渠道
		0-自营 1-分销 不传则查全部
	"""
	saleChannel:Int
	"""排除的销售渠道
		0-自营 1-分销 2-专题 3-华医网
	"""
	excludedSaleChannels:[Int]
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
	"""分销商id"""
	distributorId:String
	"""门户id"""
	portalId:String
	jobName:String
}
"""商品查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input CommoditySkuRequest12 @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.tradereport.request.nested.CommoditySkuRequest") {
	"""商品Sku名称"""
	saleTitle:String
	"""商品sku属性查询"""
	skuProperty:SkuPropertyRequest
	"""学习方案查询参数"""
	scheme:SchemeRequest12
}
"""方案查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input SchemeRequest12 @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.tradereport.request.nested.SchemeRequest") {
	"""方案类型
		@see SchemeType
	"""
	schemeType:String
	"""方案学时"""
	schemePeriodScope:DoubleScopeRequest
}
"""发票关联订单查询参数
	<AUTHOR>
	@date 2022/3/18
"""
input InvoiceAssociationInfoRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.InvoiceAssociationInfoRequest") {
	"""关联订单类型
		0:订单号
		1:批次单号
		@see AssociationTypes
	"""
	associationType:Int
	"""订单号 | 批次单号"""
	associationIdList:[String]
	"""买家信息"""
	buyerIdList:[String]
	"""企业id"""
	unitBuyerUnitIdList:[String]
	"""收款账号"""
	receiveAccountIdList:[String]
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
}
"""发票状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input InvoiceStatusChangeTimeRequest1 @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.InvoiceStatusChangeTimeRequest") {
	"""正常"""
	normal:DateScopeExcelRequest
	"""作废"""
	invalid:DateScopeExcelRequest
}
"""线下发票查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceExportRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.offline.request.OfflineInvoiceExportRequest") {
	"""发票ID集合"""
	invoiceIdList:[String]
	"""发票基本信息"""
	basicData:OfflineInvoiceBasicDataRequest
	"""发票关联订单查询参数"""
	associationInfo:InvoiceAssociationInfoRequest
	"""发票配送信息"""
	invoiceDeliveryInfo:OfflineInvoiceDeliveryInfoRequest
	jobName:String
}
"""配送地址信息
	<AUTHOR>
	@date 2022/05/07
"""
input DeliveryAddressRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.offline.request.nested.DeliveryAddressRequest") {
	"""收件人"""
	consignee:String
}
"""配送状态变更时间查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input DeliveryStatusChangeTimeRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.offline.request.nested.DeliveryStatusChangeTimeRequest") {
	"""未就绪"""
	unReady:DateScopeExcelRequest
	"""已就绪"""
	ready:DateScopeExcelRequest
	"""已配送"""
	shipped:DateScopeExcelRequest
	"""已自取"""
	taken:DateScopeExcelRequest
}
"""快递信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input ExpressRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.offline.request.nested.ExpressRequest") {
	"""快递单号"""
	expressNo:String
}
"""发票开票状态变更时间记录查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input InvoiceBillStatusChangTimeRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.offline.request.nested.InvoiceBillStatusChangTimeRequest") {
	"""发票申请开票时间"""
	unBillDateScope:DateScopeExcelRequest
	"""发票开票时间"""
	successDateScope:DateScopeExcelRequest
}
"""线下发票基本信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceBasicDataRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.offline.request.nested.OfflineInvoiceBasicDataRequest") {
	"""发票类型
		1:电子发票 2:纸质发票
		@see InvoiceTypes
	"""
	invoiceTypeList:[Int]
	"""发票种类
		1:普通发票 2:增值税普通发票 3:增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategory:[Int]
	"""发票状态
		1:正常
		2:作废
		@see InvoiceStatus
	"""
	invoiceStatus:Int
	"""发票状态变更时间记录"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeRequest1
	"""发票开票状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	billStatusList:[Int]
	"""发票开票状态变更时间记录"""
	billStatusChangTime:InvoiceBillStatusChangTimeRequest
	"""发票是否冻结"""
	freeze:Boolean
	"""发票号集合"""
	invoiceNoList:[String]
	"""商品id集合"""
	commoditySkuIdList:[String]
}
"""线下发票配送信息
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceDeliveryInfoRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.offline.request.nested.OfflineInvoiceDeliveryInfoRequest") {
	"""配送状态
		0:未就绪 1：已就绪 2：已自取 3：已配送
		@see OfflineDeliveryStatus
	"""
	deliveryStatusList:[Int]
	"""配送状态变更时间记录
		0:未就绪 1：已就绪 2：已自取 3：已配送
		key值 {@link OfflineDeliveryStatus}
	"""
	deliveryStatusChangeTime:DeliveryStatusChangeTimeRequest
	"""配送方式
		0:无 1：自取 2：快递
		@see OfflineShippingMethods
	"""
	shippingMethodList:[Int]
	"""快递信息"""
	express:ExpressRequest
	"""自取信息"""
	takeResult:TakeResultRequest
	"""配送地址信息"""
	deliveryAddress:DeliveryAddressRequest
}
"""取件信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input TakeResultRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.offline.request.nested.TakeResultRequest") {
	"""领取人"""
	takePerson:String
}
"""发票查询参数
	<AUTHOR>
	@date 2022/03/23
"""
input OnlineInvoiceRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.online.request.OnlineInvoiceRequest") {
	"""发票id集合"""
	invoiceIdList:[String]
	"""发票基础信息查询参数"""
	basicData:OnlineInvoiceBasicDataRequest
	"""发票关联订单查询参数"""
	associationInfoList:[InvoiceAssociationInfoRequest]
	"""蓝票票据查询参数"""
	blueInvoiceItem:OnlineInvoiceItemRequest
	"""红票票据查询参数"""
	redInvoiceItem:OnlineInvoiceItemRequest
	"""销售渠道
		0-自营 1-分销 不传则查全部
	"""
	saleChannel:Int
	jobName:String
}
"""发票开具状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input BillStatusChangeTimeRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.online.request.nested.BillStatusChangeTimeRequest") {
	"""未开具"""
	unBill:DateScopeExcelRequest
	"""开票中"""
	billing:DateScopeExcelRequest
	"""开票成功"""
	success:DateScopeExcelRequest
	"""开票失败"""
	failure:DateScopeExcelRequest
}
"""发票状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input InvoiceStatusChangeTimeRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.online.request.nested.InvoiceStatusChangeTimeRequest") {
	"""正常"""
	normal:DateScopeExcelRequest
	"""作废"""
	invalid:DateScopeExcelRequest
}
"""发票基础信息查询参数
	<AUTHOR>
	@date 2022/03/23
"""
input OnlineInvoiceBasicDataRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.online.request.nested.OnlineInvoiceBasicDataRequest") {
	"""发票类型
		1:电子发票 2:纸质发票
		@see InvoiceTypes
	"""
	invoiceType:Int
	"""发票种类
		1:普通发票 2:增值税普通发票 3:增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategoryList:[Int]
	"""发票状态变更时间
		@see InvoiceStatus
	"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeRequest
	"""发票状态
		1：正常 2：作废
		@see InvoiceStatus
	"""
	invoiceStatusList:[Int]
	"""蓝票票据开具状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	blueInvoiceItemBillStatusList:[Int]
	"""红票票据开具状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	redInvoiceItemBillStatusList:[Int]
	"""发票是否已冲红"""
	flushed:Boolean
	"""发票是否已生成红票票据"""
	redInvoiceItemExist:Boolean
	"""商品id集合"""
	commoditySkuIdList:[String]
	"""发票是否冻结"""
	freeze:Boolean
}
"""发票票据
	<AUTHOR>
	@date 2022/03/18
"""
input OnlineInvoiceItemRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.online.request.nested.OnlineInvoiceItemRequest") {
	"""票据开具状态变更时间"""
	billStatusChangeTime:BillStatusChangeTimeRequest
	"""发票号码"""
	billNoList:[String]
}
"""试题查询条件"""
input QuestionRequest @type(value:"com.fjhb.ms.data.export.service.question.param.QuestionRequest") {
	"""试题ID集合"""
	questionIdList:[String]
	"""题库ID集合"""
	libraryIdList:[String]
	"""关联课程ID集合"""
	relateCourseIds:[String]
	"""试题题目"""
	topic:String
	"""试题类型（1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题）"""
	questionType:Int
	"""创建时间范围"""
	createTimeScope:DateScopeRequest1
	"""是否启用"""
	isEnabled:Boolean
	jobName:String
}
"""功能描述：时间范围查询条件
	@Author： wtl
	@Date： 2022/1/25 15:30
"""
input DateScopeRequest1 @type(value:"com.fjhb.ms.data.export.service.question.param.nested.DateScopeRequest") {
	"""开始时间
		查询大于等于开始时间的结果
	"""
	beginTime:DateTime
	"""结束时间
		查询小于等于结束时间的结果
	"""
	endTime:DateTime
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
enum AuditType @type(value:"com.fjhb.ms.course.learning.query.constants.AuditType") {
	AUTO_AUDIT
	MANUAL_AUDIT
}
enum ExperienceType @type(value:"com.fjhb.ms.course.learning.query.constants.ExperienceType") {
	SCHEME
	COURSE
}
enum ParticipateType @type(value:"com.fjhb.ms.course.learning.query.constants.ParticipateType") {
	SUBMIT_FILE
	EDIT_ONLINE
}
enum StudentLearningExperienceStatus @type(value:"com.fjhb.ms.course.learning.query.constants.StudentLearningExperienceStatus") {
	SUBMITING
	SUBMITTED
	PASS
	RETURNED
}
"""异步任务组名返回对象"""
type JobGroupResponse @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.asyncTask.response.JobGroupResponse") {
	"""异步任务组key"""
	group:String
	"""异步任务组名"""
	groupName:String
	"""排序大小"""
	order:Int!
	"""所在域"""
	domain:[String]
}
"""功能描述：异步任务日志返回对象
	@Author： wtl
	@Date： 2022/4/11 17:18
"""
type UserJobLogResponse @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.asyncTask.response.UserJobLogResponse") {
	"""任务id"""
	jobId:String
	"""任务组名"""
	group:String
	"""任务名"""
	jobName:String
	"""任务开始时间"""
	beginTime:DateTime
	"""任务结束时间"""
	endTime:DateTime
	"""任务状态(executing:运行中 executed:运行完成 fail:运行失败)
		@see UserJobState
	"""
	jobState:String
	"""异步任务处理结果（true:成功 false:失败）"""
	jobResult:Boolean
	"""任务执行成功或失败的信息"""
	message:String
	"""导出文件路径"""
	exportFilePath:String
	"""是否受保护"""
	isProtected:Boolean!
	"""资源id"""
	fileResourceId:String
	"""操作人id"""
	operatorUserId:String
	"""操作人帐户id"""
	operatorAccountId:String
}
"""功能描述：排序类型
	@Author： wtl
	@Date： 2021/12/27 10:30
"""
enum SortTypeEnum @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.basicdata.request.nested.SortTypeEnum") {
	"""升序"""
	ASC
	"""降序"""
	DESC
}
"""功能描述：学员排序字段
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
enum StudentSortFieldEnum @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.basicdata.request.nested.StudentSortFieldEnum") {
	"""创建时间"""
	createdTime
}
"""批次单可用于排序的字段
	<AUTHOR>
	@date 2022/04/17
"""
enum BatchOrderSortField @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.batchorder.request.nested.BatchOrderSortField") {
	"""批次单未确认时间（批次单创建）"""
	BATCH_ORDER_UN_CONFIRMED_TIME
	"""批次单提交时间"""
	BATCH_ORDER_COMMIT_TIME
}
"""批次退货单可用于排序的字段
	<AUTHOR>
	@date 2022/04/19
"""
enum BatchReturnOrderSortField @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.batchreturnorder.request.nested.BatchReturnOrderSortField") {
	"""批次退货单创建时间"""
	CREATED_TIME
}
"""商品可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum CommoditySkuSortField @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.commodity.request.nested.CommoditySkuSortField") {
	"""上架时间"""
	ON_SHELVE_TIME
	"""最新编辑时间"""
	LAST_EDIT_TIME
	"""商品销售数"""
	SALE_TOTAL_NUMBER
	"""商品sku属性-年度"""
	SKU_PROPERTY_YEAR
	"""专题排序"""
	TRAINING_CHANNEL
}
"""排序参数
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum SortPolicy @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.common.request.SortPolicy") {
	"""正序"""
	ASC
	"""倒序"""
	DESC
}
"""订单可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum OrderSortField @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.order.request.nested.OrderSortField") {
	"""订单创建时间"""
	ORDER_NORMAL_TIME
	"""订单交易完成时间"""
	ORDER_COMPLETED_TIME
}
"""退货单可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum ReturnOrderSortField @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.returnorder.request.nested.ReturnOrderSortField") {
	"""退货单申请时间"""
	APPLIED_TIME
}

scalar List
type UserJobLogResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserJobLogResponse]}
