import { ResponseStatus } from '@hbfe/common'
import roleGateWay from '@api/ms-gateway/ms-role-v1'
export class MutationRemoveAccountRoles {
  /*
   *  账号id
   * */
  accountId = ''
  accountRoleIds: Array<string> = []

  async doRemove() {
    try {
      if (!this.accountId) {
        return new ResponseStatus(500, '用户账号id不可为空')
      }

      if (!this.accountRoleIds.length) {
        return new ResponseStatus(500, '移除的角色不可为空')
      }

      const res = await roleGateWay.removeUserOwnRoles({
        accountId: this.accountId,
        accountRoleIds: this.accountRoleIds
      })
      console.log('调用了doRemove方法，返回值=', res.status)
      return res.status
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/mutation/MutationRemoveAccountRoles.ts所处方法，doRemove',
        e
      )
    }
  }
}
