import BasicDataGateway, {
  TrainingPropertyQueryRequest,
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { listIndustryPropertyChildByCategoryV2 } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage/graphql-importer'

class QueryPositionCategory {
  /**
   * 查询对应培训对象下的岗位类别
   * @param industryPropertyId 行业属性id
   * @param trainingObjectPropertyId 培训对象属性id
   */
  async queryPositionCategoryByTrainingObjectId(
    industryPropertyId: string,
    trainingObjectPropertyId: string
  ): Promise<Array<TrainingPropertyResponse>> {
    const request = new TrainingPropertyQueryRequest()
    request.industryPropertyId = industryPropertyId
    request.propertyId = trainingObjectPropertyId
    request.categoryCode = IndustryPropertyCodeEnum.PERSON_POSITION_CATEGORY
    const res = await BasicDataGateway.listIndustryPropertyChildByCategoryV2(request)

    return res.data || new Array<TrainingPropertyResponse>()
  }

  /**
   * 批量查询对应培训对象下的岗位类别
   * @param industryPropertyId 行业属性id
   * @param trainingObjectPropertyIds 培训对象属性id数组
   */
  async batchQueryPositionCategoryByTrainingObjectId(
    industryPropertyId: string,
    trainingObjectPropertyIds: Array<string>
  ): Promise<Array<TrainingPropertyResponse>> {
    const requestList = new Array<TrainingPropertyQueryRequest>()
    trainingObjectPropertyIds.map(item => {
      const req = new TrainingPropertyQueryRequest()

      req.industryPropertyId = industryPropertyId
      req.propertyId = item
      req.categoryCode = IndustryPropertyCodeEnum.PERSON_POSITION_CATEGORY

      requestList.push(req)
    })

    const reWriteGQL = new RewriteGraph<TrainingPropertyResponse, TrainingPropertyQueryRequest>(
      BasicDataGateway._commonQuery,
      listIndustryPropertyChildByCategoryV2
    )
    await reWriteGQL.request(requestList)

    const result = new Array<TrainingPropertyResponse>()

    for (const value of reWriteGQL.itemMap.values()) {
      result.push(value)
    }

    return result
  }
}

export default new QueryPositionCategory()
