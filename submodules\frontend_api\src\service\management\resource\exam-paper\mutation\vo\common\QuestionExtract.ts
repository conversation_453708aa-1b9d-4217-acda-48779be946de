import { QuestionScopeSettingTypes } from '@api/service/management/resource/exam-paper/enum/ExamScopeSettingTypes'
import { QuestionTypeEnum } from '@api/service/common/enums/question/QuestionType'
import ExamLibrary from './ExamLibrary'
import { QuestionScoreRequest } from '@api/ms-gateway/ms-examextraction-v1'
/**
 * 大题集合
 */
class QuestionExtract {
  /**
   *本大题总分
   * [给页面用]
   */
  totalScore: number = null
  /**
   * 每题平均分
   * [平均分是0.5 的倍数]
   */
  eachQuestionScore: number = null
  /**
   * 具体试题分数
   */
  questionScores?: Array<QuestionScoreRequest> = []
  /**
   * 试题类型
   */
  questionType: QuestionTypeEnum = QuestionTypeEnum.radio
  /**
   * 大题序号
   */
  sequence = 1
  /**
   * 大题名称
   */
  groupName?: string = ''
  /**
   * 试题数
   */
  questionCount: number = null
  /**
   * 出题范围
   * LibraryQuestionScopeSetting 按题库出题
   * UserCourseScopeSetting 按学员id出题
   * LibraryFixedQuestionExtractSetting 按照题库指定数量
   */
  questionScopesTypes?: QuestionScopeSettingTypes = QuestionScopeSettingTypes.LibraryQuestionScopeSetting

  /**
   * 题库对应的
   */
  /**
   * 题库对应数量出题设置信息
   */
  libraryMapQuestionNumSettings?: Array<ExamLibrary> = []

  /**
   * 智能抽题目
   */
  /**
   * 课程来源
   * 1 考试和测验用 [默认为1]
    @see UserCourseSources
   */
  userCourseSource = 1
  /**
   * 要求的组卷信息key.
   * 当{@link #userCourseSource} &#x3D; 用户课程题库时需要指定
   * @see ExtractionMessageKeys
   */
  requireKeys?: Array<string> = []
}

export default QuestionExtract
