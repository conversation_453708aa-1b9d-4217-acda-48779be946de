import AbstractEnum from '@api/service/common/enums/AbstractEnum'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'

/**
 * 住宿方式
 */
export enum LodgingTypeEnum {
  /**
   * 无需住宿
   */
  noNeed = 0,

  /**
   * 单住
   */
  single = 1,

  /**
   * 合住
   */
  shared = 2
}

class LodgingType extends AbstractEnum<LodgingTypeEnum> {
  static enum = LodgingTypeEnum
  constructor(status?: LodgingTypeEnum) {
    super()
    this.current = status
    this.map.set(LodgingTypeEnum.noNeed, '-')
    this.map.set(LodgingTypeEnum.single, '单住')
    this.map.set(LodgingTypeEnum.shared, '合住')
  }
}

export default new LodgingType()
