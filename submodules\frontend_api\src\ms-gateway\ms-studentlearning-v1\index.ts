import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-studentlearning-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-studentlearning-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 生成学习 token
<AUTHOR> create 2021/4/15 8:48
 */
export class ApplyLearningTokenRequest {
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学习方式id
   */
  learningId: string
}

/**
 * 考核通过后的数据修复请求
<AUTHOR>
 */
export class PassedAssessDataRepairRequest {
  aggId: string
  index: number
}

/**
 * 参训资格数据修复请求
<AUTHOR>
 */
export class TrainingQualificationDataRepairRequest {
  aggId: string
  index: number
}

/**
 * 申请问卷请求
<AUTHOR>
@date 2025/4/22 16:30
 */
export class ApplyQuestionnaireRequest {
  /**
   * 学员信息token
   */
  studentToken?: string
}

export class QuestionGroup {
  sequence: number
  questionType: number
  groupName: string
  eachQuestionScore: number
}

export class PreviewPaperPublishConfigureResponse {
  name: string
  description: string
  timeLength: number
  totalScore: number
  totalQuestionCount: number
  groups: Array<QuestionGroup>
  paperType: number
  questions: Array<BaseQuestionResponse>
}

export interface BaseFillAnswerResponse {
  type: number
}

export class ChildItemResponse {
  no: number
  questionId: string
}

export class ChooseAnswerOptionResponse {
  id: string
  content: string
  enableFillContent: boolean
  mustFillContent: boolean
}

export class DisarrayFillAnswerResponse implements BaseFillAnswerResponse {
  disarrayCorrectAnswers: Array<string>
  type: number
}

export class FillCorrectAnswersResponse {
  blankNo: number
  answers: Array<string>
}

export class SequenceFillAnswerResponse implements BaseFillAnswerResponse {
  sequenceCorrectAnswers: Array<FillCorrectAnswersResponse>
  type: number
}

export class SequenceRateFillAnswerResponse implements BaseFillAnswerResponse {
  sequenceRateCorrectAnswers: Array<SequenceFillAnswerResponse>
  type: number
}

export class AskQuestionResponse implements BaseQuestionResponse {
  askAnswer: string
  id: string
  groupSequence: number
  questionType: number
  score: number
  topic: string
  isChildQuestion: boolean
  parentQuestionId: string
  dissects: string
  relateCourseId: Array<string>
  questionDifficulty: number
  answerRequired: boolean
  answered: boolean
  labelCodeList: Array<string>
}

export interface BaseQuestionResponse {
  id: string
  groupSequence: number
  questionType: number
  score: number
  topic: string
  isChildQuestion: boolean
  parentQuestionId: string
  dissects: string
  relateCourseId: Array<string>
  questionDifficulty: number
  answerRequired: boolean
  answered: boolean
  labelCodeList: Array<string>
}

export class FatherQuestionResponse implements BaseQuestionResponse {
  childQuestions: Array<ChildItemResponse>
  id: string
  groupSequence: number
  questionType: number
  score: number
  topic: string
  isChildQuestion: boolean
  parentQuestionId: string
  dissects: string
  relateCourseId: Array<string>
  questionDifficulty: number
  answerRequired: boolean
  answered: boolean
  labelCodeList: Array<string>
}

export class FillQuestionResponse implements BaseQuestionResponse {
  fillCount: number
  fillQuestionCorrectAnswer: BaseFillAnswerResponse
  id: string
  groupSequence: number
  questionType: number
  score: number
  topic: string
  isChildQuestion: boolean
  parentQuestionId: string
  dissects: string
  relateCourseId: Array<string>
  questionDifficulty: number
  answerRequired: boolean
  answered: boolean
  labelCodeList: Array<string>
}

export class MultipleQuestionResponse implements BaseQuestionResponse {
  answerOptions: Array<ChooseAnswerOptionResponse>
  multipleQuestionCorrectAnswerIds: Array<string>
  multipleAnswer: Array<string>
  fillContentMap: Map<string, string>
  id: string
  groupSequence: number
  questionType: number
  score: number
  topic: string
  isChildQuestion: boolean
  parentQuestionId: string
  dissects: string
  relateCourseId: Array<string>
  questionDifficulty: number
  answerRequired: boolean
  answered: boolean
  labelCodeList: Array<string>
}

export class OpinionQuestionResponse implements BaseQuestionResponse {
  correctAnswerText: string
  incorrectAnswerText: string
  opinionQuestionCorrectAnswer: boolean
  id: string
  groupSequence: number
  questionType: number
  score: number
  topic: string
  isChildQuestion: boolean
  parentQuestionId: string
  dissects: string
  relateCourseId: Array<string>
  questionDifficulty: number
  answerRequired: boolean
  answered: boolean
  labelCodeList: Array<string>
}

export class RadioQuestionResponse implements BaseQuestionResponse {
  answerOptions: Array<ChooseAnswerOptionResponse>
  radioQuestionCorrectAnswerId: string
  radioAnswer: string
  fillContent: string
  id: string
  groupSequence: number
  questionType: number
  score: number
  topic: string
  isChildQuestion: boolean
  parentQuestionId: string
  dissects: string
  relateCourseId: Array<string>
  questionDifficulty: number
  answerRequired: boolean
  answered: boolean
  labelCodeList: Array<string>
}

export class ScaleQuestionResponse implements BaseQuestionResponse {
  scaleType: number
  startDegree: string
  endDegree: string
  series: number
  initialValue: number
  scaleAnswer: number
  id: string
  groupSequence: number
  questionType: number
  score: number
  topic: string
  isChildQuestion: boolean
  parentQuestionId: string
  dissects: string
  relateCourseId: Array<string>
  questionDifficulty: number
  answerRequired: boolean
  answered: boolean
  labelCodeList: Array<string>
}

/**
 * 申请问卷返回值
<AUTHOR>
@date 2025/4/23 15:33
 */
export class ApplyQuestionnaireResponse {
  /**
   * 答卷返回对象
   */
  answerPaperViewResponse: PreviewPaperPublishConfigureResponse
  /**
   * 作答token
   */
  answerToken: string
  /**
   * 出卷时间
   */
  answerExtractionTime: string
}

/**
 * <AUTHOR> create 2022/4/20 10:20
 */
export class ApplyStudentLearningValidateResponse {
  /**
   * 正常&#x3D;200.
培训未开始 &#x3D; 50002
培训已结束 &#x3D; 50003
学员培训资格已冻结 &#x3D; 60001
学员培训资格已失效 &#x3D; 60002
学习方式被停用 &#x3D; 60003
前置条件未达标 &#x3D; 60004
500 其他未定义异常
   */
  code: string
  /**
   * 异常信息
   */
  message: string
  /**
   * 冻结类型 RETURN_ORDER:退货 EXCHANGE_ORDER:换货
   */
  frozenType: string
  /**
   * 失效类型 RETURN_ORDER:退货 EXCHANGE_ORDER:换货
   */
  invalidType: string
}

/**
 * 申请考试返回值
<AUTHOR>
@date 2025/4/22 16:03
 */
export class ApplyExamResponse {
  /**
   * 是否需要强制完成调查问卷
   */
  needForceQuestionnaire: boolean
  /**
   * 需要强制完成的调查问卷信息
   */
  unaccomplishedQuestionnaire: Array<UnaccomplishedQuestionnaire>
  /**
   * 考试token
   */
  examToken: string
}

/**
 * 未完成问卷信息
<AUTHOR>
@date 2025/4/15 10:15
 */
export class UnaccomplishedQuestionnaire {
  /**
   * 未完成问卷id
   */
  unaccomplishedQuestionnaireId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 允许开始时间
   */
  allowStartTime: string
  /**
   * 允许结束时间
   */
  allowEndTime: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 参训人员证书成果撤回
   * @param schemeId
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async TraineesAchievementRevocation(
    params: { schemeId?: string; time?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.TraineesAchievementRevocation,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请考试(校验是否强制问卷)
   * @param mutate 查询 graphql 语法文档
   * @param studentToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyExam(
    studentToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyExam,
    operation?: string
  ): Promise<Response<ApplyExamResponse>> {
    return commonRequestApi<ApplyExamResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { studentToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请问卷
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyQuestionnaire(
    request: ApplyQuestionnaireRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyQuestionnaire,
    operation?: string
  ): Promise<Response<ApplyQuestionnaireResponse>> {
    return commonRequestApi<ApplyQuestionnaireResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请学习token
   * 培训未开始 = 50002
   * 培训已结束 = 50003
   * 学员培训资格已冻结 = 60001
   * 学员培训资格已失效 = 60002
   * 学习方式被停用 = 60003
   * 前置条件未达标 = 60004
   * 重算未完成 = 60005
   * 非期数参训资格 = 60006
   * 非方案参训资格 = 60007
   * 500 其他未定义异常
   * @param request
   * @return
   * @see ApplyLearningTokenValidateCommand.ApplyLearningTokenValidateResult
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyStudentLearningToken(
    request: ApplyLearningTokenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyStudentLearningToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请学习token(期数)
   * 培训未开始 = 50002
   * 培训已结束 = 50003
   * 学员培训资格已冻结 = 60001
   * 学员培训资格已失效 = 60002
   * 学习方式被停用 = 60003
   * 前置条件未达标 = 60004
   * 重算未完成 = 60005
   * 非期数参训资格 = 60006
   * 非方案参训资格 = 60007
   * 500 其他未定义异常
   * @param request
   * @return
   * @see ApplyLearningTokenValidateCommand.ApplyLearningTokenValidateResult
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyStudentLearningTokenInIssue(
    request: ApplyLearningTokenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyStudentLearningTokenInIssue,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请学习token且中断智能学习任务（如果存在且未执行完成）
   * 培训未开始 = 50002
   * 培训已结束 = 50003
   * 学员培训资格已冻结 = 60001
   * 学员培训资格已失效 = 60002
   * 学习方式被停用 = 60003
   * 前置条件未达标 = 60004
   * 重算未完成 = 60005
   * 非期数参训资格 = 60006
   * 非方案参训资格 = 60007
   * 500 其他未定义异常
   * @param request
   * @return
   * @see ApplyLearningTokenValidateCommand.ApplyLearningTokenValidateResult
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyStudentLearningTokenInterruptAutoStudy(
    request: ApplyLearningTokenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyStudentLearningTokenInterruptAutoStudy,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请学习token校验
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyStudentLearningValidate(
    request: ApplyLearningTokenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyStudentLearningValidate,
    operation?: string
  ): Promise<Response<ApplyStudentLearningValidateResponse>> {
    return commonRequestApi<ApplyStudentLearningValidateResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请学习token校验(期数)
   * 非期数参训资格 = 60006
   * 非方案参训资格 = 60007
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyStudentLearningValidateInIssue(
    request: ApplyLearningTokenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyStudentLearningValidateInIssue,
    operation?: string
  ): Promise<Response<ApplyStudentLearningValidateResponse>> {
    return commonRequestApi<ApplyStudentLearningValidateResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 考核通过后的数据修复
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async passedAssessDataRepair(
    request: PassedAssessDataRepairRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.passedAssessDataRepair,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 参训人员证书成果撤回
   * @param studentNo
   * @param mutate 查询 graphql 语法文档
   * @param studentNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async revokeLearningResult(
    studentNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.revokeLearningResult,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { studentNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 参训人员数据修复接口
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async trainingQualificationDataRepair(
    request: TrainingQualificationDataRepairRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.trainingQualificationDataRepair,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 校验学员是否允许学习(支持校验 方案参训资格 期别参训资格)
   * 60015 - 期别未开始培训
   * 60012 - 期别已结束培训
   * 50006 - 班级正在开通中
   * 50007 - 班级正在退班中
   * 50002 - 方案培训未开始
   * 50003 - 方案培训已结束
   * 学员培训资格已冻结 = 60001
   * 学员培训资格已失效 = 60002
   * 重算未完成 = 60005
   * 存在未完成的强制调查问卷 = 60008
   * @param qualificationId:
   * <AUTHOR> By Cb
   * @since 2024/11/21 16:18
   * @param mutate 查询 graphql 语法文档
   * @param qualificationId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validStudentAllowLearning(
    qualificationId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validStudentAllowLearning,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { qualificationId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
