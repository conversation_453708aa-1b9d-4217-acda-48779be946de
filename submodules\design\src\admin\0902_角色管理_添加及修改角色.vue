<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">运营帐号管理</el-breadcrumb-item>
      <el-breadcrumb-item>新建角色</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-row :gutter="15" class="is-height">
        <el-col :md="11" :xl="10">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header">
              <span class="tit-txt">角色信息</span>
            </div>
            <div class="f-p30">
              <el-form ref="form" :model="form" label-width="auto" class="m-form">
                <el-form-item label="管理员类型：" required>
                  <el-select v-model="form.name" placeholder="请选择管理员类型" class="form-m"></el-select>
                </el-form-item>
                <el-form-item label="角色名称：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入角色名称" class="form-m" />
                </el-form-item>
                <el-form-item label="角色说明：">
                  <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入角色说明" />
                </el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-col>
        <el-col :md="13" :xl="14">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header">
              <span class="tit-txt">角色权限</span>
            </div>
            <div class="f-p30">
              <div class="m-authority f-pb20">
                <div class="selected-all">
                  <el-checkbox label="全选（超管）"></el-checkbox>
                </div>
                <el-tree
                  :data="data"
                  :props="defaultProps"
                  @node-click="handleNodeClick"
                  show-checkbox
                  class="m-tree-line is-border is-bg"
                ></el-tree>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button>取消</el-button>
        <el-button type="primary">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        activeNames: ['1'],
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        data: [
          {
            value: 'zhinan',
            label: '门户信息管理',
            disabled: false,
            children: [
              {
                value: 'yizhi',
                label: '资讯信息管理',
                disabled: false,
                children: [
                  {
                    value: 'yizhi1',
                    label: '修改',
                    disabled: false
                  },
                  {
                    value: 'fanku2i',
                    label: '查询',
                    disabled: false
                  },
                  {
                    value: 'xiaolv1',
                    label: '设为草稿',
                    disabled: false
                  },
                  {
                    value: 'keko2ng',
                    label: '发布',
                    disabled: false
                  },
                  {
                    value: 'keko22ng',
                    label: '新增',
                    disabled: false
                  },
                  {
                    value: 'keko2ng3',
                    label: '删除',
                    disabled: false
                  }
                ]
              },
              {
                value: 'fankui',
                label: '资讯分类管理',
                disabled: false,
                children: [
                  {
                    value: 'yiz1hi1',
                    label: '修改',
                    disabled: false
                  },
                  {
                    value: 'fanku21i',
                    label: '查询',
                    disabled: false
                  },
                  {
                    value: 'xiao1lv1',
                    label: '设为草稿',
                    disabled: false
                  },
                  {
                    value: 'kek1o2ng',
                    label: '发布',
                    disabled: false
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '工种管理',
            disabled: false,
            children: [
              {
                value: 'cexiangdaohang',
                label: '创建/编辑',
                disabled: false
              },
              {
                value: 'dingbudaohang',
                label: '上移',
                disabled: false
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '培训证明',
            disabled: false,
            children: [
              {
                value: 'cexiangdao2hang',
                label: '预览',
                disabled: false
              },
              {
                value: 'dingb2udaohang',
                label: '下载',
                disabled: false
              },
              {
                value: 'dingb2udaoh3ang',
                label: '查询',
                disabled: false
              }
            ]
          }
        ],
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        cascader1: [
          {
            value: 'zhinan',
            label: '门户信息管理',
            children: [
              {
                value: 'yizhi',
                label: '资讯信息管理',
                children: [
                  {
                    value: 'yizhi1',
                    label: '修改'
                  },
                  {
                    value: 'fanku2i',
                    label: '查询'
                  },
                  {
                    value: 'xiaolv1',
                    label: '设为草稿'
                  },
                  {
                    value: 'keko2ng',
                    label: '发布'
                  },
                  {
                    value: 'keko22ng',
                    label: '新增'
                  },
                  {
                    value: 'keko2ng3',
                    label: '删除'
                  }
                ]
              },
              {
                value: 'fankui',
                label: '资讯分类管理',
                children: [
                  {
                    value: 'yiz1hi1',
                    label: '修改'
                  },
                  {
                    value: 'fanku21i',
                    label: '查询'
                  },
                  {
                    value: 'xiao1lv1',
                    label: '设为草稿'
                  },
                  {
                    value: 'kek1o2ng',
                    label: '发布'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '工种管理',
            children: [
              {
                value: 'cexiangdaohang',
                label: '创建/编辑'
              },
              {
                value: 'dingbudaohang',
                label: '上移'
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '培训证明',
            children: [
              {
                value: 'cexiangdao2hang',
                label: '预览'
              },
              {
                value: 'dingb2udaohang',
                label: '下载'
              },
              {
                value: 'dingb2udaoh3ang',
                label: '查询'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleChange(val) {
        console.log(val)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
