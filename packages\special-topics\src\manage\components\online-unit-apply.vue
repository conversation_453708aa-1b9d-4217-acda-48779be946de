<template>
  <div>
    <el-card shadow="never" class="m-card f-mb15 is-bg">
      <div class="f-p15">
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form
              ref="onlineUnitApplyFormRef"
              :model="onlineCollectiveInfo"
              label-width="205px"
              class="m-form f-mt20"
            >
              <el-form-item label="专题线上集体报名入口：" required>
                <el-switch
                  v-model="onlineCollectiveInfo.onlineCollectiveEntry"
                  active-text="开启"
                  inactive-text="关闭"
                  class="m-switch"
                />
              </el-form-item>
              <template v-if="onlineCollectiveInfo.onlineCollectiveEntry">
                <el-form-item label="专题线上集体报名入口图片：" required>
                  <el-switch
                    active-text="开启"
                    v-model="onlineCollectiveInfo.onlineCollectiveEntryImage"
                    inactive-text="关闭"
                    class="m-switch"
                  />
                </el-form-item>
                <el-form-item
                  v-if="onlineCollectiveInfo.onlineCollectiveEntry && onlineCollectiveInfo.onlineCollectiveEntryImage"
                  label="专题线上集体报名入口图片："
                >
                  <cropper-img-upload
                    v-model="onlineCollectiveInfo.onlineCollectiveEntryImageLink"
                    :dialogStyleOpation="{
                      width: `${picSize[0]}px`,
                      height: `${picSize[1]}px`
                    }"
                    :ratioArr="[`${picSize[0]}:${picSize[1]}`]"
                    :initWidth="picSize[0]"
                    title="上传线上集体报名入口图片"
                    button-text="上传线上集体报名入口图片"
                    :is-long-pic="false"
                    reminder-text="只支持JPG、PNG、GIF"
                    :has-preview="false"
                    :mode="`${picSize[0]}px ${picSize[1]}px`"
                    :full="true"
                  >
                    <div slot="tip" class="el-upload__tip">
                      <i class="el-icon-warning"></i>
                      <span class="txt">
                        上传线上集体报名入口图片，只开放线上入口图片尺寸为{{
                          imgSize('singleOnlineCollectSize', 'width')
                        }}x{{ imgSize('singleOnlineCollectSize', 'height') }} px，线上入口和线下入口都开放则图片尺寸为{{
                          imgSize('doubleOnlineCollectSize', 'width')
                        }}x{{ imgSize('doubleOnlineCollectSize', 'height') }} px，不上传则显示模板默认图片
                        <i class="f-link" @click="handlePictureExample">示例图片</i>
                      </span>
                      <!--示例图片弹窗-->
                      <el-dialog :visible.sync="pictureVisible" width="640px" class="m-dialog-pic">
                        <!--分别读取对应的默认图片-->
                        <img :src="exampleImg" alt="" />
                      </el-dialog>
                    </div>
                  </cropper-img-upload>
                </el-form-item>
                <el-form-item label="线上集体报名模板：" required>
                  <min-upload-file v-model="hbFileUploadResponse" :file-type="1">
                    <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                      点击上传模板
                    </el-button>
                    <div class="el-upload__tip f-pt5">
                      <div class="txt"><i class="f-link" @click.stop="downloadTemplate">下载示例模版</i></div>
                    </div>
                  </min-upload-file>
                </el-form-item>
                <el-form-item label="查看报名班级链接：" required>
                  {{ onlineCollectiveInfo.signUpClassUrl }}
                  <hb-copy :content="onlineCollectiveInfo.signUpClassUrl"></hb-copy>
                </el-form-item>
              </template>
              <el-form-item class="m-btn-bar">
                <el-button @click="cancel">取 消</el-button>
                <el-button @click="validForm" type="primary" :loading="loading">保 存</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { CollectiveSignUpTypeEnum } from '@api/service/common/enums/online-school-config/CollectiveSignUpType'
  import TemplateItem from '@api/service/common/template-school/TemplateItem'
  import TemplateModule from '@api/service/common/template-school/TopicTemplateModule'
  import MutationCollectiveSignUp from '@api/service/management/online-school-config/functionality-setting/mutation/MutationCollectiveSignUp'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import OnlineCollectiveInfo from '@api/service/management/thematic-management/model/OnlineCollectiveInfo'
  import PortalInfo from '@api/service/management/thematic-management/model/PortalInfo'
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import CropperImgUpload from '@hbfe/jxjy-admin-platform/src/function/components/cropper-img-upload.vue'
  import MinUploadFile from '@hbfe/jxjy-admin-platform/src/function/components/min-upload-file.vue'
  import UnitApplyPicSizeTypeModel from '@hbfe/jxjy-admin-specialTopics/src/manage/vuex/UnitApplyPicSizeTypeModel'
  import { ElForm } from 'element-ui/types/form'
  import { bind, debounce } from 'lodash-decorators'
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  @Component({
    components: { CropperImgUpload, MinUploadFile }
  })
  export default class extends Vue {
    @Ref('onlineUnitApplyFormRef') onlineUnitApplyFormRef: ElForm
    /**
     * 表单数据
     */
    @Prop({
      type: Object,
      default: () => {
        return new ThematicManagementItem()
      }
    })
    thematicManagementItem: ThematicManagementItem
    @Watch('thematicManagementItem', { deep: true })
    thematicManagementItemChange(val: ThematicManagementItem) {
      this.onlineCollectiveInfo = this.thematicManagementItem.onlineCollectiveInfo
    }

    onlineCollectiveInfo: OnlineCollectiveInfo = new OnlineCollectiveInfo()
    mutationCollectiveSignUp: MutationCollectiveSignUp =
      OnlineSchoolConfigModule.mutationFunctionalitySettingFactory.collectiveSignUp(CollectiveSignUpTypeEnum.ONLINE)
    /**
     * 示例图片显示
     */
    pictureVisible = false
    /**
     * 专题模板
     */
    TemplateItem: TemplateItem
    /**
     * 文件上传之后的回调参数
     */
    hbFileUploadResponse = new HBFileUploadResponse()
    /**
     * 加载状态
     */
    loading = false
    /**
     * 门户信息类
     */
    @Prop({
      type: Object,
      default: () => {
        return new PortalInfo()
      }
    })
    portalInfo: PortalInfo
    /**
     * 模板webId
     */
    @Prop({
      type: String,
      default: ''
    })
    templateWeb: string

    /**
     * 获取图片尺寸
     */
    get picSize() {
      return UnitApplyPicSizeTypeModel.isSingle
        ? this.TemplateItem.singleOnlineCollectSize
        : this.TemplateItem.doubleOnlineCollectSize
    }
    /**
     * 示例图片
     */
    get exampleImg() {
      const src = UnitApplyPicSizeTypeModel.isSingle
        ? this.TemplateItem.singleOnlineExampleImgSrc
        : this.TemplateItem.doubleOnlineExampleImgSrc
      return require('@design/admin/assets/images/' + src)
    }
    @Watch('thematicManagementItem.onlineCollectiveInfo.onlineCollectiveEntry')
    onOnlineCollectiveEntryChange(val: boolean) {
      this.onlineCollectiveInfo.onlineCollectiveEntryImage = val
    }

    @Watch('thematicManagementItem.onlineCollectiveInfo.onlineCollectiveEntryImage')
    onEntryStatusChange(val: boolean) {
      UnitApplyPicSizeTypeModel.setOnlinePicEnable(val)
    }
    @Watch('templateWeb')
    templateWebChange(val: string) {
      this.TemplateItem = TemplateModule.getTemplate(val)
    }

    /**
     * 取消
     */
    cancel() {
      //TODO 需要删除编辑的数据
      this.$confirm('确定要放弃编辑吗？', '提示', {
        confirmButtonText: '确认',
        showCancelButton: true
      }).then(() => {
        this.$message.success('已取消本次编辑')
        this.$router.push({
          path: '/training/special-topics/manage'
        })
      })
    }
    /**
     * 示例图片
     */
    handlePictureExample() {
      this.pictureVisible = true
    }
    /**
     * @description 置换模板尺寸
     * @param key 尺寸key(参照 templateModule)
     * @param side 尺寸方向 width 宽 height 高
     * @return number
     * */
    get imgSize() {
      return (key: string, side: string) => {
        // todo
        const templateId = this.templateWeb
        const sizeObj = TemplateModule.getTemplate(templateId)
        const sideIndex = side == 'width' ? 0 : 1
        return sizeObj[key][sideIndex]
      }
    }

    /**
     * 下载示例模板
     */
    @bind
    @debounce(1000)
    async downloadTemplate() {
      const link = document.createElement('a')
      const templatePath = await this.mutationCollectiveSignUp.getTemplate()
      const resolver = this.$router.resolve({
        name: templatePath
      })

      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      const urlArr = link.href.split('.'),
        typeName = urlArr.pop()
      link.setAttribute('download', '集体报名表模板')
      document.body.appendChild(link)
      link.click()
      link.remove()
    }
    /**
     * 保存
     */
    validForm() {
      this.onlineUnitApplyFormRef.validate((valid: boolean) => {
        if (valid) {
          this.doSave()
        }
      })
    }
    @debounce(200)
    @bind
    async doSave() {
      if (this.onlineCollectiveInfo.onlineCollectiveEntry) {
        if (!this.hbFileUploadResponse.url || this.hbFileUploadResponse.url == '') {
          this.$message.warning('请上传线上集体报名模板')
          return
        }
      }
      if (this.hbFileUploadResponse.url) {
        const { url, fileName } = this.hbFileUploadResponse
        if (url && fileName) {
          this.onlineCollectiveInfo.onlineCollectiveEntryTemplate = {
            url,
            name: fileName
          }
        }
      }
      this.loading = true
      await this.saveOnlineCollectiveInfo()
      // const change = UnitApplyPicSizeTypeModel.isOnlinePicChange
      this.delayApply(async () => {
        await this.queryOnlineCollectiveInfo()
        this.loading = false
      }, 1000)
      // if (change) {
      //   this.$confirm('线下集体报名入口图片尺寸变更，是否立即前往调整？', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消'
      //   }).then(() => {
      //     this.$emit('update:activeName', 'offlineApply')
      //   })
      // }
    }
    /**
     * 保存线上集体报名配置
     */
    async saveOnlineCollectiveInfo() {
      UnitApplyPicSizeTypeModel.setOnlinePicEnable(
        this.thematicManagementItem.onlineCollectiveInfo.onlineCollectiveEntryImage
      )
      let status = null
      if (this.thematicManagementItem.onlineCollectiveInfo.id) {
        status = await this.thematicManagementItem.updateOnlineCollectiveInfo()
      } else {
        status = await this.thematicManagementItem.saveOnlineCollectiveInfo()
      }
      if (status.isSuccess()) {
        this.$message.success('保存成功')
      } else {
        this.$message.error('保存失败')
      }
    }
    /**
     * 查询线上集体报名配置
     */
    async queryOnlineCollectiveInfo() {
      const status = await this.thematicManagementItem.queryOnlineCollectiveInfo()
      if (!status.isSuccess()) {
        this.$message.error('查询线上集体报名配置失败')
      }
      //保存后要重新初始化
      UnitApplyPicSizeTypeModel.setOriginOnlinePicEnable(
        this.thematicManagementItem.onlineCollectiveInfo.onlineCollectiveEntryImage
      )
    }
    /**
     * 延时执行
     * @param fn 执行方法
     * @param time 延时时间
     */
    delayApply(fn: Function, time: number) {
      setTimeout(() => {
        fn.call(this)
      }, time)
    }
    created() {
      const pcTemplateId = this.templateWeb
      this.TemplateItem = TemplateModule.getTemplate(pcTemplateId)
      this.onlineCollectiveInfo = this.thematicManagementItem.onlineCollectiveInfo
      if (this.onlineCollectiveInfo && this.onlineCollectiveInfo.onlineCollectiveEntryTemplate) {
        this.hbFileUploadResponse = new HBFileUploadResponse()
        this.hbFileUploadResponse.url = this.onlineCollectiveInfo.onlineCollectiveEntryTemplate.url
        this.hbFileUploadResponse.fileName = this.onlineCollectiveInfo.onlineCollectiveEntryTemplate.name
      }
    }
  }
</script>
