/**
 * 课件信息
 */

import Mockjs from 'mockjs'

class Courseware {
  /**
   * 章节编号，对应标签编号
   */
  private id: string

  constructor() {
    this.id = Mockjs.Random.guid()
  }

  /**
   * 课件名称
   */
  name = ''

  /**
   * 课件时长
   */
  timeLength = 0

  /**
   * 所属课件目录编号
   */
  courseChapterId = ''

  /**
   * 课件类型，1表示文档，2表示视频，3表示多媒体
   */
  type: number

  /**
   * 挂在课件是否支持试听 0不可以试听，1可以试听，
   */
  trialType: number
}

export default Courseware
