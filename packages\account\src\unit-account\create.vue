<template>
  <el-main>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/basic-data/account/unit-account' }"> 单位管理员管理 </el-breadcrumb-item>
      <el-breadcrumb-item>添加帐号</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <!--基本信息 -->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基本信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="formRef" :rules="rules" :model="form" label-width="120px" class="m-form">
                <el-form-item label="帐号：" prop="account">
                  <el-input v-model="form.account" clearable placeholder="请输入帐号" class="form-m" />
                </el-form-item>
                <el-form-item label="姓名 / 昵称：" prop="name">
                  <el-input v-model="form.name" clearable placeholder="请输入姓名 / 昵称" class="form-m" />
                </el-form-item>
                <el-form-item label="手机号：" prop="phone">
                  <el-input v-model="form.phone" clearable placeholder="请输入手机号" class="form-m" />
                </el-form-item>
                <el-form-item label="所属单位：" prop="phone">
                  <el-input v-model="form.unitName" clearable placeholder="请输入所属单位" class="form-m" />
                </el-form-item>
                <el-form-item label="启用状态：" required>
                  <el-radio-group v-model="form.status">
                    <el-radio :label="UtilAdministratorStatusEnum.enable">启用</el-radio>
                    <el-radio :label="UtilAdministratorStatusEnum.disable">禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item>
                  <span class="f-co">注：初始密码为“dwgly123”</span>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">主网校数据权限</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="formRef" :rules="rules" :model="form" class="m-form">
                <el-form-item label="主网校数据权限：" prop="mainSchoolDataPermission">
                  <el-switch v-model="form.mainSchoolDataPermission"> </el-switch
                ></el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">专题数据权限</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="formRef" :rules="rules" :model="form" class="m-form">
                <el-form-item label="专题数据权限：" prop="thematicDataPermission">
                  <el-switch v-model="form.thematicDataPermission"> </el-switch>
                </el-form-item>
                <el-form-item label="专题：" prop="thematicScope">
                  <el-radio
                    v-for="item in thematicScopeTypeEnumList"
                    v-model="form.thematicScope"
                    :label="item.code"
                    :key="item.code"
                    >{{ item.desc }}</el-radio
                  >
                  <el-button type="primary" v-if="form.thematicScope === ThematicScopeTypeEnum.specify"
                    >选择专题</el-button
                  >
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <!--表格-->
          <el-table
            ref="topicsListRef"
            :data="form.thematicList"
            max-height="500px"
            class="m-table"
            v-if="form.thematicList.length"
          >
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="专题名称" min-width="150">
              <template slot-scope="scope">{{ scope.row.basicInfo.subjectName }}</template>
            </el-table-column>
            <el-table-column label="专题类型" min-width="150">
              <template slot-scope="scope">
                <el-tooltip placement="top" effect="light" v-if="scope.row.basicInfo.suiteIndustry">
                  <div slot="content">
                    <i class="f-c4">
                      <el-tag type="warning" size="mini" class="f-mr5">行业</el-tag
                      >{{ scope.row.basicInfo.suiteIndustry }}</i
                    >
                  </div>
                  <el-button type="text" class="f-to-three">
                    <i class="f-c4">
                      <el-tag type="warning" size="mini" class="f-mt5"> 行业 </el-tag>
                      {{ scope.row.basicInfo.suiteIndustry }}</i
                    >
                  </el-button>
                </el-tooltip>
                <el-tooltip placement="top" effect="light" v-if="scope.row.basicInfo.suiteArea">
                  <div slot="content">
                    <i class="f-c4"
                      ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag
                      >{{ scope.row.basicInfo.suiteArea }}</i
                    >
                  </div>
                  <el-button type="text" class="f-to-three"
                    ><i class="f-c4"
                      ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag
                      >{{ scope.row.basicInfo.suiteArea }}</i
                    ></el-button
                  >
                </el-tooltip>
                <el-tooltip placement="top" effect="light" v-if="scope.row.basicInfo.unitName">
                  <div slot="content">
                    <i class="f-c4">
                      <el-tag type="warning" size="mini" class="f-mr5">单位</el-tag
                      >{{ scope.row.basicInfo.unitName }}</i
                    >
                  </div>
                  <el-button type="text" class="f-to-three">
                    <i class="f-c4">
                      <el-tag type="warning" size="mini" class="f-mt5"> 单位 </el-tag>
                      {{ scope.row.basicInfo.unitName }}</i
                    >
                  </el-button>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="状态" min-width="100">
              <template slot-scope="scope">
                <div v-if="scope.row.enable">
                  <el-badge is-dot type="success" class="badge-status">启用</el-badge>
                </div>
                <div v-else>
                  <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="text"> 取消选择 </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-empty :image-size="40" description="暂无数据，请添加专题~" v-if="!form.thematicList.length" />
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">分销商数据权限</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="formRef" :rules="rules" :model="form" class="m-form">
                <el-form-item label="分销商数据权限：" prop="distributorDataPermission">
                  <el-switch v-model="form.distributorDataPermission"> </el-switch>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="goBack">放弃编辑</el-button>
        <el-button :loading="loading" type="primary" @click="save()">保存</el-button>
      </div>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import ThematicAdministratorItem from '@api/service/management/user/unit-administrator/model/ThematicAdministratorItem'
  import UtilAdministratorDetailItem from '@api/service/management/user/unit-administrator/UnitAdministratorDetail'
  import { UtilAdministratorStatusEnum } from '@api/service/management/user/unit-administrator/enum/UtilAdministratorStatusEnum'
  import ThematicScopeType, {
    ThematicScopeTypeEnum
  } from '@api/service/management/user/unit-administrator/enum/ThematicScopeTypeEnum'
  @Component
  export default class extends Vue {
    /**
     * loading
     */
    loading = false

    /**
     * 创建模型
     */
    form: UtilAdministratorDetailItem = new UtilAdministratorDetailItem()

    /**
     * 专题列表
     */
    thematicList: Array<ThematicAdministratorItem> = []

    /**
     * 校验规则
     */
    rules = {
      name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
      account: [{ required: true, message: '请输入帐号', trigger: 'blur' }],
      phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
      unitName: [{ required: true, message: '请选择所属单位', trigger: 'blur' }],
      status: [{ required: true, message: '请选择状态', trigger: 'change' }],
      thematicScope: [{ required: true, trigger: 'change' }]
    }

    /**
     * 枚举
     */
    UtilAdministratorStatusEnum = UtilAdministratorStatusEnum
    ThematicScopeTypeEnum = ThematicScopeTypeEnum
    thematicScopeTypeEnumList = new ThematicScopeType().list()

    /**
     * 保存
     */
    save() {
      //
    }

    /**
     * 放弃 编辑
     */
    goBack() {
      this.$router.back()
    }
  }
</script>
