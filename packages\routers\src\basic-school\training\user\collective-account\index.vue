<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: Lin yt
 * @Date: 2022-02-15 16:01:36
 * @LastEditors: <PERSON> yt
 * @LastEditTime: 2022-06-14 10:14:41
-->
<route-meta>
{
"isMenu": true,
"title": "集体报名账号管理",
"sort": 2,
"icon": "icon_guanli"
}
</route-meta>
<script lang="ts">
  import CollectiveAccount from '@hbfe/jxjy-admin-user/src/collective-account/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    groupRegistrationManagement: [WXGLY]
  })
  export default class extends CollectiveAccount {}
</script>
