import { SubjectType } from '@api/service/management/thematic-management/enum/SubjectType'
import { TrainingChannelRequest } from '@api/platform-gateway/platform-training-channel-back-gateway'
import { TrainingChannelRequest as TrainingChannelRequestv2 } from '@api/platform-gateway/jxjy-data-export-gateway-backstage'

export default class ThematicManagementQueryParam {
  /**
   * 专题入口名称
   */
  entryName = ''
  /**
   * 专题名称
   */
  subjectName = ''
  /**
   * 专题类型
   */
  subjectType: Array<SubjectType> = new Array<SubjectType>()
  /**
   * 地区
   * 级联选择器数组
   */
  suiteAreaLists: string[] = []
  /**
   * 行业
   */
  suiteIndustry = ''
  /**
   * 单位名称
   */
  unitName = ''
  /**
   * 专题状态
   */
  enable: boolean = undefined
  /**
   * 是否显示在网校
   */
  displayInSchool: false = undefined
  /**
   * 修改开始时间
   */
  modifyStartTime = ''
  /**
   * 修改结束时间
   */
  modifyEndTime = ''

  /**
   * 用户id
   */
  userIds: Array<string> = new Array<string>()

  static to(vo: ThematicManagementQueryParam) {
    const dto = new TrainingChannelRequest()
    ThematicManagementQueryParam.toParams(vo, dto)
    return dto
  }
  static toExport(vo: ThematicManagementQueryParam) {
    const dto = new TrainingChannelRequestv2()
    ThematicManagementQueryParam.toParams(vo, dto)
    return dto
  }

  static toParams(vo: ThematicManagementQueryParam, dto: TrainingChannelRequest | TrainingChannelRequestv2) {
    dto.entryName = vo.entryName
    dto.name = vo.subjectName
    dto.types = vo.subjectType
    dto.industryId = vo.suiteIndustry || undefined
    dto.unitName = vo.unitName
    dto.regionPath = vo.suiteAreaLists?.length ? vo.suiteAreaLists[vo.suiteAreaLists.length - 1] : undefined
    dto.enable = vo.enable
    dto.showOnNetSchool = vo.displayInSchool
    dto.createdDateScope = {
      beginTime: vo.modifyStartTime,
      endTime: vo.modifyEndTime
    }
    if (vo.userIds.length) {
      dto.userIdList = vo.userIds
    }
    return dto
  }
}
