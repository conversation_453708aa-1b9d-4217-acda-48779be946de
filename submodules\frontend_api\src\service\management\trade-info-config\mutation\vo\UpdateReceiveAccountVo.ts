import { CreateReceiveAccountRequest } from '@api/ms-gateway/ms-trade-configuration-v1'
import { ReceiveAccountConfigResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { AccountTypeEunm } from '@api/service/common/enums/trade-configuration/AccountType'
import MutationReceiveAccountVo from './MutationReceiveAccountVo'

class UpdateReceiveAccountVo {
  id = ''
  /**
   * 支付方式 1-线上 2-线下
   */
  accountType: AccountTypeEunm = AccountTypeEunm.ONLINE
  /**
   * 收款账号(开户号、商户号、支付宝账号)
   */
  accountNo = ''
  /**
   * 收款账号别名
   */
  accountName = ''
  /**
   * 纳税人识别号
   */
  taxPayerId = ''
  /**
   * 退款方式  1-线上 2-线下
   */
  refundWay = -1
  /**
   * 支付账号类型id
   * 培训券，对接众智汇云培训券:TRAINING_VOUCHER
   * 支付宝:ALIPAY
   * 微信：WXPAY
   * 导入开通：NO_PAYMENT
   */
  paymentChannelId = ''

  /**
   * 付款扫码引导语
   */
  qrScanPrompt = ''

  to(): CreateReceiveAccountRequest {
    return
  }

  from(res: ReceiveAccountConfigResponse) {
    return
  }
}

export default UpdateReceiveAccountVo
