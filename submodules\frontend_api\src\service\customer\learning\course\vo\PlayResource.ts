import { VideoResourceResponse } from '@api/ms-gateway/ms-course-play-resource-v1'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import ResourceTypeEnum from '@api/service/customer/learning/course/enums/ResourceTypeEnum'

class PlayResource {
  quality: number
  url: string

  static from(resource: VideoResourceResponse): PlayResource {
    const playResource = new PlayResource()
    if (resource.resourceType === ResourceTypeEnum.type_huawei_cloud_video) {
      playResource.url = `${window.location.origin}${resource.path}`
    } else if (resource.resourceType === ResourceTypeEnum.type_huawei_cloud_audio) {
      playResource.url = `${window.location.origin}${resource.path}`
    } else {
      playResource.url = resource.path
    }
    playResource.quality = resource.clarity
    return playResource
  }
}

export default PlayResource
