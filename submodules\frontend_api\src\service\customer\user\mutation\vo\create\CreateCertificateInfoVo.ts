/**
 * 证书信息
 */

import { CertificateInfo } from '@api/ms-gateway/ms-account-v1'
import CreateCertificateAttachmentVo from './CreateCertificateAttachmentVo'

class CreateCertificateInfoVo extends CertificateInfo {
  /**
   * 证书id
   */
  certificateId = ''
  /**
   * 证书编号
   */
  certificateNo = ''
  /**
   * 证书类别
   */
  certificateCategory = ''
  /**
   * 注册专业
   */
  registerProfessional?: string = ''
  /**
   * 发证日期（起）
   */
  releaseStartTime = ''
  /**
   * 证书有效期（止）
   */
  certificateEndTime = ''
  /**
   * 证书附件
   */
  certificateAttachments?: Array<CreateCertificateAttachmentVo> = new Array<CreateCertificateAttachmentVo>()
}

export default CreateCertificateInfoVo
