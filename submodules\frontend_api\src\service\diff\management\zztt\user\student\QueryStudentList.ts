import MsBasicDataQueryBackstageGateway, {
  StudentInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { Response, Page } from '@hbfe/common'
import StudentQueryIdVo from '@api/service/management/user/query/student/vo/StudentQueryIdVo'
import TradeModule from '@api/service/management/trade/TradeModule'
import DataExportBackstage from '@api/diff-gateway/zztt-data-export-gateway-backstage'
import StudentQueryVo from '@api/service/diff/management/zztt/user/student/model/StudentQueryVo'
import StudentUserInfoVo from '@api/service/diff/management/zztt/user/student/model/StudentUserInfoVo'

/**
 * 查询用户列表
 */
class QueryStudentList {
  // 查询学员id集合入参
  queryStudentIdParams = new StudentQueryIdVo()

  /**
   * @description: 根据筛选条件查询用户信息列表
   * @param {Page} page
   * @param {StudentQueryVo} params
   */
  async queryStudentListByCondition(page: Page, params: StudentQueryVo): Promise<Response<Array<StudentUserInfoVo>>> {
    const response = new Response<Array<StudentUserInfoVo>>()
    // 存在订单号需要装换为用户id
    if (params.orderNo) {
      const userId = await TradeModule.singleTradeBatchFactor.orderFactor.queryOrder.queryBuyerIdByOrderNo(
        params.orderNo.trim()
      )
      if (!userId) {
        console.error('订单号查询用户ID失败！')
        return
      }
      params.userId = userId
    }
    const res = await MsBasicDataQueryBackstageGateway.pageStudentInfoInServicer({
      page: page,
      request: params.toDto()
    })
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    page.pageSize = res.data?.pageSize
    page.totalSize = res.data?.totalSize
    response.status = res.status
    response.data = new Array<StudentUserInfoVo>()
    res.data?.currentPageData?.map((item: StudentInfoResponse) => {
      const temp = new StudentUserInfoVo()
      temp.from(item)
      response.data.push(temp)
    })
    return response
  }

  /**
   * 导出学员
   */
  async export(params: StudentQueryVo) {
    const response = await DataExportBackstage.exportStudentExcelInServicer(params.toExport())
    return response
  }
}

export default QueryStudentList
