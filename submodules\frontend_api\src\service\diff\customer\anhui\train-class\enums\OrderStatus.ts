import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * @description 培训方案类型枚举
 * CHOOSE_COURSE_LEARNING - 选课规则
 * AUTONOMOUS_COURSE_LEARNING - 自主选课
 */
export enum OrderStatusEnum {
  /**
   * 未支付
   */
  none = '0',
  /**
   * 支付中
   */
  paying = '1',
  /**
   * 已支付
   */
  paid = '2'
}
class OrderStatus extends AbstractEnum<OrderStatusEnum> {
  static enum = OrderStatusEnum
  constructor(status?: OrderStatusEnum) {
    super()
    this.current = status
    this.map.set(OrderStatusEnum.none, '未支付')
    this.map.set(OrderStatusEnum.paying, '支付中')
    this.map.set(OrderStatusEnum.paid, '已支付')
  }
}

export default OrderStatus
