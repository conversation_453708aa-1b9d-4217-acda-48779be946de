/**
 * 培训方案内的带教师信息的课程
 */
import TeacherInfo from './TeacherInfo'
import { PreExamLSCourseDTO } from '@api/gateway/PlatformTrade'

export default class LsCourseWithTeacher {
  /**
   * 课程ID
   */
  id: string

  /**
   * 课程名称
   */
  name: string

  /**
   * 封面图片路径
   */
  iconPath: string

  /**
   * 权重,表示学时,学分等
   */
  period: number

  /**
   * 课程简介
   */
  abouts: string
  /**
   * 课程类型：1/2，必修/选修
   */
  coursePoolType: number
  /**
   * 所属课程包编号
   */
  poolId: string
  /**
   * 所属课程包名称
   */
  poolName: string
  /**
   * 课程内讲师
   */
  tearcherList: Array<TeacherInfo>

  static from(dto: PreExamLSCourseDTO): LsCourseWithTeacher {
    const info = new LsCourseWithTeacher()
    info.id = dto.id
    info.name = dto.name
    info.iconPath = dto.iconPath
    info.period = dto.period
    info.abouts = dto.abouts
    info.coursePoolType = dto.courseType
    info.poolId = dto.poolId
    info.poolName = dto.poolName
    info.tearcherList = new Array<TeacherInfo>()
    dto.teacherList.forEach(teacher => {
      info.tearcherList.push(TeacherInfo.from(teacher))
    })
    return info
  }
}
