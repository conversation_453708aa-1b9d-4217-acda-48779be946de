<template>
  <el-drawer
    title="学习数据异常管理"
    :visible.sync="openDialog"
    size="85%"
    :append-to-body="true"
    custom-class="m-drawer m-table-auto"
  >
    <div class="drawer-bd">
      <el-tabs v-model="activeName" type="card" class="m-tab-card">
         <template
        v-if="$hasPermission('learnAnomalousList')"
        query
        desc="学习数据异常管理-学习规则"
        actions="@learnAnomalousList"
      >
        <el-tab-pane v-if="isShowLearningRulesTab" :label="rulseErrorTotalValue" name="learnRules">
          <learn-anomalous-list
            ref="learnAnomalousRef"
            @offDialog="offDialog()"
            @getStatistics="getLearningAnomalousCount"
          ></learn-anomalous-list>
        </el-tab-pane>
         </template>
        <template
          v-if="$hasPermission('intelligentLearningList')"
          query
          desc="学习数据异常管理-智能学习"
          actions="@intelligentLearningList"
        >
        <el-tab-pane v-if="isShowSmartLearningTab" :label="learnErrorTotalValue" name="intelligentLearning">
          <intelligent-learning-list ref="intelligentLearningListRef"></intelligent-learning-list>
        </el-tab-pane>
        </template>
      </el-tabs>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import learnAnomalousList from '@hbfe/jxjy-admin-learningStatistic/src/__components__/learn-anomalous-list.vue'
  import intelligentLearningList from '@hbfe/jxjy-admin-learningStatistic/src/__components__/intelligent-learning-list.vue'
  @Component({
    components: { learnAnomalousList, intelligentLearningList }
  })
  export default class extends Vue {
    @Ref('learnAnomalousRef') learnAnomalousRef: learnAnomalousList
    @Ref('intelligentLearningListRef') intelligentLearningListRef: intelligentLearningList
    // 智能学习异常总数
    @Prop({
      type: Number,
      default: 0
    })
    learnAnomalousCount: string
    // 学习规则异常总数
    @Prop({
      type: Number,
      default: 0
    })
    ruleAnomalousCount: number
    // 学习规则tab显隐
    @Prop({
      type: Boolean,
      default: false
    })
    isShowLearningRulesTab: boolean
    @Watch('learnAnomalousCount')
    @Watch('ruleAnomalousCount')
    initTotal() {
      this.errorTotal()
    }

    // 智能学习tab显隐
    @Prop({
      type: Boolean,
      default: false
    })
    isShowSmartLearningTab: boolean
    openDialog = false
    activeName = 'learnRules'
    // 学习规则异常总数
    rulseErrorTotalValue = ''
    // 智能学习异常总数
    learnErrorTotalValue = ''
    // 学习规则异常个数（随筛选条件变化）
    rulseErrorValue = 0
    // 智能学习异常个数（随筛选条件变化）
    learnErrorValue = 0
    init() {
      this.openDialog = true
      this.errorTotal()
    }

    errorTotal() {
      this.learnErrorTotalValue =
        '智能学习（' + (this.learnErrorValue ? this.learnErrorValue : this.learnAnomalousCount) + '）'
      this.rulseErrorTotalValue =
        '学习规则（' + (this.rulseErrorValue ? this.rulseErrorValue : this.ruleAnomalousCount) + '）'
    }

    offDialog() {
      this.openDialog = false
    }
    // 获取数据异常管理数量
    async getLearningAnomalousCount(ruleAnomalousCount: number) {
      this.rulseErrorValue = ruleAnomalousCount
      this.errorTotal()
      this.$emit('getStatistics')
    }
  }
</script>
