import PrintCertificateRequestVo from '@api/service/centre/train-class/mutation/vo/PrintCertificateRequestVo'
import { Response } from '@hbfe/common'
import Cdertificate, {
  CertificatePrintRequest,
  CheckPrintConditionResponse
} from '@api/platform-gateway/platform-certificate-v1'

/**
 * @description
 */

class MutationSignUpRecord {
  printParams: PrintCertificateRequestVo = new PrintCertificateRequestVo()

  /**
   * 打印培训证明
   */
  async doPrint(): Promise<Response<CheckPrintConditionResponse>> {
    const params = new CertificatePrintRequest()
    params.fileType = this.printParams.fileType
    params.qualificationId = this.printParams.qualificationId
    params.studentNo = this.printParams.studentNo
    const response = await Cdertificate.printCertificate(this.printParams)
    const result = new Response<CheckPrintConditionResponse>()
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
}

export default MutationSignUpRecord
