import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-identity-authentication-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-identity-authentication-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class BusinessDomainIdentity {
  applicationType: number
  applicationMemberType: number
  applicationMemberId?: string
}

export class PlatformIdentity {
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
}

/**
 * 账户信息
<AUTHOR>
 */
export class AccountInfo {
  accountId?: string
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  unitId?: string
  servicerId?: string
}

/**
 * @author: xucenhao
@time: 2024-10-09
@description:
 */
export class ImageCaptchaTrack {
  /**
   * 背景图片宽度.
   */
  bgImageWidth?: number
  /**
   * 背景图片高度.
   */
  bgImageHeight?: number
  /**
   * 滑块图片宽度.
   */
  sliderImageWidth?: number
  /**
   * 滑块图片高度.
   */
  sliderImageHeight?: number
  /**
   * 滑动开始时间.
   */
  startSlidingTime?: string
  /**
   * 滑动结束时间.
   */
  endSlidingTime?: string
  /**
   * 滑动的轨迹.
   */
  trackList?: Array<Track>
}

export class Track {
  /**
   * x.
   */
  x?: number
  /**
   * y.
   */
  y?: number
  /**
   * 时间.
   */
  t?: number
  /**
   * 类型.
   */
  type?: string
}

/**
 * 账户ID-身份认证凭据
<AUTHOR>
 */
export class AccountIdCredentialRequest {
  /**
   * 初始token
   */
  token: string
  /**
   * 授权类型
@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
identity_auth_token:身份凭证Token(默认授权类型)
identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
   */
  grantType?: string
}

/**
 * 账户密码图形验证码-身份认证凭据
<AUTHOR>
 */
export class AccountPwdCaptchaCredentialForFXSRequest {
  /**
   * 图形验证码
   */
  captchaValue: string
  /**
   * 验证验证码数据
   */
  verifyCaptchaData?: ImageCaptchaTrack
  /**
   * 认证标识
   */
  identity: string
  /**
   * 密码
   */
  password: string
  /**
   * 初始token
   */
  token: string
  /**
   * 授权类型
@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
identity_auth_token:身份凭证Token(默认授权类型)
identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
   */
  grantType?: string
}

/**
 * 账户密码图形验证码-身份认证凭据
<AUTHOR>
 */
export class AccountPwdCaptchaCredentialRequest {
  /**
   * 图形验证码
   */
  captchaValue: string
  /**
   * 验证验证码数据
   */
  verifyCaptchaData?: ImageCaptchaTrack
  /**
   * 认证标识
   */
  identity: string
  /**
   * 密码
   */
  password: string
  /**
   * 初始token
   */
  token: string
  /**
   * 授权类型
@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
identity_auth_token:身份凭证Token(默认授权类型)
identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
   */
  grantType?: string
}

/**
 * 账户密码-身份认证凭据
<AUTHOR>
 */
export class AccountPwdCredentialRequest {
  /**
   * 认证标识
   */
  identity: string
  /**
   * 密码
   */
  password: string
  /**
   * 初始token
   */
  token: string
  /**
   * 授权类型
@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
identity_auth_token:身份凭证Token(默认授权类型)
identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
   */
  grantType?: string
}

/**
 * 切换业务域-身份认证凭据
<AUTHOR>
 */
export class ChangeBusinessDomainCredentialRequest {
  /**
   * 应用方类型【如单位：5，服务商：6】，表示用于切换身份所在的业务域类型
   */
  applicationMemberType: number
  /**
   * 应用方ID（主体ID，如单位ID、服务商ID）
   */
  applicationMemberId: string
}

/**
 * 代理身份凭据请求
<AUTHOR>
 */
export class ProxyIdentityAccessSecureCredentialRequest {
  /**
   * 访问令牌
   */
  accessToken: string
  /**
   * 访问的代理业务域标识(需要安全进入的目标代理业务域)
   */
  accessProxyBusinessDomainIdentity: BusinessDomainIdentity
  /**
   * 代理业务域信息
   */
  proxyBusinessDomain: ProxyBusinessDomain
}

/**
 * 代理身份凭据请求
<AUTHOR>
 */
export class ProxyIdentityCredentialRequest {
  /**
   * 代理业务域信息
   */
  proxyBusinessDomain: ProxyBusinessDomain
}

/**
 * 再认证凭据-身份认证凭据
当前再认证平台需要自动进入的应用域初始token {@link CredentialRequest#getToken()}
<AUTHOR>
 */
export class ReAuthenticateCredentialRequest {
  /**
   * 初始token
   */
  token: string
  /**
   * 授权类型
@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
identity_auth_token:身份凭证Token(默认授权类型)
identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
   */
  grantType?: string
}

/**
 * 再认证根凭据-身份认证凭据
<AUTHOR>
 */
export class RgtCredentialRequest {
  /**
   * 根令牌凭据Token
   */
  rgtCredentialToken: string
  /**
   * 绑定的账户
   */
  boundAccount?: AccountInfo
  /**
   * 初始token
   */
  token: string
  /**
   * 授权类型
@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
identity_auth_token:身份凭证Token(默认授权类型)
identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
   */
  grantType?: string
}

/**
 * 手机验证码-身份认证凭据
<AUTHOR>
 */
export class SmsCodeCredentialForFXSRequest {
  /**
   * 手机号
   */
  phone: string
  /**
   * 短信验证码
   */
  smsCode: string
  /**
   * 初始token
   */
  token: string
  /**
   * 授权类型
@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
identity_auth_token:身份凭证Token(默认授权类型)
identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
   */
  grantType?: string
}

/**
 * 手机验证码-身份认证凭据
<AUTHOR>
 */
export class SmsCodeCredentialRequest {
  /**
   * 手机号
   */
  phone: string
  /**
   * 短信验证码
   */
  smsCode: string
  /**
   * 初始token
   */
  token: string
  /**
   * 授权类型
@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
identity_auth_token:身份凭证Token(默认授权类型)
identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
   */
  grantType?: string
}

/**
 * 代理业务域信息
<AUTHOR>
 */
export class ProxyBusinessDomain {
  /**
   * 代理平台标识
   */
  platformIdentity?: PlatformIdentity
  /**
   * 代理业务域标识
applicationType 应用类型
applicationMemberType 应用方类型 4:子项目、5:单位、6:服务商
@see com.fjhb.domain.basicdata.api.consts.ApplicationTypes
@see com.fjhb.domain.basicdata.api.consts.SystemMemberTypes
   */
  businessDomainIdentity?: BusinessDomainIdentity
}

/**
 * 第三方身份凭据请求
<AUTHOR>
 */
export class ThirdPartyIdentityCredentialRequest {
  /**
   * 第三方授权流程产生的token
   */
  token: string
}

export class BusinessDomainIdentity1 {
  applicationType: number
  applicationMemberType: number
  applicationMemberId: string
}

export class PlatformIdentity1 {
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
}

/**
 * 账户信息
<AUTHOR>
 */
export class AccountInfo1 {
  accountId: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  servicerId: string
}

/**
 * 代理业务域信息
<AUTHOR>
 */
export class ProxyBusinessDomain1 {
  /**
   * 代理平台标识
   */
  platformIdentity: PlatformIdentity1
  /**
   * 代理业务域标识
applicationType 应用类型
applicationMemberType 应用方类型 4:子项目、5:单位、6:服务商
@see com.fjhb.domain.basicdata.api.consts.ApplicationTypes
@see com.fjhb.domain.basicdata.api.consts.SystemMemberTypes
   */
  businessDomainIdentity: BusinessDomainIdentity1
}

/**
 * 代理业务域响应
<AUTHOR>
 */
export class AccessTokenResponse {
  authentication: string
  /**
   * 状态码
@see IdentityAuthenticationStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * 绑定账户响应
<AUTHOR>
 */
export class BoundAccountResponse {
  /**
   * 绑定的账户信息
   */
  boundAccounts: Array<AccountInfo1>
  /**
   * 状态码
@see IdentityAuthenticationStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * 身份认证统一响应
<AUTHOR>
 */
export class IdentityAuthenticationTokenResponse {
  /**
   * 根据授权模式不同，产生的token不同 {@link com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes}
   */
  identityAuthenticationToken: string
  /**
   * Token元数据
   */
  tokenMetadata: TokenMetadata
  /**
   * 安全元数据
   */
  securityMetadata: SecurityMetadata
  /**
   * 状态码
@see IdentityAuthenticationStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * 代理业务域响应
<AUTHOR>
 */
export class ProxyBusinessDomainResponse {
  /**
   * 绑定的代理业务域信息
   */
  boundProxyBusinessDomains: Array<ProxyBusinessDomain1>
  /**
   * 状态码
@see IdentityAuthenticationStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * Token元数据
<AUTHOR>
 */
export class TokenMetadata {
  /**
   * accountId:用户身份凭证的账户ID
   */
  accountId: string
  /**
   * userId:用户身份凭证的用户ID
   */
  userId: string
  /**
   * phone:用户身份凭证的手机号(脱敏)
   */
  phone: string
}

/**
 * 失败认证元数据
<AUTHOR>
 */
export class FailedAuthMetadata {
  /**
   * 失败上限次数
   */
  failedAuthAttemptUpperLimit: number
  /**
   * 已失败次数
   */
  failedAuthAttemptLimit: number
  /**
   * 账户是否被锁定
   */
  accountLock: boolean
  /**
   * 账户解锁时效(秒)
   */
  accountUnLockExpire: number
  /**
   * 账户解锁时间(完整时间戳)
   */
  accountUnLockTime: number
}

/**
 * 非安全（危险）信息
<AUTHOR>
 */
export class NonSecurityInfo {
  /**
   * 更换密码认证元数据
   */
  passwordChangeAuthMetadata: PasswordChangeAuthMetadata
  /**
   * 失败认证元数据
   */
  failedAuthMetadata: FailedAuthMetadata
}

/**
 * 更换密码认证元数据
<AUTHOR>
 */
export class PasswordChangeAuthMetadata {
  /**
   * 是否需要强制更换密码
   */
  forcedChangePassword: boolean
  /**
   * 更换密码周期（天）
   */
  passwordChangeCycle: number
}

/**
 * 风险信息
<AUTHOR>
 */
export class RiskInfo {
  /**
   * 更换密码认证元数据
   */
  passwordChangeAuthMetadata: PasswordChangeAuthMetadata
}

/**
 * 安全元数据
<AUTHOR>
 */
export class SecurityMetadata {
  /**
   * 安全级别
   */
  securityLevel: number
  /**
   * 风险信息
   */
  riskInfo: RiskInfo
  /**
   * 非安全（危险）信息
   */
  nonSecurityInfo: NonSecurityInfo
}

/**
 * 第三方身份凭证响应
<AUTHOR>
 */
export class ThirdPartyIdentityAuthTokenResponse {
  /**
   * 元数据
   */
  metadata: string
  /**
   * 根据授权模式不同，产生的token不同 {@link com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes}
   */
  identityAuthenticationToken: string
  /**
   * Token元数据
   */
  tokenMetadata: TokenMetadata
  /**
   * 安全元数据
   */
  securityMetadata: SecurityMetadata
  /**
   * 状态码
@see IdentityAuthenticationStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param accessToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getAccessTokenValue(
    accessToken: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAccessTokenValue,
    operation?: string
  ): Promise<Response<AccessTokenResponse>> {
    return commonRequestApi<AccessTokenResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { accessToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 返回当前平台绑定的账户信息(支持再认证单点)
   * @param credential {token,rgtCredentialToken}
   * @return {boundAccounts}
   * @param query 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBoundAccountsForReAuthenticate(
    credential: RgtCredentialRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBoundAccountsForReAuthenticate,
    operation?: string
  ): Promise<Response<BoundAccountResponse>> {
    return commonRequestApi<BoundAccountResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 返回当前平台业务域绑定的代理平台业务域信息
   * @param request {proxyBusinessDomain}
   * @return {boundProxyBusinessDomains}
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBoundProxyBusinessDomains(
    request: ProxyIdentityCredentialRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBoundProxyBusinessDomains,
    operation?: string
  ): Promise<Response<ProxyBusinessDomainResponse>> {
    return commonRequestApi<ProxyBusinessDomainResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 使用账户ID凭据申请获取身份凭证
   * @param credential {token,accountId}
   * @return {code,message,identityAuthenticationToken}
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyAuthenticateByAccountId(
    credential: AccountIdCredentialRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyAuthenticateByAccountId,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 使用账户密码凭据申请获取身份凭证
   * @param credential {token,identity,password}
   * @return {code,message,identityAuthenticationToken}
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyAuthenticateByAccountPwd(
    credential: AccountPwdCredentialRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyAuthenticateByAccountPwd,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 使用账户密码图形验证码凭据申请获取身份凭证
   * @param credential {token,identity,password,captchaValue,grantType}
   * @return {code,message,identityAuthenticationToken}
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyAuthenticateByAccountPwdCaptcha(
    credential: AccountPwdCaptchaCredentialRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyAuthenticateByAccountPwdCaptcha,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 使用账户密码图形验证码凭据申请获取身份凭证【分销商专用】
   * @param credential {token,identity,password,captchaValue}
   * @return {code,message,identityAuthenticationToken}
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyAuthenticateByAccountPwdCaptchaForFXS(
    credential: AccountPwdCaptchaCredentialForFXSRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyAuthenticateByAccountPwdCaptchaForFXS,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 使用账户密码图形验证码凭据申请获取身份凭证（分销商管理员专用临时接口，后续删除）
   * @param credential {token,identity,password,captchaValue}
   * @return {code,message,identityAuthenticationToken}
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyAuthenticateByAccountPwdCaptchaTempForFXSAdmin(
    credential: AccountPwdCaptchaCredentialForFXSRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyAuthenticateByAccountPwdCaptchaTempForFXSAdmin,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 使用账户密码图形验证码凭据申请获取身份凭证（分销商推广门户专用临时接口，后续删除）
   * @param credential {token,identity,password,captchaValue,grantType}
   * @return {code,message,identityAuthenticationToken}
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyAuthenticateByAccountPwdCaptchaTempForFXSPromotionPortal(
    credential: AccountPwdCaptchaCredentialRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyAuthenticateByAccountPwdCaptchaTempForFXSPromotionPortal,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 使用短信验证码凭据申请获取身份凭证
   * @param credential {token,phone,smsCode}
   * @return {code,message,identityAuthenticationToken}
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyAuthenticateBySmsCode(
    credential: SmsCodeCredentialRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyAuthenticateBySmsCode,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 使用短信验证码凭据申请获取身份凭证
   * @param credential {token,phone,smsCode}
   * @return {code,message,identityAuthenticationToken}
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyAuthenticateBySmsCodeForFXS(
    credential: SmsCodeCredentialForFXSRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyAuthenticateBySmsCodeForFXS,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请（业务域）身份凭证切换列表
   * @param credential {applicationMemberType,applicationMemberId}
   * @return {code,message,identityAuthenticationToken}
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyChangeIdentityAuthentication(
    credential: ChangeBusinessDomainCredentialRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyChangeIdentityAuthentication,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 使用代理标识凭据进行身份认证（获取代理身份凭证）
   * @param credential {proxyBusinessDomain}
   * 若要进行代理的用户身份属于平台级则不需要传代理业务域标识，否则需要明确代理业务域标识
   * @return {code,message,identityAuthenticationToken}
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyProxyIdentity(
    credential: ProxyIdentityCredentialRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyProxyIdentity,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请代理身份(需要调用平台与成员资格授信,签出的身份凭证会依据传入的代理业务域)
   * @param credential
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyProxyIdentityAccessSecure(
    credential: ProxyIdentityAccessSecureCredentialRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyProxyIdentityAccessSecure,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请代理身份(统一主体)
   * @param credential 代理业务域信息
   * @return 身份凭证Token
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyProxyIdentityUnifiedSubject(
    credential: ProxyIdentityCredentialRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyProxyIdentityUnifiedSubject,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 使用根令牌凭据申请身份再认证（跨平台）
   * @param credential {token,rgtCredential}
   * @return {code,message,identityAuthenticationToken}
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyReAuthenticate(
    credential: RgtCredentialRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyReAuthenticate,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 使用默认再认证凭据申请身份再认证（平台内）
   * @param credential {token}
   * @return {code,message,identityAuthenticationToken}
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyReAuthenticateBasic(
    credential: ReAuthenticateCredentialRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyReAuthenticateBasic,
    operation?: string
  ): Promise<Response<IdentityAuthenticationTokenResponse>> {
    return commonRequestApi<IdentityAuthenticationTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请第三方身份
   * @param credential {token}
   * @return {身份凭证Token,metadata}
   * @param mutate 查询 graphql 语法文档
   * @param credential 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyThirdPartyIdentity(
    credential: ThirdPartyIdentityCredentialRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyThirdPartyIdentity,
    operation?: string
  ): Promise<Response<ThirdPartyIdentityAuthTokenResponse>> {
    return commonRequestApi<ThirdPartyIdentityAuthTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { credential },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
