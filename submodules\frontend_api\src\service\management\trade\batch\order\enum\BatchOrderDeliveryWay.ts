import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 【集体报名订单】配送方式枚举
 */
export enum BatchOrderDeliveryWayEnum {
  /**
   * 邮寄
   */
  Courier = 1,
  /**
   * 自取
   */
  Self_Fetched
}

/**
 * @description 【集体报名订单】配送方式
 */
class BatchOrderDeliveryWay extends AbstractEnum<BatchOrderDeliveryWayEnum> {
  static enum = BatchOrderDeliveryWayEnum

  constructor(status?: BatchOrderDeliveryWayEnum) {
    super()
    this.current = status
    this.map.set(BatchOrderDeliveryWayEnum.Courier, '邮寄')
    this.map.set(BatchOrderDeliveryWayEnum.Self_Fetched, '自取')
  }
}

export default new BatchOrderDeliveryWay()
