<route-params content="/:id"></route-params>
<route-meta>
{
"isMenu": false,
"title": "专题编辑"
}
</route-meta>
<template>
  <div>
    <template v-if="$hasPermission('topicsEdit')" desc="编辑专题" actions="created">
      <el-main>
        <!--面包屑-->
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-button type="text" size="mini" class="return-btn" @click="goBack">
            <i class="iconfont icon-lsh-return"></i>
          </el-button>
          <el-breadcrumb-item :to="{ path: '/training/special-topics/manage' }">专题管理</el-breadcrumb-item>
          <el-breadcrumb-item>编辑专题</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="f-p15">
          <el-tabs v-model="activeName" type="card" class="m-tab-card">
            <div class="tab-right">
              <!-- <el-link type="primary" :underline="false" class="m-specialimg-pop"
                ><i class="el-icon-picture f-f20 f-mr5 f-vm"></i>查看专题示例
                <el-image class="transparent-pic" :src="elImage" :preview-src-list="reviewImage" />
              </el-link> -->
              <el-button type="primary" size="medium" class="f-mr5" @click="preview('web')">
                <i class="hb-iconfont icon-complelearn f-mr5"></i>预览专题web
              </el-button>
              <el-button type="primary" size="medium" class="f-mr15" @click="preview('h5')">
                <i class="hb-iconfont icon-complelearn f-mr5"></i>预览专题h5
              </el-button>
            </div>
            <el-tab-pane label="基础信息" name="first">
              <template v-if="$hasPermission('basicEdit')" desc="编辑基础信息" actions="@basicInfo,saveBasicInfo">
                <basic-info
                  ref="basicRef"
                  :basicInfo="thematicManagementItem.basicInfo"
                  :portalInfo="thematicManagementItem.portalInfo"
                  @saveBasicInfo="saveBasicInfo"
                  @clearPortalInfo="clearPortalInfo"
                  @haveUnit="haveUnit"
                ></basic-info>
              </template>
            </el-tab-pane>
            <el-tab-pane label="门户信息" name="second" lazy>
              <template v-if="$hasPermission('portalEdit')" desc="编辑门户信息" actions="@portalInfo,savePortalInfo">
                <portal-info
                  ref="portalInfoRef"
                  :portalInfo="thematicManagementItem.portalInfo"
                  :templateWeb="thematicManagementItem.basicInfo.templateWeb"
                  @savePortalInfo="savePortalInfo"
                ></portal-info>
              </template>
            </el-tab-pane>
            <el-tab-pane label="培训方案" name="third">
              <template
                v-if="$hasPermission('trainingEdit')"
                desc="编辑培训方案"
                actions="@TrainingInfo,saveTrainingChannelScheme"
              >
                <training-info
                  ref="trainingRef"
                  @saveTrainingChannelScheme="saveTrainingChannelScheme"
                  :topicID="topicID"
                  :trainClassList.sync="trainClassList"
                ></training-info>
              </template>
            </el-tab-pane>
            <el-tab-pane label="精品课程" name="premiumCourse">
              <template v-if="$hasPermission('premiunCourse')" desc="编辑精品课程" actions="@PremiunCourse">
                <premiun-course></premiun-course>
              </template>
            </el-tab-pane>
            <el-tab-pane label="线上集体报名" name="onlineApply" lazy v-if="!haveUnitFlag">
              <template v-if="$hasPermission('onlineUnitApply')" desc="线上集体报名" actions="@OnlineUnitApply">
                <online-unit-apply
                  :thematicManagementItem="thematicManagementItem"
                  :templateWeb="thematicManagementItem.basicInfo.templateWeb"
                  :activeName.sync="activeName"
                  :portalInfo="thematicManagementItem.portalInfo"
                ></online-unit-apply>
              </template>
            </el-tab-pane>
            <el-tab-pane label="线下集体报名" name="offlineApply" lazy v-if="!haveUnitFlag">
              <template v-if="$hasPermission('offlineUnitApply')" desc="线下集体报名" actions="@OfflineUnitApply">
                <offline-unit-apply
                  :thematicManagementItem="thematicManagementItem"
                  :templateWeb="thematicManagementItem.basicInfo.templateWeb"
                  :activeName.sync="activeName"
                  :portalInfo="thematicManagementItem.portalInfo"
                ></offline-unit-apply>
              </template>
            </el-tab-pane>
          </el-tabs>
          <!-- <div class="m-btn-bar f-tc is-sticky-1 f-mb30">
        <el-button>取 消</el-button>
        <el-button type="primary">保 存</el-button>
      </div> -->
        </div>
      </el-main>
    </template>
  </div>
</template>

<script lang="ts">
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
  import BasicInfo from '@hbfe/jxjy-admin-specialTopics/src/manage/components/basic-info.vue'
  import PortalInfo from '@hbfe/jxjy-admin-specialTopics/src/manage/components/portal-info.vue'
  import PremiunCourse from '@hbfe/jxjy-admin-specialTopics/src/manage/components/premium-course.vue'
  import TrainingInfo from '@hbfe/jxjy-admin-specialTopics/src/manage/components/training-info.vue'
  import { Component, Ref, Vue } from 'vue-property-decorator'

  import { DeleteScheme, SchemeList } from '@api/platform-gateway/platform-training-channel-v1'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import OfflineUnitApply from '@hbfe/jxjy-admin-specialTopics/src/manage/components/offline-unit-apply.vue'
  import OnlineUnitApply from '@hbfe/jxjy-admin-specialTopics/src/manage/components/online-unit-apply.vue'
  import PremiumCourseStore from '@hbfe/jxjy-admin-specialTopics/src/manage/vuex/PremiumCourseStore'
  import UnitApplyPicSizeTypeModel from '@hbfe/jxjy-admin-specialTopics/src/manage/vuex/UnitApplyPicSizeTypeModel'

  @Component({
    components: { BasicInfo, PortalInfo, TrainingInfo, PremiunCourse, OnlineUnitApply, OfflineUnitApply }
  })
  export default class extends Vue {
    activeName = 'first'
    topicID = ''
    thematicManagementItem = new ThematicManagementItem()
    elImage = require('@design/admin/assets/images/transparent-pic.png')
    reviewImage = [require('@design/admin/assets/images/demo-special-web-001.png')]
    /**
     * 集体报名保存loading
     */
    unitApplyLoading = false
    // 判断专题是否有单位属性
    haveUnitFlag = false
    /**
     * 保存培训报名配置
     */
    trainClassList: TrainClassCommodityVo[] = []
    @Ref('basicRef') basicRef: any
    @Ref('portalInfoRef') portalInfoRef: any
    @Ref('trainingRef') trainingRef: any

    //保存基础信息
    async saveBasicInfo() {
      await this.thematicManagementItem.validTrainingChannelEntryNameUnique().then(async (data) => {
        if (data && data.code == '200') {
          const status = await this.thematicManagementItem.updateBasicInfo()
          if (!status.data) {
            this.basicRef.btLoading = false
            this.$message.error(status.status.errors[0].message)
            return
          }
          if (status.data.code == '200') {
            this.basicRef.btLoading = false
            this.$confirm('修改内容已保存', '提示', {
              confirmButtonText: '我知道了',
              showCancelButton: false
            }).then(() => {
              this.$router.push({
                path: '/training/special-topics/manage'
              })
            })
          } else if (status.data.code == '200001') {
            this.basicRef.btLoading = false
            this.$message.error(status.data.message)
          } else {
            this.basicRef.btLoading = false
            this.$message.error('系统异常!')
          }
        } else {
          this.basicRef.btLoading = false
          this.$message.error(data.message)
        }
      })
    }

    //保存门户信息
    async savePortalInfo() {
      if (this.thematicManagementItem.portalID) {
        const status = await this.thematicManagementItem.updatePortalInfo()
        if (!status.data) {
          this.portalInfoRef.btLoading = false
          this.$message.error(status.status.errors[0].message)
          return
        }
        if (status && status.status.code == 200) {
          this.portalInfoRef.btLoading = false
          this.$confirm('修改内容已保存', '提示', {
            confirmButtonText: '我知道了',
            showCancelButton: false
          }).then(() => {
            this.$router.push({
              path: '/training/special-topics/manage'
            })
          })
        } else {
          this.portalInfoRef.btLoading = false
          this.$message.error('系统异常!')
        }
      } else {
        const status = await this.thematicManagementItem.savePortalInfo()
        if (status && status.status.code == 200) {
          this.portalInfoRef.btLoading = false
          this.$confirm('修改内容已保存', '提示', {
            confirmButtonText: '我知道了',
            showCancelButton: false
          }).then(() => {
            this.$router.push({
              path: '/training/special-topics/manage'
            })
          })
        } else {
          this.portalInfoRef.btLoading = false
          this.$message.error('系统异常!')
        }
      }
    }

    //保存培训方案
    async saveTrainingChannelScheme(
      addScheme: Array<SchemeList>,
      updateScheme: Array<SchemeList>,
      deleteScheme: Array<DeleteScheme>
    ) {
      const status = await this.thematicManagementItem.saveTrainingChannelScheme(addScheme, updateScheme, deleteScheme)
      if (status.status.code == 200) {
        this.trainingRef.btLoading = false
        this.$confirm('修改内容已保存', '提示', {
          confirmButtonText: '我知道了',
          showCancelButton: false
        }).then(async () => {
          this.trainingRef.addScheme = []
          this.trainingRef.deleteScheme = []
          this.trainingRef.commoditySkuIdList = new Array<HasSelectSchemeMode>()
          await this.trainingRef.searchBase()
          this.$router.push({
            path: '/training/special-topics/manage'
          })
        })
      } else {
        this.trainingRef.btLoading = false
        this.$message.error('系统异常!')
      }
    }

    goBack() {
      this.$router.push({
        path: '/training/special-topics/manage'
      })
    }

    //清除门户信息
    clearPortalInfo() {
      this.portalInfoRef.clear()
    }

    // 判断专题是否有单位属性
    haveUnit(val: boolean) {
      this.haveUnitFlag = val
    }

    /*
     *打开浏览专题页面
     **/
    async preview(type: string) {
      localStorage.setItem('topicPortal', JSON.stringify(this.thematicManagementItem))
      localStorage.setItem('topicPortal-trainClassList', JSON.stringify(this.trainClassList.slice(0, 4)))
      localStorage.setItem('topicPortal-premiumCourseList', JSON.stringify(PremiumCourseStore.getCourseList))
      if (type == 'web') {
        let type = 1
        // 预览对应模板判断
        switch (this.thematicManagementItem.basicInfo.templateWeb) {
          case 'Temlpate-Web-01':
            type = 1
            break
          case 'Temlpate-Web-02':
            type = 2
            break
          case 'Temlpate-Web-03':
            type = 3
            break
          default:
            break
        }
        window.open(window.location.origin + '/topicPortal?type=' + type, '_blank')
      } else if (type == 'h5') {
        window.open(window.location.origin + '/h5/#/platforms/h5/tab/topicPortal', '_blank')
      }
    }

    async activated() {
      ///路由传过来的专题id
      const topicID = this.$route.params.id as string
      const activeName = this.$route.query.activeName as string
      this.topicID = topicID
      this.thematicManagementItem = await this.thematicManagementItem.getDetail(topicID)
      this.trainingRef.count = this.thematicManagementItem.selectedTrainingPlanCount
      this.queryOnlineCollectiveInfo()
      this.queryOfflineCollectiveInfo()
      activeName && (this.activeName = activeName)
      this.trainingRef.init()
      // console.log('查询编辑的数据', this.thematicManagementItem)
    }
    /**
     * 查询线上集体报名配置
     */
    queryOnlineCollectiveInfo() {
      this.thematicManagementItem.queryOnlineCollectiveInfo().then(() => {
        UnitApplyPicSizeTypeModel.setOriginOnlinePicEnable(
          this.thematicManagementItem.onlineCollectiveInfo.onlineCollectiveEntryImage
        )
      })
    }
    /**
     * 查询线下集体报名配置
     */
    queryOfflineCollectiveInfo() {
      this.thematicManagementItem.queryOfflineCollectiveInfo().then(() => {
        UnitApplyPicSizeTypeModel.setOriginOfflinePicEnable(
          this.thematicManagementItem.offlineCollectiveInfo.offlineCollectiveEntry
        )
      })
    }
  }
</script>
