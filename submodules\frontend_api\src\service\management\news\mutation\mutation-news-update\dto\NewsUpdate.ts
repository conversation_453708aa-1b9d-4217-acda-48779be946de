/*
 * @Description: 创建资讯草稿DTO
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-17 13:37:51
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-18 14:25:02
 */
import { NewsUpdateRequest } from '@api/ms-gateway/ms-news-v1'

import NewsDetailVo from '../../../query/query-news-detail/vo/NewsDetail'

class NewsCreateDto extends NewsUpdateRequest {
  static from(createDraftNewsVo: NewsDetailVo) {
    const newsCreateParam = new NewsCreateDto()
    newsCreateParam.newsId = createDraftNewsVo.newId
    newsCreateParam.title = createDraftNewsVo.title
    newsCreateParam.summary = createDraftNewsVo.abstract
    newsCreateParam.coverPath = createDraftNewsVo.bgImage
    newsCreateParam.content = createDraftNewsVo.content
    newsCreateParam.source = createDraftNewsVo.source
    newsCreateParam.publishTime = createDraftNewsVo.time
    newsCreateParam.popUps = createDraftNewsVo.isPopup
    newsCreateParam.top = createDraftNewsVo.top
    newsCreateParam.popupBeginTime = createDraftNewsVo.popupBeginTime
    newsCreateParam.popupEndTime = createDraftNewsVo.popupEndTime
    newsCreateParam.newsCategoryId = createDraftNewsVo.categoryType[createDraftNewsVo.categoryType.length - 1]
    newsCreateParam.areaCodePath =
      createDraftNewsVo?.areaCodeList && createDraftNewsVo?.areaCodeList.length
        ? '/' + createDraftNewsVo.areaCodeList.join('/')
        : undefined
    newsCreateParam.verifyPopUps = createDraftNewsVo.verifyPopUps
    return newsCreateParam
  }
}
export default NewsCreateDto
