import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum AdministratorStatusEnum {
  /**
   * 启用
   */
  enable = 1,
  /**
   * 停用
   */
  disable = 2
}

class AdministratorStatus extends AbstractEnum<AdministratorStatusEnum> {
  static enum = AdministratorStatusEnum

  constructor(status?: AdministratorStatusEnum) {
    super()
    this.current = status
    this.map.set(AdministratorStatusEnum.disable, '停用')
    this.map.set(AdministratorStatusEnum.enable, '启用')
  }
}

export default AdministratorStatus
