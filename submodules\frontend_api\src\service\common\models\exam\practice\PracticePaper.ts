class PracticePaper {
  /**
   * 试卷id
   */
  id: string
  /**
   * 学习方案id
   */
  schemeId: string

  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷分类id
   */
  paperTypeId: string
  /**
   * 配置类型，1：固定卷 2：AB卷 3：智能卷
   */
  configType: number
  /**
   * 随机卷抽题类型 1：自定义（此时需要指定random配置），2：题库，3：考场所属方案下已选课程关联试题,4:标签
   */
  fetchWay: number

  /**
   * 允许作答的起始时间
   */
  enterStartTime: Date

  /**
   * 允许作答的终止时间
   */
  enterEndTime: Date
}

export default PracticePaper
