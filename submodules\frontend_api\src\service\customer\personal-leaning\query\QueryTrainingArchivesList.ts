import { Page, UiPage } from '@hbfe/common'
import MsMySchemeQueryFrontGatewayCourseLearningForestage, {
  CertificateLearningConfigResultResponse,
  SchemeSkuPropertyResponse,
  SortPolicy,
  StudentSchemeLearningSortField,
  StudentSchemeLearningSortRequest,
  StudentSchemeAndIssueLearningResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import { getSchemeLearningDetailInMyself } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage/graphql-importer'
import TrainingArchiveVo from './vo/TrainingArchiveVo'
import SkuPropertyConvertUtils, { SchemeSkuInfo } from '../../train-class/Utils/SkuPropertyConvertUtils'
import SkuPropertyVo from './vo/SkuPropertyVo'
import TrainingArchivesRequestVo from './vo/TrainingArchivesRequestVo'
import SkuPropertyResponseVo from '../../train-class/query/vo/SkuPropertyResponseVo'
import {
  CommoditySkuPropertyResponse,
  PortalCommoditySkuPropertyResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import Scheme from '@api/service/common/scheme/model/schemeDto/Scheme'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
/*
  网校域获取学习档案列表
*/
class QueryCustomerTrainingArchivesList {
  // 培训证明列表
  trainingArchivesList: Array<TrainingArchiveVo> = []

  // 刷选条件数组，类型为SkuPropertyVo
  skuProperties = new SkuPropertyVo()

  /**
   * @description: 查询学习档案列表
   * @param {UiPage} page
   */
  async queryLearningArchivesList(page: UiPage, trainingChannelId?: string): Promise<Array<TrainingArchiveVo>> {
    const filterCommodity = new TrainingArchivesRequestVo()
    // 默认培训合格
    filterCommodity.studentLearning.trainingResultList = [1]
    filterCommodity.learningRegister = {
      status: [1, 2]
    }
    if (trainingChannelId) {
      filterCommodity.trainingChannelId = trainingChannelId
      filterCommodity.saleChannels = [2]
    }

    //根据年度倒序
    const sort = new StudentSchemeLearningSortRequest()
    sort.policy = SortPolicy.DESC
    sort.field = StudentSchemeLearningSortField.SCHEME_YEAR
    const res = await MsMySchemeQueryFrontGatewayCourseLearningForestage.pageSchemeLearningInMyself({
      page: { pageNo: page.pageNo, pageSize: page.pageSize },
      request: filterCommodity,
      sort: [sort]
    })
    if (res.status.isSuccess()) {
      const tmpArr = res.data.currentPageData as TrainingArchiveVo[]
      const qualificationIds: Map<string, string> = new Map()
      tmpArr.forEach(item => {
        item.skuValueNameProperty = new SkuPropertyResponseVo()
        item.skuValueNameProperty.year.skuPropertyName = item.scheme.skuProperty.year.skuPropertyValueId
        qualificationIds.set(item.scheme.schemeId, item.qualificationId)
      })
      const configJsonMap = await this.queryConfigs(qualificationIds)
      const schemeIds = tmpArr?.map(item => item.scheme.schemeId) || []
      const currentConfigMap = await this.getCurrentConfig(schemeIds)
      for (const tmpItem of tmpArr) {
        // tmpItem.skuValueNameProperty = await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
        //   tmpItem.scheme.skuProperty
        // )
        // const learningResults = tmpItem.studentLearning.learningResult
        // if (learningResults) {
        //   const learningResult = learningResults.find(item => {
        //     return item.learningResultConfig.resultType == 2
        //   })
        //   if (learningResult) {
        //     tmpItem.hasLearnResult = true
        //     tmpItem.learningResultId = (learningResult.learningResultConfig as CertificateLearningConfigResultResponse).certificateTemplateId
        //   }
        // } else {
        //   tmpItem.hasLearnResult = false
        //   tmpItem.learningResultId = ''
        // }

        // const configJson = await this.queryConfig(tmpItem.qualificationId)
        const configJson = configJsonMap.get(tmpItem.qualificationId)
        // const configJson = currentConfigMap.get(tmpItem.scheme.schemeId)
        if (configJson) {
          tmpItem.registerBeginDate = configJson.registerBeginDate
          tmpItem.registerEndDate = configJson.registerEndDate
          tmpItem.trainingBeginDate = configJson.trainingBeginDate
          tmpItem.trainingEndDate = configJson.trainingEndDate
          tmpItem.picture = configJson.picture
          tmpItem.schemeType = SchemeTypeEnum[configJson.type as string]
          const { schemeAssessItem } = Scheme.parseTrainClassAssess(configJson as Scheme)
          if (schemeAssessItem) {
            const creditResult = schemeAssessItem.learningResults.find((item: any) => {
              return item.type == 1
            })
            tmpItem.period = creditResult.grade
            tmpItem.trainClassName = configJson.name
          }
        }
        const currentConfig = currentConfigMap.get(tmpItem.scheme.schemeId)
        if (currentConfig) {
          /*            列表读最新配置名称           */
          tmpItem.trainClassName = currentConfig.name
          tmpItem.picture = currentConfig.picture
          /*           读取最新学时           */
          const { schemeAssessItem } = Scheme.parseTrainClassAssess(currentConfig as Scheme)
          if (schemeAssessItem) {
            const creditResult = schemeAssessItem.learningResults?.find((item: any) => {
              return item.type == 1
            })
            tmpItem.period = creditResult?.grade || tmpItem.period
            /*           读取最新证明配置           */
            const learningResult = schemeAssessItem.learningResults?.find((item: any) => {
              return item.type == 2
            })
            if (learningResult) {
              tmpItem.hasLearnResult = true
              tmpItem.learningResultId = learningResult.certificateTemplateId
              tmpItem.openPrintTemplate = learningResult.openPrintTemplate
            } else {
              tmpItem.hasLearnResult = false
              tmpItem.learningResultId = ''
            }
          }
        }
      }
      const skuRequest = [] as SchemeSkuInfo[]
      tmpArr?.forEach(item => {
        skuRequest.push(
          new SchemeSkuInfo(
            item.scheme.schemeId,
            item.scheme.skuProperty as (CommoditySkuPropertyResponse & SchemeSkuPropertyResponse) &
              PortalCommoditySkuPropertyResponse
          )
        )
      })
      const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(skuRequest)
      tmpArr?.forEach(item => {
        const skuInfo = skuInfos.find(el => el.id === item.scheme.schemeId)
        if (skuInfo) item.skuValueNameProperty = skuInfo.skuName
      })
      this.trainingArchivesList = tmpArr
      page.totalSize = res?.data?.totalSize
    }

    if (this.trainingArchivesList?.length) {
      const periodIdMap = new Map<string, string>() // 方案id-期别id
      const periodNameMap = new Map<string, string>() // 方案id-期别名称
      const jsonMap = new Map<string, string>() // 方案id-json
      // 获取学号
      const studentNos = this.trainingArchivesList.map(item => item.studentNo)
      // 获取方案id集合
      const schemeIds = this.trainingArchivesList.map(item => item.scheme.schemeId)
      const page2 = new Page(1, 200)
      const response = await MsMySchemeQueryFrontGatewayCourseLearningForestage.pageSchemeConfigInServicer({
        page: page2,
        schemeIds: schemeIds
      })
      response?.data?.currentPageData?.forEach(item => {
        jsonMap.set(item.schemeId, item.schemeConfig)
      })
      const reWriteGQL = new RewriteGraph<StudentSchemeAndIssueLearningResponse, string>(
        MsMySchemeQueryFrontGatewayCourseLearningForestage._commonQuery,
        getSchemeLearningDetailInMyself
      )

      await reWriteGQL.request(studentNos)
      for (const [key, value] of reWriteGQL.itemMap.entries()) {
        if (value) {
          periodIdMap.set(value.studentSchemeWithIssueLearningResponse?.schemeId, value.studentIssueLearning?.issueId)
        }
      }
      console.log('periodIdMap', periodIdMap)
      for (const [key, value] of jsonMap.entries()) {
        if (periodIdMap.get(key)) {
          const classConfigJson = JSON.parse(value)
          const issueInfo = classConfigJson.issueConfigures?.find(
            (item: { id: string }) => item.id == periodIdMap.get(key)
          )
          if (issueInfo) {
            periodNameMap.set(key, issueInfo.issueName)
          }
        }
      }
      console.log('periodNameMap', periodNameMap)
      this.trainingArchivesList.forEach(item => {
        item.issueName = periodNameMap.get(item.scheme.schemeId)
      })
    }
    console.log('this.trainingArchivesList', this.trainingArchivesList)
    return this.trainingArchivesList
  }

  /**
   * 获取培训班属性 --- 批量
   */
  async queryConfigs(qualificationIds: Map<string, string>): Promise<any> {
    //获取培训班配置模板jsonString
    const schemaId = [...qualificationIds.keys()]
    const qualificationIdList = [...qualificationIds.values()]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = schemaId.length || 1
    const res = await MsMySchemeQueryFrontGatewayCourseLearningForestage.pageStudentSchemeConfigInMySelf({
      page,
      request: {
        qualificationIdList
      }
    })
    const configJsonMap: Map<string, any> = new Map()
    res.data.currentPageData.forEach(item => {
      //
      const temp = qualificationIds.get(item.schemeId)
      if (temp) {
        configJsonMap.set(temp, JSON.parse(item.schemeConfig))
      }
    })
    return configJsonMap
  }

  /**
   * 获取最新配置
   * @param schemeIds
   */
  async getCurrentConfig(schemeIds: string[]) {
    const configJsonMap: Map<string, any> = new Map()
    if (schemeIds.length) {
      const page = new Page()
      page.pageNo = 1
      page.pageSize = schemeIds.length || 1
      const res = await MsMySchemeQueryFrontGatewayCourseLearningForestage.pageSchemeConfigInServicer({
        page,
        schemeIds
      })
      res.data.currentPageData.forEach(item => {
        configJsonMap.set(item.schemeId, JSON.parse(item.schemeConfig))
      })
    }
    return configJsonMap
  }

  /**
   * 获取培训班属性
   */
  private async queryConfig(qualificationId: string): Promise<any> {
    //获取培训班配置模板jsonString
    const res = await MsMySchemeQueryFrontGatewayCourseLearningForestage.getMySchemeConfig({
      qualificationId: qualificationId,
      needField: [
        'picture',
        'name',
        'registerBeginDate',
        'registerEndDate',
        'trainingBeginDate',
        'trainingEndDate',
        'type',
        'assessSetting.learningResults'
      ]
    })
    let jsonObj
    try {
      jsonObj = JSON.parse(res.data.schemeConfig)
    } catch (e) {
      return ''
    }
    return jsonObj
  }
}

export default QueryCustomerTrainingArchivesList
