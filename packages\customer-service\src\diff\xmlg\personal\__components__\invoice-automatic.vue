<template>
  <el-card shadow="never" class="m-card f-mb15">
    <!--条件查询-->
    <el-row :gutter="16" class="m-query">
      <el-form :inline="true" label-width="auto">
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item label="订单号">
            <el-input v-model="queryPageInvoiceParam.orderNoList" clearable placeholder="请输入订单号" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item>
            <!-- <el-button type="text" size="mini" @click="editInvoicePopup">修改发票信息</el-button> -->
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <!--表格-->
    <el-table
      stripe
      :data="invoiceList"
      max-height="500px"
      class="m-table"
      v-loading="query.loading"
      ref="autoInvoiceTable"
    >
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="订单号" min-width="220" fixed="left">
        <template slot-scope="scope">
          {{ scope.row.orderNo }}
          <p><el-tag type="success" size="mini" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag></p>
        </template>
      </el-table-column>

      <el-table-column label="退款状态" min-width="130">
        <template slot-scope="scope">
          <el-badge
            is-dot
            :type="refundStatusMapName[scope.row.orderReturnStatus]"
            class="badge-status"
            v-if="refundStatusMapName[scope.row.orderReturnStatus]"
          >
            {{ refundStatusMapName[scope.row.orderReturnStatus] }}</el-badge
          >

          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="付款金额(元)" width="140" prop="payAmount" align="right">
        <!-- <template slot-scope="scope">
          <div v-if="scope.$index === 0">3.15</div>
          <div v-else-if="scope.$index === 1">52.36</div>
          <div v-else>158.15</div>
        </template> -->
      </el-table-column>
      <el-table-column label="开票金额(元)" width="140" align="right">
        <template slot-scope="scope">
          {{ scope.row.blueTotalAmount || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="税额(元)" width="140" prop="totalTax" align="right">
        <template slot-scope="scope">
          {{ scope.row.totalTax || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="购买人信息" min-width="240">
        <template slot-scope="scope">
          <p>姓名：{{ scope.row.name || '-' }}</p>
          <p>证件号：{{ scope.row.idCard || '-' }}</p>
          <p>手机号：{{ scope.row.phone || '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="发票抬头" min-width="300">
        <template slot-scope="scope">【{{ invoiceTitleMapType[scope.row.titleType] }}】{{ scope.row.title }}</template>
      </el-table-column>
      <el-table-column label="统一社会信用代码" prop="taxpayerNo" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.taxpayerNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="申请开票时间" prop="applyForDate" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.applyForDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="发票状态" min-width="130">
        <template slot-scope="scope">
          <el-badge
            is-dot
            :type="invoiceStatusMapType[scope.row.invoiceStatus]"
            class="badge-status"
            v-if="invoiceStatusMapName[scope.row.invoiceStatus]"
          >
            {{ invoiceStatusMapName[scope.row.invoiceStatus] }}
          </el-badge>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="开票时间" prop="invoiceDate" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.invoiceDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="发票号" prop="blueInvoiceNo" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.blueInvoiceNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="是否冻结" min-width="120">
        <template slot-scope="scope">{{ scope.row.invoiceFreezeStatus ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column label="操作" width="140" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" v-if="showModifyBtn(scope.row)" size="mini" @click="editInvoicePopup(scope.row)"
            >修改发票信息</el-button
          >
          <el-button v-else type="text" disabled size="mini">修改发票信息</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="page" v-bind="page"></hb-pagination>
    <edit-invoice-dialog
      :dialog-ctrl.sync="editInvoiceDialog"
      :invoice-id="invoiceId"
      @callBack="doQueryPage"
    ></edit-invoice-dialog>
  </el-card>
</template>
<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { UiPage, Query } from '@hbfe/common'
  import invoiceInformation from '@hbfe/jxjy-admin-customerService/src/personal/components/invoice-information.vue'
  import QueryInvoice from '@api/service/diff/management/xmlg/trade/invoice/QueryInvoice'
  import QueryPageInvoiceParam from '@api/service/management/trade/single/invoice/query/dto/QueryPageInvoiceParam'
  import {
    OnlineInvoiceSortField,
    OnlineInvoiceSortRequest,
    SortPolicy
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import InvoiceListResponse from '@api/service/diff/management/xmlg/trade/invoice/model/InvoiceListResponse'
  import EditInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/edit-invoice-dialog.vue'
  import {
    InvoiceStatusEnum,
    TitleTypeEnum,
    OrderReturnStatusEnum
  } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import OrderRefundStatus from '@api/service/common/return-order/enums/OrderRefundStatus'

  @Component({
    components: {
      invoiceInformation,
      EditInvoiceDialog
    }
  })
  export default class extends Vue {
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    // 页面分页控件
    page: UiPage
    // 分页查询
    query: Query = new Query()
    //接口请求
    queryInvoiceRemote: QueryInvoice = new QueryInvoice()
    //接口查询参数
    queryPageInvoiceParam: QueryPageInvoiceParam = new QueryPageInvoiceParam()
    //创建时间进行排序
    sort: Array<OnlineInvoiceSortRequest> = [
      { field: OnlineInvoiceSortField.INVOICE_CREAT_TIME, policy: SortPolicy.DESC }
    ]
    //订单发票列表
    invoiceList: InvoiceListResponse[] = [] as InvoiceListResponse[]
    /**
     * 打开-弹窗标识
     */
    editInvoiceDialog = false
    //订单id
    invoiceId = ''
    // 用户id
    userId = ''
    //退货状态
    OrderRefundStatus = OrderRefundStatus

    invoiceStatusMapType = {
      [InvoiceStatusEnum.NOTPTOOPEN]: 'info',
      [InvoiceStatusEnum.OPENING]: 'primary',
      [InvoiceStatusEnum.OPENERROR]: 'danger',
      [InvoiceStatusEnum.OPEMSUCCESS]: 'success'
    }

    invoiceStatusMapName = {
      [InvoiceStatusEnum.NOTPTOOPEN]: '待开票',
      [InvoiceStatusEnum.OPENING]: '开票中',
      [InvoiceStatusEnum.OPENERROR]: '开票失败',
      [InvoiceStatusEnum.OPEMSUCCESS]: '开票成功'
    }

    refundStatusMapName = {
      [OrderReturnStatusEnum.RETURNSING]: '退款处理中',
      [OrderReturnStatusEnum.RETURNSUCCESS]: '退款成功'
    }
    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }
    // 订单来源枚举
    SaleChannelEnum = SaleChannelEnum
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }

    @Prop({
      type: UserDetailVo,
      default: new UserDetailVo()
    })
    userData!: UserDetailVo
    @Watch('userData', {
      deep: true
    })
    async userDataChange({ userName, idCard, phone, userId, loginAccount }: UserDetailVo) {
      this.queryPageInvoiceParam.userName = userName
      this.queryPageInvoiceParam.idCard = idCard
      this.queryPageInvoiceParam.phone = phone
      this.userId = userId
      this.queryPageInvoiceParam.loginAccount = loginAccount
      if (userName || idCard || phone) {
        await this.search()
      } else {
        this.invoiceList = []
      }
    }
    /**
     * 查询发票分页
     */
    async doQueryPage() {
      if (!this.userId) {
        return
      }
      this.query.loading = true
      try {
        this.invoiceList = await this.queryInvoiceRemote.onLinePageInvoiceInServicer(
          this.page,
          this.queryPageInvoiceParam,
          this.sort
        )
      } catch (e) {
        console.log(e, '加载发票列表失败')
      } finally {
        ;(this.$refs['autoInvoiceTable'] as any)?.doLayout()

        this.query.loading = false
      }
    }
    async search() {
      this.page.pageNo = 1
      await this.doQueryPage()
    }
    reset() {
      this.page.pageNo = 1
      this.queryPageInvoiceParam.orderNoList = ''
    }
    async editInvoicePopup(item: InvoiceListResponse) {
      if (item.invoiceFreezeStatus) {
        this.$message.warning('发票已冻结，无法修改！')
        return
      }
      this.invoiceId = item.invoiceId
      this.editInvoiceDialog = true
    }

    // 展示修改按钮
    showModifyBtn(item: InvoiceListResponse) {
      // 开票中和开票成功不展示
      if (item.invoiceStatus == 1 || item.invoiceStatus == 2) return false
      // 冻结不展示
      if (item.invoiceFreezeStatus) return false
      return true
    }
  }
</script>
