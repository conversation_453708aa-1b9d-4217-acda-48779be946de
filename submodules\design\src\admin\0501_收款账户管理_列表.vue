<template>
  <el-main>
    <div class="f-p15">
      <div class="f-mb15">
        <el-button type="primary" icon="el-icon-plus">新建收款账号</el-button>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="收款账号别名" min-width="200" fixed="left">
            <template>收款账号别名别名</template>
          </el-table-column>
          <el-table-column label="支付方式" min-width="150" align="center">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-tag type="warning" size="small">线下</el-tag>
              </div>
              <div v-else>
                <el-tag type="primary" size="small">线上</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="退款方式" min-width="150" align="center">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-tag type="warning" size="small">线下退款</el-tag>
              </div>
              <div v-else>
                <el-tag type="primary" size="small">线上退款</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="支付帐号类型" min-width="150">
            <template>微信支付</template>
          </el-table-column>
          <el-table-column label="商户号" min-width="200">
            <template>125852</template>
          </el-table-column>
          <el-table-column label="帐号状态" min-width="150">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-badge is-dot type="success" class="badge-status">启用</el-badge>
              </div>
              <div v-else>
                <el-badge is-dot type="info" class="badge-status">停用</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="190" align="center" fixed="right">
            <template>
              <el-button type="text" size="mini">停用</el-button>
              <el-button type="text" size="mini">详细</el-button>
              <el-button type="text" size="mini">修改</el-button>
              <el-button type="text" size="mini">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-pagination
          background
          class="f-mt15 f-tr"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage4"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
        >
        </el-pagination>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
