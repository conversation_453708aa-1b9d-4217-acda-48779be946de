<template>
  <div>
    <el-input
      :id="inputId"
      @clear="clear"
      v-model="radioItemName"
      placeholder="请选择具体导入任务"
      @focus="selectImport"
      clearable
    />

    <template>
      <el-drawer
        title="选择具体导入任务"
        :visible.sync="show"
        size="1200px"
        :before-close="cancel"
        custom-class="m-drawer"
      >
        <div class="drawer-bd">
          <el-row :gutter="16" class="m-query f-mt10">
            <el-form :inline="true" label-width="auto">
              <el-col :span="6">
                <el-form-item label="执行状态">
                  <el-select v-model="trackingParam.taskState" clearable filterable placeholder="请选择执行状态">
                    <el-option label="已创建" :value="0">已创建</el-option>
                    <el-option label="已就绪" :value="1">已就绪</el-option>
                    <el-option label="执行中" :value="2">执行中</el-option>
                    <el-option label="执行完成" :value="3">执行完成</el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="执行时间">
                  <double-date-picker
                    :begin-create-time.sync="trackingParam.executeStartTime"
                    :end-create-time.sync="trackingParam.executeEndTime"
                    begin-time-placeholder="请选择导入开始时间"
                    end-time-placeholder="请选择导入结束时间"
                  ></double-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item>
                  <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
          <!--表格-->
          <el-table stripe :data="importTaskList" v-loading="loading" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="任务名称" min-width="280" prop="name">
              <template slot-scope="scope">{{ scope.row.name }}</template>
            </el-table-column>
            <el-table-column label="任务处理时间 - 任务结束时间" min-width="220">
              <template slot-scope="scope">
                <p>处理：{{ scope.row.executingTime || '-' }}</p>
                <p>结束：{{ scope.row.completedTime || '-' }}</p>
              </template>
            </el-table-column>
            <el-table-column label="任务执行状态" min-width="120">
              <template slot-scope="scope">
                <div>
                  <el-tag :type="invoiceStatusMapType[scope.row.taskState]">{{
                    questionImportTaskStatusEnum[scope.row.taskState]
                  }}</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="任务处理结果" min-width="120">
              <template slot-scope="scope">
                <div v-if="scope.row.processResult === QuestionImportTaskResultEnum.NOT_EXECUTE">
                  <el-badge is-dot type="danger" class="badge-status">未知</el-badge>
                </div>
                <div v-else-if="scope.row.processResult === QuestionImportTaskResultEnum.FAIL">
                  <el-badge is-dot type="danger" class="badge-status">{{ scope.row.result }}失败</el-badge>
                </div>
                <div v-else-if="scope.row.processResult === QuestionImportTaskResultEnum.SUCCESS">
                  <el-badge is-dot type="success" class="badge-status">{{ scope.row.result }}成功</el-badge>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="处理总条数/成功条数/失败条数" width="240" align="center">
              <template slot-scope="scope"
                >{{ scope.row.totalCount || 0 }} / {{ scope.row.successCount || 0 }} /
                {{ scope.row.failCount || 0 }}</template
              >
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <el-radio v-model="radioItemId" :label="scope.row.taskId" @change="choice(scope.row)">选择</el-radio>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="page" v-bind="page"></hb-pagination>
          <div class="m-btn-bar f-tc f-mt20">
            <el-button @click="cancel">取消</el-button>
            <el-button @click="importSelect" type="primary">确定</el-button>
          </div>
        </div>
      </el-drawer>
    </template>
  </div>
</template>

<script lang="ts">
  import { UiPage } from '@hbfe/common'
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import ImportTaskListInServicer from '@api/service/management/intelligence-learning/ImportTaskListInServicer'
  import ImportTaskListInTrainingChannel from '@api/service/management/intelligence-learning/ImportTaskListInTrainingChannel'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import ImportTaskItem from '@api/service/management/intelligence-learning/model/ImportTaskItem'
  import ImportTaskParams from '@api/service/management/intelligence-learning/model/ImportTaskParams'
  import { QuestionImportTaskStatusEnum } from '@api/service/common/enums/async-task/QuestionImportTaskStatus'
  import { QuestionImportTaskResultEnum } from '@api/service/common/enums/async-task/QuestionImportTaskResult'

  @Component({
    components: {
      DoubleDatePicker
    }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    /**
     * 绑定key的ID
     */
    @Prop({ type: String, default: '' }) inputId: string

    /**
     * 传值
     */
    @Prop({ type: String, default: '' }) value: string

    /**
     * 分页
     */
    page: UiPage

    /**
     * 加载状态
     */
    loading = false

    /**
     * 查询入参
     */
    trackingParam = new ImportTaskParams()

    /**
     * 是否展示
     */
    show = false

    /**
     * 选中的值
     */
    radioItemName = ''

    radioItemId = ''

    /**
     * 历史选中值
     */
    oldRadioName = ''

    oldRadioId = ''
    /**
     * 执行任务结果枚举
     */
    QuestionImportTaskResultEnum = QuestionImportTaskResultEnum

    /**
     * 列表数据
     */
    importTaskList = new Array<ImportTaskItem>()

    questionImportTaskStatusEnum = {
      [QuestionImportTaskStatusEnum.ALREADY_CREATED]: '已创建',
      [QuestionImportTaskStatusEnum.ALREADY_PREPARED]: '已就绪',
      [QuestionImportTaskStatusEnum.EXECUTING]: '执行中',
      [QuestionImportTaskStatusEnum.EXECUTED]: '执行完成'
    }
    invoiceStatusMapType = {
      [QuestionImportTaskStatusEnum.ALREADY_CREATED]: 'info',
      [QuestionImportTaskStatusEnum.ALREADY_PREPARED]: 'primary',
      [QuestionImportTaskStatusEnum.EXECUTING]: 'primary',
      [QuestionImportTaskStatusEnum.EXECUTED]: 'success'
    }

    /**
     * 判断当前用户是否拥有专题管理员角色类型
     */
    get isZtglyLogin() {
      return QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    }

    @Watch('value', { immediate: true })
    valueChange() {
      if (!this.value) {
        this.clear()
      }
    }
    /**
     * 选中输入框
     */
    async selectImport() {
      const inputEl = document.getElementById(this.inputId)
      inputEl.blur()
      this.show = true
      await this.doSearch()
    }

    /**
     * 专题管理员查询
     */
    async queryQueryImportListByThemeManeger() {
      try {
        this.loading = true
        const mutation = new ImportTaskListInTrainingChannel()
        mutation.queryParam = this.trackingParam
        const res = await mutation.queryList(this.page)
        if (res.isSuccess()) {
          this.importTaskList = mutation.list
        } else {
          this.$message.error(res.getMessage())
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 网校管理员查询
     */
    async queryQueryImportList() {
      try {
        this.loading = true
        const mutation = new ImportTaskListInServicer()
        mutation.queryParam = this.trackingParam
        const res = await mutation.queryList(this.page)
        if (res.isSuccess()) {
          this.importTaskList = mutation.list
        } else {
          this.$message.error(res.getMessage())
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 查询
     */
    async doSearch() {
      if (this.isZtglyLogin) {
        await this.queryQueryImportListByThemeManeger()
      } else {
        await this.queryQueryImportList()
      }
    }

    /**
     * 选中数据
     */
    choice(row: ImportTaskItem) {
      console.log('row', row)
      this.radioItemName = row.name
    }

    /**
     * 确定
     */
    importSelect() {
      this.oldRadioName = this.radioItemName
      this.oldRadioId = this.radioItemId
      this.show = false
      this.$emit('input', this.radioItemId)
    }

    /**
     * 清空事件
     */
    clear() {
      this.oldRadioName = ''
      this.radioItemName = ''
      this.radioItemId = ''
      this.oldRadioId = ''
      this.$emit('input', '')
    }

    /**
     * 取消
     */
    cancel() {
      console.log('xxxxx')
      this.radioItemName = this.oldRadioName
      this.radioItemId = this.oldRadioId
      this.show = false
    }
  }
</script>
