import TrainClassBaseModel from '@api/service/customer/train-class/query/vo/TrainClassBaseModel'
import LearningType from '@api/service/customer/train-class/query/vo/LearningType'
import { UserGetLearning } from '@api/service/customer/train-class/query/vo/UserGetLearning'
import { TrainingTypeEnum } from '@api/service/common/scheme/enum/TrainingType'

/**
 * 我的培训班详情Vo
 */
class MyTrainClassDetailClassVo {
  // region properties
  /**
   * 培训类型
   */
  trainingType: TrainingTypeEnum = TrainingTypeEnum.online
  /**
   *可见的购买渠道，1：用户自主购买，2：集体缴费，3：管理员导入4：所有，类型为number[]
   */
  visibleChannelList: number[] = []
  /**
   *商品抬头，类型为string
   */
  saleTitle = ''
  /**
   *商品id，类型为string
   */
  commoditySkuId = ''
  /**
   * 分类id
   */
  categoryId = ''
  /**
   *价格，类型为number
   */
  price = 0
  /**
   *是否关闭学员报名，类型为boolean
   */
  closeCustomerPurchase = false
  /**
   *上架计划时间，类型为string
   */
  onShelvesPlanTime = ''
  /**
   * 税务编码
   */
  taxCode = ''

  /**
   *下架计划时间，类型为string
   */
  offShelvesPlanTime = ''
  /**
   *是否立即上架，类型为boolean
   */
  onShelves = false
  /**
   *培训班基础信息，类型为TrainClassBaseModel
   */
  trainClassBaseInfo = new TrainClassBaseModel()
  /**
   *学习方式，类型为LearningType
   */
  learningTypeModel = new LearningType()
  /**
   * 学员获得的学习内容
   */
  userGetLearning = new UserGetLearning()
  // endregion
  // region methods

  // endregion
}
export default MyTrainClassDetailClassVo
