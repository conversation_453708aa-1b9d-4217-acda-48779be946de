import { ExchangeOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { ExchangeTrainClassStatusEnum } from '@api/service/common/enums/train-class/ExchangeTrainClassStatusType'
import ExchangeStatusInfoVo from '@api/service/centre/train-class/query/vo/ExchangeStatusInfoVo'
import { ExchangeOrderStatusEnum } from '@api/service/centre/train-class/enum/ExchangeOrderStatus'

/**
 * @description
 */
class ExchangeClassUtils {
  /**
   * 获取换班状态
   */
  static getExchangeStatus(response: ExchangeOrderResponse): ExchangeTrainClassStatusEnum {
    const responseStatus: number = response.basicData?.status ?? undefined
    if ((responseStatus ?? undefined) !== undefined) {
      // 换货中
      const exchangeProcessingStatusList: number[] = [0, 2, 3, 4, 5, 6]
      // 换货成功状态列表
      const completeExchangeStatusList: number[] = [7]
      // 取消换货
      const cancelExchangeStatusList: number[] = [8]
      if (exchangeProcessingStatusList.includes(responseStatus)) {
        return ExchangeTrainClassStatusEnum.Exchanging
      }
      if (completeExchangeStatusList.includes(responseStatus)) {
        return ExchangeTrainClassStatusEnum.Complete_Exchange
      }
      if (cancelExchangeStatusList.includes(responseStatus)) {
        return ExchangeTrainClassStatusEnum.Cancle_Exchange
      }
      return null
    }
    return null
  }

  /**
   * 获取换班信息
   */
  static getExchangeStatusInfoList(response: ExchangeOrderResponse): ExchangeStatusInfoVo[] {
    let result = [] as ExchangeStatusInfoVo[]
    const statusChangeTime = response.basicData?.statusChangeTime ?? undefined
    if (statusChangeTime) {
      const applyExchangeStatus = new ExchangeStatusInfoVo(ExchangeOrderStatusEnum.Apply_Exchange) // 申请发货
      const returningStatus = new ExchangeStatusInfoVo(ExchangeOrderStatusEnum.Returning) // 退货处理中
      const returnFailStatus = new ExchangeStatusInfoVo(ExchangeOrderStatusEnum.Return_Fail) // 退货失败
      const applyDeliveryStatus = new ExchangeStatusInfoVo(ExchangeOrderStatusEnum.Apply_Delivery) // 申请发货
      const deliveryProcessingStatus = new ExchangeStatusInfoVo(ExchangeOrderStatusEnum.Delivery_Processing) // 发货处理中
      const completeExchangeStatus = new ExchangeStatusInfoVo(ExchangeOrderStatusEnum.Complete_Exchange) // 换货完成
      const cancelExchangeStatus = new ExchangeStatusInfoVo(ExchangeOrderStatusEnum.Cancel_Exchange) // 申请关闭
      // 换班失败
      const failOption: ExchangeStatusInfoVo[] = new Array<ExchangeStatusInfoVo>(
        applyExchangeStatus,
        returningStatus,
        returnFailStatus
      )
      // 换班成功
      const successOption: ExchangeStatusInfoVo[] = new Array<ExchangeStatusInfoVo>(
        applyExchangeStatus,
        returningStatus,
        applyDeliveryStatus,
        deliveryProcessingStatus,
        completeExchangeStatus
      )
      // 取消换班
      const cancelOption: ExchangeStatusInfoVo[] = new Array<ExchangeStatusInfoVo>(
        applyExchangeStatus,
        cancelExchangeStatus
      )
      // 换货单状态枚举 =>  接口返回字段
      const statusMap: Map<ExchangeOrderStatusEnum, string> = new Map<ExchangeOrderStatusEnum, string>()
        .set(ExchangeOrderStatusEnum.Apply_Exchange, statusChangeTime.applied ?? '') // 申请换货 => 申请换货
        .set(ExchangeOrderStatusEnum.Returning, statusChangeTime.returning ?? '') // 退货处理中  => 退货中
        .set(ExchangeOrderStatusEnum.Return_Fail, statusChangeTime.returnFailed ?? '') // 退货失败 => 退货失败
        .set(ExchangeOrderStatusEnum.Apply_Delivery, statusChangeTime.deliveryApplied ?? '') // 申请发货 => 申请发货
        .set(ExchangeOrderStatusEnum.Delivery_Processing, statusChangeTime.delivering ?? '') // 发货处理中 => 发货中
        .set(ExchangeOrderStatusEnum.Complete_Exchange, statusChangeTime.exchanged ?? '') // 换货完成 => 换货完成
        .set(ExchangeOrderStatusEnum.Cancel_Exchange, statusChangeTime.closed ?? '') // 申请关闭 => 取消换班
      // 遍历数组赋值
      failOption.forEach((item) => {
        item.date = statusMap.get(item.status) ?? ''
      })
      successOption.forEach((item) => {
        item.date = statusMap.get(item.status) ?? ''
      })
      cancelOption.forEach((item) => {
        item.date = statusMap.get(item.status) ?? ''
      })
      if (statusChangeTime.returnFailed) {
        result = failOption
      } else if (statusChangeTime.closed) {
        result = cancelOption
      } else {
        result = successOption
      }
    }
    // 过滤掉没有时间的项
    result = result.filter((item) => item.date)
    console.log('exchangeStatusList', result)
    return result
  }
}

export default ExchangeClassUtils
