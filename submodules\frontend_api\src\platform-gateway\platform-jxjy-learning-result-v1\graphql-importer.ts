import getImportGradeResultTaskTemplatePath from './queries/getImportGradeResultTaskTemplatePath.graphql'
import pageImportGradeResultTaskInfo from './queries/pageImportGradeResultTaskInfo.graphql'
import exportTaskExcelAllResult from './mutates/exportTaskExcelAllResult.graphql'
import exportTaskExcelAllResultWithName from './mutates/exportTaskExcelAllResultWithName.graphql'
import exportTaskExcelFailedResult from './mutates/exportTaskExcelFailedResult.graphql'
import exportTaskExcelFailedResultWithName from './mutates/exportTaskExcelFailedResultWithName.graphql'
import importIssueLearningResult from './mutates/importIssueLearningResult.graphql'

export {
  getImportGradeResultTaskTemplatePath,
  pageImportGradeResultTaskInfo,
  exportTaskExcelAllResult,
  exportTaskExcelAllResultWithName,
  exportTaskExcelFailedResult,
  exportTaskExcelFailedResultWithName,
  importIssueLearningResult
}
