import { CreateStudentAndPlaceOrderResponse as CreateStudentAndPlaceOrder } from '@api/diff-gateway/platform-jxjypxtypt-zztt-school'
export default class CreateStudentAndPlaceOrderResponse extends CreateStudentAndPlaceOrder {
  /**
   * 学员ID
   */
  userId = ''
  /**
   * 订单号
   */
  orderNo = ''
  /**
   * 子订单号列表
   */
  subOrderNoList: Array<string> = []
  /**
   * 登录token
   */
  token = ''
  /**
   * 参训资格id
   */
  qualificationId = ''
  /**
   * 状态码
   */
  code = ''
  /**
   * 状态信息
   */
  message = ''

  /**
   * 跳转页面
   @see zzttKeyEntity
   商品下单页：PLACEORDER
   学习页：LEARNING
   支付页面：PAY
   */
  key: string
  /**
   * 是否学员自主下单
   */
  isSelfOrder: boolean
  /**
   * skuId
   */
  skuId: string

  get subOrderNo() {
    return this.subOrderNoList?.[0] ?? ''
  }
}
