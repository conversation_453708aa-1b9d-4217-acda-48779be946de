<route-meta>
  {
  "isMenu": true,
  "title": "地区开通统计",
  "sort": 2,
  "icon": "icon-cptj"
  }
  </route-meta>

<script lang="ts">
  import RegionSellStatistic from '@hbfe/jxjy-admin-regionSellStatistic/src/diff/zztt/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    query: [WXGLY],
    export: [WXGLY]
  })
  export default class extends RegionSellStatistic {}
</script>
