<template>
  <el-upload
    style="width: 600px"
    :action="resourceUrl"
    ref="upload"
    :disabled="isDisable ? isDisable : false"
    :auto-upload="true"
    :headers="uploadHeader"
    :on-error="error"
    :on-success="handleSuccess"
    :before-upload="beforeUpload"
    :multiple="true"
    :accept="fileType === 1 ? excelAccepts : certificateAccepts"
    :show-file-list="false"
  >
    <slot>
      <el-button type="primary" plain class="ml20 mt20" icon="el-icon-upload2">选择文件</el-button>
      <div class="el-upload__tip" slot="tip">附件资料格式支持：doc .docx .pdf .ppt .pptx jpg 等格式</div>
    </slot>
  </el-upload>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

  @Component
  export default class HBUploadFileComponent extends Vue {
    @Prop({ type: Number, required: true }) fileType!: number
    @Prop({ type: Boolean, default: false }) isDisable!: boolean
    @Prop({ type: Boolean, default: false }) isProtected!: boolean
    @Prop({ type: Object, required: false }) value!: HBFileUploadResponse

    @Ref('upload') uploader!: any

    excelAccepts = '.doc,.docx,.pdf,.ppt,.pptx,.jpg'
    certificateAccepts = '.p12,.pfx'
    uploadHeader = {}
    $authentication: any
    loading = false

    constructor() {
      super()
      this.uploadHeader = this.$authentication.getRequestHeader()
    }

    get resourceUrl() {
      if (this.isProtected) {
        return (
          ConfigCenterModule.getFrontendApplication(frontendApplication.apiendpoint) +
          '/web/ms-file-v1/web/uploadProtected'
        )
      } else {
        return (
          ConfigCenterModule.getFrontendApplication(frontendApplication.apiendpoint) +
          '/web/ms-file-v1/web/uploadPublic'
        )
      }
    }

    async handleSuccess(response: any, file: any) {
      if (file) {
        this.bindCallBackParam(file, 1)
      } else {
        this.$message.error('文件上传失败')
      }
    }

    bindCallBackParam(file: any, type: number) {
      const hbFileUploadResponse = new HBFileUploadResponse()
      if (type === 1) {
        hbFileUploadResponse.fileName = file.name
        hbFileUploadResponse.url = file.response.data
        this.$emit('submit', hbFileUploadResponse)
      }
    }

    error(error: any) {
      this.$message.error(error)
    }

    beforeUpload(file: any) {
      const fileExtension = file.name.substring(file.name.lastIndexOf('.') + 1)
      let accepts = ''
      if (this.fileType === 1) {
        accepts = this.excelAccepts
      } else if (this.fileType === 2) {
        accepts = this.certificateAccepts
      } else {
        this.$message.error('请指定上传的文件类型')
        return false
      }
      const isExcel = accepts.split(',').includes(`.${fileExtension}`)
      if (!isExcel) {
        this.$message({
          type: 'warning',
          message: '上传文件格式不正确'
        })
        return false
      }
    }
  }
</script>
