import { Page } from '@hbfe/common'
import QueryBatchOrderListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderListVo'
import BatchOrderListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderListDetailVo'
import BatchOrderListStatisticVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderListStatisticVo'
import BatchOrderListPlaceOrderResultVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderListPlaceOrderResultVo'
import { BatchOrderPayModeEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderPayMode'
import { PlaceBatchOrderResultEnum } from '@api/service/management/trade/batch/order/enum/PlaceBatchOrderResult'
import msTradeQuery, {
  BatchOrderSortField,
  BatchOrderSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import DataResolve from '@api/service/common/utils/DataResolve'
import BatchOrderUtils from '@api/service/management/trade/batch/order/query/utils/BatchOrderUtils'
import { BatchOrderTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
import { cloneDeep } from 'lodash'
import QueryBatchOrderListBase from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderListBase'
/**
 * @description 查询批次订单
 */
class QueryBatchOrderList extends QueryBatchOrderListBase {
  totalSize: number

  /**
   * 【集体报名订单】查询列表
   * @param {Page} page - 分页参数
   * @param {QueryBatchOrderListVo} queryParams - 查询参数
   * @return {Promise<BatchOrderListDetailVo[]>} - 【集体报名订单】列表
   */
  async queryBatchOrderList(page: Page, queryParams: QueryBatchOrderListVo): Promise<BatchOrderListDetailVo[]> {
    if (queryParams.buyerName || queryParams.buyerAccount) {
      const userIds = await BatchOrderUtils.getBuyerIdList(queryParams)
      if (!DataResolve.isWeightyArr(userIds)) {
        page.totalSize = 0
        page.totalPageSize = 0
        return [] as BatchOrderListDetailVo[]
      }
    }
    const remoteQueryParams = await queryParams.to()
    // 默认按批次单创建时间降序排
    const sortOption = new BatchOrderSortRequest()
    sortOption.field = BatchOrderSortField.BATCH_ORDER_UN_CONFIRMED_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = [] as BatchOrderSortRequest[]
    sortRequest.push(sortOption)
    const response = await msTradeQuery.pageBatchOrderInServicer({
      page,
      request: remoteQueryParams,
      sortRequest
    })
    page.totalSize = response.data?.totalSize || 0
    page.totalPageSize = response.data?.totalPageSize || 0
    this.totalSize = page.totalSize
    const batchOrderNos: string[] = []
    const successBatch: string[] = []
    const res =
      response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)
        ? await Promise.all(
            response.data.currentPageData.map(async (item) => {
              batchOrderNos.push(item.batchOrderNo)
              // 发票信息
              if (BatchOrderUtils.getBatchOrderStatus(item) === BatchOrderTradeStatusEnum.Pay_Success) {
                successBatch.push(item.batchOrderNo)
                // detail.hasRefundRecord = await BatchOrderUtils.getBatchHasRefundRecord(detail.batchOrderNo)
              }
              return await BatchOrderListDetailVo.fromList(item)
            })
          )
        : ([] as BatchOrderListDetailVo[])
    const billMap = await BatchOrderUtils.getBatchOrderInvoiceInfoListS(response.data.currentPageData)
    const reMap = await BatchOrderUtils.getBatchHasRefundRecords(successBatch)
    // const map = await BatchOrderUtils.getRefundInfoS(batchOrderNos)
    res.forEach((item) => {
      item.refundInfo.enableRefundPersonTime = item.payTimes ?? 0
      item.refundInfo.enableRefundAmount = item.orderAmount ?? 0
      item.refundInfo.enableRefund = item.refundInfo.enableRefundPersonTime > 0
      // if (map.get(item.batchOrderNo)) {
      //   item.refundInfo = map.get(item.batchOrderNo)
      // }
      if (billMap.get(item.batchOrderNo)) {
        item.invoiceInfoList = billMap.get(item.batchOrderNo)
      }
      item.hasRefundRecord = reMap.get(item.batchOrderNo)
    })
    return await this.fillCollectiveInfoForList(res)
  }

  /**
   * 【分销集体报名订单】查询列表
   * @param {Page} page - 分页参数
   * @param {QueryBatchOrderListVo} queryParams - 查询参数
   * @return {Promise<BatchOrderListDetailVo[]>} - 【集体报名订单】列表
   */
  async queryFxBatchOrderList(page: Page, queryParams: QueryBatchOrderListVo): Promise<BatchOrderListDetailVo[]> {
    if (queryParams.buyerName || queryParams.buyerAccount) {
      const userIds = await BatchOrderUtils.getBuyerIdList(queryParams)
      if (!DataResolve.isWeightyArr(userIds)) {
        page.totalSize = 0
        page.totalPageSize = 0
        return [] as BatchOrderListDetailVo[]
      }
    }
    const remoteQueryParams = await queryParams.to()
    // 默认按批次单创建时间降序排
    const sortOption = new BatchOrderSortRequest()
    sortOption.field = BatchOrderSortField.BATCH_ORDER_UN_CONFIRMED_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = [] as BatchOrderSortRequest[]
    sortRequest.push(sortOption)
    const response = await msTradeQuery.pageBatchOrderInDistributor({
      page,
      request: remoteQueryParams,
      sortRequest
    })
    page.totalSize = response.data?.totalSize || 0
    page.totalPageSize = response.data?.totalPageSize || 0
    this.totalSize = page.totalSize
    const batchOrderNos: string[] = []
    const successBatch: string[] = []
    const res =
      response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)
        ? await Promise.all(
            response.data.currentPageData.map(async (item) => {
              batchOrderNos.push(item.batchOrderNo)
              // 发票信息
              if (BatchOrderUtils.getBatchOrderStatus(item) === BatchOrderTradeStatusEnum.Pay_Success) {
                successBatch.push(item.batchOrderNo)
                // detail.hasRefundRecord = await BatchOrderUtils.getBatchHasRefundRecord(detail.batchOrderNo)
              }
              return await BatchOrderListDetailVo.fromList(item)
            })
          )
        : ([] as BatchOrderListDetailVo[])
    const billMap = await BatchOrderUtils.getBatchOrderInvoiceInfoListS(response.data.currentPageData)
    const reMap = await BatchOrderUtils.getFxBatchHasRefundRecords(successBatch)
    // const map = await BatchOrderUtils.getFxRefundInfoS(batchOrderNos)
    res.forEach((item) => {
      item.refundInfo.enableRefundPersonTime = item.payTimes ?? 0
      item.refundInfo.enableRefundAmount = item.orderAmount ?? 0
      item.refundInfo.enableRefund = item.refundInfo.enableRefundPersonTime > 0
      if (billMap.get(item.batchOrderNo)) {
        item.invoiceInfoList = billMap.get(item.batchOrderNo)
      }
      item.hasRefundRecord = reMap.get(item.batchOrderNo)
    })
    return await this.fillCollectiveInfoForList(res)
  }

  /**
   * 【集体报名订单】查询列表统计数据
   * @param {QueryBatchOrderListVo} queryParams - 查询参数
   * @return {Promise<BatchOrderListStatisticVo>}
   */
  async queryBatchOrderListStatistic(queryParams: QueryBatchOrderListVo): Promise<BatchOrderListStatisticVo> {
    const result = new BatchOrderListStatisticVo()
    if (queryParams.buyerName || queryParams.buyerAccount) {
      const userIds = await BatchOrderUtils.getBuyerIdList(queryParams)
      if (!DataResolve.isWeightyArr(userIds)) return result
    }
    result.totalOrderCount = this.totalSize
    result.totalOrderAmount = await this.getBatchOrderTotalAmount(queryParams)
    return result
  }

  /**
   * 【集体报名分销订单】查询列表统计数据
   * @param {QueryBatchOrderListVo} queryParams - 查询参数
   * @return {Promise<BatchOrderListStatisticVo>}
   */
  async queryFxBatchOrderListStatistic(queryParams: QueryBatchOrderListVo): Promise<BatchOrderListStatisticVo> {
    const result = new BatchOrderListStatisticVo()
    if (queryParams.buyerName || queryParams.buyerAccount) {
      const userIds = await BatchOrderUtils.getBuyerIdList(queryParams)
      if (!DataResolve.isWeightyArr(userIds)) return result
    }
    result.totalOrderCount = this.totalSize
    result.totalOrderAmount = await this.getFxBatchOrderTotalAmount(queryParams)
    return result
  }

  /**
   * 【集体报名订单】查看下单结果
   * @param {string} batchOrderNo - 批次单id
   * @return {Promise<BatchOrderListPlaceOrderResultVo>}
   */
  async queryPlaceBatchOrderResult(batchOrderNo: string): Promise<BatchOrderListPlaceOrderResultVo> {
    const result = new BatchOrderListPlaceOrderResultVo()
    const response = await msTradeQuery.getBatchOrderInServicer(batchOrderNo)
    if (response.status?.isSuccess()) {
      const data = response.data
      result.batchOrderNo = data.batchOrderNo ?? ''
      result.payPersonTime = data.basicData?.orderForBatchCount ?? 0
      result.amount = data.basicData?.amount ?? 0
      result.payMode =
        data.payInfo?.paymentOrderType === 1
          ? BatchOrderPayModeEnum.Online_Pay
          : data.payInfo?.paymentOrderType === 2
          ? BatchOrderPayModeEnum.Offline_Pay
          : null
      result.placeOrderResult =
        data.basicData?.deliveryStatus === 2
          ? PlaceBatchOrderResultEnum.Complete_Process
          : PlaceBatchOrderResultEnum.Processing
      result.placeOrderTime = data.basicData?.batchOrderStatusChangeTime?.committing ?? ''
      result.paySuccessTime = data.basicData?.paymentStatusChangeTime?.paid ?? ''
      result.currentBatchPayPersonTime = data.basicData?.orderForBatchCount ?? 0
      result.successPersonTime = data.basicData?.orderForBatchCount ?? 0
      result.payTerminal = data.basicData?.terminalCode
    }
    return result
  }

  /**
   * 【集体报名订单】查看下单结果）（分销商）
   * @param {string} batchOrderNo - 批次单id
   * @return {Promise<BatchOrderListPlaceOrderResultVo>}
   */
  async queryFxPlaceBatchOrderResult(batchOrderNo: string): Promise<BatchOrderListPlaceOrderResultVo> {
    const result = new BatchOrderListPlaceOrderResultVo()
    const response = await msTradeQuery.getBatchOrderInDistributor(batchOrderNo)
    if (response.status?.isSuccess()) {
      const data = response.data
      result.batchOrderNo = data.batchOrderNo ?? ''
      result.payPersonTime = data.basicData?.orderForBatchCount ?? 0
      result.amount = data.basicData?.amount ?? 0
      result.payMode =
        data.payInfo?.paymentOrderType === 1
          ? BatchOrderPayModeEnum.Online_Pay
          : data.payInfo?.paymentOrderType === 2
          ? BatchOrderPayModeEnum.Offline_Pay
          : null
      result.placeOrderResult =
        data.basicData?.deliveryStatus === 2
          ? PlaceBatchOrderResultEnum.Complete_Process
          : PlaceBatchOrderResultEnum.Processing
      result.placeOrderTime = data.basicData?.batchOrderStatusChangeTime?.committing ?? ''
      result.paySuccessTime = data.basicData?.paymentStatusChangeTime?.paid ?? ''
      result.currentBatchPayPersonTime = data.basicData?.orderForBatchCount ?? 0
      result.successPersonTime = data.basicData?.orderForBatchCount ?? 0
      result.payTerminal = data.basicData?.terminalCode
    }
    return result
  }

  /**
   * 获取批次单列表总数
   */
  private async getBatchOrderTotalCount(queryParams: QueryBatchOrderListVo): Promise<number> {
    let result = 0
    // 获取下单数
    const remoteQueryParams = await queryParams.to()
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    // 默认按批次单创建时间降序排
    const sortOption = new BatchOrderSortRequest()
    sortOption.field = BatchOrderSortField.BATCH_ORDER_UN_CONFIRMED_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = [] as BatchOrderSortRequest[]
    sortRequest.push(sortOption)
    const response = await msTradeQuery.pageBatchOrderInServicer({
      page,
      request: remoteQueryParams,
      sortRequest
    })
    if (response.status?.isSuccess()) {
      result = response.data?.totalSize
    }
    return result
  }

  /**
   * 获取批次单列表交易成功总金额
   */
  async getBatchOrderTotalAmount(queryParams: QueryBatchOrderListVo): Promise<number> {
    let result = 0
    const params = cloneDeep(queryParams)
    params.orderStatus = BatchOrderTradeStatusEnum.Pay_Success
    const remoteQueryParams = await params.to()
    const response = await msTradeQuery.statisticBatchOrderInServicer(remoteQueryParams)
    if (response.status?.isSuccess()) {
      result = response.data?.totalBatchOrderAmount ?? 0
    }
    return result
  }

  /**
   * 获取分销批次单列表交易成功总金额
   */
  private async getFxBatchOrderTotalAmount(queryParams: QueryBatchOrderListVo): Promise<number> {
    let result = 0
    const params = cloneDeep(queryParams)
    params.orderStatus = BatchOrderTradeStatusEnum.Pay_Success
    const remoteQueryParams = await params.to()
    const response = await msTradeQuery.statisticBatchOrderInDistributor(remoteQueryParams)
    if (response.status?.isSuccess()) {
      result = response.data?.totalBatchOrderAmount ?? 0
    }
    return result
  }
}

export default QueryBatchOrderList
