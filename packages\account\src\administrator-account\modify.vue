<route-params content="/:id"></route-params>
<template>
  <el-main>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/basic-data/account/administrator-account' }">
        管理员号管理
      </el-breadcrumb-item>
      <el-breadcrumb-item>修改账号</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基本信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="userDetail" label-width="120px" class="m-form">
                <el-form-item label="帐号：" required>
                  {{ userDetail.adminAccount }}
                </el-form-item>
                <el-form-item label="姓名 / 昵称：" required>
                  <el-input v-model="userDetail.userName" clearable placeholder="请输入姓名 / 昵称" class="form-m" />
                </el-form-item>
                <el-form-item label="性别：" required>
                  <el-radio-group v-model="userDetail.gender">
                    <el-radio :label="1">男</el-radio>
                    <el-radio :label="0">女</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="手机号：" required>
                  <el-input v-model="userDetail.phone" clearable placeholder="请输入手机号" class="form-m" />
                </el-form-item>
                <el-form-item label="邮箱：">
                  <el-input v-model="userDetail.email" clearable placeholder="请输入邮箱" class="form-m" />
                </el-form-item>
                <el-form-item label="启用状态：" required>
                  <el-radio-group v-model="userDetail.status">
                    <el-radio :label="1">启用</el-radio>
                    <el-radio :label="2">禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">分配角色</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="form" label-width="120px" class="m-form">
                <el-form-item required>
                  <el-button type="primary" icon="el-icon-plus" class="f-mb20" @click="openAddRoleDialog()"
                    >添加角色</el-button
                  >
                  <!-- 角色列表 -->
                  <el-table stripe :data="roleInfoList" max-height="500px" class="m-table" v-if="roleInfoList.length">
                    <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                    <el-table-column label="角色" min-width="180">
                      <template slot-scope="scope">{{ scope.row.name }}</template>
                    </el-table-column>
                    <el-table-column label="说明" min-width="300">
                      <template slot-scope="scope">{{ scope.row.description }}</template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" align="center" fixed="right">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="closeRoleInfo(scope.row.id)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-empty v-if="!roleInfoList.length" :image-size="40" description="暂无数据，请添加角色~" />
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="goBack">放弃编辑</el-button>
        <el-button type="primary" :loading="isLoading" @click="save">保存</el-button>
      </div>
    </div>
    <choose-list
      v-if="showRoleDialog"
      :value.sync="showRoleDialog"
      @input="input"
      @confirmDialog="confirmDialog"
      :roleList="roleInfoList"
    ></choose-list>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue, Watch } from 'vue-property-decorator'
  import UserModule from '@api/service/management/user/UserModule'
  import PageAdminInfoResponse from '@api/service/management/user/query/manager/vo/PageAdminInfoResponse'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import RoleInfoResponseVo from '@api/service/management/authority/role/query/vo/RoleInfoResponseVo'
  import CreateSubAdminRequestVo from '@api/service/management/user/mutation/manager/system-manager/vo/CreateSubAdminRequestVo'
  import chooseList from '@hbfe/jxjy-admin-account/src/administrator-account/__components__/choose-role.vue'
  import UpdateOnlineSchoolSubAdminRequestVo from '@api/service/management/user/mutation/manager/system-manager/vo/UpdateOnlineSchoolSubAdminRequestVo'
  import ServiceProviderFactory from '@api/service/management/authority/service-provider/ServiceProviderFactory'
  import MutationServiceProvider from '@api/service/management/authority/service-provider/mutation/MutationServiceProvider'
  import { UnbindPhoneRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
  class RoleInfoResponse extends RoleInfoResponseVo {
    isChecked: boolean
  }
  class CreateSubAdminRequest extends CreateSubAdminRequestVo {
    certainPassword: string
  }
  @Component({
    components: { chooseList }
  })
  export default class extends Vue {
    isLoading = false
    radio = 3
    userDetail: PageAdminInfoResponse = new PageAdminInfoResponse()
    roleInfoList: Array<RoleInfoResponseVo> = new Array<RoleInfoResponseVo>()
    showRoleDialog = false
    form = new CreateSubAdminRequest()
    queryRole = AuthorityModule.roleFactory.getQueryRoleList()
    initParam: PageAdminInfoResponse = new PageAdminInfoResponse()
    UpdateSystemManager = UserModule.mutationUserFactory.updateSystemManager
    mutationServiceProvider = new MutationServiceProvider()

    async created() {
      this.userDetail = await UserModule.queryUserFactory.queryManager.queryAdminInfo(this.$route.params.id)
      await this.queryRole.queryRoleList()
      this.roleInfoList = this.queryRole.roleList
      this.roleInfoList = this.roleInfoList.filter(
        (item) =>
          this.userDetail.roleList &&
          this.userDetail.roleList.length &&
          this.userDetail.roleList.some((ele) => ele.roleId === item.id)
      )
      this.UpdateSystemManager.updateOnlineSchoolSubAdminParams = new UpdateOnlineSchoolSubAdminRequestVo()
      this.initParam.userId = this.userDetail.userId
      this.initParam.email = this.userDetail.email
      this.initParam.gender = this.userDetail.gender
      this.initParam.userName = this.userDetail.userName
      this.initParam.phone = this.userDetail.phone
      this.UpdateSystemManager.updateOnlineSchoolSubAdminParams.originalRoleIds = this.roleInfoList.map((item) => {
        return item.id
      })
    }
    //打开
    async openAddRoleDialog() {
      this.showRoleDialog = true
    }
    input(showRoleDialog: boolean) {
      this.showRoleDialog = showRoleDialog
    }
    //确认
    confirmDialog(result: Array<RoleInfoResponse>) {
      this.roleInfoList = []
      if (result.length > 0) {
        for (let i = 0; i < result.length; i++) {
          if (result[i].isChecked) {
            this.roleInfoList.push(result[i])
          }
        }
      }
    }
    //删除列表
    closeRoleInfo(id: string) {
      this.roleInfoList = this.roleInfoList.filter((i) => i.id !== id)
    }
    // 返回上一页
    goBack() {
      this.$router.push('/basic-data/account/administrator-account')
    }

    async save() {
      this.isLoading = true
      const updateParam = this.comparisonParams(this.userDetail)

      const reg = new RegExp(/^[1]([3-9])[0-9]{9}$/)
      if (!this.userDetail.phone) {
        this.$message.error('请输入手机号码')
        this.isLoading = false
        return
      } else if (!reg.test(this.userDetail.phone)) {
        this.$message.error('请输入11位真实有效手机号')
        this.isLoading = false
        return
      }
      this.UpdateSystemManager.updateOnlineSchoolSubAdminParams.accountId = updateParam.accountId
      this.UpdateSystemManager.updateOnlineSchoolSubAdminParams.gender = updateParam.gender
      this.UpdateSystemManager.updateOnlineSchoolSubAdminParams.status = updateParam.status
      this.UpdateSystemManager.updateOnlineSchoolSubAdminParams.phone = updateParam.phone
      this.UpdateSystemManager.updateOnlineSchoolSubAdminParams.email = updateParam.email
      this.UpdateSystemManager.updateOnlineSchoolSubAdminParams.name = updateParam.userName
      this.UpdateSystemManager.updateOnlineSchoolSubAdminParams.roleIds = this.roleInfoList.map((item) => {
        return item.id
      })

      try {
        if (this.UpdateSystemManager.updateOnlineSchoolSubAdminParams.phone === '') {
          const unbindPhoneParam = new UnbindPhoneRequest()
          unbindPhoneParam.phone = this.initParam.phone
          unbindPhoneParam.userId = this.initParam.userId
          const unbindRes = await this.mutationServiceProvider.unbindPhone(unbindPhoneParam)
          if (unbindRes?.status?.code === 200) {
            console.log('ok')
          } else {
            this.$message.error('解绑手机失败')
            this.isLoading = false
            return
          }
        }

        const res = await this.UpdateSystemManager.updateOnlineSchoolSubAdminByToken()
        if (res.status.isSuccess()) {
          if (res.data?.code === '200') {
            this.$message.success('修改成功')
            this.isLoading = false
            await this.$router.push('/basic-data/account/administrator-account')
          } else {
            this.$message.error(res.data?.message)
            this.isLoading = false
          }
        } else {
          this.$message.error('重置失败')
          this.isLoading = false
        }
      } catch (e) {
        this.$message.error('重置失败')
        this.isLoading = false
      }
    }

    comparisonParams(conParam: PageAdminInfoResponse): PageAdminInfoResponse {
      const param = new PageAdminInfoResponse()
      param.accountId = conParam.accountId
      param.status = conParam.status
      param.email = conParam.email === this.initParam.email ? null : conParam.email
      param.gender = conParam.gender === this.initParam.gender ? null : conParam.gender
      param.userName = conParam.userName === this.initParam.userName ? null : conParam.userName
      param.phone = conParam.phone

      return param
    }
  }
</script>
