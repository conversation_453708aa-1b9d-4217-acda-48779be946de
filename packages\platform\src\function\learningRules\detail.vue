<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/basic-data/platform/function' }">学习规则</el-breadcrumb-item>
      <el-breadcrumb-item>学习规则详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header">
        <div slot="header" class="">
          <span class="tit-txt">基础设置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="basicInfoForm" label-width="auto" class="m-form f-pt30">
              <el-form-item label="适用行业：" class="is-text">{{ basicInfoForm.industryName }}</el-form-item>
              <el-form-item label="适用范围：">
                {{ getApplyRangeName() }}
                <el-table
                  :data="regionList"
                  max-height="500px"
                  class="m-table f-mt5"
                  border
                  ref="regionTable"
                  v-loading="loading"
                  v-if="basicInfoForm.applyRange == ApplyRangeEnum.region"
                >
                  <el-table-column type="index" label="No." width="60"></el-table-column>
                  <el-table-column label="省份" min-width="100">
                    <template v-slot="{ row }">{{ diffRegion(row, '省份') }} </template>
                  </el-table-column>
                  <el-table-column label="地市" min-width="100">
                    <template v-slot="{ row }"> {{ diffRegion(row, '地市') }}</template>
                  </el-table-column>
                  <el-table-column label="区县" min-width="240">
                    <template v-slot="{ row }"> {{ diffRegion(row, '区县') }}</template>
                  </el-table-column>
                </el-table>
                <el-table
                  :data="schemeList"
                  max-height="500px"
                  class="m-table f-mt5"
                  border
                  v-loading="loading"
                  v-if="basicInfoForm.applyRange == ApplyRangeEnum.scheme"
                >
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="培训方案名称" min-width="220">
                    <template v-slot="{ row }">{{ row.schemeName }}</template>
                  </el-table-column>
                  <el-table-column label="方案属性" min-width="240">
                    <template v-slot="{ row }">
                      <sku-display :sku-item="row"></sku-display>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
              <el-form-item label="指定培训年度学习时间：">
                <div class="f-mb5" v-for="(item, index) in basicInfoForm.yearTrainRangeList" :key="index">
                  <el-tag size="small" class="f-mr20 f-vm">年度：{{ item.year }}</el-tag>
                  <span class="f-mr30">{{ item.startTime }} 至 {{ item.endTime }}</span>
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mt15">
        <div slot="header" class="">
          <span class="tit-txt">规则设置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <p class="f-mtb30">
              <i class="f-fb f-mr10">学员同一个天内报名多个培训班时，是否需要错开开始学习日期：</i>{{ isNeedStagger }}
            </p>
          </el-col>
        </el-row>

        <!--课程学习规则-->
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">课程学习规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="rulesInfoForm" label-width="auto" class="m-form f-mt30">
              <el-form-item label="每天学习时间：" class="is-text">00:00:00 至 23:59:59</el-form-item>
              <el-form-item label="每天不学习时间：" class="is-text">
                <div v-for="(item, index) in rulesInfoForm.notLearningTimes" :key="index">
                  <p>{{ item.startTime }} 至 {{ item.endTime }}</p>
                </div>
              </el-form-item>
              <el-form-item label="首次开始学习时间：" class="is-text">
                开通班级的
                <i class="f-mlr5 f-cb">{{ rulesInfoForm.firstLearningStartTime }}</i> ~
                <i class="f-mlr5 f-cb">{{ rulesInfoForm.firstLearningEndTime }}</i>
                天内随机开始学习。
              </el-form-item>
              <el-form-item label="每天最多学习时长：" class="is-text">
                每天课程学习最多
                <i class="f-mlr5 f-cb">{{ rulesInfoForm.maxLearningTime }}</i>
                {{ basicInfoForm.timeMode === timeModeEnum.learning ? '学时' : '小时' }}
                且每次学习时长达到 <i class="f-mlr5 f-cb">{{ rulesInfoForm.learningTime }}</i>
                {{ basicInfoForm.timeMode === timeModeEnum.learning ? '学时' : '小时' }}
                ，随机休息 60~180 分钟。
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <!--测验规则-->
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">测验规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <div class="f-mt30 f-flex f-align-center">
              <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>
              <span class="f-flex-sub"
                >课程学习结束后进入对应的课程测验，测验开始时间和结束时间随机间隔分钟数15-60分钟。</span
              >
            </div>
          </el-col>
        </el-row>

        <!--考试规则-->
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">考试规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <div class="f-mt30 f-flex f-align-center">
              <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>
              <span class="f-flex-sub"
                >课程学习结束后进入考试，考试开始时间和结束时间随机间隔分钟数，最少间隔总考试时长的三分之一的时间。</span
              >
            </div>
            <el-alert type="info" :closable="false" class="m-alert f-mt10">
              例：考试总时长60分钟，开始和结束时间至少间隔20分钟。
            </el-alert>
          </el-col>
        </el-row>

        <!--特殊规则-->
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">特殊规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
              <el-form-item label=" ">
                若重新计算的学习合格时间还是超过学习区间，则开通时间随机提前
                <i class="f-mlr5 f-cb">{{ rulesInfoForm.minAdvanceDay }}</i>
                <i class="f-mlr5">～</i>
                <i class="f-mlr5 f-cb">{{ rulesInfoForm.maxAdvanceDay }}</i>
                天。
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import LearningRuleItem from '@api/service/management/learning-rule/LearningRuleItem'
  import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'
  import { ApplyRangeEnum } from '@api/service/management/learning-rule/enum/ApplyRangeEnum'
  import { ElTable } from 'element-ui/types/table'
  import RegionTreeItem from '@api/service/management/learning-rule/model/RegionTreeItem'
  import { UiPage } from '@hbfe/common'
  import RuleSchemeParams from '@api/service/management/train-class/query/vo/RuleSchemeParams'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import SkuDisplay from '@hbfe/jxjy-admin-platform/src/function/components/skuDisplay.vue'
  import { TimeModeEnum } from '@api/service/management/learning-rule/enum/TimeModeEnum'

  @Component({
    components: { SkuDisplay }
  })
  export default class extends Vue {
    @Ref('regionTable') regionTable: ElTable
    form = {}
    tableData = [{}]
    // 加载中
    isLoading = false
    // 表单初始化
    learningRuleForm = new LearningRuleItem()
    // 基础设置表单
    basicInfoForm = this.learningRuleForm.basicInfo
    // 规则设置表单
    rulesInfoForm = this.learningRuleForm.ruleInfo
    // 方案列表
    schemeList = new Array<RuleSchemeItem>()
    // 基础设置地区table列表
    regionList: Array<RegionTreeItem> = []
    // 适用范围枚举
    ApplyRangeEnum = ApplyRangeEnum
    // 列表加载中
    loading = false
    /**
     * 时长方式枚举
     */
    timeModeEnum = TimeModeEnum

    get isNeedStagger() {
      return this.rulesInfoForm.staggerStartTrainingTime ? '需要错开学习开始日期' : '不需要错开学习开始日期'
    }

    // 查询方案
    async querySchemeList() {
      const page = new UiPage()
      page.pageSize = this.learningRuleForm.basicInfo.schemeIds.length
      const queryParams = new RuleSchemeParams()
      queryParams.schemeIds = this.learningRuleForm.basicInfo.schemeIds
      this.schemeList = await new QueryTrainClassCommodityList().pageRuleSchemeList(page, queryParams)
    }
    // 初始化
    async init() {
      const res = await this.learningRuleForm.getDetail(this.$route.params.id)
      if (!res.isSuccess()) {
        this.$message.error('获取详情失败，原因：' + res.getMessage())
        return
      }
      this.basicInfoForm = this.learningRuleForm.basicInfo
      this.rulesInfoForm = this.learningRuleForm.ruleInfo
      this.loading = true
      // 只有方案类型的需要查询方案列表
      if (this.basicInfoForm.applyRange == ApplyRangeEnum.scheme) {
        await this.querySchemeList()
        this.loading = false
      }
      // 只有地区类型的需要查询地区树
      if (this.basicInfoForm.applyRange == ApplyRangeEnum.region) {
        // 初始化地区树
        this.basicInfoForm
          .getRegionTree()
          .then(() => {
            // 获取地区列表
            this.queryRegionList()
          })
          .finally(() => {
            this.loading = false
          })
        this.getApplyRangeName()
      }
    }

    async created() {
      this.init()
    }
    // 获取地区列表
    queryRegionList() {
      this.regionList = this.basicInfoForm.getSelectedRegion()
    }

    // 适用范围名字
    getApplyRangeName() {
      return this.basicInfoForm.applyRange == ApplyRangeEnum.platform
        ? '平台级别'
        : this.basicInfoForm.applyRange == ApplyRangeEnum.region
        ? '地区级别'
        : '培训方案级别'
    }

    // 差异化地区，北京、上海、天津、重庆、澳门、香港
    diffRegion(item: RegionTreeItem, region: string) {
      if (
        item.names[0] == '北京市' ||
        item.names[0] == '上海市' ||
        item.names[0] == '天津市' ||
        item.names[0] == '重庆市' ||
        item.names[0] == '澳门特别行政区' ||
        item.names[0] == '香港特别行政区'
      ) {
        if (region == '省份') {
          return '-'
        } else if (region == '地市') {
          return item.names[0] || '-'
        } else {
          return item.childrenNames.join('、') || '-'
        }
      } else {
        if (region == '省份') {
          return item.names[0] || '-'
        } else if (region == '地市') {
          return item.names[1] || '-'
        } else {
          return item.childrenNames.join('、') || '-'
        }
      }
    }
  }
</script>
