<route-meta>
{
"title": "上传组件"
}
</route-meta>
<!--sku属性-->
<template>
  <el-upload
    style="width: 600px"
    :action="resourceUrl"
    ref="upload"
    :disabled="isDisable ? isDisable : false"
    :auto-upload="false"
    :headers="uploadHeader"
    :on-error="error"
    :on-exceed="handleExceed"
    :on-remove="handleRemove"
    :on-change="change"
    :on-success="handleSuccess"
    :http-request="uploadFile"
    :file-list="fileList"
    :multiple="true"
    :limit="1"
    :accept="fileType === 1 ? excelAccepts : certificateAccepts"
    :data="{
      bizType: BizTypeEnum.TYPT_KCXX,
      owner: this.serverId,
      sign: '外链课件导入',
      userId: this.userId,
      isPublic: false
    }"
  >
    <slot>
      <el-button type="primary" plain class="ml20 mt20" icon="el-icon-upload2">选择文件 </el-button>
    </slot>
  </el-upload>
</template>

<script lang="ts">
  import { Component, Emit, Prop, Ref, Watch, Vue } from 'vue-property-decorator'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import UploadUtil from '@api/service/common/obs/UploadUtil'
  import { HttpRequestOptions } from 'element-ui/types/upload'
  import ObsUpLoad from '@api/service/common/obs/ObsUpLoad'
  import Context from '@api/service/common/context/Context'
  import { BizTypeEnum } from '@api/service/common/obs/BizTypeEnum'
  import ObsReceive from '@api/service/common/obs/ObsReceive'
  import UserModule from '@api/service/management/user/UserModule'

  @Component
  export default class HBUploadFileComponent extends Vue {
    /**
     * 1、excel
     * 2、微信证书
     */
    @Prop({
      type: Number,
      required: true
    })
    fileType: number

    @Prop({
      type: Boolean,
      default: false
    })
    isDisable: boolean

    @Prop({
      type: Object
    })
    value: HBFileUploadResponse

    @Ref('upload') uploader: any
    // 资源服务地址
    resourceUrl = ''
    /**
     * excel的文件类型
     */
    excelAccepts = '.xls,.xlsx'
    /**
     * 微信证书的默认文件后缀
     */
    certificateAccepts = '.p12,.pfx'
    uploadHeader = {}
    $authentication: any
    loading = false
    fileList: Array<any> = new Array<any>()
    uploadToken = ''
    userId = UserModule.queryUserFactory.queryManagerDetail.adminInfo.userInfo.userId

    // * obs文件访问
    obsReceive: ObsReceive = new ObsReceive()
    obsUpLoad = new ObsUpLoad()
    // obs枚举
    BizTypeEnum = BizTypeEnum
    // 上传实例化
    uploadUtil = new UploadUtil()
    // 网校id
    serverId = Context.servicerInfo.id
    constructor() {
      super()
      this.uploadHeader = this.$authentication.getRequestHeader()
    }

    @Watch('value', {
      immediate: true,
      deep: true
    })
    changeFileList() {
      this.fileList = new Array<any>()
      if (this.value.url) {
        this.fileList.push({
          name: this.value.fileName,
          url: this.value.url
        })
      }
    }

    async addInitFile(list: Array<HBFileUploadResponse>) {
      for (const item of list) {
        this.fileList.push({
          name: item.fileName
        })
      }
    }
    handleExceed() {
      this.$message.error('每次只能上传一个文件，如需重新上传，请取消已上传的文件!')
      return false
    }

    handleRemove(file: any, fileList: any) {
      //console.log(file, fileList)
      this.bindCallBackParam(fileList, 0)
    }

    async handleSuccess(file1: any, fileList: any) {
      //console.log(fileList, 'handleSuccess')
      const file = fileList
      if (file) {
        this.bindCallBackParam(file, 1)
      } else {
        this.$message.error('文件上传失败')
      }
      return false
    }

    @Emit('input')
    bindCallBackParam(val: any, type: number) {
      const hbFileUploadResponse = new HBFileUploadResponse()
      // Object.assign(hbFileUploadResponse, val)
      if (type === 1) {
        // console.log(val)
        hbFileUploadResponse.fileName = val.name
        hbFileUploadResponse.url = val.response.data
      } else {
        hbFileUploadResponse.fileName = ''
        hbFileUploadResponse.url = ''
      }
      return hbFileUploadResponse
    }

    error(error: any) {
      this.$message.error(error)
      return false
    }

    async change(file: any, fileList: Array<any>) {
      const fileExtension = file.name.substring(file.name.lastIndexOf('.') + 1)
      let accepts = ''
      if (this.fileType === 1) {
        accepts = this.excelAccepts
      } else if (this.fileType === 2) {
        accepts = this.certificateAccepts
      } else {
        this.$message.error('请指定上传的文件类型')
        return
      }
      const isExcel = accepts.split(',').includes(`.${fileExtension}`)
      if (!isExcel) {
        this.$message({
          type: 'warning',
          message: '请选择 【' + accepts + '】 格式的文件!'
        })
        fileList.splice(
          fileList.findIndex(f => f.uid === file.uid),
          1
        )
        return
      }
      this.uploader.submit()
      return false
    }

    /**
     * 上传方法
     */
    async uploadFile(file: HttpRequestOptions) {
      const uploadUtil = new UploadUtil()
      const request = new ObsUpLoad()
      let token
      // debugger
      if ((file.data as any)?.bizType && (file.data as any)?.sign) {
        token = await request.getToken({
          bizType: (file.data as any).bizType,
          owner: (file.data as any).owner,
          sign: (file.data as any).sign,
          ownerType: (file.data as any).ownerType
        })
      } else {
        return Promise.reject({ code: 500, message: '缺少token必要入参' })
      }
      if ((file.data as any).isPublic === undefined) {
        return Promise.reject({ code: 500, message: '缺少isPublic字段' })
      }
      // 文件名转换防止中文文件名上传报错
      const newFile = new File([file.file], encodeURIComponent(file.file.name), {
        type: file.file.type
      })
      console.debug(file.file)
      console.debug(newFile)
      //不确定使用这种判断文件是图片还是别的
      if (file.file.type.includes('image')) {
        const req = await uploadUtil.doUploadBase64(newFile, (file.data as any).isPublic, token)
        if (req.data.code == 200 && typeof req.data.data == 'string') {
          // this.upload.fileList =
          return Promise.resolve(req.data)
        } else {
          return Promise.reject(req.data)
        }
      } else {
        if (!(file.data as any)?.userId) {
          return Promise.reject({ code: 500, message: '缺少用户ID' })
        }
        const req = await uploadUtil.uploadFile(newFile, (file.data as any).isPublic, (file.data as any).userId, token)
        if (req.data.code == 200 && typeof req.data.data == 'string') {
          return Promise.resolve(req.data)
        } else {
          return Promise.reject(req.data)
        }
      }
    }
  }
</script>
