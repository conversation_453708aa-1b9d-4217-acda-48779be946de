import Monitor, { getEnv } from '@/common/monitor/Monitor'

const env = process.env.NODE_ENV
const branch = process.env.GIT_INFO_BRANCH

export const webfunnyDefaultUserId = `${env}_${branch}_tourist`

class WebFunny extends Monitor {
  userId = ''
  private key = 'wmUserInfo'
  private tag = `admin_${env}`
  private branch = branch

  launch() {
    if (this.config.enable) {
      this.setStorageInfo('tourist')
      require('./webfunny.js')
    }
  }

  /**
   * @param userId
   */
  setStorageInfo(userId: string) {
    const id = `${env}_${this.branch}_${userId}`
    window.localStorage.setItem(
      this.key,
      JSON.stringify({
        userId: id,
        userTag: this.tag,
        projectVersion: this.branch,
        env: getEnv() // * 暂时屏蔽 目前只用一个探针 这个字段区分webfunny环境
      })
    )
  }
}

export default new WebFunny()
