import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
import MutationTrainClassCommodityClass from '@api/service/management/train-class/mutation/MutationTrainClassCommodityClass'
import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
import CertificateVo from '@api/service/management/train-class/mutation/vo/CertificateVo'
import TrainClassBaseModel from '@api/service/diff/management/qztg/train-class/model/TrainClassBaseModel'

/**
 * 培训班商品详情Vo
 */
class TrainClassDetailClassVo {
  // region properties
  /**
   *可见的购买渠道，1：用户自主购买，2：集体缴费，3：管理员导入4：所有，类型为number[]
   */
  visibleChannelList: number[] = []
  /**
   *商品抬头，类型为string
   */
  saleTitle = ''
  /**
   *商品id，类型为string
   */
  commoditySkuId = ''
  /**
   * 分类id
   */
  categoryId = ''
  /**
   *价格，类型为number
   */
  price = 0
  /**
   *是否关闭学员报名，类型为boolean
   */
  closeCustomerPurchase = false
  /**
   *上架计划时间，类型为string
   */
  onShelvesPlanTime = ''
  /**
   * 税务编码
   */
  taxCode = ''

  /**
   *下架计划时间，类型为string
   */
  offShelvesPlanTime = ''
  /**
   *是否立即上架，类型为boolean
   */
  onShelves = true
  /**
   *培训班基础信息，类型为TrainClassBaseModel
   */
  trainClassBaseInfo = new TrainClassBaseModel()
  /**
   *学习方式，类型为LearningType
   */
  learningTypeModel = new LearningType()
  /**
   *学习方式初始数据，类型为LearningType，修改用
   */
  learningTypeModelCopy = new LearningType()
  /**
   * 证明(修改方案判断证明是否移除)
   */
  certificate = new CertificateVo()
  // endregion
  // region methods

  /**
   * 获取商品业务对象
   */
  getDoCommodity(): MutationTrainClassCommodityClass {
    const mutationClass = TrainClassManagerModule.mutationTrainClassFactory.getMutationTrainClassCommodityClass()
    mutationClass.schemeId = this.trainClassBaseInfo.id
    mutationClass.commoditySkuId = this.commoditySkuId
    return mutationClass
  }

  // endregion
}
export default TrainClassDetailClassVo
