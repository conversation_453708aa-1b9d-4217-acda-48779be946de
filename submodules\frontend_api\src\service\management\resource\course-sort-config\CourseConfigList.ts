import CourseConfigItem from '@api/service/management/resource/course-sort-config/model/CourseConfigItem'
import {
  CourseSortRuleConfigRequest,
  CourseSortRuleConfigResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import CourseLearningBackstage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { Page } from '@hbfe/common'

/**
 * 课程排序配置列表类
 */
export default class CourseConfigList {
  /**
   * 筛选参数
   */
  filterParam: CourseSortRuleConfigRequest = new CourseSortRuleConfigRequest()

  /**
   * 配置数组
   */
  configItems: Array<CourseConfigItem> = new Array<CourseConfigItem>()

  /**
   * 查询配置数组
   * @param page 页码
   */
  async queryConfigItems(page: Page): Promise<Array<CourseConfigItem>> {
    const res = await CourseLearningBackstage.pageCourseSortRuleConfigInServicer({ page, request: this.filterParam })

    this.configItems =
      (res?.data?.currentPageData?.length &&
        res?.data?.currentPageData.map((item: CourseSortRuleConfigResponse) => CourseConfigItem.from(item))) ||
      []

    page.totalSize = res.data?.totalSize
    page.totalPageSize = res.data?.totalPageSize

    return this.configItems
  }
}
