schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""渠道商详情"""
	detail(id:String):ServicerDetailDto @NotAuthenticationRequired
	"""渠道商分页"""
	page(page:Page,params:ChannelVendorQueryParams):ChannelVendorListDtoPage @page(for:"ChannelVendorListDto")
	"""渠道商申请记录分页
		for培训机构
	"""
	pageApplyRecordForTrainingInstitution(page:Page,params:ChannelVendorApplyRecordQueryParams):ChannelVendorApplyRecordDtoPage @page(for:"ChannelVendorApplyRecordDto")
	"""与当前培训机构角色签约的渠道商查询条件
		@param page   分页
		@param params 查询条件
		@return 渠道商列表
	"""
	pageForTrainingInstitution(page:Page,params:ChannelVendorForTInstitutionQueryParams):ChannelVendorForTIDtoPage @page(for:"ChannelVendorForTIDto")
}
type Mutation {
	"""培训机构同意渠道商开通"""
	agreeApplyForTrainingInstitution(channelVendorApplyId:String):String
	"""申请成为渠道商
		@return
	"""
	apply(captchaToken:String,params:ApplyChannelVendorApplyParams):GraphqlOperateResult
	"""创建渠道商（通过请求直接创建）
		@return
	"""
	createChannelVendor(token:String,params:ApplyChannelVendorApplyParams):GraphqlOperateResult @NotAuthenticationRequired
	"""创建提供企业账户渠道商（通过请求直接创建）
		@param token
		@param accountId
		@param params
		@return
	"""
	createChannelVendorByAccount(token:String,accountId:String,params:ApplyChannelVendorApplyParams):GraphqlOperateResult @NotAuthenticationRequired
	"""培训机构拒绝渠道商开通"""
	refuseApplyForTrainingInstitution(channelVendorApplyId:String):Boolean!
}
"""申请成为渠道商
	<AUTHOR>
	@version 1.0
	@date 2021/7/17 18:14
"""
input ApplyChannelVendorApplyParams @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.servicer.channelVendor.ApplyChannelVendorApplyParams") {
	"""申请的机构id"""
	trainingInstitutionId:String
	"""申请人"""
	applierName:String!
	"""手机号"""
	phoneNumber:String!
	"""渠道商名称"""
	channelVendorName:String!
	"""所在地区路径"""
	areaPath:String
	"""推广优势说明"""
	explain:String
}
"""渠道商申请记录查询条件
	<AUTHOR>
	@version 1.0
	@date 2021/7/17 20:14
"""
input ChannelVendorApplyRecordQueryParams @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.servicer.channelVendor.ChannelVendorApplyRecordQueryParams") {
	"""申请人"""
	applierName:String
	"""手机号"""
	phoneNumber:String
	"""渠道商名称"""
	channelVendorName:String
	"""所在地区路径"""
	areaPathList:[String]
	"""申请单处理状态
		1：申请中，2：已同意，3：已拒绝
	"""
	auditStatus:Int
	"""申请时间"""
	applyTime:TimeRegionRequest
	"""审批时间"""
	auditTime:TimeRegionRequest
	"""合作状态"""
	status:ServicerContractStatusEnums
}
"""与当前培训机构角色签约的渠道商查询条件
	<AUTHOR>
	@since 2021/11/1
"""
input ChannelVendorForTInstitutionQueryParams @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.servicer.channelVendor.ChannelVendorForTInstitutionQueryParams") {
	"""名称"""
	name:String
	"""渠道地区"""
	regionPath:[String]
	"""合作签约开始时间"""
	beginTime:DateTime
	"""合作签约结束时间"""
	endTime:DateTime
	"""合作状态"""
	contractStatus:ServicerContractStatusEnums
	"""手机号"""
	phone:String
}
"""渠道商查询信息"""
input ChannelVendorQueryParams @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.servicer.channelVendor.ChannelVendorQueryParams") {
	"""培训机构Id"""
	trainingInstitutionIdList:[String]
	"""渠道商名称"""
	name:String
	"""合作状态"""
	status:ServicerContractStatusEnums
}
"""时间范围
	<AUTHOR>
	@date 2020/5/3116:42
"""
input TimeRegionRequest @type(value:"com.fjhb.btpx.support.dto.TimeRegionRequest") {
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""graphql没有泛型的操作结果类
	<AUTHOR> create 2020/3/9 15:18
"""
type GraphqlOperateResult @type(value:"com.fjhb.btpx.integrative.gateway.graphql.dto.GraphqlOperateResult") {
	"""返回的code"""
	code:String
	"""返回的message"""
	message:String
	"""json字段
		存放返回的参数用
	"""
	expandData:Map
}
enum ServicerContractStatusEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerContractStatusEnums") {
	"""全部"""
	ALL
	"""合作"""
	NORMAL
	"""中止"""
	SUSPEND
}
enum ServicerTypeEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerTypeEnums") {
	"""全部"""
	ALL
	"""培训机构"""
	TRAINING_INSTITUTION
	"""课件供应商"""
	COURSEWARE_SUPPLIER
	"""渠道商"""
	CHANNEL_VENDOR
	"""参训单位"""
	PARTICIPATING_UNIT
}
"""服务商合约状态"""
enum ServicerContractStatusEnum @type(value:"com.fjhb.btpx.platform.enums.ServicerContractStatusEnum") {
	"""合作"""
	NORMAL
	"""中止"""
	SUSPEND
}
"""渠道商申请记录
	<AUTHOR>
	@version 1.0
	@date 2021/7/17 20:13
"""
type ChannelVendorApplyRecordDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.servicer.channelVendor.ChannelVendorApplyRecordDto") {
	"""申请记录id"""
	applyId:String
	"""申请的机构id"""
	trainingInstitutionId:String
	"""申请人"""
	applierName:String
	"""手机号"""
	phoneNumber:String
	"""渠道商名称"""
	channelVendorName:String
	"""所在地区路径"""
	areaPath:String
	"""推广优势说明"""
	explain:String
	"""审批人id"""
	auditor:String
	"""申请单处理状态
		1：申请中，2：已同意，3：已拒绝
	"""
	auditStatus:Int
	"""申请时间"""
	applyTime:DateTime
	"""审批时间"""
	auditTime:DateTime
	"""渠道商id"""
	channelVendorId:String
	"""渠道商账户id"""
	channelVendorAccountId:String
	"""渠道商企业账号"""
	channelVendorLoginInput:String
	"""合作状态"""
	status:ServicerContractStatusEnums
}
"""渠道商列表信息"""
type ChannelVendorForTIDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.channelVendor.ChannelVendorForTIDto") {
	"""Id"""
	id:String
	"""渠道商名称"""
	name:String
	"""地区名称列表，省市区县..."""
	regionNames:[String]
	"""所在地区路径"""
	regionPath:String
	"""负责人"""
	contactPerson:String
	"""手机号"""
	phone:String
	"""创建时间"""
	createTime:DateTime
	"""合作状态"""
	status:ServicerContractStatusEnums
	"""签约时间"""
	contractTime:DateTime
}
"""渠道商列表信息"""
type ChannelVendorListDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.channelVendor.ChannelVendorListDto") {
	"""Id"""
	id:String
	"""渠道商名称"""
	name:String
	"""地区名称列表，省市区县..."""
	regionNames:[String]
	"""所在地区路径"""
	regionPath:String
	"""负责人"""
	contactPerson:String
	"""手机号"""
	phone:String
	"""创建时间"""
	createTime:DateTime
	"""合作状态"""
	status:ServicerContractStatusEnums
}
"""账号信息"""
type AccountDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.common.AccountDto") {
	"""帐号"""
	loginAccount:String
	"""姓名"""
	name:String
	"""手机号"""
	phone:String
	"""创建时间"""
	createTime:DateTime
	"""创建人"""
	creator:String
}
"""有合约的服务商信息"""
type ServicerContractDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.common.ServicerContractDto") {
	"""服务商 Id"""
	id:String
	"""服务商类型"""
	servicerType:ServicerTypeEnums
	"""名称"""
	name:String
	"""合作状态"""
	status:ServicerContractStatusEnums
	"""服务商合作日志"""
	contractLogList:[ServicerContractLogDto]
}
"""服务商签约日志返回值"""
type ServicerContractLogDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.common.ServicerContractLogDto") {
	"""合作状态"""
	contractLogStatus:ServicerContractStatusEnum
	"""操作时间"""
	operationTime:DateTime
	"""操作人"""
	operationUserName:String
}
"""服务详情信息（渠道商、课件供应商）"""
type ServicerDetailDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.common.ServicerDetailDto") {
	"""Id"""
	id:String
	"""课件供应商名称"""
	name:String
	"""地区名称列表，省市区县..."""
	regionNames:[String]
	"""所在地区路径"""
	regionPath:String
	"""负责人"""
	contactPerson:String
	"""手机号"""
	phone:String
	"""课件供应商优势简述"""
	abouts:String
	"""合作状态"""
	status:ServicerContractStatusEnums
	"""有合约的服务商信息"""
	servicerContracts:[ServicerContractDto]
	"""账号信息"""
	accounts:[AccountDto]
}

scalar List
type ChannelVendorListDtoPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ChannelVendorListDto]}
type ChannelVendorApplyRecordDtoPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ChannelVendorApplyRecordDto]}
type ChannelVendorForTIDtoPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ChannelVendorForTIDto]}
