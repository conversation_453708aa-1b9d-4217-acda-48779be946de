import CoursewareCategory from '@api/service/common/models/course/CoursewareCategory'
import { ExtensionResourceResponse } from '@api/gateway/btpx@Course-default'

/**
 * 课件信息
 */
class Courseware {
  /**
   * 课件编号
   */
  id = ''
  /**
   * 课件名称
   */
  name = ''
  /**
   * 课件时长
   */
  timeLength = 0
  /**
   * 所属课件目录编号
   */
  courseChapterId = ''
  /**
   * 课件分类
   */
  category: CoursewareCategory
  /**
   * 课件类型，1表示文档，2表示视频，3表示多媒体
   */
  type: number
  /**
   * 挂在课件是否支持试听 0不可以试听，1可以试听，
   */
  customeStatus: number
  /**
   * 外部链接资源
   */
  extensionResourceResponse: ExtensionResourceResponse
}

export default Courseware
