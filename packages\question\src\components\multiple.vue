<!--创建试题-多选题-->
<template>
  <div>
    <el-form-item label="试题题目" required>
      <el-input type="textarea" :rows="6" v-model="createQuestion.topic" placeholder="请输入试题题目" v-if="show" />
      <!-- <hb-tiny-mce-editor v-model="createQuestion.topic" v-if="show"></hb-tiny-mce-editor> -->
    </el-form-item>
    <el-form-item label="试题选项" required>
      <div v-for="(item, index) in choiceItems" :key="index" class="m-option-btn f-mb20">
        <div class="f-flex f-align-center f-mb5">
          <p class="f-flex-sub f-cb">选项{{ resolverIndexToCharCode(index) }}</p>
          <el-button
            :type="index === 0 ? 'info' : 'primary'"
            plain
            size="mini"
            icon="el-icon-top"
            @click="questionUp(index)"
          ></el-button>
          <el-button
            :type="index == choiceItems.length - 1 ? 'info' : 'primary'"
            plain
            size="mini"
            icon="el-icon-bottom"
            @click="questionDown(index)"
          ></el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="questionDelete(index)"></el-button>
        </div>
        <div class="text-options" style="display:flex">
          <el-input type="textarea" :rows="6" v-model="item.content" v-if="show" />
          <!-- <hb-tiny-mce-editor v-model="item.content" v-if="show" style="width:722px"></hb-tiny-mce-editor> -->
        </div>
      </div>
      <el-button type="primary" class="mt20" @click="addChoiceItem">+ 添加选项</el-button>
    </el-form-item>
    <el-form-item label="正确答案" required>
      <el-checkbox-group v-model="createQuestion.correctAnswerIds">
        <el-checkbox v-for="(item, index) in choiceItems" :key="index" :label="item.id">
          {{ resolverIndexToCharCode(index) }}
        </el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item label="题目题析">
      <div class="rich-text">
        <el-input
          type="textarea"
          v-if="show"
          :rows="6"
          v-model="createQuestion.dissects"
          placeholder="请输入试题题目"
        />
      </div>
      <!-- <hb-tiny-mce-editor v-model="createQuestion.dissects" v-if="show"></hb-tiny-mce-editor> -->
    </el-form-item>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, Mixins, Watch, PropSync, Vue } from 'vue-property-decorator'
  import { ExamUtil } from '@/store/module/exam/common/ExamUtil'
  import HbTinyMceEditor from '@hbfe/jxjy-admin-components/src/tinymce-editor/index.vue'
  import { ChooseAnswerOptionVo } from '@api/service/management/resource/question/mutation/vo/ChooseAnswerOptionVo'
  import CreateMultipleQuestionDto from '@api/service/management/resource/question/mutation/vo/create/CreateMultipleQuestionVo'

  @Component({
    components: {
      HbTinyMceEditor
    }
  })
  export default class extends Vue {
    // todo 操作数组方法后期改混入
    @PropSync('createQuestionInfo', {
      type: Object
    })
    createQuestion: CreateMultipleQuestionDto

    @Watch('createQuestionInfo', { deep: true })
    changeCreateQuestionInfo(val: any) {
      console.log(val, '222')
    }
    @Prop({
      required: true,
      type: String
    })
    questionType: number
    @Prop({
      default: false
    })
    isUpdate: boolean

    choiceItems = new Array<ChooseAnswerOptionVo>() // 试题选项

    correctAnswers = new Array<string>()

    show = false
    /**
     * ui常量
     */
    uiConfig = {
      // 当前选项的索引
      currentChoiceIndex: 0
    }
    questionParams: CreateMultipleQuestionDto

    /**
     * 初始化加载
     */
    async init() {
      this.choiceItems = new Array<ChooseAnswerOptionVo>()
      this.questionParams = this.createQuestion
      this.choiceItems = this.questionParams.answerOptions
      if (!this.isUpdate && this.choiceItems.length < 2) {
        this.choiceItems.push(this.productChoiceItem(1))
        this.choiceItems.push(this.productChoiceItem(2))
      }

      this.uiConfig.currentChoiceIndex = 2
      // if (this.questionCommon.questionContent.choiceItems.length < 1) {
      //   this.choiceItems.push(this.productChoiceItem(1))
      //   this.choiceItems.push(this.productChoiceItem(2))
      //   this.uiConfig.currentChoiceIndex = 2
      // } else {
      //   const length = this.questionCommon.questionContent.choiceItems.length
      //   const lastId = this.questionCommon.questionContent.choiceItems[length - 1]?.id
      //   if (!lastId) {
      //     throw new Error('>> 试题的选项id无效')
      //   }
      //   this.uiConfig.currentChoiceIndex = parseInt(lastId) + 1
      //   this.choiceItems = cloneDeep(this.questionCommon.questionContent.choiceItems)
      //   this.correctAnswers = this.questionCommon.questionContent.correctAnswers || []
      // }
    }

    /**
     * 解析index为字母
     * @param index
     */
    resolverIndexToCharCode(index: number) {
      return ExamUtil.matchCharCode(index)
    }

    /**
     * 更新后的值
     */
    changeCheck(val: any) {
      console.log(this.correctAnswers, val)
    }

    /**
     * 新增一个选项
     */
    async addChoiceItem() {
      if (this.choiceItems.length >= 20) {
        this.$message.error('选项添加已达到上限，无法继续添加。')
        return
      }
      this.uiConfig.currentChoiceIndex++
      this.choiceItems.push(this.productChoiceItem(this.uiConfig.currentChoiceIndex))
    }

    /**
     * 父组件调用
     * 提交最终数据
     */
    commit() {
      // this.questionCommon.questionContent.choiceItems = this.choiceItems
      // this.questionCommon.questionContent.correctAnswers = this.correctAnswers
      // console.log(this.questionCommon.questionContent)
    }

    /**
     * 基本表单校验
     */
    validForm() {
      // if (
      //   !this.questionCommon.questionContent?.choiceItems ||
      //   !this.questionCommon.questionContent.choiceItems.length
      // ) {
      //   this.$message.warning('请配置多选题选项。')
      //   return false
      // }
      if (!this.questionParams.topic) {
        this.$message.warning('请输入试题题目。')
        return false
      }
      if (this.choiceItems.length < 2) {
        this.$message.warning('多选题选项不能少于两个。')
        return false
      }
      if (this.choiceItems.find((p: any) => !p.content)) {
        this.$message.warning('请输入试题选项。')
        return false
      }
      // console.log(this.questionCommon.questionContent.correctAnswer)
      if (!this.questionParams.correctAnswerIds) {
        this.$message.warning('正确答案不能为空。')
        return false
      }
      return true
    }

    async created() {
      this.deploy()
    }
    activated() {
      this.deploy()
    }
    deploy() {
      this.show = false

      this.init()

      setTimeout(() => {
        this.show = true
      }, 100)
    }
    productChoiceItem(index: number) {
      const choice = new ChooseAnswerOptionVo()
      choice.id = index + ''
      return choice
    }
    // 试题上移
    questionUp(idx: number) {
      if (this.isUpdate) {
        this.createQuestion.doQuestionUp(this.createQuestion.answerOptions, idx)
      } else {
        this.createQuestion.doQuestionUp(this.choiceItems, idx)
      }
    }
    // 试题下移
    questionDown(idx: number) {
      if (this.isUpdate) {
        this.createQuestion.doQuestionDown(this.createQuestion.answerOptions, idx)
      } else {
        this.createQuestion.doQuestionDown(this.choiceItems, idx)
      }
    }
    // 试题删除
    questionDelete(idx: number) {
      this.$confirm('确定删除该选项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteItem(idx)
        })
        .catch(() => {
          console.log('已取消删除')
        })
    }
    deleteItem(idx: number) {
      if (this.isUpdate) {
        this.createQuestion.doQuestionDelete(this.createQuestion.answerOptions, idx)
      } else {
        this.createQuestion.doQuestionDelete(this.choiceItems, idx)
      }
    }
  }
</script>
