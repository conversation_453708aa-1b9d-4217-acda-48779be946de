import {
  RadioQuestionResponse,
  MultipleQuestionResponse,
  AskQuestionResponse,
  ScaleQuestionResponse
} from '@api/ms-gateway/ms-exam-answer-v1'
import {
  AskQuestion,
  MultipleQuestion,
  RadioQuestion,
  ScaleQuestion
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryForeStage'
import { QuestionTypeEnum } from '@api/service/common/enums/question-naire/QuestionType'
import Question from '@api/service/common/question-naire/Question'
import QuestionSingleOption from '@api/service/common/question-naire/QuestionSingleOption'
import { ChooseAnswerOption } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import { TeacherQuestionTypeEnum } from '@api/service/common/question-naire/enums/TeacherQuestionType'
import { TeacherEvaluationQuestionTypeEnum } from '@api/service/common/question-naire/enums/TeacherEvaluationQuestionType'
import { QuestionSourceTypeEnum } from '@api/service/common/question-naire/enums/QuestionSourceType'
/**
 * 问卷作答
 * 题目和选项在Question类里
 */
export default class QuestionAnswer extends Question {
  /**
   * 试题答案 状态层存值
   * 选择题存选项id
   * 问答题填string
   * 量表题存number
   */
  answers = new Array<string>()
  /**
   * 填空内容 状态层存值
   * key是选项id，value是填空的内容
   */
  fillContentMap: Map<string, string> = new Map<string, string>()

  static from(dto: RadioQuestionResponse | MultipleQuestionResponse | AskQuestionResponse | ScaleQuestionResponse) {
    const vo = new QuestionAnswer()
    vo.isMustAnswered = dto.answerRequired
    switch (dto.questionType) {
      /**
       * 单选题
       */
      case 1:
        vo.id = dto.id
        vo.type = QuestionTypeEnum.single
        vo.optionQuestion.described = dto.topic
        vo.isAnswered = dto.answered
        vo.optionQuestion.options = ((dto as RadioQuestionResponse).answerOptions || []).map((item: any) => {
          const temp = new QuestionSingleOption()
          temp.id = item.id
          temp.content = item.content
          temp.completion = item.enableFillContent
          temp.isRequire = item.mustFillContent
          return temp
        })
        // if (vo.isAnswered) {
        //   vo.answers = [dto.radioAnswer]
        //   // 需要验证
        //   vo.fillContentMap.set(dto.radioAnswer, dto.fillContent)
        // }
        break
      /**
       * 多选题
       */
      case 2:
        vo.id = dto.id
        vo.type = QuestionTypeEnum.multiple
        vo.isAnswered = dto.answered
        vo.multipleOptionQuestion.described = dto.topic
        vo.multipleOptionQuestion.options = ((dto as MultipleQuestionResponse).answerOptions || []).map((item: any) => {
          const temp = new QuestionSingleOption()
          temp.id = item.id
          temp.content = item.content
          temp.completion = item.enableFillContent
          temp.isRequire = item.mustFillContent
          return temp
        })
        // if (vo.isAnswered) {
        //   vo.answers = dto.multipleAnswer
        //   // TODO 需要验证 后端模型这样给过来 应该不行
        //   vo.fillContentMap = dto.fillContentMap
        // }
        break
      /**
       * 问答题
       */
      case 5:
        vo.id = dto.id
        vo.type = QuestionTypeEnum.answer
        vo.isAnswered = dto.answered
        vo.answerQuestion.described = dto.topic
        // if (vo.isAnswered) {
        //   vo.answers = [dto.askAnswer]
        // }
        break
      /**
       * 量表题
       */
      case 7:
        vo.id = dto.id
        vo.type = QuestionTypeEnum.gauge
        vo.isAnswered = dto.answered
        vo.gaugeQuestion.described = dto.topic
        vo.gaugeQuestion.levelNum = (dto as ScaleQuestionResponse).series
        vo.gaugeQuestion.minDeepTip = (dto as ScaleQuestionResponse).startDegree
        vo.gaugeQuestion.maxDeepTip = (dto as ScaleQuestionResponse).endDegree
        vo.gaugeQuestion.startLevel = (dto as ScaleQuestionResponse).initialValue
        vo.gaugeQuestion.type = (dto as ScaleQuestionResponse).scaleType
        // if (vo.isAnswered) {
        //   vo.answers = [dto.scaleAnswer.toString()]
        // }
        break
      default:
        break
    }
    return vo
  }

  /**
   * 问答详情参转-答案
   */
  static fromAnswer(dto: RadioQuestion | MultipleQuestion | AskQuestion | ScaleQuestion) {
    const vo = new QuestionAnswer()
    const fillContentMap = new Map<string, string>()
    vo.isMustAnswered = dto.answerRequired
    vo.onlineOrOffline = dto.tag as QuestionSourceTypeEnum
    switch (dto.questionType) {
      /**
       * 单选题
       */
      case 1:
        vo.id = dto.questionId
        vo.type = QuestionTypeEnum.single
        vo.isAnswered = !!(dto as RadioQuestion).radioAnswer
        vo.answers = [(dto as RadioQuestion).radioAnswer]
        vo.fillContentMap = new Map().set((dto as RadioQuestion).radioAnswer, (dto as RadioQuestion).fillContent)

        break
      /**
       * 多选题
       */
      case 2:
        vo.id = dto.questionId
        vo.type = QuestionTypeEnum.multiple
        vo.isAnswered = !!(dto as MultipleQuestion).multipleAnswer
        vo.answers = (dto as MultipleQuestion).multipleAnswer
        ;((dto as MultipleQuestion).fillContents || []).map((item: any) => {
          fillContentMap.set(item.id, item.fillContent)
        })
        vo.fillContentMap = fillContentMap
        break
      /**
       * 问答题
       */
      case 5:
        vo.id = dto.questionId
        vo.type = QuestionTypeEnum.answer
        vo.isAnswered = !!(dto as AskQuestion).askAnswer
        vo.answers = [(dto as AskQuestion).askAnswer]
        break
      /**
       * 量表题
       */
      case 7:
        vo.id = dto.questionId
        vo.type = QuestionTypeEnum.gauge
        vo.isAnswered = !!(dto as ScaleQuestion).answer
        vo.answers = [(dto as ScaleQuestion).answer.toString()]
        break
      default:
        break
    }
    return vo
  }
  /**
   * 问答详情参转-选项
   */
  static fromQuestionAnwer(temp: QuestionAnswer, ite: any) {
    switch (temp.type) {
      case QuestionTypeEnum.single:
        if (ite.code && ite.code.includes(TeacherEvaluationQuestionTypeEnum.TEACHER_EVALUATION_QUESTION)) {
          temp.isTeacherEvaluate = true
        }
        temp.optionQuestion.described = ite.topic
        temp.optionQuestion.options = (ite.radioAnswerOptions || []).map((d: ChooseAnswerOption) => {
          const tem = new QuestionSingleOption()
          tem.id = d.id
          tem.content = d.content
          tem.completion = d.enableFillContent
          tem.isRequire = d.mustFillContent
          return tem
        })
        break
      case QuestionTypeEnum.multiple:
        if (ite.code && ite.code.includes(TeacherEvaluationQuestionTypeEnum.TEACHER_EVALUATION_QUESTION)) {
          temp.isTeacherEvaluate = true
        }
        temp.multipleOptionQuestion.described = ite.topic
        temp.multipleOptionQuestion.options = (ite.multipleAnswerOptions || []).map((d: ChooseAnswerOption) => {
          const tem = new QuestionSingleOption()
          tem.id = d.id
          tem.content = d.content
          tem.completion = d.enableFillContent
          tem.isRequire = d.mustFillContent
          return tem
        })
        break
      case QuestionTypeEnum.answer:
        temp.answerQuestion.described = ite.topic
        break
      case QuestionTypeEnum.gauge:
        temp.gaugeQuestion.described = ite.topic
        temp.gaugeQuestion.levelNum = ite.series
        temp.gaugeQuestion.minDeepTip = ite.startDegree
        temp.gaugeQuestion.maxDeepTip = ite.endDegree
        temp.gaugeQuestion.startLevel = ite.initialValue
        temp.gaugeQuestion.type = ite.scaleType
        break
      default:
        break
    }
    temp.onlineOrOffline = ite.onlineOrOffline
    return temp
  }
}
