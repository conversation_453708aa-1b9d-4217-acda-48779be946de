import MsTradeConfigurationGateway from '@api/ms-gateway/ms-trade-configuration-v1'
import { InvoiceProviderEnum } from '@api/service/common/enums/online-school-config/InvoiceProviderTypes'
import ElectronicInvoiceDetailVo from '@api/service/management/online-school-config/functionality-setting/query/vo/ElectronicInvoiceDetailVo'
import ElectronicInvoiceResponseVo from '@api/service/management/online-school-config/functionality-setting/query/vo/ElectronicInvoiceResponseVo'
import { Response } from '@hbfe/common'
class QueryElectronicInvoiceConfig {
  /**
   * @description: 获取发票类型以及税务编码集合
   */
  async queryElectronicInvoiceConfig(): Promise<Response<ElectronicInvoiceResponseVo>> {
    const res = await MsTradeConfigurationGateway.prepareElectronicInvoiceConfig()
    const response = new Response<ElectronicInvoiceResponseVo>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.data = new ElectronicInvoiceResponseVo()
    response.data.from(res.data)
    response.status = res.status
    return response
  }

  /**
   * @description: 根据纳税人编号查询增值税发票详情
   * @param {string} taxpayerId
   */
  async queryElectronicInvoiceDetail(taxpayerId: string, type?: string): Promise<Response<ElectronicInvoiceDetailVo>> {
    const response = new Response<ElectronicInvoiceDetailVo>()
    const res = await MsTradeConfigurationGateway.findElectronicInvoiceTaxpayer(taxpayerId)
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.data = new ElectronicInvoiceDetailVo()
    if (type === InvoiceProviderEnum.NUONUOV2) {
      response.data.fromV2(res.data)
    } else if (type === InvoiceProviderEnum.BWJF) {
      response.data.fromBWJF(res.data)
    } else if (type === InvoiceProviderEnum.NST || type === InvoiceProviderEnum.NSTQDP) {
      response.data.fromNST(res.data)
    } else {
      response.data.from(res.data)
    }
    response.status = res.status
    return response
  }

  /**
   * @description: 获取纳税人编号
   */
  async queryElectronicInvoiceTaxpayerId(): Promise<Response<string>> {
    const res = await MsTradeConfigurationGateway.findElectronicInvoiceTaxpayerList()
    const response = new Response<string>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.status = res.status
    if (res.data?.length) {
      response.data = res.data[0]
    } else {
      response.data = ''
    }
    return response
  }

  /**
   * @description: 判断网校是否完成开票
   */
  async queryElectronicInvoiceExisted(): Promise<Response<boolean>> {
    const res = await this.queryElectronicInvoiceTaxpayerId()
    const response = new Response<boolean>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      console.error('获取纳税人编号请求失败！')
      return response
    }
    response.status = res.status
    if (res.data) {
      response.data = true
    } else {
      response.data = false
    }
    return response
  }
}

export default QueryElectronicInvoiceConfig
