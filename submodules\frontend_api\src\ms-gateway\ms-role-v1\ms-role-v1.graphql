"""独立部署的微服务,K8S服务名:ms-role-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取所有组安全对象
		@return 所有组安全对象
	"""
	getAllPermission:[SecurityObjectGroupDto]
	"""获取系统的所有角色信息
		@return 系统的所有角色信息
	"""
	getAllRoles:[RoleDTO]
	"""获取当前角色安全对象
		@return List<SecurityObjectGroupDto>
	"""
	getCurrentUserSecurityGroup:[SecurityObjectGroupDto]
	"""获取系统中所有可用的角色
		@return 系统中所有可用的角色
	"""
	getEnabledRoles:[RoleDTO]
	"""根据roleId获取安全对象组
		@return 安全对象组
	"""
	getPermissionByRoleId(roleId:String):[SecurityObjectGroupDto]
	"""获取安全对象--编辑角色
		@param roleId 角色id
		@return 编辑角色
	"""
	getPermissionForEditRole(roleId:String):RoleEditDto
	"""根据id获取角色
		@return 角色信息
	"""
	getRoleById(roleId:String):RoleDTO
	"""判断角色是否存在
		@param check 0：忽略该字段|1：false
		@param field 角色名称
		@return 存在则返回false
	"""
	isRoleExist(check:Int!,field:String):Boolean!
	"""获取角色分页
		@param page 分页信息
		@return 角色分页
	"""
	pageRolesByQuery(page:Page):RoleDTOPage @page(for:"RoleDTO")
}
type Mutation {
	"""添加用户角色信息
		@param accountId 账户id
		@param roleIds   角色id集合
	"""
	addUserOwnRoles(accountId:String!,roleIds:[String]!):Void
	"""删除角色
		@param roleId 角色id
	"""
	deleteRole(roleId:String):Void
	getPermission(nodeSelectedIdArray:[String],itemSelectedIdArray:[String],rootSelectedIdArray:[String]):[String]
	"""移除用户角色信息
		@param accountId      账户id
		@param accountRoleIds 账户角色关系id集合
	"""
	removeUserOwnRoles(accountId:String!,accountRoleIds:[String]!):Void
	"""保存角色
		@param roleDto 角色权限
		@return boolean 保存结果
	"""
	saveRole(roleDto:RolePermissionDto):Boolean!
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""创建角色
	<AUTHOR>
"""
input RoleCreateDto @type(value:"com.fjhb.ms.role.v1.kernel.gateway.graphql.dto.RoleCreateDto") {
	"""角色id"""
	id:String
	"""角色名称"""
	name:String
	"""角色说明"""
	description:String
}
"""角色权限信息
	<AUTHOR>
"""
input RolePermissionDto @type(value:"com.fjhb.ms.role.v1.kernel.gateway.graphql.dto.RolePermissionDto") {
	"""角色创建信息"""
	roleMessage:RoleCreateDto
	"""已选中节点的权限id集合["b9efcaa673a9e2251fa2b4c34b634893", "c1b6824fa27a44f21467c544346b8fc1",…]"""
	nodeSelectedIdArray:[String]
	itemSelectedIdArray:[String]
	rootSelectedIdArray:[String]
}
"""角色信息
	<AUTHOR>
"""
type RoleDTO @type(value:"com.fjhb.ms.role.v1.kernel.gateway.graphql.dto.RoleDTO") {
	"""角色ID"""
	id:String
	"""角色级别值"""
	levelValue:Int!
	"""角色名称"""
	name:String
	"""角色描述"""
	description:String
	"""创建方式 | 1:自建; 2:导入"""
	createType:Int!
	"""数据类型 | 1:普通; 2:内置"""
	dataType:Int!
	"""创建人ID"""
	creatorId:String
	"""创建时间"""
	createDate:DateTime
	"""是否可用"""
	available:Boolean!
}
"""角色修改信息
	<AUTHOR>
"""
type RoleEditDto @type(value:"com.fjhb.ms.role.v1.kernel.gateway.graphql.dto.RoleEditDto") {
	"""安全对象组集合"""
	securityObjectGroupList:[SecurityObjectGroupDto]
	"""第三级安全对象id集合
		旧平台的东西，不建议写死层级数，请使用RoleEditDto#securityObjectGroupList
	"""
	nodeSelectedIdArray:[String]
	"""第二级安全对象id集合
		旧平台的东西，不建议写死层级数，请使用RoleEditDto#securityObjectGroupList
	"""
	itemSelectedIdArray:[String]
	"""第一级安全对象id集合
		旧平台的东西，不建议写死层级数，请使用RoleEditDto#securityObjectGroupList
	"""
	rootSelectedIdArray:[String]
}
"""安全对象组信息
	<AUTHOR>
"""
type SecurityObjectGroupDto @type(value:"com.fjhb.ms.role.v1.kernel.gateway.graphql.dto.SecurityObjectGroupDto") {
	"""安全对象组id"""
	id:String
	"""安全对象组名"""
	name:String
	"""URL内容"""
	urlContent:String
	"""url路径"""
	url:String
	"""是否被选中"""
	isSelected:Boolean!
	"""子安全对象组(嵌套)"""
	children:[SecurityObjectGroupDto]
	"""父安全对象组id"""
	parentId:String
	"""排序"""
	sort:Int!
}

scalar List
type RoleDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [RoleDTO]}
