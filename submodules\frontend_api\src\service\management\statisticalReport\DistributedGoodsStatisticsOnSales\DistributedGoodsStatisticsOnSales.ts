import DistributedGoodsStatisticsOnSalesParams from '@api/service/management/statisticalReport/DistributedGoodsStatisticsOnSales/models/DistributedGoodsStatisticsOnSalesParams'
import DistributedGoodsStatisticsOnSalesInfo from '@api/service/management/statisticalReport/DistributedGoodsStatisticsOnSales/models/DistributedGoodsStatisticsOnSalesInfo'
import { Response, ResponseStatus } from '@hbfe/common'
import DataResolve from '@api/service/common/utils/DataResolve'
import TotalStatic from '@api/service/management/statisticalReport/models/TotalStatic'

// 原型删除暂不维护
export default class DistributedGoodsStatisticsOnSales {
  /**
   * 查询参数
   */
  params: DistributedGoodsStatisticsOnSalesParams = new DistributedGoodsStatisticsOnSalesParams()
  /**
   * 列表
   */
  list: DistributedGoodsStatisticsOnSalesInfo[] = []
  /**
   * 静态统计
   */
  totalStatic: TotalStatic = new TotalStatic()

  /**
   * 查询列表
   */
  async queryList() {
    const response = new Response<string>()
    response.status = new ResponseStatus(200)
    response.data = ''
    return DataResolve.mockReqReturn(response)
  }

  /**
   * 查询静态统计
   */
  async queryTotalStatic() {
    const response = new Response<string>()
    response.status = new ResponseStatus(200)
    response.data = ''
    return DataResolve.mockReqReturn(response)
  }

  /**
   * 导出列表
   */
  async exportData() {
    const response = new Response<string>()
    response.status = new ResponseStatus(200)
    response.data = ''
    return DataResolve.mockReqReturn(response)
  }
}
