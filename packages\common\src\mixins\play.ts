import { Component, Vue } from 'vue-property-decorator'
import TokenModule from '@api/service/customer/token/TokenModule'

@Component
export default class extends Vue {
  previewDrawer: Vue

  /**
   * 试听
   * @param courseId 课程编号
   * @returns {Promise<never>}
   */
  async goAudition(courseId: string) {
    const url = await this.getCoursePlayUrl(courseId)
    if (url) {
      window.open(url, '_blank')
    }
    return Promise.reject(status)
  }

  /**
   * 试听
   * @param courseId 课程编号
   * @returns {Promise<never>}
   */
  async getCoursePlayUrl(courseId: string) {
    const { data, status } = await TokenModule.applySingleCoursePreviewToken({ courseId: courseId })
    if (status.isSuccess()) {
      return Promise.resolve(`/play/previewCourse/${data}`)
    }
    return Promise.reject(status)
  }

  /**
   * 预览课程，使用抽屉的方式将课程展示出来
   * @param courseId
   */
  async previewCourse(courseId: string) {
    const requestUrl = await this.getCoursePlayUrl(courseId)
    if (!this.previewDrawer) {
      const body = document.body
      const component = await import('@hbfe/jxjy-admin-components/src//preview-course/index.vue')
      const instance = new Vue({
        data() {
          return {
            requestUrl
          }
        },
        methods: {
          close: () => {
            this.previewDrawer.$destroy()
            this.previewDrawer.$el.remove()
            this.previewDrawer = undefined
          },
          opened() {
            this.requestUrl = requestUrl
          }
        },
        template: '<Core :preview-course-url="requestUrl" @close="close" @opened="opened"/>',
        components: {
          Core: component.default
        }
      })
      this.previewDrawer = instance.$mount()
      body.appendChild(this.previewDrawer.$el)
    } else {
      this.previewDrawer.$data.previewCourse = true
    }
  }
}
