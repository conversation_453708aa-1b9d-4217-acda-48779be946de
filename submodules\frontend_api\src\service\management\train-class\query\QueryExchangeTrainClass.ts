import MsTradeQuery, {
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import ExchangeOrderRecordDetailVo from '@api/service/management/train-class/query/vo/ExchangeOrderRecordDetailVo'
import QueryExchangeOrderRecordListVo from '@api/service/management/train-class/query/vo/QueryExchangeOrderRecordListVo'
import QueryReplaceableTrainClassListVo from '@api/service/management/train-class/query/vo/QueryReplaceableTrainClassListVo'
import QueryWaitExchangeTrainClassListVo from '@api/service/management/train-class/query/vo/QueryWaitExchangeTrainClassListVo'
import ReplaceableTrainClassDetailVo from '@api/service/management/train-class/query/vo/ReplaceableTrainClassDetailVo'
import WaitExchangeTrainClassDataVo from '@api/service/management/train-class/query/vo/WaitExchangeTrainClassDataVo'
import WaitExchangeTrainClassDetailVo from '@api/service/management/train-class/query/vo/WaitExchangeTrainClassDetailVo'
import UserModule from '@api/service/management/user/UserModule'
import QueryManager from '@api/service/management/user/query/manager/QueryManager'
import AdminUserInfoVo from '@api/service/management/user/query/manager/vo/AdminUserInfoVo'
import { Page } from '@hbfe/common'
import ChangeIssueItem from '@api/service/management/train-class/offlinePart/model/ChangeIssueItem'
import ServiceTime from '@api/service/common/service-time/ServiceTime'
import QueryCanChangeIssueParam from '@api/service/management/train-class/offlinePart/model/QueryCanChangeIssueParam'
import IssueChangeRecordItem from '@api/service/management/train-class/offlinePart/model/IssueChangeRecordItem'
import QueryIssueChangeRecordParam from '@api/service/management/train-class/offlinePart/model/QueryIssueChangeRecordParam'

/**
 * @description 换班培训班商品查询类
 */
class QueryExchangeTrainClass {
  // region 超管-查询待更换班级列表

  /**
   * 【超管】查询待更换班级列表
   * @param {Page} page - 分页参数
   * @param {QueryWaitExchangeTrainClassListVo} queryParams - 查询参数
   */
  async queryWaitExchangeTrainClassList(
    page: Page,
    queryParams: QueryWaitExchangeTrainClassListVo
  ): Promise<WaitExchangeTrainClassDataVo> {
    const userId = queryParams.buyerId
    const request = queryParams.to()
    const response = await MsTradeQuery.listBuyerValidCommodityInSerivicer(request)
    let result = new Array<WaitExchangeTrainClassDetailVo>()
    if (response.status.isSuccess() && this.isWeightyArr(response.data)) {
      result = WaitExchangeTrainClassDetailVo.batchFrom(response.data)
      const schemeIds = result.map((scheme) => {
        return scheme.schemeId
      })
      const schemeStatusMap = await WaitExchangeTrainClassDetailVo.batchGetUserStatusInScheme(userId, schemeIds)
      result = result.map((scheme) => {
        if (schemeStatusMap.has(scheme.schemeId)) {
          scheme.assessmentStatus = schemeStatusMap.get(scheme.schemeId).assessmentStatus
          scheme.status = schemeStatusMap.get(scheme.schemeId).status
        }
        return scheme
      })
    } else {
      result = new Array<WaitExchangeTrainClassDetailVo>()
    }
    page.totalSize = result.length
    page.totalPageSize = Math.ceil(page.totalSize / page.pageSize)
    const pageResult = result.slice((page.pageNo - 1) * page.pageSize, page.pageNo * page.pageSize)
    const res = new WaitExchangeTrainClassDataVo()
    res.pageList = pageResult
    result?.map((item) => {
      if (item.commoditySkuId) {
        res.commoditySkuIdList.push(item.commoditySkuId)
      }
      if (item.periodId) {
        res.issueIdList.push(item.periodId)
      }
    })
    res.commoditySkuIdList = [...new Set(res.commoditySkuIdList)]
    res.issueIdList = [...new Set(res.issueIdList)]
    console.log('waitExchangeTrainClassList', result)
    return res
  }

  // endregion

  // region 超管-查询可更换的培训方案列表

  /**
   * 【超管】查询可更换的培训方案列表
   * @param {Page} page - 分页参数
   * @param {QueryReplaceableTrainClassListVo} queryParams - 查询参数
   */
  async queryReplaceableTrainClassList(
    page: Page,
    queryParams: QueryReplaceableTrainClassListVo
  ): Promise<ReplaceableTrainClassDetailVo[]> {
    const sortOption = new CommoditySkuSortRequest()
    sortOption.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(sortOption) as CommoditySkuSortRequest[]
    const request = queryParams.to()
    const response = await MsTradeQuery.pageCommoditySkuInServicer({
      page,
      queryRequest: request,
      sortRequest
    })
    console.log('queryParams', queryParams.to())
    page.totalSize = response.data?.totalSize
    page.totalPageSize = response.data?.totalPageSize
    const result =
      response.status?.isSuccess() && this.isWeightyArr(response.data?.currentPageData)
        ? await Promise.all(
            response.data?.currentPageData?.map(async (item) => {
              return await ReplaceableTrainClassDetailVo.from(item)
            })
          )
        : ([] as ReplaceableTrainClassDetailVo[])
    console.log('replaceableTrainClassList', result)
    return result
  }

  // endregion

  // region 专题管理员-查询可更换的培训方案列表

  /**
   * 【专题管理员】查询可更换的培训方案列表
   * @param {Page} page - 分页参数
   * @param {QueryReplaceableTrainClassListVo} queryParams - 查询参数
   */
  async queryReplaceableTrainClassListByTrainingChannel(page: Page, queryParams: QueryReplaceableTrainClassListVo) {
    const sortOption = new CommoditySkuSortRequest()
    sortOption.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(sortOption) as CommoditySkuSortRequest[]
    const request = queryParams.to()
    const response = await MsTradeQuery.pageCommoditySkuInTrainingChannel({
      page,
      queryRequest: request,
      sortRequest
    })
    console.log('queryParams', request)
    page.totalSize = response.data?.totalSize
    page.totalPageSize = response.data?.totalPageSize
    const result =
      response.status?.isSuccess() && this.isWeightyArr(response.data?.currentPageData)
        ? await Promise.all(
            response.data?.currentPageData?.map(async (item) => {
              return await ReplaceableTrainClassDetailVo.from(item)
            })
          )
        : ([] as ReplaceableTrainClassDetailVo[])
    console.log('replaceableTrainClassList', result)
    return result
  }

  // endregion

  // region 查询换班记录列表

  /**
   * 查询换班记录列表
   * @param {Page} page - 分页参数
   * @param {QueryExchangeOrderRecordListVo} queryParams - 查询参数
   */
  async queryExchangeTrainClassRecordList(
    page: Page,
    queryParams: QueryExchangeOrderRecordListVo
  ): Promise<ExchangeOrderRecordDetailVo[]> {
    const request = queryParams.to()
    const response = await MsTradeQuery.pageExchangeOrderInServicer({
      page,
      request
    })
    page.totalPageSize = response.data?.totalPageSize
    page.totalSize = response.data?.totalSize
    const result =
      response.status?.isSuccess() && this.isWeightyArr(response.data?.currentPageData)
        ? await Promise.all(
            response.data?.currentPageData?.map(async (item) => {
              return await ExchangeOrderRecordDetailVo.from(item)
            })
          )
        : ([] as ExchangeOrderRecordDetailVo[])
    return await this.fillManagerInfo(result)
  }

  /**
   * 获取管理员信息
   * @param {ExchangeOrderRecordDetailVo[]} arr - 数组信息
   */
  private async fillManagerInfo(arr: ExchangeOrderRecordDetailVo[]): Promise<ExchangeOrderRecordDetailVo[]> {
    const userIds = arr.map((item) => {
      return item.operatorId
    })
    if (!userIds.length) return arr
    const queryRemote: QueryManager = UserModule.queryUserFactory.queryManager
    const response = await queryRemote.queryManager(userIds)
    const result: Map<string, string> = new Map<string, string>()
    if (response.status?.isSuccess()) {
      response.data?.map((item: AdminUserInfoVo) => {
        result.set(item.userId, item.userName)
      })
    }
    arr.forEach((item: ExchangeOrderRecordDetailVo) => {
      item.operatorName = result.get(item.operatorId)
    })
    return arr
  }

  // endregion

  // region 查询期别更换记录

  /**
   * 查询期别更换记录
   * @param page 分页
   * @param param 筛选参数
   */
  async queryIssueChangeRecord(page: Page, param: QueryIssueChangeRecordParam) {
    const request = param.toRequest()
    const res = await MsTradeQuery.pageIssueLogInServicer({ page, request })
    page.totalPageSize = res.data?.totalPageSize
    page.totalSize = res.data?.totalSize
    const reslist = new Array<IssueChangeRecordItem>()
    if (res?.data?.currentPageData?.length) {
      res.data.currentPageData.map((item) => {
        reslist.push(IssueChangeRecordItem.from(item))
      })
    }

    return reslist
  }

  // endregion

  // region 查询待更换班级列表（包含退款中）

  /**
   * 查询待更换班级列表（包含退款中）
   * @param {Page} page - 分页参数
   * @param {QueryWaitExchangeTrainClassListVo} queryParams - 查询参数
   */
  async queryAllWaitExchangeTrainClassList(
    page: Page,
    queryParams: QueryWaitExchangeTrainClassListVo
  ): Promise<WaitExchangeTrainClassDataVo> {
    const userId = queryParams.buyerId
    const request = queryParams.to()
    const response = await MsTradeQuery.listBuyerAllCommodityInSerivicer(request)
    let result = new Array<WaitExchangeTrainClassDetailVo>()
    if (response.status.isSuccess() && this.isWeightyArr(response.data)) {
      result = WaitExchangeTrainClassDetailVo.batchFrom(response.data)
      const schemeIds = result.map((scheme) => {
        return scheme.schemeId
      })
      const schemeStatusMap = await WaitExchangeTrainClassDetailVo.batchGetUserStatusInScheme(userId, schemeIds)
      result = result.map((scheme) => {
        if (schemeStatusMap.has(scheme.schemeId)) {
          scheme.assessmentStatus = schemeStatusMap.get(scheme.schemeId).assessmentStatus
          scheme.status = schemeStatusMap.get(scheme.schemeId).status
        }
        return scheme
      })
    } else {
      result = new Array<WaitExchangeTrainClassDetailVo>()
    }
    page.totalSize = result.length
    page.totalPageSize = Math.ceil(page.totalSize / page.pageSize)
    const pageResult = result.slice((page.pageNo - 1) * page.pageSize, page.pageNo * page.pageSize)
    const res = new WaitExchangeTrainClassDataVo()
    res.pageList = pageResult
    result?.map((item) => {
      if (item.commoditySkuId) {
        res.commoditySkuIdList.push(item.commoditySkuId)
      }
      if (item.periodId) {
        res.issueIdList.push(item.periodId)
      }
    })
    res.commoditySkuIdList = [...new Set(res.commoditySkuIdList)]
    res.issueIdList = [...new Set(res.issueIdList)]
    console.log('waitExchangeTrainClassData', result)
    return res
  }

  // endregion

  // region 查询可更换期别列表

  /**
   * 查询可更换期别列表
   */
  async queryCanChangeIssueList(page: Page, param: QueryCanChangeIssueParam) {
    const request = param.toRequest()
    await ServiceTime.getServiceTime()
    const currentTime = ServiceTime.currentServiceTime
    // 过滤掉已过报道结束时间的数据
    request.issueSignUpEndDate.begin = currentTime
    // 过滤掉已过培训时间的数据
    request.issueTrainingEndTime.begin = currentTime
    const res = await MsTradeQuery.pageIssueCommoditySkuInServicer({ page, request })

    page.totalSize = res.data?.totalSize ?? 0
    page.totalPageSize = res.data?.totalPageSize ?? 0

    if (res.data.currentPageData.length) {
      const resultList = res.data.currentPageData.map((item) => {
        return ChangeIssueItem.from(item)
      })

      return resultList
    } else {
      return new Array<ChangeIssueItem>()
    }
  }

  // endregion

  /**
   * 数组是否有质量（是否不为空）
   */
  private isWeightyArr<T>(arr: T[]): boolean {
    return Array.isArray(arr) && arr.length ? true : false
  }
}

export default QueryExchangeTrainClass
