<!-- 学习日志 -->
<template>
  <div>
    <el-table
      :data="tableData"
      stripe
      max-height="500"
      highlight-current-row
      class="m-table"
      :span-method="objectSpanMethod"
    >
      <!-- <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column> -->
      <el-table-column label="No." min-width="60">
        <template slot-scope="{ row }">{{ row.index || '-' }}</template>
      </el-table-column>
      <el-table-column label="媒体名称" min-width="240">
        <template slot-scope="{ row }">{{ row.courseWareName || '-' }}</template>
      </el-table-column>
      <el-table-column label="播放时长" min-width="120">
        <template slot-scope="{ row }">{{ row.userLogInfo[0] ? row.userLogInfo[0].currentLength : '-' }}</template>
      </el-table-column>
      <el-table-column label="学习进度" min-width="120">
        <template slot-scope="{ row }"
          >{{ row.userLogInfo[0] ? row.userLogInfo[0].currentSchedule : '-'
          }}{{ row.userLogInfo[0] && row.userLogInfo[0].currentSchedule ? '%' : '' }}</template
        >
      </el-table-column>
      <el-table-column label="监管时间" min-width="200">
        <template slot-scope="{ row }">{{ row.userLogInfo[0] ? row.userLogInfo[0].verifiedTime : '-' }}</template>
      </el-table-column>
      <!-- <el-table-column label="拍摄照片" min-width="150">
        <template slot-scope="{ row }">
          <img
            v-if="row.userLogInfo[0] && row.userLogInfo[0].photoList"
            :src="
              row.userLogInfo[0] && row.userLogInfo[0].photoList ? addAccessToken(row.userLogInfo[0].photoList) : ''
            "
            alt=""
            class="u-benchmark-photos-small"
          />
          <img v-else src="@design/admin/assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
        </template>
      </el-table-column> -->
      <el-table-column label="监管方式" min-width="150">
        <template slot-scope="{ row }">{{ VerifyType.map.get(row.userLogInfo[0].antiType) }}</template>
      </el-table-column>
      <el-table-column label="监管结果" min-width="140" align="center">
        <template slot-scope="{ row }">
          <div v-if="result(row.userLogInfo[0])">
            <el-badge is-dot type="success" class="badge-status">通过</el-badge>
          </div>
          <div v-if="notPass(row.userLogInfo[0])">
            <el-badge is-dot type="danger" class="badge-status">不通过</el-badge>
          </div>
          <template
            v-if="
              row.userLogInfo[0].antiType === VerifyTypeEnum.face ||
              row.userLogInfo[0].antiType === VerifyTypeEnum.tencentFace
            "
          >
            <img
              v-if="row.userLogInfo[0] && row.userLogInfo[0].photoList"
              :src="
                row.userLogInfo[0] && row.userLogInfo[0].photoList ? addAccessToken(row.userLogInfo[0].photoList) : ''
              "
              alt=""
              class="u-benchmark-photos-small"
            />
            <img v-else src="@design/admin/assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
          </template>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import UserLearningLogItem from '@api/service/management/anticheat/models/UserLearningLogItem'
  import { CurrentResultEnum } from '@hbfe-biz/biz-anticheat/dist/log/enums/SupervisionEnum'
  import FileModule from '@api/service/common/file/FileModule'
  import UserLogInfo from '@api/service/management/anticheat/models/UserLogInfo'
  import VerifyType, { VerifyTypeEnum } from '@hbfe-biz/biz-anticheat/dist/config/enums/VerifyType'
  class MergeCourse {
    courseWare: string
    count: number
  }
  @Component
  export default class extends Vue {
    // 表单数据(课件监管信息)
    @Prop({
      type: Array,
      default: () => {
        return new Array<UserLearningLogItem>()
      }
    })
    tableData: Array<UserLearningLogItem>

    // 合并二维数组
    mergeList = new Array<Array<number>>()
    /**
     * 监管方式枚举
     */
    VerifyTypeEnum = VerifyTypeEnum
    VerifyType = VerifyType

    // 匹配结果
    get result() {
      return (res: UserLogInfo) => {
        return res && res.currentResult == CurrentResultEnum.PASS
      }
    }
    get notPass() {
      return (res: UserLogInfo) => {
        return res && res.currentResult == CurrentResultEnum.NOT_PASS
      }
    }

    // 合并规则
    getMergeRules() {
      const mergeList = new Array<Array<number>>()
      mergeList.length = this.tableData.length
      mergeList.fill([0, 0])
      const mergeCourseList = new Array<MergeCourse>()
      const courseWareIdList = new Array<string>()
      this.tableData.forEach((item: UserLearningLogItem) => {
        courseWareIdList.push(item.courseWareId)
      })
      this.tableData.forEach((item: any) => {
        const mergeObj = new MergeCourse()
        const mergeNum = courseWareIdList.reduce((prev: any, cur: string) => {
          if (cur in prev) {
            prev[cur]++
          } else {
            prev[cur] = 1
          }
          return prev
        }, {})
        if (!JSON.stringify(mergeCourseList).includes(item.courseWareId)) {
          mergeObj.courseWare = item.courseWareId
          mergeObj.count = mergeNum[item.courseWareId]
        } else {
          mergeObj.courseWare = ''
          mergeObj.count = 0
        }
        mergeCourseList.push(mergeObj)
      })
      console.log(mergeCourseList)
      for (let i = 0; i < mergeCourseList.length; i++) {
        mergeList[i] = [mergeCourseList[i].count, mergeCourseList[i].count >= 1 ? 1 : 0]
      }
      this.mergeList = mergeList
    }

    objectSpanMethod({ rowIndex, columnIndex }: { rowIndex: number; columnIndex: number }) {
      //   console.log(this.tableData, '======dadada')
      const obj = {
        rowspan: 0,
        colspan: 0
      }
      if ((columnIndex === 0 || columnIndex === 1) && this.mergeList[rowIndex]) {
        obj.rowspan = this.mergeList[rowIndex][0]
        obj.colspan = this.mergeList[rowIndex][1]
        console.log(obj, this.mergeList)
        return obj
      }
    }

    @Watch('tableData', { immediate: true, deep: true })
    async tableDataChange(newVal: Array<UserLearningLogItem>) {
      if (newVal && newVal.length) {
        this.getMergeRules()
        await FileModule.applyResourceAccessToken()
      }
    }

    get addAccessToken() {
      return (url: string) => {
        return `${window.location.origin}/mfs${url}?token=${FileModule.resourceAccessToken}`
      }
    }
  }
</script>
