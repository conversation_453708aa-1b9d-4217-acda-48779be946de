/*
 * @Description: 列表转换数据
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 11:53:50
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-01-29 15:29:52
 */

import { OrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { OrderRefundTypeEnum } from '@api/service/common/return-order/enums/OrderRefundType'

export default class CheckAccountListResponse {
  /**
   * 订单号
   */
  orderId?: string
  /**
   * 交易流水号
   */
  batchId?: string
  /**
   * 交易成功开始时间
   */
  startDate?: string
  /**
   * 购买人信息 - id
   */
  userId?: string
  /**
   * 购买人信息 - 购买人
   */
  name?: string
  /**
   * 购买人信息-登录账号
   */
  loginAccount?: string
  /**
   * 购买人信息 - 证件号
   */
  idCard?: string
  /**
   * 购买人信息 - 手机号
   */
  phone?: string
  /**
   * 实付金额
   */
  money?: number
  /**
   * 销售渠道
   */
  saleChannel?: number

  /**
   * 退款类型
   */
  refundType: OrderRefundTypeEnum = undefined

  static from(orderResponse: OrderResponse) {
    const {
      orderNo,
      payInfo: { flowNo, payAmount },
      basicData: {
        orderStatusChangeTime: { completed }
      },
      buyer: { userId },
      saleChannel
    } = orderResponse
    const refundCheckAccountListResponse = new CheckAccountListResponse()
    refundCheckAccountListResponse.batchId = flowNo
    refundCheckAccountListResponse.orderId = orderNo
    refundCheckAccountListResponse.startDate = completed
    refundCheckAccountListResponse.userId = userId
    refundCheckAccountListResponse.money = payAmount
    refundCheckAccountListResponse.saleChannel = saleChannel

    return refundCheckAccountListResponse
  }
  setUserInfo(idCard: string, name: string, phone: string, loginAccount: string) {
    this.idCard = idCard
    this.name = name
    this.phone = phone
    this.loginAccount = loginAccount
  }
}
