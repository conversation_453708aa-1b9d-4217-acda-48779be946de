import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * 学习方式枚举
 */
export enum LearningTypeEnum {
  /**
   * 选课学习方式
   */
  CHOOSE_COURSE_LEARNING = 1,
  /**
   * 考试学习方式
   */
  EXAM,
  /**
   * 练习学习方式
   */
  PRACTICE,
  /**
   * 自主学习课程学习方式
   */
  AUTONOMOUS_COURSE_LEARNING,
  /**
   * 兴趣课程学习方式
   */
  INTEREST_COURSE_LEARNING,
  /**
   * 教学计划学习方式
   */
  TEACHING_PLAN,
  /**
   * 教学计划项
   */
  TEACHING_PLAN_ITEM,
  /**
   * 调查问卷
   */
  QUESTIONNAIRE,
  /**
   * 学习心得学习方式
   */
  LEARNING_EXPERIENCE_LEARNING,
  /**
   * 教学计划学习方式
   */
  TEACH_PLAN_LEARNING
}

/**
 * 学习方式枚举
 */
class LearningTypeEnumClass extends AbstractEnum<LearningTypeEnum> {
  constructor(status?: LearningTypeEnum) {
    super()
    this.current = status
    this.map.set(LearningTypeEnum.CHOOSE_COURSE_LEARNING, '选课学习方式')
    this.map.set(LearningTypeEnum.EXAM, '考试学习方式')
    this.map.set(LearningTypeEnum.PRACTICE, '练习学习方式')
    this.map.set(LearningTypeEnum.AUTONOMOUS_COURSE_LEARNING, '自主学习课程学习方式')
    this.map.set(LearningTypeEnum.INTEREST_COURSE_LEARNING, '兴趣课程学习方式')
    this.map.set(LearningTypeEnum.TEACHING_PLAN, '教学计划学习方式')
    this.map.set(LearningTypeEnum.TEACHING_PLAN_ITEM, '教学计划项')
    this.map.set(LearningTypeEnum.QUESTIONNAIRE, '调查问卷')
    this.map.set(LearningTypeEnum.LEARNING_EXPERIENCE_LEARNING, '学习心得学习方式')
    this.map.set(LearningTypeEnum.TEACH_PLAN_LEARNING, '教学计划学习方式')
  }
}

export default LearningTypeEnumClass
