import countDailyPracticePartInUser from './queries/countDailyPracticePartInUser.graphql'
import countTotalErrorProne from './queries/countTotalErrorProne.graphql'
import countTotalUserEnterExam from './queries/countTotalUserEnterExam.graphql'
import countUserHasAnswerErrorProneQuestion from './queries/countUserHasAnswerErrorProneQuestion.graphql'
import exportUserAnswerRecord from './queries/exportUserAnswerRecord.graphql'
import getDefaultQuestionLibraryId from './queries/getDefaultQuestionLibraryId.graphql'
import getExamRoundBaseInfo from './queries/getExamRoundBaseInfo.graphql'
import getMaxPracticeCountAnswerPaper from './queries/getMaxPracticeCountAnswerPaper.graphql'
import getUserLastPracticeRecord from './queries/getUserLastPracticeRecord.graphql'
import listDailyPractice from './queries/listDailyPractice.graphql'
import listExamLearning from './queries/listExamLearning.graphql'
import listExamLibrary from './queries/listExamLibrary.graphql'
import listExamPaperClassify from './queries/listExamPaperClassify.graphql'
import listExamRecord from './queries/listExamRecord.graphql'
import listExamRound from './queries/listExamRound.graphql'
import listUserQuestionAnswerCountRank from './queries/listUserQuestionAnswerCountRank.graphql'
import listUserQuestionCorrectRateRank from './queries/listUserQuestionCorrectRateRank.graphql'
import pageExamLibrary from './queries/pageExamLibrary.graphql'
import pageUserAnswerRecord from './queries/pageUserAnswerRecord.graphql'
import statisticAnswerExamPaperReport from './queries/statisticAnswerExamPaperReport.graphql'
import statisticLibraryQuestionCount from './queries/statisticLibraryQuestionCount.graphql'
import statisticPlatformExamInfo from './queries/statisticPlatformExamInfo.graphql'
import statisticQuestionAnswerCountGroupDate from './queries/statisticQuestionAnswerCountGroupDate.graphql'
import statisticQuestionCorrectRate from './queries/statisticQuestionCorrectRate.graphql'
import statisticQuestionGroupByQuestionCategory from './queries/statisticQuestionGroupByQuestionCategory.graphql'
import statisticQuestionGroupByQuestionType from './queries/statisticQuestionGroupByQuestionType.graphql'
import statisticQuestionGroupByTagIds from './queries/statisticQuestionGroupByTagIds.graphql'
import statisticSingleQuestionAnswerInfo from './queries/statisticSingleQuestionAnswerInfo.graphql'
import statisticUserAnswerPerDay from './queries/statisticUserAnswerPerDay.graphql'
import statisticUserAnswerQuestionByTag from './queries/statisticUserAnswerQuestionByTag.graphql'
import statisticUserAnswerQuestionCountByQuestionCategory from './queries/statisticUserAnswerQuestionCountByQuestionCategory.graphql'
import statisticUserAnswerQuestionSummaryInfo from './queries/statisticUserAnswerQuestionSummaryInfo.graphql'
import statisticUserExamInfo from './queries/statisticUserExamInfo.graphql'
import statisticUserExamQuestionCorrectRateByQuestionType from './queries/statisticUserExamQuestionCorrectRateByQuestionType.graphql'
import statisticUserFavoriteQuestionCount from './queries/statisticUserFavoriteQuestionCount.graphql'
import statisticUserFavoriteQuestionGroupByQuestionCategory from './queries/statisticUserFavoriteQuestionGroupByQuestionCategory.graphql'
import statisticUserFavoriteQuestionGroupByQuestionType from './queries/statisticUserFavoriteQuestionGroupByQuestionType.graphql'
import statisticUserFavoriteQuestionGroupByTag from './queries/statisticUserFavoriteQuestionGroupByTag.graphql'
import statisticUserPractice from './queries/statisticUserPractice.graphql'
import statisticUserPracticeCorrectRateTrend from './queries/statisticUserPracticeCorrectRateTrend.graphql'
import statisticUserPracticeCountTrend from './queries/statisticUserPracticeCountTrend.graphql'
import statisticUserPracticeFrequency from './queries/statisticUserPracticeFrequency.graphql'
import statisticUserRemainErrorProneGroupByQuestionType from './queries/statisticUserRemainErrorProneGroupByQuestionType.graphql'
import statisticUserRemainErrorProneGroupByTag from './queries/statisticUserRemainErrorProneGroupByTag.graphql'
import statisticUserSingleQuestionAnswerInfo from './queries/statisticUserSingleQuestionAnswerInfo.graphql'
import statisticUserWrongQuestionGroupByQuestionCategory from './queries/statisticUserWrongQuestionGroupByQuestionCategory.graphql'
import statisticUserWrongQuestionGroupByQuestionType from './queries/statisticUserWrongQuestionGroupByQuestionType.graphql'
import statisticUserWrongQuestionGroupByTag from './queries/statisticUserWrongQuestionGroupByTag.graphql'
import statisticUserWrongQuestionSummary from './queries/statisticUserWrongQuestionSummary.graphql'
import importComprehensiveQuestion from './mutates/importComprehensiveQuestion.graphql'

export {
  countDailyPracticePartInUser,
  countTotalErrorProne,
  countTotalUserEnterExam,
  countUserHasAnswerErrorProneQuestion,
  exportUserAnswerRecord,
  getDefaultQuestionLibraryId,
  getExamRoundBaseInfo,
  getMaxPracticeCountAnswerPaper,
  getUserLastPracticeRecord,
  listDailyPractice,
  listExamLearning,
  listExamLibrary,
  listExamPaperClassify,
  listExamRecord,
  listExamRound,
  listUserQuestionAnswerCountRank,
  listUserQuestionCorrectRateRank,
  pageExamLibrary,
  pageUserAnswerRecord,
  statisticAnswerExamPaperReport,
  statisticLibraryQuestionCount,
  statisticPlatformExamInfo,
  statisticQuestionAnswerCountGroupDate,
  statisticQuestionCorrectRate,
  statisticQuestionGroupByQuestionCategory,
  statisticQuestionGroupByQuestionType,
  statisticQuestionGroupByTagIds,
  statisticSingleQuestionAnswerInfo,
  statisticUserAnswerPerDay,
  statisticUserAnswerQuestionByTag,
  statisticUserAnswerQuestionCountByQuestionCategory,
  statisticUserAnswerQuestionSummaryInfo,
  statisticUserExamInfo,
  statisticUserExamQuestionCorrectRateByQuestionType,
  statisticUserFavoriteQuestionCount,
  statisticUserFavoriteQuestionGroupByQuestionCategory,
  statisticUserFavoriteQuestionGroupByQuestionType,
  statisticUserFavoriteQuestionGroupByTag,
  statisticUserPractice,
  statisticUserPracticeCorrectRateTrend,
  statisticUserPracticeCountTrend,
  statisticUserPracticeFrequency,
  statisticUserRemainErrorProneGroupByQuestionType,
  statisticUserRemainErrorProneGroupByTag,
  statisticUserSingleQuestionAnswerInfo,
  statisticUserWrongQuestionGroupByQuestionCategory,
  statisticUserWrongQuestionGroupByQuestionType,
  statisticUserWrongQuestionGroupByTag,
  statisticUserWrongQuestionSummary,
  importComprehensiveQuestion
}
