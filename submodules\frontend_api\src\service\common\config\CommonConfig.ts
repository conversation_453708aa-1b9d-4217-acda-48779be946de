import CourseCategoryListDetail from '@api/service/management/resource/course-category/query/vo/CourseCategoryListDetail'
import CoursewareCategoryDetail from '@api/service/management/resource/courseware-category/query/vo/CoursewareCategoryDetail'

const basicRootId = '-1'
const rootCoursewareCategory = new CoursewareCategoryDetail()
rootCoursewareCategory.id = basicRootId
rootCoursewareCategory.name = '课件分类'

const rootCourseCategory = new CourseCategoryListDetail()
rootCourseCategory.name = '课程分类'
rootCourseCategory.id = basicRootId

export { basicRootId, rootCoursewareCategory, rootCourseCategory }
