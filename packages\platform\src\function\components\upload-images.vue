<template>
  <div>
    <el-upload
      action="#"
      list-type="picture-card"
      class="m-pic-upload small-pic"
      :class="{ disabled: imgList.length >= limit }"
      :disabled="imgList.length >= limit"
      :on-change="uploadImgChange"
      :auto-upload="false"
      :file-list="imgList"
      :limit="limit"
      :accept="imgType"
      :multiple="isMultiple"
    >
      <div slot="default" class="upload-placeholder">
        <i class="el-icon-plus"></i>

        <p class="txt" v-if="contentText">{{ contentText }}</p>
      </div>
      <div slot="file" slot-scope="{ file }" class="img-file">
        <el-image class="el-upload-list__item-thumbnail" :src="file.url" alt="" fit="fill"></el-image>

        <div class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <i class="el-icon-zoom-in"></i>
          </span>
          <span class="el-upload-list__item-delete" @click="handleRemove(file)">
            <i class="el-icon-delete"></i>
          </span>
        </div>
      </div>
      <div slot="tip" class="el-upload__tip">
        <i class="el-icon-warning"></i>
        <span class="txt">
          上传浏览器图标，适用于门户浏览器的图标（.ico格式）。请先设计好后上传，尺寸：宽度 {{ iconSizeList[0] }}px，
          高度{{ iconSizeList[1] }}px。
        </span>
      </div>
    </el-upload>
    <el-dialog :visible.sync="picDialogVisible" class="m-dialog-pic" :append-to-body="true" :lock-scroll="false">
      <img :src="dialogImageUrl" width="100%" alt="" />
    </el-dialog>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import axios from 'axios'
  import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

  @Component
  export default class extends Vue {
    loading = {}
    // 资源地址
    resourceUrl = ''
    // 图片预览对话框
    picDialogVisible = false
    // 预览地址
    dialogImageUrl = ''
    /**
     * 上传中间文字
     */
    @Prop({
      type: String,
      default: ''
    })
    contentText: string
    // 图片后缀
    @Prop({
      type: String,
      default: '.jpg, .png, .jpeg'
    })
    imgType: string
    // 数量限制
    @Prop({
      type: Number,
      default: 10
    })
    limit: string

    // 图片后缀的文字提示
    @Prop({
      type: String,
      default: '请上传*.jpg，*.jpeg，*.png格式的图片'
    })
    imgTypeTip: string

    // 默认支持多选
    @Prop({
      type: Boolean,
      default: true
    })
    isMultiple: boolean
    // 默认支持多选
    @Prop({
      type: Array,
      default: () => {
        return [16, 16]
      }
    })
    iconSizeList: Array<number>

    // 传给后端的图片路径数组/ 预览数组
    imgList = new Array<{ name: string; url: string }>()

    // 用于接收回显的图片数组
    @Prop([Array, Object])
    value: Array<any>
    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueWatch() {
      this.imgList = this.value ? this.value : []
    }
    created() {
      this.resourceUrl = ConfigCenterModule.getIngress('ingress.resource')
    }

    // 上传图片
    uploadImgChange(file: any, fileList: any) {
      this.$set(this.loading, file.uid, true)
      const imgReg = /\.ico$/
      if (imgReg.test(file?.name)) {
        // 校验通过之后，转为base图片
        const reader = new FileReader()
        reader.readAsDataURL(file.raw || file)
        reader.onloadend = () => {
          // 图片的 base64 格式, 可以直接当成 img 的 src 属性值
          const dataURL = reader.result as string
          this.getResourceUrl(dataURL, fileList, file.name, file.uid)
        }
      } else {
        return this.$message.warning('请上传指定后缀的图标！')
      }
    }

    // 保存符合格式的图片
    getResourceUrl(imgCode: string, fileList: any, fileName: string, uid: number) {
      const data = {
        base64Data: imgCode.split(',')[1].toString(),
        fileName
      }
      // 转为图片
      //   const baseUrl = `${this.resourceUrl}/auth/uploadBase64ToProtectedFile`
      const baseUrl = `${ConfigCenterModule.getIngress(ingress.apiendpoint)}/web/ms-file-v1/web/uploadPublicBase64`
      try {
        axios.post(baseUrl, data).then(data => {
          const imgUrl = data.data.data
          fileList[fileList.length - 1].name = imgUrl
          fileList[fileList.length - 1].url = '/mfs' + imgUrl
          this.imgList = fileList
          // console.log('后端用', this.imgList)
          this.$emit('input', this.imgList)

          this.$set(this.loading, uid, false)
        })
      } catch (error) {
        this.$message('图片转换失败')
      }
    }

    // 预览图片
    handlePictureCardPreview(file: any) {
      if (file?.url) {
        this.dialogImageUrl = file.url
        this.picDialogVisible = true
      } else {
        this.$message.error('图片预览地址出错！')
      }
    }

    /* 删除图片*/
    handleRemove(file: any) {
      const idx: number = this.imgList.findIndex(el => el.url === file.url)
      this.imgList.splice(idx, 1)
      // console.log('删除后的：', this.imgList)
    }
  }
</script>
<style lang="scss" scoped>
  ::v-deep div.el-dialog__body {
    text-align: center;
  }
</style>

<style rel="stylesheet/scss" lang="scss">
  .disabled .el-upload--picture-card {
    display: none;
  }
</style>
