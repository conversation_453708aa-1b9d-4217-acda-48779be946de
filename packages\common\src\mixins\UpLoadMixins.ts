import { Component, Emit, Prop, Vue, Ref } from 'vue-property-decorator'
import ConfigCenterModule from '@api/service/common/config-center/ConfigCenter'
import axios from 'axios'

@Component
export default class UpLoadMixins extends Vue {
  /**
   * 资源服务地址
   */
  resourceUrl: any
  types = ['image/jpeg', 'image/gif', 'image/bmp', 'image/png', 'image/jpg']
  res: any
  imageCode = ''
  defaultImg = ''

  // 取决于是单张图片还是数组 方便后面混入这个文件的自由配置
  @Prop({
    type: [String, Array],
    required: false,
    default: () => {
      return ''
    }
  })
  value: any

  // 是否多选
  multiple = false

  @Ref('elImage') elImage: any

  /**
   * 预览
   */
  handlePictureCardPreview(file?: any) {
    // this.dialogVisible = true
    // https://element.eleme.cn/#/zh-CN/component/image
    this.clickHandler()
  }

  clickHandler() {
    this.$nextTick(() => {
      // 触发点击方法
      this.elImage.clickHandler()
    })
  }

  @Emit('input')
  imgCodeChange(val: string): any {
    return val
  }
  async init() {
    this.difference()
    this.resourceUrl = ConfigCenterModule.getIngressByName('ingress.resource')
  }

  // 差异化处理 依引用的页面觉定
  difference() {
    if (!Array.isArray(this.value)) {
      this.defaultImg = this.value ? this.value : ''
    }
  }

  beforeAvatarUpload(file: any, fileList: any) {
    // 大小 单位M
    const is2MB = file.size / 1024 / 1024 < 2
    // 图片格式
    const isImage = this.types.includes(file?.raw?.type)

    if (!isImage) {
      this.$message.error('仅支持以下格式: image/jpeg, image/gif, image/bmp, image/png')
      return false
    }
    if (!is2MB) {
      this.$message.error('图片超过2M')
      return false
    }
    // 实现图片覆盖
    if (fileList.length > 1 && !this.multiple) {
      fileList.splice(0, 1)
    }
    // 校验通过之后，转为base图片
    const reader = new FileReader()
    reader.readAsDataURL(file.raw)
    reader.onloadend = async () => {
      // 图片的 base64 格式, 可以直接当成 img 的 src 属性值
      const dataURL = reader.result
      await this.getResourceUrl(dataURL)
    }
    return is2MB && isImage
  }

  async getResourceUrl(imageCode: any) {
    const data = {
      base64Data: imageCode.split(',')[1].toString()
    }
    // 转为图片
    const baseUrl = `${this.resourceUrl}/auth/uploadBase64ToProtectedFile`

    try {
      axios.post(baseUrl, data).then(data => {
        this.imageCode = data.data.url
        this.imgCodeChange(this.imageCode)
      })
    } catch (error) {
      this.$message('图片转换失败')
    }
  }

  handleAvatarSuccess(response: any, file: any) {
    console.log(response, file)
    this.$message.success('上传图片成功！')
  }

  async created() {
    this.init()
  }
}
