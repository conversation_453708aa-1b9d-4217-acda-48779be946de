<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <div class="f-plr20 f-pt20">
      <el-table stripe :data="examPaperList" max-height="500px" class="m-table">
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="场次名称" min-width="240" fixed="left">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <el-table-column label="试卷名称" min-width="240">
          <template slot-scope="scope">{{ scope.row.paperPublishConfigureName }}</template>
        </el-table-column>
        <el-table-column label="组卷方式" min-width="120">
          <template>智能组卷</template>
        </el-table-column>
        <el-table-column label="考试时长 / 总分 / 及格分" min-width="190" align="center">
          <template slot-scope="scope">{{ scope.row.timeLength }} / 100 / {{ scope.row.examPassScore }}</template>
        </el-table-column>
        <el-table-column label="作答次数" min-width="120" align="center">
          <template slot-scope="scope">{{ scope.row.allowCount == -1 ? '不限次' : scope.row.allowCount }}</template>
        </el-table-column>
      </el-table>
      <el-form ref="form" label-width="auto" class="is-column f-mt30">
        <el-form-item label="前置条件：" style="margin: auto">{{
          examLearningInfo.preCondition ? '完成课程学习考核' : '无'
        }}</el-form-item>
        <el-form-item label="培训要求：" style="margin: auto">以班级考核配置为准</el-form-item>
      </el-form>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">班级考试要求</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="examLearningInfo" label-width="150px" class="m-form f-mt10">
              <el-form-item label="纳入考核：">
                {{ examLearningInfo.isExamAssessed ? '是' : '否' }}
              </el-form-item>
              <el-form-item label="成绩要求：">
                <p>
                  {{ examLearningInfo.isExamAssessed ? '班级考试考核成绩' : '班级考试及格分' }} ≥
                  {{ examLearningInfo.examPassScore }}
                  分（总分100分，考试次数：<i class="f-cr">{{ examAllowCountConfigure }}</i> 次）
                </p>
              </el-form-item>
              <el-form-item label="前置条件：">
                {{ examLearningInfo.preCondition ? '完成课程学习考核' : '无' }}
              </el-form-item>
              <el-form-item label="考核要求：">
                <p>
                  1.
                  {{ examLearningInfo.isExamAssessed ? '考试纳入考核' : '班级考试不纳入考核' }}，班级考试成绩 ≥
                  {{ examLearningInfo.examPassScore }}
                  分，{{ examLearningInfo.isExamAssessed ? '视为通过' : '考试合格' }}
                </p>
                <p v-if="examLearningInfo.preCondition">2. 参加考试前需完成课程学习考核</p>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, PropSync } from 'vue-property-decorator'
  import ExamLearningType from '@api/service/management/train-class/mutation/vo/ExamLearningType'

  @Component
  export default class extends Vue {
    @PropSync('examLearning', { type: ExamLearningType }) examLearningInfo!: ExamLearningType

    examPaperList: Array<ExamLearningType> = new Array<ExamLearningType>()
    /**
     * 考试次数
     */
    get examAllowCountConfigure() {
      return this.examLearningInfo.allowCount === -1 ? '不限' : this.examLearningInfo.allowCount
    }
    created() {
      if (this.examLearningInfo.paperPublishConfigureId) {
        const option = new ExamLearningType()
        option.name = this.examLearningInfo.name
        option.timeLength = this.examLearningInfo.timeLength
        option.examPassScore = this.examLearningInfo.examPassScore
        option.allowCount = this.examLearningInfo.allowCount
        option.paperPublishConfigureName = this.examLearningInfo.paperPublishConfigureName
        this.examPaperList.push(option)
      }
    }
  }
</script>
