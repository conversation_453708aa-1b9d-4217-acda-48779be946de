import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-shortcode-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-shortcode-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 申请短链请求
<AUTHOR> create 2022/7/9 9:32
 */
export class ApplyShortLinkRequest {
  /**
   * 跳转URL
   */
  url?: string
  /**
   * 元数据
   */
  metadata?: Map<string, string>
  /**
   * 失效时的跳转URL
   */
  invalidUrl?: string
  /**
   * 失效时的元数据
   */
  invalidMetadata?: Map<string, string>
  /**
   * 失效时间，null为不自动失效
   */
  autoInvalidTime?: string
}

/**
 * 更新短码短链
 */
export class UpdateShortCodeRequest {
  /**
   * 短码
   */
  shortCode?: string
  /**
   * 跳转URL
   */
  url?: string
}

/**
 * 申请短链结果
<AUTHOR> create 2022/7/9 9:33
 */
export class ApplyShortLinkResultResponse {
  /**
   * 状态码。
200 &#x3D; 成功
   */
  statusCode: string
  /**
   * 信息
   */
  message: string
  /**
   * 短码
   */
  shortCode: string
  /**
   * 短链
   */
  shortLink: string
}

/**
 * 短码响应
<AUTHOR> create 2022/7/9 9:11
 */
export class ShortCodeResponse {
  /**
   * 跳转URL，当短码失效后返回 invalidUrl
   */
  url: string
  /**
   * 元数据,当短码失效后返回 invalidMetadata
   */
  metadata: Map<string, string>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取短码信息
   * @param shortCode
   * @return
   * @param query 查询 graphql 语法文档
   * @param shortCode 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getShortCode(
    shortCode: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getShortCode,
    operation?: string
  ): Promise<Response<ShortCodeResponse>> {
    return commonRequestApi<ShortCodeResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { shortCode },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据短码获取重定向的url
   * @param shortCode
   * @return
   * @param query 查询 graphql 语法文档
   * @param shortCode 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getUrlByShortCode(
    shortCode: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getUrlByShortCode,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { shortCode },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请短链
   * @param applyShortLinkRequest
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param applyShortLinkRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyShortLink(
    applyShortLinkRequest: ApplyShortLinkRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyShortLink,
    operation?: string
  ): Promise<Response<ApplyShortLinkResultResponse>> {
    return commonRequestApi<ApplyShortLinkResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyShortLinkRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 更新短码短链
   * @param applyShortLinkRequest
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param applyShortLinkRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateShortLink(
    applyShortLinkRequest: UpdateShortCodeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateShortLink,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyShortLinkRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
