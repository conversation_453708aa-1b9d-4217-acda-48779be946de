import DiffSchemeConfigGatewayBackstage from '@api/platform-gateway/platform-scheme-config-backstage-v1'
import PlatformAhjspxptAttendanceTemplate, {
  AttendanceSignDto,
  CreateAttendanceTemplateRequest,
  UpdateAttendanceTemplateRequest
} from '@api/platform-gateway/platform-jxjy-attendance-template-v1'
import AttendanceConfigDto from '@api/service/common/implement/models/AttendanceConfigDto'
import { ResponseStatus } from '@hbfe/common'

export default class AttendanceTemplateConfig {
  /**
   * 考勤模板id
   */
  attendanceTemplateId: string = undefined

  /**
   * 是否开启模板
   */
  settingTemplate = false

  /**
   * 签到配置
   */
  signIn: AttendanceConfigDto = new AttendanceConfigDto()

  /**
   * 签退配置
   */
  signOut: AttendanceConfigDto = new AttendanceConfigDto()

  /**
   * 查询配置
   */
  async queryTemplate() {
    const res = await DiffSchemeConfigGatewayBackstage.getTrainingConfigTemplateInServicer()

    if (res?.data) {
      const { attendanceTemplateId, attendanceSignIn, attendanceSignOut } = res.data
      this.attendanceTemplateId = attendanceTemplateId
      if (this.attendanceTemplateId) {
        this.settingTemplate = true
      }
      // 签到
      this.signIn.isOpen = attendanceSignIn.enable
      this.signIn.checkInFrequency = attendanceSignIn.frequency
      this.signIn.checkInLocationRadius = attendanceSignIn.radius
      this.signIn.preCheckInTime = attendanceSignIn.beforeSecond
        ? attendanceSignIn.beforeSecond / 60
        : attendanceSignIn.beforeSecond
      this.signIn.afterCheckInTime = attendanceSignIn.afterSecond
        ? attendanceSignIn.afterSecond / 60
        : attendanceSignIn.afterSecond
      // 签退
      this.signOut.isOpen = attendanceSignOut.enable
      this.signOut.checkInFrequency = attendanceSignOut.frequency
      this.signOut.checkInLocationRadius = attendanceSignOut.radius
      this.signOut.preCheckInTime = attendanceSignOut.beforeSecond
        ? attendanceSignOut.beforeSecond / 60
        : attendanceSignOut.beforeSecond
      this.signOut.afterCheckInTime = attendanceSignOut.afterSecond
        ? attendanceSignOut.afterSecond / 60
        : attendanceSignOut.afterSecond
    }
  }

  /**
   * 创建模板配置
   */
  async createTemplate() {
    const request = new CreateAttendanceTemplateRequest()
    const signInDto = new AttendanceSignDto()
    const signOutDto = new AttendanceSignDto()
    // 签到
    signInDto.enable = this.signIn.isOpen
    if (this.signIn.preCheckInTime) {
      signInDto.beforeSecond = this.signIn.preCheckInTime * 60
    }
    if (this.signIn.afterCheckInTime) {
      signInDto.afterSecond = this.signIn.afterCheckInTime * 60
    }
    signInDto.radius = this.signIn.checkInLocationRadius
    signInDto.frequency = this.signIn.checkInFrequency

    // 签退
    signOutDto.enable = this.signOut.isOpen
    if (this.signOut.preCheckInTime) {
      signOutDto.beforeSecond = this.signOut.preCheckInTime * 60
    }
    if (this.signOut.afterCheckInTime) {
      signOutDto.afterSecond = this.signOut.afterCheckInTime * 60
    }
    signOutDto.radius = this.signOut.checkInLocationRadius
    signOutDto.frequency = this.signOut.checkInFrequency

    request.attendanceSignIn = signInDto
    request.attendanceSignOut = signOutDto
    const res = await PlatformAhjspxptAttendanceTemplate.createAttendanceTemplate(request)

    if (res.status && res.status.isSuccess()) {
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   * 更新模板配置
   */
  async updateTemplate() {
    const request = new UpdateAttendanceTemplateRequest()
    const signInDto = new AttendanceSignDto()
    const signOutDto = new AttendanceSignDto()
    // 签到
    signInDto.enable = this.signIn.isOpen
    if (this.signIn.preCheckInTime) {
      signInDto.beforeSecond = this.signIn.preCheckInTime * 60
    }
    if (this.signIn.afterCheckInTime) {
      signInDto.afterSecond = this.signIn.afterCheckInTime * 60
    }
    signInDto.radius = this.signIn.checkInLocationRadius
    signInDto.frequency = this.signIn.checkInFrequency

    // 签退
    signOutDto.enable = this.signOut.isOpen
    if (this.signOut.preCheckInTime) {
      signOutDto.beforeSecond = this.signOut.preCheckInTime * 60
    }
    if (this.signOut.afterCheckInTime) {
      signOutDto.afterSecond = this.signOut.afterCheckInTime * 60
    }
    signOutDto.radius = this.signOut.checkInLocationRadius
    signOutDto.frequency = this.signOut.checkInFrequency

    request.attendanceId = this.attendanceTemplateId
    request.attendanceSignIn = signInDto
    request.attendanceSignOut = signOutDto
    const res = await PlatformAhjspxptAttendanceTemplate.updateAttendanceTemplate(request)

    if (res.status && res.status.isSuccess()) {
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   * 删除模板配置
   */
  async deleteTemplate() {
    const res = await PlatformAhjspxptAttendanceTemplate.deleteAttendanceTemplate(this.attendanceTemplateId)

    if (res.status && res.status.isSuccess()) {
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }
}
