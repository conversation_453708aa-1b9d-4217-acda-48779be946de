import exportCommodityDistributorOpenReportInDistributor from './queries/exportCommodityDistributorOpenReportInDistributor.graphql'
import exportCommodityOpenStatisticsDetailExcelInSupplier from './queries/exportCommodityOpenStatisticsDetailExcelInSupplier.graphql'
import exportCommodityOpenStatisticsSummaryExcelInSupplier from './queries/exportCommodityOpenStatisticsSummaryExcelInSupplier.graphql'
import exportDistributionCommodityExcelInSupplier from './queries/exportDistributionCommodityExcelInSupplier.graphql'
import exportDistributionCommodityManageExcelInSupplier from './queries/exportDistributionCommodityManageExcelInSupplier.graphql'
import exportDistributorSalesStatisticsExcelInSupplier from './queries/exportDistributorSalesStatisticsExcelInSupplier.graphql'
import exportMyTradeRecordInDistributor from './queries/exportMyTradeRecordInDistributor.graphql'
import exportSubDistributorSellStatisticInDistributor from './queries/exportSubDistributorSellStatisticInDistributor.graphql'
import listExportTaskGroupInfoInAdmin from './queries/listExportTaskGroupInfoInAdmin.graphql'
import listExportTaskGroupInfoInDistributor from './queries/listExportTaskGroupInfoInDistributor.graphql'
import listExportTaskGroupInfoInServicer from './queries/listExportTaskGroupInfoInServicer.graphql'
import listExportTaskGroupInfoInSupplier from './queries/listExportTaskGroupInfoInSupplier.graphql'
import pageExportTaskInfoInMyself from './queries/pageExportTaskInfoInMyself.graphql'

export {
  exportCommodityDistributorOpenReportInDistributor,
  exportCommodityOpenStatisticsDetailExcelInSupplier,
  exportCommodityOpenStatisticsSummaryExcelInSupplier,
  exportDistributionCommodityExcelInSupplier,
  exportDistributionCommodityManageExcelInSupplier,
  exportDistributorSalesStatisticsExcelInSupplier,
  exportMyTradeRecordInDistributor,
  exportSubDistributorSellStatisticInDistributor,
  listExportTaskGroupInfoInAdmin,
  listExportTaskGroupInfoInDistributor,
  listExportTaskGroupInfoInServicer,
  listExportTaskGroupInfoInSupplier,
  pageExportTaskInfoInMyself
}
