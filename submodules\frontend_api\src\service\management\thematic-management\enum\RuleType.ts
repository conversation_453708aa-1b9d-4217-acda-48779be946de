import AbstractEnum from '@api/service/common/enums/AbstractEnum'
// 定义域名类型
export enum RuleTypeEnum {
  /**
   * 统一标记
   */
  unification_mark = '1',
  /**
   * 不标记
   */
  no_mark = '2',
  /**
   * 专业科目
   */
  specialized_subjects = '3',
  /**
   * 公需科目
   */
  common_subjects = '4'
}
export default class RuleType extends AbstractEnum<RuleTypeEnum> {
  static enum = RuleTypeEnum
  constructor(status?: RuleTypeEnum) {
    super()
    this.current = status
    this.map.set(RuleTypeEnum.unification_mark, '统一标记')
    this.map.set(RuleTypeEnum.no_mark, '不标记')
    this.map.set(RuleTypeEnum.specialized_subjects, '专业科目')
    this.map.set(RuleTypeEnum.common_subjects, '公需科目')
  }
}
