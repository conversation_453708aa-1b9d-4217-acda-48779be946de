import StudentCoursewareRecordDetailVo from '@api/service/management/resource/courseware/query/vo/StudentCoursewareRecordDetailVo'
import { Page } from '@hbfe/common'
import QueryStudentCoursewareRecordListVo from '@api/service/management/resource/courseware/query/vo/QueryStudentCoursewareRecordListVo'
import MsCourseLearningBackstage, {
  CoursewareRequest,
  CoursewareResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import DataResolve from '@api/service/common/utils/DataResolve'
/**
 * @description 查询学员课件
 */

class QueryStudentCourseware {
  /**
   * 根据课件id集合获取课件列表
   * @param {string[]} coursewareIds - 课件id集合
   */
  async getCoursewareListByCoursewareIds(coursewareIds: string[]): Promise<CoursewareResponse[]> {
    let result = [] as CoursewareResponse[]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 1
    const coursewareIdList = [...new Set(coursewareIds)]
    if (!DataResolve.isWeightyArr(coursewareIdList)) return result
    const request = new CoursewareRequest()
    request.coursewareIdList = coursewareIds
    const response = await MsCourseLearningBackstage.pageCoursewareInServicer({
      page,
      request
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      page.pageSize = response.data.totalSize
      const res = await MsCourseLearningBackstage.pageCoursewareInServicer({
        page,
        request
      })
      if (res.status?.isSuccess() && DataResolve.isWeightyArr(res.data?.currentPageData)) {
        result = res.data.currentPageData
      }
    }
    return result
  }

  /**
   * 查询学员所有课件学习记录
   */
  async queryStudentAllCoursewareRecord(
    request: QueryStudentCoursewareRecordListVo
  ): Promise<StudentCoursewareRecordDetailVo[]> {
    let result = [] as StudentCoursewareRecordDetailVo[]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 1
    const response = await MsCourseLearningBackstage.pageCoursewareLearningRecordInServicer({
      page,
      request
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      page.pageSize = response.data.totalSize
      const res = await MsCourseLearningBackstage.pageCoursewareLearningRecordInServicer({
        page,
        request
      })
      if (res.status?.isSuccess() && DataResolve.isWeightyArr(res.data?.currentPageData)) {
        result = res.data.currentPageData.map(StudentCoursewareRecordDetailVo.from)
      }
    }
    return await this.fillCoursewareInfo(result)
  }

  /**
   * 查询学员课件学习记录列表
   */
  async queryStudentCoursewareRecordList(
    page: Page,
    request: QueryStudentCoursewareRecordListVo
  ): Promise<StudentCoursewareRecordDetailVo[]> {
    let result = [] as StudentCoursewareRecordDetailVo[]
    const response = await MsCourseLearningBackstage.pageCoursewareLearningRecordInServicer({
      page,
      request
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = response.data.currentPageData.map(StudentCoursewareRecordDetailVo.from)
    }
    return await this.fillCoursewareInfo(result)
  }

  /**
   * 填充课件信息
   */
  private async fillCoursewareInfo(
    list: StudentCoursewareRecordDetailVo[]
  ): Promise<StudentCoursewareRecordDetailVo[]> {
    const coursewareIds = [...new Set(list.map(item => item.id))]
    const response = await this.getCoursewareListByCoursewareIds(coursewareIds)
    if (!DataResolve.isWeightyArr(response)) return list
    list?.forEach(item => {
      const coursewareInfo = response.find(el => el.id === item.id)
      if (coursewareInfo) {
        item.name = coursewareInfo.name ?? ''
        item.coursewareTimeLength = coursewareInfo.timeLength ?? 0
      }
    })
    return list
  }
}

export default QueryStudentCourseware
