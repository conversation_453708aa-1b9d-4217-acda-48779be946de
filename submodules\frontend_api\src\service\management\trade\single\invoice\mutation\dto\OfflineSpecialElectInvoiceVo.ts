import OffLinePageInvoiceResponseVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
import {
  UpdateOfflineInvoiceRequest,
  UpdateOfflineSpecialElectronicInvoiceRequest
} from '@api/ms-gateway/ms-offlineinvoice-v1'

export default class OfflineSpecialElectInvoiceVo extends OffLinePageInvoiceResponseVo {
  static to(vo: OffLinePageInvoiceResponseVo): UpdateOfflineSpecialElectronicInvoiceRequest {
    const request = new UpdateOfflineSpecialElectronicInvoiceRequest()

    request.offlineInvoiceId = vo.invoiceId
    // 此方法只针对开电子票 故写死传 2 （1电子， 2纸质）
    request.invoiceType = 1
    request.invoiceCategory = vo.invoiceCategory
    request.title = vo.title
    request.titleType = vo.titleType
    request.taxpayerNo = vo.taxpayerNo
    request.address = vo.address
    request.phone = vo.rePhone
    request.bankName = vo.bankName
    request.account = vo.account
    request.contactEmail = vo.contactEmail
    request.contactPhone = vo.contactPhone
    request.businessLicensePath = vo.businessLicenseUrl
    request.accountOpeningLicensePath = vo.permitUrl
    request.remark = vo.remark
    request.invoiceVerifyStrategy = vo.invoiceVerifyStrategy
    return request
  }
}
