<script lang="ts">
  import { Component } from 'vue-property-decorator'
  import RedInvoice from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/red-invoice.vue'
  import TradeExport from '@api/service/diff/management/jxgx/trade/TradeExport'

  @Component
  export default class extends RedInvoice {
    // 这里有坑 如果有页面插槽的话 要注意了 目前只能继承
    /**
     * 线上发票导出
     */
    tradeExport = new TradeExport()

    /**
     * 线上发票导出
     */
    async onLinePageInvoiceInExport() {
      return await this.tradeExport.onLinePageInvoiceInExport(this.exportQueryParam)
    }
  }
</script>
