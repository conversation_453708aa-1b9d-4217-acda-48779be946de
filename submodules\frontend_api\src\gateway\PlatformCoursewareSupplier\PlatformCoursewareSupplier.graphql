schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""子项目管理员课件供应商详情"""
	detail(id:String):ServicerDetailDto
	"""子项目管理员课件供应商详情"""
	page(page:Page,params:CoursewareSupplierQueryParams):CoursewareSupplierListDtoPage @page(for:"CoursewareSupplierListDto")
	"""与当前培训机构签约的课件供应商列表
		@param params 查询条件
		@param page   分页信息
		@return 课件供应商列表
	"""
	pageForTrainingInstitution(page:Page,params:CoursewareSupplierForTInstitutionQueryParams):CoursewareSupplierForTIDtoPage @page(for:"CoursewareSupplierForTIDto")
}
type Mutation {
	"""创建课件供应商（通过请求直接创建）
		@return
	"""
	createCoursewareSupplier(token:String,params:ServicerCreateParams):GraphqlOperateResult @NotAuthenticationRequired
	"""创建提供企业账户课件供应商（通过请求直接创建）
		@return
	"""
	createCoursewareSupplierByAccount(token:String,accountId:String,params:ServicerCreateParams):GraphqlOperateResult @NotAuthenticationRequired
}
"""创建服务商参数"""
input ServicerCreateParams @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.servicer.common.ServicerCreateParams") {
	"""合作机构id"""
	trainingInstitutionId:String
	"""服务商名称"""
	name:String!
	"""所在地区路径"""
	areaPath:String
	"""说明信息"""
	description:String
	"""负责人"""
	userName:String
	"""手机号"""
	phoneNumber:String
}
"""与当前培训机构角色签约的课件供应商查询条件
	<AUTHOR>
	@since 2021/11/1
"""
input CoursewareSupplierForTInstitutionQueryParams @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.servicer.coursewareSupplier.CoursewareSupplierForTInstitutionQueryParams") {
	"""名称"""
	name:String
	"""课件供应商地区"""
	regionPath:[String]
	"""合作签约开始时间"""
	beginTime:DateTime
	"""合作签约结束时间"""
	endTime:DateTime
	"""合作状态"""
	contractStatus:ServicerContractStatusEnums
	"""手机号"""
	phone:String
}
"""课件供应商查询信息"""
input CoursewareSupplierQueryParams @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.servicer.coursewareSupplier.CoursewareSupplierQueryParams") {
	"""培训机构Id"""
	trainingInstitutionIdList:[String]
	"""课件供应商名称"""
	name:String
	"""合作状态"""
	status:ServicerContractStatusEnums
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""graphql没有泛型的操作结果类
	<AUTHOR> create 2020/3/9 15:18
"""
type GraphqlOperateResult @type(value:"com.fjhb.btpx.integrative.gateway.graphql.dto.GraphqlOperateResult") {
	"""返回的code"""
	code:String
	"""返回的message"""
	message:String
	"""json字段
		存放返回的参数用
	"""
	expandData:Map
}
enum ServicerContractStatusEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerContractStatusEnums") {
	"""全部"""
	ALL
	"""合作"""
	NORMAL
	"""中止"""
	SUSPEND
}
enum ServicerTypeEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerTypeEnums") {
	"""全部"""
	ALL
	"""培训机构"""
	TRAINING_INSTITUTION
	"""课件供应商"""
	COURSEWARE_SUPPLIER
	"""渠道商"""
	CHANNEL_VENDOR
	"""参训单位"""
	PARTICIPATING_UNIT
}
"""服务商合约状态"""
enum ServicerContractStatusEnum @type(value:"com.fjhb.btpx.platform.enums.ServicerContractStatusEnum") {
	"""合作"""
	NORMAL
	"""中止"""
	SUSPEND
}
"""账号信息"""
type AccountDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.common.AccountDto") {
	"""帐号"""
	loginAccount:String
	"""姓名"""
	name:String
	"""手机号"""
	phone:String
	"""创建时间"""
	createTime:DateTime
	"""创建人"""
	creator:String
}
"""有合约的服务商信息"""
type ServicerContractDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.common.ServicerContractDto") {
	"""服务商 Id"""
	id:String
	"""服务商类型"""
	servicerType:ServicerTypeEnums
	"""名称"""
	name:String
	"""合作状态"""
	status:ServicerContractStatusEnums
	"""服务商合作日志"""
	contractLogList:[ServicerContractLogDto]
}
"""服务商签约日志返回值"""
type ServicerContractLogDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.common.ServicerContractLogDto") {
	"""合作状态"""
	contractLogStatus:ServicerContractStatusEnum
	"""操作时间"""
	operationTime:DateTime
	"""操作人"""
	operationUserName:String
}
"""服务详情信息（渠道商、课件供应商）"""
type ServicerDetailDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.common.ServicerDetailDto") {
	"""Id"""
	id:String
	"""课件供应商名称"""
	name:String
	"""地区名称列表，省市区县..."""
	regionNames:[String]
	"""所在地区路径"""
	regionPath:String
	"""负责人"""
	contactPerson:String
	"""手机号"""
	phone:String
	"""课件供应商优势简述"""
	abouts:String
	"""合作状态"""
	status:ServicerContractStatusEnums
	"""有合约的服务商信息"""
	servicerContracts:[ServicerContractDto]
	"""账号信息"""
	accounts:[AccountDto]
}
"""课件供应商列表信息"""
type CoursewareSupplierForTIDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.coursewareSupplier.CoursewareSupplierForTIDto") {
	"""Id"""
	id:String
	"""课件供应商名称"""
	name:String
	"""地区名称列表，省市区县..."""
	regionNames:[String]
	"""所在地区路径"""
	regionPath:String
	"""负责人"""
	contactPerson:String
	"""手机号"""
	phone:String
	"""创建时间"""
	createTime:DateTime
	"""合作状态"""
	status:ServicerContractStatusEnums
	"""签约时间"""
	contractTime:DateTime
}
"""课件供应商列表信息"""
type CoursewareSupplierListDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.coursewareSupplier.CoursewareSupplierListDto") {
	"""Id"""
	id:String
	"""课件供应商名称"""
	name:String
	"""地区名称列表，省市区县..."""
	regionNames:[String]
	"""所在地区路径"""
	regionPath:String
	"""负责人"""
	contactPerson:String
	"""手机号"""
	phone:String
	"""创建时间"""
	createTime:DateTime
	"""合作状态"""
	status:ServicerContractStatusEnums
}

scalar List
type CoursewareSupplierListDtoPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CoursewareSupplierListDto]}
type CoursewareSupplierForTIDtoPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CoursewareSupplierForTIDto]}
