<route-meta>
{
"isMenu": true,
"title": "分销商品开通统计",
"sort": 14,
"icon": "icon-mingxi"
}
</route-meta>
<script lang="ts">
  import SupplierDistributionOfGoodsOpenStatistics from '@hbfe/jxjy-admin-supplierDistributionOfGoodsOpenStatistics/src/diff/xmlg/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { NZGYS, NZGYSJCB } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    querySupplieDistribution: [NZGYS, NZGYSJCB],
    exportDataSupplieDistribution: [NZGYS, NZGYSJCB]
  })
  export default class extends SupplierDistributionOfGoodsOpenStatistics {}
</script>
