import MutationAffterBatchOrderSignUp from '@api/service/centre/trade/batch/order/mutation/MutationAffterBatchOrderSignUp'
import MutationBatchOrder from '@api/service/centre/trade/batch/order/mutation/MutationBatchOrder'
import MutationBatchOrderPay from '@api/service/centre/trade/batch/order/mutation/MutationBatchOrderPay'
import MutationBatchOrderSignUp from '@api/service/centre/trade/batch/order/mutation/MutationBatchOrderSignUp'

/**
 * 【集体管理员】集体报名订单-业务工厂
 */
class MutationBatchOrderFactory {
  /**
   * 集体报名列表项
   * @param batchOrderNo 批次号
   * @returns
   */
  getBatchOrder() {
    return new MutationBatchOrder()
  }

  /**
   * 集体报名 -- 报名前
   * @returns
   */
  getBeforeBatchOrderSignUp() {
    return new MutationBatchOrderSignUp()
  }

  /**
   * 集体报名 -- 报名后
   * @returns
   */
  getAffterBatchOrderSignUpData(orderNo: string) {
    return new MutationAffterBatchOrderSignUp(orderNo)
  }

  /**
   * 集体报名付款
   * @param batchOrderNo 批次号
   * @returns
   */
  getBatchOrderPay(batchOrderNo: string) {
    return new MutationBatchOrderPay(batchOrderNo)
  }
}

export default MutationBatchOrderFactory
