import QuestionType from '@api/service/common/enums/question/QuestionType'
import CreateQuestionVo from './CreateQuestionVo'
import ChooseAnswerOptionVo from '../ChooseAnswerOptionVo'
import { CreateMultipleQuestionRequest } from '@api/ms-gateway/ms-examquestion-v1'
/**
 * *多选题
 */
class CreateMultipleQuestionVo extends CreateQuestionVo {
  questionType = QuestionType.enum.multiple
  /**
   * 可选答案列表【必填】
   */
  answerOptions: Array<ChooseAnswerOptionVo> = new Array<ChooseAnswerOptionVo>()
  /**
   * 正确答案ID集合【必填】
   */
  correctAnswerIds: Array<string> = []

  // 模型转换为Dto
  toDto() {
    const createQuestionDto = new CreateMultipleQuestionRequest()
    createQuestionDto.topic = this.topic
    createQuestionDto.questionType = this.questionType
    createQuestionDto.libraryId = this.libraryId
    createQuestionDto.enabled = this.enabled
    createQuestionDto.dissects = this.dissects
    createQuestionDto.relateCourseIds = [this.relateCourseId]
    createQuestionDto.questionDifficulty = this.questionDifficulty
    createQuestionDto.answerOptions = this.answerOptions?.map(item => {
      return {
        id: item.id,
        content: item.content
      }
    })
    createQuestionDto.correctAnswerIds = this.correctAnswerIds?.map(id => {
      return id
    })
    return createQuestionDto
  }
}

export default CreateMultipleQuestionVo
