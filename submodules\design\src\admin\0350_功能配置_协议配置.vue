<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="注册登录" name="first">
        <div class="f-p15">
          <el-tabs v-model="activeName2" type="card" class="m-tab-card">
            <el-tab-pane label="注册设置" name="first">
              详见 0301_功能设置_注册登录.vue
            </el-tab-pane>
            <el-tab-pane label="登录设置" name="second">
              详见 0301_功能设置_注册登录.vue
            </el-tab-pane>
            <el-tab-pane label="验证设置" name="third">
              详见 0301_功能设置_注册登录.vue
            </el-tab-pane>
            <el-tab-pane label="协议设置" name="fourth">
              <el-card shadow="never" class="m-card f-mb15">
                <div class="m-attribute">
                  <el-collapse v-model="activeNames" @change="handleChange" accordion>
                    <el-collapse-item name="1">
                      <template slot="title">
                        <div class="m-sub-tit f-align-center">
                          <span class="tit-txt">注册协议</span>
                          <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                            <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                            <div slot="content">
                              <p>注册协议适用于学员账号、集体报名管理员账号注册环节。</p>
                              <p>开启后，学员、集体报名管理员注册时需勾选本协议。</p>
                            </div>
                          </el-tooltip>
                        </div>
                      </template>
                      <el-row type="flex" justify="center" class="width-limit f-mt20">
                        <el-col :md="20" :lg="16" :xl="13">
                          <el-form ref="form" :model="form" label-width="auto" class="m-form">
                            <el-form-item label="是否开启注册协议：">
                              <el-switch
                                v-model="form.delivery"
                                active-text="开启"
                                inactive-text="关闭"
                                class="m-switch"
                              />
                            </el-form-item>
                            <el-form-item label="协议名称：" required>
                              <el-input v-model="form.name" clearable placeholder="请输入协议名称（不超过30个字）" />
                            </el-form-item>
                            <el-form-item label="协议内容：" required>
                              <el-input type="textarea" :rows="10" v-model="form.desc" />
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-collapse-item>
                    <el-collapse-item name="2">
                      <template slot="title">
                        <div class="m-sub-tit f-align-center">
                          <span class="tit-txt">登录协议</span>
                          <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                            <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                            <div slot="content">
                              <p>登录协议适用于学员账号、集体报名管理员账号登录环节（含账号登录及微信扫码登录）</p>
                              <p>开启后，学员、集体报名管理员登录时需勾选本协议。</p>
                            </div>
                          </el-tooltip>
                        </div>
                      </template>
                      <el-row type="flex" justify="center" class="width-limit f-mt20">
                        <el-col :md="20" :lg="16" :xl="13">
                          <el-form ref="form" :model="form" label-width="auto" class="m-form">
                            <el-form-item label="是否开启注册协议：">
                              <el-switch
                                v-model="form.delivery"
                                active-text="开启"
                                inactive-text="关闭"
                                class="m-switch"
                              />
                            </el-form-item>
                            <el-form-item label="协议名称：" required>
                              <el-input v-model="form.name" clearable placeholder="请输入协议名称（不超过30个字）" />
                            </el-form-item>
                            <el-form-item label="协议内容：" required>
                              <el-input type="textarea" :rows="10" v-model="form.desc" />
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </el-card>
              <div class="m-btn-bar f-tc is-sticky-1">
                <el-button>取消</el-button>
                <el-button type="primary">保存</el-button>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      <el-tab-pane label="集体报名" name="second">详见 0303_功能设置_集体报名.vue</el-tab-pane>
      <el-tab-pane label="增值税电子普通发票（自动开票）" name="third">详见 0304_功能设置_增值税发票.vue</el-tab-pane>
      <el-tab-pane label="培训证明" name="fourth">详见 0305_功能设置_培训证明.vue</el-tab-pane>
      <el-tab-pane label="视频播放设置" name="five">详见 0306_功能设置_视频播放设置.vue</el-tab-pane>
      <el-tab-pane label="门户精品课程" name="six">详见 0307_功能设置_门户精品课程.vue</el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'fourth',
        activeNames: ['1', '2'],
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          delivery1: true,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
