<template>
  <div>
    <basic-data-diff ref="basicDataDiffRef">
      <template #topicinformation="{ studentParams }">
        <el-card shadow="never" class="m-card is-header f-mb15">
          <div slot="header" class="">
            <span class="tit-txt">专题信息</span>
          </div>
          <div class="f-pb20 f-pt10">
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt f-flex-sub">金昌市</span>
              <a
                class="f-cb"
                href="javascript:void(0);"
                v-if="$hasPermission('editThematicInformation')"
                desc="编辑专题信息"
                actions="@ThematicInformationDialog"
                @click="handleEdit()"
                ><span class="el-icon-edit-outline edit-icon f-mr5"></span>编辑</a
              >
            </div>
            <div class="f-plr40">
              <el-row :gutter="16">
                <el-form :inline="true" label-width="auto" class="m-text-form is-edit f-mt20">
                  <el-col :sm="12" :md="8">
                    <el-form-item label="工作单位性质：">
                      {{ getDisplayValue(studentParams.topicInformationVo.natureWorkUnitName) }}</el-form-item
                    >
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="在编情况：">
                      {{ getDisplayValue(studentParams.topicInformationVo.currentSituationName) }}
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="是否在专技岗位工作：">
                      {{ getDisplayValue(studentParams.topicInformationVo.isOnTechnicalPostName) }}
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="职称系列：">
                      {{ getDisplayValue(studentParams.topicInformationVo.titleSeriesName) }}
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="职称专业：">
                      {{ getDisplayValue(studentParams.topicInformationVo.professionalTitle) }}
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="现有职称等级：">
                      {{ getDisplayValue(studentParams.topicInformationVo.currentTitleLevelName) }}
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="现有职称资格名称：">
                      {{ getDisplayValue(studentParams.topicInformationVo.currentTitleQualification) }}
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="现有职称有效范围：">
                      {{ getDisplayValue(studentParams.topicInformationVo.currentTitleValidRangeName) }}
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="最高学历：">
                      {{ getDisplayValue(studentParams.topicInformationVo.highestEducation) }}
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
          </div>
        </el-card>
      </template>
    </basic-data-diff>
    <thematic-information-dialog ref="thematicInformationRef" @updateUserInfo="updateUserInfo">
    </thematic-information-dialog>
  </div>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import BasicData from '@hbfe/jxjy-admin-customerService/src/personal/components/basic-data.vue'
  import ThematicInformationDialog from '@hbfe/jxjy-admin-customerService/src/diff/gszj/personal/__components__/thematic-information-dialog.vue'
  import { ProxyRef } from '@api/service/common/utils/ProxyRefMethods'
  import UserDetailVoDiff from '@api/service/diff/management/gszj/user/student/model/UserDetailVo'
  import MutationUpdateStudentInfo from '@api/service/diff/management/gszj/user/student/MutationUpdateStudentInfo'
  import QueryStudentDetailDiff from '@api/service/diff/management/gszj/user/student/QueryStudentDetail'

  @Component
  class BasicDataDiff extends BasicData {
    // 修改入参
    studentParams = new UserDetailVoDiff()

    // 学员修改实例
    studentUpdateObj = new MutationUpdateStudentInfo()

    async userIdChange(id: string) {
      if (id) {
        this.teacherParams = []
        await this.queryStuDetailById()
        await this.queryStudentFieldVerifyToken()
        await this.historyPhotoList(id)
      } else {
        this.studentParams = new UserDetailVoDiff()
      }
    }

    async handleQueryDetail() {
      const queryStudentDetailDiff = new QueryStudentDetailDiff(this.userId)
      const res = await queryStudentDetailDiff.queryDetailDiff()
      return res
    }
  }

  @Component({
    components: { BasicDataDiff, ThematicInformationDialog }
  })
  @ProxyRef('basicDataDiffRef', true)
  export default class extends Vue {
    @Ref('basicDataDiffRef') basicDataDiffRef: BasicDataDiff
    @Ref('thematicInformationRef') thematicInformationRef: ThematicInformationDialog

    /**
     * 编辑
     */
    handleEdit() {
      if (!this.basicDataDiffRef.studentParams.idCard) {
        return
      }
      this.thematicInformationRef.drawer = true
      this.thematicInformationRef.transformData(
        this.basicDataDiffRef.studentParams,
        this.basicDataDiffRef.studentUpdateObj
      )
    }

    /**
     * 更新用户信息
     */
    updateUserInfo() {
      this.basicDataDiffRef.updateUserInfo()
    }

    getDisplayValue(value: string | null): string {
      return value ? value : '-'
    }
  }
</script>
