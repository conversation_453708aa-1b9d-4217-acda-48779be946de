/**
 * 试题类型对象
 */
import { ExamConstant } from '@/store/module/exam/common/ExamConstant'
import { ChoiceItemRequest } from '@api/gateway/PreExam-default'

export class QuestionCategory {
  /**
   * 值value
   */
  value: string
  /**
   * 中文显示
   */
  title: string

  /**
   * 构建函数
   * @param value
   * @param title
   */
  static build(value: string, title: string) {
    const item = new QuestionCategory()
    item.value = value
    item.title = title
    return item
  }
}

/**
 * 试题题型对象
 */
export class QuestionType {
  /**
   * 值value
   */
  value: number
  /**
   * 中文显示
   */
  title: string

  /**
   * 构建函数
   * @param value
   * @param title
   */
  static build(value: number, title: string) {
    const item = new QuestionType()
    item.value = value
    item.title = title
    return item
  }
}

/**
 * 考試相關的object
 */
export class ExamObject {
  /**
   * 属性key
   */
  key: any
  /**
   * 属性value
   */
  value: any
  /**
   * 属性名称
   */
  title: string

  /**
   * 构建函数
   * @param title
   * @param value
   */
  static build(title: any, value: any) {
    const item = new ExamObject()
    item.title = title
    item.value = value
    return item
  }
}

/**
 *
 * 考试相关的工具
 * @author: eleven
 * @date: 2020/4/10
 */

export class ExamUtil {
  /**
   * 系统提供的试题题型
   */
  static questionTypeList = new Array<QuestionType>()
  /**
   * 系统提供的试题类型
   */
  static questionCategoryList = new Array<QuestionCategory>()
  /**
   * 是否易错题选项
   */
  static errorPhoneOptionList = new Array<ExamObject>()
  /**
   * 是否启用状态
   */
  static enableOptionList = new Array<ExamObject>()
  /**
   * 试卷状态
   */
  static paperStatusOptionList = new Array<ExamObject>()

  /**
   * 获取系统提供的试题类型
   */
  static productQuestionCategoryList() {
    if (this.questionCategoryList.length) {
      return this.questionCategoryList
    }
    this.questionCategoryList.push(
      QuestionCategory.build(ExamConstant.QUESTION_CATEGORY_REAL, ExamConstant.QUESTION_CATEGORY_REAL_TITLE)
    )
    this.questionCategoryList.push(
      QuestionCategory.build(ExamConstant.QUESTION_CATEGORY_PRACTICE, ExamConstant.QUESTION_CATEGORY_PRACTICE_TITLE)
    )
    this.questionCategoryList.push(
      QuestionCategory.build(ExamConstant.QUESTION_CATEGORY_SIMULATION, ExamConstant.QUESTION_CATEGORY_SIMULATION_TITLE)
    )
    return this.questionCategoryList
  }

  /**
   * 获取系统提供的试题题型
   */
  static productQuestionTypeList() {
    if (this.questionTypeList.length) {
      return this.questionTypeList
    }
    this.questionTypeList.push(
      QuestionType.build(ExamConstant.QUESTION_TYPE_SINGLE, ExamConstant.QUESTION_TYPE_SINGLE_TITLE)
    )
    this.questionTypeList.push(
      QuestionType.build(ExamConstant.QUESTION_TYPE_JUDGEMENT, ExamConstant.QUESTION_TYPE_JUDGEMENT_TITLE)
    )
    this.questionTypeList.push(
      QuestionType.build(ExamConstant.QUESTION_TYPE_MULTIPLE, ExamConstant.QUESTION_TYPE_MULTIPLE_TITLE)
    )
    // this.questionTypeList.push(
    //   QuestionType.build(ExamConstant.QUESTION_TYPE_COMPREHENSIVE, ExamConstant.QUESTION_TYPE_COMPREHENSIVE_TITLE)
    // )
    return this.questionTypeList
  }

  /**
   * 获取系统提供的组卷方式
   */
  static productPaperConfigType() {
    const list = new Array<ExamObject>()
    list.push(ExamObject.build(ExamConstant.CONFIG_TYPE_RANDOM_TITLE, ExamConstant.CONFIG_TYPE_RANDOM))
    // list.push(ExamObject.build(ExamConstant.CONFIG_TYPE_FIXED_TITLE, ExamConstant.CONFIG_TYPE_FIXED))
    return list
  }

  /**
   * 生成一个选项
   * @param index
   */
  static productChoiceItem(index: number) {
    const choice = new ChoiceItemRequest()
    choice.id = index + ''
    return choice
  }

  /**
   * 解析题类
   * @param questionCategory
   */
  static resolverQuestionCategory(questionCategory: string) {
    if (ExamConstant.QUESTION_CATEGORY_REAL === questionCategory) {
      return '真题'
    }
    if (ExamConstant.QUESTION_CATEGORY_PRACTICE === questionCategory) {
      return '练习题'
    }
    if (ExamConstant.QUESTION_CATEGORY_SIMULATION === questionCategory) {
      return '模拟题'
    }
    return '未知'
  }

  /**
   * 解析题型
   * @param questionType
   */
  static resolverQuestionType(questionType: number) {
    if (ExamConstant.QUESTION_TYPE_JUDGEMENT === questionType) {
      return '判断'
    }
    if (ExamConstant.QUESTION_TYPE_SINGLE === questionType) {
      return '单选'
    }
    if (ExamConstant.QUESTION_TYPE_MULTIPLE === questionType) {
      return '多选'
    }
    if (ExamConstant.QUESTION_TYPE_BLANK_FILLING === questionType) {
      return '填空'
    }
    if (ExamConstant.QUESTION_TYPE_ESSAY === questionType) {
      return '问答'
    }
    if (ExamConstant.QUESTION_TYPE_COMPREHENSIVE === questionType) {
      return '案例'
    }
    if (ExamConstant.QUESTION_TYPE_SCALE === questionType) {
      return '量表'
    }
    return '未知'
  }

  /**
   *  是否易错题选项
   */
  static productErrorProneOption() {
    if (this.errorPhoneOptionList.length) {
      return this.errorPhoneOptionList
    }
    this.errorPhoneOptionList.push(ExamObject.build('是', true))
    this.errorPhoneOptionList.push(ExamObject.build('否', false))
    return this.errorPhoneOptionList
  }

  /**
   *  是否启用选项
   */
  static productEnableOption() {
    if (this.enableOptionList.length) {
      return this.enableOptionList
    }
    this.enableOptionList.push(ExamObject.build('全部', -1))
    this.enableOptionList.push(ExamObject.build('启用', 0))
    this.enableOptionList.push(ExamObject.build('停用', 1))
    return this.enableOptionList
  }

  /**
   *  试卷状态
   */
  static productPaperStatusOption() {
    if (this.paperStatusOptionList.length) {
      return this.paperStatusOptionList
    }
    this.paperStatusOptionList.push(ExamObject.build('草稿', 2))
    this.paperStatusOptionList.push(ExamObject.build('停用', 1))
    this.paperStatusOptionList.push(ExamObject.build('正常', 0))
    return this.paperStatusOptionList
  }

  /**
   * 匹配指定 index 的字符
   * @param index
   */
  static matchCharCode(index: number) {
    return String.fromCharCode(65 + index)
  }
}
