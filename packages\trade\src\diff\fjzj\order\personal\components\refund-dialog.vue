<template>
  <el-drawer title="退款" :visible.sync="isShow" size="900px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-alert type="warning" show-icon :closable="false" class="m-alert">
        退款后，原有的学习记录将清空，请确认是否继续。
      </el-alert>
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form ref="formRef" :rules="rules" :model="form" label-width="auto" class="m-form f-mt20">
            <el-form-item label="退款金额：" class="is-text">
              <span class="f-cr f-fb f-f16">¥ {{ refundDetail ? refundDetail.price : '' }}</span>
              <span class="f-cr">（换班订单退款退的是初始物品的订单价格，退物品是最新换入成功的物品。）</span>
            </el-form-item>
            <el-form-item label="退款说明：" class="is-text">
              退款后，原有的学习记录将清空，请确认是否继续。
            </el-form-item>
            <el-form-item
              label="退款提示："
              v-if="
                isForce &&
                foreseReasonArr.length === 2 &&
                foreseReasonArr.indexOf(1) != -1 &&
                foreseReasonArr.indexOf(2) == -1
              "
              class="is-text"
            >
              班级已考核通过，是否强制退款？
            </el-form-item>
            <el-form-item
              label="退款提示："
              v-if="
                isForce &&
                foreseReasonArr.length === 1 &&
                foreseReasonArr.indexOf(1) != -1 &&
                foreseReasonArr.indexOf(2) == -1
              "
              class="is-text"
            >
              该培训班的学习进度已达到100%，是否强制退款？
            </el-form-item>
            <el-form-item
              label="退款提示："
              v-if="
                isForce && foreseReasonArr.length == 2 && foreseReasonArr.includes(1) && foreseReasonArr.includes(2)
              "
              class="is-text"
            >
              此订单的发票已开具且培训班学习进度已达到100%，是否强制退款？
            </el-form-item>
            <el-form-item label="退款提示：" v-if="isForce && foreseReasonArr.length == 3" class="is-text">
              此订单的发票已开具且培训班已考核通过，是否强制退款？
            </el-form-item>
            <el-form-item
              label="退款提示："
              v-if="isForce && foreseReasonArr.length === 1 && foreseReasonArr.indexOf(2) != -1"
              class="is-text"
            >
              此订单已开票，是否强制退款？
            </el-form-item>
            <el-form-item label="退款理由：" required prop="reason">
              <el-select v-model="form.reason" clearable placeholder="请选择退款理由">
                <el-option
                  v-for="refundReasonItem in refundReasonList"
                  :key="refundReasonItem.reasonId"
                  :label="refundReasonItem.reasonContent"
                  :value="refundReasonItem.reasonId"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="退款说明：" required prop="explain">
              <el-input type="textarea" :rows="6" v-model="form.explain" placeholder="请输入退款说明" />
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="isShowDialog">取消</el-button>
              <el-button type="primary" @click="submit" :loading="loading">确认退款</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import {
    ReturnReasonInfoResponse,
    SubOrderResponse
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { Component, Vue, Prop, Ref } from 'vue-property-decorator'
  import { ForceReasonEnum } from '@api/service/management/trade/single/order/enum/ForceReasonEnum'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import OrderDetailVo from '@api/service/management/trade/single/order/query/vo/UserOrderDetailVo'
  @Component
  export default class extends Vue {
    @Prop({ type: Object, default: {} }) refundDetail: SubOrderResponse
    @Prop({ type: Array, default: {} }) refundReasonList: Array<ReturnReasonInfoResponse>
    @Prop({ type: Boolean, default: false }) isForce: boolean
    @Prop({ type: Array, default: [] }) foreseReasonArr: Array<ForceReasonEnum>
    @Prop({ type: Number, default: 3 }) returnOrderType: number
    @Prop({ type: Object, default: {} }) orderDetail: OrderDetailVo
    @Ref('formRef') formRef: any
    isShow = false
    loading = false
    mutationCreateReturnOrderObj = TradeModule.singleTradeBatchFactor.mutationFactory.getMutationCreateReturnOrder()

    form = {
      reason: '', // 退款理由
      explain: '', // 退款说明
      orderNo: '', // 订单号
      subOrderNo: '' // 子订单号
    }

    rules = {
      reason: [{ required: true, message: '请选择退款理由', trigger: 'blur' }],
      explain: [{ required: true, message: '请输入退款说明', trigger: ['blur', 'change'] }]
    }

    isShowDialog() {
      this.isShow = !this.isShow
    }

    submit() {
      this.formRef.validate(async (valid: any) => {
        if (valid) {
          // TODO
          this.loading = true
          this.form.orderNo = this.refundDetail.orderNo
          this.form.subOrderNo = this.refundDetail.subOrderNo
          const res = await this.refund(this.form, this.returnOrderType)
          this.$emit('refundClick', res)
          this.isShow = false
        }
      })
    }
    // 确认退款事件
    async refund(item: any, returnOrderType: number) {
      this.mutationCreateReturnOrderObj.orderNo = item.orderNo
      this.mutationCreateReturnOrderObj.subOrderNo = item.subOrderNo
      this.mutationCreateReturnOrderObj.reasonId = item.reason
      this.mutationCreateReturnOrderObj.description = item.explain
      const res = await this.mutationCreateReturnOrderObj.agreeReturnApply(returnOrderType)
      this.loading = false
      return res
    }
  }
</script>

<style scoped></style>
