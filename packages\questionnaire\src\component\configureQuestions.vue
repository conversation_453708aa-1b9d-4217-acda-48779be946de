<template>
  <div>
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :span="20" class="f-pt30 f-pb30">
        <div class="m-no-date" v-if="!questionList.length">
          <img class="img" src="@design/admin/assets/images/no-data-normal.png" />
          <div class="date-bd">
            <p class="f-f15 f-c9">暂时还没有内容~</p>
            <div class="f-mt10">
              <el-button
                type="primary"
                size="small"
                :disabled="isDisabled"
                @click="addQuestion"
                icon="el-icon-plus"
                plain
                >添加试题</el-button
              >
            </div>
          </div>
        </div>
        <div class="f-flex f-justify-between f-align-center" v-else>
          <el-button type="primary" :disabled="isDisabled" @click="addQuestion" icon="el-icon-plus" plain
            >添加试题</el-button
          >
          <el-checkbox
            :value="mustAnswered"
            v-if="questionList && questionList.length"
            :disabled="isDisabled"
            @change="clickAll"
            >全部为必答</el-checkbox
          >
        </div>
        <el-form label-width="70px" class="m-form" ref="dragWebTable" :key="basicKey">
          <div class="m-questionnaire-set f-clear f-mt20" v-for="(item, index) in questionList" :key="index">
            <div class="num">{{ padStartString(index) }}</div>
            <div class="big-label" :class="{ 'big-label-2': !item.isMustAnswered }" @click="clickMustAnswered(item)">
              {{ item.isMustAnswered ? '必答' : '选答' }}
            </div>
            <el-form-item class="is-text" v-if="item.type === QuestionTypeEnum.single">
              <div slot="label" class="f-f16 f-cb">{{ QuestionType.map.get(item.type) }}</div>
              <div class="f-f16">
                <!-- <div v-html="item.optionQuestion.described"></div> -->
                <component :is="richValue(item.optionQuestion.described)"></component>
              </div>
              <el-row :gutter="20" v-if="item.optionQuestion.options && !item.isTeacherEvaluate">
                <el-radio-group>
                  <el-col :span="24" class="f-mt10" v-for="(itm, idx) in item.optionQuestion.options" :key="idx">
                    <el-radio label="1">
                      <!-- <div v-html="itm.content"></div>-->
                      <div style="display: flex">
                        <span class="f-cb f-fb f-mr5">{{ resolverIndexToCharCode(idx) }}.</span>
                        <component :is="richValue(itm.content)"></component>
                      </div>
                    </el-radio>
                  </el-col>
                </el-radio-group>
              </el-row>
              <span class="f-cb" v-if="item.isTeacherEvaluate"
                >选项为引用此问卷模板的方案或期别中，课程授课教师名称</span
              >
            </el-form-item>
            <el-form-item class="is-text" v-if="item.type === QuestionTypeEnum.multiple">
              <div slot="label" class="f-f16 f-cb">{{ QuestionType.map.get(item.type) }}</div>
              <div class="f-f16">
                <!-- <div v-html="item.multipleOptionQuestion.described"></div> -->
                <component :is="richValue(item.multipleOptionQuestion.described)"></component>
              </div>
              <el-row :gutter="20" v-if="item.multipleOptionQuestion.options && !item.isTeacherEvaluate">
                <el-radio-group v-if="!item.isTeacherEvaluate">
                  <el-col :span="24" class="f-mt10" v-for="(itm, idx) in item.multipleOptionQuestion.options" :key="idx"
                    ><el-checkbox :value="1" style="display: flex; align-items: center">
                      <div style="display: flex">
                        <span class="f-cb f-fb f-mr5">{{ resolverIndexToCharCode(idx) }}.</span
                        ><component :is="richValue(itm.content)"></component>
                      </div> </el-checkbox
                  ></el-col>
                </el-radio-group>
              </el-row>
              <span class="f-cb" v-if="item.isTeacherEvaluate"
                >选项为引用此问卷模板的方案或期别中，课程授课教师名称</span
              >
            </el-form-item>
            <el-form-item class="is-text" v-if="item.type === QuestionTypeEnum.answer">
              <div slot="label" class="f-f16 f-cb">{{ QuestionType.map.get(item.type) }}</div>
              <div class="f-f16">
                <!-- <div v-html="item.answerQuestion.described"></div> -->
                <component :is="richValue(item.answerQuestion.described)"></component>
              </div>
              <el-row :gutter="20">
                <div class="f-mt10 f-plr10">
                  <el-input type="textarea" :rows="10" />
                </div>
              </el-row>
            </el-form-item>
            <el-form-item class="is-text" v-if="item.type === QuestionTypeEnum.gauge">
              <div slot="label" class="f-f16 f-cb">{{ QuestionType.map.get(item.type) }}</div>
              <div class="f-f16">
                <!-- <div v-html="item.gaugeQuestion.described"></div> -->
                <component :is="richValue(item.gaugeQuestion.described)"></component>
              </div>
              <el-row :gutter="20">
                <el-col :span="24" class="f-mt10">
                  <span class="f-mr30" v-if="item.gaugeQuestion.type !== GaugeTypeEnum.customer"
                    >非常不{{ GaugeQuestionTypeName(item.gaugeQuestion.type) }}</span
                  >
                  <span v-else>{{ item.gaugeQuestion.minDeepTip }}</span>

                  <el-radio
                    v-for="value in GaugeQuestionRow(item.gaugeQuestion).array"
                    :key="value"
                    :label="value"
                    class="f-mr20"
                    >{{ value }}</el-radio
                  >
                  <span v-if="item.gaugeQuestion.type !== GaugeTypeEnum.customer"
                    >非常{{ GaugeQuestionTypeName(item.gaugeQuestion.type) }}</span
                  >
                  <span v-else>{{ item.gaugeQuestion.maxDeepTip }}</span>
                </el-col>
              </el-row>
            </el-form-item>
            <div class="bottom">
              <el-button
                icon="el-icon-edit"
                size="mini"
                type="primary"
                :disabled="isDisabled"
                @click="editQuestion(item, index)"
                plain
                >编辑</el-button
              >
              <el-button
                icon="el-icon-delete"
                size="mini"
                type="primary"
                :disabled="isDisabled"
                @click="questionDelete(item, index)"
                plain
                >删除</el-button
              >
              <el-button
                icon="el-icon-upload2"
                size="mini"
                type="primary"
                @click="questionPostition(item, 'first')"
                :disabled="index === 0 || isDisabled"
                plain
                >最前</el-button
              >
              <el-button
                icon="el-icon-download"
                size="mini"
                type="primary"
                @click="questionPostition(item, 'last')"
                plain
                :disabled="index === questionList.length - 1 || isDisabled"
                >最后</el-button
              >
            </div>
          </div>
        </el-form>
      </el-col>
    </el-row>
    <QuestionDrawer
      ref="questionDrawerRef"
      :questionType="questionType"
      :business="business"
      :isDisableQuestion="isDisabled"
      :isDisableDraft="isDisableDraft"
      @handleChange="handleChange"
    ></QuestionDrawer>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, PropSync, Ref, Vue, Watch } from 'vue-property-decorator'
  import QuestionDrawer from './questionDrawer.vue'
  import DataResolve from './DataResolve'
  import QuestionType, { QuestionTypeEnum } from '@api/service/common/enums/question-naire/QuestionType'
  import Sortable from 'sortablejs'
  import Question from '@api/service/common/question-naire/Question'
  import GaugeType, { GaugeTypeEnum } from '@api/service/common/question-naire/enums/GaugeType'
  import GaugeQuestion from '@api/service/common/question-naire/GaugeQuestion'
  import { ExamUtil } from '@/store/module/exam/common/ExamUtil'
  @Component({
    components: {
      QuestionDrawer
    }
  })
  export default class extends Vue {
    @Prop({ type: Boolean, default: false }) isDisabled: boolean
    @Prop({ type: Boolean, default: false }) isDisableDraft: boolean
    @Prop({ type: Number, default: 1 }) questionType: number //问卷类型
    @PropSync('questionListInfo', { type: Array }) questionList: Array<Question>
    @Ref('dragWebTable') dragWebTable: any
    @Ref('questionDrawerRef') questionDrawerRef: QuestionDrawer //抽屉ref
    QuestionTypeEnum = QuestionTypeEnum //试题类型枚举
    isCopy = false
    QuestionType = new QuestionType() //试题类型枚举
    mustAnswered = true //全部为必答
    editItem = new Question() //修改当前项
    business = '' //业务类型
    currentIdx = 0 //当前标识
    GaugeType = new GaugeType() //量级枚举
    GaugeTypeEnum = GaugeTypeEnum //量级枚举

    @Watch('questionList', {
      deep: true
    })
    questionListChange() {
      this.mustAnswered = this.questionList.every((item) => item.isMustAnswered)
    }
    /**
     * 量级-程度（非自定义前提下）
     */
    get GaugeQuestionTypeName() {
      return (row: GaugeTypeEnum) => {
        return this.GaugeType.map.get(row).replace(/度/g, '')
      }
    }

    /**
     * 量级-区间
     */
    get GaugeQuestionRow() {
      return (row: GaugeQuestion) => {
        return {
          minRow: row.startLevel,
          maxRow: row.startLevel + row.levelNum - 1,
          array: Array.from({ length: row.levelNum }, (_, index) => row.startLevel + index)
        }
      }
    }
    /**
     * 富文本展示
     */
    get richValue() {
      return (row: string) => {
        return {
          template: '<div>' + this.testFwbImg(row) + '</div>'
        }
      }
    }
    /**
     * 点击必答
     */
    clickMustAnswered(item: Question) {
      if (this.isDisabled) return //被方案引用禁止调整
      item.isMustAnswered = !item.isMustAnswered
      this.mustAnswered = this.questionList.every((item) => item.isMustAnswered)
    }

    /**
     * 全部必答
     */
    clickAll() {
      const shouldAnswer = !this.mustAnswered
      this.questionList.forEach((item) => (item.isMustAnswered = shouldAnswer))
      this.mustAnswered = shouldAnswer
    }

    /**
     * 测试富文本图片
     */
    testFwbImg(str: string) {
      return str.replace(/<img.*?>/g, function (img) {
        const regex = /<img[^>]+src="([^">]+)"/
        const match = img.match(regex)
        return `
            <el-popover
            placement="top-start"
            width="200"
            trigger="hover">
              <img src="${match[1]}" style="width: 200px; height: 200px" />
              <span slot="reference" style="color:#1f86f0">[查看图片]</span>
          </el-popover>
         `.trim()
      })
    }

    /**
     * 添加试题
     */
    addQuestion() {
      if (!this.questionType) {
        this.$message.error('请选择问卷类型')
        return
      }
      this.business = 'add'
      this.questionDrawerRef.init()
      if (this.questionType === 2) {
        this.questionDrawerRef.form.type = 4
      }
      this.questionDrawerRef.questionDialog = true
    }

    /**
     * 编辑试题
     */
    async editQuestion(cur: Question, idx: number) {
      this.currentIdx = idx
      this.business = 'edit'
      this.questionDrawerRef.form = cur
      this.questionDrawerRef.questionDialog = true
    }
    /**
     * 删除试题
     */
    async questionDelete(item: Question, idx: number) {
      this.$confirm('是否确认删除该选项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          if (!this.isCopy) {
            const res = await item.deleteQuestion()
            if (res.status.isSuccess()) {
              this.questionList = DataResolve.doQuestionDelete(this.questionList, idx)
              this.$message.success(`删除成功`)
            } else {
              this.$message.error(`删除失败${res.status.errors[0].message}`)
            }
          } else {
            this.questionList = DataResolve.doQuestionDelete(this.questionList, idx)
            this.$message.success(`删除成功`)
          }
        })
        .catch((e) => {
          console.log(e)
        })
    }
    /**
     * 试题移动
     */
    questionPostition(item: Question, position: string) {
      this.questionList = DataResolve.moveItemToPosition(this.questionList, item, position)
    }
    /**
     * 试题选项
     */
    resolverIndexToCharCode(index: number) {
      return ExamUtil.matchCharCode(index)
    }
    /**
     * 业务处理
     */
    async handleChange(val: Question) {
      let res
      if (this.business === 'add' || !val.id) {
        res = await val.createQuestion()
        if (res.code === 200) {
          this.questionList = [...this.questionList, val] //新增
        }
      } else {
        res = await val.updateQuestion(this.isCopy)
        if (res.code === 200) {
          this.questionList.splice(this.currentIdx, 1, val) //数组某一项做替换
        }
      }
      if (res.code === 200) {
        this.$message.success(`操作成功`)
        if (!val.isContinue) {
          this.questionDrawerRef.questionDialog = false
        } else {
          this.questionDrawerRef.init('continue')
          this.questionDrawerRef.typeChange(this.questionType)
        }
      } else {
        if (res?.errors[0]?.code === 10003) {
          this.$message.error('问卷模板被引用，不可修改。')
        } else {
          this.$message.error(`操作失败`)
        }
      }
    }
    /**
     * 将数值1 变成001 11变成011
     */
    padStartString(num: number) {
      const arr = num + 1
      return arr.toString().padStart(3, '0')
    }
    basicKey = new Date().getTime()
    /**
     * 下方做拖拽用
     */
    mounted() {
      if (this.$route?.query?.isDiabled !== 'true') {
        //被方案引用禁止调整
        const elForm = (this.$refs.dragWebTable as any).$el
        const sortable = new Sortable(elForm, {
          animation: 150,
          onEnd: ({ newIndex, oldIndex }) => {
            const curRow = this.questionList.splice(oldIndex, 1)[0]
            this.questionList.splice(newIndex, 0, curRow)
            const newArray = this.questionList.slice(0)
            this.questionList = []
            this.$nextTick(function () {
              this.questionList = newArray
            })
          }
        })
      }
      this.isCopy = this.$route?.query.isCopy === 'true'
    }
  }
</script>
