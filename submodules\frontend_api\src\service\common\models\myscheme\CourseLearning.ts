/**
 * 课程学习方式
 */
class CourseLearning {
  /**
   * 课程学习方式编号
   */
  learningId = ''
  /**
   * 选课规则id
   */
  ruleConfigId = ''
  /**
   * 课程学习要求总学时
   */
  maxPeriod: number
  /**
   * 总体学习进度，百分比值，如：80%，则值为80（用户学习的实时进度）
   */
  schedule = 0
  /**
   * 考核要求的学习进度
   */
  requireSchedule = 0
  /**
   * 已经学习的学时
   */
  hasStudyPeriod: number
  /**
   * 是否包含课程学习要求总学时
   */
  hasMaxPeriod() {
    return !!this.maxPeriod
  }
}

export default CourseLearning
