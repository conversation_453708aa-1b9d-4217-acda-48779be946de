<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">统计报表</el-breadcrumb-item>
      <el-breadcrumb-item>学习日志</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header">
          <span class="tit-txt">用户信息</span>
        </div>
        <div class="f-p20">
          <el-row class="no-gutter">
            <el-form :inline="true" label-width="100px" class="m-text-form f-mt10">
              <el-col :span="8">
                <el-form-item label="姓名：">张三丰</el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="手机号：">13509357645</el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="身份证号：">352201199102107465</el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所在地区：">福建省福州市</el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="工作单位：">福建华博教育科技股份有限公司</el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </div>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">培训班信息</span>
        </div>
        <div class="f-p20">
          <el-row class="no-gutter">
            <el-form :inline="true" label-width="100px" class="m-text-form f-mt10">
              <el-col :span="8">
                <el-form-item label="学时：">10</el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="班级名称：">班级名称班级名称班级名称班级名称班级名称</el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </div>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">学习日志</span>
        </div>
        <div class="f-p20">
          <!--空数据-->
          <div class="m-no-date f-ptb50">
            <img class="img" src="./assets/images/no-data-normal.png" alt="" />
            <div class="date-bd">
              <p class="f-f15 f-c9">暂无数据</p>
            </div>
          </div>
          <el-row :gutter="16" class="m-query">
            <el-form :inline="true" label-width="auto">
              <el-col :span="6">
                <el-form-item label="课程名称">
                  <el-input v-model="input" clearable placeholder="请输入课程名称" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="操作类型">
                  <el-select v-model="select" clearable placeholder="请选择操作类型">
                    <el-option value="选项1"></el-option>
                    <el-option value="选项2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="记录时间">
                  <el-date-picker
                    v-model="value1"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item>
                  <el-button type="primary">查询</el-button>
                  <el-button>重置</el-button>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
          <el-table stripe :data="tableData" max-height="500" highlight-current-row class="m-table">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="记录时间" min-width="240">
              <template>2020-04-16 15:58</template>
            </el-table-column>
            <el-table-column label="操作类型" min-width="240">
              <template>开始学习</template>
            </el-table-column>
            <el-table-column label="课程名称" min-width="240">
              <template>课程名称课程名称课程名称课程名称课程名称课程名称</template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        activeNames: ['1'],
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        cascader1: [
          {
            value: 'zhinan',
            label: '门户信息管理',
            children: [
              {
                value: 'yizhi',
                label: '资讯信息管理',
                children: [
                  {
                    value: 'yizhi1',
                    label: '修改'
                  },
                  {
                    value: 'fanku2i',
                    label: '查询'
                  },
                  {
                    value: 'xiaolv1',
                    label: '设为草稿'
                  },
                  {
                    value: 'keko2ng',
                    label: '发布'
                  },
                  {
                    value: 'keko22ng',
                    label: '新增'
                  },
                  {
                    value: 'keko2ng3',
                    label: '删除'
                  }
                ]
              },
              {
                value: 'fankui',
                label: '资讯分类管理',
                children: [
                  {
                    value: 'yiz1hi1',
                    label: '修改'
                  },
                  {
                    value: 'fanku21i',
                    label: '查询'
                  },
                  {
                    value: 'xiao1lv1',
                    label: '设为草稿'
                  },
                  {
                    value: 'kek1o2ng',
                    label: '发布'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '工种管理',
            children: [
              {
                value: 'cexiangdaohang',
                label: '创建/编辑'
              },
              {
                value: 'dingbudaohang',
                label: '上移'
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '培训证明',
            children: [
              {
                value: 'cexiangdao2hang',
                label: '预览'
              },
              {
                value: 'dingb2udaohang',
                label: '下载'
              },
              {
                value: 'dingb2udaoh3ang',
                label: '查询'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        tableData2: [{ field101: '1' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleChange(val) {
        console.log(val)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
