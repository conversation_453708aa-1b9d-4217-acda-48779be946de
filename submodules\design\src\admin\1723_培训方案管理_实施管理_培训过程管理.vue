<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">培训方案管理</el-breadcrumb-item>
      <el-breadcrumb-item>实施管理</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="m-tab-center">
      <div class="content">
        <div class="item">训前实施设置</div>
        <div class="item z-cur">培训过程管理</div>
        <div class="item">培训成果管理</div>
      </div>
    </div>
    <div class="f-p15">
      <el-tabs v-model="activeName2" type="card" class="m-tab-card">
        <el-tab-pane label="心得管理" name="first">
          <el-card shadow="never" class="m-card f-mb15">
            <el-row :gutter="16" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="主题">
                    <el-select v-model="select" clearable filterable placeholder="请选择主题">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="4">
                  <el-form-item label="学习心得类型">
                    <el-select v-model="select" clearable filterable placeholder="请选择心得类型">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="4">
                  <el-form-item label="审核状态">
                    <el-select v-model="select" clearable filterable placeholder="请选择审核状态">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="8" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>导出列表数据</el-button>
                    <el-button>批量导出学习心得</el-button>
                    <el-button>重置</el-button>
                    <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                    <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                  </el-form-item>
                </el-col>
              </el-form>
              <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="用户信息" min-width="220" fixed="left">
                  <template
                    >姓名：用户姓名
                    <br />
                    证件号：352203198812290022</template
                  >
                </el-table-column>
                <el-table-column label="培训方案名称" min-width="220">
                  <template>培训方案名称培训方案名称培训方案名称</template>
                </el-table-column>
                <el-table-column label="主题" min-width="220">
                  <template>课程名称课程名称课程名称课程名称</template>
                </el-table-column>

                <el-table-column label="学习心得类型" min-width="100" align="center">
                  <template>班级心得</template>
                </el-table-column>
                <el-table-column label="提交时间" min-width="240" align="center">
                  <template>2023-10-23 00:00:00至2023-11-11 23:59:59</template>
                </el-table-column>
                <el-table-column label="作答形式" min-width="100" align="center">
                  <template>提交附件</template>
                </el-table-column>
                <el-table-column label="审核方式" min-width="100" align="center">
                  <template>提交自动通过</template>
                </el-table-column>
                <el-table-column label="审核结果" min-width="80" align="center">
                  <template>60</template>
                </el-table-column>

                <el-table-column label="操作" width="120" align="center" fixed="right">
                  <template>
                    <el-button type="text" size="mini">详情</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!--分页-->
              <el-pagination
                background
                class="f-mt15 f-tr"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </el-row>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="问卷管理" name="second">
          <el-card shadow="never" class="m-card">
            <!--条件查询-->
            <el-row :gutter="16" class="m-query">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="问卷名称">
                    <el-input placeholder="请输入问卷名称"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="问卷类型">
                    <el-select v-model="select" clearable placeholder="请选择问卷类型"> </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                    <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                    <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="问卷名称" min-width="220">
                <template>问卷名称问卷名称问卷名称问卷名称</template>
              </el-table-column>
              <el-table-column label="问卷类型" min-width="100" align="center">
                <template>量表问卷</template>
              </el-table-column>
              <el-table-column label="提交问卷人数" min-width="100" align="center">
                <template>60</template>
              </el-table-column>
              <el-table-column label="操作" min-width="120" align="center">
                <template>
                  <el-button type="text">查看统计报告</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="期别管理" name="third">
          <el-card shadow="never" class="m-card">
            <!--条件查询-->
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="6">
                  <el-form-item label="期别名称">
                    <el-input v-model="input" clearable placeholder="请输入期别名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="期别编号">
                    <el-input v-model="input" clearable placeholder="请输入期别编号" />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="期别名称" min-width="200" show-overflow-tooltip>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <div class="f-to">AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA</div>
                  </div>
                  <div v-else>
                    <div class="f-to">AAA<el-tag type="danger" size="mini" class="f-ml5">未设置</el-tag></div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="期别编号" min-width="100" show-overflow-tooltip>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <div class="f-to">001</div>
                  </div>
                  <div v-else>
                    <div class="f-to">001001001001001001001001001001001001001</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="可报名人数" min-width="100">
                <template>100</template>
              </el-table-column>
              <el-table-column label="已报名人数" min-width="100">
                <template>80</template>
              </el-table-column>
              <el-table-column label="报名起止时间" min-width="200">
                <template>
                  <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
                </template>
              </el-table-column>
              <el-table-column label="培训起止时间" min-width="200">
                <template>
                  <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text">教务管理</el-button>
                    <el-button type="text">报到二维码</el-button>
                    <el-button type="text">考勤二维码</el-button>
                    <el-button type="text">问卷二维码</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text">教务管理</el-button>
                    <el-button type="text">报到二维码</el-button>
                    <el-button type="text">问卷二维码</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
