import MutationReturnOrder from '@api/service/management/trade/single/order/mutation/MutationReturnOrder'
import MutationExchangeOrder from '@api/service/management/trade/single/order/mutation/MutationExchangeOrder'
import MutationCreateReturnOrder from '@api/service/management/trade/single/order/mutation/MutationCreateReturnOrder'
import { MutationCreateExchangeOrder } from '@api/service/management/trade/single/order/mutation/MutationCreateExchangeOrder'
import MutationOrder from '@api/service/management/trade/single/order/mutation/MutationOrder'
import MutationExportOrder from '@api/service/management/trade/single/order/mutation/MutationExportOrder'

class MutationTradeFactory {
  /*
   *  订单业务对象
   * */
  getMutationOrder() {
    return new MutationOrder()
  }
  /*
   *  退款业务对象
   * */
  getMutationReturnOrder() {
    return new MutationReturnOrder()
  }
  /*
   *  发起退款业务对象
   * */
  getMutationCreateReturnOrder() {
    return new MutationCreateReturnOrder()
  }
  /*
   *  发起换货业务对象
   * */
  getMutationCreateExchangeOrder() {
    return new MutationCreateExchangeOrder()
  }
  /*
   *  换货业务对象
   * */
  getMutationExchangeOrder() {
    return new MutationExchangeOrder()
  }

  /**
   * 导出订单业务对象
   */
  get mutationExportOrder() {
    return new MutationExportOrder()
  }
}

export default MutationTradeFactory
