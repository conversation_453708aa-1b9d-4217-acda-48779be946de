<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div class="f-flex f-align-center">
          <span class="f-mr5 f-fb">网校提供的配送方式</span>
          <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
            <i class="el-icon-question m-tooltip-icon f-c9"></i>
            <div slot="content">请勾选本网校支持的配送方式，对应的邮寄 / 自取方式才会对学员展示。</div>
          </el-tooltip>
          <span class="f-mr5">：</span>
          <el-checkbox-group v-model="form.type">
            <el-checkbox label="快递" name="type"></el-checkbox>
            <el-checkbox label="自取" name="type"></el-checkbox>
          </el-checkbox-group>
        </div>
      </el-card>
      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
        <el-tab-pane label="快递" name="first">
          <el-card shadow="never" class="m-card f-mb15">
            <div class="f-ptb20">
              <el-row type="flex" justify="center">
                <el-col :span="12">
                  <el-card shadow="never" class="m-card is-header">
                    <div class="m-tit is-border-bottom bg-gray">
                      <div class="tit-txt f-flex-sub">备注信息</div>
                      <a href="#" class="f-link f-underline f-cb">修改</a>
                    </div>
                    <p class="f-p20 f-mh100">这里是备注信息这里是备注信息这里是备注信息</p>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="自取点" name="second">
          <el-card shadow="never" class="m-card f-mb15">
            <el-button type="primary" icon="el-icon-plus" class="f-mb20">添加自取点</el-button>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="自取点名称" min-width="140" fixed="left">
                <template>自取点名称</template>
              </el-table-column>
              <el-table-column label="领取地点" min-width="300">
                <template>福建省福州市鼓楼区工业路611号</template>
              </el-table-column>
              <el-table-column label="领取时间" min-width="120">
                <template>工作日</template>
              </el-table-column>
              <el-table-column label="备注" min-width="250">
                <template>备注备注备注备注备注</template>
              </el-table-column>
              <el-table-column label="状态" min-width="100">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="info" class="badge-status">停用</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">可用</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">修改</el-button>
                  <el-button type="text" size="mini">停用</el-button>
                  <el-button type="text" size="mini">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
