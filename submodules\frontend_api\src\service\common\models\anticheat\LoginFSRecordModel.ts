/**
 * 登录防作弊人脸识别记录信息
 */
class LoginFSRecordModel {
  /**
   * 记录ID
   */
  id: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 跟踪维度，0/1/2/3/4，进入场景/退出场景/时间/进度/具体项目
   */
  dimensions: number
  /**
   * 跟踪记录点，当dimensions=0/1时，当前无值
   * 当dimensions=2时，单位：秒
   * 当dimensions=3时，单位：百分比
   * 当dimensions=4时，具体编号
   */
  recordPoint: string
  /**
   * 基准照片路径
   */
  standardAnswer: string
  /**
   * 单次拍照路径
   */
  currentAnswer: string
  /**
   * 达标对比值，匹配相似度
   */
  standardValue: string
  /**
   * 当前对比值，当前相似度
   */
  currentValue: string
  /**
   * 记录点对应的时间点，单位：秒，如：学习对应时长，考试对应当前考试时间
   */
  timeLength: number
  /**
   * 记录点对应的进度百分比，如：学习对应进度，考试对应的考试进度
   */
  schedule: number
  /**
   * 验证结果
   */
  result: boolean
  /**
   * 创建时间
   */
  createTime: Date
}

export default LoginFSRecordModel
