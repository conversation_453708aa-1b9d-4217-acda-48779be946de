<route-meta>
{
"isMenu": true,
"title": "分销商销售统计",
"sort": 12,
"icon": "icon-mingxi"
}
</route-meta>

<script lang="ts">
  import SupplierDistributorSalesStatistics from '@hbfe/jxjy-admin-supplierDistributorSalesStatistics/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { NZGYS, NZGYSJCB } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    queryDistributorSalesStatisticsData: [NZGYS, NZGYSJCB],
    exportDataDistributorSalesStatisticsData: [NZGYS, NZGYSJCB]
  })
  export default class extends SupplierDistributorSalesStatistics {}
</script>
