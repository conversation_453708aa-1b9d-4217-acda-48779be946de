import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-account-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-account-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * <AUTHOR> xucenhao
 */
export class CompensatingStudentRequest {
  /**
   * 文件名称
   */
  fileName?: string
  type: number
  params?: Map<string, string>
}

/**
 * 批量导入学员请求信息
@author: zhengp 2022/2/22 9:08
 */
export class BatchImportStudentRequest {
  /**
   * 文件路径
   */
  filePath?: string
  /**
   * 文件名
   */
  fileName?: string
  /**
   * 密码
   */
  password?: string
  /**
   * 默认密码类型
@see com.fjhb.domain.basicdata.api.user.consts.DefaultPasswordTypeConsts
   */
  defaultPasswordType?: number
  /**
   * 密码生效范围--1:仅新用户, 2:全部用户（含已注册）
@see com.fjhb.domain.basicdata.api.account.consts.PasswordEffectiveRange
   */
  passwordEffectiveRange?: number
  /**
   * 已注册学员更新基础信息:是否只做创建 只做创建为 true 创建+更新为false
   */
  onlyCreate?: boolean
}

/**
 * 获取导入学员信息执行结果返回信息
@author: zhengp 2022/5/9 14:52
 */
export class ExportStudentImportResultResponse {
  /**
   * 导入学员信息执行结果文件地址
   */
  fileUrl: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出全部学员导入数据
   * @param mainTaskId 任务id
   * @return 学员导入数据
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportAllStudentResult(
    mainTaskId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportAllStudentResult,
    operation?: string
  ): Promise<Response<ExportStudentImportResultResponse>> {
    return commonRequestApi<ExportStudentImportResultResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出失败学员导入数据
   * @param mainTaskId 任务id
   * @return 失败学员导入数据
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportErrorStudentResult(
    mainTaskId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportErrorStudentResult,
    operation?: string
  ): Promise<Response<ExportStudentImportResultResponse>> {
    return commonRequestApi<ExportStudentImportResultResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量导入学员
   * @param request 批量导入学员请求信息，新增密码生效范围和是否更新已注册学员信息字段
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchImportStudent(
    request: BatchImportStudentRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchImportStudent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async compensatingStudentAccount(
    request: CompensatingStudentRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.compensatingStudentAccount,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
