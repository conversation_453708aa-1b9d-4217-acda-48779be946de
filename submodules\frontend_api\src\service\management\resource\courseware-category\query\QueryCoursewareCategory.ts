import CoursewareCategoryDetail from '@api/service/management/resource/courseware-category/query/vo/CoursewareCategoryDetail'
import { UiPage } from '@hbfe/common'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import QueryCoursewareCategoryListParam from '@api/service/management/resource/courseware-category/query/vo/QueryCoursewareCategoryListParam'
import AbstractQueryCategory from '@api/service/management/resource/AbstractQueryCategory'
import CoursewareCategory from '@api/service/management/resource/courseware-category/query/vo/CoursewareCategory'
import CoursewareCategoryListDetail from '@api/service/management/resource/courseware-category/query/vo/CoursewareCategoryListDetail'
import { rootCoursewareCategory } from '@api/service/common/config/CommonConfig'

class QueryCoursewareCategory extends AbstractQueryCategory<CoursewareCategory, CoursewareCategoryDetail> {
  /**
   * 分页查询课件分类
   * @param page
   * @param queryParam
   */
  async queryCoursewareCategoryPage(page: UiPage, queryParam?: QueryCoursewareCategoryListParam) {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursewareCategoryInServicer({
      page,
      request: queryParam
    })
    return result.data.currentPageData
  }

  /**
   * 根据可见分类父分类id查询课件分类列表
   * @param parentId
   */
  async queryCoursewareCategoryList(parentId?: string) {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 200
    const request = new QueryCoursewareCategoryListParam()
    request.parentId = parentId
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursewareCategoryInServicer({
      page,
      request
    })
    if (result.data.totalPageSize >= 2) {
      const promiseList: number[] = []
      for (let i = 2; i <= result.data.totalPageSize; i++) {
        promiseList.push(i)
      }
      await Promise.all(
        promiseList.map(async res => {
          page.pageNo = res
          const response = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursewareCategoryInServicer(
            {
              page,
              request
            }
          )
          result.data.currentPageData.push(...response.data.currentPageData)
        })
      )
    }
    return result.data.currentPageData.map(CoursewareCategoryDetail.from)
  }

  /**
   * 根据父分类 id、名称查询分类信息，用来判断是否有重复名称的课件分类
   * @param parentId
   * @param name
   */
  async queryByNameUnderParent(parentId: string, name: string): Promise<CoursewareCategoryDetail | null> {
    return new CoursewareCategoryDetail()
  }

  /**
   *
   * @param name
   */
  async queryListByName(name: string): Promise<Array<CoursewareCategoryListDetail>> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    let list: Array<CoursewareCategoryListDetail> = new Array<CoursewareCategoryListDetail>()
    const request = new QueryCoursewareCategoryListParam()
    request.name = name
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursewareCategoryInServicer({
      page,
      request
    })
    page.pageSize = result.data.totalSize
    const lastQueryResult = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursewareCategoryInServicer(
      { page, request }
    )
    list = lastQueryResult.data.currentPageData.map(CoursewareCategoryListDetail.from)
    return list
  }

  async queryById(id: string): Promise<CoursewareCategory> {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.getCoursewareCategoryInServicer(id)
    const detail = CoursewareCategory.from(result.data)
    const parentResult = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.getCoursewareCategoryInServicer(
      detail.parentId
    )
    detail.parentName = CoursewareCategoryDetail.from(parentResult.data).name
    return detail
  }

  /**
   * 查询课件分类列表
   */
  async queryList(): Promise<Array<CoursewareCategoryDetail>> {
    return this.queryCoursewareCategoryList(rootCoursewareCategory.id)
  }

  /**
   * 查询子列表
   * @param id
   */
  async queryChildrenById(id: string): Promise<Array<CoursewareCategoryDetail>> {
    return this.queryCoursewareCategoryList(id)
  }

  /**
   * 查询所有列表
   */
  async queryAllChildren(): Promise<Array<CoursewareCategoryDetail>> {
    return this.queryCoursewareCategoryList()
  }

  /**
   * 根据提供的层级结构的 id，查询分类
   * @param idList
   */
  async queryCoursewareCategoryListByLevelConstruct(idList: Array<string>) {
    const newIdList = [...idList]
    const reverserSearch = async (node: { children: Array<CoursewareCategoryDetail> }) => {
      const id = newIdList[0]
      const result = await this.queryCoursewareCategoryList(id)
      if (!result.length) {
        delete node.children
      } else {
        node.children.push(...result)
      }
      newIdList.splice(0, 1)
      const nextId = newIdList[0]
      const item = result.find((detail: CoursewareCategoryDetail) => {
        return detail.id === nextId
      })
      if (newIdList.length && item) {
        item.children = new Array<CoursewareCategoryDetail>()
        await reverserSearch(item)
      }
    }
    const result = {
      children: new Array<CoursewareCategoryDetail>()
    }
    await reverserSearch(result)
    return result.children
  }

  /**
   * 提供最后一个分类的 id， 反向查询父级分类
   * @param lastLevelCategory
   */
  async queryReverserById(lastLevelCategory: string): Promise<Array<CoursewareCategoryListDetail>> {
    const list = new Array<CoursewareCategoryListDetail>()
    const traversSearch = async (id: string) => {
      const {
        data: detail
      } = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.getCoursewareCategoryInServicer(id)
      if (detail?.parentId && detail?.parentId !== rootCoursewareCategory.id) {
        await traversSearch(detail.parentId)
      }
      list.push(CoursewareCategoryListDetail.from(detail))
    }
    await traversSearch(lastLevelCategory)
    return list
  }
}

export default QueryCoursewareCategory
