import {
  default as MsCourseLearning,
  default as MsCourseLearningQueryFront,
  StudentCourseLearningRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import QueryUserCourseAppraiseList from '@api/service/customer/appraise/query/QueryUserCourseAppraiseSummary'
import Utils from '@api/service/customer/course/query/utils'
import MyLearningCourse from '@api/service/customer/course/query/vo/MyLearningCourse'
import MyLearningEvaluation from '@api/service/customer/course/query/vo/MyLearningEvaluation'
import Classification from '@api/service/customer/train-class/query/vo/Classification'
class QueryLastCourse {
  /**
   * 最后学习的课程信息
   */

  lastCourse = new MyLearningEvaluation()

  /**
   * 查询学员最后一次学习的课程
   */
  async queryLastCourseInfo(studentNo: string, outlineTree?: Array<Classification>) {
    const request = new StudentCourseLearningRequest()
    request.studentNo = studentNo
    const response = await MsCourseLearning.getLastStudentCourseLearningInMyself(request)
    if (!response.status.isSuccess() || !response.data) return new MyLearningEvaluation()
    this.lastCourse = MyLearningCourse.from(response.data)
    // 获取课程名称
    const res = new QueryUserCourseAppraiseList(this.lastCourse.detail.id)
    await res.queryInfo()
    this.lastCourse.colligationScore = res.totalAppraise || 5
    console.log(outlineTree, this.lastCourse.courseOfCourseTrainingOutline?.outlineId, 'adasfa')

    this.lastCourse.detail.categoryPath = this.getCategoryPath(
      outlineTree,
      this.lastCourse.courseOfCourseTrainingOutline?.outlineId
    )
    return this.lastCourse
  }

  private getCategoryPath(tree: Array<Classification>, leafOutlineId: string): string {
    const categoryList = Utils.treeFindPath(
      tree,
      (node: Classification) => {
        return node.id === leafOutlineId
      },
      'name',
      'childOutlines'
    )
    return categoryList.length ? categoryList.join(' > ') : ''
  }
}
export default QueryLastCourse
