import BasicDataGateway, {
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'

export default new (class QueryTrainingObject {
  /**
   * 培训对象列表
   */
  trainingObjectList: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()

  /**
   * 培训对象Map key: 属性id
   */
  private trainingObjectMap: Map<string, TrainingPropertyResponse> = new Map<string, TrainingPropertyResponse>()

  /**
   * 查询对应行业下培训对象
   * @param industryId 行业id
   * @param industryPropertyId 行业属性id
   */
  async queryTrainingObjectByIndustry(
    industryId: string,
    industryPropertyId: string
  ): Promise<Array<TrainingPropertyResponse>> {
    this.trainingObjectList = new Array<TrainingPropertyResponse>()
    this.trainingObjectMap = new Map<string, TrainingPropertyResponse>()

    const res = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
      industryPropertyId,
      industryId,
      categoryCode: IndustryPropertyCodeEnum.PERSON_TRAINING_OBJECT
    })
    if (res.data?.length) {
      this.trainingObjectList = res.data
      res.data.map(item => {
        this.trainingObjectMap.set(item.propertyId, item)
      })
    }

    return this.trainingObjectList
  }

  /**
   * 获取详情表
   * @param propertyId 属性id
   */
  getTrainingObjectDetail(propertyId: string): TrainingPropertyResponse {
    return this.trainingObjectMap.get(propertyId)
  }
})()
