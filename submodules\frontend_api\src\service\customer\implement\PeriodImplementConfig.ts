import AttendanceConfigDto from '@api/service/common/implement/models/AttendanceConfigDto'
import MsSchemeLearningQueryFront, {
  IssueStudyConfigResponse,
  TeachResourceResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import LearningMaterialItem from '@api/service/customer/implement/models/LearningMaterialItem'

export default class PeriodImplementConfig {
  /**
   * 期别id
   */
  periodId: string = undefined

  /**
   * 期别名称
   */
  periodName: string = undefined

  /**
   * 期别编号
   */
  periodNo: string = undefined

  /**
   * 签到配置
   */
  signInConfig: AttendanceConfigDto = new AttendanceConfigDto()

  /**
   * 签退配置
   */
  signOutConfig: AttendanceConfigDto = new AttendanceConfigDto()

  /**
   * 学习资料列表
   */
  learningMaterialList: Array<LearningMaterialItem> = new Array<LearningMaterialItem>()

  /**
   * 报道打卡范围
   */
  reportSignRange: number = undefined

  /**
   * 教学计划id
   */
  planId: string = undefined

  /**
   * 是否设置报道
   */
  isSetReport: boolean = undefined

  /**
   * 是否设置报道
   */
  isSetAttendance: boolean = undefined

  /**
   * 谁设置学习资料
   */
  isSetLearningMaterial: boolean = undefined

  /**
   * @param periodId 期别id
   */
  constructor(periodId: string) {
    this.periodId = periodId
  }

  from(dto: IssueStudyConfigResponse) {
    const { schemeIssueResponse, reportRuleResponse, attendanceConfigResponse, teachResourceConfigResponse } = dto
    // 期别基础信息
    if (schemeIssueResponse) {
      this.periodId = schemeIssueResponse.issueId
      this.periodName = schemeIssueResponse.issueName
      this.periodNo = schemeIssueResponse.issueNum
    }
    // 签到配置
    if (attendanceConfigResponse) {
      this.isSetAttendance = true
      const attendancesSignInConfig = attendanceConfigResponse.attendancesSignInConfig
      const attendancesSignOutConfig = attendanceConfigResponse.attendancesSignOutConfig
      if (attendancesSignInConfig) {
        this.signInConfig.isOpen = attendancesSignInConfig.enable
        this.signInConfig.checkInFrequency = attendancesSignInConfig.signInFrequencyType
        this.signInConfig.checkInLocationRadius = attendancesSignInConfig.signInAddressRadius
        this.signInConfig.preCheckInTime = attendancesSignInConfig.preSignTime
          ? attendancesSignInConfig.preSignTime / 60
          : attendancesSignInConfig.preSignTime
        this.signInConfig.afterCheckInTime = attendancesSignInConfig.postSignTime
          ? attendancesSignInConfig.postSignTime / 60
          : attendancesSignInConfig.postSignTime
      }
      if (attendancesSignOutConfig) {
        this.signOutConfig.isOpen = attendancesSignOutConfig.enable
        this.signOutConfig.checkInFrequency = Number(attendancesSignOutConfig.signInFrequencyType)
        this.signOutConfig.checkInLocationRadius = attendancesSignOutConfig.signInAddressRadius
        this.signOutConfig.preCheckInTime = attendancesSignOutConfig.preSignTime
          ? attendancesSignOutConfig.preSignTime / 60
          : attendancesSignOutConfig.preSignTime
        this.signOutConfig.afterCheckInTime = attendancesSignOutConfig.postSignTime
          ? attendancesSignOutConfig.postSignTime / 60
          : attendancesSignOutConfig.postSignTime
      }
    } else {
      this.isSetAttendance = false
    }

    // 学习资料配置
    if (teachResourceConfigResponse) {
      this.isSetLearningMaterial = true
      if (teachResourceConfigResponse.teachResourceList?.length) {
        this.learningMaterialList = teachResourceConfigResponse.teachResourceList.map((item: TeachResourceResponse) => {
          return LearningMaterialItem.from(item)
        })
      }
    } else {
      this.learningMaterialList = new Array<LearningMaterialItem>()
      this.isSetLearningMaterial = false
    }

    // 报道配置
    // todo 这边报道字段没有用全，报到的时间这边的模型没有，需要去方案期别身上取
    if (reportRuleResponse) {
      this.isSetReport = true
      this.reportSignRange = reportRuleResponse.signRadiusRange
      this.planId = reportRuleResponse.planId
    } else {
      this.isSetReport = false
    }
  }

  /**
   * 获取期别实施配置
   */
  async getPeriodImplementConfig() {
    const res = await MsSchemeLearningQueryFront.getMySchemeIssueConfigInMySelf(this.periodId)

    if (res?.data) {
      this.from(res.data)
    }
  }
}
