<template>
  <OnlineClassTable v-bind="$attrs" v-on="$listeners">
    <template #certificate-number>
      <el-table-column label="证书编号" min-width="155" align="center">
        <template slot-scope="scope">
          <div>
            {{ scope.row.certificateNumber || '-' }}
          </div>
        </template>
      </el-table-column>
    </template>
  </OnlineClassTable>
</template>

<script lang="ts">
  import OnlineClassTable from '@hbfe/jxjy-admin-learningStatistic/src/__components__/online-class-table.vue'
  import { Component, Ref, Vue } from 'vue-property-decorator'

  @Component({
    components: {
      OnlineClassTable
    }
  })
  export default class extends Vue {}
</script>
<style></style>
