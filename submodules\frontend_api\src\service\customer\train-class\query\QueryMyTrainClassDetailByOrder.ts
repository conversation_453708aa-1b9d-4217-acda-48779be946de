import { ResponseStatus } from '@hbfe/common'
import MyTrainClassDetailClassVo from '@api/service/customer/train-class/query/vo/MyTrainClassDetailClassVo'
import QueryMyTrainClassDetail from '@api/service/customer/train-class/query/QueryMyTrainClassDetail'
import MsMySchemeQueryFrontGatewayCourseLearningForeStage, {
  StudentSchemeLearningResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { getSchemeLearningBySubOrderInMyself } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage/graphql-importer'
import {
  default as MsSchemeQueryFrontGatewayCourseLearningBackstage,
  SchemeConfigResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import studentCourseLearningQuery, {
  StudentTrainingResultSimulateRequest
} from '@api/platform-gateway/student-course-learning-query-back-gateway'

/**
 * 用户域根据子订单号获取我的培训班详情
 */
class QueryMyTrainClassDetailByOrder extends QueryMyTrainClassDetail {
  // region properties
  /**
   *培训班详情，类型为MyTrainClassDetailClassVo
   */
  trainClassDetail = new MyTrainClassDetailClassVo()
  /**
   *子订单号，类型为string
   */
  subOrderNo = ''
  // endregion
  // region methods
  async requestClassDetail(): Promise<ResponseStatus> {
    const res = await MsMySchemeQueryFrontGatewayCourseLearningForeStage.getSchemeLearningBySubOrderInMyself(
      this.subOrderNo
    )
    if (res.status.isSuccess()) {
      const commodityDetail: StudentSchemeLearningResponse = res.data
      this.studentSchemeLearningResponse = commodityDetail
      await this.convertToTrainClassDetailClassVo(commodityDetail)
    }
    return res.status
  }
  async batchRequestClassDetail(subOrderNoList: string[]) {
    const onlineRew = new RewriteGraph<StudentSchemeLearningResponse, string>(
      MsMySchemeQueryFrontGatewayCourseLearningForeStage._commonQuery,
      getSchemeLearningBySubOrderInMyself
    )
    await onlineRew.request(subOrderNoList)
    return onlineRew.itemMap
  }

  async pageSchemeConfigInServicer(schemeIds: string[]) {
    const schemeRes = await MsSchemeQueryFrontGatewayCourseLearningBackstage.pageSchemeConfigInServicer({
      page: {
        pageNo: 1,
        pageSize: schemeIds.length
      },
      schemeIds,
      needField: ['commoditySale', 'trainingBeginDate']
    })
    const schemeMap = new Map<string, SchemeConfigResponse>()
    schemeRes.data.currentPageData.forEach(item => {
      schemeMap.set(item.schemeId, item)
    })
    return schemeMap
  }

  async getStudentTrainingResultSimulateResponseInServicer(simulateRequest: StudentTrainingResultSimulateRequest) {
    const simulateResult = await studentCourseLearningQuery.getStudentTrainingResultSimulateResponseInServicer(
      simulateRequest
    )
    return simulateResult
  }

  // endregion
}
export default QueryMyTrainClassDetailByOrder
