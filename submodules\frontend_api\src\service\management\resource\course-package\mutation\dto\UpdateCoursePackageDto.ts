import { ResponseStatus } from '@hbfe/common'
import MsCourseResourceV1, {
  CourseInPackageRequest,
  CoursePackageUpdateRequest
} from '@api/ms-gateway/ms-course-resource-v1'
import UpdateCoursePackageVo from '@api/service/management/resource/course-package/mutation/vo/UpdateCoursePackageVo'
import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'

class UpdateCoursePackageDto extends CoursePackageUpdateRequest {
  async save(): Promise<ResponseStatus> {
    const { status } = await MsCourseResourceV1.updateCoursePackage(this)
    return new ResponseStatus(status.code, status.getMessage())
  }

  static from(vo: UpdateCoursePackageVo): UpdateCoursePackageDto {
    const dto = new UpdateCoursePackageDto()
    dto.id = vo.id
    dto.name = vo.name
    dto.displayName = vo.showName
    dto.courseInPackageList = new Array<CourseInPackageRequest>()
    vo.addedList.forEach((course: CourseInCoursePackage, index: number) => {
      const courseInRequest = new CourseInPackageRequest()
      courseInRequest.courseId = course.id
      courseInRequest.period = Number(course.period)
      courseInRequest.sort = index + 1
      dto.courseInPackageList.push(courseInRequest)
    })
    return dto
  }
}

export default UpdateCoursePackageDto
