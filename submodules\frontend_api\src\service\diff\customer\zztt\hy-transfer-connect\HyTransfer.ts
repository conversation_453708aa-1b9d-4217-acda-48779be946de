import fjzjGateway, {
  CreateSecretSignUpInfoRequest,
  CreateStudentAndPlaceOrderRequest,
  EncryptedContentResponse,
  SyncStudentSignUpForAppRequest,
  SyncStudentSignUpForAppResponse,
  SyncStudentSignUpRegisteredRequest,
  ValidHymStudentInfoRequest
} from '@api/diff-gateway/platform-jxjypxtypt-zztt-school'
import UserModule from '@api/service/customer/user/UserModule'
import { ResponseStatus } from '@hbfe/common'
import { CreateStudentAndPlaceEnum } from '@api/service/diff/customer/zztt/hy-transfer-connect/enums/CreateStudentAndPlaceEnum'
import { CheckCurrentUserRegisterResultEnum } from '@api/service/diff/customer/zztt/hy-transfer-connect/enums/CheckCurrentUserRegisterResultEnum'
import MsTradeQuery from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import QueryPlatformForestage from '@api/service/diff/common/zztt/dictionary/QueryPlatformForestage'
import { PlatformEnum } from '@api/service/diff/customer/zztt/hy-transfer-connect/enums/PlatformEnum'
import CheckFxEnterResult from '@api/service/diff/customer/zztt/hy-transfer-connect/models/CheckFxEnterResult'
import MsSchemeLearningQueryForestage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import CheckFxParam from '@api/service/diff/customer/zztt/hy-transfer-connect/models/CheckFxParam'
import MsSchemeQueryFrontGatewayCourseLearningForestage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import CreateStudentAndPlaceOrderResponse from '@api/service/diff/customer/zztt/hy-transfer-connect/models/CreateStudentAndPlaceOrderResponse'
import WebfunnyReport from '@api/service/common/webfunny/WefunnyReport'
import { HYWJumpFJZJModel } from '@api/service/diff/customer/webfunny/models/HywModel'
import { HywReportEnum } from '../../webfunny/enums/HywReportEnum'
import Env from '@api/service/common/utils/Env'
export default class HyTransfer {
  /**
   * 轮询定时器
   */
  private _timeOut: any = null

  /**
   * 轮询间隔
   */
  private _interval = 1500

  /**
   * 创建轮询标识
   */
  private _createPolling = false

  /**
   * 轮询次数
   */
  private _pollingCount = 8

  /**
   * 密文
   */
  private _ciphertext = ''
  /**
   * 检验当前登录账号是否在华医网注册
   */
  async checkCurrentUserRegister(): Promise<CheckCurrentUserRegisterResultEnum> {
    const request = new ValidHymStudentInfoRequest()
    request.userName = UserModule.queryUserFactory.getQueryUserInfo()?.userInfo?.userInfo?.userName
    request.certificateNumber = UserModule.queryUserFactory.getQueryUserInfo()?.userInfo?.userInfo?.idCard
    const res = await fjzjGateway.validStudentInfo(request)
    if (res.status && res.status.isSuccess() && res?.data?.code == '200') {
      return res.data.type as CheckCurrentUserRegisterResultEnum
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   * 判断分销进入班级重定向路径
   * @param param
   */
  async checkFxEnterClassRedirect(param: CheckFxParam): Promise<CheckFxEnterResult> {
    const learningRes = await MsSchemeLearningQueryForestage.getSchemeLearningInMyself(param.qualificationId)

    const orderId = learningRes?.data?.learningRegister?.orderNo

    const orderRes = await MsTradeQuery.getOrderInMyself(orderId)
    const platformId = orderRes?.data?.subOrderItems[0]?.deliveryCommoditySku?.tppTypeId

    await QueryPlatformForestage.queryList()
    const check = QueryPlatformForestage.isHuaYiWeb(platformId)
    const result = new CheckFxEnterResult()
    if (check) {
      result.redirect = true
      if (param.platform == PlatformEnum.h5) {
        result.url = `/pages/transfer/study-transfer?orderNo=${orderId}&mode=study_in&accessToken=${param.aesAt}&refreshToken=${param.aesRt}`
      } else {
        const res = await this.encryptedStudentAndClassContent(orderId)

        let host = 'https://cme.91huayi.com'
        if (location?.href) {
          if (Env.isProxyProdEnv) {
            host = 'http://cmetest2.91huayi.net'
          }
        }

        result.url = `${host}/secure/SingleSignOnThird.aspx?unitCode=${res.unitCode}&params=${res.params}&timer=${res.timer}&sign=${res.sign}`
      }
    }
    return result
  }

  /**
   * 生成学员报名信息加密体
   * @param orderNo 订单号
   */
  async encryptedStudentAndClassContent(orderNo: string): Promise<EncryptedContentResponse> {
    const request = new CreateSecretSignUpInfoRequest()
    request.orderNo = orderNo
    const res = await fjzjGateway.encryptedContent(request)

    if (res?.status && res.status.isSuccess() && res.data?.code == '200') {
      return res.data
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   * 主动推送学员报名信息
   * @param orderNo 订单号
   */
  async pushStudentClass(orderNo: string): Promise<SyncStudentSignUpForAppResponse> {
    const request = new SyncStudentSignUpForAppRequest()
    request.orderNo = orderNo
    const res = await fjzjGateway.syncStudentSignUpForApp(request)

    if (res?.status && res.status.isSuccess() && res.data?.code == '200') {
      return res.data
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   * 主动推送学员报名信息
   * @param orderNo 订单号
   */
  async pushStudentClassByParam(param: SyncStudentSignUpRegisteredRequest): Promise<SyncStudentSignUpForAppResponse> {
    const res = await fjzjGateway.syncStudentSignUpUnregistered(param)

    if (res?.status && res.status.isSuccess() && res.data?.code == '200') {
      return res.data
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   *  学员注册并报班 --- 用于华医网跳转回来获取单点登录信息
   * @param param
   */
  async studentRegisterClass(param: CreateStudentAndPlaceOrderRequest): Promise<CreateStudentAndPlaceOrderResponse> {
    this._createPolling = true
    this._pollingCount = 8
    try {
      const res = await new Promise<CreateStudentAndPlaceOrderResponse>((resolve, reject) => {
        this.pollingGetStudentRegisterClassResult(param, resolve, reject)
      })
      return res
    } catch (e) {
      return Promise.reject(e)
    }
  }

  /**
   * 轮询学员注册并报班
   * @param param
   * @param resolve
   * @param reject
   */
  private async pollingGetStudentRegisterClassResult(
    param: CreateStudentAndPlaceOrderRequest,
    resolve: (data: CreateStudentAndPlaceOrderResponse) => void,
    reject: (reason?: ResponseStatus) => void
  ) {
    const res = await fjzjGateway.createStudentAndPlaceOrder(param)
    if (!res.status || !res.status.isSuccess()) {
      this._createPolling = false
      try {
        this._ciphertext = param.ciphertext
        this.upErrorEvent(res, param.ciphertext)
      } catch (e) {
        console.log('zztt埋点上报错误', e)
      }
      return reject(new ResponseStatus(CreateStudentAndPlaceEnum.error, '系统异常'))
    }
    if (Number(res.data.code) === CreateStudentAndPlaceEnum.success) {
      this._createPolling = false
      // res.data.qualificationId = ''
      // res.data.subOrderNoList = ['S24073110502574898127041400']
      const data = Object.assign(new CreateStudentAndPlaceOrderResponse(), res.data)
      if (data.key === 'PLACEORDER') {
        return resolve(data)
      } else if (data.key === 'PAY') {
        return resolve(data)
      } else if (data.qualificationId) {
        return resolve(data)
      } else if (data.subOrderNo) {
        this._createPolling = true
        return resolve(data)
      } else {
        return reject(
          new ResponseStatus(
            Number(res.data?.code || CreateStudentAndPlaceEnum.error),
            res.status?.getMessage() || '订单开通中，请刷新页面重试'
          )
        )
      }
    } else if (Number(res.data.code) === CreateStudentAndPlaceEnum.delay) {
      this._timeOut = setTimeout(() => {
        clearTimeout(this._timeOut)
        if (this._createPolling) {
          this.pollingGetStudentRegisterClassResult(param, resolve, reject)
        } else {
          reject(null)
        }
      }, this._interval)
    } else {
      this._createPolling = false
      try {
        this.upErrorEvent(res, param.ciphertext)
      } catch (e) {
        console.log('zztt埋点上报错误', e)
      }
      return reject(new ResponseStatus(Number(res.data.code), res.data.message))
    }
  }
  /**
   * 轮询学员班级-存在子订单号但没有参选资格id
   * @param data
   * @param resolve
   * @param reject
   */
  async pollingGetStudentClassResult(
    data: CreateStudentAndPlaceOrderResponse,
    resolve: (data: CreateStudentAndPlaceOrderResponse) => void,
    reject: (reason?: ResponseStatus) => void
  ) {
    const res = await MsSchemeQueryFrontGatewayCourseLearningForestage.getSchemeLearningBySubOrderInMyself(
      data.subOrderNo
    )
    if (!res.status || !res.status.isSuccess()) {
      this._createPolling = false
      try {
        this.upErrorEvent(res, this._ciphertext)
      } catch (e) {
        console.log('zztt埋点上报错误', e)
      }
      return reject(new ResponseStatus(CreateStudentAndPlaceEnum.error, '系统异常'))
    }
    // if (this._pollingCount > 2) res.data = null
    if (res.status?.isSuccess() && res.data?.qualificationId) {
      this._createPolling = false
      data.qualificationId = res.data.qualificationId
      return resolve(data)
    } else if (!res?.data?.qualificationId && this._pollingCount) {
      this._pollingCount--
      this._timeOut = setTimeout(() => {
        clearTimeout(this._timeOut)
        if (this._createPolling) {
          this.pollingGetStudentClassResult(data, resolve, reject)
        } else {
          reject(null)
        }
      }, this._interval)
    } else {
      this._createPolling = false
      try {
        this.upErrorEvent(res, this._ciphertext)
      } catch (e) {
        console.log('zztt埋点上报错误', e)
      }
      return reject(
        new ResponseStatus(
          Number(CreateStudentAndPlaceEnum.pollingQualificationIdTimesAbnormal),
          '页面加载失败，请刷新重试！'
        )
      )
    }
  }
  /**
   * 停止轮询
   */
  stopPolling() {
    this._createPolling = false
    if (this._timeOut) {
      clearTimeout(this._timeOut)
    }
  }
  /**
   * 华医网跳转福建专技埋点
   * @param res 响应体
   * @param ciphertext 加密串
   */
  upErrorEvent(res: any, ciphertext: string) {
    try {
      const upData = new HYWJumpFJZJModel()
      upData.bizCode = JSON.stringify(res?.status?.httpCode) || '200'
      upData.ciphertext = ciphertext || 'null'
      upData.httpCode = JSON.stringify(res?.status?.code) || '200'
      upData.response = JSON.stringify(res?.status)
      upData.scene = HywReportEnum.zztt_transfer_connect
      WebfunnyReport.upCommonEvent('差异化-华医网-跳转失败', upData)
    } catch (e) {
      console.log('zztt埋点上报错误', e)
    }
  }
}
