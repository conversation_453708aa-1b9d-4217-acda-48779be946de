import DataExportBackstage from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import BasicDataQueryBackstage, {
  TrainingChannelPageResponse,
  TrainingChannelRequest
} from '@api/platform-gateway/platform-training-channel-back-gateway'
import Training, {
  BatchUpdateSchemeRequest,
  GenernalResponse,
  UpdateTrainingChannelSortRequest
} from '@api/platform-gateway/platform-training-channel-v1'
import { updateTrainingChannelSort } from '@api/platform-gateway/platform-training-channel-v1/graphql-importer'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { Page, ResponseStatus } from '@hbfe/common'
import ThematicManagementItem from './ThematicManagementItem'
import ThematicManagementQueryParam from '@api/service/management/thematic-management/model/ThematicManagementQueryParam'

/**
 * 专题管理
 */
export default class ThematicManagementList {
  /**
   * 查询参数
   */
  queryParam = new ThematicManagementQueryParam()
  /**
   * 列表
   */
  list: ThematicManagementItem[] = []
  /**
   * 查询列表
   */
  async queryList(page: Page) {
    this.list = []

    const res = await BasicDataQueryBackstage.pageTrainingChannelInfo({
      page,
      request: ThematicManagementQueryParam.to(this.queryParam)
    })

    if (res?.data?.currentPageData?.length) {
      res.data.currentPageData.forEach((item) => this.list.push(ThematicManagementItem.fromList(item)))
    }

    page.totalSize = res?.data?.totalSize || 0
    page.totalPageSize = res?.data?.totalPageSize || 0
  }
  /**
   * 查询列表 - 专题管理员
   */
  async queryListInTrainingChannel(page: Page) {
    this.list = []

    const res = await BasicDataQueryBackstage.pageTrainingChannelInfoInTrainingChannelAdmin({
      page,
      request: ThematicManagementQueryParam.to(this.queryParam)
    })

    if (res?.data?.currentPageData?.length) {
      res.data.currentPageData.forEach((item) => this.list.push(ThematicManagementItem.fromList(item)))
    }

    page.totalSize = res?.data?.totalSize || 0
    page.totalPageSize = res?.data?.totalPageSize || 0
  }

  /**
   * 更新专题排序
   * @param id 专题ID
   */
  async updateSort(update: { id: string; sort: number }[]) {
    const re = new RewriteGraph<GenernalResponse, UpdateTrainingChannelSortRequest>(
      Training._commonQuery,
      updateTrainingChannelSort
    )
    const request = update.map((item) => {
      const temp = new UpdateTrainingChannelSortRequest()
      temp.id = item.id
      temp.sort = item.sort
      return temp
    })
    await re.request(request, 'id')
    return re.itemMap
  }
  /**
   * 导出列表
   */
  async exportList() {
    return DataExportBackstage.exportTrainingChannel(ThematicManagementQueryParam.toExport(this.queryParam))
  }

  /**
   * 导出列表 - 专题管理员
   */
  async exportListInTrainingChannel() {
    return DataExportBackstage.exportTrainingChannelInTrainingChannelAdmin(
      ThematicManagementQueryParam.toExport(this.queryParam)
    )
  }

  /**
   * 移除专题下方案接口
   */
  async removeSchemeOfTopic(topicId: string, schemeId: string) {
    const res = await Training.removeSchemeOfTopic({ ptcId: topicId, schemeId })
    if (res.status.isSuccess()) {
      return new ResponseStatus(Number(res.data?.code), res.data.message)
    }
    return res.status
  }

  /**
   * 根据ids查询专题信息
   */
  async getInfoMapByIds(ids: string[]) {
    const infoMap = new Map<string, ThematicManagementItem>()
    if (!ids?.length) {
      return infoMap
    }
    const infoList = new Array<TrainingChannelPageResponse>()
    const page = new Page(1, ids.length < 200 ? ids.length : 200)
    const res = await BasicDataQueryBackstage.pageTrainingChannelInfo({
      page,
      request: { ids }
    })
    infoList.push(...(res.data?.currentPageData || []))
    if (res.data.totalPageSize > 1 && ids.length >= 200) {
      await Promise.all(
        Array.from({ length: res.data.totalPageSize - 1 }, (v, k) => k + 2).map(async (ite) => {
          return await BasicDataQueryBackstage.pageTrainingChannelInfo({
            page: { pageNo: ite, pageSize: 200 },
            request: { ids }
          })
        })
      ).then((re) => {
        infoList.push(...(re.map((item) => item.data?.currentPageData || []).flat() || []))
      })
    }
    infoList.map((item) => {
      infoMap.set(item.id, ThematicManagementItem.fromList(item))
    })
    return infoMap
  }

  /**
   * 根据ids查询专题信息 - 专题管理员
   */
  async getInfoMapByIdsInTrainingChannel(ids: string[]) {
    const infoMap = new Map<string, ThematicManagementItem>()
    if (!ids?.length) {
      return infoMap
    }
    const infoList = new Array<TrainingChannelPageResponse>()
    const page = new Page(1, ids.length < 200 ? ids.length : 200)
    const res = await BasicDataQueryBackstage.pageTrainingChannelInfoInTrainingChannelAdmin({
      page,
      request: { ids }
    })
    infoList.push(...(res.data?.currentPageData || []))
    if (res.data.totalPageSize > 1 && ids.length >= 200) {
      await Promise.all(
        Array.from({ length: res.data.totalPageSize - 1 }, (v, k) => k + 2).map(async (ite) => {
          return await BasicDataQueryBackstage.pageTrainingChannelInfoInTrainingChannelAdmin({
            page: { pageNo: ite, pageSize: 200 },
            request: { ids }
          })
        })
      ).then((re) => {
        infoList.push(...(re.map((item) => item.data?.currentPageData || []).flat() || []))
      })
    }
    infoList.map((item) => {
      infoMap.set(item.id, ThematicManagementItem.fromList(item))
    })
    return infoMap
  }

  /**
   * 查询专题管理员下所有专题信息
   */
  async getThematicListByUserId(id: string) {
    const page = new Page(1, 200)
    const thematicDtoList = new Array<TrainingChannelPageResponse>()
    const res = await BasicDataQueryBackstage.pageTrainingChannelInfo({
      page,
      request: { userIdList: [id] }
    })
    if (res?.data?.currentPageData.length) {
      thematicDtoList.push(...res.data.currentPageData)
    }
    if (res?.data?.totalPageSize && res.data.totalPageSize > 1) {
      for (let i = 1; i < res.data.totalPageSize; i++) {
        page.pageNo += 1
        const res = await BasicDataQueryBackstage.pageTrainingChannelInfo({
          page,
          request: { userIdList: [id] }
        })

        if (res?.data?.currentPageData?.length) {
          thematicDtoList.push(...res.data.currentPageData)
        }
      }
    }

    return thematicDtoList.map((item) => {
      return ThematicManagementItem.fromList(item)
    })
  }

  /**
   * 批量更新方案展示
   * @param param
   * @param param.filePath 文件路径
   * @param param.fileName 文件名
   */
  async batchUpdateSchemeShow(param: { filePath: string; fileName: string }): Promise<ResponseStatus> {
    const request = new BatchUpdateSchemeRequest()
    request.filePath = param.filePath
    request.fileName = param.fileName
    const res = await Training.batchUpdateSchemeShow(request)

    if (res?.status && res.status.isSuccess()) {
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   * 获取批量更新方案展示模板
   */
  async getBatchUpdateSchemeTemplate() {
    const res = await Training.getTainingChannelSchemePortalShowTempletePath()

    return res?.data || ''
  }
}
