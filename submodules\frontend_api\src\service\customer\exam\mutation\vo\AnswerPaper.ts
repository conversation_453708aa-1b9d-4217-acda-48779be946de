import { ExamMethodEnum } from '@api/service/common/enums/train-class/ExamMethodEnum'

/**
 * 试卷大题信息
 <AUTHOR>
 */
export class QuestionGroupViewResponse {
  /**
   * 大题序号
   */
  sequence: number
  /**
   * 试题类型
   <p>
   说明：
   0 表示混合题型，即该大题下存在多种试题类型的组合
   1 表示单选题
   2 表示多选题
   3 表示填空题
   4 表示判断题
   5 表示简答题
   6 表示父子题
   </p>
   */
  questionType: number
  /**
   * 大题名称
   */
  groupName: string
  /**
   * 试题数
   */
  questionCount: number
  /**
   * 每题平均分数
   */
  eachQuestionScore: number
}
/**
 * @Description （跟底层返回的一致）答卷信息
 <AUTHOR>
 @Date 15:19 2022/2/24
 */
export class AnswerPaperResponse {
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位id
   */
  unitId: string
  /**
   * 所属服务商id
   */
  servicerId: string
  /**
   * id
   */
  id: string
  /**
   * 答卷状态
   */
  status: number
  /**
   * 答卷状态
   */
  answerPaperStatus: number
  /**
   * 答卷作答状态
   */
  answerStatus: number
  /**
   * 答卷阅卷状态
   */
  markStatus: number
  /**
   * 答卷评定结果
   */
  evaluateResult: number
  /**
   * 场景类型
   */
  sceneType: number
  /**
   * 场景id
   */
  sceneId: string
  qualificationId: string
  userId: string
  /**
   * 答题数
   */
  answerCount: number
  /**
   * 开始作答时间
   */
  beginAnswerTime: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 发布时间
   */
  publishedTime: string
  /**
   * 交卷时间
   */
  handingTime: string
  /**
   * 交卷完成时间
   */
  handedTime: string
  /**
   * 阅卷开始时间
   */
  markingTime: string
  /**
   * 阅卷完成时间
   */
  markedTime: string
  /**
   * 发布时间
   */
  pusblishedTime: string
  /**
   * 考试时长
   */
  timeLength: number
  /**
   * 阅卷人ID
   */
  markUserId: string
  /**
   * 得分，-1表示不是为分数评定方式
   */
  score: number
  /**
   * 正确题数，-1表示试题不进行评定
   */
  correctCount: number
  /**
   * 错误题数，-1表示试题不进行评定
   */
  incorrectCount: number
  studentNo: string
  name: string
  description: string
  totalScore: number
  cancelReason: string
  givenScore: number
  createUserId: string
  systemHanded: boolean
  groups: Array<QuestionGroupViewResponse>
  /**
   * 试题
   */
  questions: Array<QuestionResponse>
  /**
   * 答卷时长
   */
  answerTimeLength: number
}
/**
 * 答卷信息
 */
export class AnswerPaperViewResponse {
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷描述
   */
  description: string
  /**
   * 作答时长
   */
  timeLength: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 剩余作答时长
   */
  remainderTimeLength: number
  /**
   * 大题集合，为空集合表示本答卷没有使用大题展示方式
   */
  groups: Array<QuestionGroupViewResponse>
  /**
   * 试题集合
   */
  questions: Array<QuestionResponse>
  /**
   * 作答控制
   */
  control: AnswerPaperControlViewResponse
  /**
   * UI呈现方式
   */
  display: AnswerPaperUIDisplayViewResponse
}
/**
 * <AUTHOR>
 @date 2021/6/8 15:06
 @Description: 答卷响应抽象类
 */
export interface QuestionResponse {
  /**
   * 真项A
   */
  correctAnswerText?: string
  /**
   * 真项A
   */
  incorrectAnswerText?: string
  /**
   * 正确答案
   */
  opinionCorrectAnswer?: boolean
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
   @see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   * 0 表示混合题型，即该大题下存在多种试题类型的组合
   * 1 表示单选题
   * 2 表示多选题
   * 3 表示填空题
   * 4 表示判断题
   * 5 表示简答题
   * 6 表示父子题
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
}
/**
 * 答卷UI呈现控制视图
 <AUTHOR>
 */
export class AnswerPaperUIDisplayViewResponse {
  /**
   * 试题呈现方式
   1 表示整卷
   2 表示大题
   3 表示单题
   */
  questionDisplay: number
  /**
   * 是否展示试题解析
   */
  dissectsShow: boolean
  /**
   * 是否显示分数
   */
  scoreShow: boolean
}

/**
 * 答卷作答控制视图
 <AUTHOR>
 */
export class AnswerPaperControlViewResponse {
  /**
   * 是否有时长控制
   */
  timeLengthRestrict: boolean
  /**
   * 试题提交答案后是否可以重答
   */
  questionAgainAnswer: boolean
}
/**
 * 简单题
 @author: zhengp
 @since 2021/8/26 14:53
 */
export class AskQuestionResponse implements QuestionResponse {
  /**
   * 已作答答案
   */
  askAnswer: string
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
   @see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
}

/**
 * 父子题
 @author: zhengp
 @since 2021/8/26 14:53
 */
export class FatherQuestionResponse implements QuestionResponse {
  /**
   * 子题集合
   */
  childQuestions: Array<QuestionResponse>
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
   @see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
}

/**
 * 填空题
 @author: zhengp
 @since 2021/8/26 14:53
 */
export class FillQuestionResponse implements QuestionResponse {
  /**
   * 填空数
   */
  fillCount: number
  /**
   * 已作答答案
   */
  fillAnswer: Array<MapDto>
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
   @see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
}

/**
 * 多选题
 @author: zhengp
 @since 2021/8/26 14:53
 */
export class MultipleQuestionResponse implements QuestionResponse {
  /**
   * 可选答案列表
   */
  answerOptions: Array<ChooseAnswerOption> = []
  /**
   * 已答答案
   */
  multipleAnswer: Array<string>
  /**
   * 正确答案
   */
  trueMultipleAnswer: Array<string>
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
   @see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
}

/**
 * 判断题
 @author: zhengp
 @since 2021/8/26 14:53
 */
export class OpinionQuestionResponse implements QuestionResponse {
  /**
   * 可选答案列表
   */
  answerOptions: Array<ChooseAnswerOption>
  /**
   * 正确文本
   */
  correctAnswerText: string
  /**
   * 错误文本
   */
  incorrectAnswerText: string
  /**
   * 用户答案
   */
  opinionAnswer: boolean
  /**
   * 正确答案(废弃)
   */
  trueOpinionAnswer: boolean
  /**
   * 正确答案
   */
  opinionCorrectAnswer: boolean
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
   @see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
}

/**
 * 单选题
 @author: zhengp
 @since 2021/8/26 14:53
 */
export class RadioQuestionResponse implements QuestionResponse {
  /**
   * 可选答案列表
   */
  answerOptions: Array<ChooseAnswerOption>
  /**
   * 已作答答案
   */
  radioAnswer: string
  /**
   * 正确答案
   */
  trueRadioAnswer: string
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
   @see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
}
/**
 * @author: zhengp
 @since 2021/8/27 10:35
 */
export class MapDto {
  key: number
  answer: string
}
/**
 * 选择题答案选项
 <AUTHOR>
 */
export class ChooseAnswerOption {
  /**
   * 答案ID
   */
  id: string
  /**
   * 答案内容
   */
  content: string
}
