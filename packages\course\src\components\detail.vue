<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div slot="header" class="">
        <span class="tit-txt">基础信息</span>
      </div>
      <div class="f-p30">
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" label-width="120px" class="m-text-form is-column">
              <el-form-item label="课程封面：">
                <el-image
                  :src="'/mfs' + courseDetail.picture"
                  :preview-src-list="['/mfs' + courseDetail.picture]"
                  class="course-pic"
                >
                </el-image>
              </el-form-item>
              <el-form-item label="课程名称：">
                <el-skeleton animated :rows="1" v-if="!courseDetail.name" />
                {{ courseDetail.name }}
              </el-form-item>
              <!-- <el-form-item label="课程分类：" animated :rows="1">{{ categories }}</el-form-item> -->
              <!-- <el-form-item label="课件供应商：" animated :rows="1">{{ courseDetail.supplierName }}</el-form-item> -->
              <el-form-item label="课程简介：">
                <el-skeleton animated :rows="1" v-if="!courseDetail.id" />
                <div v-html="courseDetail.description"></div>
              </el-form-item>
              <el-form-item label="课程教师：">
                <el-skeleton animated :rows="1" v-if="!courseDetail.teachers" />
                {{ teacherName(courseDetail.getTeacherNames()) }}
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-card>
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div slot="header" class="">
        <span class="tit-txt">课程内容</span>
      </div>
      <div class="f-p30">
        <el-row type="flex" justify="center">
          <el-col>
            <p class="f-fb f-clear">
              <i class="f-dot f-mr5"></i>
              <span class="f-f15">课程目录</span>
              <el-button type="primary" size="mini" class="f-fr" @click="previewCourse(courseId)">预览</el-button>
            </p>
            <div v-for="outline in courseDetail.chapters" :key="outline.id">
              <el-table stripe :data="outline.coursewares" max-height="500px" class="m-table f-mt10 f-mb20">
                <el-table-column :label="outline.name" min-width="300">
                  <template slot-scope="{ row }">
                    <i class="el-icon-video-play f-f20 f-c9"></i>
                    <span class="f-ml5">{{ row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column min-width="100" align="center">
                  <template slot-scope="{ row }">{{ row.timeLengthFormat }}</template>
                </el-table-column>
                <el-table-column width="100" align="center">
                  <template slot-scope="{ row }">
                    <el-checkbox :disabled="true" v-model="row.trialType" :true-label="1" :false-label="0"
                      >试听</el-checkbox
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Prop, Component, Vue, Mixins } from 'vue-property-decorator'
  import CourseDetail from '@api/service/management/resource/course/query/vo/CourseDetail'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import PreviewCourseMixins from '@hbfe/jxjy-admin-common/src/mixins/PreviewCourseMixins'

  @Component
  export default class extends Mixins(PreviewCourseMixins) {
    @Prop({
      type: String,
      default: ''
    })
    courseId: string

    courseDetail: CourseDetail = new CourseDetail()

    get categories() {
      const temp =
        (this.courseDetail?.categories?.length &&
          this.courseDetail?.categories?.map(item => {
            return item.name
          })) ||
        []

      return temp.join('/')
    }

    // 教师名去重
    teacherName(name: string) {
      return Array.from(new Set(name.split('、'))).join()
    }

    async created() {
      this.courseDetail = await ResourceModule.courseFactory.queryCourse.queryCourseById(this.courseId)
      // console.log(this.courseDetail.courseOutline)
    }
  }
</script>
<style lang="scss" scoped></style>
