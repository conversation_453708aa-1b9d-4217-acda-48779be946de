<route-meta>
  {
  "isMenu": true,
  "title": "集体报名退款订单",
  "sort": 2,
  "icon": "icon_menhuxinxiguanli"
  }
</route-meta>

<script lang="ts">
  import RefundCollectiveIndex from '@hbfe/jxjy-admin-trade/src/refund/collective/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, FXS, GYS, ZTGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    query: [WXGLY, FXS, GYS],
    queryzt: [ZTGLY],
    export: [WXGLY, FXS, GYS],
    exportzt: [ZTGLY],
    exportDetail: [WXGLY, FXS, GYS, ZTGLY],
    refundDetail: [WXGLY, FXS, GYS],
    refundApproval: [WXGLY, FXS, GYS],
    batchAgreeRefund: [WXGLY, FXS, GYS],
    batchConfirmRefund: [WXGLY, FXS, GYS]
  })
  export default class extends RefundCollectiveIndex {}
</script>
