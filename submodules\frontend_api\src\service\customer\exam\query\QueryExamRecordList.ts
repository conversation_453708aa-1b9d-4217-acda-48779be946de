import { ResponseStatus, Response } from '@hbfe/common'
import MsExamQueryFrontGatewayCourseLearningForeStage, {
  AnswerPaperSort,
  Page,
  SortTypeEnum
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryForeStage'
import { ExaminationAnswerPaperResponseVo } from '@api/service/customer/exam/query/vo/ExaminationAnswerPaperResponseVo'

/**
 * 用户域获取培训班商品列表
 */
class QueryExamRecordList {
  // region properties

  /**
   *总数目，类型为number
   */
  totalSize = 0
  // /**
  //  *sku过滤条件，类型为SkuPropertyVo
  //  */
  // filterSkuVo = new SkuPropertyVo()
  /**
   * 考试记录列表，类型为TrainClassCommodityVo[]
   */
  examRecordList: ExaminationAnswerPaperResponseVo[] = []
  // endregion
  // region methods

  /**
   * 获取考试记录
   */
  async queryExamRecordList(page: Page, qualificationId: string): Promise<Array<ExaminationAnswerPaperResponseVo>> {
    // const filter = new CommoditySkuRequest()
    // filter.skuPropertyRequest = this.filterSkuVo.convertToDto()
    const res = await MsExamQueryFrontGatewayCourseLearningForeStage.pageMyExaminationRecordInMyself({
      page: {
        pageSize: page.pageSize,
        pageNo: page.pageNo
      },
      qualificationId: qualificationId,
      answerPaperStatus: 2,
      answerPaperSort: AnswerPaperSort.CREATE_TIME,
      sort: SortTypeEnum.DESC
    })
    if (res.status.isSuccess()) {
      this.examRecordList = res.data.currentPageData as ExaminationAnswerPaperResponseVo[]
      this.totalSize = res.data.totalSize
    }
    //pageCommoditySkuCustomerPurchaseInServicer
    return this.examRecordList
  }

  // endregion
}
export default QueryExamRecordList
