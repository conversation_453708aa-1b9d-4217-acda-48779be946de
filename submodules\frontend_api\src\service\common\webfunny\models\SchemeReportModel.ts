import BaseReportModel from '@api/service/common/webfunny/models/BaseReportModel'

export default class SchemeReportModel extends BaseReportModel {
  /**
   * 课程id
   */
  courseId?: string

  /**
   * 课程名称
   */
  courseName?: string

  /**
   * 附件路径或文本内容
   */
  context?: string

  /**
   * 心得类型
   */
  experienceType?: string

  /**
   * 学员学习token
   */
  applyStudentLearningToken?: string

  /**
   * 方案配置项目(SchemeConfig)
   */
  schemeConfig?: string

  /**
   * 学号参训资格id
   */
  qualificationId?: string

  /**
   * 方案id
   */
  schemeId?: string

  /**
   * 方案名称
   */
  schemeName?: string

  /**
   * 学习方式id
   */
  learningId?: string
}

//商品配置信息
export class SchemeConfig {
  schemeName?: string // 培训商品名称
  commoditySkuId?: string // 商品信息
  openPrintTemplate?: boolean // 证明开放状态
  trainingResult?: number // 培训合格状态
  templateId?: string //模板id
}
