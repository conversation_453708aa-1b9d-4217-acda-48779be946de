import { Ref, Vue } from 'vue-property-decorator'
import PageRequest from '@api/service/common/models/common/PageRequest'
import { Form } from 'element-ui'
import { ResponseStatus } from '@api/Response'
import * as _ from 'lodash'
import { bind } from 'lodash-decorators'
import CommonUtils from '@api/CommonUtils'

abstract class CrudVue<Model, Query> extends Vue {
  @Ref('addForm') addForm: Form
  @Ref('editForm') editForm: Form
  @Ref('searchForm') searchForm: Form
  detailModel: Model = {} as Model
  createModel: Model = {} as Model
  updateModel: Model = {} as Model
  showDetail = false
  showAdd = false
  showEdit = false
  loading = true
  query: Query
  pageQuery: Query
  modelConstructor: { new (): Model }
  queryConstructor: { new (): Query }
  title: string
  /**
   * 是否在增加成功后执行进行查询动作
   */
  searchAfterSave = true
  /**
   * 是否在更新成功后执行进行查询动作
   */
  searchAfterUpdate = true
  /**
   * 是否在删除成功后执行进行查询动作
   */
  searchAfterRemove = true
  createLoading = false
  updateLoading = false
  removeLoading = false
  enableLoading = false
  disableLoading = false
  page = new PageRequest()
  pageRequest = new PageRequest()

  constructor(modelConstructor: { new (): Model }, queryConstructor: { new (): Query }, title: string) {
    super()
    this.modelConstructor = modelConstructor
    this.queryConstructor = queryConstructor
    this.detailModel = new modelConstructor()
    this.createModel = new modelConstructor()
    this.updateModel = new modelConstructor()
    this.query = new queryConstructor()
    this.pageQuery = new queryConstructor()
    this.title = title
  }

  @bind
  clickSearch() {
    if (!CommonUtils.equals(this.pageQuery, this.query, 'pageNo', 'pageSize')) {
      this.pageRequest.pageNo = 1
    }
    this.pageQuery = new this.queryConstructor()
    CommonUtils.deepCopy(this.pageQuery, this.query)
    Object.assign(this.page, this.pageRequest)
    this.search()
  }

  @bind
  async search() {
    this.loading = true
    try {
      await this.doSearch()
    } finally {
      this.loading = false
    }
  }

  @bind
  clickResetSearchForm() {
    this.searchForm?.resetFields()
    this.query = new this.queryConstructor()
    this.pageRequest.pageNo = 1
  }

  @bind
  clickResetAddForm() {
    this.addForm?.resetFields()
  }

  @bind
  clickResetEditForm() {
    this.editForm?.resetFields()
  }

  @bind
  handleSizeChange(pageSize = 10) {
    this.pageRequest.pageNo = 1
    this.pageRequest.pageSize = pageSize
    if (this.pageQuery instanceof PageRequest) {
      if (pageSize !== this.pageQuery.pageSize) {
        this.pageQuery.pageSize = pageSize
        this.pageQuery.pageNo = 1
      }
    }
    this.search()
  }

  @bind
  handleCurrentChange(pageNo = 1) {
    this.pageRequest.pageNo = pageNo
    if (this.pageQuery instanceof PageRequest) {
      this.pageQuery.pageNo = pageNo
    }
    this.search()
  }

  @bind
  openDetail(id: string) {
    const model = this.getById(id)
    if (model) {
      this.detailModel = _.cloneDeep(model)
    }
    this.showDetail = true
  }

  @bind
  openAdd() {
    this.createModel = new this.modelConstructor()
    this.showAdd = true
  }

  @bind
  openEdit(id: string) {
    const model = this.getById(id)
    if (model) {
      this.updateModel = _.cloneDeep(model)
    }
    this.showEdit = true
  }

  @bind
  async clickRemove(id: string) {
    this.$confirm('确定删除该' + this.title, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(async () => {
      this.removeLoading = true
      let status
      try {
        status = await this.doRemove(id)
      } finally {
        this.removeLoading = false
      }
      if (status.isSuccess()) {
        this.$alert('删除成功', { type: 'success' }).then(async () => {
          if (this.searchAfterRemove) {
            await this.search()
            this.afterRemove()
          } else {
            this.afterRemove()
          }
        })
      } else {
        this.$alert(status.errors[0].message, '删除失败')
      }
    })
  }

  afterRemove() {
    console.log('afterRemove')
  }

  @bind
  async clickSave() {
    this.addForm.validate(async (valid: boolean) => {
      if (valid) {
        this.createLoading = true
        let status
        try {
          status = await this.doCreate()
        } finally {
          this.createLoading = false
        }
        if (status.isSuccess()) {
          this.$alert('创建成功', { type: 'success' }).then(async () => {
            this.showAdd = false
            if (this.searchAfterSave) {
              await this.search()
              this.afterSave()
            } else {
              this.afterSave()
            }
          })
        } else {
          this.$alert(status.errors[0].message, '创建失败')
        }
      }
    })
  }

  afterSave() {
    console.log('afterSave')
  }

  @bind
  async clickUpdate() {
    this.editForm.validate(async (valid: boolean) => {
      if (valid) {
        this.$confirm('是否确定修改', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(async () => {
          this.updateLoading = true
          let status
          try {
            status = await this.doUpdate()
          } finally {
            this.updateLoading = false
          }
          if (status.isSuccess()) {
            this.$alert('更新成功', { type: 'success' }).then(async () => {
              this.showEdit = false
              if (this.searchAfterUpdate) {
                await this.search()
                this.afterUpdate()
              } else {
                this.afterUpdate()
              }
            })
          } else {
            this.$alert(status.errors[0].message, '更新失败')
          }
        })
      }
    })
  }

  afterUpdate() {
    console.log('afterUpdate')
  }

  enable(id: string) {
    this.$confirm('确定启用该' + this.title, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(async () => {
      this.enableLoading = true
      let status
      try {
        status = await this.doEnable(id)
      } finally {
        this.enableLoading = false
      }
      if (status.isSuccess()) {
        this.$alert('启用成功', { type: 'success' }).then(async () => {
          if (this.searchAfterUpdate) {
            await this.search()
            this.afterUpdate()
          } else {
            this.afterUpdate()
          }
        })
      } else {
        this.$alert(status.errors[0].message, '删除失败')
      }
    })
  }

  disable(id: string) {
    this.$confirm('确定停用该' + this.title, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(async () => {
      this.disableLoading = true
      let status
      try {
        status = await this.doDisable(id)
      } finally {
        this.disableLoading = false
      }
      if (status.isSuccess()) {
        this.$alert('停用成功', { type: 'success' }).then(async () => {
          if (this.searchAfterUpdate) {
            await this.search()
            this.afterUpdate()
          } else {
            this.afterUpdate()
          }
        })
      } else {
        this.$alert(status.errors[0].message, '删除失败')
      }
    })
  }

  async doEnable(id: string): Promise<ResponseStatus> {
    return new ResponseStatus(200)
  }

  async doDisable(id: string): Promise<ResponseStatus> {
    return new ResponseStatus(200)
  }

  async doSearch(): Promise<ResponseStatus> {
    return new ResponseStatus(200)
  }

  async doCreate(): Promise<ResponseStatus> {
    return new ResponseStatus(200)
  }

  async doUpdate(): Promise<ResponseStatus> {
    return new ResponseStatus(200)
  }

  async doRemove(id: string): Promise<ResponseStatus> {
    return new ResponseStatus(200)
  }

  getById(id: string): Model | undefined {
    return new this.modelConstructor()
  }

  get list(): Array<Model> {
    return new Array<Model>()
  }

  get total(): number {
    return 0
  }

  get pageCount(): number {
    const plus = this.total % this.page.pageSize
    let pageCount = this.total / this.page.pageSize
    if (plus > 0) {
      pageCount++
    }
    return pageCount
  }
}

export default CrudVue
