import AbstractEnum from '../AbstractEnum'
// TRAINING_VOUCHER：培训券，对接众智汇云培训券
// TRAINING_VOUCHER_CHECK_PAY：培训券验证支付，对接众智汇云培训券
// ALIPAY：支付宝
// WXPAY：微信支付
// HAOBAO_PAY：号百支付
// QM：快钱支付
// TL：通联支付
// ZERO_PAY：0元订单支付
// WXPAY_V3：微信支付

enum PaymentChannelEnum {
  TRAINING_VOUCHER = 'TRAINING_VOUCHER',
  ALIPAY = 'ALIPAY',
  WXPAY = 'WXPAY',
  ALIPAYH5 = 'ALIPAYH5',
  WXPAYH5 = 'WXPAYH5'
}

export { PaymentChannelEnum }

class PaymentChannelType extends AbstractEnum<PaymentChannelEnum> {
  static enum = PaymentChannelEnum
  constructor() {
    super()
    this.map[PaymentChannelEnum.TRAINING_VOUCHER] = '培训券'
    this.map[PaymentChannelEnum.ALIPAY] = '支付宝'
    this.map[PaymentChannelEnum.WXPAY] = '微信'
    this.map[PaymentChannelEnum.ALIPAYH5] = '支付宝H5'
    this.map[PaymentChannelEnum.WXPAYH5] = '微信H5'
  }
}
export default new PaymentChannelType()
