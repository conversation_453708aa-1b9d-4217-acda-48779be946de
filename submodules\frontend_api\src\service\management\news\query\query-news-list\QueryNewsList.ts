/*
 * @Description: 资讯列表
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-09 18:49:34
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-25 11:48:32
 */

import BasicDataQueryBackstage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
import NewsListParamVo from '@api/service/management/news/query/query-news-list/vo/NewsListParamVo'
import newsListVo from '@api/service/management/news/query/query-news-list/vo/NewsListVo'
import { Page } from '@hbfe/common'
import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
import UserModule from '@api/service/management/user/UserModule'
import AdminUserInfoVo from '@api/service/management/user/query/manager/vo/AdminUserInfoVo'
import PlatformTrainingChannel, {
  TrainingChannelPageResponse,
  TrainingChannelRequest
} from '@api/platform-gateway/platform-training-channel-back-gateway'
import QueryNewsListBase from '@api/service/management/news/query/query-news-list/vo/QueryNewsListBase'

class QueryNewsList extends QueryNewsListBase {
  /**
   * 获取资讯列表
   */
  async queryNewsList(page: Page, queryRequest: NewsListParamVo): Promise<Array<newsListVo>> {
    const request = NewsListParamVo.to(queryRequest)
    const { data } = await BasicDataQueryBackstage.pageSimpleNews({
      page,
      queryRequest: request
    })
    page.totalSize = data.totalSize
    page.totalPageSize = data.totalPageSize
    const newsList = new Array<newsListVo>()
    const userIdList = new Array<string>()
    const newsRegionList = new Array<string>()
    data.currentPageData.map(item => {
      newsList.push(newsListVo.from(item))
      userIdList.push(item.publishUserId)
      if (item.areaCodePath) newsRegionList.push(...item.areaCodePath.slice(1).split('/'))
    })
    if (!newsList.length) return
    let regionRes = new Array<RegionTreeVo>()
    if (newsRegionList.length) regionRes = await QueryPhysicalRegion.querRegionDetil([...new Set(newsRegionList)])
    const map = await this.getUserInfo(userIdList)
    newsList.map(item => {
      item.setUserName(map.get(item.publishUserId)?.userName)
      item.setRegionName(regionRes)
    })
    return newsList
  }

  /**
   * 查询专题资讯
   * @param page 分页
   * @param queryRequest 筛选项
   */
  async querySpecialNewsList(page: Page, queryRequest: NewsListParamVo) {
    const request = NewsListParamVo.toSpecial(queryRequest)
    // 专题的查询要帮后端捞id
    if (queryRequest.belongSpecialName) {
      const idRequest = new TrainingChannelRequest()
      idRequest.name = queryRequest.belongSpecialName
      const ids = new Array<string>()
      const queryIdPage = new Page()
      queryIdPage.pageNo = 1
      queryIdPage.pageSize = 200
      const queryIdRes = await PlatformTrainingChannel.pageTrainingChannelInfo({
        page: queryIdPage,
        request: idRequest
      })
      if (queryIdRes?.data?.currentPageData?.length) {
        queryIdRes.data.currentPageData.map(item => {
          ids.push(item.id)
        })
        request.specialSubjectIds = ids
      } else {
        page.totalSize = 0
        page.totalPageSize = 0
        return []
      }
    }
    const { data } = await BasicDataQueryBackstage.pageTrainingChannelSimpleNews({
      page,
      queryRequest: request
    })
    page.totalSize = data.totalSize
    page.totalPageSize = data.totalPageSize

    const specialIds =
      (data?.currentPageData?.length &&
        data.currentPageData.map(item => {
          if (item.specialSubjectId) {
            return item.specialSubjectId
          }
        })) ||
      []
    let specialList = new Array<TrainingChannelPageResponse>()
    if (specialIds.length) {
      // 捞专题名称
      const queryNamePage = new Page()
      queryNamePage.pageSize = specialIds.length
      queryNamePage.pageNo = 1
      const querySpecialNameRequest = new TrainingChannelRequest()
      querySpecialNameRequest.ids = specialIds
      const querySpecial = await PlatformTrainingChannel.pageTrainingChannelInfo({
        page: queryNamePage,
        request: querySpecialNameRequest
      })

      if (querySpecial.data.currentPageData.length) {
        specialList = querySpecial.data.currentPageData
      }
    }

    const newsList = new Array<newsListVo>()
    const userIdList = new Array<string>()
    const newsRegionList = new Array<string>()
    data.currentPageData.map(item => {
      newsList.push(newsListVo.from(item, specialList))
      userIdList.push(item.publishUserId)
      if (item.areaCodePath) newsRegionList.push(...item.areaCodePath.slice(1).split('/'))
    })
    if (!newsList.length) return
    let regionRes = new Array<RegionTreeVo>()
    if (newsRegionList.length) regionRes = await QueryPhysicalRegion.querRegionDetil([...new Set(newsRegionList)])
    const map = await this.getUserInfo(userIdList)
    newsList.map(item => {
      item.setUserName(map.get(item.publishUserId)?.userName)
      item.setRegionName(regionRes)
    })
    return newsList
  }
}

export default QueryNewsList
