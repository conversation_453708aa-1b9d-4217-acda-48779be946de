/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-10-17 09:30:31
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-08-26 10:16:18
 * @Description: 证书打印
 */
import Learning, {
  StudentSchemeLearningResponsePage,
  UserPropertyRequest,
  UserRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import UserModule from '@api/service/management/user/UserModule'
import { Page, Response } from '@hbfe/common'
import ConfigJsonUtil from '../../train-class/Utils/ConfigJsonUtil'
import StudentUserInfoVo from '../../user/query/student/vo/StudentUserInfoVo'
import LearningArcjovesRequest from './vo/LearningArcjovesRequest'
import LearningArcjovesResponse from './vo/LearningArcjovesResponse'
import PrintedOut from './vo/PrintedOut'
import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
class QueryAdminTrainingArchivesList {
  // 培训档案列表
  trainingArchivesList: Array<any>

  /**
   * @description: 查询学员打印证书-网校管理员
   * @param {*}
   * @return {*}
   */
  async queryLearningArchivesList(page: Page, request: LearningArcjovesRequest) {
    const param = await this.getParam(request)
    if (!param) {
      page.totalSize = 0
      page.totalPageSize = 0
      return []
    }
    const response = await Learning.pageStudentSchemeLearningInServicer4PrintProof({
      page,
      request: param
    })
    const List = await this.getReturnData(page, response)
    await this.fillSchemeInfo(List)
    return List
  }
  /**
   * @description: 查询学员打印证书-专题管理员
   * @return {*}
   * @param page
   * @param request
   */
  async queryLearningTrainingChannelList(page: Page, request: LearningArcjovesRequest) {
    const param = await this.getParam(request)
    if (!param) {
      page.totalSize = 0
      page.totalPageSize = 0
      return []
    }
    const response = await Learning.pageStudentSchemeLearningInServicer4PrintProofInTrainingChannel({
      page,
      request: param
    })
    const List = await this.getReturnData(page, response)
    await this.fillSchemeInfo(List)
    return List
  }
  /**
   * @description: 查询学员打印证书-分销商
   * @param {*}
   * @return {*}
   */
  async queryLearningArchivesListInDistributor(page: Page, request: LearningArcjovesRequest) {
    const param = await this.getParam(request)
    if (!param) {
      page.totalSize = 0
      page.totalPageSize = 0
      return []
    }
    const response = await Learning.pageStudentSchemeLearningInServicer4PrintProofInDistributor({
      page,
      request: param
    })
    const List = await this.getReturnData(page, response)
    await this.fillSchemeInfoInDistributor(List)
    return List
  }

  /**
   * 参数处理
   */
  private async getParam(request: LearningArcjovesRequest) {
    const param = LearningArcjovesRequest.to(request)

    if (request.idCard || request.name) {
      // 查学员ID
      const module = UserModule.queryUserFactory.queryStudentList
      module.queryStudentIdParams.idCard = request.idCard ? request.idCard : undefined
      module.queryStudentIdParams.userName = request.name ? request.name : undefined
      const re = await module.queryStudentIdList()
      if (re.data.length === 0) {
        return null
      }
      param.student = new UserRequest()
      param.student.userProperty = new UserPropertyRequest()
      param.student.userIdList = re.data
      param.student.userProperty.companyName = request.unit || undefined
    }
    return param
  }

  /**
   * 返回值处理
   */
  private async getReturnData(page: Page, response: Response<StudentSchemeLearningResponsePage>, role?: CategoryEnums) {
    page.totalSize = response.data.totalSize
    page.totalPageSize = response.data.totalPageSize
    const voList = response.data.currentPageData.map((item) => LearningArcjovesResponse.from(item))
    const userId = voList.map((item) => item.id)
    if (userId.length > 0) {
      const module = UserModule.queryUserFactory.queryStudentList
      const reStu = await module.queryStudentListInSubject(userId)
      const map = new Map<string, StudentUserInfoVo>()
      reStu.data.forEach((item) => {
        map.set(item.userId, item)
      })
      voList.forEach((item) => {
        const temp = map.get(item.id)
        if (temp) {
          item.name = temp.userName
          item.idCard = temp.idCard
          item.unit = temp.companyName
        }
      })
    }
    return voList
  }

  /**
   * 填充方案信息
   * @private
   */
  private async fillSchemeInfo(voList: Array<LearningArcjovesResponse>) {
    const schemeIds: string[] = voList.map((item) => item.trainingId)
    const schemeIdList = [...new Set(schemeIds.filter(Boolean))]
    const queryM = new ConfigJsonUtil()
    const configJsonMap = await queryM.batchQuerySchemeJsonConfigMapBySchemeId(schemeIdList, ['name'])
    voList.forEach((item) => {
      const currentTemp = configJsonMap.get(item.trainingId)
      if (currentTemp) {
        item.trainingName = currentTemp.name
      }
    })
  }
  /**
   * 填充方案信息
   * @private
   */
  private async fillSchemeInfoInDistributor(voList: Array<LearningArcjovesResponse>) {
    const schemeIds: string[] = voList.map((item) => item.trainingId)
    const schemeIdList = [...new Set(schemeIds.filter(Boolean))]
    const queryM = new ConfigJsonUtil()
    const configJsonMap = await queryM.batchQuerySchemeJsonConfigMapBySchemeIdInDistributor(schemeIdList, ['name'])
    voList.forEach((item) => {
      const currentTemp = configJsonMap.get(item.trainingId)
      if (currentTemp) {
        item.trainingName = currentTemp.name
      }
    })
  }
  /**
   * 查询打印记录
   */
  queryPrintedOut(id: string) {
    //
    return new Array<PrintedOut>()
  }
}

export default QueryAdminTrainingArchivesList
