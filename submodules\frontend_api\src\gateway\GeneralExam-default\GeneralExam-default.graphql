schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询试题
		@param id
		@return
	"""
	findQuestion(id:String):QuestionResponse
	"""获取全平台的根试卷分类
		@return
	"""
	getAllRootPaperClassificationList:[PaperClassificationListResponse]
	"""获取模拟考答卷
		@param examRoundId
		@return
	"""
	getExamAnswerPaper(examRoundId:String):ExamAnswerPaperResponse
	"""获取模拟考答卷
		@param schemeId
		@param learningId
		@return
	"""
	getExamAnswerPaperByLearningId(schemeId:String,learningId:String):ExamAnswerPaperResponse
	"""获取试卷详情
		@param id
		@return
	"""
	getExamPaper(id:String):ExamPaperResponse
	"""获取试卷详情
		@param schemeId
		@param learningId
		@return
	"""
	getExamPaperByLearningId(schemeId:String,learningId:String):ExamPaperResponse
	"""根据学习方式id查询用户考前试题练习答卷
		@param learningId
		@return
	"""
	getPracticeAnswerList(learningId:String):[PracticeAnswerPaperResponse]
	"""获取练习卷
		@param id
		@return
	"""
	getPracticePaper(learningId:String):PracticePaperResponse
	"""获取子试卷分类
		@param parentId
		@return
	"""
	getSubPaperClassificationList(parentId:String):[PaperClassificationListResponse]
	"""查询子题库
		@param parentId
		@return
	"""
	getSubQuestionLibraryList(parentId:String):[QuestionLibraryListResponse]
	"""试卷是否被引用
		@param id
		@return
	"""
	hasReference(id:String):Boolean!
	"""是否是收藏试题
		@param param
		@return
	"""
	isFavoriteQuestion(param:FavoriteQuestionRequest):Boolean!
	"""试卷名称是否已存在
		@param unitId
		@param name
		@param id
		@return
	"""
	isNameExists(unitId:String,name:String,id:String):Boolean!
	"""是否有子分类
		@param id
		@return
	"""
	paperClassificationHasChild(id:String):Boolean!
	"""是否被引用
		@param id
		@return
	"""
	paperClassificationHasReference(id:String):Boolean!
	"""指定分类下的子分类名称是否重复
		@param name
		@param parentId
		@param id
		@return
	"""
	paperClassificationNameExists(name:String,parentId:String,id:String):Boolean!
	"""是否有子题库
		@param id
		@return
	"""
	questionLibraryHasChild(id:String):Boolean!
	"""是否被试题引用
		@param id
		@return
	"""
	questionLibraryHasReference(id:String):Boolean!
	"""名称是否存在
		@param name
		@param parentId
		@param id
		@return
	"""
	questionLibraryNameExists(name:String,parentId:String,id:String):Boolean!
	"""是否是收藏试题
		@param schemeId
		@param issueId
		@param questionIds
		@return
	"""
	validateFavorite(schemeId:String,issueId:String,questionIds:[String]):[FavoriteQuestionValidateResponse]
}
type Mutation {
	"""添加收藏试题
		@param param
	"""
	addUserFavoriteQuestion(param:FavoriteQuestionRequest):Void
	"""批量迁移试题到目标题库
		@param originLibraryId
		@param targetLibraryId
	"""
	batchMoveQuestionToAnotherLibrary(originLibraryId:String,targetLibraryId:String):Void
	"""拷贝试卷
		@param id
		@param newName
		@return
	"""
	copyExamPaper(id:String,newName:String):ExamPaperResponse
	"""创建考试试卷
		@param create
		@return
	"""
	createExamPaper(create:ExamPaperCreateRequest):ExamPaperResponse
	"""创建试卷分类
		@param create
		@return
	"""
	createPaperClassification(create:PaperClassificationCreateRequest):PaperClassificationResponse
	"""创建试题
		@param create
		@return
	"""
	createQuestion(create:QuestionCreateRequest):QuestionResponse
	"""创建题库
		@param create
		@return
	"""
	createQuestionLibrary(create:QuestionLibraryCreateRequest):QuestionLibraryResponse
	"""删除考试试卷
		@param id
	"""
	deleteExamPaper(id:String):Void
	"""删除试卷分类
		@param id
	"""
	deletePaperClassification(id:String):Void
	"""删除试题
		@param id
	"""
	deleteQuestion(id:String):Void
	"""删除试题库
		@param id
	"""
	deleteQuestionLibrary(id:String):Void
	"""删除用户答卷记录
		@param roundId
		@param answerInfoId
	"""
	deleteUserExamAnswerInfo(roundId:String,answerInfoId:String,userId:String):Void
	"""停用试卷
		@param id
		@return
	"""
	disableExamPaper(id:String):ExamPaperResponse
	"""停用试题
		@param id
		@return
	"""
	disableQuestion(id:String):QuestionResponse
	"""停用题库
		@param id
		@return
	"""
	disableQuestionLibrary(id:String):QuestionLibraryResponse
	"""启用试卷
		@param id
		@return
	"""
	enableExamPaper(id:String):ExamPaperResponse
	"""启用试题
		@param id
		@return
	"""
	enableQuestion(id:String):QuestionResponse
	"""启用题库
		@param id
		@return
	"""
	enableQuestionLibrary(id:String):QuestionLibraryResponse
	"""根据试卷id获取预览的token
		@param paperId
		@return
	"""
	getPreviewTokenByPaper(paperId:String):String
	"""去做模拟考试
		@param examRoundId
		@return 考场id
	"""
	goExamination(examRoundId:String):String
	"""去做模拟考试
		@param token
		@return 考场id
	"""
	goExaminationByToken(token:String):String
	"""去做练习
		@param paperId
		@return 答卷id
	"""
	goPractice(paperId:String):String
	"""去做练习
		@param token
		@return
	"""
	goPracticeByToken(token:String):String
	"""去做练习
		@param token
		@return
	"""
	goPracticeByTokenAndSpecifyQuestionCount(token:String,questionCount:Int!):String
	"""重做某场考试
		@param answersId
		@param answerRecordId
		@return 考场id
	"""
	redoExamination(answersId:String,answerRecordId:String):String
	"""重做练习
		@param answersId
		@param answerRecordId
		@return
	"""
	redoPractice(answersId:String,answerRecordId:String):String
	"""移除收藏试题
		@param param
	"""
	removeUserFavoriteQuestion(param:FavoriteQuestionRequest):Void
	"""修改考试试卷
		@param update
		@return
	"""
	updateExamPaper(update:ExamPaperUpdateRequest):ExamPaperResponse
	"""更新试卷分类
		@param update
		@return
	"""
	updatePaperClassification(update:PaperClassificationUpdateRequest):PaperClassificationResponse
	"""更新试题
		@param update
		@return
	"""
	updateQuestion(update:QuestionUpdateRequest):QuestionResponse
	"""修改题库
		@param update
		@return
	"""
	updateQuestionLibrary(update:QuestionLibraryUpdateRequest):QuestionLibraryResponse
}
input TagDTO @type(value:"com.fjhb.platform.component.exam.commons.api.dto.question.TagDTO") {
	id:String
	code:String
	tag:String
}
"""试卷分类创建请求
	<AUTHOR> create 2020/4/20 11:42
"""
input PaperClassificationCreateRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.classification.request.PaperClassificationCreateRequest") {
	"""试卷分类名称"""
	name:String
	"""父试卷分类id"""
	parentId:String
	"""描述"""
	description:String
}
"""试卷分类更新请求
	<AUTHOR> create 2020/4/20 11:58
"""
input PaperClassificationUpdateRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.classification.request.PaperClassificationUpdateRequest") {
	"""分类id"""
	id:String
	"""试卷分类名称"""
	name:String
	"""父试卷分类id"""
	parentId:String
	"""描述"""
	description:String
}
"""考试卷创建请求
	<AUTHOR> create 2020/4/20 13:35
"""
input ExamPaperCreateRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.examination.request.ExamPaperCreateRequest") {
	"""试卷名称"""
	name:String
	"""试卷分类id"""
	paperTypeId:String
	"""配置类型，来至ConfigType
		1：固定卷 2：AB卷 3：智能卷
	"""
	configType:Int!
	"""试卷分数"""
	totalScore:Double!
	"""建议及格分数"""
	passScore:Double!
	"""试卷总题数"""
	totalQuestionCount:Int!
	"""计时方式
		@see PaperTimeType
	"""
	timeType:Int!
	"""建议考试时长"""
	timeLength:Double!
	"""描述"""
	description:String
	"""是否草稿"""
	draft:Boolean!
	"""是否启用"""
	enabled:Boolean!
	"""随机卷抽题类型 1：自定义（此时需要指定random配置），2：题库，3：考场所属方案下已选课程关联试题"""
	fetchWay:Int!
	"""题库方式"""
	libraryWay:LibraryWayRequest
	"""随机"""
	random:RandomRequest
}
"""考试卷修改请求
	<AUTHOR> create 2020/4/20 13:35
"""
input ExamPaperUpdateRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.examination.request.ExamPaperUpdateRequest") {
	"""试卷id"""
	id:String
	"""试卷名称"""
	name:String
	"""试卷分类id"""
	paperTypeId:String
	"""试卷分数"""
	totalScore:Double!
	"""建议及格分数"""
	passScore:Double!
	"""试卷总题数"""
	totalQuestionCount:Int!
	"""计时方式
		@see PaperTimeType
	"""
	timeType:Int!
	"""建议考试时长"""
	timeLength:Double!
	"""描述"""
	description:String
	"""是否草稿"""
	draft:Boolean!
	"""是否启用"""
	enabled:Boolean!
	"""随机卷抽题类型 1：自定义（此时需要指定random配置），2：题库，3：考场所属方案下已选课程关联试题"""
	fetchWay:Int!
	"""题库方式"""
	libraryWay:LibraryWayRequest
	"""随机"""
	random:RandomRequest
}
"""
	<AUTHOR> create 2020/4/20 13:39
"""
input RandomConfigurationItemRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.examination.request.RandomConfigurationItemRequest") {
	"""配置项名称,可能是大题的名称"""
	name:String
	"""试题数量"""
	count:Int!
	"""试题类型，0/1/2/3/4/5/6/7 未知/判断/单选/多选/填空/简答/父子/混合
		智能卷不支持混合题型
	"""
	type:Int!
	"""分数分配类型
		1：平均分配
		2：独立分配
	"""
	scoreWay:Int!
	"""单题用时, 单位：分
		如果试卷的计时方式是[单题计时]，则控制的是大题下每道题可作答的时间长度
		@since 1.23.0
	"""
	singleAnswerableTimeLength:Double!
	"""配置项总分"""
	totalScore:Double!
	"""是否比率
		如果随机出题方式按试卷抽取则本项成员无效
	"""
	ratio:Boolean!
	"""随机抽取试题对象配置
		如果随机出题方式按试卷抽取则本项成员无效
	"""
	takeObjectConfigurationItems:[RandomTakeObjectConfigurationItemRequest]
}
"""随机卷请求
	<AUTHOR> create 2020/4/20 13:36
"""
input RandomRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.examination.request.RandomRequest") {
	"""大题项配置"""
	configurationItems:[RandomConfigurationItemRequest]
	"""随机卷类型
	"""
	randomType:Int!
	"""是否递归取题
		如果是题库卷时，才生效。
	"""
	recursive:Boolean!
	"""随机抽取试卷方式
		ExtractionType.EXAMPAPER=1：按试卷抽取 ExtractionType.BIGQUESTION=2：按配置项（大题）抽取
	"""
	randomWay:Int!
	"""是否比率
		如果随机出题方式按大题抽取则本项成员无效
	"""
	ratio:Boolean!
	"""随机抽取试题对象配置
		如果随机出题方式按大题抽取则本项成员无效
	"""
	takeObjectConfigurationItems:[RandomTakeObjectConfigurationItemRequest]
}
"""
	<AUTHOR> create 2020/4/20 13:38
"""
input RandomTakeObjectConfigurationItemRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.examination.request.RandomTakeObjectConfigurationItemRequest") {
	"""对象id：当为题库卷时存题库id
		当为对象卷时存储试题的对象id（一般为课程id）
	"""
	objectId:String
	"""对象类型  ，当为对象卷时此项必须赋值"""
	type:String
	"""比率值,最大值不大于100,最小不得小于1"""
	ratioValue:Int!
	"""标签id集合，对应类型为{@link RandomExamPaperType#NEW_TAG}时生效"""
	tags:[String]
	"""题库卷按具体数量抽题
		@see RandomExamPaper#ratio 为false生效
	"""
	extractCount:Int!
}
"""题库方式
	<AUTHOR> create 2020/6/3 10:40
"""
input LibraryWayRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.examination.request.fetchway.LibraryWayRequest") {
	"""题库id集合"""
	libraryIds:[String]
	"""是否递归取题
		如果是题库卷时，才生效。
	"""
	recursive:Boolean!
}
"""
	<AUTHOR> create 2020/4/20 17:01
"""
input BlankFillingRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.BlankFillingRequest") {
	"""答案数量"""
	answerCount:Int!
	"""当填空题类型为 精确匹配时，最外层集合为答案组（也就是每一组都是一道填空题的答案，满足当中的任意一组表示回答正确）第二层集合为空
		当填空题类似为 每空多答案时，最外层的集合为每个空的答案，第二层集合为每个空的备选答案
	"""
	answersGroup:[[String]]
	"""答案项分值
		当填空题类型为 精确匹配时此项值无效
	"""
	answersItemScore:[Double]
	"""答案类型
		@see BlankFillingAnswerType
	"""
	answerType:Int!
	"""答案是否有顺序.当{@link #answerType } = {@link BlankFillingAnswerType#MULTIPLE_PER_BLANK} 时，
		即每空多答案的情况下，答案是否是按照填空顺序排列。
	"""
	sequence:Boolean!
	"""评分标准"""
	standard:String
}
"""
	<AUTHOR> create 2020/4/20 16:58
"""
input ChoiceItemRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.ChoiceItemRequest") {
	"""选项ID"""
	id:String
	"""选项内容"""
	content:String
}
"""
	<AUTHOR> create 2020/4/20 17:06
"""
input ComprehensiveChildQuestionRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.ComprehensiveChildQuestionRequest") {
	"""子试题id"""
	questionId:String
	"""题目"""
	title:String
	"""试题类型
		@see QuestionType
	"""
	questionType:Int!
	"""判断题"""
	judgement:JudgementRequest
	"""单选题"""
	singleChoice:SingleChoiceRequest
	"""多选"""
	multipleChoice:MultipleChoiceRequest
	"""填空"""
	blankFilling:BlankFillingRequest
	"""问答题"""
	essay:EssayRequest
	"""量表题"""
	scale:ScaleRequest
	"""试题难度
		@see QuestionMode
	"""
	mode:Int!
	"""难度值"""
	difficultyValue:Double!
	"""试题解析"""
	description:String
}
"""
	<AUTHOR> create 2020/4/20 17:05
"""
input ComprehensiveRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.ComprehensiveRequest") {
	"""子题"""
	children:[ComprehensiveChildQuestionRequest]
}
"""
	<AUTHOR> create 2020/4/20 17:04
"""
input EssayRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.EssayRequest") {
	"""参考答案"""
	referenceAnswer:String
	"""评分标准"""
	standard:String
	"""是否限制作答长度"""
	limitAnswerLength:Boolean!
	"""允许作答的文本字符最少长度"""
	permitAnswerLengthMin:Int!
	"""允许作答的文本字符最大长度"""
	permitAnswerLengthMax:Int!
}
"""
	<AUTHOR> create 2020/4/20 17:19
"""
input FavoriteQuestionRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.FavoriteQuestionRequest") {
	"""试题id"""
	questionId:String
	"""方案id"""
	schemeId:String
	"""学习方式id"""
	issueId:String
}
"""
	<AUTHOR> create 2020/4/20 16:57
"""
input JudgementRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.JudgementRequest") {
	"""正确答案"""
	correctAnswer:Boolean!
	"""正确文本"""
	correctText:String
	"""错误文本"""
	incorrectText:String
}
"""
	<AUTHOR> create 2020/4/20 17:00
"""
input MultipleChoiceRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.MultipleChoiceRequest") {
	"""选项"""
	choiceItems:[ChoiceItemRequest]
	"""正确答案"""
	correctAnswers:[String]
}
"""
	<AUTHOR> create 2020/4/20 17:09
"""
input QuestionCreateRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.QuestionCreateRequest") {
	"""试题应用类型
		@see QuestionApplyType
	"""
	applyTypes:[Int]
	"""题库ID"""
	libraryId:String
	"""题目"""
	title:String
	"""判断题"""
	judgement:JudgementRequest
	"""单选题"""
	singleChoice:SingleChoiceRequest
	"""多选"""
	multipleChoice:MultipleChoiceRequest
	"""填空"""
	blankFilling:BlankFillingRequest
	"""问答题"""
	essay:ScaleRequest
	"""量表题"""
	scale:ScaleRequest
	"""综合题"""
	comprehensive:ComprehensiveRequest
	"""试题类型
		@see QuestionType
	"""
	questionType:Int!
	"""试题难度
		@see QuestionMode
	"""
	mode:Int!
	"""难度值"""
	difficulty:Double!
	"""试题解析"""
	description:String
	"""是否启用"""
	enabled:Boolean!
	"""关联课程id
		@deprecated 废弃，新字段{@link #relateCourseIds},如果关联多个课程此字段随机显示一个
	"""
	relateCourseId:String
	"""标签"""
	tags:[TagDTO]
	"""关联课程id"""
	relateCourseIds:[String]
}
"""
	<AUTHOR> create 2020/4/20 16:51
"""
input QuestionLibraryCreateRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.QuestionLibraryCreateRequest") {
	"""名称"""
	name:String
	"""描述"""
	description:String
	"""父试题库id 如果没有父题库为-1"""
	parentId:String
	"""是否可用"""
	enabled:Boolean!
	"""题库所属单位id"""
	unitId:String
}
"""
	<AUTHOR> create 2020/4/20 16:52
"""
input QuestionLibraryUpdateRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.QuestionLibraryUpdateRequest") {
	"""题库id"""
	id:String
	"""题库名称"""
	name:String
	"""描述"""
	description:String
	"""父试题库id 如果没有父题库为-1"""
	parentId:String
	"""是否可用"""
	enabled:Boolean!
}
"""
	<AUTHOR> create 2020/4/20 17:15
"""
input QuestionUpdateRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.QuestionUpdateRequest") {
	"""试题id"""
	id:String
	"""试题应用类型
		@see QuestionApplyType
	"""
	applyTypes:[Int]
	"""题库ID"""
	libraryId:String
	"""题目"""
	title:String
	"""判断题"""
	judgement:JudgementRequest
	"""单选题"""
	singleChoice:SingleChoiceRequest
	"""多选"""
	multipleChoice:MultipleChoiceRequest
	"""填空"""
	blankFilling:BlankFillingRequest
	"""问答题"""
	essay:EssayRequest
	"""量表题"""
	scale:ScaleRequest
	"""综合题"""
	comprehensive:ComprehensiveRequest
	"""试题类型
		@see QuestionType
	"""
	questionType:Int!
	"""试题难度
		@see QuestionMode
	"""
	mode:Int!
	"""难度值"""
	difficulty:Double!
	"""试题解析"""
	description:String
	"""是否启用"""
	enabled:Boolean!
	"""关联课程id
		@deprecated 废弃，新字段{@link #relateCourseIds},如果关联多个课程此字段随机显示一个
	"""
	relateCourseId:String
	"""标签"""
	tags:[TagDTO]
	"""关联课程id"""
	relateCourseIds:[String]
}
"""
	<AUTHOR> create 2020/4/20 17:05
"""
input ScaleRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.ScaleRequest") {
	"""量表类型"""
	scaleType:ScaleType
	"""程度_始，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写"""
	startDegree:String
	"""程度_止，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写"""
	endDegree:String
	"""级数"""
	series:Int!
	"""初始值"""
	initialValue:Int!
}
"""
	<AUTHOR> create 2020/4/20 16:58
"""
input SingleChoiceRequest @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.request.SingleChoiceRequest") {
	"""选项"""
	choiceItems:[ChoiceItemRequest]
	"""标准答案"""
	correctAnswer:String
}
enum ScaleType @type(value:"com.fjhb.platform.component.exam.commons.api.consts.question.ScaleType") {
	CUSTOM
	SATISFACTION
	RECOGNITION
	IMPORTANCE
	WILLING
	CONFORMITY
}
type Range @type(value:"com.fjhb.platform.component.exam.commons.api.dto.paper.mixture.immediateanswerable.Range") {
	key:String
	value:String
}
type TagDTO1 @type(value:"com.fjhb.platform.component.exam.commons.api.dto.question.TagDTO") {
	id:String
	code:String
	tag:String
}
"""试卷分类列表响应
	<AUTHOR> create 2020/4/20 11:39
"""
type PaperClassificationListResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.classification.response.PaperClassificationListResponse") {
	"""分类id"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""试卷分类名称"""
	name:String
	"""父试卷分类id"""
	parentId:String
	"""描述"""
	description:String
}
"""试卷分类响应
	<AUTHOR> create 2020/4/20 11:43
"""
type PaperClassificationResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.classification.response.PaperClassificationResponse") {
	"""分类id"""
	id:String
	"""试卷分类名称"""
	name:String
	"""父试卷分类id"""
	parentId:String
	"""描述"""
	description:String
}
"""题库方式
	<AUTHOR> create 2020/6/3 10:40
"""
type LibraryWayRequest1 @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.examination.request.fetchway.LibraryWayRequest") {
	"""题库id集合"""
	libraryIds:[String]
	"""是否递归取题
		如果是题库卷时，才生效。
	"""
	recursive:Boolean!
}
"""考试答卷信息
	<AUTHOR> create 2020/4/20 13:32
"""
type ExamAnswerPaperResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.examination.response.ExamAnswerPaperResponse") {
	"""答卷id"""
	id:String
	"""作答id"""
	answerId:String
	"""场次id"""
	examRoundId:String
	"""创建时间"""
	createTime:DateTime
	"""进入考试时间"""
	enterTime:DateTime
	"""是否交卷"""
	complete:Boolean!
	"""是否自动提交"""
	mandatorySubmission:Boolean!
	"""考试的提交时间"""
	completeTime:DateTime
	"""是否阅卷完成"""
	markComplete:Boolean!
	"""阅卷完成时间"""
	markCompleteTime:DateTime
	"""阅卷人Id"""
	markUserId:String
	"""评语"""
	comment:String
	"""及格分数"""
	passScore:Double!
	"""考试分数"""
	score:Double!
	"""试卷总分"""
	totalScore:Double!
	"""是否及格"""
	passed:Boolean!
	"""总的试题数"""
	totalCount:Int!
	"""总的试题数(包括子题)"""
	totalQuestionCount:Int!
	"""已作答的试题数"""
	answeredCount:Int!
	"""已作答的试题数(包括子题)"""
	answeredQuestionCount:Int!
	"""作答率(包括子题)"""
	answeredPercentage:Double!
	"""作答正确的试题数"""
	correctCount:Int!
	"""作答正确的试题数(包括子题)"""
	correctQuestionCount:Int!
	"""作答正确率(包括子题)"""
	correctPercentage:Double!
}
"""
	<AUTHOR> create 2020/4/20 13:43
"""
type ExamPaperResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.examination.response.ExamPaperResponse") {
	"""试卷id"""
	id:String
	"""试卷名称"""
	name:String
	"""试卷分类id"""
	paperTypeId:String
	"""配置类型，来至ConfigType
		1：固定卷 2：AB卷 3：智能卷
	"""
	configType:Int!
	"""试卷分数"""
	totalScore:Double!
	"""建议及格分数"""
	passScore:Double!
	"""试卷总题数"""
	totalQuestionCount:Int!
	"""计时方式
		@see PaperTimeType
	"""
	timeType:Int!
	"""建议考试时长"""
	timeLength:Double!
	"""描述"""
	description:String
	"""是否草稿"""
	draft:Boolean!
	"""是否启用"""
	enabled:Boolean!
	"""创建时间"""
	createTime:DateTime
	"""创建人"""
	createUserId:String
	"""最后修改时间"""
	lastChangeTime:DateTime
	"""随机"""
	random:RandomResponse
	"""随机卷抽题类型 1：自定义（此时需要指定random配置），2：题库，3：考场所属方案下已选课程关联试题"""
	fetchWay:Int!
	"""题库方式"""
	libraryWay:LibraryWayRequest1
}
"""
	<AUTHOR> create 2020/4/20 13:39
"""
type RandomConfigurationItemResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.examination.response.RandomConfigurationItemResponse") {
	"""配置项id"""
	id:String
	"""配置项名称,可能是大题的名称"""
	name:String
	"""试题数量"""
	count:Int!
	"""试题类型，0/1/2/3/4/5/6/7 未知/判断/单选/多选/填空/简答/父子/混合
		智能卷不支持混合题型
	"""
	type:Int!
	"""分数分配类型
		1：平均分配
		2：独立分配
	"""
	scoreWay:Int!
	"""单题用时, 单位：分
		如果试卷的计时方式是[单题计时]，则控制的是大题下每道题可作答的时间长度
		@since 1.23.0
	"""
	singleAnswerableTimeLength:Double!
	"""配置项总分"""
	totalScore:Double!
	"""是否比率
		如果随机出题方式按试卷抽取则本项成员无效
	"""
	ratio:Boolean!
	"""随机抽取试题对象配置
		如果随机出题方式按试卷抽取则本项成员无效
	"""
	takeObjectConfigurationItems:[RandomTakeObjectConfigurationItemResponse]
}
"""随机卷请求
	<AUTHOR> create 2020/4/20 13:36
"""
type RandomResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.examination.response.RandomResponse") {
	"""配置类型"""
	configType:Int
	"""大题项配置"""
	configurationItems:[RandomConfigurationItemResponse]
	"""随机卷类型
	"""
	randomType:Int!
	"""是否递归取题
		如果是题库卷时，才生效。
	"""
	recursive:Boolean!
	"""随机抽取试卷方式
		ExtractionType.EXAMPAPER=1：按试卷抽取 ExtractionType.BIGQUESTION=2：按配置项（大题）抽取
	"""
	randomWay:Int!
	"""是否比率
		如果随机出题方式按大题抽取则本项成员无效
	"""
	ratio:Boolean!
	"""随机抽取试题对象配置
		如果随机出题方式按大题抽取则本项成员无效
	"""
	takeObjectConfigurationItems:[RandomTakeObjectConfigurationItemResponse]
}
"""
	<AUTHOR> create 2020/4/20 13:38
"""
type RandomTakeObjectConfigurationItemResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.examination.response.RandomTakeObjectConfigurationItemResponse") {
	"""对象id：当为题库卷时存题库id
		当为对象卷时存储试题的对象id（一般为课程id）
	"""
	objectId:String
	"""对象类型  ，当为对象卷时此项必须赋值"""
	type:String
	"""比率值,最大值不大于100,最小不得小于1"""
	ratioValue:Int!
	"""标签id集合，对应类型为{@link RandomExamPaperType#NEW_TAG}时生效"""
	tags:[String]
	"""题库卷按具体数量抽题
		@see RandomExamPaper#ratio 为false生效
	"""
	extractCount:Int!
}
"""通用练习答卷响应
	<AUTHOR> create 2020/4/20 13:56
"""
type PracticeAnswerPaperResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.practice.response.PracticeAnswerPaperResponse") {
	"""答卷id"""
	id:String
	"""作答id"""
	answerId:String
	"""练习卷id"""
	practicePaperId:String
	"""创建时间"""
	createTime:DateTime
	"""进入时间"""
	enterTime:DateTime
	"""是否已完成"""
	complete:Boolean!
	"""完成时间"""
	completeTime:DateTime
	"""是否已阅卷完成"""
	markComplete:Boolean!
	"""阅卷完成时间"""
	markCompleteTime:DateTime
	"""总题数"""
	totalNum:Int!
	"""已答题数"""
	answeredNum:Int!
	"""对题数"""
	correctNum:Int!
	"""错题数"""
	failNum:Int!
	"""及格分数"""
	passScore:Double!
	"""考试分数"""
	score:Double!
	"""试卷总分"""
	totalScore:Double!
	"""抽题方式，1：指定考纲方式，2：按试题题型及抽题比例"""
	fetchWay:Int!
	"""试题类别"""
	questionCategory:String
	"""考纲id（答卷所属考纲id）"""
	examinationOutlineId:String
	"""应用程序标识
		<p>该属性要生效，必须{@link #namespaceIdentity}也要有值</p>
	"""
	applicationIdentity:String
	"""应用程序的命名空间标识, 在子项目级别唯一
		<p>该属性要生效，必须{@link #applicationIdentity}也要有值</p>
	"""
	namespaceIdentity:String
	"""考核范围"""
	ranges:[Range]
	"""卷子的生成配置"""
	generateConfiguration:PaperGenerateConfigurationResponse
	"""作答模式
		@see AnswerMode
	"""
	answerMode:Int!
	"""严格作答模式的配置"""
	strictAnswerMode:StrictAnswerModeConfigResponse
	"""宽松作答模式的配置"""
	looseAnswerMode:LooseAnswerModeConfigResponse
	"""卷子的作答配置"""
	answerConfiguration:PaperAnswerConfigurationResponse
	"""卷子的阅卷配置"""
	markConfiguration:MarkConfigurationResponse
	"""是否发布"""
	released:Boolean!
}
"""考前练习卷
	<AUTHOR> create 2020/4/20 15:26
"""
type PracticePaperResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.practice.response.PracticePaperResponse") {
	"""试卷id"""
	id:String
	"""试卷名称"""
	name:String
	"""试卷分类id"""
	paperTypeId:String
	"""配置类型，来至ConfigType
		1：固定卷 2：AB卷 3：智能卷
	"""
	configType:Int!
	"""试卷分数"""
	totalScore:Double!
	"""建议及格分数"""
	passScore:Double!
	"""试卷总题数"""
	totalQuestionCount:Int!
	"""计时方式
		@see PaperTimeType
	"""
	timeType:Int!
	"""建议考试时长"""
	timeLength:Double!
	"""描述"""
	description:String
	"""是否草稿"""
	draft:Boolean!
	"""是否启用"""
	enabled:Boolean!
	"""创建时间"""
	createTime:DateTime
	"""创建人"""
	createUserId:String
	"""最后修改时间"""
	lastChangeTime:DateTime
}
"""
	<AUTHOR> create 2020/4/20 16:19
"""
type LooseAnswerModeConfigResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.practice.response.config.LooseAnswerModeConfigResponse") {
	"""是否限制作答次数"""
	limitAnswerCount:Boolean!
	"""允许作答次数"""
	permitAnswerCount:Int!
	"""及格后是否允许继续作答
		<p>如果允许，作答次数也要控制在限制作答次数内</p>
	"""
	continueIfPassed:Boolean!
	"""允许入场的起始时间"""
	enterStartTime:DateTime
	"""允许入场的终止时间"""
	enterEndTime:DateTime
	"""<p>允许的作答时长， 单位: 分；0表示无限制,"""
	answerTimeLength:Double!
}
"""
	<AUTHOR> create 2020/4/20 16:36
"""
type MarkConfigurationResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.practice.response.config.MarkConfigurationResponse") {
	"""及格分数"""
	passedScore:Double!
	"""多选题的多选漏选是否得分"""
	scoredForMissChoice:Boolean!
	"""是否自动批阅填空题"""
	autoMarkBlankQuestion:Boolean!
	"""成绩等级的评定配置"""
	scoreGradeConfiguration:ScoreGradeConfigurationResponse
	"""交卷成功后是否在答题页面同步显示作答结果"""
	showAnswerResult:Boolean!
	"""卷子结果公布类型
		@see ScoreAnnounceType
	"""
	announceType:Int!
	"""<p>卷子结果公布时间.
		<p>{@link #announceType 卷子结果公布类型}是{@link ScoreAnnounceType#LIMIT 限时公布}且当前时间大于等于公布时间，交卷则显示成绩
	"""
	announceTime:DateTime
}
"""
	<AUTHOR> create 2020/4/20 16:35
"""
type PaperAnswerConfigurationResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.practice.response.config.PaperAnswerConfigurationResponse") {
	"""<p>学员提前交卷的最少作答时长，单位: 分；
		<p>0表示不限制
		<p>交卷时, 参与服务端的逻辑校验
	"""
	minimumAnswerTimeLength:Double!
	"""是否显示试题的正确答案, 有个【显示答案】按钮，切换显示答案。"""
	showQuestionCorrectResult:Boolean!
	"""试卷作答时试题的显示模式
		@see PaperQuestionDisplayMode
	"""
	displayMode:Int!
	"""是否允许回退作答
		<p>{@link #displayMode 试卷作答时试题的显示模式}是{@link PaperQuestionDisplayMode#SINGLE 单题作答}
	"""
	back:Boolean!
	"""是否启用剪贴版.
		禁用则不允许用户在[答题]时执行[剪切/复制]的操作
	"""
	enableClipboard:Boolean!
	"""<p>是否显示及格的分数
		<p>若开放这个选项, 必须设置{@link PaperMarkConfigurationDTO#showAnswerResult 是否在答题页面同步显示作答结果}为<code>true</code>
	"""
	showPassedScore:Boolean!
}
"""
	<AUTHOR> create 2020/4/20 16:02
"""
type PaperGenerateConfigurationResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.practice.response.config.PaperGenerateConfigurationResponse") {
	"""预生成的答卷数量
		<p>提前生成若干张答卷，学员进入场次，可从这些答卷当中随机抽取
		<p>如果试卷的生成方式是 "对象卷" 此项配置无效。（因为对象卷是在学员进入场次时才确定抽题的对象）
	"""
	preGenerateCount:Int!
	"""是否打乱试题题序, 当前仅在为固定卷时有效"""
	confuseQuestion:Boolean!
	"""是否打乱选项题的选择项"""
	confuseChoiceItem:Boolean!
}
"""
	<AUTHOR> create 2020/4/20 16:37
"""
type ScoreGradeConfigurationResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.practice.response.config.ScoreGradeConfigurationResponse") {
	"""总分"""
	score:Double!
	"""评定规则"""
	rules:[ScoreGradeRuleResponse]
}
"""
	<AUTHOR> create 2020/4/20 16:37
"""
type ScoreGradeRuleResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.practice.response.config.ScoreGradeRuleResponse") {
	"""<p>区间值，作为一种构建考核表达式的原料</p>
		<p>考试能力服务未限定或提供常量供使用者使用，建议使用大于0的数值，且等级越高数值越大，便于构建考核表达式</p>
		<p>该值在阅卷完成后，会通过MQ的方式传达至上层应用，使用者请自行维护。</p>
		<p> -1 是服务内部使用的值，供未配置评分模板的卷子阅卷后的成绩评定使用</p>
	"""
	value:Int!
	"""成绩等级名称, 例如：优、良、及格、不及格"""
	title:String
	"""<p>成绩等级的评定分数</p>"""
	gradeScore:Double!
	"""评级成绩的匹配模式
		@see GradeScoreComparisonOperator
	"""
	comparisonOperator:Int!
}
"""
	<AUTHOR> create 2020/4/20 16:06
"""
type StrictAnswerModeConfigResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.practice.response.config.StrictAnswerModeConfigResponse") {
	"""是否限制作答次数"""
	limitAnswerCount:Boolean!
	"""允许作答次数"""
	permitAnswerCount:Int!
	"""及格后是否允许继续作答
		<p>如果允许，作答次数也要控制在限制作答次数内</p>
	"""
	continueIfPassed:Boolean!
	"""作答开始时间"""
	startTime:DateTime
	"""作答结束时间"""
	endTime:DateTime
	"""允许延迟开始作答的时长, 单位: 分"""
	lateTimeLength:Double!
}
"""
	<AUTHOR> create 2020/4/20 17:01
"""
type BlankFillingResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.response.BlankFillingResponse") {
	"""答案数量"""
	answerCount:Int!
	"""当填空题类型为 精确匹配时，最外层集合为答案组（也就是每一组都是一道填空题的答案，满足当中的任意一组表示回答正确）第二层集合为空
		当填空题类似为 每空多答案时，最外层的集合为每个空的答案，第二层集合为每个空的备选答案
	"""
	answersGroup:[[String]]
	"""答案项分值
		当填空题类型为 精确匹配时此项值无效
	"""
	answersItemScore:[Double]
	"""答案类型
		@see BlankFillingAnswerType
	"""
	answerType:Int!
	"""答案是否有顺序.当{@link #answerType } = {@link BlankFillingAnswerType#MULTIPLE_PER_BLANK} 时，
		即每空多答案的情况下，答案是否是按照填空顺序排列。
	"""
	sequence:Boolean!
	"""评分标准"""
	standard:String
}
"""
	<AUTHOR> create 2020/4/20 16:58
"""
type ChoiceItemResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.response.ChoiceItemResponse") {
	"""选项ID"""
	id:String
	"""选项内容"""
	content:String
}
"""
	<AUTHOR> create 2020/4/20 17:06
"""
type ComprehensiveChildQuestionResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.response.ComprehensiveChildQuestionResponse") {
	"""子试题id"""
	questionId:String
	"""题目"""
	title:String
	"""试题类型
		@see QuestionType
	"""
	questionType:Int!
	"""判断题"""
	judgement:JudgementResponse
	"""单选题"""
	singleChoice:SingleChoiceResponse
	"""多选"""
	multipleChoice:MultipleChoiceResponse
	"""填空"""
	blankFilling:BlankFillingResponse
	"""问答题"""
	essay:EssayResponse
	"""量表题"""
	scale:ScaleResponse
	"""试题难度
		@see QuestionMode
	"""
	mode:Int!
	"""难度值"""
	difficultyValue:Double!
	"""试题解析"""
	description:String
}
"""
	<AUTHOR> create 2020/4/20 17:05
"""
type ComprehensiveResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.response.ComprehensiveResponse") {
	"""子题"""
	children:[ComprehensiveChildQuestionResponse]
}
"""
	<AUTHOR> create 2020/4/20 17:04
"""
type EssayResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.response.EssayResponse") {
	"""参考答案"""
	referenceAnswer:String
	"""评分标准"""
	standard:String
	"""是否限制作答长度"""
	limitAnswerLength:Boolean!
	"""允许作答的文本字符最少长度"""
	permitAnswerLengthMin:Int!
	"""允许作答的文本字符最大长度"""
	permitAnswerLengthMax:Int!
}
"""
	<AUTHOR> create 2020/5/19 10:31
"""
type FavoriteQuestionValidateResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.response.FavoriteQuestionValidateResponse") {
	"""试题id"""
	questionId:String
	"""是否收藏题"""
	favorite:Boolean!
}
"""
	<AUTHOR> create 2020/4/20 16:57
"""
type JudgementResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.response.JudgementResponse") {
	"""正确答案"""
	correctAnswer:Boolean!
	"""正确文本"""
	correctText:String
	"""错误文本"""
	incorrectText:String
}
"""
	<AUTHOR> create 2020/4/20 17:00
"""
type MultipleChoiceResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.response.MultipleChoiceResponse") {
	"""选项"""
	choiceItems:[ChoiceItemResponse]
	"""正确答案"""
	correctAnswers:[String]
}
"""
	<AUTHOR> create 2020/4/20 16:49
"""
type QuestionLibraryListResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.response.QuestionLibraryListResponse") {
	"""试题库ID"""
	id:String
	"""父试题库id 如果没有父题库为-1"""
	parentId:String
	"""名称"""
	name:String
	"""描述"""
	description:String
	"""是否可用"""
	enabled:Boolean!
	"""非内置根节点"""
	notBuildInRootNode:Boolean!
	"""授权归属类型
		@see AuthorizedBelongsTypeEnum
	"""
	belongsType:String
	"""试题数量"""
	questionCount:Long!
	"""是否有子题库"""
	hasChildren:Boolean!
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
}
"""
	<AUTHOR> create 2020/4/20 16:50
"""
type QuestionLibraryResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.response.QuestionLibraryResponse") {
	"""试题库ID"""
	id:String
	"""父试题库id 如果没有父题库为-1"""
	parentId:String
	"""名称"""
	name:String
	"""描述"""
	description:String
	"""是否可用"""
	enabled:Boolean!
	"""非内置根节点"""
	notBuildInRootNode:Boolean!
	"""授权归属类型
		@see AuthorizedBelongsTypeEnum
	"""
	belongsType:String
}
"""
	<AUTHOR> create 2020/4/20 16:56
"""
type QuestionResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.response.QuestionResponse") {
	"""试题id"""
	id:String
	"""试题应用类型
		@see QuestionApplyType
	"""
	applyTypes:[Int]
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""题库ID"""
	libraryId:String
	"""题目"""
	title:String
	"""判断题"""
	judgement:JudgementResponse
	"""单选题"""
	singleChoice:SingleChoiceResponse
	"""多选"""
	multipleChoice:MultipleChoiceResponse
	"""填空"""
	blankFilling:BlankFillingResponse
	"""问答题"""
	essay:EssayResponse
	"""量表题"""
	scale:ScaleResponse
	"""综合题"""
	comprehensive:ComprehensiveResponse
	"""试题类型
		@see QuestionType
	"""
	questionType:Int!
	"""试题难度
		@see QuestionMode
	"""
	mode:Int!
	"""难度值"""
	difficulty:Double!
	"""试题解析"""
	description:String
	"""创建人id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""最后修改时间"""
	lastChangeTime:DateTime
	"""是否启用"""
	enabled:Boolean!
	"""资源记录(数据)的授权源id
		a 授权 b, b 授权 c, c的sourceId是b, c的rootId是a
	"""
	rootId:String
	"""数据授权的Token, 并不需要默认值"""
	token:String
	"""关联课程id
		@deprecated 废弃，新字段{@link #relateCourseIds},如果关联多个课程此字段随机显示一个
	"""
	relateCourseId:String
	"""标签"""
	tags:[TagDTO1]
	"""关联课程id"""
	relateCourseIds:[String]
}
"""
	<AUTHOR> create 2020/4/20 17:05
"""
type ScaleResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.response.ScaleResponse") {
	"""量表类型"""
	scaleType:ScaleType
	"""程度_始，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写"""
	startDegree:String
	"""程度_止，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写"""
	endDegree:String
	"""级数"""
	series:Int!
	"""初始值"""
	initialValue:Int!
}
"""
	<AUTHOR> create 2020/4/20 16:58
"""
type SingleChoiceResponse @type(value:"com.fjhb.platform.core.v1.exam.general.kernel.gateway.graphql.questionlibrary.response.SingleChoiceResponse") {
	"""选项"""
	choiceItems:[ChoiceItemResponse]
	"""标准答案"""
	correctAnswer:String
}

scalar List
