import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-fjzj-school'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum StudentSchemeLearningSortField {
  REGISTER_TIME = 'REGISTER_TIME',
  SCHEME_YEAR = 'SCHEME_YEAR'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class DeliveryAddress {
  consignee: string
  phone: string
  region: string
  address: string
}

export class TakePoint {
  pickupLocation: string
  pickupTime: string
  remark?: string
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

export class StudentSchemeLearningRequest {
  studentNoList?: Array<string>
  student?: UserRequest
  learningRegister?: LearningRegisterRequest
  scheme?: SchemeRequest
  studentLearning?: StudentLearningRequest
  dataAnalysis?: DataAnalysisRequest
  connectManageSystem?: ConnectManageSystemRequest
  extendedInfo?: ExtendedInfoRequest
  openPrintTemplate?: boolean
  saleChannels?: Array<number>
  trainingChannelName?: string
  trainingChannelId?: string
  notDistributionPortal?: boolean
  trainingType?: string
  issueId?: string
}

export class StudentSchemeLearningSortRequest {
  field?: StudentSchemeLearningSortField
  policy?: SortPolicy
}

export class ConnectManageSystemRequest {
  syncStatus?: number
}

export class DataAnalysisRequest {
  trainingResultPeriod?: DoubleScopeRequest
  requirePeriod?: DoubleScopeRequest
  acquiredPeriod?: DoubleScopeRequest
}

export class ExtendedInfoRequest {
  whetherToPrint?: boolean
  applyCompanyCode?: string
  policyTrainingSchemeId?: string
  policyTrainingSchemeName?: string
}

export class LearningRegisterRequest {
  registerType?: number
  sourceType?: string
  sourceId?: string
  status?: Array<number>
  registerTime?: DateScopeRequest
  saleChannels?: Array<number>
  orderNoList?: Array<string>
  subOrderNoList?: Array<string>
  batchOrderNoList?: Array<string>
  distributorId?: string
  portalId?: string
}

export class RegionRequest {
  province?: string
  city?: string
  county?: string
}

export class StudentLearningRequest {
  trainingResultList?: Array<number>
  trainingResultTime?: DateScopeRequest
  notLearningTypeList?: Array<number>
  courseScheduleStatus?: number
  examAssessResultList?: Array<number>
}

export class RegionSkuPropertyRequest {
  province?: string
  city?: string
  county?: string
}

export class RegionSkuPropertySearchRequest {
  region?: Array<RegionSkuPropertyRequest>
  regionSearchType?: number
}

export class SchemeRequest {
  schemeId?: string
  schemeIdList?: Array<string>
  schemeType?: string
  schemeName?: string
  skuProperty?: SchemeSkuPropertyRequest
}

export class SchemeSkuPropertyRequest {
  year?: Array<string>
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  industry?: Array<string>
  subjectType?: Array<string>
  trainingCategory?: Array<string>
  trainingProfessional?: Array<string>
  technicalGrade?: Array<string>
  positionCategory?: Array<string>
  trainingObject?: Array<string>
  jobLevel?: Array<string>
  jobCategory?: Array<string>
  subject?: Array<string>
  grade?: Array<string>
  learningPhase?: Array<string>
  discipline?: Array<string>
  qualificationCategory?: Array<string>
  trainingWay?: Array<string>
  trainingInstitution?: Array<string>
  mainAdditionalItem?: Array<string>
}

export class UserPropertyRequest {
  regionList?: Array<RegionRequest>
  companyName?: string
  payOrderRegionList?: Array<RegionRequest>
}

export class UserRequest {
  userIdList?: Array<string>
  accountIdList?: Array<string>
  userProperty?: UserPropertyRequest
}

/**
 * 发票信息
<AUTHOR>
@since 2021/3/23
 */
export class InvoiceInfoRequest {
  /**
   * 发票抬头
   */
  title?: string
  /**
   * 发票抬头类型
<pre>
1-个人
2-企业
</pre>
   */
  titleType?: number
  /**
   * 发票类型
<pre>
1-电子发票
2-纸质发票
</pre>
   */
  invoiceType?: number
  /**
   * 发票种类
<pre>
1-普通发票
2-增值税普通发票
3-增值税专用发票
</pre>
   */
  invoiceCategory?: number
  /**
   * 购买方纳税人识别号
   */
  taxpayerNo?: string
  /**
   * 地址
   */
  address?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 开户行
   */
  bankName?: string
  /**
   * 账户
   */
  account?: string
  /**
   * 发票票面备注
   */
  remark?: string
  /**
   * 开票方式
1 - 线上开票
2 - 线下开票
   */
  invoiceMethod?: number
  /**
   * 联系电子邮箱
   */
  email?: string
  /**
   * 联系电话
   */
  contactPhone?: string
  /**
   * 营业执照
   */
  businessLicensePath?: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath?: string
  /**
   * 配送方式
0/1/2,无/自取/快递
@see OfflineShippingMethods
   */
  shippingMethod?: number
  /**
   * 配送地址信息
   */
  deliveryAddress?: DeliveryAddress
  /**
   * 自取点信息
   */
  takePoint?: TakePoint
}

/**
 * @Description
<AUTHOR>
@Date 2025/5/26 20:58
 */
export class Commodity {
  /**
   * 商品sku编号
   */
  skuId?: string
  /**
   * 商品数量
   */
  quantity?: number
}

/**
 * 请求创建订单
<AUTHOR>
@since 2021/1/22
 */
export class CreateHYWOrderRequest {
  /**
   * 买家编号
   */
  buyerId: string
  /**
   * 商品列表
   */
  commodities: Array<Commodity>
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceInfoRequest
  /**
   * 终端类型
<p>
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  terminalCode: string
}

/**
 * @Description
<AUTHOR>
@Date 2024/7/2 20:16
 */
export class CreateSecretSignUpInfoRequest {
  /**
   * 订单号
   */
  orderNo?: string
}

/**
 * 请求创建订单
<AUTHOR>
@since 2021/1/22
 */
export class CreateStudentAndPlaceOrderRequest {
  /**
   * 密文
   */
  ciphertext: string
  /**
   * 13位时间戳，用于请求过期校验
   */
  timer?: number
  /**
   * 签名
   */
  sign?: string
}

/**
 * @Description
<AUTHOR>
@Date 2024/7/2 20:16
 */
export class SyncStudentSignUpForAppRequest {
  /**
   * 订单号
   */
  orderNo?: string
}

export class SyncStudentSignUpRegisteredRequest {
  /**
   * 加密信息
@see  HywSyncStudentSignUpRegisteredParam
   */
  params?: string
  /**
   * 时间戳
   */
  time_span?: string
  /**
   * 签名
   */
  sign?: string
}

export class ValidHymStudentInfoRequest {
  /**
   * 学员姓名
   */
  userName: string
  /**
   * 证件号
   */
  certificateNumber: string
}

export class ConnectManageSystemResponse {
  syncStatus: number
  syncMessage: string
}

export class BatchOwnerResponse {
  unitId: string
  userId: string
}

export class ExtendedInfoResponse {
  whetherToPrint: boolean
  printTime: string
  pdfUrl: string
  certificateId: string
  certificateNo: string
}

export class LearningRegisterResponse {
  registerType: number
  sourceType: string
  sourceId: string
  status: number
  statusChangeTime: string
  registerTime: string
  saleChannel: number
  orderNo: string
  subOrderNo: string
  batchOrderNo: string
  frozenAndInvalidSourceType: string
  frozenAndInvalidSourceId: string
}

export class OwnerResponse {
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  servicerType: number
  servicerId: string
  batchOwner: BatchOwnerResponse
}

export class CourseLearningResponse {
  courseScheduleStatus: number
  courseQualifiedTime: string
  selectedCourseCount: number
  selectedCoursePeriod: number
  learningId: string
  learningType: number
  enabled: boolean
  learningResourceType: number
  learningResourceId: string
  userAssessResult: Array<string>
  learningResult: Array<LearningResultResponse>
}

export class DataAnalysisResponse {
  trainingResultPeriod: number
  requirePeriod: number
  acquiredPeriod: number
}

export class ExamLearningResponse {
  committedExam: boolean
  examAssessResult: number
  examQualifiedTime: string
  examCount: number
  maxExamScore: number
  learningId: string
  learningType: number
  enabled: boolean
  learningResourceType: number
  learningResourceId: string
  userAssessResult: Array<string>
  learningResult: Array<LearningResultResponse>
}

export class LearningExperienceLearningResponse {
  committedLearningExperience: boolean
  learningExperienceAssessResult: number
  learningExperienceQualifiedTime: string
  maxLearningExperienceScore: number
  learningExperiencePassCount: number
  learningId: string
  learningType: number
  enabled: boolean
  learningResourceType: number
  learningResourceId: string
  userAssessResult: Array<string>
  learningResult: Array<LearningResultResponse>
}

export class StudentLearningResponse {
  trainingResult: number
  trainingResultTime: string
  courseLearning: CourseLearningResponse
  examLearning: ExamLearningResponse
  learningExperienceLearning: LearningExperienceLearningResponse
  userAssessResult: Array<string>
  learningResult: Array<LearningResultResponse>
}

export class CertificateLearningConfigResultResponse implements LearningResultConfigResponse {
  certificateTemplateId: string
  openPrintTemplate: boolean
  resultType: number
}

export class GradeLearningConfigResultResponse implements LearningResultConfigResponse {
  gradeType: string
  grade: number
  resultType: number
}

export interface LearningResultConfigResponse {
  resultType: number
}

export class LearningResultResponse {
  learningResultId: string
  gainedTime: string
  learningResultConfig: LearningResultConfigResponse
}

export class SchemeResponse {
  schemeId: string
  schemeType: string
  skuProperty: SchemeSkuPropertyResponse
  schemeName: string
  learningResult: Array<LearningResultConfigResponse>
}

export class SchemeSkuPropertyResponse {
  year: SchemeSkuPropertyValueResponse
  province: SchemeSkuPropertyValueResponse
  city: SchemeSkuPropertyValueResponse
  county: SchemeSkuPropertyValueResponse
  industry: SchemeSkuPropertyValueResponse
  subjectType: SchemeSkuPropertyValueResponse
  trainingCategory: SchemeSkuPropertyValueResponse
  trainingProfessional: SchemeSkuPropertyValueResponse
  technicalGrade: SchemeSkuPropertyValueResponse
  positionCategory: SchemeSkuPropertyValueResponse
  trainingObject: SchemeSkuPropertyValueResponse
  jobLevel: SchemeSkuPropertyValueResponse
  jobCategory: SchemeSkuPropertyValueResponse
  subject: SchemeSkuPropertyValueResponse
  grade: SchemeSkuPropertyValueResponse
  learningPhase: SchemeSkuPropertyValueResponse
  discipline: SchemeSkuPropertyValueResponse
  certificatesType: SchemeSkuPropertyValueResponse
  practitionerCategory: SchemeSkuPropertyValueResponse
  trainingInstitution: SchemeSkuPropertyValueResponse
  mainAdditionalItem: SchemeSkuPropertyValueResponse
  trainingWay: SchemeSkuPropertyValueResponse
}

export class SchemeSkuPropertyValueResponse {
  skuPropertyValueId: string
}

export class RegionResponse {
  province: string
  city: string
  county: string
}

export class UserPropertyResponse {
  region: RegionResponse
  payOrderRegion: RegionResponse
}

export class UserResponse {
  userId: string
  accountId: string
  userProperty: UserPropertyResponse
}

/**
 * 校验结果返回
<AUTHOR> create 2021/2/3 10:53
 */
export class VerifyResultResponse {
  /**
   * 校验结果
   */
  message: string
  /**
   * 校验code
   */
  code: string
  /**
   * 订单内的商品skuId
   */
  skuId: string
  /**
   * 目前是(ms-learningscheme_reservingScheme)返回的子订单
   */
  subOrderNo: string
  /**
   * @see StudentSourceTypes
   */
  sourceType: string
  sourceId: string
}

/**
 * 创建订单结果
<AUTHOR> create 2021/1/29 17:27
 */
export class CreateHYWOrderResultResponse {
  /**
   * 是否创建成功
   */
  success: boolean
  /**
   * 订单号，仅当{@link #success}为{@code true}时有值
   */
  orderNo: string
  /**
   * 子订单号
   */
  subOrderNoList: Array<string>
  /**
   * 订单创建时间，仅当{@link #success}为{@code true}时有值
   */
  createTime: string
  /**
   * 下单结果信息
   */
  message: string
  /**
   * - 200成功
- 4002 用户未登录
- 4003 主方案或者合并方案选择错误
- 5001 订单创建异常
   */
  code: number
  /**
   * 商品验证结果信息
   */
  resultList: Array<VerifyResultResponse>
}

/**
 * 创建订单结果
返回的code
<p>
200      成功
500	    程序内部异常
6001     密文解析失败
90001    数据延迟
50001 	不可重复报名同一个班级
30001 	物品不存在
30002	商品处于下架状态
30003	购买渠道已关闭
30004	购买终端已关闭
50002	培训未开始
50003	培训已结束
50006 	当前用户此班级正在开班中
50007	当前用户此班级正在退班中
 */
export class CreateStudentAndPlaceOrderResponse {
  /**
   * 学员ID
   */
  userId: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 子订单号
   */
  subOrderNoList: Array<string>
  /**
   * 登录token
   */
  token: string
  /**
   * 参训资格id
   */
  qualificationId: string
  /**
   * 跳转页面
@see FJZJKeyEntity
商品下单页：PLACEORDER
学习页：LEARNING
支付页面：PAY
   */
  key: string
  /**
   * 是否学员自主下单
   */
  isSelfOrder: boolean
  /**
   * skuId
   */
  skuId: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

export class DropClassExtendedInfoResponse {
  /**
   * 已退公需学习内容标识 (1为专业课，2为公需课)
   */
  publicSign: boolean
  /**
   * 已退专业学习内容标识
   */
  professionalSign: boolean
}

/**
 * @Description
<AUTHOR>
@Date 2024/7/2 20:17
 */
export class EncryptedContentResponse {
  /**
   * 对接平台编号
   */
  unitCode: string
  /**
   * 参数加密
   */
  params: string
  /**
   * 13位时间戳，用于url过期校验
   */
  timer: string
  /**
   * 签名
   */
  sign: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

export class FJZJStudentSchemeLearningResponse {
  /**
   * 退课扩展消息
   */
  dropClassExtendedInfoResponse: DropClassExtendedInfoResponse
  qualificationId: string
  studentNo: string
  owner: OwnerResponse
  student: UserResponse
  learningRegister: LearningRegisterResponse
  scheme: SchemeResponse
  studentLearning: StudentLearningResponse
  dataAnalysis: DataAnalysisResponse
  connectManageSystem: ConnectManageSystemResponse
  extendedInfo: ExtendedInfoResponse
  schemeQuestionnaireRequirementCount: number
  schemeQuestionnaireNoAssessSubmittedCount: number
  schemeQuestionnaireSubmittedCount: number
  issueName: string
}

/**
 * code如下：
200  正常
500  异常
20001    人员不存在
 */
export class SyncStudentSignUpForAppResponse {
  /**
   * 加密信息
   */
  data: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * H5验证华医网是否存在学员信息返回结果
 */
export class ValidHymStudentInfoResponse {
  /**
   * 1.如是登录的身份为姓名+身份证+福建完全匹配单点登录，且用户开通权限（未开通权限由华医网开通权限）则进入掌上华医-专业课学习（选课列表）
2.如学员存在姓名+身份证，但注册地址非福建地区，则返回信息：您的当前地区不可学习该项目，请联系客服进行地区修改！
3.如学员身份证与姓名不符，则返回信息：尊敬的学员您好，您目前登录的身份1111111111******1234，不是您本人的账号，信息有误。
4.如登录角色未进行注册，则直接跳转至福建专技平台注册页进行注册
   */
  type: string
  /**
   * 提示信息
   */
  TipMessage: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

export class FJZJStudentSchemeLearningResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<FJZJStudentSchemeLearningResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 生成华医网学员/报名信息请求内容
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async encryptedContent(
    request: CreateSecretSignUpInfoRequest,
    query: DocumentNode = GraphqlImporter.encryptedContent,
    operation?: string
  ): Promise<Response<EncryptedContentResponse>> {
    return commonRequestApi<EncryptedContentResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取我的培训方案学习列表
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSchemeLearningInMyself(
    params: { page?: Page; request?: StudentSchemeLearningRequest; sort?: Array<StudentSchemeLearningSortRequest> },
    query: DocumentNode = GraphqlImporter.pageSchemeLearningInMyself,
    operation?: string
  ): Promise<Response<FJZJStudentSchemeLearningResponsePage>> {
    return commonRequestApi<FJZJStudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-分页获取当前服务商管辖地区下的学员培训方案学习列表
   * @param page
   * @param request
   * @param sort
   * @param dataFetchingEnvironment
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentSchemeLearningInDistributor(
    params: { page?: Page; request?: StudentSchemeLearningRequest; sort?: Array<StudentSchemeLearningSortRequest> },
    query: DocumentNode = GraphqlImporter.pageStudentSchemeLearningInDistributor,
    operation?: string
  ): Promise<Response<FJZJStudentSchemeLearningResponsePage>> {
    return commonRequestApi<FJZJStudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取当前服务商管辖地区下的学员培训方案学习列表
   * @param page
   * @param request
   * @param dataFetchingEnvironment
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentSchemeLearningInServicerV2(
    params: { page?: Page; request?: StudentSchemeLearningRequest },
    query: DocumentNode = GraphqlImporter.pageStudentSchemeLearningInServicerV2,
    operation?: string
  ): Promise<Response<FJZJStudentSchemeLearningResponsePage>> {
    return commonRequestApi<FJZJStudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 验证华医网是否存在学员信息
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validStudentInfo(
    request: ValidHymStudentInfoRequest,
    query: DocumentNode = GraphqlImporter.validStudentInfo,
    operation?: string
  ): Promise<Response<ValidHymStudentInfoResponse>> {
    return commonRequestApi<ValidHymStudentInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员自主下华医网订单
   * @return 订单创建序列号
   * @param mutate 查询 graphql 语法文档
   * @param createOrderInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createHYWOrder(
    createOrderInfo: CreateHYWOrderRequest,
    mutate: DocumentNode = GraphqlImporter.createHYWOrder,
    operation?: string
  ): Promise<Response<CreateHYWOrderResultResponse>> {
    return commonRequestApi<CreateHYWOrderResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createOrderInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 注册学员并报班
   * @param mutate 查询 graphql 语法文档
   * @param signUpRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createStudentAndPlaceOrder(
    signUpRequest: CreateStudentAndPlaceOrderRequest,
    mutate: DocumentNode = GraphqlImporter.createStudentAndPlaceOrder,
    operation?: string
  ): Promise<Response<CreateStudentAndPlaceOrderResponse>> {
    return commonRequestApi<CreateStudentAndPlaceOrderResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { signUpRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: true
      })
    )
  }

  /**   * 推送学员报名信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async syncStudentSignUpForApp(
    request: SyncStudentSignUpForAppRequest,
    mutate: DocumentNode = GraphqlImporter.syncStudentSignUpForApp,
    operation?: string
  ): Promise<Response<SyncStudentSignUpForAppResponse>> {
    return commonRequestApi<SyncStudentSignUpForAppResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 推送学员报名信息-学员未注册
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async syncStudentSignUpUnregistered(
    request: SyncStudentSignUpRegisteredRequest,
    mutate: DocumentNode = GraphqlImporter.syncStudentSignUpUnregistered,
    operation?: string
  ): Promise<Response<SyncStudentSignUpForAppResponse>> {
    return commonRequestApi<SyncStudentSignUpForAppResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
