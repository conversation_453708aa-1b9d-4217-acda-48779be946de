import { QuestionnaireAppliedRangeTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'

/**
 * @description 占位符常量
 * 方案JSON中，某些id创建时需要占位符，此处统一管理
 */
class PlaceholderConstant {
  // 培训方案ID
  static readonly schemeID = 'schemeId.UUID$schemeId'
  // 选课规则-学习方式ID
  static readonly chooseCourseLearningID = 'learningId.UUID$chooseCo'
  static readonly chooseCourseLearningAssessSettingID = 'assessId.UUID$chooseAs'
  static readonly chooseCourseLearningAssessSettingNAME = 'Course_Assess_001'
  static readonly chooseCourseLearningConfigID = 'resourceId.UUID$chooseCi'
  //用给课程大纲 还需要后面6位数
  static readonly courseTrainingOutlinesPrefix = 'resourceId.UUID$co'
  static readonly courseTrainingOutlinesAssessPrefix = 'assessId.UUID$co'
  static readonly courseTrainingOutlinesAssessName = 'Course_Assess_003'
  static readonly NOCourseTrainingOutlinesId = 'resourceId.UUID$noChildO'
  static readonly NOCourseTrainingOutlinesName = 'NOCourseTrainingOutlinesName'

  static readonly chooseCourseRuleId = 'resourceId.UUID$CourseRu'
  // 选课规则-按具体课程选课
  static readonly chooseCourseRuleName = 'CompulsoryAndElectivePeriodRule'
  static readonly chooseCourseRuleByCourseCategoryName = 'CompulsoryAndElectivePeriodCourseCategoryRule'
  static readonly courseQuizConfigId = 'resourceId.UUID$QuizCoId'
  static readonly courseQuizConfigQuizConfigId = 'resourceId.UUID$QuZCifID'
  static readonly courseQuizConfigQuizConfigNAME = '课后测验'
  //自主选课
  static readonly autonomousCourseLearningId = 'learningId.UUID$autoNoId'
  static readonly autonomousCourseLearningAssessSettingId = 'assessId.UUID$autoNoAs'
  static readonly autonomousCourseLearningAssessSettingNAME = 'Course_Assess_002'
  static readonly autonomousCourseLearningConfigID = 'resourceId.UUID$autoCoCi'

  //考核id数组
  static autonomousCourseLearningAssessSettingRelateAssessIds: string[] = []

  //兴趣课
  static readonly interestCourseLearningId = 'learningId.UUID$interCId'
  static readonly interestCourseConfigId = 'resourceId.UUID$interGId'
  //还需要后面6位数
  static readonly interestCourseTrainingOutlinesPrefix = 'resourceId.UUID$in'

  //考试
  static readonly examLearningId = 'learningId.UUID$examLeId'
  static readonly examLearningPreconditionId = 'preconditionId.UUID$examPrId'
  static readonly examLearningChooseCoursePreconditionName = 'Exam_Precondition_001'
  static readonly examLearningAutoCoursePreconditionName = 'Exam_Precondition_005'
  static readonly examLearningConfigId = 'learningId.UUID$examConId'
  static readonly examLearningAssessId = 'assessId.UUID$examAsId'
  static readonly examLearningAssessName = 'Exam_Assess_001'
  //练习
  static readonly practiceLearningId = 'learningId.UUID$praLeaId'
  //练习
  static readonly practiceConfigId = 'learningId.UUID$praCofId'
  //商品
  static readonly commoditySaleId = 'commoditySkuId.UUID$comSalId'
  static readonly commoditySaleCategoryId = 'categoryId.ApolloKey'
  // 班级考核
  static readonly schemeAssessSettingId = 'assessId.UUID$scAsesId'
  static readonly schemeAssessSettingName = 'Scheme_Assess_001'

  // 培训方案-考核项-学习成果-学时成果id
  static readonly schemeAssessSettingLearningResultsCreditId = 'resultId.UUID$creditId'
  // 培训方案-考核项-学习成果-证明成果id
  static readonly schemeAssessSettingLearningResultsTemplateId = 'resultId.UUID$temPlaId'
  // 期别-考核项-学习成果-学时成果id
  static readonly issueAssessSettingLearningResultsPeriodId = 'resultId.UUID$periodId'

  // 学习心得
  static readonly schemeLearningExperienceLearningId = 'learningId.UUID$expLeaId'
  static readonly learningExperienceLearningSettingId = 'assessId.UUID$expLeaId'
  static readonly learningExperienceLearningAssessSettingName = 'Learning_Activity_Assess_001'
  static readonly schemeLearningExperienceConfigId = 'resourceId.UUID$ex'
  // 期别
  static readonly issueConfigureId = 'issueId.UUID$issueId'
  static readonly issueConfigureAssessSettingId = 'assessId.UUID$ia'
  // 期别结业测试考核项名称
  static readonly issueConfigureExamAssessSettingName = 'Exam_Assess_006'
  static readonly issueConfigureIssueAssessSettingName = 'Issue_Assess_001'
  static readonly issueConfigureTrainingConfigConfigureId = 'configId.UUID$tcc'
  // 期别考核项(当前时间>=期别培训结束时间)
  static readonly issueConfigureIssueAssessSettingTime = 'Issue_Assess_002'

  static readonly questionnaireChooseCoursePreconditionName = 'Questionnaire_Precondition_001'
  static readonly questionnaireAutonomousCourseLearningPreconditionName = 'Questionnaire_Precondition_002'
  static readonly questionnaireIssueLearningPreconditionName = 'Questionnaire_Precondition_003'
  // static readonly preconditionId = 'preconditionId.UUID$'

  // 问卷
  static readonly questionnaireLearningId = 'learningId.UUID$ql'
  static readonly questionnaireLearningPreconditionId = 'preconditionId.UUID$qlp'

  static readonly questionnaireLearningAssessSettingId = 'assessId.UUID$qla'
  static readonly questionnaireLearningAssessSettingName = 'Questionary_Assess_001'
  static readonly questionnaireLearningConfigId = 'resourceId.UUID$qlr'

  // 教学计划
  static readonly teachPlanLearningId = 'learningId.UUID$tpl'
  static readonly teachPlanLearningResourceId = 'resourceId.UUID$tpr'
  static readonly teachPlanLearningAssessSettingId = 'assessId.UUID$tpla'
  static readonly teachPlanLearningAssessSettingName = 'PLAN_ASSESS_003'
  // 教学计划项教师扩展信息-教师职称key
  static readonly teachingPlanItemTeacherProfessionalTitle = 'PROFESSIONAL_TITLE'
  static readonly teachingPlanItemTeacherUnitName = 'UNIT_NAME'
  // 教学计划扩展信息
  static readonly teachingPlanHeadTeacherName = 'headTeacherName'
  static readonly teachingPlanHeadTeacherPhone = 'headTeacherPhone'
  static readonly teachingPlanHotelLiaisonName = 'hotelLiaisonName'
  static readonly teachingPlanHotelLiaisonPhone = 'hotelLiaisonPhone'
  static readonly teachingPlanAddressTrainingPointId = 'trainingPointId'

  // 教学计划组
  // 教学计划组id
  static readonly teachingPlanItemsGroupId = 'planId.UUID$PG'
  // 教学计划项id
  static readonly teachingPlanItemId = 'resourceId.UUID$tpi'
  // 教学计划配置id
  static readonly teachPlanResourceId = 'resourceId.UUID$tp'

  // 考核项
  static readonly assessSettingId = 'assessId.UUID$scass'
  static readonly notRootAssessAssessSettingName = 'Not_Root_Assess_001'

  // 默认时间
  static readonly defaultBeginTime = '1900-01-01 00:00:00'
  static readonly defaultEndTime = '2100-01-01 00:00:00'

  // momentJS 年月日时间格式
  static readonly momentYearMonthDayFormat = 'YYYY-MM-DD'
  // momentJS 时分秒时间格式
  static readonly momentHourMinuteSecondFormat = 'HH:mm:ss'
  // momentJS 完整时间格式
  static readonly momentFullTimeFormat = 'YYYY-MM-DD HH:mm:ss'
  // momentJS 完整时间格式-开始时间
  static readonly momentFullStartTimeFormat = 'YYYY-MM-DD 00:00:00'
  // momentJS 完整时间格式-结束时间
  static readonly momentFullEndTimeFormat = 'YYYY-MM-DD 23:59:59'

  // 扩展属性
  // 扩展属性-培训形式
  static readonly extendPropertyTrainingWay = 'trainingWay'

  //期别学习方式-扩展属性-全期别唯一标识-name
  static readonly questionnaireExtendPropertyUniqueKeyName = 'uniqueKey'
  //期别学习方式-扩展属性-全期别唯一标识-value
  static readonly questionnaireExtendPropertyUniqueKeyValue = 'resourceId.UUID$uk'

  /**
   * 方案问卷范围
   */
  static readonly schemeQuestionnaireRange = [
    QuestionnaireAppliedRangeTypeEnum.scheme,
    QuestionnaireAppliedRangeTypeEnum.online_course
  ]
  /**
   * 期别问卷范围
   */
  static readonly issueQuestionnaireRange = [
    QuestionnaireAppliedRangeTypeEnum.assign_issue,
    QuestionnaireAppliedRangeTypeEnum.per_issue
  ]
}

export default PlaceholderConstant
