import { InvoiceStatusEnum } from '@api/service/customer/trade/single/invoice/enum/InvoiceEnum'
import { InvoiceApplyInfoResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { OfflineInvoiceDeliveryInfoResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import {
  InvoiceCategoryEnum,
  InvoiceIdentificationEnum,
  InvoiceMethodEnum
} from '@api/service/customer/trade/single/enum/InvoiceEnum'

export default class OrderInvoiceApplyInfoResponseVo extends InvoiceApplyInfoResponse {
  //开票金额
  blueTotalAmount = 0

  //开票金额
  totalAmount = 0
  /**
   * 开票时间
   */
  invoiceDate = ''
  /**
   * 发票状态 0:未开具 1：开票中 2：开票成功 3：开票失败4 冻结中
   */
  invoiceStatus?: InvoiceStatusEnum
  //发票号
  orderNum = ''
  /**
   * 蓝票下载地址
   */
  blueFilePath: string
  /**
   * 蓝票下载地址 XML
   */
  blueFileXmlPath: string
  /**
   * 蓝票下载地址 OFD
   */
  blueFileOfdPath: string
  /**
   * 发票配送信息
   */
  deliveryInfo: OfflineInvoiceDeliveryInfoResponse

  /**
   * 发票类型
   */
  get invoiceIdentification(): InvoiceIdentificationEnum {
    // 发票类型应由三个字段去定位 （invoiceCategory,invoiceType,invoiceMethod）
    // 但目前只有这几种发票类型，为不影响旧的发票类型判断进行判断简化
    // 若后续新增发票类型则判断需重新梳理
    switch (this.invoiceCategory) {
      case InvoiceCategoryEnum.PLAININVOICE:
        return InvoiceIdentificationEnum.PLAIN_INVOICE
      case InvoiceCategoryEnum.VATPLAININVOICE:
        return InvoiceIdentificationEnum.VAT_PLAIN_INVOICE
      case InvoiceCategoryEnum.VATSPECIALPLAININVOICE:
        if (this.invoiceType === InvoiceMethodEnum.ELECT) {
          return InvoiceIdentificationEnum.VAT_SPECIAL_ELECT_PLAIN_INVOICE
        } else {
          return InvoiceIdentificationEnum.VAT_SPECIAL_PLAIN_INVOICE
        }
      default:
        return null
    }
  }
}
