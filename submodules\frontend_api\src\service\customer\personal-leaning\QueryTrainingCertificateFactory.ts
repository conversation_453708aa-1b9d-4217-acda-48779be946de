import QueryTrainingCertificateList from '@api/service/customer/personal-leaning/query/QueryTrainingCertificateList'
import QueryTrainingArchivesList from '@api/service/customer/personal-leaning/query/QueryTrainingArchivesList'
import QueryCertificateTemplate from './query/QueryCertificateTemplate'
import QueryTrainingDetail from './query/QueryTrainingDetail'
import QueryHistoryTrainingArchivesList from './query/QueryHistoryTrainingArchivesList'

/*
  查询工厂类
*/
class TrainingCertificateFactory {
  // 培训证明模板实例
  get certificateTemplate() {
    return new QueryCertificateTemplate()
  }

  // 培训证明详情
  get trainingDetail() {
    return new QueryTrainingDetail()
  }

  // 培训证明列表实例
  get trainingCertificateList() {
    return new QueryTrainingCertificateList()
  }

  // 网校域学习档案列表实例
  get trainingArchivesList() {
    return new QueryTrainingArchivesList()
  }
  // 历史培训证明列表实例
  get historyTrainingArchivesList() {
    return new QueryHistoryTrainingArchivesList()
  }
}

export default new TrainingCertificateFactory()
