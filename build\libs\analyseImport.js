const fs = require('fs')
const path = require('path')
const babelParser = require('@babel/parser')
const traverse = require('@babel/traverse').default
const { glob } = require('glob') // 确保使用 glob v8
const { parse: parseSFC } = require('@vue/compiler-sfc')

// 配置根目录和需要检查的目录
const ROOT_DIR = process.cwd() // 当前工作目录
const PACKAGES_DIR = path.join(ROOT_DIR, 'packages')
const SRC_DIR = path.join(ROOT_DIR, 'src')
const ROUTERS_SRC_DIR = path.join(PACKAGES_DIR, 'routers', 'src')

// 存储不合规的 import 信息
const nonCompliantImportsMap = new Map()
const crossSchoolImportsMap = new Map()

// 配置 Babel 解析插件
const babelPlugins = [
  'jsx',
  'typescript',
  'classProperties', // 如果使用类属性
  'decorators-legacy' // 启用 legacy 装饰器语法
]

// 1. 解析 `packages/routers/src` 目录，收集网校名称
function getSchoolNames() {
  const excludedDirs = ['basic-school', 'basic-router']
  let schoolNames = new Set()

  if (!fs.existsSync(ROUTERS_SRC_DIR)) {
    console.error(`目录不存在: ${ROUTERS_SRC_DIR}`)
    return Array.from(schoolNames)
  }

  const dirs = fs
    .readdirSync(ROUTERS_SRC_DIR, { withFileTypes: true })
    .filter((dirent) => dirent.isDirectory() && !excludedDirs.includes(dirent.name))
    .map((dirent) => dirent.name)

  dirs.forEach((school) => {
    schoolNames.add(school)
  })

  console.log('收集到的网校名称:', Array.from(schoolNames))
  return Array.from(schoolNames)
}

// 2. 使用 glob 查找所有 .vue 和 .ts 文件
async function findFiles() {
  try {
    const vueFiles = await glob(`${PACKAGES_DIR}/**/*.vue`, { nodir: true })
    const tsFiles = await glob(`${PACKAGES_DIR}/**/*.ts`, { nodir: true })
    // const srcVueFiles = await glob(`${SRC_DIR}/**/*.vue`, { nodir: true })
    // const srcTsFiles = await glob(`${SRC_DIR}/**/*.ts`, { nodir: true })
    // return [...vueFiles, ...srcVueFiles, ...tsFiles, ...srcTsFiles]
    return [...vueFiles, ...tsFiles]
  } catch (err) {
    console.error('Error while searching for files:', err)
    return []
  }
}

// 3. 确定文件所属的网校
function getSchoolFromFile(filePath, schoolNames) {
  // 假设网校目录结构为 packages/<module>/src/diff/<school>/
  const normalizedPath = path.normalize(filePath)
  const parts = normalizedPath.split(path.sep)
  const diffIndex = parts.indexOf('diff')

  if (diffIndex !== -1 && parts.length > diffIndex + 1) {
    const school = parts[diffIndex + 1]
    if (schoolNames.includes(school)) {
      return school
    }
  }

  return null
}

// 4. 检查跨网校的 `import` 语句
function isCrossSchoolImport(importPath, currentSchool, schoolNames) {
  // 使用路径分割的方法来查找 'diff/<other_school>'
  const pathParts = importPath.split(/[\\/]/)
  for (let i = 0; i < pathParts.length; i++) {
    if (pathParts[i] === 'diff' && i + 1 < pathParts.length) {
      const importedSchool = pathParts[i + 1]
      if (schoolNames.includes(importedSchool) && importedSchool !== currentSchool) {
        return importedSchool
      }
    }
  }
  return null
}

// 5. 主检查函数
async function checkImports() {
  try {
    // 获取网校名称
    const schoolNames = getSchoolNames()
    if (schoolNames.length === 0) {
      console.warn('未找到任何网校目录，脚本将继续执行但不进行跨网校 import 检查。')
    }

    const files = await findFiles()
    console.log(`找到 ${files.length} 个文件进行检查。`) // 调试日志

    // 遍历所有文件
    const fileChecks = files.map(async (file) => {
      let fileContent = ''

      if (file.endsWith('.vue')) {
        fileContent = fs.readFileSync(file, 'utf-8')

        // 使用 @vue/compiler-sfc 提取 <script> 内容
        const sfc = parseSFC(fileContent)
        if (!sfc.descriptor.script && !sfc.descriptor.scriptSetup) {
          // 如果没有 <script> 标签，跳过文件
          return
        }

        // 合并 <script> 内容
        const scripts = []
        if (sfc.descriptor.script && sfc.descriptor.script.content) {
          scripts.push(sfc.descriptor.script.content)
        }
        if (sfc.descriptor.scriptSetup && sfc.descriptor.scriptSetup.content) {
          scripts.push(sfc.descriptor.scriptSetup.content)
        }

        fileContent = scripts.join('\n')
      } else if (file.endsWith('.ts')) {
        fileContent = fs.readFileSync(file, 'utf-8')
      } else {
        // 其他文件类型，跳过
        return
      }

      // 解析代码，生成 AST
      let ast
      try {
        ast = babelParser.parse(fileContent, {
          sourceType: 'module',
          plugins: babelPlugins
        })
      } catch (parseError) {
        console.error(`Error parsing ${file}: ${parseError.message}`)
        return
      }

      // 确定文件所属的网校
      const currentSchool = getSchoolFromFile(file, schoolNames)

      // 遍历 AST，查找 import 语句
      traverse(ast, {
        ImportDeclaration({ node }) {
          const importPath = node.source.value

          // 检查是否以 '@/packages' 开头或使用相对路径
          const startsWithAtPackages = importPath.startsWith('@packages')
          const isRelativePath = importPath.startsWith('./') || importPath.startsWith('../')

          if (startsWithAtPackages || isRelativePath) {
            // 聚合规范不符合的 import
            if (!nonCompliantImportsMap.has(file)) {
              nonCompliantImportsMap.set(file, new Map())
            }
            const importPathMap = nonCompliantImportsMap.get(file)
            if (!importPathMap.has(importPath)) {
              importPathMap.set(importPath, new Set())
            }
            const importNamesSet = importPathMap.get(importPath)
            node.specifiers.forEach((spec) => {
              importNamesSet.add(spec.local.name)
            })
          }

          // 如果文件属于某个网校，检查是否跨网校 import
          if (currentSchool && schoolNames.length > 0) {
            const crossSchool = isCrossSchoolImport(importPath, currentSchool, schoolNames)
            if (crossSchool) {
              // 聚合跨网校的 import
              if (!crossSchoolImportsMap.has(file)) {
                crossSchoolImportsMap.set(file, new Map())
              }
              const importPathMap = crossSchoolImportsMap.get(file)
              if (!importPathMap.has(importPath)) {
                importPathMap.set(importPath, {
                  importNames: new Set(),
                  fromSchool: currentSchool,
                  toSchool: crossSchool
                })
              }
              const importInfo = importPathMap.get(importPath)
              node.specifiers.forEach((spec) => {
                importInfo.importNames.add(spec.local.name)
              })
            }
          }
        }
      })
    })

    await Promise.all(fileChecks)

    // 输出结果
    if (nonCompliantImportsMap.size === 0 && crossSchoolImportsMap.size === 0) {
      console.log('所有 import 语句均符合规范，没有发现不合规的 import。')
    } else {
      let outputContent = ''

      // 处理跨网校的 import
      if (crossSchoolImportsMap.size > 0) {
        console.log('发现跨网校的 import 语句如下：\n')
        outputContent += '### 跨网校的 import 语句\n\n'
        crossSchoolImportsMap.forEach((importPathMap, file) => {
          console.log(`文件: ${file}`)
          importPathMap.forEach((importInfo, importPath) => {
            const namesArray = Array.from(importInfo.importNames)
            console.log(`  import 路径: ${importPath}`)
            console.log(`    import 名称: ${namesArray.join(', ')}`)
            console.log(`    来源网校: ${importInfo.fromSchool}`)
            console.log(`    目标网校: ${importInfo.toSchool}`)
          })
          console.log('') // 空行分隔不同文件

          // 添加到输出内容
          outputContent += `文件: ${file}\n`
          importPathMap.forEach((importInfo, importPath) => {
            const namesArray = Array.from(importInfo.importNames)
            outputContent += `  import 路径: ${importPath}\n`
            outputContent += `    import 名称: ${namesArray.join(', ')}\n`
            outputContent += `    来源网校: ${importInfo.fromSchool}\n`
            outputContent += `    目标网校: ${importInfo.toSchool}\n`
          })
          outputContent += `\n`
        })

        outputContent += '\n'
      }

      // 处理规范不符合的 import
      if (nonCompliantImportsMap.size > 0) {
        console.log('发现规范不符合的 import 语句如下：\n')
        outputContent += '### 规范不符合的 import 语句\n\n'
        nonCompliantImportsMap.forEach((importPathMap, file) => {
          console.log(`文件: ${file}`)
          importPathMap.forEach((importNames, importPath) => {
            const namesArray = Array.from(importNames)
            console.log(`  import 路径: ${importPath}`)
            console.log(`    import 名称: ${namesArray.join(', ')}`)
          })
          console.log('') // 空行分隔不同文件

          // 添加到输出内容
          outputContent += `文件: ${file}\n`
          importPathMap.forEach((importNames, importPath) => {
            const namesArray = Array.from(importNames)
            outputContent += `  import 路径: ${importPath}\n`
            outputContent += `    import 名称: ${namesArray.join(', ')}\n`
          })
          outputContent += `\n`
        })
      }

      // 将结果保存到文件
      const outputPath = path.join(ROOT_DIR, 'import-check-results.txt')
      fs.writeFileSync(outputPath, outputContent, 'utf-8')
      console.log(`\n详细结果已保存到 ${outputPath}`)
    }
  } catch (error) {
    console.error('运行脚本时发生错误:', error)
  }
}

// 执行检查
checkImports()
