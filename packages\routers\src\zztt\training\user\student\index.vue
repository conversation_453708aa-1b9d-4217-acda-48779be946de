<route-meta>
{
"isMenu": true,
"title": "学员管理",
"sort": 1,
"icon": "icon_guanli"
}
</route-meta>
<script lang="ts">
  import Student from '@hbfe/jxjy-admin-user/src/diff/zztt/student/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  // 引入的角色以角色菜单为主 这里用子项目管理员做例子
  import { WXGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    //标注的安全对象的key值 : [引入的角色]
    participantsManagement: [WXGLY],
    detali: [WXGLY],
    queryTask: [WXGLY]
  })
  export default class extends Student {}
</script>
