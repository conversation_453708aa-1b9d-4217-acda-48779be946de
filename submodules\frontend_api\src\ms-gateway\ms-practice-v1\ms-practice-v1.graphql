"""独立部署的微服务,K8S服务名:ms-practice-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
type Query {
	getSchemaName:String
}
type Mutation {
	"""练习申请
		@param studentToken
		@param requireQuestionCount
		@return token字符串
	"""
	applyPractice(studentToken:String,requireQuestionCount:Int):String
}

scalar List
