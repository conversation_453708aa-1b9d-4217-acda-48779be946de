import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 换班状态枚举
 *
 *
 */
export enum ExchangeTrainClassStatusEnum {
  // 1：换班中
  Exchanging = 1,
  // 2：换班成功
  Complete_Exchange = 2,
  // 3：取消换班
  Cancle_Exchange = 3
}

class ExchangeTrainClassStatusType extends AbstractEnum<ExchangeTrainClassStatusEnum> {
  static enum = ExchangeTrainClassStatusEnum
  constructor(status?: ExchangeTrainClassStatusEnum) {
    super()
    this.current = status
    this.map.set(ExchangeTrainClassStatusEnum.Exchanging, '换班中')
    this.map.set(ExchangeTrainClassStatusEnum.Complete_Exchange, '换班成功')
    this.map.set(ExchangeTrainClassStatusEnum.Cancle_Exchange, '取消换班')
  }
}

export default new ExchangeTrainClassStatusType()
