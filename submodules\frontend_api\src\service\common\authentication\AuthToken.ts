import BizAuthToken from '@hbfe-biz/biz-authentication/dist/AuthToken'
import Authentication from '@api/service/common/authentication/Authentication'
import basicDataDomain from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

export default class AuthToken extends BizAuthToken {
  constructor(context: Authentication) {
    super(context)
    this.context = context
  }

  context: Authentication // * 上下文

  /**
   * 申请微信开放平台账户ID登陆token
   */
  async applyLoginByOpenId(openId: string) {
    const res = await basicDataDomain.applyLoginByOpenId({
      token: ConfigCenterModule.getFrontendApplication(frontendApplication.wxOpenIdToken),
      roleCategory: 1,
      openId
    })
    if (res.data.code == '200') {
      await this.context.loginWithWxH5Token(res.data.token)
    }
    // 调用单点登录 /rest/ms2/accountid/login
    return res.data.code
  }

  /**
   * 申请绑定微信开放平台并返回账户ID登陆token
   */
  async applyBindWeChatOpenPlatformAndValidLogin(account: string, password: string, openId: string, nickname: string) {
    const res = await basicDataDomain.applyBindWeChatOpenPlatformAndValidLogin({
      token: ConfigCenterModule.getFrontendApplication(frontendApplication.wxOpenIdToken),
      roleCategory: 1,
      account,
      password,
      openId,
      nickname
    })
    // if (res.data.code == '200') {
    //   await this.context.loginWithWxH5Token(res.data.token)
    // }
    return res
  }
}
