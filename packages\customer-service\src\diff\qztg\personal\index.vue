<script lang="ts">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import BasicData from '@hbfe/jxjy-admin-customerService/src/diff/qztg/personal/components/basic-data.vue'
  import ChangeClass from '@hbfe/jxjy-admin-customerService/src/diff/qztg/personal/components/change-class.vue'
  import InvoiceInfo from '@hbfe/jxjy-admin-customerService/src/diff/qztg/personal/components/invoice-info.vue'
  import OrderInfo from '@hbfe/jxjy-admin-customerService/src/diff/qztg/personal/components/order-info.vue'
  import RefundInfo from '@hbfe/jxjy-admin-customerService/src/diff/qztg/personal/components/refund-info.vue'
  import StudyContent from '@hbfe/jxjy-admin-customerService/src/diff/qztg/personal/components/study-content.vue'
  import StudyRecords from '@hbfe/jxjy-admin-customerService/src/diff/qztg/personal/components/study-records.vue'
  import Personal from '@hbfe/jxjy-admin-customerService/src/personal/index.vue'

  @Component({
    components: { BasicData, ChangeClass, InvoiceInfo, OrderInfo, RefundInfo, StudyContent, StudyRecords }
  })
  export default class extends Personal {}
</script>
