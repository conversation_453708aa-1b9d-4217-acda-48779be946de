<route-meta>
{ "isMenu": true, "title": "课程包管理", "sort": 1, "icon": "icon-dabao" }
</route-meta>
<script lang="ts">
  import CoursePackageIndex from '@hbfe/jxjy-admin-coursePackage/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY],
    create: [WXGLY],
    import: [WXGLY],
    detail: [WXGLY],
    async: [WXGLY],
    modify: [WXGLY],
    remove: [WXGLY],
    copy: [WXGLY]
  })
  export default class extends CoursePackageIndex {}
</script>
