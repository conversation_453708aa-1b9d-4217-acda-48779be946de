import store from '@api/store'
import { VuexModule, getModule, Module } from 'vuex-module-decorators'
import QueryCourse from '@api/service/customer/course/query/QueryCourse'
import QueryMyInterestCourse from '@api/service/customer/course/query/QueryMyInterestCourse'
import QueryMyAutonomousCourse from '@api/service/customer/course/query/QueryMyAutonomousCourse'
import QueryMyChooseCourseRuleCourse from '@api/service/customer/course/query/QueryMyChooseCourseRuleCourse'

@Module({ namespaced: true, name: 'CustomerQueryCourseModule', dynamic: true, store })
class CustomerQueryCourseModule extends VuexModule {
  queryCourse: QueryCourse = new QueryCourse()

  /**
   * 获取查询我的兴趣课
   */
  get queryMyInterestCourse() {
    return (studentNo: string) => {
      return new QueryMyInterestCourse(studentNo)
    }
  }

  /**
   * 获取查询我的选课规则课程实例
   */
  get queryMyChooseCourseRuleCourse() {
    return (studentNo: string) => {
      return new QueryMyChooseCourseRuleCourse(studentNo)
    }
  }

  /**
   * 获取查询我的自主选课课程实例
   */
  get queryMyAutonomousCourse() {
    return (studentNo: string) => {
      return new QueryMyAutonomousCourse(studentNo)
    }
  }
}

export default getModule(CustomerQueryCourseModule)
