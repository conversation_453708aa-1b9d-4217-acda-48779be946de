import LearningResulType from '@api/service/management/intelligence-learning/enum/LearningResultEnum'
import { CourseDetail } from '@api/ms-gateway/ms-autolearning-log-v1'

export default class CourseLogItem {
  /**
   * 课程id
   */
  courseId = ''
  /**
   * 课程名称
   */
  courseName = ''
  /**
   * 学习开始时间
   */
  startTime = ''
  /**
   * 学习结束时间
   */
  endTime = ''
  /**
   * 完成进度
   */
  learningSchedule = ''
  /**
   * 学习完成情况
   */
  learningResult = new LearningResulType()
  /**
   * 测验开始时间
   */
  quizStartTime = ''
  /**
   * 测验结束时间
   */
  quizEndTime = ''
  /**
   * 测验完成情况
   */
  quizResult = new LearningResulType()
  /**
   * 智能学习测验分数（已完成有值）
   */
  quizScore: number = null

  static from(dto: CourseDetail) {
    const vo = new CourseLogItem()
    const { courseInfo, courseQuizInfo } = dto
    if (courseInfo) {
      vo.courseId = courseInfo.courseId
      vo.startTime = courseInfo.studyStartTime
      vo.endTime = courseInfo.studyEndTime
      vo.learningSchedule = Math.min(courseInfo.completionProgress, 100) + '%'
      vo.learningResult = new LearningResulType(courseInfo.completeStatus)
    }
    if (courseQuizInfo) {
      if (!vo.courseId) vo.courseId = courseQuizInfo.courseId
      vo.quizEndTime = courseQuizInfo.answerEndTime
      vo.quizStartTime = courseQuizInfo.answerStartTime
      vo.quizScore = courseQuizInfo.quizScore
      vo.quizResult = new LearningResulType(courseQuizInfo.quizCompleteStatus)
    }
    return vo
  }
}
