import roleGateWay, { RoleEditDto, SecurityObjectGroupDto } from '@api/ms-gateway/ms-role-v1'
export class QuerySecurityGroupForEdidRole {
  /*
   *  根据roleId获取安全对象组(编辑使用)
   * */
  async getPermissionForEditRole(roleId: string) {
    try {
      console.log('roleId参数=', roleId)
      const res = await roleGateWay.getPermissionForEditRole(roleId)
      let RoleEditDtoModel = new RoleEditDto()

      if (res.status.isSuccess()) {
        RoleEditDtoModel = res.data
      }

      console.log('调用了getPermissionForEditRole方法，返回值=', RoleEditDtoModel)
      return RoleEditDtoModel
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/query/QuerySecurityGroupForEdidRole.ts所处方法，getPermissionForEditRole',
        e
      )
    }
  }
}
