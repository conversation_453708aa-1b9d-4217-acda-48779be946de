<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import EditSpecialInvoiceDialog from '@hbfe/jxjy-admin-trade/src/diff/qztg/invoice/personal/components/edit-special-invoice-dialog.vue'
  import invoiceIncrement from '@hbfe/jxjy-admin-customerService/src/personal/components/invoice-increment.vue'

  @Component({
    components: {
      EditSpecialInvoiceDialog
    }
  })
  export default class extends invoiceIncrement {}
</script>
