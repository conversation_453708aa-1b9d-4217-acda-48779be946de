<route-meta>
{
"title": "资讯分类选择器"
}
</route-meta>
<template>
  <PopTree
    placeholder="请选择资讯分类"
    :default-value="defaultNode"
    v-model="selectNode"
    :load-data="load"
    size="small"
    :multiple="false"
    ref="popTreeRef"
    :clearable="true"
    :close-after-select="true"
  ></PopTree>
</template>

<script lang="ts">
  import { Component, Emit, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import PopTree from '@hbfe-vue-components/pop-tree'
  import { InfoContentCategoryUI } from '@hbfe/jxjy-admin-components/src/models/InfoContentCategoryUI'
  import InfoContentModule from '@api/service/management/info-content/InfoContentModule'

  @Component({
    components: {
      PopTree
    }
  })
  export default class PaperClassifyTree extends Vue {
    visible = false
    categoryName = ''
    currentCategoryId = ''
    selectNode = ''

    @Prop({
      type: String
    })
    value: any

    @Watch('value')
    valueChange() {
      if (!this.value) {
        this.clearChoose()
      }
    }

    @Prop({
      type: InfoContentCategoryUI,
      required: false,
      default: function () {
        return new InfoContentCategoryUI()
      }
    })
    defaultNode: InfoContentCategoryUI

    @Ref('popTreeRef')
    popTreeRef: any

    @Emit('input')
    @Watch('selectNode')
    selectNodeChange(val: string) {
      if (!val) {
        this.clearChoose()
      }
      return val
    }

    handleNodeClick(data: any) {
      this.categoryName = data.name
      this.currentCategoryId = data.id
      this.selectNodeChange(this.currentCategoryId)
      this.visible = false
    }

    clearChoose() {
      this.popTreeRef.clear()
    }

    async load(node: any, resolve: any) {
      const categoryUIList = new Array<InfoContentCategoryUI>()
      if (!node.data.id) {
        const rootCategory = InfoContentCategoryUI.createRootNode()
        categoryUIList.push(rootCategory)
        resolve(categoryUIList)
        return
      }
      const status = await InfoContentModule.listSubCategoryByParentId(node.data.id)
      if (status.isSuccess()) {
        InfoContentModule.subCategoryList.forEach((p) => {
          const category = new InfoContentCategoryUI()
          category.id = p.id
          category.name = p.name
          category.leaf = false
          categoryUIList.push(category)
        })
        resolve(categoryUIList)
      } else {
        this.$message.error('获取资讯分类失败')
      }
    }
  }
</script>
