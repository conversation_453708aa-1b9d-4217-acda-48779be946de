import BizAccount from '@hbfe-biz/biz-authentication/dist/Account'
import Authentication from '@api/service/common/authentication/Authentication'
import basicDataDomain, {
  ApplyBindWeChatOpenPlatformLoginAccountRequest,
  BindPlatformAccountRequest,
  ChangePhoneRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import Storage from '@hbfe-biz/biz-authentication/dist/common/Storage'
import LoginParams from '@hbfe-biz/biz-authentication/dist/models/LoginParams'

export default class Account extends BizAccount {
  constructor(context: Authentication) {
    super()
    this.context = context
  }

  static rememberLoginInfoKeyName = 'Login-Info'
  context: Authentication
  rememberLoginInfo = new LoginParams() // * 缓存登录信息

  /**
   * * 换绑手机-换班新手机student
   */
  async studentChangeNewPhone(oldToken: string, newToken: string) {
    const changePhoneRequest = new ChangePhoneRequest()
    changePhoneRequest.newPhoneToken = newToken
    changePhoneRequest.oldPhoneToken = oldToken
    return await basicDataDomain.changePhone(changePhoneRequest)
  }

  /**
   * 根据Token绑定微信号
   */
  async bindPlatformAccount(request: BindPlatformAccountRequest) {
    request.token = this.context.verify.captchaToken
    // const { status, data } = await basicDataDomain.loginAndBindOpenPlatform(request)
    const { data } = await basicDataDomain.loginAndBindOpenPlatform(request)
    return data
  }

  /**
   * 根据Token绑定微信号 V2
   */
  async bindPlatformAccountV2(request: BindPlatformAccountRequest) {
    request.token = request.token || this.context.verify.captchaToken
    // const { status, data } = await basicDataDomain.loginAndBindOpenPlatform(request)
    const { data } = await basicDataDomain.loginAndBindOpenPlatformV2(request)
    return data
  }

  /**
   * 授权微信，无需验证码绑定微信
   * @param token
   */
  async applyBindWeChatOpenPlatformLoginAccount(token: string, changeBind: boolean, unionId: string, nickname: string) {
    //  如果未绑定微信 弹出微信授权页面
    const params = new ApplyBindWeChatOpenPlatformLoginAccountRequest()
    // 初始化token
    params.token = token
    params.changeBind = changeBind
    params.unionId = unionId
    params.nickname = nickname
    const res = await basicDataDomain.applyBindWeChatOpenPlatformLoginAccount(params)
    return res
  }

  /**
   * 换绑手机-集体报名帐号
   * @param phone 电话
   * @param smsCode 短信验证码
   */
  async updateUserBindingPhoneNumber(phone: string, smsCode: string) {
    const response = await basicDataDomain.changeCollectiveRegisterPhone({
      newPhone: phone,
      smsCode,
      token: this.context.verify.shortMessageCaptchaTicket
    })
    return response
  }

  doRememberLoginInfo(params: LoginParams) {
    if (params.longTerm) {
      this.rememberLoginInfo = params
      Storage.setItem(
        Account.rememberLoginInfoKeyName,
        JSON.stringify({
          account: params.identity,
          password: params.password,
          longTerm: true
        })
      )
    } else {
      this.rememberLoginInfo = new LoginParams()
      Storage.removeItem(Account.rememberLoginInfoKeyName)
    }
  }

  setRememberLoginInfo() {
    const loginParams = new LoginParams()
    try {
      const result = JSON.parse(Storage.getItem(Account.rememberLoginInfoKeyName) || '{}')
      loginParams.password = result.password
      loginParams.account = result.account
      loginParams.longTerm = !!(result.password && result.account)
      this.rememberLoginInfo = loginParams
    } catch (e) {
      this.rememberLoginInfo = new LoginParams()
    }
  }

  /**
   * 绑定手机号——分销商
   * @param token
   */
  async changeNewPhoneForFxs(token: string) {
    return basicDataDomain.changeNewPhoneForFxs(token)
  }
}
