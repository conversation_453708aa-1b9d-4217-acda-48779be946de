/*
 * @Author: dong<PERSON><PERSON>
 * @Date: 2023-07-24 16:43:45
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-04-26 20:48:53
 * @Description:
 */
import msOrder, { PreparePayRequest } from '@api/ms-gateway/ms-order-v1'
import { PreparePayResponseVo } from '@api/service/customer/trade/single/query/vo/PreparePayResponseVo'
import TrainClassModule from '@api/service/customer/train-class/TrainClassModule'
import { Response, ResponseStatus } from '@hbfe/common'
import { cloneDeep } from 'lodash'
import tradeQueryGateway, {
  Accommodation,
  CommoditySkuPropertyResponse,
  CommoditySkuResponse,
  OrderResponse,
  PortalCommoditySkuPropertyResponse,
  SchemeResourceResponse,
  SubOrderResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import TrainClassDetailClassVo from '@api/service/customer/train-class/query/vo/TrainClassDetailClassVo'
import SkuPropertyConvertUtils, { SchemeSkuInfo } from '@api/service/customer/train-class/Utils/SkuPropertyConvertUtils'
import SkuPropertyResponseVo from '@api/service/customer/train-class/query/vo/SkuPropertyResponseVo'
import { SchemeSkuPropertyResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import QueryTrainClassIssue, {
  BatchQuerySchemeIssueParam,
  SchemeIssueParam
} from '@api/service/customer/train-class/offlinePart/QueryTrainClassIssue'
import IssueDetail from '@api/service/customer/train-class/offlinePart/model/IssueDetail'
import TrainingPlaceInfoDto from '@api/service/customer/training-place/models/TrainingPlaceInfoDto'
import TrainingPlaceManage from '@api/service/customer/training-place/TrainingPlaceManage'

// 加载支付界面
export default class QueryPayOrderConfig {
  // 订单号
  private orderNo = ''

  // 查询期别列表实例
  private queryTrainClassIssue = new QueryTrainClassIssue()

  payModel = new PreparePayResponseVo()

  portalCommoditySkuId = ''

  /**
   * 轮询标识
   */
  polling = false

  /**
   * 订单轮询计时器
   */
  orderPollingTimeOut: any = undefined

  /**
   * 轮询次数
   */
  private pollingCount = 0

  /**
   * 轮询间隔
   */
  private pollingIntervalTime = 1000

  // order业务对象实例
  // private _orderInstance: CustomerOrderAction = undefined
  constructor(orderNo: string, portalCommoditySkuId = '') {
    this.orderNo = orderNo
    this.portalCommoditySkuId = portalCommoditySkuId
  }

  /**
   * @description: 初始化下单页面的配置。请先调用该方法。
   * @param {*}
   * @return {*}
   */

  async queryCreateOrderConfigInfo(request: PreparePayRequest, trainingChannelId?: string, isShowAll?: boolean) {
    // 加载商品信息
    // const res = await this.queryCommodity()
    // return res
    const res = await msOrder.preparePay(request)
    if (res.status.isSuccess()) {
      Object.assign(this.payModel, res.data)
      // if (this.payModel.orderInfo.subOrders?.length > 1) {
      //   res.status = await this.queryManyCommodity(trainingChannelId, isShowAll)
      // } else {
      //   res.status = await this.queryCommodity(trainingChannelId, isShowAll)
      // }
    } else {
      return res.status
    }

    this.payModel.commodityDetails = new Array<TrainClassDetailClassVo>()

    try {
      this.polling = true

      // 从订单身上取商品信息
      const orderRes = await new Promise<Response<OrderResponse>>((resolve, reject) => {
        this.pollingGetOrder(resolve, reject)
      })

      if (orderRes?.status && orderRes.status.isSuccess()) {
        const subOrderDtoList = orderRes.data.subOrderItems || new Array<SubOrderResponse>()
        const skuRequest: SchemeSkuInfo[] = []
        let skuInfos = new Array<SchemeSkuInfo>()
        subOrderDtoList.map((item) => {
          if (item.currentCommoditySku) {
            const schemeResource = item.currentCommoditySku?.resource as SchemeResourceResponse
            const skuPropertyResponse = Object.assign(
              new PortalCommoditySkuPropertyResponse(),
              new CommoditySkuPropertyResponse(),
              new SchemeSkuPropertyResponse()
            )
            skuRequest.push(
              new SchemeSkuInfo(
                schemeResource.schemeId,
                Object.assign(skuPropertyResponse, item.currentCommoditySku.skuProperty),
                item.currentCommoditySku.commoditySkuId
              )
            )
          }
        })
        // 统一转换sku
        if (skuRequest.length) {
          skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(skuRequest)
        }

        // 捞取期别信息
        const queryIssueParam = new BatchQuerySchemeIssueParam()
        subOrderDtoList.map((item) => {
          const param = new SchemeIssueParam()
          const resource = item.currentCommoditySku?.resource as SchemeResourceResponse
          const schemeId = resource?.schemeId
          const findSchemeParam = queryIssueParam.items.find((it) => it.schemeId == schemeId)
          if (findSchemeParam) {
            if (item.currentCommoditySku?.issueInfo?.issueId) {
              findSchemeParam.issueIds.push(item.currentCommoditySku.issueInfo.issueId)
            }
          } else {
            if (item.currentCommoditySku?.issueInfo?.issueId) {
              param.schemeId = schemeId
              param.issueIds.push(item.currentCommoditySku.issueInfo.issueId)
              queryIssueParam.items.push(param)
            }
          }
        })
        const issueListMap = await this.queryTrainClassIssue.batchQuerySchemeIssueMap(queryIssueParam)

        // 捞取培训点信息
        let trainingPointMap = new Map<string, TrainingPlaceInfoDto>()
        const allIssueList = Array.from(issueListMap.values()).flat()
        if (allIssueList.length) {
          let allTrainingPlaceIds = allIssueList.map((it) => {
            return it.trainingPointId
          })
          allTrainingPlaceIds = [...new Set(allTrainingPlaceIds)]

          const trainingPlaceManage = new TrainingPlaceManage()
          trainingPointMap = await trainingPlaceManage.getTrainingPlaceByIds(allTrainingPlaceIds)
        }

        this.payModel.accommodationMap = new Map<string, Accommodation>()
        // 单个商品赋值 -- 兼容旧逻辑
        if (subOrderDtoList.length == 1) {
          this.payModel.commodityDetail = this.transferSubOrderCommodityToTrainClassDetail(
            subOrderDtoList[0],
            skuInfos,
            issueListMap,
            trainingPointMap
          )

          this.payModel.accommodationMap.set(
            subOrderDtoList[0].currentCommoditySku?.commoditySkuId,
            subOrderDtoList[0].accommodation
          )
        }
        subOrderDtoList.map((subOrder) => {
          this.payModel.commodityDetails.push(
            this.transferSubOrderCommodityToTrainClassDetail(subOrder, skuInfos, issueListMap, trainingPointMap)
          )
          this.payModel.accommodationMap.set(subOrder.currentCommoditySku?.commoditySkuId, subOrder.accommodation)
        })
      }

      return orderRes.status
    } catch (e) {
      console.error(e)
      this.closeOrderPolling()
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   * 轮询获取订单信息
   * @param resolve
   * @param reject
   */
  async pollingGetOrder(resolve: (data: Response<OrderResponse>) => void, reject: (reason?: ResponseStatus) => void) {
    if (!this.orderNo) {
      return reject(null)
    }
    // 从订单身上取商品信息
    const orderRes = await tradeQueryGateway.getOrderInMyself(this.orderNo)

    if (orderRes?.data?.orderNo) {
      this.polling = false
      console.log('订单总轮询次数：', this.pollingCount)
      this.pollingCount = 0
      return resolve(orderRes)
    } else {
      this.pollingCount++
      this.orderPollingTimeOut = setTimeout(async () => {
        console.log('启动轮询，次数：', this.pollingCount)
        clearTimeout(this.orderPollingTimeOut)
        if (this.polling) {
          await this.pollingGetOrder(resolve, reject)
        } else {
          reject(null)
        }
      }, this.pollingIntervalTime)
    }
  }

  /**
   * 关闭订单获取轮询
   */
  closeOrderPolling() {
    this.polling = false
    clearTimeout(this.orderPollingTimeOut)
  }

  /**
   * 简易转化子单商品为培训商品vo （此转换为缺失的商品详情，缺失方案信息）
   * @param subOrder 子单dto
   * @param skuInfos sku
   * @private
   */
  private transferSubOrderCommodityToTrainClassDetail(
    subOrder: SubOrderResponse,
    skuInfos: Array<SchemeSkuInfo>,
    issueListMap: Map<string, IssueDetail[]>,
    trainingPointMap: Map<string, TrainingPlaceInfoDto>
  ) {
    const commodity = new TrainClassDetailClassVo()

    const commodityDto = subOrder.currentCommoditySku || new CommoditySkuResponse()
    const schemeResource = commodityDto.resource as SchemeResourceResponse

    // 这是创建订单所以来源肯定是子单
    commodity.sourceType = 1
    commodity.sourceId = subOrder.subOrderNo
    if (schemeResource.schemeType == 'chooseCourseLearning') {
      commodity.schemeType = 1
    } else if (schemeResource.schemeType == 'autonomousCourseLearning') {
      commodity.schemeType = 2
    } else if (schemeResource.schemeType == 'trainingCooperation') {
      commodity.schemeType = 3
    }
    commodity.commoditySkuId = commodityDto.commoditySkuId
    commodity.schemeId = schemeResource.schemeId
    const findSku = skuInfos.find((it) => it.id == schemeResource.schemeId)
    commodity.skuProperty = findSku?.skuName || new SkuPropertyResponseVo()
    commodity.picturePath = commodityDto.commodityPicturePath
    commodity.trainClassName = schemeResource.schemeName
    commodity.period = schemeResource.period
    commodity.price = commodityDto.price
    commodity.subOrderNo = subOrder.subOrderNo

    if (commodityDto.issueInfo?.issueId) {
      const issueList = issueListMap.get(schemeResource.schemeId)
      if (issueList) {
        const findIssue = issueList.find((it) => it.id == commodityDto.issueInfo.issueId)
        if (findIssue) {
          commodity.periodInfo.periodId = commodityDto.issueInfo.issueId
          commodity.periodInfo.periodName = commodityDto.issueInfo.issueName
          commodity.periodInfo.trainStartTime = commodityDto.issueInfo.trainStartTime
          commodity.periodInfo.trainEndTime = commodityDto.issueInfo.trainEndTime
          commodity.periodInfo.periodAddressId = findIssue.trainingPointId
          commodity.periodInfo.periodAddress = trainingPointMap.get(findIssue.trainingPointId)?.selectAddress
          commodity.accommodationInstruction = findIssue.accommodationInfoCollectNotice
          commodity.isOpenAccommodationGather = findIssue.isOpenAccommodationInfoCollect
        }
      }
    }

    return commodity
  }

  /**
   * @description: 加载商品信息
   * @param {*}
   * @return {*}
   */

  private async queryCommodity(trainingChannelId?: string, isShowAll?: boolean) {
    const queryTrainClassDetail = TrainClassModule.queryTrainClassFactory.getQueryTrainClassDetail()
    queryTrainClassDetail.commodityId = this.payModel.orderInfo.subOrders[0].skuId
    queryTrainClassDetail.portalCommoditySkuId = this.portalCommoditySkuId
    // 加载商品信息
    const status = await queryTrainClassDetail.queryTrainClassDetail(trainingChannelId, isShowAll)
    if (!status.isSuccess()) {
      return status
    }
    this.payModel.commodityDetail = queryTrainClassDetail.trainClassDetail
    // 加载机构组织代码

    return status
  }

  /**
   * @description: 加载多个商品信息
   * @param {*}
   * @return {*}
   */

  private async queryManyCommodity(trainingChannelId?: string, isShowAll?: boolean) {
    this.payModel.commodityDetails = []
    await Promise.all(
      this.payModel.orderInfo.subOrders.map(async (item) => {
        const queryTrainClassDetail = TrainClassModule.queryTrainClassFactory.getQueryTrainClassDetail()
        queryTrainClassDetail.commodityId = item.skuId
        queryTrainClassDetail.portalCommoditySkuId = item.skuId
        // 加载商品信息
        const status = await queryTrainClassDetail.queryTrainClassDetail(trainingChannelId, isShowAll)
        if (!status.isSuccess()) {
          return status
        }
        this.payModel.commodityDetails.push(cloneDeep(queryTrainClassDetail.trainClassDetail))
      })
    )
    if (this.payModel.commodityDetails?.length) {
      return new ResponseStatus(200, '加载多个商品成功')
    } else {
      return new ResponseStatus(500, '加载多个商品失败')
    }
  }

  // getMutationPay() {}
}
