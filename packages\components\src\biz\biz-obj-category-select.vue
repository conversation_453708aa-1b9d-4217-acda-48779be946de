<route-meta>
{
"title": "岗位类别选择器"
}
</route-meta>
<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    filterable
    clearable
  >
    <el-option
      v-for="item in positionCategoryOptions"
      :label="showLabel(item)"
      :value="item.propertyId"
      :key="item.propertyId"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import QueryTrainingCategory from '@api/service/common/basic-data-dictionary/query/QueryTrainingCategory'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
  import QueryTrainingObject from '@api/service/common/basic-data-dictionary/query/QueryTrainingObject'
  import QueryPositionCategory from '@api/service/common/basic-data-dictionary/query/QueryPositionCategory'

  @Component
  export default class extends Vue {
    selected = ''
    // 岗位类别选项
    positionCategoryOptions: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()

    @Prop({
      type: String,
      default: '请选岗位类别'
    })
    placeholder: string

    //行业属性分类id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string

    // 培训对象id
    @Prop({
      type: String,
      default: ''
    })
    trainingObjectId: string

    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      this.$emit('updateTrainingCategory', this.selected)
      return this.selected
    }

    @Watch('trainingObjectId', {
      immediate: true,
      deep: true
    })
    async trainingObjectIdChange() {
      await this.getTrainingCategoryOptions()
    }

    /**
     * 获取展示名称
     */
    get showLabel() {
      return (item: TrainingCategoryVo) => {
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    /**
     * 获取岗位类别 - 职业卫生行业
     */
    async getTrainingCategoryOptions() {
      if (!this.trainingObjectId || !this.industryPropertyId) {
        this.positionCategoryOptions = []
        return
      }
      const data = await QueryPositionCategory.queryPositionCategoryByTrainingObjectId(
        this.industryPropertyId,
        this.trainingObjectId
      )
      this.positionCategoryOptions = data
    }
  }
</script>
