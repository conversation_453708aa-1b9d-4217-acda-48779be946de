import MsTradeQueryFrontGatewayCourseLearningBacktage, {
  ExchangeOrderRequest,
  ExchangeOrderSortField,
  ExchangeOrderSortRequest,
  Page,
  ReturnOrderRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import ReturnOrderResponseVo from '@api/service/management/trade/single/order/query/vo/ReturnOrderResponseVo'
import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
import { ResponseStatus } from '@hbfe/common'
import ExchangeDetailVo from '@api/service/management/trade/single/order/query/vo/ExchangeDetailVo'
import TradeModule from '@api/service/management/trade/TradeModule'
import MsTradeQueryFrontGatewayCourseLearningForestage from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import QueryExchangeDetail from '@api/service/management/trade/single/order/query/QueryExchangeDetail'
export default class QueryExchangeDetailAccordingOrder {
  //子订单号
  subOrderNo = ''
  //取换货单详情对象
  exchangeOrderDetail = new ExchangeDetailVo()
  /**
   * 获取换货单单详情
   *
   */
  async queryRefundOrderDetail(): Promise<ResponseStatus> {
    if (!this.subOrderNo) {
      return new ResponseStatus(8006, '子订单号不能为空')
    }
    const request = new ExchangeOrderRequest()
    request.subOrderNoList = [this.subOrderNo]
    const sort = new ExchangeOrderSortRequest()
    sort.field = ExchangeOrderSortField.APPLIED_TIME
    sort.policy = SortPolicy.DESC
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pageExchangeOrderInServicer({
      page: {
        pageNo: 1,
        pageSize: 1
      },
      request: request,
      sort: [sort]
    })
    if (res.status.isSuccess()) {
      const exchangeDetailQuery = new QueryExchangeDetail()
      exchangeDetailQuery.exchangeNo = res.data.currentPageData[0].exchangeOrderNo
      // exchangeDetailQuery.exchangeNo = ''
      const refundDetailRes = await exchangeDetailQuery.queryRefundOrderDetail()
      if (refundDetailRes.isSuccess()) {
        this.exchangeOrderDetail = exchangeDetailQuery.exchangeOrderDetail
      }
    }
    return res.status
  }
  /**
   * 获取换货单单详情-分销商
   *
   */
  async queryRefundOrderDetailInDistributor(): Promise<ResponseStatus> {
    if (!this.subOrderNo) {
      return new ResponseStatus(8006, '子订单号不能为空')
    }
    const request = new ExchangeOrderRequest()
    request.subOrderNoList = [this.subOrderNo]
    const sort = new ExchangeOrderSortRequest()
    sort.field = ExchangeOrderSortField.APPLIED_TIME
    sort.policy = SortPolicy.DESC
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pageExchangeOrderInDistributor({
      page: {
        pageNo: 1,
        pageSize: 1
      },
      request: request,
      sort: [sort]
    })
    if (res.status.isSuccess()) {
      const exchangeDetailQuery = new QueryExchangeDetail()
      exchangeDetailQuery.exchangeNo = res.data.currentPageData[0].exchangeOrderNo
      // exchangeDetailQuery.exchangeNo = ''
      const refundDetailRes = await exchangeDetailQuery.queryRefundOrderDetailInDistributor()
      if (refundDetailRes.isSuccess()) {
        this.exchangeOrderDetail = exchangeDetailQuery.exchangeOrderDetail
      }
    }
    return res.status
  }
}
