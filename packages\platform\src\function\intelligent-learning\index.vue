<template>
  <div>
    <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb15 is-border-bottom">
      <div class="f-flex f-align-center">
        <div class="f-flex-sub">
          支持设置全网校智能学习的课程学习规则、考试规则。系统默认设置一套规则，若需要调整配置，修改后保存生效，新建智能学习任务的学员会按照保存的规则计算。
        </div>
        <el-button type="primary" size="small" @click="openDialog('configuration')">使用默认配置</el-button>
      </div>
    </el-alert>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header">
        <div slot="header" class="">
          <span class="tit-txt">课程学习规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form
              ref="form"
              :model="intelligenceLearningModule.IntelligenceLearningModel"
              :rules="rules"
              label-width="auto"
              class="m-form f-mt30"
            >
              <el-form-item>
                <div slot="label">
                  <span class="f-cr f-mr5">*</span>
                  <span class="f-vm">每天学习时间</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      学习时间是指触发重新计算学习记录时，每门课程的学习时间会在设置的时间段内，时间段外的时间不会学习
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                <div class="time-item form-l">
                  <div class="time">00:00:00 至 23:59:59</div>
                </div>
              </el-form-item>
              <el-form-item label="每天不学习时间：">
                <el-button type="primary" icon="el-icon-plus" plain @click="openAddNoStudyTime">添加时间段</el-button>
                <template
                  v-if="
                    intelligenceLearningModule.IntelligenceLearningModel.noLearningDate &&
                    intelligenceLearningModule.IntelligenceLearningModel.noLearningDate.length
                  "
                >
                  <div
                    class="time-item form-l f-mt15"
                    v-for="(item, index) in intelligenceLearningModule.IntelligenceLearningModel.noLearningDate"
                    :key="index"
                  >
                    <div class="time">{{ item.min }} 至 {{ item.max }}</div>
                    <i class="f-link f-cb el-icon-delete f-f18" @click="removeNoStudyTime(index)"></i>
                  </div>
                </template>
              </el-form-item>
              <el-form-item label="首次开始学习时间：" prop="firstLearningStartDay">
                开通班级的
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                  <el-input-number
                    :controls="false"
                    :precision="0"
                    :min="0"
                    type="number"
                    size="small"
                    placeholder=""
                    v-model="intelligenceLearningModule.IntelligenceLearningModel.firstLearningStartDay"
                    class="input-num f-mlr5"
                    @change="changeFirstLearningStartDay"
                  />
                  <div slot="content">请输入自然数</div>
                </el-tooltip>
                <i class="f-mlr5">~</i>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                  <el-input-number
                    :controls="false"
                    :precision="0"
                    :min="0"
                    type="number"
                    size="small"
                    placeholder=""
                    v-model="intelligenceLearningModule.IntelligenceLearningModel.firstLearningEndDay"
                    class="input-num f-mlr5"
                    @change="changeFirstLearningEndDay"
                  />
                  <div slot="content">请输入自然数</div>
                </el-tooltip>
                天内随机开始学习。
              </el-form-item>
              <el-form-item label="每天最多学习时长：" prop="everyDayMaxLearningHour">
                <el-radio-group
                  v-model="intelligenceLearningModule.IntelligenceLearningModel.timeMode"
                  @input="changeTimeMode"
                >
                  <el-radio v-model="timeModeEnum.learning" :label="timeModeEnum.learning">按课程学习学时</el-radio>
                  <el-radio v-model="timeModeEnum.physical" :label="timeModeEnum.physical">按课程物理时长</el-radio>
                </el-radio-group>
                <div class="f-mt15">
                  每天课程学习最多
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <el-input-number
                      :controls="false"
                      :precision="0"
                      type="number"
                      size="small"
                      placeholder=""
                      v-model="intelligenceLearningModule.IntelligenceLearningModel.everyDayMaxLearningHour"
                      class="input-num f-mlr5"
                      @change="changeEveryDayMaxLearningHour"
                    />
                    <div slot="content">请输入正整数</div>
                  </el-tooltip>
                  <i>{{
                    intelligenceLearningModule.IntelligenceLearningModel.timeMode === timeModeEnum.learning
                      ? '学时'
                      : '小时'
                  }}</i>
                  且每次学习时长达到
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <el-input-number
                      :controls="false"
                      :precision="0"
                      type="number"
                      size="small"
                      placeholder=""
                      v-model="intelligenceLearningModule.IntelligenceLearningModel.everyTurnMaxLearningHour"
                      class="input-num f-mlr5"
                      :class="
                        intelligenceLearningModule.IntelligenceLearningModel.everyTurnMaxLearningHour > 0
                          ? ''
                          : 'no-border'
                      "
                      @change="changeRestHour"
                    />
                    <div slot="content">请输入正整数</div>
                  </el-tooltip>
                  <i>{{
                    intelligenceLearningModule.IntelligenceLearningModel.timeMode === timeModeEnum.learning
                      ? '学时'
                      : '小时'
                  }}</i>
                  ，随机休息 60~180 分钟。
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!--测验规则-->
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">测验规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <div class="f-mtb30 f-flex f-align-center">
              <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>
              <span class="f-flex-sub"
                >课程学习结束后进入对应的课程测验，测验开始时间和结束时间随机间隔分钟数15-60分钟。</span
              >
            </div>
          </el-col>
        </el-row>

        <!--考试规则-->
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">考试规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <div class="f-mt30 f-flex f-align-center">
              <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>
              <span class="f-flex-sub"
                >课程学习结束后进入考试，考试开始时间和结束时间随机间隔分钟数，最少间隔总考试时长的三分之一的时间。</span
              >
            </div>
            <el-alert type="info" :closable="false" class="m-alert f-mt10 f-mb30">
              例：考试总时长60分钟，开始和结束时间至少间隔20分钟。
            </el-alert>
          </el-col>
        </el-row>
        <!--问卷规则-->
        <!--        <div class="m-tit is-border-bottom">-->
        <!--          <span class="tit-txt">问卷规则</span>-->
        <!--        </div>-->
        <!--        <el-row type="flex" justify="center" class="width-limit">-->
        <!--          <el-col :md="20" :lg="16" :xl="13">-->
        <!--            <div class="f-mt30 f-flex f-align-center f-mb30">-->
        <!--              <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>-->
        <!--              <span class="f-flex-sub">课程学习结束后进入问卷，问卷开始时间和结束时间随机间隔分钟数15-60分钟。</span>-->
        <!--            </div>-->
        <!--          </el-col>-->
        <!--        </el-row>-->
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="openDialog('cancelEdit')">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </div>
    </div>
    <!-- 使用默认配置二次确认弹窗 -->
    <!-- 放弃编辑弹窗 -->
    <el-dialog title="提示" :visible.sync="showDialog" :lock-scroll="true" :append-to-body="true" width="20%">
      <span>{{ context }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
    <add-no-study-time ref="addNoStudyTimeRef" @addNoStudyTime="addNoStudyTime"></add-no-study-time>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import AddNoStudyTime from '@hbfe/jxjy-admin-platform/src/function/intelligent-learning/components/addNoStudyTime.vue'
  import IntelligenceLearningModule from '@api/service/management/intelligence-learning/IntelligenceLearningModule'
  import Context from '@api/service/common/context/Context'
  import { ElForm } from 'element-ui/types/form'
  import { TimeModeEnum } from '@api/service/management/intelligence-learning/enum/TimeModeEnum'
  import { TimeRange } from '@api/ms-gateway/ms-autolearning-v1'
  @Component({ components: { AddNoStudyTime } })
  export default class extends Vue {
    /**
     * 添加时段抽屉ref
     */
    @Ref('addNoStudyTimeRef')
    addNoStudyTimeRef: AddNoStudyTime
    /**
     * 表单ref
     */
    @Ref('form')
    form: ElForm
    /**
     * 智能学习规则模块
     */
    intelligenceLearningModule = new IntelligenceLearningModule()
    /**
     * 弹窗显隐
     */
    showDialog = false
    /**
     * 弹窗类型
     */
    type = ''
    /**
     * 弹窗文字内容
     */
    context = ''
    /**
     * 时长方式枚举
     */
    timeModeEnum = TimeModeEnum
    /**
     * 表单校验规则
     */
    rules = {
      firstLearningStartDay: [{ required: true, validator: this.validateFirstLearningStartDay, trigger: 'blur' }],
      everyDayMaxLearningHour: [{ required: true, validator: this.validateEveryDayMaxLearningHour, trigger: 'blur' }]
    }
    /**
     * 首次开始学习时间校验
     */
    validateFirstLearningStartDay(rule: any, value: any, callback: any) {
      if (
        this.intelligenceLearningModule.IntelligenceLearningModel.firstLearningStartDay &&
        Number(this.intelligenceLearningModule.IntelligenceLearningModel.firstLearningStartDay) >
          Number(this.intelligenceLearningModule.IntelligenceLearningModel.firstLearningEndDay)
      ) {
        return callback('最晚开始学习时间不能早于最早开始学习时间')
      }
      return callback()
    }

    changeFirstLearningStartDay() {
      this.form.validateField('firstLearningStartDay')
    }
    changeFirstLearningEndDay() {
      this.form.validateField('firstLearningStartDay')
    }
    /**
     * 每天最多学习时长校验
     */
    validateEveryDayMaxLearningHour(rule: any, value: any, callback: any) {
      if (
        !this.intelligenceLearningModule.IntelligenceLearningModel.everyDayMaxLearningHour ||
        !this.intelligenceLearningModule.IntelligenceLearningModel.everyTurnMaxLearningHour
      ) {
        return this.intelligenceLearningModule.IntelligenceLearningModel.timeMode === this.timeModeEnum.physical
          ? callback('每天最多学习时间不能小于每次学习时长')
          : callback('每天最多学习学时不能小于每次学习学时')
      }
      if (
        Number(this.intelligenceLearningModule.IntelligenceLearningModel.everyDayMaxLearningHour) <
        Number(this.intelligenceLearningModule.IntelligenceLearningModel.everyTurnMaxLearningHour)
      ) {
        return this.intelligenceLearningModule.IntelligenceLearningModel.timeMode === this.timeModeEnum.physical
          ? callback('每天最多学习时间不能小于每次学习时长')
          : callback('每天最多学习学时不能小于每次学习学时')
      }
      if (
        this.intelligenceLearningModule.IntelligenceLearningModel.timeMode === this.timeModeEnum.physical &&
        Number(this.intelligenceLearningModule.IntelligenceLearningModel.everyDayMaxLearningHour) > 24
      ) {
        return callback('每天的学习时长不能超过24小时')
      }
      if (
        this.intelligenceLearningModule.IntelligenceLearningModel.timeMode === this.timeModeEnum.learning &&
        Number(this.intelligenceLearningModule.IntelligenceLearningModel.everyDayMaxLearningHour) > 32
      ) {
        return callback('一天学习时长不能超过32学时')
      }
      return callback()
    }
    changeEveryDayMaxLearningHour() {
      this.form.validateField('everyDayMaxLearningHour')
    }
    changeRestHour() {
      this.form.validateField('everyDayMaxLearningHour')
    }
    /**
     * 初始化查询智能学习规则配置
     */
    async doQuery() {
      await this.intelligenceLearningModule.doQuery(Context.servicerInfo.id)
      if (!this.intelligenceLearningModule.IntelligenceLearningModel.enable) {
        this.$confirm('智能学习规则未配置，请点击“确定“进行配置。', '提示', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'warning'
        }).then(() => {
          //
        })
      }
    }
    /**
     * 打开添加时段抽屉
     */
    openAddNoStudyTime() {
      this.addNoStudyTimeRef.showDialog()
    }
    /**
     * 添加时间段
     */
    addNoStudyTime(time: TimeRange) {
      this.intelligenceLearningModule.IntelligenceLearningModel.noLearningDate.push(time)
    }
    /**
     * 移除不学习时间
     */
    removeNoStudyTime(itemIndex: number) {
      this.$confirm('确认删除这条不学习时间吗？', '提示', {
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then((res) => {
          this.intelligenceLearningModule.IntelligenceLearningModel.noLearningDate.splice(
            this.intelligenceLearningModule.IntelligenceLearningModel.noLearningDate.findIndex(
              (item, index) => index == itemIndex
            ),
            1
          )
          console.log(res)
        })
        .catch(() => {
          //
        })
    }
    /**
     * 取消编辑
     */
    async cancelEdit() {
      this.form.resetFields()
      try {
        await this.intelligenceLearningModule.doQuery(Context.servicerInfo.id)
      } catch (e) {
        console.log(e)
      } finally {
        this.showDialog = false
      }
    }
    /**
     * 打开弹窗
     */
    openDialog(type: string) {
      if (type == 'configuration') {
        this.context = '选择系统默认配置后将默认值填充到页面，确定要选择？'
      } else {
        this.context = '确定要放弃编辑吗？'
      }
      this.type = type
      this.showDialog = true
    }
    /**
     * 确定
     */
    async confirm() {
      if (this.type == 'configuration') {
        // 使用默认配置
        this.intelligenceLearningModule.IntelligenceLearningModel.LearningDate = [{ min: '00:00:00', max: '23:59:59' }]
        this.intelligenceLearningModule.IntelligenceLearningModel.noLearningDate = [
          { min: '00:00:00', max: '08:00:00' },
          { min: '12:00:00', max: '14:00:00' },
          { min: '22:00:00', max: '23:59:59' }
        ]
        this.intelligenceLearningModule.IntelligenceLearningModel.firstLearningStartDay = 3
        this.intelligenceLearningModule.IntelligenceLearningModel.firstLearningEndDay = 5
        this.intelligenceLearningModule.IntelligenceLearningModel.timeMode = TimeModeEnum.learning
        this.intelligenceLearningModule.IntelligenceLearningModel.everyDayMaxLearningHour = 8
        this.intelligenceLearningModule.IntelligenceLearningModel.everyTurnMaxLearningHour = 4
        this.intelligenceLearningModule.IntelligenceLearningModel.examTimeRatio = 0.34
        const res = await this.intelligenceLearningModule.doSave()
        res.code == 200 ? this.$message.success('操作成功') : this.$message.error(res.toString())
      } else {
        // 放弃编辑
        await this.cancelEdit()
      }
      this.showDialog = false
    }
    /**
     * 保存智能学习规则
     */
    async save() {
      this.form.validate(async (valid: boolean) => {
        if (valid) {
          const res = await this.intelligenceLearningModule.doQueryServiceConfig()
          if (res === 2) {
            return this.$message.warning('该网校未配置智能学习增值服务,无法保存智能学习规则！')
          } else {
            try {
              const res = await this.intelligenceLearningModule.doSave()
              if (res.code == 200) {
                return this.$message.success('保存成功')
              } else {
                return this.$message.error(res.toString())
              }
            } catch (e) {
              console.log(e, 'e')
            }
          }
        }
      })
    }
    /**
     * 时长方式改变
     */
    changeTimeMode(e: any) {
      this.form.clearValidate('everyDayMaxLearningHour')
      if (e === 1) {
        this.intelligenceLearningModule.IntelligenceLearningModel.everyDayMaxLearningHour = 8
        this.intelligenceLearningModule.IntelligenceLearningModel.everyTurnMaxLearningHour = 4
      } else {
        this.intelligenceLearningModule.IntelligenceLearningModel.everyDayMaxLearningHour = 4
        this.intelligenceLearningModule.IntelligenceLearningModel.everyTurnMaxLearningHour = 2
      }
    }
  }
</script>
<style scoped>
  .no-border {
    border-color: #dcdfe6;
  }
</style>
