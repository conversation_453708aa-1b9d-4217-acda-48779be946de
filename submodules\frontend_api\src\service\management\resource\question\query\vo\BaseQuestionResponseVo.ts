import { LibraryResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'

/* 
  试题基类
*/
class BaseQuestionResponseVo {
  /**
   * 试题ID
   */
  questionId = ''
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic = ''
  /**
   * 试题解析
   */
  dissects = ''
  /**
   * 是否为子题
   */
  isChildQuestion = false
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId = ''
  /**
   * 创建人Id
   */
  createUserId = ''
  /**
   * 创建时间
   */
  createTime = ''

  /**
     * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
     @see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType = 1
  /**
   * 试题是否可用
   */
  isEnabled = true
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty = 1
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string> = []
}

export default BaseQuestionResponseVo
