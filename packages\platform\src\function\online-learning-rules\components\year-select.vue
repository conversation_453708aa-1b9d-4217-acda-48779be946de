<template>
  <el-select v-model="selectValue" :placeholder="placeholder" class="form-l" filterable clearable multiple>
    <el-option
      v-for="item in placeChannelOptions"
      :label="item.year"
      :value="item.id"
      :key="item.id"
      :disabled="isDisabled && item.year != '全部'"
    ></el-option>
  </el-select>
</template>

<script lang="ts">
  import { Component, Mixins } from 'vue-property-decorator'
  import YearVo from '@api/service/common/basic-data-dictionary/query/vo/YearVo'
  import QueryYear from '@api/service/common/basic-data-dictionary/query/QueryYear'
  import CommonSkuMixins from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSkuMixins'

  @Component
  export default class extends Mixins(CommonSkuMixins) {
    /**
     * 年度选项
     */
    placeChannelOptions: Array<YearVo> = new Array<YearVo>()

    async created() {
      const res = await QueryYear.queryYearList()
      if (res.isSuccess()) {
        this.placeChannelOptions = QueryYear.yearList.sort((a, b) => Number(b.year) - Number(a.year))
        const target = this.placeChannelOptions.find((item) => {
          return item.id == '-1'
        })
        if (target) {
          return
        } else {
          const param = new YearVo()
          param.enable = true
          param.id = '-1'
          param.sort = 0
          param.year = '全部'
          this.placeChannelOptions.unshift(param)
        }
      }
    }
  }
</script>
