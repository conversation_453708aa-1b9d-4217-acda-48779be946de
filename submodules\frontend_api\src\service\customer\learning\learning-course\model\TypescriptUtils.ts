const getKeyValue = (key: string) => (obj: Record<string, any>) => obj[key]
const setKeyValue = (key: string, value: any) => (obj: Record<string, any>) => (obj[key] = value)

class TypescriptUtils {
  copyObjWhenKeyEqual<T extends object, R extends object>(copyFrom: T, copyTo: R): R {
    const keysTo = Object.keys(copyTo)
    for (const key of keysTo) {
      const value = getKeyValue(key)(copyFrom)
      if (value !== undefined) {
        setKeyValue(key, value)(copyTo)
      }
    }
    return copyTo
  }
}

export default new TypescriptUtils()
