"""独立部署的微服务,K8S服务名:ms-interestcourselearningscene-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""申请课程学习
		200-成功
		60001-学习token解析异常
		60002-学习场景不存在
		60003-该大纲下课程不存在
		60004-选课失败
		@param studentLearningToken 学员学习凭证
		@param outlineId            所属课程学习大纲编号
		@param courseId             课程编号
		@return 课程学习凭证
	"""
	applyCourseLearning(studentLearningToken:String!,outlineId:String!,courseId:String!):StudentCourseLearningTokenResponse
}
"""课程学习凭证响应
	<AUTHOR>
	@since 2022/1/20
"""
type StudentCourseLearningTokenResponse @type(value:"com.fjhb.ms.clscene.interest.v1.kernel.gateway.graphql.response.StudentCourseLearningTokenResponse") {
	"""申请结果"""
	applyResult:TokenResponse
	"""课程学习凭证"""
	token:String
}
"""凭证响应基类
	<AUTHOR>
	@since 2022/1/20
"""
type TokenResponse @type(value:"com.fjhb.ms.clscene.interest.v1.kernel.gateway.graphql.response.TokenResponse") {
	"""代码：
		200-成功
	"""
	code:String
	"""信息"""
	message:String
}

scalar List
