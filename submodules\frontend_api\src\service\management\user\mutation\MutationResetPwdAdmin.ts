import { ResponseStatus } from '@hbfe/common'
import basicdataDomain, { CurrentAccountChangePasswordRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'

/**
 * 修改当前登录账号密码
 */
class MutationResetPwdAdmin {
  async doResetAdminPwd(oldPwd: string, newPwd: string): Promise<ResponseStatus> {
    const request = new CurrentAccountChangePasswordRequest()
    request.originalPassword = oldPwd
    request.newPassword = newPwd
    const { status } = await basicdataDomain.changePasswordByCurrent(request)
    return status
  }
  /**
   * 修改当前登录账号密码(有返回值信息)
   */
  async doResetAdminPwdNew(oldPwd: string, newPwd: string) {
    const request = new CurrentAccountChangePasswordRequest()
    request.originalPassword = oldPwd
    request.newPassword = newPwd
    const data = await basicdataDomain.changePasswordByCurrentWithResponse(request)
    return data
  }
}

export default MutationResetPwdAdmin
