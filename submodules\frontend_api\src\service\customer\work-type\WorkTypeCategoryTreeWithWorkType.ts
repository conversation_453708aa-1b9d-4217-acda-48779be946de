import WorkTypeCategoryWithWorkType from '@api/service/common/models/work-type/WorkTypeCategoryWithWorkType'
import WorkType from '@api/service/common/models/work-type/WorkType'

class WorkTypeCategoryTreeWithWorkType extends WorkTypeCategoryWithWorkType {
  children = new Array<WorkTypeCategoryTreeWithWorkType>()

  addChild(child: WorkTypeCategoryTreeWithWorkType): WorkTypeCategoryTreeWithWorkType {
    if (!this.children) {
      this.children = new Array<WorkTypeCategoryTreeWithWorkType>()
    }
    this.children.push(child)
    return this
  }

  hasChildren(): boolean {
    return this.children?.length > 0
  }

  removeChild(id: string) {
    if (this.children) {
      const i = this.children.findIndex(child => child.id === id)
      if (i !== -1) {
        this.children.splice(i, 1)
      }
    }
  }

  sortChildren() {
    this.children?.sort((a, b) => {
      let n = a.sort - b.sort
      if (n === 0) n = a.lastUpdateTime.getTime() - b.lastUpdateTime.getTime()
      return n
    })
    this.children?.forEach(child => child.sortChildren())
  }

  getWorkTypeCount(): number {
    let count = 0
    if (this.hasChildren()) {
      this.children.forEach(child => (count += child.getWorkTypeCount()))
    } else {
      count = this.workTypes.length
    }
    return count
  }

  getWorkTypes(): Array<WorkType> {
    if (this.hasChildren()) {
      let array = new Array<WorkType>()
      this.children.forEach(child => (array = array.concat(child.getWorkTypes())))
      return array
    } else {
      return new Array<WorkType>().concat(this.workTypes)
    }
  }
}

export default WorkTypeCategoryTreeWithWorkType
