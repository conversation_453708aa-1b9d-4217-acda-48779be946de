import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'zzkd-data-export-gateway-forestage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

export class StudentSchemeLearningRequest {
  studentNoList?: Array<string>
  student?: UserRequest
  learningRegister?: LearningRegisterRequest
  scheme?: SchemeRequest
  studentLearning?: StudentLearningRequest
  dataAnalysis?: DataAnalysisRequest
  connectManageSystem?: ConnectManageSystemRequest
  extendedInfo?: ExtendedInfoRequest
  openPrintTemplate?: boolean
  saleChannels?: Array<number>
  trainingChannelName?: string
  trainingChannelId?: string
  notDistributionPortal?: boolean
  trainingType?: string
  issueId?: string
}

export class ConnectManageSystemRequest {
  syncStatus?: number
}

export class DataAnalysisRequest {
  trainingResultPeriod?: DoubleScopeRequest
  requirePeriod?: DoubleScopeRequest
  acquiredPeriod?: DoubleScopeRequest
}

export class ExtendedInfoRequest {
  whetherToPrint?: boolean
  applyCompanyCode?: string
  policyTrainingSchemeId?: string
  policyTrainingSchemeName?: string
}

export class LearningRegisterRequest {
  registerType?: number
  sourceType?: string
  sourceId?: string
  status?: Array<number>
  registerTime?: DateScopeRequest
  saleChannels?: Array<number>
  orderNoList?: Array<string>
  subOrderNoList?: Array<string>
  batchOrderNoList?: Array<string>
  distributorId?: string
  portalId?: string
}

export class RegionRequest {
  province?: string
  city?: string
  county?: string
}

export class StudentLearningRequest {
  trainingResultList?: Array<number>
  trainingResultTime?: DateScopeRequest
  notLearningTypeList?: Array<number>
  courseScheduleStatus?: number
  examAssessResultList?: Array<number>
}

export class RegionSkuPropertyRequest {
  province?: string
  city?: string
  county?: string
}

export class RegionSkuPropertySearchRequest {
  region?: Array<RegionSkuPropertyRequest>
  regionSearchType?: number
}

export class SchemeRequest {
  schemeId?: string
  schemeIdList?: Array<string>
  schemeType?: string
  schemeName?: string
  skuProperty?: SchemeSkuPropertyRequest
}

export class SchemeSkuPropertyRequest {
  year?: Array<string>
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  industry?: Array<string>
  subjectType?: Array<string>
  trainingCategory?: Array<string>
  trainingProfessional?: Array<string>
  technicalGrade?: Array<string>
  positionCategory?: Array<string>
  trainingObject?: Array<string>
  jobLevel?: Array<string>
  jobCategory?: Array<string>
  subject?: Array<string>
  grade?: Array<string>
  learningPhase?: Array<string>
  discipline?: Array<string>
  qualificationCategory?: Array<string>
  trainingWay?: Array<string>
  trainingInstitution?: Array<string>
  mainAdditionalItem?: Array<string>
}

export class UserPropertyRequest {
  regionList?: Array<RegionRequest>
  companyName?: string
  payOrderRegionList?: Array<RegionRequest>
}

export class UserRequest {
  userIdList?: Array<string>
  accountIdList?: Array<string>
  userProperty?: UserPropertyRequest
}

/**
 * 功能描述：任务查询参数
@Author： wtl
@Date： 2022/1/18 15:13
 */
export class JobRequest {
  /**
   * 任务组名（必填）
   */
  group?: string
  /**
   * 任务组名匹配方式（EQ：完全匹配 LIKE：模糊匹配[*group*] LLIKE：左模糊匹配[group*] RLIKE：右模糊匹配[*group]，不传值默认为完全匹配）
   */
  groupOperator?: string
  /**
   * 任务名（模糊查询）
   */
  jobName?: string
  /**
   * 任务状态(executing:运行中 executed:运行完成 fail:运行失败)
@see UserJobState
   */
  jobState?: string
  /**
   * 任务执行时间 yyyy-MM-dd HH:mm:ss
   */
  executeTimeScope?: DateScopeRequest
  /**
   * 异步任务处理结果（true:成功 false:失败）
   */
  jobResult?: boolean
  /**
   * 分割粒度
null-无 1-单位
   */
  granularity?: number
}

/**
 * 功能描述：异步任务日志返回对象
@Author： wtl
@Date： 2022/4/11 17:18
 */
export class UserJobLogResponse {
  /**
   * 任务id
   */
  jobId: string
  /**
   * 任务组名
   */
  group: string
  /**
   * 任务名
   */
  jobName: string
  /**
   * 任务开始时间
   */
  beginTime: string
  /**
   * 任务结束时间
   */
  endTime: string
  /**
   * 任务状态(executing:运行中 executed:运行完成 fail:运行失败)
@see UserJobState
   */
  jobState: string
  /**
   * 异步任务处理结果（true:成功 false:失败）
   */
  jobResult: boolean
  /**
   * 任务执行成功或失败的信息
   */
  message: string
  /**
   * 导出文件路径
   */
  exportFilePath: string
  /**
   * 是否受保护
   */
  isProtected: boolean
  /**
   * 资源id
   */
  fileResourceId: string
  /**
   * 操作人id
   */
  operatorUserId: string
  /**
   * 操作人帐户id
   */
  operatorAccountId: string
}

export class UserJobLogResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserJobLogResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 集体报名管理员导出明细数据新口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportStudentSchemeLearningExcelInCollective(
    request: StudentSchemeLearningRequest,
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInCollective,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询导出任务信息
   * @param page
   * @param jobRequest
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageExportTaskInfoInMyself(
    params: { page?: Page; jobRequest?: JobRequest },
    query: DocumentNode = GraphqlImporter.pageExportTaskInfoInMyself,
    operation?: string
  ): Promise<Response<UserJobLogResponsePage>> {
    return commonRequestApi<UserJobLogResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
