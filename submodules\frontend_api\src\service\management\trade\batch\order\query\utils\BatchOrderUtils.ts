import msTradeQuery, {
  BatchOrderInfoRequest,
  BatchReturnOrderRequest,
  CommoditySkuRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import msTradeQueryBackstage, {
  BatchOrderResponse,
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  ExchangeOrderRequest,
  ExchangeOrderSortField,
  ExchangeOrderSortRequest,
  OfflineInvoiceDeliveryInfoResponse,
  OrderBasicDataRequest,
  OrderRequest,
  OrderResponse,
  OrderStatisticResponse,
  PaymentVoucherResponse,
  ReturnOrderBasicDataRequest,
  ReturnOrderRequest,
  ReturnOrderResponse,
  ReturnOrderSortField,
  ReturnOrderStatisticResponse,
  ReturnSortRequest,
  SchemeResourceResponse,
  SortPolicy,
  SubOrderInfoRequest,
  SubOrderResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import BatchOrderRemittanceVoucherInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderRemittanceVoucherInfoVo'
import QueryCollectiveManagerList from '@api/service/management/user/query/manager/QueryCollectiveManagerList'
import UserModule from '@api/service/management/user/UserModule'
import CollectiveManagerQueryIdVo from '@api/service/management/user/query/manager/vo/CollectiveManagerQueryIdVo'
import DataResolve from '@api/service/common/utils/DataResolve'
import QueryBatchOrderListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderListVo'
import { BatchOrderTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
import { Page } from '@hbfe/common'
import msCollectiveSign, {
  CollectiveSignQueryRequest,
  FindCountGroupByKeyRequest
} from '@api/ms-gateway/ms-collectivesign-v1'
import { OrderTerminalTypeEnum } from '@api/service/common/enums/order/OrderTerminalTypes'
import BatchOrderDetailInvoiceInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailInvoiceInfoVo'
import { InvoiceStatusEnum } from '@api/service/management/trade/batch/invoice/enum/InvoiceEnum'
import TradeModule from '@api/service/management/trade/TradeModule'
import InvoiceListResponse from '@api/service/management/trade/batch/invoice/query/vo/InvoiceListResponse'
import OffLinePageInvoiceResponseVo from '@api/service/management/trade/batch/invoice/query/vo/OffLinePageInvoiceResponseVo'
import { BatchOrderInvoiceStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderInvoiceStatus'
import BatchOrderDetailDeliveryInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailDeliveryInfoVo'
import { BatchOrderDeliveryWayEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderDeliveryWay'
import { BatchOrderMainOrderStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderMainOrderStatus'
import { BatchOrderMainOrderAfterSaleStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderMainOrderAfterSaleStatus'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import QueryBatchOrderMainOrderListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderMainOrderListVo'
import { cloneDeep } from 'lodash'
import BatchOrderMainOrderSubOrderListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderSubOrderListDetailVo'
import { BatchOrderSubOrderStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderSubOrderStatus'
import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
import { ExchangeTrainClassStatusEnum } from '@api/service/management/train-class/query/enum/ExchangeTrainClassStatusType'
import { BatchOrderSubOrderAfterSaleStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderSubOrderAfterSaleStatus'
import ExchangeOrderStatus from '@api/service/management/train-class/query/vo/ExchangeOrderStatus'
import ExchangeOrderRecordDetailVo from '@api/service/management/train-class/query/vo/ExchangeOrderRecordDetailVo'
import BatchOrderRefundInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderRefundInfoVo'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import {
  statisticOrderInDistributor,
  statisticOrderInServicer,
  statisticOrderInTrainingChannel
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage/graphql-importer'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import QueryInvoice from '@api/service/management/trade/batch/invoice/query/QueryInvoice'
import QueryOffLineInvoice from '@api/service/management/trade/batch/invoice/query/QueryOffLineInvoice'
import OffLinePageInvoiceVo from '@api/service/management/trade/batch/invoice/query/vo/OffLinePageInvoiceResponseVo'
import { SchemeSkuPropertyResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import {
  CommoditySkuPropertyResponse,
  PortalCommoditySkuPropertyResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import SkuPropertyConvertUtilsNew from '@api/service/customer/train-class/Utils/SkuPropertyConvertUtils'

/**
 * @description 【集体报名订单】工具类
 */
class BatchOrderUtils {
  /**
   * 获取汇款凭证信息列表
   */
  static getRemittanceVoucherInfoList(list: PaymentVoucherResponse[]): BatchOrderRemittanceVoucherInfoVo[] {
    const result = [] as BatchOrderRemittanceVoucherInfoVo[]
    list.forEach((item) => {
      const option = new BatchOrderRemittanceVoucherInfoVo()
      option.thumbnailImageUrl = item.path
      option.originImageUrl = item.path
      result.push(option)
    })
    return result
  }

  /**
   * 根据身份证、姓名获取购买人id集合【集体缴费管理员】
   */
  static async getBuyerIdList(params: QueryBatchOrderListVo): Promise<string[] | undefined> {
    let result: string[] = undefined
    // 参数传用户id，不用处理直接返回
    if (params.buyerId) return [params.buyerId]
    if (!params.buyerAccount && !params.buyerName) return result
    const queryRemote: QueryCollectiveManagerList = UserModule.queryUserFactory.queryCollectiveManagerList
    const queryParams = new CollectiveManagerQueryIdVo()
    queryParams.userName = params.buyerName || undefined
    queryParams.phone = params.buyerAccount || undefined
    const response = await queryRemote.queryCollectiveManagerIdList(queryParams)
    if (DataResolve.isWeightyArr(response)) {
      result = response
    }
    return result
  }

  /**
   * 获取批次单订单状态
   */
  static getBatchOrderStatus(response: BatchOrderResponse): BatchOrderTradeStatusEnum {
    // 待下单
    if (response.basicData?.batchOrderStatus === 0) {
      return BatchOrderTradeStatusEnum.Wait_Place_Order
    }
    // 下单中
    if (response.basicData?.batchOrderStatus === 4) {
      return BatchOrderTradeStatusEnum.Placing_Order
    }
    // 待付款
    if (response.basicData?.batchOrderStatus === 1 && response.basicData?.paymentStatus === 0) {
      return BatchOrderTradeStatusEnum.Wait_Pay
    }
    // 支付中
    if (response.basicData?.batchOrderStatus === 1 && response.basicData?.paymentStatus === 1) {
      return BatchOrderTradeStatusEnum.Paying
    }
    // 开通中
    if (response.basicData?.batchOrderStatus === 1 && response.basicData?.paymentStatus === 2) {
      return BatchOrderTradeStatusEnum.Opening
    }
    // 交易成功
    if (response.basicData?.batchOrderStatus === 2) {
      return BatchOrderTradeStatusEnum.Pay_Success
    }
    // 交易关闭中
    if (response.basicData?.batchOrderStatus === 5) {
      return BatchOrderTradeStatusEnum.Closing_Pay
    }
    // 交易关闭
    if (response.basicData?.batchOrderStatus === 3) {
      return BatchOrderTradeStatusEnum.Close_Pay
    }
    return null
  }

  /**
   * 获取上传报名人次总数
   */
  static async getUploadSignUpTotalCount(batchOrderNo: string): Promise<number> {
    let result = 0
    if (!batchOrderNo) return result
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    const queryParams = new CollectiveSignQueryRequest()
    queryParams.collectiveSignupNo = batchOrderNo
    const response = await msCollectiveSign.findImportCollectiveSignupCompleteSuccessDataByPage({
      request: queryParams,
      page
    })
    if (response.status?.isSuccess()) {
      result = response.data?.totalSize ?? 0
    }
    return result
  }

  /**
   * 获取终端渠道类型
   */
  static getTerminalCode(response: BatchOrderResponse): OrderTerminalTypeEnum {
    if (response.basicData?.terminalCode) {
      const terminalCode = response.basicData?.terminalCode
      switch (terminalCode) {
        case 'Web':
          return OrderTerminalTypeEnum.Web
        case 'IOS':
          return OrderTerminalTypeEnum.IOS
        case 'Android':
          return OrderTerminalTypeEnum.Android
        case 'WechatMini':
          return OrderTerminalTypeEnum.WechatMini
        case 'WechatOfficial':
          return OrderTerminalTypeEnum.WechatOfficial
        case 'ExternalSystemManage':
          return OrderTerminalTypeEnum.ExternalSystemManage
        case 'H5':
          return OrderTerminalTypeEnum.H5
        default:
          return null
      }
    }
    return null
  }

  /**
   * 获取批次单发票信息
   */
  static async getBatchOrderInvoiceInfoList(response: BatchOrderResponse): Promise<BatchOrderDetailInvoiceInfoVo[]> {
    const result = [] as BatchOrderDetailInvoiceInfoVo[]
    const invoiceIdList = response.invoiceApplyInfo?.invoiceIdList
    if (!DataResolve.isWeightyArr(invoiceIdList)) return result
    const invoiceIds = invoiceIdList
    const way =
      response.invoiceApplyInfo?.invoiceMethod === 1 ? 1 : response.invoiceApplyInfo?.invoiceMethod === 2 ? 2 : null
    if (!way) return result
    const queryOnLineRemote = TradeModule.batchTradeBatchFactor.invoiceFactor.queryInvoice
    const queryOffLineRemote = TradeModule.batchTradeBatchFactor.invoiceFactor.queryOffLineInvoice
    const invoiceInfoList = await Promise.all(
      invoiceIds?.map(async (item) => {
        return way === 1
          ? await queryOnLineRemote.onLineGetInvoiceInServicer(item)
          : await queryOffLineRemote.offLineGetInvoiceInServicer(item)
      })
    )
    invoiceInfoList.forEach((item) => {
      if (way === 1) {
        result.push(BatchOrderDetailInvoiceInfoVo.fromOnline(item as InvoiceListResponse))
      }
      if (way === 2) {
        result.push(BatchOrderDetailInvoiceInfoVo.fromOffLine(item as OffLinePageInvoiceResponseVo))
      }
    })
    return result
  }
  /**
   * 批量获取批次单发票信息
   */
  static async getBatchOrderInvoiceInfoListS(response: BatchOrderResponse[]) {
    const map = new Map<string, BatchOrderDetailInvoiceInfoVo[]>()
    const wayMap = new Map<number, BatchOrderResponse[]>()
    if (response.length === 0) {
      return
    }
    console.group(
      '%c%s',
      'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#32FFFF, #fff)',
      'response调试输出'
    )
    console.log(response)
    console.count('response输出次数')
    console.groupEnd()
    response.forEach((item) => {
      if (item.invoiceApplyInfo) {
        if (wayMap.get(item.invoiceApplyInfo.invoiceMethod)) {
          wayMap.get(item.invoiceApplyInfo.invoiceMethod).push(item)
        } else {
          wayMap.set(item.invoiceApplyInfo.invoiceMethod, [item])
        }
      }
    })
    const queryOnLineRemote = new QueryInvoice()
    const queryOffLineRemote = new QueryOffLineInvoice()
    const onLines = wayMap.get(1)
    const offLines = wayMap.get(2)
    const onLinseIds = onLines
      ?.map((item) => {
        if (item.invoiceApplyInfo) {
          return item.invoiceApplyInfo?.invoiceIdList
        } else {
          return []
        }
      })
      .flat()
    const offLinseIds = offLines
      ?.map((item) => {
        if (item.invoiceApplyInfo) {
          return item.invoiceApplyInfo?.invoiceIdList
        } else {
          return []
        }
      })
      .flat()
    console.group(
      '%c%s',
      'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#32FFFF, #fff)',
      'wayMap调试输出'
    )
    console.log(wayMap)
    console.count('wayMap输出次数')
    console.groupEnd()
    let onLineMap = new Map<string, InvoiceListResponse>()
    let offLineMap = new Map<string, OffLinePageInvoiceVo>()
    if (onLinseIds?.length) {
      onLineMap = await queryOnLineRemote.onLineGetInvoiceInServicers(onLinseIds)
    }
    if (offLinseIds?.length) {
      offLineMap = await queryOffLineRemote.offLineGetInvoiceInServicers(offLinseIds)
    }

    onLines?.forEach((item) => {
      const result = [] as BatchOrderDetailInvoiceInfoVo[]
      item.invoiceApplyInfo?.invoiceIdList?.forEach((invoiceId) => {
        const invoice = onLineMap?.get(invoiceId)
        if (invoice) {
          result.push(BatchOrderDetailInvoiceInfoVo.fromOnline(invoice))
        }
      })
      map.set(item.batchOrderNo, result)
    })
    offLines?.forEach((item) => {
      const result = [] as BatchOrderDetailInvoiceInfoVo[]
      item.invoiceApplyInfo?.invoiceIdList?.forEach((invoiceId) => {
        const invoice = offLineMap?.get(invoiceId)
        if (invoice) {
          result.push(BatchOrderDetailInvoiceInfoVo.fromOffLine(invoice))
        }
      })
      map.set(item.batchOrderNo, result)
    })
    return map
  }

  /**
   * 【线上】获取发票状态
   */
  static getInvoiceStatus(response: InvoiceListResponse | OffLinePageInvoiceResponseVo): BatchOrderInvoiceStatusEnum {
    // 已作废
    if (response.useless) {
      return BatchOrderInvoiceStatusEnum.Invalid
    }
    // 冻结中
    if (response.invoiceFreezeStatus) {
      return BatchOrderInvoiceStatusEnum.Frozen
    }
    // 未开票
    if (response.invoiceStatus === InvoiceStatusEnum.NOTPTOOPEN) {
      return BatchOrderInvoiceStatusEnum.Wait_For_Invoice
    }
    // 已开票
    if (response.invoiceStatus === InvoiceStatusEnum.OPEMSUCCESS) {
      return BatchOrderInvoiceStatusEnum.Complete
    }

    return null
  }

  /**
   * 获取配送信息
   * @param {OfflineInvoiceDeliveryInfoResponse} response 线下发票详细信息
   */
  static getDeliveryInfo(response: OfflineInvoiceDeliveryInfoResponse): BatchOrderDetailDeliveryInfoVo {
    const deliveryInfo = new BatchOrderDetailDeliveryInfoVo()
    if (!response) return deliveryInfo
    deliveryInfo.deliveryWay =
      response.shippingMethod === 2
        ? BatchOrderDeliveryWayEnum.Courier
        : response.shippingMethod === 1
        ? BatchOrderDeliveryWayEnum.Self_Fetched
        : BatchOrderDeliveryWayEnum.Self_Fetched
    // 填充快递信息
    if (deliveryInfo.deliveryWay === BatchOrderDeliveryWayEnum.Courier) {
      deliveryInfo.deliveryCompany = response.express?.expressCompanyName ?? ''
      deliveryInfo.distributeRegion = response.deliveryAddress?.region ?? ''
      deliveryInfo.distributeAddress = response.deliveryAddress?.address ?? ''
      deliveryInfo.distributeConsignee = response.deliveryAddress?.consignee ?? ''
      deliveryInfo.distributePhone = response.deliveryAddress?.phone ?? ''
      deliveryInfo.deliveryNo = response.express?.expressNo ?? ''
      deliveryInfo.deliveryTime = response.deliveryStatusChangeTime?.shipped ?? ''
    }
    // 填充自取信息
    if (deliveryInfo.deliveryWay === BatchOrderDeliveryWayEnum.Self_Fetched) {
      deliveryInfo.selfFetchedPoint = response.takePoint?.pickupLocation ?? ''
      deliveryInfo.selfFetchTime = response.takePoint?.pickupTime ?? ''
      deliveryInfo.remark = response.takePoint?.remark ?? ''
    }
    return deliveryInfo
  }

  /**
   * 判断子单是否换班
   */
  static validateSubOrderIsExchange(subOrderItems: SubOrderResponse[]): boolean {
    let result = false
    const target = subOrderItems.find((item) => item.exchangeStatus !== 0)
    if (target) {
      result = true
    }
    return result
  }

  /**
   * 获取主单学时
   */
  static getMainOrderPeriod(subOrderItems: SubOrderResponse[]): number {
    const total =
      subOrderItems.reduce((prev, cur) => {
        return CalculatorObj.add((cur.currentCommoditySku?.resource as SchemeResourceResponse)?.period ?? 0, prev)
      }, 0) ?? 0
    return total
  }

  /**
   * 获取主单状态
   */
  static getMainOrderStatus(response: OrderResponse): BatchOrderMainOrderStatusEnum {
    const status = response.basicData?.orderStatus ?? undefined
    const payStatus = response.basicData?.orderPaymentStatus ?? undefined
    // 等待付款
    if (status === 1 && payStatus == 0) {
      return BatchOrderMainOrderStatusEnum.Wait_Pay
    }
    // 支付中
    if (status === 1 && payStatus === 1) {
      return BatchOrderMainOrderStatusEnum.Paying
    }
    // 开通中
    if (status === 1 && payStatus === 2) {
      return BatchOrderMainOrderStatusEnum.Opening
    }
    // 交易成功
    if (status === 2) {
      return BatchOrderMainOrderStatusEnum.Complete_Trade
    }
    // 交易关闭
    if (status === 3) {
      return BatchOrderMainOrderStatusEnum.Close_Trade
    }
    return null
  }

  /**
   * 获取主单售后状态
   */
  static async getMainOrderAfterSaleStatus(
    subOrderItems: SubOrderResponse[]
  ): Promise<BatchOrderMainOrderAfterSaleStatusEnum> {
    const subOrderNoList: string[] = [...new Set(subOrderItems.map((item) => item.subOrderNo ?? ''))]
    if (DataResolve.isWeightyArr(subOrderNoList)) {
      const request = new ReturnOrderRequest()
      request.subOrderInfo = new SubOrderInfoRequest()
      request.subOrderInfo.subOrderNoList = subOrderNoList
      request.basicData = new ReturnOrderBasicDataRequest()
      request.basicData.returnOrderStatus = [8, 9, 10]
      const page = new Page()
      page.pageNo = 1
      page.pageSize = 10
      const sortOption = new ReturnSortRequest()
      sortOption.field = ReturnOrderSortField.APPLIED_TIME
      sortOption.policy = SortPolicy.DESC
      const sortRequest = Array(1).fill(sortOption) as ReturnSortRequest[]
      const response = await msTradeQuery.pageReturnOrderInServicer({
        page,
        request,
        sort: sortRequest
      })
      if (response.status?.isSuccess) {
        if (DataResolve.isWeightyArr(response.data?.currentPageData)) {
          const newest = response.data.currentPageData[0]
          const result = this.getSubOrderAfterSaleStatus(newest)
          return result
        }
        return BatchOrderMainOrderAfterSaleStatusEnum.Wait_For_Refund
      }
    }
    return null
  }

  /**
   * 获取主单售后状态（分销）
   */
  static async getFxMainOrderAfterSaleStatus(
    subOrderItems: SubOrderResponse[]
  ): Promise<BatchOrderMainOrderAfterSaleStatusEnum> {
    const subOrderNoList: string[] = [...new Set(subOrderItems.map((item) => item.subOrderNo ?? ''))]
    if (DataResolve.isWeightyArr(subOrderNoList)) {
      const request = new ReturnOrderRequest()
      request.subOrderInfo = new SubOrderInfoRequest()
      request.subOrderInfo.subOrderNoList = subOrderNoList
      request.basicData = new ReturnOrderBasicDataRequest()
      request.basicData.returnOrderStatus = [8, 9, 10]
      const page = new Page()
      page.pageNo = 1
      page.pageSize = 10
      const sortOption = new ReturnSortRequest()
      sortOption.field = ReturnOrderSortField.APPLIED_TIME
      sortOption.policy = SortPolicy.DESC
      const sortRequest = Array(1).fill(sortOption) as ReturnSortRequest[]
      const response = await msTradeQuery.pageReturnOrderInDistributor({
        page,
        request,
        sort: sortRequest
      })
      if (response.status?.isSuccess) {
        if (DataResolve.isWeightyArr(response.data?.currentPageData)) {
          const newest = response.data.currentPageData[0]
          const result = this.getSubOrderAfterSaleStatus(newest)
          return result
        }
        return BatchOrderMainOrderAfterSaleStatusEnum.Wait_For_Refund
      }
    }
    return null
  }

  /**
   * 获取子单售后状态
   */
  static getSubOrderAfterSaleStatus(response: ReturnOrderResponse): BatchOrderMainOrderAfterSaleStatusEnum {
    const returnOrderStatus: number = response.basicData?.returnOrderStatus ?? null
    // 退款成功
    if ([8, 9, 10].includes(returnOrderStatus)) {
      return BatchOrderMainOrderAfterSaleStatusEnum.Success_Refund
    }
    // 退款中
    if ([0, 1, 2, 3, 4, 5, 6].includes(returnOrderStatus)) {
      return BatchOrderMainOrderAfterSaleStatusEnum.Refunding
    }
    // 未退款
    if ([7, 11].includes(returnOrderStatus)) {
      return BatchOrderMainOrderAfterSaleStatusEnum.Wait_For_Refund
    }
    return null
  }

  /**
   * 获取批次单主单统计信息
   */
  static async getBatchOrderSignUpStatistic(
    queryParams: QueryBatchOrderMainOrderListVo
  ): Promise<OrderStatisticResponse> {
    const result = new OrderStatisticResponse()
    const params = cloneDeep(queryParams)
    const remoteQueryParams = await params.toQuerySignUpParams()
    const response = await msTradeQuery.statisticOrderInServicer(remoteQueryParams)
    if (response.status?.isSuccess()) {
      result.totalOrderAmount = response.data?.totalOrderAmount ?? 0
      result.totalOrderCount = response.data?.totalOrderCount ?? 0
      result.totalPeriod = response.data?.totalPeriod ?? 0
    }
    return result
  }

  /**
   * 获取批次单主单统计信息（分销）
   */
  static async getFxBatchOrderSignUpStatistic(
    queryParams: QueryBatchOrderMainOrderListVo
  ): Promise<OrderStatisticResponse> {
    const result = new OrderStatisticResponse()
    const params = cloneDeep(queryParams)
    const remoteQueryParams = await params.toQuerySignUpParams()
    const response = await msTradeQuery.statisticOrderInDistributor(remoteQueryParams)
    if (response.status?.isSuccess()) {
      result.totalOrderAmount = response.data?.totalOrderAmount ?? 0
      result.totalOrderCount = response.data?.totalOrderCount ?? 0
      result.totalPeriod = response.data?.totalPeriod ?? 0
    }
    return result
  }

  /**
   * 查询批次单退款统计信息
   */
  static async getBatchOrderRefundStatistic(
    queryParams: QueryBatchOrderMainOrderListVo
  ): Promise<ReturnOrderStatisticResponse> {
    const result = new ReturnOrderStatisticResponse()
    const params = cloneDeep(queryParams)
    const request = await params.toQueryRefundParams()
    // 只查询退款处理中、退款成功
    request.basicData.returnOrderStatus = [0, 1, 2, 3, 4, 5, 6, 8, 9, 10]
    const response = await msTradeQuery.statisticReturnOrderInServicer(request)
    if (response.status?.isSuccess()) {
      result.totalReturnOrderCount = response.data?.totalReturnOrderCount ?? 0
      result.totalRefundAmount = response.data?.totalRefundAmount ?? 0
    }
    return result
  }

  /**
   * 查询批次单退款统计信息 （分销）
   */
  static async getFxBatchOrderRefundStatistic(
    queryParams: QueryBatchOrderMainOrderListVo
  ): Promise<ReturnOrderStatisticResponse> {
    const result = new ReturnOrderStatisticResponse()
    const params = cloneDeep(queryParams)
    const request = await params.toQueryRefundParams()
    // 只查询退款处理中、退款成功
    request.basicData.returnOrderStatus = [0, 1, 2, 3, 4, 5, 6, 8, 9, 10]
    const response = await msTradeQuery.statisticReturnOrderInDistributor(request)
    if (response.status?.isSuccess()) {
      result.totalReturnOrderCount = response.data?.totalReturnOrderCount ?? 0
      result.totalRefundAmount = response.data?.totalRefundAmount ?? 0
    }
    return result
  }

  /**
   * 查询批次单实付金额（付款成功金额）
   */
  static async getBatchOrderPayAmount(queryParams: QueryBatchOrderMainOrderListVo): Promise<number> {
    let result = 0
    const params = cloneDeep(queryParams)
    const remoteQueryParams = await params.toQuerySignUpParams()
    // 实付金额只查询交易成功的数据
    remoteQueryParams.orderBasicData.orderStatusList = [2]
    remoteQueryParams.orderBasicData.orderPaymentStatusList = undefined
    const response = await msTradeQuery.statisticOrderInServicer(remoteQueryParams)
    if (response.status?.isSuccess()) {
      result = response.data?.totalOrderAmount ?? 0
    }
    return result
  }

  /**
   * 查询批次单实付金额（付款成功金额） （分销）
   */
  static async getFxBatchOrderPayAmount(queryParams: QueryBatchOrderMainOrderListVo): Promise<number> {
    let result = 0
    const params = cloneDeep(queryParams)
    const remoteQueryParams = await params.toQuerySignUpParams()
    // 实付金额只查询交易成功的数据
    remoteQueryParams.orderBasicData.orderStatusList = [2]
    remoteQueryParams.orderBasicData.orderPaymentStatusList = undefined
    const response = await msTradeQuery.statisticOrderInDistributor(remoteQueryParams)
    if (response.status?.isSuccess()) {
      result = response.data?.totalOrderAmount ?? 0
    }
    return result
  }

  /**
   * 获取主单详情购买清单
   */
  static async getSubOrderList(response: OrderResponse): Promise<BatchOrderMainOrderSubOrderListDetailVo[]> {
    let result = [] as BatchOrderMainOrderSubOrderListDetailVo[]
    if (DataResolve.isWeightyArr(response.subOrderItems)) {
      const mainOrderStatus = this.getMainOrderStatus(response)
      result = await Promise.all(
        response.subOrderItems.map(async (item) => {
          return await this.getSubOrderDetail(item, mainOrderStatus)
        })
      )
    }
    return result
  }

  /**
   * 获取子单信息
   */
  static async getSubOrderDetail(
    response: SubOrderResponse,
    mainOrderStatus: BatchOrderMainOrderStatusEnum
  ): Promise<BatchOrderMainOrderSubOrderListDetailVo> {
    const result = new BatchOrderMainOrderSubOrderListDetailVo()
    result.subOrderNo = response.subOrderNo ?? ''
    // 子单状态
    if (mainOrderStatus) {
      if (mainOrderStatus === BatchOrderMainOrderStatusEnum.Wait_Pay) {
        result.subOrderStatus = BatchOrderSubOrderStatusEnum.Wait_Pay
      }
      if (mainOrderStatus === BatchOrderMainOrderStatusEnum.Paying) {
        result.subOrderStatus = BatchOrderSubOrderStatusEnum.Paying
      }
      if (mainOrderStatus === BatchOrderMainOrderStatusEnum.Opening) {
        if (response.deliveryStatus === 0) {
          result.subOrderStatus = BatchOrderSubOrderStatusEnum.Wait_Delivery
        }
        if (response.deliveryStatus === 100) {
          result.subOrderStatus = BatchOrderSubOrderStatusEnum.Delivering
        }
        if (response.deliveryStatus === 200) {
          result.subOrderStatus = BatchOrderSubOrderStatusEnum.Complete_Delivery
        }
      }
      if (mainOrderStatus === BatchOrderMainOrderStatusEnum.Complete_Trade) {
        result.subOrderStatus = BatchOrderSubOrderStatusEnum.Complete_Delivery
      }
      if (mainOrderStatus === BatchOrderMainOrderStatusEnum.Close_Trade) {
        result.subOrderStatus = BatchOrderSubOrderStatusEnum.Canceled
      }
    }
    // 培训商品资源
    const resource = response.deliveryCommoditySku?.resource as SchemeResourceResponse
    const schemeType = resource?.schemeType ?? ''
    result.schemeType = SchemeTypeEnum[schemeType]
    result.schemeName = response.deliveryCommoditySku?.saleTitle ?? ''
    result.issueName = response.deliveryCommoditySku?.issueInfo?.issueName ?? ''
    result.issueId = response.deliveryCommoditySku?.issueInfo?.issueId ?? ''
    result.issueNo = response.deliveryCommoditySku?.issueInfo?.issueNum ?? ''
    const commoditySku = response.deliveryCommoditySku?.skuProperty ?? null

    const skuDto = Object.assign(new SchemeSkuPropertyResponse(), new PortalCommoditySkuPropertyResponse())
    const skuDtoMerge = Object.assign(skuDto, new CommoditySkuPropertyResponse())
    const skuVo = await SkuPropertyConvertUtilsNew.convertToSkuPropertyResponseVo(
      Object.assign(skuDtoMerge, commoditySku)
    )
    result.skuValueNameProperty = skuVo

    result.period = resource.period ?? 0
    result.quantities = response.quantity ?? 0
    result.payAmount = response.amount ?? 0
    // 换班状态
    if (response.exchangeStatus === 1 || response.exchangeStatus === 2) {
      result.exchangeStatus = ExchangeTrainClassStatusEnum.Exchanging
    }
    if (response.exchangeStatus === 3) {
      result.exchangeStatus = ExchangeTrainClassStatusEnum.Complete_Exchange
    }
    // 售后状态
    result.afterSale = await this.getMainOrderSubOrderAfterSaleStatus(response, result)
    result.refundOrderNo = result.refundDetail?.returnOrderNo ?? ''
    result.batchRefundOrderNo = result.refundDetail?.basicData?.applySourceId ?? ''
    const { exchangeStatus, exchangeInfoList } = await this.getSubOrderExchangeInfo(result.subOrderNo)
    result.exchangeStatus = exchangeStatus
    result.exchangeOrderStatusList = exchangeInfoList
    result.isExchangeIssue = !!response.isExchangeIssue
    return result
  }

  /**
   * 获取子单售后状态
   */
  static async getMainOrderSubOrderAfterSaleStatus(
    subOrderDetail: SubOrderResponse,
    data: BatchOrderMainOrderSubOrderListDetailVo
  ): Promise<BatchOrderSubOrderAfterSaleStatusEnum> {
    const request = new ReturnOrderRequest()
    request.subOrderInfo = new SubOrderInfoRequest()
    request.subOrderInfo.subOrderNoList = [subOrderDetail.subOrderNo]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    const sortOption = new ReturnSortRequest()
    sortOption.field = ReturnOrderSortField.APPLIED_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(sortOption) as ReturnSortRequest[]
    const response = await msTradeQuery.pageReturnOrderInServicer({
      page,
      request,
      sort: sortRequest
    })
    if (response.status?.isSuccess()) {
      if (DataResolve.isWeightyArr(response.data?.currentPageData)) {
        const newest = response.data.currentPageData[0]
        data.refundDetail = newest
        const refundStatus = newest.basicData?.returnOrderStatus ?? undefined
        const closeType = newest.basicData?.returnCloseReason?.closeType ?? undefined
        // 退款审核中
        if (refundStatus == 0) {
          return BatchOrderSubOrderAfterSaleStatusEnum.Refund_Approval_In_Process
        }
        // 退款处理中
        if ([2, 3, 4, 5, 6].includes(refundStatus)) {
          return BatchOrderSubOrderAfterSaleStatusEnum.Refund_In_Process
        }
        // 退款失败
        if (refundStatus === 7) {
          return BatchOrderSubOrderAfterSaleStatusEnum.Refund_Fail
        }
        // 退款成功
        if ([8, 9, 10].includes(refundStatus)) {
          return BatchOrderSubOrderAfterSaleStatusEnum.Refund_Success
        }
        // 取消退款
        if (refundStatus === 11 && [1, 2, 4].includes(closeType)) {
          return BatchOrderSubOrderAfterSaleStatusEnum.Cancel_Refund
        }
        // 拒绝退款申请
        if (refundStatus === 1 && closeType === 3) {
          return BatchOrderSubOrderAfterSaleStatusEnum.Rejected
        }
      } else {
        return BatchOrderSubOrderAfterSaleStatusEnum.Wait_For_Refund
      }
    }
    return null
  }

  /**
   * 获取子单售后状态（分销）
   */
  static async getFxMainOrderSubOrderAfterSaleStatus(
    subOrderDetail: SubOrderResponse,
    data: BatchOrderMainOrderSubOrderListDetailVo
  ): Promise<BatchOrderSubOrderAfterSaleStatusEnum> {
    const request = new ReturnOrderRequest()
    request.subOrderInfo = new SubOrderInfoRequest()
    request.subOrderInfo.subOrderNoList = [subOrderDetail.subOrderNo]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    const sortOption = new ReturnSortRequest()
    sortOption.field = ReturnOrderSortField.APPLIED_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(sortOption) as ReturnSortRequest[]
    const response = await msTradeQuery.pageReturnOrderInDistributor({
      page,
      request,
      sort: sortRequest
    })
    if (response.status?.isSuccess()) {
      if (DataResolve.isWeightyArr(response.data?.currentPageData)) {
        const newest = response.data.currentPageData[0]
        data.refundDetail = newest
        const refundStatus = newest.basicData?.returnOrderStatus ?? undefined
        const closeType = newest.basicData?.returnCloseReason?.closeType ?? undefined
        // 退款审核中
        if (refundStatus == 0) {
          return BatchOrderSubOrderAfterSaleStatusEnum.Refund_Approval_In_Process
        }
        // 退款处理中
        if ([2, 3, 4, 5, 6].includes(refundStatus)) {
          return BatchOrderSubOrderAfterSaleStatusEnum.Refund_In_Process
        }
        // 退款失败
        if (refundStatus === 7) {
          return BatchOrderSubOrderAfterSaleStatusEnum.Refund_Fail
        }
        // 退款成功
        if ([8, 9, 10].includes(refundStatus)) {
          return BatchOrderSubOrderAfterSaleStatusEnum.Refund_Success
        }
        // 取消退款
        if (refundStatus === 11 && [1, 2, 4].includes(closeType)) {
          return BatchOrderSubOrderAfterSaleStatusEnum.Cancel_Refund
        }
        // 拒绝退款申请
        if (refundStatus === 1 && closeType === 3) {
          return BatchOrderSubOrderAfterSaleStatusEnum.Rejected
        }
      } else {
        return BatchOrderSubOrderAfterSaleStatusEnum.Wait_For_Refund
      }
    }
    return null
  }

  /**
   * 获取子单换班状态
   */
  static async getSubOrderExchangeInfo(subOrderNo: string) {
    const result = {
      exchangeStatus: null as ExchangeTrainClassStatusEnum,
      exchangeInfoList: [] as ExchangeOrderStatus[]
    }
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 1
    const request = new ExchangeOrderRequest()
    request.subOrderNoList = [subOrderNo]
    const sort = new ExchangeOrderSortRequest()
    sort.field = ExchangeOrderSortField.APPLIED_TIME
    sort.policy = SortPolicy.DESC
    const response = await msTradeQuery.pageExchangeOrderInServicer({
      page,
      request,
      sort: [sort]
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      const newest = response.data.currentPageData[0]
      result.exchangeStatus = ExchangeOrderRecordDetailVo.getExchangeTrainClassStatus(newest)
      result.exchangeInfoList = ExchangeOrderRecordDetailVo.getExchangeOrderStatusList(newest)
    }
    return result
  }

  /**
   * 获取批次单退款信息
   * @param {string} batchOrderNo = 批次单id
   */
  static async getRefundInfo(batchOrderNo: string): Promise<BatchOrderRefundInfoVo> {
    const refundInfo = new BatchOrderRefundInfoVo()
    if (!batchOrderNo) return refundInfo
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    // 只查询未退款的
    const request = new OrderRequest()
    request.orderBasicData = new OrderBasicDataRequest()
    request.orderBasicData.batchOrderNoList = [batchOrderNo]
    request.subOrderReturnStatus = [0]
    const response = await msTradeQueryBackstage.statisticOrderInServicer(request)
    if (response.status?.isSuccess()) {
      refundInfo.enableRefundPersonTime = response.data?.totalOrderCount ?? 0
      refundInfo.enableRefundAmount = response.data?.totalOrderAmount ?? 0
      refundInfo.enableRefund = refundInfo.enableRefundPersonTime > 0 ? true : false
    }
    return refundInfo
  }
  /**
   * 批量获取批次单退款信息
   * @param {string} batchOrderNo = 批次单id
   */
  static async getRefundInfoS(batchOrderNos: string[]) {
    const reMap = new Map<string, BatchOrderRefundInfoVo>()

    if (!batchOrderNos.length) return reMap
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    // 只查询未退款的
    const reqS = new Array<OrderRequest>()
    batchOrderNos.forEach((batchOrderNo) => {
      const request = new OrderRequest()
      request.orderBasicData = new OrderBasicDataRequest()
      request.orderBasicData.batchOrderNoList = [batchOrderNo]
      request.subOrderReturnStatus = [0]
      reqS.push(request)
    })
    const reG = new RewriteGraph<OrderStatisticResponse, OrderRequest>(
      msTradeQueryBackstage._commonQuery,
      statisticOrderInServicer
    )
    await reG.request(reqS)
    const keys = [...reG.itemMap.keys()].map((item) => item.orderBasicData.batchOrderNoList[0])
    const values = [...reG.itemMap.values()].map((item) => {
      const refundInfo = new BatchOrderRefundInfoVo()
      refundInfo.enableRefundPersonTime = item.totalOrderCount ?? 0
      refundInfo.enableRefundAmount = item.totalOrderAmount ?? 0
      refundInfo.enableRefund = refundInfo.enableRefundPersonTime > 0 ? true : false
      return refundInfo
    })
    keys.forEach((item, index) => {
      reMap.set(item, values[index])
    })
    return reMap
  }

  /**
   * 批量获取批次单退款信息（分销商）
   * @param {string} batchOrderNo = 批次单id
   */
  static async getFxRefundInfoS(batchOrderNos: string[]) {
    const reMap = new Map<string, BatchOrderRefundInfoVo>()

    if (!batchOrderNos.length) return reMap
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    // 只查询未退款的
    const reqS = new Array<OrderRequest>()
    batchOrderNos.forEach((batchOrderNo) => {
      const request = new OrderRequest()
      request.orderBasicData = new OrderBasicDataRequest()
      request.orderBasicData.batchOrderNoList = [batchOrderNo]
      request.subOrderReturnStatus = [0]
      reqS.push(request)
    })
    const reG = new RewriteGraph<OrderStatisticResponse, OrderRequest>(
      msTradeQueryBackstage._commonQuery,
      statisticOrderInDistributor
    )
    await reG.request(reqS)
    const keys = [...reG.itemMap.keys()].map((item) => item.orderBasicData.batchOrderNoList[0])
    const values = [...reG.itemMap.values()].map((item) => {
      const refundInfo = new BatchOrderRefundInfoVo()
      refundInfo.enableRefundPersonTime = item.totalOrderCount ?? 0
      refundInfo.enableRefundAmount = item.totalOrderAmount ?? 0
      refundInfo.enableRefund = refundInfo.enableRefundPersonTime > 0 ? true : false
      return refundInfo
    })
    keys.forEach((item, index) => {
      reMap.set(item, values[index])
    })
    return reMap
  }

  /**
   * 批量获取批次单退款信息
   * @param {string} batchOrderNo = 批次单id
   */
  static async getTrainingChannelRefundInfoS(batchOrderNos: string[]) {
    const reMap = new Map<string, BatchOrderRefundInfoVo>()

    if (!batchOrderNos.length) return reMap
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    // 只查询未退款的
    const reqS = new Array<OrderRequest>()
    batchOrderNos.forEach((batchOrderNo) => {
      const request = new OrderRequest()
      request.orderBasicData = new OrderBasicDataRequest()
      request.orderBasicData.batchOrderNoList = [batchOrderNo]
      request.subOrderReturnStatus = [0]
      reqS.push(request)
    })
    const reG = new RewriteGraph<OrderStatisticResponse, OrderRequest>(
      msTradeQueryBackstage._commonQuery,
      statisticOrderInTrainingChannel
    )
    await reG.request(reqS)
    const keys = [...reG.itemMap.keys()].map((item) => item.orderBasicData.batchOrderNoList[0])
    const values = [...reG.itemMap.values()].map((item) => {
      const refundInfo = new BatchOrderRefundInfoVo()
      refundInfo.enableRefundPersonTime = item.totalOrderCount ?? 0
      refundInfo.enableRefundAmount = item.totalOrderAmount ?? 0
      refundInfo.enableRefund = refundInfo.enableRefundPersonTime > 0 ? true : false
      return refundInfo
    })
    keys.forEach((item, index) => {
      reMap.set(item, values[index])
    })
    return reMap
  }

  /**
   * 根据批次单号获取上传报名总金额【待下单】
   */
  static async getUploadSignUpTotalAmountByBatchOrderNo(batchOrderNo: string): Promise<number> {
    let result = 0
    let schemeNameList = [] as string[]
    const request = new FindCountGroupByKeyRequest()
    request.collectiveSignupNo = batchOrderNo
    request.keyName = 'SCHEME_ID'
    request.type = 1
    const response = await msCollectiveSign.findCountGroupByKey(request)
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data)) {
      const list = response.data
      console.log('keyCountMap', list)
      // 获取培训方案名称集合
      schemeNameList = [...new Set(list.map((item) => item.key ?? ''))]
      console.log('schemeNameList', schemeNameList)
      if (DataResolve.isWeightyArr(schemeNameList)) {
        const page = new Page()
        page.pageNo = 1
        page.pageSize = 200
        const queryRequest = new CommoditySkuRequest()
        queryRequest.saleTitleList = schemeNameList
        const sortOption = new CommoditySkuSortRequest()
        sortOption.sortField = CommoditySkuSortField.ON_SHELVE_TIME
        sortOption.policy = SortPolicy.DESC
        const sortRequest = Array(1).fill(sortOption)
        if (list.length === 0) {
          return result
        }
        const response1 = await msTradeQuery.pageCommoditySkuInServicer({
          page,
          queryRequest,
          sortRequest
        })
        if (response1.status?.isSuccess() && DataResolve.isWeightyArr(response1.data?.currentPageData)) {
          const schemeNamePriceMap: Map<string, number> = new Map<string, number>()
          response1.data.currentPageData.forEach((item) => {
            const schemeName = item.commodityBasicData?.saleTitle ?? ''
            const price = item.commodityBasicData?.price ?? 0
            schemeNamePriceMap.set(schemeName, price)
          })
          console.log('schemeNamePriceMap', schemeNamePriceMap)
          result =
            list.reduce((prev, cur) => {
              const perPrice = schemeNamePriceMap.get(cur.key) ?? 0
              const quantity = cur.count ?? 0
              const curPrice = CalculatorObj.multiply(perPrice, quantity) ?? 0
              return CalculatorObj.add(curPrice, prev)
            }, 0) ?? 0
        }
      }
    }
    return result
  }

  /**
   * 获取批次单是否有退款记录
   */
  static async getBatchHasRefundRecord(batchOrderNo: string): Promise<boolean> {
    let result = false
    const request = new BatchReturnOrderRequest()
    request.batchOrderInfo = new BatchOrderInfoRequest()
    request.batchOrderInfo.batchOrderNoList = [batchOrderNo]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    const response = await msTradeQuery.pageBatchReturnOrderInServicer({
      page,
      request
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = true
    }
    return result
  }
  /**
   * 获取批次单是否有退款记录（分销商）
   */
  static async getFxBatchHasRefundRecord(batchOrderNo: string): Promise<boolean> {
    let result = false
    const request = new BatchReturnOrderRequest()
    request.batchOrderInfo = new BatchOrderInfoRequest()
    request.batchOrderInfo.batchOrderNoList = [batchOrderNo]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    const response = await msTradeQuery.pageBatchReturnOrderInDistributor({
      page,
      request
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = true
    }
    return result
  }
  /**
   * 批量获取批次单是否有退款记录
   */
  static async getBatchHasRefundRecords(batchOrderNoS: string[]) {
    const map = new Map<string, boolean>()

    if (batchOrderNoS.length) {
      const request = new BatchReturnOrderRequest()
      request.batchOrderInfo = new BatchOrderInfoRequest()
      request.batchOrderInfo.batchOrderNoList = batchOrderNoS
      const page = new Page()
      page.pageNo = 1
      page.pageSize = batchOrderNoS.length
      const response = await msTradeQuery.pageBatchReturnOrderInServicer({
        page,
        request
      })
      response.data?.currentPageData.forEach((item) => {
        if (batchOrderNoS.includes(item.batchOrderInfo.batchOrderNo)) {
          map.set(item.batchOrderInfo.batchOrderNo, true)
        } else {
          map.set(item.batchOrderInfo.batchOrderNo, false)
        }
      })
    }
    return map
  }
  /**
   * 批量获取批次单是否有退款记录（分销商）
   */
  static async getFxBatchHasRefundRecords(batchOrderNoS: string[]) {
    const map = new Map<string, boolean>()
    const request = new BatchReturnOrderRequest()
    request.batchOrderInfo = new BatchOrderInfoRequest()
    request.batchOrderInfo.batchOrderNoList = batchOrderNoS
    const page = new Page()
    page.pageNo = 1
    page.pageSize = batchOrderNoS.length
    const response = await msTradeQuery.pageBatchReturnOrderInDistributor({
      page,
      request
    })
    response.data?.currentPageData.forEach((item) => {
      if (batchOrderNoS.includes(item.batchOrderInfo.batchOrderNo)) {
        map.set(item.batchOrderInfo.batchOrderNo, true)
      } else {
        map.set(item.batchOrderInfo.batchOrderNo, false)
      }
    })
    return map
  }
  /**
   * 批量获取批次单是否有退款记录（专题管理员）
   */
  static async getTrainingChannelBatchHasRefundRecords(batchOrderNoS: string[]) {
    const map = new Map<string, boolean>()
    const request = new BatchReturnOrderRequest()
    request.batchOrderInfo = new BatchOrderInfoRequest()
    request.batchOrderInfo.batchOrderNoList = batchOrderNoS
    const page = new Page()
    page.pageNo = 1
    page.pageSize = batchOrderNoS.length
    const response = await msTradeQuery.pageBatchReturnOrderInTrainingChannel({
      page,
      request
    })
    response.data?.currentPageData.forEach((item) => {
      if (batchOrderNoS.includes(item.batchOrderInfo.batchOrderNo)) {
        map.set(item.batchOrderInfo.batchOrderNo, true)
      } else {
        map.set(item.batchOrderInfo.batchOrderNo, false)
      }
    })
    return map
  }
}

export default BatchOrderUtils
