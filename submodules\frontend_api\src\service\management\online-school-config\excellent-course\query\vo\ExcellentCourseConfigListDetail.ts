import ExcellentCourseCategoryConfig from '@api/service/management/online-school-config/excellent-course/query/vo/ExcellentCourseCategoryConfig'
import { ResponseStatus } from '@hbfe/common'

class ExcellentCourseConfigListDetail {
  id: string
  name: string
  sort: number
  category: ExcellentCourseCategoryConfig = new ExcellentCourseCategoryConfig()
  createTime: string
  lastModifyTime: string

  onLoading = false

  get isShow(): boolean {
    return this._isShow
  }

  private _isShow = true

  async doCancelShow() {
    this.onLoading = true
    return new Promise(resolve => {
      setTimeout(() => {
        this.onLoading = false
        this._isShow = false
        resolve(new ResponseStatus(200, '取消展示成功'))
      }, 1500)
    })
  }

  async doShow() {
    this.onLoading = true
    return new Promise(resolve => {
      setTimeout(() => {
        this.onLoading = false
        this._isShow = true
        resolve(new ResponseStatus(200, '设置展示成功'))
      }, 1500)
    })
  }

  static from(remoteDto: any) {
    const detail = new ExcellentCourseConfigListDetail()
    detail.id = remoteDto.id
    detail.name = remoteDto.name
    detail.createTime = remoteDto.createTime
    detail.lastModifyTime = remoteDto.lastModifyTime
    detail.category = new ExcellentCourseCategoryConfig()
    detail.sort = remoteDto.sort
    return detail
  }
}

export default ExcellentCourseConfigListDetail
