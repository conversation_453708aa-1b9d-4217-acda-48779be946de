import { ResponseStatus } from '@hbfe/common'
import PlatformItem from '@api/service/diff/common/xmlg/dictionary/model/PlatformItem'
import BasicData from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'

class QueryPlatform {
  /**
   * 筛选项用
   */
  list: Array<PlatformItem> = []
  /**
   * 转换用
   */
  map: Map<string, PlatformItem> = new Map()
  /**
   * 查询列表
   */
  async queryList() {
    if (this.list.length) return new ResponseStatus(200, '读取缓存')
    const { status, data } = await BasicData.listBusinessDataDictionaryInSubProject({
      businessDataDictionaryType: 'FJZJ_THIRD_PARTY_PLATFORM_TYPE'
    })
    if (status.isSuccess() && data) {
      data.forEach(item => {
        const vo = new PlatformItem()
        vo.id = item.id
        vo.name = item.name
        this.list.push(vo)
        this.map.set(vo.id, vo)
      })
    }
    return status
  }

  /**
   * 判断对应平台id是否为华医网
   */
  isHuaYiWeb(platformId: string) {
    return platformId === 'fjzjThirdPartyPlatformType000001'
  }
}
export default new QueryPlatform()
