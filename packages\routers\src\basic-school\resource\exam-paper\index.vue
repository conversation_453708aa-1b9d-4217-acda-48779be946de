<route-meta>
{
"isMenu": true,
"title": "试卷管理",
"sort": 6,
"icon": "icon-shijuan"
}
</route-meta>
<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import ExamPaper from '@hbfe/jxjy-admin-examPaper/src/index.vue'
  import {
    // 江苏工考一体化平台子项目管理员
    ZXMGLY,
    // 内置地区管理员角色
    DQGLY,
    // 施教机构管理员
    WXGLY,
    // 学员
    XY
  } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY],
    export: [WXGLY],
    create: [WXGLY],
    category: [WXGLY],
    detail: [WXGLY],
    preview: [WXGLY],
    modify: [WXGLY],
    enable: [WXGLY],
    disable: [WXGLY],
    copy: [WXGLY]
  })
  export default class extends ExamPaper {
    // todo
  }
</script>
