"""独立部署的微服务,K8S服务名:ms-trade-configuration-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""从apollo配置中获取支持的支付渠道
		@return {@link List<PaymentChannelIdResponse>}
		<AUTHOR> By Cb
		@date 2022/10/12 16:55
	"""
	getPaymentChannelIdList:[PaymentChannelIdResponse]
	isAllowUpdate(receiveAccountId:String):Boolean!
}
type Mutation {
	"""创建电子发票纳税人
		@param request 纳税人信息
		@returns 返回纳税人编号
	"""
	createElectronicInvoiceTaxpayer(request:ElectronicInvoiceTaxpayerCreateRequest):String
	"""创建购买渠道
		@param createRequest 购买渠道创建信息
	"""
	createPurchaseChannel(createRequest:PurchaseChannelConfigCreateRequest):String
	"""创建购买渠道(管理员)
		@param createRequest 购买渠道创建信息
		@return
	"""
	createPurchaseChannelForAdmin(createRequest:PurchaseChannelConfigForAdminCreateRequest):String
	"""创建导入开通购买渠道"""
	createPurchaseChannelForImportRequest:String @optionalLogin
	"""创建收款账号
		@param info 收款账号信息
	"""
	createReceiveAccount(info:CreateReceiveAccountRequest):Void
	"""删除收款账号
		@param receiveAccountId 收款账号编号
	"""
	deleteReceiveAccount(receiveAccountId:String):Void
	"""禁用收款账号
		@param receiveAccountId 收款账号编号
	"""
	disableReceiveAccount(receiveAccountId:String):Void
	"""启用收款账号
		@param receiveAccountId 收款账号编号
	"""
	enableReceiveAccount(receiveAccountId:String):Void
	"""依据纳税人编号获取纳税人配置信息详细
		@param taxpayerId 纳税人编号
		@return 纳税人配置信息
	"""
	findElectronicInvoiceTaxpayer(taxpayerId:String):ElectronicInvoiceTaxpayerResponse
	"""获取当前网校下所有配置的纳税人
		@return 纳税人编号
	"""
	findElectronicInvoiceTaxpayerList:[String]
	"""创建用户登录配置"""
	operateUserLoginConfig(request:OperateEITaxpayerUserLogonConfigRequest):Void
	"""准备配置电子发票
		提供商如下：
		5 - 诺诺
		6 - 福建百旺
		7 - 诺诺V2
		@return 电子发票配置提供商
	"""
	prepareElectronicInvoiceConfig:PrepareElectronicInvoiceResponse
	"""根据网校id获取购买渠道类型
		@return preparePurchaseChannelResponse 购买渠道信息
	"""
	preparePurchaseChannel:PreparePurchaseChannelResponse
	"""根据服务商id获取购买渠道类型(管理员)
		@param servicerId 服务商id
		@return preparePurchaseChannelResponse 购买渠道信息
	"""
	preparePurchaseChannelForAdmin(servicerId:String):PreparePurchaseChannelResponse
	"""更新电子发票纳税人
		@param request 电子发票纳税人更新请求
	"""
	updateElectronicInvoiceTaxpayer(request:ElectronicInvoiceTaxpayerUpdateRequest):Void
	"""购买渠道更新
		@param updateRequest 购买渠道配置请求
	"""
	updatePurchaseChannel(updateRequest:PurchaseChannelConfigUpdateRequest):Void
	"""更新收款账号
		@param info 更新的收款账号信息
	"""
	updateReceiveAccount(info:UpdateReceiveAccountRequest):Void
}
"""收款账号扩展属性
	<AUTHOR>
	@since 2021/2/6
"""
input ReceiveAccountExtProperty @type(value:"com.fjhb.ms.trade.configuration.v1.api.command.receiveaccount.properties.ReceiveAccountExtProperty") {
	"""属性值"""
	name:String
	"""属性值"""
	value:String
}
"""创建商品票面信息
	<AUTHOR>
	@since 2022/3/28
"""
input CommodityInvoiceTicketConfigCreateRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.CommodityInvoiceTicketConfigCreateRequest") {
	"""商品税务编码"""
	commodityCode:String!
	"""税务编码关联信息"""
	taxCodeRelation:TaxCodeRelationCreateRequest
	"""服务名称"""
	serviceTitle:String
	"""单位"""
	unitTitle:String
	"""规格型号"""
	specificationMode:String
	"""是否打印数量"""
	printQuantity:Boolean!
	"""是否打印单价"""
	printPrice:Boolean!
	"""税率"""
	rate:BigDecimal
	"""税务优惠(仅全电票使用):0-无优惠,1-简易征收"""
	taxFavoured:Int
}
"""更新商品票面信息
	<AUTHOR>
	@since 2022/3/28
"""
input CommodityInvoiceTicketConfigUpdateRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.CommodityInvoiceTicketConfigUpdateRequest") {
	"""商品税务编码"""
	commodityCode:String
	"""税务编码关联信息"""
	taxCodeRelation:TaxCodeRelationUpdateRequest
	"""服务名称"""
	serviceTitle:String
	"""单位"""
	unitTitle:String
	"""规格型号"""
	specificationMode:String
	"""是否打印数量"""
	printQuantity:Boolean
	"""是否打印单价"""
	printPrice:Boolean
	"""税率"""
	rate:BigDecimal
	"""税务优惠(仅全电票使用):0-无优惠,1-简易征收"""
	taxFavoured:Int
	"""是否标记删除"""
	markDelete:Boolean!
}
"""创建收款账号
	<AUTHOR>
	@since 2021/2/6
"""
input CreateReceiveAccountRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.CreateReceiveAccountRequest") {
	"""收款账号（微信为商户号，支付宝为支付宝账号，建设银行为开户号）"""
	accountNo:String
	"""收款账户类型
		<pre>
		1-线上支付
		2-线下支付
		</pre>
	"""
	accountType:Int
	"""支付渠道ID，由支付服务提供的通用支付渠道编号"""
	paymentChannelId:String
	"""收款帐号名称
		别名
	"""
	name:String
	"""所属商户名称
		没有可以不写
	"""
	merchantName:String
	"""所属商户电话
		没有可以不写
	"""
	merchantPhone:String
	"""各渠道类型的收款账号扩展属性
		<p>
		1、支付宝收款账号 ----原型给的支付宝密钥不需要了
		{
		// 应用id-支付宝应用id
		"appId": "",
		// 合作者身份id
		"partner": "",
		// 支付宝应用私钥
		"privateKey": "",
		// 支付宝公钥
		"publicKey": ""
		}
		2、微信支付
		{
		// 应用id-公众账户ID
		"appId": "",
		// API秘钥，
		"merchantKey": "",
		// 证书文件名称
		"privateKeyFileName": "",
		// 证书文件地址
		"privateKeyPath": "",
		// 微信证书密钥 默认写商户号
		"privateKeyPWD": ""
		}
		3、线下收款账户
		{
		// 企业名称（开户户名）
		"merchantName":"",
		// 开户银行
		"depositBank":"",
		// 柜台号
		"counterNumber":""
		}
		4、兴业支付
		{
		//终端编号(兴业银行提供)
		"terminalId":"",
		//应用ID，为KY字母开头的字符串，区分大小写
		"appId":"",
		//请求报文签名私钥
		"requestPrivateKey":"",
		//响应报文验签公钥
		"responsePublicKey":"",
		//请求字段加密密钥
		"requestParamEncryptKey":"",
		// 当前收款账号有使用小程序&公众号支付时必填，公众账号或小程序ID
		"subAppid":""
		}
	"""
	properties:[ReceiveAccountExtProperty]
	"""退款方式
		1-线上 2-线下
	"""
	refundWay:Int!
	"""纳税人识别号"""
	taxPayerId:String
	"""付款扫码引导语"""
	qrScanPrompt:String
}
"""电子发票纳税人创建请求
	<AUTHOR>
	@since 2022/3/28
"""
input ElectronicInvoiceTaxpayerCreateRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.ElectronicInvoiceTaxpayerCreateRequest") {
	"""纳税人名称"""
	name:String
	"""纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""开户账号"""
	bankAccount:String
	"""最大开票金额"""
	invoiceMaxMoney:BigDecimal
	"""收款人"""
	payee:String
	"""开票人"""
	issuer:String
	"""复核人"""
	reviewer:String
	"""开票平台授权信息"""
	invoiceAuthList:[InvoiceServiceAuthCreateRequest]
	"""商品票面配置"""
	commodityTicketList:[CommodityInvoiceTicketConfigCreateRequest]
}
"""电子发票纳税人更新请求
	<AUTHOR>
	@since 2022/3/28
"""
input ElectronicInvoiceTaxpayerUpdateRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.ElectronicInvoiceTaxpayerUpdateRequest") {
	"""纳税人编号"""
	taxpayerId:String!
	"""纳税人名称，null表示不更新"""
	name:String
	"""纳税人识别号，null表示不更新"""
	taxpayerNo:String
	"""地址，null表示不更新"""
	address:String
	"""电话，null表示不更新"""
	phone:String
	"""开户行，null表示不更新"""
	bankName:String
	"""开户账号，null表示不更新"""
	bankAccount:String
	"""最大开票金额，null表示不更新"""
	invoiceMaxMoney:BigDecimal
	"""收款人，null表示不更新"""
	payee:String
	"""开票人，null表示不更新"""
	issuer:String
	"""复核人，null表示不更新"""
	reviewer:String
	"""开票平台授权信息，null表示不更新"""
	invoiceAuthList:[InvoiceServiceAuthUpdateRequest]
	"""商品票面配置，null表示不更新"""
	commodityTicketList:[CommodityInvoiceTicketConfigUpdateRequest]
}
input InvoiceCategoryConfig1 @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.InvoiceCategoryConfig") {
	"""允许开具的发票种类
		1 - 普通发票
		2 - 增值税普通发票
		3 - 增值税专用发票
	"""
	invoiceCategory:Int!
	"""允许发票抬头
		1 - 个人
		2 - 企业
		当openInvoiceType=0时，该值为null
	"""
	invoiceTitleTypes:[Int]
	"""开票方式
		1 - 线上开票
		2 - 线下开票
		当openInvoiceType=0时，该值为null
	"""
	invoiceMethod:Int!
	"""发票类型
		1 - 电子票
		2 - 纸质票
	"""
	invoiceType:Int
	"""发票备注类型
		0-未配置
		1-学员填写（学员、集体报名管理员）
		2-统一设置(运营端、管理员)
	"""
	invoiceRemarksType:Int
	"""发票备注内容"""
	invoiceRemarksContent:String
}
"""发票配置"""
input InvoiceConfigRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.InvoiceConfigRequest") {
	"""开放发票类型"""
	openInvoiceType:Int!
	"""是否允许索取发票"""
	allowAskFor:Boolean
	"""索取发票截止日期（格式MM/dd）"""
	askForInvoiceDeadline:String
	"""索取发票年度类型"""
	askForInvoiceYearType:Int
	"""允许开具发票种类"""
	allowInvoiceCategoryList:[InvoiceCategoryConfig1]
}
"""创建开票平台授权信息
	<AUTHOR>
	@since 2022/3/28
"""
input InvoiceServiceAuthCreateRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.InvoiceServiceAuthCreateRequest") {
	"""开票提供商编号
		5 - 诺诺，
		6 - 福建百旺
		7 - 诺诺V2
		8 - 诺税通V2全电票
		9 - 诺税通V2
	"""
	invoiceProviderId:String!
	"""应用访问标识/平台公钥"""
	accessKey:String
	"""授权码/企业私钥"""
	secretAssessKey:String
	"""部门ID"""
	deptId:String
	"""分机号"""
	extensionNumber:String
	"""企业代码"""
	enterpriseCode:String
	"""办税人身份证号"""
	taxpayerIdNumber:String
	"""备注"""
	remark:String
}
"""更新开票平台授权信息
	<AUTHOR>
	@since 2022/3/28
"""
input InvoiceServiceAuthUpdateRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.InvoiceServiceAuthUpdateRequest") {
	"""开票提供商编号
		5 - 诺诺，
		6 - 福建百旺
		7 - 诺诺V2
	"""
	invoiceProviderId:String
	"""应用访问标识/平台公钥"""
	accessKey:String
	"""授权码/企业私钥"""
	secretAssessKey:String
	"""部门ID"""
	deptId:String
	"""分机号"""
	extensionNumber:String
	"""企业代码"""
	enterpriseCode:String
	"""办税人身份证号"""
	taxpayerIdNumber:String
	"""备注"""
	remark:String
	"""是否标记删除"""
	markDelete:Boolean!
}
"""请求操作用户登录信息
	<AUTHOR>
	@since 2023/8/10
"""
input OperateEITaxpayerUserLogonConfigRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.OperateEITaxpayerUserLogonConfigRequest") {
	"""纳税人id"""
	taxpayerId:String!
	"""纳税人识别号"""
	taxpayerNo:String!
	"""登录账号
		电子税局登录账号
	"""
	loginAccount:String!
	"""登录密码
		电子税局登录密码
	"""
	loginPassword:String!
}
"""购买渠道创建信息"""
input PurchaseChannelConfigCreateRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.PurchaseChannelConfigCreateRequest") {
	"""渠道类型"""
	channelType:Int!
	"""渠道名称"""
	channelName:String
	"""终端列表"""
	terminalList:[TerminalCreateRequest]
	"""发票配置"""
	invoiceConfig:InvoiceConfigRequest
}
"""购买渠道创建信息"""
input PurchaseChannelConfigForAdminCreateRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.PurchaseChannelConfigForAdminCreateRequest") {
	"""服务商id"""
	servicerId:String
	"""渠道类型"""
	channelType:Int!
	"""渠道名称"""
	channelName:String
	"""终端列表"""
	terminalList:[TerminalCreateRequest]
	"""发票配置"""
	invoiceConfig:InvoiceConfigRequest
}
"""购买渠道配置更新请求"""
input PurchaseChannelConfigUpdateRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.PurchaseChannelConfigUpdateRequest") {
	"""购买渠道id"""
	purchaseChannelId:String!
	"""终端列表 null和空代表不更新 几何元素代表需要更新的值"""
	terminalList:[TerminalUpdateRequest]
	"""发票配置"""
	invoiceConfig:InvoiceConfigRequest
}
"""创建税务编码关联信息"""
input TaxCodeRelationCreateRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.TaxCodeRelationCreateRequest") {
	"""商品类目ID"""
	commodityCategoryId:String
}
"""更新税务编码关联信息"""
input TaxCodeRelationUpdateRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.TaxCodeRelationUpdateRequest") {
	"""商品类目ID"""
	commodityCategoryId:String
}
input TerminalCreateRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.TerminalCreateRequest") {
	"""终端代码"""
	terminalCode:String!
	"""是否关闭"""
	isClosed:Boolean!
	"""收款账号编号列表"""
	receiveAccountIdList:[String]
}
"""终端配置"""
input TerminalUpdateRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.TerminalUpdateRequest") {
	"""终端代码"""
	terminalCode:String!
	"""是否关闭"""
	isClosed:Boolean!
	"""收款账号编号列表  null代表不更新 集合元素代表所有的值 ;空数组代表移除所有;"""
	receiveAccountIdList:[String]
}
"""创建收款账号
	<AUTHOR>
	@since 2021/2/6
"""
input UpdateReceiveAccountRequest @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.UpdateReceiveAccountRequest") {
	"""收款账号编号"""
	receiveAccountId:String
	"""收款账号（微信为商户号，支付宝为支付宝账号，建设银行为开户号）"""
	accountNo:String
	"""收款帐号名称"""
	name:String
	"""所属商户名称，null表示不更新"""
	merchantName:String
	"""所属商户电话，null表示不更新"""
	merchantPhone:String
	"""退款方式 -1不更新
		1-线上 2-线下
	"""
	refundWay:Int!
	"""纳税人识别号
		null表示不更新
	"""
	taxPayerId:String
	"""各渠道类型的收款账号扩展属性
		<p>
		1、支付宝收款账号 ----原型给的支付宝密钥不需要了
		{
		// 应用id-支付宝应用id
		"appId": "",
		// 合作者身份id
		"partner": "",
		// 支付宝应用私钥
		"privateKey": "",
		// 支付宝公钥
		"publicKey": ""
		}
		2、微信支付
		{
		// 应用id-公众账户ID
		"appId": "",
		// 微信证书秘钥，默认写商户号
		"merchantKey": "",
		// 证书文件名称
		"privateKeyFileName": "",
		// 证书文件地址
		"privateKeyPath": "",
		// api秘钥
		"privateKeyPWD": ""
		}
		3、线下收款账户
		{
		// 企业名称（开户户名）
		"merchantName":"",
		// 开户银行
		"depositBank":"",
		// 柜台号
		"counterNumber":""
		}
		</p>
	"""
	properties:[ReceiveAccountExtProperty]
	"""付款扫码引导语"""
	qrScanPrompt:String
}
"""@Description
	<AUTHOR>
	@Date 2023/8/4 10:36
"""
type TaxCodeRelationConfig @type(value:"com.fjhb.ms.trade.configuration.v1.api.entites.TaxCodeRelationConfig") {
	"""关联类型"""
	relationType:Int!
	"""商品类目ID"""
	commodityCategoryId:String
	"""服务商编号"""
	servicerId:String
}
type InvoiceCategoryConfig @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.request.InvoiceCategoryConfig") {
	"""允许开具的发票种类
		1 - 普通发票
		2 - 增值税普通发票
		3 - 增值税专用发票
	"""
	invoiceCategory:Int!
	"""允许发票抬头
		1 - 个人
		2 - 企业
		当openInvoiceType=0时，该值为null
	"""
	invoiceTitleTypes:[Int]
	"""开票方式
		1 - 线上开票
		2 - 线下开票
		当openInvoiceType=0时，该值为null
	"""
	invoiceMethod:Int!
	"""发票类型
		1 - 电子票
		2 - 纸质票
	"""
	invoiceType:Int
	"""发票备注类型
		0-未配置
		1-学员填写（学员、集体报名管理员）
		2-统一设置(运营端、管理员)
	"""
	invoiceRemarksType:Int
	"""发票备注内容"""
	invoiceRemarksContent:String
}
"""商品票面信息
	<AUTHOR>
	@since 2022/3/28
"""
type CommodityInvoiceTicketConfigResponse @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.response.CommodityInvoiceTicketConfigResponse") {
	"""商品税务编码"""
	commodityCode:String
	"""税务编码关联信息"""
	taxCodeRelation:TaxCodeRelationConfig
	"""服务名称"""
	serviceTitle:String
	"""单位"""
	unitTitle:String
	"""规格型号"""
	specificationMode:String
	"""是否打印数量"""
	printQuantity:Boolean
	"""是否打印单价"""
	printPrice:Boolean
	"""税率"""
	rate:BigDecimal
	"""税务优惠(仅全电票使用):0-无优惠,1-简易征收"""
	taxFavoured:Int!
}
"""纳税人配置信息
	<AUTHOR>
	@since 2022/3/28
"""
type ElectronicInvoiceTaxpayerResponse @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.response.ElectronicInvoiceTaxpayerResponse") {
	"""纳税人名称"""
	name:String
	"""纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""开户账号"""
	bankAccount:String
	"""最大开票金额"""
	invoiceMaxMoney:BigDecimal
	"""收款人"""
	payee:String
	"""开票人"""
	issuer:String
	"""复核人"""
	reviewer:String
	"""开票平台授权信息"""
	invoiceAuthList:[InvoiceServiceAuthResponse]
	"""商品票面配置"""
	commodityTicketList:[CommodityInvoiceTicketConfigResponse]
	"""登录账号
		电子税局登录账号
	"""
	loginAccount:String
	"""登录密码
		电子税局登录密码
	"""
	loginPassword:String
}
"""发票配置
	<AUTHOR>
	@since 2022/3/24
"""
type InvoiceConfigResponse @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.response.InvoiceConfigResponse") {
	"""开放发票类型
		0 - 不开放
		1 - 自主选择
		2 - 强制提供
	"""
	openInvoiceType:Int!
	"""是否允许索取发票
		当openInvoiceType=0时，该值为null
	"""
	allowAskFor:Boolean
	"""索取发票年度类型
		1 - 当年度
		2 - 下一个年度
		当openInvoiceType=0时，该值为null
	"""
	askForInvoiceYearType:Int
	"""索取发票截止日期，格式（MM/dd）,如：5月3日，则05/03
		当openInvoiceType=0时，该值为null
	"""
	askForInvoiceDeadline:String
	"""允许开具的发票种类
		1 - 普通发票
		2 - 增值税普通发票
		3 - 增值税专用发票
		当openInvoiceType=0时，该值为null
	"""
	allowInvoiceCategoryList:[InvoiceCategoryConfig]
}
"""开票平台授权信息更新
	<AUTHOR>
	@since 2022/3/28
"""
type InvoiceServiceAuthResponse @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.response.InvoiceServiceAuthResponse") {
	"""开票提供商编号
		5 - 诺诺，
		7 - 诺诺V2
	"""
	invoiceProviderId:String
	"""应用访问标识,null 表示不更新"""
	accessKey:String
	"""授权码"""
	secretAssessKey:String
	"""企业代码"""
	enterpriseCode:String
	"""办税人身份证号"""
	taxpayerIdNumber:String
	"""部门ID"""
	deptId:String
	"""分机号"""
	extensionNumber:String
	"""备注"""
	remark:String
}
type PaymentChannelIdResponse @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.response.PaymentChannelIdResponse") {
	"""ID值"""
	paymentChannelId:String
	"""描述"""
	describe:String
}
"""<AUTHOR>
	@since 2022/3/31
"""
type PrepareElectronicInvoiceResponse @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.response.PrepareElectronicInvoiceResponse") {
	"""开票平台类型
		提供商如下：
		5 - 诺诺
	"""
	providerList:[String]
	"""商品税务编码列表"""
	commodityCodeList:[CommodityCode]
}
type CommodityCode @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.response.PrepareElectronicInvoiceResponse$CommodityCode") {
	"""商品税务编码"""
	code:String
	"""税务名称"""
	name:String
}
"""购买渠道信息
	<AUTHOR>
	@since 2022/3/25
"""
type PreparePurchaseChannelResponse @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.response.PreparePurchaseChannelResponse") {
	"""网校支持的终端代码列表
		Web - Web端
		H5 - H5端
	"""
	terminalCodeList:[String]
	"""网校下购买渠道"""
	purchaseChannelList:[PurchaseChannel]
}
type PurchaseChannel @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.response.PreparePurchaseChannelResponse$PurchaseChannel") {
	"""购买渠道id,如果该值不存在或者null,代表未创建当前购买渠道"""
	purchaseChannelId:String
	"""渠道类型 用户自主购买:1,集体缴费:2,管理员导入:3,集体报名个人缴费渠道:4"""
	channelType:Int!
	"""渠道名称"""
	channelName:String
	"""状态 (1-启用，0-禁用）"""
	status:Int!
	"""终端列表"""
	terminalList:[TerminalResponse]
	"""发票配置"""
	invoiceConfig:InvoiceConfigResponse
	"""创建人"""
	createdUserId:String
	"""创建时间"""
	createdTime:DateTime
}
"""终端配置"""
type TerminalResponse @type(value:"com.fjhb.ms.trade.configuration.v1.kernel.gateway.graphql.response.TerminalResponse") {
	"""终端代码"""
	terminalCode:String
	"""是否关闭"""
	isClosed:Boolean!
	"""收款账号编号列表"""
	receiveAccountIdList:[String]
}

scalar List
