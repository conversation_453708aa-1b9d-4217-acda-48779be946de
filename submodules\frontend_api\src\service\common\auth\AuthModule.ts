import Response, { ResponseStatus } from '../../../Response'
import $http from '@packages/request'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import platformTrainingInstitutionGateway from '@api/gateway/PlatformTrainingInstitution'
import platformBasicData, { MicroContext } from '@api/gateway/PlatformBasicData'
import platformServicerGateway, { ServicerDto } from '@api/gateway/PlatformServicer'
import platformUserGateway from '@api/gateway/PlatformUser'
import msServicerGateway, { ServicerTokenResponse } from '@api/gateway/ms-servicer-v1'
import _ from 'lodash'
import { CurrentUserServicerGroup } from '@api/service/common/auth/models/CurrentUserServicerGroup'
import { UnAuthorize } from '@api/Secure'
import { VerificationCodeResult } from '@api/service/common/models/auth/VerificationCodeResult'
import { ImageVerificationCode } from '@api/service/common/models/auth/ImageVerificationCode'

interface AuthModuleState {
  /**
   * 服务商授权凭证
   */
  servicerToken: ServicerTokenResponse
  /**
   * 当前用户服务商列表
   */
  currentUserServicerList: Array<ServicerDto>
  /**
   * 当前上下文信息
   */
  currentContext: MicroContext
}

@Module({ namespaced: true, dynamic: true, store, name: 'CommonVerificationCodeModule' })
class AuthModule extends VuexModule implements AuthModuleState {
  /**
   * 服务商授权凭证
   */
  servicerToken: ServicerTokenResponse = new ServicerTokenResponse()
  /**
   * 当前用户服务商列表
   */
  currentUserServicerList: Array<ServicerDto> = new Array<ServicerDto>()
  /**
   * 当前上下文信息
   */
  currentContext: MicroContext = new MicroContext()

  /**
   * 申请图片验证码，如果带上phoneNumber的参数则是在发送手机短信验证码前，先验证申请图片验证码再发送短信验证码流程用
   */
  @Action
  async applyImageVerificationCode(phoneNumber?: string): Promise<Response<ImageVerificationCode>> {
    let axiosResponse
    if (phoneNumber) {
      axiosResponse = await $http.post(this.authDomain + '/web/captcha/sc/apply', { phoneNumber: phoneNumber })
    } else {
      axiosResponse = await $http.get(this.authDomain + '/web/captcha/sc/apply')
    }
    const data: any = axiosResponse.data
    const code = data.code
    const response = new Response<ImageVerificationCode>()
    if (code === 200) {
      response.status = new ResponseStatus(200)
      response.data = new ImageVerificationCode()
      response.data.ticket = data.data.captchaTicket
      response.data.base64Data = data.data.b64Image
    } else {
      response.status = new ResponseStatus(code, data.message)
    }
    return response
  }

  /**
   * 验证图片验证码，返回一个验证通过后的token，用于后续申请短信验证码时使用
   * 参数ticket：申请图片验证码返回的票据，imageCode：图片验证码值
   */
  @Action
  async validateImageCode(payload: { ticket: string; imageCode: string }): Promise<Response<VerificationCodeResult>> {
    const axiosResponse = await $http.get(
      this.authDomain + '/web/captcha/sc/validate/' + payload.ticket + '/' + payload.imageCode
    )
    const data: any = axiosResponse.data
    const code = data.code
    const response = new Response<VerificationCodeResult>()
    if (code === 200) {
      response.status = new ResponseStatus(200)
      response.data = new VerificationCodeResult()
      response.data.pass = data.data.pass
      response.data.token = data.data.captchaToken
    } else {
      response.status = new ResponseStatus(code, data.message)
    }
    return response
  }

  /**
   * 在通过图片验证码验证后申请短信验证码，需要给通过图片验证码后返回的token，返回短信验证码票据
   */
  @Action
  async applySMSVerificationCodeAfterImageCode(token: string): Promise<Response<string>> {
    const axiosResponse = await $http.post(this.authDomain + '/web/captcha/sms/applywt', { captchaToken: token })
    const data: any = axiosResponse.data
    const code = data.code
    const response = new Response<string>()
    if (code === 200) {
      response.status = new ResponseStatus(200)
      response.data = data.data.captchaTicket
    } else {
      response.status = new ResponseStatus(code, data.message)
    }
    return response
  }

  /**
   * 直接获取短信验证码,用于注册，返回一个ticket用于后续验证的票据
   */
  @Action
  async applySMSVerificationCode(phoneNumber: string): Promise<Response<string>> {
    const axiosResponse = await $http.post(this.authDomain + '/web/captcha/sms/apply', { phoneNumber: phoneNumber })
    const data: any = axiosResponse.data
    const code = data.code
    const response = new Response<string>()
    if (code === 200) {
      response.status = new ResponseStatus(200)
      response.data = data.data.captchaTicket
    } else {
      response.status = new ResponseStatus(code, data.message)
    }
    return response
  }

  /**
   * 短信验证码验证，需要的参数ticket: 调用申请短信验证码后返回的票据,smsCode: 输入的短信验证码值
   * 如果验证通过则返回一个通过后签发的token
   */
  @Action
  async validateSMSCode(payload: { ticket: string; smsCode: string }): Promise<Response<VerificationCodeResult>> {
    const axiosResponse = await $http.get(
      this.authDomain + '/web/captcha/sms/validate/' + payload.ticket + '/' + payload.smsCode
    )
    const data: any = axiosResponse.data
    const code = data.code
    const response = new Response<VerificationCodeResult>()
    if (code === 200) {
      response.status = new ResponseStatus(200)
      response.data = new VerificationCodeResult()
      response.data.pass = data.data.pass
      response.data.token = data.data.captchaToken
    } else {
      response.status = new ResponseStatus(code, data.message)
    }
    return response
  }

  /**
   * 用于闽政通登录，当用户识别到未绑定，将会请求这个借口，让服务端做相关的培训平台注册等动作。
   * 这个执行成功，将会调用单点登录的服务进行token登录。
   * 同步培训机构结果返回携带培训机构的状态 用于页面进行跳转判断
   * @param token
   */
  @Action
  async doSyncTeachUnit(token: string) {
    const response = await platformTrainingInstitutionGateway.syncTrainingInstitution(token)
    return response
  }

  /**
   * 服务商申请服务凭证（获取服务商对应服务提供商信息）
   */
  @Action
  async applyForService(servicerId: string) {
    const { status, data } = await msServicerGateway.applyForService(servicerId)
    if (status.isSuccess()) {
      this.SET_SERVICER_TOKEN(data)
    }
    return status
  }

  /**
   * 获取当前环境上线
   */
  @Action
  async getCurrentMicroContext() {
    // const response = await platformBasicData.getCurrentMicroContext()
    // if (response.status.isSuccess()) {
    //   this.SET_CURRENT_MICRO_CONTEXT(response.data)
    // }
    // return response.status
    return new ResponseStatus(500, '')
  }

  /**
   * 获取当前系统时间
   */
  @Action
  async getCurrentDate() {
    return await platformBasicData.getCurrentDate()
  }

  /**
   * 获取当前用户服务商列表
   */
  @Action
  @UnAuthorize
  async getCurrentUserServicer() {
    const { status, data } = await platformServicerGateway.getCurrentUserServicer()
    if (status.isSuccess()) {
      this.SET_CURRENT_USER_SERVICER(data)
    }
    return status
  }

  /**
   * 获取当前用户服务商列表
   */
  @Action
  async preValidServicerLogin(identity: string) {
    const { status } = await platformUserGateway.preValidServicerLogin(identity)
    return status
  }

  @Mutation
  private SET_SERVICER_TOKEN(data: ServicerTokenResponse) {
    this.servicerToken = data
  }

  @Mutation
  private SET_CURRENT_USER_SERVICER(currentUserServicerList: Array<ServicerDto>) {
    this.currentUserServicerList = currentUserServicerList
  }

  @Mutation
  private SET_CURRENT_MICRO_CONTEXT(microContext: MicroContext) {
    this.currentContext = microContext
  }

  get authDomain() {
    // return ConfigCenter.getIngressByName('ingress.auth') || ''
    return ''
  }

  get getServicerToken() {
    return this.servicerToken
  }

  get getCurrentUserServicerList() {
    return this.currentUserServicerList
  }

  /**
   * 返回服务商分组列表
   */
  get getCurrentUserServicerGroup() {
    const dictionary = _.groupBy(this.currentUserServicerList, 'servicerType')
    const list = new Array<CurrentUserServicerGroup>()
    for (const key in dictionary) {
      const element = new CurrentUserServicerGroup()
      element.servicerType = Number(key)
      element.currentUserServicerList = dictionary[key]
      list.push(element)
    }
    return list
  }

  get getCurrentContext() {
    return this.currentContext
  }
}

export default getModule(AuthModule)
