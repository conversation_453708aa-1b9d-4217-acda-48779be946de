<route-meta>
{
"title": "专题名称筛选"
}
</route-meta>
<template>
  <el-input clearable :placeholder="placeholder" v-model="specialName" />
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    specialName = ''
    @Prop({
      type: String,
      default: '请输入专题进行查询'
    })
    placeholder: string
    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.specialName = val
    }

    @Emit('input')
    @Watch('specialName')
    specialNameChange() {
      return this.specialName
    }
  }
</script>
