import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 期别培训时段类型枚举
 * by_issue_courses 创建课表根据课表读取
 * custom 不创建课表自定义培训时段
 */
export enum IssueTrainingDateTypeEnum {
  by_issue_courses = 'external',
  custom = 'custom'
}

/**
 * @description 期别培训时段类型
 */
class IssueTrainingDateType extends AbstractEnum<IssueTrainingDateTypeEnum> {
  static enum = IssueTrainingDateTypeEnum

  constructor(status?: IssueTrainingDateTypeEnum) {
    super()
    this.current = status
    this.map.set(IssueTrainingDateTypeEnum.by_issue_courses, '创建课表根据课表读取')
    this.map.set(IssueTrainingDateTypeEnum.custom, '不创建课表自定义培训时段')
  }
}

export default new IssueTrainingDateType()
