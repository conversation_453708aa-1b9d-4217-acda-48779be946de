/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-21 14:21:05
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-04-21 17:06:17
 */
import MsOrder, {
  BatchApplyOnlinePaymentResponse,
  BatchOrderOfflinePaymentAndInvoiceRequest,
  BatchOrderOfflinePaymentRequest,
  BatchOrderOnlinePaymentAndApplyInvoiceRequest,
  BatchOrderOnlinePaymentRequest,
  PrepareBatchPayResponse,
  UpdateBatchOrderOfflinePaymentVoucherRequest
} from '@api/ms-gateway/ms-order-v1'
import Payment from '@api/ms-gateway/ms-payment-v1'
import InvoiceConfig from '@api/service/common/trade-config/query/QueryInvoiceData'
import { Response, ResponseStatus } from '@hbfe/common'
export class PayInfo {
  /**
   * 批次单号
   */
  batchOrderNo?: string
  /**
   * 支付渠道ID
   */
  paymentChannelId?: string
  /**
   * 支付成功后跳转地址
   */
  pageUrl?: string
  /**
   * 支付描述
   */
  description?: string
  /**
   * 发票信息
   */
  invoiceConfig = new InvoiceConfig()
}
class MutationBatchOrderRefund {
  batchOrderNo = ''
  payInfo = new PayInfo()
  constructor(batchOrderNo: string) {
    this.batchOrderNo = batchOrderNo
  }
  /**
   * 准备集体缴费付款
   * @returns
   */
  async prepareCollectivePay(): Promise<PrepareBatchPayResponse> {
    const { data } = await MsOrder.prepareBatchOrderPay({
      batchOrderNo: this.batchOrderNo,
      purchaseChannelTerminal: 'Web',
      purchaseChannelType: 2
    })
    return data
  }

  /**
   * 获取集体报名状态
   */
  async findBatchOrderBatchPayStatus() {
    const { data } = await MsOrder.findBatchOrderBatchPayStatus(this.batchOrderNo)
    return data
  }
  /**
   * 获取集体报名订单状态
   */
  async prepareBatchOrderRepay(batchOrderNo: string) {
    const { data } = await Payment.prepareBatchOrderRepay(batchOrderNo)
    return data
  }
  /**
   * 集体缴费付款 -- 线上
   * @returns
   */
  async onLinePayCollectivePaySignup(): Promise<Response<BatchApplyOnlinePaymentResponse>> {
    const batchOrderOnlinePaymentRequest = new BatchOrderOnlinePaymentRequest()
    Object.assign(batchOrderOnlinePaymentRequest, this.payInfo)
    batchOrderOnlinePaymentRequest.terminalCode = 'Web'
    if (this.payInfo.paymentChannelId === 'WXPAY') {
      //
      batchOrderOnlinePaymentRequest.paymentProperties = [
        {
          key: 'method',
          value: 'native'
        }
      ]
    } else if (this.payInfo.paymentChannelId === 'ALIPAY') {
      batchOrderOnlinePaymentRequest.paymentProperties = [
        {
          key: 'method',
          value: 'alipay.trade.page.pay'
        }
      ]
    } else if (this.payInfo.paymentChannelId === 'CIB_PAY') {
      batchOrderOnlinePaymentRequest.paymentProperties = [
        {
          key: 'method',
          value: 'unifiedTradeNative'
        }
      ]
    } else if (this.payInfo.paymentChannelId === 'CCB_PAY') {
      batchOrderOnlinePaymentRequest.paymentProperties = [
        {
          key: 'method',
          value: 'unifiedTradeNative'
        }
      ]
    } else if (this.payInfo.paymentChannelId === 'SWIFT_PASS_PAY') {
      batchOrderOnlinePaymentRequest.paymentProperties = [
        {
          key: 'method',
          value: 'unifiedTradeNative'
        },
        { key: 'mch_create_ip', value: '1' }
      ]
    } else if (this.payInfo.paymentChannelId === 'NEW_LAND_PAY') {
      batchOrderOnlinePaymentRequest.paymentProperties = [
        {
          key: 'method',
          value: 'unifiedTradeNative'
        }
      ]
    }
    const reason = await MsOrder.onlinePayBatchOrder(batchOrderOnlinePaymentRequest)
    return reason
  }
  /**
   * 集体缴费付款 -- 线下  1.0.0版本
   * @returns
   */
  async offLinePayCollectivePaySignup(
    batchOrderOfflinePayParam: BatchOrderOfflinePaymentRequest
  ): Promise<ResponseStatus> {
    let response = new ResponseStatus(200)
    const { status, data } = await MsOrder.offlinePayBatchOrder(batchOrderOfflinePayParam)
    if (status.isSuccess()) {
      if (data.code != '200') {
        response = new ResponseStatus(Number(data.code), data.message)
      }
    } else {
      response = status
    }
    return response
  }
  /**
   * 批次单更新线下汇款凭证
   * @returns
   */
  async updateBatchOfflinePaymentVouchers(
    batchOrderOfflinePayParam: UpdateBatchOrderOfflinePaymentVoucherRequest
  ): Promise<ResponseStatus> {
    let response = new ResponseStatus(200)
    const { status, data } = await MsOrder.updateBatchOfflinePaymentVouchers(batchOrderOfflinePayParam)
    if (status.isSuccess()) {
      if (data.code != '200') {
        response = new ResponseStatus(Number(data.code), data.message)
      }
    } else {
      response = status
    }
    return response
  }

  /**
   * 集体缴费付款+申请开票 -- 线上
   * @returns
   */
  async onlinePayBatchOrderAndApplyInvoice(): Promise<Response<BatchApplyOnlinePaymentResponse>> {
    const batchOrderOnlinePaymentRequest = new BatchOrderOnlinePaymentAndApplyInvoiceRequest()
    batchOrderOnlinePaymentRequest.batchOrderNo = this.payInfo.batchOrderNo
    batchOrderOnlinePaymentRequest.description = this.payInfo.description
    batchOrderOnlinePaymentRequest.pageUrl = this.payInfo.pageUrl
    batchOrderOnlinePaymentRequest.paymentChannelId = this.payInfo.paymentChannelId
    batchOrderOnlinePaymentRequest.terminalCode = 'Web'
    if (this.payInfo.paymentChannelId === 'WXPAY') {
      //
      batchOrderOnlinePaymentRequest.paymentProperties = [
        {
          key: 'method',
          value: 'native'
        }
      ]
    } else if (this.payInfo.paymentChannelId === 'ALIPAY') {
      batchOrderOnlinePaymentRequest.paymentProperties = [
        {
          key: 'method',
          value: 'alipay.trade.page.pay'
        }
      ]
    } else if (this.payInfo.paymentChannelId === 'CIB_PAY') {
      batchOrderOnlinePaymentRequest.paymentProperties = [
        {
          key: 'method',
          value: 'unifiedTradeNative'
        }
      ]
    } else if (this.payInfo.paymentChannelId === 'CCB_PAY') {
      batchOrderOnlinePaymentRequest.paymentProperties = [
        {
          key: 'method',
          value: 'unifiedTradeNative'
        }
      ]
    } else if (this.payInfo.paymentChannelId === 'SWIFT_PASS_PAY') {
      batchOrderOnlinePaymentRequest.paymentProperties = [
        {
          key: 'method',
          value: 'unifiedTradeNative'
        },
        { key: 'mch_create_ip', value: '1' }
      ]
    }
    /**
     * 开票需要赋值
     * payInfo.invoiceConfig.invoiceData和payInfo.invoiceConfig.distribution
     */
    if (this.payInfo.invoiceConfig.batchOrderNo) {
      batchOrderOnlinePaymentRequest.invoiceInfo = this.payInfo.invoiceConfig.doReference()
    }
    const reason = await MsOrder.onlinePayBatchOrderAndApplyInvoice(batchOrderOnlinePaymentRequest)
    return reason
  }

  /**
   * 集体缴费付款+申请开票 -- 线下
   * @returns
   */
  async offlinePayBatchOrderAndApplyInvoice(
    batchOrderOfflinePayParam: BatchOrderOfflinePaymentRequest
  ): Promise<ResponseStatus> {
    let response = new ResponseStatus(200)
    const request: BatchOrderOfflinePaymentAndInvoiceRequest = batchOrderOfflinePayParam
    /**
     * 开票需要赋值
     * payInfo.invoiceConfig.invoiceData和payInfo.invoiceConfig.distribution
     */
    if (this.payInfo.invoiceConfig.batchOrderNo) {
      request.invoiceInfo = this.payInfo.invoiceConfig.doReference()
    }
    const { status, data } = await MsOrder.offlinePayBatchOrderAndApplyInvoice(batchOrderOfflinePayParam)
    if (status.isSuccess()) {
      if (data.code != '200') {
        response = new ResponseStatus(Number(data.code), data.message)
      }
    } else {
      response = status
    }
    return response
  }
}
export default MutationBatchOrderRefund
