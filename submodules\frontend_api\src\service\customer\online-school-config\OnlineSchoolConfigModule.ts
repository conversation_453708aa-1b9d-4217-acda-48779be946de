import store from '@/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import QueryOnlineSchoolPortalConfigFactory from '@api/service/customer/online-school-config/online-school-partal-config/QueryOnlineSchoolPortalConfigFactory'
import QueryColumn from '@api/service/customer/online-school-config/column/query/QueryColumn'

@Module({
  name: 'CustomerOnlineSchoolConfigModule',
  dynamic: true,
  namespaced: true,
  store
})
class OnlineSchoolConfigModule extends VuexModule {
  queryColumnConfig = QueryColumn
  /**
   * @description 查询网校配置工厂类
   */
  queryOnlineSchoolConfigFactory: QueryOnlineSchoolPortalConfigFactory = new QueryOnlineSchoolPortalConfigFactory()
}

export default getModule(OnlineSchoolConfigModule)
