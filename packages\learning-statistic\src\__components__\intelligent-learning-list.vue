<template>
  <div>
    <el-row :gutter="16" class="m-query f-mt20">
      <el-form :inline="true" label-width="auto">
        <el-col :span="5">
          <el-form-item label="姓名">
            <el-input v-model="intelligenceAbnormalList.queryParams.name" clearable placeholder="请输入学员姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="证件号">
            <el-input v-model="intelligenceAbnormalList.queryParams.idCard" clearable placeholder="请输入证件号" />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="手机号">
            <el-input v-model="intelligenceAbnormalList.queryParams.phone" clearable placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="培训方案">
            <el-input
              v-model="intelligenceAbnormalList.queryParams.schemeName"
              clearable
              placeholder="请输入培训方案"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4" class="f-fr">
          <el-form-item class="f-tr">
            <el-button type="primary" @click="search()">查询</el-button>
            <el-button @click="reset()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <el-table stripe :data="intelligenceAbnormalList.list" class="m-table" max-height="650px" ref="tableRef">
      <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
      <el-table-column label="学员信息" min-width="250">
        <template #default="scope">
          <p>姓名：{{ scope.row.name }}</p>
          <p>手机号：{{ scope.row.phone }}</p>
          <p>证件号：{{ scope.row.idCard }}</p>
        </template>
      </el-table-column>
      <el-table-column label="培训方案名称" min-width="240" #default="scope">
        <template>{{ scope.row.schemeName }}</template>
      </el-table-column>
      <el-table-column label="操作时间" min-width="170">
        <template #default="scope">{{ scope.row.operationTime }}</template>
      </el-table-column>
      <el-table-column label="失败原因" min-width="180">
        <template #default="scope">{{ scope.row.failReason }}</template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="page" v-bind="page"> </hb-pagination>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import IntelligenceAbnormalList from '@api/service/management/statisticalReport/query/IntelligenceAbnormalList'
  import IntelligenceAbnormalParams from '@api/service/management/statisticalReport/query/vo/IntelligenceAbnormalParams'
  import { ElTable } from 'element-ui/types/table'
  @Component
  export default class extends Vue {
    @Ref('tableRef') tableRef: ElTable
    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }
    // 分页
    page: UiPage
    // 实例化列表模型
    intelligenceAbnormalList = new IntelligenceAbnormalList()
    created() {
      console.log('激活选项-0-')
      this.search()
    }

    search() {
      this.page.currentChange(1)
    }

    async doSearch() {
      try {
        const res = await this.intelligenceAbnormalList.queryList(this.page)
        if (res.isSuccess()) {
          // 查询成功
        } else {
          this.$message.error(res.getMessage())
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.$nextTick(() => {
          this.tableRef.doLayout()
        })
      }
    }

    // 重置
    reset() {
      this.intelligenceAbnormalList.queryParams = new IntelligenceAbnormalParams()
      this.search()
    }
  }
</script>
