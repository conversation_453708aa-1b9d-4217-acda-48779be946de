export enum frontendApplicationDiff {
  gstybServicerId = 'frontend_application_diff.gstybServicerId',
  regionTransfer = 'frontend_application_diff.regionTransfer',
  qztgServicerId = 'frontend_application_diff.qztgServicerId',
  hywXmlgFxAddress = 'frontend_application_diff.hywXmlgFxAddress',
  gszjGlzZtId = 'frontend_application_diff.gszjGlzZtId',
  zjzjServicerId = 'frontend_application_diff.zjzjServicerId',
  hywTransferTelephone = 'frontend_application_diff.hywTransferTelephone',
  xmlgHywZtId = 'frontend_application_diff.xmlgHywZtId',
  fjzjAddress = 'frontend_application_diff.fjzjAddress',
  zzttHywZtId = 'frontend_application_diff.zzttHywZtId',
  aboutUsInfoId = 'frontend_application_diff.aboutUsInfoId',
  hywFjzjFxAddress = 'frontend_application_diff.hywFjzjFxAddress',
  gszjManagementCertificateAddress = 'frontend_application_diff.gszjManagementCertificateAddress',
  fjzjHywZtId = 'frontend_application_diff.fjzjHywZtId',
  ztRegionTransfer = 'frontend_application_diff.ztRegionTransfer',
  hywZzFxAddress = 'frontend_application_diff.hywZzFxAddress',
  xmlgHywFxId = 'frontend_application_diff.xmlgHywFxId',
  lmdxServicerId = 'frontend_application_diff.lmdxServicerId',
  zzttHywFxId = 'frontend_application_diff.zzttHywFxId',
  hywSchemeId = 'frontend_application_diff.hywSchemeId',
  noNeedPopUpTemplateId = 'frontend_application_diff.noNeedPopUpTemplateId',
  hywXmlgAddress = 'frontend_application_diff.hywXmlgAddress',
  hywZzttAddress = 'frontend_application_diff.hywZzttAddress',
  hnzjManagementAddress = 'frontend_application_diff.hnzjManagementAddress',
  fjzjHywFxId = 'frontend_application_diff.fjzjHywFxId',
  zzttServicerId = 'frontend_application_diff.zzttServicerId'
}
export enum ingress {
  resource = 'ingress.resource',
  apiNodebugEndpoint = 'ingress.api-nodebug-endpoint',
  auth = 'ingress.auth',
  hbfeAppHost = 'ingress.hbfe.app.host',
  appssoIpPort = 'ingress.appsso.ip-port',
  payment = 'ingress.payment',
  paymentreceive = 'ingress.paymentreceive',
  learningHwyAudioHost = 'ingress.learning.hwyAudioHost',
  tinyurl = 'ingress.tinyurl',
  opsswopAddress = 'ingress.opsswop.address',
  sso = 'ingress.sso',
  anticheat = 'ingress.anticheat',
  oprman = 'ingress.oprman',
  apigateway = 'ingress.apigateway',
  learningHwyStreamHost = 'ingress.learning.hwyStreamHost',
  cert = 'ingress.cert',
  tinyurlToken = 'ingress.tinyurl.token',
  hbHost = 'ingress.hb.host',
  oprmanIpPort = 'ingress.oprman.ip-port',
  authIpPort = 'ingress.auth.ip-port',
  apiendpoint = 'ingress.apiendpoint',
  exam = 'ingress.exam',
  resourceIpPort = 'ingress.resource.ip-port',
  bill = 'ingress.bill',
  hwResource = 'ingress.hw.resource',
  study = 'ingress.study',
  apiendpointIpPort = 'ingress.apiendpoint.ip-port',
  appsso = 'ingress.appsso',
  ssoIpPort = 'ingress.sso.ip-port'
}
export enum frontendCommon {
  mfsHost = 'frontend_common.mfsHost',
  obsFileSizeAsBigFile = 'frontend_common.obs.fileSizeAsBigFile',
  mfsSubprojectAddress = 'frontend_common.mfs.subproject.address'
}
export enum frontendApplication {
  hljysServicerId = 'frontend_application.hljysServicerId',
  webFxRoute = 'frontend_application.webFxRoute',
  ahjxjymisDomain = 'frontend_application.ahjxjymisDomain',
  wechatBindToken = 'frontend_application.wechatBindToken',
  superLoginToken = 'frontend_application.superLoginToken',
  specialAdminPhoneLoginToken = 'frontend_application.specialAdminPhoneLoginToken',
  loginAccountServicerIds = 'frontend_application.loginAccountServicerIds',
  beforeVideoSecond = 'frontend_application.beforeVideoSecond',
  txMapKey = 'frontend_application.txMapKey',
  distributionAdministratorChangePhoneToken = 'frontend_application.distributionAdministratorChangePhoneToken',
  noAllowPushSchemeIds = 'frontend_application.noAllowPushSchemeIds',
  hljjxjymisDomain = 'frontend_application.hljjxjymisDomain',
  useHttpDomain = 'frontend_application.useHttpDomain',
  specialAdminChangePhoneNumToken = 'frontend_application.specialAdminChangePhoneNumToken',
  gymsServicerId = 'frontend_application.gymsServicerId',
  xmlgServicerId = 'frontend_application.xmlgServicerId',
  aboutUsInfoId = 'frontend_application.aboutUsInfoId',
  useOtherSpecialTemplate = 'frontend_application.useOtherSpecialTemplate',
  rzptAdminLoginToken = 'frontend_application.rzptAdminLoginToken',
  dockServicerIds = 'frontend_application.dockServicerIds',
  wxOpenIdToken = 'frontend_application.wxOpenIdToken',
  scanRedirect = 'frontend_application.scanRedirect',
  mfsSubprojectAddress = 'frontend_application.mfs.subproject.address',
  gsjczjSpecialSubjectId = 'frontend_application.gsjczjSpecialSubjectId',
  jxgxPlayDialogTime = 'frontend_application.jxgxPlayDialogTime',
  gsszzjSpecialSubjectId = 'frontend_application.gsszzjSpecialSubjectId',
  studentForgotPasswordToken = 'frontend_application.studentForgotPasswordToken',
  gszjServicerId = 'frontend_application.gszjServicerId',
  superChangePhoneNumToken = 'frontend_application.superChangePhoneNumToken',
  studentLoginToken = 'frontend_application.studentLoginToken',
  studentPhoneNumLoginToken = 'frontend_application.studentPhoneNumLoginToken',
  distinguishSchemeTypeTime = 'frontend_application.distinguishSchemeTypeTime',
  apiendpoint = 'frontend_application.apiendpoint',
  ssoDomain = 'frontend_application.ssoDomain',
  mqzjServicerId = 'frontend_application.mqzjServicerId',
  anxiServicerId = 'frontend_application.anxiServicerId',
  distributionAdministratorForgetPwdToken = 'frontend_application.distributionAdministratorForgetPwdToken',
  sunProjectForgotPasswordToken = 'frontend_application.sunProjectForgotPasswordToken',
  gslnzjSpecialSubjectId = 'frontend_application.gslnzjSpecialSubjectId',
  requestTimeoutSecond = 'frontend_application.requestTimeoutSecond',
  adminEventTrack = 'frontend_application.adminEventTrack',
  specialAdminPwdLoginToken = 'frontend_application.specialAdminPwdLoginToken',
  skipTimeVerification = 'frontend_application.skipTimeVerification',
  uniappEventTrack = 'frontend_application.uniappEventTrack',
  klmyServicerId = 'frontend_application.klmyServicerId',
  regionLoginToken = 'frontend_application.regionLoginToken',
  collectiveRegistrationForgotPasswordToken = 'frontend_application.collectiveRegistrationForgotPasswordToken',
  gsgnzjSpecialSubjectId = 'frontend_application.gsgnzjSpecialSubjectId',
  regionForgotPasswordToken = 'frontend_application.regionForgotPasswordToken',
  czscPdfUrl = 'frontend_application.czscPdfUrl',
  uniappMonitor = 'frontend_application.uniappMonitor',
  studentChangePhoneNumToken = 'frontend_application.studentChangePhoneNumToken',
  trainingInstitutionDomain = 'frontend_application.trainingInstitutionDomain',
  gszjManagementAddress = 'frontend_application.gszjManagementAddress',
  scjzsServicerId = 'frontend_application.scjzsServicerId',
  onlyWeixinServicerIds = 'frontend_application.onlyWeixinServicerIds',
  collectiveRegistrationLoginToken = 'frontend_application.collectiveRegistrationLoginToken',
  dockThirdPartyServicerIds = 'frontend_application.dockThirdPartyServicerIds',
  persistentDataTime = 'frontend_application.persistentDataTime',
  collectiveRegisterToken = 'frontend_application.collectiveRegisterToken',
  customerMonitor = 'frontend_application.customerMonitor',
  nyyzServicerId = 'frontend_application.nyyzServicerId',
  regionAdminLoginToken = 'frontend_application.regionAdminLoginToken',
  sunProjectChangePhoneNumToken = 'frontend_application.sunProjectChangePhoneNumToken',
  fjzjServicerId = 'frontend_application.fjzjServicerId',
  mfsHost = 'frontend_application.mfsHost',
  byzjServicerId = 'frontend_application.byzjServicerId',
  collectiveRegistrationChangePhoneNumToken = 'frontend_application.collectiveRegistrationChangePhoneNumToken',
  newRedirectSwitch = 'frontend_application.newRedirectSwitch',
  adminMonitor = 'frontend_application.adminMonitor',
  gsxyyjServierId = 'frontend_application.gsxyyjServierId',
  regionChangePhoneNumToken = 'frontend_application.regionChangePhoneNumToken',
  sunProjectPhoneNumLoginToken = 'frontend_application.sunProjectPhoneNumLoginToken',
  historyTrainingArchivesTrainPlatformInfo = 'frontend_application.historyTrainingArchivesTrainPlatformInfo',
  webfunnySwitch = 'frontend_application.webfunnySwitch',
  noNeedPerfectServicerIds = 'frontend_application.noNeedPerfectServicerIds',
  zzzjServicerId = 'frontend_application.zzzjServicerId',
  qztgServicerId = 'frontend_application.qztgServicerId',
  jxgxServicerId = 'frontend_application.jxgxServicerId',
  superPhoneNumLoginToken = 'frontend_application.superPhoneNumLoginToken',
  customerEventTrack = 'frontend_application.customerEventTrack',
  ahzjServicerId = 'frontend_application.ahzjServicerId',
  h5FxRoute = 'frontend_application.h5FxRoute',
  studentImproveInfoToken = 'frontend_application.studentImproveInfoToken',
  distributionAdministratorPhoneLoginToken = 'frontend_application.distributionAdministratorPhoneLoginToken',
  zzkdServicerId = 'frontend_application.zzkdServicerId',
  specialAdminRoleId = 'frontend_application.specialAdminRoleId',
  scysServicerId = 'frontend_application.scysServicerId',
  trainingInstitutionServicerId = 'frontend_application.trainingInstitutionServicerId',
  historyCertificateConfig = 'frontend_application.historyCertificateConfig',
  tybServierId = 'frontend_application.tybServierId',
  distributionAdministratorPwdLoginToken = 'frontend_application.distributionAdministratorPwdLoginToken',
  cjwtPdfUrl = 'frontend_application.cjwtPdfUrl',
  collectiveRegistrationPhoneNumLoginToken = 'frontend_application.collectiveRegistrationPhoneNumLoginToken',
  registerToken = 'frontend_application.registerToken',
  specialAdminForgotPasswordToken = 'frontend_application.specialAdminForgotPasswordToken',
  scanURL = 'frontend_application.scanURL',
  yzzjServicerId = 'frontend_application.yzzjServicerId',
  paymentHost = 'frontend_application.paymentHost',
  sjdbServicerId = 'frontend_application.sjdbServicerId',
  sunProjectSuperLoginToken = 'frontend_application.sunProjectSuperLoginToken',
  superForgotPasswordToken = 'frontend_application.superForgotPasswordToken',
  singleLoginToken = 'frontend_application.singleLoginToken',
  hideOnlineAndOfflineTeach = 'frontend_application.hideOnlineAndOfflineTeach',
  studentGetPhoneNumToken = 'frontend_application.studentGetPhoneNumToken',
  collectiveGetPhoneNumToken = 'frontend_application.collectiveGetPhoneNumToken'
}
export enum frontend {
  mfsSubprojectAddress = 'frontend.mfs.subproject.address',
  mfsHost = 'frontend.mfsHost'
}
