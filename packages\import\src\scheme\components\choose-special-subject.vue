<!--
 * @Author: z张仁榕
 * @Date: 2025-01-21 17:09:50
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-02-28 11:36:54
 * @Description: 
-->
<template>
  <div>
    <el-card shadow="never" class="m-card f-mb15">
      <!--选择专题-->
      <el-drawer title="选择专题" :visible.sync="isShow" size="1200px" custom-class="m-drawer">
        <div class="drawer-bd">
          <!--显示5个，超出部分隐藏-->
          <el-row :gutter="16" class="m-query f-mt10">
            <el-form :inline="true" label-width="auto">
              <el-col :span="8">
                <el-form-item label="专题名称">
                  <el-input v-model="queryParams.subjectName" clearable placeholder="请输入专题名称" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="专题入口名称">
                  <el-input v-model="queryParams.entryName" clearable placeholder="请输入专题入口名称" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="专题类型">
                  <el-select clearable placeholder="请选择专题类型" v-model="queryParams.subjectType" multiple>
                    <el-option label="地区" :value="1">地区</el-option>
                    <el-option label="行业" :value="2">行业</el-option>
                    <el-option label="单位" :value="3">单位</el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="isUnitSelected">
                <el-form-item label="所属单位">
                  <el-input v-model="queryParams.unitName" clearable placeholder="请输入单位名称" />
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="isIndustrySelected">
                <el-form-item label="行业">
                  <industry-select
                    v-model="thematicManagementList.queryParam.suiteIndustry"
                    @industryInfos="handleIndustryInfos"
                  ></industry-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="isRegionSelected">
                <el-form-item label="地区">
                  <biz-national-region
                    v-model="thematicManagementList.queryParam.suiteAreaLists"
                    :check-strictly="true"
                    placeholder="请选择地区"
                  ></biz-national-region>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="专题状态">
                  <el-select clearable placeholder="请选择专题状态" v-model="queryParams.enable">
                    <el-option label="停用" :value="false">停用</el-option>
                    <el-option label="启用" :value="true">启用</el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="显示在网校门户">
                  <el-select v-model="queryParams.displayInSchool" clearable placeholder="请选择是否显示在网校门户">
                    <el-option label="显示" :value="true">显示</el-option>
                    <el-option label="不显示" :value="false">不显示</el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="编辑时间">
                  <double-date-picker
                    :begin-create-time.sync="queryParams.modifyStartTime"
                    :end-create-time.sync="queryParams.modifyEndTime"
                    begin-time-placeholder="开始日期"
                    end-time-placeholder="结束日期"
                  ></double-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="f-fr">
                <el-form-item class="f-tr">
                  <el-button type="primary" @click="searchBase">查询</el-button>
                  <el-button @click="resetCondition">重置</el-button>
                  <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>

          <!-- 表格 -->
          <el-table stripe :data="thematicManagementList.list" max-height="500px" class="m-table" v-loading="loading">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="专题名称" min-width="150">
              <template slot-scope="scope">{{ scope.row.basicInfo.subjectName }}</template>
            </el-table-column>
            <el-table-column label="专题入口名称" min-width="150">
              <template slot-scope="scope">{{ scope.row.basicInfo.entryName }}</template>
            </el-table-column>
            <el-table-column label="专题类型" min-width="150">
              <template slot-scope="scope">
                <el-tooltip placement="top" effect="light" v-if="scope.row.basicInfo.suiteIndustry">
                  <div slot="content">
                    <i class="f-c4">
                      <el-tag type="warning" size="mini" class="f-mr5">行业</el-tag
                      >{{ scope.row.basicInfo.suiteIndustry }}</i
                    >
                  </div>
                  <el-button type="text" class="f-to-three">
                    <i class="f-c4">
                      <el-tag type="warning" size="mini" class="f-mt5"> 行业 </el-tag>
                      {{ scope.row.basicInfo.suiteIndustry }}</i
                    >
                  </el-button>
                </el-tooltip>
                <el-tooltip placement="top" effect="light" v-if="scope.row.basicInfo.suiteArea">
                  <div slot="content">
                    <i class="f-c4"
                      ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag
                      >{{ scope.row.basicInfo.suiteArea }}</i
                    >
                  </div>
                  <el-button type="text" class="f-to-three"
                    ><i class="f-c4"
                      ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag
                      >{{ scope.row.basicInfo.suiteArea }}</i
                    ></el-button
                  >
                </el-tooltip>
                <el-tooltip placement="top" effect="light" v-if="scope.row.basicInfo.unitName">
                  <div slot="content">
                    <i class="f-c4"
                      ><el-tag type="warning" size="mini" class="f-mr5">单位</el-tag
                      >{{ scope.row.basicInfo.unitName }}</i
                    >
                  </div>
                  <el-button type="text" class="f-to-three"
                    ><i class="f-c4"
                      ><el-tag type="warning" size="mini" class="f-mt5">单位</el-tag
                      >{{ scope.row.basicInfo.unitName }}</i
                    ></el-button
                  >
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="显示在网校" min-width="120" align="center">
              <template slot-scope="scope">
                <div v-if="scope.row.basicInfo.displayInSchool === true">显示</div>
                <div v-else>不显示</div>
              </template>
            </el-table-column>
            <el-table-column label="状态" min-width="100">
              <template slot-scope="scope">
                <div v-if="scope.row.enable === true">
                  <el-badge is-dot type="success" class="badge-status">启用</el-badge>
                </div>
                <div v-else>
                  <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="最新编辑时间" min-width="180">
              <template slot-scope="scope">{{ scope.row.lastEditTime }}</template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="chooseItem(scope.row)">选择</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="page" v-bind="page"> </hb-pagination>
        </div>
      </el-drawer>
    </el-card>
  </div>
</template>
<script lang="ts">
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import TrainClassSchemeType, {
    TrainClassSchemeEnum
  } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
  import QueryReplaceableTrainClassListVo from '@api/service/management/train-class/query/vo/QueryReplaceableTrainClassListVo'
  import ReplaceableTrainClassDetailVo from '@api/service/management/train-class/query/vo/ReplaceableTrainClassDetailVo'
  import { Query, UiPage } from '@hbfe/common'
  import BizAccounttypeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-accounttype-select.vue'
  import BizIndustrySelect from '@hbfe/jxjy-admin-components/src/biz/biz-industry-select.vue'
  import BizMajorCascader from '@hbfe/jxjy-admin-components/src/biz/biz-major-cascader.vue'
  import BizMajorSelect from '@hbfe/jxjy-admin-components/src/biz/biz-major-select.vue'
  import BizNationalRegion from '@hbfe/jxjy-admin-components/src/biz/biz-national-region.vue'
  import BizTechnicalGradeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-technical-grade-select.vue'
  import BizTrainingCategorySelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-category-select.vue'
  import BizYearSelect from '@hbfe/jxjy-admin-components/src/biz/biz-year-select.vue'
  import CreateExchangeOrderModel from '@hbfe/jxjy-admin-customerService/src/personal/components/model/CreateExchangeOrderModel'
  import UITrainClassCommodityDetail from '@hbfe/jxjy-admin-scheme/src/models/UITrainClassCommodityDetail'
  import { Component, Ref, Vue } from 'vue-property-decorator'

  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import { SubjectType } from '@api/service/management/thematic-management/enum/SubjectType'
  import ThematicManagementQueryParam from '@api/service/management/thematic-management/model/ThematicManagementQueryParam'
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
  import ThematicManagementList from '@api/service/management/thematic-management/ThematicManagementList'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import IndustrySelect from '@hbfe/jxjy-admin-specialTopics/src/add/components/industry-select.vue'

  // sku绑定模型
  class SchemeSkuProperty {
    year: string
    region: string[]
    industry: string
    subjectType: string
    trainingCategory: string
    societyTrainingMajor: string[]
    constructionTrainingMajor: string
    jobLevel: string
    schemeName: string
    positionCategory: string
    trainingObject: string
    grade: string
    subject: string
  }

  @Component({
    components: {
      BizMajorSelect,
      BizTrainingCategorySelect,
      BizMajorCascader,
      BizAccounttypeSelect,
      BizIndustrySelect,
      BizNationalRegion,
      BizYearSelect,
      BizTechnicalGradeSelect,
      DoubleDatePicker,
      IndustrySelect
    }
  })
  export default class extends Vue {
    @Ref('industrySelect') industrySelect: BizIndustrySelect
    isShow = false
    schemeTypeInfo = new Array<string>()
    SubjectType = SubjectType

    // 查询接口入口
    // queryRequestEntrance: QueryExchangeTrainClass = TrainClassManagerModule.queryTrainClassFactory.getQueryExchangeTrainClass()

    // 分页 - 可换班列表
    page: UiPage
    // 查询 - 可换班列表
    query: Query = new Query()
    // 查询参数 - 可换班列表
    // queryParams: QueryReplaceableTrainClassListVo = new QueryReplaceableTrainClassListVo()

    // 专题查询参数
    queryParams: ThematicManagementQueryParam = new ThematicManagementQueryParam()

    thematicManagementList: ThematicManagementList = new ThematicManagementList()

    // 列表 - 可换班列表
    replaceableTrainClassList: ReplaceableTrainClassDetailVo[] = []

    // 培训方案类型列表
    trainClassSchemeTypeList: EnumOption<TrainClassSchemeEnum>[] = TrainClassSchemeType.list()

    // 剔除培训班商品id集合
    excludeCommoditySkuIdList: string[] = []

    /**
     * 【培训班列表】价格
     */
    price: number = null

    /**
     * 培训方案名称
     */
    schemeName = ''

    //region sku

    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuProperty = {
      /**
       * 年度
       */
      year: '',
      /**
       * 培训方案名称
       */
      schemeName: '',
      /**
       * 地区
       */
      region: [] as string[],
      /**
       * 行业
       */
      industry: '',
      /**
       * 科目类型
       */
      subjectType: '',
      /**
       * 培训类别
       */
      trainingCategory: '',
      /**
       * 培训专业 - 建设行业
       */
      constructionTrainingMajor: '',
      /**
       * 培训专业 - 人社行业
       */
      societyTrainingMajor: [] as string[],
      /**
       * 技术等级 - 工勤行业
       **/
      jobLevel: '',
      /**
       * 培训对象
       */
      trainingObject: '',
      /**
       * 岗位类别
       */
      positionCategory: '',
      /**
       * 学段
       */
      grade: '',
      /**
       * 学科
       */
      subject: ''
    } as SchemeSkuProperty

    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''

    /**
     * 培训类别Id
     */
    trainingCategoryId = ''

    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: true,
      // 培训类别
      trainingCategory: true,
      // 技术等级
      jobLevel: true,
      // 培训对象
      trainingObject: true,
      //   岗位类别
      positionCategory: true,
      //学段
      grade: true,
      //学科
      subject: true
    }
    /**
     * 当前网校信息
     */
    envConfig = {
      // 工勤行业
      workServiceId: '',
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 职业卫生行业Id
      occupationalHealthId: '',
      //教师行业
      teacherIndustryId: ''
    }

    input = ''
    value1 = ''
    select = ''
    chooseSubjectType = ''
    topicStatus = ''
    //专题表格loading
    loading = false
    // 是否展示行业选择下拉
    isShowIndustrySelect = false
    // 是否展示地区选择下拉
    isShowRegionSelect = false
    // 当前用户是否拥有专题管理员角色类型
    isZtglyLogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    constructor() {
      super()
      this.page = new UiPage(this.pageReplaceableTrainClass, this.pageReplaceableTrainClass)
    }

    async created() {
      await this.pageReplaceableTrainClass()
    }

    /**
     * 查询（由外部触发）
     */
    async searchByExternal(params: CreateExchangeOrderModel) {
      //   console.log('createExchangeOrder', params)
      this.price = QueryReplaceableTrainClassListVo.validateIsEmpty(params.price) ? params.price : null
      this.schemeName = params.schemeName || ''
      this.excludeCommoditySkuIdList = [] as string[]
      this.excludeCommoditySkuIdList = params.excludeCommoditySkuIdList || ([] as string[])
      // 清空页面绑定属性
      this.localSkuProperty = new SchemeSkuProperty()
      // 清空接口查询属性
      //   this.queryParams = new QueryReplaceableTrainClassListVo()
      //   this.queryParams.price = this.price
      await this.searchBase()
    }

    /**
     * 查询分页
     */
    async searchBase() {
      this.page.pageNo = 1
      await this.pageReplaceableTrainClass()
    }

    /**
     * 查询分页
     */
    async pageReplaceableTrainClass() {
      this.query.loading = true
      this.loading = true
      try {
        // 参数
        this.thematicManagementList.queryParam = this.queryParams
        if (this.isZtglyLogin) {
          await this.queryListInTrainingChannel()
        } else {
          await this.queryList()
        }
      } catch (e) {
        console.log('获取可更换列表失败！', e)
      } finally {
        this.loading = false
        this.query.loading = false
      }
    }

    // 专题选择
    async queryListInTrainingChannel() {
      await this.thematicManagementList.queryListInTrainingChannel(this.page)
    }
    // 网校专题选择
    async queryList() {
      await this.thematicManagementList.queryList(this.page)
    }
    get isRegionSelected() {
      if (this.thematicManagementList.queryParam.subjectType?.includes(1)) {
        return true
      } else {
        this.thematicManagementList.queryParam.suiteAreaLists = new Array<any>()
        return false
      }
    }

    get isIndustrySelected() {
      if (this.thematicManagementList.queryParam.subjectType?.includes(2)) {
        return true
      } else {
        this.thematicManagementList.queryParam.suiteIndustry = null
        return false
      }
    }

    get isUnitSelected() {
      if (this.thematicManagementList.queryParam.subjectType?.includes(3)) {
        return true
      } else {
        this.thematicManagementList.queryParam.unitName = null
        return false
      }
    }

    /**
     * 获取查询参数
     */
    getQueryParams(): QueryReplaceableTrainClassListVo {
      //   console.log(this.schemeTypeInfo, 'this.schemeTypeInfo')
      //   const schemeName = this.queryParams.schemeName
      //   const price = this.queryParams.price
      const queryParams = new QueryReplaceableTrainClassListVo()
      if (this.schemeTypeInfo && this.schemeTypeInfo[1] === 'chooseCourseLearning') {
        queryParams.schemeType = 1
      }
      if (this.schemeTypeInfo && this.schemeTypeInfo[1] === 'autonomousCourseLearning') {
        queryParams.schemeType = 2
      }
      queryParams.schemeName = this.localSkuProperty.schemeName || ''
      //   queryParams.price = price || null
      queryParams.price = this.price
      queryParams.jobLevel = this.localSkuProperty.jobLevel ? [this.localSkuProperty.jobLevel] : ([] as string[])
      queryParams.year = this.localSkuProperty.year ? [this.localSkuProperty.year] : ([] as string[])
      queryParams.region = this.localSkuProperty.region || ([] as string[])
      queryParams.industry = this.localSkuProperty.industry ? [this.localSkuProperty.industry] : ([] as string[])
      queryParams.subjectType = this.localSkuProperty.subjectType
        ? [this.localSkuProperty.subjectType]
        : ([] as string[])
      queryParams.trainingCategory = this.localSkuProperty.trainingCategory
        ? [this.localSkuProperty.trainingCategory]
        : ([] as string[])
      queryParams.trainingProfessional = this.getTrainingProfessional()
      queryParams.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      queryParams.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      queryParams.learningPhase = !this.localSkuProperty.grade ? ([] as string[]) : [this.localSkuProperty.grade]
      queryParams.discipline = !this.localSkuProperty.subject ? ([] as string[]) : [this.localSkuProperty.subject]
      queryParams.excludeCommoditySkuIdList = this.excludeCommoditySkuIdList

      console.log(this.queryParams, 'queryParams')
      return queryParams
    }

    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人社行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }
    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: UITrainClassCommodityDetail, type: string): string {
      if (row.skuValueNameProperty[type]?.skuPropertyName) {
        const value = row.skuValueNameProperty[type].skuPropertyName
        const valuesArr = value.split('/'),
          lastIndex = valuesArr.length - 1
        return type === 'trainingMajor' && !row.isSocietyIndustry ? valuesArr[lastIndex] : value
      }
      return ''
    }

    //选择专题类型的某项展示行业或者地区下拉
    isShowIndustryRegion(value: string) {
      if (value == '1') {
        this.isShowRegionSelect = true
        this.isShowIndustrySelect = false
      } else if (value == '2') {
        this.isShowIndustrySelect = true
        this.isShowRegionSelect = false
      } else {
        return
      }
    }

    // 操作选项-选择按钮
    chooseItem(row: ThematicManagementItem) {
      //将选择的专题传给父组件接受
      this.$emit('select', row)
      //选择后直接关闭弹窗
      this.isShow = false
    }

    /**
     * 重置条件
     */
    async resetCondition() {
      this.localSkuProperty = new SchemeSkuProperty()
      //   this.queryParams = new QueryReplaceableTrainClassListVo()
      //   this.queryParams.price = this.price
      this.queryParams.subjectName = ''
      this.queryParams.entryName = ''
      this.queryParams.enable = undefined
      this.queryParams.displayInSchool = undefined
      this.queryParams.suiteAreaLists = []
      this.queryParams.subjectType = undefined
      this.queryParams.suiteIndustry = ''
      this.queryParams.modifyStartTime = ''
      this.queryParams.modifyEndTime = ''
      this.queryParams.unitName = ''
      this.isShowRegionSelect = false
      this.isShowIndustrySelect = false
      await this.searchBase()
    }

    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      this.localSkuProperty.jobLevel = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.grade = ''
      this.localSkuProperty.subject = ''
    }

    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.SUBJECT_TYPE)
      const configTrainingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.TRAINING_CATEGORY
      )
      const jobLevel = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.JOB_LEVEL)
      const trainingObject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.TRAINING_OBJECT)
      const positionCategory = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.POSITION_CATEGORY)
      const grade = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.LEARNING_PHASE)
      const subject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.DISCIPLINE)
      this.skuVisible.subjectType = configSubjectType > -1
      this.skuVisible.trainingCategory = configTrainingCategory > -1
      this.skuVisible.jobLevel = jobLevel > -1
      this.skuVisible.trainingObject = trainingObject > -1
      this.skuVisible.positionCategory = positionCategory > -1
      this.skuVisible.grade = grade > -1
      this.skuVisible.subject = subject > -1
    }

    /**
     * 响应组件行业Id集合传参
     */
    async handleIndustryInfos(values: any) {
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.occupationalHealthId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
      // 仅当单个行业，需要默认选中
      if (this.industrySelect?.industryOptions?.length === 1) {
        this.localSkuProperty.industry = this.industrySelect.industryOptions[0].id
      }

      //   await this.searchBase()
    }

    //获取专题类型
    // subject(row: ThematicManagementItem, type: string): string {
    //   if (row.basicInfo[type]) {
    //     const value = row.basicInfo[type]
    //     // const valuesArr = value.split('/'),
    //     //   lastIndex = valuesArr.length - 1
    //     if (type == 'subjectType') {
    //       return row.basicInfo[type] == 1 ? '地区' : '行业'
    //     } else if (type == 'subjectTypeValue') {
    //       return value
    //     }
    //   }
    //   return ''
    // }
  }
</script>
