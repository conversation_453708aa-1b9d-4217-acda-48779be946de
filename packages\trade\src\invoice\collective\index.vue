<route-meta>
{
"isMenu": true,
"title": "集体报名发票",
"sort": 2,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main
    v-if="$hasPermission('collectiveBill,collectiveBillZt')"
    desc="collectiveBill:集体报名发票,collectiveBillZt:集体报名发票(专题)"
    actions="collectiveBill:@ElectronicInvoiceAuto,@ElectronicInvoiceOffline,@SpecialInvoice,@InvoiceDistribution#collectiveBillZt:@ElectronicInvoiceAuto,@ElectronicInvoiceOffline,@SpecialInvoice,@InvoiceDistribution"
  >
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky" @tab-click="handleClick">
      <template
        v-if="$hasPermission('invoiceAuto,invoiceAutoZt')"
        desc="invoiceAuto:自动开票,invoiceAutoZt:自动开票(专题)"
        actions="invoiceAuto:@ElectronicInvoiceAuto#invoiceAutoZt:@ElectronicInvoiceAuto"
      >
        <el-tab-pane label="增值税电子普通发票（自动开票）" name="electronic-invoice-auto">
          <electronic-invoice-auto ref="electronicInvoiceAuto"></electronic-invoice-auto>
        </el-tab-pane>
      </template>
      <template
        v-if="$hasPermission('invoiceOffline,invoiceOfflineZt')"
        desc="invoiceOffline:线下开票,invoiceOfflineZt:线下开票(专题)"
        actions="invoiceOffline:@ElectronicInvoiceOffline#invoiceOfflineZt:@ElectronicInvoiceOffline"
      >
        <el-tab-pane label="增值税电子普通发票（线下开票）" name="electronic-invoice-offline">
          <electronic-invoice-offline ref="electronicInvoiceOffline"></electronic-invoice-offline>
        </el-tab-pane>
      </template>
      <template
        v-if="$hasPermission('electronicSpecialInvoice,electronicSpecialInvoiceZt')"
        desc="electronicSpecialInvoice:增值税电子专用发票(线下开票),electronicSpecialInvoiceZt:增值税电子专用发票(专题)"
        actions="electronicSpecialInvoice:@ElectronicSpecialInvoice#electronicSpecialInvoiceZt:@ElectronicSpecialInvoice"
      >
        <el-tab-pane label="增值税电子专用发票(线下开票)" name="electronic-special-invoice">
          <electronic-special-invoice></electronic-special-invoice>
        </el-tab-pane>
      </template>
      <template
        v-if="$hasPermission('specialInvoice,specialInvoiceZt')"
        desc="specialInvoice:增值税专用发票,specialInvoiceZt:增值税专用发票(专题)"
        actions="specialInvoice:@SpecialInvoice#specialInvoiceZt:@SpecialInvoice"
      >
        <el-tab-pane label="增值税专用发票（纸质发票）" name="special-invoice">
          <special-invoice ref="specialInvoice"></special-invoice>
        </el-tab-pane>
      </template>
      <template
        v-if="$hasPermission('invoiceDistribution,invoiceDistributionZt')"
        desc="invoiceDistribution:发票配送,invoiceDistributionZt:发票配送(专题)"
        actions="invoiceDistribution:@InvoiceDistribution#invoiceDistributionZt:@InvoiceDistribution"
      >
        <el-tab-pane label="发票配送" name="invoice-distribution">
          <invoice-distribution ref="invoiceDistribution"></invoice-distribution>
        </el-tab-pane>
      </template>
    </el-tabs>
  </el-main>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import ElectronicInvoiceAuto from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/electronic-invoice-auto.vue'
  import ElectronicInvoiceOffline from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/electronic-invoice-offline.vue'
  import SpecialInvoice from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/special-invoice.vue'
  import InvoiceDistribution from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/invoice-distribution.vue'
  import ElectronicSpecialInvoice from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/electronic-special-invoice.vue'

  @Component({
    components: {
      ElectronicInvoiceAuto,
      ElectronicInvoiceOffline,
      SpecialInvoice,
      InvoiceDistribution,
      ElectronicSpecialInvoice
    }
  })
  export default class extends Vue {
    @Ref('electronicInvoiceAuto') electronicInvoiceAuto: ElectronicInvoiceAuto
    @Ref('electronicInvoiceOffline') electronicInvoiceOffline: ElectronicInvoiceOffline
    @Ref('specialInvoice') specialInvoice: SpecialInvoice
    @Ref('invoiceDistribution') invoiceDistribution: InvoiceDistribution
    activeName = 'electronic-invoice-auto'
    async handleClick() {
      if (this.activeName === 'invoice-distribution') {
        await this.invoiceDistribution.doQueryPage()
      }
    }
    activated() {
      switch (this.activeName) {
        case 'electronic-invoice-auto':
          this.electronicInvoiceAuto?.init()
          break
        case 'electronic-invoice-offline':
          this.electronicInvoiceOffline?.page?.currentChange(1)
          break
        case 'special-invoice':
          this.specialInvoice?.page?.currentChange(1)
          break
        case 'invoice-distribution':
          this.invoiceDistribution?.page?.currentChange(1)
          break
        default:
          break
      }
    }
  }
</script>
