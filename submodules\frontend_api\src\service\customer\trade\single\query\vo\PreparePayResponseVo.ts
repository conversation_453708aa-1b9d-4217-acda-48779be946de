import { PreparePayResponse } from '@api/ms-gateway/ms-order-v1'
import TrainClassDetailClassVo from '@api/service/customer/train-class/query/vo/TrainClassDetailClassVo'
import { Accommodation } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'

export class PreparePayResponseVo extends PreparePayResponse {
  // 培训班详情
  commodityDetail = new TrainClassDetailClassVo()
  // 多个商品详情
  commodityDetails: TrainClassDetailClassVo[] = []

  /**
   * 住宿信息Map <商品id，住宿信息>
   */
  accommodationMap: Map<string, Accommodation> = new Map<string, Accommodation>()
}
