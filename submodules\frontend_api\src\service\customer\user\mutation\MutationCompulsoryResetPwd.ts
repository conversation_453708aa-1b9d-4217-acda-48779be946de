import basicdataDomain from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { ResponseStatus } from '@hbfe/common'
import CompulsoryResetPwdVo from './vo/password/CompulsoryResetPwdVo'

/**
 * 登录强制修改密码
 */
class MutationCompulsoryResetPwd {
  resetPwdParams = new CompulsoryResetPwdVo()

  async doCompulsionResetPwd(): Promise<ResponseStatus> {
    const { status } = await basicdataDomain.changePasswordByForceModifyInitPassword(this.resetPwdParams)
    return status
  }
}
export default MutationCompulsoryResetPwd
