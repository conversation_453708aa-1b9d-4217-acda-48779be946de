import { BusinessDataDictionaryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'

class LeaderPositionLevelVo {
  /**
   * 职称等级编号
   */
  id: string
  /**
   * 职称等级名称
   */
  name: string
  /**
   * 序号
   */
  sort: number

  static from(dto: BusinessDataDictionaryResponse) {
    const vo = new LeaderPositionLevelVo()
    vo.id = dto.id
    vo.name = dto.name
    vo.sort = dto.sort
    return vo
  }
}

export default LeaderPositionLevelVo
