import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-trade-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-trade-query-front-gateway-TradeQueryForestage-ldhtqdpt'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum CommoditySkuSortField {
  ON_SHELVE_TIME = 'ON_SHELVE_TIME',
  LAST_EDIT_TIME = 'LAST_EDIT_TIME',
  SALE_TOTAL_NUMBER = 'SALE_TOTAL_NUMBER'
}
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum OfflineInvoiceSortField {
  INVOICE_CREAT_TIME = 'INVOICE_CREAT_TIME'
}
export enum OrderSortField {
  ORDER_NORMAL_TIME = 'ORDER_NORMAL_TIME',
  ORDER_COMPLETED_TIME = 'ORDER_COMPLETED_TIME'
}
export enum ReturnOrderSortField {
  APPLIED_TIME = 'APPLIED_TIME'
}
export enum SortPolicy1 {
  ASC = 'ASC',
  DESC = 'DESC'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class BigDecimalScopeRequest {
  begin?: number
  end?: number
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

/**
 * 商品查询条件
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuRequest {
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品名称（精确匹配）
   */
  saleTitleList?: Array<string>
  /**
   * 商品名称（模糊查询）
   */
  saleTitleMatchLike?: string
  /**
   * 要从查询结果中剔除的商品ID集合
   */
  notShowCommoditySkuIdList?: Array<string>
  /**
   * 商品售价，精确匹配
   */
  price?: number
  /**
   * 商品原价，范围查询
   */
  originalPriceScope?: DoubleScopeRequest
  /**
   * 商品优惠价，范围查询
   */
  specialPriceScope?: DoubleScopeRequest
  /**
   * 是否启用优惠价
   */
  enableSpecialPrice?: boolean
  /**
   * 商品上下架信息
   */
  onShelveRequest?: OnShelveRequest
  /**
   * 电子劳动合同产品查询参数
   */
  electronicLaborContractProduct?: ElectronicLaborContractProductRequest
  /**
   * 商品sku属性查询
   */
  skuPropertyRequest?: SkuPropertyRequest
  /**
   * 服务商ID
   */
  severcerIdList?: Array<string>
}

/**
 * 商品排序参数
<AUTHOR>
@date 2022/01/27
 */
export class CommoditySkuSortRequest {
  /**
   * 用来排序的字段
   */
  sortField?: CommoditySkuSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 电子劳动合同产品信息
<AUTHOR>
 */
export class ElectronicLaborContractProductRequest {
  /**
   * 电子劳动合同产品ID
   */
  productIdList?: Array<string>
}

/**
 * 商品上下架相关查询参数
<AUTHOR>
@date 2022/01/25
 */
export class OnShelveRequest {
  /**
   * 商品上下架状态
<br> 0:已下架 1：已上架
   */
  onShelveStatus?: number
}

/**
 * 商品查询条件
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuRequest1 {
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品Sku名称
   */
  saleTitle?: string
  /**
   * 商品sku属性查询
   */
  skuProperty?: SkuPropertyRequest
}

/**
 * 发票关联订单查询参数
<AUTHOR>
@date 2022/3/18
 */
export class InvoiceAssociationInfoRequest {
  /**
   * 关联订单类型
0:订单号
1:批次单号
@see AssociationTypes
   */
  associationType?: number
  /**
   * 订单号 | 批次单号
   */
  associationIdList?: Array<string>
  /**
   * 单位买家单位ID
   */
  unitBuyerUnitIdList?: Array<string>
  /**
   * 单位买家经办人ID
   */
  unitBuyerAgentUserIdList?: Array<string>
  /**
   * 收款账号
   */
  receiveAccountIdList?: Array<string>
}

/**
 * 商品sku属性查询参数
<AUTHOR>
 */
export class SkuPropertyRequest {
  /**
   * 套餐类型
<p> single：单份 combo：套餐
   */
  comboType?: Array<string>
}

/**
 * 线下发票查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceRequest {
  /**
   * 发票ID集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票基本信息
   */
  basicData?: OfflineInvoiceBasicDataRequest
  /**
   * 发票关联订单查询参数
   */
  associationInfo?: InvoiceAssociationInfoRequest
  /**
   * 发票配送信息
   */
  invoiceDeliveryInfo?: OfflineInvoiceDeliveryInfoRequest
}

/**
 * 发票排序条件
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceSortRequest {
  /**
   * 用于排序的字段
   */
  field?: OfflineInvoiceSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy1
}

/**
 * 配送地址信息
<AUTHOR>
@date 2022/05/07
 */
export class DeliveryAddressRequest {
  /**
   * 收件人
   */
  consignee?: string
}

/**
 * 配送状态变更时间查询参数
<AUTHOR>
@date 2022/04/06
 */
export class DeliveryStatusChangeTimeRequest {
  /**
   * 未就绪
   */
  unReady?: DateScopeRequest
  /**
   * 已就绪
   */
  ready?: DateScopeRequest
  /**
   * 已配送
   */
  shipped?: DateScopeRequest
  /**
   * 已自取
   */
  taken?: DateScopeRequest
}

/**
 * 快递信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class ExpressRequest {
  /**
   * 快递单号
   */
  expressNo?: string
}

/**
 * 发票开票状态变更时间记录查询参数
<AUTHOR>
@date 2022/04/06
 */
export class InvoiceBillStatusChangTimeRequest {
  /**
   * 发票申请开票时间
   */
  unBillDateScope?: DateScopeRequest
  /**
   * 发票开票时间
   */
  successDateScope?: DateScopeRequest
}

/**
 * 线下发票基本信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceBasicDataRequest {
  /**
   * 发票类型
1:电子发票 2:纸质发票
@see InvoiceTypes
   */
  invoiceTypeList?: Array<number>
  /**
   * 发票种类
1:普通发票 2:增值税普通发票 3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategory?: Array<number>
  /**
   * 发票状态
1:正常
2:作废
@see InvoiceStatus
   */
  invoiceStatus?: number
  /**
   * 发票状态变更时间记录
   */
  invoiceStatusChangeTime?: OfflineInvoiceStatusChangeTimeRequest
  /**
   * 发票开票状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  billStatusList?: Array<number>
  /**
   * 发票开票状态变更时间记录
   */
  billStatusChangTime?: InvoiceBillStatusChangTimeRequest
  /**
   * 发票是否冻结
   */
  freeze?: boolean
  /**
   * 发票号集合
   */
  invoiceNoList?: Array<string>
  /**
   * 商品id集合
   */
  commoditySkuIdList?: Array<string>
}

/**
 * 线下发票配送信息
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceDeliveryInfoRequest {
  /**
   * 配送状态
0:未就绪 1：已就绪 2：已自取 3：已配送
@see OfflineDeliveryStatus
   */
  deliveryStatusList?: Array<number>
  /**
   * 配送状态变更时间记录 or 匹配
0:未就绪 1：已就绪 2：已自取 3：已配送
key值 {@link OfflineDeliveryStatus}
   */
  deliveryStatusChangeTime?: DeliveryStatusChangeTimeRequest
  /**
   * 配送方式
0:无 1：自取 2：快递
@see OfflineShippingMethods
   */
  shippingMethodList?: Array<number>
  /**
   * 快递信息
   */
  express?: ExpressRequest
  /**
   * 自取信息
   */
  takeResult?: TakeResultRequest
  /**
   * 配送地址信息
   */
  deliveryAddress?: DeliveryAddressRequest
}

/**
 * 发票状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OfflineInvoiceStatusChangeTimeRequest {
  /**
   * 正常
   */
  normal?: DateScopeRequest
  /**
   * 作废
   */
  invalid?: DateScopeRequest
}

/**
 * 取件信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class TakeResultRequest {
  /**
   * 领取人
   */
  takePerson?: string
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class OrderRequest {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 子订单退货状态
0: 未退货
1: 退货申请中
2: 退货中
3: 退货成功
4: 退款中
5: 退款成功
@see SubOrderReturnStatus
   */
  subOrderReturnStatus?: Array<number>
  /**
   * 订单基本信息查询参数
   */
  orderBasicData?: OrderBasicDataRequest
  /**
   * 订单支付信息查询
   */
  payInfo?: OrderPayInfoRequest
  /**
   * 单位买家单位ID
   */
  unitBuyerUnitIdList?: Array<string>
  /**
   * 发货商品信息
   */
  deliveryCommodity?: CommoditySkuRequest1
  /**
   * 订单服务商信息
   */
  orderServicers?: OrderServicersRequest
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: OrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 订单基本信息查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderBasicDataRequest {
  /**
   * 订单类型
1:常规订单 2:批次关联订单
@see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType?: number
  /**
   * 批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 订单状态
<br> 1:正常 2：交易完成 3：交易关闭
@see OrderStatus
   */
  orderStatusList?: Array<number>
  /**
   * 订单支付状态
<br> 0:未支付 1：支付中 2：已支付
@see com.fjhb.domain.trade.api.order.consts.OrderPaymentStatus
   */
  orderPaymentStatusList?: Array<number>
  /**
   * 订单发货状态
<br> 0:未发货 1：发货中 2：已发货
@see com.fjhb.domain.trade.api.order.consts.OrderDeliveryStatus
   */
  orderDeliveryStatusList?: Array<number>
  /**
   * 订单状态变更时间
   */
  orderStatusChangeTime?: OrderStatusChangeTimeRequest
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelTypesList?: Array<number>
  /**
   * 终端
<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCodeList?: Array<string>
  /**
   * 订单价格范围
<br> 查询非0元订单 begin填0.01
   */
  orderAmountScope?: BigDecimalScopeRequest
}

/**
 * 订单支付信息相关查询参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderPayInfoRequest {
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 付款类型
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
   */
  paymentOrderTypeList?: Array<number>
}

/**
 * 订单服务商查询参数
<AUTHOR>
 */
export class OrderServicersRequest {
  /**
   * 合同签订服务商ID
   */
  contractProviderIdList?: Array<string>
}

/**
 * 订单状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderStatusChangeTimeRequest {
  /**
   * 订单处于正常状态时间范围(创建时间范围)
   */
  normalDateScope?: DateScopeRequest
  /**
   * 订单创建时间范围
   */
  completedDatesScope?: DateScopeRequest
}

/**
 * 退货单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderRequest {
  /**
   * 退货单号
   */
  returnOrderNoList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: ReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: ReturnOrderApprovalInfoRequest
  /**
   * 退货商品名称（精确匹配）
   */
  returnCommoditySaleTitle?: string
  /**
   * sku属性
   */
  skuProperty?: SkuPropertyRequest
  /**
   * 退货商品id集合
   */
  returnCommoditySkuIdList?: Array<string>
  /**
   * 退款商品id集合
   */
  refundCommoditySkuIdList?: Array<string>
  /**
   * 退货单关联子订单查询参数
   */
  subOrderInfo?: SubOrderInfoRequest
  /**
   * 服务商
   */
  servicers?: ReturnOrderServicersRequest
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class ReturnSortRequest {
  /**
   * 需要排序的字段
   */
  field?: ReturnOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 退货单关联子订单的主订单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class OrderInfoRequest {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 关联批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 单位买家单位ID集合
   */
  unitBuyerUnitIdList?: Array<string>
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 原始订单交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelTypesList?: Array<number>
  /**
   * 终端
<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCodeList?: Array<string>
}

/**
 * 退货单关闭信息
<AUTHOR>
@date 2022年4月11日 11:33:35
 */
export class ReturnCloseReasonRequest {
  /**
   * 退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
@see ReturnOrderCloseTypes
   */
  closeTypeList?: Array<number>
}

/**
 * 退货单审批信息查询参数
<AUTHOR>
@date 2022/03/18
 */
export class ReturnOrderApprovalInfoRequest {
  /**
   * 审批时间
   */
  approveTime?: DateScopeRequest
}

/**
 * 退货单基本信息查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderBasicDataRequest {
  /**
   * 退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
   */
  returnOrderStatus?: Array<number>
  /**
   * 退货单申请来源类型
SUB_ORDER
BATCH_RETURN_ORDER
@see ReturnOrderApplySourceTypes
   */
  applySourceType?: string
  /**
   * 来源ID集合
   */
  applySourceIdList?: Array<string>
  /**
   * 退货单关闭信息
   */
  returnCloseReason?: ReturnCloseReasonRequest
  /**
   * 退货单状态变更时间
   */
  returnStatusChangeTime?: ReturnOrderStatusChangeTimeRequest
  /**
   * 退款金额范围
<br> 查询非0元  begin填0.01
   */
  refundAmountScope?: BigDecimalScopeRequest
}

/**
 * 订单服务商查询参数
<AUTHOR>
 */
export class ReturnOrderServicersRequest {
  /**
   * 合同签订服务商ID
   */
  contractProviderIdList?: Array<string>
}

/**
 * 退货单状态变更时间查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderStatusChangeTimeRequest {
  /**
   * 申请退货时间
   */
  applied?: DateScopeRequest
  /**
   * 退货单完成时间
<br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
   */
  returnCompleted?: DateScopeRequest
}

/**
 * <AUTHOR>
@date 2022/03/24
 */
export class SubOrderInfoRequest {
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 订单查询参数
   */
  orderInfo?: OrderInfoRequest
}

export class UserModel {
  userId: string
}

/**
 * 前台商品返回值模型
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuForestageResponse {
  /**
   * 商品id
   */
  commoditySkuId: string
  /**
   * 商品基本信息
   */
  commodityBasicData: CommodityBasicDataResponse
  /**
   * 商品描述信息
   */
  description: CommodityDescriptionResponse
  /**
   * 商品属性信息
   */
  skuProperty: CommoditySkuPropertyResponse
  /**
   * 用户商品拥有信息
   */
  possessionInfo: UserPossessionInfoResponse
  /**
   * 当前渠道配置信息
   */
  commodityPurchaseChannelConfig: PurchaseChannelConfigResponse
  /**
   * 上下架信息
   */
  onShelve: OnShelveResponse
  /**
   * 商品关联资源信息
   */
  resource: ResourceResponse
  /**
   * 资源数量
   */
  resourceQuality: number
  /**
   * 库存
   */
  stocks: number
}

/**
 * 商品基本信息返回值
<AUTHOR>
@date 2022/1/25
 */
export class CommodityBasicDataResponse {
  /**
   * 商品销售标题
   */
  saleTitle: string
  /**
   * 商品原价
   */
  originalPrice: number
  /**
   * 商品优惠价
   */
  specialPrice: number
  /**
   * 是否展示原价
   */
  showOriginalPrice: boolean
  /**
   * 是否启用优惠价
   */
  enableSpecialPrice: boolean
  /**
   * 商品封面图路径
   */
  commodityPicturePath: string
}

/**
 * 商品描述返回值
<AUTHOR>
 */
export class CommodityDescriptionResponse {
  /**
   * Web端商品描述介绍
   */
  commodityDescriptionWeb: string
  /**
   * 小程序端商品描述介绍
   */
  commodityDescriptionUniApp: string
  /**
   * 商品推荐语列表
   */
  keywords: Array<string>
}

/**
 * 商品sku属性返回值
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuPropertyResponse {
  /**
   * 套餐类型
<p> single：单份 combo：套餐
   */
  comboType: SkuPropertyResponse
}

/**
 * 商品上下架信息
<AUTHOR>
@date 2022/1/25
 */
export class OnShelveResponse {
  /**
   * 商品当前上架状态
<br> 0:已下架 1：已上架
@see CommoditySkuShelveStatus
   */
  shelveStatus: number
  /**
   * 最新上架时间
   */
  lastOnShelveTime: string
  /**
   * 最新下架时间
   */
  offShelveTime: string
  /**
   * 上架方式
<p>  1:立即上架 2:指定时间上架
   */
  onShelveType: number
  /**
   * 下架方式
<p>  1:立即下架 2:指定时间下架
   */
  offShelveType: number
  /**
   * 最新计划上架时间
   */
  onShelvePlanTime: string
  /**
   * 最新计划下架时间
   */
  offShelvePlanTime: string
  /**
   * 发布时间
   */
  publishTime: string
}

/**
 * 购买渠道配置
<AUTHOR>
@date 2022/01/26
 */
export class PurchaseChannelConfigResponse {
  /**
   * 是否开启可见
   */
  couldSee: boolean
  /**
   * 是否开启可购买
   */
  couldBuy: boolean
}

/**
 * 商品sku属性
<AUTHOR>
@date 2022/01/25
 */
export class SkuPropertyResponse {
  /**
   * sku属性值id
   */
  skuPropertyValueId: string
}

/**
 * 用户商品拥有信息
<AUTHOR>
@date 2022/01/25
 */
export class UserPossessionInfoResponse {
  /**
   * 用户是否拥有此商品
   */
  possessing: boolean
  /**
   * 商品来源类型
<br> 0：子订单 1：换货单
@see com.fjhb.ms.trade.query.order.constants.CommoditySkuSourceType
   */
  sourceType: number
  /**
   * 商品来源ID
<p>
<br> 如果来源类型是子订单，那么来源ID是子订单号
<br> 如果来源类型是换货单，那么来源ID是换货单
   */
  sourceId: string
}

/**
 * 商品快照sku属性信息
<AUTHOR>
@date 2022/01/26
 */
export class CommoditySkuPropertyResponse1 {
  /**
   * 套餐类型
<p> single：单份 combo：套餐
   */
  comboType: SkuPropertyResponse1
}

/**
 * 商品快照
<AUTHOR>
@date 2022/1/26
 */
export class CommoditySkuResponse {
  /**
   * 商品id
   */
  commoditySkuId: string
  /**
   * 商品Sku名称
   */
  saleTitle: string
  /**
   * 商品封面图路径
   */
  commodityPicturePath: string
  /**
   * 商品原价
   */
  originalPrice: number
  /**
   * 商品优惠价
   */
  specialPrice: number
  /**
   * 是否展示原价
   */
  showPrice: boolean
  /**
   * 是否启用优惠价
   */
  enableSpecialPrice: boolean
  /**
   * 商品sku 配置
   */
  skuProperty: CommoditySkuPropertyResponse1
  /**
   * 商品关联资源
   */
  resource: ResourceResponse
  /**
   * 商品资源数量
   */
  resourceQuality: number
}

/**
 * 电子劳动合同产品
<AUTHOR>
 */
export class ElectronicLaborContractProductResponse implements ResourceResponse {
  /**
   * 产品ID
   */
  productId: string
  /**
   * 产品名
   */
  productName: string
  /**
   * 有效期类型
   */
  validTermType: number
  /**
   * 具体有效期限
   */
  validTerm: number
  /**
   * 合同签订服务商ID
   */
  serverId: string
  /**
   * 资源类型
<br> ElectronicLaborContractProduct：电子劳动合同产品
@see com.fjhb.domain.trade.api.commodity.consts.CommoditySaleResourceTypes
   */
  resourceType: string
}

/**
 * 发票申请信息
<AUTHOR>
@date 2022/03/17
 */
export class InvoiceApplyInfoResponse {
  /**
   * 开票方式
1: 线上开票
2: 线下开票
@see InvoiceMethods
   */
  invoiceMethod: number
  /**
   * 发票类型
1：电子发票
2：纸质发票
@see InvoiceTypes
   */
  invoiceType: number
  /**
   * 发票种类
1：普通发票
2：增值税普通发票
3：增值税专用发票
@see InvoiceCategories
   */
  invoiceCategory: number
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型
1：个人
2：企业
@see InvoiceTitleTypes
   */
  titleType: number
  /**
   * 纳税人识别号（统一社会信用代码）
   */
  taxpayerNo: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 账户
   */
  account: string
  /**
   * 联系邮箱
   */
  contactEmail: string
  /**
   * 联系电话
   */
  contactPhone: string
  /**
   * 发票票面备注
   */
  remark: string
  /**
   * 营业执照
   */
  businessLicensePath: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath: string
  /**
   * 配送方式
0：无
1：自取
2：快递
@see OfflineShippingMethods
   */
  shippingMethod: number
  /**
   * 配送地址信息
   */
  deliveryAddress: DeliveryAddressResponse
  /**
   * 自取点信息
   */
  takePoint: TakePointResponse
  /**
   * 发票申请时间
   */
  appliedTime: string
  /**
   * 关联发票id集合
   */
  invoiceIdList: Array<string>
}

/**
 * 发票票面信息
<AUTHOR>
@date 2022/3/18
 */
export class InvoiceFaceInfoResponse {
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型
@see InvoiceTitleTypes
   */
  titleType: number
  /**
   * 购买方纳税人识别号
   */
  taxpayerNo: string
  /**
   * 购买方地址
   */
  address: string
  /**
   * 购买方电话号码
   */
  phone: string
  /**
   * 购买方开户行名称
   */
  bankName: string
  /**
   * 购买方银行账户
   */
  account: string
  /**
   * 购买方电子邮箱
   */
  email: string
  /**
   * 购买方营业执照
   */
  businessLicensePath: string
  /**
   * 购买方开户许可
   */
  accountOpeningLicensePath: string
  /**
   * 联系电子邮箱
   */
  contactEmail: string
  /**
   * 联系电话
   */
  contactPhone: string
  /**
   * 发票票面备注
   */
  remark: string
}

/**
 * 退货单/批次退货单的原订单支付信息
<AUTHOR>
@date 2022/03/18
 */
export class PaymentInfoResponse {
  /**
   * 支付金额
   */
  payAmount: number
  /**
   * 支付流水号
   */
  flowNo: string
  /**
   * 收款账号
   */
  receiveAccountId: string
}

/**
 * 收款账号返回值
<AUTHOR>
@date 2022/01/26
 */
export class ReceiveAccountResponse {
  /**
   * 收款账号id
   */
  receiveAccountId: string
  /**
   * 收款账号类型（1:线上收款账号 2:线下收款账号）
@see ReceiveAccountTypes
   */
  receiveAccountType: number
  /**
   * 支付渠道id
   */
  payChannelId: string
  /**
   * 支付渠道名称
   */
  payChannelName: string
}

/**
 * 退货单退款信息
<AUTHOR>
@date 2022/03/18
 */
export class RefundInfoResponse {
  /**
   * 退款单号
   */
  refundOrderNo: string
  /**
   * 退款单类型（1：线上退款 2：线下退款）
@see RefundOrderTypes
   */
  refundOrderType: number
  /**
   * 退款单状态（0：等待退款 1：退款中 2：已退款 3：退款失败）
@see com.fjhb.domain.trade.api.payment.consts.RefundOrderStatus
   */
  refundOrderStatus: number
  /**
   * 退款单状态变更时间
   */
  refundOrderStatusChangeTime: RefundOrderStatusChangeTimeResponse
  /**
   * 退款流水
   */
  refundFlow: string
  /**
   * 退款金额
   */
  refundAmount: number
  /**
   * 退款失败原因
   */
  refundFailReason: string
}

/**
 * 退款单状态变更时间
<AUTHOR>
@date 2022/03/23
 */
export class RefundOrderStatusChangeTimeResponse {
  /**
   * 等待退款
   */
  waiting: string
  /**
   * 退款中
   */
  refunding: string
  /**
   * 已退款
   */
  refunded: string
  /**
   * 退款失败
   */
  failed: string
}

/**
 * 商品资源
<AUTHOR>
@date 2022/03/02
 */
export interface ResourceResponse {
  /**
   * 资源类型
<br> ElectronicLaborContractProduct：电子劳动合同产品
@see com.fjhb.domain.trade.api.commodity.consts.CommoditySaleResourceTypes
   */
  resourceType: string
}

/**
 * 商品快照sku属性
<AUTHOR>
@date 2022/1/25
 */
export class SkuPropertyResponse1 {
  /**
   * sku属性值id
   */
  skuPropertyValueId: string
  /**
   * sku属性值名
   */
  skuPropertyValueName: string
  /**
   * sku属性值展示名
   */
  skuPropertyValueShowName: string
}

/**
 * 单位买家信息返回值
<AUTHOR>
 */
export class UnitBuyerResponse {
  /**
   * 经办人用户ID
   */
  agentUserId: string
  /**
   * 单位买家单位ID
   */
  unitBuyerUnitId: string
}

/**
 * 用户相关返回值
<AUTHOR>
@date 2022/01/26
 */
export class UserResponse {
  /**
   * 用户ID
   */
  userId: string
}

/**
 * 线下发票网关模型
<AUTHOR>
@date 2022/3/18
 */
export class OfflineInvoiceResponse {
  /**
   * 发票id
   */
  offlineInvoiceId: string
  /**
   * 发票基本信息
   */
  basicData: OfflineInvoiceBasicDataResponse
  /**
   * 发票关联订单信息
   */
  associationInfo: OfflineInvoiceAssociationInfoResponse
  /**
   * 发票配送信息
   */
  deliveryInfo: OfflineInvoiceDeliveryInfoResponse
  /**
   * 发票配送记录
   */
  deliveryRecordList: Array<OfflineDeliveryRecord>
}

/**
 * 发票开票状态变更时间记录
<AUTHOR>
@date 2022/04/06
 */
export class BillStatusChangeTimeResponse {
  /**
   * 未开票
   */
  unBill: string
  /**
   * 已开票
   */
  success: string
}

/**
 * 配送地址信息
<AUTHOR>
@date 2022/04/06
 */
export class DeliveryAddressResponse {
  /**
   * 收件人
   */
  consignee: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 所在物理地区
   */
  region: string
  /**
   * 详细地址
   */
  address: string
}

/**
 * 发票配送状态变更时间记录
<AUTHOR>
@date 2022/04/06
 */
export class DeliveryStatusChangeTimeResponse {
  /**
   * 未就绪
   */
  unReady: string
  /**
   * 已就绪
   */
  ready: string
  /**
   * 已配送
   */
  shipped: string
  /**
   * 已自取
   */
  taken: string
}

/**
 * 快递信息
<AUTHOR>
@date 2022/04/06
 */
export class ExpressResponse {
  /**
   * 快递公司名称
   */
  expressCompanyName: string
  /**
   * 快递单号
   */
  expressNo: string
}

/**
 * 发票状态变更时间记录
<AUTHOR>
@date 2022/04/06
 */
export class InvoiceStatusChangeTimeResponse {
  /**
   * 正常
   */
  normal: string
  /**
   * 作废
   */
  invalid: string
}

/**
 * 发票配送记录
<AUTHOR>
@date 2022/05/17
 */
export class OfflineDeliveryRecord {
  /**
   * 发票号
   */
  invoiceNoList: Array<string>
  /**
   * 配送方式
<p>
0:无 1:自取 2：快递
@see OfflineShippingMethods
   */
  shippingMethod: number
  /**
   * 自取结果
   */
  takeResult: TakeResultResponse
  /**
   * 快递信息
   */
  express: ExpressResponse
  /**
   * 配送时间
   */
  deliveryTime: string
}

/**
 * 发票关联订单信息
<AUTHOR>
@date 2022/3/18
 */
export class OfflineInvoiceAssociationInfoResponse {
  /**
   * 关联订单类型 | 批次单、普通订单
@see AssociationTypes
   */
  associationType: number
  /**
   * 订单号 | 批次单号
   */
  associationId: string
  /**
   * 付款金额
   */
  payAmount: number
  /**
   * 单位买家信息
   */
  unitBuyer: UnitBuyerResponse
  /**
   * 收款账号ID
   */
  receiveAccountId: string
  /**
   * 订单退货状态
0:未退货 1：退货中 2：退货成功
   */
  orderReturnStatus: number
}

/**
 * 发票基本信息
<AUTHOR>
@date 2022/3/18
 */
export class OfflineInvoiceBasicDataResponse {
  /**
   * 发票类型
@see InvoiceTypes
   */
  invoiceType: number
  /**
   * 发票种类
@see InvoiceCategories
   */
  invoiceCategory: number
  /**
   * 发票状态
@see InvoiceStatus
   */
  invoiceStatus: number
  /**
   * 发票状态变更时间记录
   */
  invoiceStatusChangeTime: InvoiceStatusChangeTimeResponse
  /**
   * 发票开票状态
@see InvoiceBillStatus
   */
  billStatus: number
  /**
   * 发票开票状态变更时间记录
   */
  billStatusChangeTime: BillStatusChangeTimeResponse
  /**
   * 发票是否冻结
   */
  freeze: boolean
  /**
   * 冻结来源类型
@see InvoiceFrozenSourceTypes
   */
  freezeSourceType: number
  /**
   * 冻结来源编号
   */
  freezeSourceId: string
  /**
   * 发票票面信息
   */
  invoiceFaceInfo: InvoiceFaceInfoResponse
  /**
   * 发票号集合
   */
  invoiceNoList: Array<string>
  /**
   * 总金额（开票金额）
   */
  amount: number
}

/**
 * 线下发票配送信息
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceDeliveryInfoResponse {
  /**
   * 配送状态
<p>
0:未就绪 1：已就绪 2：已自取 3：已配送
@see OfflineDeliveryStatus
   */
  deliveryStatus: number
  /**
   * 配送状态变更时间记录
   */
  deliveryStatusChangeTime: DeliveryStatusChangeTimeResponse
  /**
   * 配送方式
<p>
0:无 1:自取 2：快递
@see OfflineShippingMethods
   */
  shippingMethod: number
  /**
   * 配送地址信息
   */
  deliveryAddress: DeliveryAddressResponse
  /**
   * 自取点信息
   */
  takePoint: TakePointResponse
  /**
   * 快递信息
   */
  express: ExpressResponse
  /**
   * 自取信息
   */
  takeResult: TakeResultResponse
}

/**
 * 自取点信息
<AUTHOR>
@date 2022/04/06
 */
export class TakePointResponse {
  /**
   * 领取地点
   */
  pickupLocation: string
  /**
   * 领取时间
   */
  pickupTime: string
  /**
   * 备注
   */
  remark: string
}

/**
 * 取件信息
<AUTHOR>
@date 2022/04/06
 */
export class TakeResultResponse {
  /**
   * 领取人
   */
  takePerson: string
  /**
   * 手机号
   */
  phone: string
}

/**
 * 订单返回值
<AUTHOR>
@date 2022/1/25
 */
export class OrderResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 基本信息
   */
  basicData: OrderBasicDataResponse
  /**
   * 支付信息
   */
  payInfo: PayInfoResponse
  /**
   * 子订单信息
   */
  subOrderItems: Array<SubOrderResponse>
  /**
   * 单位买家信息
   */
  unitBuyer: UnitBuyerResponse
  /**
   * 订单创建人
   */
  creator: UserResponse
  /**
   * 订单是否已经申请发票
   */
  isInvoiceApplied: boolean
  /**
   * 发票申请信息
   */
  invoiceApplyInfo: InvoiceApplyInfoResponse
}

/**
 * 订单统计信息返回值
<AUTHOR>
@date 2022/01/26
 */
export class OrderStatisticResponse {
  /**
   * 订单总数量
   */
  totalOrderCount: number
  /**
   * 订单总金额
   */
  totalOrderAmount: number
  /**
   * 订单培训方案商品总学时数
   */
  totalPeriod: number
}

/**
 * 订单基本信息返回值
<AUTHOR>
@date 2022/01/26
 */
export class OrderBasicDataResponse {
  /**
   * 订单类型
<br> 1：常规订单 2：批次关联订单
@see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType: number
  /**
   * 关联批次单号
   */
  batchOrderNo: string
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3：管理员导入 4：集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelType: number
  /**
   * 终端
<br> Web:Web端 H5:H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTerminalCodes
   */
  terminalCode: string
  /**
   * 订单总金额
   */
  amount: number
  /**
   * 订单状态
0:未确认，批次单订单初始状态
1:正常
2:交易完成
3:交易关闭
<p>
ui订单状态：数据微服务订单状态
等待付款：订单状态正常&amp;支付状态未支付
支付中：订单状态正常&amp;支付状态支付中
开通中：订单状态正常&amp;支付状态已支付
交易成功：订单状态交易成功
交易关闭：订单状态交易关闭
@see OrderStatus
   */
  orderStatus: number
  /**
   * 订单支付状态
0:未支付
1:支付中
2:已支付
@see OrderPaymentStatus
   */
  orderPaymentStatus: number
  /**
   * 订单发货状态
0：未发货
1：发货中
2：已发货
@see OrderDeliveryStatus
   */
  orderDeliveryStatus: number
  /**
   * 订单状态变更时间
   */
  orderStatusChangeTime: OrderStatusChangeTimeResponse
  /**
   * 订单支付状态变更时间
   */
  orderPaymentStatusChangeTime: OrderPaymentStatusChangeTimeResponse
  /**
   * 自动关闭时间
   */
  autoCloseTime: string
  /**
   * 交易关闭原因
   */
  closeReason: OrderCloseReasonResponse
}

/**
 * 订单交易关闭返回值
<AUTHOR>
@date 2022/01/26
 */
export class OrderCloseReasonResponse {
  /**
   * 交易关闭类型
<br> 1:买家取消 2:卖家取消 3：超时取消 4：批次关联取消
@see com.fjhb.domain.trade.api.order.consts.OrderClosedTypes
   */
  closedType: number
  /**
   * 交易关闭原因ID
   */
  reasonId: string
  /**
   * 交易关闭原因说明
   */
  reason: string
}

/**
 * 订单支付状态变更时间
<AUTHOR>
@date 2022/01/26
 */
export class OrderPaymentStatusChangeTimeResponse {
  /**
   * 支付中
   */
  paying: string
  /**
   * 已支付
   */
  paid: string
}

/**
 * 订单状态变更时间
<AUTHOR>
@date 2022/01/26
 */
export class OrderStatusChangeTimeResponse {
  /**
   * 订单处于正常状态的时间（订单创建时间）
   */
  normal: string
  /**
   * 订单交易完成时间
   */
  completed: string
  /**
   * 订单交易关闭时间
   */
  closed: string
}

/**
 * 订单支付信息
<AUTHOR>
@date 2022/1/26
 */
export class PayInfoResponse {
  /**
   * 付款单号
   */
  paymentOrderNo: string
  /**
   * 付款单类型
<p>
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderTypes
   */
  paymentOrderType: number
  /**
   * 付款单状态
0:待付款
1:付款中
2:已支付
3:已取消
@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderStatus
   */
  paymentOrderStatus: number
  /**
   * 支付流水号
   */
  flowNo: string
  /**
   * 支付金额
   */
  payAmount: number
  /**
   * 支付金额类型
<br> 0:人民币现金支付（线下人民币现金、微信支付、支付宝） 1:华博电子钱包虚拟币 2:消费券(培训券)
@see CurrencyType
   */
  currencyType: number
  /**
   * 收款账号
   */
  receiveAccount: ReceiveAccountResponse
  /**
   * 汇款凭证url
   */
  paymentVoucherList: Array<PaymentVoucherResponse>
  /**
   * 汇款凭证确认人
   */
  paymentVoucherConfirmUser: UserModel
}

/**
 * 付款凭证
<AUTHOR>
@date 2022/03/07
 */
export class PaymentVoucherResponse {
  /**
   * 凭证ID
   */
  id: string
  /**
   * 凭证文件路径
   */
  path: string
  /**
   * 创建人
   */
  createUserId: UserModel
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 子订单发货状态变更时间
<AUTHOR>
@date 2022/01/26
 */
export class SubOrderDeliveryStatusChangeTimeResponse {
  /**
   * 等待发货
   */
  waiting: string
  /**
   * 发货中
   */
  delivering: string
  /**
   * 发货成功
   */
  successDelivered: string
  /**
   * 发货失败
   */
  failDelivered: string
}

/**
 * 子订单返回值
<AUTHOR>
@date 2022/1/26
 */
export class SubOrderResponse {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 子订单发货状态
0:等待发货 100:发货中 200:发货成功 400:发货失败
@see DeliveryOrderSkuStatus
   */
  deliveryStatus: number
  /**
   * 子订单换货状态
<p>
0:未换货
1:换货申请中
2:换货中
3:已换货
@see com.fjhb.domain.trade.api.order.consts.SubOrderExchangeStatus
   */
  exchangeStatus: number
  /**
   * 子订单退货状态
0：未退货
1：退货申请中
2：退货中
3：退货成功
4：退款中
5：退款成功
@see SubOrderReturnStatus
   */
  returnStatus: number
  /**
   * 该子订单的最后一笔退货单号
   */
  lastReturnOrderNo: string
  /**
   * 子订单发货状态变更时间
0: 等待发货
100: 发货中
200: 发货成功
400: 发货失败
<br> key值 {@link DeliveryOrderSkuStatus}
   */
  deliveryStatusChangeTime: SubOrderDeliveryStatusChangeTimeResponse
  /**
   * 发货失败信息
   */
  deliverFailMessage: string
  /**
   * 子订单商品数量
   */
  quantity: number
  /**
   * 商品单价
   */
  price: number
  /**
   * 子订单总金额
   */
  amount: number
  /**
   * 发货商品信息
   */
  deliveryCommoditySku: CommoditySkuResponse
  /**
   * 当前商品信息
   */
  currentCommoditySku: CommoditySkuResponse
  /**
   * 当前商品来源类型
@see CommoditySkuSourceType
子订单发货: 0
换货单换货：1
   */
  currentCommoditySourceType: number
  /**
   * 当前商品来源ID
<p>
如果来源类型是子订单，那么来源ID是子订单号
如果来源类型是换货单，那么来源ID是换货单
   */
  currentCommoditySourceId: string
}

/**
 * 退货单网关模型
<AUTHOR>
@date 2022/03/23
 */
export class ReturnOrderResponse {
  /**
   * 退货单号
   */
  returnOrderNo: string
  /**
   * 退货单基本信息
   */
  basicData: ReturnOrderBasicDataResponse
  /**
   * 退货单是否需要审批
   */
  needApprove: boolean
  /**
   * 退货单审批信息
   */
  approvalInfo: ReturnApprovalInfoResponse
  /**
   * 退款确认人
   */
  confirmUser: UserResponse
  /**
   * 退货单关联退款单信息
   */
  refundInfo: RefundInfoResponse
  /**
   * 退货商品信息
   */
  returnCommodity: ReturnCommodityResponse
  /**
   * 退款商品信息
   */
  refundCommodity: RefundCommodityResponse
  /**
   * 退货子订单信息
   */
  subOrderInfo: SubOrderInfoResponse
}

/**
 * 退货单统计信息返回值
<AUTHOR>
 */
export class ReturnOrderStatisticResponse {
  /**
   * 退货单总数
   */
  totalReturnOrderCount: number
  /**
   * 退货单退款总额
   */
  totalRefundAmount: number
}

/**
 * 退货单关联订单信息
<AUTHOR>
@date 2022/3/23
 */
export class OrderInfoResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 订单类型（1：常规订单 2：批次关联订单）
@see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType: number
  /**
   * 关联批次单号
   */
  batchOrderNo: string
  /**
   * 购买渠道（1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道）
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelType: number
  /**
   * 终端（Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统）
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTerminalCodes
   */
  terminalCode: string
  /**
   * 订单支付信息
   */
  orderPaymentInfo: PaymentInfoResponse
  /**
   * 单位买家信息
   */
  unitBuyer: UnitBuyerResponse
  /**
   * 创建人
   */
  creator: UserResponse
}

/**
 * 退款商品信息
<AUTHOR>
@date 2022/03/28
 */
export class RefundCommodityResponse {
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 商品信息
   */
  commoditySku: CommoditySkuResponse
}

/**
 * 退货审批信息
<AUTHOR>
@date 2022/03/18
 */
export class ReturnApprovalInfoResponse {
  /**
   * 审批状态（0：未审批 1：已审批）
@see ReturnApprovalReportStatus
   */
  approveStatus: number
  /**
   * 审批结果（-1：无 0：拒绝 1：同意）
@see ReturnApprovalReportResults
   */
  approveResult: number
  /**
   * 审批人
   */
  approveUser: UserResponse
  /**
   * 审批意见
   */
  approveComment: string
  /**
   * 审批时间
   */
  approveTime: string
}

/**
 * 退货单关闭信息
<AUTHOR>
@date 2022年3月29日 15:48:33
 */
export class ReturnCloseReasonResponse {
  /**
   * 退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
@see ReturnOrderCloseTypes
   */
  closeType: number
  /**
   * 退货单取消人
   */
  cancelUser: UserModel
  /**
   * 取消原因
   */
  cancelReason: string
}

/**
 * 退货商品信息
<AUTHOR>
@date 2022/03/28
 */
export class ReturnCommodityResponse {
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 商品信息
   */
  commoditySku: CommoditySkuResponse
  /**
   * 商品退货资源数量
   */
  resourceQuantity: number
}

/**
 * 退货申请信息返回值
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderApplyInfoResponse {
  /**
   * 申请人
   */
  applyUser: UserResponse
  /**
   * 来源人类型
<p>1：买家，2：卖家
@see com.fjhb.domain.trade.api.returnorder.consts.SourcePersonType
   */
  sourcePersonType: number
  /**
   * 申请原因内容id
   */
  reasonId: string
  /**
   * 申请原因内容
   */
  reasonContent: string
  /**
   * 申请描述
   */
  description: string
}

/**
 * 退货单基本信息
<AUTHOR>
@date 2022/3/18
 */
export class ReturnOrderBasicDataResponse {
  /**
   * 退货单类型（1：仅退货 2：仅退款 3：退货并退款）
@see ReturnOrderTypes
   */
  returnOrderType: number
  /**
   * 退款总额
   */
  refundAmount: number
  /**
   * 退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
@see ReturnOrderStatus
   */
  returnOrderStatus: number
  /**
   * 退货单状态变更时间
   */
  returnOrderStatusChangeTime: ReturnOrderStatusChangeTimeResponse
  /**
   * 退货单申请信息
   */
  applyInfo: ReturnOrderApplyInfoResponse
  /**
   * 退货单退货失败信息
   */
  returnFailReason: string
  /**
   * 退货单关闭信息
   */
  returnCloseReason: ReturnCloseReasonResponse
  /**
   * 退货单申请来源类型
SUB_ORDER
BATCH_RETURN_ORDER
@see ReturnOrderApplySourceTypes
   */
  applySourceType: string
  /**
   * 退货单申请来源id
当来源类型为子订单时,该申请来源id为子订单号,为批次退货单申请来源的,该申请来源id为批次退货单号
   */
  applySourceId: string
}

/**
 * 退货单状态变更时间
<AUTHOR>
@date 2022/03/23
 */
export class ReturnOrderStatusChangeTimeResponse {
  /**
   * 申请退货时间
   */
  applied: string
  /**
   * 申请退货取消处理中时间
   */
  cancelApplying: string
  /**
   * 退货处理中时间
   */
  returning: string
  /**
   * 退货失败时间
   */
  returnFailed: string
  /**
   * 正在申请退款时间
   */
  refundApplying: string
  /**
   * 已申请退款时间
   */
  refundApplied: string
  /**
   * 退款处理中时间
   */
  refunding: string
  /**
   * 退款失败
   */
  refundFailed: string
  /**
   * 退货完成时间
   */
  returned: string
  /**
   * 退款完成时间
   */
  refunded: string
  /**
   * 退货退款完成时间
   */
  returnedAndRefunded: string
  /**
   * 退货单完成时间
   */
  returnCompleted: string
  /**
   * 已关闭时间
   */
  closed: string
}

/**
 * 退货子订单信息
<AUTHOR>
@date 2022/3/18
 */
export class SubOrderInfoResponse {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 子订单商品购买数量
   */
  commodityQuantity: number
  /**
   * 子订单是否有换货
   */
  exchanged: boolean
  /**
   * 主订单信息
   */
  orderInfo: OrderInfoResponse
}

export class CommoditySkuForestageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CommoditySkuForestageResponse>
}

export class OfflineInvoiceResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<OfflineInvoiceResponse>
}

export class OrderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<OrderResponse>
}

export class ReturnOrderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ReturnOrderResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户自主购买渠道商品详情
   * @param query 查询 graphql 语法文档
   * @param commoditySkuId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCommoditySkuCustomerPurchaseInSubProject(
    commoditySkuId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCommoditySkuCustomerPurchaseInSubProject,
    operation?: string
  ): Promise<Response<CommoditySkuForestageResponse>> {
    return commonRequestApi<CommoditySkuForestageResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { commoditySkuId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取发票详情
   * @param query 查询 graphql 语法文档
   * @param invoiceId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOfflineInvoiceInUnit(
    invoiceId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOfflineInvoiceInUnit,
    operation?: string
  ): Promise<Response<OfflineInvoiceResponse>> {
    return commonRequestApi<OfflineInvoiceResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { invoiceId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取订单详情 -当前单位
   * @param query 查询 graphql 语法文档
   * @param orderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOrderInUnit(
    orderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOrderInUnit,
    operation?: string
  ): Promise<Response<OrderResponse>> {
    return commonRequestApi<OrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { orderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取退货单详情
   * @param query 查询 graphql 语法文档
   * @param returnOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getReturnOrderInUnit(
    returnOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getReturnOrderInUnit,
    operation?: string
  ): Promise<Response<ReturnOrderResponse>> {
    return commonRequestApi<ReturnOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { returnOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取用户自主购买渠道商品
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCommoditySkuCustomerPurchaseInSubProject(
    params: { page?: Page; queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommoditySkuCustomerPurchaseInSubProject,
    operation?: string
  ): Promise<Response<CommoditySkuForestageResponsePage>> {
    return commonRequestApi<CommoditySkuForestageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询发票
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOfflineInvoiceInUnit(
    params: { page?: Page; request?: OfflineInvoiceRequest; sort?: Array<OfflineInvoiceSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOfflineInvoiceInUnit,
    operation?: string
  ): Promise<Response<OfflineInvoiceResponsePage>> {
    return commonRequestApi<OfflineInvoiceResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页订单 - 当前单位
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOrderInUnit(
    params: { page?: Page; request?: OrderRequest; sortRequest?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOrderInUnit,
    operation?: string
  ): Promise<Response<OrderResponsePage>> {
    return commonRequestApi<OrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 退货单分页查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageReturnOrderInUnit(
    params: { page?: Page; request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageReturnOrderInUnit,
    operation?: string
  ): Promise<Response<ReturnOrderResponsePage>> {
    return commonRequestApi<ReturnOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取订单总金额、总数量
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticOrderInUnit(
    request: OrderRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticOrderInUnit,
    operation?: string
  ): Promise<Response<OrderStatisticResponse>> {
    return commonRequestApi<OrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取退货单总数量、退款总金额
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticReturnOrderInUnit(
    request: ReturnOrderRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticReturnOrderInUnit,
    operation?: string
  ): Promise<Response<ReturnOrderStatisticResponse>> {
    return commonRequestApi<ReturnOrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
