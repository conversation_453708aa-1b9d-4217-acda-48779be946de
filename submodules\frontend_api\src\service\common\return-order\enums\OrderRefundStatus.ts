import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum OrderRefundStatusEnum {
  /**
   * 待审核
   */
  pendingAudit = 1,

  /**
   * 已取消
   */
  cancelled = 2,

  /**
   * 已拒绝
   */
  rejected = 3,

  /**
   * 待退货/款
   */
  pendingRefundOrReturn = 4,

  /**
   * 待退货
   */
  pendingReturn = 5,

  /**
   * 已退货
   */
  returned = 6,

  /**
   * 退货失败
   */
  returnFailed = 7,

  /**
   * 待确认退款
   */
  pendingConfirmRefund = 8,

  /**
   * 待退款
   */
  pendingRefund = 9,

  /**
   * 已退款
   */
  refunded = 10,

  /**
   * 退款失败
   */
  refundFailed = 11,

  /**
   * 退货/款成功
   */
  refundSuccess = 12
}

export const ReturnStatusFilterOptions = [
  OrderRefundStatusEnum.pendingAudit,
  OrderRefundStatusEnum.cancelled,
  OrderRefundStatusEnum.rejected,
  OrderRefundStatusEnum.pendingReturn,
  OrderRefundStatusEnum.returnFailed,
  OrderRefundStatusEnum.pendingConfirmRefund,
  OrderRefundStatusEnum.pendingRefund,
  OrderRefundStatusEnum.refundFailed,
  OrderRefundStatusEnum.refundSuccess
]

class OrderRefundStatus extends AbstractEnum<OrderRefundStatusEnum> {
  static enum = OrderRefundStatusEnum
  constructor(status?: OrderRefundStatusEnum) {
    super()
    this.current = status
    this.map.set(OrderRefundStatusEnum.pendingAudit, '待审核')
    this.map.set(OrderRefundStatusEnum.cancelled, '已取消')
    this.map.set(OrderRefundStatusEnum.rejected, '已拒绝')
    this.map.set(OrderRefundStatusEnum.pendingRefundOrReturn, '待退货/款')
    this.map.set(OrderRefundStatusEnum.pendingReturn, '待退货')
    this.map.set(OrderRefundStatusEnum.returned, '已退货')
    this.map.set(OrderRefundStatusEnum.returnFailed, '退货失败')
    this.map.set(OrderRefundStatusEnum.refundFailed, '退款失败')
    this.map.set(OrderRefundStatusEnum.pendingConfirmRefund, '待确认退款')
    this.map.set(OrderRefundStatusEnum.pendingRefund, '待退款')
    this.map.set(OrderRefundStatusEnum.refunded, '已退款')
    this.map.set(OrderRefundStatusEnum.refundSuccess, '退货/款成功')
  }

  /**
   *
   * @param dtoStatus 后端退货单状态 退货单状态 (0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
   * @param closeType 退货单关闭类型  （1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
   */
  transferDtoToCurrentEnum(dtoStatus: number, closeType: number) {
    if ([0, 1].includes(dtoStatus)) {
      return OrderRefundStatusEnum.pendingAudit
    }
    if (dtoStatus == 11) {
      if (closeType == 1 || closeType == 2) {
        return OrderRefundStatusEnum.cancelled
      }
      if (closeType == 3) {
        return OrderRefundStatusEnum.rejected
      }
    }
    if (dtoStatus == 2) {
      return OrderRefundStatusEnum.pendingReturn
    }
    if (dtoStatus == 3) {
      return OrderRefundStatusEnum.returnFailed
    }
    if ([4, 5].includes(dtoStatus)) {
      return OrderRefundStatusEnum.pendingConfirmRefund
    }
    if (dtoStatus == 6) {
      return OrderRefundStatusEnum.pendingRefund
    }
    if (dtoStatus == 7) {
      return OrderRefundStatusEnum.refundFailed
    }
    // todo 后端说 8,9,10 都是退货款成功  不存在单独退款成功或退货成功，对这个有问题有bug直接丢给后端
    // todo 补充一个99也是
    if ([8, 9, 10, 99].includes(dtoStatus)) {
      return OrderRefundStatusEnum.refundSuccess
    }
  }
}

export default new OrderRefundStatus()
