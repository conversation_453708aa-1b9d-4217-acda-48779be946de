<template>
  <div>
    <el-drawer title="作废" :visible.sync="cancelVisible" size="600px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form ref="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="发票号：" class="is-text">{{ cancelItem.invoiceNo || '-' }}</el-form-item>
              <el-form-item label="发票抬头：" class="is-text"
                >【{{ invoiceTitleMapType[cancelItem.titleType] }}】{{ cancelItem.title }}</el-form-item
              >
              <el-form-item class="m-btn-bar">
                <el-button @click="cancelVisible = false">取消</el-button>
                <el-button type="primary" @click="cancelInvoice">作废发票</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
    <el-dialog title="确认作废" :visible.sync="sureCancelVisible" width="450px" class="m-dialog">
      <div>
        <p>确认要作废该发票号？</p>
        <p>确认作废后，请重新开票！</p>
      </div>
      <div slot="footer">
        <el-button @click="sureCancelVisible = false">取 消</el-button>
        <el-button type="primary" @click="sureCancelInvoice">确认作废</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
  import { TitleTypeEnum } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import MutationOffLineInvoice from '@api/service/management/trade/single/invoice/mutation/MutationOffLineInvoice'
  import TradeModule from '@api/service/management/trade/TradeModule'

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    cancelDialog: boolean

    @Prop({
      type: Object
    })
    cancelItem: OffLinePageInvoiceVo

    mutationOffLineInvoice: MutationOffLineInvoice =
      TradeModule.singleTradeBatchFactor.invoiceFactor.mutationOffLineInvoice

    cancelVisible = false

    //确定作废发票弹窗
    sureCancelVisible = false

    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }

    @Watch('cancelDialog')
    changeDialogCtrl() {
      this.cancelVisible = this.cancelDialog
    }

    @Watch('cancelVisible')
    changeDialogVisible() {
      this.$emit('update:cancelDialog', this.cancelVisible)
    }

    //作废发票
    cancelInvoice() {
      this.sureCancelVisible = true
    }

    async sureCancelInvoice() {
      const res = await this.mutationOffLineInvoice.invalidInvoice(this.cancelItem.invoiceId)
      if (res.isSuccess()) {
        this.$message.success('作废发票成功')
        this.cancelVisible = false
        this.sureCancelVisible = false
        this.$emit('cancelSuccess')
      } else {
        this.$message.error('作废发票失败')
      }
    }

    created() {
      this.cancelVisible = this.cancelDialog
    }
  }
</script>
