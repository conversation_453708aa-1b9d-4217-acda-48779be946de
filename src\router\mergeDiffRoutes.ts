function normalizePath(path: string): string {
  // 移除路径中的参数部分，只保留基本路径
  return path.split('?')[0].split('#')[0]
}

export default function mergeDiffRoutes(completeRoutes: any[], diffRoutes: any[]) {
  // 递归地合并子路由
  const mergeChildrenRoutes = (mainChildren: any[], diffChildren: any[]) => {
    diffChildren.forEach((diffChild: any) => {
      const foundIndex = mainChildren.findIndex(
        (mainChild: any) => normalizePath(mainChild.path) === normalizePath(diffChild.path)
      )

      if (foundIndex !== -1) {
        // 如果在完整路由中找到了对应的子路由
        if (diffChild.children && diffChild.children.length > 0) {
          // 如果差异化路由有子路由，则递归合并子路由
          if (!mainChildren[foundIndex].children) {
            mainChildren[foundIndex].children = [] // 确保目标子路由有children数组
          }
          mergeChildrenRoutes(mainChildren[foundIndex].children, diffChild.children)
        }
        // 替换当前层级的路由配置（除了children和meta）
        const { children, meta, ...restDiffChild } = diffChild
        mainChildren[foundIndex] = { ...mainChildren[foundIndex], ...restDiffChild }
        // 如果meta存在且不同，使用diffChild中的meta
        if (meta && JSON.stringify(meta) !== JSON.stringify(mainChildren[foundIndex].meta)) {
          mainChildren[foundIndex].meta = meta
        }
      } else {
        // 如果没有找到，保留原来的路由
        mainChildren.push(diffChild)
      }
    })
  }

  // 遍历差异化路由并进行合并
  diffRoutes.forEach((diffRoute: any) => {
    const routeIndex = completeRoutes.findIndex(
      (route: any) => normalizePath(route.path) === normalizePath(diffRoute.path)
    )
    if (routeIndex !== -1) {
      // 替换当前层级的路由配置（除了children和meta）
      const { children, meta, ...restDiffRoute } = diffRoute
      completeRoutes[routeIndex] = { ...completeRoutes[routeIndex], ...restDiffRoute }
      // 如果meta存在且不同，使用diffRoute中的meta
      if (meta && JSON.stringify(meta) !== JSON.stringify(completeRoutes[routeIndex].meta)) {
        completeRoutes[routeIndex].meta = meta
      }
      // 如果差异化路由有子路由，则递归合并子路由
      if (diffRoute.children && diffRoute.children.length > 0) {
        if (!completeRoutes[routeIndex].children) {
          completeRoutes[routeIndex].children = [] // 确保目标路由有children数组
        }
        mergeChildrenRoutes(completeRoutes[routeIndex].children, diffRoute.children)
      }
    } else {
      // 如果完整路由中没有找到，直接添加差异化路由
      completeRoutes.push(diffRoute)
    }
  })

  return completeRoutes
}
