import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-information-gateway'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-information-gateway'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class InformationRequest {
  /**
   * 查询条件
   */
  key?: string
  /**
   * 接口类型 1-天眼查  2-企查查
   */
  type?: number
}

export class InformationResponse {
  /**
   * 企业id
   */
  id: string
  /**
   * 统一社会信用代码
   */
  creditCode: string
  /**
   * 企业名称
   */
  name: string
  /**
   * 法人
   */
  legalPersonName: string
  /**
   * 注册号
   */
  regNo: string
  /**
   * 企业状态
   */
  status: string
  /**
   * 成立日期 成立
   */
  establishTime: string
  /**
   * 基本信息来源类型 1-天眼查 2-企查查
   */
  basicInfoSourceType: number
  /**
   * 是否来自于缓存 1-是 2-否
   */
  isFromCache: number
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取当前系统时间
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCurrentTime(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCurrentTime,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 模糊查询企业信息
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listInformationByFuzzyInDistributor(
    request: InformationRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listInformationByFuzzyInDistributor,
    operation?: string
  ): Promise<Response<Array<InformationResponse>>> {
    return commonRequestApi<Array<InformationResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 模糊查询企业信息
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listInformationByFuzzyInPublic(
    request: InformationRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listInformationByFuzzyInPublic,
    operation?: string
  ): Promise<Response<Array<InformationResponse>>> {
    return commonRequestApi<Array<InformationResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
