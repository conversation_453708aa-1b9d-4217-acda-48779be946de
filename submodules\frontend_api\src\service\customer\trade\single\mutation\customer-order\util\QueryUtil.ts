import DistributionRegionInfo from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/DistributionRegionInfo'
import Aadminstrative from '@hbfe-biz/biz-dictionary/dist/Aadminstrative'
import TreeUtil, { Tree } from '@api/service/customer/trade/single/mutation/customer-order/util/TreeUtil'
import DataResolve from '@api/service/common/utils/DataResolve'
import { cloneDeep } from 'lodash'

/**
 * @description 地区树资源信息
 */
export class RegionTreeResource {
  /**
   * 唯一key值
   */
  key = ''
  /**
   * 地区全路径数组
   */
  regionPathList: string[] = []
}

/**
 * @description 查询工具类
 */
class QueryUtil {
  /**
   * 批量查询分销地区
   * @param list 地区树资源信息
   */
  async batchQueryRegionTree(list: RegionTreeResource[]): Promise<Map<string, DistributionRegionInfo[]>> {
    const result = new Map<string, DistributionRegionInfo[]>()
      // 1-拿到所有的业务地区构成的地区树
    ;(Aadminstrative as any).businessId = 'PLATFORM_BUSINESS_REGION'
    const basicDataTree = await Aadminstrative.queryRegion()
    // 2-树形结构转为数组
    const treeArr = TreeUtil.treeToList(basicDataTree)
    if (!list?.length) {
      return result
    }
    // 3-分批处理每个分销地区
    list.forEach(itm => {
      const tmpArr = cloneDeep(treeArr)
      const key = itm.key
      const codePathList = itm.regionPathList
      if (!codePathList.length) {
        return
      }
      // 4-填充列表选中状态
      codePathList.forEach(itm => {
        const target = tmpArr.find((el: Tree) => el.codePath === itm || el.code === itm)
        if (target) {
          target.isSelect = true
        }
      })
      // 5-从树形结构自下而上填充列表的选中状态
      TreeUtil.fillSelectByLevel(tmpArr, 2)
      TreeUtil.fillSelectByLevel(tmpArr, 1)
      // 6-获取选中的节点数组
      const selectedItems = TreeUtil.getSelectedItems(tmpArr)
      // 7-生成新的树形结构
      const newTree = TreeUtil.listToTree(selectedItems, '0')
      // 8-转化成分销地区树
      const distributionRegionTree = this.uiTreeToDistributionRegionTree(newTree, newTree)
      // 9-添加到结果集
      result.set(key, distributionRegionTree)
    })
    console.log('===result', result)
    return result
  }

  /**
   * 获取分销地区树
   * @param uiTree 带isSelect的地区树
   * @param allTree 完整的树
   * @private
   */
  private uiTreeToDistributionRegionTree(uiTree: Tree[], allTree: Tree[]) {
    const result = [] as DistributionRegionInfo[]
    uiTree?.forEach(itm => {
      const opt = new DistributionRegionInfo()
      opt.regionPath = itm.codePath
      // 获取从根节点到叶子节点的路径
      const tmpNodeList = TreeUtil.treeFindPathByNode(allTree, itm.codePath)
      opt.regionCode = DataResolve.unique(tmpNodeList.map(el => el.code))
      opt.regionName = DataResolve.unique(tmpNodeList.map(el => el.name))
      opt.regionLevel = opt.regionCode.length
      opt.children = this.uiTreeToDistributionRegionTree(itm.children, allTree)
      result.push(opt)
    })
    return result
  }
}

export default new QueryUtil()
