/**
 * 试题类型
 */
export enum QuestionType {
  // 判断题
  JUDGEMENT = 'JUDGEMENT',
  // 单选题
  SINGLE_CHOICE = 'SINGLE_SELECTION',
  // 多选题
  MULTIPLE_CHOICE = 'MULTIPLE_SELECTION',
  // 填空题
  BLANK_FILLING = 'BLANK_FILLING',
  // 简答题
  ESSAY = 'ESSAY',
  // 案例题
  COMPREHENSIVE = 'COMPREHENSIVE'
}

/**
 * 试题难度
 */
export enum QuestionMode {
  // 简单
  EASY = 'EASY',
  // 普通
  NORMAL = 'NORMAL',
  // 困难
  DIFFICULT = 'DIFFICULT'
}

/**
 * 填空题回答类型
 */
export enum BlankFillingAnswerType {
  // 多组答案
  MULTIPLE_SETS = 1,
  // 每空多答案
  MULTIPLE_PER_BLANK
}

/**
 * 试卷配置类型
 */
export enum ExamConfigType {
  // 固定卷
  FIXED = 1,
  // AB卷
  GROUP,
  // 智能卷
  RANDOM
}

/**
 * 试卷计时方式
 */
export enum PaperTimeType {
  // 整卷计时
  WHOLE_PAPER,
  // 单题计时
  TIME_TYPE_SINGLE
}

/**
 * 随机卷类型
 */
export enum RandomPaperType {
  OBJECT_SUPPORT_RATIO = 5,
  NEW_TAG = 6
}

/**
 * 试题来源
 */
export enum QuestionSourceConstants {
  normal = 1,
  notAnswerYet,
  answered,
  favorite
}

/**
 * 组件方式
 */
export enum MakePaperType {
  // 手动组卷
  hand,
  // 只能组卷
  AI
}

/**
 * 组件方式
 */
export enum PaperConfigType {
  // 固定卷
  Fixed = 1,
  // 随机卷
  Random = 3
}
