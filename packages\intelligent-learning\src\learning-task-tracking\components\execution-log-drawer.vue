<template>
  <div v-if="$hasPermission('logExecution')" desc="查看执行日志" actions="querySearch">
    <el-drawer title="查看执行日志" :visible.sync="visible" size="1000px" custom-class="m-drawer">
      <div class="drawer-bd">
        <!--表格-->
        <el-table stripe :data="tableData" class="m-table f-mt10" v-loading="loading">
          <el-table-column type="index" label="NO." width="80"> </el-table-column>
          <el-table-column label="执行状态更新时间" min-width="100">
            <template slot-scope="scope">{{ scope.row.updateTime }}</template>
          </el-table-column>
          <el-table-column label="执行状态更新原因" min-width="160">
            <template slot-scope="scope">{{ scope.row.updateReason }}</template>
          </el-table-column>
          <el-table-column label="执行状态" min-width="80">
            <template slot-scope="scope">
              <div v-if="scope.row.status.current == ExecutionStatusEnum.fail">
                <el-badge is-dot type="danger" class="badge-status">执行失败</el-badge>
              </div>
              <div v-else-if="scope.row.status.current == ExecutionStatusEnum.stop">
                <el-badge is-dot type="info" class="badge-status">终止</el-badge>
              </div>
              <div v-else-if="scope.row.status.current == ExecutionStatusEnum.complete">
                <el-badge is-dot type="success" class="badge-status">已完成</el-badge>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column label="智能学习操作类型" min-width="100">
            <template slot-scope="scope">{{ scope.row.operationType }}</template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <div v-if="scope.row.status.current === ExecutionStatusEnum.fail">-</div>
              <template v-if="$hasPermission('executionDetail')" desc="查看执行日志详情" actions="@DetailLogDrawer">
                <el-button
                  v-if="scope.row.status.current !== ExecutionStatusEnum.fail"
                  type="text"
                  @click="searchDetail(scope.row)"
                  >查看详情</el-button
                >
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button type="primary" @click="callBack">返 回</el-button>
      </div>
      <detail-log-drawer ref="detailRef" title="查看执行日志" />
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { Vue, Component, Ref } from 'vue-property-decorator'
  import ExecutionItem from '@api/service/management/intelligence-learning/model/ExecutionItem'
  import ExecutionLogItem from '@api/service/management/intelligence-learning/model/ExecutionLogItem'
  import DetailLogDrawer from '@hbfe/jxjy-admin-intelligentLearning/src/learning-task-tracking/components/detail-log-drawer.vue'
  import { LogTypeEnum } from '@api/service/management/intelligence-learning/enum/LogType'
  import { ExecutionStatusEnum } from '@api/service/management/intelligence-learning/enum/ExecutionStatusEnum'
  import { bind, debounce } from 'lodash-decorators'

  @Component({
    components: {
      DetailLogDrawer
    }
  })
  export default class extends Vue {
    @Ref('detailRef') detailRef: DetailLogDrawer
    /**
     * 显隐
     */
    visible = false

    /**
     * 表格数据
     */
    tableData = new Array<ExecutionLogItem>()

    /**
     * 执行状态枚举
     */
    ExecutionStatusEnum = ExecutionStatusEnum

    /**
     * 加载状态
     */
    loading = false

    /**
     * 存储数据
     */
    executionItem = new ExecutionItem()

    /**
     * 打开弹框
     */
    async open(row: ExecutionItem) {
      this.tableData = new Array<ExecutionLogItem>()
      this.visible = true
      Object.assign(this.executionItem, row)
      await this.querySearch()
    }

    /**
     * 查询操作
     */
    async querySearch() {
      try {
        this.loading = true
        this.tableData = await this.executionItem.queryExecutionLogList()
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 返回
     */
    callBack() {
      this.visible = false
    }

    /**
     * 查看详情
     */
    @bind
    @debounce(200)
    async searchDetail(row: ExecutionLogItem) {
      await this.detailRef.open(row.logId, LogTypeEnum.execution)
    }
  }
</script>
