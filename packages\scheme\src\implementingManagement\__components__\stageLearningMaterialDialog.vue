<template>
  <el-drawer
    title="期别学习资料设置"
    :visible.sync="openDialog"
    size="800px"
    custom-class="m-drawer"
    :wrapper-closable="false"
  >
    <div class="drawer-bd">
      <upload-material-list :table-data="periodConfig.cacheAnnexList" />
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="cancelSetAnnex">取消</el-button>
      <el-button type="primary" :loading="uiLoading.saveLoading" @click="save">确定</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Inject, Prop, Vue } from 'vue-property-decorator'
  import PeriodConfig from '@api/service/management/implement/models/PeriodConfig'
  import UploadMaterialList from '@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/upload-material-list.vue'
  import { bind, debounce } from 'lodash-decorators'
  import SchemeStepProcess from '@api/service/management/train-class/Utils/SchemeStepProcess'
  import { SchemeProcessStatusEnum } from '@api/service/management/train-class/query/enum/SchemeProcessStatusEnum'

  @Component({
    components: {
      UploadMaterialList
    }
  })
  export default class extends Vue {
    /**
     * 期别对象
     */
    @Prop({
      type: PeriodConfig,
      required: true
    })
    periodConfig: PeriodConfig

    @Inject('SchemeStepProcess')
    SchemeStepProcess: SchemeStepProcess

    uiLoading = {
      saveLoading: false
    }

    openDialog = false

    /**
     * 保存期别学习资料
     */
    @debounce(200)
    @bind
    async save() {
      if (this.periodConfig.cacheAnnexList.length === 0) {
        this.$message.warning('请添加学习资料')
        return
      }
      this.uiLoading.saveLoading = true
      try {
        const schemeId = this.$route.params.schemeId
        const res = await this.SchemeStepProcess.checkSchemeCanModify(schemeId)
        if (res.code === SchemeProcessStatusEnum.finish) {
          await this.periodConfig.setAnnex()
          this.openDialog = false
          this.$emit('refresh')
        } else {
          this.$message.error(res.message as string)
        }
      } catch (e) {
        this.$message.error(e.message)
        console.error(e)
      } finally {
        this.uiLoading.saveLoading = false
      }
    }

    /**
     * 取消期别学习资料设置
     */
    async cancelSetAnnex() {
      await this.periodConfig.cancelAnnex()
      this.openDialog = false
    }
  }
</script>
