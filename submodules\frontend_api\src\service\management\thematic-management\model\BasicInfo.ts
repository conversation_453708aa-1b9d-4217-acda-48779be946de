import { SaveTrainingChannelInfoRequest } from '@api/platform-gateway/platform-training-channel-v1'
import { DomainTypeEnum } from '../enum/DomainType'
import { SubjectType } from '../enum/SubjectType'

/**
 * 基础信息类
 */
export default class BasicInfo {
  /**
   * 专题入口名称
   */
  entryName = ''
  /**
   * 专题名称
   */
  subjectName = ''
  /**
   * 网校名称
   */
  onlineSchoolName = ''
  /**
   * 域名类型
   */
  domainType: DomainTypeEnum = undefined
  /**
   * 专题域名
   */
  subjectDomain = ''
  /**
   * 专题类型
   */
  subjectType: SubjectType[] = []
  /**
   * 地区
   */
  suiteArea = ''
  /**
   * 行业
   */
  suiteIndustry = ''
  /**
   * 单位名称
   */
  unitName = ''
  /**
   * 专题类型是地区时候有值
   */
  countyName = '' //区
  cityName = '' //市
  provinceName = '' //省
  /**
   * 是否显示在网校
   */
  displayInSchool = true
  /**
   * web专题模板
   * 编号 Temlpate-Web-01
   * 编号 Temlpate-Web-02
   * 由UI决定编号值
   */
  templateWeb = ''
  /**
   * H5专题模板
   * 编号 Temlpate-H5-01
   * 编号 Temlpate-H5-02
   * 由UI决定编号值
   */
  templateH5 = ''

  /**
   * 专题是否允许访问
   */
  allowAccess = true

  static to(vo: BasicInfo) {
    const dto = new SaveTrainingChannelInfoRequest()
    dto.domainName = vo.subjectDomain
    dto.domainNameType = vo.domainType
    dto.entryName = vo.entryName
    dto.h5TemplateNo = vo.templateH5
    dto.name = vo.subjectName
    dto.pcTemplateNo = vo.templateWeb
    dto.showOnNetSchool = vo.displayInSchool
    dto.suiteAreaList = vo.suiteArea ? [vo.suiteArea] : undefined
    dto.suiteIndustryList = vo.suiteIndustry ? [vo.suiteIndustry] : undefined
    dto.complexTypes = vo.subjectType
    dto.unitName = vo.unitName
    dto.notAllowAccess = !vo.allowAccess
    return dto
  }
}
