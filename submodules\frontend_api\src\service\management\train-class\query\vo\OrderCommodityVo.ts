import {
  CommoditySkuPropertyResponse1,
  CommoditySkuResponse,
  SchemeResourceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import { SkuPropertyConvertUtils } from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import SkuVo from '@api/service/management/train-class/query/vo/SkuVo'
import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
import { ExchangeGoodsStatusEnum } from '@api/service/management/train-class/query/enum/ExchangeGoodsStatusList'

/**
 * @description 培训方案信息Vo
 */
class SchemeResourceResponseVo extends SchemeResourceResponse {
  /**
   * 根据schemeType转换而来的vo属性
   */
  schemeTypeVo: TrainClassSchemeEnum = null

  /**
   * 期别名称
   */
  periodName: string = undefined

  /**
   * 期别编号
   */
  periodNo: string = undefined

  static from(response: SchemeResourceResponse): SchemeResourceResponseVo {
    const detail = response as SchemeResourceResponseVo
    detail.schemeTypeVo = SchemeResourceResponseVo.getSchemeType(response.schemeType)
    return detail
  }

  /**
   * 获取培训方案类型
   */
  static getSchemeType(type: string): TrainClassSchemeEnum | null {
    if (!type) return null
    if (type === 'chooseCourseLearning') {
      return TrainClassSchemeEnum.Choose_Course_Learning
    }
    if (type === 'autonomousCourseLearning') {
      return TrainClassSchemeEnum.Autonomous_Course_Learning
    }
    return null
  }
}

/**
 * @description 订单商品信息返回值
 */
class OrderCommodityVo extends CommoditySkuResponse {
  /**
   * 换班物品状态
   */
  exchangeGoodsStatus: ExchangeGoodsStatusEnum = null

  /**
   * 根据resource转换而来的vo属性
   */
  resourceVo: SchemeResourceResponseVo = new SchemeResourceResponseVo()

  /**
   * 商品sku属性值
   */
  skuValueNameProperty: SkuPropertyResponseVo = new SkuPropertyResponseVo()

  /**
   * 获取对象值
   */
  static getOrderCommodity(commodity: CommoditySkuResponse): OrderCommodityVo {
    const detail = commodity as OrderCommodityVo
    const resource = detail.resource as SchemeResourceResponse
    detail.exchangeGoodsStatus = OrderCommodityVo.getExchangeGoodsStatus(commodity)
    detail.resourceVo = SchemeResourceResponseVo.from(resource)
    // detail.skuValueNameProperty = await OrderCommodityVo.convertSkuPropertyToValueName(detail.skuProperty)
    const skuValueNameProperty = new SkuPropertyResponseVo()
    // 行业
    skuValueNameProperty.industry.skuPropertyName =
      detail.skuProperty.industry?.skuPropertyValueShowName || detail.skuProperty.industry?.skuPropertyValueName
    skuValueNameProperty.year.skuPropertyName =
      detail.skuProperty.year?.skuPropertyValueShowName || detail.skuProperty.year?.skuPropertyValueName
    // 科目类型
    skuValueNameProperty.subjectType.skuPropertyName =
      detail.skuProperty.subjectType?.skuPropertyValueShowName || detail.skuProperty.subjectType?.skuPropertyValueName
    // 培训类别
    skuValueNameProperty.trainingCategory.skuPropertyName =
      detail.skuProperty.trainingCategory?.skuPropertyValueShowName ||
      detail.skuProperty.trainingCategory?.skuPropertyValueName
    // 专业
    skuValueNameProperty.trainingMajor.skuPropertyName =
      detail.skuProperty.trainingProfessional?.skuPropertyValueShowName ||
      detail.skuProperty.trainingProfessional?.skuPropertyValueName
    // 原技术等级
    skuValueNameProperty.jobLevel.skuPropertyName =
      detail.skuProperty.jobLevel?.skuPropertyValueShowName || detail.skuProperty.jobLevel?.skuPropertyValueName
    // 新技术等级
    skuValueNameProperty.technicalGrade.skuPropertyName =
      detail.skuProperty.technicalGrade?.skuPropertyValueShowName ||
      detail.skuProperty.technicalGrade?.skuPropertyValueName
    // 培训对象
    skuValueNameProperty.trainingObject.skuPropertyName =
      detail.skuProperty.trainingObject?.skuPropertyValueShowName ||
      detail.skuProperty.trainingObject?.skuPropertyValueName
    // 岗位类别
    skuValueNameProperty.positionCategory.skuPropertyName =
      detail.skuProperty.positionCategory?.skuPropertyValueShowName ||
      detail.skuProperty.positionCategory?.skuPropertyValueName
    // 学段
    skuValueNameProperty.learningPhase.skuPropertyName =
      detail.skuProperty.learningPhase?.skuPropertyValueShowName ||
      detail.skuProperty.learningPhase?.skuPropertyValueName
    // 学科
    skuValueNameProperty.discipline.skuPropertyName =
      detail.skuProperty.discipline?.skuPropertyValueShowName || detail.skuProperty.discipline?.skuPropertyValueName
    detail.skuValueNameProperty = skuValueNameProperty
    skuValueNameProperty.certificatesType.skuPropertyName =
      detail.skuProperty.certificatesType?.skuPropertyValueShowName ||
      detail.skuProperty.certificatesType?.skuPropertyValueName
    skuValueNameProperty.practitionerCategory.skuPropertyName =
      detail.skuProperty.practitionerCategory?.skuPropertyValueShowName ||
      detail.skuProperty.practitionerCategory?.skuPropertyValueName
    return detail
  }

  /**
   * 将sku属性转为名称
   */
  static async convertSkuPropertyToValueName(source: CommoditySkuPropertyResponse1): Promise<SkuPropertyResponseVo> {
    const factory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory
    const result = new SkuPropertyResponseVo()
    let skuIds = []
    // 年度
    if (source.year) {
      skuIds = [source.year?.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calYear(skuIds, factory)
      result.year = skuVos[0] || new SkuVo()
    }
    // 地区
    skuIds = []
    if (source.province?.skuPropertyValueId) skuIds.push(source.province.skuPropertyValueId)
    if (source.city?.skuPropertyValueId) skuIds.push(source.city.skuPropertyValueId)
    if (source.county?.skuPropertyValueId) skuIds.push(source.county.skuPropertyValueId)
    const skuVos = await SkuPropertyConvertUtils.calBusinessRegion(skuIds, factory)
    console.log('skuIds=', skuIds, 'skuVo=', skuVos)
    let regionSkuId = '',
      regionSkuName = ''
    skuVos?.forEach((item) => {
      regionSkuId += !regionSkuId ? item.skuPropertyValueId : '/' + item.skuPropertyValueId
      regionSkuName += !regionSkuName ? item.skuPropertyName : '/' + item.skuPropertyName
    })
    const regionSku = new SkuVo()
    regionSku.skuPropertyName = regionSkuName
    regionSku.skuPropertyValueId = regionSkuId
    console.log('orderRegionSku', regionSku)
    result.region = regionSku || new SkuVo()

    // 行业
    if (source.industry) {
      skuIds = [source.industry?.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calIndustry(skuIds, factory)
      result.industry = skuVos[0] || new SkuVo()
    }

    // 科目类型
    if (source.subjectType) {
      skuIds = [source.subjectType?.skuPropertyValueId]
      if (source.industry?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calTrainingProperty(
          skuIds,
          factory,
          source.industry.skuPropertyValueId
        )
        result.subjectType = skuVos[0] || new SkuVo()
      }
    }

    // 培训类别
    if (source.trainingCategory) {
      skuIds = [source.trainingCategory?.skuPropertyValueId]
      if (source.industry?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calTrainingCategory(
          skuIds,
          factory,
          source.industry.skuPropertyValueId
        )
        result.trainingCategory = skuVos[0] || new SkuVo()
      }
    }

    // 培训专业
    if (source.trainingProfessional) {
      skuIds = [source.trainingProfessional?.skuPropertyValueId]
      if (source.industry?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calTrainingMajor(
          skuIds,
          factory,
          source.industry.skuPropertyValueId
        )
        result.trainingMajor = skuVos[0] || new SkuVo()
      }
    }

    // 培训专业
    if (source.technicalGrade) {
      skuIds = [source.technicalGrade?.skuPropertyValueId]
      if (source.industry?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calTechnicalGrade(skuIds, factory)
        result.technicalGrade = skuVos[0] || new SkuVo()
      }
    }

    // 岗位类别
    if (source.positionCategory) {
      skuIds = [source.positionCategory?.skuPropertyValueId]
      if (source.positionCategory?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calPropertyObject(skuIds, source.industry.skuPropertyValueId)
        result.positionCategory = skuVos[0] || new SkuVo()
      }
    }

    // 培训对象
    if (source.trainingObject) {
      skuIds = [source.trainingObject?.skuPropertyValueId]
      if (source.trainingObject?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calPropertyObject(skuIds, source.industry.skuPropertyValueId)
        result.trainingObject = skuVos[0] || new SkuVo()
      }
    }

    // 培训对象
    if (source.jobLevel) {
      skuIds = [source.jobLevel?.skuPropertyValueId]
      if (source.jobLevel?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calPropertyObject(skuIds, source.industry.skuPropertyValueId)
        result.jobLevel = skuVos[0] || new SkuVo()
      }
    }
    // 学科、学段
    if (source.learningPhase) {
      skuIds = [source.learningPhase.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calTrainingPhase(skuIds, source.industry.skuPropertyValueId)
      result.learningPhase = (skuVos?.length && skuVos[0]) || new SkuVo()
    }
    if (source.discipline) {
      skuIds = [source.discipline.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calTrainingDiscipline(skuIds, source.industry.skuPropertyValueId)
      result.discipline = (skuVos?.length && skuVos[0]) || new SkuVo()
    }
    if (source.certificatesType) {
      skuIds = [source.certificatesType.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calCertAndPractitioner(skuIds, source.industry.skuPropertyValueId)
      result.certificatesType = (skuVos?.length && skuVos[0]) || new SkuVo()
    }
    if (source.practitionerCategory) {
      skuIds = [source.practitionerCategory.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calCertAndPractitioner(skuIds, source.industry.skuPropertyValueId)
      result.practitionerCategory = (skuVos?.length && skuVos[0]) || new SkuVo()
    }
    console.log('转换的sku对象', result)
    return result
  }

  /**
   * 获取换班物品状态
   */
  static getExchangeGoodsStatus(commodity: CommoditySkuResponse): ExchangeGoodsStatusEnum {
    // TODO
    return null
  }
}

export default OrderCommodityVo
