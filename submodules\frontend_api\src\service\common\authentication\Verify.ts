import basicDataDomain, {
  ApplyOpenIdRequest,
  ApplyOpenIdResponse,
  SmsCodeApplyRequest,
  ValidIdentityFxsRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import Authentication from '@api/service/common/authentication/Authentication'
import {
  applyCaptchaUrl,
  sendShortMessageCodeUrl,
  sendShortMessageCodeUrlApply,
  sendShortMessageCodeWithoutCaptchaUrl,
  validateCaptchaUrl
} from '@api/service/common/authentication/contant'
import { SendMessageParams } from '@api/service/common/authentication/interfaces/LoginParams'
import Context from '@api/service/common/context/Context'
import BizVerify from '@hbfe-biz/biz-authentication/dist/Verify'
import { BusinessTypeEnum } from '@hbfe-biz/biz-authentication/src/enums/BusinessTypeEnum'
import { ValidIdentityRequest } from '@hbfe-ms/ms-basicdata-domain-gateway/src'

export default class Verify extends BizVerify {
  constructor(context: Authentication) {
    super()
    this.context = context
  }

  context: Authentication
  isCaptchaValidate = false // * 是否验证验图形验证码

  /**
   * 获取验证码
   * // 时效性30秒？
   */
  async applyJXJYFXSCaptcha() {
    const result = await this.context.options.request.get(`${this.context.options.authUrl}${applyCaptchaUrl}`)
    this.captchaTicket = result.data.captchaTicket
    return result
  }

  /**
   * 验证验证码
   * @param captchaValue
   */
  async validateCaptcha(captchaValue: string) {
    if (captchaValue === this.validatedCaptchaValue) {
      return this.isCaptchaValidate
    }
    const result = await this.context.options.request.get(
      `${this.context.options.authUrl}${validateCaptchaUrl}/${this.captchaTicket}/${captchaValue}`
    )
    this.captchaToken = result.data.captchaToken
    this.isCaptchaValidate = result.data.pass
    if (!this.isCaptchaValidate) {
      return Promise.reject(result.data)
    }
    this.validatedCaptchaValue = captchaValue
    return result
  }

  /**
   * 发送短信验证码（不需要图形验证码）
   * @param sendMessageParams
   */
  async sendMessageWithoutCaptcha(sendMessageParams: SendMessageParams): Promise<any> {
    sendMessageParams.service = this.context.options.service
    return this.context.options.request.post(`${this.context.options.ssoUrl}${sendShortMessageCodeWithoutCaptchaUrl}`, {
      data: sendMessageParams
    })
  }
  /**
   * 发送短信验证码
   * @param sendMessageParams
   */
  async sendMessageNoValidate(sendMessageParams: SendMessageParams): Promise<any> {
    sendMessageParams.service = this.context.options.service
    sendMessageParams.captchaToken = this.captchaToken
    const result = await this.context.options.request.post(
      `${this.context.options.authUrl}${sendShortMessageCodeUrlApply}`,
      {
        data: sendMessageParams
      }
    )
    this.shortMessageCaptchaTicket = result.data.captchaTicket
    return result
  }

  /**
   * 发送短信验证码 - web端
   * @param sendMessageParams
   */
  async sendMessage(sendMessageParams: SendMessageParams): Promise<any> {
    sendMessageParams.service = this.context.options.service
    sendMessageParams.captchaToken = this.captchaToken
    return this.context.options.request.post(`${this.context.options.ssoUrl}${sendShortMessageCodeUrl}`, {
      data: sendMessageParams
    })
  }

  /**
   * 申请获取微信平台Openid ---- H5使用
   */
  async applyOpenIdByServicerId(code: string): Promise<ApplyOpenIdResponse> {
    const request = new ApplyOpenIdRequest()
    request.code = code
    request.servicerId = Context.servicerInfo.id
    const res = await basicDataDomain.applyOpenIdByServicerId(request)
    if (res?.data?.code == '200') {
      localStorage.setItem('customer.WX-openId', res.data.openId)
      localStorage.setItem('customer.WX-unionId', res.data.unionId)
      localStorage.setItem('customer.WX-nickname', res.data.nickname)
    }
    return res.data
  }

  /**
   * 验证身份——分销商管理员
   * @param params
   */
  async validIdentityForFxs(params: ValidIdentityRequest) {
    const request = Object.assign(new ValidIdentityFxsRequest(), params)
    request.businessType = BusinessTypeEnum.forgot_password
    const response = await basicDataDomain.validIdentityForFxs(request)
    if (response.data.code != 200 || !response.status.isSuccess()) {
      console.error('验证身份失败', response)
    }
    this.validIdentityToken = response.data?.token
    return response
  }

  /**
   * 分销商未登录获取短信验证码
   * @param phone
   */
  async applySmsCodeForLoginDistributor(phone: string) {
    const params = new SmsCodeApplyRequest()
    params.token = this.captchaToken
    params.phone = phone
    params.businessType = BusinessTypeEnum.change_binding_phone
    const response = await basicDataDomain.applySmsCodeForLoginDistributor(params)
    if (!response.status.isSuccess() || response.data.code != 200) {
      console.error('获取短信验证码报错', response)
      return response
    }
    this.shortMessageCaptchaTicket = response.data.token
    return response
  }
  /**
   * 分销商登录获取短信验证码
   * @param phone
   */
  async applySmsCodeForNeedLoginDistributor(phone: string) {
    const params = new SmsCodeApplyRequest()
    params.token = this.captchaToken
    params.phone = phone
    params.businessType = BusinessTypeEnum.change_binding_phone
    const response = await basicDataDomain.applySmsCodeForLoginDistributorNeedLogin(params)
    if (!response.status.isSuccess() || response.data.code != 200) {
      console.error('获取短信验证码报错', response)
      return response
    }
    this.shortMessageCaptchaTicket = response.data.token
    return response
  }
}
