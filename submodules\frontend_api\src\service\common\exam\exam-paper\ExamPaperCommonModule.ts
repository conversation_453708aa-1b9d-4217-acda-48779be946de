import { Action, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import ExamGateway, { ExamPaperCreateRequest, ExamPaperUpdateRequest } from '@api/gateway/btpx@GeneralExam-default'
import ExamPaperCreate from '@api/service/common/models/exam/paper/exam-paper/ExamPaperCreate'
import ExamPaperUpdate from '@api/service/common/models/exam/paper/exam-paper/ExamPaperUpdate'
import ExamPaper from '@api/service/common/models/exam/paper/exam-paper/ExamPaper'

export interface IExamPaperCommonState<TExamPaper extends ExamPaper> {
  /**
   * 试卷列表
   */
  paperList: Array<TExamPaper>

  /**
   * 总数
   */
  totalSize: number
}

@Module({ namespaced: true, store, dynamic: true, name: 'CommonExamPaperCommonModule' })
class ExamPaperCommonModule<
  TExamPaper extends ExamPaper,
  TExamPaperCreate extends ExamPaperCreate,
  TExamPaperUpdate extends ExamPaperUpdate
> extends VuexModule implements IExamPaperCommonState<TExamPaper> {
  paperList = new Array<TExamPaper>()
  totalSize = 0

  /**
   * 加载考试试卷
   */
  @Action
  async loadExamPaper(paperId: string) {
    const response = await ExamGateway.getExamPaper(paperId)
    if (response.status.isSuccess()) {
      const result = new ExamPaper() as TExamPaper
      Object.assign(result, response.data)
      this.addOrReplaceExamPaper(result)
    }
    return response.status
  }

  /**
   * 创建考试试卷
   * @param create
   */
  @Action
  async createExamPaper(create: TExamPaperCreate) {
    const request = new ExamPaperCreateRequest()
    Object.assign(request, create)
    const response = await ExamGateway.createExamPaper(request)

    return response.status
  }

  /**
   * 修改考试试卷
   * @param update
   */
  @Action
  async updateExamPaper(update: TExamPaperUpdate) {
    const request = new ExamPaperUpdateRequest()
    Object.assign(request, update)
    const response = await ExamGateway.updateExamPaper(request)

    return response.status
  }

  /**
   * 添加或替换试卷
   * @param examPaper
   */
  @Mutation
  addOrReplaceExamPaper(examPaper: TExamPaper) {
    if (this.paperList.some(x => x.id === examPaper.id)) {
      this.paperList = this.paperList.map(x => {
        if (x.id === examPaper.id) {
          return examPaper
        }
        return x
      })
    } else {
      this.paperList.push(examPaper)
    }
  }

  /**
   * 设置试卷
   * @param examPapers
   */
  @Mutation
  setExamPaper(examPapers: Array<TExamPaper>) {
    this.paperList = examPapers
  }

  /**
   * 总数
   * @param totalSize
   */
  @Mutation
  setTotalSize(totalSize: number) {
    this.totalSize = totalSize
  }

  get getExamPaper() {
    return (paperId: string) => {
      return this.paperList.find(x => x.id === paperId)
    }
  }
}

export default ExamPaperCommonModule
