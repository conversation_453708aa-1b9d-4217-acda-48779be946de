/**
 * 操作修改配送方式
 */
import { ResponseStatus } from '@hbfe/common'
import MsOfflineinvoiceV1, { UpdateDeliveryChannelShippingMethodsRequest } from '@api/ms-gateway/ms-offlineinvoice-v1'
import { DeliverWayTypeEnum } from '@api/service/common/trade-config/query/enums/DeliverWayType'

class MutationModifyDeliverWay {
  isInitDataNull = false
  deliverWays: Array<DeliverWayTypeEnum> = new Array<DeliverWayTypeEnum>()

  setDeliverWays(ways: Array<DeliverWayTypeEnum> = new Array<DeliverWayTypeEnum>()) {
    if (!ways.length) {
      this.isInitDataNull = true
    }
    this.deliverWays = ways
  }

  hasConfig() {
    return !!this.deliverWays.length
  }

  hasType(type: DeliverWayTypeEnum) {
    return this.deliverWays.find(way => way === type)
  }

  async doModify(): Promise<ResponseStatus> {
    const request = new UpdateDeliveryChannelShippingMethodsRequest()
    request.shippingMethodList = this.deliverWays.map(way => way).filter(item => item)
    // 如果是第一次初始化值认为是空的，则执行的任务是创建动作
    if (this.isInitDataNull) {
      const { status } = await MsOfflineinvoiceV1.createChannelShippingMethods(request)
      this.isInitDataNull = false
      return status
    }
    const { status } = await MsOfflineinvoiceV1.updateChannelShippingMethods(request)
    this.isInitDataNull = !request.shippingMethodList.length
    return status
  }
}

export default MutationModifyDeliverWay
