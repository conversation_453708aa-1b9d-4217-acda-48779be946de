import UpdateReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/UpdateReceiveAccountVo'
import { ReceiveAccountConfigResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import {
  CreateReceiveAccountRequest,
  ReceiveAccountExtProperty,
  UpdateReceiveAccountRequest
} from '@api/ms-gateway/ms-trade-configuration-v1'

export default class UpdateXDLPayReceiveAccountVo extends UpdateReceiveAccountVo {
  /**
   * 支付商户号
   */
  payMerchantId = ''
  /**
   * 代理商号
   */
  proxyId = ''
  /**
   * 密钥
   */
  xdlPrivateKey = ''

  from(res: ReceiveAccountConfigResponse) {
    this.id = res.id
    this.accountType = res.accountType
    this.accountNo = res.accountNo
    this.accountName = res.name
    this.taxPayerId = res.taxPayerId
    this.refundWay = res.returnType
    this.paymentChannelId = res.paymentChannelId
    this.qrScanPrompt = res.qrScanPrompt
    // JSON
    const temp = res.encryptionKeyData as any
    this.payMerchantId = res.accountNo
    this.proxyId = temp?.proxyNo
    this.xdlPrivateKey = temp?.privateKey
  }

  to(): CreateReceiveAccountRequest {
    const updateReceiveAccountRequest = new UpdateReceiveAccountRequest()
    updateReceiveAccountRequest.receiveAccountId = this.id
    updateReceiveAccountRequest.name = this.accountName
    updateReceiveAccountRequest.qrScanPrompt = this.qrScanPrompt
    updateReceiveAccountRequest.refundWay = this.refundWay
    updateReceiveAccountRequest.taxPayerId = this.taxPayerId
    updateReceiveAccountRequest.properties = new Array<ReceiveAccountExtProperty>()
    // this.updateProperties('storeNo', this.payMerchantId, updateReceiveAccountRequest.properties)
    this.updateProperties('proxyNo', this.proxyId, updateReceiveAccountRequest.properties)
    this.updateProperties('privateKey', this.xdlPrivateKey, updateReceiveAccountRequest.properties)
    return updateReceiveAccountRequest
  }

  private updateProperties(propertyName: string, propertyValue: string, properties: Array<ReceiveAccountExtProperty>) {
    const property = properties?.find((item) => item.name === propertyName)
    if (property) {
      property.value = propertyValue
    } else {
      const item = new ReceiveAccountExtProperty()
      item.name = propertyName
      item.value = propertyValue
      properties.push(item)
    }
    return properties
  }
}
