import Training, {
  SetTrainingChannelSettingRequest,
  TrainingChannelSettingExtReq
} from '@api/platform-gateway/platform-training-channel-v1'
import IndustryRule from '@api/service/management/thematic-management/model/IndustryRule'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import { RuleTypeEnum } from '@api/service/management/thematic-management/enum/RuleType'

export default class ThematicManagementItemBase {
  /**
   * 配置专题ID
   */
  id = ''
  /**
   * 是否开启专题
   */
  isOpen = false
  /**
   * 是否网校展示入口
   */
  isShow = false
  /**
   * 名称
   */
  name = ''
  /**
   * 引导语
   */
  guide = ''
  /**
   * 网校展示专题方案
   */
  showSpecialScheme = false
  /**
   * 行业规则
   */
  industryRule: Array<IndustryRule> = []
  /**
   * 获取基础配置
   */
  async getBaseConfig() {
    const res = await Training.getTrainingChannelSetting()
    this.id = res.data.id
    this.isOpen = res.data.isOpenTrainingChannel
    this.isShow = res.data.isShowEntry
    this.name = res.data.name
    this.guide = res.data.guideExpression
    this.showSpecialScheme = res.data.isShowTrainingChannelScheme
    this.industryRule = []
    await Promise.all([QueryIndustry.getIndustryDICT(), QueryIndustry.queryIndustry()])
    QueryIndustry.industryDICTList.forEach(item => {
      const vo = new IndustryRule(item.id)
      const enableIndustry = QueryIndustry.industryList.find(ite => ite.id === item.id)
      const configIndustry = res.data.trainingChannelSettingExtList?.find(ite => ite.objectId === item.id)
      if (enableIndustry) {
        vo.isEnable = true
        vo.industryName = enableIndustry.name
      }
      if (configIndustry) {
        vo.isChecked = true
        vo.type = configIndustry.value as RuleTypeEnum
      }
      if (item.id === IndustryIdEnum.RS) {
        this.industryRule.unshift(vo)
      } else {
        this.industryRule.push(vo)
      }
    })
  }
  /**
   * 保存
   */
  async save() {
    const request = new SetTrainingChannelSettingRequest()
    request.id = this.id
    request.guideExpression = this.guide
    request.isOpenTrainingChannel = this.isOpen
    request.isShowEntry = this.isShow
    request.name = this.name
    request.isShowTrainingChannelScheme = this.showSpecialScheme
    if (this.industryRule?.length) {
      request.trainingChannelSettingExtReq = []
      this.industryRule.forEach(item => {
        if (item.isChecked) {
          const dto = new TrainingChannelSettingExtReq()
          dto.objectId = item.industryId
          dto.type = 1
          dto.key = '1'
          dto.value = item.type
          dto.trainingChannelSettingId = this.id
          request.trainingChannelSettingExtReq.push(dto)
        }
      })
    }

    return Training.setTrainingChannelSetting(request)
  }
}
