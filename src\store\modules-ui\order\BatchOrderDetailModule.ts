import { getModule, Module, Action, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { BatchOrderTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
import BatchOrderDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailVo'
/**
 * @description
 */
@Module({ namespaced: true, store, dynamic: true, name: 'BatchOrderDetailModule' })
class BatchOrderDetailModule extends VuexModule {
  /**
   * 批次单状态 1:待下单、2:下单中、3:待付款、4:支付中、5:开通中、6:交易成功、7:交易关闭中、8:交易关闭
   */
  batchOrderStatus: BatchOrderTradeStatusEnum = null

  /**
   * 批次单详情
   */
  batchOrderDetail: BatchOrderDetailVo = new BatchOrderDetailVo()

  /**
   * 设置批次单详情
   */
  @Action({ rawError: true })
  setBatchOrderDetail(detail: BatchOrderDetailVo) {
    this.SET_BATCH_ORDER_DETAIL(detail)
    if (detail.batchInfo?.orderStatus) {
      const status = detail.batchInfo.orderStatus
      this.SET_BATCH_ORDER_STATUS(status)
    }
  }

  /**
   * 清空批次单详情
   */
  @Action({ rawError: true })
  clearBatchOrderDetail() {
    this.SET_BATCH_ORDER_DETAIL(null as BatchOrderDetailVo)
  }

  /**
   * 设置批次单详情
   */
  @Mutation
  SET_BATCH_ORDER_DETAIL(detail: BatchOrderDetailVo) {
    this.batchOrderDetail = detail
  }

  /**
   * 设置批次单状态
   */
  @Mutation
  SET_BATCH_ORDER_STATUS(status: BatchOrderTradeStatusEnum) {
    this.batchOrderStatus = status
  }
}

export default getModule(BatchOrderDetailModule)
