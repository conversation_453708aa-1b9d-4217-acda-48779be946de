import QueryIndustryV2 from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryIndustry'
import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
import QueryIdCardType from '@api/service/common/basic-data-dictionary/query/QueryIdCardType'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import QueryIndustryPropertyCategory from '@api/service/common/basic-data-dictionary/query/QueryIndustryPropertyCategory'
import QueryLeaderPositionLevel from '@api/service/common/basic-data-dictionary/query/QueryLeaderPositionLevel'
import Occupation from '@api/service/common/basic-data-dictionary/query/QueryOccupation'
import QueryOnlineSchoolStatus from '@api/service/common/basic-data-dictionary/query/QueryOnlineSchoolStatus'
import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
import QueryRegisterRegion from '@api/service/common/basic-data-dictionary/query/QueryRegisterRegion'
import QueryShortmessage from '@api/service/common/basic-data-dictionary/query/QueryShortmessage'
import QuerySubjectType from '@api/service/common/basic-data-dictionary/query/QuerySubjectType'
import QueryTechnologyLevel from '@api/service/common/basic-data-dictionary/query/QueryTechnologyLevel'
import QueryTrainingCategory from '@api/service/common/basic-data-dictionary/query/QueryTrainingCategory'
import QueryTrainingMajor from '@api/service/common/basic-data-dictionary/query/QueryTrainingMajor'
import QueryYear from '@api/service/common/basic-data-dictionary/query/QueryYear'

class QueryBasicdataDictionaryFactory {
  /**
   * 单例 查询业务地区
   */
  get queryBusinessRegion() {
    return QueryBusinessRegion
  }

  /**
   * 单例 查询年度
   */
  get queryYear() {
    return QueryYear
  }
  /**
   * 单例 查询职业等级
   */
  get queryTechnologyLevel() {
    return QueryTechnologyLevel
  }
  /**
   * 单例 查询行业
   */
  get queryIndustry() {
    return QueryIndustry
  }
  /**
   * 单例 查询行业(有行业属性id)
   */
  get queryIndustryV2() {
    return QueryIndustryV2
  }

  /**
   * 单例 查询证书技术工种
   */
  get queryOccupation() {
    return Occupation
  }

  /**
   * 单例 查询物理地区
   */
  get queryPhysicalRegion() {
    return QueryPhysicalRegion
  }

  /**
   * 单例 查询学员注册地区（即工作单位）
   */
  get queryRegisterRegion() {
    return QueryRegisterRegion
  }

  /**
   * 单例 查询行业属性分类
   */
  get queryIndustryPropertyCategory() {
    return QueryIndustryPropertyCategory
  }

  /**
   * 单例 查询科目类别
   */
  get querySubjectType() {
    return QuerySubjectType
  }

  /**
   * 单例 查询培训类别
   */
  get queryTrainingCategory() {
    return QueryTrainingCategory
  }

  /**
   * 单例 查询培训专业
   */
  get queryTrainingMajor() {
    return QueryTrainingMajor
  }

  /**
   * 单例 查询职称等级
   */
  get queryLeaderPositionLevel() {
    return QueryLeaderPositionLevel
  }

  /**
   * 单例 查询网校状态
   */
  get queryOnlineschoolStatus() {
    return QueryOnlineSchoolStatus
  }

  /**
   * 单例 查询是否启用短信配置
   */
  get queryShortmessage() {
    return QueryShortmessage
  }

  /**
   * 单例 查询证件类型
   */
  get queryIdCardType() {
    return QueryIdCardType
  }
}

export default QueryBasicdataDictionaryFactory
