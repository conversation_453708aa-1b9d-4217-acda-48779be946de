import { Page } from '@hbfe/common'
import PlatformTrainingChannelBackGateway from '@api/platform-gateway/platform-training-channel-back-gateway'
import ThematicMangementCourseItem from '@api/service/management/thematic-management/model/ThematicMangementCourseItem'
import PlatformCourseBackGateway, { CourseRequest } from '@api/platform-gateway/platform-course-back-gateway'
export default class QueryTopicCourses {
  constructor(trainingChannelId?: string) {
    this.trainingChannelId = trainingChannelId ?? ''
  }
  /**
   * 分类id
   默认-1
   */
  categoryId = '-1'
  /**
   * 专题id
   */
  trainingChannelId = ''
  /**
   * 查询网校课程列表
   */
  async queryCourseList(page: Page, params: { name?: string; categoryIdList?: Array<string> }) {
    const request = new CourseRequest()
    request.name = params.name
    request.categoryIdList = params.categoryIdList
    request.trainingChannelId = this.trainingChannelId
    const res = await PlatformCourseBackGateway.pageCourseInTrainingChannelInServicer({
      page,
      request
    })
    page.totalSize = res.data?.totalSize
    page.totalPageSize = res.data?.totalPageSize
    return (
      res.data?.currentPageData?.map(item => {
        const vo = new ThematicMangementCourseItem()
        vo.courseId = item.id
        vo.name = item.name
        vo.period = item.period
        vo.selected = item.isReferencedByTrainingChannel
        return vo
      }) || ([] as ThematicMangementCourseItem[])
    )
  }

  /**
   * 分页查询课程列表
   */
  async queryTopicCourseList(page: Page) {
    const res = await PlatformTrainingChannelBackGateway.pageTrainingChannelSelectCourseInSubject({
      page: page,
      request: {
        trainingChannelId: this.trainingChannelId,
        selectedCourseCategoryId: this.categoryId
      }
    })
    page.totalSize = res.data?.totalSize
    page.totalPageSize = res.data?.totalPageSize
    return res.data?.currentPageData?.map(ThematicMangementCourseItem.from) || ([] as ThematicMangementCourseItem[])
  }
}
