/**
 * 查询栏目
 */
import Column from '@api/service/management/online-school-config/column/query/vo/Column'
import MsBasicdataQueryFrontGatewayBasicDataQueryBackstage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

/**
 * 查询栏目信息
 */
class QueryColumn {
  /**
   * 查询列表数据
   */
  async queryList(): Promise<Array<Column>> {
    const { data, status } = await MsBasicdataQueryFrontGatewayBasicDataQueryBackstage.getMenuSettingByPortalType(1)
    if (status.isSuccess()) {
      const list: Array<Column> = new Array<Column>()
      data.menuSettingInfos.forEach(menuSettingInfos => {
        if (menuSettingInfos.parentId === '-1' || menuSettingInfos.parentId === null) {
          const column = Column.from(menuSettingInfos)
          column.children = data.menuSettingInfos.filter(menu => menu.parentId === column.id).map(Column.from)
          list.push(column)
        }
      })
      list.sort((a, b) => {
        return a.sort - b.sort
      })
      return list
    }
  }
}

export default QueryColumn
