import MsAccountGateway, { UserUpdateRequest } from '@api/ms-gateway/ms-account-v1'
import { ResponseStatus } from '@hbfe/common'
import MsBasicdataDomain, {
  UpdateCollectiveRegisterAccountInfoRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'

/**
 * 更新集体用户信息
 */
class MutationUpdateUserInfo {
  userUpdateRequest = new UserUpdateRequest()

  /**
   * 更新集体用户信息
   */
  async doUpdate(): Promise<ResponseStatus> {
    const { status } = await MsAccountGateway.updateUser(this.userUpdateRequest)
    return status
  }

  /**
   * 修改集体用户账号
   * @param param
   */
  async doUpdateAccount(param: UpdateCollectiveRegisterAccountInfoRequest) {
    const res = await MsBasicdataDomain.updateCollectiveRegisterAccountInfo(param)

    return res
  }
}
export default MutationUpdateUserInfo
