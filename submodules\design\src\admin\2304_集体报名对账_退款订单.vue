<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="报名订单对账" name="first">
        <div class="f-p15">详见 2303_集体报名对账_报名订单.vue</div>
      </el-tab-pane>
      <el-tab-pane label="退款订单对账" name="second">
        <div class="f-p15">
          <el-card shadow="never" class="m-card f-mb15">
            <!--条件查询-->
            <el-row :gutter="16" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训方案">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="期别名称">
                    <el-input v-model="input" clearable placeholder="请输入期别名称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="收款账号">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="批次号">
                    <el-input v-model="input" clearable placeholder="请输入批次号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="交易流水号">
                    <el-input v-model="input" clearable placeholder="请输入交易流水号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="退款单号">
                    <el-input v-model="input" clearable placeholder="请输入退款单号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="退款成功时间">
                    <el-date-picker
                      v-model="form.date1"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="起始时间"
                      end-placeholder="结束时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="专题">
                    <el-select v-model="select" clearable filterable placeholder="请选择订单是否来源专题">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="专题名称">
                    <el-input v-model="input" clearable placeholder="请输入专题进行查询" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="分销商">
                    <el-input v-model="input" clearable placeholder="请输入分销商名称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="推广门户简称">
                    <el-input v-model="input" clearable placeholder="请输入分销商推广门户简称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="退货/款类型">
                    <el-select v-model="select" clearable filterable placeholder="请选择退款类型">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>导出列表数据</el-button>
                    <el-button>重置</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <div class="f-mt20">
              <el-alert type="warning" :closable="false" class="m-alert">
                <div class="f-c6">
                  当前共有 <span class="f-fb f-co">5</span> 笔退款订单，退款总额 <span class="f-fb f-co">¥ 999</span>。
                </div>
              </el-alert>
            </div>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="集体报名批次号" min-width="220" fixed="left">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    2112071509467489926
                    <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-document-copy f-link-gray f-ml5 f-c9"></i>
                      <div slot="content">复制集体报名批次号</div>
                    </el-tooltip>
                    <p>
                      <el-tag type="primary" size="small">分销推广</el-tag>
                    </p>
                  </div>
                  <div v-else>
                    2112071509467489926
                    <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-document-copy f-link-gray f-ml5 f-c9"></i>
                      <div slot="content">复制集体报名批次号</div>
                    </el-tooltip>
                    <p>
                      <el-tag type="success" size="small">专题</el-tag>
                      <el-tag type="primary" size="small">分销推广</el-tag>
                    </p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="交易流水号" min-width="300">
                <template>WXP20210820175740748958763892081</template>
              </el-table-column>
              <el-table-column label="退款单号" min-width="300">
                <template><a href="#" class="f-link f-cb f-underline">WXP20210820175740748958763892081</a></template>
              </el-table-column>
              <el-table-column label="退款成功时间" min-width="180" sortable>
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
              <el-table-column label="购买人信息" min-width="240">
                <template>
                  <p>姓名：张依依</p>
                  <p>证件号：354875965412365896</p>
                  <p>手机号：15847412365</p>
                </template>
              </el-table-column>
              <el-table-column label="退货/款类型" min-width="180">
                <template>退货且退款</template>
              </el-table-column>
              <el-table-column label="退款金额(元)" width="140" align="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">3.15</div>
                  <div v-else-if="scope.$index === 1">52.36</div>
                  <div v-else>158.15</div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'second',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
