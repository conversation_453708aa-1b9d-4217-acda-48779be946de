<route-meta>
  {
  "isMenu": true,
  "title": "个人报名订单",
  "sort": 1,
  "icon": "icon_guanli"
  }
</route-meta>
<script lang="ts">
  import PersonalIndex from '@hbfe/jxjy-admin-trade/src/diff/jxgx/order/personal/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY, FXS, GYS],
    queryFx: [NZFXS, NZFXSJCB],
    queryZt: [ZTGLY],
    search: [WXGLY, FXS, GYS],
    searchFx: [NZFXS, NZFXSJCB],
    searchZt: [ZTGLY],
    export: [WXGLY, FXS, GYS],
    exportFx: [NZFXS, NZFXSJCB],
    exportZt: [ZTGLY],
    detail: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    closeOrder: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB],
    editInvoicePopup: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB]
  })
  export default class extends PersonalIndex {}
</script>
