import CreateTakePlaceVo from '@api/service/management/online-school-config/distribution-channels-config/mutation/vo/CreateTakePlaceVo'

class CreateTakePlaceDto {
  /**
   * 保存
   * @param
   * @return
   */
  async save(): Promise<boolean> {
    return false
  }

  /**
   * 转换器
   * @param
   * @return
   */
  static from(createTakePlaceVo: CreateTakePlaceVo) {
    return new CreateTakePlaceDto()
  }
}

export default CreateTakePlaceDto
