import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'
import CourseCategory from '@api/service/customer/course/query/vo/CourseCategory'
import { CourseDetailResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

/**
 * 课程详情类
 */
class CourseDetail {
  id = ''
  // 课程名称
  name = ''
  // 封面
  coverImage = ''
  // 课程学时
  period = 0
  // 是否必学
  isMustLearn = false
  // 课程教师信息
  teachers: Array<SimpleUserInfo> = new Array<SimpleUserInfo>()
  // 课程教师信息
  teacherNames?: Array<string> = new Array<string>()
  // 课程所属分类
  categories: Array<CourseCategory> = new Array<CourseCategory>()
  // 大纲路径
  categoryPath = ''
  // 是否 允许试听
  canListen = false
  description = ''

  /**
   * 获取教师信息
   */
  getTeacherNames() {
    return this.teacherNames
      .map((teacherName: string) => {
        return teacherName
      })
      .join('、')
  }

  from(remote: CourseDetailResponse) {
    this.id = remote.id
    this.name = remote.name
    this.coverImage = remote.iconPath
    this.period = remote.period
    this.canListen = remote.auditionStatus === 1
    this.teachers = new Array<SimpleUserInfo>()
    this.description = remote.aboutsContent
    if (remote?.teacherIds?.length) {
      remote.teacherIds.forEach(id => {
        const teacher = new SimpleUserInfo()
        teacher.id = id
        this.teachers.push(teacher)
      })
    }
    this.categories = remote.categorys.map(CourseCategory.from)
  }
}

export default CourseDetail
