<!--
 * @Author: z张仁榕
 * @Date: 2025-01-08 10:40:28
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-02-25 17:01:50
 * @Description:
-->
<route-meta>
  {
  "isMenu": true,
  "title": "批量打印证明",
  "sort": 10,
  "icon": "icon-piliangdayin"
  }
</route-meta>

<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { NZFXS, NZFXSJCB, WXGLY, ZTGLY } from '@/models/RoleTypes'
  import BatchPrint from '@hbfe/jxjy-admin-batchPrint/src/index.vue'

  @RoleTypeDecorator({
    batchPrintCertify: [WXGLY],
    schemeQuery: [WXGLY],
    batchPrint: [WXGLY, NZFXS, NZFXSJCB, ZTGLY],
    importListPrinting: [WXGLY, ZTGLY],
    queryFx: [NZFXS, NZFXSJCB],
    batchPrintFx: [NZFXS, NZFXSJCB],
    importListPrintingFx: [NZFXS, NZFXSJCB],
    distributorQuery: [WXGLY, NZFXS, NZFXSJCB, ZTGLY],
    batchSchemePrint: [WXGLY, NZFXS, NZFXSJCB, ZTGLY],
    preview: [WXGLY, NZFXS, NZFXSJCB, ZTGLY],
    downLoad: [WXGLY, NZFXS, NZFXSJCB, ZTGLY],
    studentBatchPrint: [WXGLY, NZFXS, NZFXSJCB, ZTGLY],
    axPrintType: [WXGLY, NZFXS, NZFXSJCB, ZTGLY],
    schemeQueryFx: [NZFXS, NZFXSJCB],
    schemeQueryZt: [ZTGLY],
    queryZt: [ZTGLY],
    querySchemeZt: [ZTGLY],
    querySchemeWx: [WXGLY]
  })
  export default class extends BatchPrint {}
</script>
