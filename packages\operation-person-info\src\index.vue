<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2023-07-12 16:52:00
 * @LastEditors: <PERSON>
 * @LastEditTime: 2023-07-21 10:25:44
-->
<route-meta>
{
"isMenu": true,
"onlyShowOnTab": true,
"title": "个人帐号设置",
"sort": 2,
"hideMenu": true,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('personalInfo')" desc="个人账号设置" actions="created">
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">账号信息</span>
        </div>
        <div class="f-p20">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" label-width="140px" class="m-form f-pt20">
                <el-form-item label="姓名：" class="is-text">
                  {{ userName }}
                  <template v-if="$hasPermission('editName')" desc="修改姓名" actions="sureChangeName">
                    <a class="f-link f-cb f-underline" @click="changeName">修改姓名</a>
                  </template>
                </el-form-item>
                <el-form-item label="登录帐号：" class="is-text">
                  {{ account }}
                </el-form-item>
                <el-form-item v-if="queryManagerDetail.isRegionAdmin" label="管辖地区：" class="is-text">
                  {{ manageRegionName }}
                </el-form-item>
                <el-form-item label="手机号：" class="is-text">
                  <span class="f-mr15" v-if="hasPhoneNumber">{{ phone }}</span>
                  <a class="f-link f-cb f-underline" @click="phone ? changePhone() : editPhone()">换绑手机号</a>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">重置密码</span>
        </div>
        <div class="f-p20">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form
                ref="passwordRef"
                :model="passWordform"
                :rules="passwordRules"
                label-width="140px"
                class="m-form f-pt20"
              >
                <el-form-item label="旧密码：" prop="oldPassword">
                  <el-input
                    v-model="passWordform.oldPassword"
                    clearable
                    show-password
                    placeholder="请输入当前的登录密码"
                    class="form-m"
                  />
                </el-form-item>
                <el-form-item label="新密码：" prop="newPassword">
                  <el-input
                    v-model="passWordform.newPassword"
                    clearable
                    show-password
                    placeholder="请设置新密码"
                    class="form-m"
                    @input="checkoutPasswordStrength"
                  />
                </el-form-item>
                <!--密码安全判断-->
                <el-form-item>
                  <div class="psw-tips" style="width: 40%;" v-if="passwordStrengthLow">
                    <el-progress :percentage="33.33" color="#e93737" :show-text="false"></el-progress>
                    <!--弱：txt-l，中：txt-m，强：txt-h-->
                    <span class="txt txt-l">弱</span>
                  </div>
                  <div class="psw-tips" style="width: 40%;" v-if="passwordStrengthIntermediate">
                    <el-progress :percentage="66.66" color="#ee9e2d" :show-text="false"></el-progress>
                    <!--弱：txt-l，中：txt-m，强：txt-h-->
                    <span class="txt txt-m">中</span>
                  </div>
                  <div class="psw-tips" style="width: 40%;" v-if="passwordStrengthHigh">
                    <el-progress :percentage="100" color="#49b042" :show-text="false"></el-progress>
                    <!--弱：txt-l，中：txt-m，强：txt-h-->
                    <span class="txt txt-h">强</span>
                  </div>
                </el-form-item>
                <el-form-item label="确认密码：" prop="againPassword">
                  <el-input
                    v-model="passWordform.againPassword"
                    clearable
                    show-password
                    placeholder="请再次输入密码"
                    class="form-m"
                  />
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-button @click="toback">取消</el-button>
                  <template v-if="$hasPermission('changePassword')" desc="修改密码" actions="changePassword">
                    <el-button type="primary" @click="changePassword">保存</el-button>
                  </template>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <el-drawer
          title="换绑手机号"
          :visible.sync="dialogChangePhone"
          direction="rtl"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form
                  ref="changePhoneRef"
                  :model="changePhoneFrom"
                  :rules="checkPhoneRules"
                  label-width="auto"
                  class="m-form f-mt20"
                >
                  <div class="f-mb20">
                    您当前正在换绑手机号：<span class="f-fb f-cb">{{ phone }}</span>
                  </div>
                  <el-form-item label="手机号：" prop="phone">
                    <el-input v-model="changePhoneFrom.phone" clearable placeholder="请输入11位手机号" />
                  </el-form-item>
                  <el-form-item label="图形验证码：" prop="captcha">
                    <div class="f-flex">
                      <el-input
                        v-model="changePhoneFrom.captcha"
                        clearable
                        placeholder="请输入图形验证码"
                        class="f-flex-sub"
                      />
                      <div class="code">
                        <img :src="validateCodePic" @click="refreshValidateCodePic" title="看不清，点击刷新" />
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="短信校验码：" prop="smsCode">
                    <div class="f-flex">
                      <el-input
                        v-model="changePhoneFrom.smsCode"
                        clearable
                        placeholder="请输入短信校验码"
                        class="f-flex-sub"
                      />
                      <div
                        class="code"
                        v-if="$hasPermission('getChangePhoneCapture')"
                        desc="获取短信验证码"
                        actions="getPhoneCaptcha"
                      >
                        <el-button
                          type="primary"
                          plain
                          v-if="sending"
                          :disabled="isCaptchaValid"
                          @click="getPhoneCaptcha"
                          >获取短信验证码</el-button
                        >
                        <el-button type="info" plain disabled v-if="!sending">重新获取（{{ countTime }}s）</el-button>
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button @click="cancel">取消</el-button>
                    <template v-if="$hasPermission('submitChange')" desc="确认提交手机换绑" actions="submit">
                      <el-button type="primary" @click="submit">确认提交</el-button>
                    </template>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <el-drawer
          title="修改手机号"
          :visible.sync="dialogEditPhone"
          direction="rtl"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form
                  ref="changePhoneRef"
                  :model="changePhoneFrom"
                  :rules="checkPhoneRules"
                  label-width="auto"
                  class="m-form f-mt20"
                >
                  <el-form-item label="手机号：" prop="phone">
                    <el-input v-model="changePhoneFrom.phone" clearable placeholder="请输入11位手机号" />
                  </el-form-item>
                  <el-form-item label="图形验证码：" prop="captcha">
                    <div class="f-flex">
                      <el-input
                        v-model="changePhoneFrom.captcha"
                        clearable
                        placeholder="请输入图形验证码"
                        class="f-flex-sub"
                      />
                      <div class="code">
                        <img :src="validateCodePic" @click="refreshValidateCodePic" title="看不清，点击刷新" />
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="短信校验码：" prop="smsCode">
                    <div class="f-flex">
                      <el-input
                        v-model="changePhoneFrom.smsCode"
                        clearable
                        placeholder="请输入短信校验码"
                        class="f-flex-sub"
                      />
                      <div
                        class="code"
                        v-if="$hasPermission('getEditPhoneCapture')"
                        desc="获取短信验证码"
                        actions="getPhoneCaptcha"
                      >
                        <el-button
                          type="primary"
                          plain
                          v-if="sending"
                          :disabled="isCaptchaValid"
                          @click="getPhoneCaptcha"
                          >获取短信验证码</el-button
                        >
                        <el-button type="info" plain disabled v-if="!sending">重新获取（{{ countTime }}s）</el-button>
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button @click="cancel">取消</el-button>
                    <template v-if="$hasPermission('submitEdit')" desc="确认提交手机修改" actions="submit">
                      <el-button type="primary" @click="submit">确认提交</el-button>
                    </template>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>

        <el-drawer title="修改姓名" :visible.sync="dialogName" direction="rtl" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form
                  ref="changeNameRef"
                  :model="changeNameFrom"
                  :rules="checkPhoneRules"
                  label-width="auto"
                  class="m-form f-mt20"
                >
                  <el-form-item label="姓名：">
                    <el-input v-model="changeNameFrom.name" clearable placeholder="请输入修改姓名" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button @click="nameCancel">取消</el-button>
                    <el-button @click="sureChangeName" type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
      </el-card>
      <!--  -->
      <!--  -->
      <el-dialog title="提示" :visible.sync="dialogVisible3" width="400px" class="m-dialog">
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb f-f16">帐号绑定手机号成功</p>
            <p class="f-f13 f-mt5">可以使用手机号直接登录~</p>
          </div>
        </div>
        <div slot="footer">
          <el-button type="primary" @click="changePhoneClose">确定{{ sureTime }}s</el-button>
        </div>
      </el-dialog>
      <el-dialog title="提示" :visible.sync="dialogVisible4" width="400px" class="m-dialog" :before-close="closeDialog">
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb f-f16">姓名修改成功</p>
          </div>
        </div>
        <div slot="footer">
          <el-button type="primary" @click="changeNameClose">确定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="提示" :visible.sync="dialogVisible5" width="400px" class="m-dialog">
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb f-f16">姓名修改失败</p>
          </div>
        </div>
        <div slot="footer">
          <el-button type="primary" @click="changeNameCloseV2">确定</el-button>
        </div>
      </el-dialog>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import UserModule from '@api/service/management/user/UserModule'
  //   import { QueryCurrentUserRoleList } from '@api/service/management/authority/role/query/QueryCurrentUserRoleList'
  import MutationBizAccount from '@api/service/common/account/MutationBizAccount'
  import QueryCurrentUserRoleList from '@api/service/management/authority/role/query/QueryCurrentUserRoleList'
  import MutationResetPwdAdmin from '@api/service/management/user/mutation/MutationResetPwdAdmin'
  import Authentication from '@api/service/common/authentication/Authentication'
  import MutationResetNameAdmin from '@api/service/management/user/mutation/MutationResetNameAdmin'
  import { CaptchaApplyRequest, SmsCodeApplyRequest } from '@hbfe-ms/ms-basicdata-domain-gateway'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import { BusinessTypeEnum } from '@hbfe-biz/biz-authentication/dist/enums/BusinessTypeEnum'

  @Component
  export default class extends Vue {
    $authentication: Authentication
    @Ref('changePhoneRef')
    changePhoneRef: any
    @Ref('passwordRef')
    passwordRef: any
    @Ref('changeNameRef')
    changeNameRef: any
    AccountModule = new MutationBizAccount()
    hasPhoneNumber = true
    dialogVisible3 = false
    dialogVisible4 = false
    dialogVisible5 = false
    sureTime = 5
    phoneNumbe = ''
    // 验证码发送
    sending = true
    validateCodePic = ''
    isCaptchaValid = true
    // 预计倒计时时间
    count = 60
    // 发送后倒计时计时
    countTime = 60
    userTypeInfo = new QueryCurrentUserRoleList()
    resetName = new MutationResetNameAdmin()
    userType: string
    userTypeNumber: number
    roleList: any
    /**
     * 密码强度低
     */
    passwordStrengthLow = false
    /**
     * 密码强度中
     */
    passwordStrengthIntermediate = false
    /**
     * 密码强度高
     */
    passwordStrengthHigh = false
    async getUserTpyeInfo() {
      await this.userTypeInfo.getCurrentUserRoleList()
      const res = this.userTypeInfo.roleList
      this.userTypeNumber = res[0].category
    }
    passwordRules = {
      againPassword: {
        required: true,
        max: 18,
        min: 8,
        validator: this.validateRepeatPassword,
        trigger: 'blur'
      },
      newPassword: [
        {
          required: true,
          message: '请输入8~18位由数字、字母或符号组成的密码',
          trigger: 'blur'
        },
        {
          message: '请输入8~18位由数字、字母或符号组成的密码',
          max: 18,
          min: 8,
          validator: this.validatePassword,
          trigger: 'blur'
        }
      ],
      oldPassword: {
        required: true,
        message: '请输入8~18位由数字、字母或符号组成的密码',
        max: 18,
        min: 8,
        trigger: 'blur'
      }
    }
    checkPhoneRules = {
      captcha: [
        {
          // 以大写小写字母或者数字组合的4位
          regexp: /^[a-zA-z0-9]{4}$/,
          validator: async (rule: any, value: any, callback: any) => {
            if (value && rule.regexp.test(value)) {
              try {
                const res = await this.$authentication.verify.msValidateCaptcha(value)
                if (res.data.code == 200) {
                  this.change()
                  callback()
                } else {
                  await this.refreshValidateCodePic()
                  this.isCaptchaValid = true
                  callback(new Error('验证失败'))
                }
              } catch {
                await this.refreshValidateCodePic()
                this.isCaptchaValid = true
                callback(new Error('验证失败'))
              }
            } else {
              this.isCaptchaValid = true
              callback(new Error('验证码为4位数'))
            }
          },
          required: true,
          trigger: 'change'
        },
        { required: true, message: '请输入图形验证码', trigger: 'blur' }
      ],
      smsCode: {
        required: true,
        message: '请输入验证码',
        trigger: 'blur'
      },
      phone: {
        required: true,
        message: '请输入正确的手机号',
        validator: this.validatePhone,
        trigger: 'blur'
      }
    }
    dialogChangePhone = false
    dialogEditPhone = false
    dialogName = false
    resetPwdAdmin = new MutationResetPwdAdmin()
    queryManagerDetail = UserModule.queryUserFactory.queryManagerDetail
    changePhoneFrom = {
      phone: '',
      smsCode: '',
      captcha: ''
    }
    changeNameFrom = {
      name: this.userName
    }
    passWordform = {
      oldPassword: '',
      newPassword: '',
      againPassword: ''
    }
    toback() {
      this.$router.push('/home/<USER>')
    }
    cancel() {
      this.dialogChangePhone = false
      this.dialogEditPhone = false
      this.changePhoneFrom = {
        phone: '',
        smsCode: '',
        captcha: ''
      }
      this.refreshValidateCodePic()
    }
    nameCancel() {
      this.dialogName = false
      this.changeNameFrom = {
        name: this.userName
      }
    }
    /**
     * 密码验证,必须数字和字母，特殊符号可有可无
     */
    validatePassword(rule: any, value: any, callback: any) {
      const reg = new RegExp('^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z\\W]{8,18}$')
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        reg.test(value) ? callback() : callback(new Error('请输入8~18位由数字、字母或符号组成的密码'))
      }
    }

    /**
     * / 确定密码验证
     */
    validateRepeatPassword(rule: any, value: any, callback: any) {
      const reg = new RegExp('^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z\\W]{8,18}$')
      if (value === '') {
        callback(new Error('请重新输入密码'))
      } else if (value !== this.passWordform.newPassword) {
        callback(new Error('两次输入密码不一致'))
      } else {
        reg.test(value) ? callback() : callback(new Error('请再次输入密码'))
      }
    }
    /**
     * 验证手机号格式
     */
    validatePhone(rule: any, value: any, callback: any) {
      const reg = new RegExp(/^[1]([3-9])[0-9]{9}$/)
      if (value === '') {
        callback(new Error('请输入手机号'))
      } else {
        if (reg.test(value)) {
          this.change()
          callback()
        } else {
          callback(new Error('请输入正确的手机号'))
        }
      }
    }
    async sureChangeName() {
      const res = await this.resetName.doResetAdminInfo(this.changeNameFrom.name)
      if (res.isSuccess()) {
        this.dialogVisible4 = true
      } else {
        this.dialogVisible5 = true
      }

      //   debugger
    }
    change() {
      this.isCaptchaValid = false
    }
    /**
     * 获取图片验证码
     */
    async refreshValidateCodePic() {
      const params = new CaptchaApplyRequest()
      if (this.userTypeNumber == 510) {
        params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.superChangePhoneNumToken)
      } else if (this.userTypeNumber == 1) {
        params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.studentChangePhoneNumToken)
      } else if (this.userTypeNumber == 560) {
        // params.token = ConfigCenterModule.getFrontendApplication(
        //   frontendApplication.coursewareSupplierChangePhoneNumToken
        // )
      } else if (this.userTypeNumber == 320) {
        params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionChangePhoneNumToken)
      } else if (this.userTypeNumber == 13) {
        params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.sunProjectChangePhoneNumToken)
      }
      params.businessType = BusinessTypeEnum.change_binding_phone
      const res = await this.$authentication.verify.applyCaptcha(params)
      if (res.status.code == 200) {
        this.validateCodePic = `data:image/jpeg;base64,${res.data.captcha}`
      }
      //   const res = await this.AccountModule.loadBasicValidationData()
      //   if (res.code == 200) {
      //     this.validateCodePic = `data:image/jpeg;base64,${this.AccountModule.captcha}`
      //   }
    }
    // 验证码倒计时
    sureTimeDown(time: number) {
      let timer = time
      const sureTimeDown = setInterval(() => {
        if (timer === 0) {
          clearInterval(sureTimeDown)
          this.dialogVisible3 = false
          this.dialogChangePhone = false
          this.sureTime = 5
          this.$router.go(0)
        } else {
          timer--
          this.sureTime = timer
        }
      }, 1000)
    }
    changePhoneClose() {
      this.dialogVisible3 = false
      this.dialogChangePhone = false
      this.$router.go(0)
    }
    changeNameClose() {
      this.dialogVisible4 = false
      this.dialogName = false
      this.$router.go(0)
    }
    closeDialog() {
      this.dialogVisible4 = false
      this.$router.go(0)
    }
    changeNameCloseV2() {
      this.dialogVisible5 = false
      this.dialogName = false
    }
    async submit() {
      try {
        await this.changePhoneRef.validateField(['phone', 'smsCode'])
        await this.$authentication.verify.msValidSmsCode(this.changePhoneFrom.smsCode, this.changePhoneFrom.phone)
        // const smsCodeValidRequest = new SmsCodeValidRequest()
        // smsCodeValidRequest.phone = this.changePhoneFrom.phone
        // smsCodeValidRequest.captcha = this.changePhoneFrom.captcha
        // smsCodeValidRequest.smsCode = this.changePhoneFrom.smsCode
        // smsCodeValidRequest.token = this.AccountModule.basicToken
        // const res = (await this.AccountModule.checkSmsCode(smsCodeValidRequest)) as any
        // if (res.code == 200) {
        const token = this.$authentication.verify.shortMessageCaptchaToken
        const result =
          this.phone == ''
            ? await this.$authentication.account.bindPhone(token)
            : await this.$authentication.account.bindNewPhone(token)

        if (result.status.code == 200) {
          this.dialogVisible3 = true
          this.sureTimeDown(this.sureTime)
        } else {
          this.$message.warning(result.status.errors[0].message)
        }
        // }
      } catch (e) {
        const res: any = e
        this.$message.warning(res.status?.errors?.[0]?.message || res.status?.message)
      }
    }
    //换绑手机号
    changePhone() {
      this.dialogChangePhone = true
    }
    //修改手机号
    editPhone() {
      this.dialogEditPhone = true
    }
    changeName() {
      this.dialogName = true
    }
    get account() {
      return this.queryManagerDetail.adminInfo.authenticationList.find(item => item.identityType === 1)?.identity || ''
    }
    get phone() {
      // if (this.queryManagerDetail.adminInfo.userInfo.phone) {
      //   this.changePhoneFrom.phone = this.queryManagerDetail.adminInfo.userInfo.phone
      // }
      return this.queryManagerDetail.adminInfo.userInfo.phone
    }
    get userName() {
      return this.queryManagerDetail.adminInfo.userInfo.userName
    }
    get accountId() {
      return this.queryManagerDetail.adminInfo.accountInfo.accountId
    }
    get manageRegionName() {
      const regionInfo = this.queryManagerDetail.adminInfo.userInfo.manageRegionList[0]
      return (
        regionInfo.provinceName +
        (regionInfo.cityName ? '-' + regionInfo.cityName : '') +
        (regionInfo.countyName ? '-' + regionInfo.countyName : '')
      )
    }
    async created() {
      await this.getUserTpyeInfo()
      await this.refreshValidateCodePic()
    }
    // 获取短信验证码
    async getPhoneCaptcha() {
      this.sending = false
      const params = new SmsCodeApplyRequest()
      params.phone = this.changePhoneFrom.phone
      params.businessType = BusinessTypeEnum.change_binding_phone
      params.token = this.$authentication.verify.captchaToken
      const res = await this.$authentication.verify.msSendSmsCode(params)
      //   await this.AccountModule.sendSmsCodeByUpdatePhone(
      //     this.changePhoneFrom.phone,
      //     this.accountId,
      //     this.changePhoneFrom.captcha
      //   )
      if (res.data.code != 200) {
        this.sending = true
        await this.refreshValidateCodePic()
      }
      if (res.status.code == 200 && res.status.isSuccess()) {
        this.countDown(this.count)
        this.$message.success('短信验证码已发送至手机，请查收！')
      }
      if (res.data.code === 409) {
        this.$message.error('一个手机号一天只能接收4条短信，今日次数已用完，请隔天再试。')
      } else if (res.data.code === 410) {
        this.$message.error('发送验证码失败')
      } else if (res.data.code === 701) {
        this.$message.error('请重新验证图形验证码后，再次获取短信验证码')
      } else if (res.data.code === 515) {
        this.$message.error('不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)')
      } else if (res.data.code === 400) {
        this.$message.error('token无效')
      } else if (res.data.code === 509) {
        this.$message.error('未绑定手机号')
      } else if (res.data.code === 514) {
        this.$message.error('token中未携带手机号')
      }
    }
    async changePassword() {
      try {
        await this.passwordRef.validate()
        const res = await this.resetPwdAdmin.doResetAdminPwd(
          this.passWordform.oldPassword,
          this.passWordform.againPassword
        )
        if (res.code == 200) {
          this.$message.success('修改成功')
          this.passWordform.oldPassword = ''
          this.passWordform.newPassword = ''
          this.passWordform.againPassword = ''
          this.$router.push('/school-management/management')
        } else {
          this.$message.error('密码不正确，请重新输入')
        }
      } catch (e) {
        console.log(e)
      }
    }
    // 验证码倒计时
    countDown(time: number) {
      let timer = time
      const timers = setInterval(() => {
        if (timer === 0) {
          clearInterval(timers)
          this.sending = true
          this.countTime = this.count
        } else {
          timer--
          this.countTime = timer
        }
      }, 1000)
    }
    /**
     * 密码强度校验
     */
    checkoutPasswordStrength() {
      const reg1 = /^.{1,8}$|^\d{9,}$|^[a-zA-Z]{9,}$|^(?=[\x21-\x7e]+)[^A-Za-z0-9]{9,}$/ //密码低强度正则--纯数字或字母或字符或长度1-6
      const reg2 = /^(?!\d+$)[a-zA-Z0-9]{9,}$|^(?![0-9]+$)[^a-zA-Z]{9,}$|^(?![a-zA-Z]+$)[^0-9]{9,}$/ //密码中强度正则--有两种且长度在7-10
      const reg3 = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[^0-9a-zA-Z]).{13,}$/ //密码高强度正则--三种都有且长度大于10
      const reg4 = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[^0-9a-zA-Z]).{9,12}$/ //密码中强度正则--介于9-10位的三种字符都有的密码
      if (!this.passWordform.newPassword) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = false
      } else if (reg1.test(this.passWordform.newPassword)) {
        this.passwordStrengthLow = true
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = false
      } else if (reg2.test(this.passWordform.newPassword)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = true
        this.passwordStrengthHigh = false
      } else if (reg3.test(this.passWordform.newPassword)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = true
      } else if (reg4.test(this.passWordform.newPassword)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = true
        this.passwordStrengthHigh = false
      }
    }
  }
</script>
