<template>
  <el-main>
    <div class="f-p15">
      <div class="f-mb15">
        <el-button type="primary" icon="el-icon-plus">新建专题资讯</el-button>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="资讯标题">
                <el-input v-model="input" clearable placeholder="请输入资讯标题" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="资讯分类">
                <el-select v-model="select" clearable filterable placeholder="请选择资讯分类">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                  <el-option disabled>选项<el-tag type="info" size="mini" class="f-ml5">停用</el-tag></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="是否弹窗">
                <el-select v-model="select" clearable placeholder="请选择是否弹窗">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="资讯状态">
                <el-select v-model="select" clearable placeholder="请选择资讯分类">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="所属专题">
                <el-input v-model="input" clearable placeholder="请输入所属专题名称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="4" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column width="115" align="center">
            <template slot-scope="scope">
              <div v-if="scope.$index === 1">
                <el-tag type="primary" size="small">弹窗公告</el-tag>
              </div>
              <div v-else></div>
            </template>
          </el-table-column>
          <el-table-column label="资讯标题" min-width="400">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <p>资讯标题资讯标题资讯标题资讯标题资讯标题资讯标题资讯标题资讯标题</p>
                <p class="f-c9 f-f13 f-mt5">
                  <el-tag type="danger" size="small" class="f-mr10">置顶</el-tag>
                  <span class="f-mr20">发布时间：2021-11-11 12:12:12</span>发布人：子项目管理员
                </p>
              </div>
              <div v-else>
                <p>资讯标题资讯标题资讯标题资讯标题资讯标题资讯标题资讯标题</p>
                <p class="f-c9 f-f13 f-mt5">
                  <span class="f-mr20">发布时间：2021-11-11 12:12:12</span>发布人：子项目管理员
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="所属专题" min-width="220">
            <template>专题名称专题名称专题名称专题名称专题名称</template>
          </el-table-column>
          <el-table-column label="资讯分类" min-width="120">
            <template>资讯分类</template>
          </el-table-column>
          <el-table-column label="状态" min-width="120">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-badge is-dot type="warning" class="badge-status">草稿</el-badge>
              </div>
              <div v-else>
                <el-badge is-dot type="success" class="badge-status">发布</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" align="center" fixed="right">
            <template>
              <el-button type="text" size="mini">复制</el-button>
              <el-button type="text" size="mini">修改</el-button>
              <el-button type="text" size="mini">删除</el-button>
              <el-button type="text" size="mini">置为草稿</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-pagination
          background
          class="f-mt15 f-tr"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage4"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
        >
        </el-pagination>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
