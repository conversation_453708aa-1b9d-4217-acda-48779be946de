<template>
  <el-drawer title="重启任务" :visible.sync="visible" size="600px" custom-class="m-drawer">
    <div class="drawer-bd" v-loading="loading">
      <el-form ref="formRef" :model="resetItem" label-width="auto" class="m-form f-mt20" :rules="rules">
        <el-form-item label="期望开始学习时间：" prop="timeType">
          <el-radio-group v-model="resetItem.timeType">
            <el-radio :label="RestartTaskTypeEnum.last" class="f-mt10" style="display: block"
              >按上次期望开始学习时间（{{ formatterData }}）</el-radio
            >
            <el-radio :label="RestartTaskTypeEnum.default" class="f-mt10" style="display: block"
              >系统默认开始学习时间</el-radio
            >
            <el-radio :label="RestartTaskTypeEnum.custom" class="f-mt10" style="display: block"
              >自定义时间<el-date-picker
                v-model="resetItem.startTime"
                value-format="yyyy-MM-dd 00:00:00"
                :disabled="resetItem.timeType !== RestartTaskTypeEnum.custom"
                type="date"
                size="small"
                class="f-ml10"
                placeholder="选择日期"
              >
              </el-date-picker
            ></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item class="m-btn-bar">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="confirm">确定</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Vue, Component, Ref } from 'vue-property-decorator'
  import ExecutionItem from '@api/service/management/intelligence-learning/model/ExecutionItem'
  import { RestartTaskTypeEnum } from '@api/service/management/intelligence-learning/enum/RestartTaskTypeEnum'
  import { bind, debounce } from 'lodash-decorators'
  import { ElForm } from 'element-ui/types/form'

  class ResetItem {
    timeType: RestartTaskTypeEnum = null
    startTime = ''
  }
  @Component({})
  export default class extends Vue {
    @Ref('formRef') formRef: ElForm

    /**
     * 显隐
     */
    visible = false

    /**
     * 加载状态
     */
    loading = false

    /**
     * 重开时间配置
     */
    resetItem = new ResetItem()

    /**
     * 时间类型枚举
     */
    RestartTaskTypeEnum = RestartTaskTypeEnum

    /**
     * 存储数据
     */
    executionItem = new ExecutionItem()

    rules = {
      timeType: [{ validator: this.validateTimeTyoe, trigger: 'change', required: true }]
    }

    get formatterData() {
      if (!this.executionItem.expectStartTime) {
        return '-'
      } else {
        return this.executionItem.expectStartTime.substring(0, 10)
      }
    }

    validateTimeTyoe(rule: any, value: number, callback: any) {
      if (!value && value != 0) {
        callback(new Error('请选择开始时间类型'))
      } else {
        if (value == RestartTaskTypeEnum.custom) {
          if (!this.resetItem.startTime) {
            callback(new Error('请选择自定义时间'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
    }

    async open(row: ExecutionItem) {
      this.visible = true
      this.loading = true
      this.formRef?.clearValidate()
      Object.assign(this.executionItem, row)
      try {
        await this.executionItem.queryUploadExpectStartTime()
        this.resetItem.timeType = RestartTaskTypeEnum.last
        this.resetItem.startTime = ''
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 取消
     */
    cancel() {
      this.visible = false
    }

    /**
     * 确定
     */
    @bind
    @debounce(200)
    async confirm() {
      this.formRef.validate(async (valid: boolean) => {
        try {
          if (valid) {
            this.loading = true
            const res = await this.executionItem.restartTask({
              type: this.resetItem.timeType,
              startTime: this.resetItem.startTime
            })
            console.log(this.loading, 'xxxxx')
            if (res.code == 200) {
              this.resetItem.timeType = RestartTaskTypeEnum.last
              this.resetItem.startTime = ''
              this.executionItem = new ExecutionItem()
              this.$message.success('重启成功')
              this.visible = false
              this.$emit('success')
            } else if (res.code == 1020) {
              this.$message.error('学员正在学习，不支持重启任务')
            } else if (res.code == 1022) {
              this.$message.error('本班级已有另一智能学习任务处于未开始或执行中，无法重启当前任务，请确认。')
            } else {
              this.$message.error((res.message as string) || '重启失败')
            }
          }
        } catch (e) {
          console.log(e)
        } finally {
          this.loading = false
        }
      })
    }
  }
</script>
