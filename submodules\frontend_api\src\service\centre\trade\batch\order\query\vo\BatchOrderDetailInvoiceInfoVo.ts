import { InvoiceDraftStatusEnum } from '@api/service/centre/trade/batch/order/enum/InvoiceDraftStatusList'
import { InvoiceTitleTypeEnum } from '@api/service/common/enums/trade-configuration/InvoiceTitleType'
import { InvoiceTypeEnum } from '@api/service/centre/trade/batch/order/enum/InvoiceTypeList'
import InvoiceListResponse from '@api/service/management/trade/batch/invoice/query/vo/InvoiceListResponse'
import OffLinePageInvoiceResponseVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
import BatchOrderUtils from '@api/service/centre/trade/batch/order/query/util/BatchOrderUtils'
import { InvoiceCategoryEnum } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
import { TitleTypeEnum } from '@api/service/management/trade/batch/invoice/enum/InvoiceEnum'
import BatchOrderDetailDeliveryInfoVo from '@api/service/centre/trade/batch/order/query/vo/BatchOrderDetailDeliveryInfoVo'
import { InvoiceFaceInfoResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

/**
 * @description 【集体报名订单】订单详情-发票信息
 */
class BatchOrderDetailInvoiceInfoVo {
  /**
   * 发票类型 1：增值税普通发票 2：增值税专用发票
   */
  invoiceType: InvoiceTypeEnum = null

  /**
   * 是否是线下票
   */
  isOffLine: boolean = null

  /**
   * 纸质还是电子 1电子 2纸质
   */
  invoiceMethod: number = undefined

  /**
   * 发票抬头类型 1：个人 2：单位
   */
  invoiceTitleType: InvoiceTitleTypeEnum = null

  /**
   * 发票抬头
   */
  invoiceTitle = ''

  /**
   * 申请时间
   */
  applyDate = ''

  /**
   * 开票时间
   */
  draftDate = ''

  /**
   * 发票号码
   */
  invoiceNo = ''

  /**
   * 开票状态 1：开票中 2：开票成功 3：冻结中 4：已作废
   */
  invoiceDraftStatus: InvoiceDraftStatusEnum = null

  /**
   * 发票预览信息地址
   */
  invoicePreviewUrl = ''
  /**
   * 发票预览信息地址 XML
   */
  invoiceXmlUrl = ''
  /**
   * 发票预览信息地址 OFD
   */
  invoiceOfdUrl = ''
  /**
   * 配送信息
   */
  deliveryInfo: BatchOrderDetailDeliveryInfoVo = new BatchOrderDetailDeliveryInfoVo()
  /**
   * 联系邮箱
   */
  contactEmail = ''
  /**
   * 联系人
   */
  contactPhone = ''

  /**
   * 发票票面信息
   */
  invoiceFaceInfo?: InvoiceFaceInfoResponse = new InvoiceFaceInfoResponse()

  static fromOnline(response: InvoiceListResponse): BatchOrderDetailInvoiceInfoVo {
    const detail = new BatchOrderDetailInvoiceInfoVo()
    detail.invoiceDraftStatus = BatchOrderUtils.getOnlineInvoiceDraftStatus(response)
    detail.invoiceType =
      response.invoiceCategory === InvoiceCategoryEnum.VATPLAININVOICE ? InvoiceTypeEnum.VAT_NORMAL_INVOICE : null
    detail.isOffLine = false
    detail.invoiceTitleType =
      response.titleType === TitleTypeEnum.PERSONAL
        ? InvoiceTitleTypeEnum.INDIVIDUAL
        : response.titleType === TitleTypeEnum.UNIT
        ? InvoiceTitleTypeEnum.UNIT
        : null
    detail.invoiceFaceInfo = response.invoiceFaceInfo || new InvoiceFaceInfoResponse()
    detail.invoiceTitle = response.title ?? ''
    detail.applyDate = response.applyForDate ?? ''
    detail.draftDate = response.invoiceDate ?? ''
    detail.invoiceNo = response.blueInvoiceNo ?? ''
    detail.invoicePreviewUrl = response.blueFilePath ?? ''
    detail.invoiceXmlUrl = response.blueFileXmlPath ?? ''
    detail.invoiceOfdUrl = response.blueFileOfdPath ?? ''
    return detail
  }

  static fromOffline(response: OffLinePageInvoiceResponseVo): BatchOrderDetailInvoiceInfoVo {
    const detail = new BatchOrderDetailInvoiceInfoVo()
    detail.invoiceDraftStatus = BatchOrderUtils.getOffLineInvoiceDraftStatus(response)
    detail.invoiceType =
      response.invoiceCategory === InvoiceCategoryEnum.VATPLAININVOICE
        ? InvoiceTypeEnum.VAT_NORMAL_INVOICE
        : response.invoiceCategory === InvoiceCategoryEnum.VATSPECIALPLAININVOICE
        ? InvoiceTypeEnum.VAT_SPECIAL_INVOICE
        : null
    detail.isOffLine = true
    detail.invoiceTitleType =
      response.titleType === TitleTypeEnum.PERSONAL
        ? InvoiceTitleTypeEnum.INDIVIDUAL
        : response.titleType === TitleTypeEnum.UNIT
        ? InvoiceTitleTypeEnum.UNIT
        : null
    detail.invoiceFaceInfo = response.invoiceFaceInfo || new InvoiceFaceInfoResponse()
    detail.invoiceTitle = response.title ?? ''
    detail.applyDate = response.applyForDate ?? ''
    detail.draftDate = response.invoiceDate ?? ''
    detail.invoiceNo = response.invoiceNo ?? ''
    detail.deliveryInfo = BatchOrderUtils.getDeliveryInfo(response.deliveryInfo)
    detail.deliveryInfo.region = response?.deliveryInfo?.deliveryAddress?.region
    detail.contactEmail = response.contactEmail
    detail.contactPhone = response.contactPhone
    detail.invoiceMethod = response.invoiceType

    return detail
  }
}

export default BatchOrderDetailInvoiceInfoVo
