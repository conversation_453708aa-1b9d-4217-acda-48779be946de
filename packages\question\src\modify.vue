<route-params content="/:id/:questionType"></route-params>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/question')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/question' }">试题管理</el-breadcrumb-item>
      <el-breadcrumb-item>修改试题</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="elForm" :model="questionParams" label-width="auto" class="m-form f-mt20">
              <el-form-item label="试题题库" prop="libraryId" required>
                <question-cascader v-model="relatedLibrary" ref="relatedLibrary"></question-cascader>
              </el-form-item>
              <el-form-item label="关联课程" prop="relateCourseId">
                <course-drawer :value.sync="questionParams.relateCourseId" ref="relatedCourse"></course-drawer>
              </el-form-item>
              <el-form-item label="试题题型" prop="questionType" required>
                <el-radio-group
                  v-model="questionParams.questionType"
                  @change="createNewQuestionType"
                  size="medium"
                  disabled
                >
                  <el-radio v-for="(item, index) in questionTypeList" :key="index" :label="item.value"
                    >{{ item.title }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="难度" prop="questionDifficulty" required>
                <el-radio-group v-model="questionParams.questionDifficulty">
                  <el-radio :label="1">难</el-radio>
                  <el-radio :label="2">中等</el-radio>
                  <el-radio :label="3">简单</el-radio>
                </el-radio-group>
              </el-form-item>

              <single
                v-if="questionParams.questionType === 1"
                :key="`1${basicKey}`"
                question-type="2"
                :is-update="true"
                :create-question-info.sync="questionParams"
                ref="singleRef"
              />
              <judge
                v-if="questionParams.questionType === 4"
                :create-question-info.sync="questionParams"
                parent-component-question-type="1"
                :key="`4${basicKey}`"
                ref="judgeRef"
              />

              <multiple
                v-if="questionParams.questionType === 2"
                :key="`2${basicKey}`"
                :create-question-info.sync="questionParams"
                question-type="3"
                :is-update="true"
                ref="multipleRef"
              />
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="commit" :loading="loading">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import { isEmpty } from 'lodash'
  import questionCascader from '@hbfe/jxjy-admin-question/src/components/question-cascader.vue'
  import CourseDrawer from '@hbfe/jxjy-admin-question/src/components/course-drawer.vue'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import UpdateQuestion from '@api/service/management/resource/question/mutation/UpdateQuestion'
  import UpdateQuestionVo from '@api/service/management/resource/question/mutation/vo/update/UpdateQuestionVo'
  import { UiPage } from '@hbfe/common'

  @Component({
    components: {
      CourseDrawer,
      questionCascader,
      Single: () => import('@hbfe/jxjy-admin-question/src/components/single.vue'), // 单选题
      Multiple: () => import('@hbfe/jxjy-admin-question/src/components/multiple.vue'), // 多选题
      Judge: () => import('@hbfe/jxjy-admin-question/src/components/judge.vue') // 判断题
    }
  })
  export default class extends Vue {
    @Ref('components') components: any
    @Ref('judgeRef') judgeRef: any
    @Ref('singleRef') singleRef: any
    @Ref('multipleRef') multipleRef: any
    @Ref('synthesisRef') synthesisRef: any
    props = {
      lazy: true,
      lazyLoad: this.load
    }
    basicKey = new Date().getTime()
    @Ref('relatedCourse') relatedCourse: any
    relatedLibrary = ''
    questionId = '' //id
    questionTypes = ''
    teachUnitName = '' //机构名称
    workTypes = new Array<Array<string>>() //工种
    loading = false
    /**
     *  规则匹配
     */
    rules = {
      libraryId: [
        {
          required: true,
          message: '请选择题库',
          trigger: 'blur'
        }
      ],
      questionType: [
        {
          required: true,
          message: '请选择关联课程',
          trigger: 'blur'
        }
      ],
      title: [
        {
          required: true,
          message: '请输入试题题目',
          trigger: 'blur'
        }
      ],
      questionDifficulty: [
        {
          required: true,
          message: '请选择难度',
          trigger: 'blur'
        }
      ],
      tags: [
        {
          validator: async (rule: any, value: any, callback: (error?: Error) => void) => {
            if (isEmpty(value)) {
              callback(new Error('请选择关联工种'))
            } else {
              callback()
            }
          },
          required: true,
          trigger: 'change'
        }
      ]
    }
    /**
     *  UI配置的常量
     */
    uiConfig = {
      refName: {
        judge: 'judgeRef',
        single: 'singleRef',
        multiple: 'multipleRef',
        synthesis: 'synthesisRef'
      },
      commit: false
    }
    /**
     *  获取系统提供的试题题型
     */
    questionTypeList = [
      { value: 1, title: '单选题' },
      { value: 4, title: '判断题' },
      { value: 2, title: '多选题' }
    ]

    // 获取修改试题实例
    updateQuestion: UpdateQuestion
    // 修改请求入参
    questionParams = new UpdateQuestionVo()
    libraryFactory = ResourceModule.queryQuestionLibraryFactory

    async created() {
      await this.init()
    }
    async checkQuestionLibrary() {
      const page = new UiPage()
      page.pageNo = 1
      page.pageSize = 200
      const res = await this.libraryFactory.queryQuestionLibraryMultiton.queryQuestionBankLibraryItem(
        page,
        this.relatedLibrary
      )
      if (res.data?.length) {
        this.$message.error('当前选择的题库不是末级题库，请刷新页面后重新选择！')
        return false
      } else return true
    }

    // 修改试题
    async commit() {
      this.loading = true
      let validForm
      //单选

      if (this.questionParams.questionType == 1) validForm = this.singleRef.validForm()
      // 多选
      if (this.questionParams.questionType == 2) validForm = this.multipleRef.validForm()
      //判断
      if (this.questionParams.questionType == 4) validForm = this.judgeRef.validForm()
      if (!validForm) {
        this.loading = false
        return
      }
      if (!(await this.checkQuestionLibrary())) {
        this.loading = false
        return
      }
      this.questionParams.libraryId = this.relatedLibrary
      console.log(this.questionParams.libraryId, 'this.questionParams.libraryId')
      this.uiConfig.commit = true
      try {
        const res = await this.updateQuestion.doUpdateQuestion()

        console.log(res, 'res-----修改试题')
        if (res.code !== 200) {
          this.$message.error(`${res.errors[0].message}`)
          this.loading = false
        } else {
          this.$message.success('更新成功')
          this.$router.go(-1)
        }
      } catch (e) {
        console.log(e)
        this.loading = false
      }
      this.uiConfig.commit = false
    }
    createNewQuestionType() {
      this.basicKey = new Date().getTime()
    }
    /**
     * 加载试题题库
     */
    async load(node: any, resolve: any) {
      // if (!node?.data?.value) {
      //   const rootCategory = QuestionLibraryUI.createRootCategory()
      //   const node: any = {
      //     value: '',
      //     label: '',
      //     leaf: false
      //   }
      //   debugger
      //   await QuestionLibraryModule.listCategory({ parentId: node.value || '-2', unitId: this.questionForm?.unitId })
      //   node.leaf = this.categoryList.length > 0 ? true : false
      //   node.value = rootCategory.id
      //   node.label = rootCategory.name
      //   const nodes = [node]
      //   return resolve(nodes)
      // }
      // const status = await QuestionLibraryModule.listCategory({
      //   parentId: node.value,
      //   unitId: this.questionForm?.unitId
      // })
      // if (status?.code === 200) {
      //   const nodes: Array<any> = []
      //   this.categoryList.forEach(p => {
      //     if (p.enabled === false) {
      //       return
      //     }
      //     const node: any = {
      //       label: '',
      //       value: '',
      //       leaf: false
      //     }
      //     node.value = p.id
      //     node.label = p.name
      //     node.leaf = !p.hasChildren
      //     nodes.push(node)
      //   })
      //   resolve(nodes)
      // } else {
      //   this.$message.error('获取题库分类失败')
      // }
    }
    /**
     * 取消试题更新
     */
    cancel() {
      this.$confirm('是否放弃编辑？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$router.go(-1)
      })
    }

    /**
     * 初始化
     */
    async init() {
      this.uiConfig.commit = true
      const questionId = this.$route?.params?.id
      const qType = Number(this.$route?.params?.questionType)

      this.updateQuestion = ResourceModule.mutationQuestionFactory.getUpdateQuestion(questionId, qType)
      const res = await this.updateQuestion.doQueryQuestionInfo()
      if (res?.code == 200) {
        this.questionParams = this.updateQuestion.updateQuestionParams
        // this.relatedLibrary = [this.questionParams.libraryId]
        this.relatedLibrary = this.questionParams.libraryId
      }
      this.basicKey = new Date().getTime()
      console.log(this.questionParams, 'item')

      this.$nextTick(() => {
        this.relatedCourse.setName()
      })
      this.uiConfig.commit = false
    }
  }
</script>
