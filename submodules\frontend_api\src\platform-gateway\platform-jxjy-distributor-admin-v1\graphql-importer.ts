import checkMiniDistributor from './queries/checkMiniDistributor.graphql'
import findInfoByDomainName from './queries/findInfoByDomainName.graphql'
import findPublishedInfoByIdentifier from './queries/findPublishedInfoByIdentifier.graphql'
import isOnlineSchoolContractExpired from './queries/isOnlineSchoolContractExpired.graphql'
import validDistributor from './queries/validDistributor.graphql'
import validDistributorPortal from './queries/validDistributorPortal.graphql'
import whetherBelongDistributor from './queries/whetherBelongDistributor.graphql'
import createDistributor from './mutates/createDistributor.graphql'
import createDistributorAdmin from './mutates/createDistributorAdmin.graphql'
import createMiniDistributor from './mutates/createMiniDistributor.graphql'
import enablePortal from './mutates/enablePortal.graphql'
import importSupplyPricingSchemeBatch from './mutates/importSupplyPricingSchemeBatch.graphql'
import importSupplyPricingSchemeSetupBatch from './mutates/importSupplyPricingSchemeSetupBatch.graphql'
import saveDistributorBannerList from './mutates/saveDistributorBannerList.graphql'
import savePortalBasicMessage from './mutates/savePortalBasicMessage.graphql'
import savePosterConfiguration from './mutates/savePosterConfiguration.graphql'
import saveStatusDistributorAdmin from './mutates/saveStatusDistributorAdmin.graphql'
import updateDistributor from './mutates/updateDistributor.graphql'
import updateDistributorAdmin from './mutates/updateDistributorAdmin.graphql'
import updateMiniDistributor from './mutates/updateMiniDistributor.graphql'

export {
  checkMiniDistributor,
  findInfoByDomainName,
  findPublishedInfoByIdentifier,
  isOnlineSchoolContractExpired,
  validDistributor,
  validDistributorPortal,
  whetherBelongDistributor,
  createDistributor,
  createDistributorAdmin,
  createMiniDistributor,
  enablePortal,
  importSupplyPricingSchemeBatch,
  importSupplyPricingSchemeSetupBatch,
  saveDistributorBannerList,
  savePortalBasicMessage,
  savePosterConfiguration,
  saveStatusDistributorAdmin,
  updateDistributor,
  updateDistributorAdmin,
  updateMiniDistributor
}
