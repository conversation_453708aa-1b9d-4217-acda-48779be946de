import {
  SaveTrainingChannelPortalInfoRequest,
  TrainingChannelPortalBanner
} from '@api/platform-gateway/platform-training-channel-v1'
import { LogoType } from '../enum/LogoType'
import { PhoneFooterType } from '../enum/PhoneFooterType'
/**
 * 轮播图信息
 */
class CarouselImage {
  /**
   * ID
   */
  id = ''
  /**
   * 轮播图URL
   */
  url = ''
  /**
   * 排序
   */
  sort = 0
  /**
   * 链接
   */
  link = ''
  /**
   * 创建时间
   */
  createdAt = ''
}

/**
 * 门户信息类
 */
export default class PortalInfo {
  /**
   * 专题门户logo类型
   */
  logoType: LogoType = undefined
  /**
   * 专题门户logo
   */
  logo = ''
  /**
   * 客服电话类型
   */
  phoneType: PhoneFooterType = PhoneFooterType.sameasschool
  /**
   * 客服电话
   */
  phone = ''
  /**
   * 客服电话图片
   */
  phoneImgUrl = ''
  /**
   * 培训流程类型
   */
  processType: PhoneFooterType = PhoneFooterType.sameasschool
  /**
   * 培训流程
   */
  process = ''
  /**
   * 企业微信客服类型
   */
  customerServiceType: PhoneFooterType = PhoneFooterType.sameasschool
  /**
   * 企业微信客服
   */
  customerService = ''
  /**
   * 资讯时间
   */
  informationTime = ''
  /**
   * 底部落款类型
   */
  footerType: PhoneFooterType = PhoneFooterType.sameasschool
  /**
   * 专题门户底部落款内容Id 专题门户底部落款类型为自定义时 有值
   */
  bottomShowContentId = ''
  /**
   * 底部落款
   */
  footer = ''
  /**
   * web轮播图设置
   */
  webCarouselSettings: CarouselImage[] = []
  /**
   * H5轮播图设置
   */
  H5CarouselSettings: CarouselImage[] = []
  /**
   * PC模板id
   */
  pcTemplateId = 'PCTemplateId-1'
  /**
   * h5模板id
   */
  h5TemplateId = 'H5TemplateId-1'
  /**
   * 新增轮播图
   */
  addCarouselImage(carouselImage: CarouselImage, isWeb = true) {
    if (isWeb) {
      this.webCarouselSettings.push(carouselImage)
    } else {
      this.H5CarouselSettings.push(carouselImage)
    }
  }

  static to(vo: PortalInfo) {
    const dto = new SaveTrainingChannelPortalInfoRequest()
    dto.bottomShowContent = vo.footer
    dto.bottomShowContentId = vo.bottomShowContentId
    dto.bottomShowType = vo.footerType
    dto.customerServicePhoneType = vo.phoneType
    if (vo.phoneType === PhoneFooterType.custom) {
      dto.customerServicePhone = vo.phone
    }
    dto.customerServicePhonePictureUrl = vo.phoneImgUrl
    if (vo.logoType === LogoType.text) {
      dto.logoName = vo.logo
    } else {
      dto.logoPictureUrl = vo.logo
    }
    dto.logoType = vo.logoType
    dto.seekTime = vo.informationTime
    const bannerList = new Array<TrainingChannelPortalBanner>()
    vo.webCarouselSettings.forEach(item => {
      const temp = new TrainingChannelPortalBanner()
      temp.id = item.id
      temp.linkUrl = item.link
      temp.pictureUrl = item.url
      temp.sort = item.sort
      temp.type = 1
      bannerList.push(temp)
    })
    vo.H5CarouselSettings.forEach(item => {
      const temp = new TrainingChannelPortalBanner()
      temp.id = item.id
      temp.linkUrl = item.link
      temp.pictureUrl = item.url
      temp.sort = item.sort
      temp.type = 2
      bannerList.push(temp)
    })
    dto.trainingChannelPortalBannerList = bannerList
    vo.processType = PhoneFooterType.custom
    dto.trainingProcessType = vo.processType
    if (vo.processType === PhoneFooterType.custom) {
      dto.trainingProcessAttachments = [
        {
          name: '培训流程',
          url: vo.process
        }
      ]
    }
    dto.enterpriseWechatCustomerType = vo.customerServiceType
    if (vo.customerServiceType === PhoneFooterType.custom) {
      dto.enterpriseWechatCustomerAttachments = [
        {
          name: '企业微信客服',
          url: vo.customerService
        }
      ]
    }
    return dto
  }
}
