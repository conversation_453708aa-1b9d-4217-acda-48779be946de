<!--
 * @Author: WRP
 * @Date: 单个属性修改输入框
-->
<template>
  <span>
    <el-tooltip class="item" effect="dark" placement="top">
      <span class="el-icon-edit-outline f-c9 edit-icon" @click="isShowEditbox = true"></span>
      <div slot="content">编辑</div>
    </el-tooltip>
    <div class="edit-box" v-if="isShowEditbox">
      <technical-grade-select
        v-model="editInputValue"
        :industry-id="industryId"
        :industry-property-id="industryPropertyId"
      ></technical-grade-select>
      <div class="op">
        <el-tooltip class="item" effect="dark" placement="top">
          <span class="el-icon-circle-check f-cb edit-icon" @click="saveEdit"></span>
          <div slot="content">保存</div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" placement="top">
          <span class="el-icon-circle-close f-c9 edit-icon" @click="cancelEdit"></span>
          <div slot="content">取消</div>
        </el-tooltip>
      </div>
    </div>
  </span>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import BizTechnicalGradeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-technical-grade-select.vue'
  import TrainingCategoryAndMajorCascader from '@hbfe/jxjy-admin-customerService/src/personal/components/components/training-category-and-major-cascader.vue'
  import TechnicalGradeSelect from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/training-category-select.vue'
  import { IndustryOption } from '@hbfe/jxjy-admin-customerService/src/personal/mixins/PersonalBaseData'
  @Component({
    components: {
      TrainingCategoryAndMajorCascader,
      BizTechnicalGradeSelect,
      TechnicalGradeSelect
    }
  })
  export default class extends Vue {
    // 修改的值
    editInputValue = ''
    // 输入框显隐
    isShowEditbox = false
    // 修改后的值
    newValue = ''
    // 行业数组
    industryOptions = new Array<IndustryOption>()

    // 输入框提示语
    @Prop({
      type: String,
      default: '请输入证件号'
    })
    placeholder: string

    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string

    @Prop({
      type: String,
      default: ''
    })
    industryId: string

    @Watch('value', {
      deep: true
    })
    valChange(val: string) {
      this.editInputValue = val
    }

    // 修改的值
    @Watch('editInputValue', {
      deep: true
    })
    inputValueChange(val: string) {
      if (val?.length) {
        this.newValue = val
      }
    }

    // 确认修改属性值
    saveEdit() {
      if (this.newValue) {
        this.$emit('input', this.newValue)
        this.$emit('saveEdit', true)
        this.isShowEditbox = false
      } else {
        return this.$message.error('请选择技术等级')
      }
    }

    // 放弃修改属性值
    cancelEdit() {
      this.newValue = ''
      this.editInputValue = this.value || ''
      this.isShowEditbox = false
    }
  }
</script>
