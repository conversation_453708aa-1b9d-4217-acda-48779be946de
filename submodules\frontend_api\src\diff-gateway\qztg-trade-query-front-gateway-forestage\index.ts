import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'qztg-trade-query-front-gateway-forestage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class CommoditySkuForestageResponse {
  commoditySkuId: string
  commodityBasicData: CommodityBasicDataResponse
  skuProperty: CommoditySkuPropertyResponse
  possessionInfo: UserPossessionInfoResponse
  commodityPurchaseChannelConfig: PurchaseChannelConfigResponse
  onShelve: OnShelveResponse
  resource: ResourceResponse
  trainingChannels: Array<CommodityTrainingChannelResponse>
  tppTypeId: string
  unitId: string
  unitName: string
}

export class PortalCommoditySkuForestageResponse {
  portalCommoditySkuId: string
  portalSaleTitle: string
  portalCommoditySkuSourceType: number
  portalCommoditySkuSourceId: string
  skuProperty: PortalCommoditySkuPropertyResponse
  portalCommoditySoldCount: number
}

export class CommodityBasicDataResponse {
  saleTitle: string
  price: number
  commodityPicturePath: string
}

export class CommodityPurchaseChannelConfigResponse {
  customerPurchase: PurchaseChannelConfigResponse
  collectivePurchase: PurchaseChannelConfigResponse
  administratorImport: PurchaseChannelConfigResponse
  collectiveSignUpPersonalPay: PurchaseChannelConfigResponse
}

export class CommoditySkuPropertyResponse {
  year: SkuPropertyResponse
  province: SkuPropertyResponse
  city: SkuPropertyResponse
  county: SkuPropertyResponse
  industry: SkuPropertyResponse
  subjectType: SkuPropertyResponse
  trainingCategory: SkuPropertyResponse
  trainingProfessional: SkuPropertyResponse
  technicalGrade: SkuPropertyResponse
  trainingObject: SkuPropertyResponse
  positionCategory: SkuPropertyResponse
  jobLevel: SkuPropertyResponse
  jobCategory: SkuPropertyResponse
  grade: SkuPropertyResponse
  subject: SkuPropertyResponse
  learningPhase: SkuPropertyResponse
  discipline: SkuPropertyResponse
  certificatesType: SkuPropertyResponse
  practitionerCategory: SkuPropertyResponse
  qualificationCategory: SkuPropertyResponse
  trainingForm: SkuPropertyResponse
}

export class CommodityTrainingChannelResponse {
  trainingChannelId: string
  trainingChannelName: string
  sort: number
}

export class OnShelveResponse {
  shelveStatus: number
  lastOnShelveTime: string
  offShelveTime: string
  onShelvePlanTime: string
  offShelvePlanTime: string
  publishTime: string
}

export class PortalCommoditySkuPropertyResponse {
  yearForPortal: SkuPropertyResponse
  provinceForPortal: SkuPropertyResponse
  cityForPortal: SkuPropertyResponse
  countyForPortal: SkuPropertyResponse
  industryForPortal: SkuPropertyResponse
  trainingProfessionalForPortal: SkuPropertyResponse
  belongIndustryForPortal: SkuPropertyResponse
}

export class PurchaseChannelConfigResponse {
  couldSee: boolean
  couldBuy: boolean
}

export class SkuPropertyResponse {
  skuPropertyValueId: string
  skuPropertyValueName: string
}

export class UserPossessionInfoResponse {
  possessing: boolean
  sourceType: number
  sourceId: string
  subOrderDeliveryStatus: number
}

export interface ResourceResponse {
  resourceType: string
}

export class SchemeResourceResponse implements ResourceResponse {
  schemeId: string
  schemeName: string
  period: number
  schemeType: string
  resourceType: string
}

export class CommoditySkuResponse {
  /**
   * 商品id
   */
  commoditySkuId: string
  /**
   * 商品销售标题
   */
  saleTitle: string
  /**
   * 商品价格
   */
  price: number
  /**
   * 培训方案id
   */
  schemeId: string
  /**
   * 商品封面图路径
   */
  commodityPicturePath: string
  /**
   * 用户商品拥有信息
   */
  possessionInfo: UserPossessionInfoResponse
  /**
   * 商品属性信息
   */
  skuProperty: CommoditySkuPropertyResponse
  /**
   * 商品所有渠道的配置信息
   */
  commodityPurchaseChannelConfig: CommodityPurchaseChannelConfigResponse
  /**
   * 上下架信息
   */
  onShelve: OnShelveResponse
}

export class QZTGCommoditySkuForestageResponse {
  /**
   * 当前商品合并了哪些列表
   */
  mergedCommodities: Array<CommoditySkuResponse>
  commoditySkuId: string
  commodityBasicData: CommodityBasicDataResponse
  skuProperty: CommoditySkuPropertyResponse
  possessionInfo: UserPossessionInfoResponse
  commodityPurchaseChannelConfig: PurchaseChannelConfigResponse
  onShelve: OnShelveResponse
  resource: ResourceResponse
  trainingChannels: Array<CommodityTrainingChannelResponse>
  tppTypeId: string
  unitId: string
  unitName: string
}

export class QZTGPortalCommoditySkuResponse {
  /**
   * 当前商品合并了哪些列表
   */
  mergedCommodities: Array<CommoditySkuResponse>
  originCommodityInfo: CommoditySkuForestageResponse
  portalCommoditySkuForestageResponse: PortalCommoditySkuForestageResponse
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取专题商品详情
   * @param commoditySkuId    商品ID
   * @param trainingChannelId 专题ID
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCommoditySkuTrainingChannelInServicer(
    params: { commoditySkuId?: string; trainingChannelId?: string; isShowAll?: boolean },
    query: DocumentNode = GraphqlImporter.getCommoditySkuTrainingChannelInServicer,
    operation?: string
  ): Promise<Response<QZTGCommoditySkuForestageResponse>> {
    return commonRequestApi<QZTGCommoditySkuForestageResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: true
      })
    )
  }

  /**   * 门户商品 - 获取用户自主购买渠道商品详情
   * @Param portalCommoditySkuId 门户商品ID或原商品id(使用原商品id的话只能获取到原商品信息）
   * @Param commoditySkuId 需要获取合并商品的信息传，不需要合并商品信息不用传
   * @Param isShowAll 是否展示所有资源
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getPortalCommoditySkuCustomerPurchaseInServicer(
    params: { portalCommoditySkuId?: string; commoditySkuId?: string; isShowAll?: boolean },
    query: DocumentNode = GraphqlImporter.getPortalCommoditySkuCustomerPurchaseInServicer,
    operation?: string
  ): Promise<Response<QZTGPortalCommoditySkuResponse>> {
    return commonRequestApi<QZTGPortalCommoditySkuResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
