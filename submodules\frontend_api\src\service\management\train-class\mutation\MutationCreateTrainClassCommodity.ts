import { Response, ResponseStatus } from '@hbfe/common'
import CreateCommodityBase from '@api/service/management/train-class/mutation/vo/CreateCommodityBase'
import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
import MsLearningScheme from '@api/ms-gateway/ms-learningscheme-v1'
import CertificateVo from '@api/service/management/train-class/mutation/vo/CertificateVo'
import { QuestionnaireAppliedRangeTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'
import MsTeachingplanV1 from '@api/ms-gateway/ms-teachingplan-v1'
import IssueCourseDetail from '@api/service/common/scheme/model/IssueCourseDetail'
import DataResolve from '@api/service/common/utils/DataResolve'
import GetIssueCourseListUnmodifiableIdsParam from '@api/service/management/train-class/offlinePart/model/GetIssueCourseListUnmodifiableIdsParam'
import ValidateIssueSignUpNumParam from '@api/service/management/train-class/offlinePart/model/ValidateIssueSignUpNumParam'
import CheckIssueNoAvailableParam from '@api/service/management/train-class/offlinePart/model/CheckIssueNoAvailableParam'
import TrainClassConfigJsonManager from '@api/service/management/train-class/Utils/TrainClassConfigJsonManager'
import PlatformLearningschemeV1 from '@api/platform-gateway/platform-learningscheme-v1'
import MsLearningschemeEnrollmentV1, {
  ValidateIssueSignUpNumRequest
} from '@api/ms-gateway/ms-learningscheme-enrollment-v1'
import Context from '@api/service/common/context/Context'
import SdsPlatformJxjyLearningschemeV1, {
  JudgeIssueNumExistRequest
} from '@api/platform-gateway/sds-platform-jxjy-learningscheme-v1'
import PlatformJxjyQuestionnaireV1, {
  AhjsQuestionnaireDeleteVerifyRequest
} from '@api/platform-gateway/platform-jxjy-questionnaire-v1'

/**
 * 不允许引用的问卷详情
 */
export class IllegalQuestionnaireDetail {
  /**
   * 问卷名称
   */
  name = ''
  /**
   * 问卷应用范围类型, 0-培训方案 1-线上课程 2-期别
   */
  appliedRangeType: QuestionnaireAppliedRangeTypeEnum
}

/**
 * 创建方案返回值
 */
export class CreateCommodityResponse {
  /**
   * 方案Id
   */
  schemeId = ''
}

/**
 * 创建制培训班商品类//创建，修改和复制，最终都是拿到这个业务类，ui层无需关心，就按照统一逻辑调用即可
 */
class MutationCreateTrainClassCommodity extends CreateCommodityBase {
  // region properties
  /**
   *培训班基础信息，类型为TrainClassBaseModel
   */
  trainClassBaseInfo = new TrainClassBaseModel()
  /**
   *学习方式，类型为LearningType
   */
  learningTypeModel = new LearningType()
  /**
   *学习方式初始数据，类型为LearningType，修改用
   */
  learningTypeModelCopy = new LearningType()
  /**
   * 证明(修改方案判断证明是否移除)
   */
  certificate = new CertificateVo()
  /**
   * 时间配置
   */
  timeConfig: {
    onShelvesPlanTime: string
    offShelvesPlanTime: string
    registerBeginDate: string
    registerEndDate: string
  } = null
  /**
   * 是否基础信息修改 调用只修改基础信息接口 UI判断页面组件显隐
   */
  isBaseInfoModify = false
  // /**
  //  *考核规则，类型为AssessmentTargetModel[]
  //  */
  // assessmentRules: AssessmentTargetModel[] = []
  // endregion
  // region methods

  /**
   * 创建或者更新培训班商品
   */
  async createTrainClassCommodity(): Promise<Response<CreateCommodityResponse>> {
    const result = new Response<CreateCommodityResponse>()
    result.data = new CreateCommodityResponse()
    let configJson = ''
    const convertUtil = TrainClassConfigJsonManager
    if (this.trainClassBaseInfo.id) {
      configJson = convertUtil.convertCreateCommodityToJsonString(this, this.learningTypeModelCopy)
      const params = {
        token: 'W10=',
        configJson
      }
      if (this.isBaseInfoModify) {
        // 基础信息修改
        const response = await MsLearningScheme.specialUpdateLearningScheme(params)
        result.status = response.status
        result.data.schemeId = response.data
      } else {
        // 修改培训方案
        const response = await PlatformLearningschemeV1.asyncUpdateLearningScheme(params)
        result.status = response.status
        const data = response.data
        if (data) {
          result.data.schemeId = data.schemeId
        }
      }
    } else {
      configJson = convertUtil.convertCreateCommodityToJsonString(this)
      const response = await PlatformLearningschemeV1.asyncCreateLearningScheme({
        token: 'W10=',
        configJson
      })
      result.status = response.status
      const data = response.data
      if (data) {
        result.data.schemeId = data.schemeId
      }
    }
    return result
  }

  /**
   * 校验期别编号是否重复
   * @description true-重复，false-不重复
   * @return
   * 70001 参数缺失，此时未发起请求
   */
  async judgeIssueNumExist(params: CheckIssueNoAvailableParam): Promise<Response<boolean>> {
    const servicerId = Context.businessEnvironment?.serviceToken?.tokenMeta?.servicerId
    const result = new Response<boolean>()
    result.status = new ResponseStatus(200)
    result.data = false
    const { year, issueNo } = params
    if (!year || !servicerId || !issueNo) {
      result.status = new ResponseStatus(70001, '参数缺失')
      return result
    }
    const request = new JudgeIssueNumExistRequest()
    request.servicerId = servicerId
    request.year = year
    request.issueNum = issueNo
    return await SdsPlatformJxjyLearningschemeV1.judgeIssueNumExist(request)
  }

  /**
   * 校验期别报名人数
   * @param params 查询参数
   * @return
   * 80001 已报名人数>准备开设报名人数
   */
  async validateIssueSignUpNum(params: ValidateIssueSignUpNumParam) {
    let result = new ResponseStatus(200)
    const request = new ValidateIssueSignUpNumRequest()
    request.allowSignUpNum = params.allowSignUpNum
    request.schemeId = params.schemeId
    request.issueId = params.issueId
    const { status, data } = await MsLearningschemeEnrollmentV1.validateIssueSignUpNum(request)
    result = status
    if (status && status.isSuccess() && data) {
      result = new ResponseStatus(Number(data.code), data.message)
    }
    return result
  }

  /**
   * 校验期别报名数据
   * @param issueId 期别id
   * @return
   * 80002 存在报名数据
   */
  async validateIssueSignUpData(issueId: string) {
    let result = new ResponseStatus(200)
    const { status, data } = await MsLearningschemeEnrollmentV1.validateIssueSignUpData(issueId)
    result = status
    if (status && status.isSuccess() && data) {
      result = new ResponseStatus(Number(data.code), data.message)
    }
    return result
  }

  /**
   * 校验问卷是否可删除
   * @param questionnaireId 问卷id
   * 200 可以删除
   * 500 不能删除。具体看返回的message
   */
  async questionnaireDeleteVerify(questionnaireId: string) {
    let result = new ResponseStatus(200)
    const request = new AhjsQuestionnaireDeleteVerifyRequest()
    request.questionnaireId = questionnaireId
    const { status, data } = await PlatformJxjyQuestionnaireV1.questionnaireDeleteVerify(request)
    result = status
    if (status && status.isSuccess() && data) {
      result = new ResponseStatus(Number(data.code), data.message)
    }
    return result
  }

  /**
   * 获取不可修改的期别课程id
   * @description 修改方案时使用
   * @param param 查询参数
   * @return {
   *   existTimeOverlap 是否存在时间冲突
   *   existCheckInRecord 是否存在打卡记录
   *   existStarted 存在已开始
   * }
   */
  async getIssueCourseListUnmodifiableIds(param: GetIssueCourseListUnmodifiableIdsParam): Promise<{
    existTimeOverlap: string[]
    existCheckInRecord: string[]
    existStarted: string[]
  }> {
    const result = {
      existTimeOverlap: [] as string[],
      existCheckInRecord: [] as string[],
      existStarted: [] as string[]
    }
    const { schemeId, issueId, issueCourseIds } = param
    let planItemIds = [] as string[]
    if (issueCourseIds && issueCourseIds.length) {
      planItemIds = issueCourseIds
    } else {
      const issueCourseList = this.learningTypeModelCopy.issue.issueConfigList.find(
        (el) => el.id === issueId
      )?.issueCourseList
      if (issueCourseList && issueCourseList.length) {
        planItemIds = issueCourseList.map((el) => el.id)
      }
    }
    planItemIds = DataResolve.unique(planItemIds.filter((el) => !el.startsWith(IssueCourseDetail.customPrefixId)))
    if (planItemIds.length) {
      const { status, data } = await MsTeachingplanV1.checkPlanItem({
        schemeId,
        issueId,
        planItemIds
      })
      if (status && status.isSuccess() && data) {
        data.forEach((item) => {
          if (item.code === 'C600') {
            result.existStarted.push(item.planItemId)
          }
          if (item.code === 'C601') {
            result.existCheckInRecord.push(item.planItemId)
          }
          if (item.code === 'C602') {
            result.existTimeOverlap.push(item.planItemId)
          }
        })
      }
    }
    return result
  }

  /**
   * 查询期别课程是否有签到记录
   * @param issueCourseId 期别课程ID
   * 200 正常
   * C600 课程是否已上课
   * C601 是否有打卡记录
   * C602 课程授课时间重叠
   */
  async queryIssueCourseHasSignRecord(issueCourseId: string) {
    return await MsTeachingplanV1.hasSignRecord(issueCourseId)
  }

  // endregion
}
export default MutationCreateTrainClassCommodity
