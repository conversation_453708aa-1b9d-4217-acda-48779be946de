<template>
  <el-card shadow="never" class="m-card f-mb15" v-loading="uiConfig.loading.pageLoading">
    <!--条件查询-->
    <hb-search-wrapper @reset="resetQueryParam" class="m-query is-border-bottom">
      <el-form-item label="年度">
        <biz-year-select v-model="localSkuProperty.year" placeholder="请选择培训年度"></biz-year-select>
      </el-form-item>
      <el-form-item label="地区">
        <biz-national-region
          v-model="localSkuProperty.region"
          :check-strictly="true"
          placeholder="请选择地区"
        ></biz-national-region>
      </el-form-item>
      <el-form-item label="行业">
        <biz-industry-select
          v-model="localSkuProperty.industry"
          @clearIndustrySelect="handleClearIndustrySelect"
          @industryPropertyId="handleIndustryPropertyId"
          @industryInfos="handleIndustryInfos"
          ref="industrySelect"
        ></biz-industry-select>
      </el-form-item>
      <el-form-item label="培训形式">
        <biz-training-mode-select v-model="localSkuProperty.trainingMode"></biz-training-mode-select>
      </el-form-item>
      <template v-if="localSkuProperty.industry && localSkuProperty.industry === envConfig.teacherIndustryId">
        <el-form-item label="学段">
          <biz-study-period
            v-model="localSkuProperty.studyPeriodId"
            :industry-id="localSkuProperty.industry"
            :industry-property-id="industryPropertyId"
            @updateStudyPeriod="updateStudyPeriod"
            @clearSubject="clearSubject"
          ></biz-study-period>
        </el-form-item>
        <el-form-item label="学科">
          <biz-subject
            v-model="localSkuProperty.subjectId"
            :industry-property-id="industryPropertyId"
            :studyPeriodId="localSkuProperty.studyPeriodId"
            @updateSubject="updateSubject"
          ></biz-subject>
        </el-form-item>
      </template>
      <el-form-item
        label="技术等级"
        v-if="skuVisible.jobLevel && localSkuProperty.industry && localSkuProperty.industry === envConfig.workServiceId"
      >
        <biz-technical-grade-select
          v-model="localSkuProperty.jobLevel"
          :industry-id="localSkuProperty.industry"
          :industry-property-id="industryPropertyId"
        ></biz-technical-grade-select>
      </el-form-item>
      <el-form-item label="科目类型" v-if="skuVisible.subjectType && localSkuProperty.industry">
        <biz-accounttype-select
          v-model="localSkuProperty.subjectType"
          :industry-property-id="industryPropertyId"
          :industryId="localSkuProperty.industry"
        >
        </biz-accounttype-select>
      </el-form-item>
      <!-- 药师行业显示 -->
      <el-form-item label="执业类别" v-if="skuVisible.practicingCategory && localSkuProperty.industry">
        <biz-practicing-category-cascader
          v-model="localSkuProperty.pharmacistIndustry"
          :industryId="localSkuProperty.industry"
        ></biz-practicing-category-cascader>
      </el-form-item>
      <el-form-item
        label="培训专业"
        v-if="
          skuVisible.trainingCategory &&
          localSkuProperty.industry &&
          envConfig.societyIndustryId &&
          localSkuProperty.industry === envConfig.societyIndustryId
        "
      >
        <biz-major-cascader
          v-model="localSkuProperty.societyTrainingMajor"
          :industry-property-id="industryPropertyId"
          :industryId="localSkuProperty.industry"
        />
      </el-form-item>
      <el-form-item
        label="培训类别"
        v-if="
          skuVisible.trainingCategory &&
          localSkuProperty.industry &&
          (localSkuProperty.industry === envConfig.constructionIndustryId ||
            localSkuProperty.industry === envConfig.occupationalHealthId)
        "
      >
        <biz-training-category-select
          v-model="localSkuProperty.trainingCategory"
          :industry-property-id="industryPropertyId"
          @updateTrainingCategory="handleUpdateTrainingCategory"
          :industryId="localSkuProperty.industry"
        />
      </el-form-item>
      <el-form-item
        label="培训专业"
        v-if="
          skuVisible.trainingCategory &&
          localSkuProperty.industry &&
          envConfig.constructionIndustryId &&
          localSkuProperty.industry === envConfig.constructionIndustryId
        "
      >
        <biz-major-select
          v-model="localSkuProperty.constructionTrainingMajor"
          :industry-property-id="industryPropertyId"
          :training-category-id="trainingCategoryId"
          :industryId="localSkuProperty.industry"
        />
      </el-form-item>
      <el-form-item
        label="培训对象"
        v-if="
          skuVisible.trainingCategory &&
          localSkuProperty.industry &&
          localSkuProperty.industry === envConfig.occupationalHealthId
        "
      >
        <biz-training-object-select
          v-model="localSkuProperty.trainingObject"
          placeholder="请选择培训对象"
          :industry-property-id="industryPropertyId"
          :industry-id="localSkuProperty.industry"
          @updateTrainingCategory="updateTrainingCategory"
        />
      </el-form-item>
      <el-form-item
        label="岗位类别"
        v-if="
          skuVisible.trainingCategory &&
          localSkuProperty.industry &&
          localSkuProperty.industry === envConfig.occupationalHealthId
        "
      >
        <biz-obj-category-select
          v-model="localSkuProperty.positionCategory"
          placeholder="请选择岗位类别"
          :industry-property-id="industryPropertyId"
          :industryId="localSkuProperty.industry"
          :training-object-id="localSkuProperty.trainingObject"
        />
      </el-form-item>
      <el-form-item label="培训方案类型">
        <biz-scheme-type v-model="schemeTypeInfo"></biz-scheme-type>
      </el-form-item>
      <el-form-item label="培训方案名称">
        <el-input clearable placeholder="请输入培训方案名称" v-model="schemeName" />
      </el-form-item>
      <el-form-item label="销售状态">
        <el-select clearable @clear="onShelveStatus = undefined" placeholder="请选择销售状态" v-model="onShelveStatus">
          <el-option
            v-for="item in onShelveStatusOptions"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否归属专题">
        <el-select clearable @clear="isSpecial = null" placeholder="请选择是否归属专题" v-model="isSpecial">
          <el-option :value="true" label="是">是</el-option>
          <el-option :value="false" label="否">否</el-option>
        </el-select>
      </el-form-item>
      <template slot="actions">
        <el-button type="primary" @click="searchBase">查询</el-button>
        <el-button type="primary" @click="doExportList" :loading="exportLoading">导出</el-button>
      </template>
    </hb-search-wrapper>
    <!--表格-->
    <el-table
      stripe
      :data="trainSchemeList"
      class="m-table"
      v-loading="trainSchemeQuery.loading"
      @sort-change="handleSortChange"
      ref="elTableRef"
      :max-height="'500px'"
    >
      <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
      <el-table-column label="培训方案名称" min-width="300">
        <template slot-scope="scope">
          <p>
            <biz-show-scheme-type
              :scheme-type="scope.row.schemeType"
              :training-mode="scope.row.skuValueNameProperty.trainingMode.skuPropertyValueId"
            ></biz-show-scheme-type>
            <el-tooltip class="item" effect="dark" :content="scope.row.commodityBasicData.saleTitle" placement="top">
              <span>{{ scope.row.commodityBasicData.saleTitle }}</span>
            </el-tooltip>
          </p>
          <el-tooltip
            class="item"
            effect="dark"
            placement="bottom"
            popper-class="m-tooltip"
            v-if="getSchemeIsProcessing(scope.row)"
          >
            <el-tag type="warning">处理中<i class="el-icon-info icon"></i></el-tag>
            <div slot="content">系统正在处理上一次修改方案的重算任务，暂时不支持再次修改</div>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" placement="bottom" popper-class="m-tooltip" v-if="scope.row.hasError">
            <el-tag type="danger">异常<i class="el-icon-info icon"></i></el-tag>
            <div slot="content">异常原因：{{ scope.row.errorMsg }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="培训属性" min-width="240">
        <template slot-scope="scope">
          <p v-if="getSkuPropertyName(scope.row, 'industry')">行业：{{ getSkuPropertyName(scope.row, 'industry') }}</p>
          <p v-if="getSkuPropertyName(scope.row, 'region')">地区：{{ getSkuPropertyName(scope.row, 'region') }}</p>
          <p v-if="getSkuPropertyName(scope.row, 'jobLevel')">
            技术等级：{{ getSkuPropertyName(scope.row, 'jobLevel') }}
          </p>
          <p v-if="getSkuPropertyName(scope.row, 'subjectType')">
            科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
          </p>
          <p
            v-if="
              getSkuPropertyName(scope.row, 'certificatesType') && getSkuPropertyName(scope.row, 'practitionerCategory')
            "
          >
            执业类别：{{ getSkuPropertyName(scope.row, 'certificatesType') }}/{{
              getSkuPropertyName(scope.row, 'practitionerCategory')
            }}
          </p>
          <p v-if="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
            培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
          </p>
          <p v-if="getSkuPropertyName(scope.row, 'trainingMajor')">
            培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
          </p>
          <p v-if="getSkuPropertyName(scope.row, 'trainingObject')">
            培训对象：{{ getSkuPropertyName(scope.row, 'trainingObject') }}
          </p>
          <p v-if="getSkuPropertyName(scope.row, 'positionCategory')">
            岗位类别：{{ getSkuPropertyName(scope.row, 'positionCategory') }}
          </p>
          <p v-if="getSkuPropertyName(scope.row, 'year')">培训年度：{{ getSkuPropertyName(scope.row, 'year') }}</p>
          <p v-if="getSkuPropertyName(scope.row, 'learningPhase')">
            学段：{{ getSkuPropertyName(scope.row, 'learningPhase') }}
          </p>
          <p v-if="getSkuPropertyName(scope.row, 'discipline')">
            学科：{{ getSkuPropertyName(scope.row, 'discipline') }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="销售状态" min-width="140">
        <template slot-scope="scope">
          <div v-if="scope.row.onShelve.shelveStatus === 0">
            <el-badge is-dot type="info" class="badge-status">已下架</el-badge>
          </div>
          <div v-if="scope.row.onShelve.shelveStatus === 1">
            <el-badge is-dot type="success" class="badge-status">已上架</el-badge>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="归属专题数" min-width="140" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="openSpecialDialog(scope.row.commoditySkuId)" size="mini"
            >{{ scope.row.trainingChannels ? scope.row.trainingChannels.length : 0 }}
          </el-button>
        </template>
      </el-table-column>
      <!--      <el-table-column label="学习起止时间" min-width="220">-->
      <!--        <template slot-scope="scope">-->
      <!--          <p v-if="!scope.row.isLongTerm">起始：{{ scope.row.trainingBeginDate }}</p>-->
      <!--          <p v-if="!scope.row.isLongTerm">结束：{{ scope.row.trainingEndDate }}</p>-->
      <!--          <p v-if="scope.row.isLongTerm">长期有效</p>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <!--      <el-table-column label="报名起止时间" min-width="220">-->
      <!--        <template slot-scope="scope">-->
      <!--          <p v-if="scope.row.registerBeginDate || scope.row.registerEndDate">-->
      <!--            起始：{{ scope.row.registerBeginDate ? scope.row.registerBeginDate : '&#45;&#45;' }}-->
      <!--          </p>-->
      <!--          <p v-if="scope.row.registerBeginDate || scope.row.registerEndDate">-->
      <!--            结束：{{ scope.row.registerEndDate ? scope.row.registerEndDate : '&#45;&#45;' }}-->
      <!--          </p>-->
      <!--          <p v-if="!scope.row.registerBeginDate && !scope.row.registerEndDate">暂不开启</p>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <!--      <el-table-column label="最新修改时间" min-width="180" sortable="custom" prop="commodityLastEditTime">-->
      <!--      </el-table-column>-->
      <!--      <el-table-column-->
      <!--        v-if="schoolConfigFlag"-->
      <!--        key="needDataSync"-->
      <!--        label="成果是否同步"-->
      <!--        min-width="180"-->
      <!--        prop="needDataSync"-->
      <!--        align="center"-->
      <!--      >-->
      <!--        <template slot-scope="scope">-->
      <!--          {{ isNeedDataSync(scope.row.needDataSync) }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="操作" width="200" align="center">
        <template slot-scope="scope">
          <template desc="修改" actions="@hbfe/jxjy-admin-scheme/src/modify.vue,editScheme">
            <el-button type="text" size="mini" @click="editScheme(scope.row)" :disabled="!scope.row.isNewScheme"
              >编辑方案
            </el-button>
          </template>
          <template v-if="scope.row.issueInfoResponse.issueCount" desc="查看期别" actions="@BizSchemeIssueList">
            <el-button type="text" size="mini" @click="showIssueList(scope.row)" :disabled="!scope.row.isNewScheme"
              >查看期别（{{ scope.row.issueInfoResponse.issueCount }}）
            </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="trainSchemePage" v-bind="trainSchemePage"></hb-pagination>
    <scheme-special-list ref="specialList" @pageScheme="pageScheme"></scheme-special-list>
    <el-dialog :visible.sync="exportDialog" width="400px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务管理-培训方案</p>
        </div>
      </div>
      <div slot="footer">
        <el-button type="info" @click="exportDialog = false">暂 不</el-button>
        <el-button type="primary" @click="toDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
    <biz-scheme-issue-list ref="issueDrawer"></biz-scheme-issue-list>
  </el-card>
</template>

<script lang="ts">
  import {
    CommoditySkuRequest,
    CommoditySkuSortField,
    CommoditySkuSortRequest,
    OnShelveRequest,
    RegionSkuPropertyRequest,
    RegionSkuPropertySearchRequest,
    SchemeRequest,
    SkuPropertyRequest,
    SortPolicy
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import getServicerIsDocking from '@api/service/management/online-school-config/portal/query/QueryPortal'
  import QueryOrder from '@api/service/management/trade/single/order/query/QueryOrder'
  import MutationTrainClassCommodityClass from '@api/service/management/train-class/mutation/MutationTrainClassCommodityClass'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { Query, UiPage } from '@hbfe/common'
  import BizAccounttypeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-accounttype-select.vue'
  import BizIndustrySelect from '@hbfe/jxjy-admin-components/src/biz/biz-industry-select.vue'
  import BizMajorCascader from '@hbfe/jxjy-admin-components/src/biz/biz-major-cascader.vue'
  import BizMajorSelect from '@hbfe/jxjy-admin-components/src/biz/biz-major-select.vue'
  import BizNationalRegion from '@hbfe/jxjy-admin-components/src/biz/biz-national-region.vue'
  import BizTechnicalGradeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-technical-grade-select.vue'
  import BizTrainingCategorySelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-category-select.vue'
  import BizYearSelect from '@hbfe/jxjy-admin-components/src/biz/biz-year-select.vue'
  import { FakeOperateTypeEnum } from '@hbfe/jxjy-admin-scheme/src/models/FakeOperateTypeEnum'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-scheme/src/models/SchemeSkuProperty'
  import { TableSort } from '@hbfe/jxjy-admin-scheme/src/models/TableSort'
  import UITrainClassCommodityDetail from '@hbfe/jxjy-admin-scheme/src/models/UITrainClassCommodityDetail'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import SchemeSpecialList from '@hbfe/jxjy-admin-specialTopics/src/manage/components/scheme-special-list.vue'
  import { ElTable } from 'element-ui/types/table'
  import { cloneDeep, isBoolean } from 'lodash'
  import { bind, debounce } from 'lodash-decorators'
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import BizShowSchemeType from '@hbfe/jxjy-admin-components/src/biz/biz-show-scheme-type.vue'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import BizTrainingModeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-mode-select.vue'
  import BizSchemeIssueList from '@hbfe/jxjy-admin-components/src/biz/biz-scheme-issue-list.vue'
  import {
    CommoditySkuSortField as CommoditySkuSortFieldV2,
    CommoditySkuSortRequest as CommoditySkuSortRequestv2
  } from '@api/platform-gateway/jxjy-data-export-gateway-backstage'

  @Component({
    components: {
      BizTrainingModeSelect,
      BizShowSchemeType,
      SchemeSpecialList,
      BizTechnicalGradeSelect,
      BizMajorCascader,
      BizMajorSelect,
      BizTrainingCategorySelect,
      BizAccounttypeSelect,
      BizIndustrySelect,
      BizNationalRegion,
      BizYearSelect,
      BizSchemeIssueList
    }
  })
  export default class extends Vue {
    @Ref('elTableRef') elTableRef: ElTable
    @Ref('specialList') specialList: SchemeSpecialList
    @Ref('industrySelect') industrySelect: BizIndustrySelect
    @Ref('issueDrawer') issueDrawer: BizSchemeIssueList
    FakeOperateTypeEnum = FakeOperateTypeEnum
    // 查询参数 - 培训方案
    trainSchemeQueryParam: CommoditySkuRequest
    // 分页参数 - 培训方案
    trainSchemePage: UiPage
    // 分页加载参数 - 培训方案
    trainSchemeQuery: Query = new Query()
    //导出成功弹窗
    exportDialog = false
    // 培训方案列表
    trainSchemeList: Array<UITrainClassCommodityDetail> = new Array<UITrainClassCommodityDetail>()
    // 培训方案业务状态层入口
    QueryTrainClassCommodityList = new QueryTrainClassCommodityList()
    // 排序策略
    sortPolicy: Array<CommoditySkuSortRequestv2> = new Array<CommoditySkuSortRequestv2>()

    QueryOrder = new QueryOrder()

    // 培训方案名称
    schemeName = ''
    isSpecial: boolean = null
    /**
     * 是否在当前页面，否则停止轮询
     */
    isActivated = true
    /**
     * 定时器创建，修改查询定时器
     */
    timer: any
    /**
     * loading遮罩层
     */
    loading: any
    /**
     * ui控制组
     */
    uiConfig = {
      loading: {
        pageLoading: false
      },
      exportDialog: false
    }

    /**
     * 导出列表数据按钮loading加载
     */
    exportLoading = false

    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuProperty = {
      /**
       * 年度
       */
      year: '',
      /**
       * 地区
       */
      region: [] as string[],
      /**
       * 行业
       */
      industry: '',
      /**
       * 科目类型
       */
      subjectType: '',
      /**
       * 培训类别
       */
      trainingCategory: '',
      /**
       * 培训专业 - 建设行业
       */
      constructionTrainingMajor: '',
      /**
       * 培训专业 - 人社行业
       */
      societyTrainingMajor: [] as string[],
      /**
       * 技术等级 - 工勤行业
       **/
      jobLevel: '',
      /**
       * 培训对象
       */
      trainingObject: '',
      /**
       * 岗位类别
       */
      positionCategory: '',
      /**
       * 学段id
       */
      studyPeriodId: '',
      /**
       * 学科id
       */
      subjectId: '',
      /**
       * 药师id
       */
      pharmacistIndustry: [] as string[],
      /**
       * 培训形式
       */
      trainingMode: TrainingModeEnum.online
    } as SchemeSkuProperty

    /**
     * 培训方案类型
     */
    schemeTypeInfo: Array<string> = new Array<string>()

    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''

    /**
     * 培训类别Id
     */
    trainingCategoryId = ''

    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: true,
      // 培训类别
      trainingCategory: true,
      // 技术等级
      jobLevel: true,
      // 培训对象
      trainingObject: true,
      //   岗位类别
      positionCategory: true,
      //  执业类别
      practicingCategory: true
    }

    /**
     * 当前网校信息
     */
    envConfig = {
      // 工勤行业
      workServiceId: '',
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 职业卫生行业Id
      occupationalHealthId: '',
      //教师行业id
      teacherIndustryId: '',
      //药师行业id
      pharmacistIndustryId: ''
    }

    // 长期有效 - 开始时间
    defaultBeginDate = CreateSchemeUtils.defaultBeginDate
    // 长期有效 - 结束时间
    defaultEndDate = CreateSchemeUtils.defaultEndDate

    MutationTrainClassCommodityClass = new MutationTrainClassCommodityClass()
    /**
     * 报名状态
     */
    onShelveStatus: number = null
    // 网校对接与否
    schoolConfigFlag = true
    onShelveStatusOptions = [
      { id: 1, label: '已上架' },
      { id: 0, label: '已下架' }
    ]

    @Watch('localSkuProperty', {
      immediate: true,
      deep: true
    })
    localSkuPropertyChange(val: any) {
      console.log('localSkuProperty', cloneDeep(val))
    }

    // 是否是专题管理员
    isZtGly = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)

    constructor() {
      super()
      this.initQueryParam()
      this.trainSchemePage = new UiPage(this.pageScheme, this.pageScheme)
    }

    /**
     * 初始化查询参数
     */
    initQueryParam() {
      this.trainSchemeQueryParam = new CommoditySkuRequest()
      this.trainSchemeQueryParam.onShelveRequest = new OnShelveRequest()
      this.trainSchemeQueryParam.schemeRequest = new SchemeRequest()
      this.trainSchemeQueryParam.schemeRequest.schemeName = ''
      this.trainSchemeQueryParam.skuPropertyRequest = new SkuPropertyRequest()
      this.trainSchemeQueryParam.skuPropertyRequest.year = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      this.trainSchemeQueryParam.skuPropertyRequest.regionSkuPropertySearch.region =
        new Array<RegionSkuPropertyRequest>()
      this.trainSchemeQueryParam.skuPropertyRequest.industry = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.subjectType = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingProfessional = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingObject = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.positionCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.jobLevel = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.learningPhase = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.discipline = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.certificatesType = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.practitionerCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingForm = new Array<string>()
      this.trainSchemeQueryParam.isDisabledResourceShow = true
      this.sortPolicy = new Array<CommoditySkuSortRequestv2>()
      this.schemeTypeInfo = new Array<string>()
    }

    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.studyPeriodId = ''
      this.initQueryParam()
      this.localSkuProperty = new SchemeSkuProperty()
      this.onShelveStatus = null
      this.schemeName = ''
      this.sortPolicy = new Array<CommoditySkuSortRequestv2>()
      this.isSpecial = null
      // 移除表格排序
      this.elTableRef.clearSort()
      await this.searchBase()
    }

    /**
     * 加载第一页
     */
    async searchBase() {
      this.trainSchemePage.pageNo = 1
      await this.pageScheme()
    }

    /**
     * 分页查询
     */
    async pageScheme() {
      this.trainSchemeQuery.loading = true
      try {
        this.getPageQueryParams()
        let trainSchemeList
        if (this.isZtGly) {
          trainSchemeList = await this.getTrainSchemeListZt()
        } else {
          trainSchemeList = await this.getTrainSchemeList()
        }
        //  const trainSchemeList = await this.getTrainSchemeList()
        this.trainSchemeList = new Array<UITrainClassCommodityDetail>()
        trainSchemeList?.map((el: UITrainClassCommodityDetail) => {
          const item = new UITrainClassCommodityDetail()
          Object.assign(item, el)
          item.isLongTerm = false
          item.isSocietyIndustry = false
          if (item.trainingEndDate === this.defaultEndDate && item.trainingBeginDate === this.defaultBeginDate) {
            item.isLongTerm = true
          }
          if (item.skuValueNameProperty?.industry?.skuPropertyName === '人社行业') {
            item.isSocietyIndustry = true
          }
          this.trainSchemeList.push(item)
        })
        console.log('trainSchemeList', this.trainSchemeList)
      } catch (e) {
        console.log('获取培训方案分页列表失败！', e)
      } finally {
        this.trainSchemeQuery.loading = false
      }
      this.$nextTick(() => {
        this.elTableRef && this.elTableRef.doLayout()
      })
    }

    /**
     * 处理列表查询参数
     */
    getPageQueryParams() {
      this.getLocalSkuProperty()
      this.trainSchemeQueryParam.onShelveRequest.onShelveStatus =
        this.onShelveStatus || this.onShelveStatus === 0 ? this.onShelveStatus : undefined
      this.trainSchemeQueryParam.schemeRequest.schemeName = this.schemeName || undefined
      this.trainSchemeQueryParam.existTrainingChannel = this.isSpecial ?? undefined
      this.configureTrainSchemeQueryParam()
    }

    /**
     * 专题获取培训班列表
     */
    async getTrainSchemeListZt() {
      if (this.sortPolicy.length) {
        return (
          (await this.QueryTrainClassCommodityList.queryTrainClassCommodityListInTrainingChannel(
            this.trainSchemePage,
            this.trainSchemeQueryParam,
            this.sortPolicy
          )) || ([] as TrainClassCommodityVo[])
        )
      } else {
        return (
          (await this.QueryTrainClassCommodityList.queryTrainClassCommodityListInTrainingChannel(
            this.trainSchemePage,
            this.trainSchemeQueryParam
          )) || ([] as TrainClassCommodityVo[])
        )
      }
    }

    /**
     * 网校获取培训班列表
     */
    async getTrainSchemeList() {
      if (this.sortPolicy.length) {
        return (
          (await this.QueryTrainClassCommodityList.queryTrainClassCommodityList(
            this.trainSchemePage,
            this.trainSchemeQueryParam,
            this.sortPolicy
          )) || ([] as TrainClassCommodityVo[])
        )
      } else {
        return (
          (await this.QueryTrainClassCommodityList.queryTrainClassCommodityList(
            this.trainSchemePage,
            this.trainSchemeQueryParam
          )) || ([] as TrainClassCommodityVo[])
        )
      }
    }

    /**
     * 配置查询参数
     */
    configureTrainSchemeQueryParam() {
      // 处理培训方案类型
      if (!this.schemeTypeInfo.length) {
        this.trainSchemeQueryParam.schemeRequest.schemeType = undefined
      }
      const schemeType = this.schemeTypeInfo[this.schemeTypeInfo.length - 1]
      // if (schemeType === 'chooseCourseLearning' || schemeType === 'autonomousCourseLearning') {
      this.trainSchemeQueryParam.schemeRequest.schemeType = schemeType
      // } else {
      //   this.trainSchemeQueryParam.schemeRequest.schemeType = undefined
      // }
    }

    /**
     * 获取本地sku选项
     */
    getLocalSkuProperty() {
      const skuProperties = cloneDeep(this.trainSchemeQueryParam.skuPropertyRequest)
      skuProperties.year = !this.localSkuProperty.year ? ([] as string[]) : [this.localSkuProperty.year]
      skuProperties.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
      const localRegion = cloneDeep(this.localSkuProperty.region)
      if (Array.isArray(localRegion) && localRegion.length) {
        const option = new RegionSkuPropertyRequest()
        option.province = localRegion.length >= 1 ? localRegion[0] : undefined
        option.city = localRegion.length >= 2 ? localRegion[1] : undefined
        option.county = localRegion.length >= 3 ? localRegion[2] : undefined
        skuProperties.regionSkuPropertySearch.region.push(option)
        skuProperties.regionSkuPropertySearch.regionSearchType = 1
      } else {
        skuProperties.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      }
      skuProperties.industry = !this.localSkuProperty.industry ? ([] as string[]) : [this.localSkuProperty.industry]
      skuProperties.jobLevel = !this.localSkuProperty.jobLevel ? ([] as string[]) : [this.localSkuProperty.jobLevel]
      skuProperties.subjectType = !this.localSkuProperty.subjectType
        ? ([] as string[])
        : [this.localSkuProperty.subjectType]
      skuProperties.trainingCategory = !this.localSkuProperty.trainingCategory
        ? ([] as string[])
        : [this.localSkuProperty.trainingCategory]
      skuProperties.trainingProfessional = this.getTrainingProfessional()
      skuProperties.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      skuProperties.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      skuProperties.learningPhase = !this.localSkuProperty.studyPeriodId
        ? ([] as string[])
        : [this.localSkuProperty.studyPeriodId]
      skuProperties.discipline = !this.localSkuProperty.subjectId ? ([] as string[]) : [this.localSkuProperty.subjectId]
      skuProperties.certificatesType = !this.localSkuProperty.pharmacistIndustry
        ? ([] as string[])
        : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
        ? [this.localSkuProperty.pharmacistIndustry[0]]
        : []
      skuProperties.practitionerCategory = !this.localSkuProperty.pharmacistIndustry
        ? ([] as string[])
        : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
        ? [this.localSkuProperty.pharmacistIndustry[1]]
        : []
      skuProperties.trainingForm = !this.localSkuProperty.trainingMode
        ? ([] as string[])
        : [this.localSkuProperty.trainingMode]

      this.trainSchemeQueryParam.skuPropertyRequest = cloneDeep(skuProperties)
      //   console.log('selectedSkuProperties', JSON.stringify(this.trainSchemeQueryParam.skuPropertyRequest))
    }

    /**
     * 页面初始化
     */
    async created() {
      console.log('初始化查询')
      this.isActivated = true
      this.schoolConfigFlag = await getServicerIsDocking.getServicerIsDocking()
      await QueryIndustry.queryIndustry()
      if (QueryIndustry.industryList.length == 1) {
        this.localSkuProperty.industry = QueryIndustry.industryList[0].id
      }
      await this.searchBase()
    }

    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      // console.log('envConfig', this.envConfig)
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人设行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }

    /**
     * 修改培训方案
     */
    async editScheme(row: UITrainClassCommodityDetail) {
      if (this.getSchemeIsProcessing(row) || row.hasError)
        return this.$confirm('系统正在处理上一次修改方案的重算任务，处理成功后可再次修改，请耐心等待。', '系统提醒', {
          confirmButtonText: '确定',
          type: 'warning',
          showCancelButton: false
        })
      if (row.isResourceEnabled === false)
        return this.$confirm('方案正在处理中，处理成功后可再次修改。', '系统提醒', {
          confirmButtonText: '确定',
          type: 'warning',
          showCancelButton: false
        })
      const res = await this.QueryTrainClassCommodityList.getIsProcessedByTransaction([row.schemeId])
      if (res?.length && !res[0].isProcessed) {
        return this.$confirm('该方案暂不支持修改', '系统提醒', {
          confirmButtonText: '确定',
          type: 'warning',
          showCancelButton: false
        })
      }
      await this.$router.push(`/training/scheme/modify/${row.commoditySkuId}?source=topicScheme`)
    }

    // 导出列表数据
    @bind
    @debounce(200)
    async doExportList() {
      //   console.log('导出列表数据')
      //   loading效果开启
      this.exportLoading = true
      this.getPageQueryParams()
      const res = await this.QueryTrainClassCommodityList.exportSchemeSubjectInServicer(
        this.trainSchemeQueryParam,
        this.sortPolicy
      )
      //   loading效果关闭
      this.exportLoading = false
      if (!res?.status?.isSuccess()) {
        this.$message.error('导出列表请求失败！')
      } else {
        this.exportDialog = true
      }
    }

    // 前往导出任务查看页面
    @bind
    @debounce(200)
    toDownloadPage() {
      this.exportDialog = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: {
          type: 'exportCommodity'
        }
      })
    }

    /**
     * 获取培训方案类型
     */
    getSchemeType(row: UITrainClassCommodityDetail) {
      return SchemeType.getSchemeType(row.schemeType, true)
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: UITrainClassCommodityDetail, type: string): string {
      if (row.skuValueNameProperty[type]?.skuPropertyName) {
        const value = row.skuValueNameProperty[type].skuPropertyName
        const valuesArr = value.split('/'),
          lastIndex = valuesArr.length - 1
        return type === 'trainingMajor' && !row.isSocietyIndustry ? valuesArr[lastIndex] : value
      }
      return ''
    }

    /**
     * 处理排序 - 最后修改时间
     */
    async handleSortChange(column: any) {
      console.log('start sort', column.prop, column.order)
      if (column?.prop === 'commodityLastEditTime') {
        if (!column.order) {
          // 按发布时间降序排
          console.log('默认按发布时间降序排')
          this.sortPolicy = new Array<CommoditySkuSortRequestv2>()
        }
        if (column.order === TableSort.DESC) {
          // 按最后修改时间降序排
          console.log('按最后修改时间降序排')
          this.sortPolicy = new Array<CommoditySkuSortRequestv2>()
          const option = new CommoditySkuSortRequestv2()
          option.sortField = CommoditySkuSortFieldV2.LAST_EDIT_TIME
          option.policy = SortPolicy.DESC
          this.sortPolicy.push(option)
        }
        if (column.order === TableSort.ASC) {
          // 按最后修改时间升序排
          console.log('按最后修改时间升序排')
          this.sortPolicy = new Array<CommoditySkuSortRequestv2>()
          const option = new CommoditySkuSortRequestv2()
          option.sortField = CommoditySkuSortFieldV2.LAST_EDIT_TIME
          option.policy = SortPolicy.ASC
          this.sortPolicy.push(option)
        }
      }
      await this.searchBase()
    }

    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      this.localSkuProperty.jobLevel = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.studyPeriodId = ''
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.pharmacistIndustry = [] as string[]
    }

    updateTrainingCategory(val: string) {
      if (val) {
        this.localSkuProperty.positionCategory = ''
      }
    }

    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )

      const configSubjectType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.SUBJECT_TYPE)
      const configTrainingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.TRAINING_CATEGORY
      )
      const jobLevel = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.JOB_LEVEL)
      const trainingObject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.TRAINING_OBJECT)
      const positionCategory = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.POSITION_CATEGORY)
      const practicingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.PRACTITIONER_CATEGORY
      )
      this.skuVisible.subjectType = configSubjectType > -1
      this.skuVisible.trainingCategory = configTrainingCategory > -1
      this.skuVisible.jobLevel = jobLevel > -1
      this.skuVisible.trainingObject = trainingObject > -1
      this.skuVisible.positionCategory = positionCategory > -1
      this.skuVisible.practicingCategory = practicingCategory > -1
    }

    /**
     * 响应组件行业Id集合传参
     */
    async handleIndustryInfos(values: any) {
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.occupationalHealthId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
      this.envConfig.pharmacistIndustryId = values.pharmacistIndustryId || ''

      // 仅当单个行业，需要默认选中
      //   if (this.industrySelect?.industryOptions?.length === 1) {
      //     this.localSkuProperty.industry = this.industrySelect.industryOptions[0].id
      //   }
    }

    //选择学段
    updateStudyPeriod(val: string) {
      this.localSkuProperty.studyPeriodId = val
      this.localSkuProperty.subjectId = ''
      this.trainSchemeQueryParam.skuPropertyRequest.discipline = []
    }

    //选择学科
    updateSubject(val: string) {
      this.localSkuProperty.subjectId = val
    }

    /**
     * 是否处理中
     */
    getSchemeIsProcessing(item: UITrainClassCommodityDetail) {
      return ((item?.lastTransactionStep && item?.lastTransactionStep !== 5) || item?.recalculating) && !item?.hasError
    }

    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
    }

    clearSubject() {
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.studyPeriodId = ''
      this.trainSchemeQueryParam.skuPropertyRequest.discipline = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.learningPhase = new Array<string>()
    }

    isNeedDataSync(needDataSync: boolean) {
      if (!needDataSync && isBoolean(needDataSync)) {
        return '不同步'
      } else {
        // 旧数据默认显示同步
        return '同步'
      }
    }

    openSpecialDialog(commodityId: string) {
      this.specialList.open(commodityId)
    }

    /**
     * 查看期别列表
     */
    showIssueList(row: UITrainClassCommodityDetail) {
      this.issueDrawer.open(row.commoditySkuId)
    }
  }
</script>

<style scoped lang="scss">
  .m-table {
    width: 100%;
    color: #444;
    height: auto;
  }
</style>
