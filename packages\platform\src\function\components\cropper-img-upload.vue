<template>
  <div :class="{ 'm-upload-item': isUploadItem }">
    <el-upload
      action="#"
      ref="uploadRef"
      list-type="picture-card"
      :auto-upload="false"
      :file-list="imgList"
      class="m-pic-upload"
      :class="{ hideUpload: isHideUpload, 'long-pic': isLongPic }"
    >
      <div slot="default" class="upload-placeholder" @click.stop="open" v-show="!picUrl">
        <i class="el-icon-plus"></i>
        <p class="txt">{{ buttonText }}</p>
      </div>
      <div slot="file" slot-scope="{ file }" class="img-file" v-show="picUrl">
        <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
        <div class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <i class="el-icon-zoom-in"></i>
          </span>
          <span class="el-upload-list__item-delete" @click="handleRemove(file)">
            <i class="el-icon-delete"></i>
          </span>
        </div>
      </div>
    </el-upload>
    <slot name="other"></slot>
    <slot name="tip"></slot>
    <!-- 剪裁组件弹窗 -->
    <vue-cropper
      ref="vueCropper"
      :visible.sync="cropperModel"
      :action="actionUrl"
      :headers="headersObj"
      :title="title"
      v-model="picUrl"
      :initWidth="initWidth"
      :dialogStyleOpation="dialogStyleOpation"
      class="vue-cropper"
      @onchangeImage="onchangeImage"
      :hbOpaction="{
        ratioArr: ratioArr,
        replaceOptions: {
          canMoveBox: false,
          fixedBox: true,
          centerBox: false,
          mode: mode
        },
        reminderText: reminderText,
        accept: accept
      }"
      :hasPreview="hasPreview"
    ></vue-cropper>

    <!-- 大图预览 -->
    <el-image style="width: 100px; height: 100px" :previewSrcList="previewList" v-show="false" ref="elImage">
    </el-image>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Prop, Watch } from 'vue-property-decorator'
  import VueCropper from '@hbfe-vue-components/image-cropper'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

  @Component({
    components: {
      VueCropper
    }
  })
  export default class CoverImageUpload extends Vue {
    // 传给后端的图片路径数组/ 预览数组
    imgList = new Array<any>()
    // 图片预览数组
    previewList = new Array<string>()

    // 是否隐藏上传按钮
    isHideUpload = false
    // 文件上传限制
    limit = 1
    // 请求路径
    actionUrl = ''
    // 请求头
    headersObj = {
      'App-Authentication': '',
      Authorization: ''
    }
    url: string | null = null
    cropperModel = false //裁剪弹窗

    imagePreviewDialogVisible = false
    dialogImageUrl = ''

    /**
     * 图片宽度
     */
    width = ''

    /**
     * 图片高度
     */
    height = ''

    @Prop({ type: Boolean, default: false }) full: string

    @Prop({ type: String, default: '', required: true }) value: string
    @Prop({ type: String, default: '图片设置' }) title: string
    @Prop({ type: Number, default: 150 }) initWidth: number
    @Prop({ type: Array, default: ['16:9'] }) ratioArr: string[]
    @Prop({
      type: Object,
      default: {
        width: '300px',
        height: '300px'
      }
    })
    dialogStyleOpation: object

    @Prop({ type: String, default: '只支持JPG、PNG' }) reminderText: string
    @Prop({ type: String, default: '.jpg,.png,.jpeg,.gif' }) accept: string
    @Prop({ type: String, default: '上传图片' }) buttonText: string
    @Prop({ type: Boolean, default: false }) isLongPic: boolean
    @Prop({ type: Boolean, default: false }) isUploadItem: boolean
    @Prop({ type: Boolean, default: true }) hasPreview: boolean

    /**
     * 获取默认宽高
     */
    @Prop({ type: String, default: '' }) mode: string
    get picUrl() {
      if (this.value && this.value !== '') {
        const mfsHeadReg = /^\/mfs\/\.*/
        if (mfsHeadReg.test(this.value)) {
          this.$emit('input', this.value)
          return this.value
        }
        this.$emit('input', `/mfs${this.value}`)
        return `/mfs${this.value}`
      }
      return null
    }

    set picUrl(val) {
      let url = ''
      if (val) {
        const mfsHeadReg = /^\/mfs\/\.*/
        this.imgList = new Array<any>()
        // 检测图片路径是否含有mfs
        if (mfsHeadReg.test(val)) {
          url = val?.split('/mfs/')[1]
        } else {
          url = val
        }
      }
      this.$emit('input', url)
    }

    @Watch('picUrl', {
      immediate: true,
      deep: true
    })
    picUrlChange(val: any) {
      if (val) {
        this.imgList = new Array<any>()
        this.previewList.push(val)
        this.imgList.push({ url: val })
        this.isHideUpload = this.imgList.length > 0
      }
    }
    // 大图预览ref
    @Ref('elImage') elImage: any
    // 图片上传组件
    @Ref('uploadRef') uploadRef: any
    // 图片裁剪组件
    @Ref('vueCropper') vueCropper: any

    /**
     * 判断是否使用原图
     */
    get isFull() {
      if (this.full) {
        if (this.width && this.height) {
          const [w, h] = this.ratioArr[0].split(':')
          if (w == this.width && this.height == h) {
            return true
          }
          return false
        }
        return false
      }
      return false
    }

    mounted() {
      this.headersObj.Authorization = `Mship ${this.$authentication.getAccessToken()}`
      this.headersObj['App-Authentication'] = `Basic ${process.env.VUE_APP_KEY}`
      this.headersObj['Content-Type'] = 'application/json;charset=UTF-8'
      this.actionUrl = `${ConfigCenterModule.getIngress(ingress.resource)}/auth/uploadBase64ToProtectedFile`
    }

    // 关闭裁剪弹窗
    beforeClose() {
      this.cropperModel = false
    }

    // 删除图片
    handleRemove() {
      this.imgList = new Array<any>()
      this.previewList = new Array<string>()
      this.isHideUpload = this.imgList.length > 0
      this.picUrl = undefined
    }

    // 点击预览图片
    handlePictureCardPreview(file: any) {
      this.$nextTick(() => {
        // 触发点击方法
        this.elImage.clickHandler()
      })
    }

    onchangeImage(e: any, render: any) {
      const img = new Image()
      console.log(e, 'eeee')
      img.src = render.result
      img.onload = () => {
        console.log(img.width)
        console.log(img.height)
        this.width = String(img.width)
        this.height = String(img.height)
        if (this.isFull) {
          this.vueCropper.option.full = true
        } else {
          this.vueCropper.option.full = false
        }
      }
    }

    open() {
      this.cropperModel = true
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep.hideUpload .el-upload--picture-card {
    display: none;
  }
</style>
