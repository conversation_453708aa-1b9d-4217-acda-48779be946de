<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-12-18 14:26:50
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2025-02-11 11:41:15
 * @Description: 添加12学时特殊规则
-->
<template>
  <div>
    <el-drawer
      title="添加特殊规则"
      :visible="openSpecialRulesDialog"
      @close="closeSpecialRulesDialog"
      :wrapperClosable="false"
      :close-on-press-escape="false"
      direction="rtl"
      size="900px"
      custom-class="m-drawer"
    >
      <div class="drawer-bd">
        <el-form ref="specialRuleItemForm" :model="curRuleItem" label-width="auto" class="m-form">
          <el-form-item label="每天学习时长：" required>
            <el-radio v-model="curRuleItem.timeMode" :label="TimeModeEnum.learning">按课程学习学时</el-radio>
            <el-radio v-model="curRuleItem.timeMode" :label="TimeModeEnum.physical">按课程物理时长</el-radio>
            <div class="f-mt15" v-if="curRuleItem.timeMode == TimeModeEnum.learning">
              每天课程学习最多
              <el-input-number
                :controls="false"
                :precision="1"
                :min="0"
                :max="32"
                class="input-num f-mr10 f-ml10"
                v-model="curRuleItem.everyDayLearningHours"
              />学时
              <span class="f-c9 f-ml20">
                <i class="el-icon-warning f-f16 f-mr5 f-vm"></i>一天学习时长不能超过32学时
              </span>
            </div>
            <div class="f-mt15" v-else>
              每天课程学习最多
              <el-input-number
                :controls="false"
                :precision="0"
                :min="0"
                :max="1440"
                class="input-num f-mr10 f-ml10"
                v-model="curRuleItem.everyDayLearningTime"
              />分钟<span class="f-c9 f-ml20"
                ><i class="el-icon-warning f-f16 f-mr5 f-vm"></i>
                请输入正整数，一天学习时长不能超过1440分钟
              </span>
            </div>
          </el-form-item>
          <el-form-item label="选择培训方案：">
            <el-button type="primary" class="f-mb10" @click="chooseTrainingList">选择培训方案</el-button>
            <!--表格-->
            <el-table ref="curSchemeListRef" :data="curSchemeList" max-height="500px" class="m-table" border>
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="培训方案名称" min-width="220">
                <template v-slot="{ row }">{{ row.schemeName }}</template>
              </el-table-column>
              <el-table-column label="方案属性" min-width="240">
                <template v-slot="{ row }">
                  <sku-display :sku-item="row"></sku-display>
                </template>
              </el-table-column>
              <el-table-column label="操作" min-width="100" align="center" fixed="right">
                <template v-slot="{ row }">
                  <el-button type="text" @click="deleteSchemeItem(row.schemeId)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </div>
      <div class="drawer-ft">
        <el-button @click="openSpecialRulesDialog = false">取 消</el-button>
        <el-button type="primary" @click="saveRule">保 存</el-button>
      </div>
    </el-drawer>
    <select-training-list
      ref="selectTrainingListRef"
      :info-select-list="curSchemeList"
      :basic-info="basicInfo"
      @selectList="getSelectList"
    ></select-training-list>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Ref, Prop } from 'vue-property-decorator'
  import SelectTrainingList from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/select-training-list.vue'
  import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'
  import { ElTable } from 'element-ui/types/table'
  import SkuDisplay from '@hbfe/jxjy-admin-platform/src/function/components/skuDisplay.vue'
  import SchemeSpecialRuleItem from '@api/service/management/online-learning-rule/model/SchemeSpecialRuleItem'
  import { TimeModeEnum } from '@api/service/management/online-learning-rule/enum/TimeModeEnum'
  import BasicInfo from '@api/service/management/online-learning-rule/model/BasicInfo'
  import { cloneDeep } from 'lodash'

  @Component({
    components: { SelectTrainingList, SkuDisplay }
  })
  export default class extends Vue {
    @Ref('selectTrainingListRef') selectTrainingListRef: SelectTrainingList
    @Ref('curSchemeListRef') curSchemeListRef: ElTable
    // 接收基础信息
    @Prop({
      type: BasicInfo,
      default: () => new BasicInfo()
    })
    basicInfo: BasicInfo
    /**
     * 特殊规则模型
     */
    specialRuleItem = new SchemeSpecialRuleItem()

    /**
     * 当前规则模型-存储临时数据
     */
    curRuleItem = new SchemeSpecialRuleItem()

    /**
     * 打开弹窗
     */
    openSpecialRulesDialog = false

    /**
     * 当前已选方案列表
     */
    curSchemeList = new Array<RuleSchemeItem>()

    /**
     * 时间类型枚举
     */
    TimeModeEnum = TimeModeEnum

    /**
     * 初始化
     */
    initData() {
      this.openSpecialRulesDialog = true
      this.curRuleItem = cloneDeep(this.specialRuleItem)
    }

    chooseTrainingList() {
      this.selectTrainingListRef.init()
    }

    /**
     * 保存规则
     */
    saveRule() {
      if (this.curRuleItem.timeMode != TimeModeEnum.learning && this.curRuleItem.timeMode != TimeModeEnum.physical) {
        this.$message.warning('请选择学习时长类型')
        return
      }
      if (
        (this.curRuleItem.timeMode === TimeModeEnum.learning && !this.curRuleItem.everyDayLearningHours) ||
        (this.curRuleItem.timeMode === TimeModeEnum.physical && !this.curRuleItem.everyDayLearningTime)
      ) {
        this.$message.warning('请输入每天最多学习时长')
        return
      }
      if (!this.curSchemeList.length) {
        this.$message.warning('请选择培训方案')
        return
      }
      if (!this.curRuleItem.id) {
        this.curRuleItem.id = `${Date.now().toString(36)}-${Math.random().toString(36).substring(2, 9)}`
      }
      this.curRuleItem.schemeList = this.curSchemeList
      this.$emit('selectRuleItem', this.curRuleItem)
      this.openSpecialRulesDialog = false
    }

    /**
     * 获取选中的方案列表
     */
    getSelectList(schemeList: Array<RuleSchemeItem>) {
      this.curSchemeList = schemeList
      ;(this.$refs['curSchemeListRef'] as any)?.doLayout()
    }

    // 删除方案
    deleteSchemeItem(id: string) {
      this.$confirm('确认删除对应的培训方案吗？', '提示', {
        confirmButtonText: '确定',
        type: 'warning'
      }).then(() => {
        this.curSchemeList = this.curSchemeList.filter((item) => {
          return item.schemeId != id
        })
      })
    }

    /**
     * 关闭抽屉
     */
    closeSpecialRulesDialog() {
      this.openSpecialRulesDialog = false
    }
  }
</script>
