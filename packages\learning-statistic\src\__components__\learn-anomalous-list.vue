<template>
  <div>
    <el-row :gutter="16" class="m-query f-mt20">
      <el-form :inline="true" label-width="auto">
        <el-col :span="5">
          <el-form-item label="姓名">
            <el-input v-model="queryModule.name" clearable placeholder="请输入学员姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="证件号">
            <el-input v-model="queryModule.idCard" clearable placeholder="请输入证件号" />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="手机号">
            <el-input v-model="queryModule.phone" clearable placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="培训方案">
            <el-input v-model="queryModule.schemeName" clearable placeholder="请输入培训方案" />
          </el-form-item>
        </el-col>
        <el-col :span="4" class="f-fr">
          <el-form-item class="f-tr">
            <el-button type="primary" @click="search()">查询</el-button>
            <el-button @click="reset()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <el-table
      stripe
      :data="learnAnomalousList.list"
      class="m-table"
      max-height="600px"
      ref="learnAnomalousRef"
      v-loading="loading"
    >
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="学员信息" min-width="250" fixed="left">
        <template v-slot="{ row }">
          <p>姓名：{{ row.name }}</p>
          <p>手机号：{{ row.phone }}</p>
          <p>证件号：{{ row.idCard }}</p>
        </template>
      </el-table-column>
      <el-table-column label="培训方案名称" min-width="240" v-slot="{ row }">
        <template>{{ row.schemeName }}</template>
      </el-table-column>
      <el-table-column label="属性" min-width="240">
        <template v-slot="{ row }">
          <div v-if="getSkuPropertyName(row.skuProperty, 'industry')">
            行业：{{ getSkuPropertyName(row.skuProperty, 'industry') }}
          </div>
          <div v-if="getSkuPropertyName(row.skuProperty, 'region')">
            地区：{{ getSkuPropertyName(row.skuProperty, 'region') }}
          </div>
          <div v-if="getSkuPropertyName(row.skuProperty, 'jobLevel')">
            技术等级：{{ getSkuPropertyName(row.skuProperty, 'jobLevel') }}
          </div>
          <div v-if="getSkuPropertyName(row.skuProperty, 'subjectType')">
            科目类型：{{ getSkuPropertyName(row.skuProperty, 'subjectType') }}
          </div>
          <div v-if="getSkuPropertyName(row.skuProperty, 'trainingCategory')">
            培训类别：{{ getSkuPropertyName(row.skuProperty, 'trainingCategory') }}
          </div>
          <div v-if="getSkuPropertyName(row.skuProperty, 'trainingMajor')">
            培训专业：{{ getSkuPropertyName(row.skuProperty, 'trainingMajor') }}
          </div>
          <div v-if="getSkuPropertyName(row.skuProperty, 'trainingObject')">
            培训对象：{{ getSkuPropertyName(row.skuProperty, 'trainingObject') }}
          </div>
          <div v-if="getSkuPropertyName(row.skuProperty, 'positionCategory')">
            岗位类别：{{ getSkuPropertyName(row.skuProperty, 'positionCategory') }}
          </div>
          <div v-if="getSkuPropertyName(row.skuProperty, 'learningPhase')">
            学段：{{ getSkuPropertyName(row.skuProperty, 'learningPhase') }}
          </div>
          <div v-if="getSkuPropertyName(row.skuProperty, 'discipline')">
            学科：{{ getSkuPropertyName(row.skuProperty, 'discipline') }}
          </div>
          <div v-if="getSkuPropertyName(row.skuProperty, 'year')">
            培训年度：{{ getSkuPropertyName(row.skuProperty, 'year') }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作时间" min-width="170">
        <template v-slot="{ row }">{{ row.operationTime }}</template>
      </el-table-column>
      <el-table-column label="失败原因" min-width="180">
        <template v-slot="{ row }">{{ row.failReason }}</template>
      </el-table-column>
      <el-table-column label="操作" width="140" align="center" fixed="right">
        <template v-slot="{ row }">
          <el-button type="text" size="mini" @click="goLearingRules(row)">编辑学习规则</el-button>
          <el-button type="text" size="mini" @click="recalculate(row)">重新计算</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="page" v-bind="page"> </hb-pagination>
  </div>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import LearnAnomalousList from '@api/service/management/statisticalReport/query/LearnAnomalousList'
  import LearnAnomalousItem from '@api/service/management/statisticalReport/query/vo/LearnAnomalousItem'
  import LearnAnomalousParams from '@api/service/management/statisticalReport/query/vo/LearnAnomalousParams'
  import { UiPage } from '@hbfe/common'
  import AntiSchemeItem from '@api/service/management/train-class/query/vo/AntiSchemeItem'
  import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  @Component
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }
    // 分页
    page: UiPage
    // 实例化列表模型
    learnAnomalousList = new LearnAnomalousList()
    // 查询模型
    queryModule = this.learnAnomalousList.queryParams
    // 加载
    loading = false
    async created() {
      console.log('激活选项-0-')
      this.loading = true
      await this.search()
      this.loading = false
    }

    search() {
      this.page.currentChange(1)
      //处理切换页数后行数错位问题
      ;(this.$refs['learnAnomalousRef'] as any)?.doLayout()
    }

    /**
     * 判断当前用户是否专题管理员角色类型
     */
    get isHaveZtRole() {
      return QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    }
    async doSearch() {
      this.loading = true
      let res
      if (this.isHaveZtRole){
        res = await this.learnAnomalousList.queryListTrainingChannel(this.page)
      }else {
        res = await this.learnAnomalousList.queryList(this.page)
      }


      if (res.isSuccess()) {
        console.log('查询成功')
      } else {
        this.$message.error(res.getMessage())
      }
      //处理切换页数后行数错位问题
      this.$emit('getStatistics', this.page.totalSize)
      ;(this.$refs['learnAnomalousRef'] as any)?.doLayout()
      this.loading = false
    }

    // 重置
    reset() {
      this.learnAnomalousList.queryParams = new LearnAnomalousParams()
      this.queryModule = this.learnAnomalousList.queryParams
      this.search()
    }

    // 重新计算
    async recalculate(row: LearnAnomalousItem) {
      const res = await row.reCalculate()
      if (res.isSuccess()) {
        this.$message.success('重新计算成功')
        setTimeout(() => {
          this.search()
        }, 200)
      } else {
        this.$message.error(res.getMessage())
      }
    }

    // 跳转学习规则
    goLearingRules(row: LearnAnomalousItem) {
      this.$emit('offDialog')
      this.$router.push('/basic-data/platform/function/learningRules/modify/' + row.ruleId)
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(item: SkuPropertyResponseVo, type: string): string {
      if (item[type]?.skuPropertyName) {
        return item[type].skuPropertyName
      }
      return ''
    }
  }
</script>
