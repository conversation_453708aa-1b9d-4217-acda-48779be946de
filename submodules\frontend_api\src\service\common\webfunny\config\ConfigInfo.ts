export class WebfunnyConfig {
  config = {
    dev: {
      host: 'https://webfunny.test1.59iedu.com:8443',
      projectId: 'event1088',
      projectName: '8.0通用平台',
      pointIdList: [
        { id: 278, name: '通用-智能学习' },
        { id: 262, name: '通用-阿波罗' },
        { id: 257, name: '通用-完善' },
        { id: 253, name: '通用-交易' },
        { id: 252, name: '通用-练习' },
        { id: 251, name: '通用-考试' },
        { id: 250, name: '通用--培训方案' },
        { id: 249, name: '差异化-华医网-跳转失败' },
        { id: 247, name: '通用-监管' },
        { id: 246, name: '通用--学员中心' },
        { id: 245, name: '通用-测验' },
        { id: 236, name: '通用--课程播放' },
        { id: 237, name: '华医网跳转页面h5-异常上报' },
        { id: 238, name: '华医网跳转页面-customer-异常上报' },
        { id: 239, name: '通用-网络异常' },
        { id: 240, name: 'admin-网络慢请求' },
        { id: 241, name: 'customer-网络慢请求' },
        { id: 242, name: 'h5-网络慢请求' },
        { id: 235, name: '浏览量' }
      ]
    },
    release: {
      host: 'https://webfunny.59iedu.com',
      projectId: 'event1013',
      projectName: '8.0继续教育',
      pointIdList: [
        { id: 113, name: '通用-智能学习' },
        { id: 100, name: '通用-阿波罗' },
        { id: 95, name: '通用-完善' },
        { id: 88, name: '通用-监管' },
        { id: 89, name: '通用--学员中心' },
        { id: 90, name: '通用-测验' },
        { id: 91, name: '通用-网络异常' },
        { id: 83, name: '通用-交易' },
        { id: 84, name: '通用-练习' },
        { id: 85, name: '通用-考试' },
        { id: 86, name: '通用--培训方案' },
        { id: 87, name: '差异化-华医网-跳转失败' },
        { id: 79, name: 'h5-网络慢请求' },
        { id: 73, name: '通用--课程播放' },
        { id: 74, name: '华医网跳转页面h5-异常上报' },
        { id: 75, name: '华医网跳转页面-customer-异常上报' },
        { id: 76, name: '异常请求' },
        { id: 77, name: 'admin-网络慢请求' },
        { id: 78, name: 'customer-网络慢请求' },
        { id: 71, name: '华医网跳转页面customer-上报异常' },
        { id: 62, name: '浏览量' }
      ]
    }
  }
}

//播放异常
export class ScreeningErrorPoint {
  userId?: string // 用户ID
  pointId?: number // pointId | 类型：文本 | 长度：50
  courserId?: string //课程id | 类型：文本 | 长度200
  courseName?: string //课程名称| 类型：文本 | 长度：500
  courseWareId?: string //课件id| 类型：文本 | 长度：200
  courseWareName?: string //课件名称| 类型：文本 | 长度：500
  schemeId?: string //方案id| 类型：文本 | 长度：200
  schemeName?: string //方案名称| 类型：文本 | 长度：50
  currentScale?: number //当前刻度| 类型：文本 | 长度：500
  bizCode?: string // bizCode | 类型：文本 | 长度：5 | 描述：业务状态码
  requestBody?: string // requestBody | 类型：文本 | 长度：500 | 描述：网络请求参数体
  response?: string // response  | 类型：文本 | 长度：500 | 描述：网络请求返回结果
  domainName?: string // domainName | 类型：文本 | 长度：500 | 描述：网校域名
  servicerId?: string // servicerId | 类型：文本 | 长度：200 | 描述：子项目id
  abnormalMessage?: string // abnormalMessage | 类型：文本 | 长度：1000 | 描述：异常信息
  accountId?: string // accountId | 类型：文本 | 长度：200 | 描述：账号id
}

// 完善异常
export class PerfectInfoPoint {
  abnormalMessage: string
  domainName: string
  requestBody: string
  response: string
  userId: string
}
