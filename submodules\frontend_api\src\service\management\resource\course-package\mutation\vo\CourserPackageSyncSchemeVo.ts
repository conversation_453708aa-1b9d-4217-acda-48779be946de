import { CourserPackageSyncSchemeResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class CourserPackageSyncSchemeVo {
  /**
   * 方案id
   */
  schemeId = ''
  /**
   * 方案名称
   */
  schemeName = ''
  /**
   * 数据状态  1一致 0不一致
   */
  dataStatus = ''
  /**
   * 同步状态 0未同步 1同步成功 2同步中 3同步失败
   */
  syncStatus = ''
  /**
   * 最新同步时间
   */
  syncTime = ''
  /**
   * 错误信息
   */
  errorMessage = ''
  /**
   * 课程包同步情况id
   */
  coursePackageUseId = ''

  static from(res: CourserPackageSyncSchemeResponse[], schemeMap: any): CourserPackageSyncSchemeVo[] {
    console.log('schemeMap', schemeMap)
    return res.map(res => {
      console.log('res.schemeId', res.schemeId)
      const courserPackageSyncSchemeVo = new CourserPackageSyncSchemeVo()
      courserPackageSyncSchemeVo.schemeId = res.schemeId
      courserPackageSyncSchemeVo.coursePackageUseId = res.coursePackageUseId
      courserPackageSyncSchemeVo.schemeName = schemeMap[res.schemeId]?.name
      courserPackageSyncSchemeVo.dataStatus = res.dataStatus === 0 ? '不一致' : '一致'
      courserPackageSyncSchemeVo.syncStatus =
        res.syncStatus === 0
          ? '未同步'
          : res.syncStatus === 1
          ? '同步成功'
          : res.syncStatus === 2
          ? '同步中'
          : '同步失败'
      courserPackageSyncSchemeVo.syncTime = res.syncTime
      courserPackageSyncSchemeVo.errorMessage = res.errorMessage
      return courserPackageSyncSchemeVo
    })
  }
}
export default CourserPackageSyncSchemeVo
