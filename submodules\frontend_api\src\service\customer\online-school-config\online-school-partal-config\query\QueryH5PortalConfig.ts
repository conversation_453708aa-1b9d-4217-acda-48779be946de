import InfoCategoryVo from './vo/InfoCategoryVo'
import H5PortalVo from '@api/service/customer/online-school-config/online-school-partal-config/query/vo/H5PortalVo'
import MsBasicdataQueryFrontGatewayBasicDataQueryForestage, {
  PortalInfoResponse1
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import msBasicdataQueryFrontGatewayBasicDataQueryForestage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { ResponseStatus } from '@hbfe/common'
import { MenuTypeEnum } from '@api/service/customer/online-school-config/online-school-partal-config/query/enum/MenuTypeEnum'
import ServicerSeriesV1Gateway, { FieldConstraintResponse } from '@api/ms-gateway/ms-servicer-series-v1'
import RegisterSettingVo from './vo/RegisterSettingVo'
import FieldConstraintVo from './vo/FieldConstraintVo'
import MenuVo from '@api/service/common/online-school-config/vo/MenuVo'
import BannerVo from '@api/service/common/online-school-config/vo/BannerVo'
import Context from '@api/service/common/context/Context'
import UnitConfigVo from '@api/service/customer/online-school-config/online-school-partal-config/query/vo/UnitConfigVo'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

/**
 * @description 查询门户配置（H5）
 */
class QueryH5PortalConfig {
  /**
   * 门户配置标识
   * @description
   */
  id = ''
  /**
   * 是否到期
   */
  isExpire = true
  /**
   * 是否允许访问
   */
  isAccess = false
  /**
   * 首页配置
   */
  h5Portal: H5PortalVo = new H5PortalVo()
  /**
   * 资讯类别列表
   */
  infoCategoryList: Array<InfoCategoryVo> = new Array<InfoCategoryVo>()
  /**
   * 注册配置
   */
  registerSetting: RegisterSettingVo = new RegisterSettingVo()
  /**
   * 平台名称配置
   */
  title = ''
  /**
   * 移动端二维码
   */
  mobileQRCode = ''

  /**
   * 是否需要重新请求
   * @description 比较距离最后一次请求的时间戳是否大于x分钟，如果大于x分钟则需要重新请求，x取阿波罗配置，若该配置不存在则默认5分钟
   */
  get isNeedReRequest() {
    const persistentDataTime =
      Number(ConfigCenterModule.getFrontendApplication(frontendApplication.persistentDataTime)) || 5
    return Date.now() - this.repeatRequestTime > persistentDataTime * 60 * 1000
  }

  /**
   * 重复请求时间戳
   */
  repeatRequestTime = 0

  /**
   * 转换h5端配置模型
   * @description 特殊转换，仅适用于合并请求getTrainingInstitutionPortalInfo接口同时获取web端和h5端的配置时使用
   * @param response 接口返回值
   */
  convertRespFn(response: PortalInfoResponse1) {
    this.id = response.id
    this.h5Portal.CSPhone = response.CSPhone
    this.isAccess = response.isPublished
    this.isExpire = response.onlineSchoolStatus === 1 ? false : true
    this.title = response.title
    this.mobileQRCode = response.mobileQRCode
    this.h5Portal.h5TemplateId = response.portalTemplateId
    this.h5Portal.domainNameH5 = response.domainName
  }

  /**
   * 查询H5门户信息
   * @return h5Portal
   */
  async queryH5PortalInfo() {
    let result = new ResponseStatus(200)
    // 判断唯一标识为空时请求，否则走本地缓存
    if (this.isNeedReRequest || !this.id) {
      const serverId = Context.businessEnvironment.serviceToken?.tokenMeta?.servicerId

      if (!serverId) {
        // 不满足请求条件也不请求，直接返回
        return result
      }

      const {
        status,
        data
      } = await MsBasicdataQueryFrontGatewayBasicDataQueryForestage.getTrainingInstitutionPortalInfo({
        portalType: 2,
        servicerId: serverId
      })
      // 请求成功，则更新时间戳，后续比较时间戳判断是否需要重新请求
      this.repeatRequestTime = Date.now()
      result = status
      if (status && status.isSuccess() && data) {
        this.convertRespFn(data)
      }
    }
    return result
  }

  /**
   * 查询H5的轮播图banner列表
   */
  async queryH5BannerList() {
    let result = new ResponseStatus(200)
    // 预请求门户配置，有缓存则不请求
    await this.queryH5PortalInfo()

    if (!this.isExpire && this.isAccess) {
      const { status, data } = await msBasicdataQueryFrontGatewayBasicDataQueryForestage.getBannerListByPortalType(2)
      result = status
      this.h5Portal.banners = new Array<BannerVo>()
      if (status && status.isSuccess() && data) {
        data.bannerInfos?.forEach(item => this.h5Portal.banners.push(BannerVo.from(item)))
      }
    }
    return result
  }

  /**
   * 查询H5的菜单Menu列表
   */
  async queryH5MenuList() {
    let result = new ResponseStatus(200)
    // 预请求门户配置，有缓存则不请求
    await this.queryH5PortalInfo()

    if (!this.isExpire && this.isAccess) {
      const { status, data } = await msBasicdataQueryFrontGatewayBasicDataQueryForestage.getMenusByPortalType(2)
      result = status
      if (status && status.isSuccess() && data) {
        const menus = new Array<MenuVo>()
        data.menuInfos?.forEach(item => menus.push(MenuVo.from(item)))
        this.getInfoCategoryList(menus)
      }
    }
    return result
  }

  /**
   * 查询H5的资讯列表
   * @return infoCategoryList
   */
  getInfoCategoryList(menus: Array<MenuVo>) {
    const infoCategoryList = menus?.filter(menu => {
      return menu.type === MenuTypeEnum.NEWS
    })
    this.infoCategoryList = infoCategoryList?.map(InfoCategoryVo.from)
  }

  /**
   * 查询H5注册配置项
   * @return infoCategoryList
   */
  async queryRegisterSetting() {
    const res = await ServicerSeriesV1Gateway.getStudentResisterFormConstraintForConfig()
    if (res.status.isSuccess()) {
      this.registerSetting.studentRegisterEnable = res.data.enabled
      // 学员注册字段校验
      await this.queryStudentRegisterSetting(res.data.fieldConstraints)
    }
    // 查询单位配置
    const unitConfigRes = await ServicerSeriesV1Gateway.getDockingTycAndQcc()
    if (unitConfigRes.status?.isSuccess()) {
      this.registerSetting.unitConfig = UnitConfigVo.from(unitConfigRes.data)
    }
  }

  /**
   * 查询H5的学员账号注册配置
   * @return ResponseStatus
   */
  private queryStudentRegisterSetting(fieldConstraintList: Array<FieldConstraintResponse>) {
    this.registerSetting.studentRegister = fieldConstraintList
      ?.filter(field => field.field === 'companyName')
      ?.map(FieldConstraintVo.from)
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 查询注册配置的加密token 在提交注册之前获取
   * @return registerSetting
   */
  async queryRegisterToken(): Promise<ResponseStatus> {
    const res = await ServicerSeriesV1Gateway.getStudentRegisterFormConstraint()
    if (res.status.isSuccess()) {
      this.registerSetting.token = res.data.token
    }
    return res.status
  }
}

export default new QueryH5PortalConfig()
