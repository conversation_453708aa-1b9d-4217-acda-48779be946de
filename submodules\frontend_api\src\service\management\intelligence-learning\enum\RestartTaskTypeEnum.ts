import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum RestartTaskTypeEnum {
  /**
   * 系统默认开始学习时间
   */
  default,

  /**
   * 按上次期望开始学习时间
   */
  last,

  /**
   * 自定义时间
   */
  custom
}
export default class RestartTaskTypeType extends AbstractEnum<RestartTaskTypeEnum> {
  static enum = RestartTaskTypeEnum
  constructor(status?: RestartTaskTypeEnum) {
    super()
    this.current = status
    this.map.set(RestartTaskTypeEnum.last, '按上次期望开始学习时间')
    this.map.set(RestartTaskTypeEnum.default, '系统默认开始学习时间')
    this.map.set(RestartTaskTypeEnum.custom, '自定义时间')
  }
}
