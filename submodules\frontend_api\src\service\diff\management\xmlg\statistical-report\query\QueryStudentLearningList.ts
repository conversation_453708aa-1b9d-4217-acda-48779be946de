import {
  StudentSchemeLearningInOnlineResponse,
  StudentSchemeLearningResponsePage
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
// import { StudentLearningStaticsVo } from '@api/service/management/statisticalReport/query/vo/StudentLearningStaticsVo'
import { StudentLearningStaticsVo } from '@api/service/diff/management/xmlg/statistical-report/query/vo/StudentLearningStaticsVo'
import { StudentSchemeLearningRequestVo } from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
import { Page, Response } from '@hbfe/common'

import QueryStudentLearningList from '@api/service/management/statisticalReport/query/QueryStudentLearningList'
import {
  default as schemeLearningMsGateway,
  FJZJStudentSchemeLearningResponse as XMLGStudentSchemeLearningResponse,
  FJZJStudentSchemeLearningResponsePage as XMLGStudentSchemeLearningResponsePage
} from '@api/diff-gateway/platform-jxjypxtypt-xmlg-school'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'

export default class QueryStudentLearningListDiff extends QueryStudentLearningList {
  constructor() {
    super()
  }

  /**
   * 获取学生课程学习列表
   */
  studentSchemeLearning = new Response<XMLGStudentSchemeLearningResponsePage>()

  /**
   * 获取学生课程学习列表分销
   */
  studentSchemeLearningInDistributor = new Response<XMLGStudentSchemeLearningResponsePage>()

  /**
   * 超管网授
   */
  async pageStudentSchemeLearningInServicerV2(page: Page, filter: StudentSchemeLearningRequestVo) {
    this.studentSchemeLearning = await schemeLearningMsGateway.pageStudentSchemeLearningInServicerV2({
      page: page,
      request: filter
    })
    const studentSchemeLearning = new Response<StudentSchemeLearningResponsePage>()
    Object.assign(studentSchemeLearning, this.studentSchemeLearning)
    return studentSchemeLearning
  }

  /**
   * 分销网授
   */
  async pageStudentSchemeLearningInDistributorV2(page: Page, filter: StudentSchemeLearningRequestVo) {
    this.studentSchemeLearningInDistributor = await schemeLearningMsGateway.pageStudentSchemeLearningInDistributor({
      page: page,
      request: filter
    })

    const studentSchemeLearningInDistributor = new Response<StudentSchemeLearningResponsePage>()
    Object.assign(studentSchemeLearningInDistributor, this.studentSchemeLearningInDistributor)
    return studentSchemeLearningInDistributor
  }

  // 转换成Vo对象
  convertToTrainClassDetailClassVoV2(commodityDetail: StudentSchemeLearningInOnlineResponse) {
    if (this.isHaveZtRole) {
      const studentSchemeLearning = new StudentLearningStaticsVo()
      const data = super.convertToTrainClassDetailClassVoV2(commodityDetail)
      Object.assign(studentSchemeLearning, data)
      return data
    }
    const studentSchemeLearning = new StudentLearningStaticsVo()
    const convertToTrainClassDetailClassVo = super.convertToTrainClassDetailClassVoV2(commodityDetail)
    Object.assign(studentSchemeLearning, convertToTrainClassDetailClassVo)

    let data: XMLGStudentSchemeLearningResponse | undefined

    if (this.isHaveFxRole) {
      data = this.studentSchemeLearningInDistributor.data.currentPageData.find((item) => {
        return item.studentNo === convertToTrainClassDetailClassVo.studentNo
      })
    } else {
      data = this.studentSchemeLearning.data.currentPageData.find((item) => {
        return item.studentNo === convertToTrainClassDetailClassVo.studentNo
      })
    }
    if (data) {
      studentSchemeLearning.dropClassExtendedInfoResponse = data.dropClassExtendedInfoResponse
    }

    return studentSchemeLearning
  }

  get isHaveFxRole() {
    return QueryManagerDetail.hasCategory(CategoryEnums.fxs)
  }

  /**
   * 判断当前用户是否专题管理员角色类型
   */
  get isHaveZtRole() {
    return QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
  }
}
