import Basicdata from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import BasicDataGateway, {
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
import QueryPractitionerCategory from '@api/service/common/basic-data-dictionary/query/QueryPractitionerCategory'
import CertTypeVo from '@api/service/common/basic-data-dictionary/query/vo/CertTypeVo'
import { ResponseStatus } from '@hbfe/common'
class Llst extends TrainingPropertyResponse {
  children = new Array<TrainingPropertyResponse>()
  leaf = false
}

export default new (class QuerycertType {
  /**
   * 证书类型列表
   */
  certTypeList: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()
  /**
   * 证书类型列表
   */
  certTypeListV2: Array<CertTypeVo>
  /**
   * 联级列表数据
   */
  certTypeSelectorList = new Array<Llst>()

  /**
   * 证书类型Map key: 属性id
   */
  private certTypeMap: Map<string, TrainingPropertyResponse> = new Map<string, TrainingPropertyResponse>()
  /**
   * 证书类型Map key: 字典code
   */
  private certTypeCodeMap: Map<number, TrainingPropertyResponse> = new Map<number, TrainingPropertyResponse>()
  /**
   * 证书类型列表缓存
   */
  certTypeCache: Map<string, CertTypeVo[]> = new Map()
  /**
   * 查询对应行业下证书类型
   */
  async querycertTypeByIndustry(): Promise<Array<TrainingPropertyResponse>> {
    this.certTypeList = new Array<TrainingPropertyResponse>()
    this.certTypeMap = new Map<string, TrainingPropertyResponse>()

    const res = await Basicdata.listBusinessDataDictionaryInSubProject({
      businessDataDictionaryType: IndustryPropertyCodeEnum.CERTIFICATES_TYPE
    })
    if (res.data?.length) {
      this.certTypeList = res.data.map(item => {
        const temp = new TrainingPropertyResponse()
        temp.propertyId = item.id
        temp.code = item.code
        temp.parentId = item.parentId
        temp.name = item.name
        temp.sort = item.sort
        return temp
      })

      this.certTypeList.map(item => {
        this.certTypeMap.set(item.propertyId, item)
      })

      this.certTypeList.map(item => {
        this.certTypeCodeMap.set(item.code, item)
      })
    }

    return this.certTypeList
  }

  /**
   * 查询对应行业下证书类型V2
   * @param industryId 行业id
   * @param industryPropertyId 行业属性id
   */
  async querycertTypeByIndustryV2(industryId: string, industryPropertyId: string) {
    this.certTypeList = new Array<TrainingPropertyResponse>()
    this.certTypeMap = new Map<string, TrainingPropertyResponse>()

    const res = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
      industryPropertyId,
      industryId,
      categoryCode: IndustryPropertyCodeEnum.CERTIFICATES_TYPE
    })
    if (res.data?.length) {
      this.certTypeList = res.data
      this.certTypeList.map(item => {
        this.certTypeMap.set(item.propertyId, item)
      })
      this.certTypeList.map(item => {
        this.certTypeCodeMap.set(item.code, item)
      })
    }

    return this.certTypeList
  }

  /**
   * 查询证书类型列表
   * @param industryPropertyId 行业属性id 行业属性分类id
   * @param industryId 行业id
   * @return
   */
  async queryCertTypeV2(industryPropertyId: string, industryId: string) {
    if (this.certTypeCache.get(industryId)) {
      this.certTypeListV2 = this.certTypeCache.get(industryId)
      const status = new ResponseStatus(200)
      status.code = 200
      return status
    }
    const res = await BasicDataGateway.listAllIndustryProperty({
      industryPropertyId: industryPropertyId,
      industryId
    })
    if (res.status.isSuccess()) {
      // todo 需要字段统一
      const vo = res.data.map(item => CertTypeVo.from(item))
      this.certTypeCache.set(industryId, vo)
      this.certTypeListV2 = res.data
    }
    return res.status
  }

  /**
   * 获取详情表
   * @param propertyId 属性id
   */
  getcertTypeDetail(propertyId: string): TrainingPropertyResponse {
    return this.certTypeMap.get(propertyId)
  }
  /**
   * 通过code获取详情
   * @param code 字典code
   */
  getcertTypeCodeDetail(code: number): TrainingPropertyResponse {
    return this.certTypeCodeMap.get(code)
  }
  /**
   * 联级选择器
   */
  async QuerycertTypeSelector() {
    await this.querycertTypeByIndustry()
    this.certTypeSelectorList = this.certTypeList as Array<Llst>
    await Promise.all(
      this.certTypeSelectorList.map(async item => {
        const res = await QueryPractitionerCategory.queryPractitionerCategoryByIndustry(item.propertyId)
        res?.map(ite => {
          ite['leaf'] = true
        })
        if (res?.length) {
          item.leaf = false
          item.children = res
        } else {
          item.leaf = true
        }
      })
    )
    return this.certTypeSelectorList
  }
})()
