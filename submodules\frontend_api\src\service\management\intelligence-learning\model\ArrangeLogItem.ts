import { AutoLearningArrangeLogResponse } from '@api/ms-gateway/ms-autolearning-log-v1'

export default class ArrangeLogItem {
  /**
   * 日志id
   */
  logId = ''
  /**
   * 任务id
   */
  taskId = ''
  /**
   * 任务类型
   */
  taskType = ''
  /**
   * 创建时间
   */
  createTime = ''
  /**
   * 编排完成时间
   */
  arrangeTime = ''
  /**
   * 重启次数
   */
  arrangeNum: number = null

  /**
   * 是否允许重叠
   */
  allowOverlap = false

  static from(dto: AutoLearningArrangeLogResponse) {
    const vo = new ArrangeLogItem()
    vo.logId = dto.logId
    vo.taskId = dto.mainTaskId
    vo.arrangeNum = dto.arrangeNum
    switch (dto.type) {
      case 0:
        vo.taskType = '导入智能学习'
        break
      case 1:
        vo.taskType = '重启智能学习'
        break
    }
    vo.createTime = dto.createTime
    vo.arrangeTime = dto.completeTime
    vo.allowOverlap = dto.allowOverlap
    return vo
  }
}
