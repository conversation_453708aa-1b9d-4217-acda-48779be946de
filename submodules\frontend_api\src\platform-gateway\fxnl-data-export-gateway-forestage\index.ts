import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/fxnl-data-export-gateway-forestage'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'fxnl-data-export-gateway-forestage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

/**
 * 功能描述：任务查询参数
@Author： wtl
@Date： 2022/1/18 15:13
 */
export class JobRequest {
  /**
   * 任务组名（必填）
   */
  group?: string
  /**
   * 任务组名匹配方式（EQ：完全匹配 LIKE：模糊匹配[*group*] LLIKE：左模糊匹配[group*] RLIKE：右模糊匹配[*group]，不传值默认为完全匹配）
   */
  groupOperator?: string
  /**
   * 任务名（模糊查询）
   */
  jobName?: string
  /**
   * 任务状态(executing:运行中 executed:运行完成 fail:运行失败)
@see UserJobState
   */
  jobState?: string
  /**
   * 任务执行时间 yyyy-MM-dd HH:mm:ss
   */
  executeTimeScope?: DateScopeRequest
  /**
   * 异步任务处理结果（true:成功 false:失败）
   */
  jobResult?: boolean
  /**
   * 分割粒度
null-无 1-单位
   */
  granularity?: number
}

/**
 * 功能描述：异步任务日志返回对象
@Author： wtl
@Date： 2022/4/11 17:18
 */
export class UserJobLogResponse {
  /**
   * 任务id
   */
  jobId: string
  /**
   * 任务组名
   */
  group: string
  /**
   * 任务名
   */
  jobName: string
  /**
   * 任务开始时间
   */
  beginTime: string
  /**
   * 任务结束时间
   */
  endTime: string
  /**
   * 任务状态(executing:运行中 executed:运行完成 fail:运行失败)
@see UserJobState
   */
  jobState: string
  /**
   * 异步任务处理结果（true:成功 false:失败）
   */
  jobResult: boolean
  /**
   * 任务执行成功或失败的信息
   */
  message: string
  /**
   * 导出文件路径
   */
  exportFilePath: string
  /**
   * 是否受保护
   */
  isProtected: boolean
  /**
   * 资源id
   */
  fileResourceId: string
  /**
   * 操作人id
   */
  operatorUserId: string
  /**
   * 操作人帐户id
   */
  operatorAccountId: string
}

export class UserJobLogResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserJobLogResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageExportTaskInfoInMyself(
    params: { page?: Page; jobRequest?: JobRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageExportTaskInfoInMyself,
    operation?: string
  ): Promise<Response<UserJobLogResponsePage>> {
    return commonRequestApi<UserJobLogResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
