import { BatchOrderDeliveryWayEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderDeliveryWay'

/**
 * @description 【集体报名订单】订单详情-配送信息
 */
class BatchOrderDetailDeliveryInfoVo {
  /**
   * 配送方式
   */
  deliveryWay: BatchOrderDeliveryWayEnum = null

  /**
   * 快递公司
   */
  deliveryCompany = ''

  /**
   * 收货地址
   */
  distributeRegion = ''
  /**
   * 收货详细地址
   */
  distributeAddress = ''

  /**
   * 收件人
   */
  distributeConsignee = ''

  /**
   * 手机号
   */
  distributePhone = ''

  /**
   * 运单号
   */
  deliveryNo = ''

  /**
   * 发货时间
   */
  deliveryTime = ''

  /**
   * 领取地点
   */
  selfFetchedPoint = ''

  /**
   * 领取时间
   */
  selfFetchTime = ''

  /**
   * 备注
   */
  remark = ''
}

export default BatchOrderDetailDeliveryInfoVo
