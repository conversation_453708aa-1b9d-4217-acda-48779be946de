import { Page } from '@hbfe/common'
import QueryBatchOrderListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderListVo'
import BatchOrderListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderListDetailVo'
import BatchOrderListStatisticVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderListStatisticVo'
import DataResolve from '@api/service/common/utils/DataResolve'
import UserModule from '@api/service/management/user/UserModule'
import BatchOrderDetailBuyerInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailBuyerInfoVo'
import CollectiveManagerInfoVo from '@api/service/management/user/query/manager/vo/CollectiveManagerInfoVo'

export default abstract class QueryBatchOrderListBase {
  /**
   * 【集体报名订单】查询列表
   * @param {Page} page - 分页参数
   * @param {QueryBatchOrderListVo} queryParams - 查询参数
   * @return {Promise<BatchOrderListDetailVo[]>} - 【集体报名订单】列表
   */
  abstract queryBatchOrderList(page: Page, queryParams: QueryBatchOrderListVo): Promise<BatchOrderListDetailVo[]>

  /**
   * 【集体报名订单】查询列表统计数据
   * @param {QueryBatchOrderListVo} queryParams - 查询参数
   * @return {Promise<BatchOrderListStatisticVo>}
   */
  abstract queryBatchOrderListStatistic(queryParams: QueryBatchOrderListVo): Promise<BatchOrderListStatisticVo>

  /**
   * 获取批次单列表交易成功总金额
   */
  abstract getBatchOrderTotalAmount(queryParams: QueryBatchOrderListVo): Promise<number>

  /**
   * 填充集体缴费管理员信息
   */
  protected async fillCollectiveInfoForList(list: BatchOrderListDetailVo[]): Promise<BatchOrderListDetailVo[]> {
    if (!DataResolve.isWeightyArr(list)) return list
    const userIds = list.map(item => {
      return item.buyerInfo.buyerId
    })
    if (!userIds.length) return list
    const queryRemote = UserModule.queryUserFactory.queryCollectiveManagerList
    const response = await queryRemote.queryCollectiveManagerInfoList(userIds)
    const result: Map<string, BatchOrderDetailBuyerInfoVo> = new Map<string, BatchOrderDetailBuyerInfoVo>()
    response?.map((item: CollectiveManagerInfoVo) => {
      const info: BatchOrderDetailBuyerInfoVo = new BatchOrderDetailBuyerInfoVo()
      info.buyerId = item.userId || null
      info.buyerName = item.userName || null
      info.buyerAccount = item.phone || null
      info.buyerPhone = item.phone || null
      result.set(item.userId, info)
    })
    list.forEach((item: BatchOrderListDetailVo) => {
      item.buyerInfo = result.get(item.buyerInfo.buyerId) || new BatchOrderDetailBuyerInfoVo()
    })
    return list
  }
}
