import MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import AssembleTree from '@api/service/common/utils/AssembleTree'
import TreeItem, { DTOTreeModule } from './module/TreeItem'

export class SecondElectiveMaxPeriod {
  /**
   * 学时要求
   */
  electiveMaxPeriod: number
  /**
   * 选修分类二ID
   */
  id: string
  /**
   * 选修分类名称
   */
  name: string
  /**
   * 操作
   */
  operation: number
}
/**
 * 关于栏目数操作类
 */
export default class TreeModule {
  /**
   * 选修要求 -- 配置项 -- 节点为key
   */
  secondElectiveMaxPeriodMap: Map<string, SecondElectiveMaxPeriod> = new Map()
  /**
   * 节点ID映射学时
   */
  treeIemMap: Map<string, TreeItem> = new Map()
  /**
   * 是否存在二级分类要求
   */
  isSecondElectiveMaxPeriod = false
  /**
   * 初始化整棵树 --- 选修
   * @param schemeId 方案ID
   * @param studentNo 学号
   */
  async init(schemeId: string) {
    // 处理远端分类树转换成UI使用
    const result = await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage.getSchemeConfigInServicer({
      schemeId,
      needField: ['chooseCourseLearning']
    })
    const schemeConfig = JSON.parse(result.data.schemeConfig)
    const courseLearningConstructors: DTOTreeModule[] = schemeConfig.chooseCourseLearning.config.courseTrainingOutlines

    const voTree = new Array<TreeItem>()
    const electiveTree = courseLearningConstructors.filter(item => item.category == 2)
    if (schemeConfig.chooseCourseLearning.config?.chooseCourseRule?.config?.secondElectiveMaxPeriod?.length) {
      schemeConfig.chooseCourseLearning.config?.chooseCourseRule?.config.secondElectiveMaxPeriod?.forEach(
        (item: SecondElectiveMaxPeriod) => {
          this.secondElectiveMaxPeriodMap.set(item.id, item)
        }
      )
    } else {
      // 说明只存在一级分类
      this.isSecondElectiveMaxPeriod = true
      this.secondElectiveMaxPeriodMap.set(
        electiveTree[0].id,
        schemeConfig.chooseCourseLearning.config?.chooseCourseRule?.config
      )
    }
    this.loopTree(electiveTree, voTree)
    voTree.forEach(item => {
      const temp = this.secondElectiveMaxPeriodMap.get(item.outlineId)
      if (temp) {
        item.courseConfig = temp.electiveMaxPeriod
      }
    })
    const assembleUtil = new AssembleTree<TreeItem>(voTree, 'outlineId', 'fatherOutlineId', 'childrenTreeItem')
    // 组装完成自己模型树
    const levelTree = assembleUtil.assembleTree() // 主要树
    this.treeIemMap = assembleUtil.dataMap
    // 处理一级菜单全部课程
    if (levelTree?.length) {
      // 1.提取所有末级节点
      const endTreeItem: TreeItem[] = []
      this.extractLevelEnd(voTree, endTreeItem)
      // 2. 新生成节点，gatherTreeItem存住所有末级节点
      const allNode = new TreeItem()
      const topNode = levelTree[0]
      if (topNode?.childrenTreeItem?.length) {
        allNode.name = '全部'
        allNode.outlineId = topNode.outlineId + 'all'
        allNode.level = 2
        allNode.fatherOutlineId = topNode.outlineId
        allNode.fatherOutlineName = '选修课'
        allNode.gatherTreeItem.push(...endTreeItem)
        levelTree[0].childrenTreeItem.unshift(allNode)
      }
    }

    // 处理二级菜单全部课程
    // 1. 先提取所有三级节点
    const thirdTreeItem: TreeItem[] = []
    this.extractLevelThird(voTree, thirdTreeItem)
    // 2. 取出三级目录的Map 父节点当Key值映射
    const thirdTreeMap: Map<string, TreeItem> = new Map()
    thirdTreeItem.forEach(item => {
      const mapData = thirdTreeMap.get(item.fatherOutlineId) ? thirdTreeMap.get(item.fatherOutlineId) : new TreeItem()
      mapData.name = '全部'
      mapData.outlineId = item.fatherOutlineId + 'all'
      mapData.level = 3
      mapData.fatherOutlineId = item.fatherOutlineId
      mapData.fatherOutlineName = item.fatherOutlineName
      mapData.gatherTreeItem.push(item)
      thirdTreeMap.set(item.fatherOutlineId, mapData)
    })
    this.disposeLevelTwo(levelTree, thirdTreeMap)
    return levelTree
  }
  /**
   * 打平树
   * @param tree
   * @param voTree
   * @param level
   * @param fatherTree
   */
  private loopTree(tree: DTOTreeModule[], voTree: TreeItem[] = [], level = 0, fatherTree?: DTOTreeModule) {
    const newLevel = level + 1
    tree.forEach(item => {
      if (fatherTree) {
        voTree.push(TreeItem.from(item, fatherTree, newLevel))
        if (item.childOutlines.length > 0) {
          this.loopTree(item.childOutlines, voTree, newLevel, item)
        }
      } else {
        voTree.push(TreeItem.from(item, new DTOTreeModule(), newLevel))
        this.loopTree(item.childOutlines, voTree, newLevel, item)
      }
    })
  }
  /**
   * 提取末级叶子节点
   */
  private extractLevelEnd(voTree: TreeItem[], result: TreeItem[] = []) {
    voTree.forEach(item => {
      if (item.childrenTreeItem?.length) {
        this.extractLevelThird(item.childrenTreeItem, result)
      } else {
        result.push(item)
      }
    })
  }
  /**
   * 提取三级目录
   */
  private extractLevelThird(voTree: TreeItem[], result: TreeItem[] = []) {
    voTree.forEach(item => {
      if (item.level === 3) {
        result.push(item)
        if (item.childrenTreeItem.length > 0) {
          this.extractLevelThird(item.childrenTreeItem, result)
        }
      }
    })
  }
  /**
   * 处理二级目录下的节点 --- 工种特殊处理
   */
  private disposeLevelTwo(voTree: TreeItem[], thirdTreeMap: Map<string, TreeItem>) {
    voTree.forEach(item => {
      if (item.level === 2) {
        const tempArr: TreeItem[] = []
        thirdTreeMap.get(item.outlineId) ? tempArr.push(thirdTreeMap.get(item.outlineId)) : undefined
        if (tempArr.length) {
          item.childrenTreeItem.unshift(...tempArr)
        }
      }

      if (item.childrenTreeItem.length > 0) {
        this.disposeLevelTwo(item.childrenTreeItem, thirdTreeMap)
      }
    })
  }
}
