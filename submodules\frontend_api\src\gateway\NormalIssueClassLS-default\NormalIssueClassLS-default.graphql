schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取指定的考前培训方案
		@param schemeId 学习方案ID
	"""
	findById(schemeId:String):NormalIssueClassLSResponse @NotAuthenticationRequired
	"""期数购买校验
		@param request
		@return
	"""
	validateJoinIssueLearningScheme(request:IssueJoinValidateRequest):JoinValidateResponse
}
type Mutation {
	"""申请选课Token
		@param applyInfo 申请信息
	"""
	applyChooseCourseToken(applyInfo:ApplyChooseCourseTokenRequest):String
	"""申请兴趣课程选课Token
		@param applyInfo 申请信息
	"""
	applyChooseInterestCourseToken(applyInfo:ApplyChooseInterestCourseTokenRequest):String
	"""申请考试Token，可能返回其他代码，如：
		"403001" - 考试前置条件未通过
		@param applyInfo 申请信息
	"""
	applyExamLearningToken(applyInfo:ApplyExamLearningTokenRequest):String
	"""申请兴趣课程学习Token
		@param applyInfo 申请信息
	"""
	applyInterestCourseLearningToken(applyInfo:ApplyInterestCourseLearningTokenRequest):String
	"""申请练习试题练习Token
		@param applyInfo 申请信息
	"""
	applyQuestionLibPracticeLearningToken(applyInfo:ApplyQuestionLibPLTokenDto):String
	"""申请课程学习Token
		@param applyInfo 申请信息
	"""
	applySingleCourseLearningToken(applyInfo:ApplyCourseLearningTokenRequest):String
	"""清除指定用户期数下的所有学习记录"""
	clearAllLearningRecordForIssue(request:ClearLearningRecordRequest):Boolean!
	"""立即完成指定用户的一个期数的学习，涉及的所有学习方式都将自动完成，并通过所有考核
		@param request 要立即完成的期数信息
	"""
	completedAllLearningForIssue(request:CompletedLearningRequest):Boolean!
	"""立即完成指定用户的一门课程的学习，学习进度达100%并答对所有未答的弹窗题
		@param request 课程信息
	"""
	completedLearningForOneCourse(request:CompletedOneCourseRequest):Boolean!
	"""立即完成指定用户的一门课程下一个课件的学习，学习进度达100%并答对所有未答的弹窗题
		@param request 课件信息
	"""
	completedLearningForOneCourseware(request:CompletedOneCoursewareRequest):Boolean!
	"""在指定的普通期数培训班下新增期数
		@param createInfo 期数创建信息
	"""
	createIssue(createInfo:IssueClassLSIssueCreateRequest):IssueAndCommoditySkuResponse
	"""创建一个普通期数培训班
		@param createInfo 创建信息
	"""
	createLS(createInfo:IssueClassLSCreateRequest):NormalIssueClassLSResponse
	"""删除用户答卷记录
		@param request
	"""
	deleteUserExamAnswerInfoById(request:DeleteUserAnswerRecordRequest):Boolean!
	"""发布普通期数培训班
		@param schemeId 学习方案ID
	"""
	publishLS(schemeId:String):Void
	"""删除指定期数
		@param schemeId 学习方案ID
		@param issueId  期数ID
	"""
	removeIssue(schemeId:String,issueId:String):Void
	"""删除学习方案
		@param schemeId 学习方案ID
	"""
	removeLS(schemeId:String):Void
	"""更新普通期数培训班指定的期数
		@param updateInfo 期数更新信息
	"""
	updateIssue(updateInfo:IssueClassLSIssueUpdateRequest):IssueAndCommoditySkuResponse
	"""更新一个普通期数培训班信息
		@param updateInfo 更新信息
	"""
	updateLS(updateInfo:IssueClassLSUpdateRequest):NormalIssueClassLSResponse
}
input ExamAnswerSettingsDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.ExamAnswerSettingsDto") {
	score:Double!
}
input ShelvePlain @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.ShelvePlain") {
	onShelve:Boolean!
	onShelvePlanTime:DateTime
	offShelvePlanTime:DateTime
}
"""申请选课Token信息"""
input ApplyChooseCourseTokenRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.ApplyChooseCourseTokenRequest") {
	"""用户id"""
	userId:String
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
}
"""申请兴趣课选课Token信息"""
input ApplyChooseInterestCourseTokenRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.ApplyChooseInterestCourseTokenRequest") {
	"""用户id"""
	userId:String
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
}
"""申请课程学习Token信息"""
input ApplyCourseLearningTokenRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.ApplyCourseLearningTokenRequest") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
	"""课程包ID"""
	packageId:String
	"""课程ID"""
	courseId:String
}
"""考试Token信息"""
input ApplyExamLearningTokenRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.ApplyExamLearningTokenRequest") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
}
"""<AUTHOR>
	@date 2020/8/18
	@description
"""
input ApplyInterestCourseLearningTokenRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.ApplyInterestCourseLearningTokenRequest") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
	"""课程包ID"""
	poolId:String
	"""课程ID"""
	courseId:String
}
"""申请练习学习Token"""
input ApplyQuestionLibPLTokenDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.ApplyQuestionLibPLTokenDto") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
	"""抽题数量"""
	questionCount:Int!
}
"""清楚指定条件下的所有学习记录
	<AUTHOR>
	@date 2020/8/22
	@description
"""
input ClearLearningRecordRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.ClearLearningRecordRequest") {
	"""方案id"""
	schemeId:String
	"""期数id"""
	issueId:String
}
"""完成指定条件下的所有学习方式的学习并通过考核
	<AUTHOR>
	@date 2020/8/22
	@description
"""
input CompletedLearningRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.CompletedLearningRequest") {
	"""方案id"""
	schemeId:String
	"""期数id"""
	issueId:String
	"""用户id"""
	userId:String
	"""考试自动答题设置信息"""
	examAutomaticAnswerSettings:ExamAnswerSettingsDto
}
"""完成指定条件下的课程的学习
	<AUTHOR>
	@date 2020/8/22
	@description
"""
input CompletedOneCourseRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.CompletedOneCourseRequest") {
	"""方案id"""
	schemeId:String
	"""期数id"""
	issueId:String
	"""学习方式id"""
	learningId:String
	"""课程id"""
	courseId:String
	"""用户id"""
	userId:String
	"""弹窗题对题率，1~100"""
	correctRate:Double!
}
"""完成指定条件下的课件的学习
	<AUTHOR>
	@date 2020/8/22
	@description
"""
input CompletedOneCoursewareRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.CompletedOneCoursewareRequest") {
	"""方案id"""
	schemeId:String
	"""期数id"""
	issueId:String
	"""学习方式id"""
	learningId:String
	"""课程id"""
	courseId:String
	"""用户id"""
	userId:String
	"""课件id"""
	coursewareId:String
}
"""课程学习方式设置信息"""
input CourseLearningSettingsRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.CourseLearningSettingsRequest") {
	"""是否启用课程学习方式"""
	enabled:Boolean!
	"""必修课程包编号列表"""
	compulsoryPackages:[PackageRuleSettingRequest]
	"""选修课程包集合"""
	optionalPackages:[PackageRuleSettingRequest]
	"""所有选修包要求最多选课学时，如果不填，表示不设置要求"""
	optionalTotalPeriod:Double
	"""课程学习考核，null表示不设置考核"""
	assessSetting:CourseLearningAssessSettingRequest
}
input CourseLearningAssessSettingRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.CourseLearningSettingsRequest$CourseLearningAssessSettingRequest") {
	"""是否要求已选课程全部完成"""
	allSelectedComplete:Boolean!
	"""课程学习完成进度"""
	schedule:Double!
}
"""<AUTHOR>
	@date 2020/8/29
	@description
"""
input DeleteUserAnswerRecordRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.DeleteUserAnswerRecordRequest") {
	userId:String
	issueId:String
	roundId:String
	answerInfoId:String
}
"""考试学习方式设置信息"""
input ExamLearningSettingsRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.ExamLearningSettingsRequest") {
	"""是否启用考试学习方式"""
	enabled:Boolean!
	"""考试名称"""
	name:String
	"""试卷ID"""
	examPaperId:String
	"""考试时长，单位分钟"""
	examTimeLength:Int!
	"""考试次数 0表示不限制次数"""
	examCount:Int!
	"""及格分数"""
	passScore:Double!
	"""是否开放试题解析"""
	openResolvedExam:Boolean!
	"""最短提交时长"""
	minSubmitTimeLength:Int!
	"""是否启用考试前置条件"""
	enabledExamLearningPrecondition:Boolean!
	"""考试考核，null表示不设置考核"""
	assessSettingDto:ExamAssessSettingRequest
	"""是否配置考试时间"""
	configExamTime:Boolean!
	"""开考时间"""
	beginTime:DateTime
	"""结束时间"""
	endTime:DateTime
	"""多选漏选是否的分"""
	less:Boolean!
	"""漏选得分模式
		0:不得分，适用于漏选不得分情况
		1:全得
		2:得一半
		3:平均得分
	"""
	missScorePattern:Int!
}
input ExamAssessSettingRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.ExamLearningSettingsRequest$ExamAssessSettingRequest") {
	"""考试成绩"""
	score:Double!
}
"""配置培训成果
	<AUTHOR>
	@date 2020/6/2
	@since 1.0.0
"""
input IssueClassLSAchieveSettingRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.IssueClassLSAchieveSettingRequest") {
	"""是否启用培训班考核
		<pre>
		如果启用，则默认使用选课学习方式和考试学习方式配置的考核作为子指标
		</pre>
	"""
	enabledAssess:Boolean!
	"""只有{@link #enabledAssess}为true，才生效；获取的学时，不填表示不获取学时成果"""
	grade:Double
	"""只有{@link #enabledAssess}为true，才生效；获取证书的模板编号，不填表示不获取证书"""
	templateId:String
}
input IssueClassLSCreateRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.IssueClassLSCreateRequest") {
	"""培训方案名称"""
	name:String
	"""年度"""
	year:Int!
	"""封面图片地址"""
	picture:String
	"""发布机构"""
	unitId:String
	"""web内容"""
	content:String
	"""手机端内容"""
	mobileContent:String
	"""培训班成果设置"""
	achieveSetting:IssueClassLSAchieveSettingRequest
	"""课程学习方式设置信息"""
	courseLearningSettings:CourseLearningSettingsRequest
	"""考试学习方式设置信息"""
	examLearningSettings:ExamLearningSettingsRequest
	"""试题练习学习方式配置"""
	questionPracticeLearningSettings:QPracticeLearningSettingsRequest
	"""适用人群"""
	suitablePeople:String
}
"""普通期数培训班期数创建信息"""
input IssueClassLSIssueCreateRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.IssueClassLSIssueCreateRequest") {
	"""学习方案ID"""
	schemeId:String
	"""期数名称"""
	title:String
	"""培训开始时间"""
	startTime:DateTime
	"""培训结束时间"""
	endTime:DateTime
	"""关闭的渠道"""
	closeChannelList:[Int]
	"""排序"""
	sort:Int!
	"""销售价格"""
	price:BigDecimal
	"""销售介绍"""
	saleIntroduce:String
	"""上下架设置"""
	shelvePlain:ShelvePlain
	"""学习起止时间配置类型
		@see com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.constants.IssueStudyTimeConfigTypeConst
		1:分配指定学习时间
		2:长期培训，无限制
	"""
	studyTimeConfigType:Int!
	"""重新开通的渠道"""
	reopenChannelList:[Int]
	"""是否开放学员购买"""
	openCustomerPurchase:Boolean!
}
"""普通期数培训班期数更新信息"""
input IssueClassLSIssueUpdateRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.IssueClassLSIssueUpdateRequest") {
	"""要更新期数的学习方案ID"""
	schemeId:String
	"""要更新的期数ID"""
	issueId:String
	"""期数名称"""
	title:String
	"""培训开始时间"""
	startTime:DateTime
	"""培训结束时间"""
	endTime:DateTime
	"""排序"""
	sort:Int!
	"""关闭的渠道"""
	closeChannelList:[Int]
	"""销售介绍"""
	saleIntroduce:String
	"""销售价格"""
	price:BigDecimal
	"""上下架设置"""
	shelvePlain:ShelvePlain
	"""学习起止时间配置类型 1:分配指定学习时间 2:长期培训，无限制"""
	studyTimeConfigType:Int!
	"""重新开通的渠道"""
	reopenChannelList:[Int]
	"""是否开放学员购买"""
	openCustomerPurchase:Boolean!
}
input IssueClassLSUpdateRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.IssueClassLSUpdateRequest") {
	"""学习方案ID"""
	schemeId:String
	"""培训方案名称"""
	name:String
	"""封面图片地址"""
	picture:String
	"""web内容"""
	content:String
	"""手机端内容"""
	mobileContent:String
	"""培训班成果设置"""
	achieveSetting:IssueClassLSAchieveSettingRequest
	"""课程学习方式设置信息"""
	courseLearningSettings:CourseLearningSettingsRequest
	"""考试学习方式设置信息"""
	examLearningSettings:ExamLearningSettingsRequest
	"""试题练习学习方式配置"""
	questionPracticeLearningSettings:QPracticeLearningSettingsRequest
	"""适用人群"""
	suitablePeople:String
}
"""期数是否可以预约校验信息
	<AUTHOR>
"""
input IssueJoinValidateRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.IssueJoinValidateRequest") {
	"""商品SKU"""
	skuId:String
	"""期数id"""
	issueId:String
	"""渠道类型
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	purchaseChannelType:Int!
	"""用户id"""
	userId:String
}
"""题库配置
	<AUTHOR>
	@date 2020/6/3
	@since 1.0.0
"""
input LibraryWaySettingRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.LibraryWaySettingRequest") {
	"""题库id集合"""
	libraryIds:[String]
	"""是否递归取题
		如果是题库卷时，才生效。
	"""
	recursive:Boolean!
}
"""课程包规则设置
	<AUTHOR>
	@date 2020/6/1
	@since 1.0.0
"""
input PackageRuleSettingRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.PackageRuleSettingRequest") {
	"""课程包编号"""
	packageId:String
	"""是否限制最大可选学时,如果设置true,则需要填写{@link #maxPeriod}"""
	limit:Boolean!
	"""当前课程包要求最多选课学时，如果不填，表示不设置要求"""
	maxPeriod:Double
}
"""试题练习学习方式配置"""
input QPracticeLearningSettingsRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.QPracticeLearningSettingsRequest") {
	"""是否启用试题练习"""
	enabled:Boolean!
	"""抽题方式， 1：自定义（此时需要指定random配置），2：题库，3：考场所属方案下已选课程关联试题，4：标签方式抽题"""
	fetchWay:Int!
	"""题库抽题方式配置"""
	libraryWaySetting:LibraryWaySettingRequest
	"""标签方式"""
	tagsWaySetting:TagWaySettingRequest
}
"""标签方式抽题
	<AUTHOR>
	@date 2020/6/19
	@since
"""
input TagWaySettingRequest @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.request.TagWaySettingRequest") {
	"""标签编号集合"""
	tagIds:[String]
}
type ShelvePlain1 @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.ShelvePlain") {
	onShelve:Boolean!
	onShelvePlanTime:DateTime
	offShelvePlanTime:DateTime
}
"""课程学习方式信息"""
type CourseLearningResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.CourseLearningResponse") {
	"""学习方式ID"""
	learningId:String
	"""是否启用课程学习方式"""
	enabled:Boolean!
	"""必修课程包编号列表"""
	compulsoryPackages:[PackageResponse]
	"""选修课程包集合"""
	optionalPackages:[PackageResponse]
}
"""考试学习方式信息"""
type ExamLearningResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.ExamLearningResponse") {
	"""学习方式ID"""
	learningId:String
	"""是否启用考试学习方式"""
	enabled:Boolean!
	"""考试名称"""
	name:String
	"""试卷ID"""
	examPaperId:String
	"""考试时长，单位分钟"""
	examTimeLength:Int!
	"""考试次数 0表示不限制次数"""
	examCount:Int!
	"""及格分数"""
	passScore:Double!
	"""是否开放试题解析"""
	openResolvedExam:Boolean!
	"""最短提交时长"""
	minSubmitTimeLength:Int!
	"""是否配置考试时间"""
	configExamTime:Boolean!
	"""开考时间"""
	beginTime:DateTime
	"""结束时间"""
	endTime:DateTime
	"""多选漏选是否的分"""
	less:Boolean!
	"""漏选得分模式
		0:不得分，适用于漏选不得分情况
		1:全得
		2:得一半
		3:平均得分
	"""
	missScorePattern:Int!
}
"""期数和商品SKU信息"""
type IssueAndCommoditySkuResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.IssueAndCommoditySkuResponse") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID"""
	issueId:String
	"""商品SkuId"""
	commoditySkuId:String
	"""标题"""
	title:String
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
	"""关闭的渠道"""
	closeChannelList:[Int]
	"""销售价格"""
	price:BigDecimal
	"""上下架设置"""
	shelvePlain:ShelvePlain1
	"""学习起止时间配置类型 1:分配指定学习时间 2:长期培训，无限制"""
	studyTimeConfigType:Int!
	"""重新开通的渠道"""
	reopenChannelList:[Int]
	"""是否开放学员购买
		@see PurchaseChannelTypes#CUSTOMER_PURCHASE
	"""
	openCustomerPurchase:Boolean!
}
"""<AUTHOR>
type JoinValidateResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.JoinValidateResponse") {
	"""验证结果
		200：验证通过
		500：验证不通过，带 message
		40001：已经存在预定记录
		40002：已经存在预约记录
		40003：培训时间已结束
	"""
	code:String
	"""验证失败原因"""
	message:String
	"""当 code= 40001，该值有效"""
	orderNo:String
}
"""题库配置
	<AUTHOR>
	@date 2020/6/3
	@since 1.0.0
"""
type LibraryWaySettingResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.LibraryWaySettingResponse") {
	"""题库id集合"""
	libraryIds:[String]
	"""是否递归取题
		如果是题库卷时，才生效。
	"""
	recursive:Boolean!
}
"""普通期数培训班信息"""
type NormalIssueClassLSResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.NormalIssueClassLSResponse") {
	"""学习方案ID"""
	schemeId:String
	"""培训方案名称"""
	name:String
	"""年度"""
	year:Int!
	"""封面图片地址"""
	picture:String
	"""web内容"""
	content:String
	"""手机端内容"""
	mobileContent:String
	"""发布机构"""
	unitId:String
	"""课程学习方式信息"""
	courseLearning:CourseLearningResponse
	"""考试学习方式信息"""
	examLearning:ExamLearningResponse
	"""试题练习学习方式信息"""
	questionPracticeLearning:QuestionPracticeLearningResponse
	"""创建人ID"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""适用人群"""
	suitablePeople:String
}
"""课程包规则设置
	<AUTHOR>
	@date 2020/6/1
	@since 1.0.0
"""
type PackageResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.PackageResponse") {
	"""课程包编号"""
	packageId:String
	"""是否限制最大可选学时,如果设置true,则需要填写{@link #maxPeriod}"""
	limit:Boolean!
	"""当前课程包要求最多选课学时，如果不填，表示不设置要求"""
	maxPeriod:Double
}
"""试题练习学习方式信息"""
type QuestionPracticeLearningResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.QuestionPracticeLearningResponse") {
	"""学习方式ID"""
	learningId:String
	"""是否启用试题练习"""
	enabled:Boolean!
	"""抽题方式， 1：自定义（此时需要指定random配置），2：题库，3：考场所属方案下已选课程关联试题，4：标签方式抽题"""
	fetchWay:Int!
	"""题库抽题方式配置"""
	libraryWaySetting:LibraryWaySettingResponse
	"""标签方式"""
	tagsWaySetting:TagWaySettingResponse
}
"""标签方式抽题
	<AUTHOR>
	@date 2020/6/19
	@since
"""
type TagWaySettingResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.TagWaySettingResponse") {
	"""标签编号集合"""
	tagIds:[String]
}

scalar List
