import {
  ChannelInfoDto,
  CommodityDistributorOpenReportInSupplierResponse,
  CommodityDistributorOpenReportResponse,
  DistributionPricingResponse,
  DistributorOpenReportResponse,
  PropertyResponse
} from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import { AccountTypeEunm } from '@api/service/common/enums/trade-configuration/AccountType'
import DistributorsInfo from '@api/service/management/statisticalReport/DistributionGoodsOpeningStatistics/model/DistributorsInfo'
import DistributionPricingMethodsEnumsClass from '@api/service/management/statisticalReport/enums/DistributionPricingMethodsEnumsClass'
import DistributionTiersEnumClass from '@api/service/management/statisticalReport/enums/DistributionTiersEnumClass'
import StatisticInfoItem from '@api/service/management/statisticalReport/models/StatisticInfoItem'

export enum PurchaseChannelEnums {
  // 1:用户自主购买
  user_made_purchases = 1,
  // 2:集体缴费
  group_contributions,
  // 3:管理员导入
  admin_import,
  // 4:集体报名个人缴费渠道
  collective_registration_and_individual_payment_channels
}

export default class DistributionGoodsOpeningStatisticsItem {
  /**
   * 序列号
   */
  no = 0
  /**
   * 需要合并行数 -- element使用
   */
  colspan = 0
  /**
   * 分销商信息
   */
  distributorInformation: DistributorsInfo = new DistributorsInfo()
  /**
   * 定价方式
   */
  pricingMethod = new DistributionPricingMethodsEnumsClass()
  /**
   * 价格
   */
  price = 0
  /**
   * 合计
   */
  total = new StatisticInfoItem()
  /**
   * 个人 - 线上支付
   */
  individualOnline = new StatisticInfoItem()
  /**
   * 集体 - 线上支付
   */
  collectivelyOnline = new StatisticInfoItem()
  /**
   * 集体 - 线下支付
   */
  collectivelyOffline = new StatisticInfoItem()
  /**
   * 导入 - 线下支付
   */
  importOffline = new StatisticInfoItem()

  static fromCommodityOpenReportResponse(dto: CommodityDistributorOpenReportInSupplierResponse[]) {
    const vo: DistributionGoodsOpeningStatisticsItem[] = []
    let number = 0
    dto.forEach((res) => {
      if (res.distributionPricingInfo?.length) {
        const item = new DistributionGoodsOpeningStatisticsItem()
        item.no = number + 1
        number++
        item.distributorInformation.goodsName = res.commodityName
        item.distributorInformation.distributorRegistration = new DistributionTiersEnumClass(res.distributionLevel)

        let regionStr = ''
        res.propertyList?.forEach((sku: PropertyResponse) => {
          if (['province', 'city', 'county'].includes(sku.propertyKey)) {
            if (sku.propertyValueName) {
              switch (sku.propertyKey) {
                case 'province':
                  regionStr = sku.propertyValueName + regionStr
                  break
                case 'city':
                  regionStr += '/' + sku.propertyValueName
                  break
                case 'county':
                  regionStr += '/' + sku.propertyValueName
                  break
              }
            }
          } else {
            if (sku.propertyKey == 'year') {
              const skuContent = `${sku.propertyKeyName}：${sku.propertyValue}`
              item.distributorInformation.trainingAttributes.push(skuContent)
            } else {
              const skuContent = `${sku.propertyKeyName}：${sku.propertyValueName}`
              item.distributorInformation.trainingAttributes.push(skuContent)
            }
          }
        })

        item.distributorInformation.trainingAttributes.push(`地区：${regionStr}`)
        // 处理各个分销商的数据
        const distributionPricingInfoList = res.distributionPricingInfo

        item.colspan += distributionPricingInfoList.length || 0
        // 赋值各个分销商信息
        item.distributorInformation.distributorsInfoIdName = res.distributorName
        // item.distributorInformation.distributorRegistration = new DistributionTiersEnumClass(res.distributionLevel)
        if (distributionPricingInfoList?.length) {
          // 处理需要打平得数据
          distributionPricingInfoList.forEach(
            (distributionPricingInfoItem: DistributionPricingResponse, index: number) => {
              if (index === 0) {
                item.total.open = distributionPricingInfoItem.discountSummary.tradeSuccessCount
                item.total.return = distributionPricingInfoItem.discountSummary.returnCount
                item.total.swapIntoAClass = distributionPricingInfoItem.discountSummary.exchangeInCount
                item.total.swapOutOfClass = distributionPricingInfoItem.discountSummary.exchangeOutCount
                item.total.count = distributionPricingInfoItem.discountSummary.netTradeSuccessCount
                // 赋值数据
                item.total.open = distributionPricingInfoItem.discountSummary.tradeSuccessCount
                item.total.return = distributionPricingInfoItem.discountSummary.returnCount
                item.total.swapIntoAClass = distributionPricingInfoItem.discountSummary.exchangeInCount
                item.total.swapOutOfClass = distributionPricingInfoItem.discountSummary.exchangeOutCount
                item.total.count = distributionPricingInfoItem.discountSummary.netTradeSuccessCount
                item.pricingMethod = new DistributionPricingMethodsEnumsClass(distributionPricingInfoItem.pricingType)
                item.price = distributionPricingInfoItem.tradeNetAmount
                DistributionGoodsOpeningStatisticsItem.disposeUseSpecialChannelInfos(
                  distributionPricingInfoItem.useSpecialChannelInfos,
                  item
                )
                vo.push(item)
              } else {
                // 分支数据做操作
                const temp = new DistributionGoodsOpeningStatisticsItem()
                temp.total.open = distributionPricingInfoItem.discountSummary.tradeSuccessCount
                temp.total.return = distributionPricingInfoItem.discountSummary.returnCount
                temp.total.swapIntoAClass = distributionPricingInfoItem.discountSummary.exchangeInCount
                temp.total.swapOutOfClass = distributionPricingInfoItem.discountSummary.exchangeOutCount
                temp.total.count = distributionPricingInfoItem.discountSummary.netTradeSuccessCount
                temp.pricingMethod = new DistributionPricingMethodsEnumsClass(distributionPricingInfoItem.pricingType)
                temp.price = distributionPricingInfoItem.tradeNetAmount
                temp.colspan = 0
                DistributionGoodsOpeningStatisticsItem.disposeUseSpecialChannelInfos(
                  distributionPricingInfoItem.useSpecialChannelInfos,
                  temp
                )
                vo.push(temp)
              }
            }
          )
        } else {
          vo.push(item)
        }
      } else {
        // vo.push(item)
      }
    })
    return vo
  }

  static fromCommodityDistributorOpenReportResponseInDistributor(dto: CommodityDistributorOpenReportResponse[]) {
    const vo: DistributionGoodsOpeningStatisticsItem[] = []
    dto.map((res, index) => {
      const item = new DistributionGoodsOpeningStatisticsItem()
      item.no = index + 1
      item.distributorInformation.goodsName = res.commodityName
      let regionStr = ''
      res.propertyList?.forEach((sku: PropertyResponse) => {
        if (['province', 'city', 'county'].includes(sku.propertyKey)) {
          if (sku.propertyValueName) {
            switch (sku.propertyKey) {
              case 'province':
                regionStr = sku.propertyValueName + regionStr
                break
              case 'city':
                regionStr += '/' + sku.propertyValueName
                break
              case 'county':
                regionStr += '/' + sku.propertyValueName
                break
            }
          }
        } else {
          if (sku.propertyKey == 'year') {
            const skuContent = `${sku.propertyKeyName}：${sku.propertyValue}`
            item.distributorInformation.trainingAttributes.push(skuContent)
          } else {
            const skuContent = `${sku.propertyKeyName}：${sku.propertyValueName}`
            item.distributorInformation.trainingAttributes.push(skuContent)
          }
        }
      })
      item.distributorInformation.trainingAttributes.push(`地区：${regionStr}`)

      // 处理统计数据
      if (res.distributorOpenReportList?.length) {
        res.distributorOpenReportList.map((distributorOpenReportItem: DistributorOpenReportResponse) => {
          const distributionPricingInfoList = distributorOpenReportItem.distributionPricingInfo
          if (distributionPricingInfoList.length) {
            // 处理需要合并数量
            item.colspan += distributionPricingInfoList.length || 0
            // 处理需要打平得数据
            distributionPricingInfoList.map(
              (distributionPricingInfoItem: DistributionPricingResponse, index: number) => {
                if (index === 0) {
                  item.pricingMethod = new DistributionPricingMethodsEnumsClass(distributionPricingInfoItem.pricingType)
                  item.price = distributionPricingInfoItem.tradeNetAmount
                  item.total.open = distributionPricingInfoItem.discountSummary.tradeSuccessCount
                  item.total.return = distributionPricingInfoItem.discountSummary.returnCount
                  item.total.swapIntoAClass = distributionPricingInfoItem.discountSummary.exchangeInCount
                  item.total.swapOutOfClass = distributionPricingInfoItem.discountSummary.exchangeOutCount
                  item.total.count = distributionPricingInfoItem.discountSummary.netTradeSuccessCount
                  DistributionGoodsOpeningStatisticsItem.disposeUseSpecialChannelInfos(
                    distributionPricingInfoItem.useSpecialChannelInfos,
                    item
                  )
                  vo.push(item)
                } else {
                  let temp = new DistributionGoodsOpeningStatisticsItem()
                  temp.pricingMethod = new DistributionPricingMethodsEnumsClass(distributionPricingInfoItem.pricingType)
                  temp.price = distributionPricingInfoItem.tradeNetAmount
                  temp.total.open = distributionPricingInfoItem.discountSummary.tradeSuccessCount
                  temp.total.return = distributionPricingInfoItem.discountSummary.returnCount
                  temp.total.swapIntoAClass = distributionPricingInfoItem.discountSummary.exchangeInCount
                  temp.total.swapOutOfClass = distributionPricingInfoItem.discountSummary.exchangeOutCount
                  temp.total.count = distributionPricingInfoItem.discountSummary.netTradeSuccessCount
                  temp = DistributionGoodsOpeningStatisticsItem.disposeUseSpecialChannelInfos(
                    distributionPricingInfoItem.useSpecialChannelInfos,
                    temp
                  )
                  vo.push(temp)
                }
              }
            )
          } else {
            vo.push(item)
          }
        })
      } else {
        vo.push(item)
      }
    })
    console.log('vo', vo)
    return vo
  }

  static disposeUseSpecialChannelInfos(dto: ChannelInfoDto[], vo: DistributionGoodsOpeningStatisticsItem) {
    vo.individualOnline = StatisticInfoItem.returnPayTypeData(
      dto,
      PurchaseChannelEnums.user_made_purchases,
      AccountTypeEunm.ONLINE
    )
    vo.collectivelyOnline = StatisticInfoItem.returnPayTypeData(
      dto,
      PurchaseChannelEnums.group_contributions,
      AccountTypeEunm.ONLINE
    )
    vo.collectivelyOffline = StatisticInfoItem.returnPayTypeData(
      dto,
      PurchaseChannelEnums.user_made_purchases,
      AccountTypeEunm.OFFLINE
    )
    vo.importOffline = StatisticInfoItem.returnPayTypeData(
      dto,
      PurchaseChannelEnums.admin_import,
      AccountTypeEunm.OFFLINE
    )
    return vo
  }
}
