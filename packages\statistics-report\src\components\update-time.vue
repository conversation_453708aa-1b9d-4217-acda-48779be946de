<!-- 数据更新日期 -->
<template>
  <!-- 数据更新时间 -->
  <el-card shadow="never" class="m-card is-header f-mb15 f-mh100 f-flex f-align-center f-justify-center">
    <div class="f-tc f-f22 f-fb">数据更新时间：{{ time }}</div>
  </el-card>
</template>

<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    // 数据更新时间
    @Prop({
      type: String,
      default: '-'
    })
    time: string
  }
</script>
