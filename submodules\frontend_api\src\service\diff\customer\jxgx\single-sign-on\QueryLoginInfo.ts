import PlatformJxjypxtyptJxgxSchool, {
  EnterIndexRequest,
  EnterIndexResponse
} from '@api/diff-gateway/platform-jxjypxtypt-jxgx-school'

export default class QueryLoginInfo {
  async queryLoginInfo(studentInfoKey: string) {
    if (!studentInfoKey) return new EnterIndexResponse()
    const request = new EnterIndexRequest()
    request.studentInfoKey = studentInfoKey
    const res = await PlatformJxjypxtyptJxgxSchool.enterIndex(request)
    return res?.data ?? new EnterIndexResponse()
  }
}
