<template>
  <div v-loading="loading" style="width: 100%; height: 100%" :element-loading-text="text"></div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import Query from '@hbfe/jxjy-admin-authentication/src/operation-login/models/Query'
  import ConnectState from '@hbfe/jxjy-admin-authentication/src/login/models/ConnectState'
  import AuthModule from '@api/service/common/auth/AuthModule'
  import ServiceTokenMixin from '@hbfe/jxjy-admin-common/src/mixins/ServiceTokenMixin'
  import ThirdPartyLoginEnum from '@/store/models/ThirdPartyLoginEnum'
  import { ConnectIdType, ThirdPartyType } from '@api/service/common/authentication/plugins/third-party'
  import ParticipatingUnitModule from '@api/service/management/participating-unit/ParticipatingUnitModule'
  import AccountType from '@hbfe-biz/biz-authentication/dist/enums/AccountType'

  const text = '正在登录'
  @Component
  export default class extends Vue {
    timer: any = 0
    text = text
    time = 0
    loading = false

    get query(): Query {
      return this.$route.query as any
    }

    /**
     * 根据缓存判断是哪种第三方登录
     */
    get isParticipatingUnitLogin() {
      const thirdPartyLogin = localStorage.getItem('ThirdPartyLogin')
      return thirdPartyLogin === ThirdPartyLoginEnum.PARTICIPATING_UNIT
    }

    async apply() {
      const href = this.$router.resolve('binding-3rd').href
      return await this.$authentication.thirdParty.apply(
        ThirdPartyType.BTAdmin,
        ConnectIdType.unionId,
        AccountType.admin,
        process.env.NODE_ENV === 'development' ? `${location.origin}/${href}` : `${location.origin}/admin/${href}`,
        false
      )
    }

    beforeDestroy() {
      window.clearInterval(this.timer)
    }

    isOnLoginStatus() {
      this.loading = true
      window.clearInterval(this.timer)
      this.timer = setInterval(() => {
        this.time++
        this.text = text.padEnd(text.length + this.time, '.')
        if (this.time >= 3) {
          this.time = 0
        }
      }, 1000)
    }

    /**
     * 参训单位/培训机构不存在培训平台的提示
     */
    async unExistConfirm() {
      try {
        await this.$confirm('系统暂无该机构帐号，请确认是否在补贴管理系统完成培训申报！', '系统提醒', {
          confirmButtonText: '前往补贴管理系统',
          closeOnPressEscape: false,
          closeOnClickModal: false,
          center: true
        })
        return window.location.replace('https://zypx.fjrst.cn/#/login')
      } catch (e) {
        // nothing
        window.location.replace(window.location.origin)
        window.location.reload()
      }
    }

    /**
     * 执行同步培训机构的动作
     */
    async doSyncTeachUnit(token: string) {
      const result = await AuthModule.doSyncTeachUnit(token)
      if (!result.status.isSuccess()) {
        // 不是培训机构
        if (result.status.code === 30001) {
          return this.apply()
        }
        await this.unExistConfirm()
        // 如果执行失败，将会抛出信息，并不再执行下面的动作。
        // window.location.replace(window.location.origin)
        const message = result.status.getMessage()
        return message && this.$message.error(message)
      }
      // todo 增加培训机构停用状态的判断逻辑
      let isStop = false
      if (result.data.trainingInstitutionStatus === 2) {
        isStop = true
        await this.$confirm('该机构已停用，请联系平台确认。', '系统提醒', {
          confirmButtonText: '确定',
          closeOnPressEscape: false,
          closeOnClickModal: false,
          center: true
        })
          .then(() => {
            this.apply()
          })
          .catch(() => {
            this.apply()
          })
      }
      return isStop
    }

    /**
     * 执行同步参训单位的动作
     */
    async doSyncParticipatingUnit(token: string) {
      const result = await ParticipatingUnitModule.syncTrainingInstitution(token)
      if (!result.isSuccess()) {
        await this.unExistConfirm()
        // 如果执行失败，将会抛出信息，并不再执行下面的动作。
        // window.location.replace(window.location.origin)
        const message = result.getMessage()
        return message && this.$message.error(message)
      }
    }

    async created() {
      try {
        this.loading = true
        if (!this.query.connectState) {
          try {
            return this.apply()
          } catch (e) {
            return
          } finally {
            this.loading = false
          }
        }
        this.isOnLoginStatus()
        const stateValue = Number(this.query.connectState)
        const token = this.query.token
        if (!ConnectState.isStateIllegal(stateValue) || !token) return this.$message.error('参数非法')
        const connectState = new ConnectState(stateValue)
        if (this.isParticipatingUnitLogin) {
          // 执行同步参训单位的动作
          await this.doSyncParticipatingUnit(token)
        } else {
          // 执行同步培训机构的动作
          const result = await this.doSyncTeachUnit(token)
          if (result) {
            return
          }
        }
        try {
          ServiceTokenMixin.shareInstance().clearService()
          await this.$authentication.thirdParty.login(token)
          // await this.$router.replace('/home')
          window.location.replace(this.$router.resolve('/home').href)
          window.location.reload()
          // window.location.reload()
        } catch (e) {
          console.log('thirdPartye', e)
          if (e.code !== 200) {
            this.$message.error(e.message || '登录失败')
          }
          // 登录失败
        }
      } catch (e2) {
        console.log('thridE=', e2)
      }
    }
  }
</script>
