<template>
  <div class="drawer-bd">
    <div>
      <el-row :gutter="16">
        <el-form :inline="true" label-width="auto" @keyup.enter.native="doSearch">
          <el-col :md="10" :xl="8">
            <el-form-item label="课程名称">
              <el-input clearable placeholder="请输入课程名称" v-model="pageQueryParam.name" />
            </el-form-item>
          </el-col>
          <el-col :md="10" :xl="8">
            <el-form-item label="课程分类">
              <!-- <biz-course-category v-model="pageQueryParam.categoryIdList"></biz-course-category> -->
              <course-category
                :filterable="true"
                v-model="pageQueryParam.categoryIdList"
                :showRootNode="false"
                :excellentCourseConfig="excellentCourseConfig"
                placeholder="全部"
                ref="bizCourseCategory"
                :check-strictly="false"
              ></course-category>
            </el-form-item>
          </el-col>
          <el-col :md="4" :xl="4" class="f-fr">
            <el-form-item class="f-tr">
              <el-button type="primary" @click="doSearch">查询</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <el-table :data="pageList" v-loading="query.loading" size="mini">
        <el-table-column type="index" label="No." width="50" align="center" header-align="center"></el-table-column>
        <el-table-column prop="name" label="课程名称">
          <template slot-scope="{ row }">
            <div class="is-cache" v-if="idMap[row.id].isCache"></div>
            {{ row.name }}
          </template>
        </el-table-column>
        <el-table-column prop="period" label="学时" width="100"></el-table-column>
        <el-table-column label="操作" width="150" align="center" header-align="center">
          <template slot-scope="{ row }">
            <div style="display: flex; align-items: center; justify-content: center">
              <el-button type="text" @click="goDetail(row.id)" size="mini"> 详情 </el-button>
              <span style="margin: 0px 5px; color: #1f86f0">|</span>
              <el-button type="text" @click="select(row)" size="mini" style="width: 50px; text-align: left">
                {{ idMap[row.id].item ? '取消选择' : '选择' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </div>

    <div class="select-course-foot">
      <el-button @click="closeDrawer">取 消</el-button>
      <el-badge :value="getCachesKey().length">
        <el-button class="f-ml10" type="primary" @click="chooseCertain">确 定</el-button>
      </el-badge>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .select-course-foot {
    text-align: center;
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 10px;
  }

  .is-cache {
    width: 8px;
    background: #e89820;
    height: 8px;
    border-radius: 50%;
    display: inline-flex;
    align-items: flex-start;
    justify-content: flex-start;
    margin-right: 5px;
  }
</style>

<script lang="ts">
  import { Component, Emit, Inject, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import QueryCourseListParam from '@api/service/management/resource/course//query/vo/QueryCourseListParam'
  import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'
  import CourseCategory from '@hbfe/jxjy-admin-platform/src/function/components/course-category.vue'
  import ExcellentCourseConfig from '@api/service/management/online-school-config/excellent-course/query/ExcellentCourseConfig'
  import ExcellentCourseCategoryConfig from '@api/service/management/online-school-config/excellent-course/query/vo/ExcellentCourseCategoryConfig'

  interface Selected {
    [key: string]: {
      isCache: boolean
      item: CourseListDetail
    }
  }

  @Component({
    components: { CourseCategory }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    @Inject({
      from: 'validateChooseCourse',
      default() {
        return () => {
          return true
        }
      }
    })
    validateChooseCourse: (courses: Array<CourseListDetail>, removeCourses: Array<CourseListDetail>) => {}

    page: UiPage

    query: Query = new Query()

    pageQueryParam: QueryCourseListParam = new QueryCourseListParam()

    pageList: Array<CourseListDetail> = new Array<CourseListDetail>()

    removeList: Array<CourseListDetail> = new Array<CourseListDetail>()

    @Prop({
      type: String,
      default: '请选择相关课程'
    })
    placeholder: string
    @Prop({
      type: String,
      default: ''
    })
    categoryId: string

    @Prop({
      type: ExcellentCourseConfig,
      default: new ExcellentCourseConfig()
    })
    excellentCourseConfig: ExcellentCourseConfig

    @Ref('bizCourseCategory') bizCourseCategory: CourseCategory

    isShow = false
    showClear = false
    loading = false
    idMap: Selected = {}

    // 显示
    async created() {
      this.isShow = true
      await this.doSearch()
    }

    // 选中的时候
    @Emit('update:value')
    select(course: CourseListDetail) {
      this.showClear = true
      //   console.log(this.idMap[course.id].item)
      if (this.idMap[course.id].item) {
        // 取消选中项添加至移除列表
        this.removeList.push(this.idMap[course.id].item)
        // 取消选择
        this.idMap[course.id] = {
          isCache: false,
          item: undefined
        }
      } else {
        this.idMap[course.id] = {
          isCache: true,
          item: course
        }
        //选中时若移除列表中存在该项，移除出列表
        const removeItemIndex = this.removeList.findIndex((item) => item?.id == course.id)
        if (removeItemIndex != -1) this.removeList.splice(removeItemIndex, 1)
      }
      return course.id
    }

    @Prop({
      type: Array,
      default: []
    })
    selected: Array<string>

    @Emit('cancel')
    closeDrawer() {
      this.getCachesKey().forEach((key: string) => {
        this.idMap[key].item = undefined
        this.idMap[key].isCache = false
      })
      this.isShow = false
    }

    getCachesKey() {
      const caches = new Set()

      Object.keys(this.idMap).forEach((key: string) => {
        const selected = this.idMap[key]
        if (selected.isCache) {
          caches.add(key)
        }
      })

      return Array.from(caches.values())
    }

    // 清除
    clear() {
      this.isShow = false
    }

    @Watch('selected')
    selectedChange() {
      //   this.idMap = {} // 每次判断是否选中前都要清除旧数据
      this.pageList.forEach((item) => {
        const hasInclude = this.selected.find((id) => id === item.id)
        const setItem = hasInclude ? item : undefined
        if (this.idMap[item.id]) {
          if (!this.idMap[item.id].item) {
            this.$set(this.idMap, item.id, {
              isCache: false,
              item: setItem
            })
          }
        } else {
          this.$set(this.idMap, item.id, {
            isCache: false,
            item: setItem
          })
        }
      })
    }

    async doSearch() {
      this.query.loading = true
      try {
        this.pageList = await ResourceModule.courseFactory.queryCourse.queryCoursePage(this.page, this.pageQueryParam)
        this.selectedChange()
      } catch (e) {
        // nothing
      } finally {
        this.query.loading = false
      }
    }

    chooseCertain() {
      // 获取新增项
      const newest = {}
      Object.keys(this.idMap).forEach((id) => {
        if (!this.selected.includes(id)) {
          newest[id] = this.idMap[id]
        }
      })
      const selected = Object.keys(newest)
        .map((id: string) => {
          return this.idMap[id].item
        })
        .filter((item) => !!item)
      //获取移除项
      this.removeList = this.removeList.filter((item: CourseListDetail) => {
        // todo
        if (this.selected.includes(item?.id)) return item
      })
      if (this.validateChooseCourse(selected, this.removeList)) {
        this.$emit('confirm', selected)
        console.log(this.removeList, '=========removeList')
        this.$emit('remove', this.removeList)
        this.closeDrawer()
      }
    }

    // 前往详情
    goDetail(id: string) {
      this.$emit('closeDrawer')
      this.$router.push(`/resource/course/detail/${id}`)
    }

    // 回显课程分类
    @Watch('categoryId', { immediate: true })
    async categoryIdChange(newCategoryId: string) {
      if (newCategoryId) {
        // 搜索入参
        this.pageQueryParam.categoryIdList = [newCategoryId]
        // biz组件回显
        await this.$nextTick()
        this.bizCourseCategory.categoryIdList = [newCategoryId]
      } else {
        this.pageQueryParam.categoryIdList = new Array<string>()
        await this.$nextTick()
        this.bizCourseCategory.categoryIdList = new Array<string>()
      }
    }
  }
</script>
