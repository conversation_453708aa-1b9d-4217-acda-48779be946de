import ExamLearningConfig from '@api/service/common/scheme/model/schemeDto/exam-learning/config/ExamLearningConfig'
import ExamLearningAssessSetting from '@api/service/common/scheme/model/schemeDto/exam-learning/assess-setting/ExamLearningAssessSetting'
import ExamLearningPrecondition from '@api/service/common/scheme/model/schemeDto/exam-learning/precondition/ExamLearningPrecondition'

/**
 * @description 考试学习方式
 */
class ExamLearning {
  /**
   * 学习方式id
   */
  id: string
  /**
   * 考试配置
   */
  config: ExamLearningConfig
  /**
   * 考核配置
   */
  assessSetting: ExamLearningAssessSetting
  /**
   * 前置条件
   */
  precondition: ExamLearningPrecondition
  /**
   * 操作类型
   */
  operation: number
}

export default ExamLearning
