import QueryH5PortalConfig from '@api/service/customer/online-school-config/online-school-partal-config/query/QueryH5PortalConfig'
import QueryWebPortalConfig from '@api/service/customer/online-school-config/online-school-partal-config/query/QueryWebPortalConfig'
import QueryCollectiveSignUp from './query/QueryCollectiveSignUp'

/**
 * @description 网校配置工厂
 */
class QueryOnlineSchoolPortalConfigFactory {
  /**
   * @description 查询H5门户配置类
   */
  get queryH5PortalConfig() {
    return QueryH5PortalConfig
  }

  /**
   * @description 查询web门户配置类
   */
  get queryWebPortalConfig() {
    return QueryWebPortalConfig
  }

  /**
   * @description 查询集体报名配置
   */
  get queryCollectiveSignUp() {
    return QueryCollectiveSignUp
  }
}

export default QueryOnlineSchoolPortalConfigFactory
