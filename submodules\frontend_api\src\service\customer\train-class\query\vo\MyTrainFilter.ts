import {
  DataAnalysisRequest,
  LearningRegisterRequest,
  SchemeRequest,
  StudentLearningRequest,
  UserRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'

/**
 * 学员培训方案学习查询条件
 <AUTHOR>
 @version 1.0
 @date 2022/1/17 11:40
 */
export class StudentSchemeLearningRequest {
  /**
   * 学员信息
   */
  student?: UserRequest
  /**
   * 报名信息
   */
  learningRegister?: LearningRegisterRequest
  /**
   * 培训班信息
   */
  scheme?: SchemeRequest = new SchemeRequest()
  /**
   * 学习信息
   */
  studentLearning?: StudentLearningRequest = new StudentLearningRequest()
  /**
   * 数据分析信息
   */
  dataAnalysis?: DataAnalysisRequest

  /**
   * 是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1
   */
  saleChannels? = new Array<number>()
  /**
   * 专题Id  用于不同专题域名 查询对应专题的培训班
   */
  trainingChannelId? = ''
}
