/**
 * @description 属性
 */
class Property<T = string> {
  /**
   * 是否隐藏
   */
  hidden = false
  /**
   * 父级id
   * @description 用于有父子级别关系的
   */
  parentId = ''
  /**
   * sku属性值id
   */
  skuPropertyValueId: T = ('' as unknown) as T
  /**
   * sku属性值名称
   */
  skuPropertyName = ''

  /**
   * 构造函数
   * @param skuPropertyValueId 属性值id
   * @param skuPropertyName 属性值
   */
  constructor(skuPropertyValueId?: T, skuPropertyName?: string) {
    if (skuPropertyValueId || (typeof skuPropertyValueId === 'number' && skuPropertyValueId === 0)) {
      this.skuPropertyValueId = skuPropertyValueId
    }
    if (skuPropertyName) {
      this.skuPropertyName = skuPropertyName
    }
  }
}

export default Property
