import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 订单类型枚举
 */
export enum OrderTypeEnum {
  // 个人订单
  Person = 1,
  // 集体报名
  Collective
}

class OrderTypes extends AbstractEnum<OrderTypeEnum> {
  static enum = OrderTypeEnum
  constructor(status?: OrderTypeEnum) {
    super()
    this.current = status
    this.map.set(OrderTypeEnum.Person, '个人订单')
    this.map.set(OrderTypeEnum.Collective, '集体报名')
  }
}

export default new OrderTypes()
