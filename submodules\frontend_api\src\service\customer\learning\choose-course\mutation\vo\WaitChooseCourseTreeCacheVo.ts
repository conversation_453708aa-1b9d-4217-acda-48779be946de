import ChooseCourseVo from '@api/service/customer/learning/choose-course/mutation/vo/ChooseCourseVo'
import TrainingCourseOutline from '@api/service/customer/course/query/vo/TrainingCourseOutline'

/**
 * @description 待选可选课程列表详情模型
 */
class WaitChooseCourseTreeCacheVo {
  /**
   * 课程学习大纲父级Id
   */
  outlineParentId: string
  /**
   * 课程学习大纲Id
   */
  outlineId: string
  /**
   * 课程学习大纲名称
   */
  outlineName: string
  /**
   * 待选可选课程列表
   */
  chooseCourseList: Array<ChooseCourseVo> = new Array<ChooseCourseVo>()
  /**
   * 下一级大纲
   */
  children: Array<WaitChooseCourseTreeCacheVo> = new Array<WaitChooseCourseTreeCacheVo>()

  static from(response: TrainingCourseOutline) {
    const waitChooseCourseTree = new WaitChooseCourseTreeCacheVo()
    waitChooseCourseTree.outlineId = response.id
    waitChooseCourseTree.outlineParentId = response.parentId
    waitChooseCourseTree.outlineName = response.name
    waitChooseCourseTree.chooseCourseList = new Array<ChooseCourseVo>()
    // waitChooseCourseTree.children = new Array<WaitChooseCourseTreeCacheVo>()
    // response.children.map(item => waitChooseCourseTree.children.push(WaitChooseCourseTreeCacheVo.from(item)))
    waitChooseCourseTree.children = response.children?.map(item => WaitChooseCourseTreeCacheVo.from(item))
    return waitChooseCourseTree
  }

  hasChildren() {
    return this.children.length
  }
}

export default WaitChooseCourseTreeCacheVo
