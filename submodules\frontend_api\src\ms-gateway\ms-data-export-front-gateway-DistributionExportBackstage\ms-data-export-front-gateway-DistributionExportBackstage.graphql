"""独立部署的微服务,K8S服务名:ms-data-export-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""导出培训商品开通统计（分销商）
		@param request
		@return
	"""
	exportCommodityDistributorOpenReportInDistributor(request:StatisticTradeRecordRequest):Boolean!
	"""导出培训商品开通统计明细报表"""
	exportCommodityOpenStatisticsDetailExcelInSupplier(request:StatisticTradeRecordRequest):Boolean!
	"""导出培训商品开通统计汇总报表"""
	exportCommodityOpenStatisticsSummaryExcelInSupplier(request:StatisticTradeRecordRequest):Boolean!
	"""分销产品数据导出"""
	exportDistributionCommodityExcelInSupplier(request:OnlineSchoolCommodityRequest):Boolean!
	"""分销商品管理数据导出"""
	exportDistributionCommodityManageExcelInSupplier(request:DistributorCommodityAndRelationRequest):Boolean!
	"""导出分销商销售统计报表
		@return
	"""
	exportDistributorSalesStatisticsExcelInSupplier(request:StatisticTradeRecordRequest):Boolean!
	"""导出分销销售统计-我的分销（分销商）
		@param request
		@return
	"""
	exportMyTradeRecordInDistributor(request:StatisticTradeRecordRequest):Boolean!
	"""导出分销销售统计-下级分销商（分销商）
		@param request
		@return
	"""
	exportSubDistributorSellStatisticInDistributor(request:StatisticTradeRecordRequest):Boolean!
	"""功能描述：分页查询导出任务组(系统管理域)
		@param jobGroupRequest : 任务查询条件
		@return : void
		@Author： sjm
		@Date： 2022/1/18 15:14
	"""
	listExportTaskGroupInfoInAdmin(jobGroupRequest:JobGroupRequest):[JobGroupResponse]
	"""功能描述：分页查询导出任务组(分销商)
		@param jobGroupRequest : 任务查询条件
		@return : void
		@Author： sjm
		@Date： 2022/1/18 15:14
	"""
	listExportTaskGroupInfoInDistributor(jobGroupRequest:JobGroupRequest):[JobGroupResponse]
	"""功能描述：分页查询导出任务组
		@param jobGroupRequest : 任务查询条件
		@return : void
		@Author： sjm
		@Date： 2022/1/18 15:14
	"""
	listExportTaskGroupInfoInServicer(jobGroupRequest:JobGroupRequest):[JobGroupResponse]
	"""功能描述：分页查询导出任务组(供应商)
		@param jobGroupRequest : 任务查询条件
		@return : void
		@Author： sjm
		@Date： 2022/1/18 15:14
	"""
	listExportTaskGroupInfoInSupplier(jobGroupRequest:JobGroupRequest):[JobGroupResponse]
	"""功能描述：分页查询我的导出任务日志信息列表
		@param jobRequest : 任务查询条件
		@return : void
		@Author： wtl
		@Date： 2022/1/18 15:14
	"""
	pageExportTaskInfoInMyself(page:Page,jobRequest:JobRequest):UserJobLogResponsePage @page(for:"UserJobLogResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""异步任务组名返回对象"""
input JobGroupRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.asyncTask.request.JobGroupRequest") {
	"""任务组key"""
	group:String
	"""任务组名（模糊查询）"""
	groupName:String
}
"""功能描述：任务查询参数
	@Author： wtl
	@Date： 2022/1/18 15:13
"""
input JobRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.asyncTask.request.JobRequest") {
	"""任务组名（必填）"""
	group:String
	"""任务组名匹配方式（EQ：完全匹配 LIKE：模糊匹配[*group*] LLIKE：左模糊匹配[group*] RLIKE：右模糊匹配[*group]，不传值默认为完全匹配）"""
	groupOperator:String
	"""任务名（模糊查询）"""
	jobName:String
	"""任务状态(executing:运行中 executed:运行完成 fail:运行失败)
		@see UserJobState
	"""
	jobState:String
	"""任务执行时间 yyyy-MM-dd HH:mm:ss"""
	executeTimeScope:DateScopeRequest
	"""异步任务处理结果（true:成功 false:失败）"""
	jobResult:Boolean
}
"""供应商授权出的商品"""
input DistributorCommodityAndRelationRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.distribution.request.DistributorCommodityAndRelationRequest") {
	"""分销商商品id集合"""
	distributorCommodityIdList:[String]
	"""商品id集合"""
	commodityIdList:[String]
	"""网校id"""
	onlineSchoolId:String
	"""分销商商品名称"""
	saleTitle:String
	"""培训方案名称"""
	schemeName:String
	"""培训方案类型"""
	schemeTypeList:[String]
	"""商品sku属性"""
	propertyList:[PropertyRequest]
	"""分销商id集合"""
	distributorIdList:[String]
	"""分销商等级"""
	distributorLevel:Int
	"""分销状态
		0-开启 1-关闭
		商品的分销开始时间、结束时间作为判断
	"""
	distributionStatus:Int
	"""分销地区路径"""
	contractDistributionRegionPathList:[String]
	"""产品分销授权是否展示
		0-不展示 1-展示
	"""
	relationIsShow:Boolean
	"""商品是否门户可见
		0-不展示 1-展示
	"""
	isShow:Boolean
	"""网校销售状态
		0-开启 1-关闭
		商品的网校销售开始时间、结束时间作为判断
	"""
	onlineSchoolStatus:Int
	"""来源类型"""
	commoditySourceTypeList:[Int]
	"""定价方案状态"""
	statusList:[Int]
	"""定价方案id"""
	productPricingSchemeIdList:[String]
	"""授权价格类型 1-固定 2-区间"""
	priceType:Int
	"""分销价格范围查询-最大价格"""
	maxPrice:BigDecimal
	"""分销价格范围查询-最小价格"""
	minPrice:BigDecimal
	"""是否已启用定价方案
		分销商品(授权)存在一个及以上定价方案视为启用，但价格类型为固定价格时也未启用
	"""
	enablePricingScheme:Boolean
	jobName:String
}
"""网校商品查询条件"""
input OnlineSchoolCommodityRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.distribution.request.OnlineSchoolCommodityRequest") {
	"""网校商品id集合"""
	commodityIdList:[String]
	"""网校id"""
	onlineSchoolId:String
	"""网校商品名称"""
	saleTitle:String
	"""培训方案名称"""
	schemeName:String
	"""培训方案类型
		chooseCourseLearning - 选课规则  autonomousCourseLearning - 自主学习
	"""
	schemeTypeList:[String]
	"""网校商品上下架状态
		0-已下架、1-已上架
	"""
	shelveStatusList:[Int]
	"""销售状态
		0-关闭 1-开启
		方案的报名开始时间、报名结束时间来判断
	"""
	saleStatus:Int
	"""商品sku属性"""
	propertyList:[PropertyRequest]
	"""商品创建时间排序方式
		0-升序 1-降序
	"""
	commodityCreatedTimeSort:Int
}
"""交易统计请求参数
	<AUTHOR>
"""
input StatisticTradeRecordRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.distribution.request.StatisticTradeRecordRequest") {
	"""分销商id集合"""
	distributorIdList:[String]
	"""供应商id集合"""
	supplierIdList:[String]
	"""商品名称 模糊匹配"""
	commodityName:String
	"""网校id集合 List"""
	onlineSchoolList:[String]
	"""商品ID集合"""
	commodityIdList:[String]
	"""商品售价范围"""
	commodityPriceScope:DoubleScopeRequest
	"""查询时间范围"""
	queryDateScope:DateScopeRequest
	"""查询方式
		仅查询自己
		只查询下级分销商
		包含自己和下级分销商
		@see QueryWayType
	"""
	queryWayType:QueryWayType
	jobName:String
}
"""商品sku属性查询条件"""
input PropertyRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.distribution.request.nested.PropertyRequest") {
	"""商品skuKey"""
	propertyKey:String
	"""商品skuValue"""
	propertyValue:String
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
"""@author: yxw
	@date: 2024/1/18
	@time: 15:01
	@description：
"""
enum QueryWayType @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.constants.QueryWayType") {
	"""仅查询自己"""
	ONLY_ME
	"""只查询下级分销商"""
	ONLY_MY_DISTRIBUTOR
	"""包含自己和下级分销商"""
	ALL
}
"""异步任务组名返回对象"""
type JobGroupResponse @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.asyncTask.response.JobGroupResponse") {
	"""异步任务组key"""
	group:String
	"""异步任务组名"""
	groupName:String
	"""排序大小"""
	order:Int!
	"""所在域"""
	domain:[String]
}
"""功能描述：异步任务日志返回对象
	@Author： wtl
	@Date： 2022/4/11 17:18
"""
type UserJobLogResponse @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.asyncTask.response.UserJobLogResponse") {
	"""任务id"""
	jobId:String
	"""任务组名"""
	group:String
	"""任务名"""
	jobName:String
	"""任务开始时间"""
	beginTime:DateTime
	"""任务结束时间"""
	endTime:DateTime
	"""任务状态(executing:运行中 executed:运行完成 fail:运行失败)
		@see UserJobState
	"""
	jobState:String
	"""异步任务处理结果（true:成功 false:失败）"""
	jobResult:Boolean
	"""任务执行成功或失败的信息"""
	message:String
	"""导出文件路径"""
	exportFilePath:String
	"""是否受保护"""
	isProtected:Boolean!
	"""资源id"""
	fileResourceId:String
	"""操作人id"""
	operatorUserId:String
	"""操作人帐户id"""
	operatorAccountId:String
}

scalar List
type UserJobLogResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserJobLogResponse]}
