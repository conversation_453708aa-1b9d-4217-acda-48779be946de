<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <!--条件查询-->
      <template
        v-if="$hasPermission('refundReconciliation,refundReconciliationfx,refundReconciliationzt')"
        desc="refundReconciliation:退款订单对账,refundReconciliationfx:分销退款订单对账,refundReconciliationzt:专题退款订单对账"
        actions="refundReconciliation:doSearch,exportDataty,@BizPortalSelect,@BizDistributorSelect#refundReconciliationfx:doSearchfx,exportDatafx,@BizPortalDistributorSelect#refundReconciliationzt:doSearchZt,exportDataZt,@BizPortalSelect,@BizDistributorSelect"
      >
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="收款账号" v-if="!isZtlogin">
                <el-input
                  id="input1"
                  v-model="accountName"
                  clearable
                  placeholder="请选择收款账号"
                  @focus="editInvoicePopup()"
                />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="批次号">
                <el-input v-model="refundCheckAccountParam.orderId" clearable placeholder="请输入批次号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="交易流水号">
                <el-input v-model="refundCheckAccountParam.batchId" clearable placeholder="请输入交易流水号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="退款单号">
                <el-input v-model="refundCheckAccountParam.refundId" clearable placeholder="请输入退款单号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="退款成功时间">
                <double-date-picker
                  :begin-create-time.sync="refundCheckAccountParam.startDate"
                  :end-create-time.sync="refundCheckAccountParam.endDate"
                ></double-date-picker>
              </el-form-item>
            </el-col>

            <el-col :sm="12" :md="8" :xl="6" v-if="!isFxlogin && !isZtlogin">
              <el-form-item label="销售渠道">
                <el-select
                  v-model="refundCheckAccountParam.saleSource"
                  clearable
                  filterable
                  placeholder="请选择销售渠道"
                >
                  <el-option
                    v-for="item in saleChannelList"
                    :key="item.code"
                    :value="item.code"
                    :label="item.desc"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :sm="12" :md="8" :xl="6" v-if="topPicNameFilterShow && !isFxlogin">
              <el-form-item label="专题名称">
                <el-input
                  v-model="refundCheckAccountParam.specialSubjectName"
                  clearable
                  placeholder="请输入专题进行查询"
                />
              </el-form-item>
            </el-col>

            <!--  v-if="isFXshow" -->
            <el-col :sm="12" :md="8" :xl="6" v-if="!isFxlogin && isHadFxAbility && !isZtlogin">
              <el-form-item label="分销商">
                <biz-distributor-select
                  v-model="refundCheckAccountParam.distributorId"
                  :name="distributorName"
                ></biz-distributor-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" v-if="!isFxlogin && isHadFxAbility && !isZtlogin">
              <el-form-item label="推广门户简称">
                <biz-portal-select
                  v-model="refundCheckAccountParam.promotionPortalId"
                  :name="promotionPortalName"
                ></biz-portal-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" v-if="isFxlogin && isHadFxAbility && !isZtlogin">
              <el-form-item v-if="isFxlogin && isHadFxAbility" label="推广门户简称">
                <biz-portal-distributor-select
                  v-model="refundCheckAccountParam.promotionPortalId"
                  :name="promotionPortalName"
                ></biz-portal-distributor-select>
              </el-form-item>
            </el-col>
            <!--  v-if="isFXshow" -->
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="缴费方式">
                <payment-mode v-model="refundCheckAccountParam.paymentMethod"></payment-mode>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary" @click="search">查询</el-button>
                <el-button @click="exportData">导出列表数据</el-button>
                <el-button @click="reset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert">
            <div class="f-c6">
              当前共有 <span class="f-fb f-co">{{ refundTradeCount }}</span> 笔退款订单，退款总额
              <span class="f-fb f-co">¥ {{ refundAmountCount }}</span
              >。
            </div>
          </el-alert>
        </div>
        <!--表格-->

        <el-table
          stripe
          :data="refundCheckAccountListResponse"
          max-height="500px"
          v-loading="query.loading"
          @sort-change="sortChange"
          class="m-table f-mt10"
          ref="batchRef"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="集体报名批次号" min-width="220" fixed="left">
            <!-- <template>
            2112071509467489926
            <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
              <i class="el-icon-document-copy f-link-gray f-ml5 f-c9"></i>
              <div slot="content">复制集体报名批次号</div>
            </el-tooltip>
          </template> -->
            <template slot-scope="scope">
              <hb-copy :content="scope.row.orderId"></hb-copy>
              {{ scope.row.orderId }}
              <p>
                <el-tag type="success" size="mini" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="交易流水号" prop="batchId" min-width="300"> </el-table-column>
          <el-table-column label="缴费方式" min-width="120" align="center">
            <template slot-scope="scope">{{ PaymentMethod.map.get(scope.row.paymentMethod) }}</template>
          </el-table-column>
          <el-table-column label="退款单号" min-width="300">
            <template slot-scope="scope"
              ><a href="#" @click="goRefundDetail(scope.row.refundId)" class="f-link f-cb f-underline">{{
                scope.row.refundId
              }}</a></template
            >
          </el-table-column>
          <el-table-column label="退款成功时间" min-width="180" prop="startDate" sortable>
            <!-- <template>2020-11-11 12:20:20</template> -->
          </el-table-column>
          <el-table-column label="购买人信息" min-width="240">
            <template slot-scope="scope">
              <p>姓名：{{ scope.row.name || '-' }}</p>
              <!-- <p>证件号：{{ scope.row.idCard || '-' }}</p> -->
              <p>手机号：{{ scope.row.phone || '-' }}</p>
            </template>
          </el-table-column>
          <el-table-column label="退款人数" min-width="240">
            <template slot-scope="scope">
              <p>{{ scope.row.refundCount || '-' }}</p>
            </template>
          </el-table-column>
          <el-table-column label="退款金额(元)" width="140" prop="money" align="right"> </el-table-column>
        </el-table>
        <!--分页-->
        <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务管理-集体报名退款对账</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
          </div>
        </el-dialog>
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
        <template v-if="$hasPermission('editInvoicePopup')" desc="选择收款账号" actions="@accountNumber">
          <account-number
            :visible.sync="editInvoiceDialog"
            ref="accountNumberRef"
            :get-data="getData"
            @getAccountNumber="getAccountNumber"
          ></account-number>
        </template>
      </template>
    </el-card>
  </div>
</template>

<script lang="ts">
  import {
    BatchReturnOrderSortRequest,
    SortPolicy
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import ReceiveAccountVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountVo'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import QueryCheckAccount from '@api/service/management/trade/batch/checkAccount/query/QueryCheckAccount'
  import RefundCheckAccountListResponse from '@api/service/management/trade/batch/checkAccount/query/vo/RefundCheckAccountListResponse'
  import RefundCheckAccountParam from '@api/service/management/trade/batch/checkAccount/query/vo/RefundCheckAccountParam'
  import PaymentMethod from '@api/service/management/trade/batch/order/enum/PaymentMethod'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-distributor-select.vue'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import PaymentMode from '@hbfe/jxjy-admin-trade/src/order/collective/components/payment-mode.vue'
  import accountNumber from '@hbfe/jxjy-admin-trade/src/refund/components/account-number.vue'

  import MutationCheckAccountInTrainingChannel from '@api/service/management/trade/batch/checkAccount/mutation/MutationCheckAccountInTrainingChannel'
  import QueryCheckAccountInTrainingChannel from '@api/service/management/trade/batch/checkAccount/query/QueryCheckAccountInTrainingChannel'
  import { Query, UiPage } from '@hbfe/common'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import { Component, Ref, Vue } from 'vue-property-decorator'

  @Component({
    components: {
      DoubleDatePicker,
      accountNumber,
      BizPortalSelect,
      BizDistributorSelect,
      BizPortalDistributorSelect,
      PaymentMode
    }
  })
  export default class extends Vue {
    @Ref('accountNumberRef') accountNumberRef: any
    SaleChannelEnum = SaleChannelEnum

    select = ''
    input = ''
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    page: UiPage
    query: Query = new Query()
    form = {
      data1: ''
    }
    loading = false // 加载中
    accountName = ''
    getData = new ReceiveAccountVo()
    queryCheckAccount: QueryCheckAccount = TradeModule.batchTradeBatchFactor.checkAccountFactor.queryCheckAccount
    refundCheckAccountListResponse: Array<RefundCheckAccountListResponse> = new Array<RefundCheckAccountListResponse>()
    refundCheckAccountParam: RefundCheckAccountParam = new RefundCheckAccountParam()
    // 缴费方式枚举
    PaymentMethod = PaymentMethod
    exportQueryParam: RefundCheckAccountParam = new RefundCheckAccountParam()
    sortRequest: Array<BatchReturnOrderSortRequest> = new Array<BatchReturnOrderSortRequest>()
    //导出成功弹窗
    exportSuccessVisible = false
    /**
     * 打开-弹窗标识
     */
    editInvoiceDialog = false
    queryZtReconciliation = new QueryCheckAccountInTrainingChannel()
    mutationZtCheckAccount = new MutationCheckAccountInTrainingChannel()
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable
    promotionPortalName = ''
    distributorName = ''
    // 获取销售渠道列表
    saleChannelList = [
      {
        code: SaleChannelEnum.self,
        desc: '网校'
      },
      {
        code: SaleChannelEnum.distribution,
        desc: '分销'
      },
      {
        code: SaleChannelEnum.topic,
        desc: '专题'
      }
    ]

    // 专题名称筛选显示
    get topPicNameFilterShow() {
      return (
        this.refundCheckAccountParam.saleSource === SaleChannelEnum.topic ||
        (!this.refundCheckAccountParam.saleSource && this.refundCheckAccountParam.saleSource !== SaleChannelEnum.self)
      )
    }
    get refundTradeCount() {
      if (!this.isZtlogin) {
        return this.queryCheckAccount.refundTradeCount
      } else {
        return this.queryZtReconciliation.refundTradeCount
      }
    }
    get refundAmountCount() {
      if (!this.isZtlogin) {
        return this.queryCheckAccount.refundAmountCount
      } else {
        return this.queryZtReconciliation.refundAmountCount
      }
    }
    constructor() {
      super()
      if (this.isFxlogin && this.isHadFxAbility) {
        this.page = new UiPage(this.doSearchfx, this.doSearchfx)
      } else if (this.isZtlogin) {
        this.page = new UiPage(this.doSearchZt, this.doSearchZt)
      } else {
        this.page = new UiPage(this.doSearch, this.doSearch)
      }
    }
    async doSearch() {
      this.query.loading = true
      try {
        this.refundCheckAccountListResponse = await this.queryCheckAccount.queryOfRefundOrder(
          this.page,
          this.refundCheckAccountParam
        )
        //处理切换页数后行数错位问题
        ;(this.$refs['batchRef'] as any)?.doLayout()
      } catch (e) {
        console.log(e, '退款订单查询列表失败')
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['batchRef'] as any)?.doLayout()
        this.query.loading = false
      }
      this.query.loading = false
    }

    async doSearchfx() {
      this.query.loading = true
      try {
        this.refundCheckAccountListResponse = await this.queryCheckAccount.queryOfFxRefundOrder(
          this.page,
          this.refundCheckAccountParam
        )
        //处理切换页数后行数错位问题
        ;(this.$refs['batchRef'] as any)?.doLayout()
      } catch (e) {
        console.log(e, '退款订单查询列表失败')
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['batchRef'] as any)?.doLayout()
        this.query.loading = false
      }
      this.query.loading = false
    }
    async doSearchZt() {
      this.query.loading = true
      try {
        this.refundCheckAccountListResponse = await this.queryZtReconciliation.queryOfRefundOrder(
          this.page,
          this.refundCheckAccountParam
        )
        //处理切换页数后行数错位问题
        ;(this.$refs['batchRef'] as any)?.doLayout()
      } catch (e) {
        console.log(e, '退款订单查询列表失败')
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['batchRef'] as any)?.doLayout()
        this.query.loading = false
      }
      this.query.loading = false
    }
    // 请求
    async search() {
      this.page.pageNo = 1
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doSearchfx()
      } else if (this.isZtlogin) {
        await this.doSearchZt()
      } else {
        await this.doSearch()
      }
    }
    async reset() {
      this.page.pageNo = 1
      this.promotionPortalName = ''
      this.distributorName = ''
      this.refundCheckAccountParam = new RefundCheckAccountParam()
      await this.search()
    }
    sortChange(column: any) {
      // console.log(column.order, 1)
      this.sortRequest = []
      const item = new BatchReturnOrderSortRequest()
      if (column.order === 'ascending') {
        //正序
        item.policy = 'ASC' as SortPolicy
        this.sortRequest.push(item)
      } else if (column.order === 'descending') {
        item.policy = 'DESC' as SortPolicy
        this.sortRequest.push(item)
      } else {
        this.sortRequest = []
      }
      // console.log(this.sortRequest, ' this.sortRequest')
    }
    async editInvoicePopup() {
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.accountNumberRef.doQueryPagefx()
      } else {
        await this.accountNumberRef.doQueryPage()
      }

      const inputEl = document.getElementById('input1')
      inputEl.blur()
      this.editInvoiceDialog = true
    }
    getAccountNumber(idList: ReceiveAccountVo[]) {
      this.getData = idList[0]
      this.refundCheckAccountParam.paymentAccountID = this.getData?.id ? this.getData.id : ''
      this.accountName = this.getData?.accountName
    }

    goRefundDetail(refundId: string) {
      this.$router.push('/training/trade/refund/collective/detail/' + refundId)
    }

    async exportData() {
      try {
        let res
        if (this.isHadFxAbility && this.isFxlogin) {
          res = await this.exportDatafx()
        } else if (this.isZtlogin) {
          res = await this.exportDataZt()
        } else {
          res = await this.exportDataty()
        }

        if (res) {
          this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }

    async exportDataty() {
      return await TradeModule.batchTradeBatchFactor.checkAccountFactor.mutationCheckAccount.listReturnExport(
        this.refundCheckAccountParam
      )
    }
    async exportDatafx() {
      return await TradeModule.batchTradeBatchFactor.checkAccountFactor.mutationCheckAccount.listRxReturnExport(
        this.refundCheckAccountParam
      )
    }
    async exportDataZt() {
      return await this.mutationZtCheckAccount.listReturnExport(this.refundCheckAccountParam)
    }
    //前往下载
    goDownloadPage() {
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportBatchReturnReconciliation' }
      })

      this.exportSuccessVisible = false
    }
  }
</script>
