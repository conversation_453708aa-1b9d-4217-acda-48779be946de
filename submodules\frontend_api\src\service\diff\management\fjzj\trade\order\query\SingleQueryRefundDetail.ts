import ReturnOrderResponseVo from '@api/service/diff/management/fjzj/trade/order/model/ReturnOrderResponseVo'
import TradeQueryFrontGatewayTradeQueryBackstage, {
  ReturnOrderRequest as ReturnOrderRequestDiff
} from '@api/diff-gateway/fjzj-trade-query-front-gateway-TradeQueryBackstage'
import { ReturnSortRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { ResponseStatus } from '@hbfe/common'
import { UiPage, Query } from '@hbfe/common'
import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
import { SortPolicy, ReturnOrderSortField } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

export default class QueryRefundDetailDiff {
  //退款单号
  returnOrderNo = ''

  //退款单详情对象
  returnOrderDetail = new ReturnOrderResponseVo()
  /**
   * 获取退款单详情
   *
   */
  async queryRefundOrderDetail(): Promise<ResponseStatus> {
    if (!this.returnOrderNo) {
      return new ResponseStatus(8006, '退款单号不能为空')
    }
    const res = await TradeQueryFrontGatewayTradeQueryBackstage.getReturnOrderInServicer(this.returnOrderNo)
    if (res.status.isSuccess()) {
      const returnVo = new ReturnOrderResponseVo()
      Object.assign(returnVo, res.data)
      returnVo.changeStatus()
      await returnVo.fillRecords()
      await returnVo.addBuyer()
      const ext = res.data?.ext as any
      returnVo.courseType = ext?.courseType || ''
      this.returnOrderDetail = returnVo
    }
    return res.status
  }

  /**
   * 获取退款单详情 （分销）
   *
   */
  async queryFxRefundOrderDetail(): Promise<ResponseStatus> {
    if (!this.returnOrderNo) {
      return new ResponseStatus(8006, '退款单号不能为空')
    }
    const res = await TradeQueryFrontGatewayTradeQueryBackstage.getReturnOrderInDistributor(this.returnOrderNo)
    // const
    if (res.status.isSuccess()) {
      const returnVo = new ReturnOrderResponseVo()
      Object.assign(returnVo, res.data)
      returnVo.changeStatus()
      await returnVo.fillRecords()
      await returnVo.addBuyer()
      // const newData = res.data as ReturnOrderResponseVo
      //  if ()
      const ext = res.data?.ext as any
      returnVo.courseType = ext?.courseType || ''
      this.returnOrderDetail = returnVo
    }
    return res.status
  }
  /**
   * 获取退款单列表
   *
   */
  async queryRefundOrderList(id: string): Promise<Array<ReturnOrderResponseVo>> {
    const page = new UiPage()
    const request = new ReturnOrderRequestVo()
    const sort = new ReturnSortRequest()
    request.basicData.applySourceType = 'SUB_ORDER'
    request.basicData.returnOrderStatus = [8, 9, 10, 99]
    request.subOrderInfo.subOrderNoList = [id]
    sort.policy = SortPolicy.DESC
    sort.field = ReturnOrderSortField.APPLIED_TIME
    const res = await TradeQueryFrontGatewayTradeQueryBackstage.pageReturnOrderInServicer({
      page: page,
      request: request as ReturnOrderRequestDiff,
      sort: [sort]
    })
    if (res.status.isSuccess()) {
      const dataArr = [] as ReturnOrderResponseVo[]
      if (res.data.currentPageData) {
        for (const item of res.data.currentPageData) {
          const tmpItem = new ReturnOrderResponseVo()
          Object.assign(tmpItem, item)
          const ext = item.ext as any
          tmpItem.courseType = ext?.courseType || ''
          dataArr.push(tmpItem)
        }
      }
      return dataArr
    }
    return []
  }

  /**
   * 获取分销退款单列表
   *
   */
  async queryFxRefundOrderList(id: string): Promise<Array<ReturnOrderResponseVo>> {
    const page = new UiPage()
    const request = new ReturnOrderRequestVo()
    const sort = new ReturnSortRequest()
    request.basicData.applySourceType = 'SUB_ORDER'
    request.basicData.returnOrderStatus = [8, 9, 10, 99]
    request.subOrderInfo.subOrderNoList = [id]
    sort.policy = SortPolicy.DESC
    sort.field = ReturnOrderSortField.APPLIED_TIME
    const res = await TradeQueryFrontGatewayTradeQueryBackstage.pageReturnOrderInDistributor({
      page: page,
      request: request as ReturnOrderRequestDiff,
      sort: [sort]
    })
    if (res.status.isSuccess()) {
      const dataArr = [] as ReturnOrderResponseVo[]
      if (res.data.currentPageData) {
        for (const item of res.data.currentPageData) {
          const tmpItem = new ReturnOrderResponseVo()
          Object.assign(tmpItem, item)
          const ext = item.ext as any
          tmpItem.courseType = ext?.courseType || ''
          dataArr.push(tmpItem)
        }
      }
      return dataArr
    }
    return []
  }
}
