<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="按方案" name="first">
        <div class="f-p15">
          <el-card shadow="never" class="m-card f-mb15">
            <!--条件查询-->
            <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
            <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
            <el-row :gutter="16" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="年度">
                    <el-select v-model="select" clearable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="地区">
                    <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训方案类型">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训方案名称">
                    <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="行业">
                    <el-select v-model="select" clearable placeholder="请选择行业">
                      <el-option value="人社"></el-option>
                      <el-option value="建设"></el-option>
                      <el-option value="职业卫生"></el-option>
                      <el-option value="工勤"></el-option>
                      <el-option value="教师"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="学段">
                    <el-select v-model="select" clearable placeholder="请选择学段">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="学科">
                    <el-select v-model="select" clearable placeholder="请选择学科">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="科目类型">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训类别">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训专业">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="方案学时">
                    <el-input v-model="input" class="input-num" />
                    -
                    <el-input v-model="input" class="input-num" />
                    <span class="f-ml5">学时</span>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="报名时间">
                    <el-date-picker
                      v-model="form.date1"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="起始时间"
                      end-placeholder="结束时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="供应商">
                    <el-select v-model="select" clearable filterable placeholder="请输入或选择供应商">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="分销推广">
                    <el-select v-model="select" clearable filterable placeholder="请选择是否为分销推广订单">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="专题">
                    <el-select v-model="select" clearable filterable placeholder="请选择订单是否来源专题">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="专题名称">
                    <el-input v-model="input" clearable placeholder="请输入专题进行查询" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="分销商">
                    <el-input v-model="input" clearable placeholder="请输入分销商名称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="推广门户简称">
                    <el-input v-model="input" clearable placeholder="请输入分销商推广门户简称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="剔除培训方案">
                    <el-select v-model="select" clearable placeholder="请选择不纳入统计的培训方案">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>导出列表数据</el-button>
                    <el-button>重置</el-button>
                    <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                    <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--操作栏-->
            <div class="f-mt20">
              <el-alert type="warning" :closable="false" class="m-alert f-clear">
                <div class="f-c6 f-fl">
                  搜索结果合计：当前净开通 <span class="f-fb f-co">8</span> 人次，成交总额
                  <span class="f-fb f-co">¥ 99</span>
                </div>
                <div class="f-fr f-csp f-flex f-align-center"><i class="el-icon-info f-f16 f-mr5"></i>统计口径说明</div>
              </el-alert>
            </div>
            <!--表格-->
            <el-table
              stripe
              :data="tableData"
              border
              show-summary
              max-height="500px"
              class="m-table is-statistical f-mt10"
            >
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="年度" min-width="80" fixed="left">
                <template>2021</template>
              </el-table-column>
              <el-table-column label="培训方案" min-width="240" fixed="left">
                <template>方案名称方案名称方案名称方案名称方案名称方案名称</template>
              </el-table-column>
              <el-table-column label="方案属性" min-width="200">
                <template>方案属性方案属性</template>
              </el-table-column>
              <el-table-column label="合计" header-align="center">
                <el-table-column label="开通" min-width="90" align="right">
                  <template>26252</template>
                </el-table-column>
                <el-table-column label="退班" min-width="90" align="right">
                  <template>26</template>
                </el-table-column>
                <el-table-column label="换入(换班)" min-width="90" align="right">
                  <template>26</template>
                </el-table-column>
                <el-table-column label="换出(换班)" min-width="90" align="right">
                  <template>26</template>
                </el-table-column>
                <el-table-column label="净开通" min-width="90" align="right">
                  <template>26</template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="个人缴费" header-align="center">
                <el-table-column label="线上支付" header-align="center">
                  <el-table-column label="开通" min-width="90" align="right">
                    <template>26252</template>
                  </el-table-column>
                  <el-table-column label="退班" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换入(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换出(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="净开通" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                </el-table-column>
              </el-table-column>
              <el-table-column label="集体报名" header-align="center">
                <el-table-column label="线上支付" header-align="center">
                  <el-table-column label="开通" min-width="90" align="right">
                    <template>26252</template>
                  </el-table-column>
                  <el-table-column label="退班" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换入(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换出(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="净开通" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                </el-table-column>
                <el-table-column label="线下支付" header-align="center">
                  <el-table-column label="开通" min-width="90" align="right">
                    <template>26252</template>
                  </el-table-column>
                  <el-table-column label="退班" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换入(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换出(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="净开通" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                </el-table-column>
              </el-table-column>
              <el-table-column label="导入开通" header-align="center">
                <el-table-column label="线下支付" header-align="center">
                  <el-table-column label="开通" min-width="90" align="right">
                    <template>26252</template>
                  </el-table-column>
                  <el-table-column label="退班" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换入(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换出(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="净开通" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                </el-table-column>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane label="按定价方案" name="second">
        <div class="f-p15">
          <el-card shadow="never" class="m-card f-mb15">
            <!--条件查询-->
            <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
            <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
            <el-row :gutter="16" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="年度">
                    <el-select v-model="select" clearable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="地区">
                    <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训方案类型">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训方案名称">
                    <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="行业">
                    <el-select v-model="select" clearable placeholder="请选择行业">
                      <el-option value="人社"></el-option>
                      <el-option value="建设"></el-option>
                      <el-option value="职业卫生"></el-option>
                      <el-option value="工勤"></el-option>
                      <el-option value="教师"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="学段">
                    <el-select v-model="select" clearable placeholder="请选择学段">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="学科">
                    <el-select v-model="select" clearable placeholder="请选择学科">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="科目类型">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训类别">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训专业">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="方案学时">
                    <el-input v-model="input" class="input-num" />
                    -
                    <el-input v-model="input" class="input-num" />
                    <span class="f-ml5">学时</span>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="报名时间">
                    <el-date-picker
                      v-model="form.date1"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="起始时间"
                      end-placeholder="结束时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="供应商">
                    <el-select v-model="select" clearable filterable placeholder="请输入或选择供应商">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="分销推广">
                    <el-select v-model="select" clearable filterable placeholder="请选择是否为分销推广订单">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="专题">
                    <el-select v-model="select" clearable filterable placeholder="请选择订单是否来源专题">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="专题名称">
                    <el-input v-model="input" clearable placeholder="请输入专题进行查询" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="分销商">
                    <el-input v-model="input" clearable placeholder="请输入分销商名称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="推广门户简称">
                    <el-input v-model="input" clearable placeholder="请输入分销商推广门户简称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>导出列表数据</el-button>
                    <el-button>重置</el-button>
                    <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                    <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--操作栏-->
            <div class="f-mt20">
              <el-alert type="warning" :closable="false" class="m-alert f-clear">
                <div class="f-c6 f-fl">
                  搜索结果合计：当前净开通 <span class="f-fb f-co">8</span> 人次，成交总额
                  <span class="f-fb f-co">¥ 99</span>
                </div>
                <div class="f-fr f-csp f-flex f-align-center"><i class="el-icon-info f-f16 f-mr5"></i>统计口径说明</div>
              </el-alert>
            </div>
            <!--表格-->
            <el-table
              stripe
              :data="tableData"
              border
              show-summary
              max-height="500px"
              class="m-table is-statistical f-mt10"
            >
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="年度" min-width="80" fixed="left">
                <template>2021</template>
              </el-table-column>
              <el-table-column label="培训方案" min-width="240" fixed="left">
                <template>方案名称方案名称方案名称方案名称方案名称方案名称</template>
              </el-table-column>
              <el-table-column label="方案属性" min-width="200">
                <template>方案属性方案属性</template>
              </el-table-column>
              <el-table-column label="合计" header-align="center">
                <el-table-column label="开通" min-width="90" align="right">
                  <template>26252</template>
                </el-table-column>
                <el-table-column label="退班" min-width="90" align="right">
                  <template>26</template>
                </el-table-column>
                <el-table-column label="换入(换班)" min-width="90" align="right">
                  <template>26</template>
                </el-table-column>
                <el-table-column label="换出(换班)" min-width="90" align="right">
                  <template>26</template>
                </el-table-column>
                <el-table-column label="净开通" min-width="90" align="right">
                  <template>26</template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="个人缴费" header-align="center">
                <el-table-column label="线上支付" header-align="center">
                  <el-table-column label="开通" min-width="90" align="right">
                    <template>26252</template>
                  </el-table-column>
                  <el-table-column label="退班" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换入(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换出(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="净开通" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                </el-table-column>
              </el-table-column>
              <el-table-column label="集体报名" header-align="center">
                <el-table-column label="线上支付" header-align="center">
                  <el-table-column label="开通" min-width="90" align="right">
                    <template>26252</template>
                  </el-table-column>
                  <el-table-column label="退班" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换入(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换出(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="净开通" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                </el-table-column>
                <el-table-column label="线下支付" header-align="center">
                  <el-table-column label="开通" min-width="90" align="right">
                    <template>26252</template>
                  </el-table-column>
                  <el-table-column label="退班" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换入(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换出(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="净开通" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                </el-table-column>
              </el-table-column>
              <el-table-column label="导入开通" header-align="center">
                <el-table-column label="线下支付" header-align="center">
                  <el-table-column label="开通" min-width="90" align="right">
                    <template>26252</template>
                  </el-table-column>
                  <el-table-column label="退班" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换入(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="换出(换班)" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                  <el-table-column label="净开通" min-width="90" align="right">
                    <template>26</template>
                  </el-table-column>
                </el-table-column>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
