import GeoLocation from '@api/service/customer/sign-center/models/GeoLocation'
import PeriodImplementConfig from '@api/service/customer/implement/PeriodImplementConfig'
import { calculateDistance } from '@api/service/common/utils/CalculateLocation'
import QueryMyTrainClassDetail from '@api/service/customer/train-class/query/QueryMyTrainClassDetail'
import MyTrainClassDetailClassVo from '@api/service/customer/train-class/query/vo/MyTrainClassDetailClassVo'
import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
import SignDateScope from '@api/service/customer/sign-center/models/SignDateScope'
import MsTeachingplanAttendance, {
  GetServerTimeWithStudentNoRequest
} from '@api/ms-gateway/ms-teachingplan-attendance-sds-v1'
import StudentFaceStudyLog from '@api/service/customer/implement/StudentFaceStudyLog'
import { ReportStatusEnum } from '@api/service/customer/report-center/enums/ReportStatusEnum'
import MsTeachingplanReport, {
  StudentReportCheckInWithStudentNoRequest
} from '@api/ms-gateway/ms-teachingplan-report-sds-v1'
import MsSchemeLearningQueryFrontGateway from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import { ResponseStatus } from '@hbfe/common'
import ReportSignRes, {
  BackendToReportSignResEnum,
  ReportSignResEnum
} from '@api/service/customer/report-center/enums/ReportSignResEnum'
import TrainingPlaceManage from '@api/service/customer/training-place/TrainingPlaceManage'

export default class ReportCenterControl {
  /**
   * 期别id
   */
  periodId: string = undefined

  /**
   * 期别参训资格id
   */
  private periodQualificationId: string = undefined

  /**
   * 学号
   */
  private studentNo: string = undefined

  /**
   * 方案参训资格id
   */
  private qualificationId: string = undefined

  /**
   * 当前定位点
   */
  private currentPoint: GeoLocation = new GeoLocation()

  /**
   * 签到点位置
   */
  private signLocation: GeoLocation = new GeoLocation()

  /**
   * 当前服务器时间戳
   */
  private currentServiceTimestamp: number = undefined

  /**
   * 计时器
   */
  private interval: NodeJS.Timeout = undefined

  /**
   * 是否处在倒计时
   */
  private isClock = false

  /**
   * 多少分钟开始距离报道时间倒计时时间戳
   */
  private fixTipTimestamp = 24 * 60 * 60000

  /**
   * 签到点距离
   */
  private signDistance: number = undefined

  /**
   * 当前报道状态
   */
  private currentReportStatus: ReportStatusEnum = undefined

  /**
   * 当前报到时间
   */
  private currentCountTime: string = undefined

  /**
   * 当前报到时间（小时）
   */
  private currentCountHour: string = undefined

  /**
   * 当前报到时间（分钟）
   */
  private currentMinute: string = undefined

  /**
   * 教学计划id（来源于期别配置）
   */
  planId = ''

  /**
   * 期别实施配置（取报道具体配置）
   */
  periodImplementConfig: PeriodImplementConfig = new PeriodImplementConfig(this.periodId)

  /**
   * 方案配置信息
   */
  schemeConfig: MyTrainClassDetailClassVo = new MyTrainClassDetailClassVo()

  /**
   * 当前期别配置（来源于方案，包含期别培训地点等信息）
   */
  currentPeriodConfig: IssueConfigDetail = new IssueConfigDetail()

  /**
   * 报到时间
   */
  checkDateRange: SignDateScope = new SignDateScope()

  /**
   * 学员面授学习记录
   */
  studentFaceLog: StudentFaceStudyLog = new StudentFaceStudyLog(this.studentNo)

  /**
   * 是否临近报道点
   */
  get nearSign() {
    if (typeof this.signDistance === 'number' && typeof this.periodImplementConfig.reportSignRange === 'number') {
      return this.signDistance <= this.periodImplementConfig.reportSignRange
    } else {
      return false
    }
  }

  /**
   * 获取当前位置
   */
  get currentLocation() {
    return this.currentPoint
  }
  /**
   * 设置当前位置
   * @param location 位置点
   */
  set currentLocation(location: GeoLocation) {
    this.currentPoint = location
    if (this.currentPoint.longitude && this.currentPoint.latitude) {
      this.signDistance = calculateDistance(
        this.signLocation.latitude,
        this.signLocation.longitude,
        location.latitude,
        location.longitude
      )
    }
  }

  /**
   * 报到状态
   */
  get reportStatus() {
    return this.currentReportStatus ? this.currentReportStatus : ReportStatusEnum.waitReport
  }

  /**
   * 倒计时时间 （完整）
   */
  get clockTime() {
    return this.currentCountTime ? this.currentCountTime : ''
  }

  /**
   * 倒计时（小时）
   */
  get clockHour() {
    return this.currentCountHour ? this.currentCountHour : ''
  }

  /**
   * 倒计时（分钟）
   */
  get clockMinute() {
    return this.currentMinute ? this.currentMinute : ''
  }

  /**
   * 是否展示倒计时
   */
  get showClock() {
    return this.clockHour ? Number(this.clockHour) < 24 : false
  }

  /**
   * 获取正在报道的面授课信息
   */
  static async getStudentNearStudyReport() {
    const res = await MsSchemeLearningQueryFrontGateway.getRecentTrainingQualificationInMyself()

    return res?.data
  }

  /**
   * @param studentNo 学号
   * @param qualificationId 方案参训资格id
   */
  constructor(studentNo: string, qualificationId: string) {
    this.studentNo = studentNo
    this.qualificationId = qualificationId
    this.currentLocation = new GeoLocation()
  }

  async init() {
    /**
     * 初始化做以下内容
     * 1.查询学员方案配置，捞取当前期别配置信息（核心字段：期别id、教学计划id（报到、获取系统时间使用），培训点id，学号（获取报道数据使用），取培训开始结束时间）
     * 2.获取学习token（获取系统时间、报道使用）（已废除）
     * 3.获取期别实施配置（取报道范围）
     * 4.获取面授记录，判断报道状态
     */
    // 获取班级信息（需要取班级身上的考勤配置以及面授学习方式id以及教学计划id）
    await this.getCurrentSchemeConfig()

    // 8.0存在只配置期别不配置课程教学计划的情况（没有课程但可以报道），故拿不到学习方式id，这边就废除了学员学习token的关联
    // // 获取学员学习Token
    // const applyStudentLearning = new ApplyStudentIssueLearningToken(
    //   this.periodQualificationId,
    //   this.currentPeriodConfig.relateTeachPlanLearning.id
    // )
    // await applyStudentLearning.apply()
    // this.studentLearningToken = applyStudentLearning.token

    // 取期别实施配置
    this.periodImplementConfig = new PeriodImplementConfig(this.periodId)
    await this.periodImplementConfig.getPeriodImplementConfig()

    // 取学员报道记录，初始化报道状态
    await this.getReportedLog()
  }

  /**
   * 获取报到记录
   */
  async getReportedLog() {
    this.studentFaceLog = new StudentFaceStudyLog(this.studentNo)
    await this.studentFaceLog.queryStudentFaceStudyLog()

    await this.getServiceTime()
    if (this.studentFaceLog.reported) {
      this.currentReportStatus = ReportStatusEnum.reported
    } else {
      if (this.currentServiceTimestamp > this.checkDateRange.endTimestamp) {
        this.currentReportStatus = ReportStatusEnum.unReport
      } else if (
        this.currentServiceTimestamp < this.checkDateRange.endTimestamp &&
        this.currentServiceTimestamp > this.checkDateRange.beginTimestamp
      ) {
        this.currentReportStatus = ReportStatusEnum.reporting
      } else {
        this.currentReportStatus = ReportStatusEnum.waitReport
      }
    }
  }

  /**
   * 报道（校验地点）
   * 200 成功
   * 1001 重复报道
   * 1002 不在培训点范围
   * 1003 不在报道时间
   * 1004 未设置报道地点
   * 500 系统异常
   * @param checkLocation 是否校验定位 默认开启
   */
  async report(checkLocation = true) {
    const request = new StudentReportCheckInWithStudentNoRequest()
    request.studentNo = this.studentNo
    request.qualificationId = this.periodQualificationId
    request.learningId = this.currentPeriodConfig.relateTeachPlanLearning.id || '-1'
    request.planId = this.planId || '-1'
    request.issueId = this.periodId
    if (checkLocation) {
      request.longitude = this.currentLocation.longitude
      request.latitude = this.currentLocation.latitude
    }
    request.enableRangeCheck = checkLocation

    const res = await MsTeachingplanReport.studentReportCheckInWithStudentNo(request)
    if (res?.status && res.status.isSuccess() && res?.data?.code) {
      const resEnumCode = BackendToReportSignResEnum[res.data.code]
      if (resEnumCode == ReportSignResEnum.success) {
        return Promise.resolve(new ResponseStatus(ReportSignResEnum.success, ReportSignRes.map.get(resEnumCode)))
      } else {
        return Promise.reject(new ResponseStatus(resEnumCode, ReportSignRes.map.get(resEnumCode)))
      }
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   * 根据服务器时间启动倒计时
   */
  async startClockWithService() {
    if (this.isClock) {
      console.error('已有倒计时计时器在执行')
      return
    }
    await this.getServiceTime()
    this.isClock = true
    this.interval = setInterval(() => {
      if (this.currentServiceTimestamp) {
        this.currentServiceTimestamp += 1000
      }
      this.countDown()
    }, 1000)
  }

  /**
   * 根据服务器时间更新倒计时
   */
  async updateClock() {
    this.stopClock()
    await this.startClockWithService()
  }

  /**
   * 停止倒计时
   */
  stopClock() {
    clearInterval(this.interval)
    this.interval = undefined
    this.isClock = false
  }

  /**
   * 倒计时
   */
  private countDown() {
    // 已报到不进行倒计时
    if (this.reportStatus == ReportStatusEnum.reported || !this.currentServiceTimestamp) {
      return
    }
    const reportStartTime = this.checkDateRange.beginTimestamp
    const reportEndTime = this.checkDateRange.endTimestamp
    if (this.currentServiceTimestamp < reportStartTime) {
      // 未开始签到（倒计时为签到开始时间减去签到时间）
      const countTimeStamp = reportStartTime - this.currentServiceTimestamp
      // 小于30分钟开始倒计时
      this.currentReportStatus = ReportStatusEnum.waitReport
      if (countTimeStamp < this.fixTipTimestamp) {
        this.formatTimeToReport(countTimeStamp / 1000)
      } else {
        this.currentCountHour = ''
        this.currentMinute = ''
        this.currentCountTime = ''
      }
    } else if (this.currentServiceTimestamp >= reportStartTime && this.currentServiceTimestamp <= reportEndTime) {
      // 处在签到时间（倒计时时间为签到结束时间减去当前时间）
      const countTimeStamp = reportEndTime - this.currentServiceTimestamp
      this.currentReportStatus = ReportStatusEnum.reporting
      this.formatTimeToReport(countTimeStamp / 1000)
    } else {
      // 超过签到时间
      this.currentReportStatus = ReportStatusEnum.unReport
      this.currentCountHour = ''
      this.currentMinute = ''
      this.currentCountTime = ''
    }
  }

  /**
   * 处理当前时间戳，更新当前报到时间
   * @param seconds 毫秒
   */
  private formatTimeToReport(seconds: number) {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = Math.floor(seconds % 60)

    // 使用padStart方法来确保每个部分都是两位数
    const formattedHours = String(hours).padStart(2, '0')
    const formattedMinutes = String(minutes).padStart(2, '0')
    const formattedSeconds = String(remainingSeconds).padStart(2, '0')

    this.currentCountHour = formattedHours
    this.currentMinute = formattedMinutes
    this.currentCountTime = `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
  }

  /**
   * 获取系统时间（依赖于学员学习token）
   */
  private async getServiceTime() {
    const request = new GetServerTimeWithStudentNoRequest()
    request.studentNo = this.studentNo
    request.planId = this.planId || '-1'
    request.learningId = this.currentPeriodConfig.relateTeachPlanLearning.id || '-1'
    request.issueId = this.periodId
    request.qualificationId = this.periodQualificationId
    const serviceTimeRes = await MsTeachingplanAttendance.getServerTimeWithStudentNo(request)
    this.currentServiceTimestamp = serviceTimeRes?.data
  }

  /**
   * 获取方案信息
   */
  private async getCurrentSchemeConfig() {
    const queryMyTrainClassDetail = new QueryMyTrainClassDetail()
    queryMyTrainClassDetail.studentNo = this.studentNo
    queryMyTrainClassDetail.qualificationId = this.qualificationId
    await queryMyTrainClassDetail.queryTrainClassDetail()

    this.schemeConfig = queryMyTrainClassDetail.trainClassDetail
    this.periodQualificationId = queryMyTrainClassDetail.issueLearningDetail.periodQualificationId
    this.periodId = queryMyTrainClassDetail.issueLearningDetail.periodId
    // 获取当前期别配置
    this.currentPeriodConfig = queryMyTrainClassDetail.queryIssueConfigDetailByIssueId(this.periodId)

    // 获取培训地点名称（方案Json里只有id，名称要单独拿）
    const trainingPlaceManage = new TrainingPlaceManage()
    const res = await trainingPlaceManage.getTrainingPlaceByIds([this.currentPeriodConfig.trainingPointId])
    this.currentPeriodConfig.trainingPointName = res.get(this.currentPeriodConfig.trainingPointId)?.selectAddress || ''

    // 获取期别计划id
    this.planId = this.currentPeriodConfig.relateTeachPlanLearning.configId

    // 获取期别面授地点信息
    this.signLocation.longitude = res.get(this.currentPeriodConfig.trainingPointId)?.longitude || 0
    this.signLocation.latitude = res.get(this.currentPeriodConfig.trainingPointId)?.latitude || 0

    // 取报到时间
    this.checkDateRange.beginTime = this.currentPeriodConfig.checkDateRange.startDate
    this.checkDateRange.endTime = this.currentPeriodConfig.checkDateRange.endDate
    this.checkDateRange.beginTimestamp = new Date(this.checkDateRange.beginTime.replaceAll('-', '/')).getTime()
    this.checkDateRange.endTimestamp = new Date(this.checkDateRange.endTime.replaceAll('-', '/')).getTime()
  }
}
