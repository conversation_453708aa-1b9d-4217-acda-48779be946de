<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">调研问卷统计</el-breadcrumb-item>
      <el-breadcrumb-item>查看统计报告</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
        <el-tab-pane label="整体报告" name="first">
          <el-card shadow="never" class="m-card">
            <el-row type="flex">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form">
                  <el-form-item label="调研问卷名称：">
                    读取调查问卷名称
                  </el-form-item>
                  <el-form-item label="调研问卷试题：">
                    <el-button type="text">预览</el-button>
                  </el-form-item>
                  <el-form-item label="参与问卷情况：">
                    参与且提交问卷人数 <span class="f-ci f-fb">100</span> 人
                  </el-form-item>
                </el-form>
              </el-col>
              <el-col :span="6" class="f-tr">
                <el-button type="primary">下载试题选项内容</el-button>
                <el-button type="primary">下载此报告</el-button>
              </el-col>
            </el-row>
            <div class="f-mb10">
              <div class="m-sub-tit">
                <span class="tit-txt"
                  >第1题：<span>读取试题题目</span><el-tag size="small" class="f-ml5">单选题</el-tag></span
                >
              </div>
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column label="选项" min-width="160">
                  <template>选项A：读取选项A内容</template>
                </el-table-column>
                <el-table-column label="选择数" min-width="160" align="center">
                  <template>10</template>
                </el-table-column>
                <el-table-column label="比例" min-width="160" align="center">
                  <template>10%</template>
                </el-table-column>
              </el-table>
            </div>
            <div class="f-mb10">
              <div class="m-sub-tit">
                <span class="tit-txt"
                  >第2题：<span>读取试题题目</span><el-tag size="small" class="f-ml5">多选题</el-tag></span
                >
              </div>
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column label="选项" min-width="160">
                  <template>选项A：读取选项A内容</template>
                </el-table-column>
                <el-table-column label="选择数" min-width="160" align="center">
                  <template>10</template>
                </el-table-column>
                <el-table-column label="比例" min-width="160" align="center">
                  <template>10%</template>
                </el-table-column>
              </el-table>
            </div>
            <div class="f-mb10">
              <div class="m-sub-tit">
                <span class="tit-txt"
                  >第3题：<span>读取试题题目</span><el-tag size="small" class="f-ml5">问答题</el-tag></span
                >
              </div>
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column label="答题人数" min-width="160" align="center">
                  <template>10</template>
                </el-table-column>
                <el-table-column label="比例" min-width="160" align="center">
                  <template>10%</template>
                </el-table-column>
              </el-table>
              <div class="f-mt20">
                <el-button type="primary">查看详细信息</el-button>
                <el-button type="primary">导出问答文本到EXCEL</el-button>
              </div>
            </div>
            <div class="f-mb10">
              <div class="m-sub-tit">
                <span class="tit-txt"
                  >第4题：<span>读取试题题目</span><el-tag size="small" class="f-ml5">量表题</el-tag></span
                >
              </div>
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column label="选项" min-width="160">
                  <template>1（非常不满意）</template>
                </el-table-column>
                <el-table-column label="选择数" min-width="160" align="center">
                  <template>10</template>
                </el-table-column>
                <el-table-column label="比例" min-width="160" align="center">
                  <template>10%</template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="问卷答题记录" name="second">
          <el-card shadow="never" class="m-card">
            <!--条件查询-->
            <el-row :gutter="16" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="证件号">
                    <el-input v-model="input" clearable placeholder="请输入证件号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="手机号">
                    <el-input v-model="input" clearable placeholder="请输入手机号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="姓名">
                    <el-input v-model="input" clearable placeholder="请输入姓名" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="提交时间">
                    <el-date-picker
                      v-model="value1"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="8" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="姓名" min-width="160">
                <template>姓名姓名姓名</template>
              </el-table-column>
              <el-table-column label="证件号" min-width="160">
                <template>350103200010010000</template>
              </el-table-column>
              <el-table-column label="手机号" min-width="160" align="center">
                <template>131111111</template>
              </el-table-column>
              <el-table-column label="提交时间" min-width="160" align="center">
                <template>2024-10-01 08:08:08</template>
              </el-table-column>
              <el-table-column label="操作" min-width="120" align="center">
                <template>
                  <el-button type="text">查看答卷</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      const data = [
        {
          id: 1,
          label: '一级 1',
          children: [
            {
              id: 4,
              label: '二级 1-1'
            }
          ]
        }
      ]
      return {
        num: 1,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'second',
        props: { multiple: true },
        radio: 3,
        input: '',
        input1: '1',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        tableData1: [
          {
            id: 1,
            field01: '这里是分类名称',
            hasChildren: true
          },
          {
            id: 2,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 3,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 4,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 5,
            field01: '分类名称1',
            hasChildren: true
          }
        ],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down'],
        data
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      },
      load(tree, treeNode, resolve) {
        setTimeout(() => {
          resolve([
            {
              id: 11,
              field01: '分类名称1-1'
            },
            {
              id: 12,
              field01: '分类名称1-2'
            }
          ])
        }, 1000)
      }
    }
  }
</script>
