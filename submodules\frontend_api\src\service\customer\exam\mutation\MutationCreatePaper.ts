import msPaperAnswer, { ApplyPaperAnswerResponse } from '@api/ms-gateway/ms-exam-answer-v1'
import ExamModule from '@api/service/customer/exam/ExamModule'
import { Response, ResponseStatus } from '@hbfe/common'
import {
  AnswerPaperViewResponse,
  ChooseAnswerOption,
  MultipleQuestionResponse,
  OpinionQuestionResponse
} from '@api/service/customer/exam/mutation/vo/AnswerPaper'
import { QuestionTypeEnum, QuestionKeyValue } from '@api/service/customer/exam/utils/Constant'
import { TimeOut } from '@api/service/common/utils/TimeOut'
import MsExamQueryFrontGatewayCourseLearningForeStage, {
  AnswerPaperResponse as AnswerPaperResponseA
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryForeStage'

export class MutationCreatePaper {
  /**
   * 作答token，类型为string
   */
  answerToken = ''
  /**
   * 学员学习token，类型为string
   */
  studentToken = ''
  /**
   * UI层存储的试卷对象jsonString
   */
  paperJsonString = ''
  /**
   * 试卷
   */
  answerPaper = new AnswerPaperViewResponse()
  // /**
  //  *  创建练习卷
  //  */
  // async createPracticePaper(){
  //
  // }
  /**
   * 快捷获取交卷对象
   */
  getMutationSubmitPaper() {
    const submitM = ExamModule.mutationExamFactory.getMutationSubmitPaper()
    submitM.token = this.answerToken
    return submitM
  }

  //过滤出答案数组
  // filterAnswer(){
  //   const answerArr: Array<
  //     AskQuestionAnswer | FillQuestionAnswer | MultipleQuestionAnswer | OpinionQuestionAnswer | RadioQuestionAnswer
  //     > = []
  //   this.answerPaper.questions.forEach((item)=>{
  //      if (item.questionType == 1) {
  //        const radioQuestionAnswer = new RadioQuestionAnswer()
  //      }
  //   })
  // }
  /**
   * 获取作答token
   */
  async getAnswerToken(): Promise<ResponseStatus> {
    return new ResponseStatus(200, '')
  }

  async judgeFunc(res: Response<ApplyPaperAnswerResponse>) {
    return res.status.isSuccess() && res.data.state == 1
  }

  async applyAnswer(answerToken: string) {
    return await msPaperAnswer.applyAnswer(answerToken)
  }

  /**
   * 获取练习卷子
   */
  async getPracticePaper(time?: number, count?: number, token?: string): Promise<ResponseStatus> {
    const res = await TimeOut.timeoutWithCount<Response<ApplyPaperAnswerResponse>>(
      this['applyAnswer'],
      this['judgeFunc'],
      [this.answerToken || token],
      time,
      count
    )
    if (res.status.isSuccess() && res.data.state == 1) {
      this.answerPaper = res.data.answerPaper as AnswerPaperViewResponse
      this.answerPaper.questions.forEach(item => {
        if (item.questionType == QuestionTypeEnum.QuestionTypeEnumOpi) {
          const trueItem = item as OpinionQuestionResponse
          trueItem.answerOptions = [
            {
              id: 'true',
              content: trueItem.correctAnswerText
            },
            {
              id: 'false',
              content: trueItem.incorrectAnswerText
            }
          ]
        }
      })
      await this.calPaper()
    } else {
      res.status.code = 200 + res.data.state
      res.status.message = res.data.message
    }
    return res.status
  }

  /**
   * 获取考试剩余时间
   * */

  async queryRemainingAnswerTime(answerPaperId: string) {
    const res = await msPaperAnswer.getRemainingAnswerTime(answerPaperId)
    if (res.status.isSuccess() && res.data) {
      return res.data
    } else {
      return 0
    }
  }

  async calPaper() {
    console.log('进入处理试卷')
  }
}
