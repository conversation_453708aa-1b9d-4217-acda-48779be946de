import SecondElectiveMaxPeriod from '@api/service/common/scheme/model/schemeDto/choose-course-learning/config/choose-course-rule/config/second-elective-max-period/SecondElectiveMaxPeriod'

/**
 * @description 选课规则配置
 */
class ChooseCourseRuleConfig {
  /**
   * 必修学时
   */
  compulsoryPeriod: number
  /**
   * 选修最大学时
   */
  electiveMaxPeriod: number
  /**
   * 允许最后一门选课超过选修最大学时
   */
  allowLastChooseOver: boolean
  /**
   * 选课最大学时配置
   */
  secondElectiveMaxPeriod: SecondElectiveMaxPeriod[]
  /**
   * 是否约束重复选课
   */
  constrainedRepeatSelection: boolean
  /**
   * 约束重复选课范围
   */
  constrainedRangeKeyList: string[]
}

export default ChooseCourseRuleConfig
