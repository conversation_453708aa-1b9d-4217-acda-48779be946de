import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum QuestionSourceTypeEnum {
  /**
   * 正常题
   */
  normal = '-1',
  /**
   * 教师题
   */
  teacher = '0',
  /**
   * 线上教师题
   */
  online_teacher = '1',
  /**
   * 线下教师题
   */
  offline_teacher = '2'
}
class QuestionSourceType extends AbstractEnum<QuestionSourceTypeEnum> {
  static enum = QuestionSourceTypeEnum

  constructor(status?: QuestionSourceTypeEnum) {
    super()
    this.current = status
    this.map.set(QuestionSourceTypeEnum.normal, '正常题')
    this.map.set(QuestionSourceTypeEnum.teacher, '教师题')
    this.map.set(QuestionSourceTypeEnum.online_teacher, '线上教师题')
    this.map.set(QuestionSourceTypeEnum.offline_teacher, '线下教师题')
  }
}
export default QuestionSourceType
