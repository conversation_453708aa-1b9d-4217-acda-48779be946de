/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-06-18 15:55:07
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-07-03 12:08:54
 * @Description:
 */
import { StudentCourseLearningTokenResponse } from '@api/ms-gateway/ms-autonomouscourselearningscene-v1'
import AbstractApplyToken from '@api/service/common/token/AbstractApplyToken'
import ApplyAutonomousCourseLearningToken from '@api/service/common/token/ApplyAutonomousCourseLearningToken'
import ApplyChooseCourseRuleLearningToken from '@api/service/common/token/ApplyChooseCourseRuleLearningToken'
import ApplyInterestCourseLearningToken from '@api/service/common/token/ApplyInterestCourseLearningToken'
import LearningCourseGateError from '@api/service/customer/learning/scene/errors/LearningCourseGateError'
import StudentLearningCourseTypeEnum from '@api/service/customer/learning/scene/gates/enum/StudentLearningCourseTypeEnum'
import ApplyStudentAutonomousCourseLearningSceneProof from '@api/service/customer/learning/scene/proofs/ApplyStudentAutonomousCourseLearningSceneProof'
import ApplyStudentChooseCourseRuleLearningSceneProof from '@api/service/customer/learning/scene/proofs/ApplyStudentChooseCourseRuleLearningSceneProof'
import ApplyStudentInterestCourseRuleLearningSceneProof from '@api/service/customer/learning/scene/proofs/ApplyStudentInterestCourseRuleLearningSceneProof'
import StudentLearningSceneProof from '@api/service/customer/learning/scene/proofs/StudentLearningSceneProof'
import { Response } from '@hbfe/common'
class ApplyCourseLearningTokenFactory {
  schemeType: string
  private readonly proof: StudentLearningSceneProof

  constructor(schemeType: string, proof: StudentLearningSceneProof) {
    this.schemeType = schemeType
    this.proof = proof
  }
  /**
   * 申请课程学习token返回值
   */
  response = new Response<StudentCourseLearningTokenResponse>()

  /**
   * 学习课程类型适配器
   * @param studentLearningToken
   * @private
   */
  async getStudentCourseLearningToken(studentLearningToken: string): Promise<string | LearningCourseGateError> {
    let applier: AbstractApplyToken
    if (this.schemeType === StudentLearningCourseTypeEnum.ChooseCourseRule) {
      const proof: ApplyStudentChooseCourseRuleLearningSceneProof = this
        .proof as ApplyStudentChooseCourseRuleLearningSceneProof
      applier = new ApplyChooseCourseRuleLearningToken(proof.studentCourseId, studentLearningToken)
    } else if (this.schemeType === StudentLearningCourseTypeEnum.InterestCourse) {
      const proof: ApplyStudentInterestCourseRuleLearningSceneProof = this
        .proof as ApplyStudentInterestCourseRuleLearningSceneProof
      applier = new ApplyInterestCourseLearningToken(proof.courseId, studentLearningToken, proof.outlineId)
    } else if (this.schemeType === StudentLearningCourseTypeEnum.AutonomousCourse) {
      const proof: ApplyStudentAutonomousCourseLearningSceneProof = this
        .proof as ApplyStudentAutonomousCourseLearningSceneProof
      applier = new ApplyAutonomousCourseLearningToken(proof.courseId, studentLearningToken, proof.outlineId)
    }
    try {
      // 自主选课、特殊处理
      if (this.schemeType === StudentLearningCourseTypeEnum.AutonomousCourse) {
        const result = await applier.apply()
        this.response = (applier as ApplyAutonomousCourseLearningToken).response
        if (applier.code !== 200) {
          return Promise.reject(new LearningCourseGateError(applier.code, ''))
        }
      } else {
        await applier.apply()
        this.response = (applier as ApplyAutonomousCourseLearningToken).response
      }
    } catch (e) {
      this.response = (applier as ApplyAutonomousCourseLearningToken).response
      return Promise.reject(new LearningCourseGateError(e.code, e.message))
    }
    return applier.token
  }
}

export default ApplyCourseLearningTokenFactory
