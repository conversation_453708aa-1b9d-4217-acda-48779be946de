<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-08-27 16:31:35
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-08-28 09:47:15
 * @Description:
-->
<template>
  <div>
    <el-select
      v-model="selected"
      :placeholder="placeholder"
      @clear="selected = undefined"
      class="el-input"
      filterable
      clearable
    >
      <el-option
        v-for="item in placeChannelOptions"
        :label="item.name"
        :value="item.propertyId"
        :key="item.propertyId"
      ></el-option>
    </el-select>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import QueryPersonIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustry'
  @Component
  export default class extends Vue {
    // 职称数组
    // placeChannelOptions: Array<LeaderPositionLevelVo> = new Array<LeaderPositionLevelVo>()
    placeChannelOptions: Array<any> = []

    // 选择项
    selected = ''

    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Prop({
      type: String,
      default: '请选择执业类别'
    })
    placeholder: string

    //行业Id
    @Prop({
      type: String,
      default: ''
    })
    industryId: string

    //分类code
    @Prop({
      type: String,
      default: ''
    })
    categoryCode: string

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }

    async created() {
      await this.queryPracticeCategoryList()
    }

    // 获取执业类别
    async queryPracticeCategoryList() {
      if (this.industryId) {
        const res = await QueryPersonIndustry.getPractitionerCategory(this.industryId)
        this.placeChannelOptions = res
      }
    }
  }
</script>
