import RegionVo from '@api/service/common/basic-data-dictionary/query/vo/RegionVo'

class RegionTreeVo {
  /**
   * 地区编码
   */
  code: string
  /**
   * 父级地区编码
   */
  parentCode: string
  /**
   * 地区编码路径
   */
  codePath: string
  /**
   * 地区名称
   */
  name: string
  /**
   * 级别|1省级 2市级 3区县级
   */
  level: number
  /**
   * 地区排序
   */
  sort: number
  /**
   * 子节点
   */
  children: Array<RegionTreeVo> = []
}

export default RegionTreeVo
