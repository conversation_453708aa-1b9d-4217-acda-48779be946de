import { CertificateAttachment, CertificateInfo } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'

class CertificateInfoVo extends CertificateInfo {
  /**
   * 更新时必填
   */
  certificateId: string = undefined
  /**
   * 证书编号
   */
  certificateNo: string = undefined
  /**
   * 证书类别id
   */
  certificateCategory: string = undefined
  /**
   * 注册专业id
   */
  registerProfessional: string = undefined
  /**
   * 发证日期（起）
   */
  releaseStartTime: string = undefined
  /**
   * 证书有效期（止）
   */
  certificateEndTime: string = undefined
  /**
   * 证书附件
   */
  certificateAttachments: Array<CertificateAttachment> = []

  /*
   * 属性扩展
   */
  /**
   * 证书类别名称
   */
  certificateCategoryName?: string = undefined
  /**
   * 注册专业名称
   */
  registerProfessionalName?: string = undefined
}

export default CertificateInfoVo
