import ReceiveAccountDetailVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountDetailVo'
import { PayAccountTypeEnum } from '@api/service/management/trade-info-config/enums/PayAccountTypeEnum'
import { ReceiveAccountConfigResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

export class XDLPayReceiveAccountDetailVo extends ReceiveAccountDetailVo {
  /**
   * 账号类型
   */
  paymentChannelId: PayAccountTypeEnum = undefined
  /**
   * 支付商户号
   */
  payMerchantId = ''
  /**
   * 代理商号
   */
  proxyId = ''
  /**
   * 密钥
   */
  xdlPrivateKey = ''
  /**
   * 付款扫码引导语
   */
  qrScanPrompt = ''

  from(res: ReceiveAccountConfigResponse) {
    this.accountType = res.accountType
    this.accountNo = res.accountNo
    this.accountName = res.name
    this.taxPayerId = res.taxPayerId
    this.refundWay = res.returnType
    this.paymentChannelId = res.paymentChannelId as PayAccountTypeEnum
    // JSON
    const temp = res.encryptionKeyData as any
    this.payMerchantId = res.accountNo
    this.qrScanPrompt = res.qrScanPrompt
    this.proxyId = temp?.proxyNo
    this.xdlPrivateKey = temp?.privateKey
  }
}
