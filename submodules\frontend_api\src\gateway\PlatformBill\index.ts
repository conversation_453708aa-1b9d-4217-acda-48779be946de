import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/PlatformBill'

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 发票查询参数
<AUTHOR> create 2020/4/22 13:47
 */
export class LazyBillRequestParam {
  /**
   * 集体缴费批次号
   */
  batchNo?: string
  /**
   * 集体缴费单位id
   */
  batchUnitId?: string
  /**
   * 是否集体缴费发票
   */
  isBatchBill?: boolean
  /**
   * 收款账号id
   */
  receiptAccount?: string
  /**
   * 主订单号
   */
  orderNos?: Array<string>
  /**
   * 购买人姓名
   */
  buyerName?: string
  /**
   * 买家id
   */
  buyerId?: string
  /**
   * 购买人身份证号
   */
  buyerIdentity?: string
  /**
   * 购买人手机号
   */
  buyerPhoneNumber?: string
  /**
   * 蓝票开具状态：0未开票、1开票中、2开票成功、3开票失败
   */
  blueState?: Array<number>
  /**
   * 红票开具状态：0未开票、1开票中、2开票成功、3开票失败
   */
  redState?: Array<number>
  /**
   * 是否冲红
true 是
false 否
   */
  redBill?: boolean
  /**
   * 是否冻结
   */
  frozen?: boolean
  /**
   * 发票代码
   */
  billCode?: string
  /**
   * 发票验证码
   */
  billVeriCode?: string
  /**
   * 索取发票开始时间
   */
  getBillStartTime?: string
  /**
   * 索取发票结束时间
   */
  getBillEndTime?: string
  /**
   * 发票类型：1普通发票，2增值税普通发票，3增值税专用发票
   */
  invoiceType?: string
  /**
   * 是否开票
   */
  makeInvoice?: boolean
  /**
   * 开票开始时间
   */
  makeInvoiceStartTime?: string
  /**
   * 开票结束时间
   */
  makeInvoiceEndTime?: string
  /**
   * 是否非税务发票
   */
  noTaxBill?: boolean
  /**
   * 是否电子发票
   */
  electron?: boolean
  /**
   * 是否测试数据
   */
  test?: boolean
  /**
   * 订单所属培训机构
单位id
   */
  unitId?: string
  /**
   * 发票号
   */
  billNo?: string
  /**
   * 发票抬头
   */
  billTitle?: string
  /**
   * 发票抬头类别
   */
  billTitleType?: string
}

/**
 * 发票配置
<AUTHOR> create 2020/5/19 10:29
 */
export class BillConfigDTO {
  /**
   * 配置主键ID
   */
  id: string
  /**
   * 子项目编号
   */
  subProjectId: string
  /**
   * 是否自动开票|默认0
   */
  autoBill: boolean
  /**
   * 间隔天数|自生成发票日期算起多少天后自动开票，默认7
   */
  intervalDay: number
  /**
   * 是否来源默认配置
   */
  isDefault: boolean
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime: string
}

/**
 * 发票信息对象
<AUTHOR> create 2020/4/22 13:46
 */
export class LazyBillResponse {
  /**
   * 主键 发票ID
   */
  id: string
  /**
   * 发票号
   */
  billNo: string
  /**
   * 发票对应的订单号
   */
  orderNo: string
  /**
   * 发票代码
   */
  billCode: string
  /**
   * 发票对应的集体缴费单号
   */
  batchOrderNo: string
  /**
   * 集体缴费单位id
   */
  batchUnitId: string
  /**
   * 集体缴费单位名
   */
  batchUnitName: string
  /**
   * 蓝票开具状态：0未开票、1开票中、2开票成功、3开票失败
   */
  blueState: number
  /**
   * 红票开具状态：0未开票、1开票中、2开票成功、3开票失败
   */
  redState: number
  /**
   * 退款状态
-1 没退款
   */
  refundStatus: number
  /**
   * 付款金额
   */
  payAmount: number
  /**
   * 开票金额
   */
  money: number
  /**
   * 税额
   */
  tax: number
  /**
   * 是否补考
   */
  makeUpExam: boolean
  /**
   * 购买人
   */
  buyerName: string
  /**
   * 手机号
   */
  buyerPhoneNumber: string
  /**
   * 证件号
   */
  buyerIdentity: string
  /**
   * 纳税人识别号
   */
  taxpayerNo: string
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型 1：个人 2：企业
   */
  titleType: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 账号
   */
  account: string
  /**
   * 是否电子发票
   */
  electron: boolean
  /**
   * 发票类型：1普通发票，2增值税普通发票，3增值税专用发票
   */
  invoiceType: string
  /**
   * 是否冻结
   */
  frozen: boolean
  /**
   * 索要发票时间
   */
  billCreatedTime: string
  /**
   * 开票完成时间
成功/失败时间
   */
  makeInvoiceTime: string
  /**
   * 冲红发票金额
   */
  redMoney: number
  /**
   * 冲红发票税额
   */
  redTaxRate: number
  /**
   * 冲红发票代码
   */
  redBillCode: string
  /**
   * 冲红发票号码
   */
  redBillNo: string
}

/**
 * 延时
发票统计对象
<AUTHOR> create 2020/5/7 9:10
 */
export class LazyBillStatisticsResponse {
  /**
   * 开票总金额
   */
  drawTotalAmount: number
  /**
   * 开票税额总金额
   */
  drawTaxTotalAmount: number
  /**
   * 冲红发票总金额
   */
  redBillTotalAmount: number
  /**
   * 冲红税额总金额
   */
  redBillTaxTotalAmount: number
}

export class LazyBillResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<LazyBillResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取发票自动开票配置信息
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAutoBillConfig(
    query: DocumentNode = GraphqlImporter.getAutoBillConfig,
    operation?: string
  ): Promise<Response<BillConfigDTO>> {
    return commonRequestApi<BillConfigDTO>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async lazyPageBill(
    params: { page?: Page; queryParams?: LazyBillRequestParam },
    query: DocumentNode = GraphqlImporter.lazyPageBill,
    operation?: string
  ): Promise<Response<LazyBillResponsePage>> {
    return commonRequestApi<LazyBillResponsePage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param queryParams 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async lazyStatisticsBill(
    queryParams: LazyBillRequestParam,
    query: DocumentNode = GraphqlImporter.lazyStatisticsBill,
    operation?: string
  ): Promise<Response<LazyBillStatisticsResponse>> {
    return commonRequestApi<LazyBillStatisticsResponse>(SERVER_URL, {
      query: query,
      variables: { queryParams },
      operation: operation
    })
  }
}

export default new DataGateway()
