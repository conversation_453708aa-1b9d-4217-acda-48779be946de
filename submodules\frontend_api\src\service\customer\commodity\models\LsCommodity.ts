/**
 * 学习方案商品信息
 */
import { SkuProperty } from '@api/gateway/PlatformStatisticReportQuery'

class LsCommodity extends SkuProperty {
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 培训方案名称
   */
  name: string
  /**
   * 封面图片地址
   */
  picture: string
  /**
   * 发布时间
   */
  publishTime: Date
  /**
   * 创建人ID
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: Date
  /**
   * 方案推荐指数
   */
  recommendIndex: number
  /**
   * 培训获得学时
   */
  grade: number
  /**
   * 价格
   */
  price: number
  /**
   * 用户是否购买
   */
  purchased = false
}

export default LsCommodity
