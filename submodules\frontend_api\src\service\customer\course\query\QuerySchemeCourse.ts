import { UiPage } from '@hbfe/common'
import MsCourseLearningQueryFrontGatewayCourseLearningForestage, {
  CourseRequest,
  CourseResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import { uniq } from 'lodash'

class QuerySchemeCourse {
  /**
   * 根据 id 集合查询课程信息
   * @param idList
   */
  async queryCoursePageByIdList(idList: Array<string>): Promise<Array<CourseResponse>> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    const request = new CourseRequest()
    request.courseIdList = uniq(idList)
    let result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseInServicer({
      page,
      request
    })
    if (result.data.totalSize) {
      page.pageSize = result.data.totalSize
      result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseInServicer({
        page,
        request
      })
    }
    return result.data.currentPageData
  }
}

export default QuerySchemeCourse
