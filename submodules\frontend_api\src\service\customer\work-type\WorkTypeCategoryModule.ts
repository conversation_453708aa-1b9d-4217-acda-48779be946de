import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import Vue from 'vue'
import { ResponseStatus } from '@api/Response'
import WorkTypeCategoryTree from '@api/service/common/models/work-type/WorkTypeCategoryTree'
import PlatformWorkTypeCategoryGateway, { WorkTypeCategoryResponse } from '@api/gateway/PlatformWorkTypeCategory'
import WorkTypeCategoryTreeWithWorkType from '@api/service/common/models/work-type/WorkTypeCategoryTreeWithWorkType'
import WorkType from '@api/service/common/models/work-type/WorkType'
import PlatformWorkTypeGateway from '@api/gateway/PlatformWorkType'

export interface WorkTypeCategoryState {
  /**
   * 工种分类树集合
   */
  workTypeCategoryTreeList: Array<WorkTypeCategoryTreeWithWorkType>
  /**
   * 所有工种分类集合
   */
  workTypeCategoryList: Array<WorkTypeCategoryResponse>
}

@Module({ namespaced: true, dynamic: true, store, name: 'CustomerWorkTypeCategoryModule' })
class WorkTypeCategoryModule extends VuexModule implements WorkTypeCategoryState {
  workTypeCategoryTreeList = new Array<WorkTypeCategoryTreeWithWorkType>()
  workTypeCategoryList: Array<WorkTypeCategoryResponse> = new Array<WorkTypeCategoryResponse>()
  topList: Array<WorkTypeCategoryResponse> = new Array<WorkTypeCategoryResponse>()
  private map: { [key: string]: WorkTypeCategoryTree } = {}
  private cache: { [key: string]: WorkTypeCategoryTreeWithWorkType } = {}
  reload = true // 是否重新加载 true 加载 false不加载
  reloadWorkTypeCategory = true // 是否重新加载 true 加载 false不加载
  // 知识型技能工总id
  KNOWLEDGE_TYPE_WORK = '2ca4aabc7770e5470177768a50840017'
  // 操作性技能工种 id
  OPERATION_TYPE_WORK = '2ca4aabc7770e5470177768a63c90018'
  /**
   * 获取当前系统的所有门户课程分类树，该树形结构中就包含了子分类获取到的树形结构集合放入workTypeCategoryTreeList中
   * workTypeCategoryTreeList中只存放的是最顶级的树，其中的children就是子分类
   */
  @Action
  async getAllWorkTypeCategoryTreeList(): Promise<ResponseStatus> {
    if (!this.reload) {
      return new ResponseStatus(200)
    }
    const response = await PlatformWorkTypeCategoryGateway.getAllCategoryList()
    if (response.data) {
      this.setOnList(response.data)
      //没有子分类的获取工种信息
      for (const id of Object.keys(this.cache)) {
        const tree = this.cache[id]
        if (!tree.hasChildren()) {
          const nextResponse = await PlatformWorkTypeGateway.getWorkTypeListByCategoryId({
            categoryId: id,
            containsChildren: false
          })
          if (nextResponse.data) {
            nextResponse.data.forEach(e => {
              const workType = new WorkType().parse(e)
              this.SET_WORKTYPE_CATEGORY_INFO({ id: id, workType })
            })
          }
        }
      }
      this.SET_LOAD(false)
    }
    return response.status
  }

  /**
   * 获取当前系统的所有工种分类
   */
  @Action
  async getAllWorkTypeCategoryList(): Promise<ResponseStatus> {
    if (!this.reloadWorkTypeCategory) {
      return new ResponseStatus(200)
    }
    const response = await PlatformWorkTypeCategoryGateway.getAllCategoryList()
    if (response.status.isSuccess()) {
      this.SET_WORK_TYPE_CATEGORY_LIST(response.data)
      this.SET_LOAD_WORK_TYPE_CATEGORY(false)
    }
    return response.status
  }

  /**
   * 通过Id获取工种分类信息
   */
  @Action
  async getWorkTypeCategoryList(idList: Array<string>): Promise<ResponseStatus> {
    const response = await PlatformWorkTypeCategoryGateway.getWorkTypeCategoryList(idList)
    if (response.status.isSuccess()) {
      this.SET_WORK_TYPE_CATEGORY_LIST(response.data)
    }
    return response.status
  }

  @Action
  async getAllTopCategoryList() {
    try {
      const { status, data } = await PlatformWorkTypeCategoryGateway.getAllTopCategoryList()
      if (status.isSuccess()) {
        this.SET_TOP_LIST(data)
      }
      return status
    } catch (e) {
      // nothing
    }
  }

  @Mutation
  SET_TOP_LIST(list: Array<WorkTypeCategoryResponse>) {
    this.topList = list
  }

  @Mutation
  private SET_LOAD(val: boolean) {
    this.reload = val
  }

  @Mutation
  private SET_LOAD_WORK_TYPE_CATEGORY(val: boolean) {
    this.reloadWorkTypeCategory = val
  }

  @Mutation
  SET_WORKTYPE_CATEGORY_INFO(params: { id: string; workType: WorkType }) {
    this.cache[params.id].addWorkType(params.workType)
  }

  @Mutation
  private setOnList(list: Array<WorkTypeCategoryResponse>) {
    this.workTypeCategoryTreeList.splice(0, this.workTypeCategoryTreeList.length)
    this.cache = {}
    list.forEach(e => {
      const tree = new WorkTypeCategoryTreeWithWorkType().parse(e)
      if (tree.parentId === '-1') {
        this.workTypeCategoryTreeList.push(tree)
      }
      Vue.set(this.cache, tree.id, tree)
    })
    //将获取到的分类构造成树形结构
    Object.values(this.cache).forEach(tree => {
      const parent = this.cache[tree.parentId]
      if (parent) {
        parent.addChild(tree)
      }
    })
    this.workTypeCategoryList = list
  }

  @Mutation
  private SET_WORK_TYPE_CATEGORY_LIST(list: Array<WorkTypeCategoryResponse>) {
    list.forEach(remote => {
      const index = this.workTypeCategoryList.findIndex(e => e.id === remote.id)
      if (index == -1) {
        this.workTypeCategoryList.push(remote)
      } else {
        this.workTypeCategoryList.splice(index, 1)
        this.workTypeCategoryList.push(remote)
      }
    })
    this.workTypeCategoryList.sort((a, b) => {
      return a.sort - b.sort
    })
    this.cache = {}
    this.workTypeCategoryList.forEach(e => {
      const tree = new WorkTypeCategoryTreeWithWorkType().parse(e)
      Vue.set(this.cache, tree.id, tree)
    })
  }

  get getExistsWorkTypeCategoryTreeById() {
    return (id: string): WorkTypeCategoryTree => {
      return this.map[id]
    }
  }

  /**
   * 根据指定工种类别id获取完整的父路径数组
   * 调用前请先初始化
   * @see WorkTypeCategoryModule.getAllWorkTypeCategoryTreeList
   */
  get getFatherPathByWorkTypeCategoryId() {
    return (id: string): string[] => {
      const arr = []
      arr.push(id)
      let a = this.workTypeCategoryList.find(w => w.id === id)
      while (a !== undefined && a.parentId !== '-1') {
        arr.splice(0, 0, a.parentId)
        a = this.workTypeCategoryList.find(w => w.id === a.parentId)
      }
      return arr
    }
  }

  /**
   * 根据指定工种类别id路径获取完整名字
   * 调用前请先初始化
   * @see WorkTypeCategoryModule.getAllWorkTypeCategoryTreeList
   *
   * @param param.path 以'/'分隔的类别id路径，如：'/123/456'
   * @param param.splitChar 返回时类别中文之间的分隔符，如：'》'
   * @return 'name》name' 或 'name' 或 '未知类别' 或 '未知类别》未知类别'
   */
  get getFatherPathNameByWorkTypeCategoryIdPath() {
    return (param: { path: string; splitChar: string }): string => {
      if (param.path === undefined || param.path === null) {
        return param.splitChar
      }
      const arr = param.path.split('/')
      //@return    ['','id','id']
      if (arr.length < 2) {
        // if path is ''
        return '未知类别'
      }
      const first = this.workTypeCategoryList.find(w => w.id === arr[1]) || undefined
      if (arr.length < 3) {
        // if path is '/' or '/id'
        return first?.name || '未知类别'
      }
      const second = this.workTypeCategoryList.find(w => w.id === arr[2]) || undefined
      return (first?.name || '未知类别') + param.splitChar + (second?.name || '未知类别')
    }
  }

  /**
   * 获取类别路径的根节点名字
   * @param categoryIdPath 类别路径，如：/123/456
   * @return 根节点名字，如：知识性技能工种
   */
  get getRootCategoryByPath() {
    return (categoryIdPath: string): string => {
      if (!categoryIdPath) return ''
      const arr = categoryIdPath.split('/')
      return this.workTypeCategoryList.find(w => w.id === arr[1])?.name
    }
  }

  /**
   * 获取红肿分类数，不含工种，使用前需加载getAllWorkTypeCategoryList
   */
  get workTypeCategoryTreeOnly() {
    return (): Array<WorkTypeCategoryTreeWithWorkType> => {
      const treeList = new Array<WorkTypeCategoryTreeWithWorkType>()
      this.workTypeCategoryList.forEach(e => {
        const tree = new WorkTypeCategoryTreeWithWorkType().parse(e)
        if (tree.parentId === '-1') {
          treeList.push(tree)
        }
      })
      Object.values(this.cache).forEach(tree => {
        const parent = this.cache[tree.parentId]
        if (parent) {
          parent.addChild(tree)
        }
      })
      return treeList
    }
  }

  /**
   * 获取默认的工种类别，门户、小程序固定显示的工种类别，使用前需加载getAllWorkTypeCategoryList
   */
  getDefaultWorkTypeList() {
    return (): Array<WorkTypeCategoryResponse> => {
      // 知识技能型工种 2ca4aabc7770e5470177768a50840017
      // 操作技能型工种 2ca4aabc7770e5470177768a73c10019
      // 紧缺急需工种  2ca4aabc7770e5470177768a63c90018
      const defaultWorkType = [
        '2ca4aabc7770e5470177768a50840017',
        '2ca4aabc7770e5470177768a73c10019',
        '2ca4aabc7770e5470177768a63c90018'
      ]
      return this.workTypeCategoryList.filter(e => defaultWorkType.includes(e.id))
    }
  }
}

export default getModule(WorkTypeCategoryModule)
