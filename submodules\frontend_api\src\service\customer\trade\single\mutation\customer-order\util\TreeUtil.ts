import RegionTree from '@hbfe-biz/biz-dictionary/dist/model/RegionItem'

export class Tree {
  /**
   * 父级id
   */
  parentCode = ''
  /**
   * 当前id
   */
  code = ''
  /**
   * 当前名称
   */
  name = ''
  /**
   * 全路径，形如：'/xxx/xxx/xxx'
   */
  codePath = ''
  /**
   * 等级
   * @description
   * 地区：省-1、市-2、区县-3
   */
  level = 0
  /**
   * 排序
   */
  sort = 0
  /**
   * 是否被选中
   */
  isSelect = false
  /**
   * 子节点
   */
  children: Tree[] = []
}

/**
 * @description 树工具类
 */
class TreeUtil {
  /**
   * 查找指定节点
   * @param tree 树数据
   * @param func 条件函数
   * @return 符合条件的节点
   */
  static treeFind(tree: Tree[], func: Function): Tree | null {
    for (const item of tree) {
      if (func(item)) return item
      if (item.children && item.children.length) {
        const res = TreeUtil.treeFind(item.children, func)
        if (res) return res
      }
    }
    return null
  }

  /**
   * 查询从根节点指定节点开始的所有节点
   * @param tree 树数据
   * @param codePath 节点codePath
   * @returns 节点数组
   */
  static treeFindPathByNode(tree: Tree[], codePath: string): Tree[] {
    const result = [] as Tree[]
    const findPath = (tree: Tree[], path: Tree[]): Tree[] => {
      for (const item of tree) {
        path.push(item)
        if (item.codePath === codePath) return path
        if (item.children && item.children.length) {
          const findChildren = findPath(item.children, path)
          if (findChildren.length) {
            return findChildren
          }
        }
        path.pop()
      }
      return [] as Tree[]
    }
    return findPath(tree, result)
  }

  /**
   * 查找树上所有满足条件的节点构成的数组
   * @param tree 树数据
   * @param func 条件函数
   * @return 节点数组
   */
  static treeFindAll(tree: Tree[], func: Function): Tree[] {
    const result = [] as Tree[]
    for (const item of tree) {
      if (func(item)) result.push(item)
      if (item.children && item.children.length) {
        const res = TreeUtil.treeFindAll(item.children, func)
        if (res.length) result.push(...res)
      }
    }
    return result
  }

  /**
   * 查询所有叶子节点
   * @param tree 树数据
   * @return 叶子节点数组
   */
  static treeFindAllLeaves(tree: Tree[]): Tree[] {
    const result = [] as Tree[]
    const getLeaves = (tree: Tree[]) => {
      tree.forEach((item: Tree) => {
        if (!item.children || !item.children.length) {
          result.push(item)
        } else {
          getLeaves(item.children)
        }
      })
    }
    getLeaves(tree)
    return result
  }

  /**
   * 将列表数据转为树型结构
   * @description 存在自递归行为
   * @param list 列表数据
   * @return 树型结构
   */
  static listToTree(list: Tree[], parentId = '0'): Tree[] {
    return list
      .filter(item => item.parentCode === parentId)
      .map(item => {
        const children = TreeUtil.listToTree(list, item.code)
        if (children.length) {
          item.children = children
        }
        return item
      })
  }

  /**
   * 树形结构转为列表结构
   * @param tree 树数据
   */
  static treeToList(tree: RegionTree[]): Tree[] {
    const result = [] as Tree[]
    const getList = (tree: RegionTree[]) => {
      for (const node of tree) {
        const opt = new Tree()
        opt.parentCode = node.parentCode
        opt.code = node.code
        opt.name = node.name
        opt.codePath = node.codePath
        opt.level = node.level
        opt.isSelect = false
        opt.sort = node.sort
        opt.children = [] as Tree[]
        result.push(opt)
        if (node.children && node.children.length) {
          getList(node.children)
        }
      }
    }
    getList(tree)
    return result
  }

  /**
   * 获取数组的选中项
   * @param list 数组数据
   * @return 选中项数组
   */
  static getSelectedItems(list: Tree[]): Tree[] {
    const result = [] as Tree[]
    const findSelected = (arr: Tree[], code: string) => {
      const target = list.find(el => el.code === code)
      if (target) {
        const isExist = result.find(el => el.code === target.code)
        if (!isExist) {
          result.push(target)
        }
        findSelected(arr, target.parentCode)
      } else {
        return
      }
    }
    const codeList = list.filter(el => el.isSelect === true).map(el => el.code)
    codeList.forEach(itm => {
      findSelected(list, itm)
    })
    return result
  }

  /**
   * 填充数组的选中状态
   * @description 如果当前层级的所有下级是选中状态，则当前层级也选中，其所有下级置为未选中
   * @param list 数组数据
   * @param level 层级
   */
  static fillSelectByLevel(list: Tree[], level: number) {
    const filterList = list.filter(el => el.level === level)
    if (!filterList.length) {
      return
    }
    filterList.forEach(itm => {
      const childList = list.filter(el => el.parentCode === itm.code)
      if (childList.length) {
        const existUnSelect = childList.some(el => el.isSelect === false)
        if (!existUnSelect) {
          itm.isSelect = true
          childList.forEach(el => {
            el.isSelect = false
          })
        }
      }
    })
  }
}

export default TreeUtil
