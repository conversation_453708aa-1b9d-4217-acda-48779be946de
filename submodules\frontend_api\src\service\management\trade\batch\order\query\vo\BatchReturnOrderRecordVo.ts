import { BatchRefundTradeStatusEnum } from '../../enum/BatchOrderTradeStatus'

/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-27 16:37:14
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-27 16:38:23
 */
export class BatchReturnOrderRecordVo {
  /*
   *  操作人名称
   * */
  name = ''
  /*
   *  操作时间
   * */
  time = ''
  /*
   * 备注信息
   * */
  tipMsg = ''

  /*
   * 退款金额
   * */
  money = 0

  //UI列表展示的状态
  UIReturnOrderStatue = BatchRefundTradeStatusEnum.REFUNDING
}
