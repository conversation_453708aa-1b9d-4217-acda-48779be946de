import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'

/**
 * @description
 */
class QualifiedCourseVo {
  /**
   * 参训资格id
   */
  qualificationId = ''

  /**
   * 学习方式id
   */
  learningId = ''

  /**
   * 培训方案类型 1：选课规则 2：自主选课
   */
  schemeType: TrainClassSchemeEnum = null

  /**
   * 是否配置课程测验，配置课程测验后必传测验合格分
   */
  hasConfigCourseQuiz: boolean = null

  /**
   * 测验合格分
   */
  quizScore: number = null

  /**
   * 课程id
   */
  courseId = ''
  /**
   * 学员课程id
   */
  studentCourseId = ''
}

export default QualifiedCourseVo
