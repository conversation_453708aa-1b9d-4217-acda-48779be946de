import findById from './queries/findById.graphql'
import validateJoinIssueLearningScheme from './queries/validateJoinIssueLearningScheme.graphql'
import applyChooseCourseToken from './mutates/applyChooseCourseToken.graphql'
import applyChooseInterestCourseToken from './mutates/applyChooseInterestCourseToken.graphql'
import applyExamLearningToken from './mutates/applyExamLearningToken.graphql'
import applyInterestCourseLearningToken from './mutates/applyInterestCourseLearningToken.graphql'
import applyQuestionLibPracticeLearningToken from './mutates/applyQuestionLibPracticeLearningToken.graphql'
import applySingleCourseLearningToken from './mutates/applySingleCourseLearningToken.graphql'
import clearAllLearningRecordForIssue from './mutates/clearAllLearningRecordForIssue.graphql'
import completedAllLearningForIssue from './mutates/completedAllLearningForIssue.graphql'
import completedLearningForOneCourse from './mutates/completedLearningForOneCourse.graphql'
import completedLearningForOneCourseware from './mutates/completedLearningForOneCourseware.graphql'
import createIssue from './mutates/createIssue.graphql'
import createLS from './mutates/createLS.graphql'
import deleteUserExamAnswerInfoById from './mutates/deleteUserExamAnswerInfoById.graphql'
import publishLS from './mutates/publishLS.graphql'
import removeIssue from './mutates/removeIssue.graphql'
import removeLS from './mutates/removeLS.graphql'
import updateIssue from './mutates/updateIssue.graphql'
import updateLS from './mutates/updateLS.graphql'

export {
  findById,
  validateJoinIssueLearningScheme,
  applyChooseCourseToken,
  applyChooseInterestCourseToken,
  applyExamLearningToken,
  applyInterestCourseLearningToken,
  applyQuestionLibPracticeLearningToken,
  applySingleCourseLearningToken,
  clearAllLearningRecordForIssue,
  completedAllLearningForIssue,
  completedLearningForOneCourse,
  completedLearningForOneCourseware,
  createIssue,
  createLS,
  deleteUserExamAnswerInfoById,
  publishLS,
  removeIssue,
  removeLS,
  updateIssue,
  updateLS
}
