<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div slot="header" class="">
        <span class="tit-txt">基础信息</span>
      </div>
      <div class="f-plr45 f-pt20 f-pb10">
        <el-row :gutter="16">
          <el-form :inline="true" label-width="auto" class="m-text-form is-edit">
            <el-col :sm="12" :md="8">
              <el-form-item label="管理员姓名：" class="is-editing">
                {{ userDetail.userInfo && userDetail.userInfo.userName }}
                <basic-data-edit-value
                  v-model="userInfo.adminUserName"
                  placeholder="请输入管理员姓名"
                  @saveEdit="saveEditVal"
                ></basic-data-edit-value>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8">
              <el-form-item label="登录帐号：" class="is-editing">
                {{ getAdminAccount(userDetail.authenticationList) }}
                <basic-data-edit-value-account
                  v-model="userInfo.adminUserAccount"
                  placeholder="请输入登录账号"
                  @saveEditAccount="saveEditValAccount"
                ></basic-data-edit-value-account>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8">
              <el-form-item label="注册时间：">{{ userDetail.accountInfo.createdTime }}</el-form-item>
            </el-col>
            <el-col :sm="12" :md="8">
              <el-form-item label="注册来源：">{{ getSourceTypeName(userDetail.accountInfo.sourceType) }}</el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </div>
    </el-card>
    <!-- 重置密码 -->
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div slot="header" class="">
        <span class="tit-txt">密码相关</span>
      </div>
      <div class="f-plr30 f-pt15 f-pb20">
        <el-button type="primary" size="small" class="f-ml10" @click="uiStatus.isShow.resetPwdDialog = true"
          >重置管理员密码</el-button
        >
        <span class="f-ml10 f-co">注：密码默认重置gl000000</span>
      </div>
      <el-dialog title="重置密码" :visible.sync="uiStatus.isShow.resetPwdDialog" width="380px" class="m-dialog">
        <div>
          你正在重置【<span class="f-cr">{{ userDetail.userInfo && userDetail.userInfo.userName }}</span
          >】学员的密码为：<span class="f-cr">gl000000</span>，重置后密码会同步生效，是否确认重置？
        </div>
        <div slot="footer">
          <el-button @click="uiStatus.isShow.resetPwdDialog = false">取 消</el-button>
          <el-button type="primary" @click="confirmResetPwd">确 定</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import UserModule from '@api/service/management/user/UserModule'
  import BasicDataEditValue from '@hbfe/jxjy-admin-customerService/src/collective/collective/basic-data-edit-value.vue'
  import BasicDataEditValueAccount from '@hbfe/jxjy-admin-customerService/src/collective/collective/basic-data-edit-value-account.vue'
  import {
    AccountResponse,
    AuthenticationResponse,
    CollectiveInfoResponse,
    UserInfoResponse
  } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import ResetPwdBusinessVo from '@api/service/management/user/mutation/vo/ResetPwdBusinessVo'
  import { AccountPwdAuthIdentityResetInfo } from '@api/gateway/ms-account-v1'
  import { UpdateCollectiveRegisterAccountInfoRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'

  @Component({
    components: {
      BasicDataEditValue,
      BasicDataEditValueAccount
    }
  })
  export default class extends Vue {
    // 修改管理员信息实例
    updateCollectiveObj = UserModule.mutationUserFactory.mutationUpdateCollectiveInfo
    // 修改入参
    updateCollectiveParams = this.updateCollectiveObj.userUpdateRequest
    // 重置管理员密码实例
    resetPwdBussinessObj = UserModule.mutationUserFactory.resetPwdBussiness
    // 修改管理员账号实例
    updateAccountAdminObj = UserModule.mutationUserFactory.mutationResetAccountAdmin

    // 管理员详情实例
    collectiveManagerObj = UserModule.queryUserFactory.queryCollectiveManagerList

    userInfo = {
      // 管理员姓名
      adminUserName: '',
      // 管理员账号
      adminUserAccount: ''
    }

    uiStatus = {
      isShow: {
        resetPwdDialog: false
      }
    }

    @Prop({
      type: String,
      default: ''
    })
    userId: string

    // 用户详情
    userDetail = new CollectiveInfoResponse()

    constructor() {
      super()
      this.userDetail.accountInfo = new AccountResponse()
      this.userDetail.userInfo = new UserInfoResponse()
      this.userDetail.authenticationList = new Array<AuthenticationResponse>()
    }

    @Watch('userId')
    async userIdChange(id: string) {
      await this.queryManagerDetail(id)
    }

    @Watch('userDetail', {
      deep: true
    })
    userNameChange(val: CollectiveInfoResponse) {
      this.userInfo.adminUserName = val?.userInfo?.userName
      this.userInfo.adminUserAccount = val?.authenticationList[0]?.identity
    }

    getAdminAccount(item: Array<AuthenticationResponse>) {
      if (item?.length) {
        return item[0]?.identity
      }
      return '-'
    }

    // 查询管理员详情
    async queryManagerDetail(userId: string) {
      if (!userId) {
        return
      }
      try {
        this.userDetail = await this.collectiveManagerObj.queryCollectiveInfoInServicer(userId)
      } catch (e) {
        console.log(e)
        this.$message.error('查询管理员详情失败！')
      }
    }

    // 修改管理员账号信息
    async saveEditValAccount() {
      const params = new UpdateCollectiveRegisterAccountInfoRequest()
      params.userId = this.userDetail?.userInfo?.userId
      params.identity = this.userInfo.adminUserAccount
      params.identityType = 2
      try {
        const res = await this.updateCollectiveObj.doUpdateAccount(params)
        if (res?.data?.code == '200') {
          this.$message.success('修改成功')
          await this.queryManagerDetail(this.userId)
        } else if (res?.data?.code == '100001') {
          this.$message.error('手机号已存在')
        } else if (res?.data?.code == '30000') {
          this.$message.error('请输入11位手机号')
        } else {
          this.$message.error('修改失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error('修改失败')
      }
    }

    // 修改管理员姓名
    async saveEditVal() {
      this.updateCollectiveParams.id = this.userId
      this.updateCollectiveParams.name = this.userInfo?.adminUserName
      try {
        const status = await this.updateCollectiveObj.doUpdate()
        if (status.code == 200) {
          this.$message.success('修改成功')
          await this.queryManagerDetail(this.userId)
        } else {
          this.$message.error('修改失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error('修改失败')
      }
    }

    async confirmResetPwd() {
      try {
        // const params = new ResetPwdBusinessVo()
        // params.accountId = this.userDetail?.accountInfo?.accountId
        // params.password = 'gl000000'
        const res = await this.resetPwdBussinessObj.doResetCollectivePwd(
          this.userDetail?.accountInfo?.accountId,
          'gl000000'
        )
        if (res?.status?.code == 200) {
          this.$message.success('重置密码成功')
        } else {
          this.$message.error('重置管理员密码请求失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error('重置管理员密码请求失败！')
      } finally {
        this.uiStatus.isShow.resetPwdDialog = false
      }
    }

    getSourceTypeName(val: number) {
      if (val === 0) {
        return '内置'
      } else if (val === 1) {
        return '项目主网站'
      } else if (val === 2) {
        return '安卓'
      } else if (val === 3) {
        return 'IOS'
      } else {
        return '-'
      }
    }
  }
</script>
