import SkuVo from '@api/service/customer/train-class/query/vo/SkuVo'
/**
 * @description sku属性
 */
class SkuPropertyVo {
  /**
   * 年度
   */
  year: SkuVo = new SkuVo()

  /**
   * 地区
   */
  region: SkuVo = new SkuVo()

  /**
   * 行业
   */
  industry: SkuVo = new SkuVo()

  /**
   * 科目类型
   */
  subjectType: SkuVo = new SkuVo()

  /**
   * 培训类别
   */
  trainCategory: SkuVo = new SkuVo()

  /**
   * 培训专业
   */
  trainMajor: SkuVo = new SkuVo()
  /**
   * 技术等级
   */
  technicalGrade: SkuVo = new SkuVo()
  /**
   * 卫生行业-培训对象
   */
  trainingObject: SkuVo = new SkuVo()
  /**
   * 卫生行业-岗位类别
   */
  positionCategory: SkuVo = new SkuVo()
  /**
   * 工勤行业-技术等级
   */
  jobLevel: SkuVo = new SkuVo()
  /**
   * 工勤行业-工种
   */
  jobCategory: SkuVo = new SkuVo()
  /**
   * 学段
   */
  learningPhase = new SkuVo()
  /**
   * 科目
   */
  discipline = new SkuVo()
  /**
   * 证书类型
   */
  certificatesType = new SkuVo()
  /**
   * 执业类别
   */
  practitionerCategory = new SkuVo()
}

export default SkuPropertyVo
