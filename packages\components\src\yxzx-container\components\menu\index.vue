<template>
  <el-menu
    :background-color="backgroundColor"
    class="aside-nav"
    :default-active="defaultActive"
    :unique-opened="true"
    :router="true"
    :collapse="isCollapse"
  >
    <template v-for="(menu, index) in menuList">
      <el-menu-item
        v-if="!menu.children || !menu.children.length"
        :index="menu.router.path"
        :key="menu.id + index"
        :id="`nav_guide_menu_item_${index}`"
        :class="bootstrapBegin && index == 0 ? ' driver-highlighted-element driver-position-relative' : ''"
        :disabled="bootstrapBegin"
      >
        <i
          :class="[menu.meta.icon, /^el/.test(menu.meta.icon) ? '' : 'iconfont']"
          :style="bootstrapBegin ? { color: '#ffffff' } : {}"
        ></i>
        <template slot="title">
          <span slot="title" :style="menu.router.path | isCurrent" :data-current="currentFxNav.path">
            <span :style="bootstrapBegin ? { color: '#ffffff' } : {}">{{ menu.name }}</span>
          </span>
        </template>
      </el-menu-item>
      <el-submenu
        :index="menu.id"
        v-if="menu.children && menu.children.length"
        :key="'sub' + menu.id + index"
        :id="`subNav_guide_menu_item_${index}`"
        :class="bootstrapBegin && index == 0 ? ' driver-highlighted-element driver-position-relative' : ''"
        :disabled="bootstrapBegin"
      >
        <template slot="title">
          <i :class="[/^hb-iconfont/.test(menu.meta.icon) ? 'hb-iconfont' : 'iconfont', menu.meta.icon]"></i>
          <span :data-id="menu.router.path">{{ menu.name }}</span>
        </template>

        <template v-for="(subMenu, subIndex) in menu.children">
          <el-menu-item
            :data-id="subMenu.router.path"
            v-if="!subMenu.children || !subMenu.children.length"
            :index="subMenu.router.path"
            :key="subIndex"
            :id="`nav_guide_menu_subMenuChildren_item_${index}`"
            :disabled="bootstrapBegin"
          >
            <i class="iconfont" :class="subMenu.meta.icon"></i>
            <span slot="title" :style="subMenu.router.path | isCurrent" :data-id="subMenu.router.path">
              {{ subMenu.name }}
            </span>
          </el-menu-item>
          <el-submenu
            :index="subMenu.id"
            v-if="subMenu.children && subMenu.children.length"
            :key="'grand' + subMenu.id + subIndex"
          >
            <template slot="title">
              <i :class="[/^hb-iconfont/.test(subMenu.meta.icon) ? 'hb-iconfont' : 'iconfont', subMenu.meta.icon]"></i>
              <span :style="subMenu.router.path | isCurrent" :data-id="subMenu.router.path">
                {{ subMenu.name }}
              </span>
            </template>
            <template v-for="(grandMenu, grandIndex) in subMenu.children">
              <el-menu-item :index="grandMenu.router.path" :key="grandIndex" :data-id="grandMenu.router.path">
                <i class="iconfont" :class="'icon' + recode(grandMenu.code)"></i>
                <span slot="title">{{ grandMenu.name }}</span>
              </el-menu-item>
            </template>
          </el-submenu>
        </template>
      </el-submenu>
    </template>
  </el-menu>
</template>

<style lang="less" scoped>
  @import './index.less';

  .bare-menu {
    .iconfont {
      margin-right: 10px;
      margin-left: 1px;
    }
  }
  .el-menu-vertical-demo:not(.el-menu--collapse) {
    width: 200px;
    min-height: 400px;
  }
  ::v-deep .el-menu-item.is-disabled {
    opacity: 1;
  }
  ::v-deep .el-submenu.is-disabled .el-submenu__title {
    opacity: 1;
  }
  ::v-deep .el-submenu.is-disabled .el-menu-item {
    opacity: 1;
  }
</style>

<script lang="ts">
  import { Vue, Prop, Component, Watch } from 'vue-property-decorator'
  import RootModule from '@/store/RootModule'
  import Menu from '@hbfe/jxjy-admin-common/src/models/Menu'
  import UiModule from '@/store/UiModule'

  @Component({
    filters: {
      isCurrent(path: string) {
        if (!RootModule.currentFxNav) return {}
        return RootModule.currentFxNav.path === path ? { color: '#2d8cf0' } : {}
      }
    }
  })
  export default class extends Vue {
    bootstrapBegin = false
    defaultActive = ''
    @Prop(String) backgroundColor: string
    @Prop({ default: false, type: Boolean }) isCollapse: boolean

    handleClick(event: any) {
      // 阻止事件传播，防止点击子菜单标题时展开子菜单
      console.info('🚀 ~ file:index method:handleClick line:136 -----', event.stopPropagation())
      event.stopPropagation()
    }

    get startSteer() {
      return UiModule.startSteer
    }

    get menuList() {
      const currentFxActiveTopMenu = RootModule.fxMenuMap[RootModule.currentFxActiveTopMenu]
      return currentFxActiveTopMenu?.menuList || []
    }

    get currentFxNav(): Menu {
      return this.getCurrentNavigationActive()
    }

    getCurrentNavigationActive(): any {
      if (!RootModule.currentFxNav) {
        this.defaultActive = this.$route.path
        return new Menu()
      }
      this.defaultActive = RootModule.currentFxNav.path
      return RootModule.currentFxNav
    }

    recode(name: string) {
      return name?.replace(/[A-Z]/g, (item: string) => {
        return `-${item.toLowerCase()}`
      })
    }

    @Watch('$route.path', {
      immediate: true
    })
    pathChange() {
      this.getCurrentNavigationActive()
    }

    async created() {
      try {
        const getDefaultRoute = (menu: Menu): string => {
          if (menu?.children?.length) {
            return getDefaultRoute(menu.children[0])
          }
          return menu?.router?.path
        }
        if (RootModule.fxMenuList.length) {
          const path = this.$route.matched[1]?.path
          RootModule.setFxCurrentTopMenu({
            id: path,
            index: RootModule.fxMenuMap[path]?.index
          })
          RootModule.SET_DEFAULT_ROUTE_PATH(path)
        }
      } catch (e) {
        // 菜单获取失败不处理
      }
    }
    // 获取样式
    //监听新手引导
    @Watch('startSteer')
    guideWatch() {
      if (UiModule.startSteer) {
        this.bootstrapBegin = true
      } else {
        this.bootstrapBegin = false
      }
    }
  }
</script>
