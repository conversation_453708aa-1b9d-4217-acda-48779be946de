import DateScope from '@api/service/common/models/DateScope'
import PriceScope from '@api/service/common/models/PriceScope'

export default class DistributorSalesStatisticsParams {
  /**
   * 分销商名称
   */
  distributorName = ''
  /**
   * 培训商品名称
   */
  trainingProductName = ''
  /**
   * 实付金额
   */
  outOfPocketAmount: PriceScope = new PriceScope()
  /**
   * 查询时间
   */
  queryTime: DateScope = new DateScope()
  /**
   * 推广门户名称
   */
  portalPromoteTheName = ''
}
