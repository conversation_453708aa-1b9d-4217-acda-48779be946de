/*
 * @Description: 分页请求参数(线下)
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:49:36
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-09 17:14:10
 */
import {
  BillStatusChangeTimeRequest,
  OfflineInvoiceRequest,
  OfflineInvoiceBasicDataRequest,
  InvoiceBillStatusChangTimeRequest,
  DeliveryStatusChangeTimeRequest,
  OfflineInvoiceDeliveryInfoRequest,
  ExpressRequest,
  TakeResultRequest,
  DateScopeRequest,
  InvoiceAssociationInfoRequest,
  DeliveryAddressRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { DeliveryStatusEnum, DeliveryWayEnum } from '../../enum/DeliveryInvoiceEnum'

import { TitleTypeEnum, InvoiceStatusEnum, InvoiceCategoryEnum, InvoiceTypeEnum } from '../../enum/InvoiceEnum'
import SaleChannelType, { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
export default class QueryPageInvoiceParam {
  //*****************发票基础信息查询参数 basicData********************/
  /**
   * 商品id集合-培训方案
   */
  commoditySkuIdList?: string
  /**
   * 批次单号集合
   */
  orderNoList?: string
  /**
   * 买家信息
   */
  userName?: string
  idCard?: string
  phone?: string
  userId?: string
  loginAccount?: string
  /**
   * 票据开具状态 0:未开具 1：开票中 2：开票成功 3：开票失败
   */
  invoiceStatusList?: InvoiceStatusEnum
  /**
   * 冻结状态
   */
  invoiceFreezeStatus?: boolean
  /**
   * 发票Id集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票号集合
   */
  invoiceNoList?: string
  /**
   *票据开具状态变更时间  billing对应申请开票时间   success对应开票时间
   */
  billStatusChangeTime?: BillStatusChangeTimeRequest
  /**
   * 发票类型 1:电子发票 2:纸质发票 写死
   */
  invoiceType = 2
  /**
   * 发票种类 1:普通发票 2:增值税普通发票 3:增值税专用发票
   */
  invoiceCategoryList: Array<number>
  /**
   * 配送方式
   */
  shippingMethodList?: DeliveryWayEnum
  /**
   * 配送状态 0:未就绪 1：已就绪 2：已自取 3：已配送
   */
  deliveryStatusList?: DeliveryStatusEnum
  /**
   *  * 配送状态变更时间记录
   */
  deliveryStartTime?: string
  deliveryEndTime?: string
  /**
   * 快递单号
   */
  expressNo?: string
  /**
   * 领取人
   */
  takePerson?: string
  /**
   * 收件人
   */
  consignee?: string
  /*专题查询入参*/
  /**
   * 专题名称
   */
  specialSubjectName = ''

  /**
   * 是否来源专题
   */
  isFromSpecialSubject: boolean = undefined

  /**
   * 销售渠道
   */
  saleSource: SaleChannelEnum = null

  /**
   * 期别ID
   */
  periodId: string = undefined

  static to(queryPageInvoiceParam: QueryPageInvoiceParam) {
    const offlineInvoiceRequest = new OfflineInvoiceRequest()
    offlineInvoiceRequest.issueId = queryPageInvoiceParam.periodId ? [queryPageInvoiceParam.periodId] : []
    offlineInvoiceRequest.basicData = new OfflineInvoiceBasicDataRequest()
    offlineInvoiceRequest.basicData.invoiceCategory = queryPageInvoiceParam.invoiceCategoryList
    offlineInvoiceRequest.basicData.billStatusChangTime = new InvoiceBillStatusChangTimeRequest()
    offlineInvoiceRequest.basicData.invoiceTypeList = [queryPageInvoiceParam.invoiceType]
    offlineInvoiceRequest.basicData.commoditySkuIdList = queryPageInvoiceParam.commoditySkuIdList
      ? [queryPageInvoiceParam.commoditySkuIdList]
      : undefined
    offlineInvoiceRequest.basicData.billStatusList =
      queryPageInvoiceParam.invoiceStatusList === 0 || queryPageInvoiceParam.invoiceStatusList
        ? [queryPageInvoiceParam.invoiceStatusList]
        : undefined
    offlineInvoiceRequest.basicData.freeze = queryPageInvoiceParam.invoiceFreezeStatus
    offlineInvoiceRequest.basicData.invoiceNoList = queryPageInvoiceParam.invoiceNoList
      ? [queryPageInvoiceParam.invoiceNoList]
      : undefined
    offlineInvoiceRequest.basicData.billStatusChangTime.unBillDateScope =
      queryPageInvoiceParam.billStatusChangeTime?.billing
    offlineInvoiceRequest.basicData.billStatusChangTime.successDateScope =
      queryPageInvoiceParam.billStatusChangeTime?.success
    offlineInvoiceRequest.invoiceIdList = queryPageInvoiceParam.invoiceIdList
    offlineInvoiceRequest.invoiceDeliveryInfo = new OfflineInvoiceDeliveryInfoRequest()
    offlineInvoiceRequest.invoiceDeliveryInfo.shippingMethodList = queryPageInvoiceParam.shippingMethodList
      ? [queryPageInvoiceParam.shippingMethodList]
      : undefined
    if (queryPageInvoiceParam.deliveryStatusList === 3) {
      offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusList = [2, 3]
    } else {
      offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusList =
        queryPageInvoiceParam.deliveryStatusList === 0 || queryPageInvoiceParam.deliveryStatusList
          ? [queryPageInvoiceParam.deliveryStatusList]
          : undefined
    }
    offlineInvoiceRequest.invoiceDeliveryInfo.express = new ExpressRequest()
    offlineInvoiceRequest.invoiceDeliveryInfo.express.expressNo = queryPageInvoiceParam.expressNo
      ? queryPageInvoiceParam.expressNo
      : undefined
    offlineInvoiceRequest.associationInfo = new InvoiceAssociationInfoRequest()
    if (queryPageInvoiceParam.orderNoList) {
      offlineInvoiceRequest.associationInfo.associationIdList = [queryPageInvoiceParam.orderNoList]
    } else {
      offlineInvoiceRequest.associationInfo.associationIdList = undefined
    }
    offlineInvoiceRequest.associationInfo.associationType = 1

    offlineInvoiceRequest.associationInfo.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(
      queryPageInvoiceParam.isFromSpecialSubject
    )
    // if (queryPageInvoiceParam.saleSource || queryPageInvoiceParam.saleSource === SaleChannelEnum.self) {
    //   offlineInvoiceRequest.associationInfo.saleChannels = [queryPageInvoiceParam.saleSource]
    // } else {
    //   offlineInvoiceRequest.associationInfo.saleChannels = [
    //     SaleChannelEnum.self,
    //     SaleChannelEnum.distribution,
    //     SaleChannelEnum.topic
    //   ]
    // }
    offlineInvoiceRequest.associationInfo.saleChannelName = queryPageInvoiceParam.specialSubjectName
    offlineInvoiceRequest.invoiceDeliveryInfo.takeResult = new TakeResultRequest()
    offlineInvoiceRequest.invoiceDeliveryInfo.takeResult.takePerson = queryPageInvoiceParam.takePerson
      ? queryPageInvoiceParam.takePerson
      : undefined
    offlineInvoiceRequest.invoiceDeliveryInfo.deliveryAddress = new DeliveryAddressRequest()
    offlineInvoiceRequest.invoiceDeliveryInfo.deliveryAddress.consignee = queryPageInvoiceParam.consignee
      ? queryPageInvoiceParam.consignee
      : undefined
    offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime = new DeliveryStatusChangeTimeRequest()
    switch (queryPageInvoiceParam.deliveryStatusList) {
      case 0:
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.unReady = new DateScopeRequest()
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.unReady.begin =
          queryPageInvoiceParam.deliveryStartTime
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.unReady.end =
          queryPageInvoiceParam.deliveryEndTime
        break
      case 1:
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.ready = new DateScopeRequest()
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.ready.begin =
          queryPageInvoiceParam.deliveryStartTime
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.ready.end =
          queryPageInvoiceParam.deliveryEndTime
        break
      case 3:
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.taken = new DateScopeRequest()
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.taken.begin =
          queryPageInvoiceParam.deliveryStartTime
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.taken.end =
          queryPageInvoiceParam.deliveryEndTime
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.shipped = new DateScopeRequest()
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.shipped.begin =
          queryPageInvoiceParam.deliveryStartTime
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.shipped.end =
          queryPageInvoiceParam.deliveryEndTime
        break
    }
    return offlineInvoiceRequest
  }
}
