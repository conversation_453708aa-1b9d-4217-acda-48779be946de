/**
 * 学习请求响应代码
 */
export enum LeaningErrorCode {
  /**
   * 成功
   */
  SUCCESS = '00000',
  /**
   * 访问标识无效
   */
  ACCESS_KEY_INVALID = '10001',
  /**
   * 访问标识过期
   */
  ACCESS_KEY_EXPIRED = '10002',
  /**
   * 票据无效
   */
  TICKET_INVALID = '20001',
  /**
   * 票据过期
   */
  TICKET_EXPIRED = '20002',
  /**
   * 其他地方学习此课程
   */
  LEARNING_IN_OTHER = '80001',
  /**
   * 正在学习其他课程
   */
  LEARNING_OTHER_COURSE = '80002',
  /**
   * 没有进行防作弊
   */
  LEARNING_UN_ANTI = '80003',
  /**
   * 没有防作弊凭证
   */
  LEARNING_NO_ANTI_TOKEN = '80004',
  /**
   * 参数异常
   */
  ARGUMENT_ERROR = '90001',
  /**
   * 其他错误
   */
  OTHER_RUN_ERROR = '99999'
}

/**
 * 学习响应对象
 */
class LearningResponse {
  head: {
    /**
     * 响应代码，详见
     * @typedef LeaningErrorCode
     */
    code: string
    /**
     * 响应信息
     */
    message: string
  }

  data: {
    /**
     * 课程编号
     */
    courseId: string
    /**
     * 课件编号
     */
    coursewareId: string
    /**
     * 媒体编号
     */
    mediaId: string
    /**
     * 课程进度
     */
    courseSchedule: number
    /**
     * 课件进度
     */
    coursewareSchedule: number
    /**
     * 当前媒体学习进度，按百分比计算，四舍五入至小数点后两位
     */
    mediaSchedule: number
    /**
     * 当前媒体学习状态，0/1/2，未学习/学习中/学习完成
     */
    currentState: number
    /**
     * 媒体最后一次播放刻度
     */
    lastPlayScale: number
    /**
     * 当前媒体播放刻度，单位秒
     */
    mediaTimeLength: number
    /**
     * 是否变更策略，变更策略时IntervalTime改变
     */
    isChangePolicy: boolean
    /**
     * 计时间隔时间，单位秒
     */
    intervalTime: number
    /**
     * 是否必须刷新，由于重算导致的学习实体变更，需要重置客户端学习模型
     */
    isMustRefresh: boolean
    /**
     * 当前子项目下用户学习的唯一票据
     */
    ticket: string
  }

  /**
   * 是否处理成功
   * @return true/false
   */
  successfully(): boolean {
    return LeaningErrorCode.SUCCESS === this.head.code
  }

  /**
   * 获取请求响应信息
   */
  getMessage() {
    return this.head.message
  }
}

export default LearningResponse
