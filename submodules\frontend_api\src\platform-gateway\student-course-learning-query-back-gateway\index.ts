import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/student-course-learning-query-back-gateway'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'student-course-learning-query-back-gateway'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class LearningResultErrorRequest {
  /**
   * 用户身份证
   */
  userIdCard?: string
  /**
   * 用户姓名
   */
  userName?: string
  /**
   * 用户手机号
   */
  userPhone?: string
  /**
   * 方案名称
   */
  trainingName?: string
  /**
   * 学习数据异常信息类型  1.学习规则
   */
  errorType?: number
}

export class StudentTrainingResultSimulateRequest {
  /**
   * 学号 集合
   */
  studentNos?: Array<string>
  /**
   * 学员信息
   */
  user?: UserRequest
  /**
   * 学员学习班级信息
   */
  studentSchemeLearning?: SimulateStudentSchemeLearningRequest
}

/**
 * 学员班级学习信息模型
 */
export class SimulateStudentSchemeLearningRequest {
  /**
   * 班级id  集合
   */
  schemeIds?: Array<string>
  /**
   * 班级名称
   */
  trainName?: string
}

/**
 * 学员信息主题模型
 */
export class UserRequest {
  /**
   * 用户ID
   */
  userId?: string
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 手机号
   */
  phone?: string
}

export class SchemeSkuPropertyResponse {
  year: SchemeSkuPropertyValueResponse
  province: SchemeSkuPropertyValueResponse
  city: SchemeSkuPropertyValueResponse
  county: SchemeSkuPropertyValueResponse
  industry: SchemeSkuPropertyValueResponse
  subjectType: SchemeSkuPropertyValueResponse
  trainingCategory: SchemeSkuPropertyValueResponse
  trainingProfessional: SchemeSkuPropertyValueResponse
  technicalGrade: SchemeSkuPropertyValueResponse
  positionCategory: SchemeSkuPropertyValueResponse
  trainingObject: SchemeSkuPropertyValueResponse
  jobLevel: SchemeSkuPropertyValueResponse
  jobCategory: SchemeSkuPropertyValueResponse
  subject: SchemeSkuPropertyValueResponse
  grade: SchemeSkuPropertyValueResponse
  learningPhase: SchemeSkuPropertyValueResponse
  discipline: SchemeSkuPropertyValueResponse
  certificatesType: SchemeSkuPropertyValueResponse
  practitionerCategory: SchemeSkuPropertyValueResponse
  trainingInstitution: SchemeSkuPropertyValueResponse
  mainAdditionalItem: SchemeSkuPropertyValueResponse
  trainingWay: SchemeSkuPropertyValueResponse
}

export class SchemeSkuPropertyValueResponse {
  skuPropertyValueId: string
}

export class LearningResultErrorResponse {
  /**
   * 用户身份证
   */
  userIdCard: string
  /**
   * 用户姓名
   */
  userName: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 用户手机号
   */
  userPhone: string
  /**
   * 方案名称
   */
  trainingName: string
  /**
   * sku属性
   */
  skuProperty: SchemeSkuPropertyResponse
  /**
   * 操作时间
   */
  operateTime: string
  /**
   * 错误信息
   */
  errorMsg: string
  /**
   * 生成时使用的规则ID
   */
  ruleId: string
}

export class LearningResultErrorStatisticsResponse {
  /**
   * 智能学习错误数量  //暂不使用
   */
  autoLearningErrorCount: number
  /**
   * 补学数据错误数量
   */
  supplementStudyErrorCount: number
}

export class StudentTrainingResultSimulateResponse {
  /**
   * 学号
   */
  studentNo: string
  /**
   * 归属信息
   */
  owner: OwnerModel
  /**
   * 学员信息
   */
  user: UserModel
  /**
   * 学员学习班级信息
   */
  studentSchemeLearning: SimulateStudentSchemeLearningModel
  /**
   * 学员培训内容
   */
  trainingContentList: Array<TrainingContentModel>
  /**
   * 拓展信息
@see  TrainingResultSimulateConstant
   */
  expandInfo: Map<string, string>
  /**
   * // 创建时间
   */
  recordCreatedTime: string
  /**
   * @see com.fjhb.platform.jxjy.v1.kernel.consts.SimulateType
补学数据类型：1、模拟补学全部数据 2、仅模拟考试成绩 默认1
   */
  type: number
}

/**
 * @Description   归属信息
<AUTHOR>
@Date 2023/6/29 9:49
 */
export class OwnerModel {
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 单位id
   */
  unitId: string
  /**
   * 服务商ID
   */
  servicerId: string
}

/**
 * 学员班级学习信息模型
 */
export class SimulateStudentSchemeLearningModel {
  /**
   * 班级id
   */
  schemeId: string
  /**
   * 班级名称
   */
  trainName: string
  /**
   * 培训年度
   */
  trainYear: string
  /**
   * 培训形式
   */
  trainType: number
  /**
   * 科目类型
   */
  subjectType: number
  /**
   * 公需科目名称
   */
  publicSubjectName: string
  /**
   * 培训开始时间    2023-03-01 08:02:20
   */
  trainBeginTime: string
  /**
   * 培训结束时间    2023-03-01 08:02:20
   */
  trainEndTime: string
  /**
   * 合格学时
   */
  trainPeriod: string
  /**
   * 考试开始时间    2023-03-01 08:02:20
   */
  examBeginTime: string
  /**
   * 考试结束时间    2023-03-01 08:02:20
   */
  examEndTime: string
  /**
   * 考试成绩
   */
  score: string
  /**
   * 合格时间      2023-03-12 13:13:13
   */
  qualifiedTime: string
  /**
   * 培训属性
@see SkuPropertyType
   */
  skuProperty: Map<string, string>
}

/**
 * 培训内容
 */
export class TrainingContentModel {
  /**
   * 学员课程ID
   */
  studentCourseId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 课程科目类型
   */
  courseSubjectType: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 课程学时
   */
  period: number
  /**
   * 课程开始学习时间
   */
  startTime: string
  /**
   * 课程结束结束时间
   */
  endTime: string
  /**
   * 测验分数
   */
  quizScore: number
  /**
   * 测试开始时间
   */
  quizStartTime: string
  /**
   * 测试结束时间
   */
  quizEndTime: string
}

/**
 * 学员信息主题模型
 */
export class UserModel {
  /**
   * 用户ID
   */
  userId: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 手机号
   */
  phone: string
}

export class LearningResultErrorResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<LearningResultErrorResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习异常统计数据
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getLearningResultErrorStatisticsInServicer(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getLearningResultErrorStatisticsInServicer,
    operation?: string
  ): Promise<Response<LearningResultErrorStatisticsResponse>> {
    return commonRequestApi<LearningResultErrorStatisticsResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取学习异常统计数据
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getLearningResultErrorStatisticsInTrainingChannel(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getLearningResultErrorStatisticsInTrainingChannel,
    operation?: string
  ): Promise<Response<LearningResultErrorStatisticsResponse>> {
    return commonRequestApi<LearningResultErrorStatisticsResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询学习类型
   * 学习类型 0正常学习 1一键合格 2补学
   * @return
   * @param query 查询 graphql 语法文档
   * @param studentNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getLearningType(
    studentNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getLearningType,
    operation?: string
  ): Promise<Response<Array<number>>> {
    return commonRequestApi<Array<number>>(
      SERVER_URL,
      {
        query: query,
        variables: { studentNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取模拟学习数据
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudentTrainingResultSimulateResponseInServicer(
    request: StudentTrainingResultSimulateRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentTrainingResultSimulateResponseInServicer,
    operation?: string
  ): Promise<Response<Array<StudentTrainingResultSimulateResponse>>> {
    return commonRequestApi<Array<StudentTrainingResultSimulateResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取学习异常数据
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageLearningResultErrorInServicer(
    params: { page?: Page; request?: LearningResultErrorRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageLearningResultErrorInServicer,
    operation?: string
  ): Promise<Response<LearningResultErrorResponsePage>> {
    return commonRequestApi<LearningResultErrorResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取学习异常数据 专题管理员
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageLearningResultErrorInTrainingChannel(
    params: { page?: Page; request?: LearningResultErrorRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageLearningResultErrorInTrainingChannel,
    operation?: string
  ): Promise<Response<LearningResultErrorResponsePage>> {
    return commonRequestApi<LearningResultErrorResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重新生成补学数据
   * @return
   * @param query 查询 graphql 语法文档
   * @param studentNos 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async reGenerateStudentTrainingResultSimulateInServicer(
    studentNos: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.reGenerateStudentTrainingResultSimulateInServicer,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { studentNos },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重推学员学习合格数据
   * @return
   * @param query 查询 graphql 语法文档
   * @param studentNos 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rePushStudentTrainingResultInServicer(
    studentNos: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.rePushStudentTrainingResultInServicer,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { studentNos },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重推学员学习合格数据-统一网关
   * @return
   * @deprecated 后续删除
   * @param query 查询 graphql 语法文档
   * @param studentNos 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rePushStudentTrainingResultToGatewayInServicer(
    studentNos: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.rePushStudentTrainingResultToGatewayInServicer,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { studentNos },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
