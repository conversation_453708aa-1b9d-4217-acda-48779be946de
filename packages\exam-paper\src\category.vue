<route-meta>
{
  "isMenu":false,
  "onlyShowOnTab":true,
  "title":"试卷分类"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('category')" desc="分类" actions="init" :key="refreshKey">
    <div class="f-p15">
      <div class="f-mb15">
        <template v-if="$hasPermission('create')" desc="新建" actions="commit">
          <el-button type="primary" icon="el-icon-plus" @click="clickItemAdd">新建分类</el-button>
        </template>

        <el-button type="text" size="small" icon="el-icon-refresh" class="f-ml10" @click="init">刷新表格</el-button>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <!--表格-->
        <el-table
          :data="paperPublishCategoryResponseVo"
          row-key="id"
          lazy
          :load="load"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          max-height="500px"
          class="m-table"
        >
          <el-table-column prop="name" min-width="400">
            <template slot="header">
              <span class="f-ml5">分类名称</span>
            </template>
          </el-table-column>
          <el-table-column prop="field02" label="操作" width="250" align="center" fixed="right">
            <template slot-scope="{ row }">
              <template v-if="$hasPermission('detail')" desc="详情" actions="classificationDetails">
                <el-button type="text" size="mini" @click="classificationDetails(row)">详情</el-button>
              </template>
              <template v-if="$hasPermission('modify')" desc="修改" actions="commit">
                <el-button type="text" size="mini" @click="clickItemModify(row)">编辑</el-button>
              </template>

              <!-- <el-popconfirm
                confirmButtonText="确定"
                cancelButtonText="取消"
                icon="el-icon-info"
                iconColor="red"
                placement="top"
                title="确定删除试卷分类吗？"
                @confirm="clickItemDelete(row)"
              > -->
              <!-- <el-button type="text" size="mini" @click="clickItemDelete(row)">删除</el-button> -->
              <template v-if="$hasPermission('remove')" desc="删除" actions="clickItemDelete">
                <hb-popconfirm placement="top" title="确定删除该试卷分类吗？" @confirm="clickItemDelete(row)">
                  <el-button type="text" slot="reference" size="mini">删除</el-button>
                </hb-popconfirm>
              </template>

              <!-- </el-popconfirm> -->
            </template>
          </el-table-column>
        </el-table>
        <el-drawer title="试卷分类详情" :visible.sync="classificationDetailDialog" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="currentNode" label-width="auto" class="m-text-form is-column f-mt20">
                  <el-form-item label="所属节点：">{{ currentNodeName }}</el-form-item>
                  <el-form-item label="分类名称：">{{ currentNode.name }}</el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <el-drawer
          :title="uiConfig.operationType === 1 ? '新建分类' : '编辑分类'"
          :visible.sync="classificationDialog"
          :close-on-press-escape="false"
          :wrapper-closable="false"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form
                  ref="categoryFromRef"
                  :model="target.createExamPaperCategoryParams"
                  :rules="rules"
                  label-width="auto"
                  class="m-form f-mt20"
                >
                  <!-- {{ target.createExamPaperCategoryParams.parentId }} -->
                  <el-form-item label="所属节点：" prop="parentId">
                    <paper-classify-tree
                      v-model="target.createExamPaperCategoryParams.parentId"
                      :default-node="currentNode.parentNode"
                      :unitId="currentNode.unitId"
                      v-if="uiConfig.operationType == 1 && classifyTree"
                    />
                    <span v-else>{{ currentNodeName }}</span>
                  </el-form-item>
                  <el-form-item label="分类名称：" prop="name">
                    <el-input
                      v-model="target.createExamPaperCategoryParams.name"
                      clearable
                      maxlength="30"
                      show-word-limit
                      placeholder="请输入分类名称，不超过30个字"
                    />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button @click="cancelCommit">取消</el-button>
                    <el-button type="primary" @click="commit()" :loading="saveLoading">保存</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Watch, Ref } from 'vue-property-decorator'
  import { ExamPaperClassifyUI } from '@hbfe/jxjy-admin-components/src/models/ExamPaperClassifyUI'
  import PaperClassifyTree from '@hbfe/jxjy-admin-components/src/paper-classify-tree.vue'
  import { ElForm } from 'element-ui/types/form'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { UiPage, Query } from '@hbfe/common'
  import PaperPublishCategoryResponseVo from '@api/service/management/resource/exam-paper-category/query/vo/PaperPublishCategoryResponseVo'
  import ExamPaperCategoryAction from '@api/service/management/resource/exam-paper-category/mutation/ExamPaperCategoryAction'
  import CreateExamPaperCategoryDto from '@api/service/management/resource/exam-paper-category/mutation/vo/CreateExamPaperCategoryVo'
  import CreateExamCategory from '@api/service/management/resource/exam-paper-category/mutation/CreateExamPaperCategory'
  import QueryExamPaperList from '@api/service/management/resource/exam-paper/query/QueryExamPaperList'
  import PaperPublishConfigureRequestVo from '@api/service/management/resource/exam-paper/query/vo/PaperPublishConfigureRequestVo'
  class HasPaperPublishCategoryResponseVo extends PaperPublishCategoryResponseVo {
    hasChildren = true
    children? = new Array<HasPaperPublishCategoryResponseVo>()
  }
  /**
   *  当前操作的节点
   */
  class CurrentPaperClassifyNode {
    id = ''
    name = ''
    parentId = ''
    parentName = ''
    unitId = ''
    parentNode = new ExamPaperClassifyUI()
  }
  @Component({
    components: { PaperClassifyTree }
  })
  export default class extends Vue {
    page: UiPage
    query: Query = new Query()
    refName = 'categoryFrom'
    formCheck = true
    saveLoading = false
    currentNodeName = ''
    paperPublishCategoryResponseVo = new Array<HasPaperPublishCategoryResponseVo>()
    // 获取试卷查询工厂
    queryExamPaperCategory = ResourceModule.queryExamPaperCategoryFactory
    // 获取试卷业务工厂
    mutationExamPaperCategory = ResourceModule.mutationExamPaperCategoryFactory
    // 查询试卷资源
    queryExamPaperList = new QueryExamPaperList()

    // 表单刷新
    refreshKey = 1
    // 表单刷新延时
    refreshTime = 1500

    uiConfig = {
      operationType: 1,
      unitDisable: false
    }
    classifyTree = true // 主要是 tree这个组件生产了数据树之后无法重新更新，只能这样删除了
    classificationDialog = false
    classificationDetailDialog = false
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    @Ref('elForm') elForm: ElForm
    target: CreateExamCategory = new CreateExamCategory()

    @Ref('categoryFromRef')
    categoryFromRef: ElForm
    @Ref('paperClassifyRef')
    pageData = new Array<ExamPaperClassifyUI>()
    editItemCategory: HasPaperPublishCategoryResponseVo
    editCategory: ExamPaperCategoryAction
    /**
     * 当前操作的节点数据
     */
    // currentNode = new HasPaperPublishCategoryResponseVo()
    currentNode: CreateExamPaperCategoryDto =
      ResourceModule.mutationExamPaperCategoryFactory.createExamCategory.createExamPaperCategoryParams
    @Watch('currentNode', {
      deep: true,
      immediate: true
    })
    currentNodeChange(val: any) {
      if (val) {
        console.log(val, 'valval')
      }
    }

    @Watch('target.createExamPaperCategoryParams.parentId', {
      deep: true
    })
    parentIdChange(val: any) {
      if (val) {
        this.categoryFromRef && this.categoryFromRef.clearValidate('parentId')
      }
      // else {
      //   this.categoryFromRef && this.categoryFromRef.validateField('parentId')
      // }
    }

    rules = {
      parentId: [
        {
          required: true,
          message: '所属节点必选！',
          trigger: 'change'
        }
      ],
      name: [
        {
          required: true,
          message: '试卷分类名称不可为空！',
          trigger: ['blur', 'change']
        },
        {
          validator: this.checkName,
          trigger: 'blur'
        }
      ]
    }

    async created() {
      await this.init()
    }

    // 确认创建试卷分类
    async commit() {
      this.saveLoading = false
      this.categoryFromRef.validate(async (val: any) => {
        if (val) {
          if (this.uiConfig.operationType === 1) {
            // 没有机构id就创建平台级别的试卷分类
            await this.createPaperClassification()
          } else {
            // 更新
            await this.editCommit()
          }
        }
      })
    }

    // 校验试卷分类名称
    async checkName(rule: any, value: any, callback: any) {
      const res = await this.queryExamPaperCategory.queryQueryExamCategory.queryExamPaperCategoryList(
        this.page,
        this.target.createExamPaperCategoryParams.parentId
      )
      if (!res?.status?.isSuccess()) {
        return this.$message.error('查询试卷分类名称失败！')
      }
      res.data?.forEach(p => {
        if (p.name == this.target.createExamPaperCategoryParams.name) {
          callback(new Error('分类名称重复'))
          return
        }
      })
    }
    // 初始化调用
    async init() {
      await this.doQueryPage()
    }
    // 分页查询
    async doQueryPage() {
      this.paperPublishCategoryResponseVo = new Array<HasPaperPublishCategoryResponseVo>()
      const parentId = '-1'
      this.page.pageSize = 200
      const res = await this.queryExamPaperCategory.queryQueryExamCategory.queryExamPaperCategoryList(
        this.page,
        parentId
      )
      res.data?.forEach(p => {
        const category = new HasPaperPublishCategoryResponseVo()
        category.parentCategoryId = p.parentCategoryId
        category.name = p.name
        category.id = p.id
        this.paperPublishCategoryResponseVo.push(category)
      })
    }

    // 监听unitId变化 然后重新创建所属节点
    // 主要是 tree这个组件生产了数据树之后无法重新更新，只能这样删除了
    @Watch('currentNode.unitId')
    unitIdChange() {
      this.classifyTree = false
      setTimeout(() => {
        this.classifyTree = true
      }, 50)
    }
    load(tree: HasPaperPublishCategoryResponseVo, treeNode: any, resolve: any) {
      this.suboad(tree, resolve)
    }

    // 次级节点 tree父节点信息
    async suboad(tree: HasPaperPublishCategoryResponseVo, resolve: any) {
      const res = await this.queryExamPaperCategory.queryQueryExamCategory.queryExamPaperCategoryList(
        this.page,
        tree.id
      )
      const categoryUIList = new Array<HasPaperPublishCategoryResponseVo>()
      res.data?.forEach(p => {
        const category = new HasPaperPublishCategoryResponseVo()
        category.parentCategoryId = p.parentCategoryId
        category.name = p.name
        category.id = p.id
        category.parentCategoryName = p.parentCategoryName
        categoryUIList.push(category)
      })
      resolve(categoryUIList)
    }
    /**
     * 删除试卷分类
     */
    async clickItemDelete(item: PaperPublishCategoryResponseVo) {
      // this.$confirm('确定删除该试卷分类吗？', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'error'
      // })
      //   .then(async () => {
      const params = new PaperPublishConfigureRequestVo()
      params.categoryIdList = [item.id]
      // 此方法只能判断当前分类下的有无资源,若分类下有新的含有试卷资源的分类,则无法判断到
      const checkResourse = await this.queryExamPaperList.queryExamPaperList(this.page, params)
      console.log('前端删除分类拦截调用queryExamPaperList======', checkResourse)
      if (checkResourse.status.isSuccess()) {
        if (checkResourse.data?.length) {
          // 试卷资源存在(前端拦截)
          this.$message.error('试卷资源已被试题使用不可删除!')
        } else {
          // 试卷资源不存在,若分类下有新的含有试卷资源的分类,需要依赖后端拦截
          try {
            const res = await this.mutationExamPaperCategory
              .examCategoryAction(item.id, item.name, item.parentCategoryId)
              .doDeleteExamPaperCategory()
            if (res.code === 200) {
              this.$message.success('删除成功！')
              this.delayToRefreshTable(() => {
                this.doQueryPage()
                this.refreshKey++
              })
            } else if (res.errors && res.errors.length) {
              // 如果报错了就用后端提示（后端拦截）
              this.$message.error(res.errors[0].message)
              throw new Error(res.errors[0].message)
            } else {
              this.$message.error('试卷资源已被试题使用不可删除!')
            }
          } catch (error) {
            console.log(error)
          }
        }
      }

      // })
      // .catch(() => {
      //   console.log('已取消删除')
      // })
    }
    /**
     * 详情试卷分类
     */
    async classificationDetails(item: HasPaperPublishCategoryResponseVo) {
      this.classificationDetailDialog = true
      if (!item.parentCategoryId) {
        this.currentNodeName = '试卷分类'
      } else {
        this.currentNodeName = item.parentCategoryName
      }
      this.currentNode.name = item.name
    }
    /**
     * 编辑试卷分类
     */
    async clickItemModify(item: HasPaperPublishCategoryResponseVo) {
      this.editCategory = new ExamPaperCategoryAction(item.id, item.name, item.parentCategoryId, item.sort)
      this.editItemCategory = item
      this.target = this.mutationExamPaperCategory.createExamCategory

      this.uiConfig.operationType = 2
      this.classificationDialog = true
      // this.currentNode.id = item.id
      this.currentNode.parentId = item.parentCategoryId
      this.target.createExamPaperCategoryParams.name = item.name

      if (!item.parentCategoryId) {
        this.currentNodeName = '试卷分类'
      } else {
        this.currentNodeName = item.parentCategoryName
      }
    }
    async editCommit() {
      this.saveLoading = true

      if (this.editItemCategory.parentCategoryId == undefined) {
        this.editItemCategory.parentCategoryId = '-1'
      }
      this.editCategory = new ExamPaperCategoryAction(
        this.editItemCategory.id,
        this.target.createExamPaperCategoryParams.name,
        this.editItemCategory.parentCategoryId
      )

      const getSubClassifyList = await this.requestSubPaperClassificationList(this.editItemCategory.id)
      if (getSubClassifyList == 'err') return
      const classifyList = (getSubClassifyList as any) as Array<PaperPublishCategoryResponseVo>
      const nameRepeat = classifyList.filter(item => {
        return item.name == this.target.createExamPaperCategoryParams.name
      })

      if (nameRepeat.length > 0) {
        // 创建子分类和原来的子分类重名
        this.$message.warning('分类名称重复')
      } else {
        const res = await this.editCategory.doUpdateExamPaperCategory()
        if (res.code === 200) {
          this.$message.success('更新成功')
          this.classificationDialog = false
          this.delayToRefreshTable(() => {
            this.doQueryPage()
            this.refreshKey++
          })
        } else {
          this.$message.warning('更新失败')
          this.classificationDialog = false
        }
      }
      this.saveLoading = false
    }
    async cancelCommit() {
      this.classificationDialog = false
      this.pageData = new Array<ExamPaperClassifyUI>()
    }

    async clickItemAdd() {
      const el: any = this.$refs.categoryFromRef

      this.uiConfig.operationType = 1
      this.target = new CreateExamCategory()
      this.classifyTree = false
      setTimeout(() => {
        this.classifyTree = true
      }, 50)
      this.target = this.mutationExamPaperCategory.createExamCategory
      this.target.createExamPaperCategoryParams.parentId = ''
      this.classificationDialog = true
      if (el) {
        el.resetFields()
      }
      // await this.target.doCreateExamCategory()
    }

    // 创建试卷分类请求
    async createPaperClassification() {
      this.saveLoading = true

      const res = await this.target.doCreateExamCategory()
      if (res.code == 200) {
        this.$message.success('创建成功')
        this.classificationDialog = false
        this.delayToRefreshTable(() => {
          this.doQueryPage()
          this.refreshKey++
        })
      } else if (res.errors && res.errors.length) {
        this.$message.error(res.errors[0].message)
      }
      this.saveLoading = false
    }

    // 查询子节点
    async requestSubPaperClassificationList(parentId: string) {
      const res = await this.queryExamPaperCategory.queryQueryExamCategory.queryExamPaperCategoryList(
        this.page,
        parentId
      )
      if (res.data) return res.data
      return 'err'
    }

    //延时刷新表单
    delayToRefreshTable(f: Function) {
      setTimeout(() => {
        f()
      }, this.refreshTime)
    }
  }
</script>
