import AbstractEnum from '@api/service/common/enums/AbstractEnum'
import { CoursewareStatusEnum } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

class CoursewareTransformStatus extends AbstractEnum<CoursewareStatusEnum> {
  static enum = CoursewareStatusEnum

  private valueMap: Map<string, number> = new Map<string, number>()

  constructor(status?: CoursewareStatusEnum) {
    super()
    this.current = status
    this.map.set(CoursewareStatusEnum.TRANSCODING, '转换中')
    this.map.set(CoursewareStatusEnum.AVAILABLE, '转换成功')
    this.map.set(CoursewareStatusEnum.UNAVAILABLE, '转换失败')
    this.valueMap.set(CoursewareStatusEnum.TRANSCODING, 0)
    this.valueMap.set(CoursewareStatusEnum.AVAILABLE, 1)
    this.valueMap.set(CoursewareStatusEnum.UNAVAILABLE, 2)
  }

  getValue() {
    return this.valueMap.get(this.current)
  }

  isSuccess() {
    return this.current === CoursewareStatusEnum.AVAILABLE
  }
}

export default CoursewareTransformStatus
