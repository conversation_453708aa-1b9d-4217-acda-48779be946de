import MsOrder, {
  BatchInvoiceRequest,
  BatchOrderApplyInvoiceRequest,
  BatchOrderInvoiceChangeRequest,
  DeliveryAddress,
  TakePoint
} from '@api/ms-gateway/ms-order-v1'
import DistrbutionData from '@api/service/common/trade-config/query/vo/DistrbutionData'
import InvoiceData from '@api/service/common/trade-config/query/vo/InvoiceData'
import { ResponseStatus } from '@hbfe/common'
import { InvoiceTypeEnum, OrderOfInvoiceEnum } from '@hbfe/jxjy-customer-center/src/views/create-order/enum/InvoiceEnum'
import { InvoiceItemEnum } from '@hbfe/jxjy-customer-manage/src/views/enum/InvoiceEnum'
/**
 * 集体报名详情-申请开票
 */
export default class QueryInvoiceData {
  /**
   * 批次单编号
   */
  batchOrderNo = ''
  /**
   * 发票信息
   */
  invoiceData = new InvoiceData()
  /**
   * 配送方式
   */
  distribution = new DistrbutionData()
  /**
   * 需要发票组件显示哪些字段（旧）
   */
  InvoiceItem: Array<InvoiceItemEnum> = []

  /**
   * 保存发票信息
   */
  async doSaveInvoiceData() {
    const request = new BatchOrderApplyInvoiceRequest()
    request.batchOrderNo = this.batchOrderNo
    request.invoiceInfo = this.doReference()
    const { status, data } = await MsOrder.batchApplyInvoiceDirectly(request)
    let response = new ResponseStatus(200)
    if (status.isSuccess()) {
      if (data.code != '200') {
        response = new ResponseStatus(Number(data.code), data.message)
      }
    } else {
      response = status
    }
    return response
  }
  /**
   * 修改发票信息
   */
  async doUpdateInvoiceData() {
    const request = new BatchOrderInvoiceChangeRequest()
    request.batchOrderNo = this.batchOrderNo
    request.invoiceInfo = this.doReference()
    const { status, data } = await MsOrder.batchInvoiceChange(request)
    let response = new ResponseStatus(200)
    if (status.isSuccess()) {
      if (data.code != '200') {
        response = new ResponseStatus(Number(data.code), data.message)
      }
    } else {
      response = status
    }
    return response
  }

  doReference() {
    const invoiceInfo = new BatchInvoiceRequest()
    invoiceInfo.title = this.invoiceData.name
    invoiceInfo.titleType = this.invoiceData.titleType === OrderOfInvoiceEnum.PERSONAL ? 1 : 2
    invoiceInfo.taxpayerNo = this.invoiceData.code
    invoiceInfo.address = this.invoiceData.regurl
    invoiceInfo.phone = this.invoiceData.rePhone
    invoiceInfo.bankName = this.invoiceData.bank
    invoiceInfo.account = this.invoiceData.bankAccount?.replace(/\s*/g, '')
    invoiceInfo.contactEmail = this.invoiceData.email
    invoiceInfo.remark = this.invoiceData.remark
    invoiceInfo.contactPhone = this.invoiceData.phone
    if (this.invoiceData.type == InvoiceTypeEnum.INVOICEELECTRONICORDINARYVAT) {
      invoiceInfo.invoiceType = 1
      invoiceInfo.invoiceCategory = 2
    }
    if (this.invoiceData.type == InvoiceTypeEnum.INVOICETHESPECIALVAT) {
      invoiceInfo.invoiceType = 2
      if (this.InvoiceItem.includes(InvoiceItemEnum.INVOICETHESPECIALVATETICKET)) {
        invoiceInfo.invoiceType = 1
      }
      invoiceInfo.invoiceCategory = 3
    }
    if (this.invoiceData.type == InvoiceTypeEnum.INVOICETHESPECIALVAT_ELEC) {
      invoiceInfo.invoiceType = 1
      invoiceInfo.invoiceCategory = 3
    }
    invoiceInfo.invoiceMethod = 0
    invoiceInfo.invoiceMethod = this.InvoiceItem.includes(InvoiceItemEnum.ONLINE) ? 1 : 2
    if (this.invoiceData.type == 'INVOICETHESPECIALVAT') {
      invoiceInfo.invoiceMethod = 2
      invoiceInfo.businessLicensePath = this.invoiceData.charterUrl[0].name
      invoiceInfo.accountOpeningLicensePath = this.invoiceData.accountLicenceUrl[0].name
    }

    if (this.distribution.distributionConfig == 1) {
      invoiceInfo.deliveryAddress = new DeliveryAddress()
      invoiceInfo.shippingMethod = 2
      invoiceInfo.deliveryAddress.consignee = this.distribution.consignee
      invoiceInfo.deliveryAddress.phone = this.distribution.deliveryAddressPhone
      invoiceInfo.deliveryAddress.region = this.distribution.region
      invoiceInfo.deliveryAddress.address = this.distribution.address
    }
    if (this.distribution.distributionConfig == 2) {
      invoiceInfo.takePoint = new TakePoint()
      invoiceInfo.shippingMethod = 1
      invoiceInfo.takePoint.pickupLocation = this.distribution.pickupLocation
      invoiceInfo.takePoint.pickupTime = this.distribution.pickupTime
      invoiceInfo.takePoint.remark = this.distribution.takePointRemark
    }
    return invoiceInfo
  }
}
