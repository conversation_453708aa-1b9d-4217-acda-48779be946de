<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">集体报名订单</el-breadcrumb-item>
      <el-breadcrumb-item>详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <!--交易成功-->
      <el-card shadow="never" class="m-card is-header m-order-state">
        <div class="info">
          <p>报名批次单号：batch211202103521749006690002</p>
          <p class="state f-cg">交易成功</p>
          <!--<p class="state f-co">等待付款</p>-->
          <!--<p class="state f-cb">支付中</p>-->
          <!--<p class="state f-cb">开通中</p>-->
          <!--<p class="state f-c9">交易关闭</p>-->
        </div>
        <el-steps :active="4" align-center class="process">
          <el-step title="提交订单" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-myorder"></el-step>
          <el-step title="付款" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step
            title="班级开通"
            description="2021-02-02 15:23:23"
            icon="hb-iconfont icon-s-learningcenter"
          ></el-step>
          <el-step title="交易成功" description="2021-02-02 15:23:23" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
      </el-card>
      <el-tabs v-model="activeName2" type="card" class="m-tab-card">
        <el-tab-pane label="基础信息" name="first">
          <!--订单信息-->
          <el-card shadow="never" class="m-card is-header m-order-info">
            <div class="f-flex-sub f-plr20 f-pt10">
              <div class="m-tit">
                <span class="tit-txt">发票信息</span>
              </div>
              <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
                <el-col :span="24">
                  <el-form-item label="发票类型：">增值税电子普通发票</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发票状态：">
                    <el-tag type="success" size="small" class="f-mr5">已开票</el-tag>
                    <el-tag type="warning" size="small" class="f-mr5">待开票</el-tag>
                    <el-tag type="error" size="small" class="f-mr5">冻结中</el-tag>
                    <el-tag type="info" size="small" class="f-mr5">已作废</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发票号码：">035001900111</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="申请开票时间：">2021-12-12 12:12:12</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发票抬头：">单位 - 福建华博教育科技股份有限公司</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="统一社会信用代码：">25486698745856985</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="开户银行：">中国建设银行</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="开户帐号：">2548 6698 7458 5698 215</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="注册电话：">0591-87459632</el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="注册地址：">福建省福州市鼓楼区工业路611号</el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label=" ">
                    <el-button type="primary">下载发票</el-button>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label=" ">
                    <el-alert type="info" show-icon :closable="false" class="m-alert">
                      发票查询地址：
                      <a href="https://inv-veri.chinatax.gov.cn/" target="_blank" class="f-link">
                        https://inv-veri.chinatax.gov.cn/
                      </a>
                    </el-alert>
                  </el-form-item>
                </el-col>
              </el-form>
              <!--未填写发票信息-->
              <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
                <el-col :span="12">
                  <el-form-item label="是否需要发票：">否</el-form-item>
                </el-col>
              </el-form>
              <div class="m-tit">
                <span class="tit-txt">分销信息</span>
              </div>
              <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
                <el-col :span="12">
                  <el-form-item label="分销商：">这里读取分销商名称</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="分销层级：">一级</el-form-item>
                </el-col>
              </el-form>
              <div class="m-tit">
                <span class="tit-txt">配送信息</span>
              </div>
              <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
                <el-col :span="12">
                  <el-form-item label="配送方式：">邮寄</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="快递公司：">中国邮政</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="收货地址：">福州市工业路 611号</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="收件人：">林林一</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="手机号：">13003831001</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="运单号：">
                    435235235
                    <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-document-copy f-link-gray f-ml5 f-c9"></i>
                      <div slot="content">复制运单号并查询</div>
                    </el-tooltip>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发货时间：">2020-07-17 15:12:43</el-form-item>
                </el-col>
              </el-form>
            </div>
            <div class="right f-plr20 f-ptb10">
              <div class="m-tit">
                <span class="tit-txt">购买人信息</span>
              </div>
              <el-form label-width="120px" class="m-text-form is-column f-pt10 f-pb20">
                <el-form-item label="购买人：">林林一</el-form-item>
                <el-form-item label="帐号：">350702198801240811</el-form-item>
              </el-form>
              <div class="m-tit">
                <span class="tit-txt">支付信息</span>
              </div>
              <el-form label-width="120px" class="m-text-form is-column f-pt10 f-pb20">
                <el-form-item label="支付方式：">线上支付-web端-支付宝</el-form-item>
                <el-form-item label="交易号：">ZFBP2020032616553398574899791001211</el-form-item>
                <el-form-item label="付款时间：">2020-07-17 11:49:21</el-form-item>
                <el-form-item label="付款成功时间：">-</el-form-item>
                <!--线下支付-->
                <el-form-item label="汇款凭证：">
                  <div class="course-pic is-small img-hover f-mb10">
                    <el-image
                      class="course-pic is-small"
                      src="/assets/images/demo-invoice.png"
                      :preview-src-list="['/assets/images/demo-invoice.png']"
                    />
                    <p class="hover-txt">点击图片查看大图</p>
                  </div>
                  <div class="course-pic is-small img-hover f-mb10">
                    <el-image
                      class="course-pic is-small"
                      src="/assets/images/demo-invoice.png"
                      :preview-src-list="['/assets/images/demo-invoice.png']"
                    />
                    <p class="hover-txt">点击图片查看大图</p>
                  </div>
                </el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="购买清单" name="second">
          <el-card shadow="never" class="m-card f-mb15">
            <!--条件查询-->
            <el-row :gutter="16" class="m-query f-pt10">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="6">
                  <el-form-item label="订单号">
                    <el-input v-model="input" clearable placeholder="请输入订单号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="6">
                  <el-form-item label="身份证号">
                    <el-input v-model="input" clearable placeholder="请输入身份证号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="6">
                  <el-form-item label="学员姓名">
                    <el-input v-model="input" clearable placeholder="请输入学员姓名" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="6" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <el-alert type="warning" :closable="false" class="m-alert">
              <div class="f-c3 f-flex f-align-center">
                <div class="f-flex-sub">
                  <span class="f-mr50">报名人次：2</span>
                  <span class="f-mr50">总学时数：20</span>
                  <span class="f-mr50">商品总金额：10</span>
                  <span class="f-mr50">实付金额：￥10.00</span>
                  <span class="f-mr50">退款金额：￥5.00</span>
                </div>
                <el-button type="primary" size="small" class="f-fr">批量退款</el-button>
                <el-tooltip class="item" effect="dark" placement="bottom" popper-class="m-tooltip">
                  <i class="el-icon-question m-tooltip-icon f-c9 f-ml10"></i>
                  <div slot="content">
                    批量退款只有批次内不存在退款处理中的订单才能再次发起退款。退款成功的订单无法再次发起退款。
                  </div>
                </el-tooltip>
              </div>
            </el-alert>
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table f-mt10">
              <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="订单号" min-width="240">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <p><a href="#" class="f-link f-underline f-cb">211202103524749006690164</a></p>
                    <el-tag type="danger" size="small">换班</el-tag>
                    <a href="#" class="f-link f-underline f-cb f-f13">查看关联换班信息</a>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <p><a href="#" class="f-link f-underline f-cb">211202103524749006690164</a></p>
                    <el-tag type="success" size="small">专题</el-tag>
                  </div>
                  <div v-else>
                    <p><a href="#" class="f-link f-underline f-cb">211202103524749006690164</a></p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="购买学员信息" min-width="250">
                <template>
                  <p>姓名：张依依</p>
                  <p>手机号：15659135119</p>
                  <p>身份证号：350103199001011234</p>
                </template>
              </el-table-column>
              <el-table-column label="学时" min-width="120" align="center">
                <template>20</template>
              </el-table-column>
              <el-table-column label="状态" min-width="120">
                <template>
                  <el-badge is-dot type="warning" class="badge-status">等待付款</el-badge>
                </template>
              </el-table-column>
              <el-table-column label="实付金额(元)" min-width="140" align="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">3.15</div>
                  <div v-else-if="scope.$index === 1">52.36</div>
                  <div v-else>158.15</div>
                </template>
              </el-table-column>
              <el-table-column label="退货/款状态" min-width="140" align="center">
                <template>
                  <el-tag type="warning">已部分退款，未退货</el-tag>
                  <div>
                    <el-button type="text" size="mini">查看记录</el-button>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>
      </el-tabs>
      <!--其他状态-->
      <!--等待付款-->
      <el-card shadow="never" class="m-card is-header m-order-state">
        <div class="info">
          <p>报名批次单号：batch211202103521749006690002</p>
          <p class="state f-co">等待付款</p>
        </div>
        <el-steps :active="1" align-center class="process">
          <el-step title="提交订单" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-myorder"></el-step>
          <el-step title="付款" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step title="班级开通" icon="hb-iconfont icon-s-learningcenter"></el-step>
          <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
      </el-card>
      <!--支付中-->
      <el-card shadow="never" class="m-card is-header m-order-state">
        <div class="info">
          <p>报名批次单号：batch211202103521749006690002</p>
          <p class="state f-cb">支付中</p>
        </div>
        <el-steps :active="1" align-center class="process">
          <el-step title="提交订单" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-myorder"></el-step>
          <el-step title="付款" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step title="班级开通" icon="hb-iconfont icon-s-learningcenter"></el-step>
          <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
      </el-card>
      <!--开通中-->
      <el-card shadow="never" class="m-card is-header m-order-state">
        <div class="info">
          <p>报名批次单号：batch211202103521749006690002</p>
          <p class="state f-cb">开通中</p>
        </div>
        <el-steps :active="3" align-center class="process">
          <el-step title="提交订单" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-myorder"></el-step>
          <el-step title="付款" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step
            title="班级开通"
            description="2021-02-02 15:23:23"
            icon="hb-iconfont icon-s-learningcenter"
          ></el-step>
          <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
      </el-card>
      <!--交易关闭-->
      <el-card shadow="never" class="m-card is-header m-order-state">
        <div class="info">
          <p>报名批次单号：batch211202103521749006690002</p>
          <p class="state f-c9">交易关闭</p>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step title="提交订单" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-myorder"></el-step>
          <el-step title="交易关闭" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-close"></el-step>
        </el-steps>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
