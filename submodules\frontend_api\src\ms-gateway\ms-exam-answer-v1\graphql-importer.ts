import getQuestionToAnswerByQuestionIds from './queries/getQuestionToAnswerByQuestionIds.graphql'
import queryAnswerPaperAvailable from './queries/queryAnswerPaperAvailable.graphql'
import applyAnswer from './mutates/applyAnswer.graphql'
import applyHanding from './mutates/applyHanding.graphql'
import getRemainingAnswerTime from './mutates/getRemainingAnswerTime.graphql'
import handedQuestionnaireAnswerPaper from './mutates/handedQuestionnaireAnswerPaper.graphql'
import preSubmitAnswer from './mutates/preSubmitAnswer.graphql'

export {
  getQuestionToAnswerByQuestionIds,
  queryAnswerPaperAvailable,
  applyAnswer,
  applyHanding,
  getRemainingAnswerTime,
  handedQuestionnaireAnswerPaper,
  preSubmitAnswer
}
