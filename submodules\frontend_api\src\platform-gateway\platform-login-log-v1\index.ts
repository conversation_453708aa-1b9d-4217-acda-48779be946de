import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-login-log-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-login-log-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

/**
 * 登录日志查询条件
 */
export class LoginLogRequest {
  /**
   * 用户名
   */
  userName?: string
  /**
   * 用户ID列表
   */
  userIdList?: Array<string>
  /**
   * 事件类型<br/>
SSOLoginEvent登录 SSOLogoutEvent登出 SSOReAuthLoginEvent重授权
   */
  eventType?: string
  /**
   * IP地址
   */
  ipList?: Array<string>
  /**
   * 事件时间
   */
  eventTime?: DateScopeRequest
}

/**
 * 登录日志响应体
 */
export class LoginLogResponse {
  /**
   * 主键ID
   */
  id: string
  /**
   * 账户id
   */
  accountId: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名
   */
  userName: string
  /**
   * 事件类型
   */
  eventType: string
  /**
   * 事件时间
   */
  eventTime: string
  /**
   * 登录ip
   */
  loginIp: string
  /**
   * 登录类型
   */
  clientType: string
  /**
   * 登录时间，仅当登录/重授权事件时有值
   */
  loginTime: string
  /**
   * 登出时间，仅当登出事件时有值
   */
  logoutTime: string
}

export class LoginLogResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<LoginLogResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询登录日志
   * @param page    分页条件
   * @param request 查询条件
   * @return 分页数据
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageLoginLogList(
    params: { page?: Page; request?: LoginLogRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageLoginLogList,
    operation?: string
  ): Promise<Response<LoginLogResponsePage>> {
    return commonRequestApi<LoginLogResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
