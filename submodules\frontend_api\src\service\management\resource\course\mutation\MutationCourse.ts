import { ResponseStatus } from '@hbfe/common'
import MsCourseResourceV1 from '@api/ms-gateway/ms-course-resource-v1'

class MutationCourse {
  /*
   课程id
   */
  private readonly courseId: string

  // 是否被删除
  isRemoved = false
  // 是否启用
  isEnable = false

  constructor(courseId: string) {
    this.courseId = courseId
  }

  private async changeStatus(type: boolean): Promise<ResponseStatus> {
    // 启用
    if (type) {
      const result = await MsCourseResourceV1.enableCourse(this.courseId)
      return result.status
    } else {
      const result = await MsCourseResourceV1.disableCourse(this.courseId)
      return result.status
    }
  }

  /**
   * 启用课程
   */
  async doEnable(): Promise<ResponseStatus> {
    return this.changeStatus(true)
  }

  /**
   * 禁用课程
   */
  async doDisable(): Promise<ResponseStatus> {
    return this.changeStatus(false)
  }

  /**
   * 根据 id 删除课程
   */
  async doRemove(): Promise<ResponseStatus> {
    const result = await MsCourseResourceV1.removeCourse(this.courseId)
    let message = '删除成功'
    if (!result.status.isSuccess()) {
      message = result.status.getMessage() || '删除失败'
    }
    return new ResponseStatus(result.status.code, message)
  }

  /**
   * 根据
   * // todo
   * @param idList
   */
  async doRemoveByIdList(idList: Array<string>): Promise<ResponseStatus> {
    return Promise.resolve(new ResponseStatus(200, ''))
  }
}

export default MutationCourse
