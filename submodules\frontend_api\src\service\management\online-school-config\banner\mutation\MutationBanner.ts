import MsServicerV1, { TrainingInstitutionBannerListSaveRequest } from '@api/ms-gateway/ms-servicer-v1'
import BannerVo from '@api/service/common/online-school-config/vo/BannerVo'
import QueryBanner from '@api/service/management/online-school-config/banner/query/QueryBanner'
import { ResponseStatus } from '@hbfe/common'

class MutationBanner {
  /**
   * 保存web轮播图 （新增、修改、移动）
   * @returns
   */
  async doSaveWebBanner(): Promise<ResponseStatus> {
    return this.doSave(1, QueryBanner.WebBannerList)
  }

  /**
   * 保存h5轮播图 （新增、修改、移动）
   * @returns
   */
  async doSaveH5Banner(): Promise<ResponseStatus> {
    return this.doSave(2, QueryBanner.h5BannerList)
  }

  private async doSave(portalType: number, bannerList: Array<BannerVo>): Promise<ResponseStatus> {
    const request = new TrainingInstitutionBannerListSaveRequest()
    request.portalType = portalType
    request.bannerSaveRequestList = bannerList?.map(BannerVo.to)
    const res = await MsServicerV1.saveTrainingInstitutionBannerList(request)
    return res.status
  }
}
export default new MutationBanner()
