<!--
 * @Author: l<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-24 19:27:01
 * @LastEditors: lixinye <EMAIL>
 * @LastEditTime: 2023-07-26 20:05:35
 * @FilePath: \jxjyv2_frontend_web_admin\src\unit-share\network-school\training\statistics-report\study-log.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-main>
      <!--面包屑-->
      <!-- <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-button type="text" size="mini" class="return-btn">
          <i class="iconfont icon-lsh-return"></i>
        </el-button>
        <el-breadcrumb-item :to="{ path: '/' }">统计报表</el-breadcrumb-item>
        <el-breadcrumb-item>学习日志</el-breadcrumb-item>
      </el-breadcrumb> -->
      <div class="f-p15">
        <!-- 数据更新时间 -->
        <!-- <UpdateTime :time="updateTime"></UpdateTime> -->

        <el-card shadow="never" class="m-card is-header f-mb15">
          <!-- 用户信息 -->
          <div slot="header">
            <span class="tit-txt">用户信息</span>
          </div>
          <template v-if="$hasPermission('userInfo')" desc="用户信息组件" actions="@UserInfo">
            <UserInfo ref="userInfo"></UserInfo>
          </template>

          <!-- 培训班信息 -->
          <div class="m-tit is-border-bottom">
            <span class="tit-txt">培训班信息</span>
          </div>
          <template v-if="$hasPermission('classInfo')" desc="培训班信息组件" actions="@ClassInfo">
            <ClassInfo ref="classInfo"></ClassInfo>
          </template>
          <!-- 登录日志 -->
          <!--<div class="m-tit is-border-bottom">-->
          <!--  <span class="tit-txt">登录日志</span>-->
          <!--</div>-->
          <!--<LoginLog></LoginLog>-->

          <!-- 学习日志 -->
          <div class="m-tit is-border-bottom">
            <span class="tit-txt">监管日志</span>
          </div>
          <template v-if="$hasPermission('studyLog')" desc="学习日志组件" actions="@StudyLog">
            <StudyLog ref="studyLog"></StudyLog>
          </template>
          <!-- 考试日志 -->
          <!--<div class="m-tit is-border-bottom">-->
          <!--  <span class="tit-txt">考试日志</span>-->
          <!--</div>-->
          <!--<ExamLog></ExamLog>-->

          <!-- 考试申诉记录 -->
          <!--<div class="m-tit is-border-bottom">-->
          <!--  <span class="tit-txt">考试申诉记录</span>-->
          <!--</div>-->
          <!--<ExamAppealRecord></ExamAppealRecord>-->
        </el-card>
      </div>
    </el-main>
  </div>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import UpdateTime from '@hbfe/jxjy-admin-statisticsReport/src/components/update-time.vue'
  import UserInfo from '@hbfe/jxjy-admin-statisticsReport/src/components/user-info.vue'
  import ClassInfo from '@hbfe/jxjy-admin-statisticsReport/src/components/class-info.vue'
  import LoginLog from '@hbfe/jxjy-admin-statisticsReport/src/components/login-log.vue'
  import StudyLog from '@hbfe/jxjy-admin-statisticsReport/src/components/study-log.vue'
  import ExamLog from '@hbfe/jxjy-admin-statisticsReport/src/components/exam-log.vue'
  import ExamAppealRecord from '@hbfe/jxjy-admin-statisticsReport/src/components/exam-appeal-record.vue'

  @Component({
    components: { UpdateTime, UserInfo, ClassInfo, LoginLog, StudyLog, ExamLog, ExamAppealRecord }
  })
  export default class extends Vue {
    activeName = 'first'
    // 数据更新时间
    updateTime = '2021-07-12 18:32:58'

    // Ref
    @Ref('userInfo') userInfo: UserInfo //用户信息
    @Ref('classInfo') classInfo: ClassInfo //培训班信息
    @Ref('studyLog') studyLog: StudyLog //学习日志

    // 用户id
    userId = ''
    // 方案id
    schemeId = ''
    // 方案类型
    schemeType = ''
    // 参训人员id
    qualificationId = ''
    // 学号
    studentNo = ''

    created() {
      this.userId = this.$route.query.userId as string
      this.schemeId = this.$route.query.schemeId as string
      this.schemeType = this.$route.query.schemeType as string
      this.qualificationId = this.$route.query.qualificationId as string
      this.studentNo = this.$route.query.studentNo as string
    }

    mounted() {
      this.loadUserInfo()
      this.loadClassInfo()
      this.loadLearningLogInfo()
    }

    // 加载用户信息
    loadUserInfo() {
      this.userInfo.loadData(this.userId)
    }

    // 加载培训班信息
    loadClassInfo() {
      this.classInfo.loadData(this.schemeId)
    }

    // 加载学习日志信息
    loadLearningLogInfo() {
      this.studyLog.initSearch()
    }
  }
</script>
