import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/Course-default'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'Course-default'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum ScaleType {
  CUSTOM = 'CUSTOM',
  SATISFACTION = 'SATISFACTION',
  RECOGNITION = 'RECOGNITION',
  IMPORTANCE = 'IMPORTANCE',
  WILLING = 'WILLING',
  CONFORMITY = 'CONFORMITY'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class MarkerDTO1 {
  key?: string
  value?: string
}

export class Sort {
  field?: string
  dir?: string
}

export class CourseQueryDTO {
  name?: string
  status: number
  enable: number
  resourceUploadType: number
  startCreateTime?: string
  endCreateTime?: string
  categoryId?: string
  categoryIdList?: Array<string>
  periodBegin?: number
  periodEnd?: number
  excludeCourseIdList?: Array<string>
  includeCourseIdList?: Array<string>
}

export class CoursePoolQueryDTO {
  poolName?: string
  exact: boolean
  poolState: number
  createStartTime?: string
  createEndTime?: string
  orderByField: number
  descending: boolean
  poolIdList?: Array<string>
  excludePoolIdList?: Array<string>
}

export class CourseInPoolQueryDTO {
  poolIdList?: Array<string>
  courseName?: string
  courseCatalogIdList?: Array<string>
}

export class CourseWareQueryDTO {
  name?: string
  type: number
  categoryId?: string
  supplierId?: string
  needHasQuestion: number
  isUsable: number
  status: number
  startCreateTime?: string
  endCreateTime?: string
  statusList?: Array<number>
  sort?: Array<Sort>
  unitId?: string
  courseId?: string
}

export class TeacherQueryDTO {
  name?: string
  createUserId?: string
}

/**
 * <AUTHOR> create 2020/4/21 7:34
 */
export class CourseCreateRequest {
  /**
   * 课程名称
   */
  name?: string
  /**
   * 封面图片路径
   */
  iconPath?: string
  /**
   * 分类id
   */
  categoryIds?: Array<string>
  /**
   * 课程简介
   */
  abouts?: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 课程详情
   */
  contents?: string
  /**
   * 课程目录
   */
  courseOutline?: Array<CourseOutLineWithSubOutlineCreateRequest>
  /**
   * 计划授课讲数
   */
  plannedLecturesNum: number
  /**
   * 教师ids
   */
  teacherIds?: Array<string>
}

/**
 * <AUTHOR> create 2020/4/21 7:36
 */
export class CourseOutLineCreateRequest {
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 挂载课件id
   */
  courseWareId?: string
  /**
   * 课件
   */
  courseWare?: CourseWareCreateRequest
  /**
   * 目录名称
   */
  name?: string
  /**
   * 上级目录ID
   */
  parentId?: string
  /**
   * 排序
   */
  sort: number
  /**
   * 挂在课件是否支持试听 0不可以试听，1可以试听，
   */
  customeStatus: number
}

/**
 * <AUTHOR> create 2020/4/21 7:36
 */
export class CourseOutLineUpdateRequest {
  /**
   * 目录id
   */
  id?: string
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 挂载课件id
   */
  courseWareId?: string
  /**
   * 课件
   */
  courseWare?: CourseWareCreateRequest
  /**
   * 目录名称
   */
  name?: string
  /**
   * 上级目录ID
   */
  parentId?: string
  /**
   * 排序
   */
  sort: number
  /**
   * 挂在课件是否支持试听 0不可以试听，1可以试听，
   */
  customeStatus: number
}

/**
 * <AUTHOR> create 2020/4/21 7:37
 */
export class CourseOutLineWithSubOutlineCreateRequest {
  /**
   * 子目录
   */
  subCourseOutlines?: Array<CourseOutLineCreateRequest>
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 挂载课件id
   */
  courseWareId?: string
  /**
   * 课件
   */
  courseWare?: CourseWareCreateRequest
  /**
   * 目录名称
   */
  name?: string
  /**
   * 上级目录ID
   */
  parentId?: string
  /**
   * 排序
   */
  sort: number
  /**
   * 挂在课件是否支持试听 0不可以试听，1可以试听，
   */
  customeStatus: number
}

/**
 * <AUTHOR> create 2020/4/21 7:37
 */
export class CourseOutLineWithSubOutlineUpdateRequest {
  /**
   * 子目录
   */
  subCourseOutlines?: Array<CourseOutLineUpdateRequest>
  /**
   * 目录id
   */
  id?: string
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 挂载课件id
   */
  courseWareId?: string
  /**
   * 课件
   */
  courseWare?: CourseWareCreateRequest
  /**
   * 目录名称
   */
  name?: string
  /**
   * 上级目录ID
   */
  parentId?: string
  /**
   * 排序
   */
  sort: number
  /**
   * 挂在课件是否支持试听 0不可以试听，1可以试听，
   */
  customeStatus: number
}

/**
 * <AUTHOR> create 2020/4/21 7:38
 */
export class CourseUpdateRequest {
  /**
   * 课程id
   */
  id?: string
  /**
   * 课程名称
   */
  name?: string
  /**
   * 封面图片路径
   */
  iconPath?: string
  /**
   * 课程简介
   */
  abouts?: string
  /**
   * 权重,表示学时,学分等
   */
  period?: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 课程详情
   */
  contents?: string
  /**
   * 所属分类的编号集合
   */
  categoryIds?: Array<string>
  /**
   * 课程目录
   */
  courseOutline?: Array<CourseOutLineWithSubOutlineUpdateRequest>
  /**
   * 计划授课讲数
   */
  plannedLecturesNum: number
  /**
   * 教师ids
   */
  teacherIds?: Array<string>
}

/**
 * <AUTHOR> create 2020/4/21 7:24
 */
export class CourseCategoryCreateRequest {
  /**
   * 课件分类名称
   */
  name?: string
  /**
   * 备注
   */
  remarks?: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 父分类ID
   */
  parentId?: string
  /**
   * 课程分类归属类型，{@link CourseCategoryBelongsType}
   */
  belongsType: number
}

/**
 * <AUTHOR> create 2020/4/21 7:27
 */
export class CourseCategoryUpdateRequest {
  /**
   * 分类编号
   */
  id?: string
  /**
   * 课件分类名称
   */
  name?: string
  /**
   * 备注
   */
  remarks?: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 父分类ID
   */
  parentId?: string
}

/**
 * <AUTHOR> create 2020/4/21 7:42
 */
export class CourseInPoolCreateRequest {
  /**
   * 课程编号
   */
  courseId?: string
  /**
   * 课程序号
   */
  sequence: number
  /**
   * 课程标量值|在课程池规则中，课程在课程池中权重值
   */
  quantitative: number
  /**
   * 课程学时|课程在课程池中的学时
   */
  period: number
  /**
   * 课程过期时间|课程在课程池中到期时间，null表示该课程在课程池中无期限限制
   */
  courseExpireTime?: string
}

/**
 * <AUTHOR> create 2020/4/21 7:45
 */
export class CourseInPoolUpdateRequest {
  /**
   * id
   */
  id?: string
  /**
   * 课程编号
   */
  courseId?: string
  /**
   * 课程序号
   */
  sequence: number
  /**
   * 课程标量值|在课程池规则中，课程在课程池中权重值
   */
  quantitative: number
  /**
   * 课程学时|课程在课程池中的学时
   */
  period: number
  /**
   * 课程过期时间|课程在课程池中到期时间，null表示该课程在课程池中无期限限制
   */
  courseExpireTime?: string
  /**
   * 特征标记列表
   */
  markers?: Array<MarkerDTO1>
}

/**
 * <AUTHOR> create 2020/4/21 7:42
 */
export class CoursePoolCreateRequest {
  /**
   * 必填，课程池名称
   */
  poolName?: string
  /**
   * 过期时间,null表示不设置过期
   */
  expireTime?: string
  /**
   * 课程池描述
   */
  poolDescription?: string
  /**
   * 展示名称
   */
  showName?: string
  /**
   * 包内课程
   */
  courseInPoolList?: Array<CourseInPoolCreateRequest>
  /**
   * 课程包所属单位id
   */
  unitId?: string
}

/**
 * <AUTHOR> create 2020/4/21 7:44
 */
export class CoursePoolUpdateRequest {
  /**
   * 课程包id
   */
  id?: string
  /**
   * 必填，课程池名称
   */
  poolName?: string
  /**
   * 排序序号
   */
  sequence: number
  /**
   * 课程池描述
   */
  poolDescription?: string
  /**
   * 展示名称
   */
  showName?: string
  /**
   * 包内课程
   */
  courseInPoolList?: Array<CourseInPoolUpdateRequest>
}

/**
 * <AUTHOR> create 2020/4/21 8:02
 */
export class BABAILIVideoTranscodeSettingRequest {
  /**
   * 是否进行转码
   */
  transcode: boolean
  /**
   * 转码清晰度，1代表普屏流畅|2代表普屏标清|3代表普屏高清|4代表宽屏流畅|5代表宽屏标清|6代表宽屏高清
   */
  clarityList?: Array<number>
}

/**
 * <AUTHOR> create 2020/4/21 8:01
 */
export class CourseWareCreateRequest {
  /**
   * 课件名称
   */
  name?: string
  /**
   * 课件时长，该属性只限文档型课件设置
   */
  timeLength: number
  /**
   * 教师id
   */
  teacherId?: string
  /**
   * 课件简介
   */
  abouts?: string
  /**
   * 课件分类ID
   */
  cwyId?: string
  /**
   * 课件解析资源存放的路径
   */
  coursewareResourcePath?: string
  /**
   * 视频转码配置
   */
  videoTranscodeSettings?: VideoTranscodeSettingsRequest
  /**
   * 若是大文件上传，需要异步合并时，必须填文件的MD5
   */
  resourceMD5?: string
  /**
   * 供应商ID
   */
  supplierId?: string
  /**
   * 是否可用
   */
  usable: boolean
  /**
   * 自定义拓展信息
   */
  expandData?: string
  /**
   * 创建类型，0表示自建,1表示内置,2表示共享，3表示迁移，4表示购买
   */
  createType: number
  /**
   * 外部链接资源
   */
  extensionResourceInfo?: ExtensionResourceInfo
}

export class ExtensionResourceInfo {
  videoInfoDtos?: Array<VideoInfoDto>
}

export class VideoInfoDto {
  /**
   * 视频播放路劲，相对路径
   */
  path?: string
  /**
   * 视频清晰度
1:流畅普屏 2:标清普屏 3:高清普屏 4:流畅宽屏 5:标清宽屏 6:高清宽屏
负数是代表手机端对应的清晰度
   */
  clarity: number
}

/**
 * <AUTHOR> create 2020/4/21 8:06
 */
export class CourseWareUpdateRequest {
  /**
   * 课件ID
   */
  id?: string
  /**
   * 课件名称
   */
  name?: string
  /**
   * 课件时长，该属性只限文档型课件设置
   */
  timeLength: number
  /**
   * 教师id
   */
  teacherId?: string
  /**
   * 课件简介
   */
  abouts?: string
  /**
   * 课件分类ID
   */
  cwyId?: string
  /**
   * 供应商ID
   */
  supplierId?: string
  /**
   * 是否可用
   */
  usable: boolean
  /**
   * 自定义拓展信息
   */
  expandData?: string
}

/**
 * <AUTHOR> create 2020/4/21 8:04
 */
export class HWYVideoTranscodeSettingRequest {
  /**
   * 是否进行转码
   */
  transcode: boolean
  /**
   * 华为云转码模板名称
   */
  templateName?: string
  /**
   * 是否抽取音频
   */
  extractAudio: boolean
}

/**
 * <AUTHOR> create 2020/4/21 8:03
 */
export class POLYVVideoTranscodeSettingRequest {
  /**
   * 是否进行转码
   */
  transcode: boolean
}

/**
 * <AUTHOR> create 2020/4/21 8:02
 */
export class VideoTranscodeSettingsRequest {
  /**
   * 八百里转码配置
   */
  babailiSetting?: BABAILIVideoTranscodeSettingRequest
  /**
   * 保利威视转码配置
   */
  polyvSetting?: POLYVVideoTranscodeSettingRequest
  /**
   * 华为云转码配置
   */
  hwySetting?: HWYVideoTranscodeSettingRequest
}

/**
 * <AUTHOR> create 2020/4/21 7:55
 */
export class CourseWareCategoryCreateRequest {
  /**
   * 分类名称
   */
  name?: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 备注
   */
  remarks?: string
  /**
   * 父分类id
   */
  parentId?: string
}

/**
 * <AUTHOR> create 2020/4/21 7:56
 */
export class CourseWareCategoryUpdateRequest {
  /**
   * 分类id
   */
  id?: string
  /**
   * 分类名称
   */
  name?: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 备注
   */
  remarks?: string
  /**
   * 父分类id
   */
  parentId?: string
}

/**
 * <AUTHOR> create 2020/4/20 17:01
 */
export class BlankFillingRequest {
  /**
   * 答案数量
   */
  answerCount: number
  /**
   * 当填空题类型为 精确匹配时，最外层集合为答案组（也就是每一组都是一道填空题的答案，满足当中的任意一组表示回答正确）第二层集合为空
当填空题类似为 每空多答案时，最外层的集合为每个空的答案，第二层集合为每个空的备选答案
   */
  answersGroup?: Array<string>
  /**
   * 答案项分值
当填空题类型为 精确匹配时此项值无效
   */
  answersItemScore?: Array<number>
  /**
   * 答案类型
@see BlankFillingAnswerType
   */
  answerType: number
  /**
   * 答案是否有顺序.当{@link #answerType } &#x3D; {@link BlankFillingAnswerType#MULTIPLE_PER_BLANK} 时，
即每空多答案的情况下，答案是否是按照填空顺序排列。
   */
  sequence: boolean
  /**
   * 评分标准
   */
  standard?: string
}

/**
 * <AUTHOR> create 2020/4/20 16:58
 */
export class ChoiceItemRequest {
  /**
   * 选项ID
   */
  id?: string
  /**
   * 选项内容
   */
  content?: string
}

/**
 * <AUTHOR> create 2020/4/20 17:06
 */
export class ComprehensiveChildQuestionRequest {
  /**
   * 子试题id
   */
  questionId?: string
  /**
   * 题目
   */
  title?: string
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 判断题
   */
  judgement?: JudgementRequest
  /**
   * 单选题
   */
  singleChoice?: SingleChoiceRequest
  /**
   * 多选
   */
  multipleChoice?: MultipleChoiceRequest
  /**
   * 填空
   */
  blankFilling?: BlankFillingRequest
  /**
   * 问答题
   */
  essay?: EssayRequest
  /**
   * 量表题
   */
  scale?: ScaleRequest
  /**
   * 试题难度
@see QuestionMode
   */
  mode: number
  /**
   * 难度值
   */
  difficultyValue: number
  /**
   * 试题解析
   */
  description?: string
}

/**
 * <AUTHOR> create 2020/4/20 17:05
 */
export class ComprehensiveRequest {
  /**
   * 子题
   */
  children?: Array<ComprehensiveChildQuestionRequest>
}

/**
 * <AUTHOR> create 2020/4/20 17:04
 */
export class EssayRequest {
  /**
   * 参考答案
   */
  referenceAnswer?: string
  /**
   * 评分标准
   */
  standard?: string
  /**
   * 是否限制作答长度
   */
  limitAnswerLength: boolean
  /**
   * 允许作答的文本字符最少长度
   */
  permitAnswerLengthMin: number
  /**
   * 允许作答的文本字符最大长度
   */
  permitAnswerLengthMax: number
}

/**
 * <AUTHOR> create 2020/4/20 16:57
 */
export class JudgementRequest {
  /**
   * 正确答案
   */
  correctAnswer: boolean
}

/**
 * <AUTHOR> create 2020/4/20 17:00
 */
export class MultipleChoiceRequest {
  /**
   * 选项
   */
  choiceItems?: Array<ChoiceItemRequest>
  /**
   * 正确答案
   */
  correctAnswers?: Array<string>
}

/**
 * <AUTHOR> create 2020/4/20 17:09
 */
export class PopQuestionCreateRequest {
  /**
   * 试题应用类型
@see QuestionApplyType
   */
  applyTypes?: Array<number>
  /**
   * 题库ID
   */
  libraryId?: string
  /**
   * 题目
   */
  title?: string
  /**
   * 判断题
   */
  judgement?: JudgementRequest
  /**
   * 单选题
   */
  singleChoice?: SingleChoiceRequest
  /**
   * 多选
   */
  multipleChoice?: MultipleChoiceRequest
  /**
   * 填空
   */
  blankFilling?: BlankFillingRequest
  /**
   * 问答题
   */
  essay?: ScaleRequest
  /**
   * 量表题
   */
  scale?: ScaleRequest
  /**
   * 综合题
   */
  comprehensive?: ComprehensiveRequest
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 试题难度
@see QuestionMode
   */
  mode: number
  /**
   * 难度值
   */
  difficulty: number
  /**
   * 试题解析
   */
  description?: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 弹窗时间：秒
   */
  timePoint: number
  /**
   * 课件id
   */
  courseWareId?: string
}

/**
 * <AUTHOR> create 2020/4/20 17:15
 */
export class PopQuestionUpdateRequest {
  /**
   * 试题id
   */
  id?: string
  /**
   * 试题应用类型
@see QuestionApplyType
   */
  applyTypes?: Array<number>
  /**
   * 题库ID
   */
  libraryId?: string
  /**
   * 题目
   */
  title?: string
  /**
   * 判断题
   */
  judgement?: JudgementRequest
  /**
   * 单选题
   */
  singleChoice?: SingleChoiceRequest
  /**
   * 多选
   */
  multipleChoice?: MultipleChoiceRequest
  /**
   * 填空
   */
  blankFilling?: BlankFillingRequest
  /**
   * 问答题
   */
  essay?: EssayRequest
  /**
   * 量表题
   */
  scale?: ScaleRequest
  /**
   * 综合题
   */
  comprehensive?: ComprehensiveRequest
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 试题难度
@see QuestionMode
   */
  mode: number
  /**
   * 难度值
   */
  difficulty: number
  /**
   * 试题解析
   */
  description?: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 弹窗题id、主键id
   */
  popQuestionId?: string
  /**
   * 弹窗时间：秒
   */
  timePoint: number
}

/**
 * <AUTHOR> create 2020/4/20 17:05
 */
export class ScaleRequest {
  /**
   * 量表类型
   */
  scaleType?: ScaleType
  /**
   * 程度_始，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写
   */
  startDegree?: string
  /**
   * 程度_止，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写
   */
  endDegree?: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
}

/**
 * <AUTHOR> create 2020/4/20 16:58
 */
export class SingleChoiceRequest {
  /**
   * 选项
   */
  choiceItems?: Array<ChoiceItemRequest>
  /**
   * 标准答案
   */
  correctAnswer?: string
}

/**
 * <AUTHOR> create 2020/4/21 8:12
 */
export class TeacherCreateRequest {
  /**
   * 教师名称
   */
  name?: string
  /**
   * 教师头像
   */
  photo?: string
  /**
   * 教师简介
   */
  abouts?: string
  /**
   * 教师详情
   */
  contents?: string
  /**
   * 性别，-1表示未知，0表示女，1表示男
   */
  gender: number
  /**
   * 职称
   */
  professionalTitle?: string
}

/**
 * <AUTHOR> create 2020/4/21 8:13
 */
export class TeacherUpdateRequest {
  /**
   * 教师ID
   */
  id?: string
  /**
   * 教师名称
   */
  name?: string
  /**
   * 教师头像
   */
  photo?: string
  /**
   * 教师简介
   */
  abouts?: string
  /**
   * 教师详情
   */
  contents?: string
  /**
   * 性别，-1表示未知，0表示女，1表示男
   */
  gender: number
  /**
   * 职称
   */
  professionalTitle?: string
}

export class ResAuthorizedQuery {
  authorizedState: number
  hasAuthorize?: boolean
  forbidAuthorize: boolean
  rangeType?: string
  belongsType?: string
  authorizeToUnitId?: string
  authorizedFromUnitId?: string
  objectId?: string
  useType?: string
  targetUnitId?: string
}

export class MarkerDTO {
  key: string
  value: string
}

export class OperatorDTO {
  userId: string
  name: string
  uniqueData: string
}

export class UnitDTO {
  id: string
  name: string
}

export class CoursePoolDTO {
  id: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  unit: UnitDTO
  organizationId: string
  poolName: string
  sequence: number
  markers: Array<MarkerDTO>
  createUsrId: string
  expireTime: string
  poolDescription: string
  showName: string
  creator: OperatorDTO
  createTime: string
  courseCount: number
  totalPeriod: number
}

/**
 * <AUTHOR> create 2020/4/21 7:26
 */
export class OperatorResponse {
  /**
   * 用户id
   */
  userId: string
  /**
   * 姓名
   */
  name: string
  /**
   * 唯一性值
   */
  uniqueData: string
}

/**
 * <AUTHOR> create 2020/4/21 7:31
 */
export class SupplierResponse {
  /**
   * 主键 课件提供商ID
   */
  id: string
  /**
   * 提供商名称
   */
  name: string
  /**
   * 创建人id
   */
  creator: string
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * <AUTHOR> create 2020/4/21 7:48
 */
export class UnitResponse {
  /**
   * 单位id
   */
  id: string
  /**
   * 单位名称
   */
  name: string
}

/**
 * <AUTHOR> create 2020/4/21 7:52
 */
export class CourseItemResponse {
  /**
   * 课程编号
   */
  id: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位ID
   */
  unitId: string
  /**
   * 所属单位
   */
  unit: UnitResponse
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 课程分类
   */
  categoryList: Array<CourseCategoryResponse>
  /**
   * 课程名称
   */
  name: string
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 权重,表示学时,学分等
   */
  period: number
  /**
   * 课程简介
   */
  abouts: string
  /**
   * 创建类型,0表示自建,1表示内置,2表示共享，3表示迁移，4表示授权
   */
  createType: number
  /**
   * 是否授权
   */
  hasAuthorize: boolean
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 是否被逻辑删除
   */
  isDelete: boolean
  /**
   * 课程状态，-1暂无课件，0解析中，1解析成功，2解析失败
   */
  status: number
  /**
   * 课程详情
   */
  contents: string
  /**
   * 教师信息
   */
  teacherInfo: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建者编号
   */
  createUsrId: string
  /**
   * 共享来源id
   */
  shareSourceId: string
  /**
   * 是否试听
   */
  customStatus: number
  /**
   * 计划授课讲数
   */
  plannedLecturesNum: number
}

/**
 * <AUTHOR> create 2020/4/21 7:32
 */
export class CourseOutLineResponse {
  /**
   * 目录id
   */
  id: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 挂载课件id
   */
  courseWareId: string
  /**
   * 课件
   */
  courseWare: CourseWareResponse
  /**
   * 目录名称
   */
  name: string
  /**
   * 上级目录ID
   */
  parentId: string
  /**
   * 排序
   */
  sort: number
  /**
   * 挂在课件是否支持试听 0不可以试听，1可以试听，
   */
  customeStatus: number
  /**
   * 外部链接资源
   */
  extensionResourceResponse: ExtensionResourceResponse1
}

export class ExtensionResourceResponse1 {
  videoInfoDtos: Array<VideoInfoDto1>
}

export class VideoInfoDto1 {
  /**
   * 视频播放路劲，相对路径
   */
  path: string
  /**
   * 视频清晰度
1:流畅普屏 2:标清普屏 3:高清普屏 4:流畅宽屏 5:标清宽屏 6:高清宽屏
负数是代表手机端对应的清晰度
   */
  clarity: number
}

/**
 * <AUTHOR> create 2020/4/21 7:32
 */
export class CourseOutLineWithSubOutlineResponse {
  /**
   * 子目录
   */
  subCourseOutlines: Array<CourseOutLineResponse>
  /**
   * 目录id
   */
  id: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 挂载课件id
   */
  courseWareId: string
  /**
   * 课件
   */
  courseWare: CourseWareResponse
  /**
   * 目录名称
   */
  name: string
  /**
   * 上级目录ID
   */
  parentId: string
  /**
   * 排序
   */
  sort: number
  /**
   * 挂在课件是否支持试听 0不可以试听，1可以试听，
   */
  customeStatus: number
  /**
   * 外部链接资源
   */
  extensionResourceResponse: ExtensionResourceResponse1
}

/**
 * <AUTHOR> create 2020/4/21 7:23
 */
export class CourseResponse {
  /**
   * 课程ID
   */
  id: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位ID
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 权重,表示学时,学分等
   */
  period: number
  /**
   * 供应商
   */
  supplier: SupplierResponse
  /**
   * 课程目录
   */
  courseOutline: Array<CourseOutLineWithSubOutlineResponse>
  /**
   * 课程分类
   */
  categoryList: Array<CourseCategoryResponse>
  /**
   * 教师信息
   */
  teachers: Array<TeacherResponse>
  /**
   * 课程简介
   */
  abouts: string
  /**
   * 创建类型,0表示自建,1表示内置,2表示共享，3表示迁移
   */
  createType: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 是否被逻辑删除
   */
  delete: boolean
  /**
   * 课程的课件状态，0表示解析中，1表示解析成功，2表示解析失败
   */
  status: number
  /**
   * 完成时间
   */
  completedTime: string
  /**
   * 课程详情
   */
  contents: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建者编号
   */
  createUsrId: string
  /**
   * 创建人
   */
  creator: OperatorResponse
  /**
   * 计划授课讲数
   */
  plannedLecturesNum: number
}

/**
 * <AUTHOR> create 2020/4/21 7:28
 */
export class CourseCategoryListResponse {
  /**
   * 分类编号
   */
  id: string
  /**
   * 课件分类名称
   */
  name: string
  /**
   * 备注
   */
  remarks: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 父分类ID
   */
  parentId: string
  /**
   * 课程分类归属类型，{@link CourseCategoryBelongsType}
   */
  belongsType: number
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * <AUTHOR> create 2020/4/21 7:25
 */
export class CourseCategoryResponse {
  /**
   * 分类编号
   */
  id: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属服务单位ID
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 课件分类名称
   */
  name: string
  /**
   * 备注
   */
  remarks: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 父分类ID
   */
  parentId: string
  /**
   * 创建者ID
   */
  createUsrId: string
  /**
   * 创建人
   */
  creator: OperatorResponse
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 课程分类归属类型，{@link CourseCategoryBelongsType}
   */
  belongsType: number
}

/**
 * <AUTHOR> create 2020/4/21 7:50
 */
export class CourseInPoolItemResponse {
  /**
   * 课程池与课程关系编号
   */
  id: string
  /**
   * 课程编号
   */
  courseId: string
  /**
   * 课程名称
   */
  courseName: string
  /**
   * 课程序号
   */
  sequence: number
  /**
   * 课程标量值|在课程池规则中，课程在课程池中权重值
   */
  quantitative: number
  /**
   * 课程学时|课程在课程池中的学时
   */
  period: number
  /**
   * 课程过期时间|课程在课程池中到期时间，null表示该课程在课程池中无期限限制
   */
  courseExpireTime: string
  /**
   * 特征标记列表
   */
  markers: Array<MarkerDTO>
  /**
   * 创建人编号
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 课程学时|课程中的学时
   */
  coursePeriod: number
  /**
   * 课程分类编号
   */
  categoryIdList: Array<string>
  /**
   * 课程分类集合
   */
  categoryList: Array<CourseCategoryResponse>
  /**
   * 所属课程池编号
   */
  ccpId: string
  /**
   * 创建类型,0表示自建,1表示内置,2表示共享，3表示迁移，4表示授权
   */
  createType: number
}

/**
 * <AUTHOR> create 2020/4/21 7:49
 */
export class CoursePoolItemResponse {
  /**
   * 课程池编号
   */
  id: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属服务单位ID
   */
  unitId: string
  /**
   * 单位信息
   */
  unit: UnitResponse
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 课程池名称
   */
  poolName: string
  /**
   * 展示名称
   */
  showName: string
  /**
   * 课程池状态|0/1/2，正常/无效/过期
   */
  poolState: number
  /**
   * 排序序号
   */
  sequence: number
  /**
   * 创建人编号
   */
  createUserId: string
  /**
   * 创建人
   */
  creator: OperatorResponse
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime: string
  /**
   * 过期时间,null表示不设置过期
   */
  expireTime: string
  /**
   * 课程池描述
   */
  poolDescription: string
  /**
   * 课程池内课程数量
   */
  courseCount: number
  /**
   * 课程池内课程权重值总和
   */
  totalQuantitative: number
  /**
   * 课程池内课程学时或学分总和
   */
  totalPeriod: number
}

/**
 * <AUTHOR> create 2020/4/21 7:47
 */
export class CoursePoolResponse {
  /**
   * 课程包id
   */
  id: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 必填，所属平台版本ID
   */
  platformVersionId: string
  /**
   * 必填，所属项目ID
   */
  projectId: string
  /**
   * 必填，所属子项目ID
   */
  subProjectId: string
  /**
   * 必填，所属服务单位ID
   */
  unitId: string
  /**
   * 单位信息
   */
  unit: UnitResponse
  /**
   * 必填，所属组织机构ID
   */
  organizationId: string
  /**
   * 必填，课程池名称
   */
  poolName: string
  /**
   * 排序序号，默认1
   */
  sequence: number
  /**
   * 必填，创建人编号
   */
  createUsrId: string
  /**
   * 过期时间,null表示不设置过期
   */
  expireTime: string
  /**
   * 课程池描述
   */
  poolDescription: string
  /**
   * 展示名称
   */
  showName: string
  /**
   * 创建人
   */
  creator: OperatorResponse
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 课程池内课程数量
   */
  courseCount: number
  /**
   * 课程池内课程学时或学分总和
   */
  totalPeriod: number
}

/**
 * <AUTHOR> create 2020/4/21 8:08
 */
export class CourseWareItemResponse {
  /**
   * 课件ID
   */
  id: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位ID
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 课件名称
   */
  name: string
  /**
   * 课件类型，1表示文档，2表示视频，3表示多媒体
   */
  type: number
  /**
   * 课件原始类型, 1表示文档，2表示单视频，3表示串流大师，4表示汉博尔，5表示会计靠前，6表示Power+，7表示网视宝，8表示新华网，9表示地税网络学院，10表示中经网
   */
  originalType: number
  /**
   * 媒体时长，单位秒
   */
  timeLength: number
  /**
   * 教师名称
   */
  teacherName: string
  /**
   * 教师简介
   */
  teacherAbouts: string
  /**
   * 课件分类ID
   */
  cwyId: string
  /**
   * 课件分类
   */
  category: CourseWareCategoryResponse
  /**
   * 供应商ID
   */
  supplierId: string
  /**
   * 供应商
   */
  supplier: SupplierResponse
  /**
   * 课件状态 0解析中，1解析成功，2解析失败
   */
  status: number
  /**
   * 是否可用
   */
  usable: boolean
  /**
   * 创建者ID
   */
  createUsrId: string
  /**
   * 创建人
   */
  creator: OperatorResponse
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 自定义拓展信息
   */
  expandData: string
  /**
   * 创建类型，0表示自建,1表示内置,2表示共享，3表示迁移，4表示购买
   */
  createType: number
  /**
   * 创建类型相应时间
   */
  createTypeTime: string
  /**
   * 是否被引用
   */
  hasReference: boolean
  /**
   * 弹窗题数量
   */
  popCount: number
}

/**
 * <AUTHOR> create 2020/4/21 7:58
 */
export class CourseWareResponse {
  /**
   * 课件ID
   */
  id: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位ID
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 课件名称
   */
  name: string
  /**
   * 课件类型，1表示文档，2表示视频，3表示多媒体
   */
  type: number
  /**
   * 课件原始类型, 1表示文档，2表示单视频，3表示串流大师，4表示汉博尔，5表示会计靠前，6表示Power+，7表示网视宝，8表示新华网，9表示地税网络学院，10表示中经网
   */
  originalType: number
  /**
   * 课件时长，该属性只限文档型课件设置
   */
  timeLength: number
  /**
   * 教师id
   */
  teacherId: string
  /**
   * 教师
   */
  teacher: TeacherResponse
  /**
   * 课件简介
   */
  abouts: string
  /**
   * 课件分类ID
   */
  cwyId: string
  /**
   * 课件分类
   */
  category: CourseWareCategoryResponse
  /**
   * 课件解析资源存放的路径
   */
  coursewareResourcePath: string
  /**
   * 供应商ID
   */
  supplierId: string
  /**
   * 供应商
   */
  supplier: SupplierResponse
  /**
   * 是否可用
   */
  usable: boolean
  /**
   * 课件状态 0解析中，1解析成功，2解析失败
   */
  status: number
  /**
   * 创建者ID
   */
  createUsrId: string
  /**
   * 创建人
   */
  creator: OperatorResponse
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 自定义拓展信息
   */
  expandData: string
  /**
   * 创建类型，0表示自建,1表示内置,2表示共享，3表示迁移，4表示购买
   */
  createType: number
  /**
   * 创建类型相应时间
   */
  createTypeTime: string
  /**
   * 外部链接资源
   */
  extensionResourceResponse: ExtensionResourceResponse
}

export class ExtensionResourceResponse {
  videoInfoDtos: Array<VideoInfoDto1>
}

/**
 * <AUTHOR> create 2020/4/21 7:57
 */
export class CourseWareCategoryListResponse {
  /**
   * 分类编号
   */
  id: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属服务单位ID
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 备注
   */
  remarks: string
  /**
   * 父分类id
   */
  parentId: string
  /**
   * 创建者ID
   */
  createUsrId: string
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * <AUTHOR> create 2020/4/21 7:54
 */
export class CourseWareCategoryResponse {
  /**
   * 分类编号
   */
  id: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属服务单位ID
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 备注
   */
  remarks: string
  /**
   * 父分类id
   */
  parentId: string
  /**
   * 创建者ID
   */
  createUsrId: string
  /**
   * 创建人
   */
  creator: OperatorResponse
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * <AUTHOR> create 2020/4/20 17:01
 */
export class BlankFillingResponse {
  /**
   * 答案数量
   */
  answerCount: number
  /**
   * 当填空题类型为 精确匹配时，最外层集合为答案组（也就是每一组都是一道填空题的答案，满足当中的任意一组表示回答正确）第二层集合为空
当填空题类似为 每空多答案时，最外层的集合为每个空的答案，第二层集合为每个空的备选答案
   */
  answersGroup: Array<string>
  /**
   * 答案项分值
当填空题类型为 精确匹配时此项值无效
   */
  answersItemScore: Array<number>
  /**
   * 答案类型
@see BlankFillingAnswerType
   */
  answerType: number
  /**
   * 答案是否有顺序.当{@link #answerType } &#x3D; {@link BlankFillingAnswerType#MULTIPLE_PER_BLANK} 时，
即每空多答案的情况下，答案是否是按照填空顺序排列。
   */
  sequence: boolean
  /**
   * 评分标准
   */
  standard: string
}

/**
 * <AUTHOR> create 2020/4/20 16:58
 */
export class ChoiceItemResponse {
  /**
   * 选项ID
   */
  id: string
  /**
   * 选项内容
   */
  content: string
}

/**
 * <AUTHOR> create 2020/4/20 17:06
 */
export class ComprehensiveChildQuestionResponse {
  /**
   * 子试题id
   */
  questionId: string
  /**
   * 题目
   */
  title: string
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 判断题
   */
  judgement: JudgementResponse
  /**
   * 单选题
   */
  singleChoice: SingleChoiceResponse
  /**
   * 多选
   */
  multipleChoice: MultipleChoiceResponse
  /**
   * 填空
   */
  blankFilling: BlankFillingResponse
  /**
   * 问答题
   */
  essay: EssayResponse
  /**
   * 量表题
   */
  scale: ScaleResponse
  /**
   * 试题难度
@see QuestionMode
   */
  mode: number
  /**
   * 难度值
   */
  difficultyValue: number
  /**
   * 试题解析
   */
  description: string
}

/**
 * <AUTHOR> create 2020/4/20 17:05
 */
export class ComprehensiveResponse {
  /**
   * 子题
   */
  children: Array<ComprehensiveChildQuestionResponse>
}

/**
 * <AUTHOR> create 2020/4/20 17:04
 */
export class EssayResponse {
  /**
   * 参考答案
   */
  referenceAnswer: string
  /**
   * 评分标准
   */
  standard: string
  /**
   * 是否限制作答长度
   */
  limitAnswerLength: boolean
  /**
   * 允许作答的文本字符最少长度
   */
  permitAnswerLengthMin: number
  /**
   * 允许作答的文本字符最大长度
   */
  permitAnswerLengthMax: number
}

/**
 * <AUTHOR> create 2020/4/20 16:57
 */
export class JudgementResponse {
  /**
   * 正确答案
   */
  correctAnswer: boolean
}

/**
 * <AUTHOR> create 2020/4/20 17:00
 */
export class MultipleChoiceResponse {
  /**
   * 选项
   */
  choiceItems: Array<ChoiceItemResponse>
  /**
   * 正确答案
   */
  correctAnswers: Array<string>
}

/**
 * <AUTHOR> create 2020/4/20 16:56
 */
export class PopQuestionResponse {
  /**
   * 试题id
   */
  id: string
  /**
   * 试题应用类型
@see QuestionApplyType
   */
  applyTypes: Array<number>
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位ID
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 题库ID
   */
  libraryId: string
  /**
   * 题目
   */
  title: string
  /**
   * 判断题
   */
  judgement: JudgementResponse
  /**
   * 单选题
   */
  singleChoice: SingleChoiceResponse
  /**
   * 多选
   */
  multipleChoice: MultipleChoiceResponse
  /**
   * 填空
   */
  blankFilling: BlankFillingResponse
  /**
   * 问答题
   */
  essay: EssayResponse
  /**
   * 量表题
   */
  scale: ScaleResponse
  /**
   * 综合题
   */
  comprehensive: ComprehensiveResponse
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 试题难度
@see QuestionMode
   */
  mode: number
  /**
   * 难度值
   */
  difficulty: number
  /**
   * 试题解析
   */
  description: string
  /**
   * 创建人id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 最后修改时间
   */
  lastChangeTime: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 资源记录(数据)的授权源id
a 授权 b, b 授权 c, c的sourceId是b, c的rootId是a
   */
  rootId: string
  /**
   * 数据授权的Token, 并不需要默认值
   */
  token: string
  /**
   * 弹窗题id、主键id
   */
  popQuestionId: string
  /**
   * 弹窗时间：秒
   */
  timePoint: number
  /**
   * 课件id
   */
  courseWareId: string
}

/**
 * <AUTHOR> create 2020/4/20 17:05
 */
export class ScaleResponse {
  /**
   * 量表类型
   */
  scaleType: ScaleType
  /**
   * 程度_始，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写
   */
  startDegree: string
  /**
   * 程度_止，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写
   */
  endDegree: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
}

/**
 * <AUTHOR> create 2020/4/20 16:58
 */
export class SingleChoiceResponse {
  /**
   * 选项
   */
  choiceItems: Array<ChoiceItemResponse>
  /**
   * 标准答案
   */
  correctAnswer: string
}

/**
 * <AUTHOR> create 2020/4/21 8:09
 */
export class TeacherItemResponse {
  /**
   * 教师ID
   */
  id: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位ID
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 教师名称
   */
  name: string
  /**
   * 教师头像
   */
  photo: string
  /**
   * 教师简介
   */
  abouts: string
  /**
   * 性别，-1表示未知，0表示女，1表示男
   */
  gender: number
  /**
   * 职称
   */
  professionalTitle: string
  /**
   * 创建者ID
   */
  createUsrId: string
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * <AUTHOR> create 2020/4/21 7:59
 */
export class TeacherResponse {
  /**
   * 教师ID
   */
  id: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位ID
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 教师名称
   */
  name: string
  /**
   * 教师头像
   */
  photo: string
  /**
   * 教师简介
   */
  abouts: string
  /**
   * 教师详情
   */
  contents: string
  /**
   * 性别，-1表示未知，0表示女，1表示男
   */
  gender: number
  /**
   * 职称
   */
  professionalTitle: string
  /**
   * 创建者ID
   */
  createUsrId: string
  /**
   * 创建人
   */
  creator: OperatorResponse
  /**
   * 创建时间
   */
  createTime: string
}

export class CourseInPoolItemResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseInPoolItemResponse>
}

export class CourseItemResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseItemResponse>
}

export class CoursePoolItemResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursePoolItemResponse>
}

export class CourseWareItemResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseWareItemResponse>
}

export class TeacherItemResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TeacherItemResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 课程分类名称是否存在
   * @param name
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async courseCategoryNameExists(
    params: { parentId?: string; name?: string; id?: string },
    query: DocumentNode = GraphqlImporter.courseCategoryNameExists,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 课程名称是否存在
   * @param name
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async courseNameExists(
    params: { name?: string; id?: string },
    query: DocumentNode = GraphqlImporter.courseNameExists,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 课程包名称是否存在
   * @param name
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async coursePoolNameExists(
    params: { name?: string; id?: string },
    query: DocumentNode = GraphqlImporter.coursePoolNameExists,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 课件分类名称是否存在
   * @param parentId
   * @param name
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async courseWareCategoryNameExists(
    params: { parentId?: string; name?: string; id?: string },
    query: DocumentNode = GraphqlImporter.courseWareCategoryNameExists,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 课件名称是否存在
   * @param name
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async courseWareNameExists(
    params: { name?: string; id?: string },
    query: DocumentNode = GraphqlImporter.courseWareNameExists,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课程信息
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourse(
    id: string,
    query: DocumentNode = GraphqlImporter.getCourse,
    operation?: string
  ): Promise<Response<CourseResponse>> {
    return commonRequestApi<CourseResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课程分类
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseCategory(
    id: string,
    query: DocumentNode = GraphqlImporter.getCourseCategory,
    operation?: string
  ): Promise<Response<CourseCategoryResponse>> {
    return commonRequestApi<CourseCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课程池内课程列表
   * @param courseInPoolQueryDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param courseInPoolQueryDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseInPoolList(
    courseInPoolQueryDTO: CourseInPoolQueryDTO,
    query: DocumentNode = GraphqlImporter.getCourseInPoolList,
    operation?: string
  ): Promise<Response<Array<CourseInPoolItemResponse>>> {
    return commonRequestApi<Array<CourseInPoolItemResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { courseInPoolQueryDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课程池内课程分页
   * @param page
   * @param courseInPoolQueryDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseInPoolPage(
    params: { page?: Page; courseInPoolQueryDTO?: CourseInPoolQueryDTO },
    query: DocumentNode = GraphqlImporter.getCourseInPoolPage,
    operation?: string
  ): Promise<Response<CourseInPoolItemResponsePage>> {
    return commonRequestApi<CourseInPoolItemResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课程分页
   * @param page
   * @param query
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCoursePage(
    params: { page?: Page; query?: CourseQueryDTO },
    query: DocumentNode = GraphqlImporter.getCoursePage,
    operation?: string
  ): Promise<Response<CourseItemResponsePage>> {
    return commonRequestApi<CourseItemResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询课程池dto
   * @param poolId
   * @return
   * @param query 查询 graphql 语法文档
   * @param poolId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCoursePoolDTO(
    poolId: string,
    query: DocumentNode = GraphqlImporter.getCoursePoolDTO,
    operation?: string
  ): Promise<Response<CoursePoolResponse>> {
    return commonRequestApi<CoursePoolResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { poolId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询课程池
   * @param page
   * @param query
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCoursePoolPage(
    params: { page?: Page; query?: CoursePoolQueryDTO; authorizedQuery?: ResAuthorizedQuery },
    query: DocumentNode = GraphqlImporter.getCoursePoolPage,
    operation?: string
  ): Promise<Response<CoursePoolItemResponsePage>> {
    return commonRequestApi<CoursePoolItemResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课件
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseWare(
    id: string,
    query: DocumentNode = GraphqlImporter.getCourseWare,
    operation?: string
  ): Promise<Response<CourseWareResponse>> {
    return commonRequestApi<CourseWareResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课件分类详情
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseWareCategory(
    id: string,
    query: DocumentNode = GraphqlImporter.getCourseWareCategory,
    operation?: string
  ): Promise<Response<CourseWareCategoryResponse>> {
    return commonRequestApi<CourseWareCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课件分类列表
   * @param parentId
   * @param unitId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseWareCategoryListByParentId(
    params: { parentId?: string; unitId?: string },
    query: DocumentNode = GraphqlImporter.getCourseWareCategoryListByParentId,
    operation?: string
  ): Promise<Response<Array<CourseWareCategoryListResponse>>> {
    return commonRequestApi<Array<CourseWareCategoryListResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseWarePage(
    params: { page?: Page; query?: CourseWareQueryDTO; authorizedQuery?: ResAuthorizedQuery },
    query: DocumentNode = GraphqlImporter.getCourseWarePage,
    operation?: string
  ): Promise<Response<CourseWareItemResponsePage>> {
    return commonRequestApi<CourseWareItemResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课件提供商
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseWareSupplier(
    query: DocumentNode = GraphqlImporter.getCourseWareSupplier,
    operation?: string
  ): Promise<Response<Array<SupplierResponse>>> {
    return commonRequestApi<Array<SupplierResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取弹窗题
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPopQuestion(
    id: string,
    query: DocumentNode = GraphqlImporter.getPopQuestion,
    operation?: string
  ): Promise<Response<PopQuestionResponse>> {
    return commonRequestApi<PopQuestionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取子课程分类
   * @param parentId
   * @param unitId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSubCourseCategory(
    params: { parentId?: string; unitId?: string },
    query: DocumentNode = GraphqlImporter.getSubCourseCategory,
    operation?: string
  ): Promise<Response<Array<CourseCategoryListResponse>>> {
    return commonRequestApi<Array<CourseCategoryListResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取教师
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTeacher(
    id: string,
    query: DocumentNode = GraphqlImporter.getTeacher,
    operation?: string
  ): Promise<Response<TeacherResponse>> {
    return commonRequestApi<TeacherResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询教师
   * @param page
   * @param query
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTeacherPage(
    params: { page?: Page; query?: TeacherQueryDTO },
    query: DocumentNode = GraphqlImporter.getTeacherPage,
    operation?: string
  ): Promise<Response<TeacherItemResponsePage>> {
    return commonRequestApi<TeacherItemResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课件下所有弹窗题
   * @param courseWareId
   * @return
   * @param query 查询 graphql 语法文档
   * @param courseWareId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listAllCourseWarePopQuestions(
    courseWareId: string,
    query: DocumentNode = GraphqlImporter.listAllCourseWarePopQuestions,
    operation?: string
  ): Promise<Response<Array<PopQuestionResponse>>> {
    return commonRequestApi<Array<PopQuestionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { courseWareId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 是否被当作必修包使用
   * @param query 查询 graphql 语法文档
   * @param poolId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async usedAsCompulsoryPackage(
    poolId: string,
    query: DocumentNode = GraphqlImporter.usedAsCompulsoryPackage,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { poolId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 添加课程进课程包
   * @param poolId
   * @param creates
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addCourseIntoPool(
    params: { poolId?: string; creates?: Array<CourseInPoolCreateRequest> },
    mutate: DocumentNode = GraphqlImporter.addCourseIntoPool,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 新建课程目录
   * @param creates
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addCourseOutline(
    params: { courseId?: string; creates?: Array<CourseOutLineCreateRequest> },
    mutate: DocumentNode = GraphqlImporter.addCourseOutline,
    operation?: string
  ): Promise<Response<Array<CourseOutLineResponse>>> {
    return commonRequestApi<Array<CourseOutLineResponse>>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 复制课程包
   * @param id
   * @param newName
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async copyCoursePool(
    params: { id?: string; newName?: string },
    mutate: DocumentNode = GraphqlImporter.copyCoursePool,
    operation?: string
  ): Promise<Response<CoursePoolDTO>> {
    return commonRequestApi<CoursePoolDTO>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建课程
   * @param create
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param create 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCourse(
    create: CourseCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createCourse,
    operation?: string
  ): Promise<Response<CourseResponse>> {
    return commonRequestApi<CourseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { create },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建课程分类
   * @param create
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param create 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCourseCategory(
    create: CourseCategoryCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createCourseCategory,
    operation?: string
  ): Promise<Response<CourseCategoryResponse>> {
    return commonRequestApi<CourseCategoryResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { create },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 新建课程包
   * @param create
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param create 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCoursePool(
    create: CoursePoolCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createCoursePool,
    operation?: string
  ): Promise<Response<CoursePoolResponse>> {
    return commonRequestApi<CoursePoolResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { create },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建课件
   * @param create
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param create 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCourseWare(
    create: CourseWareCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createCourseWare,
    operation?: string
  ): Promise<Response<CourseWareResponse>> {
    return commonRequestApi<CourseWareResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { create },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建课件分类
   * @param create
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param create 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCourseWareCategory(
    create: CourseWareCategoryCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createCourseWareCategory,
    operation?: string
  ): Promise<Response<CourseWareCategoryResponse>> {
    return commonRequestApi<CourseWareCategoryResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { create },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建弹窗题
   * @param create
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param create 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createPopQuestion(
    create: PopQuestionCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createPopQuestion,
    operation?: string
  ): Promise<Response<PopQuestionResponse>> {
    return commonRequestApi<PopQuestionResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { create },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建教师
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param create 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createTeacher(
    create: TeacherCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createTeacher,
    operation?: string
  ): Promise<Response<TeacherResponse>> {
    return commonRequestApi<TeacherResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { create },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除课程
   * @param id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteCourse(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deleteCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除课程分类
   * @param id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteCourseCategory(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deleteCourseCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除课程包内课程
   * @param poolId
   * @param deleteIds
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteCourseIntoPool(
    params: { poolId?: string; deleteIds?: Array<string> },
    mutate: DocumentNode = GraphqlImporter.deleteCourseIntoPool,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除课程目录
   * @param outlineIds
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteCourseOutline(
    params: { courseId?: string; outlineIds?: Array<string> },
    mutate: DocumentNode = GraphqlImporter.deleteCourseOutline,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除课程包
   * @param poolId
   * @param mutate 查询 graphql 语法文档
   * @param poolId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteCoursePool(
    poolId: string,
    mutate: DocumentNode = GraphqlImporter.deleteCoursePool,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { poolId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除课件
   * @param id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteCourseWare(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deleteCourseWare,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除课件分类
   * @param id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteCourseWareCategory(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deleteCourseWareCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除教师
   * @param id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteTeacher(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deleteTeacher,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 停用课程
   * @param courseId
   * @param mutate 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableCourse(
    courseId: string,
    mutate: DocumentNode = GraphqlImporter.disableCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 停用课件
   * @param id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableCourseWare(
    id: string,
    mutate: DocumentNode = GraphqlImporter.disableCourseWare,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 启用课程
   * @param courseId
   * @param mutate 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableCourse(
    courseId: string,
    mutate: DocumentNode = GraphqlImporter.enableCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 启用课件
   * @param id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableCourseWare(
    id: string,
    mutate: DocumentNode = GraphqlImporter.enableCourseWare,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 交换课程目录
   * @param courseId
   * @param firstOutlineId
   * @param secondOutlineId
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exchangeCourseOutlineSort(
    params: { courseId?: string; firstOutlineId?: string; secondOutlineId?: string },
    mutate: DocumentNode = GraphqlImporter.exchangeCourseOutlineSort,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 移动课程包内课程
   * @param poolId
   * @param courseId
   * @param direction
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async moveCourseInPool(
    params: { poolId?: string; courseId?: string; direction?: number },
    mutate: DocumentNode = GraphqlImporter.moveCourseInPool,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 移除弹窗题
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removePopQuestion(
    id: string,
    mutate: DocumentNode = GraphqlImporter.removePopQuestion,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 修改课程
   * @param update
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param update 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCourse(
    update: CourseUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateCourse,
    operation?: string
  ): Promise<Response<CourseResponse>> {
    return commonRequestApi<CourseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { update },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新课程分类
   * @param update
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param update 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCourseCategory(
    update: CourseCategoryUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateCourseCategory,
    operation?: string
  ): Promise<Response<CourseCategoryResponse>> {
    return commonRequestApi<CourseCategoryResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { update },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 修改课程包内课程
   * @param poolId
   * @param updates
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCourseIntoPool(
    params: { poolId?: string; updates?: Array<CourseInPoolUpdateRequest> },
    mutate: DocumentNode = GraphqlImporter.updateCourseIntoPool,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 修改课程目录
   * @param updates
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCourseOutline(
    params: { courseId?: string; updates?: Array<CourseUpdateRequest> },
    mutate: DocumentNode = GraphqlImporter.updateCourseOutline,
    operation?: string
  ): Promise<Response<Array<CourseOutLineResponse>>> {
    return commonRequestApi<Array<CourseOutLineResponse>>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 修改课程包
   * @param update
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param update 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCoursePool(
    update: CoursePoolUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateCoursePool,
    operation?: string
  ): Promise<Response<CoursePoolResponse>> {
    return commonRequestApi<CoursePoolResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { update },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 修改课件信息
   * @param update
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param update 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCourseWare(
    update: CourseWareUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateCourseWare,
    operation?: string
  ): Promise<Response<CourseWareResponse>> {
    return commonRequestApi<CourseWareResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { update },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 修改课件分类
   * @param update
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param update 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCourseWareCategory(
    update: CourseWareCategoryUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateCourseWareCategory,
    operation?: string
  ): Promise<Response<CourseWareCategoryResponse>> {
    return commonRequestApi<CourseWareCategoryResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { update },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 修改弹窗题
   * @param update
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param update 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updatePopQuestion(
    update: PopQuestionUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updatePopQuestion,
    operation?: string
  ): Promise<Response<PopQuestionResponse>> {
    return commonRequestApi<PopQuestionResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { update },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 修改教师信息
   * @param update
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param update 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTeacher(
    update: TeacherUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateTeacher,
    operation?: string
  ): Promise<Response<TeacherResponse>> {
    return commonRequestApi<TeacherResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { update },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
