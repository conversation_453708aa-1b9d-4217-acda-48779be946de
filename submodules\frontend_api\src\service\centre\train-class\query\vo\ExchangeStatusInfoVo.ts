import { ExchangeOrderStatusEnum } from '@api/service/centre/train-class/enum/ExchangeOrderStatus'

/**
 * @description 换班状态信息
 */
class ExchangeStatusInfoVo {
  /**
   * 换班状态 1：申请发货 2：退货处理中 3：退货失败 4；申请发货 5：发货处理中 6：换货完成 7：申请关闭
   */
  status: ExchangeOrderStatusEnum = null

  /**
   * 时间
   */
  date = ''

  /**
   * 备注信息（原因）
   */
  remark? = ''

  constructor(status?: ExchangeOrderStatusEnum) {
    this.status = status
  }
}

export default ExchangeStatusInfoVo
