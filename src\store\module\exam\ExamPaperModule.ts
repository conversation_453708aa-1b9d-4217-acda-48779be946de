import PreExamGateway, {
  PreExamMockExaminationPaperCreateRequest,
  PreExamMockExaminationPaperItemResponse,
  PreExamMockExaminationPaperParamRequest,
  PreExamMockExaminationPaperResponse,
  PreExamMockExaminationPaperUpdateDTO
} from '@api/gateway/PreExam-default'
import PlatformUserGateway, { UserParamDTO, UserSimpleInfoDTO } from '@api/gateway/PlatformUser'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import ExamPaperItem from '@/store/module/exam/mode/exam-paper/ExamPaperItem'
import { ExamPaper } from '@/store/module/exam/mode/exam-paper/ExamPaper'
import AnswerExamPaper from '@/store/module/exam/mode/answer/AnswerExamPaper'
import { Page, ResponseStatus } from '@hbfe/common'
import PlatformExam, { ExamPaperClassifyDTO } from '@api/gateway/PlatformExam'

/**
 * 试卷全局的state
 */
export interface IExamPaperState {
  /**
   * 总页数
   */
  totalSize: number
  /**
   * 分页总数
   */
  totalPageSize: number
  /**
   * 分页数据模型
   */
  examPaperList: Array<ExamPaperItem>
  /**
   * 试卷详情
   */
  examPaper: ExamPaper
  /**
   * 试卷预览对象
   */
  answerExamPaper: AnswerExamPaper
}

@Module({ namespaced: true, store, dynamic: true, name: 'StoreExamPaperModule' })
class ExamPaperModule extends VuexModule implements IExamPaperState {
  /**
   * 总页数
   */
  totalPageSize = 0
  /**
   * 分页总数
   */
  totalSize = 0
  /**
   * 分页数据模型
   */
  examPaperList: Array<ExamPaperItem> = new Array<ExamPaperItem>()
  /**
   * 试卷详情
   */
  examPaper: ExamPaper = new ExamPaper()
  /**
   * 试卷预览对象
   */
  answerExamPaper: AnswerExamPaper = new AnswerExamPaper()

  urlPath = {
    /**
     * 试卷预览的地址
     */
    previewPath: '/gateway/examAnswerPaper/preViewByToken'
  }

  /**
   * 获取试卷分页
   */
  @Action
  async pageExamPaper(param: { page: Page; paramDto: PreExamMockExaminationPaperParamRequest }) {
    const response = await PreExamGateway.findExamPaperPage({ page: param.page, param: param.paramDto })
    if (response.status.isSuccess()) {
      // 获取试卷分页
      const data = response.data
      const currentPageData = data.currentPageData
      this.SET_EXAM_PAPER_LIST(currentPageData)
      // 获取试卷所属分类
      const classifyRes = await PlatformExam.listExamPaperClassify(currentPageData.map(p => p.paperTypeId))
      if (classifyRes.status.isSuccess()) {
        this.APPEND_CLASSIFY(classifyRes.data)
      }
      // 获取试卷创建人
      const param = new UserParamDTO()
      param.userIdList = currentPageData.map(p => p.createUserId)
      const userRes = await PlatformUserGateway.listUserInfo(param)
      if (userRes.status.isSuccess()) {
        this.APPEND_CREATE_USER(userRes.data)
      }
    }
    return response.status
  }

  /**
   * 请求网关层获取试卷详情
   * @param paperId
   */
  @Action
  async getExamPaper(paperId: string) {
    const response = await PreExamGateway.getExamPaper(paperId)
    if (response.status.isSuccess()) {
      const paperData = response.data
      this.SET_EXAM_PAPER(paperData)
      const paperTypeRes = await PlatformExam.listExamPaperClassify(Array.of(paperData.paperTypeId))
      if (paperTypeRes.status.isSuccess()) {
        this.APPEND_CLASSIFY_FOR_DETAIL(paperTypeRes.data[0])
      }
    }
    return response.status
  }

  /**
   * 创建试卷
   * @param item
   */
  @Action
  async createExamPaper(item: PreExamMockExaminationPaperCreateRequest) {
    const response = await PreExamGateway.createExamPaper(item)
    return response.status
  }

  /**
   * 更新试卷1
   * @param item
   */
  @Action
  async update(item: PreExamMockExaminationPaperUpdateDTO) {
    const response = await PreExamGateway.updateExamPaper(item)
    return response.status
  }

  /**
   * 试卷预览
   * @param payLoad
   */
  @Action
  async previewExamPaperToken(payLoad: { paperId: string }) {
    if (!payLoad.paperId) {
      console.log('试卷id和专业id不能为空')
      return new ResponseStatus(500, '')
    }

    const tokenRes = await PreExamGateway.getPreviewTokenByPaperAndProfession({
      paperId: payLoad.paperId
    })
    if (!tokenRes.status.isSuccess()) {
      console.log('>> 申请试卷预览token失败', tokenRes.status.message)
      return new ResponseStatus(500, '')
    }
    const url = '/exam/mockPreview/' + tokenRes.data
    window.open(url, '_blank')
    return new ResponseStatus(200, tokenRes.data)
  }

  /**
   * 复制试卷
   * @param param
   */
  @Action
  async copy(param: { id: string; newName: string }) {
    const res = await PreExamGateway.copyExamPaper(param)
    return res.status
  }

  /**
   * 启用试卷
   * @param id
   */
  @Action
  async enable(id: string) {
    const res = await PreExamGateway.enableExamPaper(id)
    return res.status
  }

  /**
   * 停用试卷
   * @param id
   */
  @Action
  async disable(id: string) {
    const res = await PreExamGateway.disableExamPaper(id)
    return res.status
  }

  /**
   * 设置状态对象中的试卷总数
   * @param totalSize
   * @constructor
   */
  @Mutation
  private SET_TOTAL_SIZE(totalSize: number) {
    this.totalSize = totalSize
  }

  /**
   * 设置试卷详情
   * @param detail
   * @constructor
   */
  @Mutation
  private SET_EXAM_PAPER(detail: PreExamMockExaminationPaperResponse) {
    const paper = new ExamPaper()
    Object.assign(paper, detail)
    this.examPaper = paper
  }

  // 设置状态对象中的试卷分页的总页数
  @Mutation
  private SET_TOTAL_PAGE_SIZE(totalPageSize: number) {
    this.totalPageSize = totalPageSize
  }

  /**
   * 设置状态对象中试卷列表
   * @param examPaperList
   * @constructor
   */
  @Mutation
  private SET_EXAM_PAPER_LIST(examPaperList: Array<PreExamMockExaminationPaperItemResponse>) {
    this.examPaperList = new Array<ExamPaperItem>()
    examPaperList.map(x => {
      const item = new ExamPaperItem()
      Object.assign(item, x)
      this.examPaperList.push(item)
    })
  }

  /**
   * 追加用户信息
   * @param createUserList
   * @constructor
   */
  @Mutation
  private APPEND_CREATE_USER(createUserList: Array<UserSimpleInfoDTO>) {
    this.examPaperList.map(p => {
      const createUser = createUserList.filter(u => p.createUserId === u.userId)
      if (createUser && createUser.length) {
        p.createUser = createUser[0].nickName
      }
    })
  }

  /**
   * 追加试卷分类
   * @param list
   * @constructor
   */
  @Mutation
  private APPEND_CLASSIFY(list: Array<ExamPaperClassifyDTO>) {
    this.examPaperList = this.examPaperList.map(p => {
      const classifyDTO = list.filter(u => p.paperTypeId === u.id)
      if (classifyDTO && classifyDTO.length) {
        p.paperType = classifyDTO[0].name
      }
      return p
    })
  }

  /**
   * 追加试卷分类
   * @param item
   * @constructor
   */
  @Mutation
  private APPEND_CLASSIFY_FOR_DETAIL(item: ExamPaperClassifyDTO) {
    this.examPaper.paperType = item.name
  }

  /**
   * 更新试卷预览对象
   */
  @Mutation
  private SET_ANSWER_PAPER(answerPaper: AnswerExamPaper) {
    this.answerExamPaper = answerPaper
    console.log('预览答卷数据：', answerPaper)
  }
}

export default getModule(ExamPaperModule)
