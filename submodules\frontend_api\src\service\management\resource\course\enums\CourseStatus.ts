import AbstractEnum from '@api/service/common/enums/AbstractEnum'

enum CourseStatusEnum {
  enable = '1',
  disable = '0'
}

class CourseStatus extends AbstractEnum<CourseStatusEnum> {
  static enum = CourseStatusEnum

  constructor(status?: CourseStatusEnum) {
    super()
    this.current = status
    this.map.set(CourseStatusEnum.enable, '启用')
    this.map.set(CourseStatusEnum.disable, '停用')
  }
}

export default CourseStatus
