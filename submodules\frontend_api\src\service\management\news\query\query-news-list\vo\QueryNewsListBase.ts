import { Page } from '@hbfe/common'
import NewsListParamVo from '@api/service/management/news/query/query-news-list/vo/NewsListParamVo'
import newsListVo from '@api/service/management/news/query/query-news-list/vo/NewsListVo'
import BasicDataQueryBackstage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import UserModule from '@api/service/management/user/UserModule'
import AdminUserInfoVo from '@api/service/management/user/query/manager/vo/AdminUserInfoVo'

export default abstract class QueryNewsListBase {
  /**
   * 获取资讯列表
   */
  abstract queryNewsList(page: Page, queryRequest: NewsListParamVo): Promise<Array<newsListVo>>

  /**
   * 查询专题资讯
   * @param page 分页
   * @param queryRequest 筛选项
   */
  abstract querySpecialNewsList(page: Page, queryRequest: NewsListParamVo): Promise<Array<newsListVo>>

  /**
   * 获取资讯类别 - 一级目录
   */
  async queryNewsRootCategory() {
    const { data } = await BasicDataQueryBackstage.listRootNewsCategory(1)
    return data
  }
  /**
   * 获取资讯类别 - 二级目录
   * @param necId 资讯分类ID
   */
  async queryNewsChildrenCategory(necId: string) {
    const { data } = await BasicDataQueryBackstage.listChildNewsCategory({ status: 1, necId })
    return data
  }

  /**
   * 获取用户信息
   * @param ids id数组
   */
  protected async getUserInfo(ids: Array<string>) {
    // 根据用户ID获取用户信息
    const response = await UserModule.queryUserFactory.queryManager.queryManager(ids)
    const userIdMap: Map<string, AdminUserInfoVo> = new Map()
    response.data.forEach(item => {
      userIdMap.set(item.userId, item)
    })
    return userIdMap
  }
}
