import { ElectronicInvoiceTaxpayerResponse } from '@api/ms-gateway/ms-trade-configuration-v1'
import { InvoiceProviderEnum } from '@api/service/common/enums/online-school-config/InvoiceProviderTypes'

/*
  增值税电子发票详情
*/
class ElectronicInvoiceDetailVo extends ElectronicInvoiceTaxpayerResponse {
  /**
   * 纳税人名称
   */
  name = ''
  /**
   * 纳税人识别号
   */
  taxpayerNo = ''
  /**
   * 地址
   */
  address = ''
  /**
   * 电话
   */
  phone = ''
  /**
   * 开户行
   */
  bankName = ''
  /**
   * 开户账号
   */
  bankAccount = ''
  /**
   * 最大开票金额
   */
  invoiceMaxMoney = 0
  /**
   * 收款人
   */
  payee = ''
  /**
   * 开票人
   */
  issuer = ''
  /**
   * 复核人
   */
  reviewer = ''

  /* 开票平台授权信息 【start】*/
  /**
   * 开票提供商编号 5 - 诺诺，7 - 诺诺V2
   */
  invoiceProviderId: string = InvoiceProviderEnum.NUONUOV2
  /**
   * 授权码/企业私钥
   */
  secretAssessKey = ''
  /**
   * 应用访问标识/平台公钥
   */
  accessKey = ''
  /**
   * 部门ID
   */
  deptId = ''
  /* 开票平台授权信息 【end】*/

  /* 商品票面配置 【start】*/
  /**
   * 商品税务编码
   */
  commodityCode = ''
  /**
   * 服务名称
   */
  serviceTitle = ''
  /**
   * 单位
   */
  unitTitle = ''
  /**
   * 规格型号
   */
  specificationMode = ''
  /**
   * 是否打印数量
   */
  printQuantity: boolean = null
  /**
   * 是否打印单价
   */
  printPrice: boolean = null
  /**
   * 税率
   */
  rate = 0
  /* 商品票面配置 【end】*/
  /**
   * 备注
   */
  remark?: string = ''
  /**
   * 企业代码
   */
  enterpriseCode? = ''
  /**
   * 版税人身份证号
   */
  taxIdCard? = ''

  /**
   * 登录账号
   电子税局登录账号
   */
  taxLoginAccount: string
  /**
    * 登录密码
    电子税局登录密码
    */
  taxLoginPassword: string

  /**
   * 分机号
   */
  extensionNumber = ''
  /**
   * 税务优惠(仅全电票使用):0-无优惠,1-简易征收
   */
  taxFavoured: number = null

  from(item: ElectronicInvoiceTaxpayerResponse) {
    this.invoiceProviderId = item.invoiceAuthList[0]?.invoiceProviderId || ''
    this.secretAssessKey = item.invoiceAuthList[0]?.secretAssessKey || ''
    this.accessKey = item.invoiceAuthList[0]?.accessKey || ''
    this.deptId = item.invoiceAuthList[0]?.deptId || ''
    if (item.invoiceAuthList[0]?.invoiceProviderId == '7') {
      const invoiceAuthListV2 = item.invoiceAuthList.find(ite => ite.invoiceProviderId === '7')
      this.invoiceProviderId = invoiceAuthListV2?.invoiceProviderId || ''
      this.secretAssessKey = invoiceAuthListV2?.secretAssessKey || ''
      this.accessKey = invoiceAuthListV2?.accessKey || ''
      this.deptId = invoiceAuthListV2?.deptId || ''
      this.remark = invoiceAuthListV2?.remark || ''
    } else if (item.invoiceAuthList[0]?.invoiceProviderId == '6') {
      const invoiceAuthList = item.invoiceAuthList.find(ite => ite.invoiceProviderId === '6')
      this.invoiceProviderId = invoiceAuthList?.invoiceProviderId || ''
      this.secretAssessKey = invoiceAuthList?.secretAssessKey || ''
      this.accessKey = invoiceAuthList?.accessKey || ''
      this.remark = invoiceAuthList?.remark || ''
      this.enterpriseCode = invoiceAuthList?.enterpriseCode || ''
      this.taxIdCard = invoiceAuthList?.taxpayerIdNumber || ''
    } else if (item.invoiceAuthList[0]?.invoiceProviderId == '9' || item.invoiceAuthList[0]?.invoiceProviderId == '8') {
      const invoiceAuthListV2 = item.invoiceAuthList.find(
        ite => ite.invoiceProviderId === '9' || ite.invoiceProviderId === '8'
      )
      this.invoiceProviderId = invoiceAuthListV2?.invoiceProviderId || ''
      this.secretAssessKey = invoiceAuthListV2?.secretAssessKey || ''
      this.accessKey = invoiceAuthListV2?.accessKey || ''
      this.deptId = invoiceAuthListV2?.deptId || ''
      this.remark = invoiceAuthListV2?.remark || ''
      this.extensionNumber = invoiceAuthListV2.extensionNumber
    }
    this.extracted(item)
  }

  fromV2(item: ElectronicInvoiceTaxpayerResponse) {
    const invoiceAuthListV2 = item.invoiceAuthList.find(ite => ite.invoiceProviderId === '7')
    this.invoiceProviderId = invoiceAuthListV2?.invoiceProviderId || ''
    this.secretAssessKey = invoiceAuthListV2?.secretAssessKey || ''
    this.accessKey = invoiceAuthListV2?.accessKey || ''
    this.deptId = invoiceAuthListV2?.deptId || ''
    this.remark = invoiceAuthListV2?.remark || ''
    this.extracted(item)
  }
  fromNST(item: ElectronicInvoiceTaxpayerResponse) {
    const invoiceAuthListV2 = item.invoiceAuthList.find(
      ite => ite.invoiceProviderId === '8' || ite.invoiceProviderId === '9'
    )
    this.invoiceProviderId = invoiceAuthListV2?.invoiceProviderId || ''
    this.secretAssessKey = invoiceAuthListV2?.secretAssessKey || ''
    this.accessKey = invoiceAuthListV2?.accessKey || ''
    this.deptId = invoiceAuthListV2?.deptId || ''
    this.remark = invoiceAuthListV2?.remark || ''
    this.extensionNumber = invoiceAuthListV2.extensionNumber
    this.extracted(item)
  }
  fromBWJF(item: ElectronicInvoiceTaxpayerResponse) {
    const invoiceAuthList = item.invoiceAuthList.find(ite => ite.invoiceProviderId === '6')
    this.invoiceProviderId = invoiceAuthList?.invoiceProviderId || ''
    this.secretAssessKey = invoiceAuthList?.secretAssessKey || ''
    this.accessKey = invoiceAuthList?.accessKey || ''
    this.remark = invoiceAuthList?.remark || ''
    this.enterpriseCode = invoiceAuthList?.enterpriseCode || ''
    this.taxIdCard = invoiceAuthList?.taxpayerIdNumber || ''
    this.extracted(item)
  }

  private extracted(item: ElectronicInvoiceTaxpayerResponse) {
    this.name = item.name
    this.taxpayerNo = item.taxpayerNo
    this.address = item.address
    this.phone = item.phone
    this.bankName = item.bankName
    this.bankAccount = item.bankAccount
    this.invoiceMaxMoney = item.invoiceMaxMoney
    this.payee = item.payee
    this.issuer = item.issuer
    this.reviewer = item.reviewer
    this.commodityCode = item.commodityTicketList[0]?.commodityCode || ''
    this.serviceTitle = item.commodityTicketList[0]?.serviceTitle || ''
    this.unitTitle = item.commodityTicketList[0]?.unitTitle || ''
    this.specificationMode = item.commodityTicketList[0]?.specificationMode || ''
    this.printQuantity = item.commodityTicketList[0]?.printQuantity
    this.printPrice = item.commodityTicketList[0]?.printPrice
    this.rate = item.commodityTicketList[0]?.rate || 0
    this.taxFavoured = item.commodityTicketList[0]?.taxFavoured || 0
    this.taxLoginAccount = item.loginAccount
    this.taxLoginPassword = item.loginPassword
  }
}

export default ElectronicInvoiceDetailVo
