"""独立部署的微服务,K8S服务名:ms-news-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询资讯子分类列表
		@param childNewsCategoryListRequest 资讯分类编号，启用状态
		@return 资讯分类信息
	"""
	findChildNewsCategoryList(childNewsCategoryListRequest:ChildNewsCategoryListRequest):[NewsCategoryListResponse]
	"""查询资讯详细
		@param newsId 资讯编号
		@return 资讯分类信息
	"""
	findNewsDetail(newsId:String):[News]
	"""查询资讯展示详细
		@param newsParentIdCategoryId 资讯分类编号
		@return 展示详细资讯
	"""
	findNewsDetailWithPreviousAndNext(newsParentIdCategoryId:String):NewsDetailWithPreviousAndNextResponse
	"""查询弹窗公告列表
		@param top 返回数量
		@return 资讯列表
	"""
	findPopUpsList(top:Int!):[NewsListResponse]
	"""查询浏览数最多的资讯列表
		@param top 返回数量
		@return 资讯列表
	"""
	findReviewTopNews(top:Int!):[NewsListResponse]
	"""查询资讯分类
		@param enable 是否启用
		@return 资讯分类信息
	"""
	findRootNewsCategoryList(enable:Int!):[NewsCategoryListResponse]
	"""查询资讯列表
		@param newsParentIdCategoryId 资讯分类编号
		@return 资讯简略资讯列表
	"""
	findSimpleNewsByPublishPageList(newsParentIdCategoryId:String):[SimpleNewsByPublishPageListResponse]
	"""查询简略资讯列表
		@param simpleNewsPageListRequest 资讯搜索条件
		@return 资讯简略资讯列表
	"""
	findSimpleNewsPageList(simpleNewsPageListRequest:SimpleNewsPageListRequest):[SimpleNewsPageListResponse]
	"""查询最多资讯的资讯分类列表
		@param topNewsCategoryListRequest 排出的资讯分类编号，top数量
		@return 资讯分类信息
	"""
	findTopNewsCategoryList(topNewsCategoryListRequest:TopNewsCategoryListRequest):[NewsCategoryListResponse]
}
type Mutation {
	"""浏览资讯
		@param newsId 资讯编号
	"""
	browseNews(newsId:String):Void @optionalLogin
	"""新建草稿资讯
		@param newsCreateRequest 草稿资讯信息
		@return 资讯编号
	"""
	createNews(newsCreateRequest:NewsCreateRequest):String
	"""新建资讯分类
		@param newsCategoryCreateRequest 资讯分类信息
		@return 资讯编号
	"""
	createNewsCategory(newsCategoryCreateRequest:NewsCategoryCreateRequest):String
	"""新建专题资讯草稿
		@param request
		@return 资讯编号
	"""
	createSpecialSubjectNews(request:SpecialSubjectNewsCreateRequest):[String]
	"""删除资讯
		@param newsId 资讯编号
	"""
	deleteNews(newsId:String):Void
	"""删除资讯分类
		@param id 分类id
	"""
	deleteNewsCategory(id:String):Void
	"""禁用资讯分类
		@param id 分类id
	"""
	disableNewsCategory(id:String):Void
	"""启用资讯分类
		@param id 分类id
	"""
	enableNewsCategory(id:String):Void
	"""发布资讯
		@param newsId 资讯编号
	"""
	publishNews(newsId:String):Void
	"""发布咨询(智慧就业平台专用)
		200为发布成功，500为发布失败
		@param newsId
		@return
	"""
	pulishNewsByZhjyPlatform(newsId:String):NewsCommonResult
	"""置为草稿
		@param newsId 资讯编号
	"""
	unPublishNews(newsId:String):Void
	"""修改资讯
		@param request 修改的资讯信息
	"""
	updateNews(request:NewsUpdateRequest):Void
	"""修改资讯分类
		@param id 分类id
		@param name 分类名称
	"""
	updateNewsCategory(id:String,name:String):Void
	"""修改专题资讯
		@param request
	"""
	updateSpecialSubjectNews(request:SpecialSubjectNewsUpdateRequest):Void
}
input ChildNewsCategoryListRequest @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.request.ChildNewsCategoryListRequest") {
	"""资讯分类编号"""
	newsCategoryId:String
	"""是否启用"""
	enable:Int!
}
"""新建草稿资讯"""
input NewsCategoryCreateRequest @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.request.NewsCategoryCreateRequest") {
	"""分类名称"""
	name:String
	"""分类代码"""
	code:String
	"""上级分类编号"""
	parentId:String
}
"""新建草稿资讯"""
input NewsCreateRequest @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.request.NewsCreateRequest") {
	"""资讯分类编号"""
	newCategoryId:String
	"""资讯标题"""
	title:String
	"""摘要信息"""
	summary:String
	"""封面图片"""
	coverPath:String
	"""内容"""
	content:String
	"""来源"""
	source:String
	"""发布时间"""
	publishTime:DateTime
	"""是否弹窗公告"""
	popUps:Boolean!
	"""发布地区编码"""
	areaCodePath:String
	"""是否置顶"""
	top:Boolean!
	"""弹窗起始时间"""
	popupBeginTime:DateTime
	"""弹窗截止时间"""
	popupEndTime:DateTime
	"""是否需要校验弹窗时间段重叠，不填默认是会校验"""
	verifyPopUps:Boolean
}
"""资讯更新信息"""
input NewsUpdateRequest @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.request.NewsUpdateRequest") {
	"""资讯编号"""
	newsId:String
	"""资讯分类编号，null不更新"""
	newsCategoryId:String
	"""标题，null不更新"""
	title:String
	"""摘要，null不更新"""
	summary:String
	"""封面图片，null不更新"""
	coverPath:String
	"""内容，null不更新"""
	content:String
	"""来源，null不更新"""
	source:String
	"""发布地区编码，null不更新"""
	areaCodePath:String
	"""发布时间，null不更新"""
	publishTime:DateTime
	"""是否弹窗公告，null不更新"""
	popUps:Boolean
	"""是否置顶，null不更新"""
	top:Boolean
	"""弹窗起始时间"""
	popupBeginTime:DateTime
	"""弹窗截止时间"""
	popupEndTime:DateTime
	"""是否需要校验弹窗时间段重叠，不填默认是会校验"""
	verifyPopUps:Boolean
}
input SimpleNewsPageListRequest @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.request.SimpleNewsPageListRequest") {
	"""资讯分类编号"""
	newsCategoryId:String
	"""标题"""
	title:String
	"""是否弹窗公告"""
	popUps:Boolean
	"""资讯状态"""
	status:Int!
}
"""@Author: Czy
	@CreateTime: 2024-03-22  17:01
	@Description: 专题资讯草稿创建请求
"""
input SpecialSubjectNewsCreateRequest @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.request.SpecialSubjectNewsCreateRequest") {
	"""资讯分类编号"""
	newCategoryId:String
	"""专题信息集合"""
	specialSubjectInfoList:[SpecialSubjectInfo]
	"""资讯标题"""
	title:String
	"""摘要信息"""
	summary:String
	"""封面图片"""
	coverPath:String
	"""内容"""
	content:String
	"""来源"""
	source:String
	"""发布时间"""
	publishTime:DateTime
	"""是否弹窗公告"""
	popUps:Boolean!
	"""发布地区编码"""
	areaCodePath:String
	"""是否置顶"""
	top:Boolean!
	"""弹窗起始时间"""
	popupBeginTime:DateTime
	"""弹窗截止时间"""
	popupEndTime:DateTime
	"""是否需要校验弹窗时间段重叠，不填默认是会校验"""
	verifyPopUps:Boolean
}
"""@Author: Czy
	@CreateTime: 2024-03-22  17:22
	@Description: 专题资讯更新请求
"""
input SpecialSubjectNewsUpdateRequest @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.request.SpecialSubjectNewsUpdateRequest") {
	"""资讯编号"""
	newsId:String
	"""专题id"""
	specialSubjectInfo:SpecialSubjectInfo
	"""资讯分类编号，null不更新"""
	newsCategoryId:String
	"""标题，null不更新"""
	title:String
	"""摘要，null不更新"""
	summary:String
	"""封面图片，null不更新"""
	coverPath:String
	"""内容，null不更新"""
	content:String
	"""来源，null不更新"""
	source:String
	"""发布地区编码，null不更新"""
	areaCodePath:String
	"""发布时间，null不更新"""
	publishTime:DateTime
	"""是否弹窗公告，null不更新"""
	popUps:Boolean
	"""是否置顶，null不更新"""
	top:Boolean
	"""弹窗起始时间"""
	popupBeginTime:DateTime
	"""弹窗截止时间"""
	popupEndTime:DateTime
	"""是否需要校验弹窗时间段重叠，不填默认是会校验"""
	verifyPopUps:Boolean
}
input TopNewsCategoryListRequest @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.request.TopNewsCategoryListRequest") {
	"""排除的资讯分类编号"""
	newsCategoryId:String
	"""top数量"""
	top:Int!
}
"""@Author: Czy
	@CreateTime: 2024-04-08  19:05
	@Description: 专题信息
"""
input SpecialSubjectInfo @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.request.dto.SpecialSubjectInfo") {
	"""专题id"""
	specialSubjectId:String
	"""专题名称"""
	specialSubjectName:String
}
type News @type(value:"com.fjhb.ms.basicdata.repository.v1.news.model.News") {
	id:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerId:String
	necId:String
	title:String
	summary:String
	content:String
	coverPath:String
	source:String
	areaCodePath:String
	isPopUps:Boolean!
	isTop:Boolean!
	status:Int!
	publishUserId:String
	publishTime:DateTime
	createTime:DateTime
	updateTime:DateTime
	reviewCount:Int!
	popupBeginTime:DateTime
	popupEndTime:DateTime
	specialSubjectId:String
}
"""咨询通用返回结果类
	<AUTHOR> By lincong
	@date 2023/09/11 12:15
"""
type NewsCommonResult @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.consts.NewsCommonResult") {
	"""操作状态码"""
	code:String
	"""操作结果描述"""
	message:String
	"""咨询id"""
	newsId:String
}
type NewsCategoryListResponse @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.response.NewsCategoryListResponse") {
	"""资讯分类编号，null不更新"""
	newsCategoryId:String
	"""分类名称"""
	name:String
}
type NewsDetailWithPreviousAndNextResponse @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.response.NewsDetailWithPreviousAndNextResponse") {
	"""资讯编号"""
	newsId:String
	"""平台编号"""
	platformId:String
	"""平台版本编号"""
	platformVersionId:String
	"""项目编号"""
	projectId:String
	"""子项目编号"""
	subProjectId:String
	"""单位编号"""
	unitId:String
	"""服务商编号"""
	servicerId:String
	"""资讯分类编号"""
	newCategoryId:String
	"""资讯标题"""
	title:String
	"""摘要信息"""
	summary:String
	"""内容"""
	content:String
	"""封面图片"""
	coverPath:String
	"""来源"""
	source:String
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""是否置顶"""
	isTop:Boolean!
	"""资讯状态"""
	status:Int!
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""创建时间"""
	createTime:DateTime
	"""更新时间"""
	updateTime:DateTime
	"""浏览数量"""
	reviewCount:Int!
	"""弹窗起始时间"""
	popupBeginTime:DateTime
	"""弹窗截止时间"""
	popupEndTime:DateTime
	"""上一条咨询id"""
	previousNewsId:String
	"""下一条咨询id"""
	nextNewsId:String
}
type NewsListResponse @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.response.NewsListResponse") {
	"""资讯编号"""
	newsId:String
	"""资讯标题"""
	title:String
	"""发布时间"""
	publishTime:DateTime
}
type SimpleNewsByPublishPageListResponse @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.response.SimpleNewsByPublishPageListResponse") {
	"""资讯编号"""
	newsId:String
	"""资讯标题"""
	title:String
	"""是否置顶"""
	top:Boolean!
	"""是否弹窗公告"""
	popUps:Boolean!
	"""摘要信息"""
	summary:String
	"""发布时间"""
	publishTime:DateTime
	"""发布人编号"""
	publishUserId:String
}
type SimpleNewsPageListResponse @type(value:"com.fjhb.ms.news.v1.kernel.gateway.graphql.response.SimpleNewsPageListResponse") {
	"""资讯编号"""
	newsId:String
	"""资讯标题"""
	title:String
	"""是否置顶"""
	top:Boolean!
	"""是否弹窗公告"""
	popUps:Boolean!
	"""摘要信息"""
	summary:String
	"""发布时间"""
	publishTime:DateTime
	"""发布人编号"""
	publishUserId:String
	"""分类名称"""
	name:String
	"""发布状态"""
	publishStatus:Int!
}

scalar List
