import msExam, {
  AnswerRequest,
  AskQuestionAnswer,
  FillQuestionAnswer,
  MultipleQuestionAnswer,
  OpinionQuestionAnswer,
  RadioQuestionAnswer
} from '@api/ms-gateway/ms-exam-answer-v1'
import ExamingReportModel from '@api/service/common/webfunny/models/ExamingReportModel'
import { ExamingEnum } from '@api/service/common/webfunny/enums/ExamingEnum'
import WebfunnyReport from '@api/service/common/webfunny/WefunnyReport'

export class MutationSubmitPaper {
  errorReport = new ExamingReportModel()
  //作答token
  token = ''
  //提交成功后的答卷id
  paperID = ''
  //答案数组
  answerArr: Array<
    AskQuestionAnswer | FillQuestionAnswer | MultipleQuestionAnswer | OpinionQuestionAnswer | RadioQuestionAnswer
  > = []
  async submitPaper() {
    let status
    if (this.answerArr.length) {
      status = await this.savePaper()
      if (status.isSuccess()) {
        status = await this.submitPaperToken()
      } else {
        if (status.errors && status.errors.length) {
          const firstError = status.errors[0]
          if (firstError.code == 40001) {
            status = await this.submitPaperToken()
          }
        }
      }
    } else {
      status = await this.submitPaperToken()
    }

    return status
  }
  //预提交答卷
  async savePaper() {
    const res = await msExam.preSubmitAnswer({
      token: this.token,
      answers: this.answerArr as AnswerRequest[]
    })

    if (!res.status.isSuccess()) {
      try {
        const params = new URLSearchParams(window.location.href)
        // 获取 qualificationId 的值
        const paperType = params.get('paperType')
        if (window.location.pathname === '/exam/quiz' || paperType === 'quiz') {
          // 获取 qualificationId 的值
          const qualificationId = params.get('qualificationId')
          this.errorReport.qualificationId = qualificationId ? qualificationId : params.get('studentNo')
          this.errorReport.bizCode = `${res.status.code}`
          this.errorReport.scene = ExamingEnum.quiz_message
          this.errorReport.questionId = this.answerArr[this.answerArr.length - 1].questionId
          const param = {
            token: this.token
          }
          this.errorReport.requestUrl = 'preSubmitAnswer'
          this.errorReport.requestBody = `${JSON.stringify(param)}`
          this.errorReport.response = `${JSON.stringify(res.status.errors)}`
          WebfunnyReport.upCommonEvent('通用-测验', this.errorReport)
        } else if (window.location.pathname === '/exam/practice' || paperType === 'practice') {
          // 获取 qualificationId 的值
          const qualificationId = params.get('qualificationId')
          this.errorReport.qualificationId = qualificationId ? qualificationId : params.get('studentNo')
          this.errorReport.bizCode = `${res.status.code}`
          this.errorReport.scene = ExamingEnum.practice_message
          this.errorReport.questionId = this.answerArr[this.answerArr.length - 1].questionId
          const param = {
            token: this.token
          }
          this.errorReport.requestUrl = 'preSubmitAnswer'
          this.errorReport.requestBody = `${JSON.stringify(param)}`
          this.errorReport.response = `${JSON.stringify(res.status.errors)}`
          WebfunnyReport.upCommonEvent('通用-练习', this.errorReport)
        } else if (window.location.pathname === '/exam/mockExam' || paperType === 'exam') {
          // 获取 qualificationId 的值
          const qualificationId = params.get('qualificationId')
          this.errorReport.qualificationId = qualificationId ? qualificationId : params.get('studentNo')
          this.errorReport.bizCode = `${res.status.code}`
          this.errorReport.scene = ExamingEnum.exam_message
          this.errorReport.questionId = this.answerArr[this.answerArr.length - 1].questionId
          const param = {
            token: this.token
          }
          this.errorReport.requestUrl = 'preSubmitAnswer'
          this.errorReport.requestBody = `${JSON.stringify(param)}`
          this.errorReport.response = `${JSON.stringify(res.status.errors)}`
          WebfunnyReport.upCommonEvent('通用-考试', this.errorReport)
        }
      } catch {
        console.log('上报异常')
      }
    }

    return res.status
  }
  //根据token交卷
  private async submitPaperToken() {
    const res = await msExam.applyHanding(this.token)
    if (res.status.isSuccess()) {
      this.paperID = res.data
    }
    return res.status
  }
}
