<route-meta>
{
"isMenu": true,
"title": "导入学员",
"sort": 1
}
</route-meta>
<script lang="ts">
  import ImportStudent from '@hbfe/jxjy-admin-import/src/student/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import {
    // 江苏工考一体化平台子项目管理员
    ZXMGLY,
    // 内置地区管理员角色
    DQGLY,
    // 施教机构管理员
    WXGLY,
    // 学员
    XY
  } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    download: [WXGLY],
    uploadComponent: [WXGLY],
    upload: [WXGLY]
  })
  export default class extends ImportStudent {}
</script>
