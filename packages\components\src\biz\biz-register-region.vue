<!--
 * @Author: zhj
 * @Date: 注册地区级联选择器   --统计部分
-->
<template>
  <el-cascader
    ref="elCascaderRef"
    v-if="showRegionCascader"
    :props="props"
    :options="regionOptions"
    v-model="selctValue"
    :placeholder="placeholder"
    :clearable="clearable"
    :style="{ width: '100%' }"
    @change="changeInput"
  ></el-cascader>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Emit, Watch, Ref } from 'vue-property-decorator'
  import RegionVo from '@api/service/common/basic-data-dictionary/query/vo/RegionVo'
  import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
  import { ElCascader } from 'element-ui/types/cascader'
  import QueryRegisterRegion from '@api/service/common/basic-data-dictionary/query/QueryRegisterRegion'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import { cloneDeep } from 'lodash'
  @Component
  export default class extends Vue {
    @Ref('elCascaderRef') elCascaderRef: ElCascader
    showRegionCascader = true

    @Prop({
      type: Boolean,
      default: true
    })
    clearable: boolean

    @Prop({
      default: false
    })
    multiple: boolean

    @Prop({
      default: '请选择地区',
      type: String
    })
    placeholder: string

    // 父子节点是否强关联
    @Prop({
      default: false
    })
    checkStrictly: boolean

    // 传入的必须是数组
    @Prop({
      type: Array
    })
    valueList: string[]

    // 当前选中的值
    selctValue: string[] = []
    // 初始化配置选项
    props = {}

    @Watch('valueList', {
      deep: true,
      immediate: true
    })
    valueChange(val: string[]) {
      if (val?.length) {
        this.selctValue = val
      }
    }
    regionOptions: Array<RegionTreeVo> = new Array<RegionTreeVo>()

    // 切换地区
    changeInput(val: string[]) {
      this.$emit('input', val)
    }

    async created() {
      await this.setProps()
      this.regionOptions = await QueryRegisterRegion.queryRegion()
    }
    // 初始化
    async setProps() {
      this.props = {
        lazy: false,
        value: 'code',
        label: 'name',
        checkStrictly: this.checkStrictly
      }
    }
  }
</script>
