import {
  CourseInfoRequest,
  CourseOfCourseTrainingOutlineRequest,
  StudentCourseLearningRequest,
  StudentCourseMediaLearningRecordRequest,
  StudentCourseRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import CourseTypeEnum from '@api/service/customer/course/query/enum/CourseTypeEnum'
import MyCourseLearningStatusEnum from '@api/service/customer/course/query/enum/MyCourseLearningStatusEnum'
import CategoryTypeEnum from '../enum/CategoryTypeEnum'

enum QueryMyLearningCoursePageEnum {
  compulsory = 'COMPULSORY',
  elective = 'ELECTIVE'
}

/**
 * 查询我的学习的分页
 */
class QueryMyAutonomousCoursePageParam {
  // 未完成
  static isNotComplete = [MyCourseLearningStatusEnum.not_qualified, MyCourseLearningStatusEnum.not_judged]
  // 已完成
  static isComplete = [MyCourseLearningStatusEnum.qualified]
  studentNo: string
  learningStatus?: Array<MyCourseLearningStatusEnum>
  outlineIdList?: Array<string>
  category?: CategoryTypeEnum
  courseType?: CourseTypeEnum
  courseName?: string
  /**
   * 学员媒体课程学习信息
   */
  studentCourseMediaLearningRecord?: StudentCourseMediaLearningRecordRequest

  to(): StudentCourseLearningRequest {
    const request = new StudentCourseLearningRequest()
    request.studentNo = this.studentNo
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    request.course = new CourseInfoRequest()
    if (this.outlineIdList) {
      request.courseOfCourseTrainingOutline.outlineIds = this.outlineIdList
    }
    if (this.category) {
      request.courseOfCourseTrainingOutline.courseType = this.category
    }
    if (this.courseName) {
      request.course.courseName = this.courseName
    }
    request.courseOfCourseTrainingOutline.courseType = this.courseType
    request.studentCourse = new StudentCourseRequest()
    request.studentCourse.courseLearningStatus = this.learningStatus
    request.studentCourseMediaLearningRecord = this.studentCourseMediaLearningRecord
    return request
  }
}

export default QueryMyAutonomousCoursePageParam
