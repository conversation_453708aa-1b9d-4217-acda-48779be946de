schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""申请分销商身份再认证
		@param request {accessToken:访问令牌（网校域的令牌）}
		@return {code,message,identityAuthenticationToken}
		code 说明如下:
		30001:请求头未携带分销商服务商信息
		30002:当前分销商不存在
		30003:当前服务商不是分销商类型
		30004:当前分销商服务商信息中缺失所属服务商ID
		30010:访问令牌凭证信息异常，未包含业务域信息
		30011:当前分销商不属于目标网校
		30012:再认证失败,没有成功签发身份凭证
		30013:再认证异常,没有成功签发身份凭证
		30021:无法获取客户端标识
	"""
	applyDistributorReAuthenticate(request:ApplyDistributorReAuthenticateRequest):IdentityAuthenticationTokenResponse @optionalLogin
}
"""<AUTHOR>
input ApplyDistributorReAuthenticateRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.identityAuth.ApplyDistributorReAuthenticateRequest") {
	"""访问令牌（网校域的令牌）"""
	accessToken:String!
}
"""身份认证统一响应
	<AUTHOR>
"""
type IdentityAuthenticationTokenResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.identityAuth.IdentityAuthenticationTokenResponse") {
	"""身份凭证Token"""
	identityAuthenticationToken:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}

scalar List
