"""独立部署的微服务,K8S服务名:ms-basicdata-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""功能描述：项目级-根据用户id获取管理员信息-详细接口
		@param userId                  :用户id
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.AdminInfoResponse
		@date : 2022年10月11日 17:02:18
	"""
	getAdminInfoInSubProject(userId:String):AdminUserInfoResponse @optionalLogin
	"""@Description 项目级-根据银行编码查询单个银行字典信息-明细接口
		根据银行编码查询当前子项目下的银行信息
		<AUTHOR>
		@Date 2022/9/29 12:01
		@param code    :银行编码
		@return com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.BankResponse
	"""
	getBankInSubProject(code:String):BankResponse @optionalLogin
	"""功能描述：项目级-根据code和业务数据字典类型查询单个业务数据字典信息接口-明细接口
		描述：根据code和业务数据字典类型查询当前子项目下的业务数据字典信息
		@param request :业务数据字典查询条件
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.BusinessDataDictionaryResponse
		@date : 2022年8月12日 15:56:50
	"""
	getBusinessDataDictionaryInSubProject(request:BusinessDataDictionaryCodeRequest):BusinessDataDictionaryResponse @optionalLogin
	"""功能描述：企业-当前登录企业管理员信息
		描述：查询当前登录管理员的信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
	"""
	getEnterpriseUnitAdminInfoInMyself:EnterpriseUnitAdminInfoResponse
	"""功能描述：项目级-企业单位管理员详情查询接口
		描述：查询当前企业单位下指定管理员的信息，如不存在返回null
		@param accountId               : 帐户ID
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
		@date : 2022年6月9日 16:50:10
	"""
	getEnterpriseUnitAdminInfoInSubProject(accountId:String):EnterpriseUnitAdminInfoResponse @optionalLogin
	"""政府单位下-按条件统计企业单位总数量
		@param request 企业单位统计查询条件
		@return 统计数量结果
	"""
	getEnterpriseUnitCountInGovernmentUnit(request:EnterpriseUnitStatisticRequest):Long!
	"""子项目下-按条件统计企业单位总数量
		@param request 企业单位统计查询条件
		@return 统计数量结果
	"""
	getEnterpriseUnitCountInSubProject(request:EnterpriseUnitStatisticRequest):Long! @optionalLogin
	"""功能描述：企业单位-当前登录企业单位详情查询接口
		描述：查询当前企业单位信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse
		@date : 2022年6月9日 10:49:47
	"""
	getEnterpriseUnitInfoInMyself:EnterpriseUnitInfoResponse
	"""功能描述：项目级-企业单位详情查询接口
		描述：查询当前子项目下指定企业单位的信息
		@param unitId                  :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse
		@date : 2022年6月9日 10:49:47
	"""
	getEnterpriseUnitInfoInSubProject(unitId:String):EnterpriseUnitInfoResponse @optionalLogin
	"""功能描述：政府单位-当前登录政府单位管理员信息
		描述：查询当前登录管理员的信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
	"""
	getGovernmentUnitAdminInfoInMyself:GovernmentUnitAdminInfoResponse
	"""功能描述：政府单位管理员详情查询接口
		描述：查询当前政府单位下指定管理员的信息，如不存在返回null
		@param accountId               : 帐户ID
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.GovernmentUnitAdminInfoResponse
		@date : 2022年6月9日 16:50:10
	"""
	getGovernmentUnitAdminInfoInSubProject(accountId:String):GovernmentUnitAdminInfoResponse @optionalLogin
	"""功能描述：政府单位-获取当前登录政府单位信息接口
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.GovernmentUnitInfoResponse
		@date : 2022年6月9日 10:49:47
	"""
	getGovernmentUnitInfoInMyself:GovernmentUnitInfoResponse @optionalLogin
	"""功能描述：获取政府单位信息接口
		描述：查询当前子项目下指定政府单位的信息
		@param unitId                  :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.GovernmentUnitInfoResponse
		@date : 2022年6月9日 10:49:47
	"""
	getGovernmentUnitInfoInSubProject(unitId:String):GovernmentUnitInfoResponse @optionalLogin
	"""根据分类id获取顶级分类信息
		@param rootCategoryCode 顶级分类代码
		@param code 分类代码
		@return
	"""
	getNewsCategoryId(rootCategoryCode:String!,code:String!):NewsCategoryResponse @optionalLogin
	"""查询详细资讯
		@param newId 资讯id
		@return 资讯信息
	"""
	getNewsDetail(newId:String!):NewsDetailResponse
	"""功能描述：项目级-根据地区编码查询单个地区字典信息-明细接口
		描述：根据地区编码查询当前子项目下的地区信息
		@param request : 地区查询条件
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.RegionResponse
		@date : 2022年8月12日 15:56:50
	"""
	getRegionInSubProject(request:RegionCodeRequest):RegionResponse @optionalLogin
	"""根据项目id查询顶级资讯分类
		@return 资讯分类信息
	"""
	getRootNewsCategory:[NewsCategoryResponse]
	"""根据分类id获取顶级分类信息
		@param necId 分类id
		@return
	"""
	getRootNewsCategoryById(necId:String!):NewsCategoryResponse
	"""功能描述 :子项目-获取用户详细信息接口-详细接口
		描述：查询子项目下指定的用户信息，如不存在返回null
		@param userId           :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.StudentInfoResponse
		@date : 2022年11月9日 14:48:20
	"""
	getUserInSubProject(userId:String):UserInfoResponse @optionalLogin
	"""功能描述：项目级-查询指定业务数据字典类型的字典信息-列表接口
		描述：查询指定业务数据字典类型的字典列表，默认按排序字段升序排
		@param request :业务数据字典查询条件
		@return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.BusinessDataDictionaryResponse>
		@date : 2022年8月12日 17:28:02
	"""
	listBusinessDataDictionaryInSubProject(request:BusinessDataDictionaryRequest):[BusinessDataDictionaryResponse] @optionalLogin
	listChildNewsCategory(status:Int!,necId:String!):[NewsCategoryResponse] @optionalLogin
	"""功能描述：项目级-查询下一级地区信息-列表接口
		描述：根据父级地区编码查询当前子项目下的地区信息，默认按排序字段升序排
		@param request : 地区查询条件
		@return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.RegionResponse>
		@date : 2022年8月12日 14:08:53
	"""
	listChildRegionInSubProject(request:RegionCodeRequest):[RegionResponse] @optionalLogin
	"""功能描述：项目级-根据地区编码集合查询地区信息-列表接口
		描述：根据地区编码集合查询当前子项目下的地区信息集合，默认按排序字段升序排
		@param request : 地区集合查询条件
		@return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.RegionResponse>
		@date : 2022年8月12日 14:08:53
	"""
	listRegionByCodeInSubProject(request:RegionCodeListRequest):[RegionResponse] @optionalLogin
	"""功能描述：项目级-查询地区信息-列表接口
		描述：根据地区路径编码查询当前子项目下的地区信息，默认按排序字段升序排
	"""
	listRegionByCodePathInSubProject(request:RegionListRequest):[RegionResponse] @optionalLogin
	"""功能描述：项目级-根据地区编码查询指定级别的地区信息-列表接口
		描述：根据地区编码查询指定级别的地区信息，默认按排序字段升序排
		@param request : 地区查询条件
		@return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.RegionResponse>
		@date : 2022年9月22日 20:00:00
	"""
	listRegionInSubProject(request:RegionRequest):[RegionResponse] @optionalLogin
	listRootNewsCategory(status:Int!):[NewsCategoryResponse] @optionalLogin
	"""@Description 项目级-查询银行字典信息-分页列表接口
		查询银行字典信息分页列表，默认按排序字段升序排
		<AUTHOR>
		@Date 2022/9/29 11:53
		@param page    :分页对象
		@param request :银行字典查询条件
		@return com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.BankResponse>
	"""
	pageBankInSubProject(page:Page,request:BankRequest):BankResponsePage @page(for:"BankResponse") @optionalLogin
	"""功能描述：项目级-查询指定业务数据字典类型的字典信息-分页列表接口
		描述：查询指定业务数据字典类型的字典分页列表，默认按排序字段升序排
		@param page    :分页对象
		@param request :业务数据字典查询条件
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.BusinessDataDictionaryResponse>
		@date : 2022年8月12日 17:28:02
	"""
	pageBusinessDataDictionaryInSubProject(page:Page,request:BusinessDataDictionaryRequest):BusinessDataDictionaryResponsePage @page(for:"BusinessDataDictionaryResponse") @optionalLogin
	"""查询资讯列表(用于顶级分类的业务平台)
		@param queryRequest 简略资讯查询条件  todo
		@return 资讯分页信息
	"""
	pageCompleteNews(queryRequest:NewsCompleteQueryRequest,page:Page):NewsCompleteResponsePage @page(for:"NewsCompleteResponse")
	"""功能描述： 政府单位-查询本级及下属企业单位管理员-分页列表接口
		描述：查询政府单位管辖下的企业单位管理员信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:10
	"""
	pageEnterpriseUnitAdminInfoInGovernmentUnit(page:Page,request:EnterpriseUnitAdminQueryRequest):EnterpriseUnitAdminInfoResponsePage @page(for:"EnterpriseUnitAdminInfoResponse") @optionalLogin
	"""功能描述：企业单位-查询当前登录企业单位下属管理员-分页列表接口
		描述：查询当前企业单位下的管理员信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:10
	"""
	pageEnterpriseUnitAdminInfoInMyself(page:Page,request:AdminQueryRequest):EnterpriseUnitAdminInfoResponsePage @page(for:"EnterpriseUnitAdminInfoResponse") @optionalLogin
	"""功能描述：项目级-查询企业单位管理员-分页列表接口
		描述：查询当前子项目下的企业单位管理员信息，默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:21
	"""
	pageEnterpriseUnitAdminInfoInSubProject(page:Page,request:EnterpriseUnitAdminQueryRequest):EnterpriseUnitAdminInfoResponsePage @page(for:"EnterpriseUnitAdminInfoResponse") @optionalLogin
	"""功能描述：政府单位-查询本级及下属企业单位-分页列表接口
		描述：查询当前政府单位下管辖的企业单位信息，默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse>
		@date : 2022年6月9日 10:47:54
	"""
	pageEnterpriseUnitInfoInGovernmentUnit(page:Page,request:EnterpriseUnitRequest):EnterpriseUnitInfoResponsePage @page(for:"EnterpriseUnitInfoResponse")
	"""功能描述：服务商-查询企业单位-分页列表接口
		描述：默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse>
		@date : 2022年7月29日15:59:49
	"""
	pageEnterpriseUnitInfoInServicer(page:Page,request:EnterpriseUnitRequest):EnterpriseUnitInfoResponsePage @page(for:"EnterpriseUnitInfoResponse")
	"""功能描述：项目级-查询企业单位-分页列表接口
		描述：查询当前子项目下的企业单位信息，默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse>
		@date : 2022年6月9日 10:47:54
	"""
	pageEnterpriseUnitInfoInSubProject(page:Page,request:EnterpriseUnitRequest):EnterpriseUnitInfoResponsePage @page(for:"EnterpriseUnitInfoResponse") @optionalLogin
	"""功能描述：政府单位-查询本级及下属政府单位管理员-分页列表接口
		描述：查询当前政府单位下管辖的政府单位管理员信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.GovernmentUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:21
	"""
	pageGovernmentUnitAdminInfoInGovernmentUnit(page:Page,request:GovernmentUnitAdminQueryRequest):GovernmentUnitAdminInfoResponsePage @page(for:"GovernmentUnitAdminInfoResponse") @optionalLogin
	"""功能描述：政府单位-查询当前登录政府单位下属管理员-分页列表接口
		描述：查询当前政府单位下的政府单位管理员信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.GovernmentUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:21
	"""
	pageGovernmentUnitAdminInfoInMyself(page:Page,request:AdminQueryRequest):GovernmentUnitAdminInfoResponsePage @page(for:"GovernmentUnitAdminInfoResponse") @optionalLogin
	"""功能描述：项目级-查询政府单位管理员-分页列表接口
		描述：查询当前子项目下的政府单位管理员信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.GovernmentUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:21
	"""
	pageGovernmentUnitAdminInfoInSubProject(page:Page,request:GovernmentUnitAdminQueryRequest):GovernmentUnitAdminInfoResponsePage @page(for:"GovernmentUnitAdminInfoResponse") @optionalLogin
	"""功能描述：项目级-查询政府单位-分页列表接口
		描述：查询当前子项目下的政府单位信息，默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.GovernmentUnitInfoResponse>
		@date : 2022/9/21 19:10
	"""
	pageGovernmentUnitInfoInEnterpriseUnit(page:Page,request:GovernmentUnitRequest):GovernmentUnitInfoResponsePage @page(for:"GovernmentUnitInfoResponse") @optionalLogin
	"""功能描述：政府单位-查询本级及下属政府单位-分页列表接口
		描述：查询当前政府单位以及下级政府单位信息，默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.GovernmentUnitInfoResponse>
		@date : 2022年6月9日 10:47:54
	"""
	pageGovernmentUnitInfoInGovernmentUnit(page:Page,request:GovernmentUnitRequest):GovernmentUnitInfoResponsePage @page(for:"GovernmentUnitInfoResponse") @optionalLogin
	"""功能描述：项目级-查询政府单位-分页列表接口
		描述：查询当前子项目下的政府单位信息，默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.GovernmentUnitInfoResponse>
		@date : 2022年6月9日 10:47:54
	"""
	pageGovernmentUnitInfoInSubProject(page:Page,request:GovernmentUnitRequest):GovernmentUnitInfoResponsePage @page(for:"GovernmentUnitInfoResponse") @optionalLogin
	"""功能描述：项目级-查询评价机构-分页列表接口
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse>
		@date : 2022年6月9日 10:47:54
	"""
	pageRatingAgenciesUnitInfoInGovernmentUnit(page:Page,request:RatingAgenciesUnitRequest):RatingAgenciesUnitInfoResponsePage @page(for:"RatingAgenciesUnitInfoResponse") @optionalLogin
	"""查询简略资讯列表
		@param queryRequest 简略资讯查询条件
		@return 资讯分页信息
	"""
	pageSimpleNews(queryRequest:NewsSimpleQueryRequest,page:Page):NewsSimpleResponsePage @page(for:"NewsSimpleResponse") @optionalLogin
	"""功能描述：查询用户信息列表-分页接口
		描述：查询子项目下的学员分页信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 : 查询参数
		@param dataFetchingEnvironment :
		@date : 2022年11月7日 09:37:49
	"""
	pageUserInfoInGeneral(page:Page,request:UserQueryRequest):UserInfoResponsePage @page(for:"UserInfoResponse") @optionalLogin
	"""政府单位下-统计各时间段内企业单位总数量
		@param request
		@return
	"""
	statisticEnterpriseUnitGroupByTimeInGovernmentUnit(request:EnterpriseUnitDateHistogramRequest):DateHistogramResponse
	"""政府单位下-统计各行业类型下企业单位总数量"""
	statisticEnterpriseUnitIndustryInGovernmentUnit(request:EnterpriseUnitStatisticRequest):[EnterpriseUnitIndustryStatisticResponse]
	"""政府单位下-统计各地区下企业单位总数量"""
	statisticEnterpriseUnitRegionInGovernmentUnit(request:EnterpriseUnitStatisticRequest):[EnterpriseUnitRegionStatisticResponse]
	"""政府单位下-统计各单位类型下企业单位总数量"""
	statisticEnterpriseUnitTypeInGovernmentUnit(request:EnterpriseUnitStatisticRequest):[EnterpriseUnitTypeStatisticResponse]
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""功能描述：账户信息查询条件
	@Author： wtl
	@Date： 2022年5月11日 15:30:56
"""
input AccountRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.AccountRequest") {
	"""账户类型 1：企业帐户，2：企业个人帐户，3：个人帐户
		@see AccountTypes
	"""
	accountTypeList:[Int]
	"""账户状态 1：正常，2：冻结
		@see AccountStatus
	"""
	statusList:[Int]
	"""创建时间范围"""
	createTimeScope:DateScopeRequest
	"""创建人用户id"""
	createdUserId:String
	"""单位id （原始单位id，不会随着id 人员与单位的关系变化而变化）"""
	unitId:[String]
	"""单位id匹配方式 默认-1、and匹配 2、or匹配
		@see MatchTypeConstant
	"""
	unitIdMatchType:Int
}
"""功能描述：登录认证查询条件
	@Author： wtl
	@Date： 2022年1月26日 09:30:12
"""
input AuthenticationRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.AuthenticationRequest") {
	"""帐号"""
	identity:String
}
"""功能描述 : 直方图统计查询条件"""
input DateHistogramRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.DateHistogramRequest") {
	"""开始时间（必填）"""
	startTime:DateTime
	"""结束时间（必填）"""
	endTime:DateTime
	"""时间单位枚举（必填）"""
	timeUnit:TimeUnitEnum
}
"""功能描述：角色查询条件
	@Author： wtl
	@Date： 2022年5月11日 11:46:41
"""
input RoleRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.RoleRequest") {
	"""角色id集合"""
	roleIdList:[String]
	"""角色类型
		（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
		@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
	"""
	roleTypeList:[String]
	"""角色类别
		（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管 10:人社超管
		11:人社职建处 12:人社就业局 13:超级管理员 14:合同供应商 15:专家 16:电子劳动合同业务角色 320:地区管理员 410:人社行业管理员
		411:人社行业省级管理员 412:人社行业市级管理员 413:人社行业区县级管理员 420:人社地区管理员 430:人社业务管理员 440:省级人社主管 450:市级人社主管
		460:区县级人社主管 510:培训机构管理员 520:技工院校管理员 530:职业院校管理员 540:政策参与者 550:线上培训机构管理员 560:课件供应商
		5001:学徒制_企业管理员 5101:学徒制_培训机构管理员 5201:学徒制_技工院校管理员 5301:学徒制_职业院校管理员 4011:学徒制_人社_省级管理员
		4021:学徒制_人社_市级管理员 4031:学徒制_人社_区/县级管理员 6001:揭榜挂帅_企业管理员 6101:揭榜挂帅_培训机构管理员
		6201:揭榜挂帅_技工院校管理员 6301:揭榜挂帅_职业院校管理员 6401:揭榜挂帅_人社_省级管理员 6402:揭榜挂帅_人社_市级管理员 6403:揭榜挂帅_人社_区/县级管理员）
		@see RoleCategories
	"""
	roleCategoryList:[Int]
	"""排除的角色类别集合
		（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管 10:人社超管
		11:人社职建处 12:人社就业局 13:超级管理员 14:合同供应商 15:专家 16:电子劳动合同业务角色 320:地区管理员 410:人社行业管理员
		411:人社行业省级管理员 412:人社行业市级管理员 413:人社行业区县级管理员 420:人社地区管理员 430:人社业务管理员 440:省级人社主管 450:市级人社主管
		460:区县级人社主管 510:培训机构管理员 520:技工院校管理员 530:职业院校管理员 540:政策参与者 550:线上培训机构管理员 560:课件供应商
		5001:学徒制_企业管理员 5101:学徒制_培训机构管理员 5201:学徒制_技工院校管理员 5301:学徒制_职业院校管理员 4011:学徒制_人社_省级管理员
		4021:学徒制_人社_市级管理员 4031:学徒制_人社_区/县级管理员 6001:揭榜挂帅_企业管理员 6101:揭榜挂帅_培训机构管理员
		6201:揭榜挂帅_技工院校管理员 6301:揭榜挂帅_职业院校管理员 6401:揭榜挂帅_人社_省级管理员 6402:揭榜挂帅_人社_市级管理员 6403:揭榜挂帅_人社_区/县级管理员）
		@see RoleCategories
	"""
	excludeRoleCategoryList:[Int]
	"""角色应用方ID"""
	applicationMemberIdList:[String]
}
"""功能描述：管理员查询条件
	@Author： wtl
	@Date： 2022年1月25日 15:24:10
"""
input AdminQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.admin.AdminQueryRequest") {
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:AdminUserRequest
	"""登录认证信息"""
	authentication:AuthenticationRequest
	"""角色信息查询"""
	role:RoleRequest
	"""排序"""
	sortList:[AdminSortRequest]
}
"""功能描述：管理员排序
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
input AdminSortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.admin.AdminSortRequest") {
	"""管理员排序字段"""
	sortField:EnterprisePersonSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
"""功能描述：管理员查询条件
	@Author： wtl
	@Date： 2022年1月25日 15:24:10
"""
input AdminUserRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.admin.AdminUserRequest") {
	"""管理地区路径集合（模糊，右like）"""
	manageRegionPathList:[String]
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	phoneMatchType:Int
}
"""功能描述 : 企业单位管理员查询条件
	@date : 2022/6/17 17:32
"""
input EnterpriseUnitAdminQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.admin.EnterpriseUnitAdminQueryRequest") {
	"""企业单位管理员归属信息"""
	owner:EnterpriseUnitAdminOwnerRequest
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:AdminUserRequest
	"""登录认证信息"""
	authentication:AuthenticationRequest
	"""角色信息查询"""
	role:RoleRequest
	"""排序"""
	sortList:[AdminSortRequest]
}
"""功能描述 : 政府单位管理员查询条件
	@date : 2022/6/17 17:32
"""
input GovernmentUnitAdminQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.admin.GovernmentUnitAdminQueryRequest") {
	"""政府单位管理员归属信息"""
	owner:GovernmentUnitAdminOwnerRequest
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:AdminUserRequest
	"""登录认证信息"""
	authentication:AuthenticationRequest
	"""角色信息查询"""
	role:RoleRequest
	"""排序"""
	sortList:[AdminSortRequest]
}
"""功能描述 : 企业单位管理员归属查询条件
	@date : 2022/6/17 17:42
"""
input EnterpriseUnitAdminOwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.admin.nested.EnterpriseUnitAdminOwnerRequest") {
	"""企业单位id路径集合
		String："/福建电信id/福州电信分公司id"
	"""
	enterpriseUnitIdPathList:[String]
	"""企业单位id路径匹配方式，默认为右模糊查询(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	enterpriseUnitIdPathMatchType:Int
}
"""功能描述 : 政府单位管理员归属查询条件
	@date : 2022/6/17 17:42
"""
input GovernmentUnitAdminOwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.admin.nested.GovernmentUnitAdminOwnerRequest") {
	"""政府单位id路径集合
		String："/福建省人社id/福州市人社id"
	"""
	governmentUnitIdPathList:[String]
	"""政府单位id路径匹配方式，默认为右模糊查询(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	governmentUnitIdPathMatchType:Int
}
"""@Description 银行查询
	<AUTHOR>
	@Date 2022/9/28 11:14
	@Version 1.0
"""
input BankRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.dictionary.BankRequest") {
	"""银行编码集合"""
	codeList:[String]
	"""银行id集合"""
	idList:[String]
	"""父级id"""
	parentId:String
	"""银行类型|对应常量BankTypeConstant
		工商银行:ICBC_BANK
		@see BankTypeConstant
	"""
	bankType:String
}
"""@Description 获取单个业务数据字典信息的查询
	<AUTHOR>
	@Date 2022/9/27 9:33
	@Version 1.0
"""
input BusinessDataDictionaryCodeRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.dictionary.BusinessDataDictionaryCodeRequest") {
	"""字典编码"""
	code:Int
	"""业务数据字典类型（必填项）
		DEGREE("学位", "DEGREE"),
		EDUCATION_BACKGROUND("学历", "EDUCATION_BACKGROUND"),
		ENTERPRISE_ECONOMIC_TYPE("企业经济类型", "ENTERPRISE_ECONOMIC_TYPE"),
		ENTERPRISE_TYPE("企业类型", "ENTERPRISE_TYPE"),
		EXECUTIVES_UNIT_TYPE("主管单位类型", "EXECUTIVES_UNIT_TYPE"),
		GENDER("性别", "GENDER"),
		HOUSEHOLD_REGISTRATION_TYPE("户口性质", "HOUSEHOLD_REGISTRATION_TYPE"),
		ID_CARD_TYPE("证件类型", "ID_CARD_TYPE"),
		INDUSTRY_EXECUTIVES_TYPE("行业主管类型", "INDUSTRY_EXECUTIVES_TYPE"),
		INDUSTRY_TYPE("行业类型", "INDUSTRY_TYPE"),
		NATIONALITY("民族", "NATIONALITY"),
		PERSON_TYPE("人员类型", "PERSON_TYPE"),
		PERSON_TYPE_GROUP("人员类型分组", "PERSON_TYPE_GROUP"),
		POLITICS_STATUS("政治面貌", "POLITICS_STATUS"),
		USER_REGISTER_TYPE("注册方式", "USER_REGISTER_TYPE"),
		USER_SOURCE_TYPE("注册来源", "USER_SOURCE_TYPE");
		@see BusinessDataDictionaryTypeEnum
	"""
	businessDataDictionaryType:String
}
"""业务数据字典查询"""
input BusinessDataDictionaryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.dictionary.BusinessDataDictionaryRequest") {
	"""字典编码"""
	code:Int
	"""业务数据字典id集合"""
	idList:[String]
	"""父级业务数据字典id"""
	parentId:String
	"""业务数据字典类型（必填项）
		DEGREE("学位", "DEGREE"),
		EDUCATION_BACKGROUND("学历", "EDUCATION_BACKGROUND"),
		ENTERPRISE_ECONOMIC_TYPE("企业经济类型", "ENTERPRISE_ECONOMIC_TYPE"),
		ENTERPRISE_TYPE("企业类型", "ENTERPRISE_TYPE"),
		EXECUTIVES_UNIT_TYPE("主管单位类型", "EXECUTIVES_UNIT_TYPE"),
		GENDER("性别", "GENDER"),
		HOUSEHOLD_REGISTRATION_TYPE("户口性质", "HOUSEHOLD_REGISTRATION_TYPE"),
		ID_CARD_TYPE("证件类型", "ID_CARD_TYPE"),
		INDUSTRY_EXECUTIVES_TYPE("行业主管类型", "INDUSTRY_EXECUTIVES_TYPE"),
		INDUSTRY_TYPE("行业类型", "INDUSTRY_TYPE"),
		NATIONALITY("民族", "NATIONALITY"),
		PERSON_TYPE("人员类型", "PERSON_TYPE"),
		PERSON_TYPE_GROUP("人员类型分组", "PERSON_TYPE_GROUP"),
		POLITICS_STATUS("政治面貌", "POLITICS_STATUS"),
		USER_REGISTER_TYPE("注册方式", "USER_REGISTER_TYPE"),
		USER_SOURCE_TYPE("注册来源", "USER_SOURCE_TYPE");
		@see BusinessDataDictionaryTypeEnum
	"""
	businessDataDictionaryType:String
	"""业务数据字典业务配置id
		PERSON_TYPE_EMPLOYED_PERSON("就业人员", "PERSON_TYPE_EMPLOYED_PERSON"),
		PERSON_TYPE_APPRENTICESHIP_SUBDIVISION_ENTERPRISE_WORKERS("学徒制细分企业职工", "PERSON_TYPE_APPRENTICESHIP_SUBDIVISION_ENTERPRISE_WORKERS"),
		PERSON_TYPE_UNEMPLOYED_PERSON("失业人员", "PERSON_TYPE_UNEMPLOYED_PERSON"),
		PERSON_TYPE_GRADUATE("毕业生", "PERSON_TYPE_GRADUATE"),
		PERSON_TYPE_OVERCOME_POVERTY_PERSON("脱贫人员", "PERSON_TYPE_OVERCOME_POVERTY_PERSON"),
		PERSON_TYPE_ELSE("其他", "PERSON_TYPE_ELSE");
	"""
	businessId:String
}
"""@Description 地区code集合查询条件
	<AUTHOR>
	@Date 2022/9/27 10:57
	@Version 1.0
"""
input RegionCodeListRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.dictionary.RegionCodeListRequest") {
	"""地区字典业务配置id"""
	businessId:String
	"""地区编码集合"""
	codeList:[String]
}
"""@Description 地区code查询
	<AUTHOR>
	@Date 2022/9/27 10:44
	@Version 1.0
"""
input RegionCodeRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.dictionary.RegionCodeRequest") {
	"""地区字典业务配置id"""
	businessId:String
	"""地区编码"""
	code:String
}
input RegionListRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.dictionary.RegionListRequest") {
	"""地区字典业务配置id"""
	businessId:String
	"""地区路径"""
	codePath:String
	"""地区编码路径查询匹配模式 0:全匹配 1:模糊查询 2:左模糊 3:右模糊, 默认全等"""
	codeMatchType:Int
}
"""地区查询"""
input RegionRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.dictionary.RegionRequest") {
	"""地区字典业务配置id"""
	businessId:String
	"""地区编码"""
	code:String
	"""级别|1省级 2市级 3区县级"""
	level:Int
}
"""简略资讯查询条件"""
input NewsCompleteQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.news.NewsCompleteQueryRequest") {
	"""资讯标题"""
	title:String
	"""资讯分类编号"""
	necId:String
	"""顶级分类代码"""
	rootCategoryCode:String
	"""弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]"""
	popUpsStatus:Int!
	"""资讯状态 [-1-全部，0-草稿，1-发布 默认全部]"""
	status:Int!
}
"""简略资讯查询条件"""
input NewsSimpleQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.news.NewsSimpleQueryRequest") {
	"""资讯标题"""
	title:String
	"""资讯分类编号"""
	necId:String
	"""发布地区编码"""
	areaCodePath:String
	"""弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]"""
	popUpsStatus:Int!
	"""资讯状态 [-1-全部，0-草稿，1-发布 默认全部]"""
	status:Int!
	"""排序
		默认按照从置顶到非置顶，发布时间从新到旧顺序，发布地区编码降序排列
	"""
	order:[OrderRequest]
}
input OrderRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.news.nested.OrderRequest") {
	"""排序字段
		发布时间：0
		浏览数量：1
		置顶(0-不置顶 1-置顶)：   2
		发布地区编码: 3
	"""
	orderField:Int
	"""排序方式  0 升序   1 降序"""
	orderType:Int
}
"""功能描述：学员集体缴费信息
	@Author： wtl
	@Date： 2022年4月21日 08:58:49
"""
input CollectiveRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.student.CollectiveRequest") {
	"""集体缴费管理员用户id集合"""
	collectiveUserIdList:[String]
}
"""功能描述：学员查询条件
	@Author： wtl
	@Date： 2022年1月26日 10:10:33
"""
input UserInfoRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.student.UserInfoRequest") {
	"""工作单位名称（模糊）"""
	companyName:String
	"""单位所属地区路径集合（模糊，右like）"""
	regionPathList:[String]
	"""单位所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	regionPathListMatchType:Int
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	phoneMatchType:Int
}
"""功能描述：学员查询条件
	@Author： wtl
	@Date： 2022年1月26日 10:10:33
"""
input UserQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.student.UserQueryRequest") {
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:UserInfoRequest
	"""用户认证信息"""
	authentication:AuthenticationRequest
	"""集体缴费信息"""
	collective:CollectiveRequest
	"""排序"""
	sortList:[UserSortRequest]
}
"""功能描述 : 学员排序参数
	@date : 2022/4/1 17:15
"""
input UserSortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.student.UserSortRequest") {
	"""排序字段"""
	sortField:PersonAccountSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
"""时间纬度下单位数量统计查询条件
	@date 2022-07-27
"""
input EnterpriseUnitDateHistogramRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.unit.EnterpriseUnitDateHistogramRequest") {
	"""时间纬度查询条件"""
	dateHistogram:DateHistogramRequest
	"""企业单位基本信息"""
	unitBase:EnterpriseUnitBaseRequest
}
"""功能描述：企业单位查询条件
	@Author： wtl
	@Date： 2022年6月9日 11:56:47
"""
input EnterpriseUnitRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.unit.EnterpriseUnitRequest") {
	"""企业单位基本信息"""
	unitBase:EnterpriseUnitBaseRequest
	"""排序集合"""
	sortList:[EnterpriseUnitSortRequest]
}
"""企业单位统计查询条件
	<AUTHOR>
	@date 2022-07-23
"""
input EnterpriseUnitStatisticRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.unit.EnterpriseUnitStatisticRequest") {
	"""企业单位基本信息"""
	unitBase:EnterpriseUnitBaseRequest
}
"""功能描述：政府单位查询条件
	@Author： wtl
	@Date： 2022年6月9日 10:24:09
"""
input GovernmentUnitRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.unit.GovernmentUnitRequest") {
	"""基本归属信息"""
	owner:OwnerRequest
	"""单位业务归属信息"""
	businessOwner:GovernmentUnitOwnerRequest
	"""单位基本信息"""
	unitBase:GovernmentUnitBaseRequest
	"""排序集合"""
	sortList:[GovernmentUnitSortRequest]
}
"""功能描述：评价机构查询条件
	@Author： wtl
	@Date： 2022年6月9日 11:56:47
"""
input RatingAgenciesUnitRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.unit.RatingAgenciesUnitRequest") {
	"""企业单位基本信息"""
	unitBase:RatingAgenciesUnitBaseRequest
}
"""功能描述：企业单位基本查询条件
	@Author： wtl
	@Date： 2022年6月9日 10:24:09
"""
input EnterpriseUnitBaseRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.unit.nested.EnterpriseUnitBaseRequest") {
	"""单位id集合"""
	unitIdList:[String]
	"""单位名称"""
	unitName:String
	"""单位名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	unitNameMatchType:Int
	"""单位简称"""
	shortName:String
	"""单位简称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	shortNameMatchType:Int
	"""统一社会信用代码"""
	code:String
	"""单位业务类型
		说明：
		1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
		11地区管理单位,12行业主管单位,13技工院校,14职业院校.15线上培训机构,10000实名制报表补贴单位,10001评价机构
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
		@see UnitBusinessQueryTypes
	"""
	businessTypeList:[Int]
	"""单位类型id路径集合"""
	unitTypeIdPathList:[String]
	"""单位类型id路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*unitTypeIdPathList* 2：左模糊查询，*unitTypeIdPathList 3:右模糊查询，unitTypeIdPathList*)
		@see MatchTypeConstant
	"""
	unitTypeIdPathListMatchType:Int
	"""单位地区路径集合"""
	regionPathList:[String]
	"""单位地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	regionPathListMatchType:Int
	"""单位创建时间范围"""
	createdDateScope:DateScopeRequest
	"""企业法人"""
	legalPerson:LegalPersonRequest
	"""单位状态
		说明：1正常,2冻结
	"""
	statusList:[Int]
}
"""企业单位排序
	<AUTHOR>
	@date 2022-06-18
"""
input EnterpriseUnitSortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.unit.nested.EnterpriseUnitSortRequest") {
	"""排序字段"""
	sortField:EnterpriseUnitSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
"""功能描述：政府单位基本查询条件
	@Author： wtl
	@Date： 2022年6月9日 10:24:09
"""
input GovernmentUnitBaseRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.unit.nested.GovernmentUnitBaseRequest") {
	"""单位id集合"""
	unitIdList:[String]
	"""单位名称"""
	unitName:String
	"""单位名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	unitNameMatchType:Int
	"""单位简称"""
	shortName:String
	"""单位简称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	shortNameMatchType:Int
	"""单位业务类型
		说明：
		1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
		11地区管理单位,12行业主管单位
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
	"""
	businessTypeList:[Int]
	"""所属地区路径（行政区划）"""
	regionPathList:[String]
	"""所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	regionPathListMatchType:Int
	"""管辖地区路径"""
	manageRegionPathList:[String]
	"""管辖地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	manageRegionPathListMatchType:Int
	"""创建时间范围"""
	createTimeScope:DateScopeRequest
	"""单位状态
		说明：1正常,2冻结
	"""
	statusList:[Int]
}
"""功能描述：政府单位归属查询条件
	@Author： wtl
	@Date： 2022年6月9日 12:08:14
"""
input GovernmentUnitOwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.unit.nested.GovernmentUnitOwnerRequest") {
	"""单位id路径集合"""
	unitIdPathList:[String]
	"""单位id路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	unitIdPathMatchType:Int
}
"""政府单位排序
	<AUTHOR>
	@date 2022-06-18
"""
input GovernmentUnitSortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.unit.nested.GovernmentUnitSortRequest") {
	"""排序字段"""
	sortField:GovernmentUnitSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
"""功能描述：企业法人查询条件
	@Author： wtl
	@Date： 2022年6月9日 12:08:14
"""
input LegalPersonRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.unit.nested.LegalPersonRequest") {
	"""法人姓名"""
	name:String
	"""法人姓名匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	nameMatchType:Int
	"""证件号"""
	idCard:String
	"""证件号匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	idCardMatchType:Int
}
"""基本归属信息"""
input OwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.unit.nested.OwnerRequest") {
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
}
"""功能描述：企业单位基本查询条件
	@Author： wtl
	@Date： 2022年6月9日 10:24:09
"""
input RatingAgenciesUnitBaseRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.unit.nested.RatingAgenciesUnitBaseRequest") {
	"""单位id集合"""
	unitIdList:[String]
	"""单位名称"""
	unitName:String
	"""单位名称匹配方式，默认为equals(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	unitNameMatchType:Int
	"""单位简称"""
	shortName:String
	"""单位简称匹配方式，默认为equals(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	shortNameMatchType:Int
	"""统一社会信用代码"""
	code:String
	"""单位地区路径集合"""
	regionPathList:[String]
	"""单位地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	regionPathListMatchType:Int
	"""单位创建时间范围"""
	createdDateScope:DateScopeRequest
	"""评价机构类型
		SOCIAL_EVALUATION_ORGANIZATION:社会评价组织机构
		EMPLOYER:用人单位
		@see RatingAgenciesType
	"""
	ratingAgenciesTypeList:[String]
}
input DateScopeRequest @type(value:"com.fjhb.ms.basicdata.repository.DateScopeRequest") {
	beginTime:DateTime
	endTime:DateTime
}
enum SortTypeEnum @type(value:"com.fjhb.ms.basicdata.enums.SortTypeEnum") {
	ASC
	DESC
}
enum TimeUnitEnum @type(value:"com.fjhb.ms.basicdata.enums.TimeUnitEnum") {
	YEARS
	MONTHS
	DAYS
	HOURS
}
type DateHistogramItemModel @type(value:"com.fjhb.ms.basicdata.model.DateHistogramItemModel") {
	date:DateTime
	count:Long!
}
type RegionModel @type(value:"com.fjhb.ms.basicdata.model.RegionModel") {
	regionId:String
	regionPath:String
	provinceId:String
	provinceName:String
	cityId:String
	cityName:String
	countyId:String
	countyName:String
}
"""功能描述：账户信息
	@Author： wtl
	@Date： 2022年5月11日 15:30:56
"""
type AccountResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.AccountResponse") {
	"""账户id"""
	accountId:String
	"""帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
		@see AccountTypes
	"""
	accountType:Int
	"""单位信息"""
	unitInfo:UnitInfoResponse
	"""所属顶级企业帐户Id"""
	rootAccountId:String
	"""帐户状态 1：正常，2：冻结，3：注销
		@see AccountStatus
	"""
	status:Int
	"""注册方式
		0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
		@see AccountRegisterTypes
	"""
	registerType:Int
	"""来源类型
		0内置，1项目主网站，2安卓，3IOS
		@see AccountSourceTypes
	"""
	sourceType:Int
	"""创建时间"""
	createdTime:DateTime
	"""最后更新时间"""
	lastUpdateTime:DateTime
}
"""功能描述：帐户认证信息
	@Author： wtl
	@Date： 2022年5月11日 14:23:18
"""
type AuthenticationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.AuthenticationResponse") {
	"""帐号"""
	identity:String
	"""认证标识类型
		1用户名,2手机,3身份证,4邮箱,5第三方OpenId
	"""
	identityType:Int
	"""认证方式状态 1启用，2禁用
		@see AuthenticationStatusEnum
	"""
	status:Int
}
"""功能描述：时间直方图统计结果
	@Author： wtl
	@Date： 2021/12/30 9:58
"""
type DateHistogramResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.DateHistogramResponse") {
	"""时间单位"""
	timeUnit:TimeUnitEnum
	"""统计结果元素"""
	histogram:[DateHistogramItemModel]
	"""总计"""
	totalCount:Long!
}
"""功能描述：角色信息
	@Author： wtl
	@Date： 2022/1/24 20:17
"""
type RoleResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.RoleResponse") {
	"""角色id"""
	roleId:String
	"""角色名称"""
	roleName:String
	"""角色类型
		（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
		@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
	"""
	roleType:String
	"""角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
		@see RoleCategories
	"""
	roleCategory:Int
}
"""功能描述 : 企业单位管理员信息
	@date : 2022/6/18 12:24
"""
type EnterpriseUnitAdminInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse") {
	"""企业单位管理员归属信息"""
	enterpriseUnitAdminOwner:EnterpriseUnitAdminOwnerResponse
	"""账户信息"""
	accountInfo:AccountResponse
	"""管理员用户信息"""
	userInfo:AdminUserInfoResponse
	"""人员信息"""
	personInfo:EnterpriseUnitPersonInfoResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
	"""角色信息集合"""
	roleList:[RoleResponse]
}
"""功能描述 : 政府单位管理员信息
	@date : 2022/6/18 12:24
"""
type GovernmentUnitAdminInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.GovernmentUnitAdminInfoResponse") {
	"""政府单位管理员归属信息"""
	governmentUnitAdminOwner:GovernmentUnitAdminOwnerResponse
	"""账户信息"""
	accountInfo:AccountResponse
	"""管理员用户信息"""
	userInfo:AdminUserInfoResponse
	"""人员信息"""
	personInfo:PersonInfoBasicResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
	"""角色信息集合"""
	roleList:[RoleResponse]
}
"""功能描述：管理员用户信息
	@Author： wtl
	@Date： 2022年1月25日 15:48:48
"""
type AdminUserInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.nested.AdminUserInfoResponse") {
	"""管辖地区集合"""
	manageRegionList:[RegionModel]
	"""办公室（所在处/科室）"""
	office:String
	"""岗位/职位"""
	position:String
	"""备注"""
	remark:String
	"""用户id"""
	userId:String
	"""用户名称"""
	userName:String
	"""性别
		-1未知，0女，1男
		@see Genders
	"""
	gender:Int
	"""证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）"""
	idCardType:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""邮箱"""
	email:String
}
"""功能描述 : 企业单位管理员归属查询条件
	@date : 2022年9月2日 10:53:02
"""
type EnterpriseUnitAdminOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.nested.EnterpriseUnitAdminOwnerResponse") {
	"""企业单位id"""
	enterpriseUnitIdList:[String]
}
"""人员信息模型"""
type EnterpriseUnitPersonInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.nested.EnterpriseUnitPersonInfoResponse") {
	"""是否法人帐号"""
	isCorporateAccount:Boolean
	"""人员实名认证信息"""
	personIdentityVerificationInfo:PersonIdentityVerificationResponse
}
"""功能描述 : 政府单位管理员归属查询条件
	@date : 2022年8月30日 08:41:13
"""
type GovernmentUnitAdminOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.nested.GovernmentUnitAdminOwnerResponse") {
	"""政府单位id"""
	governmentUnitIdList:[String]
}
"""人员实名认证信息模型"""
type PersonIdentityVerificationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.nested.PersonIdentityVerificationResponse") {
	"""是否已认证"""
	identityVerification:Boolean
	"""认证渠道(1:闽政通 2：腾讯)"""
	identityVerificationChannel:Int
	"""认证时间"""
	identityVerificationTime:DateTime
}
"""人员信息模型"""
type PersonInfoBasicResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.nested.PersonInfoBasicResponse") {
	"""人员实名认证信息"""
	personIdentityVerificationInfo:PersonIdentityVerificationResponse
}
"""单位信息模型"""
type UnitInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.nested.UnitInfoResponse") {
	"""单位ID"""
	unitId:String
}
"""@Description 银行字典信息
	<AUTHOR>
	@Date 2022/9/28 11:57
	@Version 1.0
"""
type BankResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.BankResponse") {
	"""银行id"""
	id:String
	"""银行类型"""
	type:String
	"""父级ID"""
	parentId:String
	"""银行编码"""
	code:String
	"""银行名称"""
	name:String
	"""是否可用（0：禁用 1：启用）"""
	available:Int
	"""排序"""
	sort:Int
}
"""功能描述：业务数据字典信息
	@Author： wtl
	@Date： 2022年8月12日 17:25:23
"""
type BusinessDataDictionaryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.BusinessDataDictionaryResponse") {
	"""字典id"""
	id:String
	"""字典类型"""
	type:String
	"""父级字典id"""
	parentId:String
	"""字典编码"""
	code:Int
	"""字典名称"""
	name:String
	"""是否可用（0：禁用 1：启用）"""
	available:Int
	"""排序"""
	sort:Int
}
"""功能描述：地区字典信息
	@Author： wtl
	@Date： 2022年8月12日 11:54:38
"""
type RegionResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.dictionary.RegionResponse") {
	"""地区编码"""
	code:String
	"""父级地区编码"""
	parentCode:String
	"""地区编码路径"""
	codePath:String
	"""地区名称"""
	name:String
	"""级别|1省级 2市级 3区县级"""
	level:Int
	"""地区排序"""
	sort:Int
}
"""资讯分类信息"""
type NewsCategoryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.news.NewsCategoryResponse") {
	"""资讯分类编号"""
	newsCategoryId:String
	"""分类名称"""
	categoryName:String
	"""分类代码"""
	code:String
}
"""简略资讯信息"""
type NewsCompleteResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.news.NewsCompleteResponse") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""所属业务平台"""
	businessPlatform:String
	"""分类名称"""
	name:String
	"""资讯状态 0 草稿 1正常"""
	status:Int!
	"""发布时间"""
	publishTime:DateTime
	"""分类id"""
	necId:String
}
"""详细资讯信息"""
type NewsDetailResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.news.NewsDetailResponse") {
	"""资讯编号"""
	newId:String
	"""平台id"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商id"""
	serviceId:String
	"""分类id"""
	necId:String
	"""标题"""
	title:String
	"""摘要"""
	summary:String
	"""内容"""
	content:String
	"""封面图片路径"""
	coverPath:String
	"""来源"""
	source:String
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""是否置顶"""
	isTop:Boolean!
	"""发布地区编码"""
	areaCodePath:String
	"""资讯状态 0 草稿 1正常"""
	status:Int!
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""浏览数量"""
	reviewCount:Int!
	"""弹窗起始时间"""
	popupBeginTime:DateTime
	"""弹窗截止时间"""
	popupEndTime:DateTime
}
"""简略资讯信息"""
type NewsSimpleResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.news.NewsSimpleResponse") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""是否置顶"""
	isTop:Boolean!
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""摘要"""
	summary:String
	"""分类名称"""
	name:String
	"""资讯状态 0 草稿 1正常"""
	status:Int!
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""发布地区编码"""
	areaCodePath:String
}
"""功能描述：学员信息
	@Author： wtl
	@Date： 2022年1月26日 10:38:15
"""
type UserInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.UserInfoResponse") {
	"""账户信息"""
	accountInfo:AccountResponse
	"""用户信息"""
	userInfo:UserInformationResponse
	"""人员信息"""
	personInfo:PersonInfoResponse
	"""第三方绑定信息"""
	openPlatformBind:OpenPlatformBindResponse
}
"""功能描述：附件信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type AttachmentInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.AttachmentInfoResponse") {
	"""附件名称"""
	name:String
	"""附件地址"""
	url:String
}
"""功能描述：学员绑定信息
	@Author： wtl
	@Date： 2022年5月12日 14:42:51
"""
type OpenPlatformBindResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.OpenPlatformBindResponse") {
	"""是否绑定微信"""
	bindWX:Boolean!
	"""微信昵称"""
	nickNameByWX:String
}
type PersonInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.PersonInfoResponse") {
	"""人员ID"""
	personId:String
	"""姓名"""
	name:String
	"""证件类型
		@see IdCardTypes
	"""
	idCardType:Int
	"""身份证号"""
	idCard:String
	"""性别
		@see Genders
	"""
	gender:Int
	"""出生日期"""
	birthday:DateTime
	"""民族（字典）"""
	ethnicity:String
	"""居住地区"""
	resideCityArea:RegionResponse1
	"""联系地址"""
	address:String
	"""手机号"""
	phone:String
}
type RegionResponse1 @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.RegionResponse") {
	"""地区ID"""
	regionId:String
	"""地区路径"""
	regionPath:String
	"""省份ID"""
	provinceId:String
	"""省份名称"""
	provinceName:String
	"""地市ID"""
	cityId:String
	"""地市名称"""
	cityName:String
	"""区县ID"""
	countyId:String
	"""区县名称"""
	countyName:String
}
"""功能描述：学员证书信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type StudentCertificateResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.StudentCertificateResponse") {
	"""证书id"""
	certificateId:String
	"""证书编号"""
	certificateNo:String
	"""证书类别"""
	certificateCategory:String
	"""注册专业"""
	registerProfessional:String
	"""发证日期"""
	releaseStartTime:DateTime
	"""证书有效期"""
	certificateEndTime:DateTime
	"""证书附件信息"""
	attachmentList:[AttachmentInfoResponse]
}
"""功能描述：学员行业信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type StudentIndustryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.StudentIndustryResponse") {
	"""用户行业id"""
	userIndustryId:String
	"""行业id"""
	industryId:String
	"""一级专业类别id"""
	firstProfessionalCategory:String
	"""二级专业类别id"""
	secondProfessionalCategory:String
	"""职称等级"""
	professionalQualification:String
	"""学员证书信息集合"""
	userCertificateList:[StudentCertificateResponse]
}
"""功能描述：用户信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type UserInformationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.UserInformationResponse") {
	"""用户昵称"""
	nickName:String
	"""单位所属地区"""
	region:RegionModel
	"""工作单位名称"""
	companyName:String
	"""头像地址"""
	photo:String
	"""联系地址"""
	address:String
	"""学员行业信息集合"""
	userIndustryList:[StudentIndustryResponse]
	"""用户id"""
	userId:String
	"""用户名称"""
	userName:String
	"""性别
		-1未知，0女，1男
		@see Genders
	"""
	gender:Int
	"""证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）"""
	idCardType:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""邮箱"""
	email:String
}
"""行业信息下-企业单位统计
	<AUTHOR>
	@date 2022-07-25
"""
type EnterpriseUnitIndustryStatisticResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitIndustryStatisticResponse") {
	"""行业类型id，可能是门类id、大类id、中类id、小类id"""
	industryId:String
	"""企业单位统计结果"""
	statisticInfo:EnterpriseUnitStatisticResponse
}
"""功能描述：企业单位信息
	@Author： wtl
	@Date： 2022年6月9日 14:20:55
"""
type EnterpriseUnitInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse") {
	"""企业单位业务归属信息"""
	businessOwnerInfo:EnterpriseUnitBusinessOwnerResponse
	"""单位基本信息"""
	unitBase:EnterpriseUnitBaseResponse
	"""经营信息"""
	businessInfo:BusinessInfoResponse
	"""单位认证信息"""
	unitIdentityVerificationInfo:UnitIdentityVerificationResponse
}
"""行业信息下-企业单位统计
	<AUTHOR>
	@date 2022-07-25
"""
type EnterpriseUnitRegionStatisticResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitRegionStatisticResponse") {
	"""注册地区"""
	region:RegionModel
	"""企业单位统计结果"""
	statisticInfo:EnterpriseUnitStatisticResponse
}
"""单位类型下-企业单位统计
	<AUTHOR>
	@date 2022-07-25
"""
type EnterpriseUnitTypeStatisticResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitTypeStatisticResponse") {
	"""单位类型id,可能是一级id、二级id、三级id"""
	unitTypeId:String
	"""企业单位统计结果"""
	statisticInfo:EnterpriseUnitStatisticResponse
}
"""功能描述：政府单位信息
	@Author： wtl
	@Date： 2022年6月9日 10:41:26
"""
type GovernmentUnitInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.GovernmentUnitInfoResponse") {
	"""单位归属信息"""
	ownerInfo:GovernmentUnitOwnerResponse
	"""单位业务归属信息"""
	businessOwnerInfo:GovernmentUnitBusinessOwnerResponse
	"""政府单位基本信息"""
	unitBase:GovernmentUnitBaseResponse
	"""政策许可证（业务模块）"""
	licenseIssuedList:[GovernmentUnitLicenseIssuedResponse]
}
"""功能描述：评价机构信息
	@Author： wtl
	@Date： 2022年6月9日 14:20:55
"""
type RatingAgenciesUnitInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.RatingAgenciesUnitInfoResponse") {
	"""单位基本信息"""
	unitBase:RatingAgenciesUnitBaseResponse
}
"""功能描述：附件信息"""
type AttachmentResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.AttachmentResponse") {
	"""附件id"""
	attachmentId:String
	"""附件类型
		1《营业执照 / 民办法人登记证书/非民办企业法人登记证书》
		2《开展短期职业培训承诺书》（加盖公司公章）》
		3《培训单位基本信息表》（加盖公司公章）》
		4《培训单位办学许可证》（加盖公司公章）》
		5《年度年审合格证书》（加盖公司公章）》
		6 开通依据
	"""
	attachmentType:Int
	"""附件名称"""
	attachmentName:String
	"""附件路径"""
	attachmentPath:String
	"""创建时间"""
	createdTime:DateTime
}
"""功能描述：企业经营信息
	@Author： wtl
	@Date： 2022年6月9日 14:31:03
"""
type BusinessInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.BusinessInfoResponse") {
	"""营业期限起始日期"""
	operatingBeginDate:DateTime
	"""营业期限截止日期"""
	operatingEndDZhjyEnterpriseUnitBackStageQueryResolverate:DateTime
	"""行业信息"""
	industry:IndustryResponse
	"""经营范围,例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。"""
	businessScope:String
	"""主营业务
		例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。
	"""
	mainBusiness:String
}
"""功能描述：单位联系人信息"""
type ContactPersonInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.ContactPersonInfoResponse") {
	"""联系人"""
	contact:String
	"""联系电话"""
	contactPhone:String
}
"""功能描述：企业单位信息
	@Author： wtl
	@Date： 2022年6月9日 14:23:04
"""
type EnterpriseUnitBaseResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.EnterpriseUnitBaseResponse") {
	"""单位ID"""
	unitId:String
	"""单位名称"""
	unitName:String
	"""单位英文名称"""
	enName:String
	"""统一社会信用代码"""
	code:String
	"""单位简称"""
	shortName:String
	"""单位业务类型
		说明：
		1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
		11地区管理单位,12行业主管单位,13技工院校,14职业院校.15线上培训机构,10000实名制报表补贴单位,10001评价机构
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
		@see UnitBusinessQueryTypes
	"""
	businessType:Int
	"""logo"""
	logo:String
	"""法人信息"""
	legalPersonInfo:LegalPersonResponse
	"""单位类型（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）"""
	unitType:UnitTypeResponse
	"""单位规模（1：微型 2：小型 3：中型 4：大型）
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitScales
	"""
	scale:Int
	"""企业经济类型(国有经济、联营经济、私营企业、股份制、港澳台投资、外商投资、其他经济)
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitEconomicTypes
	"""
	economicTypes:Int
	"""产业类别"""
	industrialCategory:String
	"""成立日期"""
	foundedDate:DateTime
	"""联系电话"""
	phone:String
	"""邮政编码"""
	postCode:String
	"""传真"""
	faxNumber:String
	"""注册地区"""
	region:RegionModel
	"""联系地址"""
	address:String
	"""注册地址"""
	registerAddress:String
	"""登记机关"""
	registeredOrgan:String
	"""注册资金"""
	registeredCapital:String
	"""创建时间"""
	createdTime:DateTime
	"""单位状态
		说明：1正常,2冻结
	"""
	status:Int
	"""工商注册号"""
	businessRegistrationNumber:String
	"""纳税人资质"""
	taxpayerQualification:String
	"""邮箱地址"""
	emailAddress:String
	"""联系人信息"""
	contactPersonInfo:ContactPersonInfoResponse
	"""单位资质附件类型"""
	attachmentList:[AttachmentResponse]
}
"""企业单位业务归属信息"""
type EnterpriseUnitBusinessOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.EnterpriseUnitBusinessOwnerResponse") {
	"""企业归属信息路径
		单位路径（若单位为福州市企业，则该值为:"/福建省企业id/福州市企业id"）
	"""
	unitIdPath:String
}
"""企业单位统计结果
	<AUTHOR>
	@date 2022-07-25
"""
type EnterpriseUnitStatisticResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.EnterpriseUnitStatisticResponse") {
	"""企业单位数量统计"""
	enterpriseUnitCount:Long!
}
"""人社许可权限信息"""
type GovernmentUnitAuthorizationInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.GovernmentUnitAuthorizationInfoResponse") {
	"""权限描述"""
	desc:String
	"""权限授予角色列表"""
	authorizeRoles:[String]
}
"""功能描述：政府单位信息
	@Author： wtl
	@Date： 2022年6月9日 10:41:26
"""
type GovernmentUnitBaseResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.GovernmentUnitBaseResponse") {
	"""单位id"""
	unitId:String
	"""单位名称"""
	unitName:String
	"""单位简称"""
	shortName:String
	"""单位业务类型
		说明：
		1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
		11地区管理单位,12行业主管单位
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
	"""
	businessType:Int
	"""电话"""
	phone:String
	"""传真"""
	faxNumber:String
	"""所属地区"""
	region:RegionModel
	"""地址"""
	address:String
	"""管辖地区"""
	manageRegion:RegionModel
	"""创建时间"""
	createdTime:DateTime
	"""单位状态
		说明：1正常,2冻结
	"""
	status:Int
	"""单位类别（行业主管类型）
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitCategories
	"""
	category:Int
	"""单位资质附件类型"""
	attachmentList:[AttachmentResponse]
}
"""功能描述：政府单位业务归属信息
	@Author： wtl
	@Date： 2022年6月24日 19:11:30
"""
type GovernmentUnitBusinessOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.GovernmentUnitBusinessOwnerResponse") {
	"""单位路径（若单位为福州市人社，则该值为:"/福建省人社id/福州市人社id"）"""
	unitIdPath:String
}
"""功能描述：政府部门政策许可证"""
type GovernmentUnitLicenseIssuedResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.GovernmentUnitLicenseIssuedResponse") {
	"""许可证编号Id"""
	licenseId:String
	"""授予者id"""
	authorizationId:String
	"""授予者类型
		1单位
	"""
	authorizationType:Int
	"""单位业务类型
		说明：
		1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
		11地区管理单位,12行业主管单位
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
	"""
	businessType:Int
	"""政策类型(大类)"""
	policyType:String
	"""颁发时间"""
	issueTime:DateTime
	"""人社许可权限信息"""
	authorizationInfo:GovernmentUnitAuthorizationInfoResponse
	"""许可证状态
		0已回收,1已颁发,2已失效
	"""
	status:Int
	"""状态变更时间"""
	statusUpdateTime:DateTime
}
"""功能描述：政府单位信息
	@Author： wtl
	@Date： 2022年6月9日 10:41:26
"""
type GovernmentUnitOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.GovernmentUnitOwnerResponse") {
	"""上级单位id（若为顶级单位，该值为空）"""
	parentUnitId:String
}
"""行业信息
	<AUTHOR>
	@date 2022-06-18
"""
type IndustryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.IndustryResponse") {
	"""行业信息ID路径"""
	industryIdPath:String
	"""门类"""
	firstLevelIndustryId:String
	"""大类"""
	secondLevelIndustryId:String
	"""中类"""
	thirdLevelIndustryId:String
	"""小类"""
	fourthLevelIndustryId:String
}
"""功能描述：企业法人信息
	@Author： wtl
	@Date： 2022年6月9日 14:31:03
"""
type LegalPersonResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.LegalPersonResponse") {
	"""法定代表人"""
	legalPerson:String
	"""证件类型"""
	idCardType:String
	"""证件号"""
	idCard:String
}
"""功能描述：企业单位信息
	@Author： sjm
	@Date： 2023年5月8日 14:23:04
"""
type RatingAgenciesUnitBaseResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.RatingAgenciesUnitBaseResponse") {
	"""单位ID"""
	unitId:String
	"""单位名称"""
	unitName:String
	"""单位英文名称"""
	enName:String
	"""统一社会信用代码"""
	code:String
	"""联系电话"""
	phone:String
	"""注册地区"""
	region:RegionModel
	"""注册地址"""
	registerAddress:String
	"""创建时间"""
	createdTime:DateTime
	"""单位状态
		说明：1正常,2冻结
	"""
	status:Int
	"""工商注册号"""
	businessRegistrationNumber:String
	"""评价机构类型
		SOCIAL_EVALUATION_ORGANIZATION:社会评价组织机构
		EMPLOYER:用人单位
		@see RatingAgenciesType
	"""
	ratingAgenciesType:String
	"""所属机构"""
	belongInstitutions:String
}
"""单位认证信息模型"""
type UnitIdentityVerificationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.UnitIdentityVerificationResponse") {
	"""是否已认证"""
	identityVerification:Boolean
	"""认证渠道（单位认证渠道：UnitIdentityVerificationChannels 人员认证渠道：PersonIdentityVerificationChannels）
		@see PersonIdentityVerificationChannels
		@see UnitIdentityVerificationChannels
	"""
	identityVerificationChannel:Int
	"""认证时间"""
	identityVerificationTime:DateTime
}
"""单位类型:（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）
	<AUTHOR>
	@date : 2022/6/18 14:15
"""
type UnitTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.UnitTypeResponse") {
	"""单位类型ID路径"""
	unitTypeIdPath:String
	"""一级"""
	firstLevelUnitTypeId:String
	"""二级"""
	secondLevelUnitTypeId:String
	"""三级"""
	thirdLevelUnitTypeId:String
}
"""功能描述：管理员排序字段
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
enum EnterprisePersonSortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.EnterprisePersonSortFieldEnum") {
	"""创建时间"""
	createdTime
	"""账户类型"""
	accountType
	"""用户名称首字母"""
	userNameFirstLetter
	"""授予性质"""
	nature
}
"""功能描述：企业单位排序字段
	@Author： wtl
	@Date： 2022年5月12日 09:23:31
"""
enum EnterpriseUnitSortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.EnterpriseUnitSortFieldEnum") {
	"""创建时间"""
	createdTime
	"""单位名称"""
	unitName
	"""单位拼音名称"""
	pinyinName
	"""单位地区ID"""
	regionId
}
"""功能描述：政府单位排序字段
	@Author： wtl
	@Date： 2022年5月12日 09:23:31
"""
enum GovernmentUnitSortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.GovernmentUnitSortFieldEnum") {
	"""所属地区"""
	unitRegionPath
	"""单位名称"""
	unitName
	"""创建时间"""
	createdTime
}
"""功能描述：学员排序字段
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
enum PersonAccountSortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.PersonAccountSortFieldEnum") {
	"""创建时间"""
	createdTime
	"""授予性质"""
	nature
}

scalar List
type BankResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BankResponse]}
type BusinessDataDictionaryResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BusinessDataDictionaryResponse]}
type NewsCompleteResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [NewsCompleteResponse]}
type EnterpriseUnitAdminInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [EnterpriseUnitAdminInfoResponse]}
type EnterpriseUnitInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [EnterpriseUnitInfoResponse]}
type GovernmentUnitAdminInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [GovernmentUnitAdminInfoResponse]}
type GovernmentUnitInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [GovernmentUnitInfoResponse]}
type RatingAgenciesUnitInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [RatingAgenciesUnitInfoResponse]}
type NewsSimpleResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [NewsSimpleResponse]}
type UserInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserInfoResponse]}
