import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum LearningExperienceTypeEnum {
  // 班级心得
  class = 1,
  // 课程心得
  course = 2
}

export enum LearningExperienceAnswerTypeEnum {
  // 提交附件
  upload = 1,
  // 在线编辑
  edit = 2
}

export enum LearningExperienceActStatusEnum {
  // 未开始
  notStart = 0,
  // 进行中
  inProgress = 1,
  // 已结束
  Ended = 2
}

export enum LearningExperienceJoinStatusEnum {
  // 参加
  join = 1,
  // 未参加
  not_join = 2
}
export enum LearningExperienceApproveStatusEnum {
  // 待审核
  not_approved = 1,
  // 已审核
  approved = 2
}

export enum LearningExperienceApproveResultEnum {
  // 通过
  success = 1,
  // 不通过
  fail = 2
}

export enum ApproveMethodEnum {
  /**
   * 提交自动通过
   */
  AUTO = 1,
  /**
   * 人工审核
   */
  ARTIFICIAL = 2
}

export class LearningExperienceTypeType extends AbstractEnum<LearningExperienceTypeEnum> {
  static enum = LearningExperienceTypeEnum
  constructor(status?: LearningExperienceTypeEnum) {
    super()
    this.current = status
    this.map.set(LearningExperienceTypeEnum.class, '班级心得')
    this.map.set(LearningExperienceTypeEnum.course, '课程心得')
  }
}

export class LearningExperienceAnswerTypeType extends AbstractEnum<LearningExperienceAnswerTypeEnum> {
  static enum = LearningExperienceAnswerTypeEnum
  constructor(status?: LearningExperienceAnswerTypeEnum) {
    super()
    this.current = status
    this.map.set(LearningExperienceAnswerTypeEnum.upload, '提交附件')
    this.map.set(LearningExperienceAnswerTypeEnum.edit, '在线编辑')
  }
}

export class LearningExperienceActStatusType extends AbstractEnum<LearningExperienceActStatusEnum> {
  static enum = LearningExperienceActStatusEnum
  constructor(status?: LearningExperienceActStatusEnum) {
    super()
    this.current = status
    this.map.set(LearningExperienceActStatusEnum.notStart, '未开始')
    this.map.set(LearningExperienceActStatusEnum.inProgress, '进行中')
    this.map.set(LearningExperienceActStatusEnum.Ended, '已结束')
  }
}

export class LearningExperienceJoinStatusType extends AbstractEnum<LearningExperienceJoinStatusEnum> {
  static enum = LearningExperienceJoinStatusEnum
  constructor(status?: LearningExperienceJoinStatusEnum) {
    super()
    this.current = status
    this.map.set(LearningExperienceJoinStatusEnum.join, '参加')
    this.map.set(LearningExperienceJoinStatusEnum.not_join, '未参加')
  }
}

export class LearningExperienceApproveStatusType extends AbstractEnum<LearningExperienceApproveStatusEnum> {
  static enum = LearningExperienceApproveStatusEnum
  constructor(status?: LearningExperienceApproveStatusEnum) {
    super()
    this.current = status
    this.map.set(LearningExperienceApproveStatusEnum.not_approved, '待审核')
    this.map.set(LearningExperienceApproveStatusEnum.approved, '已审核')
  }
}

export class LearningExperienceApproveResultType extends AbstractEnum<LearningExperienceApproveResultEnum> {
  static enum = LearningExperienceApproveResultEnum
  constructor(status?: LearningExperienceApproveResultEnum) {
    super()
    this.current = status
    this.map.set(LearningExperienceApproveResultEnum.success, '通过')
    this.map.set(LearningExperienceApproveResultEnum.fail, '不通过')
  }
}

export class ApproveMethodType extends AbstractEnum<ApproveMethodEnum> {
  static enum = ApproveMethodEnum
  constructor(status?: ApproveMethodEnum) {
    super()
    this.current = status
    this.map.set(ApproveMethodEnum.ARTIFICIAL, '人工审核')
    this.map.set(ApproveMethodEnum.AUTO, '提交自动通过')
  }
}
