import findBatchOrderPage from './queries/findBatchOrderPage.graphql'
import findLastRecordByBatch from './queries/findLastRecordByBatch.graphql'
import findOrderListByBatchNo from './queries/findOrderListByBatchNo.graphql'
import getBatchOrder from './queries/getBatchOrder.graphql'
import getBillByOrderNo from './queries/getBillByOrderNo.graphql'
import getCollectiveOrderStatus from './queries/getCollectiveOrderStatus.graphql'
import getCollectiveRegisterRecord from './queries/getCollectiveRegisterRecord.graphql'
import getSignUpCountByBatchNos from './queries/getSignUpCountByBatchNos.graphql'
import preCommitCollectiveRegister from './queries/preCommitCollectiveRegister.graphql'
import cancelBatchOrder from './mutates/cancelBatchOrder.graphql'
import commitCollectiveRegister from './mutates/commitCollectiveRegister.graphql'
import createCollectiveRegister from './mutates/createCollectiveRegister.graphql'
import doDelCollectiveRegister from './mutates/doDelCollectiveRegister.graphql'
import removeCollectiveRegister from './mutates/removeCollectiveRegister.graphql'
import updateCollectiveRegister from './mutates/updateCollectiveRegister.graphql'

export {
  findBatchOrderPage,
  findLastRecordByBatch,
  findOrderListByBatchNo,
  getBatchOrder,
  getBillByOrderNo,
  getCollectiveOrderStatus,
  getCollectiveRegisterRecord,
  getSignUpCountByBatchNos,
  preCommitCollectiveRegister,
  cancelBatchOrder,
  commitCollectiveRegister,
  createCollectiveRegister,
  doDelCollectiveRegister,
  removeCollectiveRegister,
  updateCollectiveRegister
}
