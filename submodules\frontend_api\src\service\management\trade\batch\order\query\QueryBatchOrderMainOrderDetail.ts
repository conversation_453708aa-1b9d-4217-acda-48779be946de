import BatchOrderMainOrderDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderDetailVo'
import ExchangeOrderRecordDetailVo from '@api/service/management/train-class/query/vo/ExchangeOrderRecordDetailVo'
import msTradeQuery, {
  ExchangeOrderRequest,
  ExchangeOrderSortField,
  ExchangeOrderSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { Page } from '@hbfe/common'
import DataResolve from '@api/service/common/utils/DataResolve'
import UserModule from '@api/service/management/user/UserModule'
import { ExchangeGoodsStatusEnum } from '@api/service/common/enums/train-class/ExchangeGoodsStatusList'
/**
 * @description 【集体报名订单】主单详情
 */
class QueryBatchOrderMainOrderDetail {
  /**
   * 查询批次单主单详情
   * @param {string} mainOrderNo - 主单id
   * @param {string} batchOrderNo - 批次单id
   */
  async queryBatchOrderMainOrderDetail(
    batchOrderNo: string,
    mainOrderNo: string
  ): Promise<BatchOrderMainOrderDetailVo> {
    let result = new BatchOrderMainOrderDetailVo()
    if (!mainOrderNo) return result
    const response = await msTradeQuery.getOrderInServicer(mainOrderNo)
    if (response.status?.isSuccess()) {
      result = await BatchOrderMainOrderDetailVo.from(batchOrderNo, response.data)
    }
    return result
  }

  /**
   * 查询批次单主单详情（分销）
   * @param {string} mainOrderNo - 主单id
   * @param {string} batchOrderNo - 批次单id
   */
  async queryFxBatchOrderMainOrderDetail(
    batchOrderNo: string,
    mainOrderNo: string
  ): Promise<BatchOrderMainOrderDetailVo> {
    let result = new BatchOrderMainOrderDetailVo()
    if (!mainOrderNo) return result
    const response = await msTradeQuery.getOrderInDistributor(mainOrderNo)
    if (response.status?.isSuccess()) {
      result = await BatchOrderMainOrderDetailVo.from(batchOrderNo, response.data)
    }
    return result
  }

  /**
   * 查询关联换班信息
   * @param {string} subOrderNo - 子订单id
   */
  async queryRelativeExchangeRecord(subOrderNo: string, page: Page): Promise<ExchangeOrderRecordDetailVo[]> {
    let result = [] as ExchangeOrderRecordDetailVo[]
    const request = new ExchangeOrderRequest()
    request.subOrderNoList = [subOrderNo]
    const sort = new ExchangeOrderSortRequest()
    sort.field = ExchangeOrderSortField.APPLIED_TIME
    sort.policy = SortPolicy.ASC
    const response = await msTradeQuery.pageExchangeOrderInServicer({
      page,
      request,
      sort: [sort]
    })
    page.totalPageSize = response.data?.totalPageSize ?? 0
    page.totalSize = response.data?.totalSize ?? 0
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = await Promise.all(
        response.data.currentPageData.map(async item => {
          return await ExchangeOrderRecordDetailVo.from(item)
        })
      )
    }
    const userId = result.map(item => item.operatorId)
    const userMap = await this.fillCollectiveInfoForList(userId)
    result.forEach(item => {
      item.operatorName = userMap.get(item.operatorId)
    })
    return result
  }
  /**
   * 查询关联换班初始班级和最终班级
   * @param {string} subOrderNo - 子订单id
   */
  async queryRelativeExchange(subOrderNo: string) {
    const request = new ExchangeOrderRequest()
    request.subOrderNoList = [subOrderNo]
    const sort = new ExchangeOrderSortRequest()
    sort.field = ExchangeOrderSortField.APPLIED_TIME
    sort.policy = SortPolicy.ASC
    const response = await msTradeQuery.pageExchangeOrderInServicer({
      page: {
        pageNo: 1,
        pageSize: 1
      },
      request,
      sort: [sort]
    })
    const start = response.data.currentPageData[0] ? response.data.currentPageData[0].exchangeOrderNo : ''
    const sortLast = new ExchangeOrderSortRequest()
    sortLast.field = ExchangeOrderSortField.APPLIED_TIME
    sortLast.policy = SortPolicy.DESC
    const responseLast = await msTradeQuery.pageExchangeOrderInServicer({
      page: {
        pageNo: 1,
        pageSize: 1
      },
      request,
      sort: [sortLast]
    })
    const last = responseLast.data.currentPageData[0] ? responseLast.data.currentPageData[0].exchangeOrderNo : ''
    return { start, last }
  }
  /**
   * 填充集体缴费管理员信息
   */
  private async fillCollectiveInfoForList(userIds: string[]) {
    const queryRemote = UserModule.queryUserFactory.queryManager
    const response = await queryRemote.queryManager(userIds)

    const map = new Map<string, string>()
    response.data.forEach(item => {
      map.set(item.userId, item.userName)
    })
    return map
  }
}

export default QueryBatchOrderMainOrderDetail
