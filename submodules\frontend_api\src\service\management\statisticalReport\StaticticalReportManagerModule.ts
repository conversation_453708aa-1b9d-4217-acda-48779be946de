import store from '@/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import QueryStaticticalReportFactory from '@api/service/management/statisticalReport/QueryStaticticalReportFactory'
import MutationStaticticalReportFactory from '@api/service/management/statisticalReport/MutationStaticticalReportFactory'

/**
 * 培训班中控层
 */
@Module({
  name: 'StaticticalReportManagerModule',
  dynamic: true,
  namespaced: true,
  store
})
class StaticticalReportManagerModule extends VuexModule {
  // region properties

  /**
   *统计报表查询工厂类，QueryStaticticalReportFactory
   */
  queryStaticticalReportFactory = new QueryStaticticalReportFactory()
  /**
   *统计报表业务工厂类，MutationStaticticalReportFactory
   */
  mutationStaticticalReportFactory = new MutationStaticticalReportFactory()
  // endregion
  // region methods

  // endregion
}
export default getModule(StaticticalReportManagerModule)
