query getPracticeReport($statisticParam: UserPracticeStatisticParamDTO,$allCategoryCountParam: AnswerQuestionCountParamDTO,$frequencyParam:UserPracticeStatisticParamDTO,$favoriteParam:UserFavoriteQuestionStatisticParamDTO
,$baseStatisticParam: UserPracticeStatisticParamDTO,$baseFrequencyParam:UserPracticeStatisticParamDTO,$baseFavoriteParam:UserFavoriteQuestionStatisticParamDTO
,$toCompareStatisticParam: UserPracticeStatisticParamDTO,$toCompareFrequencyParam:UserPracticeStatisticParamDTO,$toCompareFavoriteParam:UserFavoriteQuestionStatisticParamDTO
,$correctRateTrendParam: PracticeTrendStatisticParamDTO,$practiceTrendParam: PracticeTrendStatisticParamDTO) {
  statistic:statisticUserPractice(paramDTO: $statisticParam) {
		_ALL_
  }
  allCategoryCount:statisticUserAnswerQuestionCountByQuestionCategory(paramDTO: $allCategoryCountParam) {
  		_ALL_
  }
  frequency:statisticUserPracticeFrequency(paramDTO: $frequencyParam) {
  		_ALL_
  }
  favorite:statisticUserFavoriteQuestionGroupByQuestionType(paramDTO: $favoriteParam) {
  		_ALL_
  }
  baseStatistic:statisticUserPractice(paramDTO: $baseStatisticParam) {
        _ALL_
  }
  baseFrequency:statisticUserPracticeFrequency(paramDTO: $baseFrequencyParam) {
  		_ALL_
  }
  baseFavorite:statisticUserFavoriteQuestionGroupByQuestionType(paramDTO: $baseFavoriteParam) {
  		_ALL_
  }
  toCompareStatistic:statisticUserPractice(paramDTO: $toCompareStatisticParam) {
        _ALL_
  }
  toCompareFrequency:statisticUserPracticeFrequency(paramDTO: $toCompareFrequencyParam) {
  		_ALL_
  }
  toCompareFavorite:statisticUserFavoriteQuestionGroupByQuestionType(paramDTO: $toCompareFavoriteParam) {
  		_ALL_
  }
  correctRateTrend:statisticUserPracticeCorrectRateTrend(paramDTO: $correctRateTrendParam) {
  		_ALL_
  }
  practiceTrend:statisticUserPracticeCountTrend(paramDTO: $practiceTrendParam) {
  		_ALL_
  }
}
