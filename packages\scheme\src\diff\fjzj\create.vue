<route-params content="/:schemeId"></route-params>
<route-meta>
{
"isMenu": true,
"title": "新建培训方案",
"sort": 1,
"icon": "icon_guanli"
}
</route-meta>

<template>
  <div v-if="$hasPermission('create')" desc="创建/复制" actions="@Operation">
    <Operation></Operation>
  </div>
</template>

<script lang="ts">
  import Operation from '@hbfe/jxjy-admin-scheme/src/diff/fjzj/__components__/operation.vue'
  import { Component, Vue } from 'vue-property-decorator'
  @Component({
    components: {
      Operation
    }
  })
  export default class extends Vue {
    constructor() {
      super()
      // ***差异化***
    }
  }
</script>
