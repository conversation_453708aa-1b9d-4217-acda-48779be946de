<template>
  <div
    class="f-p15"
    v-if="$hasPermission('query,queryZt')"
    desc="query:查询,queryZt:查询-专题管理员"
    actions="query:queryNewsList
  #queryZt:querySpecialNewsList"
  >
    <div class="f-mb15">
      <template
        v-if="$hasPermission('create')"
        desc="新建"
        actions="@hbfe/jxjy-admin-specialTopics/src/thematic-information-management/created.vue"
      >
        <el-button type="primary" icon="el-icon-plus" @click="addThematicInformation">新建专题资讯</el-button>
      </template>
    </div>
    <el-card shadow="never" class="m-card f-mb15">
      <!--条件查询-->
      <hb-search-wrapper @reset="reset" class="m-query is-border-bottom">
        <el-form-item label="资讯标题">
          <el-input v-model="param.title" clearable placeholder="请输入资讯标题" />
        </el-form-item>
        <el-form-item label="资讯分类">
          <information-classification
            ref="classificationRef"
            v-model="param.categoryType"
            :checkStrictly="false"
            :value="param.categoryType"
            @getChildOptions="getChildOptions"
            @getParentOptions="getParentOptions"
          ></information-classification>
        </el-form-item>
        <el-form-item label="是否弹窗">
          <el-select v-model="param.isPopup" clearable placeholder="请选择是否弹窗">
            <el-option :value="true" label="是"></el-option>
            <el-option :value="false" label="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="资讯状态">
          <el-select v-model="param.status" clearable placeholder="请选择资讯状态">
            <el-option :value="newsStatusEnum.ALL" label="全部"></el-option>
            <el-option :value="newsStatusEnum.DRAFT" label="草稿"></el-option>
            <el-option :value="newsStatusEnum.RELEASE" label="发布"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属专题">
          <el-input v-model="param.belongSpecialName" clearable placeholder="请输入专题名称查询" />
        </el-form-item>
        <template slot="actions">
          <el-button type="primary" @click="page.currentChange(1)" :loading="loading">查询</el-button>
        </template>
      </hb-search-wrapper>
      <!--表格-->
      <el-table stripe :data="tableData" max-height="500px" class="m-table" ref="tableRef" v-loading="loading">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column width="115" align="center">
          <template slot-scope="scope">
            <el-tag type="primary" size="small" v-if="scope.row.isPopup">弹窗公告</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="资讯标题" min-width="400">
          <template slot-scope="scope">
            <p>{{ scope.row.title }}</p>
            <p class="f-c9 f-f13 f-mt5">
              <el-tag type="danger" size="small" class="f-mr10" v-if="scope.row.isTop">置顶</el-tag>
              <span class="f-mr20">发布时间：{{ scope.row.date }}</span
              >发布人：{{ scope.row.issuer || '-' }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="所属专题" min-width="220">
          <template slot-scope="scope">{{ scope.row.specialName || '-' }}</template>
        </el-table-column>
        <el-table-column label="资讯分类" min-width="120">
          <template slot-scope="scope">{{ scope.row.categoryType }}</template>
        </el-table-column>
        <el-table-column label="状态" min-width="120">
          <template slot-scope="scope">
            <el-badge is-dot type="warning" class="badge-status" v-if="scope.row.status == newsStatusEnum.DRAFT"
              >草稿</el-badge
            >
            <el-badge is-dot type="success" class="badge-status" v-if="scope.row.status == newsStatusEnum.RELEASE"
              >发布</el-badge
            >
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" align="center">
          <template slot-scope="scope">
            <template
              v-if="$hasPermission('copy')"
              desc="复制"
              actions="@hbfe/jxjy-admin-specialTopics/src/thematic-information-management/created.vue"
            >
              <el-button type="text" size="mini" @click="copyThematicInformation(scope.row)">复制</el-button>
            </template>
            <template
              v-if="$hasPermission('modify')"
              desc="修改"
              actions="@hbfe/jxjy-admin-specialTopics/src/thematic-information-management/modify.vue"
            >
              <el-button type="text" size="mini" @click="modifyThematicInformation(scope.row)">修改</el-button>
            </template>
            <template v-if="$hasPermission('remove')" desc="删除" actions="deleteThematicInformation">
              <hb-popconfirm
                placement="top"
                title="删除后该资讯需要重新创建，是否确认删除？"
                @confirm="deleteThematicInformation(scope.row.id)"
              >
                <el-button slot="reference" type="text" size="mini">删除</el-button>
              </hb-popconfirm>
            </template>
            <template v-if="$hasPermission('draft')" desc="置为草稿" actions="draftThematicInformation">
              <hb-popconfirm
                placement="top"
                title="置为草稿则门户不可见，是否确认置为草稿"
                @confirm="draftThematicInformation(scope.row.id)"
                v-if="scope.row.status == newsStatusEnum.RELEASE"
              >
                <el-button slot="reference" type="text" size="mini">置为草稿</el-button>
              </hb-popconfirm>
            </template>
            <template v-if="$hasPermission('publish')" desc="发布" actions="publishThematicInformation">
              <hb-popconfirm
                placement="top"
                title="确定发布该资讯？"
                @confirm="publishThematicInformation(scope.row.id)"
                v-if="scope.row.status == newsStatusEnum.DRAFT"
              >
                <el-button slot="reference" type="text" size="mini">发布</el-button>
              </hb-popconfirm>
            </template>
            <template v-if="$hasPermission('check')" desc="查看" actions="toCheck">
              <el-button
                slot="reference"
                type="text"
                size="mini"
                :loading="checkLoading"
                :disabled="scope.row.status == newsStatusEnum.DRAFT"
                @click="toCheck(scope.row)"
                >查看</el-button
              >
            </template>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"> </hb-pagination>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { NewsCategoryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import MutationNewsFactory from '@api/service/management/news/mutation/MutationNewsFactory'
  import { NewsStatusEnum } from '@api/service/management/news/query/query-news-list/enum/NewsStatusEnum'
  import QueryNewsList from '@api/service/management/news/query/query-news-list/QueryNewsList'
  import QueryNewsListInTrainingChannel from '@api/service/management/news/query/query-news-list/QueryNewsListInTrainingChannel'
  import NewsListParamVo from '@api/service/management/news/query/query-news-list/vo/NewsListParamVo'
  import NewsListVo from '@api/service/management/news/query/query-news-list/vo/NewsListVo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { UiPage } from '@hbfe/common'
  import InformationClassification from '@hbfe/jxjy-admin-specialTopics/src/thematic-information-management/components/information-classification.vue'
  import { ElTable } from 'element-ui/types/table'
  import { bind, debounce } from 'lodash-decorators'
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import Env from '@api/service/common/utils/Env'
  class Category extends NewsCategoryResponse {
    children?: Array<Category>
    fatherId?: string
  }
  @Component({ components: { InformationClassification } })
  export default class extends Vue {
    /**
     * 表格ref
     */
    @Ref('tableRef')
    tableRef: ElTable
    /**
     * 资讯分类组件ref
     */
    @Ref('classificationRef')
    classificationRef: InformationClassification
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    /**
     * 分页
     */
    page = new UiPage()
    /**
     * 列表数据
     */
    tableData = new Array<NewsListVo>()
    // 网校查询资讯列表
    queryNewsListWx = new QueryNewsList()
    // 一级分类筛选项
    informationClassificationOptions = new Array<Category>()
    // 二级分类筛选项
    informationOptionsTwo = new Array<Category>()
    // 用于过滤资讯code
    // queryNewsListBase = new QueryNewsListBase()
    /**
     * 资讯状态枚举
     */
    newsStatusEnum = NewsStatusEnum
    /**
     * 查询入参
     */
    param = new NewsListParamVo()
    checkLoading = false
    /**
     * 列表loading
     */
    loading = false
    /**
     * 修改资讯状态实例化
     */
    mutationNewsFactory = new MutationNewsFactory()
    // 查询资讯
    queryNewsListInTrainingChannel = new QueryNewsListInTrainingChannel()
    // 是否是专题管理员
    isZtGly = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    //环境单例
    env = Env
    /**
     * 初始化
     */
    created() {
      this.doQueryPage()
      this.$nextTick(() => {
        ;(this.$refs['classificationRef'] as InformationClassification).getInformationClassificationStair().then(() => {
          ;(this.$refs['classificationRef'] as InformationClassification).echo()
        })
      })
    }
    activated() {
      this.doQueryPage()
      this.$nextTick(() => {
        ;(this.$refs['classificationRef'] as InformationClassification).getInformationClassificationStair().then(() => {
          ;(this.$refs['classificationRef'] as InformationClassification).echo()
        })
      })
    }
    /**
     * 获取列表数据
     */
    async doQueryPage() {
      this.loading = true
      try {
        if (this.isZtGly) {
          this.tableData = await this.querySpecialNewsList()
        } else {
          this.tableData = await this.queryNewsList()
        }
      } catch (e) {
        console.log(e)
      } finally {
        ;(this.$refs['tableRef'] as ElTable).doLayout()
        this.loading = false
      }
    }

    // 专题管理员查询
    async querySpecialNewsList() {
      return await this.queryNewsListInTrainingChannel.querySpecialNewsList(this.page, this.param)
    }

    // 网校管理员查询
    async queryNewsList() {
      return await this.queryNewsListWx.querySpecialNewsList(this.page, this.param)
    }

    getParentOptions(val: any) {
      this.informationClassificationOptions = val
    }

    getChildOptions(val: any) {
      this.informationOptionsTwo = val
    }
    // 查看
    async toCheck(data: NewsListVo) {
      this.checkLoading = true
      // 判断是不是现网环境
      const isProduction = this.env.proxyPortStr
      const oneCategoryCode = this.informationClassificationOptions.filter((item) => {
        if (item.newsCategoryId === data?.parentCategoryId) {
          return item.code
        }
      })
      const twoCategoryCode = this.informationOptionsTwo.filter((item) => {
        if (item.newsCategoryId === data?.categoryId) {
          return item.code
        }
      })
      let url = ''
      try {
        // 帮助中心
        if (oneCategoryCode[0]?.code === 'HELP-CENTER') {
          // 常见问题
          if (twoCategoryCode[0].code === 'QUESTION-ANSWER') {
            url = 'https://' + data.domainName + `${isProduction}/help-center/QUESTION-ANSWER?categoryId=${data.id}`
          } else if (twoCategoryCode[0].code === 'OPERATION-GUIDE') {
            //操作指南
            url = 'https://' + data.domainName + `${isProduction}/help-center/OPERATION-GUIDE?categoryId=${data.id}`
          } else if (twoCategoryCode[0].code === 'TRAINING-REQUIREMENT') {
            // 培训须知
            url =
              'https://' + data.domainName + `${isProduction}/help-center/TRAINING-REQUIREMENT?categoryId=${data.id}`
          } else {
            // 其他自定义资讯
            url = 'https://' + data.domainName + `${isProduction}/help-center`
          }
        } else {
          // 培训通知、政策法规
          url =
            'https://' +
            data.domainName +
            `${isProduction}/information/${data.categoryId}/${data.id}?categoryName=${data.categoryType}`
        }
        window.open(url, '_blank')
      } catch (error) {
        console.log(error)
      } finally {
        this.checkLoading = false
      }
    }

    /**
     * 重置
     */
    async reset() {
      this.page.pageNo = 1
      this.param = new NewsListParamVo()
      await this.doQueryPage()
    }
    /**
     * 新建专题资讯
     */
    addThematicInformation() {
      this.$router.push('/training/special-topics/thematic-information-management/created')
    }
    /**
     * 复制专题资讯
     */
    copyThematicInformation(row: NewsListVo) {
      this.$router.push(`/training/special-topics/thematic-information-management/created?id=${row.id}`)
    }
    /**
     * 修改专题资讯
     */
    modifyThematicInformation(row: NewsListVo) {
      this.$router.push(`/training/special-topics/thematic-information-management/modify/${row.id}`)
    }
    /**
     * 删除专题资讯
     */
    @bind
    @debounce(200)
    async deleteThematicInformation(id: string) {
      const res = await this.mutationNewsFactory.mutationNewsDelete().doDeleteNews(id)
      if (res.isSuccess()) {
        this.$message.success('删除成功')
        await this.doQueryPage()
      } else {
        this.$message.warning('删除失败')
      }
    }
    /**
     * 置为草稿
     */
    @bind
    @debounce(200)
    async draftThematicInformation(id: string) {
      const mutationNewsChangeStatusVo = this.mutationNewsFactory.mutationNewsChangeStatus(id)
      const res = await mutationNewsChangeStatusVo.doDraftNews()
      if (res.isSuccess()) {
        this.$message.success('置为草稿成功')
        await this.doQueryPage()
      } else {
        this.$message.warning('置为草稿失败')
      }
    }
    /**
     * 发布资讯
     */
    @bind
    @debounce(200)
    async publishThematicInformation(id: string) {
      const mutationNewsChangeStatusVo = this.mutationNewsFactory.mutationNewsChangeStatus(id)
      const res = await mutationNewsChangeStatusVo.doPublishNews()
      if (res.isSuccess()) {
        this.$message.success('发布成功')
        await this.doQueryPage()
      } else {
        this.$message({
          showClose: true,
          duration: 1500,
          type: 'warning',
          message: res.getMessage()
        })
        setTimeout(() => {
          this.$router.push(`/training/special-topics/thematic-information-management/modify/${id}`)
        }, 1500)
      }
    }
  }
</script>
