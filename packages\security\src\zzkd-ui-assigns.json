[{"name": "WXGLY", "meta": {"title": "网校管理员", "isMenu": true, "ownerGroup": ["WXGLY"], "roles": ["FXS", "GYS", "NZGYS", "NZFXS", "NZFXSJCB", "NZGYSJCB", "ZTGLY", "JTJFGLY", "WXGLY", "DQGLY"], "permissionMap": {"query": {"name": "查询(必选)", "ownerGroup": ["WXGLY.query"], "graphql": ["ms-config-v1.query.getCurrentFrontendConfig:{\"serviceName\":\"ms-config-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForServiceByDomainName:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.query.getDistributionService:{\"authorizationRequired\":false}", "ms-identity-authentication-v1.query.getAccessTokenValue:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getAdminInfoInMyself:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjy-distributor-admin-v1.query.isOnlineSchoolContractExpired:{\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.isOnlineSchoolContractExpired:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.query.findFunctionalAuthorityByRoleIdsNew:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.query.findRoleByAccountId:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-autolearning-online-school-smart-learning-service-v1.query.queryOnlineSchoolSmartLearningServiceConfigByServicerId:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applyCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptchaForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCodeForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwd:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptcha:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountId:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCode:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-account-v1.mutation.changeAuthorizationUnitInfoList:{\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applySmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validSmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentUser:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.validCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getOnlineSchoolConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getSmsCodeAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getFailedAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.forgetPassword:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCoursePreview:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "platform-training-channel-v1.mutation.updateSaleSetting:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursePackageInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseInSchemeInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageCoursewareSupplierInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyChangeIdentityAuthentication:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticate:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyProxyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticateBasic:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryPayFlowStatus:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-payment-v1.mutation.getBatchPreParePayResult:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryQrScanPromptByPayFlowNo:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.deliveredOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":true}", "ms-assess-v1.mutation.batchRePushSchemeIndicator:{\"serviceName\":\"ms-assess-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.createJxjyCommonMenu:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.temporaryOrderUpdateOrderInfoInSubject:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.enableOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForService:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.tryComparePhoto:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.updateUserDatumCodePhoto:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.refreshConfigAndUpdate:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.executeAntiInspection:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":false}", "platform-account-v1.mutation.compensatingStudentAccount:{\"serviceName\":\"platform-account-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.mutation.openDistributionService:{\"authorizationRequired\":false}", "ms-order-repair-data-v1.mutation.orderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-repair-data-v1.mutation.batchOrderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.distributionAuthBatchProduct:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.createPricePlan:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.pricePlanSetSaleChannel:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissReturnOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissExchangeOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.clearStudentLearningRule:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-teachingplan-studentattendance-sds-v1.mutation.oneClickClock:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":false}"], "roles": ["FXS", "GYS", "NZGYS", "NZFXS", "NZFXSJCB", "NZGYSJCB", "ZTGLY", "JTJFGLY", "WXGLY", "DQGLY"], "ext": {"diffSchool": ["zzkd"]}}}, "sort": "2", "diffSchool": ["zzkd"]}, "specifier": "WXGLY", "path": "", "pathSegments": ["WXGLY"], "children": [{"name": "statistic", "specifier": "Statistic", "path": "/statistic", "pathSegments": ["WXGLY", "statistic"], "component": "@/packages/routers/src/basic-router/statistic.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "统计报表", "sort": 5, "icon": "icon-shu<PERSON>", "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "permissionMap": {}, "ownerGroup": ["WXGLY.statistic"], "diffSchool": ["zzkd"]}, "children": [{"name": "statistic-learning-statistic-index", "specifier": "StatisticLearningStatisticIndex", "path": "learning-statistic", "pathSegments": ["WXGLY", "statistic", "learning-statistic"], "component": "@/packages/routers/src/basic-router/statistic/learning-statistic/index.vue", "meta": {"isMenu": true, "title": "学员学习明细", "sort": 5, "icon": "icon-mingxi", "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "permissionMap": {"query": {"roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "name": "查询", "ownerGroup": ["WXGLY.statistic.learning-statistic.query"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegionInDistribution:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageIssueCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-zzkd-school.query.pageStudentSchemeLearningInServicerManageRegion:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInServicerManageRegion:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zzkd-school.query.pageStudentSchemeLearningInTrainingChannelV2:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInTrainingChannel:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zzkd-school.query.pageStudentSchemeLearningInServicerV2:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiBasicConfigInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiModuleConfigurationInfoInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getDistributorPortalInfosInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pagePromotionPortalInfoInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageDistributorInSupplier:{\"authorizationRequired\":true}", "fxnl-query-common-disposition-information.query.getIndustriesByServiceIdInDistributor:{\"authorizationRequired\":true}", "fxnl-query-common-disposition-information.query.getYearsByServiceIdInDistributor:{\"authorizationRequired\":true}"], "ext": {"diffSchool": ["zzkd"]}}, "exceptionManagement": {"name": "学习数据异常管理", "ownerGroup": ["WXGLY.statistic.learning-statistic.exceptionManagement"], "graphql": [], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["zzkd"]}}, "learnAnomalousList": {"name": "学习数据异常管理-学习规则", "ownerGroup": ["WXGLY.statistic.learning-statistic.learnAnomalousList"], "graphql": ["student-course-learning-query-back-gateway.query.pageLearningResultErrorInTrainingChannel:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.pageLearningResultErrorInServicer:{\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.reGenerateStudentTrainingResultSimulateInServicer:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["zzkd"]}}, "intelligentLearningList": {"name": "学习数据异常管理-智能学习", "ownerGroup": ["WXGLY.statistic.learning-statistic.intelligentLearningList"], "graphql": ["ms-autolearning-student-auto-learning-task-result-v1.query.queryByPage:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-autolearning-student-auto-learning-task-result-v1.query.findLastFailSubTaskByMainTaskIdList:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["zzkd"]}}, "export": {"name": "导出", "ownerGroup": ["WXGLY.statistic.learning-statistic.export"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInServicer:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInServicer:{\"authorizationRequired\":false}", "platform-jxjypxtypt-zzkd-school.query.exportStudentSchemeLearningExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInDistributor:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInDistributor:{\"authorizationRequired\":false}", "platform-jxjypxtypt-zzkd-school.query.exportStudentSchemeLearningExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInTrainingChannel:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInTrainingChannel:{\"authorizationRequired\":true}", "platform-jxjypxtypt-zzkd-school.query.exportStudentSchemeLearningExcelInTrainingChannelV2:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInServicerManageRegion:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInServicerManageRegion:{\"authorizationRequired\":true}", "platform-jxjypxtypt-zzkd-school.query.exportStudentSchemeLearningExcelInServicerManageRegion:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["zzkd"]}}, "onlineClassTable": {"name": "网授班列表", "ownerGroup": ["WXGLY.statistic.learning-statistic.onlineClassTable"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pagePlanItemAttendanceInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getMySchemeIssueConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["zzkd"]}}, "allSync": {"name": "批量同步", "ownerGroup": ["WXGLY.statistic.learning-statistic.allSync"], "graphql": ["platform-jxjypxtypt-student-learning-backstage.query.rePushStudentTrainingResultToGatewayInServicerV2:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["zzkd"]}}, "examDetail": {"name": "考试详情", "ownerGroup": ["WXGLY.statistic.learning-statistic.examDetail"], "graphql": [], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["zzkd"]}}, "testDetail": {"name": "测验详情", "ownerGroup": ["WXGLY.statistic.learning-statistic.testDetail"], "graphql": [], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["zzkd"]}}, "toLog": {"name": "查阅监管日志", "ownerGroup": ["WXGLY.statistic.learning-statistic.toLog"], "graphql": [], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["zzkd"]}}, "userInfo": {"name": "用户信息组件", "ownerGroup": ["WXGLY.statistic.learning-statistic.userInfo"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["zzkd"]}}, "classInfo": {"name": "培训班信息组件", "ownerGroup": ["WXGLY.statistic.learning-statistic.classInfo"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigByRequestInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["zzkd"]}}, "studyLog": {"name": "学习日志组件", "ownerGroup": ["WXGLY.statistic.learning-statistic.studyLog"], "graphql": ["ms-general-supervision-query-front-gateway-Backstage.query.pageSupervisionCourseDetailsInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.listLearningSupervisionBehaviorInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["zzkd"]}}, "toLearningLog": {"name": "查阅学习日志", "ownerGroup": ["WXGLY.statistic.learning-statistic.toLearningLog"], "graphql": [], "roles": ["WXGLY", "DQGLY", "ZTGLY"], "ext": {"diffSchool": ["zzkd"]}}, "queryInfo": {"name": "查询学习日志", "ownerGroup": ["WXGLY.statistic.learning-statistic.queryInfo"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningLogsInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.getLearningType:{\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["zzkd"]}}, "faceClassTable": {"name": "面授班列表", "ownerGroup": ["WXGLY.statistic.learning-statistic.faceClassTable"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pagePlanItemAttendanceInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getMySchemeIssueConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["zzkd"]}}, "mixedClassTable": {"name": "混合班列表", "ownerGroup": ["WXGLY.statistic.learning-statistic.mixedClassTable"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pagePlanItemAttendanceInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getMySchemeIssueConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["zzkd"]}}, "attendanceDetail": {"name": "考勤详情", "ownerGroup": ["WXGLY.statistic.learning-statistic.attendanceDetail"], "graphql": [], "roles": [], "ext": {"diffSchool": ["zzkd"]}}}, "ownerGroup": ["WXGLY.statistic.learning-statistic"], "diffSchool": ["zzkd"]}}]}]}]