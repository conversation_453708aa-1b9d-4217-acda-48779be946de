import EnterpriseItem from '@api/service/common/enterprise-Information/EnterpriseItem'
import PlatformInformation, { InformationRequest } from '@api/platform-gateway/platform-information-gateway'
import { Page, ResponseStatus } from '@hbfe/common'
import { TypeEnum } from '@api/service/common/enterprise-Information/enums/TypeEnum'
export default class EnterpriseInformation {
  /**
   * 查询类型
   * @type {TypeEnum}
   */
  type: TypeEnum = null
  /**
   * 查询关键字 企业名称或信用代码
   */
  key = ''
  /**
   * 企业列表
   * @type {Array<EnterpriseItem>}
   */
  enterpriseList: Array<EnterpriseItem> = []
  /**
   * 天眼查/企查查
   * @return {Promise<ResponseStatus>}
   */
  async queryInformationByFuzzyInPublic(): Promise<ResponseStatus> {
    const params = new InformationRequest()
    params.key = this.key
    params.type = this.type
    const res = await PlatformInformation.listInformationByFuzzyInPublic(params)
    if (res.status.isSuccess()) {
      this.enterpriseList = res.data.map(EnterpriseItem.from)
    } else {
      this.enterpriseList = []
    }
    return res.status
  }
  async pageInformationByFuzzyInPublic(page: Page) {
    page.totalSize = this.enterpriseList.length
    page.totalPageSize = Math.ceil(page.totalSize / page.pageSize)
    return this.enterpriseList.slice((page.pageNo - 1) * page.pageSize, page.pageNo * page.pageSize)
  }
}
