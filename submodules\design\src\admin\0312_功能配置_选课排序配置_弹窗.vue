<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">停用-弹窗</el-button>
        <el-dialog title="" :visible.sync="dialog1" width="400px" class="m-dialog" :show-close="false">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">确定停用该规则吗？</span>
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确 定</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">停用-弹窗2</el-button>
        <el-dialog title="系统提醒" :visible.sync="dialog2" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">停用后该排序规则将失效，是否确认停用？</span>
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确 定</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog3 = true" type="primary" class="f-mr20">启用-弹窗</el-button>
        <el-dialog title="" :visible.sync="dialog3" width="400px" class="m-dialog" :show-close="false">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">确定启用该规则吗？</span>
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确 定</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog4 = true" type="primary" class="f-mr20">启用-弹窗2</el-button>
        <el-dialog title="系统提醒" :visible.sync="dialog4" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">启用后该排序规则将按照定时周期执行，是否确认启用？</span>
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确 定</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">课程分布情况-弹窗</el-button>
        <el-drawer
          title="课程分布情况"
          :visible.sync="dialog5"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--条件查询-->
            <el-row :gutter="0" class="m-query">
              <el-form :inline="true" label-width="auto">
                <el-col :span="12">
                  <el-form-item label="培训方案">
                    <el-input v-model="input" clearable placeholder="请输入培训方案" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="方案状态">
                    <el-select v-model="select" clearable filterable placeholder="请选择方案状态">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="分类名称">
                    <el-input v-model="input" clearable placeholder="请输入分类名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="课程数量范围">
                    <el-input v-model="input" clearable placeholder="" class="f-w147" />
                    <span class="f-ml5 f-mr5">至</span>
                    <el-input v-model="input" clearable placeholder="" class="f-w147" />
                  </el-form-item>
                </el-col>
                <el-col :span="12" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">搜索</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="培训方案" min-width="300">
                <template>读取培训方案名称</template>
              </el-table-column>
              <el-table-column label="方案状态" min-width="140">
                <template>报名开启</template>
              </el-table-column>
              <el-table-column label="分类名称" min-width="180">
                <template>分类名称分类名称</template>
              </el-table-column>
              <el-table-column label="课程数量" width="120" sortable align="center" fixed="right">
                <template>100</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
