"""独立部署的微服务,K8S服务名:ms-teachingplan-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getServerTime(request:GetServerTimeRequest):Long
	getServerTimeWithStudentNo(request:GetServerTimeWithStudentNoRequest):Long
}
"""获取服务器时间
	<AUTHOR>
"""
input GetServerTimeRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.GetServerTimeRequest") {
	studentLearningToken:String!
	planId:String!
}
"""获取服务器时间
	<AUTHOR>
"""
input GetServerTimeWithStudentNoRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.GetServerTimeWithStudentNoRequest") {
	"""学生学号"""
	studentNo:String!
	"""期别ID"""
	issueId:String!
	"""参训资格ID"""
	qualificationId:String!
	"""教学计划学习方式ID, 没有值时请提供-1"""
	learningId:String!
	"""教学计划ID, 没有值时请提供 -1"""
	planId:String!
}

scalar List
