import { FileTypesEnum } from '@api/service/common/enums/personal-leaning/FileTypes'
import {
  DataMigrationCertificatePrintRequest,
  DataMigrationCertificatePrintWithOutLoginRequest,
  Extend
} from '@api/platform-gateway/platform-certificate-v1'
export class ExtendVo {
  /**
   * 键
   */
  key = ''
  /**
   * 值
   */
  value = ''
}
class PrintHistoryCertificateRequestVo {
  /**
   * 历史培训档案id
   */
  historyCertificateId = ''
  /**
   * 文件类型 PDF/IMAGE
   @see FileTypesEnum
   */
  fileType: FileTypesEnum = null
  /**
   * 拓展数据
   */
  extendData: ExtendVo[] = []

  /**
   * 用户id
   */
  userId = ''
  // 模型转换
  to(): DataMigrationCertificatePrintRequest {
    const request = new DataMigrationCertificatePrintRequest()
    request.learningDataId = this.historyCertificateId
    request.fileType = this.fileType
    if (this.extendData.length) {
      request.data = new Array<Extend>()
      request.data = this.extendData.map(item => {
        const extend = new Extend()
        extend.key = item.key
        extend.value = item.value
        return extend
      })
    }
    return request
  }
  toOutLoinRequest(): DataMigrationCertificatePrintWithOutLoginRequest {
    const request = new DataMigrationCertificatePrintWithOutLoginRequest()
    request.userId = this.userId
    request.learningDataId = this.historyCertificateId
    request.fileType = this.fileType
    if (this.extendData.length) {
      request.data = new Array<Extend>()
      request.data = this.extendData.map(item => {
        const extend = new Extend()
        extend.key = item.key
        extend.value = item.value
        return extend
      })
    }
    return request
  }
}
export default PrintHistoryCertificateRequestVo
