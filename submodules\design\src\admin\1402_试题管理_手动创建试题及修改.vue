<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">试题管理</el-breadcrumb-item>
      <el-breadcrumb-item>试题修改</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="试题题库：" required>
                <el-select v-model="form.region" clearable placeholder="请选择试题题库" class="form-l">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="关联课程：" required>
                <el-select v-model="form.region" clearable placeholder="请选择关联课程" class="form-l">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="试题题型：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="单选题"></el-radio>
                  <el-radio label="多选题"></el-radio>
                  <el-radio label="判断题"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="难度：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="难"></el-radio>
                  <el-radio label="中等"></el-radio>
                  <el-radio label="简单"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="试题题目：" required>
                <div class="rich-text">
                  <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入试题题目" />
                </div>
              </el-form-item>
              <!--单选题-->
              <el-form-item label="试题选项：" required>
                <!--选项 A-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 A</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top" disabled></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom"></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                </div>
                <!--选项 B-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 B</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top"></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom"></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                </div>
                <!--选项 C-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 C</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top"></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom"></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                </div>
                <!--选项 D-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 D</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top"></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom" disabled></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                </div>
                <el-button type="primary" size="small" plain icon="el-icon-plus">添加选项</el-button>
              </el-form-item>
              <el-form-item label="正确答案：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="A"></el-radio>
                  <el-radio label="B"></el-radio>
                  <el-radio label="C"></el-radio>
                  <el-radio label="D"></el-radio>
                </el-radio-group>
              </el-form-item>
              <!--判断题-->
              <el-form-item label="试题选项：" required>
                <!--选项 A-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center">
                    <p class="f-cb">真项 A</p>
                    <el-input v-model="input1" clearable class="f-flex-sub f-ml10" />
                  </div>
                </div>
                <!--选项 B-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center">
                    <p class="f-cb">假项 B</p>
                    <el-input v-model="input2" clearable class="f-flex-sub f-ml10" />
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="正确答案：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="真项 A"></el-radio>
                  <el-radio label="假项 B"></el-radio>
                </el-radio-group>
              </el-form-item>
              <!--多选题-->
              <el-form-item label="试题选项：" required>
                <!--选项 A-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 A</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top" disabled></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom"></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                </div>
                <!--选项 B-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 B</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top"></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom"></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                </div>
                <!--选项 C-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 C</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top"></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom"></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                </div>
                <!--选项 D-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 D</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top"></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom" disabled></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                </div>
                <el-button type="primary" size="small" plain icon="el-icon-plus">添加选项</el-button>
              </el-form-item>
              <el-form-item label="正确答案：" required>
                <el-checkbox-group v-model="form.type">
                  <el-checkbox label="A" name="type"></el-checkbox>
                  <el-checkbox label="B" name="type"></el-checkbox>
                  <el-checkbox label="C" name="type"></el-checkbox>
                  <el-checkbox label="D" name="type"></el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="试题解析：">
                <div class="rich-text">
                  <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入试题题目" />
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button>取消</el-button>
        <el-button type="primary">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        input1: '对',
        input2: '错',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
