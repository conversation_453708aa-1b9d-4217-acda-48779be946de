import QueryDasicdata from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import MsBasicDataQueryBackstageGateway, {
  BusinessDictionaryAcrossTypeResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
/**
 * 根据下级字典获取上级字典信息
 */
class QueryDictionaryAcrossType {
  /**
   * 专业类型映射
   */
  private acrossTypeMap = new Map<string, Array<BusinessDictionaryAcrossTypeResponse>>()

  /**
   * 根据下级字典获取上级字典信息
   */
  async queryCategoryInfoByIds(id: string) {
    if (!this.acrossTypeMap.has(id)) {
      const res = await MsBasicDataQueryBackstageGateway.listBusinessDictionaryAcrossTypeBySalveId(id)
      if (res.status.isSuccess()) {
        this.acrossTypeMap.set(id, res.data)
      } else {
        return []
      }
    }

    return this.acrossTypeMap.get(id)
  }
}
export default new QueryDictionaryAcrossType()
