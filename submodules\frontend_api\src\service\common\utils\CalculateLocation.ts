export function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number) {
  const result = 6371e3 // meters
  const a1 = (lat1 * Math.PI) / 180
  const a2 = (lat2 * Math.PI) / 180
  const a3 = ((lat2 - lat1) * Math.PI) / 180
  const a4 = ((lon2 - lon1) * Math.PI) / 180
  const a = Math.sin(a3 / 2) * Math.sin(a3 / 2) + Math.cos(a1) * Math.cos(a2) * Math.sin(a4 / 2) * Math.sin(a4 / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  const d = result * c // in meters
  return d
}
