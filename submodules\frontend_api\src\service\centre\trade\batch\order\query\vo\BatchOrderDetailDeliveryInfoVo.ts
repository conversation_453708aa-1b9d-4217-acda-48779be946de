import { DeliveryWayEnum } from '@api/service/centre/trade/batch/order/enum/DeliveryWayList'
import { SelfFetchedDeliveryStatusEnum } from '@api/service/centre/trade/batch/order/enum/SelfFetchedDeliveryStatusList'
import { CourierDeliveryStatusEnum } from '@api/service/centre/trade/batch/order/enum/CourierDeliveryStatusList'

/**
 * @description 【集体报名订单】订单详情-配送信息
 */
class BatchOrderDetailDeliveryInfoVo {
  /**
   * 配送方式 1：自取 2：快递
   */
  deliveryWay: DeliveryWayEnum = null

  /**
   * 自取配送状态 1：未自取 2：已自取
   */
  selfFetchedDeliveryStatus: SelfFetchedDeliveryStatusEnum = null

  /**
   * 快递配送状态 1：未发货 2：已经发货
   */
  courierDeliveryStatus: CourierDeliveryStatusEnum = null

  /**
   * 快递公司
   */
  deliveryCompany = ''

  /**
   * 快递单号
   */
  deliveryNo = ''

  /**
   * 查询网址
   */
  queryWebsite = ''

  /**
   * 收货人
   */
  distributeConsignee = ''
  /**
   * 所在地区
   */
  region = ''
  /**
   * 收货地址
   */
  distributeAddress = ''

  /**
   * 联系方式
   */
  distributePhone = ''

  /**
   * 【自取】领取地点
   */
  selfFetchedPoint = ''

  /**
   * 【自取】领取时间
   */
  selfFetchTime = ''

  /**
   * 【自取】备注
   */
  remark = ''
}

export default BatchOrderDetailDeliveryInfoVo
