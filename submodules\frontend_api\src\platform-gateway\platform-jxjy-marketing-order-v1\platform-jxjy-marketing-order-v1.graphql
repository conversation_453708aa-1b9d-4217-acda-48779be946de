schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""申请进入分销销售渠道"""
	applyEnterSaleChannelByPortalId(request:ApplyEnterSaleChannelByPortalIdRequest):ApplyEnterSaleChannelResponse
	"""创建门户分销订单"""
	createMarketingOrder(request:CreatedMarketingOrderRequest):CreateMarketingOrderResponse
	"""校验门户分销订单"""
	verifyMarketingOrder(request:VerifyMarketingOrderRequest):VerifyMarketingOrderResponse
}
input DeliveryAddress @type(value:"com.fjhb.domain.trade.api.offlineinvoice.events.entities.DeliveryAddress") {
	consignee:String!
	phone:String!
	region:String!
	address:String!
}
input TakePoint @type(value:"com.fjhb.domain.trade.api.offlineinvoice.events.entities.TakePoint") {
	pickupLocation:String!
	pickupTime:String!
	remark:String
}
"""发票信息
	<AUTHOR>
	@since 2021/3/23
"""
input InvoiceInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.appservice.request.InvoiceInfoRequest") {
	"""发票抬头"""
	title:String
	"""发票抬头类型
		<pre>
		1-个人
		2-企业
		</pre>
	"""
	titleType:Int
	"""发票类型
		<pre>
		1-电子发票
		2-纸质发票
		</pre>
	"""
	invoiceType:Int
	"""发票种类
		<pre>
		1-普通发票
		2-增值税普通发票
		3-增值税专用发票
		</pre>
	"""
	invoiceCategory:Int
	"""购买方纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账户"""
	account:String
	"""发票票面备注"""
	remark:String
	"""开票方式
		1 - 线上开票
		2 - 线下开票
	"""
	invoiceMethod:Int
	"""联系电子邮箱"""
	email:String
	"""联系电话"""
	contactPhone:String
	"""营业执照"""
	businessLicensePath:String
	"""开户许可"""
	accountOpeningLicensePath:String
	"""配送方式
		0/1/2,无/自取/快递
		@see OfflineShippingMethods
	"""
	shippingMethod:Int
	"""配送地址信息"""
	deliveryAddress:DeliveryAddress
	"""自取点信息"""
	takePoint:TakePoint
}
"""推广门户id
	<AUTHOR>
	@date 2024/9/13 10:55
"""
input ApplyEnterSaleChannelByPortalIdRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.ApplyEnterSaleChannelByPortalIdRequest") {
	"""推广门户id"""
	portalId:String
}
"""下单商品描述"""
input CommodityRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.CommodityRequest") {
	"""商品数量"""
	quantity:Int!
	"""商品授权ID(分销上下文产品分销授权ID)"""
	commodityAuthId:String
	"""商品skuId"""
	commoditySkuId:String
	"""商品使用的价格策略类型 1-定价策略 2-优惠策略"""
	policyType:Int
	"""策略id
		若使用定价策略则为定价策略id,若使用优惠策略则为优惠策略id
	"""
	policyId:String
	"""面授班时有值"""
	issueInfo:IssueInfo
}
input IssueInfo @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.CommodityRequest$IssueInfo") {
	"""期别id"""
	issueId:String
	"""住宿类型
		住宿类型 0-无需住宿 1-单人住宿 2-合住
	"""
	accommodationType:Int
}
"""<AUTHOR>
	@since 2023/12/15  13:37
"""
input CreatedMarketingOrderRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.CreatedMarketingOrderRequest") {
	saleChannelPurchaseToken:String!
	"""买家编号"""
	buyerId:String!
	"""商品列表"""
	commodities:[CommodityRequest]!
	"""购买渠道类型
		1-用户自主购买
		2-集体缴费
		3-管理员导入
	"""
	purchaseChannelType:Int!
	"""终端类型
		<p>
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	terminalCode:String!
	"""是否需要发票"""
	needInvoice:Boolean!
	"""发票信息"""
	invoiceInfo:InvoiceInfoRequest
	"""参训单位id"""
	participatingUnitId:String
}
"""校验营销订单请求
	<AUTHOR>
	@since 2023/12/15  13:37
"""
input VerifyMarketingOrderRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.VerifyMarketingOrderRequest") {
	"""销售渠道购买凭证"""
	saleChannelPurchaseToken:String!
	"""买家编号"""
	buyerId:String!
	"""商品列表"""
	commodities:[CommodityRequest]!
}
"""校验结果返回
	<AUTHOR> create 2021/2/3 10:53
"""
type VerifyResultResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.appservice.response.VerifyResultResponse") {
	"""校验结果"""
	message:String
	"""校验code"""
	code:String
	"""订单内的商品skuId"""
	skuId:String
}
"""<AUTHOR>
	@date 2024/8/16 10:38
"""
type ApplyEnterSaleChannelResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.ApplyEnterSaleChannelResponse") {
	"""销售渠道购买凭证,目前为销售渠道id"""
	saleChannelPurchaseToken:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""创建营销订单结果
	<AUTHOR> create 2021/1/29 17:27
"""
type CreateMarketingOrderResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.CreateMarketingOrderResponse") {
	"""是否创建成功
		对应false code
		"62000","商品无效: 网校不在服务期限内"
		"62001","商品无效: 网校没有开启分销服务"
		"62002","商品无效: 分销商分销关系没有启用"
		"62003","商品无效: 分销关系不在合同有效期内"
		"62004","商品无效: 分销授权地区为空"
		"62006","商品无效: 分销商品不在分销有效期内"
		"60005","上属一级分销商在本网校的合作周期已到期"
		"60006","上属一级分销商在本网校该商品分销有效期已到期"
		"60014","未查询到上属一级分销商合同信息"
		"60015","未查询到一级分销商账号信息"
		"60016","未查询到二级分销商账号信息"
		"60027","优惠策略时间未开始"
		"60028","优惠策略时间已结束"
		"60029","未查询到上级分销授权信息"
		"60033","当前优惠策略无剩余优惠名额"
		"60037","上属一级分销商在本网校的合作状态已中止"
		"60040","上级分销商在本网校该商品分销授权状态已停止"
		"60044","定价策略未启用"
		"60045","定价策略销售地区为空"
		"60046","定价策略不存在"
		"60047","您的地区/单位不在分销报名范围内"
		"60048","优惠策略未启用"
		"60049","优惠策略不存在"
	"""
	success:Boolean!
	"""状态信息"""
	message:String
	"""订单号，仅当{@link #success}为{@code true}时有值"""
	orderNo:String
	"""订单创建时间，仅当{@link #success}为{@code true}时有值"""
	createTime:DateTime
	"""商品验证结果信息"""
	resultList:[VerifyResultResponse]
}
type SkuVerifyResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.SkuVerifyResponse") {
	"""商品skuId"""
	commoditySkuId:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
	"""商品对应的定价策略销售范围"""
	pricingPolicySaleScopeRespList:[PricingPolicySaleScopeResp]
	"""商品对应的优惠策略策略销售范围"""
	discountPolicySaleScopeRespList:[DiscountPolicySaleScopeResp]
}
type DiscountPolicySaleScopeResp @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.SkuVerifyResponse$DiscountPolicySaleScopeResp") {
	"""销售范围ID"""
	saleScopeId:String
	"""优惠策略ID"""
	discountPolicyId:String
	"""【必传】销售区域集合
		需知：当地区code存在父code时，不能存在子code 如 存在福建省code 则不能存在福州市code
	"""
	regionList:[String]
}
type PricingPolicySaleScopeResp @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.SkuVerifyResponse$PricingPolicySaleScopeResp") {
	"""定价策略ID"""
	pricingPolicyId:String
	"""销售范围ID"""
	saleScopeId:String
	"""销售单位集合"""
	saleUnitList:[SaleUnitResponse]
	"""【必传】销售区域集合
		需知：当地区code存在父code时，不能存在子code 如 存在福建省code 则不能存在福州市code
	"""
	regionList:[String]
	"""地区来源类型
		@see com.fjhb.domain.trade.api.marketing.consts.RegionSourceType
	"""
	regionSourceType:Int
	"""地区来源ID
		@see com.fjhb.domain.trade.api.marketing.consts.RegionSourceType
	"""
	regionSourceId:String
	"""是否启用 0 否 1 是"""
	enabled:Boolean
}
type SaleUnitResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.SkuVerifyResponse$PricingPolicySaleScopeResp$SaleUnitResponse") {
	"""单位名称"""
	name:String
	"""统一社会信用代码"""
	creditCode:String
}
"""创建营销订单结果
	<AUTHOR> create 2021/1/29 17:27
"""
type VerifyMarketingOrderResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.VerifyMarketingOrderResponse") {
	"""是否创建成功
		对应false code
		"62000","商品无效: 网校不在服务期限内"
		"62001","商品无效: 网校没有开启分销服务"
		"62002","商品无效: 分销商分销关系没有启用"
		"62003","商品无效: 分销关系不在合同有效期内"
		"62004","商品无效: 分销授权地区为空"
		"62006","商品无效: 分销商品不在分销有效期内"
		"62007","商品无效: 定价策略未设置为启用"
		"62008","商品无效: 优惠无效"
		"60005","上属一级分销商在本网校的合作周期已到期"
		"60006","上属一级分销商在本网校该商品分销有效期已到期"
		"60014","未查询到上属一级分销商合同信息"
		"60015","未查询到一级分销商账号信息"
		"60016","未查询到二级分销商账号信息"
		"60027","优惠策略时间未开始"
		"60028","优惠策略时间已结束"
		"60029","未查询到上级分销授权信息"
		"60033","当前优惠策略无剩余优惠名额"
		"60037","上属一级分销商在本网校的合作状态已中止"
		"60040","上级分销商在本网校该商品分销授权状态已停止"
		"60044","定价策略未启用"
		"60045","定价策略销售地区为空"
		"60046","定价策略不存在"
		"60047","您的地区/单位不在分销报名范围内"
		"60048","优惠策略未启用"
		"60049","优惠策略不存在"
	"""
	success:Boolean!
	"""基础校验信息，如参数填写有误，购买渠道失效等"""
	message:String
	"""商品校验结果"""
	skuVerifyResponseList:[SkuVerifyResponse]
}

scalar List
