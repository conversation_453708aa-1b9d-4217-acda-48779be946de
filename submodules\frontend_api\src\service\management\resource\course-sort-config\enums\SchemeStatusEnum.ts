import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum SchemeEnum {
  /**
   * 已下架
   */
  OFF_SHELVED = 0,
  /**
   * 已上架
   */
  ON_SHELVED = 1
}

export default class SchemeStatusEnum extends AbstractEnum<SchemeEnum> {
  static enum = SchemeEnum

  constructor(status?: SchemeEnum) {
    super()
    this.current = status
    this.map.set(SchemeEnum.OFF_SHELVED, '报名关闭')
    this.map.set(SchemeEnum.ON_SHELVED, '报名开启')
  }
}
