/**
 * webpack-theme-color-replacer 插件的 resolve 配置
 * 为特定的 css 选择器（selector）配置 resolve 规则。
 *
 * key 为 css selector 值或合法的正则表达式字符串
 * 当 key 设置 css selector 值时，会匹配对应的 css
 * 当 key 设置为正则表达式时，会匹配所有满足此正则表达式的的 css
 *
 * value 可以设置为 boolean 值 false 或 一个对象
 * 当 value 为 false 时，则会忽略此 css，即此 css 不纳入 webpack-theme-color-replacer 管理
 * 当 value 为 对象时，会调用该对象的 resolve 函数，并传入 cssText（原始的 css文本） 和 cssObj（css对象）参数; resolve函数应该返
 * 回一个处理后的、合法的 css字符串（包含 selector）
 * 注意: value 不能设置为 true
 */
const cssResolve = {
  '/keyframes/': false
}

module.exports = cssResolve
