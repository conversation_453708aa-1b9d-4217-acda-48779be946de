import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/KnowledgePreexam-default'

// 枚举

// 类

/**
 * 章节响应信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class ChapterResponse {
  /**
   * 当前关系节点的编号
   */
  relationId: string
  /**
   * 父节点编号
   */
  parentRelationId: string
  /**
   * 同一级排序
   */
  sort: number
  /**
   * 编号
   */
  id: string
  /**
   * 类型{@link SyllabusStructureConst}
   */
  type: string
  /**
   * 名称
   */
  name: string
  /**
   * 代码
   */
  code: string
  /**
   * 是否可用
   */
  enabled: boolean
}

/**
 * 行业信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class IndustryResponse {
  /**
   * 所属关系节点编号
   */
  relationId: string
  /**
   * 专业列表
   */
  majorModelList: Array<MajorResponse>
  /**
   * 编号
   */
  id: string
  /**
   * 类型{@link SyllabusStructureConst}
   */
  type: string
  /**
   * 名称
   */
  name: string
  /**
   * 代码
   */
  code: string
  /**
   * 是否可用
   */
  enabled: boolean
}

/**
 * 专业映射章节列表信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class MajorMapChapterListResponse {
  /**
   * 专业关系编号
   */
  majorRelationId: string
  /**
   * 专业关系下所有章节列表
   */
  chapterList: Array<ChapterResponse>
}

/**
 * 专业信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class MajorResponse {
  /**
   * 当前节点关系编号
   */
  relationId: string
  /**
   * 同一级排序
   */
  sort: number
  /**
   * 编号
   */
  id: string
  /**
   * 类型{@link SyllabusStructureConst}
   */
  type: string
  /**
   * 名称
   */
  name: string
  /**
   * 代码
   */
  code: string
  /**
   * 是否可用
   */
  enabled: boolean
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取某个专业关系节点下一级章节子节点
   * @param majorRelationId 专业关系子节点
   * @return 章节列表
   * @param query 查询 graphql 语法文档
   * @param majorRelationId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllChildChapterByMajor(
    majorRelationId: string,
    query: DocumentNode = GraphqlImporter.findAllChildChapterByMajor,
    operation?: string
  ): Promise<Response<Array<ChapterResponse>>> {
    return commonRequestApi<Array<ChapterResponse>>(SERVER_URL, {
      query: query,
      variables: { majorRelationId },
      operation: operation
    })
  }

  /**   * 获取指定多个专业关系下章节节点及子节点
   * @param majorRelationIdList 专业关系节点列表
   * @return 专业关系节点对应章节列表
   * @param query 查询 graphql 语法文档
   * @param majorRelationIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllChildChapterByMajorList(
    majorRelationIdList: Array<string>,
    query: DocumentNode = GraphqlImporter.findAllChildChapterByMajorList,
    operation?: string
  ): Promise<Response<Array<MajorMapChapterListResponse>>> {
    return commonRequestApi<Array<MajorMapChapterListResponse>>(SERVER_URL, {
      query: query,
      variables: { majorRelationIdList },
      operation: operation
    })
  }

  /**   * 获取所有行业及专业
   * @return 行业专业关系树
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllIndustryRelationList(
    query: DocumentNode = GraphqlImporter.findAllIndustryRelationList,
    operation?: string
  ): Promise<Response<Array<IndustryResponse>>> {
    return commonRequestApi<Array<IndustryResponse>>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 添加章节
   * @param majorRelationId  专业关系编号
   * @param parentRelationId 上级章节节点编号
   * @param chapterName      章节名称
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createChapter(
    params: { majorRelationId?: string; parentRelationId?: string; chapterName?: string },
    mutate: DocumentNode = GraphqlImporter.createChapter,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 删除章节
   * @param majorRelationId 专业关系编号
   * @param relationId      节点编号
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteChapter(
    params: { majorRelationId?: string; relationId?: string },
    mutate: DocumentNode = GraphqlImporter.deleteChapter,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 导入考纲
   * @param path 模板文件路径
   * @return 是否创建导入任务成功
   * @param mutate 查询 graphql 语法文档
   * @param path 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async importBatchSyllabus(
    path: string,
    mutate: DocumentNode = GraphqlImporter.importBatchSyllabus,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: mutate,
      variables: { path },
      operation: operation
    })
  }

  /**   * 更新章节信息
   * @param originalMajorRelationId   原始专业关系编号
   * @param originalChapterRelationId 原始章节关系编号
   * @param targetParentRelationId    更新的节点编号
   * @param chapterName               章节名称
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateChapter(
    params: {
      originalMajorRelationId?: string
      originalChapterRelationId?: string
      targetParentRelationId?: string
      chapterName?: string
    },
    mutate: DocumentNode = GraphqlImporter.updateChapter,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }
}

export default new DataGateway()
