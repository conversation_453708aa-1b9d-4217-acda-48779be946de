import msCollectiveSign, { CollectiveSignQueryRequest } from '@api/ms-gateway/ms-collectivesign-v1'
import msTradeQuery, {
  BatchOrderResponse,
  CommoditySkuResponse,
  ExchangeOrderRequest,
  ExchangeOrderSortField,
  ExchangeOrderSortRequest,
  OfflineInvoiceDeliveryInfoResponse,
  OrderBasicDataRequest,
  OrderInfoRequest,
  OrderRequest,
  OrderStatisticResponse,
  ReturnOrderBasicDataRequest,
  ReturnOrderRequest,
  ReturnOrderSortField,
  ReturnOrderStatisticResponse,
  ReturnSortRequest,
  SchemeResourceResponse,
  SortPolicy,
  SubOrderInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import TradeModule from '@api/service/centre/trade/TradeModule'
import { InvoiceTypeEnum } from '@api/service/centre/trade/batch/invoice/enum/InvoiceEnum'
import { BatchOrderStatusEnum } from '@api/service/centre/trade/batch/order/enum/BatchOrderStatusList'
import { CourierDeliveryStatusEnum } from '@api/service/centre/trade/batch/order/enum/CourierDeliveryStatusList'
import { DeliveryWayEnum } from '@api/service/centre/trade/batch/order/enum/DeliveryWayList'
import { InvoiceDraftStatusEnum } from '@api/service/centre/trade/batch/order/enum/InvoiceDraftStatusList'
import { SelfFetchedDeliveryStatusEnum } from '@api/service/centre/trade/batch/order/enum/SelfFetchedDeliveryStatusList'
import RefundOrderUtils from '@api/service/centre/trade/batch/order/query/util/RefundOrderUtils'
import BatchOrderDetailDeliveryInfoVo from '@api/service/centre/trade/batch/order/query/vo/BatchOrderDetailDeliveryInfoVo'
import BatchOrderDetailInvoiceInfoVo from '@api/service/centre/trade/batch/order/query/vo/BatchOrderDetailInvoiceInfoVo'
import CommodityDetailVo from '@api/service/centre/trade/batch/order/query/vo/CommodityDetailVo'
import RefundInfoVo from '@api/service/centre/trade/batch/order/query/vo/RefundInfoVo'
import ExchangeInfoVo from '@api/service/centre/train-class/query/vo/ExchangeInfoVo'
import ExchangeClassUtils from '@api/service/centre/train-class/util/ExchangeClassUtils'
import DataResolve from '@api/service/common/utils/DataResolve'
import InvoiceListResponse from '@api/service/management/trade/batch/invoice/query/vo/InvoiceListResponse'
import { InvoiceStatusEnum } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
import OffLinePageInvoiceResponseVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
import { Page } from '@hbfe/common'

/**
 * @description
 */
class BatchOrderUtils {
  /**
   * 获取订单状态
   */
  static getBatchOrderStatus(response: BatchOrderResponse): BatchOrderStatusEnum {
    // 待报名
    if (response.basicData?.batchOrderStatus === 0) {
      return BatchOrderStatusEnum.Wait_Sign_Up
    }
    // 报名中
    if (response.basicData?.batchOrderStatus === 4) {
      return BatchOrderStatusEnum.Signing_Up
    }
    // 待付款
    if (response.basicData?.batchOrderStatus === 1 && response.basicData?.paymentStatus === 0) {
      return BatchOrderStatusEnum.Wait_Pay
    }
    // 支付中
    if (response.basicData?.batchOrderStatus === 1 && response.basicData?.paymentStatus === 1) {
      return BatchOrderStatusEnum.Paying
    }
    // 汇款凭证待审核
    if (response.payInfo?.paymentOrderStatus === 1 && response.payInfo.paymentOrderType === 2) {
      // TODO 【1.0.0版本才提供】
      return BatchOrderStatusEnum.Remittance_Voucher_Wait_Audit
    }
    // 开通中
    if (response.basicData?.batchOrderStatus === 1 && response.basicData?.paymentStatus === 2) {
      return BatchOrderStatusEnum.Opening
    }
    // 交易成功
    if (response.basicData?.batchOrderStatus === 2) {
      return BatchOrderStatusEnum.Pay_Success
    }
    // 交易关闭中
    if (response.basicData?.batchOrderStatus === 5) {
      return BatchOrderStatusEnum.Closing_Pay
    }
    // 交易关闭
    if (response.basicData?.batchOrderStatus === 3) {
      return BatchOrderStatusEnum.Close_Pay
    }
    return null
  }

  /**
   * 获取待下单的报名人次
   */
  static async getSignUpTotalCount(batchOrderNo: string): Promise<number> {
    let result = 0
    if (!batchOrderNo) return result
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    const queryParams = new CollectiveSignQueryRequest()
    queryParams.collectiveSignupNo = batchOrderNo
    const response = await msCollectiveSign.findImportCollectiveSignupCompleteSuccessDataByPage({
      request: queryParams,
      page
    })
    if (response.status?.isSuccess()) {
      result = response.data?.totalSize ?? 0
    }
    return result
  }

  /**
   * 获取批次单发票信息
   */
  static async getBatchOrderInvoiceInfoList(response: BatchOrderResponse): Promise<BatchOrderDetailInvoiceInfoVo[]> {
    const result = [] as BatchOrderDetailInvoiceInfoVo[]
    const invoiceIdList = response.invoiceApplyInfo?.invoiceIdList
    if (!DataResolve.isWeightyArr(invoiceIdList)) {
      if (response.invoiceApplyInfo?.title && response.payInfo.paymentOrderStatus == 1) {
        // 线下支付汇款凭证审核中 临时填充信息
        const temp = new BatchOrderDetailInvoiceInfoVo()
        temp.invoiceTitle = response.invoiceApplyInfo.title
        temp.invoiceType = response.invoiceApplyInfo.invoiceCategory == 3 ? 2 : 1
        temp.invoiceMethod = response.invoiceApplyInfo.invoiceType
        temp.applyDate = response.invoiceApplyInfo.appliedTime
        temp.invoiceDraftStatus = 1
        temp.invoiceNo = null
        temp.invoiceFaceInfo.taxpayerNo = response.invoiceApplyInfo.taxpayerNo
        temp.invoiceFaceInfo.phone = response.invoiceApplyInfo.phone
        temp.invoiceFaceInfo.bankName = response.invoiceApplyInfo.bankName
        temp.invoiceFaceInfo.contactEmail = response.invoiceApplyInfo.contactEmail
        temp.invoiceFaceInfo.email = response.invoiceApplyInfo.email
        temp.invoiceFaceInfo.account = response.invoiceApplyInfo.account
        temp.invoiceFaceInfo.contactPhone = response.invoiceApplyInfo.contactPhone
        temp.invoiceFaceInfo.address = response.invoiceApplyInfo.address
        temp.invoiceFaceInfo.title = response.invoiceApplyInfo.title
        temp.invoiceFaceInfo.remark = response.invoiceApplyInfo.remark
        temp.invoiceFaceInfo.businessLicensePath = response.invoiceApplyInfo.businessLicensePath
        temp.invoiceFaceInfo.accountOpeningLicensePath = response.invoiceApplyInfo.accountOpeningLicensePath
        temp.deliveryInfo.deliveryWay = response.invoiceApplyInfo.shippingMethod
        temp.deliveryInfo.distributeConsignee = response.invoiceApplyInfo.deliveryAddress.consignee
        temp.deliveryInfo.distributePhone = response.invoiceApplyInfo.deliveryAddress.phone
        temp.deliveryInfo.distributeAddress = response.invoiceApplyInfo.deliveryAddress.address
        temp.deliveryInfo.region = response.invoiceApplyInfo.deliveryAddress.region
        temp.deliveryInfo.selfFetchTime = response.invoiceApplyInfo.takePoint.pickupTime
        temp.deliveryInfo.selfFetchedPoint = response.invoiceApplyInfo.takePoint.pickupLocation
        temp.deliveryInfo.selfFetchedDeliveryStatus = 1
        temp.deliveryInfo.remark = response.invoiceApplyInfo.takePoint.remark
        result.push(temp)
      }
      return result
    }
    const invoiceIds = invoiceIdList
    const way =
      response.invoiceApplyInfo?.invoiceMethod === 1
        ? InvoiceTypeEnum.ONLINE
        : response.invoiceApplyInfo?.invoiceMethod === 2
        ? InvoiceTypeEnum.OFFLINE
        : null
    if (!way) return result
    const queryRemote = TradeModule.batchOrderFactory.queryBatchInvoiceFactory.QueryBatchInvoice
    const invoiceInfoList = await Promise.all(
      invoiceIds?.map(async (item) => {
        return await queryRemote.queryInvoiceDetail(item, way)
      })
    )
    invoiceInfoList.forEach((item) => {
      if (way === InvoiceTypeEnum.ONLINE) {
        result.push(BatchOrderDetailInvoiceInfoVo.fromOnline(item as InvoiceListResponse))
      }
      if (way === InvoiceTypeEnum.OFFLINE) {
        result.push(BatchOrderDetailInvoiceInfoVo.fromOffline(item as OffLinePageInvoiceResponseVo))
      }
    })
    return result
  }

  /**
   * 【线上】获取开票状态
   */
  static getOnlineInvoiceDraftStatus(response: InvoiceListResponse): InvoiceDraftStatusEnum {
    // 未开票已冻结展示已已冻结不展示已作废
    if (response.invoiceStatus === InvoiceStatusEnum.NOTPTOOPEN && response.invoiceFreezeStatus) {
      return InvoiceDraftStatusEnum.Frozen
    }
    // 已作废
    if (response.useless) {
      return InvoiceDraftStatusEnum.Invalid
    }
    // 冻结中
    if (response.invoiceFreezeStatus) {
      return InvoiceDraftStatusEnum.Frozen
    }
    // 未开票
    if (response.invoiceStatus === 0) {
      return InvoiceDraftStatusEnum.Wait_For_Draft
    }
    // 开票中
    if (response.invoiceStatus === 1) {
      return InvoiceDraftStatusEnum.Drafting
    }
    // 开票成功
    if (response.invoiceStatus === 2) {
      return InvoiceDraftStatusEnum.Complete
    }
    return null
  }

  /**
   * 【线上】获取开票状态
   */
  static getOffLineInvoiceDraftStatus(response: OffLinePageInvoiceResponseVo): InvoiceDraftStatusEnum {
    // 未开票已冻结展示已已冻结不展示已作废
    if (response.invoiceStatus === InvoiceStatusEnum.NOTPTOOPEN && response.invoiceFreezeStatus) {
      return InvoiceDraftStatusEnum.Frozen
    }
    // 已作废
    if (response.useless) {
      return InvoiceDraftStatusEnum.Invalid
    }
    // 冻结中
    if (response.invoiceFreezeStatus) {
      return InvoiceDraftStatusEnum.Frozen
    }
    // 未开票
    if (response.invoiceStatus === InvoiceStatusEnum.NOTPTOOPEN) {
      return InvoiceDraftStatusEnum.Wait_For_Draft
    }
    // 开票中
    if (response.invoiceStatus === InvoiceStatusEnum.OPENING) {
      return InvoiceDraftStatusEnum.Drafting
    }
    // 开票成功
    if (response.invoiceStatus === InvoiceStatusEnum.OPEMSUCCESS) {
      return InvoiceDraftStatusEnum.Complete
    }
    return null
  }

  /**
   * 获取配送信息
   */
  static getDeliveryInfo(response: OfflineInvoiceDeliveryInfoResponse): BatchOrderDetailDeliveryInfoVo {
    const deliveryInfo = new BatchOrderDetailDeliveryInfoVo()
    if (!response) return deliveryInfo
    deliveryInfo.deliveryWay =
      response.shippingMethod === 2
        ? DeliveryWayEnum.Courier
        : response.shippingMethod === 1
        ? DeliveryWayEnum.Self_Fetched
        : null
    // 填充快递信息
    if (deliveryInfo.deliveryWay === DeliveryWayEnum.Courier) {
      deliveryInfo.courierDeliveryStatus =
        response.deliveryStatus === 3
          ? CourierDeliveryStatusEnum.Complete_Courier
          : CourierDeliveryStatusEnum.Wait_For_Courier
      deliveryInfo.deliveryCompany = response.express?.expressCompanyName ?? ''
      deliveryInfo.deliveryNo = response.express?.expressNo ?? ''
      deliveryInfo.queryWebsite = ''
      deliveryInfo.distributeConsignee = response.deliveryAddress?.consignee ?? ''
      deliveryInfo.distributeAddress = response.deliveryAddress?.address ?? ''
      deliveryInfo.distributePhone = response.deliveryAddress?.phone ?? ''
    }
    // 填充自取信息
    if (deliveryInfo.deliveryWay === DeliveryWayEnum.Self_Fetched) {
      deliveryInfo.selfFetchedDeliveryStatus =
        response.deliveryStatus === 2
          ? SelfFetchedDeliveryStatusEnum.Complete_Self_Fetched
          : SelfFetchedDeliveryStatusEnum.Wait_For_Self_Fetched
      deliveryInfo.selfFetchedPoint = response.takePoint?.pickupLocation ?? ''
      deliveryInfo.selfFetchTime = response.takePoint?.pickupTime ?? ''
      deliveryInfo.remark = response.takePoint?.remark ?? ''
    }
    return deliveryInfo
  }

  /**
   * 获取主单订单退款状态
   */
  static async getSubOrderRefundInfo(subOrderNoList: string[], batchOrderNo: string): Promise<RefundInfoVo> {
    const result = new RefundInfoVo()
    if (!DataResolve.isWeightyArr(subOrderNoList)) return result
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    const queryParams = new ReturnOrderRequest()
    queryParams.subOrderInfo = new SubOrderInfoRequest()
    queryParams.subOrderInfo.subOrderNoList = subOrderNoList
    queryParams.subOrderInfo.orderInfo = new OrderInfoRequest()
    queryParams.subOrderInfo.orderInfo.batchOrderNoList = [batchOrderNo]
    const sortOption = new ReturnSortRequest()
    sortOption.field = ReturnOrderSortField.APPLIED_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(sortOption) as ReturnSortRequest[]
    const response = await msTradeQuery.pageReturnOrderInMyself({
      page,
      request: queryParams,
      sort: sortRequest
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      const newest = response.data.currentPageData[0]
      result.refundStatusInfoList = RefundOrderUtils.getRefundStatusInfoList(newest)
      result.refundStatus = RefundOrderUtils.getRefundStatus(newest)
      result.fromBatchRefundStatus = newest.batchReturnOrder?.basicData?.batchReturnOrderStatus
    }
    return result
  }

  /**
   * 获取子单换货信息
   * @param {string[]} subOrderNoList - 子单id集合
   * @param {string} batchOrderNo - 批次订单号
   */
  static async getSubOrderExchangeInfo(subOrderNoList: string[], batchOrderNo: string): Promise<ExchangeInfoVo> {
    const result = new ExchangeInfoVo()
    if (!DataResolve.isWeightyArr(subOrderNoList)) return result
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    const queryParams = new ExchangeOrderRequest()
    queryParams.subOrderNoList = subOrderNoList
    queryParams.batchOrderNoList = [batchOrderNo]
    const sortOption = new ExchangeOrderSortRequest()
    sortOption.field = ExchangeOrderSortField.APPLIED_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(sortOption) as ExchangeOrderSortRequest[]
    const response = await msTradeQuery.pageExchangeOrderInMySelf({
      page,
      request: queryParams,
      sort: sortRequest
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      const newest = response.data.currentPageData[0]
      result.exchangeStatus = ExchangeClassUtils.getExchangeStatus(newest)
      result.exchangeStatusInfoList = ExchangeClassUtils.getExchangeStatusInfoList(newest)
    }
    return result
  }

  /**
   * 获取批次单主单统计信息
   */
  static async getMainOrderStatistic(batchOrderNo: string): Promise<OrderStatisticResponse> {
    const result = new OrderStatisticResponse()
    if (!batchOrderNo) return result
    const queryParams = new OrderRequest()
    queryParams.orderBasicData = new OrderBasicDataRequest()
    queryParams.orderBasicData.batchOrderNoList = [batchOrderNo]
    queryParams.orderBasicData.orderStatusList = [1, 2, 3]
    queryParams.orderBasicData.orderPaymentStatusList = [0, 1, 2]
    const response = await msTradeQuery.statisticOrderInMyself(queryParams)
    if (response.status?.isSuccess()) {
      result.totalOrderCount = response.data?.totalOrderCount ?? 0
      result.totalOrderAmount = response.data?.totalOrderAmount ?? 0
    }
    return result
  }

  /**
   * 获取批次已退款统计信息
   */
  static async getBatchOrderRefundedStatistic(batchOrderNo: string): Promise<ReturnOrderStatisticResponse> {
    const result = new ReturnOrderStatisticResponse()
    if (!batchOrderNo) return result
    const queryParams = new ReturnOrderRequest()
    queryParams.subOrderInfo = new SubOrderInfoRequest()
    queryParams.subOrderInfo.orderInfo = new OrderInfoRequest()
    queryParams.subOrderInfo.orderInfo.batchOrderNoList = [batchOrderNo]
    queryParams.basicData = new ReturnOrderBasicDataRequest()
    // 只查询已退款成功
    queryParams.basicData.returnOrderStatus = [8, 9, 10]
    const response = await msTradeQuery.statisticReturnOrderInMyself(queryParams)
    if (response.status?.isSuccess()) {
      result.totalRefundAmount = response.data?.totalRefundAmount ?? 0
      result.totalReturnOrderCount = response.data?.totalReturnOrderCount ?? 0
    }
    return result
  }

  /**
   * 填充商品信息
   */
  static fillCommodityInfo(response: CommoditySkuResponse): CommodityDetailVo {
    const commodity = new CommodityDetailVo()
    if (!response) return commodity
    const resource = response.resource as SchemeResourceResponse
    commodity.schemeId = resource?.schemeId ?? ''
    commodity.commoditySkuId = response.commoditySkuId ?? ''
    commodity.schemeName = response.saleTitle ?? ''
    commodity.skuProperty.year.skuPropertyName = response.skuProperty?.year?.skuPropertyValueName ?? ''
    const skuNames: string[] = []
    if (response.skuProperty?.province?.skuPropertyValueName)
      skuNames.push(response.skuProperty?.province?.skuPropertyValueName)
    if (response.skuProperty?.city?.skuPropertyValueName)
      skuNames.push(response.skuProperty?.city?.skuPropertyValueName)
    if (response.skuProperty?.county?.skuPropertyValueName)
      skuNames.push(response.skuProperty?.county?.skuPropertyValueName)
    commodity.skuProperty.region.skuPropertyName = skuNames.join('/')
    commodity.skuProperty.industry.skuPropertyName = response.skuProperty?.industry?.skuPropertyValueName ?? ''
    commodity.skuProperty.subjectType.skuPropertyName = response.skuProperty?.subjectType?.skuPropertyValueName ?? ''
    commodity.skuProperty.trainCategory.skuPropertyName =
      response.skuProperty?.trainingCategory?.skuPropertyValueName ?? ''
    commodity.skuProperty.trainMajor.skuPropertyName =
      response.skuProperty?.trainingProfessional?.skuPropertyValueName ?? ''
    commodity.skuProperty.trainingObject.skuPropertyName =
      response.skuProperty?.trainingObject?.skuPropertyValueName ?? ''
    commodity.skuProperty.positionCategory.skuPropertyName =
      response.skuProperty?.positionCategory?.skuPropertyValueName ?? ''
    commodity.skuProperty.jobLevel.skuPropertyName = response.skuProperty?.jobLevel?.skuPropertyValueName ?? ''
    commodity.skuProperty.jobCategory.skuPropertyName = response.skuProperty?.jobCategory?.skuPropertyValueName ?? ''
    commodity.skuProperty.technicalGrade.skuPropertyName =
      response.skuProperty?.technicalGrade?.skuPropertyValueName ?? ''
    commodity.period = resource?.period ?? 0
    commodity.price = response.price ?? 0
    // 转换 学科、学段
    commodity.skuProperty.learningPhase.skuPropertyName =
      response.skuProperty?.learningPhase?.skuPropertyValueName ?? ''
    commodity.skuProperty.discipline.skuPropertyName = response.skuProperty?.discipline?.skuPropertyValueName ?? ''
    commodity.skuProperty.certificatesType.skuPropertyName =
      response.skuProperty?.certificatesType?.skuPropertyValueName ?? ''
    commodity.skuProperty.practitionerCategory.skuPropertyName =
      response.skuProperty?.practitionerCategory?.skuPropertyValueName ?? ''
    // end
    return commodity
  }
}

export default BatchOrderUtils
