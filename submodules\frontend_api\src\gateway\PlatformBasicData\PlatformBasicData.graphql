schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取施教机构单位id（通过域名）
		该接口只返回id和name
		@param domain
		@return
	"""
	findTeachUnitByDomain(domain:String):Void @NotAuthenticationRequired
	"""通过施教机构分页获取总数
		@return
	"""
	findTeachUnitCount:Long! @NotAuthenticationRequired
	findTeachUnitListByIds(ids:[String]):Void
	"""获取启用的施教机构分页
		@param page
		@return
	"""
	findTeachUnitPage(page:Page,queryParam:String):Void @NotAuthenticationRequired
	"""获取当前管理员所属施教机构分页
		@param page
		@param param
		@return
	"""
	findTeachUnitPageByAdmin(page:Page,param:String):Void
	"""获取所有组安全对象
		@return
	"""
	getAllPermission:[SecurityObjectGroupDto]
	"""获取系统的所有角色信息"""
	getAllRoles:[RoleDTO]
	"""当前请求系统时间
		@return
	"""
	getCurrentDate:DateTime @NotAuthenticationRequired
	"""当前请求上下文信息
		@return
	"""
	getCurrentMicroContext:MicroContext @NotAuthenticationRequired
	"""获取当前角色安全对象
		@return
	"""
	getCurrentUserSecurityGroup:[SecurityObjectGroupDto]
	"""获取系统中所有可用的角色"""
	getEnabledRoles:[RoleDTO]
	"""根据roleId获取安全对象组
		@return
	"""
	getPermissionByRoleId(roleId:String):[SecurityObjectGroupDto]
	"""获取安全对象--编辑角色
		@param roleId
		@return
	"""
	getPermissionForEditRole(roleId:String):RoleEditDto
	"""获取二维码图片
		@param params
		@return
	"""
	getPromoteQRCode(params:PromoteQRCodeRequestDTO):String @NotAuthenticationRequired
	"""根据地区id获取地区信息"""
	getRegionById(id:String):BaseRegionDTO @NotAuthenticationRequired
	"""根据地区路径查询地区信息"""
	getRegionInfoByPath(path:String):RegionInfoDTO @NotAuthenticationRequired
	"""根据地区id获取该地区树"""
	getRegionTree(id:String):BaseRegionTreeDTO @NotAuthenticationRequired
	"""根据地区id集合批量获取地区信息"""
	getRegionsByIds(ids:[String]):[BaseRegionDTO] @NotAuthenticationRequired
	"""根据id获取角色
		@return
	"""
	getRoleById(roleId:String):RoleDTO
	"""获取短码地址
		@param url
		@return
	"""
	getShortUrl(url:String):String @NotAuthenticationRequired
	"""获取专题分页
		@param type       专题类型
		@param servicerId 服务商Id(可选参数，不传则使用上下文服务商Id)
		@return
	"""
	getSpecialTopicPage(page:Page,type:SpecialTopicTypeEnums,servicerId:String):LazyCommodityDTOPage @page(for:"LazyCommodityDTO") @NotAuthenticationRequired
	"""根据父级地区id获取子级地区
		@param parentId
		@return
	"""
	getSubRegionListByParentId(parentId:String):[BaseRegionDTO] @NotAuthenticationRequired
	"""获取施教机构
		@param unitId
		@return
	"""
	getTeachUnit(unitId:String):Void @NotAuthenticationRequired
	"""判断账号是否存在
		@param check  0：忽略该字段|1：false
		@param field
		@return 存在则返回false
	"""
	isRoleExist(check:Int!,field:String):Boolean!
	"""获取角色分页
		@param page
		@return
	"""
	pageRolesByQuery(page:Page):RoleDTOPage @page(for:"RoleDTO")
	"""异步任务处理结果查询
		@param page
		@param param
		@return
	"""
	pageUserJob(page:Page,param:UserJobParamDTO):JobLogDTOPage @page(for:"JobLogDTO")
}
type Mutation {
	"""添加用户角色信息
		@param accountId 账户id
		@param roleIds   角色id集合
	"""
	addUserOwnRoles(accountId:String!,roleIds:[String]!):Void
	"""删除角色
		@param
		@return
	"""
	deleteRole(roleId:String):Void
	getPermission(nodeSelectedIdArray:[String],itemSelectedIdArray:[String],rootSelectedIdArray:[String]):[String]
	"""移除用户角色信息
		@param accountId
		@param accountRoleIds 账户角色关系id集合
	"""
	removeUserOwnRoles(accountId:String!,accountRoleIds:[String]!):Void
	"""保存角色
		@param
		@return
	"""
	saveRole(roleDto:RolePermissionDto):Boolean!
}
"""任务查询参数
	@author: eleven
	@date: 2020/4/14
"""
input UserJobParamDTO @type(value:"com.fjhb.btpx.integrative.gateway.graphql.dto.UserJobParamDTO") {
	"""任务组名"""
	group:String
	"""任务当前的运行状态"""
	userJobState:UserJobState
	"""开始处理时间"""
	startDate:String
	"""处理完成时间"""
	endDate:String
}
"""二维码图片请求"""
input PromoteQRCodeRequestDTO @type(value:"com.fjhb.btpx.integrative.service.basicdata.dto.PromoteQRCodeRequestDTO") {
	"""page参数"""
	page:String!
	"""scene参数"""
	scene:String!
	"""width参数"""
	width:String!
}
input RoleCreateDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.role.dto.RoleCreateDto") {
	id:String
	name:String
	description:String
}
input RolePermissionDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.role.dto.RolePermissionDto") {
	roleMessage:RoleCreateDto
	nodeSelectedIdArray:[String]
	itemSelectedIdArray:[String]
	rootSelectedIdArray:[String]
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""异步任务日志
	@author: eleven
	@date: 2020/4/14
"""
type JobLogDTO @type(value:"com.fjhb.btpx.integrative.gateway.graphql.dto.JobLogDTO") {
	"""异步任务的主键ID"""
	id:String
	"""执行状态"""
	state:UserJobState
	"""异步任务的名称"""
	name:String
	"""异步任务的组名"""
	group:String
	"""操作人编号"""
	operatorId:String
	"""操作人
		比较稳定掉接口，直接返回
	"""
	operator:String
	"""处理总数量"""
	rowSum:Long!
	"""成功数"""
	rowSuccess:Long!
	"""失败数"""
	rowFail:Long!
	"""源文件地址"""
	originalUrl:String
	"""处理结果文件
		导入：导入成功的数据文件
		导出：导出的数据文件
	"""
	resultUrl:String
	"""处理失败的文件地址(导入任务的字段)"""
	failUrl:String
	"""开始处理时间"""
	startDate:String
	"""处理完成时间"""
	endDate:String
	"""导入结果"""
	result:AsyncExecuteResultEnum
	"""异常原因"""
	message:String
}
"""Author:FangKunSen
	Time:2021-02-02,19:06
"""
type LazyCommodityDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.LazyCommodityDTO") {
	"""商品id"""
	commodityId:String
	"""商品名"""
	commoditySaleTitle:String
	"""方案id"""
	schemeId:String
	"""方案名"""
	schemeName:String
	"""期别id"""
	stageId:String
	"""期别名"""
	stageName:String
	"""期数id"""
	issueId:String
	"""期数名"""
	issueName:String
	"""方案封面图路径"""
	schemePicturePath:String
	"""商品销售卖点"""
	commoditySellingPoint:String
	"""商品描述介绍"""
	commodityDescription:String
	"""商品描述介绍小程序"""
	commodityDescriptionUniApp:String
	"""商品是否开启web渠道(即是否开放报名)"""
	allowWebChannel:Boolean
	"""商品是否开启导入开通渠道"""
	allowImportChannel:Boolean
	"""商品是否开启集体缴费渠道"""
	allowBatchChannel:Boolean
	"""机构ID"""
	trainingInstitutionId:String
	"""机构名称"""
	trainingInstitutionName:String
	"""机构简介"""
	trainingInstitutionAbouts:String
	"""课件供应商id"""
	coursewareSupplierId:String
	"""课件供应商名称"""
	coursewareSupplierName:String
	"""培训类别id(路径)"""
	trainingCategoryId:String
	"""培训类别名称"""
	trainingCategoryName:String
	"""培训类别 名称路径"""
	trainingCategoryNamePath:String
	"""工种id"""
	workTypeId:String
	"""工种name"""
	workTypeName:String
	"""适用人群名"""
	suitableCrowNames:[String]
	"""学时"""
	period:Double!
	"""价格"""
	price:Double!
	"""商品状态："UPED"上架|"DOWNED"下架"""
	commodityState:String
	"""商品是否有效"""
	commodityAvailable:Boolean!
	"""培训时间 起"""
	trainingTimeStart:DateTime
	"""培训时间 止"""
	trainingTimeEnd:DateTime
	"""开通人数"""
	openNumber:Long!
	"""创建者id"""
	creatorId:String
	"""创建者名"""
	creatorName:String
	"""上架时间"""
	onShelveTime:DateTime
	"""下架时间"""
	offShelveTime:DateTime
	"""发布时间"""
	publishTime:DateTime
	"""最后修改时间"""
	lastUpdateTime:DateTime
	"""价格变更记录"""
	priceChangeHistoryDTOS:[PriceChangeHistoryDTO]
	"""渠道商"""
	channelVendorList:[ServicerDTO]
	"""商品总评价"""
	evaluation:Double!
}
"""Author:FangKunSen
	Time:2021-02-02,19:15
"""
type PriceChangeHistoryDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.PriceChangeHistoryDTO") {
	"""变更时间"""
	changeTime:DateTime
	"""操作者"""
	operatorId:String
	"""操作者名字"""
	operatorName:String
	"""原价格"""
	oldPrice:Double!
	"""新价格"""
	newPrice:Double!
}
"""@Description 服务商
	<AUTHOR>
	@Date 15:30 2021/10/21
"""
type ServicerDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.ServicerDTO") {
	"""服务商id"""
	servicerId:String
	"""服务商名称"""
	servicerName:String
}
"""专题类型"""
enum SpecialTopicTypeEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.specialtopicconfig.enums.SpecialTopicTypeEnums") {
	"""社区矫正专题"""
	SHE_QU_JIAO_ZHENG
}
type RoleDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.role.dto.RoleDTO") {
	"""角色ID"""
	id:String
	"""角色级别值"""
	levelValue:Int!
	"""角色名称"""
	name:String
	"""角色描述"""
	description:String
	"""创建方式 | 1:自建; 2:导入"""
	createType:Int!
	"""数据类型 | 1:普通; 2:内置"""
	dataType:Int!
	"""创建人ID"""
	creatorId:String
	"""创建时间"""
	createDate:DateTime
	"""是否可用"""
	available:Boolean!
}
type RoleEditDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.role.dto.RoleEditDto") {
	"""安全对象组集合"""
	securityObjectGroupList:[SecurityObjectGroupDto]
	"""第三级安全对象id集合
		旧平台的东西，不建议写死层级数，请使用RoleEditDto#securityObjectGroupList
	"""
	nodeSelectedIdArray:[String]
	"""第二级安全对象id集合
		旧平台的东西，不建议写死层级数，请使用RoleEditDto#securityObjectGroupList
	"""
	itemSelectedIdArray:[String]
	"""第一级安全对象id集合
		旧平台的东西，不建议写死层级数，请使用RoleEditDto#securityObjectGroupList
	"""
	rootSelectedIdArray:[String]
}
type SecurityObjectGroupDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.role.dto.SecurityObjectGroupDto") {
	id:String
	name:String
	"""URL内容"""
	urlContent:String
	url:String
	isSelected:Boolean!
	children:[SecurityObjectGroupDto]
	parentId:String
	sort:Int!
}
"""基础地区，只包含子级地区信息和地区路径
	<AUTHOR> create 2019/10/11 9:21
"""
type BaseRegionDTO @type(value:"com.fjhb.btpx.platform.service.region.dto.BaseRegionDTO") {
	"""地区 id"""
	id:String
	"""地区 名称"""
	name:String
	"""上级区域ID|顶级空，为空默认为-1"""
	parentId:String
	"""区域路径"""
	regionPath:String
	"""排序"""
	sort:Int!
	"""版本号"""
	version:String
	"""是否可用"""
	available:Boolean!
}
type BaseRegionTreeDTO @type(value:"com.fjhb.btpx.platform.service.region.dto.BaseRegionTreeDTO") {
	children:[BaseRegionTreeDTO]
	"""地区 id"""
	id:String
	"""地区 名称"""
	name:String
	"""上级区域ID|顶级空，为空默认为-1"""
	parentId:String
	"""区域路径"""
	regionPath:String
	"""排序"""
	sort:Int!
	"""版本号"""
	version:String
	"""是否可用"""
	available:Boolean!
}
type RegionInfoDTO @type(value:"com.fjhb.btpx.platform.service.region.dto.RegionInfoDTO") {
	"""地区路径"""
	regionPath:String
	"""省份id"""
	provinceId:String
	"""省份名称"""
	provinceName:String
	"""城市id"""
	cityId:String
	"""城市名称"""
	cityName:String
	"""区、县id"""
	countyId:String
	"""区、县名称"""
	countyName:String
}
enum UserJobState @type(value:"com.fjhb.job.commons.model.helper.UserJobState") {
	deleted
	deletedAndUnscheduled
	unstart
	running
	paused
	interrupted
	resumed
	deletedFromScheduler
	executed
	fail
	addedToScheduler
	toExecuted
	deleteAllRelevance
	deleteSchedulerRelevance
	rescheduled
	updated
	updatedAndRescheduled
}
type DataRouterIdentity @type(value:"com.fjhb.micro.context.v1.DataRouterIdentity") {
	dataPlatformVersionId:String
	dataProjectId:String
}
type HttpIdentity @type(value:"com.fjhb.micro.context.v1.HttpIdentity") {
	ip:String
	domain:String
	requestUrl:String
}
type MicroContext @type(value:"com.fjhb.micro.context.v1.MicroContext") {
	sequenceNo:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	servicerProvider:ServicerProvider
	userIdentity:UserIdentity
	dataRouterIdentity:DataRouterIdentity
	httpIdentity:HttpIdentity
}
type ServicerProvider @type(value:"com.fjhb.micro.context.v1.ServicerProvider") {
	unitId:String
	servicerType:Int!
	servicerId:String
}
type UserIdentity @type(value:"com.fjhb.micro.context.v1.UserIdentity") {
	accountId:String
	rootAccountId:String
	accountType:Int
	userId:String
}
enum AsyncExecuteResultEnum @type(value:"com.fjhb.platformstandard.common.async.core.enumeration.AsyncExecuteResultEnum") {
	SUCCESS
	FAIL
	PART_SUCCESS
	ERROR
}

scalar List
type LazyCommodityDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [LazyCommodityDTO]}
type RoleDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [RoleDTO]}
type JobLogDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [JobLogDTO]}
