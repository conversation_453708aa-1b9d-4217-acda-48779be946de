import moment from 'moment'
import PlaceholderConstant from '@api/service/common/scheme/model/schemeDto/common/PlaceholderConstant'
/**
 * @description 日期范围
 */
class DateRange {
  /**
   * 开始日期
   */
  startDate = ''
  /**
   * 结束日期
   */
  endDate = ''

  /**
   * 提供给日期选择器使用
   */
  get dateRange() {
    return [this.startDate ?? '', this.endDate ?? '']
  }

  /**
   * 提供给日期选择器使用
   * @param value 时间范围参数
   */
  set dateRange(value: string[]) {
    if (value) {
      this.startDate = value[0] ?? ''
      this.endDate = value[1] ?? ''
    } else {
      this.startDate = ''
      this.endDate = ''
    }
  }

  constructor(startDate?: string, endDate?: string) {
    if (startDate) {
      this.startDate = startDate
    }
    if (endDate) {
      this.endDate = endDate
    }
  }

  /**
   * 格式化开始日期
   */
  get formatStartDate() {
    return this.startDate ? moment(new Date(this.startDate)).format(PlaceholderConstant.momentFullStartTimeFormat) : ''
  }

  /**
   * 格式化结束日期
   */
  get formatEndDate() {
    return this.endDate ? moment(new Date(this.endDate)).format(PlaceholderConstant.momentFullEndTimeFormat) : ''
  }
}

export default DateRange
