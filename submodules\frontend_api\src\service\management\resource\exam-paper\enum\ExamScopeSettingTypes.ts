import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 试卷出题范围
 */
export enum QuestionScopeSettingTypes {
  'LibraryQuestionScopeSetting' = 1,
  'UserCourseScopeSetting' = 2,
  'LibraryFixedQuestionExtractSetting' = 3
}

class ExamScopeSettingTypes extends AbstractEnum<QuestionScopeSettingTypes> {
  static enum = QuestionScopeSettingTypes

  constructor(status?: QuestionScopeSettingTypes) {
    super()
    this.current = status
    this.map.set(QuestionScopeSettingTypes.LibraryQuestionScopeSetting, '按题库出题')
    // 智能抽题
    this.map.set(QuestionScopeSettingTypes.UserCourseScopeSetting, '按学员课程id出题')
    this.map.set(QuestionScopeSettingTypes.LibraryFixedQuestionExtractSetting, '按照题库指定数量出题')
  }
}

export default new ExamScopeSettingTypes()
