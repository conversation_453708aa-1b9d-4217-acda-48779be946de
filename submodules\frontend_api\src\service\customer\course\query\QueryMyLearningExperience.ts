import MyLearningExperience from '@api/service/customer/course/query/vo/MyLearningExperience'
import QueryMyLearningExperiencePageParam from '@api/service/customer/course/query/vo/QueryMyLearningExperiencePageParam'
import { LearningFeelEnum } from '@api/service/management/train-class/mutation/Enum/LearningFeelEnum'
import ActivityManangeModel from '@api/service/customer/course/query/vo/ActivityManangeModel'
import MsMySchemeQueryFrontGatewayCourseLearningForeStage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { SchemeConfigResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import CourseLearningForestage, {
  StudentLearningExperienceRequest,
  StudentLearningExperienceStatus,
  StudentLearningExperienceSortRequest,
  SortTypeEnum1,
  StudentLearningExperienceSortEnum,
  StudentSchemeLearningRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'
import { Page } from '@hbfe/common'
import { LearningExperienceJoinStatusEnum, LearningExperienceTypeEnum } from './enum/LearningExperienceStatusEnum'
import KnowledgeDataGateway, { QueryUnCompleteLearningExperienceTopicRequest } from '@api/ms-gateway/ms-knowledge-v1'
// import { AnswerMethodEnum, LearningExperienceEnum } from '@api/service/management/activity/enum/ActivityEnum'
import MsCourseLearningQueryFrontGatewayCourseLearningForestage, {
  StudentCourseLearningRequest,
  StudentCourseRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
class QueryMyLearningExperience {
  private studentNo: string

  constructor(studentNo: string) {
    this.studentNo = studentNo
  }
  // 是否纳入考核
  isExamed = false
  // 是否达标
  isPassed = false
  // 班级心得前置条件
  classCondition: LearningFeelEnum = null
  // 课程心得前置条件
  courseCondition: LearningFeelEnum = null
  // 学员已参加且通过的学习心得数
  learningExperiencePassNum = 0
  // 方案考核要求中至少需参加的心得数
  learningExperienceNeedNum = 0
  // 展示名称
  showName = ''
  /**
   *  前置条件校验结果
   *  1001 学习心得列表中有必选心得，且不止有课程心得（只有班级心得或同时有班级心得和课程心得）
   *  1002 学习心得列表中有必选心得，且只有课程心得
   *  1003 学习心得列表中无必选心得，列表中不止有课程心得（只有班级心得或同时有班级心得和课程心得）
   *  1004 学习心得列表中无必选心得，列表中只有课程心得
   *  1005 学习心得列表中参加的均已通过，但已通过数仍不足需通过数（包含未合格的学员，因管理端课程移除、业务查询删除课程导致的心得数不足）
   * */
  code = 0
  // 查询学习心得列表
  async queryList(page: Page, params: QueryMyLearningExperiencePageParam): Promise<MyLearningExperience[]> {
    const request = new StudentLearningExperienceRequest()
    request.studentLearning = new StudentSchemeLearningRequest()
    request.studentLearning.studentNos = [params.id]
    request.studentLearning.schemeIds = [params.schemeId]
    if (params.courseId) {
      request.courseId = params.courseId
    }
    const sorts = new Array<StudentLearningExperienceSortRequest>()
    sorts.push(new StudentLearningExperienceSortRequest())
    sorts[0].sortType = SortTypeEnum1.ASC
    sorts[0].sort = StudentLearningExperienceSortEnum.IS_REQUIRED
    // 1已参加 2未参加
    let res
    if (params.status === 1) {
      res = await CourseLearningForestage.pageLearningExperienceParticipatedInStudent({ page, request, sorts })
    } else if (params.status === 2) {
      res = await CourseLearningForestage.pageLearningExperienceNotParticipatedInStudent({ page, request, sorts })
    } else {
      return
    }

    let list = new Array<MyLearningExperience>()
    if (res.status.isSuccess()) {
      page.totalSize = res.data.totalSize
      page.pageSize = res.data.pageSize
      try {
        const courseIdList: string[] = []
        res.data.currentPageData.map((item, index) => {
          const temp = MyLearningExperience.from(item.studentLearningExperience)
          // 心得模型外参数组装
          temp.outLineId = item.outLineId
          temp.detail.ActivityManange.outLineId = item.outLineId
          temp.detail.ActivityManange.studentNo = this.studentNo
          if (params.status == 2) {
            temp.submitNum = item.submitLimitCount
          } else if (params.status == 1) {
            if (item.submitLimitCount == -1) {
              temp.submitNum = item.submitLimitCount
            } else {
              temp.submitNum = item.submitLimitCount - item.count
            }
          }
          if (item.count > 0) {
            temp.joinStatus = LearningExperienceJoinStatusEnum.join
          }
          if (item.studentLearningExperience.experienceType == 'COURSE') {
            const courseId = item.studentLearningExperience.experienceTypeInfo[0]
            temp.detail.ActivityManange.courseDetail.id = courseId
            courseIdList.push(courseId)
          }
          list.push(temp)
        })

        if (courseIdList.length) {
          const courseList = await new QueryCourse().queryCourseByIdListPage(courseIdList)
          list = list.map((item) => {
            courseList.map((sitem) => {
              if (item.type == 2 && sitem.id == item.detail.ActivityManange.courseDetail.id) {
                // 课程
                item.detail.ActivityManange.courseDetail.name = sitem.name
              }
            })
            return item
          })
        }
        // 判断学习心得下是否只有课程心得主题
        const typeRes = await this.queryTopicType(params.schemeId, params.id)
        if (typeRes.code != 1001) {
          // 未参加课程id列表
          const uncommittedCourseList = await this.queryCourseList(params.id)
          if (list.length && list[0].isMustSelect && uncommittedCourseList.length) {
            // 有必选心得
            if (typeRes.code == 1002) {
              // 只有课程心得
              this.code = 1002
            } else if (typeRes.code == 1003) {
              // 不止有课程心得
              this.code = 1001
            }
          } else if (list.length && !list[0].isMustSelect && uncommittedCourseList.length) {
            // 无必选心得
            if (typeRes.code == 1002) {
              // 只有课程心得
              this.code = 1004
            } else if (typeRes.code == 1003) {
              // 不止有课程心得
              this.code = 1003
            }
          }
        }
        if (!this.code) {
          // 即使学习心得列表中参加的均已通过，但已通过数仍不足需通过数
          const pendingPage = new Page()
          pendingPage.pageNo = 1
          pendingPage.pageSize = 1
          const pendingRes = await CourseLearningForestage.pageLearningExperienceNotParticipatedInStudent({
            page: pendingPage,
            request,
            sorts
          })
          if (pendingRes.data?.totalSize == 0) {
            const pendingRes2 = await CourseLearningForestage.pageLearningExperienceParticipatedInStudent({
              page: pendingPage,
              request,
              sorts
            })
            if (pendingRes.status.isSuccess() && pendingRes2.status.isSuccess()) {
              if (params.schemeId) {
                await this.requestClassConfig(params.schemeId)
              }
              if (pendingRes.data.totalSize + pendingRes2.data.totalSize < this.learningExperienceNeedNum) {
                this.code = 1005
              }
            }
          }
        }
      } catch (error) {
        console.error(error, '学习心得列表 error')
      }
    }
    return list
  }
  // 获取方案信息 前置条件、已通过数/需通过学习心得数 需求schemeId、studentNo
  async queryLearningExperienceConditions(schemeId: string) {
    await this.requestClassConfig(schemeId)
    this.learningExperiencePassNum = await this.queryPassedLearningExperienceCount()
  }
  // 获取已通过学员心得数
  async queryPassedLearningExperienceCount() {
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 1
    const request = new StudentLearningExperienceRequest()
    request.studentLearning = new StudentSchemeLearningRequest()
    request.studentLearning.studentNos = [this.studentNo]
    request.status = [StudentLearningExperienceStatus.PASS]
    const res = await CourseLearningForestage.pageLearningExperienceInStudent({
      page,
      request
    })
    let count = 0
    if (res.status.isSuccess()) {
      count = res.data.totalSize
    }
    return count
  }
  // 获取历史记录
  async queryHistory(page: Page, params: QueryMyLearningExperiencePageParam): Promise<MyLearningExperience[]> {
    const request = new StudentLearningExperienceRequest()
    request.studentLearning = new StudentSchemeLearningRequest()
    request.studentLearning.studentNos = [this.studentNo]
    if (params.schemeId) {
      request.studentLearning.schemeIds = [params.schemeId]
    }
    request.status = [
      StudentLearningExperienceStatus.SUBMITTED,
      StudentLearningExperienceStatus.PASS,
      StudentLearningExperienceStatus.RETURNED
    ]
    const res = await CourseLearningForestage.pageLearningExperienceInStudent({
      page,
      request
    })
    const list = new Array<MyLearningExperience>()
    if (res.status.isSuccess()) {
      res.data.currentPageData.map((item) => {
        list.push(MyLearningExperience.from(item))
      })
      page.totalSize = res.data.totalSize
      page.pageSize = res.data.pageSize
    }
    return list
  }
  //获取班级模板配置信息
  async requestClassConfig(schemeId: string) {
    //获取培训班配置模板jsonString
    const res = await MsMySchemeQueryFrontGatewayCourseLearningForeStage.getSchemeConfigInServicer({
      schemeId
    })
    // console.log('班级详情内的配置json', res.data.schemeConfig)
    if (res.status.isSuccess()) {
      this.getInfoFromJSON(res.data)
    }
    return res.status
  }
  getInfoFromJSON(commodityDetail: SchemeConfigResponse) {
    const result = JSON.parse(commodityDetail.schemeConfig)
    this.isExamed = !!result.learningExperienceLearning?.assessSetting
    this.showName = result.learningExperienceLearning?.config.showName
    if (result.learningExperienceLearning?.assessSetting) {
      this.learningExperienceNeedNum =
        result.learningExperienceLearning.assessSetting.lessParticipateLearningExperienceTopicCount
      if (result.learningExperienceLearning.config.schemeParticipateRequired?.requiredType == 2) {
        this.classCondition = LearningFeelEnum.COMPLETE_ALL_COURSES
      } else if (result.learningExperienceLearning.config.schemeParticipateRequired?.requiredType == 1) {
        this.classCondition = LearningFeelEnum.STRAIGHTWAY
      }
      if (result.learningExperienceLearning.config.courseParticipateRequired?.requiredType == 2) {
        this.courseCondition = LearningFeelEnum.COMPLETE_ALL_COURSES
      } else if (result.learningExperienceLearning.config.courseParticipateRequired?.requiredType == 1) {
        this.courseCondition = LearningFeelEnum.STRAIGHTWAY
      }
    } else {
      this.classCondition = LearningFeelEnum.STRAIGHTWAY
      this.courseCondition = LearningFeelEnum.STRAIGHTWAY
    }
  }
  async queryCourseList(studentNo?: string) {
    const request = new StudentCourseLearningRequest()
    request.studentCourse = new StudentCourseRequest()
    request.studentCourse.courseLearningStatus = [1, 0]
    request.studentNo = this.studentNo || studentNo
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    if (!this.studentNo && !studentNo) {
      console.log('学员编号不能为空')
      return []
    }
    const res =
      await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneV2InMyself(
        { page, request }
      )

    if (res.status.isSuccess()) {
      const list: string[] = res.data.currentPageData.map((item) => {
        return item.course.courseId
      })
      return list
    } else {
      return []
    }
  }
  /**
   * 查询方案心得总数
   */
  async queryAllExperienceCount(schemeId?: string) {
    const request = new StudentLearningExperienceRequest()
    request.studentLearning = new StudentSchemeLearningRequest()
    request.studentLearning.studentNos = [this.studentNo]
    request.studentLearning.schemeIds = [schemeId]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 1
    // 1已参加 2未参加
    const res1 = await CourseLearningForestage.pageLearningExperienceParticipatedInStudent({ page, request })
    const res2 = await CourseLearningForestage.pageLearningExperienceNotParticipatedInStudent({ page, request })
    if (res1.status.isSuccess() && res2.status.isSuccess()) {
      return res1.data.totalSize + res2.data.totalSize
    } else {
      return 0
    }
  }
  /**
   * 判断学习心得下是否只有课程心得主题
   */
  async queryTopicType(schemeId: string, studentNo?: string) {
    const request = new QueryUnCompleteLearningExperienceTopicRequest()
    request.studentNo = this.studentNo || studentNo
    request.learningSchemeId = schemeId
    const ExperienceTypeRes = await KnowledgeDataGateway.queryUnCompleteLearningExperienceTopic(request)
    if (ExperienceTypeRes.status.isSuccess()) {
      return ExperienceTypeRes.data
    } else {
      return { msg: '判断失败', code: -1 }
    }
  }
}

export default QueryMyLearningExperience
