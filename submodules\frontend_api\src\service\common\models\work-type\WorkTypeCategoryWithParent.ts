import WorkTypeCategory from '@api/service/common/models/work-type/WorkTypeCategory'

export default class WorkTypeCategoryWithParent extends WorkTypeCategory {
  /**
   * 所属父类
   */
  parent: WorkTypeCategoryWithParent

  /**
   * 将所属工种分类名使用'/'连接在一起
   * @param reverse 表示从顶级父类开始连接还是从子类一直连接到父类
   * 默认为false表示child/parent,true则是parent/child
   */
  seriesName(reverse = false): string {
    let name = this.name
    while (parent) {
      name = reverse ? parent.name + '/' + name : name + '/' + parent.name
    }
    return name
  }
}
