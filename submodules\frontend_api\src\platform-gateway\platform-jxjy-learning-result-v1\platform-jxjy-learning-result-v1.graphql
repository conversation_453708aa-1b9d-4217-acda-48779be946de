schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取导入期别学习成果任务excel模板地址
		@return
	"""
	getImportGradeResultTaskTemplatePath:String
	pageImportGradeResultTaskInfo(page:Page,request:ImportGradeResultTaskQueryRequest):ImportGradeResultTaskInfoResponsePage @page(for:"ImportGradeResultTaskInfoResponse")
}
type Mutation {
	"""将任务的excel的全部数据导出
		@param mainTaskId
		@return
	"""
	exportTaskExcelAllResult(mainTaskId:String):String
	"""将任务的excel的全部数据导出
		@param mainTaskId
		@return
	"""
	exportTaskExcelAllResultWithName(mainTaskId:String):ImportGradeResultExcelResponse
	"""将任务的失败数据导出excel
		@param mainTaskId
		@return
	"""
	exportTaskExcelFailedResult(mainTaskId:String):String
	"""将任务的失败数据导出excel
		@param mainTaskId
		@return
	"""
	exportTaskExcelFailedResultWithName(mainTaskId:String):ImportGradeResultExcelResponse
	"""导入期别学习成果
		@param request
	"""
	importIssueLearningResult(request:IssueLearningResultImportRequest):Void
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""学习成果导入请求"""
input IssueLearningResultImportRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.learningresult.IssueLearningResultImportRequest") {
	"""期别id"""
	issueId:String!
	"""导入文件路径"""
	importFilePath:String!
	"""导入文件名"""
	importFileName:String!
}
input ImportGradeResultTaskQueryRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.studentlearning.ImportGradeResultTaskQueryRequest") {
	"""任务名称"""
	taskName:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""执行时间（起始）"""
	executeStartTime:DateTime
	"""执行时间（终止）"""
	executeEndTime:DateTime
}
"""导入成绩excel信息"""
type ImportGradeResultExcelResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.studentlearning.ImportGradeResultExcelResponse") {
	"""文件路径"""
	filePath:String
	"""文件名"""
	fileName:String
}
"""导入成绩任务信息"""
type ImportGradeResultTaskInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.studentlearning.ImportGradeResultTaskInfoResponse") {
	"""任务编号"""
	id:String
	"""【必填】平台编号"""
	platformId:String
	"""【必填】平台版本编号"""
	platformVersionId:String
	"""【必填】项目编号"""
	projectId:String
	"""【必填】子项目编号"""
	subProjectId:String
	"""任务名称"""
	name:String
	"""任务分类"""
	category:String
	"""所属批次单编号"""
	batchNo:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""处理信息"""
	message:String
	"""创建时间"""
	createdTime:DateTime
	"""就绪时间"""
	alreadyTime:DateTime
	"""执行时间"""
	executingTime:DateTime
	"""完成时间"""
	completedTime:DateTime
	"""各状态及执行结果对应数量集合
		总数：全部数量之和
		成功数：result = 1数量之和
		失败数：result = 2数量之和
	"""
	eachStateCounts:[EachStateCount]
}
type EachStateCount @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.studentlearning.ImportGradeResultTaskInfoResponse$EachStateCount") {
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	state:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int
	"""数量"""
	count:Long!
}

scalar List
type ImportGradeResultTaskInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ImportGradeResultTaskInfoResponse]}
