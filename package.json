{"name": "jxjyv2_frontend_web_admin", "version": "0.1.0", "private": true, "scripts": {"format": "prettier --write ./package.json", "collect:git-info": "node build/libs/git-info.js", "serve": "npm run collect:git-info && vue-cli-service serve", "build": "npm run collect:git-info && vue-cli-service build", "analyseImport": "node build/libs/analyseImport.js", "test:e2e": "vue-cli-service test:e2e", "lint": "cross-env NODE_ENV=production vue-cli-service lint", "log-pkg-versions": "node build/libs/log-pkg-versions.js", "---line--secure-:start": "--------------------------------------------------------------------------------------------------------------------------------------------", "secure:util:copy-secure-to-mfs": "node ./build/libs/secure/copy-secure-to_mfs.js", "secure:util:merge": "cross-env UIMODULE=fx ITEMS=XY,super,WXGLY,JTJFGLY node ./build/libs/secure/merge.js", "secure:production": "cross-env NODE_ENV=production npm run secure", "secure:dev": "cross-env NODE_ENV=development npm run secure", "secure-ui": "vue-cli-service serve ./build/libs/secure/ui/index.tsx", "deprecated:--->secure": "cross-env VIEWS=src/views API=submodules/frontend_api/src PREFIX=super PREFIX_DESC=超管 SORT=1 node node_modules/@hbfe/security-toolkit/src/analyse-vue/index.js && npm run secure:user && npm run secure:util:merge", "--------------新版本安全对象解析-------------": "2.0.1", "secure:check-vue-files": "cross-env VIEWS=src/views API=submodules/frontend_api/src PREFIX=super PREFIX_DESC=超管 SORT=1 node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/check-vue-files.js", "secure:index": "cross-env VIEWS=src/views API=submodules/frontend_api/src PREFIX=super PREFIX_DESC=超管 SORT=1 node node_modules/@hbfe/security-toolkit/src/analyse-files/index.js", "secure:ui-invoke": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/basic-router API=submodules/frontend_api/src PREFIX=super PREFIX_DESC=超管 SORT=1 node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-invoke.js", "secure:ui-assign": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/basic-router API=submodules/frontend_api/src PREFIX=super PREFIX_DESC=超管 SORT=1 SETROLE=ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:ui-assign:quick-mode": "cross-env VIEWS=src/views API=submodules/frontend_api/src PREFIX=super PREFIX_DESC=超管 SORT=1 SETROLE=ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --noapi --target src/models --project jxjy --version ********-SNAPSHOT", "secure:qztg": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/qztg API=submodules/frontend_api/src PREFIX=qztg PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node --max-old-space-size=6000 node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure": "run-p secure:ui-assign secure:wxgly secure:qztg secure:scjzs secure:jxgx secure:gszj secure:gstyb secure:xmlg secure:hljysxh secure:fjzj secure:nyyz secure:zzzj secure:byzj secure:yzzj secure:zjzj secure:zzkd secure:zztt secure:user secure:jtjfgly && run-s secure:transform secure:merge-diff secure:util:merge secure:util:copy-secure-to-mfs", "secure:localTest": "npm run secure:ui-assign && npm run secure:wxgly && npm run secure:qztg && npm run secure:scjzs && npm run secure:jxgx && npm run secure:gszj && npm run secure:xmlg && npm run secure:hljysxh  && npm run secure:fjzj && npm run secure:nyyz && npm run secure:zzzj && npm run secure:byzj && npm run secure:yzzj && npm run secure:user && npm run secure:jtjfgly && npm run secure:transform && npm run secure:merge-diff && npm run secure:util:merge", "secure:dirty-check": "cross-env VIEWS=src/views API=submodules/frontend_api/src PREFIX=super PREFIX_DESC=超管 SORT=1 node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/dirty-check.js", "secure:wxgly": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/basic-school API=submodules/frontend_api/src PREFIX=WXGLY PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node --max-old-space-size=6000 node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:fjzj": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/fjzj API=submodules/frontend_api/src PREFIX=fjzj PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:scjzs": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/scjzs API=submodules/frontend_api/src PREFIX=scjzs PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:byzj": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/byzj API=submodules/frontend_api/src PREFIX=byzj PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:gstyb": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/gstyb API=submodules/frontend_api/src PREFIX=gstyb PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:xmlg": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/xmlg API=submodules/frontend_api/src PREFIX=xmlg PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:jxgx": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/jxgx API=submodules/frontend_api/src PREFIX=jxgx PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:api": "cross-env VIEWS=src/views API=submodules/frontend_api/src PREFIX=super PREFIX_DESC=超管 SORT=1 node node_modules/@hbfe/security-toolkit/src/ananyse-api/index.js", "secure:user": "cross-env API=submodules/frontend_api/src VIEWS=src/views PREFIX=XY PREFIX_DESC=学员 NAME_SPACES=common,customer,centre,views,diff node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/customer-secure.js --increment", "secure:jtjfgly": "cross-env API=submodules/frontend_api/src VIEWS=src/views PREFIX=JTJFGLY PREFIX_DESC=集体缴费管理员 NAME_SPACES=common,customer,centre,views node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/customer-secure.js --increment", "secure:customer": "npm run secure:user && npm run secure:jtjfgly && npm run secure:util:merge ", "secure:gszj": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/gszj API=submodules/frontend_api/src PREFIX=gszj PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:hljysxh": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/hljysxh API=submodules/frontend_api/src PREFIX=hljysxh PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:nyyz": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/nyyz API=submodules/frontend_api/src PREFIX=nyyz PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:zzzj": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/zzzj API=submodules/frontend_api/src PREFIX=zzzj PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:yzzj": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/yzzj API=submodules/frontend_api/src PREFIX=yzzj PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:zjzj": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/zjzj API=submodules/frontend_api/src PREFIX=zjzj PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:zzkd": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/zzkd API=submodules/frontend_api/src PREFIX=zzkd PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:zztt": "cross-env packagePrefix=jxjy isModule=true VIEWS=packages/routers/src/zztt API=submodules/frontend_api/src PREFIX=zztt PREFIX_DESC=网校管理员 SORT=2 EXCEPTROLE=XY,ZXMGLY node node_modules/@hbfe/security-toolkit/src/analyse-ui-files/ui-assign.js --increment --different --target src/models --project jxjy --version ********-SNAPSHOT", "secure:transform": "cross-env FLAG=scjzs,gszj,fjzj,qztg,yzzj,byzj,gstyb,xmlg,jxgx,hljysxh,nyyz,zzzj,zjzj,zzkd,zztt TARGET=WXGLY node build/libs/secure/modify-diff-security.js", "secure:merge-diff": "cross-env FLAG=scjzs,gszj,fjzj,qztg,yzzj,byzj,gstyb,xmlg,jxgx,hljysxh,nyyz,zzzj,zjzj,zzkd,zztt TARGET=WXGLY node build/libs/secure/merge-diff-security.js", "secure:analyseFiles": "cross-env VIEWS=src/views API=submodules/frontend_api/src PREFIX=super PREFIX_DESC=超管 SORT=1 node node_modules/@hbfe/security-toolkit/src/analyse-files/analyse-files.js", "---line--secure-:end": "----------------------------------------------------------------------------------------------------------------------------------------------", "secure:generate:roles": "cross-env NODE_ENV=development node node_modules/@hbfe/security-toolkit/src/auto-role/get-roles.js --target src/models --project jxjy --version ********-SNAPSHOT", "prepare": "husky install"}, "script-description": {"secure-ui": "生成安全对象的 ui 查看器", "secure.util.copy-secure-to-mfs": "将生成出来的安全对象拷贝到本地的 mfs/dev/security 目录下面, 目录规则根据 appkey 获取子项目 id 保存", "secure.util.merge": "因为后端需要的文件只有 group-tree.json、permission-tree.json，前端在多服务商情况下面会生成多份，故需要做合并动作", "secure:super": "生成【超级管理员】的权限菜单列表", "secure:org": "生成【机构】的权限菜单列表", "secure:channel": "生成【渠道商】的权限菜单列表", "secure:provider": "生成【供应商】的权限菜单列表", "secure:dirtyCheck": "检测全局请求，未被页面引用，漏检测的请求", "secure:customer": "生成学员的安全对象集合"}, "dependencies": {"npm-run-all": "4.1.5", "@commitlint/cli": "~8.3.5", "@commitlint/config-conventional": "~8.3.4", "@hbfe-biz/biz-anticheat": "1.1.0", "@hbfe-biz/biz-authentication": "1.4.2", "@hbfe-commons/has-permission": "0.0.1", "@hbfe-vue-components/image-cropper": "0.1.13", "@hbfe-vue-components/pop-tree": "0.1.0", "@hbfe-vue-themes/admin-common": "0.0.1", "@hbfe/base-event-tracking": "1.0.0", "@hbfe/behavior-validator": "1.0.1", "@hbfe/common": "1.1.2", "@hbfe/eslint-plugin-rules": "1.0.0", "@hbfe/fx-manage": "1.6.5", "@hbfe/hbfe-ui": "1.1.0-SNAPSHOT.16", "@hbfe/jxjy-admin-account": "workspace:^", "@hbfe/jxjy-admin-authentication": "workspace:^", "@hbfe/jxjy-admin-basicSchoolHome": "workspace:^", "@hbfe/jxjy-admin-basicSchoolPersonInfo": "workspace:^", "@hbfe/jxjy-admin-batchPrint": "workspace:^", "@hbfe/jxjy-admin-commodityStatistic": "workspace:^", "@hbfe/jxjy-admin-common": "workspace:^", "@hbfe/jxjy-admin-components": "workspace:^", "@hbfe/jxjy-admin-course": "workspace:^", "@hbfe/jxjy-admin-coursePackage": "workspace:^", "@hbfe/jxjy-admin-courseSelectionStatistic": "workspace:^", "@hbfe/jxjy-admin-courseware": "workspace:^", "@hbfe/jxjy-admin-customerService": "workspace:^", "@hbfe/jxjy-admin-distributionChannel": "workspace:^", "@hbfe/jxjy-admin-distributionGoodsOpeningStatistics": "workspace:^", "@hbfe/jxjy-admin-examPaper": "workspace:^", "@hbfe/jxjy-admin-exportTask": "workspace:^", "@hbfe/jxjy-admin-huayiSellStatistic": "workspace:^", "@hbfe/jxjy-admin-import": "workspace:^", "@hbfe/jxjy-admin-info": "workspace:^", "@hbfe/jxjy-admin-intelligentLearning": "workspace:^", "@hbfe/jxjy-admin-learningStatistic": "workspace:^", "@hbfe/jxjy-admin-logManagement": "workspace:^", "@hbfe/jxjy-admin-management": "workspace:^", "@hbfe/jxjy-admin-marketingCenter": "workspace:^", "@hbfe/jxjy-admin-operationPersonInfo": "workspace:^", "@hbfe/jxjy-admin-platform": "workspace:^", "@hbfe/jxjy-admin-promotionStatistic": "workspace:^", "@hbfe/jxjy-admin-question": "workspace:^", "@hbfe/jxjy-admin-questionLibrary": "workspace:^", "@hbfe/jxjy-admin-questionnaire": "workspace:^", "@hbfe/jxjy-admin-regionLearningStatistic": "workspace:^", "@hbfe/jxjy-admin-regionSellStatistic": "workspace:^", "@hbfe/jxjy-admin-registerSchool": "workspace:^", "@hbfe/jxjy-admin-routers": "workspace:^", "@hbfe/jxjy-admin-scheme": "workspace:^", "@hbfe/jxjy-admin-schemeLearningStatistic": "workspace:^", "@hbfe/jxjy-admin-schemeSellStatistic": "workspace:^", "@hbfe/jxjy-admin-specialTopics": "workspace:^", "@hbfe/jxjy-admin-statisticsReport": "workspace:^", "@hbfe/jxjy-admin-supplierDistributionOfGoodsOpenStatistics": "workspace:^", "@hbfe/jxjy-admin-supplierDistributorSalesStatistics": "workspace:^", "@hbfe/jxjy-admin-task": "workspace:^", "@hbfe/jxjy-admin-trade": "workspace:^", "@hbfe/jxjy-admin-tradeConfig": "workspace:^", "@hbfe/jxjy-admin-trainingPoints": "workspace:^", "@hbfe/jxjy-admin-trainingunitSalesStatistic": "workspace:^", "@hbfe/jxjy-admin-user": "workspace:^", "@hbfe/jxjy-admin-security": "workspace:^", "@hbfe/jxjy-customer-common": "24.6.2-1", "@jiaminghi/data-view": "~2.8.3", "@tinymce/tinymce-vue": "~3.2.1", "axios": "~0.19.2", "caniuse-lite": "~1.0.30001252", "clipboard": "~2.0.6", "core-js": "~3.8.3", "cross-env": "~7.0.2", "crypto-js": "~4.2.0", "default-gateway": "~6.0.3", "dotenv": "~10.0.0", "draggable": "~4.2.0", "driver.js": "~0.9.8", "echarts": "~4.7.0", "element-ui": "~2.15.7", "eslint-formatter-pretty": "~3.0.1", "fast-glob": "^3.3.2", "fs-extra": "~10.0.0", "got": "~11.8.2", "graphql": "~16.7.1", "graphql-playground-middleware-express": "~1.7.14", "graphql-tag": "~2.12.6", "graphql-voyager": "~1.0.0-rc.30", "handlebars": "~4.7.6", "html-to-image": "~1.11.11", "husky": "~8.0.0", "ip": "~1.1.5", "js-base64": "~3.7.7", "jsdom": "~16.2.2", "less-loader": "~6.1.0", "lodash": "~4.17.15", "lodash-decorators": "~6.0.1", "metalsmith": "~2.3.0", "metalsmith-rename": "~1.0.0", "mockjs": "~1.1.0", "moment": "~2.25.3", "monaco-editor": "~0.34.0", "node-polyfill-webpack-plugin": "~4.0.0", "nprogress": "~0.2.0", "numeral": "~2.0.6", "qrcode": "~1.4.4", "raw-loader": "~4.0.1", "resolve-url-loader": "~3.1.1", "rrweb": "~1.0.2", "sass": "~1.48.0", "screenfull": "~5.0.2", "simple-git": "~2.42.0", "simple-progress-webpack-plugin": "~1.1.2", "sortablejs": "~1.13.0", "tinymce": "~5.7.1", "tsconfig-paths": "~3.9.0", "uuid": "~8.3.0", "vue": "2.6.11", "vue-auto-routes": "~2.0.1", "vue-class-component": "~7.2.3", "vue-clipboard2": "0.3.1", "vue-cropper": "~0.5.5", "vue-observe-visibility": "~0.4.6", "vue-property-decorator": "~9.1.2", "vue-router": "~3.5.1", "vuedraggable": "^2.24.3", "vuex": "~3.6.2", "vuex-module-decorators": "~0.17.0", "whatwg-url": "8.1.0"}, "devDependencies": {"@babel/parser": "~7.25.0", "@babel/plugin-syntax-dynamic-import": "~7.8.3", "@hbfe/security-toolkit": "4.6.0-SNAPSHOT.6", "@types/clipboard": "~2.0.1", "@types/crypto-js": "~4.2.2", "@types/echarts": "~4.9.9", "@types/events": "~3.0.0", "@types/lodash": "~4.14.152", "@types/mockjs": "~1.0.3", "@types/node": "~14.0.4", "@types/nprogress": "~0.2.0", "@types/numeral": "~2.0.1", "@types/qrcode": "~1.4.0", "@types/sortablejs": "~1.10.6", "@types/uuid": "~8.3.0", "@types/vuedraggable": "~2.23.1", "@typescript-eslint/eslint-plugin": "~5.4.0", "@typescript-eslint/parser": "~5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-prettier": "7.0.0", "@vue/eslint-config-typescript": "~9.1.0", "eslint": "~7.32.0", "eslint-config-prettier": "~8.3.0", "eslint-plugin-prettier": "~4.0.0", "eslint-plugin-vue": "~8.0.3", "fast-glob": "~3.3.2", "file-loader": "~6.2.0", "glob": "~11.0.0", "lint-staged": "~9.5.0", "prettier": "~2.4.1", "sass-loader": "~8.0.2", "typescript": "4.2.2", "vue-template-compiler": "2.6.11"}, "lint-staged": {"**/*.{js,jsx,vue,ts,tsx}": ["cross-env NODE_ENV=production vue-cli-service lint"]}}