import {
  CommodityInvoiceTicketConfigCreateRequest,
  ElectronicInvoiceTaxpayerCreateRequest,
  InvoiceServiceAuthCreateRequest
} from '@api/ms-gateway/ms-trade-configuration-v1'
import EleInvoiceVo from './common/EleInvoiceVo'
/*
  增值税发票配置
*/
class ElectronicInvoiceCreateVo extends EleInvoiceVo {
  constructor() {
    super()
  }
  toDto() {
    const param = new ElectronicInvoiceTaxpayerCreateRequest()
    param.name = this.name
    param.taxpayerNo = this.taxpayerNo
    param.address = this.address
    param.phone = this.phone
    param.bankName = this.bankName
    param.bankAccount = this.bankAccount
    param.invoiceMaxMoney = this.invoiceMaxMoney
    param.payee = this.payee
    param.issuer = this.issuer
    param.reviewer = this.reviewer
    param.invoiceAuthList = new Array<InvoiceServiceAuthCreateRequest>()
    const invoice = new InvoiceServiceAuthCreateRequest()
    invoice.invoiceProviderId = this.invoiceProviderId
    invoice.secretAssessKey = this.secretAssessKey
    invoice.deptId = this.deptId
    invoice.accessKey = this.accessKey
    invoice.remark = this.remark
    param.invoiceAuthList.push(invoice)

    param.commodityTicketList = new Array<CommodityInvoiceTicketConfigCreateRequest>()
    const commodityTicket = new CommodityInvoiceTicketConfigCreateRequest()
    commodityTicket.commodityCode = this.commodityCode
    commodityTicket.printPrice = this.printPrice || false
    commodityTicket.printQuantity = this.printQuantity || false
    commodityTicket.rate = this.rate
    commodityTicket.serviceTitle = this.serviceTitle
    commodityTicket.specificationMode = this.specificationMode
    commodityTicket.unitTitle = this.unitTitle
    commodityTicket.taxFavoured = this.taxFavoured
    param.commodityTicketList.push(commodityTicket)
    return param
  }

  /**
   * 转换成诺税通
   */
  toNST() {
    const param = new ElectronicInvoiceTaxpayerCreateRequest()
    param.name = this.name
    param.taxpayerNo = this.taxpayerNo
    param.address = this.address
    param.phone = this.phone
    param.bankName = this.bankName
    param.bankAccount = this.bankAccount
    param.invoiceMaxMoney = this.invoiceMaxMoney
    param.payee = this.payee
    param.issuer = this.issuer
    param.reviewer = this.reviewer
    param.invoiceAuthList = new Array<InvoiceServiceAuthCreateRequest>()
    const invoice = new InvoiceServiceAuthCreateRequest()
    invoice.invoiceProviderId = this.invoiceProviderId
    invoice.secretAssessKey = this.secretAssessKey
    invoice.deptId = this.deptId
    invoice.accessKey = this.accessKey
    invoice.remark = this.remark
    invoice.extensionNumber = this.extensionNumber
    param.invoiceAuthList.push(invoice)

    param.commodityTicketList = new Array<CommodityInvoiceTicketConfigCreateRequest>()
    const commodityTicket = new CommodityInvoiceTicketConfigCreateRequest()
    commodityTicket.commodityCode = this.commodityCode
    commodityTicket.printPrice = this.printPrice || false
    commodityTicket.printQuantity = this.printQuantity || false
    commodityTicket.rate = this.rate
    commodityTicket.serviceTitle = this.serviceTitle
    commodityTicket.specificationMode = this.specificationMode
    commodityTicket.unitTitle = this.unitTitle
    commodityTicket.taxFavoured = this.taxFavoured
    param.commodityTicketList.push(commodityTicket)
    return param
  }

  toBWJFDto() {
    const param = new ElectronicInvoiceTaxpayerCreateRequest()
    param.name = this.name
    param.taxpayerNo = this.taxpayerNo
    param.address = this.address
    param.phone = this.phone
    param.bankName = this.bankName
    param.bankAccount = this.bankAccount
    param.invoiceMaxMoney = this.invoiceMaxMoney
    param.payee = this.payee
    param.issuer = this.issuer
    param.reviewer = this.reviewer
    param.invoiceAuthList = new Array<InvoiceServiceAuthCreateRequest>()
    const invoice = new InvoiceServiceAuthCreateRequest()
    invoice.invoiceProviderId = this.invoiceProviderId
    invoice.secretAssessKey = this.secretAssessKey
    invoice.accessKey = this.accessKey
    invoice.enterpriseCode = this.enterpriseCode
    invoice.taxpayerIdNumber = this.taxIdCard
    invoice.remark = this.remark
    param.invoiceAuthList.push(invoice)

    param.commodityTicketList = new Array<CommodityInvoiceTicketConfigCreateRequest>()
    const commodityTicket = new CommodityInvoiceTicketConfigCreateRequest()
    commodityTicket.commodityCode = this.commodityCode
    commodityTicket.printPrice = this.printPrice || false
    commodityTicket.printQuantity = this.printQuantity || false
    commodityTicket.rate = this.rate
    commodityTicket.serviceTitle = this.serviceTitle
    commodityTicket.specificationMode = this.specificationMode
    commodityTicket.unitTitle = this.unitTitle
    commodityTicket.taxFavoured = this.taxFavoured
    param.commodityTicketList.push(commodityTicket)
    return param
  }
}
export default ElectronicInvoiceCreateVo
