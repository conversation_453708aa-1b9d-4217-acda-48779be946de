import MsServicerV1, { TrainingInstitutionPortalThemeColorUpdateRequest } from '@api/ms-gateway/ms-servicer-v1'
import { ResponseStatus } from '@hbfe/common'
import Context from '@api/service/common/context/Context'
import MsBasicdataQueryFrontGateway from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import MsServicerContract from '@api/ms-gateway/ms-servicercontract-v1'

class MutationTheme {
  themeColor = ''
  themeColorList = new Array<string>()

  /**
   * 获取主题颜色列表
   * @returns
   */
  async queryList() {
    const res = await MsServicerContract.findUiThemeColorList()
    if (res.status.isSuccess()) {
      this.themeColorList = res.data?.map(color => {
        return color.colorRef
      })
    }
    return res.status
  }

  /**
   * 获取主题颜色
   * @returns
   */
  async queryDetail() {
    const result = new ResponseStatus(200)
    if (this.themeColor) {
      return result
    }
    const res = await MsBasicdataQueryFrontGateway.getTrainingInstitutionPortalThemeColor()
    if (res.status.isSuccess()) {
      this.themeColor = res.data
    }
    return res.status
  }

  /**
   * 保存主题颜色
   * @returns
   */
  async doSave(): Promise<ResponseStatus> {
    const param = new TrainingInstitutionPortalThemeColorUpdateRequest()
    if (this.themeColor === '') {
      return new ResponseStatus(500, '未选择颜色')
    }
    param.themeColor = this.themeColor
    param.trainingInstitutionId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
    const res = await MsServicerV1.updateTrainingInstitutionWebPortalThemeColor(param)
    return res.status
  }
}
export default new MutationTheme()
