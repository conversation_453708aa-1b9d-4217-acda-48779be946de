import getBillConfigByPaymentChannel from './queries/getBillConfigByPaymentChannel.graphql'
import getInvoiceConfigByPaymentChannelTab from './queries/getInvoiceConfigByPaymentChannelTab.graphql'
import getInvoiceConfigByPaymentChannelTabAndSkuId from './queries/getInvoiceConfigByPaymentChannelTabAndSkuId.graphql'
import getMerchantAccountList from './queries/getMerchantAccountList.graphql'
import getPaymentAccountByPaymentChannel from './queries/getPaymentAccountByPaymentChannel.graphql'
import getPaymentAccountByPaymentChannelId from './queries/getPaymentAccountByPaymentChannelId.graphql'
import getPaymentAccountListByPaymentChannelId from './queries/getPaymentAccountListByPaymentChannelId.graphql'
import getPaymentAccountListByQueryParam from './queries/getPaymentAccountListByQueryParam.graphql'
import getPaymentChannelConfiguration from './queries/getPaymentChannelConfiguration.graphql'
import getPaymentChannelConfiguration2 from './queries/getPaymentChannelConfiguration2.graphql'
import getPaymentChannelList from './queries/getPaymentChannelList.graphql'
import listPaymentChannel from './queries/listPaymentChannel.graphql'
import addPaymentAccount from './mutates/addPaymentAccount.graphql'
import operaBillConfig from './mutates/operaBillConfig.graphql'
import removePaymentAccount from './mutates/removePaymentAccount.graphql'

export {
  getBillConfigByPaymentChannel,
  getInvoiceConfigByPaymentChannelTab,
  getInvoiceConfigByPaymentChannelTabAndSkuId,
  getMerchantAccountList,
  getPaymentAccountByPaymentChannel,
  getPaymentAccountByPaymentChannelId,
  getPaymentAccountListByPaymentChannelId,
  getPaymentAccountListByQueryParam,
  getPaymentChannelConfiguration,
  getPaymentChannelConfiguration2,
  getPaymentChannelList,
  listPaymentChannel,
  addPaymentAccount,
  operaBillConfig,
  removePaymentAccount
}
