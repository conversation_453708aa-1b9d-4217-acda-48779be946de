import {
  OnlineSchoolProtocolConfigRequest,
  OnlineSchoolProtocolConfigResponse
} from '@api/ms-gateway/ms-servicer-series-v1'
import Agreement from './Agreement'
/**
 * 协议设置
 */
export default class AgreementSettingVo {
  /**
   * 注册协议
   */
  registerAgreement: Agreement = new Agreement()
  /**
   * 登录协议
   */
  loginAgreement: Agreement = new Agreement()

  /**
   * 转换模型
   */
  static from(dto: OnlineSchoolProtocolConfigResponse) {
    const detail = new AgreementSettingVo()
    detail.registerAgreement.isOpen = dto.hasRegisterProtocol
    detail.registerAgreement.name = dto.registerProtocolName
    detail.registerAgreement.content = dto.registerProtocolContent
    detail.loginAgreement.isOpen = dto.hasLoginProtocol
    detail.loginAgreement.name = dto.loginProtocolName
    detail.loginAgreement.content = dto.loginProtocolContent
    return detail
  }
  static to(vo: AgreementSettingVo) {
    const dto = new OnlineSchoolProtocolConfigRequest()
    dto.hasRegisterProtocol = vo.registerAgreement.isOpen
    dto.registerProtocolName = vo.registerAgreement.name
    dto.registerProtocolContent = vo.registerAgreement.content
    dto.hasLoginProtocol = vo.loginAgreement.isOpen
    dto.loginProtocolName = vo.loginAgreement.name
    dto.loginProtocolContent = vo.loginAgreement.content
    return dto
  }
}
