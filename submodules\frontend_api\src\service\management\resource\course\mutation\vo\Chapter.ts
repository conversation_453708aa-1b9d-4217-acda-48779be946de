/**
 * 章节
 * @description 章节中章节信息
 */
import Courseware from '@api/service/management/resource/course/mutation/vo/Courseware'
import CoursewareListDetail from '@api/service/management/resource/courseware/query/vo/CoursewareListDetail'
import { CourseOutline } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import Mockjs from 'mockjs'

class Chapter {
  /**
   * 章节编号，对应标签编号
   */
  id: string

  constructor() {
    this.id = Mockjs.Random.guid()
  }

  /**
   * 章节名称，对应标签名称
   */
  name = ''
  sort = 0

  /**
   * 父节点
   */
  parentId: string

  coursewares: Array<Courseware> = new Array<Courseware>()

  editMode = false

  edit() {
    this.editMode = true
  }

  /**
   * 获取选中的列表
   */
  getSelectedIdList() {
    return this.coursewares.map(item => item.id)
  }

  /**
   * 添加课件
   * @param coursewareList
   * @param reset
   */
  addCourseware(
    coursewareList: Array<CoursewareListDetail>,
    reset?: boolean,
    delCoursewareList?: Array<CoursewareListDetail>
  ) {
    const inList = coursewareList.map(item => {
      const cWare = new Courseware()
      cWare.id = item.id
      cWare.name = item.name
      cWare.timeLengthFormat = item.timeLengthFormat
      return cWare
    })
    if (reset) {
      return (this.coursewares = inList)
    }
    if (coursewareList?.length) {
      inList.map(item => {
        if (this.coursewares.findIndex(findItem => item.id === findItem.id) === -1) {
          this.coursewares.push(item)
        }
      })
    }
    if (delCoursewareList?.length) {
      delCoursewareList.map(item => {
        if (this.coursewares.findIndex(findItem => item.id === findItem.id) !== -1) {
          this.coursewares.splice(
            this.coursewares.findIndex(findItem => item.id === findItem.id),
            1
          )
        }
      })
    }
  }

  /**
   * 删除课件
   * @param index
   */
  removeCourseware(index: number) {
    this.coursewares.splice(index, 1)
  }

  static from(outline: CourseOutline) {
    const chapter = new Chapter()
    chapter.id = outline.id
    chapter.name = outline.name
    chapter.sort = outline.sort
    chapter.parentId = outline.parentId
    chapter.coursewares = new Array<Courseware>()
    return chapter
  }
}

export default Chapter
