import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 培训方案来源类型枚举
 * 1：个人报名
 * 2：导入开通
 * 3：换班
 * 4：集体报名
 */
export enum TrainClassResourceEnum {
  Personal_Register = 1,
  Import_Open = 2,
  Exchange_Train_Class = 3,
  Collective_Register = 4
}

class TrainClassResourceType extends AbstractEnum<TrainClassResourceEnum> {
  static enum = TrainClassResourceEnum
  constructor(status?: TrainClassResourceEnum) {
    super()
    this.current = status
    this.map.set(TrainClassResourceEnum.Personal_Register, '个人报名')
    this.map.set(TrainClassResourceEnum.Import_Open, '导入开通')
    this.map.set(TrainClassResourceEnum.Exchange_Train_Class, '换班')
    this.map.set(TrainClassResourceEnum.Collective_Register, '集体报名')
  }
}

export default new TrainClassResourceType()
