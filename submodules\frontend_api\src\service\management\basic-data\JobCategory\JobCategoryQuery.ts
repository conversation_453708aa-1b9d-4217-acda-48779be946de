import Basicdata from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import { Page } from '@hbfe/common'

export default class JobCategoryQuery {
  /**
   * 分页查询工种信息
   */
  async pageJobCategoryInSubProject(page: Page) {
    const response = await Basicdata.pageJobCategoryInSubProject({ page })
    page.totalPageSize = response.data.totalPageSize
    page.totalSize = response.data.totalSize
    return response.data.currentPageData
  }
  /**
   * 根据工种ID查详情
   */
  async getJobCategory(id: string) {
    const response = await Basicdata.getJobCategoryInSubProject(id)
    return response.data
  }
  /**
   * 工种全量查询
   */
  async getAllJobCategory() {
    const page = new Page(1, 200)
    const result = []
    const response = await Basicdata.pageJobCategoryInSubProject({ page })
    result.push(...response.data.currentPageData)
    let count = 0
    if (response.data.totalSize > 200) {
      count = Math.ceil(response.data.totalSize / 200)
      for (let i = 0; i < count; i++) {
        page.pageNo = i + 2
        const response = await Basicdata.pageJobCategoryInSubProject({ page })
        result.push(...response.data.currentPageData)
      }
    }
    return result
  }
}
