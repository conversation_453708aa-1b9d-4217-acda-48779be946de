<route-meta>
{
"isMenu": true,
"title": "学员学习明细",
"sort": 5,
"icon": "icon-mingxi"
}
</route-meta>

<script lang="ts">
  import LearningStatistic from '@hbfe/jxjy-admin-learningStatistic/src/diff/zjzj/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, DQGLY, NZFXS, NZFXSJCB, ZTGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    query: [WXGLY, DQGLY, NZFXS, NZFXSJCB, ZTGLY],
    export: [WXGLY, DQGLY, NZFXS, NZFXSJCB, ZTGLY],
    allSync: [WXGLY, DQGLY, NZFXS, NZFXSJCB, ZTGLY],
    testDetail: [WXGLY, DQGLY, NZFXS, NZFXSJCB, ZTGLY],
    examDetail: [WXGLY, DQGLY, NZFXS, NZFXSJCB, ZTGLY],
    toLog: [WXGLY, DQGLY, NZFXS, NZFXSJCB, ZTGLY],
    userInfo: [WXGLY, DQGLY, NZFXS, NZFXSJCB, ZTGLY],
    classInfo: [WXGLY, DQGLY, NZFXS, NZFXSJCB, ZTGLY],
    studyLog: [WXGLY, DQGLY, NZFXS, NZFXSJCB, ZTGLY],
    exceptionManagement: [WXGLY, DQGLY, NZFXS, NZFXSJCB],
    toLearningLog: [WXGLY, DQGLY, ZTGLY],
    exportData: [WXGLY],
    intelligentLearningList: [WXGLY, DQGLY, NZFXS, NZFXSJCB],
    learnAnomalousList: [WXGLY, DQGLY, NZFXS, NZFXSJCB, ZTGLY]
  })
  export default class extends LearningStatistic {}
</script>
