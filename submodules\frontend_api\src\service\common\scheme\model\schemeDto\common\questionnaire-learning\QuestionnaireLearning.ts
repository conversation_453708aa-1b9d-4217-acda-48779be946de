import QuestionnaireLearningAssessSetting from '@api/service/common/scheme/model/schemeDto/common/questionnaire-learning/assess-setting/QuestionnaireLearningAssessSetting'
import QuestionnaireLearningConfig from '@api/service/common/scheme/model/schemeDto/common/questionnaire-learning/config/QuestionnaireLearningConfig'
import QuestionnaireLearningPrecondition from '@api/service/common/scheme/model/schemeDto/common/questionnaire-learning/precondition/QuestionnaireLearningPrecondition'
import QuestionnaireLearningExtendProperty from '@api/service/common/scheme/model/schemeDto/common/questionnaire-learning/extend-properties/QuestionnaireLearningExtendProperty'

/**
 * @description 问卷学习方式
 */
class QuestionnaireLearning {
  /**
   * 学习方式id
   */
  id: string
  /**
   * 考核信息
   */
  assessSetting: QuestionnaireLearningAssessSetting[]
  /**
   * 规则配置信息
   */
  config: QuestionnaireLearningConfig
  /**
   * 操作类型
   */
  operation: number
  /**
   * 前置条件
   */
  precondition: QuestionnaireLearningPrecondition
  /**
   * 拓展属性
   */
  extendProperties: QuestionnaireLearningExtendProperty[]
}

export default QuestionnaireLearning
