<template>
  <div>
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div class="f-plr20">
        <el-row type="flex" justify="left" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" label-width="170px" class="m-form f-mt20">
              <el-form-item label="学习心得对外展示名称：">
                <el-input
                  size="small"
                  class="form-l f-mr5"
                  placeholder="请输入展示名称"
                  v-model="learningFeelInfo.experienceName"
                  :disabled="!learningFeelInfo.isSelected"
                />
                <p style="color: #f56c6c" v-if="hasLength">对外展示名称不可超过6个字符</p>
                <div class="tab-tips-3">
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                    <div slot="content">如有配置对外展示名称，学员查看学习方式显示为此名称</div>
                  </el-tooltip>
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <experience-list
          ref="experienceRef"
          :trainSchemeDetail.sync="schemeDetail"
          :course-learning="courseLearning"
          :learningFeelInfo="learningFeelInfo"
          :classification="classification"
          :routerMode="routerMode"
        ></experience-list>
        <experience-requirement
          :learningFeelInfo="learningFeelInfo"
          :classification="classification"
          :course-learning="courseLearning"
          :routerMode="routerMode"
        ></experience-requirement>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import LearningExperience from '@api/service/management/train-class/mutation/vo/LearningExperience'
  import ExperienceList from '@hbfe/jxjy-admin-scheme/src/components/functional-components/experience-list.vue'
  import ExperienceRequirement from '@hbfe/jxjy-admin-scheme/src/components/functional-components/experience-requirement.vue'
  import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
  import ModifyChooseCourseOutline from '@hbfe/jxjy-admin-scheme/src/components/functional-components/modify-choose-course-outline.vue'
  import CourseLearningLearningType from '@api/service/management/train-class/mutation/vo/CourseLearningLearningType'

  @Component({
    components: { ModifyChooseCourseOutline, ExperienceList, ExperienceRequirement }
  })
  export default class extends Vue {
    @Ref('experienceRef') experienceRef: ExperienceList

    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number

    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: TrainClassBaseModel }) schemeDetail!: TrainClassBaseModel

    /**
     * 学习心得配置 - 双向绑定
     */
    @Prop({ type: LearningExperience }) learningFeelInfo: LearningExperience
    /**
     * 接收大纲树
     */
    @Prop({
      type: Object,
      default: () => new Classification()
    })
    classification: Classification

    /**
     * 课程学习信息
     */
    @Prop({
      required: true,
      type: CourseLearningLearningType
    })
    courseLearning: CourseLearningLearningType

    props = {
      value: 'code',
      label: 'desc'
    }

    /**
     * 获取已选心得列表
     */
    async getSelectedLearningExperienceList() {
      this.experienceRef.init()
    }

    /**
     * 是否有课程，没有的时候禁用该按钮
     */
    get hasLength() {
      return this.learningFeelInfo.experienceName.length > 6
    }
  }
</script>
