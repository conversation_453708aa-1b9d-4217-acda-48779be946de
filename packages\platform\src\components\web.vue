<route-meta>
  {
  "isMenu": false,
  "title": "web设置",
  }
</route-meta>
<template>
  <div class="f-p15">
    <el-tabs
      v-if="$hasPermission('webSet')"
      desc="web设置"
      actions="@PortalInfo,@Column,@Banner,@StyleConfig"
      v-model="activeSubTabName"
      type="card"
      class="m-tab-card"
    >
      <div class="tab-right">
        <el-button
          type="primary"
          size="medium"
          class="f-mr15"
          @click="preview"
          v-if="$hasPermission('webPreview')"
          desc="web预览"
          actions="preview"
        >
          <i class="hb-iconfont icon-complelearn f-mr5"></i>预览
        </el-button>
      </div>
      <el-tab-pane
        label="门户信息配置"
        name="portalInfo"
        v-if="$hasPermission('portal')"
        desc="门户信息配置"
        actions="@PortalInfo"
      >
        <portal-info @callBack="changeWebAccess" :TemplateModuleObj="TemplateModuleObj"></portal-info>
      </el-tab-pane>
      <el-tab-pane label="栏目设置" name="column" v-if="$hasPermission('column')" desc="栏目设置" actions="@Column">
        <column></column>
      </el-tab-pane>
      <el-tab-pane label="轮播图设置" name="banner" v-if="$hasPermission('banner')" desc="轮播图设置" actions="@Banner">
        <banner :TemplateModuleObj="TemplateModuleObj"></banner>
      </el-tab-pane>

      <el-tab-pane label="风格设置" name="style" v-if="$hasPermission('style')" desc="风格设置" actions="@StyleConfig">
        <style-config></style-config>
      </el-tab-pane>
      <el-tab-pane
        label="网校SEO配置"
        name="seo"
        v-if="$hasPermission('setSeo')"
        desc="seo配置"
        actions="@Seo"
        :lazy="true"
      >
        <seo></seo>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
  import TemplateItem from '@api/service/common/template-school/TemplateItem'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import Banner from '@hbfe/jxjy-admin-platform/src/components/banner.vue'
  import Column from '@hbfe/jxjy-admin-platform/src/components/column.vue'
  import PortalInfo from '@hbfe/jxjy-admin-platform/src/components/portal-info.vue'
  import Seo from '@hbfe/jxjy-admin-platform/src/components/seo.vue'
  import StyleConfig from '@hbfe/jxjy-admin-platform/src/components/style.vue'
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  @Component({
    components: { PortalInfo, Column, StyleConfig, Banner, Seo }
  })
  export default class extends Vue {
    activeSubTabName = 'portalInfo'
    @Watch('$route', {
      immediate: true,
      deep: true
    })
    changeRoute(route: any) {
      if (route.query.column) {
        this.activeSubTabName = route.query.column
      }
    }
    @Prop({
      required: true,
      default: () => {
        return new TemplateItem()
      }
    })
    TemplateModuleObj: TemplateItem

    changeWebAccess() {
      this.$emit('callBack')
    }
    async preview() {
      await this.getPreviewConfigure()
      switch (this.TemplateModuleObj.id) {
        case 'TestTemplateId-1':
          window.open(window.location.origin + '/preview', '_blank')
          // window.open('http://fjjsjxjy.dev.59iedu.com:8081' + '/preview', '_blank')
          break
        case 'js-pc-001':
          window.open(window.location.origin + '/preview2', '_blank')
          // window.open('http://fjjsjxjy.dev.59iedu.com:8081' + '/preview2', '_blank')
          break
        case 'rs-pc-001':
          window.open(window.location.origin + '/preview2', '_blank')
          // window.open('http://fjjsjxjy.dev.59iedu.com:8081' + '/preview2', '_blank')
          break
        case 'js-pc-002':
          window.open(window.location.origin + '/preview3', '_blank')
          break
        case 'rs-pc-002':
          window.open(window.location.origin + '/preview3', '_blank')
          break
        case 'js-pc-003':
          window.open(window.location.origin + '/preview4', '_blank')
          break
        case 'rs-pc-003':
          window.open(window.location.origin + '/preview4', '_blank')
          break
        case 'js-pc-004':
          window.open(window.location.origin + '/preview5', '_blank')
          break
        case 'js-pc-005':
          window.open(window.location.origin + '/preview6', '_blank')
          break
        default:
          break
      }
    }
    async getPreviewConfigure() {
      await OnlineSchoolConfigModule.mutationPreview.doWebPreview()
    }
    // async created() {
    // }
  }
</script>
