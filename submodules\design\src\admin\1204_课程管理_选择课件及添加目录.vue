<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--添加目录 / 重命名-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">添加目录 / 重命名</el-button>
        <el-drawer
          title="添加目录 / 重命名"
          :visible.sync="dialog1"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="目录名称：" required>
                    <el-input v-model="form.name" clearable placeholder="请输入目录名称" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">保存</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--选择课件-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">选择课件</el-button>
        <el-drawer
          title="添加精品课程"
          :visible.sync="dialog2"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="8">
                  <el-form-item label="课件名称">
                    <el-input v-model="input" clearable placeholder="请输入课件名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="课件供应商">
                    <el-select v-model="select" clearable filterable placeholder="请选择课件供应商">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="课件分类">
                    <el-cascader clearable filterable multiple :options="cascader" placeholder="请选择课件分类" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="课件供应商">
                    <el-select v-model="select" clearable placeholder="请选择课件类型">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="外部链接课件">
                    <el-select v-model="select" clearable placeholder="请选择是否为外部链接">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--提示栏-->
            <div class="f-mb10">
              <el-alert type="warning" show-icon :closable="false" class="m-alert">
                如涉及到与课件供应商结算的情况，建议同一课程添加相同课件供应商的课件资源。
              </el-alert>
            </div>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="课件名称" min-width="300">
                <template>课件名称课件名称课件名称</template>
              </el-table-column>
              <el-table-column label="课件分类" min-width="180">
                <template>课件分类课件分类</template>
              </el-table-column>
              <el-table-column label="课件教师" min-width="140">
                <template>王老师</template>
              </el-table-column>
              <el-table-column label="转换状态" min-width="120">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="primary" class="badge-status">转换中</el-badge>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <el-badge is-dot type="danger" class="badge-status">转换失败</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">转换成功</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="课件类型" min-width="120">
                <template>课件类型</template>
              </el-table-column>
              <el-table-column label="时长" min-width="100">
                <template>00:21:21</template>
              </el-table-column>
              <el-table-column label="外部链接" align="center" min-width="100">
                <template>是</template>
              </el-table-column>
              <el-table-column label="课件供应商" align="center" min-width="120">
                <template><div class="f-to" title="福建华博科技股份有限公司">福建华博科技股份有限公司</div></template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-checkbox v-model="checked" label="选择"></el-checkbox>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
            <div class="m-btn-bar f-tc f-mt20">
              <el-button>取消</el-button>
              <el-button type="primary">保存</el-button>
            </div>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
