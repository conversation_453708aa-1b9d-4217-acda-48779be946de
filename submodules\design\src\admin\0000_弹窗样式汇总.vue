<template>
  <el-container>
    <el-main>
      <div class="f-p15">
        <el-card shadow="never" class="m-card f-mb15">
          <span class="f-mr10">气泡确认框（表格内的删除）：</span>
          <el-popconfirm title="这是一段内容确定删除吗？">
            <el-button type="text" slot="reference">删除</el-button>
          </el-popconfirm>
        </el-card>
        <el-card shadow="never" class="m-card f-mb15">
          <!--提示弹窗（文字）-->
          <el-button type="primary" @click="dialog1 = true" class="f-mr20">提示弹窗（文字）</el-button>
          <el-dialog title="提示" :visible.sync="dialog1" width="450px" class="m-dialog">
            <div>这是一段信息，这是<span class="f-cr">重点信息</span>，这是<span class="f-fb">加粗信息</span></div>
            <div slot="footer">
              <el-button>取 消</el-button>
              <el-button type="primary">确 定</el-button>
            </div>
          </el-dialog>
          <!--提示弹窗（小图标+文字）-->
          <!--宽度可以根据具体内容调整-->
          <el-button type="primary" @click="dialog2 = true" class="f-mr20">提示弹窗（小图标+文字）</el-button>
          <el-dialog title="提示" :visible.sync="dialog2" width="450px" class="m-dialog">
            <div class="dialog-alert">
              <!--警告-->
              <i class="icon el-icon-warning warning"></i>
              <!--错误-->
              <!--<i class="icon el-icon-error error"></i>-->
              <!--成功-->
              <!--<i class="icon el-icon-success success"></i>-->
              <!--普通信息-->
              <!--<i class="icon el-icon-info info"></i>-->
              <span class="txt">移除后课程包需重新再添加，是否确认移除？</span>
            </div>
            <div slot="footer">
              <el-button>取 消</el-button>
              <el-button type="primary">确 定</el-button>
            </div>
          </el-dialog>
          <!--提示弹窗（大图标+文字）-->
          <el-button type="primary" @click="dialog3 = true" class="f-mr20">提示弹窗（大图标+文字）</el-button>
          <el-dialog title="提示" :visible.sync="dialog3" width="400px" class="m-dialog">
            <div class="dialog-alert is-big">
              <i class="icon el-icon-success success"></i>
              <div class="txt">
                <p class="f-fb f-f16">帐号绑定手机号成功</p>
                <p class="f-f13 f-mt5">可以使用手机号直接登录~</p>
              </div>
            </div>
            <div slot="footer">
              <el-button type="primary">确定</el-button>
            </div>
          </el-dialog>
          <!--导入弹窗-->
          <el-button type="primary" @click="dialog4 = true" class="f-mr20">导入弹窗</el-button>
          <el-dialog title="提示" :visible.sync="dialog4" width="450px" class="m-dialog">
            <div class="dialog-alert is-big">
              <i class="icon el-icon-success success"></i>
              <div class="txt">
                <p class="f-fb">导入成功，是否前往下载数据？</p>
                <p class="f-f13 f-mt5">下载入口：导入任务管理-试题导入</p>
              </div>
            </div>
            <div slot="footer">
              <el-button>暂 不</el-button>
              <el-button type="primary">前往下载</el-button>
            </div>
          </el-dialog>
          <!--导出弹窗-->
          <el-button type="primary" @click="dialog7 = true" class="f-mr20">导出弹窗</el-button>
          <el-dialog title="提示" :visible.sync="dialog7" width="450px" class="m-dialog">
            <div class="dialog-alert is-big">
              <i class="icon el-icon-success success"></i>
              <div class="txt">
                <p class="f-fb">导出成功，是否前往下载数据？</p>
                <p class="f-f13 f-mt5">下载入口：导出任务管理-个人报名订单</p>
              </div>
            </div>
            <div slot="footer">
              <el-button>暂 不</el-button>
              <el-button type="primary">前往下载</el-button>
            </div>
          </el-dialog>
          <!--图片预览弹窗-->
          <el-button type="primary" @click="dialog6 = true" class="f-mr20">图片预览弹窗</el-button>
          <el-dialog :visible.sync="dialog6" width="1100px" class="m-dialog-pic">
            <img src="./assets/images/web-default-banner.jpg" alt="" />
          </el-dialog>
        </el-card>
        <el-card shadow="never" class="m-card f-mb15">
          <!--抽屉-->
          <el-button @click="dialog5 = true" type="primary" class="f-mr20">抽屉（表单）</el-button>
          <el-drawer
            title="添加管理员帐号"
            :visible.sync="dialog5"
            :direction="direction"
            size="800px"
            custom-class="m-drawer"
          >
            <div class="drawer-bd">
              <el-form ref="form" :model="form" label-width="auto" class="m-form">
                <el-form-item label="长文本：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入" />
                </el-form-item>
                <el-form-item label="长选择器：">
                  <el-select v-model="form.region" clearable placeholder="请选择活动区域">
                    <el-option label="区域一" value="shanghai"></el-option>
                    <el-option label="区域二" value="beijing"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="长级联选择器：">
                  <el-cascader clearable :options="cascader"></el-cascader>
                </el-form-item>
                <el-form-item label="多选：">
                  <el-checkbox-group v-model="form.type">
                    <el-checkbox label="美食/餐厅线上活动" name="type"></el-checkbox>
                    <el-checkbox label="地推活动" name="type"></el-checkbox>
                    <el-checkbox label="线下主题活动" name="type"></el-checkbox>
                    <el-checkbox label="单纯品牌曝光" name="type"></el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="单选：">
                  <el-radio-group v-model="form.resource">
                    <el-radio label="线上品牌商赞助"></el-radio>
                    <el-radio label="线下场地免费"></el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="多行文本：" class="is-text">
                  这里是文本内容这里是文本内容这里是文本内容这里是文本内容
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-button>取消</el-button>
                  <el-button type="primary">保存</el-button>
                </el-form-item>
              </el-form>
            </div>
            <!--<div class="drawer-ft m-btn-bar">-->
            <!--  <el-button>取消</el-button>-->
            <!--  <el-button type="primary">保存</el-button>-->
            <!--</div>-->
          </el-drawer>
        </el-card>
        <el-card shadow="never" class="m-card f-mb15">
          <!--选择培训方案-->
          <el-button @click="dialog9 = true" type="primary" class="f-mr20">选择培训方案</el-button>
          <el-drawer
            title="选择培训方案"
            :visible.sync="dialog9"
            :direction="direction"
            size="1200px"
            custom-class="m-drawer"
          >
            <div class="drawer-bd">
              <el-row :gutter="16" class="m-query f-mt10">
                <el-form :inline="true" label-width="auto">
                  <el-col :span="8">
                    <el-form-item label="年度">
                      <el-select v-model="select" clearable placeholder="请选择年度">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="地区">
                      <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="行业">
                      <el-select v-model="select" clearable placeholder="请选择行业">
                        <el-option value="人社"></el-option>
                        <el-option value="建设"></el-option>
                        <el-option value="职业卫生"></el-option>
                        <el-option value="工勤"></el-option>
                        <el-option value="教师"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="学段">
                      <el-select v-model="select" clearable placeholder="请选择学段">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="学科">
                      <el-select v-model="select" clearable placeholder="请选择学科">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="科目类型">
                      <el-select v-model="select" clearable placeholder="请选择科目类型">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训类别">
                      <el-select v-model="select" clearable placeholder="请选择培训类别">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训专业">
                      <el-select v-model="select" clearable filterable placeholder="请选择">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训方案类型">
                      <el-select clearable placeholder="请选择培训方案类型">
                        <el-option label="选项1" value=""></el-option>
                        <el-option label="选项2" value=""></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训方案名称">
                      <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="f-fr">
                    <el-form-item class="f-tr">
                      <el-button type="primary">查询</el-button>
                      <el-button>重置</el-button>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="培训方案名称" min-width="300">
                  <template>培训方案名称培训方案名称培训方案名称</template>
                </el-table-column>
                <el-table-column label="方案类型" min-width="160">
                  <template>培训班-选课规则</template>
                </el-table-column>
                <el-table-column label="培训属性" min-width="240">
                  <template>
                    <p>行业：行业行业</p>
                    <p>地区：为空，不展示</p>
                    <p>科目类型：科目类型</p>
                    <p>培训类别：培训类别</p>
                    <p>培训专业：培训专业培训专业</p>
                    <p>培训年度：2019</p>
                  </template>
                </el-table-column>
                <el-table-column label="学时" min-width="120" align="center">
                  <template>1</template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center" fixed="right">
                  <template>
                    <el-radio v-model="radio" label="1">选择</el-radio>
                  </template>
                </el-table-column>
              </el-table>
              <!--分页-->
              <el-pagination
                background
                class="f-mt15 f-tr"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
            <div class="m-btn-bar drawer-ft">
              <el-button>取消</el-button>
              <el-button type="primary">确定</el-button>
            </div>
          </el-drawer>

          <!--下载发票-->
          <el-button @click="dialog10 = true" type="primary" class="f-mr20">下载发票</el-button>
          <el-dialog title="下载发票" :visible.sync="dialog10" width="400px" class="m-dialog">
            <div class="m-btn-bar is-column f-mlr50">
              <el-button type="primary">下载PDF格式</el-button>
              <el-button type="primary">下载XML格式</el-button>
              <el-button type="primary">下载OFD格式</el-button>
            </div>
          </el-dialog>
        </el-card>
        <el-card shadow="never" class="m-card f-mb15">
          <!--访问网校弹窗-->
          <el-button type="primary" @click="dialog8 = true" class="f-mr20">访问网校弹窗</el-button>
          <el-dialog title="访问网校" :visible.sync="dialog8" width="600px" class="m-dialog">
            <el-form ref="form" :model="form" label-width="auto" class="m-form">
              <el-form-item label="WEB端网址：">
                <span class="f-mr20">https://fjjsjxjy.test1.59iedu.com:9443</span>
                <el-button type="text">复制网址</el-button>
              </el-form-item>
              <el-form-item label="H5页面：">
                <span class="f-mr20 f-c9">H5暂未发布，请先配置！</span>
                <el-button type="text">前往配置</el-button>
              </el-form-item>
              <el-form-item label="H5页面：">
                <div class="f-mb10">
                  <span class="f-mr20">https://fjjsjxjy.test1.59iedu.com:9443</span>
                  <el-button type="text">复制H5网址</el-button>
                </div>
                <img class="u-qr-code" src="./assets/images/default-photo.jpg" />
              </el-form-item>
            </el-form>
            <div slot="footer">
              <el-button>取 消</el-button>
            </div>
          </el-dialog>
          <!--考核重算提示-->
          <el-button type="primary" class="f-mr20" @click="open3">考核重算提示</el-button>
        </el-card>
      </div>
    </el-main>
  </el-container>
</template>
<script>
  export default {
    data() {
      return {
        dialog: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: true,
        activeName: 'fourth',
        activeName1: 'first',
        activeName2: 'first',
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      open3() {
        this.$message({
          message: '本次修改内容如涉及到考核重算，重算任务于程序后台自动执行，即将自动为您跳转到方案管理列表页。',
          type: 'warning',
          duration: 5000,
          customClass: 'm-message'
        })
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
