import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 问卷应用范围枚举
 * scheme 培训方案
 * online_course 线上课程
 * assign_issue 指定培训期别
 * per_issue 培训期别
 */
export enum QuestionnaireAppliedRangeTypeEnum {
  scheme = 0,
  online_course = 1,
  assign_issue = 2,
  per_issue = 3
}

/**
 * @description 问卷应用范围
 */
class QuestionnaireAppliedRangeType extends AbstractEnum<QuestionnaireAppliedRangeTypeEnum> {
  static enum = QuestionnaireAppliedRangeTypeEnum

  constructor(status?: QuestionnaireAppliedRangeTypeEnum) {
    super()
    this.current = status
    this.map.set(QuestionnaireAppliedRangeTypeEnum.scheme, '培训方案')
    this.map.set(QuestionnaireAppliedRangeTypeEnum.online_course, '线上课程')
    this.map.set(QuestionnaireAppliedRangeTypeEnum.assign_issue, '指定培训期别')
    this.map.set(QuestionnaireAppliedRangeTypeEnum.per_issue, '培训期别')
  }
}

export default new QuestionnaireAppliedRangeType()
