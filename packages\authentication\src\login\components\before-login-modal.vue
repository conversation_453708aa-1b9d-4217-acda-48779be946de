<template>
  <div>
    <el-dialog title="系统提示" append-to-body width="480px" :visible.sync="show">
      <div class="m-pop">
        <div class="text">
          <p>
            "技能福建-福建职业培训联盟平台"的培训机构注册和登录功能由“
            <b class="f-cb" style="color: #f23434; font-weight: bold">福建省社会用户实名认证和授权平台</b>
            ”提供统一支持，用户在首次登录时，需注册账号方可使用。完成账号注册后，按页面提示登录系统即可。具体操作步骤如下：
          </p>
          <p class="f-mt5">（1）点击“注册”，跳转注册界面；</p>
          <p>（2）选择注册类别，填写相关信息，完成注册，跳转登录页面；</p>
          <p>（3）按需选择登录方式登录即可。</p>
          <p class="f-mt5" style="text-indent: 0.5em;">若有系统使用相关问题，请联系在线客服。</p>
          <p class="f-mt5" style="text-indent: 0.5em;">若有注册、登录问题，请致电0591-62623959，感谢您的理解与支持</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="u-btn" @click="certain">确认登录</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Vue, Component, Prop, Watch } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    value: boolean

    show = false

    @Watch('value')
    valueChange() {
      this.show = this.value
    }

    certain() {
      this.$emit('on-certain')
    }

    @Watch('show')
    showChange() {
      this.$emit('input', this.show)
    }
  }
</script>
