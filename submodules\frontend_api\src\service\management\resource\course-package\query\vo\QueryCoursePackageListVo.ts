import { CoursePackageRequest } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class QueryCoursePackageListVo {
  // 名称
  name: string

  /*
   是否被方案使用
   */
  isBeingUsedByScheme: boolean

  /**
   * 课程包id集合
   */
  coursePackageId?: Array<string>

  /**
   * 课程包展示名称
   */
  displayName?: string

  /**
   * 排除的课程包id
   */
  excludeCoursePackageId?: Array<string>

  to(): CoursePackageRequest {
    const dto = new CoursePackageRequest()
    dto.name = this.name
    dto.isReferenced = { true: 1, false: 0 }[`${this.isBeingUsedByScheme}`]
    dto.displayName = this.displayName
    dto.coursePackageId = this.coursePackageId
    dto.excludeCoursePackageId = this.excludeCoursePackageId
    return dto
  }
}

export default QueryCoursePackageListVo
