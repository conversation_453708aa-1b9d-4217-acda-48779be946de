import YesOrNoEnumsClass, { YesOrNoEnums } from '@api/service/common/enums/general/YesOrNoEnums'
import CourseStatus, { CourseStatusEnum } from '@api/service/common/enums/course/CourseStatus'
import { TrainingPointCreateRequest, TrainingPointUpdateRequest } from '@api/ms-gateway/ms-teachingplan-v1'
import { TrainingPointResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { TrainingPointCreateRequest as TrainingPointCreateRequestInstitution } from '@api/ms-gateway/ms-teachingplan-dds-v1'

/**
 * 培训点模型
 */
export default class TrainingPlaceInfoDto {
  /**
   * 培训点id
   */
  id = ''
  /**
   * 培训点名称-原始数据
   */
  oldTrainingPlaceName = ''
  /**
   * 培训点名称
   */
  trainingPlaceName = ''
  /**
   * 是否被引用
   */
  isUsed: YesOrNoEnumsClass = new YesOrNoEnumsClass(YesOrNoEnums.no)
  /**
   * 培训点状态
   */
  trainingPlaceStatus: CourseStatus = new CourseStatus(CourseStatusEnum.DISABLE)
  /**
   * 培训点地区名称
   */
  trainingPlaceRegionNames = ''
  /**
   * 培训点地区id
   */
  trainingPlaceRegionIds: string[] = []
  /**
   * 教室名称
   */
  classroomName = ''
  /**
   * 教室Id
   */
  classroomId = ''
  /**
   * 选中的培训地址
   */
  selectAddress = ''
  /**
   * 纬度
   */
  latitude = 0
  /**
   * 经度
   */
  longitude = 0
  /**
   * 选中地址名称
   */
  chooseAddress = ''

  /**
   * 转换模型方法
   */
  static toTrainingPlaceInfo(dto: TrainingPointResponse) {
    const vo = new TrainingPlaceInfoDto()
    vo.id = dto.id
    vo.trainingPlaceName = dto.name
    vo.oldTrainingPlaceName = dto.name
    vo.trainingPlaceRegionIds = dto.areaPath.split('/').filter((res) => res)
    vo.classroomName = dto.classRoom
    if (dto.isReferencedByIssue) {
      vo.isUsed = new YesOrNoEnumsClass(YesOrNoEnums.yes)
    } else {
      vo.isUsed = new YesOrNoEnumsClass(YesOrNoEnums.no)
    }
    if (dto.enabled) {
      vo.trainingPlaceStatus = new CourseStatus(CourseStatusEnum.ENABLE)
    } else {
      vo.trainingPlaceStatus = new CourseStatus(CourseStatusEnum.DISABLE)
    }
    vo.latitude = dto.latitude
    vo.longitude = dto.longitude
    vo.selectAddress = dto.specificAddressData
    return vo
  }

  /**
   * 转换修改培训点模型
   * @param dto
   */
  static toTrainingPointUpdateRequest(dto: TrainingPlaceInfoDto) {
    const vo = new TrainingPointUpdateRequest()
    vo.id = dto.id
    vo.areaPath = `/${dto.trainingPlaceRegionIds.join('/')}`
    vo.name = dto.trainingPlaceName
    vo.classRoom = dto.classroomName
    return vo
  }

  /**
   * 转船创建培训点模型
   * @param dto
   */
  static toTrainingPointCreateRequest(dto: TrainingPlaceInfoDto) {
    const vo = new TrainingPointCreateRequest()
    vo.name = dto.trainingPlaceName
    vo.latitude = dto.latitude
    vo.longitude = dto.longitude
    vo.specificAddress = dto.selectAddress
    vo.classRoom = dto.classroomName
    vo.areaPath = `/${dto.trainingPlaceRegionIds.join('/')}`
    return vo
  }

  static toTrainingPointCreateRequestForOther(dto: TrainingPlaceInfoDto) {
    const vo = new TrainingPointCreateRequestInstitution()
    vo.name = dto.trainingPlaceName
    vo.latitude = dto.latitude
    vo.longitude = dto.longitude
    vo.specificAddress = dto.selectAddress
    vo.classRoom = dto.classroomName
    vo.areaPath = `/${dto.trainingPlaceRegionIds.join('/')}`
    return vo
  }
}
