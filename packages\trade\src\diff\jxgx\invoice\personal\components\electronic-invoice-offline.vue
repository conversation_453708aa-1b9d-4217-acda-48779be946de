<template>
  <jxgx-electronic-invoice-offline ref="electronicInvoiceOffline"></jxgx-electronic-invoice-offline>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import TradeExport from '@api/service/diff/management/jxgx/trade/TradeExport'
  import ElectronicInvoiceOffline from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/electronic-invoice-offline.vue'

  @Component
  class JxgxElectronicInvoiceOffline extends ElectronicInvoiceOffline {
    // 交易导出
    tradeExport = new TradeExport()
    /**
     * 线下专票导出
     */
    async offLinePageInvoiceInExport() {
      return await this.tradeExport.offLinePageInvoiceInExport(this.exportQueryParam)
    }
  }

  @Component({
    components: {
      JxgxElectronicInvoiceOffline
    }
  })
  export default class extends Vue {
    @Ref('electronicInvoiceOffline') electronicInvoiceOffline: JxgxElectronicInvoiceOffline

    /**
     * 线下专票查询
     */
    offLinePageInvoiceInServicer() {
      return this.electronicInvoiceOffline.offLinePageInvoiceInServicer()
    }

    /**
     * 专题线下专票查询
     */
    offLinePageInvoiceInZtServicer() {
      return this.electronicInvoiceOffline.offLinePageInvoiceInZtServicer()
    }
  }
</script>
