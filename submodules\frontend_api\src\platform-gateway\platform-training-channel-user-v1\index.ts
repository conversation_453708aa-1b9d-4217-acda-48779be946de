import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-training-channel-user-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-training-channel-user-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * @author: linxiquan
@Date: 2023/11/3 17:23
@Description: 教师行业专门字段： 用户学段、学科信息
 */
export class SectionAndSubjects {
  /**
   * 学段
   */
  section?: number
  /**
   * 学科
   */
  subjects?: number
}

/**
 * @Description 注册学员差异化请求
<AUTHOR>
@Date 2025/6/17 17:42
 */
export class CreateStudentDifferentiationRequest {
  /**
   * 地区code
   */
  areaCode: string
  /**
   * 【必填】证件号码
   */
  idCard: string
  /**
   * 【必填】证件类型
   */
  idCardType: number
  /**
   * 【必填】密码
   */
  password: string
  /**
   * 【必填】短信验证码
   */
  smsCode?: string
  /**
   * 【必填】用户输入的图片验证码
   */
  captcha?: string
  /**
   * 【必填】申请短信返回的token
   */
  token?: string
  /**
   * 单点登录Token
   */
  loginToken?: string
  /**
   * 【必填】用户名称
   */
  name?: string
  /**
   * 性别
@see com.fjhb.domain.basicdata.api.user.consts.Genders
   */
  gender?: number
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 【必填】所属区域
   */
  area?: string
  /**
   * 工作单位
   */
  companyName?: string
  /**
   * 统一社会信用码
   */
  companyCode?: string
  /**
   * [必填]加密值
   */
  encrypt?: string
  /**
   * 行业信息
   */
  userIndustryInfos?: Array<CreateUserIndustryRequest>
  /**
   * 工作单位所在地区
   */
  companyRegionCode?: string
  /**
   * 【必填-前端校验】邮箱地址
   */
  email?: string
  /**
   * 工作单位性质
   */
  unitNature?: string
  /**
   * 在编情况
   */
  staffingStatus?: string
  /**
   * 是否在专技岗位工作
   */
  isZjPosition?: string
  /**
   * 职称系列
   */
  titleSeries?: string
  /**
   * 职称专业
   */
  titleProfessional?: string
  /**
   * 现有职称等级
   */
  titleGrade?: string
  /**
   * 现有职称资格名称
   */
  titleQualificationName?: string
  /**
   * 现有职称有效范围
   */
  titleEffectiveRange?: string
  /**
   * 最高学历
   */
  highestEducationLevel?: string
}

/**
 * @Description 更新学员差异化请求
<AUTHOR>
@Date 2025/6/18 10:02
 */
export class UpdateStudentDifferentiationRequest {
  /**
   * 地区code
   */
  areaCode: string
  /**
   * 证件类别
   */
  idCardType?: number
  /**
   * 证件号码
   */
  idCard?: string
  /**
   * 短信验证码
   */
  smsCode?: string
  /**
   * 用户输入的图片验证码
   */
  captcha?: string
  /**
   * 申请短信返回的token
   */
  token?: string
  /**
   * 【必填】默认为普通更新false，强制更新true则会携带短信验证码和token
   */
  forcedUpdate?: boolean
  /**
   * 【必填】用户名称
   */
  name?: string
  /**
   * 性别
@see com.fjhb.domain.basicdata.api.user.consts.Genders
   */
  gender?: number
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 【必填】所属区域
   */
  area?: string
  /**
   * 工作单位
   */
  companyName?: string
  /**
   * 统一社会信用码
   */
  companyCode?: string
  /**
   * [必填]加密值
   */
  encrypt?: string
  /**
   * 行业信息
   */
  userIndustryInfos?: Array<CreateUserIndustryRequest>
  /**
   * 工作单位所在地区
   */
  companyRegionCode?: string
  /**
   * 【必填-前端校验】邮箱地址
   */
  email?: string
  /**
   * 工作单位性质
   */
  unitNature?: string
  /**
   * 在编情况
   */
  staffingStatus?: string
  /**
   * 是否在专技岗位工作
   */
  isZjPosition?: string
  /**
   * 职称系列
   */
  titleSeries?: string
  /**
   * 职称专业
   */
  titleProfessional?: string
  /**
   * 现有职称等级
   */
  titleGrade?: string
  /**
   * 现有职称资格名称
   */
  titleQualificationName?: string
  /**
   * 现有职称有效范围
   */
  titleEffectiveRange?: string
  /**
   * 最高学历
   */
  highestEducationLevel?: string
}

/**
 * @Description 管理端更新学员差异化请求
<AUTHOR>
@Date 2025/6/18 10:09
 */
export class UpdateStudentSystemDifferentiationRequest {
  /**
   * 地区code
   */
  areaCode: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 工作单位性质
   */
  unitNature?: string
  /**
   * 在编情况
   */
  staffingStatus?: string
  /**
   * 是否在专技岗位工作
   */
  isZjPosition?: string
  /**
   * 职称系列
   */
  titleSeries?: string
  /**
   * 职称专业
   */
  titleProfessional?: string
  /**
   * 现有职称等级
   */
  titleGrade?: string
  /**
   * 现有职称资格名称
   */
  titleQualificationName?: string
  /**
   * 现有职称有效范围
   */
  titleEffectiveRange?: string
  /**
   * 最高学历
   */
  highestEducationLevel?: string
}

/**
 * @author: zhengp 2022/1/24 15:27
 */
export class CreateUserIndustryRequest {
  /**
   * 所属行业
   */
  industryId?: string
  /**
   * 一级专业类别
   */
  firstProfessionalCategory?: string
  /**
   * 二级专业类别
   */
  secondProfessionalCategory?: string
  /**
   * 职称等级
   */
  professionalQualification?: string
  /**
   * 证书信息
   */
  certificateInfos?: Array<CertificateInfo>
  /**
   * 人员类别（职业卫生行业）
   */
  personnelCategory?: string
  /**
   * 岗位类别（职业卫生行业）
   */
  positionCategory?: string
  /**
   * 技术等级（工勤行业）
   */
  professionalLevel?: string
  /**
   * 工种（工勤行业）
   */
  jobCategoryId?: string
  /**
   * 教师行业 学段、学科信息 ，例：[{&quot;学段1&quot;:&quot;学科1&quot;}, {&quot;学段1&quot;:&quot;学科2&quot;}]
   */
  sectionAndSubjects?: Array<SectionAndSubjects>
  /**
   * 证书类型（药师行业）
   */
  certificatesType?: string
  /**
   * 执证类别（药师行业）
   */
  practitionerCategory?: string
}

export class CertificateAttachment {
  certificateUrl?: string
  name?: string
}

export class CertificateInfo {
  /**
   * 更新时必填
   */
  certificateId?: string
  /**
   * 证书编号
   */
  certificateNo?: string
  /**
   * 证书类别
   */
  certificateCategory?: string
  /**
   * 注册专业
   */
  registerProfessional?: string
  /**
   * 发证日期（起）
   */
  releaseStartTime?: string
  /**
   * 证书有效期（止）
   */
  certificateEndTime?: string
  /**
   * 证书附件
   */
  certificateAttachments?: Array<CertificateAttachment>
}

/**
 * Token 响应对象
<AUTHOR>
 */
export class TokenResponse {
  /**
   * 请求结果状态码
   */
  code: number
  /**
   * token
   */
  token: string
  /**
   * 响应消息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 注册学员账号
   * @param createInfo 注册学员账号信息 {输入的短信验证码、输入手机号、输入的图片验证码、token(申请短信返回的token)}
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async registerStudent(
    createInfo: CreateStudentDifferentiationRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.registerStudent,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 注册学员账号（2024.02.20 ·新）
   * @param createInfo 注册学员账号信息 {输入的短信验证码、输入手机号、输入的图片验证码、token(申请短信返回的token)}
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async registerStudentV2(
    createInfo: CreateStudentDifferentiationRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.registerStudentV2,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 更新学员账号信息（学员端）
   * @param updateInfo 更新学员账号信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateStudent(
    updateInfo: UpdateStudentDifferentiationRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateStudent,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新学员专题信息（管理端）
   * @param updateInfo 更新学员专题信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateStudentByAdmin(
    updateInfo: UpdateStudentSystemDifferentiationRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateStudentByAdmin,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
