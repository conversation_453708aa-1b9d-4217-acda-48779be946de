import MsAccountGateway from '@api/ms-gateway/ms-account-v1'

import { Response } from '@hbfe/common'
import CreateRegionManagerCreateRequestVo from './vo/CreateRegionManagerCreateRequestVo'

/**
 * 创建地区管理员
 */
class CreateRegionManager {
  createRegionManagerParams = new CreateRegionManagerCreateRequestVo()

  /**
   * @description: 创建地区管理员
   * @param {*}
   * @return {*}
   */
  async doCreateRegionManager(): Promise<Response<string>> {
    const res = await MsAccountGateway.registerAreaAdmin(this.createRegionManagerParams)
    const response = new Response<string>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.status = res.status
    response.data = res.data
    return response
  }
}

export default CreateRegionManager
