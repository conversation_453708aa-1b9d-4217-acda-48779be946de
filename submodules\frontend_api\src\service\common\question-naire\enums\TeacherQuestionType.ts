import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum TeacherQuestionTypeEnum {
  /**
   * 教师评价多选题
   */
  multiple = 'a4f3c64f6ac94643907ed03c9e272ec4',
  /**
   * 教师评价单选题
   */
  single = '4e2b8f2bd40c4f279f8cbfffa6e34242'
}
class TeacherQuestionType extends AbstractEnum<TeacherQuestionTypeEnum> {
  static enum = TeacherQuestionTypeEnum

  constructor(status?: TeacherQuestionTypeEnum) {
    super()
    this.current = status
    this.map.set(TeacherQuestionTypeEnum.multiple, '教师评价多选题')
    this.map.set(TeacherQuestionTypeEnum.single, '教师评价单选题')
  }
}
export default TeacherQuestionType
