schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
type Query {
	"""判断当前登录用户是否需要强制修改密码
		0-不需要 1-需要
	"""
	judgeCurrentUserPasswordUpdateForce:Int
}

scalar List
