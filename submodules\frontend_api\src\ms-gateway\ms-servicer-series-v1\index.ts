import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-servicer-series-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-servicer-series-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 附件信息请求
 */
export class AttachmentRequest {
  /**
   * 附件名称
   */
  name?: string
  /**
   * 附件地址
   */
  url?: string
}

/**
 * 对接信息
 */
export class DockingInfoRequest {
  /**
   * 【必填】服务商类型
1.天眼查
2.企查查
   */
  serviceType?: number
  /**
   * 【必填】对接账号信息
   */
  dockingAccount?: string
  /**
   * 秘钥(企查查使用)
   */
  secret?: string
}

/**
 * 学员工作单位对接天眼查/企查查请求类
 */
export class DockingTycAndQccRequest {
  /**
   * 【必填】是否启用工作单位对接天眼查企查查配置
   */
  enabled: boolean
  /**
   * 表单字段约束集合，集合为null不更新
   */
  dockingInfoList?: Array<DockingInfoRequest>
}

/**
 * 精品课程信息
<AUTHOR>
 */
export class ExcellentCourseItemRequest {
  /**
   * 课程ID
   */
  courseId?: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 精品课程分类信息
<AUTHOR>
 */
export class ExcellentCoursesCategoryRequest {
  /**
   * 课程关联的分类ID
   */
  categoryId?: string
  /**
   * 排序
   */
  sort: number
  /**
   * 精品课程列表
   */
  courses?: Array<ExcellentCourseItemRequest>
}

/**
 * 精品课程信息
<AUTHOR>
 */
export class ExcellentCoursesSaveRequest {
  /**
   * 是否使用分类
   */
  usedCategory: boolean
  /**
   * 精品课程分类
<p>使用分类时设置</p>
   */
  categories?: Array<ExcellentCoursesCategoryRequest>
  /**
   * 精品课程列表
<p>不使用分类时设置</p>
   */
  courses?: Array<ExcellentCourseItemRequest>
}

/**
 * 表单字段约束信息
<AUTHOR>
 */
export class FieldConstraintRequest {
  /**
   * 【必填】字段名称
   */
  field?: string
  /**
   * 【必填】是否为关联字段，表示需要其他字段选择值是才会出现该字段
   */
  relate: boolean
  /**
   * 【必填】字段验证器集合
   */
  validators?: Array<FieldConstraintValidatorRequest>
}

/**
 * 字段验证器信息
<AUTHOR>
 */
export class FieldConstraintValidatorRequest {
  /**
   * 【必填】验证器类型，多种类型使用 | 符号分割，如require|email
   */
  type?: string
  /**
   * 关联的字段名称
<p>如果{@link FieldConstraintRequest#isRelate()}为true 必填</p>
   */
  relateField?: string
  /**
   * 关联字段的值集合
<p>如果{@link FieldConstraintRequest#isRelate()}为true 必填</p>
   */
  values?: Array<string>
}

export class IdCardTypeModelRequest {
  /**
   * 【必填】字段名称
   */
  idCardTypeName?: string
  /**
   * 【必填】字段值
   */
  idCardTypeValue: number
  /**
   * 是否可选
   */
  select: boolean
}

/**
 * 线下集体报名配置信息
<AUTHOR>
 */
export class OfflineCollectiveRegisterConfigSaveRequest {
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 线下集体报名入口图片
   */
  registerPortalPicture?: Array<AttachmentRequest>
  /**
   * 线下集体报名标题
   */
  title?: string
  /**
   * 报名模板路径
   */
  templatePath?: string
  /**
   * 报名模板文件名
   */
  templateFileName?: string
  /**
   * 报名步骤集合
   */
  steps?: Array<OfflineCollectiveRegisterStepRequest>
  /**
   * 底部内容
   */
  footContent?: string
}

/**
 * 线下集体报名步骤信息
<AUTHOR>
 */
export class OfflineCollectiveRegisterStepRequest {
  /**
   * 步骤序号
   */
  no: number
  /**
   * 步骤名称
   */
  name?: string
  /**
   * 步骤内容
   */
  content?: string
}

/**
 * 线上集体报名配置信息
<AUTHOR>
 */
export class OnlineCollectiveRegisterConfigSaveRequest {
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 是否启用线上集体报名入口图片
   */
  enabledPicture: boolean
  /**
   * 线上集体报名入口图片
   */
  registerPortalPicture?: Array<AttachmentRequest>
  /**
   * 报名模板路径
   */
  templatePath?: string
  /**
   * 报名模板文件名
   */
  templateFileName?: string
}

/**
 * @Description 网校协议配置
<AUTHOR>
@Date 2025/3/14 9:57
 */
export class OnlineSchoolProtocolConfigRequest {
  /**
   * 是否开启注册协议
   */
  hasRegisterProtocol: boolean
  /**
   * 注册协议名称
   */
  registerProtocolName?: string
  /**
   * 注册协议内容
   */
  registerProtocolContent?: string
  /**
   * 是否开启登录协议
   */
  hasLoginProtocol: boolean
  /**
   * 登录协议名称
   */
  loginProtocolName?: string
  /**
   * 登录协议内容
   */
  loginProtocolContent?: string
}

export class OnlineSchoolSEOConfigRequest {
  /**
   * 描述
   */
  description?: string
  /**
   * 关键词
   */
  keywords?: string
}

/**
 * 学员初始密码强制修改配置
 */
export class StudentForceModifyInitialPasswordConfigRequest {
  /**
   * 是否开启学员初始密码强制修改,开启为true，否则为false
   */
  enabledModifyInitPassword: boolean
}

/**
 * 学员注册表单约束信息
<AUTHOR>
 */
export class StudentRegisterFormConstraintConfigRequest {
  /**
   * 【必填】是否启用学员注册
   */
  enabled: boolean
  /**
   * 是否强制完善学员信息
   */
  forceCompleteStuInfo: boolean
  /**
   * 表单字段约束集合，集合为null不更新
   */
  fieldConstraints?: Array<FieldConstraintRequest>
  /**
   * 是否开启证件照
   */
  enableIdCardPhoto?: boolean
  /**
   * 证件照尺寸
@see com.fjhb.domain.basicdata.api.series.consts.IdCardSizes
{@link this#getEnableIdCardPhoto()}  为true时(开启证件照)，需要设置该属性
   */
  idCardSize?: string
}

export class StudentRegisterIdCardTypeConfigRequest {
  responseList?: Array<IdCardTypeModelRequest>
}

export class UpdateTrainingCategoriesRequest {
  /**
   * 培训类别id
培训类别中每个元素模型包含id和name
name有值.id必须传值，不传值不修改
name无值，id传值，把name置空
   */
  id?: string
  /**
   * 培训类别名称
   */
  name?: string
}

export class ValidateMethodConfigRequest {
  /**
   * 是否启用验证码,默认启用验证码
   */
  enabledCaptcha: boolean
  /**
   * 是否启用滑块拼图
   */
  enabledSlideCircus: boolean
}

/**
 * 学员微信扫码登录配置信息
<AUTHOR>
 */
export class WechatLoginConfigSaveRequest {
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 扫码登录appId
   */
  qrAppId?: string
  /**
   * 扫码登录appSecret
   */
  qrAppSecret?: string
  /**
   * 是否启用微信授权登录设置
   */
  enabledAuth: boolean
  /**
   * 授权登录appId
   */
  authAppId?: string
  /**
   * 授权登录appSecret
   */
  authAppSecret?: string
  /**
   * 是否启用账号登录设置
   */
  enabledAccLogin: boolean
  /**
   * 登陆认证设置，实名认证设置，是否启用登陆实名认证
遴选版本默认实名认证
   */
  enabledRealName?: boolean
}

/**
 * 附件信息
 */
export class Attachment {
  /**
   * 附件名称
   */
  name: string
  /**
   * 附件地址
   */
  url: string
}

export class DockingInfoResponse {
  /**
   * 服务商类型类型
   */
  serviceType: number
  /**
   * 对接账号信息
   */
  dockingAccount: string
  /**
   * 秘钥(企查查使用)
   */
  secret: string
}

export class DockingTycAndQccResponse {
  /**
   * 是否启动对接天眼查/企查查
   */
  enabled: boolean
  /**
   * 对接信息
   */
  dockingInfoList: Array<DockingInfoResponse>
}

/**
 * 精品课程信息
<AUTHOR>
 */
export class ExcellentCourseItemResponse {
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 精品课程分类信息
<AUTHOR>
 */
export class ExcellentCoursesCategoryResponse {
  /**
   * 分类名称
   */
  categoryId: string
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 精品课程列表
   */
  courses: Array<ExcellentCourseItemResponse>
}

/**
 * 精品课程信息
<AUTHOR>
 */
export class ExcellentCoursesResponse {
  /**
   * 是否使用分类
   */
  usedCategory: boolean
  /**
   * 精品课程分类
<p>使用分类时读取</p>
   */
  categories: Array<ExcellentCoursesCategoryResponse>
  /**
   * 精品课程列表
<p>不使用分类时读取</p>
   */
  courses: Array<ExcellentCourseItemResponse>
}

/**
 * 表单字段约束信息
<AUTHOR>
 */
export class FieldConstraintResponse {
  /**
   * 字段名称
   */
  field: string
  /**
   * 是否为关联字段，表示需要其他字段选择值是才会出现该字段
   */
  relate: boolean
  /**
   * 字段验证器集合
   */
  validators: Array<FieldConstraintValidatorResponse>
}

/**
 * 字段验证器信息
<AUTHOR>
 */
export class FieldConstraintValidatorResponse {
  /**
   * 验证器类型，多种类型使用 | 符号分割，如require|email
   */
  type: string
  /**
   * 关联的字段名称
   */
  relateField: string
  /**
   * 关联字段的值集合
   */
  values: Array<string>
}

/**
 * 通用响应类
 */
export class GeneraleResponse {
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

export class IdCardTypeModelResponse {
  /**
   * 【必填】字段名称
   */
  idCardTypeName: string
  /**
   * 【必填】字段值
   */
  idCardTypeValue: number
  /**
   * 是否可选
   */
  select: boolean
}

/**
 * 行业字典数据
<AUTHOR>
 */
export class IndustryDictionaryResponse {
  /**
   * 行业字典数据ID
   */
  id: string
  /**
   * 行业属性字典ID
   */
  propertyId: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 线下集体报名配置信息
<AUTHOR>
 */
export class OfflineCollectiveRegisterConfigResponse {
  /**
   * 【必填】配置ID
   */
  id: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 线下集体报名入口图片
   */
  registerPortalPicture: Array<Attachment>
  /**
   * 线下集体报名标题
   */
  title: string
  /**
   * 报名模板路径
   */
  templatePath: string
  /**
   * 报名模板文件名
   */
  templateFileName: string
  /**
   * 报名步骤集合
   */
  steps: Array<OfflineCollectiveRegisterStepResponse>
  /**
   * 底部内容
   */
  footContent: string
}

/**
 * 线下集体报名步骤信息
<AUTHOR>
 */
export class OfflineCollectiveRegisterStepResponse {
  /**
   * 步骤序号
   */
  no: number
  /**
   * 步骤名称
   */
  name: string
  /**
   * 步骤内容
   */
  content: string
}

/**
 * 线上集体报名配置信息
<AUTHOR>
 */
export class OnlineCollectiveRegisterConfigResponse {
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 报名模板路径
   */
  templatePath: string
  /**
   * 报名模板文件名
   */
  templateFileName: string
  /**
   * 是否启用线上集体报名入口图片
   */
  enabledPicture: boolean
  /**
   * 线上集体报名入口图片
   */
  registerPortalPicture: Array<Attachment>
}

export class OnlineSchoolConfigResponse {
  /**
   * 网校seo
   */
  seoConfig: OnlineSchoolSEO
  /**
   * 强制完善信息：默认为true,
true：当学员信息不全时，强制触发完善信息页面
false：当学员信息不全时，强制跳过完善信息页面
   */
  enabledForceCompleteInfo: boolean
  /**
   * 是否开启学员初始密码强制修改,默认为false
   */
  enabledModifyInitPassword: boolean
  /**
   * 验证方式配置,默认启用验证码验证方式
   */
  validateMethodConfig: ValidateMethods
}

/**
 * @Description 网校协议配置响应
<AUTHOR>
@Date 2025/3/18 9:57
 */
export class OnlineSchoolProtocolConfigResponse {
  /**
   * 是否开启注册协议
   */
  hasRegisterProtocol: boolean
  /**
   * 注册协议名称
   */
  registerProtocolName: string
  /**
   * 注册协议内容
   */
  registerProtocolContent: string
  /**
   * 是否开启登录协议
   */
  hasLoginProtocol: boolean
  /**
   * 登录协议名称
   */
  loginProtocolName: string
  /**
   * 登录协议内容
   */
  loginProtocolContent: string
}

/**
 * 物理地区字典数据
<AUTHOR>
 */
export class PhysicalRegionDictionaryResponse {
  /**
   * 地区字典数据ID
   */
  id: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 业务地区字典数据
<AUTHOR>
 */
export class RegionDictionaryResponse {
  /**
   * 地区字典数据ID
   */
  id: string
  /**
   * 排序
   */
  sort: number
}

export class StudentForceModifyInitialPasswordConfigResponse {
  /**
   * 是否开启学员初始密码强制修改
   */
  enabledModifyInitPassword: boolean
}

/**
 * 学员注册表单注册配置信息
<AUTHOR>
 */
export class StudentRegisterFormConstraintConfigResponse {
  /**
   * 是否启用学员注册
   */
  enabled: boolean
  /**
   * 是否强制完善学员信息
   */
  forceCompleteStuInfo: boolean
  /**
   * 表单字段约束集合
   */
  fieldConstraints: Array<FieldConstraintResponse>
  /**
   * 是否开启证件照
   */
  enableIdCardPhoto: boolean
  /**
   * 证件照尺寸
@see com.fjhb.domain.basicdata.api.series.consts.IdCardSizes
{@link this#getEnableIdCardPhoto()}  为true时(开启证件照)，需要设置该属性
   */
  idCardSize: string
}

/**
 * 学员注册表单注册配置信息
<AUTHOR>
 */
export class StudentRegisterFormConstraintResponse {
  /**
   * 约束验证Token
<p>用于学员注册页面提交时传递给学员注册接口的约束验证token</p>
   */
  token: string
  /**
   * 表单字段约束集合
   */
  fieldConstraints: Array<FieldConstraintResponse>
}

export class StudentRegisterFormIdCardTypeResponse {
  responseList: Array<IdCardTypeModelResponse>
}

/**
 * 学员微信授权登录配置信息
<AUTHOR>
 */
export class WechatLoginConfigResponse {
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 扫码登录appId
   */
  qRAppId: string
  /**
   * 扫码登录appSecret
   */
  qRAppSecret: string
  /**
   * 是否启用微信授权登录设置
   */
  enabledAuth: boolean
  /**
   * 授权登录appId
   */
  authAppId: string
  /**
   * 授权登录appSecret
   */
  authAppSecret: string
  /**
   * 是否启用账号登录设置
   */
  enabledAccLogin: boolean
  /**
   * 登陆认证设置，实名认证设置，是否启用登陆实名认证
遴选版本默认实名认证
   */
  enabledRealName: boolean
}

/**
 * 年度字典数据
<AUTHOR>
 */
export class YearDictionaryResponse {
  /**
   * 年度字典数据ID
   */
  id: string
  /**
   * 排序
   */
  sort: number
}

export class OnlineSchoolSEO {
  /**
   * 描述
   */
  description: string
  /**
   * 关键词
   */
  keywords: string
}

export class ValidateMethods {
  /**
   * 是否启用验证码,默认启用验证码
   */
  enabledCaptcha: boolean
  /**
   * 是否启用滑块拼图
   */
  enabledSlideCircus: boolean
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学员工作单位对接天眼查企查查配置
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getDockingTycAndQcc(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDockingTycAndQcc,
    operation?: string
  ): Promise<Response<DockingTycAndQccResponse>> {
    return commonRequestApi<DockingTycAndQccResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取精品课程信息
   * @return  精品课程信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getExcellentCourses(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getExcellentCourses,
    operation?: string
  ): Promise<Response<ExcellentCoursesResponse>> {
    return commonRequestApi<ExcellentCoursesResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取学员登录时强制修改初始密码配置
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getForceModifyInitialPassword(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getForceModifyInitialPassword,
    operation?: string
  ): Promise<Response<StudentForceModifyInitialPasswordConfigResponse>> {
    return commonRequestApi<StudentForceModifyInitialPasswordConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取服务商专属行业列表
   * @return 行业字典数据列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getIndustries(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getIndustries,
    operation?: string
  ): Promise<Response<Array<IndustryDictionaryResponse>>> {
    return commonRequestApi<Array<IndustryDictionaryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取线下集体报名配置
   * @return 线下集体报名配置信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getOfflineCollectiveRegisterConfig(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOfflineCollectiveRegisterConfig,
    operation?: string
  ): Promise<Response<OfflineCollectiveRegisterConfigResponse>> {
    return commonRequestApi<OfflineCollectiveRegisterConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取线下集体报名配置（需授权）
   * @return 线下集体报名配置信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOfflineCollectiveRegisterConfigWithAuth(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOfflineCollectiveRegisterConfigWithAuth,
    operation?: string
  ): Promise<Response<OfflineCollectiveRegisterConfigResponse>> {
    return commonRequestApi<OfflineCollectiveRegisterConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取线上集体报名配置
   * @return 线上集体报名配置信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getOnlineCollectiveRegisterConfig(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOnlineCollectiveRegisterConfig,
    operation?: string
  ): Promise<Response<OnlineCollectiveRegisterConfigResponse>> {
    return commonRequestApi<OnlineCollectiveRegisterConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取网校配置
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getOnlineSchoolConfig(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOnlineSchoolConfig,
    operation?: string
  ): Promise<Response<OnlineSchoolConfigResponse>> {
    return commonRequestApi<OnlineSchoolConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getOnlineSchoolProtolConfig(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOnlineSchoolProtolConfig,
    operation?: string
  ): Promise<Response<OnlineSchoolProtocolConfigResponse>> {
    return commonRequestApi<OnlineSchoolProtocolConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取服务商物理专属地区列表
   * @return 地区字典数据列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getPhysicalRegions(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPhysicalRegions,
    operation?: string
  ): Promise<Response<Array<PhysicalRegionDictionaryResponse>>> {
    return commonRequestApi<Array<PhysicalRegionDictionaryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取服务商业务专属地区列表
   * @return 地区字典数据列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getRegions(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRegions,
    operation?: string
  ): Promise<Response<Array<RegionDictionaryResponse>>> {
    return commonRequestApi<Array<RegionDictionaryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取学员注册表单约束信息
   * <p>服务商（培训机构）学员注册页面表单约束构建使用</p>
   * @return 学员注册表单约束信息 null表示未开放学员注册
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getStudentRegisterFormConstraint(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentRegisterFormConstraint,
    operation?: string
  ): Promise<Response<StudentRegisterFormConstraintResponse>> {
    return commonRequestApi<StudentRegisterFormConstraintResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取服务商注册
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getStudentRegisterFormIdCardTypeList(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentRegisterFormIdCardTypeList,
    operation?: string
  ): Promise<Response<StudentRegisterFormIdCardTypeResponse>> {
    return commonRequestApi<StudentRegisterFormIdCardTypeResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取学员注册表单约束配置信息
   * @return 学员注册表单约束配置信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getStudentResisterFormConstraintForConfig(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentResisterFormConstraintForConfig,
    operation?: string
  ): Promise<Response<StudentRegisterFormConstraintConfigResponse>> {
    return commonRequestApi<StudentRegisterFormConstraintConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取学员微信登录配置
   * @return 学员微信登录配置信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getWechatLoginConfig(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getWechatLoginConfig,
    operation?: string
  ): Promise<Response<WechatLoginConfigResponse>> {
    return commonRequestApi<WechatLoginConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取服务商专属年度列表
   * @return 年度字典数据列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getYears(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getYears,
    operation?: string
  ): Promise<Response<Array<YearDictionaryResponse>>> {
    return commonRequestApi<Array<YearDictionaryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询线下集体报名配置是否已填写
   * @return true：已填写，false：未填写
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async isOfflineCollectiveRegisterConfigFilled(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.isOfflineCollectiveRegisterConfigFilled,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 存在而存在的口
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async save(
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.save,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员工作单位对接天眼查/企查查-配置
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveDockingTycAndQccConfig(
    request: DockingTycAndQccRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveDockingTycAndQccConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存精品课程信息
   * @param request 精品课程信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveExcellentCoursesConfig(
    request: ExcellentCoursesSaveRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveExcellentCoursesConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存学员登录时强制修改初始密码配置
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveForceModifyInitialPassword(
    request: StudentForceModifyInitialPasswordConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveForceModifyInitialPassword,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存线下集体报名配置
   * @param request 【必填】线下集体报名配置
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveOfflineCollectiveRegisterConfig(
    request: OfflineCollectiveRegisterConfigSaveRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveOfflineCollectiveRegisterConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存线上集体报名配置
   * @param request 【必填】线上集体报名配置
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveOnlineCollectiveRegisterConfig(
    request: OnlineCollectiveRegisterConfigSaveRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveOnlineCollectiveRegisterConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存网校协议配置
   * @param request 网校协议入参
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveOnlineSchoolProtocolConfig(
    request: OnlineSchoolProtocolConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveOnlineSchoolProtocolConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存网校SEO配置
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveOnlineSchoolSEOConfig(
    request: OnlineSchoolSEOConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveOnlineSchoolSEOConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存学员注册表单约束配置
   * @param request 【必填】表单约束配置信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveStudentRegisterFormConstraintConfig(
    request: StudentRegisterFormConstraintConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveStudentRegisterFormConstraintConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存学员注册表单约束配置
   * @param request 【必填】表单约束配置信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveStudentRegisterFormIdCardTypeList(
    request: StudentRegisterIdCardTypeConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveStudentRegisterFormIdCardTypeList,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存验证设置
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveValidateMethodConfig(
    request: ValidateMethodConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveValidateMethodConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存学员微信登录配置
   * @param request 学员微信登录配置信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveWechatLoginConfig(
    request: WechatLoginConfigSaveRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveWechatLoginConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改培训类别名称
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingCategoryName(
    request: UpdateTrainingCategoriesRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingCategoryName,
    operation?: string
  ): Promise<Response<GeneraleResponse>> {
    return commonRequestApi<GeneraleResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
