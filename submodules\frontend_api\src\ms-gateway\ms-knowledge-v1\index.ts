import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-knowledge-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-knowledge-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 学员申请提交学习心得请求
<AUTHOR>
@date 2023/12/4
 */
export class ApplyLearningExperienceRequest {
  /**
   * 学习Token
   */
  studentLearningToken?: string
  /**
   * 资源类型：1-班级 2-课程
   */
  resourceType: number
  /**
   * 学习资源id(班级id or 课程id)
   */
  resourceId?: string
  /**
   * 课程大纲id(课程心得创建时需要)
   */
  outlineId?: string
  /**
   * 学习心得主题id
   */
  learningExperienceTopicId?: string
}

/**
 * 学员心得审核请求
<AUTHOR>
@date 2023/12/4
 */
export class AuditStudentLearningExperienceRequest {
  /**
   * 学员心得id
   */
  studentExperienceId: string
  /**
   * 审核意见
   */
  remark?: string
  /**
   * 得分
   */
  score: number
  /**
   * 主题心得类型 1-班级  2-课程
   */
  learningExperienceTopicType: number
}

/**
 * 学员心得撤回请求
<AUTHOR>
@date 2023/12/15 16:32
 */
export class CancelStudentLearningExperienceRequest {
  /**
   * 学员心得id
   */
  studentExperienceId: string
  /**
   * 主题心得类型 1-班级  2-课程
   */
  learningExperienceTopicType: number
}

/**
 * 学员学习心得校验请求
<AUTHOR>
@date 2023/12/12 20:57
 */
export class CheckLearningExperienceRequest {
  /**
   * 学员心得token
   */
  learningExperienceToken?: string
  /**
   * 主题心得类型 1-班级  2-课程
   */
  learningExperienceTopicType: number
}

/**
 * 新增课程心得主体信息请求
<AUTHOR>
@date 2023/12/4 10:11
 */
export class CreateCourseLearningExperienceTopicRequest {
  /**
   * 学习心得id
   */
  learningExperienceId?: string
  /**
   * 学习心得主题
   */
  experienceTopicName?: string
  /**
   * 学习心得内容id
   */
  descriptionContentId?: string
  /**
   * 参加活动时间类型
@see ParticipateTimeType
   */
  participateTimeType: number
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 结束时间
   */
  endTime?: string
  /**
   * 参与形式
@see ParticipateType
   */
  participateType: number
  /**
   * 提交要求(如果是附件提交，就是文件大小，如果是在线编辑，就是字数)
   */
  submitLimitNum: number
  /**
   * 审核方式
@see AuditType
   */
  auditType: number
  /**
   * 提交次数
   */
  submitLimitCount: number
  /**
   * 课程id列表
   */
  courseIds?: Array<string>
  /**
   * 通过分数
   */
  passScore: number
  /**
   * 总分
   */
  totalScore: number
  /**
   * 是否必选
   */
  isRequired: boolean
}

/**
 * 查询为完成的学习心得主题请求
<AUTHOR> By Cb
@since 2024/01/04 10:40
 */
export class QueryUnCompleteLearningExperienceTopicRequest {
  /**
   * 学习方案ID
   */
  learningSchemeId: string
  /**
   * 学号
   */
  studentNo: string
}

/**
 * 删除学员心得请求
<AUTHOR>
@date 2023/12/15 16:34
 */
export class RemoveStudentLearningExperienceRequest {
  /**
   * 学员心得id
   */
  studentExperienceId: string
  /**
   * 主题心得类型 1-班级  2-课程
   */
  learningExperienceTopicType: number
}

/**
 * 学员保存学习心得请求请求
<AUTHOR>
@date 2023/12/4
 */
export class SaveLearningExperienceRequest {
  /**
   * 学员心得token
   */
  learningExperienceToken: string
  /**
   * 学习心得信息
   */
  content?: string
  /**
   * 编辑形式  1.提交附件 2.在线编辑
   */
  participateType: number
  /**
   * 主题心得类型 1-班级  2-课程
   */
  learningExperienceTopicType: number
}

/**
 * 学员提交学习心得请求
<AUTHOR>
@date 2023/12/4 11:37
 */
export class SubmitLearningExperienceRequest {
  /**
   * 学员心得token
   */
  learningExperienceToken?: string
  /**
   * 学习心得信息
   */
  content?: string
  /**
   * 编辑形式  1.提交附件 2.在线编辑
   */
  participateType: number
  /**
   * 主题心得类型 1-班级  2-课程
   */
  learningExperienceTopicType: number
}

/**
 * 修改课程心得主题信息请求
<AUTHOR>
@date 2023/12/4 11:22
 */
export class UpdateCourseLearningExperienceTopicRequest {
  /**
   * 课程心得主题信息
   */
  learningExperienceTopicId?: string
  /**
   * 学习心得id
   */
  learningExperienceId?: string
  /**
   * 学习心得主题
   */
  experienceTopicName?: string
  /**
   * 学习心得内容id
   */
  descriptionContentId?: string
  /**
   * 参加活动时间类型
@see ParticipateTimeType
   */
  participateTimeType: number
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 结束时间
   */
  endTime?: string
  /**
   * 参与形式
@see ParticipateType
   */
  participateType: number
  /**
   * 提交要求(如果是附件提交，就是文件大小，如果是在线编辑，就是字数)
   */
  submitLimitNum: number
  /**
   * 审核方式
@see AuditType
   */
  auditType: number
  /**
   * 提交次数
   */
  submitLimitCount: number
  /**
   * 课程id列表
   */
  courseIds?: Array<string>
  /**
   * 通过分数
   */
  passScore: number
  /**
   * 总分
   */
  totalScore: number
  /**
   * 是否必选
   */
  isRequired: boolean
}

/**
 * 更新主题文本信息请求
<AUTHOR>
@date 2023/12/4
 */
export class UpdateLearningExperienceTopicContentRequest {
  /**
   * 学员心得活动内容id
   */
  contentId: string
  /**
   * 学员心得活动内容
   */
  content?: string
}

/**
 * 校验课程是否存在请求
<AUTHOR>
@date 2023/12/29 16:24
 */
export class VerifyCourseRequest {
  /**
   * 学号
   */
  studentNo?: string
  /**
   * 资源类型：1-班级 2-课程
   */
  resourceType: number
  /**
   * 学习资源id(班级id or 课程id)
   */
  resourceId?: string
  /**
   * 课程大纲id(课程心得创建时需要)
   */
  outlineId?: string
  /**
   * 学习心得主题id
   */
  learningExperienceTopicId?: string
}

/**
 * 申请提交学习心得返回
1001 - 未完成课程学习
1002 - 未完成必选学习心得
<AUTHOR>
@date 2023/12/4
 */
export class ApplyLearningExperienceResponse {
  /**
   * 学习心得Token
   */
  learningExperienceToken: string
  /**
   * 错误码
   */
  code: string
  /**
   * 错误信息
   */
  msg: string
}

/**
 * 学员心得审核返回值
<AUTHOR>
@date 2023/12/4 11:41
 */
export class AuditStudentLearningExperienceResponse {
  /**
   * 错误码
   */
  code: string
  /**
   * 错误信息
   */
  msg: string
}

/**
 * 学员学习心得撤回返回值
<AUTHOR>
@date 2023/12/4 11:42
 */
export class CancelStudentLearningExperienceResponse {
  /**
   * 错误码
   */
  code: string
  /**
   * 错误信息
   */
  msg: string
}

/**
 * 新增课程心得主题信息返回值
<AUTHOR>
@date 2023/12/4 10:53
 */
export class CreateCourseLearningExperienceTopicResponse {
  /**
   * 课程心得主题信息id
   */
  learningExperienceTopicId: string
  /**
   * 错误码
   */
  code: string
  /**
   * 错误信息
   */
  msg: string
}

/**
 * 查询未完成的学习心得主题响应
<AUTHOR> By Cb
@since 2024/01/03 9:56
 */
export class QueryUnCompleteLearningExperienceTopicResponse {
  /**
   * code
1001 - 无未参加的学习心得主题
1002 - 存在未参加学习心得主题，但是只有课程心得主题
1003 - 存在未参加学习心得主题，且不只有课程心得主题
   */
  code: string
  /**
   * message
   */
  message: string
}

/**
 * 学员学习心得删除
<AUTHOR>
@date 2023/12/4 11:42
 */
export class RemoveStudentLearningExperienceResponse {
  /**
   * 错误码
   */
  code: string
  /**
   * 错误信息
   */
  msg: string
}

/**
 * 保存心得主题文本信息返回
<AUTHOR>
@date 2023/12/4
 */
export class SaveExperienceTopicContentResponse {
  /**
   * 错误码
   */
  code: string
  /**
   * 错误信息
   */
  msg: string
  /**
   * 文本信息id
   */
  contentId: string
}

/**
 * 保存学习心得返回
<AUTHOR>
@date 2023/12/4
 */
export class SaveLearningExperienceResponse {
  /**
   * 错误码
   */
  code: string
  /**
   * 错误信息
   */
  msg: string
}

/**
 * 学员提交学习心得返回值
<AUTHOR>
@date 2023/12/4 11:40
 */
export class SubmitLearningExperienceResponse {
  /**
   * 错误码
   */
  code: string
  /**
   * 错误信息
   */
  msg: string
}

/**
 * 修改课程心得主题信息返回值
<AUTHOR>
@date 2023/12/4 11:23
 */
export class UpdateCourseLearningExperienceTopicResponse {
  /**
   * 心得主题信息id
   */
  learningExperienceTopicId: string
  /**
   * 错误码
   */
  code: string
  /**
   * 错误信息
   */
  msg: string
}

/**
 * 更新心得主题文本信息返回值
<AUTHOR>
@date 2023/12/4 11:46
 */
export class UpdateExperienceTopicContentResponse {
  /**
   * 文本信息id
   */
  contentId: string
  /**
   * 错误码
   */
  code: string
  /**
   * 错误信息
   */
  msg: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询未完成的学习心得主题
   * @param request :
   * @return com.fjhb.ms.knowledge.v1.kernel.gateway.graphql.response.QueryUnCompleteLearningExperienceTopicResponse
   * <AUTHOR> By Cb
   * @date 2024/1/4 10:43
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryUnCompleteLearningExperienceTopic(
    request: QueryUnCompleteLearningExperienceTopicRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryUnCompleteLearningExperienceTopic,
    operation?: string
  ): Promise<Response<QueryUnCompleteLearningExperienceTopicResponse>> {
    return commonRequestApi<QueryUnCompleteLearningExperienceTopicResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员申请提交学习心得
   * @param request
   * @return {@link ApplyLearningExperienceResponse}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyLearningExperience(
    request: ApplyLearningExperienceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyLearningExperience,
    operation?: string
  ): Promise<Response<ApplyLearningExperienceResponse>> {
    return commonRequestApi<ApplyLearningExperienceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员心得审核
   * @param request
   * @return {@link AuditStudentLearningExperienceResponse}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async auditStudentLearningExperience(
    request: AuditStudentLearningExperienceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.auditStudentLearningExperience,
    operation?: string
  ): Promise<Response<AuditStudentLearningExperienceResponse>> {
    return commonRequestApi<AuditStudentLearningExperienceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员心得撤回
   * @return {@link CancelStudentLearningExperienceResponse}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelStudentLearningExperience(
    request: CancelStudentLearningExperienceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.cancelStudentLearningExperience,
    operation?: string
  ): Promise<Response<CancelStudentLearningExperienceResponse>> {
    return commonRequestApi<CancelStudentLearningExperienceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验接口
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async check(
    request: CheckLearningExperienceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.check,
    operation?: string
  ): Promise<Response<SubmitLearningExperienceResponse>> {
    return commonRequestApi<SubmitLearningExperienceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 新增课程心得主题信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCourseLearningExperienceTopic(
    request: CreateCourseLearningExperienceTopicRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createCourseLearningExperienceTopic,
    operation?: string
  ): Promise<Response<CreateCourseLearningExperienceTopicResponse>> {
    return commonRequestApi<CreateCourseLearningExperienceTopicResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学习心得删除
   * @return {@link RemoveStudentLearningExperienceResponse}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeStudentLearningExperience(
    request: RemoveStudentLearningExperienceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeStudentLearningExperience,
    operation?: string
  ): Promise<Response<RemoveStudentLearningExperienceResponse>> {
    return commonRequestApi<RemoveStudentLearningExperienceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员保存学习心得
   * @param request
   * @return {@link SaveLearningExperienceResponse}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveLearningExperience(
    request: SaveLearningExperienceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveLearningExperience,
    operation?: string
  ): Promise<Response<SaveLearningExperienceResponse>> {
    return commonRequestApi<SaveLearningExperienceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存心得主题文本信息
   * @param content 学员心得活动内容
   * @return {@link SaveExperienceTopicContentResponse}
   * @param mutate 查询 graphql 语法文档
   * @param content 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveLearningExperienceTopicContent(
    content: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveLearningExperienceTopicContent,
    operation?: string
  ): Promise<Response<SaveExperienceTopicContentResponse>> {
    return commonRequestApi<SaveExperienceTopicContentResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { content },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员提交学习心得
   * @param request
   * @return {@link SubmitLearningExperienceResponse}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async submitLearningExperience(
    request: SubmitLearningExperienceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.submitLearningExperience,
    operation?: string
  ): Promise<Response<SubmitLearningExperienceResponse>> {
    return commonRequestApi<SubmitLearningExperienceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改课程心得主题信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCourseLearningExperienceTopic(
    request: UpdateCourseLearningExperienceTopicRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateCourseLearningExperienceTopic,
    operation?: string
  ): Promise<Response<UpdateCourseLearningExperienceTopicResponse>> {
    return commonRequestApi<UpdateCourseLearningExperienceTopicResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新心得主题文本信息
   * @param request
   * @return {@link UpdateExperienceTopicContentResponse}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateLearningExperienceTopicContent(
    request: UpdateLearningExperienceTopicContentRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateLearningExperienceTopicContent,
    operation?: string
  ): Promise<Response<UpdateExperienceTopicContentResponse>> {
    return commonRequestApi<UpdateExperienceTopicContentResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验课程是否被删除
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async verifyExists(
    request: VerifyCourseRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.verifyExists,
    operation?: string
  ): Promise<Response<SubmitLearningExperienceResponse>> {
    return commonRequestApi<SubmitLearningExperienceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
