<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/question')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/question' }">试题管理</el-breadcrumb-item>
      <el-breadcrumb-item>创建试题</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="questionParams" :rules="rules" label-width="auto" class="m-form f-mt20">
              <el-form-item label="所属题库：" required class="form-l">
                <questionCascader v-model="relatedLibrary"></questionCascader>
              </el-form-item>
              <el-form-item label="关联课程" prop="relateCourseIds" class="form-l">
                <course-drawer :value.sync="relateCourseIds" ref="relatedCourse"></course-drawer>
              </el-form-item>
              <el-form-item label="试题题型" prop="questionType">
                <el-radio-group
                  v-model="createQuestion.questionParams.questionType"
                  @change="createNewQuestionType"
                  size="medium"
                >
                  <el-radio v-for="(item, index) in questionTypeList" :key="index" :label="item.value"
                    >{{ item.title }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="难度" prop="questionDifficulty">
                <el-radio-group v-model="createQuestion.questionParams.questionDifficulty">
                  <el-radio :label="1">难</el-radio>
                  <el-radio :label="2">中等</el-radio>
                  <el-radio :label="3">简单</el-radio>
                </el-radio-group>
              </el-form-item>

              <single
                v-if="createQuestion.questionParams.questionType === 1 && show"
                :key="`1${basicKey}`"
                question-type="2"
                :create-question-info.sync="createQuestion.questionParams"
                ref="singleRef"
              />
              <judge
                v-if="createQuestion.questionParams.questionType === 4"
                :create-question-info.sync="createQuestion.questionParams"
                parent-component-question-type="1"
                :key="`4${basicKey}`"
                ref="judgeRef"
              />
              <multiple
                v-if="createQuestion.questionParams.questionType === 2"
                :key="`2${basicKey}`"
                :create-question-info.sync="createQuestion.questionParams"
                question-type="3"
                ref="multipleRef"
              />
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="saveQuestion" :loading="loading">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import questionCascader from '@hbfe/jxjy-admin-question/src/components/question-cascader.vue'
  import CourseDrawer from '@hbfe/jxjy-admin-question/src/components/course-drawer.vue'
  import CreateQuestion from '@api/service/management/resource/question/mutation/CreateQuestion'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { QuestionTypeEnum } from '@api/service/common/enums/question/QuestionType'
  import CreateQuestionVo from '@api/service/management/resource/question/mutation/vo/create/CreateQuestionVo'
  import { debounce, bind } from 'lodash-decorators'
  import { UiPage } from '@hbfe/common'
  @Component({
    components: {
      CourseDrawer,
      Single: () => import('@hbfe/jxjy-admin-question/src/components/single.vue'), // 单选题
      Multiple: () => import('@hbfe/jxjy-admin-question/src/components/multiple.vue'), // 多选题
      Judge: () => import('@hbfe/jxjy-admin-question/src/components/judge.vue'), // 判断题
      questionCascader
    }
  })
  export default class extends Vue {
    @Ref('judgeRef')
    judgeComponent: any

    @Ref('singleRef')
    singleComponent: any

    @Ref('multipleRef')
    multipleComponent: any

    basicKey = new Date().getTime()
    libraryFactory = ResourceModule.queryQuestionLibraryFactory

    /*
      创建入参对象， 默认是 CreateRadioQuestionDto
      切换创建的题型时，
      入参为试题类型； 调用 this.createQuestion.createVoByQuestionType(questionType)
    */
    // 获取创建试题实例
    createQuestion: CreateQuestion = ResourceModule.mutationQuestionFactory.getCreateQuestion()
    // 创建请求入参
    questionParams: CreateQuestionVo = this.createQuestion.questionParams

    /**
     *  UI配置的常量
     */
    uiConfig = {
      refName: {
        judge: 'judgeRef',
        single: 'singleRef',
        multiple: 'multipleRef',
        synthesis: 'synthesisRef'
      },
      commit: false
    }
    //防抖
    loading = false
    relateCourseIds = ''
    // 题库id
    relatedLibrary = ''
    // questionForm = new Question() //详情数据
    /**
     * 销毁组件
     */
    alive = false
    show = false
    // 选中的题库id
    libraryIds: string[] = []
    /**
     *  获取系统提供的试题题型
     */
    questionTypeList = [
      { value: 1, title: '单选题' },
      { value: 2, title: '多选题' },
      { value: 4, title: '判断题' }
    ]
    rules = {
      libraryId: [
        {
          required: true,
          message: '请选择题库',
          trigger: 'blur'
        }
      ],
      questionType: [
        {
          required: true,
          message: '请选择关联课程',
          trigger: 'blur'
        }
      ],
      // title: [
      //   {
      //     required: true,
      //     message: '请输入试题题目',
      //     trigger: 'blur'
      //   }
      // ],
      questionDifficulty: [
        {
          required: true,
          message: '请选择难度',
          trigger: 'blur'
        }
      ]
    }
    @Watch('createQuestion.questionParams.relateCourseId', {
      deep: true,
      immediate: true
    })
    relateCourseIdChange(val: any) {
      if (val) {
        console.log(val, 'createQuestion.questionParams.relateCourseId')
      }
    }
    @Watch('relatedLibrary', {
      deep: true,
      immediate: true
    })
    questionParamsChange(val: any) {
      if (val) {
        console.log(val, 'relatedLibrary')
      }
    }
    // deactivated() {
    //   this.alive = false
    // }
    /**
     * 表单校验
     */
    public validForm() {
      if (!this.createQuestion.questionParams.libraryId) {
        this.$message.warning('所属题库不能为空。')
        return false
      }
      // createQuestion.questionParams.questionType
      if (this.createQuestion.questionParams.questionType === 4) {
        return this.judgeComponent.validForm()
      } else if (this.createQuestion.questionParams.questionType === 1) {
        return this.singleComponent.validForm()
      } else if (this.createQuestion.questionParams.questionType === 2) {
        return this.multipleComponent.validForm()
      }
      return true
    }
    async checkQuestionLibrary() {
      const page = new UiPage()
      page.pageNo = 1
      page.pageSize = 200
      const res = await this.libraryFactory.queryQuestionLibraryMultiton.queryQuestionBankLibraryItem(
        page,
        this.relatedLibrary
      )
      if (res.data?.length) {
        this.$message.error('当前选择的题库不是末级题库，请刷新页面后重新选择！')
        return false
      } else return true
    }

    // 创建试题
    @bind
    @debounce(200)
    async saveQuestion() {
      this.loading = true
      // this.createQuestion.questionParams.libraryId = this.relatedLibrary[this.relatedLibrary.length - 1]
      this.createQuestion.questionParams.libraryId = this.relatedLibrary
      this.createQuestion.questionParams.relateCourseId = this.relateCourseIds

      if (this.createQuestion.questionParams.questionType === 4) {
        // if(this.createQuestion)
      }
      const success = this.validForm() //表单验证
      // 修复多选题正确答案不选缺失提示
      if (this.createQuestion.questionParams.questionType === 2) {
        if (this.multipleComponent.createQuestion.correctAnswerIds?.length <= 0) {
          this.loading = false
          return this.$message.warning('正确答案不能为空')
        }
      }
      if (!success) {
        this.loading = false

        this.uiConfig.commit = false
        return
      }
      if (!(await this.checkQuestionLibrary())) {
        this.loading = false
        return
      }
      try {
        const res = await this.createQuestion.doCreateQuestion()
        console.log(res, '创建试题')
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.$router.push('/resource/question')
        } else {
          this.$message.error(`${res.errors[0].message}`)
          this.loading = false
        }
      } catch (e) {
        console.log(e)
        this.loading = false
      }

      // 清除
      this.rest()
    }
    rest() {
      // this.questionForm = new Question()
      // this.libraryIds = []
      // console.log(this.$refs.relatedCourse)
      // ;(this.$refs.relatedCourse as any).clear()
      // this.questionForm.questionType = QuestionType.SINGLE_CHOICE

      this.questionParams.questionType = 1
    }
    createNewQuestionType() {
      this.createQuestion.createVoByQuestionType(this.createQuestion.questionParams.questionType)
      this.basicKey = new Date().getTime()
    }

    created() {
      this.show = false
      this.init()
      this.show = true
    }
    /**
     * 初始化
     */
    init() {
      // this.alive = true
      this.libraryIds = []
    }
    // cancel() {
    //   this.$router.push('/resource/question')
    // }
    cancel() {
      this.$confirm('是否放弃编辑？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$router.push('/resource/question')
      })
    }
  }
</script>
