<route-params content="/:id/:questionType"></route-params>

<script lang="ts">
  //   import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import Detail from '@hbfe/jxjy-admin-question/src/detail.vue'
  //   import {
  //     // 江苏工考一体化平台子项目管理员
  //     ZXMGLY,
  //     // 内置地区管理员角色
  //     DQGLY,
  //     // 施教机构管理员
  //     WXGLY,
  //     // 学员
  //     XY
  //   } from '@/models/RoleTypes'
  //   @RoleTypeDecorator({
  //     query: [WXGLY],
  //     export: [WXGLY],
  //     create: [WXGLY],
  //     detail: [WXGLY],
  //     modify: [WXGLY],
  //     enable: [WXGLY],
  //     batchcreate: [WXGLY],
  //     queryTask: [WXGLY],
  //     disable: [WXGLY],
  //     remove: [WXGLY]
  //   })
  export default class extends Detail {
    // todo
  }
</script>
