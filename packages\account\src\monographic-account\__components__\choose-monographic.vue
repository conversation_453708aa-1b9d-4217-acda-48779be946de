<template>
  <div>
    <el-drawer
      title="选择专题"
      :visible.sync="showMonographicDialog"
      size="800px"
      :wrapper-closable="false"
      :close-on-press-escape="false"
      :before-close="closeChoose"
      custom-class="m-drawer"
    >
      <div class="drawer-bd">
        <el-row :gutter="16" class="m-query f-mt10">
          <el-form :inline="true" label-width="auto" :model="thematicManagementList.queryParam">
            <el-col :span="8">
              <el-form-item label="专题名称">
                <el-input
                  v-model="thematicManagementList.queryParam.subjectName"
                  clearable
                  placeholder="请输入专题名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="专题类型">
                <el-select
                  v-model="thematicManagementList.queryParam.subjectType"
                  multiple
                  clearable
                  placeholder="请选择专题类型"
                >
                  <el-option label="地区" :value="1"></el-option>
                  <el-option label="行业" :value="2"></el-option>
                  <el-option label="单位" :value="3"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary" @click="searchList" :disabled="loading">查询</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <el-table ref="topicsListRef" :data="topicsList" v-loading="loading" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="专题名称" min-width="150">
            <template v-slot="{ row }">{{ row.basicInfo.subjectName }}</template>
          </el-table-column>
          <el-table-column label="专题类型" min-width="150">
            <template v-slot="{ row }">
              <el-tooltip placement="top" effect="light" v-if="row.basicInfo.suiteIndustry">
                <div slot="content">
                  <i class="f-c4">
                    <el-tag type="warning" size="mini" class="f-mr5">行业</el-tag>{{ row.basicInfo.suiteIndustry }}</i
                  >
                </div>
                <el-button type="text" class="f-to-three">
                  <i class="f-c4">
                    <el-tag type="warning" size="mini" class="f-mt5"> 行业 </el-tag>
                    {{ row.basicInfo.suiteIndustry }}</i
                  >
                </el-button>
              </el-tooltip>
              <el-tooltip placement="top" effect="light" v-if="row.basicInfo.suiteArea">
                <div slot="content">
                  <i class="f-c4"
                    ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag>{{ row.basicInfo.suiteArea }}</i
                  >
                </div>
                <el-button type="text" class="f-to-three"
                  ><i class="f-c4"
                    ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag>{{ row.basicInfo.suiteArea }}</i
                  ></el-button
                >
              </el-tooltip>
              <el-tooltip placement="top" effect="light" v-if="row.basicInfo.unitName">
                <div slot="content">
                  <i class="f-c4">
                    <el-tag type="warning" size="mini" class="f-mr5">单位</el-tag>{{ row.basicInfo.unitName }}</i
                  >
                </div>
                <el-button type="text" class="f-to-three">
                  <i class="f-c4">
                    <el-tag type="warning" size="mini" class="f-mt5"> 单位 </el-tag>
                    {{ row.basicInfo.unitName }}</i
                  >
                </el-button>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="显示在网校" min-width="120" align="center">
            <template v-slot="{ row }">{{ row.basicInfo.displayInSchool ? '显示' : '不显示' }}</template>
          </el-table-column>
          <el-table-column label="状态" min-width="100">
            <template v-slot="{ row }">
              <div v-if="row.enable">
                <el-badge is-dot type="success" class="badge-status">启用</el-badge>
              </div>
              <div v-else>
                <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <!--            <template v-slot="{ row }">-->
            <!--              <el-checkbox-->
            <!--                @change="selectChange($index)"-->
            <!--                :checked="row.isChecked"-->
            <!--                label="选择"-->
            <!--              ></el-checkbox>-->
            <!--            </template>-->
            <template v-slot="{ row }">
              <el-button size="mini" type="text" v-if="row.isChecked" @click="selectChange(row.topicID)">
                取消选择
              </el-button>
              <el-button size="mini" type="text" v-if="!row.isChecked" @click="selectChange(row.topicID)">
                选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
        <div class="f-f18 f-fb f-pt30 f-pb10">
          已选择的专题<span class="f-cr">{{ topicsSelectedList.length }}</span
          >个
        </div>
        <el-table ref="topicsListSelectedRef" :data="topicsSelectedList" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="专题名称" min-width="150">
            <template v-slot="{ row }">{{ row.basicInfo.subjectName }}</template>
          </el-table-column>
          <el-table-column label="专题类型" min-width="150">
            <template v-slot="{ row }">
              <el-tooltip placement="top" effect="light" v-if="row.basicInfo.suiteIndustry">
                <div slot="content">
                  <i class="f-c4">
                    <el-tag type="warning" size="mini" class="f-mr5">行业</el-tag>{{ row.basicInfo.suiteIndustry }}</i
                  >
                </div>
                <el-button type="text" class="f-to-three">
                  <i class="f-c4">
                    <el-tag type="warning" size="mini" class="f-mt5"> 行业 </el-tag>
                    {{ row.basicInfo.suiteIndustry }}</i
                  >
                </el-button>
              </el-tooltip>
              <el-tooltip placement="top" effect="light" v-if="row.basicInfo.suiteArea">
                <div slot="content">
                  <i class="f-c4"
                    ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag>{{ row.basicInfo.suiteArea }}</i
                  >
                </div>
                <el-button type="text" class="f-to-three"
                  ><i class="f-c4"
                    ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag>{{ row.basicInfo.suiteArea }}</i
                  ></el-button
                >
              </el-tooltip>
              <el-tooltip placement="top" effect="light" v-if="row.basicInfo.unitName">
                <div slot="content">
                  <i class="f-c4">
                    <el-tag type="warning" size="mini" class="f-mr5">单位</el-tag>{{ row.basicInfo.unitName }}</i
                  >
                </div>
                <el-button type="text" class="f-to-three">
                  <i class="f-c4">
                    <el-tag type="warning" size="mini" class="f-mt5"> 单位 </el-tag>
                    {{ row.basicInfo.unitName }}</i
                  >
                </el-button>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="显示在网校" min-width="120" align="center">
            <template v-slot="{ row }">{{ row.basicInfo.displayInSchool ? '显示' : '不显示' }}</template>
          </el-table-column>
          <el-table-column label="状态" min-width="100">
            <template v-slot="{ row }">
              <div v-if="row.enable">
                <el-badge is-dot type="success" class="badge-status">启用</el-badge>
              </div>
              <div v-else>
                <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template v-slot="{ row }">
              <el-button size="mini" type="text" @click="selectChange(row.topicID)"> 取消选择 </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="demo-drawer__footer" style="margin-top: 20px; text-align: center">
          <el-button @click="closeChoose">取 消</el-button>
          <el-button type="primary" @click="confirmChoose">确 定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import { Page, UiPage } from '@hbfe/common'
  import ThematicManagementList from '@api/service/management/thematic-management/ThematicManagementList'
  import RoleInfoResponseVo from '@api/service/management/authority/role/query/vo/RoleInfoResponseVo'
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
  import { cloneDeep } from 'lodash'
  class ThematicItem extends ThematicManagementItem {
    isChecked = false
    declare topicID: string // 使用 declare 关键字声明继承的属性
  }
  @Component
  export default class extends Vue {
    loading = false
    page: UiPage
    // 新增独立存储已选数据对象
    selectedItems: { [key: string]: ThematicItem } = {}
    thematicManagementList = new ThematicManagementList()
    topicsList: Array<ThematicItem> = [] //列表数据
    // 计算属性自动生成已选列表
    get topicsSelectedList() {
      return Object.values(this.selectedItems)
    }

    /**
     * 是否展示
     */
    @Prop({
      required: true,
      type: Boolean,
      default: false
    })
    value: boolean
    /**
     *已选择专题
     */
    @Prop({
      type: Array
    })
    monographicList: Array<ThematicManagementItem>
    @Watch('value', {
      immediate: true,
      deep: true
    })
    valueChange(val: boolean) {
      if (this.loading === false) {
        this.showMonographicDialog = cloneDeep(val)
      }
    }
    @Watch('showMonographicDialog', { immediate: true })
    async handleDialogVisibilityChange(val: boolean) {
      // 同步到父组件
      this.$emit('input', val)
      if (val) {
        // 仅在弹窗打开时执行初始化
        await this.initSelectionState()
        await this.searchList()
      } else {
        // 弹窗关闭时执行清理（可选）
        this.cleanupSelectionCache()
      }
    }
    private async initSelectionState() {
      // 保留原始选中数据引用
      this.page.pageNo = 1
      const initialSelected = new Map((this.monographicList || []).map((item) => [item.topicID, item]))

      // 重建选中状态存储
      this.selectedItems = {}
      initialSelected.forEach((item, id) => {
        this.$set(this.selectedItems, id, { ...item, isChecked: true })
      })
    }
    private cleanupSelectionCache() {
      this.selectedItems = {}
    }
    private syncCurrentPageSelection() {
      this.topicsList.forEach((item) => {
        this.$set(item, 'isChecked', !!this.selectedItems[item.topicID])
      })

      this.$nextTick(() => {
        ;(this.$refs['topicsListRef'] as any)?.doLayout()
      })
    }
    showMonographicDialog = false
    constructor() {
      super()
      this.page = new UiPage(this.searchList, this.searchList)
    }

    //查询列表

    async searchList() {
      this.loading = true
      try {
        await this.thematicManagementList.queryList(this.page)
        // 合并初始已选数据
        if (this.monographicList && !Object.keys(this.selectedItems).length) {
          this.monographicList.forEach((item) => {
            this.$set(this.selectedItems, item.topicID, item)
          })
        }

        // 处理当前页数据
        this.topicsList = (this.thematicManagementList.list || []).map((el) => {
          const item = new ThematicItem()
          Object.assign(item, el)
          item.isChecked = !!this.selectedItems[el.topicID]
          return item
        })
      } catch (error) {
        this.$message.error('系统错误')
      }
      this.loading = false
      this.syncCurrentPageSelection()
    }

    closeChoose() {
      this.showMonographicDialog = false
    }
    // 确认方法
    confirmChoose() {
      this.showMonographicDialog = false
      // 返回所有已选数据
      this.$emit('confirmDialog', this.topicsSelectedList)
    }

    // 选择方法
    selectChange(topicID: string) {
      // 查找当前页对应项
      const currentPageItem = this.topicsList.find((item) => item.topicID === topicID)
      if (currentPageItem) {
        // 同步更新当前页状态
        currentPageItem.isChecked = !currentPageItem.isChecked
      }
      // 更新全局存储
      if (this.selectedItems[topicID]) {
        // 删除选中状态
        this.$delete(this.selectedItems, topicID)
        // 如果当前页存在该项，保持状态同步
        if (currentPageItem) {
          currentPageItem.isChecked = false
        }
      } else {
        // 添加选中状态
        const target =
          currentPageItem ||
          this.topicsSelectedList.find((item) => item.topicID === topicID) ||
          this.monographicList?.find((item) => item.topicID === topicID)
        if (target) {
          this.$set(this.selectedItems, topicID, target)
          if (currentPageItem) {
            currentPageItem.isChecked = true
          }
        }
      }
      // 强制更新当前页表格（解决ElTable缓存问题）

      this.$nextTick(() => {
        ;(this.$refs['topicsListRef'] as any)?.doLayout()
        ;(this.$refs['topicsListSelectedRef'] as any)?.doLayout()
      })
    }
  }
</script>

<style scoped></style>
