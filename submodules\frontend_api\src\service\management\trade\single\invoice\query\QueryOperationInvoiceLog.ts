import TradeQueryBackstage, {
  OnlineInvoiceOperationResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import OperationLogItem from '@api/service/management/trade/single/invoice/mutation/vo/OperationLogItem'

export default class QueryOperationInvoiceLog {
  /**
   * 查询指定发票操作记录
   * @param onlineInvoiceId 发票id
   */
  async queryOperationLog(onlineInvoiceId: string): Promise<Array<OperationLogItem>> {
    const res = await TradeQueryBackstage.listOnlineInvoiceOperationRecord(onlineInvoiceId)

    const operationList = (res?.data?.length && res.data.map(OperationLogItem.from)) || []

    return operationList
  }
}
