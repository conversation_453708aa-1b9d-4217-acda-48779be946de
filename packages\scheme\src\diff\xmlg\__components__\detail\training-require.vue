<template>
  <FjzjTrainingRequire
    v-bind="$attrs"
    v-on="$listeners"
    ref="XmlgTrainingRequireRef"
    @schemeDetailChange="schemeDetailChange"
  >
    <template #hide-printTemplate="{ getLearningResultInfo, closePrintTemplate, hasLearningResult, templateName }">
      <el-form-item label="培训成果：" v-if="schemeType !== 3">{{ getLearningResultInfo() }}</el-form-item>
      <!--选择提供证明后显示-->
      <el-form-item label="培训证明模板：" v-show="hasLearningResult && defaultSchemeType !== '3'">
        <p>{{ templateName }}</p>
        <el-checkbox class="f-mt5" :checked="closePrintTemplate" disabled>
          不开放学员和集体报名人员打印，仅管理员可打印
        </el-checkbox>
      </el-form-item>
    </template>
  </FjzjTrainingRequire>
</template>

<script lang="ts">
  import { Component, PropSync, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import TrainClassDetailClassVo from '@api/service/diff/management/xmlg/train-class/model/TrainClassDetailClassVo'
  import TrainingRequire from '@hbfe/jxjy-admin-scheme/src/components/detail/training-require.vue'
  @Component
  class XmlgTrainingRequire extends TrainingRequire {
    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: TrainClassDetailClassVo }) schemeDetail: TrainClassDetailClassVo

    @Watch('schemeDetail', { deep: true })
    schemeDetailChange(newVal: TrainClassDetailClassVo) {
      this.$emit('schemeDetailChange', newVal)
    }
  }
  @Component({
    components: {
      XmlgTrainingRequire
    }
  })
  export default class extends Vue {
    @Ref('XmlgTrainingRequireRef') XmlgTrainingRequireRef: XmlgTrainingRequire
    schemeType: number = null
    schemeDetailChange(item: TrainClassDetailClassVo) {
      this.schemeType = item.trainClassBaseInfo.schemeType
    }
  }
</script>
