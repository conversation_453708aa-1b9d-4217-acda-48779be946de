<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 17:00:41
 * @LastEditors: chenweinian
 * @LastEditTime: 2023-07-24 19:46:12
 * @Description:
-->
<template>
  <el-drawer
    title="当前监管规则下的培训方案"
    :visible.sync="openList"
    direction="rtl"
    size="700px"
    custom-class="m-drawer"
  >
    <div class="drawer-bd">
      <!--表格-->
      <el-table stripe :data="allSchemeList" class="m-table">
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="培训方案名称" min-width="200">
          <template slot-scope="scope">{{ scope.row.schemeName }}</template>
        </el-table-column>
        <el-table-column label="方案属性" width="300" align="center" fixed="right">
          <template slot-scope="scope">
            <p v-if="getSkuPropertyName(scope.row, 'industry')">
              行业：{{ getSkuPropertyName(scope.row, 'industry') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'region')">地区：{{ getSkuPropertyName(scope.row, 'region') }}</p>
            <p v-if="getSkuPropertyName(scope.row, 'technicalGrade')">
              技术等级：{{ getSkuPropertyName(scope.row, 'technicalGrade') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'subjectType')">
              科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
            </p>
            <p v-if="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
              培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'trainingMajor')">
              培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'year')">培训年度：{{ getSkuPropertyName(scope.row, 'year') }}</p>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </div>
    <div class="m-btn-bar drawer-ft">
      <el-button type="primary" @click="openList = false">确定</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, PropSync, Vue } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import AntiSchemeItem from '@api/service/management/train-class/query/vo/AntiSchemeItem'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import AntiSchemeParams from '@api/service/management/train-class/query/vo/AntiSchemeParams'
  @Component({})
  export default class extends Vue {
    // 是否打开培训方案列表抽屉
    @PropSync('openTrainingList', {
      type: Boolean,
      default: false
    })
    openList: boolean

    // 方案id列表
    schemeIds: Array<string>

    // 查询参数
    queryParams = new AntiSchemeParams()
    // 分页
    page: UiPage
    constructor() {
      super()
      this.page = new UiPage(this.querySchemeList, this.querySchemeList)
    }

    // 方案列表
    allSchemeList = new Array<AntiSchemeItem>()
    // 查询方案列表方法
    queryTrainClassCommodityList = new QueryTrainClassCommodityList()

    // tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    // 查询方案列表
    // async created() {
    //   await this.querySchemeList()
    // }
    async querySchemeList() {
      this.queryParams.schemeIds = this.schemeIds
      this.allSchemeList = await this.queryTrainClassCommodityList.pageAntiSchemeList(this.page, this.queryParams)
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: AntiSchemeItem, type: string): string {
      if (row.sku[type]?.skuPropertyName) {
        return row.sku[type].skuPropertyName

        // const valuesArr = value.split('/'),
        //   lastIndex = valuesArr.length - 1
        // return type === 'trainingMajor' ? valuesArr[lastIndex] : value
      }
      return ''
    }
  }
</script>
