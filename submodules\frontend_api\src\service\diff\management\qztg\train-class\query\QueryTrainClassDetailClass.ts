import MsSchemeQueryFrontGatewayCourseLearningBackstage, {
  SchemeConfigResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import MsTradeQueryFrontGatewayCourseLearningBackstage, {
  CommoditySkuBackstageResponse,
  SchemeResourceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import QztgTradeQuery, {
  QZTGCommoditySkuBackstageResponse,
  SchemeResourceResponse as QztgSchemeResourceResponse
} from '@api/diff-gateway/qztg-trade-query-front-gateway-backstage'
import examQueryFactory from '@api/service/management/resource/exam-paper/QueryExamPaperFactory'
import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
import {
  ComplexSkuPropertyResponse,
  SkuPropertyConvertUtils
} from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import TrainClassConfigJsonManager from '@api/service/management/train-class/Utils/TrainClassConfigJsonManager'
import MutationCreateTrainClassCommodity from '@api/service/diff/management/qztg/train-class/mutation/MutationCreateTrainClassCommodity'
import Classification from '@api/service/management/train-class/mutation/vo/Classification'
import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
import { Page, ResponseStatus } from '@hbfe/common'
import { cloneDeep } from 'lodash'

import Mockjs from 'mockjs'
import ExperienceItem from '@api/service/management/train-class/mutation/vo/ExperienceItem'
import { LearningExperienceEnum } from '@api/service/management/train-class/mutation/Enum/LearningExperienceEnum'
import { OperationEnum } from '@api/service/management/train-class/mutation/Enum/OperationEnum'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import IntelligenceLearningModule from '@api/service/management/intelligence-learning/IntelligenceLearningModule'
import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
import QueryTrainClassCommodityList from '@api/service/diff/management/qztg/train-class/query/QueryTrainClassCommodityList'
import QztgDiffCommoditySkuRequest from '@api/service/diff/management/qztg/train-class/query/vo/QztgDiffCommoditySkuRequest'

/**
 * 运营域获取培训班商品详情
 */
class QueryTrainClassDetailClass {
  // region properties
  /**
   *配置信息jsonString，类型为string
   */
  jsonString = ''
  /**
   *培训班详情对象
   */
  trainClassDetail = new TrainClassDetailClassVo()
  /**
   *培训班商品id，类型为string
   */
  commodityId = ''

  /**
   * 合并的商品列表
   */
  mergeCommodityList: Array<TrainClassCommodityVo> = new Array<TrainClassCommodityVo>()

  /**
   * 被合并的商品列表
   */
  associatedCommodityList: Array<TrainClassCommodityVo> = new Array<TrainClassCommodityVo>()

  // endregion
  // region methods

  private classificationMap = new Map<string, string>()
  /**
   * 获取培训班详情 由详情和配置信息组合而成
   */
  async queryTrainClassDetail(): Promise<ResponseStatus> {
    //获取班级详情
    let status = await this.requestClassDetail()
    if (status.isSuccess()) {
      //获取班级模板配置信息
      status = await this.requestClassConfig()
    }
    return status
  }
  /**
   * 获取培训班复制对象
   */
  async getCopyClass(): Promise<MutationCreateTrainClassCommodity> {
    let mutationCreateTrainClassCommodity = await this.getUpdateClass()
    mutationCreateTrainClassCommodity = this.clearAllIds(mutationCreateTrainClassCommodity)
    return mutationCreateTrainClassCommodity
  }
  //清除所有的id，复制时候所有资源都得是新的
  private clearAllIds(
    mutationCreateTrainClassCommodity: MutationCreateTrainClassCommodity
  ): MutationCreateTrainClassCommodity {
    mutationCreateTrainClassCommodity.commoditySkuId = ''
    mutationCreateTrainClassCommodity.categoryId = ''
    mutationCreateTrainClassCommodity.trainClassBaseInfo.id = ''
    mutationCreateTrainClassCommodity.trainClassBaseInfo.assessSettingId = ''
    mutationCreateTrainClassCommodity.trainClassBaseInfo.assessSettingName = ''
    mutationCreateTrainClassCommodity.trainClassBaseInfo.creditId = ''
    mutationCreateTrainClassCommodity.trainClassBaseInfo.learningResultAchievementsId = ''

    mutationCreateTrainClassCommodity.learningTypeModel.courseLearning.learningTypeId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.courseLearning.assessId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.courseLearning.configId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.courseLearning.assessName = ''
    mutationCreateTrainClassCommodity.learningTypeModel.courseLearning.quizConfigModel.id = ''
    mutationCreateTrainClassCommodity.learningTypeModel.courseLearning.quizConfigModel.configId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.courseLearning.quizConfigModel.configName = ''
    mutationCreateTrainClassCommodity.learningTypeModel.courseLearning.chooseCourseRule.id = ''
    mutationCreateTrainClassCommodity.learningTypeModel.courseLearning.chooseCourseRule.name = ''
    // this.clearClassFi(mutationCreateTrainClassCommodity.learningTypeModel.courseLearning.classification) 需要根据分类ID做选修课要求学时填充
    this.setClassFiMap(mutationCreateTrainClassCommodity.learningTypeModel.courseLearning.classification)
    mutationCreateTrainClassCommodity.learningTypeModel?.courseLearning?.chooseCourseRule?.secondElectiveMaxPeriod?.forEach(
      (item) => {
        item.id = this.classificationMap.get(item.id)
      }
    )
    mutationCreateTrainClassCommodity.learningTypeModel.exam.preconditionId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.exam.preconditionName = ''
    mutationCreateTrainClassCommodity.learningTypeModel.exam.configId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.exam.assessId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.exam.assessName = ''
    mutationCreateTrainClassCommodity.learningTypeModel.exam.learningTypeId = ''

    mutationCreateTrainClassCommodity.learningTypeModel.practiceLearning.learningTypeId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.practiceLearning.configId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.practiceLearning.assessName = ''
    mutationCreateTrainClassCommodity.learningTypeModel.practiceLearning.assessId = ''

    mutationCreateTrainClassCommodity.learningTypeModel.interestCourse.learningTypeId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.interestCourse.configId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.interestCourse.assessName = ''
    mutationCreateTrainClassCommodity.learningTypeModel.interestCourse.assessId = ''

    this.clearClassFi(mutationCreateTrainClassCommodity.learningTypeModel.interestCourse.classification)

    mutationCreateTrainClassCommodity.learningTypeModel.learningExperience.learningTypeId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.learningExperience.configId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.learningExperience.assessId = ''
    mutationCreateTrainClassCommodity.learningTypeModel.learningExperience.assessName = ''
    mutationCreateTrainClassCommodity.learningTypeModel.learningExperience.experienceList.forEach((item) => {
      item.id = `${ExperienceItem.experienceIdPre}${Mockjs.Random.guid()}`
      if (item.experienceType.equal(LearningExperienceEnum.course_experience)) {
        item.courseList[0].outlineId = this.classificationMap.get(item.courseList[0].outlineId)
      }
    })
    return mutationCreateTrainClassCommodity
  }
  // 递归遍历大纲，将其所有id设置为空
  private clearClassFi(classFi: Classification) {
    classFi.id = ''
    if (classFi.assessSetting) {
      classFi.assessSetting.id = ''
    }
    if (classFi.childOutlines.length) {
      classFi.childOutlines.forEach((subClassFi) => {
        this.clearClassFi(subClassFi)
      })
    }
  }
  // 递归遍历大纲，设置Map
  private setClassFiMap(classFi: Classification) {
    const id = Classification.classificationIdPre + '_' + Mockjs.Random.guid()
    this.classificationMap.set(classFi.id, id)
    classFi.id = id
    if (classFi.assessSetting) {
      classFi.assessSetting.id = ''
    }
    if (classFi.childOutlines.length) {
      classFi.childOutlines.forEach((subClassFi) => {
        this.setClassFiMap(subClassFi)
      })
    }
  }
  /**
   * 获取培训班更新对象
   */
  async getUpdateClass(): Promise<MutationCreateTrainClassCommodity> {
    const trainClassDetail = this.trainClassDetail
    const mutationCreateTrainClassCommodity: MutationCreateTrainClassCommodity =
      (await TrainClassManagerModule.mutationTrainClassFactory.getMutationCreateTrainClassCommodity()) as unknown as MutationCreateTrainClassCommodity
    mutationCreateTrainClassCommodity.trainClassBaseInfo = trainClassDetail.trainClassBaseInfo
    mutationCreateTrainClassCommodity.learningTypeModel = trainClassDetail.learningTypeModel
    mutationCreateTrainClassCommodity.learningTypeModelCopy = trainClassDetail.learningTypeModelCopy
    mutationCreateTrainClassCommodity.taxCode = trainClassDetail.taxCode
    mutationCreateTrainClassCommodity.commoditySkuId = trainClassDetail.commoditySkuId
    mutationCreateTrainClassCommodity.categoryId = trainClassDetail.categoryId
    mutationCreateTrainClassCommodity.visibleChannelList = trainClassDetail.visibleChannelList
    mutationCreateTrainClassCommodity.saleTitle = trainClassDetail.saleTitle
    mutationCreateTrainClassCommodity.trainClassBaseInfo.name = trainClassDetail.saleTitle
    mutationCreateTrainClassCommodity.price = trainClassDetail.price
    mutationCreateTrainClassCommodity.closeCustomerPurchase = trainClassDetail.closeCustomerPurchase
    mutationCreateTrainClassCommodity.onShelvesPlanTime = trainClassDetail.onShelvesPlanTime
    mutationCreateTrainClassCommodity.offShelvesPlanTime = trainClassDetail.offShelvesPlanTime
    mutationCreateTrainClassCommodity.onShelves = trainClassDetail.onShelves
    mutationCreateTrainClassCommodity.certificate = trainClassDetail.certificate
    mutationCreateTrainClassCommodity.timeConfig = {
      onShelvesPlanTime: trainClassDetail.onShelvesPlanTime,
      offShelvesPlanTime: trainClassDetail.offShelvesPlanTime,
      registerBeginDate: trainClassDetail.trainClassBaseInfo.registerBeginDate,
      registerEndDate: trainClassDetail.trainClassBaseInfo.registerEndDate
    }
    mutationCreateTrainClassCommodity.mergeCommodityList = this.mergeCommodityList
    mutationCreateTrainClassCommodity.originalAssociatedCommodityList = cloneDeep(this.associatedCommodityList)
    mutationCreateTrainClassCommodity.associatedCommodityList = this.associatedCommodityList

    return mutationCreateTrainClassCommodity
  }
  //获取班级模板配置信息
  private async requestClassConfig() {
    //获取培训班配置模板jsonString
    const res = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInServicer({
      schemeId: this.trainClassDetail.trainClassBaseInfo.id
    })
    if (res.status.isSuccess()) {
      const trainClassDetail = this.trainClassDetail
      const commodityDetail: SchemeConfigResponse = res.data
      this.jsonString = commodityDetail.schemeConfig
      //   console.log('获取到的配置jsonstring=', commodityDetail.schemeConfig)
      const classConfigJson: any = TrainClassConfigJsonManager.calculatorSchemeConfigJson(commodityDetail.schemeConfig)
      trainClassDetail.trainClassBaseInfo.provideRelearn = classConfigJson.provideRelearn
      trainClassDetail.trainClassBaseInfo.registerBeginDate = classConfigJson.registerBeginDate
      trainClassDetail.trainClassBaseInfo.registerEndDate = classConfigJson.registerEndDate
      trainClassDetail.trainClassBaseInfo.trainingBeginDate = classConfigJson.trainingBeginDate
      trainClassDetail.trainClassBaseInfo.trainingEndDate = classConfigJson.trainingEndDate
      trainClassDetail.trainClassBaseInfo.notice = classConfigJson.notice
      trainClassDetail.trainClassBaseInfo.introId = classConfigJson.introId
      trainClassDetail.trainClassBaseInfo.introContent = commodityDetail.intro
      const getExtendProperties = (key: string) =>
        classConfigJson.extendProperties.find((item: any) => item.name == key)?.value
      trainClassDetail.trainClassBaseInfo.needDataSync = getExtendProperties('needDataSync')
      trainClassDetail.trainClassBaseInfo.showNoticeDialog = getExtendProperties('showNoticeDialog')
      //获取培训成果中学分
      const creditLearningResult = classConfigJson.assessSetting.learningResults.find(
        (learnResult: any) => learnResult.type == 1
      )
      this.trainClassDetail.trainClassBaseInfo.period = creditLearningResult.grade || 0
      this.trainClassDetail.trainClassBaseInfo.assessSettingId = classConfigJson.assessSetting.id
      this.trainClassDetail.trainClassBaseInfo.assessSettingName = classConfigJson.assessSetting.name
      this.trainClassDetail.trainClassBaseInfo.creditId = creditLearningResult.id
      this.trainClassDetail.taxCode = classConfigJson.commoditySale.taxCode
      this.trainClassDetail.categoryId = classConfigJson.commoditySale.categoryId
      this.trainClassDetail.onShelves = classConfigJson.commoditySale.onOrOffShelvesPlan.onShelve
      if (!this.trainClassDetail.onShelves) {
        this.trainClassDetail.onShelvesPlanTime = classConfigJson.commoditySale.onOrOffShelvesPlan.onShelvePlanTime
      }
      this.trainClassDetail.offShelvesPlanTime = classConfigJson.commoditySale.onOrOffShelvesPlan.offShelvePlanTime
      this.trainClassDetail.visibleChannelList = classConfigJson.commoditySale.visibleChannelList
      this.trainClassDetail.closeCustomerPurchase = classConfigJson.commoditySale.closeCustomerPurchase
      //配置培训模板
      const templateResult = classConfigJson.assessSetting.learningResults.find(
        (learnResult: any) => learnResult.type == 2
      )
      this.trainClassDetail.certificate = templateResult
      if (templateResult) {
        this.trainClassDetail.trainClassBaseInfo.hasLearningResult = true
        this.trainClassDetail.trainClassBaseInfo.learningResultId = templateResult.certificateTemplateId
        this.trainClassDetail.trainClassBaseInfo.learningResultAchievementsId = templateResult.id

        this.trainClassDetail.trainClassBaseInfo.openPrintTemplate = templateResult.openPrintTemplate
      }
      //classConfigJson.type == 'autonomousCourseLearning'
      this.trainClassDetail.trainClassBaseInfo.schemeType = SchemeTypeEnum[classConfigJson.type as string] ?? 1
      this.trainClassDetail.learningTypeModel = TrainClassConfigJsonManager.jsonConfigConvertToLearningType(
        this.jsonString,
        this.trainClassDetail.trainClassBaseInfo.schemeType
      )
      //   console.log('this.trainClassDetail.learningTypeModel', this.trainClassDetail.learningTypeModel)
      if (this.trainClassDetail.learningTypeModel.exam.isSelected) {
        try {
          const resData = await examQueryFactory
            .queryExamPaperDetail(this.trainClassDetail.learningTypeModel.exam.paperPublishConfigureId)
            .queryAutomaticExamPaperDetail()
          if (resData.status.isSuccess()) {
            this.trainClassDetail.learningTypeModel.exam.paperPublishConfigureName = resData.data.name
          }
        } catch (e) {
          console.log('获取试卷详情失败', e)
        }
      }
    }
    this.trainClassDetail.learningTypeModelCopy = cloneDeep(this.trainClassDetail.learningTypeModel)
    this.trainClassDetail.learningTypeModelCopy.learningExperience.experienceList.forEach((item) => {
      item.operation = OperationEnum.REMOVE
    })
    // 智能学习
    try {
      this.trainClassDetail.trainClassBaseInfo.isIntelligenceLearning =
        (await new IntelligenceLearningModule().doCheck(this.trainClassDetail.trainClassBaseInfo.id)) || false
    } catch (e) {
      console.log('方案详情智能学习状态获取失败', e)
    }

    return res.status
  }

  //获取班级详情
  private async requestClassDetail() {
    const res = await QztgTradeQuery.getCommoditySkuInServicer(this.commodityId)
    if (res.status.isSuccess()) {
      const commodityDetail: QZTGCommoditySkuBackstageResponse = res.data
      await this.convertToTrainClassDetailClassVo(commodityDetail)

      this.mergeCommodityList = new Array<TrainClassCommodityVo>()
      this.associatedCommodityList = new Array<TrainClassCommodityVo>()
      const mergeCommodityIds = new Array<string>()
      const associatedCommodityIds = new Array<string>()

      res.data?.mergedCommodities?.length &&
        res.data.mergedCommodities.map((item) => {
          if (item.commoditySkuId) {
            mergeCommodityIds.push(item.commoditySkuId)
          }
        })

      res.data?.mergedFromCommodities?.length &&
        res.data.mergedFromCommodities.map((item) => {
          if (item.commoditySkuId) {
            associatedCommodityIds.push(item.commoditySkuId)
          }
        })

      const uniqueCommodityIds = [...new Set([...mergeCommodityIds, ...associatedCommodityIds])]

      if (uniqueCommodityIds?.length) {
        const queryCommodity = new QueryTrainClassCommodityList()
        const page = new Page()
        page.pageNo = 1
        page.pageSize = uniqueCommodityIds.length
        const queryCommodityParam = new QztgDiffCommoditySkuRequest()
        queryCommodityParam.commoditySkuIdList = uniqueCommodityIds
        const commodityRes = await queryCommodity.queryTrainClassCommodityList(page, queryCommodityParam)

        mergeCommodityIds.map((item) => {
          const findCommodity = commodityRes.find((it) => it.commoditySkuId == item)
          if (findCommodity) {
            this.mergeCommodityList.push(findCommodity)
          }
        })

        associatedCommodityIds.map((item) => {
          const findCommodity = commodityRes.find((it) => it.commoditySkuId == item)
          if (findCommodity) {
            this.associatedCommodityList.push(findCommodity)
          }
        })
      }
    }
    return res.status
  }
  //转换成Vo对象
  private async convertToTrainClassDetailClassVo(commodityDetail: QZTGCommoditySkuBackstageResponse) {
    try {
      const skuProperty = await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
        commodityDetail.skuProperty as ComplexSkuPropertyResponse
      )
      this.trainClassDetail.trainClassBaseInfo.skuProperty = cloneDeep(skuProperty)
    } catch (e) {
      console.log(e)
    }
    this.trainClassDetail.trainClassBaseInfo.picture = commodityDetail.commodityBasicData.commodityPicturePath
    this.trainClassDetail.price = commodityDetail.commodityBasicData.price
    this.trainClassDetail.saleTitle = commodityDetail.commodityBasicData.saleTitle
    this.trainClassDetail.trainClassBaseInfo.name = commodityDetail.commodityBasicData.saleTitle
    this.trainClassDetail.trainClassBaseInfo.id = (commodityDetail.resource as QztgSchemeResourceResponse).schemeId
    this.trainClassDetail.trainClassBaseInfo.idCopy = (commodityDetail.resource as QztgSchemeResourceResponse).schemeId
    this.trainClassDetail.commoditySkuId = commodityDetail.commoditySkuId
  }
  //获取商品详情
  async getCommodityDetail(commodityId: string) {
    if (!commodityId) {
      return new CommoditySkuBackstageResponse()
    }
    const res = await MsTradeQueryFrontGatewayCourseLearningBackstage.pageCommoditySkuInServicer({
      page: {
        pageNo: 1,
        pageSize: 1
      },
      queryRequest: {
        commoditySkuIdList: [commodityId],
        isDisabledResourceShow: true
      }
    })
    return res.data?.currentPageData?.[0] || new CommoditySkuBackstageResponse()
  }

  /**
   * 查询商品是否被引用
   * @param commodityId 商品id
   */
  static async queryCommodityIsReferenced(commodityId: string) {
    const res = await QztgTradeQuery.isReferenced(commodityId)

    return !!res?.data
  }
  // endregion
}
export default QueryTrainClassDetailClass
