import AbstractEnum from '../AbstractEnum'
enum FriendLinkTypeEnum {
  /**
   * 文本
   */
  TEXT = 0,
  /**
   * 图片
   */
  PICTURE = 1
}

export { FriendLinkTypeEnum }
class CollectiveSignUpType extends AbstractEnum<FriendLinkTypeEnum> {
  constructor() {
    super()
    this.map.set(FriendLinkTypeEnum.TEXT, '文本')
    this.map.set(FriendLinkTypeEnum.PICTURE, '图片')
  }
}
export default CollectiveSignUpType
