<template>
  <div class="pure">
    <el-drawer
      title="查看导入任务"
      :visible.sync="show"
      size="1200px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div class="drawer-bd">
        <!--表格-->
        <el-table stripe :data="importTaskResultList" max-height="500" class="m-table" v-loading="query.loading">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="任务名称" min-width="240" fixed="left">
            <template slot-scope="scope">{{ scope.row.taskName }}</template>
          </el-table-column>
          <el-table-column label="任务处理时间" min-width="170">
            <template slot-scope="scope">{{ scope.row.taskBeginTime }}</template>
          </el-table-column>
          <el-table-column label="任务结束时间" min-width="170">
            <template slot-scope="scope">{{ scope.row.taskEndTime }}</template>
          </el-table-column>
          <el-table-column label="任务执行状态" min-width="120">
            <template slot-scope="scope">
              <el-tag :type="asyncTaskExecStatusStyle(scope.row)" v-if="asyncTaskExecStatus(scope.row)">
                {{ asyncTaskExecStatus(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="任务处理结果" min-width="120">
            <template slot-scope="scope">
              <el-badge is-dot :type="taskProcessResultStyle(scope.row)" class="badge-status">
                {{ taskProcessResult(scope.row) }}
              </el-badge>
            </template>
          </el-table-column>
          <el-table-column label="处理总条数/成功条数/失败条数" width="240" align="center">
            <template slot-scope="scope">
              {{ scope.row.totalAmount }} / {{ scope.row.successAmount }} / {{ scope.row.failAmount }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="140" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="viewLog(scope.row)">查看日志</el-button>
              <el-button type="text" size="mini" @click="downloadFailExcel(scope.row)">下载失败数据</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </div>
    </el-drawer>
    <el-dialog
      title="提示"
      :visible.sync="uiConfig.dialog.viewLogVisible"
      width="450px"
      class="m-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div>{{ logMessage }}</div>
      <div slot="footer">
        <el-button type="primary" @click="uiConfig.dialog.viewLogVisible = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, PropSync, Vue } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import ImportTaskResultListDetailVo from '@api/service/management/trade/batch/order/query/vo/ImportTaskResultListDetailVo'
  import QueryBatchOrderDetail from '@api/service/management/trade/batch/order/query/QueryBatchOrderDetail'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import AsyncTaskExecStatusList, {
    AsyncTaskExecStatusEnum
  } from '@api/service/common/enums/async-task/AsyncTaskExecStatusList'
  import AsyncTaskProcessResultList, {
    AsyncTaskProcessResultListEnum
  } from '@api/service/common/enums/async-task/AsyncTaskProcessResultList'

  @Component
  export default class extends Vue {
    /**
     * 是否展示
     */
    @PropSync('visible', {
      type: Boolean
    })
    show: boolean

    // 分页
    page: UiPage
    // 查询
    query: Query = new Query()
    // 导入任务结果列表
    importTaskResultList: ImportTaskResultListDetailVo[] = []

    /**
     * 异步任务执行状态列表
     */
    asyncTaskExecStatusList: EnumOption<AsyncTaskExecStatusEnum>[] = AsyncTaskExecStatusList.list()

    /**
     * 异步任务执行状态Map
     */
    asyncTaskExecStatusMap: Map<AsyncTaskExecStatusEnum, string> = new Map<AsyncTaskExecStatusEnum, string>()
      .set(AsyncTaskExecStatusEnum.In_Exec, 'warning')
      .set(AsyncTaskExecStatusEnum.Has_Execed, 'success')
      .set(AsyncTaskExecStatusEnum.Fail_Exced, 'danger')

    /**
     * 异步任务处理结果列表
     */
    asyncTaskProcessResultList: EnumOption<AsyncTaskProcessResultListEnum>[] = AsyncTaskProcessResultList.list()

    /**
     * 异步任务处理结果Map
     */
    asyncTaskProcessResultMap: Map<AsyncTaskProcessResultListEnum, string> = new Map<
      AsyncTaskProcessResultListEnum,
      string
    >()
      .set(AsyncTaskProcessResultListEnum.Unknown, 'warning')
      .set(AsyncTaskProcessResultListEnum.Success, 'success')
      .set(AsyncTaskProcessResultListEnum.Fail, 'danger')

    /**
     * 远端查询入口
     */
    queryRemote: QueryBatchOrderDetail =
      TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchOrderDetail

    /**
     * ui控制组
     */
    uiConfig = {
      dialog: {
        // 查看日志
        viewLogVisible: false
      }
    }

    /**
     * 日志信息
     */
    logMessage = ''

    /**
     * 获取异步状态
     */
    get asyncTaskExecStatus() {
      return (item: ImportTaskResultListDetailVo) => {
        return this.asyncTaskExecStatusList.find((el) => el.code === item.taskExecStatus)?.desc || null
      }
    }

    /**
     * 获取异步状态样式
     */
    get asyncTaskExecStatusStyle() {
      return (item: ImportTaskResultListDetailVo) => {
        return this.asyncTaskExecStatusMap.get(item.taskExecStatus) || null
      }
    }

    /**
     * 获取异步任务处理结果
     */
    get taskProcessResult() {
      return (item: ImportTaskResultListDetailVo) => {
        return this.asyncTaskProcessResultList.find((el) => el.code === item.taskProcessResult)?.desc || null
      }
    }

    /**
     * 获取异步任务处理结果样式
     */
    get taskProcessResultStyle() {
      return (item: ImportTaskResultListDetailVo) => {
        return this.asyncTaskProcessResultMap.get(item.taskProcessResult) || null
      }
    }

    constructor() {
      super()
      this.page = new UiPage(this.pageImportTaskResult, this.pageImportTaskResult)
    }

    /**
     * 批次单id
     */
    batchOrderNo = ''

    /**
     * 查询导入任务结果列表
     */
    async searchBase() {
      this.page.pageNo = 1
      await this.pageImportTaskResult()
    }

    /**
     * 查询导入任务结果列表
     */
    async pageImportTaskResult() {
      this.query.loading = true
      try {
        this.importTaskResultList = [] as ImportTaskResultListDetailVo[]
        this.importTaskResultList = await this.queryRemote.queryImportTaskResultList(this.batchOrderNo, this.page)
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      } finally {
        this.query.loading = false
      }
    }

    /**
     * 查看日志
     */
    viewLog(row: ImportTaskResultListDetailVo) {
      this.uiConfig.dialog.viewLogVisible = true
      this.logMessage = row.logMessage
    }

    /**
     * 下载失败文件
     */
    downloadFailExcel(row: ImportTaskResultListDetailVo) {
      return
      // window.open(row.failExcelDownloadUrl, '_blank')
    }
  }
</script>

<style lang="scss" scoped>
  .pure {
    margin: 0;
    padding: 0;
  }
</style>
