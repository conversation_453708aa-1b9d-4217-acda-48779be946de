import { ResponseStatus } from '@hbfe/common'

/**
 * 引用某个课程包的培训班列表Vo
 */
class TrainClassCommoditySynchronizationResultVo {
  // region properties

  // endregion
  // region methods

  /**
   * 获取培训班课程包同步对象
   */
  async getCoursePackageSynchronization(): Promise<ResponseStatus> {
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  // endregion
}
export default TrainClassCommoditySynchronizationResultVo
