<route-meta>
{
"isMenu": true,
"title": "批量打印证明",
"sort": 10,
"icon": "icon-piliangdayin"
}
</route-meta>
<template>
  <el-main
    v-if="$hasPermission('batchPrintCertify,queryFx,queryZt')"
    query
    desc="batchPrintCertify:查询,queryFx:查询（分销）,queryZt:查询(专题)"
    actions="batchPrintCertify:doQueryPage,batchPrint,requestPreview,download,queryLearningArchivesList
    #queryFx:doQueryPageFx,batchPrint,requestPreview,download,queryLearningArchivesListInDistributor
    #queryZt:doQueryPageZt,batchPrint,requestPreview,download,queryLearningTrainingChannelList"
  >
    <!-- 顶部的tab标签 -->
    <el-tabs v-model="tabName" class="m-tab-top is-sticky">
      <!-- 按方案打印 -->
      <el-tab-pane label="按方案打印" name="scheme"> </el-tab-pane>
      <!-- 按学员打印 -->
      <el-tab-pane label="按学员打印" name="student"> </el-tab-pane>

      <div class="f-p15">
        <el-card shadow="never" class="m-card f-mb15">
          <!-- 筛选条件 -->
          <hb-search-wrapper :model="certifyParam" @reset="reset" id="search-item">
            <el-form-item label="姓名">
              <el-input v-model="certifyParam.name" clearable placeholder="请输入姓名" />
            </el-form-item>
            <el-form-item label="证件号">
              <el-input v-model="certifyParam.idCard" clearable placeholder="请输入证件号" />
            </el-form-item>
            <el-form-item label="培训方案" v-if="isZtlogin">
              <template
                v-if="$hasPermission('schemeQueryZt')"
                query
                desc="方案回调请求"
                actions="@BizLearningSchemeZtSelect"
              >
                <biz-learning-scheme-zt-select
                  v-model="commoditySkuIdList"
                  :isZtlogin="isZtlogin"
                  @getCertificateTemplateId="getCertificateTemplateId"
                ></biz-learning-scheme-zt-select>
              </template>
            </el-form-item>
            <el-form-item label="培训方案" v-else-if="isFxlogin">
              <template
                v-if="$hasPermission('schemeQueryFx')"
                query
                desc="方案回调请求"
                actions="@BizLearningSchemeSelectFx"
              >
                <BizLearningSchemeSelectFx v-model="commoditySkuIdList"></BizLearningSchemeSelectFx>
              </template>
            </el-form-item>
            <el-form-item label="培训方案" v-else>
              <template
                v-if="$hasPermission('schemeQuery')"
                query
                desc="方案回调请求"
                actions="@BizLearningSchemeSelect"
              >
                <biz-learning-scheme-select
                  v-model="commoditySkuIdList"
                  :isZtlogin="isZtlogin"
                  @getCertificateTemplateId="getCertificateTemplateId"
                ></biz-learning-scheme-select>
              </template>
            </el-form-item>
            <el-form-item label="期别名称" v-if="showIssueNameInput">
              <biz-select-issue-name
                :commodity-id="commoditySkuIdList[0].commodityId"
                v-model="certifyParam.issueId"
              ></biz-select-issue-name>
            </el-form-item>
            <el-form-item label="工作单位">
              <el-input v-model="certifyParam.unit" clearable placeholder="请输入工作单位" />
            </el-form-item>
            <el-form-item label="订单号">
              <el-input v-model="certifyParam.order" clearable placeholder="请输入订单号" />
            </el-form-item>
            <el-form-item label="打印状态">
              <el-select v-model="certifyParam.status" clearable placeholder="请选择">
                <el-option :value="false" label="未打印"></el-option>
                <el-option :value="true" label="已打印"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="考核通过时间">
              <double-date-picker
                :begin-create-time.sync="certifyParam.startTime"
                :end-create-time.sync="certifyParam.endTime"
                begin-time-placeholder="考核通过开始时间"
                end-time-placeholder="考核通过结束时间"
              ></double-date-picker>
            </el-form-item>
            <el-form-item label="销售渠道" v-if="isWxlogin">
              <el-select v-model="certifyParam.saleChannel" clearable placeholder="请选择">
                <el-option
                  v-for="option in salesChannelOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.desc"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="分销商" v-if="isWxlogin">
              <template
                v-if="$hasPermission('distributorQuery')"
                query
                desc="分销商查询"
                actions="@BizDistributorDrawer"
              >
                <biz-distributor-drawer
                  ref="distributorDrawer"
                  v-model="distributorIdList"
                  @input="handleDistributorChange"
                ></biz-distributor-drawer
              ></template>
            </el-form-item>
            <template slot="actions"> <el-button type="primary" @click="searchList">查询</el-button></template>
          </hb-search-wrapper>
          <p style="margin-bottom: 15px; margin-left: 50px">
            <template
              v-if="$hasPermission('batchPrint')"
              mutation
              desc="批量打印"
              actions="@SchemeBatchPrintDrawer,@BatchSelectTrainingPrintingDrawer,batchPrintCertifyList"
            >
              <el-button
                :loading="printBtnLoading"
                type="primary"
                @click="batchPrintCertifyList"
                style="padding: 10px 10px"
                >批量打印列表证明</el-button
              ></template
            >
            <template
              v-if="$hasPermission('importListPrinting,importListPrintingFx')"
              mutation
              desc="importListPrinting:导入名单打印,importListPrintingFx:导入名单打印(分销)"
              actions="importListPrinting:@hbfe/jxjy-admin-batchPrint/src/import-list-printing.vue#importListPrintingFx:@hbfe/jxjy-admin-batchPrint/src/import-list-printing.vue"
            >
              <el-button v-if="tabName == 'student'" @click="importListPrinting">导入名单打印</el-button>
            </template>
          </p>

          <!--表格-->
          <el-table ref="tableDataRef" stripe :data="tableData" max-height="500px" class="m-table" v-loading="loading">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="学员姓名" min-width="140">
              <template slot-scope="scope">{{ scope.row.name }}</template>
            </el-table-column>
            <el-table-column label="证件号" min-width="200">
              <template slot-scope="scope">{{ scope.row.idCard }}</template>
            </el-table-column>
            <el-table-column label="工作单位" min-width="300">
              <template slot-scope="scope">{{ scope.row.unit }}</template>
            </el-table-column>
            <el-table-column label="订单号" min-width="240">
              <template slot-scope="scope">{{ scope.row.order }}</template>
            </el-table-column>
            <el-table-column label="所属方案" min-width="240">
              <template #default="scope">
                <p>{{ scope.row.trainingName }}</p>
                <p v-if="scope.row.issueName">
                  <el-tag type="info" size="mini">培训期别</el-tag>
                  {{ scope.row.issueName }}
                </p>
              </template>
            </el-table-column>
            <el-table-column label="考核通过时间" min-width="180">
              <template slot-scope="scope">{{ scope.row.learningSuccessTime }}</template>
            </el-table-column>
            <el-table-column label="是否打印" min-width="120">
              <template slot-scope="scope">
                <div v-if="scope.row.status">
                  <el-badge is-dot type="info" class="badge-status">已打印</el-badge>
                </div>
                <div v-else>
                  <el-badge is-dot type="success" class="badge-status">未打印</el-badge>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="打印时间" min-width="180">
              <template slot-scope="scope">{{ scope.row.printTime }}</template>
            </el-table-column>
            <el-table-column label="操作" width="180" align="center" fixed="right">
              <template slot-scope="scope">
                <template v-if="$hasPermission('preview')" mutation desc="打印" actions="requestPreview">
                  <el-button type="text" size="mini" @click="requestPreview(scope.row)">打印</el-button>
                </template>
                <!--              <template v-if="$hasPermission('printRecordClick')" desc="打印记录" actions="printRecordClick">
                <el-button type="text" size="mini" @click="printRecordClick(scope.row.id)">打印记录</el-button>
              </template>-->
                <template v-if="$hasPermission('downLoad')" mutation desc="下载" actions="download">
                  <!-- 已经打印的状态 -->
                  <el-button type="text" size="mini" @click="download(scope.row)" :disabled="!scope.row.status"
                    >下载</el-button
                  >
                  <!-- 未打印的状态，按钮灰显 -->
                  <!-- <el-button type="text" size="mini" disabled v-show="!scope.row.status">下载</el-button> -->
                </template>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="page" v-bind="page" :pageSizes="pageSizes"></hb-pagination>
        </el-card>
        <!--      日志类暂不实现-->
        <!--      <el-card shadow="never" class="m-card f-mb15">
        <el-drawer title="打印记录" :visible.sync="isShowPrintRecord" size="600px" custom-class="m-drawer">
          <div class="drawer-bd f-mt20 f-mlr40">
            <el-timeline v-for="item in printRecordList" :key="item">
              <el-timeline-item>{{ item.who }} 在 {{ item.time }} 打印了该证明</el-timeline-item>
            </el-timeline>
          </div>
        </el-drawer>
      </el-card>-->
        <el-dialog :visible.sync="exportDialog" width="400px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务管理-批量打印证明</p>
            </div>
          </div>
          <div slot="footer">
            <el-button type="info" @click="exportDialog = false">暂 不</el-button>
            <el-button type="primary" @click="toDownloadPage">前往下载</el-button>
          </div>
        </el-dialog>
      </div>
    </el-tabs>

    <!-- 按方案打印时-批量打印证明抽屉 -->
    <scheme-batch-print-drawer
      ref="schemeBatchPrintDrawerRef"
      :certifyParam="certifyParam"
      :schemeIsMerge="isMerge"
    ></scheme-batch-print-drawer>

    <select-training-printing-drawer
      ref="selectTrainingPrintingDrawerRef"
      :studentNoList="studentNoList"
      :studentQualificationId="studentQualificationId"
    ></select-training-printing-drawer>

    <!-- 学员批量打印证明抽屉 -->
    <batch-select-training-printing-drawer
      :certifyParam="certifyParam"
      ref="batchSelectTrainingPrintingDrawerRef"
    ></batch-select-training-printing-drawer>

    <!-- 学员批量打印证明抽屉 -->
    <!-- <template v-if="$hasPermission('studentBatchPrint')" desc="学员批量打印证明" actions="@StudentBatchPrintProof">
      <student-batch-print-proof
        @downloadDialog="downloadDialog"
        @getLoadingResult="getLoadingResult"
        ref="studentPrintRef"
        :tableData="tableData"
        :studentNoList="studentNoList"
        :studentQualificationId="studentQualificationId"
        :printType="printType"
      ></student-batch-print-proof>
    </template> -->
    <!-- <template v-if="$hasPermission('axPrintType')" desc="安溪批量打印证明" actions="@PrintTypeComponent">
      <print-type-component ref="printType" @downloadDialog="downloadDialog" @getLoadingResult="getLoadingResult" />
    </template> -->
    <el-dialog title="提示" :visible.sync="previewDialog" width="300px" class="m-dialog" @close="cancelPreview">
      <div>是否要前往证书预览页？</div>
      <div slot="footer">
        <el-button @click="cancelPreview">取 消</el-button>
        <el-button type="primary" @click="openPreview">确 定</el-button>
      </div>
    </el-dialog>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue, Watch, Ref } from 'vue-property-decorator'
  import { Response, UiPage, Page } from '@hbfe/common'
  import LearningSchemeSelect from '@hbfe/jxjy-admin-trade/src/order/personal/components/learning-scheme-select.vue'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { debounce, bind } from 'lodash-decorators'
  import TrainingCertificateModule from '@api/service/management/personal-leaning/TrainingCertificateModule'
  import LearningArcjovesRequest from '@api/service/management/personal-leaning/query/vo/LearningArcjovesRequest'
  import LearningArcjovesResponse from '@api/service/management/personal-leaning/query/vo/LearningArcjovesResponse'
  import PrintCertificationsVo from '@api/service/management/personal-leaning/mutation/vo/PrintCertificationsVo'
  import MutationBatchPrintTraining from '@api/service/management/personal-leaning/mutation/MutationBatchPrintTraining'
  import { FileTypesEnum } from '@api/service/common/enums/personal-leaning/FileTypes'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import msTradeQuery from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { CommoditySkuRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { SchemeResourceResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
  import StudentBatchPrintProof from '@hbfe/jxjy-admin-batchPrint/src/student-batch-print-proof.vue'
  import PrintTypeComponent from '@hbfe/jxjy-admin-batchPrint/src/components/print-type-component.vue'
  import selectTrainingPrintingDrawer from '@hbfe/jxjy-admin-batchPrint/src/diff/scjzs/__components__/select-training-printing-drawer.vue'
  import BatchSelectTrainingPrintingDrawer from '@hbfe/jxjy-admin-batchPrint/src/diff/scjzs/__components__/batch-select-training-printing-drawer.vue'
  import SchemeBatchPrintDrawer from '@hbfe/jxjy-admin-batchPrint/src/diff/scjzs/__components__/scheme-batch-print-drawer.vue'
  import QueryCertificateTemplateIsMerge from '@api/service/management/personal-leaning/query/QueryCertificateTemplateIsMerge'
  import SalesChannel, { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryTrainingArchivesList from '@api/service/management/personal-leaning/query/QueryTrainingArchivesList'
  import SchemeConfigUtils from '@api/service/management/train-class/Utils/SchemeConfigUtils'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import BizDistributorDrawer from '@hbfe/fx-manage/src/components/biz/biz-distributor-drawer.vue'
  import BizLearningSchemeSelectFx from '@hbfe/fx-manage/src/components/biz/biz-learning-scheme-select.vue'
  import BizLearningSchemeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-learning-scheme-select.vue'
  import BizLearningSchemeZtSelect from '@hbfe/jxjy-admin-components/src/biz/biz-learning-scheme-zt-select.vue'
  import QueryAdminTrainingArchivesList from '@api/service/management/personal-leaning/query/QueryTrainingArchivesList'
  import { CheckPrintConditionResponse } from '@api/platform-gateway/platform-certificate-v1'
  import BizSelectIssueName from '@hbfe/jxjy-admin-components/src/biz/biz-select-issue-name.vue'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  @Component({
    components: {
      DoubleDatePicker,
      LearningSchemeSelect,
      StudentBatchPrintProof,
      PrintTypeComponent,
      selectTrainingPrintingDrawer,
      BatchSelectTrainingPrintingDrawer,
      SchemeBatchPrintDrawer,
      BizDistributorDrawer,
      BizLearningSchemeSelect,
      BizLearningSchemeSelectFx,
      BizLearningSchemeZtSelect,
      BizSelectIssueName
    }
  })
  export default class extends Vue {
    @Ref('studentPrintRef')
    studentPrintRef: StudentBatchPrintProof

    @Ref('selectTrainingPrintingDrawerRef')
    selectTrainingPrintingDrawerRef: selectTrainingPrintingDrawer

    @Ref('batchSelectTrainingPrintingDrawerRef')
    batchSelectTrainingPrintingDrawerRef: BatchSelectTrainingPrintingDrawer

    @Ref('distributorDrawer')
    distributorDrawer: BizDistributorDrawer

    @Ref('schemeBatchPrintDrawerRef')
    schemeBatchPrintDrawerRef: SchemeBatchPrintDrawer

    @Ref('printType') printTypeRef: PrintTypeComponent

    page: UiPage

    pageSizes = [5, 10, 15, 20, 25, 75]

    constructor() {
      super()
      if (this.isFxlogin) {
        this.page = new UiPage(this.doQueryPageFx, this.doQueryPageFx)
      } else if (this.isZtlogin) {
        this.page = new UiPage(this.doQueryPageZt, this.doQueryPageZt)
      } else {
        this.page = new UiPage(this.doQueryPage, this.doQueryPage)
      }
    }

    templateUrl = ''

    previewDialog = false

    tabName = 'scheme'
    salesChannelOptions = SalesChannel.list().filter((item) => item.code !== SaleChannelEnum.huayi)
    //查询证明实例
    TrainingCertificateModule = new QueryAdminTrainingArchivesList()
    // 批量打印证明实例
    batchPrintTrainingModule = new MutationBatchPrintTraining()
    // 搜索参数
    certifyParam = new LearningArcjovesRequest()
    batchPrintParam = new PrintCertificationsVo()
    //选择打印的对象
    selectCertifyList: LearningArcjovesRequest[] = []
    // // 商品id数组
    // commoditySkuIdList = new Array<HasSelectCommodityMode>()
    // 商品id数组
    commoditySkuIdList = new Array<HasSelectSchemeMode>()
    // 分销商数组
    distributorIdList = new Array<string>()
    isShowPrintRecord = false
    schemeId = ''
    exportDialog = false
    //学员批量打印证明抽屉
    studentDialog = false
    // tableData = new Array<LearningArcjovesResponse>()
    // 打印记录列表，下个版本
    // printRecordList: any = []
    tableData: LearningArcjovesResponse[] = []
    // 培训方案入参
    // @Watch('schemeId', { immediate: true })
    // changeScheme(val: string) {
    //   console.log('********', val)
    //   if (val) {
    //     this.certifyParam.trainingId = val
    //   } else {
    //     this.certifyParam.trainingId = undefined
    //   }
    // }

    // 方案选中的模版id
    certificateTemplateId = ''
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    isWxlogin = QueryManagerDetail.hasCategory(CategoryEnums.wxgly)
    // 存放学号id的数组
    studentNoList = new Array<string>()

    // 学员的参训资格id
    studentQualificationId = ''

    // 批量打印按钮的loading
    printBtnLoading = false

    printType = ''

    // 是否合并
    isMerge = false

    // 抽屉标识
    source = 'batchPrintingStudentsSource'

    /**
     * 判断当前用户是否拥有专题管理员角色类型
     */
    get isZtglyLogin() {
      return QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    }

    // 查询模版是否能合并
    queryCertificateTemplateIsMerge = new QueryCertificateTemplateIsMerge()

    /**
     * 展示期别名称输入框
     */
    get showIssueNameInput() {
      if (!this.commoditySkuIdList.length) return false
      const selectItem = this.commoditySkuIdList[0]
      return (
        selectItem.trainingMode.skuPropertyValueId === TrainingModeEnum.mixed ||
        selectItem.trainingMode.skuPropertyValueId === TrainingModeEnum.offline
      )
    }
    // 培训方案入参
    @Watch('commoditySkuIdList', {
      deep: true
    })
    async changeScheme(val: Array<HasSelectSchemeMode>) {
      const page = new Page()
      page.pageNo = 1
      page.pageSize = 1
      const queryRequest = new CommoditySkuRequest()
      // 不是分销商走原本逻辑，分销商为新增逻辑
      if (val?.length && (val[0]?.schemeId || val[0]?.id)) {
        if (this.isFxlogin) {
          this.certifyParam.trainingId = val[0].id
        } else {
          queryRequest.commoditySkuIdList = []
          queryRequest.commoditySkuIdList.push(val[0]?.schemeId)
          const response = await msTradeQuery.pageCommoditySkuInServicer({
            page,
            queryRequest
          })
          this.certifyParam.trainingId = (
            response?.data?.currentPageData[0]?.resource as SchemeResourceResponse
          )?.schemeId
        }

        // this.queryParams.commoditySkuIdList = [] as string[]
        //this.certifyParam.trainingId = val[0].schemeId
      } else {
        this.certifyParam.trainingId = undefined
      }
    }

    loading = false

    handleDistributorChange(val: any) {
      this.certifyParam.distributorId = val[0]?.id
      //   this.distributorIdList = [val[0].id]
    }

    // 查询
    async searchList() {
      if (this.isFxlogin) {
        this.page.pageNo = 1
        await this.doQueryPageFx()
      } else if (this.isZtglyLogin) {
        this.page.pageNo = 1
        await this.doQueryPageZt()
      } else {
        this.page.pageNo = 1
        await this.doQueryPage()
      }
    }
    async doQueryPage() {
      try {
        this.loading = true
        this.certifyParam.openPrintTemplate = true
        const model = new QueryTrainingArchivesList()
        const res = await this.TrainingCertificateModule.queryLearningArchivesList(this.page, this.certifyParam)
        this.tableData = res
        ;(this.$refs['tableDataRef'] as any)?.doLayout()
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    // 分销查询
    async doQueryPageFx() {
      try {
        this.loading = true
        this.certifyParam.openPrintTemplate = true
        const model = new QueryAdminTrainingArchivesList()
        const res = await this.TrainingCertificateModule.queryLearningArchivesListInDistributor(
          this.page,
          this.certifyParam
        )
        this.tableData = res
        ;(this.$refs['tableDataRef'] as any)?.doLayout()
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    // 专题查询
    async doQueryPageZt() {
      try {
        this.loading = true
        this.certifyParam.openPrintTemplate = true
        const model = new QueryTrainingArchivesList()
        const res = await model.queryLearningTrainingChannelList(this.page, this.certifyParam)
        this.tableData = res
        ;(this.$refs['tableDataRef'] as any)?.doLayout()
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    // 重置
    async reset() {
      this.commoditySkuIdList = new Array<HasSelectSchemeMode>()
      this.certifyParam = new LearningArcjovesRequest()
      this.distributorDrawer?.clear()
      await this.searchList()
    }
    async created() {
      if (this.isFxlogin) {
        await this.doQueryPageFx()
      } else if (this.isZtglyLogin) {
        await this.doQueryPageZt()
      } else {
        await this.doQueryPage()
      }
    }

    getLoadingResult(value: any) {
      this.printBtnLoading = value
    }

    downloadDialog(value: any) {
      this.exportDialog = value
    }

    // 获取方案打印模板id
    async getCertificateTemplateId(value: string[]) {
      console.log('选中方案的模版id', value)

      this.certificateTemplateId = value ? value[0] : ''

      //   if (this.certificateTemplateId !== '') {
      //     // 通过模版id查询是否合并
      //     try {
      //       this.isMerge = (await this.queryCertificateTemplateIsMerge.findCertificateTemplate(value[0])) ?? false
      //     } catch (e) {
      //       console.log(e)
      //       this.isMerge = false
      //     }
      //   }
    }

    // 批量打印证明
    @bind
    @debounce(200)
    async batchPrintCertifyList() {
      try {
        this.printType = 'batch'
        // 判断是方案批量打印证明还是按学员批量打印证明
        if (this.tabName == 'scheme') {
          if (this.commoditySkuIdList.length === 0) {
            return this.$message.warning('请先选择您要打印的培训方案且只能选一个培训方案！')
          }

          console.log('this.certificateTemplateId////////', this.certificateTemplateId)

          // 如果选中的培训方案模版id为空提示
          // if (this.certificateTemplateId == '') {
          //   return this.$message.error('该方案未配置培训证明模板')
          // }
          let res
          if (this.isFxlogin) {
            res = await new SchemeConfigUtils().queryConfigInDistributor(this.certifyParam.trainingId)
          } else res = await SchemeConfigUtils.queryConfig(this.certifyParam.trainingId)
          this.certificateTemplateId = res?.assessSetting?.learningResults?.find(
            (item: any) => item.certificateTemplateId
          )?.certificateTemplateId
          if (this.certificateTemplateId) {
            // 通过模版id查询是否合并
            try {
              this.isMerge =
                (await this.queryCertificateTemplateIsMerge.findCertificateTemplate(this.certificateTemplateId)) ??
                false
            } catch (e) {
              console.log(e)
              this.isMerge = false
            }
          } else {
            return this.$message.error('该方案未配置培训证明模板')
          }
          await this.getStudentNo()
          // 按方案打印批量打印列表证明抽屉
          this.schemeBatchPrintDrawerRef.open()
          this.printBtnLoading = false
        } else {
          await this.getStudentNo()
          this.batchSelectTrainingPrintingDrawerRef.open(this.source)
          this.printBtnLoading = false
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.printBtnLoading = false
      }
    }

    // 批量请求studentNo
    async getStudentNo() {
      // 把学员集合列表清空一次，防止无限叠加
      this.studentNoList = []

      this.printBtnLoading = true
      // 打印前查询对应方案或学员的全量数据
      // 一次查30条
      const page = new Page(1, 200)
      const printArr = new Array<LearningArcjovesResponse>()
      //   第一次请求，获取当前totalPageSize
      let printRes
      if (this.isFxlogin) {
        printRes = await this.queryLearningArchivesListInDistributor(page)
      } else if (this.isZtglyLogin) {
        printRes = await this.queryLearningTrainingChannelList(page)
      } else {
        printRes = await this.queryLearningArchivesList(page)
      }
    }

    // 分销查询
    async queryLearningArchivesListInDistributor(page: Page) {
      return await this.TrainingCertificateModule.queryLearningArchivesListInDistributor(page, this.certifyParam)
    }

    // 专题查询
    async queryLearningTrainingChannelList(page: Page) {
      return await this.TrainingCertificateModule.queryLearningTrainingChannelList(page, this.certifyParam)
    }

    // 网校查询
    async queryLearningArchivesList(page: Page) {
      return await this.TrainingCertificateModule.queryLearningArchivesList(page, this.certifyParam)
    }

    // 打印请求
    @bind
    @debounce(200)
    async requestPreview({ studentNo, qualificationId }: LearningArcjovesResponse) {
      this.printType = 'single'

      if (this.tabName == 'scheme') {
        // 四川建设：按方案打印前需要建议该模版是否有个人照片，再判断是否照片以上传
        this.batchPrintTrainingModule.printCertificateParams.studentNo = studentNo
        this.batchPrintTrainingModule.printCertificateParams.fileType = FileTypesEnum.PDF
        this.batchPrintTrainingModule.printCertificateParams.qualificationId = qualificationId
        const res = await this.batchPrintTrainingModule.doPrintTraining()
        if (!res.status.isSuccess()) {
          return this.errorResTips(res)
        }
        if (res.data?.code === '50001') {
          return this.$message.error('培训成果未推送，无法打印培训证明')
        }
        if (res.data?.path) {
          this.preview(res.data.path)
        }
      } else {
        // 选中复选框清除
        // ;(this.$refs['tableDataRef'] as any)?.clearSelection()
        this.studentQualificationId = qualificationId

        this.studentNoList = [studentNo]

        // this.studentPrintRef.open(this.certifyParam)

        // 打开选择培训证明进行打印抽屉
        this.selectTrainingPrintingDrawerRef.open()
      }
    }
    // 打印请求（分销商使用）
    @bind
    @debounce(200)
    async requestPreviewFx({ studentNo, qualificationId }: LearningArcjovesResponse) {
      this.printType = 'single'

      if (this.tabName == 'scheme') {
        this.batchPrintTrainingModule.printCertificateParams.studentNo = studentNo
        this.batchPrintTrainingModule.printCertificateParams.fileType = FileTypesEnum.PDF
        this.batchPrintTrainingModule.printCertificateParams.qualificationId = qualificationId
        const res = await this.batchPrintTrainingModule.doPrintTraining()
        if (res.data?.path && res.status) {
          this.preview(res.data.path)
        } else {
          this.errorResTips(res)
        }
      } else {
        this.studentQualificationId = qualificationId

        this.studentNoList = [studentNo]

        // 打开选择培训证明进行打印抽屉
        this.selectTrainingPrintingDrawerRef.open()
      }
    }
    // 下载
    @bind
    @debounce(500)
    async download({ studentNo, qualificationId, status }: LearningArcjovesResponse) {
      // 如果是未打印的状态
      if (!status) {
        return
      }
      this.batchPrintTrainingModule.printCertificateParams.studentNo = studentNo
      this.batchPrintTrainingModule.printCertificateParams.fileType = FileTypesEnum.PDF
      this.batchPrintTrainingModule.printCertificateParams.qualificationId = qualificationId
      let result: Response<CheckPrintConditionResponse>
      try {
        result =
          this.tabName == 'scheme'
            ? await this.batchPrintTrainingModule.doPrintTraining()
            : await this.batchPrintTrainingModule.doPrintTrainingStudent(
                this.batchPrintTrainingModule.printCertificateParams
              )
        if (result.data?.code === '50001') {
          return this.$message.error('培训成果未推送，无法打印培训证明')
        }
        if (result.data?.path && result.status.isSuccess()) {
          const url = result.data.path

          //   开始对url路径进行截取,主要用于下载按钮的文件名路径
          const fileNameIndex = url.lastIndexOf('/') + 1
          const fileName = url.slice(fileNameIndex)
          const fileUrl = fileName.split('.')[0]

          //下载文件
          const link = document.createElement('a')
          const resolver = this.$router.resolve({
            name: url
          })
          link.id = 'taskLink'
          link.style.display = 'none'
          link.href = resolver.location.name
          const urlArr = link.href.split('.'),
            typeName = urlArr.pop()
          link.setAttribute('download', fileUrl)
          document.body.appendChild(link)
          link.click()
          link.remove()
        } else {
          this.errorResTips(result)
        }
      } catch (e) {
        console.log(e)
      }
    }
    // 跳转新窗口打开证明预览
    preview(url: string) {
      this.templateUrl = url
      this.previewDialog = true
    }

    cancelPreview() {
      this.previewDialog = false
      this.templateUrl = ''
    }

    openPreview() {
      this.previewDialog = false
      window.open(this.templateUrl, '_blank')
    }

    errorResTips(res: Response<CheckPrintConditionResponse>) {
      console.log(res, 'errorResTips')

      const errorMsg = res.status?.errors[0]?.message
      if (errorMsg) {
        const msg = errorMsg.replace(/['{}?(),"\\/\b\f\n\r\t.:;]/g, '')
        this.$message.error(msg.replace(/[a-z0-9A-Z^\s]+/g, ''))
      } else {
        this.$message.error('打印失败！')
      }
    }
    // 前往导出任务查看页面
    toDownloadPage() {
      this.exportDialog = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: {
          type: 'exportCertificatePrintFile'
        }
      })
    }

    importListPrinting() {
      this.$router.push({
        path: '/training/batch-print/import-list-printing'
      })
    }

    //表格可选
    /*getSelectAble(dataItem: LearningArcjovesResponse) {
      console.log('wwwwwww', dataItem)
      //根据status判断是否可以勾选
      return dataItem.status
    }
    tableSelect(selection: LearningArcjovesRequest[]) {
      this.selectCertifyList = selection
    }*/

    // 打印记录，版本暂不实现
    /*    printRecordClick(id: string) {
      console.log(id)
      this.isShowPrintRecord = true
      this.printRecordList = [
        {
          who: '子项目管理员',
          time: '2021-02-02 12:14:15'
        }
      ]
    }*/
  }
</script>

<style lang="less" scoped>
  ::v-deep .el-tabs__active-bar {
    width: 75px !important;
  }
  #search-item /deep/ .el-form-item {
    height: 35px !important;
  }
</style>
