import { CreateSubAdminRequest } from '@api/ms-gateway/ms-account-v1'
import {
  CreateOnlineSchoolSubAdminByTokenRequest,
  CreateOnlineSchoolSubAdminRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { GendersEnum } from '@api/service/common/enums/user/GenderTypes'
import { CreateDistributorAdminRequest } from '@api/platform-gateway/platform-jxjy-distributor-admin-v1'
import Context from '@api/service/common/context/Context'

class CreateSubAdminRequestVo extends CreateSubAdminRequest {
  /**
   * 姓名
   */
  name = ''
  /**
   * 性别
   */
  gender: number = GendersEnum.BOY
  /**
   * 手机
   */
  phone?: string = ''
  /**
   * 邮箱
   */
  email?: string = ''
  /**
   *启用状态  正常: 1 禁用: 2
   */
  status = 1
  /**
   * 登录账户
   */
  identity = ''
  /**
   * 密码
   */
  password = ''
  /**
   * 角色id集合
   */
  roleIds: Array<string> = []

  static toCreateOnlineSchoolSubAdminRequest(params: CreateSubAdminRequestVo) {
    const vo = new CreateOnlineSchoolSubAdminRequest()
    vo.identity = params.identity
    vo.name = params.name
    vo.gender = params.gender
    vo.phone = params.phone
    vo.email = params.email
    vo.status = params.status
    vo.password = params.password
    vo.addRoleIds = params.roleIds
    return vo
  }
  static toCreateOnlineSchoolSubAdminRequestV2(params: CreateSubAdminRequestVo) {
    const vo = new CreateOnlineSchoolSubAdminByTokenRequest()
    vo.identity = params.identity
    vo.name = params.name
    vo.gender = params.gender
    vo.phone = params.phone
    vo.email = params.email
    vo.status = params.status
    vo.password = params.password
    vo.addRoleIds = params.roleIds
    return vo
  }

  static toCreateDistributorAdminRequest(params: CreateSubAdminRequestVo) {
    const vo = new CreateDistributorAdminRequest()
    vo.onlineSchoolId = Context.businessEnvironment.serviceToken?.tokenMeta?.servicerId
    vo.identity = params.identity
    vo.name = params.name
    vo.gender = params.gender
    vo.phone = params.phone
    vo.email = params.email
    vo.status = params.status
    vo.password = params.password
    vo.addRoleIds = params.roleIds
    return vo
  }
}

export default CreateSubAdminRequestVo
