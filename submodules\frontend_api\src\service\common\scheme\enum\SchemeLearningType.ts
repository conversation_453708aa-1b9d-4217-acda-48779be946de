import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 【业务JSON】方案类型枚举
 * choose_course_learning 选课规则
 * autonomous_course_learning 自主选课
 */
export enum SchemeLearningTypeEnum {
  choose_course_learning = 'chooseCourseLearning',
  autonomous_course_learning = 'autonomousCourseLearning',
  training_cooperation = 'trainingCooperation'
}

/**
 * @description 【业务JSON】方案类型
 */
class SchemeLearningType extends AbstractEnum<SchemeLearningTypeEnum> {
  static enum = SchemeLearningTypeEnum

  constructor(status?: SchemeLearningTypeEnum) {
    super()
    this.current = status
    this.map.set(SchemeLearningTypeEnum.choose_course_learning, '选课规则')
    this.map.set(SchemeLearningTypeEnum.autonomous_course_learning, '自主选课')
  }

  /**
   * 是否是选课规则
   * @param type 方案类型
   */
  isChooseCourseLearning(type: SchemeLearningTypeEnum) {
    return type === SchemeLearningTypeEnum.choose_course_learning
  }

  /**
   * 是否是自主选课
   * @param type 方案类型
   */
  isAutonomousCourseLearning(type: SchemeLearningTypeEnum) {
    return type === SchemeLearningTypeEnum.autonomous_course_learning
  }

  /**
   * 是否是培训合作
   * @param type 方案类型
   */
  isTrainingCooperation(type: SchemeLearningTypeEnum) {
    return type === SchemeLearningTypeEnum.training_cooperation
  }
}

export default new SchemeLearningType()
