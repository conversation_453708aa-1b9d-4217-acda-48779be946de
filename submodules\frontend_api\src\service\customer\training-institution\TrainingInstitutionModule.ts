import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import Response, { ResponseStatus } from '@api/Response'
import trainingInstitutionGateway, {
  Page,
  PortalTypeEnums,
  SimpleTrainingInstitutionDto,
  TrainingInstitutionDetailDto,
  TrainingInstitutionListDto,
  TrainingInstitutionQueryParams
} from '@api/gateway/PlatformTrainingInstitution'
import Vue from 'vue'
import servicerMS, { VerifyAuthCVendorPromotionTrainingRequest } from '@api/gateway/ms-servicer-v1'
import { UnAuthorize } from '@api/Secure'

export interface ITrainingInstitutionState {
  trainingInstitutionCount: number
  trainingInstitutionMap: { [id: string]: TrainingInstitutionDetailDto }
  trainingInstitutionDomainMap: { [id: string]: TrainingInstitutionDetailDto }
  /**
   * 详情
   */
  detailList: Array<TrainingInstitutionDetailDto>
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerTrainingInstitutionModuleCustomerModule', store })
class TrainingInstitutionModule extends VuexModule implements ITrainingInstitutionState {
  //region state
  trainingInstitutionCount = 0
  loadTrainingInstitutionCount = true
  trainingInstitutionMap: { [id: string]: TrainingInstitutionDetailDto } = {}
  trainingInstitutionDomainMap: { [id: string]: TrainingInstitutionDetailDto } = {}
  detailList = new Array<TrainingInstitutionDetailDto>()

  courseIsValid = true

  unitPageInfo = new Array<TrainingInstitutionListDto>()
  totalPageSize = 0
  totalSize = 0

  skuByPageInfo = new Array<SimpleTrainingInstitutionDto>()

  //endregion

  //region action
  /*
  获取培训机构分页
  */
  @Action
  async getTrainingInstitutionPage(params: {
    page?: Page
    params?: TrainingInstitutionQueryParams
  }): Promise<ResponseStatus> {
    const response = await trainingInstitutionGateway.page(params)
    if (response.status.isSuccess()) {
      if (params.page.pageNo === 1) {
        this.SET_UNIT_PAGE_INFO(response.data.currentPageData)
      } else {
        this.CONCAT_UNIT_PAGE_INFO(response.data.currentPageData)
      }
      this.SET_UNIT_PAGE_INFO_TOTAL_SIZE(response.data.totalSize)
      this.SET_UNIT_PAGE_INFO_TOTAL_PAGE_SIZE(response.data.totalPageSize)
    }
    return response.status
  }

  /*
  获取培训机构选项
  */
  @Action
  async findTrainingInstitutionList(params: TrainingInstitutionQueryParams): Promise<ResponseStatus> {
    const response = await trainingInstitutionGateway.findTrainingInstitutionList(params)
    if (response.status.isSuccess()) {
      this.SET_TRAINING_NAME_LIST(response.data)
    }
    return response.status
  }

  /*
  获取已启用机构首字母
  */
  @Action
  async findServicerSpellList() {
    const response = await trainingInstitutionGateway.findServicerSpellList()
    return response.data
  }

  /**
   * 获取培训机构数
   */
  @Action
  async getTrainingInstitutionCount(): Promise<ResponseStatus> {
    if (!this.loadTrainingInstitutionCount) {
      return new ResponseStatus(200)
    }
    const response = await trainingInstitutionGateway.getTrainingInstitutionCount()
    if (response.status.isSuccess()) {
      this.SET_COUNT(response.data)
      this.SET_LOAD_COUNT(false)
    }
    return response.status
  }

  /**
   * 获取培训机构信息
   */
  @Action
  async getTrainingInstitution(id: string): Promise<ResponseStatus> {
    const unit = this.trainingInstitutionMap[id]
    if (unit) {
      return new ResponseStatus(200)
    }
    const response = await trainingInstitutionGateway.detail(id)
    if (response.status.isSuccess()) {
      this.SET_DETAIL_INFO(response.data)
    }
    return response.status
  }

  /**
   * 获取培训机构信息提供给小程序使用。直接返回在小程序端做set计算时间太长导致拿不到单位信息
   */
  @Action
  async getTrainingInstitutionByWeChat(id: string) {
    let response = new Response<TrainingInstitutionDetailDto>()
    response.status = new ResponseStatus(200)
    const unit = this.trainingInstitutionMap[id]
    if (unit) {
      response.data = unit
      return response
    }
    response = await trainingInstitutionGateway.detail(id)
    if (response.status.isSuccess()) {
      this.SET_DETAIL_INFO(response.data)
    }
    return response
  }

  /**
   * 获取培训机构信息（通过域名）
   */
  @Action
  async findTrainingInstitutionByDomain(domain: string): Promise<ResponseStatus> {
    const unit = this.trainingInstitutionDomainMap[domain]
    if (unit) {
      return new ResponseStatus(200)
    }
    const response = await trainingInstitutionGateway.findTrainingInstitutionByDomain(domain)

    if (response.status.isSuccess()) {
      this.SET_DOMAIN_DETAIL_INFO(response.data)
    }
    return response.status
  }

  /**
   * 批量获取培训机构详情
   */
  @Action
  async findDetailList(idList: Array<string>): Promise<ResponseStatus> {
    const response = await trainingInstitutionGateway.findDetailList(idList)
    if (response.status.isSuccess()) {
      this.SET_DETAIL_LIST(response.data)
    }
    return response.status
  }

  /**
   * 验证培训机构下对已签约的渠道商授权推广班级是否有效
   * @param boo
   * @constructor
   */
  @Action
  @UnAuthorize
  async verifyAuthCVendorPromotionTrainingEffective(param: VerifyAuthCVendorPromotionTrainingRequest) {
    const response = await servicerMS.verifyAuthCVendorPromotionTrainingEffective(param)
    if (response.status.isSuccess()) {
      this.SET_COURSE_INFO(response.data)
    }
    return response.status
  }

  //endregion

  //region mutation
  @Mutation
  private SET_TRAINING_NAME_LIST(arr: Array<SimpleTrainingInstitutionDto>) {
    this.skuByPageInfo = arr
  }

  @Mutation
  private SET_UNIT_PAGE_INFO(pageInfo: Array<TrainingInstitutionListDto>) {
    this.unitPageInfo = pageInfo
  }
  @Mutation
  private CONCAT_UNIT_PAGE_INFO(pageInfo: Array<TrainingInstitutionListDto>) {
    this.unitPageInfo = [...this.unitPageInfo, ...pageInfo]
  }

  @Mutation
  private SET_UNIT_PAGE_INFO_TOTAL_SIZE(totalSize: number) {
    this.totalSize = totalSize
  }

  @Mutation
  private SET_UNIT_PAGE_INFO_TOTAL_PAGE_SIZE(totalPageSize: number) {
    this.totalPageSize = totalPageSize
  }

  @Mutation
  private SET_LOAD_COUNT(boo: boolean) {
    this.loadTrainingInstitutionCount = boo
  }

  @Mutation
  private SET_COUNT(count: number) {
    this.trainingInstitutionCount = count
  }

  @Mutation
  private SET_DETAIL_INFO(data: TrainingInstitutionDetailDto) {
    const old = this.trainingInstitutionMap[data.id]
    if (old) {
      Object.assign(old, data)
    } else {
      Vue.set(this.trainingInstitutionMap, data.id, data)
    }
  }

  @Mutation
  private SET_DOMAIN_DETAIL_INFO(data: TrainingInstitutionDetailDto) {
    let webDomain = ''
    data.portals?.forEach(portal => {
      if (PortalTypeEnums.WEB == portal.portalType) {
        // 原先domainName为hb.btpxv2.dev.59iedu.com改为https://hb.btpxv2.dev.59iedu.com
        if (portal.domainName.indexOf('//')) {
          webDomain = portal.domainName.split('//')[1]
        } else {
          webDomain = portal.domainName
        }
      }
    })
    if (webDomain.length > 0) {
      const old = this.trainingInstitutionDomainMap[webDomain]
      if (old) {
        Object.assign(old, data)
      } else {
        Vue.set(this.trainingInstitutionDomainMap, webDomain, data)
      }
    }
  }

  /**
   * 设置详情
   */
  @Mutation
  private SET_DETAIL_LIST(data: Array<TrainingInstitutionDetailDto>) {
    this.detailList = data
  }

  /**
   * 设置班级是否有效
   */
  @Mutation
  private SET_COURSE_INFO(data: boolean) {
    this.courseIsValid = data
  }

  //endregion

  /**
   * 从当前详情信息，根据门户类型返回门户信息
   */
  get getPortalFromDetailByType() {
    return (detailDto: TrainingInstitutionDetailDto, portalType: PortalTypeEnums) => {
      return detailDto.portals?.find(e => e.portalType == portalType)
    }
  }

  get getTrainingInstitutionDetail() {
    return (id: string) => {
      return this.trainingInstitutionMap[id] || (undefined as TrainingInstitutionDetailDto)
    }
  }
}

export default getModule(TrainingInstitutionModule)
