import AbstractEnum from '@api/service/common/enums/AbstractEnum'
import OrderRefundOption from '@api/service/common/return-order/models/OrderRefundOption'
import RefundOption from '@api/service/common/return-order/models/RefundOption'
import { CommodityRefundStatusEnum } from '@api/service/common/return-order/enums/CommodityRefundStatus'

export enum OrderRefundTypeEnum {
  /**
   * 仅退货（不退款）
   */
  returnOnly = 1,

  /**
   * 仅退款（不退货）
   */
  refundOnly = 2,

  /**
   * 退货且退款
   */
  returnAndRefund = 3,

  /**
   * 仅部分退货（不退款）
   */
  partialReturnNoRefund = 4,

  /**
   * 仅部分退款（不退货）
   */
  partialRefundNoReturn = 5,

  /**
   * 部分退货且部分退款
   */
  partialReturnPartialRefund = 6,

  /**
   * 部分退货且全额退款
   */
  partialReturnFullRefund = 7,

  /**
   * 全部退货且部分退款
   */
  fullReturnPartialRefund = 8
}

/**
 * 含退款列表
 */
export const IncludeRefundList: OrderRefundTypeEnum[] = [
  OrderRefundTypeEnum.returnAndRefund,
  OrderRefundTypeEnum.refundOnly,
  OrderRefundTypeEnum.partialRefundNoReturn,
  OrderRefundTypeEnum.fullReturnPartialRefund,
  OrderRefundTypeEnum.partialReturnPartialRefund,
  OrderRefundTypeEnum.partialReturnFullRefund
]

/**
 * 仅退货列表
 */
export const OnlyReturnList: OrderRefundTypeEnum[] = [
  OrderRefundTypeEnum.returnOnly,
  OrderRefundTypeEnum.partialReturnNoRefund
]

/**
 * 仅退款列表
 */
export const OnlyRefundList: OrderRefundTypeEnum[] = [
  OrderRefundTypeEnum.refundOnly,
  OrderRefundTypeEnum.partialRefundNoReturn
]

/**
 * 部分退货列表
 */
export const PartialReturnList: OrderRefundTypeEnum[] = [
  OrderRefundTypeEnum.partialReturnNoRefund,
  OrderRefundTypeEnum.partialReturnPartialRefund,
  OrderRefundTypeEnum.partialReturnFullRefund
]

class OrderRefundType extends AbstractEnum<OrderRefundTypeEnum> {
  static enum = OrderRefundTypeEnum
  constructor(status?: OrderRefundTypeEnum) {
    super()
    this.current = status
    this.map.set(OrderRefundTypeEnum.returnAndRefund, '退货且退款')
    this.map.set(OrderRefundTypeEnum.returnOnly, '仅退货（不退款）')
    this.map.set(OrderRefundTypeEnum.refundOnly, '仅退款（不退货）')
    this.map.set(OrderRefundTypeEnum.partialReturnNoRefund, '仅部分退货（不退款）')
    this.map.set(OrderRefundTypeEnum.partialRefundNoReturn, '仅部分退款（不退货）')
    this.map.set(OrderRefundTypeEnum.partialReturnFullRefund, '部分退货且全额退款')
    this.map.set(OrderRefundTypeEnum.fullReturnPartialRefund, '全部退货且部分退款')
    this.map.set(OrderRefundTypeEnum.partialReturnPartialRefund, '部分退货且部分退款')
  }

  /**
   * 数组转换为UI展示Option
   * @param list
   */
  transformToUiOptions(list: Array<OrderRefundTypeEnum>) {
    const optionList = new Array<OrderRefundOption>()
    const haveRefund = new OrderRefundOption()
    const notRefund = new OrderRefundOption()

    haveRefund.label = '含退款'
    notRefund.label = '不含退款'
    list.map((item: OrderRefundTypeEnum) => {
      if (IncludeRefundList.includes(item)) {
        haveRefund.arr.push(new RefundOption(item, this.map.get(item)))
      } else {
        const label = this.map.get(item)
        if (label) {
          notRefund.arr.push(new RefundOption(item, this.map.get(item)))
        }
      }
    })
    optionList.push(haveRefund)
    optionList.push(notRefund)

    return optionList
  }

  /**
   * 获取所有枚举值
   */
  getAllEnumMemberUiOptions() {
    const list = Object.values(OrderRefundTypeEnum) as Array<OrderRefundTypeEnum>

    // todo 这期剔除掉部分退货的 后续有加回来这边删掉
    const filterPartialReturnList = list.filter(it => !PartialReturnList.includes(it))

    return this.transformToUiOptions(filterPartialReturnList)
  }

  /**
   * 获取所有枚举值（给差异化使用获取全部枚举）
   */
  getAllEnumMemberUiOptionsWithDiff() {
    const list = Object.values(OrderRefundTypeEnum) as Array<OrderRefundTypeEnum>

    return this.transformToUiOptions(list)
  }

  /**
   * 获取子单可选退款类型
   * @param commodityRefundStatus 商品退货状态
   * @param remainingAmount 剩余可退款金额
   * @param remainingQuantity 剩余可退款数量
   */
  switchSubOrderCanRefundType(
    commodityRefundStatus: CommodityRefundStatusEnum,
    remainingAmount: number,
    remainingQuantity: number
  ) {
    let list = new Array<OrderRefundTypeEnum>()
    // todo 这边的转换剔除了所有部分退货的 本期不实现，后续要实现这边要调整
    if (commodityRefundStatus === CommodityRefundStatusEnum.returnedFullRefund) {
      list = []
    } else if (commodityRefundStatus === CommodityRefundStatusEnum.returnedFull) {
      if (remainingAmount > 0) {
        list = [OrderRefundTypeEnum.refundOnly, OrderRefundTypeEnum.partialRefundNoReturn]
      } else {
        list = []
      }
    } else if (commodityRefundStatus === CommodityRefundStatusEnum.refundedFull) {
      if (remainingQuantity > 0) {
        list.push(OrderRefundTypeEnum.returnOnly)
        if (remainingAmount > 1) {
          list.push(OrderRefundTypeEnum.partialReturnNoRefund)
        }
      } else {
        list = []
      }
    } else if (commodityRefundStatus === CommodityRefundStatusEnum.partialRefund) {
      if (remainingAmount > 0) {
        list.push(OrderRefundTypeEnum.partialRefundNoReturn)
        if (remainingQuantity > 0) {
          list.push(...[OrderRefundTypeEnum.fullReturnPartialRefund, OrderRefundTypeEnum.returnOnly])
        }
        if (remainingQuantity > 1) {
          list.push(OrderRefundTypeEnum.partialReturnNoRefund)
        }
      } else {
        if (remainingQuantity > 0) {
          list.push(...[OrderRefundTypeEnum.fullReturnPartialRefund, OrderRefundTypeEnum.returnOnly])
        }
        if (remainingQuantity > 1) {
          list.push(OrderRefundTypeEnum.partialReturnNoRefund)
        }
      }
    } else if (commodityRefundStatus === CommodityRefundStatusEnum.partialReturn) {
      if (remainingAmount > 0) {
        list.push(...[OrderRefundTypeEnum.refundOnly, OrderRefundTypeEnum.partialRefundNoReturn])
      }
    } else if (commodityRefundStatus === CommodityRefundStatusEnum.returnedPartialRefund) {
      if (remainingAmount > 0) {
        list.push(OrderRefundTypeEnum.partialRefundNoReturn)
      }
    } else if (commodityRefundStatus === CommodityRefundStatusEnum.partialReturnFullRefund) {
      // todo
    } else if (commodityRefundStatus === CommodityRefundStatusEnum.partialReturnPartialRefund) {
      list.push(OrderRefundTypeEnum.partialRefundNoReturn)
    } else if (commodityRefundStatus === CommodityRefundStatusEnum.processing) {
      list = []
    } else {
      if (remainingAmount > 0) {
        list.push(
          ...[
            OrderRefundTypeEnum.refundOnly,
            OrderRefundTypeEnum.partialRefundNoReturn,
            OrderRefundTypeEnum.fullReturnPartialRefund,
            OrderRefundTypeEnum.returnAndRefund
          ]
        )
      }
      list.push(OrderRefundTypeEnum.returnOnly)
    }

    return list
  }
}

export default new OrderRefundType()
