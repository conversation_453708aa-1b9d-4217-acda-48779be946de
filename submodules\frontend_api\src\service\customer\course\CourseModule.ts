import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import BTPXCourse from '@api/service/common/models/course/BTPXCourse'
import { Deprecated, UnAuthorize } from '@api/Secure'
import CourseParam from '@api/service/common/models/course/BTPXCourseParam'
import CourseGateway, { CourseQueryDTO, CourseResponse } from '@api/gateway/btpx@Course-default'
import { ResponseStatus } from '@api/Response'
import Course from '@api/service/common/models/course/Course'
import CourseCategory from '@api/service/common/models/course/course-category/CourseCategory'
import Courseware from '@api/service/common/models/course/Courseware'
import CourseChapter from '@api/service/common/models/course/CourseChapter'
import BTPXCourseGateWay from '@api/gateway/btpx@Course-default'

export interface ICourseState {
  /**
   * 课程信息
   */
  courseList: Array<BTPXCourse>
  cache: Array<BTPXCourse>

  /**
   * 总数
   */
  totalSize: number
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerCourseModule', store })
class CourseModule extends VuexModule {
  courseList = new Array<BTPXCourse>()

  totalSize = 0

  cache = new Array<BTPXCourse>()

  @Action
  @UnAuthorize
  async loadCoursePage(queryParam: { page: { pageNo: number; pageSize: number }; queryParam: CourseParam }) {
    const response = await CourseGateway.findCoursePage({ page: queryParam.page, query: queryParam.queryParam })

    if (response.status.isSuccess()) {
      this.setTotalSize(response.data.totalSize)
      const arr =
        response.data.currentPageData?.map(p => {
          const course = new BTPXCourse()
          Object.assign(course, p)
          return course
        }) || []
      this.setCourseList(arr)
      this.setAndRefreshCache(arr)
    }

    return response.status
  }

  /**
   * 加载课程到缓存内
   * @param courseId
   */
  @Action
  @UnAuthorize
  async loadCourseToCaches(param: { courseIds: Array<string>; reload: boolean }) {
    const courseIds = param.reload
      ? param.courseIds
      : param.courseIds.filter(courseId => !this.cache.some(x => x.id === courseId))
    if (courseIds.length === 0) {
      return new ResponseStatus(200)
    }
    const courseQueryDTO = new CourseQueryDTO()
    courseQueryDTO.includeCourseIdList = courseIds
    courseQueryDTO.status = -1
    courseQueryDTO.enable = -1
    courseQueryDTO.resourceUploadType = 0
    const queryParam = { page: { pageNo: 1, pageSize: courseIds.length }, query: courseQueryDTO }
    const response = await BTPXCourseGateWay.getCoursePage(queryParam)
    if (response.status.isSuccess()) {
      this.setAndRefreshCache(
        response.data.currentPageData?.map(p => {
          const course = new BTPXCourse()
          Object.assign(course, p)
          return course
        }) || []
      )
    }
    return response.status
  }

  /**
   * 加载单门课程
   * @param courseId
   */
  @Action
  @UnAuthorize
  async loadSingleCourse(courseId: string) {
    const response = await CourseGateway.getCourse(courseId)
    if (response.status.isSuccess()) {
      const course = this.convertToCourse(response.data)
      this.replaceCourseList([course])
    }
    return response.status
  }

  /**
   * 获取课程统计
   */
  @Action
  @UnAuthorize
  @Deprecated
  async statisticCourse() {
    return await BTPXCourseGateWay.getCourseStatisticForPortal()
  }

  /**
   * 转换模型
   * @param param
   */
  get convertToCourse() {
    return (param: CourseResponse) => {
      const course: Course = new Course()
      course.id = param.id
      course.unitId = param.unitId
      course.name = param.name
      course.iconPath = param.iconPath
      course.period = param.period
      course.totalLecture = param.plannedLecturesNum
      course.alreadyUpdateLecture = param.courseOutline.length
      course.timeLength = param.courseOutline
        .map(p => {
          if (p.courseWare) {
            return p.courseWare?.timeLength || 0
          } else {
            return p.subCourseOutlines.map(x => x.courseWare?.timeLength || 0).reduce((a, b) => a + b, 0)
          }
        })
        .reduce((a, b) => a + b, 0)
      course.abouts = param.abouts
      course.status = param.status
      course.categoryList =
        param.categoryList.map(x => {
          const category = new CourseCategory()
          Object.assign(category, x)
          return category
        }) || []
      if (param.courseOutline.some(x => x.courseWare)) {
        course.courseWares = param.courseOutline.map(x => {
          const courseWare = new Courseware()
          courseWare.id = x.courseWareId
          courseWare.courseChapterId = x.id
          courseWare.name = x.courseWare.name
          courseWare.timeLength = x.courseWare.timeLength
          courseWare.type = x.courseWare.type
          courseWare.category = x.courseWare.category
          courseWare.extensionResourceResponse = x.courseWare.extensionResourceResponse
          return courseWare
        })
      } else {
        course.courseOutline = param.courseOutline.map(x => {
          const chapter = new CourseChapter()
          chapter.id = x.id
          chapter.name = x.name
          chapter.parentId = x.parentId
          chapter.sort = x.sort
          chapter.courseWares = x.subCourseOutlines.map(x => {
            const courseWare = new Courseware()
            courseWare.id = x.courseWareId
            courseWare.courseChapterId = x.id
            courseWare.name = x.courseWare.name
            courseWare.timeLength = x.courseWare.timeLength
            courseWare.type = x.courseWare.type
            courseWare.category = x.courseWare.category
            courseWare.extensionResourceResponse = x.courseWare.extensionResourceResponse
            return courseWare
          })
          return chapter
        })
      }
      course.teacherList = param.teachers
      course.enabled = param.enabled
      course.createTime = param.createTime
      return course
    }
  }

  @Mutation
  protected setCourseList(courseList: Array<Course>) {
    this.courseList = courseList
  }

  @Mutation
  protected setTotalSize(totalSize: number) {
    this.totalSize = totalSize
  }

  @Mutation
  protected replaceCourseList(courseList: Array<Course>) {
    this.courseList = this.courseList.filter(x => !courseList.some(y => y.id === x.id))
    this.courseList.push(...courseList)
  }

  @Mutation
  setAndRefreshCache(courseList: Array<BTPXCourse>) {
    this.cache = this.cache.filter(x => !courseList.some(course => course.id === x.id))
    this.cache.push(...courseList)
  }

  /**
   * 获取总数
   */
  get getTotalSize() {
    return this.totalSize
  }

  /**
   * 获取课程列表
   */
  get getCourseList() {
    return this.courseList
  }

  /**
   * 获取课程信息
   */
  get getCourseInfo() {
    return (courseId: string) => {
      return this.courseList.find(p => p.id === courseId) || this.cache.find(p => p.id === courseId)
    }
  }

  /**
   * 查询某课程是否支持试听
   */
  get isCourseAllowTryListen() {
    return (courseId: string): boolean => {
      return this.getCourseInfo(courseId)?.customStatus === 1 || false
    }
  }
}

export default getModule(CourseModule)
