import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum RefundProcessTypeEnum {
  /**
   * 提交申请
   */
  submitApplication = 1,

  /**
   * 审批申请
   */
  approveApplication = 2,

  /**
   * 处理退货
   */
  handleReturn = 3,

  /**
   * 退款确认
   */
  refundConfirmation = 4,

  /**
   * 处理退款
   */
  processRefund = 5,

  /**
   * 完成
   */
  completeProcess = 6
}
class RefundProcessTypeStatus extends AbstractEnum<RefundProcessTypeEnum> {
  static enum = RefundProcessTypeEnum
  constructor(status?: RefundProcessTypeEnum) {
    super()
    this.current = status
    this.map.set(RefundProcessTypeEnum.submitApplication, '提交申请')
    this.map.set(RefundProcessTypeEnum.approveApplication, '审批申请')
    this.map.set(RefundProcessTypeEnum.handleReturn, '处理退货')
    this.map.set(RefundProcessTypeEnum.refundConfirmation, '退款确认')
    this.map.set(RefundProcessTypeEnum.processRefund, '处理退款')
    this.map.set(RefundProcessTypeEnum.completeProcess, '完成')
  }
}

export default new RefundProcessTypeStatus()
