import store from '@/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import RoleQueryFactory from '@api/service/management/authority/role/RoleQueryFactory'
import ServiceProviderFactory from '@api/service/management/authority/service-provider/ServiceProviderFactory'
import SecurityFactory from '@api/service/management/authority/security/SecurityFactory'
import RoleMutationFactory from '@api/service/management/authority/role/RoleMutationFactory'

@Module({
  name: 'AuthorityModule',
  dynamic: true,
  store,
  namespaced: true
})
class AuthorityModule extends VuexModule {
  roleFactory: RoleQueryFactory = new RoleQueryFactory()
  roleMutationFactory: RoleMutationFactory = new RoleMutationFactory()

  serviceProviderFactory: ServiceProviderFactory = new ServiceProviderFactory()

  securityFactory: SecurityFactory = new SecurityFactory()
}

export default getModule(AuthorityModule)
