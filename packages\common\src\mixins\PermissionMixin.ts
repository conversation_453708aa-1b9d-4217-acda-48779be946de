import { Component, Vue } from 'vue-property-decorator'
import SecurityModule from '@/store/SecurityModule'
import DevToolsModule from '@/store/devtools/DevToolsModule'
import RootModule from '@/store/RootModule'

@Component
class PermissionMixin extends Vue {
  /**
   *  权限路由分组
   */
  _PermissionRouteGroup = ''

  created() {
    this._PermissionRouteGroup = this.$route?.meta.group
  }

  /**
   * 权限判断
   * @param key
   * @param isGlobal
   */
  $hasPermission(key: string, isGlobal = false) {
    let permissionResult = false
    if (!this.$route) {
      return permissionResult
    }
    if (process.env.NODE_ENV === 'development') {
      if (!DevToolsModule.developmentSettings.permissionControl) {
        return true
      }
    }
    let type = SecurityModule.serviceType
      ? SecurityModule.serviceType.toLocaleLowerCase()
      : SecurityModule.isServiceProvider
      ? SecurityModule.targetRole
      : 'super'

    // 判断当前路由是否以 /fx/ 开头，并在 type 后拼接 .fx
    if (this.$route.path.startsWith('/fx/')) {
      if (!isGlobal) {
        type += '.fx'
      }
    }

    // 构建权限键数组
    const strArr = isGlobal ? [type, key] : [type, this._PermissionRouteGroup]

    // 处理可能的多个权限键
    const keys = key.split(',')
    if (isGlobal) {
      const idKey = strArr.join('.')
      return RootModule.securityList.includes(idKey)
    } else {
      permissionResult = keys.some((singleKey) => {
        const idKey = [...strArr, singleKey].join('.')
        return RootModule.securityList.includes(idKey)
      })
    }

    return permissionResult
  }
}

export default PermissionMixin
