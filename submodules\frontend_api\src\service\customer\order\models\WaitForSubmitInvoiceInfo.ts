import { InvoiceRequest } from '@api/gateway/Trade-default'

class WaitForSubmitInvoiceInfo {
  /**
   * 发票类型：1普通发票，2增值税普通发票，3增值税专用发票
   */
  type: number
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型，0：无抬头类型，1：个人，2：企业
   */
  titleType: number
  /**
   * 统一社会信用代码
   */
  taxpayerNo: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 账号
   */
  account: string
  /**
   * 是否电子票
   */
  electron = true

  toInvoiceRequestDTO(): InvoiceRequest {
    const dto = new InvoiceRequest()
    dto.type = this.type
    dto.title = this.title
    dto.titleType = this.titleType
    dto.taxpayerNo = this.taxpayerNo
    dto.address = this.address
    dto.phone = this.phone
    dto.bankName = this.bankName
    dto.account = this.account
    dto.electron = this.electron
    dto.noTaxBill = false
    return dto
  }
}

export default WaitForSubmitInvoiceInfo
