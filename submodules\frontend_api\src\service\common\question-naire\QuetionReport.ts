import ScaleStatistic from '@api/service/common/question-naire/models/ScaleStatistic'
import OptionStatistic from '@api/service/common/question-naire/models/OptionStatistic'
import Question from '@api/service/common/question-naire/Question'
import AskQuestionDetailItem from '@api/service/common/question-naire/models/AskQuestionDetailItem'
import MockUtil from '../utils/MockUtil'
import { QuestionTypeEnum } from '@api/service/common/enums/question-naire/QuestionType'
import PlatQuestionnaireQueryBackStage, {
  QuestionnaireAnswerContentRequest,
  QuestionnaireAnswerStaitsticsRequest
} from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'
import DiffBackstage, {
  AnswerAskQuestionRequest,
  AnswerPaperAnswerQuestionRequest
} from '@api/platform-gateway/diff-data-export-gateway-backstage'
import { Page } from '@hbfe/common'
import { QuestionSourceTypeEnum } from '@api/service/common/question-naire/enums/QuestionSourceType'
import {
  QuestionnaireAnswerTeacherResponse,
  AnswerStaitsticsResponse
} from '@api/platform-gateway/diff-exam-query-front-gateway-QuestionnaireQueryBackStage'
/**
 * 题目报告
 * 统计从Statistic中取
 */
export default class QuetionReport {
  /**
   * 试题id
   */
  id = ''
  /**
   * 题目类型
   */
  type: QuestionTypeEnum = null
  /**
   * 题目描述
   */
  described = ''
  /**
   * 提交答题数
   */
  submitNum = 0
  /**
   * 提交比例
   */
  submitProportion = 0
  /**
   * 选择题选项统计内容
   */
  optionsStatisc: OptionStatistic[] = []
  /**
   * 量表题统计内容
   */
  scaleStatisc: ScaleStatistic[] = []
  /**
   * 是否教师题
   */
  isTeacherEvaluate = false
  /**
   * 线上/线下
   */
  onlineOrOffline: QuestionSourceTypeEnum = QuestionSourceTypeEnum.normal
  /**
   * 管理端-查询问答题详细信息
   */
  async queryAskQuestionDetail(page: Page, questionnaireId: string) {
    const request = new QuestionnaireAnswerContentRequest()
    request.questionnaireId = questionnaireId
    request.questionId = this.id
    const res = await PlatQuestionnaireQueryBackStage.pageQuestionnaireAnswerContentInServicer({ page, request })
    let askList = new Array<AskQuestionDetailItem>()
    if (res.status.isSuccess() && res.data?.currentPageData?.length) {
      askList = res.data.currentPageData.map((item) => {
        const temp = new AskQuestionDetailItem()
        temp.text = item.answer
        temp.submitTime = item.submitTime
        return temp
      })
    }
    page.totalPageSize = res.data?.totalPageSize || 0
    page.totalSize = res.data?.totalSize || 0

    return askList
  }

  /**
   * 管理端-导出问答文本
   */
  async exportAskQuestionText(questionnaireId: string) {
    const request = new AnswerAskQuestionRequest()
    request.questionnaireId = questionnaireId
    request.questionId = this.id
    request.jobName = '问答文本'
    const res = await DiffBackstage.exportAnswerAskQuestionExcelInServicer(request)
    return res.data || false
  }

  /**
   * 参转
   */
  static from(
    dto: AnswerStaitsticsResponse,
    offlineTeachers?: QuestionnaireAnswerTeacherResponse[],
    onlineTeachers?: QuestionnaireAnswerTeacherResponse[]
  ) {
    const vo = new QuetionReport()
    // vo.described=dto.
    vo.id = dto.questionId
    // 问答题提交比例要在外面算，需要问卷人数
    vo.submitNum = dto.totalCount
    vo.onlineOrOffline = dto.tag as QuestionSourceTypeEnum
    vo.isTeacherEvaluate = vo.onlineOrOffline != QuestionSourceTypeEnum.normal
    const teacherList = dto.tag === QuestionSourceTypeEnum.online_teacher ? onlineTeachers : offlineTeachers
    switch (dto.questionType) {
      case 1:
        // 单选
        vo.type = 1
        if (dto.statisticsAnswerContents.length) {
          vo.optionsStatisc = dto.statisticsAnswerContents.map((item) => {
            const temp = new OptionStatistic()
            temp.id = item.answerItem
            temp.content = item.answerItem
            temp.selectNum = item.chooseCount
            if (vo.submitNum) {
              temp.selectProportion = Number((item.chooseCount / vo.submitNum).toFixed(4))
            }
            return temp
          })
          if (vo.isTeacherEvaluate) {
            vo.optionsStatisc.map((item) => {
              teacherList.map((sitem) => {
                if (!vo.optionsStatisc.map((mitem) => mitem.content).includes(sitem.name)) {
                  // 添加没选中过的教师
                  const temp = new OptionStatistic()
                  temp.id = sitem.id
                  temp.content = sitem.name
                  temp.selectNum = 0
                  vo.optionsStatisc.push(temp)
                } else if (sitem.name == item.content) {
                  // 已选过的教师，补全id
                  item.id = sitem.id
                }
              })
            })
          }
        } else {
          // 教师非必答题未有人作答
          vo.optionsStatisc = teacherList?.map((item) => {
            const temp = new OptionStatistic()
            temp.id = item.id
            temp.content = item.name
            temp.selectNum = 0
            temp.selectProportion = 0
            return temp
          })
          if (vo.isTeacherEvaluate) {
            vo.optionsStatisc.map((item) => {
              teacherList.map((sitem) => {
                if (!vo.optionsStatisc.map((mitem) => mitem.content).includes(sitem.name)) {
                  // 添加没选中过的教师
                  const temp = new OptionStatistic()
                  temp.id = sitem.id
                  temp.content = sitem.name
                  temp.selectNum = 0
                  vo.optionsStatisc.push(temp)
                } else if (sitem.name == item.content) {
                  // 已选过的教师，补全id
                  item.id = sitem.id
                }
              })
            })
          }
        }
        break
      case 2:
        // 多选
        vo.type = 2
        if (dto.statisticsAnswerContents.length) {
          vo.optionsStatisc = dto.statisticsAnswerContents.map((item) => {
            const temp = new OptionStatistic()
            temp.id = item.answerItem
            temp.content = item.answerItem
            temp.selectNum = item.chooseCount
            if (vo.submitNum) {
              temp.selectProportion = Number((item.chooseCount / vo.submitNum).toFixed(4))
            }
            return temp
          })
          if (vo.isTeacherEvaluate) {
            vo.optionsStatisc.map((item) => {
              teacherList.map((sitem) => {
                if (!vo.optionsStatisc.map((mitem) => mitem.content).includes(sitem.name)) {
                  // 添加没选中过的教师
                  const temp = new OptionStatistic()
                  temp.id = sitem.id
                  temp.content = sitem.name
                  temp.selectNum = 0
                  vo.optionsStatisc.push(temp)
                } else if (sitem.name == item.content) {
                  // 已选过的教师，补全id
                  item.id = sitem.id
                }
              })
            })
          }
        } else {
          // 教师非必答题未有人作答
          vo.optionsStatisc = teacherList?.map((item) => {
            const temp = new OptionStatistic()
            temp.id = item.id
            temp.content = item.name
            temp.selectNum = 0
            temp.selectProportion = 0
            return temp
          })
          if (vo.isTeacherEvaluate) {
            vo.optionsStatisc.map((item) => {
              teacherList.map((sitem) => {
                if (!vo.optionsStatisc.map((mitem) => mitem.content).includes(sitem.name)) {
                  // 添加没选中过的教师
                  const temp = new OptionStatistic()
                  temp.id = sitem.id
                  temp.content = sitem.name
                  temp.selectNum = 0
                  vo.optionsStatisc.push(temp)
                } else if (sitem.name == item.content) {
                  // 已选过的教师，补全id
                  item.id = sitem.id
                }
              })
            })
          }
        }
        break
      case 5:
        // 问答
        vo.type = 3
        break
      case 7:
        // 量表
        vo.type = 4
        vo.scaleStatisc = dto.statisticsAnswerContents.map((item) => {
          const temp = new ScaleStatistic()
          temp.levelNum = Number(item.answerItem)
          temp.selectNum = item.chooseCount || 0
          temp.selectProportion = Number((item.chooseCount / vo.submitNum).toFixed(4))
          return temp
        })
        break
      default:
        break
    }
    vo.isTeacherEvaluate = dto.isTeacherQuestion || false
    return vo
  }
}
