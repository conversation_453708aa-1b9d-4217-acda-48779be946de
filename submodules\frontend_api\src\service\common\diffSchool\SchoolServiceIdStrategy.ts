import systemContext from '@api/service/common/context/Context'
import { frontendApplication, frontendApplicationDiff } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import SchoolEnum from '@api/service/common/diffSchool/enums/SchoolEnum'

class SchoolServiceIdStrategy {
  // 网校映射
  schoolMap = new Map<string, SchoolEnum>()

  // 读取阿波罗网校服务商id
  getSchoolServiceId = {
    anxi: () => {
      const schoolServiceId = ConfigCenterModule.getFrontendApplication(frontendApplication.anxiServicerId)
      if (!this.schoolMap.has(schoolServiceId)) {
        this.schoolMap.set(schoolServiceId, SchoolEnum.ANXI)
      }
      return this.schoolMap.get(schoolServiceId)
    },
    // fjzj: () => {
    //   const schoolServiceId = ConfigCenterModule.getFrontendApplication(frontendApplication.fjzjServicerId)
    //   if (!this.schoolMap.has(schoolServiceId)) {
    //     this.schoolMap.set(schoolServiceId, SchoolEnum.FJZJ)
    //   }
    //   return this.schoolMap.get(schoolServiceId)
    // },
    fjzj: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.fjzjServicerId)?.split(',')
      schoolServiceIds?.forEach((id) => {
        if (!this.schoolMap.has(id)) {
          this.schoolMap.set(id, SchoolEnum.FJZJ)
        }
        return this.schoolMap.get(id)
      })
    },
    anhui: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.ahzjServicerId)?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.ANHUI)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    scjzs: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.scjzsServicerId)?.split(
        ','
      )
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.SCJZS)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    yzzj: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.yzzjServicerId)?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.YZZJ)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    zzkd: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.zzkdServicerId)?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.ZZKD)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    gszj: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.gszjServicerId)?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.GSZJ)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    xmlg: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.xmlgServicerId)?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.XMLG)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    byzj: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.byzjServicerId)?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.BYZJ)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    jxgx: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.jxgxServicerId)?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.JXGX)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    hljysxh: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.hljysServicerId)?.split(
        ','
      )
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.HLJYSXH)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    nyyz: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.nyyzServicerId)?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.NYYZ)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    zzzj: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.zzzjServicerId)?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.ZZZJ)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    scys: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.scysServicerId)?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.SCYS)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    gyms: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.gymsServicerId)?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.GYMS)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    mqzj: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.mqzjServicerId)?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.MQZJ)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    sjdb: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplication(frontendApplication.sjdbServicerId)?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.SJDB)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    qztg: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplicationDiff(
        frontendApplicationDiff.qztgServicerId
      )?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.QZTG)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    gstyb: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplicationDiff(
        frontendApplicationDiff.gstybServicerId
      )?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.GSTYB)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    zjzj: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplicationDiff(
        frontendApplicationDiff.zjzjServicerId
      )?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.ZJZJ)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    lmdx: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplicationDiff(
        frontendApplicationDiff.lmdxServicerId
      )?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.LMDX)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    },
    zztt: () => {
      const schoolServiceIds = ConfigCenterModule.getFrontendApplicationDiff(
        frontendApplicationDiff.zzttServicerId
      )?.split(',')
      schoolServiceIds?.forEach((schoolServiceId) => {
        if (!this.schoolMap.has(schoolServiceId)) {
          this.schoolMap.set(schoolServiceId, SchoolEnum.ZZTT)
        }
        return this.schoolMap.get(schoolServiceId)
      })
    }
  }

  buildSchoolMap() {
    this.getSchoolServiceId.anxi()
    this.getSchoolServiceId.fjzj()
    this.getSchoolServiceId.anhui()
    this.getSchoolServiceId.scjzs()
    this.getSchoolServiceId.yzzj()
    this.getSchoolServiceId.zzkd()
    this.getSchoolServiceId.gszj()
    this.getSchoolServiceId.xmlg()
    this.getSchoolServiceId.byzj()
    this.getSchoolServiceId.jxgx()
    this.getSchoolServiceId.hljysxh()
    this.getSchoolServiceId.nyyz()
    this.getSchoolServiceId.zzzj()
    this.getSchoolServiceId.scys()
    this.getSchoolServiceId.gyms()
    this.getSchoolServiceId.mqzj()
    this.getSchoolServiceId.sjdb()
    this.getSchoolServiceId.qztg()
    this.getSchoolServiceId.gstyb()
    this.getSchoolServiceId.zjzj()
    this.getSchoolServiceId.lmdx()
    this.getSchoolServiceId.zztt()
  }

  // 获取当前网校
  currentSchool() {
    const serverId = systemContext.servicerInfo.id
    if (this.schoolMap.has(serverId)) {
      return this.schoolMap.get(serverId)
    }
    return ''
  }
}

export default new SchoolServiceIdStrategy()
