import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformUserLearningScheme'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformUserLearningScheme'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum QueryParamOperatePolicyEnum {
  AND = 'AND',
  OR = 'OR'
}
export enum PaymentChannelTypeEnum {
  WEB = 'WEB',
  ANDROID = 'ANDROID',
  IOS = 'IOS',
  WECHAT_OFFICIAL_ACCOUNTS = 'WECHAT_OFFICIAL_ACCOUNTS',
  WECHAT_MINI_PROGRAMS = 'WECHAT_MINI_PROGRAMS',
  PRESENT = 'PRESENT',
  COLLECTIVE = 'COLLECTIVE',
  HTML5 = 'HTML5',
  DINGDING = 'DINGDING',
  CHANNEL_PRESENT_OPEN = 'CHANNEL_PRESENT_OPEN',
  EXTERNAL_SYSTEM_MANAGE = 'EXTERNAL_SYSTEM_MANAGE'
}

// 类

/**
 * 用户期数预约查询
@author: eleven
@date: 2020/3/13
 */
export class UserIssueReservationParamDTO {
  /**
   * 查询的用户id - 运营与参数，学员端的忽略该参数
   */
  userId?: string
  /**
   * 是否合格
   */
  qualified?: boolean
  /**
   * 期数ID集合
   */
  issueIds?: Array<string>
  /**
   * 方案名字模糊查询
   */
  schemeName?: string
  /**
   * 培训工种类别/培训工种id path
用于培训类别联合工种多条件查询
   */
  workTypeIdPathList?: Array<string>
  /**
   * 期数是否过期
   */
  isIssueFinish?: boolean
  /**
   * 期数是否过期查询策略，默认and
   */
  isIssueFinishQueryPolicy?: QueryParamOperatePolicyEnum
  /**
   * 年度
   */
  year?: number
  /**
   * 培训类别
   */
  trainingTypeId?: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId?: string
}

/**
 * 用户期数校验参数
@author: eleven
@date: 2020/3/30
 */
export class UserIssueValidParam {
  /**
   * 购买的用户
   */
  userId?: string
  /**
   * 校验的期数id
   */
  issueId?: string
}

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * graphql没有泛型的操作结果类
<AUTHOR> create 2020/3/9 15:18
 */
export class GraphqlOperateResult {
  /**
   * 返回的code
   */
  code: string
  /**
   * 返回的message
   */
  message: string
  /**
   * json字段
存放返回的参数用
   */
  expandData: Map<string, string>
}

/**
 * @author: eleven
@date: 2020/6/5
 */
export class IssueClassLSAchieveSettingResponse {
  enabledAssess: boolean
  grade: number
  templateId: string
}

/**
 * 课程考核信息
@author: eleven
@date: 2020/6/5
 */
export class UserCourseLearningAssessResponse {
  /**
   * 用户学习进度
   */
  userSchedule: number
  /**
   * 已选学时
   */
  hasSelectPeriod: number
  /**
   * 已经学习的学时
   */
  hasStudyPeriod: number
  allSelectedComplete: boolean
  schedule: number
}

/**
 * @author: eleven
@date: 2020/6/5
 */
export class UserExamAssessResponse {
  /**
   * 用户考试最高成绩
   */
  userHigestScore: number
  /**
   * 当前考核状态，-1未考核，0考核不通过，1考核通过
备注：该值可能被考核计算策略影响
   */
  assessStatus: number
  /**
   * 考核通过时间
   */
  passedTime: string
  score: number
}

/**
 * 试题练习
@author: eleven
@date: 2020/3/24
 */
export class QuestionPracticeDTO {
  practiceId: string
  fetchWay: number
  libraryWaySetting: LibraryWaySetting
  tagsWaySetting: TagWaySetting
}

/**
 * @author: eleven
@date: 2020/3/24
 */
export class UserCourseLearningDTO {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 选课规则ID
   */
  ruleConfigId: string
  /**
   * 学习方案要求的最少学时
   */
  minTotalPeriod: number
  /**
   * 课程学习考核，null表示不设置考核
   */
  userCourseLearningAssess: UserCourseLearningAssessResponse
}

/**
 * @author: eleven
@date: 2020/3/24
 */
export class UserExamLearningDTO {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 场次ID
   */
  examRoundId: string
  /**
   * 试卷ID
   */
  examPaperId: string
  /**
   * 开考时间
   */
  beginTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 考试次数 0表示不限制次数
   */
  examCount: number
  /**
   * 考试时长
   */
  examTimeLength: number
  /**
   * 场次名称
   */
  name: string
  /**
   * 考试考核，null表示不设置考核
   */
  userExamAssess: UserExamAssessResponse
}

/**
 * 用户期数预约信息
@author: eleven
@date: 2020/3/13
 */
export class UserIssueDTO {
  /**
   * 方案所属单位id
   */
  schemeUnitId: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 方案名称
   */
  scheme: string
  /**
   * 方案封面图片地址
   */
  picture: string
  /**
   * 期数id
   */
  issueId: string
  /**
   * 期数标题
   */
  issueTitle: string
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 期数预约创建时间
   */
  createTime: string
  /**
   * 课程学习方式
   */
  courseLearning: UserCourseLearningDTO
  /**
   * 考试学习方式
   */
  examLearning: UserExamLearningDTO
  /**
   * 练习学习方式
   */
  practiceLearning: UserPracticeLearningDTO
  /**
   * 培训班成果设置
   */
  achieveSetting: IssueClassLSAchieveSettingResponse
  /**
   * 兴趣课程包配置
   */
  interestCourseSetting: InterestCourseSetting
  /**
   * 当前考核状态，-1未考核，0考核不通过，1考核通过
备注：该值可能被考核计算策略影响
   */
  assessStatus: number
  /**
   * 考核通过时间
   */
  passedTime: string
  /**
   * 来源渠道
   */
  channel: PaymentChannelTypeEnum
  /**
   * 是否开放打印证明
   */
  isOpenPrintCertificate: boolean
  /**
   * 适用人群
   */
  suitablePeople: Array<string>
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * 用户期数预约信息
@author: eleven
@date: 2020/3/13
 */
export class UserIssueDetailDTO {
  /**
   * 课程供应商id
   */
  coursewareSupplierId: string
  /**
   * 课程供应商名称
   */
  coursewareSupplierName: string
  /**
   * 方案所属单位id
   */
  schemeUnitId: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 方案名称
   */
  scheme: string
  /**
   * 方案封面图片地址
   */
  picture: string
  /**
   * 期数id
   */
  issueId: string
  /**
   * 期数标题
   */
  issueTitle: string
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 期数预约创建时间
   */
  createTime: string
  /**
   * 课程学习方式
   */
  courseLearning: UserCourseLearningDTO
  /**
   * 考试学习方式
   */
  examLearning: UserExamLearningDTO
  /**
   * 练习学习方式
   */
  practiceLearning: UserPracticeLearningDTO
  /**
   * 培训班成果设置
   */
  achieveSetting: IssueClassLSAchieveSettingResponse
  /**
   * 兴趣课程包配置
   */
  interestCourseSetting: InterestCourseSetting
  /**
   * 当前考核状态，-1未考核，0考核不通过，1考核通过
备注：该值可能被考核计算策略影响
   */
  assessStatus: number
  /**
   * 考核通过时间
   */
  passedTime: string
  /**
   * 来源渠道
   */
  channel: PaymentChannelTypeEnum
  /**
   * 是否开放打印证明
   */
  isOpenPrintCertificate: boolean
  /**
   * 适用人群
   */
  suitablePeople: Array<string>
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * <AUTHOR>
@date 2020/8/22
@description
 */
export class UserIssueSimpleResponse {
  /**
   * 用户id
   */
  userId: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 方案名称
   */
  scheme: string
  /**
   * 期数id
   */
  issueId: string
  /**
   * 期数标题
   */
  issueTitle: string
  /**
   * 机构Id
   */
  trainingInstitutionId: string
  /**
   * 机构名称
   */
  trainingInstitutionName: string
  /**
   * 期数预约创建时间
   */
  createTime: string
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 方案整体是否合格
   */
  qualified: boolean
  /**
   * 物品状态
@see UserGoodState#getValue()
   */
  goodState: string
  /**
   * 是否有课程学习方式
   */
  hasCourseLearning: boolean
  /**
   * 课程总体学习进度
   */
  courseSchedule: number
  /**
   * 来源渠道
   */
  channel: PaymentChannelTypeEnum
  /**
   * 适用人群
   */
  suitablePeople: Array<string>
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * @author: eleven
@date: 2020/3/24
 */
export class UserPracticeLearningDTO {
  /**
   * 学习方式
   */
  learningId: string
  /**
   * 试题练习
   */
  questionPractice: QuestionPracticeDTO
}

/**
 * 用户学习时长统计
@author: eleven
@date: 2020/3/5
 */
export class UserSchemeLearningTimeDTO {
  /**
   * 学习时长 单位：s
前端自己换算成  h 或 min
   */
  learningTimeLength: number
  /**
   * 学习时长排名
   */
  learningTimeRank: number
  /**
   * 学习时长参与排名人数
   */
  totalUserCount: number
}

export class UserSchemeSimpleDTO {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 培训班名称
   */
  name: string
  /**
   * 培训班类别ID
   */
  categoryId: string
  /**
   * 年度
   */
  year: number
  /**
   * 培训班状态，1表示已发布、2表示配置中、3表示未发布
   */
  status: number
  /**
   * 所属地区 格式：中国/福建省/福州市
   */
  region: string
  /**
   * 培训班封面图片地址
   */
  picture: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 来源类型
   */
  sourceType: string
  /**
   * 来源ID
   */
  sourceId: string
  /**
   * 创建方式 1:系统创建 2:用户创建 3:管理员创建 4:历史迁移 5:外部接口
   */
  createType: number
  /**
   * 用户在培训班学习方案的状态，0有效，1冻结，2失效
   */
  userState: number
  /**
   * 培训结果，-1未进行考核，0考核不通过，1考核通过
   */
  trainingResult: number
  /**
   * 培训开始时间
   */
  trainingBeginDate: string
  /**
   * 培训结束时间
   */
  trainingEndDate: string
  /**
   * 培训班完成时间
   */
  completedTime: string
  /**
   * 用户报名时间
   */
  registerTime: string
  /**
   * 是否开放打印证明
   */
  openPrintCertificate: boolean
  /**
   * 培训班学习方案平台自定义类型ID
   */
  lSCustomerTypeId: string
}

/**
 * <AUTHOR>
@date 2020/8/12
@description
 */
export class InterestCourseSetting {
  /**
   * 兴趣课程包集合
前端传递的参数，服务内部转为 interestPoolIdList
   */
  poolList: Array<string>
}

export class LibraryWaySetting {
  libraryIds: Array<string>
  recursive: boolean
}

export class TagWaySetting {
  tagIds: Array<string>
}

export class UserIssueSimpleResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserIssueSimpleResponse>
}

export class UserIssueDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserIssueDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取用户已预约期数信息
   * todo 单位id（字段暂时没有调整为培训机构服务商id，待方案微服务化后再做调整）:客服咨询-方案列表-方案详情
   * @param userId
   * @param issueId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUserIssueReservation(
    params: { userId?: string; issueId?: string },
    query: DocumentNode = GraphqlImporter.getUserIssueReservation,
    operation?: string
  ): Promise<Response<UserIssueDetailDTO>> {
    return commonRequestApi<UserIssueDetailDTO>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量获取用户购买的学习方案信息
   * @param query 查询 graphql 语法文档
   * @param userIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUserSchemeByUserIds(
    userIds: Array<string>,
    query: DocumentNode = GraphqlImporter.getUserSchemeByUserIds,
    operation?: string
  ): Promise<Response<Array<UserSchemeSimpleDTO>>> {
    return commonRequestApi<Array<UserSchemeSimpleDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { userIds },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 用户期数预约查询
   * todo 单位id（字段暂时没有调整为培训机构服务商id，待方案微服务化后再做调整）:学员端-方案列表
   * @param paramDTO
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserIssueReservation(
    paramDTO: UserIssueReservationParamDTO,
    query: DocumentNode = GraphqlImporter.listUserIssueReservation,
    operation?: string
  ): Promise<Response<Array<UserIssueDTO>>> {
    return commonRequestApi<Array<UserIssueDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 用户期数分页查询 -数据来源清洗表1
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUserIssueReservation(
    params: { page?: Page; request?: UserIssueReservationParamDTO },
    query: DocumentNode = GraphqlImporter.pageUserIssueReservation,
    operation?: string
  ): Promise<Response<UserIssueSimpleResponsePage>> {
    return commonRequestApi<UserIssueSimpleResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 用户期数预约查询分页实时
   * todo 单位id（字段暂时没有调整为培训机构服务商id，待方案微服务化后再做调整）:学员端-方案列表
   * @param page
   * @param paramDTO
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUserIssueReservationActual(
    params: { page?: Page; paramDTO?: UserIssueReservationParamDTO },
    query: DocumentNode = GraphqlImporter.pageUserIssueReservationActual,
    operation?: string
  ): Promise<Response<UserIssueDTOPage>> {
    return commonRequestApi<UserIssueDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 用户学习时长统计 - - 中间表(已实现)
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserSchemeLearningTime(
    query: DocumentNode = GraphqlImporter.statisticUserSchemeLearningTime,
    operation?: string
  ): Promise<Response<UserSchemeLearningTimeDTO>> {
    return commonRequestApi<UserSchemeLearningTimeDTO>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 用户期数校验
   * 200: 校验通过
   * 201:期数已过期
   * 202:期数已失效
   * 203:期数退款冻结
   * 204:期数换货冻结 - 预留code
   * @param paramDto
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDto 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validUserIssue(
    paramDto: UserIssueValidParam,
    query: DocumentNode = GraphqlImporter.validUserIssue,
    operation?: string
  ): Promise<Response<GraphqlOperateResult>> {
    return commonRequestApi<GraphqlOperateResult>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDto },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
