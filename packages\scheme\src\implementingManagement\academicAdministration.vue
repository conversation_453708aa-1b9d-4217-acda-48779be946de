<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return" @click="jumpToPreTrainingImplementation"></i>
      </el-button>
      <el-breadcrumb-item style="cursor: pointer;"
        ><span @click="jumpToPreTrainingImplementation">实施管理</span></el-breadcrumb-item
      >
      <el-breadcrumb-item>教务管理</el-breadcrumb-item>
    </el-breadcrumb>

    <div class="f-p15">
      <template
        v-if="$hasPermission('getPeriodConfigById')"
        desc="查询教务管理培训方案信息"
        actions="getPeriodConfigById,created"
      >
        <el-card shadow="never" class="m-card f-mb15">
          <div slot="header" class="">
            <span class="tit-txt">培训方案信息 </span>
          </div>
          <el-form ref="form" label-width="150px" class="m-form">
            <el-form-item label="方案名称：">
              <el-tag
                class="f-mr10"
                v-if="trainingInfo.schemeConfig.trainClassBaseInfo.skuProperty.year.skuPropertyName"
                >{{ trainingInfo.schemeConfig.trainClassBaseInfo.skuProperty.year.skuPropertyName }}</el-tag
              >
              <el-tag
                type="success"
                class="f-mr10"
                v-if="trainingInfo.schemeConfig.trainClassBaseInfo.skuProperty.region.skuPropertyName"
                >{{ trainingInfo.schemeConfig.trainClassBaseInfo.skuProperty.region.skuPropertyName }}</el-tag
              >
              <el-tag
                type="warning"
                class="f-mr10"
                v-if="trainingInfo.schemeConfig.trainClassBaseInfo.skuProperty.trainingCategory.skuPropertyName"
                >{{ trainingInfo.schemeConfig.trainClassBaseInfo.skuProperty.trainingCategory.skuPropertyName }}</el-tag
              >
              <el-tag
                type="danger"
                class="f-mr10"
                v-if="trainingInfo.schemeConfig.trainClassBaseInfo.skuProperty.trainingMajor.skuPropertyName"
                >{{ trainingInfo.schemeConfig.trainClassBaseInfo.skuProperty.trainingMajor.skuPropertyName }}</el-tag
              >
              <el-tag
                type="info"
                class="f-mr10"
                v-if="trainingInfo.schemeConfig.trainClassBaseInfo.skuProperty.subjectType.skuPropertyName"
                >{{ trainingInfo.schemeConfig.trainClassBaseInfo.skuProperty.subjectType.skuPropertyName }}</el-tag
              >
              {{ trainingInfo.schemeConfig.trainClassBaseInfo.name || '--' }}
            </el-form-item>
            <el-form-item label="期别名称：">{{ trainingInfo.name || '--' }}</el-form-item>
            <el-form-item label="报到时间段："
              >{{ trainingInfo.checkInTime.begin || '--' }} 至 {{ trainingInfo.checkInTime.end || '--' }}</el-form-item
            >
            <el-form-item label="培训时间段："
              >{{ trainingInfo.trainingTime.begin || '--' }} 至
              {{ trainingInfo.trainingTime.end || '--' }}</el-form-item
            >
          </el-form>
        </el-card>
      </template>
      <el-tabs v-model="activeName2" type="card" class="m-tab-card">
        <el-tab-pane label="教务管理" name="frist">
          <template v-if="$hasPermission('educational')" desc="教务管理下的教务管理页" actions="@educational">
            <educational
              :periodId="periodId"
              :schemeId="schemeId"
              :trainingEndTime="trainingInfo.schemeConfig.trainClassBaseInfo.trainingEndDate"
              :isOpenAccommodationInfoCollect="trainingInfo.schemePeriodConfig.isOpenAccommodationInfoCollect"
              :isOpenGraduationTest="trainingInfo.schemePeriodConfig.isOpenGraduationTest"
              :isSetReportConfig="trainingInfo.isSetReportConfig"
              :skuProperty="trainingInfo.schemeConfig.trainClassBaseInfo.skuProperty"
            ></educational
            >"
          </template>
        </el-tab-pane>
        <el-tab-pane label="问卷管理" name="second">
          <template v-if="$hasPermission('Questionnaire')" desc="教务管理下的问卷管理页" actions="@Questionnaire">
            <questionnaire :periodId="periodId" :schemeId="schemeId"></questionnaire>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import educational from '@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/educational.vue'
  import Questionnaire from '@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/questionnaire.vue'
  import PeriodImplementBase from '@api/service/management/implement/models/PeriodImplementBase'
  @Component({
    components: {
      educational,
      Questionnaire
    }
  })
  export default class extends Vue {
    periodId = ''
    schemeId = ''
    periodConfig = ''
    trainingInfo: PeriodImplementBase = null
    constructor() {
      super()
    }
    periodImplementBase: PeriodImplementBase
    async created() {
      console.log(this.$route, '路由参数')
      this.periodId = this.$route.params.periodId
      this.schemeId = this.$route.params.schemeId
      this.periodImplementBase = new PeriodImplementBase(this.periodId)
      await this.getPeriodConfigById()
    }
    async getPeriodConfigById() {
      await this.periodImplementBase.getPeriodConfigById()
      this.trainingInfo = JSON.parse(JSON.stringify(this.periodImplementBase))
      console.log(this.trainingInfo, '培训方案信息')
    }
    // todo
    dialog1 = true
    input = ''
    activeName2 = 'frist'
    jumpToPreTrainingImplementation() {
      //带上本来传进来的 方案id
      this.$router.push(`/training/scheme/implementingManagement/${this.schemeId}`)
    }
  }
</script>

<style scoped lang="scss"></style>
