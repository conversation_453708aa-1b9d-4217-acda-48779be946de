<template>
  <el-main id="mainContent" ref="mainRef">
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/basic-data/platform/function' }">学习规则管理</el-breadcrumb-item>
      <el-breadcrumb-item>编辑学习规则</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <!-- 基础设置  -->
      <el-card shadow="never" class="m-card is-header">
        <div slot="header" class="">
          <span class="tit-txt">基础设置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form :rules="rules" :model="basicInfoForm" label-width="auto" class="m-form f-pt30">
              <el-form-item label="适用行业：" required>{{ basicInfoForm.industryName }} </el-form-item>
              <el-form-item label="适用范围：" required>
                <el-radio-group v-model="basicInfoForm.applyRange">
                  <el-radio v-for="item in applyRangeList" :label="item.code" :key="item.code" disabled
                    >{{ item.desc }}
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                      <i class="el-icon-info m-tooltip-icon f-c9"></i>
                      <div slot="content">
                        {{ applyRangeText(item.desc) }}
                      </div>
                    </el-tooltip>
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 选择地区  -->
              <el-form-item label="选择地区：" required v-show="basicInfoForm.applyRange == ApplyRangeEnum.region">
                <el-button type="primary" plain @click="openDialog('selectRegionFun')">选择地区</el-button>
                <!--表格-->
                <el-table
                  :data="regionList"
                  max-height="500px"
                  class="m-table f-mt15"
                  border
                  ref="regionTable"
                  v-loading="loading"
                >
                  <el-table-column type="index" label="No." width="60"></el-table-column>
                  <el-table-column label="省份" min-width="100">
                    <template v-slot="{ row }">{{ diffRegion(row, '省份') }} </template>
                  </el-table-column>
                  <el-table-column label="地市" min-width="100">
                    <template v-slot="{ row }"> {{ diffRegion(row, '地市') }}</template>
                  </el-table-column>
                  <el-table-column label="区县" min-width="240">
                    <template v-slot="{ row }"> {{ diffRegion(row, '区县') }}</template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="120" align="center" fixed="right">
                    <template v-slot="{ row }">
                      <el-button type="text" size="mini" @click="editTheRegionFun(row)">编辑</el-button>
                      <el-button type="text" size="mini" @click="deleteRegionItem(row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
              <!-- 选择培训方案  -->
              <el-form-item label="选择培训方案：" required v-show="basicInfoForm.applyRange == ApplyRangeEnum.scheme">
                <el-button type="primary" plain @click="openDialog('selectTrainingProgramFun')">选择培训方案</el-button>
                <!--表格-->
                <el-table
                  ref="schemeList"
                  :data="schemeList"
                  max-height="500px"
                  class="m-table f-mt15"
                  border
                  v-loading="loading"
                >
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="培训方案名称" min-width="220">
                    <template v-slot="{ row }">{{ row.schemeName }}</template>
                  </el-table-column>
                  <el-table-column label="方案属性" min-width="240">
                    <template v-slot="{ row }">
                      <sku-display :sku-item="row"></sku-display>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="100" align="center" fixed="right">
                    <template v-slot="{ row }">
                      <el-button type="text" size="mini" @click="deleteSchemeItem(row.schemeId)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
              <!-- 指定年度政策学习时间  -->
              <el-form-item label="指定年度政策学习时间：" required>
                <el-button type="primary" plain @click="trainingTimeFun('addYearFun')">添加年度</el-button>
                <el-alert type="info" show-icon :closable="false" class="m-alert f-mt10">
                  <div class="lh20">
                    设置培训年度学习时间：选择培训年度并设置学习开始时间和合格时间的时间区间。若学员开始时间或合格学习不在设置的学习区间内，则会按照规则生成学习和考试数据。指定年度配置学习时间区间，未配置的培训年度则不会触发计算。
                  </div>
                </el-alert>
              </el-form-item>
              <!-- 培训时间区间  -->
              <el-form-item label="培训时间区间：" required v-show="basicInfoForm.yearTrainRangeList.length">
                <div class="f-mb5" v-for="(item, index) in basicInfoForm.yearTrainRangeList" :key="index">
                  <el-tag size="small" class="f-mr20 f-vm">年度：{{ item.year }}</el-tag>
                  <span class="f-mr30">{{ item.startTime }} 至 {{ item.endTime }}</span>
                  <a
                    class="f-link el-icon-edit-outline f-mr10"
                    @click="trainingTimeFun('editTrainingTimeFun', item, index)"
                  ></a>
                  <a class="f-link el-icon-delete f-mr10" @click="deleteTrainingTime(index)"></a>
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>

      <!-- 规则设置  -->
      <el-card shadow="never" class="m-card is-header f-mt15">
        <div slot="header" class="">
          <span class="tit-txt">规则设置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-mt30">
              学员同一个天内报名多个培训班时：
            </el-alert>
            <el-form :rules="rules" :model="rulesInfoForm" label-width="auto" class="m-form f-mt10">
              <el-form-item label="是否需要错开开始学习日期：" required prop="staggerStartTrainingTime">
                <el-radio-group v-model="rulesInfoForm.staggerStartTrainingTime">
                  <el-radio :label="true">错开开始学习时间</el-radio>
                  <el-radio :label="false">不需要</el-radio>
                </el-radio-group>
                <el-alert type="info" show-icon :closable="false" class="m-alert f-mt10">
                  <div class="lh20">
                    选择需要错开开通日期时，若学员在同一天内报名了多个培训班时，会将学员不同培训班的学习开始日期（月、日）错开，避免重复
                  </div>
                </el-alert>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <!--以下为更多设置内容-->
        <div class="f-pt20 f-pb40">
          <el-divider>
            <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
              <!--<el-button type="primary" plain>更多设置<i class="el-icon-plus el-icon&#45;&#45;right"></i></el-button>-->
              <!--展开后显示-->
              <el-button type="primary" plain @click="controlOption"
                >{{ isShowExtra ? '收起更多设置' : '更多设置' }} <i :class="getClassName"></i
              ></el-button>
              <div slot="content">
                更多配置内可以设置课程学习规则、考试规则、特殊规则，系统默认设置一套学习规则，若需要调整配置，修改后保存生效，新合格的学员会按照保存的规则计算。
                <p class="f-co f-mt5">设置详细规则时会影响学习规则计算，请谨慎设置！</p>
              </div>
            </el-tooltip>
          </el-divider>
        </div>
        <div v-show="isShowExtra">
          <!--课程学习规则-->
          <div class="m-sub-tit is-border-bottom">
            <span class="tit-txt">课程学习规则</span>
          </div>
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form
                ref="courseStudyRulesForm"
                :rules="rules"
                :model="rulesInfoForm"
                label-width="auto"
                class="m-form f-mt30"
              >
                <el-form-item>
                  <div slot="label">
                    <!--<span class="f-cr f-mr5">*</span>-->
                    <span class="f-vm"> 每天学习时间</span>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                      <div slot="content">
                        学习时间是指触发重新计算学习记录时，每门课程的学习时间会在设置的时间段内，时间段外的时间不会学习
                      </div>
                    </el-tooltip>
                    <span>：</span>
                  </div>
                  <div class="time-item form-l">
                    <div class="time">00:00:00 至 23:59:59</div>
                  </div>
                </el-form-item>
                <el-form-item label="每天不学习时间：">
                  <el-button type="primary" icon="el-icon-plus" plain @click="addNoStudyTimeFun()"
                    >添加时间段</el-button
                  >
                  <div
                    class="time-item form-l f-mt15"
                    v-for="(item, index) in rulesInfoForm.notLearningTimes"
                    :key="index"
                  >
                    <div class="time">{{ item.startTime }} 至 {{ item.endTime }}</div>
                    <i class="f-link f-cb el-icon-delete f-f18" @click="deleteNoStudyTime(index)"></i>
                  </div>
                </el-form-item>
                <el-form-item label="首次开始学习时间：" prop="firstLearningStartTime">
                  开通班级的
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <el-input-number
                      :controls="false"
                      :precision="0"
                      :min="0"
                      type="number"
                      v-model="rulesInfoForm.firstLearningStartTime"
                      @change="changeFirstLearningStartTime"
                      size="small"
                      class="input-num f-mlr5"
                    />
                    <div slot="content">请输入正整数</div>
                  </el-tooltip>
                  <i class="f-mlr5">~</i>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <el-input-number
                      :controls="false"
                      :precision="0"
                      :min="0"
                      type="number"
                      v-model="rulesInfoForm.firstLearningEndTime"
                      @change="changeFirstLearningEndTime"
                      size="small"
                      class="input-num f-mlr5"
                    />
                    <div slot="content">请输入正整数</div>
                  </el-tooltip>
                  天内随机开始学习。
                </el-form-item>
                <el-form-item label="每天最多学习时长：" prop="maxLearningTime">
                  <el-radio-group v-model="basicInfoForm.timeMode" @input="changeTimeMode">
                    <el-radio v-model="timeModeEnum.learning" :label="timeModeEnum.learning">按课程学习学时</el-radio>
                    <el-radio v-model="timeModeEnum.physical" :label="timeModeEnum.physical">按课程物理时长</el-radio>
                  </el-radio-group>
                  <div class="f-mt15">
                    每天课程学习最多
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <el-input-number
                        :controls="false"
                        :precision="0"
                        :min="0"
                        type="number"
                        v-model="rulesInfoForm.maxLearningTime"
                        @change="changeMaxLearningTime"
                        size="small"
                        class="input-num f-mlr5"
                      />
                      <div slot="content">
                        请输入正整数，{{
                          basicInfoForm.timeMode === timeModeEnum.learning
                            ? '一天学习时长不能超过32学时'
                            : '一天学习时长不能超过1440分钟'
                        }}
                      </div>
                    </el-tooltip>
                    <i>{{ basicInfoForm.timeMode === timeModeEnum.learning ? '学时' : '小时' }}</i>
                    且每次学习时长达到
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <el-input-number
                        :controls="false"
                        :precision="0"
                        :min="0"
                        type="number"
                        :class="rulesInfoForm.learningTime > 0 ? '' : 'no-border'"
                        v-model="rulesInfoForm.learningTime"
                        @change="changeLearningTime"
                        size="small"
                        class="input-num f-mlr5"
                      />
                      <div slot="content">
                        请输入正整数，{{
                          basicInfoForm.timeMode === timeModeEnum.learning
                            ? '一天学习时长不能超过32学时'
                            : '一天学习时长不能超过1440分钟'
                        }}
                      </div>
                    </el-tooltip>
                    <i>{{ basicInfoForm.timeMode === timeModeEnum.learning ? '学时' : '小时' }}</i>
                    ，随机休息 60~180 分钟。
                  </div>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>

          <!--测验规则-->
          <div class="m-sub-tit is-border-bottom">
            <span class="tit-txt">测验规则</span>
          </div>
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <div class="f-mtb30 f-flex f-align-center">
                <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>
                <span class="f-flex-sub"
                  >课程学习结束后进入对应的课程测验，测验开始时间和结束时间随机间隔分钟数15-60分钟。</span
                >
              </div>
            </el-col>
          </el-row>

          <!--考试规则-->
          <div class="m-sub-tit is-border-bottom">
            <span class="tit-txt">考试规则</span>
          </div>
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <div class="f-mt30 f-flex f-align-center">
                <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>
                <span class="f-flex-sub"
                  >课程学习结束后进入考试，考试开始时间和结束时间随机间隔分钟数，最少间隔总考试时长的三分之一的时间。</span
                >
              </div>
              <el-alert type="info" :closable="false" class="m-alert f-mt10 f-mb30">
                例：考试总时长60分钟，开始和结束时间至少间隔20分钟。
              </el-alert>
            </el-col>
          </el-row>

          <!--特殊规则-->
          <div class="m-sub-tit is-border-bottom">
            <span class="tit-txt">特殊规则</span>
          </div>
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form
                ref="specialRulesForm"
                :rules="rules"
                :model="rulesInfoForm"
                label-width="auto"
                class="m-form f-mt30"
              >
                <el-form-item label=" " prop="minAdvanceDay">
                  若重新计算的学习合格时间还是超过学习区间，则开通时间随机提前
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <el-input-number
                      :controls="false"
                      :precision="0"
                      :min="0"
                      type="number"
                      v-model="rulesInfoForm.minAdvanceDay"
                      @change="changeFirstLearningStartTime"
                      size="small"
                      class="input-num f-mlr5"
                    />
                    <div slot="content">请输入正整数</div>
                  </el-tooltip>
                  <i class="f-mlr5">～</i>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <el-input-number
                      :controls="false"
                      :precision="0"
                      :min="0"
                      type="number"
                      v-model="rulesInfoForm.maxAdvanceDay"
                      @change="changeFirstLearningEndTime"
                      size="small"
                      class="input-num f-mlr5"
                    />
                    <div slot="content">请输入正整数</div>
                  </el-tooltip>
                  天。
                </el-form-item>
                <el-alert type="info" show-icon :closable="false" class="m-alert f-mb30">
                  <div class="lh20">
                    若计算出来的学习合格时间还是超过学习区间，会根据配置的天数随机将学员的开通时间提前 X
                    天并且重新计算学习记录，若还是超过，会继续提前开始时间，直到符合学习时间区间。
                  </div>
                </el-alert>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 保存  -->
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="cencel()">取消</el-button>
        <el-button type="primary" :loading="isLoading" @click="save()">保存</el-button>
      </div>
    </div>
    <!-- 添加年度抽屉 -->
    <add-the-year ref="addYear" :basicInfo="basicInfoForm"></add-the-year>
    <!-- 选择地区抽屉 -->
    <select-region ref="selectRegion" :basicInfo="basicInfoForm" @queryRegionList="queryRegionList()"></select-region>
    <!-- 选择培训方案抽屉 -->
    <select-training-program
      ref="selectTrainingProgram"
      :scheme-list="schemeList"
      :basicInfo="basicInfoForm"
      @isCheckIdList="getNewSchemeList"
    ></select-training-program>
    <!-- 添加不学习时间段 -->
    <add-no-study-time ref="addNoStudyTime" :ruleInfo="rulesInfoForm"></add-no-study-time>
    <!-- 编辑地区 -->
    <edit-the-region
      ref="editTheRegion"
      :basicInfo="basicInfoForm"
      @queryRegionList="queryRegionList()"
    ></edit-the-region>
    <!-- 编辑培训时间区间 -->
    <edit-training-time ref="editTrainingTime" :basicInfo="basicInfoForm"></edit-training-time>
  </el-main>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import AddTheYear from '@hbfe/jxjy-admin-platform/src/function/learningRules/components/addTheYear.vue'
  import SelectRegion from '@hbfe/jxjy-admin-platform/src/function/learningRules/components/selectRegion.vue'
  import SelectTrainingProgram from '@hbfe/jxjy-admin-platform/src/function/learningRules/components/selectTrainingProgram.vue'
  import AddNoStudyTime from '@hbfe/jxjy-admin-platform/src/function/learningRules/components/addNoStudyTime.vue'
  import EditTheRegion from '@hbfe/jxjy-admin-platform/src/function/learningRules/components/editTheRegion.vue'
  import EditTrainingTime from '@hbfe/jxjy-admin-platform/src/function/learningRules/components/editTrainingTime.vue'
  import LearningRuleItem from '@api/service/management/learning-rule/LearningRuleItem'
  import ApplyRangeType, { ApplyRangeEnum } from '@api/service/management/learning-rule/enum/ApplyRangeEnum'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'

  import { TrainingTimeRange } from '@api/service/management/learning-rule/model/BasicInfo'
  import { cloneDeep } from 'lodash'
  import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'
  import { ElTable } from 'element-ui/types/table'
  import { ElForm } from 'element-ui/types/form'
  import RuleInfo from '@api/service/management/learning-rule/model/RuleInfo'
  import RegionTreeItem from '@api/service/management/learning-rule/model/RegionTreeItem'
  import { UiPage } from '@hbfe/common'
  import RuleSchemeParams from '@api/service/management/train-class/query/vo/RuleSchemeParams'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import SkuDisplay from '@hbfe/jxjy-admin-platform/src/function/components/skuDisplay.vue'
  import { TimeModeEnum } from '@api/service/management/learning-rule/enum/TimeModeEnum'

  @Component({
    components: {
      AddTheYear,
      SelectRegion,
      SelectTrainingProgram,
      AddNoStudyTime,
      EditTheRegion,
      EditTrainingTime,
      SkuDisplay
    }
  })
  export default class extends Vue {
    @Ref('addYear') addYear: any
    @Ref('selectRegion') selectRegion: any
    @Ref('selectTrainingProgram') selectTrainingProgram: any
    @Ref('addNoStudyTime') addNoStudyTime: any
    @Ref('editTheRegion') editTheRegion: any
    @Ref('editTrainingTime') editTrainingTime: any
    @Ref('regionTable') regionTable: ElTable
    @Ref('courseStudyRulesForm') courseStudyRulesForm: ElForm
    @Ref('specialRulesForm') specialRulesForm: ElForm
    @Ref('mainRef') mainRef: any

    // 表单校验
    rules = {
      firstLearningStartTime: [{ required: true, validator: this.validateFirstLearningStartDay, trigger: 'blur' }],
      minAdvanceDay: [{ required: true, validator: this.validateMinAdvanceDay, trigger: 'blur' }],
      maxLearningTime: [{ required: true, validator: this.validateEveryDayMaxLearningHour, trigger: 'blur' }]
    }

    // 表单初始化
    learningRuleForm = new LearningRuleItem()
    // 基础设置表单
    basicInfoForm = this.learningRuleForm.basicInfo
    // 规则设置表单
    rulesInfoForm = this.learningRuleForm.ruleInfo
    // 保存加载中
    isLoading = false
    // 列表加载中
    loading = false
    // 适用范围列表
    applyRangeList = new ApplyRangeType().list()
    // 适用范围枚举
    ApplyRangeEnum = ApplyRangeEnum
    // 是否显示规则设置额外内容
    isShowExtra = false
    // 接收行业
    industryList: Array<IndustryVo> = QueryIndustry.industryList
    // 方案列表
    schemeList = new Array<RuleSchemeItem>()
    // 基础设置地区table列表
    regionList: Array<RegionTreeItem> = []
    /**
     * 时长方式枚举
     */
    timeModeEnum = TimeModeEnum

    // 获取选中的方案
    getNewSchemeList(newSchemeItemList: Array<RuleSchemeItem>) {
      this.schemeList = cloneDeep(newSchemeItemList)
      ;(this.$refs['schemeList'] as any)?.doLayout()
    }
    // 返回上一级
    cencel() {
      this.$router.push('/basic-data/platform/function')
    }
    // 查询方案
    async querySchemeList() {
      const page = new UiPage()
      page.pageSize = this.learningRuleForm.basicInfo.schemeIds.length
      const queryParams = new RuleSchemeParams()
      queryParams.schemeIds = this.learningRuleForm.basicInfo.schemeIds
      this.schemeList = await new QueryTrainClassCommodityList().pageRuleSchemeList(page, queryParams)
    }
    // 初始化
    async init() {
      const res = await this.learningRuleForm.getDetail(this.$route.params.id)
      if (!res.isSuccess()) {
        this.$message.error('获取详情失败，原因：' + res.getMessage())
        return
      }
      this.basicInfoForm = this.learningRuleForm.basicInfo
      this.rulesInfoForm = this.learningRuleForm.ruleInfo
      // 只有方案类型的需要查询方案列表
      this.loading = true
      if (this.basicInfoForm.applyRange == ApplyRangeEnum.scheme) {
        await this.querySchemeList()
        this.loading = false
      }
      if (this.basicInfoForm.applyRange == ApplyRangeEnum.region) {
        // 初始化地区树
        this.basicInfoForm
          .getRegionTree()
          .then(() => {
            // 获取地区列表
            this.queryRegionList()
          })
          .finally(() => {
            this.loading = false
          })
      }
    }
    created() {
      this.init()
    }
    // 添加年度政策学习弹框
    trainingTimeFun(str: string, row?: TrainingTimeRange, index?: number) {
      if (str == 'addYearFun') {
        // 新增年度弹窗
        this.addYear.init()
      } else {
        // 编辑培训时间弹框
        this.editTrainingTime.init(row, index)
      }
    }
    // 编辑地区弹框
    editTheRegionFun(item: RegionTreeItem) {
      this.editTheRegion.init(item)
    }
    // 删除地区item
    deleteRegionItem(item: RegionTreeItem) {
      this.$confirm('确认删除地市下所有区县吗', '提示', {
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then(() => {
          this.basicInfoForm.updateRegionTree(item.code, [], 'remove')
          this.queryRegionList()
        })
        .catch((e) => {
          //
        })
    }
    // 打开弹窗方法统一
    openDialog(str: string) {
      if (str == 'selectRegionFun') {
        // 选择地区弹窗
        this.selectRegion.openDialog = true
      } else if (str == 'selectTrainingProgramFun') {
        // 选择培训方案弹窗
        this.selectTrainingProgram.init()
      }
    }
    // 打开不学习弹框
    addNoStudyTimeFun() {
      this.addNoStudyTime.init()
    }
    // 删除不学习时间
    deleteNoStudyTime(index: number) {
      this.$confirm('确认删除这条不学习时间吗？', '提示', {
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then((res) => {
          this.rulesInfoForm.removeNoStudyTime(index)
          console.log(res)
        })
        .catch((e) => {
          //
        })
    }
    // 删除方案
    deleteSchemeItem(id: string) {
      this.$confirm('确认删除对应的培训方案吗？', '提示', {
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then(() => {
          this.schemeList = this.schemeList.filter((item) => {
            return item.schemeId != id
          })
        })
        .catch((e) => {
          //
        })
    }
    // 删除培训时间
    deleteTrainingTime(index: number) {
      this.$confirm('确认删除对应的培训时间区间吗？', '提示', {
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then((res) => {
          this.basicInfoForm.removeTimeRange(index)
        })
        .catch((e) => {
          //
        })
    }
    // 获取地区列表
    queryRegionList() {
      this.regionList = this.basicInfoForm.getSelectedRegion()
      this.regionTable.doLayout()
    }
    // 保存
    async save() {
      //todo 校验弹框
      if (!this.basicInfoForm.yearTrainRangeList.length) {
        this.$message.warning('未添加年度政策学习时间，请添加。')
        return false
      }
      if (!this.regionList.length && this.basicInfoForm.applyRange == ApplyRangeEnum.region) {
        this.$message.warning('未选择地区，请选择。')
        return false
      }
      if (!this.schemeList.length && this.basicInfoForm.applyRange == ApplyRangeEnum.scheme) {
        this.$message.warning('未添加培训方案，请添加。')
        return false
      }
      Promise.all([this.courseStudyRulesForm.validate(), this.specialRulesForm.validate()])
        .then(async () => {
          // 方案id 赋值
          this.basicInfoForm.schemeIds = this.schemeList.map((item) => item.schemeId)
          const res = await this.learningRuleForm.update()
          if (res.isSuccess()) {
            this.isLoading = false
            this.$message.success('更改成功')
            this.$router.push('/basic-data/platform/function')
          } else {
            if (this.learningRuleForm.duplicateList.length && this.basicInfoForm.applyRange == ApplyRangeEnum.scheme) {
              // 根据方案id获取方案名称
              const filteredSchemeNames = this.schemeList
                .filter((item) => this.learningRuleForm.duplicateList.some((dupItem) => dupItem === item.schemeId))
                .map((ite) => ite.schemeName)
              this.$message.error(`${filteredSchemeNames.join('、')}方案已有学习规则`)
            } else if (
              this.learningRuleForm.duplicateList.length &&
              this.basicInfoForm.applyRange == ApplyRangeEnum.region
            ) {
              // 打平数组
              const tieArray: Array<RegionTreeItem> = []
              this.basicInfoForm.getRegionList(this.regionList, tieArray)

              // 根据code获取地区名字
              const filteredCodes = tieArray
                .filter((tieItem) => this.learningRuleForm.duplicateList.some((dupItem) => dupItem === tieItem.code))
                .map((filteredItem) => filteredItem.name)
              this.$message.error(`${filteredCodes.join('、')}地区已有学习规则`)
            } else {
              this.$message.error(res.getMessage())
            }
            this.isLoading = false
          }
        })
        .catch((e) => {
          this.isShowExtra = true
          // 打开展开项
          this.openExpand()
        })
    }
    // 适用范围文字映射
    get applyRangeText() {
      return (item: string) => {
        switch (item) {
          case '平台级别':
            return '平台级别规则：对适用行业下所有培训方案都生效，学员完成学习后，若没有培训方案和地区规则时，平台级别规则生效。'
          case '地区级别':
            return '地区级别规则：指定地区的培训学习规则，学员完成学习后，若个人信息的地区在规则设置的地区内，则地区规则生效。'
          case '培训方案级别':
            return '培训方案级别：指定培训方案的培训学习规则。'
          default:
            return ''
        }
      }
    }
    // 收起、展开设置
    controlOption() {
      this.isShowExtra = !this.isShowExtra
    }
    // 获取收起、展开样式
    get getClassName() {
      if (this.isShowExtra) {
        return 'el-icon-minus el-icon--right'
      } else {
        return 'el-icon-plus el-icon&#45;&#45;right'
      }
    }
    // 差异化地区，北京、上海、天津、重庆、澳门、香港
    diffRegion(item: RegionTreeItem, region: string) {
      if (
        item.names[0] == '北京市' ||
        item.names[0] == '上海市' ||
        item.names[0] == '天津市' ||
        item.names[0] == '重庆市' ||
        item.names[0] == '澳门特别行政区' ||
        item.names[0] == '香港特别行政区'
      ) {
        if (region == '省份') {
          return '-'
        } else if (region == '地市') {
          return item.names[0] || '-'
        } else {
          return item.childrenNames.join('、') || '-'
        }
      } else {
        if (region == '省份') {
          return item.names[0] || '-'
        } else if (region == '地市') {
          return item.names[1] || '-'
        } else {
          return item.childrenNames.join('、') || '-'
        }
      }
    }
    // 清空数据方法
    clearData() {
      // 培训时间区间数组置为空
      this.basicInfoForm.yearTrainRangeList = []
      // 地区列表数组置为空
      this.regionList = []
      // 培训方案列表置为空
      this.schemeList = []
      // 重置列表，并清空校验
      this.rulesInfoForm = new RuleInfo()
      this.courseStudyRulesForm.clearValidate()
      this.specialRulesForm.clearValidate()
    }
    // 切换适用行业
    switchIndustries(value: string) {
      if (value == this.basicInfoForm.industryId) return
      this.$confirm('切换行业会导致配置清空，确认要切换行业吗？', '提示', {
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then(() => {
          this.clearData()
          this.basicInfoForm.industryId = value
        })
        .catch((e) => {
          console.log('点击了取消', this.basicInfoForm.industryId)
        })
    }
    // 切换适用范围
    switchRange(value: ApplyRangeEnum) {
      if (value == this.basicInfoForm.applyRange) return
      this.$confirm('切换适用范围会导致配置清空，确认要切换适用范围吗？', '提示', {
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then(() => {
          this.clearData()
          this.basicInfoForm.applyRange = value
        })
        .catch((e) => {
          console.log('点击了取消', this.basicInfoForm.applyRange)
        })
    }

    // 首次开始学习时间校验
    validateFirstLearningStartDay(rule: any, value: any, callback: any) {
      if (
        !this.rulesInfoForm.firstLearningStartTime ||
        this.rulesInfoForm.firstLearningStartTime === 0 ||
        !this.rulesInfoForm.firstLearningEndTime ||
        this.rulesInfoForm.firstLearningEndTime === 0
      ) {
        return callback('请输入首次开始学习时间')
      }
      if (
        this.rulesInfoForm.firstLearningStartTime &&
        Number(this.rulesInfoForm.firstLearningStartTime) > Number(this.rulesInfoForm.firstLearningEndTime)
      ) {
        return callback('最晚开始学习时间不能早于最早开始学习时间')
      }
      return callback()
    }
    // 首次开始学习时间校验
    validateMinAdvanceDay(rule: any, value: any, callback: any) {
      if (
        !this.rulesInfoForm.minAdvanceDay ||
        this.rulesInfoForm.minAdvanceDay === 0 ||
        !this.rulesInfoForm.maxAdvanceDay ||
        this.rulesInfoForm.maxAdvanceDay === 0
      ) {
        return callback('请输入首次开始学习时间')
      }
      if (
        this.rulesInfoForm.minAdvanceDay &&
        Number(this.rulesInfoForm.minAdvanceDay) > Number(this.rulesInfoForm.maxAdvanceDay)
      ) {
        return callback('最晚开始学习时间不能早于最早开始学习时间')
      }
      return callback()
    }
    // 每天最多学习时长校验
    validateEveryDayMaxLearningHour(rule: any, value: any, callback: any) {
      if (!this.rulesInfoForm.maxLearningTime || !this.rulesInfoForm.learningTime) {
        return this.basicInfoForm.timeMode === this.timeModeEnum.physical
          ? callback('请输入每天最多学习时长')
          : callback('请输入每天最多学习学时')
      }
      if (Number(this.rulesInfoForm.maxLearningTime) < Number(this.rulesInfoForm.learningTime)) {
        return this.basicInfoForm.timeMode === this.timeModeEnum.physical
          ? callback('每天最多学习时间不能小于每次学习时长')
          : callback('每天最多学习学时不能小于每次学习学时')
      }
      if (
        this.basicInfoForm.timeMode === this.timeModeEnum.physical &&
        Number(this.rulesInfoForm.maxLearningTime) > 24
      ) {
        return callback('每天的学习时长不能超过1440分钟')
      }
      if (
        this.basicInfoForm.timeMode === this.timeModeEnum.learning &&
        Number(this.rulesInfoForm.maxLearningTime) > 32
      ) {
        return callback('一天学习时长不能超过32学时')
      }
      return callback()
    }
    // 校验失败后打开展开项
    openExpand() {
      this.$nextTick(() => {
        const mainElement = document.getElementById('mainContent')
        mainElement.scrollIntoView({ behavior: 'smooth', block: 'end', inline: 'nearest' })
        mainElement.scrollTop = mainElement.scrollHeight
      })
    }

    changeFirstLearningStartTime() {
      this.courseStudyRulesForm.validateField('firstLearningStartDay')
    }
    changeFirstLearningEndTime() {
      this.courseStudyRulesForm.validateField('firstLearningStartDay')
    }
    changeMaxLearningTime() {
      this.courseStudyRulesForm.validateField('everyDayMaxLearningHour')
    }
    changeLearningTime() {
      this.courseStudyRulesForm.validateField('everyDayMaxLearningHour')
    }
    /**
     * 时长方式改变
     */
    changeTimeMode(e: any) {
      this.courseStudyRulesForm.clearValidate('maxLearningTime')
      if (e === 1) {
        this.rulesInfoForm.maxLearningTime = 8
        this.rulesInfoForm.learningTime = 4
      } else {
        this.rulesInfoForm.maxLearningTime = 4
        this.rulesInfoForm.learningTime = 2
      }
    }
  }
</script>
<style lang="less" scoped>
  ::v-deep .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
    box-shadow: none;
  }
  .no-border {
    border-color: #dcdfe6;
  }
  ::v-deep input[type='number'] {
    -moz-appearance: textfield;
  }
  ::v-deep input[type='number']::-webkit-inner-spin-button,
  ::v-deep input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
</style>
