import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-role-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-role-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 创建角色
<AUTHOR>
 */
export class RoleCreateDto {
  /**
   * 角色id
   */
  id?: string
  /**
   * 角色名称
   */
  name?: string
  /**
   * 角色说明
   */
  description?: string
}

/**
 * 角色权限信息
<AUTHOR>
 */
export class RolePermissionDto {
  /**
   * 角色创建信息
   */
  roleMessage?: RoleCreateDto
  /**
   * 已选中节点的权限id集合[&quot;b9efcaa673a9e2251fa2b4c34b634893&quot;, &quot;c1b6824fa27a44f21467c544346b8fc1&quot;,…]
   */
  nodeSelectedIdArray?: Array<string>
  itemSelectedIdArray?: Array<string>
  rootSelectedIdArray?: Array<string>
}

/**
 * 角色信息
<AUTHOR>
 */
export class RoleDTO {
  /**
   * 角色ID
   */
  id: string
  /**
   * 角色级别值
   */
  levelValue: number
  /**
   * 角色名称
   */
  name: string
  /**
   * 角色描述
   */
  description: string
  /**
   * 创建方式 | 1:自建; 2:导入
   */
  createType: number
  /**
   * 数据类型 | 1:普通; 2:内置
   */
  dataType: number
  /**
   * 创建人ID
   */
  creatorId: string
  /**
   * 创建时间
   */
  createDate: string
  /**
   * 是否可用
   */
  available: boolean
}

/**
 * 角色修改信息
<AUTHOR>
 */
export class RoleEditDto {
  /**
   * 安全对象组集合
   */
  securityObjectGroupList: Array<SecurityObjectGroupDto>
  /**
   * 第三级安全对象id集合
旧平台的东西，不建议写死层级数，请使用RoleEditDto#securityObjectGroupList
   */
  nodeSelectedIdArray: Array<string>
  /**
   * 第二级安全对象id集合
旧平台的东西，不建议写死层级数，请使用RoleEditDto#securityObjectGroupList
   */
  itemSelectedIdArray: Array<string>
  /**
   * 第一级安全对象id集合
旧平台的东西，不建议写死层级数，请使用RoleEditDto#securityObjectGroupList
   */
  rootSelectedIdArray: Array<string>
}

/**
 * 安全对象组信息
<AUTHOR>
 */
export class SecurityObjectGroupDto {
  /**
   * 安全对象组id
   */
  id: string
  /**
   * 安全对象组名
   */
  name: string
  /**
   * URL内容
   */
  urlContent: string
  /**
   * url路径
   */
  url: string
  /**
   * 是否被选中
   */
  isSelected: boolean
  /**
   * 子安全对象组(嵌套)
   */
  children: Array<SecurityObjectGroupDto>
  /**
   * 父安全对象组id
   */
  parentId: string
  /**
   * 排序
   */
  sort: number
}

export class RoleDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<RoleDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取所有组安全对象
   * @return 所有组安全对象
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAllPermission(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAllPermission,
    operation?: string
  ): Promise<Response<Array<SecurityObjectGroupDto>>> {
    return commonRequestApi<Array<SecurityObjectGroupDto>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取系统的所有角色信息
   * @return 系统的所有角色信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAllRoles(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAllRoles,
    operation?: string
  ): Promise<Response<Array<RoleDTO>>> {
    return commonRequestApi<Array<RoleDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取当前角色安全对象
   * @return List<SecurityObjectGroupDto>
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCurrentUserSecurityGroup(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCurrentUserSecurityGroup,
    operation?: string
  ): Promise<Response<Array<SecurityObjectGroupDto>>> {
    return commonRequestApi<Array<SecurityObjectGroupDto>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取系统中所有可用的角色
   * @return 系统中所有可用的角色
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnabledRoles(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnabledRoles,
    operation?: string
  ): Promise<Response<Array<RoleDTO>>> {
    return commonRequestApi<Array<RoleDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据roleId获取安全对象组
   * @return 安全对象组
   * @param query 查询 graphql 语法文档
   * @param roleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPermissionByRoleId(
    roleId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPermissionByRoleId,
    operation?: string
  ): Promise<Response<Array<SecurityObjectGroupDto>>> {
    return commonRequestApi<Array<SecurityObjectGroupDto>>(
      SERVER_URL,
      {
        query: query,
        variables: { roleId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取安全对象--编辑角色
   * @param roleId 角色id
   * @return 编辑角色
   * @param query 查询 graphql 语法文档
   * @param roleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPermissionForEditRole(
    roleId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPermissionForEditRole,
    operation?: string
  ): Promise<Response<RoleEditDto>> {
    return commonRequestApi<RoleEditDto>(
      SERVER_URL,
      {
        query: query,
        variables: { roleId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据id获取角色
   * @return 角色信息
   * @param query 查询 graphql 语法文档
   * @param roleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRoleById(
    roleId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRoleById,
    operation?: string
  ): Promise<Response<RoleDTO>> {
    return commonRequestApi<RoleDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { roleId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 判断角色是否存在
   * @param check 0：忽略该字段|1：false
   * @param field 角色名称
   * @return 存在则返回false
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async isRoleExist(
    params: { check: number; field?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.isRoleExist,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取角色分页
   * @param page 分页信息
   * @return 角色分页
   * @param query 查询 graphql 语法文档
   * @param page 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageRolesByQuery(
    page: Page,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageRolesByQuery,
    operation?: string
  ): Promise<Response<RoleDTOPage>> {
    return commonRequestApi<RoleDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: { page },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 添加用户角色信息
   * @param accountId 账户id
   * @param roleIds   角色id集合
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addUserOwnRoles(
    params: { accountId: string; roleIds: Array<string> },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.addUserOwnRoles,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除角色
   * @param roleId 角色id
   * @param mutate 查询 graphql 语法文档
   * @param roleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteRole(
    roleId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteRole,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { roleId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPermission(
    params: {
      nodeSelectedIdArray?: Array<string>
      itemSelectedIdArray?: Array<string>
      rootSelectedIdArray?: Array<string>
    },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.getPermission,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 移除用户角色信息
   * @param accountId      账户id
   * @param accountRoleIds 账户角色关系id集合
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeUserOwnRoles(
    params: { accountId: string; accountRoleIds: Array<string> },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeUserOwnRoles,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存角色
   * @param roleDto 角色权限
   * @return boolean 保存结果
   * @param mutate 查询 graphql 语法文档
   * @param roleDto 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveRole(
    roleDto: RolePermissionDto,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveRole,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { roleDto },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
