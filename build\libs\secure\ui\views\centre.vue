<template>
  <el-container>
    <el-aside width="256px" style="background-color: black" class="pf-aside">
      <div>
        <div class="brand">
          菜单配置可视化工具
        </div>
      </div>
      <div class="secure-tree">
        <el-tree
          :filter-node-method="filterNode"
          :default-expand-all="true"
          :highlight-current="true"
          ref="tree"
          node-key="component"
          @node-click="nodeClick"
          :props="{label: label}"
          :data="SecureRouteInfo"
        ></el-tree>
      </div>
    </el-aside>
    <el-container>
      <el-header class="pf-header">
        <div class="pf-menu">
          <div>
            角色列表：
            <el-select
              placeholder="请选择角色"
              size="mini"
              v-model="currentRoleSelect"
              :clearable="true"
              :filterable="true"
              style="width: 400px"
            >
              <el-option
                :label="option.label"
                v-for="option in roleList"
                :key="option.code"
                :value="option.code"
              ></el-option>
            </el-select>
            <el-button
              icon="el-icon-s-promotion"
              type="text"
              style="margin-left: 20px"
              @click="$router.push('/compare')"
            >
              比对
            </el-button>
          </div>
        </div>

        <div class="header-tool">
          <i class="el-icon-search" @click="onSearchInterface = true"></i>
        </div>
      </el-header>

      <el-main>
        <el-row :gutter="20">
          <el-col :span="24">
            <!--            <el-alert type="warning" :closable="false" style="margin-bottom: 10px">-->
            <!--              描述：{{ currentActive.meta.description }}-->
            <!--            </el-alert>-->
            <el-alert type="warning" :closable="false" style="margin-bottom: 10px">
              路径：{{ currentActive.component }}
            </el-alert>
            <el-select
              placeholder="请选择角色"
              size="mini"
              multiple
              v-model="selectedToCopy"
              :clearable="true"
              :filterable="true"
              style="width: 400px"
            >
              <el-option
                :label="option.label"
                v-for="option in roleList"
                :key="option.code"
                :value="option.code"
              ></el-option>
            </el-select>

            <el-button type="primary" size="mini" style="margin-left: 20px" @click="copy">拷贝</el-button>
            <el-table
              style="margin-top: 10px"
              :data="currentPermissionMap"
              border
              ref="table"
              size="mini"
            >
              <el-table-column type="expand">
                <template slot-scope="{ row }" style="padding: 20px">
                  <div style="padding: 20px">
                    <el-tag
                      v-for="role in row.roles"
                      :key="role"
                      :closable="false"
                      size="mini"
                      style="margin-right: 10px; font-size: 12px;"
                    >
                      {{ role }}：{{ getRoleName(role) }}
                    </el-tag>
                    <el-table size="mini" border :data="row.graphqlList" style="width: 100%; margin-top: 10px;">
                      <el-table-column type="index" label="No." align="center" header-align="center"></el-table-column>
                      <el-table-column prop="name" label="名称"></el-table-column>
                      <el-table-column prop="name" label="微服务名称" width="350">
                        <template slot-scope="scope">{{ scope.row.extendInfo.serviceName }}</template>
                      </el-table-column>
                      <el-table-column
                        prop="name"
                        label="是否需要认证"
                        width="100"
                        align="center"
                        header-align="center"
                      >
                        <template slot-scope="scope">
                          {{ scope.row.extendInfo.authorizationRequired ? '是' : '否' }}
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </template>
              </el-table-column>
              <el-table-column type="index" width="50" align="center" label="No."></el-table-column>
              <el-table-column prop="name" label="名称" width="150"></el-table-column>
              <el-table-column prop="key"></el-table-column>
            </el-table>
          </el-col>
          <!--          <el-col :span="4">-->
          <!--            <el-card header="所属角色" shadow="never">-->
          <!--              <div-->
          <!--                v-for="(role, index) in activeRoles"-->
          <!--                :key="role.code"-->
          <!--                :style="index ? { borderTop: '1px dashed #d1d1d1', marginTop: '15px' }:{}"-->
          <!--                style="font-size: 15px; padding-top: 10px"-->
          <!--              >-->
          <!--                <div style="font-weight: bold">-->
          <!--                  {{ role.name }}-->
          <!--                </div>-->
          <!--                <div style="color: #d1d1d1; font-size: 12px; margin-top: 5px">-->
          <!--                  {{ role.code }}-->
          <!--                </div>-->
          <!--              </div>-->
          <!--            </el-card>-->
          <!--          </el-col>-->
        </el-row>
      </el-main>
    </el-container>
    <el-dialog
      title="快捷查询"
      :visible.sync="onSearchInterface"
      width="50%"
    >
      <el-alert type="warning" effect="dark">
        在这边根据接口名称查询的没查到数据说明需要去检查解析配置
      </el-alert>
      <div class="search-wrapper" style="margin-top: 10px">
        <div style="" class="search-box">
          <el-input
            v-model="interfaceName"
            prefix-icon="el-icon-search" placeholder="请输入菜单/接口名称"
          ></el-input>
          <div class="search-items">
            <div v-for="(item, index) in searchList" :key="index" class="search-item" @click="toTree(item)">
              <div class="index">
                {{ index + 1 }}
              </div>
              <div>
                <div style="font-weight: bold">
                  {{ item.meta.title }}
                </div>
                <div class="search-desc">{{ item.component }}</div>
              </div>
            </div>
            <el-empty v-if="!searchList.length"></el-empty>
          </div>
        </div>
      </div>
    </el-dialog>
  </el-container>
</template>

<style lang="scss" src="../index.scss"></style>

<script lang="ts">
  import SecureRouteInfo from '@hbfe/security-toolkit/src/.cache/ui-assigns.json'
  import { Component, Vue, Watch } from 'vue-property-decorator'
  import Roles from '@/models/RoleTypesDesc.json'
  import { ElTree } from 'element-ui/types/tree'

  const secureList = JSON.parse(JSON.stringify(SecureRouteInfo)).sort((a, b) => {
    return a.meta.sort - b.meta.sort
  })

  const list = []
  const createList = (itemList) => {
    itemList.forEach(item => {
      const graphqlList = new Set()
      Object.keys(item.meta.permissionMap).forEach(key => {
        item.meta.permissionMap[key].graphql.forEach(graphql => {
          graphqlList.add(graphql)
        })
      })
      item.graphqlList = graphqlList
      list.push(item)
      if (item.children) {
        createList(item.children)
      }
    })
  }

  createList(secureList)

  @Component
  export default class extends Vue {
    onSearchInterface = false
    currentRoleSelect = 'DQGLY'
    interfaceName = ''
    selectedToCopy = []
    roles = Roles

    getRoleName(name: string) {
      return this.roles[name]?.name || ''
    }

    @Watch('currentRoleSelect')
    currentChange() {
      const tree = this.$refs.tree as ElTree<any, any>
      tree.filter(this.currentRoleSelect)
    }

    mounted() {
      const tree = this.$refs.tree as ElTree<any, any>
      tree.filter(this.currentRoleSelect)
    }

    get SecureRouteInfo() {
      return secureList
    }

    get searchList() {
      if (!this.interfaceName) return list
      return list.filter(item => {
        return Array.from(item.graphqlList.values()).filter(subItem => subItem.includes(this.interfaceName)).length || item.meta.title.includes(this.interfaceName)
      })
    }

    roleList = Object.keys(Roles).map(key => {
      return {
        label: Roles[key].name,
        code: key
      }
    })

    getRoleList(selected: any) {
      return this.roleList.filter(item => {
        return !selected.includes(item.code)
      })
    }

    label(node) {
      return (node.meta.isMenu ? '' : '【非菜单】') + node.meta.title
    }

    filterNode(value, data) {
      if (!value) return true
      return data.meta.roles && data.meta.roles.length ? data.meta.roles.includes(value) : false
    }

    currentPermissionMap = []
    currentActive: any = {}

    nodeClick(data) {
      this.currentPermissionMap = []
      this.currentActive = data
      Object.keys(data.meta.permissionMap).forEach(key => {
        const getItem = data.meta.permissionMap[key]
        getItem.key = key
        getItem.requesting = false
        getItem.graphqlList = []

        getItem.graphql.forEach(graphql => {
          if (/:/.test(graphql)) {
            const index = graphql.indexOf(':')
            const gqlKey = graphql.substring(0, index)
            const extInfo = graphql.substring(index + 1, graphql.length)
            const theItem = {
              name: gqlKey,
              extendInfo: undefined
            }
            if (extInfo) {
              try {
                theItem.extendInfo = JSON.parse(extInfo)
              } catch (e) {

              }
            }
            getItem.graphqlList.push(theItem)
          } else {
            getItem.graphqlList.push({
              name: graphql
            })
          }
        })
        this.currentPermissionMap.push(
          getItem
        )
      })
    }

    expandAll() {
      const table = this.$refs.table as any
      table.$el.querySelector('.el-table__body-wrapper').querySelectorAll('.el-icon-arrow-right').forEach(el => {
        el.click()
      })
    }

    copy() {
      const items = {}
      this.currentPermissionMap.map(item => {
        items[item.key] = this.selectedToCopy
      })
      navigator.clipboard.writeText(`@RoleTypeDecorator(${JSON.stringify(items)})`.replace(/"/g, ''))
    }

    toTree(item: any) {
      const tree = this.$refs.tree as ElTree<any, any>
      tree.setCurrentKey(item.component)
      this.nodeClick(tree.getCurrentNode())
      this.onSearchInterface = false
    }
  }
</script>
