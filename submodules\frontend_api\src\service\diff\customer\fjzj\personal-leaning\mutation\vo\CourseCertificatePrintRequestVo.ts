import {
  CertificatePrintRequest,
  Extend,
  StudentPrintCertificateRequest
} from '@api/platform-gateway/platform-certificate-v1'
import { FileTypesEnum } from '@api/service/common/enums/personal-leaning/FileTypes'

export default class CourseCertificatePrintRequestVo {
  /**
   * 参训资格id
   */
  qualificationId = ''
  /**
   * 学号
   */
  studentNo = ''
  /**
   * 文件类型 PDF/IMAGE
   @see FileTypesEnum
   */
  fileType: FileTypesEnum = FileTypesEnum.IMAGE
  /**
   * 课程id
   */
  courseId = ''

  /**
   * 方案id
   */
  schemeId = ''

  //   模型转换
  to() {
    const params = new StudentPrintCertificateRequest()
    params.qualificationId = this.qualificationId
    params.studentNo = this.studentNo
    params.fileType = this.fileType
    params.schemeId = this.schemeId
    if (this.courseId) {
      const extend = new Extend()
      extend.key = 'courseId'
      extend.value = this.courseId
      params.data = [extend]
    }
    return params
  }
}
