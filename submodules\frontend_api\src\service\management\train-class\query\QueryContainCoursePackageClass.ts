import { ResponseStatus } from '@hbfe/common'
import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'

/**
 * 查询引用某个课程包的培训班列表对象
 */
class QueryContainCoursePackageClass {
  // region properties

  /**
   *pageNo，类型为number
   */
  pageNo = 0
  /**
   *pageSize，类型为number
   */
  pageSize = 0
  /**
   *总数目，类型为number
   */
  totalSize = 0
  /**
   *包id数组，类型为string[]
   */
  packageIds: string[] = []
  /**
   *培训班商品列表，类型为TrainClassCommodityVo[]
   */
  trainClassCommodityList: TrainClassCommodityVo[] = []
  // endregion
  // region methods

  /**
   * 查询引用某个课程包的培训班列表
   */
  async queryClasses(): Promise<ResponseStatus> {
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  // endregion
}
export default QueryContainCoursePackageClass
