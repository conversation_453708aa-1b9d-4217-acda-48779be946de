import { StudentCourse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

export class StudentCourseDetail {
  /**
   * 学员课程id
   */
  id: string
  /**
   * 学员课程状态(0：失效 1：有效)
   @see StudentCourseStatus
   */
  status: number
  /**
   * 学员课程学习状态（0：未评定 1：未合格 2：合格）
   @see StudentCourseLearningStatus
   */
  learningStatus: number
  /**
   * 选课时间
   */
  selectCourseTime: string
  /**
   * 课程学习取得结果时间
   */
  learningResultTime: string

  isQualified() {
    return this.learningStatus === 2
  }

  static from(course: StudentCourse) {
    const detail = new StudentCourseDetail()
    if (!course) {
      return detail
    }
    detail.id = course.studentCourseId
    detail.status = course.studentCourseStatus
    detail.learningStatus = course.courseLearningStatus
    detail.selectCourseTime = course.selectCourseTime
    detail.learningResultTime = course.learningResultTime
    return detail
  }
}

export default StudentCourseDetail
