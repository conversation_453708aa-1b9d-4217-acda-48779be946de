import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * 网校状态
 */
export enum OnlineSchoolNatureEnum {
  ALL = 'ALL',
  DEMO = 'DEMO',
  OFFICIAL = 'OFFICIAL'
}

class OnlineSchoolNature extends AbstractEnum<OnlineSchoolNatureEnum> {
  static enum = OnlineSchoolNatureEnum

  constructor(status?: OnlineSchoolNatureEnum) {
    super()
    this.current = status
    this.map.set(OnlineSchoolNatureEnum.ALL, '全部')
    this.map.set(OnlineSchoolNatureEnum.DEMO, 'DEMO')
    this.map.set(OnlineSchoolNatureEnum.OFFICIAL, '正式实施')
  }
}

export default OnlineSchoolNature
