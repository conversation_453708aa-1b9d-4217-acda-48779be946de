import { ExchangeOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import OrderCommodityVo from '@api/service/management/train-class/query/vo/OrderCommodityVo'
import { ExchangeTrainClassStatusEnum } from '@api/service/management/train-class/query/enum/ExchangeTrainClassStatusType'
import ExchangeOrderStatus from '@api/service/management/train-class/query/vo/ExchangeOrderStatus'
import { ExchangeOrderStatusEnum } from '@api/service/management/train-class/query/enum/ExchangeOrderStatusType'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * @description 换班记录列表内详情
 */
class ExchangeOrderRecordDetailVo {
  /**
   * 班级类型
   */
  type: TrainingModeEnum = undefined

  /**
   * 价格
   */
  price = 0
  /**
   * 原始商品信息
   */
  originalCommodity: OrderCommodityVo = new OrderCommodityVo()

  /**
   * 换货商品信息
   */
  exchangeCommodity: OrderCommodityVo = new OrderCommodityVo()

  /**
   * 换班状态
   * 1：全部
   * 2：换班中
   * 3：换班成功
   */
  exchangeTrainClassStatus: ExchangeTrainClassStatusEnum = null

  /**
   * 换货单状态列表，用于查看（换货）详情
   */
  exchangeOrderStatusList: ExchangeOrderStatus[]

  /**
   * 换班时间
   */
  exchangeTrainClassTime = ''

  /**
   * 操作人名称
   */
  operatorName = ''

  /**
   * 操作人id
   */
  operatorId = ''

  /**
   * 订单号
   */
  orderNo = ''

  /**
   * 子订单号
   */
  subOrderNo = ''

  /**
   * 能否继续换班
   */
  enableExchangeTrainClass = false

  /**
   * 远端数据
   */
  remote: ExchangeOrderResponse = new ExchangeOrderResponse()

  /**
   * 转换远端模型为本地vo模型
   */
  static async from(response: ExchangeOrderResponse): Promise<ExchangeOrderRecordDetailVo> {
    const detail: ExchangeOrderRecordDetailVo = new ExchangeOrderRecordDetailVo()
    // 走这个vo转化默认为网授
    detail.type = TrainingModeEnum.online
    detail.remote = response ?? new ExchangeOrderResponse()
    detail.price = response.subOrderInfo?.amount || 0
    detail.exchangeOrderStatusList = ExchangeOrderRecordDetailVo.getExchangeOrderStatusList(response)
    if (response.originalCommodity) {
      detail.originalCommodity = await OrderCommodityVo.getOrderCommodity(response.originalCommodity?.commoditySku)
    }
    if (response.exchangeCommodity) {
      detail.exchangeCommodity = await OrderCommodityVo.getOrderCommodity(response.exchangeCommodity?.commoditySku)
    }
    detail.exchangeTrainClassStatus = ExchangeOrderRecordDetailVo.getExchangeTrainClassStatus(response)
    detail.enableExchangeTrainClass = ExchangeOrderRecordDetailVo.getEnableExchangeTrainClass(response)
    detail.exchangeTrainClassTime = response.basicData?.statusChangeTime?.applied || ''
    detail.operatorId = response.basicData?.applyInfo?.applyUser?.userId || ''
    detail.subOrderNo = response.subOrderInfo?.subOrderNo || ''
    detail.orderNo = response.subOrderInfo?.orderInfo?.orderNo || ''
    return detail
  }

  /**
   * 获取换班状态
   */
  static getExchangeTrainClassStatus(response: ExchangeOrderResponse): ExchangeTrainClassStatusEnum | null {
    const responseStatus: number = response.basicData?.status
    // 换班中状态列表
    const exchangeProcessingStatusList: number[] = [0, 2, 3, 4, 5, 6]
    // 换货成功状态列表
    const completeExchangeStatusList: number[] = [7]
    if (exchangeProcessingStatusList.includes(responseStatus)) {
      return ExchangeTrainClassStatusEnum.Exchanging
    }
    if (completeExchangeStatusList.includes(responseStatus)) {
      return ExchangeTrainClassStatusEnum.Complete_Exchange
    }
    return null
  }

  /**
   * 获取是否支持继续换班
   */
  static getEnableExchangeTrainClass(response: ExchangeOrderResponse): boolean {
    const responseStatus: number = response.basicData?.status
    const enableOptionList = [3, 6] // 退货失败、发货失败
    if (enableOptionList.includes(responseStatus)) {
      return true
    } else {
      return false
    }
  }

  /**
   * 获取换货单详情列表
   */
  static getExchangeOrderStatusList(response: ExchangeOrderResponse): ExchangeOrderStatus[] {
    let result = [] as ExchangeOrderStatus[]
    const statusChangeTime = response.basicData?.statusChangeTime
    if (statusChangeTime) {
      /** 接口换货单状态 => UI页面换货状态 */
      const applyExchangeStatus = new ExchangeOrderStatus(ExchangeOrderStatusEnum.Apply_Exchange) // 发起换班
      const offShiftProcessStatus = new ExchangeOrderStatus(ExchangeOrderStatusEnum.Off_Shift_Processing) // 退班处理中
      const offShiftFailStatus = new ExchangeOrderStatus(ExchangeOrderStatusEnum.Off_Shift_Fail) // 退班失败
      const applyDeliveryStatus = new ExchangeOrderStatus(ExchangeOrderStatusEnum.Apply_Delivery) // 申请发货
      const deliveryProcessingStatus = new ExchangeOrderStatus(ExchangeOrderStatusEnum.Delivery_Processing) // 发货处理中
      const completeExchangeStatus = new ExchangeOrderStatus(ExchangeOrderStatusEnum.Complete_Exchange) // 换班成功
      // 换班失败选项
      const failOption: ExchangeOrderStatus[] = new Array<ExchangeOrderStatus>(
        applyExchangeStatus,
        offShiftProcessStatus,
        offShiftFailStatus
      )
      // 换班成功选项
      const successOption: ExchangeOrderStatus[] = new Array<ExchangeOrderStatus>(
        applyExchangeStatus,
        offShiftProcessStatus,
        applyDeliveryStatus,
        deliveryProcessingStatus,
        completeExchangeStatus
      )
      const statusMap: Map<ExchangeOrderStatusEnum, string> = new Map<ExchangeOrderStatusEnum, string>()
      statusMap
        .set(ExchangeOrderStatusEnum.Apply_Exchange, statusChangeTime.applied ?? '') // 发起换班 => 申请换货
        .set(ExchangeOrderStatusEnum.Off_Shift_Processing, statusChangeTime.returning ?? '') // 退班处理中 => 退货(处理)中
        .set(ExchangeOrderStatusEnum.Off_Shift_Fail, statusChangeTime.returnFailed ?? '') //  退班失败 => 退货失败
        .set(ExchangeOrderStatusEnum.Apply_Delivery, statusChangeTime.deliveryApplied ?? '') // 申请发货 => 申请发货
        .set(ExchangeOrderStatusEnum.Delivery_Processing, statusChangeTime.delivering ?? '') // 发货处理中 => 发货处理中
        .set(ExchangeOrderStatusEnum.Complete_Exchange, statusChangeTime.exchanged ?? '') // 换班成功 => 换货成功

      // 遍历数组赋值
      failOption.forEach((item) => {
        item.date = statusMap.get(item.status) ?? ''
      })
      successOption.forEach((item) => {
        item.date = statusMap.get(item.status) ?? ''
      })
      if (statusChangeTime.returnFailed) {
        result = failOption
      } else {
        result = successOption
      }
    }
    // 过滤掉没有时间的项
    result = result.filter((item) => item.date)
    console.log('result', result)
    return result
  }
}

export default ExchangeOrderRecordDetailVo
