import MutationRegisterUser from '@api/service/customer/user/mutation/MutationRegisterUser'
import CreateStudentRequestVo from '@api/service/diff/customer/gszj/user/mutation/vo/create/CreateStudentRequestVo'
import { ResponseStatus } from '@hbfe/common'
import MsAccountGateway from '@api/ms-gateway/ms-account-v1'
import basicDataDomain, { CreateStudentRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import QueryUserInfoDiff from '@api/service/diff/customer/gszj/user/QueryUserInfoDiff'
import PlatformTrainingChannelUser, {
  CreateStudentDifferentiationRequest
} from '@api/platform-gateway/platform-training-channel-user-v1'
import Context from '@api/service/common/context/Context'

/**
 * 学员账号注册
 */
class MutationRegisterUserDiff extends MutationRegisterUser {
  createStudentParams = new CreateStudentRequestVo()
  get areaCode() {
    return '620300'
  }

  /**
   * 注册
   * 200  成功
   * 402  核验异常
   * 400 token解析异常
   * 408 验证码过期，请重新获取验证码
   * 500 验证码错误，请重新输入
   * 100001（手机号码）、100002（身份证）  该证件号/手机号码已注册学员账号，请直接登录
   * @param params
   */
  async newRegistryDiff(paramsDiff: CreateStudentDifferentiationRequest) {
    if (QueryUserInfoDiff.getRegionIsDiff) {
      paramsDiff.areaCode = this.areaCode
      return await PlatformTrainingChannelUser.registerStudent(paramsDiff)
    }
    const params = Object.assign(new CreateStudentRequest(), paramsDiff)
    return await basicDataDomain.registerStudent(params)
  }

  async registryV2Diff(paramsDiff: CreateStudentDifferentiationRequest) {
    if (QueryUserInfoDiff.getRegionIsDiff) {
      paramsDiff.areaCode = this.areaCode
      return await PlatformTrainingChannelUser.registerStudentV2(paramsDiff)
    }
    const params = Object.assign(new CreateStudentRequest(), paramsDiff)
    return basicDataDomain.registerStudentV2(params)
  }
}
export default MutationRegisterUserDiff
