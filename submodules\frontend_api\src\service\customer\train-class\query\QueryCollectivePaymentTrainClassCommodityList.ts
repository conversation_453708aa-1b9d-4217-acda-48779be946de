import { ResponseStatus } from '@hbfe/common'
import CollectivePaymentSkuPropertyVo from '@api/service/customer/train-class/query/vo/CollectivePaymentSkuPropertyVo'
import CollectivePaymentTrainClassCommodityVo from '@api/service/customer/train-class/query/vo/CollectivePaymentTrainClassCommodityVo'
import SkuPropertyVo from '@api/service/customer/train-class/query/vo/SkuPropertyVo'

/**
 * 获取集体缴费培训班列表
 */
class QueryCollectivePaymentTrainClassCommodityList {
  // region properties

  /**
   *pageNo，类型为number
   */
  pageNo = 0
  /**
   *pageSize，类型为number
   */
  pageSize = 0
  /**
   *总数目，类型为number
   */
  totalSize = 0
  /**
   *sku过滤条件，类型为CollectivePaymentSkuPropertyVo
   */
  filterSkuVo = new CollectivePaymentSkuPropertyVo()
  /**
   *培训班商品列表，类型为CollectivePaymentTrainClassCommodityVo[]
   */
  trainClassCommodityList: CollectivePaymentTrainClassCommodityVo[] = []
  /**
   *刷选条件数组，类型为SkuPropertyVo
   */
  skuProperties = new SkuPropertyVo()
  // endregion
  // region methods

  /**
   * 获取培训班列表
   */
  async queryCollectivePaymentTrainClassCommodityList(): Promise<ResponseStatus> {
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 获取培训班属性
   */
  async querySku(): Promise<ResponseStatus> {
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  // endregion
}
export default QueryCollectivePaymentTrainClassCommodityList
