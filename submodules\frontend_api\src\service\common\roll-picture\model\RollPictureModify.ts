/**
 *
 *
 * @author: puxf
 * @date: 2021/2/4
 */
import { RollPictureDetail } from '@api/service/common/roll-picture/model/RollPictureDetail'
import { RollPictureRequest } from '@api/gateway/PlatformRollPicture'

export class RollPictureModify {
  /**
   * id
   */
  id?: string
  /**
   * 轮播图类型
   @see com.fjhb.btpx.platform.service.rollpicture.RollPictureTypeEnum
   */
  type: string
  /**
   * 轮播图附件地址
   */
  attachmentUrl: string
  /**
   * 链接地址
   */
  url: string
  /**
   * 轮播图描述
   */
  description?: string
  /**
   * 轮播图状态 0-停用 1-启用
   */
  status: number
  /**
   * 轮播图排序
   */
  sort?: number

  static fromDetail(data: RollPictureDetail): RollPictureModify {
    const model = new RollPictureModify()
    model.id = data.id
    model.type = data.type
    model.attachmentUrl = data.attachmentUrl
    model.url = data.url
    model.description = data.description
    model.status = data.status
    model.sort = data.sort
    return model
  }

  static toRemoteRequest(data: RollPictureModify): RollPictureRequest {
    const model = new RollPictureRequest()
    model.id = data.id
    model.type = data.type
    model.attachmentUrl = data.attachmentUrl
    model.url = data.url
    model.description = data.description
    model.status = data.status
    model.sort = data.sort
    return model
  }
}
