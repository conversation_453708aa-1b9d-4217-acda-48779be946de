import applyChooseCourse from './mutates/applyChooseCourse.graphql'
import applyCourseImmediatelyLearning from './mutates/applyCourseImmediatelyLearning.graphql'
import applyCourseLearning from './mutates/applyCourseLearning.graphql'
import applyCoursewareImmediatelyLearning from './mutates/applyCoursewareImmediatelyLearning.graphql'
import invalidStudentCourse from './mutates/invalidStudentCourse.graphql'
import pushUserCourse from './mutates/pushUserCourse.graphql'
import relearnStudentCourse from './mutates/relearnStudentCourse.graphql'
import sceneDelete from './mutates/sceneDelete.graphql'
import sceneOperate from './mutates/sceneOperate.graphql'
import sceneUpdate from './mutates/sceneUpdate.graphql'

export {
  applyChooseCourse,
  applyCourseImmediatelyLearning,
  applyCourseLearning,
  applyCoursewareImmediatelyLearning,
  invalidStudentCourse,
  pushUserCourse,
  relearnStudentCourse,
  sceneDelete,
  sceneOperate,
  sceneUpdate
}
