import QueryStudentLearningList from '@api/service/management/statisticalReport/query/QueryStudentLearningList'
import exportMsgateway from '@api/diff-gateway/platform-jxjypxtypt-byzj-school'
import UserModule from '@api/service/management/user/UserModule'
import {
  SyncResultEnmu,
  StudentSchemeLearningRequestVoDiff as StudentSchemeLearningRequestVo
} from '@api/service/diff/management/gszj/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
class ExportStatementsData extends QueryStudentLearningList {
  syncResult: Record<SyncResultEnmu, number> = {
    [SyncResultEnmu.DisableSynchronized]: -1,
    [SyncResultEnmu.Unsynchronized]: 0,
    [SyncResultEnmu.Synchronized]: 1,
    [SyncResultEnmu.SynchronizationFailure]: 2,
    [SyncResultEnmu.Waitsynchronized]: 3
  }
  /**
   * 导出报盘数据
   */
  async exportStatementsDiff(param: StudentSchemeLearningRequestVo) {
    try {
      param = await this.initExportParams(param)
      const res = await exportMsgateway.exportStudentCourseLearningQuotationInServicer(param)
      return res
    } catch (e) {
      console.log(
        '报错了，所处位置submodules/frontend_api/src/service/diff/management/byzj/learning-statistic/ExportStatementsData.ts，exportStatementsDiff',
        e
      )
    }
  }
}
export default ExportStatementsData
