import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 触发问卷环节
 */
export enum TriggerQuestionnaireProcessEnum {
  /**
   * 进入考试前
   */
  before_exam = 1,
  /**
   * 打印证明前
   */
  before_print = 2
}
class TriggerQuestionnaireProcess extends AbstractEnum<TriggerQuestionnaireProcessEnum> {
  static enum = TriggerQuestionnaireProcessEnum

  constructor(status?: TriggerQuestionnaireProcessEnum) {
    super()
    this.current = status
    this.map.set(TriggerQuestionnaireProcessEnum.before_exam, '进入考试前')
    this.map.set(TriggerQuestionnaireProcessEnum.before_print, '打印证明前')
  }
}
export default TriggerQuestionnaireProcess
