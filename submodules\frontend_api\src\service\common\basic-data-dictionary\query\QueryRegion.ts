import RegionTreeVo from './vo/RegionTreeVo'

import Basicdata, { RegionRequest, RegionResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import { RewriteGraph } from '../../utils/RewriteGraph'
import {
  listChildRegionInSubProject,
  listRegionByCodeInSubProject
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage/graphql-importer'
import AssembleTree from '../../utils/AssembleTree'
/**
 * 物理地区
 */
class QueryRegion {
  /**
   * 地区树
   */
  regionTree: Array<RegionTreeVo> = new Array<RegionTreeVo>()
  businessId = 'ADMINISTRATIVE_REGION'

  /**
   * 下级地区缓存
   */
  private childRegionCache = new Map<string, Array<RegionResponse>>()
  /**
   * 所有地区Code做Map映射
   */
  regionCodeMap = new Map<string, RegionTreeVo>()
  /**
   * 所有地区Name+parentId做Map映射
   * 地区名称转code使用
   */
  regionNameMap = new Map<string, RegionTreeVo>()
  /**
   * 查询网校下的物理地区（可能是省、市、区县、省直单位） --- 做查询使用的数据
   * @return {ResponseStatus}
   */
  async queryRegion() {
    if (!this.regionTree.length) {
      const result: any[] = []

      const level1 = await Basicdata.listChildRegionInSubProject({
        businessId: this.businessId,
        code: '0'
      })
      const code = level1.data.map(item => {
        result.push(item)
        return item.code
      })
      const rew = new RewriteGraph(Basicdata._commonQuery, listChildRegionInSubProject)
      const param2 = code.map(item => {
        return {
          businessId: this.businessId,
          code: item
        }
      })
      const rewResult = await rew.request(param2)
      const param3 = []
      for (const key in rewResult.data) {
        if (Object.prototype.hasOwnProperty.call(rewResult.data, key)) {
          const element = rewResult.data[key]
          const temp = element.map((item: any) => {
            return {
              businessId: this.businessId,
              code: item.code
            }
          })
          param3.push(...temp)
          result.push(...element)
        }
      }

      const rewResult3 = await rew.request(param3)
      for (const key in rewResult3.data) {
        if (Object.prototype.hasOwnProperty.call(rewResult3.data, key)) {
          const element = rewResult3.data[key]

          result.push(...element)
        }
      }
      result.forEach(item => {
        this.regionCodeMap.set(item.code, item)
        this.regionNameMap.set(item.name + item.parentCode, item)
        item.children = []
      })
      const tree = new AssembleTree<RegionTreeVo>(result, 'code', 'parentCode')
      this.regionTree = tree.assembleTree()
    }
    return this.regionTree
  }

  /**
   * 根据code查下级地区
   */
  async queryLowerLevelRegion(code: string): Promise<Array<RegionResponse>> {
    if (!this.childRegionCache.get(code)) {
      const { data } = await Basicdata.listChildRegionInSubProject({
        businessId: this.businessId,
        code: code
      })
      this.childRegionCache.set(code, data)
    }
    return this.childRegionCache.get(code)
  }
  /**
   * 根据code查地区信息 - 批量
   */
  async querRegionDetil(codes: string[]): Promise<Array<RegionTreeVo>> {
    const response = await Basicdata.listRegionByCodeInSubProject({
      businessId: this.businessId,
      codeList: codes
    })
    return response.data.map(item => RegionTreeVo.fromBusiness(item))
  }
  /**
   * 根据完整code查地区信息 - 批量
   */
  async querRegionDetilAll(codes: string[]) {
    const temp = codes.map(item => item.split('/').filter(i => i))

    const flatArr = [...new Set(temp.flat())]
    const response = await Basicdata.listRegionByCodeInSubProject({
      businessId: this.businessId,
      codeList: flatArr
    })
    const codeMap = new Map<string, RegionResponse>()
    response.data.forEach(item => codeMap.set(item.code, item))
    const allCodeMap = new Map<string, string>()
    temp.forEach(item => {
      let value = ''
      item.forEach(item => {
        const code = codeMap.get(item)?.name
        if (code) {
          value += code + '/'
        }
      })
      value = value.substr(0, value.length - 1)
      allCodeMap.set('/' + item.join('/'), value)
    })
    return allCodeMap
    // return response.data.map(item => RegionTreeVo.fromBusiness(item))
  }
}

export default new QueryRegion()
