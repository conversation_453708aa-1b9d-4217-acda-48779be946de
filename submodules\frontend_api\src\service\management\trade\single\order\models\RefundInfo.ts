import RefundCommodity from '@api/service/management/trade/single/order/models/RefundCommodity'
import { RefundWayEnum } from '@api/service/management/trade/single/order/enum/RefundWayEnum'

export default class RefundInfo {
  /**
   * 退款方式
   */
  refundWay: RefundWayEnum = undefined

  /**
   * 退款金额
   */
  refundPrice = 0

  /**
   * 退款说明
   */
  refundInfo = ''

  /**
   * 退款原因
   */
  refundReason = ''

  /**
   * 退款内容
   */
  refundCommodityList: Array<RefundCommodity> = new Array<RefundCommodity>()
}
