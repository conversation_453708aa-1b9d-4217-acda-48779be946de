/**
 * 用户考试申诉信息
 */
class ExamUserAppealModel {
  /**
   * 申诉ID
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 考试场次ID
   */
  examRoundId: string
  /**
   * 考试作答记录ID
   */
  examAnswerId: string
  /**
   * 申诉原因
   */
  reason: string
  /**
   * 申诉审批结果
   * 0:等待审批，1:审批通过，2:审批不通过
   */
  approvedResult: number
  /**
   * 申诉审批结果备注
   */
  approvedRemark: string
  /**
   * 审批时间
   */
  approvedTime: Date
  /**
   * 审批人ID
   */
  approvedUserId: string
  /**
   * 申诉创建时间
   */
  createTime: Date
  /**
   * 创建人ID
   */
  createUserId: string
}

export default ExamUserAppealModel
