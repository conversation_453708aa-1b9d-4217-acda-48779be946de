<route-meta>
  {
  "isMenu": true,
  "title": "业务咨询",
  "sort": 1,
  "icon": "icon_menhuxinxiguanli"
  }
</route-meta>
<script lang="ts">
  import Personal from '@hbfe/jxjy-admin-customerService/src/diff/fjzj/personal/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, FXS, GYS } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    query: [WXGLY, FXS, GYS],
    basicData: [WXGLY, FXS, GYS],
    studyDontent: [WXGLY, FXS, GYS],
    queryExchangeClass: [WXGLY, FXS, GYS],
    exchangeClass: [WXGLY, FXS, GYS],
    continueExchangeClass: [WXGLY, FXS, GYS],
    orderInfo: [WXGLY, FXS, GYS],
    invoiceInfo: [WXGLY, FXS, GYS],
    refundInfo: [WXGLY, FXS, GYS],
    studyRecords: [WXGLY, FXS, GYS],
    refundDetail: [WXGLY, FXS, GYS],
    approve: [WXGLY, FXS, GYS],
    retryRecycleRefund: [WXGLY, FXS, GYS],
    confirmRefund: [WXGLY, FXS, GYS],
    continueRefund: [WXGLY, FXS, GYS],
    refundOrder: [WXGLY, FXS, GYS],
    queryChangeClassDetail: [WXGLY, FXS, GYS],
    queryRelevancyOrder: [WXGLY, FXS, GYS],

    unbundle: [WXGLY, FXS, GYS],
    confirmResetPwd: [WXGLY, FXS, GYS],
    editPhone: [WXGLY, FXS, GYS],
    editName: [WXGLY, FXS, GYS],
    editIdCard: [WXGLY, FXS, GYS],
    editGender: [WXGLY, FXS, GYS],
    editRegionCode: [WXGLY, FXS, GYS],
    editCompanyName: [WXGLY, FXS, GYS],
    editCompany: [WXGLY, FXS, GYS],
    editProfessionalCategoryName: [WXGLY, FXS, GYS],
    editProfessionalQualification: [WXGLY, FXS, GYS],
    deleteAttachmentInfoItem: [WXGLY, FXS, GYS],
    updateAndEditCertifition: [WXGLY, FXS, GYS],

    editPersonnelCategoryName: [WXGLY, FXS, GYS],
    editPositionCategoryName: [WXGLY, FXS, GYS],
    editProfessionalLevelName: [WXGLY, FXS, GYS],
    editJobCategoryIdName: [WXGLY, FXS, GYS],
    editStudyperiodSubjectName: [WXGLY, FXS, GYS],

    invoiceAutomatic: [WXGLY, FXS, GYS],
    invoiceOffline: [WXGLY, FXS, GYS],
    electronicSpecialInvoice: [WXGLY, FXS, GYS],
    invoiceIncrement: [WXGLY, FXS, GYS],
    invoiceDistribution: [WXGLY, FXS, GYS],
    detail: [WXGLY, FXS, GYS],
    fillToInvoice: [WXGLY, FXS, GYS],
    studyQuery: [WXGLY, FXS, GYS],
    deleteCourse: [WXGLY, FXS, GYS],
    deleteExamRecord: [WXGLY, FXS, GYS],
    deleteExperience: [WXGLY, FXS, GYS],
    qualified: [WXGLY, FXS, GYS],
    studty: [WXGLY, FXS, GYS],
    lookCourseCertification: [WXGLY, FXS, GYS],

    deletePhoto: [WXGLY, FXS, GYS],
    revisePhotoTime: [WXGLY, FXS, GYS],
    toLog: [WXGLY, FXS, GYS],
    toLearningLog: [WXGLY],
    showIntelligentLearningTag: [WXGLY],
    //查看面网授班
    queryMixExchangeClass: [WXGLY, FXS, GYS],
    // 面网授班换班
    exchangeMixClass: [WXGLY, FXS, GYS],
    // 面网授班换期
    exchangeMixPeriod: [WXGLY, FXS, GYS],
    // 面网授班查看详情
    viewExchangeMixDetail: [WXGLY, FXS, GYS],
    // 面网授继续换班
    continueExchangeMixTrainClass: [WXGLY, FXS, GYS],
    // 面网授继续换期
    continueExchangePeriod: [WXGLY, FXS, GYS],
    // 学习情况组件（tab）
    learningSituation: [WXGLY, FXS, GYS],
    // 调查问卷组件（tab）
    surveyQuestionnaire: [WXGLY, FXS, GYS],
    // 考勤详情弹窗
    attendanceDetailsDrawer: [WXGLY, FXS, GYS],
    queryFaceExchangeClass: [WXGLY, FXS, GYS],
    exchangeFaceClass: [WXGLY, FXS, GYS],
    exchangeFacePeriod: [WXGLY, FXS, GYS],
    viewExchangeFacexDetail: [WXGLY, FXS, GYS],
    continueExchangeFaceTrainClass: [WXGLY, FXS, GYS]
  })
  export default class extends Personal {}
</script>
