import QuestionType from '@api/service/common/enums/question/QuestionType'
import UpdateQuestionVo from './UpdateQuestionVo'
import ChooseAnswerOptionVo from '../ChooseAnswerOptionVo'
import { UpdateMultipleQuestionRequest } from '@api/ms-gateway/ms-examquestion-v1'
import { MultipleQuestionResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
/*
 *多选题
 */
class UpdateMultipleQuestionVo extends UpdateQuestionVo {
  questionType = QuestionType.enum.multiple
  /**
   * 可选答案列表【必填】
   */
  answerOptions: Array<ChooseAnswerOptionVo> = new Array<ChooseAnswerOptionVo>()
  /**
   * 正确答案ID集合【必填】
   */
  correctAnswerIds: Array<string> = []

  from(data: MultipleQuestionResponse) {
    this.id = data.questionId
    this.topic = data.topic
    this.questionType = data.questionType
    this.libraryId = data.libraryInfo.libraryId
    this.dissects = data.dissects
    this.relateCourseId = data.relateCourseIds?.join('')
    this.questionDifficulty = data.questionDifficulty
    this.answerOptions = data.multipleAnswerOptions?.map(item => {
      return {
        id: item.id,
        content: item.content
      }
    })
    this.correctAnswerIds = data.correctAnswerIds?.map(id => {
      return id
    })
  }

  // 模型转换
  toDto() {
    const updateQuestionDto = new UpdateMultipleQuestionRequest()
    updateQuestionDto.id = this.id
    updateQuestionDto.topic = this.topic
    updateQuestionDto.questionType = this.questionType
    updateQuestionDto.libraryId = this.libraryId
    updateQuestionDto.dissects = this.dissects
    updateQuestionDto.relateCourseIds = [this.relateCourseId]
    updateQuestionDto.questionDifficulty = this.questionDifficulty
    updateQuestionDto.answerOptions = this.answerOptions?.map(item => {
      return {
        id: item.id,
        content: item.content
      }
    })
    updateQuestionDto.correctAnswerIds = this.correctAnswerIds?.map(id => {
      return id
    })
    return updateQuestionDto
  }
}

export default UpdateMultipleQuestionVo
