<template>
  <div ref="container" class="main-content" :style="style">
    <el-tooltip class="item" effect="dark" :content="tipContent" placement="top-start">
      <span>{{ content }}</span>
    </el-tooltip>
  </div>
</template>

<style lang="scss" scoped>
  .main-content {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>

<script>
  export default {
    props: {
      description: {
        type: String,
        default: ''
      },
      line: {
        type: Number,
        default: 3
      }
    },
    computed: {
      content() {
        return this.description.replace(/<[^>]+>/g, '').replace(/&(.*?;)/g, '')
      },
      tipContent() {
        if (this.content.length > 100) {
          return `${this.content.substr(0, 100)} ...`
        }
        return this.content
      },
      style() {
        return {
          '-webkit-line-clamp': this.line
        }
      }
    }
  }
</script>
