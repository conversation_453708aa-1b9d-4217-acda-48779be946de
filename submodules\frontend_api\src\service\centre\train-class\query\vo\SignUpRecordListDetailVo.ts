import UserInfoVo from '@api/service/centre/train-class/query/vo/UserInfoVo'
import SkuPropertyVo from '@api/service/centre/train-class/query/vo/SkuPropertyVo'
import { ExamResultStatusEnum } from '@api/service/centre/train-class/enum/ExamResultStatus'
import { StudentSchemeLearningResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import SkuPropertyConvertUtils from '@api/service/centre/train-class/util/SkuPropertyConvertUtils'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'

/**
 * @description 报名记录列表详情
 */

class SignUpRecordListDetailVo extends StudentSchemeLearningResponse {
  /**
   * 参训资格id
   */
  qualificationId = ''

  /**
   * 学号id
   */
  studentNo = ''

  /**
   * 培训方案id
   */
  schemeId = ''

  /**
   * 培训班商品id
   */
  commoditySkuId = ''

  /**
   * 学员信息
   */
  studentInfo: UserInfoVo = new UserInfoVo()

  /**
   * 培训方案名称
   */
  schemeName = ''

  /**
   * 培训方案类型 chooseCourseLearning: 选课规则 autonomousCourseLearning: 自主选课
   */
  schemeType = ''

  /**
   * 价格
   */
  price = 0

  /**
   * sku属性
   */
  skuProperty: SkuPropertyVo = new SkuPropertyVo()

  /**
   * 已报名学时
   */
  signUpPeriod = 0

  /**
   * 报名时间
   */
  signUpDate = ''

  /**
   * 已完成学时
   */
  completedPeriod = 0

  /**
   * 考试结果 1：已合格 2：未合格 3：无需考试
   */
  examResult: ExamResultStatusEnum = null

  /**
   * 是否完成培训
   */
  hasCompleteTraining: boolean = null

  /**
   * 合格时间（考核通过时间）
   */
  qualifiedDate = ''

  /**
   * 合格分数（最高成绩）
   */
  qualifiedScore = 0

  /**
   * 是否允许打印培训证明
   */
  enablePrintCertificate = false

  /**
   * 证书模板id
   */
  certificateTemplateId = ''

  /**
   * 是否配置课程学习
   */
  hasConfigCourseLearning = false

  static async from(response: StudentSchemeLearningResponse): Promise<SignUpRecordListDetailVo> {
    const detail = new SignUpRecordListDetailVo()
    Object.assign(detail, response)
    detail.studentInfo.userId = response.student?.userId ?? null
    detail.schemeId = response.scheme?.schemeId ?? null
    detail.schemeType = response.scheme?.schemeType ?? null
    // 报名时间
    detail.signUpDate = response.learningRegister?.registerTime ?? null
    try {
      // sku属性
      detail.skuProperty = await SkuPropertyConvertUtils.convertSkuPropertyToValueName(detail.scheme?.skuProperty)
    } catch (e) {
      console.error(e)
    }
    return detail
  }

  /**
   * 填充学员学习信息
   */
  static fillSchemeLearningInfo(detail: SignUpRecordListDetailVo) {
    /** 计算已完成学时 */
    const courseLearningResult = detail.studentLearning?.courseLearning?.userAssessResult[0]
    if (courseLearningResult) {
      const courseAssessResultJson = JSON.parse(courseLearningResult)
      if (detail.schemeType === 'chooseCourseLearning') {
        // 选课规则
        detail.completedPeriod =
          CalculatorObj.add(
            courseAssessResultJson.compulsoryRequirePeriod?.current ?? 0,
            courseAssessResultJson.electiveRequirePeriod?.current ?? 0
          ) ?? 0
      } else {
        // 自主选课
        detail.completedPeriod = courseAssessResultJson.requirePeriod?.current ?? 0
      }
    }

    /** 获取考试信息 */
    const examLearnRes = detail.studentLearning?.examLearning
    if (examLearnRes) {
      detail.examResult =
        examLearnRes.examAssessResult === 1 ? ExamResultStatusEnum.Qualified : ExamResultStatusEnum.Unqualified
      detail.qualifiedScore = examLearnRes.maxExamScore ?? null
    } else {
      // 无需考试
      detail.examResult = ExamResultStatusEnum.Innate
    }
    // 考核通过时间
    detail.qualifiedDate =
      detail.studentLearning.trainingResult === 1 ? detail.studentLearning?.trainingResultTime : null
    // 是否完成培训
    detail.hasCompleteTraining = detail.studentLearning?.trainingResult === 1 ? true : false
  }
}

export default SignUpRecordListDetailVo
