<template>
  <el-main>
    <el-card shadow="never" class="m-card is-bg is-overflow-hidden">
      <div class="f-plr20 f-pt20">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="姓名">
                <el-input v-model="input" clearable placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="证件号">
                <el-input v-model="input" clearable placeholder="请输入证件号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="手机号">
                <el-input v-model="input" clearable placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="订单号">
                <el-input v-model="input" clearable placeholder="请输入订单号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="4" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--人员信息-->
        <el-collapse v-model="activeNames5" @change="handleChange" class="m-collapse no-border">
          <el-collapse-item name="1">
            <div slot="title" class="m-tit">
              <span class="tit-txt">人员信息</span>
            </div>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="240" highlight-current-row class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="姓名" min-width="160" fixed="left">
                <template>张依依</template>
              </el-table-column>
              <el-table-column label="证件号" min-width="200">
                <template>354875965412365896</template>
              </el-table-column>
              <el-table-column label="手机号" min-width="200">
                <template>13003831002</template>
              </el-table-column>
              <el-table-column label="单位地区" min-width="200">
                <template>福建省-福州市-鼓楼区</template>
              </el-table-column>
              <el-table-column label="注册时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-border-top is-sticky">
      <el-tab-pane label="学员信息" name="first">
        <div class="f-p15">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="f-flex f-justify-between f-align-center">
              <span class="tit-txt">基础信息</span>
              <a class="f-cb"><span class="el-icon-edit-outline edit-icon f-mr5"></span>编辑</a>
            </div>
            <div class="f-plr45 f-pt20 f-pb10">
              <el-row :gutter="16">
                <el-form :inline="true" label-width="auto" class="m-text-form is-edit">
                  <el-col :sm="12" :md="8">
                    <el-form-item label="姓名：">
                      林依依
                      <!--                      <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                        <span class="el-icon-edit-outline f-c9 edit-icon"></span>
                        <div slot="content">编辑</div>
                      </el-tooltip>-->
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="证件号：">
                      356247854125896547
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="性别：">男</el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="手机号：">1574596321</el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="单位地区：">
                      福建省-福州市-鼓楼区
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="工作单位：">
                      福建华博教育科技股份有限公司
                      <!--                      <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                        <span class="el-icon-edit-outline f-c9 edit-icon"></span>
                        <div slot="content">编辑</div>
                      </el-tooltip>-->
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="注册时间：">2012-11-05 14:23:12</el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="注册来源：">主网站</el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
          </el-card>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">行业信息</span>
            </div>
            <div class="f-pb20 f-pt10">
              <div class="m-sub-tit is-border-bottom">
                <span class="tit-txt">人社行业</span>
              </div>
              <div class="f-plr40">
                <el-row :gutter="16">
                  <el-form :inline="true" label-width="auto" class="m-text-form is-edit f-mt20">
                    <el-col :sm="12" :md="8">
                      <el-form-item label="专业类别：" class="is-editing">
                        系列-具体专业
                        <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                          <span class="el-icon-edit-outline f-c9 edit-icon"></span>
                          <div slot="content">编辑</div>
                        </el-tooltip>
                        <div class="edit-box">
                          <el-cascader clearable :options="cascader" placeholder="请选择专业类别" class="f-flex-sub" />
                          <div class="op">
                            <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                              <span class="el-icon-circle-check f-cb edit-icon"></span>
                              <div slot="content">保存</div>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                              <span class="el-icon-circle-close f-c9 edit-icon"></span>
                              <div slot="content">取消</div>
                            </el-tooltip>
                          </div>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8">
                      <el-form-item label="职称等级：">
                        中级
                        <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                          <span class="el-icon-edit-outline f-c9 edit-icon"></span>
                          <div slot="content">编辑</div>
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
              </div>
              <div class="m-sub-tit is-border-bottom f-mt10">
                <span class="tit-txt">建设行业</span>
              </div>
              <div class="f-plr40">
                <el-button type="primary" icon="el-icon-plus" size="small" class="f-mt20">新增证书</el-button>
                <p class="f-cb f-mt20 f-f15">二级建造师</p>
                <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
                  <el-table-column label="专业" min-width="200" fixed="left">
                    <template>具体专业具体专业具体专业</template>
                  </el-table-column>
                  <el-table-column label="证书编号" width="200">
                    <template>201145874526</template>
                  </el-table-column>
                  <el-table-column label="证书发放日期-证书有效期" min-width="200">
                    <template>2020-11-11<span class="f-plr10">-</span>2020-11-11</template>
                  </el-table-column>
                  <el-table-column label="附件" width="400">
                    <template>
                      <ul class="m-certificate-img f-clear">
                        <li>
                          <el-image
                            src="/assets/images/web-default-banner.jpg"
                            :preview-src-list="['/assets/images/web-default-banner.jpg']"
                            class="img"
                          >
                          </el-image>
                        </li>
                        <li>
                          <el-image
                            src="/assets/images/web-default-banner.jpg"
                            :preview-src-list="['/assets/images/web-default-banner.jpg']"
                            class="img"
                          >
                          </el-image>
                        </li>
                        <li>
                          <el-image
                            src="/assets/images/web-default-banner.jpg"
                            :preview-src-list="['/assets/images/web-default-banner.jpg']"
                            class="img"
                          >
                          </el-image>
                        </li>
                        <li>
                          <el-image
                            src="/assets/images/web-default-banner.jpg"
                            :preview-src-list="['/assets/images/web-default-banner.jpg']"
                            class="img"
                          >
                          </el-image>
                        </li>
                        <li>
                          <el-image
                            src="/assets/images/web-default-banner.jpg"
                            :preview-src-list="['/assets/images/web-default-banner.jpg']"
                            class="img"
                          >
                          </el-image>
                        </li>
                        <li>
                          <el-image
                            src="/assets/images/web-default-banner.jpg"
                            :preview-src-list="['/assets/images/web-default-banner.jpg']"
                            class="img"
                          >
                          </el-image>
                        </li>
                      </ul>
                    </template>
                  </el-table-column>
                  <el-table-column label="编辑" min-width="100" align="center" fixed="right">
                    <template>
                      <el-button type="text" size="mini">编辑</el-button>
                      <el-button type="text" size="mini">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="m-sub-tit is-border-bottom">
                <span class="tit-txt">职业卫生行业</span>
              </div>
              <div class="f-plr40">
                <el-row :gutter="16">
                  <el-form :inline="true" label-width="auto" class="m-text-form is-edit f-mt20">
                    <el-col :sm="12" :md="8">
                      <el-form-item label="人员类别：" class="is-editing">
                        读取人员类别
                        <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                          <span class="el-icon-edit-outline f-c9 edit-icon"></span>
                          <div slot="content">编辑</div>
                        </el-tooltip>
                        <div class="edit-box">
                          <el-cascader clearable :options="cascader" placeholder="请选择专业类别" class="f-flex-sub" />
                          <div class="op">
                            <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                              <span class="el-icon-circle-check f-cb edit-icon"></span>
                              <div slot="content">保存</div>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                              <span class="el-icon-circle-close f-c9 edit-icon"></span>
                              <div slot="content">取消</div>
                            </el-tooltip>
                          </div>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8">
                      <el-form-item label="岗位类别：">
                        读取岗位类别
                        <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                          <span class="el-icon-edit-outline f-c9 edit-icon"></span>
                          <div slot="content">编辑</div>
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
              </div>
              <div class="m-sub-tit is-border-bottom">
                <span class="tit-txt">工勤行业</span>
              </div>
              <div class="f-plr40">
                <el-row :gutter="16">
                  <el-form :inline="true" label-width="auto" class="m-text-form is-edit f-mt20">
                    <el-col :sm="12" :md="8">
                      <el-form-item label="技术等级：" class="is-editing">
                        读取技术等级
                        <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                          <span class="el-icon-edit-outline f-c9 edit-icon"></span>
                          <div slot="content">编辑</div>
                        </el-tooltip>
                        <div class="edit-box">
                          <el-cascader clearable :options="cascader" placeholder="请选择专业类别" class="f-flex-sub" />
                          <div class="op">
                            <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                              <span class="el-icon-circle-check f-cb edit-icon"></span>
                              <div slot="content">保存</div>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                              <span class="el-icon-circle-close f-c9 edit-icon"></span>
                              <div slot="content">取消</div>
                            </el-tooltip>
                          </div>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8">
                      <el-form-item label="工种：">
                        读取工种
                        <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                          <span class="el-icon-edit-outline f-c9 edit-icon"></span>
                          <div slot="content">编辑</div>
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
              </div>
              <div class="m-sub-tit is-border-bottom">
                <span class="tit-txt">教师行业</span>
              </div>
              <div class="f-plr40">
                <el-row :gutter="16">
                  <el-form :inline="true" label-width="auto" class="m-text-form is-edit f-mt20">
                    <el-col :sm="12" :md="8">
                      <el-form-item label="学段学科：" class="is-editing">
                        读取学段-学科信息联动
                        <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                          <span class="el-icon-edit-outline f-c9 edit-icon"></span>
                          <div slot="content">编辑</div>
                        </el-tooltip>
                        <div class="edit-box">
                          <el-cascader clearable :options="cascader" placeholder="请选择学段学科" class="f-flex-sub" />
                          <div class="op">
                            <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                              <span class="el-icon-circle-check f-cb edit-icon"></span>
                              <div slot="content">保存</div>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                              <span class="el-icon-circle-close f-c9 edit-icon"></span>
                              <div slot="content">取消</div>
                            </el-tooltip>
                          </div>
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
              </div>
            </div>
          </el-card>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">微信相关</span>
            </div>
            <div class="f-pt10">
              <div class="f-plr40">
                <el-row :gutter="16">
                  <el-form :inline="true" label-width="auto" class="m-form f-mt10">
                    <el-col :sm="12" :md="8">
                      <el-form-item label="绑定微信：">是</el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8">
                      <el-form-item label="微信名称：">
                        林林一
                        <el-button type="primary" size="small" class="f-ml10">微信解绑</el-button>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
              </div>
            </div>
          </el-card>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">密码相关</span>
            </div>
            <div class="f-plr30 f-pt15 f-pb20">
              <el-button type="primary" size="small" class="f-ml10">重置密码</el-button>
              <span class="f-ml10 f-co">注：密码默认重置为abc123</span>
            </div>
          </el-card>
          <el-row :gutter="15" class="is-height">
            <el-col :md="8">
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div slot="header" class="">
                  <span class="tit-txt">人脸识别基准照</span>
                </div>
                <div class="f-p20 f-tc">
                  <div class="u-benchmark-photos-big">
                    <img class="photo" src="./assets/images/face-pic.jpg" alt="" />
                    <el-button type="danger" class="f-mt10">调整基准照片修改次数</el-button>
                    <el-button type="primary" class="f-mt10">删除基准照</el-button>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :md="16">
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div slot="header" class="">
                  <span class="tit-txt">基准照片调整记录</span>
                </div>
                <div class="f-p20">
                  <!--表格-->
                  <el-table stripe :data="tableData" max-height="500px" class="m-table">
                    <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                    <el-table-column label="操作时间" min-width="200">
                      <template>2017-12-12 17:23:23</template>
                    </el-table-column>
                    <el-table-column label="剩余次数" min-width="150">
                      <template>10</template>
                    </el-table-column>
                    <el-table-column label="基准照片" min-width="150" align="center" fixed="right">
                      <template>
                        <img src="./assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
                      </template>
                    </el-table-column>
                  </el-table>
                  <!--分页-->
                  <el-pagination
                    background
                    class="f-mt15 f-tr"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage4"
                    :page-sizes="[100, 200, 300, 400]"
                    :page-size="100"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="400"
                  >
                  </el-pagination>
                  <!--空数据-->
                  <div class="m-no-date f-mt50">
                    <img class="img" src="./assets/images/no-data-normal.png" alt="" />
                    <div class="date-bd">
                      <p class="f-f15 f-c9">暂无数据~</p>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane label="学习内容" name="second">
        <div class="f-p15">详见 2602_客服管理_业务咨询_学习内容.vue</div>
      </el-tab-pane>
      <el-tab-pane label="换班信息" name="third">
        <div class="f-p15">详见 2603_客服管理_业务咨询_换班信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="订单信息" name="fourth">
        <div class="f-p15">详见 2604_客服管理_业务咨询_订单信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="发票信息" name="five">
        <div class="f-p15">详见 2605_客服管理_业务咨询_发票信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="退款信息" name="six">
        <div class="f-p15">详见 2606_客服管理_业务咨询_退款信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="学习档案" name="seven">
        <div class="f-p15">详见 2607_客服管理_业务咨询_培训档案.vue</div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames5: ['1'],
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
