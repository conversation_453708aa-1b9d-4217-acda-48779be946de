import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 上传状态枚举
 */
export enum UploadStatusEnum {
  // 上传成功
  Success,
  // 上传失败
  Fail
}

/**
 * @description 上传状态列表
 */
class UploadStatusList extends AbstractEnum<UploadStatusEnum> {
  static enum = UploadStatusEnum
  constructor(status?: UploadStatusEnum) {
    super()
    this.current = status
    this.map.set(UploadStatusEnum.Success, '上传成功')
    this.map.set(UploadStatusEnum.Fail, '上传失败')
  }
}

export default new UploadStatusList()
