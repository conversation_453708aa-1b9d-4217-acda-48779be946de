/**
 * 循环计数器
 * @description 循环计数-用于创建方案时关键Id占位
 */
class LoopCount {
  // 期别学习方式-期别Id
  issueId = 1
  // 期别学习方式-期别考核配置id
  assessIdIA = 1
  // 期别学习方式-期别考核配置Id
  configIdTCC = 1
  // 问卷学习方式-问卷Id
  learningIdQL = 1
  // 问卷学习方式-问卷前置条件Id
  preconditionIdQLP = 1
  // 问卷学习方式-问卷考核配置Id
  assessIdQLA = 1
  // 问卷学习方式-问卷配置Id
  resourceIdQLR = 1
  // 教学计划学习方式-教学计划id-learningId
  learningIdTPL = 1
  // 教学计划学习方式-教学计划id-resourceId
  resourceIdTP = 1
  // 教学计划学习方式-教学计划配置Id（教学计划培训点ID）
  resourceIdTPR = 1
  // 教学计划学习方式-教学计划考核配置Id
  assessIdTPLA = 1

  // 方案-考核配置Id
  assessIdSCASS = 1
  // 期别学习方式-拓展属性-全期别唯一标识
  resourceIdUK = 1
  // 期别学习方式-期别考核项-学时成果id
  resultIdP = 1
  // 教学计划学习方式-教学计划组-教学计划id
  planIdPG = 1
  // 教学计划学习方式-教学计划组-教学计划项id
  resourceIdTPI = 1
}

export default LoopCount
