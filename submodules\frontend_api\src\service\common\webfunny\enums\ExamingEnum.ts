export enum ExamingEnum {
  /**
   * 进入(考试)
   */
  exam_enter = '进入考试',
  /**
   * 试题信息(考试)
   */
  exam_message = '保存考试试题信息上报',
  /**
   * 试卷信息(考试)
   */
  exam_paper_message = '提交试卷信息',
  /**
   * 进入(测验)
   */
  quiz_enter = '进入测验',
  /**
   * 试题信息(测验)
   */
  quiz_message = '保存测验试题信息上报',
  /**
   * 试卷信息(测验)
   */
  quiz_paper_message = '提交试卷信息',

  /**
   * 进入(练习)
   */
  practice_enter = '进入练习',
  /**
   * 试题信息(练习)
   */
  practice_message = '保存练习试题信息上报',
  /**
   * 试卷信息(练习)
   */
  practice_paper_message = '提交试题信息'
}
