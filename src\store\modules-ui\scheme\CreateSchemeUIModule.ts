import { getModule, Module, Action, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
/**
 * @description 创建培训方案UI数据持久层
 */
@Module({ namespaced: true, store, dynamic: true, name: 'CreateSchemeUIModule' })
class CreateSchemeUIModule extends VuexModule {
  /**
   * 培训方案类型，1-选课规则，2-自主选课，默认：0
   */
  schemeType = 0

  /**
   * 创建模式，1-创建，2-复制，3-编辑，默认：0
   */
  createMode = 0

  /**
   * 设置培训方案类型
   */
  @Action({ rawError: true })
  setSchemeType(type: number) {
    this.SET_SCHEME_TYPE(type)
  }

  /**
   * 设置创建模式
   */
  @Action({ rawError: true })
  setCreateMode(mode: number) {
    this.SET_CREATE_MODE(mode)
  }

  /**
   * 设置培训方案类型
   */
  @Mutation
  SET_SCHEME_TYPE(type: number) {
    this.schemeType = type
  }

  /**
   * 设置模式
   */
  @Mutation
  SET_CREATE_MODE(mode: number) {
    this.createMode = mode
  }
}

export default getModule(CreateSchemeUIModule)
