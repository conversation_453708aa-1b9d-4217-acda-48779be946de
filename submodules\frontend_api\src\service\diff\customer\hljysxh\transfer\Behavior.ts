import Jxjypxtypt, { EnterIndexResponse } from '@api/diff-gateway/platform-jxjypxtypt-hljysxh-school'

// import Jxjypxtypt from '@api/platform-gateway/platform-jxjypxtypt-ahzj-school'
import Response, { ResponseStatus } from '@api/Response'

class Behavior {
  /**
   * 循环次数
   */
  private count = 0
  /**
   * 循环上限次数
   */
  private readonly maxCount = 3
  /**
   * 每次循环毫秒数
   */
  private readonly loopMilliseconds = 500
  /**
   * 中转根据studentInfoKey换取信息
   */
  async enterIndex(studentInfoKey: string): Promise<Response<EnterIndexResponse>> {
    if (this.count === this.maxCount) {
      const response = new Response<EnterIndexResponse>()
      response.status = new ResponseStatus(200)
      response.data = new EnterIndexResponse()
      response.data.code = '500'
      return response
      // return new ResponseStatus(500)
    } else {
      const res = await Jxjypxtypt.enterIndexResponse({ studentInfoKey })
      if (res.data.message.includes('该学员创建中')) {
        this.count++
        await new Promise(resolve => setTimeout(resolve, this.loopMilliseconds))
        return await this.enterIndex(studentInfoKey)
      }
      return res
    }
  }
}
export default new Behavior()
