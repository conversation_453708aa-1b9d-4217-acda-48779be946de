import MsSchemeQueryFrontGatewayCourseLearningForestage, {
  SchemeConfigResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import MsTradeQueryFrontGatewayCourseLearningForestage, {
  CommoditySkuForestageResponse,
  SchemeResourceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import calculatorObj from '@api/service/customer/train-class/Utils/CalculatorObj'
import SkuPropertyConvertUtils, {
  ComplexSkuPropertyResponse
} from '@api/service/customer/train-class/Utils/SkuPropertyConvertUtils'
import TrainClassConfigJsonManager from '@api/service/customer/train-class/Utils/TrainClassConfigJsonManager'
import TrainClassDetailClassVo from '@api/service/customer/train-class/query/vo/TrainClassDetailClassVo'
import { ResponseStatus } from '@hbfe/common'
import ThematicMap from '@api/service/customer/thematic-config/ThematicMap'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import Context from '@api/service/common/context/Context'
import { DomainTypeEnum } from '@api/service/common/context/enums/DomainTypeEnum'
import FxnlQueryFrontGatewayForestage, {
  SchemeResourceInfoResponse
} from '@api/platform-gateway/fxnl-query-front-gateway-forestage'
import Scheme from '@api/service/common/scheme/model/schemeDto/Scheme'
import LearningResult from '@api/service/common/scheme/model/schemeDto/common/learning-results/LearningResult'
import LearningType from '@api/service/common/scheme/model/LearningType'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * 用户域获取培训班商品详情
 */
class QueryTrainClassDetail {
  // region properties

  /**
   *商品详情，类型为TrainClassDetailClassVo
   */
  trainClassDetail = new TrainClassDetailClassVo()
  /**
   *培训班商品id，类型为string
   */
  commodityId = ''
  /**
   * 门户商品 - 商品id
   */
  portalCommoditySkuId = ''
  /**
   * 方案配置信息
   */
  schemeConfig: any = ''
  // endregion
  // region methods
  /**
   * 是否分销操作
   */
  get isFxCustomer() {
    return Context.currentDomain == DomainTypeEnum.distribution
  }

  /**
   * 获取培训班详情 由详情和配置信息组合而成
   */
  async queryTrainClassDetail(trainingChannelId?: string, isShowAll?: boolean): Promise<ResponseStatus> {
    //获取班级详情
    let status = await this.requestClassDetail(trainingChannelId, isShowAll)
    if (status.isSuccess()) {
      //获取班级模板配置信息
      status = await this.requestClassConfig()
    }
    return status
  }

  async getSchemeConfigInServicer(schemeId: string) {
    const res = await MsSchemeQueryFrontGatewayCourseLearningForestage.getSchemeConfigInServicer({
      schemeId
    })
    if (res.status.isSuccess()) {
      const commodityDetail: SchemeConfigResponse = res.data
      const classConfigJson: any = TrainClassConfigJsonManager.calculatorSchemeConfigJson(commodityDetail.schemeConfig)
      return classConfigJson
    }
  }

  //获取班级模板配置信息
  protected async requestClassConfig() {
    //获取培训班配置模板jsonString
    const res = await MsSchemeQueryFrontGatewayCourseLearningForestage.getSchemeConfigInServicer({
      schemeId: this.trainClassDetail.schemeId
    })
    if (res.status.isSuccess()) {
      const trainClassDetail = this.trainClassDetail
      const commodityDetail: SchemeConfigResponse = res.data
      const classConfigJson: Scheme = TrainClassConfigJsonManager.calculatorSchemeConfigJson(
        commodityDetail.schemeConfig
      )
      if (classConfigJson) {
        this.schemeConfig = classConfigJson
        const { schemeAssessItem } = Scheme.parseTrainClassAssess(classConfigJson)
        const creditLearningResult = schemeAssessItem.learningResults.find(
          (learnResult: LearningResult) => learnResult.type == 1
        )
        this.trainClassDetail.period = creditLearningResult.grade || 0

        trainClassDetail.registerBeginDate = classConfigJson.registerBeginDate
        trainClassDetail.registerEndDate = classConfigJson.registerEndDate
        trainClassDetail.trainingBeginDate = classConfigJson.trainingBeginDate
        trainClassDetail.trainingEndDate = classConfigJson.trainingEndDate
        trainClassDetail.notice = classConfigJson.notice
        trainClassDetail.issueNotice = classConfigJson.issueNotice
        trainClassDetail.introContent = commodityDetail.intro
        const getExtendProperties = (key: string) =>
          classConfigJson.extendProperties.find((item: any) => item.name == key)?.value
        trainClassDetail.showNoticeDialog = getExtendProperties('showNoticeDialog') as boolean
        //获取培训成果中学分

        let courseRequirePeriod = 0
        let courseAssess
        let courseQuizEva
        let courseQuizConfig
        if (classConfigJson.type == 'chooseCourseLearning' && classConfigJson.chooseCourseLearning) {
          trainClassDetail.learningTypeModel.courseLearning.isSelected = true
          trainClassDetail.learningTypeModel.courseLearning.isAssess = true
          trainClassDetail.schemeType = 1
          courseAssess = classConfigJson.chooseCourseLearning.assessSetting
          courseRequirePeriod = calculatorObj.add(
            courseAssess.compulsoryRequirePeriod,
            courseAssess.electiveRequirePeriod
          )
          courseQuizEva = classConfigJson.chooseCourseLearning.config.courseCompleteEvaluateConfig
          courseQuizConfig = classConfigJson.chooseCourseLearning.config.courseQuizConfig
        } else if (classConfigJson.type == 'autonomousCourseLearning' && classConfigJson.autonomousCourseLearning) {
          trainClassDetail.learningTypeModel.courseLearning.isSelected = true
          trainClassDetail.learningTypeModel.courseLearning.isAssess = true
          trainClassDetail.schemeType = 2
          courseAssess = classConfigJson.autonomousCourseLearning.assessSetting
          courseRequirePeriod = courseAssess.requirePeriod
          courseQuizEva = classConfigJson.autonomousCourseLearning.config.courseCompleteEvaluateConfig
          courseQuizConfig = classConfigJson.autonomousCourseLearning.config.courseQuizConfig
        } else {
          trainClassDetail.schemeType =
            SchemeTypeEnum[classConfigJson.type as string] ?? SchemeTypeEnum.chooseCourseLearning
        }
        // 获取心得方案配置
        TrainClassConfigJsonManager.configExperience(this.trainClassDetail.learningTypeModel, classConfigJson)
        if (courseAssess) {
          trainClassDetail.trainClassConfig.courseRequirePeriod = courseRequirePeriod
        }
        if (courseQuizEva && courseQuizConfig) {
          trainClassDetail.trainClassConfig.incorporateCourseQuiz = courseQuizEva.courseQuizPagerStandard
          trainClassDetail.trainClassConfig.eachCourseQuizPassScore = courseQuizConfig.quizConfig.passScore
        }
        if (classConfigJson.examLearning) {
          trainClassDetail.learningTypeModel.exam.isSelected = true
          trainClassDetail.learningTypeModel.exam.isAssess = true
          trainClassDetail.trainClassConfig.examPassScore = classConfigJson.examLearning.config.qualifiedScore
          if (classConfigJson.examLearning.config.gradesWhetherHide === undefined) {
            trainClassDetail.learningTypeModel.exam.gradesWhetherHide = true
          } else {
            trainClassDetail.learningTypeModel.exam.gradesWhetherHide =
              classConfigJson.examLearning.config.gradesWhetherHide
          }
          trainClassDetail.learningTypeModel.exam.isExamAssessed = trainClassDetail.trainClassConfig.isExamAssessed =
            classConfigJson.examLearning.assessSetting && classConfigJson.examLearning.assessSetting.operation != 3
          trainClassDetail.learningTypeModel.exam.preCondition =
            classConfigJson.examLearning.precondition && classConfigJson.examLearning.precondition.operation != 3
              ? 1
              : 0
        }
        TrainClassConfigJsonManager.configCourseLearning(trainClassDetail.learningTypeModel, classConfigJson)
        // 期别与问卷
        const { issue, questionnaire } = LearningType.configIssueAndQuestionnaire(classConfigJson)
        trainClassDetail.learningTypeModel.issue = issue
        trainClassDetail.learningTypeModel.questionnaire = questionnaire
      }
    }
    return res.status
  }

  //获取班级详情
  protected async requestClassDetail(trainingChannelId?: string, isShowAll?: boolean) {
    let responseStatus = new ResponseStatus(200)
    if (trainingChannelId) {
      const res = await MsTradeQueryFrontGatewayCourseLearningForestage.getCommoditySkuTrainingChannelInServicer({
        commoditySkuId: this.commodityId,
        trainingChannelId: trainingChannelId
      })
      if (res.status.isSuccess()) {
        const commodityDetail: CommoditySkuForestageResponse = res.data
        await this.convertToTrainClassDetailClassVo(commodityDetail)
      }
      responseStatus = res.status
    } else {
      const res = await MsTradeQueryFrontGatewayCourseLearningForestage.getPortalCommoditySkuCustomerPurchaseInServicer(
        {
          portalCommoditySkuId: this.portalCommoditySkuId || this.commodityId,
          isShowAll
        }
      )
      if (res.status.isSuccess()) {
        const commodityDetail: CommoditySkuForestageResponse = res.data.originCommodityInfo
        if (res.data.portalCommoditySkuForestageResponse) {
          // 商品门户信息---待抽离方法
          this.trainClassDetail.portalCommoditySkuId = res.data.portalCommoditySkuForestageResponse.portalCommoditySkuId
          this.trainClassDetail.portalCommoditySkuSourceType =
            res.data.portalCommoditySkuForestageResponse.portalCommoditySkuSourceType
          this.trainClassDetail.portalCommoditySkuSourceId =
            res.data.portalCommoditySkuForestageResponse.portalCommoditySkuSourceId
          Object.assign(commodityDetail.skuProperty, res.data.portalCommoditySkuForestageResponse?.skuProperty)
          if (res.data.portalCommoditySkuForestageResponse.portalCommoditySkuSourceType == 2) {
            await ThematicMap.getThematicMap([res.data.portalCommoditySkuForestageResponse.portalCommoditySkuSourceId])
            this.trainClassDetail.specialType =
              ThematicMap.map.get(res.data.portalCommoditySkuForestageResponse.portalCommoditySkuSourceId)?.type || []
          }
        }
        if (commodityDetail) {
          await this.convertToTrainClassDetailClassVo(commodityDetail)
        }
        if (this.isFxCustomer) {
          const result = await FxnlQueryFrontGatewayForestage.getDistributorCommodityInDistributor({
            distributorCommodityId: this.commodityId
          })
          if (result.status.isSuccess()) {
            this.trainClassDetail.schemeId = (result.data.schemeResourceInfo as SchemeResourceInfoResponse).schemeId
            this.trainClassDetail.commoditySkuId = result.data.commodityId
            if (result.data?.propertyList?.length) {
              this.trainClassDetail.skuProperty.trainingMode.skuPropertyValueId = result.data.propertyList.find(
                it => it.propertyKey == 'trainingWay'
              )?.propertyValue as TrainingModeEnum
            }
          }
        }
      }
      responseStatus = res.status
    }
    return responseStatus
  }

  //转换成Vo对象
  protected async convertToTrainClassDetailClassVo(commodityDetail: CommoditySkuForestageResponse) {
    this.trainClassDetail.skuProperty = await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
      commodityDetail.skuProperty as ComplexSkuPropertyResponse
    )
    this.trainClassDetail.sourceId = commodityDetail?.possessionInfo?.sourceId
    this.trainClassDetail.sourceType = commodityDetail?.possessionInfo?.sourceType
    this.trainClassDetail.picturePath = commodityDetail?.commodityBasicData?.commodityPicturePath
    this.trainClassDetail.price = commodityDetail?.commodityBasicData?.price
    this.trainClassDetail.trainClassName = commodityDetail?.commodityBasicData?.saleTitle
    this.trainClassDetail.bought = commodityDetail?.possessionInfo?.possessing
    this.trainClassDetail.subOrderNo = commodityDetail?.possessionInfo?.sourceId
    this.trainClassDetail.schemeId = (commodityDetail.resource as SchemeResourceResponse)?.schemeId
    this.trainClassDetail.commoditySkuId = commodityDetail?.commoditySkuId
    this.trainClassDetail.isOnShelve = commodityDetail?.onShelve?.shelveStatus === 1
  }

  // endregion
}

export default QueryTrainClassDetail
