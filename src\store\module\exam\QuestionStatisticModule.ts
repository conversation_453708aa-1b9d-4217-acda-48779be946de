import PlatformExam, {
  QuestionAnswerCountDTO,
  QuestionCategoryCountDTO,
  QuestionCorrectRateDTO,
  QuestionStatisticParamDTO,
  UserQuestionAnswerCorrectRateDTO,
  UserQuestionAnswerCountDTO
} from '@api/gateway/PlatformExam'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { QuestionCategoryStatistic } from '@/store/module/exam/mode/question/QuestionCategoryStatistic'
import { DashboardParam } from '@/store/module/exam/mode/question-statistic/DashboardParam'
import { QuestionAnswerCount } from '@/store/module/exam/mode/question-statistic/QuestionAnswerCount'
import { DashboardRankParam } from '@/store/module/exam/mode/question-statistic/DashboardRankParam'
import * as graphqlImporter from './graphql-importer'
import SyllabusModule from '@api/service/customer/syllabus/SyllabusModule'

/**
 * 试题统计全局的state
 */
export interface IQuestionStatisticState {
  /**
   * 按专业分别统计试题类
   */
  majorQuestionStatisticList: Array<QuestionCategoryStatistic>
  /**
   * 试题类别统计 - dashboard
   * 根据不同的行业或者专业切换统计
   */
  questionCategoryStatistic: QuestionCategoryStatistic
  /**
   * 试题答题统计-按日期分组统计(近7天答题次数)
   */
  questionAnswerCountList: Array<QuestionAnswerCount>
  /**
   * 试题类别整体正确率
   * 累计答题次数
   * 试题作答整体正确率
   */
  questionCorrectRate: QuestionCorrectRateDTO
  /**
   * 试题答题次数排行榜
   */
  userQuestionAnswerCountRankList: Array<UserQuestionAnswerCountDTO>
  /**
   * 试题答题正确率排行榜
   */
  userQuestionAnswerCorrectRateRankList: Array<UserQuestionAnswerCorrectRateDTO>
}

@Module({ namespaced: true, store, dynamic: true, name: 'QuestionStatisticModule' })
class QuestionStatisticModule extends VuexModule implements IQuestionStatisticState {
  majorQuestionStatisticList: Array<QuestionCategoryStatistic> = new Array<QuestionCategoryStatistic>()
  questionAnswerCountList = new Array<QuestionAnswerCount>()
  userQuestionAnswerCountRankList = new Array<UserQuestionAnswerCountDTO>()
  questionCorrectRate = new QuestionCorrectRateDTO()
  userQuestionAnswerCorrectRateRankList = new Array<UserQuestionAnswerCorrectRateDTO>()
  questionCategoryStatistic = new QuestionCategoryStatistic()

  /**
   * 获取指定专业的试题统计
   */
  get getQuestionCategoryStatisticByMajorId() {
    return (majorId: string | undefined): QuestionCategoryStatistic | undefined => {
      if (!majorId) {
        return new QuestionCategoryStatistic()
      }
      let questionCategoryStatistic = this.majorQuestionStatisticList.find(item => item.majorId === majorId)
      if (!questionCategoryStatistic) {
        questionCategoryStatistic = new QuestionCategoryStatistic()
      }
      return questionCategoryStatistic
    }
  }

  /**
   * 统计指定专业下的各题类题数
   * @param majorId
   */
  @Action
  async statisticQuestionGroupByQuestionCategory(majorId: string) {
    if (this.majorQuestionStatisticList.find(item => item.majorId === majorId)) {
      return
    }
    const chapterList = SyllabusModule.getLeafSyllabusByMajorId(majorId)
    const tagIdList = chapterList.map(p => p.id)

    const questionStatisticParamDTO = new QuestionStatisticParamDTO()
    questionStatisticParamDTO.tagIdList = tagIdList
    questionStatisticParamDTO.enable = -1
    const response = await PlatformExam.statisticQuestionGroupByQuestionCategory(questionStatisticParamDTO)
    if (response.status.isSuccess()) {
      this.SET_MAJOR_QUESTION_STATISTIC_LIST({ questionCategoryCountDTO: response.data, majorId: majorId })
    } else {
      console.log('加载试题统计失败')
    }
  }

  /**
   * 管理后台首页统计合并请求
   * @param param
   * @param rankParam
   */
  @Action
  async statisticDashboardExamData(param: { param: DashboardParam; rankParam: DashboardRankParam }) {
    const questionCountGroupCategoryParam = new QuestionStatisticParamDTO()
    /*const chapterList = SyllabusModule.getLeafSyllabusByMajorIdAndIndustryId(
      param.param.examTypeId,
      param.param.professionId
    )
    questionCountGroupCategoryParam.tagIdList = chapterList.map(p => p.id)*/
    questionCountGroupCategoryParam.enable = -1

    const res = await PlatformExam._commonQuery<any>(graphqlImporter.statisticDashboardExamData, {
      param: param.param,
      rankParam: param.rankParam,
      questionCountGroupCategoryParam: questionCountGroupCategoryParam
    })
    if (!res.status.isSuccess()) {
      return res.status
    }
    // 近7天答题量
    this.SET_QUESTION_ANSWER_COUNT_LIST(res.data.questionAnswerCountGroupDateList)
    // 用户答题数量排行榜
    this.SET_USER_QUESTION_ANSWER_COUNT_RANK_LIST(res.data.userQuestionAnswerCountRankList)
    // 正确率排行榜
    this.SET_USER_QUESTION_ANSWER_CORRECT_COUNT_RANK_LIST(res.data.userQuestionCorrectRateRankList)
    // 试题类型统计
    this.SET_QUESTION_STATISTIC_OF_DASHBOARD(res.data.questionCountGroupCategory)
    // 各题类的正确率统计
    this.SET_QUESTION_CORRECT_RATE(res.data.questionCorrectRateStatistic)
  }

  @Mutation
  private SET_MAJOR_QUESTION_STATISTIC_LIST(result: {
    questionCategoryCountDTO: QuestionCategoryCountDTO
    majorId: string
  }) {
    const questionCategoryStatistic = new QuestionCategoryStatistic()
    questionCategoryStatistic.majorId = result.majorId
    questionCategoryStatistic.realCount = result.questionCategoryCountDTO.real
    questionCategoryStatistic.practiceCount = result.questionCategoryCountDTO.practice
    questionCategoryStatistic.simulationCount = result.questionCategoryCountDTO.simulation
    this.majorQuestionStatisticList.push(questionCategoryStatistic)
  }

  @Mutation
  private SET_QUESTION_ANSWER_COUNT_LIST(list: Array<QuestionAnswerCountDTO>) {
    this.questionAnswerCountList = new Array<QuestionAnswerCount>()
    list.map(p => {
      const item = new QuestionAnswerCount()
      Object.assign(item, p)
      this.questionAnswerCountList.push(item)
    })
  }

  @Mutation
  private SET_USER_QUESTION_ANSWER_COUNT_RANK_LIST(list: Array<UserQuestionAnswerCountDTO>) {
    this.userQuestionAnswerCountRankList = new Array<UserQuestionAnswerCountDTO>()
    list.map(p => {
      const item = new UserQuestionAnswerCountDTO()
      Object.assign(item, p)
      this.userQuestionAnswerCountRankList.push(item)
    })
  }

  @Mutation
  private SET_USER_QUESTION_ANSWER_CORRECT_COUNT_RANK_LIST(list: Array<UserQuestionAnswerCorrectRateDTO>) {
    this.userQuestionAnswerCorrectRateRankList = new Array<UserQuestionAnswerCorrectRateDTO>()
    list.map(p => {
      const item = new UserQuestionAnswerCorrectRateDTO()
      Object.assign(item, p)
      this.userQuestionAnswerCorrectRateRankList.push(item)
    })
  }

  @Mutation
  private SET_QUESTION_CORRECT_RATE(item: QuestionCorrectRateDTO) {
    this.questionCorrectRate = new QuestionCorrectRateDTO()
    Object.assign(this.questionCorrectRate, item)
  }

  @Mutation
  private SET_QUESTION_STATISTIC_OF_DASHBOARD(item: QuestionCategoryCountDTO) {
    this.questionCategoryStatistic = new QuestionCategoryStatistic()
    this.questionCategoryStatistic.realCount = item.real
    this.questionCategoryStatistic.practiceCount = item.practice
    this.questionCategoryStatistic.simulationCount = item.simulation
  }
}

export default getModule(QuestionStatisticModule)
