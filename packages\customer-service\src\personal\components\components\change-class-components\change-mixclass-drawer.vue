<template>
  <div class="pure">
    <el-drawer title="更换班级" :visible.sync="show" size="1600px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert">
          <span class="f-c9">当前更换的班级为：</span>
          <span class="f-fb">{{ schemeName }}</span>
          <span class="f-c9" v-if="isSpecial"
            >，当前订单来源于专题【{{
              specialName
            }}】，可更换班级在本专题范围内，请在以下列表中选择更换的目标班级！</span
          >
          <span class="f-c9" v-else>，请先在以下列表中选择更换的目标班级后，再选择目标期别：</span>
        </el-alert>
        <el-row :gutter="15" class="is-height">
          <el-col :span="12">
            <hb-search-wrapper @reset="resetCondition" class="m-query f-mt20">
              <el-form-item label="班级：">
                <el-input
                  v-model="queryParams.schemeName"
                  clearable
                  placeholder="请输入班级名称"
                  @clear="queryParams.schemeName = ''"
                />
              </el-form-item>
              <el-form-item label="年度：">
                <biz-year-select v-model="localSkuProperty.year" placeholder="请选择培训年度"></biz-year-select>
              </el-form-item>
              <el-form-item label="地区：">
                <biz-national-region
                  v-model="localSkuProperty.region"
                  :check-strictly="true"
                  placeholder="请选择地区"
                ></biz-national-region>
              </el-form-item>
              <el-form-item label="行业：">
                <biz-industry-select
                  v-model="localSkuProperty.industry"
                  @clearIndustrySelect="handleClearIndustrySelect"
                  @industryPropertyId="handleIndustryPropertyId"
                  @industryInfos="handleIndustryInfos"
                  placeholder="请选择行业"
                >
                </biz-industry-select>
              </el-form-item>
              <template>
                <el-form-item label="学段：" v-if="isLs">
                  <biz-study-period
                    v-model="localSkuProperty.studyPeriodId"
                    :industry-id="localSkuProperty.industry"
                    :industry-property-id="industryPropertyId"
                    @updateStudyPeriod="updateStudyPeriod"
                    @clearSubject="clearSubject"
                  ></biz-study-period>
                </el-form-item>
                <el-form-item label="学科：" v-if="isLs">
                  <biz-subject
                    v-model="localSkuProperty.subjectId"
                    :industry-property-id="industryPropertyId"
                    :study-period-id="studyPeriodId"
                    @updateSubject="updateSubject"
                  ></biz-subject>
                </el-form-item>
              </template>
              <el-form-item label="科目类型：" v-if="isRs || isJs || isYs">
                <biz-accounttype-select
                  v-model="localSkuProperty.subjectType"
                  :industry-property-id="industryPropertyId"
                  :industryId="localSkuProperty.industry"
                  placeholder="请选择科目类型"
                ></biz-accounttype-select>
              </el-form-item>
              <el-form-item label="培训专业：" v-if="isRs">
                <biz-major-cascader
                  v-model="localSkuProperty.societyTrainingMajor"
                  :industry-property-id="industryPropertyId"
                  :industryId="localSkuProperty.industry"
                  placeholder="请选择培训专业"
                >
                </biz-major-cascader>
              </el-form-item>
              <el-form-item label="培训类别：" v-if="isWs || isJs">
                <biz-training-category-select
                  v-model="localSkuProperty.trainingCategory"
                  :industry-property-id="industryPropertyId"
                  :industryId="localSkuProperty.industry"
                  @updateTrainingCategory="handleUpdateTrainingCategory"
                  placeholder="请选择培训类别"
                ></biz-training-category-select>
              </el-form-item>
              <el-form-item label="培训专业：" v-if="isJs">
                <biz-major-select
                  v-model="localSkuProperty.constructionTrainingMajor"
                  :industry-property-id="industryPropertyId"
                  :training-category-id="trainingCategoryId"
                  placeholder="请选择培训专业"
                ></biz-major-select>
              </el-form-item>
              <template v-if="isWs">
                <el-form-item label="培训对象：">
                  <biz-training-object-select
                    v-model="localSkuProperty.trainingObject"
                    placeholder="请选择培训对象"
                    :industry-property-id="industryPropertyId"
                    :industry-id="localSkuProperty.industry"
                    @updateTrainingCategory="updateTrainingCategory"
                  />
                </el-form-item>

                <el-form-item label="岗位类别：">
                  <biz-obj-category-select
                    v-model="localSkuProperty.positionCategory"
                    placeholder="请选择岗位类别"
                    :industry-property-id="industryPropertyId"
                    :industryId="localSkuProperty.industry"
                    :training-object-id="trainingObjectId"
                  />
                </el-form-item>
              </template>
              <el-form-item label="技术等级：" v-if="isGq">
                <biz-technical-grade-select
                  v-model="localSkuProperty.jobLevel"
                  :industry-id="localSkuProperty.industry"
                  :industry-property-id="industryPropertyId"
                ></biz-technical-grade-select>
              </el-form-item>
              <el-form-item label="执业类别：" v-if="isYs">
                <biz-practicing-category-cascader
                  v-model="localSkuProperty.pharmacistIndustry"
                  :industryId="localSkuProperty.industry"
                ></biz-practicing-category-cascader>
              </el-form-item>
              <template slot="actions">
                <el-button type="primary" @click="searchBase">查询</el-button>
              </template>
            </hb-search-wrapper>
            <!--表格-->
            <el-table
              stripe
              :data="replaceableTrainClassList"
              ref="replaceableTrainClassListRef"
              max-height="500px"
              class="m-table"
              v-loading="query.loading"
            >
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="可更换的目标培训班" min-width="300">
                <template slot-scope="scope">
                  <p>
                    <el-tag type="primary" effect="dark" size="mini">{{ schemeType(scope.row) }}</el-tag
                    >{{ scope.row.schemeName }}
                  </p>
                  <p class="f-c9 f-f13">
                    {{ getSchemeProperty(scope.row.skuValueNameProperty) }}
                    <!-- {{ scope.row.skuValueNameProperty.industry.skuPropertyName }} /
                {{ scope.row.skuValueNameProperty.year.skuPropertyName }}年 /
                {{ scope.row.skuValueNameProperty.subjectType.skuPropertyName }} -->
                    <!-- 科目 -->
                  </p>
                </template>
              </el-table-column>
              <el-table-column label="单价(元)" min-width="140" align="right">
                <template slot-scope="scope">{{ scope.row.price }}</template>
              </el-table-column>
              <el-table-column label="学时" min-width="100" align="center">
                <template slot-scope="scope">{{ scope.row.period }}</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button type="text" v-if="isChooseClass(scope.row)">已选择</el-button>
                  <el-button type="text" v-else @click="chooseClass(scope.row)">选择</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <hb-pagination :page="pageClass" v-bind="pageClass"> </hb-pagination>
          </el-col>

          <el-col :span="12">
            <el-card shadow="never" class="m-card is-header-sticky f-mb15">
              <div slot="header" class="">
                <span class="tit-txt">选择期别</span>
              </div>
              <div class="f-plr20 f-pt20">
                <!--第二步：选期别-->
                <el-row :gutter="16" class="m-query">
                  <el-form :inline="true" label-width="auto">
                    <el-col :span="24">
                      <el-col :span="16">
                        <el-form-item label="期别名称：">
                          <el-input placeholder="请输入期别名称" v-model="periodQuery.issueName"></el-input>
                        </el-form-item>
                      </el-col>
                    </el-col>
                    <el-col :span="24">
                      <el-col :span="16">
                        <el-form-item label="报名时间：">
                          <double-date-picker
                            :begin-create-time.sync="periodQuery.registerStartTimeScope"
                            :end-create-time.sync="periodQuery.registerEndTime"
                            :isLimitEndTime="false"
                            beginTimePlaceholder="请选择报名成功时间"
                            endTimePlaceholder="请选择报名成功时间"
                            :isLimitEndTimeSecond="isLimitEndTimeSecond"
                          ></double-date-picker>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8" class="f-fr">
                        <el-form-item class="f-tr">
                          <el-button type="primary" @click="fillBackChooseClass">查询</el-button>
                        </el-form-item>
                      </el-col>
                    </el-col>
                  </el-form>
                </el-row>
                <!--表格-->
                <el-table stripe :data="periodList" max-height="500px" class="m-table" v-loading="periodLoading">
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="可更换的期别" min-width="300">
                    <template #default="scope">
                      {{ scope.row.issueName }}
                      <el-tag type="primary" effect="dark" size="mini">{{ scope.row.issueNo }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="报名时间" min-width="180" align="right">
                    <template #default="scope"
                      >{{ scope.row.registerStartTime || '-' }}{{ scope.row.registerEndTime || '-' }}</template
                    >
                  </el-table-column>
                  <el-table-column label="剩余可报名人数" min-width="100" align="center">
                    <template #default="scope">{{ scope.row.canRegisterNum }}</template>
                  </el-table-column>
                  <el-table-column label="操作" width="100" align="center" fixed="right">
                    <template #default="scope">
                      <div>
                        <el-button type="text" @click="confirmExchange(scope.row)">确认更换</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <hb-pagination :page="pagePeriod" v-bind="pagePeriod"> </hb-pagination>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch, PropSync } from 'vue-property-decorator'
  import { MutationCreateExchangeOrder } from '@api/service/management/trade/single/order/mutation/MutationCreateExchangeOrder'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { Query, UiPage } from '@hbfe/common'
  import QueryReplaceableTrainClassListVo from '@api/service/management/train-class/query/vo/QueryReplaceableTrainClassListVo'
  import ReplaceableTrainClassDetailVo from '@api/service/management/train-class/query/vo/ReplaceableTrainClassDetailVo'
  import QueryExchangeTrainClass from '@api/service/management/train-class/query/QueryExchangeTrainClass'
  import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
  import { bind, debounce } from 'lodash-decorators'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import TrainClassSchemeType, {
    TrainClassSchemeEnum
  } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
  import BizYearSelect from '@hbfe/jxjy-admin-components/src/biz/biz-year-select.vue'
  import BizNationalRegion from '@hbfe/jxjy-admin-components/src/biz/biz-national-region.vue'
  import BizIndustrySelect from '@hbfe/jxjy-admin-components/src/biz/biz-industry-select.vue'
  import BizAccounttypeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-accounttype-select.vue'
  import BizMajorCascader from '@hbfe/jxjy-admin-components/src/biz/biz-major-cascader.vue'
  import BizTrainingCategorySelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-category-select.vue'
  import BizMajorSelect from '@hbfe/jxjy-admin-components/src/biz/biz-major-select.vue'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import CreateExchangeOrderModel from '@hbfe/jxjy-admin-customerService/src/personal/components/model/CreateExchangeOrderModel'
  import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
  import SchemeType, { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-scheme/src/models/SchemeSkuProperty'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import BizTechnicalGradeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-technical-grade-select.vue'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/news-date-picker.vue'
  import ChangeIssueItem from '@api/service/management/train-class/offlinePart/model/ChangeIssueItem'
  import QueryCanChangeIssueParam from '@api/service/management/train-class/offlinePart/model/QueryCanChangeIssueParam'
  import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import { ElTable } from 'element-ui/types/table'
  import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
  @Component({
    components: {
      BizTechnicalGradeSelect,
      BizMajorSelect,
      BizTrainingCategorySelect,
      BizMajorCascader,
      BizAccounttypeSelect,
      BizIndustrySelect,
      BizNationalRegion,
      BizYearSelect,
      DoubleDatePicker
    }
  })
  export default class extends Vue {
    @PropSync('visible', { type: Boolean }) show!: boolean
    @Ref('BizAccountTypeSelectRef') BizAccountTypeSelectRef: BizAccounttypeSelect
    @Ref('replaceableTrainClassListRef') replaceableTrainClassListRef: ElTable
    // 查询接口入口
    queryRequestEntrance: QueryExchangeTrainClass =
      TrainClassManagerModule.queryTrainClassFactory.getQueryExchangeTrainClass()

    // 业务接口入口（发起换货）
    mutationRequestEntrance: MutationCreateExchangeOrder =
      TradeModule.singleTradeBatchFactor.mutationFactory.getMutationCreateExchangeOrder()

    // 分页 - 可换班列表
    pageClass: UiPage
    // 分页 - 可换期别
    pagePeriod: UiPage
    // 查询 - 可换班列表
    query: Query = new Query()
    // 查询参数 - 可换班列表
    queryParams: QueryReplaceableTrainClassListVo = new QueryReplaceableTrainClassListVo()
    // 列表 - 可换班列表
    replaceableTrainClassList: ReplaceableTrainClassDetailVo[] = []
    /**
     * 培训对象Id
     */
    trainingObjectId = ''
    // 培训方案类型列表
    trainClassSchemeTypeList: EnumOption<TrainClassSchemeEnum>[] = TrainClassSchemeType.list()

    // 剔除培训班商品id集合
    excludeCommoditySkuIdList: string[] = []
    /**
     * 剔除的期别id
     */
    excludedIssueIdList = new Array<string>()
    /**
     * 【培训班列表】价格
     */
    price: number = null

    /**
     * 培训方案名称
     */
    schemeName = ''
    studyPeriodId = ''
    //region sku

    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuProperty = {
      /**
       * 年度
       */
      year: '',
      /**
       * 地区
       */
      region: [] as string[],
      /**
       * 行业
       */
      industry: '',
      /**
       * 科目类型
       */
      subjectType: '',
      /**
       * 培训类别
       */
      trainingCategory: '',
      /**
       * 培训专业 - 建设行业
       */
      constructionTrainingMajor: '',
      /**
       * 培训专业 - 人社行业
       */
      societyTrainingMajor: [] as string[],
      /**
       * 技术等级 - 工勤行业
       **/
      jobLevel: '',
      /**
       * 培训对象
       */
      trainingObject: '',
      /**
       * 岗位类别
       */
      positionCategory: '',
      /**
       * 学段id
       */
      studyPeriodId: '',
      /**
       * 学科id
       */
      subjectId: '',
      /**
       * 药师id
       */
      pharmacistIndustry: [] as string[]
    } as SchemeSkuProperty

    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''

    /**
     * 培训类别Id
     */
    trainingCategoryId = ''

    /**
     * 是否是专题
     */
    isSpecial = false
    // 是否隐藏专业
    isHidden = false
    /**
     * 专题名称
     */
    specialName = ''
    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: false,
      // 培训类别
      trainingCategory: false,
      // 技术等级
      jobLevel: true,
      // 培训对象
      trainingObject: true,
      //   岗位类别
      positionCategory: true,
      //执业类别
      occupationalCategory: true
    }
    /**
     * 当前网校信息
     */
    envConfig = {
      // 工勤行业
      workServiceId: '',
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 职业卫生行业Id
      occupationalHealthId: '',
      //教师行业id
      teacherIndustryId: '',
      //药师行业id
      yshyId: ''
    }

    /**
     * 专题id
     */
    trainingChannelId = ''
    /**
     * 分销商id
     */
    distributorId = ''

    //endregion
    value1 = ''
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    industryId = ''
    /**
     * 当前选中的班级
     */
    currentChooseClass = new ReplaceableTrainClassDetailVo()
    /**
     * 期别列表
     */
    periodList = new Array<ChangeIssueItem>()
    /**
     * 期别列表查询参数
     */
    periodQuery = new QueryCanChangeIssueParam()
    /**
     * 期别列表loading
     */
    periodLoading = false
    /**
     * 换班的机构id
     */
    unitId = ''
    /**
     * 收款主体id
     */
    receiveUnitId = ''
    // 打开换班弹窗的班级类型
    openDrawerSchemeType = ''
    /**
     * 默认结束时分秒
     */
    isLimitEndTimeSecond = '23:59:59'
    /**
     * 培训方案类型
     */
    get schemeType() {
      return (item: ReplaceableTrainClassDetailVo) => {
        // if (this.openDrawerSchemeType === 'mix') {
        return SchemeType.getNewSchemeType(item, false)
        // }
        // if (this.openDrawerSchemeType === 'face') {
        //   console.log(1111)
        //   return SchemeType.getFaceSchemeType(item.schemeType)
        // }
      }
    }
    /**
     * 是否选择了班级
     */
    get isChooseClass() {
      return (item: ReplaceableTrainClassDetailVo) => {
        return item.schemeId === this.currentChooseClass?.schemeId ? true : false
      }
    }
    async created() {
      // 查询行业sku填进去行业下拉
      await QueryIndustry.queryIndustry()
      if (QueryIndustry.industryList.length == 1) {
        this.localSkuProperty.industry = QueryIndustry.industryList[0].id
        this.industryId = QueryIndustry.industryList[0].id
      }
    }
    constructor() {
      super()
      this.pageClass = new UiPage(this.pageReplaceableTrainClass, this.pageReplaceableTrainClass)
      this.pagePeriod = new UiPage(this.fillBackChooseClass, this.fillBackChooseClass)
    }
    isTrainCooperate(type: number) {
      return type == SchemeTypeEnum.trainingCooperation
    }

    // 行业属性
    getSchemeProperty(val: SkuPropertyResponseVo) {
      const arrList = Array<string>()

      if (val.industry && val.industry.skuPropertyName) arrList.push(val.industry.skuPropertyName)
      if (val.year && val.year.skuPropertyName) arrList.push(val.year.skuPropertyName)
      if (val.technicalGrade && val.technicalGrade.skuPropertyName) arrList.push(val.technicalGrade.skuPropertyName)
      if (val.subjectType && val.subjectType.skuPropertyName) arrList.push(val.subjectType.skuPropertyName)
      if (val.trainingCategory && val.trainingCategory.skuPropertyName)
        arrList.push(val.trainingCategory.skuPropertyName)
      if (val.trainingMajor && val.trainingMajor.skuPropertyName) arrList.push(val.trainingMajor.skuPropertyName)
      // 主项增项的sku
      if (val.trainingObject && val.trainingObject.skuPropertyName) arrList.push(val.trainingObject.skuPropertyName)
      if (val.positionCategory && val.positionCategory.skuPropertyName)
        arrList.push(val.positionCategory.skuPropertyName)
      if (val.jobLevel && val.jobLevel.skuPropertyName) arrList.push(val.jobLevel.skuPropertyName)
      if (val.learningPhase && val.learningPhase.skuPropertyName) arrList.push(val.learningPhase.skuPropertyName)
      if (val.discipline && val.discipline.skuPropertyName) arrList.push(val.discipline.skuPropertyName)

      if (!arrList.length) return '-'
      return arrList.join(' / ')
    }
    hiddenSelect(val: boolean) {
      this.isHidden = val
    }
    /**
     * 查询（由外部触发）
     */
    async searchByExternal(params: CreateExchangeOrderModel, schemeType: string) {
      // console.log('createExchangeOrder', params)
      this.openDrawerSchemeType = schemeType
      this.price = QueryReplaceableTrainClassListVo.validateIsEmpty(params.price) ? params.price : null
      this.schemeName = params.schemeName || ''
      this.excludeCommoditySkuIdList = [] as string[]
      this.excludeCommoditySkuIdList = params.excludeCommoditySkuIdList || ([] as string[])
      // 清空页面绑定属性
      this.localSkuProperty = new SchemeSkuProperty()
      // 清空接口查询属性
      this.queryParams = new QueryReplaceableTrainClassListVo()
      this.queryParams.price = this.price
      this.excludedIssueIdList = params.excludedIssueIdList || ([] as string[])
      this.unitId = params.unitId
      this.receiveUnitId = params.receiveUnitId
      this.mutationRequestEntrance = new MutationCreateExchangeOrder()
      this.mutationRequestEntrance.orderNo = params.orderNo || ''
      this.mutationRequestEntrance.subOrderNo = params.subOrderNo || ''
      this.mutationRequestEntrance.originIssueId = params.originIssueId
      this.isSpecial = params.isSpecialOrder
      this.specialName = params.specialOrderName
      if (this.isSpecial) {
        this.trainingChannelId = params.saleChannelId
      }
      this.distributorId = params.distributorId
      this.currentChooseClass = new ReplaceableTrainClassDetailVo()
      this.periodList = new Array<ChangeIssueItem>()
      if (QueryIndustry?.industryList?.length == 1) {
        this.industryPropertyId = QueryIndustry.industryList[0].propertyId
        this.localSkuProperty.industry = QueryIndustry.industryList[0].id
      }
      await this.searchBase()
    }

    /**
     * 查询分页
     */
    async searchBase() {
      this.pageClass.pageNo = 1
      this.currentChooseClass = new ReplaceableTrainClassDetailVo()
      this.periodList = new Array<ChangeIssueItem>()
      await this.pageReplaceableTrainClass()
    }

    /**
     * 查询分页
     */
    async pageReplaceableTrainClass() {
      this.query.loading = true
      try {
        this.queryParams = this.getQueryParams()
        if (this.localSkuProperty.studyPeriodId) this.queryParams.learningPhase = [this.localSkuProperty.studyPeriodId]
        if (this.localSkuProperty.subjectId) this.queryParams.discipline = [this.localSkuProperty.subjectId]
        if (this.openDrawerSchemeType === 'mix') {
          this.queryParams.trainingMode = TrainingModeEnum.mixed

          this.replaceableTrainClassList = await this.queryRequestEntrance.queryReplaceableTrainClassList(
            this.pageClass,
            this.queryParams
          )
        }
        if (this.openDrawerSchemeType === 'faca') {
          this.queryParams.trainingMode = TrainingModeEnum.offline
          this.replaceableTrainClassList = await this.queryRequestEntrance.queryReplaceableTrainClassList(
            this.pageClass,
            this.queryParams
          )
        }
        // 分页后回显上次选中的班级
        // this.fillBackChooseClass()
        // console.log(
        //   'replaceableTrainClassList',
        //   this.replaceableTrainClassList,
        //   '==========================================='
        // )
      } catch (e) {
        console.log('获取可更换列表失败！', e)
      } finally {
        this.query.loading = false
        this.replaceableTrainClassListRef.doLayout()
      }
    }
    /**
     * 选中行业是否是人社
     * */
    get isRs() {
      return this.localSkuProperty.industry && this.localSkuProperty.industry == IndustryIdEnum.RS
    }
    get isYs() {
      return this.localSkuProperty.industry && this.localSkuProperty.industry == IndustryIdEnum.YS
    }

    get isJs() {
      return this.localSkuProperty.industry && this.localSkuProperty.industry == IndustryIdEnum.JS
    }

    get isWs() {
      return this.localSkuProperty.industry && this.localSkuProperty.industry == IndustryIdEnum.WS
    }

    get isGq() {
      return this.localSkuProperty.industry && this.localSkuProperty.industry == IndustryIdEnum.GQ
    }

    get isLs() {
      return this.localSkuProperty.industry && this.localSkuProperty.industry == IndustryIdEnum.LS
    }
    /**
     * 获取查询参数
     */
    getQueryParams(): QueryReplaceableTrainClassListVo {
      const schemeName = this.queryParams.schemeName
      const price = this.queryParams.price
      const queryParams = new QueryReplaceableTrainClassListVo()
      queryParams.schemeName = schemeName || ''
      queryParams.price = price || null
      queryParams.price = this.price
      queryParams.year = this.localSkuProperty.year ? [this.localSkuProperty.year] : ([] as string[])
      queryParams.region = this.localSkuProperty.region || ([] as string[])
      queryParams.industry = this.localSkuProperty.industry ? [this.localSkuProperty.industry] : ([] as string[])
      queryParams.jobLevel = !this.localSkuProperty.jobLevel ? ([] as string[]) : [this.localSkuProperty.jobLevel]
      queryParams.subjectType = this.localSkuProperty.subjectType
        ? [this.localSkuProperty.subjectType]
        : ([] as string[])
      queryParams.trainingCategory = this.localSkuProperty.trainingCategory
        ? [this.localSkuProperty.trainingCategory]
        : ([] as string[])
      queryParams.trainingProfessional = this.getTrainingProfessional()
      queryParams.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      queryParams.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      queryParams.learningPhase = !this.localSkuProperty.studyPeriodId
        ? ([] as string[])
        : [this.localSkuProperty.studyPeriodId]
      queryParams.discipline = !this.localSkuProperty.subjectId ? ([] as string[]) : [this.localSkuProperty.subjectId]
      //执业类别id TODO

      queryParams.certificatesType = !this.localSkuProperty.pharmacistIndustry
        ? ([] as string[])
        : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
        ? [this.localSkuProperty.pharmacistIndustry[0]]
        : []
      queryParams.practitionerCategory = !this.localSkuProperty.pharmacistIndustry
        ? ([] as string[])
        : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
        ? [this.localSkuProperty.pharmacistIndustry[1]]
        : []
      queryParams.excludeCommoditySkuIdList = this.excludeCommoditySkuIdList
      if (this.trainingChannelId) {
        queryParams.trainingChannelIds = [this.trainingChannelId]
      }
      queryParams.distributorId = this.distributorId
      // queryParams.unitIdList = [this.unitId]
      // queryParams.receiveUnitId = this.receiveUnitId
      // queryParams.qualificationCategory = this.localSkuProperty.qualificationCategory
      //   ? [this.localSkuProperty.qualificationCategory]
      //   : []
      return queryParams
    }

    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人社行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }

    /**
     * 重置条件
     */
    async resetCondition() {
      this.localSkuProperty = new SchemeSkuProperty()
      this.queryParams = new QueryReplaceableTrainClassListVo()
      this.queryParams.price = this.price
      this.localSkuProperty.industry = this.industryId
      if (this.localSkuProperty.subjectType) {
        // this.BizAccountTypeSelectRef.clear()
      }
      this.clearSubject()
      await this.searchBase()
    }

    //选择学段
    updateStudyPeriod(val: string) {
      this.localSkuProperty.studyPeriodId = val
      this.localSkuProperty.subjectId = ''
      this.studyPeriodId = val
      this.queryParams.discipline = []
    }
    //选择学科
    updateSubject(val: string) {
      this.localSkuProperty.subjectId = val
    }
    clearSubject() {
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.studyPeriodId = ''
      this.queryParams.discipline = new Array<string>()
      this.queryParams.learningPhase = new Array<string>()
    }
    @bind()
    @debounce(200)
    async confirmExchange(row: ChangeIssueItem) {
      this.$confirm(
        `当前选择的目标班级：${this.currentChooseClass.schemeName}，目标期别：${row.issueName}，是否确认继续更换？`,
        '提示',
        {
          closeOnClickModal: false,
          confirmButtonText: '确认',
          showCancelButton: true,
          type: 'warning'
        }
      )
        .then(async () => {
          const loading = this.$loading({
            lock: true,
            text: '加载中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.8)'
          })
          this.mutationRequestEntrance.commoditySkuId = row.commoditySkuId
          this.mutationRequestEntrance.targetIssueID = row.issueId

          try {
            const status = await this.mutationRequestEntrance.sellerApplyExchange()
            console.log('🚀🐱‍🚀🐱‍👓 ~ .then ~ status:', status)
            if (status.isSuccess()) {
              this.$message.success('操作成功')
              this.$emit('reloadData')
              this.show = false
            } else if (status.code === 90001) {
              this.$message.error('目标班级不在学习时间，请重新选择')
            } else if (status.code === 60007) {
              this.$message.error('目标期别未开启报名，请重新选择')
            } else if (status.code === 60004 || status.code == 60005) {
              this.$message.error('目标期别报名时间已过，请重新选择')
            } else if (status.code === 10005 || status.code == 10006) {
              this.$message.error('目标期别培训时间已过，请重新选择')
            } else if (status.code === 60001) {
              this.$message.error('目标期别已报满，请重新选择')
            } else if (status.code == 3005) {
              this.$message.error('目标班级已下架，请重新选择')
            } else if (status.code == 3008) {
              this.$message.error('因当前订单正在办理退货/款业务，不能进行换班')
            } else if (status.code == 30025) {
              this.$message.error('原商品总价与换货后的目标商品总价不一致无法进行换货申请')
            } else if (status.code == 3009) {
              this.$message.error('因当前订单已办理部分退款，系统暂不支持课程更换服务')
            } else if ([99996, 99997, 99998, 99999, 30000, 30001, 30002].includes(Number(status.code))) {
              this.$message.error('不允许换班')
            } else if (status.code === 10001) {
              this.$confirm('无证书信息，不可报名', '提示', {
                showConfirmButton: false,
                cancelButtonText: '关闭',
                type: 'warning'
              }).catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消'
                })
              })
            } else if (status.code === 10002) {
              this.$confirm('班级专业与证书专业主增项不一致，无法报名', '提示', {
                showConfirmButton: false,
                cancelButtonText: '关闭',
                type: 'warning'
              }).catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消'
                })
              })
            } else {
              this.$message.error((status?.message as string) || '操作失败')
            }
          } catch (e) {
            this.$message.error(e)
          } finally {
            loading.close()
          }
        })
        .catch(() => {
          //
        })
    }

    //region sku-Correlation
    @Watch('localSkuProperty', {
      immediate: true,
      deep: true
    })
    localSkuPropertyChange(val: any) {
      // console.log('localSkuProperty', val)
    }

    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      this.localSkuProperty.jobLevel = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.studyPeriodId = ''
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.pharmacistIndustry = [] as string[]
    }

    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.SUBJECT_TYPE)
      const configTrainingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.TRAINING_CATEGORY
      )
      const jobLevel = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.JOB_LEVEL)
      const trainingObject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.TRAINING_OBJECT)
      const positionCategory = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.POSITION_CATEGORY)
      this.skuVisible.subjectType = configSubjectType > -1
      this.skuVisible.trainingCategory = configTrainingCategory > -1
      this.skuVisible.jobLevel = jobLevel > -1
      this.skuVisible.trainingObject = trainingObject > -1
      this.skuVisible.positionCategory = positionCategory > -1
    }

    /**
     * 响应组件行业Id集合传参
     */
    async handleIndustryInfos(values: any) {
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.occupationalHealthId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
      this.envConfig.yshyId = values.medicineIndustryId || ''
      // 仅当单个行业，需要默认选中
      const isSingleIndustry =
        (this.envConfig.societyIndustryId && !this.envConfig.constructionIndustryId) ||
        (!this.envConfig.societyIndustryId && this.envConfig.constructionIndustryId)
      if (isSingleIndustry) {
        this.localSkuProperty.industry = this.envConfig.societyIndustryId
          ? this.envConfig.societyIndustryId
          : this.envConfig.constructionIndustryId
      }
      await this.searchBase()
    }

    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
      this.isHidden = false
      this.localSkuProperty.subjectType = ''
      if (this.localSkuProperty.subjectType) {
        // this.BizAccountTypeSelectRef.clear()
      }
      // this.localSkuProperty.qualificationCategory = ''
    }
    updateTrainingCategory(val: string) {
      if (val) {
        this.trainingObjectId = val
        this.localSkuProperty.positionCategory = ''
      }
    }
    /**
     * 选择班级
     */
    chooseClass(item: ReplaceableTrainClassDetailVo) {
      this.currentChooseClass = item
      this.periodQuery.commoditySkuId = item.commoditySkuId
      this.periodQuery.excludedIssueIdList = this.excludedIssueIdList
      this.fillBackChooseClass()
    }
    /**
     * 查询期别列表
     */
    async fillBackChooseClass() {
      // 查询期别接口当没有选中上一级方案进行拦截
      if (!this.periodQuery.commoditySkuId) {
        this.periodList = []
        this.$message.warning('请先选择班级')
        return
      }
      this.periodLoading = true
      try {
        this.pagePeriod.page = 1
        this.periodList = await this.queryRequestEntrance.queryCanChangeIssueList(this.pagePeriod, this.periodQuery)
      } catch (e) {
        console.log(e)
        this.$message.error('获取数据失败')
      } finally {
        this.periodLoading = false
      }
    }
    //endregion
  }
</script>
<style scoped lang="scss">
  .pure {
    margin: 0;
    padding: 0;
  }
</style>
