import UpdateReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/UpdateReceiveAccountVo'
import {
  CIBPayEncryptionKeyDataResponse,
  ReceiveAccountConfigResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import {
  CreateReceiveAccountRequest,
  ReceiveAccountExtProperty,
  UpdateReceiveAccountRequest
} from '@api/ms-gateway/ms-trade-configuration-v1'

export default class UpdateXYPayReceiveAccountVo extends UpdateReceiveAccountVo {
  /**
   * 应用id
   */
  xyPayAppId = ''
  /**
   * 终端编号
   */
  terminalId = ''
  /**
   * SM2签名私钥
   */
  sm2key = ''
  /**
   * 响应公钥
   */
  resPublicKey = ''
  /**
   * 请求私钥
   */
  reqKey = ''
  /**
   * 公众号或小程序id
   */
  xyPaySubAppId = ''

  from(res: ReceiveAccountConfigResponse) {
    this.id = res.id
    this.accountType = res.accountType
    this.accountNo = res.accountNo
    this.accountName = res.name
    this.taxPayerId = res.taxPayerId
    this.refundWay = res.returnType
    this.paymentChannelId = res.paymentChannelId
    this.qrScanPrompt = res.qrScanPrompt
    if (res.encryptionKeyData.encryptionKeyType === 'CIBPay') {
      const temp = res.encryptionKeyData as CIBPayEncryptionKeyDataResponse
      this.xyPayAppId = temp.appId
      this.terminalId = temp.terminalId
      this.sm2key = temp.requestPrivateKey
      this.resPublicKey = temp.responsePublicKey
      this.reqKey = temp.requestParamEncryptKey
      this.xyPaySubAppId = temp.subAppid
    }
  }

  to(): CreateReceiveAccountRequest {
    const updateReceiveAccountRequest = new UpdateReceiveAccountRequest()
    updateReceiveAccountRequest.receiveAccountId = this.id
    updateReceiveAccountRequest.name = this.accountName
    updateReceiveAccountRequest.qrScanPrompt = this.qrScanPrompt
    updateReceiveAccountRequest.refundWay = this.refundWay
    updateReceiveAccountRequest.taxPayerId = this.taxPayerId
    updateReceiveAccountRequest.properties = new Array<ReceiveAccountExtProperty>()
    this.updateProperties('appId', this.xyPayAppId, updateReceiveAccountRequest.properties)
    this.updateProperties('terminalId', this.terminalId, updateReceiveAccountRequest.properties)
    this.updateProperties('requestPrivateKey', this.sm2key, updateReceiveAccountRequest.properties)
    this.updateProperties('responsePublicKey', this.resPublicKey, updateReceiveAccountRequest.properties)
    this.updateProperties('requestParamEncryptKey', this.reqKey, updateReceiveAccountRequest.properties)
    this.updateProperties('subAppid', this.xyPaySubAppId, updateReceiveAccountRequest.properties)
    return updateReceiveAccountRequest
  }

  private updateProperties(propertyName: string, propertyValue: string, properties: Array<ReceiveAccountExtProperty>) {
    const property = properties?.find((item) => item.name === propertyName)
    if (property) {
      property.value = propertyValue
    } else {
      const item = new ReceiveAccountExtProperty()
      item.name = propertyName
      item.value = propertyValue
      properties.push(item)
    }
    return properties
  }
}
