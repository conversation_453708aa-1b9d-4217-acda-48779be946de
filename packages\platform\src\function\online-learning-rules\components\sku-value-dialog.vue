<!--
 * @Author: chenweinian <EMAIL>
 * @Date: 2025-05-12 17:49:16
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2025-05-22 17:20:01
 * @Description: 适用行业属性弹窗
-->
<template>
  <el-drawer
    title="行业培训属性"
    :visible.sync="openSkuDialog"
    size="600px"
    :append-to-body="true"
    custom-class="m-drawer m-table-auto"
  >
    <div class="drawer-bd">
      <div class="m-left-divider" v-if="skuItem.RSProperty.isTraverse">
        <el-divider content-position="left"><i class="f-f15 f-fb f-cb">人社行业</i></el-divider>
        <el-form ref="form" :model="skuItem.RSProperty" label-width="150px" class="m-form is-mb f-ptb10 f-mb20">
          <el-form-item label="年度：" v-if="skuItem.RSProperty.yearName">{{
            skuItem.RSProperty.yearName
          }}</el-form-item>
          <el-form-item label="科目类型：" v-if="skuItem.RSProperty.subjectTypeName">{{
            skuItem.RSProperty.subjectTypeName
          }}</el-form-item>
          <el-form-item label="培训专业：" v-if="skuItem.RSProperty.trainingProfessionalName">{{
            skuItem.RSProperty.trainingProfessionalName
          }}</el-form-item>
        </el-form>
      </div>
      <div class="m-left-divider" v-if="skuItem.JSProperty.isTraverse">
        <el-divider content-position="left"><i class="f-f15 f-fb f-cb">建设行业</i></el-divider>
        <el-form ref="form" :model="skuItem.JSProperty" label-width="150px" class="m-form is-mb f-ptb10 f-mb20">
          <el-form-item label="年度：" v-if="skuItem.JSProperty.yearName">{{
            skuItem.JSProperty.yearName
          }}</el-form-item>
          <el-form-item label="科目类型：" v-if="skuItem.JSProperty.subjectTypeName">{{
            skuItem.JSProperty.subjectTypeName
          }}</el-form-item>
          <el-form-item label="培训类别：" v-if="skuItem.JSProperty.trainingCategoryName">{{
            skuItem.JSProperty.trainingCategoryName
          }}</el-form-item>
        </el-form>
      </div>
      <div class="m-left-divider" v-if="skuItem.WSProperty.isTraverse">
        <el-divider content-position="left"><i class="f-f15 f-fb f-cb">卫生行业</i></el-divider>
        <el-form ref="form" :model="skuItem.WSProperty" label-width="150px" class="m-form is-mb f-ptb10 f-mb20">
          <el-form-item label="年度：" v-if="skuItem.WSProperty.yearName">{{
            skuItem.WSProperty.yearName
          }}</el-form-item>
          <el-form-item label="培训类别：" v-if="skuItem.WSProperty.trainingCategoryName">{{
            skuItem.WSProperty.trainingCategoryName
          }}</el-form-item>
          <el-form-item label="培训对象：" v-if="skuItem.WSProperty.trainingObjectName">{{
            skuItem.WSProperty.trainingObjectName
          }}</el-form-item>
          <el-form-item label="岗位类别：" v-if="skuItem.WSProperty.positionCategoryName">{{
            skuItem.WSProperty.positionCategoryName
          }}</el-form-item>
        </el-form>
      </div>
      <div class="m-left-divider" v-if="skuItem.GQProperty.isTraverse">
        <el-divider content-position="left"><i class="f-f15 f-fb f-cb">工勤行业</i></el-divider>
        <el-form ref="form" :model="skuItem.GQProperty" label-width="150px" class="m-form is-mb f-ptb10 f-mb20">
          <el-form-item label="年度：" v-if="skuItem.GQProperty.yearName">{{
            skuItem.GQProperty.yearName
          }}</el-form-item>
          <el-form-item label="技术等级：" v-if="skuItem.GQProperty.jobLevelName">{{
            skuItem.GQProperty.jobLevelName
          }}</el-form-item>
        </el-form>
      </div>
      <div class="m-left-divider" v-if="skuItem.LSProperty.isTraverse">
        <el-divider content-position="left"><i class="f-f15 f-fb f-cb">教师行业</i></el-divider>
        <el-form ref="form" :model="skuItem.LSProperty" label-width="150px" class="m-form is-mb f-ptb10 f-mb20">
          <el-form-item label="年度：" v-if="skuItem.LSProperty.yearName">{{
            skuItem.LSProperty.yearName
          }}</el-form-item>
          <el-form-item label="学段：" v-if="skuItem.LSProperty.learningPhaseName">{{
            skuItem.LSProperty.learningPhaseName
          }}</el-form-item>
          <el-form-item label="学科：" v-if="skuItem.LSProperty.disciplineName">{{
            skuItem.LSProperty.disciplineName
          }}</el-form-item>
        </el-form>
      </div>
      <div class="m-left-divider" v-if="skuItem.YSProperty.isTraverse">
        <el-divider content-position="left"><i class="f-f15 f-fb f-cb">药师行业</i></el-divider>
        <el-form ref="form" :model="skuItem.YSProperty" label-width="150px" class="m-form is-mb f-ptb10 f-mb20">
          <el-form-item label="年度：" v-if="skuItem.YSProperty.yearName">{{
            skuItem.YSProperty.yearName
          }}</el-form-item>
          <el-form-item label="科目类型：" v-if="skuItem.YSProperty.subjectTypeName">{{
            skuItem.YSProperty.subjectTypeName
          }}</el-form-item>
        </el-form>
      </div>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="openSkuDialog = false">返回</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import BasicInfo from '@api/service/management/online-learning-rule/model/BasicInfo'
  import { Component, Vue } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    /**
     * 是否打开弹窗
     */
    openSkuDialog = false

    /**
     * 行业属性集合
     */
    skuItem = new BasicInfo()

    /**
     * 初始化数据
     */
    initData() {
      this.openSkuDialog = true
    }
  }
</script>
