import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-basicdata-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-basicdata-query-front-gateway-BasicDataQueryBackstage-qyfwpt'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum EnterprisePersonSortFieldEnum {
  createdTime = 'createdTime',
  accountType = 'accountType',
  userNameFirstLetter = 'userNameFirstLetter'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 功能描述：账户信息查询条件
@Author： wtl
@Date： 2022年5月11日 15:30:56
 */
export class AccountRequest {
  /**
   * 账户状态 1：正常，2：冻结
@see AccountStatus
   */
  statusList?: Array<number>
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest
  /**
   * 创建人用户id
   */
  createdUserId?: string
}

/**
 * 功能描述：登录认证查询条件
@Author： wtl
@Date： 2022年1月26日 09:30:12
 */
export class AuthenticationRequest {
  /**
   * 帐号
   */
  identity?: string
}

/**
 * 功能描述：角色查询条件
@Author： wtl
@Date： 2022年5月11日 11:46:41
 */
export class RoleRequest {
  /**
   * 角色id集合
   */
  roleIdList?: Array<string>
  /**
   * 角色类型
（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
   */
  roleTypeList?: Array<string>
  /**
   * 角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
//@see RoleCategories
   */
  roleCategoryList?: Array<number>
}

/**
 * 功能描述：管理员排序
@Author： wtl
@Date： 2021/12/27 10:32
 */
export class AdminSortRequest {
  /**
   * 管理员排序字段
   */
  sortField?: EnterprisePersonSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 功能描述：管理员查询条件
@Author： wtl
@Date： 2022年1月25日 15:24:10
 */
export class AdminUserRequest {
  /**
   * 管理地区路径集合（模糊，右like）
   */
  manageRegionPathList?: Array<string>
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  userNameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  phoneMatchType?: number
}

/**
 * 功能描述 : 企业单位管理员查询条件
@date : 2022/6/17 17:32
 */
export class EnterpriseUnitAdminQueryRequest {
  /**
   * 企业单位管理员归属信息
   */
  owner?: EnterpriseUnitAdminOwnerRequest
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: AdminUserRequest
  /**
   * 登录认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 角色信息查询
   */
  role?: RoleRequest
  /**
   * 排序
   */
  sortList?: Array<AdminSortRequest>
}

/**
 * 功能描述 : 企业单位管理员归属查询条件
@date : 2022/6/17 17:42
 */
export class EnterpriseUnitAdminOwnerRequest {
  /**
   * 企业单位id路径集合
String：&quot;/福建电信id/福州电信分公司id&quot;
   */
  enterpriseUnitIdPathList?: Array<string>
  /**
   * 企业单位id路径匹配方式，默认为右模糊查询(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  enterpriseUnitIdPathMatchType?: number
  /**
   * 企业单位归属关系状态(0:冻结 1:正常)
@see PersonUnitRelationshipStatus
   */
  status?: number
}

export class DateScopeRequest {
  beginTime?: string
  endTime?: string
}

export class RegionModel {
  regionId: string
  regionPath: string
  provinceId: string
  provinceName: string
  cityId: string
  cityName: string
  countyId: string
  countyName: string
}

/**
 * 功能描述：账户信息
@Author： wtl
@Date： 2022年5月11日 15:30:56
 */
export class AccountResponse {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
@see AccountTypes
   */
  accountType: number
  /**
   * 单位信息
   */
  unitInfo: UnitInfoResponse
  /**
   * 所属顶级企业帐户Id
   */
  rootAccountId: string
  /**
   * 帐户状态 1：正常，2：冻结，3：注销
@see AccountStatus
   */
  status: number
  /**
   * 注册方式
0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
@see AccountRegisterTypes
   */
  registerType: number
  /**
   * 来源类型
0内置，1项目主网站，2安卓，3IOS
@see AccountSourceTypes
   */
  sourceType: number
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 功能描述：帐户认证信息
@Author： wtl
@Date： 2022年5月11日 14:23:18
 */
export class AuthenticationResponse {
  /**
   * 帐号
   */
  identity: string
  /**
   * 认证标识类型
1用户名,2手机,3身份证,4邮箱,5第三方OpenId
   */
  identityType: number
  /**
   * 认证方式状态 1启用，2禁用
@see AuthenticationStatusEnum
   */
  status: number
}

/**
 * 功能描述：角色信息
@Author： wtl
@Date： 2022/1/24 20:17
 */
export class RoleResponse {
  /**
   * 角色id
   */
  roleId: string
  /**
   * 角色名称
   */
  roleName: string
  /**
   * 角色类型
（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
   */
  roleType: string
  /**
   * 角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
@see RoleCategories
   */
  roleCategory: number
  /**
   * 应用方类型
@see SystemMemberTypes
   */
  applicationMemberType: number
  /**
   * 应用方ID
   */
  applicationMemberId: string
}

/**
 * 功能描述 : 企业单位管理员信息
@date : 2022/6/18 12:24
 */
export class EnterpriseUnitAdminInfoResponse {
  /**
   * 企业单位管理员归属信息
   */
  businessOwnerInfo: EnterpriseUnitAdminBusinessOwnerResponse
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 管理员用户信息
   */
  userInfo: AdminUserInfoResponse
  /**
   * 人员信息
   */
  personInfo: EnterpriseUnitPersonInfoResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
  /**
   * 角色信息集合
   */
  roleList: Array<RoleResponse>
}

/**
 * 功能描述：管理员用户信息
@Author： wtl
@Date： 2022年1月25日 15:48:48
 */
export class AdminUserInfoResponse {
  /**
   * 管辖地区集合
   */
  manageRegionList: Array<RegionModel>
  /**
   * 办公室（所在处/科室）
   */
  office: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 性别
-1未知，0女，1男
@see Genders
   */
  gender: number
  /**
   * 证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string
}

/**
 * 企业单位管理员业务归属信息
 */
export class EnterpriseUnitAdminBusinessOwnerResponse {
  /**
   * 隶属企业单位集合
   */
  relationToEnterpriseUnitList: Array<RelationToUnitResponse>
}

/**
 * 人员信息模型
 */
export class EnterpriseUnitPersonInfoResponse {
  /**
   * 是否法人帐号
   */
  isCorporateAccount: boolean
  /**
   * 人员实名认证信息
   */
  personIdentityVerificationInfo: PersonIdentityVerificationResponse
}

/**
 * 人员实名认证信息模型
 */
export class PersonIdentityVerificationResponse {
  /**
   * 是否已认证
   */
  identityVerification: boolean
  /**
   * 认证渠道(1:闽政通 2：腾讯)
   */
  identityVerificationChannel: number
  /**
   * 认证时间
   */
  identityVerificationTime: string
}

/**
 * 与企业单位关联关系
 */
export class RelationToUnitResponse {
  /**
   * 企业单位id路径
   */
  unitIdPath: string
  /**
   * 人员与单位关系状态(0:冻结 1:正常)
@see PersonUnitRelationshipStatus
   */
  status: number
}

/**
 * 单位信息模型
 */
export class UnitInfoResponse {
  /**
   * 单位ID
   */
  unitId: string
}

export class EnterpriseUnitAdminInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<EnterpriseUnitAdminInfoResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 功能描述：企业-当前登录企业管理员信息
   * 描述：查询当前登录管理员的信息
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getEnterpriseUnitAdminInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitAdminInfoInMyself,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponse>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：项目级-企业单位管理员详情查询接口
   * 描述：查询当前企业单位下指定管理员的信息，如不存在返回null
   * @param accountId               : 帐户ID
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
   * @date : 2022年6月9日 16:50:10
   * @param query 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getEnterpriseUnitAdminInfoInSubProject(
    accountId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponse>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-企业单位管理员详情查询接口（按用户ID查询）
   * 描述：查询当前企业单位下指定管理员的信息，如不存在返回null
   * @param adminUserId             : 帐户ID
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
   * @date : 2022年6月9日 16:50:10
   * @param query 查询 graphql 语法文档
   * @param adminUserId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getEnterpriseUnitAdminUserInfoInSubProject(
    adminUserId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getEnterpriseUnitAdminUserInfoInSubProject,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponse>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { adminUserId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述： 政府单位-查询本级及下属企业单位管理员-分页列表接口
   * 描述：查询政府单位管辖下的企业单位管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:10
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageEnterpriseUnitAdminInfoInGovernmentUnit(
    params: { page?: Page; request?: EnterpriseUnitAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitAdminInfoInGovernmentUnit,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：企业单位-查询当前登录企业单位下属管理员-分页列表接口
   * 描述：查询当前企业单位下的管理员信息，默认按创建时间降序排，如不存在返回null
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:10
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageEnterpriseUnitAdminInfoInMyself(
    params: { page?: Page; request?: EnterpriseUnitAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitAdminInfoInMyself,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询企业单位管理员-分页列表接口
   * 描述：查询当前子项目下的企业单位管理员信息，默认按创建时间降序排
   * @param page                    :
   * @param request                 :
   * @param dataFetchingEnvironment :
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
   * @date : 2022年6月9日 16:50:21
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageEnterpriseUnitAdminInfoInSubProject(
    params: { page?: Page; request?: EnterpriseUnitAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageEnterpriseUnitAdminInfoInSubProject,
    operation?: string
  ): Promise<Response<EnterpriseUnitAdminInfoResponsePage>> {
    return commonRequestApi<EnterpriseUnitAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
