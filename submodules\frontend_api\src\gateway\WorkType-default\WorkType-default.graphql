schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取工种所有大类信息"""
	getAllBigCategoryList:[BigCategoryResponse]
	"""获取所有工种大类树"""
	getAllBigCategoryTreeList:[BigCategoryTreeResponse]
	"""获取工种所有中类信息"""
	getAllMediumCategoryList:[MediumCategoryResponse]
	"""获取工种所有职业信息"""
	getAllProfessionList:[ProfessionResponse]
	"""获取工种所有小类信息"""
	getAllSmallCategoryList:[SmallCategoryResponse]
	"""根据大类id，获取大类的关系树"""
	getBigCategoryTree(bigCategoryId:String):BigCategoryTreeResponse
	"""根据大类id，获取指定大类下的中类信息"""
	getMediumCategoryListByBigCategoryId(bigCategoryId:String):[MediumCategoryResponse]
	"""根据中类id，获取中类的关系树"""
	getMediumCategoryTree(mediumCategoryId:String):MediumCategoryTreeResponse
	"""根据小类id，获取指定小类下的职业信息"""
	getProfessionListBySmallCategoryId(smallCategoryId:String):[ProfessionResponse]
	"""根据中类id，获取指定中类下的小类信息"""
	getSmallCategoryListByMediumCategoryId(mediumCategoryId:String):[SmallCategoryResponse]
	"""根据小类id，获取小类的关系树"""
	getSmallCategoryTree(smallCategoryId:String):SmallCategoryTreeResponse
}
type Mutation {
	createBigCategory(request:BigCategoryCreateRequest):BigCategoryResponse
	"""根据一个工种大类树，创建一个大类以及底下的中类，小类，职业等信息"""
	createBigCategoryTree(request:BigCategoryTreeCreateRequest):BigCategoryTreeResponse
	createMediumCategory(request:MediumCategoryCreateRequest):MediumCategoryResponse
	"""根据一个工种中类树，创建一个中类以及底下的小类，职业等信息"""
	createMediumCategoryTree(request:MediumCategoryCreateRequest):MediumCategoryTreeResponse
	createProfession(request:ProfessionCreateRequest):ProfessionResponse
	createSmallCategory(request:SmallCategoryCreateRequest):SmallCategoryResponse
	"""根据一个工种小类树，创建一个小类以及底下的职业等信息"""
	createSmallCategoryTree(request:SmallCategoryCreateRequest):SmallCategoryTreeResponse
	diableBigCategory(bigCategoryId:String):BigCategoryResponse
	diableProfession(professionId:String):ProfessionResponse
	disableMediumCategory(mediumCategoryId:String):MediumCategoryResponse
	disableSmallCategory(smallCategoryId:String):SmallCategoryResponse
	enableSmallCategory(smallCategoryId:String):SmallCategoryResponse
	enableBigCategory(bigCategoryId:String):BigCategoryResponse
	enableMediumCategory(mediumCategoryId:String):MediumCategoryResponse
	enableProfession(professionId:String):ProfessionResponse
	removeBigCategory(bigCategoryId:String):BigCategoryResponse
	removeMediumCategory(mediumCategoryId:String):MediumCategoryResponse
	removeProfession(professionId:String):ProfessionResponse
	removeSmallCategory(smallCategoryId:String):SmallCategoryResponse
	updateBigCategory(request:BigCategoryUpdateRequest):BigCategoryResponse
	updateMediumCategory(request:MediumCategoryUpdateRequest):MediumCategoryResponse
	updateProfession(request:ProfessionUpdateRequest):ProfessionResponse
	updateSmallCategory(request:SmallCategoryUpdateRequest):SmallCategoryResponse
}
input BigCategoryCreateRequest @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.request.BigCategoryCreateRequest") {
	name:String!
	"""排序，-1为自动排序"""
	sort:Int!
}
input BigCategoryTreeCreateRequest @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.request.BigCategoryTreeCreateRequest") {
	mediumCategories:[MediumCategoryTreeCreateRequest]
	name:String!
	"""排序，-1为自动排序"""
	sort:Int!
}
input BigCategoryUpdateRequest @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.request.BigCategoryUpdateRequest") {
	id:String!
	name:String!
}
input MediumCategoryCreateRequest @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.request.MediumCategoryCreateRequest") {
	name:String!
	bigCategoryId:String!
	"""排序，-1为自动排序"""
	sort:Int!
}
input MediumCategoryTreeCreateRequest @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.request.MediumCategoryTreeCreateRequest") {
	smallCategories:[SmallCategoryTreeCreateRequest]
	name:String!
	bigCategoryId:String!
	"""排序，-1为自动排序"""
	sort:Int!
}
input MediumCategoryUpdateRequest @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.request.MediumCategoryUpdateRequest") {
	id:String!
	name:String!
}
input ProfessionCreateRequest @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.request.ProfessionCreateRequest") {
	name:String!
	smallCategoryId:String!
	"""排序，-1为自动排序"""
	sort:Int!
}
input ProfessionUpdateRequest @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.request.ProfessionUpdateRequest") {
	id:String!
	name:String!
}
input SmallCategoryCreateRequest @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.request.SmallCategoryCreateRequest") {
	name:String!
	mediumCategoryId:String!
	"""排序，-1为自动排序"""
	sort:Int!
}
input SmallCategoryTreeCreateRequest @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.request.SmallCategoryTreeCreateRequest") {
	professions:[ProfessionCreateRequest]
	name:String!
	mediumCategoryId:String!
	"""排序，-1为自动排序"""
	sort:Int!
}
input SmallCategoryUpdateRequest @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.request.SmallCategoryUpdateRequest") {
	id:String!
	name:String!
}
"""工种大类响应信息
	<AUTHOR>
"""
type BigCategoryResponse @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.response.BigCategoryResponse") {
	id:String
	name:String
	sort:Long!
	enabled:Boolean!
	createUserId:String
	createTime:DateTime
	creatorId:String
	code:String
}
type BigCategoryTreeResponse @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.response.BigCategoryTreeResponse") {
	children:[MediumCategoryTreeResponse]
	id:String
	name:String
	sort:Long!
	enabled:Boolean!
	createUserId:String
	createTime:DateTime
	creatorId:String
	code:String
}
type MediumCategoryResponse @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.response.MediumCategoryResponse") {
	"""所属大类id"""
	bigCategoryId:String
	id:String
	name:String
	sort:Long!
	enabled:Boolean!
	createUserId:String
	createTime:DateTime
	creatorId:String
	code:String
}
type MediumCategoryTreeResponse @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.response.MediumCategoryTreeResponse") {
	"""所属大类id"""
	bigCategoryId:String
	children:[SmallCategoryTreeResponse]
	id:String
	name:String
	sort:Long!
	enabled:Boolean!
	createUserId:String
	createTime:DateTime
	creatorId:String
	code:String
}
type ProfessionResponse @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.response.ProfessionResponse") {
	smallCategoryId:String
	id:String
	name:String
	sort:Long!
	enabled:Boolean!
	createUserId:String
	createTime:DateTime
	creatorId:String
	code:String
}
type SmallCategoryResponse @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.response.SmallCategoryResponse") {
	"""所属的中类id"""
	mediumCategoryId:String
	id:String
	name:String
	sort:Long!
	enabled:Boolean!
	createUserId:String
	createTime:DateTime
	creatorId:String
	code:String
}
type SmallCategoryTreeResponse @type(value:"com.fjhb.platform.core.v1.worktype.kernel.gateway.graphql.response.SmallCategoryTreeResponse") {
	"""所属的中类id"""
	mediumCategoryId:String
	children:[ProfessionResponse]
	id:String
	name:String
	sort:Long!
	enabled:Boolean!
	createUserId:String
	createTime:DateTime
	creatorId:String
	code:String
}

scalar List
