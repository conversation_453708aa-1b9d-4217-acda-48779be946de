<!-- eslint-disable no-undef -->
<template>
  <el-main>
    <el-dialog title="访问网校" :visible.sync="isShowStartDialog" width="600px" class="m-dialog">
      <el-form label-width="auto" class="m-form">
        <el-form-item label="WEB：">
          <span class="f-mr20">{{ http1 }}</span>
          <el-button type="text" label-width="auto" @click="open(1)" class="m-form">复制链接</el-button>
        </el-form-item>
        <el-form-item label="H5页面：">
          <div class="f-mb10">
            <span class="f-mr20">{{ http2 }}</span>
            <el-button type="text" @click="open(2)">复制H5链接</el-button>
          </div>
          <img class="u-qr-code" :src="qrcode" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="isShowStartDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb10">
      温馨提示：当前网校未开启web、h5访问。请完成网校配置后开启对外访问。
      <a href="#" class="f-link f-cb f-underline">立即前往 &gt;</a>
      <el-button type="text" @click="to">立即前往 &gt;</el-button>
    </el-alert> -->
    <el-card shadow="never" class="m-card is-bg">
      <div class="f-flex f-align-center f-ptb10 f-plr15">
        <div class="f-flex-sub">{{ date }}，欢迎登录~</div>
        <el-button type="primary" size="small" @click="isShowStartDialog = true">
          访问网校<i class="iconfont icon-qianwang el-icon--right"></i>
        </el-button>
      </div>
    </el-card>
    <div class="f-p20">
      <el-card shadow="never" class="m-card is-header f-mb20" v-if="configShow">
        <div slot="header" class="f-flex f-align-center">
          <span class="f-f16 f-fb f-flex-sub">网校配置概览</span>
          <!-- <a href="#" class="f-link f-cb">关闭</a> -->
          <el-button type="text" @click="configShow = false">关闭</el-button>
        </div>
        <el-row type="flex" class="m-school-set is-height">
          <el-col :sm="12" :xl="6">
            <div class="item">
              <div class="item-hd">
                <div class="name">完善网校设置</div>
                <!--全部配置完成-->
                <div class="statue f-cg"><i class="el-icon-success f-mr5"></i>配置完成</div>
                <!--部分配置完成-->
                <!--<div class="statue f-cb">配置中（1/3）</div>-->
                <!--未开始配置-->
                <!--<div class="statue f-co"><i class="el-icon-warning f-mr5"></i>未配置</div>-->
              </div>
              <div class="item-bd">
                <!--已完成-->
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                  <el-button type="primary" round @click="setting(1)">门户配置</el-button>
                  <div slot="content">已配置</div>
                </el-tooltip>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                  <el-button type="primary" round @click="setting(2)">移动端配置</el-button>
                  <div slot="content">已配置</div>
                </el-tooltip>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                  <el-button type="primary" round @click="setting(3)">功能配置</el-button>
                  <div slot="content">已配置</div>
                </el-tooltip>
              </div>
              <i class="el-icon-d-arrow-right arrow"></i>
            </div>
          </el-col>
          <el-col :sm="12" :xl="6">
            <div class="item">
              <div class="item-hd">
                <div class="name">设置收款、发票、报名方式</div>
                <div class="statue f-cb">配置中（1/3）</div>
              </div>
              <div class="item-bd">
                <!--已完成-->
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                  <el-button type="primary" round @click="setting(4)">收款账户</el-button>
                  <div slot="content">已配置</div>
                </el-tooltip>
                <!--未完成-->
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                  <el-button :type="buttonType(2)" round @click="setting(5)">报名方式配置</el-button>
                  <div slot="content">未配置，点击前往</div>
                </el-tooltip>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                  <el-button type="info" round @click="setting(6)">配送渠道配置</el-button>
                  <div slot="content">未配置，点击前往</div>
                </el-tooltip>
              </div>
              <i class="el-icon-d-arrow-right arrow"></i>
            </div>
          </el-col>
          <el-col :sm="12" :xl="6">
            <div class="item">
              <div class="item-hd">
                <div class="name">创建教学课程和试题等资源</div>
                <div class="statue f-cb">配置中（3/4）</div>
              </div>
              <div class="item-bd">
                <!--已完成-->
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                  <el-button type="primary" round @click="setting(7)">课件配置</el-button>
                  <div slot="content">已配置</div>
                </el-tooltip>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                  <el-button type="primary" round @click="setting(8)">课程配置</el-button>
                  <div slot="content">已配置</div>
                </el-tooltip>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                  <el-button type="primary" round @click="setting(9)">试题配置</el-button>
                  <div slot="content">已配置</div>
                </el-tooltip>
                <!--未完成-->
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                  <el-button type="info" round @click="setting(10)">试卷配置</el-button>
                  <div slot="content">未配置，点击前往</div>
                </el-tooltip>
              </div>
              <i class="el-icon-d-arrow-right arrow"></i>
            </div>
          </el-col>
          <el-col :sm="12" :xl="6">
            <div class="item">
              <div class="item-hd">
                <div class="name">发布培训方案</div>
                <div class="statue f-co"><i class="el-icon-warning f-mr5"></i>未配置</div>
              </div>
              <div class="item-bd">
                <!--未完成-->
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                  <el-button type="info" round @click="setting(11)">课程包配置</el-button>
                  <div slot="content">未配置，点击前往</div>
                </el-tooltip>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                  <el-button type="info" round @click="setting(12)">培训方案</el-button>
                  <div slot="content">未配置，点击前往</div>
                </el-tooltip>
              </div>
              <i class="el-icon-d-arrow-right arrow"></i>
            </div>
          </el-col>
        </el-row>
      </el-card>
      <el-row :gutter="20" class="is-height f-mb20">
        <!-- <el-col :span="12">
          <el-card shadow="never" class="m-card">
            <div slot="header" class="f-flex">
              <span class="f-f16 f-fb">在线学习人数</span>
              <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-c9 f-ml5"></i>
                <div slot="content">在线学习人数统计学员登录平台学习的人数，一个学员一天只能算一次</div>
              </el-tooltip>
            </div>
            <div class="m-index-data data-1">
              <div class="f-flex-sub">
                <div class="icon"><i class="iconfont icon-kecheng"></i></div>
              </div>
              <div class="num-item">
                <div class="num important">无数据</div>
                <div class="tit">今日学习人数</div>
              </div>
              <div class="num-item">
                <div class="num">无数据</div>
                <div class="tit">累计学习人数</div>
              </div>
            </div>
          </el-card>
        </el-col> -->
        <el-col :span="24">
          <el-card shadow="never" class="m-card">
            <div slot="header" class="f-flex">
              <span class="f-f16 f-fb">报名人次</span>
              <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-c9 f-ml5"></i>
                <div slot="content">报名人数统计网校报名人次，一个学员可以多次报名</div>
              </el-tooltip>
            </div>
            <div class="m-index-data data-2">
              <div class="f-flex-sub">
                <div class="icon"><i class="iconfont icon-xueyuan1"></i></div>
              </div>
              <div class="num-item">
                <div class="num important">{{ todayEnrollNum }}</div>
                <div class="tit">今日报名人次</div>
              </div>
              <div class="num-item">
                <div class="num">{{ cumulativeEnrollNum }}</div>
                <div class="tit">累积报名人次</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="is-height">
        <el-col :span="16">
          <el-card shadow="never" class="m-card">
            <div slot="header" class="f-flex">
              <span class="f-f16 f-fb">交易概述</span>
              <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-c9 f-ml5"></i>
                <div slot="content">按时间统计网校交易的金额</div>
              </el-tooltip>
            </div>
            <div class="m-index-chart f-tc">
              <!--以下为图表实例图-->
              <!-- <img style="max-width: 100%" src="@design/admin/assets/images/chart-1.png" alt="" /> -->

              <div id="chart" style="100%;height:380px;"></div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="never" class="m-card">
            <div slot="header" class="f-flex">
              <span class="f-f16 f-fb">方案报名排行</span>
              <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-c9 f-ml5"></i>
                <div slot="content">统计网校当前报名最多的培训班前十位</div>
              </el-tooltip>
            </div>
            <div class="m-index-rank">
              <div class="item hd">
                <div class="rank">排行</div>
                <div class="name">方案名称</div>
                <div class="num">成交量</div>
              </div>
              <div>
                <div class="item" v-for="(item, index) in ranking" :key="index">
                  <!--第一 first，第二 second，第三 third-->
                  <div class="rank " :class="turnoveRanking(index)">
                    <i class="rank-num">{{ index + 1 }}</i>
                  </div>
                  <div class="name" v-if="item.trainClassDetail">{{ item.trainClassDetail.saleTitle }}</div>
                  <div class="num" v-if="item.summaryInfo">{{ item.summaryInfo.tradeSuccessCount }}</div>
                </div>
                <!-- <div class="item">
                  <div class="rank second"><i class="rank-num">2</i></div>
                  <div class="name">
                    方案名称方案名称方案名称方案名称方案名称方案名称方案名称方案名称方案名称方案名称
                  </div>
                  <div class="num">9</div>
                </div>
                <div class="item">
                  <div class="rank third"><i class="rank-num">3</i></div>
                  <div class="name">方案名称方案名称方案名称方案名称</div>
                  <div class="num">8</div>
                </div>
                <div class="item">
                  <div class="rank"><i class="rank-num">4</i></div>
                  <div class="name">方案名称方案名称方案名称</div>
                  <div class="num">7</div>
                </div>
                <div class="item">
                  <div class="rank"><i class="rank-num">5</i></div>
                  <div class="name">方案名称方案名称方案名称方案名称方案名称方案名称方案名称</div>
                  <div class="num">6</div>
                </div>
                <div class="item">
                  <div class="rank"><i class="rank-num">6</i></div>
                  <div class="name">方案名称方案名称</div>
                  <div class="num">5</div>
                </div>
                <div class="item">
                  <div class="rank"><i class="rank-num">7</i></div>
                  <div class="name">方案名称方案名称方案名称方案名称</div>
                  <div class="num">4</div>
                </div>
                <div class="item">
                  <div class="rank"><i class="rank-num">8</i></div>
                  <div class="name">方案名称方案名称方案名称</div>
                  <div class="num">3</div>
                </div>
                <div class="item">
                  <div class="rank"><i class="rank-num">9</i></div>
                  <div class="name">方案名称方案名称方案名称方案名称</div>
                  <div class="num">2</div>
                </div>
                <div class="item">
                  <div class="rank"><i class="rank-num">10</i></div>
                  <div class="name">方案名称方案名称方案名称方案名称方案名称</div>
                  <div class="num">1</div>
                </div> -->
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <div class="m-layout">
      <!--服务即将到期 样式-->
      <!-- <div class="item is-warning" v-if="expire > 1">
        <i class="el-icon-warning icon"></i>
        <div class="bd">
          <p class="f-fb">服务即将到期</p>
          <p class="f-f12">有效期剩余：{{ expire }}天</p>
        </div>
      </div> -->
      <!--服务今日到期 样式-->
      <!-- <div class="item is-important" v-else>
        <i class="el-icon-warning icon"></i>
        <div class="bd">
          <p class="f-fb">服务今日到期</p>
        </div>
      </div> -->
      <!-- <div class="item f-csp">
        <i
          class="el-icon-s-tools
         icon"
        ></i>
        <div class="bd" @click="configShow = true">打开配置概览</div>
      </div> -->
      <div class="item f-csp">
        <i class="el-icon-s-opportunity icon"></i>
        <div class="bd" @click="openNoviceGuide">查看新手引导</div>
      </div>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue as VueDecorator } from 'vue-property-decorator'
  import rootModule from '@/store/RootModule'
  import Mockjs from 'mockjs'
  import UiModule from '@/store/UiModule'
  import StaticticalReportManagerModule from '@api/service/management/statisticalReport/StaticticalReportManagerModule'
  import { QueryTrainClassReportList } from '@api/service/management/statisticalReport/query/QueryTrainClassReportList'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import msTrade, {
    TradeStatisticDateHistogramBucketResponse,
    TradeStatisticDateHistogramRequest,
    TradeStatisticDateHistogramResponse,
    DateScopeRequest,
    TradeReportRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import echarts from 'echarts'
  import QRCode from 'qrcode'
  import QueryPortal from '@api/service/management/online-school-config/portal/query/QueryPortal'
  import QueryClassReportBySucnum from '@api/service/management/statisticalReport/query/QueryClassReportBySucnum'
  import PortalVo from '@api/service/common/online-school-config/vo/PortalVo'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'

  export class SchemeRanking {
    schemeName: string
    turnover: number
  }

  @Component
  export default class extends VueDecorator {
    now = new Date()
    // studentsNum = Mockjs.Random.integer(1000, 2000)
    // cumulativeNum = Mockjs.Random.integer(10000, 20000)
    // cumulativeEnrollNum = Mockjs.Random.integer(10000, 20000)
    // todayEnrollNum = Mockjs.Random.integer(1000, 20000)
    StaticticalReportManager = StaticticalReportManagerModule.queryStaticticalReportFactory.getQueryTrainClassReportList()
    StaticticalReportManager2 = StaticticalReportManagerModule.queryStaticticalReportFactory.getQueryTrainClassReportList()
    cumulativeEnrollNum = 0
    todayEnrollNum = 0
    getQueryTradeStatistic = StaticticalReportManagerModule.queryStaticticalReportFactory.getQueryTradeStatistic()
    QueryClassReportBySucnum = new QueryClassReportBySucnum()
    getCommodityReportSummaryInServicer = new TradeReportRequest()
    todayNum = [] as any
    yesterdayNum = [] as any
    yesterdayHistogramRequest = new TradeStatisticDateHistogramRequest()

    ranking = {}

    // 打开访问网校弹窗
    isShowStartDialog = false
    show = false
    close = false
    close1 = false
    // 网校配置概览
    configShow = false
    // 复制内容
    text = ''
    // 是否复制成功
    copy = false
    // web 链接
    http1 = 'http:wangxiao.59iedu.com'
    // h5链接
    http2 = 'http:h5wangxiao.59iedu.com'
    // 网校到期时间
    expire = 1
    webPortalInfo: PortalVo = OnlineSchoolConfigModule.queryPortal.webPortalInfo
    h5Url = ''

    async created() {
      // const hp = await QueryPortal.getPortalInfoInSubProject()
      //   this.close = !hp.webAccess
      //   this.close1 = !hp.h5Access
      this.http1 = window.location.origin
      this.http2 = this.http1 + '/h5'
      this.h5Url = this.webPortalInfo.mobileQRCode
      this.ranking = await this.QueryClassReportBySucnum.listCommodityOpenReportFormsBeSoldInServicer()
    }
    // h5页面链接生成二维码
    qrcode = 'https://www.baidu.com/' // url  el-image  src=qrcode
    async creatQrCode() {
      //   const qrcode = QRCode.toDataURL()
      this.qrcode = await QRCode.toDataURL(this.http2 + '?source=scan')
    }

    turnoveRanking(index: number) {
      switch (index) {
        case 0:
          return 'first'
        case 1:
          return 'second'
        case 2:
          return 'third'
        default:
          return ' '
      }
    }
    get today() {
      const todayHistogramRequest = new TradeStatisticDateHistogramRequest()
      todayHistogramRequest.statisticTime = new DateScopeRequest()
      const toDay = new Date()
      const year = toDay.getFullYear() //年
      const month = toDay.getMonth() + 1 < 10 ? '0' + (toDay.getMonth() + 1) : toDay.getMonth() + 1 //月
      const date = toDay.getDate() < 10 ? '0' + toDay.getDate() : toDay.getDate() //日
      todayHistogramRequest.timeUnit = 6
      todayHistogramRequest.interval = 2
      todayHistogramRequest.statisticTime.begin = year + '-' + month + '-' + date + ' ' + '00:00:00'
      todayHistogramRequest.statisticTime.end = year + '-' + month + '-' + date + ' ' + '24:00:00'
      return todayHistogramRequest
    }
    get yesterday() {
      const yesterdayHistogramRequest = new TradeStatisticDateHistogramRequest()
      yesterdayHistogramRequest.statisticTime = new DateScopeRequest()
      const toDay = new Date()
      const year = toDay.getFullYear() //年
      const month = toDay.getMonth() + 1 < 10 ? '0' + (toDay.getMonth() + 1) : toDay.getMonth() + 1 //月
      const date = toDay.getDate() < 10 ? '0' + toDay.getDate() : toDay.getDate() //日
      const yesterday = (date as number) - 1
      yesterdayHistogramRequest.timeUnit = 6
      yesterdayHistogramRequest.interval = 2
      yesterdayHistogramRequest.statisticTime.begin = year + '-' + month + '-' + yesterday + ' ' + '00:00:00'
      yesterdayHistogramRequest.statisticTime.end = year + '-' + month + '-' + yesterday + ' ' + '24:00:00'
      return yesterdayHistogramRequest
    }

    async mounted() {
      const todayHistogramRequest = this.today
      const yesterdayHistogramRequest = this.yesterday
      this.todayNum = await this.getQueryTradeStatistic.getTradeStatisticDateHistogramInServicer(todayHistogramRequest)
      this.yesterdayNum = await this.getQueryTradeStatistic.getTradeStatisticDateHistogramInServicer(
        yesterdayHistogramRequest
      )
      const day = new TradeReportRequest()
      day.tradeTime = this.today.statisticTime
      await this.StaticticalReportManager.getCommodityReportSummaryInServicer(this.getCommodityReportSummaryInServicer)
      const all = this.StaticticalReportManager.statisticsM
      this.cumulativeEnrollNum = all.tradeCountSummaryInfo.tradeSuccessCount
      console.log(all, 'all')
      await this.StaticticalReportManager2.getCommodityReportSummaryInServicer(day)
      const now = this.StaticticalReportManager2.statisticsM
      this.todayEnrollNum = now.tradeCountSummaryInfo.tradeSuccessCount
      console.log(now, 'now')
      await this.draw()
      // h5二维码
    }

    async activated() {
      this.creatQrCode()
      await this.draw()
    }

    draw() {
      const todayArr = [] as any
      for (let index = 0; index < this.todayNum.length; index++) {
        const num = this.todayNum[index].tradeSuccessAmount
        todayArr.push(num)
      }
      const yesterdayArr = [] as any
      for (let index = 0; index < this.yesterdayNum.length; index++) {
        const num = this.yesterdayNum[index].tradeSuccessAmount
        yesterdayArr.push(num)
      }

      const option = {
        xAxis: {
          type: 'category' as 'category',
          boundaryGap: true,
          data: ['2', '4', '6', '8', '10', '12', '14', '16', '18', '20', '22', '24']
        },
        yAxis: {
          type: 'value' as 'value',
          scale: true
        },
        tooltip: {
          trigger: 'axis' as 'axis'
        },
        legend: {
          data: ['今日', '昨日'],
          right: '6%',
          top: 'top',
          itemWidth: 15, //图例图标的宽
          itemHeight: 15, //图例图标的高
          textStyle: {
            color: '#3a6186',
            fontSize: 15
          }
        },
        series: [
          {
            name: '今日',
            colorBy: 'data',
            tooltip: {
              // 下面两个参数必须同时有，否则不生效
              trigger: 'item',
              formatter: function(params: any) {
                return params.name - 2 + '-' + params.name + '点交易金额:' + params.value
              }
            },
            symbol: 'circle',
            symbolSize: 3,
            smooth: true, //true 为平滑曲线，false为直线
            data: todayArr,
            type: 'line',
            itemStyle: {
              borderWidth: 0
            },
            areaStyle: {
              normal: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#FA5858' // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#FBEFEF' // 100% 处的颜色
                    }
                  ],
                  global: false // 缺省为 false
                }
              }
            }
          },
          {
            name: '昨日',
            colorBy: 'data',
            tooltip: {
              // 下面两个参数必须同时有，否则不生效
              trigger: 'item',
              formatter: function(params: any) {
                return params.name - 2 + '-' + params.name + '点交易金额:' + params.value
              }
            },
            symbol: 'circle',
            symbolSize: 3,
            smooth: true, //true 为平滑曲线，false为直线
            data: yesterdayArr,
            type: 'line',
            itemStyle: {
              borderWidth: 0
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#00BFFF' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#FBEFEF' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          }
        ]
      }

      const chart = echarts.init(document.getElementById('chart') as HTMLDivElement)

      chart.setOption(option)
      //图形宽度随屏幕宽度改变而改变
      window.onresize = function() {
        chart.resize()
      }
    }

    get title() {
      return rootModule.title
    }
    get date() {
      const Week = ['日', '一', '二', '三', '四', '五', '六']
      const year = this.now.getFullYear() //年
      const month = this.now.getMonth() + 1 //月
      const day = this.now.getDate() //日
      const ww = Week[this.now.getDay()]
      return year + '年' + month + '月' + day + '日' + '周' + ww
    }

    openNoviceGuide() {
      UiModule.changeGuideState(true)
    }

    open(value: number) {
      this.copy = true
      if (value == 1) {
        this.text = '复制成功！' // 复制功能实现
        const url = this.http1
        const _input = document.createElement('input') // 直接构建input
        _input.value = url // 设置内容
        document.body.appendChild(_input) // 添加临时实例
        _input.select() // 选择实例内容
        document.execCommand('Copy') // 执行复制
        document.body.removeChild(_input) // 删除临时实例
        this.$message.success('复制成功')
      } else if (value == 2) {
        // 复制功能实现
        this.text = '复制成功！您可在手机浏览器打开，建议使用xxx？待确认'
        const url = this.http2
        const _input = document.createElement('input') // 直接构建input
        _input.value = url // 设置内容
        document.body.appendChild(_input) // 添加临时实例
        _input.select() // 选择实例内容
        document.execCommand('Copy') // 执行复制
        document.body.removeChild(_input) // 删除临时实例
        this.$message.success('复制成功')
      } else {
        this.$router.push('/basic-data/platform/basic-info')
      }
      setTimeout(() => {
        this.copy = false
      }, 3000)
    }

    visit() {
      this.isShowStartDialog = false
      this.$router.push('/basic-data/platform/basic-info')
    }
    to() {
      this.$router.push('/basic-data/platform/basic-info')
    }
    // 网校配置概览按钮跳转路径
    // setting(val: number) {
    //   if (val == 1) {
    //     this.$router.push('/basic-data/platform/basic-info')
    //   } else if (val == 2) {
    //     this.$router.push('/basic-data/platform/basic-info')
    //   } else if (val == 3) {
    //     this.$router.push('/basic-data/platform/function')
    //   } else if (val == 4) {
    //     this.$router.push('/basic-data/trade/account')
    //   } else if (val == 5) {
    //     this.$router.push('/basic-data/trade/apply')
    //   } else if (val == 6) {
    //     this.$router.push('/basic-data/distribution-channel')
    //   } else if (val == 7) {
    //     this.$router.push('/resource/courseware')
    //   } else if (val == 8) {
    //     this.$router.push('/resource/course')
    //   } else if (val == 9) {
    //     this.$router.push('/resource/question')
    //   } else if (val == 10) {
    //     this.$router.push('/resource/exam-paper')
    //   } else if (val == 11) {
    //     this.$router.push('/training/course-package')
    //   } else if (val == 12) {
    //     this.$router.push('/training/scheme/manage')
    //   }
    // }
    buttonType(val: number) {
      if (val == 1) {
        return 'primary'
      }
      if (val == 2) {
        return 'info'
      }
    }
  }
</script>

<style lang="scss">
  .center-box {
    .border-box-content {
      display: flex;
      align-items: center;
    }
  }

  .dv-capsule-chart {
    &::-webkit-scrollbar,
    &::-webkit-scrollbar-track {
      background-color: #092a48;
    }

    &::-webkit-scrollbar {
      width: 3px;
      height: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #16344e;
    }

    .capsule-container,
    .label-column {
      justify-content: center;
    }
  }

  .dv-loading {
    justify-content: flex-start;
    padding-top: 300px;
    position: absolute;
    background: #000000a1;
    z-index: 11;
    bottom: 0;
    right: 0;
    left: 0;
    top: 0;
  }

  .dv-water-pond-level canvas {
    width: 130px;
    height: 130px;
  }

  .dv-water-pond-level ellipse,
  .dv-water-pond-level rect {
    fill: none;
    stroke-width: 6;
  }
  .m-visit {
    font-size: 16px;
    width: 550px;
    height: 500px;
    padding: 0 0;
    .top {
      line-height: 36px;
      padding: 10px 20px;
      background-color: #f5f7fa;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .web {
      margin-top: 30px;
      margin-bottom: 15px;
      .left {
        margin-right: 50px;
      }
    }
    .h5 {
      display: flex;
      justify-content: center;
      align-items: center;
      .h5-yemian {
        position: relative;
        div {
          margin-top: -25px;
        }
        .btn2 {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 999;
          text-align: center;
          color: #333333;
          background-color: #5e6ed2;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
      .right {
        // width: 500px;
        // height: 437px;
        margin-left: 15px;
        // img {
        //   width: auto;
        //   height: auto;
        // }
      }
    }
  }
  .web2 {
    margin-top: 150px;
    margin-bottom: 15px;
    .left {
      margin-right: 50px;
    }
  }
  .btn {
    text-align: center;
    color: #333333;
    background-color: #5e6ed2;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 70px;
  }
  .btn:hover {
    color: #333333;
    background-color: #5e6ed2;
  }
  .box {
    width: 455px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    color: #fff;
    background-color: #5388ef;
    margin-bottom: -150px;
    margin-top: 140px;
  }
  .copybtn {
    width: 160px;
    margin-left: -42px;
    font-size: 16px;
  }
  .copybtn2 {
    width: 160px;
    margin-left: -35px;
    font-size: 16px;
  }
</style>
