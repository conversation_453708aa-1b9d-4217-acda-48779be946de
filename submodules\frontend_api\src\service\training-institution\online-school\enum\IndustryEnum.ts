/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2024-08-16 09:21:35
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-08-20 18:29:29
 */
/**
 * 行业id枚举
 */
import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum IndustryIdEnum {
  /**
   * 人社行业
   */
  RS = 'industry0221018501809dc4d43e0003',

  /**
   * 建设行业
   */
  JS = 'industry0221018501809dc4d43e0002',

  /**
   * 卫生行业
   */
  WS = 'industry0221018501809dc4d43e0004',

  /**
   * 工勤行业
   */
  GQ = 'industry0221018501809dc4d43e0005',

  /**
   * 教师行业
   */
  LS = 'industry0221018501809dc4d43e0006',

  /**
   * 药师行业
   */
  YS = 'industry0221018501809dc4d43e0007'
}

export default class IndustryEnum extends AbstractEnum<IndustryIdEnum> {
  static enum = IndustryIdEnum

  constructor(status?: IndustryIdEnum) {
    super()
    this.current = status
    this.map.set(IndustryIdEnum.RS, '人社行业')
    this.map.set(IndustryIdEnum.JS, '建设行业')
    this.map.set(IndustryIdEnum.WS, '职业卫生行业')
    this.map.set(IndustryIdEnum.GQ, '工勤行业')
    this.map.set(IndustryIdEnum.LS, '教师行业')
    this.map.set(IndustryIdEnum.YS, '药师行业')
  }
}
