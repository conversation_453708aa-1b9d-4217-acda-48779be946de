<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">课件管理</el-breadcrumb-item>
      <el-breadcrumb-item>外链课件导入</el-breadcrumb-item>
    </el-breadcrumb>
    <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
      <p>温馨提示：</p>
      <p>
        1.平台仅支持外链课件的批量导入功能，在导入请下载[导入外链课件模板]，并严格根据表格内容填写保存后再导入系统；
      </p>
      <p>2.导入表格一次最多支持1000条记录，若超过1000条则不能正常导入；</p>
      <p>3.导入后可以通过“导入任务查看” 查看并确认导入结果；</p>
      <p>4.仅支持新建课件，已存在的课件无法通过导入修改；</p>
    </el-alert>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-row type="flex" justify="center">
          <el-col :sm="14" :lg="10">
            <el-steps direction="vertical" :active="2" class="m-vertical-steps">
              <el-step title="下载导入外链课件模板，填写要求信息">
                <div slot="description">
                  <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-download">
                    导入外链课件模板
                  </el-button>
                </div>
              </el-step>
              <el-step title="上传填写好的外链课件导入表格">
                <div slot="description">
                  <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                    <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                      选择文件
                    </el-button>
                  </el-upload>
                </div>
              </el-step>
            </el-steps>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc">
        <el-button>返回上一级</el-button>
        <el-button type="primary">上传</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
