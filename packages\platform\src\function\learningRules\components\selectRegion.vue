<template>
  <el-drawer
    title="选择地区"
    :visible.sync="openDialog"
    size="900px"
    :append-to-body="true"
    custom-class="m-drawer m-table-auto"
    @close="close()"
  >
    <div class="drawer-bd">
      <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
        同一个适用行业下，一个地区只能配置一个规则，地区规则按区县级设置。
      </el-alert>
      <el-row :gutter="0" class="m-query is-border-bottom">
        <el-form :inline="true" label-width="auto">
          <el-col :span="12">
            <el-form-item label="地区">
              <district-selector
                v-model="regionValue"
                :check-strictly="true"
                placeholder="请选择地区"
                :basicInfo="basicInfo"
                @regionCode="regionCall"
                @clear="clear"
              ></district-selector>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>

      <div class="f-flex f-justify-between f-mb10">
        <div class="f-c3">
          已选择 <span class="f-fb f-co">{{ accont }}</span> 区县
        </div>
        <el-checkbox v-model="isExclude" class="f-pr10" @change="changeCullingStatus"
          >剔除已配置监管规则的方案</el-checkbox
        >
      </div>
      <!--表格-->
      <el-table stripe :data="pagRegionTreeList" class="m-table">
        <el-table-column type="index" label="No." width="60"></el-table-column>
        <el-table-column label="省份" min-width="150">
          <template v-slot="{ row }" v-if="!diffRegion"> {{ row.names[0] || '-' }}</template>
          <template v-else> - </template>
        </el-table-column>
        <el-table-column label="地市" min-width="150">
          <template v-slot="{ row }" v-if="!diffRegion"> {{ row.names[1] || '-' }}</template>
          <template v-slot="{ row }" v-else> {{ row.names[0] || '-' }}</template>
        </el-table-column>
        <el-table-column label="区县" min-width="240">
          <template v-slot="{ row }" v-if="!diffRegion"> {{ row.names[2] || '-' }}</template>
          <template v-slot="{ row }" v-else> {{ row.names[1] || '-' }}</template>
        </el-table-column>
        <el-table-column label="操作" min-width="100" align="center">
          <template v-slot="{ row }">
            <div v-if="row.checked">
              <el-button type="text" size="mini" @click="check(row)">取消选择</el-button>
            </div>
            <div v-else>
              <el-button type="text" size="mini" @click="check(row)">选择</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="openDialog = false">取消</el-button>
      <el-button @click="sure" type="primary">确定</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import DistrictSelector from '@hbfe/jxjy-admin-platform/src/function/learningRules/components/districtSelector.vue'
  import BasicInfo from '@api/service/management/learning-rule/model/BasicInfo'
  import { UiPage } from '@hbfe/common'
  import RegionTreeItem from '@api/service/management/learning-rule/model/RegionTreeItem'

  @Component({
    components: { DistrictSelector }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.pageData, this.pageData)
    }
    // 接收基础信息
    @Prop({
      type: BasicInfo,
      default: () => new BasicInfo()
    })
    basicInfo: BasicInfo

    // 分页
    page: UiPage

    // 打开抽屉
    openDialog = false

    // 地区code
    regionValue: Array<string> = []

    //地区树列表
    regionTreeList: Array<RegionTreeItem> = []

    // 分页地区树列表
    pagRegionTreeList: Array<RegionTreeItem> = []

    // 切换分页时需要缓存上一次的code
    stagCode = ''

    //剔除字段
    isExclude = false

    // 区县总数
    accont = 0

    // 抽屉销毁时的回调
    close() {
      // 初始化列表
      this.regionValue = []
      this.regionTreeList = []
      this.pagRegionTreeList = []
      this.accont = 0
      this.stagCode = ''
      this.isExclude = false
      this.page.currentChange(1)
    }

    // 点击选择
    check(row: RegionTreeItem) {
      row.checked = !row.checked
      this.accont = this.regionTreeList.filter((item) => item.checked).length
    }
    // 清空列表
    clear() {
      this.close()
    }

    // 确认方法
    sure() {
      const regionList = this.regionTreeList.filter((item) => item.checked).map((item) => item.code)
      if (!regionList.length) {
        this.$message.error('请至少选择一个地区进行选择！')
        return
      }
      this.basicInfo.selectRegion(regionList)
      this.$emit('queryRegionList')
      this.openDialog = false
    }

    // 查询列表方法
    async query() {
      this.regionCall(this.stagCode)
    }
    // 更改剔除状态
    async changeCullingStatus() {
      await this.basicInfo.getExcludeRegions()
      this.regionTreeList = this.basicInfo.getRegionListDialog(this.stagCode, this.isExclude)
      this.regionTreeList.forEach((item) => {
        item.checked = true
      })
      this.page.currentChange(1)
    }

    // 地区回调
    regionCall(value: string) {
      // 切换分页时需要缓存上一次的code
      if (!value) {
        this.pagRegionTreeList = []
      }
      if (value.slice(4, 6) == '00' && this.pagRegionTreeList.length) {
        this.$confirm('切换省、市会导致已经选择的地区清除，确认选择新的省、市吗？', {
          showCancelButton: true
        })
          .then(() => {
            this.regionOperation(value)
          })
          .catch(() => {
            this.regionValue = []
          })
      } else {
        this.regionOperation(value)
      }
    }
    // 地区处理
    regionOperation(value: string) {
      this.stagCode = value
      this.regionTreeList = this.basicInfo.getRegionListDialog(value, this.isExclude)
      this.regionTreeList.forEach((item) => {
        item.checked = true
      })
      this.accont = this.regionTreeList.filter((item) => item.checked).length

      this.page.currentChange(1)
    }

    // ui 分页方法
    async pageData() {
      const filterList =
        this.regionTreeList?.slice(
          this.page.pageSize * (this.page.pageNo - 1),
          this.page.pageSize * this.page.pageNo
        ) || []
      this.page.totalSize = this.regionTreeList.length
      this.page.totalPageSize = Math.ceil(this.regionTreeList.length / this.page.pageSize)
      this.pagRegionTreeList = filterList
    }

    // 差异化地区，北京、上海、天津、重庆、澳门、香港
    get diffRegion() {
      return (
        this.regionValue[0] == '110000' ||
        this.regionValue[0] == '310000' ||
        this.regionValue[0] == '120000' ||
        this.regionValue[0] == '500000' ||
        this.regionValue[0] == '810000' ||
        this.regionValue[0] == '820000'
      )
    }
  }
</script>
