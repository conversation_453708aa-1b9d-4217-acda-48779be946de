import MsSchemeLearningQueryBackstageGateway, {
  CertificateTemplateResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import CertificateTemplateResponseVo from './vo/CertificateTemplateResponseVo'
import { Response, UiPage } from '@hbfe/common'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
import ElectronicSealResponseVo from './vo/ElectronicSealResponseVo'
/*
  培训证明模板，电子章
*/
class QueryCertificateTemplate {
  /**
   * @description: 分页获取培训证明模板列表
   * @return {*}
   */
  async queryCertificateTemplateList(page: UiPage): Promise<Response<Array<CertificateTemplateResponseVo>>> {
    const res = await MsSchemeLearningQueryBackstageGateway.pageCertificateTemplate(page)
    const response = new Response<Array<CertificateTemplateResponseVo>>()
    if (!res?.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    page.totalSize = res.data?.totalSize
    page.totalPageSize = res.data?.totalPageSize

    const resList = new Array<CertificateTemplateResponseVo>()
    // 所属行业id集合
    const belongsIndustryIdList = new Array<string>()
    res.data?.currentPageData?.forEach((item: CertificateTemplateResponse) => {
      const temp = new CertificateTemplateResponseVo()
      temp.from(item)
      belongsIndustryIdList.push(item.belongsIndustryId)
      resList.push(temp)
    })
    // 获取所属行业名称
    const list = await this.queryIndustryName(belongsIndustryIdList)
    console.log(list, 'list')
    if (list?.length) {
      resList?.forEach((far: CertificateTemplateResponseVo) => {
        list?.forEach((sub: IndustryVo) => {
          if (far.belongsIndustryId === sub.id) {
            far.belongsIndustryName = sub.name
          }
        })
      })
    }
    response.data = resList
    response.status = res.status
    return response
  }

  /**
   * @description: 根据证书模板id获取证书详情
   * @param {string} certificateTemplateId
   */
  async queryCertificateTemplate(certificateTemplateId: string): Promise<Response<CertificateTemplateResponseVo>> {
    const res = await MsSchemeLearningQueryBackstageGateway.getCertificateTemplate(certificateTemplateId)
    const response = new Response<CertificateTemplateResponseVo>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    const responseData = new CertificateTemplateResponseVo()
    responseData.from(res?.data)
    response.data = responseData
    response.status = res.status
    return response
  }

  /**
   * @description: 获取电子章详情
   */
  async queryElectronicSealInServicer(): Promise<Response<ElectronicSealResponseVo>> {
    const res = await MsSchemeLearningQueryBackstageGateway.getElectronicSealInServicer()
    const response = new Response<ElectronicSealResponseVo>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.status = res.status
    response.data = new ElectronicSealResponseVo()
    response.data.from(res.data)
    return response
  }

  /**
   * @description: 根据所属行业id查询名称
   * @param {Array} idList
   */
  private async queryIndustryName(idList: Array<string>): Promise<Array<IndustryVo>> {
    const tempList = [...new Set(idList)]
    const list = await BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustry.queryIndustryByIdList(
      tempList
    )
    if (!list?.length) {
      return new Array<IndustryVo>()
    }
    return list
  }
}

export default QueryCertificateTemplate
