import axios from 'axios'
import WebFunny, { webfunnyDefaultUserId } from './index'
import QueryUserInfo from '@api/service/customer/user/query/QueryUserInfo'

const request = axios.create()
declare const wx: any

export enum BehaviorResultEnum {
  success = 'success',
  failed = 'failed'
}

export enum UploadType {
  'customize_behavior' = 'CUSTOMIZE_BEHAVIOR',
  'customer_pv' = 'CUSTOMER_PV'
}

const _isWx = window?.navigator ? false : true

// * pv日志模型
export class PvInfo {
  //【非空】探针标识
  webMonitorId = sessionStorage.CUSTOMER_WEB_MONITOR_ID
  //【非空】 用户的id
  userId = QueryUserInfo.basicUserInfo.userId || webfunnyDefaultUserId
  //【非空】 日志类型
  uploadType: UploadType = UploadType.customer_pv
  //【非空】 日志产生时间的毫秒数
  happenTime: number = new Date().getTime()
  //【非空】 用户的唯一标识, 可以用（userId + 日期）拼接， 用户启动程序就将customerKey的值存放在本地，下次进入取本地的customerKey
  customerKey = `${QueryUserInfo.basicUserInfo.userId}_${new Date().getTime()}` || webfunnyDefaultUserId
  //【非空】 页面的url
  completeUrl = encodeURIComponent(window.location.href)
  //【非空】 n_uv（新用户）、o_uv（老用户今天首次进入）、o（老用户今天非首次进入）
  newStatus = 'o'
  // 设备名称
  deviceName = 'unknown'
  // 设备尺寸
  deviceSize = `unknown`
  // 系统信息
  os = 'unknown'
  // 浏览器名称
  browserName = `unknown`
  // 浏览器版本
  browserVersion = 'unknown'
}

// * 自定义日志模型
class WebfunnyUpMyLog {
  enable = WebFunny.config.enable
  //【非空】探针标识
  webMonitorId = sessionStorage.CUSTOMER_WEB_MONITOR_ID
  //【非空】 用户的id
  userId = QueryUserInfo.basicUserInfo.userId || webfunnyDefaultUserId
  //【非空】 日志类型
  uploadType: UploadType = UploadType.customize_behavior
  //【非空】 日志产生时间的毫秒数
  happenTime = new Date().getTime()
  //【非空】行为类型描述，如：点击了
  behaviorType = 'new_watch_log'
  // 【非空】行为产生的结果。传值为：success 或 failed
  behaviorResult: BehaviorResultEnum = BehaviorResultEnum.success
  // 自定义行为描述]
  description = ''

  private readonly _host: string = WebFunny.config.host

  get host(): string {
    return this._host
  }

  setUserId() {
    this.userId = QueryUserInfo.basicUserInfo.userId || webfunnyDefaultUserId
  }
}

export class Description {
  service: string = location.href
  userId: string = QueryUserInfo.basicUserInfo.userId
  account: string = QueryUserInfo.userInfo.userInfo.idCard || QueryUserInfo.userInfo.userInfo.phone
  message?: string = null
  response: any = null
  params: any = null
}

const getDeviceInfo = () => {
  const mobileFlags = [
    /AppleWebKit.*Mobile.*/gi, // 移动终端
    /\(i[^;]+;( U;)? CPU.+Mac OS X/gi, // ios终端
    /Android/gi, // 安卓终端
    /iPhone/gi, // iPhone
    /iPad/gi // iPad
  ]

  const deviceInfo: {
    os: string
    deviceName: string
  } = { os: 'unknown', deviceName: 'unknown' }

  const ua = navigator.userAgent.toLowerCase()

  if (mobileFlags[0].test(ua) || mobileFlags[2].test(ua)) {
    deviceInfo.deviceName = 'android'
    deviceInfo.os = 'phone'
  } else if (mobileFlags[1].test(ua) || mobileFlags[3].test(ua) || mobileFlags[4].test(ua)) {
    deviceInfo.deviceName = 'ios'
    deviceInfo.os = 'phone'
  } else {
    deviceInfo.deviceName = 'pc'
    deviceInfo.os = 'web'
  }
  return deviceInfo
}

const getBrowserInfo = () => {
  const ua = navigator.userAgent.toLocaleLowerCase()
  const browserInfo: {
    browserName: string
    browserVersion: string
  } = { browserName: 'unknown', browserVersion: 'unknown' }
  // * ie
  let uaMatch = ua.match(/(?:msie|trident)\/([\d.]+)/)
  if (uaMatch?.length) {
    browserInfo.browserName = 'edge'
    browserInfo.browserVersion = uaMatch[1] || 'unknown'
    return browserInfo
  }
  // * firefox
  uaMatch = ua.match(/firefox\/([\d.]+)/)
  if (uaMatch?.length) {
    browserInfo.browserName = 'firefox'
    browserInfo.browserVersion = uaMatch[1] || 'unknown'
    return browserInfo
  }
  uaMatch = ua.match(/ucbrowser\/([\d.]+)/)
  if (uaMatch?.length) {
    browserInfo.browserName = 'UC'
    browserInfo.browserVersion = uaMatch[1] || 'unknown'
    return browserInfo
  }
  uaMatch = ua.match(/(?:opera|opr)\/([\d.]+)/)
  if (uaMatch?.length) {
    browserInfo.browserName = 'opera'
    browserInfo.browserVersion = uaMatch[1] || 'unknown'
    return browserInfo
  }
  uaMatch = ua.match(/bidubrowser\/([\d.]+)/)
  if (uaMatch?.length) {
    browserInfo.browserName = 'baidu'
    browserInfo.browserVersion = uaMatch[1] || 'unknown'
    return browserInfo
  }
  uaMatch = ua.match(/metasr\/([\d.]+)/)
  if (uaMatch?.length) {
    browserInfo.browserName = 'sougou'
    browserInfo.browserVersion = uaMatch[1] || 'unknown'
    return browserInfo
  }
  uaMatch = ua.match(/(?:tencenttraveler|qqbrowse)\/([\d.]+)/)
  if (uaMatch?.length) {
    browserInfo.browserName = 'QQ'
    browserInfo.browserVersion = uaMatch[1] || 'unknown'
    return browserInfo
  }
  uaMatch = ua.match(/maxthon\/([\d.]+)/)
  if (uaMatch?.length) {
    browserInfo.browserName = 'maxthon'
    browserInfo.browserVersion = uaMatch[1] || 'unknown'
    return browserInfo
  }
  uaMatch = ua.match(/chrome\/([\d.]+)/)
  console.log('uaMatch', uaMatch)
  if (uaMatch?.length) {
    browserInfo.browserName = 'chrome'
    browserInfo.browserVersion = uaMatch[1] || 'unknown'
    return browserInfo
  }
  uaMatch = ua.match(/version\/([\d.]+).*safari/)
  if (uaMatch?.length) {
    browserInfo.browserName = 'Safari'
    browserInfo.browserVersion = uaMatch[1] || 'unknown'
    return browserInfo
  }
  browserInfo.browserName = 'others'
  browserInfo.browserVersion = '1.0.0'
  return browserInfo
}

export const upMyPvLog = async () => {
  const pvInfo = new PvInfo()
  if (_isWx) {
    const systemInfo = await wx.getSystemInfoSync()
    const screenHeight = systemInfo.screenHeight || null
    const screenWidth = systemInfo.screenWidth || null
    pvInfo.deviceSize = screenWidth && screenHeight ? `${screenWidth}x${screenHeight}` : 'unknown'
    pvInfo.deviceName = systemInfo.model || 'unknown'
    pvInfo.os = systemInfo.system || 'unknown'
  } else {
    const screenHeight = window.screen.height || null
    const screenWidth = window.screen.width || null
    pvInfo.deviceSize = screenWidth && screenHeight ? `${screenWidth}x${screenHeight}` : 'unknown'
    pvInfo.browserName = getBrowserInfo().browserName || 'unknown'
    pvInfo.browserVersion = getBrowserInfo().browserVersion || 'unknown'
    pvInfo.os = getDeviceInfo().os || 'unknown'
    pvInfo.deviceName = getDeviceInfo().deviceName || 'unknown'
  }
  console.log('pvInfo', pvInfo)
  return request.post(`https://${WebFunny.config.host}/server/upMyLog`, {
    logs: [pvInfo]
  })
}
export const upMyLog = (description: Description, type: string, result?: BehaviorResultEnum) => {
  // * todo webfunny报错引起 检测数据 有就把之前报错的打过去
  const event = new WebfunnyUpMyLog()
  // if (!window.webfunny) return
  if (!event.enable) {
    return
  }
  event.description = JSON.stringify(description)
  event.behaviorType = type

  if (result) {
    event.behaviorResult = result
  }

  return request.post(`https://${event.host}/server/upMyLog`, {
    logs: [event]
  })
}

export default WebfunnyUpMyLog
