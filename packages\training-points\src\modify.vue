<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/training-points' }">培训点管理</el-breadcrumb-item>
      <el-breadcrumb-item>{{ title }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card">
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form
              ref="trainingPointInfoForm"
              :model="trainingPointInfoInfo"
              :rules="rules"
              label-width="auto"
              class="m-form"
            >
              <el-form-item label="培训点名称：" prop="trainingPlaceName">
                <el-input v-model="trainingPointInfoInfo.trainingPlaceName" clearable placeholder="请输入培训点名称" />
              </el-form-item>
              <el-form-item
                :label="changeType === 'add' ? '请从地图中选择具体定位地点：' : '具体定位地点:'"
                prop="chooseAddress"
              >
                <div class="m-map-sel">
                  <div class="search">
                    <!-- <el-input v-model="trainingPointInfoInfo.selectAddress" /> -->
                    <map-autoComplete
                      v-if="changeType === 'add'"
                      v-model="trainingPointInfoInfo.chooseAddress"
                      :disabled="changeType !== 'add'"
                      @getLocation="getLocation"
                      placeholder="请输入具体定位地点"
                    ></map-autoComplete>

                    <!-- <el-button type="primary">搜索</el-button> -->
                  </div>
                  <div class="content">
                    <!-- <img src="@design/admin/assets/images/demo-invoice.png" /> -->
                    <Map
                      :locationDetail="locationDetail"
                      ref="map"
                      @point="pointChoose"
                      :isUserInput="isUserInput"
                      :isMapClickEnabled="changeType === 'add'"
                    ></Map>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="您选中的培训点地址：" prop="selectAddress">
                <el-input
                  v-model="trainingPointInfoInfo.selectAddress"
                  clearable
                  placeholder="请输入培训点地址"
                  :disabled="changeType !== 'add'"
                />
              </el-form-item>
              <el-form-item label="培训点所在地区：" prop="trainingPlaceRegionIds">
                <!--可搜索 filterable-->
                <biz-region-cascader
                  :check-strictly="true"
                  placeholder="请选择地区"
                  v-model="trainingPointInfoInfo.trainingPlaceRegionIds"
                ></biz-region-cascader>
              </el-form-item>
              <el-form-item label="培训教室：" prop="classroomName">
                <el-input v-model="trainingPointInfoInfo.classroomName" clearable placeholder="请输入教室名称" />
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar is-sticky-1 f-tc" style="z-index: 999">
        <el-button @click="back">取消</el-button>
        <el-button type="primary" @click.stop="submitForm" v-loading="saveLoading">{{
          title === '新增培训点' ? '创建' : '编辑'
        }}</el-button>
      </div>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import Map from './component/map.vue'
  import QueryRegionInfo from '@api/service/common/tx-map/QueryRegionInfo'
  import TrainingPlaceManage from '@api/service/management/resource/training-place-manage/TrainingPlaceManage'
  import TrainingPlaceInfo from '@api/service/management/resource/training-place-manage/TrainingPlaceInfo'
  import MapAutoComplete from './component/map-autoComplete.vue'
  import { bind, debounce } from 'lodash-decorators'

  @Component({
    components: {
      Map,
      MapAutoComplete
    }
  })
  export default class extends Vue {
    @Ref('trainingPointInfoForm') trainingPointInfoForm: any
    @Ref('map') map: Map
    query: Query = new Query()
    constructor() {
      super()
    }

    form: any = {}
    cascader: any = []
    input = ''
    saveLoading = false
    select = ''
    tableData = [{}, {}]
    locationDetail: {
      latitude: number
      longitude: number
      workPlace: string
      zoom: number
    } = {
      latitude: null,
      longitude: null,
      workPlace: null,
      zoom: null
    }
    queryRegionInfo = new QueryRegionInfo()
    title = ''
    isUserInput = false

    trainingPointManage = new TrainingPlaceManage()
    trainingPointInfoInfo = new TrainingPlaceInfo()
    changeType = ''
    // 表单验证规则
    rules = {
      trainingPlaceName: [
        { required: true, message: '请输入培训点名称', trigger: 'blur' },
        { validator: this.validateTrainingPlaceName, trigger: 'blur' }
      ],
      chooseAddress: [{ required: true, message: '请输入具体定位地点', trigger: ['blur', 'change'] }],
      selectAddress: [{ required: true, message: '请输入培训点地址', trigger: 'blur' }],
      trainingPlaceRegionIds: [{ required: true, message: '请选择地区', trigger: 'change' }],
      classroomName: [{ required: true, message: '请输入教室名称', trigger: 'blur' }]
    }
    async validateTrainingPlaceName(rule: any, value: any, callback: any) {
      if (!value) {
        return callback(new Error('培训点名称不能为空'))
      }

      try {
        const res = await this.trainingPointInfoInfo.checkTrainingNameIsExist()
        if (res) {
          callback(new Error('已存在相同培训点，请重新输入！'))
        } else {
          callback() // 校验通过
        }
      } catch (error) {
        console.error('校验失败:', error)
        callback(new Error('已存在相同培训点，请重新输入！'))
      }
    }

    created() {
      //   this.mapConversion()
      this.changeType = this.$route.params.type
      console.log(this.changeType, '====changeType====')
      if (this.$route.params.type === 'add') {
        this.title = '新增培训点'
      } else {
        this.title = '编辑培训点'
        this.queryDetail(`${this.$route.query.id}`)
        this.rules.chooseAddress = null
      }
    }

    async queryDetail(id: string) {
      try {
        await this.trainingPointInfoInfo.queryDetail(id)
        this.locationDetail.latitude = this.trainingPointInfoInfo.latitude
        this.locationDetail.longitude = this.trainingPointInfoInfo.longitude
        this.locationDetail.zoom = 17.2
        this.$nextTick(() => {
          this.map.drawerOpened()
        })
      } catch (e) {
        console.log(e)
      }
    }

    // async mapConversion() {

    //   this.locationDetail.latitude = res.lat
    //   this.locationDetail.longitude = res.lng
    // }

    getLocation(val: any) {
      this.trainingPointInfoInfo.latitude = val.location.lat
      this.trainingPointInfoInfo.longitude = val.location.lng
      this.trainingPointInfoInfo.selectAddress = val.address
      this.locationDetail.latitude = val.location.lat
      this.locationDetail.longitude = val.location.lng
      this.locationDetail.zoom = val.zoom ? val.zoom : 17.2
      this.isUserInput = true
      this.$nextTick(() => {
        this.map.drawerOpened()
      })
    }

    @bind
    @debounce(200)
    async createTrainingPoints() {
      const res = await this.trainingPointInfoInfo.createTrainingPlace()
      if (res.data.code === '200') {
        this.$message.success('操作成功')
        this.$router.push({
          path: '/resource/training-points'
        })
        this.saveLoading = false
      } else {
        this.saveLoading = false
        // this.$message.error(`${res.status.message}`)
      }
    }
    @bind
    @debounce(200)
    async editTrainingPoints() {
      const res = await this.trainingPointInfoInfo.editTrainingPlace()
      if (res.data.code === '200') {
        this.$message.success('操作成功')
        this.$router.push({
          path: '/resource/training-points'
        })
        this.saveLoading = false
      } else {
        this.saveLoading = false
        // this.$message.error(`${res.status.message}`)
      }
    }
    @bind
    @debounce(200)
    back() {
      this.$router.go(-1)
    }
    @bind
    @debounce(500)
    submitForm() {
      if (this.saveLoading) return
      this.saveLoading = true
      try {
        this.trainingPointInfoForm.validate(async (valid: boolean) => {
          if (valid) {
            if (this.$route.params.type === 'add') {
              const res = await this.trainingPointInfoInfo.createTrainingPlace()
              if (res.data.code === '200') {
                this.$message.success('操作成功')
                this.$router.push({
                  path: '/resource/training-points'
                })
              } else {
                this.saveLoading = false
              }
            } else {
              const res = await this.trainingPointInfoInfo.editTrainingPlace()
              if (res.data.code === '200') {
                this.$message.success('操作成功')
                this.$router.push({
                  path: '/resource/training-points'
                })
              } else {
                this.saveLoading = false
              }
            }
          } else {
            this.$message.warning('请填写必填项！')
            this.saveLoading = false
          }
        })
      } catch (error) {
        this.saveLoading = false
      }
    }

    /**
     *
     * 地图选位
     */
    pointChoose(val: any) {
      this.trainingPointInfoInfo.latitude = val.latLng.lat
      this.trainingPointInfoInfo.longitude = val.latLng.lng
      this.trainingPointInfoInfo.selectAddress = val.address
      this.trainingPointInfoInfo.chooseAddress = val.address
    }
  }
</script>
