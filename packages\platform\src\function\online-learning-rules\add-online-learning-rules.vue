<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/basic-data/platform/function' }">学习规则</el-breadcrumb-item>
      <el-breadcrumb-item>添加学习规则</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header" v-loading="isLoading">
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">基础设置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit f-p20">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="basicRef" :model="basicInfoForm" label-width="auto" class="m-form" :rules="rules">
              <el-form-item label="适用行业：" required>
                <el-checkbox-group v-model="basicInfoForm.industryIdList">
                  <el-checkbox
                    v-for="item in industryList"
                    :label="item.id"
                    :key="item.id"
                    @change="
                      (value) => {
                        return changeindustryIdList(value, item.id)
                      }
                    "
                    >{{ item.name }}</el-checkbox
                  >
                </el-checkbox-group>
                <div class="m-left-divider" v-show="hasIndustry(IndustryIdEnum.RS)">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">人社行业</i></el-divider>
                  <el-form
                    ref="rsForm"
                    :model="basicInfoForm.RSProperty"
                    label-width="150px"
                    class="m-form is-mb f-ptb10 f-mb20"
                    :rules="rules"
                  >
                    <el-form-item label="年度：" prop="year">
                      <year-select
                        placeholder="请选择培训年度"
                        :select-type="'year'"
                        :industry-id="getIndustryId(IndustryIdEnum.RS)"
                        v-model="basicInfoForm.RSProperty.year"
                        @removeSku="removeSku"
                      >
                      </year-select>
                    </el-form-item>
                    <el-form-item label="科目类型：" prop="subjectType">
                      <accounttype-select
                        placeholder="请选择科目类型"
                        :select-type="'subjectType'"
                        v-model="basicInfoForm.RSProperty.subjectType"
                        :industry-id="getIndustryId(IndustryIdEnum.RS)"
                        :industry-property-id="getindustryPropertyId(IndustryIdEnum.RS)"
                        @removeSku="removeSku"
                      >
                      </accounttype-select>
                    </el-form-item>
                    <el-form-item label="培训专业：" prop="trainingProfessional">
                      <training-major-select
                        placeholder="请选择培训专业"
                        :select-type="'trainingMajor'"
                        v-model="trainingMajor"
                        :industry-id="getIndustryId(IndustryIdEnum.RS)"
                        :industry-property-id="getindustryPropertyId(IndustryIdEnum.RS)"
                        @removeSku="removeSku"
                        @changeValue="changeValue"
                      >
                      </training-major-select>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider" v-show="hasIndustry(IndustryIdEnum.JS)">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">建设行业</i></el-divider>
                  <el-form
                    ref="jsForm"
                    :model="basicInfoForm.JSProperty"
                    label-width="150px"
                    class="m-form is-mb f-ptb10 f-mb20"
                    :rules="rules"
                  >
                    <el-form-item label="年度：" prop="year">
                      <year-select
                        placeholder="请选择培训年度"
                        :select-type="'year'"
                        :industry-id="getIndustryId(IndustryIdEnum.JS)"
                        v-model="basicInfoForm.JSProperty.year"
                        @removeSku="removeSku"
                      ></year-select>
                    </el-form-item>
                    <el-form-item label="科目类型：" prop="subjectType">
                      <accounttype-select
                        placeholder="请选择科目类型"
                        :select-type="'subjectType'"
                        v-model="basicInfoForm.JSProperty.subjectType"
                        :industry-id="getIndustryId(IndustryIdEnum.JS)"
                        :industry-property-id="getindustryPropertyId(IndustryIdEnum.JS)"
                        @removeSku="removeSku"
                      >
                      </accounttype-select>
                    </el-form-item>
                    <el-form-item label="培训类别：" prop="trainingCategory">
                      <major-cascader
                        placeholder="请选择培训类别"
                        :select-type="'trainingCategory'"
                        v-model="basicInfoForm.JSProperty.trainingCategory"
                        :industry-id="getIndustryId(IndustryIdEnum.JS)"
                        :industry-property-id="getindustryPropertyId(IndustryIdEnum.JS)"
                        @removeSku="removeSku"
                      >
                      </major-cascader>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider" v-show="hasIndustry(IndustryIdEnum.WS)">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">职业卫生行业 </i></el-divider>
                  <el-form
                    ref="wsForm"
                    :model="basicInfoForm.WSProperty"
                    label-width="150px"
                    class="m-form is-mb f-ptb10 f-mb20"
                    :rules="rules"
                  >
                    <el-form-item label="年度：" prop="year">
                      <year-select
                        :industry-id="getIndustryId(IndustryIdEnum.WS)"
                        placeholder="请选择培训年度"
                        :select-type="'year'"
                        v-model="basicInfoForm.WSProperty.year"
                        @removeSku="removeSku"
                      ></year-select>
                    </el-form-item>
                    <el-form-item label="培训类别：" prop="trainingCategory">
                      <training-category-select
                        placeholder="请选择培训类别"
                        :select-type="'trainingCategory'"
                        v-model="basicInfoForm.WSProperty.trainingCategory"
                        :industry-id="getIndustryId(IndustryIdEnum.WS)"
                        :industry-property-id="getindustryPropertyId(IndustryIdEnum.WS)"
                        @removeSku="removeSku"
                      ></training-category-select>
                    </el-form-item>
                    <el-form-item label="培训对象：" prop="trainingObject">
                      <training-object-select
                        placeholder="请选择培训对象"
                        :select-type="'trainingObject'"
                        v-model="basicInfoForm.WSProperty.trainingObject"
                        :industry-id="getIndustryId(IndustryIdEnum.WS)"
                        :industry-property-id="getindustryPropertyId(IndustryIdEnum.WS)"
                        @getPositionCategory="getPositionCategory"
                        @getAllTrainingObjectIds="getAllTrainingObjectIds"
                        @removeSku="removeSku"
                      >
                      </training-object-select>
                    </el-form-item>
                    <el-form-item label="岗位类别：" prop="positionCategory">
                      <category-select
                        placeholder="请选择岗位类别"
                        :select-type="'positionCategory'"
                        ref="categorySelectRef"
                        v-model="basicInfoForm.WSProperty.positionCategory"
                        :industry-property-id="getindustryPropertyId(IndustryIdEnum.WS)"
                        :category-code="IndustryPropertyCodeEnum.POSITION_CATEGORY"
                        :in-type="IndustryIdEnum.WS"
                        :industry-id="getIndustryId(IndustryIdEnum.WS)"
                        @removeSku="removeSku"
                      ></category-select>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider" v-show="hasIndustry(IndustryIdEnum.GQ)">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">工勤行业</i></el-divider>
                  <el-form
                    ref="gqForm"
                    :model="basicInfoForm.GQProperty"
                    label-width="150px"
                    class="m-form is-mb f-ptb10 f-mb20"
                    :rules="rules"
                  >
                    <el-form-item label="年度：" prop="year">
                      <year-select
                        :industry-id="getIndustryId(IndustryIdEnum.GQ)"
                        placeholder="请选择培训年度"
                        :select-type="'year'"
                        v-model="basicInfoForm.GQProperty.year"
                        @removeSku="removeSku"
                      ></year-select>
                    </el-form-item>
                    <el-form-item label="技术等级：" prop="jobLevel">
                      <technical-grade-select
                        placeholder="请选择技术等级"
                        :select-type="'jobLevel'"
                        v-model="basicInfoForm.GQProperty.jobLevel"
                        :industry-id="getIndustryId(IndustryIdEnum.GQ)"
                        :industry-property-id="getindustryPropertyId(IndustryIdEnum.GQ)"
                        @removeSku="removeSku"
                      ></technical-grade-select>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider" v-show="hasIndustry(IndustryIdEnum.LS)">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">教师行业</i></el-divider>
                  <el-form
                    ref="lsForm"
                    :model="basicInfoForm.LSProperty"
                    label-width="150px"
                    class="m-form is-mb f-ptb10 f-mb20"
                    :rules="rules"
                  >
                    <el-form-item label="年度：" prop="year">
                      <year-select
                        :industry-id="getIndustryId(IndustryIdEnum.LS)"
                        placeholder="请选择培训年度"
                        :select-type="'year'"
                        v-model="basicInfoForm.LSProperty.year"
                        @removeSku="removeSku"
                      ></year-select>
                    </el-form-item>
                    <el-form-item label="学段：">
                      <study-period
                        placeholder="请选择学段"
                        :select-type="'learningPhase'"
                        v-model="basicInfoForm.LSProperty.learningPhase"
                        :industry-id="getIndustryId(IndustryIdEnum.LS)"
                        :industry-property-id="getindustryPropertyId(IndustryIdEnum.LS)"
                        @getDiscipline="getDiscipline"
                        @removeSku="removeSku"
                      ></study-period>
                    </el-form-item>
                    <el-form-item label="学科：">
                      <discipline-select
                        placeholder="请选择学科"
                        ref="disciplineSelectRef"
                        :select-type="'discipline'"
                        v-model="basicInfoForm.LSProperty.discipline"
                        :industry-id="getIndustryId(IndustryIdEnum.LS)"
                        :industry-property-id="getindustryPropertyId(IndustryIdEnum.LS)"
                        :categoryCode="IndustryPropertyCodeEnum.DISCIPLINE"
                        @removeSku="removeSku"
                      ></discipline-select>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="m-left-divider" v-show="hasIndustry(IndustryIdEnum.YS)">
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">药师行业</i></el-divider>
                  <el-form
                    ref="ysForm"
                    :model="basicInfoForm.YSProperty"
                    label-width="150px"
                    class="m-form is-mb f-ptb10 f-mb20"
                    :rules="rules"
                  >
                    <el-form-item label="年度：" prop="year">
                      <year-select
                        :industry-id="getIndustryId(IndustryIdEnum.YS)"
                        :industry-property-id="getindustryPropertyId(IndustryIdEnum.YS)"
                        placeholder="请选择培训年度"
                        :select-type="'year'"
                        v-model="basicInfoForm.YSProperty.year"
                        @removeSku="removeSku"
                      ></year-select>
                    </el-form-item>
                    <el-form-item label="科目类型：" prop="subjectType">
                      <accounttype-select
                        placeholder="请选择科目类型"
                        :select-type="'subjectType'"
                        v-model="basicInfoForm.YSProperty.subjectType"
                        :industry-id="getIndustryId(IndustryIdEnum.YS)"
                        :industry-property-id="getindustryPropertyId(IndustryIdEnum.YS)"
                        @removeSku="removeSku"
                      >
                      </accounttype-select>
                    </el-form-item>

                    <!-- <el-form-item label="执业类别：">
                      <practicing-category-cascader
                        v-model="basicInfoForm.YSProperty.practitionerCategory"
                        placeholder="请选择执业类别"
                        :select-type="'practitionerCategory'"
                        :industryId="getIndustryId(IndustryIdEnum.YS)"
                        :industry-property-id="getindustryPropertyId(IndustryIdEnum.YS)"
                        @removeSku="removeSku"
                      ></practicing-category-cascader>
                    </el-form-item> -->
                  </el-form>
                </div>
              </el-form-item>
              <el-form-item label="每天学习时长：" required>
                <el-radio v-model="basicInfoForm.timeMode" :label="TimeModeEnum.learning">按课程学习学时</el-radio>
                <el-radio v-model="basicInfoForm.timeMode" :label="TimeModeEnum.physical">按课程物理时长</el-radio>
                <div class="f-mt15" v-if="basicInfoForm.timeMode == TimeModeEnum.learning">
                  每天课程学习最多
                  <el-input-number
                    :controls="false"
                    :precision="1"
                    :min="0"
                    :max="32"
                    class="input-num f-mr10 f-ml10"
                    v-model="basicInfoForm.everyDayLearningHours"
                  />学时
                  <span class="f-c9 f-ml20">
                    <i class="el-icon-warning f-f16 f-mr5 f-vm"></i>一天学习时长不能超过32学时
                  </span>
                </div>
                <div class="f-mt15" v-else>
                  每天课程学习最多
                  <el-input-number
                    :controls="false"
                    :precision="0"
                    :min="0"
                    :max="1440"
                    class="input-num f-mr10 f-ml10"
                    v-model="basicInfoForm.everyDayLearningTime"
                  />分钟<span class="f-c9 f-ml20"
                    ><i class="el-icon-warning f-f16 f-mr5 f-vm"></i>
                    请输入正整数，一天学习时长不能超过1440分钟
                  </span>
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">特殊规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit f-p20">
          <el-col :md="20" :lg="16" :xl="13">
            <div class="m-sub-tit">
              <span class="tit-txt">设置不包含的方案</span>
            </div>
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-ml15 f-mt10 f-mb25">
              <div class="lh20">
                <p>可以设置不需要在线学习规则的培训方案，选择的培训方案不受规则学习时长影响。</p>
                <p>
                  例：设置人社行业全部年度的公需课每天只能学习60分钟。设置方案A为不需要规则，那么学员在学习方案A时，将不受60分钟学习时长影响。
                </p>
              </div>
            </el-alert>
            <el-form ref="form" label-width="auto" class="m-form f-ml15">
              <!-- 选择培训方案  -->
              <el-form-item label="选择培训方案：">
                <el-button type="primary" plain @click="openSelectTrainingDialog()">选择培训方案</el-button>
                <!--表格-->
                <el-table ref="schemeList" :data="schemeList" max-height="500px" class="m-table f-mt15" border>
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="培训方案名称" min-width="220">
                    <template v-slot="{ row }">{{ row.schemeName }}</template>
                  </el-table-column>
                  <el-table-column label="方案属性" min-width="240">
                    <template v-slot="{ row }">
                      <sku-display :sku-item="row"></sku-display>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="100" align="center" fixed="right">
                    <template v-slot="{ row }">
                      <el-button type="text" @click="deleteSchemeItem(row.schemeId)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="width-limit f-p20">
          <el-col :md="20" :lg="16" :xl="13">
            <div class="m-sub-tit">
              <span class="tit-txt">指定方案设置规则</span>
            </div>
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-ml15 f-mt10 f-mb25">
              <div class="lh20">
                <p>可以指定方案单独设置在线学习规则，指定方案将按照单独设置的规则执行学习时长限制不受行业规则影响。</p>
                <p>
                  例：设置人社行业全部年度公需课每天课程学习最多12学时，设置方案A每天课程学习最多8学时，那么学员在学习方案A时，按每天课程学习最多8学时执行。
                </p>
              </div>
            </el-alert>
            <div class="f-pl15">
              <el-button type="primary" class="f-mb10" @click="openAddSpecialRuleDialog('add')">添加特殊规则</el-button>
              <!--表格-->
              <el-table
                ref="specialRuleListRef"
                :data="rulesInfoForm.schemeSpecialRuleList"
                max-height="500px"
                class="m-table"
                border
              >
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="适用培训方案" min-width="220" align="center">
                  <template v-slot="{ row }">
                    <div v-if="row.schemeNameList.length > 3">
                      <el-tooltip class="item" effect="dark" placement="top">
                        <div slot="content">
                          <p v-for="(item, index) in row.schemeNameList" :key="index">
                            {{ item + (index < row.schemeNameList.length - 1 ? '；' : '') }}
                          </p>
                        </div>
                        <div>
                          <p v-for="(item, index) in row.schemeNameList.slice(0, 3)" :key="index">
                            {{ item + (index < row.schemeNameList.length - 1 ? '；' : '') }}
                          </p>
                          <p>…</p>
                        </div>
                      </el-tooltip>
                    </div>
                    <div v-else>
                      <div>
                        <p v-for="(item, index) in row.schemeNameList" :key="index">
                          {{ item + (index < row.schemeNameList.length - 1 ? '；' : '') }}
                        </p>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="每天学习时长" min-width="240" align="center">
                  <template v-slot="{ row }">{{
                    row.timeMode == TimeModeEnum.learning
                      ? row.everyDayLearningHours + '学时'
                      : row.everyDayLearningTime + '分钟'
                  }}</template>
                </el-table-column>
                <el-table-column label="操作" min-width="100" align="center" fixed="right">
                  <template v-slot="{ row }">
                    <el-button type="text" @click="openAddSpecialRuleDialog('add', row)">编辑</el-button>
                    <el-button type="text" @click="deleteSpecialRuleItem(row.id)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="cencel()">取消</el-button>
        <el-button type="primary" :loading="isLoading" @click="save()">保存</el-button>
      </div>

      <!-- 选择培训方案抽屉 -->
      <select-training-program
        ref="selectTrainingProgram"
        :scheme-list="schemeList"
        :basicInfo="basicInfoForm"
        @isCheckIdList="getNewSchemeList"
      ></select-training-program>

      <!-- 添加特殊规则抽屉 -->
      <add-special-rules
        ref="addSpecialRulesRef"
        @selectRuleItem="getSpecialRuleItem"
        :special-dialog-type="specialDialogType"
        :basic-info="basicInfoForm"
      ></add-special-rules>
      <!-- 重复规则列表(保存拦截) -->
      <repeat-rule-list ref="repeatRuleListRef"></repeat-rule-list>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import DisciplineSelect from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/discipline-select.vue'
  import LearningRuleItem from '@api/service/management/online-learning-rule/LearningRuleItem'
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import YearSelect from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/year-select.vue'
  import AccounttypeSelect from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/accounttype-select.vue'
  import MajorCascader from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/major-cascader.vue'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import StudyPeriod from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/study-period.vue'
  import TechnicalGradeSelect from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/technical-grade-select.vue'
  import TrainingCategorySelect from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/training-category-select.vue'
  import TrainingObjectSelect from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/training-object-select.vue'
  import ObjCategorySelect from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/obj-category-select.vue'
  import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
  import SkuDisplay from '@hbfe/jxjy-admin-platform/src/function/components/skuDisplay.vue'
  import SelectTrainingProgram from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/selectTrainingProgram.vue'
  import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'
  import { cloneDeep } from 'lodash'
  import CategorySelect from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/category-select.vue'
  import { ElForm } from 'element-ui/types/form'
  import { TimeModeEnum } from '@api/service/management/online-learning-rule/enum/TimeModeEnum'
  import { bind, debounce } from 'lodash-decorators'
  import repeatRuleList from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/repeat-rule-list.vue'
  import AddSpecialRules from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/add-special-rules.vue'
  import SchemeSpecialRuleItem from '@api/service/management/online-learning-rule/model/SchemeSpecialRuleItem'
  import CommonSchemeListModel from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSchemeListModel'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
  // import PracticingCategoryCascader from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/practicing-category-cascader.vue'
  import TrainingMajorSelect from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/training-major-select.vue'
  import QueryTrainingCategory from '@api/service/common/basic-data-dictionary/query/QueryTrainingCategory'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import SocietyTrainingMajorVo from '@api/service/common/basic-data-dictionary/query/vo/SocietyTrainingMajorVo'
  import AssembleTree from '@api/service/common/utils/AssembleTree'
  @Component({
    components: {
      CategorySelect,
      SkuDisplay,
      ObjCategorySelect,
      TrainingObjectSelect,
      TechnicalGradeSelect,
      StudyPeriod,
      MajorCascader,
      AccounttypeSelect,
      YearSelect,
      DisciplineSelect,
      TrainingCategorySelect,
      SelectTrainingProgram,
      repeatRuleList,
      AddSpecialRules,
      TrainingMajorSelect
      // PracticingCategoryCascader
    }
  })
  export default class extends Vue {
    @Ref('repeatRuleListRef') repeatRuleListRef: repeatRuleList
    @Ref('selectTrainingProgram') selectTrainingProgram: SelectTrainingProgram
    @Ref('categorySelectRef') categorySelectRef: CategorySelect
    @Ref('disciplineSelectRef') disciplineSelectRef: DisciplineSelect
    @Ref('addSpecialRulesRef') addSpecialRulesRef: AddSpecialRules
    @Ref('basicRef') basicRef: ElForm
    @Ref('rsForm') rsForm: ElForm
    @Ref('jsForm') jsForm: ElForm
    @Ref('wsForm') wsForm: ElForm
    @Ref('gqForm') gqForm: ElForm
    @Ref('lsForm') lsForm: ElForm
    @Ref('ysForm') ysForm: ElForm

    // 表单初始化
    learningRuleForm = new LearningRuleItem()
    // 基础设置表单
    basicInfoForm = this.learningRuleForm.basicInfo
    // 规则设置表单
    rulesInfoForm = this.learningRuleForm.ruleInfo
    // 接收行业
    industryList: Array<IndustryVo> = QueryIndustry.industryList

    // 类型枚举
    IndustryPropertyCodeEnum = IndustryPropertyCodeEnum
    // 行业枚举
    IndustryIdEnum = IndustryIdEnum
    // 方案列表
    schemeList = new Array<RuleSchemeItem>()
    // 存储培训对象 适配卫生
    trainingObjectIds = new Array<string>()
    // 存储学段对象 适配卫生
    disciplineIds = new Array<string>()
    // 保存加载中
    isLoading = false
    /**
     * 学习类型枚举
     */
    TimeModeEnum = TimeModeEnum

    /**
     * 存储过滤方案模型
     */
    commonSchemeListModel = CommonSchemeListModel

    /**
     * UI暂存培训专业
     */
    trainingMajor = new Array<string>()

    /**
     * 特殊规则弹窗类型：新增/修改
     */
    specialDialogType = 'add'
    /**
     * 培训专业筛选项
     */
    trainingMajorList: Array<TrainingCategoryVo> = new Array<TrainingCategoryVo>()
    societyMajorOptions = new Array<SocietyTrainingMajorVo>()

    rules = {
      // 年度
      year: [{ required: true, message: '请选择年度', trigger: ['blur', 'change'] }],
      // 地区
      region: [{ required: true, message: '请选择地区', trigger: ['blur', 'change'] }],
      // 行业
      industry: [{ required: true, message: '请选择行业', trigger: ['blur', 'change'] }],
      // 科目类型
      subjectType: [{ required: true, message: '请选择科目类型', trigger: ['blur', 'change'] }],
      // 培训类别
      trainingCategory: [{ required: true, message: '请选择培训类别', trigger: ['blur', 'change'] }],
      // 技术等级
      jobLevel: [{ required: true, message: '请选择技术等级', trigger: ['blur', 'change'] }],
      // 培训对象
      trainingObject: [{ required: true, message: '请选择培训对象', trigger: ['blur', 'change'] }],
      // 岗位类别
      positionCategory: [{ required: true, message: '请选择岗位类别', trigger: ['blur', 'change'] }],
      // 自定义校验 每天学习时长
      everyDayLearningTime: [{ required: true, validator: this.validateEveryDayMaxLearningHour, trigger: 'blur' }]
    }

    // 每天最多学习时长校验
    validateEveryDayMaxLearningHour(rule: any, value: any, callback: any) {
      if (!this.basicInfoForm.everyDayLearningTime || !this.basicInfoForm.everyDayLearningTime) {
        return callback('请输入每天最多学习时长')
      }
      if (1440 < Number(this.basicInfoForm.everyDayLearningTime)) {
        return callback('一天学习时长不能超过1440分钟')
      }
      return callback()
    }

    // 初始化
    init() {
      // 清空单例内容，防止数据混乱
      this.commonSchemeListModel.noIncludeSchemeList = []
      this.commonSchemeListModel.schemeSpecialRuleList = []
      //获取行业id
      this.industryList = QueryIndustry.industryList
    }

    // 打开选择方案弹窗
    @bind
    @debounce(200)
    async openSelectTrainingDialog() {
      // 前置校验
      const res = await this.validateIndustryValue()
      // 选择培训方案弹窗
      if (res) {
        this.selectTrainingProgram.init()
      }
    }

    // 获取选中的方案
    getNewSchemeList(newSchemeItemList: Array<RuleSchemeItem>) {
      this.schemeList = cloneDeep(newSchemeItemList)
      ;(this.$refs['schemeList'] as any)?.doLayout()
      this.commonSchemeListModel.noIncludeSchemeList = this.schemeList
    }

    // 删除方案
    deleteSchemeItem(id: string) {
      this.$confirm('确认删除对应的培训方案吗？', '提示', {
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then(() => {
          this.schemeList = this.schemeList.filter((item) => {
            return item.schemeId != id
          })
          this.commonSchemeListModel.noIncludeSchemeList = this.schemeList
        })
        .catch((e) => {
          //
        })
    }

    /**
     * 添加特殊规则弹窗
     */
    @bind
    @debounce(200)
    async openAddSpecialRuleDialog(type: string, editItem?: SchemeSpecialRuleItem) {
      // 前置校验
      const res = await this.validateIndustryValue()
      if (res) {
        // 清空添加规则抽屉数据
        this.addSpecialRulesRef.specialRuleItem = new SchemeSpecialRuleItem()
        this.addSpecialRulesRef.curSchemeList = new Array<RuleSchemeItem>()
        this.specialDialogType = type
        if (editItem) {
          // 编辑状态下将数据传入抽屉
          this.addSpecialRulesRef.specialRuleItem = editItem
          this.addSpecialRulesRef.curSchemeList = editItem.schemeList
        }
        this.addSpecialRulesRef.initData()
      }
    }

    /**
     * 获取新增特殊规则
     */
    getSpecialRuleItem(ruleItem: SchemeSpecialRuleItem) {
      // 相同id的规则覆盖，不同id的规则新增
      const index = this.rulesInfoForm.schemeSpecialRuleList.findIndex((item) => item.id == ruleItem.id)
      if (index !== -1) {
        this.$set(this.rulesInfoForm.schemeSpecialRuleList, index, ruleItem)
      } else {
        this.rulesInfoForm.schemeSpecialRuleList.push(ruleItem)
      }
      this.commonSchemeListModel.schemeSpecialRuleList = this.rulesInfoForm.schemeSpecialRuleList
    }

    /**
     * 删除指定特殊规则
     */
    deleteSpecialRuleItem(ruleId: string) {
      this.$confirm('删除后已配置信息不可恢复，是否确定继续操作？', '提示', {
        confirmButtonText: '继续操作',
        type: 'warning'
      }).then(() => {
        this.rulesInfoForm.schemeSpecialRuleList = this.rulesInfoForm.schemeSpecialRuleList.filter((item) => {
          return item.id != ruleId
        })
        this.commonSchemeListModel.schemeSpecialRuleList = this.rulesInfoForm.schemeSpecialRuleList
      })
    }

    /**
     * 清除sku拦截
     */
    removeSku(industryId: string, selectType: string, removeValue: Array<string>) {
      // 移除对应的不包含方案
      this.commonSchemeListModel.noIncludeSchemeList = this.commonSchemeListModel.noIncludeSchemeList.filter((item) => {
        if (removeValue[0] == '-1' && item.sku.industry.skuPropertyValueId == industryId) {
          item.sku[selectType] = null
        } else {
          if (selectType == 'year') {
            // 年度时存在类型string或Number，写死成number判断
            return !(
              item.sku.industry.skuPropertyValueId == industryId &&
              removeValue.map((value) => Number(value)).includes(Number(item.sku[selectType].skuPropertyValueId))
            )
          } else {
            return !(
              item.sku.industry.skuPropertyValueId == industryId &&
              removeValue.includes(item.sku[selectType].skuPropertyValueId)
            )
          }
        }
      })
      this.schemeList = this.commonSchemeListModel.noIncludeSchemeList
      // 移除对应特殊规则内的方案，且若单条规则若无方案，移除整个规则
      this.commonSchemeListModel.schemeSpecialRuleList.forEach((item) => {
        item.schemeList = item.schemeList.filter((ite) => {
          if (removeValue[0] == '-1' && ite.sku.industry.skuPropertyValueId == industryId) {
            ite.sku[selectType] = null
          } else {
            if (selectType == 'year') {
              // 年度时存在类型string或Number，写死成number判断
              return !(
                ite.sku.industry.skuPropertyValueId == industryId &&
                removeValue.map((value) => Number(value)).includes(Number(ite.sku[selectType].skuPropertyValueId))
              )
            } else {
              return !(
                ite.sku.industry.skuPropertyValueId == industryId &&
                removeValue.includes(ite.sku[selectType].skuPropertyValueId)
              )
            }
          }
        })
      })
      this.commonSchemeListModel.schemeSpecialRuleList = this.commonSchemeListModel.schemeSpecialRuleList.filter(
        (item) => {
          return item.schemeList.length > 0
        }
      )
      this.rulesInfoForm.schemeSpecialRuleList = this.commonSchemeListModel.schemeSpecialRuleList
    }

    // 修改适用行业
    changeindustryIdList(newVal: boolean, id: string) {
      if (!newVal) {
        this.$confirm(`取消行业选择会导致规则清空，需重新设置。确认取消选择吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          callback: (action) => {
            if (action == 'confirm') {
              if (!this.hasIndustry(IndustryIdEnum.RS)) {
                this.basicInfoForm.RSProperty.year = []
                this.basicInfoForm.RSProperty.subjectType = []
                // 删除对应不包含方案
                this.schemeList = this.schemeList.filter((item) => {
                  return item.sku.industry.skuPropertyValueId != IndustryIdEnum.RS
                })
                // 删除对应特殊规则下人社行业方案
                this.rulesInfoForm.schemeSpecialRuleList.forEach((item) => {
                  item.schemeList = item.schemeList.filter((ite) => {
                    return ite.sku.industry.skuPropertyValueId != IndustryIdEnum.RS
                  })
                })
              }
              if (!this.hasIndustry(IndustryIdEnum.JS)) {
                this.basicInfoForm.JSProperty.year = []
                this.basicInfoForm.JSProperty.subjectType = []
                this.basicInfoForm.JSProperty.trainingCategory = []
                this.schemeList = this.schemeList.filter((item) => {
                  return item.sku.industry.skuPropertyValueId != IndustryIdEnum.JS
                })

                // 删除对应特殊规则下建设行业方案
                this.rulesInfoForm.schemeSpecialRuleList.forEach((item) => {
                  item.schemeList = item.schemeList.filter((ite) => {
                    return ite.sku.industry.skuPropertyValueId != IndustryIdEnum.JS
                  })
                })
              }
              if (!this.hasIndustry(IndustryIdEnum.WS)) {
                this.basicInfoForm.WSProperty.year = []
                this.basicInfoForm.WSProperty.trainingCategory = []
                this.basicInfoForm.WSProperty.trainingObject = []
                this.basicInfoForm.WSProperty.positionCategory = []
                this.schemeList = this.schemeList.filter((item) => {
                  return item.sku.industry.skuPropertyValueId != IndustryIdEnum.WS
                })

                // 删除对应特殊规则下卫生行业方案
                this.rulesInfoForm.schemeSpecialRuleList.forEach((item) => {
                  item.schemeList = item.schemeList.filter((ite) => {
                    return ite.sku.industry.skuPropertyValueId != IndustryIdEnum.WS
                  })
                })
              }
              if (!this.hasIndustry(IndustryIdEnum.GQ)) {
                this.basicInfoForm.GQProperty.year = []
                this.basicInfoForm.GQProperty.jobLevel = []
                this.schemeList = this.schemeList.filter((item) => {
                  return item.sku.industry.skuPropertyValueId != IndustryIdEnum.GQ
                })

                // 删除对应特殊规则下工勤行业方案
                this.rulesInfoForm.schemeSpecialRuleList.forEach((item) => {
                  item.schemeList = item.schemeList.filter((ite) => {
                    return ite.sku.industry.skuPropertyValueId != IndustryIdEnum.GQ
                  })
                })
              }
              if (!this.hasIndustry(IndustryIdEnum.LS)) {
                this.basicInfoForm.LSProperty.year = []
                this.basicInfoForm.LSProperty.learningPhase = []
                this.basicInfoForm.LSProperty.discipline = []
                this.schemeList = this.schemeList.filter((item) => {
                  return item.sku.industry.skuPropertyValueId != IndustryIdEnum.LS
                })

                // 删除对应特殊规则下教师行业方案
                this.rulesInfoForm.schemeSpecialRuleList.forEach((item) => {
                  item.schemeList = item.schemeList.filter((ite) => {
                    return ite.sku.industry.skuPropertyValueId != IndustryIdEnum.LS
                  })
                })
              }
              if (!this.hasIndustry(IndustryIdEnum.YS)) {
                this.basicInfoForm.YSProperty.year = []
                this.basicInfoForm.YSProperty.subjectType = []
                // this.basicInfoForm.YSProperty.practitionerCategory = []
                this.schemeList = this.schemeList.filter((item) => {
                  return item.sku.industry.skuPropertyValueId != IndustryIdEnum.YS
                })

                // 删除对应特殊规则下药师行业方案
                this.rulesInfoForm.schemeSpecialRuleList.forEach((item) => {
                  item.schemeList = item.schemeList.filter((ite) => {
                    return ite.sku.industry.skuPropertyValueId != IndustryIdEnum.YS
                  })
                })
              }
              this.basicInfoForm.everyDayLearningTime = 0
            } else {
              if (!this.basicInfoForm.industryIdList.includes(id)) {
                this.basicInfoForm.industryIdList.push(id)
              }
            }
            this.commonSchemeListModel.noIncludeSchemeList = this.schemeList
            // 判断若某个特殊规则下的方案列表为空，则删除该特殊规则
            this.rulesInfoForm.schemeSpecialRuleList = this.commonSchemeListModel.schemeSpecialRuleList.filter(
              (item) => item.schemeList.length > 0
            )
            this.commonSchemeListModel.schemeSpecialRuleList = this.rulesInfoForm.schemeSpecialRuleList
          }
        })
      } else {
        ;['rsForm', 'jsForm', 'wsForm', 'gqForm', 'lsForm', 'ysForm'].forEach((item) => {
          this[item]?.clearValidate()
        })
      }
    }
    industryIdList: string[] = []
    created() {
      this.init()
      this.industryIdList = [...this.basicInfoForm.industryIdList]
    }

    //保存
    @bind
    @debounce(200)
    async save() {
      if (!this.basicInfoForm.industryIdList.length) {
        this.$message.warning('请先选择适用行业。')
        return false
      }
      if (this.basicInfoForm.LSProperty.learningPhase.length && !this.basicInfoForm.LSProperty.discipline.length) {
        this.$message.warning('已有填写学段，请继续填写学科。')
        return false
      }
      if (
        this.basicInfoForm.timeMode != TimeModeEnum.learning &&
        this.basicInfoForm.timeMode != TimeModeEnum.physical
      ) {
        this.$message.warning('请选择每天学习时长类型')
        return false
      }
      if (
        (this.basicInfoForm.timeMode === TimeModeEnum.learning && !this.basicInfoForm.everyDayLearningHours) ||
        (this.basicInfoForm.timeMode === TimeModeEnum.physical && !this.basicInfoForm.everyDayLearningTime)
      ) {
        this.$message.warning('请输入每天最多学习时长')
        return false
      }
      const arr = []
      // 校验简写
      this.hasIndustry(IndustryIdEnum.RS) && this.rsForm && arr.push(this.rsForm.validate())
      this.hasIndustry(IndustryIdEnum.JS) && this.jsForm && arr.push(this.jsForm.validate())
      this.hasIndustry(IndustryIdEnum.WS) && this.wsForm && arr.push(this.wsForm.validate())
      this.hasIndustry(IndustryIdEnum.GQ) && this.gqForm && arr.push(this.gqForm.validate())
      this.hasIndustry(IndustryIdEnum.LS) && this.lsForm && arr.push(this.lsForm.validate())
      this.hasIndustry(IndustryIdEnum.YS) && this.ysForm && arr.push(this.ysForm.validate())
      try {
        await Promise.all([this.basicRef.validate(), ...arr])
        this.isLoading = true

        if (this.schemeList.length) {
          this.rulesInfoForm.schemeIds = this.schemeList.map((item) => item.schemeId)
        } else {
          this.rulesInfoForm.schemeIds = []
        }
        // 若培训专业选项为空，则将所有末级节点id传入
        if (!this.basicInfoForm.RSProperty.trainingProfessional.length && this.hasIndustry(IndustryIdEnum.RS)) {
          this.basicInfoForm.RSProperty.trainingProfessional = ['-1']
        }
        const res = await this.learningRuleForm.save()
        if (res.isSuccess()) {
          this.isLoading = false
          this.$message.success('保存成功')
          this.$router.push('/basic-data/platform/function')
        } else {
          this.isLoading = false
          if (this.learningRuleForm?.existingPropertyList.length) {
            this.repeatRuleListRef.open = true
            this.repeatRuleListRef.repeatRuleList = this.learningRuleForm.existingPropertyList
          } else {
            this.$message.error((res?.message as string) || '系统异常')
          }
        }
        this.isLoading = false
      } catch (error) {
        this.isLoading = false
        console.error('保存失败:', error)
      }
    }

    // changeEveryDayLearningTime() {
    //   this.basicRef.validateField('everyDayMaxLearningHour')
    // }

    /**
     * 卫生-初始化传递所有父级id(一次性查询)
     */
    getAllTrainingObjectIds(ids: TrainingPropertyResponse[]) {
      this.trainingObjectIds = ids.map((item: any) => item.propertyId)
      this.categorySelectRef.propertyIdList = this.trainingObjectIds
      this.categorySelectRef.initPositionCategoryData()
    }

    /**
     * 卫生-根据培训对象id筛选岗位类别
     */
    getPositionCategory() {
      this.categorySelectRef.gradePropertyId = this.basicInfoForm.WSProperty.trainingObject
      this.categorySelectRef.initPositionCategoryData()
    }

    /**
     * 根据学段id查询对应学科数据
     */
    getDiscipline() {
      this.disciplineSelectRef.gradePropertyId = this.basicInfoForm.LSProperty.learningPhase
      this.disciplineSelectRef.initDisciplineData()
    }

    // 返回上一级
    cencel() {
      this.$router.push('/basic-data/platform/function')
    }

    // 获取行业id
    get getIndustryId() {
      return (id: IndustryIdEnum) => {
        return this.industryList.find((industry) => industry.id.indexOf(id) != -1)?.id
      }
    }

    // 获取行业属性id
    get getindustryPropertyId() {
      return (id: IndustryIdEnum) => {
        return this.industryList.find((industry) => industry.id.indexOf(id) != -1)?.propertyId || ''
      }
    }

    // 查找行业数组里是否包含对应行业
    get hasIndustry() {
      return (id: IndustryIdEnum) => {
        return this.basicInfoForm.industryIdList.find((industry) => industry.indexOf(id) != -1)
      }
    }

    /**
     * 添加方案、规则前置校验
     */
    async validateIndustryValue() {
      if (!this.basicInfoForm.industryIdList.length) {
        this.$message.warning('请先配置适用行业。')
        return false
      }
      if (
        !this.warnDialog(IndustryIdEnum.RS, 'RSProperty', 'year', '请先配置人社行业年度') ||
        !this.warnDialog(IndustryIdEnum.RS, 'RSProperty', 'subjectType', '请先配置人社行业科目类型') ||
        !this.warnDialog(IndustryIdEnum.JS, 'JSProperty', 'year', '请先配置建设行业年度') ||
        !this.warnDialog(IndustryIdEnum.JS, 'JSProperty', 'subjectType', '请先配置建设行业科目类型') ||
        !this.warnDialog(IndustryIdEnum.JS, 'JSProperty', 'trainingCategory', '请先配置建设行业培训类别') ||
        !this.warnDialog(IndustryIdEnum.WS, 'WSProperty', 'year', '请先配置职业卫生行业年度') ||
        !this.warnDialog(IndustryIdEnum.WS, 'WSProperty', 'trainingCategory', '请先配置职业卫生行业培训类别') ||
        !this.warnDialog(IndustryIdEnum.WS, 'WSProperty', 'trainingObject', '请先配置职业卫生行业培训对象') ||
        !this.warnDialog(IndustryIdEnum.WS, 'WSProperty', 'positionCategory', '请先配置职业卫生行业岗位类别') ||
        !this.warnDialog(IndustryIdEnum.GQ, 'GQProperty', 'year', '请先配置工勤行业年度') ||
        !this.warnDialog(IndustryIdEnum.GQ, 'GQProperty', 'jobLevel', '请先配置工勤行业技术等级') ||
        !this.warnDialog(IndustryIdEnum.LS, 'LSProperty', 'year', '请先配置教师行业年度') ||
        !this.warnDialog(IndustryIdEnum.YS, 'YSProperty', 'year', '请先配置药师行业年度') ||
        !this.warnDialog(IndustryIdEnum.YS, 'YSProperty', 'subjectType', '请先配置药师行业科目类型')
      ) {
        return false
      }
      if (
        this.hasIndustry(IndustryIdEnum.LS) &&
        this.basicInfoForm.LSProperty.learningPhase.length &&
        !this.basicInfoForm.LSProperty.discipline.length
      ) {
        this.$message.warning('已有填写学段，请继续填写学科。')
        return false
      }
      return true
    }

    /**
     * 提示
     */
    warnDialog(industryId: IndustryIdEnum, industryProperty: string, property: string, message: string) {
      if (this.hasIndustry(industryId) && !this.basicInfoForm[industryProperty][property].length) {
        this.$message.warning(message)
        return false
      }
      return true
    }

    /**
     * 改变培训专业值
     */
    changeValue(val: string[]) {
      this.basicInfoForm.RSProperty.trainingProfessional = val
    }
  }
</script>
