{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "strictPropertyInitialization": false, "importHelpers": true, "suppressImplicitAnyIndexErrors": true, "moduleResolution": "node", "experimentalDecorators": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "strictNullChecks": false, "baseUrl": ".", "types": ["webpack-env", "mocha", "chai"], "paths": {"@api/*": ["src/*"], "@/store": ["src/store"], "@packages/*": ["packages/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "exclude": ["node_modules"]}