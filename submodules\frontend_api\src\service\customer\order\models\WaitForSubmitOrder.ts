import WaitForSubmitInvoiceInfo from './WaitForSubmitInvoiceInfo'
import { OrderCreateRequest, SubOrderItemCreateRequest } from '@api/gateway/Trade-default'

/**
 * 待提交的订单
 */
class WaitForSubmitOrder {
  /**
   * 培训班对应的商品SKUid
   */
  commoditySkuId: string
  /**
   * 发票信息
   */
  invoiceInfo: WaitForSubmitInvoiceInfo

  toOrderCreateInfo(): OrderCreateRequest {
    const dto = new OrderCreateRequest()
    dto.marketingChannelId = 'WEB'
    const subOrder = new SubOrderItemCreateRequest()
    if (this.invoiceInfo) {
      dto.invoiceInfo = this.invoiceInfo.toInvoiceRequestDTO()
      subOrder.needBill = true
    } else {
      subOrder.needBill = false
    }
    dto.subOrderList = new Array<SubOrderItemCreateRequest>()
    subOrder.purchaseQuantity = 1
    subOrder.commodityId = this.commoditySkuId
    dto.subOrderList.push(subOrder)
    return dto
  }
}

export default WaitForSubmitOrder
