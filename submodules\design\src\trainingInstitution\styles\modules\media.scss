//媒体样式
@import "../../../common/variables";

@media screen and (max-height: 800px) {
  .m-login-wrap {
    background-position: center -130px;

    .wrap-bd {
      min-height: 620px;

      .m-logo {
        padding: 30px 0 50px 37px;
      }
    }

    .m-login {
      top: 130px;
    }

    .login-footer {
      max-height: 60px;
      padding: 10px 0;
    }
  }
}

@media only screen and (max-width: 1800px) {
  .m-school-set {
    .el-col-sm-12 {
      &:nth-child(2n) {
        border-right: 0;

        .arrow {
          display: none;
        }
      }

      &:nth-child(-n + 2) {
        .item {
          border-bottom: 1px solid #ebeef5;
        }
      }
    }
  }
}
