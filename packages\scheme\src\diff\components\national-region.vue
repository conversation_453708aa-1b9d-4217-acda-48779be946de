<route-meta>
{
"title": "业务地区级联选择器,只能两级筛选(只能选到市)"
}
</route-meta>
<template>
  <el-cascader
    ref="elCascaderRef"
    v-if="show"
    :props="props"
    v-model="selectedValue"
    :options="options"
    :placeholder="placeholder"
    :clearable="clearable"
    collapse-tags
    @change="onInput"
    v-bind="$attrs"
    :disabled="disabled"
  ></el-cascader>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Emit, Watch, Ref } from 'vue-property-decorator'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
  import QueryUserFactory from '@api/service/management/user/QueryUserFactory'
  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    disabled: boolean

    @Prop({
      default: true
    })
    clearable: boolean

    @Prop({
      default: false
    })
    multiple: boolean

    // 传入的必须是数组
    @Prop({
      type: [Array, String]
    })
    value: string[]

    @Prop({
      default: '请选择地区',
      type: String
    })
    placeholder: string

    /**
     * 省份id、用于过滤省份
     */
    @Prop({
      default: '0',
      type: String
    })
    provinceId: string

    // 分销组件需要对应参数
    lowerArray: string[][] = []

    /**
     * 是否需要点击到最后一级才能选中，默认：是
     */
    @Prop({
      default: false
    })
    checkStrictly: boolean
    /**
     * 是否是分销组件
     */
    @Prop({
      default: false
    })
    isFx: boolean

    @Ref('elCascaderRef') elCascaderRef: any
    /**
     * 地区选择器配置
     */
    options: Array<RegionTreeVo> = new Array<RegionTreeVo>()
    /**
     * 全国树
     */
    nationWideTree: Array<RegionTreeVo> = new Array<RegionTreeVo>()
    /**
     * 获取服务地区id
     */
    serviceId: string[]
    // 当前选中的值
    selectedValue: string[] = []
    show = true
    props = {}

    @Watch('options', {
      immediate: true,
      deep: true
    })
    optionsChange(val: any) {
      console.log('options', val)
    }

    @Watch('value', { immediate: true, deep: true })
    setValue() {
      this.selectedValue = this.value
    }
    @Emit('input')
    onInput(values: any) {
      this.lowerArray = this.getTreeAllLeaves(this.elCascaderRef.getCheckedNodes()).map((item: any) => item.value)
      this.$emit('regionCall', this.lowerArray)
      return values
    }

    getTreeAllLeaves(tree: Node[], childKey = 'children'): Node[] {
      const result = [] as Node[]
      const getLeaves = (subTree: Node[]) => {
        subTree.forEach((item: Node) => {
          if (!item[childKey] || !item[childKey].length) {
            result.push(item)
          } else {
            getLeaves(item[childKey])
          }
        })
      }
      getLeaves(tree)
      return result
    }

    async created() {
      this.setProps()
      //   this.options = (await QueryBusinessRegion.getServiceOrIndustry(1)) || ([] as RegionTreeVo[])
      this.nationWideTree = await QueryBusinessRegion.getCountrywideRegion()
      if (QueryUserFactory.queryManagerDetail.isRegionAdmin) {
        this.serviceId = QueryBusinessRegion.getRegionAdminArea()
      } else {
        this.serviceId = await QueryBusinessRegion.getServiceRegionIds()
      }
      this.options = QueryBusinessRegion.filterRegionTree(this.nationWideTree, this.serviceId, this.isFx, this.multiple)
      // 去除区级筛选
      this.options = this.clearChildrenNodes(this.options)
      this.show = true
    }

    clearChildrenNodes(items: RegionTreeVo[]): RegionTreeVo[] {
      items.forEach((item) => item.children?.forEach((child) => (child.children = undefined)))
      return items
    }

    /**
     * 设置配置项
     */
    setProps() {
      this.props = {
        lazy: false,
        value: 'id',
        label: 'name',
        multiple: this.multiple,
        checkStrictly: this.checkStrictly
      }
    }
  }
</script>
