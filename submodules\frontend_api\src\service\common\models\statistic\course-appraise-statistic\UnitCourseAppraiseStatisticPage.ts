import { Appraise, EveryStartHit } from '@api/service/common/models/statistic/course-appraise-statistic/Appraise'
//  UnitCourseAppraiseStatisticDTO,
//   UnitLearningStatisticResponse,
import {
  TrainingInstitutionCourseAppraiseStatisticDTO,
  TrainingInstitutionLearningStatisticResponse
} from '@api/gateway/PlatformStatisticReportQuery'

/**
 * 分页数据
 * <AUTHOR>
 * @Date 2021/5/7/0007 9:32
 */
export class UnitCourseAppraiseStatistic {
  /**
   * 单位id
   */
  unitId: string
  /**
   * 单位名
   */
  unitName: string
  /**
   * 综合得分
   */
  averageComprehensive: Appraise
  /**
   * 累计培训人次
   */
  trainingTimes: number
  /**
   * 累计获得学时
   */
  totalPeriod: number
}

/**
 * 分页对象
 * <AUTHOR>
 * @Date 2021/5/7/0007 9:30
 */
export class UnitCourseAppraiseStatisticPage {
  totalSize: number
  list: Array<UnitCourseAppraiseStatistic>

  /**
   * 整合远端模型
   * @param remotes
   * @param counts
   */
  static fromRemote(
    remotes: Array<TrainingInstitutionCourseAppraiseStatisticDTO>,
    counts: Array<TrainingInstitutionLearningStatisticResponse>
  ) {
    const locals = new Array<UnitCourseAppraiseStatistic>()
    remotes.forEach(remote => {
      const local = new UnitCourseAppraiseStatistic()
      local.unitId = remote.trainingInstitutionId
      local.unitName = remote.trainingInstitutionName
      local.averageComprehensive = new Appraise()
      local.averageComprehensive.average = remote.average
      local.averageComprehensive.everyStartHit = new EveryStartHit()
      local.averageComprehensive.everyStartHit.one = remote.one
      local.averageComprehensive.everyStartHit.two = remote.two
      local.averageComprehensive.everyStartHit.three = remote.three
      local.averageComprehensive.everyStartHit.four = remote.four
      local.averageComprehensive.everyStartHit.five = remote.five
      const count = counts.find(unit => unit.trainingInstitutionId === remote.trainingInstitutionId)
      local.trainingTimes = count?.openUserCount || 0
      local.totalPeriod = count?.finishAssessGrade || 0
      locals.push(local)
    })
    return locals
  }
}

/**
 * 查询条件
 * <AUTHOR>
 * @Date 2021/5/7/0007 9:32
 */
export class UnitCourseAppraiseStatisticQueryParam {
  /**
   * 单位id集合
   */
  unitIds: Array<string>
}
