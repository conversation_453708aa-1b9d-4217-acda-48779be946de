import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 自取配送状态枚举
 */
export enum SelfFetchedDeliveryStatusEnum {
  // 1：未自取
  Wait_For_Self_Fetched = 1,
  // 2：已自取
  Complete_Self_Fetched
}

/**
 * @description
 */
class SelfFetchedDeliveryStatusList extends AbstractEnum<SelfFetchedDeliveryStatusEnum> {
  static enum = SelfFetchedDeliveryStatusEnum
  constructor(status?: SelfFetchedDeliveryStatusEnum) {
    super()
    this.current = status
    this.map.set(SelfFetchedDeliveryStatusEnum.Wait_For_Self_Fetched, '未自取')
    this.map.set(SelfFetchedDeliveryStatusEnum.Complete_Self_Fetched, '已自取')
  }
}

export default new SelfFetchedDeliveryStatusList()
