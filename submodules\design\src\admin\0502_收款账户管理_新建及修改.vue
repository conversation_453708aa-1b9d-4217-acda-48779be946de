<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">收款账户管理</el-breadcrumb-item>
      <el-breadcrumb-item>新建收款账户</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="form" label-width="140px" class="m-form">
                <el-form-item label="支付方式：" required>
                  <el-radio-group v-model="form.resource">
                    <el-radio label="线上"></el-radio>
                    <el-radio label="线下"></el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="支付账号类型：" required>
                  <el-radio-group v-model="form.resource">
                    <el-radio label="微信"></el-radio>
                    <el-radio label="支付宝"></el-radio>
                    <el-radio label="兴业银行聚合支付"></el-radio>
                    <el-radio label="建设银行聚合支付"></el-radio>
                    <el-radio label="兴业银行聚合支付(威富通）"></el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="账户别名：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入账户别名" class="form-l" />
                </el-form-item>
                <!--微信-->
                <el-form-item label="商户号：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入商户号" class="form-l" />
                </el-form-item>
                <el-form-item label="API密钥：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入API密钥" class="form-l" />
                </el-form-item>
                <el-form-item label="公众帐号ID：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入公众帐号ID" class="form-l" />
                </el-form-item>
                <el-form-item label="微信证书密钥：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入微信证书密钥" class="form-l" />
                  <span class="f-ml10 f-co">注：默认使用商户号</span>
                </el-form-item>
                <el-form-item label="微信证书：" required>
                  <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                    <el-button type="primary" plain>点击上传</el-button>
                  </el-upload>
                </el-form-item>
                <!--!!!!!!注意：该div为了效果预览，须删掉-->
                <div class="f-mt50"></div>
                <!--支付宝-->
                <el-form-item label="支付宝帐号：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入支付宝帐号" class="form-l" />
                </el-form-item>
                <el-form-item label="支付宝密钥：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入支付宝密钥" class="form-l" />
                </el-form-item>
                <el-form-item label="合作者身份ID：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入合作者身份ID" class="form-l" />
                </el-form-item>
                <el-form-item label="支付宝应用私钥：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入支付宝应用私钥" class="form-l" />
                </el-form-item>
                <el-form-item label="支付宝公钥：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入支付宝公钥" class="form-l" />
                </el-form-item>
                <el-form-item label="支付宝应用ID：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入支付宝应用ID" class="form-l" />
                </el-form-item>
                <!--!!!!!!注意：该div为了效果预览，须删掉-->
                <div class="f-mt50"></div>
                <!--线下-->
                <el-form-item label="开户号：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入开户号" class="form-l" />
                </el-form-item>
                <el-form-item label="开户银行：">
                  <el-input v-model="form.name" clearable placeholder="请输入开户银行" class="form-l" />
                </el-form-item>
                <el-form-item label="开户户名：">
                  <el-input v-model="form.name" clearable placeholder="请输入开户户名" class="form-l" />
                </el-form-item>
                <el-form-item label="柜台号：">
                  <el-input v-model="form.name" clearable placeholder="请输入柜台号" class="form-l" />
                </el-form-item>
                <!--!!!!!!注意：该div为了效果预览，须删掉-->
                <div class="f-mt50"></div>
                <el-form-item label="纳税人识别号：" required>
                  <el-select v-model="form.region" clearable placeholder="请选择纳税人识别号" class="form-m">
                    <el-option value="选项1"></el-option>
                    <el-option value="选项2"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <div slot="label">
                    <span class="f-vm">退款方式</span>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                      <div slot="content">
                        <p>退款方式说明：</p>
                        <p>线下退款，方式需要登录微信/支付宝商户后台退款，系统只记录退款状态；</p>
                        <p>线上退款，确认退款后系统会将款项返回原账户。</p>
                      </div>
                    </el-tooltip>
                    <span>：</span>
                  </div>
                  <el-radio-group v-model="form.resource">
                    <el-radio label="线下退款"></el-radio>
                    <el-radio label="线上退款"></el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-button>取消</el-button>
                  <el-button type="primary">保存</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
