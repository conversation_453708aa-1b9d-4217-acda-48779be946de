schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""验证当前登录的token对应账号是否已注册参训单位服务商
		@param mztToken
		@return
	"""
	hasParticipatingUnit(mztToken:String):Boolean! @NotAuthenticationRequired
	"""子项目管理员查询参数单位分页接口"""
	page(page:Page,params:ParticipatingUnitParams):ParticipatingUnitDtoPage @page(for:"ParticipatingUnitDto")
}
type Mutation {
	"""功能描述 : 参训单位单点登录需要同步信息接口
		@date : 2021/10/12 16:37
		@param mztToken : 闽政通token
		@return : void
	"""
	syncTrainingInstitution(mztToken:String):Void @NotAuthenticationRequired
}
"""参训单位名称查询信息"""
input ParticipatingUnitParams @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.servicer.participatingUnit.ParticipatingUnitParams") {
	"""参训单位名称"""
	name:String
	"""统一社会信用代码"""
	code:String
	"""所在地区路径"""
	regionPath:String
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
enum ServicerStatusEnums @type(value:"com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerStatusEnums") {
	"""全部"""
	ALL
	"""正常"""
	NORMAL
	"""失效"""
	SUSPEND
}
"""参训单位列表信息"""
type ParticipatingUnitDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.servicer.participatingUnit.ParticipatingUnitDto") {
	"""Id"""
	id:String
	"""参训单位"""
	name:String
	"""地区名称列表，省市区县..."""
	regionNames:[String]
	"""所在地区路径"""
	regionPath:String
	"""统一社会信用代码"""
	code:String
	"""联系人"""
	contactPerson:String
	"""联系电话"""
	phone:String
	"""创建时间"""
	createdTime:DateTime
	"""服务商状态
		@see ServicerStatusEnums
	"""
	servicerStatus:ServicerStatusEnums
}

scalar List
type ParticipatingUnitDtoPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ParticipatingUnitDto]}
