import MsSchemeQueryFrontGatewayCourseLearningBackstage, {
  SchemeConfigResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import MsTradeQueryFrontGatewayCourseLearningBackstage, {
  CommoditySkuBackstageResponse,
  SchemeResourceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import examQueryFactory from '@api/service/management/resource/exam-paper/QueryExamPaperFactory'
import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
import {
  ComplexSkuPropertyResponse,
  SkuPropertyConvertUtils
} from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import TrainClassConfigJsonManager from '@api/service/management/train-class/Utils/TrainClassConfigJsonManager'
import Classification from '@api/service/management/train-class/mutation/vo/Classification'
import { ResponseStatus } from '@hbfe/common'
import { cloneDeep } from 'lodash'

import Mockjs from 'mockjs'
import ExperienceItem from '@api/service/management/train-class/mutation/vo/ExperienceItem'
import { LearningExperienceEnum } from '@api/service/management/train-class/mutation/Enum/LearningExperienceEnum'
import { OperationEnum } from '@api/service/management/train-class/mutation/Enum/OperationEnum'
import MutationCreateTrainClassCommodity from '@api/service/diff/management/zztt/train-class/MutationCreateTrainClassCommodity'
import MutationTrainClassFactory from '@api/service/diff/management/zztt/train-class/MutationTrainClassFactory'
import TrainClassDetailClassVo from '@api/service/diff/management/zztt/train-class/model/TrainClassDetailClassVo'
import SchemeType, { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import IntelligenceLearningModule from '@api/service/management/intelligence-learning/IntelligenceLearningModule'
import { AchievementExhibitionEnum } from '@api/service/common/enums/train-class/AchievementExhibitionEnum'
import QueryTrainClassDetailClass from '@api/service/management/train-class/query/QueryTrainClassDetailClass'
import MutationTrainClass from '@api/service/management/train-class/offlinePart/MutationTrainClass'

/**
 * 运营域获取培训班商品详情
 */
class QueryTrainClassDetailClassDiff extends QueryTrainClassDetailClass {
  // region properties
  /**
   *配置信息jsonString，类型为string
   */
  jsonString = ''
  /**
   *培训班详情对象
   */
  trainClassDetail = new TrainClassDetailClassVo()
  /**
   *培训班商品id，类型为string
   */
  commodityId = ''
  // endregion
  // region methods

  protected classificationMap = new Map<string, string>()

  /**
   * 获取培训班详情 由详情和配置信息组合而成
   */
  async queryTrainClassDetail(): Promise<ResponseStatus> {
    //获取班级详情
    let status = await this.requestClassDetail()
    if (status.isSuccess()) {
      //获取班级模板配置信息
      status = await this.requestClassConfigDiff()
    }
    return status
  }

  //获取班级模板配置信息
  protected async requestClassConfigDiff() {
    const res = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInServicer({
      schemeId: this.trainClassDetail.trainClassBaseInfo.id
    })
    const data = await this.requestClassConfig()
    const classConfigJson: any = TrainClassConfigJsonManager.calculatorSchemeConfigJson(res.data.schemeConfig)
    this.trainClassDetail.trainClassBaseInfo.thirdPartyId = classConfigJson.extendProperties.find((item: any) => {
      return item.name == 'externalTrainingPlatform'
    })?.value
    return data
  }
}
export default QueryTrainClassDetailClassDiff
