import SignDateScope from '@api/service/customer/sign-center/models/SignDateScope'
import { SignStatusEnum } from '@api/service/customer/sign-center/enums/SignStatusEnum'
import { SignTypeEnum } from '@api/service/customer/sign-center/enums/SignTypeEnum'

export default class SignTime {
  /**
   * 签到点id
   */
  id: string = undefined

  /**
   * 签到类型
   */
  type: SignTypeEnum = undefined

  /**
   * 签到时段
   */
  signScope: SignDateScope = new SignDateScope()

  /**
   * 签到状态
   */
  timeSignStatus: SignStatusEnum = undefined

  /**
   * 倒计时时间
   */
  countDownTime: string = undefined

  /**
   * 已签到时间
   */
  signedTime: string = undefined

  constructor(type?: SignTypeEnum) {
    this.type = type
  }

  /**
   * 是否需要倒计时
   */
  get needCountDown() {
    return (
      this.id &&
      this.timeSignStatus != SignStatusEnum.signed &&
      this.timeSignStatus != SignStatusEnum.notSign &&
      this.timeSignStatus != SignStatusEnum.unSign
    )
  }

  /**
   * 状态文本（计时 + 状态）
   */
  get statusText() {
    let signedTime = (this.signedTime && this.signedTime.split(' ')[1]) || ''
    if (this.signedTime) {
      const time = this.signedTime.split(' ')[1]
      signedTime = time || ''
    }
    switch (this.timeSignStatus) {
      case SignStatusEnum.notSign:
        return this.type == SignTypeEnum.signOut ? '未签退' : '未签到'
      case SignStatusEnum.waitSign:
        if (this.countDownTime) {
          return `${this.countDownTime}${this.type == SignTypeEnum.signOut ? '后开始签退' : '后开始签到'}`
        } else {
          return ''
        }
      case SignStatusEnum.signed:
        return `${signedTime}${this.type == SignTypeEnum.signOut ? '已签退' : '已签到'}`
      case SignStatusEnum.signing:
        return `${this.countDownTime}${this.type == SignTypeEnum.signOut ? '后停止签退' : '后停止签到'}`
      default:
        return ''
    }
  }

  /**
   * 倒计时
   * @param timestamp 当前时间戳
   * @param fixTipTimestamp 开始前几分钟开始倒计时
   */
  countDown(timestamp: number, fixTipTimestamp: number) {
    const signStartTime = this.signScope.beginTimestamp
    const signEndTime = this.signScope.endTimestamp
    if (timestamp < signStartTime) {
      // 未开始签到（倒计时为签到开始时间减去签到时间）
      const countTimeStamp = signStartTime - timestamp
      // 小于30分钟开始倒计时
      this.timeSignStatus = SignStatusEnum.waitSign
      if (countTimeStamp < fixTipTimestamp) {
        this.countDownTime = this.secondsToHHMMSS(countTimeStamp / 1000)
      } else {
        this.countDownTime = ''
      }
    } else if (timestamp >= signStartTime && timestamp <= signEndTime) {
      // 处在签到时间（倒计时时间为签到结束时间减去当前时间）
      const countTimeStamp = signEndTime - timestamp
      this.timeSignStatus = SignStatusEnum.signing
      this.countDownTime = this.secondsToHHMMSS(countTimeStamp / 1000)
    } else {
      // 超过签到时间
      this.timeSignStatus = SignStatusEnum.notSign
      this.countDownTime = ''
    }
  }

  /**
   * 将秒数转换为 HHMMSS
   * @param seconds 秒
   */
  private secondsToHHMMSS(seconds: number) {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = Math.floor(seconds % 60)

    // 使用padStart方法来确保每个部分都是两位数
    const formattedHours = String(hours).padStart(2, '0')
    const formattedMinutes = String(minutes).padStart(2, '0')
    const formattedSeconds = String(remainingSeconds).padStart(2, '0')

    return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
  }
}
