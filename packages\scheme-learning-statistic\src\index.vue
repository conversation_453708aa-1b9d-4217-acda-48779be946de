<route-meta>
  {
  "isMenu": true,
  "title": "方案学习统计",
  "sort": 3,
  "icon": "icon-tongjiyuce"
  }
  </route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="pageScheme,@BizPortalSelect,@BizDistributorSelect">
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
        <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
        <hb-search-wrapper @reset="resetQueryParam" class="m-query is-border-bottom">
          <el-form-item label="年度">
            <biz-year-select
              placeholder="请选择培训年度"
              v-model="localSkuProperty.year"
              :multiple="true"
            ></biz-year-select>
          </el-form-item>
          <el-form-item label="地区">
            <!-- <biz-region-cascader
                v-model="localSkuProperty.region"
                :check-strictly="true"
                placeholder="请选择地区"
              ></biz-region-cascader> -->
            <biz-national-region
              ref="regionValueRef"
              v-model="localSkuProperty.region"
              :check-strictly="true"
              placeholder="请选择地区"
            ></biz-national-region>
          </el-form-item>

          <el-form-item label="培训方案类型">
            <biz-scheme-type v-model="schemeTypeInfo"></biz-scheme-type>
          </el-form-item>

          <el-form-item label="培训方案名称">
            <!-- <biz-learning-scheme-select v-model="commoditySkuIdList"></biz-learning-scheme-select> -->
            <el-input clearable placeholder="请输入培训方案名称" v-model="schemeName" />
          </el-form-item>
          <el-form-item label="行业">
            <biz-industry-select
              v-model="localSkuProperty.industry"
              @clearIndustrySelect="handleClearIndustrySelect"
              @industryPropertyId="handleIndustryPropertyId"
              @industryInfos="handleIndustryInfos"
            ></biz-industry-select>
          </el-form-item>

          <el-form-item
            label="科目类型"
            v-if="
              skuVisible.subjectType &&
              localSkuProperty.industry &&
              envConfig.societyIndustryId &&
              localSkuProperty.industry === envConfig.societyIndustryId
            "
          >
            <biz-accounttype-select
              v-model="localSkuProperty.subjectType"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            >
            </biz-accounttype-select>
          </el-form-item>

          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.societyIndustryId &&
              localSkuProperty.industry === envConfig.societyIndustryId
            "
          >
            <biz-major-cascader
              v-model="localSkuProperty.societyTrainingMajor"
              placeholder="请选择培训专业"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.constructionIndustryId &&
              localSkuProperty.industry === envConfig.constructionIndustryId
            "
          >
            <biz-training-category-select
              placeholder="请选择培训类别"
              v-model="localSkuProperty.trainingCategory"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              @updateTrainingCategory="handleUpdateTrainingCategory"
            />
          </el-form-item>

          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.constructionIndustryId &&
              localSkuProperty.industry === envConfig.constructionIndustryId
            "
          >
            <biz-major-select
              v-model="localSkuProperty.constructionTrainingMajor"
              placeholder="请选择培训专业"
              :industry-property-id="industryPropertyId"
              :training-category-id="trainingCategoryId"
            />
          </el-form-item>
          <!-- 工勤行业 -->
          <el-form-item
            label="技术等级"
            v-if="
              skuVisible.technicalGrade &&
              localSkuProperty.industry &&
              envConfig.workServiceId &&
              localSkuProperty.industry === envConfig.workServiceId
            "
          >
            <biz-technical-grade-select
              v-model="localSkuProperty.technicalGrade"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            ></biz-technical-grade-select>
          </el-form-item>
          <!-- 卫生行业 -->
          <el-form-item
            label="培训类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.professionHealthIndustryId &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-training-category-select
              placeholder="请选择培训类别"
              v-model="localSkuProperty.trainingCategory"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训对象"
            v-if="
              skuVisible.trainingObject &&
              localSkuProperty.industry &&
              envConfig.professionHealthIndustryId &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-training-object-select
              placeholder="请选择培训对象"
              v-model="localSkuProperty.trainingObject"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              @updateTrainingCategory="handleWSUpdateTrainingObject"
            />
          </el-form-item>
          <el-form-item
            label="岗位类别"
            v-if="
              skuVisible.positionCategory &&
              localSkuProperty.industry &&
              envConfig.professionHealthIndustryId &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-obj-category-select
              placeholder="请选择岗位类别"
              v-model="localSkuProperty.positionCategory"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              :training-object-id="trainingObjectId"
            />
          </el-form-item>
          <el-form-item
            label="学段"
            v-if="
              skuVisible.learningPhase &&
              localSkuProperty.industry &&
              envConfig.teacherIndustryId &&
              localSkuProperty.industry === envConfig.teacherIndustryId
            "
          >
            <!-- {{ localSkuProperty.learningPhase }} -->
            <biz-study-period
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              placeholder="请选择学段"
              v-model="localSkuProperty.learningPhase"
              @updateStudyPeriod="updateStudyPeriod"
            />
          </el-form-item>
          <el-form-item
            label="学科"
            v-if="
              skuVisible.discipline &&
              localSkuProperty.industry &&
              envConfig.teacherIndustryId &&
              localSkuProperty.industry === envConfig.teacherIndustryId
            "
          >
            <biz-subject
              :studyPeriodId="localSkuProperty.learningPhase"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              placeholder="请选择学科"
              v-model="localSkuProperty.discipline"
            />
          </el-form-item>
          <!-- <el-form-item
            label="执业类别"
            v-if="
              skuVisible.practitionerCategory &&
                localSkuProperty.industry &&
                envConfig.medicineIndustryId &&
                localSkuProperty.industry === envConfig.medicineIndustryId
            "
          >
            <biz-practicing-category-cascader
              v-model="localSkuProperty.pharmacistIndustry"
              :industryId="localSkuProperty.industry"
            ></biz-practicing-category-cascader>
          </el-form-item> -->
          <el-form-item label="报名时间">
            <double-date-picker
              :begin-create-time.sync="CommoditySkuRequestVo.registerTime.begin"
              :end-create-time.sync="CommoditySkuRequestVo.registerTime.end"
              beginTimePlaceholder="请选择报名成功时间"
              endTimePlaceholder="请选择报名成功时间"
              endDefaultTime="23:59:59"
            ></double-date-picker>
          </el-form-item>
          <!-- <el-form-item label="分销推广">
            <el-select v-model="localSkuProperty.learningPhase" placeholder="请选择是否为分销推广订单">
              <el-option value="" label="全部"></el-option>
              <el-option value="1" label="是"></el-option>
              <el-option value="2" label="否"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="专题">
            <biz-special-select v-model="saleChannels" @input="changeSaleChannels"></biz-special-select>
          </el-form-item>
          <el-form-item label="专题名称" v-if="saleChannels || saleChannels == null">
            <biz-special-name v-model="CommoditySkuRequestVo.trainingChannelName"></biz-special-name>
          </el-form-item>
          <slot name="sale-channel" :localSkuProperty="localSkuProperty"></slot>
          <el-form-item label="分销商" v-if="isOpenFxServer">
            <biz-distributor-select
              v-model="CommoditySkuRequestVo.distributorId"
              :name="CommoditySkuRequestVo.distributorName"
            ></biz-distributor-select>
          </el-form-item>
          <el-form-item label="推广门户简称" v-if="isOpenFxServer">
            <biz-portal-select
              :disabled="CommoditySkuRequestVo.notDistributionPortal"
              v-model="CommoditySkuRequestVo.portalId"
              :name="CommoditySkuRequestVo.promoteThePortalAlias"
            ></biz-portal-select>
          </el-form-item>
          <el-form-item v-if="isOpenFxServer">
            <el-checkbox
              label="查看非门户推广数据"
              name="type"
              v-model="CommoditySkuRequestVo.notDistributionPortal"
            ></el-checkbox>
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="trainSchemePage.currentChange(1)">查询</el-button>
            <!-- <el-button @click="exportListData">导出列表数据</el-button> -->
            <el-button @click="exportDialog = true">导出列表数据</el-button>
          </template>
        </hb-search-wrapper>

        <el-tabs v-model="activeName" type="card" class="m-tab-card" @tab-click="pageScheme">
          <el-tab-pane label="网授班" name="first">
            <online-class-table
              :schemeLearningStatisticsResponseVo="schemeLearningStatisticsResponseVo"
              :trainSchemePage="trainSchemePage"
              :CommoditySkuRequestVo="CommoditySkuRequestVo"
              :getQuerySchemeLearningList="getQuerySchemeLearningList"
              :loading="loading"
              @getChildRef="getChildRef"
            ></online-class-table>
          </el-tab-pane>

          <el-tab-pane v-if="showOffline" label="面授班" name="third">
            <face-class-table
              :schemeLearningStatisticsResponseVo="schemeLearningStatisticsResponseVo"
              :trainSchemePage="trainSchemePage"
              :CommoditySkuRequestVo="CommoditySkuRequestVo"
              :getQuerySchemeLearningList="getQuerySchemeLearningList"
              :loading="loading"
              @getChildRef="getChildRef"
            ></face-class-table>
          </el-tab-pane>
          <el-tab-pane v-if="showOffline" label="面网授班" name="second">
            <mixed-class-table
              :schemeLearningStatisticsResponseVo="schemeLearningStatisticsResponseVo"
              :trainSchemePage="trainSchemePage"
              :CommoditySkuRequestVo="CommoditySkuRequestVo"
              :getQuerySchemeLearningList="getQuerySchemeLearningList"
              :loading="loading"
              @getChildRef="getChildRef"
            ></mixed-class-table>
          </el-tab-pane>
        </el-tabs>

        <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务管理-方案学习统计</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goDownloadPage(1)">前往下载</el-button>
          </div>
        </el-dialog>
        <el-dialog title="提示" :visible.sync="exportPersonnelSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务管理-方案学习统计导出列表人员详情</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportPersonnelSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goDownloadPage(2)">前往下载</el-button>
          </div>
        </el-dialog>
        <el-drawer
          title="提示"
          v-if="$hasPermission('export')"
          desc="导出"
          actions="confirmExportSort"
          :visible.sync="exportDialog"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" class="m-form f-mt20">
              <el-form-item label="请选择导出方式：">
                <el-radio-group v-model="exportSort">
                  <el-radio label="导出数据列表" :value="1">导出数据列表</el-radio>
                  <el-radio label="导出列表人员详细" :value="2">导出列表人员详细</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="exportDialog = false">取消</el-button>
                <el-button type="primary" @click="confirmExportSort(exportSort)">确定</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-common/src/models/sku'
  import {
    CommoditySkuSortRequest,
    DateScopeRequest,
    RegionSkuPropertyRequest,
    RegionSkuPropertySearchRequest,
    SchemeRequest,
    SkuPropertyRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import SaleChannelType from '@api/service/common/enums/trade/SaleChannelType'
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import StaticticalReportManagerModule from '@api/service/management/statisticalReport/StaticticalReportManagerModule'
  import { QuerySchemeLearningList } from '@api/service/management/statisticalReport/query/QuerySchemeLearningList'
  import { CommoditySkuRequestVo } from '@api/service/management/statisticalReport/query/vo/CommoditySkuRequestVo'
  import { LearningStatisticsResponse } from '@api/service/management/statisticalReport/query/vo/LearningStatisticsResponseVo'
  import { SchemeLearningStatisticsResponseVo } from '@api/service/management/statisticalReport/query/vo/SchemeLearningStatisticsResponseVo'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import { Query, UiPage } from '@hbfe/common'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import { cloneDeep } from 'lodash'
  import { Component, Vue, Watch } from 'vue-property-decorator'
  import LearningSchemeSelect from '@hbfe/jxjy-admin-schemeLearningStatistic/src/__components__/learning-scheme-select.vue'
  import MixedClassTable from './__components__/mixed-class-table.vue'
  import OnlineClassTable from './__components__/online-class-table.vue'
  import FaceClassTable from './__components__/face-class-table.vue'
  import QueryShowOffline from '@api/service/common/config/QueryShowOffline'
  @Component({
    components: {
      DoubleDatePicker,
      LearningSchemeSelect,
      BizPortalSelect,
      BizDistributorSelect,
      MixedClassTable,
      OnlineClassTable,
      FaceClassTable
    }
  })
  export default class extends Vue {
    select = ''
    input = ''
    dialog7 = false
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    tableData4 = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }]
    tableData9 = [
      { field101: '1' },
      { field101: '2' },
      { field101: '3' },
      { field101: '4' },
      { field101: '5' },
      { field101: '2' },
      { field101: '4' },
      { field101: '5' },
      { field101: '2' }
    ]
    loading = false
    // 分页参数 - 培训方案
    trainSchemePage: UiPage
    // 分页加载参数 - 培训方案
    trainSchemeQuery: Query = new Query()
    form = {
      data1: ''
    }
    //培训方案id
    commitSchemeId = ''
    // 培训方案名称
    schemeName = ''
    // 排序策略
    sortPolicy: Array<CommoditySkuSortRequest> = new Array<CommoditySkuSortRequest>()

    // 培训方案业务状态层入口
    schemeBusinessEntry: QueryTrainClassCommodityList = new QueryTrainClassCommodityList()
    /**
     * 培训方案类型
     */
    schemeTypeInfo: Array<string> = new Array<string>()
    // 培训方案列表
    trainSchemeList: Array<TrainClassCommodityVo> = new Array<TrainClassCommodityVo>()
    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''

    /**
     * 培训类别Id
     */
    trainingCategoryId = ''
    /**
     * 培训对象Id
     */
    trainingObjectId = ''
    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: false,
      // 培训类别
      trainingCategory: false,
      // 岗位类别
      positionCategory: false,
      // 培训对象
      trainingObject: false,
      // 技术等级
      technicalGrade: false,
      //科目
      discipline: false,
      // 学段
      learningPhase: false,
      // 证书类型
      certificatesType: false,
      // 执业类别
      practitionerCategory: false
    }
    /**
     * 当前网校信息
     */
    envConfig = {
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 工勤行业Id
      workServiceId: '',
      // 职业卫生行业Id
      professionHealthIndustryId: '',
      // 教师行业id
      teacherIndustryId: '',
      // 药师行业id
      medicineIndustryId: ''
    }
    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuProperty = new SchemeSkuProperty()
    getQuerySchemeLearningList: QuerySchemeLearningList =
      StaticticalReportManagerModule.queryStaticticalReportFactory.getQuerySchemeLearningList()
    //导出成功弹窗
    exportSuccessVisible = false
    exportPersonnelSuccessVisible = false
    exportSort = 0
    //查询入参
    CommoditySkuRequestVo: CommoditySkuRequestVo = new CommoditySkuRequestVo()
    exportQueryParam: CommoditySkuRequestVo = new CommoditySkuRequestVo()
    commoditySkuIdList = new Array<HasSelectSchemeMode>()
    schemeLearningStatisticsResponseVo: Array<SchemeLearningStatisticsResponseVo> =
      new Array<SchemeLearningStatisticsResponseVo>()
    activeName = 'first'
    exportDialog = false
    /**
     * 是否专题
     */
    saleChannels: boolean = null
    showOffline = !QueryShowOffline.getShowOfflineApolloConfig()
    /**
     * 判断当前网校是否开启分销服务
     */
    get isOpenFxServer() {
      return CapabilityServiceConfig.fxCapabilityEnable
    }
    constructor() {
      super()
      this.trainSchemePage = new UiPage(this.pageScheme, this.pageScheme)
    }
    // 培训方案入参
    @Watch('commoditySkuIdList', {
      deep: true
    })
    changeScheme(val: Array<HasSelectSchemeMode>) {
      if (val?.length && val[0]?.schemeId) {
        this.exportQueryParam.commoditySkuIdList = [] as string[]
        this.exportQueryParam.commoditySkuIdList.push(val[0].schemeId)
        this.commitSchemeId = val[0]?.id
      } else {
        this.exportQueryParam.commoditySkuIdList = undefined
        this.commitSchemeId = undefined
      }
    }
    /**
     * 初始化查询参数
     */
    initQueryParam() {
      this.CommoditySkuRequestVo = new CommoditySkuRequestVo()

      this.CommoditySkuRequestVo.promoteThePortalAlias = ''
      this.CommoditySkuRequestVo.portalId = ''
      this.CommoditySkuRequestVo.distributorId = ''
      this.CommoditySkuRequestVo.distributorName = ''

      this.CommoditySkuRequestVo.registerTime = new DateScopeRequest()
      this.CommoditySkuRequestVo.registerTime.begin = ''
      this.CommoditySkuRequestVo.registerTime.end = ''
      this.CommoditySkuRequestVo.schemeRequest = new SchemeRequest()
      this.CommoditySkuRequestVo.skuPropertyRequest = new SkuPropertyRequest()
      this.CommoditySkuRequestVo.skuPropertyRequest.year = new Array<string>()
      this.CommoditySkuRequestVo.skuPropertyRequest.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      this.CommoditySkuRequestVo.skuPropertyRequest.regionSkuPropertySearch.region =
        new Array<RegionSkuPropertyRequest>()
      this.CommoditySkuRequestVo.skuPropertyRequest.industry = new Array<string>()
      this.CommoditySkuRequestVo.skuPropertyRequest.subjectType = new Array<string>()
      this.CommoditySkuRequestVo.skuPropertyRequest.trainingCategory = new Array<string>()
      this.CommoditySkuRequestVo.skuPropertyRequest.trainingProfessional = new Array<string>()
      // 初始化添加 学科、学段置空
      // todo
      this.sortPolicy = new Array<CommoditySkuSortRequest>()
      this.schemeTypeInfo = new Array<string>()
    }
    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.technicalGrade = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      this.localSkuProperty.discipline = ''
      this.localSkuProperty.learningPhase = ''
      this.localSkuProperty.pharmacistIndustry = [] as string[]
    }
    /**
     * 学段变化清空学科
     */
    updateStudyPeriod(val: string) {
      this.localSkuProperty.learningPhase = val
      this.localSkuProperty.discipline = ''
    }
    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => el.code == 'SUBJECT_TYPE')
      const configTrainingCategory = configList.findIndex((el) => el.code == 'TRAINING_CATEGORY')
      const configPositionCategory = configList.findIndex((el) => el.code == 'POSITION_CATEGORY')
      const configTrainingObject = configList.findIndex((el) => el.code == 'TRAINNING_OBJECT')
      const configTechnicalLevel = configList.findIndex((el) => el.code == 'JOB_LEVEL')
      // 修改
      const configTechnicalStudying = configList.findIndex((el) => el.code == 'LEARNING_PHASE')
      const configTechnicalSubject = configList.findIndex((el) => el.code == 'DISCIPLINE')
      const configCertificatesType = configList.findIndex((el) => el.code == 'CERTIFICATES_TYPE')
      const configPractitionerCategory = configList.findIndex((el) => (el.code = 'PRACTITIONER_CATEGORY'))
      this.skuVisible.subjectType = configSubjectType > -1 ? true : false
      this.skuVisible.trainingCategory = configTrainingCategory > -1 ? true : false
      this.skuVisible.positionCategory = configPositionCategory > -1 ? true : false
      this.skuVisible.trainingObject = configTrainingObject > -1 ? true : false
      this.skuVisible.technicalGrade = configTechnicalLevel > -1 ? true : false
      // 获取教师配置
      this.skuVisible.learningPhase = configTechnicalStudying > -1 ? true : false
      this.skuVisible.discipline = configTechnicalSubject > -1 ? true : false
      this.skuVisible.certificatesType = configCertificatesType > -1 ? true : false
      this.skuVisible.practitionerCategory = configPractitionerCategory > -1 ? true : false
    }
    // 卫生行业培训类别联动
    handleWSUpdateTrainingObject(value: string) {
      this.localSkuProperty.positionCategory = ''
      this.trainingObjectId = value
    }
    /**
     * 响应组件行业Id集合传参
     */
    handleIndustryInfos(values: any) {
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.professionHealthIndustryId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
      this.envConfig.medicineIndustryId = values.medicineIndustryId || ''
    }
    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      await this.initQueryParam()
      this.localSkuProperty = new SchemeSkuProperty()
      this.commoditySkuIdList = new Array<HasSelectSchemeMode>()
      // this.onShelveStatus = null
      this.schemeName = ''
      this.sortPolicy = new Array<CommoditySkuSortRequest>()
      this.saleChannels = null
      this.CommoditySkuRequestVo.portalId = ''
      await this.searchBase()
    }
    // 选择是否来源专题
    changeSaleChannels() {
      this.CommoditySkuRequestVo.saleChannel = SaleChannelType.getSpecialSubjectSaleChannel(this.saleChannels)
      if (!this.saleChannels && this.saleChannels != null) {
        this.CommoditySkuRequestVo.trainingChannelName = ''
      }
    }
    /**
     * 加载第一页
     */
    async searchBase() {
      this.trainSchemePage.pageNo = 1
      await this.pageScheme()
    }
    /**
     * 处理列表查询参数
     */
    getPageQueryParams() {
      this.getLocalSkuProperty()
      this.CommoditySkuRequestVo.saleTitleMatchLike = this.schemeName || undefined
      // if (this.commoditySkuIdList && this.commoditySkuIdList.length > 0) {

      //   this.CommoditySkuRequestVo.commoditySkuIdList = [this.commoditySkuIdList[0].schemeId] as any
      //   // this.CommoditySkuRequestVo.saleTitleMatchLike = this.commoditySkuIdList[0].scheme || undefined
      // }

      if (this.activeName == 'first') {
        this.CommoditySkuRequestVo.trainingType = 'trainingWay0001'
      } else if (this.activeName == 'second') {
        this.CommoditySkuRequestVo.trainingType = 'trainingWay0002'
      } else {
        this.CommoditySkuRequestVo.trainingType = 'trainingWay0003'
      }
      // this.CommoditySkuRequestVo.unitIdList = this.localSkuProperty.institutionId
      //   ? [this.localSkuProperty.institutionId]
      //   : []
      // this.CommoditySkuRequestVo.extInfoRequest = new CommoditySkuExtInfoRequest()
      // this.CommoditySkuRequestVo.extInfoRequest.resourceProviderIdList = this.localSkuProperty.resourceSuppiler
      //   ? [this.localSkuProperty.resourceSuppiler]
      //   : []
      this.configureTrainSchemeQueryParam()
    }
    /**
     * 配置查询参数
     */
    configureTrainSchemeQueryParam() {
      // 处理培训方案类型
      if (!this.schemeTypeInfo.length) {
        this.CommoditySkuRequestVo.schemeRequest.schemeType = undefined
      }
      const schemeType = this.schemeTypeInfo[this.schemeTypeInfo.length - 1]
      // if (schemeType === 'chooseCourseLearning' || schemeType === 'autonomousCourseLearning') {
      this.CommoditySkuRequestVo.schemeRequest.schemeType = schemeType
      // } else {
      //   this.CommoditySkuRequestVo.schemeRequest.schemeType = undefined
      // }
    }

    /**
     * 获取本地sku选项
     */
    getLocalSkuProperty() {
      // const skuProperties = cloneDeep(this.trainSchemeQueryParam.skuPropertyRequest)
      const skuProperties = cloneDeep(this.CommoditySkuRequestVo.skuPropertyRequest)
      skuProperties.year = !this.localSkuProperty.year ? ([] as string[]) : this.localSkuProperty.year
      skuProperties.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
      const localRegion = cloneDeep(this.localSkuProperty.region)
      if (Array.isArray(localRegion) && localRegion.length) {
        const option = new RegionSkuPropertyRequest()
        option.province = localRegion.length >= 1 ? localRegion[0] : undefined
        option.city = localRegion.length >= 2 ? localRegion[1] : undefined
        option.county = localRegion.length >= 3 ? localRegion[2] : undefined
        // option.province = this.localSkuProperty.region[0]
        // option.city = this.localSkuProperty.region ? this.localSkuProperty.region[1] : undefined
        // option.county = undefined
        skuProperties.regionSkuPropertySearch.region.push(option)
        skuProperties.regionSkuPropertySearch.regionSearchType = 1
      } else {
        skuProperties.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      }
      skuProperties.industry = !this.localSkuProperty.industry ? ([] as string[]) : [this.localSkuProperty.industry]
      skuProperties.subjectType = !this.localSkuProperty.subjectType
        ? ([] as string[])
        : [this.localSkuProperty.subjectType]
      skuProperties.trainingCategory = !this.localSkuProperty.trainingCategory
        ? ([] as string[])
        : [this.localSkuProperty.trainingCategory]
      skuProperties.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      skuProperties.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      skuProperties.technicalGrade = !this.localSkuProperty.technicalGrade
        ? ([] as string[])
        : [this.localSkuProperty.technicalGrade]
      skuProperties.trainingProfessional = this.getTrainingProfessional()

      // 学科、学段转换
      skuProperties.learningPhase = !this.localSkuProperty.learningPhase
        ? ([] as string[])
        : [this.localSkuProperty.learningPhase]
      skuProperties.discipline = !this.localSkuProperty.discipline
        ? ([] as string[])
        : [this.localSkuProperty.discipline]
      // skuProperties.certificatesType = !this.localSkuProperty.pharmacistIndustry
      //   ? ([] as string[])
      //   : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
      //   ? [this.localSkuProperty.pharmacistIndustry[0]]
      //   : []
      // skuProperties.practitionerCategory = !this.localSkuProperty.pharmacistIndustry
      //   ? ([] as string[])
      //   : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
      //   ? [this.localSkuProperty.pharmacistIndustry[1]]
      //   : []
      // this.trainSchemeQueryParam.skuPropertyRequest = cloneDeep(skuProperties)
      if (this.activeName == 'first') {
        skuProperties.trainingForm = ['trainingWay0001']
      } else if (this.activeName == 'second') {
        skuProperties.trainingForm = ['trainingWay0002']
      } else {
        skuProperties.trainingForm = ['trainingWay0003']
      }
      this.CommoditySkuRequestVo.skuPropertyRequest = cloneDeep(skuProperties)
      // ////console.log('selectedSkuProperties', JSON.stringify(this.trainSchemeQueryParam.skuPropertyRequest))
      // ////console.log('selectedSkuProperties', JSON.stringify(this.CommoditySkuRequestVo.skuPropertyRequest))
    }
    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      //////console.log('envConfig', this.envConfig)
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人设行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }

    async listSchemeLearningReportFormsInServicer() {
      return await this.getQuerySchemeLearningList.listSchemeLearningReportFormsInServicer(
        this.trainSchemePage,
        this.CommoditySkuRequestVo
      )
    }
    /**
     * 分页查询
     */
    async pageScheme() {
      this.loading = true
      this.trainSchemeQuery.loading = true
      try {
        this.getPageQueryParams()
        this.CommoditySkuRequestVo.existTrainingChannel = this.saleChannels
        const list = await this.listSchemeLearningReportFormsInServicer()
        this.schemeLearningStatisticsResponseVo = list.map((item) => {
          if (!item.learningStatistic) item.learningStatistic = new LearningStatisticsResponse()

          return item
        })
        this.loading = false
        console.log(
          this.schemeLearningStatisticsResponseVo,
          ' this.schemeLearningStatisticsResponseVo12355555555555555'
        )
      } catch (e) {
        this.loading = false
        ////console.log('获取培训方案分页列表失败！', e)
      } finally {
        this.loading = false
        //处理切换页数后行数错位问题
        if (this.activeName == 'first') {
          ;(this.$refs['schemeTable'] as any)?.doLayout()
        } else if (this.activeName == 'second') {
          ;(this.$refs['schemeMixedTable'] as any)?.doLayout()
        } else {
          ;(this.$refs['schemeFaceTable'] as any)?.doLayout()
        }
        this.trainSchemeQuery.loading = false

        this.exportQueryParam = Object.assign(new CommoditySkuRequestVo(), this.CommoditySkuRequestVo)
      }
      this.loading = false
    }
    goDownloadPage(idx: number) {
      let type = ''
      let trainingType = ''
      if (this.activeName == 'first') {
        trainingType = 'trainingWay0001'
      } else if (this.activeName == 'second') {
        trainingType = 'trainingWay0002'
      } else {
        trainingType = 'trainingWay0003'
      }
      if (idx == 1) {
        type = 'exportSchemeLearningStatistical'
        this.exportSuccessVisible = false
      } else {
        type = 'exportSchemeLearningDetailStatistical'
        this.exportPersonnelSuccessVisible = false
      }
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type, trainingType }
      })
    }
    async confirmExportSort(name: string) {
      this.exportQueryParam = Object.assign(new CommoditySkuRequestVo(), this.CommoditySkuRequestVo)
      this.exportQueryParam.saleChannel = SaleChannelType.getSpecialSubjectSaleChannel(this.saleChannels)
      this.exportQueryParam.trainingChannelName = this.CommoditySkuRequestVo.trainingChannelName
      this.exportQueryParam.trainingType = this.CommoditySkuRequestVo.trainingType
      this.exportQueryParam.saleTitleMatchLike = this.schemeName
      if (name == '导出数据列表') {
        await this.exportListData()
      } else if (name == '导出列表人员详细') {
        await this.exportPersonnelData()
      } else {
        this.$message.error('请选择导出方式')
        return
      }
    }
    // getCommitSchemeId(val: string) {
    //   //console.log(val)
    //   this.commitSchemeId = val
    // }
    async exportListData() {
      try {
        // if (this.commoditySkuIdList && this.commoditySkuIdList.length > 0 && this.commitSchemeId) {
        //   this.exportQueryParam.commoditySkuIdList = [this.commitSchemeId] as any
        // }
        const res = await this.getQuerySchemeLearningList.exportExcel(this.exportQueryParam)
        if (res.status.code == 200 && res.data) {
          //   this.$message.success('导出成功')
          this.exportDialog = false
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        ////console.log(e)
      } finally {
        //todo
      }
    }
    async exportPersonnelData() {
      try {
        // if (this.commoditySkuIdList && this.commoditySkuIdList.length > 0 && this.commitSchemeId) {
        //   this.exportQueryParam.commoditySkuIdList = [this.commitSchemeId] as any
        // }
        const res = await this.getQuerySchemeLearningList.exportExcelDetail(this.exportQueryParam)
        if (res.status.code == 200 && res.data) {
          //   this.$message.success('导出成功')
          this.exportDialog = false
          this.exportPersonnelSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        ////console.log(e)
      } finally {
        //todo
      }
    }
    async mounted() {
      this.initQueryParam()
      await this.pageScheme()
    }
    toCollectDetails(item: SchemeLearningStatisticsResponseVo) {
      let res
      if (this.CommoditySkuRequestVo.skuPropertyRequest.regionSkuPropertySearch?.region) {
        res =
          '/' +
          Object.values(this.CommoditySkuRequestVo.skuPropertyRequest.regionSkuPropertySearch?.region[0])
            .filter((region) => {
              return region
            })
            ?.join('/')
      }
      //console.log(res)
      this.$router.push({
        path: '/statistic/learning-statistic',
        query: {
          schemeName: item.trainClassDetail.commodityBasicData.saleTitle,
          schemeId: item.trainClassDetail.schemeId,
          regionType: res || '',
          year: JSON.stringify(this.CommoditySkuRequestVo.skuPropertyRequest.year) || '',
          registerTimeBegin: this.CommoditySkuRequestVo.registerTime.begin,
          registerTimeEnd: this.CommoditySkuRequestVo.registerTime.end,
          subjectName: this.CommoditySkuRequestVo.trainingChannelName,
          subjectType: JSON.stringify(this.CommoditySkuRequestVo.saleChannel)
        }
      })
    }
    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
    }

    getChildRef(e: any, type: string) {
      if (type == 'online') {
        this.$refs['schemeTable'] = e
      } else if (type == 'mixed') {
        this.$refs['schemeMixedTable'] = e
      } else if (type == 'face') {
        this.$refs['schemeFaceTable'] = e
      }
    }
  }
</script>
