<route-params content="/:id"></route-params>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/courseware')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/courseware' }">课件管理</el-breadcrumb-item>
      <el-breadcrumb-item>修改课件</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form
                :model="mutationUpdateCourseware.request"
                ref="modifyCoursewareForm"
                :rules="rules"
                label-width="120px"
                class="m-form"
              >
                <el-form-item label="课件分类：" prop="categoryIds">
                  <biz-courseware-category
                    v-model="mutationUpdateCourseware.request.categoryIds"
                    class="form-m"
                    :check-strictly="false"
                    :disabled="mutationUpdateCourseware.request.isBeingUsed"
                    :showRootNode="false"
                    ref="bizCoursewareCategoryRef"
                  ></biz-courseware-category>
                  <el-button type="text" class="f-ml15" @click="$router.push('/resource/courseware/category')"
                    >新建分类</el-button
                  >
                </el-form-item>
                <el-form-item label="课件名称：" prop="name">
                  <el-input
                    clearable
                    maxlength="50"
                    v-model="mutationUpdateCourseware.request.name"
                    show-word-limit
                    placeholder="请输入课件名称"
                  />
                </el-form-item>
                <el-form-item label="课件供应商：" prop="supplierId">
                  <biz-courseware-supplier
                    v-model="mutationUpdateCourseware.request.supplierId"
                    :disabled="mutationUpdateCourseware.request.isBeingUsed"
                    class="form-l"
                  ></biz-courseware-supplier>
                </el-form-item>
                <el-form-item label="课件教师：" prop="teacherName">
                  <el-input
                    clearable
                    maxlength="50"
                    v-model="mutationUpdateCourseware.request.teacherName"
                    show-word-limit
                    placeholder="请输入课件教师"
                  />
                </el-form-item>
                <el-form-item label="教师简介：" prop="teacherAboutsContent">
                  <el-input
                    type="textarea"
                    :rows="6"
                    maxlength="300"
                    v-model="mutationUpdateCourseware.request.teacherAboutsContent"
                    show-word-limit
                    placeholder="请输入教师简介"
                  />
                </el-form-item>
                <el-form-item label="课件简介：" prop="aboutsContent">
                  <el-input
                    type="textarea"
                    :rows="6"
                    maxlength="300"
                    v-model="mutationUpdateCourseware.request.aboutsContent"
                    show-word-limit
                    placeholder="请输入课件简介"
                  />
                </el-form-item>
                <el-form-item label="课件状态：" prop="enable">
                  <el-radio-group v-model="mutationUpdateCourseware.request.enable" :disabled="isStatusDisabled">
                    <el-radio :label="true">正常</el-radio>
                    <el-radio :label="false">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">关联媒体</span>
        </div>
        <media
          v-model="coursewareOutline"
          :name="mutationUpdateCourseware.request.name"
          :allDetail="mutationUpdateCourseware.request"
          :time.sync="documentTime"
          ref="media"
        ></media>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="doCancel">取消</el-button>
        <el-button type="primary" @click="doSave" :loading="loading">保存</el-button>
      </div>
    </div>
    <give-up-dialog :give-up-model="uiConfig.giveUpModel"></give-up-dialog>
  </el-main>
</template>
<script lang="ts">
  import { Ref, Component, Vue } from 'vue-property-decorator'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import Media from '@hbfe/jxjy-admin-courseware/src/components/media.vue'
  import QueryCourseware from '@api/service/management/resource/courseware/query/QueryCourseware'
  import CoursewareDetail from '@api/service/management/resource/courseware/query/vo/CoursewareDetail'
  import MutationUpdateCourseware from '@api/service/management/resource/courseware/mutation/MutationUpdateCourseware'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'
  import CoursewareFactory from '@api/service/management/resource/courseware/CoursewareFactory'
  import { MediaSourceTypeEnum } from '@api/service/management/resource/courseware/enum/MediaSourceType'
  import { CoursewareStatusEnum } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
  import { cloneDeep } from 'lodash'
  @Component({
    components: {
      GiveUpDialog,
      Media
    }
  })
  export default class extends Vue {
    @Ref('modifyCoursewareForm') modifyCoursewareForm: any
    @Ref('bizCoursewareCategoryRef') bizCoursewareCategoryRef: any
    @Ref('media') media: any
    rules = {
      categoryIds: [{ required: true, message: '课件分类不可为空！', trigger: ['change', 'blur'] }],
      name: [
        { required: true, message: '课件名称不可为空！', trigger: ['change', 'blur'] },
        {
          validator: async (rule: any, value: any, callback: any) => {
            await this.checkName(rule, value, callback)
          },
          trigger: 'blur'
        }
      ],
      supplierId: [{ required: true, message: '课件供应商不可为空！', trigger: ['change', 'blur'] }],
      // teacherName: [{ required: true, message: '课件教师不可为空！', trigger: ['change', 'blur'] }],
      enable: [{ required: true, message: '课件状态不可为空！', trigger: ['change', 'blur'] }]
    }
    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }
    teacher = new SimpleUserInfo()
    coursewareOutline: Array<any> = new Array<any>()
    documentTime = 0
    coursewareFactory = new CoursewareFactory()
    mutationUpdateCourseware: MutationUpdateCourseware = new MutationUpdateCourseware()

    loading = false
    CoursewareStatusEnum = CoursewareStatusEnum
    isStatusDisabled = false

    // 临时时间
    temporaryTime = 0
    constructor() {
      super()
      this.mutationUpdateCourseware = new MutationUpdateCourseware()
    }

    async checkName(rule: any, value: any, callback: any) {
      const res: any = await ResourceModule.coursewareFactory.getCheckCourseWare(value, this.courseWareId)
      if (res?.code !== '200') {
        return callback('课件名称重复，请修改')
      }
      return callback()
    }
    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
      this.uiConfig.giveUpModel.routerUrl = '/resource/courseware'
    }

    async doSave() {
      this.loading = true
      this.modifyCoursewareForm.validate(async (valid: boolean) => {
        if (valid) {
          this.documentTime = 0
          await this.validateMediaAndSave()
        }
      })
    }

    async validateMediaAndSave() {
      this.loading = true
      try {
        if (this.media.mediaType == MediaSourceTypeEnum.huawei) {
          if (!this.coursewareOutline.length) {
            this.$message.warning('关联媒体不能为空')
            this.loading = false
            return
          } else {
            if (this.coursewareOutline[0].courseWares[0]?.percen !== 100) {
              this.$message.warning('请等待文件上传完毕')
              this.loading = false
              return
            }
          }
          // 如果是华为云需要走的校验
          const res = await this.media.validateMediae()
          console.log(res, 'res')
          if (!res.status) {
            console.log('华为云校验不通过！')
            this.loading = false
            return
          }
        } else {
          // 新增外链校验
          if (this.media.mediaType == MediaSourceTypeEnum.outer && this.offChainCourse()) {
            this.$confirm('播放地址必须填写一种，请调整后提交。', '提示', {
              confirmButtonText: '我知道了',
              type: 'warning',
              showCancelButton: false
            }).then(async () => {
              console.log('点击了我知道了')
            })
            this.loading = false
            return
          }
          // 如果是外链需要走的校验 因为没有返回值 所以使用Promise传参
          const res = await this.media.validateOffMedia()
          console.log(res, 'res')
          if (res.status) {
            console.log('外链校验不通过！')
            this.loading = false
            return
          }
        }
        if (this.documentTime) {
          //文档播放时间
          const documentTime = this.documentTime > 0 ? this.documentTime : 0
        }

        console.log(
          this.coursewareOutline[0].courseWares[0].timeLength,
          'this.coursewareOutline[0].courseWares[0].timeLength'
        )
        this.temporaryTime = this.documentTime
        console.log(this.documentTime, 'this.documentTime')
        if (
          this.coursewareOutline[0] &&
          this.coursewareOutline[0].courseWares[0] &&
          this.coursewareOutline[0].courseWares[0].timeLength
        ) {
          this.mutationUpdateCourseware.request.isOuter = false
          this.mutationUpdateCourseware.request.resourceDataDto.timeLength = this.temporaryTime
          this.mutationUpdateCourseware.request.resourceDataDto.resourcePath = this.coursewareOutline[0].courseWares[0].coursewareResourcePath
        }
        // 外链传参
        if (this.media.mediaType == MediaSourceTypeEnum.outer) {
          this.mutationUpdateCourseware.request.isOuter = true
          // 获取媒体外部链式调用返回的第一个课程资料对象中的标准地址、高清地址和超清地址
          this.mutationUpdateCourseware.request.standardAddress = this.media.outsideChainCall().courseWares[0].standardAddress
          this.mutationUpdateCourseware.request.highAddress = this.media.outsideChainCall().courseWares[0].highAddress
          this.mutationUpdateCourseware.request.superAddress = this.media.outsideChainCall().courseWares[0].superAddress
        }

        const res = await this.mutationUpdateCourseware.doUpdate()
        if (res.code === 200) {
          this.$message.success('保存成功')
          this.$router.push('/resource/courseware')
        } else if (res.code === 500 && res.businessCode === 1001) {
          this.loading = false
          this.$message.error('课件名称不允许重复')
        } else if (res.code === 500 && res.businessCode === 1002) {
          this.loading = false
          this.$message.error('目前还在转码中，暂不能修改')
        } else {
          this.loading = false
          this.$message.error(res.tip)
        }
      } catch (e) {
        this.loading = false
        this.$message.warning('保存失败')
        console.log(e)
      }
    }
    courseWareId = ''
    clearVaild() {
      this.modifyCoursewareForm.clearValidate()
    }
    async created() {
      this.courseWareId = this.$route.params.id
      //获取课件详情
      this.mutationUpdateCourseware = await this.coursewareFactory.getUpdateCourseware(this.$route.params.id)
      // 控制课件状态是否禁用
      this.isStatusDisabled = this.mutationUpdateCourseware.request.status.current != CoursewareStatusEnum.AVAILABLE

      this.$nextTick(async () => {
        // 获取课件分类树
        await this.bizCoursewareCategoryRef.handleFocus()
      })
      this.documentTime = this.mutationUpdateCourseware.request.resourceDataDto.timeLength
      console.log(this.mutationUpdateCourseware, 'mutationUpdateCourseware')
      this.$nextTick(this.clearVaild)
    }

    // 外链课件校验
    offChainCourse() {
      if (
        this.media.outsideChainForm.standardAddress ||
        this.media.outsideChainForm.highAddress ||
        this.media.outsideChainForm.superAddress
      ) {
        return false
      } else {
        return true
      }
    }
  }
</script>
