import detail from './queries/detail.graphql'
import exportTrainingInstitutionInfo from './queries/exportTrainingInstitutionInfo.graphql'
import findDetailList from './queries/findDetailList.graphql'
import findServicerSpellList from './queries/findServicerSpellList.graphql'
import findTrainingInstitutionAddedServicesLog from './queries/findTrainingInstitutionAddedServicesLog.graphql'
import findTrainingInstitutionByDomain from './queries/findTrainingInstitutionByDomain.graphql'
import findTrainingInstitutionList from './queries/findTrainingInstitutionList.graphql'
import getTrainingInstitutionCount from './queries/getTrainingInstitutionCount.graphql'
import institutionTrainingPromotionAuthExist from './queries/institutionTrainingPromotionAuthExist.graphql'
import page from './queries/page.graphql'
import authCVendorPromotionAllTraining from './mutates/authCVendorPromotionAllTraining.graphql'
import authCVendorPromotionTraining from './mutates/authCVendorPromotionTraining.graphql'
import cancelAuthCVendorPromotionTraining from './mutates/cancelAuthCVendorPromotionTraining.graphql'
import syncTrainingInstitution from './mutates/syncTrainingInstitution.graphql'
import synchronizeTrainingInstitutions from './mutates/synchronizeTrainingInstitutions.graphql'

export {
  detail,
  exportTrainingInstitutionInfo,
  findDetailList,
  findServicerSpellList,
  findTrainingInstitutionAddedServicesLog,
  findTrainingInstitutionByDomain,
  findTrainingInstitutionList,
  getTrainingInstitutionCount,
  institutionTrainingPromotionAuthExist,
  page,
  authCVendorPromotionAllTraining,
  authCVendorPromotionTraining,
  cancelAuthCVendorPromotionTraining,
  syncTrainingInstitution,
  synchronizeTrainingInstitutions
}
