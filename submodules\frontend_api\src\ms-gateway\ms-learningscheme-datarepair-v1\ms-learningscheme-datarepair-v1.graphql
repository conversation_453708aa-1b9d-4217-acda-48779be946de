"""独立部署的微服务,K8S服务名:ms-learningscheme-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""方案配置资源修复接口
		@param configureId 配置id
	"""
	schemeConfigureResourceRepair(configureId:String):Void
	"""学员
		@param request
	"""
	studentExamRepair(request:StudentExamRepairRequest):Void
	"""参训资格修复为冻结状态
		@param request:
		<AUTHOR> By Cb
		@since 2024/7/22 14:53
	"""
	trainingQualificationRepairToFrozen(request:TrainingQualificationRepairToFrozenRequest):Void
	trainingQualificationRepairToNormal(request:TrainingQualificationRepairToNormalRequest):Void
}
"""学员考试修复请求
	<AUTHOR>
"""
input StudentExamRepairRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.StudentExamRepairRequest") {
	schemeId:String!
	qualificationIds:[String]!
	score:Double!
}
"""参训资格状态修改为冻结请求"""
input TrainingQualificationRepairToFrozenRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.TrainingQualificationRepairToFrozenRequest") {
	qualificationId:String
	userId:String
	schemeId:String
	sourceType:String
	sourceId:String
}
"""TODO
	<AUTHOR>
"""
input TrainingQualificationRepairToNormalRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.TrainingQualificationRepairToNormalRequest") {
	qualificationId:String
	userId:String
	schemeId:String
	sourceType:String
	sourceId:String
}

scalar List
