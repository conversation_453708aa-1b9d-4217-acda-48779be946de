import { SortCourseNumResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import CoursewareSupplierListDetail from '@api/service/management/authority/service-provider/query/vo/CoursewareSupplierListDetail'

export default class ChooseProviderItem {
  /**
   * 课件供应商id
   */
  serviceProviderId = ''
  //
  // /**
  //  * 内容提供方id
  //  */
  // contentProviderId = ''

  /**
   * 课件供应商名称
   */
  serviceProviderName = ''

  /**
   * 创建时间
   */
  createTime = ''

  /**
   * 选中状态
   */
  select = false

  /**
   * 课程配置数量
   */
  courseConfigNum = 0

  /**
   * 已勾选课件供应商模型转化
   * @param dto 后端数据模型
   * @param providerDetail 课件供应商详情
   */
  static selectFrom(dto: SortCourseNumResponse, providerDetail: CoursewareSupplierListDetail) {
    const vo = new ChooseProviderItem()
    vo.courseConfigNum = dto?.num
    vo.serviceProviderId = providerDetail?.coursewareId
    vo.createTime = providerDetail?.createTime
    vo.serviceProviderName = providerDetail?.name
    vo.select = true
    return vo
  }

  /**
   * 未勾选课件供应商模型转化
   * @param providerDetail 课件供应商详情
   * @param selectList 已选列表
   */
  static unSelectFrom(providerDetail: CoursewareSupplierListDetail, selectList: Array<ChooseProviderItem>) {
    const vo = new ChooseProviderItem()
    vo.serviceProviderId = providerDetail?.coursewareId
    vo.createTime = providerDetail?.createTime
    vo.serviceProviderName = providerDetail?.name
    const findSelect =
      selectList?.length && selectList.find(it => it.serviceProviderId === providerDetail?.coursewareId)
    vo.courseConfigNum = findSelect?.courseConfigNum || 0
    vo.select = !!findSelect
    return vo
  }
}
