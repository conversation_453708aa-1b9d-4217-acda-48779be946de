<template>
  <!--大题弹窗配置-->
  <el-drawer
    title="添加大题"
    :visible.sync="randomConfigurationShow"
    size="850px"
    custom-class="m-drawer"
    destroy-on-close
    :wrapper-closable="false"
    :show-close="false"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="whenClose"
  >
    <div class="drawer-bd">
      <el-form
        ref="elForm"
        :model="currentConfigurationItem"
        :rules="rules"
        size="medium"
        label-width="140px"
        class="m-form f-mt20"
      >
        <el-col :span="22">
          <el-form-item label="大题标题" prop="groupName">
            <el-input
              v-model="currentConfigurationItem.groupName"
              placeholder="请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。"
              clearable
              :style="{ width: '100%' }"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="题型" prop="questionType">
            <el-radio-group v-model="drawingQuestionType" size="medium">
              <el-radio :label="1">单选</el-radio>
              <el-radio :label="4">判断</el-radio>
              <el-radio :label="2">多选</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-show="rangeQuestion !== 2">
          <el-form-item label="抽题规则" prop="questionScopesTypes">
            <el-radio-group v-model="drawingQuestionRule" size="medium">
              <el-radio :label="1">智能抽题</el-radio>
              <el-radio :label="3">按照题库指定数量</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="本大题总分" prop="totalScore">
            <el-input-number
              :min="0"
              controls-position="right"
              :precision="0"
              size="mini"
              v-model.number="currentConfigurationItem.totalScore"
              placeholder="请输入本大题总分"
              clearable
            >
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <!--   原型没有限制本大题提数数量，注释如下代码 :max="totalTypeQuestionCount" -->
          <el-form-item label="本大题题数" prop="questionCount">
            <el-input-number
              :min="0"
              size="mini"
              controls-position="right"
              v-model.number="currentConfigurationItem.questionCount"
              placeholder="请输入本大题题数"
              clearable
            >
            </el-input-number>
          </el-form-item>
        </el-col>
        <div v-if="drawingQuestionRule === 3">
          <el-col :span="16" v-for="(item, index) in questionBankList" :key="index">
            <el-form-item :label="`${item.name}` + '题库:'" prop="questionBankList" :rules="rules.questionBankList">
              <el-input-number
                :min="0"
                size="mini"
                controls-position="right"
                v-model="item.count"
                :placeholder="'请输入' + `${item.name}` + '抽题数'"
                class="input-num"
                @blur="questionComparison(item)"
              />
            </el-form-item>
          </el-col>
        </div>

        <el-col :span="22">
          <el-form-item label="">
            <span class="f-co">注：试卷为智能组卷，配置的大题数量，需先核实是否有足够的试题满足抽取。</span>
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item class="m-btn-bar">
            <el-button @click="cancelRandomConfigurationItem">取消</el-button>
            <el-button @click="commitRandomConfigurationItem" type="primary">保存</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import { ElForm } from 'element-ui/types/form'
  import { cloneDeep } from 'lodash'
  import QuestionExtract from '@api/service/management/resource/exam-paper/mutation/vo/common/QuestionExtract'
  import ExamLibrary from '@api/service/management/resource/exam-paper/mutation/vo/common/ExamLibrary'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import LibraryQuestionCountResponseVo from '@api/service/management/resource/question-library/query/vo/LibraryQuestionCountResponseVo'
  @Component
  export default class extends Vue {
    @Ref('elForm')
    elForm: ElForm
    /**
     *   1.增加答题  2、编辑大题
     */
    @Prop({ type: Number, default: 1 })
    randomConfigurationOperationType: number
    /**
     *   当前编辑的试题类型
     */
    @Prop({ type: Number, default: 0 })
    currentEditQuestionType: number
    /**
     *   弹窗是否展示
     */
    @Prop({ type: Boolean, default: false })
    show: boolean
    /**
     * 出题范围
     */
    @Prop({ type: Number, default: 1 })
    rangeQuestion: number

    /**
     *  抽题规则
     */
    @Prop({ type: Number, default: 0 })
    questionRules: number

    /**
     *  答题配置项
     */
    @Prop({
      type: Object,
      default: () => {
        return new QuestionExtract()
      }
    })
    currentItem: QuestionExtract

    /**
     *获取题库数据
     **/

    @Prop({
      type: Array,
      default: () => {
        return new Array<ExamLibrary>()
      }
    })
    examLibrary: Array<ExamLibrary>

    @Watch('rangeQuestion', { immediate: true, deep: true })
    rangeQuestionChange(val: any) {
      if (val) {
        this.questionAccordingId = 2
      }
    }

    @Watch('questionRules', {
      deep: true,
      immediate: true
    })
    questionRulesChange(val: any) {
      if (val) {
        this.drawingQuestionRule = 1
      }
    }

    @Watch('examLibrary', {
      deep: true,
      immediate: true
    })
    examLibraryChange(val: any) {
      if (val) {
        this.questionBankList = cloneDeep(val)
      }
    }

    questionBankList = new Array<ExamLibrary>()
    drawingQuestionRule = 1
    drawingQuestionType = 1
    questionBankListIndex = 0
    questionAccordingId = 1
    libraryQuestionCountResponseVo: Array<LibraryQuestionCountResponseVo> = new Array<LibraryQuestionCountResponseVo>()
    /**
     *  获取系统提供的试题题型
     */
    questionTypeList = [
      {
        title: '单选',
        value: 1
      },
      {
        title: '多选',
        value: 2
      },
      {
        title: '判断',
        value: 4
      }
    ]

    currentConfigurationItem = new QuestionExtract()

    @Watch('currentConfigurationItem', {
      immediate: true,
      deep: true
    })
    currentConfigurationItemChange(val: any) {
      if (val) {
        // this.currentConfigurationItem.questionScopesTypes = this.drawingQuestionRule
        // if (val.questionScopesTypes == 3) {
        //   this.drawingQuestionRule = val.questionScopesTypes
        //   this.drawingQuestionType = val.questionType
        console.log(val, 'val打开的大题信息')
        val.libraryMapQuestionNumSettings?.forEach((p: any, i: any) => {
          this.questionBankList[i].count = p.count
        })
        // }
      }
    }
    rules = {
      groupName: [
        {
          required: true,
          message: '请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。',
          trigger: 'blur'
        }
      ],
      questionType: [
        {
          required: true,
          message: '请选择题型',
          trigger: 'blur'
        }
      ],
      questionScopesTypes: [
        {
          required: true,
          message: '抽题规则不能为空',
          trigger: 'change'
        }
      ],
      totalScore: [
        {
          required: true,
          message: '请输入本大题总分',
          trigger: 'blur'
        },
        {
          validator: (rule: any, value: any, callback: (error?: Error) => void) => {
            if (!Number.isInteger(value)) {
              callback(new Error('请输入数字值'))
            } else {
              if (value <= 0) {
                callback(new Error('本大题总分必须大于0'))
              } else {
                callback()
              }
            }
          },
          trigger: 'blur'
        }
      ],
      questionCount: [
        {
          required: true,
          message: '请输入本大题题数',
          trigger: 'blur'
        },
        {
          validator: (rule: any, value: any, callback: (error?: Error) => void) => {
            this.heckAge(rule, value, callback)
          },
          trigger: 'blur'
        }
      ],

      questionBankList: [
        {
          validator: this.validateQuestionBankList,
          trigger: 'blur'
        }
      ]
    }
    // @Watch('drawingQuestionType', {
    //   deep: true,
    //   immediate: true
    // })
    // drawingQuestionTypeChange(val: any) {
    //   this.getNumberQuestionBanks(this.drawingQuestionType)
    // }
    @Watch('drawingQuestionRule')
    drawingQuestionRuleChange() {
      this.currentConfigurationItem.questionCount = 0
      this.questionBankList.forEach((exam: ExamLibrary) => {
        exam.count = 0
      })
      if (this.drawingQuestionRule == 3) {
        this.getNumberQuestionBanks(this.drawingQuestionType)
      }
    }

    get totalCount() {
      let count = 0
      this.questionBankList.forEach((exam: ExamLibrary) => {
        count += exam.count || 0
      })
      return count
    }

    get totalEnableQuestionCount() {
      let enableCount = 0
      this.questionBankList.forEach((exam: ExamLibrary) => {
        enableCount += exam.enabledQuestions
      })
      return this.drawingQuestionRule === 1 ? undefined : enableCount
    }
    get totalTypeQuestionCount() {
      //获取试题类型总数
      let enableCount = 0
      if (this.rangeQuestion === 1) {
        this.questionBankList.forEach((exam: ExamLibrary) => {
          enableCount += exam.questionTypeCount
        })
      } else {
        enableCount = 100
      }
      // console.log('获取试题类型总数:', enableCount)
      return enableCount
    }
    validateQuestionBankList(rule: any, value: any, callback: (error?: Error) => void) {
      if (this.totalCount !== this.currentConfigurationItem.questionCount) {
        return callback(new Error('各题库抽题的数量必须等于本大题题数'))
      }
      this.elForm.clearValidate('questionBankList')
      callback()
    }

    // 校验
    heckAge(rule: any, value: number, callback: (error?: Error) => void) {
      console.log(value, '====')
      if (!Number.isInteger(value)) {
        callback(new Error('请输入数字值'))
      } else if (value < 1) {
        callback(new Error('该大题至少有 1 题'))
      } else {
        const singleScore = this.currentConfigurationItem.totalScore % value

        // 0.5分
        const score = this.currentConfigurationItem.totalScore / value
        const index = String(score).indexOf('.')
        const str = String(score).slice(index)

        if (singleScore !== 0 && value) {
          //有余数，判断是否为 0.5 分；比如 10.5, 5.5此类分值
          if (str == '.5' && str.length === 2) {
            callback()
          } else {
            callback(new Error('大题总分/大题总数需能除得尽'))
          }
        } else {
          if (this.drawingQuestionRule == 3) {
            // 按照指定题库数量
            callback()
          }
          if (this.drawingQuestionRule == 1) {
            // 如果是按学员课程id出题不做限制
            if (this.rangeQuestion == 2) return callback()
            // 智能抽题
            let allQuestionTypeCount = 0
            // 获取各题库具体题型启用数量总和
            console.log(this.questionBankList)
            this.questionBankList.map((item: ExamLibrary) => {
              allQuestionTypeCount += item.questionTypeCount
            })
            if (value > allQuestionTypeCount) {
              callback(new Error('本题库题数不满足抽取的题数'))
              // 回显可抽取题库最大值
              this.currentConfigurationItem.questionCount = allQuestionTypeCount
            } else {
              callback()
            }
          }
        }
      }
    }

    // 校验题库数量，给予提示弹窗
    questionComparison(item: ExamLibrary) {
      if (item.count > item.questionTypeCount) {
        this.$message.warning('本题库题数不满足抽取的题数')
        item.count = item.questionTypeCount
      }
    }

    /**
     *  获取答题弹窗是否显示
     */
    get randomConfigurationShow() {
      this.getNumberQuestionBanks(this.drawingQuestionType)

      return this.show
    }

    set randomConfigurationShow(val: boolean) {
      this.$emit('update:show', val)
    }
    /**
     *  获取答题配置项
     */
    @Watch('currentItem', { immediate: true, deep: true })
    setItem(obj: QuestionExtract) {
      this.currentConfigurationItem = cloneDeep(obj)
      this.drawingQuestionType = this.currentConfigurationItem.questionType
      this.drawingQuestionRule = this.currentConfigurationItem.questionScopesTypes
    }

    /**
     * 保存大题配置项
     */
    commitRandomConfigurationItem() {
      console.log(this.$refs)
      this.currentConfigurationItem.questionScopesTypes = this.drawingQuestionRule
      this.currentConfigurationItem.questionType = this.drawingQuestionType
      if (this.questionBankList) {
        this.currentConfigurationItem.libraryMapQuestionNumSettings = cloneDeep(this.questionBankList)
        const p = this.currentConfigurationItem.libraryMapQuestionNumSettings
        p.forEach(item => {
          item.count = Number(item.count)
        })
      }
      // 增加答题还是修改
      this.elForm.validate(val => {
        if (val) {
          if (this.randomConfigurationOperationType === 1) {
            this.$emit('questionConfigItem', this.currentConfigurationItem)
          } else {
            this.$emit('questionConfigItem', this.currentConfigurationItem)
          }
          this.randomConfigurationShow = false
        }
      })
    }

    /**
     * 取消大题配置项配置
     */
    cancelRandomConfigurationItem() {
      this.randomConfigurationShow = false
    }

    whenClose() {
      this.currentConfigurationItem = new QuestionExtract()
      this.elForm.clearValidate()
    }
    async getNumberQuestionBanks(questionType: number) {
      const idList: Array<string> = []

      this.questionBankList.forEach(p => {
        idList.push(p.id)
      })
      const res = await ResourceModule.queryQuestionLibraryFactory.queryQuestionLibraryMultiton.queryCountQuestionByType(
        idList,
        questionType,
        true
      )
      this.libraryQuestionCountResponseVo = res.data
      this.libraryQuestionCountResponseVo?.forEach((p, i) => {
        this.questionBankList[i].questionTypeCount = p.questionCount
      })
    }
  }
</script>
