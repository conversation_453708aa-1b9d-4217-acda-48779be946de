<template>
  <div>
    <el-card shadow="never" class="m-card f-mb15">
      <div class="m-tit is-border-bottom f-justify-between">
        <span class="tit-txt">基础信息</span>
      </div>
      <el-row type="flex" justify="center" class="width-limit">
        <el-col :md="20" :lg="16" :xl="13">
          <el-form ref="formRef" :rules="rules" :model="portalInfo" label-width="150px" class="m-form f-mt20 f-mb40">
            <el-form-item label="专题门户logo类型：" prop="logoType">
              <el-radio-group v-model="portalInfo.logoType" @input="handleLogoTypeChange('logo')">
                <el-radio :label="logoType.text" border class="f-mr10">文字</el-radio>
                <el-radio :label="logoType.image" border>图片</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="专题门户logo：" v-if="portalInfo.logoType == logoType.text" prop="logo">
              <el-input v-model="portalInfo.logo" clearable placeholder="请输入当前专题门户显示的名称" />
            </el-form-item>
            <el-form-item label="专题门户logo：" v-if="portalInfo.logoType == logoType.image" prop="logo">
              <cropper-img-upload
                :key="1"
                :dialogStyleOpation="{
                  width: '950px',
                  height: '180px'
                }"
                :ratioArr="[`${imgSize('logoSize', 'width')}:${imgSize('logoSize', 'height')}`]"
                :initWidth="imgSize('logoSize', 'width')"
                title="门户logo"
                button-text="上传平台名称及logo"
                :is-long-pic="true"
                reminder-text="只支持JPG、PNG、GIF"
                v-model="portalInfo.logo"
                :has-preview="false"
                :full="true"
                @input="resetVerifyItem('logo')"
                :mode="`${imgSize('logoSize', 'width')}px ${imgSize('logoSize', 'height')}px`"
              >
                <template slot="tip">
                  <div slot="tip" class="el-upload__tip">
                    <i class="el-icon-warning"></i>
                    <span class="txt">
                      上传的平台名称及LOGO图片，适用于门户顶部。请先设计好后上传，建议采用透明背景。<br />
                      尺寸：宽度{{ imgSize('logoSize', 'width') }}px ， 高度{{ imgSize('logoSize', 'height') }}px。
                      <i class="f-link" @click="logoDialog = true">示例图片</i>
                    </span>
                    <!--示例图片弹窗-->
                    <el-dialog :visible.sync="logoDialog" width="1100px" class="m-dialog-pic">
                      <img src="@design/admin/assets/images/demo-web-banner.jpg" alt="" />
                    </el-dialog>
                  </div>
                </template>
              </cropper-img-upload>
            </el-form-item>
            <el-form-item label="客服电话图片：">
              <cropper-img-upload
                :dialogStyleOpation="{
                  width: '410px',
                  height: '160px'
                }"
                :ratioArr="[`${imgSize('customerPhoneSize', 'width')}:${imgSize('customerPhoneSize', 'height')}`]"
                :initWidth="imgSize('customerPhoneSize', 'width')"
                title="客服电话"
                button-text="上传客服电话图片"
                :is-long-pic="true"
                reminder-text="只支持JPG、PNG、GIF"
                v-model="portalInfo.phoneImgUrl"
                :has-preview="false"
                :full="true"
                :mode="`${imgSize('customerPhoneSize', 'width')}px ${imgSize('customerPhoneSize', 'height')}px`"
              >
                <template slot="tip">
                  <div slot="tip" class="el-upload__tip">
                    <i class="el-icon-warning"></i>
                    <span class="txt">
                      上传客服电话图片，请先设计好后再上传，尺寸：宽度{{ imgSize('customerPhoneSize', 'width') }}px
                      ，高度 {{ imgSize('customerPhoneSize', 'height') }}px。
                      <i class="f-link" @click="customerPhoneDialog = true">示例图片</i>
                    </span>
                    <!--示例图片弹窗-->
                    <el-dialog :visible.sync="customerPhoneDialog" width="1100px" class="m-dialog-pic">
                      <img src="@design/admin/assets/images/demo-special-tel.png" alt="" />
                    </el-dialog>
                  </div>
                </template>
              </cropper-img-upload>
            </el-form-item>
            <el-form-item label="客服电话类型：">
              <el-radio-group v-model="portalInfo.phoneType" @input="handleLogoTypeChange('phone')">
                <el-radio :label="phoneFooterType.sameasschool" border class="f-mr10">同本网校</el-radio>
                <el-radio :label="phoneFooterType.custom" border>自定义</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="客服电话：" v-if="portalInfo.phoneType == phoneFooterType.custom" prop="phone">
              <el-input
                v-model="portalInfo.phone"
                clearable
                placeholder="请输入当前专题门户右侧息停区域显示的客服电话信息"
              />
              <div class="el-upload__tip">
                <i class="el-icon-warning"></i>
                <span class="txt">
                  输入专题门户右侧悬停区域显示的客服电话，如需展示多个客服电话，请用顿号“、”分隔。例如96882301、96882302
                </span>
              </div>
            </el-form-item>
            <el-form-item label="咨询时间：" v-if="portalInfo.phoneType == phoneFooterType.custom">
              <el-input
                v-model="portalInfo.informationTime"
                clearable
                placeholder="请输入当前专题门户右侧息停区域显示的客服咨询时间信息"
              />
            </el-form-item>
            <el-form-item label="培训流程：" prop="process">
              <cropper-img-upload
                :key="2"
                :dialogStyleOpation="{
                  width: '1200px',
                  height: '240px'
                }"
                :ratioArr="[`${imgSize('trainingProcessSize', 'width')}:${imgSize('trainingProcessSize', 'height')}`]"
                :initWidth="imgSize('trainingProcessSize', 'width')"
                title="图片设置"
                button-text="上传培训流程图片"
                :is-long-pic="true"
                reminder-text="只支持JPG、PNG、GIF"
                v-model="portalInfo.process"
                :has-preview="false"
                :full="true"
                @input="resetVerifyItem('process')"
                :mode="`${imgSize('trainingProcessSize', 'width')}px ${imgSize('trainingProcessSize', 'height')}px`"
              >
                <template slot="tip">
                  <div slot="tip" class="el-upload__tip">
                    <i class="el-icon-warning"></i>
                    <span class="txt">
                      上传培训流程图片，尺寸：宽度{{ imgSize('trainingProcessSize', 'width') }}px ， 高度{{
                        imgSize('trainingProcessSize', 'height')
                      }}px。
                      <i class="f-link" @click="trainingStepsDialog = true">示例图片</i>
                    </span>
                    <el-dialog :visible.sync="trainingStepsDialog" width="1100px" class="m-dialog-pic">
                      <img src="@design/admin/assets/images/demo-special-process-default.jpg" alt="" />
                    </el-dialog>
                  </div>
                </template>
              </cropper-img-upload>
            </el-form-item>
            <el-form-item label="企业微信客服类型：">
              <el-radio-group v-model="portalInfo.customerServiceType" @input="resetVerifyItem('customerService')">
                <el-radio :label="phoneFooterType.sameasschool" border class="f-mr10">同本网校</el-radio>
                <el-radio :label="phoneFooterType.custom" border>自定义</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="企业微信客服："
              v-if="portalInfo.customerServiceType == phoneFooterType.custom"
              prop="customerService"
            >
              <cropper-img-upload
                :key="3"
                :dialogStyleOpation="{
                  width: '300px',
                  height: '300px'
                }"
                :ratioArr="[
                  `${imgSize('wechatCustomerServiceSize', 'width')}:${imgSize('wechatCustomerServiceSize', 'height')}`
                ]"
                :initWidth="imgSize('wechatCustomerServiceSize', 'width')"
                title="图片设置"
                button-text="上传企业微信客服图片"
                reminder-text="只支持JPG、PNG、GIF"
                v-model="portalInfo.customerService"
                :has-preview="false"
                :full="true"
                @input="resetVerifyItem('customerService')"
              >
                <template slot="tip">
                  <div slot="tip" class="el-upload__tip">
                    <i class="el-icon-warning"></i>
                    <span class="txt"
                      >上传企业微信客服图片，尺寸：宽度{{ imgSize('wechatCustomerServiceSize', 'width') }}px ， 高度{{
                        imgSize('wechatCustomerServiceSize', 'height')
                      }}px。</span
                    >
                  </div>
                </template>
              </cropper-img-upload>
            </el-form-item>

            <el-form-item label="底部落款类型：">
              <el-radio-group v-model="portalInfo.footerType">
                <el-radio :label="phoneFooterType.sameasschool" border class="f-mr10">同本网校</el-radio>
                <el-radio :label="phoneFooterType.custom" border>自定义</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="底部落款：" v-if="portalInfo.footerType == phoneFooterType.custom">
              <div class="rich-text">
                <hb-tinymce-editor v-model="portalInfo.footer"></hb-tinymce-editor>
              </div>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div class="m-tit is-border-bottom">
        <span class="tit-txt">轮播图配置</span>
      </div>
      <div class="f-plr20 f-mt20">
        <el-tabs v-model="activeName" type="card" class="m-tab-card is-badge">
          <el-tab-pane label="" name="first">
            <div slot="label">web端<el-badge :value="isSetting(1)" class="u-badge"></el-badge></div>
            <!-- //数据待传进去 -->
            <banner-table
              ref="webBannerTableRef"
              v-model="portalInfo.webCarouselSettings"
              :isWeb="true"
              :webSizeWidth="imgSize('bannerSize', 'width')"
              :WebSizeHight="imgSize('bannerSize', 'height')"
            ></banner-table>
          </el-tab-pane>
          <el-tab-pane label="" name="second">
            <div slot="label">H5端<el-badge :value="isSetting(2)" class="u-badge"></el-badge></div>
            <!-- //数据待传进去 -->
            <banner-table
              ref="h5BannerTableRef"
              v-model="portalInfo.H5CarouselSettings"
              :isWeb="false"
              :H5SizeWidth="imgSizeH5('bannerSize', 'width')"
              :H5SizeHight="imgSizeH5('bannerSize', 'height')"
            ></banner-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="m-btn-bar f-tc is-sticky f-pt15" style="z-index: 8">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="savePortalInfo" :loading="btLoading">保存</el-button>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts">
  import TemplateModule from '@api/service/common/template-school/TopicTemplateModule'
  import { LogoType } from '@api/service/management/thematic-management/enum/LogoType'
  import { PhoneFooterType } from '@api/service/management/thematic-management/enum/PhoneFooterType'
  import PortalInfo from '@api/service/management/thematic-management/model/PortalInfo'
  import CropperImgUpload from '@hbfe/jxjy-admin-platform/src/function/components/cropper-img-upload.vue'
  import BannerTable from '@hbfe/jxjy-admin-specialTopics/src/add/components/banner-table.vue'
  import { ElForm } from 'element-ui/types/form'
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  @Component({
    components: { BannerTable, CropperImgUpload }
  })
  export default class extends Vue {
    @Prop({
      type: Object,
      default: () => {
        return new PortalInfo()
      }
    })
    portalInfo: PortalInfo
    /**
     * 模板webId
     */
    @Prop({
      type: String,
      default: ''
    })
    templateWeb: string
    btLoading = false
    logoType = LogoType
    phoneFooterType = PhoneFooterType
    logoDialog = false
    activeName = 'first'
    webBannerLength = new Array<any>()
    h5BannerLength = new Array<any>()
    /**
     * 示例图片展示
     */
    customerPhoneDialog = false
    phoneValid = (rule: any, value: any, callback: (error?: Error) => void) => {
      if (!value) {
        callback(new Error('请输入客服电话'))
      } else if (value.length > 50) {
        callback(new Error('客服电话长度需小于50个字符'))
      } else {
        callback()
      }
    }
    rules = {
      logoType: [{ required: true, message: '请选择门户logo类型', trigger: 'blur' }],
      logo: [{ required: true, message: '请输入门户logo', trigger: 'blur' }]
    }

    templateModule = TemplateModule

    /**
     * @description 培训流程示例图片
     * */
    trainingStepsDialog = false

    @Ref('webBannerTableRef') webBannerTableRef: BannerTable
    @Ref('h5BannerTableRef') h5BannerTableRef: BannerTable
    @Ref('formRef') formRef: ElForm

    @Watch('portalInfo', {
      deep: true
    })
    portalInfoSet(val: PortalInfo) {
      if (val.logoType) {
        // debugger
      }
      if (!val.customerServiceType) {
        this.portalInfo.customerServiceType = PhoneFooterType.sameasschool
      }
      if (!val.processType) {
        this.portalInfo.processType = PhoneFooterType.sameasschool
      }
    }

    /**
     * @description 置换模板尺寸
     * @param key 尺寸key(参照 templateModule)
     * @param side 尺寸方向 width 宽 height 高
     * @return number
     * */
    get imgSize() {
      return (key: string, side: string) => {
        // todo
        const templateId = this.templateWeb
        const sizeObj = this.templateModule.getTemplate(templateId)
        const sideIndex = side == 'width' ? 0 : 1
        return sizeObj[key][sideIndex]
      }
    }
    /**
     * @description 置换模板尺寸
     * @param key 尺寸key(参照 templateModule)
     * @param side 尺寸方向 width 宽 height 高
     * @return number
     * */
    get imgSizeH5() {
      return (key: string, side: string) => {
        // todo
        const templateId = this.portalInfo.h5TemplateId
        const sizeObj = TemplateModule.getTemplate(templateId)
        const sideIndex = side == 'width' ? 0 : 1
        return sizeObj[key][sideIndex]
      }
    }

    //保存并进入下一步
    async savePortalInfo() {
      this.btLoading = true
      if (this.portalInfo.phoneType == this.phoneFooterType.custom && !this.portalInfo.phone) {
        this.btLoading = false
        return this.$message.error('请填写客服电话')
      }
      if (this.portalInfo.customerServiceType == this.phoneFooterType.custom && !this.portalInfo.customerService) {
        this.btLoading = false
        return this.$message.error('请上传企业微信客服图片')
      }
      this.formRef.validate((value: boolean) => {
        if (value) {
          if (this.portalInfo.webCarouselSettings.length > 0 && this.portalInfo.H5CarouselSettings.length > 0) {
            this.$emit('savePortalInfo')
          } else {
            this.$message.error('至少添加一张轮播图')
            this.btLoading = false
          }
        } else {
          this.btLoading = false
          this.$message.error('请完善信息')
        }
      })
    }
    //取消编辑
    cancel() {
      const that = this as any
      this.$confirm('取消后,当前调整的内容不保存,确定取消?', '提示', {
        confirmButtonText: '确认',
        showCancelButton: true
      }).then(() => {
        //TODO 清除已修改的数据
        that.$message.success('已取消本次编辑')
        this.$router.push({
          path: '/training/special-topics/manage'
        })
      })
    }
    //清除数据
    clear() {
      this.portalInfo = new PortalInfo()
      this.portalInfo.webCarouselSettings = []
    }
    get isSetting() {
      let text = '未配置'
      return (val: number) => {
        if (val == 1) {
          text = this.portalInfo.webCarouselSettings.length >= 1 ? '' : '未配置'
        } else if (val == 2) {
          text = this.portalInfo.H5CarouselSettings.length >= 1 ? '' : '未配置'
        }
        return text
      }
    }

    /**
     * @description logo类型切换清除
     * @mark 用图片和文字用同一个字段，不清除文字切换图片文字会变成图片url渲染
     * */
    handleLogoTypeChange(ruleProp: string) {
      if (ruleProp == 'logo') {
        this.portalInfo.logo = ''
      }
      if (ruleProp == 'phone' && this.portalInfo.phoneType == PhoneFooterType.custom) {
        return
      }
      this.resetVerifyItem(ruleProp)
    }

    /**
     * @description 根据传入prop清除对应项校验
     * */
    resetVerifyItem(ruleProp: string) {
      this.formRef.clearValidate(ruleProp)
    }
  }
</script>
