enum State {
  bind = 1,
  unbind
}

class ConnectState {
  static isStateIllegal(state: State) {
    return [1, 2].includes(Number(state))
  }

  state: State = State.unbind

  constructor(state: State = State.unbind) {
    this.setState(state)
  }

  setState(state: State = State.unbind) {
    this.state = state
  }

  isBind() {
    return this.state === State.bind
  }

  isUnbind() {
    return this.state === State.unbind
  }
}

export default ConnectState
