import {
  default as MsBasicdataQueryFrontGatewayBasicDataQueryForestage,
  default as msBasicdataQueryFrontGatewayBasicDataQueryForestage,
  PortalInfoResponse1
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import ServicerSeriesV1Gateway, {
  ExcellentCourseItemRequest,
  FieldConstraintResponse
} from '@api/ms-gateway/ms-servicer-series-v1'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
import Context from '@api/service/common/context/Context'
import BannerVo from '@api/service/common/online-school-config/vo/BannerVo'
import LinkVo from '@api/service/common/online-school-config/vo/LinkVo'
import MenuVo from '@api/service/common/online-school-config/vo/MenuVo'
import QueryCourseModule from '@api/service/customer/course/query/QueryCourseModule'
import { MenuTypeEnum } from '@api/service/customer/online-school-config/online-school-partal-config/query/enum/MenuTypeEnum'
import CollectiveRegisterSettingVo from '@api/service/customer/online-school-config/online-school-partal-config/query/vo/CollectiveRegisterSettingVo'
import CourseVo from '@api/service/customer/online-school-config/online-school-partal-config/query/vo/CourseVo'
import FieldConstraintVo from '@api/service/customer/online-school-config/online-school-partal-config/query/vo/FieldConstraintVo'
import OfflineCollectiveRegisterSettingVo from '@api/service/customer/online-school-config/online-school-partal-config/query/vo/OfflineCollectiveRegisterSettingVo'
import OnlineCollectiveRegisterSettingVo from '@api/service/customer/online-school-config/online-school-partal-config/query/vo/OnlineCollectiveRegisterSettingVo'
import UnitConfigVo from '@api/service/customer/online-school-config/online-school-partal-config/query/vo/UnitConfigVo'
import WebPortalVo from '@api/service/customer/online-school-config/online-school-partal-config/query/vo/WebPortalVo'
import { ResponseStatus } from '@hbfe/common'
import InfoCategoryVo from './vo/InfoCategoryVo'
import RegisterSettingVo from './vo/RegisterSettingVo'

/**
 * @description 门户首页配置（web）
 */
class QueryWebPortalConfig {
  /**
   * 是否到期
   */
  isExpire = true
  /**
   * 是否允许访问
   */
  isAccess = true
  /**
   * 首页配置
   */
  webPortal: WebPortalVo = new WebPortalVo()
  /**
   * 注册配置
   */
  registerSetting: RegisterSettingVo = new RegisterSettingVo()
  /**
   * 集体报名配置
   */
  collectiveRegisterSetting: CollectiveRegisterSettingVo = new CollectiveRegisterSettingVo()
  /**
   * 资讯类别列表
   */
  infoCategoryList: Array<InfoCategoryVo> = new Array<InfoCategoryVo>()

  /**
   * 查询H5二维码
   */
  async queryMobileQRCode() {
    const serverId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
    const res = await MsBasicdataQueryFrontGatewayBasicDataQueryForestage.getTrainingInstitutionPortalInfo({
      portalType: 1,
      servicerId: serverId
    })
    if (res.data.mobileQRCodeSign === 1) {
      res.data.mobileQRCode = ''
    }
    return res.data.mobileQRCode || ''
  }
  /**
   * 查询跳转h5的url
   */
  async queryToH5Url() {
    const serverId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
    const webRes = (
      await MsBasicdataQueryFrontGatewayBasicDataQueryForestage.getTrainingInstitutionPortalInfo({
        portalType: 1,
        servicerId: serverId
      })
    ).data
    if (!webRes?.domainNameH5) {
      return ''
    }
    const h5Res = (
      await MsBasicdataQueryFrontGatewayBasicDataQueryForestage.getTrainingInstitutionPortalInfo({
        portalType: 2,
        servicerId: serverId
      })
    ).data
    if (webRes.mobileQRCodeSign === 1) {
      webRes.mobileQRCode = ''
    }
    return `/weixin/index.html?link=${encodeURIComponent(window.location.href)}&title=${encodeURIComponent(
      webRes.title
    )}&icon=${encodeURIComponent(webRes.icon)}&qrcode=${encodeURIComponent(
      webRes.mobileQRCode
    )}&isAccess=${encodeURIComponent(h5Res.isPublished)}&H5Url=${encodeURIComponent(webRes.domainNameH5)}`
  }

  /**
   * 查询web端的门户配置
   * @return webPortal
   */
  async queryWebPortalConfig() {
    // todo 获取网校门户信息、获取友情链接列表、获取门户栏目列表、获取轮播图列表、获取精品课程列表
    const response = new ResponseStatus(500, '')
    const serverId = Context.businessEnvironment?.serviceToken?.tokenMeta?.servicerId
    const webInfoRes = await MsBasicdataQueryFrontGatewayBasicDataQueryForestage.getTrainingInstitutionPortalInfo({
      portalType: 1,
      servicerId: serverId
    })
    if (webInfoRes.status.isSuccess()) {
      this.isAccess = webInfoRes.data.isPublished
      this.isExpire = webInfoRes.data.onlineSchoolStatus === 1 ? false : true
      this.webPortal.id = webInfoRes.data.id
      this.webPortal.name = webInfoRes.data.title
      this.webPortal.logo = webInfoRes.data.logo
      this.webPortal.icon = webInfoRes.data.icon
      this.webPortal.CSPhonePicture = webInfoRes.data.CSPhonePicture
      this.webPortal.CSPhone = webInfoRes.data.CSPhone
      this.webPortal.CSCallTime = webInfoRes.data.CSCallTime
      this.webPortal.CSOnlineCodeId = webInfoRes.data.CSOnlineCodeId
      this.webPortal.mobileQRCode = webInfoRes.data.mobileQRCode
      this.webPortal.trainingFlowPicture = webInfoRes.data.trainingFlowPicture
      this.webPortal.footContent = webInfoRes.data.footContent
      this.webPortal.themeColor = webInfoRes.data.themeColor
      this.webPortal.friendLinkType = webInfoRes.data.friendLinkType
      this.webPortal.cnzz = webInfoRes.data.cnzz
      this.webPortal.dirName = webInfoRes.data.dirName
      this.webPortal.domainName = webInfoRes.data.domainName
      this.webPortal.domainNameH5 = webInfoRes.data.domainNameH5
      this.webPortal.webTemplateId = webInfoRes.data.portalTemplateId
      this.webPortal.enterPriseWxCustomer = webInfoRes.data.CSWechat
      this.webPortal.mobileQRCodeSign = webInfoRes.data.mobileQRCodeSign
      response.code = 200
    }
    if (!this.isExpire && this.isAccess) {
      const friendLinkRes = await msBasicdataQueryFrontGatewayBasicDataQueryForestage.getFriendLinkListByPortalType(1)
      if (friendLinkRes.status.isSuccess()) {
        this.webPortal.friendLinks = new Array<LinkVo>()
        friendLinkRes.data.friendLinkInfos?.forEach((item) => this.webPortal.friendLinks.push(LinkVo.from(item)))
      }
      const bannerRes = await msBasicdataQueryFrontGatewayBasicDataQueryForestage.getBannerListByPortalType(1)
      if (bannerRes.status.isSuccess()) {
        this.webPortal.banners = new Array<BannerVo>()
        const sortData = bannerRes.data.bannerInfos?.sort((a, b) => {
          return a.sort - b.sort
        })
        sortData.map((item) => this.webPortal.banners.push(BannerVo.from(item)))
      }

      if (friendLinkRes.status.isSuccess() && bannerRes.status.isSuccess()) {
        response.code = 200
      }
    }
    return Promise.resolve(response)
  }

  /**
   * 转换web端配置模型
   * @description 特殊转换，仅适用于合并请求getTrainingInstitutionPortalInfo接口同时获取web端和h5端的配置时使用
   * @param response 接口返回值
   */
  convertRespFn(response: PortalInfoResponse1) {
    this.isAccess = response.isPublished
    this.isExpire = response.onlineSchoolStatus === 1 ? false : true
    this.webPortal.id = response.id
    this.webPortal.name = response.title
    this.webPortal.logo = response.logo
    this.webPortal.icon = response.icon
    this.webPortal.CSPhonePicture = response.CSPhonePicture
    this.webPortal.CSPhone = response.CSPhone
    this.webPortal.CSCallTime = response.CSCallTime
    this.webPortal.CSOnlineCodeId = response.CSOnlineCodeId
    this.webPortal.mobileQRCode = response.mobileQRCode
    this.webPortal.trainingFlowPicture = response.trainingFlowPicture
    this.webPortal.footContent = response.footContent
    this.webPortal.themeColor = response.themeColor
    this.webPortal.friendLinkType = response.friendLinkType
    this.webPortal.cnzz = response.cnzz
    this.webPortal.dirName = response.dirName
    this.webPortal.domainNameH5 = response.domainNameH5
    this.webPortal.webTemplateId = response.portalTemplateId
    this.webPortal.enterPriseWxCustomer = response.CSWechat
  }

  /**
   * 获取 web 端配置模型 仅在 h5 端使用
   * 只包含配置模型，不会查询精品课程
   */
  async queryWebPortalConfigInH5() {
    const response = new ResponseStatus(500, '')
    const serverId = Context.businessEnvironment?.serviceToken?.tokenMeta?.servicerId
    const webInfoRes = await MsBasicdataQueryFrontGatewayBasicDataQueryForestage.getTrainingInstitutionPortalInfo({
      portalType: 1,
      servicerId: serverId
    })
    if (webInfoRes.status.isSuccess() && webInfoRes.data) {
      this.convertRespFn(webInfoRes.data)
      response.code = 200
    }
    return Promise.resolve(response)
  }

  /**
   * 查询web端的注册配置
   * @return registerSetting
   */
  async queryRegisterSetting() {
    const res = await ServicerSeriesV1Gateway.getStudentResisterFormConstraintForConfig()

    if (res.status.isSuccess()) {
      this.registerSetting.studentRegisterEnable = res.data.enabled
      // 学员注册字段校验
      await this.queryStudentRegisterSetting(res.data.fieldConstraints)
      // 行业注册字段校验
      BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryV2.industryList?.forEach((industry) => {
        this.queryIndustryRegisterSetting(res.data.fieldConstraints, industry)
      })
      // 查询单位配置
      const unitConfigRes = await ServicerSeriesV1Gateway.getDockingTycAndQcc()
      if (unitConfigRes.status?.isSuccess()) {
        this.registerSetting.unitConfig = UnitConfigVo.from(unitConfigRes.data)
      }
    }
  }

  /**
   * 查询注册配置的加密token 在提交注册之前获取
   * @return registerSetting
   */
  async queryRegisterToken() {
    const res = await ServicerSeriesV1Gateway.getStudentRegisterFormConstraint()
    if (res.status.isSuccess()) {
      this.registerSetting.token = res.data.token
    }
  }

  /**
   * 查询web端的集体报名配置（线上、线下、集体报名账号注册的开发）
   * @return collectiveRegisterSetting
   */
  async queryCollectiveRegisterSetting() {
    await this.queryOfflineCollectiveRegisterSetting()
    await this.queryOnlineCollectiveRegisterSetting()
    // 设置集体报名注册的开放
    this.registerSetting.collectiveRegisterEnable =
      this.collectiveRegisterSetting.onlineCollectiveRegisterSetting.enable ||
      this.collectiveRegisterSetting.offlineCollectiveRegisterSetting.enable
  }

  /**
   * 查询web端的学员账号注册配置
   * @return ResponseStatus
   */
  private queryStudentRegisterSetting(fieldConstraintList: Array<FieldConstraintResponse>) {
    this.registerSetting.studentRegister = fieldConstraintList
      ?.filter((field) => !field.relate)
      ?.map(FieldConstraintVo.from)
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 查询web端的人设、建设行业注册配置
   * @return ResponseStatus
   */
  private queryIndustryRegisterSetting(fieldConstraintList: Array<FieldConstraintResponse>, industry: IndustryVo) {
    if (industry.name === '人社行业' || industry.name === '专技行业') {
      fieldConstraintList?.forEach((feild) => {
        if (feild.relate && feild.validators.length) {
          const filedIndex = feild.validators?.findIndex((validator) =>
            validator.values.find((value) => value == industry.id)
          )
          if (filedIndex > -1) {
            this.registerSetting.personIndustryRegister.push(FieldConstraintVo.from(feild))
          }
        }
      })
    } else {
      fieldConstraintList?.forEach((feild) => {
        if (feild.relate && feild.validators.length) {
          const filedIndex = feild.validators?.findIndex((validator) =>
            validator.values.find((value) => value == industry.id)
          )
          if (filedIndex > -1) {
            this.registerSetting.constructionIndustryRegister.push(FieldConstraintVo.from(feild))
          }
        }
      })
    }
    // return studentRegister
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 查询web端的线上集体报名配置
   * @return ResponseStatus
   */
  private async queryOnlineCollectiveRegisterSetting() {
    const res = await ServicerSeriesV1Gateway.getOnlineCollectiveRegisterConfig()
    if (res.status.isSuccess()) {
      this.collectiveRegisterSetting.onlineCollectiveRegisterSetting = OnlineCollectiveRegisterSettingVo.from(res.data)
    }
    return res.status
  }

  /**
   * 查询web端的线上集体报名配置
   * @return ResponseStatus
   */
  async queryOnlineCollectiveRegisterSettingV2() {
    const res = await ServicerSeriesV1Gateway.getOnlineCollectiveRegisterConfig()
    if (res.status.isSuccess()) {
      this.collectiveRegisterSetting.onlineCollectiveRegisterSetting = OnlineCollectiveRegisterSettingVo.from(res.data)
    }
    return res
  }

  /**
   * 查询web端的线下集体报名配置
   * @return ResponseStatus
   */
  private async queryOfflineCollectiveRegisterSetting() {
    const res = await ServicerSeriesV1Gateway.getOfflineCollectiveRegisterConfig()
    if (res.status.isSuccess()) {
      this.collectiveRegisterSetting.offlineCollectiveRegisterSetting = OfflineCollectiveRegisterSettingVo.from(
        res.data
      )
    }
    return res.status
  }

  /**
   * 获取资讯分类列表
   * @param menus 栏目列表
   * @return ResponseStatus
   */
  private getInfoCategoryList(menus: Array<MenuVo>) {
    const infoCategoryList = menus?.filter((menu) => {
      return menu.type === MenuTypeEnum.NEWS
    })
    this.infoCategoryList = infoCategoryList?.map(InfoCategoryVo.from)
  }

  /**
   * 从课程状态层中获取精品课程的详情
   */
  private async getCourseInfo(courseList: Array<ExcellentCourseItemRequest>) {
    // 记得替换成
    const courseIdList = courseList?.map((course) => course.courseId)
    const result = await QueryCourseModule.queryCourse.queryCourseListByIdList(courseIdList)
    return result?.map(CourseVo.from)
  }
}

// 采用单例模式 调用完方法将数据填充本地进行维护
export default new QueryWebPortalConfig()
