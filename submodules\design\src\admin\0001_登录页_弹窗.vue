<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">绑定手机号</el-button>
        <el-dialog
          title="绑定手机号"
          :visible.sync="dialog1"
          width="600px"
          class="m-dialog"
          :show-close="false"
          :before-close="handleClose"
        >
          <el-row type="flex" justify="center">
            <el-col :span="18">
              <el-form ref="form" :model="form" label-width="auto" class="m-form">
                <el-form-item label="手机号码：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入11位手机号" />
                </el-form-item>
                <el-form-item label="图形验证码：" required>
                  <div class="f-flex">
                    <el-input v-model="form.name" clearable placeholder="请输入图形验证码" class="f-flex-sub" />
                    <div class="code">
                      <img src="./assets/images/code.jpg" title="看不清，点击刷新" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="短信校验码：" required>
                  <div class="f-flex">
                    <el-input v-model="form.name" clearable placeholder="请输入短信校验码" class="f-flex-sub" />
                    <div class="code">
                      <el-button type="primary" plain>获取短信验证码</el-button>
                      <!--<el-button type="info" plain disabled>重新获取（60s）</el-button>-->
                    </div>
                  </div>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <div slot="footer">
            <el-button type="primary">确认提交</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">绑定成功提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog2" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-f16">帐号绑定手机号成功</p>
              <p class="f-f16">可以使用手机号直接登录</p>
            </div>
          </div>
          <div slot="footer">
            <el-button type="primary">确定（5s）</el-button>
          </div>
        </el-dialog>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <el-button @click="dialog3 = true" type="primary" class="f-mr20">密码输入错误提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog3" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">请输入正确的账号或密码，连续输错X次后，账号将被锁定，您还可重试X次！</span>
          </div>
          <div slot="footer">
            <el-button type="primary">我知道了</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog4 = true" type="primary" class="f-mr20">失败次数上限制</el-button>
        <el-dialog title="提示" :visible.sync="dialog4" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">您多次账号或密码输入错误，账号被暂时锁定，请XX分钟后再尝试或找回密码。</span>
          </div>
          <div slot="footer">
            <el-button>找回密码</el-button>
            <el-button type="primary">我知道了</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">账号被锁定后再次点击登录</el-button>
        <el-dialog title="提示" :visible.sync="dialog5" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt"
              >由于您的账号或密码多次输入错误，账号被暂时锁定，建议您在2024-12-02 12:01之后再尝试登录或找回密码。</span
            >
          </div>
          <div slot="footer">
            <el-button>我知道了</el-button>
            <el-button type="primary">找回密码</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog6 = true" type="primary" class="f-mr20">该账号没有绑定手机号码</el-button>
        <el-dialog
          title="提示"
          :visible.sync="dialog6"
          width="450px"
          class="m-dialog"
          :show-close="false"
          :before-close="handleClose"
        >
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">您当前账号尚未绑定手机号码，请进行绑定后再登录。</span>
          </div>
          <div slot="footer">
            <el-button type="primary">立即绑定</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog7 = true" type="primary" class="f-mr20">绑定成功提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog7" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-f16">帐号绑定手机号成功</p>
            </div>
          </div>
          <div slot="footer">
            <el-button type="primary">确定（5s）</el-button>
          </div>
        </el-dialog>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <el-button @click="dialog8 = true" type="primary" class="f-mr20">修改密码1</el-button>
        <el-dialog
          title="修改密码"
          :visible.sync="dialog8"
          width="450px"
          class="m-dialog"
          :show-close="false"
          :before-close="handleClose"
        >
          <div class="dialog-alert is-big">
            <i class="icon el-icon-warning warning"></i>
            <div class="txt">
              <p class="f-f16">您的密码已超过XX天未更新，请立即修改密码以保障安全。</p>
            </div>
          </div>
          <div slot="footer">
            <el-button type="primary">修改密码</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog9 = true" type="primary" class="f-mr20">修改密码2</el-button>
        <el-dialog
          title="修改密码"
          :visible.sync="dialog9"
          width="450px"
          class="m-dialog"
          :show-close="false"
          :before-close="handleClose"
        >
          <el-row type="flex" justify="center">
            <el-col :span="18">
              <el-form ref="form" :model="form" label-width="auto" class="m-form">
                <el-form-item label="新密码：" required>
                  <el-input clearable placeholder="请填写新密码" />
                </el-form-item>
                <el-form-item label="确认密码：" required>
                  <el-input clearable placeholder="请填写确认密码" />
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <div slot="footer">
            <el-button type="primary">提交新密码</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog10 = true" type="primary" class="f-mr20">修改密码</el-button>
        <el-dialog title="修改密码" :visible.sync="dialog10" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-f16">密码修改成功！</p>
            </div>
          </div>
          <div slot="footer">
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
