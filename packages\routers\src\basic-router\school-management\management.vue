<route-meta>
{
"isMenu": true,
"title": "网校管理",
"sort": 2,
"icon": "icon-daoru"
}
</route-meta>
<script lang="ts">
  import PlatformManage from '@hbfe/jxjy-admin-management/src/index.vue'
  import { ZXMGLY } from '@/models/RoleTypes'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  @RoleTypeDecorator({
    query: [ZXMGLY],
    update: [ZXMGLY]
  })
  export default class extends PlatformManage {}
</script>
