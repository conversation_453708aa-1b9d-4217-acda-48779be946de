<route-meta>
{
"isMenu": true,
"title": "专题管理员管理",
"sort": 7,
"icon": "icon_guanli"
}
</route-meta>
//
<script lang="ts">
  import AdministratorAccount from '@hbfe/jxjy-admin-account/src/monographic-account/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY],
    create: [WXGLY],
    resetPassword: [WXGLY],
    detail: [WXGLY],
    modify: [WXGLY],
    enable: [WXGLY],
    deactivate: [WXGLY]
  })
  export default class extends AdministratorAccount {}
</script>
