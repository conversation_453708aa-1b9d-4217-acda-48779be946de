<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">统计报表</el-breadcrumb-item>
      <el-breadcrumb-item>学习日志</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15 f-mh100 f-flex f-align-center f-justify-center">
        <div class="f-tc f-f22 f-fb">
          数据更新时间：2021-07-12 18:32:58
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header">
          <span class="tit-txt">用户信息</span>
        </div>
        <div class="f-p20">
          <el-row class="no-gutter">
            <el-form :inline="true" label-width="100px" class="m-text-form f-mt10">
              <el-col :span="8">
                <el-form-item label="姓名：">张三丰</el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="手机号：">13509357645</el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="身份证号：">352201199102107465</el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所在地区：">福建省福州市</el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="工作单位：">福建华博教育科技股份有限公司</el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </div>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">培训班信息</span>
        </div>
        <div class="f-p20">
          <el-row class="no-gutter">
            <el-form :inline="true" label-width="100px" class="m-text-form f-mt10">
              <el-col :span="8">
                <el-form-item label="学时：">10</el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="班级名称：">班级名称班级名称班级名称班级名称班级名称</el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </div>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">登录日志</span>
        </div>
        <div class="f-p20">
          <el-table stripe :data="tableData" max-height="500" highlight-current-row class="m-table">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="登录时间" min-width="240">
              <template>2020-04-16 15:58</template>
            </el-table-column>
            <el-table-column label="登录IP" min-width="240">
              <template>**************</template>
            </el-table-column>
            <el-table-column label="拍摄照片" min-width="150">
              <template>
                <img src="./assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
              </template>
            </el-table-column>
            <el-table-column label="人脸识别结果" min-width="140" align="center">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  <el-badge is-dot type="danger" class="badge-status">不匹配</el-badge>
                </div>
                <div v-else>
                  <el-badge is-dot type="success" class="badge-status">匹配</el-badge>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">学习日志</span>
        </div>
        <div class="f-p20">
          <el-tabs v-model="activeName" type="card" class="no-margin">
            <el-tab-pane label="进入学习日志" name="first">
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500" highlight-current-row class="m-table f-mt15">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="课程名称" min-width="240">
                  <template>课程名称课程名称课程名称课程名称课程名称课程名称</template>
                </el-table-column>
                <el-table-column label="进入学习时间" min-width="200">
                  <template>2017-12-12 17:23:23</template>
                </el-table-column>
                <el-table-column label="拍摄照片" min-width="150">
                  <template>
                    <img src="./assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
                  </template>
                </el-table-column>
                <el-table-column label="匹配结果" min-width="140" align="center">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-badge is-dot type="danger" class="badge-status">不匹配</el-badge>
                    </div>
                    <div v-else>
                      <el-badge is-dot type="success" class="badge-status">匹配</el-badge>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="学习过程日志" name="second">
              <el-badge is-dot type="info" class="badge-status f-mt15 f-mb15"
                ><span class="f-fb">课程名称：《7.0会计用03学分13》</span></el-badge
              >
              <!--表格-->
              <el-table stripe :data="tableData2" max-height="500" highlight-current-row class="m-table">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="媒体名称" min-width="240">
                  <template>媒体名称媒体名称媒体名称媒体名称媒体名称媒体名称</template>
                </el-table-column>
                <el-table-column label="播放时长" min-width="120">
                  <template>10:00</template>
                </el-table-column>
                <el-table-column label="学习进度" min-width="120">
                  <template>80%</template>
                </el-table-column>
                <el-table-column label="拍摄时间" min-width="200">
                  <template>2017-12-12 17:23:23</template>
                </el-table-column>
                <el-table-column label="拍摄照片" min-width="150">
                  <template>
                    <img src="./assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
                  </template>
                </el-table-column>
                <el-table-column label="匹配结果" min-width="140" align="center">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-badge is-dot type="danger" class="badge-status">不匹配</el-badge>
                    </div>
                    <div v-else>
                      <el-badge is-dot type="success" class="badge-status">匹配</el-badge>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <el-badge is-dot type="info" class="badge-status f-mt15 f-mb15"
                ><span class="f-fb">课程名称：《7.0会计用03学分13》</span></el-badge
              >
              <!--表格-->
              <el-table stripe :data="tableData2" max-height="500" highlight-current-row class="m-table">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="媒体名称" min-width="240">
                  <template>媒体名称媒体名称媒体名称媒体名称媒体名称媒体名称</template>
                </el-table-column>
                <el-table-column label="播放时长" min-width="120">
                  <template>10:00</template>
                </el-table-column>
                <el-table-column label="学习进度" min-width="120">
                  <template>80%</template>
                </el-table-column>
                <el-table-column label="拍摄时间" min-width="200">
                  <template>2017-12-12 17:23:23</template>
                </el-table-column>
                <el-table-column label="拍摄照片" min-width="150">
                  <template>
                    <img src="./assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
                  </template>
                </el-table-column>
                <el-table-column label="匹配结果" min-width="140" align="center">
                  <template>
                    <el-badge is-dot type="success" class="badge-status">匹配</el-badge>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">考试日志</span>
        </div>
        <div class="f-p20">
          <el-tabs v-model="activeName" type="card" class="no-margin">
            <el-tab-pane label="进入考试日志" name="first">
              <div class="f-mt15 f-mb15">
                <el-tag type="danger" class="f-vm f-mr10">第一次</el-tag>
                <el-tag type="info">考试时间：2020-01-05 10:35:40 ~ 2020-01-05 10:45:40</el-tag>
              </div>
              <!--表格-->
              <el-table stripe :data="tableData2" max-height="500" highlight-current-row class="m-table">
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="拍摄照片" min-width="150">
                  <template>
                    <img src="./assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
                  </template>
                </el-table-column>
                <el-table-column label="拍摄时间" min-width="200">
                  <template>2017-12-12 17:23:23</template>
                </el-table-column>
                <el-table-column label="匹配结果" min-width="100" align="center">
                  <template>
                    <el-badge is-dot type="danger" class="badge-status">不匹配</el-badge>
                  </template>
                </el-table-column>
              </el-table>
              <div class="f-mt15 f-mb15">
                <el-tag type="danger" class="f-vm f-mr10">第二次</el-tag>
                <el-tag type="info">考试时间：2020-01-05 10:35:40 ~ 2020-01-05 10:45:40</el-tag>
              </div>
              <!--表格-->
              <el-table stripe :data="tableData2" max-height="500" highlight-current-row class="m-table">
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="拍摄照片" min-width="150">
                  <template>
                    <img src="./assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
                  </template>
                </el-table-column>
                <el-table-column label="拍摄时间" min-width="200">
                  <template>2017-12-12 17:23:23</template>
                </el-table-column>
                <el-table-column label="匹配结果" min-width="100" align="center">
                  <template>
                    <el-badge is-dot type="success" class="badge-status">匹配</el-badge>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="考试过程日志" name="second">
              <div class="f-mt15 f-mb15">
                <el-tag type="danger" class="f-vm f-mr10">第一次</el-tag>
                <el-tag type="info">考试时间：2020-01-05 10:35:40 ~ 2020-01-05 10:45:40</el-tag>
              </div>
              <el-row class="no-gutter">
                <el-form :inline="true" label-width="auto" class="m-text-form">
                  <el-col :span="8">
                    <el-form-item label="考试成绩：">100分</el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="是否合格：">合格</el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="是否替考：">无替考</el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <!--表格-->
              <el-table stripe :data="tableData2" max-height="500" highlight-current-row class="m-table">
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="拍摄照片" min-width="150">
                  <template>
                    <img src="./assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
                  </template>
                </el-table-column>
                <el-table-column label="拍摄时间" min-width="200">
                  <template>2017-12-12 17:23:23</template>
                </el-table-column>
                <el-table-column label="匹配结果" min-width="100">
                  <template>
                    <el-badge is-dot type="danger" class="badge-status">不匹配</el-badge>
                  </template>
                </el-table-column>
                <el-table-column label="考试时间点" min-width="100" align="center">
                  <template>00:01:23</template>
                </el-table-column>
              </el-table>
              <div class="f-mt15 f-mb15">
                <el-tag type="danger" class="f-vm f-mr10">第二次</el-tag>
                <el-tag type="info">考试时间：2020-01-05 10:35:40 ~ 2020-01-05 10:45:40</el-tag>
              </div>
              <el-row class="no-gutter">
                <el-form :inline="true" label-width="auto" class="m-text-form">
                  <el-col :span="8">
                    <el-form-item label="考试成绩：">100分</el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="是否合格：">合格</el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="是否替考：">无替考</el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <!--表格-->
              <el-table stripe :data="tableData2" max-height="500" highlight-current-row class="m-table">
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="拍摄照片" min-width="150">
                  <template>
                    <img src="./assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
                  </template>
                </el-table-column>
                <el-table-column label="拍摄时间" min-width="200">
                  <template>2017-12-12 17:23:23</template>
                </el-table-column>
                <el-table-column label="匹配结果" min-width="100">
                  <template>
                    <el-badge is-dot type="success" class="badge-status">匹配</el-badge>
                  </template>
                </el-table-column>
                <el-table-column label="考试时间点" min-width="100" align="center">
                  <template>00:01:23</template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">考试申诉记录</span>
        </div>
        <div class="f-p20">
          <el-table stripe :data="tableData" max-height="500" highlight-current-row class="m-table">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="申诉来源" min-width="240">
              <template>
                <div class="f-fb">考试名称：选修必修选修必修</div>
                <div>进考时间：2021-07-12 18:32:58</div>
              </template>
            </el-table-column>
            <el-table-column label="所属班级" min-width="240">
              <template>所属班级所属班级所属班级所属班级所属班级所属班级</template>
            </el-table-column>
            <el-table-column label="申诉内容" min-width="150">
              <template>申诉内容申诉内容申诉内容申诉内容申诉内容</template>
            </el-table-column>
            <el-table-column label="申诉时间" min-width="200">
              <template>2017-12-12 17:23:23</template>
            </el-table-column>
            <el-table-column label="申诉状态" min-width="140" align="center">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  <el-badge is-dot type="danger" class="badge-status">审批不通过</el-badge>
                </div>
                <div v-else>
                  <el-badge is-dot type="success" class="badge-status">审批通过</el-badge>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        activeNames: ['1'],
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        cascader1: [
          {
            value: 'zhinan',
            label: '门户信息管理',
            children: [
              {
                value: 'yizhi',
                label: '资讯信息管理',
                children: [
                  {
                    value: 'yizhi1',
                    label: '修改'
                  },
                  {
                    value: 'fanku2i',
                    label: '查询'
                  },
                  {
                    value: 'xiaolv1',
                    label: '设为草稿'
                  },
                  {
                    value: 'keko2ng',
                    label: '发布'
                  },
                  {
                    value: 'keko22ng',
                    label: '新增'
                  },
                  {
                    value: 'keko2ng3',
                    label: '删除'
                  }
                ]
              },
              {
                value: 'fankui',
                label: '资讯分类管理',
                children: [
                  {
                    value: 'yiz1hi1',
                    label: '修改'
                  },
                  {
                    value: 'fanku21i',
                    label: '查询'
                  },
                  {
                    value: 'xiao1lv1',
                    label: '设为草稿'
                  },
                  {
                    value: 'kek1o2ng',
                    label: '发布'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '工种管理',
            children: [
              {
                value: 'cexiangdaohang',
                label: '创建/编辑'
              },
              {
                value: 'dingbudaohang',
                label: '上移'
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '培训证明',
            children: [
              {
                value: 'cexiangdao2hang',
                label: '预览'
              },
              {
                value: 'dingb2udaohang',
                label: '下载'
              },
              {
                value: 'dingb2udaoh3ang',
                label: '查询'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        tableData2: [{ field101: '1' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleChange(val) {
        console.log(val)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
