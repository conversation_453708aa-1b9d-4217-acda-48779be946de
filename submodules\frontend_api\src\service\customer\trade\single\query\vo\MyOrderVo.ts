import tradeQueryGateway, {
  OrderResponse,
  ReturnOrderSortField,
  ReturnSortRequest,
  SortPolicy,
  SubOrderResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { OrderStatusEnum } from '@api/service/common/trade/OrderStatusEnum'
import {
  InvoiceCategoryEnum,
  InvoiceIdentificationEnum,
  InvoiceMethodEnum
} from '@api/service/customer/trade/single/enum/InvoiceEnum'
import { InvoiceStatusEnum } from '@api/service/customer/trade/single/invoice/enum/InvoiceEnum'
import QueryInvoice from '@api/service/customer/trade/single/invoice/query/QueryInvoice'
import InvoiceListResponse from '@api/service/customer/trade/single/invoice/query/vo/InvoiceListResponse'
import OrderInvoiceApplyInfoResponseVo from '@api/service/customer/trade/single/query/query-customer-user-order/query-order-detail/vo/OrderInvoiceApplyInfoResponseVo'
import { ReturnOrderRequestVo } from '@api/service/customer/trade/single/query/vo/ReturnOrderRequestVo'
import ReturnOrderResponseVo from '@api/service/customer/trade/single/query/vo/ReturnOrderResponseVo'
import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
import { SubjectType } from '@api/service/customer/thematic-config/enum/SubjectType'
import CommodityRefundStatus from '@api/service/common/return-order/enums/CommodityRefundStatus'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import OrderRefundType, {
  OnlyReturnList,
  OrderRefundTypeEnum
} from '@api/service/common/return-order/enums/OrderRefundType'
import AccommodationInformation from '@api/service/customer/trade/single/query/vo/AccommodationInformation'

export default class MyOrderVo extends OrderResponse {
  //UI层展示的状态
  orderStatue = OrderStatusEnum.QueryOrderStatusEnumWaitPay
  //  退货单详情
  returnOrderDetail: ReturnOrderResponseVo = null
  //  退货单详情列表
  returnOrderDetailList: ReturnOrderResponseVo[] = null
  //发票模型数组
  invoiceList: OrderInvoiceApplyInfoResponseVo[] = []
  //   专题类型
  specialType: Array<SubjectType> = new Array<SubjectType>()
  // 专题单位
  specialUnit = ''
  // 专题地区
  specialArea = ''

  /**
   * 子订单退货状态（目前只有单一商品，直接上浮即可）
   */
  get subOrderRefundStatus() {
    return (subOrderItem: SubOrderResponse) => {
      return CommodityRefundStatus.transferSubOrderStatusToCurrent(
        subOrderItem.returnStatus,
        subOrderItem.refundSchedule,
        subOrderItem.returnSchedule
      )
    }
  }

  /**
   * 获取子订单住宿信息
   */
  get subOrderAccommodationInformation() {
    return (subOrderItem: SubOrderResponse) => {
      const accommodation = new AccommodationInformation()
      accommodation.isAccommodation = subOrderItem.accommodation?.IsAccommodation
      accommodation.accommodationType = subOrderItem.accommodation?.accommodationType
      accommodation.isOpenAccommodationGather = subOrderItem.accommodation?.accommodationType != null

      return accommodation
    }
  }

  /**
   * 获取子单可退款类型
   */
  get subOrderCanRefundType() {
    // channelType 1:用户自主购买 2:集体缴费 3：管理员导入 4：集体报名个人缴费渠道
    return (subOrderItem: SubOrderResponse, channelType?: number) => {
      const subOrderRefundStatus = this.subOrderRefundStatus(subOrderItem)

      let typeList = new Array<OrderRefundTypeEnum>()

      typeList = OrderRefundType.switchSubOrderCanRefundType(
        subOrderRefundStatus,
        subOrderItem.leftAmount,
        subOrderItem.leftQuantity
      )

      // 导入开通订单只能选择仅退货
      if (channelType == 3 || channelType == 2) {
        typeList = typeList.filter((it) => {
          return OnlyReturnList.includes(it)
        })
      }

      if (typeList?.length) {
        return OrderRefundType.transformToUiOptions(typeList)
      } else {
        return []
      }
    }
  }

  /**
   * 已退款金额
   */
  get refundAmount() {
    let totalOrderAmount = 0
    let totalRemainingAmount = 0
    this.subOrderItems.map((item) => {
      if (item.amount) {
        totalOrderAmount += item.amount
      }
      if (item.leftAmount) {
        totalRemainingAmount += item.leftAmount
      }
    })
    return CalculatorObj.subtract(totalOrderAmount, totalRemainingAmount)
  }
  /*
   *
   * 发票类型
   */
  get invoiceIdentification(): InvoiceIdentificationEnum {
    // 发票类型应由三个字段去定位 （invoiceCategory,invoiceType,invoiceMethod）
    // 但目前只有这几种发票类型，为不影响旧的发票类型判断进行判断简化
    // 若后续新增发票类型则判断需重新梳理
    switch (this.invoiceApplyInfo.invoiceCategory) {
      case InvoiceCategoryEnum.PLAININVOICE:
        return InvoiceIdentificationEnum.PLAIN_INVOICE
      case InvoiceCategoryEnum.VATPLAININVOICE:
        return InvoiceIdentificationEnum.VAT_PLAIN_INVOICE
      case InvoiceCategoryEnum.VATSPECIALPLAININVOICE:
        if (this.invoiceApplyInfo.invoiceType === InvoiceMethodEnum.ELECT) {
          return InvoiceIdentificationEnum.VAT_SPECIAL_ELECT_PLAIN_INVOICE
        } else {
          return InvoiceIdentificationEnum.VAT_SPECIAL_PLAIN_INVOICE
        }
      default:
        return null
    }
  }

  get invoiceEmail() {
    /**
     * 查订单的口 个人报名订单用email 集体报名订单用contactEmail
     * 查发票的口 线上票用email 线下票用contactEmail
     * 如果有发票 发票优先于订单
     */
    if (this.invoiceList?.length) {
      if (this.invoiceList[0]?.invoiceMethod === 2) {
        return this.invoiceList[0].contactEmail
      } else {
        return this.invoiceList[0].email
      }
    } else {
      if (this.basicData.orderType === 2) {
        return this.invoiceApplyInfo.contactEmail
      } else {
        return this.invoiceApplyInfo.email
      }
    }
  }

  changeStatue() {
    if (this.basicData.orderStatus == 1) {
      if (this.basicData.orderPaymentStatus == 0) {
        this.orderStatue = OrderStatusEnum.QueryOrderStatusEnumWaitPay
      } else if (this.basicData.orderPaymentStatus == 1) {
        this.orderStatue = OrderStatusEnum.QueryOrderStatusEnumPaying
      } else if (this.basicData.orderPaymentStatus == 2) {
        this.orderStatue = OrderStatusEnum.QueryOrderStatusEnumOpening
      }
    } else if (this.basicData.orderStatus == 2) {
      this.orderStatue = OrderStatusEnum.QueryOrderStatusEnumCompleted
    } else if (this.basicData.orderStatus == 3) {
      this.orderStatue = OrderStatusEnum.QueryOrderStatusEnumInvalid
    }
  }

  async addInvoice() {
    //如果关联发票有值，那么取关联发票
    try {
      if (this.invoiceApplyInfo && this.invoiceApplyInfo.invoiceIdList && this.invoiceApplyInfo.invoiceIdList.length) {
        const invoiceQuery = new QueryInvoice(this.invoiceApplyInfo.invoiceIdList, this.invoiceApplyInfo.invoiceMethod)
        const invoiceList = await invoiceQuery.onLineGetInvoiceInServicer()
        const tmpArr = []
        for (const firstInvoice of invoiceList) {
          const invoiceApplyInfo = new OrderInvoiceApplyInfoResponseVo()
          // const invoiceNums = (invoiceList as InvoiceListResponse[]).map(item => item.blueInvoiceNo)
          // const firstInvoice = invoiceList[0]
          invoiceApplyInfo.account = firstInvoice.account
          invoiceApplyInfo.invoiceMethod = this.invoiceApplyInfo.invoiceMethod
          invoiceApplyInfo.invoiceCategory = firstInvoice.invoiceCategory
          invoiceApplyInfo.title = firstInvoice.title
          invoiceApplyInfo.titleType = firstInvoice.titleType
          invoiceApplyInfo.taxpayerNo = firstInvoice.taxpayerNo
          invoiceApplyInfo.address = firstInvoice.address
          invoiceApplyInfo.phone = firstInvoice.rePhone
          invoiceApplyInfo.bankName = firstInvoice.bankName
          invoiceApplyInfo.email = firstInvoice.email
          invoiceApplyInfo.contactEmail = firstInvoice.contactEmail
          // invoiceApplyInfo.contactEmail = firstInvoice.email
          invoiceApplyInfo.remark = firstInvoice.remark
          invoiceApplyInfo.appliedTime = firstInvoice.applyForDate
          invoiceApplyInfo.invoiceType = firstInvoice.invoiceType
          if (this.invoiceApplyInfo.invoiceMethod == 2) {
            invoiceApplyInfo.orderNum = (firstInvoice as OffLinePageInvoiceVo).invoiceNo
          } else {
            invoiceApplyInfo.orderNum = (firstInvoice as InvoiceListResponse).blueInvoiceNo
          }
          invoiceApplyInfo.invoiceDate = firstInvoice.invoiceDate
          invoiceApplyInfo.invoiceStatus = firstInvoice.invoiceStatus
          invoiceApplyInfo.blueTotalAmount = (firstInvoice as InvoiceListResponse).blueTotalAmount
          invoiceApplyInfo.totalAmount = (firstInvoice as InvoiceListResponse).totalAmount
          if (this.invoiceApplyInfo.invoiceMethod == 2) {
            invoiceApplyInfo.deliveryInfo = (firstInvoice as OffLinePageInvoiceVo).deliveryInfo
          }
          if (firstInvoice.useless) {
            invoiceApplyInfo.invoiceStatus = 5 //作废
          } else if (firstInvoice.invoiceFreezeStatus) {
            invoiceApplyInfo.invoiceStatus = 4
          }
          invoiceApplyInfo.blueFilePath = (firstInvoice as InvoiceListResponse).blueFilePath
          invoiceApplyInfo.blueFileXmlPath = (firstInvoice as InvoiceListResponse)?.blueFileXmlPath
          invoiceApplyInfo.blueFileOfdPath = (firstInvoice as InvoiceListResponse)?.blueFileOfdPath
          console.log('有关联发票')
          tmpArr.push(invoiceApplyInfo)
        }
        this.invoiceList = tmpArr
      } else {
        if (this.invoiceApplyInfo) {
          ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceStatus = InvoiceStatusEnum.NOTPTOOPEN
        }
      }
    } catch (e) {
      console.log('添加发票错误', e)
    }
  }
  addInvoiceStatic(invoiceList: OffLinePageInvoiceVo[] | InvoiceListResponse[]) {
    //如果关联发票有值，那么取关联发票
    // const invoiceList = await invoiceQuery.onLineGetInvoiceInServicer()
    const tmpArr = []
    for (const firstInvoice of invoiceList) {
      const invoiceApplyInfo = new OrderInvoiceApplyInfoResponseVo()
      // const invoiceNums = (invoiceList as InvoiceListResponse[]).map(item => item.blueInvoiceNo)
      // const firstInvoice = invoiceList[0]
      invoiceApplyInfo.account = firstInvoice.account
      invoiceApplyInfo.invoiceMethod = this.invoiceApplyInfo.invoiceMethod
      invoiceApplyInfo.invoiceCategory = firstInvoice.invoiceCategory
      invoiceApplyInfo.title = firstInvoice.title
      invoiceApplyInfo.titleType = firstInvoice.titleType
      invoiceApplyInfo.taxpayerNo = firstInvoice.taxpayerNo
      invoiceApplyInfo.address = firstInvoice.address
      invoiceApplyInfo.phone = firstInvoice.rePhone
      invoiceApplyInfo.bankName = firstInvoice.bankName
      invoiceApplyInfo.email = firstInvoice.email
      invoiceApplyInfo.contactEmail = firstInvoice.contactEmail
      // invoiceApplyInfo.contactEmail = firstInvoice.email
      invoiceApplyInfo.remark = firstInvoice.remark
      invoiceApplyInfo.appliedTime = firstInvoice.applyForDate
      invoiceApplyInfo.invoiceType = firstInvoice.invoiceType
      if (this.invoiceApplyInfo.invoiceMethod == 2) {
        invoiceApplyInfo.orderNum = (firstInvoice as OffLinePageInvoiceVo).invoiceNo
      } else {
        invoiceApplyInfo.orderNum = (firstInvoice as InvoiceListResponse).blueInvoiceNo
      }
      invoiceApplyInfo.invoiceDate = firstInvoice.invoiceDate
      invoiceApplyInfo.invoiceStatus = firstInvoice.invoiceStatus
      invoiceApplyInfo.blueTotalAmount = (firstInvoice as InvoiceListResponse).blueTotalAmount
      invoiceApplyInfo.totalAmount = (firstInvoice as InvoiceListResponse).totalAmount
      if (this.invoiceApplyInfo.invoiceMethod == 2) {
        invoiceApplyInfo.deliveryInfo = (firstInvoice as OffLinePageInvoiceVo).deliveryInfo
      }
      if (firstInvoice.useless) {
        invoiceApplyInfo.invoiceStatus = 5
      } else if (firstInvoice.invoiceFreezeStatus) {
        invoiceApplyInfo.invoiceStatus = 4
      }
      invoiceApplyInfo.blueFilePath = (firstInvoice as InvoiceListResponse).blueFilePath
      invoiceApplyInfo.blueFileXmlPath = (firstInvoice as InvoiceListResponse)?.blueFileXmlPath
      invoiceApplyInfo.blueFileOfdPath = (firstInvoice as InvoiceListResponse)?.blueFileOfdPath
      console.log('有关联发票')
      tmpArr.push(invoiceApplyInfo)
    }
    this.invoiceList = tmpArr
  }

  async addRefundOrder() {
    const orderList = this.subOrderItems
    // if (orderDetail.returnStatus != 0) {
    const request = new ReturnOrderRequestVo()
    request.subOrderInfo.subOrderNoList = orderList.map((item) => {
      return item.subOrderNo
    })
    const sort = new ReturnSortRequest()
    sort.field = ReturnOrderSortField.APPLIED_TIME
    sort.policy = SortPolicy.DESC
    if (request.subOrderInfo?.subOrderNoList?.length && request.subOrderInfo.subOrderNoList?.length > 1) {
      const refundOrderRes = await tradeQueryGateway.pageReturnOrderInMyself({
        page: {
          pageNo: 1,
          pageSize: 200
        },
        request: request,
        sort: [sort]
      })
      if (
        refundOrderRes.status.isSuccess() &&
        refundOrderRes.data.currentPageData &&
        refundOrderRes.data.currentPageData.length
      ) {
        const returnOrderDetailList = refundOrderRes.data.currentPageData
        // const returnOrderVo = new ReturnOrderResponseVo()
        this.returnOrderDetailList = []
        await Promise.all(
          returnOrderDetailList.map(async (item) => {
            const returnOrderVo = new ReturnOrderResponseVo()
            Object.assign(returnOrderVo, item)
            returnOrderVo.changeStatus()
            await returnOrderVo.fillRecords()
            this.returnOrderDetailList.push(returnOrderVo)
            // return returnOrderVo
          })
        )
        // Object.assign(returnOrderVo, returnOrderDetail)
        // returnOrderVo.changeStatus()
        // await returnOrderVo.fillRecords()
        // this.returnOrderDetail = returnOrderVo
      } else {
        this.returnOrderDetail = null
        this.returnOrderDetailList = []
      }
    } else {
      const refundOrderRes = await tradeQueryGateway.pageReturnOrderInMyself({
        page: {
          pageNo: 1,
          pageSize: 1
        },
        request: request,
        sort: [sort]
      })
      if (
        refundOrderRes.status.isSuccess() &&
        refundOrderRes.data.currentPageData &&
        refundOrderRes.data.currentPageData.length
      ) {
        const returnOrderDetail = refundOrderRes.data.currentPageData[0]
        const returnOrderVo = new ReturnOrderResponseVo()
        Object.assign(returnOrderVo, returnOrderDetail)
        returnOrderVo.changeStatus()
        await returnOrderVo.fillRecords()
        this.returnOrderDetail = returnOrderVo
      } else {
        this.returnOrderDetail = null
      }
    }
    // }
  }
}
