"""独立部署的微服务,K8S服务名:ms-studentcourse-appraisal-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""评价课程
		@param request 请求对象
		@return 评价结果
	"""
	appraisalCourse(request:AppraisalCourseRequest!):AppraisalCourseResponse
	"""删除学员课程评价
		@param studentCourseAppraisalId 课程评价编号
	"""
	deleteAppraisalCourse(studentCourseAppraisalId:String!):Void
}
"""评价课程信息
	<AUTHOR>
	@since 2022/5/16
"""
input AppraisalCourseRequest @type(value:"com.fjhb.ms.studentcourse.appraisal.v1.kernel.gateway.graphql.request.AppraisalCourseRequest") {
	"""学员课程评价凭证，不能为空"""
	courseAppraisalToken:String!
	"""评价内容,不能为空"""
	comment:String!
	"""课程内容评价值，大于0的整型值"""
	courseContentValue:Int!
	"""课程教师评价值，大于0的整型值"""
	courseTeacherValue:Int!
}
"""课程评价结果
	<AUTHOR>
	@since 2022/5/16
"""
type AppraisalCourseResponse @type(value:"com.fjhb.ms.studentcourse.appraisal.v1.kernel.gateway.graphql.response.AppraisalCourseResponse") {
	"""代码：
		200-成功
		50001 - 已评价过，无法评价
	"""
	code:String
	"""信息"""
	message:String
}

scalar List
