import LearningTypeBase from '@api/service/management/train-class/mutation/vo/LearningTypeBase'
import { LearningExperienceEnum } from '@api/service/management/train-class/mutation/Enum/LearningExperienceEnum'
import { Page, ResponseStatus } from '@hbfe/common'
import ExperienceItem from '@api/service/management/train-class/mutation/vo/ExperienceItem'
import { cloneDeep } from 'lodash'

/**
 * 学习心得
 */
class LearningExperience extends LearningTypeBase {
  /**
   * 心得名称
   */
  experienceName = ''
  // 学习心得要求start
  /**
   * 课程心得前置条件 true直接参加
   */
  courseCondition = true
  /**
   * 班级心得前置条件 true直接参加
   */
  classCondition = true
  /**
   * 整体要求
   */
  joinCount = 0
  /**
   * 成绩要求
   */
  score = 0
  /**
   * 是否纳入考核
   */
  isExamine = false
  // 学习心得要求end
  /**
   * 心得列表
   */
  experienceList: ExperienceItem[] = []
  /**
   * 心得列表
   * @description 操作必选使用，使用前init
   */
  private experienceListClone: ExperienceItem[] = []
  /**
   * 班级心得列表
   */
  get classExperienceList() {
    return this.experienceList.filter(item => item.experienceType.equal(LearningExperienceEnum.class_experience))
  }
  /**
   * 课程心得列表
   */
  get courseExperienceList() {
    return this.experienceList.filter(item => item.experienceType.equal(LearningExperienceEnum.course_experience))
  }

  /**
   * 必选数量
   */
  get requireCount() {
    return this.experienceList.filter(item => item.isRequired).length
  }
  /**
   * 新增/编辑
   */
  async edit(item: ExperienceItem) {
    const experienceItemIndex = this.experienceList.findIndex(ite => ite.id === item.id)
    if (experienceItemIndex === -1) {
      this.experienceList.push(item)
    } else {
      Object.assign(this.experienceList[experienceItemIndex], item)
    }
    return new ResponseStatus(200, '保存成功')
  }

  /**
   * 删除
   * @param ids
   */
  remove(ids: Array<string>) {
    ids.forEach(id => {
      if (this.experienceList.findIndex(item => item.id === id) > -1) {
        this.experienceList.splice(
          this.experienceList.findIndex(item => item.id === id),
          1
        )
      }
    })
  }
  /**
   * 删除大纲时删除关联的课程心得
   * @param outlineId 大纲id
   */
  removeOutlineCourse(outlineId: string) {
    const ids = this.experienceList
      .filter(
        item =>
          item.experienceType.equal(LearningExperienceEnum.course_experience) &&
          item.courseList[0].outlineId === outlineId
      )
      .map(item => item.id)
    ids.forEach(id =>
      this.experienceList.splice(
        this.experienceList.findIndex(item => item.id === id),
        1
      )
    )
  }

  /**
   * 重置大纲时删除所有课程心得
   * @param outlineId 大纲id
   */
  removeAllOutlineCourse() {
    const ids = this.experienceList
      .filter(item => item.experienceType.equal(LearningExperienceEnum.course_experience))
      .map(item => item.id)
    ids.forEach(id =>
      this.experienceList.splice(
        this.experienceList.findIndex(item => item.id === id),
        1
      )
    )
  }

  /**
   * 设置必选前init
   */
  selectRequireInit() {
    this.experienceListClone = cloneDeep(this.experienceList)
  }
  /**
   * 获取分页列表
   * @param page
   */
  pageData(page: Page) {
    const filterList = this.experienceList?.slice(page.pageSize * (page.pageNo - 1), page.pageSize * page.pageNo) || []
    page.totalSize = this.experienceList.length
    page.totalPageSize = Math.ceil(this.experienceList.length / page.pageSize)
    return filterList
  }
  /**
   * 获取待选分页列表
   * @param page
   * @param theme 主题
   */
  waitSelectedPageData(page: Page, theme = '') {
    const filterList = this.experienceListClone.filter(item => item.theme.includes(theme)) || []
    const filterPageList = filterList.slice(page.pageSize * (page.pageNo - 1), page.pageSize * page.pageNo) || []
    page.totalSize = filterList.length
    page.totalPageSize = Math.ceil(filterList.length / page.pageSize)
    return filterPageList
  }

  /**
   * 获取已选分页列表
   * @param page
   * @param theme 主题
   */
  selectedPageData(page: Page, theme = '') {
    const filterList = this.experienceListClone.filter(item => item.theme.includes(theme) && item.isRequired) || []
    const filterPageList = filterList.slice(page.pageSize * (page.pageNo - 1), page.pageSize * page.pageNo) || []
    page.totalSize = filterList.length
    page.totalPageSize = Math.ceil(filterList.length / page.pageSize)
    return filterPageList
  }
  /**
   * 保存必选操作
   */
  saveRequired() {
    const ids = this.experienceListClone.filter(item => item.isRequired).map(item => item.id)
    this.experienceList.forEach(item => {
      item.isRequired = ids.includes(item.id)
    })
  }
}
export default LearningExperience
