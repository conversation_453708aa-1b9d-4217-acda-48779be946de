<template>
  <el-main>
    <div class="f-p15">
      <el-tabs v-model="activeName2" type="card" class="m-tab-card">
        <el-tab-pane label="导入学员并开班" name="first">
          <el-card shadow="never" class="m-card f-mb15">
            <el-alert type="warning" :closable="false" class="m-alert">
              <p>温馨提示：</p>
              <p>
                1.导入开通，将由系统以证件号（登录帐号）为依据，对未存在于平台中的学员自动生成帐号并开通学习权限。对未存在于平台中的学员自动生成帐号，对于已存在于平台内的学员，可选择是否变更密码以及除证件号外的其他的信息。
              </p>
              <p>
                2.填写的信息，请按下载的模板填写要求，严格填写。导入表格一次最多支持1000条记录，若超过1000条则不能正常导入；
              </p>
              <p>
                3.批量创建失败的原因。请查阅“导入任务查阅”并下载失败数据！你可以下载未导入成功的信息表，修改正确后再试。你仅需再次上传出错的记录即可。
              </p>
              <p>
                4.如网校开启工作单位配置天眼查/企査查，通过第三方没有査询到对应的单位无法录入系统，如需跳过査询环节，请在对应的单位字段前添加*，例如：*单位名称。
              </p>
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :sm="14" :lg="10">
                <el-steps direction="vertical" :active="5" class="m-vertical-steps">
                  <el-step title="下载批量创建学员模版，填写要求信息">
                    <div slot="description">
                      <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-download">
                        下载模板
                      </el-button>
                      <el-button type="text" size="small" class="f-mt5 f-pl20">
                        查询平台可报培训方案
                      </el-button>
                    </div>
                  </el-step>
                  <el-step title="上传填写好的开通学习权限表格">
                    <div slot="description">
                      <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                        <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                          选择文件
                        </el-button>
                      </el-upload>
                    </div>
                  </el-step>
                  <el-step>
                    <div slot="title">
                      默认密码：
                      <div class="f-flex">
                        <span class="m-radio-group-title">默认密码：</span>
                        <el-radio-group v-model="radio" class="default-psw">
                          <el-radio :label="3">000000</el-radio>
                          <el-radio :label="6">abc123</el-radio>
                          <el-radio :label="8">证件号后六位</el-radio>
                          <el-radio :label="9">
                            <span class="f-mr10">自定义密码</span>
                            <!--选中后出现输入框-->
                            <el-input v-model="form.name" clearable placeholder="请输入自定义密码" />
                          </el-radio>
                        </el-radio-group>
                      </div>
                      <div class="f-flex f-mt20">
                        <span class="m-radio-group-title">密码生效范围：</span>
                        <el-radio-group v-model="radio" class="default-psw">
                          <el-radio :label="3">仅新用户</el-radio>
                          <el-radio :label="6">全部用户（含已注册）</el-radio>
                        </el-radio-group>
                      </div>
                    </div>
                  </el-step>
                  <el-step>
                    <div slot="title">
                      已注册学员更新基础信息：
                      <el-radio-group v-model="radio">
                        <el-radio :label="3" class="f-mr50">是</el-radio>
                        <el-radio :label="6">否</el-radio>
                      </el-radio-group>
                    </div>
                  </el-step>
                  <el-step>
                    <div slot="title">
                      请选择是否按照指定专题导入
                      <el-radio-group v-model="radio" class="default-psw">
                        <el-radio :label="3">不按照专题导入</el-radio>
                        <el-radio :label="6">指定专题导入</el-radio>
                      </el-radio-group>
                    </div>
                    <div slot="description">
                      <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                        <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                          选择专题
                        </el-button>
                      </el-upload>
                      <div class="f-c6 f-f14 f-mt20">
                        <div class="f-fb">已选专题名称：</div>
                        <div class="">
                          <div class="f-mt10">1.读取专题的全称，如太长换行显示读取专题的全称，如太长换行显示</div>
                          <div class="f-mt10">2.读取专题的全称，如太长换行显示读取专题的全称，如太长换行显示</div>
                        </div>
                      </div>
                    </div>
                  </el-step>
                </el-steps>
              </el-col>
            </el-row>
          </el-card>
          <div class="m-btn-bar f-tc is-sticky-1">
            <el-button type="primary">导入</el-button>
          </div>
        </el-tab-pane>
        <el-tab-pane label="导入学员开班并学习" name="second">
          <el-card shadow="never" class="m-card f-mb15">
            <el-alert type="warning" :closable="false" class="m-alert">
              <p>温馨提示：</p>
              <p>
                1.导入学员开班并学习，将由系统以证件号（登录帐号）和报名的培训班为依据，进行模拟学习。
              </p>
              <p>
                2.对未存在于平台中的学员自动生成帐号，对于已存在于平台内的学员如需更新除证件号外的基础信息，请填写该字段，为空默认为不更新。已存在的用户不支持修改密码；
              </p>
              <p>
                3.填写的信息，请按下载的模板填写要求，严格填写。导入表格一次最多支持1000条记录，若超过1000条则不能正常导入；
              </p>
              <p>
                4.批量创建失败的原因。请查阅“导入任务查阅”并下载失败数据！你可以下载未导入成功的信息表，修改正确后再试。你仅需再次上传出错的记录即可。
              </p>
              <p>
                5.如网校开启工作单位配置天眼查/企查查，通过第三方没有查询到对应的单位无法录入系统，如需跳过查询环节，请在对应的单位字段前添加*，例如：*单位名称。
              </p>
              <p>6.培训形式为面授、面网授的班级暂不支持操作导入学员开班并学习。</p>
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :sm="14" :lg="10">
                <el-steps direction="vertical" :active="5" class="m-vertical-steps">
                  <el-step title="下载批量创建学员模版，填写要求信息">
                    <div slot="description">
                      <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-download">
                        下载模板
                      </el-button>
                      <el-button type="text" size="small" class="f-mt5 f-pl20">
                        查询平台可报培训方案
                      </el-button>
                    </div>
                  </el-step>
                  <el-step title="上传填写好的开通学习权限表格">
                    <div slot="description">
                      <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                        <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                          选择文件
                        </el-button>
                      </el-upload>
                    </div>
                  </el-step>
                  <el-step>
                    <div slot="title">
                      默认密码：
                      <div class="f-flex">
                        <span class="m-radio-group-title">默认密码：</span>
                        <el-radio-group v-model="radio" class="default-psw">
                          <el-radio :label="3">000000</el-radio>
                          <el-radio :label="6">abc123</el-radio>
                          <el-radio :label="8">证件号后六位</el-radio>
                          <el-radio :label="9">
                            <span class="f-mr10">自定义密码</span>
                            <!--选中后出现输入框-->
                            <el-input v-model="form.name" clearable placeholder="请输入自定义密码" />
                          </el-radio>
                        </el-radio-group>
                      </div>
                      <div class="f-flex f-mt20">
                        <span class="m-radio-group-title">密码生效范围：</span>
                        <el-radio-group v-model="radio" class="default-psw">
                          <el-radio :label="3">仅新用户</el-radio>
                          <el-radio :label="6">全部用户（含已注册）</el-radio>
                        </el-radio-group>
                      </div>
                    </div>
                  </el-step>
                  <el-step>
                    <div slot="title">
                      已注册学员更新基础信息：
                      <el-radio-group v-model="radio">
                        <el-radio :label="3" class="f-mr50">是</el-radio>
                        <el-radio :label="6">否</el-radio>
                      </el-radio-group>
                    </div>
                  </el-step>
                  <el-step>
                    <div slot="title">
                      请选择是否按照指定专题导入
                      <el-radio-group v-model="radio" class="default-psw">
                        <el-radio :label="3">不按照专题导入</el-radio>
                        <el-radio :label="6">指定专题导入</el-radio>
                      </el-radio-group>
                    </div>
                    <div slot="description">
                      <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                        <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                          选择专题
                        </el-button>
                      </el-upload>
                      <div class="f-c6 f-f14 f-mt20">
                        <div class="f-fb">已选专题名称：</div>
                        <div class="">
                          <div class="f-mt10">1.读取专题的全称，如太长换行显示读取专题的全称，如太长换行显示</div>
                          <div class="f-mt10">2.读取专题的全称，如太长换行显示读取专题的全称，如太长换行显示</div>
                        </div>
                      </div>
                    </div>
                  </el-step>
                </el-steps>
              </el-col>
            </el-row>
          </el-card>
          <div class="m-btn-bar f-tc is-sticky-1">
            <el-button type="primary">导入</el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'second',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
