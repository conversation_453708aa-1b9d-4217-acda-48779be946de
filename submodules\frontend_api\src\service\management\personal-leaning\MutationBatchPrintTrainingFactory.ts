import MutationBatchPrintTraining from '@api/service/management/personal-leaning/mutation/MutationBatchPrintTraining'
import MutationConfigureElectronicSeal from './mutation/MutationConfigureElectronicSeal'

/*
  业务工厂类
*/
class MutationBatchPrintTrainingFactory {
  /*
    打印证明
  */
  get batchPrintTraining() {
    return new MutationBatchPrintTraining()
  }

  /* 
    配置电子章
  */
  get configureElectronicSeal() {
    return new MutationConfigureElectronicSeal()
  }
}

export default new MutationBatchPrintTrainingFactory()
