import LsCommodityWithTeacher from './models/LsCommodityWithTeacher'
import LsCommodityDetail from './models/LsCommodityDetail'
import TeacherInfo from './models/TeacherInfo'
import platformTradeGateway, { CommodityInfoDTO1, IssueDTO, PreExamLSCourseDTO } from '@api/gateway/PlatformTrade'
import LsCourseWithTeacher from './models/LsCourseWithTeacher'
import IssueCommodity from './models/IssueCommodity'
import { ResponseStatus } from '@api/Response'
import * as GraphqlImporter from './graphql-importer'
import Vue from 'vue'
import { LsCommodityPageQuery } from './models/LsCommodityPageQuery'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { BuyCommodityValidationResult } from './models/BuyCommodityValidationResult'
import { UnAuthorize } from '@api/Secure'
import { IssueCommodityDetail } from '@api/service/customer/commodity/models/IssueCommodityDetail'
import PlatformLearningScheme from '@api/gateway/PlatformLearningScheme'
import { CommodityCoursePoolInfo } from '@api/service/customer/commodity/models/CommodityCoursePoolInfo'
import NormalIssueClassLSGateWay, { IssueJoinValidateRequest } from '@api/gateway/NormalIssueClassLS-default'
import { Page, NewPage } from '@api/service/customer/commodity/models/Page'
import MsCommodityGateway from '@api/gateway/ms-commodity-v1'
import { TerminalEnum } from '@api/service/common/models/commodity/TerminalEnum'
import PlatformCommodity, {
  LazyCommodityDTO,
  LazyCommodityQueryParamDTO,
  TrainingInstitutionBaseInfoDTO,
  WorkTypeCategoryWhichUsed
} from '@api/gateway/PlatformCommodity'
import StateKey from '@api/service/customer/common/enums/StateKey'
import PlatformLearningSchemeGateWay from '@api/gateway/PlatformLearningScheme'
import UserModule from '@api/service/customer/user/query-user/UserModule'
import CourseModule from '@api/service/customer/course/CourseModule'

class CommodityPage {
  list: Array<LazyCommodityDTO>
  totalSize: number
}
// 商品模块数据状态
interface CommodityState {
  // 商品列表，即学习方案信息列表
  commodityList: Array<LsCommodityWithTeacher>
  // 商品总数
  totalSize: number
  // 商品总页数listIssueBySchemeId
  totalPageSize: number
  // 商品详情（培训班详情信息学习方案详情）
  commodityDetailMap: {
    [key: string]: LsCommodityDetail
  }
  // 下单前校验结果对象，即点击我要报名时校验结果信息
  buyCommodityValidationResultMap: {
    [key: string]: BuyCommodityValidationResult
  }
  /**
   * 期数商品详情
   */
  issueCommodityDetail: IssueCommodityDetail
  /**
   * 商品集合
   */
  commodityListByIds: Array<CommodityInfoDTO1>
  /**
   * 方案内课程包信息
   */
  commodityCoursePoolInfos: {
    [schemeId: string]: Array<CommodityCoursePoolInfo>
  }
  // sku是否已加载 || workTypeCategoryWhichUsedList与unitList是否已加载
  skuWhichUsedLoad: boolean
  // 已被引用的工种类别与工种树
  workTypeCategoryWhichUsedList: Array<WorkTypeCategoryWhichUsed>
  unitList: Array<TrainingInstitutionBaseInfoDTO>

  // 带mark的请求结果管理
  mapList: { [key: string]: CommodityPage }
  // 详情：调用详情口与分页口会把信息都存在这里面管理，当调用get by id时走这里
  commodityDetailList: Array<LazyCommodityDTO>
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerCommodityModule', store })
class CommodityModule extends VuexModule implements CommodityState {
  mapList: { [key: string]: CommodityPage } = {}
  commodityDetailList: Array<LazyCommodityDTO> = new Array<LazyCommodityDTO>()

  skuWhichUsedLoad = false
  // 已被引用的工种类别与工种树
  workTypeCategoryWhichUsedList: Array<WorkTypeCategoryWhichUsed> = new Array<WorkTypeCategoryWhichUsed>()
  unitList: Array<TrainingInstitutionBaseInfoDTO> = new Array<TrainingInstitutionBaseInfoDTO>()
  suitableTarget: Array<string> = new Array<string>()

  commodityList = new Array<LsCommodityWithTeacher>()
  totalSize = 0
  totalPageSize = 0
  commodityDetailMap: { [key: string]: LsCommodityDetail } = {}
  buyCommodityValidationResultMap: { [key: string]: any } = {}
  commodityCoursePoolInfos: { [schemeId: string]: any } = {}
  private getCommodityPageResponseStatus: ResponseStatus
  private pageQuery?: LsCommodityPageQuery = undefined
  issueCommodityDetail: IssueCommodityDetail = new IssueCommodityDetail()
  commodityListByIds: Array<CommodityInfoDTO1> = new Array<CommodityInfoDTO1>()

  /**
   * 查询延迟商品分页 【机构主页】
   * @param params.mark 请求标记，需要根据该标记去getAppendLazyCommodityPageList方法取到请求结果
   * 默认请传值StateKey.DEFAULT,不传取不到后果自负昂
   * @return response
   */
  @Action
  @UnAuthorize
  async pageLazyTrainingInstitutionCommodity(params: {
    mark?: string
    page: NewPage
    paramDTO: LazyCommodityQueryParamDTO
    append?: boolean
  }) {
    const mark = params.mark || StateKey.DEFAULT
    params.paramDTO.commodityAvailable = true
    if (typeof params.paramDTO.commodityState === 'undefined') {
      params.paramDTO.commodityState = 'UPED'
    }
    const response = await PlatformCommodity.pageLazyCommodity({
      page: params.page,
      paramDTO: params.paramDTO
    })
    const contentIds = response.data.currentPageData.map(d => d.commoditySellingPoint)
    const contentResponse = await PlatformLearningSchemeGateWay.getContentByIds(contentIds)
    if (contentResponse.status.isSuccess()) {
      response.data.currentPageData.forEach(d => {
        d.commoditySellingPoint = contentResponse.data.find(c => c.id === d.commoditySellingPoint)?.content
      })
    }
    if (params.append) {
      // 清空数组
      if (params.page.pageNo === 1) {
        this.CLEAR_LAZY_COMMODITY_LIS(mark)
      }
      this.PUSH_LAZY_COMMODITY_LIST_APPEND({
        mark: mark,
        list: response.data.currentPageData,
        totalSize: response.data.totalSize
      })
    } else {
      this.SET_LAZY_COMMODITY_LIST_APPEND({
        mark: mark,
        list: response.data.currentPageData,
        totalSize: response.data.totalSize
      })
    }
    this.PUSH_COMMODITY_DETAIL_LIST(response.data.currentPageData)
    return response
  }

  /**
   * 查询延迟商品分页 【渠道商主页】
   * @param params.mark 请求标记，需要根据该标记去getAppendLazyCommodityPageList方法取到请求结果
   * 默认请传值StateKey.DEFAULT,不传取不到后果自负昂
   * @return response
   */
  @Action
  @UnAuthorize
  async pageLazyChannelVendorCommodity(params: {
    mark?: string
    page: NewPage
    paramDTO: LazyCommodityQueryParamDTO
    append?: boolean
  }) {
    const mark = params.mark || StateKey.DEFAULT
    params.paramDTO.commodityAvailable = true
    if (typeof params.paramDTO.commodityState === 'undefined') {
      params.paramDTO.commodityState = 'UPED'
    }
    const response = await PlatformCommodity.pageLazyCommodity({
      page: params.page,
      paramDTO: params.paramDTO
    })
    const contentIds = response.data.currentPageData.map(d => d.commoditySellingPoint)
    const contentResponse = await PlatformLearningSchemeGateWay.getContentByIds(contentIds)
    if (contentResponse.status.isSuccess()) {
      response.data.currentPageData.forEach(d => {
        d.commoditySellingPoint = contentResponse.data.find(c => c.id === d.commoditySellingPoint)?.content
      })
    }
    if (params.append) {
      // 清空数组
      if (params.page.pageNo === 1) {
        this.CLEAR_LAZY_COMMODITY_LIS(mark)
      }
      this.PUSH_LAZY_COMMODITY_LIST_APPEND({
        mark: mark,
        list: response.data.currentPageData,
        totalSize: response.data.totalSize
      })
    } else {
      this.SET_LAZY_COMMODITY_LIST_APPEND({
        mark: mark,
        list: response.data.currentPageData,
        totalSize: response.data.totalSize
      })
    }
    this.PUSH_COMMODITY_DETAIL_LIST(response.data.currentPageData)
    return response
  }

  @Mutation
  CLEAR_LAZY_COMMODITY_LIS(mark: string) {
    Vue.delete(this.mapList, mark)
  }
  @Mutation
  private PUSH_LAZY_COMMODITY_LIST_APPEND(param: { mark: string; list: Array<LazyCommodityDTO>; totalSize: number }) {
    if (this.mapList[param.mark]?.list) {
      this.mapList[param.mark].list.push(...param.list)
    } else {
      this.mapList[param.mark] = new CommodityPage()
      this.mapList[param.mark].list = param.list
    }
    Vue.set(this.mapList, param.mark, {
      list: this.mapList[param.mark].list,
      totalSize: this.mapList[param.mark].list?.length
    })
  }
  @Mutation
  private SET_LAZY_COMMODITY_LIST_APPEND(param: { mark: string; list: Array<LazyCommodityDTO>; totalSize: number }) {
    Vue.set(this.mapList, param.mark, { list: param.list, totalSize: param.totalSize })
  }
  @Mutation
  private PUSH_COMMODITY_DETAIL_LIST(details: Array<LazyCommodityDTO>) {
    details?.map(d => {
      const findIndex = this.commodityDetailList.findIndex(c => c.commodityId === d.commodityId)
      if (findIndex === -1) {
        this.commodityDetailList.push(d)
      } else {
        // lodash.remove(this.commodityDetailList, (item: LazyCommodityDTO) => {
        //   return item.commodityId === d.commodityId
        // })
        this.commodityDetailList.splice(findIndex, 1)
        this.commodityDetailList.push(d)
      }
    })
  }

  // 我要报名时校验
  @Action
  async validateBuyCommodity(params: {
    commoditySkuId: string
    issueId: string
    terminal?: TerminalEnum
  }): Promise<ResponseStatus> {
    // const response = await platformTradeGateway.checkBeforeCreateOrder(commoditySkuId)
    const commodityValidateResult = await MsCommodityGateway.validateCommodity({
      commoditySkuId: params.commoditySkuId,
      channelType: 1,
      terminalCode: params.terminal
    })
    if (commodityValidateResult.status.isSuccess()) {
      const validationResult = new BuyCommodityValidationResult()
      if (commodityValidateResult.data.code === '200') {
        validationResult.allowBuy = true
        const request = new IssueJoinValidateRequest()
        request.skuId = params.commoditySkuId
        request.userId = UserModule.userInfo.userId
        request.issueId = params.issueId
        request.purchaseChannelType = 1
        const reserveResponse = await NormalIssueClassLSGateWay.validateJoinIssueLearningScheme(request)
        if (!reserveResponse.status.isSuccess()) {
          const responseStatus = new ResponseStatus(500, '')
          responseStatus.errors = [
            {
              code: 500,
              message: reserveResponse.status.getMessage(),
              source: ''
            }
          ]
          return responseStatus
        }
        // 验证结果
        validationResult.errCode = reserveResponse.data.code
        if (reserveResponse.data.code !== '200') {
          validationResult.allowBuy = false
          validationResult.errMsg = reserveResponse.data.message
          validationResult.orderNo = reserveResponse.data.orderNo
        }
      } else {
        validationResult.allowBuy = false
        validationResult.errCode = commodityValidateResult.data.code
        validationResult.errMsg = commodityValidateResult.data.message
      }
      this.setBuyCommodityValidationResult({ commoditySkuId: params.commoditySkuId, result: validationResult })
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      const responseStatus = new ResponseStatus(500, '')
      responseStatus.errors = [
        {
          code: 500,
          message: commodityValidateResult.status.getMessage(),
          source: ''
        }
      ]
      return responseStatus
    }
  }

  @Action
  @UnAuthorize
  async listIssueByCommoditySkuIds(skuIds: Array<string>) {
    const response = await platformTradeGateway.listIssueByCommoditySkuIds(skuIds)
    this.SET_COMMODITY_LIST_BY_IDS(response.data)
    return response.status
  }

  @Mutation
  private setBuyCommodityValidationResult(payload: { commoditySkuId: string; result: BuyCommodityValidationResult }) {
    Vue.set(this.buyCommodityValidationResultMap, payload.commoditySkuId, payload.result)
  }

  // 获取学习方案(培训班)商品分页,append:表示是否往状态层的商品列表中增加数据，而不是将列表数据变成获取后的分页数据
  @Action
  async getCommodityPage(payload: { query: LsCommodityPageQuery; append: boolean }) {
    // if (this.pageQuery && this.pageQuery.equals(payload.query)) {
    //   return this.getCommodityPageResponseStatus
    // } else {
    //   const response = await platformTradeGateway.pagePutawayPreExamLS({
    //     page: {
    //       pageNo: payload.query.pageNo,
    //       pageSize: payload.query.pageSize
    //     },
    //     query: {
    //       yearCode: payload.query.yearCode,
    //       /**
    //        * 培训类别
    //        */
    //       trainingTypeId: payload.query.trainingTypeId,
    //       /**
    //        * 培训对象
    //        */
    //       workTypeId: payload.query.traineesId,
    //       /**
    //        * 方案名称
    //        */
    //       schemeName: payload.query.schemeName,
    //       /**
    //        * 方案id集合
    //        */
    //       schemeIds: payload.query.schemeIds
    //     }
    //   })
    //   const page = response.data
    //   if (page) {
    //     const purchasedList = new Array<PurchasedProductDTO>()
    //     if (UserModule.isLogin) {
    //       // 如果用户登录，则获取是否购买执行的商品
    //       const commodityIdList = page.currentPageData.map(x => x.commoditySkuId)
    //       if (commodityIdList?.length > 0) {
    //         const { status, data } = await platformTradeGateway.getUserIsPurchasedProduct(commodityIdList)
    //         if (status.isSuccess()) {
    //           purchasedList.push(...data)
    //         }
    //       }
    //     }
    //     this.setCommodityPage({ page, purchasedList, append: payload.append })
    //   }
    //   return response.status
    // }
    // modify by fks :弃用，改走中间表 CommonCommodityModule.pageLazyCommodity
    console.log(payload)
  }

  @Mutation
  private setPageQuery(query: LsCommodityPageQuery) {
    this.pageQuery = query
  }

  @Mutation
  private setGetCommodityPageResponseStatus(status: ResponseStatus) {
    this.getCommodityPageResponseStatus = status
  }

  // 获取学习方案详情
  @Action
  @UnAuthorize
  async getCommodityDetail(schemeId: string): Promise<ResponseStatus> {
    const response = await platformTradeGateway.getPreExamLSById(schemeId)
    const dto = response.data
    if (dto) {
      const detail = LsCommodityDetail.from(dto)
      const combineResponse: any = await platformTradeGateway._commonQuery(
        GraphqlImporter.listPreExamLsCourseAndIssueBySchemeId,
        { schemeId }
      )
      if (combineResponse.data) {
        const courseList = combineResponse.data.courseList as Array<PreExamLSCourseDTO>
        const issueList = combineResponse.data.issueList as Array<IssueDTO>
        if (courseList) {
          const lsCourseList = courseList.map(courseDto => LsCourseWithTeacher.from(courseDto))
          const courseIdList = new Array<string>()
          courseList.map(c => {
            courseIdList.push(c.id)
          })
          const arr = courseList.filter(p => p.courseType === 1).map(p => p.period)
          if (arr && arr.length) {
            detail.compulsoryPeriod = arr.reduce((a, b) => a + b)
            detail.optionalPeriod = detail.maxPeriod - detail.compulsoryPeriod
          } else {
            detail.optionalPeriod = detail.maxPeriod
          }
          detail.courseList = lsCourseList
          detail.teachers = new Array<TeacherInfo>()
          lsCourseList.forEach(c => {
            const list = new Array<TeacherInfo>()
            c.tearcherList.forEach(t => {
              if (!detail.teachers.find(d => d.id === t.id)) {
                list.push({
                  id: t.id,
                  name: t.name,
                  abouts: t.abouts,
                  photo: t.photo
                })
              }
            })
            detail.teachers.push(...list)
          })
          // 加载课程更多信息到缓存
          const res = await CourseModule.loadCourseToCaches({
            courseIds: courseIdList,
            reload: false
          })
          if (!res.isSuccess()) {
            return res
          }
          // 归整课程下的课程包信息
          this.SET_COMMODITY_COURSE_POOL_INFO({
            schemeId: schemeId,
            courses: lsCourseList
          })
        }
        if (issueList) {
          const issueCommodityList = issueList.map(issue => IssueCommodity.from(issue))
          detail.issueCommodityList = issueCommodityList
        }
      }
      this.setCommodityDetail(detail)
      return combineResponse.status
    }
    return response.status
  }

  @Mutation
  private SET_COMMODITY_COURSE_POOL_INFO(param: { schemeId: string; courses: Array<LsCourseWithTeacher> }) {
    if (!this.commodityCoursePoolInfos[param.schemeId]) {
      const pools = new Array<CommodityCoursePoolInfo>()
      param.courses.map(c => {
        const findPool = pools.find(p => p.coursePoolId === c.poolId) || undefined
        if (!findPool) {
          const newPool = new CommodityCoursePoolInfo()
          newPool.coursePoolId = c.poolId
          newPool.coursePoolName = c.poolName
          newPool.coursePoolType = c.coursePoolType
          newPool.courses = new Array<LsCourseWithTeacher>(c)
          pools.push(newPool)
        } else {
          findPool.courses.push(c)
        }
      })
      this.commodityCoursePoolInfos[param.schemeId] = pools
    }
  }

  @Mutation
  setCommodityDetail(detail: LsCommodityDetail) {
    Vue.set(this.commodityDetailMap, detail.schemeId, detail)
  }

  /**
   * 获取期数商品详情
   * @param schemeId
   */
  @Action
  @UnAuthorize
  async getIssueCommodityDetail(schemeId: string) {
    const response = await PlatformLearningScheme.getLearningSchemeDetail(schemeId)
    if (!response.status.isSuccess()) {
      return response.status
    }
    const detail = new IssueCommodityDetail()
    this.setIssueCommodityDetail(detail)
  }

  @Mutation
  setIssueCommodityDetail(detail: IssueCommodityDetail) {
    this.issueCommodityDetail = detail
  }

  @Mutation
  SET_COMMODITY_LIST_BY_IDS(list: Array<CommodityInfoDTO1>) {
    this.commodityListByIds = list
  }
  /**
   * 获取渠道商推广机构的班级数 【渠道商主页】
   * @param commodityState 指定一个商品状态，如果没指定则返回所有 UPED:上架|DOWNED下架
   * @return response
   */
  @Action
  @UnAuthorize
  async countChannelVendorCommodityNumber(param?: {
    commodityState?: string
    trainingInstitutionId?: string
    channelVendorId?: string
    trainingInstitutionStatus?: boolean
  }) {
    const res = await PlatformCommodity.countCommodityNumber({
      commodityState: param.commodityState ? param.commodityState : 'UPED',
      trainingInstitutionIdList: [param.trainingInstitutionId],
      channelVendorIds: [param.channelVendorId],
      trainingInstitutionStatus: param.trainingInstitutionStatus
    })
    return res
  }

  /**
   * 获取所有被引用的单位列表
   */
  get getUnitWhichUsedList() {
    return this.unitList
  }

  /**
   * 获取所有被引用的工种类别sku树
   */
  get getWorkTypeCategoryWhichUsedTree() {
    return this.workTypeCategoryWhichUsedList
  }

  get getBuyCommodityValidationResult() {
    return (commoditySkuId: string): BuyCommodityValidationResult | undefined => {
      return this.buyCommodityValidationResultMap[commoditySkuId]
    }
  }

  /**
   * 获取培训方案详情中对应课程的教师信息
   */
  get getCourseTearcherInfos() {
    return (schemeId: string, courseId: string): Array<TeacherInfo> => {
      const course = this.commodityDetailMap[schemeId]?.courseList.find(course => course.id == courseId)
      if (course) {
        return course.tearcherList
      } else {
        return new Array<TeacherInfo>()
      }
    }
  }

  /**
   * 获取培训班详细信息
   * @see getCommodityDetail
   */
  get getCommoditySchemeDetail() {
    return (schemeId: string): LsCommodityDetail | undefined => {
      return this.commodityDetailMap[schemeId]
    }
  }

  /**
   * 获取指定班级下、指定课程包、指定课程的包内学时
   */
  get getSchemeCoursePeriod() {
    return (schemeId: string, coursePoolId: string, courseId: string) => {
      return this.getCommoditySchemeDetail(schemeId)?.courseList?.find(c => c.poolId === coursePoolId && c.id)?.period
    }
  }

  /**
   * 获取指定学习方案下包含课程列表的课程包列表
   * @see getCommodityDetail
   */
  get getSchemeCourseList() {
    return (
      schemeId: string
    ): Array<{ poolName: string; poolType: number; courseList: Array<LsCourseWithTeacher> }> => {
      const scheme = this.commodityDetailMap[schemeId]
      const list = new Array<{ poolName: string; poolType: number; courseList: Array<LsCourseWithTeacher> }>()
      if (!scheme || !scheme.courseList?.length) {
        return list
      }
      const map = new Map<string, Array<LsCourseWithTeacher>>()
      scheme.courseList.forEach(e => {
        if (!map.has(e.poolId)) {
          map.set(e.poolId, new Array<LsCourseWithTeacher>())
        }
        map.get(e.poolId)?.push(e)
      })
      for (const l of map.values()) {
        if (l.length) {
          const item = {
            poolName: l[0].poolName,
            poolType: l[0].coursePoolType,
            courseList: l
          }
          list.push(item)
        }
      }
      return list
    }
  }

  /**
   * 获取方案下课程包信息（包内含课程）
   */
  get getSchemeCoursePoolInfo() {
    return (schemeId: string): Array<CommodityCoursePoolInfo> => {
      return this.commodityCoursePoolInfos[schemeId]
    }
  }

  /**
   * 获取方案内课程包的课程分页
   */
  get getSchemeCoursePoolCoursePage() {
    return (param: { page: Page; schemeId: string; coursePoolId: string }): Page => {
      if (this.commodityCoursePoolInfos[param.schemeId]) {
        const pools: Array<CommodityCoursePoolInfo> = this.commodityCoursePoolInfos[param.schemeId]
        const pool = pools.find(p => p.coursePoolId === param.coursePoolId) || undefined
        if (pool) {
          return Page.buildPage(pool.courses, param.page.pageNo, param.page.pageSize)
        }
      }
      return param.page
    }
  }
}

export default getModule(CommodityModule)
