<!--学科-->
<template>
  <el-select v-model="selectValue" :placeholder="placeholder" class="form-l" filterable clearable multiple>
    <el-option
      v-for="item in optionList"
      :label="showLabel(item)"
      :value="item.propertyId"
      :key="item.propertyId"
      :disabled="isDisabled && item.name != '全部'"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Mixins, Component, Prop, Watch } from 'vue-property-decorator'
  import QueryDictList from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryDictList'
  import SkuDictType from '@api/service/common/basic-data-dictionary/model/SkuDictType'
  import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
  import CommonSkuMixins from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSkuMixins'

  @Component
  export default class extends Mixins(CommonSkuMixins) {
    /**
     * 是否需要获取展示名称
     */
    @Prop({
      type: Boolean,
      default: false
    })
    showName: boolean

    /**
     * 确认行业id
     */
    @Prop({
      type: String,
      default: ''
    })
    idType: IndustryIdEnum

    /**
     * 父级sku的值(学段id)
     */
    gradePropertyId: string[] = []

    // 全部学科 适配教师
    propertyIdList: string[] = []

    /**
     * sku 列表
     */
    skuList = new Array<SkuDictType>()
    /**
     * 选项列表
     */
    optionList = new Array<SkuDictType>()

    /**
     * 获取展示名称
     */
    get showLabel() {
      return (item: SkuDictType) => {
        if (this.showName) {
          return item.name
        }
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    /**
     * 清空
     */
    clearSelected() {
      this.$emit('clear')
    }

    /**
     * 赋值方法
     */
    setValue() {
      // 若学段为全部
      if (this.gradePropertyId[0] != '-1') {
        this.optionList = this.skuList
        if (this.optionList.length && this.optionList[0].propertyId != '-1') {
          // 若学科为全部
          const param = new SkuDictType()
          param.propertyId = '-1'
          param.name = '全部'
          param.sort = 0
          param.showName = ''
          this.optionList.unshift(param)
        }
        // 按需踢除
        if (this.optionList) {
          this.selectValue = this.selectValue.filter((item) => {
            return this.optionList.map((item) => item.propertyId).includes(item)
          })
          this.$emit('input', this.selectValue)
        }
      } else {
        this.optionList = this.skuList
        if (this.optionList[0]?.propertyId != '-1') {
          const param = new SkuDictType()
          param.propertyId = '-1'
          param.name = '全部'
          param.sort = 0
          param.showName = ''
          this.optionList.unshift(param)
        }
      }
    }

    /**
     * 查询sku列表
     */
    async querySkuList() {
      try {
        this.skuList = await QueryDictList.queryListNew(
          this.categoryCode,
          this.industryPropertyId,
          this.gradePropertyId,
          this.gradePropertyId[0] == '-1' ? this.propertyIdList : this.gradePropertyId
        )
        if (!this.gradePropertyId.length) {
          this.skuList = []
        }
      } catch (e) {
        console.log(e)
      }
    }
    /**
     * 初始化
     */
    async initDisciplineData() {
      await this.querySkuList()
      this.setValue()
    }
  }
</script>
