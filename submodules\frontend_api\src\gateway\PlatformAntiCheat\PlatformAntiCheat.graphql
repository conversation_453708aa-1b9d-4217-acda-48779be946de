schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询用户指定考试答卷的人脸识别防作弊记录，考试过程的相同拍摄点只返回最新的一次拍摄记录，进入考试和退出考试全部返回
		@param
		@return
	"""
	findLastedExamFSInfoList(schemeId:String,userId:String):[LastedExamFSInfoDto]
	"""根据随机码获取随机码信息
		@param
		@return
	"""
	getRandomIdInfo(randomId:String):RandomIdInfo
}
type Mutation {
	applyDatumPhotoRandom:String
}
"""<AUTHOR>
type AntiCheatLogBaseDto @type(value:"com.fjhb.btpx.integrative.service.anticheat.dto.AntiCheatLogBaseDto") {
	"""跟踪维度，0/1/2/3/4，进入场景/退出场景/时间/进度/具体项目"""
	dimensions:Int!
	"""单次拍照路径"""
	currentAnswer:String
	"""当前照片的base64数据"""
	currentAnswerBase64:String
	"""验证结果"""
	result:Boolean!
	"""创建时间"""
	createTime:String
	"""更新时间"""
	updateTime:String
	"""记录点对应的时间点，单位：秒，如：学习对应时长，考试对应当前考试时间"""
	timeLength:Long!
	"""格式化后的时长"""
	formatTimeLength:String
}
"""<AUTHOR>
type LastedExamFSInfoDto @type(value:"com.fjhb.btpx.integrative.service.anticheat.dto.LastedExamFSInfoDto") {
	"""学习方案ID"""
	schemeId:String
	"""学习方式ID"""
	learningId:String
	"""答题记录卷id"""
	answerExamPaperRecordId:String
	"""考试考试时间（用户开始考试的时间）"""
	answerPaperStartTime:String
	"""答卷完成时间"""
	answerPaperCompleteTime:String
	"""考试成绩"""
	examScore:Double!
	"""拍摄认证是否全部通过 | 是否替考"""
	resultAllPass:Boolean!
	"""是否合格"""
	qualified:Boolean!
	"""进入拍摄记录"""
	enterList:[AntiCheatLogBaseDto]
	"""过程拍摄记录"""
	processList:[AntiCheatLogBaseDto]
}
"""<AUTHOR>
	@date 2020/8/24
	@description
"""
type RandomIdInfo @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.RandomIdInfo") {
	"""随机码对应上传的图片url地址"""
	imgUrl:String
	"""随机码是否过期"""
	expire:Boolean!
}

scalar List
