import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { ResponseStatus } from '@api/Response'
import AntiCheatConfigModule, { ExamConfigParameter } from '@api/service/customer/anti-cheat/AntiCheatConfigModule'
import AntiVerifyResult from '@api/service/customer/anti-cheat/models/AntiVerifyResult'
import InterceptResult from '@api/service/customer/anti-cheat/models/InterceptResult'
import $http from '@packages/request'
import Marker from '@api/service/customer/anti-cheat/models/Marker'
import AntiUtils from '@api/service/customer/anti-cheat/models/AntiUtils'
import HttpResponse from '@api/service/customer/anti-cheat/models/HttpResponse'
import ExamScenesAntiConfig from '@api/service/common/models/anticheat/ExamScenesAntiConfig'
import ExamAnswerFSResultModel from '@api/service/common/models/anticheat/ExamAnswerFSResultModel'
import AntiCheatGateway, { ExamAnswerFSResultQueryRequest } from '@api/gateway/AntiCheat-default'
import { Role, RoleType } from '@api/Secure'
/**
 * 本地状态数据
 */
interface ExamAntiCheatState {
  /**
   * 当前平台学习场景防作弊配置
   */
  examScenesConfig: ExamScenesAntiConfig
  /**
   * 请求拦截结果
   */
  interceptResult: InterceptResult
  /**
   * 短码状态
   */
  codeStateResult: AntiVerifyResult
  /**
   * 防作弊验证结果
   */
  antiVerifyResult: AntiVerifyResult
  /**
   * 是否加载学习场景防作弊
   */
  isExamScenesConfigLoad: boolean
  /**
   * 考试作答记录人脸识别结果集合
   */
  examAnswerFSResultList: Array<ExamAnswerFSResultModel>
}

export class InterceptionParameter {
  /**
   * 防作弊配置编号
   * @see #id
   */
  configId = ''
  /**
   * 防作弊模式编号
   * @see ExamScenesAntiConfig#shapeModel#id
   */
  modeId = ''
  userId = ''
  platformId = ''
  platformVersionId = ''
  projectId = ''
  subProjectId = ''
  organizationId = ''
  unitId = ''
  /**
   * 学习方案编号
   */
  schemeId = ''
  /**
   * 课程学习方案编号
   */
  learningId = ''
  /**
   * 场次编号
   */
  examRoundId = ''
  /**
   * 答题卷编号
   */
  answerId = ''
  /**
   * 维度
   */
  dimensions: number
  /**
   * 当前考试经过时间：单位：秒
   */
  timeLength?: number
  /**
   * 记录点
   */
  recordPoint = ''
}

const internalFunction = {
  generateMarkerList(schemeId: string, learningId: string, examRoundId: string, answerId: string) {
    const markerList: Array<Marker> = new Array<Marker>()
    markerList.push(Marker.create('SchemeId', schemeId), Marker.create('LearningId', learningId))

    if (examRoundId) {
      markerList.push(Marker.create('ExamRoundId', examRoundId))
    }
    if (answerId) {
      markerList.push(Marker.create('ExamAnswerId', answerId))
    }

    return markerList
  }
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerExamAntiCheatModule', store })
class ExamAntiCheatModule extends VuexModule implements ExamAntiCheatState {
  /**
   * 当前平台学习场景防作弊配置
   */
  public examScenesConfig: ExamScenesAntiConfig = new ExamScenesAntiConfig()
  /**
   * 请求拦截结果
   */
  public interceptResult: InterceptResult = new InterceptResult()
  /**
   * 短码状态
   */
  public codeStateResult: AntiVerifyResult = new AntiVerifyResult()
  /**
   * 防作弊验证结果
   */
  public antiVerifyResult: AntiVerifyResult = new AntiVerifyResult()
  /**
   * 是否加载学习场景防作弊
   */
  public isExamScenesConfigLoad = false
  /**
   * 考试作答记录人脸识别结果集合
   */
  examAnswerFSResultList: Array<ExamAnswerFSResultModel> = new Array<ExamAnswerFSResultModel>()

  //region action
  /**
   * 加载防作弊配置
   */
  @Action
  @Role([RoleType.user])
  public async init(param: ExamConfigParameter): Promise<ResponseStatus> {
    if (!this.isExamScenesConfigLoad) {
      const response = await AntiCheatConfigModule.initExamConfig(param)
      if (response.isSuccess()) {
        const config = AntiCheatConfigModule.getExamAntiConfig(param)
        if (config) {
          this.SET_EXAM_CONFIG(config)
        }
      }
      return Promise.resolve(response)
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 重新加载学习场景防作弊配置
   */
  @Action
  @Role([RoleType.user])
  public async doReloadExamConfig(param: ExamConfigParameter): Promise<ResponseStatus> {
    this.SET_IS_LOAD_EXAM_CONFIG(false)
    await AntiCheatConfigModule.doReloadExamConfig(param)
    return this.init(param)
  }

  /**
   * 请求判断是否进行防作弊
   * @param params 参数：params - 请求参数
   * @typedef InterceptionParameter
   */
  @Action
  @Role([RoleType.user])
  public async doInterception(params: InterceptionParameter): Promise<ResponseStatus> {
    if (!params.answerId) return Promise.reject(new ResponseStatus(500, 'answerId 必填'))

    const requestParam = {
      configId: params.configId,
      modeId: params.modeId,
      userId: params.userId,
      platformId: params.platformId,
      platformVersionId: params.platformVersionId,
      projectId: params.projectId,
      subProjectId: params.subProjectId,
      organizationId: -1,
      unitId: -1,
      dimensions: params.dimensions,
      recordPoint: params.recordPoint,
      markers: internalFunction.generateMarkerList(
        params.schemeId,
        params.learningId,
        params.examRoundId,
        params.answerId
      ),
      timeLength: params.timeLength
    }
    const response = await $http.post(
      AntiUtils.getInterceptionServiceUrl(),
      AntiUtils.generateRequestParameter(requestParam)
    )
    const result = Object.assign(new HttpResponse(), response.data)
    if (result.successfully()) {
      const interceptResult = Object.assign(new InterceptResult(), result.data)
      this.SET_INTERCEPT_RESULT(interceptResult)
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, result.getMessage()))
    }
  }

  /**
   * 请求检测短码状态
   * @param params 参数：code - 短码
   */
  @Action
  @Role([RoleType.user])
  public async doCheckCodeState(params: { code: string }): Promise<ResponseStatus> {
    const response = await $http.get(AntiUtils.getCheckCodeStateServiceUrl() + '?code=' + params.code)
    const result = Object.assign(new HttpResponse(), response.data)
    if (result.successfully()) {
      const codeResult = Object.assign(new AntiVerifyResult(), result.data)
      this.SET_CODE_STATE_RESULT(codeResult)
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, result.getMessage()))
    }
  }

  /**
   * 请求发送照片进行防作弊验证
   * @param params 参数：code - 短码；photo - 照片的base64字符串
   */
  @Action
  @Role([RoleType.user])
  public async doPush(params: { code: string; photo: string }): Promise<ResponseStatus> {
    const response = await $http.post(AntiUtils.getPushServiceUrl(), AntiUtils.generateRequestParameter(params))
    const result = Object.assign(new HttpResponse(), response.data)
    if (result.successfully()) {
      const pushResult = Object.assign(new AntiVerifyResult(), result.data)
      this.SET_ANTI_VERIFY_RESULT(pushResult)
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, result.getMessage()))
    }
  }

  /**
   * 加载用户一场考试下所有作答记录人脸识别结果
   */
  @Action
  @Role([RoleType.user])
  public async loadExamAnswerFSResultList(params: {
    userId: string
    schemeId: string
    learningId: string
    examRoundId: string
  }): Promise<ResponseStatus> {
    const queryInfo = new ExamAnswerFSResultQueryRequest()
    Object.assign(queryInfo, params)
    const { data, status } = await AntiCheatGateway.findAllUserExamAnswerFSResults(queryInfo)
    if (status.isSuccess()) {
      const list = new Array<ExamAnswerFSResultModel>()
      Object.assign(list, data)
      this.SET_EXAM_ANSWER_FS_RESULT_LIST(list)
    }
    return Promise.resolve(status)
  }

  //endregion

  //region mutation
  /**
   * 设置学习场景防作弊配置
   * @param params
   * @constructor
   */
  @Mutation
  private SET_EXAM_CONFIG(params: ExamScenesAntiConfig) {
    this.examScenesConfig = params
    this.isExamScenesConfigLoad = true
  }

  /**
   * 设置加载学习场景防作弊配置
   * @param isLoad 是否加载
   * @constructor
   */
  @Mutation
  private SET_IS_LOAD_EXAM_CONFIG(isLoad: boolean) {
    this.isExamScenesConfigLoad = isLoad
  }

  /**
   * 设置防作弊拦截验证结果
   * @param params
   * @constructor
   */
  @Mutation
  private SET_INTERCEPT_RESULT(params: InterceptResult) {
    this.interceptResult = params
  }

  /**
   * 设置短码验证结果
   * @param params
   * @constructor
   */
  @Mutation
  private SET_CODE_STATE_RESULT(params: AntiVerifyResult) {
    this.codeStateResult = params
  }

  /**
   * 设置防作弊验证结果
   * @param params
   * @constructor
   */
  @Mutation
  private SET_ANTI_VERIFY_RESULT(params: AntiVerifyResult) {
    this.antiVerifyResult = params
  }

  @Mutation
  private SET_EXAM_ANSWER_FS_RESULT_LIST(params: Array<ExamAnswerFSResultModel>) {
    this.examAnswerFSResultList = params
  }

  //endregion

  //region getter
  /**
   * 获取拦截结果
   * @see doInterception
   */
  get interception(): InterceptResult {
    return this.interceptResult
  }

  /**
   * 获取短码状态
   * @see doCheckCodeState
   */
  get codeState(): AntiVerifyResult {
    return this.codeStateResult
  }

  /**
   * 获取上传照片验证防作弊结果
   * @see doPush
   */
  get pushResult(): AntiVerifyResult {
    return this.antiVerifyResult
  }

  /**
   * 用户一场考试下所有作答记录人脸识别结果
   */
  get getExamAnswerFSResultList(): Array<ExamAnswerFSResultModel> {
    return this.examAnswerFSResultList
  }

  /**
   * 获取指定考试作答记录人脸识别结果
   */
  get getExamAnswerFSResult() {
    return (examAnswerId: string): ExamAnswerFSResultModel | undefined => {
      return this.examAnswerFSResultList.find(e => e.examAnswerId === examAnswerId)
    }
  }

  //endregion
}

export default getModule(ExamAntiCheatModule)
