import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-learningscheme-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-learningscheme-enrollment-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 期别参训资格更换期别请求
<AUTHOR> By Cb
@since 2024/11/12 14:39
 */
export class ChangeIssueRequest {
  /**
   * 学习方案ID
   */
  learningSchemeId: string
  /**
   * 原始期别ID
   */
  originalIssueId?: string
  /**
   * 目标期别ID
   */
  issueId: string
  /**
   * 用户ID【必填】
   */
  userId: string
  /**
   * 报名来源类型【必填】
如：子订单：SUB_ORDER、换货单：EXCHANGE_ORDER
@see StudentSourceTypes
   */
  sourceType: string
  /**
   * 报名来源ID【必填】
   */
  sourceId: string
}

/**
 * 验证报名人数请求
 */
export class ValidateIssueSignUpNumRequest {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 期别ID
   */
  issueId: string
  /**
   * 允许报名人数
   */
  allowSignUpNum: number
}

/**
 * 通用响应基类，用于异常转化为code
 */
export class BaseResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更换期别
   * 60007 -期别不开放报名
   * 60004 -报名未开始
   * 60005 -报名已结束
   * 10005 -期别已开始培训
   * 10006 -期别已结束培训
   * 60001  本期培训已报满
   * @param request:
   * <AUTHOR> By Cb
   * @since 2024/11/26 20:04
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async changeIssue(
    request: ChangeIssueRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.changeIssue,
    operation?: string
  ): Promise<Response<BaseResponse>> {
    return commonRequestApi<BaseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验期别报名数据
   * "80002", "期别已存在报名数据"
   * @param mutate 查询 graphql 语法文档
   * @param issueId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validateIssueSignUpData(
    issueId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validateIssueSignUpData,
    operation?: string
  ): Promise<Response<BaseResponse>> {
    return commonRequestApi<BaseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { issueId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验期别报名人数
   * "80001", "已报名人数>准备开设报名人数"
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validateIssueSignUpNum(
    request: ValidateIssueSignUpNumRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validateIssueSignUpNum,
    operation?: string
  ): Promise<Response<BaseResponse>> {
    return commonRequestApi<BaseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
