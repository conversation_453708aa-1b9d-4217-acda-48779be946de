<template>
  <div class="d-box line h mb15">
    <dv-decoration-2 :color="['#0fbaff']" style="width: 100%; height: 5px; position: absolute; top: -2px;" />
    <div class="tit">
      {{ title }}
      <slot name="tip"></slot>
    </div>
    <!--最少显示5位数，最多显示8位数，超出5位数时添加 more-->
    <div class="num-panel" :class="{ more: data.length > 5 }">
      <span class="item" v-for="(num, index) in data" :key="index">
        <div class="flip-card" :style="{ transform: `translateY(-${num * 46}px)` }">
          <div class="item-card" v-for="i in 10" :key="i">
            {{ i - 1 }}
          </div>
        </div>
      </span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .item {
    overflow: hidden;
  }

  .flip-card {
    position: relative;
    transition: all 1s ease-in-out;
  }
</style>
<script lang="ts">
  import { decoration2 } from '@jiaminghi/data-view'
  import Vue from 'vue'
  import { Component, Prop, Vue as VueDecorator } from 'vue-property-decorator'

  Vue.use(decoration2)
  @Component
  export default class extends VueDecorator {
    @Prop({
      required: true,
      default() {
        return new Array<number>()
      }
    })
    data: Array<number>

    @Prop({
      required: true,
      default: ''
    })
    title: string
  }
</script>
