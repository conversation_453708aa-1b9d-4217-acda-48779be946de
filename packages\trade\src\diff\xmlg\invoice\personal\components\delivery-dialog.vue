<template>
  <el-drawer
    :title="dialogType === 1 ? '确认自取' : '确认配送'"
    :visible.sync="isShow"
    size="600px"
    custom-class="m-drawer"
  >
    <div class="drawer-bd">
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form ref="formRef" :model="form" :rules="formRules" label-width="auto" class="m-form f-mt20">
            <el-form-item label="领取人：" v-if="dialogType === 1" prop="name">
              <el-input v-model="form.name" placeholder="请输入领取人姓名" />
            </el-form-item>
            <el-form-item label="快递公司名称：" v-if="dialogType === 2" prop="courierName">
              <el-input v-model="form.courierName" placeholder="请输入快递公司名称" />
            </el-form-item>
            <el-form-item label="手机号：" v-if="dialogType === 1" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入手机号" />
            </el-form-item>
            <el-form-item label="运单号：" v-if="dialogType === 2" prop="theAwb">
              <el-input v-model="form.theAwb" placeholder="请输入运单号" />
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="isShowDialog">取消</el-button>
              <el-button type="primary" @click="confim">确定</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Ref } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    @Ref('formRef') formRef: any
    @Prop({ type: Number, default: 0 }) dialogType: number
    isShow = false
    loading = false
    form = {
      name: '',
      phone: '',
      courierName: '',
      theAwb: '',
      orderId: ''
    }
    // 修改表单校验rule
    formRules = {
      name: [{ required: true, message: '请输入领取人姓名', trigger: 'blur' }],
      courierName: [{ required: true, message: '请输入快递公司名称', trigger: 'blur' }],
      phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
      theAwb: [{ required: true, message: '请输入运单号', trigger: 'blur' }]
    }
    isShowDialog() {
      this.isShow = !this.isShow
    }
    reset() {
      this.form = {
        name: '',
        phone: '',
        courierName: '',
        theAwb: '',
        orderId: ''
      }
    }
    confim() {
      this.loading = true
      this.formRef.validate(async (valid: boolean, key: any) => {
        if (valid) {
          if (this.dialogType === 1) {
            this.$emit('confirmOrder', this.form)
          }
          if (this.dialogType === 2) {
            this.$emit('confirmDeliveryOrder', this.form)
          }
        }
      })
    }
  }
</script>
