import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-course-back-gateway'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-course-back-gateway'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum CourseSortEnum {
  CREATE_TIME = 'CREATE_TIME'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 课程查询条件
 */
export class CourseRequest {
  /**
   * 分类ID集合
   */
  categoryIdList?: Array<string>
  /**
   * 课程名称
   */
  name?: string
  /**
   * 专题id
   */
  trainingChannelId?: string
}

/**
 * @description: 课程排序请求参数
@author: sugs
@create: 2022-03-14 09:30
 */
export class CourseSortRequest {
  /**
   * 课程排序枚举
   */
  courseSort?: CourseSortEnum
  /**
   * 排序方式 1降序 0升序
   */
  sortType?: number
}

/**
 * 课程
 */
export class CourseResponse {
  /**
   * 课程ID
   */
  id: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 学时
   */
  period: number
  /**
   * 是否被专题引用
   */
  isReferencedByTrainingChannel: boolean
}

export class CourseResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取当前服务商下课程分页  并判断是否被当前专题引用
   * @param page    分页对象
   * @param request 查询参数对象
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCourseInTrainingChannelInServicer(
    params: { page?: Page; request?: CourseRequest; sort?: Array<CourseSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCourseInTrainingChannelInServicer,
    operation?: string
  ): Promise<Response<CourseResponsePage>> {
    return commonRequestApi<CourseResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
