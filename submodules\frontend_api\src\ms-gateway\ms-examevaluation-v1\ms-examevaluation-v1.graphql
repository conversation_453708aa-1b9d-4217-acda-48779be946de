"""独立部署的微服务,K8S服务名:ms-examevaluation-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""人工阅卷
		@param request
	"""
	manualEvaluate(request:AnswerPaperQuestionAnswerManualEvaluatedRequest!):Void
}
"""<AUTHOR> create 2021/6/21 9:17"""
input AnswerPaperQuestionAnswerManualEvaluatedRequest @type(value:"com.fjhb.ms.exam.evaluation.v1.kernel.gateway.graphql.request.AnswerPaperQuestionAnswerManualEvaluatedRequest") {
	"""答卷ID"""
	id:String!
	"""阅卷人ID"""
	markUserId:String!
	"""试题评定列表"""
	questionAnswerEvaluatedList:[QuestionAnswerEvaluated]!
}
input QuestionAnswerEvaluated @type(value:"com.fjhb.ms.exam.evaluation.v1.kernel.gateway.graphql.request.AnswerPaperQuestionAnswerManualEvaluatedRequest$QuestionAnswerEvaluated") {
	"""试题id"""
	questionId:String
	"""试题类型"""
	questionType:Int!
	"""评定结果
		@see QuestionEvaluateResults
	"""
	evaluateResult:Int!
	"""得分，-1表示不为分数评定"""
	score:Double!
}

scalar List
