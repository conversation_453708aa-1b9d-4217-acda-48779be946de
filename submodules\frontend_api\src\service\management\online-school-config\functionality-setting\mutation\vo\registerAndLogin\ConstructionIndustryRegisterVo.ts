import { FieldConstraintResponse } from '@api/ms-gateway/ms-servicer-series-v1'
import FieldConstraintVo from './FieldConstraintVo'

class ConstructionIndustryRegisterVo {
  /**
   * 证书类别
   */
  certificateCategory: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 注册专业
   */
  registerProfessional: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 证书编号
   */
  certificateNo: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 发证日期/有效期开始时间
   */
  releaseStartTime: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 发证日期/有效期结束时间
   */
  certificateEndTime: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 证书附件
   */
  certificateAttachments: FieldConstraintVo = new FieldConstraintVo()

  from(res: Array<FieldConstraintResponse>) {
    const resMap = new Map()
    res?.forEach(item => {
      resMap.set(item.field, item)
    })
    this.certificateCategory = FieldConstraintVo.from(resMap.get('certificateCategory'))
    this.registerProfessional = FieldConstraintVo.from(resMap.get('registerProfessional'))
    this.certificateNo = FieldConstraintVo.from(resMap.get('certificateNo'))
    this.releaseStartTime = FieldConstraintVo.from(resMap.get('releaseStartTime'))
    this.certificateEndTime = FieldConstraintVo.from(resMap.get('certificateEndTime'))
    this.certificateAttachments = FieldConstraintVo.from(resMap.get('certificateAttachments'))
  }
}
export default ConstructionIndustryRegisterVo
