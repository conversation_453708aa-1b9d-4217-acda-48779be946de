import { Component, Vue, Prop } from 'vue-property-decorator'
// import TeachUnitModule from '@api/module/management/teach-unit/TeachUnitModule'
import { TrainingInstitutionQueryParams } from '@api/gateway/PlatformTrainingInstitution'

// class Query extends TeachUnitPageQuery {
//   pageNo = 1
//   pageSize = 10
//   organizationCode = ''
// }

@Component
export default class extends Vue {
  // query = new Query()
  // pageQuery = new TeachUnitPageQuery()
  // selectedUnitId = ''
  // showUnitSelectInput = false
  // // 是否开启显示权限
  // @Prop({ type: Boolean, default: true }) hasPermission: boolean
  // created(): void {
  //   this.init()
  // }
  // async init() {
  //   await BasicDataModule.init()
  //   this.pageQuery.pageNo = 1
  //   this.pageQuery.pageSize = 10
  //   await TeachUnitModule.getTeachUnitPage(this.pageQuery)
  // }
  // get totalSize() {
  //   return TeachUnitModule.totalSize
  // }
  // get totalPageSize() {
  //   return TeachUnitModule.totalPageSize
  // }
  // get teachUnitList() {
  //   return TeachUnitModule.teachUnitList
  // }
  // get totalTeachUnitList() {
  //   return TeachUnitModule.listUnit
  // }
  // //查找单位信息
  // get UnitInfo() {
  //   return (unitId: string): any => {
  //     return TeachUnitModule?.teachUnitList?.find(unit => unit.id === unitId) || {}
  //   }
  // }
  // handleSizeChange(pageSize = 10) {
  //   this.pageQuery.pageSize = pageSize
  //   TeachUnitModule.getTeachUnitPage(this.pageQuery)
  // }
  // handleCurrentChange(pageNo = 1) {
  //   this.pageQuery.pageNo = pageNo
  //   TeachUnitModule.getTeachUnitPage(this.pageQuery)
  // }
}
