import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjy-attendance-template-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjy-attendance-template-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 创建考勤模板请求
<AUTHOR>
@since 2024/12/10
 */
export class CreateAttendanceTemplateRequest {
  /**
   * 签到点信息
   */
  attendanceSignIn?: AttendanceSignDto
  /**
   * 签退点信息
   */
  attendanceSignOut?: AttendanceSignDto
}

/**
 * 更新考勤模板请求
<AUTHOR>
@since 2024/12/10
 */
export class UpdateAttendanceTemplateRequest {
  /**
   * id
   */
  attendanceId?: string
  /**
   * 签到点信息
   */
  attendanceSignIn?: AttendanceSignDto
  /**
   * 签退点信息
   */
  attendanceSignOut?: AttendanceSignDto
}

/**
 * <AUTHOR>
@since
 */
export class AttendanceSignDto {
  /**
   * 是否开启
   */
  enable: boolean
  /**
   * 签到频率
半天  1;
每节课  2;
@see SignFrequencyTypes
   */
  frequency: number
  /**
   * 签到半径
   */
  radius: number
  /**
   * 签到开始前
单位:(秒)
   */
  beforeSecond: number
  /**
   * 开始后
单位:(秒)
   */
  afterSecond: number
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建考勤模板
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createAttendanceTemplate(
    request: CreateAttendanceTemplateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createAttendanceTemplate,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除考勤模板
   * @param mutate 查询 graphql 语法文档
   * @param attendanceTemplateId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteAttendanceTemplate(
    attendanceTemplateId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteAttendanceTemplate,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { attendanceTemplateId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新考勤模板
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateAttendanceTemplate(
    request: UpdateAttendanceTemplateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateAttendanceTemplate,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
