import store from '@/store'
import QueryOnlineSchoolConfigFactory from '@api/service/common/online-school-config/QueryOnlineSchoolConfigFactory'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'

@Module({
  name: 'CommonOnlineSchoolConfigModule',
  dynamic: true,
  namespaced: true,
  store
})
class OnlineSchoolConfigModule extends VuexModule {
  queryOnlineSchoolConfigFactory: QueryOnlineSchoolConfigFactory = new QueryOnlineSchoolConfigFactory()
}

export default getModule(OnlineSchoolConfigModule)
