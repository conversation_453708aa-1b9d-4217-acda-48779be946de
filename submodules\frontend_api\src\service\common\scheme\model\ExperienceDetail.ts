import JoinTimeType, { JoinTimeTypeEnum } from '@api/service/common/scheme/enum/JoinTimeType'
import ExperienceType, { ExperienceTypeEnum } from '@api/service/common/scheme/enum/ExperienceType'
import AnswerType, { AnswerTypeEnum } from '@api/service/common/scheme/enum/AnswerType'
import CheckType, { CheckTypeEnum } from '@api/service/common/scheme/enum/CheckType'
import ExperienceCourseDetail from '@api/service/common/scheme/model/ExperienceCourseDetail'
import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
import Mockjs from 'mockjs'

/**
 * @description 学习心得模型
 */
class ExperienceDetail {
  /**
   * 心得id
   */
  id = ''
  /**
   * 默认id前缀
   */
  static experienceIdPre = 'experienceIdPre'
  /**
   * 主题
   */
  theme = ''
  /**
   * 是否必选
   */
  isRequired = false
  /**
   * 参加时间类型
   */
  joinTimeType = new JoinTimeType(JoinTimeTypeEnum.class_time)
  /**
   * 学习心得内容
   */
  content = ''
  /**
   * 参加时间
   */
  joinTime: string[] = []
  /**
   * 心得类型
   */
  experienceType = new ExperienceType(ExperienceTypeEnum.class_experience)
  /**
   * 作答形式
   */
  answerType = new AnswerType(AnswerTypeEnum.attachments)
  /**
   * 审核方式
   */
  checkType = new CheckType(CheckTypeEnum.auto)
  /**
   * 分数
   */
  score = 100
  /**
   * 提交文件大小/字数
   */
  submitLimitNum = 0
  /**
   * 提交次数类型
   * @description true-限制次数，false-不限次数
   */
  submitCountType = false
  /**
   * 提交次数/未通过作答次数
   */
  submitCount = 0
  /**
   * 指定课程
   */
  courseList: ExperienceCourseDetail[] = []
  /**
   * 操作类型
   * @description 1创建2修改3删除
   */
  operation: OperationTypeEnum = OperationTypeEnum.create
  /**
   * 课程内容及课程信息是否已回显
   */
  isInfoShow = true
  /**
   * 是否查到课程
   */
  hasCourse = true

  constructor(id = `${ExperienceDetail.experienceIdPre}${Mockjs.Random.guid()}`, isInfoShow = true) {
    // id占位符
    this.id = id
    // 默认已有
    this.isInfoShow = isInfoShow
  }
}

export default ExperienceDetail
