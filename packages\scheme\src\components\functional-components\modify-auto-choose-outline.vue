<template>
  <div class="pure">
    <div class="f-pt5 f-mtb20">
      <!--有修改的添加 <i class="is-tag"></i>-->
      <span class="f-mr10">请选择课程展示方式：</span>
      <el-radio-group v-model="hasChildren" :disabled="!enableEdit" @change="handleCourseShowTypeChange">
        <el-radio :label="false">无分类</el-radio>
        <el-radio :label="true">有分类</el-radio>
      </el-radio-group>
    </div>
    <!--无分类-->
    <div class="pure" v-show="!hasChildren">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="f-p20">
          <div class="f-flex f-align-center">
            <div class="f-mr10">
              <el-button
                type="primary"
                size="small"
                icon="el-icon-plus"
                :disabled="disabledAddCourse(outlineInfo)"
                @click="addCourse(outlineInfo)"
                >添加课程
              </el-button>
              <el-button type="primary" size="small" plain @click="removeAllCourse(outlineInfo)">
                一键移除课程
              </el-button>
              <el-button type="primary" size="small" plain @click="popCompulsoryCourseManage(outlineInfo)">
                必学课程管理
              </el-button>
              <el-button type="primary" size="small" plain @click="popViewCompulsoryCourse">查看必学课程</el-button>
            </div>
            <div class="f-fb f-flex-sub">
              一共 <i class="f-cr">{{ outlineInfo.courseTotal }}</i> 门，<i class="f-cr">
                {{ outlineInfo.coursePeriodTotal }}
              </i>
              学时，必学课程 <i class="f-cr">{{ outlineInfo.compulsoryCourseTotal }}</i> 门，<i class="f-cr">
                {{ outlineInfo.compulsoryCoursePeriodTotal }}</i
              >学时
            </div>
            <el-button
              type="text"
              class="f-ml10"
              @click="editCoursePackage(outlineInfo)"
              v-show="outlineInfo.coursePackageId"
            >
              编辑课程包
            </el-button>
          </div>
          <el-table
            stripe
            :data="outlineInfo.courseList"
            max-height="400px"
            class="m-table f-mt15"
            v-loading="courseQuery.loading"
          >
            <el-table-column type="index" label="No." width="60" align="center">
              <template slot-scope="scope">
                <span
                  :data-index="scope.$index + 1"
                  v-observe-visibility="(isVisible, entry) => visibleCourseList(isVisible, entry)"
                  >{{ scope.$index + 1 }}</span
                >
              </template>
            </el-table-column>
            <el-table-column label="课程名称" min-width="240">
              <template slot-scope="scope">
                <div v-if="isCompulsoryCourseWithoutClassification(outlineInfo, scope.row)">
                  <el-tag type="danger" size="mini" class="f-mr5">必学</el-tag>
                  {{ scope.row.name }}
                </div>
                <div v-else>{{ scope.row.name }}</div>
              </template>
            </el-table-column>
            <el-table-column label="所属课程包名称" min-width="200">
              <template slot-scope="scope">{{ scope.row.coursePackageName }}</template>
            </el-table-column>
            <el-table-column label="课程学时数" width="120" align="center">
              <template slot-scope="scope">{{ scope.row.period }}</template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <!--<el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>-->
        </div>
      </el-card>
    </div>
    <!--有分类-->
    <div class="pure" v-show="hasChildren">
      <div :class="isOutlineWeighty ? 'f-ptb20 fixed-btn' : 'f-ptb20'">
        <el-button type="primary" icon="el-icon-plus" @click="addOutlineTreeNode">添加分类</el-button>
        <el-button type="primary" icon="el-icon-plus" v-if="isOutlineWeighty" @click="addCourse">添加课程</el-button>
        <el-button type="primary" plain v-if="isOutlineWeighty" @click="popViewCompulsoryCourse">
          查看必学课程
        </el-button>
      </div>
      <el-card shadow="never" class="m-card is-header f-mb15" v-if="!isOutlineWeighty">
        <div class="m-no-date f-ptb30">
          <img class="img" :src="require('@design/admin/assets/images/no-data-normal.png')" alt="" />
          <div class="date-bd">
            <p class="f-f15 f-c9">请先添加分类再添加课程~</p>
          </div>
        </div>
      </el-card>
      <div class="pure" v-if="isOutlineWeighty && uiConfig.showTree">
        <!-- <vue-draggable v-model="outlineInfo.childOutlines"> -->
        <el-card
          shadow="never"
          class="m-card is-header f-mb15"
          v-for="(item, index) in outlineInfo.childOutlines"
          :key="index"
        >
          <div class="m-tit is-small bg-gray is-border-bottom f-align-center">
            <!--点击修改后隐藏-->
            <span class="tit-txt" v-show="!selectedOutlineInfoList[index].isNameEditable">{{ item.name }}</span>
            <a
              class="f-link f-cb f-ml10 m-tooltip-icon"
              v-show="!selectedOutlineInfoList[index].isNameEditable"
              @click="editTopLevelOutlineName(item, index)"
            >
              <i class="el-icon-edit-outline f-f18"></i>
            </a>
            <!-- /点击修改后隐藏-->
            <!--点击修改后出现-->
            <el-input
              v-show="selectedOutlineInfoList[index].isNameEditable"
              v-model="currentOutlineValue"
              size="mini"
              clearable
              placeholder="请输入名称"
              class="ipt"
            />
            <a
              class="f-link f-cb f-ml10 m-tooltip-icon"
              v-show="selectedOutlineInfoList[index].isNameEditable"
              @click="confirmTopLevelOutlineName(item, index)"
            >
              <i class="el-icon-circle-check f-f18"></i>
            </a>
            <a
              class="f-link f-cb f-ml10 m-tooltip-icon"
              v-show="selectedOutlineInfoList[index].isNameEditable"
              @click="cancelEditTopLevelOutlineName(index)"
            >
              <i class="el-icon-circle-close f-f18"></i>
            </a>
            <!--/点击修改后出现-->
            <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
              <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
              <div slot="content">
                按照分类展示课程，可指定必学课程和学习要求学时，支持设置多个子分类。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置。
              </div>
            </el-tooltip>
            <span class="f-fb f-ml20 f-flex-sub f-tr">
              一共 <i class="f-cr">{{ item.courseTotal }}</i> 门，<i class="f-cr">{{ item.coursePeriodTotal }}</i>
              学时，必学课程
              <i class="f-cr">{{ item.compulsoryCourseTotal }}</i>
              门，<i class="f-cr">{{ item.compulsoryCoursePeriodTotal }}</i>
              学时
            </span>
          </div>
          <div class="f-flex f-align-center f-plr20 f-pt20">
            <el-form :ref="`requirePeriodForm${index}`" :model="item.assessSetting" :rules="rules">
              <el-form-item
                prop="requirePeriod"
                :required="requirePeriodList[index].isRequirePeriod"
                :rules="
                  requirePeriodList[index].isRequirePeriod
                    ? requirePeriod(item)
                      ? [
                          {
                            type: 'number',
                            validator: (rules, value, callback) => {
                              return validateRequirePeriod(rules, value, callback, item)
                            },
                            trigger: ['blur', 'change']
                          }
                        ]
                      : [
                          {
                            require: true,
                            type: 'number',
                            validator: validateRequirePeriod,
                            trigger: ['blur', 'change']
                          }
                        ]
                    : []
                "
              >
                <el-checkbox
                  v-model="requirePeriodList[index].isRequirePeriod"
                  :disabled="requirePeriod(item) || !hasCourse(item)"
                  @change="
                    (value) => {
                      return handleRequirePeriod(index, value)
                    }
                  "
                >
                  {{ item.name }}要求完成
                </el-checkbox>
                <el-input-number
                  v-model.number="item.assessSetting.requirePeriod"
                  :disabled="requirePeriodList[index].isRequirePeriod && hasCourse(item) ? false : true"
                  size="small"
                  :min="0"
                  class="f-mlr5"
                />
                学时
              </el-form-item>
            </el-form>
            <div class="f-flex-sub f-tr">
              <el-button
                type="primary"
                size="small"
                plain
                v-if="isLeafNode(index) && selectedOutlineInfoList[index].currentCourseInfo.coursePackageId"
                @click="popCompulsoryCourseManage(selectedOutlineInfoList[index].currentCourseInfo)"
              >
                必学课程管理
              </el-button>
              <el-button
                type="primary"
                size="small"
                plain
                v-if="isLeafNode(index)"
                @click="removeAllCourse(selectedOutlineInfoList[index].currentCourseInfo, index)"
              >
                一键移除课程
              </el-button>
              <el-button
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="addCourse(selectedOutlineInfoList[index].currentCourseInfo)"
                v-if="isLeafNode(index)"
                :disabled="Boolean(selectedOutlineInfoList[index].currentCourseInfo.coursePackageId)"
              >
                添加课程
              </el-button>
            </div>
          </div>
          <el-divider class="m-divider no-mb"></el-divider>
          <el-row class="is-height">
            <el-col :sm="8" :xl="6" class="is-border-right">
              <div class="f-p20">
                <!--第一级-->
                <el-tree
                  :data="[item]"
                  node-key="id"
                  :expand-on-click-node="false"
                  class="m-course-tree"
                  @node-drop="handleDrop"
                  draggable
                  :allow-drag="allowDrag"
                  :allow-drop="allowDrop"
                  default-expand-all
                  :props="{ label: 'name', children: 'childOutlines' }"
                  @node-click="
                    (data, node, obj) => {
                      return handleNodeClick(data, true, index)
                    }
                  "
                >
                  <div class="custom-tree-node" slot-scope="{ node, data }">
                    <!--有新增或者修改的在 添加 <i class="is-tag"></i>-->
                    <div class="tit" v-show="!data.isEditing">{{ node.label }}</div>
                    <div class="op" v-show="!data.isEditing">
                      <el-button type="text" size="mini" @click.stop="() => editTreeNodeName(data)">
                        <i class="el-icon-edit-outline"></i>
                      </el-button>
                      <el-button type="text" size="mini" @click.stop="() => removeTreeNode(node)">
                        <i class="el-icon-delete"></i>
                      </el-button>
                    </div>
                    <!--编辑中-->
                    <div class="tit" v-show="data.isEditing">
                      <el-input v-model="currentOutlineValue" size="mini" clearable placeholder="请输入" />
                    </div>
                    <div class="op" v-show="data.isEditing">
                      <el-button type="text" size="mini" @click.stop="() => confirmEditTreeNodeName(data)">
                        <i class="el-icon-circle-check"></i>
                      </el-button>
                      <el-button type="text" size="mini" @click.stop="() => cancelEditTreeNodeName(data)">
                        <i class="el-icon-circle-close"></i>
                      </el-button>
                    </div>
                  </div>
                </el-tree>
              </div>
            </el-col>
            <el-col :sm="16" :xl="18">
              <div class="f-p20" v-show="selectedOutlineInfoList[index].isCourseListVisible">
                <div class="f-flex f-align-center">
                  <div class="m-tit is-mini">
                    <span class="tit-txt">{{ selectedOutlineInfoList[index].currentCourseInfo.name }}</span>
                  </div>
                  <div class="f-fb f-flex-sub">
                    （一共
                    <i class="f-cr">{{ selectedOutlineInfoList[index].currentCourseInfo.courseTotal }}</i> 门，<i
                      class="f-cr"
                      >{{ selectedOutlineInfoList[index].currentCourseInfo.coursePeriodTotal }}</i
                    >
                    学时，必学课程
                    <i class="f-cr">{{ selectedOutlineInfoList[index].currentCourseInfo.compulsoryCourseTotal }}</i>
                    门，<i class="f-cr">{{
                      selectedOutlineInfoList[index].currentCourseInfo.compulsoryCoursePeriodTotal
                    }}</i>
                    学时）
                  </div>
                  <el-button
                    type="text"
                    class="f-ml10"
                    v-show="isLeafNode(index) && selectedOutlineInfoList[index].currentCourseInfo.coursePackageId"
                    @click="editCoursePackage(selectedOutlineInfoList[index].currentCourseInfo)"
                    >编辑课程包</el-button
                  >
                </div>
                <el-table
                  :ref="`courseTableRef_${index}`"
                  stripe
                  :data="selectedOutlineInfoList[index].courseList"
                  v-loading="selectedOutlineInfoList[index].courseQuery.loading"
                  max-height="400px"
                  class="m-table f-mt15"
                >
                  <el-table-column type="index" label="No." width="60" align="center">
                    <template slot-scope="scope">
                      <span
                        :data-index="scope.$index + 1"
                        v-observe-visibility="
                          (isVisible, entry) => visibleCourseListHasChildren(isVisible, entry, index)
                        "
                        >{{ scope.$index + 1 }}</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column label="课程名称" min-width="240">
                    <template slot-scope="scope">
                      <div v-if="isCompulsoryCourse(index, scope.row)">
                        <el-tag type="danger" size="mini" class="f-mr5">必学</el-tag>
                        {{ scope.row.name }}
                      </div>
                      <div v-else>{{ scope.row.name }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="分类信息" min-width="200">
                    <template slot-scope="scope">{{ getCourseCategory(scope.row) }}</template>
                  </el-table-column>
                  <el-table-column label="所属课程包名称" min-width="200">
                    <template slot-scope="scope">{{ scope.row.coursePackageName }}</template>
                  </el-table-column>
                  <el-table-column label="课程学时数" width="120" align="center">
                    <template slot-scope="scope">{{ scope.row.period }}</template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <!--<el-pagination
                    background
                    class="f-mt15 f-tr"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage4"
                    :page-sizes="[100, 200, 300, 400]"
                    :page-size="100"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="400"
                  >
                  </el-pagination>-->
              </div>
            </el-col>
          </el-row>
        </el-card>
        <!-- </vue-draggable> -->
      </div>
    </div>
    <!--新建分类-->
    <el-drawer
      title="新建分类信息"
      :visible.sync="uiConfig.dialog.addOutlineVisible"
      size="600px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
      @close="cancelAddOutlineTreeNode"
    >
      <div class="drawer-bd">
        <el-form ref="form" label-width="auto" class="m-form f-mt20">
          <el-form-item label="分类名称：">
            <el-input
              v-model="createOutlineInfo.name"
              clearable
              maxlength="15"
              show-word-limit
              placeholder="请输入分类名称，不能超过15字"
            />
          </el-form-item>
          <el-form-item label="上级分类：">
            <el-radio-group v-model="hasParent" @change="handleHasParentChange" class="radio-group">
              <el-radio :label="false" class="radio">无上级分类</el-radio>
              <el-radio :label="true" class="radio"> 有上级分类 </el-radio>
            </el-radio-group>
            <el-cascader
              class="select-right"
              clearable
              lazy
              v-if="hasParent"
              :options="outlineCategoryOptions"
              :props="defaultProps"
              v-model="createOutlineInfo.parentNodePath"
              placeholder="请选择上级分类"
            />
          </el-form-item>
          <el-form-item class="m-btn-bar">
            <el-button @click="cancelAddOutlineTreeNode">取消</el-button>
            <el-button type="primary" @click="saveOutlineTreeNode">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
    <choose-course-package
      ref="chooseCoursePackageRef"
      :options="outlineInfo.childOutlines"
      v-model="uiConfig.dialog.chooseCoursePackageVisible"
      :path="selectedOutlinePath"
      :show-category="hasChildren"
      @updateTreeNode="updateTreeNode"
    >
    </choose-course-package>
    <compulsory-course-manage
      ref="compulsoryCourseManageRef"
      v-model="uiConfig.dialog.compulsoryCourseManageVisible"
      :resource="selectedOutline"
      :outline-tree="outlineInfo.childOutlines"
      :has-children="hasChildren"
      @updateNodeCompulsoryCourse="handleUpdateNodeCompulsoryCourse"
      :schemeId="schemeDetail.idCopy"
    >
    </compulsory-course-manage>
    <view-compulsory-course
      ref="viewCompulsoryCourseRef"
      v-model="uiConfig.dialog.viewCompulsoryCourseVisible"
      :options="outlineInfo.childOutlines"
      :has-children="hasChildren"
      :resource="outlineInfo"
      :schemeId="schemeDetail.idCopy"
    >
    </view-compulsory-course>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, PropSync, Ref, Vue, Watch } from 'vue-property-decorator'
  import CreateSchemeUIModule from '@/store/modules-ui/scheme/CreateSchemeUIModule'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import Mockjs from 'mockjs'
  import ChooseCoursePackage from '@hbfe/jxjy-admin-scheme/src/components/functional-components/choose-course-package.vue'
  import {
    RequirePeriod,
    CreateSchemeUtils,
    SchemeCourseDetailInCoursePackage
  } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'
  import UtilClass from '@hbfe/jxjy-admin-common/src/util'
  import CompulsoryCourseManage from '@hbfe/jxjy-admin-scheme/src/components/functional-components/compulsory-course-manage.vue'
  import { cloneDeep } from 'lodash'
  import ViewCompulsoryCourse from '@hbfe/jxjy-admin-scheme/src/components/functional-components/view-compulsory-course.vue'
  import { TreeNode } from 'element-ui/types/tree'
  import { CreateOutlineUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateOutlineUtils'
  import VueDraggable from 'vuedraggable'
  import { Query, UiPage } from '@hbfe/common'
  import QueryCoursePackage from '@api/service/management/resource/course-package/query/QueryCoursePackage'
  import SchemeDetailUtils from '@hbfe/jxjy-admin-scheme/src/utils/SchemeDetailUtils'
  import { CompulsoryCourseInfo } from '@api/service/management/train-class/mutation/vo/CompulsoryCourseInfo'
  import { CourseLoadModeEnum } from '@api/service/management/train-class/mutation/Enum/CourseLoadMode'
  import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'
  import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
  import CourseStatisticVo from '@api/service/management/resource/course/query/vo/CourseStatisticVo'
  import LearningExperience from '@api/service/management/train-class/mutation/vo/LearningExperience'

  /**
   * 选中的课程大纲节点信息
   */
  class EditOutlineInfo {
    // 是否可编辑
    isNameEditable: boolean
    // 是否展示分类下课程
    isCourseListVisible: boolean
    // 当前分类课程信息
    currentCourseInfo: Classification
    // 是否加载右侧内容
    contentLoading: boolean
    // 课程分页
    coursePage: UiPage
    // 课程查询
    courseQuery: Query = new Query()
    // 课程列表
    courseList: CourseInCoursePackage[]

    constructor() {
      this.contentLoading = false
      this.currentCourseInfo = new Classification()
      this.coursePage = new UiPage()
      this.courseQuery = new Query()
      this.courseList = [] as CourseInCoursePackage[]
    }
  }

  /**
   * 大纲节点信息
   */
  class OutlineInfo {
    // 节点路径
    parentNodePath: Array<string>
    // 展示名称
    name: string

    constructor() {
      this.parentNodePath = [] as string[]
      this.name = ''
    }
  }

  @Component({
    components: { ViewCompulsoryCourse, CompulsoryCourseManage, ChooseCoursePackage, VueDraggable }
  })
  export default class extends Vue {
    /**
     * 学习心得配置 - 双向绑定
     */
    @Prop({ type: LearningExperience }) learningExperience: LearningExperience
    /**
     * 初始化模型：1-课程学习，2-兴趣课
     */
    @Prop({
      required: true,
      type: Number
    })
    initMode: number

    /**
     * 是否允许编辑 - 结合不同initMode展示不同提示信息
     */
    @Prop({
      required: true,
      type: Boolean
    })
    enableEdit: boolean

    /**
     * 大纲树 - 双向绑定
     */
    @PropSync('outlineTree', {
      type: Classification
    })
    outlineInfo!: Classification
    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: TrainClassBaseModel }) schemeDetail!: TrainClassBaseModel
    @Ref('chooseCoursePackageRef') chooseCoursePackageRef: ChooseCoursePackage

    @Ref('compulsoryCourseManageRef') compulsoryCourseManageRef: CompulsoryCourseManage

    @Ref('viewCompulsoryCourseRef') viewCompulsoryCourseRef: ViewCompulsoryCourse

    /**
     * 有无分类
     */
    hasChildren = false
    // 选中的课程大纲节点 - 必学课程管理
    selectedOutline: Classification = new Classification()

    // 创建大纲树节点信息
    createOutlineInfo: OutlineInfo = new OutlineInfo()
    // 选中的课程大纲信息列表
    selectedOutlineInfoList: Array<EditOutlineInfo> = new Array<EditOutlineInfo>()
    // 当前（正在编辑的）大纲节点名称
    currentOutlineValue = ''
    // 课程大纲树内选中的节点路径
    selectedOutlinePath: Array<string> = new Array<string>()
    // 课程分类选项 - 新增分类
    outlineCategoryOptions: Array<Classification> = new Array<Classification>()
    // 是否有上级分类 - 新增分类
    hasParent = false
    // 要求学时管理
    requirePeriodList: RequirePeriod[] = [] as RequirePeriod[]
    // 分页查询课程包
    queryCoursePackageM: QueryCoursePackage = new QueryCoursePackage()
    // 查询课程总控
    queryCourseM: QueryCourse = new QueryCourse()
    // 课程分页
    coursePage: UiPage = new UiPage()
    // 课程查询
    courseQuery: Query = new Query()
    // 课程统计信息列表
    courseStatisticList: CourseStatisticVo[] = []
    courseCompulsoryStatisticList: Map<string, number> = new Map<string, number>()
    // 级联选择器配置项
    defaultProps = {
      label: 'name',
      children: 'childOutlines',
      value: 'id',
      checkStrictly: true // 取消关联，可选任意级
    }

    /**
     *  ui相关的变量控制
     */
    uiConfig = {
      // 对话框是否展示
      dialog: {
        // 新建课程分类抽屉
        addOutlineVisible: false,
        // 选择课程包抽屉
        chooseCoursePackageVisible: false,
        // 必学课程管理
        compulsoryCourseManageVisible: false,
        // 查看必学课程
        viewCompulsoryCourseVisible: false
      },
      loading: {
        // 页面加载
        pageLoading: false
      },
      // 展示大纲树
      showTree: false
    }

    // 校验规则
    rules = {}

    /**
     * 培训方案类型，1-选课规则，2-自主选课，默认：0
     */
    get schemeType() {
      return CreateSchemeUIModule.schemeType
    }

    /**
     * 创建模式，1-创建，2-复制，3-编辑，默认：0
     */
    get createMode() {
      return CreateSchemeUIModule.createMode
    }

    /**
     * 是否能添加课程
     */
    get disabledAddCourse() {
      return (item: Classification) => {
        return Boolean(item.coursePackageId)
      }
    }

    /**
     * 必学课程总数
     */
    get compulsoryCourseTotal() {
      return (item: Classification) => {
        return CreateSchemeUtils.getOutlineCompulsoryCourseInfo(item).length
      }
    }

    /**
     * 是否要求学时必填
     */
    get requirePeriod() {
      return (item: Classification) => {
        return Boolean(CreateSchemeUtils.getOutlineCompulsoryCourseTotal(item) || 0)
      }
    }

    get hasCourse() {
      return (item: Classification) => {
        return item.courseTotal > 0 ? true : false
      }
    }

    // /**
    //  * 必学课程学时总数
    //  */
    get compulsoryCoursePeriodTotal() {
      return (item: Classification) => {
        return CreateSchemeUtils.getOutlineCompulsoryCoursePeriod(item)
      }
    }

    /**
     * 是否是必学课程 - 有分类
     */
    // get isCompulsoryCourseWithClassification() {
    //   return (index: number, item: SchemeCourseDetailInCoursePackage) => {
    //     const { courseMap } = CreateSchemeUtils.getOutlineCompulsoryCourseInfo(this.outlineInfo.childOutlines[index])
    //     return courseMap.get(item.id) === item.courseCategoryInfo
    //   }
    // }

    /**
     * 是否是必学课程 - 无分类
     */
    get isCompulsoryCourseWithoutClassification() {
      return (node: Classification, item: SchemeCourseDetailInCoursePackage) => {
        const courseId = item.id
        return node.compulsoryCourseIdList.indexOf(courseId) > -1 ? true : false
      }
    }

    /**
     * 课程大纲一级分类是否存在
     */
    get isOutlineWeighty() {
      return CreateSchemeUtils.isWeightyArray(this.outlineInfo.childOutlines)
    }

    /**
     * 选中的课程节点是否是叶子节点
     */
    get isLeafNode() {
      return (index: number) => {
        const length = this.selectedOutlineInfoList[index].currentCourseInfo.childOutlines?.length || 0
        return length === 0 ? true : false
      }
    }

    @Watch('selectedOutlineInfoList', {
      immediate: true,
      deep: true
    })
    selectedOutlineInfoListChange(val: any) {
      console.log('selectedOutlineInfoList', val)
    }

    /**
     * 选中的课程大纲分类节点具体息
     */
    get currentOutlineInfo() {
      return (index: number) => {
        return this.selectedOutlineInfoList[index]?.currentCourseInfo || new Classification()
      }
    }

    /**
     * 是否是必学课程
     */
    get isCompulsoryCourse() {
      return (index: number, item: SchemeCourseDetailInCoursePackage) => {
        const outline = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
          return node.id === this.selectedOutlineInfoList[index].currentCourseInfo.id
        })
        const getIndex = outline.compulsoryCourseIdList.findIndex((ite) => ite === item.id)
        return getIndex === -1 ? false : true
      }
    }

    /**
     * 页面初始化
     */
    async created() {
      await this.refresh()
    }
    async refresh() {
      this.uiConfig.showTree = false
      await this.initConfiguration()
      this.uiConfig.showTree = true
    }
    /**
     * 查询课程列表-有分类
     */
    async queryCourseListHasChildren(target: EditOutlineInfo): Promise<CourseInCoursePackage[]> {
      const result = [] as CourseInCoursePackage[]
      const coursePackageId = target.currentCourseInfo.coursePackageId
      // 如果没有课程包id，直接返回空数组
      if (!coursePackageId) return result
      if (target.currentCourseInfo.courseLoadMode === CourseLoadModeEnum.BY_COURSE_PACKAGE_ID) {
        const respList = await this.queryCoursePackageM.pageQueryCourseListInCoursePackage(
          target.coursePage,
          coursePackageId
        )
        respList?.forEach((item) => {
          item.courseCategoryInfo = this.outlineTreeFindPath((node: Classification) => {
            return node.coursePackageId === coursePackageId
          }, 'name')
          result.push(item)
        })
      }
      if (target.currentCourseInfo.courseLoadMode === CourseLoadModeEnum.BY_OUTLINE_ID) {
        const resList = await this.queryCourseM.queryCourseListInSchemeByOutline(
          target.coursePage,
          [target.currentCourseInfo.idCopy],
          this.schemeDetail.idCopy
        )
        resList?.forEach((item) => {
          const coursePackageName = item.sourceCoursePackageName
          const opt = new CourseInCoursePackage()
          Object.assign(opt, item)
          opt.coursePackageName = coursePackageName
          opt.courseCategoryInfo = this.outlineTreeFindPath((node: Classification) => {
            return node.idCopy === item.outlineId
          }, 'name')
          result.push(opt)
        })
      }
      return result
    }
    /**
     * 查询课程列表-无分类
     */
    async queryCourseList(): Promise<CourseInCoursePackage[]> {
      this.courseQuery.loading = true
      const result = new Array<CourseInCoursePackage>()
      const coursePackageId = this.outlineInfo.coursePackageId
      // 如果没有课程包id，直接返回空数组
      if (!coursePackageId) return result
      let respList = new Array<CourseInCoursePackage>()
      //  加载课程包下课程
      if (this.outlineInfo.courseLoadMode === CourseLoadModeEnum.BY_COURSE_PACKAGE_ID) {
        respList = await this.queryCoursePackageM.pageQueryCourseListInCoursePackage(this.coursePage, coursePackageId)
      }
      if (this.outlineInfo.courseLoadMode === CourseLoadModeEnum.BY_OUTLINE_ID) {
        const resList = await this.queryCourseM.queryCourseListInSchemeByOutline(
          this.coursePage,
          [this.outlineInfo.idCopy],
          this.schemeDetail.idCopy
        )
        resList?.forEach((item) => {
          const coursePackageName = item.sourceCoursePackageName
          const opt = new CourseInCoursePackage()
          Object.assign(opt, item)
          opt.coursePackageName = coursePackageName
          opt.courseCategoryInfo = this.outlineTreeFindPath((node: Classification) => {
            return node.id === item.outlineId
          }, 'name')
          respList.push(opt)
        })
      }

      result.push(...respList)
      this.courseQuery.loading = false
      return result
    }
    /**
     * 滚动查询课程列表-有分类
     */
    async visibleCourseListHasChildren(isVisible: boolean, entry: any, index?: number) {
      const target = this.selectedOutlineInfoList[index]
      const scopeIndex = entry.target.dataset.index
      if (isVisible) {
        if (parseInt(scopeIndex) >= target.coursePage.totalSize) {
          // 最大值时不请求
          return
        }
        if (parseInt(scopeIndex) == target.courseList.length) {
          target.courseQuery.loading = true
          target.coursePage.pageNo++
          const list = await this.queryCourseListHasChildren(target)
          target.courseList = target.courseList.concat(list)
          target.courseQuery.loading = false
        }
      }
    }
    /**
     * 滚动查询课程列表无分类
     */
    async visibleCourseList(isVisible: boolean, entry: any, index?: number) {
      const target = this.outlineInfo
      const scopeIndex = entry.target.dataset.index
      if (isVisible) {
        if (parseInt(scopeIndex) >= this.coursePage.totalSize) {
          // 最大值时不请求
          return
        }
        if (parseInt(scopeIndex) == target.courseList.length) {
          this.coursePage.pageNo++
          const list = await this.queryCourseList()
          target.courseList = target.courseList.concat(list)
        }
      }
    }

    /**
     * 初始化配置
     */
    async initConfiguration() {
      // 编辑或复制
      this.courseStatisticList = await this.queryCourseM.queryCourseStatisticByScheme(this.schemeDetail.idCopy)
      this.courseCompulsoryStatisticList
      await this.restoreOutline()
      this.refreshIsRequirePeriod()
      this.initSelectedOutlineInfoList()
      console.log(this.outlineInfo.childOutlines, this.courseStatisticList, '(this.outlineInfo.childOutlines)')
    }

    /**
     * 还原大纲信息
     */
    async restoreOutline() {
      const childOutlines = this.outlineInfo.childOutlines
      if (CreateSchemeUtils.isWeightyArray(childOutlines)) {
        this.hasChildren = true
      }
      if (this.hasChildren) {
        // 有分类
        CreateOutlineUtils.setOutlineNameRecursion(this.outlineInfo.childOutlines)
        const outlineTreeLeaves = this.outlineTreeFindAllLeaves(this.outlineInfo.childOutlines)
        const reqList =
          outlineTreeLeaves
            ?.map((ite) => {
              return { outlineId: ite.idCopy, courseIdList: ite.compulsoryCourseIdList }
            })
            ?.filter((item) => item.courseIdList?.length > 0) || []
        this.courseCompulsoryStatisticList =
          await this.queryCourseM.allCountPeriodCountOfCourseTrainingOutlineInSchemeInServicer(
            this.schemeDetail.idCopy,
            reqList
          )
        await this.getCourseListWithClassification()
        this.initSelectedOutlineInfoList()
      } else {
        // 无分类
        if (this.outlineInfo.compulsoryCourseIdList?.length)
          this.courseCompulsoryStatisticList =
            await this.queryCourseM.allCountPeriodCountOfCourseTrainingOutlineInSchemeInServicer(
              this.schemeDetail.idCopy,
              [{ outlineId: this.outlineInfo.idCopy, courseIdList: this.outlineInfo.compulsoryCourseIdList }]
            )
        this.outlineInfo.courseLoadMode = CourseLoadModeEnum.BY_OUTLINE_ID
        await this.getCourseListWithoutClassification()
      }
    }

    /**
     * 获取有分类课程列表
     */
    async getCourseListWithClassification() {
      const outlineTreeLeaves = this.outlineTreeFindAllLeaves(this.outlineInfo.childOutlines)
      outlineTreeLeaves.forEach((item) => {
        // 标识节点加载课程的模式
        item.courseLoadMode = CourseLoadModeEnum.BY_OUTLINE_ID
        const courseStatistic =
          this.courseStatisticList.find((ite) => ite.outlineId === item.idCopy) || new CourseStatisticVo()
        item.courseTotal = courseStatistic.courseTotal
        item.coursePeriodTotal = courseStatistic.coursePeriodTotal
        item.compulsoryCoursePeriodTotal = this.courseCompulsoryStatisticList?.get(item.idCopy) || 0
        item.compulsoryCourseTotal = item.compulsoryCourseIdList?.length || 0
        this.refreshOutlineTree(item.parentId)
      })
      this.$emit('updateLearningRequire')
      // await Promise.all(
      //   outlineTreeLeaves?.map(async (el: Classification) => {
      //     await this.updateTreeNode(el.id, el.coursePackageId)
      //   })
      // )
    }

    /**
     * 获取无分类课程列表
     */
    async getCourseListWithoutClassification() {
      const coursePackageId = this.outlineInfo.coursePackageId
      if (!coursePackageId) {
        return
      }
      const targetNode = this.outlineInfo
      targetNode.courseList = [] as SchemeCourseDetailInCoursePackage[]
      targetNode.courseList = await this.queryCourseList()
      const courseStatistic =
        this.courseStatisticList.find((ite) => ite.outlineId === targetNode.idCopy) || new CourseStatisticVo()
      targetNode.courseTotal = courseStatistic.courseTotal
      targetNode.coursePeriodTotal = courseStatistic.coursePeriodTotal
      targetNode.compulsoryCourseTotal = targetNode.compulsoryCourseIdList?.length || 0
      targetNode.compulsoryCoursePeriodTotal = this.courseCompulsoryStatisticList?.get(targetNode.idCopy) || 0
      targetNode.assessSetting.requirePeriod =
        this.courseCompulsoryStatisticList?.get(targetNode.idCopy) || targetNode.assessSetting.requirePeriod
    }

    /**
     * 校验要求学时
     */
    validateRequirePeriod(rules: any, value: any, callback: any, item: Classification) {
      if (!value) {
        callback(new Error('请输入要求学时'))
      }
      if (item && value < item.compulsoryCoursePeriodTotal) {
        callback(new Error('要求完成学时要大于等于必学课程学时，请调整要求学时或必学课程'))
      }
    }

    /**
     * 切换课程展示方式
     */
    handleCourseShowTypeChange(val: boolean) {
      if (val) {
        // 有分类
        this.initHasChildrenOutline()
      } else {
        // 无分类
        this.initNoChildrenOutline()
      }
      // 切换必定触发更新学习要求总控
      this.$emit('updateLearningRequire')
      this.learningExperience.removeAllOutlineCourse()
    }

    /**
     * 初始化有分类课程大纲
     */
    initHasChildrenOutline() {
      this.outlineInfo = new Classification('9999')
      this.outlineInfo.childOutlines = new Array<Classification>()
    }

    /**
     * 初始化无子分类课程大纲
     */
    initNoChildrenOutline() {
      this.outlineInfo = new Classification()
      this.outlineInfo.childOutlines = undefined
    }

    /**
     * 初始化选中课程大纲节点信息列表
     */
    initSelectedOutlineInfoList() {
      this.selectedOutlineInfoList = new Array<EditOutlineInfo>()
      this.outlineInfo.childOutlines?.forEach(async (el, index) => {
        const option = new EditOutlineInfo()
        option.isNameEditable = false
        option.isCourseListVisible = false
        option.currentCourseInfo = new Classification()
        this.selectedOutlineInfoList.push(option)
        await this.handleNodeClick(el, false, index)
      })
    }

    /**
     * 查询符合条件的节点 - 课程大纲树通用
     */
    outlineTreeFind(tree: Array<Classification>, func: any) {
      return CreateSchemeUtils.treeFind<Classification>(tree, func, 'childOutlines')
    }

    /**
     * 查找节点路径 - 课程大纲树通用
     */
    outlineTreeFindPath(func: any, key: string) {
      return CreateSchemeUtils.treeFindPath<Classification>(this.outlineInfo.childOutlines, func, key, 'childOutlines')
    }

    /**
     * 查找所有叶子节点 - 课程大纲树通用
     */
    outlineTreeFindAllLeaves(tree: Array<Classification>) {
      return CreateSchemeUtils.treeFindAllLeaves<Classification>(tree, 'childOutlines')
    }

    /**
     * 是否可操作 - 未勾选课程学习不可操作
     */
    canOperate() {
      let result = true
      if (!this.enableEdit) {
        result = false
        const errMessage =
          this.initMode === 1 ? '请先勾选学习内容模块，再配置对应内容。' : '请先勾选“兴趣课程”，再配置课程包！'
        this.$message.error(errMessage)
      }
      return result
    }

    /**
     * 是否可编辑节点展示名称 - 课程大纲树
     */
    canEditNodeName() {
      let result = true
      const editingNameNode = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
        return node.isEditing === true
      })
      const editingNameItem = this.selectedOutlineInfoList.find((el) => el.isNameEditable === true)
      if (editingNameItem || editingNameNode) {
        this.$message.error('请先保存后再操作！')
        result = false
      }
      return result
    }

    /**
     * 添加课程
     */
    async addCourse(item?: Classification) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      this.selectedOutlinePath = new Array<string>()
      if (item) {
        this.selectedOutlinePath = this.outlineTreeFindPath((node: Classification) => {
          return node.id === item.id
        }, 'id')
      }
      // 还原选择课程包组件
      await this.chooseCoursePackageRef.reset()
      this.uiConfig.dialog.chooseCoursePackageVisible = true
    }

    /**
     * 编辑课程包
     */
    editCoursePackage(item: Classification) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      const targetUrl = `/training/course-package/modify/${item.coursePackageId}`
      UtilClass.openUrl(targetUrl)
    }

    /**
     * 一键移除课程
     */
    removeAllCourse(item: Classification, index?: number) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      if (!item.coursePackageId) {
        const message = this.hasChildren ? '当前分类下无课程， 移除失败' : '当前无课程，移除失败'
        this.$alert(message, '提示', {
          confirmButtonText: '确认',
          type: 'warning',
          callback: (action) => {
            console.log(action)
          }
        })
      } else {
        this.$confirm('一键移除后需重新添加课程，确认要移除？', '提示', {
          confirmButtonText: '确认',
          type: 'warning',
          callback: async (action) => {
            // 'confirm'-确认，'cancel'-取消
            if (action === 'confirm') {
              // 无分类要清空考核对象上的要求学时
              if (!this.hasChildren) {
                item.assessSetting.requirePeriod = 0
              }
              item.coursePackageId = ''
              item.idCopy = ''
              item.courseLoadMode = CourseLoadModeEnum.BY_COURSE_PACKAGE_ID
              item.courseList = new Array<SchemeCourseDetailInCoursePackage>()
              item.courseTotal = 0
              item.coursePeriodTotal = 0
              // 自主选课需调整
              item.compulsoryCourseIdList = [] as string[]
              item.compulsoryCourseInfoList = []
              item.compulsoryCourseTotal = 0
              item.compulsoryCoursePeriodTotal = 0
              this.refreshOutlineTree(item.parentId)
              // 删除移除课程后，校验是否需要清除要求学时
              this.changeRequirePeriod()
              this.$emit('updateLearningRequire')
              if (this.hasChildren) await this.handleNodeClick(item, false, index)
              this.learningExperience.removeOutlineCourse(item.id)
              this.$message.success('操作成功')
            }
          }
        })
      }
    }

    /**
     * 清除要求学时
     */
    changeRequirePeriod() {
      this.outlineInfo.childOutlines?.forEach((item: Classification, index: number) => {
        if (!item.childOutlines?.length && !item.coursePackageId) {
          this.requirePeriodList[index].isRequirePeriod = false
          item.assessSetting.requirePeriod = 0
        }
      })
    }

    /**
     * 更新节点信息 - 课程相关
     * @param {string} nodeId - 末级节点Id
     * @param {string} coursePackageId - 课程包id
     */
    async updateTreeNode(nodeId: string, coursePackageId: string) {
      let targetNode: Classification
      let targetIndex: number
      if (!nodeId) {
        // 无分类
        targetNode = this.outlineInfo
      } else {
        // 有分类
        targetNode = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
          return node.id === nodeId
        })
        const targetPath = this.outlineTreeFindPath((node: Classification) => {
          return node.id === nodeId
        }, 'id')
        targetIndex = this.outlineInfo.childOutlines.findIndex((item) => item.id === targetPath[0])
      }
      targetNode.coursePackageId = coursePackageId
      targetNode.courseLoadMode = CourseLoadModeEnum.BY_COURSE_PACKAGE_ID
      if (SchemeDetailUtils.isLeaf(targetNode)) {
        // 是叶子节点，查询课程包信息并存储
        const coursePackageInfo = await this.queryCoursePackageM.queryCoursePackageById(coursePackageId)
        targetNode.courseTotal = coursePackageInfo.courseCount || 0
        targetNode.coursePeriodTotal = coursePackageInfo.totalPeriod || 0
      }
      if (nodeId) {
        this.refreshOutlineTree(targetNode.parentId)
      }
      this.$emit('updateLearningRequire')
      if (this.hasChildren) {
        // 有分类
        await this.handleNodeClick(targetNode, false, targetIndex)
      } else {
        // 无分类
        this.coursePage.pageNo = 1
        this.outlineInfo.courseList = await this.queryCourseList()
      }
    }

    /**
     * 更新属性信息 - 末级课程列表变化触发
     * @param {string} parentId - 父节点id
     */
    refreshOutlineTree(parentId: string) {
      const parentOutlineNode = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
        return node.id === parentId
      })
      if (!parentOutlineNode) return
      parentOutlineNode.courseTotal =
        parentOutlineNode.childOutlines.reduce((prev, cur) => {
          return CalculatorObj.add(cur.courseTotal || 0, prev)
        }, 0) || 0
      parentOutlineNode.coursePeriodTotal =
        parentOutlineNode.childOutlines.reduce((prev, cur) => {
          return CalculatorObj.add(cur.coursePeriodTotal || 0, prev)
        }, 0) || 0
      // 必学课程学时
      parentOutlineNode.compulsoryCourseTotal =
        parentOutlineNode.childOutlines.reduce((prev, cur) => {
          return CalculatorObj.add(cur.compulsoryCourseTotal, prev)
        }, 0) || 0
      parentOutlineNode.compulsoryCoursePeriodTotal =
        parentOutlineNode.childOutlines.reduce((prev, cur) => {
          return CalculatorObj.add(cur.compulsoryCoursePeriodTotal, prev)
        }, 0) || 0
      this.refreshOutlineTree(parentOutlineNode.parentId)
    }

    /**
     * 必学课程管理
     */
    async popCompulsoryCourseManage(item: Classification) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      if (CreateSchemeUtils.isWeightyArray(item.childOutlines)) {
        this.$message.error('非末级课程大纲分类无法设置必学课程')
        return
      }
      this.compulsoryCourseManageRef.resource = item
      this.compulsoryCourseManageRef.selectedPeriodTotal = item.compulsoryCoursePeriodTotal
      this.compulsoryCourseManageRef.outlineTree = this.outlineInfo.childOutlines
      await this.compulsoryCourseManageRef.setData()
      this.uiConfig.dialog.compulsoryCourseManageVisible = true
    }

    /**
     * 更新节点必学课程Id集合
     */
    handleUpdateNodeCompulsoryCourse(
      nodeId: string,
      idList: string[],
      compulsoryCourseTotalPeriod: number,
      compulsoryCourseInfoList: Array<CompulsoryCourseInfo>
    ) {
      if (!this.hasChildren && !nodeId) {
        // 无分类
        this.outlineInfo.compulsoryCourseIdList = cloneDeep(idList)
        this.outlineInfo.compulsoryCourseInfoList = cloneDeep(compulsoryCourseInfoList)
        this.outlineInfo.compulsoryCoursePeriodTotal = compulsoryCourseTotalPeriod
        this.outlineInfo.compulsoryCourseTotal = idList.length || 0
        this.outlineInfo.assessSetting.requirePeriod = compulsoryCourseTotalPeriod
      } else {
        // 有分类
        const targetNode = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
          return node.id === nodeId
        })
        // 叶子节点
        if (!CreateSchemeUtils.isWeightyArray(targetNode.childOutlines)) {
          targetNode.compulsoryCourseIdList = cloneDeep(idList)
          targetNode.compulsoryCourseInfoList = cloneDeep(compulsoryCourseInfoList)
          targetNode.compulsoryCoursePeriodTotal = compulsoryCourseTotalPeriod
          targetNode.compulsoryCourseTotal = idList.length || 0
          this.refreshOutlineTree(targetNode.parentId)
          this.refreshIsRequirePeriod()
        }
      }
    }

    /**
     * 更新是否需要填写要求学时
     */
    refreshIsRequirePeriod() {
      this.requirePeriodList = [] as RequirePeriod[]
      this.outlineInfo.childOutlines?.forEach((el: Classification) => {
        const option = new RequirePeriod()
        option.isRequirePeriod = Boolean(CreateSchemeUtils.getOutlineCompulsoryCourseTotal(el) || 0)
        this.requirePeriodList.push(option)
      })
    }

    /**
     * 查看必学课程
     */
    popViewCompulsoryCourse() {
      const canOperate = this.canOperate()
      if (!canOperate) return
      this.viewCompulsoryCourseRef.setData()
      this.uiConfig.dialog.viewCompulsoryCourseVisible = true
    }

    /**
     * 获取课程分类信息
     */
    getCourseCategory(row: SchemeCourseDetailInCoursePackage) {
      return row.courseCategoryInfo?.join('>') || ''
    }

    /**
     * 添加分类
     */
    addOutlineTreeNode() {
      const canOperate = this.canOperate()
      if (!canOperate) return
      this.createOutlineInfo = new OutlineInfo()
      this.outlineCategoryOptions = this.getOutlineCategoryOptions()
      this.hasParent = false
      this.uiConfig.dialog.addOutlineVisible = true
    }

    /**
     * 切换是否有上级分类
     */
    handleHasParentChange() {
      this.createOutlineInfo.parentNodePath = [] as string[]
    }

    /**
     * 取消添加分类 - 创建分类弹窗
     */
    cancelAddOutlineTreeNode() {
      this.uiConfig.dialog.addOutlineVisible = false
    }

    /**
     * 保存课程大纲节点 - 创建分类弹窗
     */
    saveOutlineTreeNode() {
      const nodeName = cloneDeep(this.createOutlineInfo.name)
      const parentNodePath = cloneDeep(this.createOutlineInfo.parentNodePath)
      // 节点名称不允许为空
      if (!nodeName) {
        this.$message.error('请输入分类名称')
        return
      }
      if (this.hasParent) {
        // 有上级分类
        if (!parentNodePath || !parentNodePath.length) {
          this.$message.error('请选择上级分类')
          return
        }
        const parentNodeId = parentNodePath[parentNodePath.length - 1]
        const parentNode = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
          return node.id === parentNodeId
        })
        if (parentNode.coursePackageId) {
          this.$message.error('该上级分类已添加课程，需要移除课程后才能添加分类')
          return
        }
        // 同级节点不允许同名
        let validResult = true
        if (parentNode.childOutlines?.length >= 0) {
          const childOutlines = parentNode.childOutlines
          childOutlines.map((el) => {
            if (el.name === nodeName) {
              validResult = false
            }
          })
        }
        if (!validResult) {
          this.$message.error('同级分类不允许重名，请调整分类名称')
          return
        }
        const option = new Classification()
        option.id = Classification.classificationIdPre + '_' + Mockjs.Random.guid()
        option.parentId = parentNode.id
        option.name = nodeName
        option.courseTotal = 0
        option.coursePeriodTotal = 0
        option.compulsoryCourseTotal = 0
        option.compulsoryCoursePeriodTotal = 0
        option.compulsoryCourseInfoList = []
        option.courseLoadMode = CourseLoadModeEnum.BY_COURSE_PACKAGE_ID
        option.childOutlines = undefined
        if (!parentNode.childOutlines) {
          parentNode.childOutlines = new Array<Classification>()
        }
        parentNode.childOutlines.push(option)
        parentNode.childOutlines.map((el, index) => {
          el.sort = index
        })
        this.uiConfig.dialog.addOutlineVisible = false
      } else {
        // 无上级分类
        // 1.同级节点不允许同名
        let validResult = true
        const childOutlines = this.outlineInfo.childOutlines
        if (childOutlines.length) {
          childOutlines.map((el) => {
            if (el.name === nodeName) {
              validResult = false
            }
          })
        }
        if (!validResult) {
          this.$message.error('同级分类不允许重名，请调整分类名称')
          return
        }
        const option = new Classification()
        option.id = Classification.classificationIdPre + '_' + Mockjs.Random.guid()
        option.parentId = undefined
        option.name = nodeName
        option.courseTotal = 0
        option.coursePeriodTotal = 0
        option.childOutlines = undefined
        if (!this.outlineInfo.childOutlines) {
          this.outlineInfo.childOutlines = new Array<Classification>()
        }
        const length = this.outlineInfo.childOutlines?.length
        // this.outlineInfo.childOutlines.push(option)
        this.$set(this.outlineInfo.childOutlines, length, option)
        this.refreshIsRequirePeriod()
        this.initSelectedOutlineInfoList()
        this.uiConfig.dialog.addOutlineVisible = false
      }
    }

    @Watch('hasParent', {
      immediate: true,
      deep: true
    })
    hasParentChange(val: any) {
      console.log('hasParent', val)
    }

    /**
     * 获取课程大纲分类选项 - 二级之后的子节点都不展示
     */
    getOutlineCategoryOptions() {
      const tree = cloneDeep(this.outlineInfo.childOutlines)
      tree?.map((el: Classification) => {
        if (el.childOutlines && el.childOutlines.length) {
          el.childOutlines.map((subEl: Classification) => {
            subEl.childOutlines = undefined
          })
        }
      })
      return tree
    }
    /**
     * 编辑 - 一级课程大纲展示名称
     */
    editTopLevelOutlineName(node: Classification, index: number) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      const canEditNodeName = this.canEditNodeName()
      if (!canEditNodeName) return
      this.currentOutlineValue = cloneDeep(node.name)
      this.selectedOutlineInfoList[index].isNameEditable = true
    }

    /**
     * 确认编辑 - 一级课程大纲展示名称
     */
    confirmTopLevelOutlineName(node: Classification, index: number) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      if (!this.currentOutlineValue) {
        this.$message.error('分类名称不能为空！')
        return
      }
      const isRepeat = CreateOutlineUtils.validNodeNameIsRepeatInTheSameLevel(
        this.outlineInfo,
        node,
        this.currentOutlineValue
      )
      if (isRepeat) {
        this.$message.error('同级分类不允许重名，请调整分类名称')
        return
      } else {
        node.name = cloneDeep(this.currentOutlineValue)
        this.selectedOutlineInfoList[index].isNameEditable = false
      }
    }

    /**
     * 取消编辑 - 一级课程大纲展示名称
     */
    cancelEditTopLevelOutlineName(index: number) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      this.selectedOutlineInfoList[index].isNameEditable = false
    }

    /**
     * 切换要求学时校验
     */
    handleRequirePeriod(index: number, value: boolean) {
      if (!value) {
        if (this.outlineInfo.childOutlines[index]) {
          this.outlineInfo.childOutlines[index].assessSetting.requirePeriod = 0
        }
      }
    }

    /**
     * 末级分类是否有课程 是否可保存
     */
    validateEnableSave(): boolean {
      let result = false
      const nodeList = this.outlineInfo.childOutlines
      const leafNodeList =
        this.outlineTreeFindAllLeaves(nodeList).filter((el: Classification) => {
          return !el.coursePackageId
        }) || ([] as Classification[])
      // 没有叶子节点说明节点都挂载了课程
      if (!CreateSchemeUtils.isWeightyArray(leafNodeList)) {
        result = true
      }
      return result
    }

    /**
     * 表单校验 - 自主选课模式下校验要求学时是否达标
     */
    validateForm() {
      let finalValid = true
      try {
        const outlineInfo = this.outlineInfo
        if (this.hasChildren) {
          /** 有分类*/
          // 1.选择有分类但没有添加分类和课程
          if (!CreateSchemeUtils.isWeightyArray(this.outlineInfo.childOutlines)) {
            this.$message.error('请配置课程学习内容')
            throw new Error('中断函数执行')
          }
          // 2.校验一级分类学时
          const that = this as any
          this.outlineInfo.childOutlines?.forEach((el: Classification, index: number) => {
            const ref = `requirePeriodForm${index}`
            const form: any = that.$refs[ref][0]
            form.validate((valid: any) => {
              if (valid) {
                //
              } else {
                throw new Error('中断函数执行')
              }
            })
          })
          //   3.末级分类下无课程拦截
          const enableSave = this.validateEnableSave()
          if (!enableSave) {
            this.$message.error('末级分类下课程不能为空，请调整')
            throw new Error('中断函数执行')
          }
        } else {
          /** 无分类*/
          if (!outlineInfo.coursePackageId) {
            this.$message.error('请配置课程学习内容')
            throw new Error('中断函数执行')
          }
        }
      } catch (e) {
        finalValid = false
      }
      return finalValid
    }

    /**
     * 重置
     */
    resetClassification() {
      this.hasChildren = false
      this.initNoChildrenOutline()
    }

    // 分割线：以下都是elTree内操作
    /**
     * 课程大纲节点点击响应事件
     */
    async handleNodeClick(data: Classification, cancelLoad = true, index?: number) {
      // 判断前后节点是否重复，重复则不变化
      if (!this.selectedOutlineInfoList[index]) return
      const sourceId = this.selectedOutlineInfoList[index].currentCourseInfo.id
      const targetId = data.id
      this.selectedOutlineInfoList[index].isCourseListVisible = true
      if (cancelLoad && sourceId === targetId) return
      const target = this.selectedOutlineInfoList[index]
      // 开始加载
      target.contentLoading = true
      target.currentCourseInfo = new Classification()
      target.currentCourseInfo = data
      target.coursePage = new UiPage()
      if (this.isLeafNode(index)) {
        // 叶子节点：重新请求列表
        target.courseList = await this.queryCourseListHasChildren(target)
      } else {
        // 非叶子节点：清空课程列表
        target.courseList = [] as CourseInCoursePackage[]
      }
      // 切换节点后，表格右侧滚动条自动滚到顶部
      this.$nextTick(() => {
        const ele = this.$refs[`courseTableRef_${index}`]
          ? (this.$refs[`courseTableRef_${index}`][0] as any)
          : undefined
        const bodyWrapper = ele?.bodyWrapper as any
        if (bodyWrapper) bodyWrapper.scrollTop = 0
      })
      target.contentLoading = false
    }

    /**
     * 响应节点拖拽事件
     */
    handleDrop(
      draggingNode: TreeNode<any, Classification>,
      dropNode: TreeNode<any, Classification>,
      dropType: 'prev' | 'inner' | 'next',
      evt: any
    ) {
      const temp = draggingNode.data.sort
      draggingNode.data.sort = dropNode.data.sort
      dropNode.data.sort = temp
    }

    /**
     * 节点结束拖拽触发事件
     */
    allowDrop(draggingNode: TreeNode<any, any>, dropNode: TreeNode<any, any>, type: 'prev' | 'inner' | 'next') {
      if (draggingNode.data.level === dropNode.data.level) {
        if (draggingNode.data.parentId === dropNode.data.parentId) {
          return type === 'prev' || type === 'next'
        } else {
          return false
        }
      } else {
        return false
      }
    }

    /**
     * 节点开始拖拽触发事件
     */
    allowDrag(draggingNode: TreeNode<any, any>) {
      const canOperate = this.canOperate()
      if (!canOperate) return false
      const canEditNodeName = this.canEditNodeName()
      if (!canEditNodeName) return false
      return draggingNode.level !== 1
    }

    /**
     * 编辑节点展示名称
     */
    editTreeNodeName(data: Classification) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      const canEditNodeName = this.canEditNodeName()
      if (!canEditNodeName) return
      data.isEditing = true
      this.currentOutlineValue = cloneDeep(data.name)
    }

    /**
     * 确认编辑节点展示名称
     */
    confirmEditTreeNodeName(data: Classification) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      if (!this.currentOutlineValue) {
        this.$message.error('分类名称不能为空！')
        return
      }
      const isRepeat = CreateOutlineUtils.validNodeNameIsRepeatInTheSameLevel(
        this.outlineInfo,
        data,
        this.currentOutlineValue
      )
      if (isRepeat) {
        this.$message.error('同级分类不允许重名，请调整分类名称')
        return
      } else {
        data.name = cloneDeep(this.currentOutlineValue)
        data.isEditing = false
      }
    }

    /**
     * 取消编辑节点展示名称
     */
    cancelEditTreeNodeName(data: Classification) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      data.isEditing = false
    }

    /**
     * 移除节点
     */
    removeTreeNode(treeNode: TreeNode<any, Classification>) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      const canEditNodeName = this.canEditNodeName()
      if (!canEditNodeName) return
      const targetTree = [treeNode.data]
      const hasCourseNode = this.outlineTreeFind(targetTree, (node: Classification) => {
        return node.coursePackageId
      })
      if (hasCourseNode) {
        this.$alert('本分类下已经添加课程，无法删除。请先移除课程后再删除分类。', '提示', {
          confirmButtonText: '确认',
          type: 'warning',
          callback: (action) => {
            console.log(action)
          }
        })
        return
      }
      this.$confirm('删除后，需要重新添加分类。确定要删除分类？', '提示', {
        confirmButtonText: '确认',
        type: 'warning',
        callback: (action) => {
          console.log(action)
          // 'confirm'-确认，'cancel'-取消
          if (action === 'confirm') {
            if (!treeNode.data.parentId) {
              // 说明是一级节点
              const index = this.outlineInfo.childOutlines.findIndex((el) => el.id === treeNode.data.id)
              if (index > -1) {
                this.outlineInfo.childOutlines.splice(index, 1)
                this.refreshIsRequirePeriod()
              }
            } else {
              // 找到父级
              const parentOutline = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
                return node.id === treeNode.data.parentId
              })
              // 删除对应子节点
              const deleteSortIndex = treeNode.data.sort
              parentOutline.childOutlines.splice(deleteSortIndex, 1)
              if (parentOutline.childOutlines.length) {
                // 调整同级排序序号
                parentOutline.childOutlines?.map((el, index) => {
                  el.sort = index
                })
              } else {
                parentOutline.childOutlines = undefined
              }
            }
          }
        }
      })
    }
    // 分割线：以上都是elTree内操作
  }
</script>

<style scoped>
  .pure {
    margin: 0;
    padding: 0;
  }
  .radio-group {
    height: 36px;
  }

  .radio-group .radio {
    line-height: 36px;
  }
  .select-right {
    margin-left: 20px;
    max-width: 200px;
  }
</style>
