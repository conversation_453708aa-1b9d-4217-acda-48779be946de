<route-meta>
{
"title": "培训专业选择器"
}
</route-meta>
<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    filterable
    clearable
  >
    <el-option
      v-for="item in trainingMajorOptions"
      :label="showLabel(item)"
      :value="item.propertyId"
      :key="item.propertyId"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import TrainingMajorVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingMajorVo'
  import QueryTrainingMajor from '@api/service/common/basic-data-dictionary/query/QueryTrainingMajor'
  import MajorParam from '@api/service/common/basic-data-dictionary/query/vo/majorParam'

  @Component
  export default class extends Vue {
    selected = ''
    // 选项
    trainingMajorOptions: Array<TrainingMajorVo> = new Array<TrainingMajorVo>()

    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Prop({
      type: String,
      default: '请选培训专业'
    })
    placeholder: string

    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string //行业属性id

    @Prop({
      type: String,
      default: ''
    })
    trainingCategoryId: string // 培训类别id

    @Watch('industryPropertyId', {
      immediate: true,
      deep: true
    })
    async industryPropertyIdChange() {
      await this.getTrainingMajorOptions()
    }

    @Watch('trainingCategoryId', {
      immediate: true,
      deep: true
    })
    async trainingCategoryIdChange() {
      await this.getTrainingMajorOptions()
    }

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }

    /**
     * 获取展示名称
     */
    get showLabel() {
      return (item: TrainingMajorVo) => {
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    /**
     * 获取培训专业数据
     */
    async getTrainingMajorOptions() {
      if (!this.industryPropertyId || !this.trainingCategoryId) {
        this.trainingMajorOptions = []
        return
      }
      const params: MajorParam = {
        industryPropertyId: this.industryPropertyId,
        parentPropertyId: this.trainingCategoryId
      }
      const res = await QueryTrainingMajor.queryTrainingMajor(params)
      this.trainingMajorOptions = res.isSuccess() ? QueryTrainingMajor.trainingMajorList : ([] as TrainingMajorVo[])
    }
  }
</script>
