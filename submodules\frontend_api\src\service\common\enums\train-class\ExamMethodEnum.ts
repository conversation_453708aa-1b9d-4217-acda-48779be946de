import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 考试方式枚举
 */
export enum ExamMethodEnum {
  /**
   * 随到随考
   */
  on_call_exam = 0,
  /**
   * 固定考试
   */
  fixed_exam
}
/**
 * @description 考试方式状态枚举
 */
class ExamMethodType extends AbstractEnum<ExamMethodEnum> {
  static enum = ExamMethodEnum
  constructor(status?: ExamMethodEnum) {
    super()
    this.current = status
    this.map.set(ExamMethodEnum.on_call_exam, '随到随考')
    this.map.set(ExamMethodEnum.fixed_exam, '固定考试')
  }
}

export default new ExamMethodType()
