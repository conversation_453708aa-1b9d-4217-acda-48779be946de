import { Response } from '@hbfe/common'
import LibraryRequestDto from './vo/LibraryRequestVo'
import MsExamQueryBackStageGateway, {
  LibraryResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import LibraryResponseVo from './vo/LibraryResponseVo'
import MsBasicDataQueryBackGateway, {
  AdminQueryRequest,
  AdminUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import LibraryQuestionCountResponseVo from './vo/LibraryQuestionCountResponseVo'
import LibraryQuestionCountRequestDto from './vo/LibraryQuestionCountRequestVo'
import { UiPage } from '@hbfe/common'
import LibraryRequestVo from './vo/LibraryRequestVo'

class QueryQuestionLibrary {
  pageQueryParam = new LibraryRequestDto()
  libraryQuestionCountRequest = new LibraryQuestionCountRequestDto()

  /**
   * @description: 查询题库分页
   * @param {*}
   * @return {*}
   */
  async queryQuestionBankLibrary(
    page: UiPage,
    queryQuestionBankLibraryVo: LibraryRequestVo
  ): Promise<Response<Array<LibraryResponseVo>>> {
    const result = await MsExamQueryBackStageGateway.pageLibraryInServicer({
      page: page,
      request: queryQuestionBankLibraryVo
    })
    const response = new Response<Array<LibraryResponseVo>>()
    if (!result?.status?.isSuccess()) {
      response.status = result.status
      return response
    }
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize

    // 创建人id集合
    const createUserIdList = new Array<string>()
    // 题库id集合
    const libraryIdList = new Array<string>()

    // 题库列表
    const resultList = new Array<LibraryResponseVo>()
    result.data?.currentPageData?.forEach((item: LibraryResponse) => {
      const libraryItem = LibraryResponseVo.from(item)
      createUserIdList.push(item.createUserId)
      libraryIdList.push(item.libraryId)
      resultList.push(libraryItem)
    })
    response.data = resultList
    // 查询已启用的试题数量
    if (libraryIdList?.length) {
      const queryQuestionCountResponse = await this.queryCountQuestionInServicer(libraryIdList, true)
      if (!queryQuestionCountResponse?.status?.isSuccess()) {
        response.status = queryQuestionCountResponse.status
        return response
      }
      resultList?.forEach((item: LibraryResponseVo) => {
        queryQuestionCountResponse?.data?.forEach((el: LibraryQuestionCountResponseVo) => {
          if (item.id === el.libraryId) {
            item.enabledQuestionCount = el.questionCount
          }
        })
      })
      /* 查询所有试题数量 */
      const allQuestionResponse = await this.queryCountQuestionInServicer(libraryIdList)
      if (!allQuestionResponse.status?.isSuccess()) {
        response.status = allQuestionResponse.status
        return response
      }
      resultList?.forEach((item: LibraryResponseVo) => {
        allQuestionResponse?.data?.forEach((el: LibraryQuestionCountResponseVo) => {
          if (item.id === el.libraryId) {
            item.questionCount = el.questionCount
          }
        })
      })
    }

    //  查询创建人
    const queryCreateUserNameResponse = await this.queryCreateUserNames(page, createUserIdList)
    if (!queryCreateUserNameResponse?.status?.isSuccess()) {
      response.status = queryCreateUserNameResponse.status
      return response
    }
    resultList?.forEach((item: LibraryResponseVo) => {
      item.createUserName = queryCreateUserNameResponse.data.find(res => res.id === item.createUserId)?.name
    })

    response.data = resultList
    response.status = result.status
    return response
  }

  /**
   * @description: 查询题库分页
   * @param {*}
   * @return {*}
   */
  async queryQuestionBankLibraryNode(
    page: UiPage,
    queryQuestionBankLibraryVo: LibraryRequestVo
  ): Promise<Response<Array<LibraryResponseVo>>> {
    const result = await MsExamQueryBackStageGateway.pageLibraryInServicer({
      page: page,
      request: queryQuestionBankLibraryVo
    })
    const questionList = result?.data?.currentPageData
    if (result?.data?.totalPageSize && result?.data?.totalPageSize > 1) {
      for (let i = 2; i <= result?.data?.totalPageSize; i++) {
        page.pageNo = i
        const temp = await MsExamQueryBackStageGateway.pageLibraryInServicer({
          page: page,
          request: queryQuestionBankLibraryVo
        })
        if (temp?.data?.currentPageData?.length) {
          questionList.push(...temp?.data?.currentPageData)
        }
      }
    }
    const response = new Response<Array<LibraryResponseVo>>()
    if (!result?.status?.isSuccess()) {
      response.status = result.status
      return response
    }
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize

    // 创建人id集合
    const createUserIdList = new Array<string>()
    // 题库id集合
    const libraryIdList = new Array<string>()

    // 题库列表
    const resultList = new Array<LibraryResponseVo>()
    questionList?.forEach((item: LibraryResponse) => {
      const libraryItem = LibraryResponseVo.from(item)
      createUserIdList.push(item.createUserId)
      libraryIdList.push(item.libraryId)
      resultList.push(libraryItem)
    })
    response.data = resultList
    response.data = resultList
    response.status = result.status
    return response
  }

  /**
   * @description: 查询题库详情
   * @param {string} libraryId 题库id
   * @return {*}
   */
  async queryQuestionBankLibraryDetail(libraryId: string): Promise<Response<LibraryResponseVo>> {
    const res = await MsExamQueryBackStageGateway.getLibraryInServicer(libraryId)
    const response = new Response<LibraryResponseVo>()
    response.data = new LibraryResponseVo()
    if (!res?.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.data = LibraryResponseVo.from(res?.data)
    const libraryIdList = new Array<string>()
    libraryIdList.push(res.data?.libraryId)

    // 获取已启用试题数
    const questionCount = await this.queryCountQuestionInServicer(libraryIdList, true)
    if (!questionCount?.status?.isSuccess()) {
      response.status = questionCount.status
      return response
    }
    questionCount.data?.forEach(item => {
      response.data.enabledQuestionCount = item.questionCount
    })

    return response
  }

  /**
   * @description: 根据父级id获取子分类
   * @param {UiPage} page
   * @param {string} parentLibraryId 父级题库id
   */
  async queryQuestionBankLibraryItem(
    page: UiPage,
    parentLibraryId: string
  ): Promise<Response<Array<LibraryResponseVo>>> {
    const queryParams = new LibraryRequestVo()
    queryParams.parentLibraryId = parentLibraryId
    const result = await MsExamQueryBackStageGateway.pageLibraryInServicer({
      page: page,
      request: queryParams
    })
    const response = new Response<Array<LibraryResponseVo>>()
    if (!result?.status?.isSuccess()) {
      response.status = result.status
      return response
    }
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize

    const resultList = new Array<LibraryResponseVo>()
    // 子级题库id集合
    const subLibraryIds = new Array<string>()
    result.data?.currentPageData?.forEach((item: LibraryResponse) => {
      const libraryItem = LibraryResponseVo.from(item)
      subLibraryIds.push(item.libraryId)
      resultList.push(libraryItem)
    })

    /* 查询所有试题数量 */
    const allQuestionResponse = await this.queryCountQuestionInServicer(subLibraryIds)
    if (!allQuestionResponse.status?.isSuccess()) {
      response.status = allQuestionResponse.status
      return response
    }
    resultList?.forEach((item: LibraryResponseVo) => {
      allQuestionResponse?.data?.forEach((el: LibraryQuestionCountResponseVo) => {
        if (item.id === el.libraryId) {
          item.questionCount = el.questionCount
        }
      })
    })

    response.data = resultList
    response.status = result.status
    return response
  }

  /**
   * @description: 根据题库id集合以及试题类型 查询试题数量
   * @param {Array} idList 题库id集合
   * @param {number} questionType 试题类型
   * @param {boolean} enable 默认启用
   */
  async queryCountQuestionByType(
    idList: Array<string>,
    questionType: number,
    enable = true
  ): Promise<Response<Array<LibraryQuestionCountResponseVo>>> {
    const params = new LibraryQuestionCountRequestDto()
    params.libraryIdList = [...new Set(idList)]
    params.questionType = questionType
    params.enable = enable

    const res = await MsExamQueryBackStageGateway.getQuestionCountInServicer(params)
    const response = new Response<Array<LibraryQuestionCountResponseVo>>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.data = res.data
    response.status = res.status
    return response
  }

  /**
   * @description: 根据题库id集合查询试题数量
   * @param {Array} idList 题库id集合
   */
  async queryCountQuestionInServicer(
    idList: Array<string>,
    enable?: boolean
  ): Promise<Response<Array<LibraryQuestionCountResponseVo>>> {
    const params = new LibraryQuestionCountRequestDto()
    params.libraryIdList = [...new Set(idList)]
    params.enable = enable
    const res = await MsExamQueryBackStageGateway.getQuestionCountInServicer(params)
    return res
  }

  /**
   * @description: 根据创建人id集合查找创建人名称
   * @param {UiPage} page
   * @param {Array} idList
   * @return {*}
   */
  private async queryCreateUserNames(page: UiPage, idList: Array<string>) {
    const params = new AdminQueryRequest()
    params.user = new AdminUserRequest()
    params.user.userIdList = [...new Set(idList)]
    const res = await MsBasicDataQueryBackGateway.pageAdminInfoInServicer({
      page: page,
      request: params
    })
    const response = new Response<Array<{ name: string; id: string }>>()
    if (!res?.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    // 创建人名称数组
    const adminUserNameList = res?.data?.currentPageData?.map(item => {
      return {
        id: item.userInfo?.userId,
        name: item.userInfo?.userName
      }
    })
    response.status = res.status
    response.data = adminUserNameList
    return response
  }
}

export default QueryQuestionLibrary
