import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 换期状态枚举
 * apply 发起换期
 * processing 换期处理中
 * fail 换期失败
 * complete 换期成功
 */
export enum ExchangeIssueStatusEnum {
  apply = 'apply',
  processing = 'processing',
  fail = 'fail',
  complete = 'complete'
}

/**
 * @description 换期状态
 */
class ExchangeIssueStatus extends AbstractEnum<ExchangeIssueStatusEnum> {
  static enum = ExchangeIssueStatusEnum

  constructor(status?: ExchangeIssueStatusEnum) {
    super()
    this.current = status
    this.map.set(ExchangeIssueStatusEnum.apply, '发起换期')
    this.map.set(ExchangeIssueStatusEnum.processing, '换期处理中')
    this.map.set(ExchangeIssueStatusEnum.fail, '换期失败')
    this.map.set(ExchangeIssueStatusEnum.complete, '换期成功')
  }
}

export default new ExchangeIssueStatus()
