/**
 * 二级选修选课最大选课学时信息
 */
export class SecondElectiveMaxPeriod {
  /**
   * 分类Id
   */
  id: string = undefined
  /**
   * 最大选课学时
   */
  electiveMaxPeriod: number = undefined
}

/**
 * @description 选课规则详情
 */
class ChooseCourseRule {
  /**
   * 选课规则id
   */
  id = ''
  /**
   * 选课规则名称
   */
  name = ''
  /**
   * 必修要求学时
   */
  compulsoryPeriod = 0
  /**
   * 选修课最大允许学时
   */
  electiveMaxPeriod = 0
  /**
   * 选项课二级分类学时要求
   */
  secondElectiveMaxPeriod: SecondElectiveMaxPeriod[] = []
  /**
   * 是否允许最后一门超出最大学时
   */
  allowLastChooseOver = false
  /**
   * 是否约束重复选课
   */
  constrainedRepeatSelection = false
  /**
   * 约束重复选课范围
   */
  constrainedRangeKeyList: string[] = []
}

export default ChooseCourseRule
