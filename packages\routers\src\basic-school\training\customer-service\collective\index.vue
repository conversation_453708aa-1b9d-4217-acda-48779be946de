<route-meta>
{
"isMenu": true,
"title": "集体报名咨询",
"sort": 2,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>
<script lang="ts">
  import Collective from '@hbfe/jxjy-admin-customerService/src/collective/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, FXS, GYS } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY, FXS, GYS],
    basicData: [WXGLY, FXS, GYS],
    invoiceInfo: [WXGLY, FXS, GYS],
    refundInfo: [WXGLY, FXS, GYS],
    studentList: [WXGLY, FXS, GYS],
    batchInfo: [WXGLY, FXS, GYS]
  })
  export default class extends Collective {}
</script>
