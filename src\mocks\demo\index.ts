import Mock from 'mockjs'
import Demo from '@/mocks/demo/models/Demo'

const list = () => {
  return new Promise<Demo>(resolve => {
    const mocked = Mock.mock({
      'data|10': [
        {
          id: '@uuid',
          name: '@last @first',
          sex: '@integer(0, 1)',
          email: '@email',
          phone: '@integer(0, 11)'
        }
      ]
    })
    resolve(mocked.data)
  })
}

/**
 * 根据 id 获取详情
 * @param id
 */
const getById = (id: string) => {
  // todo
  console.log(id)
}

/**
 * 删除
 * @param id
 */
const remove = (id: string) => {
  // todo
  console.log(id)
}

/**
 * 批量删除
 * @param idList
 */

const batchRemove = (idList: Array<string>) => {
  // todo
  console.log(idList)
}

/**
 * 修改
 * @param demo
 */
const update = (demo: Demo) => {
  // todo
  console.log(demo)
}

/**
 * 创建
 * @param demo
 */
const create = (demo: Demo) => {
  // todo
  console.log(demo)
}

export { list, getById, remove, update, create, batchRemove }
