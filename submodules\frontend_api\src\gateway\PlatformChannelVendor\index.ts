import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformChannelVendor'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformChannelVendor'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum ServicerContractStatusEnums {
  ALL = 'ALL',
  NORMAL = 'NORMAL',
  SUSPEND = 'SUSPEND'
}
export enum ServicerTypeEnums {
  ALL = 'ALL',
  TRAINING_INSTITUTION = 'TRAINING_INSTITUTION',
  COURSEWARE_SUPPLIER = 'COURSEWARE_SUPPLIER',
  CHANNEL_VENDOR = 'CHANNEL_VENDOR',
  PARTICIPATING_UNIT = 'PARTICIPATING_UNIT'
}
export enum ServicerContractStatusEnum {
  NORMAL = 'NORMAL',
  SUSPEND = 'SUSPEND'
}

// 类

/**
 * 申请成为渠道商
<AUTHOR>
@version 1.0
@date 2021/7/17 18:14
 */
export class ApplyChannelVendorApplyParams {
  /**
   * 申请的机构id
   */
  trainingInstitutionId?: string
  /**
   * 申请人
   */
  applierName: string
  /**
   * 手机号
   */
  phoneNumber: string
  /**
   * 渠道商名称
   */
  channelVendorName: string
  /**
   * 所在地区路径
   */
  areaPath?: string
  /**
   * 推广优势说明
   */
  explain?: string
}

/**
 * 渠道商申请记录查询条件
<AUTHOR>
@version 1.0
@date 2021/7/17 20:14
 */
export class ChannelVendorApplyRecordQueryParams {
  /**
   * 申请人
   */
  applierName?: string
  /**
   * 手机号
   */
  phoneNumber?: string
  /**
   * 渠道商名称
   */
  channelVendorName?: string
  /**
   * 所在地区路径
   */
  areaPathList?: Array<string>
  /**
   * 申请单处理状态
1：申请中，2：已同意，3：已拒绝
   */
  auditStatus?: number
  /**
   * 申请时间
   */
  applyTime?: TimeRegionRequest
  /**
   * 审批时间
   */
  auditTime?: TimeRegionRequest
  /**
   * 合作状态
   */
  status?: ServicerContractStatusEnums
}

/**
 * 与当前培训机构角色签约的渠道商查询条件
<AUTHOR>
@since 2021/11/1
 */
export class ChannelVendorForTInstitutionQueryParams {
  /**
   * 名称
   */
  name?: string
  /**
   * 渠道地区
   */
  regionPath?: Array<string>
  /**
   * 合作签约开始时间
   */
  beginTime?: string
  /**
   * 合作签约结束时间
   */
  endTime?: string
  /**
   * 合作状态
   */
  contractStatus?: ServicerContractStatusEnums
  /**
   * 手机号
   */
  phone?: string
}

/**
 * 渠道商查询信息
 */
export class ChannelVendorQueryParams {
  /**
   * 培训机构Id
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 渠道商名称
   */
  name?: string
  /**
   * 合作状态
   */
  status?: ServicerContractStatusEnums
}

/**
 * 时间范围
<AUTHOR>
@date 2020/5/3116:42
 */
export class TimeRegionRequest {
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 结束时间
   */
  endTime?: string
}

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * graphql没有泛型的操作结果类
<AUTHOR> create 2020/3/9 15:18
 */
export class GraphqlOperateResult {
  /**
   * 返回的code
   */
  code: string
  /**
   * 返回的message
   */
  message: string
  /**
   * json字段
存放返回的参数用
   */
  expandData: Map<string, string>
}

/**
 * 渠道商申请记录
<AUTHOR>
@version 1.0
@date 2021/7/17 20:13
 */
export class ChannelVendorApplyRecordDto {
  /**
   * 申请记录id
   */
  applyId: string
  /**
   * 申请的机构id
   */
  trainingInstitutionId: string
  /**
   * 申请人
   */
  applierName: string
  /**
   * 手机号
   */
  phoneNumber: string
  /**
   * 渠道商名称
   */
  channelVendorName: string
  /**
   * 所在地区路径
   */
  areaPath: string
  /**
   * 推广优势说明
   */
  explain: string
  /**
   * 审批人id
   */
  auditor: string
  /**
   * 申请单处理状态
1：申请中，2：已同意，3：已拒绝
   */
  auditStatus: number
  /**
   * 申请时间
   */
  applyTime: string
  /**
   * 审批时间
   */
  auditTime: string
  /**
   * 渠道商id
   */
  channelVendorId: string
  /**
   * 渠道商账户id
   */
  channelVendorAccountId: string
  /**
   * 渠道商企业账号
   */
  channelVendorLoginInput: string
  /**
   * 合作状态
   */
  status: ServicerContractStatusEnums
}

/**
 * 渠道商列表信息
 */
export class ChannelVendorForTIDto {
  /**
   * Id
   */
  id: string
  /**
   * 渠道商名称
   */
  name: string
  /**
   * 地区名称列表，省市区县...
   */
  regionNames: Array<string>
  /**
   * 所在地区路径
   */
  regionPath: string
  /**
   * 负责人
   */
  contactPerson: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 合作状态
   */
  status: ServicerContractStatusEnums
  /**
   * 签约时间
   */
  contractTime: string
}

/**
 * 渠道商列表信息
 */
export class ChannelVendorListDto {
  /**
   * Id
   */
  id: string
  /**
   * 渠道商名称
   */
  name: string
  /**
   * 地区名称列表，省市区县...
   */
  regionNames: Array<string>
  /**
   * 所在地区路径
   */
  regionPath: string
  /**
   * 负责人
   */
  contactPerson: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 合作状态
   */
  status: ServicerContractStatusEnums
}

/**
 * 账号信息
 */
export class AccountDto {
  /**
   * 帐号
   */
  loginAccount: string
  /**
   * 姓名
   */
  name: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人
   */
  creator: string
}

/**
 * 有合约的服务商信息
 */
export class ServicerContractDto {
  /**
   * 服务商 Id
   */
  id: string
  /**
   * 服务商类型
   */
  servicerType: ServicerTypeEnums
  /**
   * 名称
   */
  name: string
  /**
   * 合作状态
   */
  status: ServicerContractStatusEnums
  /**
   * 服务商合作日志
   */
  contractLogList: Array<ServicerContractLogDto>
}

/**
 * 服务商签约日志返回值
 */
export class ServicerContractLogDto {
  /**
   * 合作状态
   */
  contractLogStatus: ServicerContractStatusEnum
  /**
   * 操作时间
   */
  operationTime: string
  /**
   * 操作人
   */
  operationUserName: string
}

/**
 * 服务详情信息（渠道商、课件供应商）
 */
export class ServicerDetailDto {
  /**
   * Id
   */
  id: string
  /**
   * 课件供应商名称
   */
  name: string
  /**
   * 地区名称列表，省市区县...
   */
  regionNames: Array<string>
  /**
   * 所在地区路径
   */
  regionPath: string
  /**
   * 负责人
   */
  contactPerson: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 课件供应商优势简述
   */
  abouts: string
  /**
   * 合作状态
   */
  status: ServicerContractStatusEnums
  /**
   * 有合约的服务商信息
   */
  servicerContracts: Array<ServicerContractDto>
  /**
   * 账号信息
   */
  accounts: Array<AccountDto>
}

export class ChannelVendorListDtoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ChannelVendorListDto>
}

export class ChannelVendorApplyRecordDtoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ChannelVendorApplyRecordDto>
}

export class ChannelVendorForTIDtoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ChannelVendorForTIDto>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 渠道商详情
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async detail(
    id: string,
    query: DocumentNode = GraphqlImporter.detail,
    operation?: string
  ): Promise<Response<ServicerDetailDto>> {
    return commonRequestApi<ServicerDetailDto>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 渠道商分页
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async page(
    params: { page?: Page; params?: ChannelVendorQueryParams },
    query: DocumentNode = GraphqlImporter.page,
    operation?: string
  ): Promise<Response<ChannelVendorListDtoPage>> {
    return commonRequestApi<ChannelVendorListDtoPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 渠道商申请记录分页
   * for培训机构
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageApplyRecordForTrainingInstitution(
    params: { page?: Page; params?: ChannelVendorApplyRecordQueryParams },
    query: DocumentNode = GraphqlImporter.pageApplyRecordForTrainingInstitution,
    operation?: string
  ): Promise<Response<ChannelVendorApplyRecordDtoPage>> {
    return commonRequestApi<ChannelVendorApplyRecordDtoPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 与当前培训机构角色签约的渠道商查询条件
   * @param page   分页
   * @param params 查询条件
   * @return 渠道商列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageForTrainingInstitution(
    params: { page?: Page; params?: ChannelVendorForTInstitutionQueryParams },
    query: DocumentNode = GraphqlImporter.pageForTrainingInstitution,
    operation?: string
  ): Promise<Response<ChannelVendorForTIDtoPage>> {
    return commonRequestApi<ChannelVendorForTIDtoPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构同意渠道商开通
   * @param mutate 查询 graphql 语法文档
   * @param channelVendorApplyId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeApplyForTrainingInstitution(
    channelVendorApplyId: string,
    mutate: DocumentNode = GraphqlImporter.agreeApplyForTrainingInstitution,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { channelVendorApplyId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请成为渠道商
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async apply(
    params: { captchaToken?: string; params?: ApplyChannelVendorApplyParams },
    mutate: DocumentNode = GraphqlImporter.apply,
    operation?: string
  ): Promise<Response<GraphqlOperateResult>> {
    return commonRequestApi<GraphqlOperateResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建渠道商（通过请求直接创建）
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createChannelVendor(
    params: { token?: string; params?: ApplyChannelVendorApplyParams },
    mutate: DocumentNode = GraphqlImporter.createChannelVendor,
    operation?: string
  ): Promise<Response<GraphqlOperateResult>> {
    return commonRequestApi<GraphqlOperateResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建提供企业账户渠道商（通过请求直接创建）
   * @param token
   * @param accountId
   * @param params
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createChannelVendorByAccount(
    params: { token?: string; accountId?: string; params?: ApplyChannelVendorApplyParams },
    mutate: DocumentNode = GraphqlImporter.createChannelVendorByAccount,
    operation?: string
  ): Promise<Response<GraphqlOperateResult>> {
    return commonRequestApi<GraphqlOperateResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构拒绝渠道商开通
   * @param mutate 查询 graphql 语法文档
   * @param channelVendorApplyId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async refuseApplyForTrainingInstitution(
    channelVendorApplyId: string,
    mutate: DocumentNode = GraphqlImporter.refuseApplyForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { channelVendorApplyId },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
