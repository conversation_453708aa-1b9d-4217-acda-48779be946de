import { CreateOpinionQuestionRequest } from '@api/ms-gateway/ms-examquestion-v1'
import QuestionType from '@api/service/common/enums/question/QuestionType'
import CreateQuestionVo from './CreateQuestionVo'
/**
 **判断题
 */
class CreateOpinionQuestionVo extends CreateQuestionVo {
  questionType = QuestionType.enum.opinion
  /**
   * 正确答案【必填】
   */
  correctAnswer = true
  /**
   * 正确文本【必填】
   */
  correctAnswerText = ''
  /**
   * 不正确文本【必填】
   */
  incorrectAnswerText = ''

  // 模型转换为Dto
  toDto() {
    const createQuestionDto = new CreateOpinionQuestionRequest()
    createQuestionDto.topic = this.topic
    createQuestionDto.questionType = this.questionType
    createQuestionDto.libraryId = this.libraryId
    createQuestionDto.enabled = this.enabled
    createQuestionDto.dissects = this.dissects
    createQuestionDto.relateCourseIds = [this.relateCourseId]
    createQuestionDto.correctAnswer = this.correctAnswer
    createQuestionDto.correctAnswerText = this.correctAnswerText
    createQuestionDto.incorrectAnswerText = this.incorrectAnswerText
    createQuestionDto.questionDifficulty = this.questionDifficulty
    return createQuestionDto
  }
}

export default CreateOpinionQuestionVo
