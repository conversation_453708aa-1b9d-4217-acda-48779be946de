import MsStudentcourseAppraisalV1 from '@api/ms-gateway/ms-studentcourse-appraisal-v1'

class MutationBizAppraise {
  courseAppraiseId: string

  constructor(courseAppraiseId: string) {
    this.courseAppraiseId = courseAppraiseId
  }

  async doRemove() {
    const { status } = await MsStudentcourseAppraisalV1.deleteAppraisalCourse(this.courseAppraiseId)
    return status
  }
}

export default MutationBizAppraise
