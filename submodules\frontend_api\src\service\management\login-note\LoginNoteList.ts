import { LoginNoteItem } from './models/LoginNoteItem'
import LoginNoteParams from './models/LoginNoteParams'
import PlatLoginLog, { LoginLogRequest, LoginLogResponse } from '@api/platform-gateway/platform-login-log-v1'
import { Page } from '@hbfe/common'
export default class LoginNoteList {
  /**
   * 筛选项
   */
  params = new LoginNoteParams()
  /**
   * 列表项
   */
  list = new Array<LoginNoteItem>()

  /**
   * 查询登录日志列表
   */
  async doQuery(page: Page) {
    const res = await PlatLoginLog.pageLoginLogList({
      page,
      request: this.params.to()
    })
    this.list = []
    if (res.status.isSuccess()) {
      this.list = res.data.currentPageData.map((item) => {
        return LoginNoteItem.from(item)
      })
    }
    page.totalSize = res.data?.totalSize || 0
    page.totalPageSize = res.data?.totalPageSize || 0
    return this.list
  }
}
