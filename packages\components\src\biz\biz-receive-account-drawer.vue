<!--
 * @Author: WRP
 * @Date: 收款账号抽屉
-->
<template>
  <div>
    <biz-select-Box
      :value-id="'accountId'"
      :label="'account'"
      :options="hasSelectAccountMode"
      @removeTag="cancelSelect"
      placeholder="请选择收款账号"
      @clear="clear"
      @blur="openDialog"
      :multiple="multiple"
    ></biz-select-Box>
    <el-drawer title="选择收款账号" :visible.sync="isDialogShow" size="1200px" custom-class="m-drawer">
      <div class="drawer-bd" v-loading="uiConfig.loading.pageLoading">
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="收款账号别名" min-width="300">
            <template slot-scope="scope">
              {{ scope.row.accountName }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" @click="selectAccount(scope.row)" v-if="selected(scope.row)">选择</el-button>
              <el-button type="text" @click="cancelSelect(scope.row.accountId, true)" v-else>取消选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination class="f-mt15 f-tr" :page="page" v-bind="page"> </hb-pagination>
      </div>
      <!-- <div class="m-btn-bar drawer-ft">
        <el-button @click="getValue">取消</el-button>
        <el-button type="primary" @click="setValue">确定</el-button>
      </div> -->
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Prop, Component, Vue } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import { HasSelectAccountMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectAccountMode'
  import TradeInfoConfigModule from '@api/service/management/trade-info-config/TradeInfoConfigModule'
  import QueryReceiveAccount from '@api/service/management/trade-info-config/query/QueryReceiveAccount'
  import ReceiveAccountVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountVo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'

  @Component
  export default class extends Vue {
    page: UiPage
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable
    // 收款账号列表实例
    receiveAccountObj: QueryReceiveAccount = TradeInfoConfigModule.queryTradeInfoConfigFactory.getQueryReceiveAccount()
    tableData = new Array<ReceiveAccountVo>()
    isDialogShow = false
    // 选中的值
    hasSelectAccountMode = new Array<HasSelectAccountMode>()
    // ui控制组
    uiConfig = {
      loading: {
        pageLoading: false
      }
    }

    constructor() {
      super()
      if (this.isFxlogin && this.isHadFxAbility) {
        this.page = new UiPage(this.doQueryfx, this.doQueryfx)
      } else {
        this.page = new UiPage(this.doQuery, this.doQuery)
      }
    }

    // 选中之后回传的参数
    @Prop({
      type: Array,
      required: true,
      default: () => new Array<HasSelectAccountMode>()
    })
    value: Array<HasSelectAccountMode>

    // 是否多选
    @Prop({
      type: Boolean,
      default: false
    })
    multiple: boolean

    async created() {
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryfx()
      } else {
        await this.doQuery()
      }
    }

    // 查询收款账号列表
    async doQuery() {
      this.uiConfig.loading.pageLoading = true
      try {
        this.tableData = await this.receiveAccountObj.queryPage(this.page)
      } catch (e) {
        console.log(e)
      } finally {
        this.uiConfig.loading.pageLoading = false
      }
    }

    // 查询分销收款账号列表
    async doQueryfx() {
      this.uiConfig.loading.pageLoading = true
      try {
        this.tableData = await this.receiveAccountObj.queryFxPage(this.page)
      } catch (e) {
        console.log(e)
      } finally {
        this.uiConfig.loading.pageLoading = false
      }
    }

    // 打开抽屉
    openDialog() {
      this.isDialogShow = true
    }

    /**
     * 被选中的账号
     */
    get selected() {
      return (item: ReceiveAccountVo) => {
        const index = this.hasSelectAccountMode?.findIndex((obj) => obj.accountId === item.id)
        return index === -1 ? true : false
      }
    }

    // 清空筛选框
    clear() {
      this.hasSelectAccountMode = new Array<HasSelectAccountMode>()
      this.setValue()
    }

    // 回传
    setValue() {
      this.$emit('input', this.hasSelectAccountMode)
      this.isDialogShow = false
    }

    // 选择账号
    selectAccount(item: ReceiveAccountVo) {
      if (!this.multiple) {
        // 单选
        this.hasSelectAccountMode = new Array<HasSelectAccountMode>()
      }
      const obj = new HasSelectAccountMode()
      obj.accountId = item.id
      obj.account = item.accountName
      this.hasSelectAccountMode.push(obj)
      this.setValue()
    }

    /**
     * 取消选择
     * @sure 确定删除 非弹窗情况下点击删除后需要触发set方法
     */
    cancelSelect(id: string, sure?: boolean) {
      const index = this.hasSelectAccountMode.findIndex((p) => p.accountId === id)
      this.hasSelectAccountMode.splice(index, 1)
      if (!sure) {
        this.setValue()
      }
    }
  }
</script>
