import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum LearningStatusEnum {
  /**
   * 待处理
   */
  waiting_process,
  /**
   * 进行中
   */
  processing,
  /**
   * 处理失败
   */
  process_fail,
  /**
   * 处理成功
   */
  process_success
}
export default class LearningStatusType extends AbstractEnum<LearningStatusEnum> {
  static enum = LearningStatusEnum
  constructor(status?: LearningStatusEnum) {
    super()
    this.current = status
    this.map.set(LearningStatusEnum.waiting_process, '待处理')
    this.map.set(LearningStatusEnum.processing, '进行中')
    this.map.set(LearningStatusEnum.process_fail, '处理失败')
    this.map.set(LearningStatusEnum.process_success, '处理成功')
  }
}
