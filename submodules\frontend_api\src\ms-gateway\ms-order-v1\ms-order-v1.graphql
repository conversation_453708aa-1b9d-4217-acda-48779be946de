"""独立部署的微服务,K8S服务名:ms-order-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""买家申请取消订单
		@param applyCancelOrder 申请取消订单信息
		@return 取消订单结果
	"""
	applyCancelOrder(applyCancelOrder:BuyerApplyCancelOrderRequest):CancelOrderResultResponse
	"""索取订单发票
		@param orderInvoiceRequest 发票信息
	"""
	applyInvoice(orderInvoiceRequest:ApplyOrderInvoiceRequest):ApplyInvoiceResponse
	"""索取订单发票校验
		@param orderInvoiceRequest 发票信息
	"""
	applyInvoiceValidate(orderInvoiceRequest:ApplyOrderInvoiceValidateRequest):ApplyInvoiceResponse
	"""批次单申请发票
		@param applyInvoiceParam 申请发票信息
	"""
	batchApplyInvoice(applyInvoiceParam:BatchOrderApplyInvoiceRequest):Void
	"""批次单直接申请发票（非补要发票）"""
	batchApplyInvoiceDirectly(applyInvoiceParam:BatchOrderApplyInvoiceRequest):BatchOrderApplyInvoiceDirectlyResponse
	"""批次单申请发票校验
		@param applyInvoiceValidateParam 申请发票信息
		@return
	"""
	batchApplyInvoiceValidate(applyInvoiceValidateParam:BatchOrderApplyInvoiceValidateRequest):ApplyInvoiceResult
	"""更新批次单申请发票信息"""
	batchInvoiceChange(request:BatchOrderInvoiceChangeRequest):BatchOrderInvoiceChangeResponse
	"""买家申请退货"""
	buyerApplyReturn(request:BuyerApplyOrderReturnRequest):ApplyOrderReturnResponse
	"""取消集体报名
		@param batchNo 集体报名编号
	"""
	cancelCollectiveSignup(batchNo:String,reasonId:String,reason:String):Void
	"""创建订单
		接口异常code定义：http://192.168.1.225:8090/pages/viewpage.action?pageId=39748749
		@param createOrderInfo 创建参数
		@return 订单创建序列号
	"""
	createOrder(createOrderInfo:CreateOrderRequest):CreateOrderResultResponse
	"""获取集体报名状态
		0 - 已创建，未提交
		1 - 正常
		2 - 交易完成
		3 - 交易关闭
		4 - 提交处理中
		5 - 取消处理中
		@param collectiveSignupNo 集体报名编号
	"""
	findBatchOrderBatchPayStatus(collectiveSignupNo:String!):Int!
	"""批次线下支付"""
	offlinePayBatchOrder(batchOrderOfflinePayParam:BatchOrderOfflinePaymentRequest):BatchApplyOfflinePaymentResponse
	"""批次线下支付并申请发票
		<p>
		本方法用于处理批次订单的线下支付逻辑，并在支付成功后根据请求中的发票信息申请发票
		@param request 包含批次订单线下支付和发票申请信息的请求对象
		@return 返回批次线下支付并申请发票的响应对象
	"""
	offlinePayBatchOrderAndApplyInvoice(request:BatchOrderOfflinePaymentAndInvoiceRequest):BatchApplyOfflinePaymentAndApplyInvoiceResponse
	"""线下支付
		@param request:
		@return {@link ApplyOfflinePaymentResultResponse}
		<AUTHOR> By Cb
		@date 2022/5/13 16:23
	"""
	offlinePayOrder(request:OrderOfflinePaymentRequest):ApplyOfflinePaymentResultResponse
	"""批次线上支付"""
	onlinePayBatchOrder(batchOrderOnlinePayParam:BatchOrderOnlinePaymentRequest):BatchApplyOnlinePaymentResponse
	"""批次线上支付并申请发票
		<p>
		本方法用于处理批次订单的线上支付，并在支付成功后申请发票
		它首先将请求映射为批次线上支付请求，并发送命令进行支付处理
		如果支付成功且请求中包含了发票信息，则调用申请发票的方法
		@param request 包含批次订单线上支付和申请发票信息的请求对象
		@return 返回批次线上支付并申请发票的响应对象，包含处理结果
	"""
	onlinePayBatchOrderAndApplyInvoice(request:BatchOrderOnlinePaymentAndApplyInvoiceRequest):BatchApplyOnlinePaymentAndApplyInvoiceResponse
	"""线上支付
		@param onlinePaymentRequest 线上支付信息
		@return 线上支付结果
	"""
	onlinePayOrder(onlinePaymentRequest:OrderOnlinePaymentRequest):ApplyOnlinePaymentResultResponse
	"""准备开票
		@param orderNo:
		@return {@link PrepareApplyInvoiceResponse}
		<AUTHOR> By Cb
		@date 2022/4/12 15:09
	"""
	prepareApplyInvoice(orderNo:String!):PrepareApplyInvoiceResponse
	"""准备批次支付
		@param request 请求参数
		@return 返回值
	"""
	prepareBatchOrderPay(request:PrepareBatchPayRequest!):PrepareBatchPayResponse
	"""准备支付
		@param request 请求参数
		@return 返回值
	"""
	preparePay(request:PreparePayRequest!):PreparePayResponse
	"""准备下单
		购买渠道类型：
		1-用户自主购买
		2-集体缴费
		3-管理员导入
		4-集体报名个人缴费
		@param purchaseChannelType 购买渠道类型，见备注
		@return PrepareOrderResponse 准备下单配置信息
	"""
	preparePlaceOrder(purchaseChannelType:Int!):PrepareOrderResponse
	"""卖家申请退货(订单售后服务领域)
		@param request 申请退货请求
		@return 申请退货响应
	"""
	sellerApplyAfterSale(request:SellerApplyAfterSaleRequest):SellerApplyAfterSaleResponse
	"""申请批次退货"""
	sellerApplyBatchReturn(request:SellerApplyBatchOrderReturnRequest):ApplyBatchOrderReturnResponse
	"""卖家申请换货"""
	sellerApplyExchange(request:SellerApplyOrderExchangeRequest):ApplyOrderExchangeResponse
	"""卖家申请退货"""
	sellerApplyReturn(request:SellerApplyOrderReturnRequest):ApplyOrderReturnResponse
	"""卖家取消订单
		@param sellerCancelOrder 卖家申请取消信息
		@return 取消订单结果
	"""
	sellerCancelOrder(sellerCancelOrder:SellerCancelOrderRequest):CancelOrderResultResponse
	"""批次单更新线下汇款凭证"""
	updateBatchOfflinePaymentVouchers(request:UpdateBatchOrderOfflinePaymentVoucherRequest):UpdateBatchOrderOfflinePaymentVoucherResponse
	"""批次单重新开票
		@param request 批次单发票更新信息
	"""
	updateBatchOrderInvoice(request:UpdateBatchOrderInvoiceRequest):Void
}
input BatchReturnSubOrderInfo @type(value:"com.fjhb.domain.trade.api.batchreturnedorder.models.BatchReturnSubOrderInfo") {
	orderNo:String!
	subOrderNo:String!
}
input DeliveryAddress @type(value:"com.fjhb.domain.trade.api.offlineinvoice.events.entities.DeliveryAddress") {
	consignee:String!
	phone:String!
	region:String!
	address:String!
}
input TakePoint @type(value:"com.fjhb.domain.trade.api.offlineinvoice.events.entities.TakePoint") {
	pickupLocation:String!
	pickupTime:String!
	remark:String
}
input SubOrderAfterInfo @type(value:"com.fjhb.domain.trade.api.orderAfterSale.entities.SubOrderAfterInfo") {
	subOrderNo:String
	quantity:Int!
	amount:BigDecimal
	amountSource:Int!
	properties:Map
}
input BatchOrderInvoiceDeliveryAddress @type(value:"com.fjhb.ms.order.v1.kernel.aggregate.batchpaybatchorder.entities.BatchOrderInvoiceInfo$BatchOrderInvoiceDeliveryAddress") {
	"""收件人"""
	consignee:String
	"""电话"""
	phone:String
	"""所在物理地区"""
	region:String
	"""地址"""
	address:String
}
input BatchOrderInvoiceTakePoint @type(value:"com.fjhb.ms.order.v1.kernel.aggregate.batchpaybatchorder.entities.BatchOrderInvoiceInfo$BatchOrderInvoiceTakePoint") {
	"""领取地点"""
	pickupLocation:String
	"""领取时间"""
	pickupTime:String
	"""备注"""
	remark:String
}
"""电子发票更新信息传输对象
	<AUTHOR> By lincong
	@date 2023/09/21 18:01
"""
input InvoiceDto @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.dto.InvoiceDto") {
	"""发票种类,必传
		<pre>
		1-普通发票
		2-增值税普通发票
		3-增值税专用发票
		</pre>
	"""
	invoiceCategory:Int!
	"""发票类型，必传
		1-电子发票
		2-纸质发票
	"""
	invoiceType:Int!
	"""开票方式，必传
		1-线上开票
		2-线下开票
	"""
	invoiceMethod:Int!
	"""发票抬头,必传"""
	title:String
	"""发票抬头类型，必传
		<pre>
		1-个人
		2-企业
		</pre>
	"""
	titleType:Int
	"""购买方纳税人编号"""
	taxpayerNo:String
	"""购买方地址"""
	address:String
	"""购买方电话号码"""
	phone:String
	"""购买方开户行名称"""
	bankName:String
	"""购买方银行账户"""
	account:String
	"""购买方电子邮箱"""
	contactEmail:String
	"""联系电话"""
	contactPhone:String
	"""发票票面备注"""
	remark:String
	"""营业执照"""
	businessLicensePath:String
	"""开户许可"""
	accountOpeningLicensePath:String
	"""配送方式
		0/1/2,无/自取/快递
		@see OfflineShippingMethods
	"""
	shippingMethod:Int
	"""配送地址信息"""
	deliveryAddress:BatchOrderInvoiceDeliveryAddress
	"""自取点信息"""
	batchOrderInvoiceTakePoint:BatchOrderInvoiceTakePoint
}
"""索取发票
	<AUTHOR>
	@since 2021/3/25
"""
input ApplyOrderInvoiceRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.ApplyOrderInvoiceRequest") {
	"""订单号"""
	orderNo:String
	"""发票信息"""
	invoiceInfo:InvoiceInfoRequest
}
"""索取发票校验
	<AUTHOR>
	@since 2021/3/25
"""
input ApplyOrderInvoiceValidateRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.ApplyOrderInvoiceValidateRequest") {
	"""订单号"""
	orderNo:String
}
"""买家申请取消订单
	<AUTHOR> create 2021/1/27 19:32
"""
input BuyerApplyCancelOrderRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.BuyerApplyCancelOrderRequest") {
	"""订单号"""
	orderNo:String!
	"""取消原因编号"""
	reasonId:String
	"""取消原因描述"""
	reason:String
}
"""卖家申请订单退货请求
	<AUTHOR>
"""
input BuyerApplyOrderReturnRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.BuyerApplyOrderReturnRequest") {
	orderNo:String!
	subOrderNo:String!
	"""退货原因id"""
	reasonId:String
	"""退货原因描述"""
	description:String
	"""是否需要人工审批,默认是"""
	needManualApprove:Boolean!
	"""退货单类型;1:仅退货,2:仅退款,3:退货并退款"""
	returnOrderType:Int!
}
"""请求创建订单
	<AUTHOR>
	@since 2021/1/22
"""
input CreateOrderRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.CreateOrderRequest") {
	"""买家编号"""
	buyerId:String!
	"""商品列表"""
	commodities:[Commodity]!
	"""购买渠道类型
		1-用户自主购买
		2-集体缴费
		3-管理员导入
	"""
	purchaseChannelType:Int!
	"""终端类型
		<p>
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	terminalCode:String!
	"""渠道商编号"""
	channelVendorId:String
	"""是否需要发票"""
	needInvoice:Boolean!
	"""发票信息"""
	invoiceInfo:InvoiceInfoRequest
	"""参训单位id"""
	participatingUnitId:String
	"""销售渠道Id（自营渠道为空，专题渠道时必填）"""
	saleChannelId:String
	"""销售渠道类型
		0-自营渠道
		2-专题渠道
	"""
	saleChannel:Int!
	"""购买来源类型，1-门户，2-专题
		@see com.fjhb.ms.order.v1.api.consts.PurchaseSourceType
	"""
	purchaseSourceType:Int
}
"""商品描述"""
input Commodity @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.CreateOrderRequest$Commodity") {
	"""商品sku编号"""
	skuId:String
	"""商品数量"""
	quantity:Int
	"""面授班时有值"""
	issueInfo:IssueInfo
}
input IssueInfo @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.CreateOrderRequest$Commodity$IssueInfo") {
	"""期别id"""
	issueId:String
	"""住宿类型
		住宿类型 0-无需住宿 1-单人住宿 2-合住
	"""
	accommodationType:Int
}
"""发票信息
	<AUTHOR>
	@since 2021/3/23
"""
input InvoiceInfoRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.InvoiceInfoRequest") {
	"""发票抬头"""
	title:String
	"""发票抬头类型
		<pre>
		1-个人
		2-企业
		</pre>
	"""
	titleType:Int
	"""发票类型
		<pre>
		1-电子发票
		2-纸质发票
		</pre>
	"""
	invoiceType:Int
	"""发票种类
		<pre>
		1-普通发票
		2-增值税普通发票
		3-增值税专用发票
		</pre>
	"""
	invoiceCategory:Int
	"""购买方纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账户"""
	account:String
	"""发票票面备注"""
	remark:String
	"""开票方式
		1 - 线上开票
		2 - 线下开票
	"""
	invoiceMethod:Int
	"""联系电子邮箱"""
	email:String
	"""联系电话"""
	contactPhone:String
	"""营业执照"""
	businessLicensePath:String
	"""开户许可"""
	accountOpeningLicensePath:String
	"""配送方式
		0/1/2,无/自取/快递
		@see OfflineShippingMethods
	"""
	shippingMethod:Int
	"""配送地址信息"""
	deliveryAddress:DeliveryAddress
	"""自取点信息"""
	takePoint:TakePoint
	"""发票信息校验策略
		@see InvoiceVerifyStrategy
	"""
	invoiceVerifyStrategy:Int!
}
"""graphql 使用的key、value 返回对象
	<AUTHOR> create 2021/2/24 20:07
"""
input KeyValueDataRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.KeyValueDataRequest") {
	"""字段的key"""
	key:String
	"""字段value"""
	value:String
}
"""线上支付参数
	<AUTHOR> create 2021/1/28 19:39
"""
input OrderOfflinePaymentRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.OrderOfflinePaymentRequest") {
	"""订单号"""
	orderNo:String!
	"""购买渠道类型
		1-用户自主购买
		2-集体缴费
		3-管理员导入
	"""
	purchaseChannelType:Int!
	"""支付终端
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	paymentChannelTerminal:String!
	"""/**
		付款人
	"""
	payerId:String!
	"""备注
		选填
	"""
	remark:String
	"""线下付款凭证文件路径集合"""
	paths:[String]!
}
"""线上支付参数
	<AUTHOR> create 2021/1/28 19:39
"""
input OrderOnlinePaymentRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.OrderOnlinePaymentRequest") {
	"""订单号"""
	orderNo:String!
	"""支付渠道编号
		培训券，对接众智汇云培训券:TRAINING_VOUCHER
		支付宝:ALIPAY
		微信：WXPAY
		兴业银行: CIB_PAY
	"""
	paymentChannelId:String!
	"""购买渠道类型
		1-用户自主购买
		2-集体缴费
		3-管理员导入
	"""
	purchaseChannelType:Int!
	"""支付终端
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	purchaseChannelTerminal:String!
	"""支付描述"""
	description:String
	"""支付成功后跳转地址"""
	pageUrl:String
	"""支付的附加属性，由支付渠道决定
		当支付渠道为培训券（TRAINING4_VOUCHER）时，
		{
		"couponCode":"培训券编码",
		"educationCode":"机构编码（企业统一社会信用代码）",
		"workType": "工种名称",
		"learningSchemeId": "培训班id"
		}
		当支付渠道为支付宝-移动端：
		{
		"method":"alipay.trade.wap.pay"
		}
		当支付渠道为支付宝-电脑端：
		{
		"method":"alipay.trade.page.pay"
		}
		当支付渠道为微信：
		{
		“method”:"native"
		}
		微信h5中clientIp不需要提供 由服务端获取
		当前支付渠道为兴业银行:
		兴业银行统一扫码支付
		{
		"method":"unifiedTradeNative"
		}
		兴业银行微信小程序&公众号支付
		{
		"method":"payWeixinJspay",
		"sub_openid":"用户openid",
		"is_raw":"是否原生态JS,1-是;其它-否",
		"is_minipg"："是否小程序支付,1-是;其它-否“,
		}
	"""
	paymentProperties:[KeyValueDataRequest]
}
"""准备支付参数
	<AUTHOR>
	@since 2022/3/24
"""
input PreparePayRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.PreparePayRequest") {
	"""订单号"""
	orderNo:String
	"""终端
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
		H5端：H5
	"""
	purchaseChannelTerminal:String!
	"""购买渠道类型：
		1-用户自主购买
		2-集体缴费
		3-管理员导入
		4-集体报名个人缴费
	"""
	purchaseChannelType:Int!
}
"""卖家申请退货请求"""
input SellerApplyAfterSaleRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.SellerApplyAfterSaleRequest") {
	"""订单号"""
	orderNo:String!
	"""退货原因ID"""
	reasonId:String!
	"""退货说明"""
	description:String!
	"""退货单类型
		@see ReturnOrderType
	"""
	returnOrderType:Int!
	"""退货商品列表"""
	returnList:[SubOrderAfterInfo]!
	"""扩展信息（华医网差异化courseType） 1=专业 2=公需"""
	properties:Map
}
"""卖家申请订单退货请求
	<AUTHOR>
"""
input SellerApplyBatchOrderReturnRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.SellerApplyBatchOrderReturnRequest") {
	batchOrderNo:String!
	"""退货原因id"""
	reasonId:String!
	"""退货原因描述"""
	description:String!
	"""是否需要人工审批,默认是"""
	needManualApprove:Boolean!
	"""批次退货单类型;1:仅退货,2:仅退款,3:退货并退款"""
	batchReturnOrderType:Int!
	"""如果有指定的退货的子订单信息,就只退指定的子订单,否则认为该批次单下的所有订单退货"""
	returnSubOrders:[BatchReturnSubOrderInfo]
}
"""申请订单换货
	<AUTHOR>
"""
input SellerApplyOrderExchangeRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.SellerApplyOrderExchangeRequest") {
	orderNo:String!
	subOrderNo:String!
	"""换货原因id,一般是系统内置一些原因"""
	reasonId:String
	"""换货原因描述"""
	description:String
	"""换货的目标商品sku id"""
	exchangeSkuId:String!
	"""当为分销订单单换货的时候，需要指定换货目标的定价策略"""
	exchangePricingPolicyId:String
	"""原期别id"""
	originIssueId:String
	"""目标期别id"""
	targetIssueId:String
	"""是否需要人工审批,默认是"""
	needManualApprove:Boolean!
}
"""卖家申请订单退货请求
	<AUTHOR>
"""
input SellerApplyOrderReturnRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.SellerApplyOrderReturnRequest") {
	orderNo:String!
	subOrderNo:String!
	"""退货原因id"""
	reasonId:String
	"""退货原因描述"""
	description:String
	"""是否需要人工审批,默认是"""
	needManualApprove:Boolean!
	"""退货单类型;1:仅退货,2:仅退款,3:退货并退款"""
	returnOrderType:Int!
}
"""卖家取消订单
	<AUTHOR> create 2021/1/27 20:08
"""
input SellerCancelOrderRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.SellerCancelOrderRequest") {
	"""订单号"""
	orderNo:String!
	"""取消原因ID"""
	reasonId:String
	"""取消原因说明
		选填
	"""
	reason:String
}
"""批次发票
	<AUTHOR> create 2021/4/30 9:36
"""
input BatchInvoiceRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.BatchInvoiceRequest") {
	"""发票种类，默认2
		<pre>
		1-普通发票
		2-增值税普通发票
		3-增值税专用发票
		</pre>
	"""
	invoiceCategory:Int!
	"""发票种类
		1-电子发票
		2-纸质发票
	"""
	invoiceType:Int!
	"""开票方式
		1-线上开票
		2-线下开票
	"""
	invoiceMethod:Int!
	"""发票抬头"""
	title:String
	"""发票抬头类型
		<pre>
		1-个人
		2-企业
		</pre>
	"""
	titleType:Int
	"""购买方纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账户"""
	account:String
	"""联系邮箱"""
	contactEmail:String
	"""联系电话"""
	contactPhone:String
	"""发票票面备注"""
	remark:String
	"""营业执照"""
	businessLicensePath:String
	"""开户许可"""
	accountOpeningLicensePath:String
	"""配送方式"""
	shippingMethod:Int
	"""配送地址信息"""
	deliveryAddress:DeliveryAddress
	"""自取点信息"""
	takePoint:TakePoint
	"""发票信息校验策略
		@see InvoiceVerifyStrategy
	"""
	invoiceVerifyStrategy:Int
}
"""批次单申请发票
	<AUTHOR> create 2021/4/30 9:12
"""
input BatchOrderApplyInvoiceRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.BatchOrderApplyInvoiceRequest") {
	"""批次单编号"""
	batchOrderNo:String
	"""申请批次发票信息"""
	invoiceInfo:BatchInvoiceRequest
}
"""批次单申请发票
	<AUTHOR> create 2021/4/30 9:12
"""
input BatchOrderApplyInvoiceValidateRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.BatchOrderApplyInvoiceValidateRequest") {
	"""批次单编号"""
	batchOrderNo:String
}
"""更新批次单申请发票
	<AUTHOR> create 2021/4/30 9:12
"""
input BatchOrderInvoiceChangeRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.BatchOrderInvoiceChangeRequest") {
	"""批次单编号"""
	batchOrderNo:String
	"""申请批次发票信息"""
	invoiceInfo:BatchInvoiceRequest
}
"""批次单线下支付并申请发票请求参数
	<AUTHOR> create 2021/4/23 11:11
"""
input BatchOrderOfflinePaymentAndInvoiceRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.BatchOrderOfflinePaymentAndInvoiceRequest") {
	"""批次单号"""
	batchOrderNo:String
	"""购买渠道类型
		1-用户自主购买
		2-集体缴费
		3-管理员导入
	"""
	purchaseChannelType:Int!
	"""支付终端
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	paymentChannelTerminal:String!
	"""付款人"""
	payerId:String
	"""支付描述"""
	description:String
	"""备注
		选填
	"""
	remark:String
	"""线下付款凭证文件路径集合"""
	paths:[String]
	"""申请批次发票信息"""
	invoiceInfo:BatchInvoiceRequest
}
"""批次单线下支付请求参数
	<AUTHOR> create 2021/4/23 11:11
"""
input BatchOrderOfflinePaymentRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.BatchOrderOfflinePaymentRequest") {
	"""批次单号"""
	batchOrderNo:String
	"""购买渠道类型
		1-用户自主购买
		2-集体缴费
		3-管理员导入
	"""
	purchaseChannelType:Int!
	"""支付终端
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	paymentChannelTerminal:String!
	"""付款人"""
	payerId:String
	"""支付描述"""
	description:String
	"""备注
		选填
	"""
	remark:String
	"""线下付款凭证文件路径集合"""
	paths:[String]
}
"""批次单线上支付并申请发票请求参数
	<AUTHOR> create 2021/4/23 11:10
"""
input BatchOrderOnlinePaymentAndApplyInvoiceRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.BatchOrderOnlinePaymentAndApplyInvoiceRequest") {
	"""批次单号"""
	batchOrderNo:String
	"""支付渠道ID"""
	paymentChannelId:String
	"""终端类型"""
	terminalCode:String
	"""支付成功后跳转地址"""
	pageUrl:String
	"""支付描述"""
	description:String
	"""支付的附加属性，由支付渠道决定
		当支付渠道为培训券（TRAINING_VOUCHER）时，
		{
		"couponCode":"培训券编码",
		"educationCode":"机构编码（企业统一社会信用代码）",
		"workType": "工种名称",
		"learningSchemeId": "培训班id"
		}
	"""
	paymentProperties:[KeyValueDataRequest]
	"""申请批次发票信息"""
	invoiceInfo:BatchInvoiceRequest
}
"""批次单线上支付请求参数
	<AUTHOR> create 2021/4/23 11:10
"""
input BatchOrderOnlinePaymentRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.BatchOrderOnlinePaymentRequest") {
	"""批次单号"""
	batchOrderNo:String
	"""支付渠道ID"""
	paymentChannelId:String
	"""终端类型"""
	terminalCode:String
	"""支付成功后跳转地址"""
	pageUrl:String
	"""支付描述"""
	description:String
	"""支付的附加属性，由支付渠道决定
		当支付渠道为培训券（TRAINING_VOUCHER）时，
		{
		"couponCode":"培训券编码",
		"educationCode":"机构编码（企业统一社会信用代码）",
		"workType": "工种名称",
		"learningSchemeId": "培训班id"
		}
	"""
	paymentProperties:[KeyValueDataRequest]
}
"""准备批次支付参数
	<AUTHOR> By Cb
	@since 2022/04/28
"""
input PrepareBatchPayRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.PrepareBatchPayRequest") {
	"""批次订单号"""
	batchOrderNo:String
	"""终端
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
		H5端：H5
	"""
	purchaseChannelTerminal:String!
	"""购买渠道类型：
		1-用户自主购买
		2-集体缴费
		3-管理员导入
		4-集体报名个人缴费
	"""
	purchaseChannelType:Int!
}
"""批次单更新电子发票信息请求
	<AUTHOR> By lincong
	@date 2023/09/21 17:51
"""
input UpdateBatchOrderInvoiceRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.UpdateBatchOrderInvoiceRequest") {
	"""当前发票所属的批次单号"""
	batchOrderNo:String
	"""触发重新开票按钮的当前发票id"""
	invoiceId:String
	"""电子票更新信息对象"""
	invoiceInfo:InvoiceDto
}
"""更新批次单线下支付凭证请求参数
	<AUTHOR> create 2021/4/23 11:11
"""
input UpdateBatchOrderOfflinePaymentVoucherRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.UpdateBatchOrderOfflinePaymentVoucherRequest") {
	"""批次单号"""
	batchOrderNo:String
	"""备注
		选填
	"""
	remark:String
	"""线下付款凭证文件路径集合"""
	paths:[String]
}
"""批次订单状态
	<AUTHOR> create 2021/4/19 15:47
"""
enum BatchOrderState @type(value:"com.fjhb.ms.order.v1.api.consts.BatchOrderState") {
	"""未确认"""
	UNCONFIRMED
	"""正常"""
	NORMAL
	"""交易完成"""
	COMPLETED
	"""交易关闭"""
	CLOSED
	"""提交中
		提交处理完成后，变为正常状态
		@see #NORMAL
	"""
	COMMITTING
	"""取消处理中"""
	CANCELING
}
"""支付状态
	<AUTHOR>
	@since 2021/1/20
"""
enum PaymentState @type(value:"com.fjhb.ms.order.v1.api.consts.PaymentState") {
	"""未支付"""
	NONE
	"""支付中"""
	PAYING
	"""已支付"""
	PAID
}
"""卖家申请退货响应"""
type SellerApplyAfterSaleResponse @type(value:"com.fjhb.ms.order.v1.api.response.order.SellerApplyAfterSaleResponse") {
	"""是否成功"""
	success:Boolean!
	"""售后单号"""
	orderAfterSaleNo:String
	"""错误代码"""
	code:String
	"""错误信息"""
	message:String
}
"""
	APPLY_INVOICE_NO_OPEN("30001", "当前订单不开放申请发票"),
	APPLY_INVOICE_NO_ALLOW("30002", "当前订单不允许申请发票"),
	APPLY_INVOICE_EXPIRE("30003", "当前订单索取发票日期已经截止"),
	APPLY_INVOICE_ALREADY("30004", "当前订单已经申请开过发票");
	APPLY_INVOICE_DIRECTLY_NO_ALLOW("30005", "当前订单不允许直接申请发票");
"""
type ApplyInvoiceResult @type(value:"com.fjhb.ms.order.v1.kernel.appservice.result.ApplyInvoiceResult") {
	"""是否允许开票"""
	isAllow:Boolean!
	"""失败错误代码
		@see 192.168.1.225:8090/pages/viewpage.action?pageId=39748749
	"""
	code:String
	"""错误信息"""
	message:String
}
"""批次单发票信息
	<AUTHOR>
	@since 2022/3/25
"""
type BatchOrderInvoiceResult @type(value:"com.fjhb.ms.order.v1.kernel.appservice.result.PrepareBatchPayResult$BatchOrderInvoiceResult") {
	"""发票种类
		1 - 普通发票
		2 - 增值税普通发票
		3 - 增值税专用发票
	"""
	invoiceCategory:Int!
	"""发票类型
		1 - 电子票
		2 - 纸质票
	"""
	invoiceType:Int!
	"""发票抬头"""
	title:String
	"""发票抬头类型
		1 - 个人
		2 - 企业
	"""
	titleType:Int!
	"""纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账户"""
	account:String
	"""联系电子邮箱"""
	email:String
	"""发票票面备注"""
	remark:String
	"""联系电话"""
	contactPhone:String
	"""开票方式
		1 - 线上开票
		2 - 线下开票
	"""
	invoiceMethod:Int
	"""营业执照"""
	businessLicensePath:String
	"""开户许可"""
	accountOpeningLicensePath:String
	"""配送方式
		0/1/2,无/自取/快递
	"""
	shippingMethod:Int
	"""配送地址信息"""
	deliveryAddress:PrepareBatchPayDeliveryAddress
	"""自取点信息"""
	takePoint:PrepareBatchPayTakePoint
}
type PrepareBatchPayDeliveryAddress @type(value:"com.fjhb.ms.order.v1.kernel.appservice.result.PrepareBatchPayResult$BatchOrderInvoiceResult$PrepareBatchPayDeliveryAddress") {
	"""收件人"""
	consignee:String
	"""电话"""
	phone:String
	"""所在物理地区"""
	region:String
	"""地址"""
	address:String
}
type PrepareBatchPayTakePoint @type(value:"com.fjhb.ms.order.v1.kernel.appservice.result.PrepareBatchPayResult$BatchOrderInvoiceResult$PrepareBatchPayTakePoint") {
	"""领取地点"""
	pickupLocation:String
	"""领取时间"""
	pickupTime:String
	"""备注"""
	remark:String
}
"""申请批次订单退货结果
	<AUTHOR>
"""
type ApplyBatchOrderReturnResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.ApplyBatchOrderReturnResponse") {
	"""申请退货业务code"""
	code:String
	"""申请退货信息"""
	message:String
	data:ApplyBatchOrderReturnData
}
type ApplyBatchOrderReturnData @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.ApplyBatchOrderReturnResponse$ApplyBatchOrderReturnData") {
	"""生成的批次退货单号"""
	batchReturnOrderNo:String
}
"""APPLY_INVOICE_NO_OPEN("30001", "当前订单不开放申请发票"),
	APPLY_INVOICE_NO_ALLOW("30002", "当前订单不允许申请发票"),
	APPLY_INVOICE_EXPIRE("30003", "当前订单索取发票日期已经截止"),
	APPLY_INVOICE_ALREADY("30004", "当前订单已经申请开过发票");
"""
type ApplyInvoiceResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.ApplyInvoiceResponse") {
	"""是否允许开票"""
	isAllow:Boolean!
	"""失败错误代码"""
	code:String
	"""错误信息"""
	message:String
}
"""请求线下支付结果
	<AUTHOR> create 2021/2/4 15:16
"""
type ApplyOfflinePaymentResultResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.ApplyOfflinePaymentResultResponse") {
	"""订单号"""
	orderNo:String
	"""交易号（付款流水号）"""
	payFlowNo:String
}
"""请求线上支付结果
	<AUTHOR> create 2021/2/4 15:16
"""
type ApplyOnlinePaymentResultResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.ApplyOnlinePaymentResultResponse") {
	"""订单号"""
	orderNo:String
	"""去支付的地址"""
	payUrl:String
	"""请求支付结果"""
	result:Boolean!
	"""失败错误代码，仅当result=false时有值
		培训券支付渠道返回错误代码：
		261-培训券已使用
		262-培训券应用条件不符合
		263-培训券已过期
		264-培训券已作废
		265-已兑换过【xxx】的线上培训课程
		266-当前券仅用于兑换【xxx】工种的课程
		267-今年内已参加过该工种培训
		269-培训券余额不足
	"""
	code:String
	"""错误信息"""
	message:String
	"""支付网关支付模式
		0-同步
		1-重定向
	"""
	payMode:Int!
	"""交易号（付款流水号）"""
	payFlowNo:String
}
"""申请订单换货结果
	<AUTHOR>
"""
type ApplyOrderExchangeResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.ApplyOrderExchangeResponse") {
	"""申请退货业务code
		("3002", "当前渠道下，商品定价信息不存在");
		("3003", "换货商品价格信息与原订单商品价格不一致");
		("3004", "当前渠道下，存在无效的商品优惠信息");
		("3005", "目标商品已下架");
		("3006", "商品授权无效");
		("3007", "目标商品存在未付款订单")
	"""
	code:String
	"""申请退货信息"""
	message:String
	data:ApplyOrderExchangeData
}
type ApplyOrderExchangeData @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.ApplyOrderExchangeResponse$ApplyOrderExchangeData") {
	"""生成的换货单号"""
	exchangeOrderNo:String
}
"""申请订单退货结果
	<AUTHOR>
"""
type ApplyOrderReturnResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.ApplyOrderReturnResponse") {
	"""申请退货业务code"""
	code:String
	"""申请退货信息"""
	message:String
	data:ApplyOrderReturnData
}
type ApplyOrderReturnData @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.ApplyOrderReturnResponse$ApplyOrderReturnData") {
	"""生成的退货单号"""
	returnOrderNo:String
}
"""批次单直接申请发票响应
	<AUTHOR>
"""
type BatchOrderApplyInvoiceDirectlyResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.BatchOrderApplyInvoiceDirectlyResponse") {
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""更新批次单申请发票响应
	NO_APPLY_INVOICE("60001", "当前订单未申请发票"),
	CHANGE_INVOICE_NO_ALLOW("60002", "当前订单状态不允许修改发票");
	<AUTHOR>
"""
type BatchOrderInvoiceChangeResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.BatchOrderInvoiceChangeResponse") {
	"""是否允许修改发票"""
	isAllow:Boolean!
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""取消订单结果
	<AUTHOR> create 2021/1/29 17:27
"""
type CancelOrderResultResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.CancelOrderResultResponse") {
	"""是否取消成功"""
	success:Boolean!
	"""订单号"""
	orderNo:String
	"""取消结果信息"""
	message:String
	"""商品验证结果信息"""
	resultList:[VerifyResultResponse]
}
"""创建订单结果
	<AUTHOR> create 2021/1/29 17:27
"""
type CreateOrderResultResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.CreateOrderResultResponse") {
	"""是否创建成功"""
	success:Boolean!
	"""订单号，仅当{@link #success}为{@code true}时有值"""
	orderNo:String
	"""订单创建时间，仅当{@link #success}为{@code true}时有值"""
	createTime:DateTime
	"""下单结果信息"""
	message:String
	"""商品验证结果信息"""
	resultList:[VerifyResultResponse]
}
"""发票配置
	<AUTHOR>
	@since 2022/3/24
"""
type InvoiceConfigResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.InvoiceConfigResponse") {
	"""开放发票类型
		0 - 不开放
		1 - 自主选择
		2 - 强制提供
	"""
	openInvoiceType:Int!
	"""是否允许索取发票
		当openInvoiceType=0时，该值为null
	"""
	allowAskFor:Boolean
	"""索取发票年度类型
		1 - 当年度
		2 - 下一个年度
		当openInvoiceType=0时，该值为null
	"""
	askForInvoiceYearType:Int
	"""索取发票截止日期，格式（MM/dd）,如：5月3日，则05/03
		当openInvoiceType=0时，该值为null
	"""
	askForInvoiceDeadline:String
	"""不同发票种类下发票配置"""
	allowInvoiceCategoryList:[InvoiceCategoryConfig]
}
type InvoiceCategoryConfig @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.InvoiceConfigResponse$InvoiceCategoryConfig") {
	"""允许开具的发票种类
		1 - 普通发票
		2 - 增值税普通发票
		3 - 增值税专用发票
		当openInvoiceType=0时，该值为null
	"""
	invoiceCategory:Int
	"""开票方式
		1 - 线上开票
		2 - 线下开票
		当openInvoiceType=0时，该值为null
	"""
	invoiceMethod:Int
	"""允许发票抬头
		1 - 个人
		2 - 企业
		当openInvoiceType=0时，该值为null
	"""
	invoiceTitleList:[Int]
	"""发票类型
		1 - 电子票
		2 - 纸质票
	"""
	invoiceType:Int
	"""发票备注类型
		0-未配置
		1-学员填写（包括集体报名管理员）
		2-统一配置
	"""
	invoiceRemarksType:Int
	"""发票备注内容"""
	invoiceRemarksContent:String
}
"""订单发票配送信息
	<AUTHOR>
	@since 2022/4/26
"""
type OrderInvoiceDeliveryAddressResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.OrderInvoiceDeliveryAddressResponse") {
	"""收件人"""
	consignee:String
	"""电话"""
	phone:String
	"""所在物理地区"""
	region:String
	"""地址"""
	address:String
}
"""订单发票自取信息
	<AUTHOR>
	@since 2022/4/26
"""
type OrderInvoiceTakePointResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.OrderInvoiceTakePointResponse") {
	"""领取地点"""
	pickupLocation:String
	"""领取时间"""
	pickupTime:String
	"""备注"""
	remark:String
}
type PaymentChannelInfoResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.PaymentChannelInfoResponse") {
	"""名称"""
	name:String
	"""logo地址"""
	logoPath:String
	"""支付渠道编号
		支付宝:ALIPAY
		微信：WXPAY
	"""
	channelId:String
}
"""准备开票返回信息
	<AUTHOR> By Cb
	@date 2022/4/12 15:08
"""
type PrepareApplyInvoiceResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.PrepareApplyInvoiceResponse") {
	"""发票配置返回"""
	invoiceConfigResult:InvoiceConfigResponse
}
"""准备下单返回信息"""
type PrepareOrderResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.PrepareOrderResponse") {
	"""发票配置返回"""
	invoiceConfigResult:InvoiceConfigResponse
}
"""准备支付返回值
	<AUTHOR>
	@since 2022/3/24
"""
type PreparePayResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.PreparePayResponse") {
	"""支付渠道信息"""
	paymentChannelInfoList:[PaymentChannelInfoResponse]
	"""订单信息"""
	orderInfo:OrderResponse
}
"""订单发票信息
	<AUTHOR>
	@since 2022/3/25
"""
type OrderInvoiceResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.PreparePayResponse$OrderInvoiceResponse") {
	"""发票种类
		1 - 普通发票
		2 - 增值税普通发票
		3 - 增值税专用发票
	"""
	invoiceCategory:Int!
	"""发票类型
		1 - 电子票
		2 - 纸质票
	"""
	invoiceType:Int!
	"""发票抬头"""
	title:String
	"""发票抬头类型
		1 - 个人
		2 - 企业
	"""
	titleType:Int!
	"""纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账户"""
	account:String
	"""联系电子邮箱"""
	email:String
	"""发票票面备注"""
	remark:String
	"""联系电话"""
	contactPhone:String
	"""开票方式
		1 - 线上开票
		2 - 线下开票
	"""
	invoiceMethod:Int
	"""营业执照"""
	businessLicensePath:String
	"""开户许可"""
	accountOpeningLicensePath:String
	"""配送方式
		0/1/2,无/自取/快递
	"""
	shippingMethod:Int
	"""配送地址信息"""
	deliveryAddress:OrderInvoiceDeliveryAddressResponse
	"""自取点信息"""
	takePoint:OrderInvoiceTakePointResponse
}
"""准备支付的订单信息
	<AUTHOR>
	@since 2022/3/25
"""
type OrderResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.PreparePayResponse$OrderResponse") {
	"""订单号"""
	orderNo:String
	"""买家编号"""
	buyerId:String
	"""订单金额"""
	amount:BigDecimal
	"""订单状态
		0 - 未确认
		1 - 正常
		2 - 交易完成
		3 - 交易关闭
	"""
	state:Int!
	"""支付状态
		0 - 未支付
		1 - 支付中
		2 - 已支付
	"""
	paymentState:Int!
	"""订单创建时间"""
	createTime:DateTime
	"""子订单列表"""
	subOrders:[SubOrderResponse]
	"""发票信息"""
	invoiceInfo:OrderInvoiceResponse
	"""终端
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
		H5端：H5
	"""
	purchaseChannelTerminal:String
	"""购买渠道类型：
		1-用户自主购买
		2-集体缴费
		3-管理员导入
		4-集体报名个人缴费
	"""
	purchaseChannelType:Int!
}
"""子订单信息
	<AUTHOR>
	@since 2022/3/25
"""
type SubOrderResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.PreparePayResponse$SubOrderResponse") {
	"""子订单号"""
	subOrderNo:String
	"""商品sku编号"""
	skuId:String
	"""商品sku名称"""
	skuName:String
	"""商品单价"""
	price:BigDecimal
	"""商品数量"""
	quantity:Int
	"""总价"""
	amount:BigDecimal
}
"""批次单更新线下汇款凭证响应
	CHANGE_PAYMENT_VOUCHER_NO_ALLOW("70002", "当前订单状态不允许修改付款凭证");
	<AUTHOR>
"""
type UpdateBatchOrderOfflinePaymentVoucherResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.UpdateBatchOrderOfflinePaymentVoucherResponse") {
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""校验结果返回
	<AUTHOR> create 2021/2/3 10:53
"""
type VerifyResultResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.VerifyResultResponse") {
	"""校验结果"""
	message:String
	"""校验code"""
	code:String
	"""订单内的商品skuId"""
	skuId:String
}
"""批次单请求线下支付结果
	异常code
	500-接口异常
	40001-购买渠道、支付渠道、终端下暂无可用收款账号
	APPLY_INVOICE_DIRECTLY_NO_ALLOW("30005", "当前订单不允许直接申请发票");
	<AUTHOR>
	@since 2022/5/12
"""
type BatchApplyOfflinePaymentAndApplyInvoiceResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.batchorder.BatchApplyOfflinePaymentAndApplyInvoiceResponse") {
	"""批次订单号"""
	batchOrderNo:String
	"""交易号（付款流水号）"""
	payFlowNo:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""批次单请求线下支付结果
	异常code
	500-接口异常
	40001-购买渠道、支付渠道、终端下暂无可用收款账号
	<AUTHOR>
	@since 2022/5/12
"""
type BatchApplyOfflinePaymentResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.batchorder.BatchApplyOfflinePaymentResponse") {
	"""批次订单号"""
	batchOrderNo:String
	"""交易号（付款流水号）"""
	payFlowNo:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""请求线上支付并申请发票结果
	APPLY_INVOICE_DIRECTLY_NO_ALLOW("30005", "当前订单不允许直接申请发票");
	<AUTHOR> create 2021/2/4 15:16
"""
type BatchApplyOnlinePaymentAndApplyInvoiceResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.batchorder.BatchApplyOnlinePaymentAndApplyInvoiceResponse") {
	"""批次单号"""
	batchNo:String
	"""是否允许开票"""
	isAllow:Boolean!
	"""付款地址"""
	payUrl:String
	"""支付网关支付模式
		<pre>
		0-同步
		1-重定向
		</pre>
	"""
	payMode:Int!
	"""请求支付结果"""
	result:Boolean!
	code:String
	"""错误信息"""
	message:String
}
"""请求线上支付结果
	<AUTHOR> create 2021/2/4 15:16
"""
type BatchApplyOnlinePaymentResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.batchorder.BatchApplyOnlinePaymentResponse") {
	"""批次单号"""
	batchNo:String
	"""付款地址"""
	payUrl:String
	"""支付网关支付模式
		<pre>
		0-同步
		1-重定向
		</pre>
	"""
	payMode:Int!
	"""请求支付结果"""
	result:Boolean!
	"""错误信息"""
	message:String
}
"""准备批次支付响应
	<AUTHOR> By Cb
	@date 2022/04/28
"""
type PrepareBatchPayResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.batchorder.PrepareBatchPayResponse") {
	"""支付渠道信息"""
	paymentChannelInfoList:[PaymentChannelInfoResponse]
	"""批次单信息"""
	batchOrderResult:BatchOrderResponse
}
"""准备支付的批次单信息
	<AUTHOR> By Cb
	@date 2022/04/28
"""
type BatchOrderResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.batchorder.PrepareBatchPayResponse$BatchOrderResponse") {
	"""批次单号"""
	batchOrderNo:String
	"""买家编号"""
	buyerId:String
	"""订单金额"""
	amount:BigDecimal
	"""订单状态
		0 - 未确认
		1 - 正常
		2 - 交易完成
		3 - 交易关闭
	"""
	state:BatchOrderState
	"""支付状态
		0 - 未支付
		1 - 支付中
		2 - 已支付
	"""
	paymentState:PaymentState
	"""订单创建时间"""
	createTime:DateTime
	"""发票信息"""
	invoiceResult:BatchOrderInvoiceResult
	"""终端
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
		H5端：H5
	"""
	purchaseChannelTerminal:String
	"""购买渠道类型：
		1-用户自主购买
		2-集体缴费
		3-管理员导入
		4-集体报名个人缴费
	"""
	purchaseChannelType:Int!
	"""订单总数"""
	orderTotal:Long!
}

scalar List
