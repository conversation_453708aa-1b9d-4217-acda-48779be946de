<route-params content="/:id"></route-params>
<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/basic-data/account/monographic-account' }">
        专题管理员账号管理
      </el-breadcrumb-item>
      <el-breadcrumb-item>账号详情</el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'

  @Component
  export default class extends Vue {}
</script>
