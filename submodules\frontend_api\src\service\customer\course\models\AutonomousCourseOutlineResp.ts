/**
 * 自主选课考核信息
 */
export class AutonomousCourseOutlineAssessSetting {
  // 选课配置id、例如assessId.UUID$00000002
  id: string
  // 选课配置名称、例如Course_Assess_003
  name: string
  // 要求学时
  requirePeriod: number
}

/**
 * 用户考核结果要求学时json结构
 */
export class UserAssessResultRequirePeriodResp {
  // 指标 => 需完成
  config: number
  // 已完成
  current: number
}

/**
 * 用户考核结果json结构
 */
export class UserAssessResultResp {
  // 考核id
  assessId: string
  // 考核名称
  name: string
  // 考核对象
  ownerId: string
  // 考核对象类型
  ownerType: number
  // 关联考核id集合
  relateAssessIds: string[]
  // 考核要求
  requirePeriod: UserAssessResultRequirePeriodResp = new UserAssessResultRequirePeriodResp()
}

/**
 * @description 自主选课大纲信息
 */
class AutonomousCourseOutlineResp {
  // 课程大纲id
  id: string
  // 课程名称
  name: string
  // 考核配置
  assessSetting?: AutonomousCourseOutlineAssessSetting = new AutonomousCourseOutlineAssessSetting()
  // 必学课程id集合
  compulsoryCourseIdList: string[]
  category: number
  // 子节点
  childOutlines: AutonomousCourseOutlineResp[] = []
  // 课程包id
  coursePackageId: string
  sort: number
}

export default AutonomousCourseOutlineResp
