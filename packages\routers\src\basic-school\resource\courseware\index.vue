<route-meta>
{ "isMenu": true, "title": "课件管理", "sort": 1, "icon": "icon-kejian" }
</route-meta>

<script lang="ts">
  import CourseWare from '@hbfe/jxjy-admin-courseware/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import {
    // 施教机构管理员
    WXGLY
  } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY],
    detail: [WXGLY],
    modify: [WXGLY],
    enable: [WXGLY],
    category: [WXGLY],
    preview: [WXGLY],
    remove: [WXGLY],
    create: [WXGLY],
    export: [WXGLY]
  })
  export default class extends CourseWare {}
</script>
<style lang="scss" scoped>
  .default {
    color: #999;
  }
  .el-form-item__content > div {
    width: 100%;
  }
</style>
