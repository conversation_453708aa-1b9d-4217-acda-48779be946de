import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-course-play-resource-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-course-play-resource-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 课件媒体资源申请参数
 */
export class CoursewareMediaApplyResourceRequest {
  /**
   * 课程学习播放凭证/课程试听凭证/课程预览凭证
   */
  coursePlayToken?: string
  /**
   * 章节编号
   */
  chapterId?: string
  /**
   * 课程编号
   */
  coursewareId?: string
  /**
   * 媒体资源编号
   */
  mediaResourceId?: string
}

/**
 * 课程试听凭证响应
<AUTHOR>
@since 2022/1/20
 */
export class CourseAuditionTokenResponse {
  /**
   * 申请结果
200 - 成功
30000 - 课程不存在
30001 - 不允许试听
500 - 内部异常
   */
  applyResult: TokenResponse
  /**
   * 课程试听凭证
   */
  token: string
}

export class CourseChapterPlayResourceResponse {
  /**
   * 课程章节id
   */
  id: string
  /**
   * 章节名称
   */
  name: string
  /**
   * 课程目录id
   */
  courseOutlineId: string
  /**
   * 课件id
   */
  coursewareId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否允许试听
   */
  allowAudition: boolean
}

export class CourseOutlinePlayResourceResponse {
  /**
   * 课程目录id
   */
  id: string
  /**
   * 课程id
   */
  cseId: string
  /**
   * 课程目录名称
   */
  name: string
  /**
   * 父级课程目录id
   */
  parentId: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 课程资源信息
 */
export class CoursePlayResourceResponse {
  /**
   * 课程id
   */
  id: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 课程简介内容id
   */
  aboutsContentId: string
  /**
   * 章节播放资源
   */
  courseChapterPlayResourceList: Array<CourseChapterPlayResourceResponse>
  /**
   * 课件资源
   */
  coursewarePlayResourceList: Array<CoursewarePlayResourceResponse>
  /**
   * 课程目录播放资源
   */
  courseOutlinePlayResourceList: Array<CourseOutlinePlayResourceResponse>
}

/**
 * 课程预览凭证响应
<AUTHOR>
@since 2022/1/20
 */
export class CoursePreviewTokenResponse {
  /**
   * 申请结果
200 - 成功
30000 - 课程不存在
30001 - 不允许预览
500 - 内部异常
   */
  applyResult: TokenResponse
  /**
   * 课程预览凭证
   */
  token: string
}

export class CoursewareMediaPlayConfigResponse {
  /**
   * 配置id
   */
  configId: string
  /**
   * 是否启用防录屏跑马灯
1-启用  0-不启用
   */
  enableEd: number
  /**
   * 是否启用视频贴片
1-启用 0-不启用
   */
  enableVideoPatch: number
  /**
   * 是否启用前置广告
1-启用 0-不启用
   */
  enablePreMedia: number
  /**
   * 是否启用后置广告
1-启用 0-不启用
   */
  enableRearMedia: number
  /**
   * 前置广告媒体地址
   */
  preMediaPath: string
  /**
   * 后置广告媒体地址
   */
  rearMediaPath: string
}

/**
 * 课件媒体播放资源
 */
export class CoursewareMediaPlayResourceResponse {
  /**
   * 课件媒体资源ID
   */
  id: string
  /**
   * 课件媒体资源类型
   */
  type: number
  /**
   * 媒体时长
   */
  timeLength: number
  /**
   * 课件目录集合
   */
  outlineList: Array<CoursewareOutlinePlayResourceResponse>
  /**
   * 文档播放资源
   */
  documentList: Array<DocumentPlayResourceResponse>
  /**
   * 视频信息
   */
  videoPlayResourceList: Array<VideoPlayResourceResponse>
  /**
   * 视频字幕
   */
  videoCaptionList: Array<VideoCaptionPlayResourceResponse>
  /**
   * 视频转码信息列表
   */
  videoTranscodingList: Array<VideoResourceResponse>
  /**
   * 视频讲义列表
   */
  lectureList: Array<LecturePlayResourceResponse>
  /**
   * 视频章节列表
   */
  videoChapterList: Array<VideoChapterPlayResourceResponse>
  /**
   * 播放配置
   */
  playConfig: CoursewareMediaPlayConfigResponse
  /**
   * 播放规则
   */
  playRule: CoursewareMediaPlayRuleResponse
}

export class CoursewareMediaPlayRuleResponse {
  /**
   * 规则id
   */
  ruleId: string
  /**
   * 试听时长
   */
  listenTime: number
}

/**
 * 课件媒体预览资源
 */
export class CoursewareMediaPreviewResourceResponse {
  /**
   * 课件媒体资源ID
   */
  id: string
  /**
   * 课件媒体资源类型
   */
  type: number
  /**
   * 媒体时长
   */
  timeLength: number
  /**
   * 文档播放资源
   */
  documentList: Array<DocumentPlayResourceResponse>
  /**
   * 视频信息
   */
  videoPlayResourceList: Array<VideoPlayResourceResponse>
  /**
   * 视频字幕
   */
  videoCaptionList: Array<VideoCaptionPlayResourceResponse>
  /**
   * 视频转码信息列表
   */
  videoTranscodingList: Array<VideoResourceResponse>
  /**
   * 视频讲义列表
   */
  lectureList: Array<LecturePlayResourceResponse>
}

export class CoursewareOutlinePlayResourceResponse {
  /**
   * 课件媒体资源目录ID
   */
  id: string
  /**
   * 课件媒体资源ID
   */
  cwmrId: string
  /**
   * 课件媒体资源目录名称
   */
  name: string
  /**
   * 排序
   */
  sort: number
  /**
   * 上级目录ID，0代表顶级
   */
  parentId: string
  /**
   * 所属模块类型，2代表视频，1代表文档
   */
  moduleType: number
}

export class CoursewarePlayResourceResponse {
  /**
   * 课件id
   */
  id: string
  /**
   * 课件名称
   */
  name: string
  /**
   * 媒体时长
   */
  timeLength: number
  /**
   * 媒体资源id
   */
  mediaResourceId: string
  /**
   * 课件简介内容id
   */
  aboutsContentId: string
  /**
   * 课件类型（文档 &#x3D; 1 视频 &#x3D; 2 媒体 &#x3D; 3）
   */
  type: number
}

export class DocumentPlayResourceResponse {
  /**
   * 文档id
   */
  id: string
  /**
   * 课件媒体资源ID
   */
  cwmrId: string
  /**
   * 媒体资源文档类型
   */
  type: number
  /**
   * 媒体资源文档路径
   */
  path: string
  /**
   * 排序
   */
  sort: number
  /**
   * 媒体资源文档大小，单位kb
   */
  size: number
}

export class LecturePlayResourceResponse {
  /**
   * 讲义ID
   */
  id: string
  /**
   * 课件媒体资源ID
   */
  cwmrId: string
  /**
   * 讲义类型，1代表html，2代表image
   */
  type: number
  /**
   * 播放时间点，单位秒
   */
  timePoint: number
  /**
   * 讲义路径
   */
  path: string
  /**
   * 所属模块类型
   */
  moduleType: number
  /**
   * 所属模块主键
   */
  moduleKey: string
}

/**
 * 凭证响应基类
<AUTHOR>
@since 2022/1/20
 */
export class TokenResponse {
  /**
   * 代码：
200-成功
   */
  code: string
  /**
   * 信息
   */
  message: string
}

export class VideoCaptionPlayResourceResponse {
  /**
   * 字幕ID
   */
  id: string
  /**
   * 课件媒体资源ID
   */
  cwmrId: string
  /**
   * 视频ID
   */
  vdoId: string
  /**
   * 字幕文件路径
   */
  path: string
  /**
   * 字幕类型，1表示SRT, 2表示Webvtt
   */
  captionType: number
}

export class VideoChapterPlayResourceResponse {
  /**
   * 章节ID
   */
  id: string
  /**
   * 课件媒体资源ID
   */
  cwmrId: string
  /**
   * 章节名称
   */
  name: string
  /**
   * 章节打开方式，1代表手动，2代表自动
   */
  openMode: number
  /**
   * 上级章节ID，0代表顶级
   */
  parentId: string
  /**
   * 排序
   */
  sort: number
  /**
   * 自定义拓展信息
   */
  expand: string
  /**
   * 所属模块类型,1代表视频模块
   */
  moduleType: number
  /**
   * 所属模块主键
   */
  moduleKey: string
  /**
   * 所属模块主键下的数据
   */
  moduleData: string
}

export class VideoPlayResourceResponse {
  /**
   * 视频ID
   */
  id: string
  /**
   * 课件媒体资源ID
   */
  cwmrId: string
  /**
   * 视频时长，单位秒
   */
  timeLength: number
}

/**
 * 视频资源信息
 */
export class VideoResourceResponse {
  /**
   * 视频转码编号
   */
  id: string
  /**
   * 视频ID
   */
  vdoId: string
  /**
   * 课件媒体资源ID
   */
  cwmrId: string
  /**
   * 视频播放路劲，相对路径
   */
  path: string
  /**
   * 视频清晰度
1:流畅普屏 2:标清普屏 3:高清普屏 4:流畅宽屏 5:标清宽屏 6:高清宽屏
负数是代表手机端对应的清晰度
   */
  clarity: number
  /**
   * 视频大小，单位kb
   */
  size: number
  /**
   * 资源类型，0表示八百里，1表示保利威视，2表示华为云，20表示华为云音频 4表示外链地址
   */
  resourceType: number
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请课程试听
   * @param courseId 课程编号
   * @return 课程试听凭证
   * @param mutate 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyCourseAudition(
    courseId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCourseAudition,
    operation?: string
  ): Promise<Response<CourseAuditionTokenResponse>> {
    return commonRequestApi<CourseAuditionTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请课程试听，不验证是否允许试听
   * @param courseId 课程编号
   * @return 课程试听凭证
   * @param mutate 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyCourseAuditionWithoutValidate(
    courseId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCourseAuditionWithoutValidate,
    operation?: string
  ): Promise<Response<CourseAuditionTokenResponse>> {
    return commonRequestApi<CourseAuditionTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请学习课程播放资源
   * @param coursePlayToken 课程学习播放凭证/课程试听凭证/课程预览凭证
   * @return 资源信息
   * @param mutate 查询 graphql 语法文档
   * @param coursePlayToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyCoursePlayResource(
    coursePlayToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCoursePlayResource,
    operation?: string
  ): Promise<Response<CoursePlayResourceResponse>> {
    return commonRequestApi<CoursePlayResourceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { coursePlayToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请课程预览
   * @param courseId 课程编号
   * @return 课程预览凭证
   * @param mutate 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyCoursePreview(
    courseId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCoursePreview,
    operation?: string
  ): Promise<Response<CoursePreviewTokenResponse>> {
    return commonRequestApi<CoursePreviewTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请学习课件播放防盗链
   * @param request 课件媒体播放盗源链申请参数
   * @return 课件媒体播放防盗链响应
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyCoursewareMediaPlayAntiTheftChainResource(
    request: CoursewareMediaApplyResourceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCoursewareMediaPlayAntiTheftChainResource,
    operation?: string
  ): Promise<Response<CoursewareMediaPlayResourceResponse>> {
    return commonRequestApi<CoursewareMediaPlayResourceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请学习课件播放资源
   * @param request 参数
   * @return 媒体资源信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyCoursewareMediaPlayResource(
    request: CoursewareMediaApplyResourceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCoursewareMediaPlayResource,
    operation?: string
  ): Promise<Response<CoursewareMediaPlayResourceResponse>> {
    return commonRequestApi<CoursewareMediaPlayResourceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请课件预览防盗链播放资源
   * @param coursewareId 课件id
   * @return 媒体资源信息
   * @param mutate 查询 graphql 语法文档
   * @param coursewareId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyCoursewareMediaPreviewAntiTheftChainResource(
    coursewareId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCoursewareMediaPreviewAntiTheftChainResource,
    operation?: string
  ): Promise<Response<CoursewareMediaPreviewResourceResponse>> {
    return commonRequestApi<CoursewareMediaPreviewResourceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { coursewareId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请课件预览
   * @param coursewareId 课件id
   * @return 媒体资源信息
   * @param mutate 查询 graphql 语法文档
   * @param coursewareId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyCoursewareMediaPreviewResource(
    coursewareId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCoursewareMediaPreviewResource,
    operation?: string
  ): Promise<Response<CoursewareMediaPreviewResourceResponse>> {
    return commonRequestApi<CoursewareMediaPreviewResourceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { coursewareId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 通过媒体资源ID申请学习课件防盗链播放资源
   * @param mediaIdResourceId 课件媒体资源Id
   * @return 课件媒体播放防盗链响应
   * @param mutate 查询 graphql 语法文档
   * @param mediaIdResourceId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyMediaPlayAntiTheftChainResource(
    mediaIdResourceId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyMediaPlayAntiTheftChainResource,
    operation?: string
  ): Promise<Response<CoursewareMediaPlayResourceResponse>> {
    return commonRequestApi<CoursewareMediaPlayResourceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { mediaIdResourceId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
