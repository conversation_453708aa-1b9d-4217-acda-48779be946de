import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'

/**
 * @description 课程大纲分类考核配置
 */
class ClassificationAssess {
  /**
   * id
   */
  id = ''
  /**
   * 考核名称
   */
  name = ''
  /**
   * 要求学时
   */
  requirePeriod = 0
  /**
   * 完成学时
   */
  completePeriod = 0
  /**
   * 随机Key
   */
  rangeKey = ''
  /**
   * 操作类型
   */
  operation = OperationTypeEnum.create
}

export default ClassificationAssess
