import abstractEnum from '@api/service/common/enums/AbstractEnum'

export enum SaleChannelEnum {
  /**
   * 网校
   */
  self,
  /**
   * 分销
   */
  distribution,
  /**
   * 专题
   */
  topic,

  /**
   * 华医
   */
  huayi
}
class SaleChannelType extends abstractEnum<SaleChannelEnum> {
  static enum = SaleChannelEnum
  constructor(status?: SaleChannelEnum) {
    super()
    this.current = status
    this.map.set(SaleChannelEnum.self, '网校')
    this.map.set(SaleChannelEnum.distribution, '分销')
    this.map.set(SaleChannelEnum.topic, '专题')
    this.map.set(SaleChannelEnum.huayi, '华医网')
  }

  getSpecialSubjectSaleChannel(SaleChannel: boolean) {
    if (typeof SaleChannel === 'boolean') {
      return SaleChannel ? [SaleChannelEnum.topic] : [SaleChannelEnum.self, SaleChannelEnum.distribution]
    } else {
      return undefined
    }
  }
}
export default new SaleChannelType()
