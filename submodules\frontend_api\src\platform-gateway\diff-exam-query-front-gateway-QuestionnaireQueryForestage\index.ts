import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/diff-exam-query-front-gateway-QuestionnaireQueryForestage'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'diff-exam-query-front-gateway-QuestionnaireQueryForestage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * @Description 管理端统计报表查询班级下课程教师集合条件
@Date 2025/4/25
 */
export class QuestionnaireAnswerTeachersRequest {
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 期别id
   */
  issueId?: string
}

export class OwnerInfo {
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  servicerType: number
  servicerId: string
}

/**
 * @Description
@Date 2025/4/25
 */
export class QuestionnaireAnswerAllTeacherResponse {
  /**
   * 线上教师集合
   */
  onlineTeachers: Array<QuestionnaireAnswerTeacherResponse>
  /**
   * 线上教师集合
   */
  offlineTeachers: Array<QuestionnaireAnswerTeacherResponse>
}

/**
 * @description: 教师信息
@author: sugs
@create: 2022-03-10 10:04
 */
export class QuestionnaireAnswerTeacherResponse {
  /**
   * 教师id
   */
  id: string
  /**
   * 数据归属信息
   */
  owner: OwnerInfo
  /**
   * 教师名称
   */
  name: string
  /**
   * 头像/照片
   */
  photo: string
  /**
   * 简介内容
   */
  aboutsContent: string
  /**
   * 性别 -1:未知 0:女 1:男
@see TeacherGenders
   */
  gender: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人id
   */
  createUserId: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询各班级下学习的课程教师集合包含期别中的教师集合和线上课程的教师集合
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseTeachersWithPeriodAndOnlineInMyself(
    request: QuestionnaireAnswerTeachersRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCourseTeachersWithPeriodAndOnlineInMyself,
    operation?: string
  ): Promise<Response<QuestionnaireAnswerAllTeacherResponse>> {
    return commonRequestApi<QuestionnaireAnswerAllTeacherResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
