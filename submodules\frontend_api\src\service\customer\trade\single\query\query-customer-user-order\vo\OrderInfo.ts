import ClosedOrder from './ClosedOrder'
import OrderTimeLine from './OrderTimeLine'
import { OrderDTO as GqlOrderDto } from '@api/gateway/PlatformTrade'
/**
 * 订单基本信息
 */
export default class OrderInfo {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 机构ID
   */
  trainingInstitutionId: string
  /**
   * 机构名称
   */
  trainingInstitutionName: string
  /**
   * 渠道商id
   */
  channelVendorId: string
  /**
   * 渠道商名称
   */
  channelVendorName: string

  /**
   * 订单总金额
   */
  totalAmount = 0
  /**
   * 主订单实付总价
   */
  orderTotalPayAmount = 0
  /**
      * 主订单状态
   &quot;等待付款&quot;, 1
   &quot;等待卖家确认款项&quot;, 2
   &quot;支付中&quot;, 8
   &quot;支付成功&quot;, 3
   &quot;发货中&quot;, 4
   &quot;发货部分失败&quot;, 9
   &quot;发货失败&quot;,10
   &quot;发货已完成&quot;, 5
   &quot;交易成功&quot;, 6
   &quot;交易关闭&quot;, 7
      */
  status: number

  /**
   * 订单关闭信息 只有在状态等于7的时候才有值
   */
  closedOrderInfo: ClosedOrder

  /**
   * 订单时间线
   */
  orderTimeLine: OrderTimeLine = new OrderTimeLine()

  /**
   * @description: 是否能支付
   * @param {*}
   * @return {*} true 可以发起支付
   */
  canDoPay() {
    if (this.status === 1 || this?.status === 8) {
      return true
    }
    return false
  }
}

/**
 * @description: 转换orderInfo对象工具类
 */
export class OrderInfoParse {
  private constructor() {
    // noop
  }

  static parseOrderInfo(gqlOrderDto: GqlOrderDto) {
    const orderInfo = new OrderInfo()
    this.parsePrimitiveType(orderInfo, gqlOrderDto)
    this.parseClosedOrder(orderInfo, gqlOrderDto)
    this.parseOrderTimeLine(orderInfo, gqlOrderDto)
    // .....
    return orderInfo
  }

  /**
   * @description: 解析基本类型
   */

  private static parsePrimitiveType(orderInfo: OrderInfo, gqlOrderDto: GqlOrderDto): void {
    orderInfo.orderNo = gqlOrderDto.orderNo
    orderInfo.channelVendorId = gqlOrderDto.channelVendorId
    orderInfo.channelVendorName = gqlOrderDto.channelVendorName
    orderInfo.orderTotalPayAmount = gqlOrderDto.orderTotalPayAmount
    orderInfo.status = gqlOrderDto.status
    orderInfo.totalAmount = gqlOrderDto.totalAmount
    orderInfo.trainingInstitutionId = gqlOrderDto.trainingInstitutionId
    orderInfo.trainingInstitutionName = gqlOrderDto.trainingInstitutionName
  }

  /**
   * @description: 解析订单关闭信息
   * @param {*}
   * @return {*}
   */

  private static parseClosedOrder(orderInfo: OrderInfo, gqlOrderDto: GqlOrderDto) {
    if (orderInfo.status !== 7) {
      return
    }
    const closedOrderInfo = new ClosedOrder()
    closedOrderInfo.closedType = gqlOrderDto.closedType
    closedOrderInfo.reason = gqlOrderDto.reason
    closedOrderInfo.reasonId = gqlOrderDto.reasonId
    orderInfo.closedOrderInfo = closedOrderInfo
  }

  /**
   * @description: 解析订单时间线
   * @param {*}
   * @return {*}
   */

  private static parseOrderTimeLine(orderInfo: OrderInfo, gqlOrderDto: GqlOrderDto) {
    const orderTimeLine = new OrderTimeLine()
    orderTimeLine.completeTime = gqlOrderDto.completeTime
    orderTimeLine.createTime = gqlOrderDto.createTime
    orderTimeLine.deliverTime = gqlOrderDto.deliverTime
    orderTimeLine.deliveredTime = gqlOrderDto.deliveredTime
    orderTimeLine.payFinishTime = gqlOrderDto.payFinishTime
    orderTimeLine.payTime = gqlOrderDto.payTime
    orderInfo.orderTimeLine = orderTimeLine
  }
}
