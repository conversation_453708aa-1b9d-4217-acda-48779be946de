import copyQuestionnaire from './mutates/copyQuestionnaire.graphql'
import createPaperPublishConfigure from './mutates/createPaperPublishConfigure.graphql'
import createPaperPublishConfigureCategory from './mutates/createPaperPublishConfigureCategory.graphql'
import disablePaperPublishConfigure from './mutates/disablePaperPublishConfigure.graphql'
import enablePaperPublishConfigure from './mutates/enablePaperPublishConfigure.graphql'
import previewAnswerPaper from './mutates/previewAnswerPaper.graphql'
import removePaperPublishConfigure from './mutates/removePaperPublishConfigure.graphql'
import removePaperPublishConfigureCategory from './mutates/removePaperPublishConfigureCategory.graphql'
import updatePaperPublishConfigure from './mutates/updatePaperPublishConfigure.graphql'
import updatePaperPublishConfigureCategory from './mutates/updatePaperPublishConfigureCategory.graphql'

export {
  copyQuestionnaire,
  createPaperPublishConfigure,
  createPaperPublishConfigureCategory,
  disablePaperPublishConfigure,
  enablePaperPublishConfigure,
  previewAnswerPaper,
  removePaperPublishConfigure,
  removePaperPublishConfigureCategory,
  updatePaperPublishConfigure,
  updatePaperPublishConfigureCategory
}
