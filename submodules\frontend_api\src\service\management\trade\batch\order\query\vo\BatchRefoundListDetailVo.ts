import { BatchReturnOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { BatchRefundTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
import { PaymentMethodEnum } from '@api/service/management/trade/batch/order/enum/PaymentMethod'

/**
 * @description 【集体报名退款列表】详情
 */
class BatchOrderListDetailVo {
  /**
   * 批次号
   */
  batchOrderNo = ''
  /**
   * 退款单号
   */
  refoundNo = ''
  /**
   * 交易流水号
   */
  flowNo = ''
  /**
   * 退款人数
   */
  refoundCount = 0
  /**
   * 实付金额
   */
  payAmount = 0
  /**
   * 退款金额
   */
  refoundAmount = 0
  /**
   * 购买人ID
   */
  buyerId = ''
  /**
   * 购买人姓名
   */
  buyerName = ''
  /**
   * 购买人账号
   */
  buyerAccount = ''

  /**
   * 退款申请时间
   */
  refoundDate: string
  /**
   * 审批时间
   */
  approvalDate: string
  /**
   * 退款成功时间
   */
  refoundSuccessDate: string

  /**
   * 退款状态
   */
  refundStatus: BatchRefundTradeStatusEnum

  /**
   * 销售渠道
   */
  saleChannel: number

  /**
   * 退款方式，1线上，2线下
   */
  refundOrderType = 0

  /**
   * 缴费方式
   */
  paymentMethod: PaymentMethodEnum = null

  static from(batchReturnOrderResponse: BatchReturnOrderResponse) {
    const batchOrderListDetailVo = new BatchOrderListDetailVo()
    batchOrderListDetailVo.batchOrderNo = batchReturnOrderResponse.batchOrderInfo.batchOrderNo
    batchOrderListDetailVo.refoundNo = batchReturnOrderResponse.batchReturnOrderNo
    batchOrderListDetailVo.flowNo = batchReturnOrderResponse.batchOrderInfo.paymentInfo.flowNo
    batchOrderListDetailVo.refoundCount = batchReturnOrderResponse.basicData.returnOrderCount
    batchOrderListDetailVo.payAmount = batchReturnOrderResponse.batchOrderInfo.paymentInfo.payAmount
    batchOrderListDetailVo.refoundAmount = batchReturnOrderResponse.basicData.refundAmount
    batchOrderListDetailVo.buyerId = batchReturnOrderResponse.batchOrderInfo.creator.userId
    batchOrderListDetailVo.refoundDate = batchReturnOrderResponse.basicData.batchReturnOrderStatusChangeTime?.created
    batchOrderListDetailVo.refoundSuccessDate =
      batchReturnOrderResponse.basicData.batchReturnOrderStatusChangeTime?.returnedAndRefunded
    batchOrderListDetailVo.approvalDate = batchReturnOrderResponse.approvalInfo.approveTime
    batchOrderListDetailVo.saleChannel = batchReturnOrderResponse.basicData.saleChannel
    batchOrderListDetailVo.refundOrderType = batchReturnOrderResponse.refundInfo.refundOrderType

    // 0: 已创建
    // 1: 已确认
    // 2: 取消申请中
    // 3: 退货处理中
    // 4: 退货失败
    // 5: 正在申请退款
    // 6: 已申请退款
    // 7: 退款处理中
    // 8: 退款失败
    // 9: 退货完成
    // 10: 退款完成
    // 11: 退货退款完成
    // 12: 已关闭

    switch (batchReturnOrderResponse.basicData.batchReturnOrderStatus) {
      case 0:
        batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.REFUNDING
        break
      case 1:
        batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.REFUNDING
        break
      case 2:
        batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.REFUNDING
        break
      case 3:
        batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.REFUNDDISPOSE
        break
      case 4:
        batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.RECYLING_FAILED
        break
      case 5:
        batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.REFUNDDISPOSE
        break
      case 6:
        batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.REFUND_PROCESSING
        break
      case 7:
        batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.REFUNDDISPOSE
        break
      case 8:
        batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.REFUNDFAIL
        break
      case 9:
        batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.REFUNDSUCCESS
        break
      case 10:
        batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.REFUNDSUCCESS
        break
      case 11:
        batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.REFUNDSUCCESS
        break
      case 12:
        if (batchReturnOrderResponse.basicData.closeReason.closeType === 2) {
          batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.REFUSEDREFUND
        } else {
          batchOrderListDetailVo.refundStatus = BatchRefundTradeStatusEnum.CANCELREFUND
        }
        break
      default:
        batchOrderListDetailVo.refundStatus = undefined
        break
    }
    // batchOrderListDetailVo.refundStatus = batchReturnOrderResponse.refundInfo.refundOrderStatus
    batchOrderListDetailVo.paymentMethod = batchReturnOrderResponse.batchOrderInfo.paymentInfo.paymentOrderType
    return batchOrderListDetailVo
  }
}

export default BatchOrderListDetailVo
