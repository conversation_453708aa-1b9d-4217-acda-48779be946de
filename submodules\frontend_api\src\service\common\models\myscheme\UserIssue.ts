import PracticeLearning from './PracticeLearning'
import Effectiveness from './Effectiveness'
import CourseLearning from '@api/service/common/models/myscheme/CourseLearning'
import IssueAssess from '@api/service/common/models/myscheme/IssueAssess'
import UserExamLearning from '@api/service/common/models/myscheme/UserExamLearning'
import IssueAchieve from '@api/service/common/models/myscheme/IssueAchieve'
import { SkuValueAndName } from '@api/service/common/models/common/SkuValueAndName'
import { InterestCourseSetting } from '@api/service/common/models/myscheme/InterestCourseSetting'
import { isNull } from 'lodash'
/**
 * 用户期数信息
 */
class UserIssue extends SkuValueAndName {
  /**
   * 学习方案id
   */
  schemeId = ''
  /**
   * 方案名称
   */
  schemeName = ''
  /**
   * 方案封面图片地址，已补充/mfs/
   */
  picture = ''
  /**
   * 年度
   */
  year = 0
  /**
   * 期数编号
   */
  issueId = ''
  /**
   * 期别名称
   */
  issueName = ''
  /**
   * 开始时间
   */
  startTime: Date
  /**
   * 结束时间
   */
  endTime: Date
  /**
   * 期数预约时间
   */
  appointment: Date
  /**
   * 培训成果
   */
  achieve: IssueAchieve = new IssueAchieve()
  /**
   * 期数考核情况
   */
  userAssess: IssueAssess = new IssueAssess()
  /**
   * 课程学习方式
   */
  courseLearning: CourseLearning = new CourseLearning()
  /**
   * 考试学习方式
   */
  examLearning: UserExamLearning = new UserExamLearning()
  /**
   * 练习学习方式
   */
  practiceLearning: PracticeLearning = new PracticeLearning()
  /**
   * 期数有效性
   */
  effectiveness: Effectiveness = new Effectiveness()
  /**
   * 是否提供重学
   */
  enableRelearn: boolean
  /**
   * 是否有兴趣课
   */
  interestCourseSetting: InterestCourseSetting

  /**
   * 是否配置培训成果
   */
  hasAchieve() {
    return !!this.achieve
  }

  /**
   * 是否包含考试学习方式
   */
  hasExamLearning() {
    return !!this.examLearning?.learningId
  }

  /**
   * 是否包含练习学习方式
   */
  hasPracticeLearning() {
    return !!this.practiceLearning?.learningId
  }

  /**
   * 是否开放课程学习
   */
  hasCourseStudy() {
    return !!this.courseLearning?.learningId
  }
  /**
   * 是否有兴趣课
   */
  hasInterestCourse() {
    return !!this.interestCourseSetting
  }

  /**
   * 期数培训时间是否已结束
   * true 结束
   */
  hasIssueFinish() {
    if (!this.endTime) {
      return false
    }
    return this.endTime.getTime() < new Date().getTime()
  }

  /**
   * 培训时间启动
   * true：还没开始
   */
  hasIssueStart() {
    if (!this.startTime) {
      return false
    }
    return this.startTime.getTime() > new Date().getTime()
  }

  /**判断是不是长期培训 */
  isTrainingOfLongTime() {
    if (isNull(this.startTime) && isNull(this.endTime)) {
      return true
    }
    return false
  }
}

export default UserIssue
