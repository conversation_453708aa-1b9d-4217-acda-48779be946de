import MsAccountGateway from '@api/ms-gateway/ms-account-v1'
import Basicdata from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { ResponseStatus } from '@hbfe/common'
import CreateSubAdminRequestVo from './vo/CreateSubAdminRequestVo'

import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
import platformJxjyDistributor, {
  CreateDistributorAdminRequest
} from '@api/platform-gateway/platform-jxjy-distributor-admin-v1'

/**
 * 创建系统管理员
 */
class CreateSystemManager {
  createSubAdminParams = new CreateSubAdminRequestVo()

  async doCreateSystemManager(): Promise<ResponseStatus> {
    const { status } = await MsAccountGateway.registerSubAdmin(this.createSubAdminParams)
    return status
  }

  async doCreateOnlineSchoolSubAdmin() {
    const response = await Basicdata.createOnlineSchoolSubAdmin(
      CreateSubAdminRequestVo.toCreateOnlineSchoolSubAdminRequest(this.createSubAdminParams)
    )
    return response
  }
  /**
   *
   * @returns 8.0网校使用 --- 多网校
   */
  async createOnlineSchoolSubAdminByToken() {
    let token = ''
    if (QueryManagerDetail.hasCategory(CategoryEnums.fxs)) {
      // 分销商创建管理员切换token
      token = CommonConfigCenter.getFrontendApplication(frontendApplication.distributionAdministratorPwdLoginToken)
    } else {
      token = CommonConfigCenter.getFrontendApplication(frontendApplication.superLoginToken)
    }
    const param = CreateSubAdminRequestVo.toCreateOnlineSchoolSubAdminRequestV2(this.createSubAdminParams)
    param.token = token
    const response = await Basicdata.createOnlineSchoolSubAdminByToken(param)
    return response
  }

  /**
   * 创建分销商管理员
   */
  async createDistributorAdmin() {
    const token = CommonConfigCenter.getFrontendApplication(frontendApplication.distributionAdministratorPwdLoginToken)
    const params = CreateSubAdminRequestVo.toCreateDistributorAdminRequest(this.createSubAdminParams)
    params.token = token
    return platformJxjyDistributor.createDistributorAdmin(params)
  }
}

export default CreateSystemManager
