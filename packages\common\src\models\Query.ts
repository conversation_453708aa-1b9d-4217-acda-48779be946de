import Page from '@hbfe/jxjy-admin-common/src/models/Page'
import { Message } from 'element-ui'

export interface PageResult {
  data: Array<any>
  page: PageResultPage
}

interface PageResultPage {
  totalSize: number
}

abstract class Query<T = any> {
  constructor(params?: any, getPage?: (queryParams?: any) => Promise<PageResult>) {
    this.params = params
    if (getPage) {
      this.getPage = getPage
    }

    this.page = new Page({
      currentChange: async () => {
        await this.search()
      },
      pageSizeChange: async () => {
        await this.search()
      }
    })
  }

  page: Page
  params: any = {}
  loading = false
  exporting = false
  form: any = {}
  pageData: Array<T> = new Array<T>()

  doExport(queryParams?: any): Promise<any> {
    return {} as Promise<any>
  }

  abstract getPage(queryParams?: any): Promise<PageResult>

  setForm(form: any) {
    this.form = form
  }

  async reset() {
    console.log(this.form)
    this.form.resetFields()
    await this.search()
  }

  async search() {
    this.loading = true
    try {
      const pageResult = await this.getPage({
        query: this.params,
        page: this.page.params()
      })
      this.pageData = pageResult.data
      this.page.total = pageResult.page.totalSize
    } catch (e) {
      // todo
      console.error(e)
    } finally {
      this.loading = false
    }
  }

  async searchBase() {
    this.page.pageNo = 1
    await this.search()
  }

  async export() {
    this.exporting = true
    try {
      await this.doExport({
        query: this.params,
        page: this.page.params()
      })
      Message.success('导出数据成功!')
    } catch (e) {
      // todo
      Message.success('导出数据失败!')
    } finally {
      this.exporting = false
    }
  }
}

export default Query
