<template>
  <el-container>
    <el-aside width="240px">
      <!--侧边栏按钮 展开时显示-->
      <a href="#" class="aside-btn el-icon-s-fold"></a>
      <!--侧边栏按钮 收起时显示-->
      <!--<a href="#" class="aside-btn el-icon-s-unfold"></a>-->
      <div class="logo">
        <span class="logo-txt">通用平台运营管理后台</span>
      </div>
      <div class="user-info">
        <img class="photo" src="./assets/images/default-photo.jpg" alt=" " />
        <p class="name">张某某</p>
        <div class="op-btn f-mt10">
          <el-button type="primary" size="mini" round plain>
            <i class="el-icon-s-tools"></i>
            帐号设置
          </el-button>
        </div>
      </div>
      <el-menu default-active="1" class="aside-nav" unique-opened="true" @open="handleOpen" @close="handleClose">
        <el-menu-item index="0">
          <template slot="title">
            <i class="iconfont icon-weiwangxiao"></i>
            <span>开通网校</span>
          </template>
        </el-menu-item>
        <!--网校管理-->
        <el-menu-item index="1">
          <template slot="title">
            <i class="hb-iconfont icon-setup"></i>
            <span>网校管理</span>
          </template>
        </el-menu-item>
      </el-menu>
      <div class="m-company-info">
        <p>福建华博教育科技股份有限公司</p>
        <a class="a-txt" href="http://beian.miit.gov.cn/" target="_blank">闽ICP备2021002737号</a>
      </div>
    </el-aside>
    <el-container>
      <el-header height="100px" class="f-flex">
        <ul class="header-nav f-flex-sub">
          <li class="nav-item current">
            <i class="iconfont icon-weiwangxiao"></i>
            <span class="txt">网校管理</span>
          </li>
          <li class="current-bg" style="min-width: 124px;"></li>
        </ul>
        <ul class="header-nav">
          <li class="nav-item nav-item-1"><i class="iconfont icon-tuichu"></i>退出</li>
        </ul>
      </el-header>
      <div class="tags">
        <span class="prev"><i class="el-icon el-icon-arrow-left"></i></span>
        <div class="tags-bd">
          <div class="tags-items" style="width: 500%;">
            <el-tag class="current" closable>网校管理</el-tag>
          </div>
        </div>
        <span class="next"><i class="el-icon el-icon-arrow-right"></i></span>
        <el-dropdown :hide-on-click="false">
          <span class="more"><i class="el-icon el-icon-more"></i></span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>黄金糕</el-dropdown-item>
            <el-dropdown-item>狮子头</el-dropdown-item>
            <el-dropdown-item>螺蛳粉</el-dropdown-item>
            <el-dropdown-item disabled>双皮奶</el-dropdown-item>
            <el-dropdown-item divided>蚵仔煎</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <el-main>
        <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
          <p>温馨提示：</p>
          <p>1.若是需开通新网校， 请输入点击【<a class="f-cb" href="#">开通网校</a>】进行配置网校开通信息。</p>
        </el-alert>
        <!--顶部tab标签-->
        <el-tabs v-model="activeName" class="m-tab-top is-sticky">
          <el-tab-pane label="已创建网校" name="first">
            <div class="f-p15">
              <el-card shadow="never" class="m-card">
                <!--条件查询-->
                <el-row :gutter="16" class="m-query is-border-bottom">
                  <el-form :inline="true" label-width="auto">
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="网校名称">
                        <el-input v-model="input" clearable placeholder="请输入网校名称" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="服务地区">
                        <el-cascader :options="options" :props="props" collapse-tags clearable></el-cascader>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="培训行业">
                        <el-select v-model="select" clearable placeholder="请选择网校培训行业">
                          <el-option value="全部"></el-option>
                          <el-option value="人社行业"></el-option>
                          <el-option value="建设行业"></el-option>
                          <el-option value="职业卫生行业"></el-option>
                          <el-option value="工勤行业"></el-option>
                          <el-option value="教师行业"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="网校状态">
                        <el-select v-model="select" clearable placeholder="请选择网校状态">
                          <el-option value="全部"></el-option>
                          <el-option value="使用中（网校属于有效期内的）"></el-option>
                          <el-option value="停用（执行了停用操作的）"></el-option>
                          <el-option value="到期（时间已到期的）"></el-option>
                          <el-option value="开通中（网校正在开通处理中的状态）"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="业主单位">
                        <el-input v-model="input" clearable placeholder="请输入业主单位名称" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="网校性质">
                        <el-select v-model="select" clearable placeholder="请选择网校性质">
                          <el-option value="全部"></el-option>
                          <el-option value="DEMO"></el-option>
                          <el-option value="正式实施"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="开通时间">
                        <el-date-picker
                          v-model="value1"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                        >
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="服务期限">
                        <el-date-picker v-model="value1" type="date" class="f-wf" placeholder="请选择服务期限">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="服务到期时间">
                        <el-date-picker
                          v-model="value1"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                        >
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="提供终端">
                        <el-select v-model="select" clearable placeholder="请选择提供终端">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="是否延长服务">
                        <el-select v-model="select" clearable placeholder="请选择是否延长服务">
                          <el-option value="全部"></el-option>
                          <el-option value="是"></el-option>
                          <el-option value="否"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                      <el-form-item class="f-tr">
                        <el-button type="primary">查询</el-button>
                        <el-button>重置</el-button>
                        <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                        <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
                <!--操作栏-->
                <el-alert type="warning" :closable="false" class="m-alert f-mb15">
                  <div class="f-c6">
                    当前共已开通 <span class="f-co f-fb">Z个</span> 网校，其中
                    <span class="f-co f-fb">使用中A个，停用B个，已到期C个</span>
                  </div>
                </el-alert>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column width="100">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <div class="tag-round">正式实施</div>
                      </div>
                      <div v-else>
                        <div class="tag-round is-bg-gray">DMEO</div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="网校信息" min-width="300">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <p class="f-fb">福建省专业技术人员继续教育培训平台</p>
                        <p class="f-c9 f-f13">域名：https://zjpx.59iedu.com</p>
                        <p class="f-c9 f-f13">业主：（华博教育）福建华博教育科技股份有限公司</p>
                        <p class="f-c9 f-f13">
                          <el-tag size="small" class="f-mr10">人社行业</el-tag>
                          <el-tag size="small" class="f-mr10">建设行业</el-tag>
                        </p>
                        <p class="f-c9 f-f13">
                          <el-tag type="info" size="small" class="f-mr10">福建省</el-tag
                          ><el-tag type="info" size="small" class="f-mr10">安徽省</el-tag>
                        </p>
                      </div>
                      <div v-else>
                        <p class="f-fb">福建省专业技术人员继续教育培训平台</p>
                        <p class="f-c9 f-f13">域名：https://zjpx.59iedu.com</p>
                        <p class="f-c9 f-f13">业主：（华博教育）福建华博教育科技股份有限公司</p>
                        <p class="f-c9 f-f13">
                          <el-tag size="small" class="f-mr10">人社行业</el-tag>
                          <el-tag size="small" class="f-mr10">建设行业</el-tag>
                        </p>
                        <p class="f-c9 f-f13">
                          <el-tag type="info" size="small" class="f-mr10">全国</el-tag>
                        </p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="提供终端" min-width="120">
                    <template>
                      <div><el-tag type="warning" size="small">PC端</el-tag></div>
                      <div><el-tag type="warning" size="small">H5</el-tag></div>
                    </template>
                  </el-table-column>
                  <el-table-column label="服务期限" min-width="120">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <div><el-tag type="warning" size="small">长期培训</el-tag></div>
                      </div>
                      <div v-else>
                        <div><el-tag type="warning" size="small">指定期限</el-tag></div>
                        <div class="f-c9 f-f13 f-mt5">2023.12.31到期</div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="状态" min-width="120">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="success" class="badge-status">使用中</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 2">
                        <el-badge is-dot type="warning" class="badge-status">到期</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 3">
                        <el-badge is-dot type="warning" class="badge-status">开通中</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="primary" class="badge-status">在服</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="开通时间" min-width="180">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="操作" width="100" align="center" fixed="right">
                    <template>
                      <el-button type="text" size="mini">修改</el-button>
                      <el-button type="text" size="mini">增值服务</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-card>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        activeNames: ['3'],
        props: { multiple: true },
        radio: 3,
        radio1: '1',
        input: '',
        select: '',
        checked: true,
        checked2: false,
        checked3: false,
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        visible: false,
        fits: ['cover'],
        options: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'zujian',
            label: '组件',
            children: [
              {
                value: 'basic',
                label: 'Basic',
                children: [
                  {
                    value: 'layout',
                    label: 'Layout 布局'
                  },
                  {
                    value: 'color',
                    label: 'Color 色彩'
                  },
                  {
                    value: 'typography',
                    label: 'Typography 字体'
                  },
                  {
                    value: 'icon',
                    label: 'Icon 图标'
                  },
                  {
                    value: 'button',
                    label: 'Button 按钮'
                  }
                ]
              },
              {
                value: 'form',
                label: 'Form',
                children: [
                  {
                    value: 'radio',
                    label: 'Radio 单选框'
                  },
                  {
                    value: 'checkbox',
                    label: 'Checkbox 多选框'
                  },
                  {
                    value: 'input',
                    label: 'Input 输入框'
                  },
                  {
                    value: 'input-number',
                    label: 'InputNumber 计数器'
                  },
                  {
                    value: 'select',
                    label: 'Select 选择器'
                  },
                  {
                    value: 'cascader',
                    label: 'Cascader 级联选择器'
                  },
                  {
                    value: 'switch',
                    label: 'Switch 开关'
                  },
                  {
                    value: 'slider',
                    label: 'Slider 滑块'
                  },
                  {
                    value: 'time-picker',
                    label: 'TimePicker 时间选择器'
                  },
                  {
                    value: 'date-picker',
                    label: 'DatePicker 日期选择器'
                  },
                  {
                    value: 'datetime-picker',
                    label: 'DateTimePicker 日期时间选择器'
                  },
                  {
                    value: 'upload',
                    label: 'Upload 上传'
                  },
                  {
                    value: 'rate',
                    label: 'Rate 评分'
                  },
                  {
                    value: 'form',
                    label: 'Form 表单'
                  }
                ]
              },
              {
                value: 'data',
                label: 'Data',
                children: [
                  {
                    value: 'table',
                    label: 'Table 表格'
                  },
                  {
                    value: 'tag',
                    label: 'Tag 标签'
                  },
                  {
                    value: 'progress',
                    label: 'Progress 进度条'
                  },
                  {
                    value: 'tree',
                    label: 'Tree 树形控件'
                  },
                  {
                    value: 'pagination',
                    label: 'Pagination 分页'
                  },
                  {
                    value: 'badge',
                    label: 'Badge 标记'
                  }
                ]
              },
              {
                value: 'notice',
                label: 'Notice',
                children: [
                  {
                    value: 'alert',
                    label: 'Alert 警告'
                  },
                  {
                    value: 'loading',
                    label: 'Loading 加载'
                  },
                  {
                    value: 'message',
                    label: 'Message 消息提示'
                  },
                  {
                    value: 'message-box',
                    label: 'MessageBox 弹框'
                  },
                  {
                    value: 'notification',
                    label: 'Notification 通知'
                  }
                ]
              },
              {
                value: 'navigation',
                label: 'Navigation',
                children: [
                  {
                    value: 'menu',
                    label: 'NavMenu 导航菜单'
                  },
                  {
                    value: 'tabs',
                    label: 'Tabs 标签页'
                  },
                  {
                    value: 'breadcrumb',
                    label: 'Breadcrumb 面包屑'
                  },
                  {
                    value: 'dropdown',
                    label: 'Dropdown 下拉菜单'
                  },
                  {
                    value: 'steps',
                    label: 'Steps 步骤条'
                  }
                ]
              },
              {
                value: 'others',
                label: 'Others',
                children: [
                  {
                    value: 'dialog',
                    label: 'Dialog 对话框'
                  },
                  {
                    value: 'tooltip',
                    label: 'Tooltip 文字提示'
                  },
                  {
                    value: 'popover',
                    label: 'Popover 弹出框'
                  },
                  {
                    value: 'card',
                    label: 'Card 卡片'
                  },
                  {
                    value: 'carousel',
                    label: 'Carousel 走马灯'
                  },
                  {
                    value: 'collapse',
                    label: 'Collapse 折叠面板'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ]
      }
    },
    methods: {
      onPreview() {
        this.$refs.preview.clickHandler()
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
