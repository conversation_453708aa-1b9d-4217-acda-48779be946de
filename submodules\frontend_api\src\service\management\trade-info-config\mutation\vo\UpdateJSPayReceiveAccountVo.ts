import UpdateReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/UpdateReceiveAccountVo'
import {
  CIBPayEncryptionKeyDataResponse,
  ReceiveAccountConfigResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import {
  CreateReceiveAccountRequest,
  ReceiveAccountExtProperty,
  UpdateReceiveAccountRequest
} from '@api/ms-gateway/ms-trade-configuration-v1'
import { CCBPayEncryptionKeyDataResponse } from './CCBPayEncryptionKeyData'

export default class UpdateXYPayReceiveAccountVo extends UpdateReceiveAccountVo {
  /**
   * 商户柜台代码
   */
  posId = ''
  /**
   * 分行代码
   */
  branchId = ''
  /**
   * 建行网银支付接口的公钥
   */
  jsPublicKey = ''
  /**
   * 建行的操作员账号不能为空
   */
  operator = ''
  /**
   * 建行操作员的登陆密码
   */
  password = ''
  /**
   * 是否使用防钓鱼,如果1表示使用防钓鱼接口,其他则不使用
   */
  phishing = 0
  /**
   * 小程序/公众号的 APPID 当前调起支付的小程序/公众号 APPID
   */
  jsSubAppid = ''
  /**
   * 文件证书路径
   */
  certFilePath = ''
  /**
   * 文件证书密码
   */
  certPassword = ''

  from(res: ReceiveAccountConfigResponse) {
    this.id = res.id
    this.accountType = res.accountType
    this.accountNo = res.accountNo
    this.accountName = res.name
    this.taxPayerId = res.taxPayerId
    this.refundWay = res.returnType
    this.paymentChannelId = res.paymentChannelId
    this.qrScanPrompt = res.qrScanPrompt
    if (res.encryptionKeyData.encryptionKeyType === 'CCB_PAY') {
      const temp = res.encryptionKeyData as CCBPayEncryptionKeyDataResponse

      this.posId = temp.POS_ID
      this.branchId = temp.BRANCH_ID
      this.jsPublicKey = temp.PUBLIC_KEY
      this.operator = temp.OPERATOR
      this.password = temp.PASSWORD
      this.phishing = Number(temp.PHISHING)
      this.jsSubAppid = temp.SUB_APP_ID
      this.certFilePath = temp.CERT_FILE_PATH
      this.certPassword = temp.CERT_PASS_WORD
    }
  }

  to(): CreateReceiveAccountRequest {
    const updateReceiveAccountRequest = new UpdateReceiveAccountRequest()
    updateReceiveAccountRequest.receiveAccountId = this.id
    updateReceiveAccountRequest.name = this.accountName
    updateReceiveAccountRequest.qrScanPrompt = this.qrScanPrompt
    updateReceiveAccountRequest.refundWay = this.refundWay
    updateReceiveAccountRequest.taxPayerId = this.taxPayerId
    updateReceiveAccountRequest.properties = new Array<ReceiveAccountExtProperty>()
    this.updateProperties('posId', this.posId, updateReceiveAccountRequest.properties)
    this.updateProperties('branchId', this.branchId, updateReceiveAccountRequest.properties)
    this.updateProperties('publicKey', this.jsPublicKey, updateReceiveAccountRequest.properties)
    this.updateProperties('operator', this.operator, updateReceiveAccountRequest.properties)
    this.updateProperties('password', this.password, updateReceiveAccountRequest.properties)
    this.updateProperties('phishing', this.phishing.toString(), updateReceiveAccountRequest.properties)
    this.updateProperties('subAppid', this.jsSubAppid, updateReceiveAccountRequest.properties)
    this.updateProperties('certFilePath', this.certFilePath, updateReceiveAccountRequest.properties)
    this.updateProperties('certPassword', this.certPassword, updateReceiveAccountRequest.properties)
    return updateReceiveAccountRequest
  }

  private updateProperties(propertyName: string, propertyValue: string, properties: Array<ReceiveAccountExtProperty>) {
    const property = properties?.find((item) => item.name === propertyName)
    if (property) {
      property.value = propertyValue
    } else {
      const item = new ReceiveAccountExtProperty()
      item.name = propertyName
      item.value = propertyValue
      properties.push(item)
    }
    return properties
  }
}
