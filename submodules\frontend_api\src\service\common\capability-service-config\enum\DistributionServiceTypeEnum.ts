import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * 增值服务枚举
 */
export enum DistributionServiceTypeEnum {
  /**
   * 基础版
   */
  basic = 0,
  /**
   * 专业版
   */
  professional
}

class DistributionServiceType extends AbstractEnum<DistributionServiceTypeEnum> {
  static enum = DistributionServiceTypeEnum

  constructor(status?: DistributionServiceTypeEnum) {
    super()
    this.current = status
    this.map.set(DistributionServiceTypeEnum.basic, '基础版')
    this.map.set(DistributionServiceTypeEnum.professional, '专业版')
  }
}

export default DistributionServiceType
