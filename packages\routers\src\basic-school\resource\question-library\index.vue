<route-meta>
{
"isMenu": true,
"title": "题库管理",
"sort": 4,
"icon": "icon-tiku"
}
</route-meta>
<script lang="ts">
  import QuestionLibrary from '@hbfe/jxjy-admin-questionLibrary/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY],
    create: [WXGLY],
    detail: [WXGLY],
    remove: [WXGLY],
    modify: [WXGLY]
  })
  export default class extends QuestionLibrary {
    //todo
  }
</script>
