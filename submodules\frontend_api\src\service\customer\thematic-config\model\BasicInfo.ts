import { DomainTypeEnum } from '../enum/DomainType'
import { SubjectType } from '../enum/SubjectType'
export class TemplateListItemType {
  id = ''
  /**
   * H5模板URL
   */
  h5Url = ''
  /**
   * web模板URL
   */
  webUrl = ''
  constructor(id: string, h5Url: string, webUrl: string) {
    this.id = id
    this.h5Url = h5Url
    this.webUrl = webUrl
  }
}
/**
 * 基础信息类
 */
export default class BasicInfo {
  /**
   * 专题入口名称
   */
  entryName = ''
  /**
   * 专题名称
   */
  subjectName = ''
  /**
   * 网校名称
   */
  onlineSchoolName = ''
  /**
   * 网校域名
   */
  netSchoolDoMain = ''
  /**
   * H5网校域名
   */
  netSchoolH5DoMain = ''
  /**
   * 域名类型
   */
  domainType: DomainTypeEnum = undefined
  /**
   * 专题域名
   */
  subjectDomain = ''
  /**
   * 专题类型
   */
  subjectType: Array<SubjectType> = new Array<SubjectType>()
  /**
   * 地区
   */
  suiteAreas: string[] = []
  /**
   * 行业
   */
  suiteIndustrys: string[] = []
  /**
   * 专题单位
   */
  suiteUnit = ''
  /**
   * 是否显示在网校
   */
  displayInSchool = true
  /**
   * web专题模板
   * 编号 Temlpate-Web-01
   * 编号 Temlpate-Web-02
   * 由UI决定编号值
   */
  templateWeb = ''
  /**
   * H5专题模板
   * 编号 Temlpate-H5-01
   * 编号 Temlpate-H5-02
   * 由UI决定编号值
   */
  templateH5 = ''
  /**
   * 专题是否允许访问
   */
  allowAccess = true
}
