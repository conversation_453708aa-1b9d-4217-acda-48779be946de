<template>
  <el-cascader
    ref="elCascaderRef"
    :props="props"
    v-model="selctValue"
    :placeholder="placeholder"
    :clearable="clearable"
    collapse-tags
    @change="onInput"
  ></el-cascader>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Emit, Watch, Ref } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import QueryTrainingMajor from '@api/service/common/basic-data-dictionary/query/QueryTrainingMajor'
  import TrainingMajorVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingMajorVo'
  import QueryTrainingCategory from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryTrainingCategory'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import MajorParam from '@api/service/common/basic-data-dictionary/query/vo/majorParam'
  import QueryPersonIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustry'

  @Component
  export default class extends Vue {
    @Prop({
      default: true
    })
    clearable: boolean

    // 传入的必须是数组
    @Prop({
      type: Array
    })
    value: string[]

    @Prop({
      default: '请选择培训专业',
      type: String
    })
    placeholder: string

    @Prop({
      default: false
    })
    checkStrictly: boolean

    //行业属性id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string
    @Prop({
      type: String,
      default: ''
    })
    industryId: string //行业id

    @Ref('elCascaderRef') elCascaderRef: any

    // 培训类别列表
    trainingMajorOptions: Array<TrainingMajorVo> = new Array<TrainingMajorVo>()
    // 培训专业列表
    trainingMajorList: Array<TrainingCategoryVo>

    // 当前选中的值
    selctValue: string[] = []
    props = {}

    @Watch('value', {
      immediate: true,
      deep: true
    })
    setValue(val: string[]) {
      if (val?.length) {
        this.selctValue = val
      }
    }

    onInput(values: string[]) {
      if (values?.length) {
        this.selctValue = values
        this.$emit('input', values)
      } else {
        this.selctValue = new Array<string>()
        this.$emit('input', [])
      }
    }

    async created() {
      await this.setProps()
    }
    async setProps() {
      const that = this as any
      this.props = {
        lazy: true,
        checkStrictly: this.checkStrictly,
        async lazyLoad(node: any, resolve: (val: any) => {}) {
          if (node.level < 1) {
            const res = await QueryPersonIndustry.getOperationTraining(that.industryId)
            const temp = cloneDeep(res)
            const nodes = temp?.map((sub) => {
              return {
                value: sub.propertyId,
                label: sub.name,
                leaf: false
              }
            })
            resolve(nodes)
          } else {
            if (node.level == 1) {
              const res = await QueryPersonIndustry.getIndustryDetail(node.value)
              const arr = cloneDeep(res)
              const nodes = arr?.map((sub) => {
                return {
                  value: sub.propertyId,
                  label: sub.name,
                  leaf: true
                }
              })
              if (nodes?.length) {
                resolve(nodes)
              } else {
                node.hasChildren = false
                node.data.leaf = true
                resolve([] as string[])
              }
              // resolve(nodes)
            } else {
              resolve(undefined)
            }
          }
        }
      }
    }
  }
</script>
