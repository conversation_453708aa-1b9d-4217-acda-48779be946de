<template>
  <el-card shadow="never" class="m-card f-mb15">
    <!--条件查询-->
    <el-row :gutter="16" class="m-query">
      <el-form :inline="true" label-width="auto">
        <el-col :sm="12" :md="8" :xl="6">
          <el-input v-model="pageQueryParam.orderNoList" clearable placeholder="请输入集体报名批次号" />
        </el-col>
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item label="开票状态">
            <el-select v-model="pageQueryParam.invoiceStatusList" clearable filterable placeholder="请选择">
              <el-option v-for="item in blueInvoiceStatus" :label="item.name" :value="item.value" :key="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item>
            <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <!--表格-->
    <el-table
      stripe
      :data="pageData"
      ref="invoiceOfflineRef"
      v-loading="query.loading"
      max-height="500px"
      class="m-table"
    >
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="集体报名批次号" prop="associationId" min-width="220" fixed="left">
        <!-- <template>2112071509467489926</template> -->
      </el-table-column>
      <el-table-column label="退款状态" min-width="130">
        <template slot-scope="scope">
          <el-badge
            is-dot
            :type="refundStatusMapType[scope.row.orderReturnStatus]"
            class="badge-status"
            v-if="refundStatusMapName[scope.row.orderReturnStatus]"
          >
            {{ refundStatusMapName[scope.row.orderReturnStatus] }}</el-badge
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="付款金额(元)" width="140" align="right">
        <template slot-scope="scope">
          {{ scope.row.payAmount || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="开票金额(元)" width="140" align="right">
        <template slot-scope="scope">
          {{ scope.row.totalAmount || 0 }}
        </template>
      </el-table-column>

      <el-table-column label="购买人信息" min-width="240">
        <template slot-scope="scope">
          <p>姓名：{{ scope.row.name || '-' }}</p>
          <p>证件号：{{ scope.row.idCard || '-' }}</p>
          <p>手机号：{{ scope.row.phone || '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="发票抬头" min-width="300">
        <template slot-scope="scope">【{{ invoiceTitleMapType[scope.row.titleType] }}】{{ scope.row.title }}</template>
      </el-table-column>
      <el-table-column label="统一社会信用代码" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.taxpayerNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="申请开票时间" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.applyForDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="发票状态" min-width="130">
        <template slot-scope="scope">
          <el-badge
            is-dot
            :type="invoiceStatusMapType[scope.row.invoiceStatus]"
            class="badge-status"
            v-if="invoiceStatusMapName[scope.row.invoiceStatus]"
          >
            {{ invoiceStatusMapName[scope.row.invoiceStatus] }}
          </el-badge>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="开票时间" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.invoiceDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="发票号" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.invoiceNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="手机号 / 邮箱" min-width="220">
        <template slot-scope="scope">
          {{ scope.row.contactPhone || '-' }} /
          <p>{{ scope.row.contactEmail || '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            v-if="(scope.row.invoiceStatus == 0 || scope.row.invoiceStatus == 3) && !scope.row.useless"
            @click="editInvoice(scope.row)"
            >修改发票信息</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <edit-offline-invoice-dialog
      :dialog-ctrl.sync="editInvoiceVisible"
      :invoice-id="invoiceId"
      invoice-type="2"
    ></edit-offline-invoice-dialog>
  </el-card>
</template>
<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import { Query, UiPage } from '@hbfe/common'
  import { InvoiceStatusEnum } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import EditInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/edit-invoice-dialog.vue'
  import { ElForm } from 'element-ui/types/form'
  import offLinePageInvoiceVo from '@api/service/management/trade/batch/invoice/query/vo/OffLinePageInvoiceResponseVo'
  import QueryOffLinePageInvoiceParam from '@api/service/management/trade/batch/invoice/query/vo/QueryOffLinePageInvoiceParam'
  import { OrderReturnStatusEnum, TitleTypeEnum } from '@api/service/management/trade/batch/invoice/enum/InvoiceEnum'
  import {
    BillStatusChangeTimeRequest,
    DateScopeRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import MutationOffLineInvoice from '@api/service/management/trade/batch/invoice/mutation/MutationOffLineInvoice'
  import EditOfflineInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/edit-offline-invoice-dialog.vue'
  import ImportInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/import-invoice-dialog.vue'
  import { bind, debounce } from 'lodash-decorators'
  import QueryOffLineInvoice from '@api/service/management/trade/batch/invoice/query/QueryOffLineInvoice'

  @Component({
    components: { EditOfflineInvoiceDialog, DoubleDatePicker, EditInvoiceDialog, ImportInvoiceDialog }
  })
  export default class extends Vue {
    @Ref('elForm') elForm: ElForm
    @Ref('invoiceOfflineRef') invoiceOfflineRef: any

    @Prop({
      type: String,
      default: ''
    })
    userId: string

    @Watch('userId')
    async userIdChange(id: string) {
      await this.doSearch()
    }

    select = ''
    input = ''
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    page: UiPage
    query: Query = new Query()
    form = {
      data1: ''
    }
    hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
    //查询入参
    pageQueryParam: QueryOffLinePageInvoiceParam = new QueryOffLinePageInvoiceParam()
    exportQueryParam: QueryOffLinePageInvoiceParam = new QueryOffLinePageInvoiceParam()
    pageData: Array<offLinePageInvoiceVo> = new Array<offLinePageInvoiceVo>()
    currentItem = new offLinePageInvoiceVo()
    //修改发票弹窗
    editInvoiceVisible = false
    //操作日志弹窗
    logDialog = false
    invoiceId = ''
    voidInvoice = false
    processInvoice = false
    //导出成功弹窗
    exportSuccessVisible = false
    //导入电子发票弹窗
    importInvoiceVisible = false
    InvoiceObject = {
      invoiceNo: ''
    }
    queryOffLineInvoice: MutationOffLineInvoice = new MutationOffLineInvoice()
    @Watch('InvoiceObject', {
      deep: true,
      immediate: true
    })
    InvoiceObjectChange(val: any) {
      if (val) {
        console.log(val, 'val')
      }
    }
    rules = {
      invoiceNo: [
        {
          required: true,
          message: '请输入发票号码',
          trigger: ['change', 'blur']
        }
      ]
    }
    refundStatusMapType = {
      [OrderReturnStatusEnum.RETURNSING]: 'primary',
      [OrderReturnStatusEnum.RETURNSUCCESS]: 'success'
    }
    invoiceStatusMapType = {
      [InvoiceStatusEnum.NOTPTOOPEN]: 'info',
      [InvoiceStatusEnum.OPENING]: 'primary',
      [InvoiceStatusEnum.OPENERROR]: 'danger',
      [InvoiceStatusEnum.OPEMSUCCESS]: 'success'
    }
    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }
    blueInvoiceStatus = [
      {
        name: '请选择发票状态',
        value: null
      },
      {
        name: '待开票',
        value: InvoiceStatusEnum.NOTPTOOPEN
      },
      {
        name: '开票中',
        value: InvoiceStatusEnum.OPENING
      },
      {
        name: '开票成功',
        value: InvoiceStatusEnum.OPEMSUCCESS
      },
      {
        name: '开票失败',
        value: InvoiceStatusEnum.OPENERROR
      }
    ]
    invoiceStatusMapName = {
      [InvoiceStatusEnum.NOTPTOOPEN]: '待开票',
      [InvoiceStatusEnum.OPENING]: '开票中',
      [InvoiceStatusEnum.OPENERROR]: '开票失败',
      [InvoiceStatusEnum.OPEMSUCCESS]: '开票成功'
    }
    refundStatusMapName = {
      [OrderReturnStatusEnum.RETURNSING]: '退款中',
      [OrderReturnStatusEnum.RETURNSUCCESS]: '退款成功'
    }
    constructor() {
      super()
      this.pageQueryParam.billStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.billStatusChangeTime.billing = new DateScopeRequest()
      this.pageQueryParam.billStatusChangeTime.success = new DateScopeRequest()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }
    async doSearch() {
      if (!this.userId) {
        this.pageData = []
        return
      }
      this.query.loading = true
      if (this.hasSelectSchemeMode.length) {
        //培训方案
        this.pageQueryParam.commoditySkuIdList = this.hasSelectSchemeMode[0].schemeId
      } else {
        this.pageQueryParam.commoditySkuIdList = null
      }

      const queryInvoice = new QueryOffLineInvoice()
      this.pageQueryParam.userId = this.userId
      try {
        this.pageData = await queryInvoice.offLinePageInvoiceInServicer(this.page, this.pageQueryParam)
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        this.query.loading = false
        this.exportQueryParam = Object.assign(new QueryOffLinePageInvoiceParam(), this.pageQueryParam)
      }
    }

    async pollingSearch() {
      if (!this.userId) {
        this.pageData = []
        return
      }
      this.query.loading = true
      if (this.hasSelectSchemeMode.length) {
        //培训方案
        this.pageQueryParam.commoditySkuIdList = this.hasSelectSchemeMode[0].schemeId
      } else {
        this.pageQueryParam.commoditySkuIdList = null
      }

      const queryInvoice = new QueryOffLineInvoice()
      this.pageQueryParam.userId = this.userId
      try {
        const origin = this.pageData.slice()
        const result = await queryInvoice.offLinePageInvoiceInServicer(this.page, this.pageQueryParam)

        this.pageData = [...origin, ...result]
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        this.query.loading = false
        this.exportQueryParam = Object.assign(new QueryOffLinePageInvoiceParam(), this.pageQueryParam)
      }
    }
    async resetCondition() {
      this.page.pageNo = 1
      this.pageQueryParam = new QueryOffLinePageInvoiceParam()
      this.pageQueryParam.billStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.billStatusChangeTime.billing = new DateScopeRequest()
      this.pageQueryParam.billStatusChangeTime.success = new DateScopeRequest()
      this.hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
      await this.doSearch()
    }
    //修改发票信息
    editInvoice(item: any) {
      if (item.invoiceFreezeStatus) {
        this.$message.warning('发票已冻结，无法修改！')
        return
      }
      this.invoiceId = item.invoiceId
      this.editInvoiceVisible = true
    }
    /**
     * 【换班记录】无限加载
     */
    @bind
    @debounce(200)
    async infiniteScroll() {
      const element = this.invoiceOfflineRef.bodyWrapper
      const scrollDistance = element.scrollHeight - element.scrollTop - element.clientHeight
      console.log('scrollInfo', scrollDistance, element.scrollHeight, element.scrollTop, element.clientHeight)
      if (scrollDistance <= 0) {
        if (this.pageData.length >= this.page.totalSize) {
          // this.$message.warning('没有更多数据')
        } else {
          this.page.pageNo++
          await this.pollingSearch()
        }
      }
    }
    async created() {
      // 给表格内滚动条滚动增加监听事件
      await this.$nextTick(async () => {
        const element = this.invoiceOfflineRef.bodyWrapper
        element.addEventListener('scroll', this.infiniteScroll)
      })
      await this.doSearch()
    }
  }
</script>
