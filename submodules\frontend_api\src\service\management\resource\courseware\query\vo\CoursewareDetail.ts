/**
 * 课件信息
 */
import CoursewareTransformStatus from '@api/service/management/resource/courseware/enum/CoursewareTransformStatus'
import {
  CoursewareDetailResponse,
  CoursewareTeacher
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import Mockjs from 'mockjs'
import CoursewareType from '@api/service/management/resource/courseware/enum/CoursewareType'
import { CoursewareStatusEnum } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'
import moment from 'moment'

class CoursewareDetail {
  /**
   * 章节编号，对应标签编号
   */
  id: string

  /**
   * 课件名称
   */
  name = ''

  /**
   * 课件时长
   */
  timeLength = 0
  timeLengthFormat: string

  /**
   * 课件分类 id
   */
  categoryId = ''

  categoryIdList: string[] = []

  // 课件分类名称
  categoryName = ''

  teachers: Array<SimpleUserInfo> = []

  /**
   * 课件类型，1表示文档，2表示视频，3表示多媒体
   */
  type: CoursewareType

  /*
   是否可用
   */
  enable: boolean

  /*
   是否被引用，
   在做删除动作的时候用来判断课件是否被引用，被引用删除不了
   */
  isBeingUsed: boolean

  /*
   创建时间
   */
  createTime: string

  /*
   课件转换状态
   */
  status: CoursewareTransformStatus

  /*
   课件供应商 id
   */
  providerId: string
  /*
   课件供应商名称
   */
  providerName: string

  description: string

  creator: SimpleUserInfo
  /**
   * 是否外部链接
   */
  isOuter = false
  /**
   *标清地址
   */
  standardAddress: string = undefined

  /**
   *高清地址
   */
  highAddress: string = undefined

  /**
   *超清地址
   */
  superAddress: string = undefined
  /**
   * 课件转码失败原因
   */
  errorMsg: string
  /**
   * 失败操作来源  1.创建 2。修改
   */
  errorSource: number
  /**
   * 判断是否转换成功
   */
  get isExchangeSuccess() {
    return this.status.equal(CoursewareTransformStatus.enum.AVAILABLE)
  }

  static from(coursewareDetailResponse: CoursewareDetailResponse) {
    const detail = new CoursewareDetail()
    detail.id = coursewareDetailResponse.id
    detail.name = coursewareDetailResponse.name
    const statusMap = {
      0: CoursewareStatusEnum.TRANSCODING,
      1: CoursewareStatusEnum.AVAILABLE,
      2: CoursewareStatusEnum.UNAVAILABLE
    }
    detail.status = new CoursewareTransformStatus(statusMap[coursewareDetailResponse.status])
    const typeMap = {
      // 0: CoursewareType.enum.undefined,
      1: CoursewareType.enum.document,
      2: CoursewareType.enum.video,
      3: CoursewareType.enum.videoPackage
    }
    detail.type = new CoursewareType(typeMap[coursewareDetailResponse.type])
    detail.createTime = coursewareDetailResponse.createTime
    if (coursewareDetailResponse.supplierInfo) {
      detail.providerId = coursewareDetailResponse.supplierInfo.servicerId
      detail.providerName = coursewareDetailResponse.supplierInfo.servicerName
    }
    detail.description = coursewareDetailResponse.aboutsContent
    detail.timeLength = coursewareDetailResponse.timeLength
    const mom = moment.duration(detail.timeLength * 1000)
    const hour = `${mom.get('hours')}`.padStart(2, '0')
    const minutes = `${mom.get('minutes')}`.padStart(2, '0')
    const seconds = `${mom.get('seconds')}`.padStart(2, '0')
    detail.timeLengthFormat = `${hour}:${minutes}:${seconds}`
    detail.isBeingUsed = coursewareDetailResponse.isReferenced === 1
    detail.enable = coursewareDetailResponse.enable === 1
    if (coursewareDetailResponse.category) {
      detail.categoryName = coursewareDetailResponse.category.name
      detail.categoryId = coursewareDetailResponse.category.id
      if (coursewareDetailResponse.category.parentIds.length) {
        detail.categoryIdList = coursewareDetailResponse.category.parentIds
      }
    }
    detail.creator = new SimpleUserInfo()
    detail.creator.id = coursewareDetailResponse.createUserId
    // detail.creator.name = `${Mockjs.Random.cfirst()} ${Mockjs.Random.clast()}`
    detail.teachers = new Array<SimpleUserInfo>()
    detail.teachers = coursewareDetailResponse.coursewareTeacherList.map((cTeacher: CoursewareTeacher) => {
      const teacher = new SimpleUserInfo()
      teacher.id = cTeacher.teacherId
      teacher.name = cTeacher.teacherName
      teacher.description = cTeacher.teacherContent
      return teacher
    })
    detail.isOuter = coursewareDetailResponse.isExternalLink || coursewareDetailResponse.resourceType == 4
    // 9:标清 10:高清 11:超清
    detail.standardAddress = (coursewareDetailResponse.coursewareMediaResourceResponses || []).find(
      item => item.clarity == 9
    )?.path
    detail.highAddress = (coursewareDetailResponse.coursewareMediaResourceResponses || []).find(
      item => item.clarity == 10
    )?.path
    detail.superAddress = (coursewareDetailResponse.coursewareMediaResourceResponses || []).find(
      item => item.clarity == 11
    )?.path
    if (!detail.isOuter) {
      detail.standardAddress = detail.highAddress = detail.superAddress = coursewareDetailResponse
        ?.coursewareMediaResourceResponses?.length
        ? coursewareDetailResponse.coursewareMediaResourceResponses[0].path
        : undefined
    }
    detail.errorMsg = coursewareDetailResponse.errorMsg
    detail.errorSource = coursewareDetailResponse.errorSource
    return detail
  }
}

export default CoursewareDetail
