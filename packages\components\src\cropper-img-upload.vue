<template>
  <div>
    <el-upload
      action="#"
      ref="uploadRef"
      list-type="picture-card"
      :auto-upload="false"
      :file-list="imgList"
      class="m-pic-upload proportion-pic"
      :class="{ hideUpload: isHideUpload }"
    >
      <div slot="default" class="upload-placeholder" @click.stop="cropperModel = true" v-show="!picUrl">
        <i class="el-icon-plus"></i>
        <p class="txt">上传图片</p>
      </div>
      <div slot="file" slot-scope="{ file }" class="img-file" v-show="picUrl">
        <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
        <div class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <i class="el-icon-zoom-in"></i>
          </span>
          <span class="el-upload-list__item-delete" @click="handleRemove(file)">
            <i class="el-icon-delete"></i>
          </span>
        </div>
      </div>
      <div slot="tip" class="el-upload__tip">
        <div class="txt f-mt5 f-cr">课程封面图片比例为16:9，尺寸：400px * 225px</div>
      </div>
    </el-upload>
    <!-- 剪裁组件弹窗 -->
    <vue-cropper
      ref="vueCropper"
      :visible.sync="cropperModel"
      :action="actionUrl"
      :headers="headersObj"
      :title="title"
      v-model="picUrl"
      :initWidth="initWidth"
      :dialogStyleOpation="dialogStyleOpation"
      class="vue-cropper"
      :hbOpaction="{
        ratioArr: ratioArr,
        replaceOptions: { centerBox: false, mode: mode }
      }"
    ></vue-cropper>

    <!-- 大图预览 -->
    <el-image style="width: 100px; height: 100px" :previewSrcList="previewList" v-show="false" ref="elImage">
    </el-image>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Prop, Watch } from 'vue-property-decorator'
  import VueCropper from '@hbfe-vue-components/image-cropper'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

  @Component({
    components: {
      VueCropper
    }
  })
  export default class CoverImageUpload extends Vue {
    // 传给后端的图片路径数组/ 预览数组
    imgList = new Array<any>()
    // 图片预览数组
    previewList = new Array<string>()

    // 是否隐藏上传按钮
    isHideUpload = false
    // 文件上传限制
    limit = 1
    // 请求路径
    actionUrl = ''
    // 请求头
    headersObj = {
      'App-Authentication': '',
      Authorization: ''
    }
    url: string | null = null
    cropperModel = false //裁剪弹窗

    imagePreviewDialogVisible = false
    dialogImageUrl = ''

    @Prop({ type: String, default: '', required: true }) value: string
    @Prop({ type: String, default: '班级封面' }) title: string
    @Prop({ type: Number, default: 150 }) initWidth: number
    @Prop({ type: Array, default: ['16:9'] }) ratioArr: string[]
    @Prop({
      type: Object,
      default: {
        width: '300px',
        height: '300px'
      }
    })
    dialogStyleOpation: object
    /**
     * 宽高字符串
     */
    @Prop({ type: String, default: '' }) mode: string

    get picUrl() {
      if (this.value !== '') {
        const mfsHeadReg = /^\/mfs\/\.*/
        if (mfsHeadReg.test(this.value)) {
          return this.value
        }
        return `/mfs${this.value}`
      }
      return null
    }

    set picUrl(val) {
      let url = ''
      if (val) {
        const mfsHeadReg = /^\/mfs\/\.*/
        this.imgList = new Array<any>()
        // 检测图片路径是否含有mfs
        if (mfsHeadReg.test(val)) {
          url = val?.split('/mfs/')[1]
        } else {
          url = val
        }
      }
      this.$emit('input', url)
    }

    @Watch('picUrl', {
      immediate: true,
      deep: true
    })
    picUrlChange(val: any) {
      if (val) {
        this.imgList = new Array<any>()
        this.previewList.push(val)
        this.imgList.push({ url: val })
        this.isHideUpload = this.imgList.length > 0
      }
    }
    // 大图预览ref
    @Ref('elImage') elImage: any
    // 图片上传组件
    @Ref('uploadRef') uploadRef: any
    // 图片裁剪组件
    @Ref('vueCropper') vueCropper: any

    mounted() {
      this.headersObj.Authorization = `Mship ${this.$authentication.getAccessToken()}`
      this.headersObj['App-Authentication'] = `Basic ${process.env.VUE_APP_KEY}`
      this.headersObj['Content-Type'] = 'application/json;charset=UTF-8'
      this.actionUrl = `${ConfigCenterModule.getIngress(ingress.resource)}/auth/uploadBase64ToProtectedFile`
    }

    // 关闭裁剪弹窗
    beforeClose() {
      this.cropperModel = false
    }

    // 删除图片
    handleRemove() {
      this.imgList = new Array<any>()
      this.previewList = new Array<string>()
      this.isHideUpload = this.imgList.length > 0
      this.picUrl = undefined
    }

    // 点击预览图片
    handlePictureCardPreview(file: any) {
      this.$nextTick(() => {
        // 触发点击方法
        this.elImage.clickHandler()
      })
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep.hideUpload .el-upload--picture-card {
    display: none;
  }
</style>
