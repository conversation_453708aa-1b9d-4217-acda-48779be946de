import StudentChooseCourseScene from '@api/service/customer/learning/scene/StudentChooseCourseScene'
import StudentLearningCourseSceneGate from '@api/service/customer/learning/scene/gates/StudentLearningCourseSceneGate'
import ApplyStudentChooseCourseSceneProof from '@api/service/customer/learning/scene/proofs/ApplyStudentChooseCourseSceneProof'
import StudentTrialListenCourseSceneGate from '@api/service/customer/learning/scene/gates/StudentTrialListenCourseSceneGate'
import ApplyStudentTrialLearningCourseSceneProof from '@api/service/customer/learning/scene/proofs/ApplyStudentListenCourseSceneProof'
import StudentLearningCourseTypeEnum from '@api/service/customer/learning/scene/gates/enum/StudentLearningCourseTypeEnum'
import StudentLearningSceneProof from '@api/service/customer/learning/scene/proofs/StudentLearningSceneProof'

class SceneFactory {
  get studentChooseCourseScene() {
    return (proof: ApplyStudentChooseCourseSceneProof) => {
      return new StudentChooseCourseScene(proof)
    }
  }

  get studentLearningCourseSceneGate() {
    return (proof: StudentLearningSceneProof, courseType: StudentLearningCourseTypeEnum) => {
      return new StudentLearningCourseSceneGate(proof, courseType)
    }
  }

  get studentTrialListenCourseSceneGate() {
    return (proof: ApplyStudentTrialLearningCourseSceneProof) => {
      return new StudentTrialListenCourseSceneGate(proof)
    }
  }
}

export default SceneFactory
