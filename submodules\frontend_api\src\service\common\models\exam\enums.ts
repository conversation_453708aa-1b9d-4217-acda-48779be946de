/**
 * 试题类型
 */
export enum QuestionType {
  // 判断题
  JUDGEMENT = 'JUDGEMENT',
  // 单选题
  SINGLE_CHOICE = 'SINGLE_SELECTION',
  // 多选题
  MULTIPLE_CHOICE = 'MULTIPLE_SELECTION',
  // 填空题
  BLANK_FILLING = 'BLANK_FILLING',
  // 简答题
  ESSAY = 'ESSAY',
  // 案例题
  COMPREHENSIVE = 'COMPREHENSIVE'
}

/**
 * 试题难度
 */
export enum QuestionMode {
  // 简单
  EASY = 'EASY',
  // 普通
  NORMAL = 'NORMAL',
  // 困难
  DIFFICULT = 'DIFFICULT'
}

/**
 * 填空题回答类型
 */
export enum BlankFillingAnswerType {
  // 多组答案
  MULTIPLE_SETS = 1,
  // 每空多答案
  MULTIPLE_PER_BLANK
}

/**
 * 试卷配置类型
 */
export enum ExamConfigType {
  // 固定卷
  FIXED = 1,
  // AB卷
  GROUP,
  // 智能卷
  RANDOM
}

/**
 * 试卷计时方式
 */
export enum PaperTimeType {
  // 整卷计时
  WHOLE_PAPER,
  // 单题计时
  TIME_TYPE_SINGLE
}

/**
 * 随机卷类型
 */
export enum RandomPaperType {
  OBJECT_SUPPORT_RATIO = 5,
  NEW_TAG = 6
}

/**
 * 试题来源
 */
export enum QuestionSourceConstants {
  normal = 1,
  notAnswerYet,
  answered,
  favorite
}

/**
 * 组件方式
 */
export enum MakePaperType {
  // 手动组卷
  hand,
  // 只能组卷
  AI
}

/**
 * 组件方式
 */
export enum PaperConfigType {
  // 固定卷
  Fixed = 1,
  // 随机卷
  Random = 3
}

export enum QuestionApplyType {
  EXAM = 1,
  PRACTICE,
  QUESTION_PRACTICE,
  POP,
  SURVEY,
  HOMEWORK,
  WRONG_QUESTION_PRACTICE
}

export class QuestionTypeConvertUtils {
  public static convertQuestionTypeToInt(questionType: QuestionType) {
    if (!questionType) {
      return -1
    }
    switch (questionType) {
      case QuestionType.JUDGEMENT:
        return 1
      case QuestionType.SINGLE_CHOICE:
        return 2
      case QuestionType.MULTIPLE_CHOICE:
        return 3
      case QuestionType.BLANK_FILLING:
        return 4
      case QuestionType.ESSAY:
        return 5
      case QuestionType.COMPREHENSIVE:
        return 6
      default:
        return -1
    }
  }

  public static convertIntToQuestionType(intValue: number) {
    switch (intValue) {
      case 1:
        return QuestionType.JUDGEMENT
      case 2:
        return QuestionType.SINGLE_CHOICE
      case 3:
        return QuestionType.MULTIPLE_CHOICE
      case 4:
        return QuestionType.BLANK_FILLING
      case 5:
        return QuestionType.ESSAY
      case 6:
        return QuestionType.COMPREHENSIVE
    }
  }
}

export class QuestionModeConvertUtils {
  public static convertQuestionModeToInt(mode: QuestionMode) {
    switch (mode) {
      case QuestionMode.EASY:
        return 1
      case QuestionMode.NORMAL:
        return 2
      case QuestionMode.DIFFICULT:
        return 3
    }
  }

  public static convertIntToQuestionMode(intValue: number) {
    switch (intValue) {
      case 1:
        return QuestionMode.EASY
      case 2:
        return QuestionMode.NORMAL
      case 3:
        return QuestionMode.DIFFICULT
    }
  }
}

export enum ExamRandomFetchWay {
  /**
   * 自定义
   */
  CUSTOM = 1,
  /**
   * 题库方式
   */
  LIBRARY,
  /**
   * 关联课程
   */
  RELATE_COURSE,
  /**
   * 标签
   */
  TAG
}
