<route-meta>
{
"title": "业务地区级联选择器"
}
</route-meta>
<template>
  <el-cascader
    ref="elCascaderRef"
    v-if="show"
    :props="props"
    v-model="selectedValue"
    :options="options"
    :placeholder="placeholder"
    :clearable="clearable"
    collapse-tags
    @change="onInput"
    v-bind="$attrs"
    @visible-change="onblurFun"
  ></el-cascader>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Emit, Watch, Ref } from 'vue-property-decorator'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
  import RegionVo from '@api/service/common/basic-data-dictionary/query/vo/RegionVo'
  import UserModule from '@api/service/management/user/UserModule'
  import QueryUserFactory from '@api/service/management/user/QueryUserFactory'
  import RegionTreeItem from '@api/service/management/learning-rule/model/RegionTreeItem'

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    disabled: boolean

    @Prop({
      default: true
    })
    clearable: boolean

    @Prop({
      default: false
    })
    multiple: boolean

    // 传入的必须是数组
    @Prop({
      type: [Array, String]
    })
    value: string[]

    @Prop({
      default: '请选择地区',
      type: String
    })
    placeholder: string

    /**
     * 省份id、用于过滤省份
     */
    @Prop({
      default: '0',
      type: String
    })
    provinceId: string

    /**
     * 去除子孩子
     */
    @Prop({
      type: Boolean,
      default: false
    })
    removeChild: boolean

    /**
     * 是否需要点击到最后一级才能选中，默认：是
     */
    @Prop({
      default: false
    })
    checkStrictly: boolean

    @Ref('elCascaderRef') elCascaderRef: any
    /**
     * 地区选择器配置
     */
    options: Array<RegionTreeVo> = new Array<RegionTreeVo>()
    /**
     * 全国树
     */
    nationWideTree: Array<RegionTreeVo> = new Array<RegionTreeVo>()
    /**
     * 获取服务地区id
     */
    serviceId: string[]
    // 当前选中的值
    selectedValue: string[] = []
    show = true
    props = {}

    //差异化数组
    diffRegionArray = ['北京市', '上海市', '重庆市', '天津市', '香港特别行政区', '澳门特别行政区']

    @Watch('options', {
      immediate: true,
      deep: true
    })
    optionsChange(val: any) {
      console.log('options', val)
    }

    @Watch('value')
    setValue() {
      this.selectedValue = this.value
    }
    @Emit('input')
    onInput(values: any) {
      return values
    }

    async created() {
      this.setProps()
      //   this.options = (await QueryBusinessRegion.getServiceOrIndustry(1)) || ([] as RegionTreeVo[])
      this.nationWideTree = await QueryBusinessRegion.getCountrywideRegion()
      if (QueryUserFactory.queryManagerDetail.isRegionAdmin) {
        this.serviceId = QueryBusinessRegion.getRegionAdminArea()
      } else {
        this.serviceId = await QueryBusinessRegion.getServiceRegionIds()
      }
      this.options = QueryBusinessRegion.filterRegionTree(this.nationWideTree, this.serviceId)
      console.log(this.options, '我是叔叔叔叔')
      // 学习规则列表筛选项地区需要去除对应地区
      if (this.removeChild) {
        this.options.forEach(item => {
          if (this.diffRegionArray.includes(item.name)) {
            item.children = undefined
          } else {
            item.children.forEach(ite => {
              if (ite.children) {
                ite.children = undefined
              }
            })
          }
        })
      }
      this.show = true
    }

    /**
     * 设置配置项
     */
    setProps() {
      this.props = {
        lazy: false,
        value: 'id',
        label: 'name',
        multiple: this.multiple,
        checkStrictly: this.checkStrictly,
        disabled: false
      }
    }

    /**
     * 失焦的时候触发方法
     */
    onblurFun(value: boolean) {
      if (!value && this.selectedValue.length) {
        this.$emit('regionCode', this.selectedValue[this.selectedValue.length - 1])
      }
    }
  }
</script>
