import queryHymSchemeRefundInfo from './queries/queryHymSchemeRefundInfo.graphql'
import VerifyOrderAfterSale from './mutates/VerifyOrderAfterSale.graphql'
import agreeOrderReturn from './mutates/agreeOrderReturn.graphql'
import agreeOrderReturnBatch from './mutates/agreeOrderReturnBatch.graphql'
import applyOrderReturn from './mutates/applyOrderReturn.graphql'
import cancelOrderReturn from './mutates/cancelOrderReturn.graphql'
import refuseOrderReturn from './mutates/refuseOrderReturn.graphql'

export {
  queryHymSchemeRefundInfo,
  VerifyOrderAfterSale,
  agreeOrderReturn,
  agreeOrderReturnBatch,
  applyOrderReturn,
  cancelOrderReturn,
  refuseOrderReturn
}
