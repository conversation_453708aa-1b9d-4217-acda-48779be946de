import { TaskExecuteByPageResponse } from '@api/ms-gateway/ms-importopen-v1'
import { QuestionImportTaskResultEnum } from '@api/service/common/enums/async-task/QuestionImportTaskResult'
import { QuestionImportTaskStatusEnum } from '@api/service/common/enums/async-task/QuestionImportTaskStatus'

export default class ImportTaskItem {
  /**
   * 任务编号
   */
  taskId = ''

  /**
   * 任务名称
   */
  name = ''
  /**
   * 任务分类
   */
  category = ''
  /**
   * 所属批次单编号
   */
  batchNo = ''
  /**
   * 任务执行状态
   */
  taskState: QuestionImportTaskStatusEnum = null
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
   */
  processResult: QuestionImportTaskResultEnum = null
  /**
   * 处理信息
   */
  message = ''
  /**
   * 创建时间
   */
  createdTime = ''
  /**
   * 就绪时间
   */
  alreadyTime = ''
  /**
   * 执行时间
   */
  executingTime = ''
  /**
   * 完成时间
   */
  completedTime = ''
  /**
   * 总数
   */
  totalCount = 0
  /**
   * 成功数
   */
  successCount = 0
  /**
   * 失败数
   */
  failCount = 0

  static from(dto: TaskExecuteByPageResponse) {
    const vo = new ImportTaskItem()
    vo.taskId = dto.id
    vo.name = dto.name
    vo.category = dto.category
    vo.batchNo = dto.batchNo
    vo.taskState = dto.taskState
    vo.processResult = dto.processResult
    vo.message = dto.message
    vo.createdTime = dto.createdTime
    vo.alreadyTime = dto.alreadyTime
    vo.executingTime = dto.executingTime
    vo.completedTime = dto.completedTime
    dto.eachStateCounts.forEach((item) => {
      if (item.result === 1) {
        if (vo.successCount == undefined) {
          vo.successCount = 0
        }
        vo.successCount = vo.successCount + item.count
      } else if (item.result === 2) {
        if (vo.failCount == undefined) {
          vo.failCount = 0
        }
        vo.failCount = vo.failCount + item.count
      }
      if (vo.totalCount == undefined) {
        vo.totalCount = 0
      }
      vo.totalCount = vo.totalCount + item.count
    })
    return vo
  }
}
