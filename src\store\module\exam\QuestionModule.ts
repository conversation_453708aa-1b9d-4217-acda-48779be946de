import PreExamGateway, {
  Page,
  PreExamQuestionCreateRequest,
  PreExamQuestionImportRequest,
  PreExamQuestionItemResponse,
  PreExamQuestionParamDTO,
  PreExamQuestionResponse,
  PreExamQuestionUpdateRequest
} from '@api/gateway/PreExam-default'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { QuestionItem } from '@/store/module/exam/mode/question/QuestionItem'
import { QuestionDetail } from '@/store/module/exam/mode/question/QuestionDetail'
import PlatformUserGateway, { UserParamDTO } from '@api/gateway/PlatformUser'
import PlatformExam from '@api/gateway/PlatformExam'

/**
 * 试题全局的state
 */
export interface IQuestionState {
  /**
   * 总页数
   */
  totalSize: number
  /**
   * 分页总数
   */
  totalPageSize: number

  /**
   * 试题分页对象
   */
  questionList: Array<QuestionItem>
  /**
   * 试题详情
   */
  questionDetail: QuestionDetail
  /**
   * 系统默认的题库
   */
  defaultQuestionLibraryId: string
}

@Module({ namespaced: true, store, dynamic: true, name: 'QuestionModule' })
class QuestionModule extends VuexModule implements IQuestionState {
  /**
   * 总页数
   */
  totalPageSize = 0
  /**
   * 分页总数
   */
  totalSize = 0
  /**
   * 试题分页对象
   */
  questionList = new Array<QuestionItem>()

  questionDetail = new QuestionDetail()
  /**
   * 系统默认的题库
   */
  defaultQuestionLibraryId = ''

  /**
   * 获取试题分页数据
   * @param param
   */
  @Action
  async pageQuestion(param: { page: Page; param: PreExamQuestionParamDTO }) {
    const response = await PreExamGateway.findExamQuestionPage(param)
    if (!response.status.isSuccess()) {
      console.log('加载试题分页数据失败')
      return response.status
    }
    this.SET_TOTAL_SIZE(response.data.totalSize)
    this.SET_TOTAL_PAGE_SIZE(response.data.totalPageSize)
    this.SET_QUESTION_LIST(response.data.currentPageData)
    return response.status
  }

  /**
   * 创建试题
   * @param create
   */
  @Action
  async create(create: PreExamQuestionCreateRequest) {
    const response = await PreExamGateway.createQuestion(create)
    return response.status
  }

  /**
   *  试题更新
   * @param update
   */
  @Action
  async update(update: PreExamQuestionUpdateRequest) {
    const response = await PreExamGateway.updateQuestion(update)
    return response.status
  }

  /**
   * 试题删除
   * @param id
   */
  @Action
  async delete(id: string) {
    const response = await PreExamGateway.deleteQuestion(id)
    return response.status
  }

  /**
   * 启用试题
   * @param id
   */
  @Action
  async enable(id: string) {
    const res = await PreExamGateway.enableQuestion(id)
    return res.status
  }

  /**
   * 停用试题
   * @param id
   */
  @Action
  async disable(id: string) {
    const res = await PreExamGateway.disableQuestion(id)
    return res.status
  }

  /**
   * 查询试题
   * @param id
   */
  @Action
  async get(id: string) {
    const response = await PreExamGateway.findQuestion(id)
    if (!response.status.isSuccess()) {
      return response.status
    }
    const questionInfo = response.data
    this.SET_QUESTION_DETAIL(questionInfo)

    // 追加创建人信息
    const param = new UserParamDTO()
    param.userIdList = Array.of(this.questionDetail.createUserId)
    const userRes = await PlatformUserGateway.listUserInfo(param)
    if (userRes.status.isSuccess()) {
      this.SET_QUESTION_DETAIL_CREATE_USER(userRes.data[0].nickName)
    }
    return response.status
  }

  /**
   * 批量创建试题
   * @param param
   */
  @Action
  async questionImport(param: { filePath: string; fileType: number }) {
    const preExamQuestionImportDTO = new PreExamQuestionImportRequest()
    // importComprehensiveQuestion
    preExamQuestionImportDTO.filePath = param.filePath
    let response = null
    if (param.fileType === 1) {
      response = await PreExamGateway.questionImport(preExamQuestionImportDTO)
    } else {
      response = await PlatformExam.importComprehensiveQuestion(preExamQuestionImportDTO)
    }
    if (!response.status.isSuccess()) {
      return response.status
    }
    return response.status
  }

  /**
   * 获取默认的题库
   */
  @Action
  async getDefaultQuestionLibraryId() {
    const response = await PlatformExam.getDefaultQuestionLibraryId()
    if (response.status.isSuccess()) {
      this.SET_DEFAULT_QUESTION_LIBRARY_ID(response.data)
    }
    return response.status
  }

  /**
   * 设置状态对象中的试题总数
   * @param totalSize
   * @constructor
   */
  @Mutation
  private SET_TOTAL_SIZE(totalSize: number) {
    this.totalSize = totalSize
  }

  /**
   * 设置状态对象中的试题分页的总页数
   * @param totalPageSize
   * @constructor
   */
  @Mutation
  private SET_TOTAL_PAGE_SIZE(totalPageSize: number) {
    this.totalPageSize = totalPageSize
  }

  /**
   * 设置分页对象
   * @param list
   * @constructor
   */
  @Mutation
  private SET_QUESTION_LIST(list: Array<PreExamQuestionItemResponse>) {
    this.questionList = new Array<QuestionItem>()
    list.map(p => {
      const item = new QuestionItem()
      Object.assign(item, p)
      this.questionList.push(item)
    })
  }

  /**
   * 设置试题详情
   * @param item
   * @constructor
   */
  @Mutation
  private SET_QUESTION_DETAIL(item: PreExamQuestionResponse) {
    const questionDetail = new QuestionDetail()
    Object.assign(questionDetail, item)
    this.questionDetail = questionDetail
  }

  /**
   * 设置试题详情创建用户
   * @param userName
   * @constructor
   */
  @Mutation
  private SET_QUESTION_DETAIL_CREATE_USER(userName: string) {
    this.questionDetail.createUserName = userName
  }

  /**
   * 设置默认的题库id
   * @param id
   * @constructor
   */
  @Mutation
  private SET_DEFAULT_QUESTION_LIBRARY_ID(id: string) {
    this.defaultQuestionLibraryId = id
  }
}

export default getModule(QuestionModule)
