<template>
  <div>
    <el-card shadow="never" class="m-card is-header">
      <div class="m-tit is-border-bottom">
        <span class="tit-txt">监管功能基础配置</span>
      </div>
      <el-row type="flex" justify="center" class="width-limit f-mt20">
        <el-col :md="20" :lg="16" :xl="13">
          <el-form ref="form" label-width="220px" class="m-form">
            <el-form-item label="监管功能是否开启：">
              <el-switch v-model="baseConfigModule.baseConfig.antiEnable"></el-switch>
              <div class="f-ci" v-if="baseConfigModule.baseConfig.antiEnable">
                开启后监管规则生效，请确认监管规则配置情况
              </div>
              <div class="f-ci" v-else>关闭后仍可配置监管规则，但不生效</div>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div class="m-tit is-border-bottom">
        <span class="tit-txt">人脸识别基础配置</span>
      </div>
      <el-row type="flex" justify="center" class="width-limit f-mt20">
        <el-col :md="20" :lg="16" :xl="13">
          <el-form
            ref="baseConfigFormRef"
            :rules="baseConfigRules"
            :model="baseConfigModule.baseConfig.datumConfig"
            label-width="220px"
            class="m-form"
          >
            <el-form-item label="人脸识别功能是否开启：">
              <el-switch v-model="baseConfigModule.baseConfig.datumConfig.enable"></el-switch>
              <div class="f-ci" v-if="baseConfigModule.baseConfig.datumConfig.enable">
                开启后监管规则可选择“人脸识别”作为监管方式，请确认监管规则是否配置
              </div>
              <div class="f-ci" v-else>关闭后监管规则无法选择“人脸识别”作为监管方式</div>
            </el-form-item>
            <template v-if="baseConfigModule.baseConfig.datumConfig.enable">
              <el-form-item label="基准照片数：">{{ collectCount }}</el-form-item>
              <el-form-item label="基准照片更新次数：" prop="updateCount">
                <el-input v-model="updateCount" placeholder="3" class="f-input-num f-mr5"></el-input>
                次
              </el-form-item>
              <el-form-item label="监控协议：" prop="protocolText">
                <div class="rich-text">
                  <hb-tinymce-editor v-model="baseConfigModule.baseConfig.datumConfig.protocolText"></hb-tinymce-editor>
                </div>
              </el-form-item>
              <el-form-item label="匹配相似度：" prop="comparePhoto">
                <el-input v-model="comparePhoto" placeholder="50" class="f-input-num f-mr5"></el-input>
                %<span class="f-ci f-fl5">（注：匹配系数为1%-100%，系数越大相似度就越高。）</span>
              </el-form-item>
              <el-form-item label="比对过程是否开启活体检测：">
                <el-switch v-model="liveDetection"></el-switch>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-question m-tooltip-icon f-co f-ml10"></i>
                  <div slot="content">
                    <p>说明：</p>
                    <p>1、默认全平台所有采集环节开启活体检测</p>
                    <p>
                      2、全平台比对过程的活体检测可统一设置为开启或关闭，比对环节包含登录、学习、考试，是否开启活体检测仅对设置为开启监管规则的环节生效。
                    </p>
                    <p>3、适用于具体配置的培训方案，若系统也有配置检测规则，则以培训方案的配置为准</p>
                  </div>
                </el-tooltip>
              </el-form-item>
            </template>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
    <div class="m-btn-bar f-tc is-sticky-1">
      <el-button @click="update">取消</el-button>
      <el-button @click="save" :loading="isLoading" type="primary">保存</el-button>
    </div>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, Vue, Ref } from 'vue-property-decorator'
  import AntiCheatLoginConfigModel from '@hbfe-biz/biz-anticheat/dist/config/base-models/AntiCheatLoginConfigModel'
  import BaseConfig from '@hbfe-biz/biz-anticheat/dist/config/BaseConfig'
  import { ElForm } from 'element-ui/types/form'
  import Context from '@api/service/common/context/Context'

  @Component({})
  export default class extends Vue {
    @Ref('baseConfigFormRef') baseConfigFormRef: ElForm

    // 基础配置
    @Prop({
      type: Object,
      default: () => new BaseConfig()
    })
    baseConfigModule: BaseConfig

    // 登录配置
    loginConfig = new AntiCheatLoginConfigModel()

    // 采集照片数
    collectCount = 1

    isLoading = false

    /**
     * 基准照片更新次数
     */
    get updateCount() {
      return this.baseConfigModule.baseConfig.datumConfig.updateCount
    }

    set updateCount(newValue: number | string) {
      let val = Math.min(Math.max(isNaN(Number(newValue)) ? 0 : Number(newValue), 0), 100)
      if (newValue === '') {
        val = undefined
      }
      this.$set(this.baseConfigModule.baseConfig.datumConfig, 'updateCount', val)
    }

    /**
     * 匹配相似度
     */
    get comparePhoto() {
      return this.baseConfigModule.baseConfig.datumConfig.comparePhoto
    }

    set comparePhoto(newValue: number | string) {
      let val = Math.min(Math.max(isNaN(Number(newValue)) ? 100 : Number(newValue), 1), 100)
      if (newValue === '') {
        val = undefined
      }
      this.$set(this.baseConfigModule.baseConfig.datumConfig, 'comparePhoto', val)
    }

    get liveDetection() {
      return this.baseConfigModule.baseConfig.datumConfig.liveDetection
    }

    set liveDetection(newValue: boolean) {
      this.$set(this.baseConfigModule.baseConfig.datumConfig, 'liveDetection', newValue)
    }

    updateCountValidate = (rule: any, value: any, callback: (arg?: Error) => void) => {
      const exp = /(^[0-9]\d*$)/
      if (value === undefined || value === '') {
        return callback(new Error('请输入基准照片更新次数'))
      } else if (!exp.test(value) || isNaN(Number(value))) {
        return callback(new Error('请输入正确的数值'))
      } else {
        return callback()
      }
    }

    comparePhotoValidate = (rule: any, value: any, callback: (arg?: Error) => void) => {
      const exp = /(^[0-9]\d*$)/
      if (value === undefined || value === '') {
        return callback(new Error('请输入匹配相似度'))
      } else if (!exp.test(value) || isNaN(Number(value))) {
        return callback(new Error('请输入正确的数值'))
      } else {
        return callback()
      }
    }

    get baseConfigRules() {
      return {
        updateCount: [{ validator: this.updateCountValidate, trigger: 'blur' }],
        protocolText: [{ required: true, message: '请输入监管协议', trigger: 'blur' }],
        comparePhoto: [{ validator: this.comparePhotoValidate, trigger: 'blur' }]
      }
    }

    // 保存基础配置
    async save() {
      this.baseConfigFormRef.validate(async (boolean: boolean, value: object) => {
        if (boolean) {
          this.isLoading = true
          // 采集数量暂时默认一张
          this.baseConfigModule.baseConfig.datumConfig.collectCount = this.collectCount

          try {
            // 判断是否第一次设置基础配置，第一次走创建口，否则走更新口
            if (!this.baseConfigModule.baseConfig.antiModuleConfigId) {
              await this.baseConfigModule.createSchoolAntiConfig(
                Context.businessEnvironment.serviceToken.tokenMeta.servicerId
              )
            } else {
              await this.baseConfigModule.updateSchoolAntiConfig()
            }
            if (!this.baseConfigModule.baseConfig.datumConfig.datumConfigId) {
              await this.baseConfigModule.createConfig()
            } else {
              await this.baseConfigModule.updateConfig(false)
            }
            this.$message.success('保存成功')
          } catch (error) {
            this.$message.error('保存失败')
            console.log(error)
          } finally {
            this.isLoading = false
            this.update()
          }
        }
      })
    }

    /**
     * 取消
     */
    update() {
      this.$emit('update-info')
    }
  }
</script>
