import PlatformJxjypxtyptJxgxCourseLearningGatewayForestage from '@api/diff-gateway/platform-jxjypxtypt-jxgx-course-learning-gateway-forestage'
import PlatformJxjypxtyptJxgxSchool, {
  StudentCoursesInvalidRequest,
  StudentCoursesInvalidResponse
} from '@api/diff-gateway/platform-jxjypxtypt-jxgx-school'

export default class Play {
  /**
   * 结束培训系统学习
   */
  async invalidStudentCourses(id: string) {
    const studentCoursesInvalidRequest = new StudentCoursesInvalidRequest()
    studentCoursesInvalidRequest.studentNo = id
    const { data, status } = await PlatformJxjypxtyptJxgxSchool.invalidStudentCourses(studentCoursesInvalidRequest)
    if (status.isSuccess()) {
      return data
    }
    return new StudentCoursesInvalidResponse()
  }

  /**
   * 结束管理系统学习
   */
  async stopCourseLearning(id: string) {
    const { data, status } = await PlatformJxjypxtyptJxgxCourseLearningGatewayForestage.stopCourseLearning(id)
    if (status.isSuccess()) {
      return data
    }
    return ''
  }
}
