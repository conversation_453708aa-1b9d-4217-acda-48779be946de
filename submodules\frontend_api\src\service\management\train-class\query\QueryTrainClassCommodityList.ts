import { Page, Response } from '@hbfe/common'
import SkuPropertyVo from '@api/service/management/train-class/query/vo/SkuPropertyVo'
import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
import MsTradeQueryFrontGatewayCourseLearningBackstage, {
  CommoditySkuBackstageResponse,
  CommoditySkuPropertyResponse,
  CommoditySkuRequest,
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  SchemeResourceResponse,
  SkuPropertyResponse,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import {
  ComplexSkuPropertyResponse,
  SchemeSkuInfo,
  SkuPropertyConvertUtils
} from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import MsSchemeQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import SkuVo from '@api/service/management/train-class/query/vo/SkuVo'
import ConfigJsonUtil from '@api/service/management/train-class/Utils/ConfigJsonUtil'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import * as MsLearningschemeGraphqlImporter from '@api/ms-gateway/ms-learningscheme-v1/graphql-importer'
import MsLearningscheme from '@api/ms-gateway/ms-learningscheme-v1'
import MsLearningScheme, {
  IsProcessedByTransactionRequest,
  LearningSchemeProcessStatusResponse
} from '@api/ms-gateway/ms-learningscheme-v1'
import AntiSchemeParams from '@api/service/management/train-class/query/vo/AntiSchemeParams'
import AntiSchemeItem from '@api/service/management/train-class/query/vo/AntiSchemeItem'
import BasicDataQueryBackstage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import MsDataExportBackstageGateway, {
  CommoditySkuSortField as CommoditySkuSortFieldV2,
  CommoditySkuSortRequest as CommoditySkuSortRequestv2
} from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import RuleSchemeParams from '@api/service/management/train-class/query/vo/RuleSchemeParams'
import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'
import PlatformSchemeConfig from '@api/platform-gateway/platform-scheme-config-backstage-v1'
import IntelligenceLearningModule from '@api/service/management/intelligence-learning/IntelligenceLearningModule'
import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import DataResolve from '@api/service/common/utils/DataResolve'
import FxnlQueryFrontGatewayBackstage from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import QueryTrainClass from '@api/service/management/train-class/offlinePart/QueryTrainClass'
import DiffDataExportGatewayBackstage from '@api/platform-gateway/diff-data-export-gateway-backstage'
import jxjyDataExportGatewayBackstage from '@api/platform-gateway/jxjy-data-export-gateway-backstage'

/**
 * 运营域获取培训班商品列表
 */
class QueryTrainClassCommodityList {
  // region properties
  /**
   *总数目，类型为number
   */
  totalSize = 0
  /**
   *培训班商品列表，类型为TrainClassCommodityVo[]
   */
  trainClassCommodityList: TrainClassCommodityVo[] = []
  /**
   *刷选条件数组，类型为SkuPropertyVo
   */
  skuProperties = new SkuPropertyVo()
  // endregion
  // region methods

  /**
   * 批量查询培训方案商品名称
   * @param schemeIdList 方案id集合
   */
  async batchQuerySchemeNameMapBySchemeId(schemeIdList: string[]): Promise<Map<string, string>> {
    const result = new Map<string, string>()
    const queryM = new ConfigJsonUtil()
    const schemeJsonConfigMap = await queryM.batchQuerySchemeJsonConfigMapBySchemeId(schemeIdList, ['name'])
    for (const [key, value] of schemeJsonConfigMap.entries()) {
      result.set(key, value.name || '')
    }
    return result
  }

  fromRuleSchemeItem(dto: TrainClassCommodityVo) {
    const vo = new RuleSchemeItem(dto.schemeId, dto.commodityBasicData.saleTitle, dto.skuValueNameProperty)
    return vo
  }

  /**
   * 获取培训班列表
   * @param page 分页参数
   * @param filterCommodity 筛选条件
   * @param sortRequest 排序条件
   * @param distributorId 分销商id，分销相关业务使用
   * @param queryIntelligenceLearning 是否查询智能学习
   * @param querySchemeStatus 是否查询方案状态
   * @description 参数后续优化补充模型
   */
  async queryTrainClassCommodityList(
    page: Page,
    filterCommodity: CommoditySkuRequest,
    sortRequest?: Array<CommoditySkuSortRequest>,
    distributorId?: string,
    querySchemeStatus = true,
    queryIntelligenceLearning = true
  ): Promise<Array<TrainClassCommodityVo>> {
    // let filter = new CommoditySkuRequest()
    // filter.skuPropertyRequest = this.filterSkuVo.convertToDto()
    const sortRequestM = new CommoditySkuSortRequest()
    sortRequestM.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortRequestM.policy = SortPolicy.DESC
    !sortRequest && (sortRequest = [])
    sortRequest.push(sortRequestM)
    const res = await MsTradeQueryFrontGatewayCourseLearningBackstage.pageCommoditySkuInServicer({
      page: { pageNo: page.pageNo, pageSize: page.pageSize },
      queryRequest: filterCommodity,
      sortRequest: sortRequest || []
    })
    page.totalPageSize = res.data?.totalPageSize
    page.totalSize = res.data?.totalSize
    if (res.status.isSuccess()) {
      const dtoList = res?.data?.currentPageData || new Array<CommoditySkuBackstageResponse>()
      const tmpArr = dtoList.map((dto) => {
        return Object.assign(new TrainClassCommodityVo(), dto)
      }) as TrainClassCommodityVo[]
      tmpArr.forEach((item) => {
        item.schemeId = (item.resource as SchemeResourceResponse)?.schemeId || ''
      })
      const createdTime = new Date(
        ConfigCenterModule.getFrontendApplication(frontendApplication.distinguishSchemeTypeTime)
      ).getTime()
      const statusIdList = tmpArr
        .map((item) => {
          if (createdTime < new Date(item.commodityCreatTime).getTime()) {
            item.isNewScheme = true
            return item.schemeId
          }
        })
        .filter(Boolean)
      // 构建方案id集合
      const schemeIdList = [...new Set(tmpArr.map((item) => item.schemeId).filter(Boolean))]
      const queryM = new ConfigJsonUtil()
      const configJsonMap = await queryM.batchQuerySchemeJsonConfigMapBySchemeId(schemeIdList, [])
      // 智能学习
      let IntelligenceLearningMap = new Map<string, string>()
      if (queryIntelligenceLearning && schemeIdList?.length) {
        IntelligenceLearningMap = await new IntelligenceLearningModule().doBatchCheck(schemeIdList)
      }
      let schemeStatusMap = new Map<string, LearningSchemeProcessStatusResponse>()
      if (querySchemeStatus) {
        schemeStatusMap = await this.getSchemeStatus(statusIdList)
      }
      /** 批量获取方案json配置信息 */
      tmpArr.forEach((item, index) => {
        // if (item.skuProperty) {
        //   item.skuValueNameProperty = SkuPropertyConvertUtils.convertSkuValueNameProperty(item.skuProperty)
        // }
        const configJson = configJsonMap.get(item.schemeId)
        if (configJson) {
          item.resourceServicerId = configJson.resourceServicerId
          item.resourceUnitId = configJson.resourceUnitId
          item.registerBeginDate = configJson.registerBeginDate
          item.registerEndDate = configJson.registerEndDate
          item.trainingBeginDate = configJson.trainingBeginDate
          item.trainingEndDate = configJson.trainingEndDate
          item.schemeType = SchemeTypeEnum[configJson.type as string]
          item.needDataSync = configJson.extendProperties?.find((item: any) => {
            return item.name == 'needDataSync'
          })?.value
          // 填充面授部分信息
          QueryTrainClass.parseTrainClassJsonConfigInList(configJson, item)
          const schemeStatus = schemeStatusMap?.get(item.schemeId)
          item.lastTransactionStep = schemeStatus?.lastTransactionStep || 999
          item.recalculating = schemeStatus?.recalculating || false
          // 智能学习
          const intelligentStatus = IntelligenceLearningMap[item.schemeId]
          console.log(intelligentStatus, 'intelligentStatus')
          item.intelligentLearning = intelligentStatus
          item.hangUp = schemeStatus?.hangUp || false
          if (item?.hangUp) {
            item.hasError = true
            // 可能存在多种异常情况 UI 气泡提示无法完全展示，默认取第一个
            const errMsgList = schemeStatus?.errors?.map((item) => item.message || '')
            item.errorMsg = errMsgList?.length ? errMsgList.join(' ') : '系统异常'
          }
        }

        // 填充销售渠道
        item.fileSaleChannelShowRange()
      })
      const schemeSkuInfoList: SchemeSkuInfo[] = tmpArr?.map(
        (item) => new SchemeSkuInfo(item.schemeId, item.skuProperty as ComplexSkuPropertyResponse)
      )
      if (schemeSkuInfoList.length) {
        const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(schemeSkuInfoList)
        tmpArr.forEach((item) => {
          const skyInfo = skuInfos.find((el) => el.id === item.schemeId)
          if (skyInfo) {
            item.skuValueNameProperty = skyInfo.skuName
          }
        })
      }
      /** 叠加分销业务的授权状态 */
      if (distributorId) {
        const commodityIdList = DataResolve.unique(tmpArr.map((item) => item.commoditySkuId))
        if (commodityIdList.length) {
          const { data: authResp } = await FxnlQueryFrontGatewayBackstage.judgeCommodityAuthorized({
            commodityIdList,
            distributorId
          })
          if (authResp && authResp.length) {
            tmpArr.forEach((item) => {
              item.isAuthorize = authResp.includes(item.commoditySkuId)
            })
          }
        }
      }
      this.trainClassCommodityList = tmpArr
    }
    return this.trainClassCommodityList
  }

  /**
   * 获取培训班列表
   * @param page 分页参数
   * @param filterCommodity 筛选条件
   * @param sortRequest 排序条件
   * @param distributorId 分销商id，分销相关业务使用
   * @param queryIntelligenceLearning 是否查询智能学习
   * @param querySchemeStatus 是否查询方案状态
   * @description 参数后续优化补充模型
   */
  async queryTrainClassCommodityListInTrainingChannel(
    page: Page,
    filterCommodity: CommoditySkuRequest,
    sortRequest?: Array<CommoditySkuSortRequest>,
    distributorId?: string,
    querySchemeStatus = true,
    queryIntelligenceLearning = true
  ): Promise<Array<TrainClassCommodityVo>> {
    // let filter = new CommoditySkuRequest()
    // filter.skuPropertyRequest = this.filterSkuVo.convertToDto()
    const sortRequestM = new CommoditySkuSortRequest()
    sortRequestM.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortRequestM.policy = SortPolicy.DESC
    !sortRequest && (sortRequest = [])
    sortRequest.push(sortRequestM)
    const res = await MsTradeQueryFrontGatewayCourseLearningBackstage.pageCommoditySkuInTrainingChannel({
      page: { pageNo: page.pageNo, pageSize: page.pageSize },
      queryRequest: filterCommodity,
      sortRequest: sortRequest || []
    })
    page.totalPageSize = res.data?.totalPageSize
    page.totalSize = res.data?.totalSize
    if (res.status.isSuccess()) {
      const tmpArr = res.data.currentPageData as TrainClassCommodityVo[]
      tmpArr.forEach((item) => {
        item.schemeId = (item.resource as SchemeResourceResponse)?.schemeId || ''
      })
      const createdTime = new Date(
        ConfigCenterModule.getFrontendApplication(frontendApplication.distinguishSchemeTypeTime)
      ).getTime()
      const statusIdList = tmpArr
        .map((item) => {
          if (createdTime < new Date(item.commodityCreatTime).getTime()) {
            item.isNewScheme = true
            return item.schemeId
          }
        })
        .filter(Boolean)
      // 构建方案id集合
      const schemeIdList = [...new Set(tmpArr.map((item) => item.schemeId).filter(Boolean))]
      const queryM = new ConfigJsonUtil()
      const configJsonMap = await queryM.batchQuerySchemeJsonConfigMapBySchemeId(schemeIdList, [])
      // 智能学习
      let IntelligenceLearningMap = new Map<string, string>()
      if (queryIntelligenceLearning && schemeIdList?.length) {
        IntelligenceLearningMap = await new IntelligenceLearningModule().doBatchCheck(schemeIdList)
      }
      let schemeStatusMap = new Map<string, LearningSchemeProcessStatusResponse>()
      if (querySchemeStatus) {
        schemeStatusMap = await this.getSchemeStatus(statusIdList)
      }
      /** 批量获取方案json配置信息 */
      tmpArr.forEach((item, index) => {
        // if (item.skuProperty) {
        //   item.skuValueNameProperty = SkuPropertyConvertUtils.convertSkuValueNameProperty(item.skuProperty)
        // }
        const configJson = configJsonMap.get(item.schemeId)
        if (configJson) {
          item.resourceServicerId = configJson.resourceServicerId
          item.resourceUnitId = configJson.resourceUnitId
          item.registerBeginDate = configJson.registerBeginDate
          item.registerEndDate = configJson.registerEndDate
          item.trainingBeginDate = configJson.trainingBeginDate
          item.trainingEndDate = configJson.trainingEndDate
          item.schemeType = SchemeTypeEnum[configJson.type as string]
          item.needDataSync = configJson.extendProperties?.find((item: any) => {
            return item.name == 'needDataSync'
          })?.value
          // 填充面授部分信息
          QueryTrainClass.parseTrainClassJsonConfigInList(configJson, item)
          const schemeStatus = schemeStatusMap?.get(item.schemeId)
          item.lastTransactionStep = schemeStatus?.lastTransactionStep || 999
          item.recalculating = schemeStatus?.recalculating || false
          // 智能学习
          const intelligentStatus = IntelligenceLearningMap[item.schemeId]
          console.log(intelligentStatus, 'intelligentStatus')
          item.intelligentLearning = intelligentStatus
          item.hangUp = schemeStatus?.hangUp || false
          if (item?.hangUp) {
            item.hasError = true
            // 可能存在多种异常情况 UI 气泡提示无法完全展示，默认取第一个
            const errMsgList = schemeStatus?.errors?.map((item) => item.message || '')
            item.errorMsg = errMsgList?.length ? errMsgList.join(' ') : '系统异常'
          }
        }
      })
      const schemeSkuInfoList: SchemeSkuInfo[] = tmpArr?.map(
        (item) => new SchemeSkuInfo(item.schemeId, item.skuProperty as ComplexSkuPropertyResponse)
      )
      if (schemeSkuInfoList.length) {
        const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(schemeSkuInfoList)
        tmpArr.forEach((item) => {
          const skyInfo = skuInfos.find((el) => el.id === item.schemeId)
          if (skyInfo) {
            item.skuValueNameProperty = skyInfo.skuName
          }
        })
      }
      /** 叠加分销业务的授权状态 */
      if (distributorId) {
        const commodityIdList = DataResolve.unique(tmpArr.map((item) => item.commoditySkuId))
        if (commodityIdList.length) {
          const { data: authResp } = await FxnlQueryFrontGatewayBackstage.judgeCommodityAuthorized({
            commodityIdList,
            distributorId
          })
          if (authResp && authResp.length) {
            tmpArr.forEach((item) => {
              item.isAuthorize = authResp.includes(item.commoditySkuId)
            })
          }
        }
      }
      this.trainClassCommodityList = tmpArr
    }
    return this.trainClassCommodityList
  }

  /**
   * 获取培训班属性
   */
  async queryConfig(schemeId: string): Promise<any> {
    //获取培训班配置模板jsonString
    const res = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInServicer({
      schemeId: schemeId,
      needField: [
        'name',
        'registerBeginDate',
        'registerEndDate',
        'trainingBeginDate',
        'trainingEndDate',
        'type',
        'assessSetting.learningResults'
      ]
    })
    let jsonObj = new Object()
    if (res.status?.isSuccess()) {
      const json = res.data.schemeConfig
      if (json) {
        jsonObj = JSON.parse(res.data.schemeConfig)
      }
    }
    return jsonObj
  }

  /**
   * 查询方案重算状态
   */
  async querySchemeStatus(id: string) {
    const res = await MsLearningscheme.learningSchemeProcessTransactionStepQuery(id)
    return res?.data
  }

  /**
   * 查询方案重算状态
   */
  async getIsProcessedByTransaction(idList: Array<string>) {
    const request = new IsProcessedByTransactionRequest()
    request.schemeIds = idList
    const res = await MsLearningScheme.isProcessedByTransaction(request)
    return res?.data?.results
  }

  /**
   * 查询方案重算状态-批量
   */
  async getSchemeStatus(idList: Array<string>) {
    const req = new RewriteGraph<LearningSchemeProcessStatusResponse, string>(
      MsLearningscheme._commonQuery,
      MsLearningschemeGraphqlImporter.learningSchemeProcessTransactionStepQuery
    )
    await req.request(idList)
    return req.itemMap
  }

  switchTrainCate(cate: SkuVo) {
    // if (cate.skuPropertyValueId) {
    this.skuProperties.filterTrainMajor(cate)
    // }
  }

  /**
   * 监管方案
   * @param page
   * @param params
   * @returns {Promise<AntiSchemeItem[]>}
   */
  async pageAntiSchemeList(page: Page, params: AntiSchemeParams) {
    const schemeRes = await MsSchemeQueryFrontGatewayCourseLearningBackstage.pageSchemeConfigByRequestInServicer({
      page,
      request: AntiSchemeParams.to(params)
    })
    page.totalPageSize = schemeRes.data?.totalPageSize
    page.totalSize = schemeRes.data?.totalSize
    const regionRes = await BasicDataQueryBackstage.getServiceOrIndustryRegion(1)
    const schemeSkuInfoList = new Array<SchemeSkuInfo>()
    schemeRes?.data?.currentPageData?.forEach((item) => {
      const schemeConfig = JSON.parse(item.schemeConfig)
      const skuProperty = new CommoditySkuPropertyResponse()
      skuProperty.year = new SkuPropertyResponse()
      skuProperty.year.skuPropertyValueId = schemeConfig?.year
      const region = regionRes?.data
        ?.find((re) => re.code === schemeConfig.region)
        ?.codePath?.slice(1)
        ?.split('/')
      skuProperty.province = new SkuPropertyResponse()
      skuProperty.city = new SkuPropertyResponse()
      skuProperty.county = new SkuPropertyResponse()
      skuProperty.province.skuPropertyValueId = ''
      skuProperty.city.skuPropertyValueId = ''
      skuProperty.county.skuPropertyValueId = ''
      if (region?.length) skuProperty.province.skuPropertyValueId = region[0]
      if (region?.length > 1) skuProperty.city.skuPropertyValueId = region[1]
      if (region?.length > 2) skuProperty.county.skuPropertyValueId = region[2]
      schemeConfig?.extendProperties?.forEach((sku: { name: string; value: string }) => {
        skuProperty[sku.name] = new SkuPropertyResponse()
        skuProperty[sku.name].skuPropertyValueId = sku.value
      })
      schemeSkuInfoList.push(new SchemeSkuInfo(schemeConfig.id, skuProperty as ComplexSkuPropertyResponse))
    })
    const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(schemeSkuInfoList)

    const schemeList = schemeRes?.data?.currentPageData?.map((item) => {
      const schemeConfig = JSON.parse(item.schemeConfig)
      const skyInfo = skuInfos.find((el) => el.id === schemeConfig.id)
      const { period } = QueryTrainClass.parseTrainClassJsonConfig(schemeConfig)

      return new AntiSchemeItem(schemeConfig.id, schemeConfig.name, skyInfo.skuName, period, item.hasAntiConfig)
    })
    return schemeList
  }
  /**
   * 学习规则
   * @param page
   * @param params type 1 学习规则 2 在线学习规则
   * @returns {Promise<AntiSchemeItem[]>}
   */
  async pageRuleSchemeList(page: Page, params: RuleSchemeParams) {
    const schemeRes = await PlatformSchemeConfig.pageSchemeConfigByRequestInServicer({
      page,
      request: RuleSchemeParams.to(params)
    })
    page.totalPageSize = schemeRes.data?.totalPageSize
    page.totalSize = schemeRes.data?.totalSize
    const regiongIds =
      schemeRes?.data?.currentPageData.map((item) => {
        const schemeConfig = JSON.parse(item.schemeConfig)
        return schemeConfig?.region as string
      }) || []
    const regionRes = await QueryPhysicalRegion.querRegionDetil(regiongIds)
    const schemeSkuInfoList = new Array<SchemeSkuInfo>()
    schemeRes?.data?.currentPageData?.forEach((item) => {
      const schemeConfig = JSON.parse(item.schemeConfig)
      const skuProperty = new CommoditySkuPropertyResponse()
      skuProperty.year = new SkuPropertyResponse()
      skuProperty.year.skuPropertyValueId = schemeConfig?.year
      const region = regionRes
        ?.find((re) => re.id === schemeConfig?.region)
        ?.regionPath?.slice(1)
        ?.split('/')
      skuProperty.province = new SkuPropertyResponse()
      skuProperty.city = new SkuPropertyResponse()
      skuProperty.county = new SkuPropertyResponse()
      skuProperty.province.skuPropertyValueId = ''
      skuProperty.city.skuPropertyValueId = ''
      skuProperty.county.skuPropertyValueId = ''
      if (region?.length) skuProperty.province.skuPropertyValueId = region[0]
      if (region?.length > 1) skuProperty.city.skuPropertyValueId = region[1]
      if (region?.length > 2) skuProperty.county.skuPropertyValueId = region[2]
      schemeConfig?.extendProperties?.forEach((sku: { name: string; value: string }) => {
        skuProperty[sku.name] = new SkuPropertyResponse()
        skuProperty[sku.name].skuPropertyValueId = sku.value
      })
      schemeSkuInfoList.push(new SchemeSkuInfo(schemeConfig?.id, skuProperty as ComplexSkuPropertyResponse))
    })
    const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(schemeSkuInfoList)

    const schemeList = schemeRes?.data?.currentPageData?.map((item) => {
      const schemeConfig = JSON.parse(item.schemeConfig)
      const skyInfo = skuInfos.find((el) => el.id === schemeConfig?.id)
      const { period } = QueryTrainClass.parseTrainClassJsonConfig(schemeConfig)

      return new RuleSchemeItem(schemeConfig?.id, schemeConfig?.name, skyInfo?.skuName, period, item.hasAntiConfig)
    })
    return schemeList
  }

  /**
   * 导出方案列表（商品）
   * @description 对应操作：培训方案管理-导出列表数据-“导出方案列表数据”导出
   */
  async exportCommoditySkuInServicer(
    filterCommodity: CommoditySkuRequest,
    sortRequest?: Array<CommoditySkuSortRequestv2>
  ) {
    const sortRequestM = new CommoditySkuSortRequestv2()
    sortRequestM.sortField = CommoditySkuSortFieldV2.ON_SHELVE_TIME
    sortRequestM.policy = SortPolicy.DESC
    !sortRequest && (sortRequest = [])
    sortRequest.push(sortRequestM)
    const res = await MsDataExportBackstageGateway.exportCommoditySkuInServicer({
      queryRequest: filterCommodity,
      sortRequest: sortRequest || []
    })
    return res
  }

  /**
   * 导出方案期别明细数据
   * @description 对应操作：培训方案管理-导出列表数据-“导出方案期别明细数据”导出
   * @param filterCommodity 查询条件
   * @param sortRequest 排序条件
   */
  async exportIssueCommoditySkuInServicer(
    filterCommodity: CommoditySkuRequest,
    sortRequest?: Array<CommoditySkuSortRequestv2>
  ): Promise<Response<boolean>> {
    const res = await jxjyDataExportGatewayBackstage.exportIssueCommoditySkuInServicer({
      queryRequest: filterCommodity,
      sortRequest: sortRequest ? sortRequest : undefined
    })
    return res
  }

  /**
   * 导出方案专题数据
   */
  exportSchemeSubjectInServicer(filterCommodity: CommoditySkuRequest, sortRequest?: Array<CommoditySkuSortRequestv2>) {
    const sortRequestM = new CommoditySkuSortRequestv2()
    sortRequestM.sortField = CommoditySkuSortFieldV2.ON_SHELVE_TIME
    sortRequestM.policy = SortPolicy.DESC
    !sortRequest && (sortRequest = [])
    sortRequest.push(sortRequestM)
    const res = MsDataExportBackstageGateway.exportCommoditySkuWithTrainingChannelInServicer({
      queryRequest: filterCommodity,
      sortRequest: sortRequest || []
    })
    return res
  }
  // endregion
}

export default QueryTrainClassCommodityList
