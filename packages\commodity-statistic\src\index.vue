<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <hb-search-wrapper class="m-query is-border-bottom" @reset="resetSearch">
        <el-form-item label="推广门户简称">
          <!--          <biz-school-autocomplate v-model="distributedGoodsStatisticsOnSales.params.portalPromoteTheName" />-->
        </el-form-item>
        <el-form-item label="培训商品">
          <!--          <biz-goods-autocomplete v-model="distributedGoodsStatisticsOnSales.params.trainingProductName" />-->
        </el-form-item>
        <el-form-item label="商品售价">
          <el-input-number
            v-model="distributedGoodsStatisticsOnSales.params.salesPrice.min"
            class="input-num"
            :controls="false"
          />
          -
          <el-input-number
            v-model="distributedGoodsStatisticsOnSales.params.salesPrice.max"
            class="input-num"
            :controls="false"
          />
        </el-form-item>
        <el-form-item label="查询时间">
          <el-date-picker
            v-model="distributedGoodsStatisticsOnSales.params.queryTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="起始时间"
            end-placeholder="结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="分销商">
          <!--          <biz-distributor-autocomplete-->
          <!--            v-model="distributedGoodsStatisticsOnSales.params.distributorName"-->
          <!--            :isShowFullName="true"-->
          <!--            @input="handleDistributorIdChange"-->
          <!--            :includeOneself="true"-->
          <!--          />-->
        </el-form-item>
        <el-form-item label="供应商">
          <!--          <biz-supplier-autocomplete-->
          <!--            v-model="distributedGoodsStatisticsOnSales.params.vendorName"-->
          <!--            :isShowFullName="true"-->
          <!--          />-->
        </el-form-item>
        <template slot="actions">
          <template v-if="$hasPermission('queryOpenStatistic')" desc="查询" actions="search">
            <el-button type="primary" @click="search">查询</el-button>
          </template>
          <template v-if="$hasPermission('doExport')" desc="导出" actions="exportData">
            <el-button @click="exportData" :loading="uiConfig.loadBtn.exporting">导出列表数据</el-button>
          </template>
        </template>
      </hb-search-wrapper>
      <!--操作栏-->
      <div class="f-mt20">
        <el-alert type="warning" :closable="false" class="m-alert f-clear">
          <div class="f-c6 f-fl">
            搜索结果合计：当前净开通
            <span class="f-fb f-co">{{ distributedGoodsStatisticsOnSales.totalStatic.netTurnOn }}</span> 人次，成交总额
            <span class="f-fb f-co">¥ {{ distributedGoodsStatisticsOnSales.totalStatic.transactionTotal }}</span>
          </div>
          <div @click="statisticalCaliberDeclarationDrawer.openDrawer()" class="f-fr f-csp f-flex f-align-center">
            <i class="el-icon-info f-f16 f-mr5"></i>统计口径说明
          </div>
        </el-alert>
      </div>
      <el-table stripe :data="distributedGoodsStatisticsOnSales.list" border class="m-table f-mt10">
        <el-table-column fixed="left" type="index" label="No." width="60" align="center">
          <template slot-scope="scope">
            {{ scope.row.rowIndex }}
          </template>
        </el-table-column>
        <el-table-column label="分销商品" min-width="160" key="5">
          <template slot-scope="scope">{{ scope.row.commodityName }} </template>
        </el-table-column>
        <!-- <el-table-column label="所属网校" min-width="160" key="6">
          <template slot-scope="scope">{{ scope.row.schoolName }} </template>
        </el-table-column> -->
        <el-table-column label="授权分销商" min-width="160" key="7">
          <template slot-scope="scope">{{ scope.row.distributorName }} </template>
        </el-table-column>
        <el-table-column label="分销定价方式" min-width="160" key="8">
          <template slot-scope="scope">
            <div>
              <el-tag type="warning" size="mini" class="f-ml5">优惠申请</el-tag><i class="f-mr5">¥</i
              >{{ scope.row.discountPrice.price }}
              <!-- <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-info f-f16 f-mr5"></i>
                <div slot="content">
                  培训商品按优惠申请销售的商品统计数据，同一产品多个优惠申请价格时，都在此维度统计
                </div>
              </el-tooltip> -->
            </div>
            <div>
              <el-tag type="success" size="mini" class="f-ml5">授权定价</el-tag><i class="f-mr5">¥</i
              >{{ scope.row.fixPrice.price }}
              <!-- <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-info f-f16 f-mr5"></i>
                <div slot="content">
                  培训商品按授权定价销售的商品统计数据，同一产品多个定价时，都在此维度统计
                </div>
              </el-tooltip> -->
            </div>
            <!-- <div v-if="scope.row.isNull">
              暂无销售数据
            </div> -->
          </template>
        </el-table-column>
        <el-table-column label="合计" header-align="center">
          <el-table-column label="开通" min-width="90" align="right">
            <template slot-scope="{ row }">{{ row.total.open }}</template>
          </el-table-column>
          <el-table-column label="退班" min-width="90" align="right">
            <template slot-scope="{ row }">{{ row.total.return }}</template>
          </el-table-column>
          <el-table-column label="换入(换班)" min-width="90" align="right">
            <template slot-scope="{ row }">{{ row.total.swapOutOfClass }}</template>
          </el-table-column>
          <el-table-column label="换出(换班)" min-width="90" align="right">
            <template slot-scope="{ row }">{{ row.total.swapIntoAClass }}</template>
          </el-table-column>
          <el-table-column label="净开通" min-width="90" align="right">
            <template slot-scope="{ row }">{{ row.total.count }}</template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="个人缴费" header-align="center">
          <el-table-column label="线上支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.individualOnline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.individualOnline.return }}</template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.individualOnline.swapOutOfClass }}</template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.individualOnline.swapIntoAClass }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.individualOnline.count }}</template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
        <el-table-column label="集体报名" header-align="center">
          <el-table-column label="线上支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOnline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOnline.return }}</template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOnline.swapOutOfClass }}</template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOnline.swapIntoAClass }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOnline.count }}</template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="线下支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOffline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOffline.return }}</template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOffline.swapOutOfClass }}</template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOffline.swapIntoAClass }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOffline.count }}</template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
        <el-table-column label="导入开通" header-align="center">
          <el-table-column label="线下支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.importOffline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.importOffline.return }}</template>
            </el-table-column>
            <el-table-column label="换入(换班)" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.importOffline.swapOutOfClass }}</template>
            </el-table-column>
            <el-table-column label="换出(换班)" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.importOffline.swapIntoAClass }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.importOffline.count }}</template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table>
      <hb-pagination :page="page" v-bind="page"> </hb-pagination>
    </el-card>
    <statistical-caliber-declaration-drawer
      ref="statisticalCaliberDeclarationDrawer"
      :search-data="distributionGoodsOpeningStatisticsSearchData"
      :field-data="distributionGoodsOpeningStatisticsFieldData"
    />
  </div>
</template>
<script lang="ts">
  import { UiPage } from '@hbfe/common'
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import DistributedGoodsStatisticsOnSales from '@api/service/management/statisticalReport/DistributedGoodsStatisticsOnSales/DistributedGoodsStatisticsOnSales'
  import StatisticalCaliberDeclarationDrawer from '@hbfe/jxjy-admin-components/src/statistical-caliber-declaration-drawer.vue'
  import {
    distributionGoodsOpeningStatisticsFieldData,
    distributionGoodsOpeningStatisticsSearchData
  } from '@hbfe/jxjy-admin-components/src/models/statisticalExplanatoryData'

  @Component({
    components: { StatisticalCaliberDeclarationDrawer }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.search, this.search)
    }
    // 统计口径说明抽屉ref
    @Ref('statisticalCaliberDeclarationDrawer') statisticalCaliberDeclarationDrawer: StatisticalCaliberDeclarationDrawer
    // 分销商品开通统计——搜索字段说明
    get distributionGoodsOpeningStatisticsSearchData() {
      return distributionGoodsOpeningStatisticsSearchData
    }
    // 分销商品开通统计——列表字段说明
    get distributionGoodsOpeningStatisticsFieldData() {
      return distributionGoodsOpeningStatisticsFieldData
    }
    uiConfig = {
      loadStatus: { loadPage: true, loadTotalOrderAndMoney: true },
      showStatus: { isShowTotalOrderAmount: true },
      loadBtn: { exporting: false },
      exportDialog: false
    }
    distributedGoodsStatisticsOnSales = new DistributedGoodsStatisticsOnSales()
    // 分页参数
    page: UiPage

    /**
     * 搜索
     */
    async search() {
      //
    }

    /**
     * 导出数据
     */
    async exportData() {
      console.log(1)
    }

    /**
     * 重置
     */
    async resetSearch() {
      console.log(1)
    }
  }
</script>
