import { ResponseStatus } from '@hbfe/common'
import ExamMutationGateway from '@api/ms-gateway/ms-examquestion-v1'
import UpdateLibraryDto from './vo/UpdateLibraryVo'
class QuestionLibraryAction {
  updateLibraryDto: UpdateLibraryDto = new UpdateLibraryDto()

  constructor(id: string, name: string, parentId = '-1', description?: string) {
    this.updateLibraryDto.id = id
    this.updateLibraryDto.name = name
    this.updateLibraryDto.parentId = parentId
    this.updateLibraryDto.description = description
  }

  /**
   * @description: 修改
   * @param {*}
   * @return {*}
   */
  async doUpdateQuestionLibrary(): Promise<ResponseStatus> {
    const { status } = await ExamMutationGateway.updateLibrary(this.updateLibraryDto)
    return status
  }

  /**
   * @description: 删除
   * @param {*}
   * @return {*}
   */
  async doDeleteQuestionLibrary(): Promise<ResponseStatus> {
    const { status } = await ExamMutationGateway.removeLibrary(this.updateLibraryDto.id)
    return status
  }
}

export default QuestionLibraryAction
