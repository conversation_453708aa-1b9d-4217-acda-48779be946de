import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/gql/ms-account-v1'

// 是否微服务
const isMicroService = true

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-account-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: 'ms-account-v1'
}

// 枚举

// 类

/**
 * 重新设置账户的帐号密码认证方式认证标识信息
<AUTHOR>
 */
export class AccountPwdAuthIdentityResetInfo {
  /**
   * 【必填】账户ID
   */
  accountId?: string
  /**
   * 【必填】认证标识类型 1用户名，2手机，3身份证，4电子邮箱
@see com.fjhb.domain.basicdata.api.account.consts.AuthenticationIdentityTypes
   */
  identityType: number
  /**
   * 【必填】认证标识：帐号
   */
  identity?: string
}

/**
 * 变更当前登录帐户的密码信息
<AUTHOR>
 */
export class CurrentAccountChangePasswordRequest {
  /**
   * 【必填】原始密码
   */
  originalPassword?: string
  /**
   * 【必填】新密码
   */
  newPassword?: string
}

/**
 * 当前登录用户的用户修改信息
<AUTHOR>
 */
export class CurrentUserUpdateRequest {
  /**
   * 身份证号
   */
  idCard?: string
  /**
   * 用户名称
   */
  name?: string
  /**
   * 用户昵称
   */
  nickName?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 头像地址
   */
  photo?: string
  /**
   * 所属区域
   */
  area?: string
  /**
   * 联系地址
   */
  address?: string
  /**
   * 性别
@see com.fjhb.domain.basicdata.api.user.consts.Genders
   */
  gender?: number
  /**
   * 工作单位
   */
  companyName?: string
  /**
   * 所属人群
   */
  peoples?: string
  /**
   * 电子邮箱
   */
  email?: string
}

/**
 * 立即重置密码
<AUTHOR>
 */
export class ImmediateResetPasswordRequest {
  /**
   * 【必填】帐户ID
   */
  accountId?: string
  /**
   * 【必填】重置后的密码
   */
  password?: string
}

/**
 * 用户更新信息，不设置或者设置null表示字段不更新
<AUTHOR>
 */
export class UserUpdateRequest {
  /**
   * 【必填】用户ID
   */
  id?: string
  /**
   * 身份证号
   */
  idCard?: string
  /**
   * 用户名称
   */
  name?: string
  /**
   * 用户昵称
   */
  nickName?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 头像地址
   */
  photo?: string
  /**
   * 所属区域
   */
  area?: string
  /**
   * 联系地址
   */
  address?: string
  /**
   * 性别
@see com.fjhb.domain.basicdata.api.user.consts.Genders
   */
  gender?: number
  /**
   * 工作单位
   */
  companyName?: string
  /**
   * 所属人群
   */
  peoples?: string
  /**
   * 电子邮箱
   */
  email?: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 变更当前登录帐户的密码
   * @param changeInfo 更新信息
   * @param mutate 查询 graphql 语法文档
   * @param changeInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async changePasswordByCurrent(
    changeInfo: CurrentAccountChangePasswordRequest,
    mutate: DocumentNode = GraphqlImporter.changePasswordByCurrent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { changeInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 禁用当前帐户微信小程序通知
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableNoticeForWebChatApplet(
    mutate: DocumentNode = GraphqlImporter.disableNoticeForWebChatApplet,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 启用当前帐户微信小程序通知
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableNoticeForWebChatApplet(
    mutate: DocumentNode = GraphqlImporter.enableNoticeForWebChatApplet,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 冻结帐户
   * @param accountId 【必填】帐户ID
   * @param mutate 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async freezeAccount(
    accountId: string,
    mutate: DocumentNode = GraphqlImporter.freezeAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { accountId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 立即重置密码
   * @param resetInfo 重置密码信息
   * @param mutate 查询 graphql 语法文档
   * @param resetInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async immediateResetPassword(
    resetInfo: ImmediateResetPasswordRequest,
    mutate: DocumentNode = GraphqlImporter.immediateResetPassword,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { resetInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 重新设置账户的帐号认证方式的帐号
   * @param mutate 查询 graphql 语法文档
   * @param resetInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resetAccountPwdAuthIdentity(
    resetInfo: AccountPwdAuthIdentityResetInfo,
    mutate: DocumentNode = GraphqlImporter.resetAccountPwdAuthIdentity,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { resetInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 恢复冻结的帐户
   * @param accountId 【必填】帐户ID
   * @param mutate 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resumeAccount(
    accountId: string,
    mutate: DocumentNode = GraphqlImporter.resumeAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { accountId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新用户信息
   * @param updateInfo 【必填】要更新的用户信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateUser(
    updateInfo: UserUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateUser,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新当前登录用户信息
   * @param updateInfo 【必填】要更新的用户信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateUserByCurrent(
    updateInfo: CurrentUserUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateUserByCurrent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
